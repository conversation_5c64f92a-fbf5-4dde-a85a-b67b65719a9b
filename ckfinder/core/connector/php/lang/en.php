<?php
// Copyright (c) 2003-2013, CKSource - <PERSON><PERSON>. All rights reserved.
// For licensing, see LICENSE.html or http://ckfinder.com/license

//  Defines the object for the English language. This is the base file for all translations.

$GLOBALS['CKFLang'] = array (
	'ErrorUnknown' => 'It was not possible to complete the request. (Error %1)',
	'Errors' => array (
		'10' => 'Invalid command.',
		'11' => 'The resource type was not specified in the request.',
		'12' => 'The requested resource type is not valid.',
		'102' => 'Invalid file or folder name.',
		'103' => 'It was not possible to complete the request due to authorization restrictions.',
		'104' => 'It was not possible to complete the request due to file system permission restrictions.',
		'105' => 'Invalid file extension.',
		'109' => 'Invalid request.',
		'110' => 'Unknown error.',
		'111' => 'It was not possible to complete the request due to resulting file size.',
		'115' => 'A file or folder with the same name already exists.',
		'116' => 'Folder not found. Please refresh and try again.',
		'117' => 'File not found. Please refresh the files list and try again.',
		'118' => 'Source and target paths are equal.',
		'201' => 'A file with the same name is already available. The uploaded file was renamed to "%1".',
		'202' => 'Invalid file.',
		'203' => 'Invalid file. The file size is too big.',
		'204' => 'The uploaded file is corrupt.',
		'205' => 'No temporary folder is available for upload in the server.',
		'206' => 'Upload cancelled due to security reasons. The file contains HTML-like data.',
		'207' => 'The uploaded file was renamed to "%1".',
		'300' => 'Moving file(s) failed.',
		'301' => 'Copying file(s) failed.',
		'500' => 'The file browser is disabled for security reasons. Please contact your system administrator and check the CKFinder configuration file.',
		'501' => 'The thumbnails support is disabled.',
	)
);

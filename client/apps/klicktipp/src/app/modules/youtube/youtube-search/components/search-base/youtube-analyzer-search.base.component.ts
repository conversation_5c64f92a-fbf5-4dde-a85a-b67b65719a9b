import { Component, inject, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { faEdit } from '@fortawesome/free-solid-svg-icons';
import { faInfoCircle, faTimes } from '@fortawesome/pro-light-svg-icons';
import {
  KtButtonComponent,
  KtNavigationService,
  KtTableComponent,
  ModalData,
  TableBase,
  TableButtonClicked,
  TableRowData,
  TranslatePipe,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { EMPTY, Subject, timer } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import { BreadcrumbService } from '../../../../../services/breadcrumb.service';
import { ModalService } from '../../../../../services/modal.service';
import { OnReadyBaseComponent } from '../../../../forms.shared/components/on-ready/on-ready-base.component';
import { ProgressComponent } from '../../../../forms.shared/components/progress/progress.component';
import { YouTubeResultRow, YouTubeResults, YouTubeSearchEntity, YouTubeSettings } from '../../interfaces/youtube-analyzer';
import { YoutubeAnalyzerApiService } from '../../services/youtube-analyzer-api.service';
import { YoutubeAnalyzerTableConvertService } from '../../services/youtube-analyzer-table-convert.service';
import { YoutubeAnalyzerService } from '../../services/youtube-analyzer.service';
import { YoutubeAnalyzerSearchSettingsComponent } from '../search-settings/youtube-analyzer-search-settings.component';
import { RouterFacade } from '../../../../../+state/router.facade';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'kt-search-base',
  imports: [FormsModule, ReactiveFormsModule, TranslatePipe, KtTableComponent, ProgressComponent, KtButtonComponent, YoutubeAnalyzerSearchSettingsComponent],
  templateUrl: './youtube-analyzer-search.base.component.html',
})
export class YoutubeAnalyzerSearchBaseComponent extends OnReadyBaseComponent implements OnInit, OnDestroy {
  protected readonly faEdit = faEdit;
  private readonly routerFacade = inject(RouterFacade);

  youtubeSearchSettings: YouTubeSettings;
  isLoading = false;
  loadingProgress = 0;
  youtubeSearchResults: YouTubeResults;
  tableItem: TableBase;
  progressBarHeader = this.translateService.translate('youtube-analyzer-search-base::progress-bar-header::Loading videos');
  private counter = 0;
  private filteredSearchEntites: YouTubeResultRow[] = [];
  private destroy$ = new Subject<void>();

  constructor(
    translateService: TranslateService,
    private ytApiService: YoutubeAnalyzerApiService,
    private modalService: ModalService,
    private ktBreadCrumbService: BreadcrumbService,
    private youTubeTableConvertService: YoutubeAnalyzerTableConvertService,
    private youtubeService: YoutubeAnalyzerService,
    private ktNavigationService: KtNavigationService
  ) {
    super(translateService, 'youtube-analyzer-search-base', true);
    this.ktBreadCrumbService.generateBreadcrumbs();
    this.youtubeService.currentVideo = null;
    this.youtubeService.currentChatModels = null;
    this.youtubeService.currentChatLanguages = null;
  }

  ngOnInit(): void {
    this.getInitialData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getInitialData(): void {
    this.ytApiService
      .getSettings()
      .pipe(takeUntil(this.destroy$))
      .subscribe((response: YouTubeSettings) => {
        this.youtubeSearchSettings = response;
        this.setReady(true);
      });
  }

  updateProgressbar(requestedVideos: number, retrievedVideos: number): void {
    this.loadingProgress = Math.round((retrievedVideos / requestedVideos) * 100);
  }

  searchResults(searchEntity: YouTubeSearchEntity): void {
    this.loadingProgress = 0;
    this.isLoading = true;
    this.tableItem = null;
    this.ytApiService
      .searchVideos(searchEntity)
      .pipe(
        takeUntil(this.destroy$),
        switchMap((response: YouTubeResults) => {
          this.filteredSearchEntites = [
            ...this.filteredSearchEntites,
            ...this.youtubeService.filterSearchResults(response.data.entities, searchEntity.searchMask.searchFilter, searchEntity.searchMask.searchInput)
          ];

          if (response.data?.finished) {
            return this.routerFacade.getUserIdOnce().pipe(map((userId) => ({ response, userId })));
          } else {
            // Return EMPTY to complete the current observable chain, as the new recursive call will handle the next steps.
            const newSettings: YouTubeSearchEntity = {
              searchMask: searchEntity.searchMask,
              nextPage: response.data.nextPage
            };
            this.searchResults(newSettings);
            this.updateProgressbar(response.data.nextPage.requestedVideos, response.data.nextPage.retrievedVideos);
            return EMPTY;
          }
        })
      )
      .subscribe(
        ({ response, userId }) => {
          response.data.entities = this.filteredSearchEntites;
          this.youtubeSearchResults = response;
          this.tableItem = this.youTubeTableConvertService.createTableYoutubeSearchResults(
            response.data,
            this.youtubeSearchSettings.data.filterOptions.chatGptModelOptions,
            this.youtubeSearchSettings.data.filterOptions.languageOptions,
            userId
          );
          this.loadingProgress = 100;
          this.filteredSearchEntites = [];
          this.isLoading = false;
          this.counter = 0;
        },
        (error) => {
          this.counter++;
          if (!this.youtubeService.handleApiErrors(error, this.counter)) {
            timer(1000)
              .pipe(takeUntil(this.destroy$))
              .subscribe(() => {
                this.searchResults(searchEntity);
              });
          } else {
            this.isLoading = false;
            this.counter = 0;
          }
        }
      );
  }

  summarize($event: TableButtonClicked): void {
    if ($event.key === 'summary') {
      this.openSummarization($event.element);
    }
  }

  copyUrlsClicked(): void {
    const links = this.youtubeSearchResults.data.entities.map((e) => e.titleLink);
    const modalData: ModalData = {
      id: 'youtube-analyzer-copy-urls',
      title: this.translateService.translate('youtube-analyzer-search-base::copy-urls-modal::title'),
      formItem: {
        form: new FormGroup({}),
        closeModalOnApply: true,
        textAreas: [
          {
            id: 'urls',
            labelText: '',
            value: links.join('\n'),
            cols: '1',
            rows: '15',
            row: 0,
            column: 0,
            selectOnFocus: true
          }
        ]
      },
      closeButtonVisible: true,
      titleIcon: faInfoCircle,
      closeIcon: faTimes,
      secondaryButtonText: this.translateService.translate('youtube-analyzer-search-base::copy-urls-modal::button::cancel'),
      messageIsHtml: true
    };
    this.modalService.openInfoModal(modalData);
  }

  setSingleSearchUrl(videoUrl: string): void {
    if (videoUrl) {
      this.youtubeService.singleVideoUrl = videoUrl;
      this.openSummarizeDialog();
    }
  }

  private openSummarization(element: TableRowData): void {
    this.youtubeService.currentVideo = element.rowData.find((r) => r.column === 'title');
    this.openSummarizeDialog();
  }

  private openSummarizeDialog(): void {
    this.youtubeService.currentChatModels = this.youtubeSearchSettings.data.filterOptions.chatGptModelOptions;
    this.youtubeService.currentChatLanguages = this.youtubeSearchSettings.data.filterOptions.languageOptions;
    this.routerFacade
      .getUserIdOnce()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((userId) => {
        const url = this.youtubeService.createSummarizeUrl(
          userId,
          this.youtubeSearchSettings.data.filterOptions.chatGptModelOptions,
          this.youtubeSearchSettings.data.filterOptions.languageOptions
        );

        this.ktNavigationService.routeTo(url);
      });
  }
}

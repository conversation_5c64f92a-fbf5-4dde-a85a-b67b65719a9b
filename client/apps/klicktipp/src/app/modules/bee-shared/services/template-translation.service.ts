import { Injectable } from '@angular/core';
import { TranslateService } from '@klicktipp/kt-mat-library';

@Injectable({
  providedIn: 'root'
})
export class TemplateTranslationService {
  // these objects come from the api call https://localhost:4202/ipa/kt-bee-templates/meta/1
  tagTranslations = {
    minimal: this.ts.translate('Bee::Template::Tag::minimal'),
    elegant: this.ts.translate('Bee::Template::Tag::elegant'),
    grey: this.ts.translate('Bee::Template::Tag::grey'),
    news: this.ts.translate('Bee::Template::Tag::news'),
    black: this.ts.translate('Bee::Template::Tag::black'),
    light: this.ts.translate('Bee::Template::Tag::light'),
    white: this.ts.translate('Bee::Template::Tag::white'),
    orange: this.ts.translate('Bee::Template::Tag::orange'),
    shop: this.ts.translate('Bee::Template::Tag::shop'),
    clean: this.ts.translate('Bee::Template::Tag::clean'),
    pink: this.ts.translate('Bee::Template::Tag::pink'),
    cream: this.ts.translate('Bee::Template::Tag::cream'),
    purple: this.ts.translate('Bee::Template::Tag::purple'),
    store: this.ts.translate('Bee::Template::Tag::store'),
    retail: this.ts.translate('Bee::Template::Tag::retail'),
    man: this.ts.translate('Bee::Template::Tag::man'),
    navy: this.ts.translate('Bee::Template::Tag::navy'),
    style: this.ts.translate('Bee::Template::Tag::style'),
    lightblue: this.ts.translate('Bee::Template::Tag::lightblue'),
    wear: this.ts.translate('Bee::Template::Tag::wear'),
    blue: this.ts.translate('Bee::Template::Tag::blue'),
    men: this.ts.translate('Bee::Template::Tag::men'),
    countdown: this.ts.translate('Bee::Template::Tag::countdown'),
    'multi-purpose': this.ts.translate('Bee::Template::Tag::multi-purpose'),
    niftyimages: this.ts.translate('Bee::Template::Tag::niftyimages'),
    ticket: this.ts.translate('Bee::Template::Tag::ticket'),
    impact: this.ts.translate('Bee::Template::Tag::impact'),
    red: this.ts.translate('Bee::Template::Tag::red'),
    press: this.ts.translate('Bee::Template::Tag::press'),
    fresh: this.ts.translate('Bee::Template::Tag::fresh'),
    cool: this.ts.translate('Bee::Template::Tag::cool'),
    modern: this.ts.translate('Bee::Template::Tag::modern'),
    green: this.ts.translate('Bee::Template::Tag::green'),
    testimonials: this.ts.translate('Bee::Template::Tag::testimonials'),
    tosca: this.ts.translate('Bee::Template::Tag::tosca'),
    grunge: this.ts.translate('Bee::Template::Tag::grunge'),
    street: this.ts.translate('Bee::Template::Tag::street'),
    shoes: this.ts.translate('Bee::Template::Tag::shoes'),
    dark: this.ts.translate('Bee::Template::Tag::dark'),
    contrast: this.ts.translate('Bee::Template::Tag::contrast'),
    yellow: this.ts.translate('Bee::Template::Tag::yellow'),
    annual: this.ts.translate('Bee::Template::Tag::annual'),
    peach: this.ts.translate('Bee::Template::Tag::peach'),
    gif: this.ts.translate('Bee::Template::Tag::gif'),
    review: this.ts.translate('Bee::Template::Tag::review'),
    promo: this.ts.translate('Bee::Template::Tag::promo'),
    colorful: this.ts.translate('Bee::Template::Tag::colorful'),
    platform: this.ts.translate('Bee::Template::Tag::platform'),
    saas: this.ts.translate('Bee::Template::Tag::saas'),
    digital: this.ts.translate('Bee::Template::Tag::digital'),
    subscription: this.ts.translate('Bee::Template::Tag::subscription'),
    rich: this.ts.translate('Bee::Template::Tag::rich'),
    business: this.ts.translate('Bee::Template::Tag::business'),
    author: this.ts.translate('Bee::Template::Tag::author'),
    ebooks: this.ts.translate('Bee::Template::Tag::ebooks'),
    corporate: this.ts.translate('Bee::Template::Tag::corporate'),
    message: this.ts.translate('Bee::Template::Tag::message'),
    line: this.ts.translate('Bee::Template::Tag::line'),
    'black & white': this.ts.translate('Bee::Template::Tag::black & white'),
    rounded: this.ts.translate('Bee::Template::Tag::rounded'),
    gray: this.ts.translate('Bee::Template::Tag::gray'),
    simple: this.ts.translate('Bee::Template::Tag::simple'),
    rsvp: this.ts.translate('Bee::Template::Tag::rsvp'),
    luxury: this.ts.translate('Bee::Template::Tag::luxury'),
    booking: this.ts.translate('Bee::Template::Tag::booking'),
    summer: this.ts.translate('Bee::Template::Tag::summer'),
    sales: this.ts.translate('Bee::Template::Tag::sales'),
    offer: this.ts.translate('Bee::Template::Tag::offer'),
    deal: this.ts.translate('Bee::Template::Tag::deal'),
    'happy birthday': this.ts.translate('Bee::Template::Tag::happy birthday'),
    gift: this.ts.translate('Bee::Template::Tag::gift'),
    birthday: this.ts.translate('Bee::Template::Tag::birthday'),
    flowers: this.ts.translate('Bee::Template::Tag::flowers'),
    'thank you': this.ts.translate('Bee::Template::Tag::thank you'),
    photo: this.ts.translate('Bee::Template::Tag::photo'),
    gallery: this.ts.translate('Bee::Template::Tag::gallery'),
    wine: this.ts.translate('Bee::Template::Tag::wine'),
    sport: this.ts.translate('Bee::Template::Tag::sport'),
    holiday: this.ts.translate('Bee::Template::Tag::holiday'),
    animated: this.ts.translate('Bee::Template::Tag::animated'),
    'two-column': this.ts.translate('Bee::Template::Tag::two-column'),
    serif: this.ts.translate('Bee::Template::Tag::serif'),
    fuchsia: this.ts.translate('Bee::Template::Tag::fuchsia'),
    'one-column': this.ts.translate('Bee::Template::Tag::one-column'),
    promote: this.ts.translate('Bee::Template::Tag::promote'),
    'sans seriff': this.ts.translate('Bee::Template::Tag::sans seriff'),
    invoice: this.ts.translate('Bee::Template::Tag::invoice'),
    'four-column': this.ts.translate('Bee::Template::Tag::four-column'),
    'san serif': this.ts.translate('Bee::Template::Tag::san serif'),
    inform: this.ts.translate('Bee::Template::Tag::inform'),
    handwritten: this.ts.translate('Bee::Template::Tag::handwritten'),
    'sans serif': this.ts.translate('Bee::Template::Tag::sans serif'),
    communicate: this.ts.translate('Bee::Template::Tag::communicate'),
    'two-colums': this.ts.translate('Bee::Template::Tag::two-colums'),
    discount: this.ts.translate('Bee::Template::Tag::discount'),
    tracking: this.ts.translate('Bee::Template::Tag::tracking'),
    'light blue': this.ts.translate('Bee::Template::Tag::light blue'),
    borwn: this.ts.translate('Bee::Template::Tag::borwn'),
    beige: this.ts.translate('Bee::Template::Tag::beige'),
    'light-blue': this.ts.translate('Bee::Template::Tag::light-blue'),
    communications: this.ts.translate('Bee::Template::Tag::communications'),
    'light grey': this.ts.translate('Bee::Template::Tag::light grey'),
    'three-column': this.ts.translate('Bee::Template::Tag::three-column'),
    sell: this.ts.translate('Bee::Template::Tag::sell'),
    'dark grey': this.ts.translate('Bee::Template::Tag::dark grey'),
    'four-columns': this.ts.translate('Bee::Template::Tag::four-columns'),
    portfolio: this.ts.translate('Bee::Template::Tag::portfolio'),
    lilac: this.ts.translate('Bee::Template::Tag::lilac'),
    coupon: this.ts.translate('Bee::Template::Tag::coupon'),
    'dynamic countdown': this.ts.translate('Bee::Template::Tag::dynamic countdown'),
    'light green': this.ts.translate('Bee::Template::Tag::light green'),
    launch: this.ts.translate('Bee::Template::Tag::launch'),
    brown: this.ts.translate('Bee::Template::Tag::brown'),
    announce: this.ts.translate('Bee::Template::Tag::announce'),
    confirmation: this.ts.translate('Bee::Template::Tag::confirmation'),
    invite: this.ts.translate('Bee::Template::Tag::invite'),
    book: this.ts.translate('Bee::Template::Tag::book'),
    retention: this.ts.translate('Bee::Template::Tag::retention'),
    slab: this.ts.translate('Bee::Template::Tag::slab'),
    sans: this.ts.translate('Bee::Template::Tag::sans'),
    greetings: this.ts.translate('Bee::Template::Tag::greetings'),
    notification: this.ts.translate('Bee::Template::Tag::notification'),
    'non-animated': this.ts.translate('Bee::Template::Tag::non-animated'),
    'dark gray': this.ts.translate('Bee::Template::Tag::dark gray'),
    ecommerce: this.ts.translate('Bee::Template::Tag::ecommerce'),
    survey: this.ts.translate('Bee::Template::Tag::survey'),
    'dark green': this.ts.translate('Bee::Template::Tag::dark green'),
    propose: this.ts.translate('Bee::Template::Tag::propose'),
    download: this.ts.translate('Bee::Template::Tag::download'),
    notify: this.ts.translate('Bee::Template::Tag::notify'),
    'black friday': this.ts.translate('Bee::Template::Tag::black friday'),
    halloween: this.ts.translate('Bee::Template::Tag::halloween'),
    autumn: this.ts.translate('Bee::Template::Tag::autumn'),
    october: this.ts.translate('Bee::Template::Tag::october'),
    party: this.ts.translate('Bee::Template::Tag::party'),
    holidays: this.ts.translate('Bee::Template::Tag::holidays'),
    christmas: this.ts.translate('Bee::Template::Tag::christmas'),
    ornaments: this.ts.translate('Bee::Template::Tag::ornaments'),
    rainbow: this.ts.translate('Bee::Template::Tag::rainbow'),
    paper: this.ts.translate('Bee::Template::Tag::paper'),
    music: this.ts.translate('Bee::Template::Tag::music'),
    'three column': this.ts.translate('Bee::Template::Tag::three column'),
    'sell tickets': this.ts.translate('Bee::Template::Tag::sell tickets'),
    merchandising: this.ts.translate('Bee::Template::Tag::merchandising'),
    fitness: this.ts.translate('Bee::Template::Tag::fitness'),
    school: this.ts.translate('Bee::Template::Tag::school'),
    'four column': this.ts.translate('Bee::Template::Tag::four column'),
    dicount: this.ts.translate('Bee::Template::Tag::dicount'),
    watermelon: this.ts.translate('Bee::Template::Tag::watermelon'),
    'one column': this.ts.translate('Bee::Template::Tag::one column'),
    donuts: this.ts.translate('Bee::Template::Tag::donuts'),
    invitation: this.ts.translate('Bee::Template::Tag::invitation'),
    'two column': this.ts.translate('Bee::Template::Tag::two column'),
    skating: this.ts.translate('Bee::Template::Tag::skating'),
    boy: this.ts.translate('Bee::Template::Tag::boy'),
    game: this.ts.translate('Bee::Template::Tag::game'),
    football: this.ts.translate('Bee::Template::Tag::football'),
    match: this.ts.translate('Bee::Template::Tag::match'),
    soccer: this.ts.translate('Bee::Template::Tag::soccer'),
    team: this.ts.translate('Bee::Template::Tag::team'),
    university: this.ts.translate('Bee::Template::Tag::university'),
    'book now': this.ts.translate('Bee::Template::Tag::book now'),
    hotel: this.ts.translate('Bee::Template::Tag::hotel'),
    'san valentine': this.ts.translate('Bee::Template::Tag::san valentine'),
    love: this.ts.translate('Bee::Template::Tag::love'),
    dating: this.ts.translate('Bee::Template::Tag::dating'),
    shopping: this.ts.translate('Bee::Template::Tag::shopping'),
    gold: this.ts.translate('Bee::Template::Tag::gold'),
    promotion: this.ts.translate('Bee::Template::Tag::promotion'),
    car: this.ts.translate('Bee::Template::Tag::car'),
    'rent service': this.ts.translate('Bee::Template::Tag::rent service'),
    rental: this.ts.translate('Bee::Template::Tag::rental'),
    rent: this.ts.translate('Bee::Template::Tag::rent'),
    internet: this.ts.translate('Bee::Template::Tag::internet'),
    academy: this.ts.translate('Bee::Template::Tag::academy'),
    certification: this.ts.translate('Bee::Template::Tag::certification'),
    webinar: this.ts.translate('Bee::Template::Tag::webinar'),
    course: this.ts.translate('Bee::Template::Tag::course'),
    learning: this.ts.translate('Bee::Template::Tag::learning'),
    bedroom: this.ts.translate('Bee::Template::Tag::bedroom'),
    reservation: this.ts.translate('Bee::Template::Tag::reservation'),
    bnb: this.ts.translate('Bee::Template::Tag::bnb'),
    journal: this.ts.translate('Bee::Template::Tag::journal'),
    newspaper: this.ts.translate('Bee::Template::Tag::newspaper'),
    magazine: this.ts.translate('Bee::Template::Tag::magazine'),
    articles: this.ts.translate('Bee::Template::Tag::articles'),
    'black&white': this.ts.translate('Bee::Template::Tag::black&white'),
    arrow: this.ts.translate('Bee::Template::Tag::arrow'),
    clothes: this.ts.translate('Bee::Template::Tag::clothes'),
    tech: this.ts.translate('Bee::Template::Tag::tech'),
    'cyber monday': this.ts.translate('Bee::Template::Tag::cyber monday'),
    watch: this.ts.translate('Bee::Template::Tag::watch'),
    '2019': this.ts.translate('Bee::Template::Tag::2019'),
    toys: this.ts.translate('Bee::Template::Tag::toys'),
    letter: this.ts.translate('Bee::Template::Tag::letter'),
    'new year': this.ts.translate('Bee::Template::Tag::new year'),
    fireworks: this.ts.translate('Bee::Template::Tag::fireworks'),
    bird: this.ts.translate('Bee::Template::Tag::bird'),
    snow: this.ts.translate('Bee::Template::Tag::snow'),
    bot: this.ts.translate('Bee::Template::Tag::bot'),
    chat: this.ts.translate('Bee::Template::Tag::chat'),
    software: this.ts.translate('Bee::Template::Tag::software'),
    video: this.ts.translate('Bee::Template::Tag::video'),
    snowboard: this.ts.translate('Bee::Template::Tag::snowboard'),
    winter: this.ts.translate('Bee::Template::Tag::winter'),
    ski: this.ts.translate('Bee::Template::Tag::ski'),
    hiking: this.ts.translate('Bee::Template::Tag::hiking'),
    'three-columns': this.ts.translate('Bee::Template::Tag::three-columns'),
    mountain: this.ts.translate('Bee::Template::Tag::mountain'),
    heart: this.ts.translate('Bee::Template::Tag::heart'),
    violet: this.ts.translate('Bee::Template::Tag::violet'),
    seasonal: this.ts.translate('Bee::Template::Tag::seasonal'),
    'st valentine': this.ts.translate('Bee::Template::Tag::st valentine'),
    'saint valentine': this.ts.translate('Bee::Template::Tag::saint valentine'),
    valentine: this.ts.translate('Bee::Template::Tag::valentine'),
    hearts: this.ts.translate('Bee::Template::Tag::hearts'),
    trend: this.ts.translate('Bee::Template::Tag::trend'),
    illustration: this.ts.translate('Bee::Template::Tag::illustration'),
    blog: this.ts.translate('Bee::Template::Tag::blog'),
    coral: this.ts.translate('Bee::Template::Tag::coral'),
    design: this.ts.translate('Bee::Template::Tag::design'),
    designer: this.ts.translate('Bee::Template::Tag::designer'),
    photography: this.ts.translate('Bee::Template::Tag::photography'),
    'color of the year': this.ts.translate('Bee::Template::Tag::color of the year'),
    color: this.ts.translate('Bee::Template::Tag::color'),
    pantone: this.ts.translate('Bee::Template::Tag::pantone'),
    graphics: this.ts.translate('Bee::Template::Tag::graphics'),
    mystery: this.ts.translate('Bee::Template::Tag::mystery'),
    'sans-serif': this.ts.translate('Bee::Template::Tag::sans-serif'),
    'two-columns': this.ts.translate('Bee::Template::Tag::two-columns'),
    projects: this.ts.translate('Bee::Template::Tag::projects'),
    meditation: this.ts.translate('Bee::Template::Tag::meditation'),
    development: this.ts.translate('Bee::Template::Tag::development'),
    application: this.ts.translate('Bee::Template::Tag::application'),
    app: this.ts.translate('Bee::Template::Tag::app'),
    mobile: this.ts.translate('Bee::Template::Tag::mobile'),
    org: this.ts.translate('Bee::Template::Tag::org'),
    children: this.ts.translate('Bee::Template::Tag::children'),
    support: this.ts.translate('Bee::Template::Tag::support'),
    'no profit': this.ts.translate('Bee::Template::Tag::no profit'),
    charity: this.ts.translate('Bee::Template::Tag::charity'),
    'giving day': this.ts.translate('Bee::Template::Tag::giving day'),
    prize: this.ts.translate('Bee::Template::Tag::prize'),
    'gift card': this.ts.translate('Bee::Template::Tag::gift card'),
    carnival: this.ts.translate('Bee::Template::Tag::carnival'),
    'mardi gras': this.ts.translate('Bee::Template::Tag::mardi gras'),
    dj: this.ts.translate('Bee::Template::Tag::dj'),
    event: this.ts.translate('Bee::Template::Tag::event'),
    mask: this.ts.translate('Bee::Template::Tag::mask'),
    costume: this.ts.translate('Bee::Template::Tag::costume'),
    parade: this.ts.translate('Bee::Template::Tag::parade'),
    'time sensitive': this.ts.translate('Bee::Template::Tag::time sensitive'),
    urgency: this.ts.translate('Bee::Template::Tag::urgency'),
    registration: this.ts.translate('Bee::Template::Tag::registration'),
    activation: this.ts.translate('Bee::Template::Tag::activation'),
    username: this.ts.translate('Bee::Template::Tag::username'),
    'two columns': this.ts.translate('Bee::Template::Tag::two columns'),
    fashion: this.ts.translate('Bee::Template::Tag::fashion'),
    gym: this.ts.translate('Bee::Template::Tag::gym'),
    sports: this.ts.translate('Bee::Template::Tag::sports'),
    question: this.ts.translate('Bee::Template::Tag::question'),
    'welcome email': this.ts.translate('Bee::Template::Tag::welcome email'),
    automation: this.ts.translate('Bee::Template::Tag::automation'),
    welcome: this.ts.translate('Bee::Template::Tag::welcome'),
    workflow: this.ts.translate('Bee::Template::Tag::workflow'),
    series: this.ts.translate('Bee::Template::Tag::series'),
    easter: this.ts.translate('Bee::Template::Tag::easter'),
    sale: this.ts.translate('Bee::Template::Tag::sale'),
    gadget: this.ts.translate('Bee::Template::Tag::gadget'),
    bunny: this.ts.translate('Bee::Template::Tag::bunny'),
    creative: this.ts.translate('Bee::Template::Tag::creative'),
    typography: this.ts.translate('Bee::Template::Tag::typography'),
    newsletter: this.ts.translate('Bee::Template::Tag::newsletter'),
    colors: this.ts.translate('Bee::Template::Tag::colors'),
    squared: this.ts.translate('Bee::Template::Tag::squared'),
    square: this.ts.translate('Bee::Template::Tag::square'),
    quotes: this.ts.translate('Bee::Template::Tag::quotes'),
    duotone: this.ts.translate('Bee::Template::Tag::duotone'),
    onboarding: this.ts.translate('Bee::Template::Tag::onboarding'),
    'welcome message': this.ts.translate('Bee::Template::Tag::welcome message'),
    resort: this.ts.translate('Bee::Template::Tag::resort'),
    wellness: this.ts.translate('Bee::Template::Tag::wellness'),
    soft: this.ts.translate('Bee::Template::Tag::soft'),
    spa: this.ts.translate('Bee::Template::Tag::spa'),
    'hurry up': this.ts.translate('Bee::Template::Tag::hurry up'),
    hurry: this.ts.translate('Bee::Template::Tag::hurry'),
    promotional: this.ts.translate('Bee::Template::Tag::promotional'),
    'limited time': this.ts.translate('Bee::Template::Tag::limited time'),
    animation: this.ts.translate('Bee::Template::Tag::animation'),
    'last chance': this.ts.translate('Bee::Template::Tag::last chance'),
    time: this.ts.translate('Bee::Template::Tag::time'),
    product: this.ts.translate('Bee::Template::Tag::product'),
    features: this.ts.translate('Bee::Template::Tag::features'),
    icons: this.ts.translate('Bee::Template::Tag::icons'),
    technology: this.ts.translate('Bee::Template::Tag::technology'),
    prizes: this.ts.translate('Bee::Template::Tag::prizes'),
    win: this.ts.translate('Bee::Template::Tag::win'),
    engagement: this.ts.translate('Bee::Template::Tag::engagement'),
    community: this.ts.translate('Bee::Template::Tag::community'),
    tickets: this.ts.translate('Bee::Template::Tag::tickets'),
    'earn money': this.ts.translate('Bee::Template::Tag::earn money'),
    money: this.ts.translate('Bee::Template::Tag::money'),
    vault: this.ts.translate('Bee::Template::Tag::vault'),
    cryptocurrency: this.ts.translate('Bee::Template::Tag::cryptocurrency'),
    'e-commerce': this.ts.translate('Bee::Template::Tag::e-commerce'),
    kit: this.ts.translate('Bee::Template::Tag::kit'),
    set: this.ts.translate('Bee::Template::Tag::set'),
    neutral: this.ts.translate('Bee::Template::Tag::neutral'),
    transactional: this.ts.translate('Bee::Template::Tag::transactional'),
    order: this.ts.translate('Bee::Template::Tag::order'),
    products: this.ts.translate('Bee::Template::Tag::products'),
    browsing: this.ts.translate('Bee::Template::Tag::browsing'),
    barber: this.ts.translate('Bee::Template::Tag::barber'),
    'small business': this.ts.translate('Bee::Template::Tag::small business'),
    list: this.ts.translate('Bee::Template::Tag::list'),
    backpack: this.ts.translate('Bee::Template::Tag::backpack'),
    baggage: this.ts.translate('Bee::Template::Tag::baggage'),
    nomad: this.ts.translate('Bee::Template::Tag::nomad'),
    bag: this.ts.translate('Bee::Template::Tag::bag'),
    travel: this.ts.translate('Bee::Template::Tag::travel'),
    journey: this.ts.translate('Bee::Template::Tag::journey'),
    luggage: this.ts.translate('Bee::Template::Tag::luggage'),
    planner: this.ts.translate('Bee::Template::Tag::planner'),
    professional: this.ts.translate('Bee::Template::Tag::professional'),
    presentation: this.ts.translate('Bee::Template::Tag::presentation'),
    curriculum: this.ts.translate('Bee::Template::Tag::curriculum'),
    personal: this.ts.translate('Bee::Template::Tag::personal'),
    cv: this.ts.translate('Bee::Template::Tag::cv'),
    milestones: this.ts.translate('Bee::Template::Tag::milestones'),
    freelancer: this.ts.translate('Bee::Template::Tag::freelancer'),
    'curriculum vitae': this.ts.translate('Bee::Template::Tag::curriculum vitae'),
    job: this.ts.translate('Bee::Template::Tag::job'),
    work: this.ts.translate('Bee::Template::Tag::work'),
    'qr code': this.ts.translate('Bee::Template::Tag::qr code'),
    qr: this.ts.translate('Bee::Template::Tag::qr'),
    code: this.ts.translate('Bee::Template::Tag::code'),
    updates: this.ts.translate('Bee::Template::Tag::updates'),
    digest: this.ts.translate('Bee::Template::Tag::digest'),
    videogame: this.ts.translate('Bee::Template::Tag::videogame'),
    videogames: this.ts.translate('Bee::Template::Tag::videogames'),
    gaming: this.ts.translate('Bee::Template::Tag::gaming'),
    fancy: this.ts.translate('Bee::Template::Tag::fancy'),
    textmagazine: this.ts.translate('Bee::Template::Tag::textmagazine'),
    'black and white': this.ts.translate('Bee::Template::Tag::black and white'),
    marketing: this.ts.translate('Bee::Template::Tag::marketing'),
    Pro: this.ts.translate('Bee::Template::Tag::Pro'),
    demo: this.ts.translate('Bee::Template::Tag::demo'),
    BEE: this.ts.translate('Bee::Template::Tag::BEE'),
    agency: this.ts.translate('Bee::Template::Tag::agency'),
    feature: this.ts.translate('Bee::Template::Tag::feature'),
    workshop: this.ts.translate('Bee::Template::Tag::workshop'),
    tutorial: this.ts.translate('Bee::Template::Tag::tutorial'),
    service: this.ts.translate('Bee::Template::Tag::service'),
    startup: this.ts.translate('Bee::Template::Tag::startup'),
    tool: this.ts.translate('Bee::Template::Tag::tool'),
    events: this.ts.translate('Bee::Template::Tag::events'),
    'dream job': this.ts.translate('Bee::Template::Tag::dream job'),
    fun: this.ts.translate('Bee::Template::Tag::fun'),
    project: this.ts.translate('Bee::Template::Tag::project'),
    'get-together': this.ts.translate('Bee::Template::Tag::get-together'),
    invites: this.ts.translate('Bee::Template::Tag::invites'),
    family: this.ts.translate('Bee::Template::Tag::family'),
    employee: this.ts.translate('Bee::Template::Tag::employee'),
    friends: this.ts.translate('Bee::Template::Tag::friends'),
    surprise: this.ts.translate('Bee::Template::Tag::surprise'),
    drawings: this.ts.translate('Bee::Template::Tag::drawings'),
    authority: this.ts.translate('Bee::Template::Tag::authority'),
    build: this.ts.translate('Bee::Template::Tag::build'),
    embed: this.ts.translate('Bee::Template::Tag::embed'),
    featured: this.ts.translate('Bee::Template::Tag::featured'),
    places: this.ts.translate('Bee::Template::Tag::places'),
    leisure: this.ts.translate('Bee::Template::Tag::leisure'),
    city: this.ts.translate('Bee::Template::Tag::city'),
    'travel agency': this.ts.translate('Bee::Template::Tag::travel agency'),
    explore: this.ts.translate('Bee::Template::Tag::explore'),
    peace: this.ts.translate('Bee::Template::Tag::peace'),
    'peace day': this.ts.translate('Bee::Template::Tag::peace day'),
    season: this.ts.translate('Bee::Template::Tag::season'),
    glitch: this.ts.translate('Bee::Template::Tag::glitch'),
    convention: this.ts.translate('Bee::Template::Tag::convention'),
    conference: this.ts.translate('Bee::Template::Tag::conference'),
    meeting: this.ts.translate('Bee::Template::Tag::meeting'),
    trendy: this.ts.translate('Bee::Template::Tag::trendy'),
    'coffee break': this.ts.translate('Bee::Template::Tag::coffee break'),
    break: this.ts.translate('Bee::Template::Tag::break'),
    espresso: this.ts.translate('Bee::Template::Tag::espresso'),
    coffee: this.ts.translate('Bee::Template::Tag::coffee'),
    podcast: this.ts.translate('Bee::Template::Tag::podcast'),
    drawn: this.ts.translate('Bee::Template::Tag::drawn'),
    audio: this.ts.translate('Bee::Template::Tag::audio'),
    'hand drawn': this.ts.translate('Bee::Template::Tag::hand drawn'),
    woman: this.ts.translate('Bee::Template::Tag::woman'),
    collection: this.ts.translate('Bee::Template::Tag::collection'),
    accessories: this.ts.translate('Bee::Template::Tag::accessories'),
    bold: this.ts.translate('Bee::Template::Tag::bold'),
    now: this.ts.translate('Bee::Template::Tag::now'),
    'non profit': this.ts.translate('Bee::Template::Tag::non profit'),
    cancer: this.ts.translate('Bee::Template::Tag::cancer'),
    'fund raising': this.ts.translate('Bee::Template::Tag::fund raising'),
    breast: this.ts.translate('Bee::Template::Tag::breast'),
    'breast cancer': this.ts.translate('Bee::Template::Tag::breast cancer'),
    wearitpink: this.ts.translate('Bee::Template::Tag::wearitpink'),
    donation: this.ts.translate('Bee::Template::Tag::donation'),
    unicorn: this.ts.translate('Bee::Template::Tag::unicorn'),
    geometric: this.ts.translate('Bee::Template::Tag::geometric'),
    'graphic design': this.ts.translate('Bee::Template::Tag::graphic design'),
    trial: this.ts.translate('Bee::Template::Tag::trial'),
    'computer & internet': this.ts.translate('Bee::Template::Tag::computer & internet'),
    designers: this.ts.translate('Bee::Template::Tag::designers'),
    'trial endend': this.ts.translate('Bee::Template::Tag::trial endend'),
    undraw: this.ts.translate('Bee::Template::Tag::undraw'),
    learn: this.ts.translate('Bee::Template::Tag::learn'),
    speaker: this.ts.translate('Bee::Template::Tag::speaker'),
    plans: this.ts.translate('Bee::Template::Tag::plans'),
    upsell: this.ts.translate('Bee::Template::Tag::upsell'),
    'get started': this.ts.translate('Bee::Template::Tag::get started'),
    illustrated: this.ts.translate('Bee::Template::Tag::illustrated'),
    'how to': this.ts.translate('Bee::Template::Tag::how to'),
    dashboard: this.ts.translate('Bee::Template::Tag::dashboard'),
    discover: this.ts.translate('Bee::Template::Tag::discover'),
    asian: this.ts.translate('Bee::Template::Tag::asian'),
    customize: this.ts.translate('Bee::Template::Tag::customize'),
    profile: this.ts.translate('Bee::Template::Tag::profile'),
    wecome: this.ts.translate('Bee::Template::Tag::wecome'),
    engage: this.ts.translate('Bee::Template::Tag::engage'),
    reactivate: this.ts.translate('Bee::Template::Tag::reactivate'),
    're-engagement': this.ts.translate('Bee::Template::Tag::re-engagement'),
    flat: this.ts.translate('Bee::Template::Tag::flat'),
    nonprofit: this.ts.translate('Bee::Template::Tag::nonprofit'),
    donations: this.ts.translate('Bee::Template::Tag::donations'),
    wave: this.ts.translate('Bee::Template::Tag::wave'),
    africa: this.ts.translate('Bee::Template::Tag::africa'),
    'watch video': this.ts.translate('Bee::Template::Tag::watch video'),
    dust: this.ts.translate('Bee::Template::Tag::dust'),
    race: this.ts.translate('Bee::Template::Tag::race'),
    speed: this.ts.translate('Bee::Template::Tag::speed'),
    earth: this.ts.translate('Bee::Template::Tag::earth'),
    'get it': this.ts.translate('Bee::Template::Tag::get it'),
    'how it works': this.ts.translate('Bee::Template::Tag::how it works'),
    watercolor: this.ts.translate('Bee::Template::Tag::watercolor'),
    kids: this.ts.translate('Bee::Template::Tag::kids'),
    upgrade: this.ts.translate('Bee::Template::Tag::upgrade'),
    'tourist guide': this.ts.translate('Bee::Template::Tag::tourist guide'),
    ireland: this.ts.translate('Bee::Template::Tag::ireland'),
    beer: this.ts.translate('Bee::Template::Tag::beer'),
    creepy: this.ts.translate('Bee::Template::Tag::creepy'),
    antivirus: this.ts.translate('Bee::Template::Tag::antivirus'),
    'get tickets': this.ts.translate('Bee::Template::Tag::get tickets'),
    led: this.ts.translate('Bee::Template::Tag::led'),
    ios: this.ts.translate('Bee::Template::Tag::ios'),
    android: this.ts.translate('Bee::Template::Tag::android'),
    'shop now': this.ts.translate('Bee::Template::Tag::shop now'),
    food: this.ts.translate('Bee::Template::Tag::food'),
    kitchen: this.ts.translate('Bee::Template::Tag::kitchen'),
    candy: this.ts.translate('Bee::Template::Tag::candy'),
    pastry: this.ts.translate('Bee::Template::Tag::pastry'),
    cook: this.ts.translate('Bee::Template::Tag::cook'),
    recipes: this.ts.translate('Bee::Template::Tag::recipes'),
    film: this.ts.translate('Bee::Template::Tag::film'),
    cinema: this.ts.translate('Bee::Template::Tag::cinema'),
    trailer: this.ts.translate('Bee::Template::Tag::trailer'),
    movie: this.ts.translate('Bee::Template::Tag::movie'),
    premier: this.ts.translate('Bee::Template::Tag::premier'),
    release: this.ts.translate('Bee::Template::Tag::release'),
    joker: this.ts.translate('Bee::Template::Tag::joker'),
    theater: this.ts.translate('Bee::Template::Tag::theater'),
    'book tickets': this.ts.translate('Bee::Template::Tag::book tickets'),
    'fast food': this.ts.translate('Bee::Template::Tag::fast food'),
    sandwich: this.ts.translate('Bee::Template::Tag::sandwich'),
    'food and beverage': this.ts.translate('Bee::Template::Tag::food and beverage'),
    'sandwich day': this.ts.translate('Bee::Template::Tag::sandwich day'),
    'life save': this.ts.translate('Bee::Template::Tag::life save'),
    minimalistic: this.ts.translate('Bee::Template::Tag::minimalistic'),
    'women power': this.ts.translate('Bee::Template::Tag::women power'),
    colofrul: this.ts.translate('Bee::Template::Tag::colofrul'),
    data: this.ts.translate('Bee::Template::Tag::data'),
    research: this.ts.translate('Bee::Template::Tag::research'),
    'life saving': this.ts.translate('Bee::Template::Tag::life saving'),
    fundraiser: this.ts.translate('Bee::Template::Tag::fundraiser'),
    brush: this.ts.translate('Bee::Template::Tag::brush'),
    pastel: this.ts.translate('Bee::Template::Tag::pastel'),
    lipstick: this.ts.translate('Bee::Template::Tag::lipstick'),
    cosmetics: this.ts.translate('Bee::Template::Tag::cosmetics'),
    palette: this.ts.translate('Bee::Template::Tag::palette'),
    shoe: this.ts.translate('Bee::Template::Tag::shoe'),
    diagonal: this.ts.translate('Bee::Template::Tag::diagonal'),
    'see offers': this.ts.translate('Bee::Template::Tag::see offers'),
    'protein supplements': this.ts.translate('Bee::Template::Tag::protein supplements'),
    dynamic: this.ts.translate('Bee::Template::Tag::dynamic'),
    'product promotion': this.ts.translate('Bee::Template::Tag::product promotion'),
    cybermonday: this.ts.translate('Bee::Template::Tag::cybermonday'),
    deals: this.ts.translate('Bee::Template::Tag::deals'),
    blackfriday: this.ts.translate('Bee::Template::Tag::blackfriday'),
    healthylife: this.ts.translate('Bee::Template::Tag::healthylife'),
    basketball: this.ts.translate('Bee::Template::Tag::basketball'),
    'net promoter score': this.ts.translate('Bee::Template::Tag::net promoter score'),
    boxed: this.ts.translate('Bee::Template::Tag::boxed'),
    nps: this.ts.translate('Bee::Template::Tag::nps'),
    'web app': this.ts.translate('Bee::Template::Tag::web app'),
    'social network': this.ts.translate('Bee::Template::Tag::social network'),
    'social app': this.ts.translate('Bee::Template::Tag::social app'),
    emojis: this.ts.translate('Bee::Template::Tag::emojis'),
    delivery: this.ts.translate('Bee::Template::Tag::delivery'),
    thanksgiving: this.ts.translate('Bee::Template::Tag::thanksgiving'),
    menu: this.ts.translate('Bee::Template::Tag::menu'),
    calligraphy: this.ts.translate('Bee::Template::Tag::calligraphy'),
    cooking: this.ts.translate('Bee::Template::Tag::cooking'),
    strong: this.ts.translate('Bee::Template::Tag::strong'),
    'light orange': this.ts.translate('Bee::Template::Tag::light orange'),
    turkey: this.ts.translate('Bee::Template::Tag::turkey'),
    'pumpkin pie': this.ts.translate('Bee::Template::Tag::pumpkin pie'),
    smallbusinesssaturday: this.ts.translate('Bee::Template::Tag::smallbusinesssaturday'),
    veggies: this.ts.translate('Bee::Template::Tag::veggies'),
    grocery: this.ts.translate('Bee::Template::Tag::grocery'),
    'santa claus': this.ts.translate('Bee::Template::Tag::santa claus'),
    joyful: this.ts.translate('Bee::Template::Tag::joyful'),
    hanukkah: this.ts.translate('Bee::Template::Tag::hanukkah'),
    reminder: this.ts.translate('Bee::Template::Tag::reminder'),
    hannukah: this.ts.translate('Bee::Template::Tag::hannukah'),
    december: this.ts.translate('Bee::Template::Tag::december'),
    jerusalem: this.ts.translate('Bee::Template::Tag::jerusalem'),
    judaism: this.ts.translate('Bee::Template::Tag::judaism'),
    candles: this.ts.translate('Bee::Template::Tag::candles'),
    'new years eve': this.ts.translate('Bee::Template::Tag::new years eve'),
    'happy new year': this.ts.translate('Bee::Template::Tag::happy new year'),
    newyear: this.ts.translate('Bee::Template::Tag::newyear'),
    abstract: this.ts.translate('Bee::Template::Tag::abstract'),
    '2020': this.ts.translate('Bee::Template::Tag::2020'),
    joy: this.ts.translate('Bee::Template::Tag::joy'),
    celebration: this.ts.translate('Bee::Template::Tag::celebration'),
    holy: this.ts.translate('Bee::Template::Tag::holy'),
    faith: this.ts.translate('Bee::Template::Tag::faith'),
    'chinese new year': this.ts.translate('Bee::Template::Tag::chinese new year'),
    tradition: this.ts.translate('Bee::Template::Tag::tradition'),
    china: this.ts.translate('Bee::Template::Tag::china'),
    restaurant: this.ts.translate('Bee::Template::Tag::restaurant'),
    'chinese restaurant': this.ts.translate('Bee::Template::Tag::chinese restaurant'),
    'new york restaurant': this.ts.translate('Bee::Template::Tag::new york restaurant'),
    roboto: this.ts.translate('Bee::Template::Tag::roboto'),
    chinese: this.ts.translate('Bee::Template::Tag::chinese'),
    photographic: this.ts.translate('Bee::Template::Tag::photographic'),
    'year of the rat': this.ts.translate('Bee::Template::Tag::year of the rat'),
    proposal: this.ts.translate('Bee::Template::Tag::proposal'),
    company: this.ts.translate('Bee::Template::Tag::company'),
    clear: this.ts.translate('Bee::Template::Tag::clear'),
    businessman: this.ts.translate('Bee::Template::Tag::businessman'),
    characters: this.ts.translate('Bee::Template::Tag::characters'),
    services: this.ts.translate('Bee::Template::Tag::services'),
    'laundry service': this.ts.translate('Bee::Template::Tag::laundry service'),
    'meeting confirmation': this.ts.translate('Bee::Template::Tag::meeting confirmation'),
    laundry: this.ts.translate('Bee::Template::Tag::laundry'),
    'strawberry red': this.ts.translate('Bee::Template::Tag::strawberry red'),
    'valentine day': this.ts.translate('Bee::Template::Tag::valentine day'),
    "valentine's day": this.ts.translate("Bee::Template::Tag::valentine's day"),
    'super bowl 2020': this.ts.translate('Bee::Template::Tag::super bowl 2020'),
    'super bowl liv': this.ts.translate('Bee::Template::Tag::super bowl liv'),
    'super bowl': this.ts.translate('Bee::Template::Tag::super bowl'),
    championship: this.ts.translate('Bee::Template::Tag::championship'),
    'american football': this.ts.translate('Bee::Template::Tag::american football'),
    'reserve your spot': this.ts.translate('Bee::Template::Tag::reserve your spot'),
    location: this.ts.translate('Bee::Template::Tag::location'),
    media: this.ts.translate('Bee::Template::Tag::media'),
    pattern: this.ts.translate('Bee::Template::Tag::pattern'),
    panelist: this.ts.translate('Bee::Template::Tag::panelist'),
    texture: this.ts.translate('Bee::Template::Tag::texture'),
    map: this.ts.translate('Bee::Template::Tag::map'),
    mistery: this.ts.translate('Bee::Template::Tag::mistery'),
    'valentines day': this.ts.translate('Bee::Template::Tag::valentines day'),
    picture: this.ts.translate('Bee::Template::Tag::picture'),
    handdrawn: this.ts.translate('Bee::Template::Tag::handdrawn'),
    hearth: this.ts.translate('Bee::Template::Tag::hearth'),
    polaroid: this.ts.translate('Bee::Template::Tag::polaroid'),
    moments: this.ts.translate('Bee::Template::Tag::moments'),
    memories: this.ts.translate('Bee::Template::Tag::memories'),
    frame: this.ts.translate('Bee::Template::Tag::frame'),
    recap: this.ts.translate('Bee::Template::Tag::recap'),
    summary: this.ts.translate('Bee::Template::Tag::summary'),
    'follow up': this.ts.translate('Bee::Template::Tag::follow up'),
    report: this.ts.translate('Bee::Template::Tag::report'),
    'year in review': this.ts.translate('Bee::Template::Tag::year in review'),
    bill: this.ts.translate('Bee::Template::Tag::bill'),
    colourful: this.ts.translate('Bee::Template::Tag::colourful'),
    bookkeeping: this.ts.translate('Bee::Template::Tag::bookkeeping'),
    accounting: this.ts.translate('Bee::Template::Tag::accounting'),
    multipurpose: this.ts.translate('Bee::Template::Tag::multipurpose'),
    dogs: this.ts.translate('Bee::Template::Tag::dogs'),
    pets: this.ts.translate('Bee::Template::Tag::pets'),
    cats: this.ts.translate('Bee::Template::Tag::cats'),
    'pet shop': this.ts.translate('Bee::Template::Tag::pet shop'),
    vet: this.ts.translate('Bee::Template::Tag::vet'),
    veterinary: this.ts.translate('Bee::Template::Tag::veterinary'),
    law: this.ts.translate('Bee::Template::Tag::law'),
    mardigras: this.ts.translate('Bee::Template::Tag::mardigras'),
    stars: this.ts.translate('Bee::Template::Tag::stars'),
    tape: this.ts.translate('Bee::Template::Tag::tape'),
    "father's day campaign": this.ts.translate("Bee::Template::Tag::father's day campaign"),
    "father's day": this.ts.translate("Bee::Template::Tag::father's day"),
    father: this.ts.translate('Bee::Template::Tag::father'),
    camera: this.ts.translate('Bee::Template::Tag::camera'),
    fatherhood: this.ts.translate('Bee::Template::Tag::fatherhood'),
    '8': this.ts.translate('Bee::Template::Tag::8'),
    'special sale': this.ts.translate('Bee::Template::Tag::special sale'),
    march: this.ts.translate('Bee::Template::Tag::march'),
    '8 march': this.ts.translate('Bee::Template::Tag::8 march'),
    women: this.ts.translate('Bee::Template::Tag::women'),
    "international women's day": this.ts.translate("Bee::Template::Tag::international women's day"),
    female: this.ts.translate('Bee::Template::Tag::female'),
    'nonprofit organization': this.ts.translate('Bee::Template::Tag::nonprofit organization'),
    npo: this.ts.translate('Bee::Template::Tag::npo'),
    festival: this.ts.translate('Bee::Template::Tag::festival'),
    fundraising: this.ts.translate('Bee::Template::Tag::fundraising'),
    'good luck': this.ts.translate('Bee::Template::Tag::good luck'),
    "saint patrick's day": this.ts.translate("Bee::Template::Tag::saint patrick's day"),
    fortune: this.ts.translate('Bee::Template::Tag::fortune'),
    'st patricks day': this.ts.translate('Bee::Template::Tag::st patricks day'),
    emoji: this.ts.translate('Bee::Template::Tag::emoji'),
    questions: this.ts.translate('Bee::Template::Tag::questions'),
    opinion: this.ts.translate('Bee::Template::Tag::opinion'),
    megaphone: this.ts.translate('Bee::Template::Tag::megaphone'),
    questionnaire: this.ts.translate('Bee::Template::Tag::questionnaire'),
    'thumbs up': this.ts.translate('Bee::Template::Tag::thumbs up'),
    pool: this.ts.translate('Bee::Template::Tag::pool'),
    chart: this.ts.translate('Bee::Template::Tag::chart'),
    'flash sale': this.ts.translate('Bee::Template::Tag::flash sale'),
    counter: this.ts.translate('Bee::Template::Tag::counter'),
    memphis: this.ts.translate('Bee::Template::Tag::memphis'),
    spring: this.ts.translate('Bee::Template::Tag::spring'),
    'just kidding': this.ts.translate('Bee::Template::Tag::just kidding'),
    funny: this.ts.translate('Bee::Template::Tag::funny'),
    "april fools' day": this.ts.translate("Bee::Template::Tag::april fools' day"),
    'easter egg': this.ts.translate('Bee::Template::Tag::easter egg'),
    'spring green': this.ts.translate('Bee::Template::Tag::spring green'),
    faq: this.ts.translate('Bee::Template::Tag::faq'),
    shipment: this.ts.translate('Bee::Template::Tag::shipment'),
    transportation: this.ts.translate('Bee::Template::Tag::transportation'),
    shipping: this.ts.translate('Bee::Template::Tag::shipping'),
    automotive: this.ts.translate('Bee::Template::Tag::automotive'),
    dealer: this.ts.translate('Bee::Template::Tag::dealer'),
    'test drive': this.ts.translate('Bee::Template::Tag::test drive'),
    egg: this.ts.translate('Bee::Template::Tag::egg'),
    activities: this.ts.translate('Bee::Template::Tag::activities'),
    highlights: this.ts.translate('Bee::Template::Tag::highlights'),
    chalkboard: this.ts.translate('Bee::Template::Tag::chalkboard'),
    'calendar activities': this.ts.translate('Bee::Template::Tag::calendar activities'),
    calendar: this.ts.translate('Bee::Template::Tag::calendar'),
    comics: this.ts.translate('Bee::Template::Tag::comics'),
    eggs: this.ts.translate('Bee::Template::Tag::eggs'),
    beverage: this.ts.translate('Bee::Template::Tag::beverage'),
    teaser: this.ts.translate('Bee::Template::Tag::teaser'),
    sweepstakes: this.ts.translate('Bee::Template::Tag::sweepstakes'),
    'mystery deal': this.ts.translate('Bee::Template::Tag::mystery deal'),
    lottery: this.ts.translate('Bee::Template::Tag::lottery'),
    'mystery box': this.ts.translate('Bee::Template::Tag::mystery box'),
    'earth day': this.ts.translate('Bee::Template::Tag::earth day'),
    eco: this.ts.translate('Bee::Template::Tag::eco'),
    short: this.ts.translate('Bee::Template::Tag::short'),
    covid: this.ts.translate('Bee::Template::Tag::covid'),
    coronavirus: this.ts.translate('Bee::Template::Tag::coronavirus'),
    protection: this.ts.translate('Bee::Template::Tag::protection'),
    prevention: this.ts.translate('Bee::Template::Tag::prevention'),
    covid19: this.ts.translate('Bee::Template::Tag::covid19'),
    'covid-19': this.ts.translate('Bee::Template::Tag::covid-19'),
    'crisis email': this.ts.translate('Bee::Template::Tag::crisis email'),
    floral: this.ts.translate('Bee::Template::Tag::floral'),
    'special offer': this.ts.translate('Bee::Template::Tag::special offer'),
    "mother's day": this.ts.translate("Bee::Template::Tag::mother's day"),
    night: this.ts.translate('Bee::Template::Tag::night'),
    balloon: this.ts.translate('Bee::Template::Tag::balloon'),
    fiesta: this.ts.translate('Bee::Template::Tag::fiesta'),
    'meal deal': this.ts.translate('Bee::Template::Tag::meal deal'),
    mexican: this.ts.translate('Bee::Template::Tag::mexican'),
    'cinco de mayo': this.ts.translate('Bee::Template::Tag::cinco de mayo'),
    memorial: this.ts.translate('Bee::Template::Tag::memorial'),
    mexico: this.ts.translate('Bee::Template::Tag::mexico'),
    'weekly program': this.ts.translate('Bee::Template::Tag::weekly program'),
    education: this.ts.translate('Bee::Template::Tag::education'),
    'self-care': this.ts.translate('Bee::Template::Tag::self-care'),
    feminine: this.ts.translate('Bee::Template::Tag::feminine'),
    gifts: this.ts.translate('Bee::Template::Tag::gifts'),
    mom: this.ts.translate('Bee::Template::Tag::mom'),
    influencer: this.ts.translate('Bee::Template::Tag::influencer'),
    social: this.ts.translate('Bee::Template::Tag::social'),
    'latest posts': this.ts.translate('Bee::Template::Tag::latest posts'),
    'pet food': this.ts.translate('Bee::Template::Tag::pet food'),
    'new product': this.ts.translate('Bee::Template::Tag::new product'),
    'national pet week': this.ts.translate('Bee::Template::Tag::national pet week'),
    present: this.ts.translate('Bee::Template::Tag::present'),
    giftbox: this.ts.translate('Bee::Template::Tag::giftbox'),
    'food & beverage': this.ts.translate('Bee::Template::Tag::food & beverage'),
    celebrations: this.ts.translate('Bee::Template::Tag::celebrations'),
    may: this.ts.translate('Bee::Template::Tag::may'),
    'national salad month': this.ts.translate('Bee::Template::Tag::national salad month'),
    healthy: this.ts.translate('Bee::Template::Tag::healthy'),
    vegetables: this.ts.translate('Bee::Template::Tag::vegetables'),
    greens: this.ts.translate('Bee::Template::Tag::greens'),
    salad: this.ts.translate('Bee::Template::Tag::salad'),
    recipe: this.ts.translate('Bee::Template::Tag::recipe'),
    'job search': this.ts.translate('Bee::Template::Tag::job search'),
    videographer: this.ts.translate('Bee::Template::Tag::videographer'),
    'self promotion': this.ts.translate('Bee::Template::Tag::self promotion'),
    'video editor': this.ts.translate('Bee::Template::Tag::video editor'),
    call: this.ts.translate('Bee::Template::Tag::call'),
    'video call': this.ts.translate('Bee::Template::Tag::video call'),
    'video conference': this.ts.translate('Bee::Template::Tag::video conference'),
    remote: this.ts.translate('Bee::Template::Tag::remote'),
    zine: this.ts.translate('Bee::Template::Tag::zine'),
    resume: this.ts.translate('Bee::Template::Tag::resume'),
    experience: this.ts.translate('Bee::Template::Tag::experience'),
    photographer: this.ts.translate('Bee::Template::Tag::photographer'),
    cat: this.ts.translate('Bee::Template::Tag::cat'),
    dog: this.ts.translate('Bee::Template::Tag::dog'),
    petweek: this.ts.translate('Bee::Template::Tag::petweek'),
    animal: this.ts.translate('Bee::Template::Tag::animal'),
    'conference call': this.ts.translate('Bee::Template::Tag::conference call'),
    testimonial: this.ts.translate('Bee::Template::Tag::testimonial'),
    house: this.ts.translate('Bee::Template::Tag::house'),
    'read article': this.ts.translate('Bee::Template::Tag::read article'),
    'real estate': this.ts.translate('Bee::Template::Tag::real estate'),
    listings: this.ts.translate('Bee::Template::Tag::listings'),
    property: this.ts.translate('Bee::Template::Tag::property'),
    agent: this.ts.translate('Bee::Template::Tag::agent'),
    'contact us': this.ts.translate('Bee::Template::Tag::contact us'),
    home: this.ts.translate('Bee::Template::Tag::home'),
    'view property': this.ts.translate('Bee::Template::Tag::view property'),
    search: this.ts.translate('Bee::Template::Tag::search'),
    'make an offer': this.ts.translate('Bee::Template::Tag::make an offer'),
    find: this.ts.translate('Bee::Template::Tag::find'),
    'select date': this.ts.translate('Bee::Template::Tag::select date'),
    'schedule a visit': this.ts.translate('Bee::Template::Tag::schedule a visit'),
    address: this.ts.translate('Bee::Template::Tag::address'),
    telephone: this.ts.translate('Bee::Template::Tag::telephone'),
    'google maps': this.ts.translate('Bee::Template::Tag::google maps'),
    'add to calendar': this.ts.translate('Bee::Template::Tag::add to calendar'),
    email: this.ts.translate('Bee::Template::Tag::email'),
    repair: this.ts.translate('Bee::Template::Tag::repair'),
    'car dealer': this.ts.translate('Bee::Template::Tag::car dealer'),
    'dark there': this.ts.translate('Bee::Template::Tag::dark there'),
    auto: this.ts.translate('Bee::Template::Tag::auto'),
    cut: this.ts.translate('Bee::Template::Tag::cut'),
    salon: this.ts.translate('Bee::Template::Tag::salon'),
    hair: this.ts.translate('Bee::Template::Tag::hair'),
    hairdresser: this.ts.translate('Bee::Template::Tag::hairdresser'),
    'beauty salon': this.ts.translate('Bee::Template::Tag::beauty salon'),
    decoration: this.ts.translate('Bee::Template::Tag::decoration'),
    flower: this.ts.translate('Bee::Template::Tag::flower'),
    plant: this.ts.translate('Bee::Template::Tag::plant'),
    flourish: this.ts.translate('Bee::Template::Tag::flourish'),
    mothersday: this.ts.translate('Bee::Template::Tag::mothersday'),
    'graphic designer': this.ts.translate('Bee::Template::Tag::graphic designer'),
    works: this.ts.translate('Bee::Template::Tag::works'),
    ux: this.ts.translate('Bee::Template::Tag::ux'),
    'web design': this.ts.translate('Bee::Template::Tag::web design'),
    'web designer': this.ts.translate('Bee::Template::Tag::web designer'),
    ui: this.ts.translate('Bee::Template::Tag::ui'),
    skills: this.ts.translate('Bee::Template::Tag::skills'),
    'service promotion': this.ts.translate('Bee::Template::Tag::service promotion'),
    introduction: this.ts.translate('Bee::Template::Tag::introduction'),
    'pride events': this.ts.translate('Bee::Template::Tag::pride events'),
    lgbtq: this.ts.translate('Bee::Template::Tag::lgbtq'),
    'pride parade': this.ts.translate('Bee::Template::Tag::pride parade'),
    lgbt: this.ts.translate('Bee::Template::Tag::lgbt'),
    pride: this.ts.translate('Bee::Template::Tag::pride'),
    'pride may': this.ts.translate('Bee::Template::Tag::pride may'),
    makeup: this.ts.translate('Bee::Template::Tag::makeup'),
    'seasonal promotion': this.ts.translate('Bee::Template::Tag::seasonal promotion'),
    'product launch': this.ts.translate('Bee::Template::Tag::product launch'),
    'special offers': this.ts.translate('Bee::Template::Tag::special offers'),
    dad: this.ts.translate('Bee::Template::Tag::dad'),
    body: this.ts.translate('Bee::Template::Tag::body'),
    beauty: this.ts.translate('Bee::Template::Tag::beauty'),
    'pride day': this.ts.translate('Bee::Template::Tag::pride day'),
    face: this.ts.translate('Bee::Template::Tag::face'),
    souls: this.ts.translate('Bee::Template::Tag::souls'),
    'gay pride': this.ts.translate('Bee::Template::Tag::gay pride'),
    sea: this.ts.translate('Bee::Template::Tag::sea'),
    seafood: this.ts.translate('Bee::Template::Tag::seafood'),
    fish: this.ts.translate('Bee::Template::Tag::fish'),
    waves: this.ts.translate('Bee::Template::Tag::waves'),
    register: this.ts.translate('Bee::Template::Tag::register'),
    talk: this.ts.translate('Bee::Template::Tag::talk'),
    website: this.ts.translate('Bee::Template::Tag::website'),
    speakers: this.ts.translate('Bee::Template::Tag::speakers'),
    electronics: this.ts.translate('Bee::Template::Tag::electronics'),
    vr: this.ts.translate('Bee::Template::Tag::vr'),
    devices: this.ts.translate('Bee::Template::Tag::devices'),
    strategy: this.ts.translate('Bee::Template::Tag::strategy'),
    strategic: this.ts.translate('Bee::Template::Tag::strategic'),
    innovation: this.ts.translate('Bee::Template::Tag::innovation'),
    branding: this.ts.translate('Bee::Template::Tag::branding'),
    web: this.ts.translate('Bee::Template::Tag::web'),
    running: this.ts.translate('Bee::Template::Tag::running'),
    mistake: this.ts.translate('Bee::Template::Tag::mistake'),
    'coupon code': this.ts.translate('Bee::Template::Tag::coupon code'),
    apologize: this.ts.translate('Bee::Template::Tag::apologize'),
    trainers: this.ts.translate('Bee::Template::Tag::trainers'),
    'abandoned cart': this.ts.translate('Bee::Template::Tag::abandoned cart'),
    sneakers: this.ts.translate('Bee::Template::Tag::sneakers'),
    clothing: this.ts.translate('Bee::Template::Tag::clothing'),
    'forgot to check out': this.ts.translate('Bee::Template::Tag::forgot to check out'),
    'shopping cart': this.ts.translate('Bee::Template::Tag::shopping cart'),
    'online shopping': this.ts.translate('Bee::Template::Tag::online shopping'),
    rose: this.ts.translate('Bee::Template::Tag::rose'),
    password: this.ts.translate('Bee::Template::Tag::password'),
    playful: this.ts.translate('Bee::Template::Tag::playful'),
    key: this.ts.translate('Bee::Template::Tag::key'),
    feedback: this.ts.translate('Bee::Template::Tag::feedback'),
    electronic: this.ts.translate('Bee::Template::Tag::electronic'),
    headphone: this.ts.translate('Bee::Template::Tag::headphone'),
    minimalist: this.ts.translate('Bee::Template::Tag::minimalist'),
    verification: this.ts.translate('Bee::Template::Tag::verification'),
    payment: this.ts.translate('Bee::Template::Tag::payment'),
    inbox: this.ts.translate('Bee::Template::Tag::inbox'),
    carta: this.ts.translate('Bee::Template::Tag::carta'),
    abandoned: this.ts.translate('Bee::Template::Tag::abandoned'),
    recovery: this.ts.translate('Bee::Template::Tag::recovery'),
    daddy: this.ts.translate('Bee::Template::Tag::daddy'),
    celebrate: this.ts.translate('Bee::Template::Tag::celebrate'),
    cake: this.ts.translate('Bee::Template::Tag::cake'),
    appreciation: this.ts.translate('Bee::Template::Tag::appreciation'),
    candle: this.ts.translate('Bee::Template::Tag::candle'),
    special: this.ts.translate('Bee::Template::Tag::special'),
    cupcake: this.ts.translate('Bee::Template::Tag::cupcake'),
    'discount code': this.ts.translate('Bee::Template::Tag::discount code'),
    perks: this.ts.translate('Bee::Template::Tag::perks'),
    loyalty: this.ts.translate('Bee::Template::Tag::loyalty'),
    'customer appreciation': this.ts.translate('Bee::Template::Tag::customer appreciation'),
    webshop: this.ts.translate('Bee::Template::Tag::webshop'),
    tshirt: this.ts.translate('Bee::Template::Tag::tshirt'),
    't-shirt': this.ts.translate('Bee::Template::Tag::t-shirt'),
    monochrome: this.ts.translate('Bee::Template::Tag::monochrome'),
    'business services': this.ts.translate('Bee::Template::Tag::business services'),
    'work with us': this.ts.translate('Bee::Template::Tag::work with us'),
    studio: this.ts.translate('Bee::Template::Tag::studio'),
    architecture: this.ts.translate('Bee::Template::Tag::architecture'),
    'interio design': this.ts.translate('Bee::Template::Tag::interio design'),
    lines: this.ts.translate('Bee::Template::Tag::lines'),
    interior: this.ts.translate('Bee::Template::Tag::interior'),
    glamour: this.ts.translate('Bee::Template::Tag::glamour'),
    'interior design': this.ts.translate('Bee::Template::Tag::interior design'),
    edgy: this.ts.translate('Bee::Template::Tag::edgy'),
    'educational courses': this.ts.translate('Bee::Template::Tag::educational courses'),
    'online courses': this.ts.translate('Bee::Template::Tag::online courses'),
    'independence day': this.ts.translate('Bee::Template::Tag::independence day'),
    'july fourth': this.ts.translate('Bee::Template::Tag::july fourth'),
    finance: this.ts.translate('Bee::Template::Tag::finance'),
    bank: this.ts.translate('Bee::Template::Tag::bank'),
    loan: this.ts.translate('Bee::Template::Tag::loan'),
    'blue red white': this.ts.translate('Bee::Template::Tag::blue red white'),
    usa: this.ts.translate('Bee::Template::Tag::usa'),
    'stars and stripes': this.ts.translate('Bee::Template::Tag::stars and stripes'),
    '4th july': this.ts.translate('Bee::Template::Tag::4th july'),
    festive: this.ts.translate('Bee::Template::Tag::festive'),
    'glitch effect': this.ts.translate('Bee::Template::Tag::glitch effect'),
    'computer games': this.ts.translate('Bee::Template::Tag::computer games'),
    'dark mode': this.ts.translate('Bee::Template::Tag::dark mode'),
    'video games day': this.ts.translate('Bee::Template::Tag::video games day'),
    cyber: this.ts.translate('Bee::Template::Tag::cyber'),
    'video game day': this.ts.translate('Bee::Template::Tag::video game day'),
    gamer: this.ts.translate('Bee::Template::Tag::gamer'),
    'top 10': this.ts.translate('Bee::Template::Tag::top 10'),
    'video game': this.ts.translate('Bee::Template::Tag::video game'),
    brands: this.ts.translate('Bee::Template::Tag::brands'),
    glow: this.ts.translate('Bee::Template::Tag::glow'),
    marketer: this.ts.translate('Bee::Template::Tag::marketer'),
    'meet the team': this.ts.translate('Bee::Template::Tag::meet the team'),
    volunteer: this.ts.translate('Bee::Template::Tag::volunteer'),
    volunteering: this.ts.translate('Bee::Template::Tag::volunteering'),
    'fourth of july': this.ts.translate('Bee::Template::Tag::fourth of july'),
    traveling: this.ts.translate('Bee::Template::Tag::traveling'),
    coursera: this.ts.translate('Bee::Template::Tag::coursera'),
    study: this.ts.translate('Bee::Template::Tag::study'),
    books: this.ts.translate('Bee::Template::Tag::books'),
    courses: this.ts.translate('Bee::Template::Tag::courses'),
    enroll: this.ts.translate('Bee::Template::Tag::enroll'),
    udemy: this.ts.translate('Bee::Template::Tag::udemy'),
    'rent a car': this.ts.translate('Bee::Template::Tag::rent a car'),
    'car rental': this.ts.translate('Bee::Template::Tag::car rental'),
    fleet: this.ts.translate('Bee::Template::Tag::fleet'),
    cars: this.ts.translate('Bee::Template::Tag::cars'),
    'emoji world day': this.ts.translate('Bee::Template::Tag::emoji world day'),
    happy: this.ts.translate('Bee::Template::Tag::happy'),
    'emoji day': this.ts.translate('Bee::Template::Tag::emoji day'),
    adventure: this.ts.translate('Bee::Template::Tag::adventure'),
    '4th of july': this.ts.translate('Bee::Template::Tag::4th of july'),
    independence: this.ts.translate('Bee::Template::Tag::independence'),
    destinations: this.ts.translate('Bee::Template::Tag::destinations'),
    'thank you email': this.ts.translate('Bee::Template::Tag::thank you email'),
    'purchase confirmation': this.ts.translate('Bee::Template::Tag::purchase confirmation'),
    purchase: this.ts.translate('Bee::Template::Tag::purchase'),
    receipt: this.ts.translate('Bee::Template::Tag::receipt'),
    lesson: this.ts.translate('Bee::Template::Tag::lesson'),
    appointment: this.ts.translate('Bee::Template::Tag::appointment'),
    schedule: this.ts.translate('Bee::Template::Tag::schedule'),
    notes: this.ts.translate('Bee::Template::Tag::notes'),
    guitar: this.ts.translate('Bee::Template::Tag::guitar'),
    scheduling: this.ts.translate('Bee::Template::Tag::scheduling'),
    massage: this.ts.translate('Bee::Template::Tag::massage'),
    relax: this.ts.translate('Bee::Template::Tag::relax'),
    treatments: this.ts.translate('Bee::Template::Tag::treatments'),
    health: this.ts.translate('Bee::Template::Tag::health'),
    welness: this.ts.translate('Bee::Template::Tag::welness'),
    process: this.ts.translate('Bee::Template::Tag::process'),
    bright: this.ts.translate('Bee::Template::Tag::bright'),
    'back to school': this.ts.translate('Bee::Template::Tag::back to school'),
    stationery: this.ts.translate('Bee::Template::Tag::stationery'),
    supplies: this.ts.translate('Bee::Template::Tag::supplies'),
    bookstore: this.ts.translate('Bee::Template::Tag::bookstore'),
    reviews: this.ts.translate('Bee::Template::Tag::reviews'),
    bookshop: this.ts.translate('Bee::Template::Tag::bookshop'),
    reading: this.ts.translate('Bee::Template::Tag::reading'),
    'book lovers day': this.ts.translate('Bee::Template::Tag::book lovers day'),
    'e-book': this.ts.translate('Bee::Template::Tag::e-book'),
    'book store': this.ts.translate('Bee::Template::Tag::book store'),
    guide: this.ts.translate('Bee::Template::Tag::guide'),
    'special day': this.ts.translate('Bee::Template::Tag::special day'),
    room: this.ts.translate('Bee::Template::Tag::room'),
    trip: this.ts.translate('Bee::Template::Tag::trip'),
    privacy: this.ts.translate('Bee::Template::Tag::privacy'),
    terms: this.ts.translate('Bee::Template::Tag::terms'),
    'terms of service': this.ts.translate('Bee::Template::Tag::terms of service'),
    gdpr: this.ts.translate('Bee::Template::Tag::gdpr'),
    conditions: this.ts.translate('Bee::Template::Tag::conditions'),
    'on-line': this.ts.translate('Bee::Template::Tag::on-line'),
    virtual: this.ts.translate('Bee::Template::Tag::virtual'),
    airplane: this.ts.translate('Bee::Template::Tag::airplane'),
    'flight confirmation': this.ts.translate('Bee::Template::Tag::flight confirmation'),
    flight: this.ts.translate('Bee::Template::Tag::flight'),
    resubscribe: this.ts.translate('Bee::Template::Tag::resubscribe'),
    'opt in': this.ts.translate('Bee::Template::Tag::opt in'),
    subscribe: this.ts.translate('Bee::Template::Tag::subscribe'),
    others: this.ts.translate('Bee::Template::Tag::others'),
    agenda: this.ts.translate('Bee::Template::Tag::agenda'),
    month: this.ts.translate('Bee::Template::Tag::month'),
    'online business': this.ts.translate('Bee::Template::Tag::online business'),
    online: this.ts.translate('Bee::Template::Tag::online'),
    'business growth': this.ts.translate('Bee::Template::Tag::business growth'),
    airline: this.ts.translate('Bee::Template::Tag::airline'),
    'check-in': this.ts.translate('Bee::Template::Tag::check-in'),
    contest: this.ts.translate('Bee::Template::Tag::contest'),
    equality: this.ts.translate('Bee::Template::Tag::equality'),
    "women's equality day": this.ts.translate("Bee::Template::Tag::women's equality day"),
    "women's rights": this.ts.translate("Bee::Template::Tag::women's rights"),
    giveaway: this.ts.translate('Bee::Template::Tag::giveaway'),
    'special event': this.ts.translate('Bee::Template::Tag::special event'),
    audiophiles: this.ts.translate('Bee::Template::Tag::audiophiles'),
    'music app': this.ts.translate('Bee::Template::Tag::music app'),
    'soft light': this.ts.translate('Bee::Template::Tag::soft light'),
    'app promotion': this.ts.translate('Bee::Template::Tag::app promotion'),
    'app launch': this.ts.translate('Bee::Template::Tag::app launch'),
    neon: this.ts.translate('Bee::Template::Tag::neon'),
    labor: this.ts.translate('Bee::Template::Tag::labor'),
    labour: this.ts.translate('Bee::Template::Tag::labour'),
    'labor day': this.ts.translate('Bee::Template::Tag::labor day'),
    'new app': this.ts.translate('Bee::Template::Tag::new app'),
    'app store': this.ts.translate('Bee::Template::Tag::app store'),
    tablet: this.ts.translate('Bee::Template::Tag::tablet'),
    'e-learning': this.ts.translate('Bee::Template::Tag::e-learning'),
    'online course': this.ts.translate('Bee::Template::Tag::online course'),
    'pricing table': this.ts.translate('Bee::Template::Tag::pricing table'),
    discounts: this.ts.translate('Bee::Template::Tag::discounts'),
    instruments: this.ts.translate('Bee::Template::Tag::instruments'),
    'labour day': this.ts.translate('Bee::Template::Tag::labour day'),
    offers: this.ts.translate('Bee::Template::Tag::offers'),
    post: this.ts.translate('Bee::Template::Tag::post'),
    article: this.ts.translate('Bee::Template::Tag::article'),
    'women for women': this.ts.translate('Bee::Template::Tag::women for women'),
    'women equality day': this.ts.translate('Bee::Template::Tag::women equality day'),
    fundraisers: this.ts.translate('Bee::Template::Tag::fundraisers'),
    'women rights': this.ts.translate('Bee::Template::Tag::women rights'),
    listing: this.ts.translate('Bee::Template::Tag::listing'),
    hire: this.ts.translate('Bee::Template::Tag::hire'),
    position: this.ts.translate('Bee::Template::Tag::position'),
    opportunity: this.ts.translate('Bee::Template::Tag::opportunity'),
    opportunities: this.ts.translate('Bee::Template::Tag::opportunities'),
    'job posting': this.ts.translate('Bee::Template::Tag::job posting'),
    hiring: this.ts.translate('Bee::Template::Tag::hiring'),
    account: this.ts.translate('Bee::Template::Tag::account'),
    club: this.ts.translate('Bee::Template::Tag::club'),
    sound: this.ts.translate('Bee::Template::Tag::sound'),
    membership: this.ts.translate('Bee::Template::Tag::membership'),
    savings: this.ts.translate('Bee::Template::Tag::savings'),
    'seasonal offer': this.ts.translate('Bee::Template::Tag::seasonal offer'),
    girls: this.ts.translate('Bee::Template::Tag::girls'),
    'event related promotion': this.ts.translate('Bee::Template::Tag::event related promotion'),
    'promo code': this.ts.translate('Bee::Template::Tag::promo code'),
    Christmas: this.ts.translate('Bee::Template::Tag::Christmas'),
    'womens equality day': this.ts.translate('Bee::Template::Tag::womens equality day'),
    'skin products': this.ts.translate('Bee::Template::Tag::skin products'),
    furniture: this.ts.translate('Bee::Template::Tag::furniture'),
    'new collection': this.ts.translate('Bee::Template::Tag::new collection'),
    gradients: this.ts.translate('Bee::Template::Tag::gradients'),
    fall: this.ts.translate('Bee::Template::Tag::fall'),
    'home decor': this.ts.translate('Bee::Template::Tag::home decor'),
    'fall colours': this.ts.translate('Bee::Template::Tag::fall colours'),
    warm: this.ts.translate('Bee::Template::Tag::warm'),
    'pet toy': this.ts.translate('Bee::Template::Tag::pet toy'),
    treats: this.ts.translate('Bee::Template::Tag::treats'),
    emergency: this.ts.translate('Bee::Template::Tag::emergency'),
    'local business': this.ts.translate('Bee::Template::Tag::local business'),
    local: this.ts.translate('Bee::Template::Tag::local'),
    freebie: this.ts.translate('Bee::Template::Tag::freebie'),
    diy: this.ts.translate('Bee::Template::Tag::diy'),
    stayinghome: this.ts.translate('Bee::Template::Tag::stayinghome'),
    classes: this.ts.translate('Bee::Template::Tag::classes'),
    hobbies: this.ts.translate('Bee::Template::Tag::hobbies'),
    crafts: this.ts.translate('Bee::Template::Tag::crafts'),
    interests: this.ts.translate('Bee::Template::Tag::interests'),
    baking: this.ts.translate('Bee::Template::Tag::baking'),
    'app features': this.ts.translate('Bee::Template::Tag::app features'),
    glasses: this.ts.translate('Bee::Template::Tag::glasses'),
    'collection launch': this.ts.translate('Bee::Template::Tag::collection launch'),
    'coming soon': this.ts.translate('Bee::Template::Tag::coming soon'),
    eyewear: this.ts.translate('Bee::Template::Tag::eyewear'),
    decor: this.ts.translate('Bee::Template::Tag::decor'),
    brand: this.ts.translate('Bee::Template::Tag::brand'),
    models: this.ts.translate('Bee::Template::Tag::models'),
    'fall collection': this.ts.translate('Bee::Template::Tag::fall collection'),
    menswear: this.ts.translate('Bee::Template::Tag::menswear'),
    'product list': this.ts.translate('Bee::Template::Tag::product list'),
    radio: this.ts.translate('Bee::Template::Tag::radio'),
    'on air': this.ts.translate('Bee::Template::Tag::on air'),
    'international podcast day': this.ts.translate('Bee::Template::Tag::international podcast day'),
    entertainment: this.ts.translate('Bee::Template::Tag::entertainment'),
    recruitment: this.ts.translate('Bee::Template::Tag::recruitment'),
    'hiring software': this.ts.translate('Bee::Template::Tag::hiring software'),
    talent: this.ts.translate('Bee::Template::Tag::talent'),
    mindfulness: this.ts.translate('Bee::Template::Tag::mindfulness'),
    calm: this.ts.translate('Bee::Template::Tag::calm'),
    'fall traveling': this.ts.translate('Bee::Template::Tag::fall traveling'),
    vacation: this.ts.translate('Bee::Template::Tag::vacation'),
    'fall season travels': this.ts.translate('Bee::Template::Tag::fall season travels'),
    'food delivery': this.ts.translate('Bee::Template::Tag::food delivery'),
    scary: this.ts.translate('Bee::Template::Tag::scary'),
    horror: this.ts.translate('Bee::Template::Tag::horror'),
    'game sale': this.ts.translate('Bee::Template::Tag::game sale'),
    groceries: this.ts.translate('Bee::Template::Tag::groceries'),
    ingredients: this.ts.translate('Bee::Template::Tag::ingredients'),
    'grocery store': this.ts.translate('Bee::Template::Tag::grocery store'),
    goodness: this.ts.translate('Bee::Template::Tag::goodness'),
    'helping out': this.ts.translate('Bee::Template::Tag::helping out'),
    'health and wellness': this.ts.translate('Bee::Template::Tag::health and wellness'),
    'personal care': this.ts.translate('Bee::Template::Tag::personal care'),
    natural: this.ts.translate('Bee::Template::Tag::natural'),
    'beauty care': this.ts.translate('Bee::Template::Tag::beauty care'),
    'skin care': this.ts.translate('Bee::Template::Tag::skin care'),
    'holiday season': this.ts.translate('Bee::Template::Tag::holiday season'),
    'line launch': this.ts.translate('Bee::Template::Tag::line launch'),
    soap: this.ts.translate('Bee::Template::Tag::soap'),
    'product line': this.ts.translate('Bee::Template::Tag::product line'),
    'new range': this.ts.translate('Bee::Template::Tag::new range'),
    'protein powders': this.ts.translate('Bee::Template::Tag::protein powders'),
    cleanser: this.ts.translate('Bee::Template::Tag::cleanser'),
    organization: this.ts.translate('Bee::Template::Tag::organization'),
    storytelling: this.ts.translate('Bee::Template::Tag::storytelling'),
    'non-profit': this.ts.translate('Bee::Template::Tag::non-profit'),
    awareness: this.ts.translate('Bee::Template::Tag::awareness'),
    help: this.ts.translate('Bee::Template::Tag::help'),
    animals: this.ts.translate('Bee::Template::Tag::animals'),
    'food magazine': this.ts.translate('Bee::Template::Tag::food magazine'),
    orders: this.ts.translate('Bee::Template::Tag::orders'),
    dentist: this.ts.translate('Bee::Template::Tag::dentist'),
    'order confirmation': this.ts.translate('Bee::Template::Tag::order confirmation'),
    bakery: this.ts.translate('Bee::Template::Tag::bakery'),
    'customer service': this.ts.translate('Bee::Template::Tag::customer service'),
    'lifestyle brand': this.ts.translate('Bee::Template::Tag::lifestyle brand'),
    'customer satisfaction': this.ts.translate('Bee::Template::Tag::customer satisfaction'),
    zombie: this.ts.translate('Bee::Template::Tag::zombie'),
    monster: this.ts.translate('Bee::Template::Tag::monster'),
    'online event': this.ts.translate('Bee::Template::Tag::online event'),
    'virtual party': this.ts.translate('Bee::Template::Tag::virtual party'),
    'virtual event': this.ts.translate('Bee::Template::Tag::virtual event'),
    gardeners: this.ts.translate('Bee::Template::Tag::gardeners'),
    'small business saturday': this.ts.translate('Bee::Template::Tag::small business saturday'),
    garden: this.ts.translate('Bee::Template::Tag::garden'),
    plants: this.ts.translate('Bee::Template::Tag::plants'),
    'online store': this.ts.translate('Bee::Template::Tag::online store'),
    cakes: this.ts.translate('Bee::Template::Tag::cakes'),
    outlet: this.ts.translate('Bee::Template::Tag::outlet'),
    kindness: this.ts.translate('Bee::Template::Tag::kindness'),
    drone: this.ts.translate('Bee::Template::Tag::drone'),
    'fall offers': this.ts.translate('Bee::Template::Tag::fall offers'),
    'new features': this.ts.translate('Bee::Template::Tag::new features'),
    fps: this.ts.translate('Bee::Template::Tag::fps'),
    pvp: this.ts.translate('Bee::Template::Tag::pvp'),
    renew: this.ts.translate('Bee::Template::Tag::renew'),
    'special week': this.ts.translate('Bee::Template::Tag::special week'),
    'world kindness week': this.ts.translate('Bee::Template::Tag::world kindness week'),
    phone: this.ts.translate('Bee::Template::Tag::phone'),
    'computer parts': this.ts.translate('Bee::Template::Tag::computer parts'),
    pc: this.ts.translate('Bee::Template::Tag::pc'),
    monday: this.ts.translate('Bee::Template::Tag::monday'),
    gear: this.ts.translate('Bee::Template::Tag::gear'),
    'automatic renewal': this.ts.translate('Bee::Template::Tag::automatic renewal'),
    deactivation: this.ts.translate('Bee::Template::Tag::deactivation'),
    renewal: this.ts.translate('Bee::Template::Tag::renewal'),
    desert: this.ts.translate('Bee::Template::Tag::desert'),
    landscape: this.ts.translate('Bee::Template::Tag::landscape'),
    wilderness: this.ts.translate('Bee::Template::Tag::wilderness'),
    mint: this.ts.translate('Bee::Template::Tag::mint'),
    box: this.ts.translate('Bee::Template::Tag::box'),
    'gym supplements': this.ts.translate('Bee::Template::Tag::gym supplements'),
    workout: this.ts.translate('Bee::Template::Tag::workout'),
    'gym gear': this.ts.translate('Bee::Template::Tag::gym gear'),
    'gym classes': this.ts.translate('Bee::Template::Tag::gym classes'),
    spin: this.ts.translate('Bee::Template::Tag::spin'),
    'boxing gloves': this.ts.translate('Bee::Template::Tag::boxing gloves'),
    bicycles: this.ts.translate('Bee::Template::Tag::bicycles'),
    bikestore: this.ts.translate('Bee::Template::Tag::bikestore'),
    'neon sign': this.ts.translate('Bee::Template::Tag::neon sign'),
    lens: this.ts.translate('Bee::Template::Tag::lens'),
    graffiti: this.ts.translate('Bee::Template::Tag::graffiti'),
    'street style': this.ts.translate('Bee::Template::Tag::street style'),
    skateboard: this.ts.translate('Bee::Template::Tag::skateboard'),
    celebratory: this.ts.translate('Bee::Template::Tag::celebratory'),
    toystore: this.ts.translate('Bee::Template::Tag::toystore'),
    christamas: this.ts.translate('Bee::Template::Tag::christamas'),
    'gingerbread house': this.ts.translate('Bee::Template::Tag::gingerbread house'),
    snowflakes: this.ts.translate('Bee::Template::Tag::snowflakes'),
    cupcakes: this.ts.translate('Bee::Template::Tag::cupcakes'),
    dinner: this.ts.translate('Bee::Template::Tag::dinner'),
    truck: this.ts.translate('Bee::Template::Tag::truck'),
    santa: this.ts.translate('Bee::Template::Tag::santa'),
    irish: this.ts.translate('Bee::Template::Tag::irish'),
    'art show': this.ts.translate('Bee::Template::Tag::art show'),
    covidsafe: this.ts.translate('Bee::Template::Tag::covidsafe'),
    virtualevent: this.ts.translate('Bee::Template::Tag::virtualevent'),
    concert: this.ts.translate('Bee::Template::Tag::concert'),
    'art event': this.ts.translate('Bee::Template::Tag::art event'),
    "new year's eve": this.ts.translate("Bee::Template::Tag::new year's eve"),
    bands: this.ts.translate('Bee::Template::Tag::bands'),
    show: this.ts.translate('Bee::Template::Tag::show'),
    '2021': this.ts.translate('Bee::Template::Tag::2021'),
    'giving tuesday': this.ts.translate('Bee::Template::Tag::giving tuesday'),
    generocity: this.ts.translate('Bee::Template::Tag::generocity'),
    movement: this.ts.translate('Bee::Template::Tag::movement'),
    france: this.ts.translate('Bee::Template::Tag::france'),
    art: this.ts.translate('Bee::Template::Tag::art'),
    louvre: this.ts.translate('Bee::Template::Tag::louvre'),
    museum: this.ts.translate('Bee::Template::Tag::museum'),
    'new year eve': this.ts.translate('Bee::Template::Tag::new year eve'),
    'kid clothing': this.ts.translate('Bee::Template::Tag::kid clothing'),
    tree: this.ts.translate('Bee::Template::Tag::tree'),
    registation: this.ts.translate('Bee::Template::Tag::registation'),
    'social platform': this.ts.translate('Bee::Template::Tag::social platform'),
    'annual review': this.ts.translate('Bee::Template::Tag::annual review'),
    'social media': this.ts.translate('Bee::Template::Tag::social media'),
    'mobile app': this.ts.translate('Bee::Template::Tag::mobile app'),
    'new years': this.ts.translate('Bee::Template::Tag::new years'),
    trends: this.ts.translate('Bee::Template::Tag::trends'),
    'a year in review': this.ts.translate('Bee::Template::Tag::a year in review'),
    wrap: this.ts.translate('Bee::Template::Tag::wrap'),
    'know your customers': this.ts.translate('Bee::Template::Tag::know your customers'),
    quiz: this.ts.translate('Bee::Template::Tag::quiz'),
    discovery: this.ts.translate('Bee::Template::Tag::discovery'),
    gardening: this.ts.translate('Bee::Template::Tag::gardening'),
    space: this.ts.translate('Bee::Template::Tag::space'),
    'tv shows': this.ts.translate('Bee::Template::Tag::tv shows'),
    movies: this.ts.translate('Bee::Template::Tag::movies'),
    streaming: this.ts.translate('Bee::Template::Tag::streaming'),
    films: this.ts.translate('Bee::Template::Tag::films'),
    scifi: this.ts.translate('Bee::Template::Tag::scifi'),
    'streaming service': this.ts.translate('Bee::Template::Tag::streaming service'),
    tv: this.ts.translate('Bee::Template::Tag::tv'),
    'user experience': this.ts.translate('Bee::Template::Tag::user experience'),
    promoting: this.ts.translate('Bee::Template::Tag::promoting'),
    'teaser campaign': this.ts.translate('Bee::Template::Tag::teaser campaign'),
    'tips and tricks': this.ts.translate('Bee::Template::Tag::tips and tricks'),
    grid: this.ts.translate('Bee::Template::Tag::grid'),
    typeface: this.ts.translate('Bee::Template::Tag::typeface'),
    tips: this.ts.translate('Bee::Template::Tag::tips'),
    achitecture: this.ts.translate('Bee::Template::Tag::achitecture'),
    computer: this.ts.translate('Bee::Template::Tag::computer'),
    'home services': this.ts.translate('Bee::Template::Tag::home services'),
    rate: this.ts.translate('Bee::Template::Tag::rate'),
    rating: this.ts.translate('Bee::Template::Tag::rating'),
    exclusive: this.ts.translate('Bee::Template::Tag::exclusive'),
    announcement: this.ts.translate('Bee::Template::Tag::announcement'),
    'new products': this.ts.translate('Bee::Template::Tag::new products'),
    'text app': this.ts.translate('Bee::Template::Tag::text app'),
    'annual plan': this.ts.translate('Bee::Template::Tag::annual plan'),
    'app subscription': this.ts.translate('Bee::Template::Tag::app subscription'),
    font: this.ts.translate('Bee::Template::Tag::font'),
    stylish: this.ts.translate('Bee::Template::Tag::stylish'),
    tattoo: this.ts.translate('Bee::Template::Tag::tattoo'),
    insights: this.ts.translate('Bee::Template::Tag::insights'),
    'weekly report': this.ts.translate('Bee::Template::Tag::weekly report'),
    yoga: this.ts.translate('Bee::Template::Tag::yoga'),
    mind: this.ts.translate('Bee::Template::Tag::mind'),
    'fitness tracker': this.ts.translate('Bee::Template::Tag::fitness tracker'),
    statistics: this.ts.translate('Bee::Template::Tag::statistics'),
    gamification: this.ts.translate('Bee::Template::Tag::gamification'),
    wellbeing: this.ts.translate('Bee::Template::Tag::wellbeing'),
    'weekly stats': this.ts.translate('Bee::Template::Tag::weekly stats'),
    salads: this.ts.translate('Bee::Template::Tag::salads'),
    smallbusiness: this.ts.translate('Bee::Template::Tag::smallbusiness'),
    homedelivery: this.ts.translate('Bee::Template::Tag::homedelivery'),
    '12 days': this.ts.translate('Bee::Template::Tag::12 days'),
    valentinesday: this.ts.translate('Bee::Template::Tag::valentinesday'),
    loveday: this.ts.translate('Bee::Template::Tag::loveday'),
    '2x1sale': this.ts.translate('Bee::Template::Tag::2x1sale'),
    'year of the ox': this.ts.translate('Bee::Template::Tag::year of the ox'),
    'free delivery': this.ts.translate('Bee::Template::Tag::free delivery'),
    'lunar new year': this.ts.translate('Bee::Template::Tag::lunar new year'),
    history: this.ts.translate('Bee::Template::Tag::history'),
    'black culture': this.ts.translate('Bee::Template::Tag::black culture'),
    'arts and culture': this.ts.translate('Bee::Template::Tag::arts and culture'),
    artist: this.ts.translate('Bee::Template::Tag::artist'),
    'black history month': this.ts.translate('Bee::Template::Tag::black history month'),
    athletes: this.ts.translate('Bee::Template::Tag::athletes'),
    gradient: this.ts.translate('Bee::Template::Tag::gradient'),
    'lunar year': this.ts.translate('Bee::Template::Tag::lunar year'),
    graphic: this.ts.translate('Bee::Template::Tag::graphic'),
    'digital art': this.ts.translate('Bee::Template::Tag::digital art'),
    exercise: this.ts.translate('Bee::Template::Tag::exercise'),
    'african american': this.ts.translate('Bee::Template::Tag::african american'),
    'yellow and grey': this.ts.translate('Bee::Template::Tag::yellow and grey'),
    inspiration: this.ts.translate('Bee::Template::Tag::inspiration'),
    outdoors: this.ts.translate('Bee::Template::Tag::outdoors'),
    'pantone color of the year': this.ts.translate('Bee::Template::Tag::pantone color of the year'),
    'on-line classes': this.ts.translate('Bee::Template::Tag::on-line classes'),
    'yoga studio': this.ts.translate('Bee::Template::Tag::yoga studio'),
    'pantone colours of the year': this.ts.translate('Bee::Template::Tag::pantone colours of the year'),
    urban: this.ts.translate('Bee::Template::Tag::urban'),
    'flower shop': this.ts.translate('Bee::Template::Tag::flower shop'),
    announcment: this.ts.translate('Bee::Template::Tag::announcment'),
    paris: this.ts.translate('Bee::Template::Tag::paris'),
    'paris fashion week': this.ts.translate('Bee::Template::Tag::paris fashion week'),
    onlineevent: this.ts.translate('Bee::Template::Tag::onlineevent'),
    'face products': this.ts.translate('Bee::Template::Tag::face products'),
    'make up': this.ts.translate('Bee::Template::Tag::make up'),
    'colour of the year': this.ts.translate('Bee::Template::Tag::colour of the year'),
    illuminating: this.ts.translate('Bee::Template::Tag::illuminating'),
    'ultimate gray': this.ts.translate('Bee::Template::Tag::ultimate gray'),
    'take away': this.ts.translate('Bee::Template::Tag::take away'),
    'food menu': this.ts.translate('Bee::Template::Tag::food menu'),
    'burger house': this.ts.translate('Bee::Template::Tag::burger house'),
    'app offer': this.ts.translate('Bee::Template::Tag::app offer'),
    culture: this.ts.translate('Bee::Template::Tag::culture'),
    'kids activities': this.ts.translate('Bee::Template::Tag::kids activities'),
    'maori art': this.ts.translate('Bee::Template::Tag::maori art'),
    'waitangi day': this.ts.translate('Bee::Template::Tag::waitangi day'),
    'new zealand day': this.ts.translate('Bee::Template::Tag::new zealand day'),
    'arts and crafts': this.ts.translate('Bee::Template::Tag::arts and crafts'),
    'art centre': this.ts.translate('Bee::Template::Tag::art centre'),
    steps: this.ts.translate('Bee::Template::Tag::steps'),
    meditiation: this.ts.translate('Bee::Template::Tag::meditiation'),
    'new zealand': this.ts.translate('Bee::Template::Tag::new zealand'),
    "st.patrick's day": this.ts.translate("Bee::Template::Tag::st.patrick's day"),
    December: this.ts.translate('Bee::Template::Tag::December'),
    "st. patrick's day": this.ts.translate("Bee::Template::Tag::st. patrick's day"),
    agriculture: this.ts.translate('Bee::Template::Tag::agriculture'),
    'empower women': this.ts.translate('Bee::Template::Tag::empower women'),
    florist: this.ts.translate('Bee::Template::Tag::florist'),
    spotlight: this.ts.translate('Bee::Template::Tag::spotlight'),
    horticulture: this.ts.translate('Bee::Template::Tag::horticulture'),
    '8th march': this.ts.translate('Bee::Template::Tag::8th march'),
    'women business owners': this.ts.translate('Bee::Template::Tag::women business owners'),
    singlesday: this.ts.translate('Bee::Template::Tag::singlesday'),
    singles: this.ts.translate('Bee::Template::Tag::singles'),
    'green initiatives': this.ts.translate('Bee::Template::Tag::green initiatives'),
    'climate solutions': this.ts.translate('Bee::Template::Tag::climate solutions'),
    'earth month': this.ts.translate('Bee::Template::Tag::earth month'),
    april: this.ts.translate('Bee::Template::Tag::april'),
    'egg decorating': this.ts.translate('Bee::Template::Tag::egg decorating'),
    'media and entertainment': this.ts.translate('Bee::Template::Tag::media and entertainment'),
    'wedding cake': this.ts.translate('Bee::Template::Tag::wedding cake'),
    'st. patricks day': this.ts.translate('Bee::Template::Tag::st. patricks day'),
    lucky: this.ts.translate('Bee::Template::Tag::lucky'),
    'wedding planner': this.ts.translate('Bee::Template::Tag::wedding planner'),
    'international womens day': this.ts.translate('Bee::Template::Tag::international womens day'),
    iwd: this.ts.translate('Bee::Template::Tag::iwd'),
    'behind every woman': this.ts.translate('Bee::Template::Tag::behind every woman'),
    'genders equality': this.ts.translate('Bee::Template::Tag::genders equality'),
    'ice cream': this.ts.translate('Bee::Template::Tag::ice cream'),
    'spring break': this.ts.translate('Bee::Template::Tag::spring break'),
    'video games': this.ts.translate('Bee::Template::Tag::video games'),
    'engagement party': this.ts.translate('Bee::Template::Tag::engagement party'),
    "april fool's day": this.ts.translate("Bee::Template::Tag::april fool's day"),
    donut: this.ts.translate('Bee::Template::Tag::donut'),
    'holi festival': this.ts.translate('Bee::Template::Tag::holi festival'),
    applaunch: this.ts.translate('Bee::Template::Tag::applaunch'),
    earthhour: this.ts.translate('Bee::Template::Tag::earthhour'),
    holi: this.ts.translate('Bee::Template::Tag::holi'),
    'world water day': this.ts.translate('Bee::Template::Tag::world water day'),
    water: this.ts.translate('Bee::Template::Tag::water'),
    colours: this.ts.translate('Bee::Template::Tag::colours'),
    maslenitsa: this.ts.translate('Bee::Template::Tag::maslenitsa'),
    shakespeare: this.ts.translate('Bee::Template::Tag::shakespeare'),
    'wedding vendors': this.ts.translate('Bee::Template::Tag::wedding vendors'),
    'how-to': this.ts.translate('Bee::Template::Tag::how-to'),
    'family fun': this.ts.translate('Bee::Template::Tag::family fun'),
    "fool's day": this.ts.translate("Bee::Template::Tag::fool's day"),
    'world, water, nature, earth': this.ts.translate('Bee::Template::Tag::world, water, nature, earth'),
    'food blog': this.ts.translate('Bee::Template::Tag::food blog'),
    pasta: this.ts.translate('Bee::Template::Tag::pasta'),
    'national carbonara day': this.ts.translate('Bee::Template::Tag::national carbonara day'),
    'fine art': this.ts.translate('Bee::Template::Tag::fine art'),
    'art and design': this.ts.translate('Bee::Template::Tag::art and design'),
    'international celebrations': this.ts.translate('Bee::Template::Tag::international celebrations'),
    artists: this.ts.translate('Bee::Template::Tag::artists'),
    conservation: this.ts.translate('Bee::Template::Tag::conservation'),
    cause: this.ts.translate('Bee::Template::Tag::cause'),
    'spread the word': this.ts.translate('Bee::Template::Tag::spread the word'),
    activism: this.ts.translate('Bee::Template::Tag::activism'),
    'save the date': this.ts.translate('Bee::Template::Tag::save the date'),
    reset: this.ts.translate('Bee::Template::Tag::reset'),
    'forgot password': this.ts.translate('Bee::Template::Tag::forgot password'),
    other: this.ts.translate('Bee::Template::Tag::other'),
    'easter egg hunt': this.ts.translate('Bee::Template::Tag::easter egg hunt'),
    'women clothing': this.ts.translate('Bee::Template::Tag::women clothing'),
    free: this.ts.translate('Bee::Template::Tag::free'),
    sdg: this.ts.translate('Bee::Template::Tag::sdg'),
    'festival of action': this.ts.translate('Bee::Template::Tag::festival of action'),
    cupon: this.ts.translate('Bee::Template::Tag::cupon'),
    Wedding: this.ts.translate('Bee::Template::Tag::Wedding'),
    "fools' day": this.ts.translate("Bee::Template::Tag::fools' day"),
    'raising money': this.ts.translate('Bee::Template::Tag::raising money'),
    sdgs: this.ts.translate('Bee::Template::Tag::sdgs'),
    'sustainable development goals': this.ts.translate('Bee::Template::Tag::sustainable development goals'),
    'sdg global festival of action': this.ts.translate('Bee::Template::Tag::sdg global festival of action'),
    'raising awarness': this.ts.translate('Bee::Template::Tag::raising awarness'),
    pollution: this.ts.translate('Bee::Template::Tag::pollution'),
    'global warming': this.ts.translate('Bee::Template::Tag::global warming'),
    breakfrast: this.ts.translate('Bee::Template::Tag::breakfrast'),
    'waffle day': this.ts.translate('Bee::Template::Tag::waffle day'),
    bunnies: this.ts.translate('Bee::Template::Tag::bunnies'),
    hunt: this.ts.translate('Bee::Template::Tag::hunt'),
    classroom: this.ts.translate('Bee::Template::Tag::classroom'),
    pandemic: this.ts.translate('Bee::Template::Tag::pandemic'),
    teaching: this.ts.translate('Bee::Template::Tag::teaching'),
    class: this.ts.translate('Bee::Template::Tag::class'),
    educational: this.ts.translate('Bee::Template::Tag::educational'),
    'pastel colours': this.ts.translate('Bee::Template::Tag::pastel colours'),
    'bath bombs': this.ts.translate('Bee::Template::Tag::bath bombs'),
    'tulp festival': this.ts.translate('Bee::Template::Tag::tulp festival'),
    tulip: this.ts.translate('Bee::Template::Tag::tulip'),
    'media & entertainment': this.ts.translate('Bee::Template::Tag::media & entertainment'),
    goals: this.ts.translate('Bee::Template::Tag::goals'),
    'sdg global festival': this.ts.translate('Bee::Template::Tag::sdg global festival'),
    sustainable: this.ts.translate('Bee::Template::Tag::sustainable'),
    world: this.ts.translate('Bee::Template::Tag::world'),
    'wedding venue': this.ts.translate('Bee::Template::Tag::wedding venue'),
    transformation: this.ts.translate('Bee::Template::Tag::transformation'),
    arts: this.ts.translate('Bee::Template::Tag::arts'),
    ebook: this.ts.translate('Bee::Template::Tag::ebook'),
    publishing: this.ts.translate('Bee::Template::Tag::publishing'),
    publisher: this.ts.translate('Bee::Template::Tag::publisher'),
    published: this.ts.translate('Bee::Template::Tag::published'),
    new: this.ts.translate('Bee::Template::Tag::new'),
    literature: this.ts.translate('Bee::Template::Tag::literature'),
    publish: this.ts.translate('Bee::Template::Tag::publish'),
    'food recipe': this.ts.translate('Bee::Template::Tag::food recipe'),
    waffles: this.ts.translate('Bee::Template::Tag::waffles'),
    'graduation announcement': this.ts.translate('Bee::Template::Tag::graduation announcement'),
    college: this.ts.translate('Bee::Template::Tag::college'),
    'high school': this.ts.translate('Bee::Template::Tag::high school'),
    graduation: this.ts.translate('Bee::Template::Tag::graduation'),
    highschool: this.ts.translate('Bee::Template::Tag::highschool'),
    'middle school': this.ts.translate('Bee::Template::Tag::middle school'),
    schools: this.ts.translate('Bee::Template::Tag::schools'),
    elementary: this.ts.translate('Bee::Template::Tag::elementary'),
    'carbonara day': this.ts.translate('Bee::Template::Tag::carbonara day'),
    italian: this.ts.translate('Bee::Template::Tag::italian'),
    'order online': this.ts.translate('Bee::Template::Tag::order online'),
    spaghetti: this.ts.translate('Bee::Template::Tag::spaghetti'),
    ecommerse: this.ts.translate('Bee::Template::Tag::ecommerse'),
    'tulip festival': this.ts.translate('Bee::Template::Tag::tulip festival'),
    skincare: this.ts.translate('Bee::Template::Tag::skincare'),
    achievement: this.ts.translate('Bee::Template::Tag::achievement'),
    student: this.ts.translate('Bee::Template::Tag::student'),
    cincodemayo: this.ts.translate('Bee::Template::Tag::cincodemayo'),
    fruit: this.ts.translate('Bee::Template::Tag::fruit'),
    "90's": this.ts.translate("Bee::Template::Tag::90's"),
    develope: this.ts.translate('Bee::Template::Tag::develope'),
    program: this.ts.translate('Bee::Template::Tag::program'),
    lofi: this.ts.translate('Bee::Template::Tag::lofi'),
    'event planners': this.ts.translate('Bee::Template::Tag::event planners'),
    'fools day': this.ts.translate('Bee::Template::Tag::fools day'),
    happiness: this.ts.translate('Bee::Template::Tag::happiness'),
    laugh: this.ts.translate('Bee::Template::Tag::laugh'),
    first: this.ts.translate('Bee::Template::Tag::first'),
    joke: this.ts.translate('Bee::Template::Tag::joke'),
    smile: this.ts.translate('Bee::Template::Tag::smile'),
    'anti-aging': this.ts.translate('Bee::Template::Tag::anti-aging'),
    nonstop: this.ts.translate('Bee::Template::Tag::nonstop'),
    schedules: this.ts.translate('Bee::Template::Tag::schedules'),
    coding: this.ts.translate('Bee::Template::Tag::coding'),
    hackathon: this.ts.translate('Bee::Template::Tag::hackathon'),
    carbonara: this.ts.translate('Bee::Template::Tag::carbonara'),
    'memorial day': this.ts.translate('Bee::Template::Tag::memorial day'),
    veterans: this.ts.translate('Bee::Template::Tag::veterans'),
    publication: this.ts.translate('Bee::Template::Tag::publication'),
    stories: this.ts.translate('Bee::Template::Tag::stories'),
    'special edition': this.ts.translate('Bee::Template::Tag::special edition'),
    'color block': this.ts.translate('Bee::Template::Tag::color block'),
    freedom: this.ts.translate('Bee::Template::Tag::freedom'),
    blogs: this.ts.translate('Bee::Template::Tag::blogs'),
    'decoration day': this.ts.translate('Bee::Template::Tag::decoration day'),
    lifestyle: this.ts.translate('Bee::Template::Tag::lifestyle'),
    america: this.ts.translate('Bee::Template::Tag::america'),
    manufacturing: this.ts.translate('Bee::Template::Tag::manufacturing'),
    developing: this.ts.translate('Bee::Template::Tag::developing'),
    '3d': this.ts.translate('Bee::Template::Tag::3d'),
    'mexican dish': this.ts.translate('Bee::Template::Tag::mexican dish'),
    theatre: this.ts.translate('Bee::Template::Tag::theatre'),
    'online show': this.ts.translate('Bee::Template::Tag::online show'),
    'mother’s day': this.ts.translate('Bee::Template::Tag::mother’s day'),
    stopthehate: this.ts.translate('Bee::Template::Tag::stopthehate'),
    'civil war': this.ts.translate('Bee::Template::Tag::civil war'),
    'stop the hate': this.ts.translate('Bee::Template::Tag::stop the hate'),
    'real people': this.ts.translate('Bee::Template::Tag::real people'),
    biography: this.ts.translate('Bee::Template::Tag::biography'),
    "don't forget": this.ts.translate("Bee::Template::Tag::don't forget"),
    'about my story': this.ts.translate('Bee::Template::Tag::about my story'),
    memory: this.ts.translate('Bee::Template::Tag::memory'),
    poppy: this.ts.translate('Bee::Template::Tag::poppy'),
    'about your story': this.ts.translate('Bee::Template::Tag::about your story'),
    solidarity: this.ts.translate('Bee::Template::Tag::solidarity'),
    'asian hate': this.ts.translate('Bee::Template::Tag::asian hate'),
    racism: this.ts.translate('Bee::Template::Tag::racism'),
    'news website': this.ts.translate('Bee::Template::Tag::news website'),
    united: this.ts.translate('Bee::Template::Tag::united'),
    gig: this.ts.translate('Bee::Template::Tag::gig'),
    'music campaign': this.ts.translate('Bee::Template::Tag::music campaign'),
    darkmode: this.ts.translate('Bee::Template::Tag::darkmode'),
    band: this.ts.translate('Bee::Template::Tag::band'),
    'order details': this.ts.translate('Bee::Template::Tag::order details'),
    'ticket sale': this.ts.translate('Bee::Template::Tag::ticket sale'),
    'new album': this.ts.translate('Bee::Template::Tag::new album'),
    protest: this.ts.translate('Bee::Template::Tag::protest'),
    'stop asian hate': this.ts.translate('Bee::Template::Tag::stop asian hate'),
    'no recism': this.ts.translate('Bee::Template::Tag::no recism'),
    justice: this.ts.translate('Bee::Template::Tag::justice'),
    aapi: this.ts.translate('Bee::Template::Tag::aapi'),
    socialwork: this.ts.translate('Bee::Template::Tag::socialwork'),
    strength: this.ts.translate('Bee::Template::Tag::strength'),
    voice: this.ts.translate('Bee::Template::Tag::voice'),
    advocate: this.ts.translate('Bee::Template::Tag::advocate'),
    conversation: this.ts.translate('Bee::Template::Tag::conversation'),
    hispanic: this.ts.translate('Bee::Template::Tag::hispanic'),
    stopasianhate: this.ts.translate('Bee::Template::Tag::stopasianhate'),
    stophate: this.ts.translate('Bee::Template::Tag::stophate'),
    advocacy: this.ts.translate('Bee::Template::Tag::advocacy'),
    marches: this.ts.translate('Bee::Template::Tag::marches'),
    blm: this.ts.translate('Bee::Template::Tag::blm'),
    apology: this.ts.translate('Bee::Template::Tag::apology'),
    'world meditation day': this.ts.translate('Bee::Template::Tag::world meditation day'),
    'mobile application': this.ts.translate('Bee::Template::Tag::mobile application'),
    apps: this.ts.translate('Bee::Template::Tag::apps'),
    'anit-racism': this.ts.translate('Bee::Template::Tag::anit-racism'),
    'mobile applications': this.ts.translate('Bee::Template::Tag::mobile applications'),
    'no racism': this.ts.translate('Bee::Template::Tag::no racism'),
    'anti-hate': this.ts.translate('Bee::Template::Tag::anti-hate'),
    fight: this.ts.translate('Bee::Template::Tag::fight'),
    'anti-racism': this.ts.translate('Bee::Template::Tag::anti-racism'),
    ethnicity: this.ts.translate('Bee::Template::Tag::ethnicity'),
    hate: this.ts.translate('Bee::Template::Tag::hate'),
    stop: this.ts.translate('Bee::Template::Tag::stop'),
    'no hate': this.ts.translate('Bee::Template::Tag::no hate'),
    'download app': this.ts.translate('Bee::Template::Tag::download app'),
    happyness: this.ts.translate('Bee::Template::Tag::happyness'),
    energy: this.ts.translate('Bee::Template::Tag::energy'),
    connection: this.ts.translate('Bee::Template::Tag::connection'),
    universe: this.ts.translate('Bee::Template::Tag::universe'),
    suonds: this.ts.translate('Bee::Template::Tag::suonds'),
    feel: this.ts.translate('Bee::Template::Tag::feel'),
    leaves: this.ts.translate('Bee::Template::Tag::leaves'),
    elements: this.ts.translate('Bee::Template::Tag::elements'),
    'may day': this.ts.translate('Bee::Template::Tag::may day'),
    'late delivery': this.ts.translate('Bee::Template::Tag::late delivery'),
    sorry: this.ts.translate('Bee::Template::Tag::sorry'),
    'apology email': this.ts.translate('Bee::Template::Tag::apology email'),
    'online order': this.ts.translate('Bee::Template::Tag::online order'),
    healthcare: this.ts.translate('Bee::Template::Tag::healthcare'),
    'meditation app': this.ts.translate('Bee::Template::Tag::meditation app'),
    care: this.ts.translate('Bee::Template::Tag::care'),
    pet: this.ts.translate('Bee::Template::Tag::pet'),
    jobs: this.ts.translate('Bee::Template::Tag::jobs'),
    'ux designer': this.ts.translate('Bee::Template::Tag::ux designer'),
    userexperience: this.ts.translate('Bee::Template::Tag::userexperience'),
    airy: this.ts.translate('Bee::Template::Tag::airy'),
    coverletter: this.ts.translate('Bee::Template::Tag::coverletter'),
    career: this.ts.translate('Bee::Template::Tag::career'),
    'organic food': this.ts.translate('Bee::Template::Tag::organic food'),
    'wine traditions': this.ts.translate('Bee::Template::Tag::wine traditions'),
    'wine club': this.ts.translate('Bee::Template::Tag::wine club'),
    'online magazine': this.ts.translate('Bee::Template::Tag::online magazine'),
    'national wine day': this.ts.translate('Bee::Template::Tag::national wine day'),
    'national day': this.ts.translate('Bee::Template::Tag::national day'),
    drinks: this.ts.translate('Bee::Template::Tag::drinks'),
    bathbombs: this.ts.translate('Bee::Template::Tag::bathbombs'),
    'pastel colors': this.ts.translate('Bee::Template::Tag::pastel colors'),
    pizza: this.ts.translate('Bee::Template::Tag::pizza'),
    burger: this.ts.translate('Bee::Template::Tag::burger'),
    'hamburger month': this.ts.translate('Bee::Template::Tag::hamburger month'),
    hamburger: this.ts.translate('Bee::Template::Tag::hamburger'),
    bowls: this.ts.translate('Bee::Template::Tag::bowls'),
    'meal prep': this.ts.translate('Bee::Template::Tag::meal prep'),
    'meal kits': this.ts.translate('Bee::Template::Tag::meal kits'),
    organic: this.ts.translate('Bee::Template::Tag::organic'),
    'world environment day': this.ts.translate('Bee::Template::Tag::world environment day'),
    environment: this.ts.translate('Bee::Template::Tag::environment'),
    nutrition: this.ts.translate('Bee::Template::Tag::nutrition'),
    "men's health week": this.ts.translate("Bee::Template::Tag::men's health week"),
    'healthy living': this.ts.translate('Bee::Template::Tag::healthy living'),
    "men's health issues": this.ts.translate("Bee::Template::Tag::men's health issues"),
    'mental health': this.ts.translate('Bee::Template::Tag::mental health'),
    b2b: this.ts.translate('Bee::Template::Tag::b2b'),
    'digital marketing': this.ts.translate('Bee::Template::Tag::digital marketing'),
    'marketing strategies': this.ts.translate('Bee::Template::Tag::marketing strategies'),
    'buisness growth': this.ts.translate('Bee::Template::Tag::buisness growth'),
    doughnut: this.ts.translate('Bee::Template::Tag::doughnut'),
    "don't throw": this.ts.translate("Bee::Template::Tag::don't throw"),
    'stay home': this.ts.translate('Bee::Template::Tag::stay home'),
    sweets: this.ts.translate('Bee::Template::Tag::sweets'),
    'doughnut day': this.ts.translate('Bee::Template::Tag::doughnut day'),
    'special price': this.ts.translate('Bee::Template::Tag::special price'),
    legal: this.ts.translate('Bee::Template::Tag::legal'),
    'legal studio': this.ts.translate('Bee::Template::Tag::legal studio'),
    'introduction email': this.ts.translate('Bee::Template::Tag::introduction email'),
    lawyer: this.ts.translate('Bee::Template::Tag::lawyer'),
    'company introduction': this.ts.translate('Bee::Template::Tag::company introduction'),
    'anual subscription': this.ts.translate('Bee::Template::Tag::anual subscription'),
    'annual subscription': this.ts.translate('Bee::Template::Tag::annual subscription'),
    'digital space': this.ts.translate('Bee::Template::Tag::digital space'),
    'social media day': this.ts.translate('Bee::Template::Tag::social media day'),
    nature: this.ts.translate('Bee::Template::Tag::nature'),
    'environment day': this.ts.translate('Bee::Template::Tag::environment day'),
    'computer and internet': this.ts.translate('Bee::Template::Tag::computer and internet'),
    highlight: this.ts.translate('Bee::Template::Tag::highlight'),
    securities: this.ts.translate('Bee::Template::Tag::securities'),
    divorces: this.ts.translate('Bee::Template::Tag::divorces'),
    defence: this.ts.translate('Bee::Template::Tag::defence'),
    authors: this.ts.translate('Bee::Template::Tag::authors'),
    'delicious donuts': this.ts.translate('Bee::Template::Tag::delicious donuts'),
    'online pastry': this.ts.translate('Bee::Template::Tag::online pastry'),
    'first day of summer': this.ts.translate('Bee::Template::Tag::first day of summer'),
    'summer fashion': this.ts.translate('Bee::Template::Tag::summer fashion'),
    'summer solstice': this.ts.translate('Bee::Template::Tag::summer solstice'),
    'product demo': this.ts.translate('Bee::Template::Tag::product demo'),
    'webinar invitation': this.ts.translate('Bee::Template::Tag::webinar invitation'),
    'national cancer survivors day': this.ts.translate('Bee::Template::Tag::national cancer survivors day'),
    zoom: this.ts.translate('Bee::Template::Tag::zoom'),
    people: this.ts.translate('Bee::Template::Tag::people'),
    date: this.ts.translate('Bee::Template::Tag::date'),
    participation: this.ts.translate('Bee::Template::Tag::participation'),
    sponsors: this.ts.translate('Bee::Template::Tag::sponsors'),
    seat: this.ts.translate('Bee::Template::Tag::seat'),
    answer: this.ts.translate('Bee::Template::Tag::answer'),
    place: this.ts.translate('Bee::Template::Tag::place'),
    google: this.ts.translate('Bee::Template::Tag::google'),
    asking: this.ts.translate('Bee::Template::Tag::asking'),
    meet: this.ts.translate('Bee::Template::Tag::meet'),
    day: this.ts.translate('Bee::Template::Tag::day'),
    national: this.ts.translate('Bee::Template::Tag::national'),
    socialmedia: this.ts.translate('Bee::Template::Tag::socialmedia'),
    editorial: this.ts.translate('Bee::Template::Tag::editorial'),
    oceans: this.ts.translate('Bee::Template::Tag::oceans'),
    sustainability: this.ts.translate('Bee::Template::Tag::sustainability'),
    petition: this.ts.translate('Bee::Template::Tag::petition'),
    form: this.ts.translate('Bee::Template::Tag::form'),
    anounce: this.ts.translate('Bee::Template::Tag::anounce'),
    'pride month': this.ts.translate('Bee::Template::Tag::pride month'),
    'men wearing': this.ts.translate('Bee::Template::Tag::men wearing'),
    "father's day offer": this.ts.translate("Bee::Template::Tag::father's day offer"),
    'use code': this.ts.translate('Bee::Template::Tag::use code'),
    'best gift': this.ts.translate('Bee::Template::Tag::best gift'),
    'for him': this.ts.translate('Bee::Template::Tag::for him'),
    'find out now': this.ts.translate('Bee::Template::Tag::find out now'),
    'big offer': this.ts.translate('Bee::Template::Tag::big offer'),
    'world music day': this.ts.translate('Bee::Template::Tag::world music day'),
    singer: this.ts.translate('Bee::Template::Tag::singer'),
    'music day': this.ts.translate('Bee::Template::Tag::music day'),
    album: this.ts.translate('Bee::Template::Tag::album'),
    'news magazine': this.ts.translate('Bee::Template::Tag::news magazine'),
    rescue: this.ts.translate('Bee::Template::Tag::rescue'),
    adoption: this.ts.translate('Bee::Template::Tag::adoption'),
    shelter: this.ts.translate('Bee::Template::Tag::shelter'),
    'pets and animal care': this.ts.translate('Bee::Template::Tag::pets and animal care'),
    'adopt a cat month': this.ts.translate('Bee::Template::Tag::adopt a cat month'),
    rehoming: this.ts.translate('Bee::Template::Tag::rehoming'),
    campaign: this.ts.translate('Bee::Template::Tag::campaign'),
    'animal shelter': this.ts.translate('Bee::Template::Tag::animal shelter'),
    donate: this.ts.translate('Bee::Template::Tag::donate'),
    jacket: this.ts.translate('Bee::Template::Tag::jacket'),
    suit: this.ts.translate('Bee::Template::Tag::suit'),
    hat: this.ts.translate('Bee::Template::Tag::hat'),
    belt: this.ts.translate('Bee::Template::Tag::belt'),
    briefcase: this.ts.translate('Bee::Template::Tag::briefcase'),
    'freedom day': this.ts.translate('Bee::Template::Tag::freedom day'),
    june: this.ts.translate('Bee::Template::Tag::june'),
    blackman: this.ts.translate('Bee::Template::Tag::blackman'),
    '19june': this.ts.translate('Bee::Template::Tag::19june'),
    juneteenth: this.ts.translate('Bee::Template::Tag::juneteenth'),
    lawfirm: this.ts.translate('Bee::Template::Tag::lawfirm'),
    'black history': this.ts.translate('Bee::Template::Tag::black history'),
    dance: this.ts.translate('Bee::Template::Tag::dance'),
    song: this.ts.translate('Bee::Template::Tag::song'),
    'world blood donor day': this.ts.translate('Bee::Template::Tag::world blood donor day'),
    'blood donation': this.ts.translate('Bee::Template::Tag::blood donation'),
    'free download': this.ts.translate('Bee::Template::Tag::free download'),
    'online library': this.ts.translate('Bee::Template::Tag::online library'),
    showcase: this.ts.translate('Bee::Template::Tag::showcase'),
    writer: this.ts.translate('Bee::Template::Tag::writer'),
    literary: this.ts.translate('Bee::Template::Tag::literary'),
    'cross-platform': this.ts.translate('Bee::Template::Tag::cross-platform'),
    golf: this.ts.translate('Bee::Template::Tag::golf'),
    'car hire': this.ts.translate('Bee::Template::Tag::car hire'),
    automobile: this.ts.translate('Bee::Template::Tag::automobile'),
    automated: this.ts.translate('Bee::Template::Tag::automated'),
    'essential oils': this.ts.translate('Bee::Template::Tag::essential oils'),
    pharmacy: this.ts.translate('Bee::Template::Tag::pharmacy'),
    bar: this.ts.translate('Bee::Template::Tag::bar'),
    pub: this.ts.translate('Bee::Template::Tag::pub'),
    tennis: this.ts.translate('Bee::Template::Tag::tennis'),
    wimbledon: this.ts.translate('Bee::Template::Tag::wimbledon'),
    'sport bar': this.ts.translate('Bee::Template::Tag::sport bar'),
    'spor bar': this.ts.translate('Bee::Template::Tag::spor bar'),
    cycling: this.ts.translate('Bee::Template::Tag::cycling'),
    'tour de france': this.ts.translate('Bee::Template::Tag::tour de france'),
    juice: this.ts.translate('Bee::Template::Tag::juice'),
    contact: this.ts.translate('Bee::Template::Tag::contact'),
    cart: this.ts.translate('Bee::Template::Tag::cart'),
    cherrie: this.ts.translate('Bee::Template::Tag::cherrie'),
    apple: this.ts.translate('Bee::Template::Tag::apple'),
    basket: this.ts.translate('Bee::Template::Tag::basket'),
    buyer: this.ts.translate('Bee::Template::Tag::buyer'),
    trouble: this.ts.translate('Bee::Template::Tag::trouble'),
    checkout: this.ts.translate('Bee::Template::Tag::checkout'),
    tomato: this.ts.translate('Bee::Template::Tag::tomato'),
    avocado: this.ts.translate('Bee::Template::Tag::avocado'),
    homegoods: this.ts.translate('Bee::Template::Tag::homegoods'),
    'confirm your account': this.ts.translate('Bee::Template::Tag::confirm your account'),
    'single opt-in': this.ts.translate('Bee::Template::Tag::single opt-in'),
    confirm: this.ts.translate('Bee::Template::Tag::confirm'),
    optical: this.ts.translate('Bee::Template::Tag::optical'),
    'confirmation email': this.ts.translate('Bee::Template::Tag::confirmation email'),
    'welcome mail': this.ts.translate('Bee::Template::Tag::welcome mail'),
    "70's": this.ts.translate("Bee::Template::Tag::70's"),
    'about me': this.ts.translate('Bee::Template::Tag::about me'),
    artistic: this.ts.translate('Bee::Template::Tag::artistic'),
    'my works': this.ts.translate('Bee::Template::Tag::my works'),
    instagram: this.ts.translate('Bee::Template::Tag::instagram'),
    linktree: this.ts.translate('Bee::Template::Tag::linktree'),
    linkbio: this.ts.translate('Bee::Template::Tag::linkbio'),
    link: this.ts.translate('Bee::Template::Tag::link'),
    socialnetwork: this.ts.translate('Bee::Template::Tag::socialnetwork'),
    iglinkbio: this.ts.translate('Bee::Template::Tag::iglinkbio'),
    bio: this.ts.translate('Bee::Template::Tag::bio'),
    'reset password': this.ts.translate('Bee::Template::Tag::reset password'),
    fries: this.ts.translate('Bee::Template::Tag::fries'),
    'french fries': this.ts.translate('Bee::Template::Tag::french fries'),
    linkinbio: this.ts.translate('Bee::Template::Tag::linkinbio'),
    'link in bio': this.ts.translate('Bee::Template::Tag::link in bio'),
    musician: this.ts.translate('Bee::Template::Tag::musician'),
    digitalnews: this.ts.translate('Bee::Template::Tag::digitalnews'),
    unlock: this.ts.translate('Bee::Template::Tag::unlock'),
    'password reset': this.ts.translate('Bee::Template::Tag::password reset'),
    avatar: this.ts.translate('Bee::Template::Tag::avatar'),
    developer: this.ts.translate('Bee::Template::Tag::developer'),
    persona: this.ts.translate('Bee::Template::Tag::persona'),
    page: this.ts.translate('Bee::Template::Tag::page'),
    confusion: this.ts.translate('Bee::Template::Tag::confusion'),
    questios: this.ts.translate('Bee::Template::Tag::questios'),
    games: this.ts.translate('Bee::Template::Tag::games'),
    play: this.ts.translate('Bee::Template::Tag::play'),
    domain: this.ts.translate('Bee::Template::Tag::domain'),
    hosting: this.ts.translate('Bee::Template::Tag::hosting'),
    'bed and breakfast': this.ts.translate('Bee::Template::Tag::bed and breakfast'),
    links: this.ts.translate('Bee::Template::Tag::links'),
    'new product line': this.ts.translate('Bee::Template::Tag::new product line'),
    'online shop': this.ts.translate('Bee::Template::Tag::online shop'),
    'interior designer': this.ts.translate('Bee::Template::Tag::interior designer'),
    'lead generation': this.ts.translate('Bee::Template::Tag::lead generation'),
    entrepreneur: this.ts.translate('Bee::Template::Tag::entrepreneur'),
    youtuber: this.ts.translate('Bee::Template::Tag::youtuber'),
    booklovers: this.ts.translate('Bee::Template::Tag::booklovers'),
    'publishing company': this.ts.translate('Bee::Template::Tag::publishing company'),
    'mystery giveaway': this.ts.translate('Bee::Template::Tag::mystery giveaway'),
    freebies: this.ts.translate('Bee::Template::Tag::freebies'),
    parks: this.ts.translate('Bee::Template::Tag::parks'),
    'family fun day': this.ts.translate('Bee::Template::Tag::family fun day'),
    'hr/ legal': this.ts.translate('Bee::Template::Tag::hr/ legal'),
    '26th august': this.ts.translate('Bee::Template::Tag::26th august'),
    'garden centre': this.ts.translate('Bee::Template::Tag::garden centre'),
    exploring: this.ts.translate('Bee::Template::Tag::exploring'),
    camping: this.ts.translate('Bee::Template::Tag::camping'),
    wood: this.ts.translate('Bee::Template::Tag::wood'),
    huking: this.ts.translate('Bee::Template::Tag::huking'),
    'contact with nature': this.ts.translate('Bee::Template::Tag::contact with nature'),
    'quality time': this.ts.translate('Bee::Template::Tag::quality time'),
    together: this.ts.translate('Bee::Template::Tag::together'),
    waterfall: this.ts.translate('Bee::Template::Tag::waterfall'),
    'family fun mont': this.ts.translate('Bee::Template::Tag::family fun mont'),
    wallet: this.ts.translate('Bee::Template::Tag::wallet'),
    bitcoin: this.ts.translate('Bee::Template::Tag::bitcoin'),
    trade: this.ts.translate('Bee::Template::Tag::trade'),
    investment: this.ts.translate('Bee::Template::Tag::investment'),
    crypto: this.ts.translate('Bee::Template::Tag::crypto'),
    'mystery sale': this.ts.translate('Bee::Template::Tag::mystery sale'),
    weekend: this.ts.translate('Bee::Template::Tag::weekend'),
    yardwork: this.ts.translate('Bee::Template::Tag::yardwork'),
    lawn: this.ts.translate('Bee::Template::Tag::lawn'),
    motorcycles: this.ts.translate('Bee::Template::Tag::motorcycles'),
    finances: this.ts.translate('Bee::Template::Tag::finances'),
    ocean: this.ts.translate('Bee::Template::Tag::ocean'),
    beach: this.ts.translate('Bee::Template::Tag::beach'),
    sun: this.ts.translate('Bee::Template::Tag::sun'),
    sand: this.ts.translate('Bee::Template::Tag::sand'),
    activity: this.ts.translate('Bee::Template::Tag::activity'),
    bubbles: this.ts.translate('Bee::Template::Tag::bubbles'),
    patisserie: this.ts.translate('Bee::Template::Tag::patisserie'),
    join: this.ts.translate('Bee::Template::Tag::join'),
    route: this.ts.translate('Bee::Template::Tag::route'),
    engines: this.ts.translate('Bee::Template::Tag::engines'),
    road: this.ts.translate('Bee::Template::Tag::road'),
    motorcycle: this.ts.translate('Bee::Template::Tag::motorcycle'),
    wheels: this.ts.translate('Bee::Template::Tag::wheels'),
    two: this.ts.translate('Bee::Template::Tag::two'),
    enjoy: this.ts.translate('Bee::Template::Tag::enjoy'),
    week: this.ts.translate('Bee::Template::Tag::week'),
    stage: this.ts.translate('Bee::Template::Tag::stage'),
    riders: this.ts.translate('Bee::Template::Tag::riders'),
    science: this.ts.translate('Bee::Template::Tag::science'),
    fashionstyle: this.ts.translate('Bee::Template::Tag::fashionstyle'),
    'helping kids': this.ts.translate('Bee::Template::Tag::helping kids'),
    'lip gloss': this.ts.translate('Bee::Template::Tag::lip gloss'),
    balm: this.ts.translate('Bee::Template::Tag::balm'),
    brushes: this.ts.translate('Bee::Template::Tag::brushes'),
    spotify: this.ts.translate('Bee::Template::Tag::spotify'),
    jewellery: this.ts.translate('Bee::Template::Tag::jewellery'),
    'online platform': this.ts.translate('Bee::Template::Tag::online platform'),
    'world tourism day': this.ts.translate('Bee::Template::Tag::world tourism day'),
    picnic: this.ts.translate('Bee::Template::Tag::picnic'),
    'travel agent': this.ts.translate('Bee::Template::Tag::travel agent'),
    greenpeace: this.ts.translate('Bee::Template::Tag::greenpeace'),
    renewable: this.ts.translate('Bee::Template::Tag::renewable'),
    tourism: this.ts.translate('Bee::Template::Tag::tourism'),
    'coffee day': this.ts.translate('Bee::Template::Tag::coffee day'),
    drink: this.ts.translate('Bee::Template::Tag::drink'),
    'greenpeace day': this.ts.translate('Bee::Template::Tag::greenpeace day'),
    episodes: this.ts.translate('Bee::Template::Tag::episodes'),
    tune: this.ts.translate('Bee::Template::Tag::tune'),
    listening: this.ts.translate('Bee::Template::Tag::listening'),
    button: this.ts.translate('Bee::Template::Tag::button'),
    microphone: this.ts.translate('Bee::Template::Tag::microphone'),
    'first day of fall': this.ts.translate('Bee::Template::Tag::first day of fall'),
    story: this.ts.translate('Bee::Template::Tag::story'),
    'health and wellbeing': this.ts.translate('Bee::Template::Tag::health and wellbeing'),
    'listen corner': this.ts.translate('Bee::Template::Tag::listen corner'),
    therapy: this.ts.translate('Bee::Template::Tag::therapy'),
    feelings: this.ts.translate('Bee::Template::Tag::feelings'),
    'mental health day': this.ts.translate('Bee::Template::Tag::mental health day'),
    counseling: this.ts.translate('Bee::Template::Tag::counseling'),
    'climate change': this.ts.translate('Bee::Template::Tag::climate change'),
    activist: this.ts.translate('Bee::Template::Tag::activist'),
    wildlife: this.ts.translate('Bee::Template::Tag::wildlife'),
    'greanpeace day': this.ts.translate('Bee::Template::Tag::greanpeace day'),
    'save the planet': this.ts.translate('Bee::Template::Tag::save the planet'),
    channel: this.ts.translate('Bee::Template::Tag::channel'),
    origin: this.ts.translate('Bee::Template::Tag::origin'),
    delicious: this.ts.translate('Bee::Template::Tag::delicious'),
    paste: this.ts.translate('Bee::Template::Tag::paste'),
    pizzeria: this.ts.translate('Bee::Template::Tag::pizzeria'),
    margherita: this.ts.translate('Bee::Template::Tag::margherita'),
    homemade: this.ts.translate('Bee::Template::Tag::homemade'),
    italia: this.ts.translate('Bee::Template::Tag::italia'),
    oktoberfest: this.ts.translate('Bee::Template::Tag::oktoberfest'),
    wheat: this.ts.translate('Bee::Template::Tag::wheat'),
    alcohol: this.ts.translate('Bee::Template::Tag::alcohol'),
    'seasonal sales': this.ts.translate('Bee::Template::Tag::seasonal sales'),
    'healthy skin': this.ts.translate('Bee::Template::Tag::healthy skin'),
    'halloween costume': this.ts.translate('Bee::Template::Tag::halloween costume'),
    'party invite': this.ts.translate('Bee::Template::Tag::party invite'),
    'night club': this.ts.translate('Bee::Template::Tag::night club'),
    vip: this.ts.translate('Bee::Template::Tag::vip'),
    friday: this.ts.translate('Bee::Template::Tag::friday'),
    dress: this.ts.translate('Bee::Template::Tag::dress'),
    bags: this.ts.translate('Bee::Template::Tag::bags'),
    vegan: this.ts.translate('Bee::Template::Tag::vegan'),
    'safe travel': this.ts.translate('Bee::Template::Tag::safe travel'),
    jewelry: this.ts.translate('Bee::Template::Tag::jewelry'),
    thankful: this.ts.translate('Bee::Template::Tag::thankful'),
    'world vegan day': this.ts.translate('Bee::Template::Tag::world vegan day'),
    'healthy food': this.ts.translate('Bee::Template::Tag::healthy food'),
    'healthy vegan': this.ts.translate('Bee::Template::Tag::healthy vegan'),
    'vegan environment': this.ts.translate('Bee::Template::Tag::vegan environment'),
    knitwear: this.ts.translate('Bee::Template::Tag::knitwear'),
    bussines: this.ts.translate('Bee::Template::Tag::bussines'),
    jumper: this.ts.translate('Bee::Template::Tag::jumper'),
    xmas: this.ts.translate('Bee::Template::Tag::xmas'),
    'hair salon': this.ts.translate('Bee::Template::Tag::hair salon'),
    canfirmation: this.ts.translate('Bee::Template::Tag::canfirmation'),
    cancelation: this.ts.translate('Bee::Template::Tag::cancelation'),
    'small business owner': this.ts.translate('Bee::Template::Tag::small business owner'),
    pottery: this.ts.translate('Bee::Template::Tag::pottery'),
    ceramics: this.ts.translate('Bee::Template::Tag::ceramics'),
    traditional: this.ts.translate('Bee::Template::Tag::traditional'),
    emotional: this.ts.translate('Bee::Template::Tag::emotional'),
    tailor: this.ts.translate('Bee::Template::Tag::tailor'),
    'music streaming': this.ts.translate('Bee::Template::Tag::music streaming'),
    'finance app': this.ts.translate('Bee::Template::Tag::finance app'),
    fundrasing: this.ts.translate('Bee::Template::Tag::fundrasing'),
    november: this.ts.translate('Bee::Template::Tag::november'),
    wedding: this.ts.translate('Bee::Template::Tag::wedding'),
    hiv: this.ts.translate('Bee::Template::Tag::hiv'),
    aids: this.ts.translate('Bee::Template::Tag::aids'),
    'care package': this.ts.translate('Bee::Template::Tag::care package'),
    'year review': this.ts.translate('Bee::Template::Tag::year review'),
    '1th december': this.ts.translate('Bee::Template::Tag::1th december'),
    follow: this.ts.translate('Bee::Template::Tag::follow'),
    condoms: this.ts.translate('Bee::Template::Tag::condoms'),
    playlist: this.ts.translate('Bee::Template::Tag::playlist'),
    'world aids day': this.ts.translate('Bee::Template::Tag::world aids day'),
    'make the difference': this.ts.translate('Bee::Template::Tag::make the difference'),
    'gift guide': this.ts.translate('Bee::Template::Tag::gift guide'),
    promotions: this.ts.translate('Bee::Template::Tag::promotions'),
    chocolate: this.ts.translate('Bee::Template::Tag::chocolate'),
    kpi: this.ts.translate('Bee::Template::Tag::kpi'),
    metrics: this.ts.translate('Bee::Template::Tag::metrics'),
    firends: this.ts.translate('Bee::Template::Tag::firends'),
    'the big day': this.ts.translate('Bee::Template::Tag::the big day'),
    'wedding day': this.ts.translate('Bee::Template::Tag::wedding day'),
    "répondez, s'il vous plaît": this.ts.translate("Bee::Template::Tag::répondez, s'il vous plaît"),
    'your special day': this.ts.translate('Bee::Template::Tag::your special day'),
    'share growth': this.ts.translate('Bee::Template::Tag::share growth'),
    'r.s.v.p.': this.ts.translate('Bee::Template::Tag::r.s.v.p.'),
    rings: this.ts.translate('Bee::Template::Tag::rings'),
    'couple goals': this.ts.translate('Bee::Template::Tag::couple goals'),
    church: this.ts.translate('Bee::Template::Tag::church'),
    couple: this.ts.translate('Bee::Template::Tag::couple'),
    puppies: this.ts.translate('Bee::Template::Tag::puppies'),
    year: this.ts.translate('Bee::Template::Tag::year'),
    'annual report': this.ts.translate('Bee::Template::Tag::annual report'),
    matrimony: this.ts.translate('Bee::Template::Tag::matrimony'),
    marriage: this.ts.translate('Bee::Template::Tag::marriage'),
    union: this.ts.translate('Bee::Template::Tag::union'),
    virus: this.ts.translate('Bee::Template::Tag::virus'),
    life: this.ts.translate('Bee::Template::Tag::life'),
    suffer: this.ts.translate('Bee::Template::Tag::suffer'),
    disease: this.ts.translate('Bee::Template::Tag::disease'),
    blood: this.ts.translate('Bee::Template::Tag::blood'),
    human: this.ts.translate('Bee::Template::Tag::human'),
    understanding: this.ts.translate('Bee::Template::Tag::understanding'),
    transmission: this.ts.translate('Bee::Template::Tag::transmission'),
    fear: this.ts.translate('Bee::Template::Tag::fear'),
    vaccine: this.ts.translate('Bee::Template::Tag::vaccine'),
    prime: this.ts.translate('Bee::Template::Tag::prime'),
    '2022': this.ts.translate('Bee::Template::Tag::2022'),
    tracks: this.ts.translate('Bee::Template::Tag::tracks'),
    'wedding service': this.ts.translate('Bee::Template::Tag::wedding service'),
    'end of year': this.ts.translate('Bee::Template::Tag::end of year'),
    'quarterly results': this.ts.translate('Bee::Template::Tag::quarterly results'),
    'new year’s eve': this.ts.translate('Bee::Template::Tag::new year’s eve'),
    religious: this.ts.translate('Bee::Template::Tag::religious'),
    'media & entretainment': this.ts.translate('Bee::Template::Tag::media & entretainment'),
    'fashion style': this.ts.translate('Bee::Template::Tag::fashion style'),
    jeans: this.ts.translate('Bee::Template::Tag::jeans'),
    't-shirts': this.ts.translate('Bee::Template::Tag::t-shirts'),
    'delivery note': this.ts.translate('Bee::Template::Tag::delivery note'),
    glam: this.ts.translate('Bee::Template::Tag::glam'),
    confetti: this.ts.translate('Bee::Template::Tag::confetti'),
    snapshot: this.ts.translate('Bee::Template::Tag::snapshot'),
    'r.s.v.p': this.ts.translate('Bee::Template::Tag::r.s.v.p'),
    clubbing: this.ts.translate('Bee::Template::Tag::clubbing'),
    champagne: this.ts.translate('Bee::Template::Tag::champagne'),
    'night out': this.ts.translate('Bee::Template::Tag::night out'),
    rock: this.ts.translate('Bee::Template::Tag::rock'),
    traks: this.ts.translate('Bee::Template::Tag::traks'),
    waporwave: this.ts.translate('Bee::Template::Tag::waporwave'),
    'music platform': this.ts.translate('Bee::Template::Tag::music platform'),
    'digital music': this.ts.translate('Bee::Template::Tag::digital music'),
    traklist: this.ts.translate('Bee::Template::Tag::traklist'),
    'raise funds': this.ts.translate('Bee::Template::Tag::raise funds'),
    volunteers: this.ts.translate('Bee::Template::Tag::volunteers'),
    financial: this.ts.translate('Bee::Template::Tag::financial'),
    'home goods retailer': this.ts.translate('Bee::Template::Tag::home goods retailer'),
    'jewish holiday': this.ts.translate('Bee::Template::Tag::jewish holiday'),
    'jewish festival': this.ts.translate('Bee::Template::Tag::jewish festival'),
    chanukah: this.ts.translate('Bee::Template::Tag::chanukah'),
    kislev: this.ts.translate('Bee::Template::Tag::kislev'),
    'home goods': this.ts.translate('Bee::Template::Tag::home goods'),
    hebrew: this.ts.translate('Bee::Template::Tag::hebrew'),
    'social calendar': this.ts.translate('Bee::Template::Tag::social calendar'),
    results: this.ts.translate('Bee::Template::Tag::results'),
    'review year': this.ts.translate('Bee::Template::Tag::review year'),
    'national eggnog day': this.ts.translate('Bee::Template::Tag::national eggnog day'),
    'christmas season': this.ts.translate('Bee::Template::Tag::christmas season'),
    eggnog: this.ts.translate('Bee::Template::Tag::eggnog'),
    'coffee shop': this.ts.translate('Bee::Template::Tag::coffee shop'),
    'human rights month': this.ts.translate('Bee::Template::Tag::human rights month'),
    hannukkah: this.ts.translate('Bee::Template::Tag::hannukkah'),
    classical: this.ts.translate('Bee::Template::Tag::classical'),
    sparkles: this.ts.translate('Bee::Template::Tag::sparkles'),
    'david star': this.ts.translate('Bee::Template::Tag::david star'),
    forniture: this.ts.translate('Bee::Template::Tag::forniture'),
    adopt: this.ts.translate('Bee::Template::Tag::adopt'),
    'santa paws': this.ts.translate('Bee::Template::Tag::santa paws'),
    hospital: this.ts.translate('Bee::Template::Tag::hospital'),
    medical: this.ts.translate('Bee::Template::Tag::medical'),
    park: this.ts.translate('Bee::Template::Tag::park'),
    'amusement park': this.ts.translate('Bee::Template::Tag::amusement park'),
    asymmetric: this.ts.translate('Bee::Template::Tag::asymmetric'),
    exhibition: this.ts.translate('Bee::Template::Tag::exhibition'),
    transaction: this.ts.translate('Bee::Template::Tag::transaction'),
    layout: this.ts.translate('Bee::Template::Tag::layout'),
    'portfolio website': this.ts.translate('Bee::Template::Tag::portfolio website'),
    'company presentation': this.ts.translate('Bee::Template::Tag::company presentation'),
    analytics: this.ts.translate('Bee::Template::Tag::analytics'),
    pomotion: this.ts.translate('Bee::Template::Tag::pomotion'),
    'architecture firm': this.ts.translate('Bee::Template::Tag::architecture firm'),
    'internal newsletter': this.ts.translate('Bee::Template::Tag::internal newsletter'),
    reindeer: this.ts.translate('Bee::Template::Tag::reindeer'),
    'ugly sweaters': this.ts.translate('Bee::Template::Tag::ugly sweaters'),
    'jingle bell sweaters': this.ts.translate('Bee::Template::Tag::jingle bell sweaters'),
    kwanzaa: this.ts.translate('Bee::Template::Tag::kwanzaa'),
    'limited edition': this.ts.translate('Bee::Template::Tag::limited edition'),
    duffles: this.ts.translate('Bee::Template::Tag::duffles'),
    'human rights': this.ts.translate('Bee::Template::Tag::human rights'),
    ugly: this.ts.translate('Bee::Template::Tag::ugly'),
    sweater: this.ts.translate('Bee::Template::Tag::sweater'),
    'ugly christmas sweater': this.ts.translate('Bee::Template::Tag::ugly christmas sweater'),
    african: this.ts.translate('Bee::Template::Tag::african'),
    'Christmas tree': this.ts.translate('Bee::Template::Tag::Christmas tree'),
    'year of the tiger': this.ts.translate('Bee::Template::Tag::year of the tiger'),
    'loyalty program': this.ts.translate('Bee::Template::Tag::loyalty program'),
    'chinese food': this.ts.translate('Bee::Template::Tag::chinese food'),
    'smart house': this.ts.translate('Bee::Template::Tag::smart house'),
    crm: this.ts.translate('Bee::Template::Tag::crm'),
    customer: this.ts.translate('Bee::Template::Tag::customer'),
    'after sale': this.ts.translate('Bee::Template::Tag::after sale'),
    retailer: this.ts.translate('Bee::Template::Tag::retailer'),
    'soup kitchen': this.ts.translate('Bee::Template::Tag::soup kitchen'),
    'meal packaging': this.ts.translate('Bee::Template::Tag::meal packaging'),
    'operation santa paws': this.ts.translate('Bee::Template::Tag::operation santa paws'),
    'adopting pet': this.ts.translate('Bee::Template::Tag::adopting pet'),
    'year of tiger': this.ts.translate('Bee::Template::Tag::year of tiger'),
    'pet fashion': this.ts.translate('Bee::Template::Tag::pet fashion'),
    'pet clothing': this.ts.translate('Bee::Template::Tag::pet clothing'),
    'per outfit': this.ts.translate('Bee::Template::Tag::per outfit'),
    'luxury pets': this.ts.translate('Bee::Template::Tag::luxury pets'),
    costumers: this.ts.translate('Bee::Template::Tag::costumers'),
    sweaters: this.ts.translate('Bee::Template::Tag::sweaters'),
    'post-purchase': this.ts.translate('Bee::Template::Tag::post-purchase'),
    measurement: this.ts.translate('Bee::Template::Tag::measurement'),
    client: this.ts.translate('Bee::Template::Tag::client'),
    satisfaction: this.ts.translate('Bee::Template::Tag::satisfaction'),
    'women hats': this.ts.translate('Bee::Template::Tag::women hats'),
    'print-on-demand marketplace': this.ts.translate('Bee::Template::Tag::print-on-demand marketplace'),
    stickers: this.ts.translate('Bee::Template::Tag::stickers'),
    'art&design': this.ts.translate('Bee::Template::Tag::art&design'),
    'national sticker day': this.ts.translate('Bee::Template::Tag::national sticker day'),
    'charitable event': this.ts.translate('Bee::Template::Tag::charitable event'),
    humanitary: this.ts.translate('Bee::Template::Tag::humanitary'),
    'end violence': this.ts.translate('Bee::Template::Tag::end violence'),
    'create movement': this.ts.translate('Bee::Template::Tag::create movement'),
    tutorials: this.ts.translate('Bee::Template::Tag::tutorials'),
    language: this.ts.translate('Bee::Template::Tag::language'),
    tiger: this.ts.translate('Bee::Template::Tag::tiger'),
    'chinese culture': this.ts.translate('Bee::Template::Tag::chinese culture'),
    studies: this.ts.translate('Bee::Template::Tag::studies'),
    cookies: this.ts.translate('Bee::Template::Tag::cookies'),
    nhl: this.ts.translate('Bee::Template::Tag::nhl'),
    hockey: this.ts.translate('Bee::Template::Tag::hockey'),
    chinematographic: this.ts.translate('Bee::Template::Tag::chinematographic'),
    'shop-online': this.ts.translate('Bee::Template::Tag::shop-online'),
    'new lunar year': this.ts.translate('Bee::Template::Tag::new lunar year'),
    'tiger year': this.ts.translate('Bee::Template::Tag::tiger year'),
    minimalism: this.ts.translate('Bee::Template::Tag::minimalism'),
    centist: this.ts.translate('Bee::Template::Tag::centist'),
    dental: this.ts.translate('Bee::Template::Tag::dental'),
    'know your customer': this.ts.translate('Bee::Template::Tag::know your customer'),
    clinic: this.ts.translate('Bee::Template::Tag::clinic'),
    teeth: this.ts.translate('Bee::Template::Tag::teeth'),
    'world stickers day': this.ts.translate('Bee::Template::Tag::world stickers day'),
    'pop stickers': this.ts.translate('Bee::Template::Tag::pop stickers'),
    'available in meta': this.ts.translate('Bee::Template::Tag::available in meta'),
    lounch: this.ts.translate('Bee::Template::Tag::lounch'),
    'amazing stickers': this.ts.translate('Bee::Template::Tag::amazing stickers'),
    outcomes: this.ts.translate('Bee::Template::Tag::outcomes'),
    'shop online': this.ts.translate('Bee::Template::Tag::shop online'),
    'national hat day': this.ts.translate('Bee::Template::Tag::national hat day'),
    'food app': this.ts.translate('Bee::Template::Tag::food app'),
    customers: this.ts.translate('Bee::Template::Tag::customers'),
    hats: this.ts.translate('Bee::Template::Tag::hats'),
    'hat day': this.ts.translate('Bee::Template::Tag::hat day'),
    cup: this.ts.translate('Bee::Template::Tag::cup'),
    milk: this.ts.translate('Bee::Template::Tag::milk'),
    sugar: this.ts.translate('Bee::Template::Tag::sugar'),
    vanilla: this.ts.translate('Bee::Template::Tag::vanilla'),
    spoon: this.ts.translate('Bee::Template::Tag::spoon'),
    hygiene: this.ts.translate('Bee::Template::Tag::hygiene'),
    'consumer experience': this.ts.translate('Bee::Template::Tag::consumer experience'),
    apparel: this.ts.translate('Bee::Template::Tag::apparel'),
    'baby shower': this.ts.translate('Bee::Template::Tag::baby shower'),
    'bridal shower': this.ts.translate('Bee::Template::Tag::bridal shower'),
    anniversary: this.ts.translate('Bee::Template::Tag::anniversary'),
    'event planner': this.ts.translate('Bee::Template::Tag::event planner'),
    'fall colors travel': this.ts.translate('Bee::Template::Tag::fall colors travel'),
    subscriptions: this.ts.translate('Bee::Template::Tag::subscriptions'),
    headphones: this.ts.translate('Bee::Template::Tag::headphones'),
    'pop-up food': this.ts.translate('Bee::Template::Tag::pop-up food'),
    'food order': this.ts.translate('Bee::Template::Tag::food order'),
    "80's": this.ts.translate("Bee::Template::Tag::80's"),
    'reducing waste': this.ts.translate('Bee::Template::Tag::reducing waste'),
    'living green': this.ts.translate('Bee::Template::Tag::living green'),
    'organic items': this.ts.translate('Bee::Template::Tag::organic items'),
    'pop-up': this.ts.translate('Bee::Template::Tag::pop-up'),
    deli: this.ts.translate('Bee::Template::Tag::deli'),
    'food truck': this.ts.translate('Bee::Template::Tag::food truck'),
    'green eating': this.ts.translate('Bee::Template::Tag::green eating'),
    'food specials': this.ts.translate('Bee::Template::Tag::food specials'),
    'reserve a book': this.ts.translate('Bee::Template::Tag::reserve a book'),
    library: this.ts.translate('Bee::Template::Tag::library'),
    'book reading': this.ts.translate('Bee::Template::Tag::book reading'),
    Books: this.ts.translate('Bee::Template::Tag::Books'),
    'book shop event': this.ts.translate('Bee::Template::Tag::book shop event'),
    'book shop': this.ts.translate('Bee::Template::Tag::book shop'),
    "book lover's day": this.ts.translate("Bee::Template::Tag::book lover's day"),
    'educational event': this.ts.translate('Bee::Template::Tag::educational event'),
    'book lover': this.ts.translate('Bee::Template::Tag::book lover'),
    'take out': this.ts.translate('Bee::Template::Tag::take out'),
    hospitality: this.ts.translate('Bee::Template::Tag::hospitality'),
    motel: this.ts.translate('Bee::Template::Tag::motel'),
    '4th of July': this.ts.translate('Bee::Template::Tag::4th of July'),
    'Red White and Blue': this.ts.translate('Bee::Template::Tag::Red White and Blue'),
    'American flag': this.ts.translate('Bee::Template::Tag::American flag'),
    'seasonal sale': this.ts.translate('Bee::Template::Tag::seasonal sale'),
    seeds: this.ts.translate('Bee::Template::Tag::seeds'),
    'Summer sale': this.ts.translate('Bee::Template::Tag::Summer sale'),
    nursery: this.ts.translate('Bee::Template::Tag::nursery'),
    'spring sale': this.ts.translate('Bee::Template::Tag::spring sale'),
    'hair stylist': this.ts.translate('Bee::Template::Tag::hair stylist'),
    shave: this.ts.translate('Bee::Template::Tag::shave'),
    "Men's health week": this.ts.translate("Bee::Template::Tag::Men's health week"),
    grooming: this.ts.translate('Bee::Template::Tag::grooming'),
    "men's hair cut": this.ts.translate("Bee::Template::Tag::men's hair cut"),
    'Resume with profile photo': this.ts.translate('Bee::Template::Tag::Resume with profile photo'),
    CV: this.ts.translate('Bee::Template::Tag::CV'),
    'CV with profile photo': this.ts.translate('Bee::Template::Tag::CV with profile photo'),
    Resume: this.ts.translate('Bee::Template::Tag::Resume'),
    'Curriculum Vitae': this.ts.translate('Bee::Template::Tag::Curriculum Vitae'),
    'May 5': this.ts.translate('Bee::Template::Tag::May 5'),
    computers: this.ts.translate('Bee::Template::Tag::computers'),
    'Geek Pride Day': this.ts.translate('Bee::Template::Tag::Geek Pride Day'),
    exhibit: this.ts.translate('Bee::Template::Tag::exhibit'),
    'Art promotion': this.ts.translate('Bee::Template::Tag::Art promotion'),
    'open studio': this.ts.translate('Bee::Template::Tag::open studio'),
    'art tour': this.ts.translate('Bee::Template::Tag::art tour'),
    'gallery promotion': this.ts.translate('Bee::Template::Tag::gallery promotion'),
    "April Fool's Day": this.ts.translate("Bee::Template::Tag::April Fool's Day"),
    'Spring Sale': this.ts.translate('Bee::Template::Tag::Spring Sale'),
    'April 1': this.ts.translate('Bee::Template::Tag::April 1'),
    'Spring promotion': this.ts.translate('Bee::Template::Tag::Spring promotion'),
    'Spring event': this.ts.translate('Bee::Template::Tag::Spring event'),
    'April Fools Day': this.ts.translate('Bee::Template::Tag::April Fools Day'),
    'Reset password': this.ts.translate('Bee::Template::Tag::Reset password'),
    're-set password': this.ts.translate('Bee::Template::Tag::re-set password'),
    'update password': this.ts.translate('Bee::Template::Tag::update password'),
    lock: this.ts.translate('Bee::Template::Tag::lock'),
    'forgotten password': this.ts.translate('Bee::Template::Tag::forgotten password'),
    'Folk Holiday': this.ts.translate('Bee::Template::Tag::Folk Holiday'),
    Maslenitsa: this.ts.translate('Bee::Template::Tag::Maslenitsa'),
    'animal advocates': this.ts.translate('Bee::Template::Tag::animal advocates'),
    'animal rescue': this.ts.translate('Bee::Template::Tag::animal rescue'),
    'selling art': this.ts.translate('Bee::Template::Tag::selling art'),
    'twelve days': this.ts.translate('Bee::Template::Tag::twelve days'),
    'book club': this.ts.translate('Bee::Template::Tag::book club'),
    model: this.ts.translate('Bee::Template::Tag::model'),
    actress: this.ts.translate('Bee::Template::Tag::actress'),
    LGBTQ: this.ts.translate('Bee::Template::Tag::LGBTQ'),
    'shop look': this.ts.translate('Bee::Template::Tag::shop look'),
    multicolored: this.ts.translate('Bee::Template::Tag::multicolored'),
    'black and yellow': this.ts.translate('Bee::Template::Tag::black and yellow'),
    'social justice': this.ts.translate('Bee::Template::Tag::social justice'),
    'weekly updates': this.ts.translate('Bee::Template::Tag::weekly updates'),
    'day care': this.ts.translate('Bee::Template::Tag::day care'),
    'after school care': this.ts.translate('Bee::Template::Tag::after school care'),
    preschool: this.ts.translate('Bee::Template::Tag::preschool'),
    'book of the month': this.ts.translate('Bee::Template::Tag::book of the month'),
    'mystery event': this.ts.translate('Bee::Template::Tag::mystery event'),
    'reveal your deal': this.ts.translate('Bee::Template::Tag::reveal your deal'),
    freelancers: this.ts.translate('Bee::Template::Tag::freelancers'),
    'current events': this.ts.translate('Bee::Template::Tag::current events'),
    'non-profits': this.ts.translate('Bee::Template::Tag::non-profits'),
    'hair salons': this.ts.translate('Bee::Template::Tag::hair salons'),
    "valentine's": this.ts.translate("Bee::Template::Tag::valentine's"),
    'flu season': this.ts.translate('Bee::Template::Tag::flu season'),
    'cold season': this.ts.translate('Bee::Template::Tag::cold season'),
    handwashing: this.ts.translate('Bee::Template::Tag::handwashing'),
    'staying healthy': this.ts.translate('Bee::Template::Tag::staying healthy'),
    'November 3rd': this.ts.translate('Bee::Template::Tag::November 3rd'),
    eatery: this.ts.translate('Bee::Template::Tag::eatery'),
    'e-commerce promotion': this.ts.translate('Bee::Template::Tag::e-commerce promotion'),
    'company news': this.ts.translate('Bee::Template::Tag::company news'),
    blogging: this.ts.translate('Bee::Template::Tag::blogging'),
    'organizational updates': this.ts.translate('Bee::Template::Tag::organizational updates'),
    Blog: this.ts.translate('Bee::Template::Tag::Blog'),
    bistro: this.ts.translate('Bee::Template::Tag::bistro'),
    'breast cancer awareness': this.ts.translate('Bee::Template::Tag::breast cancer awareness'),
    October: this.ts.translate('Bee::Template::Tag::October'),
    'password management': this.ts.translate('Bee::Template::Tag::password management'),
    cybersecurity: this.ts.translate('Bee::Template::Tag::cybersecurity'),
    'data protection': this.ts.translate('Bee::Template::Tag::data protection'),
    GDPR: this.ts.translate('Bee::Template::Tag::GDPR'),
    phishing: this.ts.translate('Bee::Template::Tag::phishing'),
    IT: this.ts.translate('Bee::Template::Tag::IT'),
    spam: this.ts.translate('Bee::Template::Tag::spam'),
    costumes: this.ts.translate('Bee::Template::Tag::costumes'),
    'jack-o-lantern': this.ts.translate('Bee::Template::Tag::jack-o-lantern'),
    'March 17th': this.ts.translate('Bee::Template::Tag::March 17th'),
    'customer feedback': this.ts.translate('Bee::Template::Tag::customer feedback'),
    Dublin: this.ts.translate('Bee::Template::Tag::Dublin'),
    'meet up': this.ts.translate('Bee::Template::Tag::meet up'),
    photographers: this.ts.translate('Bee::Template::Tag::photographers'),
    'cover letter': this.ts.translate('Bee::Template::Tag::cover letter'),
    'home school': this.ts.translate('Bee::Template::Tag::home school'),
    'emergency message': this.ts.translate('Bee::Template::Tag::emergency message'),
    'cyber Monday': this.ts.translate('Bee::Template::Tag::cyber Monday'),
    'welcome back': this.ts.translate('Bee::Template::Tag::welcome back'),
    podcaster: this.ts.translate('Bee::Template::Tag::podcaster'),
    café: this.ts.translate('Bee::Template::Tag::café'),
    trips: this.ts.translate('Bee::Template::Tag::trips'),
    text: this.ts.translate('Bee::Template::Tag::text'),
    networking: this.ts.translate('Bee::Template::Tag::networking'),
    'tour group': this.ts.translate('Bee::Template::Tag::tour group'),
    'hair cut': this.ts.translate('Bee::Template::Tag::hair cut'),
    'check out': this.ts.translate('Bee::Template::Tag::check out'),
    'Abandoned Cart': this.ts.translate('Bee::Template::Tag::Abandoned Cart'),
    'e-cart': this.ts.translate('Bee::Template::Tag::e-cart'),
    'track your order': this.ts.translate('Bee::Template::Tag::track your order'),
    'E-commerce': this.ts.translate('Bee::Template::Tag::E-commerce'),
    'track your package': this.ts.translate('Bee::Template::Tag::track your package'),
    'Shipping Confirmation': this.ts.translate('Bee::Template::Tag::Shipping Confirmation'),
    'next order': this.ts.translate('Bee::Template::Tag::next order'),
    'mothers day': this.ts.translate('Bee::Template::Tag::mothers day'),
    agricultre: this.ts.translate('Bee::Template::Tag::agricultre'),
    'yard work': this.ts.translate('Bee::Template::Tag::yard work'),
    smartwatch: this.ts.translate('Bee::Template::Tag::smartwatch'),
    'start now': this.ts.translate('Bee::Template::Tag::start now'),
    Spring: this.ts.translate('Bee::Template::Tag::Spring'),
    'Easter egg': this.ts.translate('Bee::Template::Tag::Easter egg'),
    'deal reveal': this.ts.translate('Bee::Template::Tag::deal reveal'),
    chatbot: this.ts.translate('Bee::Template::Tag::chatbot'),
    training: this.ts.translate('Bee::Template::Tag::training'),
    print: this.ts.translate('Bee::Template::Tag::print'),
    'print services': this.ts.translate('Bee::Template::Tag::print services'),
    performance: this.ts.translate('Bee::Template::Tag::performance'),
    skeleton: this.ts.translate('Bee::Template::Tag::skeleton'),
    run: this.ts.translate('Bee::Template::Tag::run'),
    'running app': this.ts.translate('Bee::Template::Tag::running app'),
    'school supplies': this.ts.translate('Bee::Template::Tag::school supplies'),
    'family reunion': this.ts.translate('Bee::Template::Tag::family reunion'),
    rowing: this.ts.translate('Bee::Template::Tag::rowing'),
    nautical: this.ts.translate('Bee::Template::Tag::nautical'),
    crew: this.ts.translate('Bee::Template::Tag::crew'),
    row: this.ts.translate('Bee::Template::Tag::row'),
    'pick up': this.ts.translate('Bee::Template::Tag::pick up'),
    courier: this.ts.translate('Bee::Template::Tag::courier'),
    'buy now': this.ts.translate('Bee::Template::Tag::buy now'),
    'movie review': this.ts.translate('Bee::Template::Tag::movie review'),
    trainer: this.ts.translate('Bee::Template::Tag::trainer'),
    'sign-up': this.ts.translate('Bee::Template::Tag::sign-up'),
    '24 hour sale': this.ts.translate('Bee::Template::Tag::24 hour sale'),
    'block party': this.ts.translate('Bee::Template::Tag::block party'),
    fair: this.ts.translate('Bee::Template::Tag::fair'),
    won: this.ts.translate('Bee::Template::Tag::won'),
    raffle: this.ts.translate('Bee::Template::Tag::raffle'),
    pledge: this.ts.translate('Bee::Template::Tag::pledge'),
    'community group': this.ts.translate('Bee::Template::Tag::community group'),
    'instagram bio': this.ts.translate('Bee::Template::Tag::instagram bio'),
    chefs: this.ts.translate('Bee::Template::Tag::chefs'),
    'letter to Santa': this.ts.translate('Bee::Template::Tag::letter to Santa'),
    Santa: this.ts.translate('Bee::Template::Tag::Santa'),
    'Santa Claus': this.ts.translate('Bee::Template::Tag::Santa Claus'),
    'holiday sale': this.ts.translate('Bee::Template::Tag::holiday sale'),
    'summer sale': this.ts.translate('Bee::Template::Tag::summer sale'),
    RSVP: this.ts.translate('Bee::Template::Tag::RSVP'),
    skateboarding: this.ts.translate('Bee::Template::Tag::skateboarding'),
    'information session': this.ts.translate('Bee::Template::Tag::information session'),
    'lifelong learning': this.ts.translate('Bee::Template::Tag::lifelong learning'),
    orthodontist: this.ts.translate('Bee::Template::Tag::orthodontist'),
    doctor: this.ts.translate('Bee::Template::Tag::doctor'),
    'vacation rental': this.ts.translate('Bee::Template::Tag::vacation rental'),
    bonds: this.ts.translate('Bee::Template::Tag::bonds'),
    trading: this.ts.translate('Bee::Template::Tag::trading'),
    stocks: this.ts.translate('Bee::Template::Tag::stocks'),
    brokerage: this.ts.translate('Bee::Template::Tag::brokerage'),
    bid: this.ts.translate('Bee::Template::Tag::bid'),
    quote: this.ts.translate('Bee::Template::Tag::quote'),
    contractor: this.ts.translate('Bee::Template::Tag::contractor'),
    'gig work': this.ts.translate('Bee::Template::Tag::gig work'),
    'palm tree': this.ts.translate('Bee::Template::Tag::palm tree'),
    'health & wellness': this.ts.translate('Bee::Template::Tag::health & wellness'),
    'New Year': this.ts.translate('Bee::Template::Tag::New Year'),
    "New Year's Eve": this.ts.translate("Bee::Template::Tag::New Year's Eve"),
    'Food & Beverage': this.ts.translate('Bee::Template::Tag::Food & Beverage'),
    Halloween: this.ts.translate('Bee::Template::Tag::Halloween'),
    'trick or treat': this.ts.translate('Bee::Template::Tag::trick or treat'),
    'haunted house': this.ts.translate('Bee::Template::Tag::haunted house'),
    'car show': this.ts.translate('Bee::Template::Tag::car show'),
    'Las Vegas': this.ts.translate('Bee::Template::Tag::Las Vegas'),
    'the city': this.ts.translate('Bee::Template::Tag::the city'),
    Nevada: this.ts.translate('Bee::Template::Tag::Nevada'),
    bridal: this.ts.translate('Bee::Template::Tag::bridal'),
    'make-up': this.ts.translate('Bee::Template::Tag::make-up'),
    houses: this.ts.translate('Bee::Template::Tag::houses'),
    'property management': this.ts.translate('Bee::Template::Tag::property management'),
    'vacation rentals': this.ts.translate('Bee::Template::Tag::vacation rentals'),
    stylist: this.ts.translate('Bee::Template::Tag::stylist'),
    blogger: this.ts.translate('Bee::Template::Tag::blogger'),
    'black Friday': this.ts.translate('Bee::Template::Tag::black Friday'),
    "woman's equality day": this.ts.translate("Bee::Template::Tag::woman's equality day"),
    "woman's rights": this.ts.translate("Bee::Template::Tag::woman's rights"),
    'online education': this.ts.translate('Bee::Template::Tag::online education'),
    'trade show': this.ts.translate('Bee::Template::Tag::trade show'),
    'business promotion': this.ts.translate('Bee::Template::Tag::business promotion'),
    'booth invitation': this.ts.translate('Bee::Template::Tag::booth invitation'),
    'Health & Wellness': this.ts.translate('Bee::Template::Tag::Health & Wellness'),
    clinique: this.ts.translate('Bee::Template::Tag::clinique'),
    jazz: this.ts.translate('Bee::Template::Tag::jazz'),
    Internet: this.ts.translate('Bee::Template::Tag::Internet'),
    Computer: this.ts.translate('Bee::Template::Tag::Computer'),
    racing: this.ts.translate('Bee::Template::Tag::racing'),
    'weekend sale': this.ts.translate('Bee::Template::Tag::weekend sale'),
    'Instagram bio': this.ts.translate('Bee::Template::Tag::Instagram bio'),
    'thanksgiving day': this.ts.translate('Bee::Template::Tag::thanksgiving day'),
    'Instagram Bio': this.ts.translate('Bee::Template::Tag::Instagram Bio'),
    'world emoji day': this.ts.translate('Bee::Template::Tag::world emoji day'),
    'Human Resources': this.ts.translate('Bee::Template::Tag::Human Resources'),
    'international coffee day': this.ts.translate('Bee::Template::Tag::international coffee day'),
    Hr: this.ts.translate('Bee::Template::Tag::Hr'),
    'game over': this.ts.translate('Bee::Template::Tag::game over'),
    'personal note': this.ts.translate('Bee::Template::Tag::personal note'),
    "men's hair": this.ts.translate("Bee::Template::Tag::men's hair"),
    rabbit: this.ts.translate('Bee::Template::Tag::rabbit'),
    'seasonal promotion  promotion': this.ts.translate('Bee::Template::Tag::seasonal promotion  promotion'),
    'home delivery': this.ts.translate('Bee::Template::Tag::home delivery'),
    interview: this.ts.translate('Bee::Template::Tag::interview'),
    HR: this.ts.translate('Bee::Template::Tag::HR'),
    sourcing: this.ts.translate('Bee::Template::Tag::sourcing'),
    'customer spotlight': this.ts.translate('Bee::Template::Tag::customer spotlight'),
    episode: this.ts.translate('Bee::Template::Tag::episode'),
    'wine tasting': this.ts.translate('Bee::Template::Tag::wine tasting'),
    vintner: this.ts.translate('Bee::Template::Tag::vintner'),
    winery: this.ts.translate('Bee::Template::Tag::winery'),
    diversity: this.ts.translate('Bee::Template::Tag::diversity'),
    'first day': this.ts.translate('Bee::Template::Tag::first day'),
    Newsletter: this.ts.translate('Bee::Template::Tag::Newsletter'),
    'coffee roast': this.ts.translate('Bee::Template::Tag::coffee roast'),
    cappuccino: this.ts.translate('Bee::Template::Tag::cappuccino'),
    barista: this.ts.translate('Bee::Template::Tag::barista'),
    'coffee brew': this.ts.translate('Bee::Template::Tag::coffee brew'),
    'SDG festival of action': this.ts.translate('Bee::Template::Tag::SDG festival of action'),
    'the force': this.ts.translate('Bee::Template::Tag::the force'),
    planets: this.ts.translate('Bee::Template::Tag::planets'),
    'outer space': this.ts.translate('Bee::Template::Tag::outer space'),
    'employee engagement': this.ts.translate('Bee::Template::Tag::employee engagement'),
    'sales department': this.ts.translate('Bee::Template::Tag::sales department'),
    App: this.ts.translate('Bee::Template::Tag::App'),
    Download: this.ts.translate('Bee::Template::Tag::Download'),
    custom: this.ts.translate('Bee::Template::Tag::custom'),
    'self care': this.ts.translate('Bee::Template::Tag::self care'),
    'social work': this.ts.translate('Bee::Template::Tag::social work'),
    'first meeting': this.ts.translate('Bee::Template::Tag::first meeting'),
    'company policies': this.ts.translate('Bee::Template::Tag::company policies'),
    'best practices': this.ts.translate('Bee::Template::Tag::best practices'),
    'organization news': this.ts.translate('Bee::Template::Tag::organization news'),
    'fathers day': this.ts.translate('Bee::Template::Tag::fathers day'),
    electornics: this.ts.translate('Bee::Template::Tag::electornics'),
    'monthly update': this.ts.translate('Bee::Template::Tag::monthly update'),
    patriotic: this.ts.translate('Bee::Template::Tag::patriotic'),
    'Cinco de Mayo': this.ts.translate('Bee::Template::Tag::Cinco de Mayo'),
    educationalapp: this.ts.translate('Bee::Template::Tag::educationalapp'),
    'video game tournament': this.ts.translate('Bee::Template::Tag::video game tournament'),
    educate: this.ts.translate('Bee::Template::Tag::educate'),
    stakeholders: this.ts.translate('Bee::Template::Tag::stakeholders'),
    'Labor Day': this.ts.translate('Bee::Template::Tag::Labor Day'),
    Synthesizer: this.ts.translate('Bee::Template::Tag::Synthesizer'),
    Music: this.ts.translate('Bee::Template::Tag::Music'),
    Sound: this.ts.translate('Bee::Template::Tag::Sound'),
    'Music Equipment': this.ts.translate('Bee::Template::Tag::Music Equipment'),
    'Music Production': this.ts.translate('Bee::Template::Tag::Music Production'),
    Sale: this.ts.translate('Bee::Template::Tag::Sale'),
    'gender rights': this.ts.translate('Bee::Template::Tag::gender rights'),
    'make a difference': this.ts.translate('Bee::Template::Tag::make a difference'),
    'School Spirit': this.ts.translate('Bee::Template::Tag::School Spirit'),
    'Application Information': this.ts.translate('Bee::Template::Tag::Application Information'),
    Parents: this.ts.translate('Bee::Template::Tag::Parents'),
    Discounts: this.ts.translate('Bee::Template::Tag::Discounts'),
    "Lett's connect": this.ts.translate("Bee::Template::Tag::Lett's connect"),
    Promotions: this.ts.translate('Bee::Template::Tag::Promotions'),
    Technology: this.ts.translate('Bee::Template::Tag::Technology'),
    alumni: this.ts.translate('Bee::Template::Tag::alumni'),
    'Open House': this.ts.translate('Bee::Template::Tag::Open House'),
    admissions: this.ts.translate('Bee::Template::Tag::admissions'),
    'Open day': this.ts.translate('Bee::Template::Tag::Open day'),
    School: this.ts.translate('Bee::Template::Tag::School'),
    Enrollment: this.ts.translate('Bee::Template::Tag::Enrollment'),
    'student engagement': this.ts.translate('Bee::Template::Tag::student engagement'),
    enrollment: this.ts.translate('Bee::Template::Tag::enrollment'),
    'New Years Eve': this.ts.translate('Bee::Template::Tag::New Years Eve'),
    Party: this.ts.translate('Bee::Template::Tag::Party'),
    Celebrate: this.ts.translate('Bee::Template::Tag::Celebrate'),
    Invitation: this.ts.translate('Bee::Template::Tag::Invitation'),
    Milestones: this.ts.translate('Bee::Template::Tag::Milestones'),
    'Year in review': this.ts.translate('Bee::Template::Tag::Year in review'),
    'climate change. earth day': this.ts.translate('Bee::Template::Tag::climate change. earth day'),
    'ocean health': this.ts.translate('Bee::Template::Tag::ocean health'),
    'paddle out': this.ts.translate('Bee::Template::Tag::paddle out'),
    'environmental tourism': this.ts.translate('Bee::Template::Tag::environmental tourism'),
    lodging: this.ts.translate('Bee::Template::Tag::lodging'),
    hotels: this.ts.translate('Bee::Template::Tag::hotels'),
    airlines: this.ts.translate('Bee::Template::Tag::airlines'),
    '99998': this.ts.translate('Bee::Template::Tag::99998'),
    'mother day': this.ts.translate('Bee::Template::Tag::mother day'),
    'new team member': this.ts.translate('Bee::Template::Tag::new team member'),
    'human resources day': this.ts.translate('Bee::Template::Tag::human resources day'),
    'new employee': this.ts.translate('Bee::Template::Tag::new employee'),
    'search services': this.ts.translate('Bee::Template::Tag::search services'),
    recruit: this.ts.translate('Bee::Template::Tag::recruit'),
    recruiters: this.ts.translate('Bee::Template::Tag::recruiters'),
    outsourcing: this.ts.translate('Bee::Template::Tag::outsourcing'),
    'lgbtq+': this.ts.translate('Bee::Template::Tag::lgbtq+'),
    remember: this.ts.translate('Bee::Template::Tag::remember'),
    honor: this.ts.translate('Bee::Template::Tag::honor'),
    'online news': this.ts.translate('Bee::Template::Tag::online news'),
    'may 30': this.ts.translate('Bee::Template::Tag::may 30'),
    'armed services': this.ts.translate('Bee::Template::Tag::armed services'),
    military: this.ts.translate('Bee::Template::Tag::military'),
    inclusion: this.ts.translate('Bee::Template::Tag::inclusion'),
    breakfast: this.ts.translate('Bee::Template::Tag::breakfast'),
    cafe: this.ts.translate('Bee::Template::Tag::cafe'),
    'grand opening': this.ts.translate('Bee::Template::Tag::grand opening'),
    'tour dates': this.ts.translate('Bee::Template::Tag::tour dates'),
    vino: this.ts.translate('Bee::Template::Tag::vino'),
    'National Wine Day': this.ts.translate('Bee::Template::Tag::National Wine Day'),
    Yellow: this.ts.translate('Bee::Template::Tag::Yellow'),
    Leisure: this.ts.translate('Bee::Template::Tag::Leisure'),
    Travel: this.ts.translate('Bee::Template::Tag::Travel'),
    November: this.ts.translate('Bee::Template::Tag::November'),
    followers: this.ts.translate('Bee::Template::Tag::followers'),
    tour: this.ts.translate('Bee::Template::Tag::tour'),
    'Human resources': this.ts.translate('Bee::Template::Tag::Human resources'),
    'hiring managers': this.ts.translate('Bee::Template::Tag::hiring managers'),
    'see you tomorrow': this.ts.translate('Bee::Template::Tag::see you tomorrow'),
    'Human resources. newsletter': this.ts.translate('Bee::Template::Tag::Human resources. newsletter'),
    'organizational  update': this.ts.translate('Bee::Template::Tag::organizational  update'),
    'company information': this.ts.translate('Bee::Template::Tag::company information'),
    'national gardening month': this.ts.translate('Bee::Template::Tag::national gardening month'),
    fragrance: this.ts.translate('Bee::Template::Tag::fragrance'),
    'black stories': this.ts.translate('Bee::Template::Tag::black stories'),
    create: this.ts.translate('Bee::Template::Tag::create'),
    "international woman's day": this.ts.translate("Bee::Template::Tag::international woman's day"),
    equity: this.ts.translate('Bee::Template::Tag::equity'),
    recreation: this.ts.translate('Bee::Template::Tag::recreation'),
    'chamber of commerce': this.ts.translate('Bee::Template::Tag::chamber of commerce'),
    'video games tournament': this.ts.translate('Bee::Template::Tag::video games tournament'),
    'September 12': this.ts.translate('Bee::Template::Tag::September 12'),
    'Video Games Day': this.ts.translate('Bee::Template::Tag::Video Games Day'),
    lake: this.ts.translate('Bee::Template::Tag::lake'),
    river: this.ts.translate('Bee::Template::Tag::river'),
    "Woman's Equality Day": this.ts.translate("Bee::Template::Tag::Woman's Equality Day"),
    'Small Business': this.ts.translate('Bee::Template::Tag::Small Business'),
    "Woman's entrepreneurs": this.ts.translate("Bee::Template::Tag::Woman's entrepreneurs"),
    'Woman Owned Businesses': this.ts.translate('Bee::Template::Tag::Woman Owned Businesses'),
    'professional development': this.ts.translate('Bee::Template::Tag::professional development'),
    'online learning': this.ts.translate('Bee::Template::Tag::online learning'),
    'higher education': this.ts.translate('Bee::Template::Tag::higher education'),
    'School Supplies': this.ts.translate('Bee::Template::Tag::School Supplies'),
    'School Rules': this.ts.translate('Bee::Template::Tag::School Rules'),
    'Teacher Wish Lists': this.ts.translate('Bee::Template::Tag::Teacher Wish Lists'),
    'Back to School': this.ts.translate('Bee::Template::Tag::Back to School'),
    'Parents. Enrichment Activities': this.ts.translate('Bee::Template::Tag::Parents. Enrichment Activities'),
    'Career opportunities': this.ts.translate('Bee::Template::Tag::Career opportunities'),
    'Academic Advising': this.ts.translate('Bee::Template::Tag::Academic Advising'),
    'Apply Now': this.ts.translate('Bee::Template::Tag::Apply Now'),
    'Brand Awareness': this.ts.translate('Bee::Template::Tag::Brand Awareness'),
    'Get Followers': this.ts.translate('Bee::Template::Tag::Get Followers'),
    'Social Media': this.ts.translate('Bee::Template::Tag::Social Media'),
    'Dark Mode': this.ts.translate('Bee::Template::Tag::Dark Mode'),
    SMB: this.ts.translate('Bee::Template::Tag::SMB'),
    Marketing: this.ts.translate('Bee::Template::Tag::Marketing'),
    'Get Likes': this.ts.translate('Bee::Template::Tag::Get Likes'),
    "Let's connect": this.ts.translate("Bee::Template::Tag::Let's connect"),
    moda: this.ts.translate('Bee::Template::Tag::moda'),
    sleek: this.ts.translate('Bee::Template::Tag::sleek'),
    'new york': this.ts.translate('Bee::Template::Tag::new york'),
    'fashion week': this.ts.translate('Bee::Template::Tag::fashion week'),
    'fashion show': this.ts.translate('Bee::Template::Tag::fashion show'),
    valentines: this.ts.translate('Bee::Template::Tag::valentines'),
    'february 14': this.ts.translate('Bee::Template::Tag::february 14'),
    'st. valentine': this.ts.translate('Bee::Template::Tag::st. valentine'),
    'love day': this.ts.translate('Bee::Template::Tag::love day'),
    burgers: this.ts.translate('Bee::Template::Tag::burgers'),
    superbowl: this.ts.translate('Bee::Template::Tag::superbowl'),
    rugby: this.ts.translate('Bee::Template::Tag::rugby'),
    weekfashion: this.ts.translate('Bee::Template::Tag::weekfashion'),
    survivor: this.ts.translate('Bee::Template::Tag::survivor'),
    'world cancer day': this.ts.translate('Bee::Template::Tag::world cancer day'),
    'wedding ring': this.ts.translate('Bee::Template::Tag::wedding ring'),
    diamond: this.ts.translate('Bee::Template::Tag::diamond'),
    'customer testimonial': this.ts.translate('Bee::Template::Tag::customer testimonial'),
    'give kids a smile day': this.ts.translate('Bee::Template::Tag::give kids a smile day'),
    dorctor: this.ts.translate('Bee::Template::Tag::dorctor'),
    kickoff: this.ts.translate('Bee::Template::Tag::kickoff'),
    nfl: this.ts.translate('Bee::Template::Tag::nfl'),
    fans: this.ts.translate('Bee::Template::Tag::fans'),
    'viewing party': this.ts.translate('Bee::Template::Tag::viewing party'),
    'big night': this.ts.translate('Bee::Template::Tag::big night'),
    'good actions': this.ts.translate('Bee::Template::Tag::good actions'),
    society: this.ts.translate('Bee::Template::Tag::society'),
    kind: this.ts.translate('Bee::Template::Tag::kind'),
    nice: this.ts.translate('Bee::Template::Tag::nice'),
    'good vibes': this.ts.translate('Bee::Template::Tag::good vibes'),
    rak: this.ts.translate('Bee::Template::Tag::rak'),
    helpful: this.ts.translate('Bee::Template::Tag::helpful'),
    cosmetic: this.ts.translate('Bee::Template::Tag::cosmetic'),
    beaty: this.ts.translate('Bee::Template::Tag::beaty'),
    Pathways: this.ts.translate('Bee::Template::Tag::Pathways'),
    Scholarships: this.ts.translate('Bee::Template::Tag::Scholarships'),
    'student  engagement': this.ts.translate('Bee::Template::Tag::student  engagement'),
    'Information Session': this.ts.translate('Bee::Template::Tag::Information Session'),
    Giving: this.ts.translate('Bee::Template::Tag::Giving'),
    Engagement: this.ts.translate('Bee::Template::Tag::Engagement'),
    Alumni: this.ts.translate('Bee::Template::Tag::Alumni'),
    Networking: this.ts.translate('Bee::Template::Tag::Networking'),
    Hoiday: this.ts.translate('Bee::Template::Tag::Hoiday'),
    'financial goals': this.ts.translate('Bee::Template::Tag::financial goals'),
    Spending: this.ts.translate('Bee::Template::Tag::Spending'),
    'single awareness': this.ts.translate('Bee::Template::Tag::single awareness'),
    single: this.ts.translate('Bee::Template::Tag::single'),
    medicine: this.ts.translate('Bee::Template::Tag::medicine'),
    cocktail: this.ts.translate('Bee::Template::Tag::cocktail'),
    cards: this.ts.translate('Bee::Template::Tag::cards'),
    'random act of kindness': this.ts.translate('Bee::Template::Tag::random act of kindness'),
    'give kids a smile': this.ts.translate('Bee::Template::Tag::give kids a smile'),
    magazines: this.ts.translate('Bee::Template::Tag::magazines'),
    fit: this.ts.translate('Bee::Template::Tag::fit'),
    buy: this.ts.translate('Bee::Template::Tag::buy'),
    share: this.ts.translate('Bee::Template::Tag::share'),
    size: this.ts.translate('Bee::Template::Tag::size'),
    hamsters: this.ts.translate('Bee::Template::Tag::hamsters'),
    'pet owner': this.ts.translate('Bee::Template::Tag::pet owner'),
    paw: this.ts.translate('Bee::Template::Tag::paw'),
    birds: this.ts.translate('Bee::Template::Tag::birds'),
    customes: this.ts.translate('Bee::Template::Tag::customes'),
    february: this.ts.translate('Bee::Template::Tag::february'),
    sunday: this.ts.translate('Bee::Template::Tag::sunday'),
    bear: this.ts.translate('Bee::Template::Tag::bear'),
    'super bowl 2022': this.ts.translate('Bee::Template::Tag::super bowl 2022'),
    crowd: this.ts.translate('Bee::Template::Tag::crowd'),
    stadium: this.ts.translate('Bee::Template::Tag::stadium'),
    'singles awareness day': this.ts.translate('Bee::Template::Tag::singles awareness day'),
    'dental care': this.ts.translate('Bee::Template::Tag::dental care'),
    'beautiful smiles': this.ts.translate('Bee::Template::Tag::beautiful smiles'),
    dentists: this.ts.translate('Bee::Template::Tag::dentists'),
    milan: this.ts.translate('Bee::Template::Tag::milan'),
    'london fashion week': this.ts.translate('Bee::Template::Tag::london fashion week'),
    '#stickers': this.ts.translate('Bee::Template::Tag::#stickers'),
    '#art': this.ts.translate('Bee::Template::Tag::#art'),
    '#project': this.ts.translate('Bee::Template::Tag::#project'),
    '#creative': this.ts.translate('Bee::Template::Tag::#creative'),
    '#colorful': this.ts.translate('Bee::Template::Tag::#colorful'),
    '#event': this.ts.translate('Bee::Template::Tag::#event'),
    '#nationalstickerday': this.ts.translate('Bee::Template::Tag::#nationalstickerday'),
    downloads: this.ts.translate('Bee::Template::Tag::downloads'),
    'world book day': this.ts.translate('Bee::Template::Tag::world book day'),
    "children's books": this.ts.translate("Bee::Template::Tag::children's books"),
    'travel & leisure': this.ts.translate('Bee::Template::Tag::travel & leisure'),
    shamrocks: this.ts.translate('Bee::Template::Tag::shamrocks'),
    perfume: this.ts.translate('Bee::Template::Tag::perfume'),
    "women's day": this.ts.translate("Bee::Template::Tag::women's day"),
    'book day': this.ts.translate('Bee::Template::Tag::book day'),
    "woman's day": this.ts.translate("Bee::Template::Tag::woman's day"),
    'good hair day': this.ts.translate('Bee::Template::Tag::good hair day'),
    sister: this.ts.translate('Bee::Template::Tag::sister'),
    girlfriends: this.ts.translate('Bee::Template::Tag::girlfriends'),
    parfurm: this.ts.translate('Bee::Template::Tag::parfurm'),
    wife: this.ts.translate('Bee::Template::Tag::wife'),
    'take action': this.ts.translate('Bee::Template::Tag::take action'),
    cleanup: this.ts.translate('Bee::Template::Tag::cleanup'),
    womanday: this.ts.translate('Bee::Template::Tag::womanday'),
    snack: this.ts.translate('Bee::Template::Tag::snack'),
    coctails: this.ts.translate('Bee::Template::Tag::coctails'),
    "sant patrick's day": this.ts.translate("Bee::Template::Tag::sant patrick's day"),
    saintpatricks: this.ts.translate('Bee::Template::Tag::saintpatricks'),
    'food deliver': this.ts.translate('Bee::Template::Tag::food deliver'),
    climate: this.ts.translate('Bee::Template::Tag::climate'),
    'march 8': this.ts.translate('Bee::Template::Tag::march 8'),
    "don't waste water": this.ts.translate("Bee::Template::Tag::don't waste water"),
    cinematographic: this.ts.translate('Bee::Template::Tag::cinematographic'),
    plastic: this.ts.translate('Bee::Template::Tag::plastic'),
    'plastic pollution': this.ts.translate('Bee::Template::Tag::plastic pollution'),
    'no planet b': this.ts.translate('Bee::Template::Tag::no planet b'),
    change: this.ts.translate('Bee::Template::Tag::change'),
    'online delivery': this.ts.translate('Bee::Template::Tag::online delivery'),
    meal: this.ts.translate('Bee::Template::Tag::meal'),
    'national nutrition month': this.ts.translate('Bee::Template::Tag::national nutrition month'),
    meals: this.ts.translate('Bee::Template::Tag::meals'),
    mother: this.ts.translate('Bee::Template::Tag::mother'),
    ghd: this.ts.translate('Bee::Template::Tag::ghd'),
    'hair day': this.ts.translate('Bee::Template::Tag::hair day'),
    'healthy eating': this.ts.translate('Bee::Template::Tag::healthy eating'),
    'water day': this.ts.translate('Bee::Template::Tag::water day'),
    'saint patricks': this.ts.translate('Bee::Template::Tag::saint patricks'),
    ofer: this.ts.translate('Bee::Template::Tag::ofer'),
    "st patrick's": this.ts.translate("Bee::Template::Tag::st patrick's"),
    stew: this.ts.translate('Bee::Template::Tag::stew'),
    ukraine: this.ts.translate('Bee::Template::Tag::ukraine'),
    war: this.ts.translate('Bee::Template::Tag::war'),
    russia: this.ts.translate('Bee::Template::Tag::russia'),
    aid: this.ts.translate('Bee::Template::Tag::aid'),
    crowdfunding: this.ts.translate('Bee::Template::Tag::crowdfunding'),
    'world peace': this.ts.translate('Bee::Template::Tag::world peace'),
    'barber shop': this.ts.translate('Bee::Template::Tag::barber shop'),
    'hair stylish': this.ts.translate('Bee::Template::Tag::hair stylish'),
    'hair studio': this.ts.translate('Bee::Template::Tag::hair studio'),
    sanitation: this.ts.translate('Bee::Template::Tag::sanitation'),
    action: this.ts.translate('Bee::Template::Tag::action'),
    turtles: this.ts.translate('Bee::Template::Tag::turtles'),
    planet: this.ts.translate('Bee::Template::Tag::planet'),
    'helping  ukraine': this.ts.translate('Bee::Template::Tag::helping  ukraine'),
    'stop war': this.ts.translate('Bee::Template::Tag::stop war'),
    'support ukraine': this.ts.translate('Bee::Template::Tag::support ukraine'),
    'stand with ukrine': this.ts.translate('Bee::Template::Tag::stand with ukrine'),
    'stan with peace': this.ts.translate('Bee::Template::Tag::stan with peace'),
    'stand with ukraine': this.ts.translate('Bee::Template::Tag::stand with ukraine'),
    'no war': this.ts.translate('Bee::Template::Tag::no war'),
    'against war': this.ts.translate('Bee::Template::Tag::against war'),
    'help for refugees': this.ts.translate('Bee::Template::Tag::help for refugees'),
    'help ukraine': this.ts.translate('Bee::Template::Tag::help ukraine'),
    'stand for ukraine': this.ts.translate('Bee::Template::Tag::stand for ukraine'),
    lifestile: this.ts.translate('Bee::Template::Tag::lifestile'),
    dietitian: this.ts.translate('Bee::Template::Tag::dietitian'),
    diet: this.ts.translate('Bee::Template::Tag::diet'),
    'seasonal vegetables': this.ts.translate('Bee::Template::Tag::seasonal vegetables'),
    'healty food': this.ts.translate('Bee::Template::Tag::healty food'),
    nutritionist: this.ts.translate('Bee::Template::Tag::nutritionist'),
    healty: this.ts.translate('Bee::Template::Tag::healty'),
    gadgets: this.ts.translate('Bee::Template::Tag::gadgets'),
    'april fools': this.ts.translate('Bee::Template::Tag::april fools'),
    gizmos: this.ts.translate('Bee::Template::Tag::gizmos'),
    "april's fools": this.ts.translate("Bee::Template::Tag::april's fools"),
    "april's fool": this.ts.translate("Bee::Template::Tag::april's fool"),
    footwear: this.ts.translate('Bee::Template::Tag::footwear'),
    'world art day': this.ts.translate('Bee::Template::Tag::world art day'),
    'animal care': this.ts.translate('Bee::Template::Tag::animal care'),
    petday: this.ts.translate('Bee::Template::Tag::petday'),
    teacher: this.ts.translate('Bee::Template::Tag::teacher'),
    teachingcompany: this.ts.translate('Bee::Template::Tag::teachingcompany'),
    nationalgardenmonth: this.ts.translate('Bee::Template::Tag::nationalgardenmonth'),
    prune: this.ts.translate('Bee::Template::Tag::prune'),
    cultivation: this.ts.translate('Bee::Template::Tag::cultivation'),
    gardener: this.ts.translate('Bee::Template::Tag::gardener'),
    'pet store': this.ts.translate('Bee::Template::Tag::pet store'),
    'national pet day': this.ts.translate('Bee::Template::Tag::national pet day'),
    'gallery event': this.ts.translate('Bee::Template::Tag::gallery event'),
    'pet day': this.ts.translate('Bee::Template::Tag::pet day'),
    'free shipping': this.ts.translate('Bee::Template::Tag::free shipping'),
    'house plants': this.ts.translate('Bee::Template::Tag::house plants'),
    'garden month': this.ts.translate('Bee::Template::Tag::garden month'),
    'national garden month': this.ts.translate('Bee::Template::Tag::national garden month'),
    'plant shop': this.ts.translate('Bee::Template::Tag::plant shop'),
    'indoor plants': this.ts.translate('Bee::Template::Tag::indoor plants'),
    'pet shelter': this.ts.translate('Bee::Template::Tag::pet shelter'),
    'energy company': this.ts.translate('Bee::Template::Tag::energy company'),
    'green energy': this.ts.translate('Bee::Template::Tag::green energy'),
    educa: this.ts.translate('Bee::Template::Tag::educa'),
    sculpture: this.ts.translate('Bee::Template::Tag::sculpture'),
    painting: this.ts.translate('Bee::Template::Tag::painting'),
    'learning online': this.ts.translate('Bee::Template::Tag::learning online'),
    reduce: this.ts.translate('Bee::Template::Tag::reduce'),
    'zero waste': this.ts.translate('Bee::Template::Tag::zero waste'),
    ecology: this.ts.translate('Bee::Template::Tag::ecology'),
    'respect the nature': this.ts.translate('Bee::Template::Tag::respect the nature'),
    recycle: this.ts.translate('Bee::Template::Tag::recycle'),
    reuse: this.ts.translate('Bee::Template::Tag::reuse'),
    'green stuff': this.ts.translate('Bee::Template::Tag::green stuff'),
    earthday: this.ts.translate('Bee::Template::Tag::earthday'),
    sustainabilty: this.ts.translate('Bee::Template::Tag::sustainabilty'),
    'national earth day': this.ts.translate('Bee::Template::Tag::national earth day'),
    'world day': this.ts.translate('Bee::Template::Tag::world day'),
    'save the earth': this.ts.translate('Bee::Template::Tag::save the earth'),
    sustainbility: this.ts.translate('Bee::Template::Tag::sustainbility'),
    recycling: this.ts.translate('Bee::Template::Tag::recycling'),
    environmental: this.ts.translate('Bee::Template::Tag::environmental'),
    'clean energy': this.ts.translate('Bee::Template::Tag::clean energy'),
    tableware: this.ts.translate('Bee::Template::Tag::tableware'),
    'pet and animal care': this.ts.translate('Bee::Template::Tag::pet and animal care'),
    'easter decoration': this.ts.translate('Bee::Template::Tag::easter decoration'),
    macarons: this.ts.translate('Bee::Template::Tag::macarons'),
    'plant nursery': this.ts.translate('Bee::Template::Tag::plant nursery'),
    'talent acquisition': this.ts.translate('Bee::Template::Tag::talent acquisition'),
    'job listing': this.ts.translate('Bee::Template::Tag::job listing'),
    'job offer': this.ts.translate('Bee::Template::Tag::job offer'),
    hr: this.ts.translate('Bee::Template::Tag::hr'),
    'human resources': this.ts.translate('Bee::Template::Tag::human resources'),
    posh: this.ts.translate('Bee::Template::Tag::posh'),
    recruiter: this.ts.translate('Bee::Template::Tag::recruiter'),
    'job application': this.ts.translate('Bee::Template::Tag::job application'),
    'start date': this.ts.translate('Bee::Template::Tag::start date'),
    heroes: this.ts.translate('Bee::Template::Tag::heroes'),
    illustrations: this.ts.translate('Bee::Template::Tag::illustrations'),
    'glass of wine': this.ts.translate('Bee::Template::Tag::glass of wine'),
    'wine maker': this.ts.translate('Bee::Template::Tag::wine maker'),
    tastings: this.ts.translate('Bee::Template::Tag::tastings'),
    beverages: this.ts.translate('Bee::Template::Tag::beverages'),
    tasting: this.ts.translate('Bee::Template::Tag::tasting'),
    'digital news': this.ts.translate('Bee::Template::Tag::digital news'),
    observance: this.ts.translate('Bee::Template::Tag::observance'),
    'raising awareness': this.ts.translate('Bee::Template::Tag::raising awareness'),
    giving: this.ts.translate('Bee::Template::Tag::giving'),
    waitangi: this.ts.translate('Bee::Template::Tag::waitangi'),
    'english authors': this.ts.translate('Bee::Template::Tag::english authors'),
    bard: this.ts.translate('Bee::Template::Tag::bard'),
    'shakespeare day': this.ts.translate('Bee::Template::Tag::shakespeare day'),
    'healthy life': this.ts.translate('Bee::Template::Tag::healthy life'),
    'shop local': this.ts.translate('Bee::Template::Tag::shop local'),
    'top posts': this.ts.translate('Bee::Template::Tag::top posts'),
    'World Art Day': this.ts.translate('Bee::Template::Tag::World Art Day'),
    'first week': this.ts.translate('Bee::Template::Tag::first week'),
    'Human resources newsletter': this.ts.translate('Bee::Template::Tag::Human resources newsletter'),
    gardens: this.ts.translate('Bee::Template::Tag::gardens'),
    'veteran stories': this.ts.translate('Bee::Template::Tag::veteran stories'),
    'gun violence': this.ts.translate('Bee::Template::Tag::gun violence'),
    restaurants: this.ts.translate('Bee::Template::Tag::restaurants'),
    grocer: this.ts.translate('Bee::Template::Tag::grocer'),
    Food: this.ts.translate('Bee::Template::Tag::Food'),
    produce: this.ts.translate('Bee::Template::Tag::produce'),
    campaigns: this.ts.translate('Bee::Template::Tag::campaigns'),
    'Gun violence': this.ts.translate('Bee::Template::Tag::Gun violence'),
    coins: this.ts.translate('Bee::Template::Tag::coins'),
    'market trends': this.ts.translate('Bee::Template::Tag::market trends'),
    'data share': this.ts.translate('Bee::Template::Tag::data share'),
    'year end': this.ts.translate('Bee::Template::Tag::year end'),
    veterinarian: this.ts.translate('Bee::Template::Tag::veterinarian'),
    'parent communications': this.ts.translate('Bee::Template::Tag::parent communications'),
    Education: this.ts.translate('Bee::Template::Tag::Education'),
    drums: this.ts.translate('Bee::Template::Tag::drums'),
    guitars: this.ts.translate('Bee::Template::Tag::guitars'),
    'labor day sale': this.ts.translate('Bee::Template::Tag::labor day sale'),
    records: this.ts.translate('Bee::Template::Tag::records'),
    'Student Recruitment': this.ts.translate('Bee::Template::Tag::Student Recruitment'),
    Events: this.ts.translate('Bee::Template::Tag::Events'),
    College: this.ts.translate('Bee::Template::Tag::College'),
    'Campus Tours': this.ts.translate('Bee::Template::Tag::Campus Tours'),
    University: this.ts.translate('Bee::Template::Tag::University'),
    'Higher Education': this.ts.translate('Bee::Template::Tag::Higher Education'),
    'apply now': this.ts.translate('Bee::Template::Tag::apply now'),
    'academic advising': this.ts.translate('Bee::Template::Tag::academic advising'),
    'student recruitment': this.ts.translate('Bee::Template::Tag::student recruitment'),
    'Get followers': this.ts.translate('Bee::Template::Tag::Get followers'),
    'brand experience': this.ts.translate('Bee::Template::Tag::brand experience'),
    'Car rental': this.ts.translate('Bee::Template::Tag::Car rental'),
    'Online Classes': this.ts.translate('Bee::Template::Tag::Online Classes'),
    admission: this.ts.translate('Bee::Template::Tag::admission'),
    'Life on campus': this.ts.translate('Bee::Template::Tag::Life on campus'),
    'Year End  Celebration': this.ts.translate('Bee::Template::Tag::Year End  Celebration'),
    'school spirit': this.ts.translate('Bee::Template::Tag::school spirit'),
    engagment: this.ts.translate('Bee::Template::Tag::engagment'),
    'holiday party': this.ts.translate('Bee::Template::Tag::holiday party'),
    'Ugly sweater': this.ts.translate('Bee::Template::Tag::Ugly sweater'),
    banking: this.ts.translate('Bee::Template::Tag::banking'),
    'Year in Review': this.ts.translate('Bee::Template::Tag::Year in Review'),
    'company overvierw': this.ts.translate('Bee::Template::Tag::company overvierw'),
    'clean water': this.ts.translate('Bee::Template::Tag::clean water'),
    'World Water Day': this.ts.translate('Bee::Template::Tag::World Water Day'),
    'waste water': this.ts.translate('Bee::Template::Tag::waste water'),
    'read a book day': this.ts.translate('Bee::Template::Tag::read a book day'),
    fiction: this.ts.translate('Bee::Template::Tag::fiction'),
    Football: this.ts.translate('Bee::Template::Tag::Football'),
    'Viewing party': this.ts.translate('Bee::Template::Tag::Viewing party'),
    'sports bars': this.ts.translate('Bee::Template::Tag::sports bars'),
    'Super Bowl': this.ts.translate('Bee::Template::Tag::Super Bowl'),
    NFL: this.ts.translate('Bee::Template::Tag::NFL'),
    'Chinese New Year': this.ts.translate('Bee::Template::Tag::Chinese New Year'),
    Promotion: this.ts.translate('Bee::Template::Tag::Promotion'),
    'Lunar New Year': this.ts.translate('Bee::Template::Tag::Lunar New Year'),
    Fashion: this.ts.translate('Bee::Template::Tag::Fashion'),
    Discount: this.ts.translate('Bee::Template::Tag::Discount'),
    'Chinese new year': this.ts.translate('Bee::Template::Tag::Chinese new year'),
    'asian food': this.ts.translate('Bee::Template::Tag::asian food'),
    'year of the rabbit': this.ts.translate('Bee::Template::Tag::year of the rabbit'),
    'smart watches': this.ts.translate('Bee::Template::Tag::smart watches'),
    cameras: this.ts.translate('Bee::Template::Tag::cameras'),
    'cell phones': this.ts.translate('Bee::Template::Tag::cell phones'),
    tablets: this.ts.translate('Bee::Template::Tag::tablets'),
    mandarin: this.ts.translate('Bee::Template::Tag::mandarin'),
    cantonese: this.ts.translate('Bee::Template::Tag::cantonese'),
    'remote learning': this.ts.translate('Bee::Template::Tag::remote learning'),
    'Online classes': this.ts.translate('Bee::Template::Tag::Online classes'),
    'calligraphy tutorials': this.ts.translate('Bee::Template::Tag::calligraphy tutorials'),
    'cultural discovery': this.ts.translate('Bee::Template::Tag::cultural discovery'),
    'language learning': this.ts.translate('Bee::Template::Tag::language learning'),
    'rear of the rabbit': this.ts.translate('Bee::Template::Tag::rear of the rabbit'),
    'cell phone': this.ts.translate('Bee::Template::Tag::cell phone'),
    'skin and hair care': this.ts.translate('Bee::Template::Tag::skin and hair care'),
    mlk: this.ts.translate('Bee::Template::Tag::mlk'),
    'college newsletter': this.ts.translate('Bee::Template::Tag::college newsletter'),
    'open house': this.ts.translate('Bee::Template::Tag::open house'),
    'university newsletter': this.ts.translate('Bee::Template::Tag::university newsletter'),
    'life on campus': this.ts.translate('Bee::Template::Tag::life on campus'),
    "st patrick's day": this.ts.translate("Bee::Template::Tag::st patrick's day"),
    "st patrick's day,": this.ts.translate("Bee::Template::Tag::st patrick's day,"),
    'st patricks day,': this.ts.translate('Bee::Template::Tag::st patricks day,'),
    'alumni engagement': this.ts.translate('Bee::Template::Tag::alumni engagement'),
    fundrising: this.ts.translate('Bee::Template::Tag::fundrising'),
    'company newsletter': this.ts.translate('Bee::Template::Tag::company newsletter'),
    ramadan: this.ts.translate('Bee::Template::Tag::ramadan'),
    reminders: this.ts.translate('Bee::Template::Tag::reminders'),
    'company announcement': this.ts.translate('Bee::Template::Tag::company announcement'),
    announcements: this.ts.translate('Bee::Template::Tag::announcements'),
    'employee communication': this.ts.translate('Bee::Template::Tag::employee communication'),
    'transgender rights': this.ts.translate('Bee::Template::Tag::transgender rights'),
    "Mother's Day": this.ts.translate("Bee::Template::Tag::Mother's Day"),
    "Valentine's Day": this.ts.translate("Bee::Template::Tag::Valentine's Day"),
    'EMPLOYEE COMMUNICATION': this.ts.translate('Bee::Template::Tag::EMPLOYEE COMMUNICATION'),
    BUSINESS: this.ts.translate('Bee::Template::Tag::BUSINESS'),
    'COMPANY ANNOUNCEMENT': this.ts.translate('Bee::Template::Tag::COMPANY ANNOUNCEMENT'),
    'COMPANY NEWSLETTER': this.ts.translate('Bee::Template::Tag::COMPANY NEWSLETTER'),
    'MOTHER’S DAY': this.ts.translate('Bee::Template::Tag::MOTHER’S DAY'),
    ANNOUNCEMENTS: this.ts.translate('Bee::Template::Tag::ANNOUNCEMENTS'),
    'MOTHERS DAY': this.ts.translate('Bee::Template::Tag::MOTHERS DAY'),
    'company announcements': this.ts.translate('Bee::Template::Tag::company announcements'),
    'internal communications': this.ts.translate('Bee::Template::Tag::internal communications'),
    'company reminders': this.ts.translate('Bee::Template::Tag::company reminders'),
    'monthly newsletters': this.ts.translate('Bee::Template::Tag::monthly newsletters'),
    'company updates': this.ts.translate('Bee::Template::Tag::company updates'),
    newsletters: this.ts.translate('Bee::Template::Tag::newsletters'),
    'company newsletters': this.ts.translate('Bee::Template::Tag::company newsletters'),
    passover: this.ts.translate('Bee::Template::Tag::passover'),
    'Asian Pacific American Heritage Month': this.ts.translate('Bee::Template::Tag::Asian Pacific American Heritage Month'),
    AAPI: this.ts.translate('Bee::Template::Tag::AAPI'),
    'Company Newsletter': this.ts.translate('Bee::Template::Tag::Company Newsletter'),
    'Monthly Newsletters': this.ts.translate('Bee::Template::Tag::Monthly Newsletters'),
    Newsletters: this.ts.translate('Bee::Template::Tag::Newsletters'),
    'Internal Communication': this.ts.translate('Bee::Template::Tag::Internal Communication'),
    'Company Announcements': this.ts.translate('Bee::Template::Tag::Company Announcements'),
    'Company Updates': this.ts.translate('Bee::Template::Tag::Company Updates'),
    'Employee Communication': this.ts.translate('Bee::Template::Tag::Employee Communication'),
    'Company Reminders': this.ts.translate('Bee::Template::Tag::Company Reminders'),
    Business: this.ts.translate('Bee::Template::Tag::Business'),
    'Company Newsletters': this.ts.translate('Bee::Template::Tag::Company Newsletters'),
    'Memorial Day': this.ts.translate('Bee::Template::Tag::Memorial Day'),
    'Pride Month': this.ts.translate('Bee::Template::Tag::Pride Month'),
    'Transgender rights': this.ts.translate('Bee::Template::Tag::Transgender rights'),
    Pride: this.ts.translate('Bee::Template::Tag::Pride'),
    Juneteenth: this.ts.translate('Bee::Template::Tag::Juneteenth'),
    'delivery note,': this.ts.translate('Bee::Template::Tag::delivery note,'),
    'Black Friday': this.ts.translate('Bee::Template::Tag::Black Friday'),
    tradeshow: this.ts.translate('Bee::Template::Tag::tradeshow')
  };
  collectionTranslations = {
    'real-estate': this.ts.translate('Bee::Template::Collection::Real Estate'),
    'e-commerce': this.ts.translate('Bee::Template::Collection::E-commerce'),
    transactional: this.ts.translate('Bee::Template::Collection::Transactional'),
    music: this.ts.translate('Bee::Template::Collection::Music'),
    event: this.ts.translate('Bee::Template::Collection::Event'),
    photography: this.ts.translate('Bee::Template::Collection::Photography'),
    'human-resources': this.ts.translate('Bee::Template::Collection::Human Resources'),
    'welcome-series': this.ts.translate('Bee::Template::Collection::Welcome Series'),
    educational: this.ts.translate('Bee::Template::Collection::Education'),
    'health-wellness': this.ts.translate('Bee::Template::Collection::Health & Wellness'),
    product_launch: this.ts.translate('Bee::Template::Collection::Product Launch'),
    webinar: this.ts.translate('Bee::Template::Collection::Webinar'),
    'fashion-brand-retail': this.ts.translate('Bee::Template::Collection::Fashion Brand Retail'),
    'higher-education': this.ts.translate('Bee::Template::Collection::Higher Education'),
    recruitment: this.ts.translate('Bee::Template::Collection::Recruitment'),
    'e-book-promotion': this.ts.translate('Bee::Template::Collection::E-book Promotion'),
    'internal-communications': this.ts.translate('Bee::Template::Collection::Internal Communications'),
    'hair-salon': this.ts.translate('Bee::Template::Collection::Hair Salon'),
    'survey-campaign': this.ts.translate('Bee::Template::Collection::Survey Campaign')
  };
  categoryTranslations = {
    others: this.ts.translate('Bee::Template::Category::Others'),
    usage: this.ts.translate('Bee::Template::Category::Usage'),
    events: this.ts.translate('Bee::Template::Category::Events'),
    'personal-note': this.ts.translate('Bee::Template::Category::Personal Note'),
    marketing_and_design: this.ts.translate('Bee::Template::Collection::Marketing and Design'),
    'beauty-cosmetics': this.ts.translate('Bee::Template::Category::Beauty & Personal Care'),
    'product-launch': this.ts.translate('Bee::Template::Category::Product Launch'),
    'product-promotion': this.ts.translate('Bee::Template::Category::Product Promotion'),
    seasonal: this.ts.translate('Bee::Template::Category::Seasonal'),
    'seasonal-promotion': this.ts.translate('Bee::Template::Category::Seasonal Promotion'),
    'news-blog-and-magazines': this.ts.translate('Bee::Template::Category::News, Blog & Magazines'),
    'business-services': this.ts.translate('Bee::Template::Category::Business Services'),
    newsletter: this.ts.translate('Bee::Template::Category::Newsletter'),
    'service-promotion': this.ts.translate('Bee::Template::Category::Service Promotion'),
    'health-wellness': this.ts.translate('Bee::Template::Category::Health and Wellness'),
    'non-profit': this.ts.translate('Bee::Template::Category::Non Profit'),
    tutorial: this.ts.translate('Bee::Template::Category::Tutorial'),
    'real-estate': this.ts.translate('Bee::Template::Category::Real Estate'),
    'computer-internet': this.ts.translate('Bee::Template::Category::Software and Technology'),
    electronics: this.ts.translate('Bee::Template::Category::Electronics'),
    'travel-leisure': this.ts.translate('Bee::Template::Category::Travel'),
    automated: this.ts.translate('Bee::Template::Category::Automated'),
    notification: this.ts.translate('Bee::Template::Category::Notification'),
    education: this.ts.translate('Bee::Template::Category::Education'),
    'marketing-and-design': this.ts.translate('Bee::Template::Category::Marketing & Design'),
    'media-entertainment': this.ts.translate('Bee::Template::Category::Media & Entertainment'),
    transactional: this.ts.translate('Bee::Template::Category::Transactional'),
    'small-business': this.ts.translate('Bee::Template::Category::Small Business'),
    christmas: this.ts.translate('Bee::Template::Category::Christmas'),
    holiday: this.ts.translate('Bee::Template::Category::Holiday'),
    fashion: this.ts.translate('Bee::Template::Category::Fashion'),
    'new-year': this.ts.translate('Bee::Template::Category::New Year'),
    'food-beverage': this.ts.translate('Bee::Template::Category::Food and Beverage'),
    automotive: this.ts.translate('Bee::Template::Category::Automotive'),
    hanukkah: this.ts.translate('Bee::Template::Category::Hanukkah'),
    activation: this.ts.translate('Bee::Template::Category::Activation'),
    'pets-and-animal-care': this.ts.translate('Bee::Template::Category::Pets and Animal Care'),
    'black-friday': this.ts.translate('Bee::Template::Category::Black Friday'),
    sports: this.ts.translate('Bee::Template::Category::Sports'),
    'apologize-email': this.ts.translate('Bee::Template::Category::Apologize'),
    confirmation: this.ts.translate('Bee::Template::Category::Confirmation'),
    halloween: this.ts.translate('Bee::Template::Category::Halloween'),
    'cyber-monday': this.ts.translate('Bee::Template::Category::Cyber Monday'),
    'apps-download': this.ts.translate('Bee::Template::Category::Apps & Downloads'),
    'mystery-email': this.ts.translate('Bee::Template::Category::Mystery'),
    welcome: this.ts.translate('Bee::Template::Category::Welcome'),
    thanksgiving: this.ts.translate('Bee::Template::Category::Thanksgiving'),
    'thank-you': this.ts.translate('Bee::Template::Category::Thank You'),
    'abandoned-cart': this.ts.translate('Bee::Template::Category::Abandoned Cart'),
    'financial-money': this.ts.translate('Bee::Template::Category::Finance'),
    'giving-tuesday': this.ts.translate('Bee::Template::Category::Giving Tuesday'),
    engagement: this.ts.translate('Bee::Template::Category::Engagement'),
    annual_review: this.ts.translate('Bee::Template::Category::Annual Review'),
    teaser: this.ts.translate('Bee::Template::Category::Teaser'),
    home_garden: this.ts.translate('Bee::Template::Category::Home Goods'),
    survey: this.ts.translate('Bee::Template::Category::Survey'),
    gaming: this.ts.translate('Bee::Template::Category::Gaming'),
    'valentines-day': this.ts.translate('Bee::Template::Category::Valentine’s Day'),
    'st-patricks-day': this.ts.translate('Bee::Template::Category::St. Patrick’s Day'),
    'chinese-new-year': this.ts.translate('Bee::Template::Category::Chinese New Year'),
    easter: this.ts.translate('Bee::Template::Category::Easter'),
    spring: this.ts.translate('Bee::Template::Category::Spring'),
    'earth-day': this.ts.translate('Bee::Template::Category::Earth Day'),
    'mothers-day': this.ts.translate('Bee::Template::Category::Mother’s Day'),
    'fathers-day': this.ts.translate('Bee::Template::Category::Father’s Day'),
    'lgbtq-pride-month': this.ts.translate('Bee::Template::Category::LGBTQ Pride Month'),
    'back-to-school': this.ts.translate('Bee::Template::Category::Back to School'),
    'mardi-gras': this.ts.translate('Bee::Template::Category::Mardi Gras'),
    'april-fools-day': this.ts.translate('Bee::Template::Category::April Fool’s Day'),
    'cinco-de-mayo': this.ts.translate('Bee::Template::Category::Cinco De Mayo'),
    'memorial-day': this.ts.translate('Bee::Template::Category::Memorial Day'),
    'independence-day': this.ts.translate('Bee::Template::Category::Independence Day'),
    'labor-day': this.ts.translate('Bee::Template::Category::Labor Day'),
    fall: this.ts.translate('Bee::Template::Category::Fall'),
    'breast-cancer': this.ts.translate('Bee::Template::Category::Breast Cancer'),
    winter: this.ts.translate('Bee::Template::Category::Winter'),
    'fashion-week': this.ts.translate('Bee::Template::Category::Fashion Week'),
    arts: this.ts.translate('Bee::Template::Category::Arts'),
    'black-history-month': this.ts.translate('Bee::Template::Category::Black History Month'),
    'book-lovers-day': this.ts.translate('Bee::Template::Category::Book Lover’s Day'),
    'cv-resumes': this.ts.translate('Bee::Template::Category::CV & Resumes'),
    delivery: this.ts.translate('Bee::Template::Category::Delivery'),
    'get-to-know-your-customer': this.ts.translate('Bee::Template::Category::Get To Know Your Customer'),
    culture: this.ts.translate('Bee::Template::Category::Culture'),
    animated: this.ts.translate('Bee::Template::Category::Animated'),
    'password-reset': this.ts.translate('Bee::Template::Category::Password Reset'),
    announcement: this.ts.translate('Bee::Template::Category::Announcement'),
    'weekend-sale': this.ts.translate('Bee::Template::Category::Weekend Sale'),
    luxury: this.ts.translate('Bee::Template::Category::Luxury'),
    music: this.ts.translate('Bee::Template::Category::Music'),
    'e-commerce': this.ts.translate('Bee::Template::Category::E-commerce'),
    webinar: this.ts.translate('Bee::Template::Category::Webinar'),
    legal: this.ts.translate('Bee::Template::Category::Legal'),
    summer: this.ts.translate('Bee::Template::Category::Summer'),
    fundraising: this.ts.translate('Bee::Template::Category::Fundraising'),
    juneteenth: this.ts.translate('Bee::Template::Category::Juneteenth'),
    publishing: this.ts.translate('Bee::Template::Category::Publishing'),
    'instagram-bio': this.ts.translate('Bee::Template::Category::Instagram bio'),
    'greenpeace-day': this.ts.translate('Bee::Template::Category::Greenpeace Day'),
    're-engagement': this.ts.translate('Bee::Template::Category::Re-Engagement'),
    oktoberfest: this.ts.translate('Bee::Template::Category::Oktoberfest'),
    wedding: this.ts.translate('Bee::Template::Category::Wedding'),
    photography: this.ts.translate('Bee::Template::Category::Photography'),
    'dark-mode-optimized': this.ts.translate('Bee::Template::Category::Dark Mode'),
    'human-resources': this.ts.translate('Bee::Template::Category::Human Resources'),
    activism: this.ts.translate('Bee::Template::Category::Activism'),
    'global-observances-celebrations': this.ts.translate('Bee::Template::Category::Global Observances & Celebrations'),
    kwanzaa: this.ts.translate('Bee::Template::Category::Kwanzaa'),
    'the-big-game': this.ts.translate('Bee::Template::Category::The Big Game'),
    industry: this.ts.translate('Bee::Template::Category::Industry')
  };

  constructor(private ts: TranslateService) {}

  findLabelById(type: string, id: string): string {
    let translations: Record<string, string> = {};
    switch (type) {
      case 'category':
        translations = this.categoryTranslations;
        break;
      case 'tag':
        translations = this.tagTranslations;
        break;
      case 'collection':
        translations = this.collectionTranslations;
        break;
    }

    return translations[id] ?? id;
  }
}

import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { RouterTestingModule } from '@angular/router/testing';
import { BeeBaseUtilityService } from './../bee-base-utility.service';
import { ConfigService } from './../../../../services/config.service';
import { SentryService } from './../../../../services/sentry.service';
import { MockProvider } from 'ng-mocks';
import { MarkdownModule, MarkdownService } from 'ngx-markdown';
import { EmailEditorConfigService } from './email-editor-config.service';
import { TranslateService } from '@klicktipp/kt-mat-library';
import { ErrorBaseService } from './../../../../services/error-base.service';

describe('BeeEditorConfigService', () => {
  let service: EmailEditorConfigService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [MarkdownModule.forRoot(), HttpClientTestingModule, RouterTestingModule],
      providers: [
        MarkdownService,
        EmailEditorConfigService,
        BeeBaseUtilityService,
        ErrorBaseService,
        MockProvider(MatDialog, {}, 'useValue'),
        MockProvider(ConfigService, { getConfig: jest.fn(), hasAccess: jest.fn().mockReturnValue(false) }),
        MockProvider(MatDialogRef, {}, 'useValue'),
        MockProvider(TranslateService, { translate: jest.fn().mockReturnValue('translated') }),
        MockProvider(SentryService, {
          captureException: jest.fn(),
          isSentryActive: false
        })
      ]
    });
    service = TestBed.inject(EmailEditorConfigService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});

import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { getTestBed, TestBed } from '@angular/core/testing';

import { UserTemplatesApiService } from './user-templates-api.service';
import { RouteParameterService } from './../../../../services/route-parameter.service';
import { ActivatedRouteMock } from './../../../../mocks';
import { ActivatedRoute } from '@angular/router';

describe('UserTemplatesApiService', () => {
  let injector: TestBed;
  let service: UserTemplatesApiService;
  let httpMock: HttpTestingController;
  const activatedRouteMock: ActivatedRouteMock = new ActivatedRouteMock();

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [UserTemplatesApiService, RouteParameterService, { provide: ActivatedRoute, useValue: activatedRouteMock }]
    });

    injector = getTestBed();
    service = injector.inject(UserTemplatesApiService);
    httpMock = injector.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify(); // verify that there are no outstanding http calls
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});

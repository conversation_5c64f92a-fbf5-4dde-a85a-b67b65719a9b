import { TestBed } from '@angular/core/testing';
import { AutocompleteOption } from '@klicktipp/kt-mat-library';
import { EmailCopyEntity } from '../models/emailBase';
import { EmailSettingsService } from './email-settings.service';

describe('EmailSettingsService', () => {
  let service: EmailSettingsService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [EmailSettingsService]
    });
    service = TestBed.inject(EmailSettingsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should create autocomplete options from EmailCopyEntity array', () => {
    const emailCopyEntities: EmailCopyEntity[] = [
      { id: 1, name: 'Email 1', subject: 'Subject 1', type: 'Type 1' },
      { id: 2, name: 'Email 2', subject: 'Subject 2', type: 'Type 2' }
    ];

    const expectedOptions: AutocompleteOption[] = [
      { value: 1, text: 'Email 1', subtext: 'Subject 1', type: 'Type 1' },
      { value: 2, text: 'Email 2', subtext: 'Subject 2', type: 'Type 2' }
    ];

    const options = service.createAutoCompleteOptions(emailCopyEntities);
    expect(options).toEqual(expectedOptions);
  });
});

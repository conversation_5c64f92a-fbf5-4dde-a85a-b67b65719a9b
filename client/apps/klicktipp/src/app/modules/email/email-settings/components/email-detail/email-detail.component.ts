import { ChangeDetectorRef, Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { AffixItem, KtAffixComponent, KtNavigationService, KtNotificationComponent, LinkItem, TranslateService } from '@klicktipp/kt-mat-library';
import { take } from 'rxjs/operators';
import { RouteParameterService } from '../../../../../services/route-parameter.service';
import { TabTitleService } from '../../../../../services/tab-title.service';
import { CampaignRefreshView } from '../../../../campaign/models/campaignBase';
import { OnReadyBaseComponent } from '../../../../forms.shared/components/on-ready/on-ready-base.component';
import { EmailAffixViews } from '../../general/email-settingsConstants';
import { EmailRouteParams } from '../../models/email-route-params';
import { CampaignUsageItem, EmailDetailData } from '../../models/emailBase';
import { EmailSettingsApiService } from '../../services/api/email-settings-api.service';
import { EmailContentAnalysisComponent } from '../email-content-analysis/email-content-analysis.component';
import { EmailCreateDetailComponent } from '../email-create-detail/email-create-detail.component';
import { EmailDetailOverviewComponent } from '../email-detail-overview/email-detail-overview.component';
import { RichTextEditorComponent } from '../rich-text-editor/rich-text-editor.component';
import { RouterFacade } from '../../../../../+state/router.facade';

@Component({
  selector: 'kt-email-detail',
  templateUrl: './email-detail.component.html',
  imports: [KtNotificationComponent, EmailDetailOverviewComponent, EmailContentAnalysisComponent, EmailCreateDetailComponent, KtAffixComponent, RichTextEditorComponent]
})
export class EmailDetailComponent extends OnReadyBaseComponent implements OnInit {
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly routerFacade = inject(RouterFacade);

  @Output() refresh: EventEmitter<CampaignRefreshView> = new EventEmitter<CampaignRefreshView>();
  emailSettingsMenu = EmailAffixViews;
  view: string;
  emailDetailData: EmailDetailData;
  links: LinkItem[] = [];
  isSms: boolean;
  type: string;
  affixLinks: AffixItem[] = [];

  subComponentInitDone = false;
  baseComponentReady: boolean;
  routeParams: EmailRouteParams;

  // Store campaign usage data to avoid repeated API calls
  campaignUsageData: CampaignUsageItem[] = null;
  statsLoading: boolean;

  private emailService: string;
  private basePath = '/email';

  constructor(
    protected translateService: TranslateService,
    private activatedRoute: ActivatedRoute,
    private emailSettingsApiService: EmailSettingsApiService,
    public routingService: RouteParameterService,
    private ktNavigationService: KtNavigationService,
    private tabTitleService: TabTitleService
  ) {
    super(translateService, 'email-detail');
  }

  ngOnInit(): void {
    const routeData = this.activatedRoute.snapshot.data as EmailRouteParams;
    this.type = routeData.type;
    this.routeParams = { ...routeData };
    this.isSms = routeData.isSms;
    this.emailService = routeData.emailService;

    this.updateInternalRouteParams();
    this.getInitialData();

    this.routingService.routeChanged.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((url) => {
      // do nothing if we route outside
      if (!url || !url?.startsWith(this.basePath) || !this.routingService.affixView) {
        return;
      }
      this.updateInternalRouteParams();
      this.cdr.detectChanges();
    });
  }

  refreshView(_$event: CampaignRefreshView | null): void {
    this.getInitialData();
  }

  routeToClickedAffixItem(item: AffixItem): void {
    this.checkStatsRoute(item.id);

    this.ktNavigationService.routeTo(item.link);
    this.refreshView(null);
  }

  routeToClickedButton($event: string): void {
    this.ktNavigationService.routeTo(this.affixLinks.find((link) => link.id === $event).link);
  }

  protected subComponentInitDoneChanged(ready: boolean): void {
    this.subComponentInitDone = ready;
    this.setReady(this.baseComponentReady && ready);
  }

  protected handleGetDataResult(detailData: EmailDetailData): void {
    this.routingService.currentAffixLinks = detailData.data.localPaths;
    this.affixLinks = detailData.data.localPaths;
    this.emailDetailData = detailData;
    const tabTitle = this.emailDetailData.data.entity.name ?? 'KlickTipp';
    this.tabTitleService.setTabName(`${this.tabTitleService.getTranslationForEntityType(this.emailDetailData.data.entity.type)}${tabTitle}`);
  }

  private getInitialData(): void {
    this.emailSettingsApiService
      .get(this.routingService.currentId, +this.routingService.campaignId, this.emailService)
      .pipe(take(1))
      .subscribe((emailData: EmailDetailData) => {
        this.handleGetDataResult(emailData);
        this.baseComponentReady = true;
        this.cdr.detectChanges();
      });
  }

  private updateInternalRouteParams(): void {
    this.view = this.routingService.affixView;
    if (this.routeParams.getViewFromRoute) {
      this.routeParams = { ...this.routeParams, emailView: this.view };
    }
    this.checkStatsRoute(this.view);
  }

  private checkStatsRoute(id: string): void {
    if (id === 'statistics' && this.campaignUsageData === null && !this.statsLoading) {
      this.statsLoading = true;
      this.routerFacade
        .getUserIdOnce()
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe({
          next: (userId) => {
            // If statistics is clicked and we don't have campaign usage data yet, fetch it
            this.emailSettingsApiService
              .getCampaignUsage(+userId, this.routingService.currentId, this.emailService)
              .pipe(take(1))
              .subscribe((response) => {
                this.campaignUsageData = response.data.campaignUsage;
                this.cdr.detectChanges();
                this.statsLoading = false;
              });
          },
          error: (_error) => {
            this.statsLoading = false;
          }
        });
    }
  }
}

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { RouteParameterService } from '../../../../../services/route-parameter.service';
import { EmailRichTextData, EmailRichTextSaveResponse } from '../../models/emailBase';
import { EmailSettingsBaseApiService } from './email-settings-base-api.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class EmailConfirmationService extends EmailSettingsBaseApiService {
  protected subPath = 'kt-email-confirmation';

  constructor(
    protected httpClient: HttpClient,
    protected routingService: RouteParameterService
  ) {
    super(httpClient, routingService);
  }

  saveRichTextSettings(request: EmailRichTextData, entityId: number, isSms: boolean, campaignId: number): Observable<EmailRichTextSaveResponse> {
    const data: EmailRichTextData = request;
    const options = this.getoptions();
    const url = `${this.apiPath}/${this.subPath}/editor-save/${this.routingService.userId}/${entityId}`;

    return this.httpClient.post<EmailRichTextSaveResponse>(url, data, options);
  }
}

import { Component, EventEmitter, Input, Output } from '@angular/core';

import { faAngleDown, faPlus, faTimes, faTrash } from '@fortawesome/free-solid-svg-icons';

import { isTranslateKey } from './../../../../static/translate';
import { DecisionConfig, EeConditionOptionData } from '../../models/decision-config.interface';
import { ConditionalsService } from '../../services/conditionals.service';
import { EeConditionDisplay, EeSegmentDisplay } from './../../../bee-shared/models/bee-editor/decision-display.interface';
import { TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { EeConditionEntity, FormDataAction, FormDataCondition, FormDataTimeframe } from './../../../bee-shared/models/kt-entity/kt-bee-entity-condition.response';
import { ColDef } from './../../../forms.shared/models/table-columns-def.interface';
import { EntitySelectComponent } from '../../../entities/components/entity-select/entity-select.component';
import { NgClass } from '@angular/common';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { MatMenuModule } from '@angular/material/menu';

@Component({
    selector: 'kt-decision',
    templateUrl: './decision.component.html',
    imports: [MatMenuModule, FaIconComponent, NgClass, EntitySelectComponent, TranslatePipe]
})
export class DecisionComponent {
  @Input() config: DecisionConfig;
  @Input() saveWasRequested = false;
  @Output() validationErrormessages: EventEmitter<string[]> = new EventEmitter<string[]>();

  entityColumnDefs: ColDef[] = [
    { headerName: 'Id', field: 'id', sort: 'desc', width: 120 },
    { headerName: 'Name', field: 'name' }
    // { headerName: 'Typ', field: 'type' },
  ];

  faPlus = faPlus;
  faTrash = faTrash;
  faAngleDown = faAngleDown;
  faTimes = faTimes;

  validationHasRun = false;
  selectTypeMandatory = false;

  constructor(
    private conditionalsService: ConditionalsService,
    private translateService: TranslateService
  ) {}

  validate(): string[] {
    this.validationHasRun = true;
    const result: string[] = [];
    const {
      decision: {
        decision: { segments }
      }
    } = this.config;

    this.selectTypeMandatory = !segments.find((segment) => !!segment.conditions.find((condition) => condition.type && condition.type.length > 0));
    if (this.selectTypeMandatory) {
      result.push(isTranslateKey('conditionals::validation::notypedcondition::Please define at least one condition!'));
    }

    if (segments.find((segment) => !!segment.conditions.find((condition) => condition.hasEntity && !(condition.entity > 0)))) {
      result.push(isTranslateKey('conditionals::validation::conditionmissingentity::Please select an entity!'));
    }
    const errmsgs = result.map((key) => this.translateService.translate(key));
    if (this.saveWasRequested) {
      this.validationErrormessages.emit(errmsgs);
    }
    return errmsgs;
  }

  onSelectEntity(entity: EeConditionEntity, condition: EeConditionDisplay): void {
    condition.entity = entity ? entity.id : 0;
    condition.conditionEntity = entity;
    condition.field = entity?.actions[condition.action];

    if (this.saveWasRequested) {
      this.validate();
    }
  }

  onSelectCondition(formdataCondition: FormDataCondition, condition: EeConditionDisplay): void {
    const { id, hasEntity, op } = formdataCondition;
    Object.assign(condition, { ...condition, condition: id, hasEntity, op, formdataCondition });

    if (!hasEntity) {
      condition.conditionEntity = null;
      condition.entity = 0;
      condition.field = 0;
    }
  }

  onSelectAction(formdataAction: FormDataAction, condition: EeConditionDisplay): void {
    const action = formdataAction.id;
    let { field } = condition;

    if (condition.conditionEntity) {
      field = condition.conditionEntity.actions[action];
    }

    Object.assign(condition, { ...condition, action, field, formdataAction });
  }

  onSelectTimeframe(formdataTimeframe: FormDataTimeframe, condition: EeConditionDisplay): void {
    Object.assign(condition, { ...condition, timeframe: formdataTimeframe.id, value: formdataTimeframe.value, formdataTimeframe });
  }

  onSelectType(option: EeConditionOptionData, condition: EeConditionDisplay): void {
    const conditionDisplay = this.conditionalsService.toDisplayCondition({ ...condition, ...option.default }, option);
    Object.assign(condition, conditionDisplay);

    if (this.selectTypeMandatory) {
      this.validate();
    }
  }

  onAddCondition(segment: EeSegmentDisplay): void {
    segment.conditions.push({} as EeConditionDisplay);
  }

  onDeleteCondition(condition: EeConditionDisplay, segment: EeSegmentDisplay): void {
    segment.conditions = segment.conditions.filter((c) => c !== condition);
    if (segment.conditions.length === 0) {
      this.onAddCondition(segment);
    }
  }

  onDeleteSegment(segment: EeSegmentDisplay): void {
    const { decision } = this.config.decision;
    decision.segments = decision.segments.filter((s) => s !== segment);
    if (this.config.decision.decision.segments.length === 0) {
      this.onAddSegment();
    }
  }

  onAddSegment(): void {
    const segment = this.conditionalsService.getNewSegment();
    const { decision } = this.config.decision;
    decision.segments.push(segment);
  }
}

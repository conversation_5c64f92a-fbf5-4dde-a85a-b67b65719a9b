
import { Component, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { faCheck, faPlus, faTimes } from '@fortawesome/pro-solid-svg-icons';
import {
  KtButtonComponent,
  KtMessageService,
  KtNavigationService,
  KtNotificationComponent,
  KtStaticMessageComponent,
  KtTableComponent,
  NotificationType,
  StaticMessageItem,
  TableBase,
  TableRowData,
  TranslatePipe,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { forkJoin } from 'rxjs';
import { take } from 'rxjs/operators';
import { ConfigService } from '../../../../services/config.service';
import { TagService } from '../../../../services/entities/tag.service';
import { TabTitleService } from '../../../../services/tab-title.service';
import { OnReadyBaseComponent } from '../../../forms.shared/components/on-ready/on-ready-base.component';
import { TagEntity } from '../../../tag/models/tag';
import { SignatureCreateButton, SignatureOverviewResponse, SignatureWeight } from '../../models/signature';
import { SignatureApiService } from '../../services/signature-api.service';
import { SignatureTableConvertService } from '../../services/signature-table-convert.service';

@Component({
  selector: 'kt-signature-overview',
  standalone: true,
  imports: [KtTableComponent, KtNotificationComponent, TranslatePipe, KtButtonComponent, KtStaticMessageComponent],
  templateUrl: './signature-overview.component.html',
})
export class SignatureOverviewComponent extends OnReadyBaseComponent implements OnInit {
  signatureOverviewTableItem: TableBase;
  signatureOverviewResponse: SignatureOverviewResponse;
  faPlus = faPlus;
  faCheck = faCheck;
  faTimes = faTimes;
  hasUnsavedChanges = false;
  cancelButtonText = this.translateService.translate('Signature::Overview::Button::Cancel::Cancel sorting change');
  saveButtonText = this.translateService.translate('Signature::Overview::Button::Save::Save sorting change');
  currentSortedRows: TableRowData[] = [];
  tags: TagEntity[];
  unsavedChangesMessageItem: StaticMessageItem = {
    nodeId: 'unsaved-changes',
    message: this.translateService.translate('Signature::Overview::Message::UnsavedChanges::You have unsaved changes'),
    type: NotificationType.Warn
  };

  constructor(
    protected translateService: TranslateService,
    private signatureApiService: SignatureApiService,
    private signatureTableConvertService: SignatureTableConvertService,
    private routingService: KtNavigationService,
    private ktMessageService: KtMessageService,
    private tabTitleService: TabTitleService,
    private tagService: TagService,
    protected configService: ConfigService
  ) {
    super(translateService, 'signature-overview');
  }

  ngOnInit(): void {
    this.tabTitleService.setTabName(this.translateService.translate('Signature::Overview::TabName::Signature overview'));

    const tag$ = this.tagService.getTags();
    const overview$ = this.signatureApiService.getItems();
    forkJoin([tag$, overview$])
      .pipe(take(1))
      .subscribe(([tags, overviewResponse]) => {
        this.tags = tags;
        this.signatureOverviewResponse = overviewResponse;
        this.signatureOverviewTableItem = this.signatureTableConvertService.createSignatureOverviewTable(overviewResponse.data.entities, this.tags);
        this.setReady(true);
      });
  }

  addSignature(button: SignatureCreateButton): void {
    this.routingService.routeTo(button.createUrl);
  }

  reactToChanges($event: TableRowData[]): void {
    this.hasUnsavedChanges = true;
    this.currentSortedRows = $event;
  }

  resetTableSorting(): void {
    this.signatureOverviewTableItem = this.signatureTableConvertService.createSignatureOverviewTable(this.signatureOverviewResponse.data.entities, this.tags);
    this.hasUnsavedChanges = false;
  }

  saveTableSorting(): void {
    const dataWithNewWeights: SignatureWeight[] = this.createOverviewSaveObject();
    this.signatureApiService
      .saveSortedRowItems({ entities: dataWithNewWeights })
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((data) => {
        this.ktMessageService.success(this.translateService.translate('Signature::Overview::Message::SaveSuccess::Sorting saved successfully'));
        this.signatureOverviewResponse = data;
        this.signatureOverviewTableItem = this.signatureTableConvertService.createSignatureOverviewTable(data.data.entities, this.tags);
        this.hasUnsavedChanges = false;
      });
  }

  createOverviewSaveObject(): SignatureWeight[] {
    return this.currentSortedRows.map((row: TableRowData, index: number) => ({
      id: +row.id,
      // In the backend, the weight for the default signature is 127
      weight: row.customRowCss === 'predefined' ? 127 : index
    }));
  }
}

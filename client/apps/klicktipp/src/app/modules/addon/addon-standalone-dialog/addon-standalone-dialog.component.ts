import { CommonModule, CurrencyPipe, registerLocaleData } from '@angular/common';
import localeDe from '@angular/common/locales/de';
import { Component, LOCALE_ID, OnDestroy, OnInit } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { FaIconComponent, FaLayersComponent } from '@fortawesome/angular-fontawesome';
import { faLinkSimple, faShield } from '@fortawesome/pro-light-svg-icons';
import { faArrowAltCircleLeft, faCheck, faSpinner } from '@fortawesome/pro-regular-svg-icons';
import { faCircle, faLock } from '@fortawesome/pro-solid-svg-icons';
import { KtButtonComponent, KtIconComponent, KtNavigationService, KtSelectComponent, SelectItem, SelectOption, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { Subscription } from 'rxjs';
import { AccountAccess } from '../../../enums/account-access.enum';
import { ErrorItems } from '../../../models/error/custom-error';
import { ConfigService } from '../../../services/config.service';
import { ErrorBaseService } from '../../../services/error-base.service';
import { GlobalSignalsService } from '../../../services/global-signals.service';
import { RouteParameterService } from '../../../services/route-parameter.service';
import { SentryService } from '../../../services/sentry.service';
import { transformTimestamp } from '../helper/timestampconvert.function';
import { AddonAccountDetails, AddonData, AddonDetails, AddonErrorCodes, AddonOption, AddOnUpgradeResult } from '../models/addon';
import { AddonApiService } from '../services/addon.api.service';

registerLocaleData(localeDe);

@Component({
  selector: 'kt-addon-standalone-dialog',
  imports: [MatCardModule, KtButtonComponent, KtIconComponent, FaIconComponent, FaLayersComponent, TranslatePipe, CommonModule, ReactiveFormsModule, KtSelectComponent],
  providers: [CurrencyPipe, { provide: LOCALE_ID, useValue: 'de' }], // Use the German locale for currency
  templateUrl: './addon-standalone-dialog.component.html',
})
export class AddonStandaloneDialogComponent implements OnInit, OnDestroy {
  protected readonly faSpinner = faSpinner;
  protected readonly faArrowAltCircleLeft = faArrowAltCircleLeft;
  protected readonly faCheck = faCheck;
  protected readonly faShield = faShield;
  protected readonly faLock = faLock;
  protected readonly faLinkSimple = faLinkSimple;
  protected readonly faCircle = faCircle;
  upsellCancelButtonText = this.translateService.translate('upsell::button::cancel');
  upsellApplyButtonText = this.translateService.translate('upsell::button::apply');
  redirectButtonText = this.translateService.translate('upsell::button::redirect');
  bottomText = this.translateService.translate('upsell::text::bottom');
  bottomText2 = this.translateService.translate('upsell::text::bottom-text2');
  isAdmin = false;
  upgradeRunning = false;
  addonData: AddonData;
  addonDetailData: AddonDetails;
  addonAccountDetails: AddonAccountDetails;
  addonAccountOptions: AddonOption[] = [];
  totalMonthlyPrice: number;
  paymentNow: number;
  totalFullPrice: number;
  optionPrice: number;
  totalPriceText: string;
  newPriceText: string;
  totalPriceDescText: string;
  form: FormGroup = new FormGroup({});
  optionSelect: SelectItem = {
    id: 'optionSelect',
    labelText: this.translateService.translate('addon::payment::optionSelect::label::select upgrade option'),
    options: []
  };
  isUpsell = true;
  featureText: SafeHtml;
  showRedirectMessage = false;
  redirectMessage = this.translateService.translate('addon::payment::redirect::success::message');
  redirectMessageHeader = this.translateService.translate('addon::payment::redirect::success::header');
  redirectSuccessHeader = this.translateService.translate('addon::payment::redirect::success::smallheader');
  upgradeRunningMessage = this.translateService.translate('addon::payment::upgraderunning::upgrade message');
  loadingDataMessage = this.translateService.translate('addon::payment::loadingdata::loading message');
  productText: string;
  productTier: string;
  optionDescriptionText: string;
  private baseEntity: string;
  private subscriptions: Subscription = new Subscription();
  private redirectTimeout: number | NodeJS.Timeout;

  constructor(
    private translateService: TranslateService,
    private configService: ConfigService,
    private ktNavigationService: KtNavigationService,
    private routeParameterService: RouteParameterService,
    private addonApiService: AddonApiService,
    protected errorBaseService: ErrorBaseService,
    private sanitizer: DomSanitizer,
    private globalSignalsService: GlobalSignalsService,
    private activatedRoute: ActivatedRoute,
    private sentryService: SentryService
  ) {
    this.isAdmin = this.configService.hasAccess(AccountAccess.AdministerKlicktipp);
    this.globalSignalsService.triggerReducedMenu(true);
  }

  ngOnInit(): void {
    this.baseEntity = this.routeParameterService.addonentity;
    this.getData();
    this.subscriptions.add(this.activatedRoute.data.subscribe((data) => (this.isUpsell = !data.downsell)));
  }

  ngOnDestroy(): void {
    this.globalSignalsService.triggerReducedMenu(false);
    if (this.redirectTimeout) {
      clearTimeout(this.redirectTimeout);
    }
    this.subscriptions.unsubscribe();
  }

  upgradeAddon(): void {
    this.upgradeRunning = true;
    const opt = this.addonAccountOptions.find((option) => option.id === this.form.get(this.optionSelect.id).value.value);
    this.addonApiService.upgradeAddon(this.baseEntity, opt).subscribe({
      next: (_addon: AddOnUpgradeResult) => {
        this.successRedirect();
      },
      error: (_err) => {
        this.handleValidationErrors(_err);
      },
      complete: () => {
        this.upgradeRunning = false;
      }
    });
  }

  cancel(): void {
    this.ktNavigationService.routeTo(this.addonData.data.details.redirectSuccess);
  }

  selectionChanged(option: SelectOption): void {
    const foundOpt = this.addonAccountOptions.find((opt) => opt.id === option.value);
    this.totalMonthlyPrice = foundOpt.newPriceMonthly;
    this.paymentNow = foundOpt.instantPayment;
    this.totalFullPrice = foundOpt.newPrice;
    this.optionPrice = foundOpt.price;
    this.newPriceText = this.translateService.translate('addon::totalPrice::your new price (monthly)');
    this.totalPriceText = this.translateService.translate('addon::totalPrice::you will pay until @@date@@', {
      date: transformTimestamp(this.addonData.data.accountDetails.nextPaymentDate)
    });
    this.totalPriceDescText = this.translateService.translate('addon::totalPrice::invoice for your @@interval@@', { interval: this.addonData.data.accountDetails.term });
    this.optionDescriptionText = foundOpt.description;
  }

  redirect(): void {
    this.ktNavigationService.routeTo(this.addonData.data.details.redirectSuccess);
  }

  private getData(): void {
    this.addonApiService.getAddonDetails(this.baseEntity).subscribe((data) => {
      this.addonData = data;
      this.addonDetailData = data.data.details;
      this.addonAccountDetails = data.data.accountDetails;
      this.addonAccountOptions = data.data.options;
      this.optionSelect.options = this.addonAccountOptions.map((option) => {
        return { value: option.id, text: option.name };
      });
      // keep this separate as sanitizer will not work on the fly with concatenated or manipulated strings
      const translatedFeatures = this.translateService.translate(this.addonDetailData.features);
      this.featureText = this.sanitizer.bypassSecurityTrustHtml(translatedFeatures);
      this.productText = this.translateService.translate('addon::payment::productinfo::productText @@product@@', { product: this.addonData.data.accountDetails.productName });
      this.productTier = this.translateService.translate('addon::payment::productinfo::productTier @@tier@@', { tier: this.addonData.data.accountDetails.tier });
      setTimeout(() => {
        this.form.get(this.optionSelect.id)?.setValue(this.optionSelect.options[0]);
      });
    });
  }

  private successRedirect(): void {
    this.showRedirectMessage = true;
    this.redirectTimeout = setTimeout(() => {
      this.ktNavigationService.routeTo(this.addonData.data.details.redirectSuccess);
    }, 20000);
  }

  private handleValidationErrors(err: ErrorItems): void {
    this.form.markAllAsTouched();
    if (err instanceof ErrorItems) {
      const errors: ErrorItems = new ErrorItems([]);
      for (const errorItem of err.errors) {
        let addError = true;
        switch (errorItem.code) {
          case AddonErrorCodes.UpgradeFailed:
            this.sentryService.captureException(errorItem);
            break;
          case AddonErrorCodes.InvalidOption:
            this.getData();
            break;
          default:
            errors.errors.push({ message: `Unknown error (${errorItem.code})`, code: errorItem.code });
            addError = false;
            break;
        }
        if (addError) {
          errors.errors.push(errorItem);
        } else {
          this.errorBaseService.handleError(errors);
        }
      }
      this.errorBaseService.handleFormatError(errors);
    }
  }
}

import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ActivatedRoute, provideRouter } from '@angular/router';
import { TranslateService } from '@klicktipp/kt-mat-library';
import { MockProvider } from 'ng-mocks';
import { RouteParameterService } from '../../../../services/route-parameter.service';
import { LabelCreateDetailComponent } from './label-create-detail.component';
import { LabelApiService } from '../../services/label-api.service';
import { LabelMockService } from '../../mocks/label-mock.service';
import { of } from 'rxjs';
import { LabelFacade } from '../../+state/label.facade';
import { provideMockStore } from '@ngrx/store/testing';
import { CacheFacadeProviderMock } from '../../../../mocks/cache-facade.mock';

describe('LabelCreateDetailComponent ', () => {
  let component: LabelCreateDetailComponent;
  let fixture: ComponentFixture<LabelCreateDetailComponent>;

  const mockLabelFacade = {
    settingsLoaded$: of(true),
    settingsEntity$: of({ id: 123, name: 'My Label', notes: 'Some notes' }),
    settingsError$: of(null),
    initSettings: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    navigateToOverview: jest.fn(),
    resetSettingsState: jest.fn()
  };
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LabelCreateDetailComponent],
      providers: [
        provideRouter([]),
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        MockProvider(ActivatedRoute, {
          snapshot: {
            data: {
              isCopy: false
            }
          }
        } as unknown as ActivatedRoute),
        MockProvider(LabelApiService),
        MockProvider(RouteParameterService, { currentId: 123, userId: '1' }),
        MockProvider(TranslateService, { translate: (v) => v }),
        ...CacheFacadeProviderMock,
        {
          provide: LabelApiService,
          useClass: LabelMockService
        },
        { provide: LabelFacade, useValue: mockLabelFacade }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LabelCreateDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should fetch data on initialization', () => {
    const fetchDataSpy = jest.spyOn(component, 'ngOnInit');
    component.ngOnInit();
    expect(fetchDataSpy).toHaveBeenCalled();
  });

  it('should navigate to overview when back button is clicked', () => {
    component.back();
    expect(mockLabelFacade.navigateToOverview).toHaveBeenCalled();
  });

  it('should set form values correctly', waitForAsync(async () => {
    fixture.detectChanges();
    await fixture.whenStable();
    const form = component.form;
    expect(form.getRawValue()).toEqual({
      labelNotesActive: true,
      labelNameInput: 'My Label',
      labelNotes: 'Some notes',
      entityLabels: [],
      entityLabelsInput: '',
      entityName: 'My Label',
      entityNotes: '',
      entityNotesEnabled: false
    });
  }));

  it('should call delete with the entity id', () => {
    component.deleteLabel();
    expect(mockLabelFacade.delete).toHaveBeenCalledWith(123, {
      id: 'delete-label-modal',
      title: 'Label::Delete::Modal::Title::Delete Label',
      primaryButtonText: 'Label::Delete::Modal::Button::Delete',
      message: 'Label::Delete::Modal::Message::Are you sure you want to delete this label?'
    });
  });
});

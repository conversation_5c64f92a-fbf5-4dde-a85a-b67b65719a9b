import { Component, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faCheck, faChevronLeft, faTrashAlt } from '@fortawesome/pro-regular-svg-icons';
import { KtButtonComponent, ModalData, TranslateService } from '@klicktipp/kt-mat-library';
import { Observable } from 'rxjs';
import { filter, take, tap } from 'rxjs/operators';
import { LabelFacade } from '../../+state/label.facade';
import { ErrorItems } from '../../../../models/error/custom-error';
import { ErrorBaseService } from '../../../../services/error-base.service';
import { RouteParameterService } from '../../../../services/route-parameter.service';
import { TabTitleService } from '../../../../services/tab-title.service';
import { BaseRouteParams } from '../../../email/email-settings/models/email-route-params';
import { OnReadyBaseComponent } from '../../../forms.shared/components/on-ready/on-ready-base.component';
import { LabelEntity } from '../../models/label';
import { GeneralValidationErrors, LabelValidationErrors } from '../../../../models/error/error.interface';
import { EntityCreateComponent } from '../../../forms.shared/components/entity-create/entity-create.component';
import { EntityCreateFormKeyEnum } from '../../../forms.shared/components/entity-create/entity-create.models';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'kt-label-create-detail',
  standalone: true,
  imports: [KtButtonComponent, ReactiveFormsModule, EntityCreateComponent, AsyncPipe],
  templateUrl: './label-create-detail.component.html'
})
export class LabelCreateDetailComponent extends OnReadyBaseComponent implements OnInit {
  form: FormGroup = new FormGroup({});
  labelTitle: string;
  tabTitle: string;
  isCreate: boolean;
  loaded$: Observable<boolean> = this.labelFacade.settingsLoaded$.pipe(tap((loaded) => this.setReady(loaded)));
  entity$ = this.labelFacade.settingsEntity$.pipe(filter((labelEntity) => labelEntity != null));

  labels: LabelEntity[] = [];

  iconCheck: IconProp = faCheck as IconProp;
  iconChevronLeft: IconProp = faChevronLeft as IconProp;
  iconTrash: IconProp = faTrashAlt as IconProp;
  saveButtonText = this.translateService.translate('Label::CreateDetail::Button::Save');
  deleteButtonText = this.translateService.translate('Label::CreateDetail::Button::Delete');
  createButtonText = this.translateService.translate('Label::CreateDetail::Button::Create');
  backButtonText = this.translateService.translate('Label::CreateDetail::Button::Back');

  constructor(
    private activatedRoute: ActivatedRoute,
    private routingService: RouteParameterService,
    private errorBaseService: ErrorBaseService,
    private tabTitleService: TabTitleService,
    private labelFacade: LabelFacade,
    translateService: TranslateService
  ) {
    super(translateService, 'label-create-detail');
    this.destroyRef.onDestroy(() => this.labelFacade.resetSettingsState());
  }

  ngOnInit(): void {
    this.labelFacade.settingsError$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((error) => this.handleValidationErrors(error as ErrorItems));
    const routeData: BaseRouteParams = this.activatedRoute.snapshot.data as BaseRouteParams;
    this.isCreate = routeData.isCreate;
    this.updateTranslations();
    this.tabTitleService.setTabName(this.tabTitle);
    this.labelFacade.initSettings(this.routingService.currentId);
  }

  updateTranslations(): void {
    this.labelTitle = this.isCreate
      ? this.translateService.translate('Label::CreateDetail::Title::Create Label')
      : this.translateService.translate('Label::CreateDetail::Title::Edit Label');
    this.tabTitle = this.isCreate
      ? this.translateService.translate('Label::CreateDetail::TabTitle::Create Label')
      : this.translateService.translate('Label::CreateDetail::TabTitle::Edit Label');
  }

  createLabel(): void {
    this.entity$.pipe(take(1)).subscribe((entity) => {
      const newEntity = this.mapFormToEntity(entity);
      this.labelFacade.create(newEntity);
    });
  }

  updateLabel(): void {
    this.entity$.pipe(take(1)).subscribe((entity) => {
      const updatedEntity = this.mapFormToEntity(entity);
      this.labelFacade.update(updatedEntity);
    });
  }

  deleteLabel(): void {
    const currentId = this.routingService.currentId;
    const modalData = this.createDeleteModal();
    this.labelFacade.delete(currentId, modalData);
  }

  back(): void {
    this.labelFacade.navigateToOverview();
  }

  private mapFormToEntity(entity: LabelEntity): LabelEntity {
    return {
      ...entity,
      name: this.form.get(EntityCreateFormKeyEnum.Name).value,
      notes: this.form.get(EntityCreateFormKeyEnum.NotesEnabled).value ? this.form.get(EntityCreateFormKeyEnum.Notes).value : ''
    };
  }

  private createDeleteModal(): ModalData {
    return {
      id: 'delete-label-modal',
      title: this.translateService.translate('Label::Delete::Modal::Title::Delete Label'),
      primaryButtonText: this.translateService.translate('Label::Delete::Modal::Button::Delete'),
      message: this.translateService.translate('Label::Delete::Modal::Message::Are you sure you want to delete this label?')
    };
  }

  private handleValidationErrors(err: ErrorItems): void {
    this.form.markAllAsTouched();
    if (err instanceof ErrorItems) {
      const errors: ErrorItems = new ErrorItems([]);
      for (const errorItem of err.errors) {
        let addError = true;
        switch (errorItem.code) {
          case GeneralValidationErrors.CreateFailed:
          case GeneralValidationErrors.UpdateFailed:
          case GeneralValidationErrors.DeleteFailed:
            break;
          case LabelValidationErrors.LabelNameLengthExceeded:
          case LabelValidationErrors.LabelEmptyName:
          case LabelValidationErrors.LabelDuplicateName:
            this.form.get(EntityCreateFormKeyEnum.Name).setErrors({ customError: errorItem.message });
            break;
          default:
            errors.errors.push({ message: `Unknown error (${errorItem.code})`, code: errorItem.code });
            addError = false;
            break;
        }
        if (addError) {
          errors.errors.push(errorItem);
        } else {
          this.errorBaseService.handleError(errors);
        }
      }
      this.errorBaseService.handleFormatError(errors);
    }
  }
}

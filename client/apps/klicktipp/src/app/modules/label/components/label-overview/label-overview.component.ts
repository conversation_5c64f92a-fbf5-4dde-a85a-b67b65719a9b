import { AsyncPipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faPlus } from '@fortawesome/pro-regular-svg-icons';
import { KtButtonComponent, KtNavigationService, KtNotificationComponent, KtTableComponent, TableBase, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { LabelFacade } from '../../+state/label.facade';
import { TabTitleService } from '../../../../services/tab-title.service';
import { OnReadyBaseComponent } from '../../../forms.shared/components/on-ready/on-ready-base.component';
import { LabelOverviewCreateButton, LabelOverviewLinks } from '../../models/label';
import { LabelTableConvertService } from '../../services/label-table-convert.service';

@Component({
  selector: 'kt-label-overview',
  standalone: true,
  imports: [KtButtonComponent, KtNotificationComponent, KtTableComponent, TranslatePipe, AsyncPipe],
  templateUrl: './label-overview.component.html',
})
export class LabelOverviewComponent extends OnReadyBaseComponent implements OnInit {
  loaded$: Observable<boolean> = this.labelFacade.overviewLoaded$.pipe(tap((loaded) => this.setReady(loaded)));
  labelOverviewTableItem$: Observable<TableBase> = this.labelFacade.overviewAllLabel$.pipe(map((labelData) => this.labelTableConvertService.createLabelOverviewTable(labelData)));
  links$: Observable<LabelOverviewLinks> = this.labelFacade.overviewLinks$;
  createTypes$: Observable<LabelOverviewCreateButton[]> = this.labelFacade.overviewCreateTypes$;
  iconPlus: IconProp = faPlus as IconProp;

  constructor(
    private labelTableConvertService: LabelTableConvertService,
    private ktNavigationService: KtNavigationService,
    private tabTitleService: TabTitleService,
    private labelFacade: LabelFacade,
    translateService: TranslateService
  ) {
    super(translateService, 'label-overview');
  }

  ngOnInit(): void {
    this.tabTitleService.setTabName(this.translateService.translate('Label::Overview::TabTitle::Label Overview'));
    this.labelFacade.initOverview();
  }

  addLabel(value: LabelOverviewCreateButton): void {
    this.ktNavigationService.routeTo(value.createUrl);
  }
}

import { Directive } from '@angular/core';
import { FormControl, NG_VALIDATORS, Validator } from '@angular/forms';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: '[maxFileSize]',
  providers: [{ provide: NG_VALIDATORS, useExisting: FileSizeValidator, multi: true }],
  standalone: true
})
export class FileSizeValidator implements Validator {
  static validate(c: FormControl): { [key: string]: unknown } {
    const fileList = c?.value as FileList;
    const file = !!fileList && fileList.length > 0 ? fileList[0] : null;
    if (!file) {
      return null;
    }
    const fileSize = file?.size;
    const fileSizeMb = fileSize / Math.pow(1000, 2);

    // compare with max default 512 MB - https://klicktipp.atlassian.net/browse/DEV-3148
    return fileSizeMb > 512 ? { fileTooBig: true } : null;
  }

  validate(c: FormControl): { [key: string]: unknown } {
    return FileSizeValidator.validate(c);
  }
}

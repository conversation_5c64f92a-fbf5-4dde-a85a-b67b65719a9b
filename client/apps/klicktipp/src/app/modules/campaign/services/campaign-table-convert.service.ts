import { Breakpoints } from '@angular/cdk/layout';
import { Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faCogs, faCopy, faEye, faPencil, faTrash } from '@fortawesome/pro-regular-svg-icons';
import {
  DashFilterService,
  KtComponentType,
  KtTableService,
  PaginatorSize,
  SelectItem,
  SelectOption,
  TableAlignTypes,
  TableBase,
  TableCellComponent,
  TableDefaultSortValueItem,
  TableHeaderItem,
  TableRowData,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { EModuleNames } from '../../../enums/modules-enum';
import { KtStorageKeysEnum } from '../../../enums/storage.enum';
import { ConvertTableBaseService } from '../../../services/convert-table.base.service';
import { KtStorageService } from '../../../services/kt-storage.service';
import { CampaignValidationItemDetail, EmailOverviewItem } from '../../forms.shared/models/shared-form.interfaces';
import { AutoresponderType, CampaignSettingsAffix, NewsletterType } from '../general/campaignConstants';
import { CampaignDetailData, CampaignEntityDetailData, CampaignOverview, CampaignOverviewItem, CampaignTodoListItem } from '../models/campaignBase';
import { CampaignTableDataService } from './campaign-table-data.service';

@Injectable({
  providedIn: 'root'
})
export class CampaignTableConvertService extends ConvertTableBaseService {
  faView = faEye as IconProp;
  faSettings = faCogs as IconProp;
  faEdit = faPencil as IconProp;
  faCopy = faCopy as IconProp;
  faTrash = faTrash as IconProp;

  splittestChipText = this.translateService.translate('Autoresponder::Overview::Chip::Splittest');
  smsChipText = this.translateService.translate('Autoresponder::Overview::Chip::SMS Message');

  duplicateButtonText = this.translateService.translate('Campaign::SplitTest::TableButton::Duplicate');
  editButtonText = this.translateService.translate('Campaign::SplitTest::TableButton::Edit');
  deleteButtonText = this.translateService.translate('Campaign::SplitTest::TableButton::Delete');

  previewButtonText = this.translateService.translate('Campaign::SplitTest::TableButton::Preview');

  constructor(
    protected translateService: TranslateService,
    private storageService: KtStorageService,
    tableService: KtTableService,
    private campaignTableDataService: CampaignTableDataService,
    protected dashPipe: DashFilterService,
    sanitizer: DomSanitizer
  ) {
    super(translateService, storageService, tableService, dashPipe, sanitizer);
    this.setupPaginationAndSortingForOverviewPages();
  }

  createTableSplittestStats(data: CampaignEntityDetailData): TableBase {
    const headers: TableHeaderItem[] = this.campaignTableDataService.getSplittestEmailsHeaders(data.displayOptions);
    const rows: TableRowData[] = [];
    this.paginationSizeKey = KtStorageKeysEnum.CampaignSplittestPaginationSize;
    this.currentPageKey = KtStorageKeysEnum.CampaignSplittestCurrentPage;
    this.sortSettingsKey = KtStorageKeysEnum.CampaignSplittest;

    for (let i = 0; i < data.entity.stats.splitTestEmails?.length; i++) {
      const email = data.entity.stats.splitTestEmails[i];
      const row: TableRowData = {
        id: email.id,
        rowData: []
      };
      if (data.displayOptions.showSplitTestEmailEditButtons) {
        row.rowData.push({
          id: 'duplicate',
          column: 'operations',
          text: this.duplicateButtonText,
          componentType: KtComponentType.Button,
          icon: { icon: this.faCopy },
          key: 'duplicate'
        });
        row.rowData.push({
          id: 'edit',
          column: 'operations',
          text: this.editButtonText,
          componentType: KtComponentType.LinkButton,
          icon: { icon: this.faEdit },
          key: 'edit',
          link: email.editLink
        });
        row.rowData.push({
          id: 'delete',
          column: 'operations',
          text: this.deleteButtonText,
          componentType: KtComponentType.Button,
          icon: { icon: this.faTrash },
          key: 'delete'
        });
      }
      row.rowData.push({
        id: 'preview',
        column: 'operations',
        text: this.previewButtonText,
        componentType: KtComponentType.LinkButton,
        icon: { icon: this.faView },
        key: 'preview',
        link: email.previewLink
      });

      for (const [key, _value] of Object.entries(email)) {
        if (key === 'isReadyToSend') {
          let text: string;
          let color: string;
          if (_value) {
            text = this.translateService.translate('Splittest::Table::Status::Ready to send');
            color = 'success';
          } else {
            text = this.translateService.translate('Splittest::Table::Status::Not ready to send');
            color = 'error';
          }
          const chip: TableCellComponent = {
            column: 'isReadyToSend',
            text,
            attributes: { color },
            componentType: KtComponentType.Chip,
            id: 'isReadyToSend-chip'
          };
          row.rowData.push(chip);
        } else {
          const { rowEle } = this.createRow(email, key);
          row.rowData.push(rowEle);
        }
      }

      rows.push(row);
    }
    const defaultSortValue: TableDefaultSortValueItem = {
      default: { name: 'variant', direction: 'desc' }
    };
    const sortSettings: TableDefaultSortValueItem = this.storageService.getFromSessionStorage(this.sortSettingsKey);
    for (const sortSetting in sortSettings) {
      defaultSortValue[sortSetting] = sortSettings[sortSetting];
    }
    return {
      id: 'table-splittest-stats',
      headers,
      rows,
      defaultSortValue
    };
  }

  createTableScheduleWarnings(data: CampaignEntityDetailData): TableBase {
    const headers: TableHeaderItem[] = [];
    const rows: TableRowData[] = [];

    headers.push(
      this.createHeaderItem({
        header: this.translateService.translate('Campaign::Splittest::Warning::Reason Name'),
        key: 'key'
      })
    );

    headers.push(
      this.createHeaderItem({
        header: this.translateService.translate('Campaign::Splittest::Table::Operations'),
        key: 'operations',
        disableSort: true
      })
    );

    for (let i = 0; i < data.messages.sendDateNotReadyToSendItems[0].details.length; i++) {
      const validationItem = data.messages.sendDateNotReadyToSendItems[0].details[i];
      const row: TableRowData = {
        id: i,
        rowData: []
      };

      this.translateIssues(validationItem);
      const { rowEle } = this.createRow(validationItem, 'key');
      row.rowData.push(rowEle);

      // ###### buttons ######
      row.rowData.push({
        id: 'edit-btn',
        column: 'operations',
        text: this.translateService.translate('Campaign::Schedule::Table::Edit email'),
        componentType: KtComponentType.LinkButton,
        icon: { icon: this.faEdit },
        key: 'edit',
        link: validationItem.description
      });
      rows.push(row);
    }

    return {
      id: 'table-splittest-warnings',
      customCss: 'table-splittest-warnings',
      headers,
      rows,
      defaultSortValue: {
        default: { name: 'key', direction: 'desc' }
      }
    };
  }

  createTableCampaignEmails(campaignEmails: EmailOverviewItem[]): TableBase {
    const entities = campaignEmails;
    const headers: TableHeaderItem[] = this.campaignTableDataService.campaignEmailsHeaders;
    const rows: TableRowData[] = [];
    for (const entity of entities) {
      const row: TableCellComponent[] = [];
      for (const [key, _value] of Object.entries(entity)) {
        const { propertyName, rowEle } = this.createRow(entity, key);
        switch (propertyName) {
          case 'name':
            rowEle.link = entity.linkEdit;
            break;
          case 'sent':
          case 'opened':
          case 'openRate':
          case 'clicked':
          case 'clickRate':
            rowEle.link = entity.linkStatistics;
            break;
        }
        row.push(rowEle);
      }
      this.setDashFilter(entity, row, ['sent', 'openRate', 'opened', 'clicked', 'clickRate']);
      rows.push({
        id: entity.id,
        rowData: row,
        statusFilterKey: [entity.type]
      });
    }
    this.sortSettingsKey = KtStorageKeysEnum.AutomationEmailsSortSettings;
    const sortValue: TableDefaultSortValueItem = this.ktStorageService.getFromSessionStorage(this.sortSettingsKey);
    return {
      id: 'table-campaign-emails',
      headers,
      rows,
      defaultSortValue: sortValue ?? { default: { name: 'id', direction: 'desc' } }
    };
  }

  createTableSendDateTodos(campaignEntityData: CampaignDetailData): TableBase {
    const headers: TableHeaderItem[] = [];
    const rows: TableRowData[] = [];
    headers.push(
      this.createHeaderItem({
        header: this.translateService.translate('Campaign::EditSendDate::ConditionTableHeader::Name'),
        key: 'name'
      })
    );
    headers.push(
      this.createHeaderItem({
        header: this.translateService.translate('Campaign::EditSendDate::ConditionTableHeader::Message'),
        key: 'message'
      })
    );
    for (let i = 0; i < campaignEntityData.data.filterOptions?.todoList.length; i++) {
      const entity = campaignEntityData.data.filterOptions?.todoList[i];
      rows.push({
        id: 'senddate-todo' + i,
        rowData: this.createRowDataForSendDateTodos(entity)
      });
    }
    return {
      id: 'table-senddate-todos',
      headers,
      rows
    };
  }

  createTableSendDateWarnings(campaignEntityData: CampaignDetailData): TableBase {
    const headers: TableHeaderItem[] = [];
    const rows: TableRowData[] = [];
    headers.push(
      this.createHeaderItem({
        header: this.translateService.translate('Campaign::EditSendDate::ConditionTableHeader::Name'),
        key: 'name'
      })
    );
    headers.push(
      this.createHeaderItem({
        header: this.translateService.translate('Campaign::EditSendDate::ConditionTableHeader::Message'),
        key: 'message'
      })
    );
    for (let i = 0; i < campaignEntityData.data.filterOptions?.warningList.length; i++) {
      const entity = campaignEntityData.data.filterOptions?.warningList[i];
      rows.push({
        id: 'senddate-warning' + i,
        rowData: this.createRowDataForSendDateTodos(entity)
      });
    }
    return {
      id: 'table-senddate-warnings',
      headers,
      rows
    };
  }

  createTableCampaignOverview(
    campaignEntityData: CampaignOverview,
    basePath: string,
    userId: string,
    module: EModuleNames,
    preSelectFilter: SelectItem = null,
    defaultOption: SelectOption = null
  ): TableBase {
    const entities = campaignEntityData.data.entities;
    let headers: TableHeaderItem[] = [];
    let savedFilter: string;
    switch (module) {
      case EModuleNames.Automation:
        headers = this.campaignTableDataService.campaignTableOverviewHeaders;
        this.paginationSizeKey = KtStorageKeysEnum.AutomationOverviewPaginationSize;
        this.currentPageKey = KtStorageKeysEnum.AutomationOverviewCurrentPage;
        this.sortSettingsKey = KtStorageKeysEnum.AutomationOverviewSortSettings;
        break;
      case EModuleNames.Autoresponder:
        headers = this.campaignTableDataService.autoresponderTableOverviewHeaders;
        if (campaignEntityData.data.displayOptions?.accessConversionPixel) {
          headers.splice(headers.length - 1, 0, {
            header: this.translateService.translate('Autoresponder::Overview::Table::Header::Converted'),
            key: 'converted',
            align: TableAlignTypes.Right,
            filterKey: ['all'],
            breakpoint: Breakpoints.Medium
          });
        }
        this.paginationSizeKey = KtStorageKeysEnum.AutoresponderOverviewPaginationSize;
        this.currentPageKey = KtStorageKeysEnum.AutoresponderOverviewCurrentPage;
        this.sortSettingsKey = KtStorageKeysEnum.AutoresponderOverviewSortSettings;
        savedFilter = this.storageService.getFromSessionStorage(KtStorageKeysEnum.AutoresponderOverviewFilter);
        break;
      case EModuleNames.Newsletter:
        headers = this.campaignTableDataService.newsletterTableOverviewHeaders;
        this.paginationSizeKey = KtStorageKeysEnum.NewsletterOverviewPaginationSize;
        this.currentPageKey = KtStorageKeysEnum.NewsletterOverviewCurrentPage;
        this.sortSettingsKey = KtStorageKeysEnum.NewsletterOverviewSortSettings;
        savedFilter = this.storageService.getFromSessionStorage(KtStorageKeysEnum.NewsletterOverviewFilter);
    }

    const rows: TableRowData[] = [];
    let filterStartTags: number[] | string[] = [];
    for (const entity of entities) {
      filterStartTags = module === EModuleNames.Autoresponder ? entity.filterStartTags.map((f) => f.toString()) : [entity.status];
      const rowData = this.createRowDataForCampaignOverview(entity, basePath, userId, module);
      rows.push({
        id: entity.id,
        rowData,
        statusFilterKey: filterStartTags
      });
    }
    const defaultSortValue: TableDefaultSortValueItem = {
      default: { name: 'id', direction: 'desc' },
      sent: { name: 'sendDatetime', direction: 'desc' }
    };

    const paginatorSize: number = this.storageService.getFromSessionStorage(this.paginationSizeKey) ?? PaginatorSize;
    const sortSettings: TableDefaultSortValueItem = this.storageService.getFromSessionStorage(this.sortSettingsKey);
    for (const sortSetting in sortSettings) {
      defaultSortValue[sortSetting] = sortSettings[sortSetting];
    }
    const pageIndex: number = this.storageService.getFromSessionStorage(this.currentPageKey) ?? 0;

    return {
      id: `table-overview-${EModuleNames[module].toLowerCase()}`,
      headers,
      textFilter: true,
      preSelectFilter,
      defaultSortValue,
      defaultPreSelectValue: savedFilter ?? defaultOption?.value?.toString(),
      rows,
      customCss: 'automation-overview',
      paginatorSize,
      pageIndex
    };
  }

  private createRowDataForCampaignOverview(entity: CampaignOverviewItem, basePath: string, userId: string, module: EModuleNames): TableCellComponent[] {
    const row: TableCellComponent[] = [];
    let rowElement: TableCellComponent;
    for (const [key, _value] of Object.entries(entity)) {
      const { propertyName, rowEle } = this.createRow(entity, key);
      row.push({ id: propertyName, column: key, componentType: KtComponentType.Text, text: rowEle.text });
    }

    // ###### splittest ######
    if (entity.isSplittest) {
      row.push({
        text: this.splittestChipText,
        componentType: KtComponentType.Chip,
        column: 'name',
        id: 'splittest-chip',
        attributes: { color: 'splittest' }
      });
    }

    // ###### sms ######
    if (entity.type === NewsletterType.newsletterSms || entity.type === AutoresponderType.arSms || entity.type === AutoresponderType.arSmsBirthday) {
      row.push({
        text: this.smsChipText,
        componentType: KtComponentType.Chip,
        column: 'name',
        id: 'sms-chip',
        attributes: { color: 'splittest' }
      });
    }

    rowElement = row.find((r) => r.column === 'name');
    rowElement.link = entity.linkName;
    if (module === EModuleNames.Newsletter) {
      if (!rowElement.attributes) {
        rowElement.attributes = {};
      }
      rowElement.attributes.noEllipsis = true;
    }

    if (module === EModuleNames.Automation) {
      const statNames = ['active', 'finished', 'started'];
      for (const statName of statNames) {
        rowElement = row.find((r) => r.column === statName);
        rowElement.link = entity.linkStatistics;
      }
    }

    if (module === EModuleNames.Autoresponder || module === EModuleNames.Newsletter) {
      const statNames = ['sent', 'opened', 'clicked'];
      for (const statName of statNames) {
        rowElement = row.find((r) => r.column === statName);
        rowElement.link = entity.linkStatistics;
      }
    }

    // ###### status ######
    if (entity.displayStatus) {
      const columnElement: TableCellComponent = row.find((r) => r.column === 'displayStatus');
      columnElement.text = entity.displayStatus;
      columnElement.componentType = KtComponentType.Text;
      columnElement.attributes = {
        displayStatus: entity.status
      };
    }

    this.setDashFilter(entity, row, ['active', 'finished', 'started', 'opened', 'clicked', 'sent', 'converted']);
    this.setEllipsisValue(row, ['sendDatetime', 'id', 'displayDelay']);

    // ###### buttons ######
    row.push({
      column: 'operations',
      componentType: KtComponentType.LinkButton,
      text: this.translateService.translate('campaign::automation::overview::tableButton::View'),
      icon: { icon: this.faView },
      key: CampaignSettingsAffix.statistics,
      link: `/app${basePath}${userId}/${entity.id}/view`,
      id: 'view-button',
      attributes: {
        alwaysTableButton: true
      }
    });
    row.push({
      column: 'operations',
      componentType: KtComponentType.LinkButton,
      text: this.translateService.translate('campaign::automation::overview::tableButton::Settings'),
      icon: { icon: this.faSettings },
      key: CampaignSettingsAffix.settings,
      link: `/app${basePath}${userId}/${entity.id}/edit`,
      id: 'settings-button',
      attributes: {
        alwaysTableButton: true
      }
    });
    row.push({
      column: 'operations',
      componentType: KtComponentType.LinkButton,
      text: this.translateService.translate('campaign::automation::overview::tableButton::Edit'),
      icon: { icon: this.faEdit },
      key: CampaignSettingsAffix.cockpit,
      link: `${window.location.origin}/build/marketing-cockpit/${entity.id}`,
      id: 'cockpit-button',
      attributes: {
        alwaysTableButton: true
      }
    });

    return row;
  }

  private setEllipsisValue(row: TableCellComponent[], propertiesWithoutEllipsis: string[]): void {
    propertiesWithoutEllipsis.forEach((prop) => {
      const rowElement = row.find((r) => r.column === prop);
      if (rowElement) {
        rowElement.attributes = { ...rowElement.attributes, noEllipsis: true };
      }
    });
  }

  private createRowDataForSendDateTodos(entity: CampaignTodoListItem): TableCellComponent[] {
    const row: TableCellComponent[] = [];
    for (const [key, _value] of Object.entries(entity)) {
      if (key === 'url') {
        const rowElement = row.find((r) => r.column === 'message');
        rowElement.link = entity[key];
        continue;
      }
      const { rowEle } = this.createRow(entity, key);
      row.push(rowEle);
    }
    return row;
  }

  private translateIssues(validationItem: CampaignValidationItemDetail): void {
    switch (validationItem.key) {
      case 'no-birthday-custom-field':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::NoBirthdayCustomField');
        break;
      case 'no-datetime-custom-field':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::NoDatetimeCustomField');
        break;
      case 'empty-subject':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::EmptySubject');
        break;
      case 'empty-html-content':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::EmptyHtmlContent');
        break;
      case 'not-published':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::NotPublished');
        break;
      case 'no-sms-provider':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::NoSmsProvider');
        break;
      case 'no-sms-sender':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::NoSmsSender');
        break;
      case 'empty-sms-content':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::EmptySmsContent');
        break;
      case 'splittest-empty-sms-content':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::SplittestEmptySmsContent');
        break;
      case 'splittest-missing-variant':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::SplittestMissingVariant');
        break;
      case 'splittest-not-published':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::SplittestNotPublished');
        break;
      case 'splittest-empty-subject':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::SplittestEmptySubject');
        break;
      case 'splittest-empty-html-content':
        validationItem.key = this.translateService.translate('Campaign::Schedule::Warning::SplittestEmptyHtmlContent');
        break;
      default:
        return;
    }
  }
}

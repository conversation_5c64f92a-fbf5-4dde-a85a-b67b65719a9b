import { KeyValuePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  KtButtonComponent,
  KtButtonToggleComponent,
  KtCheckboxComponent,
  KtDatepickerComponent,
  KtInputComponent,
  KtLabelComponent,
  KtMagicSelectComponent,
  KtMessageService,
  KtNavigationService,
  KtPanelComponent,
  KtRadioComponent,
  KtSelectComponent,
  KtSliderComponent,
  KtStaticMessageComponent,
  KtUnitPickerComponent,
  ModalData,
  ModalReturnData,
  SelectOption,
  ToggleSelectOption,
  TranslatePipe,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { forkJoin } from 'rxjs';
import { take } from 'rxjs/operators';
import { LabelService } from '../../../../../services/entities/label.service';
import { TagService } from '../../../../../services/entities/tag.service';
import { ErrorBaseService } from '../../../../../services/error-base.service';
import { ModalService } from '../../../../../services/modal.service';
import { RouteParameterService } from '../../../../../services/route-parameter.service';
import { SentryService } from '../../../../../services/sentry.service';
import { UtilityService } from '../../../../../services/utility.service';
import { EntityCreateComponent } from '../../../../forms.shared/components/entity-create/entity-create.component';
import { CampaignCreateResponse, CampaignSaveResponse } from '../../../models/campaignBase';
import { AutoresponderService } from '../../../services/autoresponder.service';
import { CampaignService } from '../../../services/campaign.service';
import { ItemCreateDetailComponent } from '../../shared/item-create-detail/item-create-detail.component';

@Component({
  selector: 'kt-autoresponder-create-detail',
  templateUrl: '../../shared/item-create-detail/item-create-detail.component.html',
  styleUrls: ['../../shared/item-create-detail/item-create-detail.component.scss'],
  imports: [
    ReactiveFormsModule,
    TranslatePipe,
    KtButtonComponent,
    KtStaticMessageComponent,
    KtInputComponent,
    KtMagicSelectComponent,
    KtCheckboxComponent,
    KtSelectComponent,
    KtRadioComponent,
    KeyValuePipe,
    KtLabelComponent,
    KtUnitPickerComponent,
    KtSliderComponent,
    KtPanelComponent,
    KtDatepickerComponent,
    KtButtonToggleComponent,
    EntityCreateComponent
  ]
})
export class AutoresponderCreateDetailComponent extends ItemCreateDetailComponent implements OnInit {
  weekdays: ToggleSelectOption[];

  constructor(
    protected translateService: TranslateService,
    protected tagService: TagService,
    private autoresponderService: AutoresponderService,
    protected utilityService: UtilityService,
    protected router: Router,
    protected modalService: ModalService,
    protected labelService: LabelService,
    protected routingService: RouteParameterService,
    protected errorBaseService: ErrorBaseService,
    protected ktMessageService: KtMessageService,
    protected campaignService: CampaignService,
    protected ktNavigationService: KtNavigationService,
    protected sentryService: SentryService
  ) {
    super(
      translateService,
      tagService,
      utilityService,
      router,
      modalService,
      labelService,
      routingService,
      errorBaseService,
      ktMessageService,
      campaignService,
      ktNavigationService,
      sentryService
    );
  }

  setCampaignTranslations(): void {
    this.deleteCampaignButtonText = this.translateService.translate('Autoresponder::Delete::Button::Delete Autoresponder');
    this.createCampaignButtonText = this.translateService.translate('Autoresponder::Create::Button::Create Autoresponder');
    // use translatable to put string in transifex, but not translate (will be translated later in modal)
    // prettier-ignore
    this.dependenciesWarningText = this.translateService.translatable('Autoresponder::DeleteDependencies::Modal::This Autoresponder is used in the following objects and cannot be deleted: @@deps@@');
    if (!this.isSms && this.isBirthday) {
      this.titleCreate = this.translateService.translate('Autoresponder::Email::Create::Header::Create Birthday (autoresponder)');
      this.titleEdit = this.translateService.translate('Autoresponder::Email::Edit::Header::Birthday (autoresponder) Settings');
      this.createCampaignButtonText = this.translateService.translate('Autoresponder::Create::Button::Create Birthday Autoresponder');
    } else if (this.isSms && !this.isBirthday) {
      this.titleCreate = this.translateService.translate('Autoresponder::SMS::Create::Header::Create SMS Autoresponder');
      this.titleEdit = this.translateService.translate('Autoresponder::SMS::Edit::Header::SMS Autoresponder Settings');
      this.createCampaignButtonText = this.translateService.translate('Autoresponder::Create::Button::Create SMS Autoresponder');
    } else if (this.isSms && this.isBirthday) {
      this.titleCreate = this.translateService.translate('Autoresponder::SMS::Create::Header::Create Birthday (SMS autoresponder)');
      this.titleEdit = this.translateService.translate('Autoresponder::SMS::Edit::Header::Birthday (SMS autoresponder) Settings');
      this.createCampaignButtonText = this.translateService.translate('Autoresponder::Create::Button::Create Birthday SMS Autoresponder');
    } else {
      this.titleCreate = this.translateService.translate('Autoresponder::Create::Header::Create Autoresponder');
      this.titleEdit = this.translateService.translate('Autoresponder::Edit::Header::Autoresponder Settings');
    }
  }

  createCampaign(): void {
    super.resetFormErrors();
    this.campaignEntity.birthdayTimeDelay = this.bdayDatepickerComponent.getTimeString();
    this.campaignEntity.dayOfWeekTimeDelay = this.delayDatepickerComponent.getTimeString();
    this.campaignEntity.createEntityFactory(
      this.form,
      this.selectedTaggedWithTags,
      this.selectedNotTaggedWithTags,
      this.selectedMultipleSendTags,
      this.form.get(this.segmentationTypeRadioGroup.id)?.value
    );

    this.autoresponderService
      .create(this.campaignEntity, this.emailEditorType)
      .pipe(take(1))
      .subscribe({
        next: (res: CampaignCreateResponse) => {
          this.cacheFacade.label.resetLabels();
          this.ktMessageService.success(
            this.translateService.translate('Autoresponder::Create::Success::Autoresponder @@name@@ successfully created', { name: this.campaignEntity.name }),
            false,
            true
          );
          this.ktNavigationService.routeTo(res.data.links.email);
        },
        error: (err) => {
          super.handleValidationErrors(err);
        }
      });
  }

  updateCampaignSettings(): void {
    this.campaignEntity.birthdayTimeDelay = this.bdayDatepickerComponent.getTimeString();
    this.campaignEntity.dayOfWeekTimeDelay = this.delayDatepickerComponent.getTimeString();
    this.createForm();
    this.autoresponderService
      .updateAutoresponderSettings(this.campaignEntity)
      .pipe(take(1))
      .subscribe({
        next: (res: CampaignSaveResponse) => {
          this.cacheFacade.label.resetLabels();
          super.handleSuccessfulUpdate(this.translateService.translate('Autoresponder::Edit::Settings::Save::Message::Autoresponder settings saved'), res);
          this.ktNavigationService.routeTo(this.campaignData.entity.links.email);
        },
        error: (err) => {
          this.handleValidationErrors(err);
        }
      });
  }

  deleteCampaign(): void {
    let modalData: ModalData;
    this.autoresponderService
      .getDependencies(this.currentId, this.isSms)
      .pipe(take(1))
      .subscribe((res) => {
        modalData = super.createDefaultDeleteWithDependencyCheckModal(res?.data.dependencies);
        this.showDeleteModal(modalData);
      });
  }

  showDeleteModal(modalData: ModalData): void {
    this.modalService
      .openWarnModal(modalData)
      .pipe(take(1))
      .subscribe((result: ModalReturnData) => {
        if (result && result.clickedUrl && result.id) {
          this.routeToClickedCampaign(result.clickedUrl);
          return;
        }

        if (result && result.apply) {
          this.autoresponderService
            .delete(this.currentId)
            .pipe(take(1))
            .subscribe({
              next: (res: CampaignSaveResponse) => {
                this.ktMessageService.success(res.data.message);
                this.router.navigateByUrl(`/campaign/autoresponder/${this.routingService.userId}`).finally();
              },
              error: (err) => {
                super.handleValidationErrors(err);
              }
            });
        }
      });
  }

  cancel(): void {
    this.router.navigateByUrl(`/campaign/autoresponder/${this.routingService.userId}`).finally();
  }

  protected setCampaignData(): void {
    this.getLabelsAndTags();
    this.setValuesFromBaseCampaign(this.campaignBaseData);
    if (this.campaignBaseData.data.displayOptions.canEditDelay) {
      this.updateButtonToggleStatus();
    }
    forkJoin([this.label$, this.tag$])
      .pipe(take(1), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => this.setFormDefaults());
  }

  protected setFormDefaults(): void {
    this.form.get(this.receiverEmailAddressesCheckbox.id)?.setValue(!!this.campaignBaseData.data.entity.useReceiverEmail);
    const selectedReceiver: SelectOption = this.campaignBaseData.data.filterOptions.receiverEmailAddresses.find((o) => o.value === this.campaignBaseData.data.entity.receiverEmail);
    this.form.get(this.receiverEmailAddresses.id)?.setValue(selectedReceiver);
    super.setFormDefaults();
    this.form.get(this.multipleSendCheckbox.id).setValue(!!this.campaignBaseData.data.entity.multipleSend);
    this.tagService
      .filterTagsByTypeAndConvert('tag')
      .pipe(take(1))
      .subscribe((tags) => {
        this.multipleSendTags = tags;
        this.cdr.markForCheck();
      });
    if (!this.campaignBaseData.data?.displayOptions?.canEditMultipleSend) {
      this.form.get(this.multipleSendCheckbox.id).disable();
      this.form.get('multipleSendTags').disable();
      this.form.get('multipleSendTags' + 'Input').disable();
      this.selectedMultipleSendTagsItem = { ...this.selectedMultipleSendTagsItem, disableChips: true };
    }
    if (this.campaignBaseData.data.displayOptions.canEditDelay) {
      this.delayDatepickerComponent?.setDate(this.campaignBaseData.data.entity.dayOfWeekTimeDelay);
      this.updateButtonToggleStatus();
      this.form.get('delayTimeOptions').setValue(this.campaignBaseData.data.filterOptions.delay[this.campaignBaseData.data.entity.delayUnit]);
      this.form.get('weekdays').setValue(this.campaignBaseData.data.entity.triggerDayOfWeek);
      this.form.get('delayValue').setValue(this.campaignBaseData.data.entity.delayValue);
      this.form.get('weekdaysCheckbox').setValue(!!this.campaignBaseData.data.entity.useTriggerDayOfWeek);
      this.form.get('specificTimeCheckbox').setValue(!!this.campaignBaseData.data.entity.useDayOfWeekTimeDelay);
      const selectedCustomField: SelectOption = this.campaignBaseData.data.filterOptions.dateTimeCustomFields.find(
        (o) => o.value === this.campaignBaseData.data.entity.dateTimeCustomFieldId
      );
      this.form.get('datetimeCustomFields').setValue(selectedCustomField);
    }
    if (this.campaignBaseData.data.displayOptions.canEditBirthdayDelay) {
      const selectedBdayCustomField: SelectOption = this.campaignBaseData.data.filterOptions.dateCustomFields.find(
        (o) => o.value === this.campaignBaseData.data.entity.birthdayCustomFieldId
      );
      this.form.get('bdayDelayTimeOptions').setValue(selectedBdayCustomField);
      this.bdayDatepickerComponent?.setDate(this.campaignBaseData.data.entity.birthdayTimeDelay);
    }
  }
}

/* eslint-disable @typescript-eslint/no-explicit-any */
import { ViewportScroller } from '@angular/common';
import { provideHttpClient } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { KtComponentType, KtMessageService, KtNavigationService, TranslateService } from '@klicktipp/kt-mat-library';
import { MockProvider } from 'ng-mocks';
import { of } from 'rxjs';
import { UnsubscriptionLinkFacade } from '../../+state/unsubscription-link.facade';
import { ConfigService } from '../../../../services/config.service';
import { TagService } from '../../../../services/entities/tag.service';
import { TabTitleService } from '../../../../services/tab-title.service';
import { UnsubscriptionLinkApiMockService } from '../../mocks/unsubscription-link-api-mock.service';
import { UnsubscriptionLinkCreateButton } from '../../models/unsubscription-link';
import { UnsubscriptionLinkApiService } from '../../services/unsubscription-link-api.service';
import { UnsubscriptionLinkTableConvertService } from '../../services/unsubscription-link-table-convert.service';
import { UnsubscriptionLinkOverviewComponent } from './unsubscription-link-overview.component';

describe('UnsubscriptionLinkOverviewComponent', () => {
  let component: UnsubscriptionLinkOverviewComponent;
  let fixture: ComponentFixture<UnsubscriptionLinkOverviewComponent>;
  let mockTranslateService: jest.Mocked<TranslateService>;
  let mockKtNavigationService: jest.Mocked<KtNavigationService>;
  let mockUnsubscriptionLinkTableConvertService: jest.Mocked<UnsubscriptionLinkTableConvertService>;
  let facade: UnsubscriptionLinkFacade;

  const mockOverviewEntities = [
    {
      id: 1,
      name: 'Default unsubscription link 1',
      type: 'unsubscription-link',
      weight: 0,
      tags: [],
      links: { edit: 'unsubscription-link/settings/1/1' }
    },
    {
      id: 2,
      name: 'Unsubscription Link 2',
      type: 'unsubscription-link',
      weight: 1,
      tags: [],
      links: { edit: 'unsubscription-link/settings/1/2' }
    },
    {
      id: 3,
      name: 'Unsubscription Link 3',
      type: 'unsubscription-link',
      weight: 2,
      tags: [],
      links: { edit: 'unsubscription-link/settings/1/3' }
    }
  ];

  const mockCreateTypes = [{ title: 'Create Unsubscription Link', createUrl: '/unsubscription-link/settings/1/create' }];

  beforeEach(async () => {
    mockKtNavigationService = {
      routeTo: jest.fn()
    } as any;

    mockUnsubscriptionLinkTableConvertService = {
      createUnsubscriptionLinkOverviewTable: jest.fn().mockReturnValue({
        id: 'unsubscription-link-table',
        headers: [],
        rows: []
      })
    } as any;

    await TestBed.configureTestingModule({
      imports: [UnsubscriptionLinkOverviewComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        {
          provide: UnsubscriptionLinkApiService,
          useClass: UnsubscriptionLinkApiMockService
        },
        MockProvider(TranslateService, mockTranslateService),
        MockProvider(KtNavigationService, mockKtNavigationService),
        MockProvider(KtMessageService),
        MockProvider(TabTitleService),
        MockProvider(ConfigService),
        MockProvider(TagService, {
          getTags: jest.fn(() => of([]))
        }),
        MockProvider(ActivatedRoute, {
          snapshot: {}
        } as ActivatedRoute),
        MockProvider(ViewportScroller),
        MockProvider(UnsubscriptionLinkFacade, {
          overviewLoaded$: of(true),
          overviewAllUnsubscriptionLink$: of(mockOverviewEntities),
          overviewCreateTypes$: of(mockCreateTypes),
          overviewError$: of(null),
          saveOverviewSorting: jest.fn(),
          initOverview: jest.fn()
        } as any),
        {
          provide: UnsubscriptionLinkTableConvertService,
          useValue: mockUnsubscriptionLinkTableConvertService
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(UnsubscriptionLinkOverviewComponent);
    facade = TestBed.inject(UnsubscriptionLinkFacade);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize and call initOverview', () => {
    const initOverviewSpy = jest.spyOn(facade, 'initOverview');
    component.ngOnInit();
    expect(initOverviewSpy).toHaveBeenCalled();
  });

  it('should sort and add new weight while call saveTableSorting', () => {
    const saveTableSortingSpy = jest.spyOn(facade, 'saveOverviewSorting');

    component.currentSortedRows = [
      {
        id: '3',
        rowData: [
          {
            column: 'name',
            text: 'Unsubscription Link 3',
            componentType: KtComponentType.Text,
            id: '3-name',
            attributes: {},
            link: 'unsubscription-link/settings/1/3'
          }
        ]
      },
      {
        id: '2',
        rowData: [
          {
            column: 'name',
            text: 'Unsubscription Link 2',
            componentType: KtComponentType.Text,
            id: '2-name',
            attributes: {},
            link: 'unsubscription-link/settings/1/2'
          }
        ]
      },
      {
        id: '1',
        rowData: [
          {
            column: 'name',
            text: 'Default unsubscription link 1',
            componentType: KtComponentType.Text,
            id: '1-name',
            attributes: {},
            link: 'unsubscription-link/settings/1/1'
          }
        ]
      }
    ];
    component.saveTableSorting();

    expect(saveTableSortingSpy).toHaveBeenCalledWith([
      {
        id: 3,
        weight: 0
      },
      {
        id: 2,
        weight: 1
      },
      {
        id: 1,
        weight: 2
      }
    ]);
  });

  it('should navigate to correct URL in addUnsubscriptionLink', () => {
    const value = { createUrl: '/unsubscription-link/settings/1/create' } as UnsubscriptionLinkCreateButton;
    component.addUnsubscriptionLink(value);
    expect(mockKtNavigationService.routeTo).toHaveBeenCalledWith('/unsubscription-link/settings/1/create');
  });

  it('should react to unsaved changes', () => {
    expect(component.hasUnsavedChanges).toEqual(false);
    expect(component.currentSortedRows).toEqual([]);

    const $event = [
      {
        rowData: [],
        id: 'test-row-data-1'
      }
    ];

    component.reactToChanges($event);

    expect(component.hasUnsavedChanges).toEqual(true);
    expect(component.currentSortedRows).toEqual($event);
  });

  it('should reset table sorting', () => {
    const tableItemBeforeCall = { id: 'just a test', headers: [], rows: [] };
    const originalTableItem = { id: 'after reset', headers: [], rows: [] };
    component.hasUnsavedChanges = true;
    component.tableItem = { ...tableItemBeforeCall };
    component.originalTableItem = { ...originalTableItem };

    expect(component.hasUnsavedChanges).toEqual(true);
    expect(component.tableItem).toStrictEqual(tableItemBeforeCall);
    expect(component.originalTableItem).toStrictEqual(originalTableItem);

    component.resetTableSorting();

    expect(component.hasUnsavedChanges).toEqual(false);
    expect(component.tableItem).toStrictEqual(originalTableItem);
    expect(component.originalTableItem).toStrictEqual(originalTableItem);
  });
});

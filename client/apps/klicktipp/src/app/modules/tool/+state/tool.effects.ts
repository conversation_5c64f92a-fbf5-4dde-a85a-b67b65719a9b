import { inject } from '@angular/core';
import { KtMessageService, KtNavigationService, TranslateService } from '@klicktipp/kt-mat-library';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Action } from '@ngrx/store';
import { of, zipWith } from 'rxjs';
import { catchError, exhaustMap, map, switchMap, tap } from 'rxjs/operators';
import { CacheFacade } from '../../../+state/cache/cache.facade';
import { resetTagsAndLabelsOperator } from '../../../+state/cache/rxjs.operators';
import { CustomFieldService } from '../../../services/entities/custom-field.service';
import { LabelService } from '../../../services/entities/label.service';
import { OptInService } from '../../../services/entities/opt-in.service';
import { TagService } from '../../../services/entities/tag.service';
import { ErrorBaseService } from '../../../services/error-base.service';
import { ModalService } from '../../../services/modal.service';
import { getDependenciesCheckConfig, getToolComponentMessage, ToolTypeEnum } from '../general/tool-component-map';
import { ToolEntityLinks } from '../models/tool-api';
import { ToolApiProxyService } from '../services/tool-api-proxy.service';
import * as ToolActions from './tool.actions';
import { ToolFacade } from './tool.facade';

export const initOverview$ = createEffect(
  (actions$ = inject(Actions), toolService = inject(ToolApiProxyService)) => {
    return actions$.pipe(
      ofType(ToolActions.initToolOverview),
      exhaustMap(() => {
        return toolService.getItems().pipe(
          map((response) =>
            ToolActions.loadToolOverviewSuccess({
              entities: response.data.entities,
              createTypes: response.data.createTypes,
              createOptions: response.data.createOptions,
              filterOptions: response.data.filterOptions,
              links: response.data.links
            })
          ),
          catchError((error) => of(ToolActions.loadToolOverviewFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const initSettings$ = createEffect(
  (
    actions$ = inject(Actions),
    toolService = inject(ToolApiProxyService),
    tagService = inject(TagService),
    labelService = inject(LabelService),
    optInService = inject(OptInService),
    customFieldService = inject(CustomFieldService)
  ) => {
    return actions$.pipe(
      ofType(ToolActions.initToolSettings),
      exhaustMap(({ id, copyId = 0, toolType }) => {
        toolService.setType(toolType);

        const tags$ = tagService.getTags().pipe(switchMap(() => tagService.filterTagsByTypeAndConvert('tag')));
        const labels$ = labelService.getLabels().pipe(switchMap(() => labelService.filterLabelsByTypeAndConvert('')));
        const optIn$ = optInService.getOptInIndex().pipe(switchMap(() => optInService.filterOptInByTypeAndConvert('')));
        const customField$ = customFieldService.getCustomFieldIndex().pipe(switchMap(() => customFieldService.filterCustomFieldByTypeAndConvert('')));

        return toolService.getItem(id, copyId).pipe(
          zipWith(tags$, labels$, optIn$, customField$),
          map(([response, tagOptions, labelOptions, optInOptions, customFieldOptions]) =>
            ToolActions.loadToolSettingsSuccess({
              entity: response.data?.entity ?? null,
              links: response.data?.links ?? null,
              displayOptions: response.data?.displayOptions ?? null,
              filterOptions: response.data?.filterOptions ?? null,
              cacheOptions: { tagOptions, labelOptions, optInOptions, customFieldOptions }
            })
          ),
          catchError((error) => of(ToolActions.loadToolSettingsFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const createEntity$ = createEffect(
  (actions$ = inject(Actions), toolService = inject(ToolApiProxyService), cacheFacade = inject(CacheFacade)) => {
    return actions$.pipe(
      ofType(ToolActions.createEntity),
      exhaustMap(({ entity, copyId = 0 }) => {
        return toolService.create(entity, copyId).pipe(
          resetTagsAndLabelsOperator(entity, cacheFacade),
          map((response) => {
            const message = getToolComponentMessage(entity.type as ToolTypeEnum, 'createSuccess');
            return ToolActions.createEntitySuccess({ id: entity.id, links: response.links, message });
          }),
          catchError((error) => of(ToolActions.createEntityFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const saveEntity$ = createEffect(
  (actions$ = inject(Actions), toolService = inject(ToolApiProxyService), cacheFacade = inject(CacheFacade)) => {
    return actions$.pipe(
      ofType(ToolActions.saveEntity),
      exhaustMap(({ entity }) => {
        return toolService.save(entity.id, entity).pipe(
          resetTagsAndLabelsOperator(entity, cacheFacade),
          map((response) => {
            const message = getToolComponentMessage(entity.type as ToolTypeEnum, 'saveSuccess');
            return ToolActions.saveEntitySuccess({ id: entity.id, links: response.links, message });
          }),
          catchError((error) => of(ToolActions.saveEntityFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const checkDeleteDependencies$ = createEffect(
  (
    actions$ = inject(Actions),
    toolService = inject(ToolApiProxyService),
    modalService = inject(ModalService),
    navigationService = inject(KtNavigationService),
    translateService = inject(TranslateService)
  ) => {
    return actions$.pipe(
      ofType(ToolActions.checkDeleteDependencies),
      exhaustMap(({ id, name, toolType }) => {
        return toolService.getDependencies(id).pipe(
          switchMap(({ data }) => {
            const { title, message } = getDependenciesCheckConfig(toolType);
            const modalData = modalService.createDefaultDeleteWithDependencyCheckModal(data.dependencies, translateService.translate(title), message, name);
            return modalService.openWarnModal(modalData).pipe(
              switchMap((result) => {
                if (result && result.clickedUrl && result.id) {
                  navigationService.routeTo(result.clickedUrl);
                  return of(ToolActions.checkDeleteDependenciesCancel());
                }

                if (result && result.apply) {
                  return of(ToolActions.deleteEntity({ id, toolType }));
                }

                return of(ToolActions.checkDeleteDependenciesCancel());
              })
            );
          })
        );
      })
    );
  },
  { functional: true }
);

export const deleteEntity$ = createEffect(
  (actions$ = inject(Actions), toolService = inject(ToolApiProxyService)) => {
    return actions$.pipe(
      ofType(ToolActions.deleteEntity),
      exhaustMap(({ id, toolType }) => {
        return toolService.delete(id).pipe(
          map((response) => {
            const message = getToolComponentMessage(toolType as ToolTypeEnum, 'deleteSuccess');
            return ToolActions.deleteEntitySuccess({ message: response?.data?.message ?? message });
          }),
          catchError((error) => of(ToolActions.deleteEntityFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const createSuccessMessage$ = createEffect(
  (actions$ = inject(Actions), messageService = inject(KtMessageService), translateService = inject(TranslateService), navigationService = inject(KtNavigationService)) => {
    return actions$.pipe(
      ofType(ToolActions.createEntitySuccess),
      tap(({ message }) => messageService.success(translateService.translate(message))),
      map(({ links }) => navigationService.routeTo(links.edit))
    );
  },
  { functional: true, dispatch: false }
);

export const saveSuccessMessage$ = createEffect(
  (actions$ = inject(Actions), toolService = inject(ToolApiProxyService), messageService = inject(KtMessageService), translateService = inject(TranslateService)) => {
    return actions$.pipe(
      ofType(ToolActions.saveEntitySuccess),
      tap(({ message }) => messageService.success(translateService.translate(message))),
      map(({ id }) => ToolActions.initToolSettings({ id, toolType: toolService.getType() as ToolTypeEnum, isCreate: false }))
    );
  },
  { functional: true }
);

export const deleteSuccessMessage$ = createEffect(
  (actions$ = inject(Actions), messageService = inject(KtMessageService), translateService = inject(TranslateService)) => {
    return actions$.pipe(
      ofType(ToolActions.deleteEntitySuccess),
      tap(({ message }) => messageService.success(translateService.translate(message))),
      map(() => ToolActions.navigateToOverview())
    );
  },
  { functional: true }
);

export const navigateToOverview$ = createEffect(
  (actions$ = inject(Actions), toolFacade = inject(ToolFacade), navigationService = inject(KtNavigationService)) => {
    return actions$.pipe(
      ofType(ToolActions.navigateToOverview),
      concatLatestFrom(() => toolFacade.settingsLinks$),
      tap(([_, links]: [Action, ToolEntityLinks]) => void navigationService.routeTo(links.overview))
    );
  },
  { functional: true, dispatch: false }
);

export const apiError$ = createEffect(
  (actions$ = inject(Actions), errorService = inject(ErrorBaseService)) => {
    return actions$.pipe(
      ofType(
        ToolActions.loadToolOverviewFailure,
        ToolActions.loadToolSettingsFailure,
        ToolActions.createEntityFailure,
        ToolActions.saveEntityFailure,
        ToolActions.deleteEntityFailure
      ),
      tap(({ error }) => {
        if (error?.status !== 406) {
          errorService.handleError(structuredClone(error));
        }
      })
    );
  },
  { functional: true, dispatch: false }
);

import { inject, Injectable } from '@angular/core';
import { select, Store } from '@ngrx/store';
import { ToolTypeEnum } from '../general/tool-component-map';
import { ToolEntity } from '../models/tool-api';
import * as ToolActions from './tool.actions';
import * as ToolSelectors from './tool.selectors';

export interface ToolInitializationParameters {
  id: number;
  copyId: number;
  toolType: ToolTypeEnum;
  isCreate: boolean;
  isCopy: boolean;
}

export interface ToolCreateParameters {
  entity: ToolEntity;
  copyId: number;
}

@Injectable()
export class ToolFacade {
  private readonly store = inject(Store);

  overviewLoaded$ = this.store.pipe(select(ToolSelectors.selectOverviewLoaded));
  overviewEntities$ = this.store.pipe(select(ToolSelectors.selectOverviewAllEntities));
  overviewSelectedEntity$ = this.store.pipe(select(ToolSelectors.selectOverviewEntity));
  overviewLinks$ = this.store.pipe(select(ToolSelectors.selectOverviewLinks));
  overviewCreateTypes$ = this.store.pipe(select(ToolSelectors.selectOverviewCreateTypes));
  overviewCreateOptions$ = this.store.pipe(select(ToolSelectors.selectOverviewCreateOptions));
  overviewFilterOptions$ = this.store.pipe(select(ToolSelectors.selectOverviewFilterOptions));

  settingsLoaded$ = this.store.pipe(select(ToolSelectors.selectSettingsLoaded));
  settingsEntity$ = this.store.pipe(select(ToolSelectors.selectSettingsEntity));
  settingsLinks$ = this.store.pipe(select(ToolSelectors.selectSettingsLinks));
  settingsDisplayOptions$ = this.store.pipe(select(ToolSelectors.selectSettingsDisplayOptions));
  settingsFilterOptions$ = this.store.pipe(select(ToolSelectors.selectSettingsFilterOptions));
  settingsCacheOptions$ = this.store.pipe(select(ToolSelectors.selectSettingsCacheOptions));
  settingsError$ = this.store.pipe(select(ToolSelectors.selectSettingsError));

  initOverview(): void {
    this.store.dispatch(ToolActions.initToolOverview());
  }

  initSettings(options: ToolInitializationParameters): void {
    this.store.dispatch(ToolActions.initToolSettings(options));
  }

  create(options: ToolCreateParameters): void {
    this.store.dispatch(ToolActions.createEntity(options));
  }

  save(entity: ToolEntity): void {
    this.store.dispatch(ToolActions.saveEntity({ entity }));
  }

  deleteWithDependenciesCheck(id: number, name: string, toolType: ToolTypeEnum): void {
    this.store.dispatch(ToolActions.checkDeleteDependencies({ id, name, toolType }));
  }

  delete(id: number, toolType: ToolTypeEnum): void {
    this.store.dispatch(ToolActions.deleteEntity({ id, toolType }));
  }

  resetSettingsState(): void {
    this.store.dispatch(ToolActions.resetSettingsState());
  }
}

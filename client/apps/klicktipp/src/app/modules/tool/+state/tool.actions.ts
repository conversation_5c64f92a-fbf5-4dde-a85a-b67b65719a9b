import { createAction, props } from '@ngrx/store';
import { ToolTypeEnum } from '../general/tool-component-map';
import {
  ToolEntity,
  ToolEntityLinks,
  ToolOverviewCreateOptions,
  ToolOverviewCreateType,
  ToolOverviewEntity,
  ToolOverviewFilterOptions,
  ToolSettingsCacheOptions,
  ToolSettingsDisplayOptions,
  ToolSettingsFilterOptions
} from '../models/tool-api';

// region General Actions
export const navigateToOverview = createAction('[Tool Page] Navigate To Overview');
// endregion

// region Overview Actions
export const initToolOverview = createAction('[Tool Page] Init Tool Overview');
export const loadToolOverviewSuccess = createAction(
  '[Tool/API] Load Tool Overview Success',
  props<{
    entities: ToolOverviewEntity[];
    createTypes: ToolOverviewCreateType[];
    createOptions: ToolOverviewCreateOptions;
    filterOptions: ToolOverviewFilterOptions;
    links: ToolEntityLinks;
  }>()
);
export const loadToolOverviewFailure = createAction(
  '[Tool/API] Load Tool Overview Failure',
  props<{
    error: Error | string;
  }>()
);
// endregion

// region Settings Actions
export const initToolSettings = createAction(
  '[Tool Page] Init Settings',
  props<{
    id: number;
    copyId?: number;
    toolType: string;
    isCreate: boolean;
    isCopy?: boolean;
  }>()
);
export const loadToolSettingsSuccess = createAction(
  '[Tool/API] Load Tool Settings Success',
  props<{
    entity: ToolEntity;
    links: ToolEntityLinks;
    displayOptions: ToolSettingsDisplayOptions;
    filterOptions: ToolSettingsFilterOptions;
    cacheOptions: ToolSettingsCacheOptions;
  }>()
);
export const loadToolSettingsFailure = createAction(
  '[Tool/API] Load Tool Settings Failure',
  props<{
    error: Error | string;
  }>()
);

export const createEntity = createAction(
  '[Tool Page] Create Entity',
  props<{
    entity: ToolEntity;
    copyId: number;
  }>()
);
export const createEntitySuccess = createAction(
  '[Tool/API] Create Entity Success',
  props<{
    id: number;
    links: ToolEntityLinks;
    message: string;
  }>()
);
export const createEntityFailure = createAction(
  '[Tool/API] Create Entity Failure',
  props<{
    error: Error | string;
  }>()
);

export const saveEntity = createAction(
  '[Tool Page] Save Entity',
  props<{
    entity: ToolEntity;
  }>()
);
export const saveEntitySuccess = createAction(
  '[Tool/API] Save Entity Success',
  props<{
    id: number;
    links: ToolEntityLinks;
    message: string;
  }>()
);
export const saveEntityFailure = createAction(
  '[Tool/API] Save Entity Failure',
  props<{
    error: Error | string;
  }>()
);

export const checkDeleteDependencies = createAction(
  '[Tool Page] Check Delete Dependencies',
  props<{
    id: number;
    name: string;
    toolType: ToolTypeEnum;
  }>()
);
export const checkDeleteDependenciesCancel = createAction('[Tool Page] Check Delete Dependencies Cancel');

export const deleteEntity = createAction(
  '[Tool Page] Delete Entity',
  props<{
    id: number;
    toolType: ToolTypeEnum;
  }>()
);
export const deleteEntitySuccess = createAction(
  '[Tool/API] Delete Entity Success',
  props<{
    message: string;
  }>()
);
export const deleteEntityFailure = createAction(
  '[Tool/API] Delete Entity Failure',
  props<{
    error: Error | string;
  }>()
);

export const resetSettingsState = createAction('[Tool/State] Reset Settings State');
// endregion

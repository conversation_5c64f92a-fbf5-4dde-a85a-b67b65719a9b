/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { Observable, of, throwError } from 'rxjs';
import { CustomFieldService } from '../../../services/entities/custom-field.service';
import { LabelService } from '../../../services/entities/label.service';
import { OptInService } from '../../../services/entities/opt-in.service';
import { TagService } from '../../../services/entities/tag.service';
import { getToolComponentMessage, ToolTypeEnum } from '../general/tool-component-map';
import { ToolDeleteResponse, ToolOverviewResponse, ToolSettingsResponse, ToolSettingsSaveResponse } from '../models/tool-api';
import { ToolApiProxyService } from '../services/tool-api-proxy.service';
import * as ToolActions from './tool.actions';
import * as ToolEffects from './tool.effects';

describe('ToolEffects', () => {
  describe('initOverview$', () => {
    it('should map to loadToolOverviewSuccess', (done) => {
      const actionsMock = of(ToolActions.initToolOverview());
      const toolServiceMock = {
        getItems: (): Observable<ToolOverviewResponse> =>
          of({
            data: {
              entities: [],
              createTypes: [],
              createOptions: { tool: {} } as any,
              filterOptions: { typeOptions: [] },
              links: null
            }
          }),
        setType: jest.fn().mockImplementation(() => null) as any
      } as ToolApiProxyService;

      ToolEffects.initOverview$(actionsMock, toolServiceMock).subscribe((action) => {
        expect(action).toEqual(
          ToolActions.loadToolOverviewSuccess({
            entities: [],
            createTypes: [],
            createOptions: { tool: {} } as any,
            filterOptions: { typeOptions: [] },
            links: null
          })
        );
        done();
      });
    });

    it('should map to loadToolOverviewFailure', (done) => {
      const actionsMock = of(ToolActions.initToolOverview());
      const error = new Error('loadToolOverviewFailure');
      const toolServiceMock = {
        getItems: () => throwError(error),
        setType: jest.fn().mockImplementation(() => null) as any
      };

      ToolEffects.initOverview$(actionsMock, toolServiceMock as any).subscribe((action) => {
        expect(action).toEqual(ToolActions.loadToolOverviewFailure({ error }));
        done();
      });
    });
  });

  describe('initSettings$', () => {
    it('should map to loadToolSettingsSuccess', (done) => {
      const actionsMock = of(ToolActions.initToolSettings({ id: 42, toolType: ToolTypeEnum.TaggingPixel, isCreate: false }));
      const entity = {
        id: null,
        type: ToolTypeEnum.TaggingPixel,
        name: 'Effects Test',
        metaLabels: [],
        notes: '',
        useNotes: 0,
        links: null
      };
      const toolServiceMock = {
        getItem: (toolId: number, _copyId: number): Observable<ToolSettingsResponse> =>
          of({
            data: {
              entity: { ...entity, id: toolId },
              links: { edit: 'edit', overview: 'overview' },
              displayOptions: { accessApiKey: true, accessSmsMarketing: true },
              filterOptions: { custom: 'just for test purposes' }
            }
          }),
        setType: jest.fn().mockImplementation(() => null) as any
      } as ToolApiProxyService;
      const tagServiceMock = {
        getTags: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterTagsByTypeAndConvert: jest.fn().mockReturnValue(of([{ value: 1, text: 'Tag 1' }]))
      } as unknown as TagService;
      const labelServiceMock = {
        getLabels: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterLabelsByTypeAndConvert: jest.fn().mockReturnValue(of([{ value: 2, text: 'Label 2' }]))
      } as unknown as LabelService;
      const optInServiceMock = {
        getOptInIndex: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterOptInByTypeAndConvert: jest.fn().mockReturnValue(of([{ value: 3, text: 'Opt-In 3' }]))
      } as unknown as OptInService;
      const customFieldServiceMock = {
        getCustomFieldIndex: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterCustomFieldByTypeAndConvert: jest.fn().mockReturnValue(of([{ value: 4, text: 'field-1' }]))
      } as unknown as CustomFieldService;

      ToolEffects.initSettings$(actionsMock, toolServiceMock, tagServiceMock, labelServiceMock, optInServiceMock, customFieldServiceMock).subscribe((action) => {
        expect(action).toEqual(
          ToolActions.loadToolSettingsSuccess({
            entity: { ...entity, id: 42 },
            links: { edit: 'edit', overview: 'overview' },
            displayOptions: { accessApiKey: true, accessSmsMarketing: true },
            filterOptions: { custom: 'just for test purposes' },
            cacheOptions: {
              tagOptions: [{ value: 1, text: 'Tag 1' }],
              labelOptions: [{ value: 2, text: 'Label 2' }],
              optInOptions: [{ value: 3, text: 'Opt-In 3' }],
              customFieldOptions: [{ value: 4, text: 'field-1' }]
            }
          })
        );
        expect(toolServiceMock.setType).toHaveBeenCalledWith(ToolTypeEnum.TaggingPixel);
        done();
      });
    });

    it('should map to loadToolSettingsFailure', (done) => {
      const actionsMock = of(ToolActions.initToolSettings({ id: 42, toolType: ToolTypeEnum.TaggingPixel, isCreate: false }));
      const error = new Error('loadToolSettingsFailure');
      const toolServiceMock = {
        getItem: () => throwError(error),
        setType: jest.fn().mockImplementation(() => null)
      } as any;
      const tagServiceMock = {
        getTags: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterTagsByTypeAndConvert: jest.fn().mockReturnValue(of([]))
      } as unknown as TagService;
      const labelServiceMock = {
        getLabels: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterLabelsByTypeAndConvert: jest.fn().mockReturnValue(of([]))
      } as unknown as LabelService;
      const optInServiceMock = {
        getOptInIndex: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterOptInByTypeAndConvert: jest.fn().mockReturnValue(of([]))
      } as unknown as OptInService;
      const customFieldServiceMock = {
        getCustomFieldIndex: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterCustomFieldByTypeAndConvert: jest.fn().mockReturnValue(of([]))
      } as unknown as CustomFieldService;

      ToolEffects.initSettings$(actionsMock, toolServiceMock, tagServiceMock, labelServiceMock, optInServiceMock, customFieldServiceMock).subscribe((action) => {
        expect(action).toEqual(ToolActions.loadToolSettingsFailure({ error }));
        expect(toolServiceMock.setType).toHaveBeenCalledWith(ToolTypeEnum.TaggingPixel);
        done();
      });
    });
  });

  describe('createEntity$', () => {
    it('should map to createEntitySuccess for copyId 0', (done) => {
      const entity = {
        id: 42,
        type: ToolTypeEnum.TaggingPixel,
        name: 'Effects Test',
        metaLabels: ['label-1'],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(ToolActions.createEntity({ entity: { ...entity }, copyId: 0 }));

      const toolServiceMock = {
        create: (_entity: any, _copyId: any): Observable<ToolSettingsSaveResponse> => of({ id: 42, links: { edit: 'edit', overview: 'overview' } })
      } as ToolApiProxyService;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;
      const createSpy = jest.spyOn(toolServiceMock, 'create');

      ToolEffects.createEntity$(actionsMock, toolServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).toHaveBeenCalled();
        expect(action).toEqual(
          ToolActions.createEntitySuccess({
            id: 42,
            links: { edit: 'edit', overview: 'overview' },
            message: getToolComponentMessage(entity.type as ToolTypeEnum, 'createSuccess')
          })
        );
        expect(createSpy).toHaveBeenCalledWith(entity, 0);
        done();
      });
    });

    it('should map to createEntitySuccess for copyId 456', (done) => {
      const entity = {
        id: 42,
        type: ToolTypeEnum.TaggingPixel,
        name: 'Effects Test',
        metaLabels: ['label-1'],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(ToolActions.createEntity({ entity: { ...entity }, copyId: 456 }));

      const toolServiceMock = {
        create: (_entity: any, _copyId: any): Observable<ToolSettingsSaveResponse> => of({ id: 42, links: { edit: 'edit', overview: 'overview' } })
      } as ToolApiProxyService;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;
      const createSpy = jest.spyOn(toolServiceMock, 'create');

      ToolEffects.createEntity$(actionsMock, toolServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).toHaveBeenCalled();
        expect(action).toEqual(
          ToolActions.createEntitySuccess({
            id: 42,
            links: { edit: 'edit', overview: 'overview' },
            message: getToolComponentMessage(entity.type as ToolTypeEnum, 'createSuccess')
          })
        );
        expect(createSpy).toHaveBeenCalledWith(entity, 456);
        done();
      });
    });

    it('should map to createEntityFailure', (done) => {
      const entity = {
        id: 42,
        type: ToolTypeEnum.TaggingPixel,
        name: 'Effects Test',
        metaLabels: [],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(ToolActions.createEntity({ entity: { ...entity }, copyId: 0 }));
      const error = new Error('createEntityFailure');
      const toolServiceMock = {
        create: () => throwError(error)
      } as any;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;

      ToolEffects.createEntity$(actionsMock, toolServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).not.toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).not.toHaveBeenCalled();
        expect(action).toEqual(ToolActions.createEntityFailure({ error }));
        done();
      });
    });
  });

  describe('saveEntity$', () => {
    it('should map to saveEntitySuccess', (done) => {
      const entity = {
        id: 42,
        type: ToolTypeEnum.TaggingPixel,
        name: 'Effects Test',
        metaLabels: ['label-1'],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(ToolActions.saveEntity({ entity: { ...entity } }));

      const toolServiceMock = {
        save: (_: any, __: any): Observable<ToolSettingsSaveResponse> => of({ id: 42, links: { edit: 'edit', overview: 'overview' } })
      } as ToolApiProxyService;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;

      ToolEffects.saveEntity$(actionsMock, toolServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).toHaveBeenCalled();
        expect(action).toEqual(
          ToolActions.saveEntitySuccess({
            id: 42,
            links: { edit: 'edit', overview: 'overview' },
            message: getToolComponentMessage(entity.type as ToolTypeEnum, 'saveSuccess')
          })
        );
        done();
      });
    });

    it('should map to saveEntityFailure', (done) => {
      const entity = {
        id: 42,
        type: ToolTypeEnum.TaggingPixel,
        name: 'Effects Test',
        metaLabels: [],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(ToolActions.saveEntity({ entity: { ...entity } }));
      const error = new Error('saveEntityFailure');
      const toolServiceMock = {
        save: () => throwError(error)
      } as any;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;

      ToolEffects.saveEntity$(actionsMock, toolServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).not.toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).not.toHaveBeenCalled();
        expect(action).toEqual(ToolActions.saveEntityFailure({ error }));
        done();
      });
    });
  });

  describe('deleteEntity$', () => {
    it('should map to deleteEntitySuccess with message from api', (done) => {
      const actionsMock = of(ToolActions.deleteEntity({ id: 42, toolType: ToolTypeEnum.TaggingPixel }));

      const toolServiceMock = {
        delete: (_: any): Observable<ToolDeleteResponse> => of({ data: { message: 'Message From API' } })
      } as ToolApiProxyService;

      ToolEffects.deleteEntity$(actionsMock, toolServiceMock).subscribe((action) => {
        expect(action).toEqual(
          ToolActions.deleteEntitySuccess({
            message: 'Message From API'
          })
        );
        done();
      });
    });

    it('should map to deleteEntitySuccess with fallback message', (done) => {
      const actionsMock = of(ToolActions.deleteEntity({ id: 42, toolType: ToolTypeEnum.TaggingPixel }));

      const toolServiceMock = {
        delete: (_: any): Observable<ToolDeleteResponse> => of({ data: { message: null } })
      } as ToolApiProxyService;

      ToolEffects.deleteEntity$(actionsMock, toolServiceMock).subscribe((action) => {
        expect(action).toEqual(
          ToolActions.deleteEntitySuccess({
            message: 'Tool::TaggingPixels::Message::Delete Success'
          })
        );
        done();
      });
    });

    it('should map to deleteEntityFailure', (done) => {
      const actionsMock = of(ToolActions.deleteEntity({ id: 42, toolType: ToolTypeEnum.TaggingPixel }));
      const error = new Error('deleteEntityFailure');
      const toolServiceMock = {
        delete: () => throwError(error)
      };

      ToolEffects.deleteEntity$(actionsMock, toolServiceMock as any).subscribe((action) => {
        expect(action).toEqual(ToolActions.deleteEntityFailure({ error }));
        done();
      });
    });
  });

  describe('createSuccessMessage$', () => {
    it('should map createEntitySuccess to initToolSettings', (done) => {
      const actionsMock = of(ToolActions.createEntitySuccess({ id: 42, links: { edit: '/tool/generic/settings/1/2' }, message: 'Create Success' }));
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;
      const translateServiceMock = { translate: jest.fn().mockImplementation((message) => message) } as any;
      const navigationServiceMock = { routeTo: jest.fn().mockImplementation(() => null) } as any;

      ToolEffects.createSuccessMessage$(actionsMock, messageServiceMock, translateServiceMock, navigationServiceMock).subscribe((action) => {
        expect(action).toEqual(null);
        expect(messageServiceMock.success).toHaveBeenCalledWith('Create Success');
        expect(navigationServiceMock.routeTo).toHaveBeenCalledWith('/tool/generic/settings/1/2');
        done();
      });
    });
  });

  describe('saveSuccessMessage$', () => {
    it('should map saveEntitySuccess to initToolSettings', (done) => {
      const actionsMock = of(ToolActions.saveEntitySuccess({ id: 42, links: {}, message: 'Save Success' }));
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;
      const translateServiceMock = { translate: jest.fn().mockImplementation((message) => message) } as any;
      const toolProxyServiceMock = { getType: jest.fn().mockImplementation(() => ToolTypeEnum.TaggingPixel) } as any;

      ToolEffects.saveSuccessMessage$(actionsMock, toolProxyServiceMock, messageServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(
          ToolActions.initToolSettings({
            id: 42,
            toolType: ToolTypeEnum.TaggingPixel,
            isCreate: false
          })
        );
        expect(messageServiceMock.success).toHaveBeenCalledWith('Save Success');
        done();
      });
    });
  });

  describe('deleteSuccessMessage$', () => {
    it('should map deleteEntitySuccess to navigateToOverview', (done) => {
      const actionsMock = of(ToolActions.deleteEntitySuccess({ message: 'Delete Success' }));
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;
      const translateServiceMock = { translate: jest.fn().mockImplementation((message) => message) } as any;

      ToolEffects.deleteSuccessMessage$(actionsMock, messageServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(ToolActions.navigateToOverview());
        expect(messageServiceMock.success).toHaveBeenCalledWith('Delete Success');
        done();
      });
    });
  });

  describe('navigateToOverview$', () => {
    it('should call the navigation service for navigateToOverview', (done) => {
      const dispatchAction = ToolActions.navigateToOverview();
      const toolFacadeMock = { settingsLinks$: of({ overview: '/tool/overview/me' }) } as any;
      const navigationServiceMock = { routeTo: jest.fn().mockImplementation(() => null) } as any;

      ToolEffects.navigateToOverview$(of(dispatchAction), toolFacadeMock, navigationServiceMock).subscribe((action) => {
        expect(action).toEqual([dispatchAction, { overview: '/tool/overview/me' }]);
        expect(navigationServiceMock.routeTo).toHaveBeenCalledWith('/tool/overview/me');
        done();
      });
    });
  });

  describe('apiError$', () => {
    it('should call the error service for loadToolOverviewFailure', (done) => {
      const error = new Error('Overview Error');
      const dispatchAction = ToolActions.loadToolOverviewFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      ToolEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should call the error service for loadToolSettingsFailure', (done) => {
      const error = new Error('Settings Error');
      const dispatchAction = ToolActions.loadToolSettingsFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      ToolEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should call the error service for createEntityFailure', (done) => {
      const error = new Error('Entity Create Error');
      const dispatchAction = ToolActions.createEntityFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      ToolEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should call the error service for saveEntityFailure', (done) => {
      const error = new Error('Entity Save Error');
      const dispatchAction = ToolActions.saveEntityFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      ToolEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should call the error service for deleteEntityFailure', (done) => {
      const error = new Error('Entity Delete Error');
      const dispatchAction = ToolActions.deleteEntityFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      ToolEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should skip 406 for loadToolSettingsFailure', (done) => {
      const error = new Error('Settings Error');
      (error as any).status = 406;

      const dispatchAction = ToolActions.loadToolSettingsFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      ToolEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).not.toHaveBeenCalled();
        done();
      });
    });
  });
});

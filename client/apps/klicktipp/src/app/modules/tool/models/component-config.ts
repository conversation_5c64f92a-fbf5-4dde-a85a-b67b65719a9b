import { Type } from '@angular/core';
import { ModalData } from '@klicktipp/kt-mat-library';
import { ToolBaseOverviewDirective } from '../general/tool-base-overview.directive';
import { ToolBaseSettingsDirective } from '../general/tool-base-settings.directive';

export interface ToolUpsellDialogConfig {
  optionsKey: string;
  modalData: ModalData;
}

export interface ToolDependenciesCheckConfig {
  title: string;
  message: string;
}

export interface ToolComponentData {
  key: string;
  title?: {
    create?: string;
    edit?: string;
  };
  message?: {
    createSuccess?: string;
    saveSuccess?: string;
    deleteSuccess?: string;
    saveAndTestSuccess?: string;
  };
  upsellDialogConfig?: ToolUpsellDialogConfig;
  dependenciesCheckConfig?: ToolDependenciesCheckConfig;
}

export type SingleComponent = Type<ToolBaseSettingsDirective>;
export type SplitComponent = {
  create: Type<ToolBaseSettingsDirective>;
  edit: Type<ToolBaseSettingsDirective>;
};
export type ComponentType = SingleComponent | SplitComponent;

export interface ToolComponentConfig {
  component: ComponentType;
  apiPath?: string;
  data: ToolComponentData;
}

export interface ComponentConfig {
  component: Type<ToolBaseSettingsDirective | ToolBaseOverviewDirective>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  inputs: Record<string, any>;
}

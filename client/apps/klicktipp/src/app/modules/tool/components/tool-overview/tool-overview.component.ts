import { AsyncPipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faPlus } from '@fortawesome/pro-regular-svg-icons';
import { KtButtonComponent, KtNavigationService, KtNotificationComponent, KtTableComponent, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { BehaviorSubject, switchMap } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { ToolFacade } from '../../+state/tool.facade';
import { TabTitleService } from '../../../../services/tab-title.service';
import { OnReadyBaseComponent } from '../../../forms.shared/components/on-ready/on-ready-base.component';
import { ToolOverviewCreateType } from '../../models/tool-api';
import { ToolOverviewTableService } from '../../services/tool-overview-table.service';
import { FilterChangeEvent, ToolOverviewFilterPanelComponent } from './tool-overview-filter-panel/tool-overview-filter-panel.component';

@Component({
  selector: 'kt-tool-overview',
  imports: [AsyncPipe, KtTableComponent, TranslatePipe, KtNotificationComponent, KtButtonComponent, ToolOverviewFilterPanelComponent],
  templateUrl: './tool-overview.component.html'
})
export class ToolOverviewComponent extends OnReadyBaseComponent implements OnInit {
  protected readonly title = this.translateService.translate('Tool::Overview::Tool Overview');
  private readonly toolFacade = inject(ToolFacade);
  private readonly tableService = inject(ToolOverviewTableService);
  private readonly navigationService = inject(KtNavigationService);
  private readonly tabTitleService = inject(TabTitleService);

  iconPlus: IconProp = faPlus as IconProp;

  filter$ = new BehaviorSubject<FilterChangeEvent>({ type: null });
  filteredTableItem$ = this.filter$.asObservable().pipe(
    switchMap((filter) => {
      return this.toolFacade.overviewEntities$.pipe(
        map((items) => {
          const filteredItems = items.filter((item) => {
            let passFilter = true;

            if (filter.type) {
              passFilter = item.type === filter.type;
            }

            return passFilter;
          });
          return this.tableService.createOverviewTable(filteredItems);
        })
      );
    })
  );

  createTypes$ = this.toolFacade.overviewCreateTypes$;
  loaded$ = this.toolFacade.overviewLoaded$.pipe(tap((loaded) => this.setReady(loaded)));

  typeOptions$ = this.toolFacade.overviewFilterOptions$.pipe(map((options) => options?.typeOptions ?? []));

  constructor(protected translateService: TranslateService) {
    super(translateService, 'tool-overview');
  }

  ngOnInit(): void {
    this.tabTitleService.setTabName(this.title);
    this.toolFacade.initOverview();
  }

  addTool(btn: ToolOverviewCreateType): void {
    this.navigationService.routeTo(btn.createUrl);
  }

  onFilterChange(filter: FilterChangeEvent): void {
    this.filter$.next(filter);
  }
}

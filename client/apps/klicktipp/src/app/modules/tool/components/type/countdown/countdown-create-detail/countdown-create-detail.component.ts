import { NgOptimizedImage } from '@angular/common';
import { AfterViewInit, Component, DOCUMENT, inject, OnInit, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { faCheck, faChevronLeft, faCopy, faTrashAlt } from '@fortawesome/pro-regular-svg-icons';
import { BeeFileManagerDialogComponent } from '@klicktipp/bee-file-manager';
import { FileManagerEntity } from '@klicktipp/bee-shared';
import {
  CheckBoxItem,
  DatepickerData,
  InputItem,
  KtButtonComponent,
  KtCheckboxComponent,
  KtDatepickerComponent,
  ktDatepickerFormatter,
  KtInputComponent,
  KtLabelComponent,
  KtMagicSelectComponent,
  KtMessageService,
  KtPanelComponent,
  KtSelectComponent,
  KtStaticMessageComponent,
  MagicSelectItem,
  NotificationType,
  SelectItem,
  SelectOption,
  StaticMessageItem,
  TranslatePipe,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { debounceTime, map, skip, switchMap, take } from 'rxjs/operators';
import { ErrorItems } from '../../../../../../models/error/custom-error';
import { GeneralValidationErrors } from '../../../../../../models/error/error.interface';
import { EntityConditionItem } from '../../../../../../models/shared-models';
import { TagService } from '../../../../../../services/entities/tag.service';
import { SentryService } from '../../../../../../services/sentry.service';
import { EntityCreateComponent } from '../../../../../forms.shared/components/entity-create/entity-create.component';
import { EntityCreateFormKeyEnum } from '../../../../../forms.shared/components/entity-create/entity-create.models';
import { ToolBaseSettingsDirective } from '../../../../general/tool-base-settings.directive';
import { ToolTypeEnum } from '../../../../general/tool-component-map';
import { ToolSettingsHeaderComponent } from '../../../tool-settings-header/tool-settings-header.component';
import { CountdownTerminationTagDurationComponent } from '../countdown-termination-tag-duration/countdown-termination-tag-duration.component';
import { CountdownEntity, CountdownFilterOptions, TemplateSelectOption, TerminationType } from '../countdown.models';
import { CountdownValidationError } from './countdown-validation-error.interface';

@Component({
  selector: 'kt-countdown-create-detail',
  imports: [
    ReactiveFormsModule,
    EntityCreateComponent,
    KtButtonComponent,
    ToolSettingsHeaderComponent,
    BeeFileManagerDialogComponent,
    KtSelectComponent,
    NgOptimizedImage,
    KtDatepickerComponent,
    KtMagicSelectComponent,
    TranslatePipe,
    KtLabelComponent,
    CountdownTerminationTagDurationComponent,
    KtPanelComponent,
    KtInputComponent,
    KtCheckboxComponent,
    KtStaticMessageComponent
  ],
  templateUrl: './countdown-create-detail.component.html'
})
export class CountdownCreateDetailComponent extends ToolBaseSettingsDirective<CountdownEntity, CountdownFilterOptions> implements OnInit, AfterViewInit {
  protected readonly tagService = inject(TagService);
  protected readonly document = inject(DOCUMENT);
  protected readonly messageService = inject(KtMessageService);
  private readonly sentryService = inject(SentryService);

  get isSubscriberParameterNameInputHidden(): boolean {
    return (this.form.get(this.showSubscriberParameterName.id)?.value ?? false) === false;
  }

  get isEmailParameterNameInputHidden(): boolean {
    return (this.form.get(this.showEmailParameterName.id)?.value ?? false) === false;
  }

  iconCheck = faCheck;
  iconChevronLeft = faChevronLeft;
  iconTrash = faTrashAlt;
  iconCopy = faCopy;

  backButtonText = this.translateService.translate('Tool::Countdown::CreateDetail::Button::Back');
  createButtonText = this.translateService.translate('Tool::Countdown::CreateDetail::Button::Create');
  deleteButtonText = this.translateService.translate('Tool::Countdown::CreateDetail::Button::Delete');
  copyButtonText = this.translateService.translate('Tool::Countdown::CreateDetail::Button::Copy');
  saveButtonText = this.translateService.translate('Tool::Countdown::CreateDetail::Button::Save');

  tags: SelectOption[] = [];
  selectedTags: SelectOption[] = [];

  tagCategorySelectField: SelectItem = {
    options: [],
    id: 'tagCategory',
    labelText: this.translateService.translate('Tool::Countdown::CreateDetail::TagCategory::SelectLabel::Tag Category')
  };
  taggingSelectItem: MagicSelectItem = {
    id: 'terminationTag',
    labelText: this.translateService.translate('Tool::Countdown::CreateDetail::TerminationTag::SelectLabel::Termination Tag'),
    hideTagMessage: true,
    restrictedToOneValue: true
  };
  templateSelectField: SelectItem = {
    options: [],
    id: 'template',
    labelText: this.translateService.translate('Tool::Countdown::CreateDetail::Template::SelectLabel::Template')
  };
  terminationTypeSelectField: SelectItem = {
    options: [],
    id: 'terminationType',
    labelText: this.translateService.translate('Tool::Countdown::CreateDetail::TerminationType::SelectLabel::Termination Type')
  };
  terminationDateTime: DatepickerData = {
    id: 'terminationDateTime',
    label: this.translateService.translate('Tool::Countdown::TerminationDateTime::Date'),
    timePickerHoursFormName: 'terminationDateTimeHours',
    timePickerMinutesFormName: 'terminationDateTimeMinutes',
    type: 'datetime',
    hoursLabel: this.translateService.translate('Tool::Countdown::TerminationDateTime::Time'),
    step: 1
  };
  terminationCustomfieldSelectField: SelectItem = {
    options: [],
    id: 'terminationCustomfield',
    labelText: this.translateService.translate('Tool::Countdown::TerminationCustomfield::SelectLabel::Customfield')
  };
  customExpiredImageInput: InputItem = {
    id: 'customImageUrl',
    type: 'text'
  };
  targetUrlInput: InputItem = {
    id: 'targetUrl',
    type: 'text',
    labelText: this.translateService.translate('Tool::Countdown::CustomExpiredImage::Label::Target URL'),
    descriptionText: this.translateService.translate('Tool::Countdown::CustomExpiredImage::Description::Target URL Description')
  };
  targetUrlExpiredInput: InputItem = {
    id: 'targetExpiredUrl',
    type: 'text',
    labelText: this.translateService.translate('Tool::Countdown::CustomExpiredImage::Label::Target Expired URL'),
    descriptionText: this.translateService.translate('Tool::Countdown::CustomExpiredImage::Description::Target Expired URL Description')
  };
  subscriberParameterNameInput: InputItem = {
    id: 'subscriberParameterName',
    type: 'text',
    placeholder: this.translateService.translate('Tool::Countdown::SubscriberParameterName::Placeholder::e.g. "SubscriberID"')
  };
  emailParameterNameInput: InputItem = {
    id: 'emailParameterName',
    type: 'text',
    placeholder: this.translateService.translate('Tool::Countdown::EmailParameterName::Placeholder::e.g. "email"')
  };
  showSubscriberParameterName: CheckBoxItem = {
    id: 'useSubscriberParameter',
    checkboxText: this.translateService.translate('Tool::Countdown::UseSubscriberParameter::CheckboxText::Use Subscriber Parameter')
  };
  showEmailParameterName: CheckBoxItem = {
    id: 'useEmailParameter',
    checkboxText: this.translateService.translate('Tool::Countdown::UseEmailParameter::CheckboxText::Use Email Parameter')
  };

  terminationType: TerminationType;
  terminationTypeEnum = TerminationType;
  templatePreviewSrc: string;

  terminationDateTimeComp = viewChild<KtDatepickerComponent>('terminationDateTimeComponent');
  terminationTagDurationComp = viewChild<CountdownTerminationTagDurationComponent>('terminationTagDurationComponent');

  fileManagerStaticError: StaticMessageItem | null;

  constructor(protected translateService: TranslateService) {
    super(translateService, 'tool-countdown-create-detail');
  }

  ngOnInit(): void {
    this.toolFacade.settingsError$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((error) => this.handleError(error));

    const filterOptions = this.filterOptions();
    this.templateSelectField = { ...this.templateSelectField, options: filterOptions?.templateOptions ?? [] };
    this.terminationTypeSelectField = { ...this.terminationTypeSelectField, options: filterOptions?.terminationTypeOptions ?? [] };
    this.tagCategorySelectField = { ...this.tagCategorySelectField, options: filterOptions?.tagCategoryOptions ?? [] };
    this.customExpiredImageInput = { ...this.customExpiredImageInput, placeholder: filterOptions?.defaultExpiredImage };

    const cacheOptions = this.cacheOptions();
    this.terminationCustomfieldSelectField = { ...this.terminationCustomfieldSelectField, options: cacheOptions?.customFieldOptions ?? [] };
  }

  ngAfterViewInit(): void {
    const routeData = this.routeData();
    if (!routeData.isCreate) {
      this.listenTerminationTypeChanges();
      this.listenTemplateChanges();
      this.listenTagCategoryChanges();
    }

    this.checkForUpsellDialog();

    setTimeout(() => this.setFormValues(!!routeData.isCreate));
  }

  back(): void {
    this.navigationService.routeTo(this.links().overview);
  }

  copyEntity(): void {
    const entity = this.entity();
    if (entity?.links?.copy) {
      this.navigationService.routeTo(entity.links.copy);
    }
  }

  createEntity(): void {
    const entity = this.mapFormToEntity();
    this.toolFacade.create({ entity, copyId: this.getCopyId() });
  }

  updateEntity(): void {
    const formData = this.mapFormToEntity();
    this.toolFacade.save(formData);
  }

  deleteEntity(): void {
    const { id, name, type } = this.entity();
    this.toolFacade.deleteWithDependenciesCheck(id, name, type as ToolTypeEnum);
  }

  setFormValues(isCreate: boolean): void {
    if (!isCreate) {
      const entity = this.entity();

      const terminationType = entity?.terminationType ?? TerminationType.FIXED;
      const terminationTypeOption = this.terminationTypeSelectField.options.find((option) => option.value === terminationType);
      if (terminationTypeOption) {
        this.form.get(this.terminationTypeSelectField.id).patchValue(terminationTypeOption);
      }

      const categoryOption = this.tagCategorySelectField.options.find((option) => option.value === 'tag');
      if (categoryOption) {
        this.form.get(this.tagCategorySelectField.id).patchValue(categoryOption);
      }

      const template = entity?.template ?? 'round_orange';
      const templateOption: TemplateSelectOption = this.templateSelectField.options.find((option) => option.value === template);
      if (templateOption) {
        this.form.get(this.templateSelectField.id).patchValue(templateOption);
      }

      this.form.get(this.customExpiredImageInput.id).patchValue(entity?.customExpiredImage ?? '');
      this.form.get(this.targetUrlInput.id).patchValue(entity?.targetUrl ?? '');
      this.form.get(this.targetUrlExpiredInput.id).patchValue(entity?.targetUrlExpired ?? '');
      this.form.get(this.showSubscriberParameterName.id).patchValue(entity?.subscriberParameterName?.length > 0);
      this.form.get(this.subscriberParameterNameInput.id).patchValue(entity?.subscriberParameterName ?? '');
      this.form.get(this.showEmailParameterName.id).patchValue(entity?.emailParameterName?.length > 0);
      this.form.get(this.emailParameterNameInput.id).patchValue(entity?.emailParameterName ?? '');
    }

    this.setReady(true);
    this.cdr.detectChanges();
  }

  onInsertFile(entity: FileManagerEntity): void {
    this.form.get(this.customExpiredImageInput.id).patchValue(entity['public-url'] ?? null);
  }

  onFileManagerError(error: Error): void {
    console.warn(error);
    this.sentryService.captureException(error, { component: 'kt-bee-file-manager-dialog' });
    const message = this.translateService.translate('BeeFileManagerError::The Editor is currently not available. Please reload or try again later!');
    this.fileManagerStaticError = { type: NotificationType.Warn, nodeId: 'BeeFileManagerError', message };
  }

  private mapFormToEntity(): CountdownEntity {
    const notesEnabled = this.form.get(EntityCreateFormKeyEnum.NotesEnabled)?.value;

    if (this.routeData().isCreate) {
      return {
        ...this.entity(),
        name: this.form.get(EntityCreateFormKeyEnum.Name).value,
        metaLabels: this.form.get(EntityCreateFormKeyEnum.Labels)?.value ?? [],
        notes: notesEnabled && this.form.get(EntityCreateFormKeyEnum.Notes)?.value ? this.form.get(EntityCreateFormKeyEnum.Notes)?.value : ''
      };
    }

    const terminationDateTime = this.terminationDateTimeComp()?.getDatetimeString() ?? null;
    const terminationTagIDs: EntityConditionItem = {
      ids: this.selectedTags.filter((t) => t.value).map((t) => t.value as number),
      new: this.selectedTags.filter((t) => !t.value).map((t) => t.text)
    };
    const terminationTagDuration = this.terminationTagDurationComp()?.mapToApiData() ?? null;

    return {
      ...this.entity(),
      ...terminationTagDuration,
      name: this.form.get(EntityCreateFormKeyEnum.Name).value,
      metaLabels: this.form.get(EntityCreateFormKeyEnum.Labels)?.value ?? [],
      notes: notesEnabled && this.form.get(EntityCreateFormKeyEnum.Notes)?.value ? this.form.get(EntityCreateFormKeyEnum.Notes)?.value : '',
      template: this.form.get(this.templateSelectField.id)?.value?.value ?? '',
      terminationType: this.form.get(this.terminationTypeSelectField.id)?.value?.value ?? TerminationType.FIXED,
      terminationDateTime,
      terminationCustomFieldID: this.form.get(this.terminationCustomfieldSelectField.id)?.value?.value ?? '',
      terminationTagIDs,
      customExpiredImage: this.form.get(this.customExpiredImageInput.id)?.value ?? '',
      targetURL: this.form.get(this.targetUrlInput.id)?.value ?? '',
      targetURLExpired: this.form.get(this.targetUrlExpiredInput.id)?.value ?? '',
      subscriberParameterName: this.form.get(this.subscriberParameterNameInput.id)?.value ?? '',
      emailParameterName: this.form.get(this.emailParameterNameInput.id)?.value ?? ''
    };
  }

  private handleError(error: Error | ErrorItems | string | null): void {
    this.form.markAllAsTouched();
    if (!error) return;
    if (error instanceof ErrorItems) {
      const errors = new ErrorItems([]);
      for (const errorItem of error.errors) {
        let addError = true;
        switch (errorItem.code) {
          case GeneralValidationErrors.CreateFailed:
          case GeneralValidationErrors.UpdateFailed:
          case GeneralValidationErrors.DeleteFailed:
            break;
          case CountdownValidationError.EmptyName:
          case CountdownValidationError.NameLengthExceeded:
          case CountdownValidationError.DuplicateName:
            this.form.get(EntityCreateFormKeyEnum.Name).setErrors({ customError: errorItem.message });
            break;
          case CountdownValidationError.TargetUrlEmpty:
          case CountdownValidationError.TargetUrlBlacklist:
          case CountdownValidationError.TargetUrlInvalid:
            this.form.get(this.targetUrlInput.id)?.setErrors({ customError: errorItem.message });
            break;
          case CountdownValidationError.TargetUrlExpiredBlacklist:
          case CountdownValidationError.TargetUrlExpiredInvalid:
            this.form.get(this.targetUrlExpiredInput.id)?.setErrors({ customError: errorItem.message });
            break;
          case CountdownValidationError.EmptyTerminationDateCustomField:
          case CountdownValidationError.TerminationCustomfieldNotFound:
          case CountdownValidationError.TerminationCustomfieldWrongType:
            this.form.get(this.terminationCustomfieldSelectField.id)?.setErrors({ customError: errorItem.message });
            break;
          case CountdownValidationError.EmptyTerminationDate:
          case CountdownValidationError.TerminationDateTimeFuture:
          case CountdownValidationError.TerminationDateTimeInvalidDateTime:
          case CountdownValidationError.TerminationDateTimeTooFarFuture:
            this.form.get(this.terminationDateTime.id)?.setErrors({ customError: errorItem.message });
            break;
          case CountdownValidationError.TerminationTagDurationDays:
          case CountdownValidationError.TerminationTagDurationHours:
            this.terminationTagDurationComp()?.setErrors({ customError: errorItem.message });
            break;
          case CountdownValidationError.EmptyTerminationTags:
          case CountdownValidationError.TerminationTagNotFound:
            this.form.get(this.taggingSelectItem.id)?.setErrors({ customError: errorItem.message });
            break;
          case CountdownValidationError.CustomImageUrlInvalid:
            this.form.get(this.customExpiredImageInput.id)?.setErrors({ customError: errorItem.message });
            break;
          default:
            errors.errors.push({ message: `Unknown error (${errorItem.code})`, code: errorItem.code });
            addError = false;
            break;
        }
        if (addError) {
          errors.errors.push(errorItem);
        } else {
          this.errorBaseService.handleError(errors);
        }
      }
      this.errorBaseService.handleFormatError(errors);
    } else {
      this.errorBaseService.handleError(error);
    }
  }

  private listenTerminationTypeChanges(): void {
    this.form
      .get(this.terminationTypeSelectField.id)
      .valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((option: SelectOption) => {
        this.terminationType = (option?.value as TerminationType) ?? null;
        this.setTerminationTypeFields();
      });
  }

  private listenTemplateChanges(): void {
    this.form
      .get(this.templateSelectField.id)
      .valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((option: TemplateSelectOption) => {
        this.templatePreviewSrc = option?.previewUrl ?? null;
      });
  }

  private listenTagCategoryChanges(): void {
    this.form
      .get(this.tagCategorySelectField.id)
      .valueChanges.pipe(
        skip(1),
        debounceTime(150),
        switchMap((categoryOption: SelectOption) => {
          return this.tagService.filterTagsByTypeAndConvert(categoryOption?.value ?? '');
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((tags) => {
        this.tags = tags;
        this.selectedTags = [];
      });
  }

  private setTerminationTypeFields(): void {
    switch (this.terminationType) {
      case TerminationType.FIXED:
        this.setTerminationDateTime();
        break;
      case TerminationType.TAGGING:
        this.setTerminationTags();
        this.setTerminationTagDuration();
        break;
      case TerminationType.CUSTOMFIELD:
        this.setTerminationCustomField();
        break;
    }
  }

  private setTerminationDateTime(): void {
    const terminationDateTime = this.entity()?.terminationDateTime;
    if (terminationDateTime) {
      this.terminationDateTimeComp().setDate(terminationDateTime);
    } else {
      const newInternalDate = new Date();
      newInternalDate.setHours(0);
      newInternalDate.setMinutes(0);
      const dateTimeString = ktDatepickerFormatter(newInternalDate);
      this.terminationDateTimeComp().setTime(dateTimeString);
    }
  }

  private setTerminationTags(): void {
    const terminationTagIds = this.entity()?.terminationTagIds?.ids ?? [];
    if (terminationTagIds) {
      this.tagService
        .filterTagsByTypeAndConvert('')
        .pipe(
          map((tags) => tags.filter((tag) => terminationTagIds.includes(+tag.value))),
          take(1)
        )
        .subscribe((tags) => (this.selectedTags = tags));
    }
  }

  private setTerminationTagDuration(): void {
    const entity = this.entity();
    const days = entity?.terminationTagDurationDays ?? 0;
    const hours = entity?.terminationTagDurationHours ?? 1;
    this.terminationTagDurationComp().setDuration(days, hours);
  }

  private setTerminationCustomField(): void {
    const customFieldId = this.entity()?.terminationCustomFieldId;
    if (!customFieldId) {
      return;
    }
    const customFieldOption = this.terminationCustomfieldSelectField.options.find((option) => option.value === customFieldId);
    if (!customFieldOption) {
      return;
    }
    this.form.get(this.terminationCustomfieldSelectField.id).patchValue(customFieldOption);
  }
}

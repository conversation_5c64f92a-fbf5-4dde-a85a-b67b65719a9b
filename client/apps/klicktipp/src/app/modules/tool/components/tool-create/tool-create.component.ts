import { AsyncPipe, DOCUMENT, NgOptimizedImage } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { KtNavigationService, KtNotificationComponent, TranslateService } from '@klicktipp/kt-mat-library';
import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged, map, tap } from 'rxjs/operators';
import { ToolFacade } from '../../+state/tool.facade';
import { OnReadyBaseComponent } from '../../../forms.shared/components/on-ready/on-ready-base.component';
import { ToolIconBreakPoint, ToolIconSize } from '../../general/tool-icons';
import { ToolOverviewCreateOptionsGroup } from '../../models/tool-api';

@Component({
  selector: 'kt-tool-create',
  imports: [AsyncPipe, KtNotificationComponent, NgOptimizedImage],
  templateUrl: './tool-create.component.html',
  styleUrl: 'tool-create.component.scss'
})
export class ToolCreateComponent extends OnReadyBaseComponent implements OnInit {
  private readonly toolFacade = inject(ToolFacade);
  private readonly navigationService = inject(KtNavigationService);
  private readonly document = inject(DOCUMENT);

  createGroups$ = this.toolFacade.overviewCreateOptions$.pipe(map((optionGroups) => Object.values(optionGroups) as ToolOverviewCreateOptionsGroup[]));
  loaded$ = this.toolFacade.overviewLoaded$.pipe(tap((loaded) => this.setReady(loaded)));

  showIconTitle = true;
  toolIconSize$: Observable<number>;
  ToolIconSize = ToolIconSize;
  private toolIconSizeState$ = new BehaviorSubject<number>(ToolIconSize.LARGE);

  constructor(protected translateService: TranslateService) {
    super(translateService, 'tool-create');
    this.toolIconSize$ = this.toolIconSizeState$.asObservable().pipe(distinctUntilChanged());
  }

  ngOnInit(): void {
    this.toolFacade.initOverview();
    this.initWindowWidthListener();
  }

  onIconClick(url: string): void {
    this.navigationService.routeTo(url);
  }

  private initWindowWidthListener(): void {
    const window = this.document.defaultView;
    this.updateToolIconSizeState(window.innerWidth);
    this.updateShowIcon(window.innerWidth);

    const onWindowResizeHandler = (): void => {
      this.updateToolIconSizeState(window.innerWidth);
      this.updateShowIcon(window.innerWidth);
    };
    window.addEventListener('resize', onWindowResizeHandler);
    this.destroyRef.onDestroy(() => window.removeEventListener('resize', onWindowResizeHandler));
  }

  private updateToolIconSizeState(innerWindowWidth: number): void {
    let size = ToolIconSize.LARGE;
    if (innerWindowWidth <= ToolIconBreakPoint.SMALL) {
      size = ToolIconSize.SMALL;
    } else if (innerWindowWidth <= ToolIconBreakPoint.MEDIUM) {
      size = ToolIconSize.MEDIUM;
    }
    this.toolIconSizeState$.next(size);
  }

  private updateShowIcon(innerWindowWidth: number): void {
    this.showIconTitle = innerWindowWidth > ToolIconBreakPoint.SMALL;
  }
}

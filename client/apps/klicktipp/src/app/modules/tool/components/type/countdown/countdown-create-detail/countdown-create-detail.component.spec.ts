import { ComponentRef } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { KtNavigationService, TranslateService } from '@klicktipp/kt-mat-library';
import { <PERSON><PERSON><PERSON><PERSON>ider } from 'ng-mocks';
import { BehaviorSubject, of } from 'rxjs';
import { ToolFacade } from '../../../../+state/tool.facade';
import { LabelCacheFacade } from '../../../../../../+state/cache/label/label-cache.facade';
import { TagCacheFacade } from '../../../../../../+state/cache/tag/tag-cache.facade';
import { LabelCacheFacadeMock, TagCacheFacadeMock } from '../../../../../../mocks/cache-facade.mock';
import { LabelServiceMock } from '../../../../../../mocks/label-service.mock';
import { TagServiceMock } from '../../../../../../mocks/tag-service.mock';
import { Config } from '../../../../../../models/entities/config.entity';
import { ErrorItem, ErrorItems } from '../../../../../../models/error/custom-error';
import { ConfigService } from '../../../../../../services/config.service';
import { LabelService } from '../../../../../../services/entities/label.service';
import { TagService } from '../../../../../../services/entities/tag.service';
import { ErrorBaseService } from '../../../../../../services/error-base.service';
import { ModalService } from '../../../../../../services/modal.service';
import { ToolTypeEnum } from '../../../../general/tool-component-map';
import { CountdownCreateDetailComponent } from './countdown-create-detail.component';

describe('CountdownCreateDetailComponent', () => {
  let component: CountdownCreateDetailComponent;
  let componentRef: ComponentRef<CountdownCreateDetailComponent>;
  let fixture: ComponentFixture<CountdownCreateDetailComponent>;
  let toolFacade: ToolFacade;
  const errorSubject = new BehaviorSubject<Error | ErrorItems | string | null>(null);

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CountdownCreateDetailComponent, NoopAnimationsModule],
      providers: [
        MockProvider(TranslateService),
        MockProvider(ToolFacade, { settingsError$: errorSubject.asObservable() }),
        MockProvider(KtNavigationService),
        MockProvider(ModalService),
        MockProvider(ErrorBaseService),
        MockProvider(ConfigService, {
          getConfig: (): Config =>
            ({
              account: {
                userSettings: {
                  EmailEditorManualSaveButton: true,
                  EntityMetaLabelsEnabled: true,
                  EntityNotesEnabled: true
                }
              }
            }) as Config
        }),
        MockProvider(TagService, {
          ...TagServiceMock,
          filterTagsByTypeAndConvert: () =>
            of([
              {
                value: 184,
                text: 'label2',
                link: 'https://localhost:4202/app/tag/settings/1/184'
              },
              {
                value: 183,
                text: 'label1',
                link: 'https://localhost:4202/app/tag/settings/1/183'
              },
              {
                value: 114,
                text: 'def',
                link: 'https://localhost:4202/app/tag/settings/1/114'
              },
              {
                value: 113,
                text: 'abc',
                link: 'https://localhost:4202/app/tag/settings/1/113'
              }
            ]),
          getMagicSelectOptions: () =>
            of([
              {
                value: 184,
                text: 'label2',
                link: 'https://localhost:4202/app/tag/settings/1/184'
              }
            ])
        }),
        MockProvider(LabelService, LabelServiceMock),
        MockProvider(TagCacheFacade, TagCacheFacadeMock),
        MockProvider(LabelCacheFacade, LabelCacheFacadeMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CountdownCreateDetailComponent);
    toolFacade = TestBed.inject(ToolFacade);
    component = fixture.componentInstance;
    componentRef = fixture.componentRef;
    componentRef.setInput('configData', { title: { create: 'Create Countdown', edit: 'Edit Countdown' } });
    componentRef.setInput('routeData', { isCreate: false });
    componentRef.setInput('entity', {
      id: 42,
      name: 'Test-Entity',
      metaLabels: [],
      notes: 'Test-Note',
      type: ToolTypeEnum.Countdown,
      links: {
        overview: `/tool/${ToolTypeEnum.Countdown}/overview/me`,
        copy: `/tool/${ToolTypeEnum.Countdown}/settings/me/create/42`
      }
    });
    componentRef.setInput('links', {
      overview: `/tool/${ToolTypeEnum.Countdown}/overview/me`
    });
  });

  it('should create', () => {
    expect(component).toBeInstanceOf(CountdownCreateDetailComponent);
  });

  it('should call checkForUpsellDialog initially', waitForAsync(async () => {
    const checkForUpsellDialogSpy = jest.spyOn(component, 'checkForUpsellDialog').mockImplementation(() => null);
    fixture.detectChanges();
    await fixture.whenStable();

    expect(checkForUpsellDialogSpy).toHaveBeenCalled();
  }));

  it('should set form values properly', waitForAsync(async () => {
    fixture.detectChanges();
    await fixture.whenStable();

    const form = component.form;
    expect(form.getRawValue()).toEqual({
      entityLabels: [],
      entityLabelsInput: '',
      entityName: 'Test-Entity',
      entityNotes: 'Test-Note',
      entityNotesEnabled: true,
      customImageUrl: '',
      useEmailParameter: false,
      emailParameterName: '',
      useSubscriberParameter: false,
      subscriberParameterName: '',
      tagCategory: null,
      targetExpiredUrl: '',
      targetUrl: '',
      template: null,
      terminationCustomfield: null,
      terminationDateTime: null,
      terminationDateTimeHours: null,
      terminationDateTimeMinutes: null,
      terminationTag: null,
      terminationTagDurationDays: null,
      terminationTagDurationHours: null,
      terminationTagInput: null,
      terminationType: null
    });
    expect(component.initFinished).toBe(true);
  }));

  it('should map the form values to an entity properly for create', waitForAsync(async () => {
    fixture.detectChanges();
    await fixture.whenStable();

    const createSpy = jest.spyOn(toolFacade, 'create').mockImplementation(() => null);
    component.createEntity();
    expect(createSpy).toHaveBeenCalledWith({
      entity: {
        id: 42,
        links: {
          overview: `/tool/${ToolTypeEnum.Countdown}/overview/me`,
          copy: `/tool/${ToolTypeEnum.Countdown}/settings/me/create/42`
        },
        metaLabels: [],
        name: 'Test-Entity',
        notes: 'Test-Note',
        type: ToolTypeEnum.Countdown,
        template: '',
        terminationType: 1,
        terminationDateTime: '01-01-2000 00:00',
        terminationCustomFieldID: '',
        terminationTagIDs: { ids: [], new: [] },
        customExpiredImage: '',
        targetURL: '',
        targetURLExpired: '',
        subscriberParameterName: '',
        emailParameterName: '',
        terminationTagDurationDays: 0,
        terminationTagDurationHours: 1
      },
      copyId: 0
    });
  }));

  it('should map the form values to an entity properly for update', waitForAsync(async () => {
    fixture.detectChanges();
    await fixture.whenStable();

    const saveSpy = jest.spyOn(toolFacade, 'save').mockImplementation(() => null);
    component.updateEntity();
    expect(saveSpy).toHaveBeenCalledWith({
      id: 42,
      links: {
        overview: `/tool/${ToolTypeEnum.Countdown}/overview/me`,
        copy: `/tool/${ToolTypeEnum.Countdown}/settings/me/create/42`
      },
      metaLabels: [],
      name: 'Test-Entity',
      notes: 'Test-Note',
      type: ToolTypeEnum.Countdown,
      template: '',
      terminationType: 1,
      terminationDateTime: '01-01-2000 00:00',
      terminationCustomFieldID: '',
      terminationTagIDs: { ids: [], new: [] },
      customExpiredImage: '',
      targetURL: '',
      targetURLExpired: '',
      subscriberParameterName: '',
      emailParameterName: '',
      terminationTagDurationDays: 0,
      terminationTagDurationHours: 1
    });
  }));

  it('should navigate on back', () => {
    const navigationService = fixture.debugElement.injector.get(KtNavigationService);
    const routeToSpy = jest.spyOn(navigationService, 'routeTo').mockImplementation(() => null);
    component.back();
    expect(routeToSpy).toHaveBeenCalledWith(`/tool/${ToolTypeEnum.Countdown}/overview/me`);
  });

  it('should navigate to copy create', () => {
    const navigationService = fixture.debugElement.injector.get(KtNavigationService);
    const routeToSpy = jest.spyOn(navigationService, 'routeTo').mockImplementation(() => null);
    component.copyEntity();
    expect(routeToSpy).toHaveBeenCalledWith(`/tool/${ToolTypeEnum.Countdown}/settings/me/create/42`);
  });

  it('should call delete with the entity id', () => {
    const deleteSpy = jest.spyOn(toolFacade, 'deleteWithDependenciesCheck').mockImplementation(() => null);

    component.deleteEntity();
    expect(deleteSpy).toHaveBeenCalledWith(42, 'Test-Entity', ToolTypeEnum.Countdown);
  });

  it('should call the error base service handleFormatError', waitForAsync(async () => {
    const errorBaseService = fixture.debugElement.injector.get(ErrorBaseService);
    const handleErrorSpy = jest.spyOn(errorBaseService, 'handleError').mockImplementation(() => null);
    const handleFormatErrorSpy = jest.spyOn(errorBaseService, 'handleFormatError').mockImplementation(() => null);
    fixture.detectChanges();
    await fixture.whenStable();

    const errorItem: ErrorItem = { code: '500', message: 'Internal fu' };
    const errorItems = new ErrorItems([errorItem]);
    errorSubject.next(errorItems);

    const expectedError: ErrorItem = { message: `Unknown error (${errorItem.code})`, code: errorItem.code };
    const expectedErrorItems = new ErrorItems([expectedError]);
    expect(handleErrorSpy).toHaveBeenCalledWith(expectedErrorItems);
    expect(handleFormatErrorSpy).toHaveBeenCalledWith(expectedErrorItems);
  }));
});

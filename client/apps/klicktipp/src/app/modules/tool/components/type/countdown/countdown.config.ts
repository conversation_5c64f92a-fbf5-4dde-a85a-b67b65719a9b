import { isTranslate<PERSON><PERSON> } from '../../../../../static/translate';
import { ToolComponentConfig } from '../../../models/component-config';
import { CountdownCreateDetailComponent } from './countdown-create-detail/countdown-create-detail.component';

const CountdownConfig: ToolComponentConfig = {
  component: CountdownCreateDetailComponent,
  data: {
    key: 'countdown-tool',
    title: {
      create: isTranslate<PERSON><PERSON>('Tool::Countdown::Title::Create Countdown'),
      edit: isTranslateKey('Tool::Countdown::Title::Edit Countdown')
    },
    message: {
      createSuccess: isTranslateKey('Tool::Countdown::Message::Create Success'),
      saveSuccess: isTranslateKey('Tool::Countdown::Message::Save Success'),
      deleteSuccess: isTranslateKey('Tool::Countdown::Message::Delete Success')
    },
    dependenciesCheckConfig: {
      title: isTranslate<PERSON><PERSON>('Tool::Countdown::Dependencies::Modal::Button::Delete Countdown'),
      message: isTranslate<PERSON><PERSON>('Tool::Countdown::Dependencies::Modal::This Countdown is used in the following objects and cannot be deleted: @@deps@@')
    }
  }
};

export default CountdownConfig;

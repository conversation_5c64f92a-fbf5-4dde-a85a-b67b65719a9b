<kt-tool-settings-header [isCreate]="routeData()?.isCreate === true" [configData]="configData()" />
<form [formGroup]="form">
  <kt-entity-create [form]="form" [entity]="entity()" />
  @if (!routeData().isCreate) {
    <kt-select [item]="templateSelectField" [formControlName]="templateSelectField.id" />
    @if (templatePreviewSrc) {
      <div class="template-preview">
        <img [ngSrc]="templatePreviewSrc" alt="countdown template preview" width="400" height="100" priority="true" />
      </div>
    }

    <kt-select [item]="terminationTypeSelectField" [formControlName]="terminationTypeSelectField.id" />

    <div class="termination-type-fixed" [hidden]="terminationType !== terminationTypeEnum.FIXED">
      <kt-datepicker #terminationDateTimeComponent [formControlName]="terminationDateTime.id" [datepickerData]="terminationDateTime" />
    </div>

    <div class="termination-type-tag" [hidden]="terminationType !== terminationTypeEnum.TAGGING">
      <kt-select [item]="tagCategorySelectField" [formControlName]="tagCategorySelectField.id" />
      <kt-magic-select [item]="taggingSelectItem" [formControlName]="taggingSelectItem.id" [allTags]="tags" [tags]="selectedTags" />
      <kt-label [text]="'Tool::Countdown::CreateDetail::TerminationTagDuration::Label::Duration after Tag' | translate" />
      <kt-countdown-termination-tag-duration #terminationTagDurationComponent [form]="form" />
    </div>

    <div class="termination-type-customfield" [hidden]="terminationType !== terminationTypeEnum.CUSTOMFIELD">
      <kt-select [item]="terminationCustomfieldSelectField" [formControlName]="terminationCustomfieldSelectField.id" />
    </div>

    <kt-panel
      nodeId="countdown-custom-image-settings-panel"
      [isOpen]="true"
      [panelHeadline]="'Tool::Countdown::CreateDetail::CustomImage::Panel::Custom Image Settings' | translate"
    >
      <kt-input [input]="customExpiredImageInput" [formControlName]="customExpiredImageInput.id" />
      <kt-bee-file-manager-dialog (insert)="onInsertFile($event)" (error)="onFileManagerError($event)" [userId]="routeParams()?.user ?? 'me'" />
      @if (fileManagerStaticError) {
        <div class="mt-10"><kt-static-message [item]="fileManagerStaticError" /></div>
      }
    </kt-panel>

    <kt-panel nodeId="countdown-link-settings-panel" [isOpen]="true" [panelHeadline]="'Tool::Countdown::CreateDetail::Link::Panel::Link Settings' | translate">
      <kt-input [input]="targetUrlInput" [formControlName]="targetUrlInput.id" />
      <kt-input [input]="targetUrlExpiredInput" [formControlName]="targetUrlExpiredInput.id" />

      <kt-checkbox [box]="showSubscriberParameterName" [formControlName]="showSubscriberParameterName.id" />
      <kt-input [input]="subscriberParameterNameInput" [formControlName]="subscriberParameterNameInput.id" [hidden]="isSubscriberParameterNameInputHidden" />

      <kt-checkbox [box]="showEmailParameterName" [formControlName]="showEmailParameterName.id" />
      <kt-input [input]="emailParameterNameInput" [formControlName]="emailParameterNameInput.id" [hidden]="isEmailParameterNameInputHidden" />
    </kt-panel>
  }
  <div class="mt-15">
    <kt-button [icon]="iconChevronLeft" (btnClick)="back()" [buttonText]="backButtonText" nodeId="genericCancelBtn" />
    @if (routeData().isCreate) {
      <kt-button [icon]="iconCheck" color="primary" (btnClick)="createEntity()" [buttonText]="createButtonText" nodeId="genericCreateBtn" />
    }
    @if (!routeData().isCreate) {
      <kt-button [icon]="iconTrash" color="warn" (btnClick)="deleteEntity()" [buttonText]="deleteButtonText" nodeId="genericDeleteBtn" />
      <kt-button [icon]="iconCopy" (btnClick)="copyEntity()" [buttonText]="copyButtonText" nodeId="genericCopyBtn" />
      <kt-button [icon]="iconCheck" color="primary" (btnClick)="updateEntity()" [buttonText]="saveButtonText" nodeId="genericSaveBtn" />
    }
  </div>
</form>

import CampaignTemplatesConfig from '../components/type/campaign-templates/campaign-templates.config';
import { CloseCrmConfig } from '../components/type/close-crm/close-crm.config';
import CountdownConfig from '../components/type/countdown/countdown.config';
import OutboundConfig from '../components/type/outbound/outbound.config';
import TaggingPixelConfig from '../components/type/tagging-pixel/tagging-pixel.config';
import { ToolComponentConfig, ToolComponentData, ToolDependenciesCheckConfig } from '../models/component-config';

/**
 * Note: this strings have to match with those from the backend.
 * @see backend/src/Klicktipp/MarketingTools.php
 * @see MarketingTools::$APIIndexFilterTypes
 */
export enum ToolTypeEnum {
  TaggingPixel = 'tagging-pixel',
  Countdown = 'countdown',
  Outbound = 'outbound',
  CampaignTemplates = 'campaign-templates',
  CloseCrm = 'closecrm'
}

export const ToolComponentMap: { [key in ToolTypeEnum]: ToolComponentConfig } = {
  [ToolTypeEnum.TaggingPixel]: TaggingPixelConfig,
  [ToolTypeEnum.Countdown]: CountdownConfig,
  [ToolTypeEnum.Outbound]: OutboundConfig,
  [ToolTypeEnum.CampaignTemplates]: CampaignTemplatesConfig,
  [ToolTypeEnum.CloseCrm]: CloseCrmConfig
};

export const getToolComponentMessage = (toolType: ToolTypeEnum, messageType: keyof ToolComponentData['message']): string => {
  return ToolComponentMap[toolType].data.message[messageType] ?? messageType;
};

export const getDependenciesCheckConfig = (toolType: ToolTypeEnum): ToolDependenciesCheckConfig | undefined => {
  return ToolComponentMap[toolType]?.data?.dependenciesCheckConfig;
};

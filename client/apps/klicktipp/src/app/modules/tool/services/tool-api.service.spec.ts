import { HttpClient } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@klicktipp/kt-mat-library';
import { MockProvider } from 'ng-mocks';
import { of } from 'rxjs';
import { RouterFacade } from '../../../+state/router.facade';
import { RouteParameterService } from '../../../services/route-parameter.service';
import { ToolTypeEnum } from '../general/tool-component-map';
import { ToolApiService } from './tool-api.service';

describe('ToolApiService', () => {
  let service: ToolApiService;
  let httpClient: HttpClient;
  let routerFacade: RouterFacade;
  let routeParameterService: RouteParameterService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        ToolApiService,
        MockProvider(HttpClient),
        MockProvider(RouterFacade, {
          getUserIdOnce: jest.fn().mockReturnValueOnce(of('123'))
        }),
        MockProvider(RouteParameterService, {
          setItemName: jest.fn()
        }),
        MockProvider(TranslateService, { translate: (v) => v })
      ]
    });
    service = TestBed.inject(ToolApiService);
    httpClient = TestBed.inject(HttpClient);
    routerFacade = TestBed.inject(RouterFacade);
    routeParameterService = TestBed.inject(RouteParameterService);
  });

  it('should be created', () => {
    expect(service).toBeInstanceOf(ToolApiService);
  });

  it('should set the type', () => {
    service.setType('test');
    expect(service.getType()).toBe('test');
  });

  it('should return the api full path from type properly', () => {
    service.setType('test');
    expect(service.apiFullPath).toBe('/ipa/kt-test');
  });

  it('should call the httpClient for getItems() properly', (done) => {
    const path = '/ipa/kt-marketing-tool/overview/123';
    const postSpy = jest.spyOn(httpClient, 'post').mockReturnValueOnce(of({ data: { message: 'anything' } }));

    service.setType('test');
    service.getItems().subscribe((data) => {
      expect(data).toEqual({ data: { message: 'anything' } });
      expect(postSpy).toHaveBeenCalledWith(path, null, expect.objectContaining({ headers: expect.anything(), withCredentials: true }));
      expect(routerFacade.getUserIdOnce).toHaveBeenCalled();
      done();
    });
  });

  it('should call the httpClient for getItem(id) properly', (done) => {
    const path = '/ipa/kt-test/settings-retrieve/123/456/0';
    const postSpy = jest.spyOn(httpClient, 'post').mockReturnValueOnce(of({ data: { entity: { name: 'Jest Test Entity' } } }));

    service.setType('test');
    service.getItem(456, 0).subscribe((data) => {
      expect(data).toEqual({ data: { entity: { name: 'Jest Test Entity' } } });
      expect(postSpy).toHaveBeenCalledWith(path, expect.objectContaining({ headers: expect.anything(), withCredentials: true }));
      expect(routerFacade.getUserIdOnce).toHaveBeenCalled();
      expect(routeParameterService.setItemName).toHaveBeenCalledWith('Jest Test Entity');
      done();
    });
  });

  it('should call the httpClient for getDependencies(id) properly', (done) => {
    const path = '/ipa/kt-test/dependencies/123/456';
    const postSpy = jest.spyOn(httpClient, 'post').mockReturnValueOnce(of({ data: { dependencies: [] } }));

    service.setType('test');
    service.getDependencies(456).subscribe((data) => {
      expect(data).toEqual({ data: { dependencies: [] } });
      expect(postSpy).toHaveBeenCalledWith(path, null, expect.objectContaining({ headers: expect.anything(), withCredentials: true }));
      expect(routerFacade.getUserIdOnce).toHaveBeenCalled();
      done();
    });
  });

  it('should call the httpClient for create(entity, 0) properly', (done) => {
    const toolId = 456;
    const path = '/ipa/kt-test/create/123';
    const postSpy = jest.spyOn(httpClient, 'post').mockReturnValueOnce(of({ data: { id: toolId, links: {} } }));
    const entity = { id: toolId, type: 'test', name: 'Jest Test Entity' };

    service.setType('test');
    service.create(entity, 0).subscribe((data) => {
      expect(data).toEqual({ data: { id: toolId, links: {} } });
      expect(postSpy).toHaveBeenCalledWith(path, { entity }, expect.objectContaining({ headers: expect.anything(), withCredentials: true }));
      expect(routerFacade.getUserIdOnce).toHaveBeenCalled();
      done();
    });
  });

  it('should call the httpClient for create(entity, 456) properly', (done) => {
    const toolId = 456;
    const path = '/ipa/kt-test/create/123/456';
    const postSpy = jest.spyOn(httpClient, 'post').mockReturnValueOnce(of({ data: { id: toolId, links: {} } }));
    const entity = { id: toolId, type: 'test', name: 'Jest Test Entity' };

    service.setType('test');
    service.create(entity, 456).subscribe((data) => {
      expect(data).toEqual({ data: { id: toolId, links: {} } });
      expect(postSpy).toHaveBeenCalledWith(path, { entity }, expect.objectContaining({ headers: expect.anything(), withCredentials: true }));
      expect(routerFacade.getUserIdOnce).toHaveBeenCalled();
      done();
    });
  });

  it('should call the httpClient for save(entity) properly', (done) => {
    const toolId = 456;
    const path = `/ipa/kt-test/settings-save/123/${toolId}`;
    const postSpy = jest.spyOn(httpClient, 'post').mockReturnValueOnce(of({ data: { id: toolId, links: {} } }));
    const entity = { id: toolId, type: 'test', name: 'Jest Test Entity' };

    service.setType('test');
    service.save(toolId, entity).subscribe((data) => {
      expect(data).toEqual({ data: { id: toolId, links: {} } });
      expect(postSpy).toHaveBeenCalledWith(path, { entity }, expect.objectContaining({ headers: expect.anything(), withCredentials: true }));
      expect(routerFacade.getUserIdOnce).toHaveBeenCalled();
      done();
    });
  });

  it('should call the httpClient for delete(id) properly', (done) => {
    const toolId = 456;
    const path = `/ipa/kt-test/delete/123/${toolId}`;
    const postSpy = jest.spyOn(httpClient, 'post').mockReturnValueOnce(of({ data: { message: 'success' } }));

    service.setType('test');
    service.delete(toolId).subscribe((data) => {
      expect(data).toEqual({ data: { message: 'success' } });
      expect(postSpy).toHaveBeenCalledWith(path, expect.objectContaining({ headers: expect.anything(), withCredentials: true }));
      expect(routerFacade.getUserIdOnce).toHaveBeenCalled();
      done();
    });
  });

  describe('apiFullPath', () => {
    it('should return custom path', () => {
      service.setType(ToolTypeEnum.Outbound);
      expect(service.apiFullPath).toBe('/ipa/kt-webhook');
    });

    it('should return default path for tagging pixel', () => {
      service.setType(ToolTypeEnum.TaggingPixel);
      expect(service.apiFullPath).toBe('/ipa/kt-tagging-pixel');
    });

    it('should return default path for countdown', () => {
      service.setType(ToolTypeEnum.Countdown);
      expect(service.apiFullPath).toBe('/ipa/kt-countdown');
    });

    it('should return default path for campaign-templates', () => {
      service.setType(ToolTypeEnum.CampaignTemplates);
      expect(service.apiFullPath).toBe('/ipa/kt-campaign-templates');
    });
  });
});

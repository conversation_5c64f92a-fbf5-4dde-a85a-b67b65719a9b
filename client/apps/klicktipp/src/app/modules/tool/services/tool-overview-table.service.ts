import { Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import {
  DashFilterService,
  KtComponentType,
  KtTableService,
  PaginatorSize,
  TableAlignTypes,
  TableBase,
  TableCellComponent,
  TableDefaultSortValueItem,
  TableHeaderItem,
  TableRowData,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { KtStorageKeysEnum } from '../../../enums/storage.enum';
import { ConvertTableBaseService } from '../../../services/convert-table.base.service';
import { KtStorageService } from '../../../services/kt-storage.service';
import { ToolOverviewEntity } from '../models/tool-api';

@Injectable({ providedIn: 'root' })
export class ToolOverviewTableService extends ConvertTableBaseService {
  tableHeaders: TableHeaderItem[] = [
    {
      header: this.translateService.translate('Tool::Overview::Table::Header::Id'),
      key: 'id',
      align: TableAlignTypes.Right
    },
    {
      header: this.translateService.translate('Tool::Overview::Table::Header::Name'),
      key: 'name'
    },
    {
      header: this.translateService.translate('Tool::Overview::Table::Header::Type'),
      key: 'displayType'
    },
    {
      header: '',
      key: 'copy-action',
      disableSort: true,
      align: TableAlignTypes.Center
    }
  ];

  constructor(
    protected translateService: TranslateService,
    protected dashPipe: DashFilterService,
    sanitizer: DomSanitizer,
    ktStorageService: KtStorageService,
    ktTableService: KtTableService
  ) {
    super(translateService, ktStorageService, ktTableService, dashPipe, sanitizer);
    this.setupPaginationAndSortingForOverviewPages();
  }

  createOverviewTable(toolItems: ToolOverviewEntity[]): TableBase {
    const rows: TableRowData[] = [];

    this.paginationSizeKey = KtStorageKeysEnum.ToolPaginationSize;
    this.sortSettingsKey = KtStorageKeysEnum.ToolSortSettings;

    const paginatorSize: number = this.ktStorageService.getFromSessionStorage(this.paginationSizeKey) ?? PaginatorSize;
    const sortValue: TableDefaultSortValueItem = this.ktStorageService.getFromSessionStorage(this.sortSettingsKey) ?? { default: { name: 'id', direction: 'desc' } };

    for (const entity of toolItems) {
      rows.push({
        id: `${entity.id}`,
        rowData: this.createToolTableRow(entity)
      });
    }

    return {
      id: `tool-overview`,
      headers: this.tableHeaders,
      rows,
      textFilter: true,
      defaultSortValue: sortValue,
      paginatorSize,
      customCss: 'tool-overview-table'
    };
  }

  private createToolTableRow(entity: ToolOverviewEntity): TableCellComponent[] {
    const row: TableCellComponent[] = [];
    for (const [key, _value] of Object.entries(entity)) {
      const header = this.tableHeaders.find((h) => h.key === key);
      if (header) {
        const { propertyName, rowEle } = this.createRow(entity, key);
        if (propertyName === 'name') {
          rowEle.link = entity.links.edit;
        }
        row.push(rowEle);
      }
    }

    row.push({
      text: this.translateService.translate('Tool::Overview::Table::Action::Copy'),
      componentType: KtComponentType.Button,
      column: 'copy-action',
      id: `${entity.id}-copy`
    });

    return row;
  }
}

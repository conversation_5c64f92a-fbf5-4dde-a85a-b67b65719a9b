<h1 [innerHTML]="title"></h1>
@if (!subComponentInitDone) {
  <kt-notification [hasSpinner]="true" [text]="notificationText" [nodeId]="notificationNodeId"></kt-notification>
}
<kt-landing-page-messages
  [landingPageDetailData]="landingPageDetailData"
  [isAdmin]="isAdmin"
  [isCreate]="isCreate"
  [activatedPaidLandingPages]="activatedPaidLandingPages"
  (activate)="activateAddonPlusLandingPage($event)"
  (downgrade)="downgradeAddonPlusLandingPage()"
></kt-landing-page-messages>
<form [formGroup]="form" [hidden]="!subComponentInitDone">
  <!--  ###### Name, Labels, Notes ###### -->
  <kt-entity-create [form]="form" [entity]="landingPageDetailData?.data?.entity ?? null" />

  @if (!isCreate) {
    <!--  ###### Opt In Process ###### -->
    <kt-magic-select [item]="optIn" [formControlName]="optIn.id" [allTags]="optInItems | async" [tags]="selectedOptInItems"></kt-magic-select>

    @if (landingPageDetailData.data.entity.addOnPlusStatus !== LandingPageAddonStatus.Free && activatedPaidLandingPages) {
      <!--  ###### Domain ###### -->
      <kt-radio [formControlName]="domainRadioGroup.id" [layout]="'kt-flex-row kt-flex-col-lt-sm'" [gap]="'10px'" [radioGroup]="domainRadioGroup"></kt-radio>
    }
    <!--  ###### Klicktipp Domain ###### -->
    <kt-input [input]="subdomainInput" [formControlName]="subdomainInput.id" [hidden]="selectedDomain === 1"></kt-input>

    @if (landingPageDetailData.data.entity.addOnPlusStatus !== LandingPageAddonStatus.Free) {
      <!--  ###### Custom Domain ###### -->
      <div [hidden]="selectedDomain === 0">
        <div class="kt-flex-row kt-flex-col-lt-md kt-flex-gap-20 small-label">
          <kt-input [input]="customSubdomainInput" [formControlName]="customSubdomainInput.id" class="sub-domain-input"></kt-input>
          <kt-select [item]="domainSelect" [formControlName]="domainSelect.id"></kt-select>
          @if (landingPageDetailData.data.displayOptions.allowCustomDomainCreate) {
            <kt-button
              [icon]="iconPlus"
              [color]="'accent'"
              (btnClick)="addCustomDomain()"
              buttonText="{{ 'LandingPages::CreateDetail::Button::Add Domain' | translate }}"
              nodeId="addDomainBtn"
            ></kt-button>
          }
        </div>
      </div>
    }
  }

  @if (landingPageDetailData.data.displayOptions.isAddonAvailable && activatedPaidLandingPages) {
    @if ((isCreate && landingPageDetailData?.data?.filterOptions?.addOnPlus) || landingPageDetailData.data.entity.addOnPlusStatus !== LandingPageAddonStatus.Free) {
      <kt-landingpage-type-select [form]="form" [lpTypeRadio]="lpTypeRadio" [upsell]="landingPageDetailData.data.filterOptions.addOnPlus"></kt-landingpage-type-select>
    }
  }
  <!--  ###### Buttons ###### -->
  <div class="mt-15">
    @if (currentId === 0) {
      <kt-button
        [icon]="iconCheck"
        [color]="'primary'"
        (btnClick)="createLandingPage()"
        buttonText="{{ 'LandingPages::CreateDetail::Button::Create' | translate }}"
        nodeId="createBtn"
      ></kt-button>
    }
    @if (currentId > 0) {
      <kt-button
        [icon]="iconCheck"
        [color]="'primary'"
        (btnClick)="updateLandingPageSettings()"
        [isDisabled]="saveBtnDisabled"
        buttonText="{{ 'LandingPages::CreateDetail::Button::Save' | translate }}"
        nodeId="saveBtn"
      ></kt-button>
    }
    @if (currentId > 0) {
      <kt-button
        [icon]="iconTrash"
        [color]="'warn'"
        (btnClick)="deleteLandingPage()"
        buttonText="{{ 'LandingPages::CreateDetail::Button::Delete' | translate }}"
        nodeId="deleteBtn"
      ></kt-button>
    }
    <kt-button (btnClick)="cancel()" [icon]="iconCancel" buttonText="{{ 'LandingPages::CreateDetail::Button::Cancel' | translate }}" nodeId="cancelBtn"></kt-button>

    <!--  ###### Admin Buttons ###### -->
    @if (landingPageDetailData?.data.displayOptions.showDisableButton) {
      <kt-button
        [icon]="iconStop"
        [color]="'warn'"
        (btnClick)="deactivateLandingPage()"
        buttonText="{{ 'LandingPages::CreateDetail::Button::Admin::Deactivate' | translate }}"
        nodeId="blockBtn"
      ></kt-button>
    }
    @if (landingPageDetailData?.data.displayOptions.showEnableButton) {
      <kt-button
        [icon]="iconEnable"
        [color]="'primary'"
        (btnClick)="activateLandingPage()"
        buttonText="{{ 'LandingPages::CreateDetail::Button::Admin::Reactivate' | translate }}"
        nodeId="reactivateBtn"
      ></kt-button>
    }
  </div>
</form>

@use '@angular/material' as mat;

@use '../../../../../../../../../libs/kt-mat/src/lib/scss/themes/core/abstracts/index';
@use '../../../../../../../../../libs/kt-mat/src/lib/scss/abstracts/mixins';
@use '../../../../../../../../../libs/kt-mat/src/lib/scss/abstracts/kt-palette-drupal' as f;
@use '../../../../../modules/bee-shared/components/bee-template-browser-base/bee-template-browser.base.components';

.email-choose-editor [class*='button-'] {
  display: inline-block;
  width: auto;
  height: auto;
  padding: 0.5em 1em;
  text-align: center;
  box-shadow: none;
  outline: none;
  border-radius: 4px;
  font-weight: 600;
  font-size: 1rem;
  vertical-align: baseline;
  cursor: pointer;
  transition: all 0.15s ease-out;
  border: none;
  background-color: transparent;
}

.email-choose-editor [class*='button-'] > i {
  margin-right: 1em;
  margin-left: -4px;
}

.email-choose-editor .button-primary {
  color: #fff !important;
  background: #4d92ff;
}

.email-choose-editor .button-primary:hover {
  background-color: #3376de;
  transition: all 0.15s linear;
}

.email-choose-editor p {
  color: #8090a8;
}

.email-choose-editor {
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  padding: 60px 0 60px 0;
}

.email-choose-editor .editor-option {
  min-width: 350px;
  padding: 20px;
  border-radius: 4px;
  box-shadow: inset 0 0 0 1px #d6e2f7,
  0 2px 6px 0 rgba(63, 114, 193, 0.15);
  text-align: center;
  margin-right: 30px;
  @include index.responsive(lt-md) {
    width: 100%;
    min-width: 0;
    margin-right: 0;
  }
}

.rich-text-editor {
  @include index.responsive(lt-md) {
    margin-bottom: 50px;
  }
}

.email-choose-editor .editor-option figure {
  display: inline-block;
}

.email-choose-editor .editor-option figure img {
  display: block;
  /*width: 150px;*/
  height: 130px;
  margin-top: -60px;
}

.email-choose-editor .editor-option h3 {
  margin-top: 5px;
  margin-bottom: 20px;
}

.email-choose-editor .editor-option ul {
  display: inline-block;
  width: auto;
  flex-flow: column nowrap;
  margin: 0;
  padding: 0;
}

.email-choose-editor .editor-option ul li {
  display: block;
  width: auto;
  list-style: none;
  padding-left: 15px;
  position: relative;
  text-align: left;
}

.email-choose-editor .editor-option ul li:before {
  display: block;
  content: '';
  width: 5px;
  height: 5px;
  border-radius: 100%;
  position: absolute;
  top: 8px;
  left: 0;
  background-color: #8db8fa;
}

.email-choose-editor .editor-option [class*='button-'] {
  font-size: 16px;
  margin: 25px auto 12px auto;
}

.email-choose-editor .editor-option.drag-drop-editor figure img {
  margin-left: 18px;
}

.email-choose-editor .editor-option:last-child {
  margin-right: 0;
}

.email-choose-editor.justify-center {
  justify-content: space-around;
}

.email-choose-editor.justify-center .editor-option {
  margin-right: 0;
}

.editor-option {
  position: relative;
}

.ki-badge {
  align-items: center;
  background-color: mat.m2-get-color-from-palette(f.$kt-accent, 500);
  border-radius: 50%;
  color: mat.m2-get-color-from-palette(f.$kt-basic, 100);
  display: flex;
  font-size: smaller;
  height: 90px;
  width: 90px;
  justify-content: center;
  padding: 5px;
  position: absolute;
  right: -35px;
  top: -32px;

  @include index.responsive(lt-sm) {
    right: -25px;
    top: -32px;
    height: 80px;
    width: 80px;
    font-size: smaller;
  }
}

.email-choose-editor {
  div {
    flex: 1;
  }

  p {
    color: black;
  }

  .ai-generated {
    background-color: #f4f6fe;
  }

  @include index.responsive(lt-md) {
    gap: 64px;
  }
}

.container {
  display: grid;
  grid-template-columns: 33.33% 66.67%;
  grid-template-rows: repeat(3, auto);
  gap: 10px;
  grid-column-gap: 34px;
  grid-template-areas:
    'title title'
    'subtitle subtitle'
    'ki-image promo-info'
    'ki-image promo-info'
    'ki-image promo-info';
}

.item {
  padding: 20px;
  border: 1px solid #ccc;
}

.title {
  grid-area: title;
}

.subtitle {
  grid-area: subtitle;
}

.ki-image {
  grid-area: ki-image;
  max-width: 100%;
  height: auto;
}

.promo-info {
  grid-area: promo-info;
  display: flex;
  flex-direction: column;
  font-size: 1rem;
  gap: 30px;
  justify-content: center;

  fa-icon {
    color: green;
  }
}

ol {
  margin-left: 25px;
}

.how-to-steps p {
  font-weight: bold;
}

.list-with-icons {
  list-style: none;

  span {
    margin-left: 6px;
  }
}

h3 {
  margin-bottom: 14px;
}

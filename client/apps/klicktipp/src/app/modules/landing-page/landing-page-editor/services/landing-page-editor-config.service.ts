import { Injectable } from '@angular/core';
import { TranslateService } from '@klicktipp/kt-mat-library';
import { interval, merge, Observable, of, ReplaySubject } from 'rxjs';
import { concatMap, debounce, distinctUntilChanged, exhaustMap, filter, first, map, shareReplay, skip, switchMap, takeUntil, throttleTime } from 'rxjs/operators';
import { ErrorBaseService } from '../../../../services/error-base.service';
import { SentryService } from '../../../../services/sentry.service';
import { BaseConfigSource, EditorInitConfig } from '../../../bee-shared/models/bee-editor/editor-wrapper/editor-init-config.interface';
import { BeepluginCmpConfig } from '../../../bee-shared/models/bee-plugin-cmp-config.interface';
import { EntityService } from '../../../bee-shared/models/bee-plugin/entity-service.interface';
import { BeeBaseEditorConfigService } from '../../../bee-shared/services/bee-base-config.service';
import { BeeBaseUtilityService } from '../../../bee-shared/services/bee-base-utility.service';

@Injectable({ providedIn: 'root' })
export class LandingPageEditorConfigService extends BeeBaseEditorConfigService {
  constructor(
    protected translateService: TranslateService,
    protected beeBaseUtilityService: BeeBaseUtilityService,
    protected errorBaseService: ErrorBaseService,
    protected sentryService: SentryService
  ) {
    super(translateService, beeBaseUtilityService, errorBaseService, sentryService);
  }

  getConfig$(): Observable<BeepluginCmpConfig> {
    if (!this.config$) {
      throw new Error('Not initialized');
    }

    return this.config$;
  }

  initConfig$(
    entityService: EntityService,
    initConfig: EditorInitConfig,
    lpId: number
  ): { config$: Observable<BeepluginCmpConfig>; initConfig$: Observable<BaseConfigSource>; init$: Observable<boolean> } {
    this.loadSubject = new ReplaySubject<BeepluginCmpConfig>(1);

    const initConfig$ = of(lpId).pipe(
      distinctUntilChanged(),
      concatMap((id) => entityService.getConfigBase(id, initConfig)),
      map((data) => ({ source: 'init', data })),
      shareReplay(1),
      takeUntil(this.destroy$)
    );

    const init$ = merge(of(false), this.loadSubject.pipe(map(() => true))).pipe(takeUntil(this.destroy$));

    const load$: Observable<BaseConfigSource> = this.loadSubject.pipe(
      map((data) => ({ source: 'load', data: { ...data, status: '' } })),
      takeUntil(this.destroy$)
    );

    const pushDecisions$ = this.beeBaseUtilityService.pushDecisions.pipe(takeUntil(this.destroy$));

    const save$: Observable<BaseConfigSource> = merge(
      this.saveSubject.pipe(takeUntil(this.destroy$)),
      this.loadSubject.pipe(
        filter((config) => config.autosave !== false),
        skip(1),
        takeUntil(this.destroy$)
      ),
      initConfig$.pipe(
        first(),
        filter(
          ({
            data: {
              entity: { json }
            }
          }) => !json
        ),
        switchMap(() => this.loadSubject.pipe(first()))
      ),
      pushDecisions$
    ).pipe(
      filter((config) => !!config && config.entity.id === lpId),
      debounce((config) => interval(config.autosave !== false ? config.autosave : 50)),
      // allow save calls only once a second, important: debounce first, then throttle
      throttleTime(1000),
      // use exhaustMap to avoid multiple save calls if one takes longer than expected, to avoid race conditions
      exhaustMap((config) => entityService.saveEntity(config)),
      filter((config) => !!config),
      map((data) => ({ source: 'save', data })),
      takeUntil(this.destroy$)
    );

    return this.createConfigBase(initConfig$, save$, load$, init$);
  }
}

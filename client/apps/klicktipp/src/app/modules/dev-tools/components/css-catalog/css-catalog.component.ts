
import { Component, OnInit } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faCheck, faCircleCheck, faDoNotEnter, faPlus, faTimes, faTrashAlt } from '@fortawesome/pro-regular-svg-icons';
import {
  AutocompleteItem,
  AutocompleteOption,
  CheckBoxItem,
  ChipItem,
  DatepickerData,
  InputFileItem,
  InputItem,
  KtAutocompleteComponent,
  KtButtonComponent,
  KtCheckboxComponent,
  KtChipComponent,
  KtDatepickerComponent,
  KtInputComponent,
  KtInputFileComponent,
  KtMagicSelectComponent,
  KtMessageService,
  KtNavigationService,
  KtRadioComponent,
  KtSelectComponent,
  KtSliderComponent,
  KtStaticMessageComponent,
  KtTextareaComponent,
  MagicSelectItem,
  NotificationType,
  RadioGroup,
  SelectItem,
  SelectOption,
  SliderItem,
  StaticMessageItem,
  TextAreaItem,
  TranslateService
} from '@klicktipp/kt-mat-library';

@Component({
  selector: 'kt-css-catalog',
  templateUrl: './css-catalog.component.html',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    KtButtonComponent,
    KtInputComponent,
    KtSelectComponent,
    KtRadioComponent,
    KtAutocompleteComponent,
    KtMagicSelectComponent,
    KtTextareaComponent,
    KtDatepickerComponent,
    KtSliderComponent,
    KtCheckboxComponent,
    KtChipComponent,
    KtInputFileComponent,
    KtStaticMessageComponent
]
})
export class CssCatalogComponent implements OnInit {
  // Icons
  iconCheck: IconProp = faCheck as IconProp;
  iconCancel: IconProp = faTimes as IconProp;
  iconTrash: IconProp = faTrashAlt as IconProp;
  iconStop: IconProp = faDoNotEnter as IconProp;
  iconEnable: IconProp = faCircleCheck as IconProp;
  iconPlus: IconProp = faPlus as IconProp;

  // Form
  form: FormGroup = new FormGroup({});

  // Input
  textInput: InputItem = {
    id: 'textInput',
    type: 'text',
    placeholder: 'Enter text',
    labelText: 'Text Input',
    value: '',
    required: true
  };

  emailInput: InputItem = {
    id: 'emailInput',
    type: 'email',
    placeholder: 'Enter email',
    labelText: 'Email Input',
    value: '',
    required: true
  };

  passwordInput: InputItem = {
    id: 'passwordInput',
    type: 'password',
    placeholder: 'Enter password',
    labelText: 'Password Input',
    value: '',
    required: true
  };

  // Select
  selectItem: SelectItem = {
    id: 'selectItem',
    labelText: 'Select Option',
    options: [
      { text: 'Option 1', value: '1' },
      { text: 'Option 2', value: '2' },
      { text: 'Option 3', value: '3' }
    ],
    required: true
  };

  // Radio
  radioGroup: RadioGroup = {
    id: 'radioGroup',
    labelText: 'Radio Options',
    radioOptions: [
      { text: 'Option 1', value: '1' },
      { text: 'Option 2', value: '2' },
      { text: 'Option 3', value: '3' }
    ]
  };

  // Checkbox
  checkboxItems: CheckBoxItem[] = [
    { id: 'checkbox1', checkboxText: 'Checkbox 1', color: 'primary' },
    { id: 'checkbox2', checkboxText: 'Checkbox 2', color: 'accent' },
    { id: 'checkbox3', checkboxText: 'Checkbox 3', color: 'warn' }
  ];

  // File Upload
  fileUploadItem: InputFileItem = {
    id: 'fileUpload',
    btnText: 'Upload File',
    labelFileUpload: 'File Upload',
    acceptedFormat: 'image/jpg, image/png, application/pdf'
  };

  // Autocomplete
  autocompleteItem: AutocompleteItem = {
    id: 'autocomplete',
    type: 'text',
    labelText: 'Autocomplete',
    placeholder: 'Type to search'
  };

  autocompleteOptions: AutocompleteOption[] = [
    { text: 'Option 1', value: '1' },
    { text: 'Option 2', value: '2' },
    { text: 'Option 3', value: '3' },
    { text: 'Another Option', value: '4' },
    { text: 'Last Option', value: '5' }
  ];

  // Datepicker
  datepickerItem: DatepickerData = {
    id: 'datepicker',
    label: 'Date Picker',
    type: 'datetime',
    timePickerHoursFormName: 'timePickerHours',
    timePickerMinutesFormName: 'timePickerMinutes'
  };

  // Textarea
  textareaItem: TextAreaItem = {
    id: 'textarea',
    labelText: 'Text Area',
    placeholder: 'Enter multiple lines of text',
    rows: '4',
    cols: '50'
  };

  // Chip
  chipItems: ChipItem[] = [
    { id: 1, statusText: 'Chip 1', statusColor: 'error' },
    { id: 2, statusText: 'Chip 2', statusColor: 'success' },
    { id: 3, statusText: 'Chip 3', statusColor: '' }
  ];

  // Magic Select
  magicSelectItem: MagicSelectItem = {
    id: 'magicSelect',
    labelText: 'Magic Select',
    placeholder: 'Select multiple options'
  };

  magicSelectOptions: SelectOption[] = [
    { text: 'Tag 1', value: '1' },
    { text: 'Tag 2', value: '2' },
    { text: 'Tag 3', value: '3' },
    { text: 'Tag 4', value: '4' },
    { text: 'Tag 5', value: '5' }
  ];

  selectedMagicSelectOptions: SelectOption[] = [
    { text: 'Tag 1', value: '1' },
    { text: 'Tag 3', value: '3' }
  ];

  // Slider
  sliderItem: SliderItem = {
    id: 'slider',
    labelText: 'Slider',
    minValue: 0,
    maxValue: 100,
    initialValue: 45
  };

  // Static Messages
  successMessageItem: StaticMessageItem = {
    nodeId: 'static-success-message',
    message: 'This is a static success message',
    type: NotificationType.Success
  };

  warnMessageItem: StaticMessageItem = {
    nodeId: 'static-warn-message',
    message: 'This is a static warning message',
    type: NotificationType.Warn
  };

  errorMessageItem: StaticMessageItem = {
    nodeId: 'static-error-message',
    message: 'This is a static error message',
    type: NotificationType.Danger
  };

  infoMessageItem: StaticMessageItem = {
    nodeId: 'static-info-message',
    message: 'This is a static information message',
    type: NotificationType.Info
  };

  constructor(
    private translateService: TranslateService,
    public ktNavigationService: KtNavigationService,
    private ktMessageService: KtMessageService
  ) {}

  ngOnInit(): void {
    // Show different message statuses
    this.ktMessageService.success('This is a success message', true);
    this.ktMessageService.warning('This is a warning message', true);
    this.ktMessageService.error('This is an error message', true);
    this.ktMessageService.info('This is an info message', true);
  }

  onButtonClick(): void {
    console.log('Button clicked');
  }
}

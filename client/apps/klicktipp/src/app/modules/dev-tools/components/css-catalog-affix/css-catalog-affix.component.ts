import { Component } from '@angular/core';

import { AffixItem, Breadcrumb, KtAffixComponent, KtBreadcrumbComponent, KtButtonComponent, KtMessageComponent, KtNavigationService } from '@klicktipp/kt-mat-library';
import { CssCatalogComponent } from '../css-catalog/css-catalog.component';
import { ReactiveFormsModule } from '@angular/forms';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faToggleOff, faToggleOn } from '@fortawesome/pro-regular-svg-icons';

@Component({
  selector: 'kt-dev-tools-affix',
  templateUrl: './css-catalog-affix.component.html',
  standalone: true,
  imports: [KtBreadcrumbComponent, KtMessageComponent, ReactiveFormsModule, KtAffixComponent, KtButtonComponent, CssCatalogComponent]
})
export class CssCatalogAffixComponent {
  // Toggle icons
  iconToggleOn: IconProp = faToggleOn as IconProp;
  iconToggleOff: IconProp = faToggleOff as IconProp;

  // State
  showAffix = true;

  // Breadcrumbs
  breadcrumbs: Breadcrumb[] = [
    { name: 'Home', url: '/' },
    { name: 'Dev Tools', url: '/dev-tools' }
  ];

  // Affix links
  affixLinks: AffixItem[] = [
    {
      id: 'buttons',
      title: 'Buttons',
      visible: true,
      link: '#buttons'
    },
    {
      id: 'inputs',
      title: 'Input Fields',
      visible: true,
      link: '#inputs'
    },
    {
      id: 'select',
      title: 'Select',
      visible: true,
      link: '#select'
    },
    {
      id: 'radio',
      title: 'Radio Buttons',
      visible: true,
      link: '#radio'
    },
    {
      id: 'autocomplete',
      title: 'Autocomplete',
      visible: true,
      link: '#autocomplete'
    },
    {
      id: 'datepicker',
      title: 'Datepicker',
      visible: true,
      link: '#datepicker'
    },
    {
      id: 'textarea',
      title: 'Textarea',
      visible: true,
      link: '#textarea'
    },
    {
      id: 'magicselect',
      title: 'Magic Select',
      visible: true,
      link: '#magicselect'
    },
    {
      id: 'slider',
      title: 'Slider',
      visible: true,
      link: '#slider'
    },
    {
      id: 'checkbox',
      title: 'Checkbox',
      visible: true,
      link: '#checkbox'
    },
    {
      id: 'fileupload',
      title: 'File Upload',
      visible: true,
      link: '#fileupload'
    },
    {
      id: 'staticmessage',
      title: 'Static Message',
      visible: true,
      link: '#staticmessage'
    },
    {
      id: 'chip',
      title: 'Chip',
      visible: true,
      link: '#chip'
    }
  ];

  constructor(public ktNavigationService: KtNavigationService) {}

  toggleAffix(): void {
    this.showAffix = !this.showAffix;
  }

  onAffixClicked(affixItem: AffixItem): void {
    // Handle affix item click by scrolling to the section
    if (affixItem && affixItem.link && affixItem.link.startsWith('#')) {
      const sectionId = affixItem.link.substring(1);
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
    console.log('Affix item clicked:', affixItem);
  }
}

import { Type } from '@angular/core';
import { ModalData } from '@klicktipp/kt-mat-library';
import { PluginBaseDirective } from '../general/plugin-base.directive';

export interface PluginUpsellDialogConfig {
  optionsKey: string;
  modalData: ModalData;
}

export interface PluginDependenciesCheckConfig {
  title: string;
  message: string;
}

export interface PluginComponentData {
  key: string;
  title: {
    create: string;
    edit: string;
  };
  message: {
    createSuccess: string;
    saveSuccess: string;
    deleteSuccess: string;
  };
  upsellDialogConfig?: PluginUpsellDialogConfig;
  dependenciesCheckConfig?: PluginDependenciesCheckConfig;
}

export type SingleComponent = Type<PluginBaseDirective>;
export type SplitComponent = {
  create: Type<PluginBaseDirective>;
  edit: Type<PluginBaseDirective>;
};
export type ComponentType = SingleComponent | SplitComponent;

export interface PluginComponentConfig {
  component: ComponentType;
  data: PluginComponentData;
}

export interface ComponentConfig {
  component: Type<PluginBaseDirective>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  inputs: Record<string, any>;
}

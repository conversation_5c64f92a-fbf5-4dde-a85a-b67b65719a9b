import NexmoConfig from '../components/type/integration/nexmo/nexmo.config';
import TwilioConfig from '../components/type/integration/twilio/twilio.config';
import ApiKeyConfig from '../components/type/klicktipp/api-key/api-key.config';
import BusinessCardEventConfig from '../components/type/klicktipp/business-card-event/business-card-event.config';
import GenericConfig from '../components/type/klicktipp/generic/generic-plugin.config';
import RawFormConfig from '../components/type/klicktipp/rawform/raw-form.config';
import RequestByEmailConfig from '../components/type/klicktipp/request-by-email/request-by-email.config';
import RequestBySmsConfig from '../components/type/klicktipp/request-by-sms/request-by-sms.config';
import { PluginComponentConfig, PluginComponentData, PluginDependenciesCheckConfig } from '../models/component-config';
import PaypalConfig from '../components/type/payment/paypal/paypal.config';
import AffiliconConfig from '../components/type/payment/affilicon/affilicon.config';

/**
 * Type have to macht with backend types.
 * @see backend/src/Klicktipp/Listbuildings.php -> $APIIndexFilterTypes
 */
export enum PluginTypeEnum {
  Generic = 'generic',
  RequestByEmail = 'request-email',
  RequestBySms = 'request-sms-global',
  ApiKey = 'apikey',
  Nexmo = 'request-sms-nexmo',
  Twilio = 'request-sms-twilio',
  RawForm = 'rawform',
  BusinessCardEvent = 'event',
  Paypal = 'payment-paypal',
  Affilicon = 'payment-affilicon'
}

export const PluginComponentMap: { [key in PluginTypeEnum]: PluginComponentConfig } = {
  [PluginTypeEnum.Generic]: GenericConfig,
  [PluginTypeEnum.RequestByEmail]: RequestByEmailConfig,
  [PluginTypeEnum.RequestBySms]: RequestBySmsConfig,
  [PluginTypeEnum.ApiKey]: ApiKeyConfig,
  [PluginTypeEnum.Nexmo]: NexmoConfig,
  [PluginTypeEnum.Twilio]: TwilioConfig,
  [PluginTypeEnum.RawForm]: RawFormConfig,
  [PluginTypeEnum.BusinessCardEvent]: BusinessCardEventConfig,
  [PluginTypeEnum.Paypal]: PaypalConfig,
  [PluginTypeEnum.Affilicon]: AffiliconConfig
};

export const getPluginComponentMessage = (pluginType: PluginTypeEnum, messageType: keyof PluginComponentData['message']): string => {
  return PluginComponentMap[pluginType].data.message[messageType] ?? messageType;
};

export const getDependenciesCheckConfig = (pluginType: PluginTypeEnum): PluginDependenciesCheckConfig | undefined => {
  return PluginComponentMap[pluginType]?.data?.dependenciesCheckConfig;
};

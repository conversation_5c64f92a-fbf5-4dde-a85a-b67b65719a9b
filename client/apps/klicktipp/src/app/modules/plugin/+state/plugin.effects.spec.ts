/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { TranslateService } from '@klicktipp/kt-mat-library';
import { Observable, of, throwError } from 'rxjs';
import { CustomFieldService } from '../../../services/entities/custom-field.service';
import { LabelService } from '../../../services/entities/label.service';
import { OptInService } from '../../../services/entities/opt-in.service';
import { TagService } from '../../../services/entities/tag.service';
import { ModalService } from '../../../services/modal.service';
import { EntityDependencies } from '../../forms.shared/models/shared-form.interfaces';
import { getPluginComponentMessage, PluginTypeEnum } from '../general/plugin-component-map';
import { PluginDeleteResponse, PluginOverviewResponse, PluginSettingsResponse, PluginSettingsSaveResponse } from '../models/plugin-api';
import { PluginApiProxyService } from '../services/plugin-api-proxy.service';
import * as PluginActions from './plugin.actions';
import * as PluginEffects from './plugin.effects';

describe('PluginEffects', () => {
  describe('initOverview$', () => {
    it('should map to loadPluginSuccess', (done) => {
      const actionsMock = of(PluginActions.initPluginOverview());
      const pluginServiceMock = {
        getItems: (): Observable<PluginOverviewResponse> =>
          of({
            data: {
              entities: [],
              createTypes: [],
              createOptions: {
                klicktipp: {},
                paymentProvider: {},
                integrations: {}
              } as any,
              filterOptions: {
                optInOptions: [],
                typeOptions: [],
                assignTagOptions: []
              },
              links: null
            }
          })
      } as PluginApiProxyService;

      PluginEffects.initOverview$(actionsMock, pluginServiceMock).subscribe((action) => {
        expect(action).toEqual(
          PluginActions.loadPluginOverviewSuccess({
            entities: [],
            createTypes: [],
            createOptions: {
              klicktipp: {},
              paymentProvider: {},
              integrations: {}
            } as any,
            filterOptions: {
              optInOptions: [],
              typeOptions: [],
              assignTagOptions: []
            },
            links: null
          })
        );
        done();
      });
    });

    it('should map to loadPluginFailure', (done) => {
      const actionsMock = of(PluginActions.initPluginOverview());
      const error = new Error('loadPluginFailure');
      const pluginServiceMock = { getItems: () => throwError(error) };

      PluginEffects.initOverview$(actionsMock, pluginServiceMock as any).subscribe((action) => {
        expect(action).toEqual(PluginActions.loadPluginOverviewFailure({ error }));
        done();
      });
    });
  });

  describe('initSettings$', () => {
    it('should map to loadPluginSettingsSuccess', (done) => {
      const actionsMock = of(PluginActions.initPluginSettings({ id: 42, pluginType: PluginTypeEnum.Generic, isCreate: false }));
      const entity = {
        id: null,
        type: 'generic',
        name: 'Effects Test',
        metaLabels: [],
        notes: '',
        useNotes: 0,
        links: null
      };
      const pluginServiceMock = {
        getItem: (pluginId: number, _copyId: number): Observable<PluginSettingsResponse> =>
          of({
            data: {
              entity: { ...entity, id: pluginId },
              links: { edit: 'edit', overview: 'overview' },
              displayOptions: { accessApiKey: true, accessSmsMarketing: true },
              filterOptions: { inboundNumbers: [], followUpTimeOptions: [] }
            }
          }),
        setType: jest.fn().mockImplementation(() => null) as any
      } as PluginApiProxyService;
      const tagServiceMock = {
        getTags: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterTagsByTypeAndConvert: jest.fn().mockReturnValue(of([{ value: 1, text: 'Tag 1' }]))
      } as unknown as TagService;
      const labelServiceMock = {
        getLabels: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterLabelsByTypeAndConvert: jest.fn().mockReturnValue(of([{ value: 2, text: 'Label 2' }]))
      } as unknown as LabelService;
      const optInServiceMock = {
        getOptInIndex: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterOptInByTypeAndConvert: jest.fn().mockReturnValue(of([{ value: 3, text: 'Opt-In 3' }]))
      } as unknown as OptInService;

      const customFieldServiceMock = {
        getCustomFieldIndex: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterCustomFieldByTypeAndConvert: jest.fn().mockImplementation((type, isGlobal) =>
          isGlobal
            ? of([
                { value: 123, text: 'global field 1' },
                { value: 321, text: 'global field 2' }
              ])
            : of([
                { value: 456, text: 'custom field 1' },
                { value: 654, text: 'custom field 2' }
              ])
        )
      } as unknown as CustomFieldService;

      PluginEffects.initSettings$(actionsMock, pluginServiceMock, tagServiceMock, labelServiceMock, optInServiceMock, customFieldServiceMock).subscribe((action) => {
        expect(action).toEqual(
          PluginActions.loadPluginSettingsSuccess({
            entity: { ...entity, id: 42 },
            links: { edit: 'edit', overview: 'overview' },
            displayOptions: { accessApiKey: true, accessSmsMarketing: true },
            filterOptions: { inboundNumbers: [], followUpTimeOptions: [] },
            cacheOptions: {
              tagOptions: [{ value: 1, text: 'Tag 1' }],
              labelOptions: [{ value: 2, text: 'Label 2' }],
              optInOptions: [{ value: 3, text: 'Opt-In 3' }],
              globalFieldOptions: [
                { value: 123, text: 'global field 1' },
                { value: 321, text: 'global field 2' }
              ],
              customFieldOptions: [
                { value: 456, text: 'custom field 1' },
                { value: 654, text: 'custom field 2' }
              ]
            }
          })
        );
        expect(pluginServiceMock.setType).toHaveBeenCalledWith('generic');
        done();
      });
    });

    it('should map to loadPluginSettingsFailure', (done) => {
      const actionsMock = of(PluginActions.initPluginSettings({ id: 42, pluginType: PluginTypeEnum.Generic, isCreate: false }));
      const error = new Error('loadPluginSettingsFailure');
      const pluginServiceMock = {
        getItem: () => throwError(error),
        setType: jest.fn().mockImplementation(() => null)
      };
      const tagServiceMock = {
        getTags: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterTagsByTypeAndConvert: jest.fn().mockReturnValue(of([]))
      } as unknown as TagService;
      const labelServiceMock = {
        getLabels: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterLabelsByTypeAndConvert: jest.fn().mockReturnValue(of([]))
      } as unknown as LabelService;
      const optInServiceMock = {
        getOptInIndex: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterOptInByTypeAndConvert: jest.fn().mockReturnValue(of([]))
      } as unknown as OptInService;
      const customFieldServiceMock = {
        getCustomFieldIndex: jest.fn().mockReturnValue(of({ data: { entities: [] } })),
        filterCustomFieldByTypeAndConvert: jest.fn().mockReturnValue(of([]))
      } as unknown as CustomFieldService;

      PluginEffects.initSettings$(actionsMock, pluginServiceMock as any, tagServiceMock, labelServiceMock, optInServiceMock, customFieldServiceMock).subscribe((action) => {
        expect(action).toEqual(PluginActions.loadPluginSettingsFailure({ error }));
        expect(pluginServiceMock.setType).toHaveBeenCalledWith('generic');
        done();
      });
    });
  });

  describe('createEntity$', () => {
    it('should map to createEntitySuccess for copyId 0', (done) => {
      const entity = {
        id: 42,
        type: 'generic',
        name: 'Effects Test',
        metaLabels: ['label-1'],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(PluginActions.createEntity({ entity: { ...entity }, copyId: 0 }));

      const pluginServiceMock = {
        create: (_entity: any, _copyId: any): Observable<PluginSettingsSaveResponse> => of({ id: 42, links: { edit: 'edit', overview: 'overview' } })
      } as PluginApiProxyService;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;
      const createSpy = jest.spyOn(pluginServiceMock, 'create');

      PluginEffects.createEntity$(actionsMock, pluginServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).toHaveBeenCalled();
        expect(action).toEqual(
          PluginActions.createEntitySuccess({
            id: 42,
            links: { edit: 'edit', overview: 'overview' },
            message: getPluginComponentMessage(entity.type as PluginTypeEnum, 'createSuccess')
          })
        );
        expect(createSpy).toHaveBeenCalledWith(entity, 0);
        done();
      });
    });

    it('should map to createEntitySuccess for copyId 456', (done) => {
      const entity = {
        id: 42,
        type: 'generic',
        name: 'Effects Test',
        metaLabels: ['label-1'],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(PluginActions.createEntity({ entity: { ...entity }, copyId: 456 }));

      const pluginServiceMock = {
        create: (_entity: any, _copyId: any): Observable<PluginSettingsSaveResponse> => of({ id: 42, links: { edit: 'edit', overview: 'overview' } })
      } as PluginApiProxyService;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;
      const createSpy = jest.spyOn(pluginServiceMock, 'create');

      PluginEffects.createEntity$(actionsMock, pluginServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).toHaveBeenCalled();
        expect(action).toEqual(
          PluginActions.createEntitySuccess({
            id: 42,
            links: { edit: 'edit', overview: 'overview' },
            message: getPluginComponentMessage(entity.type as PluginTypeEnum, 'createSuccess')
          })
        );
        expect(createSpy).toHaveBeenCalledWith(entity, 456);
        done();
      });
    });

    it('should map to createEntityFailure', (done) => {
      const entity = {
        id: 42,
        type: 'generic',
        name: 'Effects Test',
        metaLabels: ['label-1'],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(PluginActions.createEntity({ entity: { ...entity }, copyId: 0 }));
      const error = new Error('createEntityFailure');
      const pluginServiceMock = {
        create: () => throwError(error)
      } as any;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;

      PluginEffects.createEntity$(actionsMock, pluginServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).not.toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).not.toHaveBeenCalled();
        expect(action).toEqual(PluginActions.createEntityFailure({ error }));
        done();
      });
    });
  });

  describe('saveEntity$', () => {
    it('should map to saveEntitySuccess', (done) => {
      const entity = {
        id: 42,
        type: 'generic',
        name: 'Effects Test',
        metaLabels: ['label-1'],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(PluginActions.saveEntity({ entity: { ...entity } }));

      const pluginServiceMock = {
        save: (_: any, __: any): Observable<PluginSettingsSaveResponse> => of({ id: 42, links: { edit: 'edit', overview: 'overview' } })
      } as PluginApiProxyService;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;

      PluginEffects.saveEntity$(actionsMock, pluginServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).toHaveBeenCalled();
        expect(action).toEqual(
          PluginActions.saveEntitySuccess({
            id: 42,
            links: { edit: 'edit', overview: 'overview' },
            message: getPluginComponentMessage(entity.type as PluginTypeEnum, 'saveSuccess')
          })
        );
        done();
      });
    });

    it('should map to saveEntityFailure', (done) => {
      const entity = {
        id: 42,
        type: 'generic',
        name: 'Effects Test',
        metaLabels: ['label-1'],
        notes: '',
        useNotes: 0,
        links: null
      };
      const actionsMock = of(PluginActions.saveEntity({ entity: { ...entity } }));
      const error = new Error('saveEntityFailure');
      const pluginServiceMock = {
        save: () => throwError(error)
      } as any;
      const cacheFacadeMock = {
        label: { resetLabels: jest.fn().mockReturnValue(null) },
        tag: { resetTags: jest.fn().mockReturnValue(null) }
      } as any;

      PluginEffects.saveEntity$(actionsMock, pluginServiceMock, cacheFacadeMock).subscribe((action) => {
        expect(cacheFacadeMock.tag.resetTags).not.toHaveBeenCalled();
        expect(cacheFacadeMock.label.resetLabels).not.toHaveBeenCalled();
        expect(action).toEqual(PluginActions.saveEntityFailure({ error }));
        done();
      });
    });
  });

  describe('deleteEntity$', () => {
    it('should map to deleteEntitySuccess with message from api', (done) => {
      const actionsMock = of(PluginActions.deleteEntity({ id: 42, pluginType: PluginTypeEnum.Generic }));

      const pluginServiceMock = {
        delete: (_: any): Observable<PluginDeleteResponse> => of({ data: { message: 'Message From API' } })
      } as PluginApiProxyService;

      PluginEffects.deleteEntity$(actionsMock, pluginServiceMock).subscribe((action) => {
        expect(action).toEqual(
          PluginActions.deleteEntitySuccess({
            message: 'Message From API'
          })
        );
        done();
      });
    });

    it('should map to deleteEntitySuccess with fallback message', (done) => {
      const actionsMock = of(PluginActions.deleteEntity({ id: 42, pluginType: PluginTypeEnum.Generic }));

      const pluginServiceMock = {
        delete: (_: any): Observable<PluginDeleteResponse> => of({ data: { message: null } })
      } as PluginApiProxyService;

      PluginEffects.deleteEntity$(actionsMock, pluginServiceMock).subscribe((action) => {
        expect(action).toEqual(
          PluginActions.deleteEntitySuccess({
            message: 'Listbuilding::Generic::Message::Delete Success'
          })
        );
        done();
      });
    });

    it('should map to deleteEntityFailure', (done) => {
      const actionsMock = of(PluginActions.deleteEntity({ id: 42, pluginType: PluginTypeEnum.Generic }));
      const error = new Error('deleteEntityFailure');
      const pluginServiceMock = {
        delete: () => throwError(error)
      };

      PluginEffects.deleteEntity$(actionsMock, pluginServiceMock as any).subscribe((action) => {
        expect(action).toEqual(PluginActions.deleteEntityFailure({ error }));
        done();
      });
    });
  });

  describe('checkDeleteDependencies$', () => {
    it('should map to checkDeleteDependenciesCancel if clickedUrl and id is given from modal', (done) => {
      const actionsMock = of(PluginActions.checkDeleteDependencies({ id: 42, name: 'Effect Test', pluginType: PluginTypeEnum.ApiKey }));

      const pluginServiceMock = {
        getDependencies: (_: any): Observable<EntityDependencies> => of({ data: { dependencies: [] } }),
        setType: (_: any) => null
      } as PluginApiProxyService;
      const translateServiceMock = { translate: (v: string): string => v } as TranslateService;

      const matDialogMock = { open: () => ({ afterClosed: () => of({ clickedUrl: 'https://www.google.com', id: 42 }) }) } as any;
      const overlayMock = { scrollStrategies: { noop: () => ({ enable: () => null, disable: () => null, attach: () => null }) } } as any;
      const modalService = new ModalService(matDialogMock, overlayMock, translateServiceMock);
      const createModalDataSpy = jest.spyOn(modalService, 'createDefaultDeleteWithDependencyCheckModal');
      const openWarnModalSpy = jest.spyOn(modalService, 'openWarnModal');

      const navigationServiceMock = { routeTo: (_: any) => null } as any;

      PluginEffects.checkDeleteDependencies$(actionsMock, pluginServiceMock, modalService, navigationServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(PluginActions.checkDeleteDependenciesCancel());

        expect(createModalDataSpy.mock.calls).toHaveLength(1);
        const [deps, title, message, name] = createModalDataSpy.mock.calls[0];
        expect(deps).toEqual([]);
        expect(title).toEqual('ApiKey::Dependencies::Modal::Button::Delete API Key');
        expect(message).toEqual('ApiKey::Dependencies::Modal::This API Key is used in the following objects and cannot be deleted: @@deps@@');
        expect(name).toEqual('Effect Test');

        expect(openWarnModalSpy.mock.calls).toHaveLength(1);
        const modalData = openWarnModalSpy.mock.calls[0][0];
        expect(modalData.message).toStrictEqual('Campaign::Delete::Modal::Are you sure to delete @@name@@?');
        expect(modalData.title).toStrictEqual('ApiKey::Dependencies::Modal::Button::Delete API Key');
        done();
      });
    });

    it('should map to checkDeleteDependenciesCancel if false is given from modal', (done) => {
      const actionsMock = of(PluginActions.checkDeleteDependencies({ id: 42, name: 'Effect Test', pluginType: PluginTypeEnum.ApiKey }));

      const pluginServiceMock = {
        getDependencies: (_: any): Observable<EntityDependencies> => of({ data: { dependencies: [] } }),
        setType: (_: any) => null
      } as PluginApiProxyService;
      const translateServiceMock = { translate: (v: string): string => v } as TranslateService;

      const matDialogMock = { open: () => ({ afterClosed: () => of(false) }) } as any;
      const overlayMock = { scrollStrategies: { noop: () => ({ enable: () => null, disable: () => null, attach: () => null }) } } as any;
      const modalService = new ModalService(matDialogMock, overlayMock, translateServiceMock);

      const navigationServiceMock = { routeTo: (_: any) => null } as any;

      PluginEffects.checkDeleteDependencies$(actionsMock, pluginServiceMock, modalService, navigationServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(PluginActions.checkDeleteDependenciesCancel());
        done();
      });
    });

    it('should map to deleteEntity if apply is given from modal', (done) => {
      const actionsMock = of(PluginActions.checkDeleteDependencies({ id: 42, name: 'Effect Test', pluginType: PluginTypeEnum.ApiKey }));

      const pluginServiceMock = {
        getDependencies: (_: any): Observable<EntityDependencies> => of({ data: { dependencies: [] } }),
        setType: (_: any) => null
      } as PluginApiProxyService;
      const translateServiceMock = { translate: (v: string): string => v } as TranslateService;

      const matDialogMock = { open: () => ({ afterClosed: () => of({ apply: true }) }) } as any;
      const overlayMock = { scrollStrategies: { noop: () => ({ enable: () => null, disable: () => null, attach: () => null }) } } as any;
      const modalService = new ModalService(matDialogMock, overlayMock, translateServiceMock);

      const navigationServiceMock = { routeTo: (_: any) => null } as any;

      PluginEffects.checkDeleteDependencies$(actionsMock, pluginServiceMock, modalService, navigationServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(PluginActions.deleteEntity({ id: 42, pluginType: PluginTypeEnum.ApiKey }));
        done();
      });
    });
  });

  describe('createSuccessMessage$', () => {
    it('should map createEntitySuccess to initPluginSettings', (done) => {
      const actionsMock = of(PluginActions.createEntitySuccess({ id: 42, links: { edit: '/public/generic/settings/1/2' }, message: 'Create Success' }));
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;
      const translateServiceMock = { translate: jest.fn().mockImplementation((message) => message) } as any;
      const navigationServiceMock = { routeTo: jest.fn().mockImplementation(() => null) } as any;

      PluginEffects.createSuccessMessage$(actionsMock, messageServiceMock, translateServiceMock, navigationServiceMock).subscribe((action) => {
        expect(action).toEqual(null);
        expect(messageServiceMock.success).toHaveBeenCalledWith('Create Success');
        expect(navigationServiceMock.routeTo).toHaveBeenCalledWith('/public/generic/settings/1/2');
        done();
      });
    });
  });

  describe('successMessage$', () => {
    it('should map saveEntitySuccess to navigateToOverview', (done) => {
      const actionsMock = of(PluginActions.saveEntitySuccess({ id: 42, links: {}, message: 'Save Success' }));
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;
      const translateServiceMock = { translate: jest.fn().mockImplementation((message) => message) } as any;
      const pluginProxyServiceMock = { getType: jest.fn().mockImplementation(() => PluginTypeEnum.Generic) } as any;

      PluginEffects.saveSuccessMessage$(actionsMock, pluginProxyServiceMock, messageServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(
          PluginActions.initPluginSettings({
            id: 42,
            pluginType: PluginTypeEnum.Generic,
            isCreate: false
          })
        );
        expect(messageServiceMock.success).toHaveBeenCalledWith('Save Success');
        done();
      });
    });
  });

  describe('deleteSuccessMessage$', () => {
    it('should map deleteEntitySuccess to navigateToOverview', (done) => {
      const actionsMock = of(PluginActions.deleteEntitySuccess({ message: 'Delete Success' }));
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;
      const translateServiceMock = { translate: jest.fn().mockImplementation((message) => message) } as any;

      PluginEffects.deleteSuccessMessage$(actionsMock, messageServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(PluginActions.navigateToOverview());
        expect(messageServiceMock.success).toHaveBeenCalledWith('Delete Success');
        done();
      });
    });
  });

  describe('navigateToOverview$', () => {
    it('should call the navigation service for navigateToOverview', (done) => {
      const dispatchAction = PluginActions.navigateToOverview();
      const pluginFacadeMock = { settingsLinks$: of({ overview: '/plugin/overview/me' }) } as any;
      const navigationServiceMock = { routeTo: jest.fn().mockImplementation(() => null) } as any;

      PluginEffects.navigateToOverview$(of(dispatchAction), pluginFacadeMock, navigationServiceMock).subscribe((action) => {
        expect(action).toEqual([dispatchAction, { overview: '/plugin/overview/me' }]);
        expect(navigationServiceMock.routeTo).toHaveBeenCalledWith('/plugin/overview/me');
        done();
      });
    });
  });

  describe('apiError$', () => {
    it('should call the error service for loadPluginOverviewFailure', (done) => {
      const error = new Error('Overview Error');
      const dispatchAction = PluginActions.loadPluginOverviewFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      PluginEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should call the error service for loadPluginSettingsFailure', (done) => {
      const error = new Error('Settings Error');
      const dispatchAction = PluginActions.loadPluginSettingsFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      PluginEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should call the error service for createEntityFailure', (done) => {
      const error = new Error('Entity Create Error');
      const dispatchAction = PluginActions.createEntityFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      PluginEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should call the error service for saveEntityFailure', (done) => {
      const error = new Error('Entity Save Error');
      const dispatchAction = PluginActions.saveEntityFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      PluginEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should call the error service for deleteEntityFailure', (done) => {
      const error = new Error('Entity Delete Error');
      const dispatchAction = PluginActions.deleteEntityFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      PluginEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).toHaveBeenCalledWith(error);
        done();
      });
    });

    it('should skip 406 for loadPluginSettingsFailure', (done) => {
      const error = new Error('Settings Error');
      (error as any).status = 406;

      const dispatchAction = PluginActions.loadPluginSettingsFailure({ error });
      const errorBaseServiceMock = { handleError: jest.fn().mockImplementation(() => null) } as any;

      PluginEffects.apiError$(of(dispatchAction), errorBaseServiceMock).subscribe((action) => {
        expect(action).toEqual(dispatchAction);
        expect(errorBaseServiceMock.handleError).not.toHaveBeenCalled();
        done();
      });
    });
  });
});

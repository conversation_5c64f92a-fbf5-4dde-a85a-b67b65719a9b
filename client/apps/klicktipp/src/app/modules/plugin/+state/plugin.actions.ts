import { createAction, props } from '@ngrx/store';
import { PluginTypeEnum } from '../general/plugin-component-map';
import {
  PluginCredentials,
  PluginEntity,
  PluginEntityLinks,
  PluginOverviewCreateOptions,
  PluginOverviewCreateTypes,
  PluginOverviewEntity,
  PluginOverviewFilterOptions,
  PluginSettingsCacheOptions,
  PluginSettingsDisplayOptions,
  PluginSettingsFilterOptions
} from '../models/plugin-api';

// region General Actions
export const navigateToOverview = createAction('[Plugin Page] Navigate To Overview');
// endregion

// region Overview Actions
export const initPluginOverview = createAction('[Plugin Page] Init Overview');
export const loadPluginOverviewSuccess = createAction(
  '[Plugin/API] Load Plugin Overview Success',
  props<{
    entities: PluginOverviewEntity[];
    createTypes: PluginOverviewCreateTypes[];
    createOptions: PluginOverviewCreateOptions;
    filterOptions: PluginOverviewFilterOptions;
    links: PluginEntityLinks;
  }>()
);
export const loadPluginOverviewFailure = createAction(
  '[Plugin/API] Load Plugin Overview Failure',
  props<{
    error: Error | string;
  }>()
);
// endregion

// region Settings Actions
export const initPluginSettings = createAction(
  '[Plugin Page] Init Settings',
  props<{
    id: number;
    copyId?: number;
    pluginType: PluginTypeEnum;
    isCreate: boolean;
    isCopy?: boolean;
  }>()
);
export const loadPluginSettingsSuccess = createAction(
  '[Plugin/API] Load Plugin Settings Success',
  props<{
    entity: PluginEntity;
    links: PluginEntityLinks;
    displayOptions: PluginSettingsDisplayOptions;
    filterOptions: PluginSettingsFilterOptions;
    cacheOptions: PluginSettingsCacheOptions;
  }>()
);
export const loadPluginSettingsFailure = createAction(
  '[Plugin/API] Load Plugin Settings Failure',
  props<{
    error: Error | string;
  }>()
);

export const createEntity = createAction(
  '[Plugin Page] Create Entity',
  props<{
    entity: PluginEntity;
    copyId: number;
  }>()
);
export const createEntitySuccess = createAction(
  '[Plugin/API] Create Entity Success',
  props<{
    id: number;
    links: PluginEntityLinks;
    message: string;
  }>()
);
export const createEntityFailure = createAction(
  '[Plugin/API] Create Entity Failure',
  props<{
    error: Error | string;
  }>()
);

export const saveEntity = createAction(
  '[Plugin Page] Save Entity',
  props<{
    entity: PluginEntity;
  }>()
);
export const saveEntitySuccess = createAction(
  '[Plugin/API] Save Entity Success',
  props<{
    id: number;
    links: PluginEntityLinks;
    message: string;
  }>()
);
export const saveEntityFailure = createAction(
  '[Plugin/API] Save Entity Failure',
  props<{
    error: Error | string;
  }>()
);

export const saveCredentials = createAction(
  '[Plugin Page] Save Credentials',
  props<{
    id: number;
    pluginType: PluginTypeEnum;
    credentials: PluginCredentials;
  }>()
);
export const saveCredentialsSuccess = createAction(
  '[Tool/API] Save Credentials Success',
  props<{
    id: number;
    pluginType: PluginTypeEnum;
  }>()
);
export const saveCredentialsFailure = createAction(
  '[Plugin/API] Save Credentials Failure',
  props<{
    error: Error | string;
  }>()
);

export const checkDeleteDependencies = createAction(
  '[Plugin Page] Check Delete Dependencies',
  props<{
    id: number;
    name: string;
    pluginType: PluginTypeEnum;
  }>()
);
export const checkDeleteDependenciesCancel = createAction('[Plugin Page] Check Delete Dependencies Cancel');

export const deleteEntity = createAction(
  '[Plugin Page] Delete Entity',
  props<{
    id: number;
    pluginType: PluginTypeEnum;
  }>()
);
export const deleteEntitySuccess = createAction(
  '[Plugin/API] Delete Entity Success',
  props<{
    message: string;
  }>()
);
export const deleteEntityFailure = createAction(
  '[Plugin/API] Delete Entity Failure',
  props<{
    error: Error | string;
  }>()
);

export const resetSettingsState = createAction('[Plugin/State] Reset Settings State');
// endregion

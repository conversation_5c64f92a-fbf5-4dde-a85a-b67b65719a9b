import { inject } from '@angular/core';
import { KtMessageService, KtNavigationService, TranslateService } from '@klicktipp/kt-mat-library';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';
import { Action } from '@ngrx/store';
import { of, zipWith } from 'rxjs';
import { catchError, exhaustMap, map, switchMap, tap } from 'rxjs/operators';
import { CacheFacade } from '../../../+state/cache/cache.facade';
import { resetTagsAndLabelsOperator } from '../../../+state/cache/rxjs.operators';
import { CustomFieldService } from '../../../services/entities/custom-field.service';
import { LabelService } from '../../../services/entities/label.service';
import { OptInService } from '../../../services/entities/opt-in.service';
import { TagService } from '../../../services/entities/tag.service';
import { ErrorBaseService } from '../../../services/error-base.service';
import { ModalService } from '../../../services/modal.service';
import { getDependenciesCheckConfig, getPluginComponentMessage, PluginTypeEnum } from '../general/plugin-component-map';
import { PluginEntityLinks } from '../models/plugin-api';
import { PluginApiProxyService } from '../services/plugin-api-proxy.service';
import * as PluginActions from './plugin.actions';
import { PluginFacade } from './plugin.facade';

export const initOverview$ = createEffect(
  (actions$ = inject(Actions), pluginService = inject(PluginApiProxyService)) => {
    return actions$.pipe(
      ofType(PluginActions.initPluginOverview),
      exhaustMap(() => {
        return pluginService.getItems().pipe(
          map((response) =>
            PluginActions.loadPluginOverviewSuccess({
              entities: response.data.entities,
              createTypes: response.data.createTypes,
              createOptions: response.data.createOptions,
              filterOptions: response.data.filterOptions,
              links: response.data.links
            })
          ),
          catchError((error) => of(PluginActions.loadPluginOverviewFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const initSettings$ = createEffect(
  (
    actions$ = inject(Actions),
    pluginService = inject(PluginApiProxyService),
    tagService = inject(TagService),
    labelService = inject(LabelService),
    optInService = inject(OptInService),
    customFieldService = inject(CustomFieldService)
  ) => {
    return actions$.pipe(
      ofType(PluginActions.initPluginSettings),
      exhaustMap(({ id, copyId = 0, pluginType }) => {
        pluginService.setType(pluginType);

        const tags$ = tagService.getTags().pipe(switchMap(() => tagService.filterTagsByTypeAndConvert('tag')));
        const labels$ = labelService.getLabels().pipe(switchMap(() => labelService.filterLabelsByTypeAndConvert('')));
        const optIn$ = optInService.getOptInIndex().pipe(switchMap(() => optInService.filterOptInByTypeAndConvert('')));
        const globalFields$ = customFieldService.getCustomFieldIndex().pipe(switchMap(() => customFieldService.filterCustomFieldByTypeAndConvert('', true)));
        const customFields$ = customFieldService.getCustomFieldIndex().pipe(switchMap(() => customFieldService.filterCustomFieldByTypeAndConvert('')));

        return pluginService.getItem(id, copyId).pipe(
          zipWith(tags$, labels$, optIn$, globalFields$, customFields$),
          map(([response, tagOptions, labelOptions, optInOptions, globalFieldOptions, customFieldOptions]) =>
            PluginActions.loadPluginSettingsSuccess({
              entity: response.data?.entity ?? null,
              links: response.data?.links ?? null,
              displayOptions: response.data?.displayOptions ?? null,
              filterOptions: response.data?.filterOptions ?? null,
              cacheOptions: { tagOptions, labelOptions, optInOptions, globalFieldOptions, customFieldOptions }
            })
          ),
          catchError((error) => of(PluginActions.loadPluginSettingsFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const createEntity$ = createEffect(
  (actions$ = inject(Actions), pluginService = inject(PluginApiProxyService), cacheFacade = inject(CacheFacade)) => {
    return actions$.pipe(
      ofType(PluginActions.createEntity),
      exhaustMap(({ entity, copyId = 0 }) => {
        return pluginService.create(entity, copyId).pipe(
          resetTagsAndLabelsOperator(entity, cacheFacade),
          map((response) => {
            const message = getPluginComponentMessage(entity.type as PluginTypeEnum, 'createSuccess');
            return PluginActions.createEntitySuccess({ id: entity.id, links: response.links, message });
          }),
          catchError((error) => of(PluginActions.createEntityFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const saveEntity$ = createEffect(
  (actions$ = inject(Actions), pluginService = inject(PluginApiProxyService), cacheFacade = inject(CacheFacade)) => {
    return actions$.pipe(
      ofType(PluginActions.saveEntity),
      exhaustMap(({ entity }) => {
        return pluginService.save(entity.id, entity).pipe(
          resetTagsAndLabelsOperator(entity, cacheFacade),
          map((response) => {
            const message = getPluginComponentMessage(entity.type as PluginTypeEnum, 'saveSuccess');
            return PluginActions.saveEntitySuccess({ id: entity.id, links: response.links, message });
          }),
          catchError((error) => of(PluginActions.saveEntityFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const saveCredentials$ = createEffect(
  (actions$ = inject(Actions), pluginService = inject(PluginApiProxyService)) => {
    return actions$.pipe(
      ofType(PluginActions.saveCredentials),
      exhaustMap(({ id, pluginType, credentials }) => {
        return pluginService.saveCredentials(credentials).pipe(
          map(() => PluginActions.saveCredentialsSuccess({ id, pluginType })),
          catchError((error) => of(PluginActions.saveCredentialsFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

/**
 * Note: We need to re-iniitalize to fetch data for the settings retrieve call depending on credentials after saved them successfully.
 * For Nexmo and Twilio this are the inbound numbers fetched from the API.
 */
export const saveCredentialsSuccess$ = createEffect(
  ($actions = inject(Actions)) => {
    return $actions.pipe(
      ofType(PluginActions.saveCredentialsSuccess),
      exhaustMap(({ id, pluginType }) => {
        const isCreate = id === 0;
        return of(PluginActions.initPluginSettings({ id, pluginType, isCreate }));
      })
    );
  },
  { functional: true }
);

export const checkDeleteDependencies$ = createEffect(
  (
    actions$ = inject(Actions),
    pluginService = inject(PluginApiProxyService),
    modalService = inject(ModalService),
    navigationService = inject(KtNavigationService),
    translateService = inject(TranslateService)
  ) => {
    return actions$.pipe(
      ofType(PluginActions.checkDeleteDependencies),
      exhaustMap(({ id, name, pluginType }) => {
        pluginService.setType(pluginType);
        return pluginService.getDependencies(id).pipe(
          switchMap(({ data }) => {
            const { title, message } = getDependenciesCheckConfig(pluginType);
            const modalData = modalService.createDefaultDeleteWithDependencyCheckModal(data.dependencies, translateService.translate(title), message, name);
            return modalService.openWarnModal(modalData).pipe(
              switchMap((result) => {
                if (result && result.clickedUrl && result.id) {
                  navigationService.routeTo(result.clickedUrl);
                  return of(PluginActions.checkDeleteDependenciesCancel());
                }

                if (result && result.apply) {
                  return of(PluginActions.deleteEntity({ id, pluginType }));
                }

                return of(PluginActions.checkDeleteDependenciesCancel());
              })
            );
          })
        );
      })
    );
  },
  { functional: true }
);

export const deleteEntity$ = createEffect(
  (actions$ = inject(Actions), pluginService = inject(PluginApiProxyService)) => {
    return actions$.pipe(
      ofType(PluginActions.deleteEntity),
      exhaustMap(({ id, pluginType }) => {
        return pluginService.delete(id).pipe(
          map((response) => {
            const message = getPluginComponentMessage(pluginType, 'deleteSuccess');
            return PluginActions.deleteEntitySuccess({ message: response?.data?.message ?? message });
          }),
          catchError((error) => of(PluginActions.deleteEntityFailure({ error })))
        );
      })
    );
  },
  { functional: true }
);

export const createSuccessMessage$ = createEffect(
  (actions$ = inject(Actions), messageService = inject(KtMessageService), translateService = inject(TranslateService), navigationService = inject(KtNavigationService)) => {
    return actions$.pipe(
      ofType(PluginActions.createEntitySuccess),
      tap(({ message }) => messageService.success(translateService.translate(message))),
      map(({ links }) => navigationService.routeTo(links.edit))
    );
  },
  { functional: true, dispatch: false }
);

export const saveSuccessMessage$ = createEffect(
  (actions$ = inject(Actions), pluginService = inject(PluginApiProxyService), messageService = inject(KtMessageService), translateService = inject(TranslateService)) => {
    return actions$.pipe(
      ofType(PluginActions.saveEntitySuccess),
      tap(({ message }) => messageService.success(translateService.translate(message))),
      map(({ id }) => PluginActions.initPluginSettings({ id, pluginType: pluginService.getType() as PluginTypeEnum, isCreate: false }))
    );
  },
  { functional: true }
);

export const deleteSuccessMessage$ = createEffect(
  (actions$ = inject(Actions), messageService = inject(KtMessageService), translateService = inject(TranslateService)) => {
    return actions$.pipe(
      ofType(PluginActions.deleteEntitySuccess),
      tap(({ message }) => messageService.success(translateService.translate(message))),
      map(() => PluginActions.navigateToOverview())
    );
  },
  { functional: true }
);

export const navigateToOverview$ = createEffect(
  (actions$ = inject(Actions), pluginFacade = inject(PluginFacade), navigationService = inject(KtNavigationService)) => {
    return actions$.pipe(
      ofType(PluginActions.navigateToOverview),
      concatLatestFrom(() => pluginFacade.settingsLinks$),
      tap(([_, links]: [Action, PluginEntityLinks]) => void navigationService.routeTo(links.overview))
    );
  },
  { functional: true, dispatch: false }
);

export const apiError$ = createEffect(
  (actions$ = inject(Actions), errorService = inject(ErrorBaseService)) => {
    return actions$.pipe(
      ofType(
        PluginActions.loadPluginOverviewFailure,
        PluginActions.loadPluginSettingsFailure,
        PluginActions.createEntityFailure,
        PluginActions.saveEntityFailure,
        PluginActions.deleteEntityFailure
      ),
      tap(({ error }) => {
        if (error?.status !== 406) {
          errorService.handleError(structuredClone(error));
        }
      })
    );
  },
  { functional: true, dispatch: false }
);

import { inject, Injectable } from '@angular/core';
import { select, Store } from '@ngrx/store';
import { PluginTypeEnum } from '../general/plugin-component-map';
import { PluginCredentials, PluginEntity } from '../models/plugin-api';
import * as PluginActions from './plugin.actions';
import * as PluginSelectors from './plugin.selectors';

export interface PluginInitializationParameters {
  id: number;
  copyId: number;
  pluginType: PluginTypeEnum;
  isCreate: boolean;
  isCopy: boolean;
}

export interface PluginCreateParameters {
  entity: PluginEntity;
  copyId: number;
}

@Injectable()
export class PluginFacade {
  private readonly store = inject(Store);

  overviewLoaded$ = this.store.pipe(select(PluginSelectors.selectOverviewLoaded));
  overviewEntities$ = this.store.pipe(select(PluginSelectors.selectOverviewAllEntities));
  overviewSelectedEntity$ = this.store.pipe(select(PluginSelectors.selectOverviewEntity));
  overviewCreateTypes$ = this.store.pipe(select(PluginSelectors.selectOverviewCreateTypes));
  overviewCreateOptions$ = this.store.pipe(select(PluginSelectors.selectOverviewCreateOptions));
  overviewFilterOptions$ = this.store.pipe(select(PluginSelectors.selectOverviewFilterOptions));

  settingsLoaded$ = this.store.pipe(select(PluginSelectors.selectSettingsLoaded));
  settingsEntity$ = this.store.pipe(select(PluginSelectors.selectSettingsEntity));
  settingsLinks$ = this.store.pipe(select(PluginSelectors.selectSettingsLinks));
  settingsDisplayOptions$ = this.store.pipe(select(PluginSelectors.selectSettingsDisplayOptions));
  settingsFilterOptions$ = this.store.pipe(select(PluginSelectors.selectSettingsFilterOptions));
  settingsCacheOptions$ = this.store.pipe(select(PluginSelectors.selectSettingsCacheOptions));
  settingsError$ = this.store.pipe(select(PluginSelectors.selectSettingsError));

  hasCredentials$ = this.store.pipe(select(PluginSelectors.selectSettingsHasCredentials));

  initOverview(): void {
    this.store.dispatch(PluginActions.initPluginOverview());
  }

  initSettings(options: PluginInitializationParameters): void {
    this.store.dispatch(PluginActions.initPluginSettings(options));
  }

  create(options: PluginCreateParameters): void {
    if (!options.entity) {
      return;
    }
    this.store.dispatch(PluginActions.createEntity(options));
  }

  save(entity: PluginEntity): void {
    if (!entity) {
      return;
    }
    this.store.dispatch(PluginActions.saveEntity({ entity }));
  }

  saveCredentials(id: number, pluginType: PluginTypeEnum, credentials: PluginCredentials): void {
    this.store.dispatch(PluginActions.saveCredentials({ id, pluginType, credentials }));
  }

  deleteWithDependenciesCheck(id: number, name: string, pluginType: PluginTypeEnum): void {
    this.store.dispatch(PluginActions.checkDeleteDependencies({ id, name, pluginType }));
  }

  delete(id: number, pluginType: PluginTypeEnum): void {
    this.store.dispatch(PluginActions.deleteEntity({ id, pluginType }));
  }

  resetSettingsState(): void {
    this.store.dispatch(PluginActions.resetSettingsState());
  }
}

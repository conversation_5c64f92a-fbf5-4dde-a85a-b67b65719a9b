import { AsyncPipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faPlus } from '@fortawesome/pro-regular-svg-icons';
import { KtButtonComponent, KtNavigationService, KtNotificationComponent, KtTableComponent, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { BehaviorSubject, switchMap } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { PluginFacade } from '../../+state/plugin.facade';
import { TabTitleService } from '../../../../services/tab-title.service';
import { OnReadyBaseComponent } from '../../../forms.shared/components/on-ready/on-ready-base.component';
import { PluginOverviewCreateTypes } from '../../models/plugin-api';
import { PluginOverviewTableService } from '../../services/plugin-overview-table.service';
import { FilterChangeEvent, PluginOverviewFilterPanelComponent } from './plugin-overview-filter-panel/plugin-overview-filter-panel.component';

@Component({
  selector: 'kt-plugin-overview',
  imports: [AsyncPipe, KtTableComponent, TranslatePipe, KtNotificationComponent, KtButtonComponent, PluginOverviewFilterPanelComponent],
  templateUrl: './plugin-overview.component.html'
})
export class PluginOverviewComponent extends OnReadyBaseComponent implements OnInit {
  protected readonly title = this.translateService.translate('Plugin::Overview::Plugin Overview');
  private readonly pluginFacade = inject(PluginFacade);
  private readonly tableService = inject(PluginOverviewTableService);
  private readonly navigationService = inject(KtNavigationService);
  private readonly tabTitleService = inject(TabTitleService);

  iconPlus: IconProp = faPlus as IconProp;

  filter$ = new BehaviorSubject<FilterChangeEvent>({ type: null, tag: [], optIn: [] });
  filteredTableItem$ = this.filter$.asObservable().pipe(
    switchMap((filter) => {
      return this.pluginFacade.overviewEntities$.pipe(
        map((items) => {
          const filteredItems = items.filter((item) => {
            let passFilter = true;

            if (filter.type) {
              passFilter = item.type === filter.type;
            }

            if (passFilter && filter.optIn.length) {
              passFilter = filter.optIn.includes(item.relListId);
            }

            if (passFilter && filter.tag.length) {
              passFilter = filter.tag.includes(item.assignTagId);
            }

            return passFilter;
          });
          return this.tableService.createOverviewTable(filteredItems);
        })
      );
    })
  );

  createTypes$ = this.pluginFacade.overviewCreateTypes$;
  loaded$ = this.pluginFacade.overviewLoaded$.pipe(tap((loaded) => this.setReady(loaded)));

  optInOptions$ = this.pluginFacade.overviewFilterOptions$.pipe(map((options) => options?.optInOptions ?? []));
  typeOptions$ = this.pluginFacade.overviewFilterOptions$.pipe(map((options) => options?.typeOptions ?? []));
  assignTagOptions$ = this.pluginFacade.overviewFilterOptions$.pipe(map((options) => options?.assignTagOptions ?? []));

  constructor(protected translateService: TranslateService) {
    super(translateService, 'plugin-overview');
  }

  ngOnInit(): void {
    this.tabTitleService.setTabName(this.title);
    this.pluginFacade.initOverview();
  }

  addPlugin(btn: PluginOverviewCreateTypes): void {
    this.navigationService.routeTo(btn.createUrl);
  }

  onFilterChange(filter: FilterChangeEvent): void {
    this.filter$.next(filter);
  }
}

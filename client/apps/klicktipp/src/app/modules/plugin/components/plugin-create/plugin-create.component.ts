import { AsyncPipe, NgOptimizedImage } from '@angular/common';
import { Component, inject, OnInit, DOCUMENT } from '@angular/core';
import { KtNavigationService, KtNotificationComponent, TranslateService } from '@klicktipp/kt-mat-library';
import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged, map, tap } from 'rxjs/operators';
import { PluginFacade } from '../../+state/plugin.facade';
import { OnReadyBaseComponent } from '../../../forms.shared/components/on-ready/on-ready-base.component';
import { PluginIconBreakPoint, PluginIconSize } from '../../general/plugin-icons';
import { PluginOverviewCreateOptionsGroup } from '../../models/plugin-api';

@Component({
  selector: 'kt-plugin-create',
  imports: [AsyncPipe, KtNotificationComponent, NgOptimizedImage],
  templateUrl: './plugin-create.component.html',
})
export class PluginCreateComponent extends OnReadyBaseComponent implements OnInit {
  private readonly pluginFacade = inject(PluginFacade);
  private readonly navigationService = inject(KtNavigationService);
  private readonly document = inject(DOCUMENT);

  createGroups$ = this.pluginFacade.overviewCreateOptions$.pipe(map((optionGroups) => Object.values(optionGroups) as PluginOverviewCreateOptionsGroup[]));
  loaded$ = this.pluginFacade.overviewLoaded$.pipe(tap((loaded) => this.setReady(loaded)));

  showIconTitle = true;
  pluginIconSize$: Observable<number>;
  PluginIconSize = PluginIconSize;
  private pluginIconSizeState$ = new BehaviorSubject<number>(PluginIconSize.LARGE);

  constructor(protected translateService: TranslateService) {
    super(translateService, 'plugin-create');
    this.pluginIconSize$ = this.pluginIconSizeState$.asObservable().pipe(distinctUntilChanged());
  }

  ngOnInit(): void {
    this.pluginFacade.initOverview();
    this.initWindowWidthListener();
  }

  onIconClick(url: string): void {
    this.navigationService.routeTo(url);
  }

  private initWindowWidthListener(): void {
    const window = this.document.defaultView;
    this.updatePluginIconSizeState(window.innerWidth);
    this.updateShowIcon(window.innerWidth);

    const onWindowResizeHandler = (): void => {
      this.updatePluginIconSizeState(window.innerWidth);
      this.updateShowIcon(window.innerWidth);
    };
    window.addEventListener('resize', onWindowResizeHandler);
    this.destroyRef.onDestroy(() => window.removeEventListener('resize', onWindowResizeHandler));
  }

  private updatePluginIconSizeState(innerWindowWidth: number): void {
    let size = PluginIconSize.LARGE;
    if (innerWindowWidth <= PluginIconBreakPoint.SMALL) {
      size = PluginIconSize.SMALL;
    } else if (innerWindowWidth <= PluginIconBreakPoint.MEDIUM) {
      size = PluginIconSize.MEDIUM;
    }
    this.pluginIconSizeState$.next(size);
  }

  private updateShowIcon(innerWindowWidth: number): void {
    this.showIconTitle = innerWindowWidth > PluginIconBreakPoint.SMALL;
  }
}

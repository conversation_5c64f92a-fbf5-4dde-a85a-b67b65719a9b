import { isTranslate<PERSON>ey } from '../../../../../../static/translate';
import { PluginComponentConfig } from '../../../../models/component-config';
import { GenericComponent } from './generic.component';

const GenericConfig: PluginComponentConfig = {
  component: GenericComponent,
  data: {
    key: 'generic-listbuilding',
    title: {
      create: isTranslate<PERSON>ey('Listbuilding::Generic::Title::Create Generic'),
      edit: isTranslateKey('Listbuilding::Generic::Title::Edit Generic')
    },
    message: {
      createSuccess: isTranslate<PERSON>ey('Listbuilding::Generic::Message::Create Success'),
      saveSuccess: isTranslateKey('Listbuilding::Generic::Message::Save Success'),
      deleteSuccess: isTranslateKey('Listbuilding::Generic::Message::Delete Success')
    }
  }
};

export default GenericConfig;

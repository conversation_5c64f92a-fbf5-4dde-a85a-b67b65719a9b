import { ComponentRef } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { KtNavigationService, TranslateService } from '@klicktipp/kt-mat-library';
import { MockProvider } from 'ng-mocks';
import { of } from 'rxjs';
import { PluginFacade } from '../../../../+state/plugin.facade';
import { CacheFacadeProviderMock } from '../../../../../../mocks/cache-facade.mock';
import { LabelServiceMock } from '../../../../../../mocks/label-service.mock';
import { OptInServiceMock } from '../../../../../../mocks/opt-in-service.mock';
import { TagServiceMock } from '../../../../../../mocks/tag-service.mock';
import { ConfigService } from '../../../../../../services/config.service';
import { LabelService } from '../../../../../../services/entities/label.service';
import { OptInService } from '../../../../../../services/entities/opt-in.service';
import { TagService } from '../../../../../../services/entities/tag.service';
import { ErrorBaseService } from '../../../../../../services/error-base.service';
import { ModalService } from '../../../../../../services/modal.service';
import { PluginTypeEnum } from '../../../../general/plugin-component-map';
import { NexmoComponent } from './nexmo.component';

describe('NexmoComponent', () => {
  let component: NexmoComponent;
  let componentRef: ComponentRef<NexmoComponent>;
  let fixture: ComponentFixture<NexmoComponent>;
  let pluginFacade: PluginFacade;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NexmoComponent, NoopAnimationsModule],
      providers: [
        MockProvider(TranslateService),
        MockProvider(PluginFacade, {
          ********Error$: of(null),
          hasCredentials$: of(true)
        }),
        MockProvider(KtNavigationService),
        MockProvider(ModalService),
        MockProvider(ErrorBaseService),
        MockProvider(ConfigService),
        MockProvider(TagService, TagServiceMock),
        MockProvider(LabelService, LabelServiceMock),
        MockProvider(OptInService, OptInServiceMock),
        ...CacheFacadeProviderMock
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(NexmoComponent);
    pluginFacade = TestBed.inject(PluginFacade);
    component = fixture.componentInstance;
    componentRef = fixture.componentRef;
    componentRef.********('configData', { title: { create: 'Create Nexmo', edit: 'Edit Nexmo' } });
    componentRef.********('routeData', { isCreate: false });
    componentRef.********('filterOptions', { inboundNumbers: [] });
    componentRef.********('entity', {
      id: 42,
      type: PluginTypeEnum.Nexmo,
      name: 'Test-Entity Nexmo',
      links: {
        overview: `/plugin/${PluginTypeEnum.Nexmo}/overview/me`,
        copy: `/plugin/${PluginTypeEnum.Nexmo}/********/me/create/42`
      },
      metaLabels: [],
      notes: '',
      useNotes: 0,
      optInSubscribeTo: { ids: [113], new: [] },
      relListId: 2,
      inboundNumber: '123',
      keyword: 'keyword'
    });
    componentRef.********('links', { overview: `/plugin/${PluginTypeEnum.Nexmo}/overview/me` });
  });

  it('should create', () => {
    expect(component).toBeInstanceOf(NexmoComponent);
  });

  it('should map the form values to an entity properly for create', waitForAsync(async () => {
    fixture.detectChanges();
    await fixture.whenStable();

    const entity = {
      id: 42,
      type: PluginTypeEnum.Nexmo,
      name: 'Test-Entity Nexmo Create',
      links: {
        overview: `/plugin/${PluginTypeEnum.Nexmo}/overview/me`,
        copy: `/plugin/${PluginTypeEnum.Nexmo}/********/me/create/42`
      },
      metaLabels: [],
      notes: '',
      useNotes: 0,
      optInSubscribeTo: { ids: [113], new: [] },
      relListId: 2,
      inboundNumber: '123',
      keyword: 'keyword'
    };
    const createSpy = jest.spyOn(pluginFacade, 'create').mockImplementation(() => null);
    component.createEntity(entity);
    expect(createSpy).toHaveBeenCalledWith({ entity, copyId: 0 });
  }));

  it('should map the form values to an entity properly for update', waitForAsync(async () => {
    fixture.detectChanges();
    await fixture.whenStable();

    const entity = {
      id: 42,
      type: PluginTypeEnum.Nexmo,
      name: 'Test-Entity Nexmo Update',
      links: {
        overview: `/plugin/${PluginTypeEnum.Nexmo}/overview/me`,
        copy: `/plugin/${PluginTypeEnum.Nexmo}/********/me/create/42`
      },
      metaLabels: [],
      notes: '',
      useNotes: 0,
      optInSubscribeTo: { ids: [113], new: [] },
      relListId: 2,
      inboundNumber: '123',
      keyword: 'keyword'
    };
    const saveSpy = jest.spyOn(pluginFacade, 'save').mockImplementation(() => null);
    component.updateEntity(entity);
    expect(saveSpy).toHaveBeenCalledWith(entity);
  }));

  it('should navigate on back', () => {
    const navigationService = fixture.debugElement.injector.get(KtNavigationService);
    const routeToSpy = jest.spyOn(navigationService, 'routeTo').mockImplementation(() => null);
    component.back();
    expect(routeToSpy).toHaveBeenCalledWith(`/plugin/${PluginTypeEnum.Nexmo}/overview/me`);
  });

  it('should navigate on back', () => {
    const navigationService = fixture.debugElement.injector.get(KtNavigationService);
    const routeToSpy = jest.spyOn(navigationService, 'routeTo').mockImplementation(() => null);
    component.copyEntity();
    expect(routeToSpy).toHaveBeenCalledWith(`/plugin/${PluginTypeEnum.Nexmo}/********/me/create/42`);
  });

  it('should call delete with the entity id', () => {
    const deleteSpy = jest.spyOn(pluginFacade, 'deleteWithDependenciesCheck').mockImplementation(() => null);
    component.deleteEntity({ id: 42, name: 'Test-Entity Nexmo Update', type: PluginTypeEnum.Nexmo });
    expect(deleteSpy).toHaveBeenCalledWith(42, 'Test-Entity Nexmo Update', PluginTypeEnum.Nexmo);
  });

  it('should call saveCredentials with the given form data', waitForAsync(async () => {
    fixture.detectChanges();
    await fixture.whenStable();

    const formData = { apiKey: 'apiKey', apiSecret: 'apiSecret' };
    const saveCredentialsSpy = jest.spyOn(pluginFacade, 'saveCredentials').mockImplementation(() => of(null));
    component.saveCredentials(formData);

    expect(saveCredentialsSpy).toHaveBeenCalledWith(42, PluginTypeEnum.Nexmo, formData);
  }));
});

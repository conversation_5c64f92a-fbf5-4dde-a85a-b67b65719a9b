import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import {
  InputItem,
  KtButtonComponent,
  KtInputComponent,
  KtMessageService,
  KtPlaceholderDialogComponent,
  KtPlaceholders,
  KtPlaceholderTag,
  KtTagResult,
  ModalData,
  TranslatePipe,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { MatDialog } from '@angular/material/dialog';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { filter } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { KtEmailGenerateSubjectResponse } from './../../models/api/kt-email-generate-subject.response';
import { BeeBaseEntity } from './../../../bee-shared/models/bee-editor/bee-base-entity.interface';
import { faSpinner, faStars } from '@fortawesome/pro-regular-svg-icons';
import { SmartCopySharedService } from './../../services/smart-copy-shared.service';
import { ConfigService } from './../../../../services/config.service';
import { AccountAccess } from './../../../../enums/account-access.enum';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { ModalService } from './../../../../services/modal.service';
import { ContentCreatorBaseService } from './../../services/content-creator-base.service';
import { MatListModule } from '@angular/material/list';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { MatButtonModule } from '@angular/material/button';
import { NgClass } from '@angular/common';

@Component({
  selector: 'kt-smart-subject-base',
  templateUrl: './smart-subject-base.component.html',
  imports: [MatButtonModule, FaIconComponent, TranslatePipe, KtButtonComponent, ReactiveFormsModule, MatListModule, KtInputComponent, NgClass]
})
export class SmartSubjectBaseComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() placeholders: KtPlaceholders;
  @Input() form: FormGroup = new FormGroup({});
  @Input() showInput = true;
  @Input() hideGenerateButton = false;
  @Input() inputId: string;
  @Input() disabled: boolean;
  @Output() updateForm: EventEmitter<string> = new EventEmitter<string>();

  @Input() set emailEntity(entity: BeeBaseEntity) {
    if (entity) {
      this.myEntity = entity;
      this.setSubject(this.emailEntity.subject);
    }
  }

  get emailEntity(): BeeBaseEntity {
    return this.myEntity;
  }

  smartSubjectInput: InputItem;

  sendSubjectRequestTitle = this.translateService.translate('smart-subject-dialog::button::Suggestions are being generated');
  openPlaceholderTitle = this.translateService.translate('smart-subject-dialog::button::open placeholder dialog');
  selectBtn = this.translateService.translate('smart-subject-dialog::button::select');

  loadingText = this.translateService.translate('smart-subject-dialog::loading::suggestions are generated');
  suggestions: string[] = [];
  loadingResults: boolean;
  starIcon: IconProp = faStars as IconProp;
  upsellOpen: boolean;

  upsellModalData: ModalData = {
    id: 'upsellModal',
    titleIcon: this.starIcon,
    title: this.translateService.translate('smart-subject-dialog::modal::upsell::title::This functionality is not available'),
    message: this.translateService.translate('smart-subject-dialog::modal::upsell::message::If you want use Smart subject go to [Upsell Link](https://www.klicktipp.com/)'),
    titleBackgroundColor: '#ef7700',
    noButtons: true,
    messageIsHtml: true
  };
  readonly accessRights = AccountAccess;
  readonly faLoading = faSpinner;

  private subscriptions: Subscription = new Subscription();
  private myEntity: BeeBaseEntity;

  constructor(
    private translateService: TranslateService,
    private messageService: KtMessageService,
    private emailService: ContentCreatorBaseService,
    private matDialog: MatDialog,
    private smartCopySharedService: SmartCopySharedService,
    public configService: ConfigService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.suggestions = [];
    this.smartSubjectInput = {
      id: this.inputId ?? 'smart-subject',
      type: 'text',
      quickhelp: {
        id: this.inputId ?? 'smart-subject-input-quickhelp',
        message: this.translateService.translate('smart-subject-dialog::input::quickhelp::message'),
        header: this.translateService.translate('smart-subject-dialog::input::quickhelp::header')
      },
      labelText: this.translateService.translate('smart-subject-dialog::input::label::subject'),
      clearIcon: true,
      value: this.emailEntity?.subject
    };
  }

  ngAfterViewInit(): void {
    if (this.showInput) {
      this.form.get(this.smartSubjectInput.id).valueChanges.subscribe((val) => {
        this.updateForm.next(val);
      });
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  sendSubjectRequest(): void {
    if (this.upsellOpen) {
      return;
    }
    if (!this.configService.hasAccess(AccountAccess.SmartSubjectAccess)) {
      this.upsellOpen = true;
      this.modalService.openWarnModal(this.upsellModalData).subscribe(() => (this.upsellOpen = false));
      return;
    }
    this.suggestions = [];
    this.loadingResults = true;
    this.emailService.generateSubjectSuggestions(this.emailEntity.id.toString(), this.emailEntity.html?.toString()).subscribe(
      (responses: KtEmailGenerateSubjectResponse) => {
        if (responses?.data?.choices?.length === 0 || !responses?.data?.choices) {
          this.messageService.warning(this.translateService.translate('smart-subject-dialog::api-error::no-result'));
        } else {
          this.suggestions = responses.data.choices;
        }
        this.loadingResults = false;
      },
      (error) => {
        if (error.errors && error.errors[0]?.code) {
          this.messageService.error(this.smartCopySharedService.getErrorFromCode(error.errors[0]?.code, error.errors[0]?.message));
        }
        this.loadingResults = false;
      }
    );
  }

  openPlaceholderDialog(): void {
    const sub = this.matDialog
      .open<KtPlaceholderDialogComponent<KtPlaceholderTag>, KtPlaceholders, KtTagResult>(KtPlaceholderDialogComponent, {
        data: this.placeholders,
        panelClass: 'kt-mat-library'
      })
      .afterClosed()
      .pipe(filter((res) => !!res?.value))
      .subscribe((res) => this.insertPlaceholder(res.plain));
    this.subscriptions.add(sub);
  }

  setSubject(subject: string): void {
    this.form.get(this.smartSubjectInput?.id)?.setValue(subject);
  }

  private insertPlaceholder(res: string): void {
    const currentVal = this.form.get(this.smartSubjectInput.id)?.value;
    const inputList = document.getElementsByTagName('input');
    const inputs = Array.from(inputList);
    let mainInput: HTMLInputElement;
    for (const input of inputs) {
      const e2eId = input.getAttribute('data-e2e-id');
      if (e2eId === this.smartSubjectInput.id + '-input') {
        mainInput = input as HTMLInputElement;
        break;
      }
    }
    if (mainInput && currentVal) {
      const position = mainInput.selectionEnd;
      res = [currentVal.slice(0, position), res, currentVal.slice(position)].join('');
    }
    this.setSubject(res);
  }
}

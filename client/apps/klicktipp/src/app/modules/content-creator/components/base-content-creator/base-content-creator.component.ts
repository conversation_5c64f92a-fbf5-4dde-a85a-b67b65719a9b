import { Clipboard } from '@angular/cdk/clipboard';
import { NgClass } from '@angular/common';
import { AfterViewInit, Component, DestroyRef, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faCopy, faPaperPlane, faSpinner, faTimes } from '@fortawesome/pro-regular-svg-icons';
import { KtButtonComponent, KtTextareaComponent, TextAreaItem, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { AccountAccess } from '../../../../enums/account-access.enum';
import { ConfigService } from '../../../../services/config.service';
import { ContentCreatorResponse } from '../../models/contentCreatorBase';
import { ContentCreatorBaseService } from '../../services/content-creator-base.service';
import { SmartCopySharedService } from '../../services/smart-copy-shared.service';

@Component({
  selector: 'kt-base-content-creator',
  templateUrl: './base-content-creator.component.html',
  imports: [NgClass, ReactiveFormsModule, KtButtonComponent, KtTextareaComponent, FaIconComponent, TranslatePipe]
})
export class BaseContentCreatorComponent implements OnInit, AfterViewInit {
  protected readonly destroyRef = inject(DestroyRef);

  @Input() isEmailEditor: boolean;
  @Input() dialogMode: boolean;
  @Output() sendTextToDnd: EventEmitter<string> = new EventEmitter<string>();
  @Output() closeDialog: EventEmitter<null> = new EventEmitter<null>();
  formGroup: FormGroup = new FormGroup({});

  faTimes = faTimes as IconProp;
  faCopy = faCopy as IconProp;
  faUpload = faPaperPlane as IconProp;
  faSpinner = faSpinner as IconProp;

  hasAccess: boolean;
  apiErrorMessage: string;
  apiLoading = false;
  symfony: boolean;

  inputArea: TextAreaItem = {
    id: 'inputArea',
    cols: '1',
    rows: '5',
    labelText: this.translateService.translate('smartcopywriting::base::label::Your text assignment:'),
    placeholder: this.translateService.translate('smartcopywriting::base::inputplaceholderrequest::Write an SEO optimized text on the topic Automated Email Marketing'),
    required: true
  };

  outputArea: TextAreaItem = {
    id: 'outputArea',
    cols: '1',
    rows: '15',
    labelText: '',
    placeholder: this.translateService.translate('smartcopywriting::base::textareaplaceholderapiresponse::Your result will be posted here'),
    required: false
  };

  apiResponse: FormControl;
  customerInput: FormControl;

  constructor(
    private contentCreatorBaseService: ContentCreatorBaseService,
    private clipboard: Clipboard,
    private translateService: TranslateService,
    private smartCopySharedService: SmartCopySharedService,
    private activatedRoute: ActivatedRoute,
    private configService: ConfigService
  ) {}

  ngOnInit(): void {
    this.symfony = !!this.activatedRoute.snapshot.data?.symfony;
    this.hasAccess = this.configService.hasAccess(AccountAccess.GptModelSelectAccess);
  }

  ngAfterViewInit(): void {
    this.apiResponse = this.formGroup.get(this.outputArea.id) as FormControl;
    this.apiResponse.disable();
    this.customerInput = this.formGroup.get(this.inputArea.id) as FormControl;
  }

  copyToClipboard(): void {
    if (this.apiResponse && this.apiResponse.value) {
      this.clipboard.copy(this.apiResponse.value);
    }
  }

  send(): void {
    this.apiErrorMessage = '';
    this.apiLoading = true;
    this.callSymfonyRoute();
  }

  insertIntoDnd(): void {
    this.sendTextToDnd.emit(this.apiResponse?.value);
  }

  private callSymfonyRoute(): void {
    this.contentCreatorBaseService
      .copyWriter(this.customerInput.value, 'fast')
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response: ContentCreatorResponse) => {
          this.apiLoading = false;
          if (response.code) {
            this.apiErrorMessage = this.smartCopySharedService.getErrorFromCode(response.code);
            return;
          }
          this.apiResponse.setValue(response.data.answer);
        },
        error: (error) => {
          this.apiLoading = false;
          this.closeDialog.emit();
          if (error.errors && error.errors.length > 0) {
            this.apiErrorMessage = this.smartCopySharedService.getErrorFromCode(error.errors[0]?.code);
          }
        }
      });
  }
}

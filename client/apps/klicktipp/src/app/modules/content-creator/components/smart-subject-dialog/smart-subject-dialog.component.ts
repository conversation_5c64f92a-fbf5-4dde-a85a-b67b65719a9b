import { Component, Inject } from '@angular/core';
import { Kt<PERSON>uttonComponent, KtMessageComponent, KtMessageService, KtPlaceholders, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { faTimes } from '@fortawesome/pro-light-svg-icons';
import { EmailService } from './../../../email/email-editor/services/email.service';
import { ErrorItems } from './../../../../models/error/custom-error';
import { FormGroup } from '@angular/forms';
import { BeeBaseEntity } from './../../../bee-shared/models/bee-editor/bee-base-entity.interface';
import { KtEmailSaveSubjectResponseData } from './../../models/api/kt-email-generate-subject.response';
import { SmartSubjectBaseComponent } from './../smart-subject-base/smart-subject-base.component';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';

@Component({
    selector: 'kt-smart-subject-dialog',
    templateUrl: './smart-subject-dialog.component.html',
    imports: [SmartSubjectBaseComponent, KtButtonComponent, KtMessageComponent, TranslatePipe, FaIconComponent]
})
export class SmartSubjectDialogComponent {
  readonly faTimes = faTimes;
  emailEntity: BeeBaseEntity;
  placeholders: KtPlaceholders;
  subject: string;

  saveBtn = this.translateService.translate('smart-subject-dialog::button::save');
  cancelBtn = this.translateService.translate('smart-subject-dialog::button::cancel');
  form: FormGroup = new FormGroup({});

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: { email: BeeBaseEntity; placeholders: KtPlaceholders },
    public dialogRef: MatDialogRef<SmartSubjectDialogComponent>,
    private translateService: TranslateService,
    private messageService: KtMessageService,
    private emailService: EmailService
  ) {
    this.emailEntity = data.email;
    this.placeholders = data.placeholders;
  }

  updateFormValues(subject: string): void {
    this.subject = subject;
  }

  save(): void {
    if (!this.subject) {
      this.showError(this.translateService.translate('smart-subject-dialog::errormsg::empty subject'));
      return;
    }
    this.emailEntity.subject = this.subject;
    this.emailService.saveSubject(this.emailEntity).subscribe(
      (res: KtEmailSaveSubjectResponseData) => {
        if (res) {
          this.dialogRef.close(this.subject);
        }
      },
      (err) => {
        this.messageService.error(this.handleSaveSubjectError(err));
      }
    );
  }

  private showError = (err: string): void => {
    this.messageService.error(err);
  };

  private handleSaveSubjectError(err: ErrorItems): string {
    switch (err?.errors[0]?.code) {
      case 'email::version_not_supported':
        return this.translateService.translate('smartsubject::base::errortext::default::wrong Angular version');

      case 'email::no_subject':
        return this.translateService.translate('smartsubject::base::errortext::default::Subject empty');

      case 'email::subject_too_long':
        return this.translateService.translate('smartsubject::base::errortext::default::subject too long');

      case 'email::publish':
        return this.translateService.translate('smartsubject::base::errortext::default::publish failed');

      default:
        return this.translateService.translate('smartcopywriting::base::errortext::default::Unknown error - something went horribly wrong');
    }
  }
}

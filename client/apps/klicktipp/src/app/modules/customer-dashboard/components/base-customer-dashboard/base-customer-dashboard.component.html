<div class="kt-mat-library">
  <div class="kt-module">
    <section class="kt-drupal-theme kt-module-wrapper mt-90 mb-70">
      @if (!initFinished) {
        <kt-notification [hasSpinner]="true" [text]="notificationText" [nodeId]="notificationNodeId"></kt-notification>
      }
      @if (initFinished) {
        <div>
          <div class="title-container">
            <h1 [innerHTML]="welcomeString"></h1>
            @if (displayToggle) {
              <mat-slide-toggle class="dashboard-toggle" ngDefaultControl [(ngModel)]="showAnalyticsBoard">
                {{ 'Dashboard::NewCustomer::Toggle::Analytics Board' | translate }}
              </mat-slide-toggle>
            }
          </div>
          @if (showAnalyticsBoard && dashboardData) {
            <kt-analytics-dashboard [dashboardData]="dashboardData" (refreshData)="getData(false)"></kt-analytics-dashboard>
          }
          @if (!showAnalyticsBoard && dashboardData) {
            <kt-new-customer-dashboard [dashboardData]="dashboardData"></kt-new-customer-dashboard>
          }
        </div>
      }
    </section>
  </div>
</div>

import { AfterViewInit, Component, Input, OnDestroy } from '@angular/core';
import { Dashboard } from './../../models/dashboard';
import { ProductFruitsService } from './../../services/product-fruits.service';
import { Subscription } from 'rxjs';
import { TranslatePipe } from '@klicktipp/kt-mat-library';

@Component({
    selector: 'kt-checklists',
    templateUrl: './checklists.component.html',
    imports: [TranslatePipe]
})
export class ChecklistsComponent implements AfterViewInit, OnDestroy {
  @Input() dashboardData: Dashboard;
  wrapper: HTMLElement;
  productFruitSubscription: Subscription;

  constructor(private productFruitsService: ProductFruitsService) {}

  ngAfterViewInit(): void {
    this.wrapper = document.getElementById('embedded-checklist');
    this.productFruitSubscription = this.productFruitsService.productFruitAvailableChecklists.subscribe((ids) => {
      ids.forEach((id) => {
        this.injectChecklist(id, this.wrapper);
      });
    });
  }

  ngOnDestroy(): void {
    if (this.productFruitSubscription) {
      this.productFruitSubscription.unsubscribe();
    }
  }

  injectChecklist(id, wrapper): void {
    this.productFruitsService.productFruits.api.checklists.injectToElement(id, wrapper);
  }
}

import { Component, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { KtNotificationComponent, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { AccountAccess } from '../../../../enums/account-access.enum';
import { ConfigService } from '../../../../services/config.service';
import { OnReadyBaseComponent } from '../../../forms.shared/components/on-ready/on-ready-base.component';
import { Dashboard } from '../../models/dashboard';
import { DashboardService } from '../../services/dashboard.service';
import { ProductFruitsService } from '../../services/product-fruits.service';
import { AnalyticsDashboardComponent } from '../analytics-dashboard/analytics-dashboard.component';
import { NewCustomerDashboardComponent } from '../new-customer-dashboard/new-customer-dashboard.component';

@Component({
  selector: 'kt-base-customer-dashboard',
  templateUrl: './base-customer-dashboard.component.html',
  imports: [TranslatePipe, KtNotificationComponent, MatSlideToggleModule, ReactiveFormsModule, FormsModule, AnalyticsDashboardComponent, NewCustomerDashboardComponent]
})
export class BaseCustomerDashboardComponent extends OnReadyBaseComponent implements OnInit {
  dashboardData: Dashboard;
  hasChecklist: boolean;
  showAnalyticsBoard: boolean;
  displayToggle: boolean;

  welcomeString = this.translateService.translate('Dashboard::NewCustomer::Title::Welcome');

  constructor(
    protected translateService: TranslateService,
    private dashboardService: DashboardService,
    private configService: ConfigService,
    private productFruitsService: ProductFruitsService
  ) {
    super(translateService, 'dashboard');
  }

  ngOnInit(): void {
    this.productFruitsService.hasProductFruitsChecklists.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((hasProductFruitsChecklists) => {
      this.displayToggle = hasProductFruitsChecklists;
      this.showAnalyticsBoard = !hasProductFruitsChecklists;
      if (this.configService.hasAccess(AccountAccess.ProductFruitsConsoleLogs)) {
        console.log('final: has PFs eligible checklists state:', hasProductFruitsChecklists);
      }
      this.setReady(true);
    });

    this.getData(true);
    const firstName = this.configService.getConfig()?.account.firstname;
    if (firstName) {
      this.welcomeString = this.translateService.translate('Dashboard::NewCustomer::Title::Customized::Welcome, @@name@@!', { name: firstName });
    }
  }

  getData(updateChecklists: boolean): void {
    this.dashboardService
      .getDashboardData()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((data) => {
        this.dashboardData = data;
        if (updateChecklists) {
          this.updateChecklists(data);
        }
      });
  }

  updateChecklists(data: Dashboard): void {
    this.hasChecklist = data?.data?.checklist.hasChecklists;
    if (this.hasChecklist && this.dashboardData?.data.checklist.checklistIdentifiers?.length > 0) {
      this.productFruitsService.getChecklists(this.dashboardData.data.checklist.checklistIdentifiers);
    } else {
      this.productFruitsService.hasProductFruitsChecklists.next(false);
    }
  }
}

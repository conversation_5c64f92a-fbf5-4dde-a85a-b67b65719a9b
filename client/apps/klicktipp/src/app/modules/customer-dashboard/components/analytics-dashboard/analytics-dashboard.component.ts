import { Breakpoints } from '@angular/cdk/layout';
import { NgClass } from '@angular/common';
import { AfterViewInit, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { FaIconLibrary, FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { faEraser } from '@fortawesome/pro-regular-svg-icons';
import { faCoffee } from '@fortawesome/pro-solid-svg-icons';
import {
  KtBreakpointObserverService,
  KtButtonComponent,
  KtMessageComponent,
  KtMessageService,
  KtQuickhelpComponent,
  KtTableComponent,
  QuickhelpItem,
  TableBase,
  TranslatePipe,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { ChartType, Formatter, getPackageForChart, GoogleChartsModule, Row, ScriptLoaderService } from 'angular-google-charts';
import { DashboardChartColors } from '../../../../enums/chart-colors.enum';
import { DashboardTableTypeEnum } from '../../../../enums/dashboard-table-type.enum';
import { GoogleLineChartOptions, GooglePieChartOptions, LineChartSeriesOptions } from '../../../../models/charts';
import { ConfigService } from '../../../../services/config.service';
import { TabTitleService } from '../../../../services/tab-title.service';
import { Dashboard } from '../../models/dashboard';
import { DashboardTableConvertService } from '../../services/dashboard-table-convert.service';
import { DashboardService } from '../../services/dashboard.service';

@Component({
  selector: 'kt-analytics-dashboard',
  templateUrl: './analytics-dashboard.component.html',
  imports: [KtMessageComponent, KtButtonComponent, KtTableComponent, TranslatePipe, NgClass, GoogleChartsModule, KtQuickhelpComponent, FontAwesomeModule]
})
export class AnalyticsDashboardComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  private readonly lineChartPackage = getPackageForChart(ChartType.Line);

  @Input() dashboardData: Dashboard;
  @Output() refreshData: EventEmitter<boolean> = new EventEmitter<boolean>();

  quickhelpHeader: QuickhelpItem = {
    id: 'dashboard-header-quickhelp',
    message: this.translateService.translate('Dashboard::Analytics::Quickhelp Header Message'),
    header: this.translateService.translate('Dashboard::Analytics::Quickhelp Header Title')
  };

  faEraser = faEraser;
  tagTable: TableBase;
  newsletterTable: TableBase;
  userLink: string;
  /*LINE CHART*/
  lineChartType = ChartType.LineChart;
  lineChartData: Row[];
  lineChartOptions: GoogleLineChartOptions;
  lineChartColumnNames: (string | { role: string; type: string; p?: { html: boolean } })[];
  lineChartFormatter: Formatter[];
  lineChartCss: string;

  /*PIE CHARTS*/
  pieChart = ChartType.PieChart;
  ispChartData: (string | number)[][];
  pieChartOptions: GooglePieChartOptions;
  ispChartColumnNames: (string | { role: string; type: string; p?: { html: boolean } })[];
  bounceChartData: (string | number)[][];
  bounceChartColumnNames: (string | { role: string; type: string; p?: { html: boolean } })[];
  pieChartCss: string;

  tagTableCss: string;
  campaignTableCss: string;

  btnClearCache = this.translateService.translate('Dashboard::Analytics::Button::Clear Cache');
  cacheClearMsg = this.translateService.translate('Dashboard::Analytics::Button::User cache cleared');

  private resizeTimeout: ReturnType<typeof setTimeout>;

  constructor(
    private translateService: TranslateService,
    private dashboardTableConvertService: DashboardTableConvertService,
    private configService: ConfigService,
    private loaderService: ScriptLoaderService,
    private ktMessageService: KtMessageService,
    private dashboardService: DashboardService,
    private breakpointObserverService: KtBreakpointObserverService,
    private tabTitleService: TabTitleService,
    library: FaIconLibrary,
    private sanitizer: DomSanitizer
  ) {
    library.addIcons(faCoffee, faChevronLeft, faChevronRight);
  }

  ngOnInit(): void {
    this.tabTitleService.setTabName(this.translateService.translate('Dashboard::Analytics::Tab Name::Dashboard'));
    this.userLink = `https://app.klicktipp.com/user/clearcache?destination=users/${this.configService.username}`;
    this.updateStats();
  }

  ngAfterViewInit(): void {
    // this.testIcon = this.sanitizer.bypassSecurityTrustHtml(`<p><b>Hint:</b> You can navigate items with ${this.icon1} and ${this.icon2} buttons.</p>`) as string;
    this.loaderService.loadChartPackages(this.lineChartPackage).subscribe(() => {
      // Start creating your chart now
      this.redrawChart();
      this.breakpointObserverService.getBreakpointObserver(Breakpoints.Medium).subscribe((_result) => {
        this.redrawChart();
      });
    });
    this.updateMessages();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!changes['dashboardData']?.isFirstChange() && this.dashboardData?.data) {
      this.updateStats();
      this.redrawChart();
      this.updateMessages();
    }
  }

  ngOnDestroy(): void {
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
    }
  }

  clearCache(): void {
    this.dashboardService.clearCache().subscribe(() => {
      this.ktMessageService.success(this.cacheClearMsg);
      this.refreshData.emit(true);
    });
  }

  private updateMessages(): void {
    this.showPreviewDataMsg();
    if (this.dashboardData.data.messages) {
      this.ktMessageService.handleMessages(this.dashboardData.data.messages);
    }
  }

  private updateStats(): void {
    if (!this.dashboardData?.data?.tables) {
      return;
    }

    const tagTable = this.dashboardData.data.tables.find((t) => t.id === 'top-5-tags');
    const newsletterTable = this.dashboardData.data.tables.find((t) => t.id === 'recent-campaigns');
    if (this.dashboardData.data.stats[tagTable.key]) {
      this.tagTable = this.dashboardTableConvertService.createDashboardTable(DashboardTableTypeEnum.Top5Tags, tagTable, this.dashboardData.data.stats[tagTable.key]);
    }
    if (this.dashboardData.data.stats[newsletterTable.key]) {
      this.newsletterTable = this.dashboardTableConvertService.createDashboardTable(
        DashboardTableTypeEnum.RecentCampaigns,
        newsletterTable,
        this.dashboardData.data.stats[newsletterTable.key]
      );
    }
  }

  private createCharts(): void {
    if (!this.dashboardData?.data?.stats) {
      return;
    }

    if (this.dashboardData.data.stats.subscriberActivity) {
      this.createLineChart();
    }
    if (this.dashboardData.data.stats.subscriberISPs) {
      this.createIspChart();
    }
    if (this.dashboardData.data.stats.subscriberBounces) {
      this.createBounceChart();
    }
    this.setChartPreviewStyles();
  }

  private createLineChart(): void {
    this.lineChartData = [];
    this.lineChartColumnNames = [];
    this.lineChartColumnNames.push(this.translateService.translate('Dashboard::Analytics::Charts::LineChart::Date'));
    this.lineChartColumnNames.push(this.translateService.translate('Dashboard::Analytics::Charts::LineChart::Subscriptions'));
    this.lineChartColumnNames.push(this.translateService.translate('Dashboard::Analytics::Charts::LineChart::Unsubscriptions'));
    this.lineChartColumnNames.push(this.translateService.translate('Dashboard::Analytics::Charts::LineChart::SMS Subscriptions'));

    this.lineChartFormatter = [{ formatter: new google.visualization.DateFormat({ pattern: 'dd.LL.' }), colIndex: 0 }];
    let count = 0;
    const ticks: Date[] = [];
    const data: Row[] = [];
    for (const subscriberData of this.dashboardData.data.stats.subscriberActivity) {
      const date = new Date(subscriberData[0]);
      data.push([date, subscriberData[1], subscriberData[2], subscriberData.length > 3 ? subscriberData[3] : 0]);
      // this is still cropped to 3rd value by google charts auto-scale, but better than without ticks
      if (count % 2 === 0) {
        ticks.push(date);
      }
      count++;
    }
    const series: LineChartSeriesOptions[] = [];
    for (let i = 0; i < this.lineChartColumnNames.length; i++) {
      series.push({
        pointShape: 'circle',
        pointsVisible: true,
        pointSize: 3
      });
    }
    this.lineChartOptions = {
      chartArea: {
        width: '85%' // Width of the line chart. If larger than 85%, larger scale values on the left won't fit
      },
      colors: Object.values(DashboardChartColors),
      legend: { position: 'top' },
      hAxis: {
        format: 'dd.LL.',
        ticks,
        gridlines: {
          count: 0
        }
      },
      series
    };
    this.lineChartData = data;
  }

  private createIspChart(): void {
    this.ispChartData = [];
    let count = 0;
    for (const subscriberData of this.dashboardData.data.stats.subscriberISPs) {
      this.ispChartData.push([`${subscriberData.text}${count++}`, subscriberData.value]);
    }
    this.pieChartOptions = this.createPieChartOptions(Object.values(DashboardChartColors), 'white');
  }

  private createBounceChart(): void {
    this.bounceChartData = [];
    for (const subscriberData of this.dashboardData.data.stats.subscriberBounces) {
      this.bounceChartData.push([subscriberData.text, subscriberData.value]);
    }
  }

  private createPieChartOptions(colors: string[], labelColor: string): GooglePieChartOptions {
    const options: GooglePieChartOptions = {} as GooglePieChartOptions;
    options.legend = { position: 'none' };
    options.fontSize = 12;
    options.height = 145;
    options.width = 145;
    options.colors = colors;
    options.chartArea = { left: 15, top: 15, width: 120, height: 120 };
    options.backgroundColor = { fill: 'transparent' };
    options.pieSliceTextStyle = {
      color: labelColor
    };
    return options;
  }

  private showPreviewDataMsg(): void {
    if (!this.dashboardData?.data?.displayOptions) {
      return;
    }

    if (
      this.dashboardData.data.displayOptions.subscriberActivityPreview ||
      this.dashboardData.data.displayOptions.subscriberISPsPreview ||
      this.dashboardData.data.displayOptions.subscriberBouncesPreview ||
      this.dashboardData.data.displayOptions.top5TagsPreview ||
      this.dashboardData.data.displayOptions.recentCampaignsPreview
    ) {
      const message = this.translateService.translate('Dashboard::previewDataHint::message::As you have no valid data for all dashboard items we show dummy data');
      this.ktMessageService.warning(message, true, false);
    }
  }

  private setChartPreviewStyles(): void {
    if (this.dashboardData.data.displayOptions.subscriberActivityPreview) {
      this.lineChartCss = 'dashboard-chart-disable';
    }
    if (this.dashboardData.data.displayOptions.subscriberISPsPreview || this.dashboardData.data.displayOptions.subscriberBouncesPreview) {
      this.pieChartCss = 'dashboard-chart-disable';
    }

    if (this.dashboardData.data.displayOptions.top5TagsPreview) {
      this.tagTableCss = 'dashboard-chart-disable';
    }
    if (this.dashboardData.data.displayOptions.recentCampaignsPreview) {
      this.campaignTableCss = 'dashboard-chart-disable';
    }
  }

  private redrawChart(): void {
    setTimeout(() => {
      if (google) {
        this.createCharts();
      }
    }, 1);
  }
}

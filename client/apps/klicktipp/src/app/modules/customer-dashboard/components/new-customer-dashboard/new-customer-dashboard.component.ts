import { Component, Input } from '@angular/core';
import { Dashboard } from './../../models/dashboard';
import { OnReadyBaseComponent } from './../../../forms.shared/components/on-ready/on-ready-base.component';
import { KtNotificationComponent, KtShadowDomComponent, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { DashboardService } from './../../services/dashboard.service';
import { ConfigService } from './../../../../services/config.service';
import { ChecklistsComponent } from '../checklists/checklists.component';

@Component({
    selector: 'kt-new-customer-dashboard',
    templateUrl: './new-customer-dashboard.component.html',
    imports: [ChecklistsComponent, KtShadowDomComponent, KtNotificationComponent, TranslatePipe]
})
export class NewCustomerDashboardComponent extends OnReadyBaseComponent {
  @Input() dashboardData: Dashboard;
  marketingSidebarExists: boolean;

  constructor(
    protected translateService: TranslateService,
    protected configService: ConfigService,
    private dashboardService: DashboardService
  ) {
    super(translateService, 'dashboard-marketing-sidebar', false);
  }

  shadowDomReady($event: boolean): void {
    if ($event) {
      this.dashboardService.sideboardAvailable.next($event);
    }
    this.marketingSidebarExists = $event;
    this.setReady(true);
  }
}

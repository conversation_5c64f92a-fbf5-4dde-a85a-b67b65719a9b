import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { faAngleDown } from '@fortawesome/free-solid-svg-icons';
import { AutocompleteItem, AutocompleteOption, KtAutocompleteComponent, TranslateService } from '@klicktipp/kt-mat-library';
import { Subscription } from 'rxjs';
import { ColDef } from '../../../forms.shared/models/table-columns-def.interface';

@Component({
    selector: 'kt-entity-select',
    templateUrl: './entity-select.component.html',
    imports: [ReactiveFormsModule, KtAutocompleteComponent]
})
export class EntitySelectComponent<T> implements OnInit, OnDestroy, AfterViewInit {
  private readonly subscriptions: Subscription = new Subscription();

  @ViewChild('searchinput') searchinputElement: ElementRef<HTMLInputElement>;

  @Input() data: T[];
  @Input() initEntity: T;
  @Input() columnDefs: ColDef[];

  @Output() readonly selectAction: EventEmitter<T> = new EventEmitter<T>();

  @Input() set invalid(isInvalid: boolean) {
    if (isInvalid) {
      this.getSearchInput()?.setErrors({ customError: '' });
    } else {
      this.getSearchInput()?.setErrors(null);
    }
  }

  faAngleDown = faAngleDown;
  form: FormGroup = new FormGroup({});

  entity: T;
  autocomplete: AutocompleteItem = {
    id: 'input-ac-entity-search',
    type: 'text',
    placeholder: this.translateService.translate('entityselect::searchinput::placeholder::Please select...'),
    floatLabel: 'never',
    displayFn: (opt: AutocompleteOption) => {
      return this.acDisplayFn(opt);
    },
    panelCssClasses: ['kt-autocomplete-panel--entity-select']
  };

  autocompleteOptions: AutocompleteOption[] = [];

  constructor(private translateService: TranslateService) {}

  ngOnInit(): void {
    this.entity = this.initEntity;
    this.autocompleteOptions = this.createAutoComplete();
  }

  ngAfterViewInit(): void {
    if (this.entity) {
      setTimeout(() => {
        this.setInputValue();
      });
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  onClickInsideEntitySelect(event: Event): void {
    event.stopPropagation();
  }

  onUnselect(): void {
    this.getSearchInput().setValue(null);
    this.entity = null;
    this.selectAction.emit(this.entity);
  }

  autocompleteClicked(clicked: AutocompleteOption): void {
    this.entity = this.data.find((d) => d['id'] === clicked.value);
    this.selectAction.emit(this.entity);
  }

  private getSearchInput(): FormControl {
    return this.form.get(this.autocomplete.id) as FormControl;
  }

  private createAutoComplete(): AutocompleteOption[] {
    const opts: AutocompleteOption[] = [];
    for (const datum of this.data) {
      opts.push({
        value: datum['id'],
        text: datum['name'],
        subtext: 'Id: ' + datum['id']
      });
    }
    return opts;
  }

  private setInputValue(): void {
    const opt = this.autocompleteOptions.find((o) => o.value === this.entity['id']);
    this.getSearchInput().setValue(opt);
  }

  private acDisplayFn(_opt: AutocompleteOption): string {
    return _opt ? `${_opt.value} - ${_opt.text}` : '';
  }
}

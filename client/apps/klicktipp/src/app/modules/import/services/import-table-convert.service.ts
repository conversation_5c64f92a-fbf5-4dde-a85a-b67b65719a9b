import { Injectable } from '@angular/core';
import {
  DashFilterService,
  KtComponentType,
  KtTableService,
  TableAlignTypes,
  TableBase,
  TableCellComponent,
  TableHeaderItem,
  TableRowData,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { ConvertTableBaseService } from './../../../services/convert-table.base.service';
import { Import } from './../models/entities/import.entity';
import { ImportStatus, ImportViews } from './../enums/import-status.enum';
import { faArrowRight, faCheck, faChevronRight, faExclamationTriangle, faEye, faTimes, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
import { RouteParameterService } from './../../../services/route-parameter.service';
import { KtStorageService } from './../../../services/kt-storage.service';
import { ImportTableDataService } from './import-table-data.service';
import { DomSanitizer } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root'
})
export class ImportTableConvertService extends ConvertTableBaseService {
  faEye = faEye;
  faChevronRight = faChevronRight;
  faArrowRight = faArrowRight;
  faTrashAlt = faTrashAlt;

  constructor(
    protected translateService: TranslateService,
    private routingService: RouteParameterService,
    ktStorageService: KtStorageService,
    tableService: KtTableService,
    protected dashPipe: DashFilterService,
    private importTableDataSerivce: ImportTableDataService,
    sanitizer: DomSanitizer
  ) {
    super(translateService, ktStorageService, tableService, dashPipe, sanitizer);
  }

  createOverviewTable(data: Import[]): TableBase {
    const rows: TableRowData[] = [];
    for (let i = 0; i < data?.length; i++) {
      const entity = data[i];
      const row: TableRowData = {
        id: 'import-overview-' + i,
        rowData: []
      };
      for (const [key, _value] of Object.entries(entity)) {
        const { rowEle } = this.createRow(entity, key);
        row.rowData.push(rowEle);
        if (key === 'name') {
          rowEle.link = `/import/${this.routingService.userId}/${entity.id}`;
        }
      }
      // ###### buttons ######
      const button: TableCellComponent = {
        text: this.translateService.translate('import::list::init::button::Details'),
        icon: { icon: this.faEye },
        attributes: { color: 'accent' },
        key: entity.status,
        componentType: KtComponentType.Button,
        id: 'details-button-' + i,
        column: 'operations'
      };

      switch (entity.status) {
        case ImportStatus.Upload:
          button.text = this.translateService.translate('import::list::upload::button::Details');
          break;
        case ImportStatus.Validating:
          button.text = this.translateService.translate('import::list::validating::button::Details');
          break;
        case ImportStatus.Validated:
          button.text = this.translateService.translate('import::list::validated::button::Details');
          button.icon = { icon: this.faChevronRight };
          break;
        case ImportStatus.Importing:
          button.text = this.translateService.translate('import::list::importing::button::Details');
          break;
        case ImportStatus.Done:
          button.text = this.translateService.translate('mport::list::done::button::Summary');
          button.attributes = { color: 'primary' };
          button.icon = { icon: this.faArrowRight };
          break;
        case ImportStatus.Expired:
          button.text = this.translateService.translate('import::list::expired::button::Details');
          break;
        case ImportStatus.Assign:
          button.text = this.translateService.translate('import::list::assign::button::Details');
      }
      row.rowData.push(button);
      row.rowData.push({
        text: this.translateService.translate('import::list::delete::button::Delete'),
        icon: { icon: this.faTrashAlt },
        key: 'delete',
        attributes: {
          color: 'warn'
        },
        componentType: KtComponentType.Button,
        id: 'delete-button-' + i,
        column: 'operations'
      });

      rows.push(row);
    }

    return {
      id: 'table-import-overview',
      headers: this.importTableDataSerivce.importListHeader,
      rows,
      defaultSortValue: { default: { name: 'id', direction: 'desc' } }
    };
  }

  createResultTable(data: { count: number; type: string }[], view: string): TableBase {
    const headers: TableHeaderItem[] = [];
    const rows: TableRowData[] = [];

    headers.push(
      this.createHeaderItem({
        header: this.translateService.translate('import::tabledenied::header::number of contacts'),
        key: 'count',
        disableSort: true,
        align: TableAlignTypes.Left
      })
    );
    let title = this.translateService.translate('import::tabledenied::header::validation');
    if (view === ImportViews.Report) {
      title = this.translateService.translate('import::tablereport::header::import');
    }
    headers.push(
      this.createHeaderItem({
        header: title,
        key: 'type',
        disableSort: true
      })
    );
    headers.push(
      this.createHeaderItem({
        header: this.translateService.translate('import::tabledenied::header::action'),
        key: 'action',
        disableSort: true
      })
    );
    for (let i = 0; i < data?.length; i++) {
      const entity = data[i];
      const row: TableRowData = {
        id: 'import-result-' + i,
        rowData: []
      };
      for (const [key, _value] of Object.entries(entity)) {
        if (key === 'type') {
          const { rowEle } = this.createValidateRow(entity, key);
          row.rowData.push(rowEle);
        } else {
          const { rowEle } = this.createRow(entity, key);
          row.rowData.push(rowEle);
        }
      }
      this.createResultStatus(row, entity, view);
      rows.push(row);
    }
    return {
      id: 'table-import-result',
      headers,
      rows,
      defaultSortValue: { default: { name: 'count', direction: 'desc' } }
    };
  }

  private createResultStatus(row: TableRowData, entity: { count: number; type: string }, view: string): void {
    switch (entity.type) {
      case 'error':
        row.rowData.push({
          column: 'action',
          componentType: KtComponentType.Icon,
          id: 'import-result-' + row.id + '-action-icon',
          icon: {
            cssClass: 'error',
            icon: faTimes,
            color: 'white'
          }
        });
        row.rowData.push({
          column: 'action',
          text: this.getTranslationTextFromError(view),
          icon: {
            cssClass: 'error',
            icon: faTimes,
            color: 'white'
          },
          componentType: KtComponentType.Text,
          id: 'import-result-' + row.id + '-action'
        });
        break;
      case 'warning':
        row.rowData.push({
          column: 'action',
          componentType: KtComponentType.Icon,
          id: 'import-result-' + row.id + '-action-icon',
          icon: {
            cssClass: 'warn',
            icon: faExclamationTriangle,
            color: 'white'
          }
        });
        row.rowData.push({
          column: 'action',
          text: this.getTranslationTextFromWarn(view),
          componentType: KtComponentType.Text,
          id: 'import-result-' + row.id + '-action'
        });
        break;
      case 'valid':
      case 'success':
        row.rowData.push({
          column: 'action',
          componentType: KtComponentType.Icon,
          id: 'import-result-' + row.id + '-action-icon',
          icon: {
            cssClass: 'success',
            icon: faCheck,
            color: 'white'
          }
        });
        row.rowData.push({
          componentType: KtComponentType.Text,
          id: 'import-result-' + row.id + '-action',
          column: 'action',
          text: this.getTranslationTextFromSuccess(view),
          icon: {
            cssClass: 'success',
            icon: faCheck,
            color: 'white'
          }
        });
        break;
    }
  }

  private createValidateRow(entity: unknown, propertyKey: string): { propertyName: string; rowEle: TableCellComponent } {
    const propertyName = this.nameof(entity, (e) => e[propertyKey]);
    let text = '';
    switch (entity[propertyKey]) {
      case 'error':
        text = this.translateService.translate('import::report::message::error::Error');
        break;
      case 'warning':
        text = this.translateService.translate('import::report::message::warning::Warning');
        break;
      case 'valid':
      case 'success':
        text = this.translateService.translate('import::report::message::success::Success');
        break;
    }
    const rowEle: TableCellComponent = {
      componentType: KtComponentType.Text,
      id: propertyName,
      column: propertyName,
      text
    };
    return { propertyName, rowEle };
  }

  private getTranslationTextFromError(view: string): string {
    switch (view) {
      case ImportViews.Denied:
        return this.translateService.translate('import::denied::icon::error::will be rejected');
      case ImportViews.Verify:
        return this.translateService.translate('import::verify::icon::error::will not be imported');
      case ImportViews.Report:
        return this.translateService.translate('import::report::icon::error::were not imported');
    }
  }

  private getTranslationTextFromWarn(view: string): string {
    switch (view) {
      case ImportViews.Verify:
        return this.translateService.translate('import::verify::icon::warning::will be imported, field values with errors will be skipped');
      case ImportViews.Report:
        return this.translateService.translate('import::report::icon::warning::imported with warnings');
    }
  }

  private getTranslationTextFromSuccess(view: string): string {
    switch (view) {
      case ImportViews.Verify:
        return this.translateService.translate('import::verify::icon::success::will be imported completely');
      case ImportViews.Report:
        return this.translateService.translate('import::report::icon::success::were imported completely');
    }
  }
}

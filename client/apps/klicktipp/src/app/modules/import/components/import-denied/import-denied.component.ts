import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { faCloudDownload, faCloudUpload, faCopy } from '@fortawesome/pro-regular-svg-icons';
import { KtMessageService, KtTableComponent, TableBase, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { Subscription } from 'rxjs';
import { filter, switchMap } from 'rxjs/operators';
import { AccountAccess } from '../../../../enums/account-access.enum';
import { ConfigService } from '../../../../services/config.service';
import { RouteParameterService } from '../../../../services/route-parameter.service';
import { ImportConfirmModalComponent } from '../../../forms.shared/components/import-confirm-modal/import-confirm-modal.component';
import { ImportWizardComponent } from '../../../forms.shared/components/import-wizard/import-wizard.component';
import { ImportViews } from '../../enums/import-status.enum';
import { ImportConfig } from '../../models/import-config.interface';
import { ImportTableConvertService } from '../../services/import-table-convert.service';
import { ImportService } from '../../services/import.service';

@Component({
    selector: 'kt-import-denied',
    templateUrl: './import-denied.component.html',
    imports: [ImportWizardComponent, KtTableComponent, TranslatePipe, FaIconComponent]
})
export class ImportDeniedComponent implements OnInit, OnDestroy {
  private readonly subscriptions = new Subscription();
  // prettier-ignore
  private readonly importDeniedErrorMessage = this.translateService.translate('import::denied::error::notification::Your import file contains more than 5% email addresses that have been detected as invalid. In such cases, KlickTipp rejects the entire import file for security reasons.');

  @Input() noDomainWarnStr: string;
  @Input() config: ImportConfig;

  faTrash = faTrash;
  faCloudUpload = faCloudUpload;
  faCloudDownload = faCloudDownload;
  faCopy = faCopy;
  count = 0;
  isSupport: boolean;
  hasSenderDomain: boolean;
  tableItem: TableBase;

  constructor(
    private translateService: TranslateService,
    private matDialog: MatDialog,
    private router: Router,
    private importService: ImportService,
    private configService: ConfigService,
    private ktMessageService: KtMessageService,
    private routingService: RouteParameterService,
    private importConvertTableService: ImportTableConvertService
  ) {
    this.isSupport = this.configService.hasAccess(AccountAccess.AdministerKlicktipp);
    this.hasSenderDomain = this.configService.hasAccess(AccountAccess.HasSenderDomain);
  }

  ngOnInit(): void {
    this.ktMessageService.hideBar();
    this.ktMessageService.error(`${this.importDeniedErrorMessage}`, true);

    const validationErrors = this.config.import?.stats?.validation?.errors;
    const valid = this.config.import?.stats?.validation?.valid;
    const warnings = this.config.import?.stats?.validation?.warnings;
    const rowData = [{ count: validationErrors, type: 'error' }];
    this.count = validationErrors + valid + warnings;
    this.tableItem = this.importConvertTableService.createResultTable(rowData, ImportViews.Denied);

    if (!this.hasSenderDomain) {
      this.ktMessageService.warning(this.noDomainWarnStr, true);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  uploadRevisedClicked(): void {
    const copyImportSubscription = this.importService.copyImport(this.config.import.id).subscribe((id) => {
      this.subscriptions.add(copyImportSubscription);
      const successCopyMessage = this.translateService.translate('import::copy::successmessage::Import copied successfully');
      this.ktMessageService.success(`${successCopyMessage}`);
      this.router.navigateByUrl(`/import/${this.routingService.userId}/${id}`).finally();
    });
  }

  onDeleteClick(): void {
    const header = this.translateService.translate('import::denied::delete::header::Delete import', { name: this.config.import.name });
    // prettier-ignore
    const message = this.translateService.translate('import::denied::delete::message::Are you sure you want to delete this import? The field mapping for your import file will be discarded. If you want to upload a corrected file in the same format, use the "Upload another file" button.');
    const icon = faTrash as IconProp;
    const button = this.translateService.translate('import::denied::delete::button::Delete import');

    const confirmDeleteSubscription = this.matDialog
      .open<ImportConfirmModalComponent, { header: string; message: string; icon: IconProp; button: string }, boolean>(ImportConfirmModalComponent, {
        data: { header, message, icon, button }
      })
      .afterClosed()
      .pipe(
        filter((confirmResult) => !!confirmResult),
        switchMap(() => this.importService.deleteImport(this.config.import.id))
      )
      .subscribe(() => {
        const successDeleteMessage = this.translateService.translate('import::delete::successmessage::Import @@name@@ deleted successfully', { name: this.config.import.name });
        this.ktMessageService.success(`${successDeleteMessage}`);
        this.router.navigateByUrl(`/import/${this.routingService.userId}`).finally();
      });
    this.subscriptions.add(confirmDeleteSubscription);
  }
}

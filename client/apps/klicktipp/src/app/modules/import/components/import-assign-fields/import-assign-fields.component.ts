import { NgClass } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatTable, MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faCheck, faChevronLeft, faUndo } from '@fortawesome/free-solid-svg-icons';
import { faBan, faTimes } from '@fortawesome/pro-regular-svg-icons';
import { DeferOnViewportNotificationComponent, DeferOnViewportSpinnerComponent, KtMessageService, TranslatePipe } from '@klicktipp/kt-mat-library';
import { Subscription } from 'rxjs';
import { AccountAccess } from '../../../../enums/account-access.enum';
import { ConfigService } from '../../../../services/config.service';
import { RouteParameterService } from '../../../../services/route-parameter.service';
import { ImportConfirmModalComponent } from '../../../forms.shared/components/import-confirm-modal/import-confirm-modal.component';
import { ImportWizardComponent } from '../../../forms.shared/components/import-wizard/import-wizard.component';
import { PopupComponent } from '../../../forms.shared/components/popup/popup.component';
import { DetectFormatResponse } from '../../models/api/kt-import/detect-format';
import { ValidateFieldRequest } from '../../models/api/kt-import/validate-field';
import { AssignField } from '../../models/assign-field.interface';
import { ImportRow } from '../../models/entities/import-row';
import { ImportSelectOption } from '../../models/entities/import-select-option';
import { FieldDefinition, Import, ImportField } from '../../models/entities/import.entity';
import { ImportAssignResult } from '../../models/import-assign-result.interface';
import { ImportConfig } from '../../models/import-config.interface';
import { ImportService } from '../../services/import.service';
import { ImportAssignTranslateService } from './services/import-assign-translate.service';
import { ImportOptionsService, OptionType } from './services/import-options.service';

@Component({
  selector: 'kt-import-assign-fields',
  templateUrl: './import-assign-fields.component.html',
  imports: [
    NgClass,
    ImportWizardComponent,
    FaIconComponent,
    MatTableModule,
    MatTooltipModule,
    ReactiveFormsModule,
    FormsModule,
    PopupComponent,
    TranslatePipe,
    DeferOnViewportSpinnerComponent,
    DeferOnViewportNotificationComponent
  ],
  providers: [ImportOptionsService, ImportAssignTranslateService]
})
export class ImportAssignFieldsComponent implements OnInit, OnDestroy {
  private readonly subscriptions = new Subscription();
  private readonly noSubOptions = ['Hash', 'SubscriberId', 'EmailAddress', 'PhoneNumber'];

  @Input() setHeight: string;
  @Input() deletePadding: string;
  @Input() visible = true;

  @Output() importResult = new EventEmitter<ImportAssignResult>();
  @Output() cancel = new EventEmitter<Import>();

  @ViewChild(MatTable) table: MatTable<ImportRow>;

  @Input() set config(value: ImportConfig) {
    this.importConfig = value;
    this.imp = value?.import;
    this.isValidateExternal.setValue(!this.imp.skipExternalValidation);
    if (this.visible) {
      this.setConfig(value);
    } else {
      this.setConfigExpired(value);
      this.isValidateExternal.disable();
    }
  }

  get config(): ImportConfig {
    return this.importConfig;
  }

  get tableLoadingNotification(): string {
    return this.translateService.tableLoadingNotification;
  }

  isValidateExternal = new FormControl(true);
  parserPreviewRowsAmount = 1000;

  columns: { columnDef; header }[] = [];

  dataSource: MatTableDataSource<ImportRow> = new MatTableDataSource([]);

  faCheck = faCheck as IconProp;
  faUndo = faUndo as IconProp;
  faChevronLeft = faChevronLeft as IconProp;
  faTimes = faTimes as IconProp;

  fields: AssignField[];
  imp: Import;

  headerMVChecked: string;
  messageMVChecked: string;
  isWhiteLabel: boolean;

  assignButtonDisabled = false;
  hasPrimaryId = false;

  displayedColumns: string[];

  private importConfig: ImportConfig;
  private fieldDefinitions: FieldDefinition[];
  private detectFormatResponse: DetectFormatResponse;
  private rows: ImportRow[] = [];

  constructor(
    private router: Router,
    private importService: ImportService,
    private translateService: ImportAssignTranslateService,
    private matDialog: MatDialog,
    private configService: ConfigService,
    private ktMessageService: KtMessageService,
    private routingService: RouteParameterService,
    private optionsService: ImportOptionsService
  ) {
    this.isWhiteLabel = this.configService.hasAccess(AccountAccess.SkipMillionVerifier);
    this.columns = [
      {
        columnDef: 'column',
        header: this.translateService.columCell
      },
      {
        columnDef: 'assign',
        header: this.translateService.assignCell
      },
      {
        columnDef: 'type',
        header: this.translateService.typeCell
      },
      {
        columnDef: 'valid',
        header: this.translateService.validCell
      },
      {
        columnDef: 'format',
        header: this.translateService.formatCell
      }
    ];
    this.displayedColumns = this.columns.map((c) => c.columnDef);
  }

  ngOnInit(): void {
    this.headerMVChecked = this.translateService.tooltipHeaderMV;
    this.messageMVChecked = this.translateService.tooltipMessageMV;

    const { detectFormatResponse } = this.config;
    if (detectFormatResponse && detectFormatResponse.name) {
      this.ktMessageService.info(
        `${this.translateService.importDetailsExportDetectInfoMessage} ${detectFormatResponse.name} ${this.translateService.importDetailsAdjustStepsInfoMessage}`
      );
    }
    this.detectFormatResponse = detectFormatResponse;

    this.rows = this.createRowData(this.fields);
    this.dataSource = new MatTableDataSource(this.rows);
    for (const row of this.rows) {
      row.selectedIdOption = row.idOptions[0];
      row.selectedSubOption = row.subOptions[0];
    }
    this.setInitialFormat();
  }

  assignButtonClicked(): void {
    this.imp.skipExternalValidation = this.isValidateExternal.value ? 0 : 1;
    const importAssignResult: ImportAssignResult = this.generateImportResult();
    if (this.hasPrimaryId) {
      this.importResult.emit(importAssignResult);
    } else {
      this.openConfirmationInfoModal(
        importAssignResult,
        this.translateService.warningMissingIdentifierHeader,
        this.translateService.warningMissingIdentifierMessage,
        this.translateService.warningMissingIdentifierButton,
        this.translateService.warningMissingIdentifierButtonOverview,
        'info'
      );
    }
  }

  generateImportResult(): ImportAssignResult {
    let noEmailAddress: 0 | 1 = 1;
    const importFields: ImportField[] = this.rows
      .map((r) => {
        const id: string = r.selectedSubOption?.id;
        const header: string = r.column;
        const column: number = r.index;
        const type: string = r.selectedIdOption?.id;
        const format: string = r.selectedFormatOption?.id;
        const sampleVal: string = r.sampleValues[0];

        if (this.noSubOptions.indexOf(id) > -1 && !this.hasPrimaryId) {
          this.hasPrimaryId = true;
        }

        if (id === 'EmailAddress') {
          noEmailAddress = 0;
        }
        return { id, header, column, type, format, sampleVal };
      })
      // only send selected fields to backend
      .filter((f) => f.type !== 'ignore' && f.id !== undefined);
    const session = !!this.imp?.session && this.imp?.session?.length > 0 ? this.imp.session : this.importService.getUuid();
    return {
      entity: { ...this.imp, importFields, session, noSubscriptionImport: noEmailAddress },
      config: { ...this.config }
    };
  }

  fileSelectButtonClicked(): void {
    this.openFileSelectionWarningModal();
  }

  cancelButtonClicked(): void {
    this.openCancelWarningModal();
  }

  openConfirmationInfoModal(importAssignResult: ImportAssignResult, header: string, message: string, button: string, buttonOverview: string, type?: string): void {
    const icon = faCheck as IconProp;
    const modalType = type;

    const sub = this.matDialog
      .open<
        ImportConfirmModalComponent,
        {
          header: string;
          message: string;
          icon: IconProp;
          button: string;
          buttonOverview: string;
          modalType: string;
        },
        boolean
      >(ImportConfirmModalComponent, {
        data: { header, message, icon, button, buttonOverview, modalType }
      })
      .afterClosed()
      .subscribe((confirmResult) => {
        if (confirmResult) {
          this.importResult.emit(importAssignResult);
        }
      });

    this.subscriptions.add(sub);
  }

  openFileSelectionWarningModal(): void {
    this.openWarningModalToFileSelection(
      this.translateService.warningBackToModalHeader,
      this.translateService.warningBackToModalMessage,
      this.translateService.warningBackToModalButton
    );
  }

  openCancelWarningModal(): void {
    this.openWarningModal(this.translateService.warningModalHeader, this.translateService.warningModalMessage, this.translateService.warningModalButton);
  }

  openErrorAssignModal(): void {
    this.openErrorModal(
      this.translateService.errorModalHeader,
      this.translateService.errorModalMessage,
      this.translateService.errorModalButton,
      this.translateService.errorModalButtonOverview
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  selectChangeEvent(_newEvent: ImportSelectOption, row: ImportRow): void {
    this.setRowSelects(row);
  }

  selectChangeAssignEvent(_newEvent: ImportSelectOption, row: ImportRow): void {
    this.setRowAssignSelect(row);
  }

  selectChangeFormatEvent(_newEvent: ImportSelectOption, row: ImportRow): void {
    row.resetWarn();
    this.checkErrors();
  }

  resetAll(): void {
    for (const row of this.rows) {
      row.selectedIdOption = row.idOptions.find((o) => o.id === OptionType.Ignore);
      this.setRowAssignSelect(
        row,
        row.subOptions.find((o) => o.type === OptionType.Ignore)
      );
    }
    this.checkErrors();
  }

  private setConfig(value: ImportConfig): void {
    if (!value.header || !value.import || !value.data) {
      this.openErrorAssignModal();
    } else {
      this.fieldDefinitions = value.import.fieldDefinitions;
      const h2check = value?.detectFormatResponse?.header2 === 1;
      const samplesSrc = value.data.filter((d, index) => !!d && (!h2check || index > 0));
      this.fields = this.getFields(value, h2check, samplesSrc);
    }
  }

  private getFields(value: ImportConfig, h2check: boolean, samplesSrc: unknown[]): AssignField[] {
    return value.header.map((h, columnIndex) => {
      const column = h;
      const importField = value.import.importFields.find((i) => i.column === columnIndex);
      const fieldDefinition = value.import.fieldDefinitions.find((f) => f.id === importField?.id);
      let sampleValue = value.data.map((d) => d[h]).find((d, index) => !!d && (!h2check || index > 0)) ?? this.translateService.noExampleValueFound;
      sampleValue =
        sampleValue === this.translateService.noExampleValueFound && value.data.length >= this.parserPreviewRowsAmount
          ? this.translateService.noExampleValueFoundLargeFile
          : sampleValue;
      const samples: string[] = samplesSrc
        .map((d) => d[h])
        .filter((d) => d.length > 0)
        .slice(0, 100);
      const typeDef = ['global', 'custom'].includes(importField?.type) ? 'field' : importField?.type;
      const type = !importField?.type ? 'ignore' : typeDef;
      const valid = importField?.id;
      const formatOptionKeys = Object.keys(fieldDefinition?.formatOptions ?? {});
      const formatOptions = formatOptionKeys.map((key) => ({ value: key, label: fieldDefinition?.formatOptions[key] }));
      const format =
        (
          formatOptions.find((f) => f.value === importField?.format) ?? {
            label: 'none',
            value: null
          }
        )?.value ?? null;

      return {
        assign: sampleValue,
        tooltip: sampleValue,
        type,
        column,
        samples,
        valid,
        format,
        fieldColumn: importField?.column,
        formatOptions,
        fieldDefinition
      } as AssignField;
    });
  }

  private openWarningModal(header: string, message: string, button: string): void {
    const icon = faBan as IconProp;
    const sub = this.matDialog
      .open<
        ImportConfirmModalComponent,
        {
          header: string;
          message: string;
          icon: IconProp;
          button: string;
        },
        boolean
      >(ImportConfirmModalComponent, {
        data: { header, message, icon, button }
      })
      .afterClosed()
      .subscribe((confirmResult) => {
        if (confirmResult) {
          this.cancel.emit(this.config.import);
          this.router.navigateByUrl(`/import/${this.routingService.userId}`).finally();
        }
      });
    this.subscriptions.add(sub);
  }

  private openWarningModalToFileSelection(header: string, message: string, button: string): void {
    const icon = faBan as IconProp;
    const sub = this.matDialog
      .open<
        ImportConfirmModalComponent,
        {
          header: string;
          message: string;
          icon: IconProp;
          button: string;
        },
        boolean
      >(ImportConfirmModalComponent, {
        data: { header, message, icon, button }
      })
      .afterClosed()
      .subscribe((confirmResult) => {
        if (confirmResult) {
          this.cancel.emit(this.config.import);
        }
      });
    this.subscriptions.add(sub);
  }

  private openErrorModal(header: string, message: string, button: string, buttonOverview: string): void {
    const icon = faBan as IconProp;
    const disableClose = true;
    const isErrorModal = true;
    const hideCloseButton = true;

    const sub = this.matDialog
      .open<
        ImportConfirmModalComponent,
        {
          header: string;
          message: string;
          icon: IconProp;
          button: string;
          buttonOverview: string;
          disableClose: boolean;
          isErrorModal: boolean;
          hideCloseButton: boolean;
        },
        boolean
      >(ImportConfirmModalComponent, {
        data: { header, message, icon, button, buttonOverview, disableClose, isErrorModal, hideCloseButton }
      })
      .afterClosed()
      .subscribe((confirmResult) => {
        if (confirmResult) {
          this.cancel.emit(this.config.import);
        } else {
          this.cancel.emit(this.config.import);
          this.router.navigateByUrl(`/import/${this.routingService.userId}`).finally();
        }
      });
    this.subscriptions.add(sub);
  }

  private createRowData(fields: AssignField[]): ImportRow[] {
    if (!fields) {
      return [];
    }
    const rows: ImportRow[] = [];
    let index = 0;
    for (const field of fields) {
      const row = new ImportRow();
      row.index = index++;
      // fallback if something goes wrong with header => should be handled in papaparse service
      row.column = field.column ? field.column : this.translateService.translate('import::tableassign::header::column @@index@@ - no header found', { index });
      row.assign = field.assign;
      row.tooltip = field.tooltip;
      row.subOptions = this.optionsService.createSelectSubOptions(this.importConfig);
      const createTag = row.subOptions.filter((s) => s.type === OptionType.Tag).length > 0;
      row.idOptions = this.optionsService.createSelectIdOptions(createTag);
      row.selectedIdOption = row.idOptions[0];
      row.selectedSubOption = row.subOptions[0];
      this.optionsService.setFormatOptions(row);
      row.sampleValues = field.samples;
      rows.push(row);
    }
    return rows;
  }

  private setRowSelects(currentRow: ImportRow, opt: ImportSelectOption = null, validate = true): void {
    currentRow.subDisabled = false;
    if (!opt) {
      if (currentRow.selectedIdOption.id !== OptionType.Ignore) {
        if (currentRow.selectedIdOption.id === OptionType.TagCreate) {
          currentRow.subDisabled = true;
          opt = currentRow.subOptions.find((o) => o.type === OptionType.TagCreate);
        } else if (currentRow.selectedIdOption.id === OptionType.Tag && currentRow.subOptions.filter((o) => o.type === OptionType.Tag).length < 2) {
          currentRow.subDisabled = true;
          opt = currentRow.subOptions.find((o) => o.type === OptionType.Tag);
        } else {
          opt = currentRow.subOptions[0];
        }
      } else {
        currentRow.selectedSubOption = null;
        currentRow.selectedFormatOption = null;
        currentRow.formatOptions = [];
      }
    }
    if (opt) {
      this.setRowAssignSelect(currentRow, opt, validate);
    } else {
      currentRow.disableSubAndFormat(this.visible);
      this.checkErrors();
    }
  }

  private setRowAssignSelect(currentRow: ImportRow, opt: ImportSelectOption = null, validate = true): void {
    if (opt) {
      const newOpt = currentRow.subOptions.find((e) => e.id === opt.id);
      currentRow.selectedSubOption = newOpt ? newOpt : currentRow.subOptions[0];
    }
    this.optionsService.setFormatOptions(currentRow);
    currentRow.selectedFormatOption = currentRow.formatOptions[0];
    currentRow.disableSubAndFormat(this.visible);
    if (validate && this.visible) {
      const request = this.createValidateRequest(currentRow);
      if (request.field && request.values?.length > 0) {
        this.validateField(request, currentRow);
      }
    }
  }

  private validateField(request: ValidateFieldRequest, currentRow: ImportRow): void {
    this.importService.validateField(request).subscribe((validateResponse) => {
      switch (validateResponse.status) {
        case 'error':
          currentRow.showError = true;
          currentRow.errorMsg = validateResponse.message;
          break;
        case 'warning':
          currentRow.showWarning = true;
          currentRow.warningMsg = validateResponse.message;
          break;
        default:
          currentRow.resetErrors();
          break;
      }
      this.checkErrors();
    });
  }

  private createValidateRequest(currentRow: ImportRow): ValidateFieldRequest {
    const field = this.fieldDefinitions.find((f) => f.id === currentRow.selectedSubOption.id);
    return { field, values: currentRow.sampleValues };
  }

  private setInitialFormat(): void {
    let importFields = this.importConfig?.import?.importFields;
    if (!importFields) {
      importFields = this.detectFormatResponse?.importFields;
    }
    if (importFields) {
      this.setInitialRowValues(importFields);
    }
    this.handleNotSetValues();
    this.checkErrors();
  }

  private setInitialRowValues(importFields: ImportField[]): void {
    for (const importField of importFields) {
      const typeDef = this.optionsService.getIdType(importField.type);
      const row = this.rows.find((r) => r.column === importField.header);
      if (row && typeDef) {
        if (!row.sampleValues || row.sampleValues.length === 0) {
          row.selectedIdOption = row.idOptions.find((o) => o.id === OptionType.Ignore);
          continue;
        }
        row.selectedIdOption = row.idOptions.find((s) => s.id === typeDef);
        this.setRowAssignSelect(
          row,
          row.subOptions.find((s) => s.id === importField.id),
          false
        );
        if (importField.format) {
          row.selectedFormatOption = row.formatOptions.find((f) => f.id === importField.format);
        }
      }
    }
  }

  private handleNotSetValues(): void {
    for (const row of this.rows) {
      // set default values for ignore (not sent in importfields)
      if (row.selectedIdOption.id === OptionType.Ignore) {
        let opt = row.subOptions.find((s) => s.id === row.selectedIdOption.id);
        if (!opt || opt.type === OptionType.Ignore) {
          // set custom fields if column header is the same as custom field label
          opt = this.checkCustomFieldsHeader(row, opt);
        }
        this.setRowAssignSelect(row, opt, false);
      }
      row.disableSubAndFormat(this.visible);
      const request = this.createValidateRequest(row);
      if (request.field && this.visible) {
        this.validateField(request, row);
      }
    }
  }

  private checkCustomFieldsHeader(row: ImportRow, opt: ImportSelectOption): ImportSelectOption {
    const newOpt = row.subOptions.find((s) => s.label && s.label.toLowerCase() === row.column.toLowerCase());
    if (newOpt && row.sampleValues && row.sampleValues.length > 0) {
      // do only assign first one
      row.selectedIdOption = row.idOptions.find((o) => o.id === newOpt.type);
      // assign once directly for duplicate check
      row.selectedSubOption = newOpt;
      if (!this.checkDuplicates(row)) {
        opt = newOpt;
      } else {
        row.selectedIdOption = row.idOptions.find((o) => o.id === OptionType.Ignore);
      }
    }
    return opt;
  }

  private checkDuplicates(row: ImportRow): boolean {
    if (!row) {
      return false;
    }

    if (row.selectedIdOption?.id !== OptionType.Sub && row.selectedIdOption?.id !== OptionType.Custom) {
      // Note: only subscription and custom fields cannot be multiple assigned
      return false;
    }
    return this.rows.filter((r) => r.selectedSubOption && r.selectedSubOption.id === row.selectedSubOption.id && r.selectedSubOption.id !== OptionType.Default).length > 1;
  }

  private checkErrors(): void {
    this.assignButtonDisabled = false;
    let nothingSelected = true;
    let rowsWithErrors = 0;
    let assignedRows = 0;

    for (const row of this.rows) {
      row.showDuplicateError = this.checkDuplicates(row);
      row.duplicateMsg = this.translateService.duplicateSelectionMessage;

      if (row.showDuplicateError || row.selectedFormatOption?.id === OptionType.Default || row.selectedSubOption?.id === OptionType.Default) {
        this.assignButtonDisabled = true;
      }

      if (row.selectedSubOption?.id === OptionType.Default) {
        row.showWarning = true;
        row.warningMsg = this.translateService.selectValueWarn;
      }

      if (row.selectedFormatOption?.id === OptionType.Default) {
        row.showWarning2 = true;
        row.warningMsg = this.translateService.selectValueWarn;
      }

      if (nothingSelected && row.selectedIdOption.id !== OptionType.Ignore) {
        nothingSelected = false;
      }

      if (row.selectedIdOption.id === OptionType.TagCreate && row.selectedFormatOption?.id === OptionType.Default) {
        row.showWarning = false;
        row.showWarning2 = false;
        row.showError = true;
        row.errorMsg = this.translateService.errorTagCreatNoValue;
      }

      if (row.formatOptions.length > 0) {
        assignedRows += 1;
      }

      if (row.showError) {
        rowsWithErrors += 1;
      }
    }

    if (nothingSelected) {
      this.assignButtonDisabled = true;
    }

    if (assignedRows - rowsWithErrors <= 0) {
      this.assignButtonDisabled = true;
    }
  }

  private setConfigExpired(value: ImportConfig): void {
    this.fieldDefinitions = value.import.fieldDefinitions;
    this.fields = value.import.importFields.map((importField) => {
      if (!importField) {
        return null;
      }
      const column = importField.header;
      const fieldDefinition = value.import.fieldDefinitions.find((f) => f.id === importField?.id);
      const typeDef = ['global', 'custom'].includes(importField.type) ? 'field' : importField?.type;
      const type = !importField?.type ? 'ignore' : typeDef;
      const valid = importField?.id;
      const sample = importField.sampleVal ? importField.sampleVal : this.translateService.translate('import::tableassign::assign::No sample value found');
      const samples = [sample];
      const formatOptionKeys = Object.keys(fieldDefinition?.formatOptions ?? {});
      const formatOptions = formatOptionKeys.map((key) => ({ value: key, label: fieldDefinition?.formatOptions[key] }));
      const format =
        (
          formatOptions.find((f) => f.value === importField?.format) ?? {
            label: 'none',
            value: null
          }
        )?.value ?? null;

      return {
        assign: sample,
        type,
        column,
        valid,
        format,
        fieldColumn: importField?.column,
        formatOptions,
        samples,
        fieldDefinition
      } as AssignField;
    });
    this.fields = this.fields.filter((f) => f);
  }
}

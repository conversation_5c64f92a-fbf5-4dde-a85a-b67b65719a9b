import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { Router } from '@angular/router';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faClipboardCheck, faSpinner, faTimes } from '@fortawesome/pro-regular-svg-icons';
import {
  FileDropErrorCodes,
  InputFileItem,
  InputItem,
  KtInputComponent,
  KtInputFileComponent,
  KtMessageService,
  TableBase,
  TranslatePipe,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { combineLatest, of, Subscription } from 'rxjs';
import { catchError, map, startWith, switchMap, tap } from 'rxjs/operators';
import { AccountAccess } from '../../../../enums/account-access.enum';
import { ErrorItems } from '../../../../models/error/custom-error';
import { ConfigService } from '../../../../services/config.service';
import { PapaParseService } from '../../../../services/papa-parse.service';
import { RouteParameterService } from '../../../../services/route-parameter.service';
import { SentryService } from '../../../../services/sentry.service';
import { ImportWizardComponent } from '../../../forms.shared/components/import-wizard/import-wizard.component';
import { FileSizeValidator } from '../../../forms.shared/form-validation/file-input-filesize.validator';
import { FileRequiredValidator } from '../../../forms.shared/form-validation/file-input-required.validator';
import { ImportErrors } from '../../general/importConstants';
import { Import } from '../../models/entities/import.entity';
import { ImportInitResult } from '../../models/import-init-result.interface';
import { ParseResults } from '../../models/papa-parse-results.interface';
import { ImportService } from '../../services/import.service';

@Component({
  selector: 'kt-import-init',
  templateUrl: './import-init.component.html',
  imports: [ImportWizardComponent, ReactiveFormsModule, TranslatePipe, MatTableModule, KtInputComponent, MatPaginatorModule, FaIconComponent, KtInputFileComponent]
})
export class ImportInitComponent implements OnInit, OnDestroy, AfterViewInit {
  private readonly subscriptions = new Subscription();
  // prettier-ignore
  private readonly importDetailsSingleColumnWarningMessage = this.translateService.translate('import::singlecolumn::notification::KlickTipp recognizes only one column based on your selection of separators and text enclosers. Please proceed only if you have uploaded a file that contains data in only one column. Otherwise, check your selection in the "Text encloser" and "Separator" fields.');

  @Input() noDomainWarnStr: string;
  @Output() importResult = new EventEmitter<ImportInitResult>();
  @ViewChild(MatPaginator) paginator: MatPaginator;

  @Input() set import(value: Import) {
    this.internalImport = value;
  }

  get import(): Import {
    return this.internalImport;
  }

  @Input() set formatErrors(errors: ErrorItems) {
    this.internalFormatErrors = errors ? errors : null;
    this.isSubmittingNext = false;
    this.handleFormErrors();
  }

  get formatErrors(): ErrorItems {
    return this.internalFormatErrors;
  }

  get nameInput(): AbstractControl {
    return this.importFormgroup.get('name');
  }

  get fileInput(): AbstractControl {
    return this.importFormgroup.get('file');
  }

  get encloserInput(): AbstractControl {
    return this.importFormgroup.get('encloser');
  }

  get separatorInput(): AbstractControl {
    return this.importFormgroup.get('separator');
  }

  get headerInput(): AbstractControl {
    return this.importFormgroup.get('checked');
  }

  faTimes = faTimes as IconProp;
  faClipboardCheck = faClipboardCheck as IconProp;
  faSpinner = faSpinner as IconProp;
  parseError = false;

  name = new FormControl('', [Validators.required]);
  file = new FormControl(null, [FileRequiredValidator.validate, FileSizeValidator.validate]);
  separator = new FormControl('');
  encloser = new FormControl('');
  checked = new FormControl(true);

  csvSeparators = [
    { labelKey: this.translateService.translate('import::csvseparator::label::Automatisch ermitteln'), value: '' },
    { labelKey: this.translateService.translate('import::csvseparator::label::Semikolon'), value: ';' },
    { labelKey: this.translateService.translate('import::csvseparator::label::Komma'), value: ',' },
    { labelKey: this.translateService.translate('import::csvseparator::label::Tab'), value: '\t' },
    { labelKey: this.translateService.translate('import::csvseparator::label::Space'), value: ' ' }
  ];

  csvEnclosers = [
    { labelKey: this.translateService.translate('import::csvencloser::label::Automatisch ermitteln'), value: '' },
    { labelKey: this.translateService.translate('import::csvencloser::label::Double quote'), value: '"' },
    { labelKey: this.translateService.translate('import::csvencloser::label::Single quote'), value: "'" }
  ];

  nameInputField: InputItem = {
    type: 'text',
    labelText: this.translateService.translate('import::details::label::Name of the import process'),
    placeholder: this.translateService.translate('import::details::inputplaceholdername::Name of the import process'),
    required: true,
    id: 'name'
  };

  fileInputItem: InputFileItem = {
    id: 'file',
    acceptedFormat:
      'text/x-csv,text/plain,application/csv,application/x-csv,' +
      'text/csv,text/comma-separated-values,' +
      'text/x-comma-separated-values,' +
      'text/tab-separated-values,application/vnd.ms-excel',
    btnText: this.translateService.translate('import::details::uploadbutton::choosefile::Choose file'),
    labelFileUpload: this.translateService.translate('import::details::label::CSV file')
  };
  importFormgroup = new FormGroup({
    name: this.name,
    file: this.file,
    separator: this.separator,
    encloser: this.encloser,
    checked: this.checked
  });
  disableAssignfieldsButton = true;
  isSubmittingNext = false;
  errorMsg = '';
  errorMsg2 = '';
  encodingErrorMessage = this.translateService.translate('import::file::error::encoding::Your file has the unknown character [?]. Make sure to encode it correctly as UTF8');
  hasEncodingError = false;

  hasSenderDomain: boolean;
  useWhiteLabelDomain: boolean;
  previewTableItem: TableBase;
  displayedColumns: string[] = [];
  dataSource: MatTableDataSource<[]>;

  private internalImport: Import;
  private internalFormatErrors: ErrorItems;
  private initResult: ImportInitResult = {} as ImportInitResult;

  constructor(
    private translateService: TranslateService,
    private importService: ImportService,
    private papaParseService: PapaParseService,
    private router: Router,
    private configService: ConfigService,
    private ktMessageService: KtMessageService,
    private routingService: RouteParameterService,
    private sentryService: SentryService
  ) {
    this.hasSenderDomain = this.configService.hasAccess(AccountAccess.HasSenderDomain);
    this.useWhiteLabelDomain = this.configService.hasAccess(AccountAccess.UseWhiteLabelDomain);
  }

  ngAfterViewInit(): void {
    this.isSubmittingNext = false;
    const encloserChange$ = this.encloserInput.valueChanges.pipe(startWith(''));
    const separatorChanges$ = this.separatorInput.valueChanges.pipe(startWith(''));
    const headerChange$ = this.headerInput.valueChanges.pipe(startWith(true));
    this.nameInput.valueChanges.subscribe(() => this.checkDisableButton());

    if (!this.hasSenderDomain) {
      if (this.useWhiteLabelDomain) {
        this.ktMessageService.warning(this.translateService.translate('import::warning::No White Label Domain available'), true);
      } else {
        this.ktMessageService.warning(this.noDomainWarnStr, true);
      }
      this.importFormgroup.disable();
    }

    const fileChange$ = this.fileInput.valueChanges.pipe(
      startWith(null),
      map((fileList: FileList) => {
        this.previewTableItem = null;
        return !!fileList && fileList.length > 0 ? fileList[0] : null;
      })
    );
    const sub = combineLatest([fileChange$, separatorChanges$, encloserChange$, headerChange$])
      .pipe(
        map(([file, separator, encloser, header]) => ({
          file,
          separator: separator === '' ? undefined : separator,
          encloser: encloser === '' ? undefined : encloser,
          header
        })),
        tap(() => {
          this.parseError = false;
        }),
        switchMap(({ file, separator, encloser, header }) =>
          file
            ? this.papaParseService.parseFilePreview(file, separator, encloser, header).pipe(
                map((result) => ({
                  ...result,
                  file,
                  separator,
                  encloser,
                  header
                }))
              )
            : of({
                file,
                separator,
                encloser,
                header,
                data: [],
                errors: [],
                meta: { fields: [] as string[] }
              } as ParseResults)
        ),
        map(({ data, meta, ...rest }) => {
          if (!meta.fields || !Array.isArray(meta.fields) || meta.fields.length < 1) {
            // no header variant - add generic header and transform data
            const rawData = data as string[][];
            const maxColumns = Math.max(...rawData.map((e: string[]) => e.length));
            const label = this.translateService.translate('import::genericheader::label::Column');
            const fields = Array.from({ length: maxColumns }, (v, i) => i + 1).map((i) => `${label} ${i}`);
            const transformedData = rawData.map((e: string[]) => {
              const o = {};
              e.forEach((v, i) => (o[fields[i]] = v));
              return o;
            });
            return { data: transformedData, meta: { ...meta, fields }, ...rest };
          }

          return { data, meta, ...rest };
        }),
        map(({ file, data, errors, meta, ...rest }) => {
          const header1: string[] = meta.fields;
          const columnDefs = header1.map((field) => ({ field }));
          let header2: string[];
          if (data && Array.isArray(data) && data.length > 0) {
            header2 = header1.map((h) => data[0][h]);
          }
          return { ...rest, file, data, errors, meta, header1, header2, columnDefs };
        }),
        switchMap(({ header1, header2, file, ...rest }) =>
          file
            ? this.importService.detectFormat(header1, header2).pipe(
                map((detecFormatResponse) => ({
                  ...rest,
                  file,
                  header1,
                  header2,
                  detecFormatResponse,
                  rest
                }))
              )
            : of({ ...rest, file, header1, header2, detecFormatResponse: undefined, rest })
        ),
        catchError((err, caught) => caught)
      )
      .subscribe(({ file, data, header1, header2, columnDefs, detecFormatResponse, separator, encloser, header, rest }) => {
        this.setParseError(rest);
        if (!!header && Array.isArray(header) && header.length === 1) {
          this.ktMessageService.warning(this.importDetailsSingleColumnWarningMessage);
        }

        if (columnDefs && Array.isArray(columnDefs) && columnDefs.length > 0 && data && Array.isArray(data) && !this.parseError) {
          this.displayedColumns = columnDefs.map((c) => c['field']);
          this.dataSource = new MatTableDataSource(data as []);
          this.dataSource.paginator = this.paginator;
        }

        this.initResult.file = file;

        if (separator) {
          // Note: we only need to know this information to pre-select the dropdown
          //       and only when the user actively sets it
          this.initResult.delimiter = separator;
        }

        if (encloser) {
          // Note: we only need to know this information to pre-select the dropdown
          //       and only when the user actively sets it
          this.initResult.encloser = encloser;
        }

        this.initResult.filename = file?.name;
        this.initResult.formatIfDetected = detecFormatResponse?.name;
        this.initResult.detectFormatResponse = detecFormatResponse;
        this.initResult.header = header;
        this.initResult.hash = '';
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        this.initResult.data = data as any;
        this.initResult.headerValues = header1;
        this.initResult.headerValues2 = header2;
        if (this.import?.id) {
          this.initResult.id = this.import.id;
        }

        this.checkDisableButton();
      });
    this.nameInput.setValue(this.import?.name ?? '');
    this.separatorInput.setValue(this.import?.delimiter ?? '');
    this.encloserInput.setValue(this.import?.enclosure ?? '');
    this.headerInput.setValue(this.import?.header ? this.import.header === 1 : true);
    this.subscriptions.add(sub);
  }

  ngOnInit(): void {
    if (!this.hasSenderDomain) {
      this.ktMessageService.warning(this.noDomainWarnStr, true);
      this.importFormgroup.disable();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  cancelClicked(): void {
    this.router.navigateByUrl(`/import/${this.routingService.userId}`).finally();
  }

  invalidFileDrop(errorType: FileDropErrorCodes): void {
    this.parseError = true;
    switch (errorType) {
      case FileDropErrorCodes.OnlyOneFileAllowed:
        this.errorMsg = this.translateService.translate('import::filedrop::error::Only one file allowed');
        return;
      case FileDropErrorCodes.WrongType:
        this.errorMsg = this.translateService.translate('import::filedrop::error::Wrong file type');
        return;
      default:
        return;
    }
  }

  assignFieldsClicked(): void {
    this.initResult.name = this.nameInput?.value;
    if (this.importFormgroup.invalid) {
      return;
    }
    this.isSubmittingNext = true;
    this.importResult.emit(this.initResult);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private setParseError(result: any): void {
    this.hasEncodingError = false;
    this.checkForEncodingErrors(result.data);

    const errors = result.errors;
    this.parseError = errors.length > 0;
    this.errorMsg = '';
    this.errorMsg2 = '';
    if (this.parseError) {
      switch (errors[0].type) {
        case 'Quotes':
          this.errorMsg = `${this.translateService.translate('import::fileparser::error::Error in row: @@row@@', { row: errors[0].row })} - `;
          this.errorMsg += `${this.translateService.translate('import::fileparser::error::Check quotes')}`;
          break;
        case 'Delimiter':
          // if only one column is in file, the auto-detect delimiter fails and papaparse throws an error, but it is none
          if (result.data[0] && Object.keys(result.data[0]).length === 1) {
            this.parseError = false;
            return;
          }
          this.errorMsg += `${this.translateService.translate('import::fileparser::error::Could not auto-detect delimiter')}`;
          break;
        case 'FieldMismatch':
          this.errorMsg = `${this.translateService.translate('import::fileparser::error::Error in row: @@row@@', { row: errors[0].row })} - `;
          this.errorMsg += `${this.translateService.translate('import::fileparser::error::Check number of columns')}`;
          break;
        default:
          this.errorMsg += `${this.translateService.translate('import::fileparser::error::Invalid file - please check format')}`;
          break;
      }
      if (errors[1]) {
        this.errorMsg2 = `${this.translateService.translate('import::fileparser::error::Additional errors in other rows found')}`;
      }
    }
  }

  private handleFormErrors(): void {
    if (!this.internalFormatErrors) {
      return;
    }
    for (const errorItem of this.internalFormatErrors.errors) {
      switch (errorItem.code) {
        case ImportErrors.NoName:
        case ImportErrors.DuplicateName:
          this.nameInput.setErrors({ customError: errorItem.message });
          break;
        case ImportErrors.FileRequired:
          this.fileInput.setErrors({ customError: errorItem.message });
          break;
        default:
          break;
      }
    }
  }

  private checkForEncodingErrors(data: unknown[]): void {
    for (const row of data) {
      const strings: string[] = Object.values(row);
      const matches = strings.find((value) => /\uFFFD/.test(value));
      if (matches) {
        this.hasEncodingError = true;
        return;
      }
    }
  }

  private checkDisableButton(): void {
    this.disableAssignfieldsButton = !this.nameInput?.value || !this.initResult.file || this.parseError || this.hasEncodingError;
  }
}

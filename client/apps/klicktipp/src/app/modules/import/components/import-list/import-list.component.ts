import { AfterViewInit, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faPlus, faTrash } from '@fortawesome/free-solid-svg-icons';
import {
  KtMessageComponent,
  KtMessageService,
  KtNotificationComponent,
  KtStaticMessageComponent,
  KtTableComponent,
  NotificationType,
  StaticMessageItem,
  TableBase,
  TableButtonClicked,
  TranslatePipe,
  TranslateService
} from '@klicktipp/kt-mat-library';
import { Subject, Subscription } from 'rxjs';
import { filter, map, switchMap } from 'rxjs/operators';
import { AccountAccess } from '../../../../enums/account-access.enum';
import { ConfigService } from '../../../../services/config.service';
import { RouteParameterService } from '../../../../services/route-parameter.service';
import { TabTitleService } from '../../../../services/tab-title.service';
import { isTranslateKey } from '../../../../static/translate';
import { ImportConfirmModalComponent } from '../../../forms.shared/components/import-confirm-modal/import-confirm-modal.component';
import { ImportConsentModalComponent } from '../../../forms.shared/components/import-consent-modal/import-consent-modal.component';
import { OnReadyBaseComponent } from '../../../forms.shared/components/on-ready/on-ready-base.component';
import { Import } from '../../models/entities/import.entity';
import { ImportTableConvertService } from '../../services/import-table-convert.service';
import { ImportService } from '../../services/import.service';

@Component({
    selector: 'kt-import-list',
    templateUrl: './import-list.component.html',
    imports: [KtMessageComponent, KtNotificationComponent, KtTableComponent, TranslatePipe, FaIconComponent, KtStaticMessageComponent]
})
export class ImportListComponent extends OnReadyBaseComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly subscriptions = new Subscription();
  private readonly deleteImportClick = new Subject<{ id: number; name: string }>();

  faPlus = faPlus;
  faTrash = faTrash;
  hasSenderDomain: boolean;
  useWhiteLabelDomain: boolean;
  tableItem: TableBase;
  noSenderDomainMessage: StaticMessageItem | null = null;

  constructor(
    protected translateService: TranslateService,
    private importService: ImportService,
    private matDialog: MatDialog,
    private router: Router,
    private configService: ConfigService,
    private ktMessageService: KtMessageService,
    private routingService: RouteParameterService,
    private importTableConvertService: ImportTableConvertService,
    private tabTitleService: TabTitleService
  ) {
    super(translateService, 'import-overview');
    this.hasSenderDomain = this.configService.hasAccess(AccountAccess.HasSenderDomain);
    this.useWhiteLabelDomain = this.configService.hasAccess(AccountAccess.UseWhiteLabelDomain);
  }

  ngOnInit(): void {
    this.tabTitleService.setTabName(isTranslateKey('tabNname::importbase::Import'));
    const highlightMessage = this.translateService.translate('import::delete::highlightmessage::Attention: This action cannot be undone!');
    const icon = faTrash;
    const button = this.translateService.translate('import::confirmdeleteimport::button::Delete import');

    this.subscriptions.add(this.importService.getImports().subscribe((data: Import[]) => this.handleImports(data)));
    let deletedName = '';
    const confirmDeleteSubscription = this.deleteImportClick
      .pipe(
        switchMap(({ id, name }) => {
          deletedName = name;
          const header = this.translateService.translate('import::confirmdeleteimport::header::Delete import @@name@@', { name: deletedName });
          // prettier-ignore
          const message = this.translateService.translate('import::confirmdeleteimport::message::Are you sure you want to delete the import @@name@@? If your contacts have already been imported, you will only delete the entry in this table and the generated reports. If you have not finished the import yet, the field mapping and your import file will be lost.', { name: deletedName });
          return this.matDialog
            .open<ImportConfirmModalComponent, { header: string; message: string; highlightMessage: string; icon: IconProp; button: string }, boolean>(
              ImportConfirmModalComponent,
              {
                data: { header, message, highlightMessage, icon, button }
              }
            )
            .afterClosed()
            .pipe(
              filter((confirmResult) => !!confirmResult),
              map(() => id)
            );
        }),
        switchMap((id) => {
          return this.importService.deleteImport(id);
        })
      )
      .subscribe(() => {
        this.setReady(false);
        const successDeleteMessage = this.translateService.translate('import::delete::successmessage::Import @@name@@ deleted successfully', { name: deletedName });
        this.ktMessageService.success(`${successDeleteMessage}`);
        this.tableItem = null;
        this.subscriptions.add(this.importService.getImports().subscribe((data: Import[]) => this.handleImports(data)));
      });

    this.subscriptions.add(confirmDeleteSubscription);
  }

  ngAfterViewInit(): void {
    if (!this.hasSenderDomain) {
      const message: string = this.useWhiteLabelDomain
        ? this.translateService.translate('import::warning::No White Label Domain available')
        : // prettier-ignore
          this.translateService.translate('import::dkim::warning::Please note that the import is only enabled who have set up a sending domain [linktext](https://url.de).');
      this.noSenderDomainMessage = { type: NotificationType.Warn, nodeId: 'warn-message-box', message };
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  createImportBtnClicked(): void {
    const res = this.configService.hasAccess(AccountAccess.SkipMillionVerifier);
    if (!res) {
      const header = this.translateService.translate('import::confirmconsentimport::header::Confirm the import policy');
      // prettier-ignore
      const html = this.translateService.translate('import::confirmconsentimport::intro::When you import your file into KlickTipp, you acknowledge our [Import Policy](https://www.klicktipp.com/import-policy/)');
      const visible = true;

      const sub = this.matDialog
        .open<ImportConsentModalComponent, { header: string; html: string; visible: boolean }, boolean>(ImportConsentModalComponent, {
          data: { header, html, visible }
        })
        .afterClosed()
        .subscribe((confirmResult) => {
          if (confirmResult) {
            this.router.navigateByUrl(`/import/${this.routingService.userId}/add`).finally();
          }
        });
      this.subscriptions.add(sub);
    } else {
      this.router.navigateByUrl(`/import/${this.routingService.userId}/add`).finally();
    }
  }

  tableEvent(clicked: TableButtonClicked): void {
    if (clicked.key === 'delete') {
      this.deleteImportClick.next({
        id: +clicked.element.rowData.find((r) => r.column === 'id').text,
        name: clicked.element.rowData.find((r) => r.column === 'id').text.toString()
      });
    } else {
      this.router.navigateByUrl(`/import/${this.routingService.userId}/${+clicked.element.rowData.find((r) => r.column === 'id').text}`).finally();
    }
  }

  private handleImports(data: Import[]): void {
    this.tableItem = this.importTableConvertService.createOverviewTable(data);
    this.setReady(true);
  }
}

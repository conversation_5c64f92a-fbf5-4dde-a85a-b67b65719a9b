import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { faCloudDownload, faCopy, faTimes } from '@fortawesome/pro-regular-svg-icons';
import { KtMessageService, KtTableComponent, TableBase, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { Subscription } from 'rxjs';
import { AccountAccess } from '../../../../enums/account-access.enum';
import { ConfigService } from '../../../../services/config.service';
import { RouteParameterService } from '../../../../services/route-parameter.service';
import { ImportConfirmModalComponent } from '../../../forms.shared/components/import-confirm-modal/import-confirm-modal.component';
import { ImportWizardComponent } from '../../../forms.shared/components/import-wizard/import-wizard.component';
import { ImportViews } from '../../enums/import-status.enum';
import { Import } from '../../models/entities/import.entity';
import { ImportConfig } from '../../models/import-config.interface';
import { ImportTableConvertService } from '../../services/import-table-convert.service';
import { ImportService } from '../../services/import.service';
import { ImportAssignFieldsComponent } from '../import-assign-fields/import-assign-fields.component';
import { ImportVerifyComponent } from '../import-verify/import-verify.component';

@Component({
    selector: 'kt-import-report',
    templateUrl: './import-report.component.html',
    imports: [ImportVerifyComponent, ImportWizardComponent, TranslatePipe, KtTableComponent, FaIconComponent, ImportAssignFieldsComponent]
})
export class ImportReportComponent implements OnInit, OnDestroy {
  private readonly subscriptions = new Subscription();

  @Input() hidden = true;
  @Input() setHeight: string;
  @Input() deletePadding: string;
  @Input() noDomainWarnStr: string;
  @Input() config: ImportConfig;

  @Output() importResult = new EventEmitter<Import>();
  @Output() cancel = new EventEmitter<Import>();

  faTimes = faTimes as IconProp;
  faCloudDownload = faCloudDownload as IconProp;
  faCopy = faCopy as IconProp;
  faTrash = faTrash as IconProp;
  verify = false;
  notEditable = true;
  warningTags: string[] = [];
  assignTags: string[] = [];
  count = 0;
  hasSenderDomain: boolean;
  isVisible: boolean;
  tableItem: TableBase;

  constructor(
    private matDialog: MatDialog,
    private translateService: TranslateService,
    private router: Router,
    private importService: ImportService,
    private configService: ConfigService,
    private ktMessageService: KtMessageService,
    private routingService: RouteParameterService,
    private importConvertTableService: ImportTableConvertService
  ) {
    this.hasSenderDomain = this.configService.hasAccess(AccountAccess.HasSenderDomain);
  }

  ngOnInit(): void {
    this.isVisible = false;
    const importReport = this.config.import?.stats?.import;
    const importWarnings = importReport?.warnings ?? 0;
    const rowData = [
      { count: importReport.errors, type: 'error' },
      { count: importReport.success, type: 'success' },
      { count: importWarnings, type: 'warning' }
    ];

    this.count = this.config.import.stats?.import?.success + this.config.import.stats?.import?.errors + importWarnings;

    this.tableItem = this.importConvertTableService.createResultTable(rowData, ImportViews.Report);

    this.config.import?.accountTags?.forEach((tag) => {
      this.config.import?.assignTags?.forEach((id: number) => {
        return tag.id === id ? this.assignTags.push(tag.name) : '';
      });
    });

    this.config.import?.accountTags?.forEach((tag) => {
      this.config.import?.warningTags?.forEach((id: number) => {
        return tag.id === id ? this.warningTags.push(tag.name) : '';
      });
    });

    if (!this.hasSenderDomain) {
      this.ktMessageService.warning(this.noDomainWarnStr, true);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  cancelClicked(): void {
    this.router.navigateByUrl(`/import/${this.routingService.userId}`).finally();
  }

  deleteClicked(): void {
    const header = this.translateService.translate('import::delete::header::Delete import', { name: this.config.import.name });
    // prettier-ignore
    const message = this.translateService.translate('import::delete::message::Are you sure you want to delete the import @@name@@? If your contacts have already been imported, you will only delete the entry in this table and the generated reports. If you have not finished the import yet, the field mapping and your import file will be lost.', { name: this.config.import.name });
    const highlightMessage = this.translateService.translate('import::delete::highlightmessage::Attention: This action cannot be undone!');
    const icon = faTrash as IconProp;
    const button = this.translateService.translate('import::delete::button::Delete import');

    const confirmDeleteSubscription = this.matDialog
      .open<ImportConfirmModalComponent, { header: string; message: string; highlightMessage: string; icon: IconProp; button: string }, boolean>(ImportConfirmModalComponent, {
        data: { header, message, highlightMessage, icon, button }
      })
      .afterClosed()
      .subscribe((confirmResult) => {
        if (confirmResult) {
          const deleteImportSubscription = this.importService.deleteImport(this.config.import.id).subscribe(() => {
            this.subscriptions.add(deleteImportSubscription);
            this.subscriptions.add(confirmDeleteSubscription);
            const successDeleteMessage = this.translateService.translate('import::delete::successmessage::Import @@name@@ deleted successfully', { name: this.config.import.name });
            this.ktMessageService.success(`${successDeleteMessage}`);
            this.router.navigateByUrl(`/import/${this.routingService.userId}`).finally();
          });
        }
      });
  }

  uploadRevisedClicked(): void {
    const copyImportSubscription = this.importService.copyImport(this.config.import.id).subscribe((id) => {
      this.subscriptions.add(copyImportSubscription);
      const successCopyMessage = this.translateService.translate('import::copy::successmessage::Import copied successfully');
      this.ktMessageService.success(`${successCopyMessage}`);
      this.router.navigateByUrl(`/import/${this.routingService.userId}/${id}`).finally();
    });
  }
}

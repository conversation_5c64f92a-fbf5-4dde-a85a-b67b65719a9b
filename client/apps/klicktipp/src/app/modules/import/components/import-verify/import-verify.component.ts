import { Async<PERSON>ipe, NgClass, NgTemplateOutlet } from '@angular/common';
import { Component, ContentChild, EventEmitter, Input, OnDestroy, OnInit, Output, TemplateRef } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatAutocomplete, MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatOptionModule } from '@angular/material/core';

import { MatDialog } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faChevronLeft } from '@fortawesome/free-solid-svg-icons';
import { faArrowCircleLeft, faCloudDownload, faCloudUpload, faTimes } from '@fortawesome/pro-regular-svg-icons';
import { KtMessageService, KtTableComponent, TableBase, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { Observable, Subscription } from 'rxjs';
import { filter, map, startWith } from 'rxjs/operators';
import { ImportWizardComponent } from '../../../forms.shared/components/import-wizard/import-wizard.component';
import { PopupComponent } from '../../../forms.shared/components/popup/popup.component';
import { Import, Tag } from '../../models/entities/import.entity';
import { ImportConfig } from '../../models/import-config.interface';
import { ImportValidatedResult } from '../../models/import-validated-result.interface';
import { AccountAccess } from './../../../../enums/account-access.enum';
import { ConfigService } from './../../../../services/config.service';
import { RouteParameterService } from './../../../../services/route-parameter.service';
import { ImportConfirmModalComponent } from './../../../forms.shared/components/import-confirm-modal/import-confirm-modal.component';
import { ImportConsentModalComponent } from './../../../forms.shared/components/import-consent-modal/import-consent-modal.component';
import { ImportStatus, ImportViews } from './../../enums/import-status.enum';
import { ImportTableConvertService } from './../../services/import-table-convert.service';

@Component({
    selector: 'kt-import-verify',
    templateUrl: './import-verify.component.html',
    imports: [
        NgClass,
        ImportWizardComponent,
        NgTemplateOutlet,
        ReactiveFormsModule,
        TranslatePipe,
        KtTableComponent,
        FaIconComponent,
        PopupComponent,
        MatInputModule,
        MatAutocompleteModule,
        MatOptionModule,
        AsyncPipe
    ]
})
export class ImportVerifyComponent implements OnInit, OnDestroy {
  private readonly subscriptions = new Subscription();

  private readonly tootlipHeaderTags = this.translateService.translate('import::verify::tooltipheadertag::Tag for all imported contacts');
  private readonly tootlipMessageTags = this.translateService.translate('import::verify::tooltipmessagetag::You can tag all contacts from your file, to make them easier to find.');
  private readonly tootlipHeaderWarningTags = this.translateService.translate('import::verify::tooltipheaderwarningtag::Tags for incompletely imported contacts');
  // prettier-ignore
  private readonly tootlipMessageWarningTags = this.translateService.translate('import::verify::tooltipmessagewarningtag::You can tag all contacts from your file that were imported with a warning, i.e. incomplete, to make them easier to find and correct later.');
  private readonly tootlipHeaderDoi = this.translateService.translate('import::verify::tooltipheaderdoi::Double-Opt-In');
  // prettier-ignore
  private readonly tootlipMessageDoi = this.translateService.translate('import::verify::tooltipmessagedoi::If your list contains contacts for which you do not yet have a DOI, please select one of your DOI processes stored in KlickTipp for the imported contacts. Your contacts will then automatically receive the confirmation email after the import.');
  // prettier-ignore
  private readonly policyText = this.translateService.translate('import::verify::policyMessage::I confirm that I have read the ' + '[Import Policy](https://www.klicktipp.com/import-policy/) and...');
  private readonly textBeforeDoi = this.translateService.translate('import::infomodal::firstPart::Since you selected the');
  // prettier-ignore
  private readonly textAfterDoi = this.translateService.translate('import::infomodal::secondPart::confirmation process to confirm your contacts, all contacts added with this import will subsequently receive the confirmation email stored for this process. The contacts will be listed as unconfirmed until they confirm their email address by clicking the link in the confirmation email.');

  @Input() setHeight: string;
  @Input() deletePadding: string;
  @Input() notEditable: boolean;
  @Input() verify = true;
  @Input() config: ImportConfig;
  @Input() noDomainWarnStr: string;

  @Output() importResult = new EventEmitter<ImportValidatedResult>();
  @Output() cancel = new EventEmitter<Import>();

  @ContentChild('wizard', { read: TemplateRef }) wizard: TemplateRef<Element>;
  @ContentChild('resultTable', { read: TemplateRef }) resultTable: TemplateRef<Element>;
  @ContentChild('selectedTags', { read: TemplateRef }) selectedTags: TemplateRef<Element>;
  @ContentChild('incompleteTags', { read: TemplateRef }) incompleteTags: TemplateRef<Element>;
  @ContentChild('buttonGroup', { read: TemplateRef }) buttonGroup: TemplateRef<Element>;

  get doiSelect(): AbstractControl {
    return this.verifyFormgroup.get('doi');
  }

  get accountTags(): Tag[] {
    return this.config.import?.accountTags ?? [];
  }

  policyUrl: string;

  faChevronLeft = faChevronLeft;
  faTimes = faTimes;
  faCloudUpload = faCloudUpload;
  faCloudDownload = faCloudDownload;

  headerTags: string;
  messageTags: string;
  headerWarningTags: string;
  messageWarningTags: string;
  headerDoi: string;
  messageDoi: string;
  tableItem: TableBase;

  count = 0;
  showAssignTags = true;
  isChecked = true;

  importDOIs = [
    { labelKey: this.translateService.translate('import::importdoi::label::Please select'), value: '-1', optin: undefined },
    { labelKey: this.translateService.translate('import::importdoi::label::DOI for all contacts present'), value: '0', optin: undefined }
  ];

  filteredTags: Observable<Tag[]>;
  filteredWarningTags: Observable<Tag[]>;

  selectedAccountTags: Tag[] = [];
  selectedAccountTagsWarnings: Tag[] = [];

  showTagsContacts = false;
  showTagsContactsWarnings = false;

  checked = new FormControl(true);
  doi = new FormControl(-1);
  checkedVerify = new FormControl(false, Validators.requiredTrue);
  tagSearchFormControl = new FormControl();
  warningTagSearchFormControl = new FormControl();

  verifyFormgroup = new FormGroup({
    checked: this.checked,
    doi: this.doi,
    checkedVerify: this.checkedVerify,
    tagSearchFormControl: this.tagSearchFormControl,
    warningTagSearchFormControl: this.warningTagSearchFormControl
  });

  hasSenderDomain: boolean;

  optinLink = undefined;

  hasInvalidImportStats: boolean;

  constructor(
    private matDialog: MatDialog,
    private translateService: TranslateService,
    private router: Router,
    private configService: ConfigService,
    private ktMessageService: KtMessageService,
    private routingService: RouteParameterService,
    private importConvertTableService: ImportTableConvertService
  ) {
    this.hasSenderDomain = this.configService.hasAccess(AccountAccess.HasSenderDomain);
    this.policyUrl = this.policyText;
  }

  ngOnInit(): void {
    this.ktMessageService.hideBar();
    this.updateDoiValidation();

    if (!this.hasSenderDomain) {
      this.ktMessageService.warning(this.noDomainWarnStr, true);
      this.verifyFormgroup.disable();
    }

    this.headerTags = this.tootlipHeaderTags;
    this.messageTags = this.tootlipMessageTags;
    this.headerWarningTags = this.tootlipHeaderWarningTags;
    this.messageWarningTags = this.tootlipMessageWarningTags;
    this.headerDoi = this.tootlipHeaderDoi;
    this.messageDoi = this.tootlipMessageDoi;

    if (this.config.import?.stats?.validation) {
      const validation = this.config.import?.stats?.validation;
      const rowData = [
        { count: validation.errors, type: 'error' },
        { count: validation.warnings, type: 'warning' },
        { count: validation.valid, type: 'valid' }
      ];
      this.count = validation.valid + validation.warnings + validation.errors;

      this.showTagsContacts = validation.valid > 0;
      this.showTagsContactsWarnings = validation.warnings > 0;
      this.tableItem = this.importConvertTableService.createResultTable(rowData, ImportViews.Verify);
    }

    const accountListDOIs = this.config.import?.accountLists?.map((l) => ({ labelKey: l.name, value: l.id, optin: l.isSingleOptIn }));
    this.importDOIs = [...this.importDOIs, ...accountListDOIs];

    this.filteredTags = this.tagSearchFormControl.valueChanges.pipe(
      startWith(''),
      map((value) => this.filter(value, this.selectedAccountTags))
    );
    this.filteredWarningTags = this.warningTagSearchFormControl.valueChanges.pipe(
      startWith(''),
      map((value) => this.filter(value, this.selectedAccountTagsWarnings))
    );

    if (this.config.import?.status === ImportStatus.Done) {
      this.checkedVerify = new FormControl(true);
      const selectedList = this.config.import?.listID;
      this.doi = new FormControl(selectedList);
      if (this.notEditable) {
        this.doi.disable();
      }
    }

    this.hasInvalidImportStats = this.config.import?.stats?.validation?.valid < 1 && this.config.import?.stats?.validation?.warnings < 1;

    const userId = this.configService.getConfig().account.uid;

    const showOptinLinkSub = this.doi.valueChanges
      .pipe(startWith('-1'))
      .pipe(
        map((value) => {
          return +value > 0 ? `/lists/${userId}/${value}/edit/` : undefined;
        })
      )
      .subscribe((link) => (this.optinLink = link));

    // https://ktlocal.com/lists/1/14/edit
    // 1 => user id
    // 14 => id from doi

    this.subscriptions.add(showOptinLinkSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  updateState(): void {
    this.showAssignTags = !!this.isChecked;
  }

  overviewButtonClicked(): void {
    this.router.navigateByUrl(`/import/${this.routingService.userId}`).finally();
  }

  importButtonClicked(): void {
    const selectedDoi = this.importDOIs.find((doi) => doi.value === this.doi.value.toString());
    const header = this.translateService.translate('import::infomodal::header::Confirmation procedure for your contacts');
    const html = this.textBeforeDoi + ' ' + this.translateService.translate(selectedDoi?.labelKey) + ' ' + this.textAfterDoi;
    const visible = false;

    if (selectedDoi?.optin === 0 || undefined) {
      const sub = this.matDialog
        .open<ImportConsentModalComponent, { header: string; html: string; visible: boolean }, boolean>(ImportConsentModalComponent, {
          data: { header, html, visible }
        })
        .afterClosed()
        .pipe(filter((confirmResult) => !!confirmResult))
        .subscribe(() => {
          this.importResult.emit({
            config: this.config,
            listID: this.doi.value,
            assignTags: this.selectedAccountTags.map((t) => t.id),
            warningTags: this.selectedAccountTagsWarnings.map((t) => t.id),
            importWarnings: this.checked.value ? 1 : 0
          });
        });
      this.subscriptions.add(sub);
    } else {
      this.importResult.emit({
        config: this.config,
        listID: this.doi.value,
        assignTags: this.selectedAccountTags.map((t) => t.id),
        warningTags: this.selectedAccountTagsWarnings.map((t) => t.id),
        importWarnings: this.checked.value ? 1 : 0
      });
    }
  }

  cancelButtonClicked(): void {
    const header = this.translateService.translate('import::confirmbacktofileselection::header::Back to file selection');
    // prettier-ignore
    const message = this.translateService.translate('import::confirmbacktofileselection::message::Are you sure you want to go back to the file selection? If you continue, your import will be discarded. You can continue the import process at a later time, but you will have to select your file again and reassign the fields.');
    const icon = faArrowCircleLeft as IconProp;
    const button = this.translateService.translate('import::confirmbacktofileselection::button::Back to file selection');

    const sub = this.matDialog
      .open<ImportConfirmModalComponent, { header: string; message: string; icon: IconProp; button: string }, boolean>(ImportConfirmModalComponent, {
        data: { header, message, icon, button }
      })
      .afterClosed()
      .pipe(filter((confirmResult) => !!confirmResult))
      .subscribe(() => this.cancel.emit(this.config.import));
    this.subscriptions.add(sub);
  }

  tagSelected(value: Tag, input: HTMLInputElement, auto: MatAutocomplete): void {
    this.selectedAccountTags = [...this.selectedAccountTags, value];

    // reset input
    auto.options.forEach((option) => option.deselect());
    this.tagSearchFormControl.reset();

    input.blur();
  }

  tagSelectedWarning(value: Tag, input: HTMLInputElement, auto: MatAutocomplete): void {
    this.selectedAccountTagsWarnings = [...this.selectedAccountTagsWarnings, value];

    // reset input
    auto.options.forEach((option) => option.deselect());
    this.warningTagSearchFormControl.reset();

    input.blur();
  }

  onRemoveSelectedTag(tag: Tag): void {
    this.selectedAccountTags = this.selectedAccountTags.filter((t) => t.id !== tag.id);
    this.tagSearchFormControl.reset();
  }

  onRemoveSelectedTagWarning(tag: Tag): void {
    this.selectedAccountTagsWarnings = this.selectedAccountTagsWarnings.filter((t) => t.id !== tag.id);
    this.warningTagSearchFormControl.reset();
  }

  updateDoiValidation(): void {
    if (this.config.import.noSubscriptionImport === 1) {
      this.doi.setValue(0);
    } else {
      this.doi.setValidators([Validators.required, Validators.min(0)]);
      this.doi.updateValueAndValidity();
    }
  }

  private filter(value: string, selectedTags: Tag[]): Tag[] {
    const filterValue = typeof value === 'string' ? (value?.toLowerCase() ?? '') : '';
    return this.accountTags.filter((option) => option.name.toLowerCase().includes(filterValue)).filter((option) => !selectedTags.find((st) => st.id === option.id));
  }
}

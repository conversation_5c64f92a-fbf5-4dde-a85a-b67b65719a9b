<h1 [innerHtml]="unsubscriptionMessageTitle"></h1>
<form [formGroup]="form">
  <kt-input [input]="nameItem" [formControlName]="nameItem.id" />
  <kt-magic-select [item]="taggingSelectItem" [formControlName]="taggingSelectItem.id" [allTags]="tags$ | async" [tags]="selectedTags" />
  <kt-input [input]="messageItem" [formControlName]="messageItem.id" />

  <div class="mt-15">
    <kt-button [icon]="iconChevronLeft" (btnClick)="back()" [buttonText]="backButtonText" nodeId="unsubscription-message-cancel-button" />
    @if (isCreate) {
      <kt-button [icon]="iconCheck" [color]="'primary'" (btnClick)="createUnsubscriptionMessage()" [buttonText]="createButtonText" nodeId="unsubscription-message-create-button" />
    }
    @if (!isCreate) {
      <kt-button [icon]="iconTrash" [color]="'warn'" (btnClick)="deleteUnsubscriptionMessage()" [buttonText]="deleteButtonText" nodeId="unsubscription-message-delete-button" />
      <kt-button [icon]="iconCheck" [color]="'primary'" (btnClick)="updateUnsubscriptionMessage()" [buttonText]="saveButtonText" nodeId="unsubscription-message-save-button" />
    }
  </div>
</form>

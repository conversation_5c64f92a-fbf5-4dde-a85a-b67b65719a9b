/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { TranslateService } from '@klicktipp/kt-mat-library';
import { Observable, of, throwError } from 'rxjs';
import { ModalService } from '../../../services/modal.service';
import {
  UnsubscriptionMessageDeleteResponse,
  UnsubscriptionMessageEntity,
  UnsubscriptionMessageOverviewResponse,
  UnsubscriptionMessageSettingsResponse,
  UnsubscriptionMessageWeight
} from '../models/unsubscription-message';
import { UnsubscriptionMessageApiService } from '../services/unsubscription-message-api.service';
import * as UnsubscriptionMessageActions from './unsubscription-message.actions';
import * as UnsubscriptionMessageEffects from './unsubscription-message.effects';

describe('UnsubscriptionMessageEffects', () => {
  describe('initOverview$', () => {
    it('should map to loadOverviewSuccess', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.initOverview());
      const unsubscriptionMessageApiServiceMock = {
        getItems: (): Observable<UnsubscriptionMessageOverviewResponse> =>
          of({
            data: {
              entities: [],
              createTypes: [],
              links: null
            }
          })
      } as UnsubscriptionMessageApiService;

      UnsubscriptionMessageEffects.initOverview$(actionsMock, unsubscriptionMessageApiServiceMock).subscribe((action) => {
        expect(action).toEqual(
          UnsubscriptionMessageActions.loadOverviewSuccess({
            entities: [],
            createTypes: [],
            links: null
          })
        );
        done();
      });
    });

    it('should map to loadOverviewFailure', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.initOverview());
      const error = new Error('loadOverviewFailure');
      const unsubscriptionMessageApiServiceMock = { getItems: () => throwError(error) };

      UnsubscriptionMessageEffects.initOverview$(actionsMock, unsubscriptionMessageApiServiceMock as any).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.loadOverviewFailure({ error }));
        done();
      });
    });
  });

  describe('initSettings$', () => {
    it('should map to loadSettingsSuccess', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.initSettings({ id: 42 }));
      const entity = {
        id: 42,
        name: 'Test Unsubscription Message',
        message: 'This is a test message',
        links: { edit: 'edit' },
        tags: null
      };
      const unsubscriptionMessageApiServiceMock = {
        getItem: (id: number): Observable<UnsubscriptionMessageSettingsResponse> =>
          of({
            data: {
              entity: { ...entity, id },
              links: { overview: 'overview' }
            }
          })
      } as UnsubscriptionMessageApiService;

      UnsubscriptionMessageEffects.initSettings$(actionsMock, unsubscriptionMessageApiServiceMock).subscribe((action) => {
        expect(action).toEqual(
          UnsubscriptionMessageActions.loadSettingsSuccess({
            entity: { ...entity, id: 42 },
            links: { overview: 'overview' }
          })
        );
        done();
      });
    });

    it('should map to loadSettingsFailure', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.initSettings({ id: 42 }));
      const error = new Error('loadSettingsFailure');
      const unsubscriptionMessageApiServiceMock = { getItem: () => throwError(error) };

      UnsubscriptionMessageEffects.initSettings$(actionsMock, unsubscriptionMessageApiServiceMock as any).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.loadSettingsFailure({ error }));
        done();
      });
    });
  });

  describe('createUnsubscriptionMessage$', () => {
    it('should map to createUnsubscriptionMessageSuccess', (done) => {
      const entity: UnsubscriptionMessageEntity = {
        id: null,
        name: 'New Unsubscription Message',
        message: 'This is a new message',
        links: null,
        tags: null
      };
      const actionsMock = of(UnsubscriptionMessageActions.createUnsubscriptionMessage({ entity }));

      const unsubscriptionMessageApiServiceMock = {
        create: (_: any): Observable<any> => of({ id: 42 })
      } as UnsubscriptionMessageApiService;

      UnsubscriptionMessageEffects.createUnsubscriptionMessage$(actionsMock, unsubscriptionMessageApiServiceMock).subscribe((action) => {
        expect(action).toEqual(
          UnsubscriptionMessageActions.createUnsubscriptionMessageSuccess({
            entityName: 'New Unsubscription Message'
          })
        );
        done();
      });
    });

    it('should map to createUnsubscriptionMessageFailure', (done) => {
      const entity: UnsubscriptionMessageEntity = {
        id: null,
        name: 'New Unsubscription Message',
        message: 'This is a new message',
        links: null,
        tags: null
      };
      const actionsMock = of(UnsubscriptionMessageActions.createUnsubscriptionMessage({ entity }));
      const error = new Error('createUnsubscriptionMessageFailure');
      const unsubscriptionMessageApiServiceMock = {
        create: () => throwError(error)
      } as any;

      UnsubscriptionMessageEffects.createUnsubscriptionMessage$(actionsMock, unsubscriptionMessageApiServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.createUnsubscriptionMessageFailure({ error }));
        done();
      });
    });
  });

  describe('createUnsubscriptionMessageSuccess$', () => {
    it('should show success message and navigate to overview', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.createUnsubscriptionMessageSuccess({ entityName: 'Test Message' }));
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;
      const translateServiceMock = { translate: jest.fn().mockImplementation((message, params) => `Translated: ${message} with ${params.name}`) } as any;

      UnsubscriptionMessageEffects.createUnsubscriptionMessageSuccess$(actionsMock, messageServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.navigateToOverview());
        expect(messageServiceMock.success).toHaveBeenCalledWith(
          'Translated: UnsubscriptionMessage::CreateDetail::Create::Success::UnsubscriptionMessage @@name@@ successfully created with Test Message'
        );
        done();
      });
    });
  });

  describe('updateUnsubscriptionMessage$', () => {
    it('should map to updateUnsubscriptionMessageSuccess', (done) => {
      const entity: UnsubscriptionMessageEntity = {
        id: 42,
        name: 'Updated Unsubscription Message',
        message: 'This is an updated message',
        links: null,
        tags: null
      };
      const actionsMock = of(UnsubscriptionMessageActions.updateUnsubscriptionMessage({ entity }));

      const unsubscriptionMessageApiServiceMock = {
        save: (_id: number, _entity: any): Observable<any> => of({ id: 42 })
      } as UnsubscriptionMessageApiService;

      UnsubscriptionMessageEffects.updateUnsubscriptionMessage$(actionsMock, unsubscriptionMessageApiServiceMock).subscribe((action) => {
        expect(action).toEqual(
          UnsubscriptionMessageActions.updateUnsubscriptionMessageSuccess({
            entityName: 'Updated Unsubscription Message'
          })
        );
        done();
      });
    });

    it('should map to updateUnsubscriptionMessageFailure', (done) => {
      const entity: UnsubscriptionMessageEntity = {
        id: 42,
        name: 'Updated Unsubscription Message',
        message: 'This is an updated message',
        links: null,
        tags: null
      };
      const actionsMock = of(UnsubscriptionMessageActions.updateUnsubscriptionMessage({ entity }));
      const error = new Error('updateUnsubscriptionMessageFailure');
      const unsubscriptionMessageApiServiceMock = {
        save: () => throwError(error)
      } as any;

      UnsubscriptionMessageEffects.updateUnsubscriptionMessage$(actionsMock, unsubscriptionMessageApiServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.updateUnsubscriptionMessageFailure({ error }));
        done();
      });
    });
  });

  describe('updateUnsubscriptionMessageSuccess$', () => {
    it('should show success message and navigate to overview', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.updateUnsubscriptionMessageSuccess({ entityName: 'Test Message' }));
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;
      const translateServiceMock = { translate: jest.fn().mockImplementation((message, params) => `Translated: ${message} with ${params.name}`) } as any;

      UnsubscriptionMessageEffects.updateUnsubscriptionMessageSuccess$(actionsMock, messageServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.navigateToOverview());
        expect(messageServiceMock.success).toHaveBeenCalledWith(
          'Translated: UnsubscriptionMessage::CreateDetail::Update::Success::UnsubscriptionMessage @@name@@ successfully saved with Test Message'
        );
        done();
      });
    });
  });

  describe('deleteUnsubscriptionMessage$', () => {
    it('should map to deleteUnsubscriptionMessageSuccess', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.deleteUnsubscriptionMessage({ id: 42 }));

      const unsubscriptionMessageApiServiceMock = {
        delete: (_id: number): Observable<UnsubscriptionMessageDeleteResponse> => of({ data: { message: 'Message From API' } })
      } as UnsubscriptionMessageApiService;

      UnsubscriptionMessageEffects.deleteUnsubscriptionMessage$(actionsMock, unsubscriptionMessageApiServiceMock).subscribe((action) => {
        expect(action).toEqual(
          UnsubscriptionMessageActions.deleteUnsubscriptionMessageSuccess({
            message: 'Message From API'
          })
        );
        done();
      });
    });

    it('should map to deleteUnsubscriptionMessageFailure', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.deleteUnsubscriptionMessage({ id: 42 }));
      const error = new Error('deleteUnsubscriptionMessageFailure');
      const unsubscriptionMessageApiServiceMock = {
        delete: () => throwError(error)
      };

      UnsubscriptionMessageEffects.deleteUnsubscriptionMessage$(actionsMock, unsubscriptionMessageApiServiceMock as any).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.deleteUnsubscriptionMessageFailure({ error }));
        done();
      });
    });
  });

  describe('deleteUnsubscriptionMessageSuccess$', () => {
    it('should show success message and navigate to overview', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.deleteUnsubscriptionMessageSuccess({ message: 'Delete Success' }));
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;

      UnsubscriptionMessageEffects.deleteUnsubscriptionMessageSuccess$(actionsMock, messageServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.navigateToOverview());
        expect(messageServiceMock.success).toHaveBeenCalledWith('Delete Success');
        done();
      });
    });
  });

  describe('showUnsubscriptionMessageDeleteModal$', () => {
    it('should map to deleteUnsubscriptionMessage if apply is given from modal', (done) => {
      const actionsMock = of(
        UnsubscriptionMessageActions.showDeleteModal({ currentId: 42, modalData: { id: 'unsubscription-delete-message', title: 'Delete', message: 'Are you sure?' } })
      );
      const translateServiceMock = { translate: (v: string): string => v } as TranslateService;

      const matDialogMock = { open: () => ({ afterClosed: () => of({ apply: true }) }) } as any;
      const overlayMock = { scrollStrategies: { noop: () => ({ enable: () => null, disable: () => null, attach: () => null }) } } as any;
      const modalService = new ModalService(matDialogMock, overlayMock, translateServiceMock);

      const navigationServiceMock = { routeTo: (_: any) => null } as any;

      UnsubscriptionMessageEffects.showUnsubscriptionMessageDeleteModal$(actionsMock, modalService, navigationServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.deleteUnsubscriptionMessage({ id: 42 }));
        done();
      });
    });

    it('should map to noopAction if clickedUrl and id is given from modal', (done) => {
      const actionsMock = of(
        UnsubscriptionMessageActions.showDeleteModal({ currentId: 42, modalData: { id: 'unsubscription-delete-message', title: 'Delete', message: 'Are you sure?' } })
      );
      const translateServiceMock = { translate: (v: string): string => v } as TranslateService;

      const matDialogMock = { open: () => ({ afterClosed: () => of({ clickedUrl: 'https://www.google.com', id: 42 }) }) } as any;
      const overlayMock = { scrollStrategies: { noop: () => ({ enable: () => null, disable: () => null, attach: () => null }) } } as any;
      const modalService = new ModalService(matDialogMock, overlayMock, translateServiceMock);

      const navigationServiceMock = { routeTo: jest.fn().mockImplementation(() => null) } as any;

      UnsubscriptionMessageEffects.showUnsubscriptionMessageDeleteModal$(actionsMock, modalService, navigationServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.noopAction());
        expect(navigationServiceMock.routeTo).toHaveBeenCalledWith('https://www.google.com');
        done();
      });
    });

    it('should map to noopAction if false is given from modal', (done) => {
      const actionsMock = of(
        UnsubscriptionMessageActions.showDeleteModal({ currentId: 42, modalData: { id: 'unsubscription-delete-message', title: 'Delete', message: 'Are you sure?' } })
      );
      const translateServiceMock = { translate: (v: string): string => v } as TranslateService;

      const matDialogMock = { open: () => ({ afterClosed: () => of(false) }) } as any;
      const overlayMock = { scrollStrategies: { noop: () => ({ enable: () => null, disable: () => null, attach: () => null }) } } as any;
      const modalService = new ModalService(matDialogMock, overlayMock, translateServiceMock);

      const navigationServiceMock = { routeTo: (_: any) => null } as any;

      UnsubscriptionMessageEffects.showUnsubscriptionMessageDeleteModal$(actionsMock, modalService, navigationServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.noopAction());
        done();
      });
    });
  });

  describe('navigateToOverview$', () => {
    it('should call the navigation service for navigateToOverview', (done) => {
      const dispatchAction = UnsubscriptionMessageActions.navigateToOverview();
      const facadeMock = { settingsLinks$: of({ overview: '/unsubscription/overview' }) } as any;
      const navigationServiceMock = { routeTo: jest.fn().mockImplementation(() => null) } as any;

      UnsubscriptionMessageEffects.navigateToOverview$(of(dispatchAction), navigationServiceMock, facadeMock).subscribe(() => {
        expect(navigationServiceMock.routeTo).toHaveBeenCalledWith('/unsubscription/overview');
        done();
      });
    });
  });

  describe('saveSortedEntities$', () => {
    it('should map to saveSortedEntitiesSuccess', (done) => {
      const entities: UnsubscriptionMessageWeight[] = [
        { id: 1, weight: 0 },
        { id: 2, weight: 1 }
      ];
      const actionsMock = of(UnsubscriptionMessageActions.saveSortedEntities({ entities }));

      const unsubscriptionMessageApiServiceMock = {
        saveSortedRowItems: (_data: any): Observable<any> => of({})
      } as UnsubscriptionMessageApiService;

      UnsubscriptionMessageEffects.saveSortedEntities$(actionsMock, unsubscriptionMessageApiServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.saveSortedEntitiesSuccess());
        done();
      });
    });

    it('should map to saveSortedEntitiesFailure', (done) => {
      const entities: UnsubscriptionMessageWeight[] = [
        { id: 1, weight: 0 },
        { id: 2, weight: 1 }
      ];
      const actionsMock = of(UnsubscriptionMessageActions.saveSortedEntities({ entities }));
      const error = new Error('saveSortedEntitiesFailure');
      const unsubscriptionMessageApiServiceMock = {
        saveSortedRowItems: () => throwError(error)
      } as any;

      UnsubscriptionMessageEffects.saveSortedEntities$(actionsMock, unsubscriptionMessageApiServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.saveSortedEntitiesFailure({ error }));
        done();
      });
    });
  });

  describe('saveSortedEntitiesSuccess$', () => {
    it('should show success message and reload overview', (done) => {
      const actionsMock = of(UnsubscriptionMessageActions.saveSortedEntitiesSuccess());
      const messageServiceMock = { success: jest.fn().mockImplementation(() => null) } as any;
      const translateServiceMock = { translate: jest.fn().mockImplementation((message) => `Translated: ${message}`) } as any;

      UnsubscriptionMessageEffects.saveSortedEntitiesSuccess$(actionsMock, messageServiceMock, translateServiceMock).subscribe((action) => {
        expect(action).toEqual(UnsubscriptionMessageActions.initOverview());
        expect(messageServiceMock.success).toHaveBeenCalledWith('Translated: UnsubscriptionMessage::Overview::Message::SaveSuccess::Sorting saved successfully');
        done();
      });
    });
  });
});

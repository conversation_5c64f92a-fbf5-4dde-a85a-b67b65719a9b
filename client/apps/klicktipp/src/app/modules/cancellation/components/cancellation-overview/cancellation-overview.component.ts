import { OnReadyBaseComponent } from './../../../forms.shared/components/on-ready/on-ready-base.component';
import { InputItem, KtInputComponent, KtNotificationComponent, KtPanelComponent, KtShadowDomComponent, TranslatePipe, TranslateService } from '@klicktipp/kt-mat-library';
import { AfterViewInit, Component, OnInit } from '@angular/core';
import { CancellationApiService } from './../../services/api/cancellation-api.service';
import { CancellationOverview } from './../../models/cancellation-base';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
    selector: 'kt-cancellation-overview',
    templateUrl: './cancellation-overview.component.html',
    imports: [KtPanelComponent, KtShadowDomComponent, KtNotificationComponent, ReactiveFormsModule, KtInputComponent, TranslatePipe]
})
export class CancellationOverviewComponent extends OnReadyBaseComponent implements OnInit, AfterViewInit {
  showShadowDom: boolean;
  shadowDomReady: boolean;
  htmlUrl: string;
  jsUrl: string;
  language: string;
  cancellationPanelHeadline: string = this.translateService.translate('Cancellation::Panel::Headline::KlickTipp Product');
  categoryInput: InputItem = {
    id: 'cancellationCategory',
    type: 'text',
    disabled: true,
    labelText: this.translateService.translate('Cancellation::Input::Label::Product Category')
  };

  contactInput: InputItem = {
    id: 'contactCategory',
    type: 'text',
    disabled: true,
    labelText: this.translateService.translate('Cancellation::Input::Label::Contacts')
  };

  paymentInterval: InputItem = {
    id: 'paymentInterval',
    type: 'text',
    disabled: true,
    labelText: this.translateService.translate('Cancellation::Input::Label::Payment Interval')
  };

  constructor(
    protected translateService: TranslateService,
    private cancellationApiService: CancellationApiService
  ) {
    super(translateService, 'cancellation-overview');
  }

  ngOnInit(): void {
    super.ngOnInit();
  }

  ngAfterViewInit(): void {
    this.getCancellationOverviewData();
  }

  getCancellationOverviewData(): void {
    this.cancellationApiService.getCancellationData().subscribe((response) => {
      this.showShadowDom = response.data.showShadowDom;
      this.htmlUrl = '/ipa/account/cancellation'; //response.data.shadowDomContentUrl;
      if (!this.htmlUrl || !this.showShadowDom) {
        this.shadowDomReady = true;
      }
      this.jsUrl = response.data.shadowDomJavaScriptUrl;
      this.language = response.data.language;
      this.initializeFormData(response.data);
      this.initFinished = true;
    });
  }

  initializeFormData(data: CancellationOverview): void {
    this.form.get(this.categoryInput.id).setValue(data.product);
    this.form.get(this.contactInput.id).setValue(data.tier);
    this.form.get(this.paymentInterval.id).setValue(data.term);
  }

  setShadowDomReady(_shadowDomStatus: boolean): void {
    this.shadowDomReady = true;
  }
}

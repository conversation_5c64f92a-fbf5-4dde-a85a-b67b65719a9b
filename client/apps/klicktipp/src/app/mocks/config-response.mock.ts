export const configResponseMock = {
  status: 'ok',
  account: {
    uid: '1',
    status: '1',
    tier: 0,
    usergroup: '42',
    username: 'User1',
    email: '<EMAIL>',
    firstname: 'FirstnameUser1',
    lastname: 'LastnameUser1',
    company: '',
    website: '',
    street: '',
    city: '',
    state: '',
    zip: '',
    country: 'UK',
    phone: '',
    fax: '',
    affid: '0',
    access: {
      'administer klicktipp': true,
      'access email editor': true,
      'access subscriber import': true,
      'skip MillionVerifier import validation': true,
      'has sender domain configured': true,
      'access sms marketing': true,
      'access dashboard checklists': false,
      'use whitelabel domain': true,
      'flag-access-trans-email': false,
      'flag-activate-landing-pages': true,
      'flag-activate-sentry': true,
      'flag-allow-landing-page-create': true,
      'flag-allow-landing-page-custom-domain': true,
      'flag-allow-newsletter-pre-generation': true,
      'flag-gpt-model-select-access': true,
      'flag-has-unlimited-landing-pages-templates': false,
      'flag-instant-web-triggers': true,
      'flag-log-unknown-errors-in-angular-to-sentry': true,
      'flag-review-demo-flag': true,
      'flag-show-beacon': false,
      'flag-show-console-logs-for-product-fruits': false,
      'flag-show-pre-generation-notification': true,
      'flag-show-product-fruits': true,
      'flag-show-user-like': true,
      'flag-skip-import-validation': false,
      'flag-smart-subject-access': true,
      'flag-smart-subject-release': true
    },
    language: 'en',
    userSettings: {
      EmailEditorManualSaveButton: null
    }
  },
  menu: {
    items: [
      {
        title: 'Contact Cloud',
        children: [
          {
            title: 'Contacts',
            path: '/subscribers/me',
            children: [
              {
                title: 'Add contacts',
                path: '/contacts/me/add',
                inAppLink: '',
                e2eId: 'main-0::subscribers-me::contacts-me-add',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Import (CSV)',
                path: '/application/me/import',
                inAppLink: 'https://localhost:4202/app/import',
                e2eId: 'main-0::subscribers-me::application-me-import',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Facebook Audience Sync',
                path: '/contacts/me/facebook-audience',
                inAppLink: '',
                e2eId: 'main-0::subscribers-me::contacts-me-facebook-audience',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Duplicate Contacts',
                path: '/subscribers/me/duplicates',
                inAppLink: '',
                e2eId: 'main-0::subscribers-me::subscribers-me-duplicates',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-0::subscribers-me',
            e2eSpecial: ''
          },
          {
            title: 'Contact Record',
            path: '/customfields/me',
            children: [
              {
                title: 'Create Custom Fields',
                path: '/customfields/me/add',
                inAppLink: '',
                e2eId: 'main-0::customfields-me::customfields-me-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-0::customfields-me',
            e2eSpecial: ''
          },
          {
            divider: true
          },
          {
            title: 'Feedback',
            path: '/contacts/me/feedback',
            children: [
              {
                title: 'Unsubscription Feedback',
                path: '/contacts/me/feedback',
                inAppLink: '',
                e2eId: 'main-0::contacts-me-feedback::contacts-me-feedback',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'SMS Feedback',
                path: '/contacts/me/sms',
                inAppLink: '',
                e2eId: 'main-0::contacts-me-feedback::contacts-me-sms',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-0::contacts-me-feedback'
          },
          {
            divider: true
          },
          {
            title: 'Blacklist',
            path: '/contacts/me/blacklist',
            inAppLink: ''
          }
        ],
        e2eId: 'main-0',
        'e2e-special': ''
      },
      {
        title: 'Listbuilding',
        children: [
          {
            title: 'Listbuilding',
            path: '/listbuilding/me',
            children: [
              {
                title: 'Create Listbuilding',
                path: '/listbuilding/me/create',
                inAppLink: '',
                e2eId: 'main-1::listbuilding-me::listbuilding-me-create',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-1::listbuilding-me',
            e2eSpecial: ''
          },
          {
            title: 'Marketing Tools',
            path: '/marketingtools/me',
            children: [
              {
                title: 'Create Marketing Tool',
                path: '/marketingtools/me/create',
                inAppLink: '',
                e2eId: 'main-1::marketingtools-me::marketingtools-me-create',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-1::marketingtools-me',
            e2eSpecial: ''
          },
          {
            title: 'Landingpages',
            path: '/application/me/landing-page/settings/me',
            children: [],
            inAppLink: 'https://localhost:4202/app/landing-page/settings/1',
            e2eId: 'main-1::application-me-landing-page-settings-me',
            e2eSpecial: ''
          },
          {
            title: 'Double-Opt-In Processes',
            path: '/lists/me',
            children: [
              {
                title: 'Create Double-Opt-In Process',
                path: '/lists/me/add',
                inAppLink: '',
                e2eId: 'main-1::lists-me::lists-me-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-1::lists-me',
            e2eSpecial: ''
          },
          {
            title: 'Unsubscription Processes',
            path: '/unsubscriptions/me',
            children: [
              {
                title: 'Unsubscription messages',
                path: '/unsubscriptions/me',
                inAppLink: '',
                e2eId: 'main-1::unsubscriptions-me::unsubscriptions-me',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Unsubscription URLs',
                path: '/unsubscriptions/me/url',
                inAppLink: '',
                e2eId: 'main-1::unsubscriptions-me::unsubscriptions-me-url',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-1::unsubscriptions-me',
            e2eSpecial: ''
          }
        ],
        e2eId: 'main-1',
        'e2e-special': ''
      },
      {
        title: 'Automation',
        children: [
          {
            title: 'Signatures / Sender Profile',
            path: 'app/signature/overview/me',
            children: [
              {
                title: 'Create Signature',
                path: '/app/signature/settings/me/create',
                inAppLink: '',
                e2eId: 'main-2::signatures-me::signatures-me-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-2::signatures-me',
            e2eSpecial: ''
          },
          {
            title: 'Tags',
            path: '/tags/me',
            children: [
              {
                title: 'Create Tag',
                path: '/tags/me/add',
                inAppLink: '',
                e2eId: 'main-2::tags-me::tags-me-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-2::tags-me',
            e2eSpecial: ''
          },
          {
            title: 'Tagging Pixels',
            path: '/tools/me/taggingpixels',
            children: [
              {
                title: 'Create Tagging Pixel',
                path: '/tools/me/taggingpixel/add',
                inAppLink: '',
                e2eId: 'main-2::tools-me-taggingpixels::tools-me-taggingpixel-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-2::tools-me-taggingpixels',
            e2eSpecial: ''
          },
          {
            title: 'SmartLinks',
            path: '/tags/me/smartlinks',
            children: [
              {
                title: 'Create SmartLink',
                path: '/tags/me/smartlink/add',
                inAppLink: '',
                e2eId: 'main-2::tags-me-smartlinks::tags-me-smartlink-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-2::tags-me-smartlinks',
            e2eSpecial: ''
          },
          {
            divider: true
          },
          {
            title: 'Outbounds',
            path: '/tools/me/outbound',
            children: [
              {
                title: 'Create Outbound',
                path: '/tools/me/outbound/add',
                inAppLink: '',
                e2eId: 'main-2::tools-me-outbound::tools-me-outbound-add',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Create Kajabi Activation',
                path: '/tools/me/kajabi/add',
                inAppLink: '',
                e2eId: 'main-2::tools-me-outbound::tools-me-kajabi-add',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Create Zapier Trigger',
                path: '/tools/me/zapier/add',
                inAppLink: '',
                e2eId: 'main-2::tools-me-outbound::tools-me-zapier-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-2::tools-me-outbound'
          },
          {
            divider: true
          },
          {
            title: 'Calendar',
            path: '/cockpit/me/calendar',
            inAppLink: ''
          },
          {
            title: 'Countdowns',
            path: '/tools/me/countdowns',
            children: [
              {
                title: 'Create Countdown',
                path: '/tools/me/countdown/add',
                inAppLink: '',
                e2eId: 'main-2::tools-me-countdowns::tools-me-countdown-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-2::tools-me-countdowns',
            e2eSpecial: ''
          }
        ],
        e2eId: 'main-2',
        'e2e-special': ''
      },
      {
        title: 'Campaigns',
        children: [
          {
            title: 'Campaigns',
            path: '/application/me/campaign/automation/me',
            children: [
              {
                title: 'Create Campaign',
                path: '/application/me/campaign/automation/me/create-automation',
                inAppLink: 'https://localhost:4202/app/campaign/automation/1/create-automation',
                e2eId: 'main-3::application-me-campaign-automation-me::application-me-campaign-automation-me-create-automation',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: 'https://localhost:4202/app/campaign/automation/1',
            e2eId: 'main-3::application-me-campaign-automation-me',
            e2eSpecial: ''
          },
          {
            title: 'Campaign Templates',
            path: '/tools/me/template',
            children: [
              {
                title: 'Create Campaign Template',
                path: '/tools/me/template/add',
                inAppLink: '',
                e2eId: 'main-3::tools-me-template::tools-me-template-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-3::tools-me-template',
            e2eSpecial: ''
          },
          {
            title: 'Overview BAM Automation Templates',
            path: '/automations/me/bam',
            inAppLink: '',
            e2eId: 'main-3::automations-me-bam',
            e2eSpecial: ''
          },
          {
            divider: true
          },
          {
            title: 'Follow-Up Campaigns',
            path: '/application/me/campaign/autoresponder/me',
            children: [
              {
                title: 'Create Follow-Up E-Mail',
                path: '/application/me/campaign/autoresponder-email/me/create',
                inAppLink: 'https://localhost:4202/app/campaign/autoresponder-email/1/create',
                e2eId: 'main-3::application-me-campaign-autoresponder-me::application-me-campaign-autoresponder-email-me-create',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Create Follow-Up SMS',
                path: '/application/me/campaign/autoresponder-sms/me/create',
                inAppLink: 'https://localhost:4202/app/campaign/autoresponder-sms/1/create',
                e2eId: 'main-3::application-me-campaign-autoresponder-me::application-me-campaign-autoresponder-sms-me-create',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Create Birthday Follow-Up E-Mail',
                path: '/application/me/campaign/autoresponder-email-birthday/me/create',
                inAppLink: 'https://localhost:4202/app/campaign/autoresponder-email-birthday/1/create',
                e2eId: 'main-3::application-me-campaign-autoresponder-me::application-me-campaign-autoresponder-email-birthday-me-create',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Create Birthday Follow-Up SMS',
                path: '/application/me/campaign/autoresponder-sms-birthday/1/create',
                inAppLink: 'https://localhost:4202/app/campaign/autoresponder-sms-birthday/1/create',
                e2eId: 'main-3::application-me-campaign-autoresponder-me::application-me-campaign-autoresponder-sms-birthday-1-create',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: 'https://localhost:4202/app/campaign/autoresponder/1',
            e2eId: 'main-3::application-me-campaign-autoresponder-me'
          },
          {
            divider: true
          },
          {
            title: 'Newsletter',
            path: '/application/me/campaign/newsletter/me',
            children: [
              {
                title: 'Create E-Mail Newsletter',
                path: '/application/me/campaign/newsletter-email/me/create',
                inAppLink: 'https://localhost:4202/app/campaign/newsletter-email/1/create',
                e2eId: 'main-3::application-me-campaign-newsletter-me::application-me-campaign-newsletter-email-me-create',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Create SMS Newsletter',
                path: '/application/me/campaign/newsletter-sms/me/create',
                inAppLink: 'https://localhost:4202/app/campaign/newsletter-sms/1/create',
                e2eId: 'main-3::application-me-campaign-newsletter-me::application-me-campaign-newsletter-sms-me-create',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: 'https://localhost:4202/app/campaign/newsletter/1',
            e2eId: 'main-3::application-me-campaign-newsletter-me'
          },
          {
            divider: true
          },
          {
            title: 'E-Mails / SMS',
            path: '/application/me/email/overview/me',
            children: [
              {
                title: 'Create Campaign E-Mail',
                path: '/application/me/email/automation-email/me/create',
                inAppLink: 'https://localhost:4202/app/email/automation-email/1/create',
                e2eId: 'main-3::application-me-email-overview-me::application-me-email-automation-email-me-create',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Create Campaign SMS',
                path: '/application/me/email/automation-sms/me/create',
                inAppLink: 'https://localhost:4202/app/email/automation-sms/1/create',
                e2eId: 'main-3::application-me-email-overview-me::application-me-email-automation-sms-me-create',
                e2eSpecial: 'trigger-js-click'
              },
              {
                divider: true
              },
              {
                title: 'Create Notification E-Mail',
                path: '/application/me/email/notification-email/me/create',
                inAppLink: 'https://localhost:4202/app/email/notification-email/1/create'
              },
              {
                title: 'Create Notification SMS',
                path: '/application/me/email/notification-sms/me/create',
                inAppLink: 'https://localhost:4202/app/email/notification-sms/1/create',
                e2eId: 'main-3::application-me-email-overview-me::application-me-email-notification-sms-me-create',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: 'https://localhost:4202/app/email/overview/1',
            e2eId: 'main-3::application-me-email-overview-me'
          },
          {
            divider: true
          },
          {
            title: 'E-Mail Templates',
            path: '/application/me/email/editor/me/templates',
            children: [],
            inAppLink: 'https://localhost:4202/app/email/editor/1/templates',
            e2eId: 'main-3::application-me-email-editor-me-templates'
          }
        ],
        e2eId: 'main-3',
        'e2e-special': ''
      },
      {
        title: 'Tools',
        children: [
          {
            title: 'Labels',
            path: '/cockpit/me/metalabel',
            inAppLink: '',
            e2eId: 'main-4::cockpit-me-metalabel',
            e2eSpecial: ''
          },
          {
            title: 'Meta Search',
            path: '/cockpit/me/metasearch',
            inAppLink: '',
            e2eId: 'main-4::cockpit-me-metasearch',
            e2eSpecial: ''
          },
          {
            divider: true
          },
          {
            title: 'Analytics Reports',
            path: '/tools/me/statistics',
            children: [
              {
                title: 'Create Statistic',
                path: '/tools/me/statistics/add',
                inAppLink: '',
                e2eId: 'main-4::tools-me-statistics::tools-me-statistics-add',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-4::tools-me-statistics'
          },
          {
            title: 'Youtube Content Analysis',
            path: '/youtube-analysis/me',
            inAppLink: '',
            e2eId: 'main-4::youtube-analysis-me',
            e2eSpecial: ''
          },
          {
            divider: true
          },
          {
            title: 'Privacy Dashboard',
            path: '/user/me/privacy',
            inAppLink: ''
          }
        ],
        e2eId: 'main-4',
        'e2e-special': ''
      },
      {
        title: 'My Account',
        children: [
          {
            title: 'Personal Information / AVV',
            path: '/user/me/personal-information',
            inAppLink: '',
            e2eId: 'main-5::user-me-personal-information',
            e2eSpecial: ''
          },
          {
            title: 'Settings',
            path: '/user/me/edit',
            children: [
              {
                title: 'User Settings',
                path: '/user/me/edit',
                inAppLink: '',
                e2eId: 'main-5::user-me-edit::user-me-edit',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'E-mail delivery',
                path: '/user/me/sender',
                inAppLink: '',
                e2eId: 'main-5::user-me-edit::user-me-sender',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Change Password',
                path: '/login/me/change-password',
                inAppLink: '',
                e2eId: 'main-5::user-me-edit::login-me-change-password',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Two-Factor-Authorization',
                path: '/user/klicktipp-twofactorauth',
                inAppLink: '',
                e2eId: 'main-5::user-me-edit::user-klicktipp-twofactorauth',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Domains',
                path: '/user/me/domains',
                inAppLink: '',
                e2eId: 'main-5::user-me-edit::user-me-domains',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Campaign Editor',
                path: '/user/me/marketingcockpit',
                inAppLink: '',
                e2eId: 'main-5::user-me-edit::user-me-marketingcockpit',
                e2eSpecial: 'trigger-js-click'
              },
              {
                title: 'Marketplace Profile',
                path: '/user/me/consultant',
                inAppLink: '',
                e2eId: 'main-5::user-me-edit::user-me-consultant',
                e2eSpecial: 'trigger-js-click'
              }
            ],
            inAppLink: '',
            e2eId: 'main-5::user-me-edit',
            e2eSpecial: ''
          },
          {
            title: 'My Digistore Invoices',
            path: '/user/me/digistore-invoice',
            inAppLink: 'https://localhost:4202/app/cancellation/1',
            e2eId: 'main-5::user-me-digistore-invoice',
            e2eSpecial: ''
          },
          {
            title: 'Subaccounts',
            path: '/user/me/subaccount/switch',
            inAppLink: '',
            e2eId: 'main-5::user-me-subaccount-switch',
            e2eSpecial: ''
          },
          {
            divider: true
          },
          {
            title: 'Support',
            path: 'http://###beacon',
            inAppLink: ''
          },
          {
            title: 'Logout',
            path: '/user/logout',
            inAppLink: '',
            e2eId: 'main-5::user-logout',
            e2eSpecial: ''
          }
        ],
        e2eId: 'main-5',
        'e2e-special': ''
      }
    ],
    footer: [],
    logo: {
      image: 'https://localhost:4202/misc/img/kt-logo-enterprise.svg',
      href: 'https://localhost:4202/app/dashboard/1'
    },
    beamer: false,
    beacon: false,
    showOtherAccountInfo: false,
    showSupportInfo: false,
    support: {
      username: 'User1',
      switchAccountsLink: null
    }
  },
  assets: {
    emailEditor: {
      auth: {
        access_token:
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aC4xVvRMYxD-ecSrlnNFOi_A4JJgE6Mk1L8N8Me_KSk',
        token_type: 'bearer',
        expires_in: 300,
        refresh_token:
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aC4xVvRMYxD-ecSrlnNFOi_A4JJgE6Mk1L8N8Me_KSk',
        'as:client_id': 'edffc765-fcec-4ec4-a71c-8c2a617115b0',
        userName: 'Jg4BkqEV5zyw',
        'as:region': 'eu-west-1',
        '.issued': 'Thu, 08 Aug 2024 10:14:25 GMT',
        '.expires': 'Thu, 08 Aug 2024 10:19:25 GMT'
      }
    },
    landingPageEditor: {
      auth: {
        access_token:
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8g8TLLfb6YAQeqRxDzk0Hwc-LMAJhBlTdJ8d4bWSwOU',
        token_type: 'bearer',
        expires_in: 300,
        refresh_token:
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8g8TLLfb6YAQeqRxDzk0Hwc-LMAJhBlTdJ8d4bWSwOU',
        'as:client_id': 'f017f8bb-d247-4839-9267-dd91be81948b',
        userName: 'JVOalx1HlU0z',
        'as:region': 'eu-west-1',
        '.issued': 'Thu, 08 Aug 2024 10:14:25 GMT',
        '.expires': 'Thu, 08 Aug 2024 10:19:25 GMT'
      }
    }
  }
};

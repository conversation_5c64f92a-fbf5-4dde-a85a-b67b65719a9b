import { HTTP_INTERCEPTORS, HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { KtNavigationService } from '@klicktipp/kt-mat-library';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ConfigService } from '../services/config.service';
import { HttpErrorService } from '../services/http-error.service';
import { RouteParameterService } from '../services/route-parameter.service';
import { isTranslateKey } from '../static/translate';

@Injectable({ providedIn: 'root' })
export class ErrorHttpInterceptor implements HttpInterceptor {
  private ktConfigChecked: boolean;
  constructor(
    private httpErrorService: HttpErrorService,
    private ktNavigationService: KtNavigationService,
    private routingService: RouteParameterService,
    private router: Router,
    private configService: ConfigService
  ) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse, _caught) => {
        if (request.url.includes('/kt-bee-auth/')) {
          return throwError(() => error);
        }
        let errorMessage: string;
        const isClientSideError = error.error instanceof ErrorEvent;
        // temporary error code for custom error handling in client
        if (error.status === 406) {
          return this.httpErrorService.handleCustomErrors(error.error, error.status);
        }
        // 0 if API call is not handled => must be handled in component or not at all -> (used for Email editor offline save)
        if (error.status === 0) {
          return throwError(error);
        }
        if (isClientSideError) {
          // client-side error
          errorMessage = `Error: ${error.error.message}`;
          const header = isTranslateKey('error::errorhttpinterceptor::header::Clientside error');
          this.httpErrorService.storeInterceptorError({ message: error.error.message, error, header });
        } else {
          // server-side error
          const isConfigAuthError = (error.status === 401 || error.status === 403) && error?.url?.includes('ipa/kt-config');
          if (isConfigAuthError) {
            return this.handleKtConfigError(error.status);
          } else {
            errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
            this.httpErrorService.storeInterceptorError({ message: error.statusText, httpStatus: error.status, error });
          }
        }
        return throwError(errorMessage);
      })
    );
  }

  handleKtConfigError(status: number): Observable<never> {
    if (status === 403) {
      if (this.ktConfigChecked) {
        this.httpErrorService.storeInterceptorError({
          message: 'Your subaccount could not be loaded properly - use button below to switch your subaccount to another one or remove the non-valid subaccounts',
          httpStatus: 403,
          error: 'Access denied',
          header: 'Config error',
          configError: true
        });
        return throwError('Access denied - no config could be found for your user');
      }
      if (!this.ktConfigChecked && !this.configService.getConfig()) {
        this.ktConfigChecked = true;
        this.routingService.userId = 'me';
      }
    }
    if (status === 401) {
      this.ktNavigationService.routeTo(`${window.location.origin}/auth/login`);
    } else {
      this.router.navigate([`/dashboard/${this.routingService.userId}`]).finally();
    }
    return throwError('Access denied - rerouting started');
  }
}

/** Http interceptor providers in outside-in order */
export const httpInterceptorProviders = [{ provide: HTTP_INTERCEPTORS, useClass: ErrorHttpInterceptor, multi: true }];

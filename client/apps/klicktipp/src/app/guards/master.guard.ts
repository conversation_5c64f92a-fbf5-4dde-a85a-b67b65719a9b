import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, switchMap, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { ConfigService } from '../services/config.service';
import { RouteParameterService } from '../services/route-parameter.service';
import { AnyGuardBase } from './any.guard';
import { DefaultRouteGuardBase } from './defaultRoute.guard';
import { CanLoadEmailEditor } from './email-editor.guard';
import { GUARDS } from './guardList';
import { CanLoadImport } from './import.guard';
import { CanLoadLandingPageEditor } from './landing-page-editor.guard';

@Injectable({ providedIn: 'root' })
class MasterGuardBase {
  route: ActivatedRouteSnapshot;
  state: RouterStateSnapshot;

  constructor(
    private routingService: RouteParameterService,
    private configService: ConfigService,
    private router: Router
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    let segments = state.url.split('/');
    segments = segments.filter((segment) => segment !== ''); // Remove empty segments

    let newRoute = '';

    // Check if development and re-route to the correct URL for debugging
    if (environment.stage === 'serve') {
      if (segments[0] === 'app') {
        newRoute = '/' + segments.slice(1).join('/');
        this.router.navigate([newRoute]).finally();
        return of(false);
      }
    }
    this.route = route;
    this.state = state;
    const loadConfig = this.routingService.handleParams(state.url, route);
    // we don't have a new user id, let's get the config (only place to do that)
    if (loadConfig) {
      return this.configService.getConfigFromApi().pipe(
        switchMap((_config) => {
          return this.handleGuardResult();
        }),
        catchError(() => of(false))
      );
    } else {
      return this.handleGuardResult();
    }
  }

  private handleGuardResult(): Observable<boolean> {
    // no guards defined
    if (!this.route.data.guards || !this.route.data.guards.length) {
      return of(true);
    }
    return this.executeGuards(0).pipe(
      switchMap((hasAccess) => {
        if (!hasAccess) {
          const userId = this.routingService.userId ?? 'me';
          this.router.navigate([`/dashboard/${userId}`]).finally();
        }
        return of(hasAccess);
      }),
      catchError(() => of(false))
    );
  }

  //Execute the guards sent in the route data
  private executeGuards(guardIndex = 0): Observable<boolean> {
    return this.activateGuard(this.route.data.guards[guardIndex]).pipe(
      tap(() => {
        if (guardIndex < this.route.data.guards.length - 1) {
          return this.executeGuards(guardIndex + 1);
        } else {
          return of(true);
        }
      }),
      catchError(() => of(false))
    );
  }

  //Create an instance of the guard and fire canActivate method returning a promise
  private activateGuard(guardKey: string): Observable<boolean> {
    let guard: AnyGuardBase | CanLoadEmailEditor | CanLoadLandingPageEditor | CanLoadImport | DefaultRouteGuardBase;

    switch (guardKey) {
      case GUARDS.AnyGuard:
        guard = new AnyGuardBase(this.configService, this.router, this.routingService);
        break;
      case GUARDS.EmailEditorGuard:
        guard = new CanLoadEmailEditor(this.configService, this.routingService);
        break;
      case GUARDS.LandingPageGuard:
        guard = new CanLoadLandingPageEditor(this.configService, this.routingService, this.router);
        break;
      case GUARDS.ImportGuard:
        guard = new CanLoadImport(this.configService);
        break;
      case GUARDS.DefaultRouteGuard:
        guard = new DefaultRouteGuardBase(this.router);
        break;
      default:
        return of(true);
    }
    return guard.canActivate(this.route, this.state);
  }
}

export const MasterGuard: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> => {
  return inject(MasterGuardBase).canActivate(route, state);
};

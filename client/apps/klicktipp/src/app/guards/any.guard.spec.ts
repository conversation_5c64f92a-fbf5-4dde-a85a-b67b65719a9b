import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { RouteParameterService } from './../services/route-parameter.service';
import { MockProvider } from 'ng-mocks';
import { MarkdownModule } from 'ngx-markdown';
import { ConfigService } from '../services/config.service';
import { AnyGuardBase } from './any.guard';

describe('AnyGuardBase', () => {
  let guard: AnyGuardBase;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [MockProvider(ConfigService, { getConfig: jest.fn() }), RouteParameterService],
      imports: [RouterTestingModule, MarkdownModule.forRoot(), HttpClientTestingModule]
    });
    guard = TestBed.inject(AnyGuardBase);
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });
});

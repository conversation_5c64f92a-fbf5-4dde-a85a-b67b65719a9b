/* eslint-disable @typescript-eslint/no-explicit-any */
import { DOCUMENT } from '@angular/common';
import { RendererFactory2 } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { NotificationType, StaticMessageItem, TranslateService } from '@klicktipp/kt-mat-library';
import { FetchTranslationsService } from './fetch-translations.service';

describe('FetchTranslationsService', () => {
  let service: FetchTranslationsService;

  let mockDocument: any;
  let mockActivatedRoute: any;
  let mockTranslateService: jest.Mocked<TranslateService>;
  let mockRendererFactory: any;
  let mockRenderer: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock instances
    mockDocument = {
      defaultView: {
        navigator: {
          language: 'en-US'
        },
        kt_translations: {}
      },
      body: {
        appendChild: jest.fn()
      }
    };

    mockActivatedRoute = {
      snapshot: {
        queryParams: {}
      }
    };

    mockTranslateService = {
      load: jest.fn()
    } as unknown as jest.Mocked<TranslateService>;

    mockRenderer = {
      createElement: jest.fn().mockReturnValue({
        type: '',
        src: ''
      }),
      appendChild: jest.fn()
    };

    mockRendererFactory = {
      createRenderer: jest.fn().mockReturnValue(mockRenderer)
    };

    TestBed.configureTestingModule({
      providers: [
        FetchTranslationsService,
        { provide: DOCUMENT, useValue: mockDocument },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: TranslateService, useValue: mockTranslateService },
        { provide: RendererFactory2, useValue: mockRendererFactory }
      ]
    });

    service = TestBed.inject(FetchTranslationsService);
  });

  it('should be created', () => {
    expect(service).toBeInstanceOf(FetchTranslationsService);
  });

  describe('getLanguageKey', () => {
    it('should return language from query params if available', () => {
      mockActivatedRoute.snapshot.queryParams.lang = 'de';
      const lang = service.getLanguageKey();
      expect(lang).toBe('de');
    });

    it('should return navigator language if query param is not set', () => {
      mockActivatedRoute.snapshot.queryParams = {};
      mockDocument.defaultView.navigator.language = 'en-US';
      const lang = service.getLanguageKey();
      expect(lang).toBe('en-gb');
    });

    it('should default to en-gb if unsupported language is detected', () => {
      mockActivatedRoute.snapshot.queryParams = {};
      mockDocument.defaultView.navigator.language = 'fr-FR';
      const lang = service.getLanguageKey();
      expect(lang).toBe('en-gb');
    });

    it('should handle language codes with region tags', () => {
      mockActivatedRoute.snapshot.queryParams = {};
      mockDocument.defaultView.navigator.language = 'de-DE';
      const lang = service.getLanguageKey();
      expect(lang).toBe('de');
    });
  });

  describe('injectScript', () => {
    it('should create and append script element to document body', () => {
      const lang = 'en-us';
      service.injectScript(lang);

      expect(mockRenderer.createElement).toHaveBeenCalledWith('script');
      expect(mockRenderer.appendChild).toHaveBeenCalled();
    });

    it('should set correct script attributes', () => {
      const lang = 'en-us';
      const mockScriptElement = { type: '', src: '' };
      mockRenderer.createElement.mockReturnValue(mockScriptElement);
      service.injectScript(lang);
      expect(mockScriptElement.type).toBe('text/javascript');
      expect(mockScriptElement.src).toBe(`/angular/translation/${lang}`);
    });
  });

  describe('loadTranslations', () => {
    it('should set isLoading$ to true and isError$ to false', () => {
      service.loadTranslations();
      expect(service.isLoading$.value).toBe(true);
      expect(service.isError$.value).toBe(false);
    });

    it('should load translations if already available', () => {
      const lang = 'en-us';
      jest.spyOn(service, 'getLanguageKey').mockReturnValue(lang);
      mockDocument.defaultView.kt_translations[lang] = {};

      service.loadTranslations();

      expect(mockTranslateService.load).toHaveBeenCalledWith(lang);
      expect(service.isLoading$.value).toBe(false);
    });

    it('should inject script and wait for translations if not available', () => {
      const lang = 'en-us';
      jest.spyOn(service, 'getLanguageKey').mockReturnValue(lang);
      jest.spyOn<any, any>(service, 'waitForTranslations').mockImplementation(() => null);

      service.loadTranslations();

      expect(service.isLoading$.value).toBe(true);
      expect(service.isError$.value).toBe(false);
      expect(mockRenderer.createElement).toHaveBeenCalledWith('script');
      expect(mockRenderer.appendChild).toHaveBeenCalled();
      expect(service['waitForTranslations']).toHaveBeenCalledWith(lang);
    });
  });

  describe('waitForTranslations', () => {
    it('should set error after max wait cycles', () => {
      const lang = 'en-us';
      service['waitCyles'] = 19; // Set to one less than max
      jest.spyOn<any, any>(service, 'waitForTranslations');

      service['waitForTranslations'](lang);

      expect(service['waitCyles']).toBe(20);
      expect(service.isError$.value).toEqual({
        type: NotificationType.Danger,
        nodeId: 'load-translations-error',
        message: 'Cannot load translations. Please try again later.'
      } as StaticMessageItem);
      expect(service.isLoading$.value).toBe(false);
    });

    it('should retry if translations are not available', () => {
      const lang = 'en-us';
      service['waitCyles'] = 0;
      jest.useFakeTimers();

      service['waitForTranslations'](lang);

      // Fast-forward time
      jest.advanceTimersByTime(950);

      // Since translations are still not available, it should call waitForTranslations again
      expect(service['waitCyles']).toBe(4);

      jest.useRealTimers();
    });

    it('should load translations when available', () => {
      const lang = 'en-us';
      service['waitCyles'] = 0;
      jest.useFakeTimers();

      // Mock translations becoming available after 500ms
      setTimeout(() => {
        mockDocument.defaultView.kt_translations[lang] = {};
      }, 500);

      service['waitForTranslations'](lang);

      // Fast-forward time
      jest.advanceTimersByTime(500);

      expect(mockTranslateService.load).toHaveBeenCalledWith(lang);
      expect(service.isLoading$.value).toBe(false);

      jest.useRealTimers();
    });
  });

  describe('currentTranslations getter', () => {
    it('should return current translations from document', () => {
      mockDocument.defaultView.kt_translations = { 'en-us': {} };
      expect(service.currentTranslations).toEqual({ 'en-us': {} });
    });

    it('should handle undefined defaultView', () => {
      mockDocument.defaultView = undefined;
      expect(service.currentTranslations).toBeUndefined();
    });
  });
});

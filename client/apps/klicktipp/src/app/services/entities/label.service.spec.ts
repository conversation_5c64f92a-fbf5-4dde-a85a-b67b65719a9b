/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpClient, provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { DestroyRef } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { Dictionary } from '@ngrx/entity';
import { Store } from '@ngrx/store';
import { provideMockStore } from '@ngrx/store/testing';
import { MockProvider } from 'ng-mocks';
import { of, Subject } from 'rxjs';
import { CacheFacade } from '../../+state/cache/cache.facade';
import { LabelCacheFacade } from '../../+state/cache/label/label-cache.facade';
import { LabelEntity } from '../../models/entities/labels/LabelEntity';
import { RouteParameterService } from '../route-parameter.service';
import { LabelService } from './label.service';

describe('LabelService', () => {
  let service: LabelService;
  let cacheFacade: CacheFacade;
  let httpClient: HttpClient;
  let store: Store;

  const labelAllSubject = new Subject<LabelEntity[]>();
  const labelEntitiesSubject = new Subject<Dictionary<LabelEntity>>();
  const labelLoadedSubject = new Subject<boolean>();

  beforeEach(async () => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        MockProvider(LabelCacheFacade, {
          labelLoaded$: labelLoadedSubject.asObservable(),
          labelAll$: labelAllSubject.asObservable(),
          labelEntities$: labelEntitiesSubject.asObservable()
        }),
        MockProvider(DestroyRef),
        MockProvider(RouteParameterService)
      ]
    });

    service = TestBed.inject(LabelService);
    cacheFacade = TestBed.inject(CacheFacade);
    httpClient = TestBed.inject(HttpClient);
    store = TestBed.inject(Store);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getLabels', () => {
    it('should call retrieveLabels', (done) => {
      const mockLabels: LabelEntity[] = [{ id: '1', name: 'Test Label', type: 'Type1', description: 'Description' }];
      const retrieveSpy = jest.spyOn(service as any, 'retrieveLabels').mockImplementation(() => of(mockLabels));

      service.getLabels().subscribe((labels) => {
        expect(retrieveSpy).toHaveBeenCalled();
        expect(labels).toEqual(mockLabels);
        done();
      });

      labelLoadedSubject.next(false);
    });

    it('should return the entities directly without calling retrieveLabels', (done) => {
      const mockLabels: LabelEntity[] = [{ id: '1', name: 'Test Label', type: 'Type1', description: 'Description' }];
      const retrieveSpy = jest.spyOn(service as any, 'retrieveLabels').mockImplementation(() => of([]));

      service.getLabels().subscribe((labels) => {
        expect(retrieveSpy).not.toHaveBeenCalled();
        expect(labels).toEqual(mockLabels);
        done();
      });

      labelLoadedSubject.next(true);
      labelAllSubject.next(mockLabels);
    });
  });

  describe('refreshLabels', () => {
    it('should return loaded labels', (done) => {
      const mockLabels: LabelEntity[] = [{ id: '1', name: 'Test Label', type: 'Type1', description: 'Description' }];
      const resetLabelsSpy = jest.spyOn(cacheFacade.label, 'resetLabels');
      const postSpy = jest.spyOn(httpClient, 'post').mockReturnValue(of({ data: mockLabels }));
      const dispatchSpy = jest.spyOn(store, 'dispatch').mockImplementation(() => null);

      service.refreshLabels().subscribe((labels) => {
        expect(resetLabelsSpy).toHaveBeenCalled();
        expect(postSpy).toHaveBeenCalled();
        expect(dispatchSpy).toHaveBeenCalled();
        expect(labels).toEqual(mockLabels);
        done();
      });

      labelLoadedSubject.next(true);
    });
  });

  describe('filterLabelsByTypeAndConvert', () => {
    it('should return filtered labels as options', (done) => {
      const mockLabels: LabelEntity[] = [
        { id: '1', name: 'Test Label 1', type: 'Type1', description: 'Description 1' },
        { id: '2', name: 'Test Label 2', type: 'Type2', description: 'Description 2' },
        { id: '3', name: 'Test Label 3', type: 'Type2', description: 'Description 3' },
        { id: '4', name: 'Test Label 4', type: 'Type3', description: 'Description 4' }
      ];
      const itemType = 'Type2';

      service.filterLabelsByTypeAndConvert(itemType).subscribe((options) => {
        expect(options).toEqual([
          { value: '2', text: 'Test Label 2' },
          { value: '3', text: 'Test Label 3' }
        ]);
        done();
      });

      labelAllSubject.next(mockLabels);
    });

    it('should return all labels as aoptions', (done) => {
      const mockLabels: LabelEntity[] = [
        { id: '1', name: 'Test Label 1', type: 'Type1', description: 'Description 1' },
        { id: '2', name: 'Test Label 2', type: 'Type2', description: 'Description 2' },
        { id: '3', name: 'Test Label 3', type: 'Type2', description: 'Description 3' },
        { id: '4', name: 'Test Label 4', type: 'Type3', description: 'Description 4' }
      ];
      const itemType = '';

      service.filterLabelsByTypeAndConvert(itemType).subscribe((options) => {
        expect(options).toEqual([
          { value: '1', text: 'Test Label 1' },
          { value: '2', text: 'Test Label 2' },
          { value: '3', text: 'Test Label 3' },
          { value: '4', text: 'Test Label 4' }
        ]);
        done();
      });

      labelAllSubject.next(mockLabels);
    });
  });

  describe('getMagicSelectOptions', () => {
    it('should return filtered labels as options', (done) => {
      const labelsDictonary = {
        1: { id: '1', name: 'Test Label 1', type: 'Type1', description: 'Description 1' },
        2: { id: '2', name: 'Test Label 2', type: 'Type2', description: 'Description 2' },
        3: { id: '3', name: 'Test Label 3', type: 'Type2', description: 'Description 3' },
        4: { id: '4', name: 'Test Label 4', type: 'Type3', description: 'Description 4' }
      };
      const items = ['2', '3', '9'];

      service.getMagicSelectOptions(items).subscribe((options) => {
        expect(options).toEqual([
          { value: '2', text: 'Test Label 2' },
          { value: '3', text: 'Test Label 3' }
        ]);
        done();
      });

      labelEntitiesSubject.next(labelsDictonary);
    });
  });
});

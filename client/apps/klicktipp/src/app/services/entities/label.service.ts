import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { MagicSelectChipItem } from '@klicktipp/kt-mat-library';
import { Store } from '@ngrx/store';
import { asapScheduler, Observable, of } from 'rxjs';
import { filter, finalize, map, observeOn, switchMap, take, tap } from 'rxjs/operators';
import { CacheFacade } from '../../+state/cache/cache.facade';
import { setLabelCache } from '../../+state/cache/label/label-cache.actions';
import { LabelData, LabelEntity } from '../../models/entities/labels/LabelEntity';
import { BaseApi } from '../api/base-api.abstract';
import { RouteParameterService } from '../route-parameter.service';

@Injectable({ providedIn: 'root' })
export class LabelService extends BaseApi {
  private readonly store = inject(Store);
  private readonly cacheFacade = inject(CacheFacade);
  protected subPath = `kt-meta-labels`;

  private isPending = false;

  constructor(
    private httpClient: HttpClient,
    private routingService: RouteParameterService
  ) {
    super();
  }

  refreshLabels(): Observable<LabelEntity[]> {
    this.cacheFacade.label.resetLabels();
    if (!this.isPending) {
      return this.retrieveLabels();
    }
    return this.getFromStateWhenLoaded();
  }

  getLabels(): Observable<LabelEntity[]> {
    if (this.isPending) {
      return this.getFromStateWhenLoaded();
    }
    return this.cacheFacade.label.labelLoaded$.pipe(
      switchMap((isLoaded) => {
        return isLoaded ? this.cacheFacade.label.labelAll$ : this.retrieveLabels();
      }),
      take(1)
    );
  }

  filterLabelsByTypeAndConvert(itemType: string | number): Observable<MagicSelectChipItem[]> {
    return this.cacheFacade.label.labelAll$.pipe(
      take(1),
      map((entities) => {
        if (!itemType) {
          return entities.map((t) => {
            return { value: t.id.toString(), text: t.name };
          });
        }
        return entities
          .filter((t) => t.type === itemType)
          .map((t) => {
            return { value: t.id.toString(), text: t.name };
          });
      })
    );
  }

  getMagicSelectOptions(items: string[]): Observable<MagicSelectChipItem[]> {
    if (!items || items.length === 0) {
      return of([]);
    }
    return this.cacheFacade.label.labelEntities$.pipe(
      take(1),
      map((entities) => {
        const result = [];
        if (entities) {
          for (const tagId of items) {
            const tag = entities[tagId];
            if (tag) {
              result.push({ value: tag.id, text: tag.name });
            }
          }
        }
        return result;
      })
    );
  }

  private retrieveLabels(): Observable<LabelEntity[]> {
    this.isPending = true;
    const options = this.getoptions();
    const url = `${this.apiPath}/${this.subPath}/index-advanced/${this.routingService.userId}`;
    return this.httpClient.post<LabelData>(url, null, options).pipe(
      observeOn(asapScheduler),
      map(({ data }) => data),
      tap((entities) => this.store.dispatch(setLabelCache({ entities }))),
      take(1),
      finalize(() => (this.isPending = false))
    );
  }

  private getFromStateWhenLoaded(): Observable<LabelEntity[]> {
    return this.cacheFacade.label.labelLoaded$.pipe(
      filter(Boolean),
      switchMap(() => this.cacheFacade.label.labelAll$),
      take(1)
    );
  }
}

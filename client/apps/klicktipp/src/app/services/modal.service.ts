import { Overlay } from '@angular/cdk/overlay';
import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';
import { faTrash } from '@fortawesome/pro-light-svg-icons';
import { faTag, faTimes } from '@fortawesome/pro-regular-svg-icons';
import { KtModalComponent, ModalData, ModalReturnData, TranslateService } from '@klicktipp/kt-mat-library';
import { Observable } from 'rxjs';
import { EntityDependencyItems } from '../modules/forms.shared/models/shared-form.interfaces';
import { LandingPageAddonStatus } from '../modules/landing-page/landing-page-settings/models/landing-page-base';

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  constructor(
    private matDialog: MatDialog,
    private overlay: Overlay,
    private translateService: TranslateService
  ) {}

  openInfoModal(modalData: ModalData): Observable<ModalReturnData> {
    modalData.modalType = 'info';
    this.setDefaultValues(modalData);

    return this.matDialog
      .open(KtModalComponent, {
        data: modalData,
        panelClass: 'kt-mat-library',
        scrollStrategy: this.overlay.scrollStrategies.noop()
      })
      .afterClosed();
  }

  openWarnModal(modalData: ModalData): Observable<ModalReturnData> {
    modalData.modalType = 'warning';
    modalData.buttonColor = modalData.buttonColor ? modalData.buttonColor : 'warn';
    this.setDefaultValues(modalData);

    return this.matDialog
      .open(KtModalComponent, {
        data: modalData,
        panelClass: 'kt-mat-library',
        scrollStrategy: this.overlay.scrollStrategies.noop()
      })
      .afterClosed();
  }

  // use ref to get a reference of the dialog instead of closing result, e.g. to update data while dialog is open
  openInfoModalRef(modalData: ModalData): MatDialogRef<KtModalComponent> {
    modalData.modalType = 'info';
    this.setDefaultValues(modalData);

    return this.matDialog.open(KtModalComponent, {
      data: modalData,
      panelClass: 'kt-mat-library',
      scrollStrategy: this.overlay.scrollStrategies.noop()
    });
  }

  openWarnModalRef(modalData: ModalData): MatDialogRef<KtModalComponent> {
    modalData.modalType = 'warning';
    modalData.buttonColor = modalData.buttonColor ? modalData.buttonColor : 'warn';
    this.setDefaultValues(modalData);

    return this.matDialog.open(KtModalComponent, {
      data: modalData,
      panelClass: 'kt-mat-library',
      scrollStrategy: this.overlay.scrollStrategies.noop()
    });
  }

  createDefaultDeleteWithDependencyCheckModal(
    dependencies: EntityDependencyItems[],
    modalTitle: string,
    modalMessage: string,
    entityName: string,
    isAddOnPlus: LandingPageAddonStatus = null
  ): ModalData {
    let message: string;
    let highlightMessage: string;
    let disablePrimaryButton = false;
    if (dependencies?.length > 0) {
      message = this.translateService.translate(modalMessage, { deps: this.createDepStrings(dependencies) });
      highlightMessage = '';
      disablePrimaryButton = true;
    } else {
      message = this.translateService.translate('Campaign::Delete::Modal::Are you sure to delete @@name@@?', { name: entityName });
      highlightMessage = this.translateService.translate('Campaign::Delete::Modal::Note: This action can not be undone!');
      if (isAddOnPlus !== null && isAddOnPlus !== LandingPageAddonStatus.Free) {
        highlightMessage = this.translateService.translate('Landingpage::Delete::Modal::Note: This action can not be undone - delete Paid LP subscription!');
      }
    }
    window['e2eReplay'].waitResolve('waitForNextResolve');
    return {
      id: 'deleteModal',
      messageIsHtml: true,
      message,
      highlightMessage,
      title: modalTitle,
      primaryButtonText: this.translateService.translate('Campaign::Delete::Modal::Button::Delete'),
      secondaryButtonText: this.translateService.translate('Campaign::Delete::Modal::Button::Cancel'),
      confirmIcon: faTrash as IconProp,
      disablePrimary: disablePrimaryButton
    };
  }

  private createDepStrings(dependencies: EntityDependencyItems[]): string {
    let result = '<br><br>';
    for (const dependency of dependencies) {
      result += `${dependency.category}:`;
      result += '<ul class="dependency-modal-list">';
      for (const dep of dependency.dependencies) {
        result += `<li><a id="${dep.id}" data-url="${dep.editLink}" >${dep.name}</a></li>`;
      }
      result += '</ul>';
    }
    return result;
  }

  private setDefaultValues(modalData: ModalData): void {
    modalData.message = modalData.message ? modalData.message : '';
    modalData.modalType = modalData.modalType ? modalData.modalType : 'info';
    // keep ts compiler silent for icons => can't resolve conditional syntax with iconprop?!
    if (!modalData.closeIcon) {
      modalData.closeIcon = faTimes as IconProp;
    }
    if (!modalData.confirmIcon) {
      modalData.confirmIcon = faTag as IconProp;
    }
    if (!modalData.titleIcon) {
      modalData.titleIcon = faExclamationTriangle as IconProp;
    }
    modalData.buttonColor = modalData.buttonColor ? modalData.buttonColor : 'primary';
    modalData.closeButtonVisible = modalData.closeButtonVisible ? modalData.closeButtonVisible : true;
    modalData.disableClose = modalData.disableClose ? modalData.disableClose : false;
    modalData.baseThemeClass = modalData.baseThemeClass ? `mat-typography kt-mat-library ${modalData.baseThemeClass}` : `mat-typography kt-mat-library`;
    modalData.themeClass = modalData.themeClass ? `mat-typography kt-drupal-theme ${modalData.themeClass}` : 'mat-typography kt-drupal-theme';
    modalData.messageIsHtml = true;
  }
}

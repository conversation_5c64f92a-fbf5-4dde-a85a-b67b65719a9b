import { Injectable, OnDestroy } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, NavigationEnd, Params, Router } from '@angular/router';
import { AffixItem, KtMessageService, KtNavigationService, LinkItem } from '@klicktipp/kt-mat-library';
import { Subject, Subscription } from 'rxjs';
import { filter, startWith } from 'rxjs/operators';

/** @deprecated use `RouterFacade` instead */
@Injectable({ providedIn: 'root' })
export class RouteParameterService implements OnDestroy {
  get campaignId(): string {
    return this.internalCampaignId;
  }

  set campaignId(campaignId: string) {
    if (!campaignId) {
      campaignId = '';
    }
    this.internalCampaignId = campaignId;
    this.ktNavigationService.campaignId = this.internalCampaignId;
  }

  get emailId(): number {
    return this.internalEmailId;
  }

  set emailId(id: number) {
    this.internalEmailId = id;
  }

  get userId(): string {
    return this.internalUserId;
  }

  set userId(userId: string) {
    if (!userId) {
      return;
    }
    this.internalUserId = userId;
  }

  get currentId(): number {
    return this.internalCurrentId;
  }

  set currentId(currentId: number) {
    if (!currentId) {
      currentId = 0;
      this.setItemName('');
    }
    this.internalCurrentId = currentId;
  }

  get copyId(): number {
    return this.internalCopyId ?? 0;
  }

  set copyId(copyId: number) {
    this.internalCopyId = copyId ?? 0;
  }

  // as customfields have strings as ids we need a special solution to prevent typing errors in all other cases
  get customfieldId(): string {
    return this.internalCustomfieldId;
  }

  set customfieldId(customfieldId: string) {
    if (!customfieldId) {
      customfieldId = '0';
    }
    this.internalCustomfieldId = customfieldId;
  }

  get affixView(): string {
    return this.internalAffixView;
  }

  set affixView(view: string) {
    if (!view) {
      this.internalAffixView = '';
    }
    this.internalAffixView = view;
  }

  set currentAffixLinks(links: LinkItem[] | AffixItem[]) {
    this.translateAffixMap = new Map<string[], string>();
    for (const link of links) {
      if (Object.prototype.hasOwnProperty.call(link, 'key')) {
        const linkItem = link as LinkItem;
        this.translateAffixMap.set(linkItem.key, linkItem.linkText);
      } else {
        const linkItem = link as AffixItem;
        this.translateAffixMap.set([linkItem.id], linkItem.title);
      }
    }
    this.routeChanged.next(this.currentUrl);
  }

  addonentity: string;

  subscriptions: Subscription = new Subscription();
  routeChanged: Subject<string> = new Subject<string>();

  currentItemName: string;
  currentUrl: string;
  type: string;
  translateAffixMap: Map<string[], string>;

  protected internalUserId: string;
  protected internalCurrentId: number;
  protected internalCustomfieldId: string;
  protected internalCampaignId: string;
  protected internalCopyId: number;
  protected internalAffixView: string;
  protected internalEmailId: number;

  constructor(
    private messageService: KtMessageService,
    private ktNavigationService: KtNavigationService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    this.currentId = -1;
    this.subscriptions.add(
      this.router.events
        .pipe(
          filter((event) => event instanceof NavigationEnd),
          startWith(this.router)
        )
        .subscribe((event: NavigationEnd) => {
          this.handleParams(event.url, this.activatedRoute.snapshot);
          this.messageService.hideBar();
        })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  setItemName(name: string): void {
    this.currentItemName = name;
    this.routeChanged.next('');
  }

  getAffixTranslationForBr(key: string): string {
    if (this.translateAffixMap) {
      for (const mapKey of this.translateAffixMap.keys()) {
        if (mapKey.includes(key)) {
          return this.translateAffixMap.get(mapKey);
        }
      }
    }
    return '';
  }

  handleParams(url: string, route: ActivatedRouteSnapshot): boolean {
    // don't handle error pages as we need to show them no matter what happened before or afterwards
    if (url.includes('error/')) {
      return false;
    }
    const setMeRoute = false;
    this.messageService.hideBar();
    if (url && url !== '/') {
      this.currentId = -1;
      this.affixView = null;
      this.campaignId = null;
      this.emailId = null;
      this.copyId = null;
      this.type = null;
      const paramMap = this.getParamMap(route);
      if (paramMap) {
        this.addonentity = paramMap.get('addonentity');
        this.campaignId = paramMap.get('campaign');
        this.currentId = parseInt(paramMap.get('id'), 10); // current entity id
        this.emailId = paramMap.get('emailid');
        this.affixView = paramMap.get('view');
        this.copyId = paramMap.get('copyid');
        this.type = paramMap.get('type');
        this.customfieldId = paramMap.get('customfieldId');
        this.currentUrl = url;
        if (setMeRoute) {
          this.userId = 'me';
        } else {
          this.userId = paramMap.get('user');
        }
      } else {
        this.currentUrl = url;
        if (setMeRoute) {
          this.userId = 'me';
        }
      }
      this.routeChanged.next(this.currentUrl);
      return true;
    } else {
      this.userId = null;
    }
    return false;
  }

  getParamMap(route: ActivatedRouteSnapshot): Params | null {
    if (route.paramMap.keys.length && route.firstChild === null) {
      return route.paramMap;
    } else if (route.firstChild) {
      return this.getParamMap(route.firstChild);
    } else {
      return null;
    }
  }
}

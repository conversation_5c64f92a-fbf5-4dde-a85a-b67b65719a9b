import { createFeatureSelector, createSelector } from '@ngrx/store';
import { CACHE_FEATURE_KEY, CacheState } from '../cache.reducer';
import { cacheLabelEntityAdapter, LABELS_CACHE_FEATURE_KEY } from './label-cache.reducer';

const selectCache = createFeatureSelector<CacheState>(CACHE_FEATURE_KEY);
const selectCacheLabelSlice = createSelector(selectCache, (state) => state[LABELS_CACHE_FEATURE_KEY]);

const { selectAll: selectAllLabel, selectEntities: selectEntitiesLabel } = cacheLabelEntityAdapter.getSelectors();
export const selectCacheLabelAll = createSelector(selectCacheLabelSlice, (state) => selectAllLabel(state));
export const selectCacheLabelEntites = createSelector(selectCacheLabelSlice, (state) => selectEntitiesLabel(state));
export const selectCacheLabelLoaded = createSelector(selectCacheLabelSlice, (state) => state.loaded);

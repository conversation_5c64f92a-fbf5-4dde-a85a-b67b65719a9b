import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { select, Store } from '@ngrx/store';
import { Subject } from 'rxjs';
import { throttleTime } from 'rxjs/operators';
import { CacheResetThrottleTime } from '../cache.constants';
import * as CacheActions from './label-cache.actions';
import * as CacheSelectors from './label-cache.selectors';

@Injectable({ providedIn: 'root' })
export class LabelCacheFacade {
  private readonly destroyRef = inject(DestroyRef);
  private readonly store = inject(Store);

  labelAll$ = this.store.pipe(select(CacheSelectors.selectCacheLabelAll));
  labelEntities$ = this.store.pipe(select(CacheSelectors.selectCacheLabelEntites));
  labelLoaded$ = this.store.pipe(select(CacheSelectors.selectCacheLabelLoaded));

  private resetLabelTrigger = new Subject<void>();

  constructor() {
    this.resetLabelTrigger.pipe(throttleTime(CacheResetThrottleTime), takeUntilDestroyed(this.destroyRef)).subscribe(() => this.store.dispatch(CacheActions.resetLabelCache()));
  }

  resetLabels(): void {
    this.resetLabelTrigger.next();
  }
}

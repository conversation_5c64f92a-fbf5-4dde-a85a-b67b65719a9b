import { Component, DestroyRef, DOCUMENT, Inject, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TranslatePipe } from '@klicktipp/kt-mat-library';
import { interval, Subject } from 'rxjs';
import { exhaustMap, first } from 'rxjs/operators';

import { ConfigService } from '../../services/config.service';

@Component({
  selector: 'kt-helpbox',
  templateUrl: './helpbox.component.html',
  imports: [TranslatePipe]
})
export class HelpboxComponent implements OnInit {
  private readonly beaconSearchClickSubject = new Subject<void>();
  private readonly destroyRef = inject(DestroyRef);
  private readonly configService = inject(ConfigService);
  showBeacon = false;

  constructor(@Inject(DOCUMENT) private document: Document) {}

  ngOnInit(): void {
    const config = this.configService.getConfig();
    this.showBeacon = !!config?.menu?.beacon;
    this.beaconSearchClickSubject
      .pipe(
        exhaustMap(() => interval(80).pipe(first(() => !!this.document.defaultView.ktOpenSupportWidget))),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => this.document?.defaultView?.ktOpenSupportWidget?.());
  }

  openBeacon(event: Event): boolean {
    event.stopPropagation();
    this.beaconSearchClickSubject.next();
    return false;
  }
}

import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { BreadcrumbService } from './../../services/breadcrumb.service';
import { TabTitleService } from './../../services/tab-title.service';
import { Breadcrumb, KtBreadcrumbComponent, KtMessageComponent, KtNavigationService } from '@klicktipp/kt-mat-library';
import { Subscription } from 'rxjs';

@Component({
    templateUrl: './component-base.component.html',
    imports: [RouterOutlet, KtBreadcrumbComponent, KtMessageComponent]
})
export class ComponentBaseComponent implements OnInit, OnDestroy {
  breadcrumbs: Breadcrumb[] = [];
  private subscriptions = new Subscription();

  constructor(
    private activatedRoute: ActivatedRoute,
    private breadcrumbService: BreadcrumbService,
    protected ktNavigationService: KtNavigationService,
    private tabTitleService: TabTitleService
  ) {}

  ngOnInit(): void {
    this.breadcrumbs = this.breadcrumbService.breadcrumbs;
    this.subscriptions.add(
      this.breadcrumbService.breadcrumbsChanged$.subscribe((breadcrumbs: Breadcrumb[]) => {
        this.breadcrumbs = breadcrumbs;
      })
    );
    this.tabTitleService.setTabName(this.activatedRoute.snapshot.data['path']);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}

import { NgClass } from '@angular/common';
import { Component, effect, Inject, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, DOCUMENT } from '@angular/core';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { faBell, faCaretDown, faCaretRight } from '@fortawesome/free-solid-svg-icons';
import { faHeadset, faInfoCircle, faLifeRing } from '@fortawesome/pro-light-svg-icons';
import { faBars } from '@fortawesome/pro-regular-svg-icons';
import { hasSimpleChange, KtNavigationService, TranslatePipe } from '@klicktipp/kt-mat-library';
import { interval, Subject } from 'rxjs';
import { exhaustMap, first, takeUntil } from 'rxjs/operators';
import { Menu, MenuItem, MenuItemChild } from '../../models/entities/menu.interface';
import { ConfigService } from '../../services/config.service';
import { GlobalSignalsService } from '../../services/global-signals.service';
import { PopupCloserService } from '../../services/popup-closer.service';

@Component({
    selector: 'kt-menu',
    templateUrl: './menu.component.html',
    imports: [NgClass, FaIconComponent, TranslatePipe]
})
export class MenuComponent implements OnInit, OnDestroy, OnChanges {
  private readonly beaconSearchClickSubject = new Subject<void>();
  private readonly destroy$ = new Subject<void>();

  @Input() isAdmin: boolean;
  @Input() useConfig = true;
  @Input() menu: Menu;

  faCaretRight = faCaretRight;
  faCaretDown = faCaretDown;
  faBars = faBars;
  faSearch = faLifeRing;
  faBell = faBell;
  faHeadset = faHeadset;
  faInfo = faInfoCircle;

  isDesktop = false;
  showMenu = false;
  showBeamer: boolean;
  showBeacon: boolean;
  hasInfoBar: boolean;
  reducedMenu: boolean;

  constructor(
    private popupCloserService: PopupCloserService,
    private configService: ConfigService,
    private globalSignalsService: GlobalSignalsService,
    protected navigationService: KtNavigationService,
    @Inject(DOCUMENT) private document: Document
  ) {
    effect(() => {
      this.reducedMenu = this.globalSignalsService.reducedMenuTrigger();
    });
  }

  ngOnInit(): void {
    this.popupCloserService.close$.pipe(takeUntil(this.destroy$)).subscribe(() => this.menu?.items.forEach((i) => (i.open = false)));

    this.beaconSearchClickSubject
      .pipe(
        exhaustMap(() => interval(80).pipe(first(() => !!this.document.defaultView.ktOpenSupportWidget))),
        takeUntil(this.destroy$)
      )
      .subscribe(() => this.document.defaultView.ktOpenSupportWidget());

    if (this.useConfig) {
      const config = this.configService.getConfig();
      this.showBeamer = config.menu?.beamer;
      this.showBeacon = config.menu?.beacon;
      this.hasInfoBar = config.menu?.showOtherAccountInfo || config.menu?.showSupportInfo;
    }

    this.updateMenuPoints();
    // Our browser window for testing is 1042px wide, if we change this to for example 1200, there are tests failures
    this.isDesktop = this.document.defaultView.innerWidth > 1040;
    this.document.defaultView.addEventListener('resize', () => {
      this.isDesktop = this.document.defaultView.innerWidth > 1040;
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (hasSimpleChange(changes.menu)) {
      this.updateMenuPoints();
    }
  }

  onMenuItemClick(event: Event, item: MenuItem): void {
    const toggle = !item.open;
    this.menu.items.forEach((i) => (i.open = false));
    item.open = toggle;
    event.stopPropagation();
  }

  onBeaconSearchClick(event: Event): void {
    event.stopPropagation();
    this.beaconSearchClickSubject.next();
  }

  onToggle(): void {
    this.showMenu = !this.showMenu;
  }

  routeTo($event: PointerEvent, url: string): boolean {
    if ($event.ctrlKey || $event.metaKey || $event.shiftKey) {
      return true;
    }
    if (!url) {
      return true;
    }
    if ($event.target['href'] === url) {
      this.showMenu = false;
      return this.navigationService.routeTo(url);
    }
    return false;
  }

  goToCustomerMenu(): void {
    this.navigationService.routeTo(this.document.defaultView.location.origin + '/admin/user/customermenu');
  }

  goToMainPage(): void {
    this.navigationService.routeTo('/');
  }

  private handlePaths(item: MenuItemChild): void {
    if (item?.path === 'http://###beacon') {
      item.support = true;
    }
    if (item?.inAppLink) {
      item.path = item.inAppLink;
    }
  }

  private updateMenuPoints(): void {
    if (!this.menu || !this.menu.items) {
      return;
    }
    this.menu.items
      .filter((item) => !!item && !!item.children && Array.isArray(item.children))
      .forEach((item) =>
        item.children.forEach((child: MenuItemChild) => {
          this.handlePaths(child);
          child?.children?.forEach((childchild) => this.handlePaths(childchild));
        })
      );
  }
}

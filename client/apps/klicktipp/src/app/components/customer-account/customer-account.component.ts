import { Component } from '@angular/core';

import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons';

import { ConfigService } from './../../services/config.service';
import { Config } from './../../models/entities/config.entity';
import { TranslatePipe } from '@klicktipp/kt-mat-library';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';

@Component({
    selector: 'kt-customer-account',
    templateUrl: './customer-account.component.html',
    imports: [FaIconComponent, TranslatePipe]
})
export class CustomerAccountComponent {
  // TODO this is not used!!!!
  faExclamationTriangle = faExclamationTriangle;

  user: string;

  config: Config = this.configService.getConfig();

  constructor(private configService: ConfigService) {
    this.user = this.config.account.support;
  }

  goToClearCookie(): void {
    window.location.href = window.location.origin + '/admin/user/customermenu';
  }
}

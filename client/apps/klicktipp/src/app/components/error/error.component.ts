import { Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { TranslatePipe } from '@klicktipp/kt-mat-library';
import { filter } from 'rxjs/operators';
import { ErrorDetail, HandledError } from '../../models/error/error.interface';
import { HttpErrorService } from '../../services/http-error.service';
import { SentryService } from '../../services/sentry.service';

@Component({
  selector: 'kt-error',
  templateUrl: './error.component.html',
  imports: [TranslatePipe]
})
export class ErrorComponent {
  private readonly destroyRef = inject(DestroyRef);
  private readonly sentryService = inject(SentryService);

  error: ErrorDetail;
  showErrormessage = false;
  // we don't have config nor translations, so we need to show english message by default
  configError: boolean;

  constructor(
    private route: ActivatedRoute,
    private httpErrorService: HttpErrorService
  ) {
    this.route.data
      .pipe(
        filter(({ code }) => !!code),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((data) => {
        const code = +data.code;
        const error = this.httpErrorService.getNewErrorByStandardCode(code);
        this.setError(error);
      });

    this.route.paramMap
      .pipe(
        filter((params) => params.has('id')),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((params) => {
        const id = +params.get('id');
        if (isNaN(id)) {
          this.sentryService.captureMessage('ErrorComponent (kt-error) - id is not a number', { params });
          return this.setUnknownError();
        }

        const error = this.httpErrorService.getError(+id);
        if (error == null) {
          this.sentryService.captureMessage('ErrorComponent (kt-error) - unknown error occured', { params });
          return this.setUnknownError();
        }

        this.setError(error);
      });
  }

  supportButtonClick(): void {
    window.location.href = window.location.origin.replace('www', 'support').replace('app', 'support') + '/';
  }

  switchAccount(): void {
    window.location.href = window.location.origin + '/user/me/subaccount/switch';
  }

  private setError(error: HandledError): void {
    this.configError = error.configError;
    this.error = error;
    this.showErrormessage = !!this.error.message && this.error.message !== 'OK';
  }

  private setUnknownError(): void {
    this.configError = true;
    this.error = {
      header: 'Unexpected Error',
      message: "We're so sorry! Something unexpected happened. Please try again or contact support."
    };
    this.showErrormessage = true;
  }
}

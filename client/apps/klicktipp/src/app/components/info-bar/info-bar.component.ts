import { Component, OnInit } from '@angular/core';
import { ConfigService } from './../../services/config.service';
import { MenuSupport } from './../../models/entities/menu.interface';
import { TranslateService } from '@klicktipp/kt-mat-library';
import { Config } from './../../models/entities/config.entity';
import { NgClass } from '@angular/common';

@Component({
    selector: 'kt-info-bar',
    templateUrl: './info-bar.component.html',
    imports: [NgClass]
})
export class InfoBarComponent implements OnInit {
  supportData: MenuSupport;
  config: Config;
  userIDLabel = this.translateService.translate('infobar::label::UserId');
  emailLabel = this.translateService.translate('infobar::label::E-Mail');
  productIdLabel = this.translateService.translate('infobar::label::ProductId');
  tierLabel = this.translateService.translate('infobar::label::Tier');
  termLabel = this.translateService.translate('infobar::label::Term');
  groupIdLabel = this.translateService.translate('infobar::label::GroupId');
  amemberIdLabel = this.translateService.translate('infobar::label::AmemberId');
  subaccountUsernameLabel = this.translateService.translate('infobar::message::You are working in the account of');
  supportUsernameLabel = this.translateService.translate('infobar::message::Username');
  usernameLabel: string;
  spammerLabel = this.translateService.translate('infobar::label::Spammer');
  spammerStatus: string;

  constructor(
    private configService: ConfigService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    this.getConfig();
    this.configService.updateConfig.subscribe(() => {
      this.getConfig();
    });
  }

  private getConfig(): void {
    const config = this.configService.getConfig();
    this.supportData = config?.menu?.support;
    this.config = config;
    this.usernameLabel = config?.menu?.showSupportInfo ? this.supportUsernameLabel : this.subaccountUsernameLabel;
    this.spammerStatus = this.supportData?.spamActivity ? 'YES' : 'NO';
  }
}

import {animate, state, style, transition, trigger} from '@angular/animations';
import {CdkDragDrop, DragDropModule, moveItemInArray} from '@angular/cdk/drag-drop';
import {Breakpoints} from '@angular/cdk/layout';
import {ScrollingModule} from '@angular/cdk/scrolling';
import {NgClass, NgStyle, ViewportScroller} from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  EventEmitter,
  inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  QueryList,
  SimpleChanges,
  ViewChild,
  ViewChildren
} from '@angular/core';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import {FormControl, FormGroup, ReactiveFormsModule} from '@angular/forms';
import {MatCheckboxChange} from '@angular/material/checkbox';
import {MatPaginator, MatPaginatorModule, PageEvent} from '@angular/material/paginator';
import {MatSort, MatSortModule, Sort} from '@angular/material/sort';
import {MatTable, MatTableDataSource, MatTableModule} from '@angular/material/table';
import {faChevronDown, faChevronUp} from '@fortawesome/pro-solid-svg-icons';
import {isEqual} from 'lodash-es';
import {debounceTime, distinctUntilChanged, skip} from 'rxjs/operators';
import {DynamicCellDirective} from '../../directives/dynamic-cell.directive';
import {E2eIdAttributeDirective} from '../../directives/e2e-id-attribute.directive';
import {PaginatorDirective} from '../../directives/paginator.directive';
import {PaginatorSizeOptions} from '../../models/defaults';
import {CheckBoxItem, InputItem, SelectOption} from '../../models/form-helpers';
import {
  KtComponentType,
  TabelFormChanges,
  TableAlignTypes,
  TableBase,
  TableButtonClicked,
  TableCellComponent,
  TableCheckboxClicked,
  TableDefaultSortValue,
  TableDefaultSortValueItem,
  TableHeaderItem,
  TableRowData
} from '../../models/table';
import {TranslatePipe} from '../../pipes/translate.pipe';
import {KtBreakpointObserverService} from '../../services/kt-breakpoint-observer.service';
import {KtNavigationService} from '../../services/kt-navigation.service';
import {KtTableService} from '../../services/kt-table.service';
import {TranslateService} from '../../services/translate.service';
import {KtCheckboxComponent} from '../checkbox/kt-checkbox.component';
import {KtIconComponent} from '../icon/kt-icon.component';
import {KtInputComponent} from '../input/kt-input.component';
import {KtNotificationComponent} from '../notification/kt-notification.component';
import {KtSelectComponent} from '../select/kt-select.component';
import {KtSkeletonComponent} from '../skeleton-element/kt-skeleton-element.component';
import {KtTableCellComponent} from './table-cell/kt-table-cell.component';

@Component({
  selector: 'kt-table',
  templateUrl: './kt-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    ReactiveFormsModule,
    KtSelectComponent,
    KtInputComponent,
    MatTableModule,
    MatSortModule,
    E2eIdAttributeDirective,
    KtCheckboxComponent,
    NgStyle,
    KtIconComponent,
    DragDropModule,
    MatPaginatorModule,
    PaginatorDirective,
    TranslatePipe,
    MatTableModule,
    KtNotificationComponent,
    KtTableCellComponent,
    ScrollingModule,
    KtSkeletonComponent
  ],
  animations: [
    trigger('detailExpand', [
      state('collapsed,void', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)'))
    ])
  ]
})
export class KtTableComponent implements OnInit, AfterViewInit, OnChanges {
  protected readonly destroyRef = inject(DestroyRef);
  protected readonly faChevronUp = faChevronUp;
  protected readonly faChevronDown = faChevronDown;
  protected readonly KtComponentType = KtComponentType;
  private readonly translateService = inject(TranslateService);

  @Input() selectedElements: TableRowData[] = [];
  @Input() apiPager = false;
  @Input() currentPage!: number;
  @Input() pagerTotal!: number;
  @Output() buttonClicked: EventEmitter<TableButtonClicked> = new EventEmitter<TableButtonClicked>();
  @Output() checkboxClickedEvt: EventEmitter<TableCheckboxClicked> = new EventEmitter<TableCheckboxClicked>();
  @Output() statusFilterChanged: EventEmitter<SelectOption> = new EventEmitter<SelectOption>();
  @Output() tableRowDrop: EventEmitter<TableRowData[]> = new EventEmitter<TableRowData[]>();
  @Output() pagerUpdate: EventEmitter<PageEvent> = new EventEmitter<PageEvent>();
  @Output() sortChangedEmitter: EventEmitter<Sort> = new EventEmitter<Sort>();
  @Output() formChanges = new EventEmitter<TabelFormChanges>();
  @ViewChild(MatSort) sort: MatSort;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatTable) table: MatTable<TableBase>;
  @ViewChildren(DynamicCellDirective)
  cellPlaceholders!: QueryList<DynamicCellDirective>;

  @Input() set tableItem(data: TableBase) {
    // do a refresh only when data has changed, not on initial data
    const refresh = !!this.myTableItem;
    this.myTableItem = data;
    if (refresh) {
      this.regenerateTable();
    }
  }

  get tableItem(): TableBase {
    return this.myTableItem;
  }

  expandedElement: string;
  form: FormGroup = new FormGroup({});
  paginatorSizeOptions = PaginatorSizeOptions;

  dataSource: MatTableDataSource<TableRowData>;
  displayedColumns: string[] = [];
  resetPageIndex = false;

  inputField: InputItem;

  selectAllCb: CheckBoxItem;
  loading = true;
  loadingTable = this.translateService.translate('table::preparetabletext::loading');
  viewHeight = 200;

  private headerKeysToFilter: string[];
  private currentSortSettings: TableDefaultSortValueItem;
  private myTableItem: TableBase;
  private lastEvent: PageEvent;

  constructor(
    public ktTableService: KtTableService,
    private ktBreakpointObserverService: KtBreakpointObserverService,
    public ktNavigationService: KtNavigationService,
    private cdr: ChangeDetectorRef,
    private viewportScroller: ViewportScroller
  ) {}

  ngOnInit(): void {
    this.ktBreakpointObserverService
      .getBreakpointObserver(Breakpoints.Medium)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((_result) => {
        this.toggleTableColumns(this.form?.get(`${this.tableItem.id}-statusSelect`)?.value?.value);
      });
    this.createDefaultFormItems();
    this.dataSource = new MatTableDataSource<TableRowData>([]);
    this.displayedColumns = this.tableItem.headers.map((val) => val.key);
    this.currentSortSettings = this.tableItem.defaultSortValue ?? {
      default: { name: 'name', direction: 'asc' }
    };
  }

  ngAfterViewInit(): void {
    this.viewHeight = 400;
    this.regenerateTable();
    setTimeout(() => {
      this.loading = false;
      this.cdr.markForCheck();
      this.form.valueChanges
        .pipe(
          // skip first initial event
          skip(1),
          // make sure only real changes getting emitted
          distinctUntilChanged(isEqual),
          // debounce the changes for check all event, so that only the last change is processed
          debounceTime(150),
          takeUntilDestroyed(this.destroyRef)
        )
        .subscribe((value) => this.formChanges.emit(value));
      if (this.paginator && this.apiPager) {
        this.paginator.pageSize = this.currentPage;
        this.paginator.length = this.pagerTotal;
      }
    }, 100);
  }

  ngOnChanges({ currentPage, pagerTotal }: SimpleChanges): void {
    if (!this.paginator || !this.apiPager) {
      return;
    }
    if (currentPage) {
      this.paginator.pageIndex = currentPage.currentValue;
    }
    if (pagerTotal) {
      this.paginator.length = pagerTotal.currentValue;
    }
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  checkboxAllToggled($event: MatCheckboxChange): void {
    this.toggleAllCheckboxes($event.checked);
  }

  isNumber(value: unknown, column: TableHeaderItem): boolean {
    let result = typeof value === 'number';
    if (!result && value != '') {
      try {
        result = !isNaN(+value);
      } catch (e) {
        return false;
      }
    }
    // also set header align if necessary
    result = this.setHeaderAlign(result, column);
    return result;
  }

  sortChanged(sort: Sort): void {
    if (this.apiPager) {
      this.sortChangedEmitter.emit(sort);
      return;
    }
    const sortKey = this.preSelectFormControl()?.value?.value ?? 'default';
    const currentSortSettings: TableDefaultSortValueItem = {
      [sortKey]: {
        name: sort.active,
        direction: sort.direction ? sort.direction : 'desc'
      }
    };
    if (currentSortSettings[sortKey]?.name) {
      this.currentSortSettings[sortKey] = currentSortSettings[sortKey];
      this.ktTableService.currentSortSettings.next(currentSortSettings);
    }
  }

  onListDrop(event: CdkDragDrop<TableRowData[]>): void {
    const updatedData = [...this.dataSource.data];
    moveItemInArray(updatedData, event.previousIndex, event.currentIndex);
    this.dataSource.data = updatedData;
    this.dataSource.data[event.currentIndex].customRowCss += ' dragged-item';
    this.tableRowDrop.emit(this.dataSource.data);
    setTimeout(() => {
      this.table.renderRows();
      this.sort.disabled = false;
      this.cdr.markForCheck();
    }, 10);
  }

  dragStarted(): void {
    if (this.sort) {
      this.sort.active = null;
      this.sort.disabled = true;
    }
  }

  openExpand(element: TableRowData): void {
    this.expandedElement = this.expandedElement === element.id ? null : (element.id as string);
  }

  checkboxClicked($event: TableCheckboxClicked): void {
    this.checkboxClickedEvt.emit($event);
    this.form.get(this.selectAllCb.id).setValue(false);
  }

  pagerChanged(pagerChangedEvent: PageEvent): void {
    if (
      this.lastEvent?.pageIndex === pagerChangedEvent.pageIndex &&
      this.lastEvent?.pageSize === pagerChangedEvent.pageSize &&
      this.lastEvent?.length === pagerChangedEvent.length
    ) {
      return;
    }
    // only fire when apiPager is used AND it is not the initial call to the pager
    if (this.apiPager && this.lastEvent) {
      this.pagerUpdate.emit(pagerChangedEvent);
    }
    this.lastEvent = pagerChangedEvent;
    if(!this.apiPager){
      this.viewportScroller.scrollToAnchor('form-' + this.tableItem.id);
    }
  }

  private regenerateTable(): void {
    this.headerKeysToFilter = this.tableItem.headers.filter((h) => !h.filterBlock).map((h) => h.key);
    this.resetDataSource(this.tableItem.rows);
    this.handlePreSelectFilter();
    this.toggleTableColumns(this.preSelectFormControl()?.value?.value);
    this.cdr.detectChanges();
  }

  private toggleAllCheckboxes(checked: boolean): void {
    for (const row of this.tableItem.rows) {
      row.rowData.forEach((tableCellComponent: TableCellComponent) => {
        // ATTENTION: this only works as expected as there is only 1 checkbox configured in the data source
        if (tableCellComponent.componentType === KtComponentType.Checkbox && this.form.get(tableCellComponent.id)?.value !== checked) {
          this.form.get(`${tableCellComponent.key}-${tableCellComponent.id}`).setValue(checked);
          this.handleSelected(row, checked);
        }
      });
    }
  }

  private handleDataSource(dataSourceFilter: boolean): void {
    this.setTablePaginatorAndSort(dataSourceFilter, this.dataSource, this.paginator, this.sort);

    if (this.tableItem.hidePaginator || this.apiPager) {
      this.dataSource.paginator = null;
    }
    this.dataSource.filterPredicate = this.filterPredicate;
  }

  private setTablePaginatorAndSort(setDataSourceFilter: boolean, dataSource: MatTableDataSource<unknown>, paginator: MatPaginator, sort: MatSort): void {
    dataSource.paginator = paginator;
    dataSource.paginator.pageIndex = this.tableItem.pageIndex;
    // we can't support sort plus drag as this will interfere with each other
    // also don't use client-side sorting when using API pagination
    if (!this.tableItem.draggableRows && !this.apiPager) {
      dataSource.sort = sort;
      dataSource.sortData = this.sortData();
    }
    // default will be always sorted by name, use this to change it to another one
    if (setDataSourceFilter && !this.apiPager) {
      // force reset
      dataSource.sort?.sort({ id: null, start: 'desc', disableClear: true });
      if (this.currentSortSettings) {
        let defaultSortValue: TableDefaultSortValue;
        const value = this.preSelectFormControl()?.value;
        if (value && this.currentSortSettings[value?.value]) {
          defaultSortValue = this.currentSortSettings[value?.value];
        } else {
          if (this.currentSortSettings['default']) {
            defaultSortValue = this.currentSortSettings['default'];
          } else {
            defaultSortValue = { name: 'id', direction: 'desc' };
          }
        }
        if (this.displayedColumns.includes(defaultSortValue.name)) {
          this.dataSource.sort?.sort({
            id: defaultSortValue.name,
            start: defaultSortValue.direction,
            disableClear: true
          });
        } else {
          if (this.tableItem.defaultSortValue) {
            dataSource.sort?.sort({
              id: this.tableItem.defaultSortValue['default'].name,
              start: this.tableItem.defaultSortValue['default'].direction,
              disableClear: true
            });
          } else {
            dataSource.sort?.sort({
              id: defaultSortValue.name,
              start: defaultSortValue.direction,
              disableClear: true
            });
          }
        }
      } else {
        dataSource.sort?.sort({ id: 'name', start: 'asc', disableClear: true });
      }
    }
  }

  private filterPredicate = (data: TableRowData, filter: string): boolean => {
    let found = false;
    data.rowData.forEach((value: TableCellComponent) => {
      if (this.headerKeysToFilter.indexOf(value.column) > -1) {
        // foreach will not stop after return, so don't check other values if already true
        if (found) {
          return found;
        }
        found = value.text?.toString().toLowerCase().includes(filter);
      }
    });
    return found;
  };

  private sortData() {
    return (items: TableRowData[], sort: MatSort): TableRowData[] => {
      if (!sort.active || !this.displayedColumns.includes(sort.active) || sort.direction === '') {
        return items;
      }
      return this.ktTableService.sortTableItems(items, sort);
    };
  }

  private handlePreSelectFilter(): void {
    if (this.tableItem.preSelectFilter) {
      this.preSelectFormControl()
        ?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((value) => this.applyPreSelectFilter(value));
      setTimeout(() => {
        if (this.tableItem.defaultPreSelectValue) {
          for (const option of this.tableItem.preSelectFilter.options) {
            if (option.value?.toString() === this.tableItem.defaultPreSelectValue) {
              this.preSelectFormControl()?.setValue(option);
              this.resetPageIndex = true;
            }
          }
        } else {
          this.preSelectFormControl()?.setValue(this.tableItem.preSelectFilter?.options[0]);
        }
        this.cdr.markForCheck();
      });
    }
  }

  private applyPreSelectFilter(value: SelectOption): void {
    this.statusFilterChanged.next(value);
    const filter = value?.value;
    if (!filter) {
      this.resetDataSource(this.tableItem.rows);
      this.toggleTableColumns('');
      return;
    }
    this.toggleTableColumns(filter);
    const source: TableRowData[] = [];
    const splitFilterValues = this.ktTableService.splitFilterWithPipe(filter);
    this.tableItem.rows.forEach((tableRow) => {
      const rowStatusFilters = new Set(tableRow.statusFilterKey || []);
      const selectedFilterSet = new Set(splitFilterValues);
      const intersection = [...rowStatusFilters].filter((element) => selectedFilterSet.has(element));
      if (intersection.length) {
        source.push(tableRow);
      }
    });
    this.resetDataSource(source);
  }

  private resetDataSource(source: TableRowData[], dataSourceFilter = true): void {
    this.dataSource = new MatTableDataSource<TableRowData>([]);
    this.handleDataSource(dataSourceFilter);
    if (this.dataSource.paginator && this.resetPageIndex) {
      // First page is set only if the user changes the filter
      this.dataSource.paginator.firstPage();
    }
    this.dataSource.data = source;
  }

  private handleSelected(element: TableRowData, checked: boolean): void {
    if (checked) {
      this.selectedElements.push(element);
    } else {
      this.selectedElements.splice(this.selectedElements.indexOf(element), 1);
    }
  }

  private createDefaultFormItems(): void {
    this.inputField = {
      id: this.tableItem.id + '-filter',
      type: 'text',
      placeholder: 'Suche', // TODO: Replace with translate strings
      labelText: 'Suche' // TODO: Replace with translate strings
    };
    this.selectAllCb = { id: `all-checkbox` };
  }

  private toggleTableColumns(filter: string | number): void {
    if (!this.displayedColumns) {
      return;
    }
    let filteredColumns: TableHeaderItem[];
    if (!filter) {
      this.displayedColumns = this.tableItem.headers.map((val) => val.key);
      filteredColumns = this.tableItem.headers;
    } else {
      const splitFilterValues = this.ktTableService.splitFilterWithPipe(filter);
      filteredColumns = this.tableItem.headers.filter((h) => h.filterKey.includes(splitFilterValues[0]) || h.filterKey.includes('all'));
    }

    if (this.ktBreakpointObserverService.isXSmall()) {
      this.displayedColumns = filteredColumns
        .filter((c) => {
          return !c.breakpoint || this.ktBreakpointObserverService.getXSmallBreakpoints().includes(c.breakpoint);
        })
        .map((t) => t.key);
    } else if (this.ktBreakpointObserverService.isSmall()) {
      this.displayedColumns = filteredColumns
        .filter((c) => {
          return !c.breakpoint || this.ktBreakpointObserverService.getSmallBreakpoints().includes(c.breakpoint);
        })
        .map((t) => t.key);
    } else if (this.ktBreakpointObserverService.isMedium()) {
      this.displayedColumns = filteredColumns
        .filter((c) => {
          return !c.breakpoint || this.ktBreakpointObserverService.getMediumBreakpoints().includes(c.breakpoint);
        })
        .map((t) => t.key);
    } else {
      this.displayedColumns = filteredColumns.map((c) => c.key);
    }

    if (this.tableItem.expandedRows) {
      this.displayedColumns.push('expand');
    }

    this.cdr.markForCheck();
  }

  private setHeaderAlign(result: boolean, column: TableHeaderItem): boolean {
    result = result && column.key !== 'name' && (!column.align || column.align === TableAlignTypes.Right);
    if (result) {
      column.align = TableAlignTypes.Right;
    }
    return result;
  }

  private preSelectFormControl(): FormControl {
    return this.form.get(`${this.tableItem.id}-statusSelect`) as FormControl;
  }
}

import { DOCUMENT, NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, forwardRef, Inject, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { DateAdapter, MAT_DATE_FORMATS, MatNativeDateModule } from '@angular/material/core';
import { MatDatepicker, MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FaIconComponent } from '@fortawesome/angular-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faCalendarAlt } from '@fortawesome/pro-light-svg-icons';
import { E2eIdAttributeDirective } from '../../directives/e2e-id-attribute.directive';
import { hasSimpleChange } from '../../functions/has-simple-change';
import { MY_DATE_FORMATS } from '../../models/date-formats';
import { DatepickerData, SelectItem, SelectOption } from '../../models/form-helpers';
import { KtControlValueAccessorComponent } from '../control-value-accessor/control-value-accessor.component';
import { KtLabelComponent } from '../label/kt-label.component';
import { KtQuickhelpComponent } from '../quickhelp/kt-quickhelp.component';
import { KtSelectComponent } from '../select/kt-select.component';
import { ktDatepickerFormatter } from './kt-datepicker-formatter.function';

@Component({
  selector: 'kt-datepicker',
  templateUrl: './kt-datepicker.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MatDatepickerModule,
    MatNativeDateModule,
    ReactiveFormsModule,
    NgClass,
    E2eIdAttributeDirective,
    MatFormFieldModule,
    KtQuickhelpComponent,
    MatInputModule,
    FaIconComponent,
    KtLabelComponent,
    KtSelectComponent
  ],
  providers: [
    {
      provide: MAT_DATE_FORMATS,
      useValue: MY_DATE_FORMATS
    },
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: forwardRef(() => KtDatepickerComponent)
    }
  ]
})
export class KtDatepickerComponent extends KtControlValueAccessorComponent implements OnInit, OnChanges {
  @ViewChild('picker', { static: false }) picker: MatDatepicker<Date>;

  @Input() datepickerData: DatepickerData;

  icon: IconProp = faCalendarAlt;
  hours: SelectOption[] = [];
  minutes: SelectOption[] = [];
  selectItemHours: SelectItem;
  selectItemMinutes: SelectItem;
  hasTime: boolean;
  hasDate: boolean;

  constructor(
    private adapter: DateAdapter<unknown>,
    @Inject(DOCUMENT) private readonly document: Document
  ) {
    super();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.initComponent();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // only re-init when the component is already initialized and the data changes
    if (this.formControl && hasSimpleChange(changes?.datepickerData)) {
      this.initComponent();
    }
  }

  setDate(dateString: string, triggerEvent = true, beeForm = false): void {
    const date = this.createDateFromString(dateString, beeForm);
    if (this.hasDate) {
      this.value.set(date);
    }
    if (this.hasTime) {
      this.setTime(dateString, triggerEvent, beeForm);
    }
  }

  setTime(dateString: string, triggerEvent = true, beeForm = false): void {
    const date = this.createDateFromString(dateString, beeForm);
    this.formGroup.get(this.datepickerData.timePickerHoursFormName).setValue(
      this.hours.find((h) => h.value === date?.getHours()),
      { emitEvent: triggerEvent }
    );
    this.formGroup.get(this.datepickerData.timePickerMinutesFormName).setValue(
      this.minutes.find((h) => h.value === date?.getMinutes()),
      { emitEvent: triggerEvent }
    );
  }

  createDateFromString(dateString: string, beeForm = false): Date {
    if (!dateString && beeForm) {
      return null;
    }
    let date = new Date();
    const hoursMinutesRegex = /^\d{2}:\d{2}/;
    const hoursMinutesMatches = dateString.match(hoursMinutesRegex);
    if (dateString) {
      if (Date.parse(dateString)) {
        // format '2022-11-17 04:55:00'
        date = new Date(dateString);
      } else if (hoursMinutesMatches) {
        // format '04:55' or '04:55:00'
        const [hours, minutes] = dateString.split(':');
        date.setHours(+hours, +minutes, 0);
      }
    }
    return date;
  }

  getDateString(): string {
    const date = this.formControl.value;
    if (!date) {
      return '';
    }
    const month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : `${date.getMonth() + 1}`;
    const day = date.getDate() < 10 ? `0${date.getDate()}` : `${date.getDate()}`;
    return `${date.getFullYear()}-${month}-${day}`;
  }

  getTimeString(): string {
    const hours = this.formGroup.get(this.datepickerData.timePickerHoursFormName)?.value?.text;
    const minutes = this.formGroup.get(this.datepickerData.timePickerMinutesFormName)?.value?.text;
    if (hours && minutes) {
      return `${hours}:${minutes}`;
    } else {
      return '';
    }
  }

  getDatetimeString(forceEmpty = false, beeFormat = false): string {
    const formDate = this.formControl.value;
    const formHours = this.formGroup.get(this.datepickerData.timePickerHoursFormName)?.value?.text;
    const formMinutes = this.formGroup.get(this.datepickerData.timePickerMinutesFormName)?.value?.text;
    if (formDate && formHours && formMinutes) {
      const date = new Date(formDate);
      date.setHours(formHours, formMinutes);
      return ktDatepickerFormatter(date, beeFormat);
    } else {
      return forceEmpty ? null : beeFormat ? '01-01-2000T00:00' : '01-01-2000 00:00';
    }
  }

  getDatetimeStringBee(forceEmpty = false): string {
    return this.getDatetimeString(forceEmpty, true);
  }

  configureType(): void {
    this.hasTime = this.datepickerData.type.includes('time');
    this.hasDate = this.datepickerData.type.includes('date');
  }

  close(): void {
    this.picker?.close();
  }

  private createSelectItems(): void {
    const step = this.datepickerData.step ?? 1;
    if (this.hasTime) {
      for (let i = 0; i < 24; i++) {
        const text = i < 10 ? `0${i}` : i.toString();
        this.hours.push({ value: i, text });
        if (i % step === 0) {
          this.minutes.push({ value: i, text });
        }
      }
      for (let i = 24; i < 60; i++) {
        if (i % step === 0) {
          this.minutes.push({ value: i, text: i.toString() });
        }
      }
    }
    this.selectItemHours = {
      options: this.hours,
      selected: null,
      isDisabled: false,
      quickhelp: this.datepickerData.quickhelpTime,
      id: this.datepickerData.id + 'select-hours'
    };
    this.selectItemMinutes = {
      options: this.minutes,
      selected: null,
      isDisabled: false,
      id: this.datepickerData.id + 'select-min'
    };
  }

  private initComponent(): void {
    const language = this.document.documentElement.lang ?? 'de';
    this.adapter.setLocale(language);

    this.configureType();
    this.createSelectItems();
  }
}

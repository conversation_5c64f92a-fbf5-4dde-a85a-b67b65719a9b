/* eslint-disable @typescript-eslint/no-empty-function, @typescript-eslint/no-explicit-any */
import { ChangeDetectorRef, computed, DestroyRef, Directive, effect, inject, input, model, OnInit, untracked } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ControlContainer, FormArray, FormControl, FormGroup, ValidatorFn } from '@angular/forms';
import { KtControlValueAccessor } from '../../models/control-value-accessor';

/**
 * Base class for custom form controls designed to work with reactive and template driven forms.
 *
 * For reactive forms, the form control adds itself to the parent form group dynamically.
 * For template driven forms, the form control subscribes to value changes and updates the form control value.
 *
 * Components which extend this class can be used for both cases.
 *
 * @see Angular University Guide - https://blog.angular-university.io/angular-custom-form-controls/
 * @see Proof of Concept - https://github.com/JohnnyDevNull/ng-custom-form-control
 */
@Directive()
export abstract class KtControlValueAccessorComponent<T_VALUE = any> implements KtControlValueAccessor, OnInit {
  protected readonly destroyRef = inject(DestroyRef);
  protected readonly cdr = inject(ChangeDetectorRef);
  protected readonly controlContainer = inject(ControlContainer, { optional: true, skipSelf: true });

  get formGroup(): FormGroup | null {
    return this.controlContainer?.control as FormGroup;
  }

  touched = computed<boolean>(() => this.formControl?.touched ?? false);
  isDisabled = model<boolean>(false);
  value = model<T_VALUE>(null);
  formControlName = input<string | null>(null);
  formArrayName = input<string | null>(null);

  formControl: FormControl | FormArray;

  protected isFormArray = false;

  constructor() {
    effect(() => {
      // Disabled Changed, but just change the formControl disabled state if it's different
      if (this.formControl && this.isDisabled() !== this.formControl.disabled) {
        if (this.isDisabled()) {
          this.formControl.disable();
        } else {
          this.formControl.enable();
        }
      }
    });

    effect(() => {
      // Value Changed, but just change the formControl value if it's different
      if (this.formControl && this.value() !== null && this.value() !== this.formControl.value) {
        // We have to do the change untracked, because otherweise we will get NG0600 error
        // But then we have to manually trigger change detection
        untracked(() => {
          this.formControl.setValue(this.value());
          this.cdr.markForCheck();
        });
      }
    });
  }

  ngOnInit(): void {
    if (this.isReactiveForm()) {
      this.handleReactiveForm();
    } else {
      this.handleTemplateDrivenForm();
    }
  }

  onChange: (value: T_VALUE) => void = () => {};

  onTouched: () => void = () => {};

  writeValue(value: T_VALUE): void {
    // For reactive forms we use the formControl, so we don't need to set the value here
    // see this.handleReactiveForm()
    if (this.formControl && !this.isReactiveForm()) {
      // Template driven form [(ngModel)]
      // Because we're using a decoupled internal form Control, we need to update the value here for the NgForm
      this.onChange(value);
      this.formControl.setValue(value);
    }
  }

  registerOnChange(onChange: (value: T_VALUE) => void): void {
    this.onChange = onChange;
  }

  registerOnTouched(onTouched: () => void): void {
    this.onTouched = onTouched;
  }

  markAsTouched(): void {
    if (!this.touched()) {
      this.onTouched();
      this.formControl.markAsTouched();
    }
  }

  setValidators(validators: ValidatorFn | ValidatorFn[] | null): void {
    this.formControl.setValidators(validators);
  }

  protected isReactiveForm(): boolean {
    return !!(this.getFormControlName() && this.controlContainer?.control instanceof FormGroup);
  }

  protected formHasControl(form: FormGroup): boolean {
    return !!(this.getFormControlName() && form.contains(this.getFormControlName()));
  }

  /** Reactive form [formControlName] */
  private handleReactiveForm(): void {
    const parentFormGroup = this.formGroup;
    if (this.formHasControl(parentFormGroup)) {
      // The control is pre-configured - this is the angular default behavior
      this.formControl = parentFormGroup.get(this.getFormControlName()) as FormControl<T_VALUE>;
    } else {
      // Create a new control and register it to the form group - this is the klicktipp dynamic behavior
      this.formControl = this.isFormArray ? new FormArray([]) : new FormControl<T_VALUE>(this.value() ?? null);
      // this is needed if we add controls dynamically and form is already set to "disabled" state
      // otherwise a new control will change disabled state of the form to valid/invalid
      if (this.isDisabled() || parentFormGroup.status === 'DISABLED') {
        this.formControl.disable();
      }
      parentFormGroup.addControl(this.getFormControlName(), this.formControl);
    }
  }

  /** Template driven form [(ngModel)] */
  private handleTemplateDrivenForm(): void {
    this.formControl = new FormControl<T_VALUE>(this.value() ?? null);
    this.formControl.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((value) => this.onChange(value));
  }

  private getFormControlName(): string {
    return this.isFormArray ? this.formArrayName() : this.formControlName();
  }
}

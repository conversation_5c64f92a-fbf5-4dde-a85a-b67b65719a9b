@use './abstracts/_kt-palette-drupal';
@use './abstracts/_mixins';
@use '@angular/material' as mat;

h1 {
  font-size: 36px;
  font-weight: 500;
  margin-bottom: 20px;
}

h3 {
  font-size: 24px;
  font-weight: 500;
}

h1,
h3 {
  line-height: 1.1;
}

@include mixins.responsive(lt-md) {
  h1 {
    font-size: 24px;
  }

  h3 {
    font-size: 18px;
  }
}

.mat-mdc-tab-header {
  margin-bottom: 20px;
}

.box {
  width: 100%;
  border: 1px solid lightgray;
}

table,
th,
td,
tr {
  vertical-align: middle;
}

input,
textarea,
select {
  min-height: 34px;
}

.right {
  float: right;
}

.left {
  float: left;
}

.align-center {
  text-align: center !important;
}

.align-right {
  text-align: right !important;
}

.align-left {
  text-align: left !important;
}

.align-justify {
  text-align: justify !important;

  p {
    text-align: justify !important;
  }
}

.d-block {
  display: block;
}

d-inline-block {
  display: inline-block;
}

.text-success {
  color: mat.m2-get-color-from-palette(kt-palette-drupal.$kt-success, 700);
}

.text-warn {
  color: mat.m2-get-color-from-palette(kt-palette-drupal.$kt-warn, 600);
}

.text-error,
.text-danger {
  color: mat.m2-get-color-from-palette(kt-palette-drupal.$kt-error, 700);
}

b {
  font-weight: bold;
}

.clearAll {
  clear: both;
}

.kt-description-text {
  display: block;
  font-size: 12px;
  margin-bottom: 2px;
  margin-left: 2px;
  color: #737373;
  font-weight: normal;
}

.mat-mdc-autocomplete-panel.kt-mat-autocomplete,
.mat-mdc-select-panel {
  margin-top: -8px !important;
  margin-bottom: -12px !important;

  .mat-mdc-option {
    font-weight: normal;
    min-height: 32px;
  }
}

.noLabel .mat-mdc-autocomplete-panel.kt-mat-autocomplete,
.noLabel.mat-mdc-select-panel {
  margin-top: -12px !important;
}

.quickTippNotAtlabel .mat-mdc-autocomplete-panel.kt-mat-autocomplete,
.quickTippNotAtlabel.mat-mdc-select-panel {
  margin-top: -10px !important;
}

.mat-mdc-option {
  font-weight: normal;
  font-family: kt-palette-drupal.$global-font-family;
  letter-spacing: normal;
  font-size: 14px;
}

.mat-mdc-select-panel {
  .mat-mdc-option {
    min-height: 32px;
    line-height: 1.2;

    &:hover:not(.mat-list-item-disabled) {
      background: rgba(0, 0, 0, 0.04);
    }

    &:focus:not(.mdc-list-item--disabled),
    &.mat-mdc-option-active,
    &.mdc-list-item--selected:not(.mat-mdc-option-multiple):not(.mdc-list-item--disabled) {
      background: rgba(0, 0, 0, 0.18);
    }
  }
}

.kt-two-line-ellipsis,
.long.tags p.kt-two-line-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
  word-break: break-all;
}

/**CKEDITOR FULLSCREEN OVERWRITES**/
.cke_inner.cke_maximized {
  z-index: 1001 !important;
}

.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,
.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above {
  color: kt-palette-drupal.$dark-primary-text;
}

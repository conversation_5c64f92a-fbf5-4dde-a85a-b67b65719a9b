@forward '../../../library/abstracts/mixins';
@forward '../../../library/abstracts/kt-palette-drupal';
@use '../../../library/abstracts/mixins';
@use '../../../library/abstracts/kt-palette-drupal';

:host ::ng-deep {
  .mat-column-id .mat-sort-header-container {
    justify-content: flex-end;
  }

  .mat-column-id {
    width: 80px;
  }

  td.mat-column-name,
  td.mat-column-link {
    min-width: 250px;
    max-width: 350px;
  }

  td.mat-column-name {
    div:first-child {
      > a:first-child {
        @include mixins.responsive(lt-sm) {
          max-width: 150px;
        }
      }
    }
  }

  .kt-table-default {
    width: 396px;
    @include mixins.responsive(lt-sm) {
      width: 300px;
    }
  }
}

@use '../../../../../../core/abstracts/index';

form {
  input {
    min-height: auto;
  }

  input[type='text'] {
    width: 50%;
    @include index.responsive(tablet) {
      width: 100%;
    }
  }

  input[type='file'] {
    border: none;
    padding: 0;
  }

  label {
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 14px;
    color: index.color(ktold, dark);

    &.kt-label {
      font-weight: normal;
      vertical-align: bottom;
    }
  }

  .no-file-chosen,
  .import-file-name {
    color: #4a698d;
    margin-top: 18px;
    place-self: center;
    font-size: 14px;
    font-weight: bold;
    display: inline-flex;
    justify-content: center;
    @include index.responsive(tablet) {
      width: 100%;
    }
  }

  .import-upload-button-container {
    display: flex;
  }

  .uploadFileInput {
    display: inline-flex;
  }
}

.no-rows-text {
  font-size: 14px;
  padding: 15px;
  text-align: center;
}

:host::ng-deep {
  .mat-mdc-cell {
    border-left: 1px solid #dddddd;
    padding: 8px;
  }

  .mdc-data-table__row:last-child .mdc-data-table__cell {
    border-bottom: 1px solid #dddddd;
  }

  tr td.mat-mdc-cell:last-child {
    border-right: 1px solid #dddddd;
  }

  .mat-mdc-header-cell {
    white-space: nowrap;
  }
}

.kt-module {
  &-table {
    padding: 15px;
    overflow-x: scroll;
  }

  &-box {
    border: 1px solid index.color(ktold, whiteBorderLight);
    border-radius: 4px;
    width: 100%;

    &-header {
      background: index.color(ktold, grayLight);
      padding: 5px 10px;

      .headline {
        color: index.color(ktold, dark);
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
}

.import-preview-table::ng-deep {
  border-collapse: collapse;

  tbody td {
    border: 1px solid index.color(ktold, whiteBorderLight);
    max-width: 200px;
    min-width: 200px;
    text-overflow: ellipsis !important;
  }
}

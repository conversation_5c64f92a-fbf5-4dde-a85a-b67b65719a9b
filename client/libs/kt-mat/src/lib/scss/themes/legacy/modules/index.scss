@use 'sass:meta';
@include meta.load-css('addon/addon-standalone-dialog/addon-standalone-dialog.component');

@include meta.load-css('cacellation/components/cacelation-overview/cancellation-overview.component');

@include meta.load-css('conditionals.shared/components/decision/decision.component');

@include meta.load-css('content-creator/components/base-content-creator/base-content-creator.component');
@include meta.load-css('content-creator/components/smart-subject-base/smart-subject-base.component');
@include meta.load-css('content-creator/components/smart-subject-dialog/smart-subject-dialog.component');

@include meta.load-css('customer-dashboard/components/analytics-dashboard/analytics-dashboard.component');
@include meta.load-css('customer-dashboard/components/base-customer-dashboard/base-customer-dashboard.component');
@include meta.load-css('customer-dashboard/components/checklists/checklists.component');
@include meta.load-css('customer-dashboard/components/new-customer-dashboard/new-customer-dashboard.component');

@include meta.load-css('dev-tools/components/css-catalog/css-catalog.component');
@include meta.load-css('dev-tools/components/css-catalog-affix/css-catalog-affix.component');

@include meta.load-css('entities/components/entity-select/entity-select.component');

@include meta.load-css('import/components/import-assign-fields/import-assign-fields.component');
@include meta.load-css('import/components/import-denied/import-denied.component');
@include meta.load-css('import/components/import-init/import-init.component');
@include meta.load-css('import/components/import-list/import-list.component');
@include meta.load-css('import/components/import-report/import-report.component');
@include meta.load-css('import/components/import-verify/import-verify.component');

@include meta.load-css('label/components/label-overview/label-overview.component');

@include meta.load-css('plugin/components/plugin-create/plugin-create.component');
@include meta.load-css('plugin/components/plugin-overview/plugin-overview.component');

@include meta.load-css('signature/components/signature-overview/signature-overview.component');

@include meta.load-css('tool/components/tool-create/tool-create.component');
@include meta.load-css('tool/components/tool-overview/tool-overview.component');

@include meta.load-css('youtube/youtbe-search/components/search-base/youtube-analyzer-search.base.component');

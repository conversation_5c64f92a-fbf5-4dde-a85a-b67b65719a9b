@use '../../../../../../../../libs/kt-mat/src/lib/scss/abstracts/mixins';

:host ::ng-deep {
  ul li {
    list-style: none;
  }

  .svg-trend- {
    &neutral {
      svg {
        vertical-align: -0.225em;
        color: #fc960f;
      }
    }

    &down {
      svg {
        vertical-align: -0.225em;
        color: #de0b0b;
      }
    }

    &up {
      svg {
        vertical-align: -0.225em;
        color: #89b30b;
      }
    }
  }
}

.dashboard {
  &-chart {
    &-container {
      display: inline-flex;
      width: 100%;
    }

    &-linechart {
      div div {
        width: 100% !important;
      }

      flex: 2;
    }

    @include mixins.responsive(lt-lg) {
      &-container {
        display: flex;
        flex-direction: column;
      }
    }

    &-pies {
      flex: 1;
      display: inline-flex;
    }

    &-pie-isp,
    &-pie-bounces {
      flex: 1;
      align-items: center;
      justify-content: center;
      display: grid;
    }

    &-pie-subtext {
      font-size: 12px;
      text-align: center;
    }

    &-disable {
      opacity: 0.5;
    }
  }
}

svg > g > g:last-child {
  pointer-events: none;
}

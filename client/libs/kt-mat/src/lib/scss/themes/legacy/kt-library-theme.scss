@use '@angular/material' as mat;
@use 'sass:map';
@use '../../library/imports';
@use '../../library/abstracts/kt-palette-drupal';

@include mat.core();

$kt-theme-primary: mat.m2-define-palette(kt-palette-drupal.$kt-success, 500);
$kt-theme-accent: mat.m2-define-palette(kt-palette-drupal.$kt-accent, 500);
$kt-theme-warn: mat.m2-define-palette(kt-palette-drupal.$kt-error);
$color: kt-palette-drupal.$dark-primary-text;

$custom-typography: mat.m2-define-typography-config(
  $font-family: kt-palette-drupal.$global-font-family,
  $body-1: mat.m2-define-typography-level(14px, 1em, 400, $letter-spacing: normal),
  $body-2: mat.m2-define-typography-level(14px, 1em, 400, $letter-spacing: normal),
  // tbd see: https://material.angular.io/guide/typography corresponding to the <h3> tag.
  $headline-1: mat.m2-define-typography-level(null, null, null, $font-family: "Roboto, Arial,'sans-serif'"),
  $headline-2: mat.m2-define-typography-level(null, null, null, $font-family: "Roboto, Arial,'sans-serif'"),
  $headline-3: mat.m2-define-typography-level(18px, 28px, 400, $font-family: "Roboto, Arial,'sans-serif'")
);

@include mat.typography-hierarchy($custom-typography);

$kt-theme: mat.m2-define-light-theme(
  (
    color: (
      primary: $kt-theme-primary,
      accent: $kt-theme-accent,
      warn: $kt-theme-warn
    ),
    typography: $custom-typography
  )
);
// change color for font
@function kt-mat-light-theme-foreground($color) {
  @return (
    base: $color,
    divider: $color,
    // tbd
    dividers: $color,
    // tbd
    disabled: rgba($color, 0.65),
    disabled-button: rgba($color, 0.65),
    disabled-text: rgba($color, 0.87),
    hint-text: $color,
    // tbd
    secondary-text: $color,
    // tbd
    icon: $color,
    // tbd
    icons: $color,
    // tbd
    text: $color,
    slider-min: $color,
    // tbd
    slider-off: $color,
    // tbd
    slider-off-active: $color // tbd
  );
}

$kt-foreground: kt-mat-light-theme-foreground(mat.m2-get-color-from-palette(kt-palette-drupal.$kt-basic, A700));

$kt-app-theme-custom: map.merge(
  $kt-theme,
  (
    foreground: $kt-foreground,
    letter-spacing: normal
  )
);
@include mat.all-component-themes($kt-app-theme-custom);

$button-typography: mat.m2-define-typography-config(
  // Specify "Comic Sans" as the default font family for all levels.
  $font-family: 'Arial',
  $button: mat.m2-define-typography-level(14px, 18px, 400, $letter-spacing: normal)
);

@include mat.button-typography($button-typography);

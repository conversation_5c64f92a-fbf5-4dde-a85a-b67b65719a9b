@use '../../../../../../../../libs/kt-mat/src/lib/scss/abstracts/mixins';

:host ::ng-deep .kt-mat-library.dialog-mode {
  min-width: 500px;

  .kt-module-wrapper.mt-70,
  .kt-module-wrapper.mb-70 {
    margin: 0 !important;
  }
}

:host ::ng-deep .kt-mat-library.email-editor {
  .kt-drupal-theme {
    .mat-mdc-unelevated-button.mat-accent {
      background-color: #4cb9ea;
      border: none;
      color: white;
    }

    .mat-mdc-unelevated-button.mat-primary {
      background-color: #898989;
      border: none;
      color: white;
    }

    .mat-mdc-unelevated-button.mat-primary:disabled {
      background-color: #ebebeb;
      border: none;
      color: black;
    }
  }
}

.float-left,
.float-left button {
  display: block;
  float: left;
}

.clearAll {
  clear: both;
}

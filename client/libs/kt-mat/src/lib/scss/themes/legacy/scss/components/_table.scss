@use "../../../core/abstracts/functions";
@use "../../../core/base/fonts";

.kt-table {
  width: 100%;
  font-family: fonts.$font-primary;
  .mat-mdc-header-cell {
    color: functions.color(font, dark);
    font-weight: 700;
    font-size: 14px;
  }
  .mat-mdc-header-row {
    min-height: 45px;
  }
  .mat-mdc-row {
    min-height: 45px;
    &:hover {
      transition: all 0.1s linear;
      background: functions.color(ktold, wizardBackground);
    }
  }
  .mat-mdc-cell {
    border-color: functions.color(ktold, whiteBorderDark);
    &.mat-column-column {
      font-size: 14px;
      color: functions.color(ktold, blueLight);
      cursor: default;
    }
  }
}
@media screen and (max-width: 960px) {
  .kt-table {
    .mat-mdc-header-row {
      display: none;
    }
    .mat-mdc-row {
      display: grid;
      grid-template-columns: 50% 50%;
      overflow: hidden;
      height: auto;
      position: relative;
      clear: both;
      border-radius: 3px;
      border: 1px solid functions.color(ktold, whiteBorderLight);
      padding: 5px 0;
      &:first-child {
        margin-top: 5px;
      }
      + .mat-mdc-row {
        margin-top: 20px;
      }
    }
    .mat-mdc-cell {
      display: block;
      width: 100% !important;
      min-height: 60px;
      padding: 0 16px;
      margin: 5px 0;
      border: none;
      &:first-of-type {
        padding-left: 16px;
      }
      &:last-of-type {
        padding-right: 16px;
      }
      &.m-card-label {
        &::before {
          content: attr(data-label);
          display: block;
          color: functions.color(font, dark);
          font-weight: 700;
          font-size: 14px;
          margin-bottom: 3px;
        }
        &.mat-column-assign {
          grid-column: 1;
        }
      }
    }
  }
}

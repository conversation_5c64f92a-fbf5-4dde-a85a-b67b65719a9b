.tools-icon-grid {
  text-align: center;

  .tools-icon-grid-title {
    position: relative;
    display: block;
    background: #ffffff url('https://assets.klicktipp.com/content_includes/frontpage/divider-02-mit-verlauf-zum-rand.png') no-repeat scroll center center;
    margin: 40px 0 50px 0;

    h3 {
      position: relative;
      display: inline-block;
      text-align: center;
      margin: 0;
      padding: 0 15px;
      font-size: 32px;
      line-height: 29px;
      vertical-align: middle;
      background-color: #ffffff;
    }
  }

  .tools-icon-grid-icons {
    display: grid;
    grid-template-columns: auto auto auto auto;
    grid-template-rows: auto;
    grid-column-gap: 10px;
    grid-row-gap: 20px;

    .tools-icon-wrapper {
      height: auto;
      position: relative;
      display: inline-block;
      text-align: center;

      [class^='kt-icon-'] {
        position: relative;
        display: inline-block;
        border: none;
        cursor: pointer;
      }

      .tools-icon-title {
        text-align: center;
        font-size: 15px;
      }

      .kt-icon-plugin-image {
        display: flex;
        padding: 15px;
        margin: auto;

        img {
          display: inline-block;
          max-width: 100%;
          margin: auto;
          object-fit: contain;
        }
      }
    }
  }
}

@media (min-width: 1200px) {
  .tools-icon-grid {
    .tools-icon-grid-icons {
      .tools-icon-wrapper {
        [class^='kt-icon-'] {
          width: 204px;
          height: 204px;
          background: transparent url('/misc/img/stc_icons_sprite_large.png') scroll no-repeat 0 0;
        }

        .kt-icon-plugin-default {
          background-image: url('/misc/plugins/background.png');
          &:hover {
            background-position: 0px -204px;
          }
        }
      }
    }
  }
}

@media (min-width: 993px) and (max-width: 1199px) {
  .tools-icon-grid {
    .tools-icon-grid-icons {
      .tools-icon-wrapper {
        [class^='kt-icon-'] {
          background: transparent url('/misc/img/stc_icons_sprite_medium.png') scroll no-repeat 0 0;
          width: 164px !important;
          height: 164px !important;
        }

        .kt-icon-plugin-default {
          background-image: url('/misc/plugins/background_medium.png');
        }
      }
    }
  }
}

@media (max-width: 992px) {
  .tools-icon-grid {
    .tools-icon-grid-icons {
      grid-row-gap: 10px;

      .tools-icon-wrapper {
        [class^='kt-icon-'] {
          background: transparent url('/misc/img/stc_icons_sprite_small.png') scroll no-repeat 0 0;
          width: 94px;
          height: 94px;
        }

        .kt-icon-plugin-default {
          background-image: url('/misc/plugins/background_small.png');
        }
      }
    }
  }
}

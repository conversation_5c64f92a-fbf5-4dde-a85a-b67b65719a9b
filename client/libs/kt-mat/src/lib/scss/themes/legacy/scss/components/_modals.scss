@use '../../../core/abstracts/functions';

.modal-body {
  padding: 15px;
}

.kt {
  &-header-section {
    padding: 20px;
    h4 {
      color: white;
      font-size: 18px;
      font-weight: 500;
      text-transform: none;
    }
    &--info {
      background: functions.color(ktold, ocean);
    }
    &--warning {
      background: functions.color(ktold, redLight);
    }
  }
  &-footer-section {
    background: functions.color(ktold, grayLight);
    padding: 20px;
  }
}
.modal-section {
  max-width: 600px;
  box-sizing: border-box;
  padding: 5px;
  p,
  .kt-list {
    color: functions.color(ktold, dark);
    font-size: 14px;
    &.bold {
      font-weight: 900;
    }
  }
  .kt-list {
    max-width: 550px;
    width: 90%;
    margin: 10px auto;
  }
}

.on-publish-email-modal {
  mat-dialog-container {
    max-height: calc(100vh - 15px);
    overflow-y: auto;
  }
}

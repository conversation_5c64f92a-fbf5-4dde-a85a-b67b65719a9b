@use '@angular/material' as mat;
@use '../../../../../../abstracts/mixins';
@use '../../../../../../abstracts/kt-palette-drupal' as p;

$addonFontSize: 18px;
$borderColor: #e5e5e5;

.outer-container {
  align-items: center;
  justify-content: center;
  height: calc(100vh - 70px);
  min-height: 885px;
}

.upsell-card-small-header {
  font-size: 14px;
  font-weight: 700;
  line-height: 40px;
  text-align: left;
  padding: 16px 16px 0;
  margin-bottom: 0;
  color: #3276b1;
}

:host::ng-deep {
  .feature-list {
    font-size: 18px;

    ul li {
      list-style: none;
      margin-left: 15px;
    }
  }

  .mat-mdc-card-header {
    padding-top: 0;
  }

  .mat-mdc-card-content {
    margin-top: 8px;
  }

  .mat-mdc-card {
    margin-bottom: 15px;
    width: 60%;
  }

  .mat-mdc-floating-label,
  .mdc-text-field:not(.mdc-text-field--disabled) .mdc-floating-label,
  .mdc-text-field.mdc-text-field--disabled .mdc-floating-label {
    font-size: $addonFontSize !important;
  }

  .mat-mdc-form-field-type-mat-select {
    width: 100%;

    .mdc-text-field {
      padding-top: 7px !important;
    }
  }
}

hr {
  width: calc(100% - 32px);
}

.select-container {
  flex-grow: 1;
  max-width: 75%;
}

.total-price {
  font-size: 20px;
  font-weight: 700;
}

.downgrade {
  opacity: 0.5;
}

.green-text,
red-text {
  font-weight: 600;
  margin-top: 16px;
}

.green-text {
  color: mat.m2-get-color-from-palette(p.$kt-success, 600);
}

.red-text {
  color: mat.m2-get-color-from-palette(p.$kt-error, 600);
}

.bottom-text-2,
.bottom-text {
  color: mat.m2-get-color-from-palette(p.$kt-basic, 600);
}

.bottom-text-2 {
  margin-top: 10px;

  fa-icon {
    color: #4cae4c;
  }
}

.bottom-text-base {
  margin-top: 15px;
  text-align: center;
  width: 60%;
}

.button-container {
  width: 100%;
  margin-top: 16px;
}

.icon-container {
  width: 45%;
  border: 1px solid $borderColor;
  border-radius: 8px;
  font-size: 16px;
  align-items: center;
  padding: 8px;

  img {
    width: 50px;
    height: 50px;
  }

  .desc-text {
    color: mat.m2-get-color-from-palette(p.$kt-basic, A350);
    font-size: 14px;
  }
}

.option-desc {
  font-size: 12px;
  color: mat.m2-get-color-from-palette(p.$kt-basic, A300);
}

.redirect-text {
  text-align: center;
}

.middle-container {
  min-width: 50px;
  font-size: 2em;
  align-items: center;
  color: mat.m2-get-color-from-palette(p.$kt-basic, 600);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px; // adjust this to change the thickness of the line
    background-color: mat.m2-get-color-from-palette(p.$kt-basic, 600);
  }
}

.loading-container {
  font-size: 1.5em;
}

.success-container {
  font-size: 18px;
  width: 60%;
}

.loading-container {
  padding: 45px;
}

.redirect-header {
  font-size: 14px;
  color: mat.m2-get-color-from-palette(p.$kt-success, 500);
  font-weight: bold;
}

.addon-font-size {
  font-size: $addonFontSize;
}

.total-price-container {
  border-top: 1px solid $borderColor;
  border-bottom: 1px solid $borderColor;
  padding: 16px 0;
  margin-top: -16px;
}

@use '../../../../../core/abstracts/index';

kt-entity-select {
  .kt-mat-library {
    width: 100%;
  }

  .kt-mat-library .kt-drupal-theme input[type='text'] {
    width: 100%;
    border-color: #d6e2f7;
    background-color: white;
    font-weight: 600;
    color: #6e789a;

    &.ng-invalid.ng-touched {
      border-color: #d43f3a;
      box-shadow: 0 0 2px #d43f3a;
    }
  }

  .kt-mat-library .mat-mdc-form-field.mat-focused .mat-form-field-label,
  .kt-mat-library .mat-form-field .mat-form-field-label {
    color: #6e789a;
  }

  .kt-mat-library .kt-drupal-theme input[type='text']::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: red;
    opacity: 1; /* Firefox */
  }


  .kt-entity-select {
    display: flex;
    position: relative;

    .kt-unselect {
      right: 20px;
      top: -5px;
      position: relative;
      cursor: pointer;

      &:before,
      &:after {
        background-color: index.color(font, mediumDark);
        display: block;
        content: '';
        width: 12px;
        height: 2px;
        position: absolute;
        top: 50%;
      }

      &:before {
        @include index.rotate(-45deg);
      }

      &:after {
        @include index.rotate(45deg);
      }
    }
  }

  .kt-hidden {
    display: none;
  }

  .kt-close-header {
    position: relative;
    padding: 0;
    z-index: 1;

    .kt-close {
      top: 8px;
      right: 10px;
      width: 12px;
      height: 12px;
      position: absolute;
      cursor: pointer;

      &:before,
      &:after {
        background-color: index.color(font, light);
        display: block;
        content: '';
        width: 18px;
        height: 2px;
        position: absolute;
        top: 50%;
      }

      &:before {
        @include index.rotate(-45deg);
      }

      &:after {
        @include index.rotate(45deg);
      }

      &:hover {
        &:before,
        &:after {
          background-color: index.color(font, dark);
          transition: background-color 0.15s linear;
        }
      }
    }
  }
}

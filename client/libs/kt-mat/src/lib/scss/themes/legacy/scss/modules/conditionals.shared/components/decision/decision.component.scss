@use '../../../../../../../../libs/kt-mat/src/lib/scss/themes/core/abstracts/index';

.kt {
  &-decision {
    padding: 25px 50px 40px;

    > .kt-segment-wrapper:nth-last-of-type(2) > .kt-add-logic {
      display: none;
    }

    > .kt-segment-wrapper:first-of-type {
      .kt-condition:nth-child(1) + .kt-segment-footer {
        button.trash {
          display: none;
        }
      }
    }
  }

  &-segment {
    border: 1px solid index.color(border, default);
    border-radius: 5px;
    background-color: index.color(background, light);
    padding: 25px;
    flex-wrap: wrap;
    box-sizing: border-box;

    &-footer {
      display: flex;
      align-items: center;
      padding-top: 30px;
      justify-content: space-between;
    }
  }

  &-condition {
    display: flex;
    flex-flow: row wrap;

    .kt-select-logic {
      margin-top: 8px;
      margin-right: 6px;
    }

    .kt-select {
      // padding-top: 8px;
      padding-right: 6px;

      &::before {
        display: block;
        content: '';
        width: 6px;
        height: 1px;
        background: index.color(border, medium);
        left: -6px;
        top: 18px;
        @include index.translate(-6px, 18px);
      }

      &-condition {
        z-index: 2;
      }
    }

    &-group {
      display: inline-flex;
      flex-flow: row wrap;

      .kt-entity-select {
        width: 100%;
        position: relative;
      }
    }

    &-close {
      display: inline-flex;
      margin: 8px auto 0 0;

      fa-icon {
        font-size: 16px;
      }
    }

    &:not(:first-of-type) {
      .kt-condition-group-nested {
        .kt-select-nested,
        .kt-select-action {
          margin-left: 100px;
        }
      }
    }

    &:first-of-type {
      .kt-select.kt-select-condition,
      .kt-select.kt-select-type {
        &::before {
          display: none;
        }
      }

      .kt-select-logic {
        display: none;
      }
    }
  }

  &-condition-group-nested {
    display: contents;

    .kt-select-nested {
      margin-left: 20px;
    }

    .kt-select-action {
      margin-left: 20px;
      position: relative;

      &::after {
        display: block;
        content: '';
        width: 1px;
        height: calc(100% + 35px);
        background: index.color(border, medium);
        position: absolute;
        left: -6px;
        bottom: 17px;
        z-index: 1;
      }
    }

    .kt-select-condition {
      button {
        .kt-text-overflow {
          @include index.for-laptop-l {
            max-width: 100%;
          }
          @include index.for-laptop-xs {
            max-width: 100%;
          }
        }
      }
    }
  }

  &-add {
    &-logic {
      margin-bottom: 12px;

      &::after {
        display: block;
        content: '';
        width: 2px;
        height: 12px;
        background: index.color(border, medium);
        position: absolute;
        left: 50%;
        bottom: -12px;
      }
    }

    &-segment,
    &-logic {
      flex-basis: 100%;
      text-align: center;
      margin-top: 12px;
      position: relative;

      &::before {
        display: block;
        content: '';
        width: 2px;
        height: 12px;
        background: index.color(border, medium);
        position: absolute;
        left: 50%;
        top: -12px;
      }
    }
  }
}

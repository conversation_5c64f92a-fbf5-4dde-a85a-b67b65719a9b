@use '../../../../../core/abstracts/index';
@use '../../../../../../library/abstracts/mixins';

kt-signature-overview {
  .table-signature-overview table.mat-mdc-table tbody tr.predefined {
    background-color: index.color(table, highlight);

    &:hover {
      background-color: index.color(table, highlight-hover);
    }
  }

  tr.dragged-item span.svg-drag-drop-arrows::after {
    content: '*';
    display: inline-block;
    color: index.color(alert, error);
  }

  .svg-drag-drop-arrows {
    color: #707070;

    &:hover {
      color: black;
    }
  }

  .mat-column-weight {
    width: 120px;
  }

  .cdk-drag-preview.cdk-drag {
    padding: 8px;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    width: 100%;

    td {
      border: none;
      background-color: transparent;
    }

    .mat-column-tags {
      display: none;
    }
  }

  .mat-column-weight {
    width: 120px;
  }
}

@use '@angular/material' as mat;
@use '../../../../../../../abstracts/mixins';
@use '../../../../../../../abstracts/kt-palette-drupal' as p;

:host::ng-deep {
  input[type='text'] {
    width: 100%;
    min-width: 400px;
    min-height: 30px;
    max-height: 32px;
    line-height: 17px;
  }
}

.subject-modal-close-icon {
  top: 0;
  right: 0;
}

.dialog-content {
  padding: 15px;
  border-radius: 5px;
  border: 1px solid mat.m2-get-color-from-palette(p.$kt-basic, 800);

  &.kt-smart-subject {
    .dialog-container {
      display: flex;
    }

    .dialog-input-row {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: 4px;

      .mdc-text-field-wrapper {
        padding-bottom: 0;
      }

      .button-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-top: 13px;
      }
    }

    .result-container {
      width: 100%;
      margin-top: 15px;

      .mat-list-item-text {
        padding-right: 0;
      }
    }

    .result-loader {
      height: 242px;
      text-align: center;
      padding-top: 120px;

      border-radius: 6px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.07);
      border: 1px solid #cccccc;

      span {
        margin-left: 15px;
      }
    }

    .subject-selection-list-items {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.dialog-content.hide-border {
  padding: 0;
  border-radius: 0;
  border: none;
}

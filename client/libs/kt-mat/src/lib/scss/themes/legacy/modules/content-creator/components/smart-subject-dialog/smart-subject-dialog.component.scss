@use '@angular/material' as mat;
@use '../../../../../../library/abstracts/kt-palette-drupal';

kt-smart-subject-dialog {
  .kt-mat-library .kt-drupal-theme {
    min-height: 192px;

    .subject-modal-close-icon {
      top: 0;
      right: 0;

      width: auto;
      height: auto;
      padding: 0;

      float: right;
      z-index: 1001;

      color: #adadad;
      transition: color 0.5s linear;

      &.mat-mdc-icon-button {
        --mat-mdc-button-persistent-ripple-color: #000;
        --mat-mdc-button-ripple-color: rgba(0, 0, 0, 0.1);
      }
    }

    .footer-row {
      margin-top: 5px;
      margin-bottom: 5px;
      display: flex;
      justify-content: flex-end;

      kt-button:last-child button {
        margin-right: 0;
      }
    }
  }
}

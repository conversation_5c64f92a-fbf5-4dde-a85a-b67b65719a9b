kt-plugin-create {
  .listbuilding-icon-grid {
    text-align: center;

    .listbuilding-icon-grid-title {
      position: relative;
      display: block;
      background: #ffffff url('https://assets.klicktipp.com/content_includes/frontpage/divider-02-mit-verlauf-zum-rand.png') no-repeat scroll center center;
      margin: 40px 0 50px 0;

      h3 {
        position: relative;
        display: inline-block;
        text-align: center;
        margin: 0;
        padding: 0 15px;
        font-size: 32px;
        line-height: 29px;
        vertical-align: middle;
        background-color: #ffffff;
      }
    }

    .listbuilding-icon-grid-icons {
      display: grid;
      grid-template-columns: auto auto auto auto;
      grid-template-rows: auto;
      grid-column-gap: 10px;
      grid-row-gap: 20px;

      .listbuilding-icon-wrapper {
        height: auto;
        position: relative;
        display: inline-block;
        text-align: center;

        [class^='kt-icon-'] {
          position: relative;
          display: inline-block;
          border: none;
          cursor: pointer;
        }

        .listbuilding-icon-title {
          text-align: center;
          font-size: 15px;
        }

        .kt-icon-plugin-image {
          display: flex;
          padding: 15px;
          margin: auto;

          img {
            display: inline-block;
            max-width: 100%;
            margin: auto;
            object-fit: contain;
          }
        }
      }
    }
  }

  @media (min-width: 1200px) {
    .listbuilding-icon-grid {
      .listbuilding-icon-grid-icons {
        .listbuilding-icon-wrapper {
          [class^='kt-icon-'] {
            width: 204px;
            height: 204px;
            background: transparent url('/misc/img/kt_icons_sprite_large.png') scroll no-repeat 0px 0px;
          }

          .kt-icon-plugin-default {
            background-image: url('/misc/plugins/background.png');

            &:hover {
              background-position: 0px -204px;
            }
          }

          .kt-icon-payment-digiStore {
            background-position: -3672px 0;

            &:hover {
              background-position: -3672px -204px;
            }
          }

          .kt-icon-payment-affilicon {
            background-position: -3876px 0;

            &:hover {
              background-position: -3876px -204px;
            }
          }

          .kt-icon-payment-clickbank {
            background-position: -4284px 0;

            &:hover {
              background-position: -4284px -204px;
            }
          }

          .kt-icon-payment-paypal {
            background-position: -4488px 0;

            &:hover {
              background-position: -4488px -204px;
            }
          }

          .kt-icon-payment-cleverbridge {
            background-position: -4692px 0;

            &:hover {
              background-position: -4692px -204px;
            }
          }

          .kt-icon-wufoo {
            background-position: -2856px 0;

            &:hover {
              background-position: -2856px -204px;
            }
          }

          .kt-icon-request-sms-twilio {
            background-position: -8366px 0;

            &:hover {
              background-position: -8366px -204px;
            }
          }

          .kt-icon-request-sms-nexmo {
            background-position: -8162px 0;

            &:hover {
              background-position: -8162px -204px;
            }
          }

          .kt-icon-facebook-button {
            background-position: -2244px 0;

            &:hover {
              background-position: -2244px -204px;
            }
          }

          .kt-icon-combo-box {
            background-position: -2448px 0;

            &:hover {
              background-position: -2448px -204px;
            }
          }

          .kt-icon-leadpages {
            background-position: -204px 0;

            &:hover {
              background-position: -204px -204px;
            }
          }

          .kt-icon-optimize-press {
            background-position: -408px 0;

            &:hover {
              background-position: -408px -204px;
            }
          }

          .kt-icon-thrive {
            background-position: -1428px 0;

            &:hover {
              background-position: -1428px -204px;
            }
          }

          .kt-icon-terminpilot {
            background-position: -7550px 0;

            &:hover {
              background-position: -7550px -204px;
            }
          }

          .kt-icon-woocommerce {
            background-position: -6120px 0;

            &:hover {
              background-position: -6120px -204px;
            }
          }

          .kt-icon-shopware {
            background-position: -4896px 0;

            &:hover {
              background-position: -4896px -204px;
            }
          }
        }
      }
    }
  }

  @media (min-width: 993px) and (max-width: 1199px) {
    .listbuilding-icon-grid {
      .listbuilding-icon-grid-icons {
        .listbuilding-icon-wrapper {
          [class^='kt-icon-'] {
            background: transparent url('/misc/img/kt_icons_sprite_medium.png') scroll no-repeat 0px 0px;
            width: 164px !important;
            height: 164px !important;
          }

          .kt-icon-plugin-default {
            background-image: url('/misc/plugins/background_medium.png');

            &:hover {
              background-position: 0px -164px;
            }
          }

          .kt-icon-payment-digiStore {
            background-position: -2952px 0;

            &:hover {
              background-position: -2952px -164px;
            }
          }

          .kt-icon-payment-affilicon {
            background-position: -3116px 0;

            &:hover {
              background-position: -3116px -164px;
            }
          }

          .kt-icon-payment-clickbank {
            background-position: -3440px 0;

            &:hover {
              background-position: -3440px -164px;
            }
          }

          .kt-icon-payment-paypal {
            background-position: -3604px 0;

            &:hover {
              background-position: -3604px -164px;
            }
          }

          .kt-icon-payment-cleverbridge {
            background-position: -3764px 0;

            &:hover {
              background-position: -3764px -164px;
            }
          }

          .kt-icon-wufoo {
            background-position: -2296px 0;

            &:hover {
              background-position: -2296px -164px;
            }
          }

          .kt-icon-request-sms-twilio {
            background-position: -6684px 0;

            &:hover {
              background-position: -6684px -164px;
            }
          }

          .kt-icon-request-sms-nexmo {
            background-position: -6518px 0;

            &:hover {
              background-position: -6518px -164px;
            }
          }

          .kt-icon-facebook-button {
            background-position: -1804px 0;

            &:hover {
              background-position: -1804px -164px;
            }
          }

          .kt-icon-combo-box {
            background-position: -1968px 0;

            &:hover {
              background-position: -1968px -164px;
            }
          }

          .kt-icon-leadpages {
            background-position: -164px 0;

            &:hover {
              background-position: -164px -164px;
            }
          }

          .kt-icon-optimize-press {
            background-position: -328px 0;

            &:hover {
              background-position: -328px -164px;
            }
          }

          .kt-icon-thrive {
            background-position: -1148px 0;

            &:hover {
              background-position: -1148px -164px;
            }
          }

          .kt-icon-terminpilot {
            background-position: -6034px 0;

            &:hover {
              background-position: -6034px -164px;
            }
          }
        }
      }
    }
  }

  @media (max-width: 992px) {
    .listbuilding-icon-grid {
      .listbuilding-icon-grid-icons {
        grid-row-gap: 10px;

        .listbuilding-icon-wrapper {
          [class^='kt-icon-'] {
            background: transparent url('/misc/img/kt_icons_sprite_small.png') scroll no-repeat 0px 0px;
            width: 94px;
            height: 94px;
          }

          .kt-icon-plugin-default {
            background-image: url('/misc/plugins/background_small.png');

            :hover {
              background-position: 0px -94px;
            }
          }

          .kt-icon-payment-digiStore {
            background-position: -3947px 0;

            :hover {
              background-position: -3947px -94px;
            }
          }

          .kt-icon-payment-affilicon {
            background-position: -1786px 0;

            :hover {
              background-position: -1786px -94px;
            }
          }

          .kt-icon-payment-clickbank {
            background-position: -1974px 0;

            :hover {
              background-position: -1974px -94px;
            }
          }

          .kt-icon-payment-paypal {
            background-position: -2068px 0;

            :hover {
              background-position: -2068px -94px;
            }
          }

          .kt-icon-payment-cleverbridge {
            background-position: -2162px 0;

            :hover {
              background-position: -2162px -94px;
            }
          }

          .kt-icon-wufoo {
            background-position: -1316px 0;

            :hover {
              background-position: -1316px -94px;
            }
          }

          .kt-icon-request-sms-twilio {
            background-position: -3852px 0;

            :hover {
              background-position: -3852px -94px;
            }
          }

          .kt-icon-request-sms-nexmo {
            background-position: -3758px 0;

            :hover {
              background-position: -3758px -94px;
            }
          }

          .kt-icon-facebook-button {
            background-position: -1034px 0;

            :hover {
              background-position: -1034px -94px;
            }
          }

          .kt-icon-combo-box {
            background-position: -1128px 0;

            :hover {
              background-position: -1128px -94px;
            }
          }

          .kt-icon-leadpages {
            background-position: -94px 0;

            :hover {
              background-position: -94px -94px;
            }
          }

          .kt-icon-optimize-press {
            background-position: -188px 0;

            :hover {
              background-position: -188px -94px;
            }
          }

          .kt-icon-thrive {
            background-position: -658px 0;

            :hover {
              background-position: -658px -94px;
            }
          }

          .kt-icon-terminpilot {
            background-position: -3476px 0;

            :hover {
              background-position: -3476px -94px;
            }
          }
        }
      }
    }
  }
}

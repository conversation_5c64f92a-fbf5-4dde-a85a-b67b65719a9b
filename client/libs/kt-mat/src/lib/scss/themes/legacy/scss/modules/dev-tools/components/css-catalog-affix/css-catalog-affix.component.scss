.toggle-container {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  justify-content: flex-end;
}

.kt-flex-row {
  display: flex;
  flex-direction: row;
}

.kt-flex-col-lt-md {
  @media (max-width: 959px) {
    flex-direction: column;
  }
}

.kt-flex-gap-20 {
  gap: 20px;
}

.kt-flex-15 {
  flex: 0 0 15%;
  max-width: 15%;

  @media (max-width: 959px) {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 20px;
  }
}

.kt-flex-85 {
  flex: 0 0 85%;
  max-width: 85%;

  @media (max-width: 959px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.kt-flex-100 {
  flex: 0 0 100%;
  max-width: 100%;
}

// Style for the affix component
:host ::ng-deep kt-affix {
  .mat-nav-list {
    padding: 0;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background-color: #f9f9f9;
  }

  .mat-list-item {
    height: auto;
    padding: 8px 16px;
    font-size: 14px;
    border-bottom: 1px solid #e0e0e0;

    &:last-child {
      border-bottom: none;
    }

    &.selectedListItem {
      background-color: #e3f2fd;
      color: #1976d2;
      font-weight: 500;
    }

    &:hover {
      background-color: #f0f0f0;
    }
  }
}

import { AfterViewInit, ApplicationRef, ComponentFactoryResolver, Directive, Do<PERSON>heck, Host, Injector, Optional, Renderer2, Self, ViewContainerRef } from '@angular/core';
import { MatButton } from '@angular/material/button';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { KtButtonToggleComponent } from '../components/buttons/button-toggle/kt-button-toggle.component';
import { PaginatorSizeOptions } from '../models/defaults';
import { ToggleSelectOption } from '../models/form-helpers';
import { KtTableService } from '../services/kt-table.service';

// code source is from here https://github.com/AzizStark/angular-custom-material-paginator
@Directive({
  selector: '[ktMatPagination]',
  standalone: true
})
export class PaginatorDirective implements DoCheck, AfterViewInit {
  private currentPage: number;
  private pageGapTxt: string[];
  private rangeStart: number;
  private rangeEnd: number;
  private buttons: MatButton[] = [];
  private showTotalPages: number;
  private checkPage: number[];

  private initDone = false;

  constructor(
    @Host() @Self() @Optional() private readonly matPag: MatPaginator,
    private readonly viewContainerRef: ViewContainerRef,
    private readonly renderer: Renderer2,
    private resolver: ComponentFactoryResolver,
    private injector: Injector,
    private app: ApplicationRef,
    private ktTableService: KtTableService
  ) {
    this.currentPage = 1;
    this.pageGapTxt = ['•••', '---'];
    this.showTotalPages = 4;
    this.checkPage = [0, 0, 0];
    // Display custom range label text

    this.matPag._intl.getRangeLabel = (page: number, pageSize: number, length: number): string => {
      const startIndex = page * pageSize;
      const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;
      return length > 0 ? `${startIndex + 1} – ${endIndex}/${length}` : '0/0';
    };
    // Subscribe to rerender buttons when next page and last page button is used
    this.matPag.page.subscribe((paginator: PageEvent) => {
      if (this.currentPage === paginator.pageIndex) {
        return;
      }
      this.currentPage = paginator.pageIndex;
      this.matPag.pageIndex = paginator.pageIndex;
      this.initPageRange();
    });
  }

  ngAfterViewInit(): void {
    this.rangeStart = 0;
    this.rangeEnd = this.showTotalPages - 1;
    this.initDone = true;
    this.initPageRange();
    setTimeout(() => this.createPageSizeSelectButtons());
  }

  ngDoCheck(): void {
    // Reset paginator if the pageSize, pageIndex, length changes
    if (this.matPag?.length !== this.checkPage[0] || this.matPag?.pageSize !== this.checkPage[1] || this.matPag?.pageIndex !== this.checkPage[2]) {
      const pageCount = this.matPag.getNumberOfPages();
      if (this.currentPage > pageCount && pageCount !== 0) {
        this.currentPage = 1;
        this.matPag.pageIndex = 0;
      }
      this.currentPage = this.matPag.pageIndex;
      this.initPageRange();
      this.checkPage = [this.matPag.length, this.matPag.pageSize, this.matPag.pageIndex];
    }
  }

  changePageSize(pagesize: number): void {
    this.matPag._changePageSize(pagesize);
  }

  addDynamicComponent(newNode: HTMLDivElement): void {
    const factory = this.resolver.resolveComponentFactory(KtButtonToggleComponent);
    const newDiv = newNode.insertBefore(document.createElement('div'), newNode.childNodes[0]);
    const ref = factory.create(this.injector, [], newDiv);
    ref.instance.nodeId = 'kt-mat-paginator-size-options';
    ref.instance.options = this.createPageSizeOptions();
    ref.instance.colorNotSelected = 'white';
    ref.instance.colorSelected = 'blue';
    ref.instance.singleSelect = true;
    ref.instance.changed.subscribe((formVal: string | number) => this.pageSizeChanged(formVal));
    this.app.attachView(ref.hostView);
    this.changePageSize(this.matPag.pageSize);
  }

  private buildPageNumbers = (): void => {
    const totalPages: number = this.matPag.getNumberOfPages();
    const dots = [false, false];
    const page = this.showTotalPages + 2;

    const pageDifference = totalPages - page;
    const startIndex = Math.max(this.currentPage - this.showTotalPages - 2, 1);

    // Container div with paginator elements
    const actionContainer = this.viewContainerRef.element.nativeElement.querySelector('div.mat-mdc-paginator-range-actions');
    // Button that triggers the next page action
    const nextPageNode = this.viewContainerRef.element.nativeElement.querySelector('button.mat-mdc-paginator-navigation-next');
    // Label showing the page range
    const pageRange = this.viewContainerRef.element.nativeElement.querySelector('div.mat-mdc-paginator-range-label');

    let prevButtonCount = this.buttons.length;

    // Remove buttons before creating new ones
    if (prevButtonCount > 0) {
      this.buttons.forEach((button) => {
        this.renderer.removeChild(actionContainer, button);
      });
      // Empty state array
      prevButtonCount = 0;
    }

    if (!pageRange || !actionContainer) {
      return;
    }

    this.renderer.addClass(pageRange, 'custom-paginator-counter');
    this.renderer.addClass(actionContainer, 'custom-paginator-container');
    // Initialize next page and last page buttons
    if (prevButtonCount === 0) {
      const nodeArray = actionContainer.childNodes;
      setTimeout(() => {
        for (const node of nodeArray) {
          if (node.nodeName === 'BUTTON') {
            // Next Button styles
            if (node.innerHTML.length > 100 && node.disabled) {
              this.renderer.addClass(node, 'custom-paginator-arrow-disabled');
              this.renderer.removeClass(node, 'custom-paginator-arrow-enabled');
            } else if (node.innerHTML.length > 100 && !node.disabled) {
              this.renderer.addClass(node, 'custom-paginator-arrow-enabled');
              this.renderer.removeClass(node, 'custom-paginator-arrow-disabled');
            }
          }
        }
      });
    }

    if (totalPages > 0) {
      this.renderer.insertBefore(actionContainer, this.createButton('0', this.matPag.pageIndex), nextPageNode);
    }

    for (let index = startIndex; index < totalPages - 1; index = index + 1) {
      if (
        (index < page && this.currentPage <= this.showTotalPages) ||
        (index >= this.rangeStart && index <= this.rangeEnd) ||
        (this.currentPage > pageDifference && index >= pageDifference) ||
        totalPages < this.showTotalPages + page
      ) {
        this.renderer.insertBefore(actionContainer, this.createButton(`${index}`, this.matPag.pageIndex), nextPageNode);
      } else {
        if (index > this.rangeEnd && !dots[0]) {
          this.renderer.insertBefore(actionContainer, this.createButton(this.pageGapTxt[0], this.matPag.pageIndex), nextPageNode);
          dots[0] = true;
          break;
        }
        if (index < this.rangeEnd && !dots[1]) {
          this.renderer.insertBefore(actionContainer, this.createButton(this.pageGapTxt[1], this.matPag.pageIndex), nextPageNode);
          dots[1] = true;
        }
      }
    }

    if (totalPages > 1) {
      this.renderer.insertBefore(actionContainer, this.createButton(`${totalPages - 1}`, this.matPag.pageIndex), nextPageNode);
    }
  };

  private createButton(index: string, pageIndex: number): MatButton {
    const linkBtn: MatButton = this.renderer.createElement('button');
    this.renderer.setAttribute(linkBtn, 'class', 'custom-paginator-page');
    this.renderer.addClass(linkBtn, 'custom-paginator-page-enabled');
    if (index === this.pageGapTxt[0] || index === this.pageGapTxt[1]) {
      this.renderer.addClass(linkBtn, 'custom-paginator-arrow-enabled');
    }
    const pagingTxt = isNaN(+index) ? this.pageGapTxt[0] : +index + 1;
    const text = this.renderer.createText(pagingTxt + '');
    this.renderer.addClass(linkBtn, 'mat-custom-page');
    switch (index) {
      case `${pageIndex}`:
        this.renderer.setAttribute(linkBtn, 'disabled', 'disabled');
        this.renderer.removeClass(linkBtn, 'custom-paginator-page-enabled');
        this.renderer.addClass(linkBtn, 'custom-paginator-page-disabled');
        break;
      case this.pageGapTxt[0]:
        this.renderer.listen(linkBtn, 'click', () => {
          this.switchPage(this.currentPage < this.showTotalPages + 1 ? this.showTotalPages + 2 : this.currentPage + this.showTotalPages - 1);
        });
        break;
      case this.pageGapTxt[1]:
        this.renderer.listen(linkBtn, 'click', () => {
          this.switchPage(
            this.currentPage > this.matPag.getNumberOfPages() - this.showTotalPages - 2
              ? this.matPag.getNumberOfPages() - this.showTotalPages - 3
              : this.currentPage - this.showTotalPages + 1
          );
        });
        break;
      default:
        this.renderer.listen(linkBtn, 'click', () => {
          this.switchPage(+index);
        });
        break;
    }
    this.renderer.appendChild(linkBtn, text);
    // Add button to private array for state
    this.buttons.push(linkBtn);
    return linkBtn;
  }

  /**
   * @description calculates the button range based on class input parameters and based on current page index value.
   */
  private initPageRange(): void {
    this.rangeStart = this.currentPage - this.showTotalPages / 2;
    this.rangeEnd = this.currentPage + this.showTotalPages / 2;
    this.buildPageNumbers();
    this.createPageSizeSelectButtons();
  }

  private switchPage(index: number): void {
    this.ktTableService.currentPage.next(index);
    this.matPag.pageIndex = index;
    this.matPag.page.emit({
      previousPageIndex: this.currentPage,
      pageIndex: index,
      pageSize: this.matPag.pageSize,
      length: this.matPag.length
    });
    this.currentPage = index;
    this.initPageRange();
  }

  private createPageSizeSelectButtons(): void {
    const paginatorContainer = this.viewContainerRef.element.nativeElement.querySelector('div.mat-mdc-paginator-container');
    const subContainer = this.viewContainerRef.element.nativeElement.querySelector('mat-button-toggle-group');
    if (this.initDone && !subContainer) {
      this.addDynamicComponent(paginatorContainer);
    }
  }

  private createPageSizeOptions(): ToggleSelectOption[] {
    const options: ToggleSelectOption[] = [];
    for (const paginatorSizeOption of PaginatorSizeOptions) {
      let checked = false;
      if (paginatorSizeOption === this.matPag.pageSize) {
        checked = true;
      }
      options.push({ value: paginatorSizeOption, checked, text: paginatorSizeOption.toString() });
    }
    return options;
  }

  private pageSizeChanged(formVal: string | number): void {
    this.ktTableService.paginatorSize.next(formVal as number);
    this.changePageSize(formVal as number);
  }
}

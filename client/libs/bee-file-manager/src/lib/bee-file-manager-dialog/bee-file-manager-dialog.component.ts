import { ChangeDetectionStrategy, Component, inject, input, output } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { FileManagerEntity } from '@klicktipp/bee-shared';
import { KtButtonComponent } from '@klicktipp/kt-mat-library';
import { BeeFileManagerComponent } from '../bee-file-manager/bee-file-manager.component';

@Component({
  selector: 'kt-bee-file-manager-dialog',
  imports: [KtButtonComponent],
  templateUrl: './bee-file-manager-dialog.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BeeFileManagerDialogComponent {
  private readonly dialog = inject(MatDialog);

  insert = output<FileManagerEntity>();
  cancel = output<void>();
  error = output<Error>();

  userId = input<string>();

  onOpenDialog(): void {
    this.dialog
      .open(BeeFileManagerComponent, { data: { userId: this.userId() } })
      .afterClosed()
      .subscribe((result: { data?: FileManagerEntity; error?: Error } | null) => {
        if (result?.error) {
          this.error.emit(result.error);
        } else if (result?.data) {
          this.insert.emit(result.data);
        } else if (result == null) {
          this.cancel.emit();
        } else {
          this.error.emit(new Error('Unexpected FileManager dialog result'));
        }
      });
  }
}

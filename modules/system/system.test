<?php

/**
 * @file
 * Tests for system.module.
 */

/**
 * Helper class for module test cases.
 */
class ModuleTestCase extends DrupalWebTestCase {
  protected $admin_user;

  function setUp() {
    parent::setUp('system_test');

    $this->admin_user = $this->drupalCreateUser(array('access administration pages', 'administer modules'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Assert there are tables that begin with the specified base table name.
   *
   * @param $base_table
   *   Beginning of table name to look for.
   * @param $count
   *   (optional) Whether or not to assert that there are tables that match the
   *   specified base table. Defaults to TRUE.
   */
  function assertTableCount($base_table, $count = TRUE) {
    $tables = db_find_tables_d8($base_table . '%');

    if ($count) {
      return $this->assertTrue($tables, format_string('Tables matching "@base_table" found.', array('@base_table' => $base_table)));
    }
    return $this->assertFalse($tables, format_string('Tables matching "@base_table" not found.', array('@base_table' => $base_table)));
  }

  /**
   * Assert that all tables defined in a module's hook_schema() exist.
   *
   * @param $module
   *   The name of the module.
   */
  function assertModuleTablesExist($module) {
    $tables = array_keys(drupal_get_schema_unprocessed($module));
    $tables_exist = TRUE;
    foreach ($tables as $table) {
      if (!db_table_exists($table)) {
        $tables_exist = FALSE;
      }
    }
    return $this->assertTrue($tables_exist, format_string('All database tables defined by the @module module exist.', array('@module' => $module)));
  }

  /**
   * Assert that none of the tables defined in a module's hook_schema() exist.
   *
   * @param $module
   *   The name of the module.
   */
  function assertModuleTablesDoNotExist($module) {
    $tables = array_keys(drupal_get_schema_unprocessed($module));
    $tables_exist = FALSE;
    foreach ($tables as $table) {
      if (db_table_exists($table)) {
        $tables_exist = TRUE;
      }
    }
    return $this->assertFalse($tables_exist, format_string('None of the database tables defined by the @module module exist.', array('@module' => $module)));
  }

  /**
   * Assert the list of modules are enabled or disabled.
   *
   * @param $modules
   *   Module list to check.
   * @param $enabled
   *   Expected module state.
   */
  function assertModules(array $modules, $enabled) {
    module_list(TRUE);
    foreach ($modules as $module) {
      if ($enabled) {
        $message = 'Module "@module" is enabled.';
      }
      else {
        $message = 'Module "@module" is not enabled.';
      }
      $this->assertEqual(module_exists($module), $enabled, format_string($message, array('@module' => $module)));
    }
  }

  /**
   * Verify a log entry was entered for a module's status change.
   * Called in the same way of the expected original watchdog() execution.
   *
   * @param $type
   *   The category to which this message belongs.
   * @param $message
   *   The message to store in the log. Keep $message translatable
   *   by not concatenating dynamic values into it! Variables in the
   *   message should be added by using placeholder strings alongside
   *   the variables argument to declare the value of the placeholders.
   *   See t() for documentation on how $message and $variables interact.
   * @param $variables
   *   Array of variables to replace in the message on display or
   *   NULL if message is already translated or not possible to
   *   translate.
   * @param $severity
   *   The severity of the message, as per RFC 3164.
   * @param $link
   *   A link to associate with the message.
   */
  function assertLogMessage($type, $message, $variables = array(), $severity = WATCHDOG_NOTICE, $link = '') {
    $count = db_select('watchdog', 'w')
      ->condition('type', $type)
      ->condition('message', $message)
      ->condition('variables', serialize($variables))
      ->condition('severity', $severity)
      ->condition('link', $link)
      ->countQuery()
      ->execute()
      ->fetchField();
    $this->assertTrue($count > 0, format_string('watchdog table contains @count rows for @message', array('@count' => $count, '@message' => $message)));
  }
}

/**
 * Test module enabling/disabling functionality.
 */
class EnableDisableTestCase extends ModuleTestCase {
  protected $profile = 'testing';

  public static function getInfo() {
    return array(
      'name' => 'Enable/disable modules',
      'description' => 'Enable/disable core module and confirm table creation/deletion.',
      'group' => 'Module',
    );
  }

  /**
   * Test that all core modules can be enabled, disabled and uninstalled.
   */
  function testEnableDisable() {
    // Try to enable, disable and uninstall all core modules, unless they are
    // hidden or required.
    $modules = system_rebuild_module_data();
    foreach ($modules as $name => $module) {
      if ($module->info['package'] != 'Core' || !empty($module->info['hidden']) || !empty($module->info['required'])) {
        unset($modules[$name]);
      }
    }
    $this->assertTrue(count($modules), format_string('Found @count core modules that we can try to enable in this test.', array('@count' => count($modules))));

    // Enable the dblog module first, since we will be asserting the presence
    // of log messages throughout the test.
   if (isset($modules['dblog'])) {
     $modules = array('dblog' => $modules['dblog']) + $modules;
   }

   // Set a variable so that the hook implementations in system_test.module
   // will display messages via drupal_set_message().
   variable_set('test_verbose_module_hooks', TRUE);

    // Throughout this test, some modules may be automatically enabled (due to
    // dependencies). We'll keep track of them in an array, so we can handle
    // them separately.
    $automatically_enabled = array();

    // Go through each module in the list and try to enable it (unless it was
    // already enabled automatically due to a dependency).
    foreach ($modules as $name => $module) {
      if (empty($automatically_enabled[$name])) {
        // Start a list of modules that we expect to be enabled this time.
        $modules_to_enable = array($name);

        // Find out if the module has any dependencies that aren't enabled yet;
        // if so, add them to the list of modules we expect to be automatically
        // enabled.
        foreach (array_keys($module->requires) as $dependency) {
          if (isset($modules[$dependency]) && empty($automatically_enabled[$dependency])) {
            $modules_to_enable[] = $dependency;
            $automatically_enabled[$dependency] = TRUE;
          }
        }

        // Check that each module is not yet enabled and does not have any
        // database tables yet.
        foreach ($modules_to_enable as $module_to_enable) {
          $this->assertModules(array($module_to_enable), FALSE);
          $this->assertModuleTablesDoNotExist($module_to_enable);
        }

        // Install and enable the module.
        $edit = array();
        $edit['modules[Core][' . $name . '][enable]'] = $name;
        $this->drupalPost('admin/modules', $edit, t('Save configuration'));
        // Handle the case where modules were installed along with this one and
        // where we therefore hit a confirmation screen.
        if (count($modules_to_enable) > 1) {
          $this->drupalPost(NULL, array(), t('Continue'));
        }
        $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');

        // Check that hook_modules_installed() and hook_modules_enabled() were
        // invoked with the expected list of modules, that each module's
        // database tables now exist, and that appropriate messages appear in
        // the logs.
        foreach ($modules_to_enable as $module_to_enable) {
          $this->assertText(t('hook_modules_installed fired for @module', array('@module' => $module_to_enable)));
          $this->assertText(t('hook_modules_enabled fired for @module', array('@module' => $module_to_enable)));
          $this->assertModules(array($module_to_enable), TRUE);
          $this->assertModuleTablesExist($module_to_enable);
          $this->assertLogMessage('system', "%module module installed.", array('%module' => $module_to_enable), WATCHDOG_INFO);
          $this->assertLogMessage('system', "%module module enabled.", array('%module' => $module_to_enable), WATCHDOG_INFO);
        }

        // Disable and uninstall the original module, and check appropriate
        // hooks, tables, and log messages. (Later, we'll go back and do the
        // same thing for modules that were enabled automatically.) Skip this
        // for the dblog module, because that is needed for the test; we'll go
        // back and do that one at the end also.
        if ($name != 'dblog') {
          $this->assertSuccessfulDisableAndUninstall($name);
        }
      }
    }

    // Go through all modules that were automatically enabled, and try to
    // disable and uninstall them one by one.
    while (!empty($automatically_enabled)) {
      $initial_count = count($automatically_enabled);
      foreach (array_keys($automatically_enabled) as $name) {
        // If the module can't be disabled due to dependencies, skip it and try
        // again the next time. Otherwise, try to disable it.
        $this->drupalGet('admin/modules');
        $disabled_checkbox = $this->xpath('//input[@type="checkbox" and @disabled="disabled" and @name="modules[Core][' . $name . '][enable]"]');
        if (empty($disabled_checkbox) && $name != 'dblog') {
          unset($automatically_enabled[$name]);
          $this->assertSuccessfulDisableAndUninstall($name);
        }
      }
      $final_count = count($automatically_enabled);
      // If all checkboxes were disabled, something is really wrong with the
      // test. Throw a failure and avoid an infinite loop.
      if ($initial_count == $final_count) {
        $this->fail(t('Remaining modules could not be disabled.'));
        break;
      }
    }

    // Disable and uninstall the dblog module last, since we needed it for
    // assertions in all the above tests.
    if (isset($modules['dblog'])) {
      $this->assertSuccessfulDisableAndUninstall('dblog');
    }

    // Now that all modules have been tested, go back and try to enable them
    // all again at once. This tests two things:
    // - That each module can be successfully enabled again after being
    //   uninstalled.
    // - That enabling more than one module at the same time does not lead to
    //   any errors.
    $edit = array();
    foreach (array_keys($modules) as $name) {
      $edit['modules[Core][' . $name . '][enable]'] = $name;
    }
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');
  }

  /**
   * Ensures entity info cache is updated after changes.
   */
  function testEntityInfoChanges() {
    module_enable(array('entity_cache_test'));
    $entity_info = entity_get_info();
    $this->assertTrue(isset($entity_info['entity_cache_test']), 'Test entity type found.');

    // Change the label of the test entity type and make sure changes appear
    // after flushing caches.
    variable_set('entity_cache_test_label', 'New label.');
    drupal_flush_all_caches();
    $info = entity_get_info('entity_cache_test');
    $this->assertEqual($info['label'], 'New label.', 'New label appears in entity info.');

    // Disable the providing module and make sure the entity type is gone.
    module_disable(array('entity_cache_test', 'entity_cache_test_dependency'));
    $entity_info = entity_get_info();
    $this->assertFalse(isset($entity_info['entity_cache_test']), 'Entity type of the providing module is gone.');
  }

  /**
   * Tests entity info cache after enabling a module with a dependency on an entity providing module.
   *
   * @see entity_cache_test_watchdog()
   */
  function testEntityInfoCacheWatchdog() {
    module_enable(array('entity_cache_test'));
    $info = variable_get('entity_cache_test');
    $this->assertEqual($info['label'], 'Entity Cache Test', 'Entity info label is correct.');
    $this->assertEqual($info['controller class'], 'DrupalDefaultEntityController', 'Entity controller class info is correct.');
  }

  /**
   * Disables and uninstalls a module and asserts that it was done correctly.
   *
   * @param $module
   *   The name of the module to disable and uninstall.
   */
  function assertSuccessfulDisableAndUninstall($module) {
    // Disable the module.
    $edit = array();
    $edit['modules[Core][' . $module . '][enable]'] = FALSE;
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');
    $this->assertModules(array($module), FALSE);

    // Check that the appropriate hook was fired and the appropriate log
    // message appears.
    $this->assertText(t('hook_modules_disabled fired for @module', array('@module' => $module)));
    $this->assertLogMessage('system', "%module module disabled.", array('%module' => $module), WATCHDOG_INFO);

    //  Check that the module's database tables still exist.
    $this->assertModuleTablesExist($module);

    // Uninstall the module.
    $edit = array();
    $edit['uninstall[' . $module . ']'] = $module;
    $this->drupalPost('admin/modules/uninstall', $edit, t('Uninstall'));
    $this->drupalPost(NULL, NULL, t('Uninstall'));
    $this->assertText(t('The selected modules have been uninstalled.'), 'Modules status has been updated.');
    $this->assertModules(array($module), FALSE);

    // Check that the appropriate hook was fired and the appropriate log
    // message appears. (But don't check for the log message if the dblog
    // module was just uninstalled, since the {watchdog} table won't be there
    // anymore.)
    $this->assertText(t('hook_modules_uninstalled fired for @module', array('@module' => $module)));
    if ($module != 'dblog') {
      $this->assertLogMessage('system', "%module module uninstalled.", array('%module' => $module), WATCHDOG_INFO);
    }

    // Check that the module's database tables no longer exist.
    $this->assertModuleTablesDoNotExist($module);
  }
}

/**
 * Tests failure of hook_requirements('install').
 */
class HookRequirementsTestCase extends ModuleTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Requirements hook failure',
      'description' => "Attempts enabling a module that fails hook_requirements('install').",
      'group' => 'Module',
    );
  }

  /**
   * Assert that a module cannot be installed if it fails hook_requirements().
   */
  function testHookRequirementsFailure() {
    $this->assertModules(array('requirements1_test'), FALSE);

    // Attempt to install the requirements1_test module.
    $edit = array();
    $edit['modules[Testing][requirements1_test][enable]'] = 'requirements1_test';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));

    // Makes sure the module was NOT installed.
    $this->assertText(t('Requirements 1 Test failed requirements'), 'Modules status has been updated.');
    $this->assertModules(array('requirements1_test'), FALSE);
  }
}

/**
 * Test module dependency functionality.
 */
class ModuleDependencyTestCase extends ModuleTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Module dependencies',
      'description' => 'Enable module without dependency enabled.',
      'group' => 'Module',
    );
  }

  /**
   * Checks functionality of project namespaces for dependencies.
   */
  function testProjectNamespaceForDependencies() {
    // Enable module with project namespace to ensure nothing breaks.
    $edit = array(
      'modules[Testing][system_project_namespace_test][enable]' => TRUE,
    );
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertModules(array('system_project_namespace_test'), TRUE);
  }

  /**
   * Attempt to enable translation module without locale enabled.
   */
  function testEnableWithoutDependency() {
    // Attempt to enable content translation without locale enabled.
    $edit = array();
    $edit['modules[Core][translation][enable]'] = 'translation';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('Some required modules must be enabled'), 'Dependency required.');

    $this->assertModules(array('translation', 'locale'), FALSE);

    // Assert that the locale tables weren't enabled.
    $this->assertTableCount('languages', FALSE);
    $this->assertTableCount('locale', FALSE);

    $this->drupalPost(NULL, NULL, t('Continue'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');

    $this->assertModules(array('translation', 'locale'), TRUE);

    // Assert that the locale tables were enabled.
    $this->assertTableCount('languages', TRUE);
    $this->assertTableCount('locale', TRUE);
  }

  /**
   * Attempt to enable a module with a missing dependency.
   */
  function testMissingModules() {
    // Test that the system_dependencies_test module is marked
    // as missing a dependency.
    $this->drupalGet('admin/modules');
    $this->assertRaw(t('@module (<span class="admin-missing">missing</span>)', array('@module' => drupal_ucfirst('_missing_dependency'))), 'A module with missing dependencies is marked as such.');
    $checkbox = $this->xpath('//input[@type="checkbox" and @disabled="disabled" and @name="modules[Testing][system_dependencies_test][enable]"]');
    $this->assert(count($checkbox) == 1, 'Checkbox for the module is disabled.');

    // Force enable the system_dependencies_test module.
    module_enable(array('system_dependencies_test'), FALSE);

    // Verify that the module is forced to be disabled when submitting
    // the module page.
    $this->drupalPost('admin/modules', array(), t('Save configuration'));
    $this->assertText(t('The @module module is missing, so the following module will be disabled: @depends.', array('@module' => '_missing_dependency', '@depends' => 'system_dependencies_test')), 'The module missing dependencies will be disabled.');

    // Confirm.
    $this->drupalPost(NULL, NULL, t('Continue'));

    // Verify that the module has been disabled.
    $this->assertModules(array('system_dependencies_test'), FALSE);
  }

  /**
   * Tests enabling a module that depends on an incompatible version of a module.
   */
  function testIncompatibleModuleVersionDependency() {
    // Test that the system_incompatible_module_version_dependencies_test is
    // marked as having an incompatible dependency.
    $this->drupalGet('admin/modules');
    $this->assertRaw(t('@module (<span class="admin-missing">incompatible with</span> version @version)', array(
      '@module' => 'System incompatible module version test (>2.0)',
      '@version' => '1.0',
    )), 'A module that depends on an incompatible version of a module is marked as such.');
    $checkbox = $this->xpath('//input[@type="checkbox" and @disabled="disabled" and @name="modules[Testing][system_incompatible_module_version_dependencies_test][enable]"]');
    $this->assert(count($checkbox) == 1, 'Checkbox for the module is disabled.');
  }

  /**
   * Tests enabling a module that depends on a module with an incompatible core version.
   */
  function testIncompatibleCoreVersionDependency() {
    // Test that the system_incompatible_core_version_dependencies_test is
    // marked as having an incompatible dependency.
    $this->drupalGet('admin/modules');
    $this->assertRaw(t('@module (<span class="admin-missing">incompatible with</span> this version of Drupal core)', array(
      '@module' => 'System incompatible core version test',
    )), 'A module that depends on a module with an incompatible core version is marked as such.');
    $checkbox = $this->xpath('//input[@type="checkbox" and @disabled="disabled" and @name="modules[Testing][system_incompatible_core_version_dependencies_test][enable]"]');
    $this->assert(count($checkbox) == 1, 'Checkbox for the module is disabled.');
  }

  /**
   * Tests enabling a module that depends on a module which fails hook_requirements().
   */
  function testEnableRequirementsFailureDependency() {
    $this->assertModules(array('requirements1_test'), FALSE);
    $this->assertModules(array('requirements2_test'), FALSE);

    // Attempt to install both modules at the same time.
    $edit = array();
    $edit['modules[Testing][requirements1_test][enable]'] = 'requirements1_test';
    $edit['modules[Testing][requirements2_test][enable]'] = 'requirements2_test';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));

    // Makes sure the modules were NOT installed.
    $this->assertText(t('Requirements 1 Test failed requirements'), 'Modules status has been updated.');
    $this->assertModules(array('requirements1_test'), FALSE);
    $this->assertModules(array('requirements2_test'), FALSE);

    // Makes sure that already enabled modules the failing modules depend on
    // were not disabled.
    $this->assertModules(array('comment'), TRUE);

  }

  /**
   * Tests that module dependencies are enabled in the correct order via the
   * UI. Dependencies should be enabled before their dependents.
   */
  function testModuleEnableOrder() {
    module_enable(array('module_test'), FALSE);
    $this->resetAll();
    $this->assertModules(array('module_test'), TRUE);
    variable_set('dependency_test', 'dependency');
    // module_test creates a dependency chain: forum depends on poll, which
    // depends on php. The correct enable order is, php, poll, forum.
    $expected_order = array('php', 'poll', 'forum');

    // Enable the modules through the UI, verifying that the dependency chain
    // is correct.
    $edit = array();
    $edit['modules[Core][forum][enable]'] = 'forum';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertModules(array('forum'), FALSE);
    $this->assertText(t('You must enable the Poll, PHP filter modules to install Forum.'), t('Dependency chain created.'));
    $edit['modules[Core][poll][enable]'] = 'poll';
    $edit['modules[Core][php][enable]'] = 'php';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertModules(array('forum', 'poll', 'php'), TRUE);

    // Check the actual order which is saved by module_test_modules_enabled().
    $this->assertIdentical(variable_get('test_module_enable_order', FALSE), $expected_order, t('Modules enabled in the correct order.'));
  }

  /**
   * Tests attempting to uninstall a module that has installed dependents.
   */
  function testUninstallDependents() {
    // Enable the forum module.
    $edit = array('modules[Core][forum][enable]' => 'forum');
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertModules(array('forum'), TRUE);

    // Disable forum and comment. Both should now be installed but disabled.
    $edit = array('modules[Core][forum][enable]' => FALSE);
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertModules(array('forum'), FALSE);
    $edit = array('modules[Core][comment][enable]' => FALSE);
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertModules(array('comment'), FALSE);

    // Check that the taxonomy module cannot be uninstalled.
    $this->drupalGet('admin/modules/uninstall');
    $checkbox = $this->xpath('//input[@type="checkbox" and @disabled="disabled" and @name="uninstall[comment]"]');
    $this->assert(count($checkbox) == 1, 'Checkbox for uninstalling the comment module is disabled.');

    // Uninstall the forum module, and check that taxonomy now can also be
    // uninstalled.
    $edit = array('uninstall[forum]' => 'forum');
    $this->drupalPost('admin/modules/uninstall', $edit, t('Uninstall'));
    $this->drupalPost(NULL, NULL, t('Uninstall'));
    $this->assertText(t('The selected modules have been uninstalled.'), 'Modules status has been updated.');
    $edit = array('uninstall[comment]' => 'comment');
    $this->drupalPost('admin/modules/uninstall', $edit, t('Uninstall'));
    $this->drupalPost(NULL, NULL, t('Uninstall'));
    $this->assertText(t('The selected modules have been uninstalled.'), 'Modules status has been updated.');
  }

  /**
   * Tests whether the correct module metadata is returned.
   */
  function testModuleMetaData() {
    // Generate the list of available modules.
    $modules = system_rebuild_module_data();
    // Check that the mtime field exists for the system module.
    $this->assertTrue(!empty($modules['system']->info['mtime']), 'The system.info file modification time field is present.');
    // Use 0 if mtime isn't present, to avoid an array index notice.
    $test_mtime = !empty($modules['system']->info['mtime']) ? $modules['system']->info['mtime'] : 0;
    // Ensure the mtime field contains a number that is greater than zero.
    $this->assertTrue(is_numeric($test_mtime) && ($test_mtime > 0), 'The system.info file modification time field contains a timestamp.');
  }

  /**
   * Tests whether the correct theme metadata is returned.
   */
  function testThemeMetaData() {
    // Generate the list of available themes.
    $themes = system_rebuild_theme_data();
    // Check that the mtime field exists for the bartik theme.
    $this->assertTrue(!empty($themes['bartik']->info['mtime']), 'The bartik.info file modification time field is present.');
    // Use 0 if mtime isn't present, to avoid an array index notice.
    $test_mtime = !empty($themes['bartik']->info['mtime']) ? $themes['bartik']->info['mtime'] : 0;
    // Ensure the mtime field contains a number that is greater than zero.
    $this->assertTrue(is_numeric($test_mtime) && ($test_mtime > 0), 'The bartik.info file modification time field contains a timestamp.');
  }
}

/**
 * Test module dependency on specific versions.
 */
class ModuleVersionTestCase extends ModuleTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Module versions',
      'description' => 'Check module version dependencies.',
      'group' => 'Module',
    );
  }

  function setUp() {
    parent::setUp('module_test');
  }

  /**
   * Test version dependencies.
   */
  function testModuleVersions() {
    $dependencies = array(
      // Alternating between being compatible and incompatible with 7.x-2.4-beta3.
      // The first is always a compatible.
      'common_test',
      // Branch incompatibility.
      'common_test (1.x)',
      // Branch compatibility.
      'common_test (2.x)',
      // Another branch incompatibility.
      'common_test (>2.x)',
      // Another branch compatibility.
      'common_test (<=2.x)',
      // Another branch incompatibility.
      'common_test (<2.x)',
      // Another branch compatibility.
      'common_test (>=2.x)',
      // Nonsense, misses a dash. Incompatible with everything.
      'common_test (=7.x2.x, >=2.4)',
      // Core version is optional. Compatible.
      'common_test (=7.x-2.x, >=2.4-alpha2)',
      // Test !=, explicitly incompatible.
      'common_test (=2.x, !=2.4-beta3)',
      // Three operations. Compatible.
      'common_test (=2.x, !=2.3, <2.5)',
      // Testing extra version. Incompatible.
      'common_test (<=2.4-beta2)',
      // Testing extra version. Compatible.
      'common_test (>2.4-beta2)',
      // Testing extra version. Incompatible.
      'common_test (>2.4-rc0)',
    );
    variable_set('dependencies', $dependencies);
    $n = count($dependencies);
    for ($i = 0; $i < $n; $i++) {
      $this->drupalGet('admin/modules');
      $checkbox = $this->xpath('//input[@id="edit-modules-testing-module-test-enable"]');
      $this->assertEqual(!empty($checkbox[0]['disabled']), $i % 2, $dependencies[$i]);
    }
  }
}

/**
 * Test required modules functionality.
 */
class ModuleRequiredTestCase extends ModuleTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Required modules',
      'description' => 'Attempt disabling of required modules.',
      'group' => 'Module',
    );
  }

  /**
   * Assert that core required modules cannot be disabled.
   */
  function testDisableRequired() {
    $module_info = system_get_info('module');
    $this->drupalGet('admin/modules');
    foreach ($module_info as $module => $info) {
      // Check to make sure the checkbox for each required module is disabled
      // and checked (or absent from the page if the module is also hidden).
      if (!empty($info['required'])) {
        $field_name = "modules[{$info['package']}][$module][enable]";
        if (empty($info['hidden'])) {
          $this->assertFieldByXPath("//input[@name='$field_name' and @disabled='disabled' and @checked='checked']", '', format_string('Field @name was disabled and checked.', array('@name' => $field_name)));
        }
        else {
          $this->assertNoFieldByName($field_name);
        }
      }
    }
  }
}

class IPAddressBlockingTestCase extends DrupalWebTestCase {
  protected $blocking_user;

  /**
   * Implement getInfo().
   */
  public static function getInfo() {
    return array(
      'name' => 'IP address blocking',
      'description' => 'Test IP address blocking.',
      'group' => 'System'
    );
  }

  /**
   * Implement setUp().
   */
  function setUp() {
    parent::setUp();

    // Create user.
    $this->blocking_user = $this->drupalCreateUser(array('block IP addresses'));
    $this->drupalLogin($this->blocking_user);
  }

  /**
   * Test a variety of user input to confirm correct validation and saving of data.
   */
  function testIPAddressValidation() {
    $this->drupalGet('admin/config/people/ip-blocking');

    // Block a valid IP address.
    $edit = array();
    $edit['ip'] = '*******';
    $this->drupalPost('admin/config/people/ip-blocking', $edit, t('Add'));
    $ip = db_query("SELECT iid from {blocked_ips} WHERE ip = :ip", array(':ip' => $edit['ip']))->fetchField();
    $this->assertTrue($ip, t('IP address found in database.'));
    $this->assertRaw(t('The IP address %ip has been blocked.', array('%ip' => $edit['ip'])), t('IP address was blocked.'));

    // Try to block an IP address that's already blocked.
    $edit = array();
    $edit['ip'] = '*******';
    $this->drupalPost('admin/config/people/ip-blocking', $edit, t('Add'));
    $this->assertText(t('This IP address is already blocked.'));

    // Try to block a reserved IP address.
    $edit = array();
    $edit['ip'] = '***************';
    $this->drupalPost('admin/config/people/ip-blocking', $edit, t('Add'));
    $this->assertText(t('Enter a valid IP address.'));

    // Try to block a reserved IP address.
    $edit = array();
    $edit['ip'] = 'test.example.com';
    $this->drupalPost('admin/config/people/ip-blocking', $edit, t('Add'));
    $this->assertText(t('Enter a valid IP address.'));

    // Submit an empty form.
    $edit = array();
    $edit['ip'] = '';
    $this->drupalPost('admin/config/people/ip-blocking', $edit, t('Add'));
    $this->assertText(t('Enter a valid IP address.'));

    // Pass an IP address as a URL parameter and submit it.
    $submit_ip = '*******';
    $this->drupalPost('admin/config/people/ip-blocking/' . $submit_ip, NULL, t('Add'));
    $ip = db_query("SELECT iid from {blocked_ips} WHERE ip = :ip", array(':ip' => $submit_ip))->fetchField();
    $this->assertTrue($ip, t('IP address found in database'));
    $this->assertRaw(t('The IP address %ip has been blocked.', array('%ip' => $submit_ip)), t('IP address was blocked.'));

    // Submit your own IP address. This fails, although it works when testing manually.
     // TODO: on some systems this test fails due to a bug or inconsistency in cURL.
     // $edit = array();
     // $edit['ip'] = ip_address();
     // $this->drupalPost('admin/config/people/ip-blocking', $edit, t('Save'));
     // $this->assertText(t('You may not block your own IP address.'));
  }

  /**
   * Test duplicate IP addresses are not present in the 'blocked_ips' table.
   */
  function testDuplicateIpAddress() {
    drupal_static_reset('ip_address');
    $submit_ip = $_SERVER['REMOTE_ADDR'] = '***********';
    system_block_ip_action();
    system_block_ip_action();
    $ip_count = db_query("SELECT COUNT(*) from {blocked_ips} WHERE ip = :ip", array(':ip' => $submit_ip))->fetchColumn();
    $this->assertEqual('1', $ip_count);
    drupal_static_reset('ip_address');
    $submit_ip = $_SERVER['REMOTE_ADDR'] = ' ';
    system_block_ip_action();
    system_block_ip_action();
    system_block_ip_action();
    $ip_count = db_query("SELECT COUNT(*) from {blocked_ips} WHERE ip = :ip", array(':ip' => $submit_ip))->fetchColumn();
    $this->assertEqual('1', $ip_count);
  }
}

class CronRunTestCase extends DrupalWebTestCase {
  /**
   * Implement getInfo().
   */
  public static function getInfo() {
    return array(
      'name' => 'Cron run',
      'description' => 'Test cron run.',
      'group' => 'System'
    );
  }

  function setUp() {
    parent::setUp(array('common_test', 'common_test_cron_helper'));
  }

  /**
   * Test cron runs.
   */
  function testCronRun() {
    global $base_url;

    // Run cron anonymously without any cron key.
    $this->drupalGet($base_url . '/cron.php', array('external' => TRUE));
    $this->assertResponse(403);

    // Run cron anonymously with a random cron key.
    $key = $this->randomName(16);
    $this->drupalGet($base_url . '/cron.php', array('external' => TRUE, 'query' => array('cron_key' => $key)));
    $this->assertResponse(403);

    // Run cron anonymously with the valid cron key.
    $key = variable_get('cron_key', 'drupal');
    $this->drupalGet($base_url . '/cron.php', array('external' => TRUE, 'query' => array('cron_key' => $key)));
    $this->assertResponse(200);
  }

  /**
   * Ensure that the automatic cron run feature is working.
   *
   * In these tests we do not use REQUEST_TIME to track start time, because we
   * need the exact time when cron is triggered.
   */
  function testAutomaticCron() {
    // Ensure cron does not run when the cron threshold is enabled and was
    // not passed.
    $cron_last = time();
    $cron_safe_threshold = 100;
    variable_set('cron_last', $cron_last);
    variable_set('cron_safe_threshold', $cron_safe_threshold);
    $this->drupalGet('');
    $this->assertTrue($cron_last == variable_get('cron_last', NULL), 'Cron does not run when the cron threshold is not passed.');

    // Test if cron runs when the cron threshold was passed.
    $cron_last = time() - 200;
    variable_set('cron_last', $cron_last);
    $this->drupalGet('');
    sleep(1);
    $this->assertTrue($cron_last < variable_get('cron_last', NULL), 'Cron runs when the cron threshold is passed.');

    // Disable the cron threshold through the interface.
    $admin_user = $this->drupalCreateUser(array('administer site configuration'));
    $this->drupalLogin($admin_user);
    $this->drupalPost('admin/config/system/cron', array('cron_safe_threshold' => 0), t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'));
    $this->drupalLogout();

    // Test if cron does not run when the cron threshold is disabled.
    $cron_last = time() - 200;
    variable_set('cron_last', $cron_last);
    $this->drupalGet('');
    $this->assertTrue($cron_last == variable_get('cron_last', NULL), 'Cron does not run when the cron threshold is disabled.');
  }

  /**
   * Ensure that temporary files are removed.
   *
   * Create files for all the possible combinations of age and status. We are
   * using UPDATE statements rather than file_save() because it would set the
   * timestamp.
   */
  function testTempFileCleanup() {
    // Temporary file that is older than DRUPAL_MAXIMUM_TEMP_FILE_AGE.
    $temp_old = file_save_data('');
    db_update('file_managed')
      ->fields(array(
        'status' => 0,
        'timestamp' => 1,
      ))
      ->condition('fid', $temp_old->fid)
      ->execute();
    $this->assertTrue(file_exists($temp_old->uri), 'Old temp file was created correctly.');

    // Temporary file that is less than DRUPAL_MAXIMUM_TEMP_FILE_AGE.
    $temp_new = file_save_data('');
    db_update('file_managed')
      ->fields(array('status' => 0))
      ->condition('fid', $temp_new->fid)
      ->execute();
    $this->assertTrue(file_exists($temp_new->uri), 'New temp file was created correctly.');

    // Permanent file that is older than DRUPAL_MAXIMUM_TEMP_FILE_AGE.
    $perm_old = file_save_data('');
    db_update('file_managed')
      ->fields(array('timestamp' => 1))
      ->condition('fid', $temp_old->fid)
      ->execute();
    $this->assertTrue(file_exists($perm_old->uri), 'Old permanent file was created correctly.');

    // Permanent file that is newer than DRUPAL_MAXIMUM_TEMP_FILE_AGE.
    $perm_new = file_save_data('');
    $this->assertTrue(file_exists($perm_new->uri), 'New permanent file was created correctly.');

    // Run cron and then ensure that only the old, temp file was deleted.
    $this->cronRun();
    $this->assertFalse(file_exists($temp_old->uri), 'Old temp file was correctly removed.');
    $this->assertTrue(file_exists($temp_new->uri), 'New temp file was correctly ignored.');
    $this->assertTrue(file_exists($perm_old->uri), 'Old permanent file was correctly ignored.');
    $this->assertTrue(file_exists($perm_new->uri), 'New permanent file was correctly ignored.');
  }

  /**
   * Make sure exceptions thrown on hook_cron() don't affect other modules.
   */
  function testCronExceptions() {
    variable_del('common_test_cron');
    // The common_test module throws an exception. If it isn't caught, the tests
    // won't finish successfully.
    // The common_test_cron_helper module sets the 'common_test_cron' variable.
    $this->cronRun();
    $result = variable_get('common_test_cron');
    $this->assertEqual($result, 'success', 'Cron correctly handles exceptions thrown during hook_cron() invocations.');
  }

  /**
   * Ensure that the manual cron run is working.
   */
  function testManualCron() {
    $admin_user = $this->drupalCreateUser(array('administer site configuration'));
    $this->drupalLogin($admin_user);

    $this->drupalGet('admin/reports/status/run-cron');
    $this->assertResponse(403);

    $this->drupalGet('admin/reports/status');
    $this->clickLink(t('run cron manually'));
    $this->assertResponse(200);
    $this->assertText(t('Cron ran successfully.'));
  }

  /**
   * Tests that hook_flush_caches() is not invoked on every single cron run.
   *
   * @see system_cron()
   */
  public function testCronCacheExpiration() {
    module_enable(array('system_cron_test'));
    variable_del('system_cron_test_flush_caches');

    // Invoke cron the first time: hook_flush_caches() should be called and then
    // get cached.
    drupal_cron_run();
    $this->assertEqual(variable_get('system_cron_test_flush_caches'), 1, 'hook_flush_caches() was invoked the first time.');
    $cache = cache_get('system_cache_tables');
    $this->assertEqual(empty($cache), FALSE, 'Cache is filled with cache table data.');

    // Run cron again and ensure that hook_flush_caches() is not called.
    variable_del('system_cron_test_flush_caches');
    drupal_cron_run();
    $this->assertNull(variable_get('system_cron_test_flush_caches'), 'hook_flush_caches() was not invoked the second time.');
  }

}

/**
 * Test execution of the cron queue.
 */
class CronQueueTestCase extends DrupalWebTestCase {
  /**
   * Implement getInfo().
   */
  public static function getInfo() {
    return array(
      'name' => 'Cron queue functionality',
      'description' => 'Tests the cron queue runner.',
      'group' => 'System'
    );
  }

  function setUp() {
    parent::setUp(array('common_test', 'common_test_cron_helper', 'cron_queue_test'));
  }

  /**
   * Tests that exceptions thrown by workers are handled properly.
   */
  function testExceptions() {
    $queue = DrupalQueue::get('cron_queue_test_exception');

    // Enqueue an item for processing.
    $queue->createItem(array($this->randomName() => $this->randomName()));

    // Run cron; the worker for this queue should throw an exception and handle
    // it.
    $this->cronRun();

    // The item should be left in the queue.
    $this->assertEqual($queue->numberOfItems(), 1, 'Failing item still in the queue after throwing an exception.');
  }

  /**
   * Tests worker defined as a class method callable.
   */
  function testCallable() {
    $queue = DrupalQueue::get('cron_queue_test_callback');

    // Enqueue an item for processing.
    $queue->createItem(array($this->randomName() => $this->randomName()));

    // Run cron; the worker should perform the task and delete the item from the
    // queue.
    $this->cronRun();

    // The queue should be empty.
    $this->assertEqual($queue->numberOfItems(), 0);
  }

}

class AdminMetaTagTestCase extends DrupalWebTestCase {
  /**
   * Implement getInfo().
   */
  public static function getInfo() {
    return array(
      'name' => 'Fingerprinting meta tag',
      'description' => 'Confirm that the fingerprinting meta tag appears as expected.',
      'group' => 'System'
    );
  }

  /**
   * Verify that the meta tag HTML is generated correctly.
   */
  public function testMetaTag() {
    list($version, ) = explode('.', VERSION);
    $string = '<meta name="Generator" content="Drupal ' . $version . ' (http://drupal.org)" />';
    $this->drupalGet('node');
    $this->assertRaw($string, 'Fingerprinting meta tag generated correctly.', 'System');
  }
}

/**
 * Tests custom access denied functionality.
 */
class AccessDeniedTestCase extends DrupalWebTestCase {
  protected $admin_user;

  public static function getInfo() {
    return array(
      'name' => '403 functionality',
      'description' => 'Tests page access denied functionality, including custom 403 pages.',
      'group' => 'System'
    );
  }

  function setUp() {
    parent::setUp();

    // Create an administrative user.
    $this->admin_user = $this->drupalCreateUser(array('access administration pages', 'administer site configuration', 'administer blocks'));
  }

  function testAccessDenied() {
    $this->drupalGet('admin');
    $this->assertText(t('Access denied'), 'Found the default 403 page');
    $this->assertResponse(403);

    $this->drupalLogin($this->admin_user);
    $edit = array(
      'title' => $this->randomName(10),
      'body' => array(LANGUAGE_NONE => array(array('value' => $this->randomName(100)))),
    );
    $node = $this->drupalCreateNode($edit);

    // Use a custom 403 page.
    $this->drupalPost('admin/config/system/site-information', array('site_403' => 'node/' . $node->nid), t('Save configuration'));

    $this->drupalLogout();
    $this->drupalGet('admin');
    $this->assertText($node->title, 'Found the custom 403 page');

    // Logout and check that the user login block is shown on custom 403 pages.
    $this->drupalLogout();

    $this->drupalGet('admin');
    $this->assertText($node->title, 'Found the custom 403 page');
    $this->assertText(t('User login'), 'Blocks are shown on the custom 403 page');

    // Log back in and remove the custom 403 page.
    $this->drupalLogin($this->admin_user);
    $this->drupalPost('admin/config/system/site-information', array('site_403' => ''), t('Save configuration'));

    // Logout and check that the user login block is shown on default 403 pages.
    $this->drupalLogout();

    $this->drupalGet('admin');
    $this->assertText(t('Access denied'), 'Found the default 403 page');
    $this->assertResponse(403);
    $this->assertText(t('User login'), 'Blocks are shown on the default 403 page');

    // Log back in, set the custom 403 page to /user and remove the block
    $this->drupalLogin($this->admin_user);
    variable_set('site_403', 'user');
    $this->drupalPost('admin/structure/block', array('blocks[user_login][region]' => '-1'), t('Save blocks'));

    // Check that we can log in from the 403 page.
    $this->drupalLogout();
    $edit = array(
      'name' => $this->admin_user->name,
      'pass' => $this->admin_user->pass_raw,
    );
    $this->drupalPost('admin/config/system/site-information', $edit, t('Log in'));

    // Check that we're still on the same page.
    $this->assertText(t('Site information'));

    // Check batch page response.
    $query_parameters = array(
      ':type' => 'php',
      ':severity' => WATCHDOG_WARNING,
    );
    $this->assertEqual(db_query('SELECT COUNT(*) FROM {watchdog} WHERE type = :type AND severity = :severity', $query_parameters)->fetchField(), 0, 'No warning message appears in the logs before accessing the batch page.');
    $this->drupalGet('batch');
    $this->assertResponse(403);
    $this->assertEqual(db_query('SELECT COUNT(*) FROM {watchdog} WHERE type = :type AND severity = :severity', $query_parameters)->fetchField(), 0, 'No warning message appears in the logs after accessing the batch page.');
  }
}

class PageNotFoundTestCase extends DrupalWebTestCase {
  protected $admin_user;

  /**
   * Implement getInfo().
   */
  public static function getInfo() {
    return array(
      'name' => '404 functionality',
      'description' => "Tests page not found functionality, including custom 404 pages.",
      'group' => 'System'
    );
  }

  /**
   * Implement setUp().
   */
  function setUp() {
    parent::setUp();

    // Create an administrative user.
    $this->admin_user = $this->drupalCreateUser(array('administer site configuration'));
    $this->drupalLogin($this->admin_user);
  }

  function testPageNotFound() {
    $this->drupalGet($this->randomName(10));
    $this->assertText(t('Page not found'), 'Found the default 404 page');

    $edit = array(
      'title' => $this->randomName(10),
      'body' => array(LANGUAGE_NONE => array(array('value' => $this->randomName(100)))),
    );
    $node = $this->drupalCreateNode($edit);

    // As node IDs must be integers, make sure requests for non-integer IDs
    // return a page not found error.
    $this->drupalGet('node/invalid');
    $this->assertResponse(404);

    // Use a custom 404 page.
    $this->drupalPost('admin/config/system/site-information', array('site_404' => 'node/' . $node->nid), t('Save configuration'));

    $this->drupalGet($this->randomName(10));
    $this->assertText($node->title, 'Found the custom 404 page');
  }
}

/**
 * Tests site maintenance functionality.
 */
class SiteMaintenanceTestCase extends DrupalWebTestCase {
  protected $admin_user;
  protected $user;

  public static function getInfo() {
    return array(
      'name' => 'Site maintenance mode functionality',
      'description' => 'Test access to site while in maintenance mode.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp();

    // Create a user allowed to access site in maintenance mode.
    $this->user = $this->drupalCreateUser(array('access site in maintenance mode'));
    // Create an administrative user.
    $this->admin_user = $this->drupalCreateUser(array('administer site configuration', 'access site in maintenance mode'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Verify site maintenance mode functionality.
   */
  function testSiteMaintenance() {
    // Turn on maintenance mode.
    $edit = array(
      'maintenance_mode' => 1,
    );
    $this->drupalPost('admin/config/development/maintenance', $edit, t('Save configuration'));

    $admin_message = t('Operating in maintenance mode. <a href="@url">Go online.</a>', array('@url' => url('admin/config/development/maintenance')));
    $user_message = t('Operating in maintenance mode.');
    $offline_message = t('@site is currently under maintenance. We should be back shortly. Thank you for your patience.', array('@site' => variable_get('site_name', 'Drupal')));

    $this->drupalGet('');
    $this->assertRaw($admin_message, 'Found the site maintenance mode message.');

    // Logout and verify that offline message is displayed.
    $this->drupalLogout();
    $this->drupalGet('');
    $this->assertText($offline_message);
    $this->drupalGet('node');
    $this->assertText($offline_message);
    $this->drupalGet('user/register');
    $this->assertText($offline_message);

    // Verify that user is able to log in.
    $this->drupalGet('user');
    $this->assertNoText($offline_message);
    $this->drupalGet('user/login');
    $this->assertNoText($offline_message);

    // Log in user and verify that maintenance mode message is displayed
    // directly after login.
    $edit = array(
      'name' => $this->user->name,
      'pass' => $this->user->pass_raw,
    );
    $this->drupalPost(NULL, $edit, t('Log in'));
    $this->assertText($user_message);

    // Log in administrative user and configure a custom site offline message.
    $this->drupalLogout();
    $this->drupalLogin($this->admin_user);
    $this->drupalGet('admin/config/development/maintenance');
    $this->assertNoRaw($admin_message, 'Site maintenance mode message not displayed.');

    $offline_message = 'Sorry, not online.';
    $edit = array(
      'maintenance_mode_message' => $offline_message,
    );
    $this->drupalPost(NULL, $edit, t('Save configuration'));

    // Logout and verify that custom site offline message is displayed.
    $this->drupalLogout();
    $this->drupalGet('');
    $this->assertRaw($offline_message, 'Found the site offline message.');

    // Verify that custom site offline message is not displayed on user/password.
    $this->drupalGet('user/password');
    $this->assertText(t('Username or e-mail address'), 'Anonymous users can access user/password');

    // Submit password reset form.
    $edit = array(
      'name' => $this->user->name,
    );
    $this->drupalPost('user/password', $edit, t('E-mail new password'));
    $mails = $this->drupalGetMails();
    $start = strpos($mails[0]['body'], 'user/reset/'. $this->user->uid);
    $path = substr($mails[0]['body'], $start, 66 + strlen($this->user->uid));

    // Log in with temporary login link.
    $this->drupalPost($path, array(), t('Log in'));
    $this->assertText($user_message);
  }
}

/**
 * Tests generic date and time handling capabilities of Drupal.
 */
class DateTimeFunctionalTest extends DrupalWebTestCase {
  protected $admin_user;

  public static function getInfo() {
    return array(
      'name' => 'Date and time',
      'description' => 'Configure date and time settings. Test date formatting and time zone handling, including daylight saving time.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp(array('locale'));

    // Create admin user and log in admin user.
    $this->admin_user = $this->drupalCreateUser(array('administer site configuration'));
    $this->drupalLogin($this->admin_user);
  }


  /**
   * Test time zones and DST handling.
   */
  function testTimeZoneHandling() {
    // Setup date/time settings for Honolulu time.
    variable_set('date_default_timezone', 'Pacific/Honolulu');
    variable_set('configurable_timezones', 0);
    variable_set('date_format_medium', 'Y-m-d H:i:s O');

    // Create some nodes with different authored-on dates.
    $date1 = '2007-01-31 21:00:00 -1000';
    $date2 = '2007-07-31 21:00:00 -1000';
    $node1 = $this->drupalCreateNode(array('created' => strtotime($date1), 'type' => 'article'));
    $node2 = $this->drupalCreateNode(array('created' => strtotime($date2), 'type' => 'article'));

    // Confirm date format and time zone.
    $this->drupalGet("node/$node1->nid");
    $this->assertText('2007-01-31 21:00:00 -1000', 'Date should be identical, with GMT offset of -10 hours.');
    $this->drupalGet("node/$node2->nid");
    $this->assertText('2007-07-31 21:00:00 -1000', 'Date should be identical, with GMT offset of -10 hours.');

    // Set time zone to Los Angeles time.
    variable_set('date_default_timezone', 'America/Los_Angeles');

    // Confirm date format and time zone.
    $this->drupalGet("node/$node1->nid");
    $this->assertText('2007-01-31 23:00:00 -0800', 'Date should be two hours ahead, with GMT offset of -8 hours.');
    $this->drupalGet("node/$node2->nid");
    $this->assertText('2007-08-01 00:00:00 -0700', 'Date should be three hours ahead, with GMT offset of -7 hours.');
  }

  /**
   * Test date type configuration.
   */
  function testDateTypeConfiguration() {
    // Confirm system date types appear.
    $this->drupalGet('admin/config/regional/date-time');
    $this->assertText(t('Medium'), 'System date types appear in date type list.');
    $this->assertNoRaw('href="/admin/config/regional/date-time/types/medium/delete"', 'No delete link appear for system date types.');

    // Add custom date type.
    $this->clickLink(t('Add date type'));
    $date_type = strtolower($this->randomName(8));
    $machine_name = 'machine_' . $date_type;
    $date_format = 'd.m.Y - H:i';
    $edit = array(
      'date_type' => $date_type,
      'machine_name' => $machine_name,
      'date_format' => $date_format,
    );
    $this->drupalPost('admin/config/regional/date-time/types/add', $edit, t('Add date type'));
    $this->assertEqual($this->getUrl(), url('admin/config/regional/date-time', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertText(t('New date type added successfully.'), 'Date type added confirmation message appears.');
    $this->assertText($date_type, 'Custom date type appears in the date type list.');
    $this->assertText(t('delete'), 'Delete link for custom date type appears.');

    // Delete custom date type.
    $this->clickLink(t('delete'));
    $this->drupalPost('admin/config/regional/date-time/types/' . $machine_name . '/delete', array(), t('Remove'));
    $this->assertEqual($this->getUrl(), url('admin/config/regional/date-time', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertText(t('Removed date type ' . $date_type), 'Custom date type removed.');
  }

  /**
   * Test date format configuration.
   */
  function testDateFormatConfiguration() {
    // Confirm 'no custom date formats available' message appears.
    $this->drupalGet('admin/config/regional/date-time/formats');
    $this->assertText(t('No custom date formats available.'), 'No custom date formats message appears.');

    // Add custom date format.
    $this->clickLink(t('Add format'));
    $edit = array(
      'date_format' => 'Y',
    );
    $this->drupalPost('admin/config/regional/date-time/formats/add', $edit, t('Add format'));
    $this->assertEqual($this->getUrl(), url('admin/config/regional/date-time/formats', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertNoText(t('No custom date formats available.'), 'No custom date formats message does not appear.');
    $this->assertText(t('Custom date format added.'), 'Custom date format added.');

    // Ensure custom date format appears in date type configuration options.
    $this->drupalGet('admin/config/regional/date-time');
    $this->assertRaw('<option value="Y">', 'Custom date format appears in options.');

    // Edit custom date format.
    $this->drupalGet('admin/config/regional/date-time/formats');
    $this->clickLink(t('edit'));
    $edit = array(
      'date_format' => 'Y m',
    );
    $this->drupalPost($this->getUrl(), $edit, t('Save format'));
    $this->assertEqual($this->getUrl(), url('admin/config/regional/date-time/formats', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertText(t('Custom date format updated.'), 'Custom date format successfully updated.');

    // Check that ajax callback is protected by CSRF token.
    $this->drupalGet('admin/config/regional/date-time/formats/lookup', array('query' => array('format' => 'Y m d')));
    $this->assertResponse(403, 'Access denied with no token');
    $this->drupalGet('admin/config/regional/date-time/formats/lookup', array('query' => array('token' => 'invalid', 'format' => 'Y m d')));
    $this->assertResponse(403, 'Access denied with invalid token');
    $this->drupalGet('admin/config/regional/date-time/formats');
    $this->clickLink(t('edit'));
    $settings = $this->drupalGetSettings();
    $lookup_url = $settings['dateTime']['date-format']['lookup'];
    preg_match('/token=([^&]+)/', $lookup_url, $matches);
    $this->assertFalse(empty($matches[1]), 'Found token value');
    $this->drupalGet('admin/config/regional/date-time/formats/lookup', array('query' => array('token' => $matches[1], 'format' => 'Y m d')));
    $this->assertResponse(200, 'Access allowed with valid token');
    $this->assertText(format_date(time(), 'custom', 'Y m d'));

    // Delete custom date format.
    $this->drupalGet('admin/config/regional/date-time/formats');
    $this->clickLink(t('delete'));
    $this->drupalPost($this->getUrl(), array(), t('Remove'));
    $this->assertEqual($this->getUrl(), url('admin/config/regional/date-time/formats', array('absolute' => TRUE)), 'Correct page redirection.');
    $this->assertText(t('Removed date format'), 'Custom date format removed successfully.');
  }

  /**
   * Test if the date formats are stored properly.
   */
  function testDateFormatStorage() {
    $date_format = array(
      'type' => 'short',
      'format' => 'dmYHis',
      'locked' => 0,
      'is_new' => 1,
    );
    system_date_format_save($date_format);

    $format = db_select('date_formats', 'df')
      ->fields('df', array('format'))
      ->condition('type', 'short')
      ->condition('format', 'dmYHis')
      ->execute()
      ->fetchField();
    $this->verbose($format);
    $this->assertEqual('dmYHis', $format, 'Unlocalized date format resides in general table.');

    $format = db_select('date_format_locale', 'dfl')
      ->fields('dfl', array('format'))
      ->condition('type', 'short')
      ->condition('format', 'dmYHis')
      ->execute()
      ->fetchField();
    $this->assertFalse($format, 'Unlocalized date format resides not in localized table.');

    // Enable German language
    locale_add_language('de', NULL, NULL, LANGUAGE_LTR, '', '', TRUE, 'en');

    $date_format = array(
      'type' => 'short',
      'format' => 'YMDHis',
      'locales' => array('de', 'tr'),
      'locked' => 0,
      'is_new' => 1,
    );
    system_date_format_save($date_format);

    $format = db_select('date_format_locale', 'dfl')
      ->fields('dfl', array('format'))
      ->condition('type', 'short')
      ->condition('format', 'YMDHis')
      ->condition('language', 'de')
      ->execute()
      ->fetchField();
    $this->assertEqual('YMDHis', $format, 'Localized date format resides in localized table.');

    $format = db_select('date_formats', 'df')
      ->fields('df', array('format'))
      ->condition('type', 'short')
      ->condition('format', 'YMDHis')
      ->execute()
      ->fetchField();
    $this->assertEqual('YMDHis', $format, 'Localized date format resides in general table too.');

    $format = db_select('date_format_locale', 'dfl')
      ->fields('dfl', array('format'))
      ->condition('type', 'short')
      ->condition('format', 'YMDHis')
      ->condition('language', 'tr')
      ->execute()
      ->fetchColumn();
    $this->assertFalse($format, 'Localized date format for disabled language is ignored.');
  }
}

/**
 * Tests date format configuration.
 */
class DateFormatTestCase extends DrupalWebTestCase {
  protected $admin_user;

  public static function getInfo() {
    return array(
      'name' => 'Date format',
      'description' => 'Test date format configuration and defaults.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp();

    // Create admin user and log in admin user.
    $this->admin_user = $this->drupalCreateUser(array('administer site configuration'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Test the default date type formats are consistent.
   */
  function testDefaultDateFormats() {
    // These are the default format values from format_date().
    $default_formats = array(
      'short' => 'm/d/Y - H:i',
      'medium' => 'D, m/d/Y - H:i',
      'long' => 'l, F j, Y - H:i',
    );

    // Clear the date format variables.
    variable_del('date_format_short');
    variable_del('date_format_medium');
    variable_del('date_format_long');

    $this->drupalGet('admin/config/regional/date-time');

    foreach ($default_formats as $format_name => $format_value) {
      $id = 'edit-date-format-' . $format_name;
      // Check that the configuration fields match the default format.
      $this->assertOptionSelected(
        $id,
        $format_value,
        format_string('The @type format type matches the expected format @format.',
        array(
          '@type' => $format_name,
          '@format' => $format_value,
        )
      ));
    }
  }
}

class PageTitleFiltering extends DrupalWebTestCase {
  protected $content_user;
  protected $saved_title;

  /**
   * Implement getInfo().
   */
  public static function getInfo() {
    return array(
      'name' => 'HTML in page titles',
      'description' => 'Tests correct handling or conversion by drupal_set_title() and drupal_get_title() and checks the correct escaping of site name and slogan.',
      'group' => 'System'
    );
  }

  /**
   * Implement setUp().
   */
  function setUp() {
    parent::setUp();

    $this->content_user = $this->drupalCreateUser(array('create page content', 'access content', 'administer themes', 'administer site configuration'));
    $this->drupalLogin($this->content_user);
    $this->saved_title = drupal_get_title();
  }

  /**
   * Reset page title.
   */
  function tearDown() {
    // Restore the page title.
    drupal_set_title($this->saved_title, PASS_THROUGH);

    parent::tearDown();
  }

  /**
   * Tests the handling of HTML by drupal_set_title() and drupal_get_title()
   */
  function testTitleTags() {
    $title = "string with <em>HTML</em>";
    // drupal_set_title's $filter is CHECK_PLAIN by default, so the title should be
    // returned with check_plain().
    drupal_set_title($title, CHECK_PLAIN);
    $this->assertTrue(strpos(drupal_get_title(), '<em>') === FALSE, 'Tags in title converted to entities when $output is CHECK_PLAIN.');
    // drupal_set_title's $filter is passed as PASS_THROUGH, so the title should be
    // returned with HTML.
    drupal_set_title($title, PASS_THROUGH);
    $this->assertTrue(strpos(drupal_get_title(), '<em>') !== FALSE, 'Tags in title are not converted to entities when $output is PASS_THROUGH.');
    // Generate node content.
    $langcode = LANGUAGE_NONE;
    $edit = array(
      "title" => '!SimpleTest! ' . $title . $this->randomName(20),
      "body[$langcode][0][value]" => '!SimpleTest! test body' . $this->randomName(200),
    );
    // Create the node with HTML in the title.
    $this->drupalPost('node/add/page', $edit, t('Save'));

    $node = $this->drupalGetNodeByTitle($edit["title"]);
    $this->assertNotNull($node, 'Node created and found in database');
    $this->drupalGet("node/" . $node->nid);
    $this->assertText(check_plain($edit["title"]), 'Check to make sure tags in the node title are converted.');
  }
  /**
   * Test if the title of the site is XSS proof.
   */
  function testTitleXSS() {
    // Set some title with JavaScript and HTML chars to escape.
    $title = '</title><script type="text/javascript">alert("Title XSS!");</script> & < > " \' ';
    $title_filtered = check_plain($title);

    $slogan = '<script type="text/javascript">alert("Slogan XSS!");</script>';
    $slogan_filtered = filter_xss_admin($slogan);

    // Activate needed appearance settings.
    $edit = array(
      'toggle_name'           => TRUE,
      'toggle_slogan'         => TRUE,
      'toggle_main_menu'      => TRUE,
      'toggle_secondary_menu' => TRUE,
    );
    $this->drupalPost('admin/appearance/settings', $edit, t('Save configuration'));

    // Set title and slogan.
    $edit = array(
      'site_name'    => $title,
      'site_slogan'  => $slogan,
    );
    $this->drupalPost('admin/config/system/site-information', $edit, t('Save configuration'));

    // Load frontpage.
    $this->drupalGet('');

    // Test the title.
    $this->assertNoRaw($title, 'Check for the unfiltered version of the title.');
    // Adding </title> so we do not test the escaped version from drupal_set_title().
    $this->assertRaw($title_filtered . '</title>', 'Check for the filtered version of the title.');

    // Test the slogan.
    $this->assertNoRaw($slogan, 'Check for the unfiltered version of the slogan.');
    $this->assertRaw($slogan_filtered, 'Check for the filtered version of the slogan.');
  }
}

/**
 * Test front page functionality and administration.
 */
class FrontPageTestCase extends DrupalWebTestCase {
  protected $admin_user;
  protected $node_path;

  public static function getInfo() {
    return array(
      'name' => 'Front page',
      'description' => 'Tests front page functionality and administration.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp('system_test');

    // Create admin user, log in admin user, and create one node.
    $this->admin_user = $this->drupalCreateUser(array('access content', 'administer site configuration'));
    $this->drupalLogin($this->admin_user);
    $this->node_path = "node/" . $this->drupalCreateNode(array('promote' => 1))->nid;

    // Enable front page logging in system_test.module.
    variable_set('front_page_output', 1);
  }

  /**
   * Test front page functionality.
   */
  function testDrupalIsFrontPage() {
    $this->drupalGet('');
    $this->assertText(t('On front page.'), 'Path is the front page.');
    $this->drupalGet('node');
    $this->assertText(t('On front page.'), 'Path is the front page.');
    $this->drupalGet($this->node_path);
    $this->assertNoText(t('On front page.'), 'Path is not the front page.');

    // Change the front page to an invalid path.
    $edit = array('site_frontpage' => 'kittens');
    $this->drupalPost('admin/config/system/site-information', $edit, t('Save configuration'));
    $this->assertText(t("The path '@path' is either invalid or you do not have access to it.", array('@path' => $edit['site_frontpage'])));

    // Change the front page to a valid path.
    $edit['site_frontpage'] = $this->node_path;
    $this->drupalPost('admin/config/system/site-information', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'The front page path has been saved.');

    $this->drupalGet('');
    $this->assertText(t('On front page.'), 'Path is the front page.');
    $this->drupalGet('node');
    $this->assertNoText(t('On front page.'), 'Path is not the front page.');
    $this->drupalGet($this->node_path);
    $this->assertText(t('On front page.'), 'Path is the front page.');
  }
}

class SystemBlockTestCase extends DrupalWebTestCase {
  protected $profile = 'testing';

  public static function getInfo() {
    return array(
      'name' => 'Block functionality',
      'description' => 'Configure and move powered-by block.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp('block');

    // Create and login user
    $admin_user = $this->drupalCreateUser(array('administer blocks', 'access administration pages'));
    $this->drupalLogin($admin_user);
  }

  /**
   * Test displaying and hiding the powered-by and help blocks.
   */
  function testSystemBlocks() {
    // Set block title and some settings to confirm that the interface is available.
    $this->drupalPost('admin/structure/block/manage/system/powered-by/configure', array('title' => $this->randomName(8)), t('Save block'));
    $this->assertText(t('The block configuration has been saved.'), t('Block configuration set.'));

    // Set the powered-by block to the footer region.
    $edit = array();
    $edit['blocks[system_powered-by][region]'] = 'footer';
    $edit['blocks[system_main][region]'] = 'content';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->assertText(t('The block settings have been updated.'), t('Block successfully moved to footer region.'));

    // Confirm that the block is being displayed.
    $this->drupalGet('node');
    $this->assertRaw('id="block-system-powered-by"', t('Block successfully being displayed on the page.'));

    // Set the block to the disabled region.
    $edit = array();
    $edit['blocks[system_powered-by][region]'] = '-1';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));

    // Confirm that the block is hidden.
    $this->assertNoRaw('id="block-system-powered-by"', t('Block no longer appears on page.'));

    // For convenience of developers, set the block to its default settings.
    $edit = array();
    $edit['blocks[system_powered-by][region]'] = 'footer';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->drupalPost('admin/structure/block/manage/system/powered-by/configure', array('title' => ''), t('Save block'));

    // Set the help block to the help region.
    $edit = array();
    $edit['blocks[system_help][region]'] = 'help';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));

    // Test displaying the help block with block caching enabled.
    variable_set('block_cache', TRUE);
    $this->drupalGet('admin/structure/block/add');
    $this->assertRaw(t('Use this page to create a new custom block.'));
    $this->drupalGet('admin/index');
    $this->assertRaw(t('This page shows you all available administration tasks for each module.'));
  }
}

/**
 * Test main content rendering fallback provided by system module.
 */
class SystemMainContentFallback extends DrupalWebTestCase {
  protected $admin_user;
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'Main content rendering fallback',
      'description' => ' Test system module main content rendering fallback.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp('system_test');

    // Create and login admin user.
    $this->admin_user = $this->drupalCreateUser(array(
      'access administration pages',
      'administer site configuration',
      'administer modules',
      'administer blocks',
      'administer nodes',
    ));
    $this->drupalLogin($this->admin_user);

    // Create a web user.
    $this->web_user = $this->drupalCreateUser(array('access user profiles', 'access content'));
  }

  /**
   * Test availability of main content.
   */
  function testMainContentFallback() {
    $edit = array();
    // Disable the dashboard module, which depends on the block module.
    $edit['modules[Core][dashboard][enable]'] = FALSE;
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');
    // Disable the block module.
    $edit['modules[Core][block][enable]'] = FALSE;
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');
    module_list(TRUE);
    $this->assertFalse(module_exists('block'), 'Block module disabled.');

    // At this point, no region is filled and fallback should be triggered.
    $this->drupalGet('admin/config/system/site-information');
    $this->assertField('site_name', 'Admin interface still available.');

    // Fallback should not trigger when another module is handling content.
    $this->drupalGet('system-test/main-content-handling');
    $this->assertRaw('id="system-test-content"', 'Content handled by another module');
    $this->assertText(t('Content to test main content fallback'), 'Main content still displayed.');

    // Fallback should trigger when another module
    // indicates that it is not handling the content.
    $this->drupalGet('system-test/main-content-fallback');
    $this->assertText(t('Content to test main content fallback'), 'Main content fallback properly triggers.');

    // Fallback should not trigger when another module is handling content.
    // Note that this test ensures that no duplicate
    // content gets created by the fallback.
    $this->drupalGet('system-test/main-content-duplication');
    $this->assertNoText(t('Content to test main content fallback'), 'Main content not duplicated.');

    // Request a user* page and see if it is displayed.
    $this->drupalLogin($this->web_user);
    $this->drupalGet('user/' . $this->web_user->uid . '/edit');
    $this->assertField('mail', 'User interface still available.');

    // Enable the block module again.
    $this->drupalLogin($this->admin_user);
    $edit = array();
    $edit['modules[Core][block][enable]'] = 'block';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');
    module_list(TRUE);
    $this->assertTrue(module_exists('block'), 'Block module re-enabled.');
  }
}

/**
 * Tests for the theme interface functionality.
 */
class SystemThemeFunctionalTest extends DrupalWebTestCase {
  protected $admin_user;
  protected $node;

  public static function getInfo() {
    return array(
      'name' => 'Theme interface functionality',
      'description' => 'Tests the theme interface functionality by enabling and switching themes, and using an administration theme.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp();

    $this->admin_user = $this->drupalCreateUser(array('access administration pages', 'view the administration theme', 'administer themes', 'bypass node access', 'administer blocks'));
    $this->drupalLogin($this->admin_user);
    $this->node = $this->drupalCreateNode();
  }

  /**
   * Test the theme settings form.
   */
  function testThemeSettings() {
    // Specify a filesystem path to be used for the logo.
    $file = current($this->drupalGetTestFiles('image'));
    $file_relative = strtr($file->uri, array('public:/' => variable_get('file_public_path', conf_path() . '/files')));
    $default_theme_path = 'themes/stark';

    $supported_paths = array(
      // Raw stream wrapper URI.
      $file->uri => array(
        'form' => file_uri_target($file->uri),
        'src' => file_create_url($file->uri),
      ),
      // Relative path within the public filesystem.
      file_uri_target($file->uri) => array(
        'form' => file_uri_target($file->uri),
        'src' => file_create_url($file->uri),
      ),
      // Relative path to a public file.
      $file_relative => array(
        'form' => $file_relative,
        'src' => file_create_url($file->uri),
      ),
      // Relative path to an arbitrary file.
      'misc/druplicon.png' => array(
        'form' => 'misc/druplicon.png',
        'src' => $GLOBALS['base_url'] . '/' . 'misc/druplicon.png',
      ),
      // Relative path to a file in a theme.
      $default_theme_path . '/logo.png' => array(
        'form' => $default_theme_path . '/logo.png',
        'src' => $GLOBALS['base_url'] . '/' . $default_theme_path . '/logo.png',
      ),
    );
    foreach ($supported_paths as $input => $expected) {
      $edit = array(
        'default_logo' => FALSE,
        'logo_path' => $input,
      );
      $this->drupalPost('admin/appearance/settings', $edit, t('Save configuration'));
      $this->assertNoText('The custom logo path is invalid.');
      $this->assertFieldByName('logo_path', $expected['form']);

      // Verify the actual 'src' attribute of the logo being output.
      $this->drupalGet('');
      $elements = $this->xpath('//*[@id=:id]/img', array(':id' => 'logo'));
      $this->assertEqual((string) $elements[0]['src'], $expected['src']);
    }

    $unsupported_paths = array(
      // Stream wrapper URI to non-existing file.
      'public://whatever.png',
      'private://whatever.png',
      'temporary://whatever.png',
      // Bogus stream wrapper URIs.
      'public:/whatever.png',
      '://whatever.png',
      ':whatever.png',
      'public://',
      // Relative path within the public filesystem to non-existing file.
      'whatever.png',
      // Relative path to non-existing file in public filesystem.
      variable_get('file_public_path', conf_path() . '/files') . '/whatever.png',
      // Semi-absolute path to non-existing file in public filesystem.
      '/' . variable_get('file_public_path', conf_path() . '/files') . '/whatever.png',
      // Relative path to arbitrary non-existing file.
      'misc/whatever.png',
      // Semi-absolute path to arbitrary non-existing file.
      '/misc/whatever.png',
      // Absolute paths to any local file (even if it exists).
      drupal_realpath($file->uri),
    );
    $this->drupalGet('admin/appearance/settings');
    foreach ($unsupported_paths as $path) {
      $edit = array(
        'default_logo' => FALSE,
        'logo_path' => $path,
      );
      $this->drupalPost(NULL, $edit, t('Save configuration'));
      $this->assertText('The custom logo path is invalid.');
    }

    // Upload a file to use for the logo.
    $edit = array(
      'default_logo' => FALSE,
      'logo_path' => '',
      'files[logo_upload]' => drupal_realpath($file->uri),
    );
    $this->drupalPost('admin/appearance/settings', $edit, t('Save configuration'));

    $fields = $this->xpath($this->constructFieldXpath('name', 'logo_path'));
    $uploaded_filename = 'public://' . $fields[0]['value'];

    $this->drupalGet('');
    $elements = $this->xpath('//*[@id=:id]/img', array(':id' => 'logo'));
    $this->assertEqual($elements[0]['src'], file_create_url($uploaded_filename));
  }


  /**
   * Test the individual per-theme settings form.
   */
  function testPerThemeSettings() {
    // Enable the test theme and the module that controls it. Clear caches in
    // between so that the module's hook_system_theme_info() implementation is
    // correctly registered.
    module_enable(array('theme_test'));
    drupal_flush_all_caches();
    theme_enable(array('test_theme'));

    // Test that the theme-specific settings form can be saved and that the
    // theme-specific checkbox is checked and unchecked as appropriate.
    $this->drupalGet('admin/appearance/settings/test_theme');
    $this->assertNoFieldChecked('edit-test-theme-checkbox', 'The test_theme_checkbox setting is unchecked.');
    $this->drupalPost(NULL, array('test_theme_checkbox' => TRUE), t('Save configuration'));
    $this->assertText('The test theme setting was saved.');
    $this->assertFieldChecked('edit-test-theme-checkbox', 'The test_theme_checkbox setting is checked.');
    $this->drupalPost(NULL, array('test_theme_checkbox' => FALSE), t('Save configuration'));
    $this->assertText('The test theme setting was saved.');
    $this->assertNoFieldChecked('edit-test-theme-checkbox', 'The test_theme_checkbox setting is unchecked.');
  }

  /**
   * Test the administration theme functionality.
   */
  function testAdministrationTheme() {
    theme_enable(array('stark'));
    variable_set('theme_default', 'stark');
    // Enable an administration theme and show it on the node admin pages.
    $edit = array(
      'admin_theme' => 'seven',
      'node_admin_theme' => TRUE,
    );
    $this->drupalPost('admin/appearance', $edit, t('Save configuration'));

    $this->drupalGet('admin/config');
    $this->assertRaw('themes/seven', 'Administration theme used on an administration page.');

    $this->drupalGet('node/' . $this->node->nid);
    $this->assertRaw('themes/stark', 'Site default theme used on node page.');

    $this->drupalGet('node/add');
    $this->assertRaw('themes/seven', 'Administration theme used on the add content page.');

    $this->drupalGet('node/' . $this->node->nid . '/edit');
    $this->assertRaw('themes/seven', 'Administration theme used on the edit content page.');

    // Disable the admin theme on the node admin pages.
    $edit = array(
      'node_admin_theme' => FALSE,
    );
    $this->drupalPost('admin/appearance', $edit, t('Save configuration'));

    $this->drupalGet('admin/config');
    $this->assertRaw('themes/seven', 'Administration theme used on an administration page.');

    $this->drupalGet('node/add');
    $this->assertRaw('themes/stark', 'Site default theme used on the add content page.');

    // Reset to the default theme settings.
    variable_set('theme_default', 'bartik');
    $edit = array(
      'admin_theme' => '0',
      'node_admin_theme' => FALSE,
    );
    $this->drupalPost('admin/appearance', $edit, t('Save configuration'));

    $this->drupalGet('admin');
    $this->assertRaw('themes/bartik', 'Site default theme used on administration page.');

    $this->drupalGet('node/add');
    $this->assertRaw('themes/bartik', 'Site default theme used on the add content page.');
  }

  /**
   * Test switching the default theme.
   */
  function testSwitchDefaultTheme() {
    // Enable "stark" and set it as the default theme.
    theme_enable(array('stark'));
    $this->drupalGet('admin/appearance');
    $this->clickLink(t('Set default'), 1);
    $this->assertTrue(variable_get('theme_default', '') == 'stark', 'Site default theme switched successfully.');

    // Test the default theme on the secondary links (blocks admin page).
    $this->drupalGet('admin/structure/block');
    $this->assertText('Stark(' . t('active tab') . ')', 'Default local task on blocks admin page is the default theme.');
    // Switch back to Bartik and test again to test that the menu cache is cleared.
    $this->drupalGet('admin/appearance');
    $this->clickLink(t('Set default'), 0);
    $this->drupalGet('admin/structure/block');
    $this->assertText('Bartik(' . t('active tab') . ')', 'Default local task on blocks admin page has changed.');
  }
}


/**
 * Test the basic queue functionality.
 */
class QueueTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Queue functionality',
      'description' => 'Queues and dequeues a set of items to check the basic queue functionality.',
      'group' => 'System',
    );
  }

  /**
   * Queues and dequeues a set of items to check the basic queue functionality.
   */
  function testQueue() {
    // Create two queues.
    $queue1 = DrupalQueue::get($this->randomName());
    $queue1->createQueue();
    $queue2 = DrupalQueue::get($this->randomName());
    $queue2->createQueue();

    // Create four items.
    $data = array();
    for ($i = 0; $i < 4; $i++) {
      $data[] = array($this->randomName() => $this->randomName());
    }

    // Queue items 1 and 2 in the queue1.
    $queue1->createItem($data[0]);
    $queue1->createItem($data[1]);

    // Retrieve two items from queue1.
    $items = array();
    $new_items = array();

    $items[] = $item = $queue1->claimItem();
    $new_items[] = $item->data;

    $items[] = $item = $queue1->claimItem();
    $new_items[] = $item->data;

    // First two dequeued items should match the first two items we queued.
    $this->assertEqual($this->queueScore($data, $new_items), 2, 'Two items matched');

    // Add two more items.
    $queue1->createItem($data[2]);
    $queue1->createItem($data[3]);

    $this->assertTrue($queue1->numberOfItems(), 'Queue 1 is not empty after adding items.');
    $this->assertFalse($queue2->numberOfItems(), 'Queue 2 is empty while Queue 1 has items');

    $items[] = $item = $queue1->claimItem();
    $new_items[] = $item->data;

    $items[] = $item = $queue1->claimItem();
    $new_items[] = $item->data;

    // All dequeued items should match the items we queued exactly once,
    // therefore the score must be exactly 4.
    $this->assertEqual($this->queueScore($data, $new_items), 4, 'Four items matched');

    // There should be no duplicate items.
    $this->assertEqual($this->queueScore($new_items, $new_items), 4, 'Four items matched');

    // Delete all items from queue1.
    foreach ($items as $item) {
      $queue1->deleteItem($item);
    }

    // Check that both queues are empty.
    $this->assertFalse($queue1->numberOfItems(), 'Queue 1 is empty');
    $this->assertFalse($queue2->numberOfItems(), 'Queue 2 is empty');
  }

  /**
   * This function returns the number of equal items in two arrays.
   */
  function queueScore($items, $new_items) {
    $score = 0;
    foreach ($items as $item) {
      foreach ($new_items as $new_item) {
        if ($item === $new_item) {
          $score++;
        }
      }
    }
    return $score;
  }
}

/**
 * Test token replacement in strings.
 */
class TokenReplaceTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Token replacement',
      'description' => 'Generates text using placeholders for dummy content to check token replacement.',
      'group' => 'System',
    );
  }

  /**
   * Creates a user and a node, then tests the tokens generated from them.
   */
  function testTokenReplacement() {
    // Create the initial objects.
    $account = $this->drupalCreateUser();
    $node = $this->drupalCreateNode(array('uid' => $account->uid));
    $node->title = '<blink>Blinking Text</blink>';
    global $user, $language;

    $source  = '[node:title]';         // Title of the node we passed in
    $source .= '[node:author:name]';   // Node author's name
    $source .= '[node:created:since]'; // Time since the node was created
    $source .= '[current-user:name]';  // Current user's name
    $source .= '[date:short]';         // Short date format of REQUEST_TIME
    $source .= '[user:name]';          // No user passed in, should be untouched
    $source .= '[bogus:token]';        // Non-existent token

    $target  = check_plain($node->title);
    $target .= check_plain($account->name);
    $target .= format_interval(REQUEST_TIME - $node->created, 2, $language->language);
    $target .= check_plain($user->name);
    $target .= format_date(REQUEST_TIME, 'short', '', NULL, $language->language);

    // Test that the clear parameter cleans out non-existent tokens.
    $result = token_replace($source, array('node' => $node), array('language' => $language, 'clear' => TRUE));
    $result = $this->assertEqual($target, $result, 'Valid tokens replaced while invalid tokens cleared out.');

    // Test without using the clear parameter (non-existent token untouched).
    $target .= '[user:name]';
    $target .= '[bogus:token]';
    $result = token_replace($source, array('node' => $node), array('language' => $language));
    $this->assertEqual($target, $result, 'Valid tokens replaced while invalid tokens ignored.');

    // Check that the results of token_generate are sanitized properly. This does NOT
    // test the cleanliness of every token -- just that the $sanitize flag is being
    // passed properly through the call stack and being handled correctly by a 'known'
    // token, [node:title].
    $raw_tokens = array('title' => '[node:title]');
    $generated = token_generate('node', $raw_tokens, array('node' => $node));
    $this->assertEqual($generated['[node:title]'], check_plain($node->title), 'Token sanitized.');

    $generated = token_generate('node', $raw_tokens, array('node' => $node), array('sanitize' => FALSE));
    $this->assertEqual($generated['[node:title]'], $node->title, 'Unsanitized token generated properly.');

    // Test token replacement when the string contains no tokens.
    $this->assertEqual(token_replace('No tokens here.'), 'No tokens here.');
  }

  /**
   * Test whether token-replacement works in various contexts.
   */
  function testSystemTokenRecognition() {
    global $language;

    // Generate prefixes and suffixes for the token context.
    $tests = array(
      array('prefix' => 'this is the ', 'suffix' => ' site'),
      array('prefix' => 'this is the', 'suffix' => 'site'),
      array('prefix' => '[', 'suffix' => ']'),
      array('prefix' => '', 'suffix' => ']]]'),
      array('prefix' => '[[[', 'suffix' => ''),
      array('prefix' => ':[:', 'suffix' => '--]'),
      array('prefix' => '-[-', 'suffix' => ':]:'),
      array('prefix' => '[:', 'suffix' => ']'),
      array('prefix' => '[site:', 'suffix' => ':name]'),
      array('prefix' => '[site:', 'suffix' => ']'),
    );

    // Check if the token is recognized in each of the contexts.
    foreach ($tests as $test) {
      $input = $test['prefix'] . '[site:name]' . $test['suffix'];
      $expected = $test['prefix'] . 'Drupal' . $test['suffix'];
      $output = token_replace($input, array(), array('language' => $language));
      $this->assertTrue($output == $expected, format_string('Token recognized in string %string', array('%string' => $input)));
    }
  }

  /**
   * Tests the generation of all system site information tokens.
   */
  function testSystemSiteTokenReplacement() {
    global $language;
    $url_options = array(
      'absolute' => TRUE,
      'language' => $language,
    );

    // Set a few site variables.
    variable_set('site_name', '<strong>Drupal<strong>');
    variable_set('site_slogan', '<blink>Slogan</blink>');

    // Generate and test sanitized tokens.
    $tests = array();
    $tests['[site:name]'] = check_plain(variable_get('site_name', 'Drupal'));
    $tests['[site:slogan]'] = check_plain(variable_get('site_slogan', ''));
    $tests['[site:mail]'] = '<EMAIL>';
    $tests['[site:url]'] = url('<front>', $url_options);
    $tests['[site:url-brief]'] = preg_replace(array('!^https?://!', '!/$!'), '', url('<front>', $url_options));
    $tests['[site:login-url]'] = url('user', $url_options);

    // Test to make sure that we generated something for each token.
    $this->assertFalse(in_array(0, array_map('strlen', $tests)), 'No empty tokens generated.');

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array(), array('language' => $language));
      $this->assertEqual($output, $expected, format_string('Sanitized system site information token %token replaced.', array('%token' => $input)));
    }

    // Generate and test unsanitized tokens.
    $tests['[site:name]'] = variable_get('site_name', 'Drupal');
    $tests['[site:slogan]'] = variable_get('site_slogan', '');

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array(), array('language' => $language, 'sanitize' => FALSE));
      $this->assertEqual($output, $expected, format_string('Unsanitized system site information token %token replaced.', array('%token' => $input)));
    }
  }

  /**
   * Tests the generation of all system date tokens.
   */
  function testSystemDateTokenReplacement() {
    global $language;

    // Set time to one hour before request.
    $date = REQUEST_TIME - 3600;

    // Generate and test tokens.
    $tests = array();
    $tests['[date:short]'] = format_date($date, 'short', '', NULL, $language->language);
    $tests['[date:medium]'] = format_date($date, 'medium', '', NULL, $language->language);
    $tests['[date:long]'] = format_date($date, 'long', '', NULL, $language->language);
    $tests['[date:custom:m/j/Y]'] = format_date($date, 'custom', 'm/j/Y', NULL, $language->language);
    $tests['[date:since]'] = format_interval((REQUEST_TIME - $date), 2, $language->language);
    $tests['[date:raw]'] = filter_xss($date);

    // Test to make sure that we generated something for each token.
    $this->assertFalse(in_array(0, array_map('strlen', $tests)), 'No empty tokens generated.');

    foreach ($tests as $input => $expected) {
      $output = token_replace($input, array('date' => $date), array('language' => $language));
      $this->assertEqual($output, $expected, format_string('Date token %token replaced.', array('%token' => $input)));
    }
  }
}

class InfoFileParserTestCase extends DrupalUnitTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Info file format parser',
      'description' => 'Tests proper parsing of a .info file formatted string.',
      'group' => 'System',
    );
  }

  /**
   * Test drupal_parse_info_format().
   */
  function testDrupalParseInfoFormat() {
    $config = '
simple = Value
quoted = " Value"
multiline = "Value
  Value"
array[] = Value1
array[] = Value2
array_assoc[a] = Value1
array_assoc[b] = Value2
array_deep[][][] = Value
array_deep_assoc[a][b][c] = Value
array_space[a b] = Value';

    $expected = array(
      'simple' => 'Value',
      'quoted' => ' Value',
      'multiline' => "Value\n  Value",
      'array' => array(
        0 => 'Value1',
        1 => 'Value2',
      ),
      'array_assoc' => array(
        'a' => 'Value1',
        'b' => 'Value2',
      ),
      'array_deep' => array(
        0 => array(
          0 => array(
            0 => 'Value',
          ),
        ),
      ),
      'array_deep_assoc' => array(
        'a' => array(
          'b' => array(
            'c' => 'Value',
          ),
        ),
      ),
      'array_space' => array(
        'a b' => 'Value',
      ),
    );

    $parsed = drupal_parse_info_format($config);

    $this->assertEqual($parsed['simple'], $expected['simple'], 'Set a simple value.');
    $this->assertEqual($parsed['quoted'], $expected['quoted'], 'Set a simple value in quotes.');
    $this->assertEqual($parsed['multiline'], $expected['multiline'], 'Set a multiline value.');
    $this->assertEqual($parsed['array'], $expected['array'], 'Set a simple array.');
    $this->assertEqual($parsed['array_assoc'], $expected['array_assoc'], 'Set an associative array.');
    $this->assertEqual($parsed['array_deep'], $expected['array_deep'], 'Set a nested array.');
    $this->assertEqual($parsed['array_deep_assoc'], $expected['array_deep_assoc'], 'Set a nested associative array.');
    $this->assertEqual($parsed['array_space'], $expected['array_space'], 'Set an array with a whitespace in the key.');
    $this->assertEqual($parsed, $expected, 'Entire parsed .info string and expected array are identical.');
  }
}

/**
 * Tests the effectiveness of hook_system_info_alter().
 */
class SystemInfoAlterTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'System info alter',
      'description' => 'Tests the effectiveness of hook_system_info_alter().',
      'group' => 'System',
    );
  }

  /**
   * Tests that {system}.info is rebuilt after a module that implements
   * hook_system_info_alter() is enabled. Also tests if core *_list() functions
   * return freshly altered info.
   */
  function testSystemInfoAlter() {
    // Enable our test module. Flush all caches, which we assert is the only
    // thing necessary to use the rebuilt {system}.info.
    module_enable(array('module_test'), FALSE);
    drupal_flush_all_caches();
    $this->assertTrue(module_exists('module_test'), 'Test module is enabled.');

    $info = $this->getSystemInfo('seven', 'theme');
    $this->assertTrue(isset($info['regions']['test_region']), 'Altered theme info was added to {system}.info.');
    $seven_regions = system_region_list('seven');
    $this->assertTrue(isset($seven_regions['test_region']), 'Altered theme info was returned by system_region_list().');
    $system_list_themes = system_list('theme');
    $info = $system_list_themes['seven']->info;
    $this->assertTrue(isset($info['regions']['test_region']), 'Altered theme info was returned by system_list().');
    $list_themes = list_themes();
    $this->assertTrue(isset($list_themes['seven']->info['regions']['test_region']), 'Altered theme info was returned by list_themes().');

    // Disable the module and verify that {system}.info is rebuilt without it.
    module_disable(array('module_test'), FALSE);
    drupal_flush_all_caches();
    $this->assertFalse(module_exists('module_test'), 'Test module is disabled.');

    $info = $this->getSystemInfo('seven', 'theme');
    $this->assertFalse(isset($info['regions']['test_region']), 'Altered theme info was removed from {system}.info.');
    $seven_regions = system_region_list('seven');
    $this->assertFalse(isset($seven_regions['test_region']), 'Altered theme info was not returned by system_region_list().');
    $system_list_themes = system_list('theme');
    $info = $system_list_themes['seven']->info;
    $this->assertFalse(isset($info['regions']['test_region']), 'Altered theme info was not returned by system_list().');
    $list_themes = list_themes();
    $this->assertFalse(isset($list_themes['seven']->info['regions']['test_region']), 'Altered theme info was not returned by list_themes().');
  }

  /**
   * Returns the info array as it is stored in {system}.
   *
   * @param $name
   *   The name of the record in {system}.
   * @param $type
   *   The type of record in {system}.
   *
   * @return
   *   Array of info, or FALSE if the record is not found.
   */
  function getSystemInfo($name, $type) {
    $raw_info = db_query("SELECT info FROM {system} WHERE name = :name AND type = :type", array(':name' => $name, ':type' => $type))->fetchField();
    return $raw_info ? unserialize($raw_info) : FALSE;
  }
}

/**
 * Tests for the update system functionality.
 */
class UpdateScriptFunctionalTest extends DrupalWebTestCase {
  private $update_url;
  private $update_user;

  public static function getInfo() {
    return array(
      'name' => 'Update functionality',
      'description' => 'Tests the update script access and functionality.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp('update_script_test');
    $this->update_url = $GLOBALS['base_url'] . '/update.php';
    $this->update_user = $this->drupalCreateUser(array('administer software updates'));
  }

  /**
   * Tests that there are no pending updates for the first test method.
   */
  function testNoPendingUpdates() {
    // Ensure that for the first test method in a class, there are no pending
    // updates. This tests a drupal_get_schema_versions() bug that previously
    // led to the wrong schema version being recorded for the initial install
    // of a child site during automated testing.
    $this->drupalLogin($this->update_user);
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->drupalPost(NULL, array(), t('Continue'));
    $this->assertText(t('No pending updates.'), 'End of update process was reached.');
  }

  /**
   * Tests access to the update script.
   */
  function testUpdateAccess() {
    // Try accessing update.php without the proper permission.
    $regular_user = $this->drupalCreateUser();
    $this->drupalLogin($regular_user);
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->assertResponse(403);

    // Try accessing update.php as an anonymous user.
    $this->drupalLogout();
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->assertResponse(403);

    // Access the update page with the proper permission.
    $this->drupalLogin($this->update_user);
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->assertResponse(200);

    // Access the update page as user 1.
    $user1 = user_load(1);
    $user1->pass_raw = user_password();
    require_once DRUPAL_ROOT . '/' . variable_get('password_inc', 'includes/password.inc');
    $user1->pass = user_hash_password(trim($user1->pass_raw));
    db_query("UPDATE {users} SET pass = :pass WHERE uid = :uid", array(':pass' => $user1->pass, ':uid' => $user1->uid));
    $this->drupalLogin($user1);
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->assertResponse(200);
  }

  /**
   * Tests that requirements warnings and errors are correctly displayed.
   */
  function testRequirements() {
    $this->drupalLogin($this->update_user);

    // If there are no requirements warnings or errors, we expect to be able to
    // go through the update process uninterrupted.
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->drupalPost(NULL, array(), t('Continue'));
    $this->assertText(t('No pending updates.'), 'End of update process was reached.');
    // Confirm that all caches were cleared.
    $this->assertText(t('hook_flush_caches() invoked for update_script_test.module.'), 'Caches were cleared when there were no requirements warnings or errors.');

    // If there is a requirements warning, we expect it to be initially
    // displayed, but clicking the link to proceed should allow us to go
    // through the rest of the update process uninterrupted.

    // First, run this test with pending updates to make sure they can be run
    // successfully.
    variable_set('update_script_test_requirement_type', REQUIREMENT_WARNING);
    drupal_set_installed_schema_version('update_script_test', drupal_get_installed_schema_version('update_script_test') - 1);
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->assertText('This is a requirements warning provided by the update_script_test module.');
    $this->clickLink('try again');
    $this->assertNoText('This is a requirements warning provided by the update_script_test module.');
    $this->drupalPost(NULL, array(), t('Continue'));
    $this->drupalPost(NULL, array(), t('Apply pending updates'));
    $this->assertText(t('The update_script_test_update_7000() update was executed successfully.'), 'End of update process was reached.');
    // Confirm that all caches were cleared.
    $this->assertText(t('hook_flush_caches() invoked for update_script_test.module.'), 'Caches were cleared after resolving a requirements warning and applying updates.');

    // Now try again without pending updates to make sure that works too.
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->assertText('This is a requirements warning provided by the update_script_test module.');
    $this->clickLink('try again');
    $this->assertNoText('This is a requirements warning provided by the update_script_test module.');
    $this->drupalPost(NULL, array(), t('Continue'));
    $this->assertText(t('No pending updates.'), 'End of update process was reached.');
    // Confirm that all caches were cleared.
    $this->assertText(t('hook_flush_caches() invoked for update_script_test.module.'), 'Caches were cleared after applying updates and re-running the script.');

    // If there is a requirements error, it should be displayed even after
    // clicking the link to proceed (since the problem that triggered the error
    // has not been fixed).
    variable_set('update_script_test_requirement_type', REQUIREMENT_ERROR);
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->assertText('This is a requirements error provided by the update_script_test module.');
    $this->clickLink('try again');
    $this->assertText('This is a requirements error provided by the update_script_test module.');

    // Check if the optional 'value' key displays without a notice.
    variable_set('update_script_test_requirement_type', REQUIREMENT_INFO);
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $this->assertText('This is a requirements info provided by the update_script_test module.');
    $this->assertNoText('Notice: Undefined index: value in theme_status_report()');
  }

  /**
   * Tests the effect of using the update script on the theme system.
   */
  function testThemeSystem() {
    // Since visiting update.php triggers a rebuild of the theme system from an
    // unusual maintenance mode environment, we check that this rebuild did not
    // put any incorrect information about the themes into the database.
    $original_theme_data = db_query("SELECT * FROM {system} WHERE type = 'theme' ORDER BY name")->fetchAll();
    $this->drupalLogin($this->update_user);
    $this->drupalGet($this->update_url, array('external' => TRUE));
    $final_theme_data = db_query("SELECT * FROM {system} WHERE type = 'theme' ORDER BY name")->fetchAll();
    $this->assertEqual($original_theme_data, $final_theme_data, 'Visiting update.php does not alter the information about themes stored in the database.');
  }

  /**
   * Tests update.php when there are no updates to apply.
   */
  function testNoUpdateFunctionality() {
    // Click through update.php with 'administer software updates' permission.
    $this->drupalLogin($this->update_user);
    $this->drupalPost($this->update_url, array(), t('Continue'), array('external' => TRUE));
    $this->assertText(t('No pending updates.'));
    $this->assertNoLink('Administration pages');
    $this->clickLink('Front page');
    $this->assertResponse(200);

    // Click through update.php with 'access administration pages' permission.
    $admin_user = $this->drupalCreateUser(array('administer software updates', 'access administration pages'));
    $this->drupalLogin($admin_user);
    $this->drupalPost($this->update_url, array(), t('Continue'), array('external' => TRUE));
    $this->assertText(t('No pending updates.'));
    $this->clickLink('Administration pages');
    $this->assertResponse(200);
  }

  /**
   * Tests update.php after performing a successful update.
   */
  function testSuccessfulUpdateFunctionality() {
    drupal_set_installed_schema_version('update_script_test', drupal_get_installed_schema_version('update_script_test') - 1);
    // Click through update.php with 'administer software updates' permission.
    $this->drupalLogin($this->update_user);
    $this->drupalPost($this->update_url, array(), t('Continue'), array('external' => TRUE));
    $this->drupalPost(NULL, array(), t('Apply pending updates'));
    $this->assertText('Updates were attempted.');
    $this->assertLink('site');
    $this->assertNoLink('Administration pages');
    $this->assertNoLink('logged');
    $this->clickLink('Front page');
    $this->assertResponse(200);

    drupal_set_installed_schema_version('update_script_test', drupal_get_installed_schema_version('update_script_test') - 1);
    // Click through update.php with 'access administration pages' and
    // 'access site reports' permissions.
    $admin_user = $this->drupalCreateUser(array('administer software updates', 'access administration pages', 'access site reports'));
    $this->drupalLogin($admin_user);
    $this->drupalPost($this->update_url, array(), t('Continue'), array('external' => TRUE));
    $this->drupalPost(NULL, array(), t('Apply pending updates'));
    $this->assertText('Updates were attempted.');
    $this->assertLink('logged');
    $this->clickLink('Administration pages');
    $this->assertResponse(200);
  }
}

/**
 * Functional tests for the flood control mechanism.
 */
class FloodFunctionalTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Flood control mechanism',
      'description' => 'Functional tests for the flood control mechanism.',
      'group' => 'System',
    );
  }

  /**
   * Test flood control mechanism clean-up.
   */
  function testCleanUp() {
    $threshold = 1;
    $window_expired = -1;
    $name = 'flood_test_cleanup';

    // Register expired event.
    flood_register_event($name, $window_expired);
    // Verify event is not allowed.
    $this->assertFalse(flood_is_allowed($name, $threshold));
    // Run cron and verify event is now allowed.
    $this->cronRun();
    $this->assertTrue(flood_is_allowed($name, $threshold));

    // Register unexpired event.
    flood_register_event($name);
    // Verify event is not allowed.
    $this->assertFalse(flood_is_allowed($name, $threshold));
    // Run cron and verify event is still not allowed.
    $this->cronRun();
    $this->assertFalse(flood_is_allowed($name, $threshold));
  }
}

/**
 * Test HTTP file downloading capability.
 */
class RetrieveFileTestCase extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'HTTP file retrieval',
      'description' => 'Checks HTTP file fetching and error handling.',
      'group' => 'System',
    );
  }

  /**
   * Invokes system_retrieve_file() in several scenarios.
   */
  function testFileRetrieving() {
    // Test 404 handling by trying to fetch a randomly named file.
    drupal_mkdir($sourcedir = 'public://' . $this->randomName());
    $filename = 'Файл для тестирования ' . $this->randomName();
    $url = file_create_url($sourcedir . '/' . $filename);
    $retrieved_file = system_retrieve_file($url);
    $this->assertFalse($retrieved_file, 'Non-existent file not fetched.');

    // Actually create that file, download it via HTTP and test the returned path.
    file_put_contents($sourcedir . '/' . $filename, 'testing');
    $retrieved_file = system_retrieve_file($url);

    // URLs could not contains characters outside the ASCII set so $filename
    // has to be encoded.
    $encoded_filename = rawurlencode($filename);

    $this->assertEqual($retrieved_file, 'public://' . $encoded_filename, 'Sane path for downloaded file returned (public:// scheme).');
    $this->assertTrue(is_file($retrieved_file), 'Downloaded file does exist (public:// scheme).');
    $this->assertEqual(filesize($retrieved_file), 7, 'File size of downloaded file is correct (public:// scheme).');
    file_unmanaged_delete($retrieved_file);

    // Test downloading file to a different location.
    drupal_mkdir($targetdir = 'temporary://' . $this->randomName());
    $retrieved_file = system_retrieve_file($url, $targetdir);
    $this->assertEqual($retrieved_file, "$targetdir/$encoded_filename", 'Sane path for downloaded file returned (temporary:// scheme).');
    $this->assertTrue(is_file($retrieved_file), 'Downloaded file does exist (temporary:// scheme).');
    $this->assertEqual(filesize($retrieved_file), 7, 'File size of downloaded file is correct (temporary:// scheme).');
    file_unmanaged_delete($retrieved_file);

    file_unmanaged_delete_recursive($sourcedir);
    file_unmanaged_delete_recursive($targetdir);
  }
}

/**
 * Functional tests shutdown functions.
 */
class ShutdownFunctionsTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Shutdown functions',
      'description' => 'Functional tests for shutdown functions',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp('system_test');
  }

  /**
   * Test shutdown functions.
   */
  function testShutdownFunctions() {
    $arg1 = $this->randomName();
    $arg2 = $this->randomName();
    $this->drupalGet('system-test/shutdown-functions/' . $arg1 . '/' . $arg2);
    $this->assertText(t('First shutdown function, arg1 : @arg1, arg2: @arg2', array('@arg1' => $arg1, '@arg2' => $arg2)));
    $this->assertText(t('Second shutdown function, arg1 : @arg1, arg2: @arg2', array('@arg1' => $arg1, '@arg2' => $arg2)));

    // Make sure exceptions displayed through _drupal_render_exception_safe()
    // are correctly escaped.
    $this->assertRaw('Drupal is &amp;lt;blink&amp;gt;awesome&amp;lt;/blink&amp;gt;.');
  }
}

/**
 * Tests administrative overview pages.
 */
class SystemAdminTestCase extends DrupalWebTestCase {
  protected $admin_user;
  protected $web_user;

  public static function getInfo() {
    return array(
      'name' => 'Administrative pages',
      'description' => 'Tests output on administrative pages and compact mode functionality.',
      'group' => 'System',
    );
  }

  function setUp() {
    // testAdminPages() requires Locale module.
    parent::setUp(array('locale'));

    // Create an administrator with all permissions, as well as a regular user
    // who can only access administration pages and perform some Locale module
    // administrative tasks, but not all of them.
    $this->admin_user = $this->drupalCreateUser(array_keys(module_invoke_all('permission')));
    $this->web_user = $this->drupalCreateUser(array(
      'access administration pages',
      'translate interface',
    ));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Tests output on administrative listing pages.
   */
  function testAdminPages() {
    // Go to Administration.
    $this->drupalGet('admin');

    // Verify that all visible, top-level administration links are listed on
    // the main administration page.
    foreach (menu_get_router() as $path => $item) {
      if (strpos($path, 'admin/') === 0 && ($item['type'] & MENU_VISIBLE_IN_TREE) && $item['_number_parts'] == 2) {
        $this->assertLink($item['title']);
        $this->assertLinkByHref($path);
        $this->assertText($item['description']);
      }
    }

    // For each administrative listing page on which the Locale module appears,
    // verify that there are links to the module's primary configuration pages,
    // but no links to its individual sub-configuration pages. Also verify that
    // a user with access to only some Locale module administration pages only
    // sees links to the pages they have access to.
    $admin_list_pages = array(
      'admin/index',
      'admin/config',
      'admin/config/regional',
    );

    foreach ($admin_list_pages as $page) {
      // For the administrator, verify that there are links to Locale's primary
      // configuration pages, but no links to individual sub-configuration
      // pages.
      $this->drupalLogin($this->admin_user);
      $this->drupalGet($page);
      $this->assertLinkByHref('admin/config');
      $this->assertLinkByHref('admin/config/regional/settings');
      $this->assertLinkByHref('admin/config/regional/date-time');
      $this->assertLinkByHref('admin/config/regional/language');
      $this->assertNoLinkByHref('admin/config/regional/language/configure/session');
      $this->assertNoLinkByHref('admin/config/regional/language/configure/url');
      $this->assertLinkByHref('admin/config/regional/translate');
      // On admin/index only, the administrator should also see a "Configure
      // permissions" link for the Locale module.
      if ($page == 'admin/index') {
        $this->assertLinkByHref("admin/people/permissions#module-locale");
      }

      // For a less privileged user, verify that there are no links to Locale's
      // primary configuration pages, but a link to the translate page exists.
      $this->drupalLogin($this->web_user);
      $this->drupalGet($page);
      $this->assertLinkByHref('admin/config');
      $this->assertNoLinkByHref('admin/config/regional/settings');
      $this->assertNoLinkByHref('admin/config/regional/date-time');
      $this->assertNoLinkByHref('admin/config/regional/language');
      $this->assertNoLinkByHref('admin/config/regional/language/configure/session');
      $this->assertNoLinkByHref('admin/config/regional/language/configure/url');
      $this->assertLinkByHref('admin/config/regional/translate');
      // This user cannot configure permissions, so even on admin/index should
      // not see a "Configure permissions" link for the Locale module.
      if ($page == 'admin/index') {
        $this->assertNoLinkByHref("admin/people/permissions#module-locale");
      }
    }
  }

  /**
   * Test compact mode.
   */
  function testCompactMode() {
    $this->drupalGet('admin/compact/on');
    $this->assertTrue($this->cookies['Drupal.visitor.admin_compact_mode']['value'], 'Compact mode turns on.');
    $this->drupalGet('admin/compact/on');
    $this->assertTrue($this->cookies['Drupal.visitor.admin_compact_mode']['value'], 'Compact mode remains on after a repeat call.');
    $this->drupalGet('');
    $this->assertTrue($this->cookies['Drupal.visitor.admin_compact_mode']['value'], 'Compact mode persists on new requests.');

    $this->drupalGet('admin/compact/off');
    $this->assertEqual($this->cookies['Drupal.visitor.admin_compact_mode']['value'], 'deleted', 'Compact mode turns off.');
    $this->drupalGet('admin/compact/off');
    $this->assertEqual($this->cookies['Drupal.visitor.admin_compact_mode']['value'], 'deleted', 'Compact mode remains off after a repeat call.');
    $this->drupalGet('');
    $this->assertTrue($this->cookies['Drupal.visitor.admin_compact_mode']['value'], 'Compact mode persists on new requests.');
  }
}

/**
 * Tests authorize.php and related hooks.
 */
class SystemAuthorizeCase extends DrupalWebTestCase {
  protected $admin_user;

  public static function getInfo() {
    return array(
      'name' => 'Authorize API',
      'description' => 'Tests the authorize.php script and related API.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp(array('system_test'));

    variable_set('allow_authorize_operations', TRUE);

    // Create an administrator user.
    $this->admin_user = $this->drupalCreateUser(array('administer software updates'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Helper function to initialize authorize.php and load it via drupalGet().
   *
   * Initializing authorize.php needs to happen in the child Drupal
   * installation, not the parent. So, we visit a menu callback provided by
   * system_test.module which calls system_authorized_init() to initialize the
   * $_SESSION inside the test site, not the framework site. This callback
   * redirects to authorize.php when it's done initializing.
   *
   * @see system_authorized_init().
   */
  function drupalGetAuthorizePHP($page_title = 'system-test-auth') {
    $this->drupalGet('system-test/authorize-init/' . $page_title);
  }

  /**
   * Tests the FileTransfer hooks
   */
  function testFileTransferHooks() {
    $page_title = $this->randomName(16);
    $this->drupalGetAuthorizePHP($page_title);
    $this->assertTitle(strtr('@title | Drupal', array('@title' => $page_title)), 'authorize.php page title is correct.');
    $this->assertNoText('It appears you have reached this page in error.');
    $this->assertText('To continue, provide your server connection details');
    // Make sure we see the new connection method added by system_test.
    $this->assertRaw('System Test FileTransfer');
    // Make sure the settings form callback works.
    $this->assertText('System Test Username');
  }
}

/**
 * Test the handling of requests containing 'index.php'.
 */
class SystemIndexPhpTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Index.php handling',
      'description' => "Test the handling of requests containing 'index.php'.",
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp();
  }

  /**
   * Test index.php handling.
   */
  function testIndexPhpHandling() {
    $index_php = $GLOBALS['base_url'] . '/index.php';

    $this->drupalGet($index_php, array('external' => TRUE));
    $this->assertResponse(200, 'Make sure index.php returns a valid page.');

    $this->drupalGet($index_php, array('external' => TRUE, 'query' => array('q' => 'user')));
    $this->assertResponse(200, 'Make sure index.php?q=user returns a valid page.');

    $this->drupalGet($index_php .'/user', array('external' => TRUE));
    $this->assertResponse(404, "Make sure index.php/user returns a 'page not found'.");
  }
}

/**
 * Test token replacement in strings.
 */
class TokenScanTest extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Token scanning',
      'description' => 'Scan token-like patterns in a dummy text to check token scanning.',
      'group' => 'System',
    );
  }

  /**
   * Scans dummy text, then tests the output.
   */
  function testTokenScan() {
    // Define text with valid and not valid, fake and existing token-like
    // strings.
    $text = 'First a [valid:simple], but dummy token, and a dummy [valid:token with: spaces].';
    $text .= 'Then a [not valid:token].';
    $text .= 'Last an existing token: [node:author:name].';
    $token_wannabes = token_scan($text);

    $this->assertTrue(isset($token_wannabes['valid']['simple']), 'A simple valid token has been matched.');
    $this->assertTrue(isset($token_wannabes['valid']['token with: spaces']), 'A valid token with space characters in the token name has been matched.');
    $this->assertFalse(isset($token_wannabes['not valid']), 'An invalid token with spaces in the token type has not been matched.');
    $this->assertTrue(isset($token_wannabes['node']), 'An existing valid token has been matched.');
  }
}

/**
 * Test case for drupal_valid_token().
 */
class SystemValidTokenTest extends DrupalUnitTestCase {

  /**
   * Flag to indicate whether PHP error reportings should be asserted.
   *
   * @var bool
   */
  protected $assertErrors = TRUE;

  public static function getInfo() {
    return array(
      'name' => 'Token validation',
      'description' => 'Test the security token validation.',
      'group' => 'System',
    );
  }

  /**
   * Tests invalid invocations of drupal_valid_token() that must return FALSE.
   */
  public function testTokenValidation() {
    // The following checks will throw PHP notices, so we disable error
    // assertions.
    $this->assertErrors = FALSE;

    // PHP5 incompat: Error class. Not really guarded, but somehow an accepted class-not-found-fallthrou (?)
    // @codingStandardsIgnoreStart
    try {
      $this->assertFalse(drupal_valid_token(NULL, new stdClass()), 'Token NULL, value object returns FALSE.');
    }
    // PHP 7.4 compatibility: the stdClass string conversion throws an exception
    // which is also an acceptable outcome of this test.
    catch (Error $e) {
      $this->pass('Token NULL, value object throws error exception which is ok.');
    }
    // @codingStandardsIgnoreEnd

    $this->assertFalse(drupal_valid_token(0, array()), 'Token 0, value array returns FALSE.');
    $this->assertFalse(drupal_valid_token('', array()), "Token '', value array returns FALSE.");
    $this->assertFalse('' === drupal_get_token(array()), 'Token generation does not return an empty string on invalid parameters.');
    $this->assertErrors = TRUE;

    $this->assertFalse(drupal_valid_token(TRUE, 'foo'), 'Token TRUE, value foo returns FALSE.');
    $this->assertFalse(drupal_valid_token(0, 'foo'), 'Token 0, value foo returns FALSE.');
  }

  /**
   * Overrides DrupalTestCase::errorHandler().
   */
  public function errorHandler($severity, $message, $file = NULL, $line = NULL) {
    if ($this->assertErrors) {
      return parent::errorHandler($severity, $message, $file, $line);
    }
    return TRUE;
  }
}

/**
 * Tests drupal_set_message() and related functions.
 */
class DrupalSetMessageTest extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Messages',
      'description' => 'Tests that messages can be displayed using drupal_set_message().',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp('system_test');
  }

  /**
   * Tests setting messages and removing one before it is displayed.
   */
  function testSetRemoveMessages() {
    // The page at system-test/drupal-set-message sets two messages and then
    // removes the first before it is displayed.
    $this->drupalGet('system-test/drupal-set-message');
    $this->assertNoText('First message (removed).');
    $this->assertText('Second message (not removed).');
  }
}

/**
 * Tests confirm form destinations.
 */
class ConfirmFormTest extends DrupalWebTestCase {
  protected $admin_user;

  public static function getInfo() {
    return array(
      'name' => 'Confirm form',
      'description' => 'Tests that the confirm form does not use external destinations.',
      'group' => 'System',
    );
  }

  function setUp() {
    parent::setUp();

    $this->admin_user = $this->drupalCreateUser(array('administer users'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Tests that the confirm form does not use external destinations.
   */
  function testConfirmForm() {
    $this->drupalGet('user/1/cancel');
    $this->assertCancelLinkUrl(url('user/1'));
    $this->drupalGet('user/1/cancel', array('query' => array('destination' => 'node')));
    $this->assertCancelLinkUrl(url('node'));
    $this->drupalGet('user/1/cancel', array('query' => array('destination' => 'http://example.com')));
    $this->assertCancelLinkUrl(url('user/1'));
  }

  /**
   * Asserts that a cancel link is present pointing to the provided URL.
   */
  function assertCancelLinkUrl($url, $message = '', $group = 'Other') {
    $links = $this->xpath('//a[normalize-space(text())=:label and @href=:url]', array(':label' => t('Cancel'), ':url' => $url));
    $message = ($message ? $message : format_string('Cancel link with url %url found.', array('%url' => $url)));
    return $this->assertTrue(isset($links[0]), $message, $group);
  }
}

/**
 * Test case for Archiver classes.
 *
 * Cannot be a DrupalUnitTestCase as it requires access to the db and files.
 */
class SystemArchiverTest extends DrupalWebTestCase
{

  public static function getInfo()
  {
    return array(
      'name' => 'Archiver tests',
      'description' => 'Test the Archiver classes.',
      'group' => 'System',
    );
  }

  /**
   * Tests interacting with a tarball archive.
   */
  public function testArchiverTarball() {
    $src_tarball = DRUPAL_ROOT . '/' . drupal_get_path('module', 'system') . '/tests/system_test_archive.tar.gz';
    $tmp = file_directory_temp();
    $tarball = $tmp . '/' . basename($src_tarball);
    file_unmanaged_copy($src_tarball, $tarball);
    try {
      $archiver = archiver_get_archiver($tarball);
    }
    catch (Exception $e) {
      // The file's not there (this is not part of the test).
      $this->assertTrue(FALSE, $e);
      return;
    }

    // Test \ArchiverTar::listContents
    $listing = $archiver->listContents();
    $this->assertEqual(count($listing), 4, 'Tarball listing has 4 entries.');
    $this->assertTrue((strpos(implode(',', $listing), 'tarball.module') !== FALSE), 'Tarball listing includes tarball.module');

    // Test \ArchiverTar::extract
    $extract_dir = file_directory_temp() . '/testArchiverTarball';
    $archiver->extract($extract_dir);
    $this->assertTrue(file_exists($extract_dir . '/system_test_archive/test.txt'), 'test.txt extracted from tarball');
    $this->assertEqual(count(glob($extract_dir . '/system_test_archive/*')), 3, '3 files extracted from tarball');

    // Test \ArchiverTar::add
    $extra_file = DRUPAL_ROOT . '/misc/druplicon.png';
    $archiver->add($extra_file);
    $new_listing = $archiver->listContents();
    // \ArchiverTar::add probably should not add the new file with its absolute
    // path. However that's how \Archive_Tar::add works. If we wanted to modify
    // the file path within the archive, we could call \Archive_Tar::addModify
    // directly and use its additional parameters. That could be done using
    // \ArchiverTar::getArchive like in _testArchiverOutOfPath() which calls
    // \Archive_Tar::extract directly.
    $this->assertTrue(in_array($extra_file, $new_listing), 'Druplicon added to tarball');
  }

  /**
   * Tests out-of-path extraction protection.
   */
  public function testArchiverOutOfPath() {
    $this->_testArchiverOutOfPath('system_test_archive_rel.tar', 'Relative out-of-path extraction caught');
    $this->_testArchiverOutOfPath('system_test_archive_abs.tgz', 'Absolute out-of-path extraction caught');
  }

  /**
   * Helper to test out-of-path extraction protection.
   */
  public function _testArchiverOutOfPath($archive, $message) {
    $src_tarball = DRUPAL_ROOT . '/modules/system/tests/' . $archive;
    $tarball = file_directory_temp() . '/' . $archive;
    file_unmanaged_copy($src_tarball, $tarball);
    try {
      $archiver = archiver_get_archiver($tarball);
    } catch (Exception $e) {
      // The file's not there (this is not part of the test).
      $this->assertTrue(FALSE, $e);
      return;
    }

    $extract_dir = file_directory_temp() . '/testArchiverTarball';
    $caught_exception = FALSE;
    try {
      // Drupal's \ArchiverTar::extract() doesn't support symlinks, so we have
      // to access the underlying Archive_Tar object.
      $archiver->getArchive()->extract($extract_dir, FALSE, TRUE);
    }
    catch (Exception $e) {
      $caught_exception = (strpos($e->getMessage(), 'Out-of-path file extraction') !== FALSE);
    }
    $this->assertTrue($caught_exception, $message);
  }
}

/**
 * Tests .htaccess is working correctly.
 */
class HtaccessTest extends DrupalWebTestCase {
  public static function getInfo() {
    return array(
      'name' => '.htaccess tests',
      'description' => 'Tests .htaccess is working correctly.',
      'group' => 'System',
    );
  }

  /**
   * Get an array of file paths for access testing.
   */
  protected function getProtectedFiles() {
    drupal_mkdir($path = 'public://' . $this->randomName());

    // Tests the FilesMatch directive which denies access to certain file
    // extensions.
    $file_exts_to_deny = array(
      'engine',
      'inc',
      'info',
      'install',
      'make',
      'module',
      'module~',
      'module.bak',
      'module.orig',
      'module.save',
      'module.swo',
      'module.swp',
      'php~',
      'php.bak',
      'php.orig',
      'php.save',
      'php.swo',
      'php.swp',
      'profile',
      'po',
      'sh',
      'sql',
      'test',
      'theme',
      'tpl.php',
      'xtmpl',
    );

    foreach ($file_exts_to_deny as $file_ext) {
      $file_paths["$path/access_test.$file_ext"] = 403;
    }

    // Test extensions that should be permitted.
    $file_exts_to_allow = array(
      'php-info.txt',
    );

    foreach ($file_exts_to_allow as $file_ext) {
      $file_paths["$path/access_test.$file_ext"] = 200;
    }

    // Ensure web server configuration files cannot be accessed.
    $file_paths["$path/.htaccess"] = 403;
    $file_paths["$path/web.config"] = 403;

    return $file_paths;
  }

  /**
   * Iterates over protected files and calls assertNoFileAccess().
   */
  function testFileAccess() {
    foreach ($this->getProtectedFiles() as $file => $response_code) {
      file_put_contents($file, '# test content');
      $this->assertFileAccess($file, $response_code);
    }
  }

  /**
   * Asserts that a file exists and requesting it returns a specific response.
   *
   * @param string $path
   *   Path to file. Without leading slash.
   * @param int $response_code
   *   The expected response code. For example: 200, 403 or 404.
   */
  protected function assertFileAccess($path, $response_code) {
    $this->assertTrue(file_exists(drupal_realpath($path)), format_string('@filename exists.', array('@filename' => $path)));
    $this->drupalGet(file_create_url($path), array('external' => TRUE));
    $this->assertResponse($response_code, "Response code to $path should be $response_code");
  }
}

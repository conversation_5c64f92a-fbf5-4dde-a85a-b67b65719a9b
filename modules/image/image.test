<?php

/**
 * @file
 * Tests for image.module.
 */

/**
 * TODO: Test the following functions.
 *
 * image.effects.inc:
 *   image_style_generate()
 *   image_style_create_derivative()
 *
 * image.module:
 *   image_style_load()
 *   image_style_save()
 *   image_style_delete()
 *   image_style_options()
 *   image_effect_definition_load()
 *   image_effect_load()
 *   image_effect_save()
 *   image_effect_delete()
 *   image_filter_keyword()
 */

/**
 * This class provides methods specifically for testing Image's field handling.
 */
class ImageFieldTestCase extends DrupalWebTestCase {
  protected $admin_user;

  function setUp() {
    $modules = func_get_args();
    if (isset($modules[0]) && is_array($modules[0])) {
      $modules = $modules[0];
    }
    $modules[] = 'image';
    parent::setUp($modules);
    $this->admin_user = $this->drupalCreateUser(array('access content', 'access administration pages', 'administer site configuration', 'administer content types', 'administer nodes', 'create article content', 'edit any article content', 'delete any article content', 'administer image styles', 'administer fields'));
    $this->drupalLogin($this->admin_user);
  }

  /**
   * Create a new image field.
   *
   * @param $name
   *   The name of the new field (all lowercase), exclude the "field_" prefix.
   * @param $type_name
   *   The node type that this field will be added to.
   * @param $field_settings
   *   A list of field settings that will be added to the defaults.
   * @param $instance_settings
   *   A list of instance settings that will be added to the instance defaults.
   * @param $widget_settings
   *   A list of widget settings that will be added to the widget defaults.
   */
  function createImageField($name, $type_name, $field_settings = array(), $instance_settings = array(), $widget_settings = array()) {
    $field = array(
      'field_name' => $name,
      'type' => 'image',
      'settings' => array(),
      'cardinality' => !empty($field_settings['cardinality']) ? $field_settings['cardinality'] : 1,
    );
    $field['settings'] = array_merge($field['settings'], $field_settings);
    field_create_field($field);

    $instance = array(
      'field_name' => $field['field_name'],
      'entity_type' => 'node',
      'label' => $name,
      'bundle' => $type_name,
      'required' => !empty($instance_settings['required']),
      'settings' => array(),
      'widget' => array(
        'type' => 'image_image',
        'settings' => array(),
      ),
    );
    $instance['settings'] = array_merge($instance['settings'], $instance_settings);
    $instance['widget']['settings'] = array_merge($instance['widget']['settings'], $widget_settings);
    return field_create_instance($instance);
  }

  /**
   * Create a random style.
   *
   * @return array
   *  A list containing the details of the generated image style.
   */
  function createRandomStyle() {
    $style_name = strtolower($this->randomName(10));
    $style_label = $this->randomString();
    image_style_save(array('name' => $style_name, 'label' => $style_label));
    $style_path = 'admin/config/media/image-styles/edit/' . $style_name;
    return array(
      'name' => $style_name,
      'label' => $style_label,
      'path' => $style_path,
    );
  }

  /**
   * Upload an image to a node.
   *
   * @param $image
   *   A file object representing the image to upload.
   * @param $field_name
   *   Name of the image field the image should be attached to.
   * @param $type
   *   The type of node to create.
   */
  function uploadNodeImage($image, $field_name, $type) {
    $edit = array(
      'title' => $this->randomName(),
    );
    $edit['files[' . $field_name . '_' . LANGUAGE_NONE . '_0]'] = drupal_realpath($image->uri);
    $this->drupalPost('node/add/' . $type, $edit, t('Save'));

    // Retrieve ID of the newly created node from the current URL.
    $matches = array();
    preg_match('/node\/([0-9]+)/', $this->getUrl(), $matches);
    return isset($matches[1]) ? $matches[1] : FALSE;
  }
}

/**
 * Tests the functions for generating paths and URLs for image styles.
 */
class ImageStylesPathAndUrlTestCase extends DrupalWebTestCase {
  protected $style_name;
  protected $image_info;
  protected $image_filepath;

  public static function getInfo() {
    return array(
      'name' => 'Image styles path and URL functions',
      'description' => 'Tests functions for generating paths and URLs to image styles.',
      'group' => 'Image',
    );
  }

  function setUp() {
    parent::setUp('image_module_test');

    $this->style_name = 'style_foo';
    image_style_save(array('name' => $this->style_name, 'label' => $this->randomString()));
  }

  /**
   * Test image_style_path().
   */
  function testImageStylePath() {
    $scheme = 'public';
    $actual = image_style_path($this->style_name, "$scheme://foo/bar.gif");
    $expected = "$scheme://styles/" . $this->style_name . "/$scheme/foo/bar.gif";
    $this->assertEqual($actual, $expected, 'Got the path for a file URI.');

    $actual = image_style_path($this->style_name, 'foo/bar.gif');
    $expected = "$scheme://styles/" . $this->style_name . "/$scheme/foo/bar.gif";
    $this->assertEqual($actual, $expected, 'Got the path for a relative file path.');
  }

  /**
   * Test image_style_url() with a file using the "public://" scheme.
   */
  function testImageStyleUrlAndPathPublic() {
    $this->_testImageStyleUrlAndPath('public');
  }

  /**
   * Test image_style_url() with a file using the "private://" scheme.
   */
  function testImageStyleUrlAndPathPrivate() {
    $this->_testImageStyleUrlAndPath('private');
  }

  /**
   * Test image_style_url() with the "public://" scheme and unclean URLs.
   */
   function testImageStylUrlAndPathPublicUnclean() {
     $this->_testImageStyleUrlAndPath('public', FALSE);
   }

  /**
   * Test image_style_url() with the "private://" schema and unclean URLs.
   */
  function testImageStyleUrlAndPathPrivateUnclean() {
    $this->_testImageStyleUrlAndPath('private', FALSE);
  }

  /**
   * Test image_style_url() with a file URL that has an extra slash in it.
   */
  function testImageStyleUrlExtraSlash() {
    $this->_testImageStyleUrlAndPath('public', TRUE, TRUE);
  }

  /**
   * Test that an invalid source image returns a 404.
   */
  function testImageStyleUrlForMissingSourceImage() {
    $non_existent_uri = 'public://foo.png';
    $generated_url = image_style_url($this->style_name, $non_existent_uri);
    $this->drupalGet($generated_url);
    $this->assertResponse(404, 'Accessing an image style URL with a source image that does not exist provides a 404 error response.');
  }

  /**
   * Test that we do not pass an array to drupal_add_http_header.
   */
  function testImageContentTypeHeaders() {
    $files = $this->drupalGetTestFiles('image');
    $file = array_shift($files);
    // Copy the test file to private folder.
    $private_file = file_copy($file, 'private://', FILE_EXISTS_RENAME);
    // Tell image_module_test module to return the headers we want to test.
    variable_set('image_module_test_invalid_headers', $private_file->uri);
    // Invoke image_style_deliver so it will try to set headers.
    $generated_url = image_style_url($this->style_name, $private_file->uri);
    $this->drupalGet($generated_url);
    variable_del('image_module_test_invalid_headers');
  }

  /**
   * Test image_style_url().
   */
  function _testImageStyleUrlAndPath($scheme, $clean_url = TRUE, $extra_slash = FALSE) {
    // Make the default scheme neither "public" nor "private" to verify the
    // functions work for other than the default scheme.
    variable_set('file_default_scheme', 'temporary');
    variable_set('clean_url', $clean_url);

    // Create the directories for the styles.
    $directory = $scheme . '://styles/' . $this->style_name;
    $status = file_prepare_directory($directory, FILE_CREATE_DIRECTORY);
    $this->assertNotIdentical(FALSE, $status, 'Created the directory for the generated images for the test style.');

    // Create a working copy of the file.
    $files = $this->drupalGetTestFiles('image');
    $file = array_shift($files);
    $image_info = image_get_info($file->uri);
    $original_uri = file_unmanaged_copy($file->uri, $scheme . '://', FILE_EXISTS_RENAME);
    // Let the image_module_test module know about this file, so it can claim
    // ownership in hook_file_download().
    variable_set('image_module_test_file_download', $original_uri);
    $this->assertNotIdentical(FALSE, $original_uri, 'Created the generated image file.');

    // Get the URL of a file that has not been generated and try to create it.
    $generated_uri = image_style_path($this->style_name, $original_uri);
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $generate_url = image_style_url($this->style_name, $original_uri);

    // Ensure that the tests still pass when the file is generated by accessing
    // a poorly constructed (but still valid) file URL that has an extra slash
    // in it.
    if ($extra_slash) {
      $modified_uri = str_replace('://', ':///', $original_uri);
      $this->assertNotEqual($original_uri, $modified_uri, 'An extra slash was added to the generated file URI.');
      $generate_url = image_style_url($this->style_name, $modified_uri);
    }

    if (!$clean_url) {
      $this->assertTrue(strpos($generate_url, '?q=') !== FALSE, 'When using non-clean URLS, the system path contains the query string.');
    }
    // Add some extra chars to the token.
    $this->drupalGet(str_replace(IMAGE_DERIVATIVE_TOKEN . '=', IMAGE_DERIVATIVE_TOKEN . '=Zo', $generate_url));
    $this->assertResponse(403, 'Image was inaccessible at the URL with an invalid token.');
    // Change the parameter name so the token is missing.
    $this->drupalGet(str_replace(IMAGE_DERIVATIVE_TOKEN . '=', 'wrongparam=', $generate_url));
    $this->assertResponse(403, 'Image was inaccessible at the URL with a missing token.');

    // Check that the generated URL is the same when we pass in a relative path
    // rather than a URI. We need to temporarily switch the default scheme to
    // match the desired scheme before testing this, then switch it back to the
    // "temporary" scheme used throughout this test afterwards.
    variable_set('file_default_scheme', $scheme);
    $relative_path = file_uri_target($original_uri);
    $generate_url_from_relative_path = image_style_url($this->style_name, $relative_path);
    $this->assertEqual($generate_url, $generate_url_from_relative_path, 'Generated URL is the same regardless of whether it came from a relative path or a file URI.');
    variable_set('file_default_scheme', 'temporary');

    // Fetch the URL that generates the file.
    $this->drupalGet($generate_url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it.');
    $this->assertRaw(file_get_contents($generated_uri), 'URL returns expected file.');
    $generated_image_info = image_get_info($generated_uri);
    $this->assertEqual($this->drupalGetHeader('Content-Type'), $generated_image_info['mime_type'], 'Expected Content-Type was reported.');
    $this->assertEqual($this->drupalGetHeader('Content-Length'), $generated_image_info['file_size'], 'Expected Content-Length was reported.');
    if ($scheme == 'private') {
      $this->assertEqual($this->drupalGetHeader('Expires'), 'Sun, 19 Nov 1978 05:00:00 GMT', 'Expires header was sent.');
      $this->assertEqual($this->drupalGetHeader('Cache-Control'), 'no-cache, must-revalidate', 'Cache-Control header was set to prevent caching.');
      $this->assertEqual($this->drupalGetHeader('X-Image-Owned-By'), 'image_module_test', 'Expected custom header has been added.');

      // Make sure that a second request to the already existing derivate works
      // too.
      $this->drupalGet($generate_url);
      $this->assertResponse(200, 'Image was generated at the URL.');

      // Make sure that access is denied for existing style files if we do not
      // have access.
      variable_del('image_module_test_file_download');
      $this->drupalGet($generate_url);
      $this->assertResponse(403, 'Confirmed that access is denied for the private image style.');

      // Repeat this with a different file that we do not have access to and
      // make sure that access is denied.
      $file_noaccess = array_shift($files);
      $original_uri_noaccess = file_unmanaged_copy($file_noaccess->uri, $scheme . '://', FILE_EXISTS_RENAME);
      $generated_uri_noaccess = $scheme . '://styles/' . $this->style_name . '/' . $scheme . '/'. drupal_basename($original_uri_noaccess);
      $this->assertFalse(file_exists($generated_uri_noaccess), 'Generated file does not exist.');
      $generate_url_noaccess = image_style_url($this->style_name, $original_uri_noaccess);

      $this->drupalGet($generate_url_noaccess);
      $this->assertResponse(403, 'Confirmed that access is denied for the private image style.');
      // Verify that images are not appended to the response. Currently this test only uses PNG images.
      if (strpos($generate_url, '.png') === FALSE ) {
        $this->fail('Confirming that private image styles are not appended require PNG file.');
      }
      else {
        // Check for PNG-Signature (cf. http://www.libpng.org/pub/png/book/chapter08.html#png.ch08.div.2) in the
        // response body.
        $this->assertNoRaw( chr(137) . chr(80) . chr(78) . chr(71) . chr(13) . chr(10) . chr(26) . chr(10), 'No PNG signature found in the response body.');
      }
    }
    elseif ($clean_url) {
      // Add some extra chars to the token.
      $this->drupalGet(str_replace(IMAGE_DERIVATIVE_TOKEN . '=', IMAGE_DERIVATIVE_TOKEN . '=Zo', $generate_url));
      $this->assertResponse(200, 'Existing image was accessible at the URL with an invalid token.');
    }

    // Allow insecure image derivatives to be created for the remainder of this
    // test.
    variable_set('image_allow_insecure_derivatives', TRUE);

    // Create another working copy of the file.
    $files = $this->drupalGetTestFiles('image');
    $file = array_shift($files);
    $image_info = image_get_info($file->uri);
    $original_uri = file_unmanaged_copy($file->uri, $scheme . '://', FILE_EXISTS_RENAME);
    // Let the image_module_test module know about this file, so it can claim
    // ownership in hook_file_download().
    variable_set('image_module_test_file_download', $original_uri);

    // Get the URL of a file that has not been generated and try to create it.
    $generated_uri = image_style_path($this->style_name, $original_uri);
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $generate_url = image_style_url($this->style_name, $original_uri);

    // Check that the image is accessible even without the security token.
    $this->drupalGet(str_replace(IMAGE_DERIVATIVE_TOKEN . '=', 'wrongparam=', $generate_url));
    $this->assertResponse(200, 'Image was accessible at the URL with a missing token.');

    // Check that a security token is still required when generating a second
    // image derivative using the first one as a source.
    $nested_uri = image_style_path($this->style_name, $generated_uri);
    $nested_url = image_style_url($this->style_name, $generated_uri);
    $nested_url_with_wrong_token = str_replace(IMAGE_DERIVATIVE_TOKEN . '=', 'wrongparam=', $nested_url);
    $this->drupalGet($nested_url_with_wrong_token);
    $this->assertResponse(403, 'Image generated from an earlier derivative was inaccessible at the URL with a missing token.');
    // Check that this restriction cannot be bypassed by adding extra slashes
    // to the URL.
    $this->drupalGet(substr_replace($nested_url_with_wrong_token, '//styles/', strrpos($nested_url_with_wrong_token, '/styles/'), strlen('/styles/')));
    $this->assertResponse(403, 'Image generated from an earlier derivative was inaccessible at the URL with a missing token, even with an extra forward slash in the URL.');
    $this->drupalGet(substr_replace($nested_url_with_wrong_token, '/\styles/', strrpos($nested_url_with_wrong_token, '/styles/'), strlen('/styles/')));
    $this->assertResponse(403, 'Image generated from an earlier derivative was inaccessible at the URL with a missing token, even with an extra backslash in the URL.');
    // Make sure the image can still be generated if a correct token is used.
    $this->drupalGet($nested_url);
    $this->assertResponse(200, 'Image was accessible when a correct token was provided in the URL.');

    // Suppress the security token in the URL, then get the URL of a file. Check
    // that the security token is not present in the URL but that the image is
    // still accessible.
    variable_set('image_suppress_itok_output', TRUE);
    $generate_url = image_style_url($this->style_name, $original_uri);
    $this->assertIdentical(strpos($generate_url, IMAGE_DERIVATIVE_TOKEN . '='), FALSE, 'The security token does not appear in the image style URL.');
    $this->drupalGet($generate_url);
    $this->assertResponse(200, 'Image was accessible at the URL with a missing token.');

    // Check that requesting a nonexistent image does not create any new
    // directories in the file system.
    $directory = $scheme . '://styles/' . $this->style_name . '/' . $scheme . '/' . $this->randomName();
    $this->drupalGet(file_create_url($directory . '/' . $this->randomName()));
    $this->assertFalse(file_exists($directory), 'New directory was not created in the filesystem when requesting an unauthorized image.');

    // Check that requesting a partial image style path returns access denied.
    $partial_url = $scheme . '://styles/' . $this->style_name . '/';
    $this->drupalGet(file_create_url($partial_url) . '/');
    $this->assertResponse(403, 'Access was denied to a partial image style path.');
  }
}

/**
 * Use the image_test.module's mock toolkit to ensure that the effects are
 * properly passing parameters to the image toolkit.
 */
class ImageEffectsUnitTest extends ImageToolkitTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Image effects',
      'description' => 'Test that the image effects pass parameters to the toolkit correctly.',
      'group' => 'Image',
    );
  }

  function setUp() {
    parent::setUp('image_module_test');
    module_load_include('inc', 'image', 'image.effects');
  }

  /**
   * Test the image_resize_effect() function.
   */
  function testResizeEffect() {
    $this->assertTrue(image_resize_effect($this->image, array('width' => 1, 'height' => 2)), 'Function returned the expected value.');
    $this->assertToolkitOperationsCalled(array('resize'));

    // Check the parameters.
    $calls = image_test_get_all_calls();
    $this->assertEqual($calls['resize'][0][1], 1, 'Width was passed correctly');
    $this->assertEqual($calls['resize'][0][2], 2, 'Height was passed correctly');
  }

  /**
   * Test the image_scale_effect() function.
   */
  function testScaleEffect() {
    // @todo: need to test upscaling.
    $this->assertTrue(image_scale_effect($this->image, array('width' => 10, 'height' => 10)), 'Function returned the expected value.');
    $this->assertToolkitOperationsCalled(array('resize'));

    // Check the parameters.
    $calls = image_test_get_all_calls();
    $this->assertEqual($calls['resize'][0][1], 10, 'Width was passed correctly');
    $this->assertEqual($calls['resize'][0][2], 5, 'Height was based off aspect ratio and passed correctly');
  }

  /**
   * Test the image_crop_effect() function.
   */
  function testCropEffect() {
    // @todo should test the keyword offsets.
    $this->assertTrue(image_crop_effect($this->image, array('anchor' => 'top-1', 'width' => 3, 'height' => 4)), 'Function returned the expected value.');
    $this->assertToolkitOperationsCalled(array('crop'));

    // Check the parameters.
    $calls = image_test_get_all_calls();
    $this->assertEqual($calls['crop'][0][1], 0, 'X was passed correctly');
    $this->assertEqual($calls['crop'][0][2], 1, 'Y was passed correctly');
    $this->assertEqual($calls['crop'][0][3], 3, 'Width was passed correctly');
    $this->assertEqual($calls['crop'][0][4], 4, 'Height was passed correctly');
  }

  /**
   * Test the image_scale_and_crop_effect() function.
   */
  function testScaleAndCropEffect() {
    $this->assertTrue(image_scale_and_crop_effect($this->image, array('width' => 5, 'height' => 10)), 'Function returned the expected value.');
    $this->assertToolkitOperationsCalled(array('resize', 'crop'));

    // Check the parameters.
    $calls = image_test_get_all_calls();
    $this->assertEqual($calls['crop'][0][1], 7.5, 'X was computed and passed correctly');
    $this->assertEqual($calls['crop'][0][2], 0, 'Y was computed and passed correctly');
    $this->assertEqual($calls['crop'][0][3], 5, 'Width was computed and passed correctly');
    $this->assertEqual($calls['crop'][0][4], 10, 'Height was computed and passed correctly');
  }

  /**
   * Test the image_desaturate_effect() function.
   */
  function testDesaturateEffect() {
    $this->assertTrue(image_desaturate_effect($this->image, array()), 'Function returned the expected value.');
    $this->assertToolkitOperationsCalled(array('desaturate'));

    // Check the parameters.
    $calls = image_test_get_all_calls();
    $this->assertEqual(count($calls['desaturate'][0]), 1, 'Only the image was passed.');
  }

  /**
   * Test the image_rotate_effect() function.
   */
  function testRotateEffect() {
    // @todo: need to test with 'random' => TRUE
    $this->assertTrue(image_rotate_effect($this->image, array('degrees' => 90, 'bgcolor' => '#fff')), 'Function returned the expected value.');
    $this->assertToolkitOperationsCalled(array('rotate'));

    // Check the parameters.
    $calls = image_test_get_all_calls();
    $this->assertEqual($calls['rotate'][0][1], 90, 'Degrees were passed correctly');
    $this->assertEqual($calls['rotate'][0][2], 0xffffff, 'Background color was passed correctly');
  }

  /**
   * Test image effect caching.
   */
  function testImageEffectsCaching() {
    $image_effect_definitions_called = &drupal_static('image_module_test_image_effect_info_alter');

    // First call should grab a fresh copy of the data.
    $effects = image_effect_definitions();
    $this->assertTrue($image_effect_definitions_called === 1, 'image_effect_definitions() generated data.');

    // Second call should come from cache.
    drupal_static_reset('image_effect_definitions');
    drupal_static_reset('image_module_test_image_effect_info_alter');
    $cached_effects = image_effect_definitions();
    $this->assertTrue(is_null($image_effect_definitions_called), 'image_effect_definitions() returned data from cache.');

    $this->assertTrue($effects == $cached_effects, 'Cached effects are the same as generated effects.');
  }
}

/**
 * Tests the administrative user interface.
 */
class ImageAdminUiTestCase extends ImageFieldTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Administrative user interface',
      'description' => 'Tests the forms used in the administrative user interface.',
      'group' => 'Image',
    );
  }

  function setUp() {
    parent::setUp(array('image'));
  }

  /**
   * Test if the help text is available on the add effect form.
   */
  function testAddEffectHelpText() {
    // Create a random image style.
    $style = $this->createRandomStyle();

    // Open the add effect form and check for the help text.
    $this->drupalGet($style['path'] . '/add/image_crop');
    $this->assertText(t('Cropping will remove portions of an image to make it the specified dimensions.'), 'The image style effect help text was displayed on the add effect page.');
  }

  /**
   * Test if the help text is available on the edit effect form.
   */
  function testEditEffectHelpText() {
    // Create a random image style.
    $random_style = $this->createRandomStyle();

    // Add the crop effect to the image style.
    $edit = array();
    $edit['data[width]'] = 20;
    $edit['data[height]'] = 20;
    $this->drupalPost($random_style['path'] . '/add/image_crop', $edit, t('Add effect'));

    // Open the edit effect form and check for the help text.
    drupal_static_reset('image_styles');
    $style = image_style_load($random_style['name']);

    foreach ($style['effects'] as $ieid => $effect) {
      $this->drupalGet($random_style['path'] . '/effects/' . $ieid);
      $this->assertText(t('Cropping will remove portions of an image to make it the specified dimensions.'), 'The image style effect help text was displayed on the edit effect page.');
    }
  }
}

/**
 * Tests creation, deletion, and editing of image styles and effects.
 */
class ImageAdminStylesUnitTest extends ImageFieldTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Image styles and effects UI configuration',
      'description' => 'Tests creation, deletion, and editing of image styles and effects at the UI level.',
      'group' => 'Image',
    );
  }

  function setUp() {
    parent::setUp('image_module_test', 'image_module_styles_test');
  }

  /**
   * Given an image style, generate an image.
   */
  function createSampleImage($style) {
    static $file_path;

    // First, we need to make sure we have an image in our testing
    // file directory. Copy over an image on the first run.
    if (!isset($file_path)) {
      $files = $this->drupalGetTestFiles('image');
      $file = reset($files);
      $file_path = file_unmanaged_copy($file->uri);
    }

    return image_style_url($style['name'], $file_path) ? $file_path : FALSE;
  }

  /**
   * Count the number of images currently create for a style.
   */
  function getImageCount($style) {
    return count(file_scan_directory('public://styles/' . $style['name'], '/.*/'));
  }

  /**
   * Test creating an image style with a numeric name and ensuring it can be
   * applied to an image.
   */
  function testNumericStyleName() {
    $style_name = rand();
    $style_label = $this->randomString();
    $edit = array(
      'name' => $style_name,
      'label' => $style_label,
    );
    $this->drupalPost('admin/config/media/image-styles/add', $edit, t('Create new style'));
    $this->assertRaw(t('Style %name was created.', array('%name' => $style_label)), 'Image style successfully created.');
    $options = image_style_options();
    $this->assertTrue(array_key_exists($style_name, $options), format_string('Array key %key exists.', array('%key' => $style_name)));
  }

  /**
   * General test to add a style, add/remove/edit effects to it, then delete it.
   */
  function testStyle() {
    // Setup a style to be created and effects to add to it.
    $style_name = strtolower($this->randomName(10));
    $style_label = $this->randomString();
    $style_path = 'admin/config/media/image-styles/edit/' . $style_name;
    $effect_edits = array(
      'image_resize' => array(
        'data[width]' => 100,
        'data[height]' => 101,
      ),
      'image_scale' => array(
        'data[width]' => 110,
        'data[height]' => 111,
        'data[upscale]' => 1,
      ),
      'image_scale_and_crop' => array(
        'data[width]' => 120,
        'data[height]' => 121,
      ),
      'image_crop' => array(
        'data[width]' => 130,
        'data[height]' => 131,
        'data[anchor]' => 'center-center',
      ),
      'image_desaturate' => array(
        // No options for desaturate.
      ),
      'image_rotate' => array(
        'data[degrees]' => 5,
        'data[random]' => 1,
        'data[bgcolor]' => '#FFFF00',
      ),
    );

    // Add style form.

    $edit = array(
      'name' => $style_name,
      'label' => $style_label,
    );
    $this->drupalPost('admin/config/media/image-styles/add', $edit, t('Create new style'));
    $this->assertRaw(t('Style %name was created.', array('%name' => $style_label)), 'Image style successfully created.');

    // Add effect form.

    // Add each sample effect to the style.
    foreach ($effect_edits as $effect => $edit) {
      // Add the effect.
      $this->drupalPost($style_path, array('new' => $effect), t('Add'));
      if (!empty($edit)) {
        $this->drupalPost(NULL, $edit, t('Add effect'));
      }
    }

    // Edit effect form.

    // Revisit each form to make sure the effect was saved.
    drupal_static_reset('image_styles');
    $style = image_style_load($style_name);

    foreach ($style['effects'] as $ieid => $effect) {
      $this->drupalGet($style_path . '/effects/' . $ieid);
      foreach ($effect_edits[$effect['name']] as $field => $value) {
        $this->assertFieldByName($field, $value, format_string('The %field field in the %effect effect has the correct value of %value.', array('%field' => $field, '%effect' => $effect['name'], '%value' => $value)));
      }
    }

    // Image style overview form (ordering and renaming).

    // Confirm the order of effects is maintained according to the order we
    // added the fields.
    $effect_edits_order = array_keys($effect_edits);
    $effects_order = array_values($style['effects']);
    $order_correct = TRUE;
    foreach ($effects_order as $index => $effect) {
      if ($effect_edits_order[$index] != $effect['name']) {
        $order_correct = FALSE;
      }
    }
    $this->assertTrue($order_correct, 'The order of the effects is correctly set by default.');

    // Test the style overview form.
    // Change the name of the style and adjust the weights of effects.
    $style_name = strtolower($this->randomName(10));
    $style_label = $this->randomString();
    $weight = count($effect_edits);
    $edit = array(
      'name' => $style_name,
      'label' => $style_label,
    );
    foreach ($style['effects'] as $ieid => $effect) {
      $edit['effects[' . $ieid . '][weight]'] = $weight;
      $weight--;
    }

    // Create an image to make sure it gets flushed after saving.
    $image_path = $this->createSampleImage($style);
    $this->assertEqual($this->getImageCount($style), 1, format_string('Image style %style image %file successfully generated.', array('%style' => $style['label'], '%file' => $image_path)));

    $this->drupalPost($style_path, $edit, t('Update style'));

    // Note that after changing the style name, the style path is changed.
    $style_path = 'admin/config/media/image-styles/edit/' . $style_name;

    // Check that the URL was updated.
    $this->drupalGet($style_path);
    $this->assertResponse(200, format_string('Image style %original renamed to %new', array('%original' => $style['label'], '%new' => $style_label)));

    // Check that the image was flushed after updating the style.
    // This is especially important when renaming the style. Make sure that
    // the old image directory has been deleted.
    $this->assertEqual($this->getImageCount($style), 0, format_string('Image style %style was flushed after renaming the style and updating the order of effects.', array('%style' => $style['label'])));

    // Load the style by the new name with the new weights.
    drupal_static_reset('image_styles');
    $style = image_style_load($style_name, NULL);

    // Confirm the new style order was saved.
    $effect_edits_order = array_reverse($effect_edits_order);
    $effects_order = array_values($style['effects']);
    $order_correct = TRUE;
    foreach ($effects_order as $index => $effect) {
      if ($effect_edits_order[$index] != $effect['name']) {
        $order_correct = FALSE;
      }
    }
    $this->assertTrue($order_correct, 'The order of the effects is correctly set by default.');

    // Image effect deletion form.

    // Create an image to make sure it gets flushed after deleting an effect.
    $image_path = $this->createSampleImage($style);
    $this->assertEqual($this->getImageCount($style), 1, format_string('Image style %style image %file successfully generated.', array('%style' => $style['label'], '%file' => $image_path)));

    // Test effect deletion form.
    $effect = array_pop($style['effects']);
    $this->drupalPost($style_path . '/effects/' . $effect['ieid'] . '/delete', array(), t('Delete'));
    $this->assertRaw(t('The image effect %name has been deleted.', array('%name' => $effect['label'])), 'Image effect deleted.');

    // Style deletion form.

    // Delete the style.
    $this->drupalPost('admin/config/media/image-styles/delete/' . $style_name, array(), t('Delete'));

    // Confirm the style directory has been removed.
    $directory = file_default_scheme() . '://styles/' . $style_name;
    $this->assertFalse(is_dir($directory), format_string('Image style %style directory removed on style deletion.', array('%style' => $style['label'])));

    drupal_static_reset('image_styles');
    $this->assertFalse(image_style_load($style_name), format_string('Image style %style successfully deleted.', array('%style' => $style['label'])));

  }

  /**
   * Test to override, edit, then revert a style.
   */
  function testDefaultStyle() {
    // Setup a style to be created and effects to add to it.
    $style_name = 'thumbnail';
    $style_label = 'Thumbnail (100x100)';
    $edit_path = 'admin/config/media/image-styles/edit/' . $style_name;
    $delete_path = 'admin/config/media/image-styles/delete/' . $style_name;
    $revert_path = 'admin/config/media/image-styles/revert/' . $style_name;

    // Ensure deleting a default is not possible.
    $this->drupalGet($delete_path);
    $this->assertText(t('Page not found'), 'Default styles may not be deleted.');

    // Ensure that editing a default is not possible (without overriding).
    $this->drupalGet($edit_path);
    $disabled_field = $this->xpath('//input[@id=:id and @disabled="disabled"]', array(':id' => 'edit-name'));
    $this->assertTrue($disabled_field, 'Default styles may not be renamed.');
    $this->assertNoField('edit-submit', 'Default styles may not be edited.');
    $this->assertNoField('edit-add', 'Default styles may not have new effects added.');

    // Create an image to make sure the default works before overriding.
    drupal_static_reset('image_styles');
    $style = image_style_load($style_name);
    $image_path = $this->createSampleImage($style);
    $this->assertEqual($this->getImageCount($style), 1, format_string('Image style %style image %file successfully generated.', array('%style' => $style['name'], '%file' => $image_path)));

    // Verify that effects attached to a default style do not have an ieid key.
    foreach ($style['effects'] as $effect) {
      $this->assertFalse(isset($effect['ieid']), format_string('The %effect effect does not have an ieid.', array('%effect' => $effect['name'])));
    }

    // Override the default.
    $this->drupalPost($edit_path, array(), t('Override defaults'));
    $this->assertRaw(t('The %style style has been overridden, allowing you to change its settings.', array('%style' => $style_label)), 'Default image style may be overridden.');

    // Add sample effect to the overridden style.
    $this->drupalPost($edit_path, array('new' => 'image_desaturate'), t('Add'));
    drupal_static_reset('image_styles');
    $style = image_style_load($style_name);

    // Verify that effects attached to the style have an ieid now.
    foreach ($style['effects'] as $effect) {
      $this->assertTrue(isset($effect['ieid']), format_string('The %effect effect has an ieid.', array('%effect' => $effect['name'])));
    }

    // The style should now have 2 effect, the original scale provided by core
    // and the desaturate effect we added in the override.
    $effects = array_values($style['effects']);
    $this->assertEqual($effects[0]['name'], 'image_scale', 'The default effect still exists in the overridden style.');
    $this->assertEqual($effects[1]['name'], 'image_desaturate', 'The added effect exists in the overridden style.');

    // Check that we are able to rename an overridden style.
    $this->drupalGet($edit_path);
    $disabled_field = $this->xpath('//input[@id=:id and @disabled="disabled"]', array(':id' => 'edit-name'));
    $this->assertFalse($disabled_field, 'Overridden styles may be renamed.');

    // Create an image to ensure the override works properly.
    $image_path = $this->createSampleImage($style);
    $this->assertEqual($this->getImageCount($style), 1, format_string('Image style %style image %file successfully generated.', array('%style' => $style['label'], '%file' => $image_path)));

    // Revert the image style.
    $this->drupalPost($revert_path, array(), t('Revert'));
    drupal_static_reset('image_styles');
    $style = image_style_load($style_name);

    // The style should now have the single effect for scale.
    $effects = array_values($style['effects']);
    $this->assertEqual($effects[0]['name'], 'image_scale', 'The default effect still exists in the reverted style.');
    $this->assertFalse(array_key_exists(1, $effects), 'The added effect has been removed in the reverted style.');
  }

  /**
   * Test deleting a style and choosing a replacement style.
   */
  function testStyleReplacement() {
    // Create a new style.
    $style_name = strtolower($this->randomName(10));
    $style_label = $this->randomString();
    image_style_save(array('name' => $style_name, 'label' => $style_label));
    $style_path = 'admin/config/media/image-styles/edit/' . $style_name;

    // Create an image field that uses the new style.
    $field_name = strtolower($this->randomName(10));
    $this->createImageField($field_name, 'article');
    $instance = field_info_instance('node', $field_name, 'article');
    $instance['display']['default']['type'] = 'image';
    $instance['display']['default']['settings']['image_style'] = $style_name;
    field_update_instance($instance);

    // Create a new node with an image attached.
    $test_image = current($this->drupalGetTestFiles('image'));
    $nid = $this->uploadNodeImage($test_image, $field_name, 'article');
    $node = node_load($nid);

    // Test that image is displayed using newly created style.
    $this->drupalGet('node/' . $nid);
    $this->assertRaw(check_plain(image_style_url($style_name, $node->{$field_name}[LANGUAGE_NONE][0]['uri'])), format_string('Image displayed using style @style.', array('@style' => $style_name)));

    // Rename the style and make sure the image field is updated.
    $new_style_name = strtolower($this->randomName(10));
    $new_style_label = $this->randomString();
    $edit = array(
      'name' => $new_style_name,
      'label' => $new_style_label,
    );
    $this->drupalPost('admin/config/media/image-styles/edit/' . $style_name, $edit, t('Update style'));
    $this->assertText(t('Changes to the style have been saved.'), format_string('Style %name was renamed to %new_name.', array('%name' => $style_name, '%new_name' => $new_style_name)));
    $this->drupalGet('node/' . $nid);
    $this->assertRaw(check_plain(image_style_url($new_style_name, $node->{$field_name}[LANGUAGE_NONE][0]['uri'])), format_string('Image displayed using style replacement style.'));

    // Delete the style and choose a replacement style.
    $edit = array(
      'replacement' => 'thumbnail',
    );
    $this->drupalPost('admin/config/media/image-styles/delete/' . $new_style_name, $edit, t('Delete'));
    $message = t('Style %name was deleted.', array('%name' => $new_style_label));
    $this->assertRaw($message, $message);

    $this->drupalGet('node/' . $nid);
    $this->assertRaw(check_plain(image_style_url('thumbnail', $node->{$field_name}[LANGUAGE_NONE][0]['uri'])), format_string('Image displayed using style replacement style.'));
  }

  /**
   * Test disabling a module providing an effect in use by an image style.
   */
  function testOrphanedEffect() {
    // This will not check whether anything depends on the module.
    module_disable(array('image_module_test'), FALSE);
    $this->drupalGet('admin/config/media/image-styles');
    $this->assertText('Test Image Style', 'Image style with an orphaned effect displayed in the list of styles.');
    $image_log = db_query_range('SELECT message FROM {watchdog} WHERE type = :type ORDER BY wid DESC', 0, 1, array(':type' => 'image'))->fetchField();
    $this->assertEqual('Image style %style_name has an effect %effect_name with no definition.', $image_log, 'A watchdog message was logged for the broken image style effect');
  }
}

/**
 * Test class to check that formatters and display settings are working.
 */
class ImageFieldDisplayTestCase extends ImageFieldTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Image field display tests',
      'description' => 'Test the display of image fields.',
      'group' => 'Image',
    );
  }

  /**
   * Test image formatters on node display for public files.
   */
  function testImageFieldFormattersPublic() {
    $this->_testImageFieldFormatters('public');
  }

  /**
   * Test image formatters on node display for private files.
   */
  function testImageFieldFormattersPrivate() {
    // Remove access content permission from anonymous users.
    user_role_change_permissions(DRUPAL_ANONYMOUS_RID, array('access content' => FALSE));
    $this->_testImageFieldFormatters('private');
  }

  /**
   * Test image formatters on node display.
   */
  function _testImageFieldFormatters($scheme) {
    $field_name = strtolower($this->randomName());
    $this->createImageField($field_name, 'article', array('uri_scheme' => $scheme));
    // Create a new node with an image attached.
    $test_image = current($this->drupalGetTestFiles('image'));
    $nid = $this->uploadNodeImage($test_image, $field_name, 'article');
    $node = node_load($nid, NULL, TRUE);

    // Test that the default formatter is being used.
    $image_uri = $node->{$field_name}[LANGUAGE_NONE][0]['uri'];
    $image_info = array(
      'path' => $image_uri,
      'width' => 40,
      'height' => 20,
    );
    $default_output = theme('image', $image_info);
    $this->assertRaw($default_output, 'Default formatter displaying correctly on full node view.');

    // Test the image linked to file formatter.
    $instance = field_info_instance('node', $field_name, 'article');
    $instance['display']['default']['type'] = 'image';
    $instance['display']['default']['settings']['image_link'] = 'file';
    field_update_instance($instance);
    $default_output = l(theme('image', $image_info), file_create_url($image_uri), array('html' => TRUE));
    $this->drupalGet('node/' . $nid);
    $this->assertRaw($default_output, 'Image linked to file formatter displaying correctly on full node view.');
    // Verify that the image can be downloaded.
    $this->assertEqual(file_get_contents($test_image->uri), $this->drupalGet(file_create_url($image_uri)), 'File was downloaded successfully.');
    if ($scheme == 'private') {
      // Only verify HTTP headers when using private scheme and the headers are
      // sent by Drupal.
      $this->assertEqual($this->drupalGetHeader('Content-Type'), 'image/png', 'Content-Type header was sent.');
      $this->assertEqual($this->drupalGetHeader('Cache-Control'), 'private', 'Cache-Control header was sent.');

      // Log out and try to access the file.
      $this->drupalLogout();
      $this->drupalGet(file_create_url($image_uri));
      $this->assertResponse('403', 'Access denied to original image as anonymous user.');

      // Log in again.
      $this->drupalLogin($this->admin_user);
    }

    // Test the image linked to content formatter.
    $instance['display']['default']['settings']['image_link'] = 'content';
    field_update_instance($instance);
    $default_output = l(theme('image', $image_info), 'node/' . $nid, array('html' => TRUE, 'attributes' => array('class' => 'active')));
    $this->drupalGet('node/' . $nid);
    $this->assertRaw($default_output, 'Image linked to content formatter displaying correctly on full node view.');

    // Test the image style 'thumbnail' formatter.
    $instance['display']['default']['settings']['image_link'] = '';
    $instance['display']['default']['settings']['image_style'] = 'thumbnail';
    field_update_instance($instance);
    // Ensure the derivative image is generated so we do not have to deal with
    // image style callback paths.
    $this->drupalGet(image_style_url('thumbnail', $image_uri));
    // Need to create the URL again since it will change if clean URLs
    // are disabled.
    $image_info['path'] = image_style_url('thumbnail', $image_uri);
    $image_info['width'] = 100;
    $image_info['height'] = 50;
    $default_output = theme('image', $image_info);
    $this->drupalGet('node/' . $nid);
    $this->assertRaw($default_output, 'Image style thumbnail formatter displaying correctly on full node view.');

    if ($scheme == 'private') {
      // Log out and try to access the file.
      $this->drupalLogout();
      $this->drupalGet(image_style_url('thumbnail', $image_uri));
      $this->assertResponse('403', 'Access denied to image style thumbnail as anonymous user.');
    }
  }

  /**
   * Tests for image field settings.
   */
  function testImageFieldSettings() {
    $test_image = current($this->drupalGetTestFiles('image'));
    list(, $test_image_extension) = explode('.', $test_image->filename);
    $field_name = strtolower($this->randomName());
    $instance_settings = array(
      'alt_field' => 1,
      'file_extensions' => $test_image_extension,
      'max_filesize' => '50 KB',
      'max_resolution' => '100x100',
      'min_resolution' => '10x10',
      'title_field' => 1,
    );
    $widget_settings = array(
      'preview_image_style' => 'medium',
    );
    $field = $this->createImageField($field_name, 'article', array(), $instance_settings, $widget_settings);
    $field['deleted'] = 0;
    $table = _field_sql_storage_tablename($field);
    $schema = drupal_get_schema($table, TRUE);
    $instance = field_info_instance('node', $field_name, 'article');

    $this->drupalGet('node/add/article');
    $this->assertText(t('Files must be less than 50 KB.'), 'Image widget max file size is displayed on article form.');
    $this->assertText(t('Allowed file types: ' . $test_image_extension . '.'), 'Image widget allowed file types displayed on article form.');
    $this->assertText(t('Images must be at least 10x10 pixels. Images larger than 100x100 pixels will be resized.'), 'Image widget allowed resolution displayed on article form.');

    // We have to create the article first and then edit it because the alt
    // and title fields do not display until the image has been attached.
    $nid = $this->uploadNodeImage($test_image, $field_name, 'article');
    $this->drupalGet('node/' . $nid . '/edit');
    $this->assertFieldByName($field_name . '[' . LANGUAGE_NONE . '][0][alt]', '', 'Alt field displayed on article form.');
    $this->assertFieldByName($field_name . '[' . LANGUAGE_NONE . '][0][title]', '', 'Title field displayed on article form.');
    // Verify that the attached image is being previewed using the 'medium'
    // style.
    $node = node_load($nid, NULL, TRUE);
    $image_info = array(
      'path' => image_style_url('medium', $node->{$field_name}[LANGUAGE_NONE][0]['uri']),
      'width' => 220,
      'height' => 110,
    );
    $default_output = theme('image', $image_info);
    $this->assertRaw($default_output, "Preview image is displayed using 'medium' style.");

    // Add alt/title fields to the image and verify that they are displayed.
    $image_info = array(
      'path' => $node->{$field_name}[LANGUAGE_NONE][0]['uri'],
      'alt' => $this->randomName(),
      'title' => $this->randomName(),
      'width' => 40,
      'height' => 20,
    );
    $edit = array(
      $field_name . '[' . LANGUAGE_NONE . '][0][alt]' => $image_info['alt'],
      $field_name . '[' . LANGUAGE_NONE . '][0][title]' => $image_info['title'],
    );
    $this->drupalPost('node/' . $nid . '/edit', $edit, t('Save'));
    $default_output = theme('image', $image_info);
    $this->assertRaw($default_output, 'Image displayed using user supplied alt and title attributes.');

    // Verify that alt/title longer than allowed results in a validation error.
    $test_size = 2000;
    $edit = array(
      $field_name . '[' . LANGUAGE_NONE . '][0][alt]' => $this->randomName($test_size),
      $field_name . '[' . LANGUAGE_NONE . '][0][title]' => $this->randomName($test_size),
    );
    $this->drupalPost('node/' . $nid . '/edit', $edit, t('Save'));
    $this->assertRaw(t('Alternate text cannot be longer than %max characters but is currently %length characters long.', array(
      '%max' => $schema['fields'][$field_name .'_alt']['length'],
      '%length' => $test_size,
    )));
    $this->assertRaw(t('Title cannot be longer than %max characters but is currently %length characters long.', array(
      '%max' => $schema['fields'][$field_name .'_title']['length'],
      '%length' => $test_size,
    )));
  }

  /**
   * Test passing attributes into the image field formatters.
   */
  function testImageFieldFormatterAttributes() {
    $image = theme('image_formatter', array(
      'item' => array(
        'uri' => 'http://example.com/example.png',
        'attributes' => array(
          'data-image-field-formatter' => 'testFound',
        ),
        'alt' => t('Image field formatter attribute test.'),
        'title' => t('Image field formatter'),
      ),
    ));
    $this->assertTrue(stripos($image, 'testFound') > 0, 'Image field formatters can have attributes.');
  }

  /**
   * Test use of a default image with an image field.
   */
  function testImageFieldDefaultImage() {
    // Create a new image field.
    $field_name = strtolower($this->randomName());
    $this->createImageField($field_name, 'article');

    // Create a new node, with no images and verify that no images are
    // displayed.
    $node = $this->drupalCreateNode(array('type' => 'article'));
    $this->drupalGet('node/' . $node->nid);
    // Verify that no image is displayed on the page by checking for the class
    // that would be used on the image field.
    $this->assertNoPattern('<div class="(.*?)field-name-' . strtr($field_name, '_', '-') . '(.*?)">', 'No image displayed when no image is attached and no default image specified.');

    // Add a default image to the public imagefield instance.
    $images = $this->drupalGetTestFiles('image');
    $edit = array(
      'files[field_settings_default_image]' => drupal_realpath($images[0]->uri),
    );
    $this->drupalPost('admin/structure/types/manage/article/fields/' . $field_name, $edit, t('Save settings'));
    // Clear field info cache so the new default image is detected.
    field_info_cache_clear();
    $field = field_info_field($field_name);
    $image = file_load($field['settings']['default_image']);
    $this->assertTrue($image->status == FILE_STATUS_PERMANENT, 'The default image status is permanent.');
    $default_output = theme('image', array('path' => $image->uri));
    $this->drupalGet('node/' . $node->nid);
    $this->assertRaw($default_output, 'Default image displayed when no user supplied image is present.');

    // Create a node with an image attached and ensure that the default image
    // is not displayed.
    $nid = $this->uploadNodeImage($images[1], $field_name, 'article');
    $node = node_load($nid, NULL, TRUE);
    $image_info = array(
      'path' => $node->{$field_name}[LANGUAGE_NONE][0]['uri'],
      'width' => 40,
      'height' => 20,
    );
    $image_output = theme('image', $image_info);
    $this->drupalGet('node/' . $nid);
    $this->assertNoRaw($default_output, 'Default image is not displayed when user supplied image is present.');
    $this->assertRaw($image_output, 'User supplied image is displayed.');

    // Remove default image from the field and make sure it is no longer used.
    $edit = array(
      'field[settings][default_image][fid]' => 0,
    );
    $this->drupalPost('admin/structure/types/manage/article/fields/' . $field_name, $edit, t('Save settings'));
    // Clear field info cache so the new default image is detected.
    field_info_cache_clear();
    $field = field_info_field($field_name);
    $this->assertFalse($field['settings']['default_image'], 'Default image removed from field.');
    // Create an image field that uses the private:// scheme and test that the
    // default image works as expected.
    $private_field_name = strtolower($this->randomName());
    $this->createImageField($private_field_name, 'article', array('uri_scheme' => 'private'));
    // Add a default image to the new field.
    $edit = array(
      'files[field_settings_default_image]' => drupal_realpath($images[1]->uri),
    );
    $this->drupalPost('admin/structure/types/manage/article/fields/' . $private_field_name, $edit, t('Save settings'));
    $private_field = field_info_field($private_field_name);
    $image = file_load($private_field['settings']['default_image']);
    $this->assertEqual('private', file_uri_scheme($image->uri), 'Default image uses private:// scheme.');
    $this->assertTrue($image->status == FILE_STATUS_PERMANENT, 'The default image status is permanent.');
    // Create a new node with no image attached and ensure that default private
    // image is displayed.
    $node = $this->drupalCreateNode(array('type' => 'article'));
    $default_output = theme('image', array('path' => $image->uri));
    $this->drupalGet('node/' . $node->nid);
    $this->assertRaw($default_output, 'Default private image displayed when no user supplied image is present.');
  }

  /**
   * Tests the display of image field with the missing FID.
   */
  function testMissingImageFieldDisplay() {
    $field_name = strtolower($this->randomName());
    $type_name = 'article';
    $field_settings = array(
      'display_field' => '1',
      'display_default' => '1',
    );
    $instance_settings = array(
      'description_field' => '1',
    );
    $widget_settings = array();
    $this->createImageField($field_name, $type_name, $field_settings, $instance_settings, $widget_settings);
    $images = $this->drupalGetTestFiles('image');

    // Create a new node with the uploaded file.
    $nid = $this->uploadNodeImage($images[1], $field_name, 'article');
    // Delete uploaded file from file_managed table.
    $max_fid_after = db_query('SELECT MAX(fid) AS fid FROM {file_managed}')->fetchField();
    $uploaded_file = file_load($max_fid_after);
    file_delete($uploaded_file, TRUE);
    // Clear field cache.
    field_cache_clear();
    // Check the node detail if the file is loaded.
    $this->drupalGet('node/' . $nid);
    $this->assertResponse(200);
  }
}

/**
 * Test class to check for various validations.
 */
class ImageFieldValidateTestCase extends ImageFieldTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Image field validation tests',
      'description' => 'Tests validation functions such as min/max resolution.',
      'group' => 'Image',
    );
  }

  /**
   * Test min/max resolution settings.
   */
  function testResolution() {
    $field_name = strtolower($this->randomName());
    $min_resolution = 50;
    $max_resolution = 100;
    $instance_settings = array(
      'max_resolution' => $max_resolution . 'x' . $max_resolution,
      'min_resolution' => $min_resolution . 'x' . $min_resolution,
    );
    $this->createImageField($field_name, 'article', array(), $instance_settings);

    // We want a test image that is too small, and a test image that is too
    // big, so cycle through test image files until we have what we need.
    $image_that_is_too_big = FALSE;
    $image_that_is_too_small = FALSE;
    foreach ($this->drupalGetTestFiles('image') as $image) {
      $info = image_get_info($image->uri);
      if ($info['width'] > $max_resolution) {
        $image_that_is_too_big = $image;
      }
      if ($info['width'] < $min_resolution) {
        $image_that_is_too_small = $image;
      }
      if ($image_that_is_too_small && $image_that_is_too_big) {
        break;
      }
    }
    $nid = $this->uploadNodeImage($image_that_is_too_small, $field_name, 'article');
    $this->assertText(t('The specified file ' . $image_that_is_too_small->filename . ' could not be uploaded. The image is too small; the minimum dimensions are 50x50 pixels.'), 'Node save failed when minimum image resolution was not met.');
    $nid = $this->uploadNodeImage($image_that_is_too_big, $field_name, 'article');
    $this->assertText(t('The image was resized to fit within the maximum allowed dimensions of 100x100 pixels.'), 'Image exceeding max resolution was properly resized.');

    // Verify that image resolution can contain only positive numbers.
    $edit = array(
      'instance[settings][max_resolution][x]' => -10,
      'instance[settings][max_resolution][y]' => -10,
    );
    $this->drupalPost('admin/structure/types/manage/article/fields/' . $field_name, $edit, t('Save settings'));
    $this->assertRaw(t('Height and width values must be positive numbers.'));

    $edit = array(
      'instance[settings][max_resolution][x]' => 'x',
      'instance[settings][max_resolution][y]' => 'x',
    );
    $this->drupalPost('admin/structure/types/manage/article/fields/' . $field_name, $edit, t('Save settings'));
    $this->assertRaw(t('Height and width values must be numeric.'));
  }
}

/**
 * Tests that images have correct dimensions when styled.
 */
class ImageDimensionsTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Image dimensions',
      'description' => 'Tests that images have correct dimensions when styled.',
      'group' => 'Image',
    );
  }

  function setUp() {
    parent::setUp('image_module_test');
  }

  /**
   * Test styled image dimensions cumulatively.
   */
  function testImageDimensions() {
    // Create a working copy of the file.
    $files = $this->drupalGetTestFiles('image');
    $file = reset($files);
    $original_uri = file_unmanaged_copy($file->uri, 'public://', FILE_EXISTS_RENAME);

    // Create a style.
    $style = image_style_save(array('name' => 'test', 'label' => 'Test'));
    $generated_uri = 'public://styles/test/public/'. drupal_basename($original_uri);
    $url = image_style_url('test', $original_uri);

    $variables = array(
      'style_name' => 'test',
      'path' => $original_uri,
      'width' => 40,
      'height' => 20,
    );

    // Scale an image that is wider than it is high.
    $effect = array(
      'name' => 'image_scale',
      'data' => array(
        'width' => 120,
        'height' => 90,
        'upscale' => TRUE,
      ),
      'isid' => $style['isid'],
    );

    image_effect_save($effect);
    $img_tag = theme_image_style($variables);
    $this->assertEqual($img_tag, '<img typeof="foaf:Image" src="' . check_plain($url) . '" width="120" height="60" alt="" />', 'Expected img tag was found.');
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $this->drupalGet($url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it.');
    $image_info = image_get_info($generated_uri);
    $this->assertEqual($image_info['width'], 120, 'Expected width was found.');
    $this->assertEqual($image_info['height'], 60, 'Expected height was found.');

    // Rotate 90 degrees anticlockwise.
    $effect = array(
      'name' => 'image_rotate',
      'data' => array(
        'degrees' => -90,
        'random' => FALSE,
      ),
      'isid' => $style['isid'],
    );

    image_effect_save($effect);
    $img_tag = theme_image_style($variables);
    $this->assertEqual($img_tag, '<img typeof="foaf:Image" src="' . check_plain($url) . '" width="60" height="120" alt="" />', 'Expected img tag was found.');
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $this->drupalGet($url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it.');
    $image_info = image_get_info($generated_uri);
    $this->assertEqual($image_info['width'], 60, 'Expected width was found.');
    $this->assertEqual($image_info['height'], 120, 'Expected height was found.');

    // Scale an image that is higher than it is wide (rotated by previous effect).
    $effect = array(
      'name' => 'image_scale',
      'data' => array(
        'width' => 120,
        'height' => 90,
        'upscale' => TRUE,
      ),
      'isid' => $style['isid'],
    );

    image_effect_save($effect);
    $img_tag = theme_image_style($variables);
    $this->assertEqual($img_tag, '<img typeof="foaf:Image" src="' . check_plain($url) . '" width="45" height="90" alt="" />', 'Expected img tag was found.');
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $this->drupalGet($url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it.');
    $image_info = image_get_info($generated_uri);
    $this->assertEqual($image_info['width'], 45, 'Expected width was found.');
    $this->assertEqual($image_info['height'], 90, 'Expected height was found.');

    // Test upscale disabled.
    $effect = array(
      'name' => 'image_scale',
      'data' => array(
        'width' => 400,
        'height' => 200,
        'upscale' => FALSE,
      ),
      'isid' => $style['isid'],
    );

    image_effect_save($effect);
    $img_tag = theme_image_style($variables);
    $this->assertEqual($img_tag, '<img typeof="foaf:Image" src="' . check_plain($url) . '" width="45" height="90" alt="" />', 'Expected img tag was found.');
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $this->drupalGet($url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it.');
    $image_info = image_get_info($generated_uri);
    $this->assertEqual($image_info['width'], 45, 'Expected width was found.');
    $this->assertEqual($image_info['height'], 90, 'Expected height was found.');

    // Add a desaturate effect.
    $effect = array(
      'name' => 'image_desaturate',
      'data' => array(),
      'isid' => $style['isid'],
    );

    image_effect_save($effect);
    $img_tag = theme_image_style($variables);
    $this->assertEqual($img_tag, '<img typeof="foaf:Image" src="' . check_plain($url) . '" width="45" height="90" alt="" />', 'Expected img tag was found.');
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $this->drupalGet($url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it.');
    $image_info = image_get_info($generated_uri);
    $this->assertEqual($image_info['width'], 45, 'Expected width was found.');
    $this->assertEqual($image_info['height'], 90, 'Expected height was found.');

    // Add a random rotate effect.
    $effect = array(
      'name' => 'image_rotate',
      'data' => array(
        'degrees' => 180,
        'random' => TRUE,
      ),
      'isid' => $style['isid'],
    );

    image_effect_save($effect);
    $img_tag = theme_image_style($variables);
    $this->assertEqual($img_tag, '<img typeof="foaf:Image" src="' . check_plain($url) . '" alt="" />', 'Expected img tag was found.');
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $this->drupalGet($url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it.');


    // Add a crop effect.
    $effect = array(
      'name' => 'image_crop',
      'data' => array(
        'width' => 30,
        'height' => 30,
        'anchor' => 'center-center',
      ),
      'isid' => $style['isid'],
    );

    image_effect_save($effect);
    $img_tag = theme_image_style($variables);
    $this->assertEqual($img_tag, '<img typeof="foaf:Image" src="' . check_plain($url) . '" width="30" height="30" alt="" />', 'Expected img tag was found.');
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $this->drupalGet($url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it.');
    $image_info = image_get_info($generated_uri);
    $this->assertEqual($image_info['width'], 30, 'Expected width was found.');
    $this->assertEqual($image_info['height'], 30, 'Expected height was found.');

    // Rotate to a non-multiple of 90 degrees.
    $effect = array(
      'name' => 'image_rotate',
      'data' => array(
        'degrees' => 57,
        'random' => FALSE,
      ),
      'isid' => $style['isid'],
    );

    $effect = image_effect_save($effect);
    $img_tag = theme_image_style($variables);
    $this->assertEqual($img_tag, '<img typeof="foaf:Image" src="' . check_plain($url) . '" alt="" />', 'Expected img tag was found.');
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $this->drupalGet($url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it.');

    image_effect_delete($effect);

    // Ensure that an effect with no dimensions callback unsets the dimensions.
    // This ensures compatibility with 7.0 contrib modules.
    $effect = array(
      'name' => 'image_module_test_null',
      'data' => array(),
      'isid' => $style['isid'],
    );

    image_effect_save($effect);
    $img_tag = theme_image_style($variables);
    $this->assertEqual($img_tag, '<img typeof="foaf:Image" src="' . check_plain($url) . '" alt="" />', 'Expected img tag was found.');
  }
}

/**
 * Tests image_dimensions_scale().
 */
class ImageDimensionsScaleTestCase extends DrupalUnitTestCase {
  public static function getInfo() {
    return array(
      'name' => 'image_dimensions_scale()',
      'description' => 'Tests all control flow branches in image_dimensions_scale().',
      'group' => 'Image',
    );
  }

  /**
   * Tests all control flow branches in image_dimensions_scale().
   */
  function testImageDimensionsScale() {
    // Define input / output datasets to test different branch conditions.
    $test = array();

    // Test branch conditions:
    // - No height.
    // - Upscale, don't need to upscale.
    $tests[] = array(
      'input' => array(
        'dimensions' => array(
          'width' => 1000,
          'height' => 2000,
        ),
        'width' => 200,
        'height' => NULL,
        'upscale' => TRUE,
      ),
      'output' => array(
        'dimensions' => array(
          'width' => 200,
          'height' => 400,
        ),
        'return_value' => TRUE,
      ),
    );

    // Test branch conditions:
    // - No width.
    // - Don't upscale, don't need to upscale.
    $tests[] = array(
      'input' => array(
        'dimensions' => array(
          'width' => 1000,
          'height' => 800,
        ),
        'width' => NULL,
        'height' => 140,
        'upscale' => FALSE,
      ),
      'output' => array(
        'dimensions' => array(
          'width' => 175,
          'height' => 140,
        ),
        'return_value' => TRUE,
      ),
    );

    // Test branch conditions:
    // - Source aspect ratio greater than target.
    // - Upscale, need to upscale.
    $tests[] = array(
      'input' => array(
        'dimensions' => array(
          'width' => 8,
          'height' => 20,
        ),
        'width' => 200,
        'height' => 140,
        'upscale' => TRUE,
      ),
      'output' => array(
        'dimensions' => array(
          'width' => 56,
          'height' => 140,
        ),
        'return_value' => TRUE,
      ),
    );

    // Test branch condition: target aspect ratio greater than source.
    $tests[] = array(
      'input' => array(
        'dimensions' => array(
          'width' => 2000,
          'height' => 800,
        ),
        'width' => 200,
        'height' => 140,
        'upscale' => FALSE,
      ),
      'output' => array(
        'dimensions' => array(
          'width' => 200,
          'height' => 80,
        ),
        'return_value' => TRUE,
      ),
    );

    // Test branch condition: don't upscale, need to upscale.
    $tests[] = array(
      'input' => array(
        'dimensions' => array(
          'width' => 100,
          'height' => 50,
        ),
        'width' => 200,
        'height' => 140,
        'upscale' => FALSE,
      ),
      'output' => array(
        'dimensions' => array(
          'width' => 100,
          'height' => 50,
        ),
        'return_value' => FALSE,
      ),
    );

    foreach ($tests as $test) {
      // Process the test dataset.
      $return_value = image_dimensions_scale($test['input']['dimensions'], $test['input']['width'], $test['input']['height'], $test['input']['upscale']);

      // Check the width.
      $this->assertEqual($test['output']['dimensions']['width'], $test['input']['dimensions']['width'], format_string('Computed width (@computed_width) equals expected width (@expected_width)', array('@computed_width' => $test['output']['dimensions']['width'], '@expected_width' => $test['input']['dimensions']['width'])));

      // Check the height.
      $this->assertEqual($test['output']['dimensions']['height'], $test['input']['dimensions']['height'], format_string('Computed height (@computed_height) equals expected height (@expected_height)', array('@computed_height' => $test['output']['dimensions']['height'], '@expected_height' => $test['input']['dimensions']['height'])));

      // Check the return value.
      $this->assertEqual($test['output']['return_value'], $return_value, 'Correct return value.');
    }
  }
}

/**
 * Tests default image settings.
 */
class ImageFieldDefaultImagesTestCase extends ImageFieldTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Image field default images tests',
      'description' => 'Tests setting up default images both to the field and field instance.',
      'group' => 'Image',
    );
  }

  function setUp() {
    parent::setUp(array('field_ui'));
  }

  /**
   * Tests CRUD for fields and fields instances with default images.
   */
  function testDefaultImages() {
    // Create files to use as the default images.
    $files = $this->drupalGetTestFiles('image');
    $default_images = array();
    foreach (array('field', 'instance', 'instance2', 'field_new', 'instance_new') as $image_target) {
      $file = array_pop($files);
      $file = file_save($file);
      $default_images[$image_target] = $file;
    }

    // Create an image field and add an instance to the article content type.
    $field_name = strtolower($this->randomName());
    $field_settings = array(
      'default_image' => $default_images['field']->fid,
    );
    $instance_settings = array(
      'default_image' => $default_images['instance']->fid,
    );
    $widget_settings = array(
      'preview_image_style' => 'medium',
    );
    $this->createImageField($field_name, 'article', $field_settings, $instance_settings, $widget_settings);
    $field = field_info_field($field_name);
    $instance = field_info_instance('node', $field_name, 'article');

    // Add another instance with another default image to the page content type.
    $instance2 = array_merge($instance, array(
      'bundle' => 'page',
      'settings' => array(
        'default_image' => $default_images['instance2']->fid,
      ),
    ));
    field_create_instance($instance2);
    $instance2 = field_info_instance('node', $field_name, 'page');


    // Confirm the defaults are present on the article field admin form.
    $this->drupalGet("admin/structure/types/manage/article/fields/$field_name");
    $this->assertFieldByXpath(
      '//input[@name="field[settings][default_image][fid]"]',
      $default_images['field']->fid,
      format_string(
        'Article image field default equals expected file ID of @fid.',
        array('@fid' => $default_images['field']->fid)
      )
    );
    $this->assertFieldByXpath(
      '//input[@name="instance[settings][default_image][fid]"]',
      $default_images['instance']->fid,
      format_string(
        'Article image field instance default equals expected file ID of @fid.',
        array('@fid' => $default_images['instance']->fid)
      )
    );

    // Confirm the defaults are present on the page field admin form.
    $this->drupalGet("admin/structure/types/manage/page/fields/$field_name");
    $this->assertFieldByXpath(
      '//input[@name="field[settings][default_image][fid]"]',
      $default_images['field']->fid,
      format_string(
        'Page image field default equals expected file ID of @fid.',
        array('@fid' => $default_images['field']->fid)
      )
    );
    $this->assertFieldByXpath(
      '//input[@name="instance[settings][default_image][fid]"]',
      $default_images['instance2']->fid,
      format_string(
        'Page image field instance default equals expected file ID of @fid.',
        array('@fid' => $default_images['instance2']->fid)
      )
    );

    // Confirm that the image default is shown for a new article node.
    $article = $this->drupalCreateNode(array('type' => 'article'));
    $article_built = node_view($article);
    $this->assertEqual(
      $article_built[$field_name]['#items'][0]['fid'],
      $default_images['instance']->fid,
      format_string(
        'A new article node without an image has the expected default image file ID of @fid.',
        array('@fid' => $default_images['instance']->fid)
      )
    );

    // Confirm that the image default is shown for a new page node.
    $page = $this->drupalCreateNode(array('type' => 'page'));
    $page_built = node_view($page);
    $this->assertEqual(
      $page_built[$field_name]['#items'][0]['fid'],
      $default_images['instance2']->fid,
      format_string(
        'A new page node without an image has the expected default image file ID of @fid.',
        array('@fid' => $default_images['instance2']->fid)
      )
    );

    // Upload a new default for the field.
    $field['settings']['default_image'] = $default_images['field_new']->fid;
    field_update_field($field);

    // Confirm that the new field default is used on the article admin form.
    $this->drupalGet("admin/structure/types/manage/article/fields/$field_name");
    $this->assertFieldByXpath(
      '//input[@name="field[settings][default_image][fid]"]',
      $default_images['field_new']->fid,
      format_string(
        'Updated image field default equals expected file ID of @fid.',
        array('@fid' => $default_images['field_new']->fid)
      )
    );

    // Reload the nodes and confirm the field instance defaults are used.
    $article_built = node_view($article = node_load($article->nid, NULL, $reset = TRUE));
    $page_built = node_view($page = node_load($page->nid, NULL, $reset = TRUE));
    $this->assertEqual(
      $article_built[$field_name]['#items'][0]['fid'],
      $default_images['instance']->fid,
      format_string(
        'An existing article node without an image has the expected default image file ID of @fid.',
        array('@fid' => $default_images['instance']->fid)
      )
    );
    $this->assertEqual(
      $page_built[$field_name]['#items'][0]['fid'],
      $default_images['instance2']->fid,
      format_string(
        'An existing page node without an image has the expected default image file ID of @fid.',
        array('@fid' => $default_images['instance2']->fid)
      )
    );

    // Upload a new default for the article's field instance.
    $instance['settings']['default_image'] = $default_images['instance_new']->fid;
    field_update_instance($instance);

    // Confirm the new field instance default is used on the article field
    // admin form.
    $this->drupalGet("admin/structure/types/manage/article/fields/$field_name");
    $this->assertFieldByXpath(
      '//input[@name="instance[settings][default_image][fid]"]',
      $default_images['instance_new']->fid,
      format_string(
        'Updated article image field instance default equals expected file ID of @fid.',
        array('@fid' => $default_images['instance_new']->fid)
      )
    );

    // Reload the nodes.
    $article_built = node_view($article = node_load($article->nid, NULL, $reset = TRUE));
    $page_built = node_view($page = node_load($page->nid, NULL, $reset = TRUE));

    // Confirm the article uses the new default.
    $this->assertEqual(
      $article_built[$field_name]['#items'][0]['fid'],
      $default_images['instance_new']->fid,
      format_string(
        'An existing article node without an image has the expected default image file ID of @fid.',
        array('@fid' => $default_images['instance_new']->fid)
      )
    );
    // Confirm the page remains unchanged.
    $this->assertEqual(
      $page_built[$field_name]['#items'][0]['fid'],
      $default_images['instance2']->fid,
      format_string(
        'An existing page node without an image has the expected default image file ID of @fid.',
        array('@fid' => $default_images['instance2']->fid)
      )
    );

    // Remove the instance default from articles.
    $instance['settings']['default_image'] = NULL;
    field_update_instance($instance);

    // Confirm the article field instance default has been removed.
    $this->drupalGet("admin/structure/types/manage/article/fields/$field_name");
    $this->assertFieldByXpath(
      '//input[@name="instance[settings][default_image][fid]"]',
      '',
      'Updated article image field instance default has been successfully removed.'
    );

    // Reload the nodes.
    $article_built = node_view($article = node_load($article->nid, NULL, $reset = TRUE));
    $page_built = node_view($page = node_load($page->nid, NULL, $reset = TRUE));
    // Confirm the article uses the new field (not instance) default.
    $this->assertEqual(
      $article_built[$field_name]['#items'][0]['fid'],
      $default_images['field_new']->fid,
      format_string(
        'An existing article node without an image has the expected default image file ID of @fid.',
        array('@fid' => $default_images['field_new']->fid)
      )
    );
    // Confirm the page remains unchanged.
    $this->assertEqual(
      $page_built[$field_name]['#items'][0]['fid'],
      $default_images['instance2']->fid,
      format_string(
        'An existing page node without an image has the expected default image file ID of @fid.',
        array('@fid' => $default_images['instance2']->fid)
      )
    );
  }

}

/**
 * Tests image theme functions.
 */
class ImageThemeFunctionWebTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Image theme functions',
      'description' => 'Test that the image theme functions work correctly.',
      'group' => 'Image',
    );
  }

  function setUp() {
    parent::setUp(array('image'));
  }

  /**
   * Tests usage of the image field formatters.
   */
  function testImageFormatterTheme() {
    // Create an image.
    $files = $this->drupalGetTestFiles('image');
    $file = reset($files);
    $original_uri = file_unmanaged_copy($file->uri, 'public://', FILE_EXISTS_RENAME);

    // Create a style.
    image_style_save(array('name' => 'test', 'label' => 'Test'));
    $url = image_style_url('test', $original_uri);

    // Test using theme_image_formatter() without an image title, alt text, or
    // link options.
    $path = $this->randomName();
    $element = array(
      '#theme' => 'image_formatter',
      '#image_style' => 'test',
      '#item' => array(
        'uri' => $original_uri,
      ),
      '#path' => array(
        'path' => $path,
      ),
    );
    $rendered_element = render($element);
    $expected_result = '<a href="' . url($path) . '"><img typeof="foaf:Image" src="' . check_plain($url) . '" alt="" /></a>';
    $this->assertEqual($expected_result, $rendered_element, 'theme_image_formatter() correctly renders without title, alt, or path options.');

    // Link the image to a fragment on the page, and not a full URL.
    $fragment = $this->randomName();
    $element['#path']['path'] = '';
    $element['#path']['options'] = array(
      'external' => TRUE,
      'fragment' => $fragment,
    );
    $rendered_element = render($element);
    $expected_result = '<a href="#' . $fragment . '"><img typeof="foaf:Image" src="' . check_plain($url) . '" alt="" /></a>';
    $this->assertEqual($expected_result, $rendered_element, 'theme_image_formatter() correctly renders a link fragment.');
  }

}

/**
 * Tests flushing of image styles.
 */
class ImageStyleFlushTest extends ImageFieldTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Image style flushing',
      'description' => 'Tests flushing of image styles.',
      'group' => 'Image',
    );
  }

  /**
   * Given an image style and a wrapper, generate an image.
   */
  function createSampleImage($style, $wrapper) {
    static $file;

    if (!isset($file)) {
      $files = $this->drupalGetTestFiles('image');
      $file = reset($files);
    }

    // Make sure we have an image in our wrapper testing file directory.
    $source_uri = file_unmanaged_copy($file->uri, $wrapper . '://');
    // Build the derivative image.
    $derivative_uri = image_style_path($style['name'], $source_uri);
    $derivative = image_style_create_derivative($style, $source_uri, $derivative_uri);

    return $derivative ? $derivative_uri : FALSE;
  }

  /**
   * Count the number of images currently created for a style in a wrapper.
   */
  function getImageCount($style, $wrapper) {
    return count(file_scan_directory($wrapper . '://styles/' . $style['name'], '/.*/'));
  }

  /**
   * General test to flush a style.
   */
  function testFlush() {

    // Setup a style to be created and effects to add to it.
    $style_name = strtolower($this->randomName(10));
    $style_label = $this->randomString();
    $style_path = 'admin/config/media/image-styles/edit/' . $style_name;
    $effect_edits = array(
      'image_resize' => array(
        'data[width]' => 100,
        'data[height]' => 101,
      ),
      'image_scale' => array(
        'data[width]' => 110,
        'data[height]' => 111,
        'data[upscale]' => 1,
      ),
    );

    // Add style form.
    $edit = array(
      'name' => $style_name,
      'label' => $style_label,
    );
    $this->drupalPost('admin/config/media/image-styles/add', $edit, t('Create new style'));
    // Add each sample effect to the style.
    foreach ($effect_edits as $effect => $edit) {
      // Add the effect.
      $this->drupalPost($style_path, array('new' => $effect), t('Add'));
      if (!empty($edit)) {
        $this->drupalPost(NULL, $edit, t('Add effect'));
      }
    }

    // Load the saved image style.
    $style = image_style_load($style_name);

    // Create an image for the 'public' wrapper.
    $image_path = $this->createSampleImage($style, 'public');
    // Expecting to find 2 images, one is the sample.png image shown in
    // image style preview.
    $this->assertEqual($this->getImageCount($style, 'public'), 2, format_string('Image style %style image %file successfully generated.', array('%style' => $style['name'], '%file' => $image_path)));

    // Create an image for the 'private' wrapper.
    $image_path = $this->createSampleImage($style, 'private');
    $this->assertEqual($this->getImageCount($style, 'private'), 1, format_string('Image style %style image %file successfully generated.', array('%style' => $style['name'], '%file' => $image_path)));

    // Remove the 'image_scale' effect and updates the style, which in turn
    // forces an image style flush.
    $effect = array_pop($style['effects']);
    $this->drupalPost($style_path . '/effects/' . $effect['ieid'] . '/delete', array(), t('Delete'));
    $this->assertResponse(200);
    $this->drupalPost($style_path, array(), t('Update style'));
    $this->assertResponse(200);

    // Post flush, expected 1 image in the 'public' wrapper (sample.png).
    $this->assertEqual($this->getImageCount($style, 'public'), 1, format_string('Image style %style flushed correctly for %wrapper wrapper.', array('%style' => $style['name'], '%wrapper' => 'public')));

    // Post flush, expected no image in the 'private' wrapper.
    $this->assertEqual($this->getImageCount($style, 'private'), 0, format_string('Image style %style flushed correctly for %wrapper wrapper.', array('%style' => $style['name'], '%wrapper' => 'private')));
  }

}

/**
 * Tests for correct file size, dimensions and HTTP headers after scaling.
 */
class ImageStylesHTTPHeadersTestCase extends ImageFieldTestCase {

  /**
   * Generated style name.
   *
   * @var string
   */
  protected $styleName;

  /**
   * ID of generated style.
   *
   * @var int
   */
  protected $styleId;

  /**
   * {@inheritdoc}
   */
  public static function getInfo() {
    return array(
      'name' => 'Image styles HTTP headers tests',
      'description' => 'Tests file size, dimensions and HTTP headers of image styles generated with scaling.',
      'group' => 'Image',
    );
  }

  /**
   * {@inheritdoc}
   */
  public function setUp() {
    parent::setUp('image_module_test');

    $this->styleName = 'style_foo_scale';
    $style = image_style_save(array(
      'name' => $this->styleName,
      'label' => $this->randomString(),
    ));
    $this->styleId = $style['isid'];
  }

  /**
   * Test image_style_url() with a file with public scheme and upscaling.
   */
  public function testImageStyleUrlAndPathPublicAndUpscale() {
    $width = 3600;
    $height = 2400;
    $this->_imagestyleUpscale($width, $height);
    $this->_testImageStyleUrlAndPath('public', $width, $height);
  }

  /**
   * Test image_style_url() with a file with private scheme and upscaling.
   */
  public function testImageStyleUrlAndPathPrivateAndUpscale() {
    $width = 3600;
    $height = 2400;
    $this->_imagestyleUpscale($width, $height);
    $this->_testImageStyleUrlAndPath('private', $width, $height);
  }

  /**
   * Test image_style_url() with a file with public scheme and downscaling.
   */
  public function testImageStyleUrlAndPathPublicAndDownscale() {
    $width = 180;
    $height = 120;
    $this->_imagestyleUpscale($width, $height);
    $this->_testImageStyleUrlAndPath('public', $width, $height);
  }

  /**
   * Test image_style_url() with a file with private scheme and downscaling.
   */
  public function testImageStyleUrlAndPathPrivateAndDownscale() {
    $width = 180;
    $height = 120;
    $this->_imagestyleUpscale($width, $height);
    $this->_testImageStyleUrlAndPath('private', $width, $height);
  }

  /**
   * Adding upscale effect to imagestyle.
   */
  private function _imagestyleUpscale($width, $height) {
    // Scale an image that is wider than it is high.
    $effect = array(
      'name' => 'image_scale',
      'data' => array(
        'width' => $width,
        'height' => $height,
        'upscale' => TRUE,
      ),
      'isid' => $this->styleId,
    );
    image_effect_save($effect);
  }

  /**
   * Function to scale and test.
   */
  private function _testImageStyleUrlAndPath($scheme, $width, $height) {
    $directory = $scheme . '://styles/' . $this->styleName;
    $status = file_prepare_directory($directory, FILE_CREATE_DIRECTORY);
    $this->assertNotIdentical(FALSE, $status, 'Created the directory for the generated images for the test style.');

    // Create an image field that uses the new style.
    $field_name = strtolower($this->randomName(10));
    $this->createImageField($field_name, 'article', array('uri_scheme' => $scheme));
    $instance = field_info_instance('node', $field_name, 'article');
    $instance['display']['default']['type'] = 'image';
    $instance['display']['default']['settings']['image_style'] = $this->styleName;
    field_update_instance($instance);

    // Create a new node with an image attached.
    $images = $this->drupalGetTestFiles('image');
    // Taking image no. 6 because Apache sets own
    // Content-Length if response is smaller than 8000 bytes.
    $test_image = $images[6];
    $nid = $this->uploadNodeImage($test_image, $field_name, 'article');
    $node = node_load($nid);

    // Get image file from created node.
    $field_items = field_get_items('node', $node, $field_name);
    $file = file_load($field_items[0]['fid']);
    $original_uri = $file->uri;

    // Let the image_module_test module know about this file, so it can claim
    // ownership in hook_file_download().
    variable_set('image_module_test_file_download', $original_uri);

    // Get the URL of a file that has not been generated and try to create it.
    $generated_uri = image_style_path($this->styleName, $original_uri);
    $this->assertFalse(file_exists($generated_uri), 'Generated file does not exist.');
    $generate_url = image_style_url($this->styleName, $original_uri);

    // Fetch the URL that generates the file.
    $this->drupalGet($generate_url);
    $this->assertResponse(200, 'Image was generated at the URL.');
    $this->assertTrue(file_exists($generated_uri), 'Generated file does exist after we accessed it: ' . $generated_uri);

    // Store generated image info.
    $generated_image_info = image_get_info($generated_uri);

    // Compare result.
    $this->assertEqual($this->drupalGetHeader('Content-Type'), $generated_image_info['mime_type'], 'Expected Content-Type was reported.');
    $this->assertEqual($this->drupalGetHeader('Content-Length'), $generated_image_info['file_size'], 'Expected Content-Length was reported: ' . $generated_image_info['file_size'] . ' vs. ' . $this->drupalGetHeader('Content-Length'));
    $this->assertEqual($width, $generated_image_info['width'], 'Expected width was reported.');
    $this->assertEqual($height, $generated_image_info['height'], 'Expected height was reported.');
  }

}

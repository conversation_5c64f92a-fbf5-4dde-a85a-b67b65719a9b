<?php

/**
 * @file
 * Tests for forum.module.
 */

/**
 * Provides automated tests for the Forum module.
 */
class ForumTestCase extends DrupalWebTestCase {

  /**
   * A user with various administrative privileges.
   */
  protected $admin_user;

  /**
   * A user that can create forum topics and edit its own topics.
   */
  protected $edit_own_topics_user;

  /**
   * A user that can create, edit, and delete forum topics.
   */
  protected $edit_any_topics_user;

  /**
   * A user with no special privileges.
   */
  protected $web_user;

  /**
   * An array representing a container.
   */
  protected $container;

  /**
   * An array representing a forum.
   */
  protected $forum;

  /**
   * An array representing a root forum.
   */
  protected $root_forum;

  /**
   * An array of forum topic node IDs.
   */
  protected $nids;

  /**
   * A forum to delete.
   */
  protected $delete_forum;

  public static function getInfo() {
    return array(
      'name' => 'Forum functionality',
      'description' => 'Create, view, edit, delete, and change forum entries and verify its consistency in the database.',
      'group' => 'Forum',
    );
  }

  function setUp() {
    parent::setUp('taxonomy', 'comment', 'forum');
    // Create users.
    $this->admin_user = $this->drupalCreateUser(array(
      'access administration pages',
      'administer modules',
      'administer blocks',
      'administer forums',
      'administer menu',
      'administer taxonomy',
      'create forum content',
    ));
    $this->edit_any_topics_user = $this->drupalCreateUser(array(
      'access administration pages',
      'create forum content',
      'edit any forum content',
      'delete any forum content',
    ));
    $this->edit_own_topics_user = $this->drupalCreateUser(array(
      'create forum content',
      'edit own forum content',
      'delete own forum content',
    ));
    $this->web_user = $this->drupalCreateUser(array());
  }

  /**
   * Tests disabling and re-enabling the Forum module.
   */
  function testEnableForumField() {
    $this->drupalLogin($this->admin_user);

    // Disable the Forum module.
    $edit = array();
    $edit['modules[Core][forum][enable]'] = FALSE;
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');
    module_list(TRUE);
    $this->assertFalse(module_exists('forum'), 'Forum module is not enabled.');

    // Attempt to re-enable the Forum module and ensure it does not try to
    // recreate the taxonomy_forums field.
    $edit = array();
    $edit['modules[Core][forum][enable]'] = 'forum';
    $this->drupalPost('admin/modules', $edit, t('Save configuration'));
    $this->assertText(t('The configuration options have been saved.'), 'Modules status has been updated.');
    module_list(TRUE);
    $this->assertTrue(module_exists('forum'), 'Forum module is enabled.');
  }

  /**
   * Tests forum functionality through the admin and user interfaces.
   */
  function testForum() {
    //Check that the basic forum install creates a default forum topic
    $this->drupalGet("/forum");
    // Look for the "General discussion" default forum
    $this->assertText(t("General discussion"), "Found the default forum at the /forum listing");

    // Do the admin tests.
    $this->doAdminTests($this->admin_user);
    // Generate topics to populate the active forum block.
    $this->generateForumTopics($this->forum);

    // Login an unprivileged user to view the forum topics and generate an
    // active forum topics list.
    $this->drupalLogin($this->web_user);
    // Verify that this user is shown a message that they may not post content.
    $this->drupalGet('forum/' . $this->forum['tid']);
    $this->assertText(t('You are not allowed to post new content in the forum'), "Authenticated user without permission to post forum content is shown message in local tasks to that effect.");

    $this->viewForumTopics($this->nids);

    // Log in, and do basic tests for a user with permission to edit any forum
    // content.
    $this->doBasicTests($this->edit_any_topics_user, TRUE);
    // Create a forum node authored by this user.
    $any_topics_user_node = $this->createForumTopic($this->forum, FALSE);

    // Log in, and do basic tests for a user with permission to edit only its
    // own forum content.
    $this->doBasicTests($this->edit_own_topics_user, FALSE);
    // Create a forum node authored by this user.
    $own_topics_user_node = $this->createForumTopic($this->forum, FALSE);
    // Verify that this user cannot edit forum content authored by another user.
    $this->verifyForums($this->edit_any_topics_user, $any_topics_user_node, FALSE, 403);

    // Verify that this user is shown a local task to add new forum content.
    $this->drupalGet('forum');
    $this->assertLink(t('Add new Forum topic'));
    $this->drupalGet('forum/' . $this->forum['tid']);
    $this->assertLink(t('Add new Forum topic'));

    // Login a user with permission to edit any forum content.
    $this->drupalLogin($this->edit_any_topics_user);
    // Verify that this user can edit forum content authored by another user.
    $this->verifyForums($this->edit_own_topics_user, $own_topics_user_node, TRUE);

    // Verify the topic and post counts on the forum page.
    $this->drupalGet('forum');

    // Verify row for testing forum.
    $forum_arg = array(':forum' => 'forum-list-' . $this->forum['tid']);

    // Topics cell contains number of topics and number of unread topics.
    $xpath = $this->buildXPathQuery('//tr[@id=:forum]//td[@class="topics"]', $forum_arg);
    $topics = $this->xpath($xpath);
    $topics = trim($topics[0]);
    $this->assertEqual($topics, '6', 'Number of topics found.');

    // Verify the number of unread topics.
    $unread_topics = _forum_topics_unread($this->forum['tid'], $this->edit_any_topics_user->uid);
    $unread_topics = format_plural($unread_topics, '1 new', '@count new');
    $xpath = $this->buildXPathQuery('//tr[@id=:forum]//td[@class="topics"]//a', $forum_arg);
    $this->assertFieldByXPath($xpath, $unread_topics, 'Number of unread topics found.');

    // Verify total number of posts in forum.
    $xpath = $this->buildXPathQuery('//tr[@id=:forum]//td[@class="posts"]', $forum_arg);
    $this->assertFieldByXPath($xpath, '6', 'Number of posts found.');

    // Test loading multiple forum nodes on the front page.
    $this->drupalLogin($this->drupalCreateUser(array('administer content types', 'create forum content')));
    $this->drupalPost('admin/structure/types/manage/forum', array('node_options[promote]' => 'promote'), t('Save content type'));
    $this->createForumTopic($this->forum, FALSE);
    $this->createForumTopic($this->forum, FALSE);
    $this->drupalGet('node');

    // Test adding a comment to a forum topic.
    $node = $this->createForumTopic($this->forum, FALSE);
    $edit = array();
    $edit['comment_body[' . LANGUAGE_NONE . '][0][value]'] = $this->randomName();
    $this->drupalPost("node/$node->nid", $edit, t('Save'));
    $this->assertResponse(200);

    // Test editing a forum topic that has a comment.
    $this->drupalLogin($this->edit_any_topics_user);
    $this->drupalGet('forum/' . $this->forum['tid']);
    $this->drupalPost("node/$node->nid/edit", array(), t('Save'));
    $this->assertResponse(200);

    // Make sure constructing a forum node programmatically produces no notices.
    $node = new stdClass;
    $node->type = 'forum';
    $node->title = 'Test forum notices';
    $node->uid = 1;
    $node->taxonomy_forums[LANGUAGE_NONE][0]['tid'] = $this->root_forum['tid'];
    node_save($node);
  }

  /**
   * Tests that forum nodes can't be added without a parent.
   *
   * Verifies that forum nodes are not created without choosing "forum" from the
   * select list.
   */
  function testAddOrphanTopic() {
    // Must remove forum topics to test creating orphan topics.
    $vid = variable_get('forum_nav_vocabulary');
    $tree = taxonomy_get_tree($vid);
    foreach ($tree as $term) {
      taxonomy_term_delete($term->tid);
    }

    // Create an orphan forum item.
    $this->drupalLogin($this->admin_user);
    $this->drupalPost('node/add/forum', array('title' => $this->randomName(10), 'body[' . LANGUAGE_NONE .'][0][value]' => $this->randomName(120)), t('Save'));

    $nid_count = db_query('SELECT COUNT(nid) FROM {node}')->fetchField();
    $this->assertEqual(0, $nid_count, 'A forum node was not created when missing a forum vocabulary.');

    // Reset the defaults for future tests.
    module_enable(array('forum'));
  }

  /**
   * Runs admin tests on the admin user.
   *
   * @param object $user
   *   The logged in user.
   */
  private function doAdminTests($user) {
    // Login the user.
    $this->drupalLogin($user);

    // Enable the active forum block.
    $edit = array();
    $edit['blocks[forum_active][region]'] = 'sidebar_second';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->assertResponse(200);
    $this->assertText(t('The block settings have been updated.'), 'Active forum topics forum block was enabled');

    // Enable the new forum block.
    $edit = array();
    $edit['blocks[forum_new][region]'] = 'sidebar_second';
    $this->drupalPost('admin/structure/block', $edit, t('Save blocks'));
    $this->assertResponse(200);
    $this->assertText(t('The block settings have been updated.'), '[New forum topics] Forum block was enabled');

    // Retrieve forum menu id.
    $mlid = db_query_range("SELECT mlid FROM {menu_links} WHERE link_path = 'forum' AND menu_name = 'navigation' AND module = 'system' ORDER BY mlid ASC", 0, 1)->fetchField();

    // Add forum to navigation menu.
    $edit = array();
    $this->drupalPost('admin/structure/menu/manage/navigation', $edit, t('Save configuration'));
    $this->assertResponse(200);

    // Edit forum taxonomy.
    // Restoration of the settings fails and causes subsequent tests to fail.
    $this->container = $this->editForumTaxonomy();
    // Create forum container.
    $this->container = $this->createForum('container');
    // Verify "edit container" link exists and functions correctly.
    $this->drupalGet('admin/structure/forum');
    $this->clickLink('edit container');
    $this->assertRaw('Edit container', 'Followed the link to edit the container');
    // Create forum inside the forum container.
    $this->forum = $this->createForum('forum', $this->container['tid']);
    // Verify the "edit forum" link exists and functions correctly.
    $this->drupalGet('admin/structure/forum');
    $this->clickLink('edit forum');
    $this->assertRaw('Edit forum', 'Followed the link to edit the forum');
    // Navigate back to forum structure page.
    $this->drupalGet('admin/structure/forum');
    // Create second forum in container.
    $this->delete_forum = $this->createForum('forum', $this->container['tid']);
    // Save forum overview.
    $this->drupalPost('admin/structure/forum/', array(), t('Save'));
    $this->assertRaw(t('The configuration options have been saved.'));
    // Delete this second forum.
    $this->deleteForum($this->delete_forum['tid']);
    // Create forum at the top (root) level.
    $this->root_forum = $this->createForum('forum');

    // Test vocabulary form alterations.
    $this->drupalGet('admin/structure/taxonomy/forums/edit');
    $this->assertFieldByName('op', t('Save'), 'Save button found.');
    $this->assertNoFieldByName('op', t('Delete'), 'Delete button not found.');

    // Test term edit form alterations.
    $this->drupalGet('taxonomy/term/' . $this->container['tid'] . '/edit');
    // Test parent field been hidden by forum module.
    $this->assertNoField('parent[]', 'Parent field not found.');

    // Test tags vocabulary form is not affected.
    $this->drupalGet('admin/structure/taxonomy/tags/edit');
    $this->assertFieldByName('op', t('Save'), 'Save button found.');
    $this->assertFieldByName('op', t('Delete'), 'Delete button found.');
    // Test tags vocabulary term form is not affected.
    $this->drupalGet('admin/structure/taxonomy/tags/add');
    $this->assertField('parent[]', 'Parent field found.');
    // Test relations fieldset exists.
    $relations_fieldset = $this->xpath("//fieldset[@id='edit-relations']");
    $this->assertTrue(isset($relations_fieldset[0]), 'Relations fieldset element found.');
  }

  /**
   * Edits the forum taxonomy.
   */
  function editForumTaxonomy() {
    // Backup forum taxonomy.
    $vid = variable_get('forum_nav_vocabulary', '');
    $original_settings = taxonomy_vocabulary_load($vid);

    // Generate a random name/description.
    $title = $this->randomName(10);
    $description = $this->randomName(100);

    $edit = array(
      'name' => $title,
      'description' => $description,
      'machine_name' => drupal_strtolower(drupal_substr($this->randomName(), 3, 9)),
    );

    // Edit the vocabulary.
    $this->drupalPost('admin/structure/taxonomy/' . $original_settings->machine_name . '/edit', $edit, t('Save'));
    $this->assertResponse(200);
    $this->assertRaw(t('Updated vocabulary %name.', array('%name' => $title)), 'Vocabulary was edited');

    // Grab the newly edited vocabulary.
    entity_get_controller('taxonomy_vocabulary')->resetCache();
    $current_settings = taxonomy_vocabulary_load($vid);

    // Make sure we actually edited the vocabulary properly.
    $this->assertEqual($current_settings->name, $title, 'The name was updated');
    $this->assertEqual($current_settings->description, $description, 'The description was updated');

    // Restore the original vocabulary.
    taxonomy_vocabulary_save($original_settings);
    drupal_static_reset('taxonomy_vocabulary_load');
    $current_settings = taxonomy_vocabulary_load($vid);
    $this->assertEqual($current_settings->name, $original_settings->name, 'The original vocabulary settings were restored');
  }

  /**
   * Creates a forum container or a forum.
   *
   * @param $type
   *   The forum type (forum container or forum).
   * @param $parent
   *   The forum parent. This defaults to 0, indicating a root forum.
   *   another forum).
   *
   * @return
   *   The created taxonomy term data.
   */
  function createForum($type, $parent = 0) {
    // Generate a random name/description.
    $name = $this->randomName(10);
    $description = $this->randomName(100);

    $edit = array(
      'name' => $name,
      'description' => $description,
      'parent[0]' => $parent,
      'weight' => '0',
    );

    // Create forum.
    $this->drupalPost('admin/structure/forum/add/' . $type, $edit, t('Save'));
    $this->assertResponse(200);
    $type = ($type == 'container') ? 'forum container' : 'forum';
    $this->assertRaw(t('Created new @type %term.', array('%term' => $name, '@type' => t($type))), format_string('@type was created', array('@type' => ucfirst($type))));

    // Verify forum.
    $term = db_query("SELECT * FROM {taxonomy_term_data} t WHERE t.vid = :vid AND t.name = :name AND t.description = :desc", array(':vid' => variable_get('forum_nav_vocabulary', ''), ':name' => $name, ':desc' => $description))->fetchAssoc();
    $this->assertTrue(!empty($term), 'The ' . $type . ' exists in the database');

    // Verify forum hierarchy.
    $tid = $term['tid'];
    $parent_tid = db_query("SELECT t.parent FROM {taxonomy_term_hierarchy} t WHERE t.tid = :tid", array(':tid' => $tid))->fetchField();
    $this->assertTrue($parent == $parent_tid, 'The ' . $type . ' is linked to its container');

    return $term;
  }

  /**
   * Deletes a forum.
   *
   * @param $tid
   *   The forum ID.
   */
  function deleteForum($tid) {
    // Delete the forum.
    $this->drupalPost('admin/structure/forum/edit/forum/' . $tid, array(), t('Delete'));
    $this->drupalPost(NULL, array(), t('Delete'));

    // Assert that the forum no longer exists.
    $this->drupalGet('forum/' . $tid);
    $this->assertResponse(404, 'The forum was not found');

    // Assert that the associated term has been removed from the
    // forum_containers variable.
    $containers = variable_get('forum_containers', array());
    $this->assertFalse(in_array($tid, $containers), 'The forum_containers variable has been updated.');
  }

  /**
   * Runs basic tests on the indicated user.
   *
   * @param $user
   *   The logged in user.
   * @param $admin
   *   User has 'access administration pages' privilege.
   */
  private function doBasicTests($user, $admin) {
    // Login the user.
    $this->drupalLogin($user);
    // Attempt to create forum topic under a container.
    $this->createForumTopic($this->container, TRUE);
    // Create forum node.
    $node = $this->createForumTopic($this->forum, FALSE);
    // Verify the user has access to all the forum nodes.
    $this->verifyForums($user, $node, $admin);
  }

  /**
   * Creates forum topic.
   *
   * @param array $forum
   *   A forum array.
   * @param boolean $container
   *   TRUE if $forum is a container; FALSE otherwise.
   *
   * @return object
   *   The created topic node.
   */
  function createForumTopic($forum, $container = FALSE) {
    // Generate a random subject/body.
    $title = $this->randomName(20);
    $body = $this->randomName(200);

    $langcode = LANGUAGE_NONE;
    $edit = array(
      "title" => $title,
      "body[$langcode][0][value]" => $body,
    );
    $tid = $forum['tid'];

    // Create the forum topic, preselecting the forum ID via a URL parameter.
    $this->drupalPost('node/add/forum/' . $tid, $edit, t('Save'));

    $type = t('Forum topic');
    if ($container) {
      $this->assertNoRaw(t('@type %title has been created.', array('@type' => $type, '%title' => $title)), 'Forum topic was not created');
      $this->assertRaw(t('The item %title is a forum container, not a forum.', array('%title' => $forum['name'])), 'Error message was shown');
      return;
    }
    else {
      $this->assertRaw(t('@type %title has been created.', array('@type' => $type, '%title' => $title)), 'Forum topic was created');
      $this->assertNoRaw(t('The item %title is a forum container, not a forum.', array('%title' => $forum['name'])), 'No error message was shown');
    }

    // Retrieve node object, ensure that the topic was created and in the proper forum.
    $node = $this->drupalGetNodeByTitle($title);
    $this->assertTrue($node != NULL, format_string('Node @title was loaded', array('@title' => $title)));
    $this->assertEqual($node->taxonomy_forums[LANGUAGE_NONE][0]['tid'], $tid, 'Saved forum topic was in the expected forum');

    // View forum topic.
    $this->drupalGet('node/' . $node->nid);
    $this->assertRaw($title, 'Subject was found');
    $this->assertRaw($body, 'Body was found');

    return $node;
  }

  /**
   * Verifies that the logged in user has access to a forum nodes.
   *
   * @param $node_user
   *   The user who creates the node.
   * @param $node
   *   The node being checked.
   * @param $admin
   *   Boolean to indicate whether the user can 'access administration pages'.
   * @param $response
   *   The exptected HTTP response code.
   */
  private function verifyForums($node_user, $node, $admin, $response = 200) {
    $response2 = ($admin) ? 200 : 403;

    // View forum help node.
    $this->drupalGet('admin/help/forum');
    $this->assertResponse($response2);
    if ($response2 == 200) {
      $this->assertTitle(t('Forum | Drupal'), 'Forum help title was displayed');
      $this->assertText(t('Forum'), 'Forum help node was displayed');
    }

    // Verify the forum blocks were displayed.
    $this->drupalGet('');
    $this->assertResponse(200);
    $this->assertText(t('New forum topics'), '[New forum topics] Forum block was displayed');

    // View forum container page.
    $this->verifyForumView($this->container);
    // View forum page.
    $this->verifyForumView($this->forum, $this->container);
    // View root forum page.
    $this->verifyForumView($this->root_forum);

    // View forum node.
    $this->drupalGet('node/' . $node->nid);
    $this->assertResponse(200);
    $this->assertTitle($node->title . ' | Drupal', 'Forum node was displayed');
    $breadcrumb = array(
      l(t('Home'), NULL),
      l(t('Forums'), 'forum'),
      l($this->container['name'], 'forum/' . $this->container['tid']),
      l($this->forum['name'], 'forum/' . $this->forum['tid']),
    );
    $this->assertRaw(theme('breadcrumb', array('breadcrumb' => $breadcrumb)), 'Breadcrumbs were displayed');

    // View forum edit node.
    $this->drupalGet('node/' . $node->nid . '/edit');
    $this->assertResponse($response);
    if ($response == 200) {
      $this->assertTitle('Edit Forum topic ' . $node->title . ' | Drupal', 'Forum edit node was displayed');
    }

    if ($response == 200) {
      // Edit forum node (including moving it to another forum).
      $edit = array();
      $langcode = LANGUAGE_NONE;
      $edit["title"] = 'node/' . $node->nid;
      $edit["body[$langcode][0][value]"] = $this->randomName(256);
      // Assume the topic is initially associated with $forum.
      $edit["taxonomy_forums[$langcode]"] = $this->root_forum['tid'];
      $edit['shadow'] = TRUE;
      $this->drupalPost('node/' . $node->nid . '/edit', $edit, t('Save'));
      $this->assertRaw(t('Forum topic %title has been updated.', array('%title' => $edit["title"])), 'Forum node was edited');

      // Verify topic was moved to a different forum.
      $forum_tid = db_query("SELECT tid FROM {forum} WHERE nid = :nid AND vid = :vid", array(
        ':nid' => $node->nid,
        ':vid' => $node->vid,
      ))->fetchField();
      $this->assertTrue($forum_tid == $this->root_forum['tid'], 'The forum topic is linked to a different forum');

      // Delete forum node.
      $this->drupalPost('node/' . $node->nid . '/delete', array(), t('Delete'));
      $this->assertResponse($response);
      $this->assertRaw(t('Forum topic %title has been deleted.', array('%title' => $edit['title'])), 'Forum node was deleted');
    }
  }

  /**
   * Verifies display of forum page.
   *
   * @param $forum
   *   A row from the taxonomy_term_data table in an array.
   * @param $parent
   *   (optional) An array representing the forum's parent.
   */
  private function verifyForumView($forum, $parent = NULL) {
    // View forum page.
    $this->drupalGet('forum/' . $forum['tid']);
    $this->assertResponse(200);
    $this->assertTitle($forum['name'] . ' | Drupal', 'Forum name was displayed');

    $breadcrumb = array(
      l(t('Home'), NULL),
      l(t('Forums'), 'forum'),
    );
    if (isset($parent)) {
      $breadcrumb[] = l($parent['name'], 'forum/' . $parent['tid']);
    }

    $this->assertRaw(theme('breadcrumb', array('breadcrumb' => $breadcrumb)), 'Breadcrumbs were displayed');
  }

  /**
   * Generates forum topics to test the display of an active forum block.
   *
   * @param array $forum
   *   The foorum array (a row from taxonomy_term_data table).
   */
  private function generateForumTopics($forum) {
    $this->nids = array();
    for ($i = 0; $i < 5; $i++) {
      $node = $this->createForumTopic($this->forum, FALSE);
      $this->nids[] = $node->nid;
    }
  }

  /**
   * Views forum topics to test the display of an active forum block.
   *
   * @todo The logic here is completely incorrect, since the active forum topics
   *   block is determined by comments on the node, not by views.
   * @todo DIE
   *
   * @param $nids
   *   An array of forum node IDs.
   */
  private function viewForumTopics($nids) {
    for ($i = 0; $i < 2; $i++) {
      foreach ($nids as $nid) {
        $this->drupalGet('node/' . $nid);
        $this->drupalGet('node/' . $nid);
        $this->drupalGet('node/' . $nid);
      }
    }
  }
}

/**
 * Tests the forum index listing.
 */
class ForumIndexTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'Forum index',
      'description' => 'Tests the forum index listing.',
      'group' => 'Forum',
    );
  }

  function setUp() {
    parent::setUp('taxonomy', 'comment', 'forum');

    // Create a test user.
    $web_user = $this->drupalCreateUser(array('create forum content', 'edit own forum content', 'edit any forum content', 'administer nodes'));
    $this->drupalLogin($web_user);
  }

  /**
   * Tests the forum index for published and unpublished nodes.
   */
  function testForumIndexStatus() {

    $langcode = LANGUAGE_NONE;

    // The forum ID to use.
    $tid = 1;

    // Create a test node.
    $title = $this->randomName(20);
    $edit = array(
      "title" => $title,
      "body[$langcode][0][value]" => $this->randomName(200),
    );

    // Create the forum topic, preselecting the forum ID via a URL parameter.
    $this->drupalPost('node/add/forum/' . $tid, $edit, t('Save'));

    // Check that the node exists in the database.
    $node = $this->drupalGetNodeByTitle($title);
    $this->assertTrue(!empty($node), 'New forum node found in database.');

    // Verify that the node appears on the index.
    $this->drupalGet('forum/' . $tid);
    $this->assertText($title, 'Published forum topic appears on index.');

    // Unpublish the node.
    $edit = array(
      'status' => FALSE,
    );
    $this->drupalPost("node/{$node->nid}/edit", $edit, t('Save'));
    $this->drupalGet("node/{$node->nid}");
    $this->assertText(t('Access denied'), 'Unpublished node is no longer accessible.');

    // Verify that the node no longer appears on the index.
    $this->drupalGet('forum/' . $tid);
    $this->assertNoText($title, 'Unpublished forum topic no longer appears on index.');
  }
}

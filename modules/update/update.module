<?php

/**
 * @file
 * Handles updates of <PERSON><PERSON><PERSON> core and contributed projects.
 *
 * The module checks for available updates of <PERSON><PERSON><PERSON> core and any installed
 * contributed modules and themes. It warns site administrators if newer
 * releases are available via the system status report (admin/reports/status),
 * the module and theme pages, and optionally via e-mail. It also provides the
 * ability to install contributed modules and themes via an user interface.
 */

/**
 * URL to check for updates, if a given project doesn't define its own.
 */
define('UPDATE_DEFAULT_URL', 'http://updates.drupal.org/release-history');

// These are internally used constants for this code, do not modify.

/**
 * Project is missing security update(s).
 */
define('UPDATE_NOT_SECURE', 1);

/**
 * Current release has been unpublished and is no longer available.
 */
define('UPDATE_REVOKED', 2);

/**
 * Current release is no longer supported by the project maintainer.
 */
define('UPDATE_NOT_SUPPORTED', 3);

/**
 * Project has a new release available, but it is not a security release.
 */
define('UPDATE_NOT_CURRENT', 4);

/**
 * Project is up to date.
 */
define('UPDATE_CURRENT', 5);

/**
 * Project's status cannot be checked.
 */
define('UPDATE_NOT_CHECKED', -1);

/**
 * No available update data was found for project.
 */
define('UPDATE_UNKNOWN', -2);

/**
 * There was a failure fetching available update data for this project.
 */
define('UPDATE_NOT_FETCHED', -3);

/**
 * We need to (re)fetch available update data for this project.
 */
define('UPDATE_FETCH_PENDING', -4);

/**
 * Maximum number of attempts to fetch available update data from a given host.
 */
define('UPDATE_MAX_FETCH_ATTEMPTS', 2);

/**
 * Maximum number of seconds to try fetching available update data at a time.
 */
define('UPDATE_MAX_FETCH_TIME', 30);

/**
 * Implements hook_help().
 */
function update_help($path, $arg) {
  switch ($path) {
    case 'admin/reports/updates':
      return '<p>' . t('Here you can find information about available updates for your installed modules and themes. Note that each module or theme is part of a "project", which may or may not have the same name, and might include multiple modules or themes within it.') . '</p>';

    case 'admin/help#update':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t("The Update manager module periodically checks for new versions of your site's software (including contributed modules and themes), and alerts administrators to available updates. In order to provide update information, anonymous usage statistics are sent to Drupal.org. If desired, you may disable the Update manager module from the <a href='@modules'>Module administration page</a>. For more information, see the online handbook entry for <a href='@update'>Update manager module</a>.", array('@update' => 'http://drupal.org/documentation/modules/update', '@modules' => url('admin/modules'))) . '</p>';
      // Only explain the Update manager if it has not been disabled.
      if (update_manager_access()) {
        $output .= '<p>' . t('The Update manager also allows administrators to update and install modules and themes through the administration interface.') . '</p>';
      }
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Checking for available updates') . '</dt>';
      $output .= '<dd>' . t('A report of <a href="@update-report">available updates</a> will alert you when new releases are available for download. You may configure options for the frequency for checking updates (which are performed during <a href="@cron">cron</a> runs) and e-mail notifications at the <a href="@update-settings">Update manager settings</a>  page.', array('@update-report' => url('admin/reports/updates'), '@cron' => 'http://drupal.org/cron', '@update-settings' => url('admin/reports/updates/settings'))) . '</dd>';
      // Only explain the Update manager if it has not been disabled.
      if (update_manager_access()) {
        $output .= '<dt>' . t('Performing updates through the user interface') . '</dt>';
        $output .= '<dd>' . t('The Update manager module allows administrators to perform updates directly through the administration interface. At the top of the <a href="@modules_page">modules</a> and <a href="@themes_page">themes</a> pages you will see a link to update to new releases. This will direct you to the <a href="@update-page">update page</a> where you see a listing of all the missing updates and confirm which ones you want to upgrade. From there, you are prompted for your FTP/SSH password, which then transfers the files into your Drupal installation, overwriting your old files. More detailed instructions can be found in the <a href="@update">online handbook</a>.', array('@modules_page' => url('admin/modules'), '@themes_page' => url('admin/appearance'),  '@update-page' => url('admin/reports/updates/update'), '@update' => 'http://drupal.org/documentation/modules/update')) . '</dd>';
        $output .= '<dt>' . t('Installing new modules and themes through the user interface') . '</dt>';
        $output .= '<dd>' . t('You can also install new modules and themes in the same fashion, through the <a href="@install">install page</a>, or by clicking the <em>Install new module/theme</em> link at the top of the <a href="@modules_page">modules</a> and <a href="@themes_page">themes</a> pages. In this case, you are prompted to provide either the URL to the download, or to upload a packaged release file from your local computer.', array('@modules_page' => url('admin/modules'), '@themes_page' => url('admin/appearance'), '@install' => url('admin/reports/updates/install'))) . '</dd>';
      }
      $output .= '</dl>';
      return $output;
  }
}

/**
 * Implements hook_init().
 */
function update_init() {
  if (arg(0) == 'admin' && user_access('administer site configuration')) {
    switch ($_GET['q']) {
      // These pages don't need additional nagging.
      case 'admin/appearance/update':
      case 'admin/appearance/install':
      case 'admin/modules/update':
      case 'admin/modules/install':
      case 'admin/reports/updates':
      case 'admin/reports/updates/update':
      case 'admin/reports/updates/install':
      case 'admin/reports/updates/settings':
      case 'admin/reports/status':
      case 'admin/update/ready':
        return;

      // If we are on the appearance or modules list, display a detailed report
      // of the update status.
      case 'admin/appearance':
      case 'admin/modules':
        $verbose = TRUE;
        break;

    }
    module_load_install('update');
    $status = update_requirements('runtime');
    foreach (array('core', 'contrib') as $report_type) {
      $type = 'update_' . $report_type;
      if (!empty($verbose)) {
        if (isset($status[$type]['severity'])) {
          if ($status[$type]['severity'] == REQUIREMENT_ERROR) {
            drupal_set_message($status[$type]['description'], 'error', FALSE);
          }
          elseif ($status[$type]['severity'] == REQUIREMENT_WARNING) {
            drupal_set_message($status[$type]['description'], 'warning', FALSE);
          }
        }
      }
      // Otherwise, if we're on *any* admin page and there's a security
      // update missing, print an error message about it.
      else {
        if (isset($status[$type])
            && isset($status[$type]['reason'])
            && $status[$type]['reason'] === UPDATE_NOT_SECURE) {
          drupal_set_message($status[$type]['description'], 'error', FALSE);
        }
      }
    }
  }
}

/**
 * Implements hook_menu().
 */
function update_menu() {
  $items = array();

  $items['admin/reports/updates'] = array(
    'title' => 'Available updates',
    'description' => 'Get a status report about available updates for your installed modules and themes.',
    'page callback' => 'update_status',
    'access arguments' => array('administer site configuration'),
    'weight' => -50,
    'file' => 'update.report.inc',
  );
  $items['admin/reports/updates/list'] = array(
    'title' => 'List',
    'access arguments' => array('administer site configuration'),
    'type' => MENU_DEFAULT_LOCAL_TASK,
  );
  $items['admin/reports/updates/settings'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('update_settings'),
    'access arguments' => array('administer site configuration'),
    'file' => 'update.settings.inc',
    'type' => MENU_LOCAL_TASK,
    'weight' => 50,
  );
  $items['admin/reports/updates/check'] = array(
    'title' => 'Manual update check',
    'page callback' => 'update_manual_status',
    'access arguments' => array('administer site configuration'),
    'type' => MENU_CALLBACK,
    'file' => 'update.fetch.inc',
  );

  // We want action links for updating projects at a few different locations:
  // both the module and theme administration pages, and on the available
  // updates report itself. The menu items will be mostly identical, except the
  // paths and titles, so we just define them in a loop. We pass in a string
  // indicating what context we're entering the action from, so that can
  // customize the appearance as needed.
  $paths = array(
    'report' => 'admin/reports/updates',
    'module' => 'admin/modules',
    'theme' => 'admin/appearance',
  );
  foreach ($paths as $context => $path) {
    $items[$path . '/install'] = array(
      'page callback' => 'drupal_get_form',
      'page arguments' => array('update_manager_install_form', $context),
      'access callback' => 'update_manager_access',
      'access arguments' => array(),
      'weight' => 25,
      'type' => MENU_LOCAL_ACTION,
      'file' => 'update.manager.inc',
    );
    $items[$path . '/update'] = array(
      'page callback' => 'drupal_get_form',
      'page arguments' => array('update_manager_update_form', $context),
      'access callback' => 'update_manager_access',
      'access arguments' => array(),
      'weight' => 10,
      'title' => 'Update',
      'type' => MENU_LOCAL_TASK,
      'file' => 'update.manager.inc',
    );
  }
  // Customize the titles of the action links depending on where they appear.
  // We use += array() to let the translation extractor find these menu titles.
  $items['admin/reports/updates/install'] += array('title' => 'Install new module or theme');
  $items['admin/modules/install'] += array('title' => 'Install new module');
  $items['admin/appearance/install'] += array('title' => 'Install new theme');

  // Menu callback used for the confirmation page after all the releases
  // have been downloaded, asking you to backup before installing updates.
  $items['admin/update/ready'] = array(
    'title' => 'Ready to update',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('update_manager_update_ready_form'),
    'access callback' => 'update_manager_access',
    'access arguments' => array(),
    'type' => MENU_CALLBACK,
    'file' => 'update.manager.inc',
  );

  return $items;
}

/**
 * Access callback: Resolves if the current user can access updater menu items.
 *
 * It both enforces the 'administer software updates' permission and the global
 * kill switch for the authorize.php script.
 *
 * @return
 *   TRUE if the current user can access the updater menu items; FALSE
 *   otherwise.
 *
 * @see update_menu()
 */
function update_manager_access() {
  return variable_get('allow_authorize_operations', TRUE) && user_access('administer software updates');
}

/**
 * Implements hook_theme().
 */
function update_theme() {
  return array(
    'update_manager_update_form' => array(
      'render element' => 'form',
      'file' => 'update.manager.inc',
    ),
    'update_last_check' => array(
      'variables' => array('last' => NULL),
    ),
    'update_report' => array(
      'variables' => array('data' => NULL),
      'file' => 'update.report.inc',
    ),
    'update_version' => array(
      'variables' => array('version' => NULL, 'tag' => NULL, 'class' => array()),
      'file' => 'update.report.inc',
    ),
    'update_status_label' => array(
      'variables' => array('status' => NULL),
      'file' => 'update.report.inc',
    ),
  );
}

/**
 * Implements hook_cron().
 */
function update_cron() {
  $frequency = variable_get('update_check_frequency', 1);
  $interval = 60 * 60 * 24 * $frequency;
  if ((REQUEST_TIME - variable_get('update_last_check', 0)) > $interval) {
    // If the configured update interval has elapsed, we want to invalidate
    // the cached data for all projects, attempt to re-fetch, and trigger any
    // configured notifications about the new status.
    update_refresh();
    update_fetch_data();
  }
  else {
    // Otherwise, see if any individual projects are now stale or still
    // missing data, and if so, try to fetch the data.
    update_get_available(TRUE);
  }
  if ((REQUEST_TIME - variable_get('update_last_email_notification', 0)) > $interval) {
    // If configured time between notifications elapsed, send email about
    // updates possibly available.
    module_load_include('inc', 'update', 'update.fetch');
    _update_cron_notify();
  }

  // Clear garbage from disk.
  update_clear_update_disk_cache();
}

/**
 * Implements hook_themes_enabled().
 *
 * If themes are enabled, we invalidate the cache of available updates.
 */
function update_themes_enabled($themes) {
  // Clear all update module caches.
  _update_cache_clear();
}

/**
 * Implements hook_themes_disabled().
 *
 * If themes are disabled, we invalidate the cache of available updates.
 */
function update_themes_disabled($themes) {
  // Clear all update module caches.
  _update_cache_clear();
}

/**
 * Implements hook_form_FORM_ID_alter() for system_modules().
 *
 * Adds a form submission handler to the system modules form, so that if a site
 * admin saves the form, we invalidate the cache of available updates.
 *
 * @see _update_cache_clear()
 */
function update_form_system_modules_alter(&$form, $form_state) {
  $form['#submit'][] = 'update_cache_clear_submit';
}

/**
 * Form submission handler for system_modules().
 *
 * @see update_form_system_modules_alter()
 */
function update_cache_clear_submit($form, &$form_state) {
  // Clear all update module caches.
  _update_cache_clear();
}

/**
 * Returns a warning message when there is no data about available updates.
 */
function _update_no_data() {
  $destination = drupal_get_destination();
  $cron_token = array('token' => drupal_get_token('run-cron'));
  return t('No update information available. <a href="@run_cron">Run cron</a> or <a href="@check_manually">check manually</a>.', array(
    '@run_cron' => url('admin/reports/status/run-cron', array('query' => $cron_token + $destination)),
    '@check_manually' => url('admin/reports/updates/check', array('query' => $destination)),
  ));
}

/**
 * Tries to get update information from cache and refreshes it when necessary.
 *
 * In addition to checking the cache lifetime, this function also ensures that
 * there are no .info files for enabled modules or themes that have a newer
 * modification timestamp than the last time we checked for available update
 * data. If any .info file was modified, it almost certainly means a new version
 * of something was installed. Without fresh available update data, the logic in
 * update_calculate_project_data() will be wrong and produce confusing, bogus
 * results.
 *
 * @param $refresh
 *   (optional) Boolean to indicate if this method should refresh the cache
 *   automatically if there's no data. Defaults to FALSE.
 *
 * @return
 *   Array of data about available releases, keyed by project shortname.
 *
 * @see update_refresh()
 * @see update_get_projects()
 */
function update_get_available($refresh = FALSE) {
  module_load_include('inc', 'update', 'update.compare');
  $needs_refresh = FALSE;

  // Grab whatever data we currently have cached in the DB.
  $available = _update_get_cached_available_releases();
  $num_avail = count($available);

  $projects = update_get_projects();
  foreach ($projects as $key => $project) {
    // If there's no data at all, we clearly need to fetch some.
    if (empty($available[$key])) {
      update_create_fetch_task($project);
      $needs_refresh = TRUE;
      continue;
    }

    // See if the .info file is newer than the last time we checked for data,
    // and if so, mark this project's data as needing to be re-fetched. Any
    // time an admin upgrades their local installation, the .info file will
    // be changed, so this is the only way we can be sure we're not showing
    // bogus information right after they upgrade.
    if ($project['info']['_info_file_ctime'] > $available[$key]['last_fetch']) {
      $available[$key]['fetch_status'] = UPDATE_FETCH_PENDING;
    }

    // If we have project data but no release data, we need to fetch. This
    // can be triggered when we fail to contact a release history server.
    if (empty($available[$key]['releases'])) {
      $available[$key]['fetch_status'] = UPDATE_FETCH_PENDING;
    }

    // If we think this project needs to fetch, actually create the task now
    // and remember that we think we're missing some data.
    if (!empty($available[$key]['fetch_status']) && $available[$key]['fetch_status'] == UPDATE_FETCH_PENDING) {
      update_create_fetch_task($project);
      $needs_refresh = TRUE;
    }
  }

  if ($needs_refresh && $refresh) {
    // Attempt to drain the queue of fetch tasks.
    update_fetch_data();
    // After processing the queue, we've (hopefully) got better data, so pull
    // the latest from the cache again and use that directly.
    $available = _update_get_cached_available_releases();
  }

  return $available;
}

/**
 * Creates a new fetch task after loading the necessary include file.
 *
 * @param $project
 *   Associative array of information about a project. See update_get_projects()
 *   for the keys used.
 *
 * @see _update_create_fetch_task()
 */
function update_create_fetch_task($project) {
  module_load_include('inc', 'update', 'update.fetch');
  return _update_create_fetch_task($project);
}

/**
 * Refreshes the release data after loading the necessary include file.
 *
 * @see _update_refresh()
 */
function update_refresh() {
  module_load_include('inc', 'update', 'update.fetch');
  return _update_refresh();
}

/**
 * Attempts to fetch update data after loading the necessary include file.
 *
 * @see _update_fetch_data()
 */
function update_fetch_data() {
  module_load_include('inc', 'update', 'update.fetch');
  return _update_fetch_data();
}

/**
 * Returns all currently cached data about available releases for all projects.
 *
 * @return
 *   Array of data about available releases, keyed by project shortname.
 */
function _update_get_cached_available_releases() {
  $data = array();
  $cache_items = _update_get_cache_multiple('available_releases');
  foreach ($cache_items as $cid => $cache) {
    $cache->data['last_fetch'] = $cache->created;
    if ($cache->expire < REQUEST_TIME) {
      $cache->data['fetch_status'] = UPDATE_FETCH_PENDING;
    }
    // The project shortname is embedded in the cache ID, even if there's no
    // data for this project in the DB at all, so use that for the indexes in
    // the array.
    $parts = explode('::', $cid, 2);
    $data[$parts[1]] = $cache->data;
  }
  return $data;
}

/**
 * Implements hook_mail().
 *
 * Constructs the e-mail notification message when the site is out of date.
 *
 * @param $key
 *   Unique key to indicate what message to build, always 'status_notify'.
 * @param $message
 *   Reference to the message array being built.
 * @param $params
 *   Array of parameters to indicate what kind of text to include in the message
 *   body. This is a keyed array of message type ('core' or 'contrib') as the
 *   keys, and the status reason constant (UPDATE_NOT_SECURE, etc) for the
 *   values.
 *
 * @see drupal_mail()
 * @see _update_cron_notify()
 * @see _update_message_text()
 */
function update_mail($key, &$message, $params) {
  $language = $message['language'];
  $langcode = $language->language;
  $message['subject'] .= t('New release(s) available for !site_name', array('!site_name' => variable_get('site_name', 'Drupal')), array('langcode' => $langcode));
  foreach ($params as $msg_type => $msg_reason) {
    $message['body'][] = _update_message_text($msg_type, $msg_reason, FALSE, $language);
  }
  $message['body'][] = t('See the available updates page for more information:', array(), array('langcode' => $langcode)) . "\n" . url('admin/reports/updates', array('absolute' => TRUE, 'language' => $language));
  if (update_manager_access()) {
    $message['body'][] = t('You can automatically install your missing updates using the Update manager:', array(), array('langcode' => $langcode)) . "\n" . url('admin/reports/updates/update', array('absolute' => TRUE, 'language' => $language));
  }
  $settings_url = url('admin/reports/updates/settings', array('absolute' => TRUE));
  if (variable_get('update_notification_threshold', 'all') == 'all') {
    $message['body'][] = t('Your site is currently configured to send these emails when any updates are available. To get notified only for security updates, !url.', array('!url' => $settings_url));
  }
  else {
    $message['body'][] = t('Your site is currently configured to send these emails only when security updates are available. To get notified for any available updates, !url.', array('!url' => $settings_url));
  }
}

/**
 * Returns the appropriate message text when site is out of date or not secure.
 *
 * These error messages are shared by both update_requirements() for the
 * site-wide status report at admin/reports/status and in the body of the
 * notification e-mail messages generated by update_cron().
 *
 * @param $msg_type
 *   String to indicate what kind of message to generate. Can be either 'core'
 *   or 'contrib'.
 * @param $msg_reason
 *   Integer constant specifying why message is generated.
 * @param $report_link
 *   (optional) Boolean that controls if a link to the updates report should be
 *   added. Defaults to FALSE.
 * @param $language
 *   (optional) A language object to use. Defaults to NULL.
 *
 * @return
 *   The properly translated error message for the given key.
 */
function _update_message_text($msg_type, $msg_reason, $report_link = FALSE, $language = NULL) {
  $langcode = isset($language) ? $language->language : NULL;
  $text = '';
  switch ($msg_reason) {
    case UPDATE_NOT_SECURE:
      if ($msg_type == 'core') {
        $text = t('There is a security update available for your version of Drupal. To ensure the security of your server, you should update immediately!', array(), array('langcode' => $langcode));
      }
      else {
        $text = t('There are security updates available for one or more of your modules or themes. To ensure the security of your server, you should update immediately!', array(), array('langcode' => $langcode));
      }
      break;

    case UPDATE_REVOKED:
      if ($msg_type == 'core') {
        $text = t('Your version of Drupal has been revoked and is no longer available for download. Upgrading is strongly recommended!', array(), array('langcode' => $langcode));
      }
      else {
        $text = t('The installed version of at least one of your modules or themes has been revoked and is no longer available for download. Upgrading or disabling is strongly recommended!', array(), array('langcode' => $langcode));
      }
      break;

    case UPDATE_NOT_SUPPORTED:
      if ($msg_type == 'core') {
        $text = t('Your version of Drupal is no longer supported. Upgrading is strongly recommended!', array(), array('langcode' => $langcode));
      }
      else {
        $text = t('The installed version of at least one of your modules or themes is no longer supported. Upgrading or disabling is strongly recommended. See the project homepage for more details.', array(), array('langcode' => $langcode));
      }
      break;

    case UPDATE_NOT_CURRENT:
      if ($msg_type == 'core') {
        $text = t('There are updates available for your version of Drupal. To ensure the proper functioning of your site, you should update as soon as possible.', array(), array('langcode' => $langcode));
      }
      else {
        $text = t('There are updates available for one or more of your modules or themes. To ensure the proper functioning of your site, you should update as soon as possible.', array(), array('langcode' => $langcode));
      }
      break;

    case UPDATE_UNKNOWN:
    case UPDATE_NOT_CHECKED:
    case UPDATE_NOT_FETCHED:
    case UPDATE_FETCH_PENDING:
      if ($msg_type == 'core') {
        $text = t('There was a problem checking <a href="@update-report">available updates</a> for Drupal.', array('@update-report' => url('admin/reports/updates')), array('langcode' => $langcode));
      }
      else {
        $text = t('There was a problem checking <a href="@update-report">available updates</a> for your modules or themes.', array('@update-report' => url('admin/reports/updates')), array('langcode' => $langcode));
      }
      break;
  }

  if ($report_link) {
    if (update_manager_access()) {
      $text .= ' ' . t('See the <a href="@available_updates">available updates</a> page for more information and to install your missing updates.', array('@available_updates' => url('admin/reports/updates/update', array('language' => $language))), array('langcode' => $langcode));
    }
    else {
      $text .= ' ' . t('See the <a href="@available_updates">available updates</a> page for more information.', array('@available_updates' => url('admin/reports/updates', array('language' => $language))), array('langcode' => $langcode));
    }
  }

  return $text;
}

/**
 * Orders projects based on their status.
 *
 * Callback for uasort() within update_requirements().
 */
function _update_project_status_sort($a, $b) {
  // The status constants are numerically in the right order, so we can
  // usually subtract the two to compare in the order we want. However,
  // negative status values should be treated as if they are huge, since we
  // always want them at the bottom of the list.
  $a_status = $a['status'] > 0 ? $a['status'] : (-10 * $a['status']);
  $b_status = $b['status'] > 0 ? $b['status'] : (-10 * $b['status']);
  return $a_status - $b_status;
}

/**
 * Returns HTML for the last time we checked for update data.
 *
 * In addition to properly formatting the given timestamp, this function also
 * provides a "Check manually" link that refreshes the available update and
 * redirects back to the same page.
 *
 * @param $variables
 *   An associative array containing:
 *   - last: The timestamp when the site last checked for available updates.
 *
 * @see theme_update_report()
 * @see theme_update_available_updates_form()
 * @ingroup themeable
 */
function theme_update_last_check($variables) {
  $last = $variables['last'];
  $output = '<div class="update checked">';
  $output .= $last ? t('Last checked: @time ago', array('@time' => format_interval(REQUEST_TIME - $last))) : t('Last checked: never');
  $output .= ' <span class="check-manually">(' . l(t('Check manually'), 'admin/reports/updates/check', array('query' => drupal_get_destination())) . ')</span>';
  $output .= "</div>\n";
  return $output;
}

/**
 * Implements hook_verify_update_archive().
 *
 * First, we ensure that the archive isn't a copy of Drupal core, which the
 * update manager does not yet support. See http://drupal.org/node/606592
 *
 * Then, we make sure that at least one module included in the archive file has
 * an .info file which claims that the code is compatible with the current
 * version of Drupal core.
 *
 * @see drupal_system_listing()
 * @see _system_rebuild_module_data()
 */
function update_verify_update_archive($project, $archive_file, $directory) {
  $errors = array();

  // Make sure this isn't a tarball of Drupal core.
  if (
    file_exists("$directory/$project/index.php")
    && file_exists("$directory/$project/update.php")
    && file_exists("$directory/$project/includes/bootstrap.inc")
    && file_exists("$directory/$project/modules/node/node.module")
    && file_exists("$directory/$project/modules/system/system.module")
  ) {
    return array(
      'no-core' => t('Automatic updating of Drupal core is not supported. See the <a href="@upgrade-guide">upgrade guide</a> for information on how to update Drupal core manually.', array('@upgrade-guide' => 'http://drupal.org/upgrade')),
    );
  }

  // Parse all the .info files and make sure at least one is compatible with
  // this version of Drupal core. If one is compatible, then the project as a
  // whole is considered compatible (since, for example, the project may ship
  // with some out-of-date modules that are not necessary for its overall
  // functionality).
  $compatible_project = FALSE;
  $incompatible = array();
  $files = file_scan_directory("$directory/$project", '/^' . DRUPAL_PHP_FUNCTION_PATTERN . '\.info$/', array('key' => 'name', 'min_depth' => 0));
  foreach ($files as $key => $file) {
    // Get the .info file for the module or theme this file belongs to.
    $info = drupal_parse_info_file($file->uri);

    // If the module or theme is incompatible with Drupal core, set an error.
    if (empty($info['core']) || $info['core'] != DRUPAL_CORE_COMPATIBILITY) {
      $incompatible[] = !empty($info['name']) ? $info['name'] : t('Unknown');
    }
    else {
      $compatible_project = TRUE;
      break;
    }
  }

  if (empty($files)) {
    $errors[] = t('%archive_file does not contain any .info files.', array('%archive_file' => drupal_basename($archive_file)));
  }
  elseif (!$compatible_project) {
    $errors[] = format_plural(
      count($incompatible),
      '%archive_file contains a version of %names that is not compatible with Drupal !version.',
      '%archive_file contains versions of modules or themes that are not compatible with Drupal !version: %names',
      array('!version' => DRUPAL_CORE_COMPATIBILITY, '%archive_file' => drupal_basename($archive_file), '%names' => implode(', ', $incompatible))
    );
  }

  return $errors;
}

/**
 * @defgroup update_status_cache Private update status cache system
 * @{
 * Functions to manage the update status cache.
 *
 * We specifically do NOT use the core cache API for saving the fetched data
 * about available updates. It is vitally important that this cache is only
 * cleared when we're populating it after successfully fetching new available
 * update data. Usage of the core cache API results in all sorts of potential
 * problems that would result in attempting to fetch available update data all
 * the time, including if a site has a "minimum cache lifetime" (which is both a
 * minimum and a maximum) defined, or if a site uses memcache or another
 * pluggable cache system that assumes volatile caches.
 *
 * The Update Manager module still uses the {cache_update} table, but instead of
 * using cache_set(), cache_get(), and cache_clear_all(), there are private
 * helper functions that implement these same basic tasks but ensure that the
 * cache is not prematurely cleared, and that the data is always stored in the
 * database, even if memcache or another cache backend is in use.
 */

/**
 * Stores data in the private update status cache table.
 *
 * @param $cid
 *   The cache ID to save the data with.
 * @param $data
 *   The data to store.
 * @param $expire
 *   One of the following values:
 *   - CACHE_PERMANENT: Indicates that the item should never be removed except
 *     by explicitly using _update_cache_clear().
 *   - A Unix timestamp: Indicates that the item should be kept at least until
 *     the given time, after which it will be invalidated.
 *
 * @see _update_cache_get()
 */
function _update_cache_set($cid, $data, $expire) {
  $fields = array(
    'created' => REQUEST_TIME,
    'expire' => $expire,
  );
  if (!is_string($data)) {
    $fields['data'] = serialize($data);
    $fields['serialized'] = 1;
  }
  else {
    $fields['data'] = $data;
    $fields['serialized'] = 0;
  }
  db_merge('cache_update')
    ->key(array('cid' => $cid))
    ->fields($fields)
    ->execute();
}

/**
 * Retrieves data from the private update status cache table.
 *
 * @param $cid
 *   The cache ID to retrieve.
 *
 * @return
 *   An array of data for the given cache ID, or NULL if the ID was not found.
 *
 * @see _update_cache_set()
 */
function _update_cache_get($cid) {
  $cache = db_query("SELECT data, created, expire, serialized FROM {cache_update} WHERE cid = :cid", array(':cid' => $cid))->fetchObject();
  if (isset($cache->data)) {
    if ($cache->serialized) {
      $cache->data = unserialize($cache->data);
    }
  }
  return $cache;
}

/**
 * Returns an array of cache items with a given cache ID prefix.
 *
 * @param string $cid_prefix
 *   The cache ID prefix.
 *
 * @return
 *   Associative array of cache items, keyed by cache ID.
 */
function _update_get_cache_multiple($cid_prefix) {
  $data = array();
  $result = db_select('cache_update')
    ->fields('cache_update', array('cid', 'data', 'created', 'expire', 'serialized'))
    ->condition('cache_update.cid', $cid_prefix . '::%', 'LIKE')
    ->execute();
  foreach ($result as $cache) {
    if ($cache) {
      if ($cache->serialized) {
        $cache->data = unserialize($cache->data);
      }
      $data[$cache->cid] = $cache;
    }
  }
  return $data;
}

/**
 * Invalidates cached data relating to update status.
 *
 * @param $cid
 *   (optional) Cache ID of the record to clear from the private update module
 *   cache. If empty, all records will be cleared from the table except fetch
 *   tasks. Defaults to NULL.
 * @param $wildcard
 *   (optional) If TRUE, cache IDs starting with $cid are deleted in addition to
 *   the exact cache ID specified by $cid. Defaults to FALSE.
 */
function _update_cache_clear($cid = NULL, $wildcard = FALSE) {
  if (empty($cid)) {
    db_delete('cache_update')
      // Clear everything except fetch task information because these are used
      // to ensure that the fetch task queue items are not added multiple times.
      ->condition('cid', 'fetch_task::%', 'NOT LIKE')
      ->execute();
  }
  else {
    $query = db_delete('cache_update');
    if ($wildcard) {
      $query->condition('cid', $cid . '%', 'LIKE');
    }
    else {
      $query->condition('cid', $cid);
    }
    $query->execute();
  }
}

/**
 * Implements hook_flush_caches().
 *
 * Called from update.php (among others) to flush the caches. Since we're
 * running update.php, we are likely to install a new version of something, in
 * which case, we want to check for available update data again. However,
 * because we have our own caching system, we need to directly clear the
 * database table ourselves at this point and return nothing, for example, on
 * sites that use memcache where cache_clear_all() won't know how to purge this
 * data.
 *
 * However, we only want to do this from update.php, since otherwise, we'd lose
 * all the available update data on every cron run. So, we specifically check if
 * the site is in MAINTENANCE_MODE == 'update' (which indicates update.php is
 * running, not update module... alas for overloaded names).
 */
function update_flush_caches() {
  if (defined('MAINTENANCE_MODE') && MAINTENANCE_MODE == 'update') {
    _update_cache_clear();
  }
  return array();
}

/**
 * @} End of "defgroup update_status_cache".
 */

/**
 * Returns a short unique identifier for this Drupal installation.
 *
 * @return
 *   An eight character string uniquely identifying this Drupal installation.
 */
function _update_manager_unique_identifier() {
  $id = &drupal_static(__FUNCTION__, '');
  if (empty($id)) {
    $id = substr(hash('sha256', drupal_get_hash_salt()), 0, 8);
  }
  return $id;
}

/**
 * Returns the directory where update archive files should be extracted.
 *
 * @param $create
 *   (optional) Whether to attempt to create the directory if it does not
 *   already exist. Defaults to TRUE.
 *
 * @return
 *   The full path to the temporary directory where update file archives should
 *   be extracted.
 */
function _update_manager_extract_directory($create = TRUE) {
  $directory = &drupal_static(__FUNCTION__, '');
  if (empty($directory)) {
    $directory = 'temporary://update-extraction-' . _update_manager_unique_identifier();
    if ($create && !file_exists($directory)) {
      mkdir($directory);
    }
  }
  return $directory;
}

/**
 * Returns the directory where update archive files should be cached.
 *
 * @param $create
 *   (optional) Whether to attempt to create the directory if it does not
 *   already exist. Defaults to TRUE.
 *
 * @return
 *   The full path to the temporary directory where update file archives should
 *   be cached.
 */
function _update_manager_cache_directory($create = TRUE) {
  $directory = &drupal_static(__FUNCTION__, '');
  if (empty($directory)) {
    $directory = 'temporary://update-cache-' . _update_manager_unique_identifier();
    if ($create && !file_exists($directory)) {
      mkdir($directory);
    }
  }
  return $directory;
}

/**
 * Clears the temporary files and directories based on file age from disk.
 */
function update_clear_update_disk_cache() {
  // List of update module cache directories. Do not create the directories if
  // they do not exist.
  $directories = array(
    _update_manager_cache_directory(FALSE),
    _update_manager_extract_directory(FALSE),
  );

  // Search for files and directories in base folder only without recursion.
  foreach ($directories as $directory) {
    file_scan_directory($directory, '/.*/', array('callback' => 'update_delete_file_if_stale', 'recurse' => FALSE));
  }
}

/**
 * Deletes stale files and directories from the update manager disk cache.
 *
 * Files and directories older than 6 hours and development snapshots older than
 * 5 minutes are considered stale. We only cache development snapshots for 5
 * minutes since otherwise updated snapshots might not be downloaded as
 * expected.
 *
 * When checking file ages, we need to use the ctime, not the mtime
 * (modification time) since many (all?) tar implementations go out of their way
 * to set the mtime on the files they create to the timestamps recorded in the
 * tarball. We want to see the last time the file was changed on disk, which is
 * left alone by tar and correctly set to the time the archive file was
 * unpacked.
 *
 * @param $path
 *   A string containing a file path or (streamwrapper) URI.
 */
function update_delete_file_if_stale($path) {
  if (file_exists($path)) {
    $filectime = filectime($path);
    if (REQUEST_TIME - $filectime > DRUPAL_MAXIMUM_TEMP_FILE_AGE || (preg_match('/.*-dev\.(tar\.gz|zip)/i', $path) && REQUEST_TIME - $filectime > 300)) {
      file_unmanaged_delete_recursive($path);
    }
  }
}

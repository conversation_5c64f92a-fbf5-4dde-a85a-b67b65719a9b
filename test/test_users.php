<?php

/**
 * https://staging.zauberlist.com/test/test_users.php
 */

use App\Klicktipp\Tag;
use App\Klicktipp\VarDataProcessingOrder;

require_once 'form.inc';

// bootstrap
test_bootstrap();

$form = drupal_get_form('test_form');
test_show_form('Create Test User', drupal_render($form));

exit;

/**
 */
function test_form($form, $form_state) {

  $weight = 1;

  $form['parameters'] = array(
    '#type' => 'fieldset',
    '#title' => t('User Parameter'),
    '#weight' => $weight++,
  );

  $form['parameters']['usercount'] = array(
    '#type' => 'textfield',
    '#title' => t('No of users'),
    '#default_value' => TEST_USERS_COUNT,
    '#description' => t('No of users we run in parallel (one campaign per user).'),
    '#weight' => $weight++,
  );

  $form['parameters']['pattern'] = array(
    '#type' => 'textfield',
    '#title' => t('Username pattern'),
    '#default_value' => TEST_USERNAME_PATTERN,
    '#description' => t('Enter a pattern (sprintf) for the usernames. The pattern !pattern creates !username for uid !uid.',
      array('!pattern' => TEST_USERNAME_PATTERN, '!username' => sprintf(TEST_USERNAME_PATTERN, 99), '!uid' => 99)),
    '#weight' => $weight++,
  );

  $form['parameters']['tagname'] = array(
    '#type' => 'textfield',
    '#title' => t('Tag name'),
    '#default_value' => 'pmtatest',
    '#description' => t('Enter a tag name to segment subscribers (campaign start condition).'),
    '#weight' => $weight++,
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#value' => t('Create'),
  );

  return $form;
}

/**
 */
function test_form_validate($form, &$form_state) {
}

/**
 */
function test_form_submit($form, &$form_state) {

  $pattern = $form_state['values']['pattern'];
  $usercount = $form_state['values']['usercount'];
  $tagname = $form_state['values']['tagname'];

  // get group "Enterprise Users"
  $UserGroupID = db_query("SELECT UserGroupID FROM {user_groups} WHERE GroupName = 'Enterprise 99'")->fetchField();

  for ($no = 0; $no < $usercount; $no++) {
    $username = sprintf($pattern, $no+1);

    $account = user_load_by_name($username);
    if ($account) {
      drupal_set_message("User $username exists.");
    }
    else {
      // create user

      $account = user_save(NULL, [
        'RelUserGroupID' => $UserGroupID,
        'name' => $username,
        'FirstName' => 'Firstname' . $username,
        'LastName' => 'Lastname' . $username,
        'mail' => $username . '@zauberlist.com',
        'pass' => 'U4nATY2f'
      ]);
      if ($account) {
        drupal_set_message("User $username created.");
      }
      else {
        drupal_set_message("User $username creation failed.", 'error');
      }
    }

    if ($account) {
      if (Tag::RetrieveTagIdByName($account->uid, $tagname)) {
        drupal_set_message("Tag $tagname for $username already exists.");
      }
      else {
        // create tag

        $tagid = Tag::CreateManualTag($account->uid, $tagname, '');
        if ($tagid) {
          drupal_set_message("Tag $tagname for $username created.");
        }
        else {
          drupal_set_message("Tag $tagname for $username creation failed.", 'error');
        }
      }
    }

    // confirm dpo
    if (VarDataProcessingOrder::Confirm($account->uid)) {
      drupal_set_message("DPO contract for $username confirmed.");
    }
    else {
      drupal_set_message("DPO contract could not be confirmed for $username.", 'error');
    }
  }

  $form_state['redirect'] = '/test/test_users.php';
}

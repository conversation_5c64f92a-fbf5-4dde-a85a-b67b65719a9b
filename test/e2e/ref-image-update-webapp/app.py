import os
import re
from shutil import copyfile

from flask import render_template
from flask import Flask

app = Flask(__name__)

REPORT_SCREENSHOTS_ROOT_DIR = "static"
SCREENSHOTS_DIR_VOLUME = "/screenshots"


@app.route('/')
def report():
    return render_template('index.html')


@app.route('/copy-all')
def copy_all():
    list_of_copied_files = []

    for root, dirs, files in os.walk(REPORT_SCREENSHOTS_ROOT_DIR):
        for new_image in files:
            if re.search(r'current\.png$', new_image):
                # list_of_files.append(os.path.join(root, file))
                print("file : " + new_image)

                new_image_on_volume = re.sub(r'_current\.png', "_expected.png", new_image)

                new_ref_img_src = os.path.join(root, new_image)
                new_ref_img_target_dir = os.path.join(SCREENSHOTS_DIR_VOLUME,
                                                      re.match(r'.+?([^\/]+)\/([^/]+)$', new_ref_img_src).group(1))
                new_ref_img_target = os.path.join(new_ref_img_target_dir, new_image_on_volume)

                print("source file -> " + new_ref_img_src)
                print("target dir  -> " + new_ref_img_target)

                copyfile(new_ref_img_src, new_ref_img_target)
                list_of_copied_files.append(new_ref_img_src + " -> " + new_ref_img_target)

    # Shown on html page as feedback.
    if list_of_copied_files != []:
        return "Copied all current screenshots as new reference images!\n" + "\n".join(list_of_copied_files)
    else:
        return "No new current screenshots in report found, which need to be copied as new reference."


@app.route('/copy-new/<path:new_image>')
def copy_new(new_image):
    print("new_image : " + new_image)
    new_image_on_volume = new_image
    new_image = re.sub(r'/?$', ".png", new_image)
    new_image_on_volume = re.sub(r'(expected|current)/?$', "expected.png", new_image_on_volume)
    new_ref_img_src = os.path.join(REPORT_SCREENSHOTS_ROOT_DIR, new_image)
    new_ref_img_target_dir = os.path.join(SCREENSHOTS_DIR_VOLUME, os.path.dirname(new_image))

    if not os.path.exists(new_ref_img_target_dir):
        os.makedirs(new_ref_img_target_dir)

    new_ref_img_target = os.path.join(new_ref_img_target_dir, os.path.basename(new_image_on_volume))

    print("new_image fixed : " + new_image)
    print("copy : " + new_ref_img_src + " -> " + new_ref_img_target)
    copyfile(new_ref_img_src, new_ref_img_target)

    # Shown on html page as feedback.
    return 'Copied ' + new_ref_img_src + " -> " + new_ref_img_target


@app.route('/test/')
def test():
    return 'Your flask app is running!'

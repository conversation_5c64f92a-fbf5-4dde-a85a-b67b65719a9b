import argparse
import subprocess
import os
import sys
import glob
import json

import run_parallel_tests_helper
from tests.helpers import aws_secret_manager as aws_sm

help_text = '''\
Especially for running e2e-rr recordings in parallel the gitlab CI.
Parallel means here: one test recording against different browsers at the same time.

Runs directly on a python based gitlab runner. Not triggered by the local e2e-rr backend.

Maybe, if this message is shown, you missed some parameters which are mandatory to run the script locally.

Or the call in the endpoint is no more up-2-date and incorrect.

'''

parser = argparse.ArgumentParser(formatter_class=argparse.RawDescriptionHelpFormatter,
                                 usage=help_text,
                                 description=help_text)

parser.add_argument('--bundle-nr', help='0 means recordings 1-10, 1 means 11-20, ...', required=True)
parser.add_argument('--tests-per-bundle', help='e.g. 6', required=True)
parser.add_argument('--testsession', help='e.g. some name identifying the test execution', required=True)
args = parser.parse_args()

arg_bundle_nr = int(args.bundle_nr)
arg_tests_per_bundle = int(args.tests_per_bundle)
arg_testsession = args.testsession

# bundle nr (index starting by 0)
print("--bundle-nr            : ")
print(arg_bundle_nr)

print("--tests-per-bundle     : ")
print(arg_tests_per_bundle)

print("--testsession          : ")
print(arg_testsession)

class ParallelHandlerCI(object):
    """Handle execution in pipeline."""

    bundle_nr = None
    tests_per_bundle = None
    testsession = None

    def __init__(self, bundle_nr, tests_per_bundle, testsession):
        """Init some instance attributes."""

        ParallelHandlerCI.bundle_nr = bundle_nr
        ParallelHandlerCI.tests_per_bundle = tests_per_bundle
        ParallelHandlerCI.testsession = testsession

    def exec_tests(self):
        bundles = run_parallel_tests_helper.get_bundles(self.tests_per_bundle)
        if self.bundle_nr + 1 > len(bundles):
            print("No more test bundles. No test execution. All tests already executed by the other gitlab jobs.")
            sys.exit(0)

        # Exec tests in appropriate bundle
        # Get standard params for this test session
        app_location = os.getenv("APP_LOCATION")
        klicktipp_url = os.getenv("KLICKTIPP_URL")
        browsers = os.getenv("BROWSERS")
        force_new_dumps = os.getenv("FORCE_NEW_DUMPS")
        prepare = os.getenv("PREPARE")
        caching = os.getenv("CACHING")
        compare_and_allow_fail = os.getenv("E2E_RR_SCREENS_COMPARE_AND_ALLOW_FAIL")
        compare_and_fail = os.getenv("E2E_RR_SCREENS_COMPARE_AND_FAIL")
        new_screenshots = os.getenv("E2E_RR_NEW_SCREENSHOTS")
        retry = os.getenv("RETRY")
        browserstack_user = "non_existing_browserstack_user_not_needed_in_ci"
        browserstack_token = "non_existing_browserstack_token_not_needed_in_ci"
        exec_user = f"e2e-test-replay-parallel-{self.bundle_nr}"
        aws_secret_manager = aws_sm.SecretManager()
        exec_user_pwd = aws_secret_manager.get_secret("record_replay_user_by_browser", None)
        cmd = f"make tests-e2e-parallel TESTSESSION={self.testsession} APP_LOCATION={app_location} KLICKTIPP_URL={klicktipp_url} \
        BROWSERS={browsers} FORCE_NEW_DUMPS={force_new_dumps} \
        PREPARE={prepare} CACHING={caching} \
        EXEC_USER={exec_user} EXEC_USER_PWD={exec_user_pwd} \
        E2E_RR_NEW_SCREENSHOTS={new_screenshots} E2E_RR_SCREENS_COMPARE_AND_FAIL={compare_and_fail} E2E_RR_SCREENS_COMPARE_AND_ALLOW_FAIL={compare_and_allow_fail} \
        DEBUG=0 RETRY={retry} BROWSERSTACK_USER={browserstack_user} BROWSERSTACK_TOKEN={browserstack_token}"
        results = {}
        failed = False
        for test in bundles[self.bundle_nr]:
            log_file_path = f"test_execution_{self.bundle_nr}.log"
            with open(log_file_path, "a") as log_file:
                command_line_process = subprocess.Popen(
                    cmd + f" TESTCASE={test}",
                    shell=True,
                    stdout=log_file,
                    stderr=log_file,
                    universal_newlines=True
                )
            return_code = command_line_process.wait()
            if return_code:
                # False means failed
                results[test] = "Failed"
                failed = True
            else:
                results[test] = "Passed"

        for test in bundles[self.bundle_nr]:
            test_result = results[test]
            print("=====================================")
            print(f"{test} : {test_result}")
            print("-------------------------------------")
            # https://klicktipp.gitlab.io/-/ktdev/klick-tipp/-/jobs/5079516114/artifacts/test/e2e/reports/ci_recording_test_bundle_0/test_campaigns__newsletter__email__ready__navigation_roundtrip_1688633380389/report-chrome/index.html
            base_path = "https://klicktipp.gitlab.io/-/ktdev/klick-tipp/-/jobs/"
            ci_job_id = os.getenv("CI_JOB_ID")
            artifact_base_path = "/artifacts/test/e2e/reports/"
            test_case_path = f"{base_path}{ci_job_id}{artifact_base_path}{self.testsession}/{test}/"
            logs_base_path = f"{test_case_path}logs/"

            if os.getenv("CI_JOB_ID"):
                # Check for data preparation failures
                local_logs_base_path = os.path.join(os.getcwd(), "reports", self.testsession, test, "logs")
                if test_result == "Failed":
                    dataprep_failure_files = glob.glob(os.path.join(local_logs_base_path, "*_dataprep_failure.json"))
                    for dataprep_failure_path in dataprep_failure_files:
                        with open(dataprep_failure_path, "r") as fh:
                            dataprep_failure_info = json.load(fh)
                            print(f"Data preparation failed: {dataprep_failure_info['reason']}")
                            print(f"Failure details: {os.path.join(logs_base_path, os.path.basename(dataprep_failure_path))}")

                print("--------------- HTML REPORT --------------")
                for browser in browsers.split(","):
                    report_path = f"{test_case_path}report-{browser}/index.html"
                    print(f"-> REPORT for {browser}: {report_path}")

                if test_result == "Failed":
                    print("------------------------------------------")
                    print("HTML report not found? Check the following files for more information")
                    print("--------------- LOG FILES --------------")
                    for browser in browsers.split(","):
                        machine_name_json = f"{logs_base_path}{browser}.{test}"
                        browser_log = f"{logs_base_path}{browser}.log"
                        print(f"{browser} log: {browser_log}")
                        print(f"{browser} exec info: {machine_name_json}")

                    # Construct URIs to the JSON, exec_info.json, and log files
                    exec_info = f"{logs_base_path}exec_info.json"
                    caching_states = f"{logs_base_path}caching_states.json"
                    cache_exec_info = f"{logs_base_path}cache_exec_info.json"

                    print(f"Execution info: {exec_info}")
                    print(f"Caching states: {caching_states}")
                    print(f"Cache execution info: {cache_exec_info}")

                print("=====================================")

        if failed:
            sys.exit(1)


parallel_handler_ci = ParallelHandlerCI(arg_bundle_nr, arg_tests_per_bundle, arg_testsession)

parallel_handler_ci.exec_tests()

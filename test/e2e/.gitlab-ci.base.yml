---

###############################################################################
#
#                                  E2E Gitlab Pipeline
#
###############################################################################

###############################################################################
# Settings
###############################################################################

stages:
  - pre
  - test
  - e2e

default:
  image: "035931995993.dkr.ecr.eu-west-1.amazonaws.com/kt-gitlab-runner:master"
  before_script:
    - source .env
    - cat /etc/os-release
  retry:
    max: 2
    when:
      - api_failure
      - job_execution_timeout
      - runner_system_failure
      - runner_unsupported
      - scheduler_failure
      - stale_schedule
      - stuck_or_timeout_failure
      - unknown_failure

workflow:
  # Rules applied to the pipeline execution
  rules:
    ## Do not run Pipeline on merge requests
    - if: $CI_MERGE_REQUEST_IID
      when: never
    ## Always execute Pipeline on other branches
    - when: always

###############################################################################
# Templates
###############################################################################

include:

  # https://gitlab.com/klicktipp/cicd/kt-common-components/-/blob/main/templates/.gitlab-ci-template.yml
  - project: klicktipp/cicd/kt-common-components
    file:
      - /templates/.gitlab-ci-template.yml
    ref: main

.e2e-template:
  extends: .job_docker_test
  stage: test
  timeout: 45m
  retry:
    max: 1
  interruptible: true
  needs:
    - pipeline: $PARENT_PIPELINE_ID
      job: e2e:script-executor-images
      artifacts: true
    - pipeline: $PARENT_PIPELINE_ID
      job: "kt:docker:cockpit:build: [amd64, ]"   # https://docs.gitlab.com/ci/jobs/job_control/#fetch-artifacts-from-a-parallelmatrix-job
      artifacts: true
    - pipeline: $PARENT_PIPELINE_ID
      job: "kt:docker:client:build: [amd64, ]"   # https://docs.gitlab.com/ci/jobs/job_control/#fetch-artifacts-from-a-parallelmatrix-job
      artifacts: true
    - pipeline: $PARENT_PIPELINE_ID
      job: kt:docker:php:manifest
      artifacts: false
    - pipeline: $PARENT_PIPELINE_ID
      job: kt:docker:angular-nginx:manifest
  variables:
    # Set the docker compose env vars in order to use the CI config and per-test-run project
    COMPOSE_PROJECT_NAME: "${CI_PROJECT_NAME}-e2e-${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}"
    COMPOSE_PATH_SEPARATOR: ";"
    COMPOSE_FILE: "docker-compose.yml;docker-compose.override.yml;docker-compose.ci.yml;docker-compose.e2e.local.yml"
    TESTSET: ci_local
  artifacts:
    when: always
    name: "$CI_JOB_NAME"
    paths:
      - test/e2e/report-$BROWSER
      - browserstacklocal-ubuntu.log
    expire_in: 4 weeks
  rules:
    - if: $CI_PIPELINE_SOURCE != "pipeline"
      when: on_success
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE != "schedule"
      when: always
      allow_failure: false
    - when: manual

.e2e-chrome-template:
  extends: .e2e-template
  timeout: 2h
  variables:
    BROWSER: chrome
  script:
    - cd test/e2e
    - env
    - make print_compose_config BROWSER=$BROWSER TESTSET=$TESTSET KLICKTIPP_URL=$KLICKTIPP_URL CI_COMMIT_TAG=$CI_COMMIT_TAG PUBLISH_IN_TM4J=1 CI_PIPELINE_ID=$CI_PIPELINE_ID CI_COMMIT_BRANCH=$CI_COMMIT_BRANCH
    - docker volume rm -f klicktipp-keycloak-db klicktipp-percona klick-tipp-e2e_percona-data klick-tipp-e2e_keycloak-db-data 2>/dev/null || true
    - make tests-e2e-screen-tests BROWSER=$BROWSER TESTSET=$TESTSET KLICKTIPP_URL=$KLICKTIPP_URL CI_COMMIT_TAG=$CI_COMMIT_TAG PUBLISH_IN_TM4J=1 CI_PIPELINE_ID=$CI_PIPELINE_ID CI_COMMIT_BRANCH=$CI_COMMIT_BRANCH

.e2e-chrome-local-template:
  extends: .e2e-chrome-template
  script:
    - python --version
    - cd test/e2e
    - env
    - make print_compose_config BROWSER=$BROWSER TESTSET=$TESTSET
    - docker volume rm -f klicktipp-keycloak-db klicktipp-percona klick-tipp-e2e_percona-data klick-tipp-e2e_keycloak-db-data 2>/dev/null || true
    - make kt-stack-up
    - make tests-e2e-local-screen-tests BROWSER=$BROWSER TESTSET=$TESTSET
  needs:
    - e2e:local-testdata-prep
    - pipeline: $PARENT_PIPELINE_ID
      job: "kt:docker:cockpit:build: [amd64, ]"   # https://docs.gitlab.com/ci/jobs/job_control/#fetch-artifacts-from-a-parallelmatrix-job
      artifacts: true
    - pipeline: $PARENT_PIPELINE_ID
      job: "kt:docker:client:build: [amd64, ]"   # https://docs.gitlab.com/ci/jobs/job_control/#fetch-artifacts-from-a-parallelmatrix-job
      artifacts: true
    - pipeline: $PARENT_PIPELINE_ID
      job: kt:docker:php:manifest
      artifacts: false
    - pipeline: $PARENT_PIPELINE_ID
      job: kt:docker:angular-nginx:manifest

.e2e-chrome-local-clean-install-template:
  extends: .e2e-chrome-local-template
  script:
    - python --version
    - cd test/e2e
    - env
    - make print_compose_config BROWSER=$BROWSER TESTSET=$TESTSET
    - docker volume rm -f klicktipp-keycloak-db klicktipp-percona klick-tipp-e2e_percona-data klick-tipp-e2e_keycloak-db-data 2>/dev/null || true
    - make kt-stack-up
    - make kt-install-via-drush
    - make tests-e2e-local-screen-tests BROWSER=$BROWSER TESTSET=$TESTSET

.e2e-rr-template:
  extends: .e2e-template
  image: 035931995993.dkr.ecr.eu-west-1.amazonaws.com/e2e-gitlab-runner:master
  stage: e2e
  # We disable the retries for e2e-rr tests.
  # We have (see below - RETRY=1) fast retries on level of the e2e-rr parallel handler.
  # We spare the time, re-building up a gitlab runner.
  # side effect of the fast retries on parallel-handler level: we need more time in the runner. Increase timeout to 25m.
  # No retries for job error (so, when our tests have failed)
  timeout: 25m
  retry:
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
      - scheduler_failure
      - data_integrity_failure
      - runner_unsupported
      - api_failure
      - unknown_failure
    max: 1
  variables:
    # Test execution parameters
    TESTSESSION_PREFIX: ci_recording_test_bundle_
    CI_RECORDING_BUNDLE: 0
    CI_TEST_CLICK_STEPS_PER_RECORDING_BUNDLE: 80
    APP_LOCATION: remote
    KLICKTIPP_URL: "overwritten_in_script_part"
    BROWSERS: chrome
    FORCE_NEW_DUMPS: 0
    PREPARE: 1
    E2E_RR_SCREENS_COMPARE_AND_ALLOW_FAIL: 1
    E2E_RR_SCREENS_COMPARE_AND_FAIL: 0
    E2E_RR_NEW_SCREENSHOTS: 0
    CACHING: 1
    RETRY: 1
  script:
    - cat /etc/issue
    - cd test/e2e
    - env
    - docker network ls
    - docker ps -a
    - docker volume ls
    - TZ=Europe/Berlin echo "$(date +"%Y-%m-%d %T") START TESTS E2E PARALLEL CI"
    - export APP_URL="app.${KLICKTIPP_NAMESPACE}.${KLICKTIPP_ENVIRONMENT}.${CLUSTER_BASE_DOMAIN}"
    - echo $APP_URL
    - export KLICKTIPP_URL=$APP_URL
    - echo $KLICKTIPP_URL
    - make tests-e2e-parallel-ci TESTSESSION=$TESTSESSION_PREFIX$CI_RECORDING_BUNDLE CI_RECORDING_BUNDLE=$CI_RECORDING_BUNDLE CI_TEST_CLICK_STEPS_PER_RECORDING_BUNDLE=$CI_TEST_CLICK_STEPS_PER_RECORDING_BUNDLE
    - TZ=Europe/Berlin echo "$(date +"%Y-%m-%d %T") END TESTS E2E PARALLEL CI"
  artifacts:
    when: always
    name: "$CI_JOB_NAME"
    paths:
      - test/e2e/reports
      - test/e2e/test_execution_*.log
    expire_in: 4 weeks

# Currently no TM4J/Zephyr publish
.e2e-browserstack:local-template:
  extends: .e2e-chrome-local-template
  variables:
    KLICKTIPP_URL: app-e2e.ktlocal.com
    TESTSET: ci_local
    BROWSER: edge
  script:
    - env
    - cd test/e2e
    - make tests-e2e-local-bs BROWSER=$BROWSER KLICKTIPP_URL=$KLICKTIPP_URL TESTSET=$TESTSET

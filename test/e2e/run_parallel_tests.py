import argparse
import getpass
import docker
import shutil
import psutil  # for killing subprocesses when no process group id is given
import signal
import subprocess
import glob
import json
import os
import re
import sys
import time
import traceback
from datetime import datetime
import requests

from record_replay.flask_app import script_creator
from record_replay.flask_app import dependencies

from tests.helpers import aws_secret_manager as aws_sm
from cryptography.fernet import Fernet

help_text = '''\
Runs recorded tests in parallel.
Parallel means here: one test recording against different browsers at the same time.

Triggered via an tool backend endpoint in a docker container.

Maybe, if this message is shown, you missed some parameters which are mandatory to run the script locally.

Or the call in the endpoint is no more up-2-date and incorrect.
'''

parser = argparse.ArgumentParser(formatter_class=argparse.RawDescriptionHelpFormatter,
                                 usage=help_text,
                                 description=help_text)

parser.add_argument('--app-location', help='\'e.g. local || remote\'', required=True)
parser.add_argument('--domain', help='\'e.g. app-e2e.ktlocal.com || www.klicktipp-staging.com\'', required=True)
parser.add_argument('--browsers', help='\'list of browsers, comma separated without spaces\'', required=True)
parser.add_argument('--test-case', help='test case list, comma separated without spaces', required=True)
parser.add_argument('--prepare', help='0|1 - prepare=1 also imports actual (last) tc - would be skipped for browser x, if caching was possible', required=True)
parser.add_argument('--force-new-dumps', help='0|1 - =1 after each tc execution, a new dump will be created', required=True)
parser.add_argument('--caching', help='0|1 - =1 after each tc execution, a new dump will be created', required=True)
parser.add_argument('--exec-user', help='0|1 - =1 dump if all passed or skipped because browser was cached', required=True)
parser.add_argument('--exec-user-pwd', help='0|1 - =1 dump if all passed or skipped because browser was cached', required=True)
parser.add_argument('--browserstack-user', help='0|1 - =1 dump if all passed or skipped because browser was cached', required=True)
parser.add_argument('--browserstack-token', help='0|1 - =1 dump if all passed or skipped because browser was cached', required=True)
parser.add_argument('--compare-and-fail', help='0|1', required=True)
parser.add_argument('--compare-and-allow-fail', help='0|1', required=True)
parser.add_argument('--new-screenshots', help='0|1', required=True)
parser.add_argument('--debug', help='0|1 - =1 dump if all passed or skipped because browser was cached', required=True)
parser.add_argument('--retry', help='0|1 - =1 retry the test execution', required=True)
args = parser.parse_args()

arg_browsers = args.browsers.split(',')
arg_app_location = args.app_location
arg_test_case = args.test_case
arg_domain = args.domain
arg_prepare = args.prepare
arg_force_new_dumps = args.force_new_dumps
arg_caching = args.caching
arg_exec_user = args.exec_user
arg_exec_user_pwd = args.exec_user_pwd
arg_browserstack_user = args.browserstack_user
arg_browserstack_token = args.browserstack_token
arg_compare_and_fail = args.compare_and_fail
arg_compare_and_allow_fail = args.compare_and_allow_fail
arg_new_screenshots = args.new_screenshots
arg_debug = args.debug
arg_retry = args.retry

# local,remote
print("App location     : ")
print(arg_app_location)

# chrome,safari_macos,...
print("Domain     : ")
print(arg_domain)

# chrome,safari_macos,...
print("Browsers   : ")
print(arg_browsers)

# test_create_tag_1234
print("Test Case  : ")
print(arg_test_case)

# user
print("Exec user  : ")
print(arg_exec_user)


def encrypt_exec_user_pwd(p: str):
    dir_path = os.path.dirname(os.path.realpath(__file__))
    tmp_file = "tmp300dkd"
    tmp_file_path = os.path.join(dir_path, "tests", "fixtures", tmp_file)

    if os.path.exists(tmp_file_path):
        os.remove(tmp_file_path)
        print(f"filepath : {tmp_file_path}")
    with open(tmp_file_path, 'wb') as FH:
        aws_secret_manager = aws_sm.SecretManager()
        k = aws_secret_manager.get_secret("exec_user_pwd_crypt_key", None)
        FH.write(Fernet(k.encode()).encrypt(p.encode()))
    print("Creation was fine")


class ParallelHandler(object):
    """Handle parallel execution."""

    app_location = None
    domain = None
    browsers = None
    browser_user_prefix = None
    users = []
    browser_user_mapping = {}
    test_case = None
    prepare = None
    branch = None
    create_new_dumps_hard = None
    caching = None
    exec_user = None
    exec_user_pwd = None
    browserstack_user = None
    browserstack_token = None
    compare_and_fail = None
    compare_and_allow_fail = None
    new_screenshots = None
    debug = None

    # klick-tipp/test/e2e
    curr_dir = None
    tests_dir = None
    recordings_dir = None
    tc_dir = None
    relative_tc_parent_folder = None
    tc_exec_py_package_name = None
    tc_cache_dir = None

    test_set_dir = None
    test_set_file = None
    test_set = None

    tc_id = None
    tc_script_path = None

    dependency_chain = None
    all_dep_infos = None
    deps_and_tc_infos = None
    tc_names_in_order = None
    tc_ids_in_order = None
    temp_exec_py_package_dir = None

    caching_states = None
    # Holds general execution information
    exec_info = {}
    # Holds caching information
    cache_exec_info = None
    pids = {}  # { 'chrome': 2988, 'firefox': 2783 } <- one entry per parallel running browser test
    executors = {}  # { 'chrome': 'klick-tipp-e2e-test-chrome_tester_run_25be005b1feb'}
    # Will be set during evaluating cache situation, before the local test starts
    imported_test_case = None

    def __init__(self, app_location, domain, browsers, test_case, prepare, force_new_dumps, caching,
                 exec_user, exec_user_pwd, browserstack_user, browserstack_token,
                 compare_and_fail, compare_and_allow_fail, new_screenshots, debug):
        """Init some instance attributes."""

        # remote | local
        ParallelHandler.app_location = app_location

        # app-e2e.ktlocal.com
        # www.klicktipp-staging.com
        # app.ktlocal.com
        ParallelHandler.domain = domain

        # ["chrome", "safari_macos"]
        ParallelHandler.browsers = browsers

        # test_create_formular_6666
        ParallelHandler.test_case = test_case

        # 0 | 1 - include last tc in caching
        # so, import also last tc, if caching is possible
        ParallelHandler.prepare = bool(int(prepare))

        # 0 | 1 - =1 don't use caching and re-create any new dump after a tc has finished (with all executions passed!)
        ParallelHandler.create_new_dumps_hard = bool(int(force_new_dumps))

        # 0 | 1 - =1 use caching e.g. import dumps if possible and update dumps if possible
        #         =0 don't import possible dumps and don't update any dump
        ParallelHandler.caching = bool(int(caching))

        # The user you want to run the test for. Especially for staging or other non-local domains/environments
        ParallelHandler.exec_user = exec_user

        # Above exec_user pwd
        encrypt_exec_user_pwd(exec_user_pwd)

        # Generate usernames for handling caching
        # ["e2e-test-replay-chrome", "e2e-test-replay-safari_macos"]
        #  or if exec_user is not set
        # ["e2e-test-recorder"] <- standard local recorder username
        ParallelHandler.users = []
        ParallelHandler.browser_user_prefix = "e2e-test-replay-"
        if ParallelHandler.exec_user != "" and ParallelHandler.exec_user.lower() != "none":
            ParallelHandler.users.append(ParallelHandler.exec_user)
            # If you choose a special user, you only run for that user
            # We choose for preparation the first browser in the browser list
            # The rest will be ignored
            ParallelHandler.browsers = ["chrome"]
            ParallelHandler.browser_user_mapping[ParallelHandler.browsers[0]] = ParallelHandler.exec_user
        else:
            for browser in browsers:
                username = ParallelHandler.browser_user_prefix + browser
                ParallelHandler.users.append(username)
                ParallelHandler.browser_user_mapping[browser] = username

        # browserstack user for triggering tests and retrieving videos
        ParallelHandler.browserstack_user = browserstack_user

        # browserstack token for triggering tests and retrieving videos
        ParallelHandler.browserstack_token = browserstack_token

        ParallelHandler.compare_and_fail = bool(int(compare_and_fail))
        ParallelHandler.compare_and_allow_fail = bool(int(compare_and_allow_fail))
        ParallelHandler.new_screenshots = bool(int(new_screenshots))

        # debug mode for local docker browsers like chrome and firefox
        # 0 | 1 - =1 enable vnc server on browser docker containers (port :5600 chrome, :5601 firefox)
        ParallelHandler.debug = bool(int(debug))

        # Depending on domain (local|staging|k8s) we reset
        # -> browser
        # -> caching
        # -> force_dump
        if not self.is_local_stack():
            # We only run for the first given browser
            # no parallel execution
            self.browsers = [self.browsers[0]]
            self.caching = False
            self.create_new_dumps_hard = False

        # klick-tipp/test/e2e
        ParallelHandler.curr_dir = os.path.abspath(os.path.join(os.path.realpath(__file__), ".."))

        # klick-tipp/test/e2e/tests
        ParallelHandler.tests_dir = os.path.join(ParallelHandler.curr_dir, "tests")

        # klick-tipp/test/e2e/tests/recordings
        ParallelHandler.recordings_dir = os.path.join(ParallelHandler.tests_dir, "recordings")

        # Holds general json file with recording info, .sql cache files
        # /klick-tipp/test/e2e/tests/recordings/test_add_subscriber_1651644923/
        # or could also be repoReady : .../recordings/repo/..../test_add_subscriber_1651644923
        # glob should match only one tc folder, otherwise something is wrong
        tc_folders = glob.glob(f"{ParallelHandler.recordings_dir}/**/{ParallelHandler.test_case}",
                               recursive=True)
        if not tc_folders or (tc_folders and tc_folders == []):
            print(f"Test case not found : {ParallelHandler.recordings_dir}/**/{ParallelHandler.test_case}")
            sys.exit(1)

        ParallelHandler.tc_dir = tc_folders[0]

        ParallelHandler.relative_tc_parent_folder = re.search(r"recordings\/(.*?)\/?" + ParallelHandler.test_case,
                                                              ParallelHandler.tc_dir).group(1)

        # /klick-tipp/test/e2e/tests/recordings/test_add_subscriber_1651644923/test_add_subscriber_1651644923.json
        ParallelHandler.tc_json_file = os.path.join(
            ParallelHandler.tc_dir, f"{ParallelHandler.test_case}.json"
        )

        # e2e-test-tool/record_replay_mvp
        ParallelHandler.branch = self.get_branch()

        # Here (under /local) we place the created python script
        # Additionally, we need that name for the test set path creation below
        ParallelHandler.tc_exec_py_package_name = "test_recording_temp"

        # '1234'
        ParallelHandler.tc_id = int(re.match(r'.+?_([0-9]+)$', test_case).group(1))

        # /home/<USER>/pr/klick-tipp/test/e2e/tests/testsets
        ParallelHandler.test_set_dir = os.path.join(ParallelHandler.tests_dir, "testsets")

        # recording_temp
        ParallelHandler.test_set = "recording_temp"

        # /home/<USER>/pr/klick-tipp/test/e2e/tests/testsets/recording_temp
        # e.g. content: test_recording_temp/local/test_create_formular_6666.py
        ParallelHandler.test_set_file = os.path.join(ParallelHandler.test_set_dir, ParallelHandler.test_set)

        # Depending on domain (local or not) it has appended 'local' or not
        # /klick-tipp/test/e2e/tests/test_recording_temp (/local)
        ParallelHandler.temp_exec_py_package_dir = self.reset_temp_exec_py_package_dir()

        # Define the test set content
        # e.g. test_recording_temp/local/test_create_formular_6666.py
        test_set_content = ParallelHandler.temp_exec_py_package_dir
        test_set_content += f"/{ParallelHandler.test_case}.py"

        if os.path.exists(ParallelHandler.test_set_file):
            os.remove(ParallelHandler.test_set_file)

        with open(ParallelHandler.test_set_file, "x") as test_set_file_fh:
            test_set_file_fh.write(test_set_content)

        # test script filepath
        ParallelHandler.tc_script_path = os.path.join(
            ParallelHandler.temp_exec_py_package_dir,
            f"{ParallelHandler.test_case}.py",
        )

        # [1234  # dep to execute first (origin)
        #  3999  # next
        #  9949] # last, after this dep, the actual tc can start
        ParallelHandler.dependency_chain = dependencies.get_dependencies(ParallelHandler.tc_id)

        ParallelHandler.all_dep_infos = {}
        for dep_id in ParallelHandler.dependency_chain:
            dep_info = dependencies.get_dependency_info_json(dep_id)
            ParallelHandler.all_dep_infos[dep_id] = dep_info

        with open(ParallelHandler.tc_json_file, "r") as tc_info_fh:
            ParallelHandler.tc_info = json.load(tc_info_fh)

        deps_and_tc_in_order = ParallelHandler.dependency_chain
        deps_and_tc_in_order.append(ParallelHandler.tc_id)
        ParallelHandler.tc_ids_in_order = deps_and_tc_in_order
        ParallelHandler.deps_and_tc_infos = ParallelHandler.all_dep_infos
        ParallelHandler.deps_and_tc_infos[ParallelHandler.tc_id] = ParallelHandler.tc_info

        # List with Test case names in order
        ParallelHandler.tc_names_in_order = []
        for info_json in ParallelHandler.deps_and_tc_infos.keys():
            ParallelHandler.tc_names_in_order.append(ParallelHandler.deps_and_tc_infos[info_json]["machineName"])

        tc_code = script_creator.get_script_code(deps_and_tc_in_order, ParallelHandler.deps_and_tc_infos, ParallelHandler.new_screenshots)
        with open(ParallelHandler.tc_script_path, "x") as script_path_fh:
            script_path_fh.write(tc_code)

    def reset_temp_exec_py_package_dir(self):
        # temp execution package dir
        # /home/<USER>/pr/klick-tipp/test/e2e/tests/test_recording_temp
        temp_folder_name = "test_recording_temp"
        temp_py_package_dir = os.path.join(ParallelHandler.tests_dir, temp_folder_name)
        if os.path.exists(temp_py_package_dir):
            # double-check rmtree!
            if re.search(re.escape(temp_folder_name), temp_py_package_dir):
                shutil.rmtree(temp_py_package_dir)

        os.mkdir(temp_py_package_dir)
        temp_py_init_file_path = os.path.join(temp_py_package_dir, "__init__.py")
        init_file_content = """import sys
import os
sys.path.insert(0, os.path.abspath('..'))
"""
        with open(temp_py_init_file_path, "x") as temp_init_fh:
            temp_init_fh.write(init_file_content)
        temp_py_package_dir = os.path.join(temp_py_package_dir, "local")
        os.mkdir(temp_py_package_dir)
        temp_py_init_file_path = os.path.join(temp_py_package_dir, "__init__.py")
        with open(temp_py_init_file_path, "x") as temp_init_fh:
            temp_init_fh.write(init_file_content)
        return temp_py_package_dir

    def get_dump_file_path(self, test_case):
        tc_cache_dir = os.path.join(ParallelHandler.recordings_dir,
                                    self.get_relative_tc_parent_folder(test_case),
                                    test_case, "cache")
        return os.path.join(tc_cache_dir, self.get_dump_file_name())

    def get_dump_file_name(self):
        # branch: [DEV-999]e2e-test-tool/record_replay_mvp
        # filename: __DEV-999__e2e-test-tool__record_replay_mvp.sql
        filename_friendly = re.sub(r'[' + re.escape("/[]().") + r']', "__", ParallelHandler.branch)
        return f"{filename_friendly}.sql"

    def get_cache_infos(self, test_case):
        tc_cache_dir = os.path.join(ParallelHandler.recordings_dir,
                                    self.get_relative_tc_parent_folder(test_case),
                                    test_case, "cache")
        tc_cache_infos_file = os.path.join(tc_cache_dir, "cache_infos.json")
        cache_info = None
        if os.path.exists(tc_cache_infos_file):
            with open(tc_cache_infos_file, "r") as cache_info_fh:
                cache_info = json.load(cache_info_fh)

        return cache_info

    def is_ci_context(self) -> bool:
        return os.getenv("CI_JOB_NAME") is not None

    def get_branch(self):
        path = ParallelHandler.curr_dir
        command = 'git rev-parse --abbrev-ref HEAD'.split()
        branch = subprocess.Popen(command, stdout=subprocess.PIPE, cwd=path).stdout.read()
        branch = branch.strip().decode('utf-8')
        # In CI above git command returns HEAD
        if branch.lower() == "head":
            branch = os.getenv("CI_COMMIT_REF_SLUG", "HEAD")
        print(f"branch: {branch}")
        return branch

    def is_local_stack(self):
        if self.app_location == "local":
            return True
        else:
            return False

    def prepare_caching_and_exec_info(self,
                                      caching: bool) -> bool:
        cache_manager = CacheManager(self)
        if ParallelHandler.create_new_dumps_hard:
            if not cache_manager.delete_all_dumps_and_info_for_test_cases():
                print("Deletion all dumps and caching infos of test cases failed.")
                return False
        ParallelHandler.caching_states = cache_manager.collect_caching_states()
        caching_states_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, "caching_states.json")
        with open(caching_states_filepath, "x") as cache_states_fh:
            cache_states_fh.write(json.dumps(ParallelHandler.caching_states, indent=2))
        print(json.dumps(ParallelHandler.caching_states, indent=2))
        print("\n")

        ParallelHandler.cache_exec_info = cache_manager.create_exec_info_json(caching_states=ParallelHandler.caching_states,
                                                                              include_last=ParallelHandler.prepare,
                                                                              caching=caching)
        self.write_cache_exec_info()
        return True

    def write_cache_exec_info(self):
        cache_exec_info_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, "cache_exec_info.json")
        with open(cache_exec_info_filepath, "x") as cache_exec_info_fh:
            cache_exec_info_fh.write(json.dumps(ParallelHandler.cache_exec_info, indent=2))
        print(json.dumps(ParallelHandler.cache_exec_info, indent=2))

    def collect_exec_info(self) -> dict:
        infos = {"appLocation": ParallelHandler.app_location,
                 "domain": ParallelHandler.domain,
                 "browsers": ParallelHandler.browsers,
                 "execUser": ParallelHandler.exec_user,
                 "users": ParallelHandler.users,
                 "browserUserMap": ParallelHandler.browser_user_mapping,
                 "testCase": ParallelHandler.test_case,
                 "prepare": ParallelHandler.prepare,
                 "forceNewDumps": ParallelHandler.create_new_dumps_hard,
                 "caching": ParallelHandler.caching,
                 "browserstackUser": ParallelHandler.browserstack_user,
                 "browserstackToken": ParallelHandler.browserstack_token,
                 "debug": ParallelHandler.debug}
        return infos

    def write_exec_info(self):
        exec_info_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, "exec_info.json")
        with open(exec_info_filepath, "x") as exec_info_fh:
            exec_info_fh.write(json.dumps(ParallelHandler.exec_info, indent=2))
        print(json.dumps(ParallelHandler.exec_info, indent=2))

    def log_dataprep_failure(self, user, reason):
        """Log the failure of the data preparation."""
        dataprep_failure_json = {
            "state": "error",
            "testResult": "dataprep_failed",
            "reason": f"user dataprep failed for {user} because {reason}"
        }
        tc_exec_state_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, f"{user}_dataprep_failure.json")
        with open(tc_exec_state_filepath, "w+") as fh:
            json.dump(dataprep_failure_json, fh, indent=2)

    def call_app_data_create_for_user(self, user) -> bool:
        # ToDo: call api endpoint on domain
        # id : <test id>:<timestamp before the first api call>
        # Post:
        # {
        #     id: <string>,
        #     user: <string>,
        #     app-data-creation: <string[]>
        # }
        #
        # Response:
        # id: <same id as passed, just for reference>
        # state: <string> "COMPLETE" | "PROGRESS" | "ERROR"
        # message: <string> // only on error == error description

        # Remove any existing data-prep-failed information
        dataprep_failure_path = os.path.join(ParallelHandler.temp_exec_py_package_dir, f"{user}_dataprep_failure.json")
        if os.path.exists(dataprep_failure_path):
            os.remove(dataprep_failure_path)

        print("Call app data creation calls on Tonys endpoint.")

        # 1st tc holds the app data creation infos
        tc_id = ParallelHandler.tc_ids_in_order[0]
        first_tc = ParallelHandler.deps_and_tc_infos[tc_id]
        app_data_creation_infos = first_tc["data"]
        timestamp = re.sub(r'\.[0-9]+', '', str(datetime.now().timestamp()))
        call_id = str(tc_id) + ":" + timestamp
        data = {"id": call_id,
                "user": user,
                "data": app_data_creation_infos}

        # in docker host context
        endpoint_data_creation = f"https://{ParallelHandler.domain}/e2e/api/prepare.json"
        post_header = {'Content-Type': "application/json"}

        if self.is_ci_context() and self.is_local_stack():
            print("CI Context und app location 'local' (Caching möglich mit e2e stack).")
            endpoint_data_creation = f"https://127.0.0.1:20443/e2e/api/prepare.json"

        print(f"Endpoint data creation : {endpoint_data_creation}")
        print(f"Post header : {post_header}")
        print(f"User: {user}")
        print(f"data: {data}")

        # Assume we have state=='PROGRESS'
        progress_retries = 10
        for progress_retry in range(progress_retries):
            print(f"Retry {progress_retry + 1} of {progress_retries} for user {user}.")
            time.sleep(20)
            try:
                resp = requests.post(endpoint_data_creation,
                                     data=json.dumps(data),
                                     headers=post_header,
                                     verify=False)
            except Exception as e:
                if progress_retry == progress_retries - 1:
                    traceback.print_exc()
                    print(e)
                    self.log_dataprep_failure(user, f"Exception sending POST request to Endpoint: {endpoint_data_creation}: {e}")
                    return False
                continue

            try:
                print(f"Response status code: {resp.status_code}")
                if resp.status_code != 200:
                    try:
                        http_error_reason = resp.reason
                    except:
                        http_error_reason = "No error reason in response object"
                    print(f"Error reason: {http_error_reason}")
                    self.log_dataprep_failure(user, f"Error: {http_error_reason}")
                    continue

                response_json = resp.json()

                state = response_json.get('state', '').upper()
                print(f"Response state: {state}")
                if state == "COMPLETE":
                    print(f"App data creation completed for user {user}.")
                    return True
                elif state == "ERROR":
                    self.log_dataprep_failure(user, response_json.get('message', 'Error message for tonys endpoint not found'))
                    return False
                elif state == "PROGRESS":
                    print(f"App data creation in progress for user {user}.")
                    continue
                else:
                    self.log_dataprep_failure(user, f"Unknown state returned by Tony Endpoint: {state}")
                    return False
            except Exception as e:
                print("No valid response object from Tony endpoint.")
                self.log_dataprep_failure(user, "No valid response object returned by POST to tonys endpoint.")
                continue
        print("Reached max number of retries when calling Tony dataprep endpoint.")
        return False


    def app_data_create_for_not_imported(self) -> bool:
        if self.caching:
            exec_info_tc_1 = ParallelHandler.cache_exec_info[ParallelHandler.tc_names_in_order[0]]
            for user_to_exec in ParallelHandler.users:
                exec_flag = exec_info_tc_1[user_to_exec]["execution"]
                if exec_flag:
                    if not self.call_app_data_create_for_user(user_to_exec):
                        print("Failed: App data creation (php) for user " + user_to_exec)
                        return False
        else:
            for user_to_exec in ParallelHandler.users:
                if not self.call_app_data_create_for_user(user_to_exec):
                    print("Failed: App data creation (php) for user " + user_to_exec)
                    return False
        return True

    def import_dump(self, dump_path):
        """Pass a .sql file path."""
        import_cmd_res = self.call_process_in_kt_container(
            f"Import dump : {dump_path}",
            f"drush --verbose -v sql-cli < {dump_path}",
        )
        if not import_cmd_res:
            print(f"Dump import failed! {dump_path}")
            return False
        return True

    def get_abs_tc_path(self, tc_name) -> str:
        tc_folders = glob.glob(f"{ParallelHandler.recordings_dir}/**/{tc_name}",
                               recursive=True)
        return tc_folders[0]

    def get_relative_tc_parent_folder(self, tc_name) -> str:
        relative_tc_parent_folder = re.search(r"recordings\/(.*?)\/?" + tc_name,
                                              self.get_abs_tc_path(tc_name)).group(1)
        return relative_tc_parent_folder

    def import_cached_users(self, tc_name) -> bool:
        print("Import cached users.")
        # workdir in klicktipp container is kt root.
        recordings_dir_in_kt_container = os.path.join("test", "e2e", "tests", "recordings")
        tc_cache_dir_in_kt_container = os.path.join(recordings_dir_in_kt_container,
                                                    self.get_relative_tc_parent_folder(tc_name),
                                                    tc_name, "cache")
        dump_path = os.path.join(tc_cache_dir_in_kt_container, self.get_dump_file_name())
        if not self.import_dump(dump_path):
            return False
        return True

    def call_process_in_kt_container(self, comment_on_cmd, cmd_string) -> bool:
        """Execute e.g. drush commands inside kt container.
    
        comment_on_cmd : describe your call
        cmd_string : Your command string e.g. drush --verbose ....
        """

        # container started on docker for mac have no underscores in their name
        compose_project_name = os.getenv("COMPOSE_PROJECT_NAME")
        if compose_project_name and self.is_ci_context():
            container_name = f"{compose_project_name}-klicktipp-1"
        else:
            container_name = "klick-tipp-e2e-klicktipp-1"
        subproc_return = None
        try:
            subproc_return = subprocess.run(
                f"docker exec -w /srv/www/klicktipp/htdocs {container_name} bash --login -c '{cmd_string}'",
                shell=True,
            )
            print(f"Done : {comment_on_cmd}")
        except Exception as e:
            print(f"Failed system call : {comment_on_cmd} :: {cmd_string}")
            traceback.print_exc()
            print(e)
            return False

        if subproc_return.returncode != 0:
            print(f"Failed system call : {comment_on_cmd} :: {cmd_string}")
            return False

        return True

    def dump_into_sql_file(self, sql_file_path) -> bool:
        """Pass a .sql file path."""
        cmd: str = "drush cache-clear all"
        if not self.call_process_in_kt_container("Clear cache after update",
                                                 cmd):
            return False
        cmd = f"drush --db-url=mysql://root:klicktipp@percona/klicktipp sql:dump --extra=--no-tablespaces --verbose -v --result-file={sql_file_path}"
        if not self.call_process_in_kt_container("Export db as : " + sql_file_path,
                                                 cmd):
            return False
        cmd = "chmod a+w " + sql_file_path
        if not self.call_process_in_kt_container("Sql dump created by kt root user. Chown for docker host : " + sql_file_path,
                                                 cmd):
            return False
        return True

    def delete_dump(self, test_case) -> bool:
        # workdir in klicktipp container is kt root.
        recordings_dir_in_kt_container = os.path.join("test", "e2e", "tests", "recordings")
        tc_cache_dir_in_kt_container = os.path.join(recordings_dir_in_kt_container,
                                                    self.get_relative_tc_parent_folder(test_case),
                                                    test_case, "cache")
        dump_path = os.path.join(tc_cache_dir_in_kt_container, self.get_dump_file_name())
        if not self.call_process_in_kt_container("Delete dump : " + dump_path,
                                                 "rm -f " + dump_path + " || echo \"Not able to delete the dump!\""):
            return False
        return True

    def dump(self, test_case, tc_results: json) -> bool:
        tc_cache_dir = os.path.join(ParallelHandler.recordings_dir,
                                    self.get_relative_tc_parent_folder(test_case),
                                    test_case, "cache")
        cache_info_file = os.path.join(tc_cache_dir, "cache_infos.json")
        tc_caching_info = {}
        if os.path.exists(tc_cache_dir):
            if os.path.exists(cache_info_file):
                with open(cache_info_file, "r") as cache_info_tc_fh:
                    tc_caching_info = json.load(cache_info_tc_fh)
        else:
            os.mkdir(tc_cache_dir)

        print("caching info")
        print(tc_caching_info)
        # branch caching infos
        if tc_caching_info and tc_caching_info != {}:
            tc_branch_caching_info = tc_caching_info.get(ParallelHandler.branch)
            if not tc_branch_caching_info:
                tc_branch_caching_info = {"dump": self.get_dump_file_name(),
                                          "users": []}
            print("non empty caching info")
            print(tc_branch_caching_info)
        else:
            tc_branch_caching_info = {"dump": self.get_dump_file_name(),
                                      "users": []}
            print("empty caching info")
            print(tc_branch_caching_info)

        # Add new passed browsers to list of browsers which are cached
        users_cached_in_curr_db = []
        for browser in tc_results[test_case].keys():
            # user connected to browser
            user = self.browser_user_mapping[browser]
            users_cached_in_curr_db = tc_branch_caching_info.get("users")
            if users_cached_in_curr_db:
                # at least one user in "users"
                if user not in users_cached_in_curr_db:
                    users_cached_in_curr_db.append(user)
            else:
                users_cached_in_curr_db.append(user)

        # Remove browsers which are no longer cached.
        # We need to do this here, if current test case is not the imported one.
        #    example:
        #              execution was for chrome/firefox
        #              and
        #              the current test case cache info tells me, that chrome/firefox/safari_macos were cached
        #              Then: safari_macos would only be valid for caching, if the current test, was also the imported dump.
        #              Reason: If current test is not the imported one, the test execution started with a previous dependency/test,
        #                      but the current was obviously not executed for the safari_macos. So safari_macos is on the finish state of the previous test,
        #                      which we don't want to keep as valid dump for the current test finish. So we remove the safari_macos from current cached browser list.
        #              Remark: safari_macos users data is for the current dump on the previous dependency/test. That is no problem. If it would be the first test case in
        #                      dependency list, the dataprep routine (Tony) would reset that user in case of part of the browsers to execute.
        #
        # ToDo
        # Letzter Gedanke:
        #                  Wenn imported wurde, aber der aktuelle tc nicht der imported ist,
        #                  kann ich ohne weiteres dumpen und als neue cached browser list, die aktuelle browser list benutzen
        #
        #                  Was ist, wenn ich der erste tc bin
        #                  es wurde nicht importiert, aber caching info hat browser drin, die nicht teil der testausführung sind/waren
        #
        # if condition below passed, we need to remove old cached browser info (browser name from cache list)
        # -> in short : new cache list is list of browsers executed
        if self.imported_test_case:
            print("imported : " + self.imported_test_case + "\n")
            print("curr TC  : " + test_case + "\n")
        # users_cached_in_curr_db now contains:
        # -> current run users + old users cached (From previous runs)
        if self.imported_test_case and test_case != self.imported_test_case:
            # we have imported AND we have here finished a test case AFTER the import
            # Only set currently executed users as cached
            # Yes, there is still data present of the users coming with the import (previous test case), BUT
            # those users were not executed currently, and so their state is not on the current Test Case!
            tc_branch_caching_info["users"] = ParallelHandler.users
        else:
            # We are evaluating the imported test case here (test_case = imported test_case)
            tc_branch_caching_info["users"] = users_cached_in_curr_db
        #########################

        # tc_branch_caching_info["users"] = users_cached_in_curr_db

        tc_caching_info[ParallelHandler.branch] = tc_branch_caching_info

        with open(cache_info_file, "w+") as cache_info_tc_fh:
            json.dump(tc_caching_info,
                      cache_info_tc_fh,
                      indent=2)

        # workdir in klicktipp container is kt root.
        recordings_dir_in_kt_container = os.path.join("test", "e2e", "tests", "recordings")
        tc_cache_dir_in_kt_container = os.path.join(recordings_dir_in_kt_container,
                                                    self.get_relative_tc_parent_folder(test_case),
                                                    test_case, "cache")
        dump_path = os.path.join(tc_cache_dir_in_kt_container, self.get_dump_file_name())
        if not self.dump_into_sql_file(dump_path):
            return False
        return True

    def remove_executor_and_browser_containers(self):
        for browser in ParallelHandler.browsers:
            os.system("cd " + ParallelHandler.curr_dir + "; make clean-browser-instances BROWSER=" + browser)
            os.system("cd " + ParallelHandler.curr_dir + "; make clean-script-executors BROWSER=" + browser)

    def get_proc_info(self, browser, test_case):
        proc_iter = psutil.process_iter(attrs=["pid", "name", "cmdline"])
        for p in proc_iter:
            cmdline = p.info["cmdline"]
            pytest_call = False
            browser_name = ""
            for i in range(len(cmdline)):
                cmd_part = cmdline[i]
                if cmd_part.lower() == "browsername":
                    browser_name = cmdline[i+1]
                if re.search(test_case, cmd_part):
                    print("Found test_case")
                    print(cmdline)
                    if re.search(r'pytest', cmdline[1]):
                        print("Found pytest")
                        pytest_call = True
                        cmdline_oneliner = ' '.join(cmdline)
                        print(cmdline_oneliner)

            if pytest_call and browser_name:
                if browser_name == browser:
                    return p.info["pid"]

        return None

    def wait_for_test_containers_online(self, timeout):
        # Maybe the drupal image needs to be fetched from AWS
        num_of_executor_starts = 0
        num_of_expected_executor_starts = len(ParallelHandler.browsers)
        self.executors = {}
        print(f"num_of_executor_starts         : {num_of_executor_starts}")
        print(f"num_of_expected_executor_starts: {num_of_expected_executor_starts}")
        for i in range(timeout):
            print(f"browsers {ParallelHandler.browsers}")
            for browser in ParallelHandler.browsers:
                test_executor_name = self.executors.get(browser)
                print(f"1 test_executor_name {test_executor_name}")
                if not test_executor_name:
                    self.add_executor_name_to_exec_info(browser)
                    print(f"1 browser in executor list? {self.executors.get(browser)}")

                    if self.executors.get(browser):
                        print(f"1 increase num of executor_starts")
                        num_of_executor_starts += 1
                        print(f"1 now {num_of_executor_starts}")
            if num_of_executor_starts == num_of_expected_executor_starts:
                break
            time.sleep(1)

        if num_of_executor_starts == num_of_expected_executor_starts:
            return True
        return False

    def wait_for_pytest_started_all_browsers(self, timeout):
        # Maybe the drupal image needs to be fetched from AWS
        num_of_pytest_starts = 0
        num_of_expected_starts = len(ParallelHandler.browsers)
        for i in range(timeout):
            for browser in ParallelHandler.browsers:
                browser_proc_pid = self.pids.get(browser)
                if not browser_proc_pid:
                    self.add_pid_to_exec_info(browser)
                    if self.pids.get(browser):
                        num_of_pytest_starts += 1
            if num_of_pytest_starts == num_of_expected_starts:
                break
            time.sleep(1)

        if num_of_pytest_starts == num_of_expected_starts:
            return True
        return False

    def add_pid_to_exec_info(self, browser):
        print("Search for " + browser + " " + self.test_case)
        pid = self.get_proc_info(browser, self.test_case)
        print("!!!!!!!!!!!!!!111 FOUND PIT")
        self.pids[browser] = pid
        ParallelHandler.cache_exec_info["pids"] = self.pids

    def add_executor_name_to_exec_info(self, browser):
        client = docker.from_env()
        # 'klick-tipp-e2e-test-chrome_tester_run_25be005b1feb'
        try:
            for container in client.containers.list():
                if re.search(re.escape(browser) + r'-tester-run', container.name):
                    self.executors[browser] = container.name
            ParallelHandler.cache_exec_info["executors"] = self.executors
        except:
            pass

    def get_unfinished_tests(self, test_case):
        # firefox.test_create_tag_1651563386
        # testcase name test_create_tag_1651563386
        unfinished = []
        for browser in ParallelHandler.browsers:
            browser_user = ParallelHandler.browser_user_mapping[browser]
            browser_tc_exec_state = self.get_browser_tc_exec_state(browser_user, test_case)
            if browser_tc_exec_state:
                state = browser_tc_exec_state.get("state")
                if not state or state.lower() != "finished":
                    unfinished.append(browser)
            else:
                unfinished.append(browser)
        return unfinished

    def get_browser_tc_exec_state(self, browser, test_case):
        test_run_info_files = glob.glob(ParallelHandler.temp_exec_py_package_dir + "/*." + test_case)
        tc_exec_state_filename = browser + "." + test_case
        tc_exec_state_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, tc_exec_state_filename)
        if tc_exec_state_filepath in test_run_info_files:
            try:
                with open(tc_exec_state_filepath, "r") as fh:
                    return json.load(fh)
            except:
                pass
        return None

    def kill_unfinished_test_execution_procs(self, unfinished_browsers):
        # Get browsers represent in both lists
        browsers = list(set(self.pids.keys()) & set(unfinished_browsers))
        for browser in browsers:
            pid = self.pids.get(browser)
            print("Pid " + str(pid))
            for i in range(3):
                try:
                    print("kill " + str(pid))
                    os.kill(pid, signal.SIGINT)
                except:
                    pass
                time.sleep(0.2)

    def kill_unfinished_test_executor_containers(self, unfinished_browsers):
        # Get browsers represent in both lists
        client = docker.from_env()
        browsers = list(set(self.executors.keys()) & set(unfinished_browsers))
        print("executor containers to kill : ")
        print(browsers)
        for browser in browsers:
            container = None
            container_name = self.executors.get(browser)
            try:
                container = client.containers.get(container_name)
            except:
                # Container no more exists? Or docker problem.
                pass

            if container:
                print("Kill Container " + container_name)
                container.remove(force=True)

    def kill_unfinished_test_execution_procs(self, unfinished_browsers):
        # Get browsers represent in both lists
        browsers = list(set(self.pids.keys()) & set(unfinished_browsers))
        for browser in browsers:
            pid = self.pids.get(browser)
            print("Pid " + str(pid))
            for i in range(3):
                try:
                    print("kill " + str(pid))
                    os.kill(pid, signal.SIGINT)
                except:
                    pass
                time.sleep(0.2)

    def update_unfinished_state_files(self, test_case, unfinished):
        # firefox.test_create_formular_1651562892
        # {
        #     "state": "finished",
        #     "testResult": "skipped",
        #     "reason": "imported"
        # }
        for browser in unfinished:
            tc_exec_state_filename = browser + "." + test_case
            tc_exec_state_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, tc_exec_state_filename)
            if os.path.exists(tc_exec_state_filepath):
                os.remove(tc_exec_state_filepath)

            exec_state = {
                "state": "finished",
                "testResult": "error",
                "reason": "Timed out and killed"
            }
            with open(tc_exec_state_filepath, "w+") as fh:
                json.dump(exec_state, fh, indent=2)

    def write_results_for_all_test_cases(self, result_json: json):
        # e.g.
        # {
        #     "state": "finished",
        #     "testResult": "skipped",
        #     "reason": "previous test timed out and killed"
        # }
        reached_tc_with_timeouts = False
        for tc in ParallelHandler.tc_names_in_order:
            # only update if a test case after the current timed out one
            for browser in ParallelHandler.browsers:
                tc_exec_state_filename = browser + "." + tc
                tc_exec_state_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, tc_exec_state_filename)

                with open(tc_exec_state_filepath, "w+") as fh:
                    json.dump(result_json, fh, indent=2)

    def write_result_files_for_followup_test_cases(self, test_case, unfinished):
        reached_tc_with_timeouts = False
        for tc in ParallelHandler.tc_names_in_order:
            if tc == test_case:
                reached_tc_with_timeouts = True

            # only update if a test case after the current timed out one
            if reached_tc_with_timeouts and tc != test_case:
                for browser in unfinished:
                    tc_exec_state_filename = browser + "." + tc
                    tc_exec_state_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, tc_exec_state_filename)

                    exec_state = {
                        "state": "finished",
                        "testResult": "skipped",
                        "reason": "Previous test timed out and killed"
                    }
                    with open(tc_exec_state_filepath, "w+") as fh:
                        json.dump(exec_state, fh, indent=2)

    def exec_tests(self):
        # Save general run information in test case package dir
        ParallelHandler.exec_info = self.collect_exec_info()
        self.write_exec_info()
        if not self.prepare_caching_and_exec_info(self.caching):
            print("Prepare caching and execution info failed.")
            return False
        if self.is_local_stack():
            if ParallelHandler.cache_exec_info["import"] != {}:
                self.imported_test_case = ParallelHandler.cache_exec_info["import"]["testCase"]
                if not self.import_cached_users(self.imported_test_case):
                    print("Failed to import cached users for : " + self.imported_test_case)
                    return False

            # After the import we create the app data for users which were not imported by the dump,
            # Necessary to start with the first test in execution chain.
            # The 1st test is the one test, without any tc dependency. (Only dependent on pre-created user data)
            if not self.app_data_create_for_not_imported():
                return False
        else:
            # On non-local test deployments, we always create the necessary data before we start with the test.
            if not self.call_app_data_create_for_user(ParallelHandler.exec_user):
                print(f"Failed: App data creation (php) for user {ParallelHandler.exec_user}")
                return False

        # We don't need to execute any test, if:
        #  imported dump == last test Case
        #    and
        #  there is no browser we need to execute a test for (all relevant imported)
        execution_necessary = True
        if self.is_local_stack() and self.imported_test_case:
            last_tc = self.tc_names_in_order[-1]
            print("Imported : " + self.imported_test_case)
            print("Last TC  : " + last_tc)
            print(self.cache_exec_info)
            if self.imported_test_case == last_tc:
                # Find out, do we have any execution?
                user_for_imported_tc = self.cache_exec_info[self.imported_test_case]
                for user in user_for_imported_tc.keys():
                    execution_necessary = execution_necessary and user_for_imported_tc[user]["execution"]

        if not execution_necessary:
            print("No execution necessary. Every browser/user to exec was imported. No html reports created.")
            self.write_results_for_all_test_cases({
                "state": "finished",
                "testResult": "skipped",
                "reason": "No execution necessary. Every browser/user to exec was imported."
            })
            return True
        else:
            any_fail = False  # somehow we get return codes from this script, even if we have skipped but no failed tests: check on our own.

            # Remove all old script executors and browser containers
            self.remove_executor_and_browser_containers()

            # Trigger the test case processes
            procs = []
            for browser in ParallelHandler.browsers:
                if ParallelHandler.compare_and_allow_fail:
                    make_target = "tests-e2e-screen-tests-fail-ignore"
                elif ParallelHandler.compare_and_fail:
                    make_target = "tests-e2e-screen-tests"
                elif ParallelHandler.new_screenshots:
                    make_target = "tests-e2e-screen-tests"  # To create new screenshots, we need at least one of the two comparison modes above.
                else:
                    make_target = "tests-e2e"

                make_command = "make " + make_target + \
                               " E2E_RECORD_REPLAY=1" + \
                               " BROWSER=" + browser + \
                               " TESTSET=" + ParallelHandler.test_set
                is_browserstack_browser = True
                if re.match(r'^(chrome|firefox)$', browser):
                    is_browserstack_browser = False

                browserstack_local = False
                if self.is_local_stack():
                    if is_browserstack_browser:
                        browserstack_local = True
                klicktipp_url = ParallelHandler.domain

                make_command += " KLICKTIPP_URL=" + klicktipp_url
                make_command += " BROWSERSTACK_LOCAL=" + str(int(browserstack_local))

                if is_browserstack_browser:
                    make_command += " BROWSERSTACK_USER=" + self.browserstack_user
                    make_command += " BROWSERSTACK_TOKEN=" + self.browserstack_token

                if self.exec_user and self.exec_user != "" and self.exec_user != "none":
                    make_command += " EXEC_USER=" + self.exec_user

                make_command += " DEBUG=" + str(int(self.debug))
                make_command += " CACHING=" + str(int(self.caching))
                if pipeline_execution_context():
                    print("Pipeline context -> Publish test in zephyr and skip test if deactivated via zephyr.")
                    make_command += " PUBLISH_IN_TM4J=1"

                log_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, browser + ".log")
                print(make_command)
                with open(log_filepath, "x") as fh:
                    p = subprocess.Popen(make_command,
                                         shell=True,
                                         stdout=fh,
                                         stderr=subprocess.STDOUT)

                    procs.append(p)

            # wait for pytest containers start in seconds
            # if self.wait_for_pytest_started_all_browsers(45):
            startup_timeout = 5*60

            if self.wait_for_test_containers_online(startup_timeout):
                print("All pytest containers up and running")
            else:
                print("Not all pytest containers started within timeout of " + str(startup_timeout) + " sec.")
                print("Please retry - Maybe slow internet connection while docker image pulling.")
                print("Especially drupal images or similar are very big data bobbles.")
                for p in procs:
                    os.kill(p.pid, signal.SIGINT)
                # kill ALL browser containers, which were already started
                self.remove_executor_and_browser_containers()
                self.write_results_for_all_test_cases({
                    "state": "finished",
                    "testResult": "error",
                    "reason": "Timed out during ramp up phase. Maybe slow internet connection while docker image pulling."
                })
                return False

            test_case_results = {}
            for test_case in ParallelHandler.tc_names_in_order:
                browser_results = {}
                browsers_finished = {}
                test_case_results[test_case] = {}
                num_passed = 0
                at_least_one_executed_test = False
                start_time = datetime.now()
                while True:
                    # Test for timeout
                    # Max time for a single subprocess/test 240 sec.
                    curr_time = datetime.now()
                    delta = curr_time - start_time
                    # Especially browserstack connections could be slower than expected.
                    # Normally a tc should be finished in max 90 seconds. But selenium command
                    # transmission to browserstack and back through local tunnel, can cost time.
                    if hasattr(delta, "seconds") and delta.seconds > 6*60:
                        print("DELTA : " + str(delta.seconds))
                        # test_case = test_create_tag1651563386
                        # unfinished : ['chrome', 'firefox', ...
                        unfinished = self.get_unfinished_tests(test_case)
                        # self.kill_unfinished_test_execution_procs(unfinished)
                        self.kill_unfinished_test_executor_containers(unfinished)
                        self.update_unfinished_state_files(test_case, unfinished)  # firefox.test_create_formular_1651562892
                        self.write_result_files_for_followup_test_cases(test_case, unfinished)  # Set all followup tc results as skipped

                    test_run_info_files = glob.glob(ParallelHandler.temp_exec_py_package_dir + "/*." + test_case)
                    if len(test_run_info_files) == len(ParallelHandler.browsers):
                        num_of_finished = 0
                        for info_file in test_run_info_files:
                            try:
                                print(f"info_file       : {info_file}")
                                with open(info_file, 'r') as info_file_handle:
                                    info = json.loads(info_file_handle.read())
                                if info["state"].lower() == "finished":
                                    num_of_finished += 1
                                print(f"num_of_finished : {num_of_finished}")
                                browser = re.match(r'.+/(.+)\.' + test_case, info_file).group(1)
                                print(f"browser : {browser}")
                                browser_results[browser] = info
                                print(f"testResult :  {info['testResult']}")
                                if info["testResult"] == "passed" or (info["testResult"] == "skipped" and (info["reason"] == "imported" or re.search(re.escape('Skipped via zephyr'), info["reason"]) or re.search(re.escape('previous test skipped'), info["reason"]))):
                                    print("testResult is passed or skipped with good reason")
                                    if info.get("reason"):
                                        print(f"reason :  {info['reason']}")
                                    print("browsers finished:")
                                    print(browsers_finished)
                                    if browser not in browsers_finished:
                                        print("Browser not in browsers_finished")
                                        print("Increase num_passed")
                                        num_passed += 1
                                        browsers_finished[browser] = True
                                    if info["testResult"] == "passed":
                                        print("info[testResult]==passed")
                                        print("set at_least_one_executed_test=True")
                                        at_least_one_executed_test = True
                                elif info["state"].lower() == "finished":
                                    # none of the finished results in the if-case matched, so we are here, and can assume, something is wrong
                                    any_fail = True
                            except:
                                pass
                        if num_of_finished == len(ParallelHandler.browsers):
                            break
                    time.sleep(0.2)
                print("browser_results\n")
                print(browser_results)

                test_case_results[test_case] = browser_results
                print(test_case + " Finished for all Browsers!\n")
                if self.is_local_stack() and self.caching:
                    print(f"num browsers to exec : {len(ParallelHandler.browsers)}")
                    print(f"num_passed           : {num_passed}\n")

                    # num_passed also includes imported/skipped users
                    if num_passed == len(ParallelHandler.browsers) and at_least_one_executed_test and not (info["testResult"] == "skipped" and (re.search(re.escape('Skipped via zephyr'), info["reason"]) or re.search(re.escape('previous test skipped'), info["reason"]))):
                        print("Dump - all passed and min. 1 executed test\n")
                        if not self.dump(test_case, test_case_results):
                            print("Dump failed!")
                            return False
                    else:
                        print("No Dump - not all tests have passed or not at least 1 test was executed or we skipped, because of zephyr deactivated test.\n")

                # tell pytest threads, that all parallel tests have been finished, data was dumped (local case), and the next test case can start
                mark_all_finished_filepath = os.path.join(ParallelHandler.temp_exec_py_package_dir, test_case + ".Finished")
                print("!!!!!!!!!!!!!!!!! FINISHED !!!!!!!!!!!!!!!!!!!!!!")
                os.system("touch " + mark_all_finished_filepath)

            for p in procs:
                p.wait()

        print("All test cases, if there were dependend tests (test chain), are finished.")
        if any_fail:
            print("Some test failed. (skipped tests are not failed tests!)")
            return False
        else:
            print("Only skips or passes. No Fails.")
            return True


class CacheManager(object):
    paral_handler: ParallelHandler = None
    caching_states: json = None
    exec_cache_info: json = None

    def __init__(self, paral_handler: ParallelHandler):
        """Init some instance attributes."""
        CacheManager.paral_handler = paral_handler
        # branch
        # browsers
        # test_case_order
        # recordings_dir
        # /home/<USER>/pr/klick-tipp/test/e2e/tests/recordings/test_create_tag_3333/cache/cache_infos.json
        # /home/<USER>/pr/klick-tipp/test/e2e/tests/recordings/test_create_tag_3333/cache/e2e-test-tool__record_replay_mvp.sql
        # execution_dir
        # /home/<USER>/pr/klick-tipp/test/e2e/tests/test_recording_temp/local/cache_exec_info.json

    def delete_all_dumps_and_info_for_test_cases(self) -> bool:
        cache_state = {}

        for test_case in self.paral_handler.tc_names_in_order:
            cache_info_file = os.path.join(self.paral_handler.recordings_dir,
                                           self.paral_handler.get_relative_tc_parent_folder(test_case),
                                           test_case, "cache", "cache_infos.json")

            if not os.path.exists(cache_info_file):
                cache_state[test_case] = {}
                continue

            with open(cache_info_file, "r") as cache_info_file_fh:
                cache_info_json = json.load(cache_info_file_fh)

            branch_cache_info = cache_info_json.get(self.paral_handler.get_branch())
            if not branch_cache_info:
                continue
            del cache_info_json[self.paral_handler.get_branch()]

            if not self.paral_handler.delete_dump(test_case):
                print("Dump deletion for test case failed : " + test_case)
                return False

            with open(cache_info_file, "w+") as cache_info_tc_fh:
                json.dump(cache_info_json,
                          cache_info_tc_fh,
                          indent=2)
        return True

    def create_exec_info_json(self,
                              caching_states: json,
                              include_last: bool,
                              caching: bool) -> json:
        # find out, if we can cache, and which hits are most and latest in execution queue
        # depending on, if we prepare for recording (include_last = true) or if we skip the last test from caching,
        # because the last one should be executed.
        last_tc = self.paral_handler.tc_names_in_order[-1]
        num_users = len(self.paral_handler.users)
        last_hits = 0
        for test_case in self.paral_handler.tc_names_in_order:
            cache_state = caching_states.get(test_case)
            if cache_state != {}:
                hits = cache_state.get("cacheHits")
                if test_case != last_tc:
                    if hits >= last_hits:
                        most_cache_hits = test_case
                        last_hits = hits
                else:
                    # last test case in order
                    if include_last:
                        if hits >= last_hits:
                            most_cache_hits = test_case
                            last_hits = hits

        # if no hits, ensure, that we don't import the last TC in the dependency chain!
        # We import the first TC
        # - make tony data creation for new user(s)
        # - keep imported cached users and re-dump (without change of course) in case of new users success
        # - then you will have the old imported users in 1st TC dump + the new users (if all were successful finished)
        if last_hits == 0:
            most_cache_hits = self.paral_handler.tc_names_in_order[0]
        # We only import test case dump, if ALL browsers we want to execute for, were cached
        #  OR
        # we import the 1st test case, if there is at least one browser cached
        tc_we_can_import = None
        import_with_all_browsers_cached = False
        if num_users == last_hits:
            import_with_all_browsers_cached = True
            tc_we_can_import = most_cache_hits
        else:
            first_tc = self.paral_handler.tc_names_in_order[0]
            if caching_states.get(first_tc) != {}:
                first_tc_cache_hits = caching_states.get(first_tc).get("cacheHits")
                num_old_cached_in_first_tc = len(caching_states.get(first_tc).get("usersCached"))
                if first_tc_cache_hits > 0 or num_old_cached_in_first_tc > 0:
                    if last_tc == first_tc:
                        if include_last:
                            tc_we_can_import = first_tc
                    else:
                        tc_we_can_import = first_tc

        # i have set most_cache_hits to test case name with most cache hits
        # if not set most_cache_hits, there wasn't any cached browser we need
        users_imported = []
        users_not_imported = []
        if tc_we_can_import:
            # list of the cached browsers (we want to execute), which we get with the import sql
            for user in self.paral_handler.users:
                if user in caching_states.get(tc_we_can_import)["usersCached"]:
                    users_imported.append(user)
                else:
                    users_not_imported.append(user)

        exec_cache_info = {}
        import_tc_evaluated = False

        for test_case in self.paral_handler.tc_names_in_order:
            exec_cache_info[test_case] = {}
            # Do we import?
            if caching and tc_we_can_import:
                # Test Cases after the import
                if import_tc_evaluated:
                    for user in self.paral_handler.users:
                        exec_cache_info[test_case][user] = {"execution": True}
                else:
                    # Test Case before the TC we import
                    if not import_tc_evaluated and test_case != tc_we_can_import:
                        for user in self.paral_handler.users:
                            exec_cache_info[test_case][user] = {"execution": False}

                    # Test Case we import
                    if not import_tc_evaluated and test_case == tc_we_can_import:
                        for user in self.paral_handler.users:
                            if user in users_imported:
                                exec_cache_info[test_case][user] = {"execution": False}
                            else:
                                exec_cache_info[test_case][user] = {"execution": True}
                            import_tc_evaluated = True
            else:
                # No import -> All browsers need to be executed
                for user in self.paral_handler.users:
                    exec_cache_info[test_case][user] = {"execution": True}

        if caching and tc_we_can_import:
            exec_cache_info["import"] = {"testCase": tc_we_can_import,
                                         "importedUsers": users_imported,
                                         "notImportedUsers": users_not_imported,
                                         "dump": caching_states[tc_we_can_import]["dump"]
                                         }
        else:
            exec_cache_info["import"] = {}

        CacheManager.exec_cache_info = exec_cache_info
        return exec_cache_info

    def collect_caching_states(self) -> json:
        """{'test_create_tag_3333':
               {'UsersExec': ['e2e-test-replay-chrome', 'e2e-test-replay-safari_macos', 'e2e-test-replay-edge'],
                'usersCached': ['e2e-test-replay-safari_macos', 'e2e-test-replay-edge'],
                'cacheHits': 2},
            'test_create_formular_6666':
               {'usersExec': ['e2e-test-replay-chrome', 'e2e-test-replay-safari_macos', 'e2e-test-replay-edge'],
               'usersCached': ['e2e-test-replay-chrome'],
               'cacheHits': 1}}
        """
        cache_state = {}

        for test_case in self.paral_handler.tc_names_in_order:
            cache_info_file = os.path.join(self.paral_handler.recordings_dir, test_case, "cache", "cache_infos.json")

            if not os.path.exists(cache_info_file):
                cache_state[test_case] = {}
                continue

            with open(cache_info_file, "r") as cache_info_file_fh:
                cache_info_json = json.load(cache_info_file_fh)

            branch_cache_info = cache_info_json.get(self.paral_handler.get_branch())
            if not branch_cache_info:
                cache_state[test_case] = {}
                continue
            cache_hit = sum(el in self.paral_handler.users for el in branch_cache_info.get("users"))

            cache_state[test_case] = {"usersExec": self.paral_handler.users,
                                      "usersCached": branch_cache_info.get("users"),
                                      "cacheHits": cache_hit,
                                      "dump": branch_cache_info.get("dump")}
        CacheManager.caching_states = cache_state
        return cache_state


def pipeline_execution_context():
    """Return true, when test is running in gitlab pipeline (Check CI_* env vars)."""
    return (os.getenv("CI_PIPELINE_ID") is not None and
            (os.getenv("CI_COMMIT_BRANCH") is not None or
             os.getenv("CI_COMMIT_TAG") is not None))


retry = bool(int(arg_retry))
retries = 0
max_retries = 1
while True:
    parallel_handler = ParallelHandler(arg_app_location,
                                       arg_domain,
                                       arg_browsers,
                                       arg_test_case,
                                       arg_prepare,
                                       arg_force_new_dumps,
                                       arg_caching,
                                       arg_exec_user,
                                       arg_exec_user_pwd,
                                       arg_browserstack_user,
                                       arg_browserstack_token,
                                       arg_compare_and_fail,
                                       arg_compare_and_allow_fail,
                                       arg_new_screenshots,
                                       arg_debug
                                       )

    passed = parallel_handler.exec_tests()
    if not passed:
        if retry:
            if retries < max_retries:
                retries += 1
                print(f"Trigger the retry number #{retries} of the test execution (max retries #{max_retries})")
                continue
            print(f"Max retries executed : #{max_retries}")
            sys.exit(1)
        sys.exit(1)
    sys.exit(0)

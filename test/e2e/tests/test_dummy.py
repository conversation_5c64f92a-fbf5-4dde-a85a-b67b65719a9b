# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""Selenium based tests for end-2-end."""

import pytest

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import image_compare_helper as ih


# Uncomment decorator for marking as xfail (expexted fail -> TDD)
# @pytest.mark.xfail
@pytest.mark.incremental  # Set test result to xfail and skip, when previous test was failing
@pytest.mark.order1  # When you have tests, which depend on each other, you can define a order
def test_dummy_1(base_url):
    """Use Case - kt open."""
    kh.open_kt(base_url)

    ih.screen_test("smoke_test",
                   "before_login", {"1366x768": [(168, 85, 1186, 709)],
                                    "2732x2048": [(339, 283, 2424, 2047)]})


@pytest.mark.incremental(if_failed='test_frontpage')
@pytest.mark.order2
def test_dummy_2(base_url):
    """Use Case - kt login."""
    kh.open_kt_and_login(base_url, account="e2e-test-dev")

    ih.screen_test("smoke_test",
                   "after_login", {"1366x768": [(168, 85, 1186, 709)],
                                   "2732x2048": [(339, 283, 2424, 2047)]})


__author__ = "<PERSON>rling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

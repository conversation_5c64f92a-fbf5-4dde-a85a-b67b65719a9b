ARG DIND_BASE_IMAGE_PATH
FROM $DIND_BASE_IMAGE_PATH

ARG DOCKER_HOST_DOCKER_GROUP
ARG DOCKER_HOST_USR
ARG DOCKER_HOST_USR_NAME
ARG DOCKER_HOST_USR_GROUP_NAME
ARG DOCKER_HOST_USR_GROUP
ARG DOCKER_USERNAME

RUN echo "ARG DOCKER_HOST_DOCKER_GROUP   = ${DOCKER_HOST_DOCKER_GROUP}"
RUN echo "ARG DOCKER_HOST_USR            = ${DOCKER_HOST_USR}"
RUN echo "ARG DOCKER_HOST_USR_NAME       = ${DOCKER_HOST_USR_NAME}"
RUN echo "ARG DOCKER_HOST_USR_GROUP_NAME = ${DOCKER_HOST_USR_GROUP_NAME}"
RUN echo "ARG DOCKER_HOST_USR_GROUP      = ${DOCKER_HOST_USR_GROUP}"
RUN echo "ARG DOCKER_USERNAME            = ${DOCKER_USERNAME}"

LABEL name="Base image for end-2-end selenium test script executor" Version="1.0.0"

# ToDo: parallel
# On MacOS users are sometimes in gid 20 (staff).
# This is the dialout group id.
# To allow re-assign of gid below to gid 20
#          we need to change the gid for dialout
RUN groupmod -g 9000 dialout

### In some docker images we could have the situation, that the ID we want to assign to the docker group inside
#   the docker container, is already used by another group of the system. In that situation we need to re-assign
#   a new GID to the group which is holding the needed group ID.
ARG NEW_GID=898

# Find the group with the GID that we want to use for the docker group, and change its GID to NEW_GID
RUN existing_group=$(getent group ${DOCKER_HOST_DOCKER_GROUP} | cut -d: -f1) && \
    if [ -n "$existing_group" ]; then \
        # If the group exists, change the group id to NEW_GID
        groupmod -g ${NEW_GID} $existing_group; \
    fi

# We get permission problems when docker group ID is different from
# our group ID inside the docker container
RUN groupmod -g ${DOCKER_HOST_DOCKER_GROUP} docker || echo "No valid group : ${DOCKER_HOST_DOCKER_GROUP}"

RUN \
if [ "${DOCKER_USERNAME}" = "executor_user" ] ; then \
echo we use executor_user ; \
groupadd -r -g 1000 executor_user ; \
adduser -u 1000 --gid 1000 --disabled-password --gecos "" --shell /bin/sh executor_user \
 && mkdir /home/<USER>/bin ; \
usermod -u ${DOCKER_HOST_USR} executor_user ; \
groupmod -g ${DOCKER_HOST_USR_GROUP} executor_user ; \
find /home -user 1000 -exec chown -h ${DOCKER_HOST_USR} {} \; ; \
find /home -group 1000 -exec chgrp -h ${DOCKER_HOST_USR_GROUP} {} \; ; \
usermod -g ${DOCKER_HOST_USR_GROUP} executor_user ; \
else \
echo we use the root user ; \
fi

RUN usermod -aG docker ${DOCKER_USERNAME}

USER ${DOCKER_USERNAME}

CMD pip3 freeze

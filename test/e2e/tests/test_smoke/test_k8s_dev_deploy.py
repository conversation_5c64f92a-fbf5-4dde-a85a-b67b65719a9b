# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2024, Klick-Tipp
# Email: <EMAIL>
"""Local e2e smoke test. Runs against local KT compose stack."""

import time

import pytest
from flaky import flaky

from tests.helpers import bee_editor_helper as beeh
from tests.helpers import klick_tipp_helper as kh
from tests.helpers import image_compare_helper as ih
from tests.helpers.image_compare_helper import ScreenMask
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains

from tests.helpers import test_data as td


@pytest.fixture(scope='module', autouse=False)
def login_and_save_session_cookie(get_webdriver_for_setting_session_cookie, base_url, standard_e2e_test_dev_user,
                                  standard_e2e_test_dev_pwd):
    """Just overwrite for test data setup module."""
    pass


@pytest.mark.incremental
@pytest.mark.order1
def test_login(base_url, record_replay_user_pwd):
    """Use Case - kt login."""
    kh.open_kt_and_login(base_url, account="e2e-test-recorder", pwd=record_replay_user_pwd)


__author__ = "Simon Werling"
__copyright__ = "Copyright 2024, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

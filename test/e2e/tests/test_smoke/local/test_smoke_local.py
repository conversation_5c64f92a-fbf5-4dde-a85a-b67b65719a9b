# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2024, Klick-Tipp
# Email: <EMAIL>
"""Local e2e smoke test. Runs against local KT compose stack."""

import time

import pytest
from flaky import flaky

from tests.helpers import bee_editor_helper as beeh
from tests.helpers import klick_tipp_helper as kh
from tests.helpers import image_compare_helper as ih
from tests.helpers.image_compare_helper import ScreenMask
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains

from tests.helpers import test_data as td


@pytest.fixture(scope='module', autouse=False)
def login_and_save_session_cookie(get_webdriver_for_setting_session_cookie, base_url, standard_e2e_test_dev_user,
                                  standard_e2e_test_dev_pwd):
    """Just overwrite for test data setup module."""
    pass

# Uncomment decorator for marking as xfail (expected fail -> TDD)
# @pytest.mark.xfail
@pytest.mark.incremental  # Set test result to xfail and skip, when previous test was failing
@pytest.mark.order1  # When you have tests, which depend on each other, you can define a order
def test_frontpage(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Use Case - kt open."""
    # ToDo: Dirty example for special test data load. Put in flexible fixture!
    td.import_testdata_sql('ready_for_test/standard_import_configured.sql')
    kh.open_kt(base_url)

@pytest.mark.incremental(if_failed='test_frontpage')
@pytest.mark.order2
def test_login(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Use Case - kt login."""
    td.import_testdata_sql('ready_for_test/standard_import_configured.sql')
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

@pytest.mark.incremental(if_failed='test_login')
@pytest.mark.order3
def test_logout(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Use Case - kt logout."""
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("My Account", "Logout")

    kh.sh.click_by_xpath("Expect the logout confirm screen and a logout button", "//input[@id='kc-logout']")
    time.sleep(2)
    kh.sh.pass_xpath_exists("Test for login button on frontpage after logout.", "//*[@id='kc-login']")

@pytest.mark.incremental(if_failed='test_login')
@pytest.mark.order4
def test_create_tag(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Create a manual tag."""
    td.import_testdata_sql('ready_for_test/standard_import_configured.sql')
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Automation", "Tags", "Create Tag")

    kh.sh.send_string_to_input("//input[@data-e2e-id='entityName-input']", "First manual tag")

    kh.sh.click_by_xpath("Save manual tag", "//button[@data-e2e-id='tagCreateBtn']")

@pytest.mark.incremental(if_failed='test_login')
@pytest.mark.order5
def test_create_anmeldeformular(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Create Anmeldeform with defaults."""
    td.import_testdata_sql('ready_for_test/standard_import_configured.sql')
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Listbuilding", "Listbuilding", "Create Listbuilding")

    # click | id=edit-100 |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-100']")

    # click | id=edit-name |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-name']")

    # type | id=edit-name | sometest
    kh.sh.send_string_to_input("//*[@id='edit-name']", 'sometest')

    # click | id=edit-submit |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-submit']")

    # click | id=ms-input-0 |
    kh.sh.click_by_xpath("step x", "//*[@id='ms-input-0']")

    # type | id=ms-input-0 | mylabel
    kh.sh.send_string_to_input("//*[@id='ms-input-0']", 'mylabel')
    kh.sh.send_string_to_input("//*[@id='ms-input-0']", Keys.RETURN)

    # submit | id=klicktipp-list-forms-custom-edit-form |
    kh.sh.pass_xpath_exists("step x", "//*[@id='klicktipp-list-forms-custom-edit-form']").submit()

    # click | id=ms-input-1 |
    kh.sh.click_by_xpath("step x", "//*[@id='ms-input-1']")

    # type | id=ms-input-1 | sometag
    kh.sh.send_string_to_input("//*[@id='ms-input-1']", 'sometag')
    kh.sh.send_string_to_input("//*[@id='ms-input-1']", Keys.RETURN)

    # submit | id=klicktipp-list-forms-custom-edit-form |
    kh.sh.pass_xpath_exists("step x", "//*[@id='klicktipp-list-forms-custom-edit-form']").submit()

    # select | id=edit-customfields-customfieldselect | label=Mobile Phone
    kh.sh.click_by_xpath("step x", "//select[@id='edit-customfields-customfieldselect']/option[text()='Mobile Phone']")

    # click | id=edit-customfields-customfieldselect |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-customfields-customfieldselect']")

    # click | //div[@id='edit-formstyle-wrapper']/div/div[2]/div[9]/div[2]/span |
    kh.sh.click_by_xpath("step x", "//div[@id='edit-formstyle-wrapper']/div/div[2]/div[9]/div[2]/span")

    # click | id=edit-settings-colors-chicklet-gradient-button |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-settings-colors-chicklet-gradient-button']")

    # click | //div[4]/div[2]/div/div[2]/div/div/div |
    kh.sh.click_by_xpath("step x", "//div[4]/div[2]/div/div[2]/div/div/div")

    # click | //div[4]/div[2]/div/div[2]/div[3] |
    kh.sh.click_by_xpath("step x", "//div[4]/div[2]/div/div[2]/div[3]")

    # click | //div[4]/div[2]/div/div[2]/div/div/div |
    kh.sh.click_by_xpath("step x", "//div[4]/div[2]/div/div[2]/div/div/div")

    # click | id=edit-save |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-save']")

@pytest.mark.incremental(if_failed='test_login')
@pytest.mark.order6
def test_automation_menu_1(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Use Case - Test some stuff in the automation menu."""
    td.import_testdata_sql('ready_for_test/standard_import_configured.sql')
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Campaigns", "Campaigns")
    kh.click_menu_item("Campaigns", "Campaigns", "Create Campaign")
    kh.click_menu_item("Campaigns", "Overview BAM Automation Templates")
    kh.click_menu_item("Campaigns", "Campaign Templates")
    kh.click_menu_item("Campaigns", "Campaign Templates", "Create Campaign Template")

    # click | id=edit-name |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-name']")

    # type | id=edit-name | Some automation template
    kh.sh.send_string_to_input("//*[@id='edit-name']", 'Some automation template')

    # click | id=ms-sel-ctn-0 |
    kh.sh.click_by_xpath("step x", "//*[@id='ms-sel-ctn-0']")

    # type | id=ms-input-0 | automation-template-label
    kh.sh.send_string_to_input("//*[@id='ms-input-0']", 'automation-template-label')
    kh.sh.send_string_to_input("//*[@id='ms-input-0']", Keys.RETURN)

    # submit | id=klicktipp-tools-template-create-form |
    kh.sh.pass_xpath_exists("step x", "//*[@id='klicktipp-tools-template-create-form']").submit()

    # click | id=edit-selectedcampaign |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-selectedcampaign']")

    # click | id=edit-submit |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-submit']")

    # click | id=edit-cancel |
    kh.sh.click_by_xpath("step x", "//*[@id='edit-cancel']")

    kh.click_menu_item("Campaigns", "E-Mails / SMS")
    kh.click_menu_item("Campaigns", "E-Mails / SMS", "Create Campaign E-Mail")

    # click | //button[@" + kh.dataE2eId + "='editor-select-btn-dnd'] |
    kh.sh.click_by_xpath("Choose richtext editor", "//button[@" + kh.dataE2eId + "='editor-select-btn-rt']")

    name_edit = kh.sh.pass_xpath_clickable("Get the field for name", "//*[@" + kh.dataE2eId + "='entityName-input']")
    name_edit.send_keys('some automation mail')

    # click | data-e2e-id=entityLabels-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']")

    # type | data-e2e-id=entityLabels-chip-list-input | some label
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", 'some label')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.RETURN)

    # type | data-e2e-id=entityLabels-chip-list-input |some other label
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", 'some other label')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.RETURN)

    # click | id=edit-notes |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityNotesEnabled']")

    # type | id=edit-notes | some notes for may mail
    textarea = kh.sh.pass_xpath_exists("Get notes area", "//*[@" + kh.dataE2eId + "='entityNotes-input']")
    textarea.send_keys('some notes for may mail')

    # click | data-e2e-id=createBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='createBtn']")

    # click | data-e2e-id=extra-settings-panel | open extra settings panel
    kh.sh.click_by_xpath("step x", "//mat-accordion[@" + kh.dataE2eId + "='extra-settings-panel']")

    # click | //div[@id='edit-useascrmtemplate-wrapper']/label |
    kh.sh.click_by_xpath("step x", "//mat-checkbox[@" + kh.dataE2eId + "='cbEmailTemplate']")

    # click | //div[@id='edit-useasinvoicetemplate-wrapper']/label |
    kh.sh.click_by_xpath("step x", "//mat-checkbox[@" + kh.dataE2eId + "='cbBillDraft']")

    # click | id=edit-fromname |
    from_name_input = kh.sh.pass_xpath_exists("Get notes area", "//*[@" + kh.dataE2eId + "='senderName-input']")
    from_name_input.clear()
    from_name_input.send_keys(' e2e-test-dev')

    # select | id=edit-signature | label=Default Signature
    kh.sh.click_by_xpath("Open select by click", "//mat-select[@" + kh.dataE2eId + "='signatureFormatSelect-select']")
    kh.sh.click_by_xpath("Get option to connect with formular", "//mat-option[@" + kh.dataE2eId + "='signatureFormatSelect-select-option-1']")

    # click | data-e2e-id=saveBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='saveBtnEdit']")

    # click | data-e2e-id=email detail affix |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='emailDetailAffix-item-editor']")

    subject_input = kh.sh.pass_xpath_exists("step x", "//input[@" + kh.dataE2eId + "='smart-subject-input']")
    subject_input.send_keys('some subject')

    time.sleep(5)
    ck_editor_div = kh.sh.pass_xpath_exists("step x", "//ckeditor//div//div//div//div")
    ck_editor_div.send_keys('some content')

    # click | data-e2e-id=saveBtnRichText |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='saveBtnRichText']")

    # click | data-e2e-id=email_editor_automation_successlink_email_analysis |
    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='email_editor_automation_successlink_email_analysis']")

    # click | data-e2e-id=email_editor_automation_successlink_email_analysis |
    kh.sh.click_by_xpath("step x", "//input[@" + kh.dataE2eId + "='preview-email-magic-select-chip-list-input']")

    # click | data-e2e-id=emailEditorBtn |
    kh.sh.click_by_xpath("step x", "//button[@" + kh.dataE2eId + "='emailEditorBtn']")

    # click | data-e2e-id=emailDetailAffix-item-statistics |
    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='emailDetailAffix-item-statistics']")

    kh.click_menu_item("Campaigns", "E-Mails / SMS", "Create Campaign SMS")
    kh.click_menu_item("Campaigns", "E-Mails / SMS", "Create Notification E-Mail")
    kh.click_menu_item("Campaigns", "E-Mails / SMS", "Create Notification SMS")

    kh.click_menu_item("Campaigns", "Newsletter", "Create E-Mail Newsletter")

    # click | //mat-radio-button[@data-e2e-id='editorType-option-1'] |
    kh.sh.click_by_xpath("Choose rich text editor", "//button[@" + kh.dataE2eId + "='editor-select-btn-rt']")

    # click | data-e2e-id=entityName-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityName-input']")

    # type | data-e2e-id=entityName-input | some newsletter
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityName-input']", 'some newsletter')

    # click | data-e2e-id=entityLabels-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']")

    # type | data-e2e-id=entityLabels-chip-list-input | newletter-label
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", 'newletter-label')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.RETURN)

    # click | data-e2e-id=selectedTaggedWithMS-chip-list |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list']")

    # type | data-e2e-id=selectedTaggedWithMS-chip-list | sometag
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list']", 'sometag')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list']", Keys.ESCAPE)

    # click | data-e2e-id=selectedNotTaggedWithMS-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']")

    # type | data-e2e-id=selectedNotTaggedWithMS-chip-list-input | anothertag
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list-input']")
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']", 'anothertag')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']", Keys.ESCAPE)

    # click | data-e2e=cbSplittest |
    kh.sh.click_by_xpath("step x", "//mat-checkbox[@" + kh.dataE2eId + "='cbSplittest']")

    # click | data-e2e-id=createBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='createBtn']")

    # to give Angular time to refresh view use a small timeout, or it will try to click on a stale/deprecated element
    time.sleep(2)
    # click | link=campaignAffix-item-1 | => Settings
    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='campaignAffix-item-edit']")

    # click | //*[@data-e2e-id='splittestSlider-slider'] |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='splittestSlider-slider']")

    # select | data-e2e-id=splitTestDurationSelect-select | label=Days
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='splitTestDurationSelect-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='splitTestDurationSelect-select-option-1']")

    # click | data-e2e-id=saveBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='saveBtn']")

    # to give Angular time to refresh view use a small timeout, or it will try to click on a stale/deprecated element
    time.sleep(2)
    # click | link=campaignAffix-item-0 | => view
    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='campaignAffix-item-statistics']")

    kh.click_menu_item("Campaigns", "Newsletter")

    # select | data-e2e-id=statusSelect-select | label=Outbox (0)
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='statusSelect-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='statusSelect-select-option-2']")

    kh.click_menu_item("Campaigns", "Newsletter", "Create SMS Newsletter")

    # click | data-e2e-id=entityName-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityName-input']")

    # type | data-e2e-id=entityName-input | some newsletter
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityName-input']", 'some sms newsletter')

    # click | data-e2e-id=entityLabels-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']")

    # type | data-e2e-id=entityLabels-chip-list-input | newletter-label
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", 'labelforsmsnewletter')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.ESCAPE)

    # click | data-e2e-id=selectedTaggedWithMS-chip-list |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list']")

    # type | data-e2e-id=selectedTaggedWithMS-chip-list | hurray
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list']", 'hurray')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list']", Keys.ESCAPE)

    # click | data-e2e-id=selectedNotTaggedWithMS-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']")

    # type | data-e2e-id=selectedNotTaggedWithMS-chip-list-input | wohooo
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']", 'wohooo')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']", Keys.ESCAPE)

    # click | for=cbSplittest |
    kh.sh.click_by_xpath("step x", "//mat-checkbox[@" + kh.dataE2eId + "='cbSplittest']")

    # click | data-e2e-id==testDuration-input |
    kh.sh.click_by_xpath("step x", "//input[@" + kh.dataE2eId + "='testDuration-input']")

    # type | data-e2e-id==testDuration-input |
    kh.sh.send_string_to_input("//input[@" + kh.dataE2eId + "='testDuration-input']", 2, True)

    # select | data-e2e-id=splitTestDurationSelect-select | label=Months
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='splitTestDurationSelect-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='splitTestDurationSelect-select-option-2']")

    # select | data-e2e-id=splitTestWinnerSelect-select | label=The version with the most unique conversions
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='splitTestWinnerSelect-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='splitTestWinnerSelect-select-option-2']")

    # click | data-e2e-id=createBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='createBtn']")

    # to give Angular time to refresh view use a small timeout, or it will try to click on a stale/deprecated element
    time.sleep(2)
    # click | link=campaignAffix-item-0 | view
    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='campaignAffix-item-statistics']")

@pytest.mark.incremental(if_failed='test_login')
@pytest.mark.order7
def test_automation_menu_2(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Use Case - Test some stuff in the automation menu."""
    td.import_testdata_sql('ready_for_test/standard_import_configured.sql')
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Campaigns", "Follow-Up Campaigns")
    kh.click_menu_item("Campaigns", "Follow-Up Campaigns", "Create Follow-Up E-Mail")

    # click | //mat-radio-button[@data-e2e-id='editorType-option-1'] |
    kh.sh.click_by_xpath("Choose rich text editor", "//button[@" + kh.dataE2eId + "='editor-select-btn-rt']")

    # click | data-e2e-id=entityName-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityName-input']")

    # type | data-e2e-id=entityName-input | some newsletter
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityName-input']", 'some autoresponder')

    # click | data-e2e-id=entityLabels-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']")

    # type | data-e2e-id=entityLabels-chip-list-input | responderlabel
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", 'responderlabel')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.ESCAPE)

    # select | data-e2e-id=delaySelect-select | label=Days later
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='delayTimeOptions-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='delayTimeOptions-select-option-4']")

    # click | data-e2e=weekdaysCheckbox |
    kh.sh.click_by_xpath("step x", "//mat-checkbox[@" + kh.dataE2eId + "='weekdaysCheckbox']")

    # click | data-e2e-id=weekdays-1 |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='weekdays-1']")

    # click | data-e2e-id=delayValue-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='delayValue-input']")

    # type | data-e2e-id=edit-delayValue-input | 2
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='delayValue-input']", 2, True)

    # click | id=specificTimeCheckbox-input |
    kh.sh.click_by_xpath("step x", "//mat-checkbox[@" + kh.dataE2eId + "='specificTimeCheckbox']")

    # select | data-e2e-id=datepickerselect-hours-select | label=16
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='autoresponderDatepickerselect-hours-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='autoresponderDatepickerselect-hours-select-option-22']")

    # select | data-e2e-id=datepickerselect-min-select | label=15
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='autoresponderDatepickerselect-min-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='autoresponderDatepickerselect-min-select-option-6']")

    # select | data-e2e-id=taggedPreSelect-select | label=Contact has ALL of these tags
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='taggedPreSelect-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='taggedPreSelect-select-option-1']")

    # click | data-e2e-id=selectedTaggedWithMS-chip-list |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list-input']")

    # type | data-e2e-id=selectedTaggedWithMS-chip-list | some
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list-input']", 'some')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list-input']", Keys.RETURN)

    # click | data-e2e-id=selectedTaggedWithMS-chip-list | anothertag
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list-input']")
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list-input']", 'anothertag')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list-input']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list-input']", Keys.ESCAPE)

    # select | data-e2e-id=notTaggedPreSelect-select | label=Contact doesn't have ALL of these tags
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='notTaggedPreSelect-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='notTaggedPreSelect-select-option-1']")

    # INFO this test will fail as no untag after send is set
    # click | data-e2e-id=multipleSendCheckbox-input |
    kh.sh.click_by_xpath("step x", "//mat-checkbox[@" + kh.dataE2eId + "='multipleSendCheckbox']")

    # click | data-e2e-id=createBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='createBtn']")

    # to give Angular time to refresh view use a small timeout, or it will try to click on a stale/deprecated element
    time.sleep(2)
    # click | link=campaignAffix-item-1 | Settings
    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='campaignAffix-item-edit']")

    # to give Angular time to refresh view use a small timeout, or it will try to click on a stale/deprecated element
    time.sleep(1)
    # click | link=campaignAffix-item-0 | View
    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='campaignAffix-item-statistics']")

    kh.click_menu_item("Campaigns", "Follow-Up Campaigns")

    # select | data-e2e-id=triggerSelect-select
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='triggerSelect-select']")
    kh.sh.click_by_xpath("step x", "//mat-option/span[text()=' anothertag ']")

    kh.click_menu_item("Campaigns", "Follow-Up Campaigns", "Create Birthday Follow-Up E-Mail")

    # click | //mat-radio-button[@data-e2e-id='editorType-option-1'] |
    kh.sh.click_by_xpath("Choose rich text editor", "//button[@" + kh.dataE2eId + "='editor-select-btn-rt']")

    # click | data-e2e-id=entityName-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityName-input']")

    # type | data-e2e-id=entityName-input | some birthday mail
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityName-input']", 'some birthday mail')

    # click | data-e2e-id=entityLabels-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']")

    # type | data-e2e-id=entityLabels-chip-list-input | birthday mail label
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", 'birthday mail label')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.ESCAPE)

    # select | data-e2e-id=bdayAutoresponderDatepickerselect-hours-select | label=03
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='bdayAutoresponderDatepickerselect-hours-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='bdayAutoresponderDatepickerselect-hours-select-option-3']")

    # select | data-e2e-id=bdayAutoresponderDatepickerselect-min-select | label=20
    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='bdayAutoresponderDatepickerselect-min-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='bdayAutoresponderDatepickerselect-min-select-option-5']")

    # click | data-e2e-id=createBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='createBtn']")

    kh.click_menu_item("Campaigns", "Follow-Up Campaigns", "Create Follow-Up SMS")

    # click | data-e2e-id=entityName-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityName-input']")

    # type | data-e2e-id=entityName-input | sms autoresponder
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityName-input']", 'sms autoresponder')

    # click | data-e2e-id=entityLabels-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']")

    # type | data-e2e-id=entityLabels-chip-list-input | label sms autoresponder
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", 'label sms autoresponder')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.ESCAPE)

    # click | data-e2e-id=selectedNotTaggedWithMS-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']")

    # type | data-e2e-id=selectedNotTaggedWithMS-chip-list-input | sometag
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']", 'sometag')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='selectedNotTaggedWithMS-chip-list-input']", Keys.RETURN)

    # click | data-e2e-id=createBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='createBtn']")

    kh.click_menu_item("Automation", "Countdowns")
    kh.click_menu_item("Automation", "Countdowns", "Create Countdown")

@pytest.mark.incremental(if_failed='test_login')
@pytest.mark.order8
def test_cockpit_1(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Use Case - test some cockpit stuff."""
    td.import_testdata_sql('ready_for_test/standard_import_configured.sql')
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Campaigns", "Campaigns", "Create Campaign")

    # click | data-e2e-id=entityName-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityName-input']")

    # type | data-e2e-id=entityName-input | cockpittest1
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityName-input']", 'cockpittest1')

    # click | data-e2e-id=entityLabels-chip-list-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']")

    # type | data-e2e-id=entityLabels-chip-list-input | cockpittest1label
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", 'cockpittest1label')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.ESCAPE)

    # type | data-e2e-id=entityLabels-chip-list-input | cockpittest1label
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", 'cockpittest1label2')
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.RETURN)
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityLabels-chip-list-input']", Keys.ESCAPE)

    # click | data-e2e-id=createBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='createBtn']")

    kh.sh.set_wait_timeout(15)
    try:
        # click | //main[@id='main-content']/section/section/section/div[2]/div/div[4]/div/h3 |
        kh.sh.click_by_xpath("step x", "//main[@id='main-content']/section/section/section/div[2]/div/div[4]/div/h3")
    except:
        kh.sh.reset_wait_timeout()
        pytest.fail("Cockpit was not loaded in acceptable time of 15 seconds.")
    kh.sh.reset_wait_timeout()

    # click | //input[@type='text'] |
    kh.sh.click_by_xpath("step x", "//input[@type='text']")

    # type | //input[@type='text'] | start condition
    kh.sh.send_string_to_input("//input[@type='text']", 'start condition')
    time.sleep(1.5)

    body = kh.sh.pass_xpath_exists("asdf", "//body")
    body.send_keys(Keys.PAGE_DOWN)
    body.send_keys(Keys.PAGE_UP)
    # click | //div[@id='color']/forms-elements-color-palette/div/div |
    kh.sh.click_by_xpath("step x", "//div[@id='color']/forms-elements-color-palette/div/div")
    time.sleep(1.5)

    # click | //div[@id='color']/forms-elements-color-palette/div/div[2]/div/ul/li[4] |
    kh.sh.click_by_xpath("step x", "//div[@id='color']/forms-elements-color-palette/div/div[2]/div/ul/li[4]")
    time.sleep(1.5)

    body.send_keys(Keys.PAGE_DOWN)
    time.sleep(1.5)
    # click | //div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div/div |
    kh.sh.click_by_xpath("step x", "//div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div/div")

    body.send_keys(Keys.PAGE_DOWN)
    time.sleep(1.5)
    # click | //div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div/div/div |
    kh.sh.click_by_xpath("step x",
                         "//div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div/div/div")
    time.sleep(1.5)

    # click | //div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div/forms-processflow-state-edit-decision-condition/forms-elements-dropdown/div/div/span[2] |
    kh.sh.click_by_xpath("step x",
                         "//div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div/forms-processflow-state-edit-decision-condition/forms-elements-dropdown/div/div/span[2]")
    time.sleep(1.5)

    # click | //div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div/forms-processflow-state-edit-decision-condition/forms-elements-dropdown/div/div[2]/div[2] |
    kh.sh.click_by_xpath("step x",
                         "//div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div/forms-processflow-state-edit-decision-condition/forms-elements-dropdown/div/div[2]/div[2]")
    time.sleep(1.5)

    # click | //div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div[3]/forms-processflow-state-edit-decision-condition/forms-elements-dropdown/div/div/span[2] |
    kh.sh.click_by_xpath("step x",
                         "//div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div[3]/forms-processflow-state-edit-decision-condition/forms-elements-dropdown/div/div/span[2]")
    time.sleep(1.5)

    # click | //div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div[3]/forms-processflow-state-edit-decision-condition/forms-elements-dropdown/div/div[2]/div[3] |
    kh.sh.click_by_xpath("step x",
                         "//div[@id='segments']/forms-processflow-state-edit-decision-segments/div/div[3]/forms-processflow-state-edit-decision-condition/forms-elements-dropdown/div/div[2]/div[3]")
    time.sleep(1.5)

    # click | link=Save |
    kh.sh.click_by_xpath("step x", "//a[text()='Save']")
    time.sleep(1.5)

    # click | //main[@id='main-content']/section/section/section/div[2]/div[3]/div/div |
    kh.sh.click_by_xpath("step x", "//main[@id='main-content']/section/section/section/div[2]/div[3]/div/div")
    time.sleep(1.5)

    # click | //section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[4]/div |
    kh.sh.click_by_xpath("step x",
                         "//section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[4]/div")

@pytest.mark.incremental(if_failed='test_login')
@flaky(max_runs=1)
@pytest.mark.order9
def test_cockpit_2(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Use Case - test some cockpit stuff."""
    td.import_testdata_sql('ready_for_test/standard_import_configured.sql')
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Campaigns", "Campaigns", "Create Campaign")

    # click | data-e2e-id=entityName-input |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='entityName-input']")

    # type | data-e2e-id=entityName-input | some newsletter
    kh.sh.send_string_to_input("//*[@" + kh.dataE2eId + "='entityName-input']", 'cockpittest2')

    # click | data-e2e-id=createBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='createBtn']")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/section/div[2]/div[3]/div/div |
    kh.sh.click_by_xpath("step x", "//main[@id='main-content']/section/section/section/div[2]/div[3]/div/div")

    time.sleep(1.5)
    # click | //section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div/div |
    kh.sh.click_by_xpath("step x",
                         "//section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div/div")

    # click | id=SvgjsSvg1016 |
    # kh.sh.click_by_xpath("step x", "//*[@id='SvgjsSvg1016']")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/section/div[2]/div[2]/div[3]/div/div[2]/div/div |
    kh.sh.click_by_xpath("step x",
                         "//main[@id='main-content']/section/section/section/div[2]/div[2]/div[3]/div/div[2]/div/div")

    time.sleep(1.5)
    # click | //section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[10]/div |
    kh.sh.click_by_xpath("step x",
                         "//section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[10]/div")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/section/div[2]/div[2]/div[4]/div/div[2]/div/div |
    kh.sh.click_by_xpath("step x",
                         "//main[@id='main-content']/section/section/section/div[2]/div[2]/div[4]/div/div[2]/div/div")

    time.sleep(1.5)
    # click | //section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[7]/div |
    kh.sh.click_by_xpath("step x",
                         "//section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[7]/div")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/section/div[2]/div[2]/div[4]/div/div[3]/div/div |
    kh.sh.click_by_xpath("step x",
                         "//main[@id='main-content']/section/section/section/div[2]/div[2]/div[4]/div/div[3]/div/div")

    time.sleep(1.5)
    # click | //section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[18]/div |
    kh.sh.click_by_xpath("step x",
                         "//section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[18]/div")

    # click | //main[@id='main-content']/section/section/section/div[2]/div[2]/div[4]/div/div[2]/div[4]/div/div |
    time.sleep(1.5)
    kh.sh.click_by_xpath("step x",
                         "//main[@id='main-content']/section/section/section/div[2]/div[2]/div[4]/div/div[2]/div[4]/div/div")

    time.sleep(1.5)
    # click | //input[@type='text'] |
    kh.sh.click_by_xpath("step x", "//input[@type='text']")

    time.sleep(1.5)
    # type | //input[@type='text'] | set field
    kh.sh.send_string_to_input("(//input[@type='text'])[1]", 'set field')

    time.sleep(1.5)
    # click | //div[@id='color']/forms-elements-color-palette/div/div |
    # First time i click, the edit field above only left the focus, but the color palette is not yet encapsulated
    kh.sh.click_by_xpath("step x", "//div[@id='color']/forms-elements-color-palette/div/div")
    kh.sh.click_by_xpath("step x", "//div[@id='color']/forms-elements-color-palette/div/div")

    time.sleep(1.5)
    # click | //div[@id='color']/forms-elements-color-palette/div/div[2]/div/ul[2]/li[8] |
    kh.sh.click_by_xpath("step x", "//div[@id='color']/forms-elements-color-palette/div/div[2]/div/ul[2]/li[8]")

    time.sleep(1.5)
    # click | //div[@id='entityID']/forms-elements-entity/div/div/div[2]/div |
    kh.sh.click_by_xpath("step x", "//div[@id='entityID']/forms-elements-entity/div/div/div[2]/div")

    time.sleep(1.5)
    # click | //div[@id='entityID']/forms-elements-entity/div/div[2]/div[2] |
    kh.sh.click_by_xpath("step x", "//div[@id='entityID']/forms-elements-entity/div/div[2]/div[2]")

    time.sleep(1.5)
    # click | link=Save |
    kh.sh.click_by_xpath("step x", "//a[text()='Save']")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/section/div[2]/div[2]/div[3]/div/div[3]/div/div |
    kh.sh.click_by_xpath("step x",
                         "//main[@id='main-content']/section/section/section/div[2]/div[2]/div[3]/div/div[3]/div/div")

    time.sleep(1.5)
    # click | //section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div/div |
    kh.sh.click_by_xpath("step x",
                         "//section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div/div")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/section/div[2]/div[2]/div[3]/div/div[2]/div[3]/div/div[2]/div/div |
    kh.sh.click_by_xpath("step x",
                         "//main[@id='main-content']/section/section/section/div[2]/div[2]/div[3]/div/div[2]/div[3]/div/div[2]/div/div")

    time.sleep(1.5)
    # click | //section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[3]/div |
    kh.sh.click_by_xpath("step x",
                         "//section[@id='app']/kt-router/automation-component/kt-modal/section/div/div/section/div/flowchart-command-add/div/div[3]/div")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/section/div[2]/div[2]/div[3]/div/div[2]/div[3]/div/div/div[4]/div/div |
    kh.sh.click_by_xpath("step x",
                         "//main[@id='main-content']/section/section/section/div[2]/div[2]/div[3]/div/div[2]/div[3]/div/div/div[4]/div/div")

    time.sleep(1.5)
    # click | //input[@type='text'] |
    kh.sh.click_by_xpath("step x", "//input[@type='text']")

    time.sleep(1.5)
    # type | //input[@type='text'] | start
    kh.sh.send_string_to_input("//input[@type='text']", 'start')

    time.sleep(1.5)
    # click | //div[@id='target']/forms-elements-dropdown/div/div/span[2] |
    kh.sh.click_by_xpath("step x", "//div[@id='target']/forms-elements-dropdown/div/div/span[2]")

    time.sleep(1.5)
    # click | //div[@id='target']/forms-elements-dropdown/div/div[2]/div[6]/span[2] |
    kh.sh.click_by_xpath("step x", "//div[@id='target']/forms-elements-dropdown/div/div[2]/div[6]/span[2]")

    time.sleep(1.5)
    # click | link=Save |
    kh.sh.click_by_xpath("step x", "//a[text()='Save']")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/aside/div[3]/ul/li[3] |
    kh.sh.click_by_xpath("step x", "//main[@id='main-content']/section/section/aside/div[3]/ul/li[3]")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/aside/div[3]/ul/li[3] |
    kh.sh.click_by_xpath("step x", "//main[@id='main-content']/section/section/aside/div[3]/ul/li[3]")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/aside/div[3]/ul/li[4] |
    kh.sh.click_by_xpath("step x", "//main[@id='main-content']/section/section/aside/div[3]/ul/li[4]")

    time.sleep(1.5)
    # click | //main[@id='main-content']/section/section/aside/div/a |
    kh.sh.click_by_xpath("step x", "//main[@id='main-content']/section/section/aside/div/a")

@pytest.mark.incremental(if_failed='test_login')
@pytest.mark.order10
def test_new_emaileditor_load_existing(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """
    Use Case - We create some new testdata für new mail editor.

    Based on the standard data set.
    """
    td.import_testdata_sql("ready_for_test/new_emaileditor.sql")

    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Campaigns", "E-Mails / SMS")

    # click | data-e2e-id=table-overview-email-r0-c1 |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='table-overview-email-r0-c1']//a")

    kh.sh.set_wait_timeout(25)

    # click | link=E-Mail Editor |
    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='emailDetailAffix-item-editor']")
    kh.sh.reset_wait_timeout()

    time.sleep(5)
    # Default Content is the main page (Our KT Menu bar frame)
    # Switch back by pytest.selenium_driver.switch_to.default_content()
    pytest.selenium_driver.switch_to.frame(0)

    # Inserted text field in standard template
    kh.sh.pass_xpath_exists("Await some pre created text by data creation.", "//*[contains(text(),'Block')]")

@pytest.mark.incremental(if_failed='test_login')
@pytest.mark.order11
def test_new_emaileditor_create_mail(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """
    Use Case - We create some new testdata für new mail editor.

               Based on the standard data set.
    """
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Campaigns", "E-Mails / SMS")

    mail_name = "new bee email"
    try:
        edit_button = kh.sh.pass_xpath_exists("Try to get the edit button of our mail : '" + mail_name + "'",
                      "//kt-table-text//a[text()='" + mail_name + "']")

        kh.sh.click("Click on email name anchor, then we can delete it.", edit_button)

        kh.sh.click_by_xpath("Click on details affix.", "//a[@" + kh.dataE2eId + "='emailDetailAffix-item-details']")


        kh.sh.click_by_xpath("click delete button.", "//button[@" + kh.dataE2eId + "='deleteBtn']")

        time.sleep(1)

        kh.sh.click_by_xpath("Confirm deletion by click on 'Löschen'", "//button[@" + kh.dataE2eId + "='deleteModal-confirmBtn']")

        kh.wait_modal_dialog_disappear()
    except:
        pass

    kh.click_menu_item("Campaigns", "E-Mails / SMS", "Create Campaign E-Mail")

    # click | //button[@" + kh.dataE2eId + "='editor-select-btn-dnd'] |
    kh.sh.click_by_xpath("Choose new mail editor", "//button[@" + kh.dataE2eId + "='editor-select-btn-dnd']")


    name_edit = kh.sh.pass_xpath_clickable("Get the field for name", "//*[@" + kh.dataE2eId + "='entityName-input']")
    name_edit.send_keys(mail_name)

    # click | data-e2e-id=createBtn |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='createBtn']")

    # click | data-e2e-id=emailDetailAffix-item-editor |
    kh.sh.click_by_xpath("Open DD editor", "//a[@" + kh.dataE2eId + "='emailDetailAffix-item-editor']")


    time.sleep(5)

    # Close Email Template Browser -> created from mother document object (not bee iframe).
    # -> for that reason. Stay and switch after closing browser.
    kh.sh.click_by_xpath("Close template browser", "//div[contains(@class,'modal-base-close')]")
    # Default Content is the main page (Our KT Menu bar frame)
    # Switch back by pytest.selenium_driver.switch_to.default_content()
    pytest.selenium_driver.switch_to.frame(0)

    # Inserted text field in standard template
    kh.sh.pass_xpath_exists("Await some pre created text by data creation.", "//a[text()='Im Browser anzeigen']")
    time.sleep(5)

    ih.screen_test("smoke_test_local",
                   "bee_template_after_mail_creation",
                   {"1366x2000": [(0, 0, 1366, 200),
                                  (931, 127, 1344, 1864),
                                  (0, 0, 60, 1860),
                                  ScreenMask.bee_bottom_scrollbar]})
    pytest.selenium_driver.switch_to.default_content()

    # click | data-e2e-id=ee-subject-container |
    kh.sh.click_by_xpath("step x", "//*[@" + kh.dataE2eId + "='ee-subject-container']")
    time.sleep(25)

    # type | data-e2e-id=edit-subject | some subject
    subject_edit = kh.sh.pass_xpath_clickable("Get the field for name", "//*[@" + kh.dataE2eId + "='smart-subject-input']")
    subject_edit.send_keys(mail_name)

    # type | data-e2e-id==apply-subject-button
    kh.sh.click_by_xpath("step x", "//button[@" + kh.dataE2eId + "='apply-subject-button']")

    kh.sh.reset_wait_timeout()

@pytest.mark.incremental(if_failed='test_new_emaileditor_create_mail')
@pytest.mark.order12
def test_new_emaileditor_load_mail(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """
    Use Case - We create some new testdata für new mail editor.

               Based on the standard data set.
    """
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Campaigns", "E-Mails / SMS")

    edit_button = kh.sh.pass_xpath_exists("Try to get the edit button of our mail : 'new bee email'",
                                          "//kt-table-text//a[text()='new bee email']")

    kh.sh.click("Click on email name anchor, then we can delete it.", edit_button)

    kh.sh.set_wait_timeout(25)

    # click | data-e2e-id=emailDetailAffix-item-editor |
    kh.sh.click_by_xpath("Open DD editor", "//a[@" + kh.dataE2eId + "='emailDetailAffix-item-editor']")
    kh.sh.reset_wait_timeout()

    time.sleep(9)
    # Default Content is the main page (Our KT Menu bar frame)
    # Switch back by pytest.selenium_driver.switch_to.default_content()
    pytest.selenium_driver.switch_to.frame(0)

    ih.screen_test("smoke_test_local",
                   "bee_template_reload_without_change",
                   {"1366x2000": [(0, 0, 1366, 114),
                                  (931, 127, 1344, 1864),
                                  (0, 0, 60, 1860),
                                  ScreenMask.bee_bottom_scrollbar]})

@flaky(max_runs=1)
@pytest.mark.incremental(if_failed='test_new_emaileditor_create_mail')
@pytest.mark.order13
def test_new_emaileditor_change_and_reload(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """
    Use Case - We create some new testdata für new mail editor.

               Based on the standard data set.
    """
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)

    kh.click_menu_item("Campaigns", "E-Mails / SMS")

    kh.sh.click_by_xpath("hint", "//kt-table-text//a[text()='new bee email']")

    kh.sh.set_wait_timeout(25)
    # click | data-e2e-id=emailDetailAffix-item-editor |
    kh.sh.click_by_xpath("Open DD editor", "//a[@" + kh.dataE2eId + "='emailDetailAffix-item-editor']")
    kh.sh.reset_wait_timeout()

    time.sleep(9)
    # Default Content is the main page (Our KT Menu bar frame)
    # Switch back by pytest.selenium_driver.switch_to.default_content()
    pytest.selenium_driver.switch_to.frame(0)

    src = kh.sh.pass_xpath_exists("Drag", "//div[(@data-dnd-name='Paragraph' or @data-dnd-name='Text') and @data-drag-source='true']")
    tgt = kh.sh.pass_xpath_exists("Drop", "//div[@id='20-0-0']")
    ActionChains(pytest.selenium_driver).click_and_hold(src).move_to_element_with_offset(tgt, 50, 200).pause(
        2).release().perform()

    ih.screen_test("smoke_test_local",
                   "bee_template_changed", {"1366x2000": [(0, 0, 1366, 114),
                                                          (931, 127, 1344, 1864),
                                                          (0, 0, 60, 1860),
                                                          ScreenMask.bee_bottom_scrollbar]})

    pytest.selenium_driver.switch_to.default_content()

    kh.click_menu_item("Campaigns", "E-Mails / SMS")

    kh.sh.click_by_xpath("hint", "//kt-table-text//a[text()='new bee email']")

    kh.sh.set_wait_timeout(25)
    # click | data-e2e-id=emailDetailAffix-item-editor |
    kh.sh.click_by_xpath("Open DD editor", "//a[@" + kh.dataE2eId + "='emailDetailAffix-item-editor']")
    kh.sh.reset_wait_timeout()

    time.sleep(9)

    ih.screen_test("smoke_test_local",
                   "bee_template_reload_changed_mail",
                   {"1366x2000": [(0, 0, 1366, 114),
                                  (931, 127, 1344, 1864),
                                  (0, 0, 60, 1860),
                                  ScreenMask.bee_bottom_scrollbar]})

@pytest.mark.blocked(by=['browser container access on kt containers https://assets.klicktipp not working properly'])
@pytest.mark.incremental(if_failed='test_login')
@pytest.mark.order14
def test_templates_images(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """
    Test that resources like images and others are properly loaded for templates.

    Test page provided by Tony with all available templates.
    One negative test, expected failure, at the top of the template link list. Missing images.
    """
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)
    template_load_failed = beeh.get_templates_failed_loading(base_url)
    assert len(template_load_failed) == 1 and template_load_failed[
        0] == "Negative test", "Only the negative test should fail. So we have a real fail! Failed for ... " + ", ".join(
        template_load_failed)


__author__ = "Simon Werling"
__copyright__ = "Copyright 2024, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

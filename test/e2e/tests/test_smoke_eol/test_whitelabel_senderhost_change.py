# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, K<PERSON>-Tipp
# Email: <EMAIL>
"""EOL : Change sender host of whitelabel domain and check sender hosts in email."""

import os
import random
import re
import pytest
import logging
from flaky import flaky
from tests.helpers import klick_tipp_helper as kh
from tests.helpers import anmeldeformular_helper as aform_helper
from tests.helpers import subscriber_area_helper as sah
from tests.helpers import adminarea_helper as aah

PYTEST_LOGGER = logging.getLogger('pytest_logger')

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._app_url = "app.klicktipp-staging.com"
    staging_data._shared_domain = "staging-beta.com"
    staging_data._klick_host_alias = "news"
    staging_data._klick_host_url = "https://" + staging_data._klick_host_alias + "." + staging_data._shared_domain
    staging_data._form_preview = "https://klicktipp-staging.s3.amazonaws.com/userimages/13397/forms/1411/15bzawrz8zba07.html"
    staging_data._whitelabel_domain_name = "staging-gamma.com"
    staging_data._wl_host1 = "mx1"
    staging_data._wl_host2 = "mx2"
    staging_data._wl_hosts_ip = "*************"  # Both sender hosts configured with same IP
    staging_data._confirm_mail_subject = "One click left ..."
    staging_data._manual_validate_button_text = "Manuelle Überprüfung"

    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._app_url = "app.klicktipp.com"
    production_data._shared_domain = "automizen.io"
    production_data._klick_host_alias = "klick"
    production_data._klick_host_url = "https://" + production_data._klick_host_alias + "." + production_data._shared_domain
    production_data._form_preview = "https://klicktipp.s3.amazonaws.com/userimages/312010/forms/220961/55d6z79okz8z43a8.html"
    production_data._whitelabel_domain_name = "klicktipptest.net"
    production_data._wl_host1 = "sirius"
    production_data._wl_host2 = "canopus"
    production_data._wl_hosts_ip = "**************"  # Both sender hosts configured with same IP
    production_data._confirm_mail_subject = "Ein Klick fehlt noch ..."
    production_data._manual_validate_button_text = "Manuelle Überprüfung"
    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._subscriber_test_mailaddr_current_host = "randsomrandy19+" + str(random.getrandbits(32)) + "@gmail.com"
    test_data._subscriber_test_mailaddr_changed_host = "randsomrandy19+" + str(random.getrandbits(32)) + "@gmail.com"


@pytest.mark.tm4j(publish_for='DEV-T238')
@flaky(max_runs=1)
@pytest.mark.order1
def test_register_and_test_confirmmail_senderhost(base_url, standard_support_user, standard_support_user_pwd,
                                                  standard_whitelabel_user, standard_subscriber,
                                                  standard_subscriber_pwd):
    """Get senderhost for whitelabel domain, register subscriber, check confirmation mail sender host."""
    # Login with support account
    kh.open_kt_and_login(base_url, account=standard_support_user, pwd=standard_support_user_pwd)
    # Set customermenu for whitelabel user
    aah.open_customermenu_for(standard_whitelabel_user)

    aah.open_customermenu_whitelabel()
    # Switch back to first tab with customer menu
    # pytest.selenium_driver.switch_to.window(pytest.selenium_driver.window_handles[0])
    # Switch to new tab
    pytest.selenium_driver.switch_to.window(pytest.selenium_driver.window_handles[-1])

    # Get configured whitelabel domains sender host
    host1_url = test_data._wl_host1 + "." + test_data._whitelabel_domain_name
    host2_url = test_data._wl_host2 + "." + test_data._whitelabel_domain_name
    xpath = f'//td[contains(text(),"{test_data._wl_hosts_ip}")]/../td[contains(text(),"{host1_url}") or contains(text(),"{host2_url}")]'
    try:
        current_host = kh.sh.pass_xpath_exists("Get configured sender host", xpath).text
    except:
        PYTEST_LOGGER.warning(f"We were not able to retrieve one senderhost for xpath {xpath}.")
        PYTEST_LOGGER.warning(f"Maybe there is not any mx1/2 host defined in the table or we have two entries.")
        PYTEST_LOGGER.warning(f"We will delete all senderhosts and add the mx1 host.")

        # check whether we have none or two senderhosts
        matched_hosts = kh.sh.pass_xpath_exists("Get configured sender host or list of hosts", xpath)
        current_host_url = list()
        if type(matched_hosts) is list:
            for host in matched_hosts:
                current_host_url.append(host.text)

        aah.open_whitelabel_configuration_for(test_data._whitelabel_domain_name)

        if type(current_host_url) is list:
            # we have two senderhosts, last added senderhost
            kh.sh.click_by_xpath("Click delete button for sender host " + current_host_url[0],
                                 "//div[contains(text(),'" + current_host_url[0] + "')]/../../..//a[text()='Löschen']")
        else:
            # we have none senderhosts, add one
            kh.sh.click_by_xpath("Click 'Hinzufügen' for new host", "//a[@id='edit-hosts-addbutton']")
            kh.sh.send_string_to_input("//input[@id='edit-hosts-hostname']", test_data._wl_host1, clear=True)
            kh.sh.send_string_to_input("//input[@id='edit-hosts-ipaddress']", test_data._wl_hosts_ip, clear=True)
            kh.sh.click_by_xpath("Submit host entry by click on 'Host hinzufügen'", "//input[@id='edit-hosts-addbutton']")

        kh.sh.set_wait_timeout(15)
        kh.close_success_alert()
        kh.sh.reset_wait_timeout()

        # click on 'Speichern'
        kh.sh.click_by_xpath("Click on 'Speichern' in our wl domains config page", "//input[@id='edit-submit']")
        kh.sh.reset_wait_timeout()

        # Wait 5 minutes until change was done on serverside
        kh.sh.keep_selenium_session_alive(5)

        current_host = kh.sh.pass_xpath_exists("Get configured sender host", xpath).text

    # Register subscriber
    aform_helper.preview_register_with_mail_by_form_link(test_data._form_preview,
                                                         test_data._subscriber_test_mailaddr_current_host)
    # Wait for confirmation mail
    # Only for polling html method
    sah.get_mail_html_part(standard_subscriber, pwd=standard_subscriber_pwd,
                           mail_address=test_data._subscriber_test_mailaddr_current_host, subject=test_data._confirm_mail_subject)
    headers = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                  mail_address=test_data._subscriber_test_mailaddr_current_host,
                                                  subject=test_data._confirm_mail_subject)
    received_header_values = sah.get_mail_header_entries_from_header_list(headers, "Received")
    relevant_header_entry = None
    # Find this entry : {'Received': 'from mx1.staging-omega.com ([***************]) ...'}
    for header_entry in received_header_values:
        if re.search(r'^from ', header_entry["Received"]):
            relevant_header_entry = header_entry["Received"]
            break
    senderhost = re.search(r'.+?' + re.escape(current_host), relevant_header_entry)
    # Test Received: header for configured sender host
    assert senderhost is not None, "Configured senderhost " + current_host + " not set in 'Received:'' header of confirmation mail! Instead : " + relevant_header_entry


@pytest.mark.incremental(if_failed="test_register_and_test_confirmmail_senderhost")
@pytest.mark.tm4j(publish_for='DEV-T238')
@flaky(max_runs=1)
@pytest.mark.order2
def test_change_senderhost_and_test_confirmmail(base_url, standard_support_user, standard_support_user_pwd,
                                                standard_whitelabel_user, standard_subscriber, standard_subscriber_pwd):
    """Set alternative senderhost, register subscriber, check confirmation mail sender host."""
    # Login with support account
    kh.open_kt_and_login(base_url, account=standard_support_user, pwd=standard_support_user_pwd)
    # Set customermenu for whitelabel user
    aah.open_customermenu_for(standard_whitelabel_user)
    aah.open_customermenu_whitelabel()

    # Switch back to first tab with customer menu
    # pytest.selenium_driver.switch_to.window(pytest.selenium_driver.window_handles[0])
    # Switch to new tab
    pytest.selenium_driver.switch_to.window(pytest.selenium_driver.window_handles[-1])
    # Change whitelabel domains sender host
    aah.open_whitelabel_configuration_for(test_data._whitelabel_domain_name)
    host1_url = test_data._wl_host1 + "." + test_data._whitelabel_domain_name
    host2_url = test_data._wl_host2 + "." + test_data._whitelabel_domain_name
    xpath = "//td//div[contains(text(),'" + host1_url + "') or contains(text(),'" + host2_url + "')]"
    current_host_url = kh.sh.pass_xpath_exists("Get configured sender host", xpath).text

    new_host_url = host2_url if current_host_url == host1_url else host1_url
    new_host = test_data._wl_host2 if current_host_url == host1_url else test_data._wl_host1

    kh.sh.click_by_xpath("Click 'Hinzufügen' for new host", "//a[@id='edit-hosts-addbutton']")
    kh.sh.send_string_to_input("//input[@id='edit-hosts-hostname']", new_host, clear=True)
    kh.sh.send_string_to_input("//input[@id='edit-hosts-ipaddress']", test_data._wl_hosts_ip, clear=True)
    kh.sh.click_by_xpath("Submit host entry by click on 'Host hinzufügen'", "//input[@id='edit-hosts-addbutton']")
    kh.sh.set_wait_timeout(15)
    kh.close_success_alert()
    kh.sh.reset_wait_timeout()

    kh.sh.click_by_xpath("Click delete button for sender host " + current_host_url,
                         "//td[contains(text(),'" + current_host_url + "')]/../../..//a[text()='Löschen']")
    kh.sh.set_wait_timeout(15)
    kh.close_success_alert()
    kh.sh.reset_wait_timeout()

    kh.sh.pass_xpath_exists("Check new host entry",
                            "//td//div[text()='" + new_host_url + "']/../../../td//div[text()='" + test_data._wl_hosts_ip + "']")

    kh.sh.click_by_xpath("Click on 'Speichern' in our wl domains config page", "//input[@id='edit-submit']")

    kh.sh.reset_wait_timeout()

    # Wait 5 minutes until change was done on serverside
    kh.sh.keep_selenium_session_alive(5)

    # Register subscriber
    aform_helper.preview_register_with_mail_by_form_link(test_data._form_preview,
                                                         test_data._subscriber_test_mailaddr_changed_host)
    # Wait for confirmation mail
    # Only for polling html method
    sah.get_mail_html_part(standard_subscriber, pwd=standard_subscriber_pwd,
                           mail_address=test_data._subscriber_test_mailaddr_changed_host, subject=test_data._confirm_mail_subject)
    headers = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                  mail_address=test_data._subscriber_test_mailaddr_changed_host,
                                                  subject=test_data._confirm_mail_subject)
    received_header_values = sah.get_mail_header_entries_from_header_list(headers, "Received")
    relevant_header_entry = None
    # Find this entry : {'Received': 'from mx2.staging-omega.com ([***************]) ...'}
    for header_entry in received_header_values:
        if re.search(r'^from ', header_entry["Received"]):
            relevant_header_entry = header_entry["Received"]
            break
    senderhost = re.search(r'.+?' + re.escape(new_host_url), relevant_header_entry)
    # Test Received: header for configured sender host
    assert senderhost is not None, "Configured senderhost " + new_host_url + " not set in 'Received:'' header of confirmation mail! Instead : " + relevant_header_entry


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2024, Klick-Tipp
# Email: <EMAIL>
"""EOL : request forwarder test."""

import os
import random
import re
import time
from flaky import flaky
import pytest
import logging

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import contactcloud_helper as ccloud_helper
from tests.helpers import forwarder_helper_new_angular as fwh
from tests.helpers import gmail_helper as gmh
from tests.helpers import subscriber_area_helper as sah

PYTEST_LOGGER = logging.getLogger('pytest_logger')

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._app_url = "app.klicktipp-staging.com"
    staging_data._shared_domain = "staging-beta.com"
    staging_data._klick_host_alias = "news"
    staging_data._klick_host_url = "https://" + staging_data._klick_host_alias + "." + staging_data._shared_domain
    staging_data._form_preview = "https://klicktipp-staging.s3.amazonaws.com/userimages/268/forms/791/mlz7nz8z614e.html"
    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._app_url = "app.klicktipp.com"
    production_data._shared_domain = "automizen.io"
    production_data._klick_host_alias = "klick"
    production_data._klick_host_url = "https://" + production_data._klick_host_alias + "." + production_data._shared_domain
    production_data._form_preview = "https://klicktipp.s3.amazonaws.com/userimages/173644/forms/216161/51g1z41q9z8z5ec1.html"
    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    # Something like that : 1390139486
    test_data._current_unique_forwarder_key = str(random.getrandbits(32))
    test_data._forwarder_name = "e2e tests: forwarder " + test_data._current_unique_forwarder_key
    test_data._forwarder_mail_prefix = "e2e-test-dev+" + test_data._current_unique_forwarder_key
    test_data._forwarder_subscribe_cmd = "eintragen"
    test_data._forwarder_unsubscribe_cmd = "austragen"


@pytest.mark.tm4j(publish_for='DEV-T232')
@flaky(max_runs=1)
@pytest.mark.order1
def test_request_forwarder(login_via_session_cookie, standard_subscriber, standard_subscriber_pwd,
                           standard_subscriber_random_mail_address):
    """Create request forwarder and test subscribe and unsubscribe."""
    # standard_subscriber randsomrandy19@gmailcom -> subscribing via smtp with changing 'From:'' and smtp sender does currently not working as expected.
    # Means: It is always <NAME_EMAIL> and not <NAME_EMAIL> subscriber
    ccloud_helper.delete_subscriber(standard_subscriber, allow_failure=True)

    # Delete old forwarders
    list_of_old_forwarders = fwh.get_forwarders_edit_links_by_regex(re.escape("e2e tests: forwarder ") + r'[0-9]{5,10}')
    fwh.delete_list_of_forwarders(list_of_old_forwarders, allow_failure=True)

    # Create new forwarder
    kh.click_menu_item("Listbuilding", "Listbuilding", "Neues Listbuilding")
    kh.sh.click_by_xpath("Click 'Eintragung per E-Mail'", "//a[text()='Eintragung per E-Mail']")
    kh.sh.send_string_to_input("//*[@data-e2e-id='entityName-input']", test_data._forwarder_name, clear=True)

    kh.sh.send_string_to_input("//input[@data-e2e-id='localRequestAddressPart-input']", test_data._forwarder_mail_prefix,
                               clear=True)
    kh.sh.send_string_to_input("//*[@data-e2e-id='subscriptionCommand-input']", test_data._forwarder_subscribe_cmd,
                               clear=True)
    kh.sh.send_string_to_input("//*[@data-e2e-id='unsubscriptionCommand-input']",
                               test_data._forwarder_unsubscribe_cmd, clear=True)
    mail_suffix = kh.sh.pass_xpath_exists("Get forwarder suffix @klick-tipp-staging.com for example.",
                                          "//span[@class='kt-field-suffix']").text
    kh.sh.click_by_xpath("Submit new forwarder by click on 'Eintragung per E-Mail anlegen'",
                         "//*[@data-e2e-id='genericCreateBtn']")

    # Wait around 7 minutes until forwarder is created on server side
    kh.sh.keep_selenium_session_alive(9)

    # Assume forwarder created. Write mail
    forwarder_mail = test_data._forwarder_mail_prefix + mail_suffix
    PYTEST_LOGGER.info(f"Send mail to request forwarder : {forwarder_mail}")
    PYTEST_LOGGER.info(f"With subject : {test_data._forwarder_subscribe_cmd}")

    gmh.send_mail_via_smtp(standard_subscriber, gmail_pwd=standard_subscriber_pwd,
                           from_addr=standard_subscriber_random_mail_address,
                           to=forwarder_mail, subject=test_data._forwarder_subscribe_cmd)

    # Give delivery report a chance
    time.sleep(180)
    # Test for mailer daemon/delivery report
    # Reduce search time by only checking mails arrived within the last two minutes
    plain_text_parts_of_maybe_deliveryreports = sah.get_mails_within_last_minutes_to(standard_subscriber,
                                                                                     standard_subscriber_pwd,
                                                                                     standard_subscriber)
    # We throw an error showing you the message id. You can search in gmail client of randsomrandy19 (dashlane)
    # for analysis purposes (search field in client):  rfc822msgid:<<EMAIL>>.
    for msgid, plain_text in plain_text_parts_of_maybe_deliveryreports.items():
        assert None is re.search(re.escape(forwarder_mail),
                                 plain_text), "We found a mailer daemon after deleting forwarder and newly sent subscribe. msg-id : Check by searching in gmail client - query: 'rfc822msgid:" + msgid + "'' :: the mail content: " + plain_text

    ccloud_helper.open_cc_suchen_dialog()
    assert ccloud_helper.poll_for_something_in_subscriber_row(ccloud_helper.pending_icon_set_for, standard_subscriber,
                                                              120), "Pending icon not set for : " + standard_subscriber
    assert ccloud_helper.poll_for_something_in_subscriber_row(ccloud_helper.nobounce_icon_set_for, standard_subscriber,
                                                              1), "Nobounce icon not set for : " + standard_subscriber

    # Delete old forwarders
    list_of_old_forwarders = fwh.get_forwarders_edit_links_by_regex(re.escape("e2e tests: forwarder ") + r'[0-9]{5,10}')
    fwh.delete_list_of_forwarders(list_of_old_forwarders, allow_failure=True)

    # Wait around 4 minutes until forwarder is deleted on server side
    kh.sh.keep_selenium_session_alive(8)

    ccloud_helper.delete_subscriber(standard_subscriber, allow_failure=False)
    gmh.send_mail_via_smtp(standard_subscriber, gmail_pwd=standard_subscriber_pwd,
                           from_addr=standard_subscriber_random_mail_address,
                           to=forwarder_mail, subject=test_data._forwarder_subscribe_cmd)

    # Forwarder is now deleted and the subscriber in klicktipp.
    # Now two more checks.

    # 1st bit more complicated check.
    #
    # Check that there was no mailer daemon / delivery report mail
    # for already deleted forwarder.
    # The search criteria (only mail address) seems to be weak! But, since it
    # is not 100% clear how the appearance of the delivery report will look like
    # we take the risk of false negatives here! Adjust criteria, if too weak!
    #
    # e.g. Delivery Report from staging
    #
    #     Content-Type: text/plain
    #
    # Hello, this is the mail server on staging11.zauberlist.com.
    #
    # I am sending you this message to inform you on the delivery status of a
    # message you previously sent.  Immediately below you will find a list of
    # the affected recipients;  also attached is a Delivery Status Notification
    # (DSN) report in standard format, as well as the headers of the original
    # message.
    #
    #   !!!! Simon: We search for this mail address! <- the forwarder who shouldn't throw this kind of delivery report!
    # <AUTHOR> <EMAIL>  delivery failed; will not continue trying

    # Give delivery report a chance
    time.sleep(180)
    # Reduce search time by only checking mails arrived within the last two minutes
    plain_text_parts_of_maybe_deliveryreports = sah.get_mails_within_last_minutes_to(standard_subscriber,
                                                                                     standard_subscriber_pwd,
                                                                                     standard_subscriber)
    # We throw an error showing you the message id. You can search in gmail client of randsomrandy19 (dashlane)
    # for analysis purposes (search field in client):  rfc822msgid:<<EMAIL>>.
    for msgid, plain_text in plain_text_parts_of_maybe_deliveryreports.items():
        assert None is re.search(re.escape(forwarder_mail),
                                 plain_text), "We found a mailer daemon after deleting forwarder and newly sent subscribe. msg-id : Check by searching in gmail client - query: 'rfc822msgid:" + msgid + "'' :: the mail content: " + plain_text

    # 2nd easy check: there should be no new creation of the subscriber!
    ccloud_helper.open_cc_suchen_dialog()
    assert not ccloud_helper.poll_for_something_in_subscriber_row(ccloud_helper.pending_icon_set_for,
                                                                  standard_subscriber,
                                                                  120), "After deletion of forwarder/subscriber and re-subscribe on non-existing forwarder, our search shouldn't find the subscriber! : " + standard_subscriber


__author__ = "Simon Werling"
__copyright__ = "Copyright 2024, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

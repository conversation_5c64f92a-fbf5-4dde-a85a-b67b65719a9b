# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""EOL : Autoresponder test."""

import os
import pytest
import time

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import contactcloud_helper as ccloud_helper
from tests.helpers import anmeldeformular_helper as aform_helper
from tests.helpers import subscriber_area_helper as sah


@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._app_url = "app.klicktipp-staging.com"
    staging_data._shared_domain = "staging-beta.com"
    staging_data._klick_host_alias = "news"
    staging_data._klick_host_url = "https://" + staging_data._klick_host_alias + "." + staging_data._shared_domain
    staging_data._form_preview = "https://assets.klicktipp-staging.com/userimages/268/forms/791/mlz7nz8z614e.html"
    staging_data._confirm_mail_subject = "One click left ..."
    staging_data._confirm_link_text = "Confirm E-mail address"
    staging_data._images_folder_name = "images"
    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._app_url = "app.klicktipp.com"
    production_data._shared_domain = "automizen.io"
    production_data._klick_host_alias = "klick"
    production_data._klick_host_url = "https://" + production_data._klick_host_alias + "." + production_data._shared_domain
    production_data._form_preview = "https://assets.klicktipp.com/userimages/173644/forms/216161/51g1z41q9z8z5ec1.html"
    production_data._confirm_mail_subject = "Ein Klick fehlt noch ..."
    production_data._confirm_link_text = "E-Mail-Adresse bestätigen"
    production_data._images_folder_name = "bilder"
    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]


@pytest.mark.tm4j(publish_for='DEV-T228')
@pytest.mark.order1
def test_autoresponder(login_via_session_cookie, standard_subscriber, standard_subscriber_pwd,
                       standard_subscriber_random_mail_address):
    """Register via form, confirm, check autoresponder received and smart tagging."""
    aform_helper.preview_register_with_mail_by_form_link(test_data._form_preview,
                                                         standard_subscriber_random_mail_address)

    time.sleep(10)
    confirmmail_anchors = sah.get_anchors_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                    subscriber_mail_address=standard_subscriber_random_mail_address,
                                                    subject=test_data._confirm_mail_subject)
    bestaetigen_url = sah.get_href_by_anchortext(confirmmail_anchors, test_data._confirm_link_text)
    assert bestaetigen_url is not None, "Coundn't find link with following text in confirmation mail : " + test_data._confirm_link_text
    pytest.selenium_driver.get(bestaetigen_url)
    kh.sh.pass_xpath_exists("Get Email field in preview",
                            "//p[contains(text(),'Bitte fügen Sie uns Ihren Kontakten hinzu')]")

    assert None is not sah.get_mail_html_part(standard_subscriber, pwd=standard_subscriber_pwd,
                                              mail_address=standard_subscriber_random_mail_address,
                                              subject="e2e tests: bee follow up mail"), "Autoresponder not received!"

    # Tag for "geöffnet" not fired when opening mail via smtp!
    # Trigger "geöffnet" Tag by tracking pixel.
    html_content = sah.get_parsed_mail_html(standard_subscriber,
                                            pwd=standard_subscriber_pwd,
                                            subscriber_mail_address=standard_subscriber_random_mail_address,
                                            subject="e2e tests: bee follow up mail")
    tracking_pixel_url = sah.get_tracking_pixel(html_content).get('src')
    pytest.selenium_driver.get(tracking_pixel_url)

    # Wait for the smart tagging
    time.sleep(5)

    # Open subscribers history
    pytest.selenium_driver.get("https://" + test_data._app_url)
    ccloud_helper.open_cc_suchen_dialog()
    ccloud_helper.search_by_mail(standard_subscriber_random_mail_address)
    ccloud_helper.open_subscriber_from_search_results_page(standard_subscriber_random_mail_address)
    ccloud_helper.open_subscriber_history()

    smarttag_erhalten = True
    kh.sh.set_wait_timeout(1)
    try:
        kh.sh.pass_xpath_exists("Smart tag exists for received autoresponder.",
                                "//td[text()='Tag-Zuweisung']/../td/a[contains(text(),'Follow-up-E-Mail erhalten')]")
    except:
        smarttag_erhalten = False

    assert smarttag_erhalten, "Smart Tag not set for Follow-up Mail erhalten."

    smarttag_geoeffnet = True
    try:
        kh.sh.pass_xpath_exists("Smart tag exists for opened mail.",
                                "//td[text()='Tag-Zuweisung']/../td/a[contains(text(),'Follow-up-E-Mail geöffnet')]")
    except:
        smarttag_geoeffnet = False

    assert smarttag_geoeffnet, "Smart Tag not set for Follow-up Mail geöffnet."


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

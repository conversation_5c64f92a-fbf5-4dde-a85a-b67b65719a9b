# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""EOL : Outbound test."""

import os
import pytest
import time
import logging

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import anmeldeformular_helper as aform_helper
from selenium.webdriver.common.action_chains import ActionChains

PYTEST_LOGGER = logging.getLogger('pytest_logger')


@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._app_url = "app.klicktipp-staging.com"
    staging_data._shared_domain = "staging-beta.com"
    staging_data._klick_host_alias = "news"
    staging_data._klick_host_url = "https://" + staging_data._klick_host_alias + "." + staging_data._shared_domain
    staging_data._form_preview = "https://klicktipp-staging.s3.amazonaws.com/userimages/268/forms/1395/14uz7nz8zc24c.html"
    staging_data._outbound_edit = "https://app.klicktipp-staging.com/tools/268/outbound/265/edit"
    staging_data._kt_user_mail = "<EMAIL>"
    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._app_url = "app.klicktipp.com"
    production_data._shared_domain = "automizen.io"
    production_data._klick_host_alias = "klick"
    production_data._klick_host_url = "https://" + production_data._klick_host_alias + "." + production_data._shared_domain
    production_data._form_preview = "https://klicktipp.s3.amazonaws.com/userimages/173644/forms/220661/554lz41q9z8z672e.html"
    production_data._outbound_edit = "https://app.klicktipp.com/tools/173644/outbound/71055/edit"
    production_data._kt_user_mail = "<EMAIL>"
    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._testpage_for_outbounds = "https://webhook.site/"


@pytest.mark.tm4j(publish_for='DEV-T242')
@pytest.mark.order1
def test_outbound(login_via_session_cookie, standard_subscriber, standard_subscriber_pwd,
                  standard_subscriber_random_mail_address):
    """Test outbound test button and trigger outbound via campaign."""
    # Open webhook page and delete old requests
    pytest.selenium_driver.get(test_data._testpage_for_outbounds)
    time.sleep(3)
    try:
        kh.sh.set_wait_timeout(2)
        # The crosses are only visible by mouse over, and therefore only attachable when visible. First move mouse over request entry, then click delete cross img.
        req_row_entries = kh.sh.pass_xpath_exists("Get request entries from sidebar",
                                                  "//ul[contains(@class,'nav-sidebar')]//li[not(contains(@class,'ng-hide'))]")
        req_del_img_of_all_entries = kh.sh.pass_xpath_exists("Get request entries from sidebar",
                                                             "//div//li//span[text()='POST']/../..//a[@title='Delete request']")
        req_row_entries = req_row_entries if type(req_row_entries) is list else [req_row_entries]
        req_del_img_of_all_entries = req_del_img_of_all_entries if type(req_del_img_of_all_entries) is list else [
            req_del_img_of_all_entries]

        for i in range(0, len(req_row_entries)):
            ActionChains(pytest.selenium_driver).move_to_element(req_row_entries[i]).perform()
            ActionChains(pytest.selenium_driver).move_to_element_with_offset(req_del_img_of_all_entries[i], 3, 3).pause(
                2).click().perform()
    except:
        PYTEST_LOGGER.info("There were no request to delete.")

    kh.sh.reset_wait_timeout()

    # Get our unique activation url for the outbound
    unique_outbound_url = kh.sh.pass_xpath_exists("Get unique test outbound url",
                                                  "//p/code[contains(text(),'" + test_data._testpage_for_outbounds + "')]").text

    # set activation url in outbound and trigger test
    pytest.selenium_driver.get(test_data._outbound_edit)
    kh.sh.send_string_to_input("//input[@id='edit-activationurl']", unique_outbound_url, clear=True)
    kh.sh.click_by_xpath("Save and test outbound.", "//input[@id='edit-saveandtest']")

    # Check the request and its data on webhook test page
    pytest.selenium_driver.get(test_data._testpage_for_outbounds)

    kh.sh.pass_xpath_exists("Test email field overgiven with POST outbound",
                            "//td[text()='email']/..//code[text()='" + test_data._kt_user_mail + "']")
    kh.sh.pass_xpath_exists("Test param1 custom field overgiven with POST outbound",
                            "//td[text()='param1']/..//code[text()='param1Value']")

    # Delete old requests, before we fire the campaign with the outbound
    pytest.selenium_driver.get(test_data._testpage_for_outbounds)
    time.sleep(3)
    try:
        kh.sh.set_wait_timeout(2)
        # The crosses are only visible by mouse over, and therefore only attachable when visible. First move mouse over request entry, then click delete cross img.
        req_row_entries = kh.sh.pass_xpath_exists("Get request entries from sidebar",
                                                  "//ul[contains(@class,'nav-sidebar')]//li[not(contains(@class,'ng-hide'))]")
        req_del_img_of_all_entries = kh.sh.pass_xpath_exists("Get request entries from sidebar",
                                                             "//div//li//span[text()='POST']/../..//a[@title='Delete request']")
        req_row_entries = req_row_entries if type(req_row_entries) is list else [req_row_entries]
        req_del_img_of_all_entries = req_del_img_of_all_entries if type(req_del_img_of_all_entries) is list else [
            req_del_img_of_all_entries]

        for i in range(0, len(req_row_entries)):
            ActionChains(pytest.selenium_driver).move_to_element(req_row_entries[i]).perform()
            ActionChains(pytest.selenium_driver).move_to_element_with_offset(req_del_img_of_all_entries[i], 3, 3).pause(
                2).click().perform()
    except:
        PYTEST_LOGGER.info("There were no request to delete.")

    kh.sh.reset_wait_timeout()

    # Trigger the campaign with the outbound
    aform_helper.preview_register_with_mail_by_form_link(test_data._form_preview,
                                                         standard_subscriber_random_mail_address, single_opt_in=True)

    # open webhook test page and wait for incoming request
    pytest.selenium_driver.get(test_data._testpage_for_outbounds)

    # Wait for the campaign triggering the outbound
    request_found = False
    kh.sh.set_wait_timeout(1)
    while i < (10 * 60):
        try:
            kh.sh.pass_xpath_exists("Test email field overgiven with POST outbound",
                                    "//td[text()='email']/..//code[text()='" + standard_subscriber_random_mail_address + "']")
            kh.sh.pass_xpath_exists("Test param1 custom field overgiven with POST outbound",
                                    "//td[text()='param1']/..//code[text()='param1Value']")
            request_found = True
            break
        except:
            pass
        i += 2  # Two seconds waited on pass_xpath_exists directives

    assert request_found, "Request from campaign triggered outbound not found on test page within ~5 min."


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

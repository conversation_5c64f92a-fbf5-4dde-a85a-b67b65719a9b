# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, K<PERSON>-Tipp
# Email: <EMAIL>
"""Provide URLs to domains, pages, etc."""
import os
import re

import pytest


@pytest.fixture(scope='session')
def kt_app_url():
    """Receive URL to kt application e.g https://app.klicktipp.com or https://klicktipp (local).

    www.klicktipp.com would be no valid app url. That's the marketing page,
    redirecting to app.klicktipp.com <- this is the klicktipp application.

    Depending on given URL : local, staging, production
    """
    app_url = {"www.klicktipp-staging.com": 'https://app.klicktipp-staging.com',  # Staging
               "www.klicktipp.com": 'https://app.klicktipp.com',  # Production
               "app.ktlocal.com": "https://app.ktlocal.com",  # local dev compose stack
               "app-e2e.ktlocal.com": "https://app-e2e.ktlocal.com",  # local e2e compose stack
               "www-e2e.ktlocal.com": "https://www-e2e.ktlocal.com"}  # local e2e compose stack

    return app_url[os.environ['KLICKTIPP_URL']]


@pytest.fixture(scope='session')
def forbidden_domain(base_url):
    """Return from https://staging.zauberlist.com only staging.zauberlist.com."""
    return re.sub(r'[^/]+//', "", base_url)


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

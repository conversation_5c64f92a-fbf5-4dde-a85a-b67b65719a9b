# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2024, <PERSON><PERSON>-Tip<PERSON>
# Email: <EMAIL>
"""Selenium fixtures like get_webdriver, etc."""
import os
import re
import logging
import json
import time
from datetime import date

import pytest
import selenium
import selenium.webdriver.safari.options as SafariOptions
import selenium.webdriver.firefox.options as FirefoxOptions
import selenium.webdriver.edge.options as EdgeOptions
import selenium.webdriver.chrome.options as ChromeOptions


PYTEST_LOGGER = logging.getLogger('pytest_logger')


@pytest.fixture(scope='session')
def set_selenium_webdriver_params(pytestconfig):
    """Set all relevent information for creating the selenium webdriver."""
    pytest.browser = pytestconfig._capabilities["browserName"]
    if pytest.browser == "edge":
        pytest.browser_resolution = '1366x768'
    elif pytest.browser == "safari_macos":
        pytest.browser_resolution = '1920x1080'
    elif pytest.browser == "chrome_macos":
        pytest.browser_resolution = '1920x1080'
    elif pytest.browser == "firefox_win":
        pytest.browser_resolution = '1920x1080'


@pytest.fixture(scope="module")
def get_webdriver_for_setting_session_cookie(pytestconfig, request, set_selenium_webdriver_params):
    """Init the pytest.selenium_driver instance. Module scope. No autouse.

    Spare time for login into KT app.

    Once save our cookies after an initial login at pytest session start.
    """
    if pytest.selenium_driver is None:
        pytest.selenium_driver = get_selenium_webdriver(pytestconfig.getoption('driver'),
                                                        pytest.browser,
                                                        pytestconfig.getoption("--selenium-host"),
                                                        pytestconfig.getoption("--selenium-port"),
                                                        pytestconfig.getoption("--base-url"),
                                                        pytest.browserstack_user,
                                                        pytest.browserstack_token,
                                                        pytest.browser_resolution,
                                                        "global_no_spec_module",
                                                        "global_no_spec_func")


@pytest.fixture(scope="function", autouse=True)
def test_func_prep(pytestconfig, request, set_selenium_webdriver_params):
    record_replay_context = os.environ.get("E2E_RECORD_REPLAY")
    if record_replay_context and bool(int(record_replay_context)):
        test_mod_path = os.path.abspath(os.path.join(request.node.fspath, ".."))
        test_name = request.node.name

        tc_state_filepath = os.path.join(test_mod_path, pytest.browser + "." + test_name)
        tc_state_all_parallels_filepath = os.path.join(test_mod_path, test_name + ".Finished")

        # Caching is based on usernames
        # If EXEC_USER is not set, we need to construct the appropriate username by the standard prefix + browsername.
        exec_user = os.environ.get("EXEC_USER", "")
        if exec_user == "" or exec_user == "none":
            exec_user = "e2e-test-replay-" + pytest.browser

        # If caching enabled, test for necessary execution (or not, because of the import)
        # '0' | '1'
        caching_enabled_str = os.environ.get("CACHING")
        caching_enabled = bool(int(caching_enabled_str))
        if caching_enabled:
            cache_info_filepath = os.path.join(test_mod_path, "cache_exec_info.json")
            cache_info_fh = open(cache_info_filepath, "r")  # create and open for writing
            complete_cache_info = json.load(cache_info_fh)
            cache_info_fh.close()

            shall_we_execute = complete_cache_info[test_name][exec_user]["execution"]
            if not shall_we_execute:
                f = open(tc_state_filepath, "x")  # create and open for writing
                f.write(json.dumps({"state": "finished",
                                    "testResult": "skipped",
                                    "reason": "imported"}, indent=2))
                f.close()
                while True:
                    if os.path.exists(tc_state_all_parallels_filepath):
                        break
                # We wait here for all test cases finish, because after the pytest.skip
                # the teardown under yield won't be executed.
                pytest.skip("Test is cached, skip!")

        if pytest.previous_failed:
            f = open(tc_state_filepath, "x")  # create and open for writing
            f.write(json.dumps({"state": "finished",
                                "testResult": "skipped",
                                "reason": "previous test failed"}, indent=2))
            f.close()
            while True:
                if os.path.exists(tc_state_all_parallels_filepath):
                    break
            # We wait here for all test cases finish, because after the pytest.skip
            # the teardown under yield won't be executed.
            pytest.skip("Previous test failed, skip!")

        if pytest.previous_skipped:
            f = open(tc_state_filepath, "x")  # create and open for writing
            f.write(json.dumps({"state": "finished",
                                "testResult": "skipped",
                                "reason": "previous test skipped"}, indent=2))
            f.close()
            while True:
                if os.path.exists(tc_state_all_parallels_filepath):
                    break
            # We wait here for all test cases finish, because after the pytest.skip
            # the teardown under yield won't be executed.
            pytest.skip("Previous test skipped, skip!")


        f = open(tc_state_filepath, "x")  # create and open for writing
        f.write(json.dumps({"state": "running", "testResult": ""}, indent=2))
        f.close()

        if pytest.selenium_driver is None:
            pytest.selenium_driver = get_selenium_webdriver(pytestconfig.getoption('driver'),
                                                            pytest.browser,
                                                            pytestconfig.getoption("--selenium-host"),
                                                            pytestconfig.getoption("--selenium-port"),
                                                            pytestconfig.getoption("--base-url"),
                                                            pytest.browserstack_user,
                                                            pytest.browserstack_token,
                                                            pytest.browser_resolution,
                                                            re.match(r'<module \'([^\']+)\'', str(request.module)).group(1),
                                                            re.match(r'<function ([^ ]+)', str(request.function)).group(1))
        yield None
        if not hasattr(request.node, "_html_report_outcome"):
            PYTEST_LOGGER.info("It seems the current test is deactivated/skipped via zephyr. Set previous_skipped, that upcoming tests know about that.")
            pytest.previous_skipped = True

            f = open(tc_state_filepath, "w")  # create and open for writing
            f.write(json.dumps({"state": "finished",
                                "testResult": "skipped",
                                "reason": "Skipped via zephyr"}, indent=2))
            f.close()
        else:
            f = open(tc_state_filepath, "w")  # create and open for writing
            f.write(json.dumps({"state": "finished", "testResult": request.node._html_report_outcome}, indent=2))
            f.close()

            if request.node._html_report_outcome == "failed" or request.node._html_report_outcome == "error":
                pytest.previous_failed = True

        # start_time = datetime.now()
        while True:
            # curr_time = datetime.now()
            # delta = curr_time - start_time

            # When file exists, all parallel tests have finished with the test case
            # Now we can make a reliable dump with all test users finished.
            # Bzw. The next test case function below can start.
            if os.path.exists(tc_state_all_parallels_filepath):
                break

            # Don't poll forever
            # if hasattr(delta, "seconds") and delta.seconds > 360:
            #     break
            time.sleep(2)
    else:
        if pytest.selenium_driver is None:
            pytest.selenium_driver = get_selenium_webdriver(pytestconfig.getoption('driver'),
                                                            pytest.browser,
                                                            pytestconfig.getoption("--selenium-host"),
                                                            pytestconfig.getoption("--selenium-port"),
                                                            pytestconfig.getoption("--base-url"),
                                                            pytest.browserstack_user,
                                                            pytest.browserstack_token,
                                                            pytest.browser_resolution,
                                                            re.match(r'<module \'([^\']+)\'', str(request.module)).group(1),
                                                            re.match(r'<function ([^ ]+)', str(request.function)).group(1))
        yield None


@pytest.fixture(scope="function", autouse=False)
def get_webdriver(pytestconfig, request, set_selenium_webdriver_params):
    """Init the pytest.selenium_driver instance for function scope.

    With every teardown of a test function we completely auto clean up the
    selenium driver (clear cookies, set to None, ...).

    This fixture is auto used for every test function.
    """
    if pytest.selenium_driver is None:
        pytest.selenium_driver = get_selenium_webdriver(pytestconfig.getoption('driver'),
                                                        pytest.browser,
                                                        pytestconfig.getoption("--selenium-host"),
                                                        pytestconfig.getoption("--selenium-port"),
                                                        pytestconfig.getoption("--base-url"),
                                                        pytest.browserstack_user,
                                                        pytest.browserstack_token,
                                                        pytest.browser_resolution,
                                                        re.match(r'<module \'([^\']+)\'', str(request.module)).group(1),
                                                        re.match(r'<function ([^ ]+)', str(request.function)).group(1))


def get_selenium_webdriver(driver_type,  # "remote" or you are local. Actually we only handle 'remote'
                           browser_name,
                           selenium_driver_host,
                           selenium_driver_port,
                           project_name,
                           browserstack_user,
                           browserstack_token,
                           browser_resolution,
                           test_module,
                           test_function) -> selenium.webdriver.remote.webdriver.WebDriver:
    """Return a selenium webdriver instance.

    Used by fixtures.selenium.get_webdriver.
    @return selenium.webdriver.remote.webdriver.WebDriver
    """
    driver_instance = None
    if driver_type == "remote":
        build = 'Build ' + date.today().strftime("%d.%m.%Y")
        project = project_name
        module = test_module
        function = test_function
        test_session = os.environ["TESTSET"] + "." + module + "." + function

        # By default, We assume we run on Browserstack.
        # If not, we overwrite it in the 'else' path below, for local chrome/firefox execution
        # remote_driver = 'http://simonwerling1:<EMAIL>:80/wd/hub'
        remote_driver = "http://%s:%s@%s:%s/wd/hub" % (
            browserstack_user, browserstack_token, selenium_driver_host, selenium_driver_port)

        # Capabilities for BrowserStack are generated by the following page
        # https://www.browserstack.com/automate/capabilities
        # 'browser' overrides 'browserName'
        # 'os' overrides 'platform'

        desired_cap = None
        browser_options = None
        if browser_name == "edge":
            # Running on BrowserStack
            desired_cap = {
                'bstack:options': {
                    "os": "Windows",
                    "osVersion": "11",
                    "projectName": project,
                    "buildName": build,
                    "sessionName": test_session,
                    "local": "false",
                    "video": "true",
                    "seleniumVersion": "4.23.0",
                    "resolution": browser_resolution
                },
                "browser": "edge",
                "browserName": "edge",
                "browserVersion": "128.0",
                "browser_version": "128.0",
                "acceptSslCerts": True
            }
        elif browser_name == "chrome_macos":
            # Running on BrowserStack

            desired_cap = {
                'bstack:options': {
                    "os": "OS X",
                    "osVersion": "Sequoia",
                    "local": "false",
                    "seleniumVersion": "4.23.0",
                    "resolution": browser_resolution,
                    "projectName": project,
                    "buildName": build,
                    "sessionName": test_session,
                },
                "browserName": "Chrome",
                "browserVersion": "128.0",
                'acceptInsecureCerts': True,
                "acceptSslCerts": True
            }
        elif browser_name == "firefox_win":
            # Running on BrowserStack
            desired_cap = {
                'bstack:options': {
                    "os": "Windows",
                    "osVersion": "11",
                    "local": "false",
                    "seleniumVersion": "4.23.0",
                    "resolution": browser_resolution,
                    "projectName": project,
                    "buildName": build,
                    "sessionName": test_session,
                },
                "browserName": "Firefox",
                "browserVersion": "130.0",
                "acceptSslCerts": True
            }
        elif browser_name == "safari_ipad":
            # Running on BrowserStack
            # No chance to choose resolution on BrowserStack for the real ipad device.
            # We update pytest.browser_resolution for ipad in image_compare_helper.screen_test

            # More often used Pro 11 version
            # Use portrait (instead of landscape) orientation because of flaky behavior.
            # See comment below for ipad 12.9 caps

            desired_cap = {
                'bstack:options': {
                    "osVersion": "17",
                    "deviceName": "iPad Pro 13 2024",
                    "realMobile": "true",
                    "projectName": project,
                    "buildName": build,
                    "sessionName": test_session,
                    "appiumVersion": "1.21.0",
                    "local": "false",
                },
                "acceptSslCerts": True
            }

            # landscape on bigger ipad 12.9 pro
            # flaky behavior because of landscape orientation
            # delay on switch from portrait to landscape
            # ToDo: Fix that
        elif browser_name == "safari_macos":
            # Running on BrowserStack
            desired_cap = {
                'bstack:options': {
                    "os": "OS X",
                    "osVersion": "Sequoia",
                    "browserVersion": "18.0",
                    "resolution": browser_resolution,
                    "projectName": project,
                    "buildName": build,
                    "sessionName": test_session,
                    "local": "false",
                    "seleniumVersion": "4.23.0",
                    "networkLogs": "true",
                    "acceptInsecureCerts": True,
                    'safari': {"allowCookies": "true"}
                },
                'browserstack.acceptInsecureCerts': True,
                "acceptSslCerts": True,
                "acceptInsecureCerts": True,
                "browserName": "Safari",
            }
        else:
            # chrome or firefox - running in local compose stack
            remote_driver = "http://%s:%s/wd/hub" % (
                selenium_driver_host, selenium_driver_port)

        # hub.docker seleniumHQ Image
        if browser_name == 'chrome':
            chrome_options = selenium.webdriver.ChromeOptions()
            chrome_options.add_argument("--test-type")
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            # chrome_options.add_argument('--disable-gpu')
            # Run in headless mode inside the gitlab jobs
            if os.getenv("CI_COMMIT_SHORT_SHA", "") != "" and os.getenv("CI_COMMIT_TAG", "") != "":
                PYTEST_LOGGER.info("Run Chrome headless -> we are in pipeline context")
                chrome_options.add_argument("--headless=new")
            # chrome_options.add_argument('--disable-infobars')  # Deprecated
            # Disable info box "chrome controlled by automated ..."
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            disable_pwd_manager = {"profile.password_manager_enabled": False,
                                   "credentials_enable_service": False}
            chrome_options.add_experimental_option("prefs", disable_pwd_manager)

            # Since selenium 4, capabilities are passed by the browser options
            chrome_options.set_capability("goog:loggingPrefs", {'performance': 'ALL',
                                                                'browser': 'ALL'})
            chrome_options.set_capability("browserName", browser_name)
            chrome_options.set_capability("platformName", "Linux")
            chrome_options.platform_name = "Linux"
            chrome_options.project_name = project
            chrome_options.build_name = build
            chrome_options.session_name = test_session
            chrome_options.browser_name = browser_name

            driver_instance = selenium.webdriver.Remote(
                command_executor=remote_driver,
                options=chrome_options)

        elif browser_name == "firefox":  # hub.docker seleniumHQ Image
            # this should be firefox
            # ToDo: here we need to set the capabilities (like chrome above)
            #       and we need to check the setting for retrieving the
            #       performance log for cookie, redirect tests.
            # Code below need to be refactored -> goog:loggingPrefs ...
            # Remark: at this time 15.10.2021, it seems not to be straight
            #         forward to capture network logs for firefox in the same
            #         format, as for chrome. I recommend to run tests with
            #         cookie, redirect checks only for chrome.
            firefox_options = selenium.webdriver.FirefoxOptions()

            # Since selenium 4, capabilities are passed by the browser options
            firefox_options.set_capability("browserName", browser_name)
            firefox_options.set_capability("platformName", "Linux")
            firefox_options.platform_name = "Linux"
            firefox_options.project_name = project
            firefox_options.build_name = build
            firefox_options.session_name = test_session
            firefox_options.browser_name = browser_name
            firefox_options.accept_insecure_certs = True

            driver_instance = selenium.webdriver.Remote(
                command_executor=remote_driver,
                options=firefox_options)

        else:  # e.g. browserstack(-local) chrome_bs, firefox_bs, safari_macos, edge
            bs_local_env = os.environ["BROWSERSTACK_LOCAL"]
            if bs_local_env == "1" or bs_local_env.lower() == "true":
                bstack_options = desired_cap["bstack:options"]
                bstack_options["local"] = "true"
                bstack_options["acceptInsecureCerts"] = "true"
                # desired_cap["bstack:options"] = bstack_options

            if browser_name == "safari_macos" or browser_name == "safari_ipad":
                options = SafariOptions.Options()
            elif browser_name == "firefox_win":
                options = FirefoxOptions.Options()
            elif browser_name == "edge":
                options = EdgeOptions.Options()
            elif browser_name == "chrome_macos":
                options = ChromeOptions.Options()

            options.set_capability('bstack:options', bstack_options)
            options.accept_insecure_certs = True
            driver_aquired = False
            for i in range(3):
                try:
                    driver_instance = selenium.webdriver.Remote(command_executor=remote_driver,
                                                                # desired_capabilities=desired_cap,
                                                                options=options)
                    driver_aquired = True
                    break
                except:
                    # Just re-try - sometimes we have flaky behaviour when getting the browserstack driver connection
                    pass

            # Trigger a last time to get the exception in the log
            if not driver_aquired:
                driver_instance = selenium.webdriver.Remote(command_executor=remote_driver,
                                                            # desired_capabilities=desired_cap,
                                                            options=options)

            # Get session info from browserstack
            bs_session_info = driver_instance.execute_script('browserstack_executor: {"action": "getSessionDetails"}')

            # Save session details for webapp frontend
            # E.g. Embed the video and grab the video url from session info below

            # Save file in container root
            # ToDo: add branch name in session file name
            # e.g. recording_temp.test_recordings.local.test_prototype_1649324405947.test_prototype_1649324405947.chrome.json
            # bs_session_info_filepath = '/' + bs_session_info["name"] + '.' + browser_name + '.json'
            bs_session_info_folderpath = os.path.join(os.environ["KLICKTIPP_LOCAL_ROOT"], "test", "e2e", "browserstack_sessions")
            if not os.path.exists(bs_session_info_folderpath):
                os.mkdir(bs_session_info_folderpath)

            bs_session_info_filepath = os.path.join(bs_session_info_folderpath, 'bs.session.' + browser_name + '.json')

            if os.path.exists(bs_session_info_filepath):
                os.remove(bs_session_info_filepath)
            with open(bs_session_info_filepath, 'w') as f:
                json.dump(bs_session_info, f, ensure_ascii=True)

        PYTEST_LOGGER.info("User agent string: " + driver_instance.execute_script("return navigator.userAgent"))

        driver_instance.implicitly_wait(10)
        driver_instance.set_page_load_timeout(10)
        driver_instance.set_script_timeout(20)

    return driver_instance


__author__ = "Simon Werling"
__copyright__ = "Copyright 2024, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

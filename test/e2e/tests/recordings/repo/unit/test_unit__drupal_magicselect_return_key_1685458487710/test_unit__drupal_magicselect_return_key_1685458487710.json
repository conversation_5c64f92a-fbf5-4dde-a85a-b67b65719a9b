{"id": *************, "displayName": "Unit::<PERSON><PERSON><PERSON> magicselect return key", "description": "Type some chars into magic select and press enter to accept string as new label.", "status": "recorded", "data": {"url": "https://www-e2e.ktlocal.com/", "testCaseDependency": 0, "testCaseDataCreation": [{"class": "E2eDataReset", "params": {}}, {"class": "E2eDataAccount", "params": {"userGroupId": "31", "roleNames": ["Enterprise"]}}]}, "steps": [{"command": "click", "target": "//div[@data-e2e-id='main-2']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='main-2::tools-me-outbound::tools-me-outbound-add']", "targetCss": "", "value": "", "special": "trigger-js-click"}, {"command": "redirect-wait", "target": "", "targetCss": "", "value": ""}, {"command": "click", "target": "//div[@id='ms-sel-ctn-0']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@id='ms-input-0']", "targetCss": "", "value": "my label", "special": ""}, {"command": "key-press", "target": "", "targetCss": "", "value": "return", "special": ""}, {"command": "assert-element", "target": "//div[@data-e2e-id='ms-ctn-0::my-label'][text()='my label']", "targetCss": "", "value": ""}], "recordingSteps": [{"command": "click", "target": "//div[@data-e2e-id='main-2']", "targetCSS": "nav#top-menu.navbar.navbar-default.navbar-fixed-top.navbar-inverse div.container div.collapse.navbar-collapse.navbar-responsive-collapse ul.nav.navbar-nav li.dropdown.open a.dropdown-toggle", "value": {"value": "Automation", "tagName": "a", "attributes": [{"value": "#", "text": "href"}, {"value": "dropdown-toggle", "text": "class"}, {"value": "dropdown", "text": "data-toggle"}, {"value": "main-2", "text": "data-e2e-id"}, {"value": "true", "text": "aria-expanded"}, {"value": "Automation", "text": "innerText"}], "selectOptions": []}, "timestamp": 1685458512605, "execute": [{"command": "click", "target": "//div[@data-e2e-id='main-2']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1685458512605.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='main-2::tools-me-outbound::tools-me-outbound-add']", "targetCSS": "nav#top-menu.navbar.navbar-default.navbar-fixed-top.navbar-inverse div.container div.collapse.navbar-collapse.navbar-responsive-collapse ul.nav.navbar-nav li.dropdown ul.dropdown-menu li.dropdown-submenu ul.dropdown-menu li a", "value": {"value": "Create Outbound", "tagName": "a", "attributes": [{"value": "/tools/me/outbound/add", "text": "href"}, {"value": "main-2::tools-me-outbound::tools-me-outbound-add", "text": "data-e2e-id"}, {"value": "trigger-js-click", "text": "data-e2e-special"}, {"value": "Create Outbound", "text": "innerText"}], "selectOptions": []}, "timestamp": 1685458514517, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-2::tools-me-outbound::tools-me-outbound-add']", "targetCss": "", "value": "", "special": "trigger-js-click"}], "special": "trigger-js-click", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1685458514517.jpg"}, {"command": "redirect-wait", "target": "", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1685458514864, "screenshot": [], "execute": [{"command": "redirect-wait", "target": "", "targetCss": "", "value": ""}], "special": "", "somewhereReason": ""}, {"command": "click", "target": "//div[@id='ms-sel-ctn-0']", "targetCSS": "div#ms-sel-ctn-0.ms-sel-ctn", "value": {"value": "", "tagName": "div", "attributes": [{"value": "ms-sel-ctn-0", "text": "id"}, {"value": "ms-sel-ctn", "text": "class"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1685458517095, "execute": [{"command": "click", "target": "//div[@id='ms-sel-ctn-0']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1685458517095.jpg"}, {"command": "text-input", "target": "//input[@id='ms-input-0']", "targetCSS": "input#ms-input-0", "value": {"value": "my label", "tagName": "input.text", "attributes": [{"value": "ms-input-0", "text": "id"}, {"value": "text", "text": "type"}, {"value": "", "text": "class"}, {"value": "", "text": "value"}, {"value": "width: 1010.31px;", "text": "style"}, {"value": "off", "text": "autocomplete"}, {"value": "013731d07fd03f43", "text": "data-dashlane-rid"}, {"value": "other", "text": "data-form-type"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1685458522267, "execute": [{"command": "text-input", "target": "//input[@id='ms-input-0']", "targetCss": "", "value": "my label", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1685458522267.jpg"}, {"command": "key-enter", "target": "//input[@id='ms-input-0']", "targetCSS": "input#ms-input-0", "value": {"value": "my label", "tagName": "input.text", "attributes": [{"value": "ms-input-0", "text": "id"}, {"value": "text", "text": "type"}, {"value": "", "text": "class"}, {"value": "", "text": "value"}, {"value": "width: 1010.31px;", "text": "style"}, {"value": "off", "text": "autocomplete"}, {"value": "013731d07fd03f43", "text": "data-dashlane-rid"}, {"value": "other", "text": "data-form-type"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1685458522553, "screenshot": [], "execute": [{}], "special": "", "somewhereReason": ""}, {"command": "assert-element", "target": "//div[@data-e2e-id='ms-ctn-0::my-label']", "targetCSS": "div#ms-sel-ctn-0.ms-sel-ctn div.ms-sel-item.", "value": {"value": "my label", "tagName": "div", "attributes": [{"value": "ms-sel-item ", "text": "class"}, {"value": "ms-ctn-0::my-label", "text": "data-e2e-id"}, {"value": "my label", "text": "innerText"}], "selectOptions": []}, "timestamp": 1685458524889, "execute": [{"command": "assert-attribute:innerText", "xPath": "//div[@data-e2e-id='ms-ctn-0::my-label']", "attribute": "innerText", "value": "my label", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1685458524889.jpg"}], "datasets": {"url": "https://www-e2e.ktlocal.com/", "testCaseDependency": 0, "testCaseDataCreation": [], "roleName": "Enterprise", "userGroupId": "31"}, "machineName": "test_unit__drupal_magicselect_return_key_*************", "repoPath": "unit", "repoReady": true, "pipelineReady": true, "zephyrTest": "T379", "zephyrTestUrl": "https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-T379"}
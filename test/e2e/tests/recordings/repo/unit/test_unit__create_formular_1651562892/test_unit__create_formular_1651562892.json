{"id": 1651562892, "machineName": "test_unit__create_formular_1651562892", "displayName": "Unit::Create Formular", "description": "Creates formular and adds manual tag.", "data": {"url": "https://www-e2e.ktlocal.com/listbuilding/me/form-custom/add", "testCaseDependency": 1651563386, "testCaseDataCreation": []}, "steps": [{"command": "click", "target": "//input[@id='edit-name']", "targetCss": "input#edit-name.edit-Name.required.form-control.form-text.required", "value": 0, "special": ""}, {"command": "text-input", "target": "//input[@id='edit-name']", "targetCss": "input#edit-name.edit-Name.required.form-control.form-text.required", "value": "Listbuilding1", "special": ""}, {"command": "click", "target": "//input[@id='edit-submit']", "targetCss": "input#edit-submit.btn.btn-submit.btn-icon-arrow-white.btn-green.form-submit", "value": 0, "special": ""}, {"command": "redirect", "target": "", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@id='ms-input-1']", "targetCss": "input#ms-input-1", "value": 0, "special": ""}, {"command": "text-input", "target": "//input[@id='ms-input-1']", "targetCss": "input#ms-input-1", "value": "tag1", "special": ""}, {"command": "click", "target": "//html[contains(@class, 'js')]//body[contains(@class, 'lang-en') and contains(@class, 'form-single-submit-processed')]", "targetCss": "html.js body.lang-en.form-single-submit-processed", "value": 0, "special": ""}, {"command": "click", "target": "//input[@id='edit-save']", "targetCss": "input#edit-save.btn.btn-submit.btn-icon-arrow-white.btn-green.form-submit", "value": 0, "special": ""}, {"command": "redirect", "target": "", "targetCss": "", "value": "", "special": ""}], "screenshots": {}, "repoPath": "unit", "repoReady": true, "pipelineReady": true, "zephyrTest": "T363", "zephyrTestUrl": "https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-T363"}
{"id": **********, "machineName": "test_unit__create_newsletter_**********", "displayName": "Unit::C<PERSON> Newsletter", "description": "This test creates a new newsletter!", "data": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [{"class": "E2eDataReset", "params": {}}, {"class": "E2eDataAccount", "params": {"userGroupId": "43", "roleNames": ["Enterprise"], "tierInAccount": "10000"}}]}, "steps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-newsletter-me::application-me-campaign-newsletter-email-me-create']", "targetCss": "", "value": "", "special": "trigger-js-click"}, {"command": "click", "target": "//button[@data-e2e-id='editor-select-btn-rt']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "My new newsletter", "special": ""}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//a[@data-e2e-id='breadcrumbs-ele-2']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//a[@data-e2e-id='breadcrumbs-ele-2'][contains(text(),'Newsletter: My new newsletter')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-email-settings-wrapper//kt-email-create-detail//h1[contains(text(),'Edit Newsletter Email')]", "targetCss": "", "value": "", "special": ""}], "recordingSteps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCSS": "html body kt-app kt-menu.kt-menu div.kt-menu-wrapper.fixed div.kt-menu-container div.kt-menu-item.noselect.kt-menu-item-header.e2e-mark-element.is-active", "value": {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "tagName": "div", "attributes": [{"value": "", "text": "_ngcontent-ng-c2554274691"}, {"value": "kt-menu-item noselect kt-menu-item-header e2e-mark-element is-active", "text": "class"}, {"value": "main-3", "text": "data-e2e-id"}, {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "text": "innerText"}], "selectOptions": []}, "timestamp": 1724744689627, "execute": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/**********/1724744689627.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-newsletter-me::application-me-campaign-newsletter-email-me-create']", "targetCSS": "div.kt-menu-dropdown.min-width.ng-star-inserted div.kt-menu-child.noselect.ng-star-inserted div.kt-menu-dropdown.child.ng-star-inserted div.kt-menu-child-child.noselect.ng-star-inserted a.e2e-mark-element", "value": {"value": "Create E-Mail Newsletter", "tagName": "a", "attributes": [{"value": "", "text": "_ngcontent-ng-c2554274691"}, {"value": "https://app.ktlocal.com/app/campaign/newsletter-email/14/create", "text": "href"}, {"value": "main-3::application-me-campaign-newsletter-me::application-me-campaign-newsletter-email-me-create", "text": "data-e2e-id"}, {"value": "trigger-js-click", "text": "data-e2e-special"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Create E-Mail Newsletter", "text": "innerText"}], "selectOptions": []}, "timestamp": 1724744696044, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-newsletter-me::application-me-campaign-newsletter-email-me-create']", "targetCss": "", "value": "", "special": "trigger-js-click"}], "special": "trigger-js-click", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/**********/1724744696044.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.ng-untouched.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element.ng-dirty.ng-valid", "value": {"value": "M", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element ng-dirty ng-valid", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "Name", "text": "placeholder"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1724744723056, "execute": [{"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/**********/1724744723056.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "My new newsletter", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "Name", "text": "placeholder"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1724744730853, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "My new newsletter", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/**********/1724744730853.jpg"}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1724744732602, "execute": [{"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/**********/1724744732602.jpg"}, {"command": "click", "target": "//mat-radio-button[@data-e2e-id='editorType-option-1']", "targetCSS": "mat-radio-button#mat-radio-6.mat-mdc-radio-button.mat-accent.ng-star-inserted.cdk-focused.cdk-mouse-focused.e2e-mark-element.mat-mdc-radio-checked", "value": {"value": "Rich Text", "tagName": "mat-radio-button", "attributes": [{"value": "mat-mdc-radio-button mat-accent ng-star-inserted cdk-focused cdk-mouse-focused e2e-mark-element mat-mdc-radio-checked", "text": "class"}, {"value": "editorType-option-1", "text": "data-e2e-id"}, {"value": "mat-radio-6", "text": "id"}, {"value": "Rich Text", "text": "innerText"}], "selectOptions": []}, "timestamp": 1724744745093, "execute": [{"command": "click", "target": "//mat-radio-button[@data-e2e-id='editorType-option-1']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/**********/1724744745093.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCSS": "kt-item-create.ng-star-inserted kt-newsletter-create-detail.ng-star-inserted form.ng-invalid.ng-dirty.ng-touched div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Create Newsletter", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Create Newsletter", "text": "innerText"}], "selectOptions": []}, "timestamp": 1724744751023, "execute": [{"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/**********/1724744751023.jpg"}, {"command": "assert-element", "target": "//a[@data-e2e-id='breadcrumbs-ele-2']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-breadcrumb div.breadcrumbs-container ol.breadcrumbs li.ng-star-inserted span.breadcrumb-current-route.breadcrumb-history-link.ng-star-inserted a.ng-star-inserted.e2e-mark-element", "value": {"value": "Newsletter: My new newsletter", "tagName": "a", "attributes": [{"value": "breadcrumbs-ele-2", "text": "data-e2e-id"}, {"value": "/app/campaign/newsletter-email/14/9/statistics", "text": "href"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Newsletter: My new newsletter", "text": "innerText"}], "selectOptions": []}, "timestamp": 1724744769855, "execute": [{"command": "assert-xpath", "target": "//a[@data-e2e-id='breadcrumbs-ele-2']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-attribute:innerText", "xPath": "//a[@data-e2e-id='breadcrumbs-ele-2']", "attribute": "innerText", "value": "Newsletter: My new newsletter", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/**********/1724744769855.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-email-settings-wrapper//kt-email-create-detail//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-settings-wrapper.ng-star-inserted kt-email-create-detail h1.ng-star-inserted.e2e-mark-element", "value": {"value": "Edit Newsletter Email", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-ng-c3415663012"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Edit Newsletter Email", "text": "innerText"}], "selectOptions": []}, "timestamp": *************, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-email-settings-wrapper//kt-email-create-detail//h1", "attribute": "innerText", "value": "Edit Newsletter Email", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/**********/*************.jpg"}], "datasets": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [], "roleNames": ["Enterprise"], "userGroupId": "43", "tierInAccount": "10000"}, "repoPath": "unit", "repoReady": true, "pipelineReady": true, "zephyrTest": "T364", "zephyrTestUrl": "https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-T364"}
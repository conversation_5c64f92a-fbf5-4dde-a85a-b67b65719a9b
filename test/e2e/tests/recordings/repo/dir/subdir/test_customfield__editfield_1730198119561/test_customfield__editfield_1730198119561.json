{"id": *************, "displayName": "Customfield::EditField", "description": "Create an customfield, edit, then update and check all properties", "status": "recorded", "data": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [{"class": "E2eDataReset", "params": {}}, {"class": "E2eDataAccount", "params": {"userGroupId": "43", "roleNames": ["Enterprise"], "tierInAccount": "10000"}}]}, "steps": [{"command": "click", "target": "//div[@data-e2e-id='main-0']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='main-0::application-me-customfield-overview-me']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCss": "", "value": "EditField", "special": ""}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-label-chip-list-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-label-chip-list-input']", "targetCss": "", "value": "EditField", "special": ""}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}, {"command": "click", "target": "//mat-select[@data-e2e-id='customfield-type-select']//div//div", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-option[@data-e2e-id='customfield-type-select-option-6']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='customfield-multivalue-radio']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='customfieldCreateBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCss": "", "value": "EditField", "special": "assert-angular-text-field-value"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='customfield-label::editfield']//span//span[contains(text(),'EditField')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='customfield-category-chip-list-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-category-chip-list-input']", "targetCss": "", "value": "EditCategory", "special": ""}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-email-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-email-input']", "targetCss": "", "value": "emailParam", "special": ""}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "fbParam", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='customfield-customfields-chip-list-input']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-option[@data-e2e-id='customfield-customfields::res-item::birthday']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='customfieldSaveBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//kt-table-cell//a[contains(text(),'EditField')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='customfield-category::editcategory']//span//span[contains(text(),'EditCategory')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-email-input']", "targetCss": "", "value": "emailParam", "special": "assert-angular-text-field-value"}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "fbParam", "special": "assert-angular-text-field-value"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='customfield-customfields::birthday']//span//span[contains(text(),'Birthday')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCss": "", "value": "EditField22", "special": ""}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}, {"command": "click", "target": "//button[@data-e2e-id='customfield-label::close::editfield']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "fbParam2", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='customfield-customfields-chip-list-input']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-option[@data-e2e-id='customfield-customfields::res-item::firstname']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='customfieldSaveBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//kt-table-cell//a[contains(text(),'EditField22')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCss": "", "value": "EditField22", "special": "assert-angular-text-field-value"}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "fbParam2", "special": "assert-angular-text-field-value"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='customfield-customfields::firstname']//span//span[contains(text(),'Firstname')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='customfieldCancelBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//kt-table-cell//a[contains(text(),'EditField22')]", "targetCss": "", "value": "", "special": ""}], "recordingSteps": [{"command": "click", "target": "//div[@data-e2e-id='main-0']", "targetCSS": "html body kt-app kt-menu.kt-menu.ng-star-inserted div.kt-menu-wrapper.fixed div.kt-menu-container div.kt-menu-item.noselect.kt-menu-item-header.ng-star-inserted.e2e-mark-element.is-active", "value": {"value": "Kontakte \nKontakte verwalten\nContact Record\nFeedback", "tagName": "div", "attributes": [{"value": "", "text": "_ngcontent-ng-c2913637593"}, {"value": "kt-menu-item noselect kt-menu-item-header ng-star-inserted e2e-mark-element is-active", "text": "class"}, {"value": "main-0", "text": "data-e2e-id"}, {"value": "Kontakte \nKontakte verwalten\nContact Record\nFeedback", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198302078, "execute": [{"command": "click", "target": "//div[@data-e2e-id='main-0']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198302078.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='main-0::application-me-customfield-overview-me']", "targetCSS": "div.kt-menu-dropdown.min-width.ng-star-inserted div.kt-menu-child.noselect.ng-star-inserted a.ng-star-inserted.e2e-mark-element", "value": {"value": "Contact Record", "tagName": "a", "attributes": [{"value": "", "text": "_ngcontent-ng-c2913637593"}, {"value": "https://app.ktlocal.com/app/customfield/overview/11", "text": "href"}, {"value": "main-0::application-me-customfield-overview-me", "text": "data-e2e-id"}, {"value": "", "text": "data-e2e-special"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Contact Record", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198304755, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-0::application-me-customfield-overview-me']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198304755.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCSS": "kt-customfield-overview.ng-star-inserted div.full-width.align-right.ng-star-inserted kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-accent.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Create field", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Create field", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198309563, "execute": [{"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198309563.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCSS": "input#mat-input-9.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "EditField", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-name-input", "text": "data-e2e-id"}, {"value": "Name", "text": "placeholder"}, {"value": "mat-input-9", "text": "id"}, {"value": "", "text": "required"}, {"value": "customfield-name-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198315074, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCss": "", "value": "EditField", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198315074.jpg"}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div//section", "targetCSS": "html body kt-app div.kt-outer-container.ng-star-inserted ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70.e2e-mark-element", "value": {"value": "StartseiteCustomfieldCreate\nTitle\nBase Settings Title\nName*\nLabels\nType*\nZeile\nLabel\nBack\nCreate", "tagName": "section", "attributes": [{"value": "", "text": "_ngcontent-ng-c3423068296"}, {"value": "kt-drupal-theme kt-module-wrapper mb-70 e2e-mark-element", "text": "class"}, {"value": "StartseiteCustomfieldCreate\nTitle\nBase Settings Title\nName*\nLabels\nType*\nZeile\nLabel\nBack\nCreate", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198316250, "execute": [{"command": "click", "target": "//html//body//kt-app//div//ng-component//div//section", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198316250.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-label-chip-list-input']", "targetCSS": "input#mat-input-8.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-autocomplete-trigger.mat-mdc-form-field-input-control.cdk-text-field-autofill-monitored.ng-valid.e2e-mark-element.ng-touched.ng-dirty", "value": {"value": "Ed", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-autocomplete-trigger mat-mdc-form-field-input-control cdk-text-field-autofill-monitored ng-valid e2e-mark-element ng-touched ng-dirty", "text": "class"}, {"value": "customfield-label-chip-list-input", "text": "data-e2e-id"}, {"value": "mat-input-8", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198317587, "execute": [{"command": "click", "target": "//input[@data-e2e-id='customfield-label-chip-list-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198317587.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-label-chip-list-input']", "targetCSS": "input#mat-input-8.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-autocomplete-trigger.mat-mdc-form-field-input-control.cdk-text-field-autofill-monitored.ng-valid.ng-touched.ng-dirty.e2e-mark-element", "value": {"value": "EditField", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-autocomplete-trigger mat-mdc-form-field-input-control cdk-text-field-autofill-monitored ng-valid ng-touched ng-dirty e2e-mark-element", "text": "class"}, {"value": "customfield-label-chip-list-input", "text": "data-e2e-id"}, {"value": "mat-input-8", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198320385, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='customfield-label-chip-list-input']", "targetCss": "", "value": "EditField", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198320385.jpg"}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1730198321287, "execute": [{"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198321287.jpg"}, {"command": "dropdown-angular-input", "target": "//mat-select[@data-e2e-id='customfield-type-select']//div//div", "targetCSS": "mat-option#mat-option-19.mat-mdc-option.mdc-list-item.ng-tns-c1711764913-38.ng-star-inserted.e2e-mark-element.mdc-list-item--selected.mat-mdc-option-active", "value": {"value": "//mat-option[@data-e2e-id='customfield-type-select-option-6']", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1730198324810, "execute": [{"command": "dropdown-angular-input", "target": "//mat-select[@data-e2e-id='customfield-type-select']//div//div", "targetCss": "", "value": "//mat-option[@data-e2e-id='customfield-type-select-option-6']", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198324810.jpg"}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='customfield-multivalue-radio']", "targetCSS": "mat-checkbox#customfield-multivalue-radio.mat-mdc-checkbox.mat-accent.ng-untouched.ng-valid.e2e-mark-element.ng-dirty", "value": {"value": "Label", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-valid e2e-mark-element ng-dirty", "text": "class"}, {"value": "customfield-multivalue-radio", "text": "data-e2e-id"}, {"value": "customfield-multivalue-radio", "text": "id"}, {"value": "Label", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198327030, "execute": [{"command": "click", "target": "//mat-checkbox[@data-e2e-id='customfield-multivalue-radio']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198327030.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='customfieldCreateBtn']", "targetCSS": "html body kt-app div.kt-outer-container.ng-star-inserted ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-customfield-create-detail.ng-star-inserted form.ng-dirty.ng-valid.ng-touched div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Create", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Create", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198328830, "execute": [{"command": "click", "target": "//button[@data-e2e-id='customfieldCreateBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198328830.jpg"}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCSS": "input#mat-input-16.mat-mdc-input-element.custom-autofill.ng-untouched.ng-pristine.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-valid.e2e-mark-element", "value": {"value": "EditField", "tagName": "input.text", "attributes": [{"value": "EditField", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched ng-pristine mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-name-input", "text": "data-e2e-id"}, {"value": "Name", "text": "placeholder"}, {"value": "mat-input-16", "text": "id"}, {"value": "", "text": "required"}, {"value": "customfield-name-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198334081, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@data-e2e-id='customfield-name-input']", "attribute": "value", "value": "EditField", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198334081.jpg"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='customfield-label::editfield']//span//span", "targetCSS": "mat-chip-row#mat-mdc-chip-1.mat-mdc-chip.mat-mdc-chip-row.mdc-evolution-chip.mat-primary.mdc-evolution-chip--with-trailing-action.mat-mdc-standard-chip.mat-mdc-chip-with-trailing-icon.ng-star-inserted.cdk-focused.cdk-mouse-focused span.mdc-evolution-chip__action.mat-mdc-chip-action.mdc-evolution-chip__cell.mdc-evolution-chip__cell--primary.mdc-evolution-chip__action--primary span.mdc-evolution-chip__text-label.mat-mdc-chip-action-label.e2e-mark-element", "value": {"value": "EditField", "tagName": "span", "attributes": [{"value": "mdc-evolution-chip__text-label mat-mdc-chip-action-label e2e-mark-element", "text": "class"}, {"value": "EditField", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198337045, "execute": [{"command": "assert-attribute:innerText", "xPath": "//mat-chip-row[@data-e2e-id='customfield-label::editfield']//span//span", "attribute": "innerText", "value": "EditField", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198337045.jpg"}, {"command": "assert-element", "target": "//mat-form-field[@data-e2e-id='customfield-type']", "targetCSS": "div#cdk-accordion-child-3.mat-expansion-panel-content.ng-tns-c857250080-39.ng-trigger.ng-trigger-bodyExpansion div.mat-expansion-panel-body.ng-tns-c857250080-39 div.ng-tns-c857250080-39 kt-select.ng-untouched.ng-pristine div.ng-star-inserted mat-form-field.mat-mdc-form-field.ng-tns-c508571215-53.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill.mat-primary.ng-untouched.ng-pristine.ng-star-inserted.mat-form-field-disabled.e2e-mark-element", "value": {"value": "Type*\nDatum", "tagName": "mat-form-field", "attributes": [{"value": "mat-mdc-form-field ng-tns-c508571215-53 mat-mdc-form-field-type-mat-select mat-form-field-appearance-fill mat-primary ng-untouched ng-pristine ng-star-inserted mat-form-field-disabled e2e-mark-element", "text": "class"}, {"value": "customfield-type", "text": "data-e2e-id"}, {"value": "Type*\nDatum", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198342081, "execute": [], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198342081.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-category-input']", "targetCSS": "input#mat-input-11.mat-mdc-input-element.mat-mdc-autocomplete-trigger.custom-autofill.ng-tns-c508571215-45.ng-untouched.ng-pristine.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-valid.e2e-mark-element", "value": {"value": "EditField", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element mat-mdc-autocomplete-trigger custom-autofill ng-tns-c508571215-45 ng-untouched ng-pristine mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "customfield-category-input", "text": "data-e2e-id"}, {"value": "Categories", "text": "placeholder"}, {"value": "mat-input-11", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "true", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-autocomplete-5", "text": "aria-controls"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198346862, "execute": [{"command": "click", "target": "//input[@data-e2e-id='customfield-category-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198346862.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-category-input']", "targetCSS": "input#mat-input-11.mat-mdc-input-element.mat-mdc-autocomplete-trigger.custom-autofill.ng-tns-c508571215-45.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-valid.ng-dirty.ng-touched.e2e-mark-element", "value": {"value": "EditCategory", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element mat-mdc-autocomplete-trigger custom-autofill ng-tns-c508571215-45 mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-valid ng-dirty ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "customfield-category-input", "text": "data-e2e-id"}, {"value": "Categories", "text": "placeholder"}, {"value": "mat-input-11", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198350693, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='customfield-category-input']", "targetCss": "", "value": "EditCategory", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198350693.jpg"}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1730198351913, "execute": [{"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198351913.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-email-input']", "targetCSS": "input#mat-input-12.mat-mdc-input-element.custom-autofill.ng-untouched.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-valid.e2e-mark-element.ng-dirty", "value": {"value": "<PERSON>ai", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-valid e2e-mark-element ng-dirty", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-email-input", "text": "data-e2e-id"}, {"value": "mat-input-12", "text": "id"}, {"value": "customfield-email-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198353535, "execute": [{"command": "click", "target": "//input[@data-e2e-id='customfield-email-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198353535.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-email-input']", "targetCSS": "input#mat-input-12.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-valid.ng-dirty.ng-touched.e2e-mark-element", "value": {"value": "emailParam", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-valid ng-dirty ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-email-input", "text": "data-e2e-id"}, {"value": "mat-input-12", "text": "id"}, {"value": "customfield-email-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198356814, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='customfield-email-input']", "targetCss": "", "value": "emailParam", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198356814.jpg"}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1730198357950, "execute": [{"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198357950.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCSS": "input#mat-input-13.mat-mdc-input-element.custom-autofill.ng-untouched.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-valid.e2e-mark-element.ng-dirty", "value": {"value": "fc", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-valid e2e-mark-element ng-dirty", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-facebook-input", "text": "data-e2e-id"}, {"value": "mat-input-13", "text": "id"}, {"value": "customfield-facebook-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198359589, "execute": [{"command": "click", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198359589.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCSS": "input#mat-input-13.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-valid.ng-dirty.ng-touched.e2e-mark-element", "value": {"value": "fbParam", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-valid ng-dirty ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-facebook-input", "text": "data-e2e-id"}, {"value": "mat-input-13", "text": "id"}, {"value": "customfield-facebook-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198364306, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "fbParam", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198364306.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-customfields-chip-list-input']", "targetCSS": "input#mat-input-14.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-autocomplete-trigger.mat-mdc-form-field-input-control.ng-pristine.cdk-text-field-autofill-monitored.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "fbParam", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-autocomplete-trigger mat-mdc-form-field-input-control ng-pristine cdk-text-field-autofill-monitored ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "customfield-customfields-chip-list-input", "text": "data-e2e-id"}, {"value": "mat-input-14", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "true", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-autocomplete-6", "text": "aria-controls"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198365593, "execute": [{"command": "click", "target": "//input[@data-e2e-id='customfield-customfields-chip-list-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198365593.jpg"}, {"command": "click", "target": "//mat-option[@data-e2e-id='customfield-customfields::res-item::geburtstag']", "targetCSS": "mat-option#mat-option-37.mat-mdc-option.mdc-list-item.ng-tns-c3964489799-50.ng-star-inserted span.mdc-list-item__primary-text span.e2e-mark-element", "value": {"value": "Geburtstag", "tagName": "span", "attributes": [{"value": "e2e-mark-element", "text": "class"}, {"value": "Geburtstag", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198372377, "execute": [{"command": "click", "target": "//mat-option[@data-e2e-id='customfield-customfields::res-item::geburtstag']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198372377.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='customfieldSaveBtn']", "targetCSS": "html body kt-app div.kt-outer-container.ng-star-inserted ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-customfield-create-detail.ng-star-inserted form.ng-touched.ng-dirty div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Update", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Update", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198375250, "execute": [{"command": "click", "target": "//button[@data-e2e-id='customfieldSaveBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198375250.jpg"}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-category-input']", "targetCSS": "input#mat-input-11.mat-mdc-input-element.mat-mdc-autocomplete-trigger.custom-autofill.ng-tns-c508571215-45.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.ng-valid.e2e-mark-element", "value": {"value": "EditCategory", "tagName": "input.text", "attributes": [{"value": "EditCategory", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element mat-mdc-autocomplete-trigger custom-autofill ng-tns-c508571215-45 mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "customfield-category-input", "text": "data-e2e-id"}, {"value": "Categories", "text": "placeholder"}, {"value": "mat-input-11", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "true", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-autocomplete-5", "text": "aria-controls"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198380577, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@data-e2e-id='customfield-category-input']", "attribute": "value", "value": "EditCategory", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198380577.jpg"}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-email-input']", "targetCSS": "input#mat-input-12.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.ng-valid.e2e-mark-element", "value": {"value": "emailParam", "tagName": "input.text", "attributes": [{"value": "emailParam", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-email-input", "text": "data-e2e-id"}, {"value": "mat-input-12", "text": "id"}, {"value": "customfield-email-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198383870, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@data-e2e-id='customfield-email-input']", "attribute": "value", "value": "emailParam", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198383870.jpg"}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCSS": "input#mat-input-13.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.ng-valid.e2e-mark-element", "value": {"value": "fbParam", "tagName": "input.text", "attributes": [{"value": "fbParam", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-facebook-input", "text": "data-e2e-id"}, {"value": "mat-input-13", "text": "id"}, {"value": "customfield-facebook-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198387092, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@data-e2e-id='customfield-facebook-input']", "attribute": "value", "value": "fbParam", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198387092.jpg"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='customfield-customfields::geburtstag']//span//span", "targetCSS": "mat-chip-row#mat-mdc-chip-4.mat-mdc-chip.mat-mdc-chip-row.mdc-evolution-chip.mat-primary.mdc-evolution-chip--with-trailing-action.mat-mdc-standard-chip.mat-mdc-chip-with-trailing-icon.ng-star-inserted.cdk-focused.cdk-mouse-focused span.mdc-evolution-chip__action.mat-mdc-chip-action.mdc-evolution-chip__cell.mdc-evolution-chip__cell--primary.mdc-evolution-chip__action--primary span.mdc-evolution-chip__text-label.mat-mdc-chip-action-label.e2e-mark-element", "value": {"value": "Geburtstag", "tagName": "span", "attributes": [{"value": "mdc-evolution-chip__text-label mat-mdc-chip-action-label e2e-mark-element", "text": "class"}, {"value": "Geburtstag", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198390112, "execute": [{"command": "assert-attribute:innerText", "xPath": "//mat-chip-row[@data-e2e-id='customfield-customfields::geburtstag']//span//span", "attribute": "innerText", "value": "Geburtstag", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198390112.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCSS": "input#mat-input-16.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-touched.ng-valid.e2e-mark-element.ng-dirty", "value": {"value": "EditField2", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-touched ng-valid e2e-mark-element ng-dirty", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-name-input", "text": "data-e2e-id"}, {"value": "Name", "text": "placeholder"}, {"value": "mat-input-16", "text": "id"}, {"value": "", "text": "required"}, {"value": "customfield-name-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198393626, "execute": [{"command": "click", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198393626.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCSS": "input#mat-input-16.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-touched.ng-valid.ng-dirty.e2e-mark-element", "value": {"value": "EditField22", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-touched ng-valid ng-dirty e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-name-input", "text": "data-e2e-id"}, {"value": "Name", "text": "placeholder"}, {"value": "mat-input-16", "text": "id"}, {"value": "", "text": "required"}, {"value": "customfield-name-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198397135, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCss": "", "value": "EditField22", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198397135.jpg"}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div//section", "targetCSS": "html body kt-app div.kt-outer-container.ng-star-inserted ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70.e2e-mark-element", "value": {"value": "StartseiteCustomfieldEditField\nEdit\nBase Settings Title\nName*\nLabels\nKeine Tags oder Labels gefunden\nAlle entfernen\nEditField\nType*\nDatum\nLabel\nAdvanced Settings Title\nCategories\nEmail\nApi\nFacebook\nCopy to customfield\nAlle entfernen\nGeburtstag\nBack\nMerge\nDelete\nUpdate", "tagName": "section", "attributes": [{"value": "", "text": "_ngcontent-ng-c3423068296"}, {"value": "kt-drupal-theme kt-module-wrapper mb-70 e2e-mark-element", "text": "class"}, {"value": "StartseiteCustomfieldEditField\nEdit\nBase Settings Title\nName*\nLabels\nKeine Tags oder Labels gefunden\nAlle entfernen\nEditField\nType*\nDatum\nLabel\nAdvanced Settings Title\nCategories\nEmail\nApi\nFacebook\nCopy to customfield\nAlle entfernen\nGeburtstag\nBack\nMerge\nDelete\nUpdate", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198398395, "execute": [{"command": "click", "target": "//html//body//kt-app//div//ng-component//div//section", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198398395.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='customfield-label::close::editfield']", "targetCSS": "mat-chip-row#mat-mdc-chip-3.mat-mdc-chip.mat-mdc-chip-row.mdc-evolution-chip.mat-primary.mdc-evolution-chip--with-trailing-action.mat-mdc-standard-chip.mat-mdc-chip-with-trailing-icon.ng-star-inserted span.mdc-evolution-chip__cell.mdc-evolution-chip__cell--trailing.ng-star-inserted button.mat-mdc-chip-remove.mat-mdc-chip-trailing-icon.mat-mdc-focus-indicator.mdc-evolution-chip__icon.mdc-evolution-chip__icon--trailing.mdc-evolution-chip__action.mat-mdc-chip-action.mdc-evolution-chip__action--trailing.e2e-mark-element", "value": {"value": "", "tagName": "button", "attributes": [{"value": "button", "text": "role"}, {"value": "", "text": "matchipremove"}, {"value": "mat-mdc-chip-remove mat-mdc-chip-trailing-icon mat-mdc-focus-indicator mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing mdc-evolution-chip__action mat-mdc-chip-action mdc-evolution-chip__action--trailing e2e-mark-element", "text": "class"}, {"value": "button", "text": "type"}, {"value": "customfield-label::close::editfield", "text": "data-e2e-id"}, {"value": "-1", "text": "tabindex"}, {"value": "false", "text": "aria-disabled"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198400241, "execute": [{"command": "click", "target": "//button[@data-e2e-id='customfield-label::close::editfield']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198400241.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCSS": "input#mat-input-13.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.ng-valid.e2e-mark-element", "value": {"value": "fbParam2", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-facebook-input", "text": "data-e2e-id"}, {"value": "mat-input-13", "text": "id"}, {"value": "customfield-facebook-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198404200, "execute": [{"command": "click", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198404200.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCSS": "input#mat-input-13.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.ng-valid.e2e-mark-element", "value": {"value": "fbParam2", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-facebook-input", "text": "data-e2e-id"}, {"value": "mat-input-13", "text": "id"}, {"value": "customfield-facebook-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198407088, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCss": "", "value": "fbParam2", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198407088.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='customfield-customfields-chip-list-input']", "targetCSS": "input#mat-input-14.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-autocomplete-trigger.mat-mdc-form-field-input-control.cdk-text-field-autofill-monitored.ng-touched.ng-dirty.ng-valid.e2e-mark-element", "value": {"value": "fbParam2", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-autocomplete-trigger mat-mdc-form-field-input-control cdk-text-field-autofill-monitored ng-touched ng-dirty ng-valid e2e-mark-element", "text": "class"}, {"value": "customfield-customfields-chip-list-input", "text": "data-e2e-id"}, {"value": "mat-input-14", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "true", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-autocomplete-6", "text": "aria-controls"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198408359, "execute": [{"command": "click", "target": "//input[@data-e2e-id='customfield-customfields-chip-list-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198408359.jpg"}, {"command": "click", "target": "//mat-option[@data-e2e-id='customfield-customfields::res-item::vorname']", "targetCSS": "mat-option#mat-option-50.mat-mdc-option.mdc-list-item.ng-star-inserted.ng-tns-c3964489799-50 span.mdc-list-item__primary-text span.e2e-mark-element", "value": {"value": "<PERSON><PERSON><PERSON>", "tagName": "span", "attributes": [{"value": "e2e-mark-element", "text": "class"}, {"value": "<PERSON><PERSON><PERSON>", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198414462, "execute": [{"command": "click", "target": "//mat-option[@data-e2e-id='customfield-customfields::res-item::vorname']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198414462.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='customfieldSaveBtn']", "targetCSS": "html body kt-app div.kt-outer-container.ng-star-inserted ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-customfield-create-detail.ng-star-inserted form.ng-touched.ng-dirty div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Update", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Update", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198417859, "execute": [{"command": "click", "target": "//button[@data-e2e-id='customfieldSaveBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198417859.jpg"}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-name-input']", "targetCSS": "input#mat-input-16.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-touched.ng-dirty.ng-valid.e2e-mark-element", "value": {"value": "EditField22", "tagName": "input.text", "attributes": [{"value": "EditField22", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-touched ng-dirty ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-name-input", "text": "data-e2e-id"}, {"value": "Name", "text": "placeholder"}, {"value": "mat-input-16", "text": "id"}, {"value": "", "text": "required"}, {"value": "customfield-name-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198424266, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@data-e2e-id='customfield-name-input']", "attribute": "value", "value": "EditField22", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198424266.jpg"}, {"command": "assert-element", "target": "//input[@data-e2e-id='customfield-facebook-input']", "targetCSS": "input#mat-input-13.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.ng-valid.e2e-mark-element", "value": {"value": "fbParam2", "tagName": "input.text", "attributes": [{"value": "fbParam2", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "customfield-facebook-input", "text": "data-e2e-id"}, {"value": "mat-input-13", "text": "id"}, {"value": "customfield-facebook-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198429837, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@data-e2e-id='customfield-facebook-input']", "attribute": "value", "value": "fbParam2", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198429837.jpg"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='customfield-customfields::vorname']//span//span", "targetCSS": "mat-chip-row#mat-mdc-chip-6.mat-mdc-chip.mat-mdc-chip-row.mdc-evolution-chip.mat-primary.mdc-evolution-chip--with-trailing-action.mat-mdc-standard-chip.mat-mdc-chip-with-trailing-icon.ng-star-inserted.cdk-focused.cdk-mouse-focused span.mdc-evolution-chip__action.mat-mdc-chip-action.mdc-evolution-chip__cell.mdc-evolution-chip__cell--primary.mdc-evolution-chip__action--primary span.mdc-evolution-chip__text-label.mat-mdc-chip-action-label.e2e-mark-element", "value": {"value": "<PERSON><PERSON><PERSON>", "tagName": "span", "attributes": [{"value": "mdc-evolution-chip__text-label mat-mdc-chip-action-label e2e-mark-element", "text": "class"}, {"value": "<PERSON><PERSON><PERSON>", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198432756, "execute": [{"command": "assert-attribute:innerText", "xPath": "//mat-chip-row[@data-e2e-id='customfield-customfields::vorname']//span//span", "attribute": "innerText", "value": "<PERSON><PERSON><PERSON>", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198432756.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='customfieldCancelBtn']", "targetCSS": "kt-customfield-create-detail.ng-star-inserted form.ng-touched.ng-dirty.ng-valid div.mt-15 kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-unthemed.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Back", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Back", "text": "innerText"}], "selectOptions": []}, "timestamp": 1730198437449, "execute": [{"command": "click", "target": "//button[@data-e2e-id='customfieldCancelBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1730198437449.jpg"}, {"command": "assert-element", "target": "//a[@data-e2e-id='table-text-r-1-c-1']", "targetCSS": "table#cdk-drop-list-3.mat-mdc-table.mdc-data-table__table.cdk-table.mat-sort.cdk-drop-list.ng-tns-c3939424804-55.cdk-drop-list-disabled tbody.mdc-data-table__content.ng-star-inserted tr.mat-mdc-row.mdc-data-table__row.cdk-row.cdk-drag.ng-tns-c3939424804-55.cdk-drag-disabled.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-name.mat-column-name.ng-tns-c3939424804-55.ng-star-inserted div.cell-content.ng-tns-c3939424804-55 div.ng-tns-c3939424804-55.no-chip.width-100.ng-star-inserted kt-table-cell.ng-tns-c3939424804-55.ng-star-inserted kt-table-text.form-flex.ng-star-inserted div.kt-text-container a.kt-two-line-ellipsis.ng-star-inserted.e2e-mark-element", "value": {"value": "EditField22", "tagName": "a", "attributes": [{"value": "", "text": "ktlineclamp"}, {"value": "kt-two-line-ellipsis ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "https://app.ktlocal.com/app/customfield/settings/11/51", "text": "href"}, {"value": "EditField22", "text": "title"}, {"value": "_self", "text": "target"}, {"value": "table-text-r-1-c-1", "text": "data-e2e-id"}, {"value": "EditField22", "text": "innerText"}], "selectOptions": []}, "timestamp": *************, "execute": [{"command": "assert-attribute:innerText", "xPath": "//a[@data-e2e-id='table-text-r-1-c-1']", "attribute": "innerText", "value": "EditField22", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/*************.jpg"}], "datasets": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [], "roleNames": ["Enterprise"], "userGroupId": "43", "tierInAccount": "10000"}, "machineName": "test_customfield__editfield_*************", "pipelineReady": true, "zephyrTest": "T414", "repoReady": true, "repoPath": "dir/subdir", "zephyrTestUrl": "https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-T414"}
{"id": *************, "displayName": "Emails::AutomationEmail::Create::DD automation email", "description": "New DD automation email and assert in email editor", "status": "recorded", "data": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [{"class": "E2eDataReset", "params": {}}, {"class": "E2eDataAccount", "params": {"userGroupId": "43", "roleNames": ["Enterprise"], "tierInAccount": "10000"}}]}, "steps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-email-overview-me::application-me-email-automation-email-me-create']", "targetCss": "", "value": "", "special": "trigger-js-click"}, {"command": "click", "target": "//button[@data-e2e-id='editor-select-btn-dnd']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "e2e new DD automation email ", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-editor']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//mat-dialog-container[@id='mat-mdc-dialog-0']//div//div//kt-template-browser-dialog//kt-modal-base//section//div//kt-tabs//div//div[contains(text(),'KlickTipp Templates')]", "targetCss": "", "value": "", "special": ""}], "recordingSteps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCSS": "html body kt-app kt-menu.kt-menu div.kt-menu-wrapper.fixed div.kt-menu-container div.kt-menu-item.noselect.kt-menu-item-header.e2e-mark-element.is-active", "value": {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "tagName": "div", "attributes": [{"value": "", "text": "_ngcontent-ng-c1926400999"}, {"value": "kt-menu-item noselect kt-menu-item-header e2e-mark-element is-active", "text": "class"}, {"value": "main-3", "text": "data-e2e-id"}, {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "text": "innerText"}], "selectOptions": []}, "timestamp": 1717422022573, "execute": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1717422022573.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-email-overview-me::application-me-email-automation-email-me-create']", "targetCSS": "div.kt-menu-dropdown.min-width.ng-star-inserted div.kt-menu-child.noselect.ng-star-inserted div.kt-menu-dropdown.child.ng-star-inserted div.kt-menu-child-child.noselect.ng-star-inserted a.e2e-mark-element", "value": {"value": "Create Campaign E-Mail", "tagName": "a", "attributes": [{"value": "", "text": "_ngcontent-ng-c1926400999"}, {"value": "https://app.ktlocal.com/app/email/automation-email/11/create", "text": "href"}, {"value": "main-3::application-me-email-overview-me::application-me-email-automation-email-me-create", "text": "data-e2e-id"}, {"value": "trigger-js-click", "text": "data-e2e-special"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Create Campaign E-Mail", "text": "innerText"}], "selectOptions": []}, "timestamp": 1717422026246, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-email-overview-me::application-me-email-automation-email-me-create']", "targetCss": "", "value": "", "special": "trigger-js-click"}], "special": "trigger-js-click", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1717422026246.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.ng-untouched.ng-pristine.ng-invalid.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element", "value": {"value": "", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched ng-pristine ng-invalid mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "Email Name Placeholder", "text": "placeholder"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1717422034572, "execute": [{"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1717422034572.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.ng-dirty.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "e2e new DD automation email ", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input ng-dirty ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "Email Name Placeholder", "text": "placeholder"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1717422052341, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "e2e new DD automation email ", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1717422052341.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCSS": "kt-email-create.ng-star-inserted kt-email-create-detail.ng-star-inserted form.ng-dirty.ng-valid.ng-touched.ng-submitted div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Create Automation Email", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Create Automation Email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1717422053497, "execute": [{"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1717422053497.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-editor']", "targetCSS": "kt-email-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted span.mdc-list-item__content span.mat-mdc-list-item-unscoped-content.mdc-list-item__primary-text span.e2e-mark-element", "value": {"value": "DD Editor", "tagName": "span", "attributes": [{"value": "e2e-mark-element", "text": "class"}, {"value": "DD Editor", "text": "innerText"}], "selectOptions": []}, "timestamp": 1717422057306, "execute": [{"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-editor']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1717422057306.jpg"}, {"command": "assert-element", "target": "//mat-dialog-container[@id='mat-mdc-dialog-0']//div//div//kt-template-browser-dialog//kt-modal-base//section//div//kt-tabs//div//div", "targetCSS": "mat-dialog-container#mat-mdc-dialog-0.mat-mdc-dialog-container.mdc-dialog.cdk-dialog-container.mdc-dialog--open div.mdc-dialog__container div.mat-mdc-dialog-surface.mdc-dialog__surface kt-template-browser-dialog.mat-mdc-dialog-component-host.ng-star-inserted kt-modal-base section.modal-base-container div.modal-base-content kt-tabs div.tabs div.tab.noselect.active.ng-star-inserted.e2e-mark-element", "value": {"value": "KlickTipp Templates", "tagName": "div", "attributes": [{"value": "", "text": "_ngcontent-ng-c3780569851"}, {"value": "tab noselect active ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "KlickTipp Templates", "text": "innerText"}], "selectOptions": []}, "timestamp": *************, "execute": [{"command": "assert-attribute:innerText", "xPath": "//mat-dialog-container[@id='mat-mdc-dialog-0']//div//div//kt-template-browser-dialog//kt-modal-base//section//div//kt-tabs//div//div", "attribute": "innerText", "value": "KlickTipp Templates", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/*************.jpg"}], "datasets": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [], "roleNames": ["Enterprise"], "userGroupId": "43", "tierInAccount": "10000"}, "machineName": "test_emails__automationemail__create__dd_automation_email_*************", "pipelineReady": true, "zephyrTest": "T392", "repoReady": true, "repoPath": "emails/automationemail/create", "zephyrTestUrl": "https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-T392"}
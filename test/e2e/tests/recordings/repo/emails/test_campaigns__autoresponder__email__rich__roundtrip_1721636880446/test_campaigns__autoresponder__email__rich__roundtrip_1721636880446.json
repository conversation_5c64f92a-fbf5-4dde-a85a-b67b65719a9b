{"id": *************, "displayName": "Campaigns::Autoresponder::Email::Rich::Roundtrip", "description": "Create AR; Rename; Add content; Save senddate; Test Affix; Delete;", "status": "recorded", "data": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [{"class": "E2eDataReset", "params": {}}, {"class": "E2eDataAccount", "params": {"userGroupId": "43", "roleNames": ["Enterprise"], "tierInAccount": "10000"}}]}, "steps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-autoresponder-me']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='editor-select-btn-rt']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "e2e email AR", "special": ""}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='senderName-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='senderName-input']", "targetCss": "", "value": "e2e Test Recorder new name", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-email']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//input[@data-e2e-id='senderName-input']", "targetCss": "", "value": "e2e Test Recorder new name", "special": "assert-angular-text-field-value"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-editor']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCss": "", "value": "subject", "special": ""}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='email-ckEditor-toggleEditorCb']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//textarea[@data-e2e-id='ckEditorTextModeArea-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//textarea[@data-e2e-id='ckEditorTextModeArea-input']", "targetCss": "", "value": "hello content", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnRichText']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='email_editor_autoresponder_successlink_email_analysis']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='goBackToEditorBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCss": "", "value": "subject", "special": "assert-angular-text-field-value"}, {"command": "assert-element", "target": "//textarea[@data-e2e-id='ckEditorTextModeArea-input']", "targetCss": "", "value": "hello content", "special": "assert-angular-text-field-value"}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnRichText']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='email_editor_autoresponder_successlink_schedule']", "targetCss": "", "value": "", "special": ""}, {"command": "wait", "target": "", "targetCss": "", "value": 3, "special": ""}, {"command": "click", "target": "//mat-select[@data-e2e-id='sendDateSelect-select']", "targetCss": "", "value": "", "special": ""}, {"command": "wait", "target": "", "targetCss": "", "value": 3, "special": ""}, {"command": "click", "target": "//mat-option[@data-e2e-id='sendDateSelect-select-option-0']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='saveSendDateBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='checkRecipientsModal-confirmBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "wait", "target": "", "targetCss": "", "value": 3, "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-autoresponder-create-detail//h1[contains(text(),'Autoresponder Settings')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-email']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-email-settings-wrapper//kt-email-create-detail//h1[contains(text(),'Edit Autoresponder Email')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-editor']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-rich-text-editor-wrapper//kt-rich-text-editor//h1[contains(text(),'RichText Title')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-analysis']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-email-content-analysis-wrapper//kt-email-content-analysis//h1[contains(text(),'Content Analysis')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-schedule']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-autoresponder-senddate//h1[contains(text(),'Edit send data')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-statistics']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-item-detail-overview//h1[contains(text(),'Overview')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-item-detail-overview//div//div//span[contains(text(),'Status: This autoresponder is active.')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='deleteBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='deleteModal-confirmBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-overview//p[contains(text(),'No campaigns within this category yet.')]", "targetCss": "", "value": "", "special": ""}], "recordingSteps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCSS": "html body kt-app kt-menu.kt-menu div.kt-menu-wrapper.fixed div.kt-menu-container div.kt-menu-item.noselect.kt-menu-item-header.e2e-mark-element.is-active", "value": {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "tagName": "div", "attributes": [{"value": "", "text": "_ngcontent-ng-c1926400999"}, {"value": "kt-menu-item noselect kt-menu-item-header e2e-mark-element is-active", "text": "class"}, {"value": "main-3", "text": "data-e2e-id"}, {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636896436, "execute": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636896436.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-autoresponder-me']", "targetCSS": "div.kt-menu-dropdown.min-width.ng-star-inserted div.kt-menu-child.noselect.ng-star-inserted a.ng-star-inserted.e2e-mark-element", "value": {"value": "Follow-Up Campaigns", "tagName": "a", "attributes": [{"value": "", "text": "_ngcontent-ng-c1926400999"}, {"value": "https://app.ktlocal.com/app/campaign/autoresponder/11", "text": "href"}, {"value": "main-3::application-me-campaign-autoresponder-me", "text": "data-e2e-id"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Follow-Up Campaigns", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636898797, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-autoresponder-me']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636898797.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCSS": "kt-autoresponder-overview.ng-star-inserted div.full-width.align-right.ng-star-inserted kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-accent.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Create email autoresponder", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Create email autoresponder", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636900950, "execute": [{"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636900950.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.ng-untouched.ng-pristine.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-invalid.e2e-mark-element", "value": {"value": "", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched ng-pristine mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-invalid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "Name", "text": "placeholder"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636902091, "execute": [{"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636902091.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "e2e email AR", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "Name", "text": "placeholder"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636908961, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "e2e email AR", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636908961.jpg"}, {"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1721636910272, "execute": [{"command": "click", "target": "//html//body//kt-app//div//ng-component//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636910272.jpg"}, {"command": "click", "target": "//mat-radio-button[@data-e2e-id='editorType-option-1']", "targetCSS": "mat-radio-button#mat-radio-6.mat-mdc-radio-button.mat-accent.ng-star-inserted.cdk-focused.cdk-mouse-focused.e2e-mark-element.mat-mdc-radio-checked", "value": {"value": "Rich Text", "tagName": "mat-radio-button", "attributes": [{"value": "mat-mdc-radio-button mat-accent ng-star-inserted cdk-focused cdk-mouse-focused e2e-mark-element mat-mdc-radio-checked", "text": "class"}, {"value": "editorType-option-1", "text": "data-e2e-id"}, {"value": "mat-radio-6", "text": "id"}, {"value": "Rich Text", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636912734, "execute": [{"command": "click", "target": "//mat-radio-button[@data-e2e-id='editorType-option-1']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636912734.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCSS": "kt-item-create.ng-star-inserted kt-autoresponder-create-detail.ng-star-inserted form.ng-dirty.ng-valid.ng-touched.ng-submitted div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mat-mdc-button-touch-target.e2e-mark-element", "value": {"value": "", "tagName": "span", "attributes": [{"value": "mat-mdc-button-touch-target e2e-mark-element", "text": "class"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636914923, "execute": [{"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636914923.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='senderName-input']", "targetCSS": "input#mat-input-13.mat-mdc-input-element.custom-autofill.ng-untouched.ng-valid.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element.ng-dirty", "value": {"value": "e2e email AR", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched ng-valid mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element ng-dirty", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "senderName-input", "text": "data-e2e-id"}, {"value": "First name Last name", "text": "placeholder"}, {"value": "mat-input-13", "text": "id"}, {"value": "senderName-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636922986, "execute": [{"command": "click", "target": "//input[@data-e2e-id='senderName-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636922986.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='senderName-input']", "targetCSS": "input#mat-input-13.mat-mdc-input-element.custom-autofill.ng-valid.mat-mdc-form-field-input-control.mdc-text-field__input.ng-dirty.ng-touched.e2e-mark-element", "value": {"value": "e2e Test Recorder new name", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-valid mat-mdc-form-field-input-control mdc-text-field__input ng-dirty ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "senderName-input", "text": "data-e2e-id"}, {"value": "First name Last name", "text": "placeholder"}, {"value": "mat-input-13", "text": "id"}, {"value": "senderName-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636928820, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='senderName-input']", "targetCss": "", "value": "e2e Test Recorder new name", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636928820.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCSS": "kt-email-settings-wrapper.ng-star-inserted kt-email-create-detail form.ng-valid.ng-dirty.ng-touched.ng-submitted div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.icon-right.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Save and continue", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Save and continue", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636929970, "execute": [{"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636929970.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-email']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "Email", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "Email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636932053, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-email']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636932053.jpg"}, {"command": "assert-element", "target": "//input[@data-e2e-id='senderName-input']", "targetCSS": "input#mat-input-25.mat-mdc-input-element.custom-autofill.ng-untouched.ng-pristine.ng-valid.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element", "value": {"value": "e2e Test Recorder new name", "tagName": "input.text", "attributes": [{"value": "e2e Test Recorder new name", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched ng-pristine ng-valid mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "senderName-input", "text": "data-e2e-id"}, {"value": "First name Last name", "text": "placeholder"}, {"value": "mat-input-25", "text": "id"}, {"value": "senderName-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636938342, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@data-e2e-id='senderName-input']", "attribute": "value", "value": "e2e Test Recorder new name", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636938342.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-editor']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "<PERSON>Rich <PERSON>", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "<PERSON>Rich <PERSON>", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636940559, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-editor']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636940559.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCSS": "input#mat-input-31.mat-mdc-input-element.custom-autofill.ng-untouched.ng-valid.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element.ng-dirty", "value": {"value": "su", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched ng-valid mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element ng-dirty", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "subject-input", "text": "data-e2e-id"}, {"value": "mat-input-31", "text": "id"}, {"value": "subject-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636943243, "execute": [{"command": "click", "target": "//input[@data-e2e-id='subject-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636943243.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='subject-input']", "targetCSS": "input#mat-input-31.mat-mdc-input-element.custom-autofill.ng-valid.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.e2e-mark-element", "value": {"value": "subject", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-valid mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "subject-input", "text": "data-e2e-id"}, {"value": "mat-input-31", "text": "id"}, {"value": "subject-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636947494, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='subject-input']", "targetCss": "", "value": "subject", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636947494.jpg"}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='toggleEditorCb']", "targetCSS": "mat-checkbox#toggleEditorCb.mat-mdc-checkbox.mat-accent.ng-untouched.ng-valid.mat-mdc-checkbox-checked.ng-dirty.e2e-mark-element", "value": {"value": "Deactivate HTML Editor", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-valid mat-mdc-checkbox-checked ng-dirty e2e-mark-element", "text": "class"}, {"value": "toggleEditorCb", "text": "data-e2e-id"}, {"value": "toggleEditorCb", "text": "id"}, {"value": "Deactivate HTML Editor", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636948501, "execute": [{"command": "click", "target": "//mat-checkbox[@data-e2e-id='toggleEditorCb']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636948501.jpg"}, {"command": "click", "target": "//textarea[@data-e2e-id='ckEditorTextModeArea-input']", "targetCSS": "textarea#mat-input-28.mat-mdc-input-element.ng-tns-c3736059725-131.ng-untouched.ng-pristine.ng-valid.mat-mdc-form-field-textarea-control.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element", "value": {"value": "subject", "tagName": "textarea", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element ng-tns-c3736059725-131 ng-untouched ng-pristine ng-valid mat-mdc-form-field-textarea-control mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element", "text": "class"}, {"value": "1", "text": "cols"}, {"value": "10", "text": "rows"}, {"value": "ckEditorTextModeArea-input", "text": "data-e2e-id"}, {"value": "mat-input-28", "text": "id"}, {"value": "ckEditorTextModeArea", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "lt-662802", "text": "data-lt-tmp-id"}, {"value": "false", "text": "spellcheck"}, {"value": "false", "text": "data-gramm"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636950428, "execute": [{"command": "click", "target": "//textarea[@data-e2e-id='ckEditorTextModeArea-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636950428.jpg"}, {"command": "text-input", "target": "//textarea[@data-e2e-id='ckEditorTextModeArea-input']", "targetCSS": "textarea#mat-input-28.mat-mdc-input-element.ng-tns-c3736059725-131.ng-valid.mat-mdc-form-field-textarea-control.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.e2e-mark-element", "value": {"value": "hello content", "tagName": "textarea", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element ng-tns-c3736059725-131 ng-valid mat-mdc-form-field-textarea-control mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched e2e-mark-element", "text": "class"}, {"value": "1", "text": "cols"}, {"value": "10", "text": "rows"}, {"value": "ckEditorTextModeArea-input", "text": "data-e2e-id"}, {"value": "mat-input-28", "text": "id"}, {"value": "ckEditorTextModeArea", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "lt-662802", "text": "data-lt-tmp-id"}, {"value": "false", "text": "spellcheck"}, {"value": "false", "text": "data-gramm"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636956585, "execute": [{"command": "text-input", "target": "//textarea[@data-e2e-id='ckEditorTextModeArea-input']", "targetCss": "", "value": "hello content", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636956585.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnRichText']", "targetCSS": "html.cdk-global-scrollblock body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-rich-text-editor-wrapper.ng-star-inserted kt-rich-text-editor.ng-star-inserted form.ng-valid.ng-dirty.ng-touched.ng-submitted div.mt-15 kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-accent.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label.e2e-mark-element", "value": {"value": "Save", "tagName": "span", "attributes": [{"value": "mdc-button__label e2e-mark-element", "text": "class"}, {"value": "Save", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636957638, "execute": [{"command": "click", "target": "//button[@data-e2e-id='saveBtnRichText']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636957638.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='email_editor_autoresponder_successlink_email_analysis']", "targetCSS": "kt-publish-result-dialog.mat-mdc-dialog-component-host.ng-star-inserted kt-modal-base section.modal-base-container div.modal-base-content section.modal-body.ng-star-inserted kt-validation.modal-section.publish-conditions.publish-success kt-validation-success-links.ng-star-inserted div ul.list.list-actions.ng-star-inserted li a span.e2e-mark-element", "value": {"value": "Email analysis", "tagName": "span", "attributes": [{"value": "", "text": "_ngcontent-ng-c58296112"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Email analysis", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636961730, "execute": [{"command": "click", "target": "//a[@data-e2e-id='email_editor_autoresponder_successlink_email_analysis']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636961730.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='goBackToEditorBtn']", "targetCSS": "div#cdk-accordion-child-9.mat-expansion-panel-content.ng-tns-c857250080-147.ng-trigger.ng-trigger-bodyExpansion div.mat-expansion-panel-body.ng-tns-c857250080-147 div.ng-tns-c857250080-147 kt-button.browser-preview-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-accent.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Go Back to editor", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "", "text": "style"}, {"value": "Go Back to editor", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636965330, "execute": [{"command": "click", "target": "//button[@data-e2e-id='goBackToEditorBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636965330.jpg"}, {"command": "assert-element", "target": "//input[@data-e2e-id='subject-input']", "targetCSS": "input#mat-input-38.mat-mdc-input-element.custom-autofill.ng-untouched.ng-pristine.ng-valid.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element", "value": {"value": "subject", "tagName": "input.text", "attributes": [{"value": "subject", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched ng-pristine ng-valid mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "subject-input", "text": "data-e2e-id"}, {"value": "mat-input-38", "text": "id"}, {"value": "subject-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636969813, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@data-e2e-id='subject-input']", "attribute": "value", "value": "subject", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636969813.jpg"}, {"command": "assert-element", "target": "//textarea[@data-e2e-id='ckEditorTextModeArea-input']", "targetCSS": "textarea#mat-input-35.mat-mdc-input-element.ng-tns-c3736059725-157.ng-untouched.ng-pristine.ng-valid.mat-mdc-form-field-textarea-control.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element", "value": {"value": "hello content", "tagName": "textarea", "attributes": [{"value": "hello content", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element ng-tns-c3736059725-157 ng-untouched ng-pristine ng-valid mat-mdc-form-field-textarea-control mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element", "text": "class"}, {"value": "1", "text": "cols"}, {"value": "10", "text": "rows"}, {"value": "ckEditorTextModeArea-input", "text": "data-e2e-id"}, {"value": "mat-input-35", "text": "id"}, {"value": "ckEditorTextModeArea", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "lt-46634", "text": "data-lt-tmp-id"}, {"value": "false", "text": "spellcheck"}, {"value": "false", "text": "data-gramm"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636978078, "execute": [{"command": "assert-attribute:value", "xPath": "//textarea[@data-e2e-id='ckEditorTextModeArea-input']", "attribute": "value", "value": "hello content", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636978078.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnRichText']", "targetCSS": "html.cdk-global-scrollblock body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-rich-text-editor-wrapper.ng-star-inserted kt-rich-text-editor.ng-star-inserted form.ng-pristine.ng-valid.ng-touched.ng-submitted div.mt-15 kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-accent.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Save", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Save", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636981008, "execute": [{"command": "click", "target": "//button[@data-e2e-id='saveBtnRichText']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636981008.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='email_editor_autoresponder_successlink_schedule']", "targetCSS": "kt-publish-result-dialog.mat-mdc-dialog-component-host.ng-star-inserted kt-modal-base section.modal-base-container div.modal-base-content section.modal-body.ng-star-inserted kt-validation.modal-section.publish-conditions.publish-success kt-validation-success-links.ng-star-inserted div ul.list.list-actions.ng-star-inserted li a.e2e-mark-element", "value": {"value": "Schedule", "tagName": "a", "attributes": [{"value": "", "text": "_ngcontent-ng-c58296112"}, {"value": "https://app.ktlocal.com/app/campaign/autoresponder-email/11/111/schedule", "text": "href"}, {"value": "email_editor_autoresponder_successlink_schedule", "text": "data-e2e-id"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Schedule", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636985604, "execute": [{"command": "click", "target": "//a[@data-e2e-id='email_editor_autoresponder_successlink_schedule']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636985604.jpg"}, {"command": "dropdown-angular-input", "target": "//div[@id='mat-select-value-93']", "targetCSS": "mat-option#mat-option-302.mat-mdc-option.mdc-list-item.ng-tns-c1771602899-172.ng-star-inserted.mdc-list-item--selected.mat-mdc-option-active span.mdc-list-item__primary-text.e2e-mark-element", "value": {"value": "//mat-option[@data-e2e-id='sendDateSelect-select-option-1']", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1721636991513, "execute": [{"command": "dropdown-angular-input", "target": "//div[@id='mat-select-value-93']", "targetCss": "", "value": "//mat-option[@data-e2e-id='sendDateSelect-select-option-1']", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636991513.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='saveSendDateBtn']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-autoresponder-senddate.ng-star-inserted kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Save send date", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Save send date", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636992878, "execute": [{"command": "click", "target": "//button[@data-e2e-id='saveSendDateBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636992878.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='checkRecipientsModal-confirmBtn']", "targetCSS": "kt-modal.mat-mdc-dialog-component-host.ng-star-inserted div.mat-typography.kt-mat-library div.mat-typography.kt-drupal-theme mat-dialog-actions.mat-mdc-dialog-actions.mdc-dialog__actions kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Save send date", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Save send date", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636994574, "execute": [{"command": "click", "target": "//button[@data-e2e-id='checkRecipientsModal-confirmBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636994574.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.first-item.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "Edit", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "Edit", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721636999276, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721636999276.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-autoresponder-create-detail//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-autoresponder-create-detail.ng-star-inserted h1.ng-star-inserted.e2e-mark-element", "value": {"value": "Autoresponder Settings", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-ng-c1928168165"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Autoresponder Settings", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637002663, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-autoresponder-create-detail//h1", "attribute": "innerText", "value": "Autoresponder Settings", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637002663.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-email']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "Email", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "Email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637005262, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-email']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637005262.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-email-settings-wrapper//kt-email-create-detail//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-settings-wrapper.ng-star-inserted kt-email-create-detail h1.ng-star-inserted.e2e-mark-element", "value": {"value": "Edit Autoresponder Email", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-ng-c3415663012"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Edit Autoresponder Email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637008905, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-email-settings-wrapper//kt-email-create-detail//h1", "attribute": "innerText", "value": "Edit Autoresponder Email", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637008905.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-editor']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "<PERSON>Rich <PERSON>", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "<PERSON>Rich <PERSON>", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637011420, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-editor']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637011420.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-rich-text-editor-wrapper//kt-rich-text-editor//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-rich-text-editor-wrapper.ng-star-inserted kt-rich-text-editor.ng-star-inserted h1.e2e-mark-element", "value": {"value": "RichText Title", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-ng-c1743139663"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "RichText Title", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637015077, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-rich-text-editor-wrapper//kt-rich-text-editor//h1", "attribute": "innerText", "value": "RichText Title", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637015077.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-analysis']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "—Analysis", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "—Analysis", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637017385, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-analysis']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637017385.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-email-content-analysis-wrapper//kt-email-content-analysis//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-content-analysis-wrapper.ng-star-inserted kt-email-content-analysis.ng-star-inserted h1.e2e-mark-element", "value": {"value": "Content Analysis", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-ng-c457979260"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Content Analysis", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637020910, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-email-content-analysis-wrapper//kt-email-content-analysis//h1", "attribute": "innerText", "value": "Content Analysis", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637020910.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-schedule']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "Schedule", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "Schedule", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637022873, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-schedule']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637022873.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-autoresponder-senddate//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-autoresponder-senddate.ng-star-inserted h1.e2e-mark-element", "value": {"value": "Edit send data", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-ng-c1067864282"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Edit send data", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637025983, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-autoresponder-senddate//h1", "attribute": "innerText", "value": "Edit send data", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637025983.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-statistics']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.last-item.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "Statistics", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "Statistics", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637027838, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-statistics']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637027838.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-item-detail-overview//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted h1.e2e-mark-element", "value": {"value": "Overview", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-ng-c1470239724"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Overview", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637040012, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-item-detail-overview//h1", "attribute": "innerText", "value": "Overview", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637040012.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-item-detail-overview//div//div//span", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted div.status-bar.ng-star-inserted div.status-container span.e2e-mark-element", "value": {"value": "Status: This autoresponder is active.", "tagName": "span", "attributes": [{"value": "", "text": "_ngcontent-ng-c1470239724"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Status: This autoresponder is active.", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637045211, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-detail//div//div//kt-item-detail-overview//div//div//span", "attribute": "innerText", "value": "Status: This autoresponder is active.", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637045211.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list.ng-star-inserted a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.first-item.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "Edit", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "Edit", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637047825, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637047825.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='deleteBtn']", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-autoresponder-create-detail.ng-star-inserted form.ng-untouched.ng-pristine.ng-valid.ng-submitted div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-warn.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Delete Autoresponder", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Delete Autoresponder", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637050287, "execute": [{"command": "click", "target": "//button[@data-e2e-id='deleteBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637050287.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='deleteModal-confirmBtn']", "targetCSS": "kt-modal.mat-mdc-dialog-component-host.ng-star-inserted div.mat-typography.kt-mat-library div.mat-typography.kt-drupal-theme mat-dialog-actions.mat-mdc-dialog-actions.mdc-dialog__actions kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-warn.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Delete", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Delete", "text": "innerText"}], "selectOptions": []}, "timestamp": 1721637053639, "execute": [{"command": "click", "target": "//button[@data-e2e-id='deleteModal-confirmBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1721637053639.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-overview//p", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-autoresponder-overview.ng-star-inserted p.ng-star-inserted.e2e-mark-element", "value": {"value": "No campaigns within this category yet.", "tagName": "p", "attributes": [{"value": "", "text": "_ngcontent-ng-c2430455433"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "No campaigns within this category yet.", "text": "innerText"}], "selectOptions": []}, "timestamp": *************, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-autoresponder-overview//p", "attribute": "innerText", "value": "No campaigns within this category yet.", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/*************.jpg"}], "datasets": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [], "roleNames": ["Enterprise"], "userGroupId": "43", "tierInAccount": "10000"}, "machineName": "test_campaigns__autoresponder__email__rich__roundtrip_*************", "pipelineReady": true, "zephyrTest": "T398", "repoReady": true, "repoPath": "emails", "zephyrTestUrl": "https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-T398"}
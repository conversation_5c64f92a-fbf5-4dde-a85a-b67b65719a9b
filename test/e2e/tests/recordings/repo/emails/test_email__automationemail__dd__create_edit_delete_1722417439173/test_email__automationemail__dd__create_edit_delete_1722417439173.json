{"id": *************, "displayName": "Email::AutomationEmail::DD::Create_Edit_Delete", "description": "Create an automation email, apply a template, publish, edit and delete", "status": "recorded", "data": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [{"class": "E2eDataReset", "params": {}}, {"class": "E2eDataAccount", "params": {"userGroupId": "43", "roleNames": ["Enterprise"], "tierInAccount": "10000"}}]}, "steps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-email-overview-me']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='editor-select-btn-dnd']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "DD Automation Email", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCss": "", "value": "<PERSON><PERSON><PERSON><PERSON>", "special": ""}, {"command": "click", "target": "//html//body//kt-app//div//kt-email-settings-base//div", "targetCss": "", "value": "", "special": "somewhere"}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='entityNotesEnabled']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCss": "", "value": "mynotes", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-select[@data-e2e-id='senderEmailNamesSelect-select']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-option[@data-e2e-id='senderEmailNamesSelect-select-option-1']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-select[@data-e2e-id='replyEmailAddressesSelect-select']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-option[@data-e2e-id='replyEmailAddressesSelect-select-option-1']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-select[@data-e2e-id='signatureFormatSelect-select']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-option//span[contains(text(),'Default Signature')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-accordion[@data-e2e-id='extra-settings-panel']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='cbLinkTracking']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='cbBillDraft']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='cbOptimizeSoi']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='confirmTransactionalEmail-confirmBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//div[@data-e2e-id='template-overview-tile-0']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//div[@data-e2e-id='template-overview-tile-0']//button[contains(@class, 'kt-btn-apply')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='ee-toolbar-button-publish']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//kt-publish-result-dialog//kt-modal-base//section//div//section//kt-validation//ul//li//ul//li//span", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCss": "", "value": "mysubject", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='apply-subject-button']", "targetCss": "", "value": "", "special": ""}, {"command": "wait", "target": "", "targetCss": "", "value": 3, "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='ee-toolbar-button-publish']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='email_editor_automation_successlink_email_analysis']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//kt-email-settings-base//div//section//kt-email-detail//div//div//kt-email-content-analysis//h1[contains(text(),'Content Analysis')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='campaignOverviewBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-automation-overview//h1[contains(text(),'Campaigns')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-email-overview-me']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//a[@data-e2e-id='table-text-r-0-c-1'][contains(text(),'DD Automation Email')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='table-text-r-0-c-1']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//mat-select[@data-e2e-id='senderEmailNamesSelect-select']//span//span[contains(text(),'From dispatch profile')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//mat-checkbox[@data-e2e-id='cbLinkTracking']", "targetCss": "", "value": "", "special": "angular-checkbox-checked"}, {"command": "assert-element", "target": "//mat-checkbox[@data-e2e-id='cbBillDraft']", "targetCss": "", "value": "", "special": "angular-checkbox-checked"}, {"command": "assert-element", "target": "//mat-checkbox[@data-e2e-id='cbOptimizeSoi']", "targetCss": "", "value": "", "special": "angular-checkbox-checked"}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='confirmTransactionalEmail-confirmBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//kt-email-editor//kt-email-editor-wrapper//div//kt-toolbar//div//div//kt-email-settings-svg", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-details']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='deleteBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='deleteModal-secondaryBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='entityLabels::close::mylabel']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//div", "targetCss": "", "value": "", "special": "somewhere"}, {"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-details']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='entityNotesEnabled']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-details']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='deleteBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='deleteModal-confirmBtn']", "targetCss": "", "value": "", "special": ""}], "recordingSteps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCSS": "html body kt-app kt-menu.kt-menu div.kt-menu-wrapper.fixed div.kt-menu-container div.kt-menu-item.noselect.kt-menu-item-header.e2e-mark-element.is-active", "value": {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "tagName": "div", "attributes": [{"value": "", "text": "_ngcontent-ng-c1926400999"}, {"value": "kt-menu-item noselect kt-menu-item-header e2e-mark-element is-active", "text": "class"}, {"value": "main-3", "text": "data-e2e-id"}, {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417460433, "execute": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417460433.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-email-overview-me']", "targetCSS": "div.kt-menu-dropdown.min-width.ng-star-inserted div.kt-menu-child.noselect.ng-star-inserted a.ng-star-inserted.e2e-mark-element", "value": {"value": "E-Mails / SMS", "tagName": "a", "attributes": [{"value": "", "text": "_ngcontent-ng-c1926400999"}, {"value": "https://app.ktlocal.com/app/email/overview/11", "text": "href"}, {"value": "main-3::application-me-email-overview-me", "text": "data-e2e-id"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "E-Mails / SMS", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417463800, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-email-overview-me']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417463800.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCSS": "kt-email-overview.ng-star-inserted div.full-width.align-right.ng-star-inserted kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-accent.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mat-mdc-button-touch-target.e2e-mark-element", "value": {"value": "", "tagName": "span", "attributes": [{"value": "mat-mdc-button-touch-target e2e-mark-element", "text": "class"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417467415, "execute": [{"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417467415.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.ng-untouched.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element.ng-dirty.ng-valid", "value": {"value": "DD", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element ng-dirty ng-valid", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "Email Name Placeholder", "text": "placeholder"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417468797, "execute": [{"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417468797.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "DD Automation Email", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "Email Name Placeholder", "text": "placeholder"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417478397, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "DD Automation Email", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417478397.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCSS": "input#mat-input-3.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-autocomplete-trigger.mat-mdc-form-field-input-control.ng-valid.cdk-text-field-autofill-monitored.ng-touched.e2e-mark-element.ng-dirty", "value": {"value": "my", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-autocomplete-trigger mat-mdc-form-field-input-control ng-valid cdk-text-field-autofill-monitored ng-touched e2e-mark-element ng-dirty", "text": "class"}, {"value": "entityLabels-chip-list-input", "text": "data-e2e-id"}, {"value": "mat-input-3", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417479759, "execute": [{"command": "click", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417479759.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCSS": "input#mat-input-3.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-autocomplete-trigger.mat-mdc-form-field-input-control.ng-valid.cdk-text-field-autofill-monitored.ng-touched.ng-dirty.e2e-mark-element", "value": {"value": "<PERSON><PERSON><PERSON><PERSON>", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-autocomplete-trigger mat-mdc-form-field-input-control ng-valid cdk-text-field-autofill-monitored ng-touched ng-dirty e2e-mark-element", "text": "class"}, {"value": "entityLabels-chip-list-input", "text": "data-e2e-id"}, {"value": "mat-input-3", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417486788, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCss": "", "value": "<PERSON><PERSON><PERSON><PERSON>", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417486788.jpg"}, {"command": "click", "target": "//html//body//kt-app//div//kt-email-settings-base//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1722417488453, "execute": [{"command": "click", "target": "//html//body//kt-app//div//kt-email-settings-base//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417488453.jpg"}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='entityNotesEnabled']", "targetCSS": "mat-checkbox#entityNotesEnabled.mat-mdc-checkbox.mat-accent.ng-untouched.ng-valid.e2e-mark-element.mat-mdc-checkbox-checked.ng-dirty", "value": {"value": "Make notes", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-valid e2e-mark-element mat-mdc-checkbox-checked ng-dirty", "text": "class"}, {"value": "entityNotesEnabled", "text": "data-e2e-id"}, {"value": "entityNotesEnabled", "text": "id"}, {"value": "Make notes", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417490554, "execute": [{"command": "click", "target": "//mat-checkbox[@data-e2e-id='entityNotesEnabled']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417490554.jpg"}, {"command": "click", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCSS": "textarea#mat-input-0.mat-mdc-input-element.ng-tns-c3736059725-10.ng-untouched.ng-valid.mat-mdc-form-field-textarea-control.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element.ng-dirty", "value": {"value": "mynotes", "tagName": "textarea", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element ng-tns-c3736059725-10 ng-untouched ng-valid mat-mdc-form-field-textarea-control mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element ng-dirty", "text": "class"}, {"value": "100", "text": "cols"}, {"value": "8", "text": "rows"}, {"value": "entityNotes-input", "text": "data-e2e-id"}, {"value": "mat-input-0", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417493306, "execute": [{"command": "click", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417493306.jpg"}, {"command": "text-input", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCSS": "textarea#mat-input-0.mat-mdc-input-element.ng-tns-c3736059725-10.ng-valid.mat-mdc-form-field-textarea-control.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.e2e-mark-element", "value": {"value": "mynotes", "tagName": "textarea", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element ng-tns-c3736059725-10 ng-valid mat-mdc-form-field-textarea-control mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched e2e-mark-element", "text": "class"}, {"value": "100", "text": "cols"}, {"value": "8", "text": "rows"}, {"value": "entityNotes-input", "text": "data-e2e-id"}, {"value": "mat-input-0", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417497266, "execute": [{"command": "text-input", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCss": "", "value": "mynotes", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417497266.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCSS": "html body kt-app div.kt-outer-container kt-email-settings-base.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-email-create.ng-star-inserted kt-email-create-detail.ng-star-inserted form.ng-dirty.ng-valid.ng-touched div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Create Automation Email", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Create Automation Email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417498670, "execute": [{"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417498670.jpg"}, {"command": "dropdown-angular-input", "target": "//div[@id='mat-select-value-23']", "targetCSS": "mat-option#mat-option-25.mat-mdc-option.mdc-list-item.ng-tns-c1771602899-48.ng-star-inserted.mdc-list-item--selected.mat-mdc-option-active span.mdc-list-item__primary-text.e2e-mark-element", "value": {"value": "//mat-option[@data-e2e-id='senderEmailNamesSelect-select-option-0']", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1722417510207, "execute": [{"command": "dropdown-angular-input", "target": "//div[@id='mat-select-value-23']", "targetCss": "", "value": "//mat-option[@data-e2e-id='senderEmailNamesSelect-select-option-0']", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417510207.jpg"}, {"command": "click", "target": "//mat-select[@data-e2e-id='replyEmailAddressesSelect-select']", "targetCSS": "mat-select#mat-select-26.mat-mdc-select.ng-tns-c1771602899-53.ng-untouched.ng-pristine.ng-valid.ng-star-inserted.e2e-mark-element", "value": {"value": "<EMAIL>", "tagName": "mat-select", "attributes": [{"value": "combobox", "text": "role"}, {"value": "none", "text": "aria-autocomplete"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "true", "text": "hidesingleselectionindicator"}, {"value": "mat-mdc-select ng-tns-c1771602899-53 ng-untouched ng-pristine ng-valid ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "mat-mdc-form-field-label-50 mat-select-value-27", "text": "aria-<PERSON>by"}, {"value": "replyEmailAddressesSelect-select", "text": "data-e2e-id"}, {"value": "mat-select-26", "text": "id"}, {"value": "0", "text": "tabindex"}, {"value": "true", "text": "aria-expanded"}, {"value": "false", "text": "aria-required"}, {"value": "false", "text": "aria-disabled"}, {"value": "false", "text": "aria-invalid"}, {"value": "mat-select-26-panel", "text": "aria-controls"}, {"value": "mat-option-28", "text": "aria-activedescendant"}, {"value": "<EMAIL>", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417515364, "execute": [{"command": "click", "target": "//mat-select[@data-e2e-id='replyEmailAddressesSelect-select']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417515364.jpg"}, {"command": "click", "target": "//mat-option[@data-e2e-id='replyEmailAddressesSelect-select-option-dispatchProfile']", "targetCSS": "mat-option#mat-option-29.mat-mdc-option.mdc-list-item.ng-tns-c1771602899-53.ng-star-inserted.mdc-list-item--selected.mat-mdc-option-active span.mdc-list-item__primary-text.e2e-mark-element", "value": {"value": " From dispatch profile ", "tagName": "span", "attributes": [{"value": "mdc-list-item__primary-text e2e-mark-element", "text": "class"}, {"value": " From dispatch profile ", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417517494, "execute": [{"command": "click", "target": "//mat-option[@data-e2e-id='replyEmailAddressesSelect-select-option-dispatchProfile']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417517494.jpg"}, {"command": "dropdown-angular-input", "target": "//div[@id='mat-select-value-31']", "targetCSS": "mat-option#mat-option-31.mat-mdc-option.mdc-list-item.ng-tns-c1771602899-57.ng-star-inserted.mdc-list-item--selected.mat-mdc-option-active span.mdc-list-item__primary-text.e2e-mark-element", "value": {"value": "//mat-option[@data-e2e-id='signatureFormatSelect-select-option-99']", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1722417521079, "execute": [{"command": "dropdown-angular-input", "target": "//div[@id='mat-select-value-31']", "targetCss": "", "value": "//mat-option[@data-e2e-id='signatureFormatSelect-select-option-99']", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417521079.jpg"}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='cbLinkTracking']", "targetCSS": "mat-checkbox#cbLinkTracking.mat-mdc-checkbox.mat-accent.ng-untouched.ng-valid.e2e-mark-element.mat-mdc-checkbox-checked.ng-dirty", "value": {"value": "Turn off link tracking", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-valid e2e-mark-element mat-mdc-checkbox-checked ng-dirty", "text": "class"}, {"value": "cbLinkTracking", "text": "data-e2e-id"}, {"value": "cbLinkTracking", "text": "id"}, {"value": "Turn off link tracking", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417523937, "execute": [{"command": "click", "target": "//mat-checkbox[@data-e2e-id='cbLinkTracking']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417523937.jpg"}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='cbBillDraft']", "targetCSS": "mat-checkbox#cbBillDraft.mat-mdc-checkbox.mat-accent.ng-untouched.ng-valid.e2e-mark-element.mat-mdc-checkbox-checked.ng-dirty", "value": {"value": "Use for bill template", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-valid e2e-mark-element mat-mdc-checkbox-checked ng-dirty", "text": "class"}, {"value": "cbBillDraft", "text": "data-e2e-id"}, {"value": "cbBillDraft", "text": "id"}, {"value": "Use for bill template", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417526952, "execute": [{"command": "click", "target": "//mat-checkbox[@data-e2e-id='cbBillDraft']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417526952.jpg"}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='cbOptimizeSoi']", "targetCSS": "mat-checkbox#cbOptimizeSoi.mat-mdc-checkbox.mat-accent.ng-untouched.ng-valid.e2e-mark-element.mat-mdc-checkbox-checked.ng-dirty", "value": {"value": "Optimize SOI first sent", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-valid e2e-mark-element mat-mdc-checkbox-checked ng-dirty", "text": "class"}, {"value": "cbOptimizeSoi", "text": "data-e2e-id"}, {"value": "cbOptimizeSoi", "text": "id"}, {"value": "Optimize SOI first sent", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417529405, "execute": [{"command": "click", "target": "//mat-checkbox[@data-e2e-id='cbOptimizeSoi']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417529405.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCSS": "html body kt-app div.kt-outer-container kt-email-settings-base.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-email-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-create-detail.ng-star-inserted form.ng-valid.ng-touched.ng-dirty div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.icon-right.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Save and continue", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Save and continue", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417577955, "execute": [{"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417577955.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='confirmTransactionalEmail-confirmBtn']", "targetCSS": "kt-modal.mat-mdc-dialog-component-host.ng-star-inserted div.mat-typography.kt-mat-library div.mat-typography.kt-drupal-theme mat-dialog-actions.mat-mdc-dialog-actions.mdc-dialog__actions kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mat-mdc-button-touch-target.e2e-mark-element", "value": {"value": "", "tagName": "span", "attributes": [{"value": "mat-mdc-button-touch-target e2e-mark-element", "text": "class"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417584840, "execute": [{"command": "click", "target": "//button[@data-e2e-id='confirmTransactionalEmail-confirmBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417584840.jpg"}, {"command": "click", "target": "//mat-dialog-container[@id='mat-mdc-dialog-1']//div//div//kt-template-browser-dialog//kt-modal-base//section//div//kt-tabs//div//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1722417684887, "execute": [{"command": "click", "target": "//mat-dialog-container[@id='mat-mdc-dialog-1']//div//div//kt-template-browser-dialog//kt-modal-base//section//div//kt-tabs//div//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417684887.jpg"}, {"command": "click", "target": "//mat-dialog-container[@id='mat-mdc-dialog-1']//div//div//kt-template-browser-dialog//kt-modal-base//section//div//kt-tabs//div//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1722417688769, "execute": [{"command": "click", "target": "//mat-dialog-container[@id='mat-mdc-dialog-1']//div//div//kt-template-browser-dialog//kt-modal-base//section//div//kt-tabs//div//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417688769.jpg"}, {"command": "click", "target": "//mat-dialog-container[@id='mat-mdc-dialog-1']//div//div//kt-template-browser-dialog//kt-modal-base//section//div//kt-tabs//div//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1722417690717, "execute": [{"command": "click", "target": "//mat-dialog-container[@id='mat-mdc-dialog-1']//div//div//kt-template-browser-dialog//kt-modal-base//section//div//kt-tabs//div//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417690717.jpg"}, {"command": "click", "target": "//div[@data-e2e-id='template-overview-tile-0']//div//button", "targetCSS": "kt-template-browser-dialog.mat-mdc-dialog-component-host.ng-star-inserted kt-modal-base section.modal-base-container div.modal-base-content section.kt-modal-body kt-shared-template-browser-overview.ng-star-inserted div.email-templates-filter div.kt-mat-library.mat-typography div section.kt-drupal-theme div.kt-box kt-landing-page-template.kt-template.ng-star-inserted div.kt-tile-border div.kt-cover button.kt-btn-apply.button-primary.button-primary--inverted.e2e-mark-element", "value": {"value": " Apply ", "tagName": "button", "attributes": [{"value": "", "text": "_ngcontent-ng-c1761088129"}, {"value": "kt-btn-apply button-primary button-primary--inverted e2e-mark-element", "text": "class"}, {"value": " Apply ", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417696222, "execute": [{"command": "click", "target": "//div[@data-e2e-id='template-overview-tile-0']//div//button", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417696222.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='ee-toolbar-button-publish']", "targetCSS": "html body kt-app div.kt-outer-container kt-email-editor.ng-star-inserted kt-email-editor-wrapper div.email-editor.ng-star-inserted kt-toolbar.ng-tns-c126875561-68.ng-star-inserted div.ee-topbar.ng-tns-c126875561-68.ng-star-inserted div.ee-tools.ng-tns-c126875561-68 div.ee-toolgroup.ng-tns-c126875561-68 ul.ng-tns-c126875561-68 li.ng-tns-c126875561-68 button.button-primary.ng-tns-c126875561-68.ng-star-inserted.e2e-mark-element", "value": {"value": "Release Email", "tagName": "button", "attributes": [{"value": "", "text": "_ngcontent-ng-c126875561"}, {"value": "ee-toolbar-button-publish", "text": "kte2eidattribute"}, {"value": "button-primary ng-tns-c126875561-68 ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "ee-toolbar-button-publish", "text": "data-e2e-id"}, {"value": "Release Email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417706595, "execute": [{"command": "click", "target": "//button[@data-e2e-id='ee-toolbar-button-publish']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417706595.jpg"}, {"command": "click", "target": "//kt-publish-result-dialog//kt-modal-base//section//div//section//kt-validation//ul//li//ul//li", "targetCSS": "kt-publish-result-dialog.mat-mdc-dialog-component-host.ng-star-inserted kt-modal-base section.modal-base-container div.modal-base-content section.modal-body.ng-star-inserted kt-validation.modal-section.publish-conditions.publish-success ul.list.list-markers li.ng-star-inserted ul.list.list-bullets.ng-star-inserted li span.color-medium.isLink.e2e-mark-element", "value": {"value": "Edit subject.", "tagName": "span", "attributes": [{"value": "", "text": "_ngcontent-ng-c4165968795"}, {"value": "color-medium isLink e2e-mark-element", "text": "class"}, {"value": "Edit subject.", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417716076, "execute": [{"command": "click", "target": "//kt-publish-result-dialog//kt-modal-base//section//div//section//kt-validation//ul//li//ul//li", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417716076.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCSS": "input#mat-input-21.mat-mdc-input-element.custom-autofill.ng-untouched.ng-pristine.ng-valid.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element", "value": {"value": "mynotes", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-untouched ng-pristine ng-valid mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "smart-subject-input", "text": "data-e2e-id"}, {"value": "mat-input-21", "text": "id"}, {"value": "smart-subject-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417717636, "execute": [{"command": "click", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417717636.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCSS": "input#mat-input-21.mat-mdc-input-element.custom-autofill.ng-valid.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-touched.e2e-mark-element", "value": {"value": "mysubject", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-valid mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "smart-subject-input", "text": "data-e2e-id"}, {"value": "mat-input-21", "text": "id"}, {"value": "smart-subject-input", "text": "name"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417722656, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='smart-subject-input']", "targetCss": "", "value": "mysubject", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417722656.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='apply-subject-button']", "targetCSS": "kt-smart-subject-dialog.mat-mdc-dialog-component-host.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme div.footer-row kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "save", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "save", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417723876, "execute": [{"command": "click", "target": "//button[@data-e2e-id='apply-subject-button']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417723876.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='ee-toolbar-button-publish']", "targetCSS": "html body kt-app div.kt-outer-container kt-email-editor.ng-star-inserted kt-email-editor-wrapper div.email-editor.ng-star-inserted kt-toolbar.ng-tns-c126875561-68.ng-star-inserted div.ee-topbar.ng-tns-c126875561-68.ng-star-inserted div.ee-tools.ng-tns-c126875561-68 div.ee-toolgroup.ng-tns-c126875561-68 ul.ng-tns-c126875561-68 li.ng-tns-c126875561-68 button.button-primary.ng-tns-c126875561-68.ng-star-inserted.e2e-mark-element", "value": {"value": "Release Email", "tagName": "button", "attributes": [{"value": "", "text": "_ngcontent-ng-c126875561"}, {"value": "ee-toolbar-button-publish", "text": "kte2eidattribute"}, {"value": "button-primary ng-tns-c126875561-68 ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "ee-toolbar-button-publish", "text": "data-e2e-id"}, {"value": "Release Email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417726778, "execute": [{"command": "click", "target": "//button[@data-e2e-id='ee-toolbar-button-publish']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417726778.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='email_editor_automation_successlink_email_analysis']", "targetCSS": "kt-publish-result-dialog.mat-mdc-dialog-component-host.ng-star-inserted kt-modal-base section.modal-base-container div.modal-base-content section.modal-body.ng-star-inserted kt-validation.modal-section.publish-conditions.publish-success kt-validation-success-links.ng-star-inserted div ul.list.list-actions.ng-star-inserted li a span.e2e-mark-element", "value": {"value": "Email analysis", "tagName": "span", "attributes": [{"value": "", "text": "_ngcontent-ng-c58296112"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Email analysis", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417738375, "execute": [{"command": "click", "target": "//a[@data-e2e-id='email_editor_automation_successlink_email_analysis']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417738375.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//kt-email-settings-base//div//section//kt-email-detail//div//div//kt-email-content-analysis//h1", "targetCSS": "html body kt-app div.kt-outer-container kt-email-settings-base.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-email-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-content-analysis.ng-star-inserted h1.e2e-mark-element", "value": {"value": "Content Analysis", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-ng-c457979260"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Content Analysis", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417748766, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//kt-email-settings-base//div//section//kt-email-detail//div//div//kt-email-content-analysis//h1", "attribute": "innerText", "value": "Content Analysis", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417748766.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='campaignOverviewBtn']", "targetCSS": "html body kt-app div.kt-outer-container kt-email-settings-base.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-email-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-content-analysis.ng-star-inserted kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.icon-right.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Go to Campaign Overview", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Go to Campaign Overview", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417761130, "execute": [{"command": "click", "target": "//button[@data-e2e-id='campaignOverviewBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417761130.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-automation-overview//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-automation-overview.ng-star-inserted h1.e2e-mark-element", "value": {"value": "Campaigns", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-ng-c1408520530"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Campaigns", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417782648, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-automation-overview//h1", "attribute": "innerText", "value": "Campaigns", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417782648.jpg"}, {"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCSS": "html body kt-app kt-menu.kt-menu div.kt-menu-wrapper.fixed div.kt-menu-container div.kt-menu-item.noselect.kt-menu-item-header.ng-star-inserted.e2e-mark-element.is-active", "value": {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "tagName": "div", "attributes": [{"value": "", "text": "_ngcontent-ng-c1926400999"}, {"value": "kt-menu-item noselect kt-menu-item-header ng-star-inserted e2e-mark-element is-active", "text": "class"}, {"value": "main-3", "text": "data-e2e-id"}, {"value": "Campaigns \nCampaigns\nCampaign Templates\nOverview BAM Automation Templates\nFollow-Up Campaigns\nNewsletter\nE-Mails / SMS\nE-Mail Templates", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417785282, "execute": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417785282.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-email-overview-me']", "targetCSS": "div.kt-menu-dropdown.min-width.ng-star-inserted div.kt-menu-child.noselect.ng-star-inserted a.ng-star-inserted.e2e-mark-element", "value": {"value": "E-Mails / SMS", "tagName": "a", "attributes": [{"value": "", "text": "_ngcontent-ng-c1926400999"}, {"value": "https://app.ktlocal.com/app/email/overview/11", "text": "href"}, {"value": "main-3::application-me-email-overview-me", "text": "data-e2e-id"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "E-Mails / SMS", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417786443, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-email-overview-me']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417786443.jpg"}, {"command": "assert-element", "target": "//a[@data-e2e-id='table-text-r-0-c-1']", "targetCSS": "table#cdk-drop-list-4.mat-mdc-table.mdc-data-table__table.cdk-table.mat-sort.cdk-drop-list.ng-tns-c3758795375-96.cdk-drop-list-disabled tbody.mdc-data-table__content.ng-star-inserted tr.mat-mdc-row.mdc-data-table__row.cdk-row.cdk-drag.ng-tns-c3758795375-96.cdk-drag-disabled.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-name.mat-column-name.ng-tns-c3758795375-96.ng-star-inserted div.cell-content.ng-tns-c3758795375-96 div.ng-tns-c3758795375-96.no-chip.ng-star-inserted kt-table-cell.ng-tns-c3758795375-96.ng-star-inserted kt-table-text.form-flex.ng-star-inserted a.kt-two-line-ellipsis.ng-star-inserted.e2e-mark-element", "value": {"value": "DD Automation Email", "tagName": "a", "attributes": [{"value": "", "text": "_ngcontent-ng-c434396179"}, {"value": "", "text": "ktlineclamp"}, {"value": "kt-two-line-ellipsis ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "https://app.ktlocal.com/app/email/automation-email/11/2794/edit", "text": "href"}, {"value": "DD Automation Email", "text": "title"}, {"value": "table-text-r-0-c-1", "text": "data-e2e-id"}, {"value": "DD Automation Email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417792664, "execute": [{"command": "assert-attribute:innerText", "xPath": "//a[@data-e2e-id='table-text-r-0-c-1']", "attribute": "innerText", "value": "DD Automation Email", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417792664.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='table-text-r-0-c-1']", "targetCSS": "tr.mat-mdc-row.mdc-data-table__row.cdk-row.cdk-drag.ng-tns-c3758795375-96.cdk-drag-disabled.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-name.mat-column-name.ng-tns-c3758795375-96.ng-star-inserted div.cell-content.ng-tns-c3758795375-96 div.ng-tns-c3758795375-96.no-chip.ng-star-inserted kt-table-cell.ng-tns-c3758795375-96.ng-star-inserted kt-table-text.form-flex.ng-star-inserted a.kt-two-line-ellipsis.ng-star-inserted.e2e-mark-element", "value": {"value": "DD Automation Email", "tagName": "a", "attributes": [{"value": "", "text": "_ngcontent-ng-c434396179"}, {"value": "", "text": "ktlineclamp"}, {"value": "kt-two-line-ellipsis ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "https://app.ktlocal.com/app/email/automation-email/11/2794/edit", "text": "href"}, {"value": "DD Automation Email", "text": "title"}, {"value": "table-text-r-0-c-1", "text": "data-e2e-id"}, {"value": "DD Automation Email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417795871, "execute": [{"command": "click", "target": "//a[@data-e2e-id='table-text-r-0-c-1']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417795871.jpg"}, {"command": "assert-element", "target": "//div[@id='mat-select-value-49']//span//span", "targetCSS": "div#mat-select-value-49.mat-mdc-select-value.ng-tns-c1771602899-121 span.mat-mdc-select-value-text.ng-tns-c1771602899-121.ng-star-inserted span.mat-mdc-select-min-line.ng-tns-c1771602899-121.ng-star-inserted.e2e-mark-element", "value": {"value": "From dispatch profile", "tagName": "span", "attributes": [{"value": "mat-mdc-select-min-line ng-tns-c1771602899-121 ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "From dispatch profile", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417802695, "execute": [{"command": "assert-attribute:innerText", "xPath": "//div[@id='mat-select-value-49']//span//span", "attribute": "innerText", "value": "From dispatch profile", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417802695.jpg"}, {"command": "assert-element", "target": "//mat-checkbox[@data-e2e-id='cbLinkTracking']", "targetCSS": "mat-checkbox#cbLinkTracking.mat-mdc-checkbox.mat-accent.ng-untouched.ng-pristine.ng-valid.mat-mdc-checkbox-checked.e2e-mark-element", "value": {"value": "Turn off link tracking", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-pristine ng-valid mat-mdc-checkbox-checked e2e-mark-element", "text": "class"}, {"value": "cbLinkTracking", "text": "data-e2e-id"}, {"value": "cbLinkTracking", "text": "id"}, {"value": "Turn off link tracking", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417813110, "execute": [{"command": "assert-special:angular-checkbox-checked", "target": "//mat-checkbox[@data-e2e-id='cbLinkTracking']", "targetCss": "", "value": "", "special": "angular-checkbox-checked"}], "special": "", "specialOptions": ["angular-checkbox-checked", "angular-checkbox-not-checked"], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417813110.jpg"}, {"command": "assert-element", "target": "//mat-checkbox[@data-e2e-id='cbBillDraft']", "targetCSS": "mat-checkbox#cbBillDraft.mat-mdc-checkbox.mat-accent.ng-untouched.ng-pristine.ng-valid.mat-mdc-checkbox-checked.e2e-mark-element", "value": {"value": "Use for bill template", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-pristine ng-valid mat-mdc-checkbox-checked e2e-mark-element", "text": "class"}, {"value": "cbBillDraft", "text": "data-e2e-id"}, {"value": "cbBillDraft", "text": "id"}, {"value": "Use for bill template", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417817074, "execute": [{"command": "assert-special:angular-checkbox-checked", "target": "//mat-checkbox[@data-e2e-id='cbBillDraft']", "targetCss": "", "value": "", "special": "angular-checkbox-checked"}], "special": "", "specialOptions": ["angular-checkbox-checked", "angular-checkbox-not-checked"], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417817074.jpg"}, {"command": "assert-element", "target": "//mat-checkbox[@data-e2e-id='cbOptimizeSoi']", "targetCSS": "mat-checkbox#cbOptimizeSoi.mat-mdc-checkbox.mat-accent.ng-untouched.ng-pristine.ng-valid.mat-mdc-checkbox-checked.e2e-mark-element", "value": {"value": "Optimize SOI first sent", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-pristine ng-valid mat-mdc-checkbox-checked e2e-mark-element", "text": "class"}, {"value": "cbOptimizeSoi", "text": "data-e2e-id"}, {"value": "cbOptimizeSoi", "text": "id"}, {"value": "Optimize SOI first sent", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417820401, "execute": [{"command": "assert-special:angular-checkbox-checked", "target": "//mat-checkbox[@data-e2e-id='cbOptimizeSoi']", "targetCss": "", "value": "", "special": "angular-checkbox-checked"}], "special": "", "specialOptions": ["angular-checkbox-checked", "angular-checkbox-not-checked"], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417820401.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCSS": "html body kt-app div.kt-outer-container kt-email-settings-base.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-email-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-create-detail.ng-star-inserted form.ng-pristine.ng-valid.ng-touched div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.icon-right.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Save and continue", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Save and continue", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417823640, "execute": [{"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417823640.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='confirmTransactionalEmail-confirmBtn']", "targetCSS": "mat-dialog-container#mat-mdc-dialog-5.mat-mdc-dialog-container.mdc-dialog.cdk-dialog-container.mat-mdc-dialog-container-with-actions.mdc-dialog--closing div.mdc-dialog__container div.mat-mdc-dialog-surface.mdc-dialog__surface kt-modal.mat-mdc-dialog-component-host.ng-star-inserted div.mat-typography.kt-mat-library div.mat-typography.kt-drupal-theme mat-dialog-actions.mat-mdc-dialog-actions.mdc-dialog__actions kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Save & Edit email content", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Save & Edit email content", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417826257, "execute": [{"command": "click", "target": "//button[@data-e2e-id='confirmTransactionalEmail-confirmBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417826257.jpg"}, {"command": "assert-element", "target": "//div[@data-e2e-id='ee-subject-container']//div", "targetCSS": "html body kt-app div.kt-outer-container kt-email-editor.ng-star-inserted kt-email-editor-wrapper div.email-editor.ng-star-inserted kt-toolbar.ng-tns-c126875561-141.ng-star-inserted div.ee-topbar.ng-tns-c126875561-141.ng-star-inserted div.ee-title.ng-tns-c126875561-141 div.ee-subject-container.ng-tns-c126875561-141 div.ee-subject.ng-tns-c126875561-141.ng-star-inserted.e2e-mark-element", "value": {"value": "mysubject", "tagName": "div", "attributes": [{"value": "", "text": "_ngcontent-ng-c126875561"}, {"value": "ee-subject ng-tns-c126875561-141 ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "mysubject", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417837079, "execute": [], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417837079.jpg"}, {"command": "click", "target": "//kt-email-editor//kt-email-editor-wrapper//div//kt-toolbar//div//div//kt-email-settings-svg//svg//path", "targetCSS": "kt-email-editor.ng-star-inserted kt-email-editor-wrapper div.email-editor.ng-star-inserted kt-toolbar.ng-tns-c126875561-141.ng-star-inserted div.ee-topbar.ng-tns-c126875561-141.ng-star-inserted div.ee-brand.ng-tns-c126875561-141.ng-star-inserted kt-email-settings-svg.ng-tns-c126875561-141 svg path.e2e-mark-element", "value": {"value": "", "tagName": "path", "attributes": [{"value": "", "text": "_ngcontent-ng-c27729131"}, {"value": "M12.39,14.24l.08,0,0-.08a2.33,2.33,0,0,1,2.1-3.32,2.28,2.28,0,0,1,1,.22l.08,0,0-.08a2.37,2.37,0,0,1,\n        .88-1.14V7.92a.27.27,0,0,0-.14-.24.26.26,0,0,0-.28,0L10,11a2.52,2.52,0,0,1-2.62,0L1.26,7.69a.26.26,0,0,0-.28,\n        0,.27.27,0,0,0-.14.24v5.17a1.65,1.65,0,0,0,1.65,1.63h9.1A2.41,2.41,0,0,1,12.39,14.24Z", "text": "d"}, {"value": "translate(-0.84 -3.82)", "text": "transform"}, {"value": "fill: #5789f7;", "text": "style"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417839485, "execute": [{"command": "click", "target": "//kt-email-editor//kt-email-editor-wrapper//div//kt-toolbar//div//div//kt-email-settings-svg//svg//path", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417839485.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-details']", "targetCSS": "a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.first-item.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "Details", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "Details", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417847541, "execute": [{"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-details']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417847541.jpg"}, {"command": "assert-element", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCSS": "textarea#mat-input-32.mat-mdc-input-element.ng-tns-c3736059725-146.ng-untouched.ng-pristine.ng-valid.mat-mdc-form-field-textarea-control.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.e2e-mark-element", "value": {"value": "mysubject", "tagName": "textarea", "attributes": [{"value": "mynotes", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element ng-tns-c3736059725-146 ng-untouched ng-pristine ng-valid mat-mdc-form-field-textarea-control mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored e2e-mark-element", "text": "class"}, {"value": "100", "text": "cols"}, {"value": "8", "text": "rows"}, {"value": "entityNotes-input", "text": "data-e2e-id"}, {"value": "mat-input-32", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417853208, "execute": [], "special": "assert-angular-text-field-value", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417853208.jpg"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='entityLabels::mylabel']//span//span", "targetCSS": "mat-chip-row#mat-mdc-chip-4.mat-mdc-chip.mat-mdc-chip-row.mdc-evolution-chip.mat-primary.mdc-evolution-chip--with-trailing-action.mat-mdc-standard-chip.mat-mdc-chip-with-trailing-icon.ng-star-inserted.cdk-focused.cdk-mouse-focused span.mdc-evolution-chip__action.mat-mdc-chip-action.mdc-evolution-chip__cell.mdc-evolution-chip__cell--primary.mdc-evolution-chip__action--primary span.mdc-evolution-chip__text-label.mat-mdc-chip-action-label.e2e-mark-element", "value": {"value": "<PERSON><PERSON><PERSON><PERSON>", "tagName": "span", "attributes": [{"value": "mdc-evolution-chip__text-label mat-mdc-chip-action-label e2e-mark-element", "text": "class"}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417859237, "execute": [], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417859237.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='deleteBtn']", "targetCSS": "html body kt-app div.kt-outer-container kt-email-settings-base.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-email-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-create-detail.ng-star-inserted form.ng-valid.ng-touched.ng-dirty div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-warn.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Delete email", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Delete email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417862774, "execute": [{"command": "click", "target": "//button[@data-e2e-id='deleteBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417862774.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='deleteModal-secondaryBtn']", "targetCSS": "mat-dialog-container#mat-mdc-dialog-6.mat-mdc-dialog-container.mdc-dialog.cdk-dialog-container.mat-mdc-dialog-container-with-actions.mdc-dialog--closing div.mdc-dialog__container div.mat-mdc-dialog-surface.mdc-dialog__surface kt-modal.mat-mdc-dialog-component-host.ng-star-inserted div.mat-typography.kt-mat-library div.mat-typography.kt-drupal-theme mat-dialog-actions.mat-mdc-dialog-actions.mdc-dialog__actions kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-unthemed.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mat-mdc-button-touch-target.e2e-mark-element", "value": {"value": "", "tagName": "span", "attributes": [{"value": "mat-mdc-button-touch-target e2e-mark-element", "text": "class"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417868234, "execute": [{"command": "click", "target": "//button[@data-e2e-id='deleteModal-secondaryBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417868234.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='entityLabels::close::mylabel']", "targetCSS": "mat-chip-row#mat-mdc-chip-4.mat-mdc-chip.mat-mdc-chip-row.mdc-evolution-chip.mat-primary.mdc-evolution-chip--with-trailing-action.mat-mdc-standard-chip.mat-mdc-chip-with-trailing-icon.ng-star-inserted span.mdc-evolution-chip__cell.mdc-evolution-chip__cell--trailing.ng-star-inserted button.mat-mdc-chip-remove.mat-mdc-chip-trailing-icon.mat-mdc-focus-indicator.mdc-evolution-chip__icon.mdc-evolution-chip__icon--trailing.mdc-evolution-chip__action.mat-mdc-chip-action.mdc-evolution-chip__action--trailing.e2e-mark-element", "value": {"value": "", "tagName": "button", "attributes": [{"value": "button", "text": "role"}, {"value": "", "text": "matchipremove"}, {"value": "mat-mdc-chip-remove mat-mdc-chip-trailing-icon mat-mdc-focus-indicator mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing mdc-evolution-chip__action mat-mdc-chip-action mdc-evolution-chip__action--trailing e2e-mark-element", "text": "class"}, {"value": "button", "text": "type"}, {"value": "entityLabels::close::mylabel", "text": "data-e2e-id"}, {"value": "-1", "text": "tabindex"}, {"value": "false", "text": "aria-disabled"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417872376, "execute": [{"command": "click", "target": "//button[@data-e2e-id='entityLabels::close::mylabel']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417872376.jpg"}, {"command": "click", "target": "//div", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1722417874656, "execute": [{"command": "click", "target": "//div", "targetCss": "", "value": "", "special": "somewhere"}], "special": "somewhere", "specialOptions": [], "somewhereReason": "multiple-targets", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417874656.jpg"}, {"command": "click", "target": "//html//body//kt-app//div//kt-email-settings-base//div//section//kt-email-detail//div//kt-affix", "targetCSS": "html body kt-app div.kt-outer-container kt-email-settings-base.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-email-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-affix.kt-flex-15.ng-star-inserted.e2e-mark-element", "value": {"value": "Details\nEdit\nDD Editor\nAnalysis\nStatistics", "tagName": "kt-affix", "attributes": [{"value": "", "text": "_ngcontent-ng-c518803727"}, {"value": "emailDetailAffix", "text": "nodeid"}, {"value": "kt-flex-15 ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Details\nEdit\nDD Editor\nAnalysis\nStatistics", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417879094, "execute": [{"command": "click", "target": "//html//body//kt-app//div//kt-email-settings-base//div//section//kt-email-detail//div//kt-affix", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417879094.jpg"}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='entityNotesEnabled']", "targetCSS": "mat-checkbox#entityNotesEnabled.mat-mdc-checkbox.mat-accent.ng-untouched.ng-valid.e2e-mark-element.ng-dirty", "value": {"value": "Make notes", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-valid e2e-mark-element ng-dirty", "text": "class"}, {"value": "entityNotesEnabled", "text": "data-e2e-id"}, {"value": "entityNotesEnabled", "text": "id"}, {"value": "Make notes", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417883092, "execute": [{"command": "click", "target": "//mat-checkbox[@data-e2e-id='entityNotesEnabled']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417883092.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCSS": "html body kt-app div.kt-outer-container kt-email-settings-base.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-email-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-create-detail.ng-star-inserted form.ng-valid.ng-touched.ng-dirty div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.icon-right.mat-primary.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Save and continue", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Save and continue", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417886205, "execute": [{"command": "click", "target": "//button[@data-e2e-id='saveBtnEdit']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417886205.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-details']", "targetCSS": "a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.first-item.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content.e2e-mark-element", "value": {"value": "Details", "tagName": "span", "attributes": [{"value": "mdc-list-item__content e2e-mark-element", "text": "class"}, {"value": "Details", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417890427, "execute": [{"command": "click", "target": "//a[@data-e2e-id='emailDetailAffix-item-details']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417890427.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='deleteBtn']", "targetCSS": "html body kt-app div.kt-outer-container kt-email-settings-base.ng-star-inserted div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-email-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-email-create-detail.ng-star-inserted form.ng-valid.ng-touched.ng-dirty div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-warn.mat-mdc-button-base.mat-mdc-tooltip-disabled.cdk-focused.cdk-mouse-focused span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Delete email", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Delete email", "text": "innerText"}], "selectOptions": []}, "timestamp": 1722417903359, "execute": [{"command": "click", "target": "//button[@data-e2e-id='deleteBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1722417903359.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='deleteModal-confirmBtn']", "targetCSS": "kt-modal.mat-mdc-dialog-component-host.ng-star-inserted div.mat-typography.kt-mat-library div.mat-typography.kt-drupal-theme mat-dialog-actions.mat-mdc-dialog-actions.mdc-dialog__actions kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-warn.mat-mdc-button-base.mat-mdc-tooltip-disabled span.mdc-button__label span.ng-star-inserted.e2e-mark-element", "value": {"value": "Delete", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Delete", "text": "innerText"}], "selectOptions": []}, "timestamp": *************, "execute": [{"command": "click", "target": "//button[@data-e2e-id='deleteModal-confirmBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "specialOptions": [], "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/*************.jpg"}], "datasets": {"url": "user/me", "testCaseDependency": 0, "testCaseDataCreation": [], "roleNames": ["Enterprise"], "userGroupId": "43", "tierInAccount": "10000"}, "machineName": "test_email__automationemail__dd__create_edit_delete_*************", "pipelineReady": true, "zephyrTest": "T399", "repoReady": true, "repoPath": "emails", "zephyrTestUrl": "https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-T399"}
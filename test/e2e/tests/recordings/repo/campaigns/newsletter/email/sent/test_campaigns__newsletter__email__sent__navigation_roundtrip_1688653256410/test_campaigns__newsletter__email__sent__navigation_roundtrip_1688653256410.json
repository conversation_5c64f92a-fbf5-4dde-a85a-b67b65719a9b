{"id": *************, "displayName": "Campaigns::Newsletter::Email::Sent::Navigation Roundtrip", "description": "Test the navigation (Affix and Buttons)", "status": "recorded", "zephyrTest": "T373", "data": {"url": "https://app.ktlocal.com/", "testCaseDependency": 0, "testCaseDataCreation": [{"class": "E2eDataReset", "params": {}}, {"class": "E2eDataAccount", "params": {"userGroupId": "43", "roleNames": ["Enterprise"], "tierInAccount": "10000"}}, {"class": "App\\Klicktipp\\E2e\\E2eDataNewsletter", "params": {"name": "e2e", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichDraftEmpty": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichDraftReady": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailDdDraftEmpty": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailDdDraftReady": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestEmpty": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestReady": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichScheduledNow": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichScheduledFuture": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichSending": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichSent": "1", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestScheduleNow": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestScheduleFuture": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestRunning": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestFinished": "0", "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestSent": "0"}}]}, "steps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-newsletter-me']", "targetCss": "", "value": "", "special": ""}, {"command": "redirect-wait", "target": "", "targetCss": "", "value": ""}, {"command": "click", "target": "//mat-select[@data-e2e-id='statusSelect-select']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//mat-option[@data-e2e-id='statusSelect-select-option-3']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//h1[contains(text(),'Overview')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//span[contains(text(),'Status: Sent on')]", "targetCss": "", "value": "", "special": "", "screenshot": {"masked": {"1366x2000": [[168, 168, 168, 168], [168, 85, 1186, 709]], "1366x768": [[168, 85, 1186, 709]]}, "mode": ""}}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b[contains(text(),'18')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b[contains(text(),'11')]", "targetCss": "", "value": "", "special": "", "screenshot": {"masked": {"1366x2000": [[168, 168, 168, 168], [168, 85, 1186, 709]], "1366x768": [[168, 85, 1186, 709]]}, "mode": ""}}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b[contains(text(),'11')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-link-stats-r0-c0']//div//div//a", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-link-stats-r0-c1']//div[contains(text(),'5')]", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-link-stats-r0-c2']//div[contains(text(),'8')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-newsletter-create-detail//h1[contains(text(),'Newsletter Settings')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='cancelBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//h1[contains(text(),'Overview')]", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='breadcrumbs-ele-1']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCss": "", "value": "", "special": ""}], "recordingSteps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCSS": "nav#top-menu.navbar.navbar-default.navbar-fixed-top.navbar-inverse div.container div.collapse.navbar-collapse.navbar-responsive-collapse ul.nav.navbar-nav li.dropdown.open a.dropdown-toggle.e2e-mark-element", "value": {"value": "Campaigns", "tagName": "a", "attributes": [{"value": "#", "text": "href"}, {"value": "dropdown-toggle e2e-mark-element", "text": "class"}, {"value": "dropdown", "text": "data-toggle"}, {"value": "main-3", "text": "data-e2e-id"}, {"value": "Campaigns", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653275697, "execute": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653275697.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-newsletter-me']", "targetCSS": "nav#top-menu.navbar.navbar-default.navbar-fixed-top.navbar-inverse div.container div.collapse.navbar-collapse.navbar-responsive-collapse ul.nav.navbar-nav li.dropdown ul.dropdown-menu li.dropdown-submenu a.dropdown-submenu-toggle.e2e-mark-element", "value": {"value": "Newsletter", "tagName": "a", "attributes": [{"value": "/campaigns/me", "text": "href"}, {"value": "main-3::application-me-campaign-newsletter-me", "text": "data-e2e-id"}, {"value": "dropdown-submenu-toggle e2e-mark-element", "text": "class"}, {"value": "dropdown-submenu-toggle", "text": "data-toggle"}, {"value": "Newsletter", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653277005, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-newsletter-me']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653277005.jpg"}, {"command": "redirect-wait", "target": "", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1688653277575, "screenshot": [], "execute": [{"command": "redirect-wait", "target": "", "targetCss": "", "value": ""}], "special": "", "somewhereReason": ""}, {"command": "dropdown-angular-input", "target": "//mat-select[@data-e2e-id='statusSelect-select']", "targetCSS": "mat-option#mat-option-3.mat-mdc-option.mdc-list-item.ng-tns-c34-1.ng-star-inserted.mdc-list-item--selected.mat-mdc-option-active span.mdc-list-item__primary-text.e2e-mark-element", "value": {"value": "//mat-option[@data-e2e-id='statusSelect-select-option-sent']", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1688653282305, "execute": [{"command": "dropdown-angular-input", "target": "//div[@id='mat-select-value-65']", "targetCss": "", "value": "//mat-option[@data-e2e-id='statusSelect-select-option-sent']", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653282305.jpg"}, {"command": "click", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCSS": "tr.mat-mdc-row.mdc-data-table__row.cdk-row.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-name.mat-column-name.ng-star-inserted div.kt-table-default div.table-default.ellipsis.is-link a.ng-star-inserted.e2e-mark-element", "value": {"value": "e2e EmailRichSent ", "tagName": "a", "attributes": [{"value": "/app/campaign/newsletter/11/1210", "text": "href"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "e2e EmailRichSent ", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653283888, "execute": [{"command": "click", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653283888.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted h1.e2e-mark-element", "value": {"value": "Overview", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-htg-c134"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Overview", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653287132, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//h1", "attribute": "innerText", "value": "Overview", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653287132.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//span", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted div.status-bar.ng-star-inserted div.status-container span.e2e-mark-element", "value": {"value": "Status: Sent on 06.07.2023 16:22:03", "tagName": "span", "attributes": [{"value": "", "text": "_ngcontent-htg-c134"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Status: Sent on 06.07.2023 16:22:03", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653289508, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//span", "attribute": "innerText", "value": "Status: Sent on 06.07.2023 16:22:03", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653289508.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted div.donut-chart-container.ng-star-inserted div.donut-stats div.total-stats b.e2e-mark-element", "value": {"value": "18", "tagName": "b", "attributes": [{"value": "", "text": "_ngcontent-htg-c134"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "18", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653294255, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b", "attribute": "innerText", "value": "18", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653294255.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted div.donut-chart-container.ng-star-inserted div.donut-stats div.total-stats b.e2e-mark-element", "value": {"value": "11", "tagName": "b", "attributes": [{"value": "", "text": "_ngcontent-htg-c134"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "11", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653297402, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b", "attribute": "innerText", "value": "11", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653297402.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted div.donut-chart-container.ng-star-inserted div.donut-stats div.total-stats b.e2e-mark-element", "value": {"value": "11", "tagName": "b", "attributes": [{"value": "", "text": "_ngcontent-htg-c134"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "11", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653300231, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b", "attribute": "innerText", "value": "11", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653300231.jpg"}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-link-stats-r0-c0']//div//div//a", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted kt-table.ng-star-inserted div form.ng-untouched.ng-pristine.ng-valid table.mat-mdc-table.mdc-data-table__table.cdk-table.mat-sort tbody.mdc-data-table__content tr.mat-mdc-row.mdc-data-table__row.cdk-row.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-link.mat-column-link.ng-star-inserted div.kt-table-default div.table-default.ellipsis.is-link a.ng-star-inserted.e2e-mark-element", "value": {"value": "Link to Klicktipp", "tagName": "a", "attributes": [{"value": "Link to Klicktipp", "text": "href"}, {"value": "_self", "text": "target"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Link to Klicktipp", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653303643, "execute": [{"command": "assert-xpath", "target": "//td[@data-e2e-id='table-link-stats-r0-c0']//div//div//a", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653303643.jpg"}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-link-stats-r0-c1']//div//div//span", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted kt-table.ng-star-inserted div form.ng-untouched.ng-pristine.ng-valid table.mat-mdc-table.mdc-data-table__table.cdk-table.mat-sort tbody.mdc-data-table__content tr.mat-mdc-row.mdc-data-table__row.cdk-row.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-uniqueClicks.mat-column-uniqueClicks.align-right.ng-star-inserted div.kt-table-default div.table-default.ellipsis span.ng-star-inserted.e2e-mark-element", "value": {"value": "5", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "5", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653306504, "execute": [{"command": "assert-attribute:innerText", "xPath": "//td[@data-e2e-id='table-link-stats-r0-c1']//div//div//span", "attribute": "innerText", "value": "5", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653306504.jpg"}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-link-stats-r0-c2']//div//div//span", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted kt-table.ng-star-inserted div form.ng-untouched.ng-pristine.ng-valid table.mat-mdc-table.mdc-data-table__table.cdk-table.mat-sort tbody.mdc-data-table__content tr.mat-mdc-row.mdc-data-table__row.cdk-row.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-totalClicks.mat-column-totalClicks.align-right.ng-star-inserted div.kt-table-default div.table-default.ellipsis span.ng-star-inserted.e2e-mark-element", "value": {"value": "8", "tagName": "span", "attributes": [{"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "8", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653309121, "execute": [{"command": "assert-attribute:innerText", "xPath": "//td[@data-e2e-id='table-link-stats-r0-c2']//div//div//span", "attribute": "innerText", "value": "8", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653309121.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 kt-nav-list.kt-flex-15.ng-star-inserted mat-nav-list.mat-mdc-nav-list.mat-mdc-list-base.mdc-list a.mat-mdc-list-item.mdc-list-item.mat-mdc-list-item-interactive.mat-mdc-list-item-single-line.mdc-list-item--with-one-line.ng-star-inserted.selectedListItem span.mdc-list-item__content span.mat-mdc-list-item-unscoped-content.mdc-list-item__primary-text.e2e-mark-element", "value": {"value": "Settings", "tagName": "span", "attributes": [{"value": "mat-mdc-list-item-unscoped-content mdc-list-item__primary-text e2e-mark-element", "text": "class"}, {"value": "Settings", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653314235, "execute": [{"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653314235.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-newsletter-create-detail//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-newsletter-create-detail.ng-star-inserted h1.ng-star-inserted.e2e-mark-element", "value": {"value": "Newsletter Settings", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-htg-c136"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Newsletter Settings", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653317077, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-newsletter-create-detail//h1", "attribute": "innerText", "value": "Newsletter Settings", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653317077.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='cancelBtn']", "targetCSS": "kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-newsletter-create-detail.ng-star-inserted form.ng-untouched.ng-pristine.ng-valid div.mt-15 kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-unthemed.mat-mdc-button-base.ng-star-inserted span.mdc-button__label.e2e-mark-element", "value": {"value": " Cancel ", "tagName": "span", "attributes": [{"value": "mdc-button__label e2e-mark-element", "text": "class"}, {"value": " Cancel ", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653319684, "execute": [{"command": "click", "target": "//button[@data-e2e-id='cancelBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653319684.jpg"}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-overview.ng-star-inserted kt-table.ng-star-inserted div.automation-overview form.ng-untouched.ng-pristine.ng-valid table.mat-mdc-table.mdc-data-table__table.cdk-table.mat-sort tbody.mdc-data-table__content tr.mat-mdc-row.mdc-data-table__row.cdk-row.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-name.mat-column-name.ng-star-inserted div.kt-table-default div.table-default.ellipsis.is-link a.ng-star-inserted.e2e-mark-element", "value": {"value": "e2e EmailRichSent", "tagName": "a", "attributes": [{"value": "/app/campaign/newsletter/11/1210", "text": "href"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "e2e EmailRichSent", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653323880, "execute": [{"command": "assert-xpath", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653323880.jpg"}, {"command": "click", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCSS": "tr.mat-mdc-row.mdc-data-table__row.cdk-row.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-name.mat-column-name.ng-star-inserted div.kt-table-default div.table-default.ellipsis.is-link a.ng-star-inserted.e2e-mark-element", "value": {"value": "e2e EmailRichSent ", "tagName": "a", "attributes": [{"value": "/app/campaign/newsletter/11/1210", "text": "href"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "e2e EmailRichSent ", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653326518, "execute": [{"command": "click", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653326518.jpg"}, {"command": "assert-element", "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//h1", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-detail.ng-star-inserted div.kt-flex-row.kt-flex-col-lt-md.kt-flex-gap-20 div.kt-flex-85 kt-item-detail-overview.ng-star-inserted h1.e2e-mark-element", "value": {"value": "Overview", "tagName": "h1", "attributes": [{"value": "", "text": "_ngcontent-htg-c134"}, {"value": "e2e-mark-element", "text": "class"}, {"value": "Overview", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653335012, "execute": [{"command": "assert-attribute:innerText", "xPath": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//h1", "attribute": "innerText", "value": "Overview", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653335012.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='breadcrumbs-ele-1']", "targetCSS": "li.ng-star-inserted span.breadcrumb-current-route.breadcrumb-history-link.ng-star-inserted a.ng-star-inserted.e2e-mark-element", "value": {"value": "Newsletter", "tagName": "a", "attributes": [{"value": "/app/campaign/newsletter/11", "text": "href"}, {"value": "breadcrumbs-ele-1", "text": "data-e2e-id"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "Newsletter", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653336701, "execute": [{"command": "click", "target": "//a[@data-e2e-id='breadcrumbs-ele-1']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653336701.jpg"}, {"command": "assert-element", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCSS": "html body kt-app div.kt-outer-container ng-component div.mat-typography.kt-module.kt-mat-library section.kt-drupal-theme.kt-module-wrapper.mb-70 kt-newsletter-overview.ng-star-inserted kt-table.ng-star-inserted div.automation-overview form.ng-untouched.ng-pristine.ng-valid table.mat-mdc-table.mdc-data-table__table.cdk-table.mat-sort tbody.mdc-data-table__content tr.mat-mdc-row.mdc-data-table__row.cdk-row.ng-star-inserted td.mat-mdc-cell.mdc-data-table__cell.cdk-cell.cdk-column-name.mat-column-name.ng-star-inserted div.kt-table-default div.table-default.ellipsis.is-link a.ng-star-inserted.e2e-mark-element", "value": {"value": "e2e EmailRichSent", "tagName": "a", "attributes": [{"value": "/app/campaign/newsletter/11/1210", "text": "href"}, {"value": "ng-star-inserted e2e-mark-element", "text": "class"}, {"value": "e2e EmailRichSent", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688653340358, "execute": [{"command": "assert-xpath", "target": "//td[@data-e2e-id='table-overview-newsletter-r0-c1']//div//div//a", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688653340358.jpg"}], "datasets": {"url": "https://app.ktlocal.com/", "testCaseDependency": 0, "testCaseDataCreation": [{"id": "App\\Klicktipp\\E2e\\E2eDataNewsletter", "name": "Create a set of Newsletter in all possible states", "description": "TODO: Description", "index": 0, "config": [{"id": "name", "title": "Name-prefix", "description": "If count > 1, an index is appended to this prefix.", "formField": "textfield", "value": "e2e"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichDraftEmpty", "title": "EmailRichDraftEmpty", "description": "Creates an empty RichText Newsletter (default settings, no subject or content)", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichDraftReady", "title": "EmailRichDraftReady", "description": "Creates a RichText Newsletter with subject and content that is ready to be scheduled", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailDdDraftEmpty", "title": "E2eNewsletterEmailDdDraftEmpty", "description": "Creates a D&D Newsletter (default settings, no subject or content, not published)", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailDdDraftReady", "title": "EmailDdDraftReady", "description": "Creates a D&D Newsletter with published subject and content that is ready to be scheduled", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestEmpty", "title": "EmailSplitTestEmpty", "description": "Creates a Split Test Newsletter (default settings, rich email with no subject or content)", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestReady", "title": "EmailSplitTestReady", "description": "Creates a Split Test Newsletter with 2 emails (rich and D&D) that is ready to be scheduled", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichScheduledNow", "title": "EmailRichScheduledNow", "description": "Creates a scheduled (now) Newsletter with pending/subscribed/unsubscribed subscribers", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichScheduledFuture", "title": "EmailRichScheduledFuture", "description": "Creates a scheduled (in 1 week) Newsletter with pending/subscribed/unsubscribed subscribers", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichSending", "title": "EmailRichSending", "description": "Creates an outgoing Newsletter with pending/subscribed/unsubscribed subscribers", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailRichSent", "title": "EmailRichSent", "description": "Creates a sent Newsletter with stats", "formField": "number", "value": "1"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestScheduleNow", "title": "EmailSplitTestScheduleNow", "description": "Creates a scheduled (now) Split Test Newsletter with pending/subscribed/unsubscribed subscribers", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestScheduleFuture", "title": "EmailSplitTestScheduleFuture", "description": "Creates a scheduled (in 1 week) Split Test Newsletter with pending/subscribed/unsubscribed subscribers", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestRunning", "title": "EmailSplitTestScheduleRunning", "description": "Creates a running Split Test Newsletter with pending/subscribed/unsubscribed subscribers", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestFinished", "title": "EmailSplitTestFinished", "description": "Creates a running Split Test Newsletter (Split Test finished, but winners not sent yet) with pending/subscribed/unsubscribed subscribers", "formField": "number", "value": "0"}, {"id": "App\\Klicktipp\\E2e\\Entity\\Campaign\\Newsletter\\E2eNewsletterEmailSplitTestSent", "title": "EmailSplitTestSent", "description": "Creates a sent Split Test Newsletter (Split Test finished, winner sent) with pending/subscribed/unsubscribed subscribers", "formField": "number", "value": "0"}]}], "roleNames": ["Enterprise"], "userGroupId": "43", "tierInAccount": "10000"}, "machineName": "test_campaigns__newsletter__email__sent__navigation_roundtrip_*************", "repoPath": "campaigns/newsletter/email/sent", "repoReady": true, "pipelineReady": true, "zephyrTestUrl": "https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-T373"}
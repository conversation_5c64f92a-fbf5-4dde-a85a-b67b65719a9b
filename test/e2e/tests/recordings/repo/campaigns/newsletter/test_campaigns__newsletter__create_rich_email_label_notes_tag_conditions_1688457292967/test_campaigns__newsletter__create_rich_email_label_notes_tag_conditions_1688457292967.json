{"id": *************, "displayName": "Campaigns::Newsletter::Create rich email label notes tag conditions", "description": "Creates a rich email newsletter with create settings", "status": "recorded", "data": {"url": "https://app.ktlocal.com/", "testCaseDependency": 0, "testCaseDataCreation": [{"class": "E2eDataReset", "params": {}}, {"class": "E2eDataAccount", "params": {"userGroupId": "43", "roleNames": ["Enterprise"], "tierInAccount": "10000"}}]}, "steps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-newsletter-me']", "targetCss": "", "value": "", "special": ""}, {"command": "redirect-wait", "target": "", "targetCss": "", "value": ""}, {"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='editor-select-btn-rt']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "create", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCss": "", "value": "create", "special": ""}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='entityNotesEnabled']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCss": "", "value": "notes", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='selectedTaggedWithMS-chip-list-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='selectedTaggedWithMS-chip-list-input']", "targetCss": "", "value": "has", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='selectedNotTaggedWithMS-chip-list-input']", "targetCss": "", "value": "", "special": ""}, {"command": "text-input", "target": "//input[@data-e2e-id='selectedNotTaggedWithMS-chip-list-input']", "targetCss": "", "value": "has not", "special": ""}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//a[@data-e2e-id='campaignAffix-item-edit']", "targetCss": "", "value": "", "special": ""}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "create", "special": "assert-angular-text-field-value"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='entityLabels::create']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//input[@id='entityNotesEnabled-input']", "targetCss": "", "value": "true", "special": "assert-angular-checkbox"}, {"command": "assert-element", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCss": "", "value": "notes", "special": "assert-angular-text-field-value"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='selectedTaggedWithMS::has']", "targetCss": "", "value": "", "special": ""}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='selectedNotTaggedWithMS::has-not']", "targetCss": "", "value": "", "special": ""}], "recordingSteps": [{"command": "click", "target": "//div[@data-e2e-id='main-3']", "targetCSS": "nav#top-menu.navbar.navbar-default.navbar-fixed-top.navbar-inverse div.container div.collapse.navbar-collapse.navbar-responsive-collapse ul.nav.navbar-nav li.dropdown.open a.dropdown-toggle.e2e-mark-element", "value": {"value": "Campaigns", "tagName": "a", "attributes": [{"value": "#", "text": "href"}, {"value": "dropdown-toggle e2e-mark-element", "text": "class"}, {"value": "dropdown", "text": "data-toggle"}, {"value": "main-3", "text": "data-e2e-id"}, {"value": "Campaigns", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457305398, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-3']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457305398.jpg"}, {"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-newsletter-me']", "targetCSS": "nav#top-menu.navbar.navbar-default.navbar-fixed-top.navbar-inverse div.container div.collapse.navbar-collapse.navbar-responsive-collapse ul.nav.navbar-nav li.dropdown ul.dropdown-menu li.dropdown-submenu a.dropdown-submenu-toggle.e2e-mark-element", "value": {"value": "Newsletter", "tagName": "a", "attributes": [{"value": "/campaigns/me", "text": "href"}, {"value": "main-3::campaigns-me", "text": "data-e2e-id"}, {"value": "dropdown-submenu-toggle e2e-mark-element", "text": "class"}, {"value": "dropdown-submenu-toggle", "text": "data-toggle"}, {"value": "Newsletter", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457308835, "execute": [{"command": "click", "target": "//a[@data-e2e-id='main-3::application-me-campaign-newsletter-me']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457308835.jpg"}, {"command": "redirect-wait", "target": "", "targetCSS": "", "value": {"value": "", "tagName": "", "attributes": [], "selectOptions": []}, "timestamp": 1688457309117, "screenshot": [], "execute": [{"command": "redirect-wait", "target": "", "targetCss": "", "value": ""}], "special": "", "somewhereReason": ""}, {"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCSS": "kt-newsletter-overview div.full-width.align-right kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-accent.mat-mdc-button-base span.mdc-button__label.e2e-mark-element", "value": {"value": " Create email newsletter ", "tagName": "span", "attributes": [{"value": "mdc-button__label e2e-mark-element", "text": "class"}, {"value": " Create email newsletter ", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457312092, "execute": [{"command": "click", "target": "//button[@data-e2e-id='addBtn-0']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457312092.jpg"}, {"command": "click", "target": "//mat-radio-button[@data-e2e-id='editorType-option-1']", "targetCSS": "kt-email-create-menu div.email-choose-editor div.editor-option.rich-text-editor div kt-button button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-accent.mat-mdc-button-base span.mdc-button__label.e2e-mark-element", "value": {"value": " Continue ", "tagName": "span", "attributes": [{"value": "mdc-button__label e2e-mark-element", "text": "class"}, {"value": " Continue ", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457314223, "execute": [{"command": "click", "target": "//mat-radio-button[@data-e2e-id='editorType-option-1']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457314223.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.ng-tns-c22-5.mat-mdc-form-field-input-control.mdc-text-field__input.ng-untouched.ng-pristine.cdk-text-field-autofill-monitored.ng-invalid.e2e-mark-element", "value": {"value": "", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-tns-c22-5 mat-mdc-form-field-input-control mdc-text-field__input ng-untouched ng-pristine cdk-text-field-autofill-monitored ng-invalid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "Name", "text": "placeholder"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457316550, "execute": [{"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457316550.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-1.mat-mdc-input-element.custom-autofill.ng-tns-c22-5.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-dirty.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "create", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-tns-c22-5 mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-dirty ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "Name", "text": "placeholder"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "mat-input-1", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457320746, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "create", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457320746.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCSS": "input#mat-input-2.mat-mdc-autocomplete-trigger.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-form-field-input-control.ng-pristine.cdk-text-field-autofill-monitored.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "create", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-autocomplete-trigger mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-form-field-input-control ng-pristine cdk-text-field-autofill-monitored ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "entityLabels-chip-list-input", "text": "data-e2e-id"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-input-2", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457321832, "execute": [{"command": "click", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457321832.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCSS": "input#mat-input-2.mat-mdc-autocomplete-trigger.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-form-field-input-control.cdk-text-field-autofill-monitored.ng-valid.ng-touched.ng-dirty.e2e-mark-element", "value": {"value": "create", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-autocomplete-trigger mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-form-field-input-control cdk-text-field-autofill-monitored ng-valid ng-touched ng-dirty e2e-mark-element", "text": "class"}, {"value": "entityLabels-chip-list-input", "text": "data-e2e-id"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-input-2", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457329487, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='entityLabels-chip-list-input']", "targetCss": "", "value": "create", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457329487.jpg"}, {"command": "click", "target": "//mat-checkbox[@data-e2e-id='entityNotesEnabled']", "targetCSS": "mat-checkbox#entityNotesEnabled.mat-mdc-checkbox.mat-accent.ng-untouched.ng-valid.mat-mdc-checkbox-checked.ng-dirty.e2e-mark-element", "value": {"value": "Make notes", "tagName": "mat-checkbox", "attributes": [{"value": "mat-mdc-checkbox mat-accent ng-untouched ng-valid mat-mdc-checkbox-checked ng-dirty e2e-mark-element", "text": "class"}, {"value": "entityNotesEnabled", "text": "data-e2e-id"}, {"value": "entityNotesEnabled", "text": "id"}, {"value": "Make notes", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457330607, "execute": [{"command": "click", "target": "//mat-checkbox[@data-e2e-id='entityNotesEnabled']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457330607.jpg"}, {"command": "click", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCSS": "textarea#mat-input-0.mat-mdc-input-element.ng-tns-c22-4.mat-mdc-form-field-textarea-control.mat-mdc-form-field-input-control.mdc-text-field__input.ng-untouched.cdk-text-field-autofill-monitored.ng-valid.e2e-mark-element.ng-dirty", "value": {"value": "n", "tagName": "textarea", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element ng-tns-c22-4 mat-mdc-form-field-textarea-control mat-mdc-form-field-input-control mdc-text-field__input ng-untouched cdk-text-field-autofill-monitored ng-valid e2e-mark-element ng-dirty", "text": "class"}, {"value": "100", "text": "cols"}, {"value": "8", "text": "rows"}, {"value": "entityNotes-input", "text": "data-e2e-id"}, {"value": "mat-input-0", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457333218, "execute": [{"command": "click", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457333218.jpg"}, {"command": "text-input", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCSS": "textarea#mat-input-0.mat-mdc-input-element.ng-tns-c22-4.mat-mdc-form-field-textarea-control.mat-mdc-form-field-input-control.mdc-text-field__input.cdk-text-field-autofill-monitored.ng-valid.ng-dirty.ng-touched.e2e-mark-element", "value": {"value": "notes", "tagName": "textarea", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element ng-tns-c22-4 mat-mdc-form-field-textarea-control mat-mdc-form-field-input-control mdc-text-field__input cdk-text-field-autofill-monitored ng-valid ng-dirty ng-touched e2e-mark-element", "text": "class"}, {"value": "100", "text": "cols"}, {"value": "8", "text": "rows"}, {"value": "entityNotes-input", "text": "data-e2e-id"}, {"value": "mat-input-0", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457337263, "execute": [{"command": "text-input", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCss": "", "value": "notes", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457337263.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='selectedTaggedWithMS-chip-list-input']", "targetCSS": "input#mat-input-4.mat-mdc-autocomplete-trigger.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-form-field-input-control.ng-pristine.cdk-text-field-autofill-monitored.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "notes", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-autocomplete-trigger mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-form-field-input-control ng-pristine cdk-text-field-autofill-monitored ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "selectedTaggedWithMS-chip-list-input", "text": "data-e2e-id"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-input-4", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457338379, "execute": [{"command": "click", "target": "//input[@data-e2e-id='selectedTaggedWithMS-chip-list-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457338379.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='selectedTaggedWithMS-chip-list-input']", "targetCSS": "input#mat-input-4.mat-mdc-autocomplete-trigger.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-form-field-input-control.cdk-text-field-autofill-monitored.ng-valid.ng-touched.ng-dirty.e2e-mark-element", "value": {"value": "has", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-autocomplete-trigger mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-form-field-input-control cdk-text-field-autofill-monitored ng-valid ng-touched ng-dirty e2e-mark-element", "text": "class"}, {"value": "selectedTaggedWithMS-chip-list-input", "text": "data-e2e-id"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-input-4", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457341850, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='selectedTaggedWithMS-chip-list-input']", "targetCss": "", "value": "has", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457341850.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='selectedNotTaggedWithMS-chip-list-input']", "targetCSS": "input#mat-input-5.mat-mdc-autocomplete-trigger.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-form-field-input-control.ng-pristine.cdk-text-field-autofill-monitored.ng-valid.ng-touched.e2e-mark-element", "value": {"value": "has", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-autocomplete-trigger mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-form-field-input-control ng-pristine cdk-text-field-autofill-monitored ng-valid ng-touched e2e-mark-element", "text": "class"}, {"value": "selectedNotTaggedWithMS-chip-list-input", "text": "data-e2e-id"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-input-5", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457342967, "execute": [{"command": "click", "target": "//input[@data-e2e-id='selectedNotTaggedWithMS-chip-list-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457342967.jpg"}, {"command": "text-input", "target": "//input[@data-e2e-id='selectedNotTaggedWithMS-chip-list-input']", "targetCSS": "input#mat-input-5.mat-mdc-autocomplete-trigger.mat-mdc-chip-input.mat-mdc-input-element.mdc-text-field__input.mat-input-element.mat-mdc-form-field-input-control.ng-valid.ng-touched.ng-dirty.e2e-mark-element", "value": {"value": "has not", "tagName": "input.null", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-autocomplete-trigger mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element mat-mdc-form-field-input-control ng-valid ng-touched ng-dirty e2e-mark-element", "text": "class"}, {"value": "selectedNotTaggedWithMS-chip-list-input", "text": "data-e2e-id"}, {"value": "off", "text": "autocomplete"}, {"value": "combobox", "text": "role"}, {"value": "list", "text": "aria-autocomplete"}, {"value": "false", "text": "aria-expanded"}, {"value": "listbox", "text": "aria-haspopup"}, {"value": "mat-input-5", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457350159, "execute": [{"command": "text-input", "target": "//input[@data-e2e-id='selectedNotTaggedWithMS-chip-list-input']", "targetCss": "", "value": "has not", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457350159.jpg"}, {"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCSS": "kt-item-create kt-newsletter-create-detail.ng-star-inserted form.ng-dirty.ng-valid.ng-touched.ng-submitted div.mt-15 kt-button.ng-star-inserted button.mat-mdc-tooltip-trigger.mdc-button.mdc-button--unelevated.mat-mdc-unelevated-button.mat-primary.mat-mdc-button-base.ng-star-inserted span.mdc-button__label.e2e-mark-element", "value": {"value": " Create Newsletter ", "tagName": "span", "attributes": [{"value": "mdc-button__label e2e-mark-element", "text": "class"}, {"value": " Create Newsletter ", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457351529, "execute": [{"command": "click", "target": "//button[@data-e2e-id='createBtn']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457351529.jpg"}, {"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-10.mat-mdc-input-element.custom-autofill.ng-tns-c22-52.mat-mdc-form-field-input-control.mdc-text-field__input.ng-untouched.ng-pristine.cdk-text-field-autofill-monitored.ng-valid.e2e-mark-element", "value": {"value": "has not", "tagName": "input.text", "attributes": [{"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-tns-c22-52 mat-mdc-form-field-input-control mdc-text-field__input ng-untouched ng-pristine cdk-text-field-autofill-monitored ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "Name", "text": "placeholder"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "mat-input-10", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457355036, "execute": [{"command": "click", "target": "//input[@data-e2e-id='entityName-input']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457355036.jpg"}, {"command": "assert-element", "target": "//input[@data-e2e-id='entityName-input']", "targetCSS": "input#mat-input-10.mat-mdc-input-element.custom-autofill.ng-tns-c22-52.mat-mdc-form-field-input-control.mdc-text-field__input.ng-untouched.ng-pristine.cdk-text-field-autofill-monitored.ng-valid.e2e-mark-element", "value": {"value": "create", "tagName": "input.text", "attributes": [{"value": "create", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element custom-autofill ng-tns-c22-52 mat-mdc-form-field-input-control mdc-text-field__input ng-untouched ng-pristine cdk-text-field-autofill-monitored ng-valid e2e-mark-element", "text": "class"}, {"value": "text", "text": "type"}, {"value": "undefined", "text": "min"}, {"value": "undefined", "text": "max"}, {"value": "Name", "text": "placeholder"}, {"value": "entityName-input", "text": "data-e2e-id"}, {"value": "mat-input-10", "text": "id"}, {"value": "", "text": "required"}, {"value": "entityName-input", "text": "name"}, {"value": "true", "text": "aria-required"}, {"value": "false", "text": "aria-invalid"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457359165, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@data-e2e-id='entityName-input']", "attribute": "value", "value": "create", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457359165.jpg"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='entityLabels::create']", "targetCSS": "mat-chip-row#mat-mdc-chip-3.mat-mdc-chip.mat-mdc-chip-row.mdc-evolution-chip.mat-primary.mdc-evolution-chip--with-trailing-action.mat-mdc-standard-chip.mat-mdc-chip-with-trailing-icon.ng-star-inserted.cdk-focused.cdk-mouse-focused span.mdc-evolution-chip__cell.mdc-evolution-chip__cell--primary span.mdc-evolution-chip__action.mat-mdc-chip-action.mdc-evolution-chip__action--primary span.mdc-evolution-chip__text-label.mat-mdc-chip-action-label.e2e-mark-element", "value": {"value": "create", "tagName": "span", "attributes": [{"value": "mdc-evolution-chip__text-label mat-mdc-chip-action-label e2e-mark-element", "text": "class"}, {"value": "create", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457361766, "execute": [{"command": "assert-xpath", "target": "//mat-chip-row[@data-e2e-id='entityLabels::create']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457361766.jpg"}, {"command": "assert-element", "target": "//input[@id='entityNotesEnabled-input']", "targetCSS": "input#entityNotesEnabled-input.mdc-checkbox__native-control.mdc-checkbox--selected", "value": {"value": "true", "tagName": "input.checkbox", "attributes": [{"value": "true", "text": "value"}, {"value": "checkbox", "text": "type"}, {"value": "mdc-checkbox__native-control mdc-checkbox--selected", "text": "class"}, {"value": "entityNotesEnabled-input", "text": "id"}, {"value": "0", "text": "tabindex"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457365839, "execute": [{"command": "assert-attribute:value", "xPath": "//input[@id='entityNotesEnabled-input']", "attribute": "value", "value": "true", "special": "assert-angular-checkbox"}], "special": "assert-angular-checkbox", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457365839.jpg"}, {"command": "assert-element", "target": "//textarea[@data-e2e-id='entityNotes-input']", "targetCSS": "textarea#mat-input-9.mat-mdc-input-element.ng-tns-c22-51.mat-mdc-form-field-textarea-control.mat-mdc-form-field-input-control.mdc-text-field__input.ng-untouched.ng-pristine.cdk-text-field-autofill-monitored.ng-valid.e2e-mark-element", "value": {"value": "has not", "tagName": "textarea", "attributes": [{"value": "notes", "text": "value"}, {"value": "", "text": "matinput"}, {"value": "mat-mdc-input-element ng-tns-c22-51 mat-mdc-form-field-textarea-control mat-mdc-form-field-input-control mdc-text-field__input ng-untouched ng-pristine cdk-text-field-autofill-monitored ng-valid e2e-mark-element", "text": "class"}, {"value": "100", "text": "cols"}, {"value": "8", "text": "rows"}, {"value": "entityNotes-input", "text": "data-e2e-id"}, {"value": "mat-input-9", "text": "id"}, {"value": "false", "text": "aria-invalid"}, {"value": "false", "text": "aria-required"}, {"value": "", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457368367, "execute": [{"command": "assert-attribute:value", "xPath": "//textarea[@data-e2e-id='entityNotes-input']", "attribute": "value", "value": "notes", "special": "assert-angular-text-field-value"}], "special": "assert-angular-text-field-value", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457368367.jpg"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='selectedTaggedWithMS::has']", "targetCSS": "mat-chip-row#mat-mdc-chip-4.mat-mdc-chip.mat-mdc-chip-row.mdc-evolution-chip.mat-primary.chip-success.mdc-evolution-chip--with-trailing-action.mat-mdc-standard-chip.mat-mdc-chip-with-trailing-icon.ng-star-inserted.cdk-focused.cdk-mouse-focused span.mdc-evolution-chip__cell.mdc-evolution-chip__cell--primary span.mdc-evolution-chip__action.mat-mdc-chip-action.mdc-evolution-chip__action--primary span.mdc-evolution-chip__text-label.mat-mdc-chip-action-label.e2e-mark-element", "value": {"value": "has", "tagName": "span", "attributes": [{"value": "mdc-evolution-chip__text-label mat-mdc-chip-action-label e2e-mark-element", "text": "class"}, {"value": "has", "text": "innerText"}], "selectOptions": []}, "timestamp": 1688457371562, "execute": [{"command": "assert-xpath", "target": "//mat-chip-row[@data-e2e-id='selectedTaggedWithMS::has']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/1688457371562.jpg"}, {"command": "assert-element", "target": "//mat-chip-row[@data-e2e-id='selectedNotTaggedWithMS::has-not']", "targetCSS": "mat-chip-row#mat-mdc-chip-5.mat-mdc-chip.mat-mdc-chip-row.mdc-evolution-chip.mat-primary.chip-error.mdc-evolution-chip--with-trailing-action.mat-mdc-standard-chip.mat-mdc-chip-with-trailing-icon.ng-star-inserted.cdk-focused.cdk-mouse-focused span.mdc-evolution-chip__cell.mdc-evolution-chip__cell--primary span.mdc-evolution-chip__action.mat-mdc-chip-action.mdc-evolution-chip__action--primary span.mdc-evolution-chip__text-label.mat-mdc-chip-action-label.e2e-mark-element", "value": {"value": "has not", "tagName": "span", "attributes": [{"value": "mdc-evolution-chip__text-label mat-mdc-chip-action-label e2e-mark-element", "text": "class"}, {"value": "has not", "text": "innerText"}], "selectOptions": []}, "timestamp": *************, "execute": [{"command": "assert-xpath", "target": "//mat-chip-row[@data-e2e-id='selectedNotTaggedWithMS::has-not']", "targetCss": "", "value": "", "special": ""}], "special": "", "somewhereReason": "", "screenshotUrl": "https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/*************/*************.jpg"}], "datasets": {"url": "https://app.ktlocal.com/", "testCaseDependency": 0, "testCaseDataCreation": [], "roleNames": ["Enterprise"], "userGroupId": "43", "tierInAccount": "10000"}, "machineName": "test_campaigns__newsletter__create_rich_email_label_notes_tag_conditions_*************", "repoPath": "campaigns/newsletter", "repoReady": true, "pipelineReady": true, "zephyrTest": "T377", "zephyrTestUrl": "https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-T377"}
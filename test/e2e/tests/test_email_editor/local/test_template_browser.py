# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""Email Editor template browser tests."""

import os
import pytest
import time
from flaky import flaky

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import email_helper as eh
from tests.helpers import bee_editor_helper as beeh
from tests.helpers import image_compare_helper as ih
from tests.helpers.image_compare_helper import ScreenMask

from tests.helpers import test_data as td


@pytest.fixture(scope='module', autouse=False)
def login_and_save_session_cookie(get_webdriver_for_setting_session_cookie, base_url, standard_e2e_test_dev_user,
                                  standard_e2e_test_dev_pwd):
    """Just overwrite for test data setup module."""
    pass


@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "app-e2e.ktlocal.com"
    local_data = kh.sh.TestData()
    local_data._sut_domain = sut_domain
    test_data_pool[sut_domain] = local_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._temp_mail_name = "e2e-tests: temp mail for template test"
    test_data._existing_mail = "some automation mail"


@flaky(max_runs=1)
@pytest.mark.order1
def test_new_mail_auto_open_import_apply_template(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Test template browser."""
    # Delete mail with name 'e2e-tests: temp mail for template test'
    td.import_testdata_sql("ready_for_test/new_emaileditor.sql")
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)
    kh.click_menu_item("Campaigns", "E-Mails / SMS")
    eh.delete_mail_by_name(test_data._temp_mail_name)

    # Create mail with name 'e2e-tests: temp mail for template test'
    kh.click_menu_item("Campaigns", "E-Mails / SMS", "Create Campaign E-Mail")
    eh.create_mail(test_data._temp_mail_name, test_data._temp_mail_name, 'bee')
    eh.campaign_mail_save_and_edit()

    # <kt-landing-page-template _ngcontent-ng-c3046062376="" class="kt-template ng-star-inserted" _nghost-ng-c1761088129="">
    # <div _ngcontent-ng-c1761088129="" class="kt-tile-border" data-e2e-id="template-overview-tileundefined"><img _ngcontent-ng-c1761088129="" alt="template thumbnail" class="kt-tile-img" src="https://d1oco4z2z1fhwp.cloudfront.net/templates/default/3181.jpg"><div _ngcontent-ng-c1761088129="" class="kt-cover"><button _ngcontent-ng-c1761088129="" class="kt-btn-details button-secondary button-secondary--inverted"> Details </button><button _ngcontent-ng-c1761088129="" class="kt-btn-apply button-primary button-primary--inverted"> Apply </button></div></div><div _ngcontent-ng-c1761088129="" class="kt-usage-label" title="Happy April Fool's Day Sale"> Happy April Fool's Day Sale
    # </div></kt-landing-page-template>

    # Hover over template, so details/anwenden button get visible
    beeh.templ_browser_find_and_apply_templ("Book Lovers")
    beeh.confirm_clear_all_modal_dialog_by('Yes')

    time.sleep(6)  # Wait for template loaded
    # replace screenshot for loaded template
    ih.screen_test("email_template_browser",
                   "create_mail_apply_templ", {"1366x2000": [ScreenMask.top_main_menu,
                                                             ScreenMask.left_feedback_area,
                                                             ScreenMask.bottom_feedback_area,
                                                             ScreenMask.bee_top_menu,
                                                             ScreenMask.bee_sidebar,
                                                             ScreenMask.bee_bottom_scrollbar],
                                               "1366x768": [(168, 85, 1186, 709)],
                                               "2732x2048": [(339, 283, 2424, 2047)]})


@flaky(max_runs=1)
@pytest.mark.order2
def test_existing_mail_import_and_clear_all(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Test template browser."""
    # Open mail with name 'e2e-tests: mail for template test'
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)
    kh.click_menu_item("Campaigns", "E-Mails / SMS")
    eh.open_mail_via_overview(test_data._temp_mail_name)
    beeh.open_bee_editor_via_sidebar_in_mail()

    beeh.switch_back_to_default_frame()
    # clear all
    beeh.clear_all_and_confirm_by('Yes')

    time.sleep(12)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "delete_all_content", {"1366x2000": [ScreenMask.top_main_menu,
                                                        ScreenMask.left_feedback_area,
                                                        ScreenMask.bottom_feedback_area,
                                                        ScreenMask.bee_top_menu,
                                                        ScreenMask.bee_sidebar,
                                                        ScreenMask.bee_bottom_scrollbar],
                                          "1366x768": [(168, 85, 1186, 709)],
                                          "2732x2048": [(339, 283, 2424, 2047)]})

    # Import via template browser
    beeh.open_templ_browser()

    kh.sh.set_wait_timeout(3 * 60)
    beeh.templ_browser_find_and_apply_templ("Book Lovers")
    kh.sh.reset_wait_timeout()

    # re-select template with above steps and click 'Yes' and overwrite the content
    beeh.confirm_clear_all_modal_dialog_by('Yes')

    time.sleep(12)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "existing_mail_templ_loaded", {"1366x2000": [ScreenMask.top_main_menu,
                                                                ScreenMask.left_feedback_area,
                                                                ScreenMask.bottom_feedback_area,
                                                                ScreenMask.bee_top_menu,
                                                                ScreenMask.bee_sidebar,
                                                                ScreenMask.bee_bottom_scrollbar],
                                                  "1366x768": [(168, 85, 1186, 709)],
                                                  "2732x2048": [(339, 283, 2424, 2047)]})

    # Select 'No' in modal dialog
    beeh.switch_back_to_default_frame()
    beeh.clear_all_and_confirm_by('No')

    time.sleep(12)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "mail_not_deleted_after_clearall_denied", {"1366x2000": [ScreenMask.top_main_menu,
                                                                            ScreenMask.left_feedback_area,
                                                                            ScreenMask.bottom_feedback_area,
                                                                            ScreenMask.bee_top_menu,
                                                                            ScreenMask.bee_sidebar,
                                                                            ScreenMask.bee_bottom_scrollbar],
                                                              "1366x768": [(168, 85, 1186, 709)],
                                                              "2732x2048": [(339, 283, 2424, 2047)]})


@flaky(max_runs=1)
@pytest.mark.order3
def test_details_page(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Test template browser."""
    td.import_testdata_sql("ready_for_test/new_emaileditor.sql")
    # Open mail with name 'e2e-tests: mail for template test'
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)
    kh.click_menu_item("Campaigns", "E-Mails / SMS")
    eh.open_mail_via_overview(test_data._existing_mail)
    beeh.open_bee_editor_via_sidebar_in_mail()

    # Import via template browser
    beeh.switch_back_to_default_frame()
    beeh.open_templ_browser()
    time.sleep(6)  # Wait for template loaded

    # Hover over template, so details/anwenden button get visible
    # click on 'details'
    beeh.templ_browser_open_templ_details("Book Lovers")

    time.sleep(15)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "template_details_page", {"1366x2000": [ScreenMask.top_main_menu,
                                                           ScreenMask.left_feedback_area,
                                                           ScreenMask.bottom_feedback_area,
                                                           ScreenMask.bee_top_menu,
                                                           ScreenMask.bee_sidebar,
                                                           ScreenMask.bee_templbrowser_mask_above_details,
                                                           ScreenMask.bee_templbrowser_mask_below_details,
                                                           ScreenMask.bee_templbrowser_mask_rightof_details,
                                                           ScreenMask.bee_templbrowser_mask_leftof_details,
                                                           ScreenMask.bee_bottom_scrollbar],
                                             "1366x768": [(168, 85, 1186, 709)],
                                             "2732x2048": [(339, 283, 2424, 2047)]})


@flaky(max_runs=1)
@pytest.mark.order4
def test_filter(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Test template browser."""
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)
    kh.click_menu_item("Campaigns", "E-Mails / SMS")
    eh.open_mail_via_overview(test_data._existing_mail)
    beeh.open_bee_editor_via_sidebar_in_mail()

    # Import via template browser
    beeh.switch_back_to_default_frame()
    beeh.open_templ_browser()
    time.sleep(10)

    # click in filter lineedit
    beeh.open_tags_list()

    # click option 'rot'
    beeh.select_tag_from_open_tag_list('red')

    # audy template exists
    assert beeh.test_templ_is_matching_filter('Share The Love'), "Share The Love template not matched by filter!"

    # ecommerce-bag template exists
    assert beeh.test_templ_is_matching_filter('Freedom Day'), "Freedom Day template not matched by filter!"

    time.sleep(12)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "rot_tag_set", {"1366x2000": [ScreenMask.top_main_menu,
                                                 ScreenMask.left_feedback_area,
                                                 ScreenMask.bottom_feedback_area,
                                                 ScreenMask.bee_top_menu,
                                                 ScreenMask.bee_sidebar,
                                                 ScreenMask.bee_templbrowser_editbackground_left,
                                                 ScreenMask.bee_templbrowser_editbackground_bottom,
                                                 ScreenMask.bee_bottom_scrollbar],
                                   "1366x768": [(168, 85, 1186, 709)],
                                   "2732x2048": [(339, 283, 2424, 2047)]})

    # click in filter lineedit
    beeh.open_tags_list()

    time.sleep(5)
    # click option 'animated'
    beeh.select_tag_from_open_tag_list('animated')
    time.sleep(5)

    # Test the following have disappeared
    # audy template not matched filter
    assert False is beeh.test_templ_is_matching_filter('Share The Love'), "should not be matched by 'animiert' tag!"

    # ecommerce-bag template not matching
    assert False is beeh.test_templ_is_matching_filter('Freedom Day'), "should not be matched by 'animiert' tag!"

    # open "Branche" drop down
    beeh.open_branche_dropdown_filter_list()

    # Click 'Transportation & Storage' in Branche dropdown
    beeh.select_branche_tag_from_open_list('Transportation & Storage')

    # Filter more by "Verwendung" drop-down
    beeh.open_verwendung_dropdown_filter_list()

    beeh.select_verwendung_tag_from_open_list('Notification')

    time.sleep(12)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "after_filter_1", {"1366x2000": [ScreenMask.top_main_menu,
                                                    ScreenMask.left_feedback_area,
                                                    ScreenMask.bottom_feedback_area,
                                                    ScreenMask.bee_top_menu,
                                                    ScreenMask.bee_sidebar,
                                                    ScreenMask.bee_templbrowser_editbackground_left,
                                                    ScreenMask.bee_templbrowser_editbackground_bottom,
                                                    ScreenMask.bee_bottom_scrollbar],
                                      "1366x768": [(168, 85, 1186, 709)],
                                      "2732x2048": [(339, 283, 2424, 2047)]})

    list_of_new_tags = ["light", "white", "light blue", "notify", "two-columns"]
    for curr_tag in list_of_new_tags:
        # click in filter lineedit
        beeh.open_tags_list()

        # click option 'rot'
        beeh.select_tag_from_open_tag_list(curr_tag)

    beeh.open_tags_list()

    time.sleep(12)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "empty_tag_list", {"1366x2000": [ScreenMask.top_main_menu,
                                                    ScreenMask.left_feedback_area,
                                                    ScreenMask.bottom_feedback_area,
                                                    ScreenMask.bee_top_menu,
                                                    ScreenMask.bee_sidebar,
                                                    ScreenMask.bee_templbrowser_editbackground_left,
                                                    ScreenMask.bee_templbrowser_editbackground_bottom,
                                                    ScreenMask.bee_bottom_scrollbar],
                                      "1366x768": [(168, 85, 1186, 709)],
                                      "2732x2048": [(339, 283, 2424, 2047)]})

    for curr_tag in list_of_new_tags:
        beeh.delete_tag(curr_tag)

    # open "Branche" drop down
    beeh.open_branche_dropdown_filter_list()

    time.sleep(12)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "branche_drop_down", {"1366x2000": [ScreenMask.top_main_menu,
                                                       ScreenMask.left_feedback_area,
                                                       ScreenMask.bottom_feedback_area,
                                                       ScreenMask.bee_top_menu,
                                                       ScreenMask.bee_sidebar,
                                                       ScreenMask.bee_templbrowser_editbackground_left,
                                                       ScreenMask.bee_templbrowser_editbackground_bottom,
                                                       ScreenMask.bee_bottom_scrollbar],
                                         "1366x768": [(168, 85, 1186, 709)],
                                         "2732x2048": [(339, 283, 2424, 2047)]})

    # close branche dropdown - its hidden/blocked
    transparent_backdrop_overlay = kh.sh.pass_xpath_exists("Get blocking overlay",
                                                           "//div[contains(@class,'cdk-overlay-transparent-backdrop')]")
    transparent_backdrop_overlay = transparent_backdrop_overlay[1] if type(
        transparent_backdrop_overlay) is list else transparent_backdrop_overlay
    pytest.selenium_driver.execute_script("arguments[0].style.visibility = 'hidden'", transparent_backdrop_overlay)
    beeh.open_branche_dropdown_filter_list()  # closes the dropdown

    # open "Verwendung" drop down
    beeh.open_verwendung_dropdown_filter_list()

    time.sleep(12)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "verwendung_drop_down", {"1366x2000": [ScreenMask.top_main_menu,
                                                          ScreenMask.left_feedback_area,
                                                          ScreenMask.bottom_feedback_area,
                                                          ScreenMask.bee_top_menu,
                                                          ScreenMask.bee_sidebar,
                                                          ScreenMask.bee_templbrowser_editbackground_left,
                                                          ScreenMask.bee_templbrowser_editbackground_bottom,
                                                          ScreenMask.bee_bottom_scrollbar],
                                            "1366x768": [(168, 85, 1186, 709)],
                                            "2732x2048": [(339, 283, 2424, 2047)]})


@flaky(max_runs=1)
@pytest.mark.order5
def test_eigene_emails(base_url, standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Test template browser."""
    kh.open_kt_and_login(base_url, account=standard_e2e_test_dev_user, pwd=standard_e2e_test_dev_pwd)
    kh.click_menu_item("Campaigns", "E-Mails / SMS")
    eh.open_mail_via_overview(test_data._temp_mail_name)
    beeh.open_bee_editor_via_sidebar_in_mail()

    # Import via template browser
    beeh.switch_back_to_default_frame()
    beeh.open_templ_browser()

    time.sleep(15)

    # Open 'Eigene E-Mails' tab
    beeh.open_own_mails_tab()

    # Sometimes the table with own mails needs to long.
    time.sleep(3)
    # Test that there is more than one row (header is the first)
    assert beeh.get_nr_of_own_emails() > 0, "There shall exist at minimum 1 or more test mails. Something is wrong."

    # Import one of the Mails 'new bee email'
    beeh.find_own_mail_by_name(test_data._existing_mail)
    beeh.import_own_mail_by_name(test_data._existing_mail)
    beeh.confirm_clear_all_modal_dialog_by('Yes')

    time.sleep(12)  # Wait for template loaded
    ih.screen_test("email_template_browser",
                   "own_mail_as_templ_loaded", {"1366x2000": [ScreenMask.top_main_menu,
                                                              ScreenMask.left_feedback_area,
                                                              ScreenMask.bottom_feedback_area,
                                                              ScreenMask.bee_top_menu,
                                                              ScreenMask.bee_sidebar,
                                                              ScreenMask.bee_bottom_scrollbar],
                                                "1366x768": [(168, 85, 1186, 709)],
                                                "2732x2048": [(339, 283, 2424, 2047)]})


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

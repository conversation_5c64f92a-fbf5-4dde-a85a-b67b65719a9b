# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2025, Klick-Tipp
# Email: <EMAIL>
"""Selenium based tests for end-2-end."""

import pytest
import logging
import os

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import test_data as td

PYTEST_LOGGER = logging.getLogger('pytest_logger')


# Overwrite fixture defined in fixtures/login_and_sessions.py
# We dont have any ready database with valid data.
@pytest.fixture(scope='module', autouse=False)
def login_and_save_session_cookie(get_webdriver_for_setting_session_cookie, base_url, standard_e2e_test_dev_user,
                                  standard_e2e_test_dev_pwd):
    """Just overwrite for test data setup module."""
    pass


@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "app-e2e.ktlocal.com"
    local_data = kh.sh.TestData()
    local_data._sut_domain = sut_domain

    test_data_pool[sut_domain] = local_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._standard_data_e2etestdev_created = "dev_installation_with_e2etestdev.sql"


@pytest.mark.order1
def test_create_e2e_test_dev_user(base_url, standard_local_kt_admin, standard_local_kt_admin_pwd,
                                  standard_e2e_test_dev_user, standard_e2e_test_dev_pwd):
    """Create standard test user 'e2e-test-dev'"""
    # kh.open_kt_and_login(base_url, account=standard_local_kt_admin, pwd=standard_local_kt_admin_pwd)
    #
    # pytest.selenium_driver.get(base_url + "/admin/people/create")
    #
    # kh.sh.send_string_to_input("//input[@id='edit-name']", standard_e2e_test_dev_user)
    # kh.sh.send_string_to_input("//input[@id='edit-mail']", "<EMAIL>")
    # kh.sh.send_string_to_input("//input[@id='edit-pass-pass1']", standard_e2e_test_dev_pwd)
    # kh.sh.send_string_to_input("//input[@id='edit-pass-pass2']", standard_e2e_test_dev_pwd)
    # kh.sh.click_by_xpath("Enable 'Enterprise' checkbox", "//label[text()='Enterprise ']/../input")
    # kh.sh.click_by_xpath("Submit new e2e-test-dev user", "//input[@id='edit-submit']")

    # We add the user now via the klicktipp_app.install script
    # So no need to add the user by click actions anymore
    pass


@pytest.mark.order2
def test_add_e2etestdev_to_cockpit_group():
    """Create standard test user 'e2e-test-dev'"""
    # pytest.selenium_driver.get("http://phpmyadmin")
    #
    # kh.sh.click_by_xpath("Select klicktipp DB", "//a[text()='klicktipp']")
    # console_div = kh.sh.pass_xpath_exists("Get console div", "//div[@id='pma_console']")
    # pytest.selenium_driver.execute_script("arguments[0].parentNode.removeChild(arguments[0])", console_div)
    # kh.sh.click_by_xpath("Select drupal_users table", "//a[contains(text(),'drupal_users')]")
    # import pdb; pdb.set_trace()
    # kh.sh.click_by_xpath("Click on search", "//img[@title='Search']/../../a")
    # kh.sh.click_by_xpath("Search by name and compare with =", "//th[text()='name']/..//select//option[@value='=']")
    # kh.sh.send_string_to_input("//th[text()='name']/..//input[@type='text']", "e2e-test-dev")
    # kh.sh.press_key_return()
    # kh.sh.click_by_xpath("Edit e2e-test-dev record",
    #                      "//span[text()='e2e-test-dev']/../..//span[contains(text(),'Edit')]/../../a")
    # kh.sh.send_string_to_input("//span[text()='RelUserGroupID']/../..//input[@value=0 and not(@type='hidden')]", "42",
    #                            clear=True)  # Group ID to get permission for cockpit
    # kh.sh.click_by_xpath("Press 'Go' to save record changes.", "//input[@id='buttonYes']")

    # We add the user now via the klicktipp_app.install script
    # So no phpadmin actions needed anymore

    td.call_process_in_kt_container("Clear cache after update", "drush cache-clear all")
    td.call_process_in_kt_container("Export db as : " + test_data._standard_data_e2etestdev_created,
                                    "drush --verbose -v --db-url=mysql://root:klicktipp@percona/klicktipp sql-dump --result-file=test/e2e/tests/data/" + test_data._standard_data_e2etestdev_created)


__author__ = "Simon Werling"
__copyright__ = "Copyright 2025, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

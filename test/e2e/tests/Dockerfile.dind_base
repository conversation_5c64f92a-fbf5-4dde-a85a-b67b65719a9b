FROM python:3.13.2-slim-bookworm

LABEL name="Base DinD image for end-2-end selenium test script executor" Version="1.0.0"

RUN apt-get update
RUN apt-get -y install \
    curl gosu

RUN curl -fsSL https://get.docker.com -o get-docker.sh
RUN sh get-docker.sh

RUN /usr/local/bin/python -m pip install --upgrade pip

WORKDIR /
COPY ./requirements-dind.txt /

RUN pip3 install -r requirements-dind.txt

RUN pip3 freeze

CMD pip3 freeze

# -*- coding: utf-8 -*-
# Author: <PERSON><PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
DKIM : Create a dkim domain (use exising domain with valid DNS key).
Make sure, it's verified. Delete domain.
"""

import fnmatch
import pytest
import logging
from flaky import flaky
from tests.helpers.dkim_helper import check_email_headers, send_test_mail
from tests.helpers import subscriber_area_helper as sah

PYTEST_LOGGER = logging.getLogger('pytest_logger')


@pytest.mark.tm4j(publish_for='DEV-T324')
@flaky(max_runs=1)
@pytest.mark.order1
def test_email_headers_of_user_without_any_domain(base_url, standard_shared_nodkim_user,
                                                  standard_shared_nodkim_user_pwd, standard_subscriber,
                                                  standard_subscriber_pwd):
    domain = 'staging-beta.com'
    sender_domain = 'infos.' + domain
    subject = 'Header - Shared - alter Fall - kein <PERSON><PERSON>IM - kein Freeimailer'
    subscriber_mail_address = '<EMAIL>'

    send_test_mail(base_url, standard_shared_nodkim_user, user_pwd=standard_shared_nodkim_user_pwd,
                   subscriber_mail_address=subscriber_mail_address, subject=subject)

    headers = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                  mail_address=subscriber_mail_address,
                                                  subject=subject)

    PYTEST_LOGGER.info('All headers: ' + str(headers))

    check_email_headers(headers,
                        from_mail_address='<EMAIL>',
                        return_path_domain='infos.' + domain,
                        expected_sender='postmaster@infos.' + domain,
                        dkim_selector='ktdkim',
                        dkim_domain=sender_domain)

    mail_deleted = sah.delete_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                   mail_address=subscriber_mail_address, subject=subject)
    assert mail_deleted, 'could not delete e-mail'


@pytest.mark.tm4j(publish_for='DEV-T325')
@flaky(max_runs=1)
@pytest.mark.order2
def test_freemailer_email_headers_of_user_without_any_domain(base_url, standard_shared_nodkim_user,
                                                             standard_shared_nodkim_user_pwd, standard_subscriber,
                                                             standard_subscriber_pwd):
    domain = 'staging-beta.com'
    subject = 'Header - Shared - alter Fall - kein DKIM - Freeimailer'
    subscriber_mail_address='<EMAIL>'

    send_test_mail(base_url, standard_shared_nodkim_user, user_pwd=standard_shared_nodkim_user_pwd,
                   subscriber_mail_address=subscriber_mail_address, subject=subject)

    headers = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                  mail_address=subscriber_mail_address,
                                                  subject=subject)

    PYTEST_LOGGER.info('All headers: ' + str(headers))

    check_email_headers(headers,
                        from_mail_address='randsomrandy19+e2e-test-shared-nodkim-gmail.com@' + domain,
                        return_path_domain='infos.' + domain,
                        expected_sender=None,
                        dkim_selector='ktdkim',
                        dkim_domain=domain)

    mail_deleted = sah.delete_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                   mail_address=subscriber_mail_address, subject=subject)
    assert mail_deleted, 'could not delete e-mail'

# -*- coding: utf-8 -*-
# Author: <PERSON><PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
DKIM : Create a dkim domain (use exising domain with valid DNS key).
Make sure, it's verified. Delete domain.
"""

import time
import logging
import pytest
from flaky import flaky
from tests.helpers import klick_tipp_helper as kh
from tests.helpers.dkim_helper import check_email_headers, send_test_mail
from tests.helpers import subscriber_area_helper as sah

PYTEST_LOGGER = logging.getLogger('pytest_logger')


@pytest.fixture(scope='module', autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data = kh.sh.TestData()
    test_data._domain = 'staging-theta.com'


@pytest.mark.tm4j(publish_for='DEV-T324')
@flaky(max_runs=1)
@pytest.mark.order1
def test_email_headers_of_user_with_new_shared_domains(base_url, standard_shared_dkim_user,
                                                       standard_shared_dkim_user_pwd, standard_subscriber,
                                                       standard_subscriber_pwd):
    domain = 'staging-beta.click'
    user_domain = 'www3411332.' + domain
    subject = 'Header - Shared - New Domains - No DKIM'
    subscriber_mail_address = '<EMAIL>'

    send_test_mail(base_url, standard_shared_dkim_user, user_pwd=standard_shared_dkim_user_pwd,
                   subscriber_mail_address=subscriber_mail_address, subject=subject)

    headers = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                  mail_address=subscriber_mail_address,
                                                  subject=subject)

    PYTEST_LOGGER.info('All headers: ' + str(headers))

    check_email_headers(
        headers,
        from_mail_address='nodkim-new-zauberlist.com@' + user_domain,
        return_path_domain='infos.' + domain,
        expected_sender=None,
        dkim_selector='ktdkim2',
        dkim_domain=user_domain)

    mail_deleted = sah.delete_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                   mail_address=subscriber_mail_address, subject=subject)
    assert mail_deleted, 'could not delete e-mail'



@pytest.mark.tm4j(publish_for='DEV-T326')
@flaky(max_runs=1)
@pytest.mark.order2
def test_create_and_verify_dkim_domain(base_url, standard_shared_dkim_user, standard_shared_dkim_user_pwd):
    domain = test_data._domain
    kh.open_kt_and_login(base_url, account=standard_shared_dkim_user, pwd=standard_shared_dkim_user_pwd)
    kh.click_menu_item("Mein Konto", "Einstellungen", "Domains")

    domain_exists = kh.sh.test_xpath_exists('Check, if domain exists already',
                                            '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]')
    if domain_exists:
        delete_dkim_domain(domain)
    kh.sh.send_string_to_input("//input[@id='edit-newwhitelabeldomain']", domain)
    kh.sh.click_by_xpath('Create domain ' + domain, '//input[@id="edit-addbutton"]')

    kh.sh.pass_xpath_exists('Success message appears', '//div[contains(@class, "alert-success")]')
    kh.close_success_alert()

    kh.sh.pass_xpath_exists(
      'Domain is supposed to be verified from a point in future (3 seconds)',
      '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/../td[contains(text(), "verifiziert (verfügbar ab")]'
    )

    time.sleep(4)

    kh.sh.click_by_xpath(
      'Reload Domain ' + domain,
      '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/..//a[text()="Prüfen"]'
    )

    kh.sh.pass_xpath_exists('Domain is supposed to be verified',
                            '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/../td[text()="verifiziert"]')


@pytest.mark.tm4j(publish_for='DEV-T324')
@flaky(max_runs=1)
@pytest.mark.order3
@pytest.mark.incremental
def test_email_headers_of_user_with_dkim_domain(base_url, standard_shared_dkim_user,
                                                standard_shared_dkim_user_pwd, standard_subscriber,
                                                standard_subscriber_pwd, standard_shared_dkim_user_subdomain):
    dkim_domain = 'staging-theta.com'
    subject='Header - Shared - DKIM'
    subscriber_mail_address='<EMAIL>'

    send_test_mail(base_url, standard_shared_dkim_user, user_pwd=standard_shared_dkim_user_pwd,
                   subscriber_mail_address=subscriber_mail_address, subject=subject)

    headers = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                  mail_address=subscriber_mail_address,
                                                  subject=subject)

    PYTEST_LOGGER.info('All headers: ' + str(headers))

    check_email_headers(
        headers,
        from_mail_address='dkim@' + dkim_domain,
        return_path_domain=standard_shared_dkim_user_subdomain + '.' + dkim_domain,
        expected_sender=None,
        dkim_selector='ktdkim2',
        dkim_domain=dkim_domain)

    mail_deleted = sah.delete_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                   mail_address=subscriber_mail_address, subject=subject)
    assert mail_deleted, 'could not delete e-mail'


@pytest.mark.tm4j
@flaky(max_runs=1)
@pytest.mark.incremental
@pytest.mark.order4
def test_delete_dkim_domain(base_url, standard_shared_dkim_user, standard_shared_dkim_user_pwd):
    # Login with shared dkom account and go to domains
    kh.open_kt_and_login(base_url, account=standard_shared_dkim_user, pwd=standard_shared_dkim_user_pwd)
    kh.click_menu_item("Mein Konto", "Einstellungen", "Domains")

    # kh.sh.set_wait_timeout(2)
    delete_dkim_domain(test_data._domain)


def delete_dkim_domain(domain):
    kh.sh.click_by_xpath('Delete Domain ' + domain,
                         '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/..//a[text()="Löschen"]')

    dotless_domain = domain.replace('.', '_')
    kh.sh.click_by_xpath(
        'Confirm delete dialog for domain ' + domain,
        '//a[@id="klicktipp_domain_delete_confirm_form_' + dotless_domain + '_submit"]'
    )

    kh.sh.pass_xpath_exists('Success message appears', '//div[contains(@class, "alert-success")]')
    kh.close_success_alert()

    domain_exists = kh.sh.test_xpath_exists('Check, if domain exists already', '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]')
    assert domain_exists is False, 'Domain ' + domain + ' does not appear in list anymore'

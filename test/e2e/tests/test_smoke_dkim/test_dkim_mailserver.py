# -*- coding: utf-8 -*-
# Author: <PERSON><PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: n.gie<PERSON><EMAIL>
"""
DKIM:
- Transform an existing dkim  domain into a mailserver
- remove mailserver functionality (transform domain into a dkim one again)
"""

import time
import pytest
import logging
from flaky import flaky
from tests.helpers.dkim_helper import check_email_headers, send_test_mail
from tests.helpers import klick_tipp_helper as kh
from tests.helpers import adminarea_helper as aah

from tests.helpers import subscriber_area_helper as sah

PYTEST_LOGGER = logging.getLogger('pytest_logger')


@pytest.fixture(scope='module', autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data = kh.sh.TestData()
    test_data._domain = 'staging-alpha.com'
    test_data._hostname = 'mx1'
    test_data._hostip = '*************'


@pytest.mark.tm4j(publish_for='DEV-T327')
@flaky(max_runs=1)
@pytest.mark.order1
def test_nodkim_email_headers_of_whitelabel_users(base_url, standard_enterprise_dkim_user,
                                                  standard_enterprise_dkim_user_pwd, standard_subscriber,
                                                  standard_subscriber_pwd):
    domain = 'staging-delta.com'
    subject='Header - Whitelabel - kein DKIM - kein Freeimailer'
    subscriber_mail_address='<EMAIL>'

    send_test_mail(base_url, standard_enterprise_dkim_user, user_pwd=standard_enterprise_dkim_user_pwd,
                   subscriber_mail_address=subscriber_mail_address, subject=subject)

    headers = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                  mail_address=subscriber_mail_address,
                                                  subject=subject)

    PYTEST_LOGGER.info('All headers: ' + str(headers))

    check_email_headers(headers,
                        from_mail_address='nodkim-wl-zauberlist.com@' + domain,
                        return_path_domain='infos.' + domain,
                        expected_sender=None,
                        dkim_selector='ktdkim2',
                        dkim_domain=domain)

    mail_deleted = sah.delete_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                   mail_address=subscriber_mail_address, subject=subject)
    assert mail_deleted, 'could not delete e-mail'


@pytest.mark.tm4j(publish_for='DEV-T327')
@flaky(max_runs=1)
@pytest.mark.order2
def test_dkim_email_headers_of_whitelabel_users(base_url, standard_enterprise_dkim_user,
                                                standard_enterprise_dkim_user_pwd, standard_subscriber,
                                                standard_subscriber_pwd):
    dkim_domain = 'staging-zeta.com'
    subject='Header - Whitelabel - DKIM'
    subscriber_mail_address='<EMAIL>'

    send_test_mail(base_url, standard_enterprise_dkim_user, user_pwd=standard_enterprise_dkim_user_pwd,
                   subscriber_mail_address=subscriber_mail_address, subject=subject)

    headers = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                  mail_address=subscriber_mail_address,
                                                  subject=subject)

    PYTEST_LOGGER.info('All headers: ' + str(headers))

    check_email_headers(headers,
                        from_mail_address='test@' + dkim_domain,
                        return_path_domain='www3308.' + dkim_domain,
                        expected_sender=None,
                        dkim_selector='ktdkim2',
                        dkim_domain=dkim_domain)

    mail_deleted = sah.delete_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                   mail_address=subscriber_mail_address, subject=subject)
    assert mail_deleted, 'could not delete e-mail'


@pytest.mark.tm4j(publish_for='DEV-T327')
@flaky(max_runs=1)
@pytest.mark.order0
def test_entrerprise_email_headers_of_whitelabel_users(base_url, standard_enterprise_dkim_user,
                                                       standard_enterprise_dkim_user_pwd, standard_subscriber,
                                                       standard_subscriber_pwd):
    domain = 'staging-delta.com'
    subject='Header - Whitelabel - Enterprise Domain'
    subscriber_mail_address='<EMAIL>'

    send_test_mail(base_url, standard_enterprise_dkim_user, user_pwd=standard_enterprise_dkim_user_pwd,
                   subscriber_mail_address=subscriber_mail_address, subject=subject)

    headers = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                  mail_address=subscriber_mail_address,
                                                  subject=subject)

    PYTEST_LOGGER.info('All headers: ' + str(headers))

    check_email_headers(headers,
                        from_mail_address='test@' + domain,
                        return_path_domain='infos.' + domain,
                        expected_sender=None,
                        dkim_selector='ktdkim2',
                        dkim_domain=domain)

    mail_deleted = sah.delete_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                   mail_address=subscriber_mail_address, subject=subject)
    assert mail_deleted, 'could not delete e-mail'


@pytest.mark.tm4j(publish_for='DEV-T329')
@flaky(max_runs=1)
@pytest.mark.order4
def test_convert_dkim_domain_into_mailserver(base_url, standard_support_user,
                                             standard_support_user_pwd, standard_enterprise_dkim_user):
    domain = test_data._domain
    kh.open_kt_and_login(base_url, account=standard_support_user, pwd=standard_support_user_pwd)
    aah.open_customermenu_for(standard_enterprise_dkim_user)
    aah.open_customermenu_whitelabel()
    pytest.selenium_driver.switch_to.window(pytest.selenium_driver.window_handles[-1])

    status = kh.sh.pass_xpath_exists(
      'Check, if domain is verified and get exact status',
      '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/../td[contains(text(),"verifiziert")]'
    )

    # we need a dkim only domain so make sure we have one

    # mailserver is being set up
    if status.text == 'verifiziert, Mailserver wird eingerichtet':
        delete_hosts(domain)
    # mailserver is active
    elif status.text != 'verifiziert':
        delete_mailserver(domain)

    kh.sh.click_by_xpath(
      'Configure domain ' + domain,
      '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/..//a[text()="Mailserver konfigurieren"]'
    )

    mailserver_button_exists = kh.sh.test_xpath_exists(
      'Check if delete mailserver button is present',
      '//form[@id="klicktipp-account-whitelabel-domain-form"]//a[text()="Mailserver löschen"]'
    )

    assert mailserver_button_exists is False, 'since there is no mailserver, mailserver delete button is not present'

    kh.sh.click_by_xpath(
      'Open host dialog for domain ' + domain,
      # don't search for exacts string here (text()="xy"), icons seem to disturb selenium
      '//a[@id="edit-hosts-addbutton"]'
    )

    kh.sh.send_string_to_input("//input[@id='edit-hosts-hostname']", test_data._hostname)
    kh.sh.send_string_to_input("//input[@id='edit-hosts-ipaddress']", test_data._hostip)
    kh.sh.click_by_xpath('Add host to  ' + domain, '//input[@id="edit-hosts-addbutton"]')
    kh.sh.click_by_xpath('Go to domain configuration', '//a[@id="edit-cancel"]')
    kh.sh.click_by_xpath('Go to domain list', '//a[@id="edit-cancel"]')

    kh.sh.pass_xpath_exists(
      'Check, if domain is verified and mailserver is being set up',
      '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/../td[text()="verifiziert, Mailserver wird eingerichtet"]'
    )

    kh.sh.click_by_xpath(
      'Configure domain ' + domain,
      '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/..//a[text()="Mailserver konfigurieren"]'
    )
    kh.sh.click_by_xpath('Validate mailserver of domain ' + domain, '//input[@id="edit-validate"]')

    kh.sh.pass_xpath_exists('Success message appears', '//div[contains(@class, "alert-success")]')
    kh.close_success_alert()

    kh.sh.pass_xpath_exists(
      'Check, if domain is verified and mailserver is activated',
      '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/../td[text()="verifiziert, Mailserver aktiviert"]'
    )


@pytest.mark.tm4j(publish_for='DEV-T330')
@flaky(max_runs=1)
@pytest.mark.order5
def test_delete_mailserver(base_url, standard_support_user, standard_support_user_pwd, standard_enterprise_dkim_user):
    domain = test_data._domain
    kh.open_kt_and_login(base_url, account=standard_support_user, pwd=standard_support_user_pwd)
    aah.open_customermenu_for(standard_enterprise_dkim_user)
    aah.open_customermenu_whitelabel()
    pytest.selenium_driver.switch_to.window(pytest.selenium_driver.window_handles[-1])
    delete_mailserver(domain)


def delete_mailserver(domain):
    open_domain_configuration(domain)

    kh.sh.click_by_xpath('Delete mailserver of domain ' + domain,
                         '//form[@id="klicktipp-account-whitelabel-domain-form"]//a[text()="Mailserver löschen"]')

    kh.sh.pass_xpath_exists('Success message appears', '//div[contains(@class, "alert-success")]')
    kh.close_success_alert()

    kh.sh.pass_xpath_exists('Status of domain is just verified. Mailserver is not mentioned',
                            '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/../td[text()="verifiziert"]')


def delete_hosts(domain):
    open_domain_configuration(domain)

    hostdelete_link_xpath = '//form[@id="klicktipp-account-whitelabel-domain-form"]//a[contains(@href,"/hostdelete/")]'
    while kh.sh.test_xpath_exists('test if there is a host to delete', hostdelete_link_xpath):
        kh.sh.click_by_xpath(
            'Delete host of domain ' + domain,
            hostdelete_link_xpath
        )
        kh.sh.pass_xpath_exists('Success message appears', '//div[contains(@class, "alert-success")]')

        kh.close_success_alert()

    kh.sh.click_by_xpath('Go to domains list', '//a[@id="edit-cancel"]')


def open_domain_configuration(domain):
    kh.sh.click_by_xpath(
        'Configure domain ' + domain,
        '//form[@id="klicktipp-domains-settings-form"]//td[text()="' + domain + '"]/..//a[text()="Mailserver konfigurieren"]'
    )

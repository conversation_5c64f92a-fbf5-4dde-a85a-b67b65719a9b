# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
Test Bestätigungsmail and followup mail links after changing mailing links from klick-tipp.com to other klick domains (shared domains).

Ensure that there are no more klick-tipp.com/staging.zauberlist.com links. Topic: Spamhaus blacklisting
"""

import pytest
import logging

import os
import re

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import anmeldeformular_helper as aform_helper
from tests.helpers import subscriber_area_helper as sah
from tests.helpers import splittest_helper as sth

PYTEST_LOGGER = logging.getLogger('pytest_logger')


####################################################################################
# Globals, used in several test functions

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._shared_domain = "staging-beta.com"
    staging_data._register_form_url = "https://klicktipp-staging.s3.amazonaws.com/userimages/268/forms/777/m7z7nz8z2402.html"
    staging_data._splittest_edit_url = "https://app.klicktipp-staging.com/marketingtools/me/splittest/144"
    staging_data._track_pixel_url_edit = "https://app.klicktipp-staging.com/marketingtools/me/splittest/144/embed"

    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._shared_domain = "automizen.io"
    production_data._register_form_url = "https://klicktipp.s3.amazonaws.com/userimages/173644/forms/216179/51gjz41q9z8z7826.html"
    production_data._splittest_edit_url = "https://app.klicktipp.com/marketingtools/me/splittest/69660"
    production_data._track_pixel_url_edit = "https://app.klicktipp.com/marketingtools/me/splittest/69660/embed"

    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._test_customer_mail_subject = "T71"  # Set in e2e-test-dev follow up mail

    # Fill up in test function and use outcome in follow up tests
    test_data._perflog_confirmation = None
    test_data._newsletter_anchors = []

    # For cookie tests
    test_data._stored_cookies_after_confirm = []

    # splittest stats
    test_data._splittest_url_obj = None
    test_data._track_pixel_url = None
    test_data._trackpixel_url_object = None
    test_data._variant_1 = "https://www.example.com/?q=1"
    test_data._variant_2 = "https://www.example.com/?q=2"

    test_data._variant_current_subscriber = None  # We have to variants. Will be set to 1|2 depending on which variant was chosen for subscriber
    test_data._splittest_stats = {}


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.order1  # When you have tests, which depend on each other, you can define a order
def test_get_splittest_stats(login_via_session_cookie):
    """Use Case - Register subscriber with iframe preview of anmeldeform."""
    pytest.selenium_driver.get(test_data._splittest_edit_url)
    kh.sh.click_by_xpath("Set statistics back", '//a[text() = "Statistik zurücksetzen"]')
    kh.sh.click_by_xpath("Accept statistic reset by click on 'zurücksetzen'",
                         '//input[@type="submit" and @value="Zurücksetzen"]')

    test_data._splittest_stats = sth.get_splittest_stats_by_splittest_url(test_data._splittest_edit_url,
                                                                          [test_data._variant_1, test_data._variant_2])

    assert test_data._splittest_stats != {}, "Was not able to fetch splittest stats for " + test_data._splittest_edit_url


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.order2  # When you have tests, which depend on each other, you can define a order
def test_register_single_optin(standard_subscriber_random_mail_address):
    """Test register via form works and collect performance logs and stored cookies for later tests."""
    PYTEST_LOGGER.info("Test register via form works and collect performance logs and stored cookies for later tests.")

    test_data._perflog_confirmation, test_data._stored_cookies_after_confirm, pending_page_ok = aform_helper.preview_register_with_mail_by_form_link(
        test_data._register_form_url, standard_subscriber_random_mail_address, single_opt_in=True)


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_register_single_optin')
@pytest.mark.order3  # When you have tests, which depend on each other, you can define a order
def test_get_followupmail_anchors(standard_subscriber, standard_subscriber_pwd,
                                  standard_subscriber_random_mail_address):
    """Test get and save follow-up mail anchors."""
    test_data._newsletter_anchors = sah.get_anchors_from_mail_via_subscriber_mail_address(standard_subscriber,
                                                                                          pwd=standard_subscriber_pwd,
                                                                                          subscriber_mail_address=standard_subscriber_random_mail_address,
                                                                                          subject=test_data._test_customer_mail_subject)
    assert len(
        test_data._newsletter_anchors) > 0, "Num of newsletter anchors in newsletter should be > 0. Please check. Was newsletter sent/received?"


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order4  # When you have tests, which depend on each other, you can define a order
def test_get_track_pixel(login_via_session_cookie):
    """Get tracking pixel from embed html code."""
    # Call track pixel in same browser session
    pytest.selenium_driver.get(test_data._track_pixel_url_edit)

    embed_html_code_track_pix = kh.sh.pass_xpath_exists("Get embed code for track pixel",
                                                        '//div[@id="edit-trackingpixel"]').text

    # Get something back like "<img src="https://news.staging-beta.com/api/split/44z7nz1hzkz8640" width="1" height="1" />"
    link_match = re.match(r'^[^\s]+ +src="([^"]+)', embed_html_code_track_pix)

    assert link_match.group(1), "Not able to match <img> source of track pixel embed code." + embed_html_code_track_pix

    test_data._track_pixel_url = link_match.group(1)


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order5  # When you have tests, which depend on each other, you can define a order
def test_splittest_url_target_page(base_url):
    """Test Abort anchor on subscriberupdate page. Provide the URL."""
    PYTEST_LOGGER.info("test_splittest_url_target_page")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Splittest-URL")

    test_data._splittest_url_obj = sah.subscriber_area_link(url, "splittest url", check_xpath_list=None,
                                                            check_url_list=[re.escape(test_data._variant_1),
                                                                            re.escape(test_data._variant_2)])
    assert test_data._splittest_url_obj.perflog_setcookie_list != [], "No Set-Cookie header found when calling url : " + url
    # check for track pixel image
    # https://news.staging-beta.com/api/split/44z7nz1hzkz8640
    check_xpath_track_pixel = "//img[@src='" + test_data._track_pixel_url + "']"
    test_data._trackpixel_url_object = sah.subscriber_area_link(test_data._track_pixel_url, "trackpixel url",
                                                                check_xpath_list=[check_xpath_track_pixel])


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_splittest_url_target_page')
@pytest.mark.order6  # When you have tests, which depend on each other, you can define a order
def test_splittest_url_redirects_forbidden_domain(base_url):
    """test_splittest_url_redirects_forbidden_domain."""
    assert [] == test_data._splittest_url_obj.get_redirects_forbidden_domain(base_url, ignore_urls=[
        r'favicon\.ico']), "array should be empty. Otherwise splittest url redirects over forbidden domain"


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_splittest_url_target_page')
@pytest.mark.order7  # When you have tests, which depend on each other, you can define a order
def test_splittest_url_setcookie_cachecontrol():
    """test_splittest_url_setcookie_cachecontrol."""
    assert test_data._splittest_url_obj.get_setcookie_cachecontrol(
        expected_cache_control="no-cache") == [], "Array should be empty if no forbidden cache-control was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_splittest_url_target_page')
@pytest.mark.order8  # When you have tests, which depend on each other, you can define a order
def test_splittest_setcookie_no_forbidden_domain(forbidden_domain):
    """test_splittest_setcookie_no_forbidden_domain."""
    assert test_data._splittest_url_obj.get_setcookie_no_forbidden_domain(
        forbidden_domain) == [], "Array should be empty if no forbidden domain was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_splittest_url_target_page')
@pytest.mark.order9  # When you have tests, which depend on each other, you can define a order
def test_splittest_setcookie_only_shared_domain():
    """test_splittest_setcookie_only_shared_domain."""
    assert test_data._splittest_url_obj.get_setcookie_only_shared_domain(
        test_data._shared_domain) == [], "Array should be empty if only shared domain (" + test_data._shared_domain + ") was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_splittest_url_target_page')
@pytest.mark.order10  # When you have tests, which depend on each other, you can define a order
def test_splittest_cookie_KTSBS_correct_domain():
    """test_splittest_cookie_KTSBS."""
    assert sah.is_cookie_KTSBS_set_for_domain(test_data._splittest_url_obj.perflog_setcookie_list,
                                              test_data._shared_domain) is not None, "Cookie KTSBS should be set."


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_splittest_url_target_page')
@pytest.mark.order11  # When you have tests, which depend on each other, you can define a order
def test_splittest_cookie_KTSTC_correct_domain():
    """test_splittest_cookie_KTSTC."""
    assert sah.is_cookie_KTSTC_set_for_domain(test_data._splittest_url_obj.perflog_setcookie_list,
                                              test_data._shared_domain) is not None, "Cookie KTSTC should be set."


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_splittest_url_target_page')
@pytest.mark.order12  # When you have tests, which depend on each other, you can define a order
def test_splittest_variant_correct_in_KTSTC_cookie():
    """test_splittest_variant_correct_in_KTSTC_cookie."""
    ktstc_cookie = sah.is_cookie_in_cookie_list(test_data._splittest_url_obj.perflog_setcookie_list, r'^KTSTC50',
                                                r'0|1', None)

    assert ktstc_cookie is not None, "Cookie KTSTC should be set and value should be 0 or 1 for the splittest variant index."

    if int(ktstc_cookie["value"]) == 0:
        test_data._variant_current_subscriber = test_data._variant_1
        assert test_data._splittest_url_obj.target_page_url_match == test_data._variant_1, "KTSTC value is 0 but variant page is unexpected. Should match : " + test_data._variant_1
    if int(ktstc_cookie["value"]) == 1:
        test_data._variant_current_subscriber = test_data._variant_2
        assert test_data._splittest_url_obj.target_page_url_match == test_data._variant_2, "KTSTC value is 1 but variant page is unexpected. Should match : " + test_data._variant_2

    assert test_data._variant_current_subscriber is not None, "The KTSTC50 cookie value was not set to 0 or 1 for appropriate variant! " + \
                                                              ktstc_cookie["name"] + "=" + ktstc_cookie["value"]


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_splittest_url_target_page')
@pytest.mark.order13  # When you have tests, which depend on each other, you can define a order
def test_track_pixel_cookies():
    """test_splittest_variant_correct_in_KTSTC_cookie."""
    request_cookies_found = False
    response_setcookies_found = False
    for cookie_obj in test_data._trackpixel_url_object.perflog_cookies:
        print(cookie_obj)
        cook = cookie_obj[next(iter(cookie_obj))]
        if cook.response_url and test_data._track_pixel_url == cook.response_url and cook.setcookie_details:  # test for https://news.staging-beta.com/api/split/44z7nz1fzkz8640

            # expect :
            # Set-Cookie:
            # KTSTC52Z144=543750824    <- Tracking url action 52
            print(cook)

            # Test for response tracking pixel cookie with timestamp - action 52
            ktstc = sah.is_cookie_in_cookie_list(cook.setcookie_details, r'^KTSTC52', r'\d\d\d\d+', None)
            assert ktstc is not None, "Set-Cookie for KTSTC52... not found"
            response_setcookies_found = True
            break

    for cookie_obj in test_data._trackpixel_url_object.perflog_cookies:
        print(cookie_obj)
        cook = cookie_obj[next(iter(cookie_obj))]
        if cook.request_url and test_data._track_pixel_url == cook.request_url and cook.cookies:  # test for https://news.staging-beta.com/api/split/44z7nz1fzkz8640

            # expect :
            # Cookie:
            # KTSBS54716=543750824
            # KTSTC50Z144=1            <- Tracking url action 50
            print(cook)

            # Test for request cookies
            assert hasattr(cook,
                           "cookies") and cook.cookies is not None and cook.cookies != [], "No Cookie header found for request : " + test_data._track_pixel_url
            ktsbs = sah.is_cookie_in_cookie_list(cook.cookies, r'^KTSBS', r'\d+', None)
            ktstc = sah.is_cookie_in_cookie_list(cook.cookies, r'^KTSTC50', r'0|1', None)
            assert ktsbs is not None and ktstc is not None, "One expected cookie is not set: ktsbs or ktstc"

            request_cookies_found = True
            break

    assert response_setcookies_found and request_cookies_found, "We didn't found a response/request url in performance log matching our set-cookie (KTSTC52Z144=543750824) and cookie (KTSBS54716=543750824, KTSTC50Z144=1 or KTSTC52Z144=1) : " + test_data._track_pixel_url


@pytest.mark.tm4j(publish_for="DEV-T71")
@pytest.mark.incremental(if_failed='test_splittest_url_target_page')
@pytest.mark.order14  # When you have tests, which depend on each other, you can define a order
def test_variant_visit_and_action_increased_by_one(login_via_session_cookie):
    """Use Case - Register subscriber with iframe preview of anmeldeform."""
    splittest_stats_now = sth.get_splittest_stats_by_splittest_url(test_data._splittest_edit_url,
                                                                   [test_data._variant_current_subscriber])

    besucher_before = test_data._splittest_stats[test_data._variant_current_subscriber + "_besucher"]
    aktionen_before = test_data._splittest_stats[test_data._variant_current_subscriber + "_aktionen"]

    besucher_now = splittest_stats_now[test_data._variant_current_subscriber + "_besucher"]
    aktionen_now = splittest_stats_now[test_data._variant_current_subscriber + "_aktionen"]

    PYTEST_LOGGER.info("Test stats for variant : " + test_data._variant_current_subscriber)
    PYTEST_LOGGER.info("Original Besucher # : " + besucher_before)
    PYTEST_LOGGER.info("Original Aktionen # : " + aktionen_before)
    PYTEST_LOGGER.info("Now      Besucher # : " + besucher_now)
    PYTEST_LOGGER.info("Now      Aktionen # : " + aktionen_now)

    assert int(besucher_before) + 1 == int(besucher_now) and int(aktionen_before) + 1 == int(
        aktionen_now), "Aktuelle besucher/aktionen vor variant " + test_data._variant_current_subscriber + " should be increased by 1. Besucher/aktionen before : " + besucher_before + "/" + aktionen_before


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

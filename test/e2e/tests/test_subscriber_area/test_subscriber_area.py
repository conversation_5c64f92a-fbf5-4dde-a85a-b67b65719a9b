# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
Test Bestätigungsmail and followup mail links after changing mailing links from klick-tipp.com to other klick domains (shared domains).

Ensure that there are no more klick-tipp.com/staging.zauberlist.com links. Topic: Spamhaus blacklisting
"""

import pytest
import logging

import os
import re
import time

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import anmeldeformular_helper as aform_helper
from tests.helpers import subscriber_area_helper as sah

PYTEST_LOGGER = logging.getLogger('pytest_logger')


####################################################################################
# Globals, used in several test functions

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._shared_domain = "staging-beta.com"
    staging_data._gmailpreview_unsubscribe_link_text = "Unsubscribe"
    staging_data._unsubscribe_signature_link_text = "von unserem Newsletter abmelden"
    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._shared_domain = "automizen.io"
    production_data._gmailpreview_unsubscribe_link_text = "austragen"
    production_data._unsubscribe_signature_link_text = "abmelden"
    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._bestaetigungsmail_subject = "Ein Klick fehlt noch ..."
    test_data._test_customer_mail_subject = "e2e Personalization Links"  # Set in e2e-test-dev follow up mail
    test_data._form_name = "e2eTest: Personalization Links Test"

    test_data._newsletter_anchors = []
    test_data._external_ressources = []


@pytest.mark.tm4j(publish_for='DEV-T280')
@pytest.mark.order1  # When you have tests, which depend on each other, you can define a order
def test_register_subscriber_with_form_preview(login_via_session_cookie, standard_subscriber_random_mail_address):
    """Use Case - Register subscriber with iframe preview of anmeldeform."""
    # Call formular preview via Klick-Tipp and register for newsletter
    new_tab = aform_helper.open_form_preview(test_data._form_name)
    aform_helper.preview_register_with_mail(standard_subscriber_random_mail_address, new_tab)
    kh.sh.click_by_xpath("Close the 'Einbettungscode' Dialog", "//*[@class='modal fade in']//a[text()='Abbrechen']")
    time.sleep(2)


@pytest.mark.tm4j(publish_for='DEV-T280')
@pytest.mark.incremental(if_failed='test_register_subscriber_with_form_preview')
@pytest.mark.order2
def test_accept_bestaetigungsmail(standard_subscriber, standard_subscriber_pwd,
                                  standard_subscriber_random_mail_address):
    """Klick bestaetigenlink in bestätigungslink."""
    # Accept the newsletter after link checks
    bestaetigen_link = sah.get_anchor_from_mail_by_anchor_text("E-Mail-Adresse bestätigen", standard_subscriber,
                                                               pwd=standard_subscriber_pwd,
                                                               subscriber_mail_address=standard_subscriber_random_mail_address,
                                                               subject=test_data._bestaetigungsmail_subject)
    pytest.selenium_driver.get(bestaetigen_link.link)
    kh.sh.pass_xpath_exists("Get Email field in preview",
                            "//p[contains(text(),'Bitte fügen Sie uns Ihren Kontakten hinzu')]")


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_accept_bestaetigungsmail')
@pytest.mark.order3  # When you have tests, which depend on each other, you can define a order
def test_get_followupmail_anchors(standard_subscriber, standard_subscriber_pwd,
                                  standard_subscriber_random_mail_address):
    """Test get and save follow-up mail anchors."""
    test_data._newsletter_anchors = sah.get_anchors_from_mail_via_subscriber_mail_address(standard_subscriber,
                                                                                          pwd=standard_subscriber_pwd,
                                                                                          subscriber_mail_address=standard_subscriber_random_mail_address,
                                                                                          subject=test_data._test_customer_mail_subject)

    test_data._newsletter_anchors = sah.discard_urls_we_can_ignore(test_data._newsletter_anchors, links_to_ignore=[], links_to_ignore_by_linktext=[r'EditLink::'])

    assert len(test_data._newsletter_anchors) > 0, "Num of newsletter anchors in newsletter should be > 0. Please check. Was newsletter sent/received?"


@pytest.mark.tm4j(publish_for='DEV-T255')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order4  # When you have tests, which depend on each other, you can define a order
def test_existence_gmailpreview_trackingpix_3pointimage(standard_subscriber, standard_subscriber_pwd,
                                                        standard_subscriber_random_mail_address):
    """Test existence of images and other resources."""
    test_data._external_ressources = sah.get_external_ressource_links_from_mail(standard_subscriber,
                                                                                standard_subscriber_pwd,
                                                                                standard_subscriber_random_mail_address,
                                                                                test_data._test_customer_mail_subject)

    html_content = sah.get_parsed_mail_html(standard_subscriber,
                                            pwd=standard_subscriber_pwd,
                                            subscriber_mail_address=standard_subscriber_random_mail_address,
                                            subject=test_data._test_customer_mail_subject)

    assert sah.check_for_expected_ressources(
        html_content) is True, "preheader or tracking pixel or 3point image not part of newsletter!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_existence_gmailpreview_trackingpix_3pointimage')
@pytest.mark.order5  # When you have tests, which depend on each other, you can define a order
def test_followup_mail_ressource_urls(forbidden_domain):
    """Test image URLs and other resource URLs. no redirect checks."""
    assert len(
        test_data._external_ressources) > 0, "Num of followup-mail ressource urls (images, etc.) should be > 0. Please check. Was newsletter sent/received? Gmailpreview activated?"

    assert [] == sah.check_achors_for_klicktipp_com(test_data._external_ressources,
                                                    forbidden_domain), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_existence_gmailpreview_trackingpix_3pointimage')
@pytest.mark.order6  # When you have tests, which depend on each other, you can define a order
def test_followup_mail_ressource_url_redirections(standard_subscriber, standard_subscriber_pwd,
                                                  standard_subscriber_random_mail_address, base_url, forbidden_domain,
                                                  standard_e2e_test_dev_pwd):
    """Test image URLs and call them for checking redirects."""
    assert len(
        test_data._newsletter_anchors) > 0, "Num of newsletter anchors in newsletter should be > 0. Please check. Was newsletter sent/received?"

    kh.open_kt_and_login(base_url, account="e2e-test-dev", pwd=standard_e2e_test_dev_pwd)

    test_data._external_ressources = sah.get_external_ressource_links_from_mail(standard_subscriber,
                                                                                standard_subscriber_pwd,
                                                                                standard_subscriber_random_mail_address,
                                                                                test_data._test_customer_mail_subject,
                                                                                ignore_regex_match=[r'api.qrserver.com/v1/create-qr-code?'])  # ignore vCard QR Code -> 403er here, But fine in Email! hmmm

    html_content = sah.get_parsed_mail_html(standard_subscriber,
                                            pwd=standard_subscriber_pwd,
                                            subscriber_mail_address=standard_subscriber_random_mail_address,
                                            subject=test_data._test_customer_mail_subject)

    if not sah.check_for_expected_ressources(html_content):
        PYTEST_LOGGER.error("preheader or tracking pixel or 3point image not part of newsletter!")
        return False

    assert [] == sah.check_anchors_redirections(test_data._external_ressources,
                                                forbidden_domain), "Should be empty array if no unallowed redirects!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order7  # When you have tests, which depend on each other, you can define a order
def test_followup_mail_urls(forbidden_domain):
    """Test URLs in newsletter."""
    assert len(
        test_data._newsletter_anchors) > 0, "Num of newsletter anchors in newsletter should be > 0. Please check. Was newsletter sent/received?"

    # Remove affiliate links from test list e.g. https://staging.zauberlist.com0 Bug existing for that on staging
    #                                            https://klick-tipp.com/29403(/)
    test_data._newsletter_anchors_no_affiliates = sah.discard_urls_we_can_ignore(test_data._newsletter_anchors, links_to_ignore=[
        re.escape(forbidden_domain) + r'/\d+/?$', re.escape(forbidden_domain) + r'\d+/?$'], links_to_ignore_by_linktext=[])
    assert len(
        test_data._newsletter_anchors_no_affiliates) > 0, "After removal of the URLs we can ignore, there were no more URLs to test! Please check your test data (followup mail)"

    # We ignore affiliate links like https://staging.zauberlist.com0 (The 0 behind without a slash is a bug)
    assert [] == sah.check_achors_for_klicktipp_com(test_data._newsletter_anchors_no_affiliates,
                                                    forbidden_domain), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order8  # When you have tests, which depend on each other, you can define a order
def test_redirects_gmail_vorschau_unsubscribe(base_url):
    """Test unsubscribe link in gmail preview."""
    PYTEST_LOGGER.info("Test subscriberupdate page - abort anchor")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, test_data._gmailpreview_unsubscribe_link_text)

    perflog = sah.get_single_anchor_log(url, test_data._gmailpreview_unsubscribe_link_text)
    assert [] == sah.check_single_anchors_redirections(perflog, test_data._gmailpreview_unsubscribe_link_text,
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T322')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order9  # When you have tests, which depend on each other, you can define a order
def test_redirects_subscriptionreferrer(base_url):
    """Test redirects of subscription referrer. %Subscriber:SubscriptionReferrer%"""
    PYTEST_LOGGER.info("Test subscriberupdate page - abort anchor")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::Subscriber::SubscriptionReferrer")

    perflog = sah.get_single_anchor_log(url, "Link::Subscriber::SubscriptionReferrer")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::Subscriber::SubscriptionReferrer",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order10  # When you have tests, which depend on each other, you can define a order
def test_redirects_urlfromcustomfieldtypesingle(base_url):
    """Test redirects of URL from a custom field as Type Single."""
    PYTEST_LOGGER.info("Test subscriberupdate page - abort anchor")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::UrlFromCustomFieldTypeSingle")

    perflog = sah.get_single_anchor_log(url, "Link::UrlFromCustomFieldTypeSingle")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::UrlFromCustomFieldTypeSingle",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order11  # When you have tests, which depend on each other, you can define a order
def test_redirects_urlfromcustomfieldtypeurl(base_url):
    """Test redirects of URL from a custom field as Type URL."""
    PYTEST_LOGGER.info("Test subscriberupdate page - abort anchor")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::UrlFromCustomFieldTypeURL")

    perflog = sah.get_single_anchor_log(url, "Link::UrlFromCustomFieldTypeURL")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::UrlFromCustomFieldTypeURL",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order12  # When you have tests, which depend on each other, you can define a order
def test_redirects_vcard_oldintegration(base_url):
    """Test redirects of old vcard integration."""
    PYTEST_LOGGER.info("Test redirects of old vcard integration.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::VCardOldIntegration")

    perflog = sah.get_single_anchor_log(url, "Link::VCardOldIntegration")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::VCardOldIntegration",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order13  # When you have tests, which depend on each other, you can define a order
def test_redirects_vcard(base_url):
    """Test redirects vcard."""
    PYTEST_LOGGER.info("Test redirects vcard.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::VCard")

    perflog = sah.get_single_anchor_log(url, "Link::VCard")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::VCard",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order14  # When you have tests, which depend on each other, you can define a order
def test_redirects_wufooform(base_url):
    """Test redirects wofoo."""
    PYTEST_LOGGER.info("Test redirects wofoo.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::WufooForm")

    perflog = sah.get_single_anchor_log(url, "Link::WufooForm")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::WufooForm",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order15  # When you have tests, which depend on each other, you can define a order
def test_redirects_placeholder_unsubscribe(base_url):
    """Test placeholder unsubscribe redirects."""
    PYTEST_LOGGER.info("Test placeholder unsubscribe redirects.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::Unsubscribe")

    perflog = sah.get_single_anchor_log(url, "Link::Unsubscribe")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::Unsubscribe",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order16  # When you have tests, which depend on each other, you can define a order
def test_redirects_subscriberinfo(base_url):
    """Test redirects subscriberinfo."""
    PYTEST_LOGGER.info("Test redirects subscriberinfo")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::SubscriberInfo")

    perflog = sah.get_single_anchor_log(url, "Link::SubscriberInfo")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::SubscriberInfo",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order17  # When you have tests, which depend on each other, you can define a order
def test_redirects_subscriber_update(base_url):
    """Test redirects subscriber update."""
    PYTEST_LOGGER.info("Test redirects subscriber update.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::SubscriberUpdate")

    perflog = sah.get_single_anchor_log(url, "Link::SubscriberUpdate")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::SubscriberUpdate",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order18  # When you have tests, which depend on each other, you can define a order
def test_redirects_change_emailaddress(base_url):
    """Test redirects change email address."""
    PYTEST_LOGGER.info("Test redirects change email address.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::ChangeEmailAddress")

    perflog = sah.get_single_anchor_log(url, "Link::ChangeEmailAddress")
    assert [] == sah.check_single_anchors_redirections(perflog, "Link::ChangeEmailAddress",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order20  # When you have tests, which depend on each other, you can define a order
def test_redirects_unsubscribe_signature(base_url):
    """Test redirects unsubscribe signature."""
    PYTEST_LOGGER.info("Test redirects unsubscribe signature.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, test_data._unsubscribe_signature_link_text)

    perflog = sah.get_single_anchor_log(url, "signature_unsubscribe_url")
    assert [] == sah.check_single_anchors_redirections(perflog, "signature_unsubscribe_url",
                                                       base_url), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T69')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order22  # When you have tests, which depend on each other, you can define a order
def test_subscriberupdate_submit(forbidden_domain):
    """Test subscriberupdate submit."""
    PYTEST_LOGGER.info("Test subscriberupdate submit.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::SubscriberUpdate")

    pytest.selenium_driver.get(url)
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldFirstName']", "Randy")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldLastName']", "Randsom")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldCompanyName']", "CompanyName")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldStreet1']", "Street1")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldStreet2']", "Street2")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldCity']", "City")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldState']", "State")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldZip']", "Zip")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldCountry']", "Country")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldPrivatePhone']", "+49 123 45678")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldMobilePhone']", "+49 234 56789")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldPhone']", "+49 345 67890")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldFax']", "+49 456 78901")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldWebsite']", "http://Web.site")
    kh.sh.send_string_to_input("//input[@id='FormValue_Fields[CustomFieldBirthday]']", "01.02.2000")
    kh.sh.send_string_to_input("//input[@id='FormValue_CustomFieldLeadValue']", "2,34", clear=True)

    submit_button = kh.sh.pass_xpath_clickable("Wait for submit button clickable", "//input[@id='edit-submit']")
    kh.sh.pass_xpath_visible("Wait for submit button visible", "//input[@id='edit-submit']")

    # Not working for closing the date picker widget! Worked a long time without problems.
    # kh.sh.click_somewhere("Click somewhere because date widget blocks click on submit button.")
    # ToDo: better workaround to close widget.
    kh.sh.pass_xpath_exists('open date picker', '//a[@data-toggle="collapse"]').click()
    time.sleep(2)
    kh.sh.pass_xpath_exists('close date picker', '//a[@data-toggle="collapse"]').click()
    time.sleep(1)

    perflog = sah.get_single_anchor_log(submit_button, "Submit::UpdateSubscriberPage")
    assert [] == sah.check_single_anchors_redirections(perflog, "Submit::UpdateSubscriberPage",
                                                       forbidden_domain), "Should be empty array if no unallowed redirect!"


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

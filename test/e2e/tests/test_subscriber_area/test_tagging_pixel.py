# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
Test Bestätigungsmail and followup mail links after changing mailing links from klick-tipp.com to other klick domains (shared domains).

Ensure that there are no more klick-tipp.com/staging.zauberlist.com links. Topic: Spamhaus blacklisting
"""

import pytest
import logging

import os
import re
import time

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import anmeldeformular_helper as aform_helper
from tests.helpers import subscriber_area_helper as sah
from tests.helpers import contactcloud_helper as ccloud_helper

PYTEST_LOGGER = logging.getLogger('pytest_logger')


####################################################################################
# Globals, used in several test functions

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._shared_domain = "staging-beta.com"
    staging_data._register_form = "https://klicktipp-staging.s3.amazonaws.com/userimages/268/forms/791/mlz7nz8z614e.html"
    staging_data._tagging_pixel_page = "https://app.klicktipp-staging.com/app/tool/tagging-pixel/settings/268/149"
    staging_data._klick_host_alias = "news"
    staging_data._klick_host_url = "https://" + staging_data._klick_host_alias + "." + staging_data._shared_domain
    staging_data._tagging_pixel_url_xpath = "//img[@src='https://news.staging-beta.com/pix/49z7nzfz97d4']"
    staging_data._bestaetigungsmail_subject = "One click left ..."
    staging_data._confirm_link_text = "Confirm E-mail address"
    staging_data._ktpix_cookie = 'KTPIX54716'
    staging_data._ktsbs_cookie = 'KTSBS54716'
    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._shared_domain = "automizen.io"
    production_data._register_form = "https://klicktipp.s3.amazonaws.com/userimages/173644/forms/216161/51g1z41q9z8z5ec1.html"
    production_data._tagging_pixel_page = "https://app.klicktipp.com/app/tool/tagging-pixel/settings/173644/69664"
    production_data._klick_host_alias = "klick"
    production_data._klick_host_url = "https://" + production_data._klick_host_alias + "." + production_data._shared_domain
    production_data._tagging_pixel_url_xpath = "//img[@src='https://klick.automizen.io/pix/1luez41q9zfz259c']"
    production_data._bestaetigungsmail_subject = "Ein Klick fehlt noch ..."
    production_data._confirm_link_text = "E-Mail-Adresse bestätigen"
    production_data._ktpix_cookie = 'KTPIX35423420'
    production_data._ktsbs_cookie = 'KTSBS35423420'

    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._test_customer_mail_subject = "e2e Personalization Links"  # Set in e2e-test-dev follow up mail

    test_data._tagging_pixel_url_form = re.escape(test_data._klick_host_url + "/pix/") + r'[a-z0-9]{6,}'
    test_data._tagging_pixel_url_obj = None
    test_data._bestaetigen_url_obj = None


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.order1  # When you have tests, which depend on each other, you can define a order
def test_register_via_form(standard_subscriber_random_mail_address):
    """test_register_via_form."""
    PYTEST_LOGGER.info("test_register_via_form")

    aform_helper.preview_register_with_mail_by_form_link(test_data._register_form,
                                                         standard_subscriber_random_mail_address, single_opt_in=False)


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_register_via_form')
@pytest.mark.order2  # When you have tests, which depend on each other, you can define a order
def test_tagging_pixel_url_and_confirm_afterwards(login_via_session_cookie, standard_subscriber,
                                                  standard_subscriber_pwd, standard_subscriber_random_mail_address):
    """Klick bestaetigenlink in bestätigungslink."""
    pytest.selenium_driver.get(test_data._tagging_pixel_page)

    embed_code_tagging_pixel = kh.sh.pass_xpath_exists("get tagging pixel markup",
                                                       "//pre[contains(@class,'snippet-container')]").text
    link_match = re.match(r'^[^\s]+ +src=\'([^\']+)', embed_code_tagging_pixel)
    tagging_pixel_url = link_match.group(1)

    # <img src='https://news.staging-beta.com/pix/49z7nzfz97d4' height='1' width='1' />
    assert re.search(test_data._tagging_pixel_url_form,
                     tagging_pixel_url) is not None, "Did not match expected form of tagging url : " + test_data._tagging_pixel_url_form + " in " + tagging_pixel_url

    test_data._tagging_pixel_url_obj = sah.subscriber_area_link(tagging_pixel_url, "tagging pixel url",
                                                                check_xpath_list=[test_data._tagging_pixel_url_xpath])

    # Accept the newsletter after tagging pixel call to see KTPIX in request. Otherwise we have a new browsersession and dont see that cookie.
    # Make the other tests later as in the TM4J TC
    bestaetigen_link = sah.get_anchor_from_mail_by_anchor_text(test_data._confirm_link_text, standard_subscriber,
                                                               pwd=standard_subscriber_pwd,
                                                               subscriber_mail_address=standard_subscriber_random_mail_address,
                                                               subject=test_data._bestaetigungsmail_subject)

    test_data._bestaetigen_url_obj = sah.subscriber_area_link(bestaetigen_link.link, "Bestätigen url",
                                                              check_xpath_list=[
                                                                  "//p[text()='Bitte fügen Sie uns Ihren Kontakten hinzu.']"])


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_tagging_pixel_url')
@pytest.mark.order3  # When you have tests, which depend on each other, you can define a order
def test_pixel_redirects_forbidden_domain(base_url):
    """test_url_redirects_forbidden_domain."""
    assert [] == test_data._tagging_pixel_url_obj.get_redirects_forbidden_domain(base_url, ignore_urls=[r'favicon\.ico',
                                                                                                        r'/helplog']), "array should be empty. Otherwise tagging pixel url redirects over forbidden domain"


@pytest.mark.tm4j(publish_for="DEV-T281")
@pytest.mark.incremental(if_failed='test_tagging_pixel_url')
@pytest.mark.order4  # When you have tests, which depend on each other, you can define a order
def test_pixel_setcookie_cachecontrol():
    """test_splittest_url_setcookie_cachecontrol."""
    assert test_data._tagging_pixel_url_obj.get_setcookie_cachecontrol(
        expected_cache_control="no-cache") == [], "Array should be empty if no forbidden cache-control was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_tagging_pixel_url')
@pytest.mark.order5  # When you have tests, which depend on each other, you can define a order
def test_pixel_setcookie_no_forbidden_domain(forbidden_domain):
    """test_splittest_setcookie_no_forbidden_domain."""
    assert test_data._tagging_pixel_url_obj.get_setcookie_no_forbidden_domain(
        forbidden_domain) == [], "Array should be empty if no forbidden domain was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_tagging_pixel_url')
@pytest.mark.order6  # When you have tests, which depend on each other, you can define a order
def test_pixel_setcookie_only_shared_domain():
    """test_splittest_setcookie_only_shared_domain."""
    assert test_data._tagging_pixel_url_obj.get_setcookie_only_shared_domain(
        test_data._shared_domain) == [], "Array should be empty if only shared domain (" + test_data._shared_domain + ") was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_tagging_pixel_url')
@pytest.mark.order7  # When you have tests, which depend on each other, you can define a order
def test_pixel_cookie_KTPIX_set_and_correct_domain():
    """Test_pixel_cookie_KTPIX_set_and_correct_domain."""
    ktpix_cookie = sah.is_cookie_in_cookie_list(test_data._tagging_pixel_url_obj.perflog_setcookie_list,
                                                r'^' + test_data._ktpix_cookie, r'[a-z0-9]{6,}', None)

    assert ktpix_cookie is not None, "Cookie " + test_data._ktpix_cookie + " should be set."

    assert sah.is_cookie_KTPIX_set_for_domain(test_data._tagging_pixel_url_obj.perflog_setcookie_list,
                                              test_data._shared_domain) is not None, "Cookie KTPIX should be set."


@pytest.mark.incremental(if_failed='test_register_via_form')
@pytest.mark.order8  # When you have tests, which depend on each other, you can define a order
def test_confirmlink_redirects(base_url):
    """test_confirmlink_redirects."""
    assert [] == test_data._bestaetigen_url_obj.get_redirects_forbidden_domain(base_url, ignore_urls=[
        r'favicon\.ico']), "array should be empty. Otherwise confirm url redirects over forbidden domain"


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_register_via_form')
@pytest.mark.order9  # When you have tests, which depend on each other, you can define a order
def test_confirmlink_cookie_KTSBS_set_and_correct_domain():
    """test_confirmlink_cookie_KTSBS_set_and_correct_domain."""
    ktsbs_cookie = sah.is_cookie_in_cookie_list(test_data._bestaetigen_url_obj.perflog_setcookie_list,
                                                r'^' + test_data._ktsbs_cookie, r'[0-9]{6,}', None)

    assert ktsbs_cookie is not None, "Cookie " + test_data._ktsbs_cookie + " should be set."

    assert sah.is_cookie_KTSBS_set_for_domain(test_data._bestaetigen_url_obj.perflog_setcookie_list,
                                              test_data._shared_domain) is not None, "Cookie KTSBS54716 should be set for " + test_data._shared_domain + "."


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_register_via_form')
@pytest.mark.order10  # When you have tests, which depend on each other, you can define a order
def test_confirmlink_cookie_KTPIX_received_and_correct_domain():
    """test_confirmlink_cookie_KTSBS_set_and_correct_domain."""
    request_cookies_found = False
    response_setcookies_found = False
    for cookie_obj in test_data._bestaetigen_url_obj.perflog_cookies:
        print(cookie_obj)
        cook = cookie_obj[next(iter(cookie_obj))]
        if cook.response_url and re.search(re.escape(test_data._klick_host_url + "/thankyou/") + r'[a-z0-9]{10,}$',
                                           cook.response_url):  # test for https://news.staging-beta.com/api/split/44z7nz1fzkz8640

            # expect :
            # GET https://news.staging-beta.com/thankyou/7nz8paz1r5hsz1zd025
            # Set-Cookie: KTSBS54716=543659636; ... path=/; domain=.staging-beta.com
            print(cook)

            # Test for response tracking pixel cookie with timestamp - action 52
            ktsbs = sah.is_cookie_in_cookie_list(cook.setcookie_details, r'^' + test_data._ktsbs_cookie, r'[0-9]{6,}',
                                                 None)
            assert ktsbs is not None, "Set-Cookie for " + test_data._ktsbs_cookie + "=... not found"

            response_setcookies_found = True
            break

    for cookie_obj in test_data._bestaetigen_url_obj.perflog_cookies:
        print(cookie_obj)
        cook = cookie_obj[next(iter(cookie_obj))]
        if cook.request_url and re.search(re.escape(test_data._klick_host_url + "/thankyou/") + r'[a-z0-9]{10,}$',
                                          cook.request_url):  # test for https://news.staging-beta.com/api/split/44z7nz1fzkz8640

            # expect :
            # GET https://news.staging-beta.com/thankyou/7nz8paz1r5hsz1zd025
            # Cookie:
            # KTPIX54716=49zgz1
            print(cook)

            # Test for request cookies
            assert hasattr(cook,
                           "cookies") and cook.cookies is not None and cook.cookies != [], "No Cookie header found for request : " + "https://news.staging-beta.com/thankyou/...."
            ktpix = sah.is_cookie_in_cookie_list(cook.cookies, r'^' + test_data._ktpix_cookie, r'[a-z0-9]{6,}', None)
            assert ktpix is not None, "One expected cookie is not set: " + test_data._ktpix_cookie

            request_cookies_found = True
            break

    assert response_setcookies_found and request_cookies_found, "We didn't found a request/response url with expected cookies KTPIX54716=49zgz1 and set-cookie->KTSBS54716=543659636: " + test_data._klick_host_url + "/thankyou/"


@pytest.mark.incremental(if_failed='test_register_via_form')
@pytest.mark.order11  # When you have tests, which depend on each other, you can define a order
def test_confirm_setcookie_cachecontrol():
    """test_confirm_setcookie_cachecontrol."""
    assert test_data._bestaetigen_url_obj.get_setcookie_cachecontrol(
        expected_cache_control="no-cache") == [], "Array should be empty if no forbidden cache-control was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_register_via_form')
@pytest.mark.order12  # When you have tests, which depend on each other, you can define a order
def test_confirm_setcookie_no_forbidden_domain(forbidden_domain):
    """test_confirm_setcookie_no_forbidden_domain."""
    assert test_data._bestaetigen_url_obj.get_setcookie_no_forbidden_domain(
        forbidden_domain) == [], "Array should be empty if no forbidden domain was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_register_via_form')
@pytest.mark.order13  # When you have tests, which depend on each other, you can define a order
def test_confirm_setcookie_only_shared_domain():
    """test_confirm_setcookie_only_shared_domain."""
    assert test_data._bestaetigen_url_obj.get_setcookie_only_shared_domain(
        test_data._shared_domain) == [], "Array should be empty if only shared domain (" + test_data._shared_domain + ") was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T92")
@pytest.mark.incremental(if_failed='test_register_via_form')
@pytest.mark.order14  # When you have tests, which depend on each other, you can define a order
def test_smart_tag_in_historie(login_via_session_cookie, standard_subscriber_random_mail_address):
    """test_smart_tag_in_historie."""
    ccloud_helper.open_subscriber(standard_subscriber_random_mail_address)
    time.sleep(2)

    kh.sh.click_by_xpath("Open subscriber Historie", "//a[text()='Kontakt-Historie']")
    time.sleep(2)

    kh.sh.pass_xpath_exists("Test for 'Tagging-Pixel ausgelöst >> General Tagging Pixel",
                            "//a[text()='Tagging-Pixel ausgelöst » General Tagging Pixel (Primärkontakt)']")


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

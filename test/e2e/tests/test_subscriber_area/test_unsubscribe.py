# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
Test Bestätigungsmail and followup mail links after changing mailing links from klick-tipp.com to other klick domains (shared domains).

Ensure that there are no more klick-tipp.com/staging.zauberlist.com links. Topic: Spamhaus blacklisting
"""

import pytest
import logging

import os
import random
import time

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import anmeldeformular_helper as aform_helper
from tests.helpers import subscriber_area_helper as sah

PYTEST_LOGGER = logging.getLogger('pytest_logger')


####################################################################################
# Globals, used in several test functions

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._shared_domain = "staging-beta.com"

    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._shared_domain = "automizen.io"

    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._bestaetigungsmail_subject = "Ein Klick fehlt noch ..."
    test_data._test_customer_mail_subject = "e2e Personalization Links"  # Set in e2e-test-dev follow up mail
    test_data._form_name = "e2eTest: Personalization Links Test"

    # Fill up in test function and use outcome in follow up tests
    test_data._newsletter_anchors = []
    test_data._external_ressources = []

    # cookie tests
    test_data._stored_cookies_after_trackingpixel = []
    test_data._perflog_trackingpixel = None
    test_data._trackingpixel_url = None

    test_data._bestaetigungsmail_subject = "Ein Klick fehlt noch ..."
    test_data._test_customer_mail_subject = "e2e Personalization Links"  # Set in e2e-test-dev follow up mail
    test_data._form_name = "e2eTest: Personalization Links Test"
    # For the "unsubscribe with subscriber data deletion" button test
    test_data._new_mail = "randsomrandy19+" + str(random.getrandbits(32)) + "@gmail.com"

    # Fill up in test function and use outcome in follow up tests
    test_data._newsletter_anchors = []
    test_data._stored_cookies_after_unsubscribe = []
    test_data._perflog_unsubscribe = None
    test_data._perflog_feedback = None


@pytest.mark.tm4j(publish_for="DEV-T256")
@pytest.mark.order1  # When you have tests, which depend on each other, you can define a order
def test_register_subscriber_with_form_preview(login_via_session_cookie, standard_subscriber_random_mail_address):
    """Use Case - Register subscriber with iframe preview of anmeldeform."""
    # Call formular preview via Klick-Tipp and register for newsletter
    new_tab = aform_helper.open_form_preview(test_data._form_name)
    aform_helper.preview_register_with_mail(standard_subscriber_random_mail_address, new_tab)
    kh.sh.click_by_xpath("Close the 'Einbettungscode' Dialog", "//*[@class='modal fade in']//a[text()='Abbrechen']")
    time.sleep(2)


@pytest.mark.tm4j(publish_for="DEV-T256")
@pytest.mark.incremental(if_failed='test_register_subscriber_with_form_preview')
@pytest.mark.order2  # When you have tests, which depend on each other, you can define a order
def test_accept_bestaetigungsmail(standard_subscriber, standard_subscriber_pwd,
                                  standard_subscriber_random_mail_address):
    """Klick bestaetigenlink in bestätigungslink."""
    # Accept the newsletter after link checks
    bestaetigen_link = sah.get_anchor_from_mail_by_anchor_text("E-Mail-Adresse bestätigen", standard_subscriber,
                                                               pwd=standard_subscriber_pwd,
                                                               subscriber_mail_address=standard_subscriber_random_mail_address,
                                                               subject=test_data._bestaetigungsmail_subject)
    pytest.selenium_driver.get(bestaetigen_link.link)
    kh.sh.pass_xpath_exists("Get Email field in preview",
                            "//p[contains(text(),'Bitte fügen Sie uns Ihren Kontakten hinzu')]")


@pytest.mark.tm4j(publish_for="DEV-T256")
@pytest.mark.incremental(if_failed='test_accept_bestaetigungsmail')
@pytest.mark.order3  # When you have tests, which depend on each other, you can define a order
def test_get_followupmail_anchors(standard_subscriber, standard_subscriber_pwd,
                                  standard_subscriber_random_mail_address):
    """Test get and save follow-up mail anchors."""
    test_data._newsletter_anchors = sah.get_anchors_from_mail_via_subscriber_mail_address(standard_subscriber,
                                                                                          pwd=standard_subscriber_pwd,
                                                                                          subscriber_mail_address=standard_subscriber_random_mail_address,
                                                                                          subject=test_data._test_customer_mail_subject)
    assert len(
        test_data._newsletter_anchors) > 0, "Num of newsletter anchors in newsletter should be > 0. Please check. Was newsletter sent/received?"


@pytest.mark.tm4j(publish_for="DEV-T256")
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order4  # When you have tests, which depend on each other, you can define a order
def test_unsubscribepage_abort(forbidden_domain):
    """Test Abbrechen button on unsubscribe page. Test redirections after click."""
    PYTEST_LOGGER.info("Test Abbrechen button on unsubscribe page. Test redirections after click.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::Unsubscribe")

    pytest.selenium_driver.get(url)

    abort_button = kh.sh.pass_xpath_clickable("Wait for abort anchor clickable", "//a[@id='edit-cancel']")
    kh.sh.pass_xpath_visible("Wait for abort anchor visible", "//a[@id='edit-cancel']")

    perflog = sah.get_single_anchor_log(abort_button, "Abort::UnsubscribePage")
    assert [] == sah.check_single_anchors_redirections(perflog, "Abort::UnsubscribePage",
                                                       forbidden_domain), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for="DEV-T98")
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order5  # When you have tests, which depend on each other, you can define a order
def test_unsubscribepage_submit_and_feedback(forbidden_domain):
    """Test submit/feedback button on unsubscribe page and follow up page with feedback textarea. Test redirections after click on submit."""
    PYTEST_LOGGER.info(
        "Test Abbrechen button on unsubscribe page and follow up page with feedback textarea. Test redirections after click.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::Unsubscribe")

    pytest.selenium_driver.get(url)

    submit_button = kh.sh.pass_xpath_clickable("Wait for submit button clickable", "//input[@id='edit-submit']")
    kh.sh.pass_xpath_visible("Wait for submit button visible", "//input[@id='edit-submit']")

    test_data._perflog_unsubscribe = sah.get_single_anchor_log(submit_button, "Submit::UnsubscribePage")
    test_data._stored_cookies_after_unsubscribe = pytest.selenium_driver.get_cookies()
    unsubscribe_ok = sah.check_single_anchors_redirections(test_data._perflog_unsubscribe, "Submit::UnsubscribePage",
                                                           forbidden_domain)

    # Send feedback to klick-tipp, why you want to unsubscribe
    kh.sh.send_string_to_input("//textarea[@id='edit-feedback']", "Because randy wants to unsubscribe!")

    submit_button = kh.sh.pass_xpath_clickable("Wait for submit button (unsubscribe feedback) clickable",
                                               "//input[@id='edit-submit']")
    kh.sh.pass_xpath_visible("Wait for submit button (unsubscribe feedback) visible", "//input[@id='edit-submit']")

    test_data._perflog_feedback = sah.get_single_anchor_log(submit_button, "Submit::UnsubscribeFeedbackPage")
    feedback_send_ok = sah.check_single_anchors_redirections(test_data._perflog_feedback,
                                                             "Submit::UnsubscribeFeedbackPage", forbidden_domain)

    assert unsubscribe_ok == [] and feedback_send_ok == [], "Unsubscribe or feedback sending failed because of unallowed redirections. (see log)"


@pytest.mark.tm4j(publish_for="DEV-T98")
@pytest.mark.incremental(if_failed='test_unsubscribepage_submit_and_feedback')
@pytest.mark.order6  # When you have tests, which depend on each other, you can define a order
def test_unsubscribepage_setcookie_no_forbidden_domain(forbidden_domain):
    """Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie).")
    assert test_data._perflog_unsubscribe, "No performance log catched for unsubscribe submit button click"

    unsubscribe_setcookie_domain = sah.check_single_anchor_cookies_forbidden_domain(test_data._perflog_unsubscribe,
                                                                                    "Submit::UnsubscribePage",
                                                                                    forbidden_domain)

    assert unsubscribe_setcookie_domain == [], "Array should be empty if no forbidden domain was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T98")
@pytest.mark.incremental(if_failed='test_unsubscribepage_submit_and_feedback')
@pytest.mark.order7  # When you have tests, which depend on each other, you can define a order
def test_unsubscribepage_setcookie_only_shared_domain():
    """Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie).")
    assert test_data._perflog_unsubscribe, "No performance log catched for unsubscribe submit button click"

    unsubscribe_setcookie_domain = sah.check_single_anchor_cookies_only_shared_domain(test_data._perflog_unsubscribe,
                                                                                      "Submit::UnsubscribePage",
                                                                                      test_data._shared_domain)

    assert unsubscribe_setcookie_domain == [], "Array should be empty if only shared domain (" + test_data._shared_domain + ") was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T98")
@pytest.mark.incremental(if_failed='test_unsubscribepage_submit_and_feedback')
@pytest.mark.order8  # When you have tests, which depend on each other, you can define a order
def test_unsubscribepage_cookie_KTSBS():
    """Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie).")

    KTSBS_cookie = sah.is_cookie_KTSBS_set_for_domain(test_data._stored_cookies_after_unsubscribe,
                                                      test_data._shared_domain)

    assert KTSBS_cookie is not None, "Cookie KTSBS<csid>=... should be set."


@pytest.mark.tm4j(publish_for="DEV-T98")
@pytest.mark.incremental(if_failed='test_unsubscribepage_submit_and_feedback')
@pytest.mark.order9  # When you have tests, which depend on each other, you can define a order
def test_feedbackpage_setcookie_domain(forbidden_domain):
    """Test Submit feedback after unsubscribe and setting of domain for Set-Cookie."""
    PYTEST_LOGGER.info("Test Submit feedback after unsubscribe and setting of domain for Set-Cookie.")
    assert test_data._perflog_feedback, "No performance log catched for feedback submit button click"

    feedback_setcookie_domain = sah.check_single_anchor_cookies_forbidden_domain(test_data._perflog_feedback,
                                                                                 "Submit::UnsubscribeFeedbackPage",
                                                                                 forbidden_domain)

    assert feedback_setcookie_domain == [], "Array should be empty if no forbidden domain was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T281")
@pytest.mark.incremental(if_failed='test_unsubscribepage_submit_and_feedback')
@pytest.mark.order10  # When you have tests, which depend on each other, you can define a order
def test_unsubscribepage_setcookie_cachecontrol():
    """Test Submit unsubscribtion for invalid cache-control during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid cache-control during cookie setting (Set-Cookie).")
    assert test_data._perflog_unsubscribe, "No performance log catched for unsubscribe submit button click"
    unsubscribe_setcookie_cachecontrol = sah.check_single_anchor_cookies_cachecontrol(test_data._perflog_unsubscribe,
                                                                                      "Submit::UnsubscribePage",
                                                                                      mandatory_cache_control="no-cache")

    assert unsubscribe_setcookie_cachecontrol == [], "Array should be empty if no forbidden cache-control was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T281")
@pytest.mark.incremental(if_failed='test_unsubscribepage_submit_and_feedback')
@pytest.mark.order11  # When you have tests, which depend on each other, you can define a order
def test_feedbackpage_setcookie_cachecontrol():
    """Test Submit feedback after unsubscribe and setting of cache-control for Set-Cookie."""
    PYTEST_LOGGER.info("Test Submit feedback after unsubscribe and setting of cache-control for Set-Cookie.")
    assert test_data._perflog_feedback, "No performance log catched for feedback submit button click"

    feedback_setcookie_cachecontrol = sah.check_single_anchor_cookies_cachecontrol(test_data._perflog_feedback,
                                                                                   "Submit::UnsubscribeFeedbackPage",
                                                                                   mandatory_cache_control="no-cache")

    assert feedback_setcookie_cachecontrol == [], "Array should be empty if no forbidden cache-control was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T258")
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order12  # When you have tests, which depend on each other, you can define a order
def test_unsubscribepage_after_already_unsubscribed(forbidden_domain):
    """Test Abbrechen button on unsubscribe page. Test redirections after click."""
    PYTEST_LOGGER.info("Test Abbrechen button on unsubscribe page. Test redirections after click.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::Unsubscribe")

    pytest.selenium_driver.get(url)

    kh.sh.pass_xpath_exists("Wait for 'Sie sind bereits ausgetragen.'", "//h1[text()='Sie sind bereits ausgetragen.']")


@pytest.mark.tm4j(publish_for="DEV-T257")
@pytest.mark.order13  # When you have tests, which depend on each other, you can define a order
def test_register_another_subscriber_with_form_preview(base_url, standard_e2e_test_dev_pwd):
    """Use Case - Register subscriber with iframe preview of anmeldeform."""
    kh.open_kt_and_login(base_url, account="e2e-test-dev", pwd=standard_e2e_test_dev_pwd)

    # Call formular preview via Klick-Tipp and register for newsletter
    new_tab = aform_helper.open_form_preview(test_data._form_name)
    aform_helper.preview_register_with_mail(test_data._new_mail, new_tab)
    kh.sh.click_by_xpath("Close the 'Einbettungscode' Dialog", "//*[@class='modal fade in']//a[text()='Abbrechen']")
    time.sleep(2)


@pytest.mark.tm4j(publish_for="DEV-T257")
@pytest.mark.incremental(if_failed='test_register_another_subscriber_with_form_preview')
@pytest.mark.order14  # When you have tests, which depend on each other, you can define a order
def test_accept_bestaetigungsmail_for_another_subscriber(standard_subscriber, standard_subscriber_pwd):
    """Klick bestaetigenlink in bestätigungslink."""
    # Accept the newsletter after link checks
    bestaetigen_link = sah.get_anchor_from_mail_by_anchor_text("E-Mail-Adresse bestätigen", standard_subscriber,
                                                               pwd=standard_subscriber_pwd,
                                                               subscriber_mail_address=test_data._new_mail,
                                                               subject=test_data._bestaetigungsmail_subject)
    pytest.selenium_driver.get(bestaetigen_link.link)
    kh.sh.pass_xpath_exists("Get Email field in preview",
                            "//p[contains(text(),'Bitte fügen Sie uns Ihren Kontakten hinzu')]")


@pytest.mark.tm4j(publish_for="DEV-T257")
@pytest.mark.incremental(if_failed='test_accept_bestaetigungsmail_for_another_subscriber')
@pytest.mark.order15  # When you have tests, which depend on each other, you can define a order
def test_get_followupmail_anchors_for_another_subscriber(standard_subscriber, standard_subscriber_pwd,
                                                         standard_subscriber_random_mail_address):
    """Test get and save follow-up mail anchors."""
    test_data._newsletter_anchors = sah.get_anchors_from_mail_via_subscriber_mail_address(standard_subscriber,
                                                                                          pwd=standard_subscriber_pwd,
                                                                                          subscriber_mail_address=test_data._new_mail,
                                                                                          subject=test_data._test_customer_mail_subject)
    assert len(
        test_data._newsletter_anchors) > 0, "Num of newsletter anchors in newsletter should be > 0. Please check. Was newsletter sent/received?"


@pytest.mark.tm4j(publish_for="DEV-T257")
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors_for_another_subscriber')
@pytest.mark.order16  # When you have tests, which depend on each other, you can define a order
def test_unsubscribepage_submit_and_delete(forbidden_domain):
    """Test Abbrechen button on unsubscribe page. Test redirections after click."""
    PYTEST_LOGGER.info("Test Abbrechen button on unsubscribe page. Test redirections after click.")
    assert len(test_data._newsletter_anchors) > 0, "No anchors in newsletter anchor list."
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "Link::Unsubscribe")

    pytest.selenium_driver.get(url)

    submitdelete_button = kh.sh.pass_xpath_clickable("Wait for submit button clickable", "//input[@id='edit-delete']")
    kh.sh.pass_xpath_visible("Wait for submit button visible", "//input[@id='edit-delete']")

    perflog = sah.get_single_anchor_log(submitdelete_button, "SubmitDelete::UnsubscribePage")
    assert [] == sah.check_single_anchors_redirections(perflog, "SubmitDelete::UnsubscribePage",
                                                       forbidden_domain), "Should be empty array if no unallowed redirect!"


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

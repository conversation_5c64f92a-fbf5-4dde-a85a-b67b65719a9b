# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
Test Bestätigungsmail and followup mail links after changing mailing links from klick-tipp.com to other klick domains (shared domains).

Ensure that there are no more klick-tipp.com/staging.zauberlist.com links. Topic: Spamhaus blacklisting
"""

import pytest
import logging

import os
import re
import random
import time

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import anmeldeformular_helper as aform_helper
from tests.helpers import subscriber_area_helper as sah

PYTEST_LOGGER = logging.getLogger('pytest_logger')


####################################################################################
# Globals, used in several test functions

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._shared_domain = "staging-beta.com"
    # T77
    staging_data._register_form_url = "https://klicktipp-staging.s3.amazonaws.com/userimages/268/forms/791/mlz7nz8z614e.html"
    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._shared_domain = "automizen.io"
    production_data._register_form_url = "https://klicktipp.s3.amazonaws.com/userimages/173644/forms/216161/51g1z41q9z8z5ec1.html"
    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._bestaetigungsmail_subject = "Ein Klick fehlt noch ..."
    test_data._test_customer_mail_subject = "e2e Personalization Links"  # Set in e2e-test-dev follow up mail
    test_data._form_name = "e2eTest: Personalization Links Test"
    test_data._bestaetigungsmail_anchors = None
    test_data._perflog_register = None
    test_data._perflog_confirmation = None
    test_data._pending_page_testmail = "randsomrandy19+" + str(random.getrandbits(32)) + "@gmail.com"

    # For cookie tests
    test_data._stored_cookies_after_register = []
    test_data._stored_cookies_after_confirm = []


@pytest.mark.tm4j(publish_for="DEV-T77")
@pytest.mark.order1  # When you have tests, which depend on each other, you can define a order
def test_confirm_pending_page():
    """Test register via form works and collect performance logs and stored cookies for later tests."""
    PYTEST_LOGGER.info("Test register via form works and collect performance logs and stored cookies for later tests.")

    test_data._perflog_register, test_data._stored_cookies_after_register, pending_page_ok = aform_helper.preview_register_with_mail_by_form_link(
        test_data._register_form_url, test_data._pending_page_testmail)


@pytest.mark.tm4j(publish_for="DEV-T77")
@pytest.mark.incremental(if_failed='test_confirm_pending_page')
@pytest.mark.order2  # When you have tests, which depend on each other, you can define a order
def test_newslregister_setcookie_no_forbidden_domain(forbidden_domain):
    """T77 - Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie).")
    assert test_data._perflog_register, "No performance log catched for unsubscribe submit button click"

    setcookie_forbidden_domain = sah.check_single_anchor_cookies_forbidden_domain(test_data._perflog_register,
                                                                                  "E-Mail-Adresse_bestätigen",
                                                                                  forbidden_domain,
                                                                                  response_urls_to_ignore=[re.escape(
                                                                                      'api/subscriber/signin.html')])

    assert setcookie_forbidden_domain == [], "Array should be empty if no forbidden domain was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T77")
@pytest.mark.incremental(if_failed='test_confirm_pending_page')
@pytest.mark.order3  # When you have tests, which depend on each other, you can define a order
def test_newslregister_setcookie_only_shared_domain():
    """Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie).")
    assert test_data._perflog_register, "No performance log catched for unsubscribe submit button click"

    setcookie_allowed_domain = sah.check_single_anchor_cookies_only_shared_domain(test_data._perflog_register,
                                                                                  "E-Mail-Adresse_bestätigen",
                                                                                  test_data._shared_domain,
                                                                                  response_urls_to_ignore=[re.escape(
                                                                                      'api/subscriber/signin.html')])

    assert setcookie_allowed_domain == [], "Array should be empty if only shared domain (" + test_data._shared_domain + ") was used for setting the cookie."


@pytest.mark.tm4j(publish_for='DEV-T77')
@pytest.mark.incremental(if_failed='test_confirm_pending_page')
@pytest.mark.order4  # When you have tests, which depend on each other, you can define a order
def test_newslregister_cookie_KTSBS():
    """Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie).")

    KTSBS_cookie = sah.is_cookie_KTSBS_set_for_domain(test_data._stored_cookies_after_register,
                                                      test_data._shared_domain)

    assert KTSBS_cookie is not None, "Cookie KTSBS<csid>=... should be set."


@pytest.mark.tm4j(publish_for="DEV-T77")
@pytest.mark.incremental(if_failed='test_confirm_pending_page')
@pytest.mark.order5  # When you have tests, which depend on each other, you can define a order
def test_newslregister_setcookie_cachecontrol():
    """Test Submit unsubscribtion for invalid cache-control during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid cache-control during cookie setting (Set-Cookie).")
    assert test_data._perflog_register, "No performance log catched for unsubscribe submit button click"
    setcookie_cachecontrol = sah.check_single_anchor_cookies_cachecontrol(test_data._perflog_register,
                                                                          "E-Mail-Adresse_bestätigen",
                                                                          mandatory_cache_control="no-cache")

    assert setcookie_cachecontrol == [], "Array should be empty if no forbidden cache-control was used for setting the cookie."


@pytest.mark.tm4j(publish_for='DEV-T82')
@pytest.mark.order6  # When you have tests, which depend on each other, you can define a order
def test_register_subscriber_with_form_preview(login_via_session_cookie, standard_subscriber_random_mail_address):
    """Use Case - Register subscriber with iframe preview of anmeldeform."""
    # Call formular preview via Klick-Tipp and register for newsletter
    new_tab = aform_helper.open_form_preview(test_data._form_name)
    aform_helper.preview_register_with_mail(standard_subscriber_random_mail_address, new_tab)
    kh.sh.click_by_xpath("Close the 'Einbettungscode' Dialog", "//*[@class='modal fade in']//a[text()='Abbrechen']")
    time.sleep(2)


@pytest.mark.tm4j(publish_for='DEV-T82')
@pytest.mark.incremental(if_failed='test_register_subscriber_with_form_preview')
@pytest.mark.order7  # When you have tests, which depend on each other, you can define a order
def test_bestaetigungsmail_urls(forbidden_domain, standard_subscriber, standard_subscriber_pwd,
                                standard_subscriber_random_mail_address):
    """Test direct URLs in Bestätigungsmail - no redirect checks. No call on URLs."""
    # Wait for Bestätigungsmail
    time.sleep(10)
    test_data._bestaetigungsmail_anchors = sah.get_anchors_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                                     subscriber_mail_address=standard_subscriber_random_mail_address,
                                                                     subject=test_data._bestaetigungsmail_subject)
    assert len(
        test_data._bestaetigungsmail_anchors) > 0, "Num of anchors in Bestätigungsmail should be > 0. Please check. Was there a Bestätigungsmail?"
    assert [] == sah.check_achors_for_klicktipp_com(test_data._bestaetigungsmail_anchors,
                                                    forbidden_domain), "Should be empty array if no unallowed redirect!"


@pytest.mark.tm4j(publish_for='DEV-T82')
@pytest.mark.incremental(if_failed='test_register_subscriber_with_form_preview')
@pytest.mark.order8  # When you have tests, which depend on each other, you can define a order
def test_accept_bestaetigungsmail(standard_subscriber, standard_subscriber_pwd,
                                  standard_subscriber_random_mail_address):
    """Klick bestaetigenlink in bestätigungslink."""
    # Accept the newsletter after link checks
    bestaetigen_link = sah.get_href_by_anchortext(test_data._bestaetigungsmail_anchors, "E-Mail-Adresse bestätigen")
    test_data._perflog_confirmation = sah.get_single_anchor_log(bestaetigen_link, "E-Mail-Adresse_bestätigen")
    test_data._stored_cookies_after_confirm = pytest.selenium_driver.get_cookies()

    kh.sh.pass_xpath_exists("Get Email field in preview",
                            "//p[contains(text(),'Bitte fügen Sie uns Ihren Kontakten hinzu')]")


@pytest.mark.tm4j(publish_for="DEV-T68")
@pytest.mark.incremental(if_failed='test_accept_bestaetigungsmail')
@pytest.mark.order9  # When you have tests, which depend on each other, you can define a order
def test_confirmlink_setcookie_no_forbidden_domain(forbidden_domain):
    """Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie).")
    assert test_data._perflog_confirmation, "No performance log catched for unsubscribe submit button click"

    setcookie_forbidden_domain = sah.check_single_anchor_cookies_forbidden_domain(test_data._perflog_confirmation,
                                                                                  "E-Mail-Adresse_bestätigen",
                                                                                  forbidden_domain)

    assert setcookie_forbidden_domain == [], "Array should be empty if no forbidden domain was used for setting the cookie."


@pytest.mark.tm4j(publish_for="DEV-T68")
@pytest.mark.incremental(if_failed='test_accept_bestaetigungsmail')
@pytest.mark.order10  # When you have tests, which depend on each other, you can define a order
def test_confirmlink_only_shared_domain():
    """Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie).")
    assert test_data._perflog_confirmation, "No performance log catched for unsubscribe submit button click"

    setcookie_allowed_domain = sah.check_single_anchor_cookies_only_shared_domain(test_data._perflog_confirmation,
                                                                                  "E-Mail-Adresse_bestätigen",
                                                                                  test_data._shared_domain)

    assert setcookie_allowed_domain == [], "Array should be empty if only shared domain (" + test_data._shared_domain + ") was used for setting the cookie."


@pytest.mark.tm4j(publish_for='DEV-T82')
@pytest.mark.incremental(if_failed='test_accept_bestaetigungsmail')
@pytest.mark.order11  # When you have tests, which depend on each other, you can define a order
def test_confirmlink_KTSBS():
    """Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid domains during cookie setting (Set-Cookie).")

    KTSBS_cookie = sah.is_cookie_KTSBS_set_for_domain(test_data._stored_cookies_after_confirm, test_data._shared_domain)

    assert KTSBS_cookie is not None, "Cookie KTSBS<csid>=... should be set."


@pytest.mark.tm4j(publish_for='DEV-T82')
@pytest.mark.incremental(if_failed='test_accept_bestaetigungsmail')
@pytest.mark.order12  # When you have tests, which depend on each other, you can define a order
def test_confirmlink_cachecontrol():
    """Test Submit unsubscribtion for invalid cache-control during cookie setting (Set-Cookie)."""
    PYTEST_LOGGER.info("Test Submit unsubscribtion for invalid cache-control during cookie setting (Set-Cookie).")
    assert test_data._perflog_confirmation, "No performance log catched for unsubscribe submit button click"
    setcookie_cachecontrol = sah.check_single_anchor_cookies_cachecontrol(test_data._perflog_confirmation,
                                                                          "E-Mail-Adresse_bestätigen",
                                                                          mandatory_cache_control="no-cache")

    assert setcookie_cachecontrol == [], "Array should be empty if no forbidden cache-control was used for setting the cookie."


@pytest.mark.tm4j(publish_for='DEV-T68')
@pytest.mark.incremental(if_failed='test_register_subscriber_with_form_preview')
@pytest.mark.order13  # When you have tests, which depend on each other, you can define a order
def test_bestaetigungsmail_urls_redirects(forbidden_domain):
    """Test URLs redirects in Bestätigungsmail."""
    assert len(
        test_data._bestaetigungsmail_anchors) > 0, "Num of Bestätigungsmail anchors in newsletter should be > 0. Please check. Was newsletter sent/received?"

    assert [] == sah.check_anchors_redirections(test_data._bestaetigungsmail_anchors,
                                                forbidden_domain), "Should be empty array if no unallowed redirects!"


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
Test affiliate links for correct redirections.

T78
"""

import pytest
from flaky import flaky
import logging

import os
import re

import jmespath
import json

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import subscriber_area_helper as sah
from tests.helpers import anmeldeformular_helper as aform_helper

PYTEST_LOGGER = logging.getLogger('pytest_logger')


# Overwrite fixture defined in fixtures/login_and_sessions.py
# We dont need to login to klicktipp during the test. Spare time.
@pytest.fixture(scope='module', autouse=False)
def login_and_save_session_cookie(get_webdriver_for_setting_session_cookie, base_url, standard_e2e_test_dev_user,
                                  standard_e2e_test_dev_pwd):
    """Just overwrite for test data setup module."""
    pass


####################################################################################
# Globals, used in several test functions

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._shared_domain = "staging-beta.com"
    staging_data._form_preview = "https://klicktipp-staging.s3.amazonaws.com/userimages/268/forms/801/mvz7nz8z7439.html"
    staging_data._aff_id = "5"
    staging_data._app_domain = "app." + re.match(r'.+?\.(.+)', staging_data._sut_domain).group(1)
    staging_data._app_url = "https://app." + re.match(r'.+?\.(.+)', staging_data._sut_domain).group(1)
    staging_data._sut_full_url = "https://" + staging_data._sut_domain
    staging_data._digistore_id = "38219"
    staging_data._digistore_user = "klicktipp-e2e-test"
    staging_data._standard_aff_domain = "klicktipp-staging.com"
    staging_data._standard_aff_domain_full_url = "https://" + staging_data._standard_aff_domain
    staging_data._aff_cookie_domain = re.match(r'.+?\.(.+)', staging_data._sut_domain).group(1)
    staging_data._old_aff_domain = "klick-tipp-staging.com"
    staging_data._old_aff_domain_full_url = "https://" + staging_data._old_aff_domain
    staging_data._special_aff_domain = re.match(r'.+?\.(.+)', staging_data._sut_domain).group(1)
    staging_data._special_aff_full_url = "https://" + re.match(r'.+?\.(.+)', staging_data._sut_domain).group(1)

    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._shared_domain = "automizen.io"
    production_data._form_preview = "https://klicktipp.s3.amazonaws.com/userimages/173644/forms/228575/5bkpz41q9z8z8578.html"
    production_data._aff_id = "78474"
    production_data._app_domain = "app." + re.match(r'.+?\.(.+)', production_data._sut_domain).group(1)
    production_data._app_url = "https://" + production_data._app_domain
    production_data._sut_full_url = "https://" + production_data._sut_domain
    production_data._digistore_id = "38219"
    production_data._digistore_user = "e2e-test-dev-dri-prod-affiliate"
    production_data._standard_aff_domain = "www.klicktipp.com"
    production_data._standard_aff_domain_full_url = "https://" + production_data._standard_aff_domain
    production_data._aff_cookie_domain = re.match(r'.+?\.(.+)', production_data._sut_domain).group(1)
    production_data._old_aff_domain = "klick-tipp.com"
    production_data._old_aff_domain_full_url = "https://" + production_data._old_aff_domain
    production_data._special_aff_domain = re.match(r'.+?\.(.+)', production_data._sut_domain).group(1)
    production_data._special_aff_full_url = "https://" + re.match(r'.+?\.(.+)', production_data._sut_domain).group(1)

    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._digistore_url = "https://www.digistore24.com/redir"
    test_data._digistore_checkout_url = "https://www.checkout-ds24.com/redir"
    # Once there was one zephyr test to rule them all ;)
    # It was the T78
    # We keep the subject of the original test. Still existing in zephyr.
    test_data._test_customer_mail_subject = "T78"


def get_fail_lists(perflog, redirects):
    """Return two lists. 1st missing redirects, 2nd missing set-cookie headers."""
    failed = []
    failed_cookies = []

    for msg in perflog:
        method = msg['method']
        if method == "Network.responseReceivedExtraInfo":
            #         if method == "Network.requestWillBeSent":
            exp_redirect = redirects.pop(0)
            exp_redirect_pattern = exp_redirect[0]  # URL pattern
            curr_redirect = msg["params"]["documenturl"]
            if not re.search(exp_redirect_pattern, curr_redirect):
                failed.append(exp_redirect_pattern + "::::" + curr_redirect)

            exp_set_cookie = exp_redirect[1]
            if exp_set_cookie is not None:
                try:
                    set_cookie_obj = sah.SetCookieRequest(msg)
                    test_this_setcookie = jmespath.search("[?name=='" + exp_set_cookie["name"] + "']",
                                                          set_cookie_obj.setcookie_details)
                    test_this_setcookie = test_this_setcookie[0]  # only one list item
                    if not (test_this_setcookie is not None and
                            test_this_setcookie["value"] == exp_set_cookie["value"] and
                            test_this_setcookie["domain"] == exp_set_cookie["domain"]):
                        failed_cookies.append(
                            exp_set_cookie["domain"] + "::" + exp_set_cookie["name"] + "::" + exp_set_cookie["value"])
                except:
                    failed_cookies.append(
                        str(exp_redirect_pattern) + "::---------->::" + exp_set_cookie["domain"] + "::" +
                        exp_set_cookie["name"] + "::" + exp_set_cookie["value"])

        if len(redirects) == 0:
            break
    return failed, failed_cookies

# The mail trigger and link fetching is done once for all affiliate links.
# I won't connect it to any zephyr case.
@pytest.mark.order1
def test_trigger_mail_and_get_anchors(standard_subscriber_random_mail_address, standard_subscriber,
                                      standard_subscriber_pwd):
    """Set up steps for test module."""
    aform_helper.preview_register_with_mail_by_form_link(test_data._form_preview,
                                                         standard_subscriber_random_mail_address, single_opt_in=True)
    test_data._newsletter_anchors = sah.get_anchors_from_mail_via_subscriber_mail_address(standard_subscriber,
                                                                                          pwd=standard_subscriber_pwd,
                                                                                          subscriber_mail_address=standard_subscriber_random_mail_address,
                                                                                          subject=test_data._test_customer_mail_subject)
    assert len(
        test_data._newsletter_anchors) > 0, "Num of newsletter anchors in newsletter should be > 0. Please check. Was newsletter sent/received?"


# The test mail:
#
# ​Click <a href="%Link:WebBrowser%">here</a> to open this email in your web browser.<br />
# <br />
# <a href="%User:AffiliateURL%">#A1 Standard Affiliate Link</a><br />
# <a href="https://klick-tipp.com/78474">#A2 Old Affiliate Link</a><br />
# <a href="https://klicktipp.com/datenschutzerklarung/78474">#A4 Special Affiliate Link (from klicktipp.com)</a><br />
# <a href="https://app.klicktipp.com/78474">#A6 Affiliate Link (from app.klicktipp.com)</a><br />
# <a href="https://www.klick-tipp.com/goto.php?goto_id=78474&goto_url=https://www.klicktipp.com/">#B3 Direct Call to goto.php (from www.klick-tipp.com)</a><br />
# <br />
# For referenced #id see table at bottom of<br />
# <a href="https://klicktipp.atlassian.net/wiki/spaces/INFRA/pages/1890943021/Run+aMember+php5.6+on+a+separate+server">Run aMember+php5.6 on a separate server</a>

@flaky(max_runs=1)
@pytest.mark.tm4j(publish_for='DEV-T380')
@pytest.mark.incremental(if_failed='test_trigger_mail_and_get_anchors')
@pytest.mark.order2
def test_redirects_standard_affiliate_link():
    """Test standard affiliate link redirects.
       %User:AffiliateURL%

        e.g. for prod:

        # assert perflog[0]["message"]["params"]["documentURL"] == "https://www.klicktipp.com/78474"
        # assert perflog[12]["message"]["params"]["documentURL"] == "https://www.klicktipp.com?a=78474"
    """
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "#A1 Standard Affiliate Link")
    # url = "https://www.klicktipp.com/78474"

    perflog = sah.get_single_anchor_log(url, "#A1 Standard Affiliate Link")

    # Redirects:
    # https://www.klicktipp-staging.com/5
    # https://www.klicktipp-staging.com/?a=5

    # Every list item contains:
    # URL pattern to match correct redirect
    # set-cookie header (optional)
    redirects = [[re.escape(test_data._sut_full_url + "/" + test_data._aff_id),
                  None],
                 [re.escape(test_data._sut_full_url + "/?a=" + test_data._aff_id),
                  None]
                 ]

    failed_redirects, failed_cookies = get_fail_lists(perflog, redirects)
    assert len(failed_redirects) == 0, "Missing expected redirect for url : \n" + url + "\nMissing:\n" + "\n".join(
        failed_redirects)
    assert len(failed_cookies) == 0, "Missing expected set-cookie header : \n" + url + "\nMissing:\n" + "\n".join(
        failed_cookies)


@flaky(max_runs=1)
@pytest.mark.tm4j(publish_for='DEV-T381')
@pytest.mark.incremental(if_failed='test_trigger_mail_and_get_anchors')
@pytest.mark.order3
def test_redirects_old_affiliate_link():
    """Test standard affiliate link redirects.
       #A2 Old Affiliate Link

       https://klick-tipp.com/78474
    """
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors, "#A2 Old Affiliate Link")
    # url = "https://klick-tipp.com/78474"

    perflog = sah.get_single_anchor_log(url, "#A2 Old Affiliate Link")
    # Every list item contains:
    # URL pattern to match correct redirect
    # set-cookie header (optional)

    # Redirects:
    # https://klick-tipp-staging.com/5
    # https://app.klicktipp-staging.com/5
    # https://www.klicktipp-staging.com/?a=5
    redirects = [[re.escape("/info/"),  # link tracking activated. So first url will be an info link.
                  None],
                 [re.escape(test_data._old_aff_domain_full_url + "/" + test_data._aff_id),
                  None],
                 [re.escape(test_data._app_url + "/" + test_data._aff_id),
                  None],
                 [re.escape(test_data._sut_full_url + "/de/?a=" + test_data._aff_id),
                  None]
                 ]

    failed_redirects, failed_cookies = get_fail_lists(perflog, redirects)

    assert len(failed_redirects) == 0, "Missing expected redirect for url : \n" + url + "\nMissing:\n" + "\n".join(
        failed_redirects)
    assert len(failed_cookies) == 0, "Missing expected set-cookie header : \n" + url + "\nMissing:\n" + "\n".join(
        failed_cookies)


@flaky(max_runs=1)
@pytest.mark.tm4j(publish_for='DEV-T382')
@pytest.mark.incremental(if_failed='test_trigger_mail_and_get_anchors')
@pytest.mark.order4
def test_redirects_special_affiliate_link():
    """Test special affiliate link redirects.
       #A4 Special Affiliate Link (from klicktipp.com)

       https://klicktipp.com/datenschutzerklarung/78474
    """
    sut_domain_without_www = re.match(r'.+?\.(.+)', test_data._sut_domain).group(1)
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors,
                                     "#A4 Special Affiliate Link (from " + sut_domain_without_www + ")")
    # url = "https://klicktipp.com/datenschutzerklarung/78474"

    perflog = sah.get_single_anchor_log(url, "#A4 Special Affiliate Link (from " + sut_domain_without_www + ")")
    # Every list item contains:
    # URL pattern to match correct redirect
    # set-cookie header (optional)

    # Redirects:
    # https://klicktipp-staging.com/datenschutzerklarung/5
    # https://www.klicktipp-staging.com/datenschutzerklarung/5
    # https://www.klicktipp-staging.com/datenschutzerklarung?a=5
    # https://www.klicktipp-staging.com/de/datenschutzerklarung/?a=5
    redirects = [[re.escape("/info/"),  # link tracking activated. So first url will be an info link.
                  None],
                 [re.escape(test_data._special_aff_full_url + "/datenschutzerklarung/" + test_data._aff_id),
                  None],
                 [re.escape(test_data._sut_full_url + "/datenschutzerklarung/" + test_data._aff_id),
                  None],
                 [re.escape(test_data._sut_full_url + "/datenschutzerklarung?a=" + test_data._aff_id),
                  None],
                 [re.escape(test_data._sut_full_url + "/de/datenschutzerklarung/?a=" + test_data._aff_id),
                  None]
                 ]

    failed_redirects, failed_cookies = get_fail_lists(perflog, redirects)

    assert len(failed_redirects) == 0, "Missing expected redirect for url : \n" + url + "\nMissing:\n" + "\n".join(
        failed_redirects)
    assert len(failed_cookies) == 0, "Missing expected set-cookie header : \n" + "\nMissing:\n" + "\n".join(
        failed_cookies)


@flaky(max_runs=1)
@pytest.mark.tm4j(publish_for='DEV-T383')
@pytest.mark.incremental(if_failed='test_trigger_mail_and_get_anchors')
@pytest.mark.order5
def test_redirects_app_klicktipp_com():
    """Affiliate Link (from app.klicktipp.com).
       #A6 Affiliate Link (from app.klicktipp.com)

       https://app.klicktipp.com/78474
    """
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors,
                                     "#A6 Affiliate Link (from " + test_data._app_domain + ")")

    perflog = sah.get_single_anchor_log(url, "#A6 Affiliate Link (from " + test_data._app_domain + ")")
    # Every list item contains:
    # URL pattern to match correct redirect
    # set-cookie header (optional)

    # Redirects:
    # https://app.klicktipp-staging.com/5
    # https://www.klicktipp-staging.com/?a=5
    redirects = [[re.escape("/info/"),  # link tracking activated. So first url will be an info link.
                  None],
                 [re.escape(test_data._app_url + "/" + test_data._aff_id),
                  None],
                 [re.escape(test_data._sut_full_url + "/de/?a=" + test_data._aff_id),
                  None]
                 ]

    failed_redirects, failed_cookies = get_fail_lists(perflog, redirects)

    assert len(failed_redirects) == 0, "Missing expected redirect for url : \n" + url + "\nMissing:\n" + "\n".join(
        failed_redirects)
    assert len(failed_cookies) == 0, "Missing expected set-cookie header : \n" + "\nMissing:\n" + "\n".join(
        failed_cookies)


@flaky(max_runs=1)
@pytest.mark.tm4j(publish_for='DEV-T384')
@pytest.mark.incremental(if_failed='test_trigger_mail_and_get_anchors')
@pytest.mark.order6
def test_redirects_direct_gotophp_from_klick_tipp_com():
    """Direct Call to goto.php (from www.klick-tipp.com).
       #B3 Direct Call to goto.php (from www.klick-tipp.com)

       https://www.klick-tipp.com/goto.php?goto_id=78474&goto_url=https://www.klicktipp.com/
    """
    old_aff_domain_with_www = "www." + test_data._old_aff_domain
    url = sah.get_href_by_anchortext(test_data._newsletter_anchors,
                                     "#B3 Direct Call to goto.php (from " + old_aff_domain_with_www + ")")

    perflog = sah.get_single_anchor_log(url, "#B3 Direct Call to goto.php (from " + old_aff_domain_with_www + ")")
    # Every list item contains:
    # URL pattern to match correct redirect
    # set-cookie header (optional)

    # Redirects:
    # https://www.klick-tipp-staging.com/goto.php?goto_id=5&goto_url=https://www.klicktipp-staging.com/
    # https://app.klicktipp-staging.com/crm/goto.php?goto_id=5&goto_url=https://www.klicktipp-staging.com/
    # https://www.digistore24.com/redir/38219/klicktipp-e2e-test/?redirectto=https://www.klicktipp-staging.com/&redirecthash=99674bdeee7d6ec7044737ddffd2d4682201a64f16c811ae3834e645b72cb9b3
    # https://www.checkout-ds24.com/redir/38219/klicktipp-e2e-test/?redirectto=https://www.klicktipp-staging.com/&redirecthash=99674bdeee7d6ec7044737ddffd2d4682201a64f16c811ae3834e645b72cb9b3&ds24_domain=www.digistore24.com
    # https://www.klicktipp-staging.com/
    redirects = [[re.escape("/info/"),  # link tracking activated. So first url will be an info link.
                  None],
                 [re.escape(
                     "https://www." + test_data._old_aff_domain + "/goto.php?goto_id=" + test_data._aff_id + "&goto_url=" + test_data._sut_full_url + "/"),
                     None],
                 [re.escape(
                     test_data._app_url + "/crm/goto.php?goto_id=" + test_data._aff_id + "&goto_url=" + test_data._sut_full_url + "/"),
                     {'domain': "." + test_data._aff_cookie_domain,  # set-cookie header details
                      'name': 'amember_aff_id',
                      'value': test_data._aff_id
                      }],
                 [re.escape(
                     test_data._digistore_url + "/" + test_data._digistore_id + "/" + test_data._digistore_user + "/" + "?redirectto=https") + r'.+?' + re.escape(
                     test_data._sut_domain) + r'.+?' + re.escape("redirecthash=") + r'[0-9a-z]+',
                  None],
                 [re.escape(
                     test_data._digistore_checkout_url + "/" + test_data._digistore_id + "/" + test_data._digistore_user + "/" + "?redirectto=https") + r'.+?' + re.escape(
                     test_data._sut_domain) + r'.+?' + re.escape("redirecthash=") + r'[0-9a-z]+',
                  None],
                 [re.escape(test_data._sut_full_url + "/"),
                  None]
                 ]

    failed_redirects, failed_cookies = get_fail_lists(perflog, redirects)

    assert len(failed_redirects) == 0, "Missing expected redirect for url : \n" + url + "\nMissing:\n" + "\n".join(
        failed_redirects)
    assert len(failed_cookies) == 0, "Missing expected set-cookie header : \n" + "\nMissing:\n" + "\n".join(
        failed_cookies)


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

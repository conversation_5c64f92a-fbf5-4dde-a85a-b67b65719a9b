# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
Test Bestätigungsmail and followup mail links after changing mailing links from klick-tipp.com to other klick domains (shared domains).

Ensure that there are no more klick-tipp.com/staging.zauberlist.com links. Topic: Spamhaus blacklisting
"""

import pytest
import logging

import os
import re
import random

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import anmeldeformular_helper as aform_helper
from tests.helpers import subscriber_area_helper as sah

PYTEST_LOGGER = logging.getLogger('pytest_logger')


####################################################################################
# Globals, used in several test functions

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._shared_domain = "staging-beta.com"
    staging_data._register_form_url_listhelp = "https://klicktipp-staging.s3.amazonaws.com/userimages/268/forms/791/mlz7nz8z614e.html"
    staging_data._register_form_url_no_listhelp = "https://klicktipp-staging.s3.amazonaws.com/userimages/268/forms/792/mmz7nz8z2426.html"
    staging_data._bestaetigungsmail_subject = "One click left ..."
    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._shared_domain = "automizen.io"
    production_data._register_form_url_listhelp = "https://klicktipp.s3.amazonaws.com/userimages/173644/forms/216161/51g1z41q9z8z5ec1.html"
    production_data._register_form_url_no_listhelp = "https://klicktipp.s3.amazonaws.com/userimages/173644/forms/216052/51cwz41q9z8z6b7c.html"
    production_data._bestaetigungsmail_subject = "Ein Klick fehlt noch ..."
    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._follow_up_mail_subject = "T75"

    test_data._new_mail = "randsomrandy19+" + str(random.getrandbits(32)) + "@gmail.com"


@pytest.mark.tm4j(publish_for="DEV-T74")
@pytest.mark.order1  # When you have tests, which depend on each other, you can define a order
def test_register_via_form(standard_subscriber_random_mail_address):
    """test_register_via_form."""
    PYTEST_LOGGER.info("test_register_via_form")

    aform_helper.preview_register_with_mail_by_form_link(test_data._register_form_url_listhelp,
                                                         standard_subscriber_random_mail_address, single_opt_in=False)


@pytest.mark.tm4j(publish_for="DEV-T74")
@pytest.mark.incremental(if_failed='test_register_via_form')
@pytest.mark.order2  # When you have tests, which depend on each other, you can define a order
def test_listhelp_confirmmail_exists_and_no_listunsubscribe(standard_subscriber, standard_subscriber_pwd,
                                                            standard_subscriber_random_mail_address):
    """test_listhelp_confirmmail_exists_and_no_listunsubscribe."""
    # ToDo: write a pure poll mail function. We do not need the html.
    #       Only use this func for fast solution to wait properly for bestätigungsmail. This func polls.
    sah.get_mail_html_part(standard_subscriber, pwd=standard_subscriber_pwd, mail_address=standard_subscriber_random_mail_address,
                           subject=test_data._bestaetigungsmail_subject, subscriber_id=None)
    all_header_fields = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                            mail_address=standard_subscriber_random_mail_address,
                                                            subject=test_data._bestaetigungsmail_subject)

    listhelp_value = sah.get_mail_header_entries_from_header_list(all_header_fields, "List-Help")
    assert listhelp_value is not None, "List-Help should be set!"

    listunsub = sah.get_mail_header_entries_from_header_list(all_header_fields, "List-Unsubscribe")
    assert listunsub is None, "List-Unsubscribe should NOT be set in case of List-Help header field!"

    assert re.search(re.escape(test_data._shared_domain) + "/csa-compliance",
                     listhelp_value) is not None, "Shared domain " + test_data._shared_domain + " not found in List-Help value : " + listhelp_value


@pytest.mark.tm4j(publish_for="DEV-T75")
@pytest.mark.order3  # When you have tests, which depend on each other, you can define a order
def test_register_via_form_no_listhelp(standard_subscriber_random_mail_address):
    """test_register_via_form_no_listhelp."""
    PYTEST_LOGGER.info("test_register_via_form_no_listhelp")

    aform_helper.preview_register_with_mail_by_form_link(test_data._register_form_url_no_listhelp, test_data._new_mail,
                                                         single_opt_in=True)


@pytest.mark.tm4j(publish_for="DEV-T75")
@pytest.mark.incremental(if_failed='test_register_via_form_no_listhelp')
@pytest.mark.order4  # When you have tests, which depend on each other, you can define a order
def test_no_listhelp_followupmail_but_listunsubscribe_post(standard_subscriber, standard_subscriber_pwd):
    """test_no_listhelp_followupmail_but_listunsubscribe_post."""
    # We dont need the anchors, but this function also polls for some minutes
    sah.get_anchors_from_mail_via_subscriber_mail_address(standard_subscriber, pwd=standard_subscriber_pwd,
                                                          subscriber_mail_address=test_data._new_mail,
                                                          subject=test_data._follow_up_mail_subject)

    all_header_fields = sah.get_all_header_fields_from_mail(standard_subscriber, pwd=standard_subscriber_pwd,
                                                            mail_address=test_data._new_mail,
                                                            subject=test_data._follow_up_mail_subject)

    listhelp_value = sah.get_mail_header_entries_from_header_list(all_header_fields, "List-Help")
    assert listhelp_value is None, "List-Help should NOT be set!"

    listunsub_post_value = sah.get_mail_header_entries_from_header_list(all_header_fields, "List-Unsubscribe-Post")
    assert listunsub_post_value is not None, "List-Unsubscribe-Post should be set in case of no List-Help header field!"

    assert re.search("List-Unsubscribe=One-Click",
                     listunsub_post_value) is not None, "List-Unsubscribe-Post header should be set to List-Unsubscribe=One-Click"


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

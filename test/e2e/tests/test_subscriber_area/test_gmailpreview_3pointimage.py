# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""
Test Bestätigungsmail and followup mail links after changing mailing links from klick-tipp.com to other klick domains (shared domains).

Ensure that there are no more klick-tipp.com/staging.zauberlist.com links. Topic: Spamhaus blacklisting
"""

import pytest
import logging

import os
import time

from tests.helpers import klick_tipp_helper as kh
from tests.helpers import anmeldeformular_helper as aform_helper
from tests.helpers import subscriber_area_helper as sah

PYTEST_LOGGER = logging.getLogger('pytest_logger')


####################################################################################
# Globals, used in several test functions

@pytest.fixture(scope="module", autouse=True)
def test_data_init(base_url):
    """Init your test data."""
    global test_data
    test_data_pool = {}

    sut_domain = "www.klicktipp-staging.com"
    staging_data = kh.sh.TestData()
    staging_data._sut_domain = sut_domain
    staging_data._shared_domain = "staging-beta.com"
    staging_data._3pointimage_text = "Please enable images"
    test_data_pool[sut_domain] = staging_data

    sut_domain = "www.klicktipp.com"
    production_data = kh.sh.TestData()
    production_data._sut_domain = sut_domain
    production_data._shared_domain = "automizen.io"
    production_data._3pointimage_text = "Klicken Sie 'Bilder zulassen', um die Bilder in dieser E-Mail anzusehen."
    test_data_pool[sut_domain] = production_data

    test_data = test_data_pool[os.environ['KLICKTIPP_URL']]

    test_data._bestaetigungsmail_subject = "Ein Klick fehlt noch ..."
    test_data._test_customer_mail_subject = "e2e Personalization Links"  # Set in e2e-test-dev follow up mail
    test_data._form_name = "e2eTest: Personalization Links Test"

    # Fill up in test function and use outcome in follow up tests
    test_data._newsletter_anchors = []
    test_data._external_ressources = []

    # cookie tests
    test_data._perflog_3pointimage = None
    test_data._gmailprev_3pointimage_url = None


@pytest.mark.tm4j(publish_for='DEV-T255')
@pytest.mark.order1  # When you have tests, which depend on each other, you can define a order
def test_register_subscriber_with_form_preview(login_via_session_cookie, standard_subscriber_random_mail_address):
    """Use Case - Register subscriber with iframe preview of anmeldeform."""
    # Call formular preview via Klick-Tipp and register for newsletter
    new_tab = aform_helper.open_form_preview(test_data._form_name)
    aform_helper.preview_register_with_mail(standard_subscriber_random_mail_address, new_tab)
    kh.sh.click_by_xpath("Close the 'Einbettungscode' Dialog", "//*[@class='modal fade in']//a[text()='Abbrechen']")
    time.sleep(2)


@pytest.mark.tm4j(publish_for='DEV-T255')
@pytest.mark.incremental(if_failed='test_register_subscriber_with_form_preview')
@pytest.mark.order2  # When you have tests, which depend on each other, you can define a order
def test_accept_bestaetigungsmail(standard_subscriber, standard_subscriber_pwd,
                                  standard_subscriber_random_mail_address):
    """Klick bestaetigenlink in bestätigungslink."""
    # Accept the newsletter after link checks
    bestaetigen_link = sah.get_anchor_from_mail_by_anchor_text("E-Mail-Adresse bestätigen", standard_subscriber,
                                                               pwd=standard_subscriber_pwd,
                                                               subscriber_mail_address=standard_subscriber_random_mail_address,
                                                               subject=test_data._bestaetigungsmail_subject)
    pytest.selenium_driver.get(bestaetigen_link.link)
    kh.sh.pass_xpath_exists("Get Email field in preview",
                            "//p[contains(text(),'Bitte fügen Sie uns Ihren Kontakten hinzu')]")


@pytest.mark.tm4j(publish_for='DEV-T255')
@pytest.mark.incremental(if_failed='test_accept_bestaetigungsmail')
@pytest.mark.order3  # When you have tests, which depend on each other, you can define a order
def test_get_followupmail_anchors(standard_subscriber, standard_subscriber_pwd,
                                  standard_subscriber_random_mail_address):
    """Test get and save follow-up mail anchors."""
    test_data._newsletter_anchors = sah.get_anchors_from_mail_via_subscriber_mail_address(standard_subscriber,
                                                                                          pwd=standard_subscriber_pwd,
                                                                                          subscriber_mail_address=standard_subscriber_random_mail_address,
                                                                                          subject=test_data._test_customer_mail_subject)
    assert len(
        test_data._newsletter_anchors) > 0, "Num of newsletter anchors in newsletter should be > 0. Please check. Was newsletter sent/received?"


@pytest.mark.tm4j(publish_for='DEV-T255')
@pytest.mark.incremental(if_failed='test_get_followupmail_anchors')
@pytest.mark.order4  # When you have tests, which depend on each other, you can define a order
def test_existence_gmailpreview_3pointimage(standard_subscriber, standard_subscriber_pwd,
                                            standard_subscriber_random_mail_address):
    """Test existence of tracking pixel."""
    test_data._external_ressources = sah.get_external_ressource_links_from_mail(standard_subscriber,
                                                                                standard_subscriber_pwd,
                                                                                standard_subscriber_random_mail_address,
                                                                                test_data._test_customer_mail_subject)
    assert len(
        test_data._external_ressources) > 0, "Num of followup-mail ressource urls (images, etc.) should be > 0. Please check. Was newsletter sent/received? Gmailpreview activated?"
    test_data._gmailprev_3pointimage_url = sah.get_href_by_anchortext(test_data._external_ressources,
                                                                      test_data._3pointimage_text, r'misc/pai.png')
    assert test_data._gmailprev_3pointimage_url is not None, "3pointimage not part of newsletter!"


@pytest.mark.tm4j(publish_for='DEV-T255')
@pytest.mark.incremental(if_failed='test_existence_gmailpreview_3pointimage')
@pytest.mark.order5  # When you have tests, which depend on each other, you can define a order
def test_pointimage_url(forbidden_domain):
    """Test image URLs and other resource URLs. no redirect checks."""
    assert [] == sah.check_achors_for_klicktipp_com(
        [sah.link_class("3pointimage", test_data._gmailprev_3pointimage_url)],
        forbidden_domain), "Should be empty array if no unallowed redirect!"


# @pytest.mark.blocked(by=['DEV-1576'])
@pytest.mark.tm4j(publish_for='DEV-T255')
@pytest.mark.incremental(if_failed='test_existence_gmailpreview_3pointimage')
@pytest.mark.order6  # When you have tests, which depend on each other, you can define a order
def test_redirects(forbidden_domain):
    """Test image URLs and call them for checking redirects."""
    test_data._perflog_3pointimage = sah.get_single_anchor_log(test_data._gmailprev_3pointimage_url, "3pointimage")

    assert [] == sah.check_single_anchors_redirections(test_data._perflog_3pointimage, "3pointimage",
                                                       forbidden_domain), "Should be empty array if no unallowed redirect!"


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, K<PERSON>-Tip<PERSON>
# Email: <EMAIL>
"""Some basic functions to create and config newsletters."""

import logging
import time

import pytest

from tests.helpers import klick_tipp_helper as kh

PYTEST_LOGGER = logging.getLogger('pytest_logger')


def delete_newsletter(name, allow_failure=False):
    """Delete newsletter by name."""
    try:
        category = get_newsletter_category(name)
        open_edit_newsletter(name, category)
        time.sleep(4)
        kh.sh.click_by_xpath("Click on 'Einstellungen' to see Löschen button", "//a[@" + kh.dataE2eId + "='campaignAffix-item-edit']")

        kh.sh.click_by_xpath("Click Löschen button anchor", "//*[@" + kh.dataE2eId + "='deleteBtn']")
        time.sleep(7)
        kh.sh.click_by_xpath("Click confirm delete button from modal dialog", "//*[@" + kh.dataE2eId + "='deleteModal-confirmBtn']")

        kh.wait_modal_dialog_disappear()
    except:
        if not allow_failure:
            raise
        else:
            kh.sh.click_somewhere("Close category dropdown in case of exception above and unfolded combobox")
        pass

import pdb

def create_newsletter_and_mail(name, subject):
    """Create newsletter and mail by newsletter name."""
    kh.click_menu_item("Kampagnen", "Newsletter", "Neuer E-Mail-Newsletter")
    kh.sh.click_by_xpath("Choose rich text editor", "//button[@" + kh.dataE2eId + "='editor-select-btn-rt']")

    name_edit = kh.sh.pass_xpath_clickable("Get the field for name", "//*[@" + kh.dataE2eId + "='entityName-input']")
    name_edit.send_keys(name)

    submit_button = kh.sh.pass_xpath_exists("Get the submit button", "//*[@" + kh.dataE2eId + "='createBtn']")
    kh.sh.click("Click on submit", submit_button)

    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='campaignAffix-item-email']")

    # click | data-e2e-id=extra-settings-panel | open extra settings panel
    kh.sh.click_by_xpath("step x", "//mat-accordion[@" + kh.dataE2eId + "='extra-settings-panel']")

    # Enable gmail Vorschau
    kh.sh.click_by_xpath("step x", "//mat-checkbox[@" + kh.dataE2eId + "='cbGmailPreview']")
    gmail_preview_edit = kh.sh.pass_xpath_exists("Get edit field to enter string for gmail preview",
                                                 "//input[@" + kh.dataE2eId + "='gmailPreview-input']")
    gmail_preview_edit.send_keys("gmailpreview")

    save_btn = kh.sh.pass_xpath_exists("Get the submit button", "//*[@" + kh.dataE2eId + "='saveBtnEdit']")
    kh.sh.click("Click on submit", save_btn)

    time.sleep(1)

    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='campaignAffix-item-editor']")


    # Now we get directly the dialog for creating the newsletter Email
    subject_input = kh.sh.pass_xpath_exists("Get the field for mail subject", "//input[@" + kh.dataE2eId + "='smart-subject-input']")
    subject_input.send_keys(subject)
    time.sleep(1)

    # DEACTIVATED HTML ckeditor -> error phrone behavior. timeouts, etc.
    # # frame_to_be_available_and_switch_to_it
    # html_edit_iframe = kh.sh.pass_xpath_exists("Get html editor iframe", "//iframe[@title='WYSIWYG-Editor, edit-htmlcontent']")
    # pytest.selenium_driver.switch_to.frame(frame_reference=html_edit_iframe)
    # content_textarea = kh.sh.pass_xpath_exists("Get the wysiwyg editor for mail content", "//body")
    # time.sleep(6)
    # kh.sh.pass_xpath_visible("Wait for visibility of edit field for mail content", "//body")
    # # content_textarea = kh.sh.pass_xpath_exists("Get the field for mail content", "//textarea[@id='edit-plaincontent']")
    # # ToDo : workaround to keep some progress here. Often stale element exceptions. Waiting for visibility (above) is not enough it seems.
    # # StaleElementReferenceException: Message: stale element reference: element is not attached to the page document
    # content_textarea.send_keys("Filler text is text that shares some characteristics of a real written text, but is random or otherwise generated. It may be used to display a sample of fonts, generate text for testing, or to spoof an e-mail spam filter. The process of using filler text is sometimes called greeking, although the text itself may be nonsense, or largely Latin, as in Lorem ipsum.")
    # pytest.selenium_driver.switch_to.default_content()

    # Add placeholder links (Austragungslink, etc.)
    kh.sh.click_by_xpath("Enable raw HTML editing", "//mat-checkbox[@data-e2e-id='email-ckEditor-toggleEditorCb']")
    time.sleep(3)
    mail_raw_html_editor = kh.sh.pass_xpath_exists("Get raw HTML text area", "//textarea[@" + kh.dataE2eId + "='ckEditorTextModeArea-input']")
    mail_raw_html_editor.send_keys(
        "Filler text is text that shares some characteristics of a real written text, but is random or otherwise generated. It may be used to display a sample of fonts, generate text for testing, or to spoof an e-mail spam filter. The process of using filler text is sometimes called greeking, although the text itself may be nonsense, or largely Latin, as in Lorem ipsum.")
    mail_raw_html_editor.send_keys("""
    <br>
    <br>
    <a href="%Link:Unsubscribe%">Link:Unsubscribe</a><br>
    <a href="%Link:SubscriberInfo%">Link:SubscriberInfo</a><br>
    <a href="%Link:SubscriberUpdate%">Link:SubscriberUpdate</a><br>
    <a href="%Link:ChangeEmailAddress%">Link:ChangeEmailAddress</a><br>
    <a href="%User:AffiliateURL%">User:AffiliateURL</a><br>
    <a href="%Link:WebBrowser%">Link:WebBrowser</a><br>
    """)
    kh.sh.click_by_xpath("Deactivate raw HTML editing (Force re-formatting of HTML)", "//mat-checkbox[@data-e2e-id='email-ckEditor-toggleEditorCb']")

    # Causes HTML validation error -> Ask Edmund/Tony
    # ToDo : <a href="%Link:NoTrack(URL)%">Link:NoTrack(URL)</a><br>

    # kh.sh.click_by_xpath("Add Austragen-Link to HTML Mail body", '//select//option[@value="%Link:Unsubscribe%"]')
    # wait for ckeditor to rerender
    time.sleep(3)
    kh.sh.click_by_xpath("step x", "//button[@" + kh.dataE2eId + "='saveBtnRichText']")

    time.sleep(1)
    kh.sh.click_by_xpath("step x", "//a[@" + kh.dataE2eId + "='email_editor_newsletter_successlink_stay_in_editor']")


def connect_newsletter_with_anmeldeformular(newsletter, formular):
    """Connect newsletter with given Anmeldeformular."""
    open_edit_newsletter(newsletter)

    time.sleep(3)
    kh.sh.click_by_xpath("Open select by click", "//mat-select[@" + kh.dataE2eId + "='taggedSelect-select']")
    kh.sh.click_by_xpath("Get option to connect with formular", "//mat-option[@" + kh.dataE2eId + "='taggedSelect-select-option-24']")


    # Trigger multiselect dropdown arrow on the right of the dropdown
    # Necessary to get list of available forms
    kh.sh.click_by_xpath("Click multiselect arrow to get list of available form tags", "//*[@" + kh.dataE2eId + "='selectedTaggedWithMS-chip-list']")

    form_div = kh.sh.pass_xpath_exists("Get the correct form",'//mat-option/span/span[text()="über Anmeldeformular eingetragen » ' + formular + '"]')

    kh.sh.click("Click on form div we want to connect to", form_div)

    speichern_button = kh.sh.pass_xpath_exists("Get the Speichern button",
                                               "//*[@" + kh.dataE2eId + "='saveBtn']")
    kh.sh.click("Click the speichern button", speichern_button)

    kh.close_alert_box_angular()


def get_newsletter_category(name):
    """Get string of category e.g. Draft or Scheduled or Sent or Outbox."""
    kh.click_menu_item("Kampagnen", "Newsletter")
    kh.sh.set_wait_timeout(3)
    # categories are ['Draft', 'Sent', 'Scheduled', 'Outgoing'] but we use array index to get the correct xpath:
    for curr_category in ['0', '1', '2', '3']:
        kh.sh.click_by_xpath("click status select", "//mat-select[@" + kh.dataE2eId + "='statusSelect-select']")
        kh.sh.click_by_xpath("select given category x", "//mat-option[@" + kh.dataE2eId + "='statusSelect-select-option-" + curr_category.lower() + "']")

        try:
            kh.sh.pass_xpath_exists("Click the newsletter anchor to get in edit mode",
                                    '//td//a[text()="' + name + '"]')
            return curr_category
        except:
            pass
    PYTEST_LOGGER.error("Newsletter not found! : " + name)
    kh.sh.reset_wait_timeout
    return None


def open_edit_newsletter(name, category="0"):
    """Go into edit mode for newsletter."""
    kh.click_menu_item("Kampagnen", "Newsletter")

    kh.sh.click_by_xpath("step x", "//mat-select[@" + kh.dataE2eId + "='statusSelect-select']")
    kh.sh.click_by_xpath("step x", "//mat-option[@" + kh.dataE2eId + "='statusSelect-select-option-" + category.lower() + "']")

    kh.sh.click_by_xpath("Click the newsletter anchor to get in edit mode", '//td//a[text()="' + name + '"]')


def activate_newsletter_and_wait_for_sending(name):
    """Activate the newsletter and wait until the newsletter was sent."""
    open_edit_newsletter(name)

    time.sleep(1)

    kh.sh.click_by_xpath("Click 'Versandzeitpunkt'", "//a[@" + kh.dataE2eId + "='campaignAffix-item-schedule']")

    time.sleep(1)
    kh.sh.click_by_xpath("Open select to chose time", "//mat-select[@" + kh.dataE2eId + "='sendDateSelect-select']")
    kh.sh.click_by_xpath("Get option to send 'Sofort'", "//mat-option[@" + kh.dataE2eId + "='sendDateSelect-select-option-1']")

    # Something happens in gui and a UI element appears and disappears again. Kind of flickering. This causes some undeterministic behavior
    # in accessing the WebElements. Adding some sleep should help. Avoid interacting during the flickering.

    time.sleep(1)
    kh.sh.click_by_xpath("Click saving newsletter send time button", "//*[@" + kh.dataE2eId + "='saveSendDateBtn']")
    time.sleep(1)
    # Accept the save
    kh.sh.click_by_xpath("Accept the send date modal dialog", "//*[@" + kh.dataE2eId + "='checkRecipientsModal-confirmBtn']")

    kh.close_alert_box_angular()


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

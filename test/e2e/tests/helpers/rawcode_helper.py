# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, <PERSON><PERSON>-Tipp
# Email: <EMAIL>
"""Some basic functions to create and config rawcode (Listbuilding).

Remark!!

Dont wonder about the 10 sec. sleeps. There is no gui problem in reacting on the webelements.
For some reason I have to wait. After hours of research, even a longer single wait
doesn't fix the problem. I am out for the moment. ToDo!

If i dont wait, i get the message, that the subscriber is already subscribed. Same
success page if i would use a single opt-in! But the rawcode is created with the
pre-configured double-optin.
"""

import logging

import pytest

import time
from selenium.webdriver.common.action_chains import ActionChains

from tests.helpers import klick_tipp_helper as kh

PYTEST_LOGGER = logging.getLogger('pytest_logger')


def open_form_preview(name):
    """Given formular name. Open on "Übersicht Listbuilding" page -> Einbettungscode->Vorschau."""
    kh.click_menu_item("Listbuilding", "Listbuilding")

    kh.sh.set_wait_timeout(5)
    kh.sh.click_by_xpath("click the 'Einbettungscode' button of our formular : '" + name + "'",
                         "//span[text()='" + name + "']/../../..//a[text()='Einbettungscode']")
    kh.sh.reset_wait_timeout()

    rawcode = kh.sh.pass_xpath_exists("Get rawcode from einbettungscode dialog",
                                      "//div[@class='modal fade in']//div[contains(@class,'modal-dialog')]//div[@class='form-textarea-wrapper']//textarea").text
    pytest.selenium_driver.get("https://www.tutorialspoint.com/compilers/online-html-editor.htm")
    kh.sh.click_somewhere("provoke datenschutz banner")
    kh.sh.click_somewhere("provoke datenschutz banner")
    time.sleep(10)  # wait for banner

    try:
        kh.sh.click_by_xpath("Accept datenschutz banner","//button[contains(@class,'fc-cta-consent')]")
    except:
        PYTEST_LOGGER.info("Datenschutz banner is not accepted. Maybe not appeared. See screenshot.")
        pass

    actions = ActionChains(pytest.selenium_driver)
    edit_button = kh.sh.pass_xpath_exists("hover edit menu","//button[normalize-space()='Edit']")
    actions.move_to_element(edit_button).perform()

    kh.sh.click_by_xpath("click delete button", "//button[@id='select']")

    actions = ActionChains(pytest.selenium_driver)
    edit_button = kh.sh.pass_xpath_exists("hover settings to move away from edit menu","//button[normalize-space()='Settings']")
    actions.move_to_element(edit_button).perform()

    actions = ActionChains(pytest.selenium_driver)
    edit_button = kh.sh.pass_xpath_exists("re-hover edit menu to get the delete menu entry","//button[normalize-space()='Edit']")
    actions.move_to_element(edit_button).perform()

    kh.sh.click_by_xpath("click delete button", "//button[@id='delete']")

    textarea = kh.sh.pass_xpath_exists("Get code area", "//textarea[contains(@class,'ace_text-input')]")[0]
    textarea.send_keys(rawcode)
    time.sleep(5)


def preview_register_with_mail(mail_address, single_opt_in=False):
    """Pass mail address, and if tab is given, we switch to this tab and back after registering.

    Switch to preview tab,
    enter mail address,
    submit,
    close tab,
    switch back to main window.
    """
    # generated html preview is in iframe
    pytest.selenium_driver.switch_to.frame(0)
    email_field = kh.sh.pass_xpath_exists("Get Email field in preview", "//input[@id='FormField_EmailAddress']")
    time.sleep(5)
    kh.sh.pass_xpath_visible("Wait for email field visibility", "//input[@id='FormField_EmailAddress']")
    time.sleep(5)
    email_field.send_keys(mail_address)
    time.sleep(5)
    kh.sh.click_by_xpath("Click submit", "//input[@id='FormSubmit']")

    time.sleep(13)

    if single_opt_in is False:
        kh.sh.pass_xpath_exists("Check response page after sending mail address via formular.",
                                "//p[contains(text(),'Öffnen Sie unsere E-Mail in Ihrem')]")
    else:
        kh.sh.pass_xpath_exists("Check response page after sending mail address via formular.",
                                "//p[contains(text(),'Bitte fügen Sie uns Ihren Kontakten hinzu.')]")


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, K<PERSON>-Tipp
# Email: <EMAIL>
"""This module provides image comparison functions."""

import os
import time
import math
import logging
import operator
from functools import reduce
from pathlib import Path
import pytest
import glob

from PIL import Image
from PIL import ImageDraw
from PIL import ImageChops

from .selenium_helper import resize_browser_window
from .selenium_helper import pass_xpath_exists
from .selenium_helper import set_wait_timeout
from .selenium_helper import reset_wait_timeout
from .selenium_helper import wait_for_page_ready_for_test
from .selenium_helper import log_url_at_fail


PYTEST_LOGGER = logging.getLogger('pytest_logger')


class ScreenMask:
    """Some constants for masking."""

    top_main_menu = (1, 1, 1359, 61)
    left_feedback_area = (1, 128, 35, 1359)
    bottom_feedback_area = (1003, 1652, 1340, 1901)
    bee_top_menu = (1, 61, 1359, 128)
    bee_sidebar = (955, 125, 1363, 1400)
    bee_mail_content = (30, 129, 978, 2000)
    bee_templbrowser_editbackground_left = (30, 124, 71, 1926)
    bee_templbrowser_editbackground_bottom = (26, 1901, 959, 1926)
    bee_templbrowser_mask_above_details = (1, 1, 1356, 529)
    bee_templbrowser_mask_below_details = (1, 1377, 1355, 1926)
    bee_templbrowser_mask_leftof_details = (1, 1, 76, 1926)
    bee_templbrowser_mask_rightof_details = (1243, 503, 1355, 1424)
    bee_bottom_scrollbar = (1, 1885, 1356, 1914)
    beacon_area = (927, 1227, 1344, 1908)


def delete_expected_images(test_case):
    new_screenshots_flag = os.getenv("E2E_RR_NEW_SCREENSHOTS")
    if new_screenshots_flag:
        new_screenshots = bool(int(new_screenshots_flag))
    else:
        new_screenshots = False

    if new_screenshots:
        ipad_dummy_shot = "ipad_dummy_shot.png"
        if pytest.browser == 'safari_ipad' and not os.path.exists(ipad_dummy_shot):

            pytest.selenium_driver.save_screenshot(ipad_dummy_shot)
            pytest.selenium_driver.get_screenshot_as_png()
            dummy_img = Image.open(ipad_dummy_shot)
            pytest.browser_resolution = str(dummy_img.width) + "x" + str(dummy_img.height)
            dummy_img.close()

        helpers_dir = Path(os.path.dirname(os.path.realpath(__file__)))

        screens_dir = os.path.join(str(helpers_dir.parent), 'screenshots', test_case)

        # At first, we construct the filenames of the 3 images we work with
        #
        # ./screenshots/test_login/after_login_chrome_1024x768_expected.png
        # ./screenshots/test_login/after_login_chrome_1024x768_current.png
        # ./screenshots/test_login/after_login_chrome_1024x768_difference.png
        base_screen_name = "*" + "_" + pytest.browser + "_" + pytest.browser_resolution
        expected = os.path.join(screens_dir, base_screen_name + "_expected.png")
        expected_images = glob.glob(expected)

        PYTEST_LOGGER.info(f"Delete expected images for current resolution ({pytest.browser_resolution}) and browser ({pytest.browser})")
        if expected_images:
            for image in expected_images:
                PYTEST_LOGGER.info(f"Delete {image}")
                os.remove(image)
        else:
            PYTEST_LOGGER.info(f"No expected images to delete.")
    else:
        PYTEST_LOGGER.info(f"E2E_RR_NEW_SCREENSHOTS not set or 0/false. We keep the current expected images.")


def screen_test(test_case, screen_name, ignore_areas=None, ignore=None, allow_fail=None, fail=None, new_expected=None):
    """Make a screenshot of the current screen and compare it against a reference image.

    Depending on the current browser and resolution
    the correct reference image will be chosen from the screenshots' folder.

    You need to set --screen-tests for pytest, if you want to do a screen comparison.

    Ignore screen comparison errors with pytest parameter --screen-tests-fail-ignore.
    You will still see image differences in the report, BUT you have to encapsulate the
    passed results, because the ignored image differences are hidden when the workflow test passed.

    If no reference/expected image exists, there will be no error, but the image will be generated.

    If you want to update reference images or add new reference images created by the test to your
    git repository, you need somehow to copy the created images out of the docker container.

    Do that after the test execution by:
    > docker cp e2e-test-firefox-tester-run_1:/tests-e2e/screenshots ./screenshots

    Then copy the new screenshots from the local screenshots folder to the correct place in ./tests/screenshots/...

    The next test execution with docker will take the new reference images.

    e.g. screen_test("test_login","after_login")

    ./screenshots/test_login/after_login_chrome_1024x768_expected.png
    ./screenshots/test_login/after_login_chrome_1024x768_current.png
    ./screenshots/test_login/after_login_chrome_1024x768_difference.png

    :param test_case: folder name for screenshot
    :param screen_name: some prefix in front of screenshot
    :param ignore_areas: list of ignored rectangles like [(220,220,330,540),(99,33,449,889)]
    :param ignore: maybe you want to ignore this screen test for now
    :param allow_fail: override global allow-fail setting
    :param fail: maybe you want to force the fail if screen test fails, even if allow-fail is globally set
    :param new_expected: create a new expected image and delete old one, if existing
    """
    # ToDo: Update ignore_areas because i bring into the appropriate resolution. ignore_areas will differ between resolutions.

    if not pytest.screen_tests or (ignore is not None and ignore is True):
        return

    PYTEST_LOGGER.info("Screen comparison '" + screen_name + "' " + pytest.browser_resolution)

    try:
        set_wait_timeout(2)
        acc_info_webel = pass_xpath_exists("Remove account info image.", "//div[@id='account-info-wrapper']")
        pytest.selenium_driver.execute_script("arguments[0].parentNode.removeChild(arguments[0])", acc_info_webel)
    except:
        # account info graphic already removed
        pass
    reset_wait_timeout()
    wait_for_page_ready_for_test()

    # Do screenshots always with the same browser resolution

    # The ipad 12.x device on BrowserStack doesn't allow resizing or defining screen size.
    # Also getting screen size doesn't return the real size.
    # We create a dummy image and get the resolution.
    ipad_dummy_shot = "ipad_dummy_shot.png"
    if pytest.browser == 'safari_ipad' and not os.path.exists(ipad_dummy_shot):

        pytest.selenium_driver.save_screenshot(ipad_dummy_shot)
        pytest.selenium_driver.get_screenshot_as_png()
        dummy_img = Image.open(ipad_dummy_shot)
        pytest.browser_resolution = str(dummy_img.width) + "x" + str(dummy_img.height)
        dummy_img.close()
    else:
        resize_browser_window()

    # ToDo: Sometimes we have some flaky behavior. Dirty workaround.
    while pytest.selenium_driver.execute_script("return document.readyState") != "complete":
        continue

    wait_for_page_ready_for_test()
    time.sleep(3)

    helpers_dir = Path(os.path.dirname(os.path.realpath(__file__)))

    screens_dir = os.path.join(str(helpers_dir.parent), 'screenshots', test_case)

    # At first, we construct the filenames of the 3 images we work with
    #
    # ./screenshots/test_login/after_login_chrome_1024x768_expected.png
    # ./screenshots/test_login/after_login_chrome_1024x768_current.png
    # ./screenshots/test_login/after_login_chrome_1024x768_difference.png
    base_screen_name = screen_name + "_" + pytest.browser + "_" + pytest.browser_resolution
    current = os.path.join(screens_dir, base_screen_name + "_current.png")
    expected = os.path.join(screens_dir, base_screen_name + "_expected.png")
    difference = os.path.join(screens_dir, base_screen_name + "_difference.png")

    # Create the screenshot dir, if we never produced screenshots before
    if not os.path.exists(screens_dir):
        os.makedirs(screens_dir)
    else:
        # Ensure that no old current images are existing
        if os.path.exists(current):
            PYTEST_LOGGER.info("Remove previously created 'current-image' : " + current)
            os.remove(current)

        # Ensure that old difference images are no longer existing
        if os.path.exists(difference):
            PYTEST_LOGGER.info("Delete old difference images: " + difference)
            os.remove(difference)

        # Ensure that old difference images are no longer existing
        if os.path.exists(expected) and (new_expected is not None and new_expected is True):
            PYTEST_LOGGER.info("Delete expected images: " + expected)
            os.remove(expected)


    # If no reference exists, we only create the reference and abort the screen test.
    # The image can be copied out of the container in the appropriate screenshot folder.
    # Then just commit/push to your repository.
    if not os.path.exists(expected):
        PYTEST_LOGGER.info("Reference image not existing: " + expected)
        PYTEST_LOGGER.info("Creating reference: " + expected)
        pytest.selenium_driver.save_screenshot(expected)
        pytest.selenium_driver.get_screenshot_as_png()
        # By setting current,difference to None, we tell our conftest.py, that
        # we have created a new reference image.
        pytest.screen_tests_failed_images.append([None, expected, None])
        PYTEST_LOGGER.info("Abort screen comparison because reference image was not existing")
        PYTEST_LOGGER.info(
            "Below the command to copy the new reference out of the container into your local screenshots dir.")
        PYTEST_LOGGER.info("docker cp e2e-test-" + pytest.browser + "-tester-run-1:" + os.path.join(test_case,
                                                                                                    expected) + " ./tests/screenshots/" + test_case + "/" + os.path.basename(
            expected))
        return

    # Get the current screen as screenshot
    pytest.selenium_driver.save_screenshot(current)
    pytest.selenium_driver.get_screenshot_as_png()

    # If we need to ignore some areas within the images, we have to draw
    # some rectangles and fill them up with black, that we do not run into
    # ignorable differences, like time stamps in the image or other situation
    # depending differences.

    curr_masked = None
    exp_masked = None

    if ignore_areas and pytest.browser_resolution in ignore_areas:
        # Depending on the resolution the ignore area has different coordinates
        relevant_areas = ignore_areas[pytest.browser_resolution]

        if relevant_areas:
            ref_tmp = os.path.join(screens_dir, "ref_tmp.png")
            cur_tmp = os.path.join(screens_dir, "cur_tmp.png")

            try:
                os.remove(cur_tmp)
            except:
                pass

            try:
                os.remove(ref_tmp)
            except:
                pass

            cur = Image.open(current).convert("RGB")
            ref = Image.open(expected).convert("RGB")

            exp_draw = ImageDraw.Draw(ref)
            cur_draw = ImageDraw.Draw(cur)

            for rect in relevant_areas:
                cur_draw.rectangle(rect, fill='black')
                exp_draw.rectangle(rect, fill='black')

            ref.save(ref_tmp)
            cur.save(cur_tmp)
            cur.close()
            ref.close()

            curr_masked = cur_tmp
            exp_masked = ref_tmp
        else:
            log_url_at_fail()
            pytest.fail(
                "For your current resolution (" + pytest.browser_resolution + "), no ignore area block is defined! e.g. " + pytest.browser_resolution + ": [(339, 283, 2424, 2047)]",
                pytrace=False)
    else:
        curr_masked = current
        exp_masked = expected

    PYTEST_LOGGER.info("Test current screenshot against: '" + expected)
    # Test for RMS (Root-Mean-Square) and if RMS is not acceptable, we do a pixel by pixel
    diff_exists = compare_rms_and_pxbypx(curr_masked, exp_masked, difference)

    if diff_exists:
        pytest.screen_tests_failed_images.append([current, expected, difference])

        PYTEST_LOGGER.info(
            "Difference between ref and curr image: '" + test_case + "/" + screen_name + "' " + pytest.browser_resolution)
        PYTEST_LOGGER.info("If you want to use the current image as new ref, here is the path.")
        PYTEST_LOGGER.info(
            "But you have to copy it out of the container and save it locally in your screenshots folder.")
        PYTEST_LOGGER.info(os.path.join(test_case, current))
        # ToDo: use os.path.join to concat clean!
        PYTEST_LOGGER.info("docker cp e2e-test-" + pytest.browser + "-tester-run-1:" + os.path.join(test_case,
                                                                                                    current) + " ./tests/screenshots/" + test_case + "/" + os.path.basename(
            expected))

        log_url_at_fail()

        # The allow_fail and fail flags are coming from the record replay test tool
        # They can additionaly modify the reporting and screen-test error behavior
        if not pytest.screen_tests_fail_ignore:
            if allow_fail is not None and allow_fail is True:
                PYTEST_LOGGER.info("Ignore image comparison error because of '--screen-tests-fail-ignore' NOT SET and rr-allow_fail = True")
                return

        if pytest.screen_tests_fail_ignore:
            if fail is not None and fail is False:
                PYTEST_LOGGER.info("Ignore image comparison error because of '--screen-tests-fail-ignore' IS SET and rr-fail = False")
                return
            PYTEST_LOGGER.info("Pass image comparison error because of 'pytest --screen-tests-fail-ignore'")
            return

        # if pytest.screen_tests_fail_ignore:
        #     if allow_fail is not None or fail is not None:
        #         if allow_fail is True:
        #             PYTEST_LOGGER.info("Pass image comparison error because of 'pytest --screen-tests-fail-ignore' and rr-allow_fail = True")
        #         if fail is False:
        #             PYTEST_LOGGER.info("Pass image comparison error because of 'pytest --screen-tests-fail-ignore' and rr-fail = False")
        #         if allow_fail is True or fail is False:
        #             return
        #     PYTEST_LOGGER.info("Pass image comparison error because of 'pytest --screen-tests-fail-ignore'")
        #     return

        # Use assume, to avoid test execution stop, by first assertion.
        # Assume allows ongoing test execution and summarized failed asserts as collection at the end.
        with pytest.assume:
            assert False, "Image comparison Error(s)! " + current + " != " + expected
    else:
        PYTEST_LOGGER.info("Screen comparison pass for '" + test_case + "/" + screen_name + "' !")


def compare_rms_and_pxbypx(pic1, pic2, path_to_diff):
    """Get paths of three images and compare the first two.

    Save the difference image in the third path.

    1st compare by the RMS value.
    RMS (Root-Mean-Square) == 0.0 means, no difference.
    Only if RMS fails and value is >1.5, we do the pixel by pixel.

    2nd compare pixel by pixel
    Compare and write the difference image.

    :param pic1: path to image 1 -> in our case the current
    :param pic2: path to image 2 -> in our case the expected
    :rtype: Return False if diff exists!
    """
    pillow_img1 = Image.open(pic1).convert("RGB")
    pillow_img2 = Image.open(pic2).convert("RGB")

    # After some tests, i decided for 100 -> lets see if it is a stable value
    rms_threshold = 160.0

    # General formular taken from some paper
    rms_value = math.sqrt(
        reduce(operator.add, map(lambda a, b: (a - b) ** 2, pillow_img1.histogram(), pillow_img2.histogram())) / len(
            pillow_img1.histogram()))
    PYTEST_LOGGER.info("RMS value (Root-Mean-Square) : " + str(rms_value))

    diff_exists = False

    if rms_value <= rms_threshold:
        pillow_img1.close()
        pillow_img2.close()
        PYTEST_LOGGER.info("RMS <= " + str(rms_threshold) + ". No difference recognizable by human eye.")
        return False
    else:
        diff_exists = True

    # Faster difference image creation, but no red highlighting. Lets see how it works and how the acceptance is.
    pillow_img_diff = ImageChops.difference(pillow_img1, pillow_img2)
    pillow_img_diff.save(path_to_diff)

    # Old diff image creation.
    # # Create some empty image with the same size of one of the compared images. Those should be equal.
    # pillow_img_diff = Image.new("RGB", pillow_img1.size, (255, 255, 255))
    # for coord_x in range(pillow_img1.size[0]):
    #     for coord_y in range(pillow_img1.size[1]):
    #         if pillow_img1.getpixel((coord_x, coord_y)) != pillow_img2.getpixel((coord_x, coord_y)):
    #             # Put a red pixel, where we have a different color
    #             pillow_img_diff.putpixel((coord_x, coord_y), (255, 0, 0))
    #         else:
    #             # no diff, take over org color
    #             pillow_img_diff.putpixel((coord_x, coord_y), pillow_img1.getpixel((coord_x, coord_y)))
    #
    # pillow_img_diff.save(path_to_diff)

    pillow_img1.close()
    pillow_img2.close()
    pillow_img_diff.close()

    return diff_exists


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

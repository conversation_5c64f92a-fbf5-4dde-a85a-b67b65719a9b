# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, K<PERSON>-Tipp
# Email: <EMAIL>
"""Some basic functions to work with request forwarders."""

import logging

import pytest

import time
import re

from tests.helpers import selenium_helper as sh
from tests.helpers import klick_tipp_helper as kh

PYTEST_LOGGER = logging.getLogger('pytest_logger')


def get_forwarders_edit_links_by_regex(regex_to_match):
    """Pass a regex and you get a list of forwarder edit links matching this regex. List is empty if nothing was found."""
    kh.click_menu_item("Listbuilding", "Listbuilding")
    is_collapsed = False
    kh.sh.set_wait_timeout(2)
    try:
        kh.sh.click_by_xpath("Open dropdown to make anchors available for grep.",
                             "//div[@id='edit-filter-buildtype-wrapper']//button[@data-toggle='dropdown']")
        kh.sh.reset_wait_timeout()
    except:
        kh.sh.reset_wait_timeout()
        is_collapsed = True
        pass

    if is_collapsed:
        sh.click_by_xpath("Open filter options.", "//div[@class='panel-heading']//a")
        kh.sh.click_by_xpath("Open dropdown to make anchors available for grep.",
                             "//div[@id='edit-filter-buildtype-wrapper']//button[@data-toggle='dropdown']")

    sh.click_by_xpath("Filter by type forwarder.",
                      "//div[contains(@class,'kt-optgroup-select')]//a[text()='Eintragung per E-Mail']")
    sh.click_by_xpath("Apply filter by click on submit",
                      "//div[contains(@class,'panel-default')]//input[@id='edit-submit']")

    # Get not cutted name by span element with hidden-xxs class
    try:
        spans = sh.pass_xpath_exists("Get all spans representing a forwarder (span hidden-xxs class set).",
                                     "//thead//a[text()='Name']/../../../..//span[contains(@class,'hidden-xxs')]")
        spans = [spans] if type(spans) is not list else spans
    except:
        spans = []
        pass

    # Get all uncutted spans because forwarder name is not too long
    try:
        spans2 = sh.pass_xpath_exists("Get all spans representing a forwarder (span hidden-xxs/visible-xxs not set, ).",
                                      "//thead//a[text()='Name']/../../../..//span[not(contains(@class,'hidden-xxs')) and not(contains(@class,'visible-xxs'))]")
        spans2 = [spans2] if type(spans2) is not list else spans2
    except:
        spans2 = []
        pass

    spans += spans2

    if spans is []:
        return []

    hrefs_for_deletion = []
    for span in spans:
        curr_forwarder_name = span.text
        if re.search(regex_to_match, curr_forwarder_name):
            curr_anchor = sh.pass_xpath_exists("get href for " + curr_forwarder_name, "./../../a", span)
            curr_href = curr_anchor.get_attribute("href")
            hrefs_for_deletion.append(curr_href)
    return hrefs_for_deletion


def delete_list_of_forwarders(list_of_forwarder_edit_links, allow_failure=False):
    """Pass a list with forwarder names. Those will be deleted.

    Get a list of links to pass from : get_forwarders_edit_links_by_regex.
    """
    for forwarder_link in list_of_forwarder_edit_links:
        delete_forwarder_by_edit_link(forwarder_link, allow_failure)


def delete_forwarder_by_edit_link(edit_link, allow_failure=False):
    """Delete forwarder behind the listbuilding edit link."""
    pytest.selenium_driver.get(edit_link)

    sh.set_wait_timeout(10)
    try:
        sh.click_by_xpath("click the delete button at bottom of page", "//*[@id='edit-modaldeleteconfirmtrigger']")

        time.sleep(1)
        sh.click_by_xpath("Confirm deletion by click on 'Löschen'", "//*[@id='edit-submit']")

        kh.wait_modal_dialog_disappear()
        sh.reset_wait_timeout()
        kh.close_success_alert()
    except:
        sh.reset_wait_timeout()
        if not allow_failure:
            raise
        pass


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

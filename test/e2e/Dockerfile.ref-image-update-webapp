FROM jazzdd/alpine-flask

ARG DOCKER_HOST_USR
ARG DOCKER_HOST_USR_NAME
ARG DOCKER_HOST_USR_GROUP_NAME
ARG DOCKER_HOST_USR_GROUP

RUN apk --no-cache add shadow

# On MacOS users are sometimes in gid 20 (staff).
# This is the dialout group id in alpine.
# To allow re-assign of gid for the nginx below to gid 20
#          we need to change the gid for dialout
# To avoid side effects, we remove and re-assign the dialout group
# RUN gpasswd -d root dialout
RUN groupmod -g 9000 dialout
# RUN usermod -aG dialout root

RUN usermod -u ${DOCKER_HOST_USR} nginx
RUN groupmod -g ${DOCKER_HOST_USR_GROUP} nginx
RUN find / -user 100 -exec chown -h ${DOCKER_HOST_USR} {} \;
RUN find / -group 101 -exec chgrp -h ${DOCKER_HOST_USR_GROUP} {} \;
RUN usermod -g ${DOCKER_HOST_USR_GROUP} nginx

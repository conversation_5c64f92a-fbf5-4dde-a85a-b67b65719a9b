import time

from flask import Flask, jsonify, request, redirect, url_for
from flask_cors import cross_origin
from celery import Celery

from flask_swagger_ui import get_swaggerui_blueprint

import traceback
import json
import subprocess
import os
import re
import glob
import shutil
import boto3

from . import script_creator as sc
from . import dependencies as deps
from . import zephyr as zephyr
from . import aws_secret_manager as aws_sm


app = Flask(__name__)
app.config.broker_url = 'amqp://rmq_record_replay:rmq_record_replay@rabbitmq-e2e-rr:5672/my_vhost'
app.config.result_backend = 'redis://redis-e2e-rr:6379/0'
app.config.imports = ['test.e2e.record_replay.flask_app']
app.config.task_default_delivery_mode = 'persistent'
app.config.broker_connection_retry = True
app.config.broker_connection_max_retries = 0
app.config.broker_connection_timeout = 120
app.config.result_expires = 0
app.config.task_track_started = True

celery_app = Celery(app.name,
                    broker=app.config.broker_url,
                    backend=app.config.result_backend)
celery_app.conf.update(app.config)

list_of_commands = []
current_test = {}

SWAGGER_URL = '/api'  # URL for exposing Swagger UI (without trailing '/')
API_URL = '/static/swagger.yaml'  # Our API url (can of course be a local resource)

swaggerui_blueprint = get_swaggerui_blueprint(
    SWAGGER_URL,  # Swagger UI static files will be mapped to '{SWAGGER_URL}/dist/'
    API_URL,
    config={  # Swagger UI config overrides
        'app_name': "Backend API - e2e test webapp",
        'url': "http://localhost:5006/static/swagger.yaml"
    },
)
app.register_blueprint(swaggerui_blueprint, url_prefix=SWAGGER_URL)


class TaskFailure(Exception):
    pass


def get_task_result(task_id: str) -> dict:
    return_bubble = {"state": "UNDEFINED",
                     "info": {
                         "log": ""
                     }}
    result = celery_app.backend.get_result(task_id)
    if result:
        return_bubble["info"] = result
    task_state = celery_app.backend.get_state(task_id)
    if task_state:
        return_bubble["state"] = task_state
    return return_bubble


def trigger_cli_proc(cmd):
    """Trigger cmd, return stdout/err as string when finished."""

    # run against local stack
    command_line_process = subprocess.Popen(
        cmd,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT
    )

    all_lines = ""
    for line in iter(command_line_process.stdout.readline, b''):
        last_line = line
        last_line.strip()
        last_line = last_line.decode('utf-8')
        all_lines = all_lines + last_line

    command_line_process.communicate()
    command_line_process.wait()

    return all_lines


def trigger_cli_proc_for_celtask(self, cmd):
    """Trigger cmd, update celery states and return log when finished."""
    # run against local stack
    command_line_process = subprocess.Popen(
        cmd,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
    )

    all_lines = ""
    for line in iter(command_line_process.stdout.readline, b''):
        last_line = line
        last_line.strip()
        last_line = last_line.decode('utf-8')
        all_lines = all_lines + last_line
        self.update_state(state='PROGRESS',
                          meta={'log': json.dumps(all_lines)})

    command_line_process.communicate()
    command_line_process.wait()
    result = "success"
    if command_line_process.returncode != 0:
        result = "failed"

    self.update_state(state='SUCCESS',
                      meta={'log': json.dumps(all_lines),
                            'result': result,
                            'returncode': command_line_process.returncode})
    return {'log': json.dumps(all_lines),
            'result': result,
            'returncode': command_line_process.returncode}


def eval_celtask_state(celtask_obj):
    """Evaluate celery task states and return appropriate json."""
    state = "UNDEFINED"
    if celtask_obj.state:
        state = celtask_obj.state
    info = ""
    if celtask_obj.info:
        info = celtask_obj.info

    response = {'state': state,
                'info': info}
    return jsonify(response)


def get_test_dependencies(test_id):
    return deps.get_dependencies(test_id)


def upload_screenshots_and_update_json(test_id):
    """Upload screenshots to S3 and update JSON with screenshot URLs."""
    # Set recordings dir
    recordings_dir = os.path.join(os.getcwd(), 'test/e2e/tests/recordings')
    # Get the json file for the given test_id
    recording_json_files = glob.glob(recordings_dir + f"/**/*_{test_id}.json", recursive=True)

    if not recording_json_files:
        return False

    recording_json_file = recording_json_files[0]
    with open(recording_json_file, 'r') as recording_json_fh:
        recording_json = json.load(recording_json_fh)

    for step in recording_json.get("recordingSteps", []):
        screenshot_data = step.get("screenshot")
        timestamp = step.get("timestamp")
        if screenshot_data and timestamp:
            filename = f"{timestamp}.jpg"
            screenshot_url = upload_jpg_data_to_s3(test_id, screenshot_data, filename)
            if screenshot_url:
                step["screenshotUrl"] = screenshot_url
                del step["screenshot"]

    # Save the updated JSON back to the file
    with open(recording_json_file, 'w') as recording_json_fh:
        json.dump(recording_json, recording_json_fh, indent=2)

    return True


def upload_jpg_data_to_s3(test_id, image_data_from_json, filename):
    """Upload image to S3."""
    aws_secretmanager = aws_sm.SecretManager()
    s3_access_key = aws_secretmanager.get_secret("e2err-tool-s3-accesskey", None)
    s3_secret_key = aws_secretmanager.get_secret("e2err-tool-s3-secretkey", None)
    s3_bucket_name = "kt-e2e-assets"
    s3_region = "eu-west-1"

    base_folder_in_s3_bucket = "e2e-rr/screenshots"
    base_folder_local = os.path.join(os.getcwd(), 'test/e2e/tests/recordings/screenshots')
    # "screenshotUrl": "<filename>"
    # Example path for local: base_folder_local + "/<machineName>/steps/<filename>"

    # Create local image path
    local_image_path = os.path.join(base_folder_local, "steps", f"{test_id}", filename)
    os.makedirs(os.path.dirname(local_image_path), exist_ok=True)

    image_data_bytes = bytes(image_data_from_json)

    # Write image data to local file
    with open(local_image_path, 'wb') as img_file:
        img_file.write(image_data_bytes)

    object_path = f"{base_folder_in_s3_bucket}/steps/{test_id}/{filename}"
    # Return the S3 URL -> e.g. https://kt-e2e-assets.s3.eu-west-1.amazonaws.com/e2e-rr/screenshots/steps/29284240202/1729152196770.jpg
    s3_url = f"https://{s3_bucket_name}.s3.{s3_region}.amazonaws.com/{object_path}"
    try:
        # Upload to S3
        s3 = boto3.client('s3', aws_access_key_id=s3_access_key, aws_secret_access_key=s3_secret_key, region_name=s3_region)
        s3.upload_file(local_image_path, s3_bucket_name, object_path)

        return s3_url
    except Exception as e:
        app.logger.error(f"Failed to upload to S3 {s3_url}: {e}")
        return None


@app.route('/')
@cross_origin()
def home():
    return redirect(request.base_url + "api", code=302)


@app.route('/cancel_task', methods=['POST'])
@cross_origin()
def cancel_task():
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    post_data = request.get_json()
    if not post_data or "taskID" not in post_data:
        return jsonify({"Parameters missing.": "Please POST with 'task_id' in json body.."}), 400
    task_id = post_data.get("taskID")
    celery_app.control.revoke(task_id,
                              terminate=True)
    return jsonify({"state": "SUCCESS",
                    "info": {"log": "Killed task with ID. " + task_id}}), 200

@app.route('/docker_stats', defaults={'task_id': ""}, methods=['POST'])
@app.route('/docker_stats/<task_id>', methods=['GET'])
@cross_origin()
def docker_stats(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        task = celtask_docker_stats.apply_async()
        return jsonify({'taskID': task.id}), 202

    elif request.method == 'GET':
        return eval_celtask_state(celtask_docker_stats.AsyncResult(task_id))


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_docker_stats(self):
    """Background task for retrieving docker stats."""
    docker_stats_cmd = "docker ps -a; docker network inspect kt-e2e-net"
    return trigger_cli_proc_for_celtask(self, docker_stats_cmd)


@app.route('/browserstack_local', defaults={'task_id': ""}, methods=['POST'])
@app.route('/browserstack_local/<task_id>', methods=['GET'])
@cross_origin()
def browserstack_local(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        post_data = request.get_json()
        if "browserstackUser" not in post_data or "browserstackToken" not in post_data:
            return jsonify({"Parameters missing.": "Please POST with 'browserstackUser' and 'browserstackToken' in json body.."}), 400
        browserstack_user = post_data.get("browserstackUser")
        browserstack_token = post_data.get("browserstackToken")
        task = celtask_browserstack_local.apply_async([browserstack_user,
                                                       browserstack_token])
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return eval_celtask_state(celtask_browserstack_local.AsyncResult(task_id))


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_browserstack_local(self, browserstack_user, browserstack_token):
    """Background task for startup browserstack local tunnel."""
    tunnel_start_cmd = "cd test/e2e && make bs-local-start-tunnel BROWSERSTACK_USER=" + browserstack_user + " BROWSERSTACK_TOKEN=" + browserstack_token
    return trigger_cli_proc_for_celtask(self, tunnel_start_cmd)


@app.route('/browserstack_session/<browser>', methods=['GET'])
@cross_origin()
def browserstack_session(browser):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    try:
        filename = "bs.session." + browser + ".json"
        container_filepath = "/" + filename
        host_filepath = os.path.join(os.getcwd(), 'test/e2e/browserstack_sessions/' + filename)
        os.system(
            "docker cp $(docker ps -a | awk '/" + browser + "_tester_run/' | awk '{print $1}'):" + container_filepath + " " + host_filepath)
        with open(host_filepath, 'r') as bs_session_info:
            bs_session_data = json.load(bs_session_info)
            app.logger.info(bs_session_data)
            return bs_session_data
    except Exception as e:
        return jsonify({"state": "FAILURE",
                        "info": {"log": "No browserstack session info available. " + str(e) + " ## " + traceback.format_exc()}}), 500


@app.route('/test_dependencies/<test_id>', methods=['GET'])
@cross_origin()
def test_dependencies(test_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    return_json = {"dependencyChain": get_test_dependencies(test_id)}
    app.logger.info(return_json)

    return jsonify(return_json)


@app.route('/tests', methods=['GET'])
@cross_origin()
def tests():
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """

    # Set recordings dir
    recordings_dir = os.path.join(os.getcwd(), 'test/e2e/tests/recordings')
    # Get all json files

    recording_json_files = glob.glob(recordings_dir + "/**/*.json",
                                     recursive=True)
    # return json array with all json contents of above json files
    # Every element in list is content of a json file
    invalid_json_format = []
    json_array = []
    for json_file in recording_json_files:
        with open(json_file, 'r') as recording_json_fh:
            recording_json = json.load(recording_json_fh)
        try:
            app.logger.info(json_file)
            app.logger.info(recording_json["id"])
            test_case_list_info = {"id": recording_json["id"],
                                   "version": recording_json.get("version", ""),
                                   "description": recording_json["description"],
                                   "displayName": recording_json["displayName"],
                                   "machineName": recording_json["machineName"],
                                   "dependencyChain": get_test_dependencies(recording_json["id"]),
                                   "repoReady": recording_json.get("repoReady", False),
                                   "pipelineReady": recording_json.get("pipelineReady", False),
                                   "zephyrTest": recording_json.get("zephyrTest", ""),
                                   "zephyrTestUrl": recording_json.get("zephyrTestUrl", "")}
            json_array.append(test_case_list_info)
            app.logger.info(test_case_list_info)
        except:
            invalid_json_format.append(json_file)

    return jsonify(json_array)


@app.route('/repo_folders', methods=['GET'])
@cross_origin()
def repo_folders():
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """

    if request.method == 'GET':
        try:
            recordings_dir = os.path.join(os.getcwd(), 'test/e2e/tests/recordings')
            all_files = glob.glob(f"{recordings_dir}/**",
                                  recursive=True)
            folders_under_repo_dir = []
            for folder in all_files:
                curr_folder_match = re.search(r"repo\/(.+?)$",
                                              folder)
                if curr_folder_match:
                    curr_folder = curr_folder_match.group(1)
                    curr_folder = re.sub(r'\/test_.+?[0-9]{9,15}.+?$', '', curr_folder)
                    if curr_folder not in folders_under_repo_dir:
                        folders_under_repo_dir.append(curr_folder)

            return jsonify(folders_under_repo_dir)
        except Exception as e:
            return jsonify({"state": "FAILURE",
                            "info": {"log": "Failed by retrieving repo folders. " + str(e) + " ## " + traceback.format_exc()}}), 500


@app.route('/zephyr_test_create_empty', methods=['POST'])
@cross_origin()
def zephyr_test_create_empty():
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    try:
        data = request.get_json()
        app.logger.info("/test: json data")
        app.logger.info(data)

        test_recording_id = str(data["id"])
        tc_type = data["tcType"]

        # Set recordings dir
        kt_relative_recordings_path = 'test/e2e/tests/recordings'
        recordings_dir = os.path.join(os.getcwd(), kt_relative_recordings_path)
        # Get all json files

        recording_json_files = glob.glob(recordings_dir + "/**/*_" + test_recording_id,
                                         recursive=True)

        # If recording found, we load the json content.
        # Then we delete the whole folder and re-create it new.
        if recording_json_files:
            found_recording_path = recording_json_files[0]
            recording_json_file = glob.glob(f"{recordings_dir}/**/*_{test_recording_id}/*_{test_recording_id}.json",
                                            recursive=True)
            with open(recording_json_file[0], 'r') as recording_json_fh:
                recording_json = json.load(recording_json_fh)

            repo_ready_setting = recording_json.get("repoReady", False)
            repo_path_setting = recording_json.get("repoPath", "")

            if not repo_ready_setting or not repo_path_setting:
                return jsonify({"conflict": "You want to create an empty zephyr TC, but the given recording json has not set repoReady=True and repoPath to a valid path."}), 400

            zephyr_handler = zephyr.ZEPHYR()

            name = recording_json.get("machineName", None)

            try:
                new_test_key = zephyr_handler.create_empty_recording_tc(repo_path_setting, tc_type, name)
            except Exception as e:
                return jsonify({"state": "FAILURE",
                                "info": {"log": "Creating zephyr test failed. " + str(e) + " ## " + traceback.format_exc()}}), 500
            return jsonify({"id": data["id"],
                            "zephyrTest": new_test_key,
                            "zephyrTestUrl": f"https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-{new_test_key}"}), 200

        else:
            return jsonify({"testCaseNotExisting": f"Test Case with id {test_recording_id} not existing."}), 400
    except Exception as e:
        return jsonify({"state": "FAILURE",
                        "info": {"log": "Creating a test in zephyr failed. " + str(e) + " ## " + traceback.format_exc()}}), 500


@app.route('/zephyr_test_assign', methods=['POST'])
@cross_origin()
def zephyr_test_assign():
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """

    try:
        data = request.get_json()
        app.logger.info("/test: json data")
        app.logger.info(data)

        test_recording_id = str(data["id"])
        zephyr_test_id = data.get("zephyrTest", "")

        if zephyr_test_id == "" or not re.search(r'^T[0-9]{3,5}$', zephyr_test_id):
            return jsonify({"invalidParam": "Pass a valid zephyr test id e.g. 'T123' (Internally we access via DEV-T123)."}), 400

        # Set recordings dir
        kt_relative_recordings_path = 'test/e2e/tests/recordings'
        recordings_dir = os.path.join(os.getcwd(), kt_relative_recordings_path)
        # Get all json files

        recording_json_files = glob.glob(recordings_dir + "/**/*_" + test_recording_id,
                                         recursive=True)

        # If recording found, we load the json content.
        # Then we delete the whole folder and re-create it new.
        if recording_json_files:
            found_recording_path = recording_json_files[0]
            recording_json_file = glob.glob(f"{recordings_dir}/**/*_{test_recording_id}/*_{test_recording_id}.json",
                                            recursive=True)
            with open(recording_json_file[0], 'r') as recording_json_fh:
                recording_json = json.load(recording_json_fh)

            repo_ready_setting = recording_json.get("repoReady", False)
            repo_path_setting = recording_json.get("repoPath", "")

            if not repo_ready_setting or repo_path_setting == "":
                return jsonify({"repoTestInconsitency": "You want to set a zephyr reference, but the test has either no repoReady=True or a valid repoPath setting."}), 400

            recording_json["zephyrTest"] = zephyr_test_id
            recording_json["zephyrTestUrl"] = f"https://klicktipp.atlassian.net/projects/DEV?selectedItem=com.atlassian.plugins.atlassian-connect-plugin:com.kanoah.test-manager__main-project-page#!/testCase/DEV-{zephyr_test_id}"

            # double-check the rmtree!
            if re.search(re.escape(kt_relative_recordings_path), found_recording_path):
                shutil.rmtree(found_recording_path)
        else:
            return jsonify({"testCaseNotExisting": f"Test Case with id {test_recording_id} not existing."}), 400

        machine_name = recording_json["machineName"]
        recording_dir = os.path.join(recordings_dir, "repo", repo_path_setting, machine_name)
        os.makedirs(recording_dir,
                    exist_ok=True)
        f = open(recording_json_file[0], "x")  # create and open for writing
        f.write(json.dumps(recording_json, indent=2))
        f.close()

        return jsonify({"updatedZephyrTest": f"{machine_name}, zephyrTest={zephyr_test_id}"}), 200
    except Exception as e:
        return jsonify({"state": "FAILURE",
                        "info": {"log": "Updating for pipelineReady and saving of recording in the backend failed. " + str(e) + " ## " + traceback.format_exc()}}), 500


@app.route('/pipeline_ready', methods=['POST'])
@cross_origin()
def pipeline_ready():
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """

    try:
        data = request.get_json()
        app.logger.info("/test: json data")
        app.logger.info(data)

        test_recording_id = str(data["id"])
        pipeline_ready_flag = data["pipelineReady"]

        # Set recordings dir
        kt_relative_recordings_path = 'test/e2e/tests/recordings'
        recordings_dir = os.path.join(os.getcwd(), kt_relative_recordings_path)
        # Get all json files

        recording_json_files = glob.glob(recordings_dir + "/**/*_" + test_recording_id,
                                         recursive=True)

        # If recording found, we load the json content.
        # Then we delete the whole folder and re-create it new.
        if recording_json_files:
            found_recording_path = recording_json_files[0]
            recording_json_file = glob.glob(f"{recordings_dir}/**/*_{test_recording_id}/*_{test_recording_id}.json",
                                            recursive=True)
            with open(recording_json_file[0], 'r') as recording_json_fh:
                recording_json = json.load(recording_json_fh)

            repo_ready_setting = recording_json.get("repoReady", False)
            repo_path_setting = recording_json.get("repoPath")

            if pipeline_ready_flag and (not repo_ready_setting or not repo_path_setting):
                return jsonify({"invalidParam": "You want to set pipelineReady=True, but key repoReady in original recording json was not set or set to False. Or maybe 'repoPath' is not set."}), 400

            recording_json["pipelineReady"] = pipeline_ready_flag
            recording_json["repoReady"] = repo_ready_setting

            # double-check the rmtree!
            if re.search(re.escape(kt_relative_recordings_path), found_recording_path):
                shutil.rmtree(found_recording_path)
        else:
            return jsonify({"testCaseNotExisting": f"Test Case with id {test_recording_id} not existing."}), 400

        machine_name = recording_json["machineName"]
        recording_dir = os.path.join(recordings_dir, "repo", repo_path_setting, machine_name)
        os.makedirs(recording_dir,
                    exist_ok=True)
        f = open(recording_json_file[0], "x")  # create and open for writing
        f.write(json.dumps(recording_json, indent=2))
        f.close()

        return jsonify({"updatedPipelineReady": f"{machine_name}, repoReady={repo_ready_setting}, pipelineReady={pipeline_ready_flag}"}), 200
    except Exception as e:
        return jsonify({"state": "FAILURE",
                        "info": {"log": "Updating for pipelineReady and saving of recording in the backend failed. " + str(e) + " ## " + traceback.format_exc()}}), 500


@app.route('/repo_ready', methods=['POST'])
@cross_origin()
def repo_ready():
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """

    try:
        data = request.get_json()
        app.logger.info("/test: json data")
        app.logger.info(data)

        test_recording_id = str(data["id"])
        repo_ready_flag = data["repoReady"]
        repo_path_from_post = data.get("repoPath", '')

        # Set recordings dir
        kt_relative_recordings_path = 'test/e2e/tests/recordings'
        recordings_dir = os.path.join(os.getcwd(), kt_relative_recordings_path)
        # Get all json files

        recording_json_files = glob.glob(recordings_dir + "/**/*_" + test_recording_id,
                                         recursive=True)

        # If recording found, we load the json content.
        # Then we delete the whole folder and re-create it new.
        if recording_json_files:
            found_recording_path = recording_json_files[0]
            recording_json_file = glob.glob(f"{recordings_dir}/**/*_{test_recording_id}/*_{test_recording_id}.json",
                                            recursive=True)
            with open(recording_json_file[0], 'r') as recording_json_fh:
                recording_json = json.load(recording_json_fh)
            # repoPath must be set, in json or with this call in the body
            if repo_path_from_post and repo_path_from_post != '':
                recording_json["repoPath"] = repo_path_from_post
            repo_path = recording_json["repoPath"]

            if repo_ready_flag and repo_path == '':
                return jsonify({"invalidParam": "You want to set repoReady=True, but key repoPath in original recording json was not set and the here passed repoPath is empty or none."}), 400

            # double-check the rmtree!
            if re.search(re.escape(kt_relative_recordings_path), found_recording_path):
                shutil.rmtree(found_recording_path)
        else:
            return jsonify({"testCaseNotExisting": f"Test Case with id {test_recording_id} not existing."}), 400
        # Replace all special chars by an underscore, for machine_name -> later used as py test module name
        # The original display name in the recording info will remain as it is

        # (Re-)create the tc folder and json tc info file in the correct folder.
        # If repoReady : in the repoPath under tests/recordings/repo (git auto-add)
        # else: directly under the tests/recordings folder
        recording_json["repoReady"] = repo_ready_flag

        machine_name = recording_json["machineName"]

        if repo_ready_flag:
            recording_dir = os.path.join(recordings_dir, "repo", repo_path, machine_name)
        else:
            recording_dir = os.path.join(recordings_dir, machine_name)

        recording_json_file = os.path.join(recording_dir, machine_name + ".json")
        os.makedirs(recording_dir,
                    exist_ok=True)
        f = open(recording_json_file, "w")  # create and open for writing
        f.write(json.dumps(recording_json, indent=2))
        f.close()

        # The upload is only done when the recording is shared with others by adding to the repo
        # So, to re-upload images, just re-trigger the repoReady endpoint and set repoReady = True
        if repo_ready_flag:
            upload_screenshots_and_update_json(test_recording_id)

        return jsonify({"updatedRepoReady": f"{machine_name}, repoReady={repo_ready_flag}, repoPath={repo_path}"}), 200
    except Exception as e:
        return jsonify({"state": "FAILURE",
                        "info": {"log": "Updating for repoReady and saving of recording in the backend failed. " + str(e) + " ## " + traceback.format_exc()}}), 500


@app.route('/test', defaults={'test_id': ""}, methods=['POST'])
@app.route('/test/<test_id>', methods=['GET'])
@cross_origin()
def test(test_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """

    if request.method == 'GET':
        try:
            recordings_dir = os.path.join(os.getcwd(), 'test/e2e/tests/recordings')
            # Get all json files
            glob_str = recordings_dir + "/**/*_" + str(test_id) + ".json"
            recording_json_file = glob.glob(glob_str,
                                            recursive=True)

            if recording_json_file:
                with open(recording_json_file[0], 'r') as recording_json_info:
                    recording_as_json = json.load(recording_json_info)
                    app.logger.info(recording_as_json)

                return jsonify(recording_as_json)
            else:
                return jsonify({"state": "FAILURE",
                                "info": {"log": "Recording json file not found : " + glob_str}}), 500
        except Exception as e:
            return jsonify({"state": "FAILURE",
                            "info": {"log": "Failed by retrieving test infos. " + str(e) + " ## " + traceback.format_exc()}}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()
            app.logger.info("/test: json data")
            app.logger.info(data)

            test_recording_id = str(data["id"])

            # update or creation with repoReady without repoPath is not allowed
            if data.get("repoReady", False) is True and data.get("repoPath", '') == '':
                return jsonify({"invalidParam": "You want to set repoReady=True, but key repoPath empty or none."}), 400

            # Set recordings dir
            kt_relative_recordings_path = 'test/e2e/tests/recordings'
            recordings_dir = os.path.join(os.getcwd(), kt_relative_recordings_path)
            # Get all json files

            recording_json_files = glob.glob(recordings_dir + "/**/*_" + test_recording_id,
                                             recursive=True)

            # If recording exists with ID, we assume, it is an update.
            # We delete the whole folder and re-create it new.
            if recording_json_files:
                found_recording_path = recording_json_files[0]

                # double-check the rmtree!
                if re.search(re.escape(kt_relative_recordings_path), found_recording_path):
                    shutil.rmtree(found_recording_path)

            # Replace all special chars by an underscore, for machine_name -> later used as py test module name
            # The original display name in the recording info will remain as it is
            display_name = re.sub(r'[^ÄÖÜäöüA-Za-z0-9_]', "_", data["displayName"].strip().lower())
            display_name = re.sub(r'Ä|ä', "ae", display_name)
            display_name = re.sub(r'Ö|ö', "oe", display_name)
            display_name = re.sub(r'Ü|ü', "ue", display_name)
            machine_name = re.sub(r'^(test)?( |_)?', 'test_', display_name, re.IGNORECASE)
            machine_name += '_' + test_recording_id
            data["machineName"] = machine_name

            # Defaulting zephyr test case and pipelineReady if not set
            pipeline_ready_flag = data.get("pipelineReady", False)
            zephyr_test_flag = data.get("zephyrTest", "")
            data["pipelineReady"] = pipeline_ready_flag
            data["zephyrTest"] = zephyr_test_flag

            # (Re-)create the tc folder and json tc info file in the correct folder.
            # If repoReady : in the repoPath under tests/recordings/repo (git auto-add)
            # else: directly under the tests/recordings folder
            repo_ready_flag = data.get("repoReady", False)
            repo_path = data.get("repoPath", '')
            data["repoReady"] = repo_ready_flag
            data["repoPath"] = repo_path
            if repo_ready_flag:
                recording_dir = os.path.join(recordings_dir, "repo", repo_path, machine_name)
            else:
                recording_dir = os.path.join(recordings_dir, machine_name)

            recording_json_file = os.path.join(recording_dir, machine_name + ".json")
            os.makedirs(recording_dir,
                        exist_ok=True)
            f = open(recording_json_file, "x")  # create and open for writing
            f.write(json.dumps(data, indent=2))
            f.close()
            return jsonify({"machineName": machine_name}), 200
        except Exception as e:
            return jsonify({"state": "FAILURE",
                            "info": {"log": "Saving of recording in the backend failed. " + str(e) + " ## " + traceback.format_exc()}}), 500


@app.route('/script', methods=['POST'])
@cross_origin()
def script():
    global current_test

    if request.method == 'POST':
        if current_test != {}:

            code = sc.get_script_code(current_test)

            script_path = "test/e2e/tests/test_recordings/local/test_" + current_test["name"] + ".py"
            if os.path.exists(script_path):
                os.remove(script_path)
            f = open(script_path, "x")  # create and open for writing

            f.write(code)

            f.close()

            return jsonify({'log': 'Script file created : ' + script_path})
        else:
            return jsonify({"log": "Please POST a test from extension before creating the script."}), 400
    else:
        return jsonify({"log": "Request method not supported: " + request.method}), 400


@app.route('/exec_test_parallel', defaults={'task_id': ""}, methods=['POST'])
@app.route('/exec_test_parallel/<task_id>', methods=['GET'])
@cross_origin()
def exec_test_parallel(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'GET':
        return get_task_result(task_id), 200
    elif request.method == 'POST':
        post_data = request.get_json()
        app.logger.info(post_data)
        if not post_data:
            return jsonify({"error": "No parameters passed to POST"}), 405

        mandatory_params = list([
            'appLocation',  # remote, local
            'browsers',  # chrome,firefox,...
            'testCase',  # machine name e.g. test_create_formular_16284938840
            'prepare',  # true, false
            'caching',  # true, false
            'forceNewDumps',  # true, false
            'domain',  # local, staging (for MVP, we determine domain by execUser. given -> check whether it should be local or staging)
            'execUser',  # pass at least 'none', staging: e2e-test-record-replay-<suffix> (..-simon), local: e2e-test-recorder, user not given => local: e.g. e2e-test-replay-chrome
            'execUserPwd',  # pass at least 'none'
            'browserstackUser',  # only local relevant
            'browserstackToken',  # only local relevant
            'debug'  # true, false
        ])
        missing_params = list()
        for param in mandatory_params:
            if param not in post_data:
                missing_params.append(param)
        if missing_params:
            return jsonify({"missingParams": ','.join(missing_params)}), 400

        app_location = post_data["appLocation"]
        browsers = post_data["browsers"]
        browsers = ','.join(browsers)  # -> "chrome,firefox,..."
        test_case = post_data["testCase"]
        prepare = post_data["prepare"]
        caching = post_data["caching"]
        force_new_dumps = post_data["forceNewDumps"]
        domain = post_data["domain"]
        exec_user = post_data["execUser"]
        exec_user_pwd = post_data["execUserPwd"]
        browserstack_user = post_data["browserstackUser"]
        browserstack_token = post_data["browserstackToken"]
        debug = post_data["debug"]
        # optional parameters
        testsession = post_data.get("testSession")
        screenshots = post_data.get("screenshots")
        retry = post_data.get("retry")

        app.logger.info("app_location       " + app_location)
        app.logger.info("browsers           " + ','.join(browsers))
        app.logger.info("test_case          " + test_case)
        app.logger.info("prepare            " + str(int(prepare)))
        app.logger.info("caching            " + str(int(caching)))
        app.logger.info("force_new_dumps    " + str(int(force_new_dumps)))
        app.logger.info("domain             " + domain)
        app.logger.info("exec_user          " + exec_user)
        app.logger.info("browserstack_user  " + browserstack_user)
        app.logger.info("browserstack_token " + browserstack_token)
        app.logger.info("debug              " + str(int(debug)))

        if retry and retry != "":
            app.logger.info("retry              " + str(int(retry)))
        else:
            retry = None
            app.logger.info("retry is not defined. Default is no fast retries.")

        if testsession and testsession != "":
            app.logger.info("testsession        " + testsession)
        else:
            testsession = None
            app.logger.info("No test session identifier passed ('testSession').")

        if screenshots and screenshots != "":
            allowed_flags_str = "compareAndFail|compareAndAllowFail|newScreenshots"
            allowed_flags = allowed_flags_str.split("|")
            error_msg_wrong_params = f"'screenshots' value invalid. You can combine with '|' the following flags {allowed_flags_str} - allowFail flag overwrites compareAndFail"
            screenshots_flags = screenshots.split("|")
            if screenshots_flags:
                for flag in screenshots_flags:
                    if flag not in allowed_flags:
                        return jsonify({"wrongParams": error_msg_wrong_params}), 400
            else:
                return jsonify({"wrongParams": error_msg_wrong_params}), 400
            app.logger.info("screenshots        " + screenshots)
        else:
            screenshots = None
            app.logger.info("No screenshot mode.")

        task = celtask_exec_test_parallel.apply_async([app_location,
                                                       browsers,
                                                       test_case,
                                                       prepare,
                                                       caching,
                                                       force_new_dumps,
                                                       domain,
                                                       exec_user,
                                                       exec_user_pwd,
                                                       browserstack_user,
                                                       browserstack_token,
                                                       debug,
                                                       testsession,
                                                       screenshots,
                                                       retry])

        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202

    else:
        return jsonify({"state": "FAILURE",
                        "info": {"log": "Endpoint only accepts POST and GET. It was " + request.method}}), 405


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_exec_test_parallel(self,
                               app_location,
                               browsers,
                               test_case,
                               prepare,
                               caching,
                               force_new_dumps,
                               domain,
                               exec_user,
                               exec_user_pwd,
                               browserstack_user,
                               browserstack_token,
                               debug,
                               testsession,
                               screenshots,
                               retry):
    """Background task creating temp test set and executing the test."""
    makefile_dir_change = "cd test/e2e"
    make_target = "tests-e2e-parallel"
    app_location_param = "APP_LOCATION=" + app_location
    browsers_param = "BROWSERS=" + browsers
    test_case_param = "TESTCASE=" + test_case
    prepare_param = "PREPARE=" + str(int(prepare))
    caching_param = "CACHING=" + str(int(caching))
    force_new_dumps_param = "FORCE_NEW_DUMPS=" + str(int(force_new_dumps))
    domain_param = "KLICKTIPP_URL=" + domain
    exec_user_param = "EXEC_USER=" + exec_user
    exec_user_pwd_param = "EXEC_USER_PWD=" + exec_user_pwd
    browserstack_user_param = "BROWSERSTACK_USER=" + browserstack_user
    browserstack_token_param = "BROWSERSTACK_TOKEN=" + browserstack_token
    debug_param = "DEBUG=" + str(int(debug))
    if testsession:
        testsession_param = "TESTSESSION=" + testsession
    else:
        testsession_param = ""

    new_screenshots_param = "E2E_RR_NEW_SCREENSHOTS=0"
    compare_and_allow_fail_param = "E2E_RR_SCREENS_COMPARE_AND_ALLOW_FAIL=0"
    compare_and_fail_param = "E2E_RR_SCREENS_COMPARE_AND_FAIL=0"

    if screenshots and screenshots != "":
        screenshots_flags = screenshots.split("|")
        if "newScreenshots" in screenshots_flags:
            new_screenshots_param = "E2E_RR_NEW_SCREENSHOTS=1"

        if "compareAndAllowFail" in screenshots_flags:
            compare_and_allow_fail_param = "E2E_RR_SCREENS_COMPARE_AND_ALLOW_FAIL=1"

        if "compareAndFail" in screenshots_flags:
            compare_and_fail_param = "E2E_RR_SCREENS_COMPARE_AND_FAIL=1"

    if retry and retry != "":
        retry_param = "RETRY=" + str(int(retry))
    else:
        retry_param = ""

    make_call = "make" + " " + \
                make_target + " " + \
                app_location_param + " " + \
                browsers_param + " " + \
                test_case_param + " " + \
                prepare_param + " " + \
                caching_param + " " + \
                force_new_dumps_param + " " + \
                domain_param + " " + \
                exec_user_param + " " + \
                exec_user_pwd_param + " " + \
                browserstack_user_param + " " + \
                browserstack_token_param + " " + \
                debug_param + " " + \
                testsession_param + " " + \
                new_screenshots_param + " " + \
                compare_and_allow_fail_param + " " + \
                compare_and_fail_param + " " + \
                retry_param + " "

    make_call = makefile_dir_change + " && " + make_call

    return trigger_cli_proc_for_celtask(self, make_call)


@app.route('/exec_test_parallel_states/', methods=['GET'])
@cross_origin()
def exec_test_parallel_states():
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """

    if request.method == 'GET':
        info_bobble = {}

        pytest_package_dir = os.path.join(os.getcwd(), 'test/e2e/tests/test_recording_temp/local')

        exec_info_filepath = os.path.join(pytest_package_dir, "exec_info.json")
        try:
            with open(exec_info_filepath, "r") as fh:
                exec_info = json.load(fh)
        except Exception as e:
            return jsonify({"state": "FAILURE",
                            "info": {"log": "json from exec_info.json not readable. " + str(e) + " ## " + traceback.format_exc()}}), 500

        cache_exec_info_filepath = os.path.join(pytest_package_dir, "cache_exec_info.json")
        try:
            with open(cache_exec_info_filepath, "r") as fh:
                cache_exec_info = json.load(fh)
        except Exception as e:
            return jsonify({"state": "FAILURE",
                            "info": {"log": "json from cache_exec_info.json not readable. " + str(e) + " ## " + traceback.format_exc()}}), 500

        caching_states_filepath = os.path.join(pytest_package_dir, "caching_states.json")
        try:
            with open(caching_states_filepath, "r") as fh:
                caching_states = json.load(fh)
        except Exception as e:
            return jsonify({"state": "FAILURE",
                            "info": {"log": "json from caching_states.json not readable. " + str(e) + " ## " + traceback.format_exc()}}), 500

        test_cases = []
        for key in cache_exec_info.keys():
            if re.search(r'^test_', key):
                test_cases.append(key)

        browsers = exec_info["browsers"]

        # /home/<USER>/pr/klick-tipp/test/e2e/tests/test_recording_temp/local/chrome.test_create_formular_1651562892
        test_case_states = {}
        for tc in test_cases:
            tc_bobble = {}
            for browser in browsers:
                browser_tc_state = {}
                tc_state_filename = browser + "." + tc
                tc_state_filepath = os.path.join(pytest_package_dir, tc_state_filename)
                for i in range(3):
                    try:
                        with open(tc_state_filepath, "r") as fh:
                            browser_tc_state = json.load(fh)
                        break
                    except:
                        time.sleep(0.1)

                if not browser_tc_state:
                    # file corrupt or was not created
                    browser_tc_state = {"state": "pending",
                                        "testResult": ""}

                tc_bobble[browser] = browser_tc_state

            test_case_states[tc] = tc_bobble

        browser_logs = {}
        for browser in browsers:
            loglines = ""
            browser_log_filepath = os.path.join(pytest_package_dir, browser + ".log")
            loglines = ""
            for i in range(3):
                try:
                    with open(browser_log_filepath, "r") as fh:
                        loglines = fh.read()
                except:
                    time.sleep(0.1)
            browser_logs[browser] = json.dumps(loglines)

        # Order is finally random when returned as json.
        # Except: we put those entries in a list. Then order will be kept.
        info_bobble["execInfo"] = exec_info
        info_bobble["testCaseStates"] = test_case_states
        info_bobble["cacheExecInfo"] = cache_exec_info
        info_bobble["cachingStates"] = caching_states
        info_bobble["logs"] = browser_logs

        return info_bobble

    return jsonify({"state": "FAILURE",
                    "info": {"log": url_for(exec_test_parallel_states) + " only supports GET."}}), 405


@app.route('/exec_test', defaults={'task_id': ""}, methods=['POST'])
@app.route('/exec_test/<task_id>', methods=['GET'])
@cross_origin()
def exec_test(task_id):
    global current_test

    if request.method == 'POST':
        post_data = request.get_json()
        app.logger.info(post_data)
        if "browser" not in post_data:
            return jsonify({"No browser set.": "Please set the browser you want to test against (chrome|edge)."}), 400
        mode = post_data["mode"]
        browser = post_data["browser"]
        env = post_data["env"]
        debug = post_data["debug"]
        if current_test != {}:
            app.logger.info("/script: json data")
            app.logger.info(current_test)
            task = celtask_exec_test.apply_async([current_test,
                                                  mode,
                                                  browser,
                                                  env,
                                                  debug])
            app.logger.info("task id : " + task.id)
            return jsonify({'task_id': task.id}), 202
        else:
            return jsonify({"No Test until now.":
                            "Please POST a test from extension and create the script, before starting the test."}), 400
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 405


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_exec_test(self, test_infos, mode, browser, env, debug):
    """Background task creating temp test set and executing the test."""
    test_module_name = "test_" + test_infos["name"] + ".py"
    test_module_folder = 'test/e2e/tests/test_recordings/local/'
    test_full_path = test_module_folder + test_module_name
    if not os.path.exists(test_module_folder + test_module_name):
        self.update_state(state='FAILURE')
        raise TaskFailure('Test not existing : ' + test_full_path)

    test_set_path = "test/e2e/tests/testsets/recording_temp"
    if os.path.exists(test_set_path):
        os.remove(test_set_path)

    f = open(test_set_path, "x")  # create and open for writing
    f.write("test_recordings/local/" + test_module_name)
    f.close()
    self.update_state(state='PROGRESS',
                      meta={'log': "Created testset file " + test_set_path})

    self.update_state(state='PROGRESS',
                      meta={'log': "Start testset " + test_set_path})

    target = 'tests-e2e'
    if env.lower() == "local":
        target += '-local'

    if mode.lower() == "noscreentest":
        target = target
    elif mode == "screens":
        target += '-screen-tests'
    else:  # mode == "screensfailallow"
        target += '-screen-tests-fail-ignore'

    if browser.lower() != "chrome" and browser != "firefox":
        target = 'bs-' + target
        klicktipp_url = "KLICKTIPP_URL=www-e2e.ktlocal.com"
    else:
        klicktipp_url = ""

    if debug.lower() == "yes":
        debug_flag = "DEBUG=1"
    else:
        debug_flag = ""

    make_cmd_for_trigger_test = 'cd test/e2e/; make ' + target + ' BROWSER=' + browser + ' ' + klicktipp_url + ' ' + debug_flag + ' TESTSET=recording_temp'

    return trigger_cli_proc_for_celtask(self, make_cmd_for_trigger_test)


@app.route('/report_server', defaults={'task_id': ""}, methods=['POST'])
@app.route('/report_server/<task_id>', methods=['GET'])
@cross_origin()
def report_server(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        post_data = request.get_json()
        if "browser" not in post_data:
            return jsonify({"No browser set.": "Please set the browser you want to start the report server for."}), 400
        task = celtask_report_server.apply_async([post_data["browser"]])
        return jsonify({'taskID': task.id}), 202

    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_report_server(self, browser):
    """Background starting ref img update report app."""
    self.update_state(state='PROGRESS',
                      meta={'log': "Check report existence"})
    report_path = "test/e2e/report-" + browser + "/index.html"

    if not os.path.exists(report_path):
        self.update_state(state='FAILURE')
        raise TaskFailure('Report not existing : ' + report_path)

    self.update_state(state='PROGRESS',
                      meta={'log': "Start report app"})
    srv_start_cmd = "cd test/e2e && make ref-image-update-webapp-start BROWSER=" + browser
    return trigger_cli_proc_for_celtask(self, srv_start_cmd)


@app.route('/docker_aws_login', defaults={'task_id': ""}, methods=['POST'])
@app.route('/docker_aws_login/<task_id>', methods=['GET'])
@cross_origin()
def docker_aws_login(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        task = celtask_docker_aws_login.apply_async()
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_docker_aws_login(self):
    """Background task doing the docker aws login."""
    make_cmd_docker_aws_login = 'id; make docker-login'

    return trigger_cli_proc_for_celtask(self, make_cmd_docker_aws_login)


@app.route('/kt_dev_stack', defaults={'task_id': ""}, methods=['POST'])
@app.route('/kt_dev_stack/<task_id>', methods=['GET'])
@cross_origin()
def kt_dev_stack(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        post_data = request.get_json()
        if "mode" not in post_data:
            return jsonify({"No mode set.": "Please POST one of the stack commands 'mode': up|down"}), 400
        mode = post_data['mode']
        if not re.search(r'up|down', mode):
            return jsonify({"Invalid mode.": "Please POST up|down, its : " + mode}), 400
        task = celtask_kt_dev_stack.apply_async([mode])
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_kt_dev_stack(self, stack_cmd):
    """Background task able to up|down|reset the stack (subcmd given as POST json data)."""
    make_cmd_for_trigger_stack_cmd = 'make docker-login && make klicktipp-' + stack_cmd

    return trigger_cli_proc_for_celtask(self, make_cmd_for_trigger_stack_cmd)


@app.route('/kt_stack', defaults={'task_id': ""}, methods=['POST'])
@app.route('/kt_stack/<task_id>', methods=['GET'])
@cross_origin()
def kt_stack(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        post_data = request.get_json()
        if "mode" not in post_data:
            return jsonify({"No mode set.": "Please POST one of the stack commands 'mode': up|down|reset"}), 400
        mode = post_data['mode']
        if not re.search(r'up|down|reset', mode):
            return jsonify({"Invalid mode.": "Please POST up|down|reset, its : " + mode}), 400
        task = celtask_kt_stack.apply_async([mode])
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_kt_stack(self, stack_cmd):
    """Background task able to up|down|reset the stack (subcmd given as POST json data)."""
    make_cmd_for_trigger_stack_cmd = 'cd test/e2e/ && make login && make kt-stack-' + stack_cmd

    return trigger_cli_proc_for_celtask(self, make_cmd_for_trigger_stack_cmd)


@app.route('/pull_local_browser_image', defaults={'task_id': ""}, methods=['POST'])
@app.route('/pull_local_browser_image/<task_id>', methods=['GET'])
@cross_origin()
def pull_local_browser_image(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        post_data = request.get_json()
        if "browser" not in post_data:
            return jsonify({"No browser set.": "Please POST with 'browser' in body set to 'chrome' or 'firefox'."}), 400
        browser = post_data['browser']
        if not re.search(r'chrome|firefox', browser):
            return jsonify({"Invalid browser.": "Please POST 'chrome' or 'firefox', its : " + browser}), 400
        task = celtask_pull_local_browser_image.apply_async([browser])
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_pull_local_browser_image(self, browser):
    """Background task able to up|down|reset the stack (subcmd given as POST json data)."""
    make_cmd_for_trigger_pull_cmd = 'cd test/e2e/ && make pull-browser-image BROWSER=' + browser

    return trigger_cli_proc_for_celtask(self, make_cmd_for_trigger_pull_cmd)


@app.route('/build_executor_base_image', defaults={'task_id': ""}, methods=['POST'])
@app.route('/build_executor_base_image/<task_id>', methods=['GET'])
@cross_origin()
def build_executor_base_image(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        task = celtask_build_executor_base_image.apply_async()
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_build_executor_base_image(self):
    """Background task able to up|down|reset the stack (subcmd given as POST json data)."""
    make_cmd_for_trigger_build_cmd = 'cd test/e2e/ && make build-executor-base-image'

    return trigger_cli_proc_for_celtask(self, make_cmd_for_trigger_build_cmd)


@app.route('/build_executor_final_image', defaults={'task_id': ""}, methods=['POST'])
@app.route('/build_executor_final_image/<task_id>', methods=['GET'])
@cross_origin()
def build_executor_final_image(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        post_data = request.get_json()
        if "browser" not in post_data:
            return jsonify({"No browser set.": "Please POST with 'browser' in body."}), 400
        browser = post_data['browser']
        if not re.search(r'chrome|firefox|safari_ipad|chrome_macos|firefox_win|safari_macos|edge', browser):
            return jsonify({"Invalid browser.": "Please set 'browser' to one of the following: chrome|firefox|safari_ipad|chrome_macos|firefox_win|safari_macos|edge, its : " + browser}), 400
        task = celtask_build_executor_final_image.apply_async([browser])
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_build_executor_final_image(self, browser):
    """Background task able to up|down|reset the stack (subcmd given as POST json data)."""
    make_cmd_for_trigger_build_cmd = 'cd test/e2e/ && make build-executor-final-image BROWSER=' + browser

    return trigger_cli_proc_for_celtask(self, make_cmd_for_trigger_build_cmd)


@app.route('/kt_install', defaults={'task_id': ""}, methods=['POST'])
@app.route('/kt_install/<task_id>', methods=['GET'])
@cross_origin()
def kt_install(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        task = celtask_kt_install.apply_async()
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_kt_install(self):
    """Background task able to up|down|reset the stack (subcmd given as POST json data)."""
    make_cmd_for_install = "cd test/e2e/ && make kt-install-via-drush"

    return trigger_cli_proc_for_celtask(self, make_cmd_for_install)


@app.route('/kt_dev_install', defaults={'task_id': ""}, methods=['POST'])
@app.route('/kt_dev_install/<task_id>', methods=['GET'])
@cross_origin()
def kt_dev_install(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        task = celtask_kt_dev_install.apply_async()
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_kt_dev_install(self):
    """Background task able to up|down|reset the stack (subcmd given as POST json data)."""
    make_cmd_for_install = "make klicktipp-init-drush"

    return trigger_cli_proc_for_celtask(self, make_cmd_for_install)


@app.route('/build_cockpit', defaults={'task_id': ""}, methods=['POST'])
@app.route('/build_cockpit/<task_id>', methods=['GET'])
@cross_origin()
def build_cockpit(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        task = celtask_build_cockpit.apply_async()
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_build_cockpit(self):
    """Background task for building angular cockpit."""
    make_cmd_for_cockpit_build = 'make cockpit-build'

    return trigger_cli_proc_for_celtask(self, make_cmd_for_cockpit_build)


@app.route('/build_email_editor', defaults={'task_id': ""}, methods=['POST'])
@app.route('/build_email_editor/<task_id>', methods=['GET'])
@cross_origin()
def build_email_editor(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        task = celtask_build_email_editor.apply_async()
        app.logger.info("task id : " + task.id)
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_build_email_editor(self):
    """Background task for email editor build."""
    make_cmd_for_email_editor_build = 'make client-klicktipp-build'

    return trigger_cli_proc_for_celtask(self, make_cmd_for_email_editor_build)


@app.route('/test_data_import', defaults={'task_id': ""}, methods=['POST'])
@app.route('/test_data_import/<task_id>', methods=['GET'])
@cross_origin()
def test_data_import(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        post_data = request.get_json()
        if "data_path" not in post_data:
            return jsonify({"No test_data set.": "Please set the test_data you want to import."}), 400
        else:
            data_path = post_data["data_path"]
            task = celtask_test_data_import.apply_async([data_path])
            return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_test_data_import(self, data_path):
    """Background task importing testdata .sql file."""
    make_cmd_for_trigger_import = 'cd test/e2e/; make kt-stack-import-local-test-data TESTDATA_SQL=' + data_path

    return trigger_cli_proc_for_celtask(self, make_cmd_for_trigger_import)


@app.route('/test_data_prep', defaults={'task_id': ""}, methods=['POST'])
@app.route('/test_data_prep/<task_id>', methods=['GET'])
@cross_origin()
def test_data_prep(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        task = celtask_test_data_prep.apply_async()
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_test_data_prep(self):
    """Background task preparing testdata .sql files."""
    make_cmd_data_prep = 'cd test/e2e/; make tests-e2e-local-testdata-prep BROWSER=chrome'

    return trigger_cli_proc_for_celtask(self, make_cmd_data_prep)


@app.route('/test_data_recreate_base', defaults={'task_id': ""}, methods=['POST'])
@app.route('/test_data_recreate_base/<task_id>', methods=['GET'])
@cross_origin()
def test_data_recreate_base(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'POST':
        task = celtask_test_data_recreate_base.apply_async()
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_test_data_recreate_base(self):
    """Background task re-installing kt and creating e2e-test-dev user."""
    make_cmd_for_data_base_recreation = 'cd test/e2e/; make tests-e2e-local-recreate-standard-data BROWSER=chrome'

    return trigger_cli_proc_for_celtask(self, make_cmd_for_data_base_recreation)


@app.route('/test_data_export', defaults={'task_id': ""}, methods=['POST'])
@app.route('/test_data_export/<task_id>', methods=['GET'])
@cross_origin()
def test_data_export(task_id):
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    global current_test

    if request.method == 'POST':
        post_data = request.get_json()
        if "data_path" not in post_data:
            return jsonify({"No data_path set.": "Please POST with data_path set e.g. ready_for_test/my_current_dbdump.sql"}), 400
        data_path = post_data['data_path']
        task = celtask_test_data_export.apply_async([data_path])
        return jsonify({'taskID': task.id}), 202
    elif request.method == 'GET':
        return get_task_result(task_id), 200
    else:
        return jsonify({"Endpoint only accepts POST and GET, but it was ": request.method}), 400


@celery_app.task(ignore_result=False, task_ignore_result=False, bind=True)
def celtask_test_data_export(self, data_path):
    """export db to given filepath (relative to <ktroot>/test/e2e/tests/data/<your-given-path>)."""
    make_cmd_for_data_export = 'cd test/e2e/; make kt-stack-export-local-test-data TESTDATA_SQL=' + data_path

    return trigger_cli_proc_for_celtask(self, make_cmd_for_data_export)


@app.route('/get_test_data', methods=['GET'])
@cross_origin()
def get_test_data():
    """ See static/swagger.yaml or http://localhost:5006/static/swagger.yaml. """
    if request.method == 'GET':
        output = trigger_cli_proc("cd test/e2e; make tests-e2e-local-data-ready-for-test")
        output_by_line = output.split('\n')
        list_of_files = []
        app.logger.info("list of files")
        app.logger.info(list_of_files)
        for line in output_by_line:
            app.logger.info('line: %s', line)
            match = re.match(r'\./(.+\.sql)', line)
            if match:
                list_of_files.append(match.group(1))
        app.logger.info(list_of_files)
        return jsonify(test_data=list_of_files)
    else:
        return jsonify({"Endpoint only accepts GET, but it was ": request.method}), 400

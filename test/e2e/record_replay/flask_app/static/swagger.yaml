openapi: 3.0.3
info:
  title: Backend API - e2e record/replay tool
  description: |-
    General information about the e2e record/replay backend API.

    Some useful links:
    - [Link to user documentation](http://localhost:5006)
    - [The source API definition for the tool](http://localhost:5006/static/swagger.yaml)
    
    # When to use which Preparation endpoint
    
    **Only relevant for the local domain**
  
    ## Attention:
    
    **/pull_local_browser_image**
    If the user has selected chrome AND firefox (not browserstack!) /pull_local_browser_image have to be called for both browsers.

    **/build_executor_final_image**
    For any of the selected browsers (non-browserstack and browserstack), this endpoint should be called.
    

    | After backend start or branch switch                                 | For browserstack                                                       | After any change of browsers/exec_user                                               |   |   |
    |-------------------------------------------------------------------|------------------------------------------------------------------------|--------------------------------------------------------------------------------------|---|---|
    | [/docker_aws_login](#/Preparation/PostDockerAwsLogin)             | [/browserstack_local](#/Preparation/PostBrowserstackLocal)             | [/pull_local_browser_image](#/Preparation/PostPullLocalBrowserImage)     |   |   |
    | [/kt_stack](#/Preparation/PostKtStack)                            |                                                                        | [/build_executor_base_image](#/Preparation/PostBuildExecutorBaseImage)   |   |   |
    | [/kt_install](#/Preparation/PostKtInstall)                        |                                                                        | [/build_executor_final_image](#/Preparation/PostBuildExecutorFinalImage) |   |   |
    | [/build_cockpit](#/Preparation/PostBuildCockpit)                  |                                                                        |                                                                          |   |   |
    | [/build_email_editor](#/Preparation/PostBuildEmailEditor)         |                                                                        |                                                                          |   |   |

    <br><br>
    You can <b>monitor your celery tasks</b> [here](http://localhost:5555).
    <br><br><br>
#    ![alt text1](/static/image.png)
  contact:
    email: <EMAIL>
  version: 1.0.1
# externalDocs:
#   description: Find out more about Swagger
#   url: http://some-confluence-page.com
servers:
  - url: http://localhost:5006
tags:
  - name: System
    description: Endpoints for the technical system in the background.
  - name: Report
    description: Endpoints for reports or similar stuff.
  - name: Preparation
    description: Endpoints for preparing the test environment like stack up, docker images pull, etc.
  - name: Tests
    description: Endpoints for test creation, infos and test execution.
  - name: Dev
    description: Endpoints for the dev stack.
#    externalDocs:
#      description: Find out more
#      url: http://swagger.io
paths:
  /cancel_task:
    post:
      description: Cancel a task.<br><br>
                   <b>Attention:<b><br>
                   After killing, you will get exceptions if you try to get the state of the task.
                   Get the last info of the task BEFORE you kill it.
      operationId: PostCancelTask
      tags:
        - System
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - taskID
              properties:
                taskID:
                  description: Task ID of the task you want to cancel
                  type: string
                  example: fdcff850-7fea-4d46-8b4c-b0ce3ddfe6c5
      responses:
        200:
          description: Success response, when task kill was ok.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description:
                      SUCCESS if task was killed successfully.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
  /docker_stats:
    post:
      description:  Triggers RabbitMQ to retrieve current running containers and infos about the kt-e2e-net, where the
                    tool relevant containers are hanging in.<br><br>
      operationId: PostDockerStats
      tags:
        - System
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
          links:
            GetDockerStats: # <---- arbitrary name for the link
              operationId: GetDockerStats
              parameters:
                taskID: '$response.body#/taskID'
              description: >
                The `taskID` value returned in the response can be used as
                the `taskID` parameter in `GET /docker_stats/{taskID}`.
  /docker_stats/{taskID}:
    get:
      operationId: GetDockerStats
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - System
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                                     This field setting is optional.
                        type: integer
                        example: 0
  /browserstack_session/{browser}:
    get:
      operationId: GetBrowserstackSession
      description:  Every run of a browserstack test retrieves its session info from Browserstack.<br>
        The Selenium Webdriver is doing that for us. The file is saved in the module folder
        of the test.<br><br>
        
        See <kt-root>/test/e2e/tests/fixtures/selenium_webdriver<br><br>
        
        e.g. Saved in .../e2e/tests/test_recordings/local/browserstack_session.json<br><br>
        
        We return the content of the file as json.
      parameters:
        - in: path
          name: browser
          description: Possible values are safari_macos.
          required: true
          schema:
            type: string
            enum:
              - safari_macos
              - firefox_win
              - safari_ipad
              - edge
              - chrome_macos
      tags:
        - Report
      responses:
        200:
          description: Return a json. e.g. <a target="_blank" href="https://www.browserstack.com/docs/automate/api-reference/selenium/session#get-session-details">Browserstack example</a>
          content:
            application/json:
              schema:
                type: object
                properties:
                  name:
                    type: string
                    example: recording_temp.test_recordings.local.test_prototype_1648207907069.test_prototype_1646897132615
                    description: test name
                  video_url:
                    type: string
                    description: url to the recorded video
        500:
          description: Error response when server ran into an error.
          content:
            application/json:
              schema:
                type: object
                required:
                  - state
                  - info
                properties:
                  state:
                    type: string
                    example: FAILURE
                    description: The state of the job.
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
  /browserstack_local:
    post:
      description:  Triggers RabbitMQ to start browserstack local tunnel.
      operationId: PostBrowserstackLocal
      tags:
        - Preparation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - browserstackUser
                - browserstackToken
              properties:
                browserstackUser:
                  description: Get it from within your account details on the browserstack page. [Here](https://www.browserstack.com)
                  type: string
                  example: simonwerling2
                browserstackToken:
                  description: Get it from within your account details on the browserstack page. [Here](https://www.browserstack.com)
                  type: string
                  example: zDqgxpCMGfxWz26PDdf2
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
          links:
            GetBrowserstackLocal: # <---- arbitrary name for the link
              operationId: GetBrowserstackLocal
              parameters:
                taskID: '$response.body#/taskID'
              description: >
                The `taskID` value returned in the response can be used as
                the `taskID` parameter in `GET /browserstack_local/{taskID}`.
  /browserstack_local/{taskID}:
    get:
      operationId: GetBrowserstackLocal
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Preparation
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /test_dependencies/{testID}:
    get:
      operationId: GetTestDependencies
      parameters:
        - in: path
          name: testID
          description: Test ID of the recorded test.
          required: true
          schema:
            type: integer
      tags:
        - Tests
      responses:
        200:
          description: Success response with dependency list.
          content:
            application/json:
              schema:
                required:
                  - dependencyChain
                type: object
                properties:
                  dependencyChain:
                    description: list of dependencies of the given test id.
                    type: array
                    example:
                      - 1234
                      - 3456
  /repo_ready:
    post:
      description: Set test repoReady flag.<br>
        If repoPath is set, it overwrites the current repoPath setting for the test.<br>
        If repoReady=True, test is moved in repoPath and non-existing folders are created.
      tags:
        - Tests
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - repoReady
                - repoPath
              properties:
                id:
                  description: ID of the test.
                  type: integer
                  example: 1651512888
                repoReady:
                  description: Is test in repo folder?
                  type: boolean
                  example: false
                repoPath:
                  description: The path relative to the repo folder. Forced to be non-empty, when repoReady=true.
                  type: string
                  example: dir/subdir
      responses:
        200:
          description: Test updated with new repoReady setting
  /pipeline_ready:
    post:
      description: Set test pipelineReady flag.<br>
        If repoReady or repoPath is not set, you get a 400.<br>
        When in CI context (gitlab pipeline) all pipelineReady=True test cases are executed in the pipeline.
      tags:
        - Tests
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - pipelineReady
              properties:
                id:
                  description: ID of the test.
                  type: integer
                  example: 1651512888
                pipelineReady:
                  description: Shall test be executed in pipeline?
                  type: boolean
                  example: true
      responses:
        200:
          description: Test updated with new pipelineReady setting
  /zephyr_test_create_empty:
    post:
      description: Create an empty zephyr test reference. Necessary to de/-reactivate a test in the pipeline via zephyr.<br>
        The tcType is 'kt' if it is a normal TC against our application. Otherwise, example for testing e2e tool itself, set to 'unit'.
      tags:
        - Tests
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - tcType
              properties:
                id:
                  description: ID of the test.
                  type: integer
                  example: 1651512888
                tcType:
                  description: Pass "kt" for klicktipp test and pass empty "" for a unit test.
                  type: string
                  example: kt
      responses:
        200:
          description: Empty zephyr test created.
          content:
            application/json:
              schema:
                type: object
                required:
                  - id
                  - zephyrTest
                  - zephyrTestUrl
                properties:
                  id:
                    description: Test ID.
                    type: integer
                    example: 1651562444
                  zephyrTest:
                    description: The Test ID.
                    type: string
                    example: T123
                  zephyrTestUrl:
                    description: The zephyr test URL.
                    type: string
  /zephyr_test_assign:
    post:
      description: Set zephyr test reference.
      tags:
        - Tests
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - zephyrTest
              properties:
                id:
                  description: ID of the test.
                  type: integer
                  example: 1651512888
                zephyrTest:
                  description: Test ID without jira Project prefix ("T123").<br>
                    Internally we add the project suffix DEV- (e.g. DEV-T123).
                  type: string
                  example: T123
      responses:
        200:
          description: Test updated with zephyr test reference/id
  /repo_folders:
    get:
      description: Return a list of all existing repo folders.
      tags:
        - Tests
      responses:
        200:
          description: json list with all folders.
  /tests:
    get:
      description: Return a short info blob about all existing tests in a json array.
      operationId: GetTests
      tags:
        - Tests
      responses:
        200:
          description: Success response, with list of all tests with additional infos like name or dependency chain.
          content:
            application/json:
              schema:
                type: array
                items:
                  required:
                    - version
                    - dependencyChain
                    - description
                    - displayName
                    - id
                    - machineName
                  properties:
                    id:
                      description: ID of the test.
                      type: integer
                      example: 1651512888
                    version:
                      description: version of the json format of the test. Empty string if not set.
                      type: string
                      example: 2.0
                    dependencyChain:
                      description: descr
                      type: array
                      example:
                        - 1234
                        - 2345
                    description:
                      description: Short description of the test.
                      type: string
                      example: Subscriber remove and readd same tag.
                    displayName:
                      description: Displayed in the UI.
                      type: string
                      example: test subscriber remove readd tag
                    machineName:
                      description: Unique name generated by the backend when a test is created.
                      type: string
                      example: test_subscriber_remove_readd_tag_1651512888
                    repoReady:
                      description: Is test in repo folder?
                      type: boolean
                      example: false
                    pipelineReady:
                      description: Is test ready for the repo?
                      type: boolean
                      example: false
                    zephyrTest:
                      description: The assigned zephyr Test
                      type: string
                      example: T123
                    zephyrTestUrl:
                      description: Direct URL to the test in zephyr
                      type: string
                      example: TheURL
  /test:
    post:
      operationId: PostTest
      description: Creates a recording in the backend.<br><br>
        Also generates the machine name based on the displayName json object below.<br>
        From displayName 'Subscriber remove and readd same tag' the endpoints defines<br>
        machineName as 'test_subscriber_remove_readd_tag_1651512888' (based on its ID)
      tags:
        - Tests
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - displayName
                - description
                - data
                - steps
              properties:
                id:
                  description: Test ID.
                  type: integer
                  example: 20003399
                displayName:
                  description: The name displayed in the UI.
                  type: string
                  example: Make a simple click
                description:
                  description: A short oneline to describe the test.
                  type: string
                  example: This test creates another Tag!
                repoReady:
                  description: Test will be saved in repo folder. You have to manually add/commit.
                  type: boolean
                  example: false
                repoPath:
                  description: The path relative to the repo folder. Forced to be non-empty, when repoReady=true.
                  type: string
                  example: dir/subdir
                pipelineReady:
                  description: Ready for pipeline?
                  type: boolean
                  example: false
                zephyrTest:
                  description: Relation to zephyrTest. The test ID.
                  type: string
                  example: T235
                data:
                  description: Parameters regarding test data.
                  type: object
                  required:
                    - creation
                    - url
                    - testCaseDependency
                  properties:
                    creation:
                      description: An array with Tonys data creation statements.<br>
                        Empty json array for no data creation before test start.
                      type: array
                    url:
                      description: The domain where the test was recorded.
                      type: string
                      example: https\://app-e2e.ktlocal.com/tags/me/add
                    testCaseDependency:
                      description: The ID of the test case which needs to be executed before this test case.<br>
                        Set to 0 if there is no test case dependency.
                      type: integer
                      example: 0
                steps:
                  description: Test steps to be translated into python code before test execution starts.
                  type: array
                  items:
                    required:
                      - command
                      - target
                      - targetCss
                      - value
                    properties:
                      command:
                        description: One of the available, into python translatable command Mnemonics.
                        type: string
                        enum:
                          - assert-element
                          - click
                          - text-input
                        example: click
                      target:
                        description: XPath of the webelement.
                        type: string
                      targetCss:
                        description: CSS alternative to locate the webelement.
                        type: string
                      value:
                        description: Could be a string or integer, depending on the command.
                      message:
                        description: Describe the command and its purpose.
                        type: string
                        example: Save tag by click on save button.
                screenshots:
                  description: Screenshots of the recorded steps.
                  type: array
      responses:
        200:
          description: Test recording successfully created in the backend.
          content:
            application/json:
              schema:
                required:
                  - machineName
                type: object
                properties:
                  machineName:
                    description: Unique name generated by the backend when a test is created.
                    type: string
                    example: test_create_tag_new_1651562444
          links:
            GetTest: # <---- arbitrary name for the link
              operationId: GetTest
              parameters:
                taskID: '$response.body#/taskID'
              description: >
                The `taskID` value returned in the response can be used as
                the `taskID` parameter in `GET /test/{taskID}`.
  /test/{testID}:
    get:
      operationId: GetTest
      description:  Returns a recording.<br><br>
        Also includes the generated machine name based on the displayName.<br>
        From displayName 'Subscriber remove and readd same tag' the endpoints defines<br>
        machineName as 'test_subscriber_remove_readd_tag_1651512888' (based on its ID)
      tags:
        - Tests
      parameters:
        - in: path
          name: testID
          description: test ID.
          example: 1651562444
          required: true
          schema:
            type: integer
      responses:
        200:
          description: Test recording successfully returned.
          content:
            application/json:
              schema:
                type: object
                required:
                  - id
                  - displayName
                  - machineName
                  - description
                  - data
                  - steps
                properties:
                  id:
                    description: Test ID.
                    type: integer
                    example: 1651562444
                  displayName:
                    description: The name displayed in the UI.
                    type: string
                    example: Create Tag new
                  machineName:
                      description: Unique name generated by the backend when a test is created.
                      type: string
                      example: test_create_tag_new_1651562444
                  description:
                    description: A short oneline to describe the test.
                    type: string
                    example: This test creates another Tag!
                  repoReady:
                    description: Test will be saved in repo folder. You have to manually add/commit.
                    type: boolean
                    example: false
                  repoPath:
                    description: The path relative to the repo folder. Forced to be non-empty, when repoReady=true.
                    type: string
                    example: dir/subdir
                  pipelineReady:
                    description: Ready for pipeline?
                    type: boolean
                    example: false
                  zephyrTest:
                    description: Relation to zephyrTest. The test ID.
                    type: string
                    example: T235
                  zephyrTestUrl:
                      description: Direct URL to the test in zephyr
                      type: string
                      example: TheURL
                  data:
                    description: Parameters regarding test data.
                    type: object
                    required:
                      - creation
                      - url
                      - testCaseDependency
                    properties:
                      creation:
                        description: An array with Tonys data creation statements.<br>
                          Empty json array for no data creation before test start.
                        type: array
                      url:
                        description: The domain where the test was recorded.
                        type: string
                        example: https\://app-e2e.ktlocal.com/tags/me/add
                      testCaseDependency:
                        description: The ID of the test case which needs to be executed before this test case.<br>
                          Set to 0 if there is no test case dependency.
                        type: integer
                        example: 0
                  steps:
                    description: Test steps to be translated into python code before test execution starts.
                    type: array
                    items:
                      required:
                        - command
                        - target
                        - targetCss
                        - value
                      properties:
                        command:
                          description: One of the available, into python translatable command Mnemonics.
                          type: string
                          enum:
                            - assert-element
                            - click
                            - text-input
                          example: click
                        target:
                          description: XPath of the webelement.
                          type: string
                        targetCss:
                          description: CSS alternative to locate the webelement.
                          type: string
                        value:
                          description: Could be a string or integer, depending on the command.
                        message:
                          description: Describe the command and its purpose.
                          type: string
                          example: Save tag by click on save button.
                  screenshots:
                    description: Screenshots of the recorded steps.
                    type: array
  /exec_test_parallel:
    post:
      description:  Per test the browsers chosen are executed for that test case in parallel.<br><br>
                    Possible values in the browsers list -> see schema<br><br>
                    <b>Local docker browsers</b><br>
                    chrome<br>
                    firefox<br><br>
                    <b>Browserstack browsers</b><br>
                    safari_macos<br>
                    safari_ipad<br>
                    firefox_win<br>
                    edge<br>
                    chrome_macos
      operationId: PostExecTestParallel
      tags:
        - Tests
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - appLocation
                - domain
                - browsers
                - testCase
                - prepare
                - caching
                - forceNewDumps
                - execUser
                - execUserPwd
                - browserstackUser
                - browserstackToken
                - debug
                - retry
              properties:
                appLocation:
                  description: domain can be remote or local.<br>
                               app-e2e.ktlocal.com is 'local' (local caching possible)<br>
                               k8s, staging, app.ktlocal.com are 'remote'
                  type: string
                  enum:
                    - remote
                    - local
                  example: remote
                domain:
                  description: domains without https<br>
                               e.g. app.ktlocal.com, www.klicktipp-staging.com, app-e2e.ktlocal.com<br>
                               k8s example app.e2e-rr-dev-4846-e2e-rr.dev.ktsys.cloud
                  type: string
                  example: app.e2e-rr-dev-4846-e2e-rr.dev.ktsys.cloud
                browsers:
                  description: A json array with strings/browsers you want to execute against in parallel.<br><br>
                               _Possible item values are<br>
                               chrome<br>
                               firefox<br><br>
                               _Browserstack browsers are<br>
                               safari_macos<br>
                               safari_ipad<br>
                               firefox_win<br>
                               edge<br>
                               chrome_macos<br><br>
                               ! If you use a special execUser you need to set this list to ["chrome"]
                  type: array
                  uniqueItems: true
                  minItems: 1
                  items:
                    type: string
                    enum:
                      - chrome
                      - firefox
                      - safari_macos
                      - safari_ipad
                      - firefox_win
                      - edge
                      - chrome_macos
                  example:
                    - chrome
                    - safari_macos
                testCase:
                  description: The machineName of the test. (Get it over /tests endpoint e.g.)
                  type: string
                  example: test_subscriber_remove_readd_tag_1651512888
                prepare:
                  description: Prepare means\:<br>
                               If the test has no dependencies, only Tonys data creation will be triggered, then<br>
                               the test will be executed. Now you could start a new recording based on the current<br>
                               kt stack situation.<br>
                               If caching is true, and the test case is cached which you want to start with prepare=true<br>
                               the dump with the cached state will be imported and there is no test execution and no<br>
                               Tony data creation.
                  type: boolean
                  example: true
                caching:
                  description: Set to true and caching will be used. If set to false, forceNewDumps will be ignored
                  type: boolean
                  example: true
                forceNewDumps:
                  description: If caching=true, delete all dumps of tests in dependency chain if set to true.<br>
                               Recreate dumps (Only created, if all browsers of the test execution pass the test).<br>
                               As soon as one of the tests in the dependency chain fails, no more dumps will be created.
                  type: boolean
                  example: false
                execUser:
                  description: ! You need to set to 'none' if no special user.<br>
                               If 'none' we execute against the standard browser users. For chrome, the user e2e-test-replay-chrome<br>
                               if set to a special username, we use always chrome.<br>
                               Mostly, when set != 'none', you want run against staging with your special staging user or<br>
                               you run with domain=local and prepare=True mode, then your database will be prepared<br>
                               locally on the e2e-test-recorder user.
                  type: string
                  example: none
                execUserPwd:
                  description: The password of the given execUser.<br>
                               ! You need to set to 'none' if no special user.
                  type: string
                  example: none
                browserstackUser:
                  description: Please always set this to a valid user.
                  type: string
                  example: simonwerling2
                browserstackToken:
                  description: Please always set a valid token.
                  type: string
                  example: zDqgxpCMGfxWz26PDdf2
                debug:
                  description: Set to true, if you want to observe the local browsers via VNC.
                  type: boolean
                  example: true
                retry:
                  description: Set to true, if you want to have a fast retry after a failed test.
                  type: boolean
                  example: true
                testSession:
                  description: Some test session identifier. All reports will be separately saved<br>
                    and are not overwritten by tests. The report path will no longer accessible via<br>
                    .../report-chrome/index.html (The overwritten report).<br>
                    Instead .../reports/my_temp_test_session_0/<machine-name-of-test>/report-chrome/index.html
                  type: string
                  example: my_temp_test_session_0
                screenshots:
                  description: |
                    With this flag you can execute a test with screenshot creation and comparison.<br>
                    This string value can hold a combination of several flags<br>
                    e.g.<br>
                      "compareAndFail" - For non existing reference images, new images will be created.<br>
                                          Difference leads to assertion error and a failing test.<br>
                      "compareAndAllowFail" - Same as compareAndFail, but test will pass screen comparison errors<br>
                                               Nevertheless, you will see the differences in the HTML report.<br>
                      "newScreenshots" - Before the appropriate test runs, all existing reference images will be deleted<br><br>
                    You can combine the above flags. "compareAndAllowFail" overrides compareAndFail behavior.<br>
                    Remark - The flags are case sensitive.<br>
                    e.g. "compareAndFail|newScreenshots" - remark: with this run, you create all new references.<br>
                         If you want to compare the new references with a new run, you have to remove the "newScreenshots"<br><br>
                    Only for json test steps, which contains the (at least an empty) "screenshot" key, screenshots will be created.<br>
                    e.g.<br>
                    ```json
                    {
                    "command": "assert-element",
                    "target": "//html//body//kt-app//div//ng-component//div//section//kt-newsletter-detail//div//div//kt-item-detail-overview//div//div//div//b[contains(text(),'11')]",
                    "targetCss": "",
                    "value": "",
                    "special": "",
                    "screenshot": {
                      "masked": { "1366x2000": [ [ 168, 168, 168, 168 ],
                                                 [ 168, 85, 1186, 709 ] ],
                                  "1366x768": [ [ 168, 85, 1186, 709 ] ]
                      },
                      "mode": "compareAndFail|compareAndAllowFail|newScreenshot|ignore" }
                    }
                    ```
                    <br><br>
                    You can mask areas, to ignore those during comparison. Format is per rectangle, [x_start,y_start,x_end,y_end]<br>
                    The "mode" key behaves similar to the global "screenshots" flag. BUT you can decide on step level, if you<br>
                    need special behavior for some step.<br>
                    Remark - "newScreenshot" is without 's' at the end ;) sorry Tony xD
                    E.g.<br>
                    global: compareAndFail - step: compareAndAllowFail - This step will not fail with differences<br>
                    global: compareAndAllowFail - step: compareAndFail - This step will fail with differences<br>
                    global: compareAndFail - step: newScreenshot - compare has no effect, we create a new reference for this step<br>
                    global: newScreenshots - step: ignore - For this step, no new screenshot will be created!<br>
                    Attention: You need at least to set global compareAndFail or compareAndAllowFail,<br>
                               if you want to use "newScreenshot" on step level.<br><br>                    
                    "ignore" simply does no screenshot actions -> will override any other mode you have set on step level
                  type: string
                  example: compareAndFail
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
          links:
            GetExecTestParallel: # <---- arbitrary name for the link
              operationId: GetExecTestParallel
              parameters:
                taskID: '$response.body#/taskID'
              description: >
                The `taskID` value returned in the response can be used as
                the `taskID` parameter in `GET /exec_test_parallel/{taskID}`.
  /exec_test_parallel/{taskID}:
    get:
      operationId: GetExecTestParallel
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Tests
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /exec_test_parallel_states:
    get:
      operationId: GetExecTestParallelStates
      tags:
        - Tests
      responses:
        200:
          description: Returns a json object giving detailed informations during test execution.<br>
                       Contains infos about caching, console logs of pytest, test execution progress, result.
          content:
            application/json:
              schema:
                required:
                  - execInfo
                  - cacheExecInfo
                  - cachingStates
                  - logs
                  - testCaseStates
                type: object
                properties:
                  execInfo:
                    description: Start parameters and user:browser mapping.
                    type: object
                    properties:
                      domain:
                        description: domain type.
                        type: string
                        example: local
                        enum:
                          - local
                          - staging
                          - k8s
                      browsers:
                        description: The browsers you've started the tests for.<br>
                                     If you started with the execUser != 'none' your browser<br>
                                     will be set to ['chrome'].
                        type: array
                        example:
                          - chrome
                          - safari_macos
                        items:
                          type: string
                          enum:
                            - chrome
                            - firefox
                            - safari_macos
                            - safari_ipad
                            - firefox_win
                            - edge
                            - chrome_macos
                      execUser:
                        description: If 'none' we execute against the standard browser users. For chrome, the user e2e-test-replay-chrome<br>
                                     if set to a special username, we use always chrome.<br>
                                     Mostly, when set != 'none', you want run against staging with your special staging user or<br>
                                     you run with domain=local and prepare=True mode, then your database will be prepared<br>
                                     locally on the e2e-test-recorder user.<br>
                                     On staging your user could be e2e-test-record-replay-simon.
                        type: string
                        example: none
                      users:
                        description: The users used for the executions.<br>
                                     for chrome e2e-test-replay-chrome
                        type: array
                        items:
                          type: string
                        example:
                          - e2e-test-replay-chrome
                          - e2e-test-replay-safari_macos
                      browserUserMap:
                        description: usernames generated if you set execUser='none'.<br>
                                     browser will be set to chrome, if you set execUser!='none'<br>
                                     Here you see the final browsers with assigned users.
                        example:
                          chrome: e2e-test-replay-chrome.
                          safari_macos: e2e-test-replay-safari_macos.
                        additionalProperties:
                          type: string
                      testCase:
                        description: The test case we want to execute.
                        type: string
                        example: test_add_subscriber_new_1651644555
                      prepare:
                        description: prepare mode=true -> last test case won`t be executed if cached.
                        type: boolean
                        example: true
                      forceNewDumps:
                        description: delete relevant dumps in execution chain (of all dependencies) if caching=true
                        type: boolean
                        example: false
                      caching:
                        description: we import database dumps with users if the caching algorithm matches a senseful caching situation.
                        type: boolean
                        example: true
                      browserstackUser:
                        description: Get it from within your account details on the browserstack page. [Here](https://www.browserstack.com)
                        type: string
                        example: simonwerling2
                      browserstackToken:
                        description: Get it from within your account details on the browserstack page. [Here](https://www.browserstack.com)
                        type: string
                        example: zDqgxpCMGfxWz26PDdf2
                      debug:
                        description: Set to true, if you want to observe the local browsers via VNC.
                        type: boolean
                        example: true
                  cacheExecInfo:
                    description: Infos about the caching.<br>
                                 Contains object import. Is empty json object if no dump was imported.<br>
                                 Furthermore it contains per test in execution chain an object with appropriate<br>
                                 machineName
                    type: object
                    properties:
                      import:
                        type: object
                        properties:
                          dump:
                            type: string
                            example: e2e-test-tool__record_replay_mvp.sql
                          importedUsers:
                            description: Could be an empty json array.
                            type: array
                            items:
                              type: string
                              example:
                                - e2e-test-replay-chrome
                                - e2e-test-replay-firefox
                          notImportedUsers:
                            description: Could be an empty json array []
                            type: array
                            items:
                              type: string
                              example:
                                - e2e-test-replay-safari_macos
                          testCase:
                            description: The test case which should be executed. Dependencies not shown.
                            type: string
                            example: test_add_subscriber_new_1651644555
                      additionalProperties:
                        description: Is the machine name of a test case.
                        example: test_add_subscriber_new_1651644555
                        type: object
                        additionalProperties:
                          description: Is the name of a browser.
                          example: chrome
                          type: object
                          properties:
                            execution:
                              description: Last Truth. Tells you if the caching algorithm decided to execute your test.
                              type: boolean
                              example: false
                    example:
                      import:
                        dump: e2e-test-tool__record_replay_mvp.sql
                        importedUsers: e2e-test-replay-chrome
                        notImportedUsers: []
                        testCase: test_add_subscriber_new_1651644555
                      test_add_subscriber_new_1651644555:
                        e2e-test-replay-chrome:
                          execution: false
                      test_create_tag_new_1651562444:
                        e2e-test-replay-chrome:
                          execution: false
                  cachingStates:
                    description: Holds information about the caching hits per test case.
                    type: object
                    additionalProperties:
                      type: object
                      properties:
                        usersCached:
                          description: Browsers cached.
                          type: array
                          items:
                            type: string
                          example:
                            - e2e-test-replay-chrome
                            - e2e-test-replay-firefox
                        usersExec:
                          description: Browsers in execution list. Caching algorithm tells you in cacheExecInfo, whether<br>
                                       there is really an execution for the test with browser or if cache/dump is used.
                          type: array
                          items:
                            type: string
                          example:
                            - e2e-test-replay-chrome
                            - e2e-test-replay-firefox
                        cacheHits:
                          type: integer
                          example: 1
                        dump:
                          type: string
                          example: e2e-test-tool__record_replay_mvp.sql
                    example:
                      - test_add_subscriber_new_1651644555:
                          usersCached:
                            - e2e-test-replay-chrome
                          usersExec:
                            - e2e-test-replay-chrome
                          cacheHits: 1
                          dump: e2e-test-tool__record_replay_mvp.sql
                      - test_create_tag_new_1651562444:
                          usersCached:
                            - e2e-test-replay-chrome
                          usersExec:
                            - e2e-test-replay-chrome
                          cacheHits: 1
                          dump: e2e-test-tool__record_replay_mvp.sql
                  logs:
                    example:
                      chrome: Some log.
                      safari_macos: Some log.
                      edge: Some log.
                    description: One entry per browser execution.
                    additionalProperties:
                      type: string
                  testCaseStates:
                    description: One entry per test case with machineName of the test.
                    type: object
                    example:
                      - test_add_subscriber_new_1651644555:
                          - chrome:
                              testResult: failed
                              state: Finished
                          - safari_macos:
                              testResult: passed
                              state: Finished
                          - edge:
                              testResult: error
                              state: Finished
                              reason: Exception during pytest execution. See log file.
                      - test_add_tag_new_29394002:
                          - chrome:
                              testResult: ""
                              state: pending
                          - safari_macos:
                              testResult: ""
                              state: running
                          - edge:
                              testResult: skipped
                              state: Finished
                              reason: No execution necessary. Every browser/user to exec was imported.
                    additionalProperties:
                      description: Every object key is a test case machine name with execution infos as attribute.
                      type: object
                      additionalProperties:
                        description: Every object key is the browser name for which the test case is executed.
                        type: object
                        properties:
                          testResult:
                            description: Can be an empty string if state is in running or pending mode.<br>
                                         error Means a system error. Maybe webdriver problems.<br>
                                         failed Tells you, there is a real test error.<br>
                                         skipped Maybe the test was cached.<br>
                                         passed Everything was fine.
                            type: string
                            enum:
                              - skipped
                              - passed
                              - failed
                              - error
                            example: skipped
                          state:
                            type: string
                            enum:
                              - Finished
                              - pending
                              - Running
                            example: Finished
                          reason:
                            description: Reason for a skip or error.<br>
                                         Is only existing in attribute list, for testResult error and skipped.
                            type: string
                            example: No execution necessary. Every browser/user to exec was imported.
  /report_server:
    post:
      description:  Starts up a local webserver where you can open the report.<br><br>
                    Possible values for 'browser' in request body (only one browser!)<br>
                    chrome<br>
                    firefox<br>
                    safari_macos<br>
                    safari_ipad<br>
                    firefox_win<br>
                    edge<br>
                    chrome_macos
      operationId: PostReportServer
      tags:
        - Report
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - browser
              properties:
                browser:
                  description: The browser name you want to start the report server for.
                  type: string
                  example: chrome
                  enum:
                    - chrome
                    - firefox
                    - safari_macos
                    - safari_ipad
                    - firefox_win
                    - edge
                    - chrome_macos
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
          links:
            GetReportServer: # <---- arbitrary name for the link
              operationId: GetReportServer
              parameters:
                taskID: '$response.body#/taskID'
              description: >
                The `taskID` value returned in the response can be used as
                the `taskID` parameter in `GET /report_server/{taskID}`.
  /report_server/{taskID}:
    get:
      description: Shows state of the report server startup. As soon as up, you can access the report on the appropriate<br>
                   port on the local host.<br>
                   [chrome :82](http://localhost:82)<br>
                   [firefox :83](http://localhost:83)<br>
                   [safari_macos :84](http://localhost:84)<br>
                   [safari_ipad :85](http://localhost:85)<br>
                   [firefox_win :86](http://localhost:86)<br>
                   [edge :87](http://localhost:87)<br>
                   [chrome_macos :88](http://localhost:88)<br>
      operationId: GetReportServer
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Report
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /docker_aws_login:
    post:
      operationId: PostDockerAwsLogin
      description:  Make the aws docker login to pull images unlimited.<br><br>
      tags:
        - Preparation
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
          links:
            GetReportServer: # <---- arbitrary name for the link
              operationId: GetDockerAwsLogin
              parameters:
                taskID: '$response.body#/taskID'
              description: >
                The `taskID` value returned in the response can be used as
                the `taskID` parameter in `GET /docker_aws_login/{taskID}`.
  /docker_aws_login/{taskID}:
    get:
      operationId: GetDockerAwsLogin
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Preparation
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /kt_stack:
    post:
      operationId: PostKtStack
      description:  Depending on mode the kt e2e stack gets up, down or resetted.<br><br>
      tags:
        - Preparation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mode
              properties:
                mode:
                  enum:
                    - up
                    - down
                    - reset
                  type: string
                  example: reset
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
  /kt_stack/{taskID}:
    get:
      operationId: GetKtStack
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Preparation
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /pull_local_browser_image:
    post:
      operationId: PostPullLocalBrowserImage
      description:  Pull chrome or firefox local browser docker images.<br><br>
      tags:
        - Preparation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - browser
              properties:
                browser:
                  type: string
                  example: chrome
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
  /pull_local_browser_image/{taskID}:
    get:
      operationId: GetPullLocalBrowserImage
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Preparation
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /build_executor_base_image:
    post:
      operationId: PostBuildExecutorBaseImage
      description: Build pytest executor base image. See also /build_executor_final_image.<br><br>
      tags:
        - Preparation
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
  /build_executor_base_image/{taskID}:
    get:
      operationId: GetBuildExecutorBaseImage
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Preparation
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /build_executor_final_image:
    post:
      operationId: PostBuildExecutorFinalImage
      description:  Build pytest executor image for given browser. You should have executed POST /build_executor_base_image<br><br>
                    Possible values for 'browser' in request body (only one browser!)<br>
                    chrome<br>
                    firefox<br>
                    safari_macos<br>
                    safari_ipad<br>
                    firefox_win<br>
                    edge<br>
                    chrome_macos
      tags:
        - Preparation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - browser
              properties:
                browser:
                  type: string
                  example: chrome
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
  /build_executor_final_image/{taskID}:
    get:
      operationId: GetBuildExecutorFinalImage
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Preparation
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /kt_install:
    post:
      operationId: PostKtInstall
      description: Beforehand you should have executed /kt_stack with mode=reset or up.<br><br>
      tags:
        - Preparation
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
  /kt_install/{taskID}:
    get:
      operationId: GetKtInstall
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Preparation
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /build_cockpit:
    post:
      description:  Create celery task building the angular cockpit.<br><br>
      operationId: PostBuildCockpit
      tags:
        - Preparation
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
          links:
            GetDockerStats: # <---- arbitrary name for the link
              operationId: GetBuildCockpit
              parameters:
                taskID: '$response.body#/taskID'
              description: >
                The `taskID` value returned in the response can be used as
                the `taskID` parameter in `GET /build_cockpit/{taskID}`.
  /build_cockpit/{taskID}:
    get:
      operationId: GetBuildCockpit
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Preparation
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /build_email_editor:
    post:
      description:  Create celery task building the angular email editor.<br><br>
      operationId: PostBuildEmailEditor
      tags:
        - Preparation
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
          links:
            GetDockerStats: # <---- arbitrary name for the link
              operationId: GetBuildEmailEditor
              parameters:
                taskID: '$response.body#/taskID'
              description: >
                The `taskID` value returned in the response can be used as
                the `taskID` parameter in `GET /build_email_editor/{taskID}`.
  /build_email_editor/{taskID}:
    get:
      operationId: GetBuildEmailEditor
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Preparation
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /kt_dev_stack:
    post:
      operationId: PostKtDevStack
      description:  Depending on mode the kt DEV stack gets up, down.<br><br>
      tags:
        - Dev
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - mode
              properties:
                mode:
                  enum:
                    - up
                    - down
                  type: string
                  example: up
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
  /kt_dev_stack/{taskID}:
    get:
      operationId: GetKtDevStack
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Dev
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0
  /kt_dev_install:
    post:
      operationId: PostKtDevInstall
      description: Beforehand you should have executed /kt_dev_stack with mode=up or up.<br><br>
      tags:
        - Dev
      responses:
        202:
          description: Task is successfully triggered in RabbitMQ.
          content:
            application/json:
              schema:
                type: object
                properties:
                  taskID:
                    type: string
                    example: 82f57ec5-a0dd-4884-9101-9ac3b4361474
                    description: RabbitMQ/Celery task id. Used in GET command of this endpoint.
  /kt_dev_install/{taskID}:
    get:
      operationId: GetKtDevInstall
      parameters:
        - in: path
          name: taskID
          description: taskID you retrieved by the POST command of this endpoint.
          required: true
          schema:
            type: string
      tags:
        - Dev
      responses:
        200:
          description: Success response, when recording was sent successfully.
          content:
            application/json:
              schema:
                required:
                  - state
                  - info
                type: object
                properties:
                  state:
                    description: State of the job. (Not the command, the job was executing) <br><br>
                      States could be <br>
                      PENDING taskID not existing or task is queued (Last is not really realistic) <br>
                      SUCCESS task was finished successfully. (Maybe not the command itself) <br>
                      PROGRESS task still in progress<br>
                      FAILURE task processing failed. Maybe an exception.
                    type: string
                    example: SUCCESS
                  info:
                    type: object
                    description: Logs and detailed command results.
                    required:
                      - log
                    properties:
                      log:
                        description: Logs of the command executed inside RabbitMQ.
                        type: string
                        example: Some log message.
                      result:
                        description: Could be "success" or "failed". This field setting is optional.
                        type: string
                        example: "success"
                      returncode:
                        description: For example if there was a shell command executed, you get the command return value.<br>
                          This field setting is optional.
                        type: integer
                        example: 0

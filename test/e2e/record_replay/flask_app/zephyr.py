# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""Provide URLs to domains, pages, etc."""
import requests
from datetime import datetime, timezone, timedelta

from . import aws_secret_manager as aws_sm


class ZEPHYR(object):
    """Handle ZEPHYR interactions via API."""

    autotests_folder_id = None
    cycle_key = None
    environment = None

    autotests_folder_name = "Automated Tests"
    project_key = "DEV"
    api_path = "https://api.zephyrscale.smartbear.com/v2/"
    zephyr_bearer = None
    default_header = None
    post_header = None

    endpoint_cycles = api_path + "testcycles"
    endpoint_folders = api_path + "folders"
    endpoint_testexecutions = api_path + "testexecutions"
    endpoint_testcases = api_path + "testcases"

    def __init__(self):
        """Init some instance attributes."""
        print("init method")


    @staticmethod
    def create_empty_recording_tc(path: str,
                                  tc_type: str,  # pass "kt" for the "Klick-Tipp" folder, otherwise the base folder is the record replay folder in zephyr
                                  name: str) -> dict:
        """Create a nearly empty test case in the library. Minimum for handling deactivation of TCs in pipeline."""
        existing_zephyr_tests = ZEPHYR.get_tests_by_name(name)

        # Creates all non-existing folders in the given path
        folder_id = ZEPHYR.get_tc_folder_id(path, tc_type)
        data = {
            "projectKey": ZEPHYR.project_key,
            "name": name,
            "priorityName": "Normal",
            "statusName": "Finalisiert",
            "folderId": folder_id,
            "ownerId": "5cc839cc9e792a0e3fab0b7e",
            "customFields": {
                "Feature Category": "Klick-Tipp UI",
                "Autom.": "Yes",
                "Executable on": [
                    "Local"
                ],
                "Autom. Candidate": "Yes",
                "Autom. test name": name,
                "Recorded": True
            }
        }

        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.post_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer,
                              'Content-Type': "application/json",
                              'Accept': "application/json"}

        tc = requests.post(ZEPHYR.endpoint_testcases,
                           json=data,
                           headers=ZEPHYR.post_header)

        if tc.ok:
            if existing_zephyr_tests:
                for test in existing_zephyr_tests:
                    # Delete means here. Move into the "deleted" folder.
                    ZEPHYR.delete_tc(test)
            return tc.json().get("key").replace("DEV-", "")
        raise "Something failed when creating the TC in zephyr: " + tc.reason

    @staticmethod
    def get_tests_by_name(name: str) -> list:
        all_tests = ZEPHYR.get_all_tests()
        all_tests_with_same_name = []
        for test in all_tests:
            if test.get("name") == name:
                all_tests_with_same_name.append(test)
        return all_tests_with_same_name

    @staticmethod
    def delete_tc(test: dict) -> bool:
        """Moves the test case in the folder "deleted" in the test library.

        Return True when fine.
        """
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.post_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer,
                              'Content-Type': "application/json",
                              'Accept': "application/json"}

        # ID and path to the folder with name "deleted"
        id_of_folder_deleted = 9857203
        if test["folder"]["id"] != id_of_folder_deleted:
            test["folder"] = {"id": id_of_folder_deleted,
                              "self": f"https://api.zephyrscale.smartbear.com/v2/folders/{id_of_folder_deleted}"}
            response = requests.put(ZEPHYR.endpoint_testcases + "/" + test.get("key"),
                                    json=test,
                                    headers=ZEPHYR.post_header)

            return response.ok

        return False

    @staticmethod
    def get_all_tests() -> list:
        """Return a list with all zephyr tests."""
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.default_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer}

        data = {"projectKey": ZEPHYR.project_key,
                "maxResults": 100,
                "startAt": 0,
                "folderType": "TEST_CASE"}

        tests = requests.get(ZEPHYR.endpoint_testcases,
                             params=data,
                             headers=ZEPHYR.default_header)

        tests_list = []
        while tests.ok:
            tests_json = tests.json()

            max_results = tests_json.get("maxResults")
            is_last = tests_json.get("isLast")
            next_url = tests_json.get("next")

            if max_results > 0:
                test_list = tests_json.get("values")
                tests_list.extend(test_list)

            if not is_last and next_url is not None:
                tests = requests.get(next_url,
                                     headers=ZEPHYR.default_header)
            else:
                break

        return tests_list

    @staticmethod
    def get_tc_folder_id(path, tc_type):
        if tc_type == "kt":
            base_folder_id = ZEPHYR.get_folder_id("Klick-Tipp", "TEST_CASE")
        else:
            base_folder_id = ZEPHYR.get_folder_id("e2e record replay tool", "TEST_CASE")

        path_folders = path.split("/")
        curr_parent_folder_id = base_folder_id
        for curr_folder in path_folders:
            curr_folder_id = ZEPHYR.get_folder_id_within_parent(curr_folder, curr_parent_folder_id)
            if not curr_folder_id:
                curr_folder_id = ZEPHYR.create_folder_in_lib(curr_folder, curr_parent_folder_id)
            curr_parent_folder_id = curr_folder_id

        return curr_parent_folder_id

    @staticmethod
    def get_folder_id_within_parent(curr_folder: str, curr_parent_folder_id: int) -> int:
        all_folders = ZEPHYR.get_all_tc_folders()
        for folder in all_folders:
            if folder.get("name") == curr_folder and folder.get("parentId", -1) == curr_parent_folder_id:
                return folder.get("id")
        return None

    @staticmethod
    def get_all_tc_folders() -> list:
        """Return a list with all zephyr folder informations."""
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.default_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer}

        data = {"projectKey": ZEPHYR.project_key,
                "maxResults": 120,
                "startAt": 0,
                "folderType": "TEST_CASE"}

        folders = requests.get(ZEPHYR.endpoint_folders,
                               params=data,
                               headers=ZEPHYR.default_header)

        folders_list = []
        while folders.ok:
            folders_json = folders.json()

            max_results = folders_json.get("maxResults")
            is_last = folders_json.get("isLast")
            next_url = folders_json.get("next")

            if max_results > 0:
                folder_list = folders_json.get("values")
                folders_list.extend(folder_list)

            if not is_last and next_url is not None:
                folders = requests.get(next_url,
                                       headers=ZEPHYR.default_header)
            else:
                break

        return folders_list

    @staticmethod
    def create_folder_in_lib(name: str, parent_folder_id: int) -> int:
        """Create a folder in the test case library."""
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.post_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer,
                              'Content-Type': "application/json",
                              'Accept': "application/json"}

        data = {"projectKey": ZEPHYR.project_key,
                "parentId": parent_folder_id,
                "name": name,
                "folderType": "TEST_CASE"}

        folder = requests.post(ZEPHYR.endpoint_folders,
                               json=data,
                               headers=ZEPHYR.post_header)

        if folder.ok:
            return folder.json().get("id")

        return None

    @staticmethod
    def get_folder_id(name, folder_type="TEST_CYCLE"):
        """Give me the folder name, and I return its id or None."""
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.default_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer}

        data = {"projectKey": ZEPHYR.project_key,
                "maxResults": 40,
                "startAt": 0,
                "folderType": folder_type}

        folders = requests.get(ZEPHYR.endpoint_folders,
                               params=data,
                               headers=ZEPHYR.default_header)

        while folders.ok:
            folders_json = folders.json()

            max_results = folders_json.get("maxResults")
            is_last = folders_json.get("isLast")
            next_url = folders_json.get("next")

            if max_results > 0:
                folder_list = folders_json.get("values")

                for folder in folder_list:
                    if folder.get("name") == name:
                        ZEPHYR.autotests_folder_id = folder.get("id")
                        return ZEPHYR.autotests_folder_id

            if not is_last and next_url is not None:
                folders = requests.get(next_url,
                                       headers=ZEPHYR.default_header)
            else:
                break

        return None

    @staticmethod
    def get_cycle(cycle_name, folder_id):
        """Give me the cycle name + cycle folder, and I test for its existence.

        Return key or None.
        """
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.default_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer}

        data = {"projectKey": ZEPHYR.project_key,
                "maxResults": 40,
                "startAt": 0,
                "folderId": folder_id}

        cycles = requests.get(ZEPHYR.endpoint_cycles,
                              params=data,
                              headers=ZEPHYR.default_header)

        while cycles.ok:
            cycles_json = cycles.json()

            max_results = cycles_json.get("maxResults")
            is_last = cycles_json.get("isLast")
            next_url = cycles_json.get("next")

            if max_results > 0:
                cycle_list = cycles_json.get("values")

                for cycle in cycle_list:
                    if cycle.get("name") == cycle_name:
                        ZEPHYR.cycle_key = cycle.get("key")
                        return ZEPHYR.cycle_key

            if not is_last and next_url is not None:

                cycles = requests.get(next_url,
                                      headers=ZEPHYR.default_header)
            else:
                break

        return None

    @staticmethod
    def get_cycles(folder_id):
        """Give me all cycles for given folder.

        Return list or [].
        """
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.default_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer}

        data = {"projectKey": ZEPHYR.project_key,
                "maxResults": 40,
                "startAt": 0,
                "folderId": folder_id}

        all_cycles = []
        cycles = requests.get(ZEPHYR.endpoint_cycles,
                              params=data,
                              headers=ZEPHYR.default_header)

        while cycles.ok:
            cycles_json: dict = cycles.json()

            max_results = cycles_json.get("maxResults")
            is_last = cycles_json.get("isLast")
            next_url = cycles_json.get("next")

            if max_results > 0:
                cycle_list = cycles_json.get("values")
                all_cycles.extend(cycle_list)

            if not is_last and next_url is not None:

                try:
                    cycles = requests.get(next_url,
                                          headers=ZEPHYR.default_header)
                except:
                    raise "Something went wrong. Maybe another test is already deleting old cycles and your next_url" \
                          "is no longer valid."
            else:
                break

        return all_cycles

    @staticmethod
    def create_cycle(cycle_name, folder_id):
        """Give me the cycle name + cycle folderm and I create the cycle.

        Return key or None.
        """
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.post_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer,
                              'Content-Type': "application/json",
                              'Accept': "application/json"}

        nowstamp = datetime.now(timezone.utc)
        tomorrow_stamp = nowstamp + timedelta(days=1)
        start_datetime = nowstamp.strftime("%Y-%m-%dT%H:%M:%SZ")
        end_datetime = tomorrow_stamp.strftime("%Y-%m-%dT%H:%M:%SZ")
        data = {"projectKey": ZEPHYR.project_key,
                "folderId": folder_id,
                "name": cycle_name,
                "plannedStartDate": start_datetime,
                "plannedEndDate": end_datetime}
        # "plannedStartDate": "2020-11-12T14:31:57Z",
        # "plannedEndDate": "2022-01-03T03:10:15Z"}

        cycles = requests.post(ZEPHYR.endpoint_cycles,
                               json=data,
                               headers=ZEPHYR.post_header)

        if cycles.ok:
            cycles_json = cycles.json()
            ZEPHYR.cycle_key = cycles_json.get("key")
            return ZEPHYR.cycle_key

        return None

    @staticmethod
    def update_cycle(cycle_key: str, body: dict) -> bool:
        """Give me the cycle id + request body as dict with updated fields.

        Return True when update was successful.
        """
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.post_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer,
                              'Content-Type': "application/json",
                              'Accept': "application/json"}

        try:
            resp = requests.put(ZEPHYR.endpoint_cycles + "/" + cycle_key,
                                json=body,
                                headers=ZEPHYR.post_header)
        except:
            raise "ZEPHYR resource maybe no longer existing. Cycle " + cycle_key

        if resp.ok:
            return True
        else:
            return False

    @staticmethod
    def create_test_execution(cycle_key, tc_key, tc_result):
        """Give me the cycle key + tc_key + tc result. Create execution and set status.

        Return id or None if failed.
        """
        # { "projectKey": "DEV", "testCycleKey": "DEV-R90", "testCaseKey": "DEV-T238", "statusName": "Pass" }
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.post_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer,
                              'Content-Type': "application/json",
                              'Accept': "application/json"}

        data = {"projectKey": ZEPHYR.project_key,
                "testCycleKey": cycle_key,
                "testCaseKey": tc_key,
                "statusName": tc_result}

        if ZEPHYR.environment is not None:
            data["environmentName"] = ZEPHYR.environment

        tc_exec = requests.post(ZEPHYR.endpoint_testexecutions,
                                json=data,
                                headers=ZEPHYR.post_header)

        if tc_exec.ok:
            tc_exec_json = tc_exec.json()
            return tc_exec_json.get("id")

        return None

    @staticmethod
    def get_test_case(tc_key):
        """Give me the test case key.

        Return json with test case information.
        """
        aws_secretmanager = aws_sm.SecretManager()
        ZEPHYR.tm4j_bearer = aws_secretmanager.get_secret("zephyr_api_bearer_token", None)
        ZEPHYR.default_header = {"Authorization": "Bearer " + ZEPHYR.tm4j_bearer}

        tc_data = requests.get(ZEPHYR.endpoint_testcases + "/" + tc_key,
                               headers=ZEPHYR.default_header)

        return tc_data

    @staticmethod
    def get_cycles_older_than(older_than: int) -> list:
        """Return a list with cycle IDs or []."""
        older_than_matches = []
        curr_automated_tests_folderid = ZEPHYR.get_folder_id(ZEPHYR.autotests_folder_name)
        try:
            cycles_in_folder = ZEPHYR.get_cycles(curr_automated_tests_folderid)
            # Example of end date time format
            # "plannedEndDate": "2022-01-03T03:10:15Z"}
            for cycle in cycles_in_folder:
                curr_tc_date = cycle.get("plannedEndDate")
                curr_tc_timestamp = datetime.strptime(curr_tc_date, "%Y-%m-%dT%H:%M:%SZ")
                nowstamp = datetime.now(tz=curr_tc_timestamp.tzinfo)
                diff = nowstamp - curr_tc_timestamp
                if diff.days > older_than:
                    older_than_matches.append(cycle)
        except:
            raise "Something went wrong. Maybe concurrent running tests working on our ZEPHYR resources."
        return older_than_matches

    @staticmethod
    def move_cycles_in_folder(cycles: list, folder: str) -> int:
        """Return number of successful updates."""
        folder_id = ZEPHYR.get_folder_id(folder)
        num_success_updates = 0
        for cycle in cycles:
            key = cycle.get("key")
            id = cycle.get("id")
            name = cycle.get("name")
            project_id = (cycle.get("project")).get("id")
            status_id = (cycle.get("status")).get("id")
            planned_start_date = cycle.get("plannedStartDate")
            planned_end_date = cycle.get("plannedEndDate")

            # Only folder ID is updated/changed
            # To the folders ID where we want to move the cycles into
            body = {
                "key": key,
                "id": id,
                "name": name,
                "project": {
                    "id": project_id
                },
                "status": {
                    "id": status_id
                },
                "folder": {
                    "id": folder_id
                },
                "plannedStartDate": planned_start_date,
                "plannedEndDate": planned_end_date
            }
            try:
                if ZEPHYR.update_cycle(key, body):
                    num_success_updates += 1
            except:
                # Ignore exception, maybe no longer existing. Another test already deleted it?
                pass

        return num_success_updates


# zephyr_handler = ZEPHYR()
#
# return_val = ZEPHYR.create_empty_recording_tc("campaigns/newsletter/email/ready", "kt", "test_campaigns__newsletter__email__ready__navigation_roundtrip_1688633380389")
# print(return_val)


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

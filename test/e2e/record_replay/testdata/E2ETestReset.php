<?php

use App\Klicktipp\Listbuildings;
use App\Klicktipp\MarketingTools;
use App\Klicktipp\Tag;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Reference;
use App\Klicktipp\Emails;
use App\Klicktipp\Signatures;
use App\Klicktipp\DomainSet;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\Campaigns;
use App\Klicktipp\SplitTests;
use App\Klicktipp\Lists;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Statistics;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\UserCache;
use App\Klicktipp\UserVariables;
use App\Klicktipp\VarAdditionalAddresses;

require_once 'E2ETest.php';

class E2ETestReset
{

  private E2ETest $e2e;

  /**
   * @param E2ETest $e2e
   * @throws Exception
   */
  public function __construct(E2ETest $e2e) {
    $this->e2e = $e2e;
  }

  /**
   * Taken from klicktipp module hook user_cancel
   * @return void
   */
  public function execute() {

    // TODO reset roles permissions etc

    $account = $this->e2e->getAccount();
    $UserID = $this->e2e->getUserID();

    // delete all listbuildings
    Listbuildings::DeleteOfUser($UserID);
    MarketingTools::DeleteOfUser($UserID);

    Tag::DeleteTagsOfUser($UserID);
    Subscribers::RemoveSubscribersOfUser($UserID);
    Reference::DeleteOfUser($UserID);

    Emails::DeleteOfUser($UserID);
    Signatures::DeleteOfUser($UserID);

    DomainSet::DeleteWhitelabelDomains($UserID);

    // delete transaction data first, as all other functions are less efficient in doing so
    TransactionEmails::DeleteTransactionEmailsOfUser($UserID);
    Campaigns::DeleteOfUser($UserID);
    SplitTests::DeleteOfUser($UserID);

    Lists::DeleteOfUser($UserID);

    CustomFields::Delete($UserID);

    // stats last
    Statistics::DeleteStatisticsOfUser($UserID);

    kt_delete_rows(array('UserID' => $UserID), '{fbl_reports}');

    //delete user inbound sms numbers
    kt_delete_rows(array('RelOwnerUserID' => $UserID), '{smsnumbers}');

    MetaLabels::DeleteOfUser($UserID);

    UserVariables::DeleteOfUser($UserID);

    $SignatureID = Signatures::CreateDefaultSignature($UserID);
    Signatures::CreateRevision($UserID, $SignatureID);

    // create default subscription process
      $list_id = Lists::InsertDB(array(
        'Name' => '',
        'RelOwnerUserID' => $UserID
      ));

    Reference::createDefaultReference($UserID);
    VarAdditionalAddresses::SetAddresses($account->uid, [['email' => $account->mail, 'verified' => 1, 'valid' => 1]]);

    // clear user cache
    UserCache::clear($UserID);

  }

}

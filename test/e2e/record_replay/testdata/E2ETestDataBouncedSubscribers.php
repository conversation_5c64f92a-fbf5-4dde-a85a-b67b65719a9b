<?php


use App\Klicktipp\BounceEngine;
use App\Klicktipp\Campaigns;
use App\Klicktipp\Lists;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;

require_once 'E2ETest.php';

class E2ETestDataBouncedSubscribers {

  const NAME = "Create Bounce Subscribers";

  const DESCRIPTION = "Create X sets of subscribers with all bounces and subscription status." .
  "(no bounce, soft-bounce, hard-bounce, spam-bounce, spam-complaint) (pending, subscribed, unsubscribed)" .
  "An email and a single DOI will also be created.";

  private E2ETest $e2e;

  public static array $config = [
    [
      'id' => 'count',
      'title' => 'Count',
      'description' => 'How many sets should be created.',
      'value' => '1',
      'formField' => 'number'
    ],
  ];

  /**
   * @param E2ETest $e2e
   *
   * @throws Exception
   */
  public function __construct(E2ETest $e2e) {
    $this->e2e = $e2e;
  }

  public function execute(array $params): array {

    $defaultParams = [];
    foreach (self::$config as $param) {
      $defaultParams[$param['id']] = $param['value'];
    }

    $args = array_merge($defaultParams, $params);

    $userID = $this->e2e->getUserID();

    $now = time();
    $suffix = date('Y-m-d H:i:s', $now);

    $pendingSubscribers = [
      'pending-none-[index]@klicktipp-bounce.com' => 'none',
      'pending-soft-[index]@klicktipp-bounce.com' => 'soft',
      'pending-hard-[index]@klicktipp-bounce.com' => 'hard',
      'pending-s-p-a-m-[index]@klicktipp-bounce.com' => 'spam',
      'pending-s-p-a-m-complaint-[index]@klicktipp-bounce.com' => 'spam-complaint',
    ];

    $subscribedSubscribers = [
      'subscribed-none-[index]@klicktipp-bounce.com' => 'none',
      'subscribed-soft-[index]@klicktipp-bounce.com' => 'soft',
      'subscribed-hard-[index]@klicktipp-bounce.com' => 'hard',
      'subscribed-s-p-a-m-[index]@klicktipp-bounce.com' => 'spam',
      'subscribed-s-p-a-m-complaint-[index]@klicktipp-bounce.com' => 'spam-complaint',
    ];

    $unsubscribedSubscribers = [
      'unsubscribed-none-[index]@klicktipp-bounce.com' => 'none',
      'unsubscribed-soft-[index]@klicktipp-bounce.com' => 'soft',
      'unsubscribed-hard-[index]@klicktipp-bounce.com' => 'hard',
      'unsubscribed-s-p-a-m-[index]@klicktipp-bounce.com' => 'spam',
      'unsubscribed-s-p-a-m-complaint-[index]@klicktipp-bounce.com' => 'spam-complaint',
    ];

    $doubleDOI = Lists::RetrieveDefaultList($userID);
    $doubleDOIEmailId = $doubleDOI['RelOptInConfirmationEmailID'];

    $singleDOI = Lists::InsertDB([
      'RelOwnerUserID' => $userID,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
      'Name' => "E2ETestDataBouncedSubscribers $suffix"
    ]);
    $singleDOI = Lists::FromID($userID, $singleDOI);
    $singleDOI = $singleDOI->GetData();

    $totalSent = count($subscribedSubscribers) + count($unsubscribedSubscribers);
    $newsletter = $this->createNewsletter($totalSent, $now, $suffix);

    if (!empty($newsletter['error'])) {
      return [
        'error' => $newsletter['error']
      ];
    }

    $defaultSubscribeParams = array(
      'UserID' => $userID,
      'ReferenceID' => 0,
      'ListInformation' => array(),
      'OptInSubscribeTo' => $newsletter['triggerTagId'],
      'EmailAddress' => '',
      'PhoneNumber' => '',
      'IPAddress' => '0.0.0.0', //OptIn IP address used for imported subscribers
      'SubscriptionReferrer' => '',
      'SubscriptionStatus' => 0,
      'SMSSubscriptionStatus' => 0,
      'OtherFields' => array(),
      'UpdateIfUnsubscribed' => FALSE,
      'UpdatePhoneNumberIfUnsubscribed' => FALSE,
      'SendConfirmationEmail' => FALSE,
      'UpdateStatistics' => TRUE,
      'TriggerAutoResponders' => FALSE,
      'ListFormID' => 0,
      'ImportOptInDate' => 0, //OptInDate for imported subscribers from CSV file
      'ImportOptInIPAddress' => '', //OptInIP-Address for imported subscribers from CSV file
      'ImportConfirmationDate' => 0, //Confirmation Date for imported subscribers from CSV file
      'UpdatePrimarySubscription' => 0, // Updates primary Subscription, even if another subscription is specified (don't create new subscription with same reference=,
      'Optional' => 0, // is specified subscription optional for given reference
    );

    for ($i=1; $i<=$args['count']; $i++) {

      foreach($pendingSubscribers as $email => $bounceType) {

        $params = $defaultSubscribeParams;
        $params['ListInformation'] = $doubleDOI;
        $params['EmailAddress'] = str_replace('[index]', $i, $email);

        $result = Subscribers::Subscribe($params);
        $subscriberId = $result[1];

        $this->registerBounce($bounceType, $subscriberId, $params['EmailAddress'], $doubleDOIEmailId, 0, $doubleDOI['ListID']);

      }

      foreach($subscribedSubscribers as $email => $bounceType) {

        $params = $defaultSubscribeParams;
        $params['ListInformation'] = $singleDOI;
        $params['EmailAddress'] = str_replace('[index]', $i, $email);

        $result = Subscribers::Subscribe($params);
        $subscriberId = $result[1];

        Subscribers::TagSubscriber($userID, $subscriberId, $newsletter['SentSmartTagID'], 0);

        $this->registerBounce($bounceType, $subscriberId, $params['EmailAddress'], $newsletter['emailId'], $newsletter['campaignId'], $doubleDOI['ListID']);
      }

      foreach($unsubscribedSubscribers as $email => $bounceType) {

        $params = $defaultSubscribeParams;
        $params['ListInformation'] = $singleDOI;
        $params['EmailAddress'] = str_replace('[index]', $i, $email);

        $result = Subscribers::Subscribe($params);
        $subscriberId = $result[1];

        Subscribers::TagSubscriber($userID, $subscriberId, $newsletter['SentSmartTagID'], 0);

        $this->registerBounce($bounceType, $subscriberId, $params['EmailAddress'], $newsletter['emailId'], $newsletter['campaignId'], $doubleDOI['ListID']);

        Subscription::UnsubscribeSubscription($userID, $params['EmailAddress'], Subscription::SUBSCRIPTIONTYPE_EMAIL);

      }

      CampaignsStats::update(
        [
          'TotalRecipients' => $totalSent,
          'TotalSent' => $totalSent,
        ],
        $newsletter['campaignId'],
        $userID
      );

    }

    return [];

  }

  private function createNewsletter(int $totalSent, int $now, string $suffix): array
  {

    $userID = $this->e2e->getUserID();

    $result = [];

    $triggerTagId = \App\Klicktipp\Tag::CreateManualTag($userID,"E2ETestDataBouncedSubscribers $suffix");

    if (!$triggerTagId) {
      $result['error'] = 'Create campaign failed';
      return $result;
    }

    $result['triggerTagId'] = $triggerTagId;

    $campaignId = \App\Klicktipp\CampaignsNewsletterEmail::InsertDB([
      'RelOwnerUserID' => $userID,
      'CampaignName' => "E2ETestDataBouncedSubscribers $suffix",
      'CampaignStatusEnum' => Campaigns::STATUS_SENT,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
      'SendDatetime' => $now,
      // stats
      'SendProcessFinishedOn' => $now + 1,
      // receivers
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => [$triggerTagId => $triggerTagId],
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => [],
      // autoresponder
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_CAMPAIGN,
      'TriggerTimeTypeEnum' => Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY,
      'TriggerTime' => 0,
      'EstimatedRecipients' => $totalSent,
    ]);

    if (!$campaignId) {
      $result['error'] = 'Create campaign failed';
      return $result;
    }

    $objectCampaign = \App\Klicktipp\Campaigns::FromID($userID, $campaignId);

    if (!$objectCampaign) {
      $result['error'] = 'Created campaign could not be retrieved';
      return $result;
    }

    $result['campaignId'] = $campaignId;
    $result['SentSmartTagID'] = $objectCampaign->GetData('SentSmartTagID');

    $emailId = $objectCampaign->GetData('RelEmailID');

    $objectEmail = \App\Klicktipp\Emails::FromID($userID, $emailId);

    if (!$objectEmail) {
      $result['error'] = 'Email of created campaign could not be retrieved';
      return $result;
    }

    $result['emailId'] = $emailId;

    $arrayEmail = $objectEmail->GetData();

    $arrayEmail['Subject'] = "E2ETestDataBouncedSubscribers $suffix";
    $arrayEmail['HTMLContent'] = '<p>This email has not actually been sent. It is only used to register bounces</p>';
    $arrayEmail['PlainContent'] = 'This email has not actually been sent. It is only used to register bounces';

    if (!\App\Klicktipp\Emails::UpdateDB($arrayEmail)) {
      $result['error'] = 'Email of created campaign could not be updated';
      return $result;
    }

    return $result;

  }

  private function registerBounce(string $bounceType, int $subscriberId, string $emailAddress, int $emailId, int $campaignId, int $listId): array
  {

    $userId = $this->e2e->getUserID();

    switch ($bounceType)
    {
      case 'soft':
        BounceEngine::RegisterSoftBounce($userId, $subscriberId, $emailAddress, $emailId, $campaignId, '', TRUE);
        break;
      case 'hard':
        BounceEngine::RegisterHardBounce($userId, $subscriberId, $emailAddress, $emailId, $campaignId, '', TRUE);
        break;
      case 'spam':
        BounceEngine::RegisterSpamBounce($userId, $subscriberId, $emailAddress, $emailId, $campaignId, '');
        break;
      case 'spam-complaint':
        BounceEngine::RegisterSpamComplaint($userId, $subscriberId, $emailAddress, '', $emailId, $campaignId, $listId);
        break;
    }


    return [];

  }

}


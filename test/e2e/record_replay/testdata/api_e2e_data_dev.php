<?php

//https://www.ktlocal.com/test/e2e/record_replay/testdata/api_e2e_data_dev.php

use App\Klicktipp\Cache;

require_once 'E2ETest.php';
require_once 'E2ETestReset.php';
require_once "E2ETestDataUser.php";
require_once "E2ETestDataUserPermissions.php";
require_once 'E2ETestDataTagManual.php';
require_once 'E2ETestDataProcessFlowEmpty.php';

const DATA_CREATION_MAP = [
  'test_complete' => 'test_result_complete',
  'test_progress' => 'test_result_progress',
  'test_server_error' => 'test_result_error',
];

do_bootstrap();

if (strpos(KLICKTIPP_DOMAIN, '.klicktipp.com') !== FALSE) {
  // show a not found to give no indication that this file exists
  drupal_not_found();
  drupal_exit();
}

$result = handle_request();
echo json_encode($result);
exit;

// ----------

function handle_request(): array {

  header('Content-Type: application/json');
  header('Access-Control-Allow-Origin: *');

  try {

    $input = json_decode(file_get_contents("php://input"), true) ?? [];

    $id = (string) $input['id'];
    $username = (string) $input['user'];

    if (isset($input['data'])) {

      $data = $input['data'] ?? [];

      $account = user_load_by_name($username);

      if (!$account || !$account->uid) {

        //clear session
        writeSession('');

        return [
          'id' => $id,
          'state' => 'ERROR',
          'message' => "'User '$username' not found"
        ];
      }

      try {
        $e2e = new E2ETest($username);
      }
      catch (Exception $e) {
        return [
          'id' => $id,
          'state' => 'ERROR',
          'message' => "'User '$username' not allowed."
        ];
      }


      if (empty($id)) {

        //clear session
        writeSession('');

        return [
          'id' => $id,
          'state' => 'ERROR',
          'message' => 'No id given'
        ];
      }

      if (empty($data['testCaseDataCreation'])) {
        // Noting to do

        //clear session
        writeSession('');

        return [
          'id' => $id,
          'state' => 'COMPLETE'
        ];

      }
      else {

        $session = readSession($id);

        return createData($e2e, $id, $data['testCaseDataCreation'], $session);

      }

    }
    else {

      //clear session
      writeSession('');

      return [
        'id' => $id,
        'state' => 'ERROR',
        'message' => 'Bad request: data missing'
      ];

    }

  }
  catch(Exception $e) {
    return [
      'id' => $id,
      'state' => 'ERROR',
      'message' => 'Exception: ' . $e->getMessage()
    ];
  }

}

function createData(E2ETest $e2e, string $id, array $steps, array $session = []): array {

  $currentStep = $session['currentStep'] ?: 0;

  //$e2eReset = new E2ETestReset($e2e);
  //$e2eReset->execute();

  foreach($steps as $index => $step) {

    if ($index < $currentStep) {
      // step has already been executed
      continue;
    }

    $class = $step['class'];
    $params = $step['params'] ?? [];

    if (class_exists($class)) {

      try {

        $e2eData = new $class($e2e);

        $leftover =$e2eData->execute($params);

        writeSession($id, $index, $leftover ?? []);

        if (!empty($leftover)) {

          return [
            'id' => $id,
            'state' => 'PROGRESS'
          ];

        }

      }
      catch(Exception $e) {
        //clear session
        writeSession('');

        return [
          'id' => $id,
          'state' => 'ERROR',
          'message' => $e->getMessage()
        ];
      }

    }
    elseif (function_exists(DATA_CREATION_MAP[$class])) {
      $leftover = call_user_func(DATA_CREATION_MAP[$class], $session['data'] ?: [], $params);

      writeSession($id, $index, $leftover);

      if (!empty($leftover)) {

        return [
          'id' => $id,
          'state' => 'PROGRESS'
        ];

      }

    }
    else {

      //clear session
      writeSession('');

      return [
        'id' => $id,
        'state' => 'ERROR',
        'message' => "Data creation step '$step' not found"
      ];

    }

  }

  //clear session
  writeSession('');

  return [
    'id' => $id,
    'state' => 'COMPLETE',
  ];

}

function readSession(string $id): array {
  return Cache::get("e2e-recorder:$id") ?? [];
}

function writeSession(string $id, int $currentStep = 0, array $data = []) {
  Cache::set("e2e-recorder:$id", [
    'currentStep' => $currentStep,
    'data' => $data
  ]);
}

// --- test functions for dev ---

/**
 * Test function to return complete
 * @param array $data
 * @return array
 */
function test_result_complete(array $data = [], $params = ''): array {

  // No need to do anything
  return [];

}

/**
 * Test function to create progress
 * @param array $data
 * @return array
 */
function test_result_progress(array $data = [], $params = ''): array {

  if (empty($data)) {
    // first time this function is called
    // set a leftover so the return status will be PROGRESS
    return ['some leftover'];
  }

  // the second time this function is call, we will have data from the leftover -> session
  // now return nothing to get a complete
  return [];

}

/**
 * Test function to create a server error (code 408 timeout)
 * @param array $data
 * @return array
 */
function test_result_error(array $data = [], $params = ''): array {
  header("HTTP/1.1 408 DEMO");
  exit;
}


/**
 * @return void
 * @throws Exception
 */
function ui() {

  try {

    global $user;

    $e2e = new E2ETestDataUser($user);

    echo "<hr />";
    echo "<p>Welcome e2e-Tester $user->name</p>";

    echo "<form method='post'>";

    displayRoles($e2e);

    echo "<input type='hidden' name='command' value='submit' />";
    echo "<input type='submit' value='submit' />";
    echo "</form>";

  }
  catch (Exception $e) {
    exitWithError($e->getMessage());
  }

}

function displayRoles(E2ETestDataUser $e2e) {

  echo "<h3>Select the roles the account should have for the test case</h3>";

  $roles = $e2e->getAvailableRoles();

  echo "<div>";
  foreach($roles as $rid => $name) {
    echo "<input type='checkbox' id='role-$rid' name='role[]' value='$rid' /><label for='role-$rid'>$name</label>";
  }

  echo "</div>";

}

function exitWithError($message) {
  echo "<p>Error: $message</p>";
  exit;
}

/**
 * @param $code
 * @param $message
 * @return void
 * @throws Exception
 */
function response($code, $message) {
  echo "<p>$code: $message</p>";
  ui();
  exit;
}


function do_bootstrap() {

  // bootstrap
  // bootstrap as if we were in drupal root
  $oldDir = getcwd();
  chdir('../../../../');

  // drupal bootstrap, see index.php
  ini_set('display_errors', 0);
  define('DRUPAL_ROOT', getcwd());

  require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
  drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);
  ini_set('display_errors', 1);

  // get back to calling environment
  chdir($oldDir);

}

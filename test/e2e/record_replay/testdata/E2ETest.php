<?php

class E2ETest
{

  const DEV_USERNAME_PREFIX = 'e2e-test-';

  // TODO: during dev, the current test is stored in a drupal_variable
  //       @see LoadCurrentTest(), SaveCurrentTest(), ResetCurrentTest()
  const VAR_NAME_CURRENT_TEST = 'e2e-recorder-test';

  protected stdClass $account;

  private array $defaultTest = [
    'meta' => [
      'name' => '',
      'description' => '',
    ],
    'user' => [
      'role' => 0
    ],
    'data' => []
  ];

  /**
   * @param string $username
   * @throws Exception
   */
  public function __construct(string $username)
  {

    $this->_bootstrap();

    if (strpos(KLICKTIPP_DOMAIN, '.klicktipp.com') !== FALSE) {
      // show a not found to give no indication that this file exists
      drupal_not_found();
      drupal_exit();
    }

    if (strpos($username, self::DEV_USERNAME_PREFIX) !== 0) {
      // Access is done by username since permissions of the user could change for test cases
      throw new Exception("Access denied: Use user with name prefix " . self::DEV_USERNAME_PREFIX);
    }

    $account = user_load_by_name($username);
    if (!$account || !$account->uid) {
      throw new Exception("Access denied: User not found '$username'");
    }

    $this->account = $account;

  }

  private function _bootstrap() {
    // bootstrap
    // bootstrap as if we were in drupal root
    $oldDir = getcwd();
    chdir('../../../../');

    // drupal bootstrap, see index.php
    ini_set('display_errors', 0);
    define('DRUPAL_ROOT', getcwd());

    require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
    drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);
    ini_set('display_errors', 1);

    // get back to calling environment
    chdir($oldDir);
  }

  public function getAccount(): stdClass {
    return $this->account;
  }

  public function getUserID(): int {
    return $this->account->uid;
  }

  public function LoadCurrentTest() {
    return variable_get(static::VAR_NAME_CURRENT_TEST, $this->defaultTest);
  }

  public function SaveCurrentTest($key, $data): array {

    $test = $this->LoadCurrentTest();

    if (empty($key)) {
      $test = $data;
    }
    else {
      $test[$key] = $data;
    }

    variable_set(static::VAR_NAME_CURRENT_TEST, $test);

    return $test;

  }

  public function ResetCurrentTest() {
    variable_del(static::VAR_NAME_CURRENT_TEST);
    return $this->defaultTest;
  }

}

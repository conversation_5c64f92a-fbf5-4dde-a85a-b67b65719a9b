class ReportServerController {
  String browser = "";

  List<String> getBrowsers() {
    return [
      "chrome",
      "firefox",
      "chrome_macos",
      "firefox_win",
      "safari_macos",
      "edge",
      "safari_ipad",
    ];
  }

  String? getBrowserPort(String browser) {
    return {
      "chrome": ":82",
      "firefox": ":83",
      "safari_ipad": ":84",
      "chrome_macos": ":85",
      "firefox_win": ":86",
      "safari_macos": ":87",
      "edge": ":88",
    }[browser];
  }

  void setBrowser(String browser) {
    // set selected browser for execution
    this.browser = browser;
  }
}

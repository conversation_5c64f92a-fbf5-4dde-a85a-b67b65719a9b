import 'package:flutter/material.dart';

class DropDownV2 extends StatefulWidget {
  final Function updateValue;
  final List<String> values;
  final String initValue;
  const DropDownV2(
      {Key? key,
      required this.initValue,
      required this.updateValue,
      required this.values})
      : super(key: key);

  @override
  DropDownV2State createState() => DropDownV2State();
}

class DropDownV2State extends State<DropDownV2> {
  String dropdownvalue = 'val2';
  var items = ["val1", "val2", "val3"];
  List<DropdownMenuItem> dropDownMenuItems = [
    const DropdownMenuItem(
      value: "item1",
      child: Text("item1"),
    )
  ];

  void updateValues(List<String> newValues) {
    dropDownMenuItems = newValues.map((String items) {
      return DropdownMenuItem(
        value: items,
        child: Text(items),
      );
    }).toList();
    setState(() {
      dropDownMenuItems;
    });
    updateValue(newValues[0]);
  }

  void updateValue(var newValue) {
    setState(() {
      dropdownvalue = newValue!;
    });
    widget.updateValue(newValue);
  }

  @override
  void initState() {
    super.initState();
    print('initState Called');
    dropdownvalue = widget.initValue;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 15,
      child: DropdownButton(
        // Initial Value
        value: dropdownvalue,
        isExpanded: true,
        // Down Arrow Icon
        icon: const Icon(Icons.keyboard_arrow_down),

        // style: TextStyle(
        //   fontSize: 14,
        //   color: Colors.black,
        //   backgroundColor: Colors.orangeAccent,
        // ),
        underline: Container(),

        // Array list of items
        items: dropDownMenuItems,
        // After selecting the desired option,it will
        // change button value to selected value
        onChanged: updateValue,
      ),
    );
  }
}

import 'package:flutter/material.dart';

import 'tabs/current_recording.dart';
import 'tabs/specific_execution.dart';

void main() {
  runApp(const WebApp());
}

class WebApp extends StatelessWidget {
  const WebApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(
        // Define the default brightness and colors.
        brightness: Brightness.dark,
        primaryColor: Colors.indigo[900],

        // Define the default font family.
        fontFamily: 'Georgia',

        // Define the default `TextTheme`. Use this to specify the default
        // text styling for headlines, titles, bodies of text, and more.
        textTheme: const TextTheme(
          headline1: TextStyle(fontSize: 72.0, fontWeight: FontWeight.bold),
          headline6: TextStyle(fontSize: 36.0, fontStyle: FontStyle.italic),
          bodyText2: TextStyle(fontSize: 14.0, fontFamily: 'Hind'),
        ),
        buttonTheme: const ButtonThemeData(
          height: 46,
          minWidth: 200,
        ),
        textButtonTheme: TextButtonThemeData(
          style: ButtonStyle(
              minimumSize: MaterialStateProperty.all(
            const Size(90, 30),
          )),
        ),
        inputDecorationTheme: const InputDecorationTheme(
          contentPadding: EdgeInsets.all(5.0),
          isDense: true,
          // border: InputBorder.none,  // Disable Underline
        ),
      ),
      home: SafeArea(
        child: DefaultTabController(
          length: 2,
          child: Scaffold(
            appBar: AppBar(
              bottom: const TabBar(
                tabs: [
                  Tab(icon: Icon(Icons.warning_amber_rounded)),
                  Tab(icon: Icon(Icons.videogame_asset_off_rounded)),
                ],
              ),
              toolbarHeight: 0,
            ),
            body: const TabBarView(
              children: [
                CurrentRecording(),
                SpecificExecution(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

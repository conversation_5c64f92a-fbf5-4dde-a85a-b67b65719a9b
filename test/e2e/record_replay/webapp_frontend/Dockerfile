# Provide nginx webserver
FROM nginx:stable

# Set directory to Copy App
WORKDIR /app

# Prerequisites
RUN apt update && apt install -y curl git unzip xz-utils zip libglu1-mesa

# Download Flutter SDK
RUN git clone https://github.com/flutter/flutter.git \
    && cd flutter \
    && git checkout 2.10.4
ENV PATH "$PATH:/app/flutter/bin:/app/flutter/bin/cache/dart-sdk/bin"

# Copy Flutter App into Image
COPY . .
RUN flutter clean
RUN flutter doctor
RUN flutter config --enable-web
RUN flutter pub get
RUN flutter doctor
RUN flutter build web --release --dart-define TITLE=${TITLE:-}

# Copy nginx config
COPY nginx/nginx.conf /etc/nginx/conf.d/default.conf
RUN chmod +x /app/entrypoint.sh

RUN chmod -R 755 /usr/share/nginx/html
RUN cp -r /app/build/web/* /usr/share/nginx/html

# Open http port
EXPOSE 80

# Set custom entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]

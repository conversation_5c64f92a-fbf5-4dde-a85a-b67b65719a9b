[[_TOC_]]

# How to develop and build this frontend

Versions you should use:

`flutter 2.10.4`

`chrome < v.100.x` (Versionen >= 100 konnte ich nicht mehr debuggen. Attach auf chrome hat nicht mehr funktioniert. Ob außerhalb oder innerhalb der IDE.)

## intellij IDEA Ultimate

### Installation of Flutter SDK

Get the correct version from here:
- https://docs.flutter.dev/development/tools/sdk/releases

Follow the installation steps for your platform:
- https://docs.flutter.dev/get-started/install

Ensure:
- Add flutter `bin` to your `$PATH` (e.g. into the `.bashrc`)
- Run `flutter doctor -v` and note the path to your SDK.
  e.g. below my path `/home/<USER>/snap/flutter/common/flutter`

```
> flutter doctor -v

[✓] Flutter (Channel stable, 2.10.4, on Ubuntu 21.10 5.13.0-39-generic, locale en_US.UTF-8)
• Flutter version 2.10.4 at /home/<USER>/snap/flutter/common/flutter
```

Test your Environment:
```
> flutter doctor -v

[✓] Flutter (Channel stable, 2.10.4, on Ubuntu 21.10 5.13.0-39-generic, locale en_US.UTF-8)
• Flutter version 2.10.4 at /home/<USER>/snap/flutter/common/flutter
• Upstream repository https://github.com/flutter/flutter.git
• Framework revision c860cba910 (10 days ago), 2022-03-25 00:23:12 -0500
• Engine revision 57d3bac3dd
• Dart version 2.16.2
• DevTools version 2.9.2

[✗] Android toolchain - develop for Android devices
✗ Unable to locate Android SDK.
Install Android Studio from: https://developer.android.com/studio/index.html
On first launch it will assist you in installing the Android SDK components.
(or visit https://flutter.dev/docs/get-started/install/linux#android-setup for detailed instructions).
If the Android SDK has been installed to a custom location, please use
`flutter config --android-sdk` to update to that location.

[✓] Chrome - develop for the web
• Chrome at google-chrome

[✓] Linux toolchain - develop for Linux desktop
• clang version 6.0.0-1ubuntu2 (tags/RELEASE_600/final)
• cmake version 3.10.2
• ninja version 1.8.2
• pkg-config version 0.29.1

[!] Android Studio (not installed)
• Android Studio not found; download from https://developer.android.com/studio/index.html
(or visit https://flutter.dev/docs/get-started/install/linux#android-setup for detailed instructions).

[✓] IntelliJ IDEA Ultimate Edition (version 2021.3)
• IntelliJ at /home/<USER>/.local/share/JetBrains/Toolbox/apps/IDEA-U/ch-0/213.7172.25
• Flutter plugin can be installed from:
🔨 https://plugins.jetbrains.com/plugin/9212-flutter
• Dart plugin can be installed from:
🔨 https://plugins.jetbrains.com/plugin/6351-dart

[✓] VS Code (version 1.66.0)
• VS Code at /usr/share/code
• Flutter extension version 3.36.0

[✓] Connected device (2 available)
• Linux (desktop) • linux  • linux-x64      • Ubuntu 21.10 5.13.0-39-generic
• Chrome (web)    • chrome • web-javascript • Google Chrome 96.0.4664.45

[✓] HTTP Host Availability
• All required HTTP hosts are available

! Doctor found issues in 2 categories.
```

### Install the plugin in intellij IDEA Ultimate

- Open IDEA
- Got to `File->Settings->Plugins`
- Search for the flutter plugin and install the plugin

# To work at the frontend ...

- Open IDEA 
- Open the IDEA project in `<KT-Root>/test`
- Open `File->settings`
- Open `Languages & Frameworks -> Flutter` in the dialog
- Adjust the flutter SDK path

import 'package:flutter/material.dart';
import 'package:ui/entities/dataset.dart';
import 'package:ui/singletons/app_data.dart';

class DatasetOverviewWidget extends StatefulWidget {
  const DatasetOverviewWidget({
    Key? key,
    required this.datasets,
    required this.modeEdit,
  }) : super(key: key);

  final List<Dataset> datasets;
  final bool modeEdit;

  @override
  _DatasetOverviewWidget createState() => _DatasetOverviewWidget();
}

class _DatasetOverviewWidget extends State<DatasetOverviewWidget> {

  String searchText = '';

  final TextEditingController _textFieldController = TextEditingController();

  late List<Dataset> filtered;

  @override
  void initState() {
    super.initState();
    filtered = widget.modeEdit ? appData.recording.getDatasets() : widget.datasets;
  }

  @override
  Widget build(BuildContext context) {


    //List<Dataset> filtered = appData.recording.getDatasets();

    if (filtered.isNotEmpty && searchText != '') {

      List<String> terms = searchText.split(' ');

      filtered = filtered.where((item) {

        String text = '${item.id} ${item.name} ${item.description}';

        return terms.every((item) => text.toLowerCase().contains(item));

      }).toList();

    }

    List<Widget> items = filtered.map<Widget>((item) {
      return ExpansionTile(
        title: Row(
          children: [
            Text(item.name),
            if (widget.modeEdit) const Spacer(),
            if (widget.modeEdit) const ButtonBar(
              children: [
                ElevatedButton(
                  onPressed: null,
                  child: Text('Edit'),
                ),
                ElevatedButton(
                  onPressed: null,
                  child: Text('Remove'),
                ),
              ],
            ),
          ],
        ),
        children: [
          Text(item.description),
        ],
      );
    }).toList();

    return CustomScrollView(
      shrinkWrap: true,
      slivers: <Widget>[
        SliverAppBar(
          floating: false,
          pinned: true,
          snap: false,
          centerTitle: false,
          // when a Bottom-Sheet is shown, do not show a back button in the app bar
          automaticallyImplyLeading: false,
          title: widget.modeEdit ? ButtonBar(
            alignment: MainAxisAlignment.start,
            children: [
              const Text('Datasets'),
              ElevatedButton(
                child: const Text('Add dataset'),
                onPressed: () {
                  addDataset(context, widget.datasets);
                },
              ),
              const ElevatedButton(
                onPressed: null,
                child: Text('Remove all'),
              ),
            ],
          ) : const Text("Add dataset"),
          actions: widget.modeEdit ? const [
            IconButton(
              icon: Icon(Icons.refresh),
              onPressed: null
              /*
              onPressed: () {
                futureTestIndex = _futureFetchTestIndex();
              },
              */
            ),
          ] : [],
          bottom: AppBar(
            // when a Bottom-Sheet is shown, do not show a back button in the app bar
            automaticallyImplyLeading: false,
            title: Container(
              width: double.infinity,
              height: 40,
              color: Colors.white,
              child: Center(
                child: TextField(
                  controller: _textFieldController,
                  decoration: InputDecoration(
                    hintText: 'Filter by name or description',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _textFieldController.clear();
                          searchText = '';
                        });
                      },
                      icon: const Icon(Icons.clear),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      searchText = value;
                    });
                  },
                ),
              ),
            ),
          ),
        ),
        SliverPadding(
          padding: const EdgeInsets.all(20.0),
          sliver: SliverList(
            delegate: SliverChildListDelegate(
                filtered.isEmpty ? const [Text("No datasets")] : items
            ),
          ),
        ),
      ],
    );
  }

  Future<void> addDataset(BuildContext context, List<Dataset> datasets) async {

    //DataSetOverviewWidget(datasets: datasets, modeEdit: false),

    return showModalBottomSheet<void>(
      isDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return DatasetOverviewWidget(datasets: datasets, modeEdit: false);
      },
    );

  }

  onAddDataset(Dataset dataset) {
    setState(() {

      final sets = appData.recording.getDatasets();
      sets.add(dataset);
      appData.recording.execData.testCaseDataCreation = sets;
    });

  }

}

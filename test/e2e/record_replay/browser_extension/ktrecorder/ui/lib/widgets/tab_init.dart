import 'package:flutter/material.dart';
import 'package:ui/entities/recording_index_item.dart';
import 'package:ui/entities/recording.dart';
import 'dart:convert';

import 'package:ui/singletons/app_data.dart';

import 'package:http/http.dart' as http;

import '../entities/dynamic_form.dart';
import '../entities/persistentData/persistent_exec_user.dart';
import '../entities/test_execute.dart';
import '../entities/test_retrieve_response.dart';
import '../futures/backend.dart';
import '../futures/environment.dart';
import 'dynamic_form_renderer.dart';

class TabInitWidget extends StatefulWidget {
  const TabInitWidget({
    Key? key,
    required this.navigateToTab
  }) : super(key: key);

  final Function navigateToTab;

  @override
  _TabInitWidgetState createState() => _TabInitWidgetState();
}

class _TabInitWidgetState extends State<TabInitWidget> {

  late Future<List<RecordingIndexItem>> futureTestIndex;
  late Future<Recording> futureTestLoad;

  String searchText = '';

  final TextEditingController _textFieldController = TextEditingController();

  late DynamicForm formExecuteTest;

  late List<RecordingIndexItem> filtered;

  @override
  void initState() {
    super.initState();
    futureTestIndex = _futureFetchTestIndex();
  }

  @override
  Widget build(BuildContext context) {

    return FutureBuilder<dynamic>(
        future: futureTestIndex,
        builder: (context, snapshot) {

          if (!snapshot.hasData) {
            return const Center(
                child: Text('Loading...')
            );
          }

          filtered = snapshot.data;
          filtered.sort((a,b) => a.displayName.compareTo(b.displayName));

          if (filtered.isNotEmpty && searchText != '') {

            List<String> terms = searchText.split(' ');

            filtered = filtered.where((item) {

              String text = '${item.displayName} ${item.description}';

              return terms.every((item) => text.toLowerCase().contains(item));

            }).toList();

          }

          List<Widget> items = filtered.map<Widget>((item) {
            return ExpansionTile(
              title: Row(
                children: [
                  Text(item.displayName),
                  const Spacer(),
                  ButtonBar(
                    children: [
                      ElevatedButton(
                        child: const Text('Load'),
                        onPressed: () {
                          loadTest(item.id);
                        },
                      ),
                      const ElevatedButton(
                        onPressed: null,
                        child: Text('Create from'),
                        /*
                        onPressed: () {
                          createTestFrom(item.id);
                        },
                        */
                      ),
                      ElevatedButton(
                        child: const Text('Run test'),
                        onPressed: () {
                          onRunTest(context, [item.id]);
                        },
                      ),
                    ],
                  ),
                ],
              ),
              children: [
                Text(item.description)
              ],
            );
          }).toList();

          return CustomScrollView(
            shrinkWrap: true,
            slivers: <Widget>[
              SliverAppBar(
                floating: false,
                pinned: true,
                snap: false,
                centerTitle: false,
                title: ButtonBar(
                  alignment: MainAxisAlignment.start,
                  children: [
                    ElevatedButton(
                      onPressed: createNewTest,
                      child: const Text('Create new Test from scratch'),
                    ),
                    const ElevatedButton(
                      onPressed: null,
                      child: Text('Import'),
                    ),
                  ],
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: () {
                      futureTestIndex = _futureFetchTestIndex();
                    },
                  ),
                ],
                bottom: AppBar(
                  title: Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 40,
                          margin: const EdgeInsets.only(right: 8),
                          child: TextField(
                            controller: _textFieldController,
                            decoration: InputDecoration(
                              hintText: 'Filter by name or description',
                              filled: true,
                              fillColor: Colors.white,
                              prefixIcon: const Icon(Icons.search),
                              suffixIcon: IconButton(
                                onPressed: () {
                                  setState(() {
                                    _textFieldController.clear();
                                    searchText = '';
                                  });
                                },
                                icon: const Icon(Icons.clear),
                              ),
                            ),
                            onChanged: (value) {
                              setState(() {
                                searchText = value;
                              });
                            },
                          ),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          List<int> ids = filtered.map<int>((item) => item.id).toList();
                          onRunTest(context, ids);
                        },
                        child: const Text('Run filtered Tests'),
                      ),
                    ]
                  ),
                ),
              ),
              SliverPadding(
                padding: const EdgeInsets.all(20.0),
                sliver: SliverList(
                  delegate: SliverChildListDelegate(items),
                ),
              ),
            ],
          );


        }
    );

  }

  createNewTest() {
    setState(() {
      appData.recording.createRecording();
      appData.recording.toExtension(true);
    });
    widget.navigateToTab(1);
  }

  loadTest(int id) {
    _futureFetchTest(id).then((recording) {
      setState(() {
        appData.recording = recording;
      });
      widget.navigateToTab(3);
    });
  }

  createTestFrom(int id) {
    _futureFetchTest(id).then((recording) {
      setState(() {
        recording.clone();
        appData.recording = recording;
      });
      widget.navigateToTab(1);
    });
  }

  onRunTest(BuildContext context, List<int> ids) {

    bool first = true;
    FutureBackend backend = FutureBackend();
    Future<List<TestRetrieveResponse>> futureRetrieveResponse = backend.testRetrieveMultiple(ids);

    late final TestExecute execTest;
    late List<TestRetrieveResponse> retrieveResponses;
    String errorMessage = '';

    Widget content = const Text('...');

    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
            builder: (context, setState) {

              return AlertDialog(
                content: FutureBuilder<dynamic>(
                  future: futureRetrieveResponse,
                  builder: (context, snapshot) {

                    if (snapshot.hasData && first) {

                      first = false;

                      retrieveResponses = snapshot.data;

                      if (retrieveResponses.isNotEmpty) {
                        execTest = TestExecute(machineName: '', testCaseName: '');
                        formExecuteTest = execTest.convertToForm();
                        content = DynamicFormRenderer(form: formExecuteTest, modeEdit: true, updateCallback: onUpdateFormExecuteTest);
                      }

                    } else if (snapshot.hasError) {
                      content = Text('ERROR: ${snapshot.error}');
                    }

                    return content;

                  },
                ),
                actions: <Widget>[
                  if (errorMessage != '') Text(
                      errorMessage,
                      style: const TextStyle(
                          color: Colors.red
                      )
                  ),
                  ElevatedButton(
                    //color: Colors.green,
                    //textColor: Colors.white,
                    child: const Text('Run'),
                    onPressed: () async {

                      bool accessChecked = false;
                      bool valid = true;
                      for(TestRetrieveResponse response in retrieveResponses) {
                        TestExecute exec = TestExecute(testCaseName: response.testCaseName, machineName: response.machineName);
                        exec.convertFromForm(formExecuteTest);

                        final env = FutureEnvironment();

                        if (!accessChecked && exec.appLocation == 'remote') {
                          accessChecked = true;
                          valid = await env.checkLogin(exec.domain, exec.execUser, exec.execUserPwd);
                          if (valid) {
                            appData.environment.setCurrentUser(
                                PersistentExecUser(
                                    username: exec.execUser,
                                    password: exec.execUserPwd,
                                    domain: exec.domain
                                )
                            );
                          }
                        }

                        if (valid) {
                          appData.environment.addTestCase(exec);
                        }

                      }

                      setState(() {

                        if (valid) {
                          Navigator.pop(context);
                        }
                        else {
                          errorMessage = 'Login failed.';
                        }

                      });
                    },
                  ),
                ],
              );
            });
      });

  }

  onLoadTest(BuildContext context, int id) {

    bool first = true;
    FutureBackend backend = FutureBackend();
    Future<TestRetrieveResponse> futureRetrieveResponse = backend.testRetrieve(id);

    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
              builder: (context, setState) {

                return AlertDialog(
                  content: FutureBuilder<dynamic>(
                    future: futureRetrieveResponse,
                    builder: (context, snapshot) {

                      Widget content = const Text('Loading Test...');

                      if (snapshot.hasData && first) {

                        first = false;

                        TestRetrieveResponse retrieveResponse = snapshot.data;

                        if (false && retrieveResponse.hasError()) { // TODO remove false
                          content = Column(
                            children: [
                              const Text('An error occurred!'),
                              Text('Status code: ${retrieveResponse.statusCode}'),
                            ],
                          );
                        }
                        else {
                          setState(() {
                            appData.recording = retrieveResponse.getRecording();
                            Navigator.pop(context);
                          });
                        }

                      } else if (snapshot.hasError) {
                        content = Text('ERROR: ${snapshot.error}');
                      }

                      return content;

                    },
                  ),
                );
              });
        });

  }

  // TODO old, remove
  Future<List<RecordingIndexItem>> _futureFetchTestIndex() async {

    List<RecordingIndexItem> result = [];

    final response = await http
        .get(Uri.parse('http://127.0.0.1:5006/tests'));

    if (response.statusCode == 200 ) {

      final index = jsonDecode(response.body) as List<dynamic>;

      if (index.isNotEmpty) {

        for (var item in index) {
          result.add(RecordingIndexItem.fromJsonObject(Map.castFrom(item)));
        }

      }

    }

    appData.testIndex = result;

    return result;

  }

  Future<Recording> _futureFetchTest(int id) async {

    final response = await http
        .get(Uri.parse('http://127.0.0.1:5006/test/${id.toString()}'));

    if (response.statusCode == 200 ) {
      return Recording.fromJsonString(response.body);
    }

    throw Exception('Failed to load recording with id: ${id.toString()}');

  }

  onUpdateFormExecuteTest(DynamicForm form) {
    setState(() {
      formExecuteTest = form;
    });
  }

}

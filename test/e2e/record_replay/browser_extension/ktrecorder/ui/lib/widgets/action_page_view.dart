import 'package:flutter/material.dart';
import 'package:ui/entities/dynamic_form.dart';
import 'package:ui/entities/execute.dart';
import 'package:ui/entities/recording_step.dart';

import '../entities/action.dart';

import 'package:ui/singletons/app_data.dart';

import 'assert_value.dart';


class ActionPageViewWidget extends StatefulWidget {
  const ActionPageViewWidget({
    Key? key,
    required this.navigateToTab
  }) : super(key: key);

  final Function navigateToTab;

  @override
  _ActionPageViewState createState() => _ActionPageViewState();
}

class _ActionPageViewState extends State<ActionPageViewWidget> {

  late List<ActionWidget> _pages;

  late RecordingStep currentAction;

  final PageController _controller = PageController(initialPage: appData.currentPage);

  late List<Map<String, dynamic>> bottomNavConfig;

  late List<Execute> executeOptions;

  // stores the updated forms of the actions execute options
  Map<String, DynamicForm> updatedActionExecuteOptions = {};
  Map<String, bool> updatedActionExecuteOptionActiveStates = {};

  @override
  void initState() {
    super.initState();

    var actions = appData.recording.getDisplayableSteps();

    if (actions.isNotEmpty) {
      _pages = actions.map<ActionWidget>((action) => ActionWidget(action: action)).toList();
      currentAction = actions[appData.currentPage];
    }
    else {
      _pages = [];
    }

    bottomNavConfig = [
      {
        'icon': (int curPage, int totalPages, RecordingStep action) {
          return (curPage == 0) ? const Icon(
              Icons.navigate_before_rounded,
              color: Colors.grey
          ) :  const Icon(
            Icons.navigate_before_rounded,
            color: Colors.blue
          );
        },
        'label': (int curPage, int totalPages, RecordingStep action) {
          return 'Previous Action';
        },
        'onTab': previousAction
      },
      {
        'icon': (int curPage, int totalPages, RecordingStep action) {

          if (action.hasNotes()) {
            return const Icon(
                Icons.message_outlined,
                color: Colors.green
            );
          }

          return const Icon(
              Icons.message_outlined,
              color: Colors.blue
          );

        },
        'label': (int curPage, int totalPages, RecordingStep action) {
          return 'Notes';
        },
        'onTab': editNote
      },
      {
        'icon': (int curPage, int totalPages, RecordingStep action) {

          if (action.isAssertStep()) {
            return const Icon(
                Icons.screen_search_desktop_outlined,
                color: Colors.green
            );
          }

          return const Icon(
              Icons.screen_search_desktop_outlined,
              color: Colors.grey
          );

        },
        'label': (int curPage, int totalPages, RecordingStep action) {
          return 'Assert Options';
        },
        'onTab': assertOptions
      },
      {
        'icon': (int curPage, int totalPages, RecordingStep action) {

          if (action.isDismissed()) {
            return const Icon(
                Icons.undo_rounded,
                color: Colors.red
            );
          }

          return const Icon(
              Icons.delete_forever_outlined,
              color: Colors.blue
          );

        },
        'label': (int curPage, int totalPages, RecordingStep action) {
          return 'Dismiss Action';
        },
        'onTab': dismissAction
      },
      {
        'icon': (int curPage, int totalPages, RecordingStep action) {
          return (curPage == totalPages) ? const Icon(
              Icons.info_outline_rounded,
              color: Colors.blue
          ) : const Icon(
              Icons.navigate_next_rounded,
              color: Colors.blue
          );
        },
        'label': (int curPage, int totalPages, RecordingStep action) {
          return (curPage == totalPages) ? 'Meta data' : 'Next Action';
        },
        'onTab': nextAction
      },
    ];

  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    if (_pages.isEmpty) {
      return const Text('No actions...');
    }

    return Scaffold(
      body: PageView(
        controller: _controller,
        physics: const NeverScrollableScrollPhysics(),
        children: _pages,
      ),
      bottomNavigationBar: getBottomNavigator(context)
    );
  }

  previousAction(BuildContext context) {

    if (appData.currentPage == 0) {
      return;
    }

    setState(() {

      appData.recording.setStep(appData.currentPage, currentAction);
      appData.currentPage--;

      currentAction = appData.recording.getStep(appData.currentPage);

    });

    _controller.previousPage(
        duration: const Duration(milliseconds: 100),
        curve: Curves.linear
    );
  }

  nextAction(BuildContext context) {

    if (appData.currentPage == _pages.length - 1) {
      setState(() {
        widget.navigateToTab(3);
      });
      return;
    }

    setState(() {

      appData.recording.setStep(appData.currentPage, currentAction);
      appData.currentPage++;

      currentAction = appData.recording.getStep(appData.currentPage);

    });

    _controller.nextPage(
        duration: const Duration(milliseconds: 100),
        curve: Curves.linear
    );
  }

  dismissAction(BuildContext context) {

    setState(() {
      currentAction.toggleDismiss();
      appData.recording.setStep(appData.currentPage, currentAction);
    });

    if (appData.currentPage == _pages.length - 1) {
      return;
    }

    setState(() {
      appData.currentPage++;
      currentAction = appData.recording.getStep(appData.currentPage);
    });

    // the user dismissed the action, go to next action
    _controller.nextPage(
        duration: const Duration(milliseconds: 100),
        curve: Curves.linear
    );

  }

  editNote(BuildContext context) {
    _displayTextInputDialog(context, currentAction);
  }

  assertOptions(BuildContext context) {
    //if (currentAction.isAssertStep()) {
      _displayAssertOptions(context, currentAction);
    //}
  }

  BottomNavigationBar? getBottomNavigator(BuildContext context) {

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      items: bottomNavConfig.map((item) {

        final label = item['label'](appData.currentPage, _pages.length - 1, currentAction);
        final icon = item['icon'](appData.currentPage, _pages.length - 1, currentAction);

        return BottomNavigationBarItem(
          icon: icon,
          label: label,
          tooltip: label,
        );

      }).toList(),
      //selectedItemColor: Colors.amber[800],
      onTap: (index) {
        bottomNavConfig[index]['onTab'](context);
      },
      showSelectedLabels: false,
      showUnselectedLabels: false,
      currentIndex: 0, //can be always 0 since we do not highlight the selected tab
    );

  }

  Future<void> _displayTextInputDialog(BuildContext context, RecordingStep action) async {

    final TextEditingController textFieldController = TextEditingController();

    String tmpInput = action.getNotes();

    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          content: TextField(
            onChanged: (value) {
              tmpInput = value;
            },
            controller: textFieldController..text = action.getNotes(),
            decoration: const InputDecoration(hintText: "Insert a description for this action..."),
            minLines: 10,
            maxLines: 10,
          ),
          actions: <Widget>[
            TextButton(
              //color: Colors.green,
              //textColor: Colors.white,
              child: const Text('Clear'),
              onPressed: () {
                setState(() {
                  textFieldController.text = '';
                });
              },
            ),
            TextButton(
              //color: Colors.red,
              //textColor: Colors.white,
              child: const Text('CANCEL'),
              onPressed: () {
                setState(() {
                  Navigator.pop(context);
                });
              },
            ),
            TextButton(
              //color: Colors.green,
              //textColor: Colors.white,
              child: const Text('OK'),
              onPressed: () {
                setState(() {
                  action.setNotes(tmpInput);
                  Navigator.pop(context);
                });
              },
            ),
          ],
        );
      });
  }

  Future<void> _displayAssertOptions(BuildContext context, RecordingStep action) async {

    // copy the list, so changed will only applied on save
    executeOptions = List<Execute>.from(action.getExecuteOptions());
    updatedActionExecuteOptions = {};
    updatedActionExecuteOptionActiveStates = {};

    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {

            List<ExpansionTile> tiles = executeOptions.map<ExpansionTile>((option) {

              // TODO how to get the changes out
              // TODO do I really need children?
              List<Widget> children = [
                ExecuteOption(
                    id: option.getCommandId(),
                    form: option.convertToForm(),
                    updateCallback: updateExecuteOptions)
              ];

              Widget title = Text(option.getTitle());
              if (action.isAssertStep()) {
                title = CheckboxListTile(
                  title: Text(option.getTitle()),
                  value: updatedActionExecuteOptionActiveStates.containsKey(option.getCommandId()) ? updatedActionExecuteOptionActiveStates[option.getCommandId()] : option.getActive(),
                  onChanged: (bool? value) {
                    setState(() {
                      updateExecuteOptionActiveState(option.getCommandId(), value!);
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,  //  <-- leading Checkbox
                );
              }

              return ExpansionTile(
                title: title,
                initiallyExpanded: !action.isAssertStep(),
                children: children,
              );

            }).toList();

            return AlertDialog(
              content: SingleChildScrollView(
                  child: Column(
                    children: tiles,
                  )
              ),
              actions: <Widget>[
                TextButton(
                  //color: Colors.red,
                  //textColor: Colors.white,
                  child: const Text('Cancel'),
                  onPressed: () {
                    setState(() {
                      Navigator.pop(context);
                    });
                  },
                ),
                TextButton(
                  //color: Colors.green,
                  //textColor: Colors.white,
                  child: const Text('OK'),
                  onPressed: () {
                    setState(() {
                      saveExecuteOptions(action);
                      Navigator.pop(context);
                    });
                  },
                ),
              ],
            );
        });
      });
  }

  updateExecuteOptions(String id, DynamicForm form) {
    updatedActionExecuteOptions[id] = form;
  }

  updateExecuteOptionActiveState(String id, bool isActive) {
    updatedActionExecuteOptionActiveStates[id] = isActive;
  }

  saveExecuteOptions(RecordingStep action) {

    updatedActionExecuteOptions.forEach((id, form) {
      final index = executeOptions.indexWhere((e) => e.getCommandId() == id);

      if (index != -1) {
        executeOptions[index].convertFromForm(form);
      }
    });

    updatedActionExecuteOptionActiveStates.forEach((id, isActive) {
      final index = executeOptions.indexWhere((e) => e.getCommandId() == id);

      if (index != -1) {
        executeOptions[index].setActive(isActive);
      }
    });

    action.setExecuteOptions(executeOptions);

  }

}

import 'package:flutter/material.dart';
import 'package:ui/entities/dynamic_form_element_dropdown.dart';

import '../../entities/option.dart';

class WidgetElementDropdown extends StatefulWidget {
  final DynamicFormElementDropdown element;
  final Function? onChanged;

  const WidgetElementDropdown({
    super.key,
    required this.element,
    required this.onChanged
  });

  @override
  _WidgetElementDropdown createState() => _WidgetElementDropdown();
}

class _WidgetElementDropdown extends State<WidgetElementDropdown> {
  late DynamicFormElementDropdown element;
  late String value;
  late List<Option> options;

  @override
  void initState() {
    super.initState();
    element = widget.element;
    value = element.getValue();
    options = element.options;
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _handleValueChange() {
    widget.onChanged!(element.id, value);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          element.getTitle(),
          style: const TextStyle(fontSize: 16.0),
        ),
        DropdownButton<String>(
          value: value,
          onChanged: (String? newValue) {
            setState(() {
              value = newValue ?? value;
              _handleValueChange();
            });
          },
          items: options.map((Option item) {
            return DropdownMenuItem<String>(
              value: item.value,
              child: Text(item.text),
            );
          }).toList(),
        ),
      ],
    );
  }
}
import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:ui/entities/response/response_state.dart';
import 'package:ui/entities/response/response_task_id.dart';

class FutureEnvironment {

  Future<ResponseTaskId> login() async {
    const url = "http://127.0.0.1:5006/docker_aws_login";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> loginFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/docker_aws_login/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<ResponseTaskId> stack({String mode = 'reset'}) async {
    const url = "http://127.0.0.1:5006/kt_stack";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode({
          'mode': mode
        })
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> stackFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/kt_stack/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<ResponseTaskId> install() async {
    const url = "http://127.0.0.1:5006/kt_install";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> installFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/kt_install/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<ResponseTaskId> buildCockpit() async {
    const url = "http://127.0.0.1:5006/build_cockpit";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> buildCockpitFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/build_cockpit/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<ResponseTaskId> buildEmailEditor() async {
    const url = "http://127.0.0.1:5006/build_email_editor";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> buildEmailEditorFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/build_email_editor/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<ResponseTaskId> browserStackLocal(String user, String token) async {
    const url = "http://127.0.0.1:5006/browserstack_local";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode({
          'browserstackUser': user,
          'browserstackToken': token
        })
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> browserStackLocalFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/browserstack_local/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<ResponseTaskId> pullImageLocalBrowser(String browser) async {
    const url = "http://127.0.0.1:5006/pull_local_browser_image";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode({
          'browser': browser
        })
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> pullImageLocalBrowserFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/pull_local_browser_image/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<ResponseTaskId> buildImageExecutorBase() async {
    const url = "http://127.0.0.1:5006/build_executor_base_image";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> buildImageExecutorBaseFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/build_executor_base_image/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<ResponseTaskId> buildImageExecutorFinal(String browser) async {
    const url = "http://127.0.0.1:5006/build_executor_final_image";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode({
          'browser': browser
        })
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> buildImageExecutorFinalFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/build_executor_final_image/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<bool> checkLogin(String domain, String user, String pass) async {

    String url = "https://$domain/e2e/api/access";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode({
          'user': user,
          'pass': pass
        })
    );

    if (response.statusCode != 200) {
      return false;
    }

    Map<String, dynamic> json = jsonDecode(response.body) as Map<String, dynamic>;

    return json['access'] ?? false;

  }

}
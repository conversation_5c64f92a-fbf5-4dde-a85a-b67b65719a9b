import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:ui/entities/response/response_testcase_states.dart';
import 'package:ui/entities/test_execute.dart';
import 'package:ui/entities/test_retrieve_response.dart';

import '../entities/response/response_state.dart';
import '../entities/response/response_task_id.dart';
import '../entities/test_create_response.dart';

class FutureBackend {

  Future<TestCreateResponse> testCreate(String dataJsonString) async {
    const url = "http://127.0.0.1:5006/test";

    final response = await http.post(
      Uri.parse(url),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: dataJsonString
    );

    return TestCreateResponse(response: response);

  }

  Future<TestRetrieveResponse> testRetrieve(int id) async {
    String url = "http://127.0.0.1:5006/test/$id";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
    );

    return TestRetrieveResponse(response: response);

  }

  Future<List<TestRetrieveResponse>> testRetrieveMultiple(List<int> ids) async {

    List<TestRetrieveResponse> responses = [];
    for (int id in ids) {

      String url = "http://127.0.0.1:5006/test/$id";

      final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
      );

      responses.add(TestRetrieveResponse(response: response));

    }

    return responses;

  }

  Future<ResponseTaskId> executeTest(TestExecute testCase) async {
    const url = "http://127.0.0.1:5006/exec_test_parallel";

    final response = await http.post(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        },
        body: jsonEncode(testCase.toJsonObject())
    );

    return ResponseTaskId(response: response);

  }

  Future<ResponseState> executeTestFinished(String taskId) async {
    String url = "http://127.0.0.1:5006/exec_test_parallel/$taskId";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseState(response: response);

  }

  Future<ResponseTestcaseStates> getTestCaseStates() async {
    String url = "http://127.0.0.1:5006/exec_test_parallel_states/";

    final response = await http.get(
        Uri.parse(url),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
        }
    );

    return ResponseTestcaseStates(response: response);

  }


}
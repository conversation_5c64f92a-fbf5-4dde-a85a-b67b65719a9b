
import 'package:ui/entities/environment/environment.dart';
import 'package:ui/entities/persistentData/persistent_data.dart';
import 'package:ui/entities/queue/queue.dart';
import 'package:ui/entities/recording_index_item.dart';
import 'package:ui/entities/recording.dart';

class AppData {
  static final AppData _appData = AppData._internal();

  late Recording recording;
  late PersistentData persistentData;
  int currentPage = 0;

  Queue queue = Queue();
  Environment environment = Environment();

  late List<RecordingIndexItem> testIndex = [];

  factory AppData() {
    return _appData;
  }

  AppData._internal();

}

final appData = AppData();

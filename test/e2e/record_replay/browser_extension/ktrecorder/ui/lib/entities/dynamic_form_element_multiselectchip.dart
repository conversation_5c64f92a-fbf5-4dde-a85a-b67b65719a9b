import 'dynamic_form_element.dart';
import 'option.dart';

class DynamicFormElementMultiSelectChip extends DynamicFormElement {

  late final List<Option> options;

  List<Option> selection = [];
  List<Option> defaultSelection = [];

  DynamicFormElementMultiSelectChip({
    required super.id,
    required super.title,
    required this.options,
    super.description = '',
    this.selection = const [],
    this.defaultSelection = const [],
    super.hiddenCallback,
    super.adjustFormValuesCallback
  });

  @override
  String getType() {
    return DynamicFormElement.typeMultiSelectChip;
  }

  @override
  dynamic getValue() {
    return selection;
  }

  @override
  setValue (dynamic value) {
    selection = value as List<Option>;
  }

  List<Option> getOptions() {
    return options;
  }

  List<Option> getDefaultSelection() {
    return defaultSelection;
  }

}
import 'package:ui/entities/execute.dart';

import 'dynamic_form.dart';
import 'dynamic_form_element.dart';

class ExecuteDropdownInput extends Execute {

  static const commandId = 'dropdown-input';
  static const title = 'Select dropdown option (Drupal)';

  String xPath = '';
  String special = '';
  String value = '';

  ExecuteDropdownInput({
    required this.xPath,
    this.value = '',
    this.special = ''
  });

  factory ExecuteDropdownInput.fromJson(Map<String, dynamic> json) {
    return ExecuteDropdownInput(
      xPath: json.containsKey('xPath') ? json['xPath'] : '',
      value: json.containsKey('value') ? json['value'] : '',
      special: json.containsKey('special') ?json['special'] : ''
    );
  }

  @override
  String getCommandId() {
    return commandId;
  }

  @override
  String getTitle() {
    return title;
  }

  @override
  Map<String, dynamic> toJsonObject() {
    return {
      'command': getCommandId(),
      'target': xPath,
      'targetCss': '',
      'value': value,
      'special': special
    };
  }

  @override
  List<Map<String, dynamic>> toExecute() {
    return [{
      'command': 'click',
      'target': "$xPath//option[@value='$value']",
      'targetCss': '',
      'value': '',
      'special': special
    }];
  }

  @override
  DynamicForm convertToForm() {

    DynamicForm form = DynamicForm();

    form.addElement(DynamicFormElement(
        id: 'xPath',
        title: 'XPath',
        stringValue: xPath
    ));

    form.addElement(DynamicFormElement(
        id: 'value',
        title: 'Selected option value',
        stringValue: value
    ));

    return form;
  }

  @override
  convertFromForm(DynamicForm form) {
    xPath = form.getValueFor('xPath');
    value = form.getValueFor('value');
  }

}
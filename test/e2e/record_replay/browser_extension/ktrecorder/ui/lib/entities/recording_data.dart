import 'package:ui/entities/dataset.dart';
import 'package:ui/entities/dataset_config.dart';

class RecordingExecData {
  late String url;
  late int testCaseDependency;
  late List<Dataset> testCaseDataCreation;

  late List<String> roleNames;
  late String userGroupId;
  late String tier;

  RecordingExecData({
    this.url = '',
    this.testCaseDependency = 0,
    this.testCaseDataCreation = const [],
    this.roleNames = const ['Enterprise'],
    this.userGroupId = '43',
    this.tier = '10000'
  });

  factory RecordingExecData.fromJsonObject(Map<String, dynamic> json) {

    int dependency = 0;
    if (json.containsKey('testCaseDependency')) {
      dependency = json['testCaseDependency'] is int ? json['testCaseDependency'] : int.parse(json['testCaseDependency']);
    }

    final datasets = json.containsKey('testCaseDataCreation') ? List.castFrom(json['testCaseDataCreation']) : [];

    final List<Dataset> creation = datasets.map<Dataset>((ds) {

      final config = ds.containsKey('config') ? List.castFrom(ds['config']) : [];

      return Dataset(
        id: ds['id'].toString(),
        name: ds['name'].toString(),
        description: ds['description'].toString(),
        index: int.parse(ds['index'].toString()),
        config: config.map<DatasetConfig>((c) => DatasetConfig.fromJsonObject(c)).toList()
      );
    }).toList();

    return RecordingExecData(
      url: json.containsKey('url') ? json['url'] : '',
      testCaseDependency: dependency,
      testCaseDataCreation: creation,
      roleNames: json.containsKey('roleNames') ? List.castFrom(json['roleNames']) : ['Enterprise'],
      userGroupId: json.containsKey('userGroupId') ? json['userGroupId'].toString() : '43',
      tier: json.containsKey('tierInAccount') ? json['tierInAccount'].toString() : '10000',
    );

  }

  Map<String, dynamic> toJsonObject() {
    return {
      "url": url,
      "testCaseDependency": testCaseDependency,
      "testCaseDataCreation": testCaseDataCreation.map<Map<String, dynamic>>((dataset) {
        return dataset.toJsonObject();
      }).toList(),
      "roleNames": roleNames,
      "userGroupId": userGroupId,
      "tierInAccount": tier
    };
  }

  Map<String, dynamic> forExecution(bool useDependency) {

    // clean up
    late final int dep;
    late final List<Map<String, dynamic>> data;

    if (useDependency) {
      dep = testCaseDependency;
      data = [];
    }
    else {
      dep = 0;
      // TODO Reset and Account could be option when data creation only
      data = [
        {
          "class": "E2eDataReset",
          "params": {}
        },
        {
          "class":"E2eDataAccount",
          "params": {
            "userGroupId": userGroupId,
            "roleNames": roleNames,
            "tierInAccount": tier
          }
        }
      ];

      for(Dataset dataset in testCaseDataCreation) {
        data.add(dataset.forExecution());
      }

    }

    return {
      "url": url,
      "testCaseDependency": dep,
      "testCaseDataCreation": data
    };
  }

  addDataset(Dataset dataset) {

    int existingIndex = testCaseDataCreation.indexWhere((item) => item.internalId == dataset.internalId);

    if (existingIndex != -1) {
      testCaseDataCreation[existingIndex] = dataset;
    } else {
      testCaseDataCreation.add(dataset);
    }

  }

  removeDataset(Dataset dataset) {
    testCaseDataCreation.removeWhere((item) => item.internalId == dataset.internalId);
  }

  setRoles(List<String> names) {
    roleNames = names;
  }

  setUserGroup(String groupId) {
    userGroupId = groupId;
  }

  setTier(String value) {
    tier = value;
  }

}

import 'package:flutter/material.dart';
import 'package:ui/entities/execute_click.dart';
import 'package:ui/entities/recording_step.dart';
import 'dart:typed_data';

class ActionWidget extends StatefulWidget {
  const ActionWidget({Key? key, required this.action}) : super(key: key);

  final RecordingStep action;

  @override
  _ActionWidgetState createState() => _ActionWidgetState();
}

class _ActionWidgetState extends State<ActionWidget> {

  late final Uint8List screenshot;
  late final String screenshotUrl;

  @override
  void initState() {
    super.initState();
    screenshot = widget.action.getScreenShot() ?? Uint8List(0);
    screenshotUrl = widget.action.getScreenshotUrl();
  }

  @override
  Widget build(BuildContext context) {


    // https://stackoverflow.com/questions/57267165/how-to-show-snackbar-without-scaffold

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: getActionInfo(),
        ),
        Expanded(
          child: Center(
              child: screenshotUrl.isNotEmpty == true
                  ? Image.network(screenshotUrl, fit: BoxFit.fitHeight)
                  : screenshot.isNotEmpty
                  ? Image.memory(screenshot, fit: BoxFit.fitHeight)
                  : const Text("No screenshot available.")
          )
        )

      ],
    ); // This trailing comma makes auto-formatting nicer for build methods.
  }

  Text getActionInfo() {
    final command = widget.action.command;
    final special = widget.action.special;
    final reason = widget.action.somewhereReason;
    String info = '';
    Color color = Colors.blueAccent;

    if (command == ExecuteClick.commandId && special == 'somewhere') {

      switch (reason) {
        case 'html/body':
          info = "-> This was recorded as a click to somewhere";
          color = Colors.orange;
          break;
        case 'multiple-targets':
          info = "-> There are multiple targets for this xPath!";
          color = Colors.red;
          break;
      }

    }

    return Text(
      'Command: $command $info',
      style: TextStyle(
        fontSize: 18,
        color: color
      ),
    );

  }
}

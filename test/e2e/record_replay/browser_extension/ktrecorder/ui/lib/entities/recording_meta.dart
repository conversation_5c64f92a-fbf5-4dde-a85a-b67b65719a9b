
class RecordingMeta {
  late final int id;
  late final String displayName;
  late final String description;
  late final String machineName;

  RecordingMeta({
    required this.id,
    required this.displayName,
    required this.description,
    required this.machineName,
  });

  factory RecordingMeta.fromJsonObject(Map<String, dynamic> json) {

    return RecordingMeta(
      id: json.containsKey('id') ? json['id'] : 0,
      displayName: json.containsKey('displayName') ? json['displayName'] : '',
      description: json.containsKey('description') ? json['description'] : '',
      machineName: json.containsKey('machineName') ? json['machineName'] : ''
    );

  }

  Map<String, dynamic> toJsonObject() {
    return {
      'id': id,
      'displayName':displayName,
      'description': description,
      'machineName': machineName,
    };
  }

}

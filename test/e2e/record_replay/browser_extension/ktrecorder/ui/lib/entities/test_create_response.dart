
import 'dart:convert';

import 'package:http/http.dart';

class TestCreateResponse {

  final Response response;
  int statusCode = 0;
  String state = '';
  String log = '';
  String machineName = '';

  TestCreateResponse({
    required this.response,
  }) {
    statusCode = response.statusCode;
    parseResponseBody();
  }

  parseResponseBody() {

    Map<String, dynamic> json = jsonDecode(response.body) as Map<String, dynamic>;

    state = json.containsKey('state') ? json['state'].toString() : 'unknown';
    machineName = json.containsKey('machineName') ? json['machineName'].toString() : '';

    if (json.containsKey('info')) {
      log = json['info'].containsKey('log') ? json['info']['log'].toString() : '';
    }

  }

  bool hasError() {
    return statusCode != 200;
  }

  @override
  String toString() {
    return "statusCode: $statusCode\nstate: $state\nlog: $log\nmachineName: $machineName";
  }

}
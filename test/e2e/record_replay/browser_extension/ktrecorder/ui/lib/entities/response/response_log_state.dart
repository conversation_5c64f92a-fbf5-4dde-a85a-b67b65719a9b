
import 'package:ui/entities/response/response_log.dart';

class ResponseLogState extends ResponseLog {

  final String returnCode;
  final String result;
  final String log;
  late final List<String> logEntries;

  ResponseLogState({
    required super.statusCode,
    required super.message,
    super.url = '',
    required this.returnCode,
    required this.result,
    required this.log,
  }) {
    logEntries = log.split('\\n');
  }

  @override
  String getType() {
    return ResponseLog.typeState;
  }

  String getReturnCode() {
    return returnCode;
  }

  String getResult() {
    return result;
  }

  List<String> getLog() {
    return logEntries;
  }

}
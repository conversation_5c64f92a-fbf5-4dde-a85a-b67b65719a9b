

class PersistentExecUser {

  String username;
  String password;
  String domain;
  int lastUsed;

  PersistentExecUser({
    required this.username,
    required this.password,
    required this.domain,
    this.lastUsed = 0
  });

  factory PersistentExecUser.fromJsonObject(Map<String, dynamic> json) {
    return PersistentExecUser(
        username: json.containsKey('username') ? json['username'].toString() :'',
        password: json.containsKey('password') ? json['password'].toString() : '',
        domain : json.containsKey('domain') ? json['domain'].toString() : 'app.ktlocal.com',
        lastUsed : json.containsKey('lastUsed') ? int.tryParse(json['lastUsed']) ?? 0 : 0,
    );

  }

  Map<String, dynamic> toJsonObject() {
    return {
      'username': username,
      'password': password,
      'domain': domain,
      'lastUsed': lastUsed
    };
  }



}
import 'package:ui/entities/dynamic_form.dart';
import 'package:ui/entities/dynamic_form_element.dart';
import 'package:ui/entities/execute.dart';

class ExecuteTextInput extends Execute {

  static const commandId = 'text-input';
  static const title = 'Input text into textfield/textarea';

  String xPath = '';
  String value = '';
  String special = '';

  ExecuteTextInput({
    required this.xPath,
    required this.value,
    this.special = '',
  });

  factory ExecuteTextInput.fromJson(Map<String, dynamic> json) {
    return ExecuteTextInput(
      xPath: json.containsKey('xpath') ? json['xpath'] : '',
      value: json.containsKey('value') ? json['value'] : '',
      special: json.containsKey('special') ? json['special'] : '',
    );
  }

  @override
  String getCommandId() {
    return commandId;
  }

  @override
  String getTitle() {
    return title;
  }

  @override
  Map<String, dynamic> toJsonObject() {
    return {
      'command': getCommandId(),
      'target': xPath,
      'targetCss': '',
      'value': value,
      'special': special
    };
  }

  @override
  DynamicForm convertToForm() {

    DynamicForm form = DynamicForm();

    form.addElement(DynamicFormElement(
        id: 'xPath',
        title: 'XPath',
        stringValue: xPath
    ));

    form.addElement(DynamicFormElement(
        id: 'value',
        title: 'Value to insert',
        stringValue: value
    ));

    return form;
  }

  @override
  convertFromForm(DynamicForm form) {
    xPath = form.getValueFor('xPath');
    value = form.getValueFor('value');
  }

}
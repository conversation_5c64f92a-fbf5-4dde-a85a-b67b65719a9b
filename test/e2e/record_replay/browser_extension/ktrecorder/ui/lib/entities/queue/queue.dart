
import 'package:ui/entities/queue/queue_item.dart';

class Queue {

  List<QueueItem> highPriorityQueue = [];
  List<QueueItem> lowPriorityQueue = [];

  bool isPaused = false;

  int getTotalItems() {
    return highPriorityQueue.length + lowPriorityQueue.length;
  }

  bool hasItems() {

    if (getTotalItems() == 0) {
      return false;
    }

    return true;

  }

  process(int tick) {

    if (isPaused) {
      return;
    }

    if (!hasItems()) {
      return;
    }

    QueueItem? item = getNext();

    if (item == null) {
      return;
    }

    item.process(tick);

  }

  addHighPriorityItem(QueueItem item) {
    highPriorityQueue.add(item);
  }

  addLowPriorityItem(QueueItem item) {
    lowPriorityQueue.add(item);
  }

  addLowPriorityItemFirst(QueueItem item) {
    lowPriorityQueue.insertAll(0, [item]);
  }

  QueueItem? getNext() {

    if (highPriorityQueue.isNotEmpty) {
      List<QueueItem> items = highPriorityQueue.where((item) => item.isInProgress()).toList();
      if (items.isNotEmpty) {
        return items.first;
      }

      items = highPriorityQueue.where((item) => item.isWaiting()).toList();
      if (items.isNotEmpty) {
        return items.first;
      }
    }

    if (lowPriorityQueue.isNotEmpty) {
      List<QueueItem> items = lowPriorityQueue.where((item) => item.isInProgress()).toList();
      if (items.isNotEmpty) {
        return items.first;
      }

      items = lowPriorityQueue.where((item) => item.isWaiting()).toList();
      if (items.isNotEmpty) {
        return items.first;
      }
    }

    return null;

  }

  pauseQueue() {
    isPaused = true;
  }

  continueQueue() {
    isPaused = false;
  }

  List<QueueItem> getItemsHighPriority() {
    return highPriorityQueue.where((item) => !item.isFinished()).toList();
  }

  List<QueueItem> getItemsInProgress() {
    return lowPriorityQueue.where((item) => item.isInProgress()).toList();
  }

  List<QueueItem> getItemsWaiting() {
    return lowPriorityQueue.where((item) => item.isWaiting()).toList();
  }

  List<QueueItem> getItemsFinished() {
    List<QueueItem> highLow = highPriorityQueue + lowPriorityQueue;
    List<QueueItem> finished = highLow.where((item) => item.isFinished()).toList();
    finished.sort((a, b) => a.finishedTick.compareTo(b.finishedTick));
    return finished;
  }

  abortItemsHighPriority(String reason, int tick) {
    highPriorityQueue = highPriorityQueue.map<QueueItem>((item) {
      if (!item.isFinished()) {
        item.lastTick = tick;
        item.abort(reason);
      }
      return item;
    }).toList();
  }

  abortProcessingItems(String reason, int tick) {
    lowPriorityQueue = lowPriorityQueue.map<QueueItem>((item) {
      if (item.isInProgress()) {
        item.lastTick = tick;
        item.abort(reason);
      }
      return item;
    }).toList();
  }

  abortWaitingItems(String reason, int tick) {
    lowPriorityQueue = lowPriorityQueue.map<QueueItem>((item) {
      if (item.isWaiting()) {
        item.lastTick = tick;
        item.abort(reason);
      }
      return item;
    }).toList();
  }

  abortAll(String reason, int tick) {
    abortItemsHighPriority(reason, tick);
    abortWaitingItems(reason, tick);
    abortProcessingItems(reason, tick);
  }

}

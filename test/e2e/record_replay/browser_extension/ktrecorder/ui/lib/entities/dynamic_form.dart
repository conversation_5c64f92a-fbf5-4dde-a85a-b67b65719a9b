import 'package:ui/entities/dynamic_form_element.dart';

class DynamicForm {

  List<DynamicFormElement> elements = [];

  DynamicForm();

  addElement(DynamicFormElement element) {
    elements.add(element);
  }

  List<DynamicFormElement> getElements() {
    return elements.where((element) => !element.isHidden(this)).toList();
  }

  dynamic getValueFor(String id) {

    List<DynamicFormElement> el = elements.where((e) => e.getId() == id).toList();

    if (el.isEmpty) {
      return '';
    }

    return el.first.getValue();

  }

  setValueFor(String id, dynamic value) {
    List<DynamicFormElement> el = elements.where((e) => e.getId() == id).toList();

    if (el.isNotEmpty) {
      final updated = el[0].setValue(value);
      el[0].adjustFormValues(this);
      return updated;
    }
  }

  // TODO we should not use this
  updateElement(DynamicFormElement element) {

   final index = elements.indexWhere((e) => e.getId() == element.id);

   if (index != -1) {
     elements[index] = element;
   }

  }

}
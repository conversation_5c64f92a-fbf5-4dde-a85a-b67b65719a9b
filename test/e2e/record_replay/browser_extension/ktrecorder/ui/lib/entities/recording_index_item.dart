class RecordingIndexItem {
  final int id;
  final String machineName;
  final String displayName;
  final String description;
  final List<int> dependencyChain;

  RecordingIndexItem({
    required this.id,
    required this.machineName,
    required this.displayName,
    required this.description,
    required this.dependencyChain,
  });

  factory RecordingIndexItem.fromJsonObject(Map<String, dynamic> jsonObject) {
    return RecordingIndexItem(
      id: jsonObject['id'],
      machineName: jsonObject['machineName'],
      displayName: jsonObject['displayName'],
      description: jsonObject['description'],
      dependencyChain: List.castFrom(jsonObject['dependencyChain']),
    );
  }

}

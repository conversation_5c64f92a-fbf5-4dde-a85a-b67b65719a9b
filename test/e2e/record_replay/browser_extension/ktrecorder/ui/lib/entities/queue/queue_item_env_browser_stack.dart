
import 'package:ui/entities/queue/queue_item.dart';

import '../environment/environment_task.dart';
import '../environment/environment_task_browserstack.dart';

class QueueItemEnvBrowserStack extends QueueItem {

  final String browserstackUser;
  final String browserstackToken;
  late final EnvironmentTaskBrowserstack task;

  QueueItemEnvBrowserStack({
    super.finishedCallback,
    required this.browserstackUser,
    required this.browserstackToken
  }) {
    task = EnvironmentTaskBrowserstack(
      browserstackUser: browserstackUser,
      browserstackToken: browserstackToken
    );
  }

  // --- Overwrites ---

  @override
  int getDelay() {
    return 0;
  }

  @override
  int execute() {

    int taskStatus = task.process();
    writeLog(task.readLog());

    switch(taskStatus) {
      case EnvironmentTask.statusSuccess:
        status = QueueItem.statusFinished;
        break;
      case EnvironmentTask.statusTimeout:
      case EnvironmentTask.statusFailed:
        status = QueueItem.statusFailed;
        break;
      case EnvironmentTask.statusInit:
      case EnvironmentTask.statusWaiting:
      default:
        status = QueueItem.statusProcessing;
        break;
    }

    return status;
  }

  @override
  String title() {
    return 'Start Browserstack (local)';
  }

  @override
  String info() {

    switch(status) {
      case QueueItem.statusWaiting:
        return 'Waiting...';
      case QueueItem.statusProcessing:
      case QueueItem.statusExecuting:
        return 'Processing...';
      case QueueItem.statusFinished:
        return 'Done.';
      case QueueItem.statusCanceled:
        return 'Canceled.';
      case QueueItem.statusFailed:
        return 'Failed.';
    }

    return 'Unknown status';

  }

}

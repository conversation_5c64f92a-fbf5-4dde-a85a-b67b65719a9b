
import 'package:ui/entities/queue/queue_item.dart';
import 'package:ui/singletons/app_data.dart';

import '../environment/environment_task.dart';

class QueueItemEnvInstall extends QueueItem {

  QueueItemEnvInstall({
    super.finishedCallback
  });

  // --- Overwrites ---

  @override
  int getDelay() {
    return 0;
  }

  @override
  int execute() {

    int loginStatus = appData.environment.install.process();
    writeLog(appData.environment.install.readLog());

    switch(loginStatus) {
      case EnvironmentTask.statusSuccess:
        status = QueueItem.statusFinished;
        break;
      case EnvironmentTask.statusTimeout:
      case EnvironmentTask.statusFailed:
        status = QueueItem.statusFailed;
        break;
      case EnvironmentTask.statusInit:
      case EnvironmentTask.statusWaiting:
      default:
        status = QueueItem.statusProcessing;
        break;
    }

    return status;
  }

  @override
  String title() {
    return 'Install KlickTipp';
  }

  @override
  String info() {

    switch(status) {
      case QueueItem.statusWaiting:
        return 'Waiting...';
      case QueueItem.statusProcessing:
      case QueueItem.statusExecuting:
        return 'Processing...';
      case QueueItem.statusFinished:
        return 'Done.';
      case QueueItem.statusCanceled:
        return 'Canceled.';
      case QueueItem.statusFailed:
        return 'Failed.';
    }

    return 'Unknown status';

  }

}

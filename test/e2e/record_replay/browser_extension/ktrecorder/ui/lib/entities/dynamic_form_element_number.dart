import 'dynamic_form_element.dart';

class DynamicFormElementNumber extends DynamicFormElement {

  int numberValue = 0;

  DynamicFormElementNumber({
    required super.id,
    required super.title,
    super.description = '',
    this.numberValue = 0,
    super.hiddenCallback,
    super.adjustFormValuesCallback
  });

  @override
  String getType() {
    return DynamicFormElement.typeNumber;
  }

  @override
  dynamic getValue() {
    return numberValue;
  }

  @override
  setValue (dynamic value) {
    numberValue = (value is int) ? value : int.parse(value);
  }

}
import 'package:ui/entities/dynamic_form.dart';
import 'package:ui/entities/dynamic_form_element.dart';
import 'package:ui/entities/execute.dart';

class ExecuteKeyEnter extends Execute {

  static const commandId = 'key-enter';
  static const title = 'Key enter was pressed';

  String xPath = '';

  ExecuteKeyEnter({
    required this.xPath
  });

  factory ExecuteKeyEnter.fromJson(Map<String, dynamic> json) {
    return ExecuteKeyEnter(
        xPath: json.containsKey('xPath') ? json['xPath'] : '',
    );
  }

  @override
  String getCommandId() {
    return commandId;
  }

  @override
  String getTitle() {
    return title;
  }

  @override
  Map<String, dynamic> toJsonObject() {
    return {
      'command': 'key-press',
      'target': xPath,
      'targetCss': '',
      'value': 'enter',
      'special': ''
    };
  }

  @override
  DynamicForm convertToForm() {

    DynamicForm form = DynamicForm();

    form.addElement(DynamicFormElement(
        id: 'xPath',
        title: 'XPath',
        stringValue: xPath
    ));

    return form;
  }

  @override
  convertFromForm(DynamicForm form) {
    xPath = form.getValueFor('xPath');
  }

}
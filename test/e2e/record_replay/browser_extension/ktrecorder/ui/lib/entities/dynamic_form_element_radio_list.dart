import 'dynamic_form_element.dart';
import 'option.dart';

class DynamicFormElementRadioList extends DynamicFormElement {

  String selected;
  final List<Option> options;

  DynamicFormElementRadioList({
    required super.id,
    required super.title,
    required this.selected,
    required this.options,
    super.description = '',
    super.hiddenCallback,
    super.adjustFormValuesCallback
  });

  @override
  String getType() {
    return DynamicFormElement.typeRadioList;
  }

  @override
  dynamic getValue() {
    return selected;
  }

  @override
  setValue (dynamic value) {
    selected = value.toString();
  }

}
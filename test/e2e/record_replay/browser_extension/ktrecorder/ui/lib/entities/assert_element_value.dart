

import 'package:ui/entities/option.dart';

class AssertElementValue {
  final String value;
  final String tagName;
  final List<Option> attributes;
  final List<Option> selectOptions;

  AssertElementValue({
    required this.value,
    required this.tagName,
    required this.attributes,
    required this.selectOptions,
  });

  factory AssertElementValue.fromJsonObject(Map<String, dynamic> json) {

    return AssertElementValue(
        value: json.containsKey('value') ? json['value'].toString() : '',
        tagName: json.containsKey('tagName') ? json['tagName'].toString() : '',
        attributes: json.containsKey('attributes') ? List.castFrom(json['attributes']).map<Option>((option) => Option.fromJsonObject(option)).toList() : [],
        selectOptions: json.containsKey('selectOptions') ? List.castFrom(json['selectOptions']).map<Option>((option) => Option.fromJsonObject(option)).toList() : [],
    );

  }

  Map<String, dynamic> toJsonObject() {
    return {
      'value': value,
      'tagName': tagName,
      'attributes': attributes.map<Map<String, dynamic>>((attr) => attr.toJsonObject()).toList(),
      'selectOptions': selectOptions.map<Map<String, dynamic>>((op) => op.toJsonObject()).toList()
    };
  }

  List<Option> getAttributes() {
    return attributes;
  }

}


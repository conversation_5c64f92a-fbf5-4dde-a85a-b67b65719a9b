import 'package:ui/entities/dynamic_form.dart';
import 'package:ui/entities/dynamic_form_element_checkbox.dart';
import 'package:ui/entities/dynamic_form_element_dropdown.dart';
import 'package:ui/entities/dynamic_form_element_multiselectchip.dart';
import 'package:ui/entities/dynamic_form_element_radio_list.dart';
import 'package:ui/entities/dynamic_form_element_single_text.dart';
import 'package:ui/entities/environment/environment_browsers.dart';
import 'package:ui/entities/option.dart';
import 'package:ui/entities/persistentData/persistent_exec_user.dart';

import '../singletons/app_data.dart';

class TestExecute {

  String testCaseName = '';
  String machineName = '';

  String appLocation = 'local';
  String domain = 'app.ktlocal.com';
  List<Option> browsers = [];
  String execUser = '';
  String execUserPwd = '';
  String browserstackUser = 'simonwerling2';
  String browserstackToken = 'zDqgxpCMGfxWz26PDyg6';
  bool prepare = false;
  bool caching = false;
  bool forceNewDumps = false;
  bool debug = true;

  TestExecute({
    required this.testCaseName,
    required this.machineName
  }) {
    browsers = EnvironmentBrowsers().getBrowsersAsOptions(browserIds: ['chrome']);
    PersistentExecUser currentUser = appData.environment.getCurrentUser();
    execUser = currentUser.username;
    execUserPwd = currentUser.password;
    domain = appData.environment.currentDomain;
  }

  Map<String, dynamic> toJsonObject() {
    return {
      "appLocation": appLocation,
      "domain": domain,
      "browsers": browsers.map<String>((browser) => browser.value).toList(),
      "testCase": machineName,
      "execUser": execUser,
      "execUserPwd": execUserPwd,
      "browserstackUser": browserstackUser,
      "browserstackToken": browserstackToken,
      "prepare": prepare,
      "caching": caching,
      "forceNewDumps": forceNewDumps,
      "debug": debug
    };
  }

  DynamicForm convertToForm() {

    DynamicForm form = DynamicForm();

    form.addElement(DynamicFormElementRadioList(
        id: 'domain',
        title: 'Domain',
        selected: domain,
        options: [
          Option(value: 'app-e2e.ktlocal.com', text: 'app-e2e.ktlocal.com'),
          Option(value: 'app.ktlocal.com', text: 'app.ktlocal.com'),
          Option(value: 'www.klicktipp-staging.com', text: 'Staging'),
        ],
        adjustFormValuesCallback: (DynamicForm form) {
          final domainUsers = appData.persistentData.getExecUsers(form.getValueFor('domain'));
          final latestUser = domainUsers.first;
          form.setValueFor('persistentUser', latestUser.username);
        }
    ));

    List<PersistentExecUser> users = appData.persistentData.getExecUsers(domain);

    form.addElement(DynamicFormElementDropdown(
        id: 'persistentUser',
        title: 'Select previous User',
        selected: execUser,
        options: users.map<Option>((user) => Option(value: user.username, text: user.username.isEmpty ? 'Enter credentials' : user.username)).toList(),
        hiddenCallback: (DynamicForm form) {
          return form.getValueFor('domain') == 'app-e2e.ktlocal.com';
        },
        adjustFormValuesCallback: (DynamicForm form) {
          final domainUsers = appData.persistentData.getExecUsers(form.getValueFor('domain'));
          final latestUser = domainUsers.first;

          form.setValueFor('execUser', latestUser.username);
          form.setValueFor('execUserPwd', latestUser.password);
        }
    ));

    form.addElement(DynamicFormElementTextSingle(
      id: 'execUser',
      title: 'Perform test in account (username)',
      strValue: execUser,
      hiddenCallback: (DynamicForm form) {
        return form.getValueFor('domain') == 'app-e2e.ktlocal.com';
      }
    ));

    form.addElement(DynamicFormElementTextSingle(
      id: 'execUserPwd',
      title: 'Password for account above',
      strValue: execUserPwd,
      hiddenCallback: (DynamicForm form) {
        return form.getValueFor('domain') == 'app-e2e.ktlocal.com';
      }
    ));

    form.addElement(DynamicFormElementMultiSelectChip(
      id: 'browsers',
      title: 'Run test in the following browsers',
      selection: browsers,
      defaultSelection: const [],
      options: EnvironmentBrowsers().getBrowsersAsOptions(),
    ));

    form.addElement(DynamicFormElementTextSingle(
      id: 'browserstackUser',
      title: 'Browserstack User',
      strValue: browserstackUser
    ));

    form.addElement(DynamicFormElementTextSingle(
      id: 'browserstackToken',
      title: 'Browserstack Token',
      strValue: browserstackToken
    ));

    form.addElement(DynamicFormElementCheckbox(
        id: 'prepare',
        title: 'Prepare only (no test execution)',
        checked: prepare
    ));

    form.addElement(DynamicFormElementCheckbox(
        id: 'caching',
        title: 'Activate caching',
        checked: caching
    ));

    form.addElement(DynamicFormElementCheckbox(
        id: 'forceNewDumps',
        title: 'Force new dumps',
        checked: forceNewDumps
    ));

    form.addElement(DynamicFormElementCheckbox(
        id: 'debug',
        title: 'Debug',
        checked: debug
    ));

    return form;
  }

  convertFromForm(DynamicForm form) {

    domain = form.getValueFor('domain').toString();
    appLocation = (domain == 'app-e2e.ktlocal.com') ? 'local' : 'remote';
    browsers = form.getValueFor('browsers') as List<Option>;
    browserstackUser = form.getValueFor('browserstackUser').toString();
    browserstackToken = form.getValueFor('browserstackToken').toString();
    prepare = form.getValueFor('prepare') as bool;

    if (prepare && (execUser == 'none' || execUser.isEmpty)) {
      execUser = 'e2e-test-recorder';
      execUserPwd = 'U4nATY2f';
    }
    else {
      execUser = form.getValueFor('execUser').toString();
      execUserPwd = form.getValueFor('execUserPwd').toString();
    }

    caching = form.getValueFor('caching') as bool;
    forceNewDumps = form.getValueFor('forceNewDumps') as bool;
    debug = form.getValueFor('debug') as bool;

  }

  List<String> getBrowsers() {
    return browsers.map<String>((b) => b.value).toList();
  }

}
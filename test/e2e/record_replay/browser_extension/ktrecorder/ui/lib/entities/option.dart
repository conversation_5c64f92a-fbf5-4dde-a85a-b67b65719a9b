

class Option {
  final String value;
  final String text;

  Option({
    required this.value,
    required this.text,
  });

  factory Option.fromJsonObject(Map<String, dynamic> json) {

    return Option(
      value: json.containsKey('value') ? json['value'].toString() : '',
      text: json.containsKey('text') ? json['text'].toString() : '',
    );

  }

  Map<String, dynamic> toJsonObject() {
    return {
      'value': value,
      'text': text,
    };
  }

  @override
  bool operator ==(Object other) =>
      other is Option &&
              runtimeType == other.runtimeType &&
              value == other.value;

  @override
  int get hashCode => value.hashCode;

}


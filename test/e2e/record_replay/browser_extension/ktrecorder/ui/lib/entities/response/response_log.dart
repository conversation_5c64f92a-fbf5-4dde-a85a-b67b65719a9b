

class ResponseLog {

  static const String typeText = 'text';
  static const String typeTaskId = 'task-id';
  static const String typeState = 'state';
  static const String typeTestCaseStates = 'test-case-states';
  static const String typeTestCase = 'test-case';

  final int statusCode;
  final String message;

  String url;

  ResponseLog({
    required this.statusCode,
    required this.message,
    this.url = ''
  });

  String getType() {
    return typeText;
  }

  String getStatusCode() {
    return statusCode.toString();
  }

  String getMessage() {
    return message;
  }

  String getUrl() {
    return url;
  }

  @override
  String toString() {
    return '$statusCode: $message';
  }

}
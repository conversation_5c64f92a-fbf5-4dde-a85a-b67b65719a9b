import 'package:ui/entities/dynamic_form.dart';
import 'package:ui/entities/dynamic_form_element_single_text.dart';
import 'package:ui/entities/execute.dart';

class ExecuteAssertAttribute extends Execute {

  static const commandId = 'assert-attribute';
  static const title = 'Element has attribute: ';

  String xPath = '';
  String attribute = '';
  String value = '';
  String special = '';

  ExecuteAssertAttribute({
    required this.xPath,
    required this.attribute,
    required this.value,
    this.special = ''
  }) {
    super.isActive = false;
  }

  factory ExecuteAssertAttribute.fromJson(Map<String, dynamic> json) {
    return ExecuteAssertAttribute(
      xPath: json.containsKey('xPath') ? json['xPath'] : '',
      attribute: json.containsKey('attribute') ? json['attribute'] : '',
      value: json.containsKey('value') ? json['value'] : '',
      special: json.containsKey('special') ?json['special'] : ''
    );
  }

  @override
  String getCommandId() {
    return '$commandId:$attribute';
  }

  @override
  String getTitle() {
    return title + attribute;
  }

  @override
  Map<String, dynamic> toJsonObject() {
    return {
      'command': getCommandId(),
      'xPath': xPath,
      'attribute': attribute,
      'value': value,
      'special': special
    };
  }

  @override
  List<Map<String, dynamic>> toExecute() {

    if (attribute == 'innerText') {
      return [{
        'command': 'assert-element',
        'target': "$xPath[contains(text(),'$value')]",
        'targetCss': '',
        'value': '',
        'special': ''
      }];
    }

    if (attribute == 'class') {
      return [{
        'command': 'assert-element',
        'target': "$xPath[contains(@class,'$value')]",
        'targetCss': '',
        'value': '',
        'special': ''
      }];
    }

    Map<String, dynamic> execute = {
      'command': 'assert-element',
      'target': "$xPath[@$attribute='$value']",
      'targetCss': '',
      'value': '',
      'special': ''
    };

    if (attribute == 'value' && special.isNotEmpty) {
      execute['target'] = xPath;
      execute['special'] = special;
      execute['value'] = value;
    }

    return [execute];
  }

  @override
  DynamicForm convertToForm() {

    DynamicForm form = DynamicForm();

    form.addElement(DynamicFormElementTextSingle(
        id: 'xPath',
        title: 'XPath',
        strValue: xPath
    ));

    form.addElement(DynamicFormElementTextSingle(
        id: 'attribute',
        title: 'Attribute name',
        strValue: attribute
    ));

    form.addElement(DynamicFormElementTextSingle(
        id: 'value',
        title: 'Attribute value',
        strValue: value
    ));

    return form;
  }

  @override
  convertFromForm(DynamicForm form) {
    xPath = form.getValueFor('xPath');
    attribute = form.getValueFor('attribute');
    value = form.getValueFor('value');
  }

}
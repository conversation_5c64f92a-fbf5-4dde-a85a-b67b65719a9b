import 'dynamic_form_element.dart';

class DynamicFormElementCheckbox extends DynamicFormElement {

  bool checked = false;

  DynamicFormElementCheckbox({
    required super.id,
    required super.title,
    super.description = '',
    this.checked = false,
    super.hiddenCallback,
    super.adjustFormValuesCallback
  });

  @override
  String getType() {
    return DynamicFormElement.typeCheckbox;
  }

  @override
  dynamic getValue() {
    return checked;
  }

  @override
  setValue (dynamic value) {
    checked = value as bool;
  }

}
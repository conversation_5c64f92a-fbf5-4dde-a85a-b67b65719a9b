
import 'package:ui/entities/response/response_log.dart';
import 'package:ui/entities/response/response_log_browser.dart';
import 'package:ui/entities/response/response_log_state.dart';
import 'package:ui/entities/response/response_log_testcase_states.dart';

class ResponseLogTestcase extends ResponseLogState {

  final List<String> browserIds;
  ResponseLogState logTask;
  ResponseLogTestcaseStates? logTestcaseStates;

  ResponseLogTestcase({
    required this.browserIds,
    required this.logTask,
    this.logTestcaseStates
  }) : super(
    statusCode: logTask.statusCode,
    message: logTask.message,
    url: logTask.url,
    returnCode: logTask.returnCode,
    result: logTask.result,
    log: logTask.log
  );

  @override
  String getType() {
    return ResponseLog.typeTestCase;
  }

  List<String> getBrowsers() {
    return browserIds;
  }

  ResponseLogBrowser getLogForBrowser(String browserId) {
    return logTestcaseStates != null ? ResponseLogBrowser(
      id: browserId,
      log: logTestcaseStates!.log,
      states: logTestcaseStates!.states
    ) : ResponseLogBrowser(
        id: browserId,
        log: {} ,
        states: {}
    );
  }

  // TODO get debug info

  @override
  String toString() {
    return logTask.toString();
  }

}

import 'package:flutter/material.dart';
import 'package:ui/custom_icons_icons.dart';

import '../option.dart';

class EnvironmentBrowsers {

  List<EnvironmentBrowser> browsers = [
    EnvironmentBrowser(
        id: 'chrome',
        title: 'Chrome (local)',
        needsBrowserStack: false,
        icons: [CustomIcons.chrome_1],
        reportUrl: 'app.ktlocal.com/test/e2e/report-chrome/index.html',
    ),
    EnvironmentBrowser(
        id: 'firefox',
        title: 'Firefox (local)',
        needsBrowserStack: false,
        icons: [CustomIcons.firefox],
        reportUrl: 'app.ktlocal.com/test/e2e/report-firefox/index.html',
    ),
    EnvironmentBrowser(
        id: 'safari_macos',
        title: 'Safari MacOS',
        needsBrowserStack: true,
        icons: [CustomIcons.safari, CustomIcons.apple],
        reportUrl: 'app.ktlocal.com/test/e2e/report-safari_macos/index.html',
    ),
    EnvironmentBrowser(
        id: 'safari_ipad',
        title: 'Safari iPad',
        needsBrowserStack: true,
        icons: [CustomIcons.safari, CustomIcons.mobileAlt],
        reportUrl: 'app.ktlocal.com/test/e2e/report-safari_ipad/index.html',
    ),
    EnvironmentBrowser(
        id: 'firefox_win',
        title: 'Firefox Windows',
        needsBrowserStack: true,
        icons: [CustomIcons.firefox, CustomIcons.windows],
        reportUrl: 'app.ktlocal.com/test/e2e/firefox_win/index.html',
    ),
    EnvironmentBrowser(
        id: 'edge',
        title: 'Edge (Windows)',
        needsBrowserStack: true,
        icons: [CustomIcons.edge, CustomIcons.windows],
        reportUrl: 'app.ktlocal.com/test/e2e/report-edge/index.html',
    ),
    EnvironmentBrowser(
        id: 'chrome_macos',
        title: 'Chrome MacOS',
        needsBrowserStack: true,
        icons: [CustomIcons.chrome_1, CustomIcons.apple],
        reportUrl: 'app.ktlocal.com/test/e2e/report-chrome_macos/index.html',
    )
  ];

  List<EnvironmentBrowser> getBrowsers() {
    return browsers;
  }

  EnvironmentBrowser getBrowserById(String id) {
    final filtered = browsers.where((b) => b.id == id).toList();
    if (filtered.isEmpty) {
      return EnvironmentBrowser(id: '', title: 'Browser $id not found', needsBrowserStack: false, icons: [Icons.question_mark], reportUrl: '');
    }
    return filtered.first;
  }

  List<Option> getBrowsersAsOptions({List<String> browserIds = const []}) {

    if (browserIds.isEmpty) {
      return browsers.map<Option>((browser) {
        return Option(value: browser.id, text: browser.title);
      }).toList();
    }

    List<EnvironmentBrowser> filtered = browsers.where((browser) => browserIds.contains(browser.id)).toList();

    if (filtered.isEmpty) {
      return [];
    }

    return filtered.map<Option>((browser) {
      return Option(value: browser.id, text: browser.title);
    }).toList();

  }

  setBrowserImageStatus(String browserId, bool imageStatus) {
    browsers = browsers.map<EnvironmentBrowser>((browser) {
      if (browser.id == browserId) {
        browser.setBrowserImageStatus(imageStatus);
      }
      return browser;
    }).toList();
  }

  setExecutorImageStatus(String browserId, bool imageStatus) {
    browsers = browsers.map<EnvironmentBrowser>((browser) {
      if (browser.id == browserId) {
        browser.setExecutorImageStatus(imageStatus);
      }
      return browser;
    }).toList();
  }

}

class EnvironmentBrowser {
  final String id;
  final String title;
  final bool needsBrowserStack;
  final List<IconData> icons;
  final String reportUrl;

  bool isBrowserImagedPulled = false;
  bool isExecutorImagedBuild = false;

  EnvironmentBrowser({
    required this.id,
    required this.title,
    required this.needsBrowserStack,
    required this.icons,
    required this.reportUrl,
  });

  bool hasBrowserImage() {
    return needsBrowserStack || isBrowserImagedPulled;
  }

  setBrowserImageStatus(bool status) {
    isBrowserImagedPulled = status;
  }

  bool hasExecutorImage() {
    return isExecutorImagedBuild;
  }

  setExecutorImageStatus(bool status) {
    isExecutorImagedBuild = status;
  }

  String getTitle() {
    return title;
  }

  List<IconData> getIcons() {
    return icons;
  }

  String getReportUrl() {
    return reportUrl;
  }

}

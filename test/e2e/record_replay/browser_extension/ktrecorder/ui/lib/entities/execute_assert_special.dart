import 'package:ui/entities/dynamic_form.dart';
import 'package:ui/entities/dynamic_form_element_single_text.dart';
import 'package:ui/entities/execute.dart';

class ExecuteAssertSpecial extends Execute {

  static const commandId = 'assert-special';
  static const title = 'Assert special';

  String xPath = '';
  String special = '';

  ExecuteAssertSpecial({
    required this.xPath,
    this.special = ''
  }) {
    super.isActive = false;
  }

  factory ExecuteAssertSpecial.fromJson(Map<String, dynamic> json) {
    return ExecuteAssertSpecial(
        xPath: json.containsKey('target') ? json['target'] : '',
        special: json.containsKey('special') ?json['special'] : ''
    );
  }

  @override
  String getCommandId() {
    return '$commandId:$special';
  }

  @override
  String getTitle() {
    return '$title:$special';
  }

  @override
  Map<String, dynamic> toJsonObject() {
    return {
      'command': getCommandId(),
      'target': xPath,
      'targetCss': '',
      'value': '',
      'special': special
    };
  }

  @override
  List<Map<String, dynamic>> toExecute() {
    Map<String, dynamic> data = toJsonObject();
    data['command'] = 'assert-element';
    return [data];
  }

  @override
  DynamicForm convertToForm() {

    DynamicForm form = DynamicForm();

    form.addElement(DynamicFormElementTextSingle(
        id: special,
        title: special,
        strValue: special
    ));

    return form;
  }

  @override
  convertFromForm(DynamicForm form) {
    special = form.getValueFor('special');
  }

}
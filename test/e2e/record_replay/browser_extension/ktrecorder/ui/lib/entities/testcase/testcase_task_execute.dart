
import 'package:ui/entities/environment/environment_task.dart';
import 'package:ui/entities/response/response_log_testcase_states.dart';
import 'package:ui/entities/response/response_testcase_states.dart';
import 'package:ui/entities/test_execute.dart';
import 'package:ui/futures/backend.dart';
import 'package:ui/singletons/app_data.dart';

import '../response/response_log_state.dart';
import '../response/response_log_testcase.dart';
import '../response/response_state.dart';
import '../response/response_task_id.dart';

class TestcaseTaskExecute extends EnvironmentTask {

  final TestExecute testCase;

  bool isReset = false;
  bool isPreparingEnvironment = false;

  ResponseLogState? responseLogTask;
  ResponseLogTestcaseStates? responseLogStates;

  TestcaseTaskExecute({
    required this.testCase
  });

  @override
  int execute() {

    if (isFinished()) {
      return status;
    }

    if (!isReset) {
      isReset = true;
      isPreparingEnvironment = false;
      appData.environment.resetForTestCase();
    }

    if(!appData.environment.isCurrentTestCaseReady()) {
      if (!isPreparingEnvironment) {
        isPreparingEnvironment = true;
        // TODO check return code if something went wrong, then status failed
        appData.environment.prepareTestCase(testCase);
      }
      return status;
    }

    FutureBackend backend = FutureBackend();

    if (taskId.isEmpty) {

      Future<ResponseTaskId> startResponse = backend.executeTest(testCase);

      startResponse.then((response) {

        writeLog(response.getLog());

        if (response.hasError()) {
          status = EnvironmentTask.statusFailed;
        }
        else {
          taskId = response.taskId;
          status = EnvironmentTask.statusWaiting;
        }

      }, onError: (e) => onError(e));

    }
    else {

      Future<ResponseState> finishedResponse = backend.executeTestFinished(taskId);

      finishedResponse.then((response) {

        responseLogTask = response.getLog() as ResponseLogState;

        if (responseLogStates == null) {
          writeLog(ResponseLogTestcase(
            browserIds: testCase.getBrowsers(),
            logTask: responseLogTask!,
          ));
        }
        else {
          writeLog(ResponseLogTestcase(
            browserIds: testCase.getBrowsers(),
            logTask: responseLogTask!,
            logTestcaseStates: responseLogStates
          ));
        }

        if (response.hasError()) {
          status = EnvironmentTask.statusFailed;
        }
        else {
          status = response.state == ResponseState.stateSuccess ? EnvironmentTask.statusSuccess : EnvironmentTask.statusWaiting;

          Future<ResponseTestcaseStates> statesResponse = backend.getTestCaseStates();

          statesResponse.then((responseStates) {

            responseLogStates = responseStates.getLog();

            writeLog(ResponseLogTestcase(
                browserIds: testCase.getBrowsers(),
                logTask: responseLogTask!,
                logTestcaseStates: responseLogStates
            ));

            if (responseStates.hasError()) {
              //status = EnvironmentTask.statusFailed;
            }

          });

        }

      }, onError: (e) => onError(e));

    }

    return status;

  }

  ResponseLogState getTaskLog() {
    return responseLogTask != null ? responseLogTask! : ResponseLogState(
        statusCode: 0,
        message: '',
        returnCode: '',
        result: '',
        log: 'No answer received yet.'
    );
  }

  @override
  retry() {
    super.retry();
    isReset = false;
    isPreparingEnvironment = false;
  }

}
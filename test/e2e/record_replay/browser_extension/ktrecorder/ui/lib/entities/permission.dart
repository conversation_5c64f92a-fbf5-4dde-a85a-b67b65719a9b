

class Permission {
  final String id;
  final String name;
  final String description;

  Permission({
    required this.id,
    required this.name,
    required this.description,
  });

  factory Permission.fromJsonObject(Map<String, dynamic> json) {

    return Permission(
        id: json['id'].toString(),
        name: json['name'].toString(),
        description: json['description'].toString(),
    );

  }

}


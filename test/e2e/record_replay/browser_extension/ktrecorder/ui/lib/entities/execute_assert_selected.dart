


// TODO: is that not used?

import 'package:ui/entities/execute.dart';

class ExecuteAssertSelected extends Execute {

  static const commandId = 'assert-selected';

  late final String cssSelector;
  late final String value;

  ExecuteAssertSelected({
    required this.cssSelector,
    required this.value,
  }) {
    super.isActive = false;
  }

  factory ExecuteAssertSelected.fromJson(Map<String, dynamic> json) {
    return ExecuteAssertSelected(
      cssSelector: json.containsKey('cssSelector') ? json['cssSelector'] : '',
      value: json.containsKey('value') ? json['value'] : '0',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'command': getCommandId(),
      'cssSelector': cssSelector,
      'selected': value
    };
  }

}
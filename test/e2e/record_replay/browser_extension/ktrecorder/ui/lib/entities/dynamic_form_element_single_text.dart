
import 'dynamic_form_element.dart';

class DynamicFormElementTextSing<PERSON> extends DynamicFormElement {

  String strValue;

  DynamicFormElementTextSingle({
    required super.id,
    required super.title,
    super.description = '',
    this.strValue = '',
    super.hiddenCallback,
    super.adjustFormValuesCallback
  });

  @override
  String getType() {
    return DynamicFormElement.typeTextSingle;
  }

  @override
  dynamic getValue() {
    return strValue;
  }

  @override
  setValue (dynamic value) {
    strValue = value.toString();
  }

}
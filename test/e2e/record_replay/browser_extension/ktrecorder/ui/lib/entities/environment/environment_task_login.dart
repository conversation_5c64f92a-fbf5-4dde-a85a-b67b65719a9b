
import 'package:ui/entities/environment/environment_task.dart';
import 'package:ui/entities/response/response_task_id.dart';

import '../../futures/environment.dart';
import '../response/response_state.dart';

class EnvironmentTaskLogin extends EnvironmentTask {

  @override
  int execute() {

    if (isFinished()) {
      return status;
    }

    FutureEnvironment env = FutureEnvironment();

    if (taskId.isEmpty) {

      Future<ResponseTaskId> startResponse = env.login();

      startResponse.then((response) {

        writeLog(response.getLog());

        if (response.hasError()) {
          status = EnvironmentTask.statusFailed;
        }
        else {
          taskId = response.taskId;
          status = EnvironmentTask.statusWaiting;
        }

      }, onError: (e) => onError(e));

    }
    else {

      Future<ResponseState> finishedResponse = env.loginFinished(taskId);

      finishedResponse.then((response) {

        writeLog(response.getLog());

        if (response.hasError()) {
          status = EnvironmentTask.statusFailed;
        }
        else {
          status = response.state == ResponseState.stateSuccess ? EnvironmentTask.statusSuccess : EnvironmentTask.statusWaiting;
        }

      }, onError: (e) => onError(e));

    }

    return status;

  }

}
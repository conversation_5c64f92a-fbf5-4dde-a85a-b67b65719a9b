import 'package:ui/entities/dynamic_form.dart';
import 'package:ui/entities/dynamic_form_element.dart';
import 'package:ui/entities/execute.dart';

class ExecuteClick extends Execute {

  static const commandId = 'click';
  static const title = 'Click element by xPath';

  String xPath = '';
  String special = '';

  ExecuteClick({
    required this.xPath,
    this.special = ''
  });

  factory ExecuteClick.fromJson(Map<String, dynamic> json) {
    print('execute click');
    print(json);
    return ExecuteClick(
      xPath: json.containsKey('target') ? json['target'] : '',
      special: json.containsKey('special') ?json['special'] : ''
    );
  }

  @override
  String getCommandId() {
    return commandId;
  }

  @override
  String getTitle() {
    return title;
  }

  @override
  Map<String, dynamic> toJsonObject() {
    return {
      'command': getCommandId(),
      'target': xPath,
      'targetCss': '',
      'value': '',
      'special': special
    };
  }

  @override
  DynamicForm convertToForm() {

    DynamicForm form = DynamicForm();

    form.addElement(DynamicFormElement(
      id: 'xPath',
      title: 'XPath',
      stringValue: xPath
    ));

    return form;
  }

  @override
  convertFromForm(DynamicForm form) {
    xPath = form.getValueFor('xPath');
  }

}

import 'dart:convert';
import 'dart:typed_data';

import 'package:ui/entities/dataset.dart';
import 'package:ui/entities/recording_data.dart';
import 'package:ui/entities/recording_export.dart';
import 'package:ui/entities/recording_step.dart';
import 'package:ui/js_chrome_get_data.dart';

class Recording {

  static const String statusInit = 'init'; // no recording loaded or recorded or recording
  static const String statusCreate = 'create'; // a new test is being created
  static const String statusRecording = 'recording'; // a test is currently recorded
  static const String statusRecorded = 'recorded'; // the browser extension has a recording that has not been saved (created)
  static const String statusLoaded = 'loaded'; // existing test loaded

  static const String extStatusInit = 'init'; // the browser extension has no recording or is not recording
  static const String extStatusRecording = 'recording'; // the browser extension is currently recording
  static const String extStatusRecorded = 'recorded'; // the browser extension has a recording ready


  int id;
  String status;
  String extStatus = statusInit;
  String displayName;
  String description;
  String machineName;
  List<RecordingStep> steps;
  RecordingExecData execData;
  bool repoReady;
  String repoPath;
  bool pipelineReady;
  String zephyrTest;
  String zephyrTestUrl = '';
  String error = '';
  String url = '';

  bool useDependency = false;

  Recording({
    required this.id,
    required this.status,
    required this.displayName,
    required this.description,
    required this.machineName,
    required this.steps,
    required this.execData,
    required this.repoReady,
    required this.repoPath,
    required this.pipelineReady,
    required this.zephyrTest,
    required this.zephyrTestUrl,
    this.error = '',
    this.url = '',
  }) {
    useDependency = execData.testCaseDependency > 0;
  }

  factory Recording.fromJsonString(String jsonString) {

    Map<String, dynamic> json = jsonDecode(jsonString) as Map<String, dynamic>;

    List<RecordingStep> recSteps = [];
    if (json.containsKey('recordingSteps')) {
      recSteps = List.castFrom(json['recordingSteps']).map<RecordingStep>((step) {
        return RecordingStep.fromJsonObject(step);
      }).toList();
    }

    final recData = json.containsKey('datasets') ? RecordingExecData.fromJsonObject(json['datasets']) : RecordingExecData();

    return Recording(
      id: json.containsKey('id') ? json['id'] : 0,
      status: json.containsKey('status') ? json['status'] : Recording.statusInit,
      displayName: json.containsKey('displayName') ? json['displayName'] : '',
      description: json.containsKey('description') ? json['description'] : '',
      machineName: json.containsKey('machineName') ? json['machineName'] : '',
      execData: recData,
      steps: recSteps,
      url: json.containsKey('url') ? json['url'] : '',
      repoReady: json.containsKey('repoReady') ? json['repoReady'] : false,
      repoPath: json.containsKey('repoPath') ? json['repoPath'] : '',
      pipelineReady: json.containsKey('pipelineReady') ? json['pipelineReady'] : false,
      zephyrTest: json.containsKey('zephyrTest') ? json['zephyrTest'] : '',
      zephyrTestUrl: json.containsKey('zephyrTestUrl') ? json['zephyrTestUrl'] : '',
    );
  }

  factory Recording.fromExtension(String jsonString) {

    Map<String, dynamic> json = jsonDecode(jsonString) as Map<String, dynamic>;

    String extStatus = json['extensionStatus'].toString();

    if (extStatus == extStatusInit || extStatus == extStatusRecording) {
      return Recording(
        id: 0,
        status: extStatus == extStatusInit ? Recording.statusInit : Recording.statusRecording,
        displayName: '',
        description: '',
        machineName: '',
        execData:  RecordingExecData(),
        steps: [],
        repoReady: false,
        repoPath: '',
        pipelineReady: false,
        zephyrTest: '',
        zephyrTestUrl: '',
      );
    }

    // from here status is recorded

    final List<Map<String, dynamic>> recording = json.containsKey('recording') ? List.castFrom(json['recording']) : [];

    List<RecordingStep> recSteps = [];

    if (recording.isNotEmpty) {
      // recording is only present after a recording from the extension
      // TODO make sure of that!!!

      recSteps = recording.map<RecordingStep>((step) {

        final imageData = step['screenshot'] ?? '';
        final base64 = imageData.split(',').last;
        final Uint8List screenshot = const Base64Decoder().convert(base64);

        //return RecordingStep.fromJsonObject({
        return RecordingStep.fromRecording({
          'command': step.containsKey('command') ? step['command'] : '',
          'target': step.containsKey('target') ? step['target'] : '',
          'targetCss': step.containsKey('targetCss') ? step['targetCss'] : '',
          'value': step.containsKey('value') ? step['value'] : '',
          'timestamp': step.containsKey('timestamp') ? step['timestamp'] : 0,
          'screenshot': screenshot,
          'special': step.containsKey('special') ? step['special'] : '',
          'specialOptions': step.containsKey('specialOptions') ? List<String>.from(step['specialOptions']) : [],
          'somewhereReason': step.containsKey('somewhereReason') ? step['somewhereReason'] : '',
        });

      }).toList();

    }
    else {
      // an existing and already formatted recording
      recSteps = json.containsKey('steps') ? List.castFrom(json['steps']).map<RecordingStep>((step) => RecordingStep.fromJsonObject(step)).toList() : [];
    }

    return Recording(
      id: json.containsKey('id') ? json['id'] : 0,
      status: Recording.statusRecorded,
      displayName: json.containsKey('displayName') ? json['displayName'] : '',
      description: json.containsKey('description') ? json['description'] : '',
      machineName: json.containsKey('machineName') ? json['machineName'] : '',
      execData: json.containsKey('executeData') ? RecordingExecData.fromJsonObject(json['executeData']) : RecordingExecData(url: json['url'] ?? ''), // a new recording cannot have execData
      repoReady: false,
      repoPath: '',
      pipelineReady: false,
      zephyrTest: '',
      zephyrTestUrl: '',
      steps: recSteps,
      error: recSteps.isEmpty ? 'The recording has no steps.' : '',
    );
  }

  createRecording() {
    id = DateTime.now().millisecondsSinceEpoch;
    status = statusCreate;
    displayName = '';
    description = '';
    machineName = '';
    steps = [];
    execData = RecordingExecData.fromJsonObject({});
    repoReady = false;
    repoPath = '';
    pipelineReady = false;
    zephyrTest = '';
    zephyrTestUrl = '';
  }

  toExtension(bool withStatus) {

    Map<String, dynamic> recording = {
      'id': id,
      'status': status,
      'displayName': displayName,
      'description': description,
      'machineName': machineName,
      'executeData': execData.toJsonObject()
    };

    if (withStatus) {
      // TODO: what was the idea here?
      //recording['status'] = 'status';
    }

    chromeSetData(jsonEncode(recording));

  }

  clone() {
    status = statusCreate;
    id = DateTime.now().millisecondsSinceEpoch;
  }

  String getDisplayName() {

    if (status == statusInit) {
      return '';
    }

    return displayName.isNotEmpty ? " - [$displayName]" : ' - [Untitled]';

  }

  List<RecordingStep> getDisplayableSteps() {
    // TODO constant for commands
    //return steps.where((element) => element.command != 'redirect-wait').toList();
    return steps;
  }

  RecordingStep getStep(int index) {
    return steps[index];
  }

  setStep(int index, RecordingStep? step) {
    if (step != null) {
      steps[index] = step;
    }
  }

  int getDependency() {
    return execData.testCaseDependency;
  }

  setDependency(int testID) {
    execData.testCaseDependency = testID;
  }

  bool getUseDependency() {
    return useDependency;
  }

  bool setUseDependency(bool value) {
    useDependency = value;
    return useDependency;
  }

  List<Dataset> getDatasets() {
    return execData.testCaseDataCreation;
  }

  addDataset(Dataset dataset) {
    execData.addDataset(dataset);
  }

  // --- Export ---

  RecordingExport export() {
    return RecordingExport(recording: this);
  }


}

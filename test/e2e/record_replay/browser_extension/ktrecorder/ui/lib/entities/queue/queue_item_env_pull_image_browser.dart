
import 'package:ui/entities/queue/queue_item.dart';

import '../environment/environment_task.dart';
import '../environment/environment_task_pull_image_browser.dart';

class QueueItemEnvPullImageBrowser extends QueueItem {

  final String browser;
  late final EnvironmentTaskPullImageBrowser task;

  QueueItemEnvPullImageBrowser({
    super.finishedCallback,
    required this.browser
  }) {
    task = EnvironmentTaskPullImageBrowser(
        browser: browser
    );
  }

  // --- Overwrites ---

  @override
  int getDelay() {
    return 0;
  }

  @override
  int execute() {

    int taskStatus = task.process();
    writeLog(task.readLog());

    switch(taskStatus) {
      case EnvironmentTask.statusSuccess:
        status = QueueItem.statusFinished;
        break;
      case EnvironmentTask.statusTimeout:
      case EnvironmentTask.statusFailed:
        status = QueueItem.statusFailed;
        break;
      case EnvironmentTask.statusInit:
      case EnvironmentTask.statusWaiting:
      default:
        status = QueueItem.statusProcessing;
        break;
    }

    return status;
  }

  @override
  String title() {
    return 'Pull browser image for $browser';
  }

  @override
  String info() {

    switch(status) {
      case QueueItem.statusWaiting:
        return 'Waiting...';
      case QueueItem.statusProcessing:
      case QueueItem.statusExecuting:
        return 'Processing...';
      case QueueItem.statusFinished:
        return 'Done.';
      case QueueItem.statusCanceled:
        return 'Canceled.';
      case QueueItem.statusFailed:
        return 'Failed.';
    }

    return 'Unknown status';

  }

  @override
  onFinished() {
    finishedTick = lastTick;
    Function.apply(finishedCallback!, [], {#status: status, #browser: browser, #tick: finishedTick});
  }

}

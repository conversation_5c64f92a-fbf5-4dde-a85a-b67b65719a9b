
import '../response/response_log.dart';

class EnvironmentTask {

  static const int statusInit = 0;
  static const int statusWaiting = 1;
  static const int statusSuccess = 2;
  static const int statusFailed = 3;
  static const int statusTimeout = 4;

  String taskId = '';
  int status = statusInit;
  bool isExecuting = false;

  int startTime = 0;
  int timeout = 480000; // milliseconds => 8 minutes

  ResponseLog responseLog = ResponseLog(statusCode: 0, message: '');

  ResponseLog readLog() {
    return responseLog;
  }

  writeLog(ResponseLog log) {
    responseLog = log;
  }

  bool isFinished() {
    return status == statusSuccess || status == statusFailed || status == statusTimeout;
  }

  int process() {

    if (isExecuting || isFinished()) {
      return status;
    }

    // TODO when status pending start timeout max 30 seconds

    if (status == statusInit) {
      startTime = DateTime.now().millisecondsSinceEpoch;
    }

    if (startTime + timeout < DateTime.now().millisecondsSinceEpoch) {
      return statusTimeout;
    }

    isExecuting = true;

    status = execute();

    isExecuting = false;

    return status;

  }

  int execute() {
    return statusSuccess;
  }

  retry() {
    status = statusInit;
    isExecuting = false;
    startTime = 0;
    taskId = '';
  }

  onError(dynamic e) {
    status = EnvironmentTask.statusFailed;
    writeLog(ResponseLog(statusCode: 0, message: 'The api request failed. See browser log for more information.'));
  }


}
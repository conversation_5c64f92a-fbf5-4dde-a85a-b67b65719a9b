import 'dynamic_form.dart';

// TODO: make abstract class so this is not used directly
class DynamicFormElement {

  static const typeTextfield = 'textfield'; //deprecated
  static const typeTextSingle = 'text-single';
  static const typeCheckbox = 'checkbox';
  static const typeMultiSelectChip = 'multi-select-chip';
  static const typeRadioList = 'radio-list';
  static const typeNumber = 'number';
  static const typeDropdown = 'dropdown';

  late final String id;
  late final String title;
  late final String description;
  late final bool Function(DynamicForm form)? hiddenCallback;
  late final Function(DynamicForm form)? adjustFormValuesCallback;

  String stringValue;

  DynamicFormElement({
    required this.id,
    required this.title,
    this.description = '',
    this.stringValue = '',
    this.hiddenCallback,
    this.adjustFormValuesCallback
  });

  String getType() {
    return typeTextfield;
  }

  String getId() {
    return id;
  }

  String getTitle() {
    return title;
  }

  String getDescription() {
    return description;
  }

  dynamic getValue() {
    return stringValue;
  }

  setValue (dynamic value) {
    stringValue = value.toString();
  }

  bool isHidden(DynamicForm form) {
    return hiddenCallback?.call(form) ?? false;
  }

  adjustFormValues(DynamicForm form) {
    adjustFormValuesCallback?.call(form);
  }

}
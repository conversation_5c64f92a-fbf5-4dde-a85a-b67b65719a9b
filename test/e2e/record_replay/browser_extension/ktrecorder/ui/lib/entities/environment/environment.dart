import 'package:ui/entities/environment/environment_browsers.dart';
import 'package:ui/entities/environment/environment_task_build_cockpit.dart';
import 'package:ui/entities/environment/environment_task_build_email_editor.dart';
import 'package:ui/entities/environment/environment_task_install.dart';
import 'package:ui/entities/environment/environment_task_login.dart';
import 'package:ui/entities/environment/environment_task_stack.dart';
import 'package:ui/entities/queue/queue_item.dart';
import 'package:ui/entities/queue/queue_item_env_browser_stack.dart';
import 'package:ui/entities/queue/queue_item_env_build_cockpit.dart';
import 'package:ui/entities/queue/queue_item_env_build_email_editor.dart';
import 'package:ui/entities/queue/queue_item_env_build_image_executor_base.dart';
import 'package:ui/entities/queue/queue_item_env_build_image_executor_final.dart';
import 'package:ui/entities/queue/queue_item_env_install.dart';
import 'package:ui/entities/queue/queue_item_env_stack.dart';
import 'package:ui/entities/queue/queue_item_testcase_execute.dart';

import '../../singletons/app_data.dart';
import '../persistentData/persistent_exec_user.dart';
import '../queue/queue_item_env_login.dart';
import '../queue/queue_item_env_pull_image_browser.dart';
import '../test_execute.dart';

class Environment {

  final EnvironmentTaskLogin login = EnvironmentTaskLogin();
  final EnvironmentTaskStack stack = EnvironmentTaskStack();
  final EnvironmentTaskInstall install = EnvironmentTaskInstall();
  final EnvironmentTaskBuildCockpit buildCockpit = EnvironmentTaskBuildCockpit();
  final EnvironmentTaskBuildEmailEditor buildEmailEditor = EnvironmentTaskBuildEmailEditor();

  final EnvironmentBrowsers browsers = EnvironmentBrowsers();

  late TestExecute currentTestCase;

  bool isInitialized = false;
  bool isAngularInitialized = false;

  bool envIsLoggedIn = false;
  bool envIsStackBuild = false;
  bool envIsInstalled = false;
  bool envIsCockpitBuild = false;
  bool envIsEmailEditorBuild = false;
  bool envIsExecutorBaseImagePulled = false;

  bool envIsBrowserStackReady = false;
  bool currentTextCaseNeedsBrowserstack = false;

  int retryAttempts = 0;

  String currentDomain = 'app.ktlocal.com';
  PersistentExecUser currentUser = PersistentExecUser(username: '', password: '', domain: 'app.ktlocal.com');

  bool isEnvironmentReady() {

    if (!isInitialized) {
      // the first test we try without initializing the environment
      // since it could be already running
      return true;
    }

    if (!isAngularInitialized) {
      return envIsLoggedIn &&
          envIsStackBuild &&
          envIsInstalled;
    }

    return envIsLoggedIn &&
        envIsStackBuild &&
        envIsInstalled &&
        envIsCockpitBuild &&
        envIsEmailEditorBuild;
  }

  bool isCurrentTestCaseReady() {

    bool isEnvReady = isEnvironmentReady();

    final testCaseBrowserIds = currentTestCase.getBrowsers();

    if (testCaseBrowserIds.isEmpty) {
      return false; // TODO set error
    }

    bool imagesReady = true;
    for (var browserId in testCaseBrowserIds) {

      EnvironmentBrowser testCaseBrowser = browsers.getBrowserById(browserId);

      if (testCaseBrowser.id.isEmpty) {
        return false; // TODO set error
      }

      if (!testCaseBrowser.hasBrowserImage() || !testCaseBrowser.hasExecutorImage()) {
        // at least 1 of the images is not ready
        imagesReady = false;
      }

    }

    return isEnvReady &&
        imagesReady &&
        (envIsBrowserStackReady || !currentTextCaseNeedsBrowserstack);

  }

  reset() {

    // TODO reset (init) environment (retries!)
    envIsLoggedIn = false;
    envIsStackBuild = false;
    envIsInstalled = false;
    envIsCockpitBuild = false;
    envIsEmailEditorBuild = false;
    envIsBrowserStackReady = false;
    envIsExecutorBaseImagePulled = false;

    currentTextCaseNeedsBrowserstack = false;

    login.retry();
    stack.retry();
    install.retry();
    buildCockpit.retry();
    buildEmailEditor.retry();

    resetForTestCase();

  }

  resetForTestCase() {

    for (var browser in browsers.getBrowsers()) {
      browser.setBrowserImageStatus(false);
      browser.setExecutorImageStatus(false);
    }

  }

  bool canRetry() {
    return retryAttempts == 0 && currentTestCase.appLocation != 'remote';
  }

  retry(int tick) {

    if (!canRetry()) {
      if (currentTestCase.appLocation != 'remote') {
        appData.queue.abortAll('Resetting the environment failed.', tick);
      }
      return;
    }

    appData.queue.pauseQueue();

    if (!isInitialized) {

      isInitialized = true;

      appData.queue.abortItemsHighPriority('The environment has not been initialized yet, initializing...', tick);
      appData.queue.abortProcessingItems('The environment has not been initialized yet. This test case has been aborted and will be re-queued.', tick);

    }
    else if (!isAngularInitialized) {

      isAngularInitialized = true;

      retryAttempts++;

      appData.queue.abortItemsHighPriority('The angular environment has not been initialized yet, initializing...', tick);
      appData.queue.abortProcessingItems('The angular environment has not been initialized yet. This test case has been aborted and will be re-queued.', tick);

    }
    else {

      retryAttempts++;

      appData.queue.abortItemsHighPriority('Due to errors, the environment will be reset.', tick);
      appData.queue.abortProcessingItems('Due to environment errors, this test case has been aborted and will be re-queued.', tick);

    }

    reset();

    retryCurrentTestCase();

    appData.queue.continueQueue();

  }

  createEnvironmentQueueJobs() {

    appData.queue.addHighPriorityItem(QueueItemEnvLogin(finishedCallback: _onFinishedLogin));
    appData.queue.addHighPriorityItem(QueueItemEnvStack(finishedCallback: _onFinishedStack));
    appData.queue.addHighPriorityItem(QueueItemEnvInstall(finishedCallback: _onFinishedInstall));

    if (isAngularInitialized) {
      appData.queue.addHighPriorityItem(
          QueueItemEnvBuildCockpit(finishedCallback: _onFinishedBuildCockpit));
      appData.queue.addHighPriorityItem(QueueItemEnvBuildEmailEditor(
          finishedCallback: _onFinishedBuildEmailEditor));
    }
  }

  addTestCase(TestExecute testCase) {
    currentTestCase = testCase;
    appData.queue.addLowPriorityItem(QueueItemTestcaseExecute(
        testCase: testCase,
        finishedCallback: _onFinishedTestCaseExecute
    ));
  }

  retryCurrentTestCase() {
    appData.queue.addLowPriorityItemFirst(QueueItemTestcaseExecute(
        testCase: currentTestCase,
        finishedCallback: _onFinishedTestCaseExecute
    ));
  }

  // TODO return true/false plus getError
  prepareTestCase(TestExecute testCase) {

    currentTestCase = testCase;

    final testCaseBrowserIds = currentTestCase.getBrowsers();

    List<QueueItem> queueItems = [];

    if (testCaseBrowserIds.isEmpty) {
      return; // TODO set error
    }

    bool browserstackAdded = false;
    for (var browserId in testCaseBrowserIds) {

      EnvironmentBrowser testCaseBrowser = browsers.getBrowserById(browserId);

      if (testCaseBrowser.id.isEmpty) {
        return; // TODO set error
      }

      if (testCaseBrowser.needsBrowserStack) {
        // only add browserstack item if at least 1 item needs browser stack and it is not ready yet
        currentTextCaseNeedsBrowserstack = true;

        if (!browserstackAdded && !envIsBrowserStackReady) {
          browserstackAdded = true;
          queueItems.add(QueueItemEnvBrowserStack(
            browserstackUser: currentTestCase.browserstackUser,
            browserstackToken: currentTestCase.browserstackToken,
            finishedCallback: _onFinishedBrowserStack
          ));
        }

      }
      else {
        queueItems.add(QueueItemEnvPullImageBrowser(
            browser: browserId,
            finishedCallback: _onFinishedPullImageBrowser
        ));
      }

      queueItems.add(QueueItemEnvBuildImageExecutorBase(
        finishedCallback: _onFinishedBuildImageExecutorBase
      ));
      queueItems.add(QueueItemEnvBuildImageExecutorFinal(
        browser: browserId,
        finishedCallback: _onFinishedBuildImageExecutorFinal
      ));

    }

    if (queueItems.isEmpty) {
      return; // TODO set error
    }

    if (!isEnvironmentReady()) {
      createEnvironmentQueueJobs();
    }

    for (var item in queueItems) {
      appData.queue.addHighPriorityItem(item);
    }

  }

  _onFinishedLogin({int? status = QueueItem.statusFinished, int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
    envIsLoggedIn = true;
  }

  _onFinishedStack({int? status = QueueItem.statusFinished, int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
    envIsStackBuild = true;
  }

  _onFinishedInstall({int? status = QueueItem.statusFinished, int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
    envIsInstalled = true;
  }

  _onFinishedBuildCockpit({int? status = QueueItem.statusFinished, int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
    envIsCockpitBuild = true;
  }

  _onFinishedBuildEmailEditor({int? status = QueueItem.statusFinished, int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
    envIsEmailEditorBuild = true;
  }

  _onFinishedBrowserStack({int? status = QueueItem.statusFinished, int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
    envIsBrowserStackReady = true;
  }

  _onFinishedPullImageBrowser({int status = QueueItem.statusFinished, String browser = '', int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
    browsers.setBrowserImageStatus(browser, true);
  }

  _onFinishedBuildImageExecutorBase({int? status = QueueItem.statusFinished, int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
    envIsExecutorBaseImagePulled = true;
  }

  _onFinishedBuildImageExecutorFinal({int status = QueueItem.statusFinished, String browser = '', int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
    browsers.setExecutorImageStatus(browser, true);
  }

  _onFinishedTestCaseExecute({int status = QueueItem.statusFinished, TestExecute? testCase, int tick = 0}) {
    if (status == QueueItem.statusFailed) {
      retry(tick);
      return;
    }
  }

  setCurrentUser(PersistentExecUser user, {bool saveData = true}) {
    currentUser = user;
    if (saveData) {
      currentUser.lastUsed = DateTime.now().millisecond;
      appData.persistentData.addExecUser(currentUser);
    }

  }

  PersistentExecUser getCurrentUser() {
    return currentUser;
  }



}
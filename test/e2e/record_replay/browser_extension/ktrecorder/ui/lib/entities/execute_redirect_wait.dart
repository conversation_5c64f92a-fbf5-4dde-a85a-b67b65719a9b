import 'package:ui/entities/execute.dart';

class ExecuteRedirectWait extends Execute {

  static const commandId = 'redirect-wait';

  ExecuteRedirectWait();

  factory ExecuteRedirectWait.fromJson(Map<String, dynamic> json) {
    return ExecuteRedirectWait();
  }

  @override
  Map<String, dynamic> toJsonObject() {
    return {
      'command': getCommandId(),
      'target': '',
      'targetCss': '',
      'value': ''
    };
  }

  @override
  String getCommandId() {
    return commandId;
  }

}
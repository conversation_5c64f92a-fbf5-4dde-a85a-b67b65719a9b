
import 'dart:convert';

import 'package:http/http.dart';
import 'package:ui/entities/recording.dart';

class TestRetrieveResponse {

  final Response response;
  int statusCode = 0;
  String machineName = '';
  String testCaseName = '';
  late final Map<String, dynamic> json;

  TestRetrieveResponse({
    required this.response,
  }) {
    statusCode = response.statusCode;
    parseResponseBody();
  }

  parseResponseBody() {
    json = jsonDecode(response.body) as Map<String, dynamic>;
    machineName = json.containsKey('machineName') ? json['machineName'].toString() : '';
    testCaseName = json.containsKey('displayName') ? json['displayName'].toString() : '<No name>';
  }

  bool hasError() {
    return statusCode != 200;
  }

  @override
  String toString() {
    return "statusCode: $statusCode\nName: $testCaseName\nMachineName: $machineName\nData: $json";
  }

  Recording getRecording() {
    return Recording.fromJsonString(response.body);
  }

}
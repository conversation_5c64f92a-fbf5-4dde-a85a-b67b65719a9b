import 'dart:convert';
import 'package:ui/entities/dataset.dart';
import 'package:ui/entities/group.dart';
import 'package:ui/entities/option.dart';
import 'package:ui/entities/role.dart';

class UserDataSetUp {
  final List<String> users;
  final List<Role> roles;
  final List<Group> groups;
  final List<Option> tiers;
  final List<Dataset> datasets;

  UserDataSetUp({
    required this.users,
    required this.roles,
    required this.groups,
    required this.tiers,
    required this.datasets,
  });

  factory UserDataSetUp.fromJsonString(String jsonString) {

    Map<String, dynamic> json = jsonDecode(jsonString) as Map<String, dynamic>;
    List<Role> roles = List.castFrom(json['roles']).map<Role>((role) => Role.fromJsonObject(role)).toList();
    List<String> users = List.castFrom(json['users']);
    List<Group> groups = List.castFrom(json['groups']).map<Group>((group) => Group.fromJsonObject(group)).toList();
    List<Option> tiers = List.castFrom(json['tiers']).map<Option>((t) => Option.fromJsonObject(t)).toList();
    List<Dataset> datasets = List.castFrom(json['datasets']).map<Dataset>((d) => Dataset.fromJsonObject(d)).toList();

    return UserDataSetUp(
      users: users,
      roles: roles,
      groups: groups,
      tiers: tiers,
      datasets: datasets
    );

  }

}

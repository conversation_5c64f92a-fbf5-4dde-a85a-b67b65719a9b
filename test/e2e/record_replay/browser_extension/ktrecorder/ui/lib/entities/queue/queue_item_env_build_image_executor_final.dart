
import 'package:ui/entities/environment/environment_task_build_image_executor_final.dart';
import 'package:ui/entities/queue/queue_item.dart';

import '../environment/environment_task.dart';

class QueueItemEnvBuildImageExecutorFinal extends QueueItem {

  final String browser;
  late final EnvironmentTaskBuildImageExecutorFinal task;

  QueueItemEnvBuildImageExecutorFinal({
    super.finishedCallback,
    required this.browser
  }) {
    task = EnvironmentTaskBuildImageExecutorFinal(
        browser: browser
    );
  }

  // --- Overwrites ---

  @override
  int getDelay() {
    return 0;
  }

  @override
  int execute() {

    int taskStatus = task.process();
    writeLog(task.readLog());

    switch(taskStatus) {
      case EnvironmentTask.statusSuccess:
        status = QueueItem.statusFinished;
        break;
      case EnvironmentTask.statusTimeout:
      case EnvironmentTask.statusFailed:
        status = QueueItem.statusFailed;
        break;
      case EnvironmentTask.statusInit:
      case EnvironmentTask.statusWaiting:
      default:
        status = QueueItem.statusProcessing;
        break;
    }

    return status;
  }

  @override
  String title() {
    return 'Build final executor image for browser $browser';
  }

  @override
  String info() {

    switch(status) {
      case QueueItem.statusWaiting:
        return 'Waiting...';
      case QueueItem.statusProcessing:
      case QueueItem.statusExecuting:
        return 'Processing...';
      case QueueItem.statusFinished:
        return 'Done.';
      case QueueItem.statusCanceled:
        return 'Canceled.';
      case QueueItem.statusFailed:
        return 'Failed.';
    }

    return 'Unknown status';

  }

  @override
  onFinished() {
    finishedTick = lastTick;
    Function.apply(finishedCallback!, [], {#status: status, #browser: browser, #tick: finishedTick});
  }

}

/*
TODO: the modal dialog is used 3 times, make a function/widget

import '../entities/persistentData/persistent_exec_user.dart';
import '../entities/persistentData/persistnt_exec_user.dart';
import '../singletons/app_data.dart';

onRunTest(BuildContext context, List<int> ids) {

  bool first = true;
  FutureBackend backend = FutureBackend();
  Future<List<TestRetrieveResponse>> futureRetrieveResponse = backend.testRetrieveMultiple(ids);

  late final TestExecute execTest;
  late List<TestRetrieveResponse> retrieveResponses;
  String errorMessage = '';

  Widget content = const Text('...');

  return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
            builder: (context, setState) {

              return AlertDialog(
                content: FutureBuilder<dynamic>(
                  future: futureRetrieveResponse,
                  builder: (context, snapshot) {

                    if (snapshot.hasData && first) {

                      first = false;

                      retrieveResponses = snapshot.data;

                      if (retrieveResponses.isNotEmpty) {
                        execTest = TestExecute(machineName: '', testCaseName: '');
                        formExecuteTest = execTest.convertToForm();
                        content = DynamicFormRenderer(form: formExecuteTest, modeEdit: true, updateCallback: onUpdateFormExecuteTest);
                      }

                    } else if (snapshot.hasError) {
                      content = Text('ERROR: ${snapshot.error}');
                    }

                    return content;

                  },
                ),
                actions: <Widget>[
                  if (errorMessage != '') Text(
                      errorMessage,
                      style: const TextStyle(
                          color: Colors.red
                      )
                  ),
                  ElevatedButton(
                    //color: Colors.green,
                    //textColor: Colors.white,
                    child: const Text('Run'),
                    onPressed: () async {

                      bool accessChecked = false;
                      bool valid = true;
                      for(TestRetrieveResponse response in retrieveResponses) {
                        TestExecute exec = TestExecute(testCaseName: response.testCaseName, machineName: response.machineName);
                        exec.convertFromForm(formExecuteTest);

                        final env = FutureEnvironment();

                        if (!accessChecked && exec.appLocation == 'remote') {
                          accessChecked = true;
                          valid = await env.checkLogin(exec.domain, exec.execUser, exec.execUserPwd);
                          if (valid) {
                            appData.environment.setCurrentUser(
                                PersistentExecUser(
                                    username: exec.execUser,
                                    password: exec.execUserPwd,
                                    domain: exec.domain
                                )
                            );
                          }
                        }

                        if (valid) {
                          appData.environment.addTestCase(exec);
                        }

                      }

                      setState(() {

                        if (valid) {
                          Navigator.pop(context);
                        }
                        else {
                          errorMessage = 'Login failed.';
                        }

                      });
                    },
                  ),
                ],
              );
            });
      });

}

 */
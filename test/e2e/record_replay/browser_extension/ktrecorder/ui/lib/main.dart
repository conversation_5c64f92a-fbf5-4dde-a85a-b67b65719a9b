// TODO U4nATY2f
/// Chrome extension with Flutter:
///   https://www.yayocode.com/2021/07/how-to-create-google-chrome-extension.html
///   Build: flutter build web --web-renderer html --csp
///   Container: make e2e-rr

// TODO: e2e-test-recorder:U4nATY2f
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:ui/entities/persistentData/persistent_data.dart';
import 'package:ui/widgets/action_page_view.dart';
import 'package:ui/singletons/app_data.dart';
import 'package:ui/widgets/tab_data.dart';
import 'package:ui/widgets/tab_export.dart';
import 'package:ui/widgets/tab_meta.dart';
import 'package:ui/widgets/tab_queue.dart';
import 'package:ui/js_chrome_get_data.dart';


import 'entities/recording.dart';

import './widgets/tab_init.dart';

void main() {
  runApp(const MyApp());
}

/// Unfortunately I did not get the javascript promise to work here
/// so just wait 100 ms
Future<Recording> fetchRecording() async {

  await Future.delayed(const Duration(milliseconds: 500));

  chromeGetData(true);

  await Future.delayed(const Duration(milliseconds: 100));

  String chromeData = chromeGetData(false);

  appData.persistentData = PersistentData.fromJsonString(chromeData);
  appData.environment.setCurrentUser(
      appData.persistentData.getExecUsers(appData.environment.currentDomain).first,
      saveData: false
  );

  return Recording.fromExtension(chromeData);

}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    Color selectedColor = ColorSeed.deepOrange.color;
    Brightness selectedBrightness = Brightness.light;
    return MaterialApp(
      title: 'E2E Recorder',
      theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: selectedColor,
            brightness: selectedBrightness,
          )),
      home: const MyHomePage()
      /*
      home: const DefaultTabController(
        length: 5,
        child: MyHomePage(title: 'KlickTipp E2E Recorder'),
      ),

       */
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({Key? key}) : super(key: key);

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> with SingleTickerProviderStateMixin {

  Key restartKey = UniqueKey();

  late Future<Recording> futureRecording;

  late TabController _tabController;

  late bool _initialized;

  final Timer cron = Timer.periodic(const Duration(seconds: 1), (timer) {
    appData.queue.process(timer.tick);
  });

  @override
  void initState() {
    super.initState();

    _initialized = false;
    futureRecording = fetchRecording();

    // Note: needs SingleTickerProviderStateMixin
    _tabController = TabController(vsync: this, length: 6, initialIndex: 0);

    _tabController.addListener(() {
      if (!_canAccessTab(_tabController.index)) {
        setState(() {
          _tabController.index = _tabController.previousIndex;
        });
      }

    });

  }

  void restartApp() {

    setState(() {
      restartKey = UniqueKey();
      _initialized = false;
      futureRecording = fetchRecording();
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return KeyedSubtree(
      key: restartKey,
      child: FutureBuilder<dynamic>(
        future: futureRecording,
        builder: (context, snapshot) {

          if (snapshot.hasData) {

            //if (snapshot.data!.actions.isNotEmpty) {

              if (!_initialized) {
                appData.recording = snapshot.data;
                _initialized = true;
              }


              if (appData.recording.status == Recording.statusRecording) {
                return Scaffold(
                    body: Center(
                      child: Column(
                        children: [
                          const Text('The Browser extension is currentlich recording.'),
                          ElevatedButton(
                            child: const Text('Abort Recording'),
                            onPressed: () {
                              chromeCancelRecording();
                              restartApp();
                            },
                          ),
                        ],
                      ),
                    )
                );
              }

              // TODO https://codewithandrea.com/articles/flutter-tab-bar-navigation/

              return Scaffold(
                appBar: AppBar(
                  bottom: TabBar(
                    controller: _tabController,
                    tabs: [
                      const Tab(icon: Icon(Icons.create_new_folder_outlined)),
                      Tab(icon: Icon(
                        Icons.data_object_rounded,
                        color: _getTabColor(3),
                      )),
                      Tab(icon: Icon(
                        Icons.checklist_rounded,
                        color: _getTabColor(2),
                      )),
                      Tab(icon: Icon(
                        Icons.info_outline_rounded,
                        color: _getTabColor(1),
                      )),
                      Tab(icon: Icon(
                        Icons.build_circle_outlined,
                        color: _getTabColor(4),
                      )),
                      Tab(icon: Icon(
                        Icons.build_circle_outlined,
                        color: _getTabColor(5),
                      )),
                    ],
                  ),
                  // Here we take the value from the MyHomePage object that was created by
                  // the App.build method, and use it to set our appbar title.
                  title: Text('KlickTipp E2E Recorder${appData.recording.getDisplayName()}'),
                ),
                body: TabBarView(
                  controller: _tabController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    TabInitWidget(navigateToTab: navigateToTab),
                    const TabDataWidget(),
                    ActionPageViewWidget(navigateToTab: navigateToTab),
                    TabMetaWidget(navigateToTab: navigateToTab),
                    TabExportWidget(navigateToTab: navigateToTab),
                    const TabQueueWidget(),
                  ]
                ),
              );
            /*
            }
            else {
              return const Text('Recording has no actions.');
            }

             */
            //return Text(snapshot.data!.startUrl);
          } else if (snapshot.hasError) {
            return const Text('Error loading recording from Chrome extension');
          }

          // By default, show a loading spinner.
          return const CircularProgressIndicator();

        },

    )
    );
  }

  bool _canAccessTab(int index) {

    if (index == 5) {
      return true;
    }

    if (appData.recording.status == Recording.statusInit && index > 0) {
      return false;
    }

    if ( index >= 3 && appData.recording.steps.isEmpty) {
      return false;
    }

    return true;

  }

  Color _getTabColor(int index) {

    if (!_canAccessTab(index)) {
      return Colors.grey;
    }

    return Colors.blue;
  }

  void navigateToTab(int? index) {

    setState(() {

      if (index != null) {
        _tabController.index = index;
      }

    });
  }

}

enum ColorSeed {
  baseColor('M3 Baseline', Color(0xff6750a4)),
  indigo('Indigo', Colors.indigo),
  blue('Blue', Colors.blue),
  teal('Teal', Colors.teal),
  green('Green', Colors.green),
  yellow('Yellow', Colors.yellow),
  orange('Orange', Colors.orange),
  deepOrange('Deep Orange', Colors.deepOrange),
  pink('Pink', Colors.pink),
  brightBlue('Bright Blue', Color(0xFF0000FF)),
  brightGreen('Bright Green', Color(0xFF00FF00)),
  brightRed('Bright Red', Color(0xFFFF0000));

  const ColorSeed(this.label, this.color);
  final String label;
  final Color color;
}
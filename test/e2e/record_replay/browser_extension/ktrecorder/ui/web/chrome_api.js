console.log('chrome api loaded 13.3');

// add dev data for development, it's big so extra file
const my_awesome_script = document.createElement('script');
my_awesome_script.setAttribute('src','chrome_dev_data.js');
document.head.appendChild(my_awesome_script);


/*
const storageCache = {};
// Asynchronously retrieve data from storage.sync, then cache it.
const initStorageCache = getAllStorageSyncData().then(items => {
  // Copy the data retrieved from storage into storageCache.
  Object.assign(storageCache, items);
});
*/
window['chromeData'] = {};

window['chromeGetData'] = (request) => {

  if (request) {

    if (chrome.storage) {

      getAllStorageSyncData().then(items => {
        // Copy the data retrieved from storage into storageCache.
        //Object.assign(window['chromeData'], items);

        console.log('chrome api storage items', items);

        var execData = items['executeData'] || {};
        execData['url'] = items['startUrl'] || '';

        window['chromeData'] = {
          id: items['id']  || 0,
          status: items['status']  || 'init',
          extensionStatus: items['extensionStatus']  || 'init',
          displayName: items['displayName'] || '',
          description: items['description'] || '',
          machineName: items['machineName'] || '',
          url: items['startUrl'] || '',
          recording: items['recording'] || [],
          executeData: items['executeData'],
          persistent: items['persistent'] || {}
        }

      });

    }
    else {
      window['chromeData'] = devData;

    }

  }

  console.log('data from extension', window['chromeData']);

  return JSON.stringify(window['chromeData']);

}

// Reads all data out of storage.sync and exposes it via a promise.
//
// Note: Once the Storage API gains promise support, this function
// can be greatly simplified.
async function getAllStorageSyncData() {
  // Immediately return a promise and start asynchronous work
  return new Promise((resolve, reject) => {
    // Asynchronously fetch all data from storage.sync.
    chrome.storage.local.get(null, (result) => {
      // Pass any observed errors down the promise chain.
      if (chrome.runtime.lastError) {
        return reject(chrome.runtime.lastError);
      }
      // Pass the data retrieved from storage down the promise chain.
      resolve(result);
    });
  });
}

function chromeSetData(json) {

  const payload = JSON.parse(json);

  if (chrome.storage) {
    //chrome.storage.local.set(data);
    chrome.runtime.sendMessage('', {
      type: 'update-from-app',
      data: payload
    }, {}, (response) => {});
  }

}

function chromeSetPersistentData(json) {

  const payload = JSON.parse(json);

  console.log('chromeSetPersistentData', payload);

  if (chrome.storage) {
    //chrome.storage.local.set(data);
    chrome.runtime.sendMessage('', {
      type: 'update-from-app-persistent-data',
      data: payload
    }, {}, (response) => {});
  }

}

function chromeCancelRecording() {

  if (chrome.storage) {
    //chrome.storage.local.set(data);
    chrome.runtime.sendMessage('', {
      type: 'cancel-recording',
      data: {}
    }, {}, (response) => {});
  }

}


function setPrepareEnvironemntStatus(inProgress) {

  if (chrome.storage) {
    chrome.storage.local.set({
      isPreparingEnvironment: !!inProgress
    });
  }
}


function exportRecording() {
  console.log(JSON.stringify(window['chromeData']));
}



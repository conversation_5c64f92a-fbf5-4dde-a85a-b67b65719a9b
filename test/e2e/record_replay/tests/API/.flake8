[flake8]
ignore =
    # expected 2 blank lines, found 0
    E302,
    # line too long (82 > 79 characters)
    E501,
    # do not use bare except, specify exception instead
    E722,
    # expected 2 blank lines after end of function or class
    E305
    # allow multiline conditions with binary operator like and/or
    W504

exclude =
    # No need to traverse our git directory
    .git,
    # There's no value in checking cache directories
    __pycache__,

# Re-enable later. Make functions simpler!
# max-complexity = 10

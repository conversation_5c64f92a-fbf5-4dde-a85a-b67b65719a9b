# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2023, Klick-Tipp
# Email: <EMAIL>
"""In local context (not pipeline) we can retrieve pwds from AWS with the SecretManager.
As long as you have logged in (make docker-login on klicktipp src root)."""

import boto3
from botocore.exceptions import ClientError
import re
import json
import os


class SecretManager:
    def __init__(self):
        self.secret_name = "kt-local-e2e-logins"
        self.region_name = "eu-west-1"
        self.secrets_dict = None
        token_string = os.getenv("AWS_WEB_IDENTITY_TOKEN_FILE", "")
        print(f"AWS_WEB_IDENTITY_TOKEN_FILE : {token_string}")
        self.load_secrets()

    def load_secrets(self):
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name=self.region_name
        )

        try:
            get_secret_value_response = client.get_secret_value(
                SecretId=self.secret_name
            )
        except ClientError as e:
            raise e

        secret = get_secret_value_response['SecretString']
        secret_string_cleaned = re.sub(r'\s', '', secret)
        self.secrets_dict = json.loads(secret_string_cleaned)

    def get_secret(self, user, domain):
        if self.secrets_dict:
            for usr_pwd_pair in self.secrets_dict:
                if usr_pwd_pair.get("username") == user:
                    usr_pwd = usr_pwd_pair.get("password")
                    if isinstance(usr_pwd, dict):
                        return usr_pwd.get(domain, None)
                    return usr_pwd
        return None


__author__ = "Simon Werling"
__copyright__ = "Copyright 2023, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

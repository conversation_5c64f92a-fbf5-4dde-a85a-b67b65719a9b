#!/bin/bash

ls -ltr
pwd

mkdir /home/<USER>/.docker > /dev/null 2>&1
cp /home/<USER>/.docker_from_host/config.json /home/<USER>/.docker/config.json
jq 'del(.credsStore, .currentContext)' /home/<USER>/.docker/config.json > /home/<USER>/.docker/config.json.tmp && mv /home/<USER>/.docker/config.json.tmp /home/<USER>/.docker/config.json

celery -A test.e2e.record_replay.flask_app.celery_app worker -l info

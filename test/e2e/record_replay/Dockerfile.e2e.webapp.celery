ARG E2ERR_DIND_BASE_DOCKER_TAG=latest
FROM record_replay-dind-base:${E2ERR_DIND_BASE_DOCKER_TAG}

ARG DOCKER_HOST_DOCKER_GROUP
ARG DOCKER_HOST_USR
ARG DOCKER_HOST_USR_NAME
ARG DOCKER_HOST_USR_GROUP_NAME
ARG DOCKER_HOST_USR_GROUP

RUN echo "ARG DOCKER_HOST_DOCKER_GROUP   = ${DOCKER_HOST_DOCKER_GROUP}"
RUN echo "ARG DOCKER_HOST_USR            = ${DOCKER_HOST_USR}"
RUN echo "ARG DOCKER_HOST_USR_NAME       = ${DOCKER_HOST_USR_NAME}"
RUN echo "ARG DOCKER_HOST_USR_GROUP_NAME = ${DOCKER_HOST_USR_GROUP_NAME}"
RUN echo "ARG DOCKER_HOST_USR_GROUP      = ${DOCKER_HOST_USR_GROUP}"

LABEL name="Celery integrates rabbitmq with flask." Version="1.0.0"

ENV MAC_DOCKER_SOCK_GROUP_ID=${MAC_DOCKER_SOCK_GROUP_ID}
ENV MAC_DOCKER_SOCK_GROUP_NAME=${MAC_DOCKER_SOCK_GROUP_NAME}

# On MacOS users are sometimes in gid 20 (staff).
# This is the dialout group id.
# To allow re-assign of gid below to gid 20
#          we need to change the gid for dialout
RUN groupmod -g 9000 dialout

### In some docker images we could have the situation, that the ID we want to assign to the docker group inside
#   the docker container, is already used by another group of the system. In that situation we need to re-assign
#   a new GID to the group which is holding the needed group ID.
ARG NEW_GID=898

# Find the group with the GID that we want to use for the docker group, and change its GID to NEW_GID
RUN existing_group=$(getent group ${DOCKER_HOST_DOCKER_GROUP} | cut -d: -f1) && \
    if [ -n "$existing_group" ]; then \
        # If the group exists, change the group id to NEW_GID
        groupmod -g ${NEW_GID} $existing_group; \
    fi

# We get permission problems when docker group ID is different from
# our group ID inside the docker container
RUN groupmod -g ${DOCKER_HOST_DOCKER_GROUP} docker || echo "No valid group : ${DOCKER_HOST_DOCKER_GROUP}"

# Create files on mounted volumes with native hosts user/group ID
RUN groupadd -r -g 1000 celery_user

RUN adduser -u 1000 --gid 1000 --disabled-password --gecos "" --shell /bin/sh celery_user \
 && mkdir /home/<USER>/bin

RUN usermod -u ${DOCKER_HOST_USR} celery_user
RUN groupmod -g ${DOCKER_HOST_USR_GROUP} celery_user
RUN find /home -user 1000 -exec chown -h ${DOCKER_HOST_USR} {} \;
RUN find /home -group 1000 -exec chgrp -h ${DOCKER_HOST_USR_GROUP} {} \;
RUN usermod -g ${DOCKER_HOST_USR_GROUP} celery_user

RUN usermod -aG docker celery_user

USER celery_user

CMD ["echo", "Hello"]

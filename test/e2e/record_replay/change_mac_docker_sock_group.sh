#!/bin/sh

if [ $(getent group "${MAC_DOCKER_SOCK_GROUP_NAME}") ]; then
  echo "group exists : ${MAC_DOCKER_SOCK_GROUP_NAME}";
  CURR_DOCKER_SOCK_GROUP_ID_IN_CONTAINER=`getent group $MAC_DOCKER_SOCK_GROUP_NAME | cut -d: -f3`;
  echo "current group id : ${CURR_DOCKER_SOCK_GROUP_ID_IN_CONTAINER}"
  echo "Try to change group ID to : ${MAC_DOCKER_SOCK_GROUP_ID}"
  groupmod -g $MAC_DOCKER_SOCK_GROUP_ID $MAC_DOCKER_SOCK_GROUP_NAME || echo "Not able to change the group ID to ${MAC_DOCKER_SOCK_GROUP_ID}";
else
  echo "group does not exist : ${MAC_DOCKER_SOCK_GROUP_NAME}";
  echo "create group";
  groupadd -g $MAC_DOCKER_SOCK_GROUP_ID $MAC_DOCKER_SOCK_GROUP_NAME;
fi

echo "Add user to group"
usermod -aG $MAC_DOCKER_SOCK_GROUP_NAME celery_user
groups celery_user
echo "set permission group of /var/run/docker.sock"
chgrp $MAC_DOCKER_SOCK_GROUP_NAME /var/run/docker.sock
ls -l /var/run/docker.sock
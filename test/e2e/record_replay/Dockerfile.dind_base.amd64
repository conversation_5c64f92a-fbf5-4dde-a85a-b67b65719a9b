FROM python:3.12-slim-bookworm

RUN apt-get update
RUN apt-get -y install \
	ca-certificates \
	gnupg \
    curl \
    wget \
    make \
    git \
    unzip \
    iputils-ping \
    jq

RUN install -m 0755 -d /etc/apt/keyrings
RUN curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
RUN chmod a+r /etc/apt/keyrings/docker.gpg

RUN echo \
    "deb [arch="$(dpkg --print-architecture)" signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian \
    "$(. /etc/os-release && echo "$VERSION_CODENAME")" stable" | \
    tee /etc/apt/sources.list.d/docker.list > /dev/null
RUN apt-get update
RUN apt-get -y install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

RUN wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/download/v4.44.6/yq_linux_amd64
RUN chmod a+x /usr/local/bin/yq

RUN curl -LO https://github.com/getsops/sops/releases/download/v3.9.2/sops-v3.9.2.linux.amd64
RUN mv sops-v3.9.2.linux.amd64 /usr/local/bin/sops
RUN chmod +x /usr/local/bin/sops

RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscli2.zip" && \
    unzip awscli2.zip && \
    ./aws/install

RUN /usr/local/bin/python -m pip install --upgrade pip

COPY ./requirements-dind.txt /
RUN pip3 install -r requirements-dind.txt
RUN pip3 freeze

CMD ["echo", "Hello"]

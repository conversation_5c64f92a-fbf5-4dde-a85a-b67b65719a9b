services:
  rabbitmq-e2e-rr:
    build:
      platforms:
        - "${DOCKER_IMAGES_PLATFORM}"
      context: rabbitmq
      dockerfile: Dockerfile.rabbitmq
    image: record_replay_rabbitmq
    ports:
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=rmq_record_replay
      - RABBITMQ_USER=rmq_record_replay
      - RABBITMQ_DEFAULT_PASS=rmq_record_replay
      - RABBITMQ_PASS=rmq_record_replay
      - RABBITMQ_DEFAULT_VHOST=my_vhost

  worker:
    depends_on:
      - rabbitmq-e2e-rr
      - redis-e2e-rr
    build:
      platforms:
        - "${DOCKER_IMAGES_PLATFORM}"
      context: ../../../
      dockerfile: test/e2e/record_replay/Dockerfile.e2e.webapp.celery
    environment:
      - MAC_DOCKER_SOCK_GROUP_ID=${MAC_DOCKER_SOCK_GROUP_ID}
      - MAC_DOCKER_SOCK_GROUP_NAME=${MAC_DOCKER_SOCK_GROUP_NAME}
      - DOCKER_HOST_PLATFORM=${DOCKER_HOST_PLATFORM}
      - E2ERR_DIND_BASE_DOCKER_TAG=${E2ERR_DIND_BASE_DOCKER_TAG}
    image: record_replay_worker
    command: ['${KLICKTIPP_LOCAL_ROOT}/test/e2e/record_replay/worker_service_docker_cmd.sh']
    volumes:
      - e2e-rr-klicktipp-vol:${KLICKTIPP_LOCAL_ROOT}
      - ${DOCKER_SOCK}:/var/run/docker.sock
      - home-vol-aws:/home/<USER>/.aws:rw
      - ${DOCKER_VOLUME_MOUNT_PREFIX}${HOME}/.docker/config.json:/home/<USER>/.docker_from_host/config.json:rw
    working_dir: ${KLICKTIPP_LOCAL_ROOT}

  redis-e2e-rr:
    image: redis:7.0.9
    ports:
      - "6379:6379"

#  worker_monitor:
#    depends_on:
#      - worker
#    build:
#      context: ../../../
#      dockerfile: test/e2e/record_replay/Dockerfile.e2e.webapp.celery
#    image: record_replay_worker
#    command: ['celery', '-A', 'test.e2e.record_replay.flask_app.celery_app', 'flower', "--port=5555"]
#    ports:
#      - "5555:5555"
#    volumes:
#      - e2e-rr-klicktipp-vol:${KLICKTIPP_LOCAL_ROOT}
#    working_dir: ${KLICKTIPP_LOCAL_ROOT}

  flask:
    depends_on:
      - worker
    image: record_replay_flask
    environment:
      - FLASK_DEBUG=0
      - FLASK_ENV=development
      - FLASK_APP=${KLICKTIPP_LOCAL_ROOT}/test/e2e/record_replay/flask_app/app.py
      - MAC_DOCKER_SOCK_GROUP_ID=${MAC_DOCKER_SOCK_GROUP_ID}
      - MAC_DOCKER_SOCK_GROUP_NAME=${MAC_DOCKER_SOCK_GROUP_NAME}
      - DOCKER_HOST_PLATFORM=${DOCKER_HOST_PLATFORM}
      - E2ERR_DIND_BASE_DOCKER_TAG=${E2ERR_DIND_BASE_DOCKER_TAG}
    command: ['${KLICKTIPP_LOCAL_ROOT}/test/e2e/record_replay/flask_app/flask_service_docker_cmd.sh']
    volumes:
      - e2e-rr-klicktipp-vol:${KLICKTIPP_LOCAL_ROOT}
      - ${DOCKER_SOCK}:/var/run/docker.sock
      - home-vol-aws:/home/<USER>/.aws:rw
      # Why we do that? See longer comment below at volume definitions
      - ${DOCKER_VOLUME_MOUNT_PREFIX}${HOME}/.docker/config.json:/home/<USER>/.docker_from_host/config.json:rw
    working_dir: ${KLICKTIPP_LOCAL_ROOT}
    ports:
      - "5006:5000"
    expose:
      - "5000"
    build:
      platforms:
        - "${DOCKER_IMAGES_PLATFORM}"
      context: ../../../
      dockerfile: test/e2e/record_replay/flask_app/Dockerfile.dind

  browserextension:
    image: record_replay-flutter-ui:${E2ERR_FLUTTER_UI_DOCKER_TAG}
    ports:
     - "5001:80"
    expose:
      - "80"

#  frontend:
#    depends_on:
#      - flask
#    build: ./webapp_frontend
#    ports:
#     - "5001:80"
#    expose:
#      - "80"

volumes:
  e2e-rr-klicktipp-vol:
    name: e2e-rr-klicktipp-vol
    driver_opts:
      o: bind
      device: ${DOCKER_VOLUME_MOUNT_PREFIX}${KLICKTIPP_LOCAL_ROOT}
      type: none

  home-vol-aws:
    name: home-vol-aws
    driver_opts:
      o: bind
      device: ${DOCKER_VOLUME_MOUNT_PREFIX}${HOME}/.aws
      type: none

# Mac OrbStack uses own buildx (DOCKER_BUILD_KIT) binaries linked as symbolic link inside
# the .docker directory.
# When building with buildkit enabled inside the docker container (what is the case, we need buildx), those binaries can
# not be executed - because not existing inside the docker container. So far. When i only
# mount the config.json, the make docker login fails,
# because at some point (make docker-login) the file is regenerated, means, deleted and recreated. This is not
# possible with a mounted file inside a container/service.
# Solution: So i have to mount the whole .docker directory in another
# non-standard .docker dir and copy only the config.json to the standard .docker dir.
# A nice side effect: During orbstack restarts or update installations, the .config.json gets new orbstack entries like
# credstore and context. We do not use the orb credstore and we don't want to keep the orb context. Since we have a copy
# of the config.json, we can remove those entries inside the container and keep the original untouched.
#
#  home-vol-docker:
#    name: home-vol-docker
#    driver_opts:
#      o: bind
#      device: ${DOCKER_VOLUME_MOUNT_PREFIX}${HOME}/.docker
#      type: none

networks:
  default:
    name: kt-e2e-net
    external: true

---

###############################################################################
#
#                        k8s Tests based on environment and URLS
#
###############################################################################

###############################################################################
# Templates
###############################################################################

###############################################################################
# Jobs
###############################################################################

e2e:url-tests:
  extends: .e2e-rr-template
  stage: e2e
  script:
    - export KLICKTIPP_TEST_URLS_BASE_DOMAIN="${KLICKTIPP_TEST_URLS_BASE_DOMAIN:-${KLICKTIPP_NAMESPACE}.${KLICKTIPP_ENVIRONMENT}.${CLUSTER_BASE_DOMAIN}}"
    - echo "KLICKTIPP_NAMESPACE=${KLICKTIPP_NAMESPACE}"
    - echo "KLICKTIPP_ENVIRONMENT=${KLICKTIPP_ENVIRONMENT}"
    - echo "CLUSTER_BASE_DOMAIN=${CLUSTER_BASE_DOMAIN}"
    - echo "KLICKTIPP_TEST_URLS_BASE_DOMAIN=${KLICKTIPP_TEST_URLS_BASE_DOMAIN}"
    - cd test/unit/url
    - make test

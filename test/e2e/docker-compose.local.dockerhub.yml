# Author: <PERSON>
# Copyright: Copyright 2025, Klick-Tipp
# Email: <EMAIL>

# Local Service stack for test scripts against local selenium browsers
# - chrome
# - firefox

services:
  tester:
    depends_on:
      - selenium

  selenium:
    platform: "${DOCKER_IMAGES_PLATFORM}"
    image: "035931995993.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/${DOCKER_HUB_SELENIUM_IMAGE}:${BROWSER_TAG}"
    hostname: "selenium-host-${BROWSER}"
    shm_size: 2g
    # volumes:
    #   # We need the shm mount. Pages with 'heavy' content or unreliable includes of external resources,
    #   # could end up in waiting forever for the (external) resources (banners, vids, etc.).
    #   # By usage of the docker hosts shm, we can increase power for the chrome/firefox container.
    #   # Additionally, we set page/script loading timeouts in the conftest.py when creating the driver.
    #   - /dev/shm:/dev/shm
    environment:
      # We need to set the resolution for the browser container.
      # Need to be at least size of the browser window.
      # Ran into a WebDriver exception, when container desktop resolution was smaller as browser resolution.
      - SCREEN_WIDTH=${SCREEN_WIDTH}
      - SCREEN_HEIGHT=${SCREEN_HEIGHT}

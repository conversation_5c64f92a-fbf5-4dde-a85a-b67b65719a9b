---

###############################################################################
#
#                        STAGING - E2E Gitlab Pipeline
#
###############################################################################

###############################################################################
# Templates
###############################################################################

.e2e-chrome:staging-template:
  extends: .e2e-chrome-template
  variables:
    TESTSET: ci_staging
    KLICKTIPP_URL: www.klicktipp-staging.com

###############################################################################
# STAGING Jobs
###############################################################################

c:smoke0:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_smoke0

c:smoke_bee0:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_smoke_bee0

c:smoke_bee1:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_smoke_bee1

c:bee_templates:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_bee_templates

c:dkim:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_smoke_dkim

c:listbuildings0:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_listbuildings0

c:listbuildings1:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_listbuildings1

c:smoke_eol0:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_smoke_eol0

c:smoke_eol1:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_smoke_eol1
  # whitelabel tests. take longer. not splittable. only nightly.
  #
  # -> new decision (Edmund idea): dont care about test execution duration for deployments during working hours
  # Enable whitelabel tests for every deployment during working hours
  # -> increases pipeline test exeuction after deployment by 20 minutes.
  #
  ## Run job on scheduled pipelines in staging branch
  # rules:
  #   - if: $CI_COMMIT_REF_NAME =~ /^staging$/ && $CI_PIPELINE_SOURCE == "schedule"
  #   # - if: $CI_COMMIT_REF_NAME =~ /ci\/speedup_e2e_test_jobs/ && $CI_PIPELINE_SOURCE == "schedule"
  #     when: always
  #     allow_failure: false
  #   ## Otherwise do not run e2e tests
  #   - when: never

c:smoke_eol2:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_smoke_eol2
  # requestforwarder tests. take longer. not splittable. only nightly.
  #
  # -> new decision (Edmund idea): dont care about test execution duration for deployments during working hours
  # Enable requestforwarder tests for every deployment during working hours
  # -> increases pipeline test exeuction after deployment by 20 minutes.
  #
  # rules:
  #   ## Run job on scheduled pipelines in staging branch
  #   - if: $CI_COMMIT_REF_NAME =~ /^staging$/ && $CI_PIPELINE_SOURCE == "schedule"
  #   # - if: $CI_COMMIT_REF_NAME =~ /ci\/speedup_e2e_test_jobs/ && $CI_PIPELINE_SOURCE == "schedule"
  #     when: always
  #     allow_failure: false
  #   ## Otherwise do not run e2e tests
  #   - when: never

c:smoke_eol3:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_smoke_eol3

c:subscriberarea0:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_subscriberarea0

c:subscriberarea1:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_subscriberarea1

c:subscriberarea2:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_subscriberarea2

c:subscriberarea3:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_subscriberarea3

c:subscriberarea4:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_subscriberarea4

c:affiliate:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_affiliate

c:mehrfachversand0:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_mehrfachversand0

c:mehrfachversand1:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_mehrfachversand1

c:bee_template_images:stg:
  extends: .e2e-chrome:staging-template
  variables:
    TESTSET: ci_bee_template_images
  rules:
    ## Run job on scheduled pipelines
    - if: $PARENT_PIPELINE_SOURCE == "schedule"
      when: always
      allow_failure: false
    ## Otherwise do not run e2e tests
    - when: never


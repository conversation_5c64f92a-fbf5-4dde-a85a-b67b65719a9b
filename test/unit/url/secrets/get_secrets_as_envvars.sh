#!/usr/bin/env bash

set -e

# Check if sops binary exists in PATH
if ! command -v sops &> /dev/null; then
  echo "Error: 'sops' binary not found in \$PATH."
  echo ""
  echo "Please install the required 'sops' binary. See docs under: https://klicktipp.atlassian.net/wiki/spaces/INFRA/pages/2726166544/SOPS+Secrets+Operations"
  echo ""
  exit 1
fi

SECRETS_FILES="${*}"

for f in ${SECRETS_FILES}; do
	sops --decrypt --output-type dotenv ${f}
done


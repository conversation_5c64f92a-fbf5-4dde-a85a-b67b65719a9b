{"homepage_redirection": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/", "expected_status": 302, "expected_location": "https://klick-tipp.com/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de", "expected_status": 302, "expected_location": "https://klick-tipp.com/"}], "iphone_icons_redirection": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-114x114.png", "expected_status": 302, "expected_location": "https://klick-tipp.com/misc/img/apple-touch-icon-114x114.png", "expected_cache_control": "max-age=1209600"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-114x114.png", "expected_status": 200, "expected_cache_control": "max-age=1209600", "follow_redirects": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-precomposed.png", "expected_status": 302, "expected_location": "https://klick-tipp.com/misc/img/apple-touch-icon-precomposed.png", "expected_cache_control": "max-age=1209600"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-precomposed.png", "expected_status": 200, "expected_cache_control": "max-age=1209600", "follow_redirects": true}], "redirect_bcr_tests": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr", "expected_status": 301, "expected_location": "https://www.klicktipp.com/bcr/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr/", "expected_status": 301, "expected_location": "https://www.klicktipp.com/bcr/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr/somepath", "expected_status": 301, "expected_location": "https://www.klicktipp.com/bcr/"}], "healhtz_checks": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/healthz.php", "expected_status": 404, "note": "Health check endpoints access disabled at 50-load-balancer AWS ALB level"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/livez.php", "expected_status": 404, "note": "Health check endpoints access disabled at 50-load-balancer AWS ALB level"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/readyz.php", "expected_status": 404, "note": "Health check endpoints access disabled at 50-load-balancer AWS ALB level"}], "url_rewrite_info": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/info/580zz6lwlz3r8wgz1zz3z3", "expected_status": [301, 302], "expected_location": "https://klick.marketron.io/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/info/c2mz3z39vsz5s3f5zz1zzqz3", "expected_status": [301, 302], "expected_location": "https://klick.advertaro.io/", "note": "Bad /info redirect. Internal fail, but fail safe for user"}], "rewrite_urls_to_index_php": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/server-status", "expected_status": [403]}], "redirects_marketing": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/partnerprogramm/joint-promotions", "expected_status": 301, "expected_location": "https://klick-tipp.com/partnerprogramm/lead-cloning"}], "hidden_directories_files": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.well-known/assetlinks.json", "expected_status": 404}], "block_filters": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/handbuch", "expected_status": [301, 302]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/webinar", "expected_status": [301, 302]}], "countdown": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/clock/7nz", "expected_status": [302], "expected_location": "https://klicktipp.s3.amazonaws.com/tools/emailcountdown/default/expired.gif"}], "general_file_caching": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/index.html", "expected_status": 404, "expected_cache_control": "public, max-age=0"}], "php_file_caching": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/index.php", "expected_status": 200, "expected_cache_control": "public, max-age=604800", "follow_redirects": true}], "global_de_redirection": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/somepath", "expected_status": 302, "expected_location": "https://klick-tipp.com/somepath"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/anotherpath/resource", "expected_status": 302, "expected_location": "https://klick-tipp.com/anotherpath/resource"}], "landing_pages": [{"url": "https://pingdomtest.klicktipp.site", "expected_status": [200]}]}
{"redirect_bcr_tests": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr", "expected_status": 301, "expected_location": "https://www.klicktipp-staging.com/bcr/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr/", "expected_status": 301, "expected_location": "https://www.klicktipp-staging.com/bcr/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr/somepath", "expected_status": 301, "expected_location": "https://www.klicktipp-staging.com/bcr/"}], "healhtz_checks": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/healthz", "expected_status": 200, "expected_headers": {"Content-Type": "application/json"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/healthz.php", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/livez.php", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/readyz.php", "expected_status": 404}], "url_rewrite_info": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/info/580zz6lwlz3r8wgz1zz3z3", "expected_status": [301, 302], "expected_location": "https://news.staging-beta.com/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/info/c2mz3z39vsz5s3f5zz1zzqz3", "expected_status": [301, 302], "expected_location": "https://news.staging-beta.com/", "note": "Bad /info redirect. Internal fail, but fail safe for user"}]}
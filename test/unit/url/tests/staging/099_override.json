{"homepage_redirection": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/", "expected_status": 302, "expected_location": "https://klick-tipp-staging.com/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de", "expected_status": 302, "expected_location": "https://klick-tipp-staging.com/"}], "iphone_icons_redirection": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-114x114.png", "expected_status": 302, "expected_location": "https://klick-tipp-staging.com/misc/img/apple-touch-icon-114x114.png", "expected_cache_control": "max-age=1209600"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-114x114.png", "expected_status": 200, "expected_cache_control": "max-age=1209600", "follow_redirects": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-precomposed.png", "expected_status": 302, "expected_location": "https://klick-tipp-staging.com/misc/img/apple-touch-icon-precomposed.png", "expected_cache_control": "max-age=1209600"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-precomposed.png", "expected_status": 200, "expected_cache_control": "max-age=1209600", "follow_redirects": true}], "redirect_bcr_tests": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr", "expected_status": 301, "expected_location": "https://www.klicktipp-staging.com/bcr/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr/", "expected_status": 301, "expected_location": "https://www.klicktipp-staging.com/bcr/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr/somepath", "expected_status": 301, "expected_location": "https://www.klicktipp-staging.com/bcr/"}], "healhtz_checks": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/healthz.php", "expected_status": 404, "note": "Health check endpoints access disabled at 50-load-balancer AWS ALB level"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/livez.php", "expected_status": 404, "note": "Health check endpoints access disabled at 50-load-balancer AWS ALB level"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/readyz.php", "expected_status": 404, "note": "Health check endpoints access disabled at 50-load-balancer AWS ALB level"}], "url_rewrite_info": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/info/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b", "expected_status": [301, 302], "expected_location": "https://news.staging-beta.com/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/info/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b", "expected_status": [200], "follow_redirects": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/pix/43z8ezfz8a77", "expected_status": [200]}], "rewrite_urls_to_index_php": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/server-status", "expected_status": [403]}], "redirects_marketing": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/partnerprogramm/joint-promotions", "expected_status": 301, "expected_location": "https://klick-tipp-staging.com/partnerprogramm/lead-cloning"}], "hidden_directories_files": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.well-known/assetlinks.json", "expected_status": 404}], "block_filters": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/handbuch", "expected_status": [301, 302]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/webinar", "expected_status": [301, 302]}], "global_de_redirection": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/somepath", "expected_status": 302, "expected_location": "https://klick-tipp-staging.com/somepath"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/anotherpath/resource", "expected_status": 302, "expected_location": "https://klick-tipp-staging.com/anotherpath/resource"}], "landing_pages": [{"url": "https://pingdomtest.klicktipp-staging.site", "expected_status": [200]}]}
{"rewrite_contact": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/contact", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/kontakt/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/contact/foo", "expected_status": [404]}], "url_rewrite_confirm": [{"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/confirm/testparam", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/opt_confirm.php?p=testparam"}], "url_rewrite_bestaetigen": [{"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bestaetigen/testparam", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/opt_confirm.php?p=testparam"}], "url_rewrite_images": [{"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/images/testparam", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/track_open.php?p=testparam"}, {"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bilder/testparam", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/track_open.php?p=testparam"}], "url_rewriting_subscribe": [{"method": "GET", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/subscribe.php?FormValue_ListID=1&FormValue_Fields%5BEmailAddress%5D=<EMAIL>", "expected_status": [200, 301, 302]}], "url_rewriting_unsubscribe": [{"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/unsubscribe/testparam", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/unsubscribe.php?p=testparam"}, {"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/abmelden/testparam", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/unsubscribe.php?p=testparam"}], "url_rewriting_web": [{"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/web/testparam", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/web_browser.php?p=testparam"}], "url_rewriting_spam": [{"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/spam/testparam", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/spam_report.php?p=testparam"}]}
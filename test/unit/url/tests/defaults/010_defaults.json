{"protected_files": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.htaccess", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.git", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.env", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.svn", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.DS_Store", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.htpasswd", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.php", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/example.sql", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/example.sql.orig", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/example.sql.save", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/settings.xtmpl.save", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.example", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/Entries.foo", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/Repository", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/Root", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/Tag", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/Template", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.hiddenEntries", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/web.config", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/composer.json", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/composer.lock", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/example.php~", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/example.php.swp", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/example.php.bak", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/example.php.orig", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/example.php.save", "expected_status": 403}], "accessible_files": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/angular/index.html", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/index.html", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/index.php", "expected_status": 301}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/index.php", "expected_status": 200, "follow_redirects": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/install.php", "expected_status": 404}], "healhtz_checks": [{"enabled": false, "note": "Only enabled in nginx", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/healthz", "expected_status": 200, "expected_headers": {"Content-Type": "application/json"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/healthz.php", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/livez.php", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/readyz.php", "expected_status": 200}], "directory_listing_disabled": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/some_directory/", "expected_status": 404, "follow_redirects": true}], "handle_any_404_errors": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/non_existent_page", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/another_missing_page", "expected_status": 404}], "handle_5xx_errors": [{"enabled": false, "note": "Only enabled in nginx/openresty", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/test_50x", "expected_status": 500}], "favicon": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/favicon.ico", "expected_status": 200}], "general_file_caching": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/css/main.css", "expected_status": 200, "expected_cache_control": null}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/Chart.bundle.js", "expected_status": 200, "expected_cache_control": null}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/index.html", "expected_status": 404, "expected_cache_control": "no-cache, must-revalidate"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/angular/index.html", "expected_status": 200, "expected_cache_control": "max-age=0, no-cache, no-store, must-revalidate"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/files/favicon.svg", "expected_status": 200, "expected_cache_control": "max-age=1209600"}], "php_file_caching": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/index.php", "expected_status": 200, "follow_redirects": true}], "hidden_directories_files": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.hidden_directory/", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.git/", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.svn/", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.hidden_file", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/.well-known/assetlinks.json", "expected_status": 200}], "block_filters": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/admin", "expected_status": [301, 302]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/admin", "expected_status": 200, "follow_redirects": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/app", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build", "expected_status": [301, 302]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/handbuch", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/content_includes", "expected_status": [301]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/nutzungsbedingungen", "expected_status": [404]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/webinar", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/docker/klicktipp/backend/openresty/Dockerfile", "expected_status": 404}], "block_access_plugin_includes": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/plugin_includes/schema.json", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/plugin_includes/digistore_create_url.inc", "expected_status": 403}], "block_access_backend_dirs": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/backend", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/backend/assets/app.js", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/backend/config/routes.yaml", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/backend/public/index.php", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/backend/vendor/composer/ClassLoader.php", "expected_status": 404}], "block_access_scripts_dirs": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/scripts", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/scripts/send-test-email.sh", "expected_status": 403}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/scripts/send-test-email.php", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/scripts/docker/README.md", "expected_status": [403, 404]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/scripts/whoami.php", "expected_status": 404}], "block_access_test_dirs": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/test/e2e/__init__.py", "expected_status": [301, 403, 404], "headers": {"Host": "disallowed.example.com"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/test/e2e/__init__.py", "expected_status": [301, 403, 404]}], "block_access_config_dirs": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/config", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/config/klicktipp.yaml", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/config/secrets/klicktipp.prod.yaml", "expected_status": 404}], "iphone_icons_redirection": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-114x114.png", "expected_status": 302, "expected_location": "/misc/img/apple-touch-icon-114x114.png", "expected_cache_control": "max-age=1209600"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-114x114.png", "expected_status": 200, "expected_cache_control": "max-age=1209600", "follow_redirects": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-precomposed.png", "expected_status": 302, "expected_location": "/misc/img/apple-touch-icon-precomposed.png", "expected_cache_control": "max-age=1209600"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-touch-icon-precomposed.png", "expected_status": 200, "expected_cache_control": "max-age=1209600", "follow_redirects": true}], "redirect_old_chicklets": [{"enabled": false, "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/user/website_integration_chicklet.php", "expected_status": 302, "expected_location": "/chicklet.php"}], "rewrite_post_requests": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/form.php", "method": "POST", "expected_status": [200, 405]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/subscribe.php", "method": "POST", "expected_status": 302, "comment": "redirect to error page due to missing parameters"}], "homepage_redirection": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/", "expected_status": 302, "expected_location": "/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de", "expected_status": 302, "expected_location": "/"}], "global_de_redirection": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/somepath", "expected_status": 302, "expected_location": "/somepath"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/de/anotherpath/resource", "expected_status": 302, "expected_location": "/anotherpath/resource"}], "prevent_google_indexing": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/robots.txt", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/form.php", "expected_status": 200, "expected_headers": {"X-Robots-Tag": "noindex, nofollow"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/opt_confirm.php", "expected_status": 200, "expected_headers": {"X-Robots-Tag": "noindex, nofollow"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/track_open.php", "expected_status": 200, "expected_headers": {"X-Robots-Tag": "noindex, nofollow"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/unsubscribe.php", "expected_status": 200, "expected_headers": {"X-Robots-Tag": "noindex, nofollow"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/web_browser.php", "expected_status": 200, "expected_headers": {"X-Robots-Tag": "noindex, nofollow"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/spam_report.php", "expected_status": 200, "expected_headers": {"X-Robots-Tag": "noindex, nofollow"}}], "redirect_bcr_tests": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr", "expected_status": 301, "expected_location": "https://www.klicktipp.com/bcr/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr/", "expected_status": 301, "expected_location": "https://www.klicktipp.com/bcr/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bcr/somepath", "expected_status": 301, "expected_location": "https://www.klicktipp.com/bcr/"}], "rewrite_apple_app_site_association": [{"note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/apple-app-site-association", "expected_status": 200}], "forward_angular1_traffic": [{"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/somepath", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/angular/index.html"}, {"enabled": false, "note": "FIXME: This rule is broken", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/anotherpath/resource", "expected_status": 200, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/angular/index.html"}], "forward_angular2_traffic": [{"note": "Check the frontend container and angular apps", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/app/somepath", "expected_status": 200}, {"note": "Check the frontend container and angular apps", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/app/anotherpath/resource", "expected_status": 200}], "rewrite_smart_copy_writer": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/ipa/kt-smart-copy-writer", "expected_status": 404}, {"method": "GET", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/ipa/kt-smart-copy-writer/1", "expected_status": [405]}, {"method": "POST", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/ipa/kt-smart-copy-writer/1", "expected_status": [401, 406]}], "rewrite_specific_patterns_to_backend_php": [{"method": "GET", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/ipa/kt-email/sendpreview", "expected_status": [405]}, {"method": "POST", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/ipa/kt-email/sendpreview", "expected_status": [401]}, {"method": "POST", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/ipa/kt-calendar", "expected_status": [401]}, {"method": "GET", "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/ipa/kt-calendar/calendar", "expected_status": [401]}], "prevent_caching_index_html": [{"enabled": false, "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/angular/index.html", "expected_status": 200, "expected_headers": {"Cache-Control": "max-age=0, no-cache, no-store, must-revalidate", "Pragma": "no-cache", "Expires": "Wed, 11 Jan 1984 05:00:00 GMT"}}], "rewrite_urls_to_index_php": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/somepath", "expected_status": [404]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/anotherpath/resource", "expected_status": [404]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/favicon.ico", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/server-status", "expected_status": [200, 501], "note": "Allowed in for RFC 1918 IP addresses. Required for Datadog monitoring"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/fpm-status", "expected_status": 403}], "serve_gzip_compressed_files": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/css/main.css", "headers": {"Accept-Encoding": "gzip"}, "expected_status": 200, "expected_headers": {"Content-Encoding": "gzip", "Content-Type": "text/css"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/build/js/translation.js", "headers": {"Accept-Encoding": "gzip"}, "expected_status": 200, "expected_headers": {"Content-Encoding": "gzip", "Content-Type": "application/javascript"}}], "api": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/", "expected_status": 301}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/", "expected_status": 200, "follow_redirects": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/subscriber/signin.html", "method": "POST", "expected_status": 302, "headers": {"Accept": "application/json"}, "comment": "invalid POST data redirects to error page"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/subscriber/3141592653", "expected_status": 403, "headers": {"Accept": "application/json"}, "comment": "return 403 for invalid subscriber id"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/split/42", "expected_status": 403}], "various_header_fixes": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/somepage", "expected_status": 404, "expected_headers": {"X-Content-Type-Options": "nosniff"}}, {"url": "https://${KLICKTIPP_TEST_URLS_API_DOMAIN}/tag_index", "expected_status": 404}], "directory_security_files": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/files/button.png", "expected_status": 200, "expected_headers": {"Content-Type": "image/png"}, "note": "Ensure basic file access with correct content type"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/files/favicon.svg", "expected_status": 200, "expected_headers": {"Content-Type": "image/svg+xml"}, "note": "Ensure basic file access with correct content type"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/files/b756y8y33271iw6c.testfile.php", "expected_status": 200, "expected_headers": null, "note": "Ensure PHP file is not executed and served as plain text or binary"}, {"enabled": false, "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/files/b756y8y33271iw6c.testfile.php", "expected_status": 200, "expected_headers": {"Content-Type": "application/octet-stream"}, "note": "Ensure PHP file is not executed and served as plain text or binary"}, {"enabled": false, "url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/sites/default/files/b756y8y33271iw6c.testfile.php", "expected_status": 200, "expected_headers": {"Content-Type": "application/octet-stream"}, "note": "Ensure PHP file is not executed and served as plain text or binary"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/sites/default/files/composer/composer.json", "expected_status": 403}], "api_serialized_php_output": [{"debug": false, "url": "https://${KLICKTIPP_TEST_URLS_API_DOMAIN}/account/login", "method": "POST", "headers": {"Accept": "application/vnd.php.serialized"}, "json_data": {"username": "wrong-user", "password": "wrong-password"}, "expected_status": [401], "expected_headers": {"X-Robots-Tag": "noindex, nofollow", "Content-Type": "application/vnd.php.serialized"}, "note": "Ensure API response is served as serialized PHP when Accept header is set"}], "landing_pages": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/landing-page/mysubdomain.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}.site", "method": "GET", "expected_status": [200, 404]}], "zapier_public_api": [{"enabled": false, "url": "https://zapier-api.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}/public/api/v1/accounts/is_authenticated", "method": "POST", "json_data": {"username": "x", "password": "x"}, "expected_status": [401], "expected_headers": {"X-Robots-Tag": "noindex", "Content-Type": "application/json"}}, {"enabled": false, "url": "https://zapier-api.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}/public/api/v1/accounts/is_authenticated", "method": "POST", "json_data": {"username": "${KLICKTIPP_TEST_USERNAME}", "password": "${KLICKTIPP_TEST_PASSWORD}"}, "expected_status": [200], "expected_headers": {"X-Robots-Tag": "noindex, nofollow", "Content-Type": "application/json"}}, {"enabled": false, "url": "https://zapier-api.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}/public/api/v1/accounts/login", "method": "POST", "json_data": {"username": "${KLICKTIPP_TEST_USERNAME}", "password": "${KLICKTIPP_TEST_PASSWORD}"}, "expected_status": [200], "expected_headers": {"X-Robots-Tag": "noindex, nofollow", "Content-Type": "application/json"}}], "e2e_api": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/e2e/api/config.json", "method": "GET", "expected_status": [200], "expected_headers": {"Content-Type": "application/json"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/e2e/api/prepare.json", "method": "POST", "expected_status": [200], "expected_headers": {"Content-Type": "application/json"}}], "internal_keycloak_user_migration_api": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/internal/keycloak-user-migration/1", "method": "GET", "expected_status": [500]}], "redirects_affiliate": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/42", "method": "GET", "expected_status": [301], "expected_location": "https://${KL<PERSON><PERSON>TIPP_TEST_URLS_MARKETING_DOMAIN}/de/?a=42"}], "redirects_marketing": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bestellen/42", "method": "GET", "expected_status": [301], "expected_location": "https://www.klicktipp.com/de/pricing/?a=42"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bestellen/enterprise/42", "method": "GET", "expected_status": [301], "expected_location": "https://www.klicktipp.com/de/pricing/enterprise/?a=42"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/pna", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=4015"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/dawid", "expected_status": 301, "expected_location": "https://www.digistore24.com/redir/202951/klick-tipp/youtube"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/partnerprogramm", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/partnerprogramm/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/partnerprogramm/joint-promotions", "expected_status": 301, "expected_location": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/partnerprogramm/lead-cloning"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/partnerprogramm/42", "expected_status": 301, "expected_location": "https://app.klicktipp.com/crm/goto.php?goto_id=42&goto_url=https://www.klicktipp.com/de/partnerprogramm/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/consultants", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/support/certified-consultants/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/consultants/42", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/support/certified-consultants/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/consultants/abcef42", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/support/certified-consultants/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/certified-consultants/42", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=42"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/email-marketing-formel", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/einstieg-in-einen-mega-markt-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/grundlagen-im-klick-tipp-konto-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/digitalisier<PERSON>-von-visit<PERSON><PERSON><PERSON>-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/termine-automatisieren-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/vertrieb-4-null-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/online-reputation-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/geburtstage-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/Recruiting-4-null-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/recruiting-4-null-kurs", "expected_status": [404]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/fachkraeftemangel-war-gestern-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/kundenonboarding-der-extraklasse-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/event-management-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/leadgenerierung-live-on-stage-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/newsletteranmeldung-40-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/automatisierung-mit-klicktipp-und-zapier-kurs", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/bam"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/pricing", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/pricing/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/kundenstimmen/42", "expected_status": 301, "expected_location": "https://app.klicktipp.com/crm/goto.php?goto_id=42&goto_url=https://www.klicktipp.com/de/erfahrungen/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/webinar/42/<EMAIL>", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=42&email=<EMAIL>"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/webinar/42", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=42"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/webinar-marketing-automatisierung/42/<EMAIL>", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=42&email=<EMAIL>"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/webinar-marketing-automatisierung/42", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=42"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/webinar-marketing-automatisierung/42", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=42"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/academy/42", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=42"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/academy-2020/42", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=42"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/academy-2020/42", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/schulung/?a=42"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/anti-spam/kein-import-.+ber-user-interface", "expected_status": 301, "expected_location": "https://www.klicktipp.com/de/import-policy"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/vielen-dank-756f", "expected_status": 301, "expected_location": "https://www.klicktipp.com/thank-you/"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/vielen-dank-000000", "expected_status": 404}], "change-email": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/change-email", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/change-email/foo", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/change-email/0123456789abcdef0123456789abcdef", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/change-email/0123456789abcdef0123456789abcdef0123456789", "expected_status": 200}], "unsubscribe": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/unsubscribe", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/unsubscribe/foo", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/unsubscribe/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b/cancel", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/unsubscribe/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b/ok", "expected_status": 200}], "abmelden": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/abmelden", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/abmelden/foo", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/abmelden/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b/cancel", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/abmelden/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b/ok", "expected_status": 200}], "spam": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/spam", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/spam/foo", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/spam/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b/cancel", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/spam/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b/ok", "expected_status": 200}], "info": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/info", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/info/foo", "expected_status": 302}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/info/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b", "expected_status": [200, 302]}], "web": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/web", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/web/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b", "expected_status": [200, 302]}], "bilder": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bilder", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bilder/foo", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bilder/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b", "expected_status": 200}], "images": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/images", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/images/foo", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/images/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b", "expected_status": 200}], "my-data": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/my-data", "expected_status": 404}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/my-data/foo", "expected_status": 200}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/my-data/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b", "expected_status": [200, 302]}], "confirm": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/confirm", "expected_status": 404, "comment": "route to /confirm only with encoded parameter"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/confirm/foo", "expected_status": 302, "comment": "invalid encoded parameter, redirect to error page"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/confirm/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b", "expected_status": 302, "comment": "invalid list id, redirect to error page"}], "bestaetigen": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bestaetigen", "expected_status": 404, "comment": "route to /bestaetigen only with encoded parameter"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bestaetigen/foo", "expected_status": 302, "comment": "invalid encoded parameter, redirect to error page"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/bestaetigen/z12rusz1mu1f4z1oo44zzz1txyumpzrz4b6b", "expected_status": 302, "comment": "invalid list id, redirect to error page"}], "conversion_pixel": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/pic/abcdefabcdef/42", "expected_status": [200]}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/pic/abcdefabcdef", "expected_status": [404]}], "oidc_login": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/auth/logout", "expected_status": [302], "expected_location": "^https://auth.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}/realms/.+", "use_regex": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/auth/login", "expected_status": [302], "expected_location": "^https://auth.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}/realms/.+", "use_regex": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/auth/login/callback", "expected_status": [302]}], "error_pages": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/error-pages/public-key", "expected_status": [200]}], "legacy-api": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/legacy-api/subscriber/1", "expected_status": [403, 404]}, {"url": "https://${KLICKTIPP_TEST_URLS_API_DOMAIN}/legacy-api/subscriber", "expected_status": [403, 404]}], "symfony-api-endpoints": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/list/1", "expected_status": [403], "headers": {"Accept": "application/json"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/tag/42", "expected_status": [403], "headers": {"Accept": "application/json"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/field/42", "expected_status": [403, 404], "headers": {"Accept": "application/json"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/group/1", "expected_status": [403, 404], "headers": {"Accept": "application/json"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/bcrevent/1", "expected_status": [403, 404], "headers": {"Accept": "application/json"}}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/pix/43z8ezfz8a77", "expected_status": [200]}], "countdown": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/clock/7nz", "expected_status": [302], "expected_location": "https://assets.klicktipp.com/tools/emailcountdown/default/expired.gif"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/clock/7nz", "expected_status": 200, "follow_redirects": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/infourl/7nz", "expected_status": [302], "expected_location": "^.*/app/error.+", "use_regex": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/infourl/7nz", "expected_status": 200, "follow_redirects": true}], "user": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/user", "expected_status": [302], "expected_location": "^https://auth.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}/realms/.+", "use_regex": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/user/login", "expected_status": [302], "expected_location": "^https://auth.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}/realms/.+", "use_regex": true}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/auth/login", "expected_status": [302], "expected_location": "^https://auth.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}/realms/.+", "use_regex": true}]}
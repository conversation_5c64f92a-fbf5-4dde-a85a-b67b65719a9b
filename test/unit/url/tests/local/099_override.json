{"accessible_files": [{"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/install.php", "expected_status": 200}], "api_serialized_php_output": [{"url": "https://${KLICKTIPP_TEST_URLS_API_DOMAIN}/account/login", "method": "POST", "headers": {"Accept": "application/json", "Origin": "https://${KL<PERSON>KTIPP_TEST_URLS_MARKETING_DOMAIN}", "Access-Control-Request-Method": "POST", "Access-Control-Request-Headers": "Content-Type, Authorization"}, "json_data": {"username": "User1", "password": "klicktipp"}, "expected_status": [200], "expected_headers": {"Content-Type": "application/json"}, "note": "Ensure API response is served as serialized PHP when Accept header is set"}, {"url": "https://${KLICKTIPP_TEST_URLS_APP_DOMAIN}/api/account/login", "method": "POST", "headers": {"Accept": "application/json", "Origin": "https://${KL<PERSON>KTIPP_TEST_URLS_MARKETING_DOMAIN}", "Access-Control-Request-Method": "POST", "Access-Control-Request-Headers": "Content-Type, Authorization"}, "json_data": {"username": "User1", "password": "klicktipp"}, "expected_status": 200, "expected_headers": {"Content-Type": "application/json"}, "note": "Ensure API response is served as serialized PHP when Accept header is set"}]}
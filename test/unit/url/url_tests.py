#!/usr/bin/env python3
# This script runs URL tests against the Klick-Tipp application.
# By default, each test uses a new session (no cookies are shared between requests).
# To use a persistent session with cookies, set 'use_session': true in the test JSON object.

import os
import sys
import time
from venv import logger

import requests
import unittest
import logging
from urllib.parse import urlparse, urlunparse
import json
import glob
import urllib3
import http.client as http_client
from string import Template
import re

def str_to_bool(s):
    return s.lower() == 'true'

logging.basicConfig(level=os.getenv("LOG_LEVEL", "INFO"), format='%(asctime)s - %(levelname)s - %(message)s')

KLICKTIPP_ENVIRONMENT = os.getenv("KLICKTIPP_ENVIRONMENT")
KLICKTIPP_TEST_RATE_LIMIT = float(os.getenv("KLICKTIPP_TEST_RATE_LIMIT", 0) or 0)
KLICKTIPP_TEST_URLS_TLS_VERIFY = str_to_bool(os.getenv("KLICKTIPP_TEST_URLS_TLS_VERIFY", "true"))
KLICKTIPP_TEST_URLS_KTLOCAL_CA_FILE = os.getenv("KLICKTIPP_TEST_URLS_KTLOCAL_CA_FILE", "../../docker/certs/ca/klicktippCA.crt")
KLICKTIPP_TEST_URLS_BASE_DOMAIN = os.getenv("KLICKTIPP_TEST_URLS_BASE_DOMAIN", "klicktipp")
KLICKTIPP_TEST_URLS_APP_DOMAIN = os.getenv("KLICKTIPP_APP_DOMAIN", f"app.{KLICKTIPP_TEST_URLS_BASE_DOMAIN}")
KLICKTIPP_TEST_URLS_API_DOMAIN = os.getenv("KLICKTIPP_API_DOMAIN", f"api.{KLICKTIPP_TEST_URLS_BASE_DOMAIN}")
KLICKTIPP_TEST_URLS_MARKETING_DOMAIN = os.getenv("KLICKTIPP_TEST_URLS_MARKETING_DOMAIN", f"www.{KLICKTIPP_TEST_URLS_BASE_DOMAIN}")
KLICKTIPP_TEST_USERNAME = os.getenv("KLICKTIPP_TEST_USERNAME")
KLICKTIPP_TEST_PASSWORD = os.getenv("KLICKTIPP_TEST_PASSWORD")
try:
    KLICKTIPP_TEST_EXTRA_HEADERS = json.loads(os.getenv("KLICKTIPP_TEST_EXTRA_HEADERS", "{}"))  # Example: "{'X_KLICKTIPP': '42'}"
except json.decoder.JSONDecodeError:
    logging.warning("Error loading JSON from KLICKTIPP_TEST_EXTRA_HEADERS environment variable. Using default '{}'")
    KLICKTIPP_TEST_EXTRA_HEADERS = {}

if KLICKTIPP_ENVIRONMENT is None:
    raise ValueError("KLICKTIPP_ENVIRONMENT environment variable must be set")

# Specify the directories containing JSON files for test configurations
JSON_DIRS = [
    'tests/defaults/*.json',
    f'tests/{str(KLICKTIPP_ENVIRONMENT).lower()}/*.json'
]

default_headers = {
    #'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
    'User-Agent': 'KlickTipp-UnitTestAgent/0.1'
}

# Disable TLS warnings
# ref: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#ssl-warnings
urllib3.disable_warnings()

def get_rate_limit():
    return KLICKTIPP_TEST_RATE_LIMIT and round(1 / KLICKTIPP_TEST_RATE_LIMIT, 3) or 0  # a / b

def template_substitue(template_string):
    def _substitute(_str):
        return Template(_str).substitute(
            KLICKTIPP_TEST_URLS_BASE_DOMAIN=KLICKTIPP_TEST_URLS_BASE_DOMAIN,
            KLICKTIPP_TEST_URLS_APP_DOMAIN=KLICKTIPP_TEST_URLS_APP_DOMAIN,
            KLICKTIPP_TEST_URLS_API_DOMAIN=KLICKTIPP_TEST_URLS_API_DOMAIN,
            KLICKTIPP_TEST_URLS_MARKETING_DOMAIN=KLICKTIPP_TEST_URLS_MARKETING_DOMAIN,
            KLICKTIPP_TEST_USERNAME=KLICKTIPP_TEST_USERNAME,
            KLICKTIPP_TEST_PASSWORD=KLICKTIPP_TEST_PASSWORD,
            )

    if not template_string:
        return template_string
    if isinstance(template_string, str):
        return _substitute(template_string)
    elif isinstance(template_string, dict):
        return {key: _substitute(value) for key, value in template_string.items()}
    else:
        return template_string

class TestURLRules(unittest.TestCase):
    """
    Test class for URL rules.

    By default, each test uses a new session (no cookies are shared between requests).
    To use a persistent session with cookies, set 'use_session': true in the test JSON object.
    """
    session = requests.Session()  # This session is only used when 'use_session' is true

    @staticmethod
    def get_domain(url):
        parsed_url = urlparse(url)
        return parsed_url.netloc  # the domain

    @staticmethod
    def is_url(s):
        parsed = urlparse(s)
        return bool(parsed.scheme and parsed.netloc)

    @staticmethod
    def strip_url_path(url):
        parsed = urlparse(url)
        # Reconstruct URL without the path, parameters, query, and fragment components
        stripped_url = urlunparse((parsed.scheme, parsed.netloc, '', '', '', ''))
        return stripped_url

    @staticmethod
    def fix_location(s, url):
        if TestURLRules.is_url(s):
            return s  # It's a URL or domain, return as is
        else:
            _base_url = TestURLRules.strip_url_path(url)
            # Ensure base_url ends with a slash for proper path appending
            if not _base_url.endswith('/'):
                _base_url += '/'
            return _base_url + s.lstrip('/')  # Append path to base URL, ensuring no double slashes
    @staticmethod
    def set_headers(unit_headers=None):
        headers = default_headers
        headers.update(KLICKTIPP_TEST_EXTRA_HEADERS)
        if unit_headers:
            headers.update(unit_headers)

        return headers

    @staticmethod
    def contains_headers(headers, expected_headers):
        for expected_key, expected_value in expected_headers.items():
            value = headers.get(expected_key)
            if value is not None and  value.lower() != expected_value.lower():
                return False
        return True
def generate_test_method(category, utest):
    """
    Generate a test method for a given test case.

    Parameters:
    - category: The category of the test
    - utest: The test case configuration from the JSON file

    The test case configuration can include the following parameters:
    - enabled: Whether the test is enabled (default: True)
    - url: The URL to test (required)
    - verify: Whether to verify SSL certificates (default: KLICKTIPP_TEST_URLS_TLS_VERIFY)
    - method: The HTTP method to use (default: GET)
    - headers: Additional headers to include in the request
    - follow_redirects: Whether to follow redirects (default: False)
    - data: Form data to include in the request
    - json_data: JSON data to include in the request
    - expected_status: The expected HTTP status code(s)
    - expected_location: The expected location header for redirects
    - expected_cache_control: The expected cache-control header
    - expected_headers: Other expected headers
    - debug: Whether to enable HTTP debugging (default: False)
    - use_regex: Whether to use regex for location matching (default: False)
    - use_session: Whether to use a persistent session with cookies (default: False)
    """
    def test(self):
        enabled = utest.get('enabled', True)
        if not enabled:
            return

        # sleep rate limit: 1/$RATE_LIMT
        time.sleep(get_rate_limit())

        _url = utest.get('url')
        if not _url:
            raise ValueError("'url' is not defined in test case")
        url = template_substitue(_url)

        verify = utest.get('verify', KLICKTIPP_TEST_URLS_TLS_VERIFY)
        method = utest.get('method', 'GET').upper()
        headers = TestURLRules.set_headers(template_substitue(utest.get('headers')))
        follow_redirects = utest.get('follow_redirects', False)
        data = template_substitue(utest.get('data', None))
        json_data = template_substitue(utest.get('json_data', None))
        expected_status = template_substitue(utest['expected_status'])
        expected_location = template_substitue(utest.get('expected_location', None))
        expected_cache_control_headers = template_substitue(utest.get('expected_cache_control', None))
        expected_headers = template_substitue(utest.get('expected_headers', None))
        debug = utest.get('debug', False)
        use_regex = utest.get('use_regex', False)
        use_session = utest.get('use_session', False)

        if not isinstance(expected_status, list):
            expected_status = [expected_status]

        try:
            http_client.HTTPConnection.debuglevel = 1 if debug else 0   # Enable HTTP Debugging

            # Use the class session only if use_session is True, otherwise create a new session for this request
            if use_session:
                session = TestURLRules.session
                logging.debug(f"Using persistent session for URL: {url}")
            else:
                session = requests.Session()
                logging.debug(f"Using new session for URL: {url}")

            response = session.request(method, url, headers=headers, data=data, json=json_data, allow_redirects=follow_redirects, verify=verify)
        except (requests.RequestException, urllib3.exceptions.HTTPError) as e:
            logging.error(f"Connection error for URL: {url} - {str(e)}")
            raise e
        finally:
            # Disable HTTP Debugging for subsequent requests
            http_client.HTTPConnection.debuglevel = 0
            if not use_session:
                session.close()

        self.assertIn(response.status_code, expected_status, f"FAILED: Expected one of {expected_status}, got {response.status_code} for {url}")
        if expected_location:
            _location = response.headers.get('Location')
            if use_regex:  # Use regex match if 'use_regex' is True
                self.assertTrue(re.match(expected_location, _location), f"FAILED: Expected location to match regex pattern {expected_location}, got {_location} for {url}")
            else:
                _expected_location = TestURLRules.fix_location(expected_location, url)
                self.assertEqual(_location, _expected_location, f"FAILED: Expected Location {_expected_location}, got {_location} for {url}")
        if expected_cache_control_headers:
            cache_control_header = response.headers.get('Cache-Control', '')
            _received_headers = [h.strip().lower() for h in cache_control_header.split(',')]
            _expected_headers = [h.strip().lower() for h in expected_cache_control_headers.split(',')]
            for _header in _expected_headers:
                self.assertIn(_header, _received_headers, f"Failed for URL: {url}, expected on of Cache-Control: {_expected_headers}, got: {_header}")
        if expected_headers:

            for expected_header_name, expected_header_value in expected_headers.items():
                _received_headers = [h.strip().lower() for h in response.headers.get(expected_header_name, "").split(',')]
                _expected_headers = [h.strip().lower() for h in expected_header_value.split(',')]
                for _header in _expected_headers:
                    self.assertIn(_header, _received_headers, f"Failed for URL: {url}, expected Headers: {_expected_headers}, got: {_received_headers}")
        session_info = "with persistent session" if use_session else "with new session"
        logging.info(f"PASSED: [{category}] Test for {url} [{method}, expected_status: {expected_status}, redirects: {follow_redirects}, {session_info}]")
    return test

def generate_test_name(category, method, url, expected_status):
    return f"test_{category}_{method}_{url}_->_{expected_status}"

def load_test_cases():
    """
    Loads test cases from all JSON files in specified directories.
    For each filename, extracts the name of the parent directory and appends this string
    to each key (in the first level) of the json loaded data dict.
    """
    test_cases = {}
    for json_dir in JSON_DIRS:
        for filename in glob.glob(json_dir):
            # Extract the parent directory name
            parent_dir_name = os.path.basename(os.path.dirname(filename))

            try:
                with open(filename, 'r') as file:
                    data = json.load(file)
            except json.JSONDecodeError as e:
                # Provide detailed error information to help locate the problem
                logging.error(f"Invalid JSON format in file {filename}: {e}")
                sys.exit(1)

            # Update keys with parent directory name
            updated_data = {key: value for key, value in data.items()}
            test_cases.update(updated_data)
    return test_cases

if __name__ == '__main__':

    logging.info(80*'=')
    logging.info(f"Running tests against KLICKTIPP_ENVIRONMENT '{KLICKTIPP_ENVIRONMENT}' environment at '{KLICKTIPP_TEST_URLS_BASE_DOMAIN}'...")
    logging.info(f"KLICKTIPP_TEST_URLS_BASE_DOMAIN = {KLICKTIPP_TEST_URLS_BASE_DOMAIN}")
    logging.info(f"KLICKTIPP_TEST_URLS_APP_DOMAIN = {KLICKTIPP_TEST_URLS_APP_DOMAIN}")
    logging.info(f"KLICKTIPP_TEST_URLS_API_DOMAIN = {KLICKTIPP_TEST_URLS_API_DOMAIN}")
    logging.info(f"KLICKTIPP_TEST_URLS_MARKETING_DOMAIN = {KLICKTIPP_TEST_URLS_MARKETING_DOMAIN}")
    logging.info(f"KLICKTIPP_TEST_URLS_TLS_VERIFY = {KLICKTIPP_TEST_URLS_TLS_VERIFY}")
    logging.info(f"KLICKTIPP_TEST_EXTRA_HEADERS = {KLICKTIPP_TEST_EXTRA_HEADERS}")
    logging.info(80*'=')

    if KLICKTIPP_ENVIRONMENT.lower() == "local" and KLICKTIPP_TEST_URLS_TLS_VERIFY:
        logging.warning(f"TLS verification is enabled! To disable it, set KLICKTIPP_TEST_URLS_TLS_VERIFY to False")

    test_cases = load_test_cases()

    for category, cases in test_cases.items():
        for utest in cases:

            test_name = generate_test_name(category, utest.get("method", "GET"), utest['url'], utest['expected_status'])
            test_name = template_substitue(test_name)
            test_func = generate_test_method(category, utest)
            setattr(TestURLRules, test_name, test_func)

    unittest.main()

#include .env

# Misc
.DEFAULT_GOAL = help	# if you type 'make' without arguments, this is the default: show the help
.PHONY        : # Not needed here, but you can put your all your targets to be sure
                # there is no name conflict between your files and your targets.

MKFILE_DIR := $(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))

SECRET_FILE ?= ./secrets/test.*.yaml

ifndef KLICKTIPP_ENVIRONMENT
#Throw error if environment not defined
$(error KLICKTIPP_ENVIRONMENT is not set)
else
export KLICKTIPP_ENVIRONMENT
endif

##
##—— Secrets - Get —————————————————————————————————————————————————————————
##

.ONESHELL:
secrets-get: SECRET_FILE = ./secrets/test.$(KLICKTIPP_ENVIRONMENT).yaml
secrets-get:
	@./secrets/get_secrets.sh $(SECRET_FILE)

##
##—— Secrets - Get —————————————————————————————————————————————————————————
##

.ONESHELL:
secrets-envvars: SECRET_FILE = ./secrets/test.$(KLICKTIPP_ENVIRONMENT).yaml
secrets-envvars:
	@./secrets/get_secrets_as_envvars.sh $(SECRET_FILE)

##
##—— Test —————————————————————————————————————————————————————————————————————
##

test: SECRET_FILE = ./secrets/test.$(KLICKTIPP_ENVIRONMENT).yaml
test:
    # execute a command with decrypted SOPS values inserted into the environment
    ifdef CI
		echo "= CI environment detected= "
		sops exec-env $(SECRET_FILE) 'python3 ./url_tests.py'
    else
		sops exec-env $(SECRET_FILE) 'docker compose run --build --remove-orphans --rm urltester'
    endif

##
##—— Test LOCAL Environment ———————————————————————————————————————————————————
##

test-local: export KLICKTIPP_ENVIRONMENT := local
test-local: export KLICKTIPP_TEST_URLS_BASE_DOMAIN := ktlocal.com
test-local: export KLICKTIPP_TEST_URLS_MARKETING_DOMAIN := www.klicktipp.com
test-local: export KLICKTIPP_TEST_URLS_TLS_VERIFY := false
test-local: test

##
##—— Test DEV Environment —————————————————————————————————————————————————
##

test-dev: export KLICKTIPP_ENVIRONMENT := dev
test-dev: export KLICKTIPP_TEST_URLS_BASE_DOMAIN ?= $(error KLICKTIPP_TEST_URLS_BASE_DOMAIN is not set)
test-dev: export KLICKTIPP_TEST_URLS_TLS_VERIFY := false
test-dev: test

##
##—— Test STAGING Environment —————————————————————————————————————————————————
##

test-staging: export KLICKTIPP_ENVIRONMENT := staging
test-staging: export KLICKTIPP_TEST_URLS_BASE_DOMAIN := klicktipp-staging.com
test-staging: test
##
##—— Test PROD Environment ————————————————————————————————————————————————————
##

test-prod: export KLICKTIPP_ENVIRONMENT := prod
test-prod: export KLICKTIPP_TEST_URLS_BASE_DOMAIN := klicktipp.com
test-prod: test

##
##

##
##—— Misc ———————————————————————————————————————————————————————————————————————
##

## Print this help message
.PHONY: help
help:
	@echo "\033[33m \
	..............................................................................\n \
	.........███████╗███████╗ ██████╗██████╗ ███████╗████████╗███████╗............\n \
	.........██╔════╝██╔════╝██╔════╝██╔══██╗██╔════╝╚══██╔══╝██╔════╝............\n \
	.........███████╗█████╗..██║.....██████╔╝█████╗.....██║...███████╗............\n \
	.........╚════██║██╔══╝..██║.... ██╔══██╗██╔══╝.....██║...╚════██║............\n \
	.........███████║███████╗╚██████╗██║..██║███████╗...██║...███████║............\n \
	.........╚══════╝╚══════╝ ╚═════╝╚═╝..╚═╝╚══════╝...╚═╝...╚══════╝............\n \
	..............................................................................\n \
	\033[0m"
	@awk '{ \
			if ($$0 ~ /^.PHONY: [a-zA-Z\-\_\.0-9]+$$/) { \
				helpCommand = substr($$0, index($$0, ":") + 2); \
				if (helpMessage) { \
					printf "\033[36m%-40s\033[0m \t%s\n", \
						helpCommand, helpMessage; \
					helpMessage = ""; \
				} \
			} else if ($$0 ~ /^[a-zA-Z\-\_0-9.]+:/) { \
				helpCommand = substr($$0, 0, index($$0, ":")); \
				if (helpMessage) { \
					printf "\033[36m%-40s\033[0m %s\n", \
						helpCommand, helpMessage; \
					helpMessage = ""; \
				} \
			} else if ($$0 ~ /^##/) { \
				if (helpMessage) { \
					helpMessage = helpMessage"\n                                                  "substr($$0, 3); \
				} else { \
					helpMessage = substr($$0, 3); \
				} \
			} else { \
				if (helpMessage) { \
					printf "\n\033[33m%-80s\033[0m\n", \
          	helpMessage; \
				} \
				helpMessage = ""; \
			} \
		}' \
		$(MAKEFILE_LIST)

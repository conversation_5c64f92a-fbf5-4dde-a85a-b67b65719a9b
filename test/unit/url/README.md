# URL Tester Unit Tests

## Overview
Instructions for setting up and running the URL Tester unit tests in a local environment.

## Prerequisites
- Python 3.x
- Terminal access
- Understanding of environment variables

# Installation / Setup

In this folder run
```bash
python -m venv ./venv
source ./venv/bin/activate
pip3 install -r requirements.txt
```

## Configuration
Set the required environment variables:

### LOCAL Environment

```bash
export KLICKTIPP_ENVIRONMENT=local
export KLICKTIPP_TEST_URLS_TLS_VERIFY=false
export KLICKTIPP_TEST_URLS_BASE_DOMAIN=ktlocal.com
```

### K8s Environment (DEV | STAGING  | PROD)

#### Dev
```bash
export KLICKTIPP_ENVIRONMENT=dev
export KLICKTIPP_NAMESPACE=ad-4242-foo # Replace your_namespace with your value.
export CLUSTER_BASE_DOMAIN=ktsys.cloud # Replace your_cluster_base_domain with your value.
export KLICKTIPP_TEST_URLS_BASE_DOMAIN="${KLICKTIPP_NAMESPACE}.${KLICKTIPP_ENVIRONMENT}.${CLUSTER_BASE_DOMAIN}"
```

#### Prod
```bash
export KLICKTIPP_ENVIRONMENT=prod
export KLICKTIPP_NAMESPACE=prod
export CLUSTER_BASE_DOMAIN=ktsys.cloud
export KLICKTIPP_TEST_URLS_BASE_DOMAIN=klicktipp.com
```

#### Staging
```bash
KLICKTIPP_NAMESPACE=staging
KLICKTIPP_ENVIRONMENT=staging
CLUSTER_BASE_DOMAIN=ktsys.cloud
KLICKTIPP_TEST_URLS_BASE_DOMAIN=klicktipp-staging.com
```

## Running the Tests

```bash
cd test/unit/url
python3 ./url_tests.py
```

## Troubleshooting

Ensure all environment variables are set correctly, you're using Python 3.x, and you're in the test/unit/url directory.

## Test Cases Overview

The `010_defaults.json` file within the `./tests/defaults` directory outlines structured test cases aimed at validating 
various aspects of web application security and functionality across all environments. 
The categories covered include Protected Files, Accessible Files, Directory Listing & 404 Errors, Favicon & File Caching,
and Security & Redirection Rules.

### Example Test Case: Protected Files

Each test case under "protected_files" verifies that accessing a sensitive file (e.g., `.htaccess`) results in an 
HTTP 403 Forbidden status, enhancing the application's security posture.

```json
{
  "url": "https://app.${KLICKTIPP_TEST_URLS_BASE_DOMAIN}/.htaccess",
  "expected_status": 403
}
```

### Structure of the `tests/` Folder

The `tests/` directory is structured to facilitate environment-specific and general testing:

- `./tests/defaults`: Contains test cases applicable across all `KLICKTIPP_ENVIRONMENT` settings.
- `./tests/dev`: Holds tests specific for the development (`KLICKTIPP_ENVIRONMENT=dev`) setup.
- `./tests/local`: Stores tests for the local (`KLICKTIPP_ENVIRONMENT=local`) environment, useful during development and debugging phases.
- `./tests/prod`: Dedicated to production (`KLICKTIPP_ENVIRONMENT=prod`) environment tests, with a focus on security and performance aspects.
- `./tests/staging`: Includes tests tailored for the staging (`KLICKTIPP_ENVIRONMENT=staging`) environment, aimed at pre-production checks.

This organization supports precise testing aligned with the deployment stage, enhancing the testing process's efficiency and relevance.

# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
# Email: <EMAIL>
"""Interface for convertion from csv data to objects."""

import csv


class CSVConverter(object):
    """Convert CSV data into a list of objects, where each obj represents
    the aggreated locust stats at a specific time."""

    class AggregatedStatAtTimeX(object):
        """Represents one aggregated entry from stats history csv.

        CSV Header:

        Timestamp,User Count,Type,Name,Requests/s,Failures/s,
        50%,66%,75%,80%,90%,95%,98%,99%,99.9%,99.99%,100%,
        Total Request Count,Total Failure Count,Total Median Response Time,
        Total Average Response Time,Total Min Response Time,
        Total Max Response Time,Total Average Content Size

        1611049438,50,GET,/info/580zz6lwlz3ji6cz1zz3z3,0.500000,0.000000,490,600,690,700,790,910,970,1200,1200,1200,1200,84,0,490.0,520.8417039404724,246.78911400042125,1184.8011009997208,1256.0
        1611049438,50,GET,/info/580zz6lwlz3ji6dz1zz3z3,0.500000,0.000000,550,680,760,820,900,930,1100,1100,1100,1100,1100,85,0,550.0,563.7220162117742,250.82261500028835,1098.8599590000376,1256.0
        1611049438,50,GET,/info/580zz6lwlz3ji6ez1zz3z3,0.600000,0.000000,490,580,660,720,830,920,980,1000,1000,1000,1000,84,0,490.0,515.602220309542,246.54708900015976,1037.8337210004247,1256.0
        ....
        ....
        1611049438,50,GET,/info/580zz6lwlz3ji6fz1zz3z3,0.600000,0.000000,510,620,720,770,830,950,1200,1300,1300,1300,1300,91,0,510.0,550.7561144286528,246.53819800005294,1342.8845150001507,1256.0
        1611049438,50,GET,/info/580zz6lwlz3ji6gz1zz3z3,0.400000,0.000000,500,580,710,750,930,980,1200,1200,1200,1200,1200,87,0,500.0,525.8212436667089,242.11047900007543,1206.006935000005,1256.0
        1611049438,50,GET,/info/580zz6lwlz3ji6hz1zz3z3,0.500000,0.000000,510,630,700,770,910,1100,1200,1200,1200,1200,1200,87,0,510.0,546.9077146091835,251.78091100042366,1241.3823859997137,1256.0
        1611049438,50,POST,/subscriber/signin,0.000000,0.000000,340,430,510,630,710,840,1200,1200,1200,1200,1200,50,0,340.0,444.81980028002,283.0510769999819,1173.2091339999897,64.0
        1611049438,50,,Aggregated,25.700000,0.000000,490,600,680,730,860,970,1100,1200,1300,1800,1800,4501,0,490.0,528.2413447349479,239.61895999946137,1813.4863029999906,1242.7584981115308

        The object data represents the last aggregated summary line of the timestamp.
        """

        def __init__(self):
            """Init some attributes (class/instance)."""
            self._timestamp = None

        @property
        def timestamp(self):
            """Get timestamp."""
            return self._timestamp

        @timestamp.setter
        def timestamp(self, timestamp):
            """Set timestamp."""
            if type(timestamp is not int):
                timestamp = int(timestamp)
            self._timestamp = timestamp

    def __init__(self):
        """Init some attributes (class/instance)."""
        self._csv_stats_history = None
        self._aggregated_stats_list = []

    @property
    def csv_stats_history(self):
        """Get stats object list with objects from class self.AggregatedStatAtTimeX."""
        return self._aggregated_stats_list

    @csv_stats_history.setter
    def csv_stats_history(self, csv_filepath):
        """Init stats object list with objects from class self.AggregatedStatAtTimeX.

        Pass the locust csv stats history file. e.g.: stg-1ktu-50u-1w-5m_stats_history.csv.
        """
        self._csv_stats_history = csv_filepath
        self._aggregated_stats_list = []
        with open(self._csv_stats_history, newline='') as csv_file:
            reader = csv.reader(csv_file)
            next(reader, None)  # Skip the header.
            # Unpack the row directly in the head of the for loop.
            for csv_line in reader:

                curr_aggr_obj = self.AggregatedStatAtTimeX()

                # Test name for Aggregated column (col 5)
                if csv_line[3].lower() != "aggregated":
                    continue

                curr_aggr_obj.timestamp = int(csv_line[0])
                curr_aggr_obj.user_count = csv_line[1]
                curr_aggr_obj.req_type = csv_line[2]
                curr_aggr_obj.name = csv_line[3]
                curr_aggr_obj.req_s = float(csv_line[4])
                curr_aggr_obj.fails_s = float(csv_line[5])
                curr_aggr_obj.perc50 = csv_line[6]
                curr_aggr_obj.perc66 = csv_line[7]
                curr_aggr_obj.perc75 = csv_line[8]
                curr_aggr_obj.perc80 = csv_line[9]
                curr_aggr_obj.perc90 = csv_line[10]
                curr_aggr_obj.perc95 = csv_line[11]
                curr_aggr_obj.perc98 = csv_line[12]
                curr_aggr_obj.perc99 = csv_line[13]
                curr_aggr_obj.perc99_9 = csv_line[14]
                curr_aggr_obj.perc99_99 = csv_line[15]
                curr_aggr_obj.perc100 = csv_line[16]
                curr_aggr_obj.total_req_cnt = csv_line[17]
                curr_aggr_obj.total_fail_cnt = csv_line[18]
                curr_aggr_obj.total_median_resp_time = csv_line[19]
                curr_aggr_obj.total_avg_resp_time = float(csv_line[20])
                curr_aggr_obj.total_min_resp_time = float(csv_line[21])
                curr_aggr_obj.total_max_resp_time = float(csv_line[22])
                curr_aggr_obj.total_avg_content_size = csv_line[23]

                self._aggregated_stats_list.append(curr_aggr_obj)


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, K<PERSON>-Tipp
# Email: <EMAIL>
"""Interface for convertion from csv data to objects."""

import sys
import os
import csv_converter
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
import numpy as np
import scipy.integrate as integrate
from datetime import datetime
import argparse
import textwrap

parser = argparse.ArgumentParser(
    prog='python3 stats_plotter.py',
    formatter_class=argparse.RawDescriptionHelpFormatter,
    description=textwrap.dedent('''\
    Please provide two mandatory parameters:

    1st : input csv e.g. stg-1ktu-200u-1w-5m_stats_history.csv
    2nd : output jpg where this program should save the plot

    Optionally you can pass the following option:

    --test-against expected-result-stats_history.csv

    The execution duration of the csv with expected values should be the
    same duration as the current test run.

    e.g. Compare graphs with a test duration of 50 minutes.
    '''))

parser.add_argument('--test-against', help='''Give a reference history stats csv. with
                                           \nsame execution duration.''')

parser.add_argument('--csv-stats', help='''Please pass the path to the locust stats history file.
                                        e.g. ./stg-1ktu-50u-1w-5m_stats_history.csv\nand the output jpg file path.''',
                    required=True)
parser.add_argument('--plot-jpg', help='''Output jpg where this program should save the plot.''',
                    required=True)

args = parser.parse_args()


class StatsPlotter(object):
    """Create plot as image from CSV data."""

    def __init__(self, csv_stats_history_file, test_against=None):
        """Init some attributes (class/instance)."""
        self._csv_stats_history_file = csv_stats_history_file
        self._test_against = test_against

        self.min_req_sec = None
        self._max_req_sec = None

        self._max_graph_diff = None

        self._max_resp_average = None
        self._min_resp_average = None

        self._csv_conf_obj = None
        self._output_file = None
        self.csv_conv_obj_exp = None
        self._area_between_curves = None

    @property
    def csv_conv_obj(self):
        """Get csv converter for current result."""
        return self._csv_conf_obj

    @csv_conv_obj.setter
    def csv_conv_obj(self, csv_conf_obj):
        """Set csv converter for current result."""
        self._csv_conf_obj = csv_conf_obj

    def plot(self, output_file):
        """Init some attributes (class/instance)."""
        self._output_file = output_file

        # Current stats
        self.csv_conv_obj = csv_converter.CSVConverter()
        self.csv_conv_obj.csv_stats_history = self._csv_stats_history_file

        stats = self.csv_conv_obj.csv_stats_history

        # Axis for both plots
        x_timeline = []

        # 1st plot
        y_req_s = []
        y_fails_s = []

        # 2nd plot
        y_total_avg_resp_time = []
        y_total_min_resp_time = []
        y_total_max_resp_time = []

        x_timeline_interpol = []
        y_req_s_interpol = []

        i = 0
        for stat in stats:
            time = stat.timestamp
            time = datetime.fromtimestamp(time)

            x_timeline.append(time)
            if i % 20 == 0:
                x_timeline_interpol.append(i)
                y_req_s_interpol.append(stat.req_s)

            y_req_s.append(stat.req_s)
            y_fails_s.append(stat.fails_s)

            y_total_avg_resp_time.append(stat.total_avg_resp_time)
            y_total_min_resp_time.append(stat.total_min_resp_time)
            y_total_max_resp_time.append(stat.total_max_resp_time)

            i += 1

        # Shall we plot the comparison against expected csv stats?
        x_timeline_interpol_exp = []
        y_req_s_interpol_exp = []

        if self._test_against:
            # Expected stats
            self.csv_conv_obj_exp = csv_converter.CSVConverter()
            self.csv_conv_obj_exp.csv_stats_history = self._test_against

            stats_exp = self.csv_conv_obj_exp.csv_stats_history

            i = 0
            for stat in stats_exp:
                if i % 20 == 0:
                    x_timeline_interpol_exp.append(i)
                    y_req_s_interpol_exp.append(stat.req_s)
                i += 1

        # Setting the figure size
        fig = plt.figure(figsize=(10, 10))
        # Rotate/Align x-axis labels
        plt.xticks(rotation=30, ha='right')

        # Plot requests and fails per second
        fig.add_subplot(3, 1, 1)
        plt.plot_date(x_timeline, y_req_s, 'g-', label="Requests/sec")
        plt.plot_date(x_timeline, y_fails_s, 'r-', label="Fails/sec")
        plt.xlabel('Timeline')
        plt.ylabel('Requests')
        plt.title('Requests/Fails per second')
        plt.legend()

        # Plot interpolations with highlighted differencing areas
        fig.add_subplot(3, 1, 2)
        f = interp1d(x_timeline_interpol, y_req_s_interpol, kind='cubic')  # quadratic
        # For Debug - uncomment: See the interpolation points
        # plt.plot(x_timeline_interpol, f(x_timeline_interpol), 'g-', label="Requests/sec")
        el_nr = len(x_timeline_interpol)
        x = np.linspace(0, x_timeline_interpol[-1], num=el_nr * 4, endpoint=True)
        plt.plot(x, f(x), 'g-', label="Requests/sec interp")
        if self._test_against:
            f_exp = interp1d(x_timeline_interpol_exp, y_req_s_interpol_exp, kind='cubic')  # quadratic
            x_exp = np.linspace(0, x_timeline_interpol_exp[-1], num=el_nr * 4, endpoint=True)
            plt.plot(x_exp, f_exp(x_exp), 'r--', label="Requests/sec interp exp")
            plt.fill_between(x, f(x), f_exp(x))
            self._area_between_curves = abs(integrate.quad(f, x[0], x[-1])[0] - integrate.quad(f_exp, x[0], x[-1])[0])
            print("Graph difference : " + str(self._area_between_curves))

        plt.xlabel('Timeline')
        plt.ylabel('Requests')
        plt.title('Requests/Fails per second')
        plt.legend()

        # Plot average, min, max response times
        fig.add_subplot(3, 1, 3)
        plt.plot_date(x_timeline, y_total_avg_resp_time, 'g-', label="AVG resp time (total)")
        plt.plot_date(x_timeline, y_total_min_resp_time, 'b-', label="MIN resp time (total)")
        plt.plot_date(x_timeline, y_total_max_resp_time, 'm-', label="MAX resp time (total)")
        plt.xlabel('Timeline')
        plt.ylabel('Response Time')
        plt.title('Response time stats (total)')
        plt.legend()

        # Saving the plot as an image
        fig.savefig(self._output_file, bbox_inches='tight', dpi=150)


if __name__ == "__main__":
    args = parser.parse_args()
    if not os.path.isfile(args.csv_stats):
        print("File not existing: " + args.csv_stats)
        sys.exit(2)

    output_jpg_file = args.plot_jpg
    try:
        if os.path.isfile(args.plot_jpg):
            print("Delete old jpg plot: " + args.plot_jpg)
            os.remove(args.plot_jpg)
    except:
        print("Old jpg plot could not be deleted: " + args.plot_jpg)
        sys.exit(3)

    print("From that csv file: " + args.csv_stats)
    print("Create this plot  : " + args.plot_jpg)

    plotter = StatsPlotter(csv_stats_history_file=args.csv_stats,
                           test_against=args.test_against)
    plotter.plot(args.plot_jpg)

    print("Done.")

__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

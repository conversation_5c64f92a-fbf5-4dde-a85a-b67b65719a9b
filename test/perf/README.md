# Klicktipp perf tests Package

[Github-flavored Markdown](https://guides.github.com/features/mastering-markdown/)

# Currently, the tests are running for one user on staging

* See dashlane for user `perf-test-dev`

# Test subscribers

Currently, the existing locust test scripts are using the
Klick-Tipp API to sign-in test subscribers via a special API key.
See locust test scripts for details or log in on the test server
with user `perf-test-dev`.

Please ensure that your locust test script is deleting your
test subscribers in teardown functions. For example how to handle
this, take a look at `locust_files/locustfile_tracking_links.py`

# Prepare virtual environment

    cd <klicktipp-src-root>/test
    virtualenv venv -p python3
    . ./venv/bin/activate

# Prepare API test package

    cd <klicktipp-src-root>/test/api
    make install_build_tools
    make build

# Prepare perf test package

    cd <klicktipp-src-root>/test/perf
    make update_required_packages

# Start test for link tracking (.../info/...)

    cd <klicktipp-src-root>/test/perf
    make test-tracking-links-by-conf CONFIG=./configs/stg-1ktu-50u-1w-5m.conf    

# What, if I have not yet any expected values (csv reference file)?

Just run your test as described above, and you will encounter an
exception like below. Just copy the new csv file in the expected folder.

*Error*

    Create this plot  : tracking_links_stg-1ktu-1u-1w-1m.jpg
    Traceback (most recent call last):
    File "/home/<USER>/pr/klick-tipp/test/perf/report/stats_plotter.py", line 203, in <module>
        plotter.plot(args.plot_jpg)
    File "/home/<USER>/pr/klick-tipp/test/perf/report/stats_plotter.py", line 122, in plot
        self.csv_conv_obj_exp.csv_stats_history = self._test_against
    File "/home/<USER>/pr/klick-tipp/test/perf/report/csv_converter.py", line 70, in csv_stats_history
        with open(self._csv_stats_history, newline='') as csv_file:
        FileNotFoundError: [Errno 2] No such file or directory: './expected/tracking_links_stg-1ktu-1u-1w-1m_stats_history.csv'

*Copy Example for newly created csv*

    cd <klicktipp-src-root>/test/perf
    cp stg-1ktu-1u-1w-1m_stats_history.csv ./expected/tracking_links_stg-1ktu-1u-1w-1m_stats_history.csv

# Stats on console

Open `configs/stg-1ktu-50u-1w-5m.conf`

Set `print-stats = true`

Set `only-summary = false`

# Plots, logs and csv files

Under `test/perf`:

    Plot ::./tracking_links_stg-1ktu-50u-1w-5m.jpg
    Log  ::./logs/stg-1ktu-50u-1w-5m.log
    CSVs ::./stg-1ktu-50u-1w-5m*.csv

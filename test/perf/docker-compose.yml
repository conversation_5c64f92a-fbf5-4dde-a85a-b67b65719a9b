version: '3'

services:
  master:
    build: .
    image: kt_locust
#    image: locustio/locust
    ports:
     - "8089:8089"
    volumes:
      - ./:/mnt/locust
    environment:
      - CONFIG=/mnt/locust/configs/signin-stg-1ktu-50u-1w-1m.conf
      # - LOCUST_LOCUSTFILE=/mnt/locust/locust_files/locustfile.py
      # - CONFIG=${CONFIG}
    command: "--config ${CONFIG} --master"

  worker:
    build: .
    image: kt_locust
#    image: locustio/locust
    volumes:
      - ./:/mnt/locust
    environment:
      - CONFIG=/mnt/locust/configs/signin-stg-1ktu-50u-1w-1m.conf
      # - LOCUST_LOCUSTFILE=/mnt/locust/locust_files/locustfile.py
      # - CONFIG=${CONFIG}
    command: "--config ${CONFIG} --worker --master-host master"

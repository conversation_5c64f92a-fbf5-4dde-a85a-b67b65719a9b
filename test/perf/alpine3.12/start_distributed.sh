docker run --name master \
--hostname master -p 8089:8089 \
-p 5557:5557 -p 5558:5558 \
-v $PWD:/locust -e ATTACKED_HOST=http://master:8089 \
-e LOCUST_MODE=master --rm -d grubykarol/locust:1.2.3-python3.9-alpine3.12

docker run --name worker0 --link master \
--env NO_PROXY=master \
-v $PWD:/locust -e ATTACKED_HOST=http://master:8089 \
-e LOCUST_MODE=worker -e LOCUST_MASTER=master \
-e LOCUST_MASTER_HOST=master --rm -d grubykarol/locust:1.2.3-python3.9-alpine3.12

docker run --name worker1 --link master \
--env NO_PROXY=master \
-v $PWD:/locust -e ATTACKED_HOST=http://master:8089 \
-e LOCUST_MODE=worker -e LOCUST_MASTER=master \
-e LOCUST_MASTER_HOST=master --rm -d grubykarol/locust:1.2.3-python3.9-alpine3.12

# Generation of Kubernetes YAML is still under development!
#
# Save the output of this file and use kubectl create -f to import
# it into Kubernetes.
#
# Created with podman-2.1.1
apiVersion: v1
kind: Pod
metadata:
  creationTimestamp: "2020-11-02T18:01:16Z"
  labels:
    app: master
  name: master
spec:
  containers:
  - env:
    - name: PATH
      value: /usr/local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
    - name: TERM
      value: xterm
    - name: container
      value: podman
    - name: LANG
      value: C.UTF-8
    - name: PYTHON_PIP_VERSION
      value: 20.1.1
    - name: PYTHON_GET_PIP_SHA256
      value: b3153ec0cf7b7bbf9556932aa37e4981c35dc2a2c501d70d91d2795aa532be79
    - name: LOCUST_LOCUSTFILE
      value: /locust/locustfile.py
    - name: containers
      value: podman
    - name: GPG_KEY
      value: E3FF2839C048B25C084DEBE9B26995E310250568
    - name: PYTHON_VERSION
      value: 3.8.3
    - name: PYTHONUNBUFFERED
      value: "1"
    - name: LOCUST_MODE_MASTER
      value: "1"
    - name: PYTHON_GET_PIP_URL
      value: https://github.com/pypa/get-pip/raw/eff16c878c7fd6b688b9b4c4267695cf1a0bf01b/get-pip.py
    - name: HOSTNAME
    image: localhost/k8s_klicktipp_loadtest:latest
    name: master
    ports:
    - containerPort: 8089
      hostPort: 8089
      protocol: TCP
    resources: {}
    securityContext:
      allowPrivilegeEscalation: true
      capabilities: {}
      privileged: false
      readOnlyRootFilesystem: false
      runAsGroup: 1000
      runAsUser: 1000
      seLinuxOptions: {}
    workingDir: /locust
status: {}
---
metadata:
  creationTimestamp: null
spec: {}
status:
  loadBalancer: {}

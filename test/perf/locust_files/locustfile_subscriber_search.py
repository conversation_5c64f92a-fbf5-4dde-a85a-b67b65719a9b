# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, <PERSON><PERSON>-Tip<PERSON>
# Email: <EMAIL>

# from locust import HttpUser, task, between
from locust.contrib.fasthttp import FastHttpUser
from locust import task, between
from helpers import aws_secret_manager as aws_sm
from klicktipp_api import KTReq
import json


class KlicktippUser(FastHttpUser):
    """Klicktipp User logging in and searching a subscriber."""
    wait_time = between(0, 3)

    def __init__(self, *args, **kwargs):
        """Init some instance attributes."""
        super(KlicktippUser, self).__init__(*args, **kwargs)
        self.req = None
        self.session_cookie = None

    def on_start(self):
        """Prepare User."""
        # print("Prepare User.... ")
        user = "perf-test-dev"
        aws_secretmanager = aws_sm.SecretManager()
        pwd = aws_secretmanager.get_secret("perf-test-dev", None)
        self.req = KTReq()

        data = {"username": user,
                "password": pwd}

        self.client.request(path=self.req.endpoint_login,
                            method="POST",
                            data=json.dumps(data),
                            headers=self.req.post_header)
        # print("Prepare Done.")

    def on_stop(self):
        """Teardown User."""
        # print("Teardown User.... ")
        # print("Teardown Done. ")

    @task
    def subscriber_search(self):
        """Subscriber search Task."""
        # print("subscriber_search start")
        data = {"email": "<EMAIL>"}
        self.client.request(path=self.req.endpoint_subscriber_search,
                            method="POST",
                            data=json.dumps(data),
                            headers=self.req.post_header)

        # print("subscriber_search Done")


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

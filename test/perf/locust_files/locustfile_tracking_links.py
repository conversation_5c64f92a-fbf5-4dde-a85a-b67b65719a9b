# FastHTTP Causing a failing redirect!! User the standard requests lib based HTTP user
# from locust.contrib.fasthttp import <PERSON>HttpUser
from locust import HttpUser
from locust import task, between

from klicktipp_api import KTReq
from helpers import aws_secret_manager as aws_sm
import json
from helpers.subscriber import Subscriber

from faker import Faker
import re

fake = Faker()

subscribers = []

# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>

# FastHTTP Causing a failing redirect!! Use the standard requests lib based HTTP user
# class LocustSubscriber(FastHttpUser):


class LocustSubscriber(HttpUser):
    """Klicktipp User logging in and searching a subscriber."""
    wait_time = between(0, 3)
    track_url = None

    def __init__(self, *args, **kwargs):
        """Init some instance attributes."""
        super(LocustSubscriber, self).__init__(*args, **kwargs)
        self.subscriber = Subscriber()

    def on_start(self):
        """Prepare User."""
        rand_int = fake.unique.random_int(min=1000000000, max=100000000000)
        self.subscriber.set_subscriber_mail("randsomrandy19+track_link_" + str(rand_int) + "@gmail.com")
        subscribers.append(self.subscriber)

        ktreq = KTReq()
        aws_secretmanager = aws_sm.SecretManager()
        api_key = aws_secretmanager.get_secret("perf-test-dev-apikey", None)

        data = {"apikey": api_key,
                "email": self.subscriber.subscriber_mail}

        resp = self.client.request(url=ktreq.endpoint_signin,
                                   method="POST",
                                   data=json.dumps(data),
                                   headers=ktreq.post_header)
        subscriber_id = re.match(r'[^z]+z[^z]+z([^z]+)', resp.text).group(1)
        self.subscriber.set_subscriber_id(str(int(subscriber_id, 35)))

        self.track_url = self.subscriber.get_track_link_url(rev_id="6405", camp_id="284116")

    def on_stop(self):
        """Teardown User."""
        ktreq = KTReq()
        user = "perf-test-dev"
        pwd = "FD=rxf}xS9:&"

        ktreq.login(user, pwd)
        ktreq.subscriber_delete(self.subscriber.subscriber_id)

    @task
    def trigger_smart_link(self):
        """Subscriber open tracked/smart link."""

        self.client.request(url=self.track_url,
                            method="GET")


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

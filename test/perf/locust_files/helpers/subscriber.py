# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, <PERSON><PERSON>-Tip<PERSON>
# Email: <EMAIL>
"""Interfaces for interacting with subscriber area."""

import baseconvert


class Subscriber(object):
    """Handle subscriber area actions."""

    subscriber_id = None
    subscriber_mail = None

    def __init__(self):
        """Init some instance/class attributes."""
        return

    def set_subscriber_id(self, id):
        """Set subscribers id."""
        self.subscriber_id = id

    def set_subscriber_mail(self, mail):
        """Set subscribers mail."""
        self.subscriber_mail = mail

    def get_track_link_url(self, rev_id, camp_id):
        """Return "user clicked smart link" url."""
        # https://news.staging-omega.com/info/580zz6lwlz2ke2hz1zz3z3

        rev_id = baseconvert.dec_to_base(int(rev_id), 35).lower()
        lnk_nr = ""
        cmp_id = baseconvert.dec_to_base(int(camp_id), 35).lower()
        sub_id = baseconvert.dec_to_base(int(self.subscriber_id), 35).lower()
        html = "1"
        prev = ""
        url_sig = "3"
        url_hash = "3"

        url_base = "https://news.staging-omega.com/info/"
        url = url_base + rev_id + "z" + lnk_nr + "z" + cmp_id + "z" + sub_id + "z" + html + "z" + prev + "z" + url_sig + "z" + url_hash
        return url

    def get_track_open_url(self, campaign, subscriber, mail):
        """Return "mail opened" tracking pixel URL"""
        return
        # echo "\n\n";
        # $cid = '281042';
        # $sid = '3875856';
        # $eid = '17629';
        # $prev = '';
        # $encr_url_sig = 'j';
        # $encr_url_hsh = '7';
        #
        # $c_cid = base_convert($cid, 10, 35);
        # echo "campaign :".$c_cid."\n";
        # $c_sid = base_convert($sid, 10, 35);
        # echo "subscriber :".$c_sid."\n";
        # $c_eid = base_convert($eid, 10, 35);
        # echo "email :".$c_eid."\n";
        # echo "encr.url.signature :".$encr_url_sig."\n";
        # echo "encr.url.hash :".$encr_url_hsh."\n";
        #
        # echo "https://news.staging-beta.com/images/".$c_cid."z".$c_sid."z".$c_eid."z".$prev."z".$encr_url_sig."z".$encr_url_hsh;

    class Url(object):
        """Subscriber area URL handler class."""

        def __init__(self):
            """Init class/instance attributes."""
            return


__author__ = "Simon Werling"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, Klick-Tipp
# Email: <EMAIL>
"""Interfaces for interacting with test data."""


class TestData(object):
    """Handle test data actions."""

    def __init__(self):
        """Init some instance/class attributes."""
        return


__author__ = "<PERSON>"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "<PERSON>"
__email__ = "<EMAIL>"

# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2022, <PERSON><PERSON>-Tipp
# Email: <EMAIL>
from locust.contrib.fasthttp import FastHttpUser
from locust import task, between


class MyUser(FastHttpUser):
    wait_time = between(0, 3)

    @task
    def index(self):
        self.client.get("/get")


__author__ = "<PERSON>"
__copyright__ = "Copyright 2022, Klick-Tipp"
__maintainer__ = "<PERSON>rling"
__email__ = "<EMAIL>"

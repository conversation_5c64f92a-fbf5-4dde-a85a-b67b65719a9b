# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2025, K<PERSON>-Tipp
# Email: <EMAIL>
"""Setup custom project!"""

from lemoncheesecake.project import Project


class CustomProject(Project):
    def __init__(self, *args, **kwargs):
        import sys
        import pathlib
        api_dir = pathlib.Path(__file__).parent.absolute()
        sys.path.insert(0, str(api_dir))
        # Also add the suites directory to ensure imports work
        suites_dir = api_dir / "suites"
        if suites_dir.exists():
            sys.path.insert(0, str(suites_dir))
        super(CustomProject, self).__init__(*args, **kwargs)


project = CustomProject()


__author__ = "Simon Werling"
__copyright__ = "Copyright 2025, Klick-Tipp"
__maintainer__ = "<PERSON> Werling"
__email__ = "<EMAIL>"

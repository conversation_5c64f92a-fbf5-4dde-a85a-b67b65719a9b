# -*- coding: utf-8 -*-
# Author: <PERSON>
# Copyright: Copyright 2025, K<PERSON>-Tipp
# Email: <EMAIL>

from klicktipp_api import KTReq
import json
from suites.helpers import aws_secret_manager as aws_sm
import lemoncheesecake.api as lcc
from lemoncheesecake import matching as m
import re

from faker import Faker


@lcc.suite(rank=1)  # Run first to establish session
class SuiteSession:
    @lcc.test("Test login end-point")
    def login(self):
        req = KTReq()
        aws_secretmanager = aws_sm.SecretManager()
        user = "perf-test-dev"
        pwd = aws_secretmanager.get_secret("perf-test-dev", None)

        lcc.set_step("Login and check session cookie")
        lcc.log_info("POST " + KTReq.endpoint_login)
        lcc.log_info("POST " + json.dumps(KTReq.post_header))
        lcc.log_info("DATA user::" + user + " pwd::" + pwd)

        resp = req.login(user, pwd)
        m.check_that("HTTP code", resp.status_code, m.is_(200))

        for cookie in resp.cookies:
            if m.check_that("Session cookie", cookie.name, m.match_pattern('^SSESS.+')):
                m.check_that_in(
                    cookie.__dict__,
                    "value", m.not_equal_to(""),
                    "domain", m.equal_to('api.klicktipp-staging.com')
                )
                break


@lcc.suite(rank=10)
class SuiteTags:

    @lcc.depends_on("basic.SuiteSession.login")
    @lcc.test("Test tag creation")
    def tag_create(self):
        req = KTReq()
        fake = Faker()
        rand_int = fake.unique.random_int(min=1000000000, max=100000000000)
        tag_name = "testtag_" + str(rand_int)
        tag_desc = tag_name + " description"

        lcc.set_step("Create a tag with tag description")
        lcc.log_info("POST " + KTReq.endpoint_tag)
        lcc.log_info("POST " + json.dumps(KTReq.post_header))
        lcc.log_info("DATA name::" + tag_name + " text::" + tag_desc)

        resp = req.create_tag(tag_name, tag_desc)
        m.check_that("HTTP code", resp.status_code, m.is_(200))
        # before: '["[123456]"]'
        # now: Got '["[123456]"]'
        m.check_that("Tag id returned", resp.text, m.match_pattern(r'\[\d+\]'))


@lcc.suite(rank=20)
class SuiteSubscriber:
    # Set by via API key registration (Mail Address of subscriber)
    signed_in_subscriber = None
    signed_in_subscriber_id = None

    @lcc.test("Test subscribe via API key")
    def signin(self):
        req = KTReq()
        fake = Faker()
        rand_int = fake.unique.random_int(min=1000000000, max=100000000000)

        api_key = "1pczbbxz8zaf8b"
        email = "randsomrandy19+" + str(rand_int) + "@gmail.com"
        self.signed_in_subscriber = email

        lcc.set_step("Subscribe (single optin) via API key")
        lcc.log_info("POST " + KTReq.endpoint_signin)
        lcc.log_info("POST " + json.dumps(KTReq.post_header))
        lcc.log_info("DATA apikey::" + api_key + " email::" + email)

        resp = req.signin(api_key, email)
        m.check_that("HTTP code", resp.status_code, m.is_(200))
        # before: '["https://news.staging-omega.com/thankyou/bbxz8thz2kds5z1z4b1c"]'
        # now: "[\"https:\\/\\/news.staging-omega.com\\/thankyou\\/bbxz8thz5sda5zzpzaa2b\"]"
        m.check_that("Thankyou page URL returned.", resp.text,
                     m.match_pattern(r'.+?https:.+?(thankyou.+[a-z0-9]+).+$'))

    @lcc.depends_on("basic.SuiteSubscriber.signin")
    @lcc.test("Test subscriber search via Email.")
    def subscriber_search(self):
        req = KTReq()

        lcc.set_step("Endpoint subscriber_search with email address.")
        lcc.log_info("POST " + KTReq.endpoint_subscriber_search)
        lcc.log_info("POST " + json.dumps(KTReq.post_header))
        lcc.log_info("DATA email::" + self.signed_in_subscriber)

        resp = req.subscriber_search(self.signed_in_subscriber)
        m.check_that("HTTP code", resp.status_code, m.is_(200))
        match_id = re.compile(r'\[(\d+)\]')
        m.check_that("Subscriber id returned", resp.text, m.match_pattern(match_id))

        self.signed_in_subscriber_id = match_id.match(resp.text).group(1)

    @lcc.depends_on("basic.SuiteSubscriber.signin")
    @lcc.test("Test subscriber get by ID.")
    def subscriber_get(self):
        req = KTReq()

        lcc.set_step("Get subscriber details by ID.")
        lcc.log_info("GET " + KTReq.endpoint_subscriber_get + "/<id>")
        lcc.log_info("HEADER " + json.dumps(KTReq.post_header))
        lcc.log_info("SUBSCRIBER_ID: " + str(self.signed_in_subscriber_id))

        resp = req.subscriber_get(self.signed_in_subscriber_id)
        m.check_that("HTTP code", resp.status_code, m.is_(200))

        # Parse response and check subscriber data
        try:
            subscriber_data = resp.json()
            lcc.log_info("Subscriber data: " + json.dumps(subscriber_data, indent=2))

            # Check that we got subscriber data back
            m.check_that("Response contains subscriber data", subscriber_data, m.is_not_none())

            # Check that the subscriber ID matches what we searched for
            if isinstance(subscriber_data, dict) and 'id' in subscriber_data:
                m.check_that("Subscriber ID matches", str(subscriber_data['id']), m.equal_to(str(self.signed_in_subscriber_id)))

            # Check that email is present and matches our test subscriber
            if isinstance(subscriber_data, dict) and 'email' in subscriber_data:
                m.check_that("Email is present", subscriber_data['email'], m.equal_to(self.signed_in_subscriber))

        except json.JSONDecodeError:
            lcc.log_error("Response is not valid JSON: " + resp.text)
            m.check_that("Response is valid JSON", False, m.is_(True))

    @lcc.depends_on("basic.SuiteSubscriber.signin")
    @lcc.test("Test subscriber deletion.")
    def subscriber_delete(self):
        req = KTReq()
        lcc.set_step("Endpoint subscriber_delete with subscriber ID.")
        lcc.log_info("DELETE " + KTReq.endpoint_subscriber_delete + "/<id>")
        lcc.log_info("HEADER " + json.dumps(KTReq.post_header))
        resp = req.subscriber_delete(self.signed_in_subscriber_id)
        m.check_that("HTTP code", resp.status_code, m.is_(200))
        m.check_that("Subscriber id returned", resp.text, m.match_pattern(r'true'))


@lcc.suite(rank=1000)  # High rank to ensure it runs last
class SuiteSessionExtended:
    @lcc.depends_on("basic.SuiteSession.login")
    @lcc.test("Test logout end-point")
    def logout(self):
        req = KTReq()

        lcc.set_step("Logout from current session")
        lcc.log_info("POST " + KTReq.endpoint_logout)
        lcc.log_info("HEADER " + json.dumps(KTReq.post_header))

        resp = req.logout()
        m.check_that("HTTP code", resp.status_code, m.is_(200))
        lcc.log_info("Logout response: " + resp.text)


@lcc.suite(rank=30)
class SuiteLists:
    @lcc.depends_on("basic.SuiteSession.login")
    @lcc.test("Test get lists end-point")
    def get_lists(self):
        req = KTReq()

        lcc.set_step("Get available lists")
        lcc.log_info("GET " + KTReq.endpoint_list)
        lcc.log_info("HEADER " + json.dumps(KTReq.post_header))

        resp = req.get_lists()
        m.check_that("HTTP code", resp.status_code, m.is_(200))

        try:
            lists_data = resp.json()
            lcc.log_info("Lists data: " + json.dumps(lists_data, indent=2))
            m.check_that("Response contains lists data", lists_data, m.is_not_none())
        except json.JSONDecodeError:
            lcc.log_info("Response is not JSON: " + resp.text)


@lcc.suite(rank=40)
class SuiteTagsExtended:
    # Store created tag ID for cleanup
    created_tag_id = None

    @lcc.depends_on("basic.SuiteTags.tag_create")
    @lcc.test("Test tag get by ID")
    def tag_get(self):
        req = KTReq()
        fake = Faker()
        rand_int = fake.unique.random_int(min=1000000000, max=100000000000)
        tag_name = "testtag_get_" + str(rand_int)
        tag_desc = tag_name + " description"

        # First create a tag to get
        lcc.set_step("Create a tag for testing get operation")
        create_resp = req.create_tag(tag_name, tag_desc)
        m.check_that("Tag creation HTTP code", create_resp.status_code, m.is_(200))

        # Extract tag ID from response
        tag_id_match = re.search(r'\[(\d+)\]', create_resp.text)
        if tag_id_match:
            self.created_tag_id = tag_id_match.group(1)
            lcc.log_info("Created tag ID: " + self.created_tag_id)

            lcc.set_step("Get tag by ID")
            lcc.log_info("GET " + KTReq.endpoint_tag_get + "/<id>")
            lcc.log_info("TAG_ID: " + self.created_tag_id)

            resp = req.tag_get(self.created_tag_id)
            m.check_that("HTTP code", resp.status_code, m.is_(200))

            try:
                tag_data = resp.json()
                lcc.log_info("Tag data: " + json.dumps(tag_data, indent=2))
                m.check_that("Response contains tag data", tag_data, m.is_not_none())
            except json.JSONDecodeError:
                lcc.log_info("Response is not JSON: " + resp.text)
        else:
            lcc.log_error("Could not extract tag ID from creation response")

    @lcc.depends_on("basic.SuiteTagsExtended.tag_get")
    @lcc.test("Test tag update by ID")
    def tag_update(self):
        if not self.created_tag_id:
            lcc.log_error("No tag ID available for update test")
            return

        req = KTReq()
        fake = Faker()
        rand_int = fake.unique.random_int(min=1000000000, max=100000000000)
        new_tag_name = "updated_tag_" + str(rand_int)

        lcc.set_step("Update tag name")
        lcc.log_info("PUT " + KTReq.endpoint_tag_update + "/<id>")
        lcc.log_info("TAG_ID: " + self.created_tag_id)
        lcc.log_info("NEW_NAME: " + new_tag_name)

        resp = req.tag_update(self.created_tag_id, new_tag_name)
        m.check_that("HTTP code", resp.status_code, m.is_(200))
        lcc.log_info("Update response: " + resp.text)

    @lcc.depends_on("basic.SuiteTagsExtended.tag_update")
    @lcc.test("Test tag delete by ID")
    def tag_delete(self):
        if not self.created_tag_id:
            lcc.log_error("No tag ID available for delete test")
            return

        req = KTReq()

        lcc.set_step("Delete tag by ID")
        lcc.log_info("DELETE " + KTReq.endpoint_tag_delete + "/<id>")
        lcc.log_info("TAG_ID: " + self.created_tag_id)

        resp = req.tag_delete(self.created_tag_id)
        m.check_that("HTTP code", resp.status_code, m.is_(200))
        lcc.log_info("Delete response: " + resp.text)


@lcc.suite(rank=50)
class SuiteSubscriberExtended:
    # Store test data for cross-test usage
    test_subscriber_email = None
    test_tag_id = None

    @lcc.depends_on("basic.SuiteSession.login")
    @lcc.test("Test subscriber tagging and untagging")
    def subscriber_tag_untag(self):
        req = KTReq()
        fake = Faker()
        rand_int = fake.unique.random_int(min=1000000000, max=100000000000)

        # Create test subscriber email
        self.test_subscriber_email = "tagtest+" + str(rand_int) + "@gmail.com"

        # First create a tag for testing
        tag_name = "testtag_tagging_" + str(rand_int)
        tag_desc = tag_name + " description"

        lcc.set_step("Create a tag for tagging tests")
        tag_resp = req.create_tag(tag_name, tag_desc)
        m.check_that("Tag creation HTTP code", tag_resp.status_code, m.is_(200))

        # Extract tag ID
        tag_id_match = re.search(r'\[(\d+)\]', tag_resp.text)
        if tag_id_match:
            self.test_tag_id = tag_id_match.group(1)
            lcc.log_info("Created tag ID: " + self.test_tag_id)

            # Subscribe the test user first
            lcc.set_step("Subscribe test user")
            subscribe_resp = req.subscribe(self.test_subscriber_email, tag_id=self.test_tag_id)
            lcc.log_info("Subscribe response: " + subscribe_resp.text)

            # Tag the subscriber
            lcc.set_step("Tag subscriber with additional tag")
            lcc.log_info("POST " + KTReq.endpoint_subscriber_tag)
            lcc.log_info("EMAIL: " + self.test_subscriber_email)
            lcc.log_info("TAG_IDS: [" + self.test_tag_id + "]")

            tag_subscriber_resp = req.subscriber_tag(self.test_subscriber_email, [self.test_tag_id])
            m.check_that("Tag subscriber HTTP code", tag_subscriber_resp.status_code, m.is_(200))
            lcc.log_info("Tag subscriber response: " + tag_subscriber_resp.text)

            # Untag the subscriber
            lcc.set_step("Remove tag from subscriber")
            lcc.log_info("POST " + KTReq.endpoint_subscriber_untag)
            lcc.log_info("EMAIL: " + self.test_subscriber_email)
            lcc.log_info("TAG_ID: " + self.test_tag_id)

            untag_resp = req.subscriber_untag(self.test_subscriber_email, self.test_tag_id)
            m.check_that("Untag subscriber HTTP code", untag_resp.status_code, m.is_(200))
            lcc.log_info("Untag subscriber response: " + untag_resp.text)
        else:
            lcc.log_error("Could not extract tag ID from creation response")

    @lcc.depends_on("basic.SuiteSubscriberExtended.subscriber_tag_untag")
    @lcc.test("Test get subscribers by tag")
    def subscriber_tagged(self):
        if not self.test_tag_id:
            lcc.log_error("No tag ID available for tagged subscribers test")
            return

        req = KTReq()

        lcc.set_step("Get subscribers with specific tag")
        lcc.log_info("POST " + KTReq.endpoint_subscriber_tagged)
        lcc.log_info("TAG_ID: " + self.test_tag_id)

        resp = req.subscriber_tagged(self.test_tag_id)
        m.check_that("HTTP code", resp.status_code, m.is_(200))

        try:
            tagged_data = resp.json()
            lcc.log_info("Tagged subscribers data: " + json.dumps(tagged_data, indent=2))
            m.check_that("Response contains tagged subscribers data", tagged_data, m.is_not_none())
        except json.JSONDecodeError:
            lcc.log_info("Response is not JSON: " + resp.text)

    @lcc.depends_on("basic.SuiteSubscriberExtended.subscriber_tagged")
    @lcc.test("Test subscriber unsubscribe")
    def subscriber_unsubscribe(self):
        if not self.test_subscriber_email:
            lcc.log_error("No test subscriber email available for unsubscribe test")
            return

        req = KTReq()

        lcc.set_step("Unsubscribe test subscriber")
        lcc.log_info("POST " + KTReq.endpoint_unsubscribe)
        lcc.log_info("EMAIL: " + self.test_subscriber_email)

        resp = req.unsubscribe(self.test_subscriber_email)
        # Accept both 200 (success) and 406 (already unsubscribed or not found)
        m.check_that("HTTP code", resp.status_code, m.any_of(m.is_(200), m.is_(406)))
        lcc.log_info("Unsubscribe response: " + resp.text)
        if resp.status_code == 406:
            lcc.log_info("Subscriber might already be unsubscribed or not found - this is acceptable")

    @lcc.test("Test signout via API key")
    def signout_test(self):
        req = KTReq()
        fake = Faker()
        rand_int = fake.unique.random_int(min=1000000000, max=100000000000)

        api_key = "1pczbbxz8zaf8b"
        email = "signouttest+" + str(rand_int) + "@gmail.com"

        # First sign in the user
        lcc.set_step("Sign in user via API key")
        signin_resp = req.signin(api_key, email)
        lcc.log_info("Signin response: " + signin_resp.text)

        # Only proceed with signout if signin was successful
        if signin_resp.status_code == 200:
            # Then sign out the user
            lcc.set_step("Sign out user via API key")
            lcc.log_info("POST " + KTReq.endpoint_signout)
            lcc.log_info("API_KEY: " + api_key)
            lcc.log_info("EMAIL: " + email)

            resp = req.signout(api_key, email)
            # Accept both 200 (success) and 406 (already signed out or not found)
            m.check_that("HTTP code", resp.status_code, m.any_of(m.is_(200), m.is_(406)))
            lcc.log_info("Signout response: " + resp.text)
            if resp.status_code == 406:
                lcc.log_info("User might already be signed out or not found - this is acceptable")
        else:
            lcc.log_error("Signin failed, skipping signout test")
            lcc.log_info("Signin failed with status: " + str(signin_resp.status_code))


__author__ = "Simon Werling"
__copyright__ = "Copyright 2025, Klick-Tipp"
__maintainer__ = "Simon Werling"
__email__ = "<EMAIL>"

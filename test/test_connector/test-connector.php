#!/usr/bin/php
<?php

// Version 1.4

// turn errors into exceptions ...
set_error_handler(
  function ($errno, $errstr, $errfile, $errline) {
    throw new ErrorException($errstr, $errno, 0, $errfile, $errline);
  }
);

function klicktipp_include($file, $path) {
}

define('MINIHASHSECRET', '1LNBz7McjZlG9hTO3YOsJVCUikk5');

function SimplehashCreate($param1, $param2 = 'NA') {
  return substr(sha1($param1 . MINIHASHSECRET . $param2), 12, 4);
}

global $_SERVER;
$_SERVER['SERVER_NAME'] = gethostname() . '/';

$apiPath = './'; // 'sites/all/modules/klicktipp/api/';
$connectorTestPath = './';

$doLog = FALSE;
$rounds = 10;
// 350ms for stage, otherwise sometimes 429 "Too many requests"
$millisToSleep = 0;

$opts = getopt('s:u:p:t:l:r:h:w:a:k:q:i:x:c:n:');
$server = '';
if (isset($opts['s'])) {
  $server = $opts['s'];
}
$drupalUser = '';
if (isset($opts['u'])) {
  $drupalUser = $opts['u'];
}
$drupalPassword = '';
if (isset($opts['p'])) {
  $drupalPassword = $opts['p'];
}
$testId = 'login';
if (isset($opts['t'])) {
  $testId = $opts['t'];
}
$doLog = isset($opts['l']);
if (isset($opts['r']) && is_numeric($opts['r'])) {
  $rounds = intval($opts['r']);
}
if (isset($opts['w']) && is_numeric($opts['w'])) {
  $millisToSleep = intval($opts['w']);
}
$showHelp = isset($opts['h']) || count($opts) === 1 || empty($server)
  || empty($drupalUser);
$amount = -1;
if (isset($opts['a'])) {
  $match = preg_match('/^([0-9]+)([km]?)$/', $opts['a'], $out);
  $number = intval($out[1]);
  $factor = 1;
  if (count($out) >= 3) {
    $unit = $out[2];
    if ($unit === 'k') {
      $factor = $factor * 1024;
    }
    else {
      if ($unit === 'm') {
        $factor = $factor * 1024 * 1024;
      }
    }
  }
  $amount = $number * $factor;
}
$customerKey = "";
if (isset($opts['k'])) {
  $customerKey = $opts['k'];
}
$randomQueryParam = FALSE;
if (isset($opts['q'])) {
  $randomQueryParam = TRUE;
}
$singleOptInListId = 0;
if (isset($opts['i']) && is_numeric($opts['i'])) {
  $singleOptInListId = intval($opts['i']);
}
$apiKey = "";
if (isset($opts['x'])) {
  $apiKey = $opts['x'];
}
$keepConnectionAlive = isset($opts['c']);
$newsLetterId = 0;
if (isset($opts['n'])) {
  $newsLetterId = intval($opts['n']);
}

if ($showHelp) {
  echo "Usage: ./test-connector.php -s <service url>";
  echo " -u <drupal user>";
  echo " -p <drupal password>";
  echo " [-l 1]";
  echo " [-t <test>]";
  echo " [-r <number of rounds>]";
  echo " [-w <millis to sleep after http request>]";
  echo " [-a <number>(k|m)]\n";
  echo " [-k <customer key>\n";
  echo " [-i <number>\n";
  echo " [-n <number>\n";
  echo " [-x <key>\n";
  echo " [-q 1\n";
  echo " [-h]\n";
  echo " [-c 1]\n";
  echo "\n";
  echo " -s is drupal's home url\n";
  echo " -u is a drupal's username for login\n";
  echo " -p is a drupal's password for login\n";
  echo " -l enables logging of http responses (default: off)\n";
  echo " -t runs a specific test (default: login and logout); possible values: fast-get, fastest-get, post-put-delete, whole-connector, field-index\n";
  echo " -r sets the number of rounds to repeat a specific test (default: $rounds)\n";
  echo " -w set the milliseconds to wait after a request (default: $millisToSleep)\n";
  echo " -h or no arguments prints this help\n";
  echo " -c keep connection alive (for -r)\n";
  echo " -a amount of text in KB or MB to POST\n";
  echo " -k if this option is set the KlicktippPartnerConnector is used for login otherwise the session based KlicktippConnector)\n";
  echo " -q enables random http query request parameter (default: off)\n";
  echo " -i id of a database entry of type subscription process which is set to single opt-in\n";
  echo " -x api key string for a single opt-in subscription process (use value of option -i when creating the key in drupal and assign a tag) \n";
  echo " -n id of a newsletter\n";
  echo "\n";
  echo "Example: ./test-connector.php -s https://staging.zauberlist.com/api -u testuser01n -p 'abc$123' -t fast-get -r 20\n";
  echo "\n";
  echo "Regarding the test:\n";
  echo "- fast-get: Doesn't support -a\n";
  echo "  final output format:\n";
  echo "  Example: 'Fast GET test ends (218.25ms per request, 2.54 requests per second) with following count of http status codes: Array([200] => 17 [429] => 3)'\n";
  echo "  Each key is an http status code and the value is the count. In the example 429 'Too many requests' occurred three times.\n";
  echo "  Note that the request per second calculation considers the wait time (-w) too.\n";
  echo "  Logging:\n";
  echo "  Please note that syslog is used for logging. If -l is omitted, a progress is logged every 10% of processing status.\n";
  echo "\n";
  echo "- post-put-delete doesn't support: -l, -w, but optionally -a.\n";
  echo "\n";
  echo "- whole-connector: doesn't support: -l, -w, but requires -i (otherwise the checks of subscribe(), "
    . "unsubscribe() fail), -x (otherwise signin/singout/signoff fail) and -n (otherwise resend fails). "
    . "The resend() check expects a user who has the permission 'klicktipp api reset autoresponder'. "
    . "The log output shouldn't contain the word 'ERROR', because otherwise one test failed.\n";
  echo "\n";
}
else {

  class DrupalWebTestCase {

  }

  require_once $connectorTestPath . 'klicktipp_api_connector.test';

  // Note: self signed https certificates are not supported!
  require_once 'TestConnectorSuite.php';


  class ExternalTestCase extends klicktippAPIConnectorTestCase {

    public function assertTrue($Condition, $Message) {
      $ok = $Condition === TRUE;
      $prio = (!$ok) ? LOG_ERR : LOG_INFO;
      $prefix = $ok ? "INFO" : "ERROR";
      syslog($prio, $prefix . ' ' . $Message);
    }

    public function assertEqual($v1, $v2, $Message) {
      $this->assertTrue($v1 == $v2, $Message);
    }

    public function assertFalse($Condition, $Message) {
      $this->assertTrue(!$Condition, $Message);
    }

  }

  $suite = new TestConnectorSuite(
    $apiPath, $server, $drupalUser, $drupalPassword, NULL, $millisToSleep,
    $customerKey, $randomQueryParam, $keepConnectionAlive
  );

  if ($suite->isLoginOk()) {
    switch ($testId) {
      case 'fast-get':
        $suite->runTestFastGET($doLog, $rounds);
        break;
      case 'post-put-delete':
        $suite->runPostPutDelete($amount, $rounds);
        break;
      case 'fastest-get':
        $suite->runTestFastestGET($rounds);
        break;
      case 'whole-connector':
        $suite->runTestAllConnectorMethods(
          new ExternalTestCase(), $singleOptInListId, $apiKey, $newsLetterId
        );
        break;
      case 'field-index':
        $suite->runTestFieldIndex($doLog, $rounds);
        break;
      default:
        echo "Unknown test: '${testId}'\n";
    }
  }

  $suite->cleanup();
}
?>

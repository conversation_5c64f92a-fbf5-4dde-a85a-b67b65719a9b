<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
</head>
<body>
<p>Copy this file to your drupal installation in /misc/test-connector.html</p>
<p>Point your browser to &lt;your drupal server&gt/misc/test-connector.html</p>
<script>
  console.log("api list signatures");

  var startTime = new Date().getTime();
  var url = '/api/kt-signature';
  var totalTime = 0;
  var rounds = 10; // most browsers queue more than 6 requests to the same domain!
  var promiseList = [];
  var fails = 0;
  var statusCount = {};

  for (var i = 0; i < rounds; i++) {
    promiseList.push(
      fetch(url, {
        method: "GET",
        mode: "cors", // no-cors, cors, *same-origin
        cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
        credentials: "include",
        headers: new Headers({
          "Accept": "application/json",
          "Content-Type": "application/json",
          "X-IPA": "g3bedd6g5c071g",
        }),
        redirect: "follow",
        referrer: "no-referrer", // no-referrer, *client
        // body: JSON.stringify(data), // body data type must match "Content-Type" header
      })
        .then(response => {
          statusCount[response.status] = (statusCount[response.status] || 0) + 1;
          return response.json();
        })
        .then(data => {
          var duration = new Date().getTime() - startTime;
          totalTime += duration;
          // console.log("OK fetch in ", duration, "ms");
        })
        .catch(() => {
          var duration = new Date().getTime() - startTime;
          totalTime += duration;
          // console.log("FAIL fetch in ", duration, "ms");
          fails++;
        })
    );
  }

  function addMessage(message) {
    console.log(message);
    var div = document.createElement("div");
    div.innerHTML = "<b>" + message + "</b>";
    document.body.appendChild(div);
  }

  function showResult() {
    var millisPerRequest = totalTime / rounds;
    var requestsPerSecond = 1000 / millisPerRequest;
    var message = "Test done in " + totalTime + "ms, " + millisPerRequest.toFixed(2)
      + "ms per request, " + requestsPerSecond.toFixed(2) + " requests per second, failures: " + fails + " of " + rounds;
    addMessage(message);
    addMessage("HTTP Status Count: " + JSON.stringify(statusCount))
  }

  Promise.all(promiseList)
    .then(() => showResult())
    .catch(() => showResult());
</script>

</body>
</html>

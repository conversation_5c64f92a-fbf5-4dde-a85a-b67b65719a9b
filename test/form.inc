<?php

define('TEST_USERS_COUNT', 30);
define('TEST_USERNAME_PATTERN', 'testuser%02dn');
define('TEST_SUBSCRIBERS_COUNT', 20000);
define('TEST_SUBSCRIBERS_SLICE', 1000);

function test_bootstrap() {

  /* drupal bootstrap */

  // bootstrap as if we were in drupal root
  // we are in "<docroot>/ckfinder/core/connector/php"
  // chdir to "<docroot>"
  $olddir = getcwd();
  chdir('../');

  // drupal bootstrap, see index.php
  ini_set('display_errors', 0);
  define('DRUPAL_ROOT', getcwd());
  require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
  drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);
  ini_set('display_errors', 1);


  // ?? beanstalkd_load_pheanstalk();

  // get back to calling environment
  //chdir($olddir);

}

function test_show_form($page_title, $rendered_form) {
  klicktipp_set_title($page_title);
  print theme('klicktipp_whitelabel', array(
    'message' => $page_title,
    'content' => $rendered_form
  ));
}
{"_meta": {"hash": {"sha256": "9dcbcc2178dfcc229a91b03631708e41759406b57c26188a6b0cebab7a657b70"}, "pipfile-spec": 6, "requires": {"python_version": "3.10"}, "sources": [{"name": "pypi", "url": "https://pypi.org/simple", "verify_ssl": true}]}, "default": {"anyio": {"hashes": ["sha256:25ea0d673ae30af41a0c442f81cf3b38c7e79fdc7b60335a4c14e05eb0947421", "sha256:fbbe32bd270d2a2ef3ed1c5d45041250284e31fc0a4df4a5a6071842051a51e3"], "markers": "python_full_version >= '3.6.2'", "version": "==3.6.2"}, "async-generator": {"hashes": ["sha256:01c7bf666359b4967d2cda0000cc2e4af16a0ae098cbffcb8472fb9e8ad6585b", "sha256:6ebb3d106c12920aaae42ccb6f787ef5eefdcdd166ea3d628fa8476abe712144"], "markers": "python_version >= '3.5'", "version": "==1.10"}, "attrs": {"hashes": ["sha256:29adc2665447e5191d0e7c568fde78b21f9672d344281d0c6e1ab085429b22b6", "sha256:86efa402f67bf2df34f51a335487cf46b1ec130d02b8d39fd248abfd30da551c"], "markers": "python_version >= '3.5'", "version": "==22.1.0"}, "certifi": {"hashes": ["sha256:0d9c601124e5a6ba9712dbc60d9c53c21e34f5f641fe83002317394311bdce14", "sha256:90c1a32f1d68f940488354e36370f6cca89f0f106db09518524c88d6ed83f382"], "markers": "python_version >= '3.6'", "version": "==2022.9.24"}, "charset-normalizer": {"hashes": ["sha256:5a3d016c7c547f69d6f81fb0db9449ce888b418b5b9952cc5e6e66843e9dd845", "sha256:83e9a75d1911279afd89352c68b45348559d1fc0506b054b346651b5e7fee29f"], "markers": "python_full_version >= '3.6.0'", "version": "==2.1.1"}, "exceptiongroup": {"hashes": ["sha256:2e3c3fc1538a094aab74fad52d6c33fc94de3dfee3ee01f187c0e0c72aec5337", "sha256:9086a4a21ef9b31c72181c77c040a074ba0889ee56a7b289ff0afb0d97655f96"], "markers": "python_version < '3.11'", "version": "==1.0.0rc9"}, "h11": {"hashes": ["sha256:36a3cb8c0a032f56e2da7084577878a035d3b61d104230d4bd49c0c6b555a9c6", "sha256:47222cb6067e4a307d535814917cd98fd0a57b6788ce715755fa2b6c28b56042"], "markers": "python_version >= '3.6'", "version": "==0.12.0"}, "httpcore": {"hashes": ["sha256:1105b8b73c025f23ff7c36468e4432226cbb959176eab66864b8e31c4ee27fa6", "sha256:18b68ab86a3ccf3e7dc0f43598eaddcf472b602aba29f9aa6ab85fe2ada3980b"], "markers": "python_version >= '3.7'", "version": "==0.15.0"}, "idna": {"hashes": ["sha256:814f528e8dead7d329833b91c5faa87d60bf71824cd12a7530b5526063d02cb4", "sha256:90b77e79eaa3eba6de819a0c442c0b4ceefc341a7a2ab77d7562bf49f425c5c2"], "markers": "python_version >= '3.5'", "version": "==3.4"}, "iniconfig": {"hashes": ["sha256:011e24c64b7f47f6ebd835bb12a743f2fbe9a26d4cecaa7f53bc4f35ee9da8b3", "sha256:bc3af051d7d14b2ee5ef9969666def0cd1a000e121eaea580d4a313df4b37f32"], "version": "==1.1.1"}, "outcome": {"hashes": ["sha256:6f82bd3de45da303cf1f771ecafa1633750a358436a8bb60e06a1ceb745d2672", "sha256:c4ab89a56575d6d38a05aa16daeaa333109c1f96167aba8901ab18b6b5e0f7f5"], "markers": "python_version >= '3.7'", "version": "==1.2.0"}, "packaging": {"hashes": ["sha256:dd47c42927d89ab911e606518907cc2d3a1f38bbd026385970643f9c5b8ecfeb", "sha256:ef103e05f519cdc783ae24ea4e2e0f508a9c99b2d4969652eed6a2e1ea5bd522"], "markers": "python_version >= '3.6'", "version": "==21.3"}, "pluggy": {"hashes": ["sha256:4224373bacce55f955a878bf9cfa763c1e360858e330072059e10bad68531159", "sha256:74134bbf457f031a36d68416e1509f34bd5ccc019f0bcc952c7b909d06b37bd3"], "markers": "python_version >= '3.6'", "version": "==1.0.0"}, "py": {"hashes": ["sha256:51c75c4126074b472f746a24399ad32f6053d1b34b68d2fa41e558e6f4a98719", "sha256:607c53218732647dff4acdfcd50cb62615cedf612e72d1724fb1a0cc6405b378"], "markers": "python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3, 3.4'", "version": "==1.11.0"}, "pyparsing": {"hashes": ["sha256:2b020ecf7d21b687f219b71ecad3631f644a47f01403fa1d1036b0c6416d70fb", "sha256:5026bae9a10eeaefb61dab2f09052b9f4307d44aee4eda64b309723d8d206bbc"], "markers": "python_full_version >= '3.6.8'", "version": "==3.0.9"}, "pytest": {"hashes": ["sha256:1377bda3466d70b55e3f5cecfa55bb7cfcf219c7964629b967c37cf0bda818b7", "sha256:4f365fec2dff9c1162f834d9f18af1ba13062db0c708bf7b946f8a5c76180c39"], "index": "pypi", "version": "==7.1.3"}, "pytest-parallel": {"hashes": ["sha256:9aac3fc199a168c0a8559b60249d9eb254de7af58c12cee0310b54d4affdbfab", "sha256:9e3703015b0eda52be9e07d2ba3498f09340a56d5c79a39b50f22fc5c38212fe"], "index": "pypi", "version": "==0.1.1"}, "requests": {"hashes": ["sha256:7c5599b102feddaa661c826c56ab4fee28bfd17f5abca1ebbe3e7f19d7c97983", "sha256:8fefa2a1a1365bf5520aac41836fbee479da67864514bdb821f31ce07ce65349"], "index": "pypi", "version": "==2.28.1"}, "rfc3986": {"extras": ["idna2008"], "hashes": ["sha256:270aaf10d87d0d4e095063c65bf3ddbc6ee3d0b226328ce21e036f946e421835", "sha256:a86d6e1f5b1dc238b218b012df0aa79409667bb209e58da56d0b94704e712a97"], "version": "==1.5.0"}, "sniffio": {"hashes": ["sha256:e60305c5e5d314f5389259b7f22aaa33d8f7dee49763119234af3755c55b9101", "sha256:eecefdce1e5bbfb7ad2eeaabf7c1eeb404d7757c379bd1f7e5cce9d8bf425384"], "markers": "python_version >= '3.7'", "version": "==1.3.0"}, "sortedcontainers": {"hashes": ["sha256:25caa5a06cc30b6b83d11423433f65d1f9d76c4c6a0c90e3379eaa43b9bfdb88", "sha256:a163dcaede0f1c021485e957a39245190e74249897e2ae4b2aa38595db237ee0"], "version": "==2.4.0"}, "tblib": {"hashes": ["sha256:059bd77306ea7b419d4f76016aef6d7027cc8a0785579b5aad198803435f882c", "sha256:289fa7359e580950e7d9743eab36b0691f0310fce64dee7d9c31065b8f723e23"], "markers": "python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3, 3.4'", "version": "==1.7.0"}, "tomli": {"hashes": ["sha256:939de3e7a6161af0c887ef91b7d41a53e7c5a1ca976325f429cb46ea9bc30ecc", "sha256:de526c12914f0c550d15924c62d72abc48d6fe7364aa87328337a31007fe8a4f"], "markers": "python_version >= '3.7'", "version": "==2.0.1"}, "urllib3": {"hashes": ["sha256:3fa96cf423e6987997fc326ae8df396db2a8b7c667747d47ddd8ecba91f4a74e", "sha256:b930dd878d5a8afb066a637fbb35144fe7901e3b209d1cd4f524bd0e9deee997"], "markers": "python_version >= '2.7' and python_version not in '3.0, 3.1, 3.2, 3.3, 3.4, 3.5' and python_version < '4'", "version": "==1.26.12"}}, "develop": {}}
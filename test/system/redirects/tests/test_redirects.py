import pytest
import csv
import requests
from requests.adapters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Retry

def retry_session(retries=5):
    session = requests.Session()
    retries = Retry(total=retries,
                backoff_factor=0.5,
                status_forcelist=[500, 503, 502, 504],
                allowed_methods=frozenset(['GET', 'POST', 'HEAD']))

    session.mount('https://', HTTPAdapter(max_retries=retries))
    session.mount('http://', HTTPAdapter(max_retries=retries))

    return session

def check_url(url, allow_redirects=False):
    session = retry_session(retries=5)
    return session.head(
                        url,
                        allow_redirects=allow_redirects,
                        timeout=20)

def generate_test_parameters(csv_file):
    if not csv_file:
        raise ValueError("CSV file not specified")

    test_cases = []
    with open(csv_file, newline='') as f:
        reader = csv.DictReader(f)
        for line in reader:
            req_url = "https://" + line.get("pattern_host") + line.get("pattern_path")
            check_url = line.get("substitution_url")
            http_code = int(line.get("redirect_flag"))
            expected_http_code = int(line.get("expected_http_code"))
            test_case_name = "{}::{}".format(line.get("filename"),req_url)
            test_cases.append(
                pytest.param(
                    { "req_url": req_url,  "check_url": check_url, "http_code": http_code, "expected_http_code": expected_http_code },
                    id=test_case_name)
            )

    return test_cases

@pytest.mark.parametrize(
    "redirect",
    generate_test_parameters("redirect_rules.test.csv"),
)

def test_apache_redirect(redirect):
    req_url = redirect.get("req_url")
    expected_url = redirect.get("check_url")
    expected_http_code = redirect.get("expected_http_code")

    if expected_http_code == -1:
        return True

    if "/build" in req_url:
        return True

    allow_redirects = False
    if "klick-tipp.com" in req_url:
        allow_redirects = True
    elif "klicktipp.com" in req_url:
        allow_redirects = True
    elif "klick-tipp-staging.com" in req_url:
        allow_redirects = True
    elif "klicktipp-staging.com" in req_url:
        allow_redirects = True
    elif "staging.zauberlist.com" in req_url:
        allow_redirects = True
    response = check_url(req_url, allow_redirects)

    history = [_r.url for _r in response.history ]

    if response.next:
        #assert response.status_code == expected_http_code
        history.append(response.next.url)

    history.append(response.url)
    #print(80*'+', expected_url)
    #print(80*'+', response.url)
    #print(80*'+', history)
    assert expected_url in history

#!/usr/bin/env python3

from os.path import dirname, realpath
import os
import sys
import errno
import re
import logging
import csv
from argparse import ArgumentParser
from urllib.parse import urlparse
import itertools
import requests

sys.path.append(dirname(realpath(__file__)))

KLICKTIPP_ENVIRONMENT = os.environ.get("KLICKTIPP_ENVIRONMENT")
KLICKTIPP_APACHE_APP_SERVER_ALIAS_PREFIX = []
KLICKTIPP_APACHE_APP_SERVERS = {
    "prod": [
        "klick-tipp.com",
        "www.klick-tipp.com",
        "app.klicktipp.com",
        "app.klick-tipp.com",
    ],
    "staging": [
        "klick-tipp-staging.com",
        "www.klick-tipp-staging.com",
        "app.klicktipp-staging.com",
        "app.klick-tipp-staging.com",
        "staging.zauberlist.com",
        "www.staging.zauberlist.com"
    ]
}

# Generate KLICKTIPP DOMAINS by combining the app server list and the prefixes
if KLICKTIPP_APACHE_APP_SERVER_ALIAS_PREFIX:
    KLICKTIPP_DOMAINS = [".".join(map(str,x)) for x in
                            list(itertools.product(KLICKTIPP_APACHE_APP_SERVER_ALIAS_PREFIX, KLICKTIPP_APACHE_APP_SERVERS[KLICKTIPP_ENVIRONMENT]))]
else:
    KLICKTIPP_DOMAINS = KLICKTIPP_APACHE_APP_SERVERS[KLICKTIPP_ENVIRONMENT]


def parse_args():
    """Parse commandline arguments"""
    parser = ArgumentParser()
    parser.add_argument("-v", "--verbose", action='store_true', default=False, help="Enable DEBUG logging")
    parser.add_argument("-o", "--output", default=None, help="Set output file. If unset print to /dev/stdout")
    parser.add_argument("-c", "--check-url", default=False, help="Make a HTTP HEAD request for each url and save HTTP code")
    parser.add_argument("-e", "--exclude-files", nargs='+', default=[], help="List of excluded redirect files")

    group = parser.add_mutually_exclusive_group()
    group.add_argument("-p", "--path", default="/etc/apache2/redirects", help="Apache2 redirects path")
    group.add_argument("-f", "--file", help="Apache2 redirect file")

    return parser.parse_args()


def get_redirect_files(redirects_path=None):
    """Iterate recursivelly given path and return list of apache conf files"""
    if not redirects_path:
        raise ValueError("Apache2 redirects path not specified")

    redirects = []
    for subdir, dirs, files in os.walk(redirects_path.rstrip("/")):
        for file in files:
            filepath = subdir + os.sep + file
            if filepath.endswith(".conf"):
                redirects.append(filepath)
                logging.debug("get_redirect_files: {}".format(filepath))

    return sorted(redirects)

def replace_rewrite_condition_domain(rewrite_condition, domain):
    if rewrite_condition:
        if rewrite_condition.startswith("www") and domain.startswith("www"):
            return domain
        elif rewrite_condition.startswith("app") and domain.startswith("app"):
            return domain
    return ""

def get_domain(rewrite_cond_host, netloc, domain):
    if rewrite_cond_host:
        return rewrite_cond_host
    elif netloc:
        return netloc
    elif domain:
        return domain
    raise Exception("This should not happen")

def extend_pattern_path(pattern_path, url):
    paths = []
    if "(/.*)" in pattern_path:
        for suffix in ["", "/foo", "/bar42"]:
            p = pattern_path.replace("(/.*)", suffix)
            u = url.replace("$1", suffix)
            paths.append((p, u))
    elif "(/.+)" in pattern_path:
        for suffix in ["/foo", "/bar42"]:
            p = pattern_path.replace("(/.*)", suffix)
            u = url.replace("$1", suffix)
            paths.append((p, u))
    else:
        paths.append((pattern_path, url))
    return paths

def get_url_http_code(req_url):
    requests_log = logging.getLogger("requests.packages.urllib3")
    requests_log.setLevel(logging.WARN)
    requests_log.propagate = True
    try:
        response = requests.head(req_url, allow_redirects=False, timeout=10)
        return response.status_code
    except Exception as e:
        logging.error("check_url: {}".format(e))
        return -1

def replace_hex_codes(p):
    return p.replace("\\xC3\\xA4","ä").replace("\\x20", " ").replace("\\xC3\\xBC","ü")

def parse_redirect_files(redirect_files, check_url=False):
    pattern_comment = r'^#\s*RewriteRule'
    pattern_rewrite_rule = r'^(?P<rewrite_rule>RewriteRule)\s+(?P<pattern>\S+)\s+(?P<url>.+)\s+\[(.*)(R=(?P<redirect_flag>\d{3}|\w+))(.*)\]\s*$'
    prog = re.compile(pattern_rewrite_rule)

    data = []

    # iterate over all found redirect files,
    # then discard all commented or not interesting lines
    # finally extract with regular expressions the necessary information
    # and build the return object with all data.
    # If we find a RewriteRule we cannot parse, throw an exception to notify the user.
    for filename in redirect_files:
        with open(filename, encoding="utf-8") as f:

            last_rewrite_cond_host = ""
            last_rewrite_cond_uri = ""
            for line in f.readlines():
                line = line.replace("^", "").replace("$ ", " ")   # Remove annoying characters
                if line.startswith("#"):
                    continue
                if "RewriteCond %{HTTP_HOST}" in line:
                    last_rewrite_cond_host = line.split(" ", 2)[2].strip()
                    continue
                if "RewriteCond %{REQUEST_URI}" in line:
                    last_rewrite_cond_uri = line.split(" ", 2)[2].strip()
                    continue
                if not "RewriteRule" in line:
                    continue
                if re.match(pattern_comment, line):
                    continue

                #line = line.replace("RewriteRule (.*)", "RewriteRule https://any.host.com") # Replace wildcard hostname with key hostname
                line = line.replace("%{REQUEST_URI}", "")   # Remove not needed apache params

                result = prog.match(line)
                if result:
                    for domain in KLICKTIPP_DOMAINS:
                        logging.debug(80 * '=')
                        logging.debug("parse_redirect_files: Search successful")
                        logging.debug("parse_redirect_files: last_rewrite_cond_host: {}".format(last_rewrite_cond_host.strip()))
                        logging.debug("parse_redirect_files: last_rewrite_cond_uri:  {}".format(last_rewrite_cond_uri.strip()))
                        logging.debug("parse_redirect_files: line:                   {}".format(line.strip()))
                        logging.debug("parse_redirect_files: pattern:                {}".format(urlparse(result.group("pattern"))))
                        logging.debug("parse_redirect_files: url:                    {}".format(urlparse(result.group("url"))))
                        logging.debug("parse_redirect_files: redirect_flag:          {}".format(result.group("redirect_flag")))
                        logging.debug("parse_redirect_files: domain:                 {}".format(domain))

                        # Get URL and domain components
                        pattern = urlparse(result.group("pattern"))

                        # If we have 'RewriteRule (.*) ', then set empty host
                        pattern_path = "" if pattern.path == "(.*)" else pattern.path

                        # Set the domain for the tests based on rewrite_conditons
                        new_host = replace_rewrite_condition_domain(last_rewrite_cond_host, domain)
                        # If we found a matching host, then continue, otherwise the RewriteCond was set and we need to keep up
                        if not new_host:
                            continue

                        # For each pattern_path with wildcards (.+), (/.*), generate corresponding test cases
                        for _path,url in extend_pattern_path(pattern_path, result.group("url")):

                            _host = get_domain(new_host, pattern.netloc, domain)

                            if check_url:
                                req_url = "https://" + _host + _path
                                expected_http_code = get_url_http_code(req_url)
                            else:
                                expected_http_code = 0
                            logging.debug("parse_redirect_files: check_url http code:    {}".format(expected_http_code))

                            _path = replace_hex_codes(_path)

                            data.append({
                                "pattern_host": _host,
                                "pattern_path": _path,
                                "substitution_url": url,
                                "redirect_flag": result.group("redirect_flag").replace("permanent", "301"),
                                "expected_http_code": expected_http_code,
                                "filename": (os.path.basename(filename)),
                            })

                else:
                    logging.debug("parse_redirect_files: Search unsuccessful.")
                    logging.debug("parse_redirect_files: line:          {}".format(line.strip()))

                    raise Exception("Unable to parse file: {}, line: '{}'".format(filename, line))

                logging.debug("parse_redirect_files: result: {}".format(result))

    return data

def write_to_csv_file(csv_file, rows):
    _fieldnames = ["pattern_host", "pattern_path", "substitution_url", "redirect_flag", "expected_http_code", "filename"]

    from contextlib import nullcontext
    with open(csv_file, "w", encoding="utf-8", newline="") if csv_file else nullcontext(sys.stdout) as f:
        writer = csv.DictWriter(f, fieldnames=_fieldnames)
        writer.writeheader()
        writer.writerows(rows)

def print_to_stdout(*a):

    # Here a is the array holding the objects
    # passed as the argument of the function
    print(*a, file=sys.stdout)

args = parse_args()

LOG_LEVEL = logging.DEBUG if (args.verbose) else logging.INFO

# Setup logging
logging.basicConfig(
    level=LOG_LEVEL,
    format="%(asctime)s  [%(levelname)s] %(message)s",
    datefmt="%d.%m.%Y %H:%M:%S")

redirect_files = []
if args.file:
    redirect_files.append(args.file)
elif args.path:
    redirect_files = get_redirect_files(args.path)
else:
    raise ValueError("Path or Files not specified!")

if not redirect_files:
    raise ValueError("Error opening redirect files")

files = []
exclude_files = args.exclude_files

if exclude_files:
    for ef in exclude_files:
        for rf in redirect_files:
            if not ef in rf:
                files.append(rf)
else:
    files = redirect_files

logging.debug("files: {}".format(files))
logging.debug("redirect_files: {}".format(redirect_files))
logging.debug("exclude_files: {}".format(exclude_files))
rows = parse_redirect_files(files,check_url=args.check_url)

write_to_csv_file(args.output, rows)

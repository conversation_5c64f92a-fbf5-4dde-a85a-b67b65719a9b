<?php

/**
 * https://sim.zauberlist.com/test/test_newsletter.php
 */

use App\Klicktipp\NewsletterQueue;
use App\Klicktipp\Tag;
use App\Klicktipp\TransactionEmails;

define('CLEAR_SESSION_BUTTON_TEXT', 'clear session');

require_once 'form.inc';

// bootstrap
test_bootstrap();

$form = drupal_get_form('test_form');
test_show_form('Newsletter Test', drupal_render($form));

exit;

/**
 */
function test_form($form, $form_state) {

  //drupal_set_message(print_r($form_state,1));

  // $debug[] = $_SESSION['mytest'];

  // show messages from worker
  foreach($_SESSION['newsletters-test']['messages'] as $message) {
    drupal_set_message($message);
  }
  unset($_SESSION['newsletters-test']['messages']);

  $weight = 1;

  if (empty($form_state['input']) && !empty($_SESSION['newsletters-test']['started'])) {
    $form['run'] = array(
      '#type' => 'fieldset',
      '#title' => t('Runtime stats'),
      '#weight' => $weight++,
    );

    $firststart = time();
    $max_createq_time = 0;
    $max_sendq_time = 0;
    $max_sendq_sps = 0;
    $max_tq_time = 0;

    $rows = [];
    foreach ($_SESSION['newsletters-test']['campaigns'] as $workerdata) {
      $row = [];

      [$success, $uid, $campaignid] = $workerdata;
      $row[] = array(
        'data' => l($campaignid, "https://app.klicktipp-staging.com/emails/$uid/$campaignid", ['attributes' => ['target' => '_blank']]),
        'class' => array('table-col-fit table-col-right'),
      );

      if ($success) {

        // processlog (create_queue)
        $result = db_query("SELECT * FROM {processlog} WHERE QueueName = 'create_queue' AND EntityID = :EntityID",
          array(
            ':EntityID' => $campaignid,
          )
        );
        $createqdata = kt_fetch_array($result);
        if (empty($createqdata)) {
          $point = 0;
          $rowdata = '';
        }
        elseif ($createqdata['finished'] > 0) {
          $point = ($createqdata['finished'] - $createqdata['started']);
          $rowdata = $point . ' s';
        }
        else {
          $point = (time() - $createqdata['started']);
          $rowdata = $point . ' s (running)';
        }
        $row[] = array(
          'data' => $rowdata,
          'class' => array('table-col-fit table-col-right'),
        );
        $max_createq_time = max($max_createq_time, $point);

        // get start of simulation
        $firststart = min($firststart, $createqdata['started']);

        // processlog (send_queue)
        $result = db_query("SELECT * FROM {processlog} WHERE QueueName IN (:QueueNames) AND EntityID = :EntityID",
          array(
            ':QueueNames' => ['send_queue', 'send_queue_to_one'],
            ':EntityID' => $campaignid,
          )
        );
        $sendqdata = kt_fetch_array($result);
        if (empty($sendqdata)) {
          $point = 0;
          $rowdata = '';
        }
        elseif ($sendqdata['finished'] > 0) {
          $point = ($sendqdata['finished'] - $sendqdata['started']);
          $rowdata = $point . ' s';
        }
        else {
          $point = (time() - $sendqdata['started']);
          $rowdata = $point . ' s (running)';
        }
        $row[] = array(
          'data' => $rowdata,
          'class' => array('table-col-fit table-col-right'),
        );
        $max_sendq_time = max($max_sendq_time, $point);
        $vars = unserialize($sendqdata['Data']);
        if (!empty($vars['timing']['sum']) && !empty($vars['itemcount']['sum'])) {
          $rowdata = intval($vars['itemcount']['sum'] / $vars['timing']['sum']);
        }
        else {
          $rowdata = 0;
        }
        $row[] = array(
          'data' => empty($rowdata) ? $rowdata : $rowdata.' /s',
          'class' => array('table-col-fit table-col-right'),
        );
        $max_sendq_sps = max($max_sendq_sps, $rowdata);

        // time of automation finished
        $last_finished = db_query("SELECT MAX(tt.SubscriptionDate) AS last_finished FROM {tagging} tt " .
          " INNER JOIN {tag} t ON tt.RelOwnerUserID = t.RelOwnerUserID AND  tt.RelTagID = t.TagID " .
          " WHERE t.RelOwnerUserID = :RelOwnerUserID AND t.Category = :Category AND t.EntityID = :EntityID",
          array(
            ':RelOwnerUserID' => $uid,
            ':Category' => Tag::CATEGORY_SENT,
            ':EntityID' => $campaignid,
          )
        )->fetchField();
        if ($last_finished) {
          $rowdata = ($last_finished - $_SESSION['newsletters-test']['start']);
        }
        else {
          $rowdata = 0;
        }
        $row[] = array(
          'data' => empty($rowdata) ? $rowdata : $rowdata.' /s',
          'class' => array('table-col-fit table-col-right'),
        );
        $max_tq_time = max($max_tq_time, $rowdata);

        // newsletter_queue
        $pending = 0;
        $sent = 0;
        $other = 0;
        $result = db_query("SELECT StatusEnum, count(*) as Count FROM {".NewsletterQueue::TABLE_NAME."} WHERE RelAutoResponderID = :EntityID AND RelOwnerUserID = :RelOwnerUserID GROUP BY StatusEnum ORDER BY StatusEnum",
          array(
            ':EntityID' => $campaignid,
            ':RelOwnerUserID' => $uid,
          )
        );
        while ($status = kt_fetch_array($result)) {
          switch ($status['StatusEnum']) {
            case TransactionEmails::STATUS_PENDING:
              // StatusEnum == 0
              $pending = $status['Count'];
              break;
            case TransactionEmails::STATUS_SENT:
              // StatusEnum == 2
              $sent = $status['Count'];
              break;
            default:
              $other += $status['Count'];
              break;
          };
        }
        $row[] = array(
          'data' => "$pending | $other | $sent",
          'class' => array('table-col-fit table-col-right'),
        );

      }
      else {
        $row[] = 'failed creation';
      }

      $rows[] = $row;
    }

    $rows[] = [
      ['data' => "MAX", 'class' => array('table-col-fit table-col-right')],
      ['data' => "$max_createq_time s", 'class' => array('table-col-fit table-col-right')],
      ['data' => "$max_sendq_time s", 'class' => array('table-col-fit table-col-right')],
      ['data' => "$max_sendq_sps /s", 'class' => array('table-col-fit table-col-right')],
      ['data' => "$max_tq_time s", 'class' => array('table-col-fit table-col-right')],
      ['data' => "", 'class' => array('table-col-fit table-col-right')],
    ];

    $header = array(
      array('data' => t('CampaignID'), 'class' => array('table-col-fit table-col-right')),
      array('data' => t('create_queue time'), 'class' => array('table-col-fit table-col-right')),
      array('data' => t('send_queue time'), 'class' => array('table-col-fit table-col-right')),
      array('data' => t('send_queue sps'), 'class' => array('table-col-fit table-col-right')),
      array('data' => t('newsletter_queue time'), 'class' => array('table-col-fit table-col-right')),
      array('data' => t('pending | other | sent'), 'class' => array('table-col-fit table-col-right')),
    );

    $form['run']['stats'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array('header' => $header, 'rows' => $rows)),
      '#weight' => $weight++,
    );

    // trigger time
    $maxdelay = 0;
    $lastrun = 0;
    $tps = 0;
    $result = db_query("SELECT * FROM {processlog} WHERE QueueName = 'subscriber_queue' AND started > :firststart",
      array(
        ':firststart' => $firststart,
      )
    );
    while ($subsqdata = kt_fetch_array($result)) {
      $vars = unserialize($subsqdata['Data']);
      $jobdata = $vars['jobdata'];

      // aggregate data
      if ($jobdata['#maxdelay'] > $maxdelay) {
        // datasets with maximum delay usually belong to the test campaigns, so we can get the total timespan
        $maxdelay = $jobdata['#maxdelay'];
        $lastrun = $subsqdata['finished'] - $firststart;
      }
      $maxdelay = max($maxdelay, $jobdata['#maxdelay']);
      $tps = max($tps, $jobdata['#sps']);
    }

    $form['run']['trigger_maxdelay'] = array(
      '#type' => 'item',
      '#title' => t('Trigger max delay:'),
      '#value' => "$maxdelay s",
      '#weight' => $weight++,
    );

    $form['run']['trigger_maxsps'] = array(
      '#type' => 'item',
      '#title' => t('Trigger max sps:'),
      '#value' => "$tps /s",
      '#weight' => $weight++,
    );

    $form['run']['trigger_lastrun'] = array(
      '#type' => 'item',
      '#title' => t('Trigger last run:'),
      '#value' => "$lastrun s",
      '#weight' => $weight++,
    );

    // transactional time
    $maxdelay = 0;
    $tps = 0;
    $fps = 0;
    $result = db_query("SELECT * FROM {processlog} WHERE QueueName = '".NewsletterQueue::QUEUENAME."' AND started > :firststart",
      array(
        ':firststart' => $firststart,
      )
    );
    while ($subsqdata = kt_fetch_array($result)) {
      $vars = unserialize($subsqdata['Data']);
      $jobdata = $vars['jobdata'];

      // aggregate data
      $maxdelay = max($maxdelay, $jobdata['#maxdelay']);
      $tps = max($tps, $jobdata['eps']);
      $fps = max($fps, $jobdata['fileswritten']);
    }

    $form['run']['tq_maxdelay'] = array(
      '#type' => 'item',
      '#title' => t('Emails max delay:'),
      '#value' => "$maxdelay s",
      '#weight' => $weight++,
    );

    $form['run']['tq_maxsps'] = array(
      '#type' => 'item',
      '#title' => t('Email max eps:'),
      '#value' => "$tps /s",
      '#weight' => $weight++,
    );

    $form['run']['tq_maxfw'] = array(
      '#type' => 'item',
      '#title' => t('Max files written:'),
      '#value' => "$fps /s",
      '#weight' => $weight++,
    );

    /*
    $form['run']['debug'] = array(
      '#type' => 'markup',
      '#value' => print_r($debug,1),
      '#weight' => $weight++,
    );
    */

  }

  $form['parameters'] = array(
    '#type' => 'fieldset',
    '#title' => t('Test Parameter'),
    '#weight' => $weight++,
  );

  $start = empty($_SESSION['newsletters-test']['start']) ? date("Y-m-d H:i") : date("Y-m-d H:i", $_SESSION['newsletters-test']['start']);
  $form['parameters']['start'] = array(
    '#type' => 'textfield',
    '#title' => t('Start date'),
    '#default_value' => $start,
    '#description' => t('Enter the (email send) start date. Note: send_queue needs time to generate the transactional queue, so put it somewhere in the future.'),
    '#weight' => $weight++,
  );

  /*
  $template = empty($_SESSION['newsletters-test']['template']) ? 'scheduled' : $_SESSION['newsletters-test']['template'];
  $form['parameters']['template'] = array(
    '#type' => 'select',
    '#options' => array(
      'scheduled' => 'Scheduled',
      'secondsofday' => 'Wait (Seconds of day)',
    ),
    '#title' => t('Campaign Template'),
    '#default_value' => $template,
    '#weight' => $weight++,
  );
  */

  $onlyone = empty($_SESSION['newsletters-test']['onlyone']) ? 1 : $_SESSION['newsletters-test']['onlyone'];
  $form['parameters']['onlyone'] = array(
    '#type' => 'checkbox',
    '#title' => t('Send to only one'),
    '#default_value' => $onlyone,
    '#weight' => $weight++,
  );

  $tagname = empty($_SESSION['newsletters-test']['tagname']) ? 'pmtatest' : $_SESSION['newsletters-test']['tagname'];
  $form['parameters']['tagname'] = array(
    '#type' => 'textfield',
    '#title' => t('Tag name (tagged with)'),
    '#default_value' => $tagname,
    '#description' => t('Enter a tag name to segment subscribers (newsletter start condition).'),
    '#weight' => $weight++,
  );

  $nottagname = empty($_SESSION['newsletters-test']['nottagname']) ? '' : $_SESSION['newsletters-test']['nottagname'];
  $form['parameters']['nottagname'] = array(
    '#type' => 'textfield',
    '#title' => t('Tag name (NOT tagged with)'),
    '#default_value' => $nottagname,
    '#description' => t('Enter a tag name to segment subscribers (newsletter start condition).'),
    '#weight' => $weight++,
  );

  $pattern = empty($_SESSION['newsletters-test']['pattern']) ? TEST_USERNAME_PATTERN : $_SESSION['newsletters-test']['pattern'];
  $form['parameters']['pattern'] = array(
    '#type' => 'textfield',
    '#title' => t('Username pattern'),
    '#default_value' => $pattern,
    '#description' => t('Enter a pattern (sprintf) for the usernames. The pattern !pattern creates !username for uid !uid.',
      array('!pattern' => $pattern, '!username' => sprintf($pattern, 99), '!uid' => 99)),
    '#weight' => $weight++,
  );

  $usercount = empty($_SESSION['newsletters-test']['usercount']) ? TEST_USERS_COUNT : $_SESSION['newsletters-test']['usercount'];
  $form['parameters']['usercount'] = array(
    '#type' => 'textfield',
    '#title' => t('No of users'),
    '#default_value' => $usercount,
    '#description' => t('No of users/campaigns we run in parallel (one campaign per user).'),
    '#weight' => $weight++,
  );

  // NOTE: just copy from admin_settings and adjust

  $form['send_queue'] = array(
    '#type' => 'fieldset',
    '#title' => t('Send Queue'),
    '#weight' => $weight++,
  );

  $form['send_queue']['klicktipp_wts_queue_slice'] = array(
    '#type' => 'item',
    '#title' => t('Job size'),
    '#value' => variable_get('klicktipp_wts_queue_slice', 10000),
    '#weight' => $weight++,
  );

  $form['send_queue']['klicktipp_sq_timeout'] = array(
    '#type' => 'item',
    '#title' => t('Job timeout'),
    '#value' => variable_get('klicktipp_sq_timeout', 45),
    '#weight' => $weight++,
  );

  $form['transactional_queue'] = array(
    '#type' => 'fieldset',
    '#title' => t('Transactional Queue'),
    '#weight' => $weight++,
  );

  $form['transactional_queue']['klicktipp_wts_limit'] = array(
    '#type' => 'item',
    '#title' => t('Max job limit'),
    '#value' => variable_get('klicktipp_wts_limit', 3000),
    '#weight' => $weight++,
  );

  $form['transactional_queue']['klicktipp_wts_slices'] = array(
    '#type' => 'item',
    '#title' => t('Queue slices'),
    '#value' => variable_get('klicktipp_wts_slices', 0),
    '#weight' => $weight++,
  );

  $form['subscriber_queue'] = array(
    '#type' => 'fieldset',
    '#title' => t('Subscriber Queue'),
    '#weight' => $weight++,
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#value' => empty($_SESSION['newsletters-test']['started']) ? t('START') : t('Refresh'),
  );

  $form['buttons']['clearsession'] = array(
    '#type' => 'submit',
    '#value' => t(CLEAR_SESSION_BUTTON_TEXT),
  );

  return $form;
}

/**
 */
function test_form_validate($form, &$form_state) {
  if ($form_state['values']['op'] == t(CLEAR_SESSION_BUTTON_TEXT)) {
    // ignores $form_state['values']
  }
  elseif (!empty($_SESSION['newsletters-test']['started'])) {
    // ignores $form_state['values']
  }
  else {
    // new batch
    $start = date("Y-m-d", strtotime($form_state['values']['start']));
    if ($start != date("Y-m-d")) {
      form_set_error('start', t('Sorry, work with date = today only'));
    }
  }
}

/**
 */
function test_form_submit($form, &$form_state) {

  if ($form_state['values']['op'] == t(CLEAR_SESSION_BUTTON_TEXT)) {
    unset($_SESSION['newsletters-test']);
  }
  elseif (!empty($_SESSION['newsletters-test']['started'])) {
    // started, so nothing to do
    $_SESSION['newsletters-test']['messages'][] = t('Refreshed');
  }
  else {
    $_SESSION['newsletters-test']['messages'][] = t('Batch started');

    // evaluate parameters
    $timetosend = strtotime($form_state['values']['start']);
    $_SESSION['newsletters-test']['start'] = $timetosend;
    $_SESSION['newsletters-test']['pattern'] = $form_state['values']['pattern'];
    $_SESSION['newsletters-test']['usercount'] = $form_state['values']['usercount'];
    $_SESSION['newsletters-test']['tagname'] = $form_state['values']['tagname'];
    $_SESSION['newsletters-test']['nottagname'] = $form_state['values']['nottagname'];
    $_SESSION['newsletters-test']['template'] = $form_state['values']['template'];
    $_SESSION['newsletters-test']['onlyone'] = $form_state['values']['onlyone'];

    // start test
    $_SESSION['newsletters-test']['started'] = TRUE;

    $data = [];
    for ($no = 0; $no < $_SESSION['newsletters-test']['usercount']; $no++) {
      $data[] = array(
        'shard' => $no,
        'username' => sprintf($_SESSION['newsletters-test']['pattern'], $no+1),
        'tag' => $_SESSION['newsletters-test']['tagname'],
        'nottag' => $_SESSION['newsletters-test']['nottagname'],
        'timetosend' => $timetosend,
        'template' => $_SESSION['newsletters-test']['template'],
        'onlyone' => $_SESSION['newsletters-test']['onlyone'],
      );
    }

    // BATCH
    $batch = array(
      'operations' => array(
        array('test_batch_worker', array($data)),
      ),
      'file' => 'test/test_newsletter_worker.inc',
    );
    batch_set($batch);

    $_SESSION['newsletters-test']['messages'][] = t('Batch started');
  }

  $form_state['redirect'] = '/test/test_newsletter.php';
}

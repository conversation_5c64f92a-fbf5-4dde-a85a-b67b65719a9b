#include .env

# Misc
SHELL :=/bin/bash

.DEFAULT_GOAL = help	# if you type 'make' without arguments, this is the default: show the help
.PHONY        : # Not needed here, but you can put your all your targets to be sure
                # there is no name conflict between your files and your targets.

MKFILE_DIR := $(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))

# For the e2e test webapp targets below (search for e2e-rr or webapp).
# On MacOS (orbstack) we have special circumstances
# 1. MacOS has a special mount dir and you can't directly do bind bounds
#    into the containers. Remark: that was the case for Docker-Desktop on Mac. Orbstack seems to
#	have a different setup. No special volume path is needed.
# 2. We have no docker group on MAC
# 3. We have a different docker.sock path on MAC
OS := $(shell uname -s)
ifeq ($(OS),Darwin)
export DOCKER_VOLUME_MOUNT_PREFIX :=
export DOCKER_HOST_DOCKER_GROUP := "NoDockerGroupOnMac"
export DOCKER_SOCK := $(HOME)/.orbstack/run/docker.sock
export MAC_DOCKER_SOCK_GROUP_NAME := `stat $(DOCKER_SOCK) | cut -d ' ' -f6`
export MAC_DOCKER_SOCK_GROUP_ID := `stat -f '%g' $(DOCKER_SOCK)`
export DOCKER_HOST_PLATFORM := Darwin
export DOCKER_IMAGES_PLATFORM := linux/arm64
export DOCKER_IMAGES_ARCH := arm64
else
export DOCKER_VOLUME_MOUNT_PREFIX :=
export DOCKER_HOST_DOCKER_GROUP := `getent group docker | cut -d: -f3`
export DOCKER_SOCK := /var/run/docker.sock
export MAC_DOCKER_SOCK_GROUP_NAME := "NotNecessaryOnLinux"
export MAC_DOCKER_SOCK_GROUP_ID := "NotNecessaryOnLinux"
export DOCKER_HOST_PLATFORM := Linux
export DOCKER_IMAGES_PLATFORM := linux/amd64
export DOCKER_IMAGES_ARCH := amd64
endif

# For e2e record/replay
override E2ERR_DIND_BASE_DOCKER_TAG = 07082024_v1
export E2ERR_BASE_DOCKER_TAG
# <date>_<flutter-version>_<our_versioning>
override E2ERR_FLUTTER_BUILD_BASE_DOCKER_TAG = 07082024_3_22_3_v1
export E2ERR_FLUTTER_BUILD_BASE_DOCKER_TAG
override E2ERR_FLUTTER_UI_DOCKER_TAG = 07082024_v1
export E2ERR_FLUTTER_UI_DOCKER_TAG

override KLICKTIPP_LOCAL_ROOT = $(MKFILE_DIR)/../..
export KLICKTIPP_LOCAL_ROOT

# WARNING: need to export the env var, otherwise docker build will fail
export DOCKER_BUILDKIT ?= 1
export COMPOSE_DOCKER_CLI_BUILD ?= 1

export DOCKER_ENGINE_MIN_VERSION ?= 23.0.1

# Ask user for confirmation helper
_check:
	@echo -n "Are you sure? [y/N] " && read ans && [ $${ans:-N} = y ]

##
##—— DOCKER —————————————————————————————————————————————————————————————————————
##

docker-check-version:
ifneq ($(shell expr `docker version -f '{{.Server.Version}}'` \>= "$(DOCKER_ENGINE_MIN_VERSION)"),1)
	$(error Docker Daemon/Engine check: ERROR - version is lower than $(DOCKER_ENGINE_MIN_VERSION))
endif
ifneq ($(shell docker compose version --short >/dev/null 2>&1; echo $$?),0)
	$(error Docker Compose Plugin check: ERROR - 'docker compose' is not installed or not functional)
endif

## Log in to KlickTipp Docker registry
.PHONY: docker-login
.ONESHELL:
docker-login: docker-check-version
	@./scripts/ci/docker-login.sh

## Pulls images for services defined in a Compose file, but does not start the containers.
.PHONY: docker-pull
.ONESHELL:
docker-pull: docker-check-version
	@./scripts/docker/docker-compose-pull.sh

docker-networks: docker-check-version
	@docker network create --attachable --internal=false kt-net 2>/dev/null || true
	@docker network create --attachable --internal=false kt-e2e-net 2>/dev/null || true

## Create and start ALL containers
.PHONY: docker-up
.ONESHELL:
docker-up: docker-check-version docker-networks docker-pull docker-nginx-proxy-up
	@./scripts/docker/docker-compose-start.sh $(COMMAND_ARGS)

## Stop services
.PHONY: docker-stop
.ONESHELL:
docker-stop: docker-check-version
	@./scripts/docker/docker-compose-stop.sh $(COMMAND_ARGS)

## Restart services
.PHONY: docker-restart
.ONESHELL:
docker-restart: docker-check-version
	@./scripts/docker/docker-compose-restart.sh $(COMMAND_ARGS)

## Stop and remove containers and networks
.PHONY: docker-down
.ONESHELL:
docker-down: docker-check-version
	@./scripts/docker/docker-compose-down.sh $(COMMAND_ARGS)

## Stops containers and removes containers, networks, volumes, and images.
## WARNING: External Docker Volumes will be NOT DELETED. However other persistent volumes will be removed!
.PHONY: docker-purge
.ONESHELL:
docker-purge: docker-check-version _check
	@./scripts/docker/docker-compose-down.sh --volume $(COMMAND_ARGS)

docker-ps: klicktipp-docker-ps docker-nginx-proxy-ps

## Show the status of all Docker Compose containers
.PHONY: docker-status
.ONESHELL:
docker-status: docker-check-version docker-ps

## Starts the nginx-proxy
.PHONY: docker-nginx-proxy-up
.ONESHELL:
docker-nginx-proxy-up: docker-check-version
	@docker rm --force traefik 2>/dev/null || true # delete old traefik container to avoid conflicts
	@docker network create --driver=bridge --attachable --internal=false gateway 2>/dev/null || true
	@echo "== Start nginx-proxy Proxy =="
	@docker compose -p nginx-proxy -f docker-compose.nginx-proxy.yml up --build --detach --remove-orphans

## Starts the nginx-proxy proxy
.PHONY: docker-nginx-proxy-down
.ONESHELL:
docker-nginx-proxy-down: docker-check-version
	@if [ -n "${CI}" ];  then echo "WARNING: We are running in a CI enviornment. Will not stop nginx-proxy."; exit 0; fi
	@echo "== Stopping nginx-proxy Proxy =="
	@docker compose -p nginx-proxy -f docker-compose.nginx-proxy.yml down --timeout 3

## Show the status of the nginx-proxy Docker containers
.PHONY: docker-nginx-proxy-ps
.ONESHELL:
docker-nginx-proxy-ps: docker-check-version
	@docker compose -p nginx-proxy -f docker-compose.nginx-proxy.yml ps --all

##
##—— Environment Variables ——————————————————————————————————————————————————————
##

## Generate envvars from klicktipp config files
.PHONY: envvars
.ONESHELL:
envvars: KLICKTIPP_ENVIRONMENT ?= local
envvars:
	@KLICKTIPP_ENVIRONMENT=$(KLICKTIPP_ENVIRONMENT) scripts/ci/generate-envvars.sh

##
##—— LOCAL Environment ————————————————————————————————————————————————————————
##

## Install required LOCAL environment dependencies
.PHONY: install-local-dependencies
.ONESHELL:
install-local-dependencies: KLICKTIPP_ENVIRONMENT ?= local
install-local-dependencies:
	@scripts/ci/install_dependencies.sh

## Install required git hooks
.PHONY: install-git-hooks
.ONESHELL:
install-git-hooks:
	@scripts/git/install-hooks.sh

##
##—— KLICKTIPP PHP Backend ——————————————————————————————————————————————————————
##

## Start the KlickTipp application containers (frontend, backend, DB, etc.).  Alias for `docker-up`
.PHONY: klicktipp-up
.ONESHELL:
klicktipp-up: envvars docker-up
	@./scripts/docker/exec-composer-install.sh
	@echo ""
	@echo "=== Checking for Config and Deployment Errors ==="

	docker compose exec --no-TTY klicktipp sh -c './scripts/ci/wait_for_rabbitmq.sh'
	docker compose exec --no-TTY klicktipp sh -c './scripts/ci/provision_rabbitmq.sh'
	docker compose exec --no-TTY keycloak  sh -c '/usr/bin/wait_for_keycloak.sh'
	docker compose exec --no-TTY klicktipp sh -c './scripts/ci/wait_for_url.sh https://klicktipp/ 120 "--insecure"'

## Stop the KlickTipp application containers (frontend, backend, DB, etc.). Alias for `docker-down`
.PHONY: klicktipp-down
.ONESHELL:
klicktipp-down: docker-down

## Restart the KlickTipp application containers (frontend, backend, DB, etc.). Alias for `docker-restart`
.PHONY: klicktipp-restart
.ONESHELL:
klicktipp-restart: envvars docker-restart

## Wrapper to the drupal install script (with drush)
.PHONY: klicktipp-init-drush
.ONESHELL:
klicktipp-init-drush:
	@./scripts/docker/exec-init-drush.sh $(COMMAND_ARGS)

## Wrapper to the setup RabbitMQ
.PHONY: klicktipp-init-rabbitmq
.ONESHELL:
klicktipp-init-rabbitmq:
	@./scripts/docker/exec-init-rabbitmq.sh


## Recreate DB volume.
.PHONY: klicktipp-recreate-db-volume
.ONESHELL:
klicktipp-recreate-db-volume: _check
	@./scripts/docker/recreate-db-volume.sh $(COMMAND_ARGS)

## Stops containers and removes containers, networks, volumes, and images.
## WARNING: External Docker Volumes will be NOT DELETED. However other persistent volumes will be removed!
## Alias for `docker-purge`
.PHONY: klicktipp-purge
.ONESHELL:
klicktipp-purge: docker-purge

## Start a /bin/bash shell in the klicktipp container
.PHONY: klicktipp-shell
.ONESHELL:
klicktipp-shell:
	@./scripts/docker/docker-compose-shell.sh

## Display klicktipp container logs
.PHONY: klicktipp-logs
.ONESHELL:
klicktipp-logs:
	@docker compose logs -tf --tail=1000 klicktipp

## Display all container logs
.PHONY: klicktipp-logs-all
.ONESHELL:
klicktipp-logs-all:
	@docker compose logs -tf --tail=1000

## Display docker compose running config
.PHONY: klicktipp-config
.ONESHELL:
klicktipp-config: envvars
	@docker compose config

## Dump the percona (MySQL) DB to StdOut
.PHONY: klicktipp-db-dump
.ONESHELL:
klicktipp-db-dump:
	@./scripts/docker/exec-db-dump.sh $(COMMAND_ARGS)

## Restore a (MySQL) DB dump over StdIn
.PHONY: klicktipp-db-restore
.ONESHELL:
klicktipp-db-restore:
	@scripts/docker/exec-db-restore.sh $(COMMAND_ARGS)

## Alias for `klicktipp-print-urls`
.PHONY: klicktipp-urls
.ONESHELL:
klicktipp-urls: klicktipp-print-urls

## Show the list of URLs/Services used for the LOCAL Dev Environment
.PHONY: klicktipp-print-urls
.ONESHELL:
klicktipp-print-urls:
	@./scripts/docker/docker-compose-print-urls.sh

klicktipp-docker-ps:
	@docker compose ps --all

## Show the status of the KlickTipp Docker containers
.PHONY: klicktipp-status
.ONESHELL:
klicktipp-status: klicktipp-docker-ps

## Start the KlickTipp application container ("klicktipp") without any dependencies
.PHONY: klicktipp-app-up
.ONESHELL:
klicktipp-app-up: envvars
	@./scripts/docker/docker-compose-start.sh --no-deps klicktipp

## Klicktipp Composer install
.PHONY: klicktipp-composer-install
.ONESHELL:
klicktipp-composer-install: envvars docker-networks klicktipp-app-up
	@./scripts/docker/exec-composer-install.sh

## Klicktipp Composer audit
.PHONY: klicktipp-composer-audit
.ONESHELL:
klicktipp-composer-audit: envvars docker-networks klicktipp-app-up
	@./scripts/docker/exec-composer-audit.sh

## Klicktipp Build PHP image
.PHONY: klicktipp-build-image
.ONESHELL:
KLICKTIPP_PHP_VERSION ?= 7.4
klicktipp-build-image: envvars
	@echo "=== Creating KlickTipp PHP Backend App Docker Image ==="
	source .env && \
	pushd docker/klicktipp/backend/drupal-php/ && \
	make docker-build docker-tag \
		KLICKTIPP_PHP_BASE_DOCKER_IMAGE_TAG="$${KLICKTIPP_PHP_BASE_DOCKER_IMAGE_TAG}" \
      	TAG="$${KLICKTIPP_KLICKTIPP_DOCKER_IMAGE_TAG}" \
      	DOCKER_TARGET=source

##
##—— Symphony phpunit tests  ——————————————————————————————————————————————————————
##

## Klicktipp phpunit helper
## phpunit
.PHONY: klicktipp-phpunit
.ONESHELL:
klicktipp-phpunit:
	@./scripts/docker/exec-phpunit.sh $(ARGS)

## unit tests
.PHONY: klicktipp-phpunit-unit-tests
.ONESHELL:
klicktipp-phpunit-unit-tests:
	@./scripts/docker/exec-phpunit.sh --testsuite=unit $(ARGS)

## integration tests
.PHONY: klicktipp-phpunit-integration-tests
.ONESHELL:
klicktipp-phpunit-integration-tests:
	@./scripts/docker/exec-phpunit.sh --testsuite=integration $(ARGS)

##
##—— PHP Code Quality  ——————————————————————————————————————————————————————
##

## Klicktipp phpcs helper
## phpcs
.PHONY: klicktipp-phpcs
.ONESHELL:
klicktipp-phpcs:
	@./scripts/docker/exec-phpcs.sh $(ARGS)

## phpcs generate baseline
.PHONY: klicktipp-phpcs-generate-baseline
.ONESHELL:
klicktipp-phpcs-generate-baseline:
	@./scripts/docker/exec-phpcs.sh --report=\\DR\\CodeSnifferBaseline\\Reports\\Baseline --report-file=phpcs.baseline.xml


## Klicktipp PHP code beautifier and fixer
## phpcbf
.PHONY: klicktipp-phpcbf
.ONESHELL:
klicktipp-phpcbf:
	@./scripts/docker/exec-phpcbf.sh $(ARGS)

## Klicktipp PHPStan
## analyse
.PHONY: klicktipp-phpstan-analyse
.ONESHELL:
klicktipp-phpstan-analyse:
	@./scripts/ci/phpstan.sh analyse

## generate baseline
.PHONY: klicktipp-phpstan-generate-baseline
.ONESHELL:
klicktipp-phpstan-generate-baseline:
	@./scripts/ci/phpstan.sh analyse --generate-baseline

##
##—— KLICKTIPP Simpletests ——————————————————————————————————————————————————————
##

## Execute a specific bundle of all available test classes.
## Means:
##  assume these are ALL test classes (so spread over cli, coreapi, forms, ...)
##     classes: class1,class2,class3,class4,class5
##  If you pass bundle_index 1 with bundle_size 2, the following classes would be executed:
##     class3, class4
.PHONY: klicktipp-simpletests-multi-bundle
.ONESHELL:
klicktipp-simpletests-multi-bundle: envvars
	@export KLICKTIPP_SIMPLETEST_PREFIX=klicktipp
	@./scripts/docker/exec-simpletests-bundle-multi.sh $(COMMAND_ARGS)


## Run all Simpletests. Also starts the containers.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/simpletest/tests_results/.
## Example: make klicktipp-simpletests KLICKTIPP_SIMPLETEST_PARAMS="--verbose --class cliAmemberDigistoreTestCase"
.PHONY: klicktipp-simpletests-all
.ONESHELL:
klicktipp-simpletests-all: envvars docker-up
	@export KLICKTIPP_SIMPLETEST_PREFIX=all
	@./scripts/docker/exec-simpletests-multi.sh $(COMMAND_ARGS)

## Executes an isolated test environment for the 'klicktipp' simpletest suite.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/simpletest/tests_results/klicktipp/.
## Example: 'make klicktipp-simpletests-multi-klicktipp SIMPLETEST_CLASS_NAMES=klicktippAPIapikeyTestCase'
.PHONY: klicktipp-simpletests-multi-klicktipp
.ONESHELL:
klicktipp-simpletests-multi-klicktipp: envvars
	@export KLICKTIPP_SIMPLETEST_PREFIX=klicktipp
	@./scripts/docker/exec-simpletests-multi.sh $(COMMAND_ARGS)

## Executes an isolated test environment for the 'forms' simpletest suite.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/simpletest/tests_results/forms/.
## Example: 'make klicktipp-simpletests-multi-forms SIMPLETEST_CLASS_NAMES=klicktippAPIapikeyTestCase'
.PHONY: klicktipp-simpletests-multi-forms
.ONESHELL:
klicktipp-simpletests-multi-forms: envvars
	@export KLICKTIPP_SIMPLETEST_PREFIX=forms
	@./scripts/docker/exec-simpletests-multi.sh $(COMMAND_ARGS)

## Executes an isolated test environment for the 'cli' simpletest suite.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/simpletest/tests_results/cli/.
## Example: 'make klicktipp-simpletests-multi-cli SIMPLETEST_CLASS_NAMES=klicktippAPIapikeyTestCase'
.PHONY: klicktipp-simpletests-multi-cli
.ONESHELL:
klicktipp-simpletests-multi-cli: envvars
	@export KLICKTIPP_SIMPLETEST_PREFIX=cli
	@./scripts/docker/exec-simpletests-multi.sh $(COMMAND_ARGS)

## Executes an isolated test environment for the 'coreapi' simpletest suite.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/simpletest/tests_results/coreapi/.
## Example: 'make klicktipp-simpletests-multi-coreapi SIMPLETEST_CLASS_NAMES=klicktippAPIapikeyTestCase'
.PHONY: klicktipp-simpletests-multi-coreapi
.ONESHELL:
klicktipp-simpletests-multi-coreapi: envvars
	@export KLICKTIPP_SIMPLETEST_PREFIX=coreapi
	@./scripts/docker/exec-simpletests-multi.sh $(COMMAND_ARGS)

##
##—— KLICKTIPP Unittests ————————————————————————————————————————————————————————
##

## Run all Unittests. Also starts the containers.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/simpletest/tests_results/.
.PHONY: klicktipp-unittests-all
.ONESHELL:
klicktipp-unittests-all: envvars docker-up
	@export KLICKTIPP_UNITTEST_PREFIX=all
	@./scripts/docker/exec-unittests-multi.sh $(COMMAND_ARGS)

## Executes an isolated test environment for the 'klicktipp' unittest suite.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/unittest/tests_results/klicktipp/.
## Example: 'make klicktipp-unittests-multi-klicktipp UNITTEST_CLASS_NAMES=klicktippAPIapikeyTestCase'
.PHONY: klicktipp-unittests-multi-klicktipp
.ONESHELL:
klicktipp-unittests-multi-klicktipp: envvars
	@export KLICKTIPP_UNITTEST_PREFIX=klicktipp
	@./scripts/docker/exec-unittests-multi.sh $(COMMAND_ARGS)

## Executes an isolated test environment for the 'forms' unittest suite.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/unittest/tests_results/forms/.
## Example: 'make klicktipp-unittests-multi-forms UNITTEST_CLASS_NAMES=klicktippAPIapikeyTestCase'
.PHONY: klicktipp-unittests-multi-forms
.ONESHELL:
klicktipp-unittests-multi-forms: envvars
	@export KLICKTIPP_UNITTEST_PREFIX=forms
	@./scripts/docker/exec-unittests-multi.sh $(COMMAND_ARGS)

## Executes an isolated test environment for the 'cli' unittest suite.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/unittest/tests_results/cli/.
## Example: 'make klicktipp-unittests-multi-cli UNITTEST_CLASS_NAMES=klicktippAPIapikeyTestCase'
.PHONY: klicktipp-unittests-multi-cli
.ONESHELL:
klicktipp-unittests-multi-cli: envvars
	@export KLICKTIPP_UNITTEST_PREFIX=cli
	@./scripts/docker/exec-unittests-multi.sh $(COMMAND_ARGS)

## Executes an isolated test environment for the 'coreapi' unittest suite.
## The tests will be called inside the klicktipp docker container and executed there.
## Test results will be stored under ./files/logs/unittest/tests_results/coreapi/.
## Example: 'make klicktipp-unittests-multi-coreapi UNITTEST_CLASS_NAMES=klicktippAPIapikeyTestCase'
.PHONY: klicktipp-unittests-multi-coreapi
.ONESHELL:
klicktipp-unittests-multi-coreapi: envvars
	@export KLICKTIPP_UNITTEST_PREFIX=coreapi
	@./scripts/docker/exec-unittests-multi.sh $(COMMAND_ARGS)

##
##—— KLICKTIPP Cockpit ——————————————————————————————————————————————————————————
##

## Execute all npm commands for the KlickTipp Angular Cockpit with Docker
.PHONY: cockpit
.ONESHELL:
cockpit: cockpit-lint cockpit-test cockpit-build

## Build the KlickTipp Angular Cockpit with Docker
.PHONY: cockpit-build
.ONESHELL:
cockpit-build:
	@cd ./app/klicktipp/ && \
  	make  angular-klicktipp-docker-pull \
  				angular-klicktipp-docker-build \
  				angular-klicktipp-docker-tag \
  				angular-klicktipp-docker-copy-build

## lint the KlickTipp Angular Cockpit with Docker
.PHONY: cockpit-lint
.ONESHELL:
cockpit-lint:
	@cd ./app/klicktipp/ && \
    	make  angular-klicktipp-docker-pull \
    				angular-klicktipp-npm-lint

## test the KlickTipp Angular Cockpit with Docker
.PHONY: cockpit-test
.ONESHELL:
cockpit-test:
	@cd ./app/klicktipp/ && \
    	make  angular-klicktipp-docker-pull \
    				angular-klicktipp-npm-test

##
##—— KLICKTIPP Client —————————————————————————————————————————————————————
##

## Execute all npm commands for the KlickTipp Angular Client with Docker
.PHONY: client-klicktipp
.ONESHELL:
client-klicktipp: client-klicktipp-lint client-klicktipp-test client-klicktipp-build

## Build the KlickTipp Angular Client with Docker
.PHONY: client-klicktipp-build
.ONESHELL:
client-klicktipp-build:
	@cd ./client/ && \
	make client-klicktipp-docker-pull \
         client-klicktipp-docker-build \
         client-klicktipp-docker-tag \
         client-klicktipp-docker-copy-build

## lint the KlickTipp Angular Client with Docker
.PHONY: client-klicktipp-lint
.ONESHELL:
client-klicktipp-lint:
	@cd ./client/ && \
	make client-klicktipp-docker-pull \
         client-klicktipp-npm-lint

## test the KlickTipp Angular Client with Docker
.PHONY: client-klicktipp-test
.ONESHELL:
client-klicktipp-test:
	@cd ./client/ && \
	make client-klicktipp-docker-pull \
         client-klicktipp-npm-test

##
##—— KLICKTIPP npm ——————————————————————————————————————————————————————————————
##

## Execute all npm commands for the KlickTipp Angular Cockpit and Client with Docker
.PHONY: npm-all
.ONESHELL:
npm-all: cockpit client-klicktipp

##
##—— Tests E2E ——————————————————————————————————————————————————————————————————
##

## Wrapper to execute all e2e test suites
.PHONY: tests
tests: klicktipp-simpletests-all tests-e2e-local
	@echo "Running all KlickTipp tests..."

## Initialize local E2E tests infrastructure
.PHONY: tests-e2e-init
.ONESHELL:
tests-e2e-init:
		export DEBUG=1
		export TESTSET=ci_local
		cd test/e2e
		make print_compose_config
		make clean-docker
		make kt-stack-reset-and-import-local-test-data

## Cleanup old python trash from previous runs
.PHONY: tests-e2e-clean
.ONESHELL:
tests-e2e-clean:
		export DEBUG=1
		export TESTSET=ci_local
		cd test/e2e
		make clean

## Start the E2E Klicktipp Docker Compose stack
.PHONY: tests-e2e-stack-up
.ONESHELL:
tests-e2e-stack-up:
		export DEBUG=1
		export TESTSET=ci_local
		cd test/e2e
		make kt-stack-up

## Stop the E2E Klicktipp Docker Compose stack
.PHONY: tests-e2e-stack-down
.ONESHELL:
tests-e2e-stack-down:
		export DEBUG=1
		export TESTSET=ci_local
		cd test/e2e
		make kt-stack-down

## Reset the E2E Klicktipp Docker Compose stack
.PHONY: tests-e2e-stack-reset
.ONESHELL:
tests-e2e-stack-reset:
		export DEBUG=1
		export TESTSET=ci_local
		cd test/e2e
		make kt-stack-reset

## Import local test data into the E2E Klicktipp Docker Compose stack
.PHONY: tests-e2e-stack-import-local-test-data
.ONESHELL:
tests-e2e-stack-import-local-test-data:
		export DEBUG=1
		export TESTSET=ci_local
		cd test/e2e
		make kt-stack-import-local-test-data

## Export local test data to test/e2e/tests/data/new_kt_testdata_dump.sql
.PHONY: tests-e2e-stack-export-local-test-data
.ONESHELL:
tests-e2e-stack-export-local-test-data:
		export DEBUG=1
		export TESTSET=ci_local
		cd test/e2e
		make kt-stack-export-local-test-data

## Execute local E2E tests
.PHONY: tests-e2e-local
.ONESHELL:
tests-e2e-local:
		export DEBUG=1
		export TESTSET=ci_local
		cd test/e2e
		make tests-e2e-local
		make clean-docker

## Build e2e tests webapp images
.PHONY: e2e-rr-build
.ONESHELL:
e2e-rr-build:
		cd test/e2e/record_replay && docker compose --file docker-compose.e2e.webapp.yml build \
 		               --build-arg DOCKER_HOST_DOCKER_GROUP=$(DOCKER_HOST_DOCKER_GROUP) \
 		               --build-arg DOCKER_HOST_USR=$(shell id -u) \
 		               --build-arg DOCKER_HOST_USR_GROUP=$(shell id -g) \
 		               --build-arg DOCKER_HOST_USR_GROUP_NAME=$(shell id -g -n) \
 		               --build-arg DOCKER_HOST_USR_NAME=$(shell id -u -n) \
 		               --build-arg DOCKER_IMAGES_PLATFORM=$(DOCKER_IMAGES_PLATFORM) \
 		               --build-arg DOCKER_IMAGES_ARCH=$(DOCKER_IMAGES_ARCH) \
 		               --build-arg E2ERR_DIND_BASE_DOCKER_TAG=$(E2ERR_DIND_BASE_DOCKER_TAG)

## start e2e tests webapp stack
.PHONY: e2e-rr-up
.ONESHELL:
e2e-rr-up: docker-networks
		cd test/e2e/record_replay && KLICKTIPP_LOCAL_ROOT=$(PWD) \
		 && export DOCKER_SOCK=$(DOCKER_SOCK) \
		 && export MAC_DOCKER_SOCK_GROUP_ID=$(MAC_DOCKER_SOCK_GROUP_ID) \
		 && export MAC_DOCKER_SOCK_GROUP_NAME=$(MAC_DOCKER_SOCK_GROUP_NAME) \
		 && export DOCKER_HOST_PLATFORM=$(DOCKER_HOST_PLATFORM) \
		 && export DOCKER_IMAGES_PLATFORM=$(DOCKER_IMAGES_PLATFORM) \
		 && export DOCKER_IMAGES_ARCH=$(DOCKER_IMAGES_ARCH) \
		 && export E2ERR_FLUTTER_UI_DOCKER_TAG=$(E2ERR_FLUTTER_UI_DOCKER_TAG) \
		 && docker compose --file docker-compose.e2e.webapp.yml up --renew-anon-volumes --detach
		sleep 5
		echo $(DOCKER_SOCK)
		echo $(MAC_DOCKER_SOCK_GROUP_NAME)
		echo $(MAC_DOCKER_SOCK_GROUP_ID)
		if [ "$(MAC_DOCKER_SOCK_GROUP_NAME)" = "NotNecessaryOnLinux" ]; then echo "No /home/<USER>/.aws owner (celery_user:celery_user) adjustments on Linux"; else docker exec -u root -it record_replay-worker-1 bash -c 'chown celery_user:celery_user /home/<USER>/.aws'; fi
		if [ "$(MAC_DOCKER_SOCK_GROUP_NAME)" = "NotNecessaryOnLinux" ]; then echo "No /home/<USER>/.docker owner (celery_user:celery_user) adjustments on Linux"; else docker exec -u root -it record_replay-worker-1 bash -c 'chown celery_user:celery_user /home/<USER>/.docker'; fi
		if [ "$(MAC_DOCKER_SOCK_GROUP_NAME)" = "NotNecessaryOnLinux" ]; then echo "No /var/run/docker.sock owner (celery_user:docker) adjustments on Linux"; else docker exec -u root -it record_replay-worker-1 bash -c 'chown celery_user:docker /var/run/docker.sock'; fi
		if [ "$(MAC_DOCKER_SOCK_GROUP_NAME)" = "NotNecessaryOnLinux" ]; then echo "No /var/run/docker.sock owner (flask_user:docker) adjustments on Linux"; else docker exec -u root -it record_replay-flask-1 bash -c 'chown flask_user:docker /var/run/docker.sock'; fi

## show e2e webapp backend logs on console
.PHONY: e2e-rr-log
.ONESHELL:
e2e-rr-log:
		cd test/e2e/record_replay && docker compose --file docker-compose.e2e.webapp.yml logs --follow --timestamps

## shutdown e2e tests webapp stack
.PHONY: e2e-rr-down
.ONESHELL:
e2e-rr-down:
		cd test/e2e/record_replay && KLICKTIPP_LOCAL_ROOT=$(PWD) \
		&& export DOCKER_SOCK=$(DOCKER_SOCK) \
		&& export MAC_DOCKER_SOCK_GROUP_ID=$(MAC_DOCKER_SOCK_GROUP_ID) \
		&& export MAC_DOCKER_SOCK_GROUP_NAME=$(MAC_DOCKER_SOCK_GROUP_NAME) \
		&& export DOCKER_HOST_PLATFORM=$(DOCKER_HOST_PLATFORM) \
		&& export DOCKER_IMAGES_PLATFORM=$(DOCKER_IMAGES_PLATFORM) \
		&& export DOCKER_IMAGES_ARCH=$(DOCKER_IMAGES_ARCH) \
		&& export E2ERR_DIND_BASE_DOCKER_TAG=$(E2ERR_DIND_BASE_DOCKER_TAG) \
		&& export E2ERR_FLUTTER_UI_DOCKER_TAG=$(E2ERR_FLUTTER_UI_DOCKER_TAG) \
		&& docker compose --file docker-compose.e2e.webapp.yml down --volumes --timeout 3

## reset e2e record/replay webapp and show log
## rebuild flutter frontend for extension
.PHONY: e2e-rr-reset-and-show-log
.ONESHELL:
e2e-rr-reset-and-show-log: e2e-rr-down
e2e-rr-reset-and-show-log: flutter-build-base
e2e-rr-reset-and-show-log: dind-base
e2e-rr-reset-and-show-log: e2e-rr-build-flutter-ui
e2e-rr-reset-and-show-log: e2e-rr-build
e2e-rr-reset-and-show-log: e2e-rr-up
e2e-rr-reset-and-show-log: e2e-rr-copy-flutter-webbuild-local
e2e-rr-reset-and-show-log: e2e-rr-log

.PHONY: e2e-rr-build-dind-base
.ONESHELL:
e2e-rr-build-dind-base:
	cd test/e2e/record_replay && \
		docker build --platform=$(DOCKER_IMAGES_PLATFORM) --no-cache -f Dockerfile.dind_base.$(DOCKER_IMAGES_ARCH) \
		. -t record_replay-dind-base:$(E2ERR_DIND_BASE_DOCKER_TAG)

.PHONY: e2e-rr-build-flutter-build-base
.ONESHELL:
e2e-rr-build-flutter-build-base:
	cd test/e2e/record_replay/browser_extension/ktrecorder/ui && \
		docker build --platform=$(DOCKER_IMAGES_PLATFORM) --no-cache -f Dockerfile.flutter_build_base \
		. -t flutter-build-base:$(E2ERR_FLUTTER_BUILD_BASE_DOCKER_TAG)

.PHONY: e2e-rr-build-flutter-ui
.ONESHELL:
e2e-rr-build-flutter-ui:
	-mkdir test/e2e/record_replay/browser_extension/ktrecorder/ui/build 2> /dev/null
	-rm -rf test/e2e/record_replay/browser_extension/ktrecorder/ui/build/web 2> /dev/null
	cd test/e2e/record_replay/browser_extension/ktrecorder/ui && \
		docker build --platform=$(DOCKER_IMAGES_PLATFORM) --build-arg E2ERR_FLUTTER_BUILD_BASE_DOCKER_TAG=$(E2ERR_FLUTTER_BUILD_BASE_DOCKER_TAG) -f Dockerfile \
		. -t record_replay-flutter-ui:$(E2ERR_FLUTTER_UI_DOCKER_TAG)

FLUTTER_BASE := $(shell docker images -q flutter-build-base:$(E2ERR_FLUTTER_BUILD_BASE_DOCKER_TAG) 2> /dev/null)
DIND_BASE := $(shell docker images -q record_replay-dind-base:$(E2ERR_DIND_BASE_DOCKER_TAG) 2> /dev/null)

.PHONY: flutter-build-base
.ONESHELL:
ifdef FLUTTER_BASE
flutter-build-base:
	@echo "Flutter build base exists. No Build."
else
flutter-build-base: e2e-rr-build-flutter-build-base
endif

.PHONY: dind-base
.ONESHELL:
ifdef DIND_BASE
dind-base:
	@echo "DinD base exists. No Build."
else
dind-base: e2e-rr-build-dind-base
endif

.PHONY: e2e-rr-copy-flutter-webbuild-local
.ONESHELL:
e2e-rr-copy-flutter-webbuild-local:
	-mkdir test/e2e/record_replay/browser_extension/ktrecorder/ui/build 2> /dev/null
	-rm -rf test/e2e/record_replay/browser_extension/ktrecorder/ui/build/web 2> /dev/null
	docker cp $$(docker ps -a | awk '/-browserextension/' | awk '{print $$1}'):/app/build/web ./test/e2e/record_replay/browser_extension/ktrecorder/ui/build/web

## reset e2e record/replay backend and show log
## rebuild flutter frontend for extension
.PHONY: e2e-rr
.ONESHELL:
e2e-rr: e2e-rr-reset-and-show-log

##
##—— PHP Compat —————————————————————————————————————————————————————————————————
##

## Check PHP 7.2 compatibility
.PHONY: php-compat
.ONESHELL:
php-compat:
	@./scripts/ci/php7-compatibility.sh

## Check PHP 7.2 compatibility
.PHONY: php7.2-compat
.ONESHELL:
php7.2-compat:
	@./scripts/ci/php7-compatibility.sh 7.2

## Check PHP 7.3 compatibility
.PHONY: php7.3-compat
.ONESHELL:
php7.3-compat:
	@./scripts/ci/php7-compatibility.sh 7.3

## Check PHP 7.4 compatibility
.PHONY: php7.4-compat
.ONESHELL:
php7.4-compat:
	@./scripts/ci/php7-compatibility.sh 7.4

##
##—— Confluent stack —————————————————————————————————————————————————————————————————
##

## confluent-up			:	Create and start Confluent Stack
##							Usage:
##							make confluent-up
##
.PHONY: confluent-up
.ONESHELL:
confluent-up: klicktipp-up confluent-pull
	@scripts/docker/docker-compose-confluent-stack.sh up

## confluent-stop			:	Stop Confluent Stack
##								Usage:
##								make confluent-stop
##
.PHONY: confluent-stop
.ONESHELL:
confluent-stop:
	@scripts/docker/docker-compose-confluent-stack.sh stop

## confluent-restart		:	Restart Confluent Stack
##								Usage:
##								make confluent-restart
##
.PHONY: confluent-restart
.ONESHELL:
confluent-restart: confluent-pull
	@scripts/docker/docker-compose-confluent-stack.sh restart

## confluent-pull			:	Pulls images for services defined in Compose file, but does not start the containers.
##								Usage:
##								make confluent-pull
##
.PHONY: confluent-pull
.ONESHELL:
confluent-pull:
	@scripts/docker/docker-compose-confluent-stack.sh pull

## confluent-down			:	Stop and remove Confluent Stack
##								(Only data stored in the Harddisk or other Volumes is preserved!)
##
##								Usage:
##								make confluent-down
##
.PHONY: confluent-down
.ONESHELL:
confluent-down:
	@scripts/docker/docker-compose-confluent-stack.sh down


##
##—— KT Tools —————————————————————————————————————————————————————————————————
##

## klicktipp-tools-up			:	Create and start KT Tools Stack
##									Usage:
##									make klicktipp-tools-up
##
.PHONY: klicktipp-tools-up
.ONESHELL:
klicktipp-tools-up: klicktipp-up klicktipp-tools-pull
	@scripts/docker/docker-compose-tools.sh up

## klicktipp-tools-stop			:	Stop KT Tools Stack
##									Usage:
##									make klicktipp-tools-stop
##
.PHONY: klicktipp-tools-stop
.ONESHELL:
klicktipp-tools-stop:
	@scripts/docker/docker-compose-tools.sh stop

## klicktipp-tools-restart		:	Restart KT Tools Stack
##									Usage:
##									make klicktipp-tools-restart
##
.PHONY: klicktipp-tools-restart
.ONESHELL:
klicktipp-tools-restart: klicktipp-tools-pull
	@scripts/docker/docker-compose-tools.sh restart

## klicktipp-tools-pull			:	Pulls images for services defined in Compose file, but does not start the containers.
##									Usage:
##									make KT Tools-pull
##
.PHONY: klicktipp-tools-pull
.ONESHELL:
klicktipp-tools-pull:
	@scripts/docker/docker-compose-tools.sh pull

## klicktipp-tools-down			:	Stop and remove KT Tools Stack
##									(Only data stored in the Harddisk or other Volumes is preserved!)
##
##									Usage:
##									make klicktipp-tools-down
##
.PHONY: klicktipp-tools-down
.ONESHELL:
klicktipp-tools-down:
	@scripts/docker/docker-compose-tools.sh down


##
##—— Misc ———————————————————————————————————————————————————————————————————————
##

## Print this help message
.PHONY: help
help:
	@echo "\033[33m \
		...............................................................................\n \
		.........██╗..██╗██╗.....██╗.██████╗██╗..██╗████████╗██╗██████╗.██████╗........\n \
		.........██║.██╔╝██║.....██║██╔════╝██║.██╔╝╚══██╔══╝██║██╔══██╗██╔══██╗.......\n \
		.........█████╔╝.██║.....██║██║.....█████╔╝....██║...██║██████╔╝██████╔╝.......\n \
		.........██╔═██╗.██║.....██║██║.....██╔═██╗....██║...██║██╔═══╝.██╔═══╝........\n \
		.........██║..██╗███████╗██║╚██████╗██║..██╗...██║...██║██║.....██║............\n \
		.........╚═╝..╚═╝╚══════╝╚═╝.╚═════╝╚═╝..╚═╝...╚═╝...╚═╝╚═╝.....╚═╝............\n \
		...............................................................................\n \
	\033[0m"
	@awk '{ \
			if ($$0 ~ /^.PHONY: [a-zA-Z\-\_\.0-9]+$$/) { \
				helpCommand = substr($$0, index($$0, ":") + 2); \
				if (helpMessage) { \
					printf "\033[36m%-40s\033[0m \t%s\n", \
						helpCommand, helpMessage; \
					helpMessage = ""; \
				} \
			} else if ($$0 ~ /^[a-zA-Z\-\_0-9.]+:/) { \
				helpCommand = substr($$0, 0, index($$0, ":")); \
				if (helpMessage) { \
					printf "\033[36m%-40s\033[0m %s\n", \
						helpCommand, helpMessage; \
					helpMessage = ""; \
				} \
			} else if ($$0 ~ /^##/) { \
				if (helpMessage) { \
					helpMessage = helpMessage"\n                                                  "substr($$0, 3); \
				} else { \
					helpMessage = substr($$0, 3); \
				} \
			} else { \
				if (helpMessage) { \
					printf "\n\033[33m%-80s\033[0m\n", \
          	helpMessage; \
				} \
				helpMessage = ""; \
			} \
		}' \
		$(MAKEFILE_LIST)

## Send test mail to fictional user over PHP and ssmtp
.PHONY: send-test-mail
send-test-mail:
	@./scripts/docker/exec-send-test-email.sh

## Adds KlickTipp's CA Cert to PKI. Adding Trusted Root Certificates to the computer
.PHONY: ca-add-cert
ca-add-cert:
	@./scripts/ci/manage-ca-pki.sh add

## Removes KlickTipp's CA Cert from PKI.
.PHONY: ca-remove-cert
ca-remove-cert:
	@./scripts/ci/manage-ca-pki.sh remove

.PHONY: reload-apache
reload-apache:
	@docker compose exec klicktipp bash -c 'cp -v docker/klicktipp/backend/drupal-php/rootfs/etc/apache2/includes/htaccess/* /etc/apache2/includes/htaccess/; apachectl -k graceful'

---

version: '3.8'

services:

  zookeeper:
    image: confluentinc/cp-zookeeper:6.2.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      confluent-net:
      kt-net:   # Use the klicktipp.git compose network, in order to reach the klicktipp php app


  kafka:
    image: confluentinc/cp-kafka:6.2.0
    depends_on:
      - zookeeper
    ports:
      # "`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-
      # An important note about accessing Kafka from clients on other machines:
      # -----------------------------------------------------------------------
      #
      # The config used here exposes port 9092 for _external_ connections to the broker
      # i.e. those from _outside_ the docker network. This could be from the host machine
      # running docker, or maybe further afield if you've got a more complicated setup.
      # If the latter is true, you will need to change the value 'localhost' in
      # KAFKA_ADVERTISED_LISTENERS to one that is resolvable to the docker host from those
      # remote clients
      #
      # For connections _internal_ to the docker network, such as from other services
      # and components, use kafka:29092.
      #
      # See https://rmoff.net/2018/08/02/kafka-listeners-explained/ for details
      # "`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-'"`-._,-
      #
      - 9092:9092
    networks:
      confluent-net:
      kt-net:   # Use the klicktipp.git compose network, in order to reach the klicktipp php app
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://${DOCKER_HOST_IP:-127.0.0.1}:9092
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://${DOCKER_HOST_IP:-127.0.0.1}:9092
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      # -v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v-v
      # Useful settings for development/laptop use - modify as needed for Prod
      # This one makes ksqlDB feel a bit more responsive when queries start running
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 100
    command:
      - bash
      - -c
      - |
        echo '127.0.0.1 kafka' >> /etc/hosts
        /etc/confluent/docker/run
        sleep infinity

  schema-registry:
    image: confluentinc/cp-schema-registry:6.2.0
    depends_on:
      - kafka
    ports:
      - 8082:8081
    networks:
      confluent-net:
      kt-net:   # Use the klicktipp.git compose network, in order to reach the klicktipp php app
    environment:
      SCHEMA_REGISTRY_HOST_NAME: schema-registry
      SCHEMA_REGISTRY_KAFKASTORE_CONNECTION_URL: zookeeper:2181

  kafka-connect:
    image: confluentinc/cp-kafka-connect-base:6.2.0
    depends_on:
      - schema-registry
      - opensearch
    ports:
      - 8083:8083
    networks:
      confluent-net:
      kt-net:   # Use the klicktipp.git compose network, in order to reach the klicktipp php app
    environment:
      CONNECT_BOOTSTRAP_SERVERS: "kafka:29092"
      CONNECT_REST_ADVERTISED_HOST_NAME: "kafka-connect"
      CONNECT_REST_PORT: 8083
      CONNECT_GROUP_ID: kafka-connect
      CONNECT_CONFIG_STORAGE_TOPIC: _kafka-connect-configs
      CONNECT_OFFSET_STORAGE_TOPIC: _kafka-connect-offsets
      CONNECT_STATUS_STORAGE_TOPIC: _kafka-connect-status
      CONNECT_KEY_CONVERTER: io.confluent.connect.avro.AvroConverter
      CONNECT_KEY_CONVERTER_SCHEMA_REGISTRY_URL: 'http://schema-registry:8081'
      CONNECT_VALUE_CONVERTER: io.confluent.connect.avro.AvroConverter
      CONNECT_VALUE_CONVERTER_SCHEMA_REGISTRY_URL: 'http://schema-registry:8081'
      CONNECT_LOG4J_ROOT_LOGLEVEL: "INFO"
      CONNECT_LOG4J_LOGGERS: "org.apache.kafka.connect.runtime.rest=WARN,org.reflections=ERROR"
      CONNECT_LOG4J_APPENDER_STDOUT_LAYOUT_CONVERSIONPATTERN: "[%d] %p %X{connector.context}%m (%c:%L)%n"
      CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR: "1"
      CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR: "1"
      CONNECT_STATUS_STORAGE_REPLICATION_FACTOR: "1"
        # If you want to use the Confluent Hub installer to d/l component, but make them available
        # when running this offline, spin up the stack once and then run :
        #   docker cp kafka-connect:/usr/share/confluent-hub-components ./connectors
        #   mv ./connectors/confluent-hub-components/* ./connectors
      #   rm -rf ./connectors/confluent-hub-components
      #CONNECT_PLUGIN_PATH: '/usr/share/java,/usr/share/confluent-hub-components/,/connectors/'
    #volumes:
    #  - $PWD/connectors:/connectors
    # In the command section, $ are replaced with $$ to avoid the error 'Invalid interpolation format for "command" option'
    command:
      - bash
      - -c
      - |
        # Nasty hack for ECS
        echo '127.0.0.1 kafka-connect' >> /etc/hosts
        #
        echo "Installing connector plugins"
        confluent-hub install --no-prompt debezium/debezium-connector-mysql:1.6.0
        # confluent-hub install --no-prompt confluentinc/kafka-connect-datagen:0.5.0
        confluent-hub install --no-prompt confluentinc/kafka-connect-elasticsearch:11.1.1
        confluent-hub install --no-prompt confluentinc/connect-transforms:1.4.1
        #
        echo "Launching Kafka Connect worker"
        /etc/confluent/docker/run &
        #
        echo "Waiting for Kafka Connect to start listening on localhost ⏳"
        while : ; do
          curl_status=$$(curl -s -o /dev/null -w %{http_code} http://localhost:8083/connectors)
          echo -e $$(date) " Kafka Connect listener HTTP state: " $$curl_status " (waiting for 200)"
          if [ $$curl_status -eq 200 ] ; then
            break
          fi
          sleep 5
        done
        #
        echo "Creating mysql source connector"
        curl -XPUT 'http://localhost:8083/connectors/klicktipp_email_mysql_source/config' -H 'Content-Type: application/json' -d'
            {
                "connector.class":"io.debezium.connector.mysql.MySqlConnector",
                "database.hostname":"percona.kt-net",
                "database.port":"3306",
                "database.user":"debezium",
                "database.password":"dbz",
                "database.server.name":"klicktipp",
                "table.whitelist":"klicktipp.drupal_subscription",
                "database.history.kafka.bootstrap.servers":"kafka:29092",
                "database.history.kafka.topic":"subscription.history" ,
                "include.schema.changes":"false",
                "transforms":"unwrap,reroute,includeOnlyEmail,replaceKey,whitelistFields",
                "transforms.unwrap.type":"io.debezium.transforms.ExtractNewRecordState",
                "transforms.unwrap.drop.tombstones":"false",
                "transforms.unwrap.delete.handling.mode":"none",
                "transforms.reroute.type":"io.debezium.transforms.ByLogicalTableRouter",
                "transforms.reroute.topic.regex":"(.*)",
                "transforms.reroute.topic.replacement":"klicktipp_emails",
                "transforms.reroute.key.enforce.uniqueness":"false",
                "transforms.includeOnlyEmail.type":"io.confluent.connect.transforms.Filter$$Key",
                "transforms.includeOnlyEmail.filter.type":"include",
                "transforms.includeOnlyEmail.missing.or.null.behavior":"fail",
                "transforms.includeOnlyEmail.filter.condition":"$$[?(@.SubscriptionType == 1)]",
                "transforms.replaceKey.type":"org.apache.kafka.connect.transforms.ReplaceField$$Key",
                "transforms.replaceKey.blacklist":"SubscriptionType",
                "transforms.replaceKey.renames":"RelOwnerUserID:user_id,ContactInfo:email",
                "transforms.whitelistFields.type":"org.apache.kafka.connect.transforms.ReplaceField$$Value",
                "transforms.whitelistFields.whitelist":"RelOwnerUserID,RelSubscriberID,ContactInfo",
                "transforms.whitelistFields.renames":"RelOwnerUserID:user_id,RelSubscriberID:subscriber_id,ContactInfo:email",
                "value.converter":"io.confluent.connect.avro.AvroConverter",
                "value.converter.schema.registry.url":"http://schema-registry:8081",
                "key.converter": "org.apache.kafka.connect.storage.StringConverter"
            }'
        #
        echo "Creating opensearch sink connector"
        curl -XPUT 'http://localhost:8083/connectors/klicktipp_email_es_sink/config' -H 'Content-Type: application/json' -d'
            {
                "connector.class":"io.confluent.connect.elasticsearch.ElasticsearchSinkConnector",
                "connection.url":"http://opensearch:9200",
                "type.name":"_doc",
                "behavior.on.malformed.documents":"warn",
                "errors.tolerance":"all",
                "errors.log.enable":"true",
                "errors.log.include.messages":"true",
                "topics":"klicktipp_emails",
                "key.ignore":"false",
                "schema.ignore":"false",
                "behavior.on.null.values":"delete",
                "transforms":"unwrap",
                "transforms.unwrap.type":"io.debezium.transforms.ExtractNewRecordState",
                "transforms.unwrap.drop.tombstones":"false",
                "transforms.unwrap.delete.handling.mode":"none",
                "key.converter": "org.apache.kafka.connect.storage.StringConverter"
            }'
        sleep infinity
  opensearch:
    image: opensearchproject/opensearch:1.0.0
    ports:
      - 9200:9200
    networks:
      confluent-net:
      kt-net:
    ulimits:
      nofile:
        soft: 65535
        hard: 65535
      memlock:
        soft: -1
        hard: -1
    environment:
      plugins.security.disabled: "true"
      ES_JAVA_OPTS: "-Xms1g -Xmx1g"
      discovery.type: "single-node"
      node.store.allow_mmap: "false"
      compatibility.override_main_response_version: "true"
    command:
      - bash
      - -c
      - |
        /usr/share/opensearch/opensearch-docker-entrypoint.sh &
        echo "Waiting for Opensearch to start ⏳"
        while [ $$(curl -s -o /dev/null -w %{http_code} http://localhost:9200/) -ne 200 ] ; do
          echo -e $$(date) " Opensearch listener HTTP state: " $$(curl -s -o /dev/null -w %{http_code} http://localhost:9200/) " (waiting for 200)"
          sleep 5
        done

        curl -s -XPUT "http://localhost:9200/_template/kafkaconnect/" -H 'Content-Type: application/json' -d'
                  {
                    "template": "*",
                    "settings": { "number_of_shards": 1, "number_of_replicas": 0 },
                    "mappings": { "dynamic_templates": [ { "dates": { "match": "*_TS", "mapping": { "type": "date" } } } ]  }
                  }'
        #
        echo "Create ingest pipeline for localpart extraction"
        curl -XPUT 'http://localhost:9200/_ingest/pipeline/extract-localpart' -H 'Content-Type: application/json' -d'
                  {
                    "description": "Extracts localpart and copies it into tokenized localpart",
                    "processors": [
                      {
                        "gsub": {
                          "field": "email",
                          "pattern": "@[^@]*$$",
                          "replacement": "",
                          "target_field": "localpart"
                        },
                        "set": {
                          "field": "localpart-tokenized",
                          "value": "{{localpart}}"
                        }
                      },
                      {
                        "gsub": {
                          "field": "email",
                          "pattern": "^.*@",
                          "replacement": "",
                          "target_field": "domain"
                        }
                      }
                    ]
                  }'
        #
        echo "Create index for email addresses"
        curl -XPUT 'http://localhost:9200/klicktipp_emails' -H 'Content-Type: application/json' -d'
                  {
                    "settings": {
                      "index": {
                        "default_pipeline": "extract-localpart"
                      }
                    },
                    "mappings": {
                      "properties": {
                        "email": {
                          "type": "keyword"
                        },
                        "localpart": {
                          "type": "keyword"
                        },
                        "localpart-tokenized": {
                          "type": "text",
                          "analyzer": "simple"
                        },
                        "domain": {
                          "type": "keyword"
                        },
                        "user_id": {
                          "type": "keyword"
                        },
                        "subscriber_id": {
                          "type": "keyword"
                        }
                      }
                    }
                  }'

        sleep infinity

networks:
  confluent-net:
  kt-net:
    name: kt-net  # This is the docker compose klicktipp's second network
    external: true

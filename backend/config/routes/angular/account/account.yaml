# Angular api routes for account

ipa.kt-dashboard:
    methods: [ POST ]
    path: /ipa/kt-dashboard/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 0
        _format: json
    controller: App\Controller\Api\AccountDashboardController::overview

ipa.kt-cache-clear:
    methods: [ POST ]
    path: /ipa/kt-cache-clear/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 0
        _format: json
    controller: App\Controller\Api\AccountStatsController::cacheClear

ipa.kt-cancellation/overview:
    methods: [ POST ]
    path: /ipa/kt-cancellation/overview/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 0
        _format: json
    controller: App\Klicktipp\Cancellation\Controller\CancellationOverviewController::overview
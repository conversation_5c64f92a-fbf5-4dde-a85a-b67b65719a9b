ipa.kt-subscriber.feedback-unsubscription.index:
    methods: [ POST ]
    path: /ipa/kt-subscriber/feedback-unsubscription/index/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 4 # Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
        _format: json
    controller: App\Klicktipp\Subscriber\Feedback\Unsubscription\Controller\SubscriberUnsubscriptionFeedbackIndexController

ipa.kt-subscriber.feedback-unsubscription.overview:
    methods: [ POST ]
    path: /ipa/kt-subscriber/feedback-unsubscription/overview/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 4 # Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
        _format: json
    controller: App\Klicktipp\Subscriber\Feedback\Unsubscription\Controller\SubscriberUnsubscriptionFeedbackOverviewController
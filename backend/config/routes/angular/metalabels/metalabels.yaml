ipa.kt-metalabel.overview:
    methods: [ POST ]
    path: /ipa/kt-metalabel/overview/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 5 # Subaccount::SUBACCOUNT_WEBSITE_BUILDER
        _format: json
    controller: App\Klicktipp\MetaLabel\Controller\MetaLabelOverviewController

ipa.kt-metalabel.settings-retrieve:
    methods: [ POST ]
    path: /ipa/kt-metalabel/settings-retrieve/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 5 # Subaccount::SUBACCOUNT_WEBSITE_BUILDER
        _format: json
    controller: App\Klicktipp\MetaLabel\Controller\MetaLabelRetrieveController

ipa.kt-metalabel.create:
    methods: [ POST ]
    path: /ipa/kt-metalabel/create/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 5 # Subaccount::SUBACCOUNT_WEBSITE_BUILDER
        _format: json
    controller: App\Klicktipp\MetaLabel\Controller\MetaLabelCreateController

ipa.kt-metalabel.settings-save:
    methods: [ POST ]
    path: /ipa/kt-metalabel/settings-save/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 5 # Subaccount::SUBACCOUNT_WEBSITE_BUILDER
        _format: json
    controller: App\Klicktipp\MetaLabel\Controller\MetaLabelSaveController

ipa.kt-metalabel.delete:
    methods: [ POST ]
    path: /ipa/kt-metalabel/delete/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        forUserId: 'me'
        subAccountRole: 5 # Subaccount::SUBACCOUNT_WEBSITE_BUILDER
        _format: json
    controller: App\Klicktipp\MetaLabel\Controller\MetaLabelDeleteController

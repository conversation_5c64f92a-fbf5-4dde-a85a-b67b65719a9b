# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            Redis $domainWhitelistRedis: '@redis.domain_whitelist'
            Redis $rateLimitRedis: '@redis.rate_limit'
            Redis $oidcTokenCacheRedis: '@redis.oidc_token_cache'

    _instanceof:
        App\Klicktipp\LandingPage\Validators\LandingPageValidatorInterface:
            tags: ['klicktipp.landing_page_validator']
        App\Klicktipp\Email\Validators\EmailValidatorInterface:
            tags: [ 'klicktipp.email_validator' ]
        App\Klicktipp\Sms\Validators\SmsValidatorInterface:
            tags: [ 'klicktipp.sms_validator' ]
        App\Klicktipp\Campaign\Validators\CampaignValidatorInterface:
            tags: [ 'klicktipp.campaign_validator' ]
        App\JsonRpc\RemoteCallableServiceInterface:
            tags: ['klicktipp.jsonrpc.remote_callable_service']
        App\Klicktipp\Tag\Validators\TagValidatorInterface:
            tags: [ 'klicktipp.tag_validator' ]
        App\Klicktipp\SmartLink\Validators\SmartLinkValidatorInterface:
            tags: [ 'klicktipp.smartlink_validator' ]
        App\Klicktipp\Customfield\Validators\CustomfieldValidatorInterface:
            tags: [ 'klicktipp.customfield_validator' ]
        App\Klicktipp\MetaLabel\Validators\MetaLabelValidatorInterface:
            tags: [ 'klicktipp.metalabel_validator' ]
        App\Klicktipp\TaggingPixel\Validators\TaggingPixelsValidatorInterface:
            tags: [ 'klicktipp.tagging_pixel_validator' ]
        App\Klicktipp\Countdown\Validators\CountdownValidatorInterface:
            tags: [ 'klicktipp.countdown_validator' ]
        App\Klicktipp\ApiKey\Validators\ApiKeyValidatorInterface:
            tags: [ 'klicktipp.apikey_validator' ]
        App\Klicktipp\Queue\KeycloakEventQueues\KeycloakEventMessageInterface:
            tags: [ 'klicktipp.keycloak_event_queue.message' ]
        App\Klicktipp\OptInProcess\Validators\OptInProcessValidatorInterface:
            tags: [ 'klicktipp.optin_process_validator' ]
        App\Klicktipp\RequestByMail\Validators\RequestByMailValidatorInterface:
            tags: [ 'klicktipp.requestbymail_validator' ]
        App\Klicktipp\Validators\EmailAddress\EmailAddressValidatorInterface:
            tags: [ 'klicktipp.emailaddress_validator' ]
        App\Klicktipp\RequestSms\Validators\RequestSmsValidatorInterface:
            tags: [ 'klicktipp.request_sms_validator' ]
        App\Klicktipp\Validators\SpamScore\SpamScoreValidatorInterface:
            tags: [ 'klicktipp.spamscore_validator' ]
        App\Klicktipp\Signature\Validators\SignatureValidatorInterface:
            tags: [ 'klicktipp.signature_validator' ]
        App\Klicktipp\UserSettings\UtmParameter\Validators\UtmParameterValidatorInterface:
            tags: [ 'klicktipp.utm_settings_validator' ]

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
            - '../src/Tests/'

    App\Controller\:
        resource: '../src/Controller'
        tags: ['controller.service_arguments']

    # here we overwrite constructor argument of security water (by default 'ROLE_').
    # This way we cause symfony voter to accept roles without ROLE_-prefix (drupal compatibility)
    security.access.simple_role_voter:
      class: Symfony\Component\Security\Core\Authorization\Voter\RoleVoter
      arguments: ['']

    App\Klicktipp\AccountManager:
      public: true

    App\Klicktipp\UserProvider:
        public: true

    App\Klicktipp\Translator:
        public: true

    App\Drupal\Variables:
        public: true
        calls:
            - initialize: []

    App\Digistore\DigistoreApi:
        factory: [null, 'connect']
        arguments:
            $api_key: '%env(KLICKTIPP_DIGISTORE_API_KEY)%'

    App\Klicktipp\Authenticator:
        public: true

    App\Security\Http\Authenticator\PublicApiAuthenticator:
        public: true

    App\ArgumentResolver\ArrayValueResolver:
        tags:
            - { name: controller.argument_value_resolver, priority: 50 }

    App\Controller\KeycloakUserMigrationController:
        arguments:
            $apiKey: '%env(KLICKTIPP_KEYCLOAK_MIGRATION_API_KEY)%'

    # Allow usage in migrated Drupal methods.
    Symfony\Component\Security\Core\Security:
        public: true

    App\Klicktipp\LandingPage\LandingPageValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.landing_page_validator

    App\Klicktipp\Email\EmailValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.email_validator

    App\Klicktipp\Sms\SmsValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.sms_validator

    App\Klicktipp\Campaign\CampaignValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.campaign_validator

    App\Klicktipp\Tag\TagValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.tag_validator

    App\Klicktipp\SmartLink\SmartLinkValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.smartlink_validator

    App\Klicktipp\Customfield\CustomfieldValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.customfield_validator

    App\Klicktipp\OptInProcess\OptInProcessValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.optin_process_validator

    App\Klicktipp\MetaLabel\MetaLabelValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.metalabel_validator

    App\Klicktipp\ApiKey\ApiKeyValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.apikey_validator

    App\Klicktipp\RequestSms\RequestSmsValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.request_sms_validator

    App\Klicktipp\TaggingPixel\TaggingPixelsValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.tagging_pixel_validator

    App\Klicktipp\Countdown\CountdownValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.countdown_validator

    App\Klicktipp\RequestByMail\RequestByMailValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.requestbymail_validator

    App\Klicktipp\Validators\EmailAddress\EmailAddressValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.emailaddress_validator

    App\Klicktipp\Validators\SpamScore\SpamScoreValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.spamscore_validator

    App\Klicktipp\Signature\SignatureValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.signature_validator

    App\Klicktipp\UserSettings\UtmParameter\UtmParameterValidator:
        arguments:
            $validators: !tagged_iterator klicktipp.utm_settings_validator

    redis.default:
        class: Redis
        lazy: true
        factory: [ '@App\Klicktipp\Redis\RedisFactory', 'getRedis' ]
        arguments:
            $connectionString: '%env(KLICKTIPP_REDIS_DSN)%'

    redis.domain_whitelist:
        class: Redis
        lazy: true
        factory: [ '@App\Klicktipp\Redis\RedisFactory', 'getRedis' ]
        arguments:
            $connectionString: '%env(KLICKTIPP_REDIS_DOMAIN_WHITELIST_DSN)%'

    redis.rate_limit:
        class: Redis
        lazy: true
        factory: [ '@App\Klicktipp\Redis\RedisFactory', 'getRedis' ]
        arguments:
            $connectionString: '%env(KLICKTIPP_REDIS_RATE_LIMIT_DSN)%'

    redis.oidc_token_cache:
        class: Redis
        lazy: true
        factory: [ '@App\Klicktipp\Redis\RedisFactory', 'getRedis' ]
        arguments:
            $connectionString: '%env(KLICKTIPP_REDIS_OIDC_TOKEN_CACHE_DSN)%'

    Redis:
        alias: redis.default

    App\JsonRpc\JsonRpcServer:
        arguments:
            $services: !tagged_iterator klicktipp.jsonrpc.remote_callable_service

    App\JsonRpc\Generator\OpenRpcGenerator:
        arguments:
            $services: !tagged_iterator klicktipp.jsonrpc.remote_callable_service

    App\JsonRpc\Framework\JsonRpcSymfonyBridge:
        public: true

    App\Datadog\DogStatsD:
        arguments:
            $config:
                host: '%env(KLICKTIPP_DATADOG_AGENT_HOST)%'
                port: '%env(KLICKTIPP_DATADOG_AGENT_PORT)%'

    App\Security\Oidc\OidcClient:
        arguments:
            $clientID: '%env(KLICKTIPP_OAUTH2_CLIENT_ID)%'
            $clientSecret: '%env(KLICKTIPP_OAUTH2_CLIENT_SECRET)%'

    App\Security\Oidc\OidcConfigurationProvider:
        arguments:
            $configurationUrl: '%env(KLICKTIPP_OIDC_CONFIGURATION_SERVER)%/realms/%env(KLICKTIPP_KEYCLOAK_REALM_NAME)%/%env(KLICKTIPP_OIDC_CONFIGURATION_WELL_KNOWN_PATH)%'

    App\Security\Keycloak\KeycloakApi:
        arguments:
            $apiUri: '%env(KLICKTIPP_KEYCLOAK_API_URI)%/%env(KLICKTIPP_KEYCLOAK_REALM_NAME)%'
            $adminApiUri: '%env(KLICKTIPP_KEYCLOAK_ADMIN_API_URI)%/%env(KLICKTIPP_KEYCLOAK_REALM_NAME)%'
            $clientID: '%env(KLICKTIPP_OAUTH2_CLIENT_ID)%'

    App\Klicktipp\AMQP\AmqpConnection:
        arguments:
            $host: '%env(KLICKTIPP_RABBITMQ_HOST)%'
            $port: '%env(KLICKTIPP_RABBITMQ_PORT)%'
            $user: '%env(KLICKTIPP_RABBITMQ_USER)%'
            $password: '%env(KLICKTIPP_RABBITMQ_PASS)%'
            $vhost: '%env(KLICKTIPP_RABBITMQ_VHOST)%'

    App\Klicktipp\Queue\KeycloakEventQueues\AmqpKeycloakEventsQueues:
        arguments:
            $messages: !tagged_iterator klicktipp.keycloak_event_queue.message
            $realmName: '%env(KLICKTIPP_KEYCLOAK_REALM_NAME)%'

    App\Messenger\KeycloakEvents\KeycloakEventsMessageSerializer:
        arguments:
            $messages: !tagged_iterator klicktipp.keycloak_event_queue.message

    App\Klicktipp\ErrorPage\KeyProvider\KeyProvider:
        arguments:
            $privateKey: '%env(KLICKTIPP_ERROR_PAGE_PRIVATE_KEY)%'

    App\Klicktipp\ErrorPage\ErrorPageManager:
        arguments:
            $errorPageUrl: '%env(KLICKTIPP_ERROR_PAGE_URL)%'

    MailSystemInterface:
        alias: App\Klicktipp\Mail\QueueMailSystem
        public: true

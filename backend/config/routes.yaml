imports:
  # import angular api routes
  resource: routes
  type:     directory

app.dashboard_redirect:
    methods: [ GET ]
    path: /dashboard
    controller: App\Controller\DashboardRedirectController

# authentication
app.authentication.login:
  methods: [ GET ]
  path: /auth/login
  controller: App\Controller\AuthenticationController::login

app.authentication.login_callback:
    methods: [ GET ]
    path: /auth/login/callback
    controller: App\Controller\AuthenticationController::callback

app.authentication.logout:
  methods: [ GET ]
  path: /auth/logout
  controller: App\Controller\AuthenticationController::logout

app.authentication.logout_confirm:
  methods: [ GET ]
  path: /auth/logout/confirm
  controller: App\Controller\AuthenticationController::logoutConfirm

app.authentication.account_console_redirect:
  methods: [ GET ]
  path: /auth/account-console
  controller: App\Controller\AuthenticationController::redirectToAccountConsole

# emails general
ipa.email.settings-retrieve:
  methods: [ POST ]
  path: /ipa/kt-email/settings-retrieve/{forUserId}/{id}/{campaignId}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    campaignId: 0
    id: 0
    _format: json
  controller: App\Controller\Api\EmailDefaultController::settingsRetrieve

ipa.email.spam-score:
  methods: [ POST ]
  path: /ipa/kt-email/spam-score/{forUserId}/{id}/{campaignId}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    campaignId: 0
    _format: json
  controller: App\Controller\Api\EmailDefaultController::spamScoreRetrieve

ipa.email.preview:
  methods: [ POST ]
  path: /ipa/kt-email/preview/{forUserId}/{id}/{campaignId}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    campaignId: 0
    _format: json
  controller: App\Controller\Api\EmailDefaultController::previewSend

# automation email dialogs

ipa.email-automation.delete:
  methods: [ POST ]
  path: /ipa/kt-email-automation/delete/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailAutomationEmailController::delete

ipa.email-automation.dependencies:
  methods: [ POST ]
  path: /ipa/kt-email-automation/dependencies/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailAutomationEmailController::dependencies

# automation sms dialogs

ipa.sms-automation.delete:
  methods: [ POST ]
  path: /ipa/kt-sms-automation/delete/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailAutomationSmsController::delete

ipa.sms-automation.dependencies:
  methods: [ POST ]
  path: /ipa/kt-sms-automation/dependencies/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailAutomationSmsController::dependencies

# automation notification email dialogs
ipa.email-notification.delete:
  methods: [ POST ]
  path: /ipa/kt-email-notification/delete/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailAutomationNotificationEmailController::delete

ipa.email-notification.dependencies:
  methods: [ POST ]
  path: /ipa/kt-email-notification/dependencies/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailAutomationNotificationEmailController::dependencies

# automation notification sms dialogs

ipa.sms-notification.delete:
  methods: [ POST ]
  path: /ipa/kt-sms-notification/delete/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailAutomationSmsController::delete

ipa.sms-notification.dependencies:
  methods: [ POST ]
  path: /ipa/kt-sms-notification/dependencies/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailAutomationSmsController::dependencies

# campaign email dialogs

ipa.email-campaign.settings-retrieve:
  methods: [ POST ]
  path: /ipa/kt-email-campaign/settings-retrieve/{forUserId}/{id}/{campaignId}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    id: 0
    _format: json
  controller: App\Controller\Api\EmailNewsletterEmailController::settingsRetrieve

ipa.email-campaign.settings-save:
  methods: [ POST ]
  path: /ipa/kt-email-campaign/settings-save/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailNewsletterEmailController::settingsSave

# campaign email dialogs

ipa.sms-campaign.settings-retrieve:
  methods: [ POST ]
  path: /ipa/kt-sms-campaign/settings-retrieve/{forUserId}/{id}/{campaignId}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    id: 0
    _format: json
  controller: App\Controller\Api\EmailNewsletterSmsController::settingsRetrieve

ipa.sms-campaign.settings-save:
  methods: [ POST ]
  path: /ipa/kt-sms-campaign/settings-save/{forUserId}/{id}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\EmailNewsletterSmsController::settingsSave

# email editor
ipa.email.personalize:
    methods: [ POST ]
    path: /ipa/kt-email/personalize.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailDefaultController::personalize

ipa.email.signature:
    methods: [ POST ]
    path: /ipa/kt-email/signature-index.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailDefaultController::signatureIndex

ipa.email.editor_subject_save:
    methods: [ POST ]
    path: /ipa/kt-email/editor-subject-save/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
        forUserId: 'me'
        subAccountRole: 2 # Subaccount::SUBACCOUNT_TEXTER
    controller: App\Controller\Api\EmailDefaultController::editorSubjectSave

ipa.email.editor_subject_generate:
    methods: [ POST ]
    path: /ipa/kt-email/editor-subject-generate/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
        forUserId: 'me'
        subAccountRole: 2 # Subaccount::SUBACCOUNT_TEXTER
    controller: App\Controller\Api\EmailDefaultController::editorSubjectGenerate

ipa.email.editor_import:
    methods: [ POST ]
    path: /ipa/kt-email/editor-import/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
        forUserId: 'me'
        subAccountRole: 2 # Subaccount::SUBACCOUNT_TEXTER
    controller: App\Controller\Api\EmailDefaultController::editorImport

ipa.email.editor_condition:
    methods: [ POST ]
    path: /ipa/kt-email/editor-condition/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
        forUserId: 'me'
        subAccountRole: 2 # Subaccount::SUBACCOUNT_TEXTER
    controller: App\Controller\Api\EmailDefaultController::editorCondition

ipa.email.update:
    methods: [ PUT ]
    path: /ipa/kt-email/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailDefaultController::update

ipa.email.index_advanced:
    methods: [ POST ]
    path: /ipa/kt-email/index_advanced.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailDefaultController::indexAdvanced

ipa.email.spam_score:
    methods: [ POST ]
    path: /ipa/kt-email/spamscore.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailDefaultController::spamScore

# im Cockpit wird nur kt-automation-email verwendet
ipa.email.send_preview:
    methods: [ POST ]
    path: /ipa/kt-email/sendpreview.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailDefaultController::sendPreview

# im Cockpit wird nur kt-automation-email verwendet
ipa.email.send_preview_sms:
    methods: [ POST ]
    path: /ipa/kt-email/sendpreview.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailDefaultController::sendPreviewSms

# email editor automation
# TODO CHECK IF NEEDED FOR EMAIL EDITOR OR ONLY COCKPIT => REFACTOR

# im Cockpit wird nur kt-automation-email verwendet
ipa.email_automation.spam_score:
    methods: [ POST ]
    path: /ipa/kt-automation-email/spamscore.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailAutomationController::spamScore

# im Cockpit wird nur kt-automation-email verwendet
ipa.email_automation.sendpreview:
    methods: [ POST ]
    path: /ipa/kt-automation-email/sendpreview.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailAutomationController::sendPreview

# im Cockpit wird nur kt-automation-email verwendet
ipa.email_automation.gmailpreview:
    methods: [ POST ]
    path: /ipa/kt-automation-email/gmailpreview.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailAutomationController::gmailPreview

# email editor automation sms
# TODO CHECK IF NEEDED FOR EMAIL EDITOR OR ONLY COCKPIT => REFACTOR
# im Cockpit wird nur kt-automation-email verwendet
ipa.email_automation_sms.sendpreview:
    methods: [ POST ]
    path: /ipa/kt-automation-sms/sendpreview.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailAutomationController::sendPreviewSms


# email editor notification
# TODO CHECK IF NEEDED FOR EMAIL EDITOR OR ONLY COCKPIT => REFACTOR

ipa.email_notification.spam_score:
    methods: [ POST ]
    path: /ipa/kt-notification-email/spamscore.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailNotificationController::spamScore

ipa.email_notification.sendpreview:
    methods: [ POST ]
    path: /ipa/kt-notification-email/sendpreview.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailNotificationController::sendPreview

# email editor notification sms
# TODO CHECK IF NEEDED FOR EMAIL EDITOR OR ONLY COCKPIT => REFACTOR

ipa.email_notification_sms.sendpreviewSms:
    methods: [ POST ]
    path: /ipa/kt-notification-sms/sendpreview.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\EmailNotificationController::sendPreviewSms

# import
ipa.import.overview:
  methods: [ POST ]
  path: /ipa/kt-import/overview/{forUserId}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\ImportController::overview

ipa.import.create:
    methods: [ POST ]
    path: /ipa/kt-import/{forUserId}/create.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::create

ipa.import.settings-retrieve:
    methods: [ POST ]
    path: /ipa/kt-import/settings-retrieve/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::settingsRetrieve

ipa.import.file_info:
    methods: [ POST ]
    path: /ipa/kt-import/fileinfo/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::fileInfo

ipa.import.validate_field:
    methods: [ POST ]
    path: /ipa/kt-import/validate-field/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::validateField

ipa.import.detect_format:
    methods: [ POST ]
    path: /ipa/kt-import/detect-format/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::detectFormat

ipa.import.assign-fields:
    methods: [ POST ]
    path: /ipa/kt-import/assign-fields/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::assignFields

ipa.import.import:
    methods: [ POST ]
    path: /ipa/kt-import/import/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::import

ipa.import.push:
    methods: [ POST ]
    path: /ipa/kt-import/push/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::push

ipa.import.cancel:
    methods: [ POST ]
    path: /ipa/kt-import/cancel/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::cancel

ipa.import.progress:
    methods: [ POST ]
    path: /ipa/kt-import/progress/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::progress

ipa.import.delete:
    methods: [ POST ]
    path: /ipa/kt-import/delete/{forUserId}/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\ImportController::delete

# calendar
ipa.calendar.create:
    methods: [ POST ]
    path: /ipa/kt-calendar.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\CalendarController::create

ipa.calendar.update:
    methods: [ PUT ]
    path: /ipa/kt-calendar/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\CalendarController::update

ipa.calendar.get:
    methods: [ GET ]
    path: /ipa/kt-calendar/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\CalendarController::retrieve


ipa.calendar.index_advanced:
    methods: [ POST ]
    path: /ipa/kt-calendar/index_advanced.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\CalendarController::indexAdvanced

ipa.calendar.dependencies:
    methods: [ POST ]
    path: /ipa/kt-calendar/dependencies.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\CalendarController::dependencies

ipa.calendar.delete:
    methods: [ DELETE ]
    path: /ipa/kt-calendar/{id}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\CalendarController::delete

ipa.calendar.get_dates:
    methods: [ POST ]
    path: /ipa/kt-calendar/calendar.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\CalendarController::getDates

### tag

ipa.tag.index_advanced:
    methods: [ POST ]
    path: /ipa/kt-tag-all/index-advanced/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\TagController::index_advanced

### metalabels

ipa.meta-labels.index_advanced:
    methods: [ POST ]
    path: /ipa/kt-meta-labels/index-advanced/{forUserId}.{_format}
    requirements:
        _format: json|xml|yaml
    defaults:
        _format: json
    controller: App\Controller\Api\MetaLabelsController::index_advanced

### Smart Copy Writer

ipa.smart-copy-writer:
  methods: [ POST ]
  path: /ipa/kt-smart-copy-writer/{forUserId}.{_format}
  requirements:
    _format: json|xml|yaml
  defaults:
    _format: json
  controller: App\Controller\Api\ChatGPTController::smartCopyWriter

# TBD not implemented yet.
#api.v1.accounts.index:
#    methods: [ GET ]
#    path: /public/api/v1/accounts
#    controller: App\Controller\PublicApi\AccountController::index

api.v1.accounts.login:
    methods: [ POST ]
    path: /public/api/v1/accounts/login
    controller: App\Controller\PublicApi\AccountController::login

api.v1.accounts.logout:
    methods: [ POST ]
    path: /public/api/v1/accounts/logout
    controller: App\Controller\PublicApi\AccountController::logout

api.v1.accounts.is_authenticated:
    methods: [ POST ]
    path: /public/api/v1/accounts/is_authenticated
    controller: App\Controller\PublicApi\AccountController::isAuthenticatedAction

api.v1.lists.index:
    methods: [ GET ]
    path: /public/api/v1/lists
    controller: App\Controller\PublicApi\ListController::index

api.v1.custom_fields.index:
    methods: [ GET ]
    path: /public/api/v1/custom_fields
    controller: App\Controller\PublicApi\CustomFieldController::index

api.v1.tags.index:
    methods: [ GET ]
    path: /public/api/v1/tags
    controller: App\Controller\PublicApi\TagController::index

api.v1.zapier.dummy_subscriber.index:
    methods: [ GET ]
    path: /public/api/v1/zapier/dummy_subscriber
    controller: App\Controller\PublicApi\ZapierController::index

api.v1.subscribers.tag:
    methods: [ POST ]
    path: /public/api/v1/subscribers/tag
    controller: App\Controller\PublicApi\SubscriberController::tagAction

api.v1.subscribers.untag:
    methods: [ POST ]
    path: /public/api/v1/subscribers/untag
    controller: App\Controller\PublicApi\SubscriberController::untagAction

# TBD not implemented yet.
#api.v1.subscribers.index:
#    methods: [ GET ]
#    path: /public/api/v1/subscribers
#    controller: App\Controller\PublicApi\SubscriberController::index

api.v1.subscribers.subscribe:
    methods: [ POST ]
    path: /public/api/v1/subscribers/subscribe
    controller: App\Controller\PublicApi\SubscriberController::subscribeAction

api.v1.subscribers.unsubscribe:
    methods: [ POST ]
    path: /public/api/v1/subscribers/unsubscribe
    controller: App\Controller\PublicApi\SubscriberController::unsubscribeAction

### Legacy API - list

api.legacy.list.index:
    methods: [ GET ]
    path: /api/list.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiListController::index

api.legacy.list.retrieve:
    methods: [ GET ]
    path: /api/list/{listid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiListController::retrieve
    requirements:
        listid: \d+

api.legacy.list.create:
    methods: [ POST ]
    path: /api/list.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiListController::create

api.legacy.list.update:
    methods: [ PUT ]
    path: /api/list/{listid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiListController::update
    requirements:
        listid: \d+

api.legacy.list.delete:
    methods: [ DELETE ]
    path: /api/list/{listid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiListController::delete
    requirements:
        listid: \d+

api.legacy.list.redirect:
    methods: [ POST ]
    path: /api/list/redirect.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiListController::redirectionurl

### Legacy API - tag

api.legacy.tag.index:
    methods: [ GET ]
    path: /api/tag.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiTagController::index

api.legacy.tag.retrieve:
    methods: [ GET ]
    path: /api/tag/{tagid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiTagController::retrieve
    requirements:
        tagid: \d+

api.legacy.tag.create:
    methods: [ POST ]
    path: /api/tag.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiTagController::create

api.legacy.tag.update:
    methods: [ PUT ]
    path: /api/tag/{tagid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiTagController::update
    requirements:
        tagid: \d+

api.legacy.tag.delete:
    methods: [ DELETE ]
    path: /api/tag/{tagid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiTagController::delete
    requirements:
        tagid: \d+

### Legacy API - subscriber

api.legacy.subscriber.index:
    methods: [ GET ]
    path: /api/subscriber.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::index

api.legacy.subscriber.retrieve:
    methods: [ GET ]
    path: /api/subscriber/{subscriberid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::retrieve

api.legacy.subscriber.create:
    methods: [ POST ]
    path: /api/subscriber.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::create

api.legacy.subscriber.unsubscribe:
    methods: [ POST ]
    path: /api/subscriber/unsubscribe.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::unsubscribe

api.legacy.subscriber.tag:
    methods: [ POST ]
    path: /api/subscriber/tag.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::tag

api.legacy.subscriber.untag:
    methods: [ POST ]
    path: /api/subscriber/untag.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::untag

api.legacy.subscriber.update:
    methods: [ PUT ]
    path: /api/subscriber/{subscriberid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::update

api.legacy.subscriber.delete:
    methods: [ DELETE ]
    path: /api/subscriber/{subscriberid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::delete

api.legacy.subscriber.search:
    methods: [ POST ]
    path: /api/subscriber/search.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::search

api.legacy.subscriber.tagged:
    methods: [ POST ]
    path: /api/subscriber/tagged.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::tagged

## Legacy API - subscriber via key

api.legacy.subscriber.signin.html:
    methods: [ POST ]
    path: /api/subscriber/signin.html
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::subscribeWithKeyHtml

api.legacy.subscriber.signin:
    methods: [ POST ]
    path: /api/subscriber/signin.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::subscribeWithKey

api.legacy.subscriber.signout:
    methods: [ POST ]
    path: /api/subscriber/signout.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::untagWithKey

api.legacy.subscriber.signoff:
    methods: [ POST ]
    path: /api/subscriber/signoff.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiSubscriberController::unsubscribeWithKey

## Legacy API - custom field

api.legacy.custom_field.index:
    methods: [ GET ]
    path: /api/field.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiCustomFieldController::index

api.legacy.custom_field.retrieve:
    methods: [ GET ]
    path: /api/field/{fieldid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiCustomFieldController::retrieve

api.legacy.custom_field.create:
    methods: [ POST ]
    path: /api/field.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiCustomFieldController::create

api.legacy.custom_field.update:
    methods: [ PUT ]
    path: /api/field/{fieldid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiCustomFieldController::update

api.legacy.custom_field.delete:
    methods: [ DELETE ]
    path: /api/field/{fieldid}.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiCustomFieldController::delete

api.legacy.custom_field.index_advanced:
    methods: [ POST ]
    path: /api/field/index_advanced.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiCustomFieldController::indexAdvanced

api.legacy.custom_field.dependencies:
    methods: [ POST ]
    path: /api/field/dependencies.{_format?}
    controller: App\Controller\LegacyApi\LegacyApiCustomFieldController::dependencies

# tagging pixel
taggingpixel.show:
    methods: [ GET ]
    path: /pix/{encodedParameters}
    controller: App\Controller\TaggingPixelController::show

# countdown

countdown.clock:
    methods: [ GET ]
    path: /clock/{encodedParameters}
    controller: App\Klicktipp\Countdown\Controller\CountdownController::clock

countdown.link:
    methods: [ GET ]
    path: /infourl/{encodedParameters}
    controller: App\Klicktipp\Countdown\Controller\CountdownController::link

# vcard

vcard.user:
    methods: [ GET ]
    path: /vcard/{encodedParameters}
    controller: App\Controller\VcardController::userVcard

vcard.signature:
    methods: [ GET ]
    path: /signature_vcard/{encodedParameters}
    controller: App\Controller\VcardController::signatureVcard

## account/login - rest of api/account/* still via drupal

api.account.login:
    methods: [ POST ]
    path: /api/account/login.{_format?}
    controller: App\Controller\AuthenticationController::apiLogin

# subscribe and confirm

web.subscribe:
    methods: [ GET, POST ]
    path: /subscribe.php
    controller: App\Controller\SubscribeController::subscribe

web.subscribe.de:
    methods: [ GET, POST ]
    path: /de/subscribe.php
    controller: App\Controller\SubscribeController::subscribe

web.confirm:
    methods: [ GET, POST ]
    path: /{action}/{encodedParameters}
    controller: App\Controller\SubscribeController::confirm
    requirements:
        action: confirm|bestaetigen

# wufoo callbacks

web.wufoo.signin:
    methods: [ POST ]
    path: /wufooipn/
    controller: App\Klicktipp\Wufoo\Controller\WufooController::signin

web.wufoo.thankyou:
    methods: [ GET, POST ]
    path: /wufoothankyou/{encodedApiKey}
    controller: App\Klicktipp\Wufoo\Controller\WufooController::thankyou

web.wufoo.prefill:
    methods: [ GET, POST ]
    path: /wufoo/{encodedApiKey}/{subscriberSecret}
    controller: App\Klicktipp\Wufoo\Controller\WufooController::prefill

# business card reader callback

web.bcr.callback:
    methods: [ POST ]
    path: /businesscard/{encodedApikey}
    controller: App\Klicktipp\BCR\Controller\BCRController::wrapper

# digistore affiliates callback

web.digistore_affiliates:
    methods: [ GET, POST ]
    path: /digit/{encodedApikey}
    controller: App\Klicktipp\Digistore\Controller\DigistoreController::affiliates

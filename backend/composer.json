{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.3", "ext-ctype": "*", "ext-iconv": "*", "ext-json": "*", "ext-pdo": "*", "ext-redis": "*", "datadog/php-datadogstatsd": "^1.6", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^2.5", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.10", "firebase/php-jwt": "^6.8", "google/cloud-web-risk": "^1.1", "guzzlehttp/guzzle": "7.9.*", "guzzlehttp/oauth-subscriber": "0.8.*", "jeremykendall/php-domain-parser": "6.*", "justinrainbow/json-schema": "^5.2", "kevinrob/guzzle-cache-middleware": "^4.0", "launchdarkly/server-sdk": "4.2.*", "nyholm/dsn": "^2.0", "pda/pheanstalk": "^3.2", "php-amqplib/php-amqplib": "^3.0", "phpdocumentor/reflection-docblock": "^5.3", "phpmailer/phpmailer": "6.9.1", "phpstan/phpdoc-parser": "^1.2", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0", "stechstudio/backoff": "^1.2", "symfony/amqp-messenger": "5.4.*", "symfony/asset": "5.4.*", "symfony/cache": "5.4.*", "symfony/console": "5.4.*", "symfony/dotenv": "5.4.*", "symfony/expression-language": "5.4.*", "symfony/flex": "^1.17|^2", "symfony/form": "5.4.*", "symfony/framework-bundle": "5.4.*", "symfony/http-client": "5.4.*", "symfony/intl": "5.4.*", "symfony/lock": "5.4.*", "symfony/mailer": "5.4.*", "symfony/messenger": "5.4.*", "symfony/mime": "5.4.*", "symfony/monolog-bundle": "^3.0", "symfony/notifier": "5.4.*", "symfony/process": "5.4.*", "symfony/property-access": "5.4.*", "symfony/property-info": "5.4.*", "symfony/proxy-manager-bridge": "5.4.*", "symfony/runtime": "5.4.*", "symfony/security-bundle": "5.4.*", "symfony/serializer": "5.4.*", "symfony/string": "5.4.*", "symfony/translation": "5.4.*", "symfony/twig-bundle": "5.4.*", "symfony/validator": "5.4.*", "symfony/web-link": "5.4.*", "symfony/webpack-encore-bundle": "^1.12", "symfony/yaml": "5.4.*", "twig/extra-bundle": "^2.12|^3.0"}, "require-dev": {"digitalrevolution/php-codesniffer-baseline": "1.1.*", "mikey179/vfsstream": "1.6.*", "mockery/mockery": "^1.6", "phpstan/phpstan": "^2.1.2", "phpstan/phpstan-mockery": "^2.0", "phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "3.*", "symfony/browser-kit": "5.4.*", "symfony/css-selector": "5.4.*", "symfony/debug-bundle": "5.4.*", "symfony/phpunit-bridge": "^6.0", "symfony/stopwatch": "5.4.*", "symfony/web-profiler-bundle": "5.4.*"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true, "digitalrevolution/php-codesniffer-baseline": true}, "optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}, "psr-0": {"": "DrupalTransition/"}, "files": ["drupal/datadog.php", "drupal/drupal_amember.php", "drupal/drupal_baseconfig.php", "drupal/drupal_constants.php", "drupal/beanstalkd.php", "drupal/drupal_bootstrap.php", "drupal/drupal_common.php", "drupal/drupal_cron.php", "drupal/drupal_db.php", "drupal/drupal_file.php", "drupal/drupal_install.php", "drupal/drupal_klicktipp.php", "drupal/drupal_unicode.php", "drupal/drupal_mail.php", "drupal/drupal_kt_cache.php", "drupal/drupal_modules.php", "drupal/drupal_queue.php", "drupal/drupal_services.php", "drupal/drupal_settings.php", "drupal/drupal_taxonomy.php", "drupal/drupal_translation.php", "drupal/drupal_user.php", "drupal/drupal_utils.php", "drupal/drupal_password.php", "drupal/drupal_services_formatter.php"]}, "autoload-dev": {"psr-4": {"App\\": "tests/unit/", "App\\Tests\\": "tests/unit/", "App\\Tests\\Integration\\": "tests/integration/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "test": "phpunit --configuration phpunit.xml"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "5.4.*"}, "runtime": {"error_handler": "App\\ErrorHandler\\CustomErrorHandler"}}}
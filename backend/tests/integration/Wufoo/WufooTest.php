<?php

/**
 * @noinspection DuplicatedCode
 * @noinspection PhpDeprecationInspection
 */

namespace App\Tests\Integration\Wufoo;

use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\ErrorPage\ErrorPageManager;
use App\Klicktipp\Errors;
use App\Klicktipp\Lists;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;
use App\Klicktipp\TestData\TestUser;
use App\Klicktipp\Wufoo;
use App\Klicktipp\Wufoo\Controller\WufooController;
use Doctrine\DBAL\Connection;
use Exception;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;

class WufooTest extends WebTestCase
{
    private ?Connection $connection;

    private KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();
        $GLOBALS['app'] = $this->getContainer()->get('kernel');
        /** @var Connection $connection */
        $connection = $this->getContainer()->get('database_connection');
        $this->connection = $connection;
    }

    public function tearDown(): void
    {
        $this->connection = null;
    }

    /**
     * @throws Exception
     */
    public function testDrupalSimpleTest(): void
    {
        $this->assertNotNull($this->connection);
        $testUser = new TestUser($this->connection);
        $testUser->getAnonymousUser();
        $UserID = $testUser->getEnterpriseUser();
        $testUser->loginUser($this->client, $UserID);

        $ReferenceID = 0;

        $message = "Wufoo - Create:";
        $UserID = 1;
        $DefaultList = Lists::RetrieveListByIDOrDefault($UserID, 'default');
        $this->assertNotEmpty($DefaultList, "$message Default List retrieved");

        $NewWufooName = 'Wufoo Simpletest';
        $NewWufooFormID = 'wufoo_simpletest';
        $NewWufooRelListID = $DefaultList['ListID'];
        $NewWufooApiKey = 'simpletest api key';
        $NewWufooPrefillURL = '';
        $NewWufooThankyouURL = '';
        $NewWufooFields = $this->formFieldsTestData($UserID);

        $NewWufooBuildID = Wufoo::InsertDB(array(
            'RelOwnerUserID' => $UserID,
            'Name' => $NewWufooName,
            'VarcharIndexed' => $NewWufooFormID,
            'RelListID' => $NewWufooRelListID
        ));
        $this->assertTrue($NewWufooBuildID > 0, "$message Wufoo created - BiuldID = $NewWufooBuildID");

        $ArrayWufoo = Wufoo::FromID($UserID, $NewWufooBuildID)->GetData();
        $this->assertNotEmpty($ArrayWufoo, "$message ArrayWufoo retrieved");
        $this->assertNotEmpty($ArrayWufoo['SmartTagID'], $message . ' SmartTagID ' . $ArrayWufoo['SmartTagID']);

        $ArrayCustomFields = CustomFields::RetrieveCustomFields($UserID, true);
        $this->assertTrue($DefaultList > 0, "$message User CustomFields retrieved");

        $form_state = array();
        $form_state['values'] = array(
            'account' => (object)array('uid' => $UserID),
            'BuildID' => $NewWufooBuildID,
            'Name' => $NewWufooName,
            'VarcharIndexed' => $NewWufooFormID,
            'CustomFields' => $ArrayCustomFields,
            'ArrayWufoo' => $ArrayWufoo,
            'RelListID' => $NewWufooRelListID,
            'OptInSubscribeTo' => 0,
            'WufooUsername' => $NewWufooName,
            'WufooApiKey' => $NewWufooApiKey,
            'PrefillURL' => $NewWufooPrefillURL,
            'WufooThankyouURL' => $NewWufooThankyouURL,
            'Fields' => $NewWufooFields,
        );

        $message = "Wufoo - Update:";

        $result = $this->listUpdate($form_state, true); //skip the WufooWebhookAPI
        $ArrayWufoo = Wufoo::FromID($UserID, $NewWufooBuildID)->GetData();
        $this->assertTrue($result && !empty($ArrayWufoo['Fields']), "$message ArrayWufoo updated");

        //--- check if tags and CustomFields have been created

        $ArrayWufooFields = $ArrayWufoo['Fields'];
        foreach ($NewWufooFields as $FieldID => $field) {
            if (in_array($ArrayWufooFields[$FieldID]['CustomFieldID'], ['email', 'subscriber', 'PhoneNumber'])) {
                continue;
            }

            $CF = CustomFields::RetrieveCustomField($ArrayWufooFields[$FieldID]['CustomFieldID'], $UserID);
            if (empty($CF)) {
                $this->fail(
                    "$message CustomField for $FieldID with AutoCreateName "
                    . "'{$field['AutoCreateName']}' not created"
                );
            } else {
                if (!$CF['IsGlobal'] && $CF['FieldTypeEnum'] != CustomFields::TYPE_DATETIME) {
                    $this->assertTrue(
                        strpos($CF['FieldName'], $field['AutoCreateName']) === 0,
                        "$message CustomField for $FieldID '{$CF['FieldName']}' created"
                    );
                }
            }

            $Options = $ArrayWufooFields[$FieldID]['Options'];
            if (!empty($Options)) {
                foreach ($Options as $id => $option) {
                    if (!empty($option['OptInSubscribeTo'])) {
                        foreach ($option['OptInSubscribeTo'] as $TagID) {
                            $Tag = Tag::RetrieveTag($UserID, $TagID);
                            $this->assertNotEmpty($Tag, "$message Tag for $FieldID Option $id created");
                        }
                    }
                }
            }
        }

        // --- Wofoo IPN
        $errorPageManager = $this->getContainer()->get(ErrorPageManager::class);
        $controller = new WufooController($errorPageManager);

        // --- TEST CASE: IPN without handshake ---
        $response = $controller->signin(new Request());
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertStringContainsString('/app/error', $response->getTargetUrl());

        // --- TEST CASE: IPN without handshake ---
        $postData = ['HandshakeKey' => Core::EncryptURL(array(
            'BuildID' => 4711,
            'UserID' => 101,
        ), 'api_key')]; //generate api_key with invalid BuildID and UserID
        $response = $controller->signin(new Request([], $postData));
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertStringContainsString('/app/error', $response->getTargetUrl());

        // --- TEST CASE: Subscribe with EmailAddess ---

        $message = "Wufoo IPN Subscription:";

        $SubscriberEmailAddress = 'wufoo@exämple.com';

        $postData = array(
            'Field1' => 'textfield value',
            'Field4' => '0',
            'Field2' => 'textarea value',
            'Field3' => 'Erste Wahl',
            'Field231' => 'OtherFieldValue',
            'Field5' => 'Erste Wahl',
            'Field6' => '',
            'Field7' => '',
            'Field232' => 'Erste Wahl',
            'Field233' => 'Zweite Wahl',
            'Field234' => '',
            'Field105' => 'Erste Wahl',
            'Field106' => 'FirstName',
            'Field107' => 'LastName',
            'Field110' => 'Mr',
            'Field111' => 'First',
            'Field112' => 'Last',
            'Field113' => 'Dipl.',
            'Field114' => 'Street 1',
            'Field115' => 'Street 2',
            'Field116' => 'City',
            'Field117' => 'State',
            'Field118' => '4711',
            'Field119' => 'Deutschland',
            'Field120' => $SubscriberEmailAddress,
            'Field121' => '5417543010',
            'Field150' => '004915212345678',
            'Field126' => '015212345678',
            'Field122' => '100',
            'Field123' => '4',
            'Field125' => '20140101', //YYYYMMDD
            'Field140' => '12:34:56',
            'Field127' => '20151224', //YYYYMMDD
            'Field128' => '16:00:00',
            'Field129' => 'https://www.klick-tipp.com',
            'Field130' => 'Überhaupt nicht einverstanden',
            'Field131' => 'Nicht einverstanden',
            'Field132' => 'Einverstanden',
            'Field124-url' => 'https://klicktipp.wufoo.com/cabinet/z46jmdf1tcncy9/4VP8GeEO7Dc%3D/logo_de.png',
            'IP' => '*********',
            'HandshakeKey' => Core::EncryptURL(array('BuildID' => $NewWufooBuildID, 'UserID' => $UserID), 'api_key'),
        );

        // get redirect URL
        // "thank you" is faster than webhook case:
        $ApiKey = Core::EncryptURL(array('BuildID' => $NewWufooBuildID, 'UserID' => $UserID), 'api_key');

        $request = new Request(['email' => $SubscriberEmailAddress]);
        $response = $controller->thankyou($ApiKey, $request);
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $param_hash = explode('pending/', $response->getTargetUrl());
        $param_decoded = Core::DecryptURL($param_hash[1]);
        $this->assertTrue($param_decoded['uid'] == $UserID, $message . ' decrypted UserID');

        // webhook is faster than "thank you" case:
        $response = $controller->signin(new Request([], $postData));
        $this->assertEquals(200, $response->getStatusCode());

        $ExpectedValues = $postData;
        unset($ExpectedValues['Field4']); //SubscriberID field -> ignore
        unset($ExpectedValues['Field120']); //email field -> ignore
        unset($ExpectedValues['Field150']); //sms field -> ignore
        unset($ExpectedValues['IP']); //ignore
        unset($ExpectedValues['HandshakeKey']); //ignore

        $ExpectedValues['Field124'] = $ExpectedValues['Field124-url'];
        unset($ExpectedValues['Field124-url']);

        // --- retrieve subscription ---
        $ArraySubscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            Subscribers::PunycodeEmailAddress($SubscriberEmailAddress)
        );
        $this->assertNotEmpty($ArraySubscriber, "$message email address '$SubscriberEmailAddress' subscribed");

        // --- retrieve sms subscription ---
        $SMSSubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $postData['Field150']);
        $this->assertTrue(
            $SMSSubscriberID == $ArraySubscriber['SubscriberID'],
            "$message sms '{$postData['Field150']}' subscribed"
        );

        //retrieve subscription with custom fields
        $ArraySubscription = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $ArraySubscriber['SubscriberID'],
            $ReferenceID
        );
        $this->assertNotEmpty($ArraySubscription, "$message subscription retrieved" . print_r($ArraySubscription, 1));

        $ExpectedTags = array();
        foreach ($ExpectedValues as $FieldID => $Value) {
            $CustomFieldID = $ArrayWufoo['Fields'][$FieldID]['CustomFieldID'];

            switch ($FieldID) {
                case 'Field3':
                case 'Field231':
                case 'Field105':
                case 'Field130':
                case 'Field131':
                case 'Field132':
                    //field with options (radio, select, likert)
                    $OptionID = trim(substr(base64_encode($Value), 0, 64), '=');
                    $OptionValue = $ArrayWufoo['Fields'][$FieldID]['Options'][$OptionID]['CustomFieldValue'];
                    $this->assertTrue(
                        $ArraySubscription["CustomField$CustomFieldID"] == $OptionValue,
                        "$message custom field value ($FieldID)"
                    );
                    if (!empty($ArrayWufoo['Fields'][$FieldID]['Options'][$OptionID]['OptInSubscribeTo'])) {
                        $ExpectedTags = array_merge(
                            $ExpectedTags,
                            $ArrayWufoo['Fields'][$FieldID]['Options'][$OptionID]['OptInSubscribeTo']
                        );
                    }
                    break;
                case 'Field5':
                case 'Field6':
                case 'Field7':
                case 'Field232':
                case 'Field233':
                case 'Field234':
                    //checkbox
                    $checked = (empty($Value)) ? 'unchecked' : 'checked';
                    $OptionValue = $ArrayWufoo['Fields'][$FieldID]['Options'][$checked]['CustomFieldValue'];
                    $this->assertTrue(
                        $ArraySubscription["CustomField$CustomFieldID"] == $OptionValue,
                        "$message custom field value ($FieldID) "
                        . "[$OptionValue][{$ArraySubscription["CustomField$CustomFieldID"]}]"
                    );
                    if (!empty($ArrayWufoo['Fields'][$FieldID]['Options'][$checked]['OptInSubscribeTo'])) {
                        $ExpectedTags = array_merge(
                            $ExpectedTags,
                            $ArrayWufoo['Fields'][$FieldID]['Options'][$checked]['OptInSubscribeTo']
                        );
                    }
                    break;
                case 'Field125':
                    //date field
                    $this->assertTrue(
                        $ArraySubscription["CustomField$CustomFieldID"] == strtotime("2014-01-01 00:00:00"),
                        "$message custom field value ($FieldID)"
                    );
                    break;
                case 'Field140':
                    //time field
                    $this->assertTrue(
                        $ArraySubscription["CustomField$CustomFieldID"] == strtotime("1970-01-01 12:34:00 GMT"),
                        "$message custom field value ($FieldID)"
                    );
                    break;
                case 'Field127':
                case 'Field128':
                    $this->assertTrue(
                        $ArraySubscription["CustomField$CustomFieldID"] == strtotime("2015-12-24 16:00:00"),
                        "$message custom field value ($FieldID) - " . date(
                            "Y-m-d H:i:s",
                            $ArraySubscription["CustomField$CustomFieldID"]
                        )
                    );
                    break;
                default:
                    $this->assertTrue(
                        $ArraySubscription["CustomField$CustomFieldID"] == $Value,
                        "$message custom field value ($FieldID)"
                    );
            }
        }

        //check if subscriber was tagged
        $this->assertNotEmpty($ExpectedTags, $message . ' taggings expected ' . print_r($ExpectedTags, true));
        $ArrayTags = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));
        foreach ($ExpectedTags as $TagID) {
            $this->assertTrue(isset($ArrayTags[$TagID]), "$message tagging $TagID");
            $this->assertNotEmpty(
                Subscribers::RetrieveTagging(
                    $ArraySubscription['RelOwnerUserID'],
                    $TagID,
                    $ArraySubscription['SubscriberID'],
                    $ReferenceID
                ),
                $message . ' Subscriber tagged with ' . $TagID
            );
        }
        $this->assertNotEmpty(
            Subscribers::RetrieveTagging(
                $ArraySubscription['RelOwnerUserID'],
                $ArrayWufoo['SmartTagID'],
                $ArraySubscription['SubscriberID'],
                $ReferenceID
            ),
            $message . ' Subscriber tagged with SmartTagID'
        );

        //check prefill URL

        $SubscriberSecret = Core::EncryptURL(array(
            'UserID' => $UserID,
            'SubscriberID' => $ArraySubscription['SubscriberID'],
        ), 'subscriber_secret');
        $ApiKey = Core::EncryptURL(array('BuildID' => $NewWufooBuildID, 'UserID' => $UserID), 'api_key');
        $response = $controller->prefill($ApiKey, $SubscriberSecret);
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertNotEmpty($response->getTargetUrl(), "$message Prefill URL");

        $request = new Request(['email' => $SubscriberEmailAddress]);
        $response = $controller->thankyou($ApiKey, $request);
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertNotEmpty($response->getTargetUrl(), "$message ThankYou URL");
        $param_hash = explode('pending/', $response->getTargetUrl());
        $param_decoded = Core::DecryptURL($param_hash[1]);
        $this->assertTrue($param_decoded['uid'] == $UserID, $message . ' decrypted UserID');

        // --- TEST CASE: Subscribe with SubscriberSecret ---

        $message = "Wufoo IPN - Update subscription via SubscriberID:";
        $NewValue = "textfield value change";
        $postData = array(
            'Field1' => $NewValue,
            'Field120' => '<EMAIL>', //should not change
            'Field4' => Core::EncryptURL(array(
                "UserID" => $UserID,
                "SubscriberID" => $ArraySubscription['SubscriberID'],
            ), 'subscriber_secret'),
            'IP' => '*********',
            'HandshakeKey' => Core::EncryptURL(array('BuildID' => $NewWufooBuildID, 'UserID' => $UserID), 'api_key'),
        );

        $response = $controller->signin(new Request([], $postData));
        $this->assertEquals(200, $response->getStatusCode());

        //retrieve subscription with custom fields
        $ArraySubscription = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $ArraySubscriber['SubscriberID'],
            $ReferenceID
        );

        $CustomFieldID = $ArrayWufoo['Fields']['Field1']['CustomFieldID'];
        $this->assertTrue(
            $ArraySubscription["CustomField$CustomFieldID"] == $NewValue,
            "$message value for Field1 changed to '$NewValue'"
        );

        // invalid subscriber secret

        $postData = array(
            'Field1' => $NewValue,
            'Field4' => Core::EncryptURL(array("UserID" => $UserID, "SubscriberID" => 4711), 'subscriber_secret'),
            //invalid secret, subscriber does not exist
            'IP' => '*********',
            'HandshakeKey' => Core::EncryptURL(array('BuildID' => $NewWufooBuildID, 'UserID' => $UserID), 'api_key'),
        );

        $response = $controller->signin(new Request([], $postData));
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertStringContainsString('/app/error', $response->getTargetUrl());
    }

    /**
     * @param int $UserID
     * @return array<mixed>
     */
    private function formFieldsTestData(int $UserID): array
    {
        //Datetime CustomFields cannot automatically be created in the Wufoo-Dialog
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'Datetime',
            'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
        );
        $DateTimeID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($DateTimeID > 0, "Wufoo - Create: Datetime CustomField created ($DateTimeID)");

        return array(
            'Field1' => array(
                'Type' => 'text',
                'Title' => 'Textfeld',
                'AutoCreateName' => 'Wufoo - Simpletest - Textfeld',
                'CustomFieldID' => 'create',
            ),
            // --- Field to store the SubscriberID ---
            'Field4' => array(
                'Type' => 'number',
                'Title' => 'SubscriberID',
                'AutoCreateName' => 'Wufoo - Simpletest - Zahl',
                'CustomFieldID' => 'subscriber',
            ),
            'Field2' => array(
                'Type' => 'textarea',
                'Title' => 'Textarea',
                'AutoCreateName' => 'Wufoo - Simpletest - Textarea',
                'CustomFieldID' => 'create',
            ),
            'Field3' => array(
                'Type' => 'radio',
                'Title' => 'Auswahl treffen',
                'AutoCreateName' => 'Wufoo - Simpletest - Auswahl treffen',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'RXJzdGUgV2FobA' => array(
                        'OptInSubscribeTo' => array('Field3-Opt1' => 'Field3-Opt1'),
                        'CustomFieldValue' => '1',
                    ),
                    'WndlaXRlIFdhaGw' => array(
                        'OptInSubscribeTo' => array('Field3-Opt2' => 'Field3-Opt2'),
                        'CustomFieldValue' => '2',
                    ),
                    'RHJpdHRlIFdhaGw' => array(
                        'OptInSubscribeTo' => array('Field3-Opt2' => 'Field3-Opt2'),
                        'CustomFieldValue' => '3',
                    ),
                ),
            ),
            'Field5' => array(
                'Type' => 'checkbox',
                'Title' => 'Erste Wahl',
                'AutoCreateName' => 'Wufoo - Simpletest - Wählen Sie alle zutreffenden Antworten aus - Erste Wahl',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'checked' => array(
                        'OptInSubscribeTo' => array('Field5-Checked' => 'Field5-Checked'),
                        'CustomFieldValue' => 'checked',
                    ),
                    'unchecked' => array(
                        'OptInSubscribeTo' => array(),
                        'CustomFieldValue' => '',
                    ),
                ),
            ),
            'Field6' => array(
                'Type' => 'checkbox',
                'Title' => 'Zweite Wahl',
                'AutoCreateName' => 'Wufoo - Simpletest - Wählen Sie alle zutreffenden Antworten aus - Zweite Wahl',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'checked' => array(
                        'OptInSubscribeTo' => array('Field6-Checked' => 'Field6-Checked'),
                        'CustomFieldValue' => 'checked',
                    ),
                    'unchecked' => array(
                        'OptInSubscribeTo' => array(),
                        'CustomFieldValue' => '',
                    ),
                ),
            ),
            'Field7' => array(
                'Type' => 'checkbox',
                'Title' => 'Dritte Wahl',
                'AutoCreateName' => 'Wufoo - Simpletest - Wählen Sie alle zutreffenden Antworten aus - Dritte Wahl',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'checked' => array(
                        'OptInSubscribeTo' => array('Field7-Checked' => 'Field7-Checked'),
                        'CustomFieldValue' => 'checked',
                    ),
                    'unchecked' => array(
                        'OptInSubscribeTo' => array(),
                        'CustomFieldValue' => '',
                    ),
                ),
            ),
            'Field232' => array(
                'Type' => 'checkbox',
                'Title' => 'Erste Wahl',
                'AutoCreateName' => 'Wufoo - Simpletest - Wählen Sie alle zutreffenden Antworten aus - Erste Wahl',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'checked' => array(
                        'OptInSubscribeTo' => array('Field232-Checked' => 'Field232-Checked'),
                        'CustomFieldValue' => 'checked',
                    ),
                    'unchecked' => array(
                        'OptInSubscribeTo' => array('Field232-Unchecked' => 'Field232-Unchecked'),
                        'CustomFieldValue' => 'unchecked',
                    ),
                ),
            ),
            'Field233' => array(
                'Type' => 'checkbox',
                'Title' => 'Zweite Wahl',
                'AutoCreateName' => 'Wufoo - Simpletest - Wählen Sie alle zutreffenden Antworten aus - Zweite Wahl',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'checked' => array(
                        'OptInSubscribeTo' => array('Field133-Checked' => 'Field133-Checked'),
                        'CustomFieldValue' => 'checked',
                    ),
                    'unchecked' => array(
                        'OptInSubscribeTo' => array('Field133-Unchecked' => 'Field133-Unchecked'),
                        'CustomFieldValue' => 'unchecked',
                    ),
                ),
            ),
            'Field234' => array(
                'Type' => 'checkbox',
                'Title' => 'Dritte Wahl',
                'AutoCreateName' => 'Wufoo - Simpletest - Wählen Sie alle zutreffenden Antworten aus - Dritte Wahl',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'checked' => array(
                        'OptInSubscribeTo' => array('Field234-Checked' => 'Field234-Checked'),
                        'CustomFieldValue' => 'checked',
                    ),
                    'unchecked' => array(
                        'OptInSubscribeTo' => array('Field234-Unchecked' => 'Field234-Unchecked'),
                        'CustomFieldValue' => 'unchecked',
                    ),
                ),
            ),
            'Field105' => array(
                'Type' => 'select',
                'Title' => 'Auswahl treffen',
                'AutoCreateName' => 'Wufoo - Simpletest - Auswahl treffen',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'W0VNUFRZXQ' => array(
                        'OptInSubscribeTo' => array('Field105-Opt1' => 'Field105-Opt1'),
                        'CustomFieldValue' => '1',
                    ),
                    'RXJzdGUgV2FobA' => array(
                        'OptInSubscribeTo' => array('Field105-Opt2' => 'Field105-Opt2'),
                        'CustomFieldValue' => '2',
                    ),
                    'WndlaXRlIFdhaGw' => array(
                        'OptInSubscribeTo' => array('Field105-Opt3' => 'Field105-Opt3'),
                        'CustomFieldValue' => '3',
                    ),
                    'RHJpdHRlIFdhaGw' => array(
                        'OptInSubscribeTo' => array('Field105-Opt4' => 'Field105-Opt4'),
                        'CustomFieldValue' => '4',
                    ),
                ),
            ),
            'Field106' => array(
                'Type' => 'shortname',
                'Title' => 'Vorname',
                'AutoCreateName' => 'Wufoo - Simpletest - Name: - Vorname',
                'CustomFieldID' => 'FirstName',
            ),
            'Field107' => array(
                'Type' => 'shortname',
                'Title' => 'Nachname',
                'AutoCreateName' => 'Wufoo - Simpletest - Name: - Nachname',
                'CustomFieldID' => 'LastName',
            ),
            'Field110' => array(
                'Type' => 'name',
                'Title' => 'Titel',
                'AutoCreateName' => 'Wufoo - Simpletest - Name: - Titel',
                'CustomFieldID' => 'create',
            ),
            'Field111' => array(
                'Type' => 'name',
                'Title' => 'Vorname',
                'AutoCreateName' => 'Wufoo - Simpletest - Name: - Vorname',
                'CustomFieldID' => 'create',
            ),
            'Field112' => array(
                'Type' => 'name',
                'Title' => 'Nachname',
                'AutoCreateName' => 'Wufoo - Simpletest - Name: - Nachname',
                'CustomFieldID' => 'create',
            ),
            'Field113' => array(
                'Type' => 'name',
                'Title' => 'Zusatz',
                'AutoCreateName' => 'Wufoo - Simpletest - Name: - Zusatz',
                'CustomFieldID' => 'create',
            ),
            'Field114' => array(
                'Type' => 'address',
                'Title' => 'Straße',
                'AutoCreateName' => 'Wufoo - Simpletest - Adresse - Straße',
                'CustomFieldID' => 'Street1',
            ),
            'Field115' => array(
                'Type' => 'address',
                'Title' => 'Adresszeile 2',
                'AutoCreateName' => 'Wufoo - Simpletest - Adresse - Adresszeile 2',
                'CustomFieldID' => 'Street2',
            ),
            'Field116' => array(
                'Type' => 'address',
                'Title' => 'Ort',
                'AutoCreateName' => 'Wufoo - Simpletest - Adresse - Ort',
                'CustomFieldID' => 'City',
            ),
            'Field117' => array(
                'Type' => 'address',
                'Title' => 'Bundesland',
                'AutoCreateName' => 'Wufoo - Simpletest - Adresse - Bundesland',
                'CustomFieldID' => 'State',
            ),
            'Field118' => array(
                'Type' => 'address',
                'Title' => 'PLZ',
                'AutoCreateName' => 'Wufoo - Simpletest - Adresse - PLZ',
                'CustomFieldID' => 'Zip',
            ),
            'Field119' => array(
                'Type' => 'address',
                'Title' => 'Land',
                'AutoCreateName' => 'Wufoo - Simpletest - Adresse - Land',
                'CustomFieldID' => 'Country',
            ),
            // --- Field to store the SubscriberID ---
            'Field120' => array(
                'Type' => 'email',
                'Title' => 'E-Mail-Adresse',
                'AutoCreateName' => 'Wufoo - Simpletest - E-Mail-Adresse',
                'CustomFieldID' => 'email',
            ),
            'Field150' => array(
                'Type' => 'email',
                'Title' => 'SMS',
                'AutoCreateName' => 'Wufoo - Simpletest - SMS',
                'CustomFieldID' => 'PhoneNumber',
            ),
            'Field121' => array(
                'Type' => 'phone',
                'Title' => 'Telefonnummer',
                'AutoCreateName' => 'Wufoo - Simpletest - Telefonnummer',
                'CustomFieldID' => 'Phone',
            ),
            'Field126' => array(
                'Type' => 'europhone',
                'Title' => 'Telefonnummer',
                'AutoCreateName' => 'Wufoo - Simpletest - Telefonnummer',
                'CustomFieldID' => 'PrivatePhone',
            ),
            'Field122' => array(
                'Type' => 'money',
                'Title' => 'Betrag',
                'AutoCreateName' => 'Wufoo - Simpletest - Betrag',
                'CustomFieldID' => 'create',
            ),
            'Field123' => array(
                'Type' => 'rating',
                'Title' => 'Rating',
                'AutoCreateName' => 'Wufoo - Simpletest - Rating',
                'CustomFieldID' => 'create',
            ),
            'Field124' => array(
                'Type' => 'file',
                'Title' => 'Datei anhängen',
                'AutoCreateName' => 'Wufoo - Simpletest - Datei anhängen',
                'CustomFieldID' => 'create',
            ),
            'Field125' => array(
                'Type' => 'date',
                'Title' => 'Datum',
                'AutoCreateName' => 'Wufoo - Simpletest - Datum',
                'CustomFieldID' => 'create',
            ),
            'Field140' => array(
                'Type' => 'time',
                'Title' => 'Uhrzeit',
                'AutoCreateName' => 'Wufoo - Simpletest - Uhrzeit',
                'CustomFieldID' => 'create',
            ),
            'Field127' => array(
                'Type' => 'eurodate',
                'Title' => 'Datum',
                'AutoCreateName' => 'Wufoo - Simpletest - Datum',
                'CustomFieldID' => $DateTimeID,
            ),
            'Field128' => array(
                'Type' => 'time',
                'Title' => 'Uhrzeit',
                'AutoCreateName' => 'Wufoo - Simpletest - Uhrzeit',
                'CustomFieldID' => $DateTimeID,
            ),
            'Field129' => array(
                'Type' => 'url',
                'Title' => 'Website',
                'AutoCreateName' => 'Wufoo - Simpletest - Website',
                'CustomFieldID' => 'Website',
            ),
            'Field130' => array(
                'Type' => 'likert',
                'Title' => 'Aussage eins',
                'AutoCreateName' => 'Wufoo - Simpletest - Bewerten Sie die folgenden Aussagen. - Aussage eins',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'w5xiZXJoYXVwdCBuaWNodCBlaW52ZXJzdGFuZGVu' => array(
                        'OptInSubscribeTo' => array('Field130-Opt1' => 'Field130-Opt1'),
                        'CustomFieldValue' => '1',
                    ),
                    'TmljaHQgZWludmVyc3RhbmRlbg' => array(
                        'OptInSubscribeTo' => array('Field130-Opt2' => 'Field130-Opt2'),
                        'CustomFieldValue' => '2',
                    ),
                    'RWludmVyc3RhbmRlbg' => array(
                        'OptInSubscribeTo' => array('Field130-Opt3' => 'Field130-Opt3'),
                        'CustomFieldValue' => '3',
                    ),
                    'QWJzb2x1dCBlaW52ZXJzdGFuZGVu' => array(
                        'OptInSubscribeTo' => array('Field130-Opt4' => 'Field130-Opt4'),
                        'CustomFieldValue' => '4',
                    ),
                ),
            ),
            'Field131' => array(
                'Type' => 'likert',
                'Title' => 'Aussage zwei',
                'AutoCreateName' => 'Wufoo - Simpletest - Bewerten Sie die folgenden Aussagen. - Aussage zwei',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'w5xiZXJoYXVwdCBuaWNodCBlaW52ZXJzdGFuZGVu' => array(
                        'OptInSubscribeTo' => array('Field131-Opt1' => 'Field131-Opt1'),
                        'CustomFieldValue' => '1',
                    ),
                    'TmljaHQgZWludmVyc3RhbmRlbg' => array(
                        'OptInSubscribeTo' => array('Field131-Opt2' => 'Field131-Opt2'),
                        'CustomFieldValue' => '2',
                    ),
                    'RWludmVyc3RhbmRlbg' => array(
                        'OptInSubscribeTo' => array('Field131-Opt3' => 'Field131-Opt3'),
                        'CustomFieldValue' => '3',
                    ),
                    'QWJzb2x1dCBlaW52ZXJzdGFuZGVu' => array(
                        'OptInSubscribeTo' => array('Field131-Opt4' => 'Field131-Opt4'),
                        'CustomFieldValue' => '4',
                    ),
                ),
            ),
            'Field132' => array(
                'Type' => 'likert',
                'Title' => 'Aussage drei',
                'AutoCreateName' => 'Wufoo - Simpletest - Bewerten Sie die folgenden Aussagen. - Aussage drei',
                'CustomFieldID' => 'create',
                'Options' => array(
                    'w5xiZXJoYXVwdCBuaWNodCBlaW52ZXJzdGFuZGVu' => array(
                        'OptInSubscribeTo' => array('Field132-Opt1' => 'Field132-Opt1'),
                        'CustomFieldValue' => '1',
                    ),
                    'TmljaHQgZWludmVyc3RhbmRlbg' => array(
                        'OptInSubscribeTo' => array('Field132-Opt2' => 'Field132-Opt2'),
                        'CustomFieldValue' => '2',
                    ),
                    'RWludmVyc3RhbmRlbg' => array(
                        'OptInSubscribeTo' => array('Field132-Opt3' => 'Field132-Opt3'),
                        'CustomFieldValue' => '3',
                    ),
                    'QWJzb2x1dCBlaW52ZXJzdGFuZGVu' => array(
                        'OptInSubscribeTo' => array('Field132-Opt4' => 'Field132-Opt4'),
                        'CustomFieldValue' => '4',
                    ),
                ),
            ),
        );
    }

    private function listUpdate($form_state, bool $SkipWebhookAPI = false): bool
    {
        $UserID = $form_state['values']['account']->uid;
        $ArrayWufoo = $form_state['values']['ArrayWufoo'];
        $WufooFormID = $ArrayWufoo['VarcharIndexed'];
        $CustomFields = $form_state['values']['CustomFields'];
        $MetaLabels = array_keys($form_state['values']['MetaLabels'] ?? []);

        $OptInSubscribeTo = array();
        if (!empty($form_state['values']['OptInSubscribeTo'])) {
            //in case user entered a free value, create new tag, it's 1 tag but pass as array,
            //return value: $OptInSubscribeTo[0] = new/existing TagID
            $OptInSubscribeTo = Tag::CreateManualTagByTagName(
                $UserID,
                array($form_state['values']['OptInSubscribeTo'])
            );
            if (!$OptInSubscribeTo) {
                //error creating the tag, no changes in the form have been applied yet,
                //so show error and stay in the settings dialog
                $error = array(
                    '!UserID' => $UserID,
                    '!function' => __FUNCTION__,
                    '!BuildID' => $ArrayWufoo['BuildID'],
                    '!OptInSubscribeTo' => $form_state['values']['OptInSubscribeTo'],
                );
                Errors::unexpected(
                    "Error: CreateManualTagByTagName with '!OptInSubscribeTo'"
                    . " in !function for User !UserID with BuildID !BuildID",
                    $error,
                    true
                );
                return false;
            }
        }

        $ArrayWufoo['Name'] = $form_state['values']['Name'];
        $ArrayWufoo['RelListID'] = $form_state['values']['RelListID'];
        $ArrayWufoo['AssignTagID'] = (empty($OptInSubscribeTo) || !is_array($OptInSubscribeTo))
            ? 0 : $OptInSubscribeTo[0];
        $ArrayWufoo['WufooUsername'] = $form_state['values']['WufooUsername'];
        $ArrayWufoo['WufooApiKey'] = $form_state['values']['WufooApiKey'];
        $ArrayWufoo['PrefillURL'] = $form_state['values']['PrefillURL'];
        $ArrayWufoo[DatabaseTableLabeled::LABEL_DATA_KEY] = $MetaLabels;

        // --------------------------------------

        //store each field that has been matched with either a Customfield or Tag
        //Note: if the user changed his username or api key in Wufoo, the fields can't be displayed in the form
        //      do not update the field data so the user is able to change his username and/or api key
        if (!empty($form_state['values']['Fields'])) {
            $WufooFields = array();

            foreach ($form_state['values']['Fields'] as $FieldID => $field) {
                $WufooFields[$FieldID] = array(
                    'Title' => $field['Title'], //WufooTitle
                    'Type' => $field['Type'], //WufooType
                );

                if (!empty($field['CustomFieldID'])) {
                    //a custom field has been assigned

                    if ($field['CustomFieldID'] == 'create') {
                        //the user wants to create a new Custom field

                        //do not create custom fields with the same name
                        $CheckName = $field['AutoCreateName'];
                        $AutocreateName = $CheckName;
                        $tries = 2;
                        $used = true;
                        while ($used && $tries < 100) {
                            $used = false;
                            foreach ($CustomFields as $cf) {
                                if ($cf['FieldName'] == $AutocreateName) {
                                    //"Name" -> "Name 2" -> "Name 3" ...
                                    $AutocreateName = "$CheckName $tries";
                                    $tries++;
                                    $used = true;
                                    break;
                                }
                            }
                        }

                        //Create Customfield with FieldTypeEnum matching the WufooFieldType
                        //@see klicktipp_list_wufoo_form_field_type_enum
                        //Note: Datetime Customfields cannot automatically be created
                        $ArrayNewCustomField = array(
                            'FieldName' => $AutocreateName,
                            'FieldTypeEnum' => $this->formFieldTypeEnum($field['Type']),
                            'RelOwnerUserID' => $UserID,
                            'FieldDefaultValue' => "",
                            'FieldOptions' => '',
                            'FieldCopyTo' => '',
                            'FieldParameters' => array(),
                        );

                        if ($NewCustomFieldID = CustomFields::Create($ArrayNewCustomField)) {
                            //assign newly created Customfield to WufooField
                            $WufooFields[$FieldID]['CustomFieldID'] = $NewCustomFieldID;

                            //add the newly created CustomField to the array, so it will be included in the name check
                            $CustomFields[] = array(
                                'CustomFieldID' => $NewCustomFieldID,
                                'FieldName' => $AutocreateName,
                            );

                            drupal_set_message(
                                t("Custom field %name successfully created.", array('%name' => $AutocreateName))
                            );
                        } else {
                            //unexpected error, notify the user but still try to save the Wufoo form

                            //error log
                            $error = array(
                                '!UserID' => $UserID,
                                '!NewCustomField' => $ArrayNewCustomField,
                                '!WufooType' => $field['Type'],
                            );
                            Errors::unexpected(
                                "Wufoo: Error while auto-creating Customfield for User !UserID.",
                                $error
                            );

                            //message to user
                            drupal_set_message(
                                t("Error while creating Custom field %name.", array('%name' => $AutocreateName)),
                                'error'
                            );
                        }
                    } else {
                        //assign WufooField to Customfield
                        //Note: 'email' and 'subscriber' will also be stored in 'CustomFieldID'
                        //@see klicktipp_list_wufoo_signin
                        $WufooFields[$FieldID]['CustomFieldID'] = $field['CustomFieldID'];
                    }

                    if ($CustomFields[$field['CustomFieldID']]['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
                        $WufooFields[$FieldID]['isDatetime'] = true; //@see klicktipp_list_wufoo_signin
                    }
                }

                //store field options
                if (in_array($field['Type'], array('radio', 'likert', 'select', 'checkbox'))) {
                    $WufooFields[$FieldID]['Options'] = $field['Options'];
                }

                if (!empty($field['Options'])) {
                    //tags have been assigned

                    foreach ($field['Options'] as $op_id => $data) {
                        $TagIDs = $data['OptInSubscribeTo'];

                        if (!empty($TagIDs)) {
                            $OptInSubscribeTo = Tag::CreateManualTagByTagName($UserID, $TagIDs);
                            if (!$OptInSubscribeTo || !is_array($OptInSubscribeTo)) {
                                //error creating the tags (unexpected), notify the user but still try
                                //to save the Wufoo form
                                $error = array(
                                    '!UserID' => $UserID,
                                    '!OptInSubscribeTo' => $TagIDs,
                                );
                                Errors::unexpected("Wufoo: Error while auto-creating tags for User !UserID.", $error);

                                //message to user
                                drupal_set_message(t("Error, one or more tags could not be created."), 'error');
                            } else {
                                $WufooFields[$FieldID]['Options'][$op_id]['OptInSubscribeTo'] =
                                    array_values($OptInSubscribeTo);
                            }
                        }
                    }
                }
            }

            $ArrayWufoo['Fields'] = $WufooFields;

            $OldWufooThankyouURL = $ArrayWufoo['WufooThankyouURL'];
            $ArrayWufoo['WufooThankyouURL'] = Wufoo::GetThankyouURL($ArrayWufoo);
            if (!empty($OldWufooThankyouURL) && $OldWufooThankyouURL != $ArrayWufoo['WufooThankyouURL']) {
                drupal_set_message(
                    t(
                        'You have changed your email or contact-id field. If you are using the Confirmation pending page, please update the URL in your Wufoo Subscription form.'
                    ),
                    'warning'
                );
            }
        }

        //Wufoo form data is sent via a WebHook to "https://www.klick-tipp.com/wufoo"
        //this WebHook URL is set for each form via the Wufoo API
        //a Wufoo Webhook has a handshakeKey ($_POST param) which we set to our API key

        // create the handshake with UserID and BuildID to identify the Wufoo form in the IPN
        $ArrayQueryParameters = array(
            'UserID' => $UserID,
            'BuildID' => $ArrayWufoo['BuildID'],
        );
        $WufooHandshake = array(
            'url=' . APP_URL . "wufooipn/",
            'handshakeKey=' . Core::EncryptURL($ArrayQueryParameters, 'api_key'),
            'metadata=0', //we don't need field specs
        );

        //create/update the WebHook
        //Wufoo docs: "this call can be used to both create and update a Web Hook"
        //the WebHook is identified by the URL (which is always '/wufoo'), so we won't create countless
        //WebHooks for a WufooForm
        //we update on every submit, since the user can delete the Webhook in Wufoo
        //if he submits the form, we can assume the he wants to connect his Wufoo form with KlickTipp
        //PUTPOST: params must be passed as post parameters to the Web Hook API (Wufoo docs)
        if (!$SkipWebhookAPI) {
            //Note: do not call the Wufoo-API from Simpletest

            $WufooWebHookAPI = Wufoo::GetApiURL(Wufoo::API_WEBHOOK, $ArrayWufoo);
            $WufooResponse = Core::DataPostToRemoteURL(
                $WufooWebHookAPI,
                $WufooHandshake,
                'PUTPOST',
                true,
                $ArrayWufoo['WufooApiKey'],
                'nopassneeded',
                60,
                false,
                false,
                array(
                    'Content-type: multipart/form-data',
                    'Expect:',
                )
            );
            /** @var array<string, array<string>> $WufooResult */
            $WufooResult = json_decode($WufooResponse[1], true);
            //store WebHook hash for DELETE on wufoo_delete_form_submit
            $ArrayWufoo['WebHook'] = (empty($WufooResult['WebHookPutResult']['Hash'])) ?
                '' : $WufooResult['WebHookPutResult']['Hash'];

            if (empty($ArrayWufoo['WebHook'])) {
                //the WebHook API call should only fail if authorization (username/API key) changed or the Wufoo form
                //was deleted in Wufoo, (which must happen after loading the edit dialog and before submitting it)
                //after submit we will be redirected to the edit dialog in which we load the form fields via the API,
                //if API authorization or not found error messages will be shown to the user there

                $error = array(
                    '!UserID' => $UserID,
                    '!BuildID' => $ArrayWufoo['BuildID'],
                    '!WufooResult' => $WufooResult,
                    '!WufooHandshake' => $WufooHandshake,
                );
                Errors::unexpected(
                    "Wufoo: Error while updating the Webhook for user !UserID with BuildID !BuildID.",
                    $error,
                    true
                );
            }
        }

        if (!Wufoo::UpdateDB($ArrayWufoo)) {
            $error = array(
                '!UserID' => $UserID,
                '!BuildID' => $ArrayWufoo['BuildID'],
                '!Name' => $ArrayWufoo['Name'],
                '!WufooFormID' => $WufooFormID,
                '!ArrayWufoo' => $ArrayWufoo,
            );
            Errors::unexpected(
                "Wufoo: Error while updating a Wufoo Subscription form for user !UserID with BuildID !BuildID.",
                $error,
                true
            );

            return false;
        }

        return true;
    }

    /**
     * Match the Wufoo field type with the corresponding KlickTipp CustomFieldTypeEnum
     */
    private function formFieldTypeEnum(string $WufooType): int
    {
        switch ($WufooType) {
            case 'textarea':
                return CustomFields::TYPE_PARAGRAPH;
            case 'number':
            case 'rating':
            case 'money':
                return CustomFields::TYPE_NUMBER;
            case 'email':
                return CustomFields::TYPE_EMAIL;
            case 'url':
            case 'file': // returns url to uploaded filename on wufoo.com
                return CustomFields::TYPE_URL;
            case 'time':
                return CustomFields::TYPE_TIME;
            case 'date':
            case 'eurodate':
                return CustomFields::TYPE_DATE;
            case 'text':
            case 'phone':
            case 'europhone':
            case 'radio': //selected values are stored as singles
            case 'select': //selected values are stored as singles
            case 'checkbox': //selected values are stored as singles
            case 'shortname': //shortname consists of 2 textfields (firstname, lastname)
            case 'name': //name consists of 4 textfields (title, firstname, lastname, suffix)
            case 'address': //address consists of 6 textfields (street 1, street 2, city, state, zip, country)
            case 'likert': //consists of options
            default:
                return CustomFields::TYPE_SINGLE;
        }
    }
}

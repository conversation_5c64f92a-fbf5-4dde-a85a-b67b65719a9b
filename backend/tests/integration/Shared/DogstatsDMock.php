<?php

namespace App\Tests\Integration\Shared;

use App\Datadog\DogStatsD;

/**
 * <PERSON><PERSON> of DogStatsd class to avoid sending metrics to DataDog during tests
 *
 * @codeCoverageIgnore
 */
class DogstatsDMock extends DogStatsD
{
    public function __construct()
    {
        parent::__construct();
    }

    public function timing($stat, $time, $sampleRate = 1.0, $tags = null)
    {
        // ignore
    }
}
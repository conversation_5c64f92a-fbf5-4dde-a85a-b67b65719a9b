<?php

namespace App\Tests\Integration\Klicktipp;

use App\Klicktipp\Core;
use App\Klicktipp\CSAClient;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\EmailsNewsletterEmail;
use App\Klicktipp\EmailsNotificationEmail;
use App\Klicktipp\Libraries;
use App\Klicktipp\SendEngine;
use App\Klicktipp\Signatures;
use App\Klicktipp\Subscribers;
use App\Klicktipp\TestData\TestUser;
use App\Tests\Integration\Helpers\SendEngineMock;
use App\Tests\Integration\Helpers\TestingMailSystem;
use Doctrine\DBAL\Connection;
use Exception;
use GuzzleHttp\Psr7\Response;
use MailSystemInterface;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

require_once 'dns_shell_exec.php';

class CoreapiSendEngineTest extends WebTestCase
{
    private ?Connection $connection;

    protected function setUp(): void
    {
        parent::setUp();

        $GLOBALS['app'] = $this->getContainer()->get('kernel');
        /** @var Connection $connection */
        $connection = $this->getContainer()->get('database_connection');
        $this->connection = $connection;

        $this->getContainer()->set(MailSystemInterface::class, new TestingMailSystem());
    }

    public function tearDown(): void
    {
        $this->connection = null;
    }

    /**
     * @throws Exception
     */
    public function testDrupalSimpleTest(): void
    {
        $GLOBALS['shellExecMock'] = fn() => '';
        $GLOBALS['conf']['clean_url'] = true;
        Libraries::include("send_engine.inc", "/tests/includes");

        /*
         * prepare data
         */
        $message = 'prepare';

        $testUser = new TestUser($this->connection);
        $UserID = $testUser->getEnterpriseUser();


        $account = user_load($UserID);
        $this->assertTrue(user_access('use whitelabel domain', $account), "$message permission");

        $FromName = 'FromName';
        $FromMail = '<EMAIL>';
        $ToEmail = '<EMAIL>';
        $ReplyToEmail = '<EMAIL>';
        $XSubscription = 'XSubscription';
        $AbuseMessageID = 'AbuseMessageID';
        $CampaignID = 4711;
        $EmailID = 4712;
        $SubscriberID = 4714;
        $ReferenceID = 42;
        $ListID = 4715;
        $AbuseMessageID = Core::GenerateAbuseMessageID($EmailID, $SubscriberID, $ReferenceID, $ToEmail, $UserID, $CampaignID);
        $ArraySubscription = array(
            'SubscriberID' => $SubscriberID,
            'EmailAddress' => $ToEmail,
            'RelListID' => $ListID,
        );

        $Domain = 'zauberlist.com';
        $Domain2 = 'mailandbeyond.com';
        $Domain3 = 'no-master-nameserver.com';
        $Hostname = 'host1';
        $Hostname2 = 'host2';
        $IPAddress = '***********';
        $IPAddress2 = '***********';
        $Alias = 'news';
        $MX = 'mail';

        /*
         * whitelabel domains (except deletion)
         */

        // test defaults (no whitelabel domain defined yet)
        $message = 'GetValidSenderDomains (default)';
        $SenderDomains = DomainSet::GetValidSenderDomains((array) $account);
        $this->assertTrue($SenderDomains == TRUE, "$message alias");

        $message = 'SelectSenderDomain (default)';
        $ArrayEmail = array();
        $SenderDomain = DomainSet::SelectSenderDomain($UserID, $SenderDomains, $ArrayEmail);
        $this->assertTrue($SenderDomain['alias'] == KLICKTIPP_DOMAIN, "$message alias");
        $this->assertTrue($SenderDomain['host'] == '', "$message host");

        /*
         * plain
         */
        $message = 'SendEngine::SetEmailProperties plain';

        $ContentType = Emails::CONTENT_TYPE_PLAIN;
        $TMPSubject = 'we try a non encoding subject';
        $TMPHTMLBody = 'TMPHTMLBody';
        $TMPPlainBody = <<<plain
there will be some coded lines with arbitrary length (80 chars)
these are 80 chars90123456789012345678901234567890123456789012345678901234567890
only 60 chars 5678901234567890123456789012345678901234567890
we are going to encode special äöüßÄÖÜ chars



and a lot of new lines
plain;

        variable_set('klicktipp_list_help_header', '<https://!domain/help>');

        $EmailContent = SendEngine::SetEmailProperties($SenderDomain, $FromName, $FromMail, $ToEmail, $ReplyToEmail, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody, $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID);

        // see /tests/includes/send_engine.inc
        checkEmailContent($this, $EmailContent, $message, $FromName, $FromMail, $ToEmail, $ReplyToEmail, $XSubscription, $ContentType, $TMPSubject, TRUE, 'bounces.' . KLICKTIPP_DOMAIN, $ArraySubscription, $ReferenceID, $CampaignID, $EmailID, $UserID, $SenderDomain);
        $this->assertFalse(strpos($EmailContent, 'x-dkim-options'));

        $message = 'SendEngine::SetEmailProperties plain without unsubscribe link';

        $EmailContent = SendEngine::SetEmailProperties($SenderDomain, $FromName, $FromMail, $ToEmail, $ReplyToEmail, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody, $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID, TRUE);
        checkEmailContent($this, $EmailContent, $message, $FromName, $FromMail, $ToEmail, $ReplyToEmail, $XSubscription, $ContentType, $TMPSubject, TRUE, 'bounces.' . KLICKTIPP_DOMAIN, $ArraySubscription, $ReferenceID, $CampaignID, $EmailID, $UserID, $SenderDomain, '', '', FALSE, '<https://'.KLICKTIPP_DOMAIN.'/help>');
        $this->assertFalse(strpos($EmailContent, 'x-dkim-options'));


        $message = 'CreateWhitelabelDomain';
        $result = DomainSet::CreateWhitelabelDomain($UserID, $Domain);
        $this->assertTrue($result, "$message result");
        $result = DomainSet::CreateWhitelabelDomain($UserID, $Domain2);
        $this->assertTrue($result, "$message result 2");
        $result = DomainSet::CreateWhitelabelDomain($UserID, $Domain3);
        $this->assertTrue($result, "$message result 3");
        $result = DomainSet::CreateWhitelabelDomain(4711, 'example.com');
        $this->assertTrue($result, "$message result 3");
        $result = DomainSet::CreateWhitelabelDomain($UserID, 'example.com');
        $this->assertFalse($result, "$message already exists");

        $message = 'RetrieveWhitelabelDomain';
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain);
        $this->assertTrue($domainset['Domain'] == $Domain, "$message result");
        $this->assertTrue($domainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_NEW, "$message Status");

        $message = 'RetrieveWhitelabelDomains';
        $ArrayDomains = DomainSet::RetrieveWhitelabelDomains($UserID, $Domain);
        $this->assertTrue(count($ArrayDomains) == 1, "$message $UserID $Domain");
        $ArrayDomains = DomainSet::RetrieveWhitelabelDomains($UserID);
        $this->assertTrue(count($ArrayDomains) == 3, "$message $UserID");
        $ArrayDomains = DomainSet::RetrieveWhitelabelDomains();
        $this->assertTrue(count($ArrayDomains) == 4, "$message");

        $message = 'UpdateWhitelabelDomain';
        $domainset['Data']['hosts'][$Hostname] = $IPAddress;
        $domainset['Data']['alias'] = $Alias;
        $domainset['Data']['mxhost'] = $MX;
        $domainset['Data']['defaultsender'] = TRUE;
        $domainset['Status'] = DomainSet::WHITELABELDOMAIN_STATUS_ACTIVE;
        DomainSet::UpdateWhitelabelDomain($Domain, $domainset);
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain);
        $this->assertTrue(count($domainset['Data']['hosts']) == 1, "$message result");

        $message = 'GetWhitelabelDNSKeys';
        $dnskeys = DomainSet::GetWhitelabelDNSKeys($domainset, KLICKTIPP_DOMAIN);
        $testkeys = testGetWhitelabelDNSKeys($domainset, KLICKTIPP_DOMAIN, TRUE, FALSE, $IPAddress); // this will set the values
        foreach ($dnskeys as $key => $value) {
            if ($key != 'dkimvalue') {
                $this->assertTrue($testkeys[$key] == $value, "$message $key");
            }
        }
        $message = 'DNSCheckWithUpdate';

        DomainSet::setDigFunction('testCallDIGForVerify');
        DomainSet::setDnsKeysFunction('testGetWhitelabelDNSKeys');

        DomainSet::DNSCheckWithUpdate($domainset);
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain);
        $this->assertTrue($domainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED, "$message Status" . print_r($domainset, 1));
        $this->assertTrue($domainset['validFrom'] > strtotime('+1 days'), "$message validFrom");

        $message = 'DNSCheckWithUpdate complex spf';
        $testkeys = testGetWhitelabelDNSKeys($domainset, KLICKTIPP_DOMAIN, TRUE, TEST_DNS_SPF_COMPLEX, $IPAddress); // this will set the values

        $spf = testCallDIGForVerify($testkeys['mxalias'], 'TXT');
        $this->assertTrue(strpos($spf, 'ip4') > 0, "$message spf"); // test if complex spf is returned
        DomainSet::DNSCheckWithUpdate($domainset);
        $this->assertTrue($domainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED, "$message result" . print_r($result, 1)); // test if verification accepts it

        $message = 'DNSCheckWithUpdate new spf';
        testGetWhitelabelDNSKeys($domainset, KLICKTIPP_DOMAIN, TRUE, TEST_DNS_SPF_MX, $IPAddress); // this will set the values
        DomainSet::DNSCheckWithUpdate($domainset);
        $this->assertTrue($domainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED, "$message result" . print_r($result, 1)); // test if verification accepts it

        $message = 'DNSCheckWithUpdate main domain restricted by spf';
        $resrictedDomainset = array_merge($domainset, ['Domain' => 'restricted.com']);
        DomainSet::DNSCheckWithUpdate($resrictedDomainset);
        $this->assertFalse($resrictedDomainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED, $message . ' (no success)'); // test if verification accepts it

        $message = 'DNSCheckWithUpdate main domain included in spf';
        $spfDomainIncludedDomainset = array_merge($domainset, ['Domain' => 'included.com']);
        testGetWhitelabelDNSKeys($spfDomainIncludedDomainset, KLICKTIPP_DOMAIN, TRUE, TEST_DNS_SPF_MX, $IPAddress); // this will set the values
        $this->assertTrue($spfDomainIncludedDomainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED, $message . ' (success)'); // test if verification accepts it
        testGetWhitelabelDNSKeys($domainset, KLICKTIPP_DOMAIN, TRUE, TEST_DNS_SPF_MX, $IPAddress); // this will set the values

        $message = 'CheckWhitelabelDomain';
        DomainSet::CheckWhitelabelDomain($domainset);
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain);
        $this->assertTrue($domainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED, "$message Status");


        $message = 'CheckWhitelabelDomain with errors';
        $userWhitelabelDomainCheckEmail = [
            'SenderName' => 'Simple Test',
            'SenderAddress' => '<EMAIL>',
            'Subject' => 'Wichtig: Enterprise-Server !domain deaktiviert!',
            'Body' => 'Hello !firstName, something is wrong with !domain. Please check it using !url'
        ];
        variable_set(COREAPI_NOTIFY_EMAIL_USER_WHITELABELDOMAIN_CHECK, $userWhitelabelDomainCheckEmail);

        DomainSet::UpdateWhitelabelDomain($Domain3, $domainset);
        $domainset3 = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain3);
        $this->assertEquals($domainset3['Status'], DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED, "$message verified domain created");
        DomainSet::CheckWhitelabelDomain($domainset3);
        $domainset3 = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain3);
        $this->assertEquals(
            $domainset3['Status'],
            DomainSet::WHITELABELDOMAIN_STATUS_ACTIVE,
            "$message status switched from verified to active in case of errors"
        );
        $emails = TestingMailSystem::getMails();
        $configurationEmail = end($emails);
        $userHash = Core::CryptNumber($UserID);
        $this->assertEquals($configurationEmail['to'], $account->mail, "$message emails has expected 'to'-address");
        $this->assertEquals($configurationEmail['from'], "{$userWhitelabelDomainCheckEmail['SenderName']} <{$userWhitelabelDomainCheckEmail['SenderAddress']}>", "$message email has expected sender");
        $this->assertEquals($configurationEmail['subject'], format_string($userWhitelabelDomainCheckEmail['Subject'], ['!domain' => $Domain3]), "$message email has expected sobject");
        $this->assertNotSame(strpos($configurationEmail['body'], $Domain3), FALSE, "$message email body contains domain" );
        $this->assertNotSame(strpos($configurationEmail['body'], $account->FirstName), FALSE, "$message email body contains expected firstname");
        $this->assertNotSame(strpos($configurationEmail['body'], '/domain/www' . $userHash . '/' . $Domain3), FALSE, "$message email body contains expected url");


        $message = 'GetValidSenderDomains';
        $SenderDomains = DomainSet::GetValidSenderDomains((array) $account);
        $this->assertTrue($SenderDomains === FALSE, "$message validFrom > now" . print_r($SenderDomains, 1));
        $domainset['validFrom'] = strtotime('-1 days');
        DomainSet::UpdateWhitelabelDomain($Domain, $domainset);
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain);
        $SenderDomains = DomainSet::GetValidSenderDomains((array) $account);
        $this->assertTrue(count($SenderDomains) == 1, "$message validFrom < now" . print_r($SenderDomains, 1));

        $message = 'SelectSenderDomain';
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain2);
        $domainset['Status'] = DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED;
        $domainset['validFrom'] = strtotime('-1 days');
        DomainSet::UpdateWhitelabelDomain($Domain2, $domainset);
        $SenderDomains = DomainSet::GetValidSenderDomains((array) $account);
        $this->assertTrue(count($SenderDomains) == 2, "$message 2 verified" . print_r($SenderDomains, 1));
        $ArrayEmail = array('SenderDomain' => $Domain2);
        $SenderDomain = DomainSet::SelectSenderDomain($UserID, $SenderDomains, $ArrayEmail);
        $this->assertTrue($SenderDomain['domain'] == $Domain2, "$message domain");

        // get different sender domains for tests below
        $WhitelabelSenderDomain = DomainSet::SelectSenderDomain($UserID, $SenderDomains);
        $this->assertTrue($WhitelabelSenderDomain['domain'] == $Domain, "$message default whitelabel ".print_r($WhitelabelSenderDomain,1));
        $this->assertTrue($WhitelabelSenderDomain['host'] == "$Hostname.$Domain", "$message host");
        $this->assertTrue($WhitelabelSenderDomain['sender'] == "postmaster@$MX.$Domain", "$message sender");
        $SenderDomain = DomainSet::SelectSenderDomain($UserID);
        $this->assertTrue($SenderDomain['alias'] == KLICKTIPP_DOMAIN, "$message default klicktipp");

        /*
         * whitelabel
         */
        $message = 'SendEngine::SetEmailProperties whitelabel';
        $CCEmail = "<EMAIL>";
        $BCCEmail = "<EMAIL>";

        $WhitelabelFromMail = '<EMAIL>';

        $EmailContent = SendEngine::SetEmailProperties($WhitelabelSenderDomain, $FromName, $WhitelabelFromMail, $ToEmail, $ReplyToEmail, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody, $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID, FALSE, 0, $CCEmail, $BCCEmail);

        // see /tests/includes/send_engine.inc
        checkEmailContent($this, $EmailContent, $message, $FromName, $WhitelabelFromMail, $ToEmail, $ReplyToEmail, $XSubscription, $ContentType, $TMPSubject, FALSE, "$MX.$Domain", $ArraySubscription, $ReferenceID, $CampaignID, $EmailID, $UserID, $WhitelabelSenderDomain, $CCEmail, $BCCEmail);
        $this->assertStringContainsString("x-dkim-options: s=ktdkim;d=zauberlist.com", $EmailContent);

        $message = 'SendEngine::SetEmailProperties whitelabel without unsubscribe link';

        $EmailContent = SendEngine::SetEmailProperties($WhitelabelSenderDomain, $FromName, $WhitelabelFromMail, $ToEmail, $ReplyToEmail, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody, $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID, TRUE, 0, $CCEmail, $BCCEmail);
        checkEmailContent($this, $EmailContent, $message, $FromName, $WhitelabelFromMail, $ToEmail, $ReplyToEmail, $XSubscription, $ContentType, $TMPSubject, FALSE, "$MX.$Domain", $ArraySubscription, $ReferenceID, $CampaignID, $EmailID, $UserID, $WhitelabelSenderDomain, $CCEmail, $BCCEmail, FALSE, '<https://news.zauberlist.com/help>');
        $this->assertStringContainsString("x-dkim-options: s=ktdkim;d=zauberlist.com", $EmailContent);

        $message = 'SendEngine::SetEmailProperties whitelabel - dkim-domain (without mailserver)';

        prepareDkimDomain($UserID, 'wl-with-dkim.com');
        $WhitelabelDkimFromMail = '<EMAIL>';
        $EmailContent = SendEngine::SetEmailProperties($WhitelabelSenderDomain, $FromName, $WhitelabelDkimFromMail, $ToEmail, $ReplyToEmail, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody, $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID, TRUE, 0, $CCEmail, $BCCEmail);
        checkEmailContent($this, $EmailContent, $message, $FromName, $WhitelabelDkimFromMail, $ToEmail, $ReplyToEmail, $XSubscription, $ContentType, $TMPSubject, FALSE, "$MX.$Domain", $ArraySubscription, $ReferenceID, $CampaignID, $EmailID, $UserID, $WhitelabelSenderDomain, $CCEmail, $BCCEmail, FALSE, '<https://news.zauberlist.com/help>');
        $this->assertStringContainsString("x-dkim-options: s=ktdkim2;d=wl-with-dkim.com;key-list=dkim-shared", $EmailContent);

        /*
         * check sender
         */
        $message = 'SendEngine::SetEmailProperties sender';

        // what happens with sender, if from == replyto
        $REPLY = $FromMail;

        // sender is klicktipp system address, klicktipp
        $EmailContent = SendEngine::SetEmailProperties($SenderDomain, $FromName, $FromMail, $ToEmail, $REPLY, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, TRUE, 'GAVNO', $TMPPlainBody, $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID);
        $this->assertTrue(strpos($EmailContent, "Sender: verifications@" . variable_get('klicktipp_shared_sender_domain', KLICKTIPP_DOMAIN)) !== FALSE, "$message Sender klicktipp");

        // sender is postmaster, if whitelabel
        $EmailContent = SendEngine::SetEmailProperties($WhitelabelSenderDomain, $FromName, $FromMail, $ToEmail, $REPLY, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, TRUE, 'GAVNO', $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID);
        $this->assertTrue(strpos($EmailContent, "Sender: {$WhitelabelSenderDomain['sender']}") !== FALSE, "$message Sender whitelabel");

        /*
         * html
         */
        $message = 'SendEngine::SetEmailProperties html';

        $ContentType = Emails::CONTENT_TYPE_BOTH;
        $TMPSubject = 'this subject will be enoded due to these äöüßÄÖÜ special chars and it will get an arbitrary length';
        $TMPHTMLBody = '<some attr="html" like>text that should be split encoded</and>&amp; whatever äöüß' . "\n" . 'even it has some newlines in it';
        $TMPPlainBody = 'simple plain text'; // less than 24 chars

        $EmailContent = SendEngine::SetEmailProperties($SenderDomain, $FromName, $FromMail, $ToEmail, $ReplyToEmail, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody, $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID);

        // see /tests/includes/send_engine.inc
        checkEmailContent($this, $EmailContent, $message, $FromName, $FromMail, $ToEmail, $ReplyToEmail, $XSubscription, $ContentType, $TMPSubject, TRUE, 'bounces.' . KLICKTIPP_DOMAIN, $ArraySubscription, $ReferenceID, $CampaignID, $EmailID, $UserID);
        $this->assertFalse(strpos($EmailContent, 'x-dkim-options'));

        /*
         * different SenderDomain
         */
        $message = 'SendEngine::SetEmailProperties SenderDomain';

        $DifferentSenderDomain = 'example.com';

        $ContentType = Emails::CONTENT_TYPE_BOTH;
        $TMPSubject = 'this subject will be enoded due to these äöüßÄÖÜ special chars and it will get an arbitrary length';
        $TMPHTMLBody = '<some attr="html" like>text that should be split encoded</and>&amp; whatever äöüß' . "\n" . 'even it has some newlines in it';
        $TMPPlainBody = 'simple plain text'; // less than 24 chars

        $EmailContent = SendEngine::SetEmailProperties($SenderDomain, $FromName, $FromMail, $ToEmail, $ReplyToEmail, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody, $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID);

        // see /tests/includes/send_engine.inc
        checkEmailContent($this, $EmailContent, $message, $FromName, $FromMail, $ToEmail, $ReplyToEmail, $XSubscription, $ContentType, $TMPSubject, TRUE, 'bounces.' . KLICKTIPP_DOMAIN, $ArraySubscription, $ReferenceID, $CampaignID, $EmailID, $UserID);
        $this->assertFalse(strpos($EmailContent, 'x-dkim-options'));


        $message = 'SendEngine::SetEmailProperties - shared email';
        $sharedUserID = $testUser->getStandardUser();
        $SharedAbuseMessageID = Core::GenerateAbuseMessageID($EmailID, $SubscriberID, $ReferenceID, $ToEmail, $sharedUserID, $CampaignID);

        $sharedDomainUser = 1000;
        $sharedDomains = ['local-beta.com'];
        $newSharedDomains = ['local-beta-with-dkim.click'];
        variable_set('shared_senderdomain_user', $sharedDomainUser);
        variable_set('shared_senderdomain_domains', json_encode($sharedDomains));
        variable_set('new_shared_senderdomain_domains', json_encode($newSharedDomains));
        variable_get('klicktipp_shared_dkimdomain', 'zauberlist.com');
        prepareDomain($sharedDomainUser, $sharedDomains[0], TRUE, $Alias, $Hostname, $MX);
        prepareDomain($sharedDomainUser, $newSharedDomains[0], TRUE, $Alias, $Hostname, $MX);

        // config changed, so we need to reset static cache
        DomainSet::GetModDomains('', FALSE, TRUE);

        $message = 'SelectSenderDomain - shared';
        $SenderDomain = DomainSet::SelectSenderDomain($sharedUserID, TRUE);
        $this->assertTrue($SenderDomain['domain'] == $sharedDomains[0], "$message - old domain -  domains");
        $this->assertTrue($SenderDomain['bounces'] == $MX . '.' . $sharedDomains[0], "$message - old domain - bounces");

        variable_set('last_user_without_subdomains', $sharedUserID - 1);
        $SenderDomain = DomainSet::SelectSenderDomain($sharedUserID);
        $this->assertTrue($SenderDomain['domain'] == $sharedDomains[0], "$message - old domain -  domains");
        $this->assertTrue($SenderDomain['bounces'] == $MX . '.' . $sharedDomains[0], "$message - old domain - subdomains - bounces");

        variable_set('last_user_without_subdomains', PHP_INT_MAX);
        // after first dkim domain exists, user is switched to new domains and sending approach
        prepareDkimDomain($sharedUserID, 'with-dkim.com');

        $SenderDomain = DomainSet::SelectSenderDomain($sharedUserID);
        $this->assertTrue($SenderDomain['domain'] == $newSharedDomains[0], "$message - new domain -  domains");
        $this->assertTrue($SenderDomain['bounces'] == $MX . '.' . $newSharedDomains[0], "$message - new domain - subdomains - bounces");


        $mailWithoutDkim = '<EMAIL>';
        $userSubdomain = DomainSet::EncodeSharedSubDomain($sharedUserID, $newSharedDomains[0]);
        $overwrittenMailWithoutDkim = 'someone-without-dkim.com@' . $userSubdomain;
        $EmailContent = SendEngine::SetEmailProperties($SenderDomain, $FromName, $mailWithoutDkim, $ToEmail, $ReplyToEmail, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody, $SharedAbuseMessageID, $CampaignID, $EmailID, $sharedUserID, $SubscriberID, $ReferenceID, $ListID);
        checkEmailContent($this, $EmailContent, "$message - new domains - without dkim", $FromName, $overwrittenMailWithoutDkim, $ToEmail, $ReplyToEmail, $XSubscription, $ContentType, $TMPSubject, FALSE, $MX . '.' .  $newSharedDomains[0], $ArraySubscription, $ReferenceID, $CampaignID, $EmailID, $sharedUserID, $SenderDomain);
        $this->assertStringContainsString("x-dkim-options: s=ktdkim2;d=" . $userSubdomain . ";key-list=dkim-shared", $EmailContent);

        $mailWithDkim = '<EMAIL>';
        $userSubdomain = DomainSet::EncodeSharedSubDomain($sharedUserID, $newSharedDomains[0]);
        $EmailContent = SendEngine::SetEmailProperties($SenderDomain, $FromName, $mailWithDkim, $ToEmail, $ReplyToEmail, array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody, $SharedAbuseMessageID, $CampaignID, $EmailID, $sharedUserID, $SubscriberID, $ReferenceID, $ListID);
        checkEmailContent($this, $EmailContent, "$message - new domains - with dkim", $FromName, $mailWithDkim, $ToEmail, $ReplyToEmail, $XSubscription, $ContentType, $TMPSubject, FALSE, $MX . '.' .  $newSharedDomains[0], $ArraySubscription, $ReferenceID, $CampaignID, $EmailID, $sharedUserID, $SenderDomain);
        $this->assertStringContainsString("x-dkim-options: s=ktdkim2;d=with-dkim.com;key-list=dkim-shared", $EmailContent);

        /*
         * SendEmail
         */
        $message = 'SendEngine::SendEmail';

        $ArrayResult = SendEngine::SendEmail($EmailContent);
        $this->assertTrue($ArrayResult[0], $message . ' result' . print_r($ArrayResult, 1));
        $this->assertTrue(!file_exists($ArrayResult[3]['sourcedir'] . '/' . $ArrayResult[3]['filename']), $message . ' no file in tmp');
        $this->assertTrue(file_exists($ArrayResult[3]['targetdir'] . '/' . $ArrayResult[3]['filename']), $message . ' file in public');

        /*
         * whitelabel domains -- deletion
         */

        $message = 'DeleteWhitelabelDomains';

        // mock successfull csa delete response
        CSAClient::setResultMock([new Response(200, [], 'OK')]);

        $result = DomainSet::DeleteWhitelabelDomains($UserID, $Domain);
        $this->assertTrue($result, "$message result");
        $ArrayDomains = DomainSet::RetrieveWhitelabelDomains();
        $this->assertTrue(count($ArrayDomains) == 5, "$message count");

        $message = 'DeleteWhitelabelDomains of user';
        $result = DomainSet::DeleteWhitelabelDomains($UserID);
        $this->assertTrue($result, "$message result");
        $ArrayDomains = DomainSet::RetrieveWhitelabelDomains();
        $this->assertTrue(count($ArrayDomains) == 3, "$message count");

        /*
         * X-Vitual-MTA (transactional pool)
         */

        $message = 'transactional pool';

        // test defaults (no whitelabel domain defined)

        $SenderDomains = DomainSet::GetValidSenderDomains((array) $account);
        $this->assertTrue($SenderDomains == TRUE, "$message alias");

        $result = DomainSet::CreateWhitelabelDomain($UserID, $Domain);
        $this->assertTrue($result, "$message result");

        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain);
        $this->assertTrue($domainset['Domain'] == $Domain, "$message result");
        $this->assertTrue($domainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_NEW, "$message Status");

        // check for misconfigured confirmation hosts

        $domainset['Data']['hosts'][$Hostname] = $IPAddress;
        $domainset['Data']['hosts'][$Hostname2] = $IPAddress2;
        $arecords = [
            "$Hostname.$Domain." => $IPAddress,
            "$Hostname2.$Domain." => $IPAddress2,
        ];
        $testkeys = testGetWhitelabelDNSKeys($domainset, KLICKTIPP_DOMAIN, TRUE, FALSE, $arecords); // this will set the values
        // set all hosts to be transactional
        $domainset['Data']['hostdata'][$Hostname]['confirmation'] = 1;
        $domainset['Data']['hostdata'][$Hostname2]['confirmation'] = 1;
        $domainset['Data']['alias'] = $Alias;
        $domainset['Data']['mxhost'] = $MX;
        $domainset['Data']['defaultsender'] = TRUE;
        $domainset['Status'] = DomainSet::WHITELABELDOMAIN_STATUS_ACTIVE;
        DomainSet::UpdateWhitelabelDomain($Domain, $domainset);
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain);
        $this->assertTrue(count($domainset['Data']['hosts']) == 2, "$message hosts");
        $this->assertTrue($domainset['Data']['useTransactionalPool'] == 0, "$message transactional pool");

        $message = 'hasNonConfirmationHosts';

        $this->assertFalse(DomainSet::hasNonConfirmationHosts($domainset), "$message error");

        $message = 'DNSCheckWithUpdate';

        DomainSet::setDigFunction('testCallDIGForVerify');
        DomainSet::setDnsKeysFunction('testGetWhitelabelDNSKeys');

        // check for well configured confirmation hosts

        $domainset['Data']['hostdata'] = [];
        $domainset['Data']['hostdata'][$Hostname2]['confirmation'] = 1;
        $domainset['Status'] = DomainSet::WHITELABELDOMAIN_STATUS_ACTIVE;
        DomainSet::UpdateWhitelabelDomain($Domain, $domainset);
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain);
        $this->assertTrue(DomainSet::hasNonConfirmationHosts($domainset), "$message has non confirmation hosts");
        $this->assertTrue(count($domainset['Data']['hosts']) == 2, "$message hosts");
        $this->assertTrue($domainset['Data']['useTransactionalPool'] == 1, "$message transactional pool");

        DomainSet::DNSCheckWithUpdate($domainset);
        $this->assertTrue($domainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED, "$message Status" . print_r($domainset, 1));
        $this->assertTrue($domainset['validFrom'] > strtotime('+1 days'), "$message validFrom");

        // make it a valid sender and get sender domain

        $domainset['validFrom'] = strtotime('-1 days');
        DomainSet::UpdateWhitelabelDomain($Domain, $domainset);
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $Domain);
        $SenderDomains = DomainSet::GetValidSenderDomains((array) $account);
        $this->assertTrue(count($SenderDomains) == 1, "$message validFrom < now" . print_r($SenderDomains, 1));
        $SenderDomain = DomainSet::SelectSenderDomain($UserID, $SenderDomains);
        $this->assertTrue($SenderDomain['domain'] == $Domain, "$message domain");

        // confirmation email

        $message = 'transactional pool';

        $EmailContent = SendEngine::SetEmailProperties($SenderDomain, $FromName, $FromMail, $ToEmail, $ReplyToEmail,
            array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody,
            $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID,
            FALSE, time(), '', '', TRUE);
        $this->assertTrue(strpos($EmailContent, "X-Virtual-MTA: $Domain-transactional") !== FALSE, "$message X-Virtual-MTA transactional" . htmlspecialchars($EmailContent));

        // campaign email

        $message = 'non transactional pool';

        $EmailContent = SendEngine::SetEmailProperties($SenderDomain, $FromName, $FromMail, $ToEmail, $ReplyToEmail,
            array('X-Subscription' => $XSubscription), $ContentType, $TMPSubject, $TMPHTMLBody, $TMPPlainBody,
            $AbuseMessageID, $CampaignID, $EmailID, $UserID, $SubscriberID, $ReferenceID, $ListID,
            FALSE, time(), '', '', FALSE);
        $this->assertTrue(strpos($EmailContent, "X-Virtual-MTA: $Domain-transactional") === FALSE, "$message X-Virtual-MTA transactional" . htmlspecialchars($EmailContent));
        $this->assertTrue(strpos($EmailContent, "X-Virtual-MTA: $Domain") !== FALSE, "$message X-Virtual-MTA non transactional");

        /**
         * selectEmailProperties
         */

        // create signature for user
        $message = 'SendEngine::SelectEmailProperties';
        $Signature = array(
            'RelOwnerUserID' => $UserID,
            'SignatureName' => 'Sample name',
            'RelTagIDs' => array(2),
            'HTMLSignatureText' => variable_get('klicktipp_htmlsignature_default', ''),
            'PlainSignatureText' => variable_get('klicktipp_plainsignature_default', ''),
            'Data' => array(
                'CCEmail' => '',
                'BCCEmail' => '',
                'ToEmail' => '<EMAIL>',
            ),
        );
        $SignatureID = Signatures::InsertDB($Signature);
        $this->assertTrue($SignatureID > 0, $message . ' save');
        $RevisionID = Signatures::CreateRevision($UserID, $SignatureID);
        $this->assertTrue($RevisionID > 0, $message . ' save');

        // create email for user
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'SendTransactionalEmail',
            'FromName' => Signatures::SIGNATURE_DISPATCH_PROFILE,
            'FromEmail' => Signatures::SIGNATURE_DISPATCH_PROFILE,
            'ReplyToEmail' => Signatures::SIGNATURE_DISPATCH_PROFILE,
            'Subject' => 'a simple test subject',
            'PlainContent' => 'a simple plain text',
            'HTMLContent' => 'a simple html text',
        );
        $EmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($EmailID > 0, $message . ' create');
        $ArrayFieldAndValues['CCEmail'] = Signatures::SIGNATURE_DISPATCH_PROFILE;
        $ArrayFieldAndValues['BCCEmail'] = Signatures::SIGNATURE_DISPATCH_PROFILE;
        $EmailID2 = EmailsNotificationEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($EmailID2 > 0, $message . ' create');

        // retrieve email and signature by tag
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
        $this->assertTrue(!empty($ArrayEmail), $message . ' retrieve email');
        $ArraySignature = Signatures::FindSignatureByTag($ArrayEmail, array(2), array(), TRUE);

        // check that default values will be set
        $ToEmail = '<EMAIL>';
        $account = (array) $account;
        [$ArrayEmail, $ToEmail] = SendEngine::SelectEmailProperties($account, $ToEmail, $ArrayEmail, $ArraySignature);
        // from name is set to empty value as it will be set by SendEngine::SetEmailProperties()
        $this->assertTrue(empty($ArrayEmail['FromName']), $message . ' from name');
        $this->assertTrue($ArrayEmail['FromEmail'] == $account['mail'], $message . ' from email');
        $this->assertTrue($ArrayEmail['ReplyToEmail'] == $account['mail'], $message . ' reply email');
        $this->assertTrue(empty($ArrayEmail['CCEmail']), $message . ' cc email');
        $this->assertTrue(empty($ArrayEmail['BCCEmail']), $message . ' bcc email');

        // set cc email and bcc email from signature
        $ObjectEmail = Emails::FromID($UserID, $EmailID);
        $ArrayEmail = $ObjectEmail->GetData();
        $ArrayEmail['CCEmail'] = Signatures::SIGNATURE_DISPATCH_PROFILE;
        $ArrayEmail['BCCEmail'] = Signatures::SIGNATURE_DISPATCH_PROFILE;
        $ObjectEmail->UpdateDB($ArrayEmail);

        // check cc email and bcc email are set empty because the signature has no cc/bcc email addresses defined
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
        [$ArrayEmail, $ToEmail] = SendEngine::SelectEmailProperties($account, $ToEmail, $ArrayEmail, $ArraySignature);
        $this->assertTrue(empty($ArrayEmail['FromName']), $message . ' from name');
        $this->assertTrue($ArrayEmail['FromEmail'] == $account['mail'], $message . ' from email');
        $this->assertTrue($ArrayEmail['ReplyToEmail'] == $account['mail'], $message . ' reply email');
        $this->assertTrue(empty($ArrayEmail['CCEmail']), $message . ' cc email');
        $this->assertTrue(empty($ArrayEmail['BCCEmail']), $message . ' bcc email');

        // changes to signature that set cc/bcc email addresses
        $Signature = Signatures::RetrieveSignatureByID($UserID, $SignatureID);
        $Signature['CCEmail'] = '<EMAIL>';
        $Signature['BCCEmail'] = '<EMAIL>';
        $Updated = Signatures::UpdateDB($Signature);
        $this->assertTrue($Updated, $message . ' signature data updated: ' . $Signature['SignatureName']);
        // check changed data
        $ArraySignature = Signatures::FindSignatureByTag($ArrayEmail, array(2), array(), TRUE);
        // check cc email and bcc email are set from signature
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
        [$ArrayEmail, $ToEmail] = SendEngine::SelectEmailProperties($account, $ToEmail, $ArrayEmail, $ArraySignature);
        $this->assertTrue(empty($ArrayEmail['FromName']), $message . ' from name');
        $this->assertTrue($ArrayEmail['FromEmail'] == $account['mail'], $message . ' from email');
        $this->assertTrue($ArrayEmail['ReplyToEmail'] == $account['mail'], $message . ' reply email');
        $this->assertTrue($ArrayEmail['CCEmail'] == $Signature['CCEmail'], $message . ' cc email');
        $this->assertTrue($ArrayEmail['BCCEmail'] == $Signature['BCCEmail'], $message . ' bcc email');

        // update values
        $NewFromName = "Sample From Name";
        $NewFromEmail = "<EMAIL>";
        $NewReplyEmail = "<EMAIL>";
        $ArrayEmail['FromName'] = $NewFromName;
        $ArrayEmail['FromEmail'] = $NewFromEmail;
        $ArrayEmail['ReplyToEmail'] = $NewReplyEmail;
        $ObjectEmail->UpdateDB($ArrayEmail);

        // check from name and from/reply-to email are set from email and cc email and bcc email are set from signature
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
        [$ArrayEmail, $ToEmail] = SendEngine::SelectEmailProperties($account, $ToEmail, $ArrayEmail, $ArraySignature);
        $this->assertTrue($ArrayEmail['FromName'] == $NewFromName, $message . ' from name');
        $this->assertTrue($ArrayEmail['FromEmail'] == $NewFromEmail, $message . ' from email');
        $this->assertTrue($ArrayEmail['ReplyToEmail'] == $NewReplyEmail, $message . ' reply email');
        $this->assertTrue($ArrayEmail['CCEmail'] == $Signature['CCEmail'], $message . ' cc email');
        $this->assertTrue($ArrayEmail['BCCEmail'] == $Signature['BCCEmail'], $message . ' bcc email');

        // set cc email and bcc email in email as fixed values
        $NewCCEmail = "<EMAIL>";
        $NewBCCEmail = "<EMAIL>";
        $ObjectEmail = Emails::FromID($UserID, $EmailID);
        $ArrayEmail = $ObjectEmail->GetData();
        $ArrayEmail['CCEmail'] = $NewCCEmail;
        $ArrayEmail['BCCEmail'] = $NewBCCEmail;
        $ObjectEmail->UpdateDB($ArrayEmail);

        // check all values are set from email
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
        [$ArrayEmail, $ToEmail] = SendEngine::SelectEmailProperties($account, $ToEmail, $ArrayEmail, $ArraySignature);
        $this->assertTrue($ArrayEmail['FromName'] == $NewFromName, $message . ' from name');
        $this->assertTrue($ArrayEmail['FromEmail'] == $NewFromEmail, $message . ' from email');
        $this->assertTrue($ArrayEmail['ReplyToEmail'] == $NewReplyEmail, $message . ' reply email');
        $this->assertTrue($ArrayEmail['CCEmail'] == $NewCCEmail, $message . ' cc email');
        $this->assertTrue($ArrayEmail['BCCEmail'] == $NewBCCEmail, $message . ' bcc email');

        // update signature
        $Signature = Signatures::RetrieveSignatureByID($UserID, $SignatureID);
        $NewValues = $Signature;
        $NewValues['FromName'] = "Signature From Name";
        $NewValues['FromEmail'] = "<EMAIL>";
        $NewValues['ReplyToEmail'] = "<EMAIL>";
        $NewValues['ToEmail'] = "<EMAIL>";
        $Updated = Signatures::UpdateDB($NewValues);
        $this->assertTrue($Updated, $message . ' signature updated');

        // update email so all values will be retrieved from signature
        $ObjectEmail = Emails::FromID($UserID, $EmailID);
        $ArrayEmail = $ObjectEmail->GetData();
        $ArrayEmail['FromName'] = Signatures::SIGNATURE_DISPATCH_PROFILE;
        $ArrayEmail['FromEmail'] = Signatures::SIGNATURE_DISPATCH_PROFILE;
        $ArrayEmail['ReplyToEmail'] = Signatures::SIGNATURE_DISPATCH_PROFILE;
        $ArrayEmail['CCEmail'] = Signatures::SIGNATURE_DISPATCH_PROFILE;
        $ArrayEmail['BCCEmail'] = Signatures::SIGNATURE_DISPATCH_PROFILE;
        $ObjectEmail->UpdateDB($ArrayEmail);

        // check all values are set from email except to email as the email is a newsletter
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
        $ArraySignature = Signatures::FindSignatureByTag($ArrayEmail, array(2), array(), TRUE);
        $ToEmail = Signatures::SIGNATURE_DISPATCH_PROFILE;
        [$ArrayEmail, $ToEmail] = SendEngine::SelectEmailProperties($account, $ToEmail, $ArrayEmail, $ArraySignature);
        $this->assertTrue($ArrayEmail['FromName'] == $ArraySignature['FromName'], $message . ' from name');
        $this->assertTrue($ArrayEmail['FromEmail'] == $ArraySignature['FromEmail'], $message . ' from email');
        $this->assertTrue($ArrayEmail['ReplyToEmail'] == $ArraySignature['ReplyToEmail'], $message . ' reply email');
        $this->assertTrue($ArrayEmail['CCEmail'] == $ArraySignature['CCEmail'], $message . ' cc email');
        $this->assertTrue($ArrayEmail['BCCEmail'] == $ArraySignature['BCCEmail'], $message . ' bcc email');
        $this->assertTrue($ToEmail == Signatures::SIGNATURE_DISPATCH_PROFILE, $message . ' to email unchanged');

        // check all values are set from email including to email
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID2);
        $ToEmail = Signatures::SIGNATURE_DISPATCH_PROFILE;
        [$ArrayEmail, $ToEmail] = SendEngine::SelectEmailProperties($account, $ToEmail, $ArrayEmail, $ArraySignature);
        $this->assertTrue($ArrayEmail['FromName'] == $ArraySignature['FromName'], $message . ' from name');
        $this->assertTrue($ArrayEmail['FromEmail'] == $ArraySignature['FromEmail'], $message . ' from email');
        $this->assertTrue($ArrayEmail['ReplyToEmail'] == $ArraySignature['ReplyToEmail'], $message . ' reply email');
        $this->assertTrue($ArrayEmail['CCEmail'] == $ArraySignature['CCEmail'], $message . ' cc email');
        $this->assertTrue($ArrayEmail['BCCEmail'] == $ArraySignature['BCCEmail'], $message . ' bcc email');
        $this->assertTrue(Subscribers::IsSameEmailAddress($ToEmail, $ArraySignature['ToEmail']), $message . ' to email');

        $message = 'SendEngine - sending_mode_campaigns flag - smtp off: ';

        SendEngineMock::SendEmail('confirmation', 'con');
        SendEngineMock::SendEmail('autoresponder', 'ar');

        $this->assertEquals(SendEngineMock::countDiscCalls(), 2, $message . 'all messages sent via disc');
        $this->assertEquals(SendEngineMock::countSmtpCalls(), 0, $message . 'no messages sent via smtp');


        variable_set('smtp_on', 1);

        $message = 'SendEngine - sending_mode_campaigns flag - smtp on - campaign mode default: ';

        SendEngineMock::reset();
        SendEngineMock::SendEmail('confirmation', 'con');
        SendEngineMock::SendEmail('autoresponder', 'ar');

        $this->assertEquals(SendEngineMock::countDiscCalls(), 1, $message . 'campaign message sent via disc');
        $this->assertEquals(SendEngineMock::countSmtpCalls(), 1, $message . 'conf message sent via smtp');

        $message = 'SendEngine - sending_mode_campaigns flag - smtp on - campaign mode explicitly set to disc: ';

        variable_set('sending_mode_campaigns', SendEngine::SENDING_MODE_DISC);

        SendEngineMock::reset();
        SendEngineMock::SendEmail('confirmation', 'con');
        SendEngineMock::SendEmail('autoresponder', 'ar');

        $this->assertEquals(SendEngineMock::countDiscCalls(), 1, $message . 'campaign message sent via disc');
        $this->assertEquals(SendEngineMock::countSmtpCalls(), 1, $message . 'conf message sent via smtp');

        $message = 'SendEngine - sending_mode_campaigns flag - smtp on - campaign mode set to smtp: ';

        variable_set('sending_mode_campaigns', SendEngine::SENDING_MODE_SMTP);

        SendEngineMock::reset();
        SendEngineMock::SendEmail('confirmation', 'con');
        SendEngineMock::SendEmail('autoresponder', 'ar');

        $this->assertEquals(SendEngineMock::countDiscCalls(), 0, $message . 'no messages sent via disc');
        $this->assertEquals(SendEngineMock::countSmtpCalls(), 2, $message . 'all messages sent via smtp');

        $message = 'SendEngine - sending_mode_campaigns flag - smtp on - campaign mode set to smtp, but smtp is broken: ';

        SendEngineMock::reset();
        SendEngineMock::breakSmtp();
        SendEngineMock::SendEmail('confirmation', 'con');
        SendEngineMock::SendEmail('autoresponder', 'ar');

        $this->assertEquals(SendEngineMock::countDiscCalls(), 2,$message . 'all messages sent via disc (fallback)');
        $this->assertEquals(SendEngineMock::countSmtpCalls(), 2, $message . 'tried no send all messages via smtp');
    }
}

<?php

namespace App\Tests\Integration\Klicktipp;

use App\Klicktipp\FeatureToggle;
use App\Klicktipp\ProductFruitsWidget;
use App\Klicktipp\TestData\TestUser;
use App\Tests\Integration\Helpers\LanguageAware;
use Doctrine\DBAL\Connection;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\Security\Core\User\UserInterface;

class ProductFruitsTest extends WebTestCase
{
    use LanguageAware;

    /**
     * @var Connection
     */
    private $connection;

    private $client;

    private TestUser $testUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();
        $GLOBALS['app'] = $this->getContainer()->get('kernel');
        $this->connection = $this->getContainer()->get('database_connection');
        $this->setUpTestLanguages();
        $this->testUser = new TestUser($this->connection);
    }

    private function getTestEnterpriseUser(): UserInterface
    {
        $this->testUser->getAnonymousUser();
        $UserID = $this->testUser->getDeluxeUser();
        $UserID = $this->testUser->getEnterpriseUser();

        return $this->testUser->loginUser($this->client, $UserID);
    }

    public function testWidgetNotLoadedWhenFeatureToggleIsDisabled(): void
    {
        $user = $this->getTestEnterpriseUser();
        $account = $user->getAccount();

        /** @var FeatureToggle&MockObject $featureToggle */
        $featureToggle = $this->getMockBuilder(FeatureToggle::class)
            ->disableOriginalConstructor()
            ->getMock();

        $featureToggle->expects($this->any())
            ->method('featureToggle')
            ->with('show-product-fruits')
            ->willReturn(false);

        $productFruits = new ProductFruitsWidget($account, $featureToggle);
        $this->assertFalse($productFruits->isActive());
        $this->assertEmpty($productFruits->getHeaderWidget());
    }

    /**
     * @dataProvider provideProductFruitsSettings
     */
    public function testWidgetIsLoadedWhenFeatureToggleIsEnabled(
        ?string $configuredWorkspaceCode,
        ?string $configuredScripts,
        bool $expectedIsActive,
        string $expectedHeaderWidget
    ): void {
        variable_set('klicktipp_product_fruits_workspace_code', $configuredWorkspaceCode);
        variable_set('klicktipp_product_fruits_scripts', $configuredScripts);

        $user = $this->getTestEnterpriseUser();
        $account = $user->getAccount();

        /** @var FeatureToggle&MockObject $featureToggle */
        $featureToggle = $this->getMockBuilder(FeatureToggle::class)
            ->disableOriginalConstructor()
            ->getMock();

        $featureToggle->expects($this->any())
            ->method('featureToggle')
            ->with('show-product-fruits')
            ->willReturn(true);

        $productFruits = new ProductFruitsWidget($account, $featureToggle);
        $this->assertSame($expectedIsActive, $productFruits->isActive());
        $this->assertEquals($expectedHeaderWidget, $productFruits->getHeaderWidget());
    }

    public function provideProductFruitsSettings(): array
    {
        $workspaceCode = 'aBcD1234eFgH5678';

        return [
            'no configuration' => [
                'configuredWorkspaceCode' => null,
                'configuredScripts' => null,
                'expectedIsActive' => false,
                'expectedHeaderWidget' => ''
            ],
            'proper configuration' => [
                'configuredWorkspaceCode' => $workspaceCode,
                'configuredScripts' => $this->getDefaultScript(),
                'expectedIsActive' => true,
                'expectedHeaderWidget' => $this->getDefaultScript() . $this->getInitScript($workspaceCode)
            ]
        ];
    }

    private function getDefaultScript(): string
    {
        return <<<'EOL'
<script type="text/javascript">
    (function(w,d,u){
        w.$productFruits=w.$productFruits||[];
        w.productFruits=w.productFruits||{};w.productFruits.scrV='2';
        let a=d.getElementsByTagName('head')[0];let r=d.createElement('script');r.async=1;r.src=u;a.appendChild(r);
    })(window,document,'https://app.productfruits.com/static/script.js');
</script>
EOL;
    }

    private function getInitScript(string $workspaceCode): string
    {
        $rawScript = <<<'EOL'
<script type="text/javascript">document.addEventListener('DOMContentLoaded', () => {$productFruits.push(["init", "%s", "en", {"username":"ktlocal.com::cw","email":"<EMAIL>","firstname":"FirstnameEnterpriseUser","lastname":"LastnameEnterpriseUser","role":"Account Enterprise","props":{"segments":[],"tier":0}}])});</script><script type='text/javascript'>var productFruitsUser = {"username":"ktlocal.com::cw","email":"<EMAIL>","firstname":"FirstnameEnterpriseUser","lastname":"LastnameEnterpriseUser","role":"Account Enterprise","props":{"segments":[],"tier":0}};</script>
EOL;

        return sprintf($rawScript, $workspaceCode);
    }
}

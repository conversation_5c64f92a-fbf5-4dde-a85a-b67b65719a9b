<?php

namespace App\Tests\Integration\Klicktipp;

use App\Klicktipp\Dates;
use App\Klicktipp\Includes\UtilsNumber;
use App\Klicktipp\Libraries;
use App\Klicktipp\TestData\TestUser;
use App\Tests\Integration\Helpers\LanguageAware;
use Doctrine\DBAL\Connection;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Reference;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\VarPrivacy;
use App\Klicktipp\Lists;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\Tag;
use App\Klicktipp\Subscription;
use App\Klicktipp\Personalization;

class CustomFieldsTest extends KernelTestCase
{
    use LanguageAware;

    /**
     * @var Connection
     */
    private $connection;

    protected function setUp(): void
    {
        $GLOBALS['app'] = self::bootKernel();
        $this->connection = $this->getContainer()->get('database_connection');
        $this->setUpTestLanguages();
    }

    public function tearDown(): void
    {
        $this->connection = null;
    }

    public function testDrupalSimpleTest()
    {
        Libraries::include('full_user.inc', '/tests/includes');

        $testUser = new TestUser($this->connection);
        $testUser->getAnonymousUser();
        $testUser->getEnterpriseUser();
        $testUser->getStandardUser();


        $ReferenceID = 0;

        // test static functions

        /**
         * Returns custom field options as an array
         */
        $message = 'CustomFields::GetOptionsAsArray';
        $ArrayCustomFieldOptions = CustomFields::GetOptionsAsArray('[[Turkey]||[tr]]*,,,[[Germany]||[de]]');
        // expected:
        // Array ( [0] => Array ( [label] => Turkey [value] => tr [is_selected] => true ) [1] => Array ( [label] => Germany [value] => de [is_selected] => false ) )
        $this->assertTrue(is_array($ArrayCustomFieldOptions), $message . " is_array");
        $this->assertTrue($ArrayCustomFieldOptions[0]['is_selected'] == 'true', $message . " is_selected");
        $this->assertTrue($ArrayCustomFieldOptions[1]['value'] == 'de', $message . " value == 'de'");

        /**
         * Returns custom field option as a formatted string
         */
        $message = 'CustomFields::GetOptionAsString';
        $ArrayCustomFieldOptionString = CustomFields::GetOptionAsString('Germany', 'de', true);
        // expected:
        // [[Germany]||[de]]*
        $this->assertTrue($ArrayCustomFieldOptionString == '[[Germany]||[de]]*', $message);

        /**
         * Returns custom field option form values as a formatted string
         */
        $message = 'CustomFields::GetSelectedOptionsString';
        $ArrayCustomFieldOptionString = CustomFields::GetSelectedOptionsString(
            '[[Turkey]||[tr]],,,[[Germany]||[de]],,,[[Brazil]||[br]]',
            '0,2'
        );
        // expected (select 1st and 3rd option):
        // [[Turkey]||[tr]]*,,,[[Germany]||[de]],,,[[Brazil]||[br]]*
        $this->assertTrue(
            $ArrayCustomFieldOptionString == '[[Turkey]||[tr]]*,,,[[Germany]||[de]],,,[[Brazil]||[br]]*',
            $message
        );

        /**
         * Returns html code of a custom field
         **/
        $message = 'CustomFields::GenerateCustomFieldHTMLCode';
        $ArrayCustomFieldInformation = array(
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'CustomFieldID' => '4711',
            'FieldDefaultValue' => 'default',
        );
        $CustomFieldHTMLCode = CustomFields::GenerateCustomFieldHTMLCode($ArrayCustomFieldInformation, '', '', true);
        // expected: no value because $minimalversion = TRUE
        // <input type="text" name="FormValue_Fields[CustomField4711]" value="" id="FormValue_CustomField4711" /><br />
        $this->assertTrue(
            $CustomFieldHTMLCode == '<input type="text" name="FormValue_Fields[CustomField4711]" value="" id="FormValue_CustomField4711" /><br />',
            $message
        );

        /**
         * Validates the provided value against custom field validation rule
         */

        $emailAddress = 'tast@exämple.com';

        $message = 'CustomFields::ValidateCustomFieldValue';
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_EMAIL),
            $emailAddress
        );
        $this->assertTrue($result[0], $message . ' email');
        $result = CustomFields::ValidateCustomFieldValue(array('FieldTypeEnum' => CustomFields::TYPE_TIME), '17:11');
        $this->assertFalse($result[0], $message . ' time');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_TIME),
            '17:11',
            true
        );
        $this->assertTrue($result[0], $message . ' time');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_EMAIL),
            'testexample.com'
        );
        $this->assertFalse($result[0], $message . ' email fail');
        $result = CustomFields::ValidateCustomFieldValue(array('FieldTypeEnum' => CustomFields::TYPE_TIME), 'hugo');
        $this->assertFalse($result[0], $message . ' time fail');
        $result = CustomFields::ValidateCustomFieldValue(array('FieldTypeEnum' => CustomFields::TYPE_SINGLE), 'hugo');
        $this->assertTrue($result[0], $message . ' no rule');
        $result = CustomFields::ValidateCustomFieldValue(array('FieldTypeEnum' => CustomFields::TYPE_EMAIL), '');
        $this->assertTrue($result[0], $message . ' empty');

        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_URL),
            'http://www.example.com'
        );
        $this->assertTrue($result[0], $message . ' http');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_URL),
            'https://api.example.com'
        );
        $this->assertTrue($result[0], $message . ' https');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_URL),
            'example.com'
        );
        $this->assertTrue(!$result[0], $message . ' empty scheme');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_URL),
            '//example.com'
        );
        $this->assertTrue(!$result[0], $message . ' empty scheme');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_URL),
            'ftp://example.com'
        );
        $this->assertTrue($result[0], $message . ' ftp scheme');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_URL),
            'feed://example.com'
        );
        $this->assertTrue($result[0], $message . ' feed scheme');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_URL),
            'file://example.com'
        );
        $this->assertTrue(!$result[0], $message . ' fail file scheme');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_URL),
            '://example.com'
        );
        $this->assertTrue(!$result[0], $message . ' fail scheme');
        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_URL),
            'this is no url'
        );
        $this->assertTrue(!$result[0], $message . ' no url');


        //--- TestCase:: Validate max length of custom field value
        //Note: max length is determined by DB (field type text == 64k)
        //Note: strlen() counts bytes, so 1 multibyte character of 3 bytes is counted as 3

        $message = "CustomFields::ValidateCustomFieldValue MaxLength:";

        $maxLengthValue = str_repeat('x', CustomFields::VALUE_MAX_LENGTH);

        $mbChar = "\u{8A66}"; //some japanese kanji
        $mb = strlen($mbChar); //3 bytes

        $maxLengthValueMB = str_repeat($mbChar, floor(CustomFields::VALUE_MAX_LENGTH / strlen($mbChar)));

        $invalidLengthValue = $maxLengthValue . '#';
        $invalidLengthValueMB = $maxLengthValueMB . '#';

        $UserID = 1;
        $SubscriberID = 10000;

        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_SINGLE),
            $maxLengthValue
        );
        $this->assertTrue($result[0] === true, "$message max length value" . $result[1]);

        $ArrayFirstName = CustomFields::$GlobalCustomFieldDefs['FirstName'];

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, 0, $ArrayFirstName, $maxLengthValue);

        $value = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $ArrayFirstName, 0, '');
        $this->assertTrue($value == $maxLengthValue, "$message max value stored in DB");

        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_SINGLE),
            $invalidLengthValue
        );
        $this->assertTrue($result[0] === false, $message . $result[1]);

        // ---

        $message = "CustomFields::ValidateCustomFieldValue MaxLength (MultiByte):";

        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_SINGLE),
            $maxLengthValueMB
        );
        $this->assertTrue($result[0] === true, "$message max length value" . $result[1]);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, 0, $ArrayFirstName, $maxLengthValueMB);

        $value = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $ArrayFirstName, 0, '');
        $this->assertTrue($value == $maxLengthValueMB, "$message max value stored in DB");

        $result = CustomFields::ValidateCustomFieldValue(
            array('FieldTypeEnum' => CustomFields::TYPE_SINGLE),
            $invalidLengthValueMB
        );
        $this->assertTrue($result[0] === false, $message . $result[1]);

        //clean up
        db_query(
            "DELETE FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID",
            array(':RelOwnerUserID' => $UserID)
        );


        /*
         define ('CustomFields::TYPE_SINGLE', 1);x
         define ('CustomFields::TYPE_PARAGRAPH', 2);
         define ('CustomFields::TYPE_DROPDOWN', 3);
         define ('CustomFields::TYPE_CHECKBOX', 4);
         define ('CustomFields::TYPE_EMAIL', 5);
         define ('CustomFields::TYPE_NUMBER', 6);
         define ('CustomFields::TYPE_URL', 7);
         define ('CustomFields::TYPE_DATE', 8);
         define ('CustomFields::TYPE_TIME', 9);
         */

        /**
         * Creates a new custom field with given values
         **/
        $message = 'CustomFields::Create';
        // Single line - no validation
        $ArrayFieldAndValues = array(
            'FieldName' => 'Line',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertFalse($NewCustomFieldID, $message . ' no owner');
        // Single line - no validation
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Line',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $NewCustomFieldID1 = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($NewCustomFieldID1 > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        // Email
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Email',
            'FieldTypeEnum' => CustomFields::TYPE_EMAIL,
            'MultiValue' => 0,
        );
        $NewCustomFieldID2 = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID2 == $NewCustomFieldID1 + 1,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );
        // Number
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Number',
            'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
        );
        $NewCustomFieldID3 = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID3 == $NewCustomFieldID1 + 2,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );
        // Decimal
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Decimal',
            'FieldTypeEnum' => CustomFields::TYPE_DECIMAL,
        );
        $NewCustomFieldID3b = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID3b == $NewCustomFieldID1 + 3,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );
        // URL
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'URL',
            'FieldTypeEnum' => CustomFields::TYPE_URL,
            'FieldParameters' => array(
                'FieldCategory' => 'category',
                'FacebookName' => 'facebook name url',
                'RequestsName' => 'requests name url',
            ),
        );
        $NewCustomFieldID4 = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID4 == $NewCustomFieldID1 + 4,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );
        // Date
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Date',
            'FieldTypeEnum' => CustomFields::TYPE_DATE,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID == $NewCustomFieldID1 + 5,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );
        // Time
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Time',
            'FieldTypeEnum' => CustomFields::TYPE_TIME,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID == $NewCustomFieldID1 + 6,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );
        // Paragraph
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Paragraph',
            'FieldTypeEnum' => CustomFields::TYPE_PARAGRAPH,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID == $NewCustomFieldID1 + 7,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );
        // Dropdown
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Dropdown',
            'FieldTypeEnum' => CustomFields::TYPE_DROPDOWN,
            'FieldOptions' => '[[Turkey]||[tr]]*,,,[[Germany]||[de]]',
            'FieldParameters' => array(
                'FieldCategory' => 'category2',
            ),
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID == $NewCustomFieldID1 + 8,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );
        // Checkbox
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Checkbox',
            'FieldTypeEnum' => CustomFields::TYPE_CHECKBOX,
            'FieldOptions' => '[[Turkey]||[tr]]*,,,[[Germany]||[de]]',
            'FieldParameters' => array(
                'FieldCategory' => 'category',
                'FacebookName' => 'facebook name checkbox',
                'RequestsName' => 'requests name checkbox',
            ),
        );
        $NewCustomFieldID9 = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID9 == $NewCustomFieldID1 + 9,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );

        // FieldName already exists
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Checkbox',
            'FieldTypeEnum' => CustomFields::TYPE_CHECKBOX,
            'FieldOptions' => '[[Turkey]||[tr]]*,,,[[Germany]||[de]]',
            'FieldParameters' => array(
                'FieldCategory' => 'category',
                'FacebookName' => 'facebook name checkbox',
                'RequestsName' => 'requests name checkbox',
            ),
        );
        $NewCustomFieldID10 = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID10 === false,
            $message . ' (FieldName already exists) ' . $ArrayFieldAndValues['FieldName']
        );

        // Datetime
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'Datetime',
            'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
        );
        $NewCustomFieldID11 = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID11 == $NewCustomFieldID1 + 10,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );

        // HTML
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'HTML',
            'FieldTypeEnum' => CustomFields::TYPE_HTML,
            'FieldParameters' => array(
                'FieldCategory' => 'category',
                'FacebookName' => 'facebook name html',
                'RequestsName' => 'requests name html',
            ),
        );
        $NewCustomFieldID12 = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(
            $NewCustomFieldID12 == $NewCustomFieldID1 + 11,
            $message . ' ' . $ArrayFieldAndValues['FieldName']
        );

        $TotalCustomFields = db_query(
            "SELECT COUNT(*) FROM {custom_fields} WHERE RelOwnerUserID = :RelOwnerUserID",
            array(':RelOwnerUserID' => 1)
        )->fetchField();
        $this->assertTrue(
            $TotalCustomFields == 12,
            $message . ' count'
        ); //$NewCustomFieldID10 was not created, expected count: 12

        /**
         * Returns a single custom field matching given criterias
         **/
        $message = 'CustomFields::RetrieveCustomField';
        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID4, 1);
        $this->assertTrue($ArrayCustomFieldInformation['FieldName'] == 'URL', $message . ' by id');
        $this->assertTrue($ArrayCustomFieldInformation['FieldTypeEnum'] == CustomFields::TYPE_URL, $message . ' by id');
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldParameters']['FacebookName'] == 'facebook name url',
            $message . ' FieldParameters retrieve unserialized'
        );

        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomField('FirstName', 1);
        $this->assertTrue($ArrayCustomFieldInformation['FieldName'] == 'Firstname', $message . ' global field');
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldTypeEnum'] == CustomFields::TYPE_SINGLE,
            $message . ' global field'
        );

        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID4, 4711);
        $this->assertTrue($ArrayCustomFieldInformation === false, $message . ' check ownership');

        $message = 'CustomFields::RetrieveCustomFieldByName Requests';
        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomFieldByName(
            CustomFields::REQUESTTYPE_REQUESTS,
            'requests name checkbox',
            1
        );
        $this->assertTrue($ArrayCustomFieldInformation['CustomFieldID'] == $NewCustomFieldID9, $message . ' id');
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldTypeEnum'] == CustomFields::TYPE_CHECKBOX,
            $message . ' type'
        );
        $ArrayOptions = CustomFields::GetOptionsAsArray($ArrayCustomFieldInformation['FieldOptions']);
        $this->assertTrue(is_array($ArrayOptions), $message . ' options');
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldParameters']['FacebookName'] == 'facebook name checkbox',
            $message . ' FieldParameters retrieve unserialized'
        );

        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomFieldByName(
            CustomFields::REQUESTTYPE_REQUESTS,
            'Vorname',
            1
        );
        $this->assertTrue($ArrayCustomFieldInformation['CustomFieldID'] == 'FirstName', $message . ' global field');
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldTypeEnum'] == CustomFields::TYPE_SINGLE,
            $message . ' global field by name'
        );

        $message = 'CustomFields::RetrieveCustomFieldByName Facebook';
        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomFieldByName(
            CustomFields::REQUESTTYPE_FACEBOOK,
            'facebook name checkbox',
            1
        );
        $this->assertTrue($ArrayCustomFieldInformation['CustomFieldID'] == $NewCustomFieldID9, $message . ' id');
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldTypeEnum'] == CustomFields::TYPE_CHECKBOX,
            $message . ' type'
        );
        $ArrayOptions = CustomFields::GetOptionsAsArray($ArrayCustomFieldInformation['FieldOptions']);
        $this->assertTrue(is_array($ArrayOptions), $message . ' options');
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldParameters']['FacebookName'] == 'facebook name checkbox',
            $message . ' FieldParameters retrieve unserialized'
        );

        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomFieldByName(
            CustomFields::REQUESTTYPE_FACEBOOK,
            'first_name',
            1
        );
        $this->assertTrue($ArrayCustomFieldInformation['CustomFieldID'] == 'FirstName', $message . ' global field');
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldTypeEnum'] == CustomFields::TYPE_SINGLE,
            $message . ' global field by name'
        );

        /**
         * Returns all custom fields matching given criterias
         **/
        $message = 'CustomFields::RetrieveCustomFields';
        $ArrayCustomFields = CustomFields::RetrieveCustomFields(1, null, null, true);
        $this->assertTrue(
            count($ArrayCustomFields) == $TotalCustomFields + count(CustomFields::$GlobalCustomFieldDefs),
            $message . ' count'
        );
        $this->assertTrue($ArrayCustomFields[0]['FieldName'] == 'Line', $message . ' first');
        $packed4 = unserialize($ArrayCustomFields[4]['FieldParameters']);
        $packed9 = unserialize($ArrayCustomFields[9]['FieldParameters']);
        $this->assertTrue(
            ($packed4['FieldCategory'] == 'category') && ($packed9['FieldCategory'] == 'category'),
            $message . ' (all) FieldParameters retrieve serialized'
        );

        /**
         * Returns all custom fields categorized matching given criterias
         **/
        $message = 'CustomFields::RetrieveCustomFieldsCategories';
        $ArrayCustomFieldsUnpacked = CustomFields::RetrieveCustomFieldsCategories(1, null, true);
        $this->assertTrue(
            count($ArrayCustomFieldsUnpacked['AllCustomFields']) == $TotalCustomFields + count(
                CustomFields::$GlobalCustomFieldDefs
            ),
            $message . ' count'
        );
        $this->assertTrue(count($ArrayCustomFieldsUnpacked['CategoryNames']) == 2, $message . ' count categories = 2');
        $this->assertTrue(
            in_array('category', $ArrayCustomFieldsUnpacked['CategoryNames']) && in_array(
                'category2',
                $ArrayCustomFieldsUnpacked['CategoryNames']
            ),
            $message . ' both categories found'
        );
        $this->assertTrue(
            (count($ArrayCustomFieldsUnpacked['CategoryNames']) + 1) == count(
                $ArrayCustomFieldsUnpacked['CategoryFields']
            ),
            $message . ' count categorized fields array'
        ); //+1 == general category
        $match_category = $ArrayCustomFieldsUnpacked['AllCustomFields'][$NewCustomFieldID9]['FieldParameters']['FieldCategory'];
        $match_name = $ArrayCustomFieldsUnpacked['AllCustomFields'][$NewCustomFieldID9]['FieldName'];
        $this->assertTrue(
            $ArrayCustomFieldsUnpacked['CategoryFields'][$match_category][$NewCustomFieldID9]['FieldName'] == $match_name,
            $message . ' match cetegorized field'
        );

        /**
         * Updates custom field information
         **/
        $message = 'CustomFields::Update';

        $ArrayFieldAndValues = array(
            'FieldName' => 'Email changed',
            'FieldTypeEnum' => CustomFields::TYPE_URL,
            'FieldCopyTo' => $NewCustomFieldID2, // same ID as the field, should be reset to ''
            'MultiValue' => 1,
            'FieldParameters' => array(
                'FieldCategory' => 'update category',
                'FacebookName' => 'update facebook name',
                'RequestsName' => 'update requests name',
            ),
        );

        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID2);
        $this->assertTrue(
            $ArrayCustomFieldInformation['MultiValue'] == 0,
            $message . ' field is single value ' . $ArrayCustomFieldInformation['FieldName']
        );

        $result = CustomFields::Update($ArrayFieldAndValues, $NewCustomFieldID2);
        $this->assertTrue($result, $message . ' ' . $ArrayCustomFieldInformation['FieldName']);
        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID2);
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldName'] == 'Email changed',
            $message . ' Name ' . $ArrayCustomFieldInformation['FieldName']
        );
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldTypeEnum'] == CustomFields::TYPE_URL,
            $message . ' Type ' . $ArrayCustomFieldInformation['FieldName']
        );
        $this->assertTrue(
            $ArrayCustomFieldInformation['MultiValue'] == 1,
            $message . ' field is now multi value ' . $ArrayCustomFieldInformation['FieldName']
        );
        $this->assertTrue(
            $ArrayCustomFieldInformation['FieldParameters']['RequestsName'] == 'update requests name',
            $message . ' FieldParameters'
        );
        $this->assertTrue(
            ($ArrayFieldAndValues['FieldCopyTo'] == $NewCustomFieldID2) && ($ArrayCustomFieldInformation['FieldCopyTo'] == ''),
            $message . ' prevent FieldCopyTo == CustomFieldID'
        );

        $result = CustomFields::Update($ArrayFieldAndValues, 'Firstname');
        $this->assertFalse($result, $message . ' global field');

        /**
         * Read and write Custom Field Data
         **/

        $message = 'CustomFields::Get+UpdateCustomFieldData';
        $UserID = 2;
        $SubscriberID = 4711;
        $FieldName = 'Testfield';
        $ReferenceID4712 = 4712;

        // just a text
        $FieldTypeEnum = CustomFields::TYPE_SINGLE;
        $Value = 'this is a string value';

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => $FieldName . $FieldTypeEnum,
            'FieldTypeEnum' => $FieldTypeEnum,
            'MultiValue' => 0,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($NewCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        $FieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID);

        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === '', $message . ' get empty ' . $ArrayFieldAndValues['FieldName']);

        $ArrayMVFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => "$FieldName MV $FieldTypeEnum",
            'FieldTypeEnum' => $FieldTypeEnum,
            'MultiValue' => 1,
        );
        $MVCustomFieldID = CustomFields::Create($ArrayMVFieldAndValues);
        $this->assertTrue($MVCustomFieldID > 0, $message . ' ' . $ArrayMVFieldAndValues['FieldName']);
        $MVFieldInformation = CustomFields::RetrieveCustomField($MVCustomFieldID);

        // insert single value
        $subscriberStateChanged = CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FieldInformation,
            $Value
        );
        $this->assertTrue($subscriberStateChanged, "$message initial insert, subscriber status changed");
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === $Value, "$message get value {$ArrayFieldAndValues['FieldName']} $ReferenceID");
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID4712);
        $this->assertTrue($Data === $Value, "$message get value {$ArrayFieldAndValues['FieldName']} $ReferenceID4712");

        // same with multi value
        CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $MVFieldInformation,
            "$Value$ReferenceID"
        );
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $MVFieldInformation, $ReferenceID);
        $this->assertTrue(
            $Data === "$Value$ReferenceID",
            "$message get value {$ArrayMVFieldAndValues['FieldName']} $ReferenceID"
        );
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $MVFieldInformation, $ReferenceID4712);
        $this->assertTrue(empty($Data), "$message get value {$ArrayMVFieldAndValues['FieldName']} $ReferenceID4712");

        // add second multi value
        CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberID,
            $ReferenceID4712,
            $MVFieldInformation,
            "$Value$ReferenceID4712"
        );
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $MVFieldInformation, $ReferenceID4712);
        $this->assertTrue(
            $Data === "$Value$ReferenceID4712",
            "$message get value {$ArrayMVFieldAndValues['FieldName']} $ReferenceID4712"
        );

        // check caching
        db_query(
            "UPDATE {custom_field_values} SET ValueText = CONCAT('###', ValueText) WHERE RelOwnerUserID = :RelOwnerUserID AND " .
            " RelSubscriberID = :RelSubscriberID AND RelFieldID IN (:RelFieldID)",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':RelFieldID' => [$FieldInformation['CustomFieldID'], $MVFieldInformation['CustomFieldID']]
            )
        );
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID4712);
        $this->assertTrue($Data == $Value, $message . ' value unchanged ' . $ArrayFieldAndValues['FieldName']);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $MVFieldInformation, $ReferenceID4712);
        $this->assertTrue(
            $Data == "$Value$ReferenceID4712",
            "$message value unchanged {$ArrayMVFieldAndValues['FieldName']} $ReferenceID4712"
        );
        CustomFields::GetCustomFieldData($UserID, $SubscriberID, [], $ReferenceID, '', true);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID4712);
        $this->assertTrue($Data != $Value, $message . ' value changed ' . $ArrayFieldAndValues['FieldName']);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $MVFieldInformation, $ReferenceID4712);
        $this->assertTrue(
            $Data == "###$Value$ReferenceID4712",
            "$message value changed {$ArrayMVFieldAndValues['FieldName']} $ReferenceID4712"
        );

        // update single value
        $Value2 = 'abc';
        $subscriberStateChanged = CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FieldInformation,
            "$Value2$ReferenceID"
        );
        $this->assertTrue($subscriberStateChanged, "$message update single value, subscriber status changed");
        $subscriberStateChanged = CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FieldInformation,
            "$Value2$ReferenceID"
        );
        $this->assertFalse($subscriberStateChanged, "$message update with same value, subscriber status unchanged");
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue(
            $Data === "$Value2$ReferenceID",
            $message . ' get value ' . $ArrayFieldAndValues['FieldName']
        );
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID4712);
        $this->assertTrue(
            $Data === "$Value2$ReferenceID",
            $message . ' get value ' . $ArrayFieldAndValues['FieldName']
        );
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID4712, $FieldInformation, $Value2);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === $Value2, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        // update multi value
        CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $MVFieldInformation,
            "$Value2$ReferenceID"
        );
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $MVFieldInformation, $ReferenceID);
        $this->assertTrue(
            $Data === "$Value2$ReferenceID",
            $message . ' get value ' . $ArrayFieldAndValues['FieldName']
        );
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $MVFieldInformation, $ReferenceID4712);
        $this->assertTrue($Data != "$Value2$ReferenceID", $message . ' get value ' . $ArrayFieldAndValues['FieldName']);
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID4712, $MVFieldInformation, $Value2);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $MVFieldInformation, $ReferenceID);
        $this->assertTrue(
            $Data === "$Value2$ReferenceID",
            $message . ' get value ' . $ArrayFieldAndValues['FieldName']
        );
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $MVFieldInformation, $ReferenceID4712);
        $this->assertTrue($Data === $Value2, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        // update with invalid utf-8, see https://www.drupal.org/node/1929862
        $Value3 = "\xD0\xCF\x11\xE0\xA1\xB1...";
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value3);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data != $Value3, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);
        $this->assertTrue(
            strlen($Data) === strlen($Value3),
            $message . ' get value ' . $ArrayFieldAndValues['FieldName']
        );

        // update with (empty) 0 value
        $Value4 = '0';
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value4);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === $Value4, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        // delete
        $subscriberStateChanged = CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FieldInformation,
            ''
        );
        $this->assertTrue($subscriberStateChanged, "$message deletion of a set value, subscriber status changed");
        $subscriberStateChanged = CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FieldInformation,
            ''
        );
        $this->assertFalse($subscriberStateChanged, "$message deletion of an unset value, subscriber status unchanged");

        $data = db_query(
            "SELECT 1 FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID = :RelFieldID LIMIT 0,1",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':RelFieldID' => $FieldInformation['CustomFieldID']
            )
        )->fetchField();
        $this->assertTrue(empty($data), $message . ' value deleted ' . $ArrayFieldAndValues['FieldName']);

        // number
        $FieldTypeEnum = CustomFields::TYPE_NUMBER;
        $Value = 123;

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => $FieldName . $FieldTypeEnum,
            'FieldTypeEnum' => $FieldTypeEnum,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($NewCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        $FieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID);

        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === '', $message . ' get empty ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data == $Value, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        // update with (empty) 0 value
        $Value2 = 0;
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value2);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        // we do not store 0 as a number value
        $this->assertTrue($Data == $Value2, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, '');
        $data = db_query(
            "SELECT 1 FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID = :RelFieldID LIMIT 0,1",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':RelFieldID' => $FieldInformation['CustomFieldID']
            )
        )->fetchField();
        $this->assertTrue($data === false, $message . ' value deleted ' . $ArrayFieldAndValues['FieldName']);

        // decimal
        $FieldTypeEnum = CustomFields::TYPE_DECIMAL;
        $Value = 432;

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => $FieldName . $FieldTypeEnum,
            'FieldTypeEnum' => $FieldTypeEnum,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($NewCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        $FieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID);

        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === '', $message . ' get empty ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data == $Value, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        // update with (empty) 0 value
        $Value2 = 0;
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value2);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        // we do not store 0 as a number value
        $this->assertTrue($Data == $Value2, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, '');
        $data = db_query(
            "SELECT 1 FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID = :RelFieldID LIMIT 0,1",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':RelFieldID' => $FieldInformation['CustomFieldID']
            )
        )->fetchField();
        $this->assertTrue($data === false, $message . ' value deleted ' . $ArrayFieldAndValues['FieldName']);

        // date
        $FieldTypeEnum = CustomFields::TYPE_DATE;
        $d = time();
        $Valuefromwidget = date("Y-m-d", $d);

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => $FieldName . $FieldTypeEnum,
            'FieldTypeEnum' => $FieldTypeEnum,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($NewCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        $FieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID);

        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === '', $message . ' get empty ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Valuefromwidget);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === $Valuefromwidget, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, '');
        $data = db_query(
            "SELECT 1 FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID = :RelFieldID LIMIT 0,1",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':RelFieldID' => $FieldInformation['CustomFieldID']
            )
        )->fetchField();
        $this->assertTrue(empty($data), $message . ' value deleted ' . $ArrayFieldAndValues['FieldName']);

        // time
        $FieldTypeEnum = CustomFields::TYPE_TIME;
        $Value = date('H:i:s');

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => $FieldName . $FieldTypeEnum,
            'FieldTypeEnum' => $FieldTypeEnum,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($NewCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        $FieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID);

        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === '', $message . ' get empty ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === $Value, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, '');
        $data = db_query(
            "SELECT 1 FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID = :RelFieldID LIMIT 0,1",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':RelFieldID' => $FieldInformation['CustomFieldID']
            )
        )->fetchField();
        $this->assertTrue(empty($data), $message . ' value deleted ' . $ArrayFieldAndValues['FieldName']);

        // datetime
        $FieldTypeEnum = CustomFields::TYPE_DATETIME;
        $Value = time();

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => $FieldName . $FieldTypeEnum,
            'FieldTypeEnum' => $FieldTypeEnum,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($NewCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        $FieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID);

        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === '', $message . ' get empty ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data == $Value, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, '');
        $data = db_query(
            "SELECT 1 FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID = :RelFieldID LIMIT 0,1",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':RelFieldID' => $FieldInformation['CustomFieldID']
            )
        )->fetchField();
        $this->assertTrue(empty($data), $message . ' value deleted ' . $ArrayFieldAndValues['FieldName']);

        // html
        $FieldTypeEnum = CustomFields::TYPE_HTML;
        $Value = "<h3 class=\"text-centered subheadline\">Die erste lernende E-Mail-Marketing-Lösung. Mit bahnbrechendem Tagging-System.<br>Zuverlässige und seriöse E-Mails. Zur richtigen Zeit bei den richtigen Menschen.<br>Wir haben viel in Klick-Tipp gesteckt. Damit es für Sie noch mehr herausholt.</h3>";

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => $FieldName . $FieldTypeEnum,
            'FieldTypeEnum' => $FieldTypeEnum,
        );
        $NewCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($NewCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        $FieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID);

        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data === '', $message . ' get empty ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value);
        $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $this->assertTrue($Data == $Value, $message . ' get value ' . $ArrayFieldAndValues['FieldName']);

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, '');
        $data = db_query(
            "SELECT 1 FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID = :RelFieldID LIMIT 0,1",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':RelFieldID' => $FieldInformation['CustomFieldID']
            )
        )->fetchField();
        $this->assertTrue(empty($data), $message . ' value deleted ' . $ArrayFieldAndValues['FieldName']);

        // global field
        $ValueString = 'this is global field data';
        $ValueArrayKey = "some key";
        foreach (CustomFields::$GlobalCustomFieldDefs as $NewCustomFieldID => $GlobalFieldDef) {
            $FieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID);

            // as arrays have multiple values, parameters change
            $Value = $ValueString . ' in ' . $FieldInformation['FieldName'];
            if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_ARRAY) {
                $key = $ValueArrayKey;
                $ValueArray[$key] = $Value;
                $ValueForUpdate = $ValueArray;
            } else {
                $key = '';
                $ValueForUpdate = $Value;
            }

            // check empty
            $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID, $key);
            $this->assertTrue(
                $Data === '',
                $message . ' check empty ' . $FieldInformation['FieldName'] . print_r($FieldInformation, 1) . print_r(
                    $Data,
                    1
                )
            );

            // update
            CustomFields::UpdateCustomFieldData(
                $UserID,
                $SubscriberID,
                $ReferenceID,
                $FieldInformation,
                $ValueForUpdate
            );

            // check changes
            $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID, $key);
            $this->assertTrue($Data === $Value, $message . ' get value ' . $FieldInformation['FieldName']);
        }

        foreach (CustomFields::$GlobalCustomFieldDefs as $NewCustomFieldID => $GlobalFieldDef) {
            $FieldInformation = CustomFields::RetrieveCustomField($NewCustomFieldID);

            // clear field
            CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, '');
            $Data = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
            $this->assertTrue(empty($data), $message . ' value deleted ' . $FieldInformation['FieldName']);
        }

        //FieldCopyTo -> UpdateCustomFieldData($UserID, $SubscriberID, $FieldInformation, $Value )
        //Clear values for FirstName and LastName
        $message = "UpdateData+FieldCopyTo:";
        $CF_FirstName = CustomFields::RetrieveCustomField('FirstName', $UserID);
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $CF_FirstName, '');
        $CF_FirstNameValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $CF_FirstName, $ReferenceID);
        $this->assertTrue(empty($CF_FirstNameValue), "$message is empty FirstName");

        //create custom field
        $ArrayCopyToField = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'CopyToField',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'FieldCopyTo' => 'FirstName',
        );
        $CopyToFieldID = CustomFields::Create($ArrayCopyToField);
        $CF_CopyToField = CustomFields::RetrieveCustomField($CopyToFieldID, $UserID);
        $this->assertTrue($CopyToFieldID > 0, $message . ' CustomField created ' . $ArrayCopyToField['FieldName']);
        $this->assertTrue(
            $CF_CopyToField['FieldCopyTo'] == $ArrayCopyToField['FieldCopyTo'],
            $message . ' CopyToField set to ' . $ArrayCopyToField['FieldCopyTo']
        );

        $CopyValue = 'FirstName';
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $CF_CopyToField, $CopyValue);
        $CF_CopyToFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $CF_CopyToField, $ReferenceID);
        $CF_FirstNameValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $CF_FirstName, $ReferenceID);
        $this->assertTrue($CF_CopyToFieldValue == $CopyValue, "$message Value updated to $CopyValue");
        $this->assertTrue(
            $CF_FirstNameValue == $CopyValue,
            "$message Value $CopyValue copied to " . $CF_FirstName['FieldName']
        );

        //FirstName now has a value and should not be overwritten
        $CopyValueChanged = 'FirstName changed';
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $CF_CopyToField, $CopyValueChanged);
        $CF_CopyToFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $CF_CopyToField, $ReferenceID);
        $CF_FirstNameValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $CF_FirstName, $ReferenceID);
        $this->assertTrue($CF_CopyToFieldValue == $CopyValueChanged, "$message Value updated to $CopyValueChanged");
        $this->assertTrue(
            $CF_FirstNameValue == $CopyValue,
            "$message Value $CopyValueChanged NOT copied to " . $CF_FirstName['FieldName']
        );


        $singleValueFieldData_I = [
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'CopyToFieldSV_I',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
        ];
        $singleValueFieldID_I = CustomFields::Create($singleValueFieldData_I);

        $singleValueFieldData_II = [
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'CopyToFieldSV_II',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
        ];
        $singleValueFieldID_II = CustomFields::Create($singleValueFieldData_II);

        $multiValueFieldData_I = [
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'CopyToFieldMV_I',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 1,
        ];
        $multiValueFieldID_I = CustomFields::Create($multiValueFieldData_I);

        $multiValueFieldData_II = [
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'CopyToFieldMV_II',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 1,
        ];
        $multiValueFieldID_II = CustomFields::Create($multiValueFieldData_II);

        // setting copy fields
        CustomFields::Update(['FieldCopyTo' => $singleValueFieldID_II], $singleValueFieldID_I);
        CustomFields::Update(['FieldCopyTo' => $multiValueFieldID_I], $singleValueFieldID_II);
        CustomFields::Update(['FieldCopyTo' => $multiValueFieldID_II], $multiValueFieldID_I);
        CustomFields::Update(['FieldCopyTo' => $singleValueFieldID_I], $multiValueFieldID_II);


        // COPY: single value field --> single value field

        $message = "Copy Field Value [Single Value -> Single Value]: ";
        $ReferenceID = 0;

        $copyValue = "appearsInSvII";
        $singleValueField_I = CustomFields::RetrieveCustomField($singleValueFieldID_I, $UserID);
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $singleValueField_I, $copyValue);

        $singleValueField_II = CustomFields::RetrieveCustomField($singleValueFieldID_II, $UserID);
        $singleValue_II = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $singleValueField_II, $ReferenceID);

        $this->assertTrue($singleValueField_I['MultiValue'] == 0, $message . "first  field is single value");
        $this->assertTrue($singleValueField_II['MultiValue'] == 0, $message . "second field is single value");
        $this->assertTrue($singleValue_II === $copyValue, $message . " value for ReferenceID=$ReferenceID OK");

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $singleValueField_I, null);

        $singleValue_I = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $singleValueField_I, $ReferenceID);
        $this->assertTrue(
            empty($singleValue_I),
            $message . " single value I was cleared for Reference ID=$ReferenceID"
        );


        // COPY: single value field --> multi value field
        $message = "Copy Field Value [Single Value -> Multi Value]: ";
        $ReferenceID_I = Reference::InsertDB(
            ['RelOwnerUserID' => $UserID, 'NumberRange' => 'simpletests', 'ExtReferenceID' => 'simpletests_1']
        );
        $ReferenceID_II = Reference::InsertDB(
            ['RelOwnerUserID' => $UserID, 'NumberRange' => 'simpletests', 'ExtReferenceID' => 'simpletests_2']
        );
        ;
        $ReferenceID_III = Reference::InsertDB(
            ['RelOwnerUserID' => $UserID, 'NumberRange' => 'simpletests', 'ExtReferenceID' => 'simpletests_3']
        );
        ;

        $copyValue = "appearsInMvI";
        $singleValueField_II = CustomFields::RetrieveCustomField($singleValueFieldID_II, $UserID);
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $singleValueField_II, $copyValue);

        $multiValueField_I = CustomFields::RetrieveCustomField($multiValueFieldID_I, $UserID);
        $multiValue_I = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $multiValueField_I, $ReferenceID);
        $multiValue_I_Ref_I = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberID,
            $multiValueField_I,
            $ReferenceID_I
        );
        $multiValue_I_Ref_II = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberID,
            $multiValueField_I,
            $ReferenceID_II
        );
        $multiValue_I_Ref_III = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberID,
            $multiValueField_I,
            $ReferenceID_III
        );

        $this->assertTrue($singleValueField_II['MultiValue'] == 0, $message . "first field is single value");
        $this->assertTrue($multiValueField_I['MultiValue'] == 1, $message . "second field is multi value");
        $this->assertTrue($multiValue_I === $copyValue, $message . " value for ReferenceID=$ReferenceID OK");
        $this->assertTrue($multiValue_I_Ref_I === $copyValue, $message . " value for ReferenceID=$ReferenceID_I OK");
        $this->assertTrue($multiValue_I_Ref_II === $copyValue, $message . " value for ReferenceID=$ReferenceID_II OK");
        $this->assertTrue(
            $multiValue_I_Ref_III === $copyValue,
            $message . " value for ReferenceID=$ReferenceID_III OK"
        );

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $singleValueField_II, null);
        $singleValue_II = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $singleValueField_II, $ReferenceID);
        $this->assertTrue(
            empty($singleValue_II),
            $message . " single value II was cleared for Reference ID=$ReferenceID"
        );

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID_II, $multiValueField_I, null);
        $multiValue_I_Ref_II = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberID,
            $multiValueField_I,
            $ReferenceID_II
        );
        $this->assertTrue(
            empty($multiValue_I_Ref_II),
            $message . "multi value I was cleared for Reference ID=$ReferenceID_II"
        );


        // COPY: multi value field --> multi value field
        $message = "Copy Field Value [Multi Value -> Multi Value]: ";

        $copyValue = "appearsInMvII";
        $multiValueField_I = CustomFields::RetrieveCustomField($multiValueFieldID_I, $UserID);
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID_II, $multiValueField_I, $copyValue);

        $multiValueField_II = CustomFields::RetrieveCustomField($multiValueFieldID_II, $UserID);
        $multiValue_II_Ref_I = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberID,
            $multiValueField_II,
            $ReferenceID_I
        );
        $multiValue_II_Ref_II = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberID,
            $multiValueField_II,
            $ReferenceID_II
        );
        $multiValue_II_Ref_III = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberID,
            $multiValueField_II,
            $ReferenceID_III
        );

        $this->assertTrue($multiValueField_I['MultiValue'] == 1, $message . "first field is multi value");
        $this->assertTrue($multiValueField_II['MultiValue'] == 1, $message . "second field is multi value");
        $this->assertTrue($multiValue_II_Ref_I !== $copyValue, $message . "value for ReferenceID=$ReferenceID_I OK");
        $this->assertTrue($multiValue_II_Ref_II === $copyValue, $message . "value for ReferenceID=$ReferenceID_II OK");
        $this->assertTrue(
            $multiValue_II_Ref_III !== $copyValue,
            $message . "value for ReferenceID=$ReferenceID_III OK"
        );

        // COPY: multi value field  --> single value field
        $message = "Copy Field Value [Multi Value -> Single Value]: ";

        $copyValue = "appearsInSvI_Ref_III";
        $multiValueField_II = CustomFields::RetrieveCustomField($multiValueFieldID_II, $UserID);
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID_III, $multiValueField_II, $copyValue);

        $singleValueField_I = CustomFields::RetrieveCustomField($singleValueFieldID_I, $UserID);
        $singleValue_I = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $singleValueField_I, $ReferenceID);

        $this->assertTrue($multiValueField_II['MultiValue'] == 1, $message . "first field is multi value");
        $this->assertTrue($singleValueField_I['MultiValue'] == 0, $message . "second field is single value");
        $this->assertTrue(
            $singleValue_I === $copyValue,
            $message . "value for ReferenceID=$ReferenceID OK, copied from ReferenceID=$ReferenceID_III"
        );

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $singleValueField_I, null);
        $singleValue_I = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $singleValueField_I, $ReferenceID);
        $this->assertTrue(
            empty($singleValue_I),
            $message . " single value I was cleared for Reference ID=$ReferenceID in order to be filled again"
        );

        $copyValue = "appearsInSvI_Ref_I";
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID_I, $multiValueField_II, $copyValue);

        $singleValueField_I = CustomFields::RetrieveCustomField($singleValueFieldID_I, $UserID);
        $singleValue_I = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $singleValueField_I, $ReferenceID);

        $this->assertTrue(
            $singleValue_I === $copyValue,
            $message . "value for ReferenceID=$ReferenceID OK, copied from ReferenceID=$ReferenceID_I"
        );

        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $singleValueField_I, null);
        $singleValue_I = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $singleValueField_I, $ReferenceID);
        $this->assertTrue(
            empty($singleValue_I),
            $message . " single value I was cleared for Reference ID=$ReferenceID in order to be filled again"
        );

        $copyValue = "appearsInSvI";
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $multiValueField_II, $copyValue);

        $singleValueField_I = CustomFields::RetrieveCustomField($singleValueFieldID_I, $UserID);
        $singleValue_I = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $singleValueField_I, $ReferenceID);

        $this->assertTrue(
            $singleValue_I === $copyValue,
            $message . "value for ReferenceID=$ReferenceID OK, copied from ReferenceID=$ReferenceID"
        );

        // check for empty subscriptions by tagging
        $message = 'addReference by CustomFields::UpdateCustomFieldData';

        $FieldInformation = CustomFields::RetrieveCustomField('FirstName');
        $Parameters = array(
            'UserID' => $UserID,
            'ListInformation' => array('ListID' => 0), //import
            'EmailAddress' => '<EMAIL>',
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        );
        $Result = Subscribers::Subscribe($Parameters);
        $this->assertTrue($Result[0], $message . ' add subscriber');
        $SubscriberID8 = $Result[1];

        // subscriptions: email/ref0
        $subsciptionsCount = db_query(
            "SELECT COUNT(*) FROM {subscription} WHERE RelSubscriberID = $SubscriberID8"
        )->fetchField();
        $this->assertTrue($subsciptionsCount == 1, "$message subscriptions $subsciptionsCount");
        $subreferencesCount = db_query(
            "SELECT COUNT(*) FROM {subscription_reference} WHERE RelSubscriberID = $SubscriberID8"
        )->fetchField();
        $this->assertTrue($subreferencesCount == 1, "$message references $subreferencesCount");

        // add a value for an existing subscription/reference
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID8, $ReferenceID, $FieldInformation, $Value);

        // subscriptions: email/ref0
        $subsciptionsCount = db_query(
            "SELECT COUNT(*) FROM {subscription} WHERE RelSubscriberID = $SubscriberID8"
        )->fetchField();
        $this->assertTrue($subsciptionsCount == 1, "$message subscriptions $subsciptionsCount");
        $subreferencesCount = db_query(
            "SELECT COUNT(*) FROM {subscription_reference} WHERE RelSubscriberID = $SubscriberID8"
        )->fetchField();
        $this->assertTrue($subreferencesCount == 1, "$message references $subreferencesCount");

        // add a value for a new reference
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID8, $ReferenceID4712, $FieldInformation, $Value);

        // subscriptions: email/ref0 + empty/ref4712
        $subsciptionsCount = db_query(
            "SELECT COUNT(*) FROM {subscription} WHERE RelSubscriberID = $SubscriberID8"
        )->fetchField();
        $this->assertTrue($subsciptionsCount == 2, "$message subscriptions $subsciptionsCount");
        $subreferencesCount = db_query(
            "SELECT COUNT(*) FROM {subscription_reference} WHERE RelSubscriberID = $SubscriberID8"
        )->fetchField();
        $this->assertTrue($subreferencesCount == 2, "$message references $subreferencesCount");

        // add a value for a existing reference
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID8, $ReferenceID4712, $MVFieldInformation, $Value);

        // subscriptions: email/ref0 + empty/ref4712
        $subsciptionsCount = db_query(
            "SELECT COUNT(*) FROM {subscription} WHERE RelSubscriberID = $SubscriberID8"
        )->fetchField();
        $this->assertTrue($subsciptionsCount == 2, "$message subscriptions $subsciptionsCount");
        $subreferencesCount = db_query(
            "SELECT COUNT(*) FROM {subscription_reference} WHERE RelSubscriberID = $SubscriberID8"
        )->fetchField();
        $this->assertTrue($subreferencesCount == 2, "$message references $subreferencesCount");

        /**
         * Deletes custom fields
         **/
        $message = 'CustomFields::Delete';

        $UserID = 1;

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'CampaignName' => 'Test campaign 1',
            'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_DATETIME,
            'DelayByCustomFieldDatetimeFieldID' => $NewCustomFieldID1,
            'CampaignStatusEnum' => Campaigns::STATUS_SENDING,
        );
        $NewID1 = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($NewID1 > 0, $message . ' campaign 1');
        $AutoResponder = Campaigns::RetrieveCampaignByID($NewID1, $UserID);
        $this->assertTrue(
            $AutoResponder['CampaignStatusEnum'] == Campaigns::STATUS_SENDING,
            $message . ' CampaignStatusEnum'
        );

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'CampaignName' => 'Test campaign 2',
            'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_BIRTHDAY,
            'BirthdayCustomFieldID' => $NewCustomFieldID2,
            'CampaignStatusEnum' => Campaigns::STATUS_SENDING,
        );
        $NewID2 = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($NewID2 > 0, $message . ' campaign 2');

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'CampaignName' => 'Test campaign 3',
            'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY,
            'BirthdayCustomFieldID' => $NewCustomFieldID2,
            'CampaignStatusEnum' => Campaigns::STATUS_SENDING,
        );
        $NewID3 = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($NewID3 > 0, $message . ' campaign 3');
        $AutoResponder = Campaigns::RetrieveCampaignByID($NewID3, $UserID);
        $this->assertTrue(
            $AutoResponder['CampaignStatusEnum'] == Campaigns::STATUS_SENDING,
            $message . ' CampaignStatusEnum 3'
        );

        $TotalCustomFields = db_query(
            "SELECT COUNT(*) FROM {custom_fields} WHERE RelOwnerUserID = :RelOwnerUserID",
            array(':RelOwnerUserID' => $UserID)
        )->fetchField();
        $this->assertTrue($TotalCustomFields == 12, $message . ' by custom id');

        CustomFields::DeleteDB([
            'RelOwnerUserID' => $UserID,
            'CustomFieldID' => $NewCustomFieldID1
        ]);
        CustomFields::DeleteDB([
            'RelOwnerUserID' => $UserID,
            'CustomFieldID' => $NewCustomFieldID2
        ]);

        $TotalCustomFields = db_query(
            "SELECT COUNT(*) FROM {custom_fields} WHERE RelOwnerUserID = :RelOwnerUserID",
            array(':RelOwnerUserID' => $UserID)
        )->fetchField();
        $this->assertTrue($TotalCustomFields == 10, $message . ' by custom id');

        $AutoResponder = Campaigns::RetrieveCampaignByID($NewID1, $UserID);
        $this->assertTrue(
            $AutoResponder['CampaignStatusEnum'] == Campaigns::STATUS_READY,
            $message . ' CampaignStatusEnum 1 #' . $NewCustomFieldID1 . ' # ' . print_r($AutoResponder, 1)
        );
        $AutoResponder = Campaigns::RetrieveCampaignByID($NewID2, $UserID);
        $this->assertTrue(
            $AutoResponder['CampaignStatusEnum'] == Campaigns::STATUS_READY,
            $message . ' CampaignStatusEnum 2'
        );
        $AutoResponder = Campaigns::RetrieveCampaignByID($NewID3, $UserID);
        $this->assertTrue(
            $AutoResponder['CampaignStatusEnum'] == Campaigns::STATUS_READY,
            $message . ' CampaignStatusEnum 3'
        );

        CustomFields::DeleteOfUser(1);
        $TotalCustomFields = db_query(
            "SELECT COUNT(*) FROM {custom_fields} WHERE RelOwnerUserID = :RelOwnerUserID",
            array(':RelOwnerUserID' => $UserID)
        )->fetchField();
        $this->assertTrue($TotalCustomFields == 0, $message . ' by UserID');

        $TotalCustomFields = db_query("SELECT COUNT(*) FROM {custom_fields}")->fetchField();
        $this->assertTrue($TotalCustomFields > 0, $message . ' count all');


        /**
         * Merge custom fields
         **/

        $message = "CustomFields::Merge (create custom fields)";
        $ArrayFieldSingle = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'FieldSingle',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $FieldSingleID = CustomFields::Create($ArrayFieldSingle);
        $this->assertTrue($FieldSingleID > 0, $message . ' ' . $ArrayFieldSingle['FieldName']);

        $ArrayFieldSingleEmpty = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'FieldSingleEmpty',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $FieldSingleEmptyID = CustomFields::Create($ArrayFieldSingleEmpty);
        $this->assertTrue($FieldSingleEmptyID > 0, $message . ' ' . $ArrayFieldSingleEmpty['FieldName']);

        $ArrayFieldSingleEmptySource = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'FieldSingleEmptySource',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $FieldSingleEmptySourceID = CustomFields::Create($ArrayFieldSingleEmptySource);
        $this->assertTrue($FieldSingleEmptySourceID > 0, $message . ' ' . $ArrayFieldSingleEmptySource['FieldName']);

        $ArrayFieldParagraph = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'FieldParagraph',
            'FieldTypeEnum' => CustomFields::TYPE_PARAGRAPH,
        );
        $FieldParagraphID = CustomFields::Create($ArrayFieldParagraph);
        $this->assertTrue($FieldParagraphID > 0, $message . ' ' . $ArrayFieldParagraph['FieldName']);

        $ArrayFieldDate = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'FieldDate',
            'FieldTypeEnum' => CustomFields::TYPE_DATE,
        );
        $FieldDateID = CustomFields::Create($ArrayFieldDate);
        $this->assertTrue($FieldDateID > 0, $message . ' ' . $ArrayFieldDate['FieldName']);

        $ArrayFieldEmail = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'FieldEmail',
            'FieldTypeEnum' => CustomFields::TYPE_EMAIL,
        );
        $FieldEmailID = CustomFields::Create($ArrayFieldEmail);
        $this->assertTrue($FieldEmailID > 0, $message . ' ' . $ArrayFieldEmail['FieldName']);

        $ArrayFieldCheckbox = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'FieldCheckbox',
            'FieldTypeEnum' => CustomFields::TYPE_CHECKBOX,
            'FieldOptions' => '[[Turkey]||[tr]]*,,,[[Germany]||[de]]',
        );
        $FieldCheckboxID = CustomFields::Create($ArrayFieldCheckbox);
        $this->assertTrue($FieldCheckboxID > 0, $message . ' ' . $ArrayFieldCheckbox['FieldName']);

        $ArrayFieldDropdown = array(
            'RelOwnerUserID' => 1,
            'FieldName' => 'FieldDropdown',
            'FieldTypeEnum' => CustomFields::TYPE_DROPDOWN,
            'FieldOptions' => '[[Brazil]||[br]]*,,,[[Germany]||[de]]',
        );
        $FieldDropdownID = CustomFields::Create($ArrayFieldDropdown);
        $this->assertTrue($FieldDropdownID > 0, $message . ' ' . $ArrayFieldDropdown['FieldName']);

        $message = "CustomFields::Merge (create subscribers)";
        // general test data
        $ListID = 1;
        $ArraySubscriberList = Lists::RetrieveListByIDOrDefault(1, $ListID);

        $SubscriberIDs = array();
        // create 10 subscribers with data
        for ($subscriber = 1; $subscriber <= 10; $subscriber++) {
            $Parameters = array(
                'UserID' => $ArraySubscriberList['RelOwnerUserID'],
                'ListInformation' => $ArraySubscriberList,
                'EmailAddress' => "cf_$<EMAIL>",
                'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
                'SendConfirmationEmail' => false,
                'TriggerAutoResponders' => false,
                'OtherFields' => array(
                    'CustomField' . $FieldSingleID => "SingleValue Subscriber $subscriber",
                    'CustomField' . $FieldParagraphID => "Text from subscriber $subscriber",
                    'CustomField' . $FieldDateID => strtotime(date("d.m.Y")),
                    'CustomField' . $FieldEmailID => "cf_$<EMAIL>",
                    'CustomField' . $FieldCheckboxID => "tr",
                ),
            );

            $Result = Subscribers::Subscribe($Parameters);
            $this->assertTrue($Result[0], $message . " add subscriber $subscriber" . print_r($Result, 1));
            $SubscriberIDs = $Result[1];
        }

        //Testcase: no SourceFieldValue
        $message = "CustomFields::Merge (no source values)";
        $ArrayMergeInformation = array(
            "RelOwnerUserID" => 1,
            "SourceFieldID" => $FieldSingleEmptySourceID,
            "TargetFieldID" => $FieldSingleID,
            "ValuePriority" => 'source',
        );
        $result = CustomFields::Merge($ArrayMergeInformation);
        $this->assertTrue($result['Success'] && $result['AffectedValues'] == 0, $message . " " . $result['Message']);
        $check_delete = CustomFields::RetrieveCustomField($ArrayMergeInformation['SourceFieldID'], 1);
        $this->assertTrue($check_delete === false, $message . " field deleted after merge");

        //Testcase: merge to empty fields
        $message = "CustomFields::Merge (no target values)";
        $ArrayMergeInformation = array(
            "RelOwnerUserID" => 1,
            "SourceFieldID" => $FieldSingleID,
            "TargetFieldID" => $FieldSingleEmptyID,
            "ValuePriority" => 'source',
        );
        $result = CustomFields::Merge($ArrayMergeInformation);
        $this->assertTrue(
            $result['Success'] && $result['AffectedValues'] == 10,
            $message . " " . $result['Message'] . print_r($result, 1)
        );
        $check_delete = CustomFields::RetrieveCustomField($ArrayMergeInformation['SourceFieldID'], 1);
        $this->assertTrue($check_delete === false, $message . " field deleted after merge");

        //Testcase: merge both fields not empty, overwrite target
        $message = "CustomFields::Merge (overwrite target)";
        $ArrayMergeInformation = array(
            "RelOwnerUserID" => 1,
            "SourceFieldID" => $FieldEmailID,
            "TargetFieldID" => $FieldParagraphID,
            "ValuePriority" => 'source',
        );
        $result = CustomFields::Merge($ArrayMergeInformation);
        $this->assertTrue(
            $result['Success'] && $result['AffectedValues'] == 10,
            $message . " " . $result['Message'] . print_r($result, 1)
        );
        $check_delete = CustomFields::RetrieveCustomField($ArrayMergeInformation['SourceFieldID'], 1);
        $this->assertTrue($check_delete === false, $message . " field deleted after merge");

        //Testcase: merge both fields not empty, overwrite target
        $message = "CustomFields::Merge (do not overwrite target)";
        $ArrayMergeInformation = array(
            "RelOwnerUserID" => 1,
            "SourceFieldID" => $FieldDateID,
            "TargetFieldID" => $FieldParagraphID,
            "ValuePriority" => 'target',
        );
        $result = CustomFields::Merge($ArrayMergeInformation);
        $this->assertTrue($result['Success'] && $result['AffectedValues'] == 0, $message . " " . $result['Message']);
        $check_delete = CustomFields::RetrieveCustomField($ArrayMergeInformation['SourceFieldID'], 1);
        $this->assertTrue($check_delete === false, $message . " field deleted after merge");

        //Testcase: field options conflict
        $message = "CustomFields::Merge (field options conflict)";
        $ArrayMergeInformation = array(
            "RelOwnerUserID" => 1,
            "SourceFieldID" => $FieldCheckboxID,
            "TargetFieldID" => $FieldDropdownID,
            "ValuePriority" => 'source',
        );
        $result = CustomFields::Merge($ArrayMergeInformation);
        $this->assertTrue(
            $result['Success'] == false && $result['ErrorCode'] == 6,
            $message . " " . $result['Message']
        );
        $check_delete = CustomFields::RetrieveCustomField($ArrayMergeInformation['SourceFieldID'], 1);
        $this->assertTrue(!empty($check_delete), $message . " field not deleted");

        $message = "CustomFields::Update (resolve field options conflict)";
        $ArrayFieldAndValues = array(
            'FieldOptions' => '[[Brazil]||[br]]*,,,[[Germany]||[de]]',
        );
        $result = CustomFields::Update($ArrayFieldAndValues, $FieldCheckboxID);
        $this->assertTrue($result, $message . ' options updated');

        $message = "CustomFields::Merge (field options conflict resolved)";
        $ArrayMergeInformation = array(
            "RelOwnerUserID" => 1,
            "SourceFieldID" => $FieldCheckboxID,
            "TargetFieldID" => $FieldDropdownID,
            "ValuePriority" => 'source',
        );
        $result = CustomFields::Merge($ArrayMergeInformation);
        $this->assertTrue(
            $result['Success'] == true && $result['AffectedValues'] == 10,
            $message . " " . $result['Message']
        );
        $check_delete = CustomFields::RetrieveCustomField($ArrayMergeInformation['SourceFieldID'], 1);
        $this->assertTrue($check_delete === false, $message . " field deleted after merge");

        // single value / multi value merge
        $message = "CustomFields::Merge single/multi value (setting up fields): ";

        $ArrayFieldSingleSV = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'FieldSingleSV',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
        );
        $FieldSingleIDSV = CustomFields::Create($ArrayFieldSingleSV);
        $this->assertTrue($FieldSingleIDSV > 0, $message . ' ' . $ArrayFieldSingleSV['FieldName']);
        $FieldSingleSV = CustomFields::RetrieveCustomField($FieldSingleIDSV, $UserID);
        $this->assertTrue(empty($FieldSingleSV['MultiValue']), $message . "set up single value I");

        $ArrayFieldSingleSV_II = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'FieldSingleSV_II',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
        );
        $FieldSingleIDSV_II = CustomFields::Create($ArrayFieldSingleSV_II);
        $this->assertTrue($FieldSingleIDSV_II > 0, $message . ' ' . $ArrayFieldSingleSV_II['FieldName']);
        $FieldSingleSV_II = CustomFields::RetrieveCustomField($FieldSingleIDSV_II, $UserID);
        $this->assertTrue(empty($FieldSingleSV_II['MultiValue']), $message . "set up single value II");

        $ArrayFieldSingleMV = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'FieldSingleMV',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
        );
        $FieldSingleIDMV = CustomFields::Create($ArrayFieldSingleMV);
        $this->assertTrue($FieldSingleIDMV > 0, $message . ' ' . $ArrayFieldSingleMV['FieldName']);
        CustomFields::Update(['MultiValue' => 1], $FieldSingleIDMV);
        $FieldSingleMV = CustomFields::RetrieveCustomField($FieldSingleIDMV, $UserID);
        $this->assertTrue($FieldSingleMV['MultiValue'] == 1, $message . "set up multi value I");

        $ArrayFieldSingleMV_II = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'FieldSingleMV_II',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
        );
        $FieldSingleIDMV_II = CustomFields::Create($ArrayFieldSingleMV_II);
        $this->assertTrue($FieldSingleIDMV_II > 0, $message . ' ' . $ArrayFieldSingleMV_II['FieldName']);
        CustomFields::Update(['MultiValue' => 1], $FieldSingleIDMV_II);
        $FieldSingleMV_II = CustomFields::RetrieveCustomField($FieldSingleIDMV_II, $UserID);
        $this->assertTrue($FieldSingleMV_II['MultiValue'] == 1, $message . "set up multi value II");

        $ListID = 1;
        $ArraySubscriberList = Lists::RetrieveListByIDOrDefault($UserID, $ListID);

        // create 10 subscribers with data
        $ReferenceID_II = Reference::InsertDB(
            [
                'RelOwnerUserID' => $ArraySubscriberList['RelOwnerUserID'],
                'NumberRange' => 'simpletests',
                'ExtReferenceID' => 'simpletests_666'
            ]
        );
        $ReferenceID_III = Reference::InsertDB(
            [
                'RelOwnerUserID' => $ArraySubscriberList['RelOwnerUserID'],
                'NumberRange' => 'simpletests',
                'ExtReferenceID' => 'simpletests_999'
            ]
        );
        $SubscriberIDs = [];
        for ($subscriber = 1; $subscriber <= 10; $subscriber++) {
            $email = "cf_$<EMAIL>";
            $Parameters = array(
                'UserID' => $ArraySubscriberList['RelOwnerUserID'],
                'ListInformation' => $ArraySubscriberList,
                'EmailAddress' => $email,
                'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
                'SendConfirmationEmail' => false,
                'TriggerAutoResponders' => false,
                'OtherFields' => array(
                    'CustomField' . $FieldSingleIDSV => "SV",
                    'CustomField' . $FieldSingleIDSV_II => "SV II",
                    'CustomField' . $FieldSingleIDMV => "MV",
                    'CustomField' . $FieldSingleIDMV_II => "MV II",
                ),
            );

            $Result = Subscribers::Subscribe($Parameters);
            $this->assertTrue($Result[0], $message . " add subscriber $subscriber" . print_r($Result, 1));

            CustomFields::UpdateCustomFieldData(
                $UserID,
                $Result[1],
                $ReferenceID_II,
                $FieldSingleMV,
                "MV with Reference ID $ReferenceID_II"
            );
            CustomFields::UpdateCustomFieldData(
                $UserID,
                $Result[1],
                $ReferenceID_II,
                $FieldSingleMV_II,
                "MV II with Reference ID $ReferenceID_II"
            );

            $SubscriberIDs[$subscriber] = $Result[1];
        }

        // single value to single value is tested above

        // single value to multi value
        $message = "CustomFields::Merge single/multi value (single value to multi value): ";

        $ArrayMergeInformation = array(
            "RelOwnerUserID" => $UserID,
            "SourceFieldID" => $FieldSingleIDSV,
            "TargetFieldID" => $FieldSingleIDMV,
            "ValuePriority" => 'source',
        );
        $result = CustomFields::Merge($ArrayMergeInformation);
        $this->assertTrue(
            $result['Success'] == true && $result['AffectedValues'] == 30,
            $message . " " . $result['Message']
        );
        $check_delete = CustomFields::RetrieveCustomField($ArrayMergeInformation['SourceFieldID'], $UserID);
        $this->assertTrue($check_delete === false, $message . " field deleted after merge");

        $FieldSingleMV_Subscriber_5_Data = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberIDs[5],
            $FieldSingleMV,
            $ReferenceID
        );
        $this->assertTrue(
            $FieldSingleMV_Subscriber_5_Data == "SV",
            $message . "references set correct for reference ID " . $ReferenceID
        );
        $FieldSingleMV_Subscriber_5_RefII_Data = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberIDs[5],
            $FieldSingleMV,
            $ReferenceID_II
        );
        $this->assertTrue(
            $FieldSingleMV_Subscriber_5_RefII_Data == "SV",
            $message . "references set correct for reference ID " . $ReferenceID_II
        );
        $FieldSingleMV_Subscriber_5_RefIII_Data = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberIDs[5],
            $FieldSingleMV,
            $ReferenceID_II
        );
        $this->assertTrue(
            $FieldSingleMV_Subscriber_5_RefIII_Data == "SV",
            $message . "references set correct for reference ID " . $ReferenceID_III
        );


        // multi value to multi value
        $message = "CustomFields::Merge single/multi value (multi value to multi value): ";

        $ArrayMergeInformation = array(
            "RelOwnerUserID" => $UserID,
            "SourceFieldID" => $FieldSingleIDMV,
            "TargetFieldID" => $FieldSingleIDMV_II,
            "ValuePriority" => 'source',
        );
        $result = CustomFields::Merge($ArrayMergeInformation);
        $this->assertTrue(
            $result['Success'] == true && $result['AffectedValues'] == 30,
            $message . " " . $result['Message']
        );
        $check_delete = CustomFields::RetrieveCustomField($ArrayMergeInformation['SourceFieldID'], $UserID);
        $this->assertTrue($check_delete === false, $message . " field deleted after merge");

        $FieldSingleMV_II_Subscriber_3_Data = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberIDs[3],
            $FieldSingleMV_II,
            $ReferenceID
        );
        $this->assertTrue(
            $FieldSingleMV_II_Subscriber_3_Data == "SV",
            $message . "references set correct for reference ID " . $ReferenceID
        );
        $FieldSingleMV_II_Subscriber_3_RefII_Data = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberIDs[3],
            $FieldSingleMV_II,
            $ReferenceID_II
        );
        $this->assertTrue(
            $FieldSingleMV_II_Subscriber_3_RefII_Data == "SV",
            $message . "references set correct for reference ID " . $ReferenceID_II
        );
        $FieldSingleMV_II_Subscriber_3_RefIII_Data = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberIDs[3],
            $FieldSingleMV_II,
            $ReferenceID_II
        );
        $this->assertTrue(
            $FieldSingleMV_II_Subscriber_3_RefIII_Data == "SV",
            $message . "references set correct for reference ID " . $ReferenceID_III
        );

        // multi value to single value
        $message = "CustomFields::Merge single/multi value (multi value to single value): ";

        $value = "MV->SV I";
        $value_II = "MV->SV II";
        $value_III = "MV->SV III";

        // adding different values for multi value Field to check overwriting of single value field
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberIDs[7], $ReferenceID, $FieldSingleMV_II, $value);
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberIDs[7], $ReferenceID_II, $FieldSingleMV_II, $value_II);
        CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberIDs[7],
            $ReferenceID_III,
            $FieldSingleMV_II,
            $value_III
        );

        $ArrayMergeInformation = array(
            "RelOwnerUserID" => $UserID,
            "SourceFieldID" => $FieldSingleIDMV_II,
            "TargetFieldID" => $FieldSingleIDSV_II,
            "ValuePriority" => 'source',
        );
        $result = CustomFields::Merge($ArrayMergeInformation);
        $this->assertTrue(
            $result['Success'] == true && $result['AffectedValues'] == 30,
            $message . " " . $result['Message']
        );
        $check_delete = CustomFields::RetrieveCustomField($ArrayMergeInformation['SourceFieldID'], $UserID);
        $this->assertTrue($check_delete === false, $message . " field deleted after merge");

        $FieldSingleSV_II_Subscriber_7_Data = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberIDs[7],
            $FieldSingleSV_II,
            $ReferenceID
        );
        $this->assertTrue(
            $FieldSingleSV_II_Subscriber_7_Data == $value_III,
            $message . "references set correct for reference ID " . $ReferenceID
        );
        $FieldSingleSV_II_Subscriber_7_RefII_Data = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberIDs[7],
            $FieldSingleSV_II,
            $ReferenceID_II
        );
        $this->assertTrue(
            $FieldSingleSV_II_Subscriber_7_RefII_Data == $value_III,
            $message . "references set correct for reference ID " . $ReferenceID_II
        );
        $FieldSingleSV_II_Subscriber_7_RefIII_Data = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberIDs[7],
            $FieldSingleSV_II,
            $ReferenceID_III
        );
        $this->assertTrue(
            $FieldSingleSV_II_Subscriber_7_RefIII_Data == $value_III,
            $message . "references set correct for reference ID " . $ReferenceID_III
        );


        /*
         * TEST: Convert custom field widget values
         */

        /*
         * FieldType datetime
         * datetime has 2 widgets
         * - for klicktipp forms (Drupal): 1 textfield for date and 2 dropdowns for time (hours and minutes)
         * - for subscriptions forms: 5 dropdowns (day, month, year, hours, minutes
         */

        //---Widget:klicktipp forms---

        // TEST: valid value
        $message = "Convert datetime value ";
        $Testdate = strtotime("2014-04-30 09:59:00");
        $ToWidget = CustomFields::ConvertCustomFieldDataToWidget($Testdate, CustomFields::WIDGET_DATETIME);
        $expected = array(
            'date' => date(variable_get(Dates::FORMAT_DMY_DATEPICKER_PHP, "d.m.Y"), $Testdate),
            'hours' => "09",
            'minutes' => "59",
        );
        $this->assertTrue($ToWidget == $expected, "$message to widget (klicktipp)" . print_r($ToWidget, 1));
        //convert back
        $FromWidget = CustomFields::ConvertCustomFieldDataFromWidget($ToWidget, CustomFields::WIDGET_DATETIME);
        $this->assertTrue($FromWidget == $Testdate, "$message from widget (klicktipp) " . print_r($FromWidget, 1));

        //values for a datetime field are not validated, we always return the result of strtotime (for now)

        //---Widget: subscription form---

        // TEST: valid value
        $message = "Convert datetime value ";
        $Testdate = strtotime("2014-04-30 09:59:00");
        $ToWidget = CustomFields::ConvertCustomFieldDataToWidget($Testdate, CustomFields::WIDGET_DATETIME_5SELECTS);
        $expected = array("30", "04", "2014", "09", "59"); //day, month, year, hours, minutes
        $this->assertTrue($ToWidget == $expected, "$message to widget (subscription form)" . print_r($ToWidget, 1));
        //convert back
        $FromWidget = CustomFields::ConvertCustomFieldDataFromWidget($ToWidget, CustomFields::WIDGET_DATETIME_5SELECTS);
        $this->assertTrue(
            $FromWidget == $Testdate,
            "$message from widget (subscription form) " . date("Y-m-d H:i:s", $FromWidget)
        );

        // TEST: empty value
        $message = "Convert datetime value (empty) ";
        $Testdate = '';
        $ToWidget = CustomFields::ConvertCustomFieldDataToWidget($Testdate, CustomFields::WIDGET_DATETIME_5SELECTS);
        $expected = array("", "", "", "", ""); //day, month, year, hours, minutes
        $this->assertTrue($ToWidget == $expected, "$message to widget (subscription form)" . print_r($ToWidget, 1));
        //convert back
        $FromWidget = CustomFields::ConvertCustomFieldDataFromWidget($ToWidget, CustomFields::WIDGET_DATETIME_5SELECTS);
        $this->assertTrue(
            $FromWidget == $Testdate,
            "$message from widget (subscription form) " . date("Y-m-d H:i:s", $FromWidget)
        );

        // TEST: invalid value
        $message = "Convert datetime value (invalid) ";
        $Testdate = 'not a timestamp';
        $ToWidget = CustomFields::ConvertCustomFieldDataToWidget($Testdate, CustomFields::WIDGET_DATETIME_5SELECTS);
        $expected = array("", "", "", "", ""); //day, month, year, hours, minutes
        $this->assertTrue($ToWidget == $expected, "$message to widget (klicktipp)" . print_r($ToWidget, 1));
        //convert back
        $FromWidget = CustomFields::ConvertCustomFieldDataFromWidget($ToWidget, CustomFields::WIDGET_DATETIME_5SELECTS);
        $this->assertTrue($FromWidget == '', "$message from widget (klicktipp) " . print_r($FromWidget, 1));

        /*
         * very long field name
         */
        $message = 'very long field name';
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => 2,
            'FieldName' => 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanc',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $CFLongFieldname = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($CFLongFieldname > 0, $message);
        $ArrayCustomFieldInformation = CustomFields::RetrieveCustomField($CFLongFieldname, 2);
        $this->assertTrue(
            strlen($ArrayCustomFieldInformation['FieldName']) < strlen($ArrayFieldAndValues['FieldName']),
            $message . ' length fieldname'
        );


        // ####### Calculate CustomField Data ####### //

        $message = "CalculateCustomFieldData: ";

        $TestDate = strtotime("2016-09-01 08:15");
        $TestTime = (8 * 3600) + (15 * 60);
        $TestDateTime = strtotime("2016-09-26 08:15");

        $DefaultValues = array(
            CustomFields::TYPE_SINGLE => "This is a single line text.",
            CustomFields::TYPE_PARAGRAPH => "This is a multi\nline text.",
            CustomFields::TYPE_EMAIL => "example@" . KLICKTIPP_DOMAIN,
            CustomFields::TYPE_NUMBER => "4711",
            CustomFields::TYPE_DECIMAL => "999",
            CustomFields::TYPE_URL => APP_URL . 'simpletest/test?op=0815&x=text',
            CustomFields::TYPE_DATE => strtotime(date('Y-m-d 00:00:00', $TestDate)),
            CustomFields::TYPE_TIME => $TestTime,
            CustomFields::TYPE_DATETIME => $TestDate,
        );

        $CalcCfSubscriberEmail = '<EMAIL>';
        $CalcCfSubscriberPhone = '0049471100001';

        $ArrayFieldAndValues = array(
            'Name' => "Set CF list",
            'RelOwnerUserID' => $UserID,
        );
        $SubscriberListID = Lists::InsertDB($ArrayFieldAndValues);
        $ArraySubscriberList = Lists::RetrieveListByID($UserID, $SubscriberListID);

        $Parameters = array(
            'UserID' => $UserID,
            'ListInformation' => $ArraySubscriberList,
            'EmailAddress' => $CalcCfSubscriberEmail,
            'PhoneNumber' => $CalcCfSubscriberPhone,
            'IPAddress' => '127.0.0.1',
            'SubscriptionReferrer' => APP_URL,
            'SubscriptionStatus' => 0,
            'SendConfirmationEmail' => false,
            'TriggerAutoResponders' => false,
        );

        $Result = Subscribers::Subscribe($Parameters);

        $Parameters = array(
            'UserID' => $UserID,
            'ListInformation' => $ArraySubscriberList,
            'EmailAddress' => $CalcCfSubscriberEmail,
            'IPAddress' => '0.0.0.0',
            'SubscriptionReferrer' => APP_URL . 'confirm',
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SendConfirmationEmail' => false,
            'TriggerAutoResponders' => false,
        );

        Subscribers::Subscribe($Parameters);

        Subscribers::Unsubscribe($UserID, $CalcCfSubscriberEmail, 0, $UnsubscriptionIP = '*********');
        Subscribers::UnsubscribeSMSNumber($UserID, $CalcCfSubscriberPhone);

        $CalcCfSubscriber = Subscribers::RetrieveSubscriber($UserID, $Result[1]);
        $CalcCfSubscriberID = $CalcCfSubscriber['SubscriberID'];
        $this->assertTrue(!empty($CalcCfSubscriber), "$message create subscriber - {$Result[1]}");


        $SourceCustomFields = _testdata_custom_field_create_set($UserID, 'source');
        foreach ($SourceCustomFields as $type => $field) {
            $this->assertTrue(!empty($field), "$message create source customfield type $type");
        }

        $TargetCustomFields = _testdata_custom_field_create_set($UserID, 'target');
        foreach ($TargetCustomFields as $type => $field) {
            $this->assertTrue(!empty($field), "$message create target customfield type $type");
        }

        // --- TESTCASE: CALCULATE_OP_VALUE

        $op = CustomFields::CALCULATE_OP_VALUE;

        foreach ($SourceCustomFields as $type => $field) {
            if ($type == CustomFields::TYPE_DATE) {
                $setvalue = date('Y-m-d', $DefaultValues[$type]);
            } elseif ($type == CustomFields::TYPE_DATETIME) {
                $setvalue = date('Y-m-d H:i', $DefaultValues[$type]);
            } elseif ($type == CustomFields::TYPE_TIME) {
                $setvalue = CustomFields::ConvertCustomFieldDataToWidget(
                    $DefaultValues[$type],
                    CustomFields::WIDGET_TIME_SECONDSOFDAY
                );
            } elseif ($type == CustomFields::TYPE_DECIMAL) {
                $setvalue = CustomFields::ConvertCustomFieldDataToWidget(
                    $DefaultValues[$type],
                    CustomFields::WIDGET_DECIMAL
                );
            } else {
                $setvalue = $DefaultValues[$type];
            }
            $result = $this->testSetCalculatedField(
                $UserID,
                $SourceCustomFields[$type],
                $CalcCfSubscriberID,
                $op,
                $setvalue,
                '',
                array(),
                $TestDateTime
            );
            $this->assertTrue($result !== false, "$message [$op] set value '$result' for customfield type $type");
        }

        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);

        foreach ($SourceCustomFields as $type => $field) {
            $cfvalue = $Subscription["CustomField{$field['CustomFieldID']}"];
            $this->assertTrue(
                $DefaultValues[$type] == $cfvalue,
                "$message [$op] value '$cfvalue' set for customfield type $type"
            );
        }

        // --- TESTCASE: CALCULATE_OP_COPY

        $op = CustomFields::CALCULATE_OP_COPY;

        foreach ($TargetCustomFields as $type => $field) {
            $result = $this->testSetCalculatedField(
                $UserID,
                $TargetCustomFields[$type],
                $CalcCfSubscriberID,
                $op,
                $SourceCustomFields[$type]['CustomFieldID'],
                '',
                array(),
                $TestDateTime
            );
            $this->assertTrue(
                $result !== false,
                "$message [$op] copy value '$result' for customfield {$SourceCustomFields[$type]['FieldName']}"
            );
        }

        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);

        foreach ($TargetCustomFields as $type => $field) {
            $cfvalue = $Subscription["CustomField{$field['CustomFieldID']}"];
            $this->assertTrue(
                $DefaultValues[$type] == $cfvalue,
                "$message [$op] value '$cfvalue' copied from customfield {$SourceCustomFields[$type]['FieldName']}"
            );
        }

        // --- TESTCASE: CALCULATE_OP_EMPTY

        $op = CustomFields::CALCULATE_OP_EMPTY;

        foreach ($TargetCustomFields as $type => $field) {
            $result = $this->testSetCalculatedField(
                $UserID,
                $TargetCustomFields[$type],
                $CalcCfSubscriberID,
                $op,
                '',
                '',
                array(),
                $TestDateTime
            );
            $this->assertTrue(
                $result !== false,
                "$message [$op] empty customfield {$TargetCustomFields[$type]['FieldName']}"
            );
        }

        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);

        foreach ($TargetCustomFields as $type => $field) {
            $this->assertTrue(empty($Subscription["CustomField{$field['CustomFieldID']}"]), "$message [$op] OK");
        }

        // --- TESTCASE: CALCULATE_OP_PREPEND

        $op = CustomFields::CALCULATE_OP_TEXT_PREPEND;
        $prepend_result = 'prefix_' . $DefaultValues[CustomFields::TYPE_SINGLE];
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            $op,
            'prefix_',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_SINGLE]['CustomFieldID']}"];
        $this->assertTrue($cfvalue == $prepend_result, "$message [$op] $cfvalue == $prepend_result");

        // --- TESTCASE: CALCULATE_OP_APPEND

        $op = CustomFields::CALCULATE_OP_TEXT_APPEND;

        $append_result = $prepend_result . '_suffix';
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            $op,
            '_suffix',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_SINGLE]['CustomFieldID']}"];
        $this->assertTrue($cfvalue == $append_result, "$message [$op] $cfvalue == $append_result");

        // --- TESTCASE: CALCULATE_OP_REPLACE

        $op = CustomFields::CALCULATE_OP_TEXT_REPLACE;

        $replace_result = $prepend_result . '_SUFFIX';
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            $op,
            '_suffix',
            '_SUFFIX',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_SINGLE]['CustomFieldID']}"];
        $this->assertTrue($cfvalue == $replace_result, "$message [$op] $cfvalue == $replace_result");

        // --- TESTCASE: CALCULATE_OP_TEXT_HASH_CRC32;

        $op = CustomFields::CALCULATE_OP_TEXT_HASH_CRC32;

        $algo = 'crc32';
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $algo,
            '',
            array(),
            $TestDateTime
        );
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_SINGLE]['CustomFieldID']}"];
        $hash = hash($algo, $algo);
        $this->assertTrue($cfvalue == $hash, "$message [$op] $cfvalue == $hash");

        // --- TESTCASE: CALCULATE_OP_TEXT_HASH_MD5;

        $op = CustomFields::CALCULATE_OP_TEXT_HASH_MD5;

        $algo = 'md5';
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $algo,
            '',
            array(),
            $TestDateTime
        );
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_SINGLE]['CustomFieldID']}"];
        $hash = hash($algo, $algo);
        $this->assertTrue($cfvalue == $hash, "$message [$op] $cfvalue == $hash");

        // --- TESTCASE: CALCULATE_OP_TEXT_HASH_SHA1;

        $op = CustomFields::CALCULATE_OP_TEXT_HASH_SHA1;

        $algo = 'sha1';
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $algo,
            '',
            array(),
            $TestDateTime
        );
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_SINGLE]['CustomFieldID']}"];
        $hash = hash($algo, $algo);
        $this->assertTrue($cfvalue == $hash, "$message [$op] $cfvalue == $hash");

        // --- TESTCASE: CALCULATE_OP_TEXT_HASH_SHA256;

        $op = CustomFields::CALCULATE_OP_TEXT_HASH_SHA256;

        $algo = 'sha256';
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $algo,
            '',
            array(),
            $TestDateTime
        );
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_SINGLE]['CustomFieldID']}"];
        $hash = hash($algo, $algo);
        $this->assertTrue($cfvalue == $hash, "$message [$op] $cfvalue == $hash");

        // --- TESTCASE: CALCULATE_OP_TEXT_HASH_SHA384;

        $op = CustomFields::CALCULATE_OP_TEXT_HASH_SHA384;

        $algo = 'sha384';
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $algo,
            '',
            array(),
            $TestDateTime
        );
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_SINGLE]['CustomFieldID']}"];
        $hash = hash($algo, $algo);
        $this->assertTrue($cfvalue == $hash, "$message [$op] $cfvalue == $hash");

        // --- TESTCASE: CALCULATE_OP_TEXT_HASH_SHA512;

        $op = CustomFields::CALCULATE_OP_TEXT_HASH_SHA512;

        $algo = 'sha512';
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $algo,
            '',
            array(),
            $TestDateTime
        );
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_SINGLE],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_SINGLE]['CustomFieldID']}"];
        $hash = hash($algo, $algo);
        $this->assertTrue($cfvalue == $hash, "$message [$op] $cfvalue == $hash");

        // --- TESTCASE: CALCULATE_OP_NUMBER_ADD

        $op = CustomFields::CALCULATE_OP_NUMBER_ADD;

        $res = $DefaultValues[CustomFields::TYPE_NUMBER]; //4711
        $number = 89;
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_NUMBER],
            $CalcCfSubscriberID,
            $op,
            $number,
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_NUMBER]['CustomFieldID']}"];
        $res = $res + $number;
        $this->assertTrue($cfvalue == $res, "$message [$op] 4711 + 89 = $res == $cfvalue");

        $res2 = $DefaultValues[CustomFields::TYPE_DECIMAL]; //999
        $number = "10,01";
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DECIMAL],
            $CalcCfSubscriberID,
            $op,
            $number,
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_DECIMAL]['CustomFieldID']}"];
        $res2 = $res2 + 1001;
        $this->assertTrue($cfvalue == $res2, "$message [$op] 9,99 + 10,01 = $res2 == $cfvalue");

        // --- TESTCASE: CALCULATE_OP_NUMBER_SUBTRACT

        $op = CustomFields::CALCULATE_OP_NUMBER_SUBTRACT;

        //$res = 4800 (field value)
        $number = 5000;
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_NUMBER],
            $CalcCfSubscriberID,
            $op,
            $number,
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_NUMBER]['CustomFieldID']}"];
        $res = $res - $number;
        $this->assertTrue($cfvalue == $res, "$message [$op] 4800 - 5000 = $res == $cfvalue");

        //$res2 = 2000 (field value)
        $number = "23,33";
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DECIMAL],
            $CalcCfSubscriberID,
            $op,
            $number,
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_DECIMAL]['CustomFieldID']}"];
        $res2 = $res2 - 2333;
        $this->assertTrue($cfvalue == $res2, "$message [$op] 20,00 - 23,33 = $res2 == $cfvalue");

        // --- TESTCASE: CALCULATE_OP_NUMBER_RANDOM

        $op = CustomFields::CALCULATE_OP_NUMBER_RANDOM; //TODO

        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_NUMBER],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = (int)$Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_NUMBER]['CustomFieldID']}"];
        $this->assertTrue($cfvalue >= 0 && $cfvalue <= getrandmax(), "$message [$op] 0 <= $cfvalue <= " . getrandmax());

        $min = 500;
        $max = 1000;
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_NUMBER],
            $CalcCfSubscriberID,
            $op,
            $min,
            $max,
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_NUMBER]['CustomFieldID']}"];
        $this->assertTrue($cfvalue >= $min && $cfvalue <= $max, "$message [$op] $min <= $cfvalue <= $max");

        // --- TESTCASE: CALCULATE_OP_URL_TRUNCATE_QUERY

        $op = CustomFields::CALCULATE_OP_URL_TRUNCATE_QUERY;

        $url = $DefaultValues[CustomFields::TYPE_URL]; // APP_URL . 'simpletest/test?op=0815&x=text'
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_URL],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_URL]['CustomFieldID']}"];
        $withoutQuery = explode('?', $url);
        $this->assertTrue($cfvalue == $withoutQuery[0], "$message [$op] $url => $cfvalue");

        // --- TESTCASE: CALCULATE_OP_URL_TRUNCATE_QUERY

        $op = CustomFields::CALCULATE_OP_URL_TRUNCATE_PATH;

        $url = $withoutQuery[0]; // APP_URL . 'simpletest/test'
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_URL],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_URL]['CustomFieldID']}"];
        $withoutPath = trim(APP_URL, '/');
        $this->assertTrue($cfvalue == $withoutPath, "$message [$op] $url => $cfvalue");

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_URL],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            '//tony.vmdevel01.mailandbeyond.com/webinar',
            '',
            array(),
            $TestDateTime
        );
        $result = $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_URL],
            $CalcCfSubscriberID,
            $op,
            '',
            '',
            array(),
            $TestDateTime
        );
        $this->assertTrue($result !== false, "$message [$op] OK");
        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);
        $cfvalue = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_URL]['CustomFieldID']}"];
        $this->assertTrue(
            $cfvalue == '//tony.vmdevel01.mailandbeyond.com',
            "$message [$op] //tony.vmdevel01.mailandbeyond.com/webinar => $cfvalue"
        );

        // --- TESTCASE: CALCULATE_OP_DATETIME_NOW, CALCULATE_OP_DATETIME_INCREASE, CALCULATE_OP_DATETIME_DECREASE

        $testDatetimes = array(
            //array(op, value1, value2 (use current time), weekdays, current (test) time, expected result)
            array(
                '0 days',
                CustomFields::CALCULATE_OP_DATETIME_NOW,
                array(1, 2, 3, 4, 5, 6, 7),
                $TestDateTime,
                '0 days'
            ),
            array(
                '90 minutes',
                CustomFields::CALCULATE_OP_DATETIME_NOW,
                array(1, 2, 3, 4, 5, 6, 7),
                $TestDateTime,
                '90 minutes'
            ),
            array(
                '20 hours',
                CustomFields::CALCULATE_OP_DATETIME_NOW,
                array(1, 2, 3, 4, 5, 6, 7),
                $TestDateTime,
                '20 hours'
            ),
            array(
                '10 weeks',
                CustomFields::CALCULATE_OP_DATETIME_NOW,
                array(1, 2, 3, 4, 5, 6, 7),
                $TestDateTime,
                '10 weeks'
            ),
            array(
                '12 months',
                CustomFields::CALCULATE_OP_DATETIME_NOW,
                array(1, 2, 3, 4, 5, 6, 7),
                $TestDateTime,
                '12 months'
            ),
            array(
                '50 years',
                CustomFields::CALCULATE_OP_DATETIME_NOW,
                array(1, 2, 3, 4, 5, 6, 7),
                $TestDateTime,
                '50 years'
            ),
            array('0 days', CustomFields::CALCULATE_OP_DATETIME_NOW, array(6), $TestDateTime, '0 days next saturday'),
            //set back to now to test increase decrease
            array(
                '0 days',
                CustomFields::CALCULATE_OP_DATETIME_NOW,
                array(1, 2, 3, 4, 5, 6, 7),
                $TestDateTime,
                '0 days'
            ),
        );

        $operations = array(
            CustomFields::CALCULATE_OP_DATETIME_NOW,
            CustomFields::CALCULATE_OP_DATETIME_INCREASE,
            CustomFields::CALCULATE_OP_DATETIME_DECREASE
        );

        foreach ($operations as $op) {
            foreach ($testDatetimes as $t) {
                //reference time
                if ($op == CustomFields::CALCULATE_OP_DATETIME_NOW) {
                    $refTime = $TestDateTime;
                } else {
                    //Note: the datetime field contains the same date as the date field but with hours and minutes
                    $Subscription = Subscribers::RetrieveFullSubscriberWithFields(
                        $UserID,
                        $CalcCfSubscriberID,
                        $ReferenceID
                    );
                    $refTime = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_DATETIME]['CustomFieldID']}"];
                }

                $sign = ($op == CustomFields::CALCULATE_OP_DATETIME_DECREASE) ? '-' : '+';

                $this->testSetCalculatedField(
                    $UserID,
                    $SourceCustomFields[CustomFields::TYPE_DATETIME],
                    $CalcCfSubscriberID,
                    $op,
                    $sign . $t[0],
                    $t[1],
                    $t[2],
                    $t[3]
                );
                $this->testSetCalculatedField(
                    $UserID,
                    $SourceCustomFields[CustomFields::TYPE_DATE],
                    $CalcCfSubscriberID,
                    $op,
                    $sign . $t[0],
                    $t[1],
                    $t[2],
                    $t[3]
                );
                $Subscription = Subscribers::RetrieveFullSubscriberWithFields(
                    $UserID,
                    $CalcCfSubscriberID,
                    $ReferenceID
                );

                //expected
                $expected = strtotime($sign . $t[4] . date(' H:i', $refTime), $refTime);

                //validate

                $cfdatetime = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_DATETIME]['CustomFieldID']}"];
                $cfdatetime = date('Y-m-d H:i', $cfdatetime);
                $testDateTime = date('Y-m-d H:i', $expected);

                $cfdate = $Subscription["CustomField{$SourceCustomFields[CustomFields::TYPE_DATE]['CustomFieldID']}"];
                $cfdate = date('Y-m-d', $cfdate);
                $testDate = date('Y-m-d', $expected);

                $refTimeDisplay = date("l, Y-m-d H:i", $refTime);

                //validate date field
                $this->assertTrue(
                    $cfdatetime == $testDateTime,
                    "$message $refTimeDisplay: [$op -> {$t[0]} -> weekdays(" . implode(
                        ',',
                        $t[2]
                    ) . ")] | $cfdatetime == $testDateTime"
                );
                //validate datetime field
                $this->assertTrue(
                    $cfdate == $testDate,
                    "$message $refTimeDisplay: [$op -> {$t[0]} -> weekdays(" . implode(
                        ',',
                        $t[2]
                    ) . ")] | $cfdate == $testDate"
                );
            }
        }

        // daylight saving time
        $sommertime = strtotime("2016-03-27 00:00:00"); //last sunday in march

        //date field

        //set field to 1 day before summer time => "Saturday, 2016-03-26"
        $value = date('Y-m-d H:i', strtotime('-1 day', $sommertime));
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $value,
            '',
            array(),
            $TestDateTime
        );

        //increase by one day, but constrain to weekday monday and keep the current time
        $value = '+1 day';
        $delayWeekday = array(1);
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_INCREASE,
            $value,
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-28'),
            "$message increase date before summertimer to next monday after - " . date(
                'l, Y-m-d H:i:s',
                $checkvalue
            ) . ' - ' . date('l, Y-m-d H:i:s', strtotime('2016-03-28'))
        );

        //decrease by one week
        $value = '-1 week';
        $delayWeekday = array();
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_DECREASE,
            $value,
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-21'),
            "$message decrease date after summertimer by 1 week and set time - " . date(
                'l, Y-m-d H:i:s',
                $checkvalue
            ) . ' - ' . date('l, Y-m-d H:i:s', strtotime('2016-03-21'))
        );

        //datetime field

        //set field to 1 day before summer time at 12:00 => "Saturday, 2016-03-26 00:00:00"
        $value = date('Y-m-d H:i', strtotime('-1 day 12:00', $sommertime));
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $value,
            '',
            array(),
            $TestDateTime
        );

        //increase by one day, but constrain to weekday monday and keep the current time
        $value = '+1 day';
        $delayWeekday = array(1);
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_INCREASE,
            $value,
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-28 12:00:00'),
            "$message increase datetime before summertimer to next monday after - " . date(
                'l, Y-m-d H:i:s',
                $checkvalue
            ) . ' - ' . date('l, Y-m-d H:i:s', strtotime('2016-03-28 12:00:00'))
        );

        //decrease by one week and set time to 14:29
        $value = '-1 week 14:29';
        $delayWeekday = array();
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_DECREASE,
            $value,
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-21 14:29:00'),
            "$message decrease datetime after summertimer by 1 week and set time - " . date(
                'l, Y-m-d H:i:s',
                $checkvalue
            ) . ' - ' . date('l, Y-m-d H:i:s', strtotime('2016-03-21 14:29:00'))
        );

        //round time in datetime field
        $value = '+0 days';
        $delayWeekday = array();
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_INCREASE,
            $value,
            CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND,
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-21 14:00:00'),
            "$message round time in datetime field (down) - " . date('l, Y-m-d H:i:s', $checkvalue) . ' - ' . date(
                'l, Y-m-d H:i:s',
                strtotime('2016-03-21 14:00:00')
            )
        );

        //round time in datetime field
        $value = '-20 minutes';
        $delayWeekday = array();
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_DECREASE,
            $value,
            CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND,
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-21 14:00:00'),
            "$message round time in datetime field (up) - " . date('l, Y-m-d H:i:s', $checkvalue) . ' - ' . date(
                'l, Y-m-d H:i:s',
                strtotime('2016-03-21 14:00:00')
            )
        );

        //floor time in datetime field
        $value = '+20 minutes';
        $delayWeekday = array();
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_INCREASE,
            $value,
            CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_FLOOR,
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-21 14:00:00'),
            "$message floor time in datetime field (up) - " . date('l, Y-m-d H:i:s', $checkvalue) . ' - ' . date(
                'l, Y-m-d H:i:s',
                strtotime('2016-03-21 14:00:00')
            )
        );

        //ceil time in datetime field
        $value = '-10 minutes';
        $delayWeekday = array();
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_DECREASE,
            $value,
            CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_CEIL,
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-21 14:00:00'),
            "$message ceil time in datetime field (up) - " . date('l, Y-m-d H:i:s', $checkvalue) . ' - ' . date(
                'l, Y-m-d H:i:s',
                strtotime('2016-03-21 14:00:00')
            )
        );

        //round time in time field
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            '12:12',
            '',
            array(),
            $TestDateTime
        );
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $ReferenceID
        );
        $checkvalue = CustomFields::ConvertCustomFieldDataToWidget($checkvalue, CustomFields::WIDGET_TIME_SECONDSOFDAY);
        $this->assertTrue($checkvalue == '12:00', "$message round time in time field (down) - $checkvalue - 12:00");

        //round time in time field
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            '13:30',
            '',
            array(),
            $TestDateTime
        );
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $ReferenceID
        );
        $checkvalue = CustomFields::ConvertCustomFieldDataToWidget($checkvalue, CustomFields::WIDGET_TIME_SECONDSOFDAY);
        $this->assertTrue($checkvalue == '14:00', "$message round time in time field (up) - $checkvalue - 14:00");

        //floor time in time field
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            '08:50',
            '',
            array(),
            $TestDateTime
        );
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_FLOOR,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $ReferenceID
        );
        $checkvalue = CustomFields::ConvertCustomFieldDataToWidget($checkvalue, CustomFields::WIDGET_TIME_SECONDSOFDAY);
        $this->assertTrue($checkvalue == '08:00', "$message floor time in time field - $checkvalue - 08:00");

        //ceil time in time field
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            '19:02',
            '',
            array(),
            $TestDateTime
        );
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_CEIL,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_TIME],
            $ReferenceID
        );
        $checkvalue = CustomFields::ConvertCustomFieldDataToWidget($checkvalue, CustomFields::WIDGET_TIME_SECONDSOFDAY);
        $this->assertTrue($checkvalue == '20:00', "$message ceil time in time field - $checkvalue - 20:00");

        //ultimo month

        $value = date('Y-m-d H:i', strtotime('2016-10-02 12:00'));
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $value,
            '',
            array(),
            $TestDateTime
        );
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_ULTIMO_MONTH,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-10-31 00:00:00'),
            "$message ultimo month of $value = " . date('l, Y-m-d H:i', $checkvalue)
        );

        $value = date('Y-m-d', strtotime('2016-02-02'));
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $value,
            '',
            array(),
            $TestDateTime
        );
        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_ULTIMO_MONTH,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-02-29 00:00:00'),
            "$message ultimo month of $value = " . date('l, Y-m-d', $checkvalue)
        );

        //easter

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_EASTER,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-27 00:00:00'),
            "$message easter of 2016 = " . date('l, Y-m-d H:i', $checkvalue)
        );

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_EASTER,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-03-27 00:00:00'),
            "$message easter of 2016 = " . date('l, Y-m-d', $checkvalue)
        );

        //next first of month in field

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_MONTH_FROM_FIELD,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-04-01 00:00:00'),
            "$message next first of month in field = " . date('l, Y-m-d H:i', $checkvalue)
        );

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_MONTH_FROM_FIELD,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2016-04-01 00:00:00'),
            "$message next first of month in field = " . date('l, Y-m-d', $checkvalue)
        );

        //next first of year in field

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_YEARS_FROM_FIELD,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2017-01-01 00:00:00'),
            "$message next first of year in field = " . date('l, Y-m-d H:i', $checkvalue)
        );

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_YEARS_FROM_FIELD,
            '',
            '',
            $delayWeekday
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2017-01-01 00:00:00'),
            "$message next first of year in field = " . date('l, Y-m-d', $checkvalue)
        );

        //next first of month from now
        $from_now = strtotime('2017-11-01 13:45');

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_MONTH_FROM_NOW,
            '',
            '',
            $delayWeekday,
            $from_now
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2017-12-01 00:00:00'),
            "$message next first of month from now = " . date('l, Y-m-d H:i', $checkvalue)
        );

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_MONTH_FROM_NOW,
            '',
            '',
            $delayWeekday,
            $from_now
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2017-12-01 00:00:00'),
            "$message next first of month from now = " . date('l, Y-m-d', $checkvalue)
        );

        //next first of year from now

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_YEARS_FROM_NOW,
            '',
            '',
            $delayWeekday,
            $from_now
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATETIME],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2018-01-01 00:00:00'),
            "$message next first of year from now = " . date('l, Y-m-d H:i', $checkvalue)
        );

        $this->testSetCalculatedField(
            $UserID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $CalcCfSubscriberID,
            CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_YEARS_FROM_NOW,
            '',
            '',
            $delayWeekday,
            $from_now
        );
        $checkvalue = CustomFields::GetCustomFieldData(
            $UserID,
            $CalcCfSubscriberID,
            $SourceCustomFields[CustomFields::TYPE_DATE],
            $ReferenceID
        );
        $this->assertTrue(
            $checkvalue == strtotime('2018-01-01 00:00:00'),
            "$message next first of year from now = " . date('l, Y-m-d', $checkvalue)
        );


        // --- TESTCASE: CALCULATE_OP_COPY - subscriber fields

        $op = CustomFields::CALCULATE_OP_COPY_SUBSCRIBER_FIELDS;

        $Parameters = array(
            'UserID' => $UserID,
            'ListInformation' => $ArraySubscriberList,
            'EmailAddress' => '<EMAIL>',
            'IPAddress' => '2001:0db8:85a3:0000:0000:8a2e:0370:7334',
            'SubscriptionReferrer' => APP_URL . 'confirm',
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SendConfirmationEmail' => false,
            'TriggerAutoResponders' => false,
        );

        $Result = Subscribers::Subscribe($Parameters);
        $Ipv6Subscriber = $Result[1];

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => "IPv6 address",
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );

        $NewCustomFieldIDIPv6 = CustomFields::Create($ArrayFieldAndValues);
        $NewCustomFieldIPv6 = CustomFields::RetrieveCustomField($NewCustomFieldIDIPv6, $UserID);

        $value = "OptInIP";
        $this->testSetCalculatedField(
            $UserID,
            $NewCustomFieldIPv6,
            $Ipv6Subscriber,
            CustomFields::CALCULATE_OP_COPY_SUBSCRIBER_FIELDS,
            $value
        );
        $checkvalue = CustomFields::GetCustomFieldData($UserID, $Ipv6Subscriber, $NewCustomFieldIPv6, $ReferenceID);
        $this->assertTrue($checkvalue == $Parameters['IPAddress'], "$message  copied IPv6 address");


        $SMSCustomFields = _testdata_custom_field_create_set($UserID, 'sms');
        foreach ($SMSCustomFields as $type => $field) {
            $this->assertTrue(!empty($field), "$message create sms customfield type $type");
        }

        $SubscriberFields = array(
            'SubscriberID' => $TargetCustomFields[CustomFields::TYPE_NUMBER],
            'EmailAddress' => $TargetCustomFields[CustomFields::TYPE_EMAIL],
            'SubscriptionStatus' => CustomFields::RetrieveCustomField('FirstName', $UserID),
            'BounceType' => CustomFields::RetrieveCustomField('LastName', $UserID),
            'SubscriptionDate' => $TargetCustomFields[CustomFields::TYPE_DATE],
            'SubscriptionIP' => CustomFields::RetrieveCustomField('Street1', $UserID),
            'SubscriberIP' => CustomFields::RetrieveCustomField('Street2', $UserID),
            'OptInIP' => CustomFields::RetrieveCustomField('City', $UserID),
            'UnsubscriptionDate' => $TargetCustomFields[CustomFields::TYPE_DATETIME],
            'OptInDate' => $SourceCustomFields[CustomFields::TYPE_DATE],
            'SubscriptionReferrer' => $TargetCustomFields[CustomFields::TYPE_URL],
            'PhoneNumber' => CustomFields::RetrieveCustomField('MobilePhone', $UserID),
            'SMSSubscriptionStatus' => $SMSCustomFields[CustomFields::TYPE_SINGLE],
            'SMSBounceType' => $SourceCustomFields[CustomFields::TYPE_SINGLE],
            'SMSSubscriptionDate' => $SMSCustomFields[CustomFields::TYPE_DATE],
            'SMSUnsubscriptionDate' => $SMSCustomFields[CustomFields::TYPE_DATETIME],
            'SubscriptionSMS' => $SMSCustomFields[CustomFields::TYPE_PARAGRAPH],
        );

        foreach ($SubscriberFields as $field => $CustomField) {
            $newValue = $this->testSetCalculatedField(
                $UserID,
                $CustomField,
                $CalcCfSubscriberID,
                $op,
                $field,
                '',
                array(),
                $TestDateTime
            );
            $this->assertTrue(
                $newValue !== false,
                "$message [$op] copy subscriber field '$field' into customfield {$CustomField['FieldName']}"
            );
        }

        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $CalcCfSubscriberID, $ReferenceID);

        foreach ($SubscriberFields as $field => $CustomField) {
            if ($field == 'SubscriberIP') {
                $field = 'LastOpenIP';
            }

            $fieldvalue = (empty($Subscription[$field])) ? '' : $Subscription[$field];
            $cfvalue = $Subscription["CustomField{$CustomField['CustomFieldID']}"];

            if ($field == 'BounceType' || $field == 'SMSBounceType') {
                $fieldvalue = t(Subscribers::$TranslationBounceTypes[$Subscription[$field]]);
            } elseif ($field == 'SubscriptionStatus' || $field == 'SMSSubscriptionStatus') {
                $fieldvalue = t(Subscribers::$DisplaySubscriptionStatus[$Subscription[$field]]);
            }

            $this->assertTrue(
                $fieldvalue == $cfvalue,
                "$message [$op] $field = '$cfvalue' copied into customfield {$CustomField['FieldName']} - $fieldvalue"
            );
        }

        $TestIPs = array(
            '0.0.0.0' => '0.0.0.0',
            '*******' => '*******',
            '*************' => '*************',
            '*************55' => '*************55',
            '-*******' => '*******',
            '0***********' => '***********',
            '**********' => false,
            '2********' => '********',
            '**********0' => '**********',
            '127.0.1234.1' => false,
            '*************4' => '*************',
        );

        $regex = '/(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)/';
        foreach ($TestIPs as $ip => $expected) {
            $match = array();
            preg_match($regex, $ip, $match);
            $this->assertTrue(
                $match[0] == $expected,
                "$message parse ip '$ip' = " . ((!$expected) ? 'FALSE' : $expected) . " - " . ((!$match[0]) ? 'FALSE' : $match[0])
            );
        }

        /**
         * Creates a new firstname with given values
         **/
        $message = 'CustomFields::CreateFirstname and CustomFields::RetrieveFirstNameAndGender';
        $TestData = array(
            0 => array(
                'firstname' => 'Tom',
                'gender' => CustomFields::GENDER_MALE,
            ),
            1 => array(
                'firstname' => 'Anne-Marie',
                'gender' => CustomFields::GENDER_FEMALE,
            ),
            2 => array(
                'firstname' => 'Annegret',
                'gender' => CustomFields::GENDER_FEMALE,
            )
        );
        foreach ($TestData as $name) {
            CustomFields::CreateFirstname($name['firstname'], $name['gender']);
            $firstName = CustomFields::RetrieveFirstNameAndGender($name['firstname']);
            $this->assertTrue(!empty($firstName), $message . ' firstname created');
            $this->assertTrue($firstName['firstname'] == strtolower($name['firstname']), $message . ' firstname equal');
            $this->assertTrue($firstName['gender'] == $name['gender'], $message . ' gender equal');
        }

        $message = 'CustomFields::RecognizeNames';
        $expectedFirstName = "Tom";
        $expectedLastName = "Tailor";

        // case 1: whole local part is a firstname
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue(empty($recognizedNames['LastName']), $message . ' no lastname recognized');

        // case 2: after split, local part consists of two parts of which one is a firstname
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == $expectedLastName, $message . ' lastname recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == $expectedLastName, $message . ' lastname recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == $expectedLastName, $message . ' lastname recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == $expectedLastName, $message . ' lastname recognized');

        // case 3: upper and lowercase chars as well as numbers do not matter
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == $expectedLastName, $message . ' lastname recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == $expectedLastName, $message . ' lastname recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == $expectedLastName, $message . ' lastname recognized');

        // case 4: lastname must have at least two characters
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue(empty($recognizedNames['LastName']), $message . ' no lastname recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == ucfirst("au"), $message . ' lastname recognized');
        ;

        // case 5: after split, local part consists of two parts of which one is a firstname but lastname contains special character
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue(empty($recognizedNames['LastName']), $message . ' no lastname recognized');

        // case 6: local part does not contain a firstname
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue(empty($recognizedNames), $message . ' no names recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue(empty($recognizedNames), $message . ' no names recognized');

        // case 7: after split, local part consists of more than two parts
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue(empty($recognizedNames['LastName']), $message . ' no lastname recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue(empty($recognizedNames['LastName']), $message . ' no lastname recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue(empty($recognizedNames['LastName']), $message . ' no lastname recognized');

        // case 8: double name
        $expectedFirstName = "Anne-Marie";
        $expectedLastName = "Kramp-Karrenbauer";
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == $expectedLastName, $message . ' lastname recognized');
        $recognizedNames = CustomFields::RecognizeNames("<EMAIL>");
        $this->assertTrue($recognizedNames['FirstName'] == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($recognizedNames['LastName'] == $expectedLastName, $message . ' lastname recognized');

        $message = 'CustomFields::CalculateNameOfSubscriber';
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'FirstNameField',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $FirstNameCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($FirstNameCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'LastNameField',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $LastNameCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($LastNameCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'Gender',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $GenderCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue($GenderCustomFieldID > 0, $message . ' ' . $ArrayFieldAndValues['FieldName']);

        $Result = Subscribers::Subscribe([
            'UserID' => $UserID,
            'ListInformation' => $ArraySubscriberList,
            'EmailAddress' => "<EMAIL>",
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
            'SendConfirmationEmail' => false,
            'TriggerAutoResponders' => false,
        ]);
        $this->assertTrue($Result[0], $message . " add subscriber");
        $SubscriberID = $Result[1];

        // clear field cache
        TransactionEmails::GetCachedCustomFields($UserID, true);
        $FirstNameField = CustomFields::RetrieveCustomField($FirstNameCustomFieldID, $UserID);
        $LastNameField = CustomFields::RetrieveCustomField($LastNameCustomFieldID, $UserID);
        $GenderField = CustomFields::RetrieveCustomField($GenderCustomFieldID, $UserID);

        // case 1: identify firstname only
        $Updated = CustomFields::CalculateNameOfSubscriber(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FirstNameCustomFieldID
        );
        $this->assertTrue($Updated, $message . ' name fields updated');
        $firstNameValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FirstNameField, $ReferenceID);
        $lastNameValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $LastNameField, $ReferenceID);
        $this->assertTrue($firstNameValue == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue(empty($lastNameValue), $message . ' no lastname recognized');

        // case 2: identify firstname and lastname
        $Updated = CustomFields::CalculateNameOfSubscriber(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FirstNameCustomFieldID,
            $LastNameCustomFieldID
        );
        $this->assertTrue($Updated, $message . ' name fields updated');
        $firstNameValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FirstNameField, $ReferenceID);
        $lastNameValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $LastNameField, $ReferenceID);
        $this->assertTrue($firstNameValue == $expectedFirstName, $message . ' firstname recognized');
        $this->assertTrue($lastNameValue == $expectedLastName, $message . ' lastname recognized');

        // case 3: overwrite existing firstname and lastname if recognized names match the stored ones
        $Updated = CustomFields::CalculateNameOfSubscriber(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FirstNameCustomFieldID,
            $LastNameCustomFieldID
        );
        $this->assertFalse($Updated, $message . ' not overwritten');

        // case 4: do not overwrite if recognized firstname differs from stored one
        // update email address in order to recognize different firstname
        $Updated = Subscribers::UpdateSubscription($UserID, $SubscriberID, array(
            'EmailAddress' => '<EMAIL>'
        ), Subscribers::DOI_PROCESS_USE_SINGLE_OPTIN);
        $this->assertTrue($Updated['Success'], $message . ' new email address updated');
        $Updated = CustomFields::CalculateNameOfSubscriber(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FirstNameCustomFieldID,
            $LastNameCustomFieldID
        );
        $this->assertFalse($Updated, $message . ' not overwritten');
        // fields not updated
        $firstNameValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FirstNameField, $ReferenceID);
        $lastNameValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $LastNameField, $ReferenceID);
        $this->assertTrue($firstNameValue == $expectedFirstName, $message . ' firstname not changed');
        $this->assertTrue($lastNameValue == $expectedLastName, $message . ' lastname not changed');

        $message = 'CustomFields::CalculateGenderFromFirstname';
        $fieldValues = array(t("Male"), t("Female"), t("Unisex"));
        $tagIDs = array();
        foreach ($fieldValues as $fieldValue) {
            $tagIDs[] = Tag::CreateManualTag($UserID, $fieldValue, "");
        }
        $ExpectedGender = $fieldValues[1];
        $ExpectedTagID = $tagIDs[1];

        // case 1: identify gender
        $Updated = CustomFields::CalculateGenderFromFirstname(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FirstNameCustomFieldID,
            $GenderCustomFieldID,
            $fieldValues,
            []
        );
        $genderValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $GenderField, $ReferenceID);
        $this->assertTrue($Updated, $message . " gender recognized: $genderValue");
        $this->assertTrue($genderValue == $ExpectedGender, $message . ' gender recognized and updated');
        foreach ($tagIDs as $tagID) {
            $this->assertFalse(
                Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID),
                $message . " not tagged with tagID $tagID"
            );
        }

        // case 2: identify gender and tag
        // empty existing gender first
        CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $GenderField, "");
        //use new gender values
        $fieldValues = array(t("Man"), t("Woman"), t("N/A"));
        $ExpectedGender = $fieldValues[1];
        $Updated = CustomFields::CalculateGenderFromFirstname(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FirstNameCustomFieldID,
            $GenderCustomFieldID,
            $fieldValues,
            $tagIDs
        );
        $genderValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $GenderField, $ReferenceID);
        $this->assertTrue($Updated, $message . " gender recognized: $genderValue");
        $this->assertTrue($genderValue == $ExpectedGender, $message . ' gender recognized and updated');
        foreach ($tagIDs as $tagID) {
            if ($tagID == $ExpectedTagID) {
                $this->assertTrue(
                    !empty(Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID)),
                    $message . " subscriber tagged with tagID $tagID"
                );
            } else {
                $this->assertFalse(
                    Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID),
                    $message . " not tagged with tagID $tagID"
                );
            }
        }

        // case 3: do not overwrite existing gender
        //keep existing value
        $existingValue = $ExpectedGender; //from previous test
        //use new gender values
        $fieldValues = array(t("Boy"), t("Girl"), t("???"));
        $ExpectedGender = $fieldValues[1];
        $Updated = CustomFields::CalculateGenderFromFirstname(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FirstNameCustomFieldID,
            $GenderCustomFieldID,
            $fieldValues,
            $tagIDs
        );
        $genderValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $GenderField, $ReferenceID);
        $this->assertTrue($Updated, $message . " gender recognized: $genderValue");
        $this->assertTrue($genderValue != $ExpectedGender, $message . ' gender not overwritten');
        $this->assertTrue($genderValue == $existingValue, $message . ' gender value unchanged');

        /*
         * Personalize Custom Field Value
         */

        $message = "PersonalizeCustomFieldData:";

        //create custom fields to create placeholder
        $PersoCustomFields = array(
            array(
                'name' => 'TYPE_SINGLE',
                'type' => CustomFields::TYPE_SINGLE,
                'value' => 'This is a one-liner.',
                'expected' => 'This is a one-liner.',
                'placeholder' => '', //will be generated after creating cf, need cf id
            ),
            array(
                'name' => 'TYPE_PARAGRAPH',
                'type' => CustomFields::TYPE_PARAGRAPH,
                'value' => "This is a\nmultiline\nvalue.",
                'expected' => "This is a\nmultiline\nvalue.",
            ),
            array(
                'name' => 'TYPE_EMAIL',
                'type' => CustomFields::TYPE_EMAIL,
                'value' => '<EMAIL>',
                'expected' => '<EMAIL>',
            ),
            array(
                'name' => 'TYPE_NUMBER',
                'type' => CustomFields::TYPE_NUMBER,
                'value' => 4711,
                'expected' => 4711,
            ),
            array(
                'name' => 'TYPE_DATE',
                'type' => CustomFields::TYPE_DATE,
                'value' => strtotime('2021-01-11'),
                'expected' => Dates::formatDate(Dates::FORMAT_DMY, strtotime('2021-01-11'))
            ),
            array(
                'name' => 'TYPE_TIME',
                'type' => CustomFields::TYPE_TIME,
                'value' => 12 * 60 * 60 + 34 * 60,
                'expected' => '12:34'
            ),
            array(
                'name' => 'TYPE_DATETIME',
                'type' => CustomFields::TYPE_DATETIME,
                'value' => strtotime('2021-01-11 12:34'),
                'expected' => Dates::formatDate(Dates::FORMAT_DMYHI, strtotime('2021-01-11 12:34'))
            ),
            array(
                'name' => 'TYPE_HTML',
                'type' => CustomFields::TYPE_HTML,
                'value' => '<p>This is HTML</p>',
                'expected' => '<p>This is HTML</p>',
            ),
            array(
                'name' => 'TYPE_DECIMAL',
                'type' => CustomFields::TYPE_DECIMAL,
                'value' => 12345,
                'expected' => klicktipp_number_format(123.45, 2),
            )
        );

        $SubscriberFields = array(
            'SubscriberID' => array(
                'value' => 0,
                'placeholder' => '%Subscriber:SubscriberID%'
            ),
            'EmailAddress' => array(
                'value' => '<EMAIL>',
                'placeholder' => '%Subscriber:EmailAddress%'
            ),
            'BounceType' => array(
                'value' => 0,
                'placeholder' => '%Subscriber:BounceType%'
            ),
            'OptInDate' => array(
                'value' => 0,
                'placeholder' => '%Subscriber:OptInDate%',
                'formatDate' => true
            ),
            'SubscriptionStatus' => array(
                'value' => 0,
                'placeholder' => '%Subscriber:SubscriptionStatus%'
            ),
            'SubscriptionDate' => array(
                'value' => '',
                'placeholder' => '%Subscriber:SubscriptionDate%',
                'formatDate' => true
            ),
            'SubscriptionIP' => array(
                'value' => '*************',
                'placeholder' => '%Subscriber:SubscriptionIP%'
            ),
            'UnsubscriptionDate' => array(
                'value' => '',
                'placeholder' => '%Subscriber:UnsubscriptionDate%',
                'formatDate' => true
            ),
            'UnsubscriptionIP' => array(
                'value' => '*************',
                'placeholder' => '%Subscriber:UnsubscriptionIP%'
            ),
            'PhoneNumber' => array(
                'value' => '004917612345678',
                'placeholder' => '%Subscriber:PhoneNumber%'
            ),
            'SMSSubscriptionStatus' => array(
                'value' => 0,
                'placeholder' => '%Subscriber:SMSSubscriptionStatus%'
            ),
            'SMSBounceType' => array(
                'value' => 0,
                'placeholder' => '%Subscriber:SMSBounceType%'
            ),
            'SMSSubscriptionDate' => array(
                'value' => '',
                'placeholder' => '%Subscriber:SMSSubscriptionDate%',
                'formatDate' => true
            ),
            'SMSUnsubscriptionDate' => array(
                'value' => '',
                'placeholder' => '%Subscriber:SMSUnsubscriptionDate%',
                'formatDate' => true
            ),
            'SubscriptionSMS' => array(
                'value' => 'SMS content',
                'placeholder' => '%Subscriber:SubscriptionSMS%'
            ),
        );

        $SubscribeData = array();

        foreach ($PersoCustomFields as &$Field) {
            $NewCustomFieldID = CustomFields::Create(array(
                'RelOwnerUserID' => $UserID,
                'FieldName' => $Field['name'],
                'FieldTypeEnum' => $Field['type'],
            ));

            $Field['placeholder'] = "%Subscriber:CustomField$NewCustomFieldID%";

            $SubscribeData["CustomField$NewCustomFieldID"] = $Field['value'];
        }

        $TargetSingleID = CustomFields::Create(array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'Target Single',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        ));
        $newfield = CustomFields::FromID($UserID, $TargetSingleID);
        $TargetSingle = $newfield->GetData();

        $TargetParagraphID = CustomFields::Create(array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'Target Paragraph',
            'FieldTypeEnum' => CustomFields::TYPE_PARAGRAPH,
        ));
        $newfield = CustomFields::FromID($UserID, $TargetParagraphID);
        $TargetParagraph = $newfield->GetData();

        $TargetHTMLID = CustomFields::Create(array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'Target HTML',
            'FieldTypeEnum' => CustomFields::TYPE_HTML,
        ));
        $newfield = CustomFields::FromID($UserID, $TargetHTMLID);
        $TargetHTML = $newfield->GetData();

        $Parameters = array(
            'ReferenceID' => $ReferenceID,
            'UserID' => $UserID,
            'ListInformation' => array('ListID' => 0), //import
            'EmailAddress' => $SubscriberFields['EmailAddress']['value'],
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SendConfirmationEmail' => false,
            'TriggerAutoResponders' => false,
            'OtherFields' => $SubscribeData,
            'IPAddress' => $SubscriberFields['SubscriptionIP']['value'],
            'PhoneNumber' => $SubscriberFields['PhoneNumber']['value'],
            'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SubscriptionReferrer' => $SubscriberFields['SubscriptionSMS']['value'],
            'ImportOptInDate' => strtotime('-1 day')
        );

        $Result = Subscribers::Subscribe($Parameters);

        $SubscriberID = $Result[1];

        Subscribers::Unsubscribe(
            $UserID,
            $SubscriberFields['PhoneNumber']['value'],
            0,
            '0.0.0.0',
            Subscription::SUBSCRIPTIONTYPE_SMS
        );
        Subscribers::Unsubscribe(
            $UserID,
            $SubscriberFields['EmailAddress']['value'],
            0,
            $SubscriberFields['UnsubscriptionIP']['value'],
            Subscription::SUBSCRIPTIONTYPE_EMAIL
        );

        $FullSubscriber = Subscribers::RetrieveFullSubscriberWithFields(
            $ArraySubscriberList['RelOwnerUserID'],
            $SubscriberID,
            $ReferenceID
        );

        foreach ($SubscriberFields as $key => $SField) {
            if (empty($SField['value'])) {
                if ($SField['formatDate']) {
                    $SubscriberFields[$key]['value'] = Dates::formatDate(Dates::FORMAT_DMY, (int) $FullSubscriber[$key]);
                } else {
                    $SubscriberFields[$key]['value'] = $FullSubscriber[$key];
                }
            }
        }

        // --- Type Single ---

        // --- set value
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetSingle,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message Single -> personalize set value");

        // --- prepend value

        $presetValue = ' ###prepend';
        $this->testSetCalculatedField(
            $UserID,
            $TargetSingle,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $presetValue
        );
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $ExpectedValue .= $presetValue;
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetSingle,
            $SubscriberID,
            CustomFields::CALCULATE_OP_TEXT_PREPEND,
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message Single -> personalize prepend value");

        // --- append value

        $presetValue = 'append### ';
        $this->testSetCalculatedField(
            $UserID,
            $TargetSingle,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $presetValue
        );
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $ExpectedValue = $presetValue . $ExpectedValue;
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetSingle,
            $SubscriberID,
            CustomFields::CALCULATE_OP_TEXT_APPEND,
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message Single -> personalize append value");

        // --- replace value

        $presetValue = '### replace ###';
        $this->testSetCalculatedField(
            $UserID,
            $TargetSingle,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $presetValue
        );
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $ExpectedValue = str_replace('replace', $ExpectedValue, $presetValue);
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetSingle,
            $SubscriberID,
            CustomFields::CALCULATE_OP_TEXT_REPLACE,
            'replace',
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message Single -> personalize replace value");

        // --- Type Paragraph ---

        // --- set value
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetParagraph,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message Paragraph -> personalize set value");

        // --- prepend value

        $presetValue = ' ###prepend';
        $this->testSetCalculatedField(
            $UserID,
            $TargetParagraph,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $presetValue
        );
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $ExpectedValue .= $presetValue;
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetParagraph,
            $SubscriberID,
            CustomFields::CALCULATE_OP_TEXT_PREPEND,
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message Paragraph -> personalize prepend value");

        // --- append value

        $presetValue = 'append### ';
        $this->testSetCalculatedField(
            $UserID,
            $TargetParagraph,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $presetValue
        );
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $ExpectedValue = $presetValue . $ExpectedValue;
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetParagraph,
            $SubscriberID,
            CustomFields::CALCULATE_OP_TEXT_APPEND,
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message Paragraph -> personalize append value");

        // --- replace value

        $presetValue = '### replace ###';
        $this->testSetCalculatedField(
            $UserID,
            $TargetParagraph,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $presetValue
        );
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $ExpectedValue = str_replace('replace', $ExpectedValue, $presetValue);
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetParagraph,
            $SubscriberID,
            CustomFields::CALCULATE_OP_TEXT_REPLACE,
            'replace',
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message Paragraph -> personalize replace value");

        // --- Type HTML ---

        // --- set value
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetHTML,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message HTML -> personalize set value");

        // --- prepend value

        $presetValue = ' ###prepend';
        $this->testSetCalculatedField(
            $UserID,
            $TargetHTML,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $presetValue
        );
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $ExpectedValue .= $presetValue;
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetHTML,
            $SubscriberID,
            CustomFields::CALCULATE_OP_TEXT_PREPEND,
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message HTML -> personalize prepend value");

        // --- append value

        $presetValue = 'append### ';
        $this->testSetCalculatedField(
            $UserID,
            $TargetHTML,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $presetValue
        );
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $ExpectedValue = $presetValue . $ExpectedValue;
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetHTML,
            $SubscriberID,
            CustomFields::CALCULATE_OP_TEXT_APPEND,
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message HTML -> personalize append value");

        // --- replace value

        $presetValue = '### replace ###';
        $this->testSetCalculatedField(
            $UserID,
            $TargetHTML,
            $SubscriberID,
            CustomFields::CALCULATE_OP_VALUE,
            $presetValue
        );
        $SetFieldValue = implode(' | ', array_column($PersoCustomFields, 'placeholder')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'placeholder'));
        $ExpectedValue = implode(' | ', array_column($PersoCustomFields, 'expected')) . ' | ' .
            implode(' | ', array_column($SubscriberFields, 'value'));
        $ExpectedValue = str_replace('replace', $ExpectedValue, $presetValue);
        $CalculatedValue = $this->testSetCalculatedField(
            $UserID,
            $TargetHTML,
            $SubscriberID,
            CustomFields::CALCULATE_OP_TEXT_REPLACE,
            'replace',
            $SetFieldValue
        );

        $this->assertTrue($ExpectedValue == $CalculatedValue, "$message HTML -> personalize replace value");
    }

    /**
     * @dataProvider dataProviderDecimalInput
     */
    public function testDecimalUserInput(array $testData)
    {
        foreach ($testData as $data) {
            $this->validateDecimalValue($data);
        }
    }

    private function testSetCalculatedField(
        $UserID,
        $FieldInformation,
        $SubscriberID,
        $op,
        $value = 0,
        $value2 = 0,
        $delayDayOfWeek = [],
        $time = 0
    ) {
        $ReferenceID = 0;

        $account = user_load($UserID);
        $NewValue = CustomFields::CalculateCustomFieldData(
            $account,
            $SubscriberID,
            $ReferenceID,
            $FieldInformation,
            $op,
            $value,
            $value2,
            $delayDayOfWeek,
            $time
        );

        if ($NewValue !== false) {
            CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $NewValue);
        }
        return $NewValue;
    }

    public function dataProviderDecimalInput(): array
    {
        return [
            'Validate user input' => [
                'testData' => [
                    // normal float, decimal point ,
                    [
                        'input' => '123,45',
                        'isValid' => true,
                        'formatted' => '123.45',
                        'toDB' => '12345',
                        'fromDBConverted' => '123,45',
                        'float' => '123.45',
                    ],
                    // normal float, decimal point .
                    [
                        'input' => '123.45',
                        'isValid' => true,
                        'formatted' => '123.45',
                        'toDB' => '12345',
                        'fromDBConverted' => '123,45',
                        'float' => '123.45',
                    ],
                    // normal negative float, decimal point ,
                    [
                        'input' => '-123,45',
                        'isValid' => true,
                        'formatted' => '-123.45',
                        'toDB' => '-12345',
                        'fromDBConverted' => '-123,45',
                        'float' => '-123.45',
                    ],
                    // normal float, decimal point .
                    [
                        'input' => '-123.45',
                        'isValid' => true,
                        'formatted' => '-123.45',
                        'toDB' => '-12345',
                        'fromDBConverted' => '-123,45',
                        'float' => '-123.45',
                    ],
                    // normal float with more than 2 fractional digits, decimal point ,
                    [
                        'input' => '123,4567',
                        'isValid' => true,
                        'formatted' => '123.45',
                        'toDB' => '12345',
                        'fromDBConverted' => '123,45',
                        'float' => '123.45',
                    ],
                    // normal float with more than 2 fractional digits, decimal point .
                    [
                        'input' => '123.4567',
                        'isValid' => true,
                        'formatted' => '123.45',
                        'toDB' => '12345',
                        'fromDBConverted' => '123,45',
                        'float' => '123.45',
                    ],
                    // normal negative float with more than 2 fractional digits, decimal point ,
                    [
                        'input' => '-123,4567',
                        'isValid' => true,
                        'formatted' => '-123.45',
                        'toDB' => '-12345',
                        'fromDBConverted' => '-123,45',
                        'float' => '-123.45',
                    ],
                    // normal negative float with more than 2 fractional digits, decimal point .
                    [
                        'input' => '-123.4567',
                        'isValid' => true,
                        'formatted' => '-123.45',
                        'toDB' => '-12345',
                        'fromDBConverted' => '-123,45',
                        'float' => '-123.45',
                    ],
                    // float with 1 thousand separator, decimal point ,
                    [
                        'input' => '9.123,45',
                        'isValid' => true,
                        'formatted' => '9123.45',
                        'toDB' => '912345',
                        'fromDBConverted' => '9123,45',
                        'float' => '9123.45',
                    ],
                    // float with 1 thousand separator, decimal point .
                    [
                        'input' => '9,123.45',
                        'isValid' => true,
                        'formatted' => '9123.45',
                        'toDB' => '912345',
                        'fromDBConverted' => '9123,45',
                        'float' => '9123.45',
                    ],
                    // negative float with 1 thousand separator, decimal point ,
                    [
                        'input' => '-9.123,45',
                        'isValid' => true,
                        'formatted' => '-9123.45',
                        'toDB' => '-912345',
                        'fromDBConverted' => '-9123,45',
                        'float' => '-9123.45',
                    ],
                    // negative float with 1 thousand separator, decimal point .
                    [
                        'input' => '-9,123.45',
                        'isValid' => true,
                        'formatted' => '-9123.45',
                        'toDB' => '-912345',
                        'fromDBConverted' => '-9123,45',
                        'float' => '-9123.45',
                    ],
                    // float with 1 thousand separator with more than 2 fractional digits, decimal point ,
                    [
                        'input' => '9.123,4567',
                        'isValid' => true,
                        'formatted' => '9123.45',
                        'toDB' => '912345',
                        'fromDBConverted' => '9123,45',
                        'float' => '9123.45',
                    ],
                    // float with 1 thousand separator with more than 2 fractional digits, decimal point .
                    [
                        'input' => '9,123.4567',
                        'isValid' => true,
                        'formatted' => '9123.45',
                        'toDB' => '912345',
                        'fromDBConverted' => '9123,45',
                        'float' => '9123.45',
                    ],
                    // negative float with 1 thousand separator with more than 2 fractional digits, decimal point ,
                    [
                        'input' => '-9.123,4567',
                        'isValid' => true,
                        'formatted' => '-9123.45',
                        'toDB' => '-912345',
                        'fromDBConverted' => '-9123,45',
                        'float' => '-9123.45',
                    ],
                    // negative float with 1 thousand separator with more than 2 fractional digits, decimal point .
                    [
                        'input' => '-9,123.4567',
                        'isValid' => true,
                        'formatted' => '-9123.45',
                        'toDB' => '-912345',
                        'fromDBConverted' => '-9123,45',
                        'float' => '-9123.45',
                    ],
                    // float with 2 thousand separator, decimal point ,
                    [
                        'input' => '1.009.123,45',
                        'isValid' => true,
                        'formatted' => '1009123.45',
                        'toDB' => '100912345',
                        'fromDBConverted' => '1009123,45',
                        'float' => '1009123.45',
                    ],
                    // float with 2 thousand separator, decimal point .
                    [
                        'input' => '1,009,123.45',
                        'isValid' => true,
                        'formatted' => '1009123.45',
                        'toDB' => '100912345',
                        'fromDBConverted' => '1009123,45',
                        'float' => '1009123.45',
                    ],
                    // negative float with 2 thousand separator, decimal point ,
                    [
                        'input' => '-1.009.123,45',
                        'isValid' => true,
                        'formatted' => '-1009123.45',
                        'toDB' => '-100912345',
                        'fromDBConverted' => '-1009123,45',
                        'float' => '-1009123.45',
                    ],
                    // negative float with 1 thousand separator, decimal point .
                    [
                        'input' => '-1,009,123.45',
                        'isValid' => true,
                        'formatted' => '-1009123.45',
                        'toDB' => '-100912345',
                        'fromDBConverted' => '-1009123,45',
                        'float' => '-1009123.45',
                    ],
                    // float with 1 thousand separator with more than 2 fractional digits, decimal point ,
                    [
                        'input' => '1.009.123,4567',
                        'isValid' => true,
                        'formatted' => '1009123.45',
                        'toDB' => '100912345',
                        'fromDBConverted' => '1009123,45',
                        'float' => '1009123.45',
                    ],
                    // float with 1 thousand separator with more than 2 fractional digits, decimal point .
                    [
                        'input' => '1,009,123.4567',
                        'isValid' => true,
                        'formatted' => '1009123.45',
                        'toDB' => '100912345',
                        'fromDBConverted' => '1009123,45',
                        'float' => '1009123.45',
                    ],
                    // negative float with 1 thousand separator with more than 2 fractional digits, decimal point ,
                    [
                        'input' => '-1.009.123,4567',
                        'isValid' => true,
                        'formatted' => '-1009123.45',
                        'toDB' => '-100912345',
                        'fromDBConverted' => '-1009123,45',
                        'float' => '-1009123.45',
                    ],
                    // negative float with 1 thousand separator with more than 2 fractional digits, decimal point .
                    [
                        'input' => '-1,009,123.4567',
                        'isValid' => true,
                        'formatted' => '-1009123.45',
                        'toDB' => '-100912345',
                        'fromDBConverted' => '-1009123,45',
                        'float' => '-1009123.45',
                    ],

                    // float with multiple decimal points .
                    [
                        'input' => '1.009.123.4567',
                        'isValid' => true,
                        'formatted' => '1009123.45',
                        'toDB' => '100912345',
                        'fromDBConverted' => '1009123,45',
                        'float' => '1009123.45',
                    ],
                    // float with multiple decimal points .
                    [
                        'input' => '1,009,123,4567',
                        'isValid' => true,
                        'formatted' => '1009123.45',
                        'toDB' => '100912345',
                        'fromDBConverted' => '1009123,45',
                        'float' => '1009123.45',
                    ],
                    // 0 float
                    [
                        'input' => '0',
                        'isValid' => true,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0',
                    ],
                    // 0.0 float
                    [
                        'input' => '0.0',
                        'isValid' => true,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0',
                    ],
                    // 0.00 float
                    [
                        'input' => '0.00',
                        'isValid' => true,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0',
                    ],
                    // 0,0 float
                    [
                        'input' => '0,0',
                        'isValid' => true,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0',
                    ],
                    // 0,00 float
                    [
                        'input' => '0,00',
                        'isValid' => true,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0',
                    ],
                    // empty float
                    [
                        'input' => '',
                        'isValid' => true,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0',
                    ],
                    // invalid float
                    [
                        'input' => 'invalid',
                        'isValid' => false,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0',
                    ],
                    // multiple - at beginning float, OK
                    [
                        'input' => '--1,23',
                        'isValid' => false,
                        'formatted' => '-1.23',
                        'toDB' => '-123',
                        'fromDBConverted' => '-1,23',
                        'float' => '-1.23',
                    ],
                    // - not at beginning float, OK
                    [
                        'input' => '1,-23',
                        'isValid' => false,
                        'formatted' => '1.23',
                        'toDB' => '123',
                        'fromDBConverted' => '1,23',
                        'float' => '1.23',
                    ],
                    // - int value
                    [
                        'input' => '100',
                        'isValid' => true,
                        'formatted' => '100.00',
                        'toDB' => '10000',
                        'fromDBConverted' => '100,00',
                        'float' => '100.00',
                    ],
                    // - 0000.0000 => 0.00
                    [
                        'input' => '0000.0000',
                        'isValid' => true,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0.00',
                    ],
                    // - 0000,0000 => 0.00
                    [
                        'input' => '0000,0000',
                        'isValid' => true,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0.00',
                    ],
                    [
                        'input' => '0000',
                        'isValid' => true,
                        'formatted' => '0.00',
                        'toDB' => '0',
                        'fromDBConverted' => '0,00',
                        'float' => '0.00',
                    ],
                ]
            ]
        ];
    }

    private function validateDecimalValue(array $data)
    {
        $field = CustomFields::RetrieveCustomField('LeadValue');

        $isValid = CustomFields::ValidateCustomFieldValue($field, $data['input']);

        $this->assertEquals($isValid[0], $data['isValid'], "Input validation mismatch");

        $formatted = UtilsNumber::formatStringToFloat($data['input']);

        $this->assertEquals($formatted, $data['formatted'], "Formatted value mismatch");

        $floatValue = UtilsNumber::formatStringToFloat($data['input']);

        $this->assertEquals($floatValue, $data['float'], "Float value mismatch");

        $toDB = CustomFields::ConvertCustomFieldDataFromWidget($data['input'], CustomFields::WIDGET_DECIMAL);

        $this->assertEquals($toDB, $data['toDB'], "DB value mismatch");

        $fromDBConverted = CustomFields::ConvertCustomFieldDataToWidget($toDB, CustomFields::WIDGET_DECIMAL);

        $this->assertEquals($fromDBConverted, $data['fromDBConverted'], "From DB converted value mismatch");
    }

    //TODO FieldCopyTo testen
}

<?php

namespace App\Tests\Integration\Klicktipp;

use App\Klicktipp\AutoresponderQueue;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Dates;
use App\Klicktipp\LegacyApi\LegacyApiSubscriber;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\ServicesException;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\TestData\TestUser;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\User;
use App\Tests\Integration\Helpers\LanguageAware;
use Doctrine\DBAL\Connection;
use KlicktippPartnerTestConnector;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Exception;

class ApiSubscribersTest extends WebTestCase
{
    use LanguageAware;

    private ?Connection $connection;

    private KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();
        $GLOBALS['app'] = $this->getContainer()->get('kernel');
        /** @var Connection $connection */
        $connection = $this->getContainer()->get('database_connection');
        $this->connection = $connection;
        $this->setUpTestLanguages();
    }

    public function tearDown(): void
    {
        $this->connection = null;
    }

    /**
     * @throws Exception
     */
    public function testDrupalSimpleTest(): void
    {
        /* load API functions */
        global $last_services_error;
        function services_error($message, $code = 0, $data = null): bool
        {
            global $last_services_error;
            $last_services_error = array($message, $code, $data);
            return false;
        }

        Libraries::include('api_subscribers.inc', '/api');

        Libraries::include('full_user.inc', '/tests/includes');
        Libraries::include('transactional.inc', '/tests/includes');
        Libraries::include('api.inc', '/tests/includes');

        $this->assertNotNull($this->connection, 'Database connection is not null');
        $testUser = new TestUser($this->connection);
        $testUser->getAnonymousUser();
        $UserID = $testUser->getEnterpriseUser(); // api_resource works on global user, so we should too
        $testUser->getPremiumUser();
        /** @var User $rawUser */
        $rawUser = $testUser->loginUser($this->client, $UserID);
        $user = (object)$rawUser->getData();
        $ReferenceID = 0;

        $message = 'prepeare data';

        /**************
         * NOTE
         * This uses the partner connector to test api function directly through the connector.
         * The partner connector does not need a session, so we overcome the session problem in test scripts (see klicktipp_api_connector.inc)
         */

        /** @var KlicktippPartnerTestConnector $connector */
        [$connector, $partner, $customer, $developer_key, $customer_key, $useragent] = prepare_api_connector_test(
            $this,
            'simpletest1234'
        );

        if (!$useragent) {
            // dont proceed without proper user agent
            return;
        }

        // create tags
        $TagName1 = 'some tag name';
        $TagID1 = Tag::CreateManualTag($UserID, $TagName1, '');
        $this->assertTrue($TagID1 > 0, $message . ' tag 1');
        $TagID2 = Tag::CreateManualTag($UserID, 'some tag name 2', '');
        $this->assertTrue($TagID2 > 0, $message . ' tag 2');

        // create list 1
        $ListName1 = 'List 1';
        $ArrayFieldAndValues = array(
            'Name' => $ListName1,
            'RelOwnerUserID' => $UserID,
        );
        $ListID1 = Lists::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($ListID1 > 0, $message . ' list 1');
        $ArraySubscriberList = Lists::RetrieveListByID($UserID, $ListID1);

        // create one custom field
        $UserCustomFieldName = 'user field';
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => $UserCustomFieldName,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        );
        $UserCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        // this is the user defined field
        $UserCustomFieldInformation = CustomFields::RetrieveCustomField($UserCustomFieldID, $UserID);
        $this->assertTrue(is_array($UserCustomFieldInformation), $message . ' user field exists');
        // create second custom field
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'html field',
            'FieldTypeEnum' => CustomFields::TYPE_HTML,
        );
        $HTMLCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        // this is the user defined field
        $HTMLCustomFieldInformation = CustomFields::RetrieveCustomField($HTMLCustomFieldID, $UserID);
        $this->assertTrue(is_array($HTMLCustomFieldInformation), $message . ' html field exists');
        // this is the global field
        $GlobalCustomFieldInformation = CustomFields::RetrieveCustomField('FirstName');
        $this->assertTrue(is_array($GlobalCustomFieldInformation), $message . ' global field exists');

        // create a subscriber
        $UserFieldValue = 'Userfield-Value';
        $HTMLFieldValue = "<ul style=\"margin-top:1em; margin-bottom:1.5em;\">
  <li style=\"margin-bottom:0.3em;\"><span style=\"font-family:'Courier New';color:#142D68; font-size:1.072em;\">manual_tags</span> – assoziatives Array manuelle Tags <span style=\"font-family:'Courier New';color:#142D68; font-size:1.072em;\">[tag_id] =&gt; [timestamp]</span></li>
  <li style=\"margin-bottom:0.3em;\"><span style=\"font-family:'Courier New';color:#142D68; font-size:1.072em;\">smart_links</span> – assoziatives Array SmartLinks <span style=\"font-family:'Courier New';color:#142D68; font-size:1.072em;\">[tag_id] =&gt; [timestamp]</span></li>
  <li style=\"margin-bottom:0.3em;\"><span style=\"font-family:'Courier New';color:#142D68; font-size:1.072em;\">emails_sent</span> – assoziatives Array „Newsletter / Autoresponder erhalten“ <span style=\"font-family:'Courier New';color:#142D68; font-size:1.072em;\">[Newsletter / Autoresponder ID] =&gt; [timestamp]</span></li></ul>";
        $GlobalFieldValue = 'Globalfield-Value';
        $IPAddress = '***********';
        $Parameters = array(
            'UserID' => $UserID,
            'ListInformation' => $ArraySubscriberList,
            'IPAddress' => $IPAddress,
            'SendConfirmationEmail' => false,
            'TriggerAutoResponders' => false,
            'OtherFields' => array(
                'CustomField' . $UserCustomFieldInformation['CustomFieldID'] => $UserFieldValue,
                'CustomField' . $GlobalCustomFieldInformation['CustomFieldID'] => $GlobalFieldValue,
            ),
        );

        // Double Optin - Pending
        $EmailAddress1 = '<EMAIL>';
        $Parameters['EmailAddress'] = $EmailAddress1;
        $Result = Subscribers::Subscribe($Parameters);
        $this->assertTrue($Result[0], $message . ' add subscriber 1');
        $SubscriberID1 = $Result[1];

        // Double Optin - Subscribed
        $EmailAddress2 = '<EMAIL>';
        $Parameters['EmailAddress'] = $EmailAddress2;
        $Parameters['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
        $Result = Subscribers::Subscribe($Parameters);
        $this->assertTrue($Result[0], $message . ' add subscriber 2');
        $SubscriberID2 = $Result[1];

        // Double Optin - Subscribed
        $EmailAddress6 = '<EMAIL>';
        $Parameters['EmailAddress'] = $EmailAddress6;
        $Parameters['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
        $Result = Subscribers::Subscribe($Parameters);
        $this->assertTrue($Result[0], $message . ' add subscriber 6');
        $SubscriberID6 = $Result[1];

        // create list 2
        $ArrayFieldAndValues = array(
            'Name' => 'List 2',
            'RelOwnerUserID' => $UserID,
            'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
        );
        $ListIDSOI = Lists::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($ListIDSOI > 0, $message . ' list 2');
        $ArraySubscriberList = Lists::RetrieveListByID($UserID, $ListIDSOI);
        $Parameters['ListInformation'] = $ArraySubscriberList;
        unset($Parameters['SubscriptionStatus']);

        // Single Optin - Subscribed
        $EmailAddress3 = '<EMAIL>';
        $Parameters['EmailAddress'] = $EmailAddress3;
        $Result = Subscribers::Subscribe($Parameters);
        $this->assertTrue($Result[0], $message . ' add subscriber 3');
        $SubscriberID3 = $Result[1];

        // Single Optin - Subscribed
        $EmailAddress4 = '<EMAIL>';
        $Parameters['EmailAddress'] = $EmailAddress4;
        $Result = Subscribers::Subscribe($Parameters);
        $this->assertTrue($Result[0], $message . ' add subscriber 4');
        $SubscriberID4 = $Result[1];

        // Single Optin - Subscribed
        $EmailAddress5 = '<EMAIL>';
        $Parameters['EmailAddress'] = $EmailAddress5;
        $Result = Subscribers::Subscribe($Parameters);
        $this->assertTrue($Result[0], $message . ' add subscriber 5');
        $SubscriberID5 = $Result[1];

        // SMS
        $EmailAddressSMS = '<EMAIL>';
        $SMSPhonenumber = '0049123456789';
        $Parameters['PhoneNumber'] = $SMSPhonenumber;
        $Parameters['EmailAddress'] = $EmailAddressSMS;
        $Result = Subscribers::Subscribe($Parameters);
        $this->assertTrue($Result[0], $message . ' add subscriber SMS');
        $SubscriberIDSMS = $Result[1];

        /*
         * LegacyApiSubscriber::index
         */
        $message = 'LegacyApiSubscriber::index';

        $result = LegacyApiSubscriber::index();
        $this->assertTrue(count($result) == 6, $message . ' count' . print_r($result, true));
        $this->assertTrue(current($result) == $SubscriberID2, $message . ' id');

        /*
         * LegacyApiSubscriber::retrieve
         */
        $message = 'LegacyApiSubscriber::retrieve';

        $result = LegacyApiSubscriber::retrieve($SubscriberIDSMS);
        $this->assertNotEmpty($result, $message . ' SMS result' . print_r($result, true));
        $this->assertTrue($result->id == $SubscriberIDSMS, $message . ' SMS id');
        $this->assertTrue($result->listid == $ListIDSOI, $message . ' SMS listid');
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $EmailAddressSMS), $message . ' SMS email');
        $this->assertTrue($result->bounce == 'Not Bounced', $message . ' SMS bounce');
        $this->assertTrue($result->status == 'Subscribed', $message . ' SMS status');
        $this->assertTrue(
            (new \DateTime($result->date))->getTimestamp() >=
            (new \DateTime())->setTimestamp(REQUEST_TIME)->setTime(0,0)->getTimestamp(),
            $message . ' SMS date'
        );
        $this->assertTrue($result->sms_phone == $SMSPhonenumber, $message . ' SMS sms_email');
        $this->assertTrue($result->sms_bounce == 'Not Bounced', $message . ' SMS sms_bounce');
        $this->assertTrue($result->sms_status == 'Subscribed', $message . ' SMS sms_status');
        $this->assertTrue(
            (new \DateTime($result->sms_date))->getTimestamp() >=
            (new \DateTime())->setTimestamp(REQUEST_TIME)->setTime(0,0)->getTimestamp(),
            $message . ' SMS sms_date'
        );

        // start: same retrieve with subscriber-key

        $messageKey = $message . ' by subscriber-key';

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $SubscriberIDSMS,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue(!empty($result), $messageKey . ' SMS result' . print_r($result, true));
        $this->assertTrue($result->id == $SubscriberIDSMS, $messageKey . ' SMS id');
        $this->assertTrue($result->listid == $ListIDSOI, $messageKey . ' SMS listid');
        $this->assertTrue(
            Subscribers::IsSameEmailAddress($result->email, $EmailAddressSMS),
            $messageKey . ' SMS email'
        );
        $this->assertTrue($result->bounce == 'Not Bounced', $messageKey . ' SMS bounce');
        $this->assertTrue($result->status == 'Subscribed', $messageKey . ' SMS status');
        $this->assertTrue(
            (new \DateTime($result->date))->getTimestamp() >=
            (new \DateTime())->setTimestamp(REQUEST_TIME)->setTime(0,0)->getTimestamp(),
            $message . ' SMS date'
        );
        $this->assertTrue($result->sms_phone == $SMSPhonenumber, $messageKey . ' SMS sms_email');
        $this->assertTrue($result->sms_bounce == 'Not Bounced', $messageKey . ' SMS sms_bounce');
        $this->assertTrue($result->sms_status == 'Subscribed', $messageKey . ' SMS sms_status');
        $this->assertTrue(
            (new \DateTime($result->sms_date))->getTimestamp() >=
            (new \DateTime())->setTimestamp(REQUEST_TIME)->setTime(0,0)->getTimestamp(),
            $message . ' SMS sms_date'
        );


        // end: retrieve with subscriber-key

        $result = LegacyApiSubscriber::retrieve($SubscriberID1);
        $this->assertTrue(!empty($result), $message . ' result' . print_r($result, true));
        $this->assertTrue($result->id == $SubscriberID1, $message . ' id');

        // start: same retrieve with subscriber-key

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $SubscriberID1,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue(!empty($result), $messageKey . ' result' . print_r($result, true));
        $this->assertTrue($result->id == $SubscriberID1, $messageKey . ' id');

        // end: retrieve with subscriber-key

        // check tagging before
        $result = LegacyApiSubscriber::retrieve($SubscriberID4);
        $this->assertTrue(!empty($result), $message . ' result' . print_r($result, true));
        $this->assertTrue($result->id == $SubscriberID4, $message . ' id');
        $this->assertEmpty($result->tags, $message . ' tags all before');
        $this->assertEmpty($result->manual_tags, $message . ' tags manual before');
        $this->assertEmpty($result->smart_links, $message . ' tags smart links before');
        $this->assertEmpty($result->smart_tags, $message . ' tags smart tags before');
        $this->assertEmpty($result->emails_sent, $message . ' tags sent before');
        $this->assertEmpty($result->emails_opened, $message . ' tags opened before');
        $this->assertEmpty($result->emails_clicked, $message . ' tags clicked before');
        $this->assertEmpty($result->emails_viewed, $message . ' tags viewed before');
        $this->assertEmpty($result->outbound, $message . ' tags outbound before');
        $this->assertEmpty($result->kajabi_activated, $message . ' tags kajabi_activated before');
        $this->assertEmpty($result->kajabi_deactivated, $message . ' tags kajabi_deactivated before');
        $this->assertEmpty($result->payment_activations, $message . ' tags payment_activations before');
        $this->assertEmpty($result->payment_expirations, $message . ' tags payment_expirations before');

        // start: same retrieve with subscriber-key

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $SubscriberID4,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue(!empty($result), $messageKey . ' result' . print_r($result, true));
        $this->assertTrue($result->id == $SubscriberID4, $message . ' id');
        $this->assertEmpty($result->tags, $messageKey . ' tags all before');
        $this->assertEmpty($result->manual_tags, $messageKey . ' tags manual before');
        $this->assertEmpty($result->smart_links, $messageKey . ' tags smart links before');
        $this->assertEmpty($result->smart_tags, $messageKey . ' tags smart tags before');
        $this->assertEmpty($result->emails_sent, $messageKey . ' tags sent before');
        $this->assertEmpty($result->emails_opened, $messageKey . ' tags opened before');
        $this->assertEmpty($result->emails_clicked, $messageKey . ' tags clicked before');
        $this->assertEmpty($result->emails_viewed, $messageKey . ' tags viewed before');
        $this->assertEmpty($result->outbound, $messageKey . ' tags outbound before');
        $this->assertEmpty($result->kajabi_activated, $messageKey . ' tags kajabi_activated before');
        $this->assertEmpty($result->kajabi_deactivated, $messageKey . ' tags kajabi_deactivated before');
        $this->assertEmpty($result->payment_activations, $messageKey . ' tags payment_activations before');
        $this->assertEmpty($result->payment_expirations, $messageKey . ' tags payment_expirations before');


        // end: retrieve with subscriber-key

        // tag
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $TagID1, $ReferenceID);
        $this->assertTrue($result, $message . ' tag Tag1');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $TagID2, $ReferenceID);
        $this->assertTrue($result, $message . ' tag Tag2');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_SENT,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_SENT,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4712
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_OPENED,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_CLICKED,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_VIEWED,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_CONVERTED,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_OUTBOUND,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_KAJABI_ACTIVATE,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_KAJABI_DEACTIVATE,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_PAYMENT_COMPLETED,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        $stid = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_PAYMENT_EXPIRED,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711
            /* fake */,
        ));
        $this->assertTrue($stid > 0, $message . ' create SmartTag');
        $result = Subscribers::TagSubscriber($UserID, $SubscriberID4, $stid, $ReferenceID);
        $this->assertTrue($result, $message . ' tag SmartTag');
        // check tagging after
        $result = LegacyApiSubscriber::retrieve($SubscriberID4);
        $this->assertTrue(!empty($result), $message . ' result' . print_r($result, true));
        $this->assertTrue($result->id == $SubscriberID4, $message . ' id');
        $this->assertTrue(count($result->tags) == 13, $message . ' tags all after');
        $this->assertTrue(count($result->manual_tags) == 2, $message . ' tags manual after');
        $this->assertEmpty($result->smart_links, $message . ' tags smart links after');
        $this->assertTrue(count($result->smart_tags) == 11, $message . ' tags smart tags after');
        $this->assertTrue(count($result->emails_sent) == 2, $message . ' tags sent after');
        $this->assertTrue(count($result->emails_opened) == 1, $message . ' tags opened after');
        $this->assertTrue(count($result->emails_clicked) == 1, $message . ' tags clicked after');
        $this->assertTrue(count($result->emails_viewed) == 1, $message . ' tags viewed after');
        $this->assertTrue(count($result->conversions) == 1, $message . ' tags conversions after');
        $this->assertTrue(count($result->outbound) == 1, $message . ' tags outbound after');
        $this->assertTrue(count($result->kajabi_activated) == 1, $message . ' tags kajabi_activated after');
        $this->assertTrue(count($result->kajabi_deactivated) == 1, $message . ' tags kajabi_deactivated after');
        $this->assertTrue(count($result->payment_activations) == 1, $message . ' tags payment_activations before');
        $this->assertTrue(count($result->payment_expirations) == 1, $message . ' tags payment_expirations before');

        // start: same retrieve with subscriber-key

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $SubscriberID4,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertNotEmpty($result, $messageKey . ' result' . print_r($result, true));
        $this->assertTrue($result->id == $SubscriberID4, $messageKey . ' id');
        $this->assertTrue(count($result->tags) == 13, $messageKey . ' tags all after');
        $this->assertTrue(count($result->manual_tags) == 2, $messageKey . ' tags manual after');
        $this->assertEmpty($result->smart_links, $messageKey . ' tags smart links after');
        $this->assertTrue(count($result->smart_tags) == 11, $messageKey . ' tags smart tags after');
        $this->assertTrue(count($result->emails_sent) == 2, $messageKey . ' tags sent after');
        $this->assertTrue(count($result->emails_opened) == 1, $messageKey . ' tags opened after');
        $this->assertTrue(count($result->emails_clicked) == 1, $messageKey . ' tags clicked after');
        $this->assertTrue(count($result->emails_viewed) == 1, $messageKey . ' tags viewed after');
        $this->assertTrue(count($result->conversions) == 1, $messageKey . ' tags conversions after');
        $this->assertTrue(count($result->outbound) == 1, $messageKey . ' tags outbound after');
        $this->assertTrue(count($result->kajabi_activated) == 1, $messageKey . ' tags kajabi_activated after');
        $this->assertTrue(count($result->kajabi_deactivated) == 1, $messageKey . ' tags kajabi_deactivated after');
        $this->assertTrue(count($result->payment_activations) == 1, $messageKey . ' tags payment_activations before');
        $this->assertTrue(count($result->payment_expirations) == 1, $messageKey . ' tags payment_expirations before');


        // end: retrieve with subscriber-key

        /*
         * LegacyApiSubscriber::create
         */
        $message = 'LegacyApiSubscriber::create';

        $before_count = get_confirmation_email_count();

        $email = '<EMAIL>';
        $listid = 0;
        $tagid = 0;
        $fieldid = 'field' . $UserCustomFieldID;
        $htmlfieldid = 'field' . $HTMLCustomFieldID;
        $fields = array(
            $fieldid => $UserFieldValue,
            $htmlfieldid => $HTMLFieldValue,
            'fieldFirstName' => $GlobalFieldValue,
        );
        $ArraySubscriberList = Lists::RetrieveListByIDOrDefault($user->uid, $listid);

        $result = LegacyApiSubscriber::create($email, '', $listid, $tagid, $fields);
        global $last_services_error;

        $this->assertTrue(
            !empty($result),
            $message . ' result 1' . print_r(array(
                $last_services_error,
                $ArraySubscriberList,
            ), true)
        );
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $email), $message . ' email');
        $this->assertTrue($result->$fieldid == $UserFieldValue, $message . ' field');
        $this->assertTrue($result->$htmlfieldid == $HTMLFieldValue, $message . ' html field');
        $this->assertTrue($result->status == 'Opt-In Pending', $message . ' status');
        check_confirmation_email_count($this, $message, $before_count + 1);

        $email = '<EMAIL>';
        $listid = $ListID1;
        $tagid = $TagID1;
        $fields = array();
        $result = LegacyApiSubscriber::create($email, '', $listid, $tagid, $fields);
        $this->assertTrue(!empty($result), $message . ' result 2' . print_r($result, true));
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $email), $message . ' email');
        check_confirmation_email_count($this, $message, $before_count + 2);

        $email = '<EMAIL>';
        $listid = $ListIDSOI;
        $tagid = $TagID1;
        $fields = array();
        $result = LegacyApiSubscriber::create($email, '', $listid, $tagid, $fields);
        $this->assertTrue(!empty($result), $message . ' result 3' . print_r($result, true));
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $email), $message . ' email');
        $this->assertTrue($result->status == 'Subscribed', $message . ' status');
        check_confirmation_email_count($this, $message, $before_count + 2);

        // --- Connector Test

        $ApiDefaultList = Lists::RetrieveDefaultList($customer->uid);
        $ApiDefaultListID = $ApiDefaultList['ListID'];

        $ApiTagID = Tag::CreateManualTag($customer->uid, 'API-Tag', '', '');

        $APIFields = array(
            'fieldFirstName' => 'First name',
            'fieldLastName' => 'Last name',
        );

        //--- TestCase: $connector->subscribe
        // TODO: Re-enable tests when Symfony works with $connector.
//
//        $message = '$connector->subscribe: ';
//
//        $email = '<EMAIL>';
//        $subscriber = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields);
//        $this->assertTrue(!empty($subscriber), "$message $email -> {$connector->get_last_error()}".print_r($subscriber,1));
//        check_confirmation_email_count($this, $message, $before_count + 3);
//
//        // same call again, should change nothing
//        $subscriber = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields);
//        $this->assertTrue(!empty($subscriber), "$message $email -> {$connector->get_last_error()}".print_r($subscriber,1));
//        check_confirmation_email_count($this, $message, $before_count + 3);
//
//        $email = '<EMAIL>';
//        $smsnumber = '004915209000001';
//        $subscriber = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields, $smsnumber);
//        $this->assertTrue(!empty($subscriber), "$message $email + $smsnumber -> {$connector->get_last_error()}".print_r($subscriber,1));
//        $this->assertTrue($subscriber->sms_phone == $smsnumber, "$message smsnumber $smsnumber set");
//        check_confirmation_email_count($this, $message, $before_count + 4);
//
//        $email = '';
//        $smsnumber = '004915209000002';
//        $subscriber = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields, $smsnumber);
//        $this->assertTrue(!empty($subscriber), "$message smsnumber_only $smsnumber -> {$connector->get_last_error()}".print_r($subscriber,1));
//        $this->assertTrue($subscriber->sms_phone == $smsnumber, "$message smsnumber $smsnumber set");
//        $this->assertTrue(empty($subscriber->email), "$message no email address subscription");
//        check_confirmation_email_count($this, $message, $before_count + 4);
//
//        $email = '<EMAIL>';
//        $smsnumber = 'invalid';
//        $subscriber = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields, $smsnumber);
//        $this->assertTrue(empty($subscriber), "$message $email -> invalid sms number - {$connector->get_last_error()}".print_r($subscriber,1));
//        $this->assertTrue($subscriber->sms_phone == '', "$message invalid smsnumber not set");
//        check_confirmation_email_count($this, $message, $before_count + 4);
//
//        $email = '<EMAIL>';
//        $smsnumber = '004915209000001';
//        $subscriber = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields, $smsnumber);
//        $this->assertTrue(!empty($subscriber), "$message re-subscribe $email + $smsnumber -> {$connector->get_last_error()}".print_r($subscriber,1));
//        $this->assertTrue($subscriber->sms_phone == $smsnumber, "$message smsnumber $smsnumber set");
//        check_confirmation_email_count($this, $message, $before_count + 4);
//
//        $email = '<EMAIL>';
//        $smsnumber = '004915209000002';
//        $subscriber = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields, $smsnumber);
//        $this->assertTrue(!empty($subscriber), "$message update dummy email by subscribe $email + $smsnumber -> {$connector->get_last_error()}".print_r($subscriber,1));
//        $this->assertTrue(Subscribers::IsSameEmailAddress($subscriber->email, $email), "$message dummy email for $smsnumber updated to $email");
//        check_confirmation_email_count($this, $message, $before_count + 5);
//
//        $email = '';
//        $smsnumber = '';
//        $subscriber = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields, $smsnumber);
//        $this->assertTrue(empty($subscriber), "$message no email, no smsnumber -> {$connector->get_last_error()}".print_r($subscriber,1));
//        check_confirmation_email_count($this, $message, $before_count + 5);
//
//        // like in content_includes/webinar/klicktipp.api.inc "tag" function (which is a subscribe)
//        $email = '<EMAIL>';
//        $data = array(
//            'email' => $email,
//            'tagid' => $ApiTagID,
//        );
//        $response = $connector->_http_request('/subscriber', 'POST', $data);
//        $this->assertTrue(!empty($response->data), "$message ".print_r($response,1));
//        $subscriber = $response->data;
//        $this->assertTrue(!empty($subscriber), "$message reduced data -> {$connector->get_last_error()}".print_r($subscriber,1));
//        $this->assertTrue(Subscribers::IsSameEmailAddress($subscriber->email, $email), "$message subscribed to $email");
//        $this->assertTrue(in_array($ApiTagID, $subscriber->tags), "$message tagged $ApiTagID");
//        check_confirmation_email_count($this, $message, $before_count + 6);

        /*
         * LegacyApiSubscriber::update
         */
        $message = 'LegacyApiSubscriber::update';

        $before_count = get_confirmation_email_count();

        $GlobalFieldValueNew = 'new firstname';
        $fields = array(
            'fieldFirstName' => $GlobalFieldValueNew,
        );

        $subscriberid = $SubscriberID3;
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $this->assertTrue($result->fieldFirstName != $GlobalFieldValueNew, $message . ' firstname before');
        $email = $result->email;

        // start: same retrieve with subscriber-key

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue($result->fieldFirstName != $GlobalFieldValueNew, $messageKey . ' firstname before');
        // end: retrieve with subscriber-key

        $newemail = '';
        $result = LegacyApiSubscriber::update($subscriberid, $fields, $newemail);
        $this->assertTrue($result, $message . ' result');
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $this->assertTrue(
            Subscribers::IsSameEmailAddress($result->email, $email),
            $message . ' email' . print_r($result, true)
        );
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $message . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->status == 'Subscribed', $message . ' status');
        check_confirmation_email_count($this, $message, $before_count);

        // start: same retrieve with subscriber-key

        $messageKey = $message . ' by subscriber-key';

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue(
            Subscribers::IsSameEmailAddress($result->email, $email),
            $messageKey . ' email' . print_r($result, true)
        );
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $messageKey . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->status == 'Subscribed', $messageKey . ' status');

        // end: retrieve with subscriber-key

        // change email of Double Optin
        $message = 'LegacyApiSubscriber::update DO';

        $subscriberid = $SubscriberID2;
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $this->assertTrue($result->status == 'Subscribed', $message . ' status before');

        // start: same retrieve with subscriber-key

        $messageKey = $message . ' by subscriber-key';

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue($result->status == 'Subscribed', $messageKey . ' status before');

        // end: retrieve with subscriber-key

        $newemail = '<EMAIL>';
        $result = LegacyApiSubscriber::update($subscriberid, $fields, $newemail);
        $this->assertTrue($result, $message . ' result');
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $newemail), $message . ' email');
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $message . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->status == 'Opt-In Pending', $message . ' status');
        check_confirmation_email_count($this, $message, $before_count + 1);

        // start: same retrieve with subscriber-key

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $newemail), $messageKey . ' email');
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $messageKey . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->status == 'Opt-In Pending', $messageKey . ' status');

        // end: retrieve with subscriber-key

        // change email of Single Optin
        $message = 'LegacyApiSubscriber::update SO';

        $subscriberid = $SubscriberID4;
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $this->assertTrue($result->status == 'Subscribed', $message . ' status before');

        // start: same retrieve with subscriber-key

        $messageKey = $message . ' by subscriber-key';

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue($result->status == 'Subscribed', $messageKey . ' status before');

        // end: retrieve with subscriber-key

        $newemail = '<EMAIL>';
        $result = LegacyApiSubscriber::update($subscriberid, $fields, $newemail);
        $this->assertTrue($result, $message . ' result');
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $newemail), $message . ' email');
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $message . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->status == 'Subscribed', $message . ' status');
        check_confirmation_email_count($this, $message, $before_count + 1);

        // start: same retrieve with subscriber-key

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $newemail), $messageKey . ' email');
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $messageKey . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->status == 'Subscribed', $messageKey . ' status');

        // end: retrieve with subscriber-key

        // change email of Double Optin with ChangeEmailList = SOI
        $message = 'LegacyApiSubscriber::update DOI w/ ChangeEmailList SOI';

        // mark list2 (SOI) as ChangeEmailList, see klicktipp_list_edit_form_submit
        $UserLists = Lists::RetrieveLists($UserID);
        if (!empty($UserLists) && is_array($UserLists)) {
            foreach ($UserLists as $ArrayList) {
                if (!empty($ArrayList['UseAsChangeEmailList'])) {
                    $ArrayList['UseAsChangeEmailList'] = 0;
                    Lists::UpdateDB($ArrayList);
                }
            }
        }
        $ArrayList = Lists::RetrieveListByID($UserID, $ListIDSOI);
        $ArrayList['UseAsChangeEmailList'] = 1;
        Lists::UpdateDB($ArrayList);

        $subscriberid = $SubscriberID6;
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $this->assertTrue($result->status == 'Subscribed', $message . ' status before');
        $this->assertTrue($result->listid == $ListID1, $message . ' listid');

        // start: same retrieve with subscriber-key

        $messageKey = $message . ' by subscriber-key';

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue($result->status == 'Subscribed', $messageKey . ' status before');
        $this->assertTrue($result->listid == $ListID1, $messageKey . ' listid');

        // end: retrieve with subscriber-key

        $newemail = '<EMAIL>';
        $result = LegacyApiSubscriber::update($subscriberid, $fields, $newemail);
        $this->assertTrue($result, $message . ' result');
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $newemail), $message . ' email');
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $message . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->status == 'Subscribed', $message . ' status');
        check_confirmation_email_count($this, $message, $before_count + 1);

        // start: same retrieve with subscriber-key

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $newemail), $messageKey . ' email');
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $messageKey . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->status == 'Subscribed', $messageKey . ' status');

        // end: retrieve with subscriber-key

        // change sms
        $message = 'LegacyApiSubscriber::update SMS';

        $subscriberid = $SubscriberIDSMS;
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $email = $result->email;
        $this->assertTrue($result->status == 'Subscribed', $message . ' status before');
        $this->assertTrue($result->sms_status == 'Subscribed', $message . ' sms_status before');
        $this->assertTrue($result->sms_phone == $SMSPhonenumber, $message . ' phonenumber before');

        // start: same retrieve with subscriber-key

        $messageKey = $message . ' by subscriber-key';

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue($result->status == 'Subscribed', $messageKey . ' status before');
        $this->assertTrue($result->sms_status == 'Subscribed', $messageKey . ' sms_status before');
        $this->assertTrue($result->sms_phone == $SMSPhonenumber, $messageKey . ' phonenumber before');
        $this->assertTrue($result->email == $email, $messageKey . ' mail before');

        // end: retrieve with subscriber-key

        $newsmsnumber = '+149111222333444';
        $result = LegacyApiSubscriber::update($subscriberid, $fields, '', $newsmsnumber);
        $this->assertTrue($result, $message . ' result');
        $result = LegacyApiSubscriber::retrieve($subscriberid);
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $email), $message . ' email');
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $message . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->sms_phone == $newsmsnumber, $message . ' phonenumber ' . print_r($result, true));
        $this->assertTrue($result->sms_status == 'Subscribed', $message . ' status');
        check_confirmation_email_count($this, $message, $before_count + 1);

        // start: same retrieve with subscriber-key

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $subscriberid,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $email), $messageKey . ' email');
        $this->assertTrue(
            $result->fieldFirstName == $GlobalFieldValueNew,
            $messageKey . ' field' . print_r($result, true)
        );
        $this->assertTrue($result->sms_phone == $newsmsnumber, $messageKey . ' phonenumber ' . print_r($result, true));
        $this->assertTrue($result->sms_status == 'Subscribed', $messageKey . ' status');

        // end: retrieve with subscriber-key

        // --- Connector Test

        //--- TestCase: $connector->subscriber_update
        // TODO: Re-enable tests when Symfony works with $connector.
//
//        $message = '$connector->subscriber_update: ';
//
//        $email = '<EMAIL>';
//        $subscriber = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields);
//        $this->assertTrue(!empty($subscriber), "$message subscribe to update -> {$connector->get_last_error()}".print_r($subscriber,1));
//        check_confirmation_email_count($this, $message, $before_count + 2);
//
//        $new_email_address = '<EMAIL>';
//        $connector->subscriber_update($subscriber->id, array(), $new_email_address);
//        $updated_subscriber = $connector->subscriber_get($subscriber->id);
//        $this->assertTrue($new_email_address == $updated_subscriber->email , "$message update email {$subscriber->email} -> {$updated_subscriber->email} ({$connector->get_last_error()})");
//        check_confirmation_email_count($this, $message, $before_count + 3);
//
//        // doing the same call should change nothing
//        $connector->subscriber_update($subscriber->id, array(), $new_email_address);
//        $updated_subscriber = $connector->subscriber_get($subscriber->id);
//        $this->assertTrue($new_email_address == $updated_subscriber->email , "$message update email {$subscriber->email} -> {$updated_subscriber->email} ({$connector->get_last_error()})");
//        check_confirmation_email_count($this, $message, $before_count + 3);
//
//        $smsnumber = '004915207000001';
//        $connector->subscriber_update($subscriber->id, array(), '', $smsnumber);
//        $subscriber = $connector->subscriber_get($subscriber->id);
//        $this->assertTrue($subscriber->sms_phone == $smsnumber, "$message smsnumber {$subscriber->sms_phone} set by subscriber_update ({$connector->get_last_error()})");
//        check_confirmation_email_count($this, $message, $before_count + 3);
//
//        $newsmsnumber = '004915207000002';
//        $connector->subscriber_update($subscriber->id, array(), '', $newsmsnumber);
//        $subscriber = $connector->subscriber_get($subscriber->id);
//        $this->assertTrue($subscriber->sms_phone == $newsmsnumber, "$message smsnumber {$subscriber->sms_phone} updated ({$connector->get_last_error()})".print_r($subscriber,1));
//        check_confirmation_email_count($this, $message, $before_count + 3);
//
//        $email = '<EMAIL>';
//        $existing_smsnumber = '004915205555555';
//        $existing = $connector->subscribe($email, $ApiDefaultListID, $ApiTagID, $APIFields, $existing_smsnumber);
//        $this->assertTrue($existing->sms_phone == $existing_smsnumber, "$message smsnumber {$existing->sms_phone} updated ({$connector->get_last_error()})".print_r($existing,1));
//        check_confirmation_email_count($this, $message, $before_count + 4);
//
//        //update with existing number
//        $failed = $connector->subscriber_update($subscriber->id, array(), '', $existing_smsnumber);
//        $this->assertFalse($failed, "$message update with existing number $existing_smsnumber failed ({$connector->get_last_error()})");
//        $subscriber = $connector->subscriber_get($subscriber->id);
//        $this->assertTrue($subscriber->sms_phone == $newsmsnumber, "$message smsnumber {$subscriber->sms_phone} unchanged ({$connector->get_last_error()})".print_r($subscriber,1));
//        check_confirmation_email_count($this, $message, $before_count + 4);

        /*
         * LegacyApiSubscriber::delete
         */
        $message = 'LegacyApiSubscriber::delete';

        $result = LegacyApiSubscriber::delete($SubscriberID1);
        $this->assertTrue($result, $message);
        $message = null;
        try {
            LegacyApiSubscriber::retrieve($SubscriberID1);
        } catch (ServicesException $e) {
            $message = $e->getMessage();
        }
        $this->assertEquals('There is no such subscriber.', $message);

        // start: same retrieve with subscriber-key

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $SubscriberID1,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $message = null;
        try {
            LegacyApiSubscriber::retrieve($subscriberKey);
        } catch (ServicesException $e) {
            $message = $e->getMessage();
        }
        $this->assertEquals('There is no such subscriber.', $message);

        // end: retrieve with subscriber-key

        /*
         * LegacyApiSubscriber::unsubscribe
         */
        $message = 'LegacyApiSubscriber::unsubscribe';

        $result = LegacyApiSubscriber::unsubscribe($EmailAddress3);
        $this->assertTrue($result, $message);
        $result = LegacyApiSubscriber::retrieve($SubscriberID3);
        $this->assertTrue($result->status == 'Unsubscribed', $message . ' status' . print_r($result, true));

        // start: same retrieve with subscriber-key

        $messageKey = $message . ' by subscriber-key';

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $SubscriberID3,
            'ReferenceID' => 0
        ], 'subscriber_key');
        $result = LegacyApiSubscriber::retrieve($subscriberKey);
        $this->assertTrue($result->status == 'Unsubscribed', $messageKey . ' status' . print_r($result, true));

        // end: retrieve with subscriber-key

        /*
         * LegacyApiSubscriber::search
         */
        $message = 'LegacyApiSubscriber::search';

        $result = LegacyApiSubscriber::search($EmailAddress5);
        $this->assertTrue($result == $SubscriberID5, $message . ' id');

        /*
         * LegacyApiSubscriber::tag
         */
        $message = 'LegacyApiSubscriber::tag';

        $EmailAddressTagging = '<EMAIL>';
        $result = LegacyApiSubscriber::create($EmailAddressTagging, '', $ListIDSOI);
        $this->assertTrue(!empty($result), $message . ' subscribed' . print_r($result, true));
        $SubscriberIDTagging = $result->id;
        $TagIDOnlyOne = Tag::CreateManualTag($UserID, 'only one subscriber', '');
        $this->assertTrue($TagIDOnlyOne > 0, $message . ' tag only one');

        $result = LegacyApiSubscriber::tag($EmailAddressTagging, array($TagID1, $TagIDOnlyOne));
        $this->assertTrue($result, $message . ' tagged');

        $result = LegacyApiSubscriber::retrieve($SubscriberIDTagging);
        $this->assertTrue(count($result->manual_tags) == 2, $message . ' tags manual after' . print_r($result, true));
        $ts = $result->manual_tags[$TagID1];

        // start: same retrieve with subscriber-key

        $messageKey = $message . ' by subscriber-key';

        $subscriberKey = Core::EncryptURL([
            'UserID' => $UserID,
            'SubscriberID' => $SubscriberIDTagging,
            'ReferenceID' => 0
        ], 'subscriber_key');

        // end: retrieve with subscriber-key

        /*
         * LegacyApiSubscriber::tagged
         */
        $message = 'LegacyApiSubscriber::tagged';

        $result = LegacyApiSubscriber::tagged($TagID1);
        $this->assertTrue(count($result) > 1, $message . ' count tagged with');
        $this->assertTrue(
            in_array($SubscriberIDTagging, array_keys($result)),
            $message . ' subscriber is tagged' . print_r($result, true)
        );

        $result = LegacyApiSubscriber::tagged($TagIDOnlyOne);
        $this->assertTrue(count($result) == 1, $message . ' count tagged with (only one)');
        $this->assertTrue(
            in_array($SubscriberIDTagging, array_keys($result)),
            $message . ' subscriber is tagged (only one)'
        );
        $this->assertTrue($result[$SubscriberIDTagging] >= $ts, $message . ' timestamp');

        /*
         * LegacyApiSubscriber::untag
         */
        $message = 'LegacyApiSubscriber::untag';

        $result = LegacyApiSubscriber::untag($EmailAddressTagging, $TagIDOnlyOne);
        $this->assertTrue($result, $message . ' result');
        $result = LegacyApiSubscriber::tagged($TagIDOnlyOne);
        $this->assertTrue(empty($result), $message . ' count tagged with');

        // create smarttag to check permissions
        $Parameters = array(
            'Category' => Tag::CATEGORY_SENT,
            'RelOwnerUserID' => $UserID,
            'EntityID' => 4711, // fake
        );
        $SmartTagID = Tag::CreateTag($Parameters);
        $this->assertTrue($SmartTagID > 0, $message . ' SmartTagID');
        $message = null;
        try {
            LegacyApiSubscriber::untag($EmailAddressTagging, $SmartTagID);
        } catch (ServicesException $e) {
            $message = $e->getMessage();
        }
        $this->assertEquals('untagging failed', $message);

        // create test ar
        LegacyApiSubscriber::tag($EmailAddressTagging, array($TagIDOnlyOne));
        _testdata_campaigns($UserID, 1, 'test', true /* AR */, $TagIDOnlyOne, true, false, true /* $dosend  */);
        $QueueEntries = simpletest_transactional_retrieve_emails(
            ['*'],
            [
                'RelSubscriberID' => $SubscriberIDTagging
            ],
            AutoresponderQueue::TABLE_NAME
        );
        $this->assertTrue(count($QueueEntries) == 1, 'only one queue entry' . print_r($QueueEntries, true));
        $QueueEntry = simpletest_transactional_retrieve_email(
            ['RelSubscriberID' => $SubscriberIDTagging],
            AutoresponderQueue::TABLE_NAME
        );
        $this->assertEquals(TransactionEmails::STATUS_SENT, $QueueEntry['StatusEnum']);
    }
}

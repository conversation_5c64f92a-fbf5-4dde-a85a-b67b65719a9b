<?php

namespace App\Tests\Integration\Klicktipp;

use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Dates;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\EmailsConfirmationEmail;
use App\Klicktipp\EmailsNewsletterEmail;
use App\Klicktipp\EmailsNewsletterSMS;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\Personalization;
use App\Klicktipp\Settings;
use App\Klicktipp\Signatures;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\TestData\TestUser;
use App\Klicktipp\ToolCountdown;
use App\Tests\Integration\Helpers\LanguageAware;
use Doctrine\DBAL\Connection;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

require_once 'dns_shell_exec.php';

class PersonalizationTest extends KernelTestCase
{
    use LanguageAware;

    /**
     * @var Connection
     */
    private $connection;

    protected function setUp(): void
    {
        $GLOBALS['app'] = self::bootKernel();
        $this->connection = $this->getContainer()->get('database_connection');
        $this->setUpTestLanguages(['en', 'pt-br']);

        // Mock _dmarc-Check by DnsClient
        global $shellExecMock;
        $shellExecMock = static function (string $command) {
            if (preg_match('/dig @.* _dmarc.(.*) TXT/', $command, $matches)) {
                $domain = $matches[1];
                return ";; ANSWER SECTION:\n_dmarc.$domain.com. 3600	IN	TXT	\"v=DMARC1; p=node;\"\n";
            }

            return '';
        };
    }

    public function tearDown(): void
    {
        $this->connection = null;
    }

    public function testDrupalSimpleTest()
    {
        $testUser = new TestUser($this->connection);
        $testUser->getEnterpriseUser();

        // disbale randomness to get comparable resultsp
        MinihashCreate(1, '', true);

        Libraries::include('send_engine.inc', '/tests/includes');

        $message = 'Personalization';

        $ReferenceID = 0;

        // create some special test data
        $languages = language_list('enabled');
        $languages = $languages[1];

        $this->assertTrue(isset($languages['pt-br']), $message . ' pt-br exists');

        $transen = 'Germany';
        $transptbr = 'Alemanha';

        // insert translations into database
        require_once DRUPAL_ROOT . '/includes/locale.inc';
        $report = array();
        _locale_import_one_string_db(
            $report,
            'pt-br',
            '',
            $transen,
            $transptbr,
            'default',
            '',
            LOCALE_IMPORT_OVERWRITE,
            0,
            0
        );
        variable_set(
            'locale_cache_strings',
            0
        ); // disable trans caching, so we get the written translationsMinihashCreate(1, '', TRUE);

        $Domain = 'endlich-ohne-schufa.info';
        $Alias = 'news4711';
        $UserID = prepareWhitelabelDomainUser(
            true,
            true,
            true,
            $Domain,
            $Alias
        ); // this will create a valid sender domain
        $account = user_load($UserID);
        $ArrayUser = (array)$account;
        $SenderDomains = DomainSet::GetValidSenderDomains($ArrayUser);
        $SenderDomain = DomainSet::SelectSenderDomain($UserID, $SenderDomains);
        $WLDomainParam = 'ZZZ%Link:Application%ZZZ';
        $WLDomainReplacement = "ZZZhttps://{$Alias}.{$Domain}ZZZ"; // this is $SenderDomain['appurl'] == http://news4711.endlich-ohne-schufa.info

        $affid = 4711;
        user_save($account, array(
            'amemberid' => $affid,
            'Country' => 'DE',
            'language' => 'pt-br'
        ));
        $ArrayUser = (array)user_load($UserID);
        $this->assertTrue($ArrayUser['amemberid'] == $affid, $message . ' affilateid');
        $AffiliateURL = variable_get('amember_affiliate_url', 'http://www.klick-tipp.com/') . $affid;

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => "url field",
            'FieldTypeEnum' => CustomFields::TYPE_URL,
        );
        $URLCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(!empty($URLCustomFieldID), $message . ' custom field url');
        $URLCustomFieldParameter = "%Subscriber:CustomField$URLCustomFieldID%";
        $URLCustomFieldValue = "http://url.zauberlist.com";

        $WebsiteCustomFieldParameter = "%Subscriber:CustomFieldWebsite%";

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => "html field",
            'FieldTypeEnum' => CustomFields::TYPE_HTML,
        );
        $HTMLCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(!empty($HTMLCustomFieldID), $message . ' custom field html');
        $HTMLCustomFieldParameter = "%Subscriber:CustomField$HTMLCustomFieldID%";
        $HTMLCustomFieldValue = "<ul style=\"margin-top:1em; margin-bottom:1.5em;\"><li style=\"margin-bottom:0.3em;\"><span style=\"font-family:'Courier New';color:#142D68; font-size:1.072em;\">manual_tags</span></li></ul>";

        $NONexistingCustomFieldParameter = "%Subscriber:CustomField99999%";

        $AffiliateParam = '%User:AffiliateURL%';

        $Param2 = '%Subscriber:SubscriberID%';

        $ConfirmationPlainLink = 'http://www.example.com/confirmationplainlink';
        $ConfirmationPlainLinkWP1 = "http://www.example.com/confirmationplainlink?url=$URLCustomFieldParameter&param2=$Param2";
        $ConfirmationPlainLinkWP2 = "http://www.example.com/confirmationplainlink/$URLCustomFieldParameter/$Param2";
        $ConfirmationPlainLinkWP3 = "http://www.example.com/confirmationplainlink?url=$WebsiteCustomFieldParameter&param2=$Param2";
        $ConfirmationPlainLinkWP4 = "http://www.example.com/confirmationplainlink/$WebsiteCustomFieldParameter/$Param2";

        $CampaignPlainLink = 'http://www.example.com/campaignplainlink';
        $CampaignPlainLinkWP1 = "http://www.example.com/campaignplainlink?url=$URLCustomFieldParameter&param2=$Param2";
        $CampaignPlainLinkWP2 = "http://www.example.com/campaignplainlink/$URLCustomFieldParameter/$Param2";
        $CampaignPlainLinkWP3 = "http://www.example.com/campaignplainlink?url=$WebsiteCustomFieldParameter&param2=$Param2";
        $CampaignPlainLinkWP4 = "http://www.example.com/campaignplainlink/$WebsiteCustomFieldParameter/$Param2";

        $CampaignHTMLLink = 'http://www.example.com/campaignHTMLlink';
        $CampaignHTMLLinkWP1 = "http://www.example.com/campaignHTMLlink?url=$URLCustomFieldParameter&param2=$Param2";
        $CampaignHTMLLinkWP2 = "http://www.example.com/campaignHTMLlink/$URLCustomFieldParameter/$Param2";
        $CampaignHTMLLinkWP3 = "http://www.example.com/campaignHTMLlink?url=$WebsiteCustomFieldParameter&param2=$Param2";
        $CampaignHTMLLinkWP4 = "http://www.example.com/campaignHTMLlink/$WebsiteCustomFieldParameter/$Param2";

        $LinkNoTrackPlain = 'http://www.zauberlist.com/plain';
        $LinkNoTrackHTML = 'http://www.zauberlist.com/html';

        $PercentText = ' i am 100% sure that a % sign does not affect email params, even if 3 %-signes are in text ';

        // create signature
        $HTMLSignatureText = 'Signature Test HTML';
        $PlainSignatureText = 'Signature Test Plain';
        $SignatureHTMLLink = 'http://www.example.com/signaturehtmllink';
        $SignatureHTMLLinkWP = 'http://www.example.com/signaturehtmllink?' . $Param2;
        $SignaturePlainLink = 'http://www.example.com/signatureplainlink';
        $SignaturePlainLinkWP = 'http://www.example.com/signatureplainlink?' . $Param2;
        $Signature = array(
            'RelOwnerUserID' => $UserID,
            'SignatureName' => "Signature " . $message,
            'HTMLSignatureText' => $HTMLSignatureText .
                '<a href="' . $SignatureHTMLLink . '">signaturelink</a>' .
                '<a href="' . $SignatureHTMLLinkWP . '">signaturelink with param</a>' .
                '<a href="' . $AffiliateParam . '">affiliatelink</a>',
            'PlainSignatureText' => $PlainSignatureText . ' ' .
                $SignaturePlainLink . ' ' .
                $SignaturePlainLinkWP . ' ' .
                $AffiliateParam,
        );
        $SignatureID = Signatures::InsertDB($Signature);
        $RevisionID = Signatures::CreateRevision($UserID, $SignatureID);
        $this->assertTrue($SignatureID > 0, $message . ' create signature');
        $Signature = Signatures::RetrieveSignatureByID($UserID, $SignatureID);
        $this->assertTrue(!empty($Signature), $message . ' signature');
        $Revision = Signatures::RetrieveSignatureByRevisionID($RevisionID);
        $this->assertTrue(!empty($Revision), $message . ' revision');
        $ArraySignature = array_merge($Signature, $Revision);
        $this->assertTrue(!empty($ArraySignature), $message . ' get signature' . print_r($ArraySignature, 1));
        $ArrayContent = unserialize($ArraySignature['PlainSignature']);
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $SignaturePlainLink),
            "$message revision $SignaturePlainLink"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $SignaturePlainLinkWP),
            "$message revision $SignaturePlainLinkWP"
        );
        $ArrayContent = unserialize($ArraySignature['HTMLSignature']);
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $SignatureHTMLLink),
            "$message revision $SignatureHTMLLink"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $SignatureHTMLLinkWP),
            "$message revision $SignatureHTMLLinkWP"
        );

        $ExpectedReferrer = 'http://www.example.com';
        $ArraySubscription = array(
            'SubscriberID' => 1,
            'RelOwnerUserID' => $UserID,
            'EmailAddress' => '<EMAIL>',
            'BounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SubscriptionDate' => strtotime('-5 days'),
            'LastOpenDate' => strtotime('-1 days'),
            'SubscriptionIP' => '127.0.0.1',
            'SubscriptionReferrer' => "{$ExpectedReferrer}äöüß\nÄÖÜ",
            'UnsubscriptionDate' => 0,
            'UnsubscriptionIP' => '127.0.0.1',
            'OptInDate' => strtotime('-10 days'),
            'OptInIP' => '127.0.0.1',
        );
        $ArraySubscription["CustomField$URLCustomFieldID"] = $URLCustomFieldValue;
        $ArraySubscription["CustomField$HTMLCustomFieldID"] = $HTMLCustomFieldValue;

        /*
         * GetSubscriberPersonalizationTags
         */
        //TODO

        /*
         * GetPersonalizationLinkTags
         */
        //TODO

        /*
         * GetPersonalizationUserTags
         */
        //TODO

        /*
         * GetTagsFor
         */
        $Mode = 'signature';
        $message = 'Personalization::GetTagsFor ' . $Mode;

        $array_content_tags = Personalization::GetTagsFor($Mode, $account);
        $array_content_tags = array_merge(
            $array_content_tags,
            array('User information' => Personalization::GetPersonalizationUserTags(true))
        ); //include the %User:...% tags to init replacements for this test
        $this->assertTrue(
            count($array_content_tags) > 0,
            $message . ' is array content ' . print_r($array_content_tags, 1)
        );
        $SignatureAllParams = 'SignatureParams:';
        foreach ($array_content_tags as $keyarray) {
            $SignatureAllParams .= implode("\n", array_keys($keyarray));
        }
        $this->assertTrue(strpos($SignatureAllParams, '%Subscriber:SubscriberID%') > 0, $message . ' SubscriberID');
        $this->assertTrue(strpos($SignatureAllParams, '%Subscriber:BounceType%') === false, $message . ' BounceType');
        $this->assertTrue(strpos($SignatureAllParams, '%Subscriber:EmailAddress%') > 0, $message . ' EmailAddress');
        $this->assertTrue(
            strpos($SignatureAllParams, '%Subscriber:SubscriptionDate%') > 0,
            $message . ' SubscriptionDate'
        );
        $this->assertTrue(strpos($SignatureAllParams, '%Subscriber:SubscriptionIP%') > 0, $message . ' SubscriptionIP');
        $this->assertTrue(strpos($SignatureAllParams, '%Subscriber:OptInDate%') > 0, $message . ' OptInDate');
        $this->assertTrue(
            strpos($SignatureAllParams, '%Subscriber:CustomFieldFirstName%') > 0,
            $message . ' CustomFieldFirstName'
        );
        $this->assertTrue(strpos($SignatureAllParams, '%Link:Confirm%') === false, $message . ' %Link:Confirm%');
        $this->assertTrue(strpos($SignatureAllParams, '%Link:WebBrowser%') > 0, $message . ' %Link:WebBrowser%');
        $this->assertTrue(strpos($SignatureAllParams, '%Link:Unsubscribe%') > 0, $message . ' %Link:Unsubscribe%');
        $this->assertTrue(strpos($SignatureAllParams, '%Link:NoTrack(URL)%') > 0, $message . ' %Link:NoTrack(URL)%');
        $this->assertTrue(
            strpos($SignatureAllParams, '%Link:ChangeEmailAddress%') > 0,
            $message . ' %Link:ChangeEmailAddress%'
        );
        $this->assertTrue(strpos($SignatureAllParams, '%User:AffiliateURL%') > 0, $message . ' %User:AffiliateURL%');
        $this->assertTrue(strpos($SignatureAllParams, '%User:Country%') > 0, $message . ' %User:Country%');

        $Mode = 'confirmation';
        $message = 'Personalization::GetTagsFor ' . $Mode;
        $array_content_tags = Personalization::GetTagsFor($Mode, $account);
        $array_content_tags = array_merge(
            $array_content_tags,
            array('User information' => Personalization::GetPersonalizationUserTags(true))
        ); //include the %User:...% tags to init replacements for this test
        $this->assertTrue(
            count($array_content_tags) > 0,
            $message . ' is array content ' . print_r($array_content_tags, 1)
        );
        $ConfirmationAllParams = 'ConfirmationParams:';
        foreach ($array_content_tags as $keyarray) {
            $ConfirmationAllParams .= implode("\n", array_keys($keyarray));
        }
        $this->assertTrue(strpos($ConfirmationAllParams, '%Subscriber:SubscriberID%') > 0, $message . ' SubscriberID');
        $this->assertTrue(
            strpos($ConfirmationAllParams, '%Subscriber:BounceType%') === false,
            $message . ' BounceType'
        );
        $this->assertTrue(
            strpos($ConfirmationAllParams, '%Subscriber:SubscriptionDate%') === false,
            $message . ' SubscriptionDate'
        );
        $this->assertTrue(strpos($ConfirmationAllParams, '%Subscriber:OptInDate%') > 0, $message . ' OptInDate');
        $this->assertTrue(
            strpos($ConfirmationAllParams, '%Subscriber:CustomFieldFirstName%') > 0,
            $message . ' CustomFieldFirstName'
        );
        $this->assertTrue(strpos($ConfirmationAllParams, '%Link:Confirm%') > 0, $message . ' %Link:Confirm%');
        $this->assertTrue(
            strpos($ConfirmationAllParams, '%Link:WebBrowser%') === false,
            $message . ' %Link:WebBrowser%'
        );
        $this->assertTrue(
            strpos($ConfirmationAllParams, '%Link:Unsubscribe%') === false,
            $message . ' %Link:Unsubscribe%'
        );
        $this->assertTrue(
            strpos($ConfirmationAllParams, '%Link:NoTrack(URL)%') === false,
            $message . ' %Link:NoTrack(URL)%'
        );
        $this->assertTrue(
            strpos($ConfirmationAllParams, '%Link:ChangeEmailAddress%') === false,
            $message . ' %Link:ChangeEmailAddress%'
        );
        $this->assertTrue(
            strpos($ConfirmationAllParams, '%Link:ClockLinkedImage%') === false,
            $message . ' %Link:ClockLinkedImage%'
        );
        $this->assertTrue(strpos($ConfirmationAllParams, '%User:Country%') > 0, $message . ' %User:Country%');

        $Mode = 'campaign';
        $message = 'Personalization::GetTagsFor ' . $Mode;

        $array_content_tags = Personalization::GetTagsFor($Mode, $account, -1, true);
        $array_content_tags += Personalization::GetPersonalizationUserTags(
            true
        ); //include the %User:...% tags to init replacements for this test
        $CampaignAllParams = 'CampaignParams:';
        foreach (array_filter($array_content_tags, 'is_array') as $keyarray) {

            $CampaignAllParams .= implode("\n", array_keys($keyarray));
        }
        $CampaignAllParams .= "\n$WLDomainParam\n";
        $this->assertTrue(
            strpos($CampaignAllParams, '%Subscriber:SubscriberID%') > 0,
            $message . ' SubscriberID' . $CampaignAllParams
        );
        $this->assertTrue(strpos($CampaignAllParams, '%Subscriber:BounceType%') === false, $message . ' BounceType');
        $this->assertTrue(
            strpos($CampaignAllParams, '%Subscriber:SubscriptionDate%') > 0,
            $message . ' SubscriptionDate'
        );
        $this->assertTrue(strpos($CampaignAllParams, '%Subscriber:OptInDate%') === false, $message . ' OptInDate');
        $this->assertTrue(
            strpos($CampaignAllParams, '%Subscriber:CustomFieldFirstName%') > 0,
            $message . ' CustomFieldFirstName'
        );
        $this->assertTrue(strpos($CampaignAllParams, '%Link:Confirm%') === false, $message . ' %Link:Confirm%');
        $this->assertTrue(strpos($CampaignAllParams, '%Link:WebBrowser%') > 0, $message . ' %Link:WebBrowser%');
        $this->assertTrue(strpos($CampaignAllParams, '%Link:Unsubscribe%') > 0, $message . ' %Link:Unsubscribe%');
        $this->assertTrue(strpos($CampaignAllParams, '%Link:NoTrack(URL)%') > 0, $message . ' %Link:NoTrack(URL)%');
        $this->assertTrue(
            strpos($CampaignAllParams, '%Link:ChangeEmailAddress%') > 0,
            $message . ' %Link:ChangeEmailAddress%'
        );
        $this->assertTrue(strpos($CampaignAllParams, '%User:FirstName%') === false, $message . ' %User:FirstName%');
        $this->assertTrue(strpos($CampaignAllParams, '%User:AffiliateURL%') > 0, $message . ' %User:AffiliateURL%');
        $this->assertTrue(strpos($CampaignAllParams, '%Invoice:Amount%') > 0, $message . ' %Invoice:Amount%');
        $this->assertTrue(strpos($CampaignAllParams, '%Invoice:GrossAmount%') > 0, $message . ' %Invoice:GrossAmount%');
        $this->assertTrue(strpos($CampaignAllParams, '%Invoice:NetAmount%') > 0, $message . ' %Invoice:NetAmount%');
        $this->assertTrue(strpos($CampaignAllParams, '%Invoice:TaxAmount%') > 0, $message . ' %Invoice:TaxAmount%');
        $this->assertTrue(strpos($CampaignAllParams, '%Invoice:ReceiptID%') > 0, $message . ' %Invoice:ReceiptID%');
        $this->assertTrue(strpos($CampaignAllParams, '%Invoice:CreatedOn%') > 0, $message . ' %Invoice:CreatedOn%');

        // create email test templates

        // create confirmation email

        $ConfirmationPlain = 'ConfirmationPlain ' . $PercentText .
            $ConfirmationPlainLink . ' ' .
            $ConfirmationPlainLinkWP1 . ' ' .
            $ConfirmationPlainLinkWP2 . ' ' .
            $ConfirmationPlainLinkWP3 . ' ' .
            $ConfirmationPlainLinkWP4 . ' ' .
            $NONexistingCustomFieldParameter . ' ' .
            $ConfirmationAllParams;

        $ConfirmationHTML = '';

        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'Confirmation Email',
            'ReportSpamEnabled' => 1,
            'Subject' => 'no email without subject',
            'PlainContent' => $ConfirmationPlain,
            'HTMLContent' => $ConfirmationHTML,
        );
        $ConfirmationEmailID = EmailsConfirmationEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($ConfirmationEmailID > 0, $message . ' create email');
        $ArrayConfirmationEmail = Emails::RetrieveEmailByID($UserID, $ConfirmationEmailID);
        $this->assertTrue(
            !empty($ArrayConfirmationEmail),
            $message . ' get email' . htmlspecialchars(print_r($ArrayConfirmationEmail, 1))
        );
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayConfirmationEmail);
        $this->assertTrue(
            !empty($ArrayRevision),
            $message . ' get email revision' . htmlspecialchars(print_r($ArrayRevision, 1))
        );
        $ArrayContent = unserialize($ArrayRevision['PlainContent']);
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $ConfirmationPlainLink),
            "$message revision $ConfirmationPlainLink"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $ConfirmationPlainLinkWP1),
            "$message revision $ConfirmationPlainLinkWP1"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $ConfirmationPlainLinkWP2),
            "$message revision $ConfirmationPlainLinkWP2"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $ConfirmationPlainLinkWP3),
            "$message revision $ConfirmationPlainLinkWP3"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $ConfirmationPlainLinkWP4),
            "$message revision $ConfirmationPlainLinkWP4"
        );

        // create campaign email

        $CampaignPlain = 'CampaignPlain ' . $PercentText .
            'CampaignPlainLink: ' . $CampaignPlainLink . ' ' .
            'CampaignPlainLinkWP1: ' . $CampaignPlainLinkWP1 . "\t" .
            'CampaignPlainLinkWP2: ' . $CampaignPlainLinkWP2 . "\n" .
            'CampaignPlainLinkWP3: ' . $CampaignPlainLinkWP3 . "\t" .
            'CampaignPlainLinkWP4: ' . $CampaignPlainLinkWP4 . "\n" .
            'URLCustomFieldParameter: ' . $URLCustomFieldParameter . ' ' .
            'WebsiteCustomFieldParameter: ' . $WebsiteCustomFieldParameter . ' ' .
            'HTMLCustomFieldParameter: ' . $HTMLCustomFieldParameter . ' ' .
            'NONexistingCustomFieldParameter: ' . $NONexistingCustomFieldParameter . ' ' .
            $CampaignAllParams;
        $CampaignPlain = str_replace('%Link:NoTrack(URL)%', "%Link:NoTrack($LinkNoTrackPlain)%", $CampaignPlain);

        $CampaignHTML = 'CampaignHTML ' . $PercentText .
            '<a href="' . $CampaignHTMLLink . '">CampaignHTMLLink</a><br/>' .
            '<a href="' . $CampaignHTMLLinkWP1 . '">CampaignHTMLLinkWP1</a><br/>' .
            '<a href="' . $CampaignHTMLLinkWP2 . '">CampaignHTMLLinkWP2</a><br/>' .
            '<a href="' . $CampaignHTMLLinkWP3 . '">CampaignHTMLLinkWP3</a><br/>' .
            '<a href="' . $CampaignHTMLLinkWP4 . '">CampaignHTMLLinkWP4</a><br/>' .
            'URLCustomFieldParameter: ' . $URLCustomFieldParameter . '<br/>' . // TESTX
            'WebsiteCustomFieldParameter: ' . $WebsiteCustomFieldParameter . '<br/>' . // TESTX
            '<a href="' . $URLCustomFieldParameter . '">URLCustomFieldParameter</a><br/>' .
            '<a href="' . $WebsiteCustomFieldParameter . '">WebsiteCustomFieldParameter</a><br/>' .
            'HTMLCustomFieldParameter: ' . $HTMLCustomFieldParameter . '<br/>' .
            'NONexistingCustomFieldParameter: ' . $NONexistingCustomFieldParameter . '<br/>' .
            $CampaignAllParams;
        $CampaignHTML = str_replace(
            '%Link:NoTrack(URL)%',
            "<a href=\"%Link:NoTrack($LinkNoTrackHTML)%\">no track</a>",
            $CampaignHTML
        );
        $CampaignHTML = str_replace('%Link:WebBrowser%', "<a href=\"%Link:WebBrowser%\">webbrowser</a>", $CampaignHTML);
        $CampaignHTML = str_replace(
            '%Link:Unsubscribe%',
            "<a href=\"%Link:Unsubscribe%\">unsubscribe</a>",
            $CampaignHTML
        );

        // create campaign email
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'Campaign Email',
            'ReportSpamEnabled' => 1,
            'Subject' => 'no email without subject',
            'PlainContent' => $CampaignPlain,
            'HTMLContent' => $CampaignHTML,
        );
        $CampaignEmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($CampaignEmailID > 0, $message . ' create email');
        $ArrayCampaignEmail = Emails::RetrieveEmailByID($UserID, $CampaignEmailID);
        $this->assertTrue(
            !empty($ArrayCampaignEmail),
            $message . ' get email' . htmlspecialchars(print_r($ArrayCampaignEmail, 1))
        );
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayCampaignEmail);
        $this->assertTrue(
            !empty($ArrayRevision),
            $message . ' get email revision' . htmlspecialchars(print_r($ArrayRevision, 1))
        );
        $ArrayContent = unserialize($ArrayRevision['PlainContent']);
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignPlainLink),
            "$message revision $CampaignPlainLink"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignPlainLinkWP1),
            "$message revision $CampaignPlainLinkWP1"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignPlainLinkWP2),
            "$message revision $CampaignPlainLinkWP2"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignPlainLinkWP3),
            "$message revision $CampaignPlainLinkWP3"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignPlainLinkWP4),
            "$message revision $CampaignPlainLinkWP4"
        );
        $WebsiteCustomFieldPlainLinkNumber = $this->linkNumberOfURLinRevision(
            $ArrayContent,
            $WebsiteCustomFieldParameter
        );
        $this->assertTrue(
            $WebsiteCustomFieldPlainLinkNumber >= 0,
            "$message revision $WebsiteCustomFieldParameter (link num $WebsiteCustomFieldPlainLinkNumber)"
        );
        $URLCustomFieldPlainLinkNumber = $this->linkNumberOfURLinRevision($ArrayContent, $URLCustomFieldParameter);
        $this->assertTrue(
            $URLCustomFieldPlainLinkNumber >= 0,
            "$message revision $URLCustomFieldParameter (link num $URLCustomFieldPlainLinkNumber)"
        );
        $ArrayContent = unserialize($ArrayRevision['HTMLContent']);
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignHTMLLink),
            "$message revision $CampaignHTMLLink"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignHTMLLinkWP1),
            "$message revision $CampaignHTMLLinkWP1"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignHTMLLinkWP2),
            "$message revision $CampaignHTMLLinkWP2"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignHTMLLinkWP3),
            "$message revision $CampaignHTMLLinkWP3"
        );
        $this->assertTrue(
            $this->findURLinRevision($ArrayContent, $CampaignHTMLLinkWP4),
            "$message revision $CampaignHTMLLinkWP4"
        );
        $WebsiteCustomFieldHtmlLinkNumber = $this->linkNumberOfURLinRevision(
            $ArrayContent,
            $WebsiteCustomFieldParameter
        );
        $this->assertTrue(
            $WebsiteCustomFieldHtmlLinkNumber >= 0,
            "$message revision $WebsiteCustomFieldParameter (link num  $WebsiteCustomFieldHtmlLinkNumber)"
        );
        $URLCustomFieldHtmlLinkNumber = $this->linkNumberOfURLinRevision($ArrayContent, $URLCustomFieldParameter);
        $this->assertTrue(
            $URLCustomFieldHtmlLinkNumber >= 0,
            "$message revision $URLCustomFieldParameter (link num $URLCustomFieldHtmlLinkNumber)"
        );

        /*
         * PersonalizeLinkByRevision
         */
        $message = 'Personalization::PersonalizeLinkByRevision';

        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayConfirmationEmail);
        $ArrayContent = unserialize($ArrayRevision['PlainContent']);
        $Params = array();
        $Content = Personalization::PersonalizeLinkByRevision(
            $ArrayConfirmationEmail['PlainContent'],
            $ArrayContent,
            'track_link',
            '0',
            $ArrayRevision['RevisionID'],
            $Params
        );
        $this->assertTrue(!empty($Content), $message . ' content' . print_r($Content, 1));
        $this->assertTrue(!empty($Params), $message . ' content' . print_r($Params, 1));
        $this->assertTrue(
            strpos($Content, '%Tracking-track_link-0-') > 0,
            $message . ' track_link plain ' . print_r($ArrayContent, 1)
        );
        $this->assertTrue(strpos($Content, $Params[0]) > 0, $message . ' track_link plain from params');

        /*
         * PersonalizePrepareContent
         */

        $Mode = 'campaign';
        $message = 'Personalization::PersonalizePrepareContent ' . $Mode;

        // test plain
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayCampaignEmail);
        $this->assertTrue(
            !empty($ArrayRevision),
            $message . ' revisions: ' . htmlspecialchars(print_r($ArrayRevision, 1))
        );
        $Params = array();
        $PlainContent = Personalization::PersonalizePrepareContent(
            $ArrayCampaignEmail['PlainContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );
        $this->assertTrue(!empty($Params), $message . ' content: ' . htmlspecialchars(print_r($Params, 1)));
        $this->assertTrue(strpos($PlainContent, $Params[0]) > 0, $message . ' track_link plain from params');
        $this->assertTrue(
            strpos($PlainContent, $HTMLSignatureText) === false,
            $message . ' html signature' . $PlainContent
        );
        $this->assertTrue(strpos($PlainContent, $PlainSignatureText) > 0, $message . ' plain signature');
        $this->assertTrue(strpos($PlainContent, 'LinkNoTrackLinkNoTrack') > 0, $message . ' link no track');
        $this->assertTrue(strpos($PlainContent, 'ktmailheader') === false, $message . ' spam report preview');
        $this->assertTrue(strpos($PlainContent, '%Link:TrackOpen%') === false, $message . ' TrackOpen');
        $this->assertTrue(strpos($PlainContent, '%Tracking-track_link-0-') > 0, $message . ' track_link plain ');
        $this->assertTrue(strpos($PlainContent, '%Tracking-track_link-1-') === false, $message . ' track_link html ');
        $this->assertTrue(
            strpos($PlainContent, '%Tracking-track_sign_link-0-') > 0,
            $message . ' track_sign_link plain '
        );
        $this->assertTrue(
            strpos($PlainContent, '%Tracking-track_sign_link-1-') === false,
            $message . ' track_sign_link html '
        );
        $this->assertTrue(strpos($PlainContent, '%Subscriber:SubscriberID%') > 0, $message . ' SubscriberID');
        $this->assertTrue(strpos($PlainContent, '%Subscriber:BounceType%') === false, $message . ' BounceType');
        $this->assertTrue(strpos($PlainContent, '%Subscriber:SubscriptionDate%') > 0, $message . ' SubscriptionDate');
        $this->assertTrue(strpos($PlainContent, '%Subscriber:OptInDate%') === false, $message . ' OptInDate');
        // custom url fields are replaced with track links
        $this->assertTrue(
            strpos($PlainContent, '%Subscriber:CustomField' . $URLCustomFieldID . '%') === false,
            $message . ' Custom url field'
        );
        $this->assertTrue(
            strpos($PlainContent, '%Subscriber:CustomFieldWebsite%') === false,
            $message . ' Global custom website field'
        );
        // non custom url fields are not replaced with track links
        $this->assertTrue(
            strpos($PlainContent, '%Subscriber:CustomField' . $HTMLCustomFieldID . '%') > 0,
            $message . ' Custom url field'
        );
        $this->assertTrue(strpos($PlainContent, '%Link:Confirm%') === false, $message . ' %Link:Confirm%');
        $this->assertTrue(strpos($PlainContent, '%Link:WebBrowser%') > 0, $message . ' %Link:WebBrowser%');
        $this->assertTrue(strpos($PlainContent, '%Link:Unsubscribe%') > 0, $message . ' %Link:Unsubscribe%');
        $this->assertTrue(strpos($PlainContent, '%Link:NoTrack(URL)%') === false, $message . ' %Link:NoTrack(URL)%');
        $this->assertTrue(strpos($PlainContent, '%User:FirstName%') === false, $message . ' %User:FirstName%');
        $this->assertTrue(strpos($PlainContent, '%User:Country%') === false, $message . ' %User:Country%');
        $this->assertTrue(strpos($PlainContent, '%User:AffiliateURL%') > 0, $message . ' %User:AffiliateURL%');
        // check if all tracking links are present ($CampaignPlainLink is a substring of $CampaignPlainLinkWP1 ...)
        $ArrayContent = unserialize($ArrayRevision['PlainContent']);
        foreach ($ArrayContent as $id => $ArrayLink) {
            $p = "%Tracking-track_link-0-{$ArrayRevision['RevisionID']}-$id-0-link%";
            if ($ArrayLink['Link'] == $CampaignPlainLink) {
                $this->assertTrue(strpos($PlainContent, $p) > 0, $message . ' track_link plain ' . $CampaignPlainLink);
                $this->assertTrue(in_array($p, $Params), $message . ' track_link plain in params' . $p);
            }
            if ($ArrayLink['Link'] == $CampaignPlainLinkWP1) {
                $this->assertTrue(
                    strpos($PlainContent, $p) > 0,
                    $message . ' track_link plain ' . $CampaignPlainLinkWP1
                );
                $this->assertTrue(in_array($p, $Params), $message . ' track_link plain in params' . $p);
            }
            if ($ArrayLink['Link'] == $CampaignPlainLinkWP2) {
                $this->assertTrue(
                    strpos($PlainContent, $p) > 0,
                    $message . ' track_link plain ' . $CampaignPlainLinkWP2
                );
                $this->assertTrue(in_array($p, $Params), $message . ' track_link plain in params' . $p);
            }
            if ($ArrayLink['Link'] == $CampaignPlainLinkWP3) {
                $this->assertTrue(
                    strpos($PlainContent, $p) > 0,
                    $message . ' track_link plain ' . $CampaignPlainLinkWP3
                );
                $this->assertTrue(in_array($p, $Params), $message . ' track_link plain in params' . $p);
            }
            if ($ArrayLink['Link'] == $CampaignPlainLinkWP4) {
                $this->assertTrue(
                    strpos($PlainContent, $p) > 0,
                    $message . ' track_link plain ' . $CampaignPlainLinkWP4
                );
                $this->assertTrue(in_array($p, $Params), $message . ' track_link plain in params' . $p);
            }
        }

        // test html
        $Params = array();
        $ReportSpamEnabled = !empty($ArrayCampaignEmail['ReportSpamEnabled']);
        $ReportSpamPreviewText = '';
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayCampaignEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature,
            0,
            false,
            $ReportSpamEnabled,
            $ReportSpamPreviewText
        );
        $this->assertTrue(!empty($Params), $message . ' html content' . htmlspecialchars(print_r($Params, 1)));
        $this->assertTrue(
            strpos($HTMLContent, $HTMLSignatureText) > 0,
            $message . ' html signature' . htmlspecialchars($HTMLContent)
        );
        $this->assertTrue(strpos($HTMLContent, $PlainSignatureText) === false, $message . ' plain signature');
        $this->assertTrue(strpos($HTMLContent, 'LinkNoTrackLinkNoTrack') > 0, $message . ' link no track');
        $this->assertTrue(strpos($HTMLContent, 'ktmailheader') > 0, $message . ' spam report preview');
        $this->assertStringContainsString('%Link:ReportSpam%', $HTMLContent, $message . ' spam report preview');
        $this->assertStringContainsString('%Link:TrackOpen%', $HTMLContent, $message . ' TrackOpen');
        $this->assertTrue(strpos($HTMLContent, '%Tracking-track_link-0-') === false, $message . ' track_link plain ');
        $this->assertTrue(strpos($HTMLContent, '%Tracking-track_link-1-') > 0, $message . ' track_link html '); // fails
        $this->assertTrue(
            strpos($HTMLContent, '%Tracking-track_sign_link-0-') === false,
            $message . ' track_sign_link plain '
        );
        $this->assertTrue(
            strpos($HTMLContent, '%Tracking-track_sign_link-1-') > 0,
            $message . ' track_sign_link html '
        );
        $this->assertTrue(strpos($HTMLContent, '%Subscriber:SubscriberID%') > 0, $message . ' SubscriberID');
        $this->assertTrue(strpos($HTMLContent, '%Subscriber:BounceType%') === false, $message . ' BounceType');
        $this->assertTrue(strpos($HTMLContent, '%Subscriber:SubscriptionDate%') > 0, $message . ' SubscriptionDate');
        $this->assertTrue(strpos($HTMLContent, '%Subscriber:OptInDate%') === false, $message . ' OptInDate');
        // custom url fields are replaced with track links: 2 url custom fields are used outside of <a>/<area>
        // and should remain
        $this->assertTrue(
            substr_count($HTMLContent, '%Subscriber:CustomField' . $URLCustomFieldID . '%') === 2,
            $message . ' Custom url field ' . $URLCustomFieldID
        );
        $this->assertTrue(
            substr_count($HTMLContent, '%Subscriber:CustomFieldWebsite%') === 2,
            $message . ' Global custom website field'
        );
        // non custom url fields are not replaced with track links
        $this->assertTrue(
            strpos($HTMLContent, '%Subscriber:CustomField' . $HTMLCustomFieldID . '%') > 0,
            $message . ' Custom url field'
        );
        $this->assertTrue(strpos($HTMLContent, '%Link:Confirm%') === false, $message . ' %Link:Confirm%');
        $this->assertTrue(strpos($HTMLContent, '%Link:WebBrowser%') > 0, $message . ' %Link:WebBrowser%');
        $this->assertTrue(strpos($HTMLContent, '%Link:Unsubscribe%') > 0, $message . ' %Link:Unsubscribe%');
        $this->assertTrue(strpos($HTMLContent, '%Link:NoTrack(URL)%') === false, $message . ' %Link:NoTrack(URL)%');
        $this->assertTrue(strpos($HTMLContent, '%User:FirstName%') === false, $message . ' %User:FirstName%');
        $this->assertTrue(strpos($HTMLContent, '%User:Country%') === false, $message . ' %User:Country%');
        $this->assertTrue(strpos($HTMLContent, '%User:AffiliateURL%') > 0, $message . ' %User:AffiliateURL%');

        $ReportSpamPreviewText = 'I am a nice PreHeader';
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayCampaignEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature,
            0,
            false,
            false, // pre header should still work
            $ReportSpamPreviewText
        );

        $this->assertStringNotContainsString('%Link:ReportSpam%', $HTMLContent, $message . ' spam report preview');
        $this->assertStringContainsString($ReportSpamPreviewText, $HTMLContent, $message . ' PreHeader');
        $this->assertStringContainsString('%Link:TrackOpen%', $HTMLContent, $message . ' TrackOpen');


        /*
         * PersonalizeExtractSingle
         */

        $message = 'Personalization::PersonalizeExtractSingle';

        $TestSubject = '%TEST%';
        $Params = Personalization::PersonalizeExtractSingle($TestSubject);
        $Params = Personalization::PersonalizeExtractSingle($PlainContent, $Params);
        $Params = Personalization::PersonalizeExtractSingle($HTMLContent, $Params);
        $Params = array_fill_keys($Params, '');
        $this->assertTrue(!empty($Params), $message . ' params' . print_r($Params, 1));
        $Keys = array_keys($Params);
        $this->assertTrue(
            in_array('%Subscriber:SubscriptionDate%', $Keys),
            $message . ' %Subscriber:SubscriptionDate%'
        );
        $this->assertTrue(
            in_array($NONexistingCustomFieldParameter, $Keys),
            $message . ' NONexistingCustomFieldParameter'
        );
        $this->assertTrue(!in_array($TestSubject, $Keys), $message . ' ' . $TestSubject);

        /*
         * PersonalizeAssign confirmation
         */

        $message = 'Personalization::PersonalizeAssign confirmation';

        $IsPreview = true; // just for coding links
        $ListFormID = 4713; // just for coding links
        $ListID = 4714; // just for coding links

        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $ConfirmationEmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);
        $PlainContent = $ArrayEmail['PlainContent'];
        $HTMLContent = $ArrayEmail['HTMLContent'];
        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['Subject']);
        $Params = Personalization::PersonalizeExtractSingle($PlainContent, $Params);
        $Params = Personalization::PersonalizeExtractSingle($HTMLContent, $Params);
        $this->assertTrue(!empty($Params), $message . ' params' . print_r($Params, 1));

        // test
        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            0,
            $ArraySubscription,
            $ReferenceID,
            $IsPreview,
            $SenderDomain,
            [],
            $ListFormID,
            false,
            $ListID
        );
        $this->assertTrue(!empty($Replacements), $message . ' Replacements' . print_r($Replacements, 1));
        foreach ($Params as $key) {
            $this->assertTrue(
                isset($Replacements[$key]),
                $message . ' Replacements ' . $key . ' = ' . $Replacements[$key]
            );
        }

        /*
         * PersonalizeReplace confirmation
         */

        $message = 'Personalization::PersonalizeReplace confirmation';

        $TMPSubject = Personalization::PersonalizeReplace($ArrayEmail['Subject'], $Replacements);
        $this->assertTrue($TMPSubject == $ArrayEmail['Subject'], $message . ' TMPSubject ' . $TMPSubject);

        $TMPPlainBody = Personalization::PersonalizeReplace($PlainContent, $Replacements);

        // urls may contain coded chars (%3D) and the percent text contains %, but we want to check for parameters in the next step
        $this->assertTrue(strpos($TMPPlainBody, $PercentText) > 0, $message . ' TMPPlainBody PercentText');
        $TMPPlainBody = str_replace(array(
            $PercentText,
            '%3D'
        ), '', $TMPPlainBody); // remove % content

        $this->assertTrue(strpos($TMPPlainBody, '%') === false, $message . ' TMPPlainBody ' . $TMPPlainBody);
        $this->assertTrue(strpos($TMPPlainBody, $URLCustomFieldValue) > 0, $message . ' TMPPlainBody cf url');
        $this->assertTrue(strpos($TMPPlainBody, $HTMLCustomFieldValue) !== false, $message . ' TMPPlainBody cf html');

        $TMPHTMLBody = Personalization::PersonalizeReplace($HTMLContent, $Replacements);
        $this->assertTrue(empty($TMPHTMLBody), $message . ' TMPHTMLBody ' . $TMPHTMLBody);

        /*
         * PersonalizeAssign campaign
         */

        $message = 'Personalization::PersonalizeAssign campaign';

        $CampaignID = 4711; // just for coding links
        $IsPreview = true; // just for coding links

        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $CampaignEmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);
        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['Subject']);
        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['PlainContent'], $Params);
        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent'], $Params);
        $PlainContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['PlainContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );
        $this->assertTrue(!empty($PlainContent), $message . ' plain content: ' . htmlspecialchars($PlainContent));
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature,
            0,
            false
        );
        $this->assertTrue(!empty($HTMLContent), $message . ' html content: ' . htmlspecialchars($HTMLContent));
        $this->assertTrue(!empty($Params), $message . ' params' . print_r($Params, 1));

        // test
        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            $CampaignID,
            $ArraySubscription,
            $ReferenceID,
            $IsPreview,
            $SenderDomain
        );
        $this->assertTrue(!empty($Replacements), $message . ' Replacements' . print_r($Replacements, 1));
        foreach ($Params as $key) {
            $this->assertTrue(
                isset($Replacements[$key]),
                $message . ' Replacements ' . $key . ' = ' . $Replacements[$key]
            );
        }

        /*
         * PersonalizeReplace campaign
         */

        $message = 'Personalization::PersonalizeReplace campaign';

        $TMPSubject = Personalization::PersonalizeReplace($ArrayEmail['Subject'], $Replacements);
        $this->assertTrue($TMPSubject == $ArrayEmail['Subject'], $message . ' TMPSubject ' . $TMPSubject);

        $TMPPlainBody = Personalization::PersonalizeReplace($PlainContent, $Replacements);

        // urls may contain coded chars (%3D) and the percent text contains %, but we want to check for parameters in the next step
        $this->assertTrue(strpos($TMPPlainBody, $PercentText) > 0, $message . ' TMPPlainBody PercentText');
        $TMPPlainBody = str_replace(array(
            $PercentText,
            '%3D'
        ), '', $TMPPlainBody); // remove % content

        $ArrayQueryParameters = [
            'RevisionID' => $ArrayRevision['RevisionID'],
            'Linknumber' => $URLCustomFieldPlainLinkNumber,
            'CampaignID' => $CampaignID,
            'SubscriberID' => $ArraySubscription['SubscriberID'],
            'Preview' => $IsPreview,
        ];
        $urlCustomFieldTrackLink = Core::EncryptURL($ArrayQueryParameters, "track_link", "");

        $ArrayQueryParameters = [
            'RevisionID' => $ArrayRevision['RevisionID'],
            'Linknumber' => $WebsiteCustomFieldPlainLinkNumber,
            'CampaignID' => $CampaignID,
            'SubscriberID' => $ArraySubscription['SubscriberID'],
            'Preview' => $IsPreview,
        ];
        $websiteCustomFieldTrackLink = Core::EncryptURL($ArrayQueryParameters, "track_link", "");

        $this->assertTrue(
            strpos($TMPPlainBody, '%') === false,
            $message . ' TMPPlainBody ' . htmlspecialchars($TMPPlainBody)
        );
        $this->assertTrue(
            strpos($TMPPlainBody, $urlCustomFieldTrackLink) > 0,
            $message . ' TMPPlainBody cf url ' . $urlCustomFieldTrackLink
        );
        $this->assertTrue(
            strpos($TMPPlainBody, $websiteCustomFieldTrackLink) > 0,
            $message . ' TMPPlainBody cf website ' . $websiteCustomFieldTrackLink
        );
        $this->assertTrue(strpos($TMPPlainBody, $HTMLCustomFieldValue) !== false, $message . ' TMPPlainBody cf html');
        $this->assertTrue(strpos($TMPPlainBody, $WLDomainReplacement) > 0, $message . ' TMPPlainBody WL Domain');

        $TMPHTMLBody = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

        // urls may contain coded chars (%3D) and the percent text contains %, but we want to check for parameters in the next step
        $this->assertTrue(strpos($TMPHTMLBody, $PercentText) > 0, $message . ' TMPHTMLBody PercentText');
        $TMPHTMLBody = str_replace(array(
            $PercentText,
            '%3D'
        ), '', $TMPHTMLBody); // remove % content

        $ArrayQueryParameters = [
            'RevisionID' => $ArrayRevision['RevisionID'],
            'Linknumber' => $URLCustomFieldHtmlLinkNumber,
            'CampaignID' => $CampaignID,
            'SubscriberID' => $ArraySubscription['SubscriberID'],
            'Preview' => $IsPreview,
            'HTML' => true,
        ];
        $urlCustomFieldTrackLink = Core::EncryptURL($ArrayQueryParameters, "track_link", "");
        $urlCustomFieldTrackLinkFull = ' href="' . Core::EncryptURL(
            $ArrayQueryParameters,
            "track_link",
            $SenderDomain['appurl']
        ) . '"';

        $ArrayQueryParameters = [
            'RevisionID' => $ArrayRevision['RevisionID'],
            'Linknumber' => $WebsiteCustomFieldHtmlLinkNumber,
            'CampaignID' => $CampaignID,
            'SubscriberID' => $ArraySubscription['SubscriberID'],
            'Preview' => $IsPreview,
            'HTML' => true,
        ];
        $websiteCustomFieldTrackLink = Core::EncryptURL($ArrayQueryParameters, "track_link", "");
        $websiteCustomFieldTrackLinkFull = ' href="' . Core::EncryptURL(
            $ArrayQueryParameters,
            "track_link",
            $SenderDomain['appurl']
        ) . '"';

        $this->assertTrue(
            strpos($TMPHTMLBody, '%') === false,
            $message . ' TMPHTMLBody ' . htmlspecialchars($TMPHTMLBody)
        );
        $this->assertTrue(
            strpos($TMPHTMLBody, $urlCustomFieldTrackLink) > 0,
            $message . ' TMPHTMLBody cf url' . $urlCustomFieldTrackLink
        );
        $this->assertTrue(
            strpos($TMPHTMLBody, $websiteCustomFieldTrackLink) > 0,
            $message . ' TMPHTMLBody cf website ' . $websiteCustomFieldTrackLink
        );
        $this->assertTrue(strpos($TMPHTMLBody, $HTMLCustomFieldValue) !== false, $message . ' TMPPlainBody cf html');
        $this->assertTrue(strpos($TMPHTMLBody, $WLDomainReplacement) > 0, $message . ' TMPHTMLBody WL Domain');

        $this->assertTrue(
            strpos($TMPHTMLBody, $urlCustomFieldTrackLinkFull) > 0,
            $message . ' TMPHTMLBody cf url full: ' . $urlCustomFieldTrackLinkFull
        );
        $this->assertTrue(
            strpos($TMPHTMLBody, $websiteCustomFieldTrackLinkFull) > 0,
            $message . ' TMPHTMLBody cf website full: ' . $websiteCustomFieldTrackLinkFull
        );

        /*
         * PersonalizeGetCountry
         */

        $message = 'Personalization::PersonalizeGetCountry';

        $Country = Personalization::PersonalizeGetCountry('DE');
        $this->assertTrue($Country == 'Germany', $message . ' Germany ' . $Country);
        $Country = Personalization::PersonalizeGetCountry('ZW');
        $this->assertTrue($Country == 'Zimbabwe', $message . ' Zimbabwe ' . $Country);
        $Country = Personalization::PersonalizeGetCountry(null);
        $this->assertTrue(empty($Country), $message . ' empty ' . $Country);

        /*
         * PersonalizeSMS
         */

        $message = 'Personalization::PersonalizeSMS';

        $ArrayUser = (array)user_load($UserID);
        $ArrayAutoResponder = array('CampaignID' => 4711); // just for coding links

        $ArraySubscription = array(
            // email
            'SubscriberID' => 1,
            'RelOwnerUserID' => $UserID,
            'EmailAddress' => '1',
            'BounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
            'SubscriptionDate' => 0,
            'LastOpenDate' => 0,
            'SubscriptionIP' => '',
            'SubscriptionReferrer' => '',
            'UnsubscriptionDate' => 0,
            'UnsubscriptionIP' => '',
            'OptInDate' => 0,
            'OptInIP' => '',
            // sms
            'PhoneNumber' => '4711',
            'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SMSBounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
            'SMSSubscriptionDate' => strtotime('-5 days'),
            'SMSUnsubscriptionDate' => 0,
            'SubscriptionSMS' => 'yes, send me SMS',
        );
        $ArraySubscription["CustomField$URLCustomFieldID"] = $URLCustomFieldValue;
        $ArraySubscription["CustomField$HTMLCustomFieldID"] = $HTMLCustomFieldValue;

        // create a fresh campaign email
        $CampaignSMS = "there is a link $CampaignPlainLink and some parameters %Subscriber:PhoneNumber%%Subscriber:SMSSubscriptionDate%%Subscriber:SubscriptionSMS%";
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'Campaign SMS',
            'PlainContent' => $CampaignSMS,
        );
        $CampaignEmailID = EmailsNewsletterSMS::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($CampaignEmailID > 0, $message . ' create email');
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $CampaignEmailID);

        $ArrayEmail['TurnOffTrackLink'] = 0; // with short links

        $TMPPlainBody = Personalization::PersonalizeSMS(
            $ArrayUser,
            $CampaignID,
            $ArrayEmail,
            $ArraySubscription,
            $ReferenceID
        );

        $this->assertTrue(
            strpos($TMPPlainBody, variable_get('klicktipp_short_link_domain', APP_URL . 's') . '/') > 0,
            $message . ' CampaignPlainLink ' . $TMPPlainBody
        );
        $this->assertTrue(strpos($TMPPlainBody, $ArraySubscription['PhoneNumber']) > 0, $message . ' PhoneNumber');
        $this->assertTrue(
            strpos($TMPPlainBody, Dates::formatDate(Dates::FORMAT_DMY, (int) $ArraySubscription['SMSSubscriptionDate'])) > 0,
            $message . ' SMSSubscriptionDate'
        );
        $this->assertTrue(
            strpos($TMPPlainBody, $ArraySubscription['SubscriptionSMS']) > 0,
            $message . ' SubscriptionSMS'
        );

        $ArrayEmail['TurnOffTrackLink'] = 1; // no short links

        $TMPPlainBody = Personalization::PersonalizeSMS(
            $ArrayUser,
            $CampaignID,
            $ArrayEmail,
            $ArraySubscription,
            $ReferenceID
        );

        $this->assertTrue(
            strpos($TMPPlainBody, $CampaignPlainLink) > 0,
            $message . ' CampaignPlainLink' . $TMPPlainBody
        );

        /*
         * PersonalizeEmail campaign
         */

        $message = 'Personalization::PersonalizeEmail campaign';

        $ArrayUser = (array)user_load($UserID);
        $ArrayAutoResponder = array('CampaignID' => 4711); // just for coding links
        $ArraySubscriber = Subscribers::SelectDummySubscriber($UserID);
        $ArraySubscriber["CustomField$URLCustomFieldID"] = $URLCustomFieldValue;
        $ArraySubscriber["CustomField$HTMLCustomFieldID"] = $HTMLCustomFieldValue;

        // create a fresh campaign email
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'Campaign Email 2',
            'ReportSpamEnabled' => 1,
            'Subject' => 'congrats %Subscriber:CustomFieldFirstName% - we love tests day',
            'PlainContent' => $CampaignPlain,
            'HTMLContent' => $CampaignHTML,
        );
        $CampaignEmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($CampaignEmailID > 0, $message . ' create email');
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $CampaignEmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

        $PaymentData = [
            //'PaymentID' => $PaymentID,
            //'BuildID' => $BuildID,
            'Payment' => array(
                'GrossAmount' => 11900,
                'NetAmount' => 10000,
                'TaxAmount' => 1900,
                'Currency' => 'EUR',
                //'Payout' => 0,
                //'PayoutCurrency' => 'EUR',
                'ReceiptID' => 'IPN4711RECEIPT',
                'CreatedOn' => strtotime('2018-01-01 01:01'),
                //'SubscriptionID' => '',
            ),
            'Amount' => 22222,
            'Currency' => 'EUR',
            //'ProductName' => $Name,
            //'ProductEditURL' => $EditURL,
            //'IPN' => $ipn_data,
        ];

        [$TMPSubject, $TMPPlainBody, $TMPHTMLBody, $ContentType] = Personalization::PersonalizeEmail(
            $ArrayUser,
            $ArrayAutoResponder['CampaignID'],
            $ArrayEmail,
            $ArraySignature,
            $ArraySubscriber,
            $ReferenceID,
            $SenderDomain,
            $PaymentData,
            true
        );

        $this->assertTrue(
            $ContentType == Emails::CONTENT_TYPE_BOTH,
            $message . ' ContentType' . print_r(array(
                $TMPSubject,
                $TMPPlainBody,
                $TMPHTMLBody,
                $ContentType,
            ), 1)
        );

        $this->assertTrue(strpos($TMPSubject, $AffiliateURL) === false, $message . ' AffiliateURL TMPSubject');
        $this->assertTrue(
            strpos($TMPPlainBody, $AffiliateURL) > 0,
            $message . ' AffiliateURL TMPPlainBody' . $AffiliateURL
        );
        $this->assertTrue(strpos($TMPHTMLBody, $AffiliateURL) > 0, $message . ' AffiliateURL TMPHTMLBody');
        $this->assertTrue(strpos($TMPHTMLBody, $WLDomainReplacement) > 0, $message . ' TMPHTMLBody WL Domain');

        $this->assertTrue(
            strpos($TMPHTMLBody, klicktipp_number_format(intval($PaymentData['Amount']) / 100, 2)) > 0,
            $message . ' Amount'
        );
        $this->assertTrue(
            strpos($TMPHTMLBody, klicktipp_number_format(intval($PaymentData['Payment']['GrossAmount']) / 100, 2)) > 0,
            $message . ' GrossAmount'
        );
        $this->assertTrue(
            strpos($TMPHTMLBody, klicktipp_number_format(intval($PaymentData['Payment']['NetAmount']) / 100, 2)) > 0,
            $message . ' NetAmount'
        );
        $this->assertTrue(
            strpos($TMPHTMLBody, klicktipp_number_format(intval($PaymentData['Payment']['TaxAmount']) / 100, 2)) > 0,
            $message . ' TaxAmount'
        );
        $this->assertTrue(
            strpos($TMPHTMLBody, $PaymentData['Payment']['ReceiptID']) > 0,
            $message . ' ReceiptID' . $PaymentData['Payment']['ReceiptID']
        );
        $this->assertTrue(
            strpos($TMPHTMLBody, Dates::formatDate(Dates::FORMAT_DMY, (int) $PaymentData['Payment']['CreatedOn'])) > 0,
            $message . ' CreatedOn' . Dates::formatDate(Dates::FORMAT_DMY, (int) $PaymentData['Payment']['CreatedOn'])
        );

        /*
         * PersonalizeEmail confirmation
         */

        $message = 'Personalization::PersonalizeEmail confirmation';

        $ArrayUser = (array)user_load($UserID);
        $ArraySubscriber = Subscribers::SelectDummySubscriber($UserID);
        $ArraySubscriber["CustomField$URLCustomFieldID"] = $URLCustomFieldValue;
        $ArraySubscriber["CustomField$HTMLCustomFieldID"] = $HTMLCustomFieldValue;
        $IsPreview = true; // just for coding links
        $OptInSubscribeTo = 4712; // just for coding links
        $ListFormID = 4713; // just for coding links
        $ListID = 4714; // just for coding links

        // create a fresh confirmation email
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'Confirmation Email 2',
            'ReportSpamEnabled' => 1,
            'Subject' => '%User:Country%',
            // special test to check countries and translations
            'PlainContent' => $ConfirmationPlain,
            'HTMLContent' => $ConfirmationHTML,
        );
        $ConfirmationEmailID = EmailsConfirmationEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($ConfirmationEmailID > 0, $message . ' create email');
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $ConfirmationEmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

        // make sure, we start with default
        klicktipp_set_language();

        [$TMPSubject, $TMPPlainBody, $TMPHTMLBody, $ContentType] = Personalization::PersonalizeEmail(
            $ArrayUser,
            0,
            $ArrayEmail,
            [],
            $ArraySubscriber,
            $ReferenceID,
            $SenderDomain,
            [],
            false,
            $IsPreview,
            true,
            $ListFormID,
            $ListID
        );

        $this->assertTrue(
            $ContentType == Emails::CONTENT_TYPE_PLAIN,
            $message . ' ContentType' . print_r(array(
                $TMPSubject,
                $TMPPlainBody,
                $TMPHTMLBody,
                $ContentType,
            ), 1)
        );
        $this->assertTrue(empty($TMPHTMLBody), $message . ' TMPHTMLBody' . print_r($ArrayEmail, 1));

        $this->assertTrue(strpos($TMPSubject, $AffiliateURL) === false, $message . ' AffiliateURL TMPSubject');
        $this->assertTrue(strpos($TMPPlainBody, $AffiliateURL) > 0, $message . ' AffiliateURL TMPPlainBody');

        // test working translation
        klicktipp_set_language(); // reset to default
        // @todo comment in if t-function works
//        $this->assertTrue($TMPSubject == $transptbr, $message . ' trans Country pt-br' . print_r(array(
//                $ArrayUser['Country'],
//                $TMPSubject,
//                $transptbr,
//                $transen,
//                t('Germany'),
//            ), 1));
        $this->assertTrue(t('Germany') == $transen, $message . ' trans Country current language');

        $AccountLanguage = $ArrayUser['language'];

        /*
         * PersonalizeEmail different signatures
         */

        $message = 'Personalization::PersonalizeEmail different signatures';

        // two different signatures
        $Signature1 = array(
            'HTMLSignatureText' => 'signature1html',
            'PlainSignatureText' => 'signature1plain',
        );
        $Signature2 = array(
            'HTMLSignatureText' => 'signature2html',
            'PlainSignatureText' => 'signature2plain',
        );
        // same email
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $CampaignEmailID);

        // email with signature 1
        [$TMPSubject, $TMPPlainBody1, $TMPHTMLBody1, $ContentType] = Personalization::PersonalizeEmail(
            $ArrayUser,
            $ArrayAutoResponder['CampaignID'],
            $ArrayEmail,
            $Signature1,
            $ArraySubscriber,
            $ReferenceID,
            $SenderDomain,
            [],
            true
        );
        $this->assertTrue(
            strpos($TMPHTMLBody1, $Signature1['HTMLSignatureText']) > 0,
            $message . ' HTMLSignatureText 1' . print_r($TMPHTMLBody1, 1)
        );
        $this->assertTrue(
            strpos($TMPPlainBody1, $Signature1['PlainSignatureText']) > 0,
            $message . ' PlainSignatureText 1' . print_r($TMPPlainBody1, 1)
        );

        // email with signature 2
        [$TMPSubject, $TMPPlainBody2, $TMPHTMLBody2, $ContentType] = Personalization::PersonalizeEmail(
            $ArrayUser,
            $ArrayAutoResponder['CampaignID'],
            $ArrayEmail,
            $Signature2,
            $ArraySubscriber,
            $ReferenceID,
            $SenderDomain,
            [],
            true
        );
        $this->assertTrue(
            strpos($TMPHTMLBody2, $Signature2['HTMLSignatureText']) > 0,
            $message . ' HTMLSignatureText 2'
        );
        $this->assertTrue(
            strpos($TMPPlainBody2, $Signature2['PlainSignatureText']) > 0,
            $message . ' PlainSignatureText 2'
        );

        /*
         * PersonalizeEmail Tracklink ON
         */


        $DomainLinkHttp = "http://example.com";
        $DomainLinkHttps = "https://example.com";
        $SimpleLinkHttp = "http://www.example.com/contact";
        $SimpleLinkHttps = "https://www.example.com/contact";
        $ParamLinkHttp = "http://www.example.com?page=2&menu=3";
        $ParamLinkHttps = "https://www.example.com?page=2&menu=3";
        $NoTrackLinkHttp = "http://www.example.com/notrack";
        $NoTrackLinkHttps = "https://www.example.com?notrack=1";
        $UrlCustomFieldLink = '%Subscriber:CustomField' . $URLCustomFieldID . '%';
        $WebsiteCustomFieldLink = '%Subscriber:CustomFieldWebsite%';

        $TrackLinkContentPlain = "
            DomainLinkHttp: $DomainLinkHttp
            DomainLinkHttps: $DomainLinkHttps
            SimpleLinkHttp: $SimpleLinkHttp
            SimpleLinkHttps: $SimpleLinkHttps
            ParamLinkHttp: $ParamLinkHttp
            ParamLinkHttps: $ParamLinkHttps
            NoTrackLinkHttp: %Link:NoTrack($NoTrackLinkHttp)%
            NoTrackLinkHttps: %Link:NoTrack($NoTrackLinkHttps)%
            UrlCustomFieldLink: $UrlCustomFieldLink
            WebsiteCustomFieldLink: $WebsiteCustomFieldLink
        ";

        $TrackLinkContentHTML = '
            <a href="' . $DomainLinkHttp . '">DomainLinkHttp</a>
            <a href="' . $DomainLinkHttps . '">DomainLinkHttps</a>
            <a href="' . $SimpleLinkHttp . '">SimpleLinkHttp</a>
            <a href="' . $SimpleLinkHttps . '">SimpleLinkHttps</a>
            <a href="' . $ParamLinkHttp . '">ParamLinkHttp</a>
            <a href="' . $ParamLinkHttps . '">ParamLinkHttps</a>
            <a href="%Link:NoTrack(' . $NoTrackLinkHttp . ')%">NoTrackLinkHttp</a>
            <a href="%Link:NoTrack(' . $NoTrackLinkHttps . ')%">NoTrackLinkHttps</a>
            <a href="' . $UrlCustomFieldLink . '">UrlCustomFieldLink</a>
            <a href="' . $WebsiteCustomFieldLink . '">WebsiteCustomFieldLink</a>
        ';

        $ArrayUser = (array)user_load($UserID);
        $ArraySubscriber = Subscribers::SelectDummySubscriber($UserID);
        $IsPreview = true; // just for coding links
        $ListFormID = 4713; // just for coding links
        $ListID = 4714; // just for coding links

        // create a fresh confirmation email
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'TrackLink',
            'ReportSpamEnabled' => 1,
            'Subject' => 'TrackLink',
            'PlainContent' => $TrackLinkContentPlain,
            'HTMLContent' => $TrackLinkContentHTML,
        );
        $TrackLinkEmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($TrackLinkEmailID > 0, $message . ' create tracklink email');
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $TrackLinkEmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

        $message = 'Personalization::TrackLink ON:';

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['PlainContent']);
        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent'], $Params);
        $PlainContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['PlainContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );

        $this->assertTrue(
            strpos($PlainContent, '%Tracking-track_link-0-6-6-0-link%') !== false
            && strpos($HTMLContent, '%Tracking-track_link-0-6-6-0-link%') !== false,
            "$message Tracking url custom field"
        );
        $this->assertTrue(
            strpos($PlainContent, '%Tracking-track_link-0-6-7-0-link%') !== false
            && strpos($HTMLContent, '%Tracking-track_link-0-6-7-0-link%') !== false,
            "$message Tracking url custom field"
        );

        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            $CampaignID,
            $ArraySubscriber,
            $ReferenceID,
            $IsPreview,
            $SenderDomain,
            [],
            $ListFormID,
            false,
            $ListID
        );

        $PlainContent = Personalization::PersonalizeReplace($PlainContent, $Replacements);
        $HTMLContent = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

        $this->assertTrue(
            strpos($PlainContent, $DomainLinkHttp) === false && strpos($HTMLContent, $DomainLinkHttp) === false,
            "$message Tracking $DomainLinkHttp"
        );
        $this->assertTrue(
            strpos($PlainContent, $DomainLinkHttps) === false && strpos($HTMLContent, $DomainLinkHttps) === false,
            "$message Tracking $DomainLinkHttps"
        );
        $this->assertTrue(
            strpos($PlainContent, $SimpleLinkHttp) === false && strpos($HTMLContent, $SimpleLinkHttp) === false,
            "$message Tracking $SimpleLinkHttp"
        );
        $this->assertTrue(
            strpos($PlainContent, $SimpleLinkHttps) === false && strpos($HTMLContent, $SimpleLinkHttps) === false,
            "$message Tracking $SimpleLinkHttps"
        );
        $this->assertTrue(
            strpos($PlainContent, $ParamLinkHttp) === false && strpos($HTMLContent, $ParamLinkHttp) === false,
            "$message Tracking $ParamLinkHttp"
        );
        $this->assertTrue(
            strpos($PlainContent, $ParamLinkHttps) === false && strpos($HTMLContent, $ParamLinkHttps) === false,
            "$message Tracking $ParamLinkHttps"
        );
        $this->assertTrue(
            strpos($PlainContent, $NoTrackLinkHttp) !== false && strpos($HTMLContent, $NoTrackLinkHttp) !== false,
            "$message Not Tracking NoTrack($NoTrackLinkHttp)"
        );
        $this->assertTrue(
            strpos($PlainContent, $NoTrackLinkHttps) !== false && strpos($HTMLContent, $NoTrackLinkHttps) !== false,
            "$message Not Tracking NoTrack()"
        );
        $this->assertTrue(
            strpos($PlainContent, '%Tracking-track_link-0-6-6-link%') === false
            && strpos($HTMLContent, '%Tracking-track_link-0-6-6-link%') === false,
            "$message Tracking url custom field"
        );
        $this->assertTrue(
            strpos($PlainContent, '%Tracking-track_link-0-6-7-link%') === false
            && strpos($HTMLContent, '%Tracking-track_link-0-6-7-link%') === false,
            "$message Tracking url custom field"
        );

        $message = 'Personalization::TrackLink OFF:';

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['PlainContent']);
        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent'], $Params);
        $PlainContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['PlainContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature,
            1
        );
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature,
            1,
            false
        );

        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            $CampaignID,
            $ArraySubscriber,
            $ReferenceID,
            $IsPreview,
            $SenderDomain,
            [],
            $ListFormID,
            false,
            $ListID
        );

        $PlainContent = Personalization::PersonalizeReplace($PlainContent, $Replacements);
        $HTMLContent = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

        $this->assertTrue(
            strpos($PlainContent, $DomainLinkHttp) !== false && strpos($HTMLContent, $DomainLinkHttp) !== false,
            "$message Not Tracking $DomainLinkHttp"
        );
        $this->assertTrue(
            strpos($PlainContent, $DomainLinkHttps) !== false && strpos($HTMLContent, $DomainLinkHttps) !== false,
            "$message Not Tracking $DomainLinkHttps"
        );
        $this->assertTrue(
            strpos($PlainContent, $SimpleLinkHttp) !== false && strpos($HTMLContent, $SimpleLinkHttp) !== false,
            "$message Not Tracking $SimpleLinkHttp"
        );
        $this->assertTrue(
            strpos($PlainContent, $SimpleLinkHttps) !== false && strpos($HTMLContent, $SimpleLinkHttps) !== false,
            "$message Not Tracking $SimpleLinkHttps"
        );
        $this->assertTrue(
            strpos($PlainContent, $ParamLinkHttp) !== false && strpos($HTMLContent, $ParamLinkHttp) !== false,
            "$message Not Tracking $ParamLinkHttp"
        );
        $this->assertTrue(
            strpos($PlainContent, $ParamLinkHttps) !== false && strpos($HTMLContent, $ParamLinkHttps) !== false,
            "$message Not Tracking $ParamLinkHttps"
        );
        $this->assertTrue(
            strpos($PlainContent, $NoTrackLinkHttp) !== false && strpos($HTMLContent, $NoTrackLinkHttp) !== false,
            "$message Not Tracking NoTrack($NoTrackLinkHttp)"
        );
        $this->assertTrue(
            strpos($PlainContent, $NoTrackLinkHttps) !== false && strpos($HTMLContent, $NoTrackLinkHttps) !== false,
            "$message Not Tracking NoTrack()"
        );
        $this->assertTrue(
            strpos($PlainContent, '%Tracking-track_link-0-6-6-link%') === false
            && strpos($HTMLContent, '%Tracking-track_link-0-6-6-link%') === false,
            "$message Not Tracking url custom field"
        );
        $this->assertTrue(
            strpos($PlainContent, '%Tracking-track_link-0-6-7-link%') === false
            && strpos($HTMLContent, '%Tracking-track_link-0-6-7-link%') === false,
            "$message Not Tracking url custom field"
        );
        $this->assertTrue(
            strpos($PlainContent, 'UrlCustomFieldLink: This is a single-line string') !== false
            && strpos($HTMLContent, '"This is a single-line string">UrlCustomFieldLink') !== false,
            "$message Not Tracking url custom field"
        );
        $this->assertTrue(
            strpos($PlainContent, 'WebsiteCustomFieldLink: This is a single-line string') !== false
            && strpos($HTMLContent, '"This is a single-line string">WebsiteCustomFieldLink') !== false,
            "$message Not Tracking website custom field"
        );

        /*
         *  --- Countdown ---
         */

        $message = 'Personalization::Countdown: ';

        $ArrayUser = (array)user_load($UserID);
        $ArraySubscriber = Subscribers::SelectDummySubscriber($UserID);
        //Note: with $Ispreview == TRUE, clock image url will lead to a static S3 file
        $IsPreview = false;
        unset($Params['Preview']); //preview would lead to preview countdown image and target url
        $ListFormID = 4713; // just for coding links
        $ListID = 4714; // just for coding links

        $TargetURL = "https://www.example.com";
        $CountdownData = array(
            'RelOwnerUserID' => $UserID,
            'Template' => 'round_orange',
            'TerminationType' => ToolCountdown::TERMINATION_TYPE_FIXED,
            'TerminationDateTime' => strtotime("+1 day"),
            'TerminationCustomFieldID' => 0,
            'UseCustomExpiredImage' => 0,
            'CustomExpiredImage' => '',
            'TargetURL' => $TargetURL,
        );

        $CountdownData['Name'] = 'Countdown 1';
        $CountdownData['TargetURL'] = $TargetURL . '/1';
        $Countdown1ID = ToolCountdown::InsertDB($CountdownData);
        $this->assertTrue($Countdown1ID > 0, "$message Countdown 1 created");
        $ObjectCountDown1 = ToolCountdown::FromID($UserID, $Countdown1ID);

        $CountdownData['Name'] = 'Countdown 2';
        $CountdownData['TargetURL'] = $TargetURL . '/2';
        $Countdown2ID = ToolCountdown::InsertDB($CountdownData);
        $this->assertTrue($Countdown2ID > 0, "$message Countdown 2 created");
        $ObjectCountDown2 = ToolCountdown::FromID($UserID, $Countdown2ID);

        $CountdownData['Name'] = 'Countdown 3 Linked';
        $CountdownData['TargetURL'] = $TargetURL . '/3';
        $Countdown3ID = ToolCountdown::InsertDB($CountdownData);
        $this->assertTrue($Countdown3ID > 0, "$message Countdown 3 created");
        $ObjectCountDown3 = ToolCountdown::FromID($UserID, $Countdown3ID);

        $CountdownContentPlain = "
      %Link:ClockImage($Countdown1ID)%
      %Link:ClockImage($Countdown2ID)%

      %Link:ClockLinkedImage($Countdown3ID)%
    ";

        $CountdownContentHTML = "
      Countdown 1:<img src='%Link:ClockImage($Countdown1ID)%' />
      Countdown 2:<img src='%Link:ClockImage($Countdown2ID)%' />

      Countdown 3:<span data-clock='1'>%Link:ClockLinkedImage($Countdown3ID)%</span>
    ";

        // create a fresh email
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'Countdown',
            'ReportSpamEnabled' => 1,
            'Subject' => 'Countdown',
            'PlainContent' => $CountdownContentPlain,
            'HTMLContent' => $CountdownContentHTML,
        );
        $CountdownEmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($CountdownEmailID > 0, $message . ' create countdown email');

        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $CountdownEmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

        $CountdownSenderDomain = DomainSet::SelectValidSenderDomainById($UserID, $CountdownEmailID);

        // --- check system links (live emails)

        $EncryptParams = array(
            'UserID' => $UserID,
            'ToolID' => $Countdown1ID,
            'SubscriberID' => $ArraySubscriber['SubscriberID'],
            'CampaignID' => $CampaignID,
            'EmailID' => $CountdownEmailID,
            'ReferenceID' => $ReferenceID
        );
        $Countdown1ClockHash = Core::EncryptURL($EncryptParams, 'clock', $CountdownSenderDomain['appurl']);

        $EncryptParams['ToolID'] = $Countdown2ID;
        $Countdown2ClockHash = Core::EncryptURL($EncryptParams, 'clock', $CountdownSenderDomain['appurl']);

        $EncryptParams['ToolID'] = $Countdown3ID;
        $Countdown3ClockHash = Core::EncryptURL($EncryptParams, 'clock', $CountdownSenderDomain['appurl']);
        $Countdown3LinkHash = Core::EncryptURL($EncryptParams, 'info_url', $CountdownSenderDomain['appurl']);

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['PlainContent']);

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent'], $Params);
        $PlainContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['PlainContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );

        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            $CampaignID,
            $ArraySubscriber,
            $ReferenceID,
            $IsPreview,
            $CountdownSenderDomain,
            [],
            $ListFormID,
            false,
            $ListID
        );

        $PlainContent = Personalization::PersonalizeReplace($PlainContent, $Replacements);
        $HTMLContent = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

        //check countdown image in HTML content
        $this->assertTrue(strpos($HTMLContent, $Countdown1ClockHash) !== false, $message . ' HTML: Countdown 1 image');
        $this->assertTrue(strpos($HTMLContent, $Countdown2ClockHash) !== false, $message . ' HTML: Countdown 2 image');

        //check linked countdown image in HTML
        $expectedHTML = "<a href=\"$Countdown3LinkHash\"><img src=\"$Countdown3ClockHash\" /></a>";

        $this->assertTrue(
            strpos($HTMLContent, $expectedHTML) !== false,
            $message . ' HTML: Countdown 3 image and link found'
        );

        //check for countdown link in plain content
        $this->assertTrue(
            strpos($PlainContent, $Countdown3LinkHash) !== false,
            $message . ' Plain: Countdown 3 link found'
        );

        // --- check preview links (email preview -> preview countdown image, link == TargetURL)

        $IsPreview = true;
        $Params['Preview'] = 1;

        $preview_image = Settings::get('marketing_tools_emailcountdown_s3url') . "/TEMPLATE/1/TEMPLATE_120.gif";
        $Countdown1ClockHash = str_replace('TEMPLATE', $ObjectCountDown1->GetData('Template'), $preview_image);
        $Countdown2ClockHash = str_replace('TEMPLATE', $ObjectCountDown2->GetData('Template'), $preview_image);
        $Countdown3ClockHash = str_replace('TEMPLATE', $ObjectCountDown3->GetData('Template'), $preview_image);
        $Countdown3LinkHash = $ObjectCountDown3->GetData('TargetURL');

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['PlainContent']);

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent'], $Params);
        $PlainContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['PlainContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );

        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            $CampaignID,
            $ArraySubscriber,
            $ReferenceID,
            $IsPreview,
            $CountdownSenderDomain,
            [],
            $ListFormID,
            false,
            $ListID
        );

        $PlainContent = Personalization::PersonalizeReplace($PlainContent, $Replacements);
        $HTMLContent = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

        //check countdown image in HTML content
        $this->assertTrue(
            strpos($HTMLContent, $Countdown1ClockHash) !== false,
            $message . ' HTML: Countdown 1 preview image'
        );
        $this->assertTrue(
            strpos($HTMLContent, $Countdown2ClockHash) !== false,
            $message . ' HTML: Countdown 2 preview image'
        );

        //check linked countdown image in HTML
        $expectedHTML = "<a href=\"$Countdown3LinkHash\"><img src=\"$Countdown3ClockHash\" /></a>";

        $this->assertTrue(
            strpos($HTMLContent, $expectedHTML) !== false,
            $message . ' HTML: Countdown 3 preview image and preview link found'
        );

        //check for countdown link in plain content
        $this->assertTrue(
            strpos($PlainContent, $Countdown3LinkHash) !== false,
            $message . ' Plain: Countdown 3 preview link found'
        );

        /*
         *  --- Signature QR Code vCard ---
         */

        $message = 'Personalization::vCard ';

        $ArrayUser = (array)user_load($UserID);
        $IsPreview = false; // just for coding links
        unset($Params['Preview']); //preview links would lead to static S3 content
        $ListFormID = 4713; // just for coding links
        $ListID = 4714; // just for coding links

        $SignatureData = array(
            'RelOwnerUserID' => $UserID,
            'SignatureName' => 'Signature 1',
            'PlainSignatureText' => variable_get('klicktipp_plainsignature_default', ''),
            'HTMLSignatureText' => variable_get('klicktipp_htmlsignature_default', ''),
            'vCard' => array(
                'FirstName' => 'Max',
                'LastName' => 'Mustermann',
                'CompanyName' => 'Klick-Tipp Ltd.',
                'EmailAddress' => '<EMAIL>',
                'Phone' => '00491234567891',
                'CellPhone' => '00491234567891',
                'Street' => 'Musterstraße 12',
                'City' => 'Musterstadt',
                'State' => 'Musterstaat',
                'Zip' => 'Musterpostleitzahl',
                'Country' => 'Musterland',
                'Website' => 'https://www.klick-tipp.com',
            ),
        );
        $Signature1ID = Signatures::InsertDB($SignatureData);
        $this->assertTrue($Signature1ID > 0, "$message Signature 1 created");
        $ArraySignature1 = Signatures::RetrieveSignatureByID($UserID, $Signature1ID);

        $SignatureData['SignatureName'] = 'Signature 2';
        // unset so no vcard will be generated
        unset($SignatureData['vCard']);
        $Signature2ID = Signatures::InsertDB($SignatureData);
        $this->assertTrue($Signature2ID > 0, "$message Signature 2 created");

        $SignatureContentPlain = "
      %Link:vCardURL($Signature1ID)%
      %Link:vCard($Signature1ID)%
      %Link:vCardURL($Signature2ID)%
      %Link:vCard($Signature2ID)%
    ";

        $SignatureInro1 = t('You can download the !vcard or scan the following qr code.', array(
            '!vcard' => "<a href=\"%Link:vCardURL({$Signature1ID})%\">" . t('vCard') . "</a>",
        ));
        $SignatureInro2 = t('You can download the !vcard or scan the following qr code.', array(
            '!vcard' => "<a href=\"%Link:vCardURL({$Signature2ID})%\">" . t('vCard') . "</a>",
        ));

        $SignatureContentHTML = "
      $SignatureInro1
      .....
      vCard QRCode 1:<span data-qrcode='$Signature1ID'>%Link:vCard($Signature1ID)%</span>
      ----------
      $SignatureInro2
      .....
      vCard QRCode 2:<span data-qrcode='$Signature2ID'>%Link:vCard($Signature2ID)%</span>
    ";

        // create a fresh email
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'Signature vCard',
            'ReportSpamEnabled' => 1,
            'Subject' => 'Signature vCard',
            'PlainContent' => $SignatureContentPlain,
            'HTMLContent' => $SignatureContentHTML,
        );
        $SignatureEmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($SignatureEmailID > 0, $message . ' create signature email');

        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $SignatureEmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

        $VCardSenderDomain = DomainSet::SelectValidSenderDomainById($UserID, $SignatureEmailID);

        $EncryptParams = array(
            'UserID' => $UserID,
            'SignatureID' => $Signature1ID,
        );

        // --- check system links (live emails)

        $Signature1vCardHash = Core::EncryptURL($EncryptParams, 'signature_vcard', $VCardSenderDomain['appurl']);

        $EncryptParams['SignatureID'] = $Signature2ID;
        $Signature2vCardHash = Core::EncryptURL($EncryptParams, 'signature_vcard', $VCardSenderDomain['appurl']);

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['PlainContent']);

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent'], $Params);
        $PlainContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['PlainContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );

        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            $CampaignID,
            $ArraySubscriber,
            $ReferenceID,
            $IsPreview,
            $VCardSenderDomain,
            [],
            $ListFormID,
            false,
            $ListID
        );

        $PlainContent = Personalization::PersonalizeReplace($PlainContent, $Replacements);
        $HTMLContent = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

        //check for qrcode link in plain content
        $this->assertTrue(
            strpos($PlainContent, $Signature1vCardHash) !== false,
            $message . ' Plain: Signature 1 link found'
        );
        $this->assertTrue(
            strpos($PlainContent, $Signature2vCardHash) === false,
            $message . ' Plain: Signature 2 link not found'
        );

        //check qrcode image in HTML content
        $this->assertTrue(
            strpos(
                $HTMLContent,
                str_replace("%Link:vCardURL({$Signature1ID})%", $Signature1vCardHash, $SignatureInro1)
            ) !== false,
            $message . ' HTML: Signature 1 intro link'
        );
        $this->assertTrue(
            strpos(
                $HTMLContent,
                str_replace("%Link:vCardURL({$Signature2ID})%", $Signature2vCardHash, $SignatureInro2)
            ) === false,
            $message . ' HTML: Signature 2 intro link not found'
        );

        $ArraySignature1 = Signatures::RetrieveSignatureByID($UserID, $Signature1ID);
        $vCard = Signatures::GenerateVCard($ArraySignature1);
        $ExpectedSrc = "https://api.qrserver.com/v1/create-qr-code/?" . http_build_query(array(
                'size' => '200x200',
                'data' => $vCard,
            ));
        //check linked qrcode image in HTML
        $expectedHTML = "<a href=\"$Signature1vCardHash\"><img src=\"$ExpectedSrc\" width=\"200\" height=\"200\" /></a>";
        $this->assertTrue(
            strpos($HTMLContent, $expectedHTML) !== false,
            $message . ' HTML: Signature 1 image and link found'
        );

        // --- check d+d editor preview

        $EditorTags = Personalization::GetEditorTags($account, Emails::TYPE_AUTOMATIONEMAIL);
        $this->assertTrue(!empty($EditorTags['customaddons']), "$message GetEditorTags");
        $vcardtag = array_filter($EditorTags['customaddons'], function ($t) use ($Signature1ID) {
            return $t['id'] == $Signature1ID;
        });
        $vcardtag = reset($vcardtag);
        $this->assertTrue(
            $vcardtag['button_link'] == "%Link:vCardURL({$Signature1ID})%",
            "$message button_link " . htmlspecialchars(print_r($vcardtag, 1))
        );
        $this->assertTrue(strpos($vcardtag['html'], $ExpectedSrc) > 0, "$message card url " . print_r($ExpectedSrc, 1));

        // --- check preview links (email preview -> static content on S3)

        $IsPreview = true;
        $Params['Preview'] = 1;

        $EncryptParams = array(
            'UserID' => $UserID,
            'SignatureID' => $Signature1ID,
        );
        $Signature1vCardHash = Core::EncryptURL($EncryptParams, 'signature_vcard', $VCardSenderDomain['appurl'], true);
        $checkS3URL = klicktipp_get_assets_url(
            "static/email-preview/preview_link.html?type=signature_vcard&lang=$AccountLanguage"
        );
        $this->assertTrue(
            strpos($Signature1vCardHash, $checkS3URL) !== false,
            "$message Signature 1 hash contains S3 URL to static content"
        );

        $EncryptParams['SignatureID'] = $Signature2ID;
        $Signature2vCardHash = Core::EncryptURL($EncryptParams, 'signature_vcard', $VCardSenderDomain['appurl'], true);
        $this->assertTrue(
            strpos($Signature2vCardHash, $checkS3URL) !== false,
            "$message Signature 2 hash contains S3 URL to static content"
        );

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['PlainContent']);

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent'], $Params);
        $PlainContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['PlainContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );

        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            $CampaignID,
            $ArraySubscriber,
            $ReferenceID,
            $IsPreview,
            $VCardSenderDomain,
            [],
            $ListFormID,
            false,
            $ListID
        );

        $PlainContent = Personalization::PersonalizeReplace($PlainContent, $Replacements);
        $HTMLContent = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

        //check for qrcode link in plain content
        //Note: the preview links for both signatures are the same, so there should only be one
        //      there are 2 links (intro and image) in the content for each signature
        $previewLinkCount = substr_count($PlainContent, $Signature1vCardHash);
        $this->assertTrue(
            $previewLinkCount == 2,
            $message . " Plain: Signature 1 preview link found ($previewLinkCount)"
        );
        $this->assertFalse(
            $previewLinkCount > 2,
            $message . " Plain: Signature 2 preview link not found ($previewLinkCount)"
        );

        $ArraySignature1 = Signatures::RetrieveSignatureByID($UserID, $Signature1ID);
        $vCard = Signatures::GenerateVCard($ArraySignature1);
        $ExpectedSrc = "https://api.qrserver.com/v1/create-qr-code/?" . http_build_query(array(
                'size' => '200x200',
                'data' => $vCard,
            ));
        //check linked qrcode image in HTML
        $expectedHTML = "<a href=\"$Signature1vCardHash\"><img src=\"$ExpectedSrc\" width=\"200\" height=\"200\" /></a>";
        $this->assertTrue(
            strpos($HTMLContent, $expectedHTML) !== false,
            $message . ' HTML: Signature 1 image and preview link found'
        );

        /*
         *  --- FullContact ---
         */

        // test against new enrich api

        $message = 'Personalization::FullContact (enrich API)';

        $ArrayUser = (array)user_load_by_name(USERNAME_ENTERPRISE);
        $UserID = $ArrayUser['uid'];
        $IsPreview = true; // just for coding links
        $ListFormID = 4713; // just for coding links
        $ListID = 4714; // just for coding links

        // create subscriber
        $DefaultList = Lists::RetrieveList(array('*'), array(
            'RelOwnerUserID' => $UserID,
            'ListID' => 'default',
        ), true);
        $Parameters = array(
            'UserID' => $UserID,
            'EmailAddress' => "<EMAIL>",
            'ListInformation' => $DefaultList,
            'IPAddress' => '127.0.0.1',
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SendConfirmationEmail' => false,
            'TriggerAutoResponders' => true, // register into queue
        );
        $Result = Subscribers::Subscribe($Parameters);
        $this->assertTrue($Result[0], $message . ' add subscriber');
        $SubscriberID = $Result[1];
        $ArraySubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);

        $FullContactData = array(
            "fullName" => "Bill Gates",
            "ageRange" => "57-67",
            "gender" => "Male",
            "location" => "Seattle, Wa, United States Of America",
            "title" => "Co-chair",
            "organization" => "Bill & Melinda Gates Foundation",
            "twitter" => "https://twitter.com/BillGates",
            "linkedin" => "https://www.linkedin.com/in/williamhgates",
            "facebook" => "https://www.facebook.com/BillGates",
            "avatar" => "https://img.fullcontact.com/static/b763bd967fc5a8f0d914b3cd049e1581_99b54d444695ec334501ebbed0e25c7dc61110a2b5f5b095f35278012d3ae176",
            "website" => "http://www.thegatesnotes.com/",
            "details" => array(
                "name" => array(
                    "given" => "Bill",
                    "family" => "Gates",
                    "full" => "Bill Gates",
                ),
                "age" => array(
                    "range" => "55-64",
                    "value" => 62,
                ),
                "gender" => "Male",
                "demographics" => array(
                    "gender" => "Male",
                    "age" => array(
                        "range" => "55-64",
                        "value" => 62,
                    ),
                ),
                "profiles" => array(
                    "twitter" => array(
                        "url" => "https://twitter.com/BillGates",
                        "service" => "twitter",
                    ),
                    "facebook" => array(
                        "url" => "https://www.facebook.com/BillGates",
                        "service" => "facebook",
                    ),
                    "linkedin" => array(
                        "url" => "https://www.linkedin.com/in/williamhgates",
                        "service" => "linkedin",
                    ),
                ),
                "locations" => array(
                    0 => array(
                        "city" => "Seattle",
                        "region" => "Washington",
                        "regionCode" => "WA",
                        "country" => "United States",
                        "countryCode" => "US",
                        "formatted" => "Seattle, Wa, United States Of America",
                    ),
                ),
                "employment" => array(
                    0 => array(
                        "name" => "Bill & Melinda Gates Foundation",
                        "current" => 1,
                        "title" => "Co-chair",
                        "start" => array("year" => 2010, "month" => 11, "day" => 3),
                        // only on higher plans (Employment History)
                        "end" => array("year" => 2012, "month" => 6, "day" => 23),
                        // only on higher plans (Employment History)
                    ),
                ),
                "photos" => array(
                    0 => array(
                        "label" => "avatar",
                        "value" => "https://img.fullcontact.com/static/b763bd967fc5a8f0d914b3cd049e1581_99b54d444695ec334501ebbed0e25c7dc61110a2b5f5b095f35278012d3ae176",
                    ),
                ),
                "education" => array(
                    0 => array(
                        "name" => "Harvard University",
                        "end" => array(
                            "year" => 1975,
                        ),
                    ),
                    1 => array(
                        "name" => "Lakeside School, Seattle",
                    ),
                ),
                "urls" => array(
                    0 => array(
                        "value" => "http://www.thegatesnotes.com/",
                    ),
                ),
            ),
            "updated" => "2019-09-20",
        );

        $FullContactField = array(
            'data' => $FullContactData,
            'timestamp' => time(),
            'apiVersion' => 3,
        );

        // save full response in hidden global custom field FullContact
        $CustomFields['CustomFieldFullContact'] = drupal_json_encode($FullContactField);
        // update global custom fields
        Subscribers::UpdateSubscription($UserID, $ArraySubscriber['SubscriberID'], $CustomFields);

        // create a fresh email
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'FullContact',
            'ReportSpamEnabled' => 1,
            'Subject' => 'FullContact',
            'PlainContent' => '',
            'HTMLContent' => "FullContact: %Subscriber:FullContact%​",
        );
        $FullContactEmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($FullContactEmailID > 0, $message . ' create fullcontact email');

        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $FullContactEmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent']);
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );

        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            $CampaignID,
            $ArraySubscriber,
            $ReferenceID,
            $IsPreview,
            $SenderDomain,
            [],
            $ListFormID,
            false,
            $ListID
        );

        $HTMLContent = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

        //check fullcontact data in HTML
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['details']['photos'][0]['value']) !== false,
            $message . ' HTML: Image URL'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['details']['name']['family']) !== false,
            $message . ' HTML: lastname'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['details']['name']['given']) !== false,
            $message . ' HTML: firstname'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['details']['urls'][0]['value']) !== false,
            $message . ' HTML: website url'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['details']['profiles']['twitter']['service']) !== false,
            $message . ' HTML: twitter'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['details']['employment'][0]['name']) !== false,
            $message . ' HTML: employment'
        );
        $this->assertTrue(
            strpos($HTMLContent, (string)$FullContactData['details']['employment'][0]['start']['year']) !== false,
            $message . ' HTML: employment start year'
        );
        $this->assertTrue(
            strpos($HTMLContent, "(3.11.2010 - 23.6.2012)") !== false,
            $message . ' HTML: employment date'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['details']['locations'][0]['city']) !== false,
            $message . ' HTML: city'
        );
        // check function to format fullcontact data
        $expectedHTML = CustomFields::FormatFullContactData($FullContactField);
        $this->assertTrue(strpos($HTMLContent, $expectedHTML) !== false, $message . ' HTML: FullContact Data found');

        // old data structure (person v2 api)

        $message = 'Personalization::FullContact (old person v2 api)';

        $FullContactData = array(
            'likelihood' => 0.95,
            'photos' => array(
                0 => array(
                    'type' => 'google',
                    'typeId' => 'google',
                    'typeName' => 'Other',
                    'url' => 'https://d2ojpxxtu63wzl.cloudfront.net/static/96becec110491247c42377568a890410_6119e0903d1cdfd6eddebdaa4e4b9cab5ff1f0825c97220e154735636032c143',
                    'isPrimary' => true,
                ),
            ),
            'contactInfo' => array(
                'familyName' => 'Lorang',
                'givenName' => 'Bart',
                'websites' => array(
                    0 => array(
                        'url' => 'http://bartlorang.com',
                    ),
                ),
                'chats' => array(
                    0 => array(
                        'client' => 'skype',
                        'handle' => 'bart.lorang',
                    ),
                ),
            ),
            'organizations' => array(
                0 => array(
                    'isPrimary' => false,
                    'name' => 'Dimension Technology Solutions',
                    'startDate' => '2002-06',
                    'endDate' => '2006-06-12',
                    'title' => 'Chief Technology Officer',
                    'current' => false,
                ),
            ),
            'demographics' => array(
                'locationDeduced' => array(
                    'city' => array(
                        'deduced' => false,
                        'name' => 'Denver',
                    ),
                    'state' => array(
                        'deduced' => false,
                        'name' => 'Colorado',
                    ),
                    'country' => array(
                        'deduced' => false,
                        'name' => 'United States',
                        'code' => 'US',
                    ),
                    'likelihood' => 0,
                ),
                'age' => '39',
                'gender' => 'Male',
            ),
            'socialProfiles' => array(
                0 => array(
                    'type' => 'pinterest',
                    'typeId' => 'pinterest',
                    'typeName' => 'Pinterest',
                    'url' => 'http://www.pinterest.com/lorangb/',
                    'username' => 'lorangb',
                ),
            ),
        );

        $FullContactField = array(
            'data' => $FullContactData,
            'timestamp' => time(),
        );
        // save full response in hidden global custom field FullContact
        $CustomFields['CustomFieldFullContact'] = drupal_json_encode($FullContactField);
        // update global custom fields
        Subscribers::UpdateSubscription($UserID, $ArraySubscriber['SubscriberID'], $CustomFields);

        // create a fresh email
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'FullContact',
            'ReportSpamEnabled' => 1,
            'Subject' => 'FullContact',
            'PlainContent' => '',
            'HTMLContent' => "FullContact: %Subscriber:FullContact%​",
        );
        $FullContactEmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($FullContactEmailID > 0, $message . ' create fullcontact email');

        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $FullContactEmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

        $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent']);
        $HTMLContent = Personalization::PersonalizePrepareContent(
            $ArrayEmail['HTMLContent'],
            $Params,
            $ArrayRevision,
            $ArraySignature
        );

        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $ArrayUser,
            $ArrayEmail,
            $CampaignID,
            $ArraySubscriber,
            $ReferenceID,
            $IsPreview,
            $SenderDomain,
            [],
            $ListFormID,
            false,
            $ListID
        );

        $HTMLContent = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

        //check fullcontact data in HTML
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['photos'][0]['url']) !== false,
            $message . ' HTML: Image URL'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['contactInfo']['familyName']) !== false,
            $message . ' HTML: lastname'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['contactInfo']['givenName']) !== false,
            $message . ' HTML: firstname'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['contactInfo']['websites'][0]['url']) !== false,
            $message . ' HTML: website url'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['socialProfiles'][0]['typeId']) !== false,
            $message . ' HTML: pinterest'
        );
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['organizations'][0]['name']) !== false,
            $message . ' HTML: employment'
        );
        $this->assertTrue(strpos($HTMLContent, "(6.2002 - 12.6.2006)") !== false, $message . ' HTML: employment date');
        $this->assertTrue(
            strpos($HTMLContent, $FullContactData['demographics']['locationDeduced']['city']['name']) !== false,
            $message . ' HTML: city'
        );
        // check function to format fullcontact data
        $expectedHTML = CustomFields::FormatFullContactData($FullContactField);
        $this->assertTrue(strpos($HTMLContent, $expectedHTML) !== false, $message . ' HTML: FullContact Data found');

        // test conversion

        $message = 'Personalization::FullContact (conversion test)';

        $FullContactData = array(
            'likelihood' => 0.95,
            'photos' => array(
                0 => array(
                    'url' => 'https://image.url',
                ),
            ),
            'contactInfo' => array(
                'familyName' => 'Lorang',
                'givenName' => 'Bart',
                'websites' => array(
                    0 => array(
                        'url' => 'http://bartlorang.com/1',
                    ),
                    1 => array(
                        'url' => 'http://bartlorang.com/2',
                    ),
                ),
            ),
            'organizations' => array(
                0 => array(
                    'name' => 'Org 1',
                    'startDate' => '2002-06',
                    'endDate' => '2006-07-01',
                    'title' => 'CTO 1',
                ),
                1 => array(
                    'name' => 'Org 2',
                    'startDate' => '2019',
                    'title' => 'CTO 2',
                    'current' => false,
                ),
                2 => array(
                    'name' => 'Org 3',
                    'title' => 'CTO 3',
                    'current' => true,
                ),
            ),
            'demographics' => array(
                'locationDeduced' => array(
                    'city' => array(
                        'deduced' => false,
                        'name' => 'Denver',
                    ),
                    'state' => array(
                        'deduced' => false,
                        'name' => 'Colorado',
                    ),
                    'country' => array(
                        'deduced' => false,
                        'name' => 'United States',
                        'code' => 'US',
                    ),
                    'likelihood' => 0,
                ),
                'age' => '39',
                'gender' => 'Male',
            ),
            'socialProfiles' => array(
                0 => array(
                    'type' => 'pinterest',
                    'typeId' => 'pinterest',
                    'typeName' => 'Pinterest',
                    'url' => 'http://www.pinterest.com/lorangb/',
                    'username' => 'lorangb',
                ),
                1 => array(
                    'type' => 'twitter',
                    'typeId' => 'twitter',
                    'typeName' => 'Twitter',
                    'url' => 'http://www.twitter.com/lorangb/',
                    'username' => 'lorangb',
                ),
            ),
        );

        $FullContactField = array(
            'data' => $FullContactData,
            'timestamp' => time(),
        );

        // get the new structure
        $FullContactField = CustomFields::ConvertFullContactData($FullContactField);

        $ConvertedFullContactData = $FullContactField['data'];

        $this->assertTrue(
            is_array($ConvertedFullContactData) && !empty($ConvertedFullContactData),
            "$message conversion generated array"
        );


        // for fields to test see CustomFields::ConvertFullContactData()

        $this->assertTrue(
            $ConvertedFullContactData['details']['photos'][0]['value'] === $FullContactData['photos'][0]['url'],
            "$message correct photo url"
        );

        $this->assertTrue(
            $ConvertedFullContactData['details']['name']['given'] === $FullContactData['contactInfo']['givenName'],
            "$message correct first name"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['name']['family'] === $FullContactData['contactInfo']['familyName'],
            "$message correct family name"
        );

        $this->assertTrue(
            count($ConvertedFullContactData['details']['urls']) === count($FullContactData['contactInfo']['websites']),
            "$message correct amount of urls"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['urls'][0]['value'] === $FullContactData['contactInfo']['websites'][0]['url'],
            "$message correct url #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['urls'][1]['value'] === $FullContactData['contactInfo']['websites'][1]['url'],
            "$message correct url #2"
        );

        $this->assertTrue(
            $ConvertedFullContactData['details']['age']['value'] === $FullContactData['demographics']['age'],
            "$message correct age"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['gender'] === $FullContactData['demographics']['gender'],
            "$message correct gender"
        );

        $this->assertTrue(
            $ConvertedFullContactData['details']['locations'][0]['city'] === $FullContactData['demographics']['locationDeduced']['city']['name'],
            "$message correct city name"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['locations'][0]['region'] === $FullContactData['demographics']['locationDeduced']['state']['name'],
            "$message correct region/state name"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['locations'][0]['country'] === $FullContactData['demographics']['locationDeduced']['country']['name'],
            "$message correct country name"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['locations'][0]['countryCode'] === $FullContactData['demographics']['locationDeduced']['country']['code'],
            "$message correct country code"
        );

        $this->assertTrue(
            count($ConvertedFullContactData['details']['profiles']) === count($FullContactData['socialProfiles']),
            "$message correct amount of social profiles"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['profiles'][$FullContactData['socialProfiles'][0]['typeId']]['service'] === $FullContactData['socialProfiles'][0]['typeId'],
            "$message correct social profile name #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['profiles'][$FullContactData['socialProfiles'][0]['typeId']]['url'] === $FullContactData['socialProfiles'][0]['url'],
            "$message correct social profile url #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['profiles'][$FullContactData['socialProfiles'][1]['typeId']]['service'] === $FullContactData['socialProfiles'][1]['typeId'],
            "$message correct social profile name #2"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['profiles'][$FullContactData['socialProfiles'][1]['typeId']]['url'] === $FullContactData['socialProfiles'][1]['url'],
            "$message correct social profile url #2"
        );

        $this->assertTrue(
            count($ConvertedFullContactData['details']['employment']) === count($FullContactData['organizations']),
            "$message correct amount of employment stations / organizations"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][0]['name'] === $FullContactData['organizations'][0]['name'],
            "$message correct employment name #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][0]['title'] === $FullContactData['organizations'][0]['title'],
            "$message correct employment title #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][0]['current'] === $FullContactData['organizations'][0]['current'],
            "$message correct current employment setting  #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][0]['start']['year'] === '2002',
            "$message correct employment start year #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][0]['start']['month'] === '6',
            "$message correct employment start month #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][0]['end']['year'] === '2006',
            "$message correct employment end year #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][0]['end']['month'] === '7',
            "$message correct employment end month #1"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][0]['end']['day'] === '1',
            "$message correct employment end day #1"
        );

        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][1]['name'] === $FullContactData['organizations'][1]['name'],
            "$message correct employment name #2"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][1]['title'] === $FullContactData['organizations'][1]['title'],
            "$message correct employment title #2"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][1]['current'] === $FullContactData['organizations'][1]['current'],
            "$message correct current employment setting  #2"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][1]['start']['year'] === '2019',
            "$message correct employment start year #2"
        );
        $this->assertTrue(
            !isset($ConvertedFullContactData['details']['employment'][1]['end']),
            "$message no employment end date #2"
        );

        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][2]['name'] === $FullContactData['organizations'][2]['name'],
            "$message correct employment name #3"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][2]['title'] === $FullContactData['organizations'][2]['title'],
            "$message correct employment title #3"
        );
        $this->assertTrue(
            $ConvertedFullContactData['details']['employment'][2]['current'] === $FullContactData['organizations'][2]['current'],
            "$message correct current employment setting #3"
        );
        $this->assertTrue(
            !isset($ConvertedFullContactData['details']['employment'][2]['start']),
            "$message no employment start date #3"
        );
        $this->assertTrue(
            !isset($ConvertedFullContactData['details']['employment'][2]['end']),
            "$message no employment end date #3"
        );

        //TESTCASE: live against preview emails

        $message = "LiveVSPreviewLinks: ";

        $trackLink = "https://www.example.com";

        $plainContent = "
      TrackLink: $trackLink \n
      SubscriberInfo: %Link:SubscriberInfo% \n
      SubscriberUpdate: %Link:SubscriberUpdate% \n
      ChangeEmailAddress: %Link:ChangeEmailAddress% \n
      Unsubscribe: %Link:Unsubscribe% \n
      SpamReport: %Link:ReportSpam% \n
      Confirm: %Link:Confirm% \n
      WebBrowser: %Link:WebBrowser% \n
      Note: Countdowns and vCards have been tested seperately. \n
      Note: TrackOpen is added automatically. \n
    ";

        $htmlContent = "
      <a href=\"$trackLink\">TrackLink</a><br />
      <a href=\"%Link:SubscriberInfo%\">\"SubscriberInfo</a><br />
      <a href=\"%Link:SubscriberUpdate%\">SubscriberUpdate</a><br />
      <a href=\"%Link:ChangeEmailAddress%\">ChangeEmailAddress</a><br />
      <a href=\"%Link:Unsubscribe%\">Unsubscribe</a><br />
      <a href=\"%Link:ReportSpam%\">SpamReport</a><br />
      <a href=\"%Link:Confirm%\">Confirm</a><br />
      <a href=\"%Link:WebBrowser%\">WebBrowser</a><br />
      <p>Note: Countdowns and vCards have been tested seperately.</p>
      <p>Note: TrackOpen is added automatically.</p>
    ";

        // create a fresh email
        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'Live against preview',
            'ReportSpamEnabled' => 1,
            'Subject' => 'Live against preview',
            'PlainContent' => $plainContent,
            'HTMLContent' => $htmlContent,
        );
        $LivePreviewID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($LivePreviewID > 0, $message . ' create live/preview email');
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $LivePreviewID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

        $livePreviewDomain = DomainSet::SelectValidSenderDomainById($UserID, $LivePreviewID);
        $livePreviewAppURL = $livePreviewDomain['appurl'];

        $ArrayQueryParameters = [
            'UserID' => $UserID,
            'RevisionID' => $ArrayRevision['RevisionID'],
            'Linknumber' => 0,
            'CampaignID' => $CampaignID,
            'SubscriberID' => $ArraySubscription['SubscriberID'],
            'ListID' => $ListID,
            'ListFormID' => $ListFormID,
            'EmailID' => $ArrayEmail['EmailID'],
            'EmailAddress' => $ArraySubscription['EmailAddress'],
            //'Preview' => $IsPreview,
            'HTML' => '1',
        ];
        $encodedTrackLinkHTML = Core::EncryptURL($ArrayQueryParameters, "track_link", $livePreviewAppURL);
        unset($ArrayQueryParameters['HTML']);
        $ArrayQueryParameters['Linknumber'] = 2;
        $encodedTrackLinkPlain = Core::EncryptURL($ArrayQueryParameters, "track_link", $livePreviewAppURL);
        $previewTrackLink = $trackLink;

        $encodedSubscriberInfoLink = Core::EncryptURL($ArrayQueryParameters, "subscriber_info", $livePreviewAppURL);
        $previewSubscriberInfoLink = Core::EncryptURL(
            $ArrayQueryParameters,
            "subscriber_info",
            $livePreviewAppURL,
            true
        );
        $hardcodedSubscriberInfoLink = klicktipp_get_assets_url(
            "static/email-preview/preview_link.html?type=subscriber_info&lang=$AccountLanguage"
        );
        $this->assertTrue(
            strpos($hardcodedSubscriberInfoLink, $previewSubscriberInfoLink) !== false,
            "$message S3 subscriber_info link"
        );

        $encodedSubscriberUpdateLink = Core::EncryptURL($ArrayQueryParameters, "subscriber_update", $livePreviewAppURL);
        $previewSubscriberUpdateLink = Core::EncryptURL(
            $ArrayQueryParameters,
            "subscriber_update",
            $livePreviewAppURL,
            true
        );
        $hardcodedSubscriberUpdateLink = klicktipp_get_assets_url(
            "static/email-preview/preview_link.html?type=subscriber_update&lang=$AccountLanguage"
        );
        $this->assertTrue(
            strpos($hardcodedSubscriberUpdateLink, $previewSubscriberUpdateLink) !== false,
            "$message S3 subscriber_update link"
        );

        $encodedChangeEmailLink = Core::EncryptURL($ArrayQueryParameters, "change_email", $livePreviewAppURL);
        $previewChangeEmailLink = Core::EncryptURL($ArrayQueryParameters, "change_email", $livePreviewAppURL, true);
        $hardcodedChangeEmailLink = klicktipp_get_assets_url(
            "static/email-preview/preview_link.html?type=change_email&lang=$AccountLanguage"
        );
        $this->assertTrue(
            strpos($hardcodedChangeEmailLink, $previewChangeEmailLink) !== false,
            "$message S3 change_email link"
        );

        $encodedUnsubscribeLink = Core::EncryptURL($ArrayQueryParameters, "unsubscribe", $livePreviewAppURL);
        $previewUnsubscribeLink = Core::EncryptURL($ArrayQueryParameters, "unsubscribe", $livePreviewAppURL, true);
        $hardcodedUnsubscribeLink = klicktipp_get_assets_url(
            "static/email-preview/preview_link.html?type=unsubscribe&lang=$AccountLanguage"
        );
        $this->assertTrue(
            strpos($hardcodedUnsubscribeLink, $previewUnsubscribeLink) !== false,
            "$message S3 unsubscribe link"
        );

        $encodedSpamReportLink = Core::EncryptURL($ArrayQueryParameters, "spam_report", $livePreviewAppURL);
        $previewSpamReportLink = Core::EncryptURL($ArrayQueryParameters, "spam_report", $livePreviewAppURL, true);
        $hardcodedSpamReportLink = klicktipp_get_assets_url(
            "static/email-preview/preview_link.html?type=spam_report&lang=$AccountLanguage"
        );
        $this->assertTrue(
            strpos($hardcodedSpamReportLink, $previewSpamReportLink) !== false,
            "$message S3 spam_report link"
        );

        $encodedOptConfirmLink = Core::EncryptURL($ArrayQueryParameters, "opt_confirm", $livePreviewAppURL);
        $previewOptConfirmLink = Core::EncryptURL($ArrayQueryParameters, "opt_confirm", $livePreviewAppURL, true);
        $hardcodedOptConfirmLink = klicktipp_get_assets_url(
            "static/email-preview/preview_link.html?type=opt_confirm&lang=$AccountLanguage"
        );
        $this->assertTrue(
            strpos($hardcodedOptConfirmLink, $previewOptConfirmLink) !== false,
            "$message S3 opt_confirm link"
        );

        $encodedTrackOpenURL = Core::EncryptURL($ArrayQueryParameters, "track_open", $livePreviewAppURL);
        $previewTrackOpenURL = Core::EncryptURL($ArrayQueryParameters, "track_open", $livePreviewAppURL, true);
        $hardcodedTrackOpenURL = klicktipp_get_assets_url("static/email-preview/trans.gif");
        $this->assertTrue(strpos($hardcodedTrackOpenURL, $previewTrackOpenURL) !== false, "$message S3 track_open URL");

        $encodedWebBrowserLink = Core::EncryptURL($ArrayQueryParameters, "web_browser", $livePreviewAppURL);
        $ArrayQueryParameters['Preview'] = '1';
        $previewWebBrowserLink = Core::EncryptURL($ArrayQueryParameters, "web_browser", $livePreviewAppURL, true);


        // --- check live links
        $IsPreview = false;

        [
            $TMPSubject,
            $PlainContent,
            $HTMLContent,
            $ContentType
        ] = Personalization::PersonalizeEmail(
            $account,
            $CampaignID,
            $ArrayEmail,
            $ArraySignature,
            $ArraySubscription,
            $ReferenceID,
            $livePreviewDomain,
            [],
            false,
            $IsPreview,
            false,
            $ListFormID,
            $ListID
        );


        $this->assertTrue(
            strpos($PlainContent, $encodedTrackLinkPlain) !== false,
            "$message live PLAIN contains encoded tracklink"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewTrackLink) === false,
            "$message live PLAIN does not contain preview tracklink"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedTrackLinkHTML) !== false,
            "$message live HTML contains encoded tracklink"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewTrackLink) === false,
            "$message live HTML does not contain preview tracklink"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedSubscriberInfoLink) !== false,
            "$message live PLAIN contains encoded subscriber_info"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewSubscriberInfoLink) === false,
            "$message live PLAIN does not contain preview subscriber_info"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedSubscriberInfoLink) !== false,
            "$message live HTML contains encoded subscriber_info"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewSubscriberInfoLink) === false,
            "$message live HTML does not contain preview subscriber_info"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedSubscriberUpdateLink) !== false,
            "$message live PLAIN contains encoded subscriber_update"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewSubscriberUpdateLink) === false,
            "$message live PLAIN does not contain preview subscriber_update"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedSubscriberUpdateLink) !== false,
            "$message live HTML contains encoded subscriber_update"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewSubscriberUpdateLink) === false,
            "$message live HTML does not contain preview subscriber_update"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedChangeEmailLink) !== false,
            "$message live PLAIN contains encoded change_email"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewChangeEmailLink) === false,
            "$message live PLAIN does not contain preview change_email"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedChangeEmailLink) !== false,
            "$message live HTML contains encoded change_email"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewChangeEmailLink) === false,
            "$message live HTML does not contain preview change_email"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedUnsubscribeLink) !== false,
            "$message live PLAIN contains encoded unsubscribe"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewUnsubscribeLink) === false,
            "$message live PLAIN does not contain preview unsubscribe"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedUnsubscribeLink) !== false,
            "$message live HTML contains encoded unsubscribe"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewUnsubscribeLink) === false,
            "$message live HTML does not contain preview unsubscribe"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedSpamReportLink) !== false,
            "$message live PLAIN contains encoded spam_report"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewSpamReportLink) === false,
            "$message live PLAIN does not contain preview spam_report"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedSpamReportLink) !== false,
            "$message live HTML contains encoded spam_report"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewSpamReportLink) === false,
            "$message live HTML does not contain preview spam_report"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedOptConfirmLink) !== false,
            "$message live PLAIN contains encoded opt_confirm"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewOptConfirmLink) === false,
            "$message live PLAIN does not contain preview opt_confirm"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedOptConfirmLink) !== false,
            "$message live HTML contains encoded opt_confirm"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewOptConfirmLink) === false,
            "$message live HTML does not contain preview opt_confirm"
        );

        $this->assertTrue(
            strpos($HTMLContent, $encodedTrackOpenURL) !== false,
            "$message live HTML contains encoded track_open"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewTrackOpenURL) === false,
            "$message live HTML does not contain preview track_open"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedWebBrowserLink) !== false,
            "$message live PLAIN contains encoded browser_view"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedWebBrowserLink) !== false,
            "$message live HTML contains encoded browser_view"
        );

        // --- check preview links
        $IsPreview = true;

        [
            $TMPSubject,
            $PlainContent,
            $HTMLContent,
            $ContentType
        ] = Personalization::PersonalizeEmail(
            $account,
            $CampaignID,
            $ArrayEmail,
            $ArraySignature,
            $ArraySubscription,
            $ReferenceID,
            $livePreviewDomain,
            [],
            false,
            $IsPreview,
            false,
            $ListFormID,
            $ListID
        );


        $this->assertTrue(
            strpos($PlainContent, $encodedTrackLinkPlain) === false,
            "$message live PLAIN does not contain tracklink"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewTrackLink) !== false,
            "$message live PLAIN contains preview tracklink"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedTrackLinkHTML) === false,
            "$message live HTML does not contain tracklink"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewTrackLink) !== false,
            "$message live HTML contains preview tracklink"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedSubscriberInfoLink) === false,
            "$message live PLAIN does not contain subscriber_info"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewSubscriberInfoLink) !== false,
            "$message live PLAIN contains preview subscriber_info"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedSubscriberInfoLink) === false,
            "$message live HTML does not contain subscriber_info"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewSubscriberInfoLink) !== false,
            "$message live HTML contains preview subscriber_info"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedSubscriberUpdateLink) === false,
            "$message live PLAIN does not contain subscriber_update"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewSubscriberUpdateLink) !== false,
            "$message live PLAIN contains preview subscriber_update"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedSubscriberUpdateLink) === false,
            "$message live HTML does not contain subscriber_update"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewSubscriberUpdateLink) !== false,
            "$message live HTML contains preview subscriber_update"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedChangeEmailLink) === false,
            "$message live PLAIN does not contain change_email"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewChangeEmailLink) !== false,
            "$message live PLAIN contains preview change_email"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedChangeEmailLink) === false,
            "$message live HTML does not contain change_email"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewChangeEmailLink) !== false,
            "$message live HTML contains preview change_email"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedUnsubscribeLink) === false,
            "$message live PLAIN does not contain unsubscribe"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewUnsubscribeLink) !== false,
            "$message live PLAIN contains preview unsubscribe"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedUnsubscribeLink) === false,
            "$message live HTML does not contain unsubscribe"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewUnsubscribeLink) !== false,
            "$message live HTML contains preview unsubscribe"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedSpamReportLink) === false,
            "$message live PLAIN does not contain spam_report"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewSpamReportLink) !== false,
            "$message live PLAIN contains preview spam_report"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedSpamReportLink) === false,
            "$message live HTML does not contain spam_report"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewSpamReportLink) !== false,
            "$message live HTML contains preview spam_report"
        );

        $this->assertTrue(
            strpos($PlainContent, $encodedOptConfirmLink) === false,
            "$message live PLAIN does not contain opt_confirm"
        );
        $this->assertTrue(
            strpos($PlainContent, $previewOptConfirmLink) !== false,
            "$message live PLAIN contains preview opt_confirm"
        );
        $this->assertTrue(
            strpos($HTMLContent, $encodedOptConfirmLink) === false,
            "$message live HTML does not contain opt_confirm"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewOptConfirmLink) !== false,
            "$message live HTML contains preview opt_confirm"
        );

        $this->assertTrue(
            strpos($HTMLContent, $encodedTrackOpenURL) === false,
            "$message live HTML does not contain track_open"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewTrackOpenURL) !== false,
            "$message live HTML contains preview track_open"
        );

        $this->assertTrue(
            strpos($PlainContent, $previewWebBrowserLink) !== false,
            "$message live PLAIN contains encoded browser_view"
        );
        $this->assertTrue(
            strpos($HTMLContent, $previewWebBrowserLink) !== false,
            "$message live HTML contains encoded browser_view"
        );

        $message = 'Personalization with conditions';

        $MaleTagID = Tag::CreateManualTag($UserID, 'male', 'male');
        [$success, $MaleSubscriberID] = Subscribers::Subscribe([
            'UserID' => $UserID,
            'ReferenceID' => $ReferenceID,
            'EmailAddress' => '<EMAIL>',
            'IPAddress' => '127.0.0.1',
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'OptInSubscribeTo' => $MaleTagID
        ]);

        $FemaleTagID = Tag::CreateManualTag($UserID, 'female', 'female');
        [$success, $FemaleSubscriberID] = Subscribers::Subscribe([
            'UserID' => $UserID,
            'ReferenceID' => $ReferenceID,
            'EmailAddress' => '<EMAIL>',
            'IPAddress' => '127.0.0.1',
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'OptInSubscribeTo' => $FemaleTagID
        ]);

        $ArrayFieldAndValues = array(
            'RelUserID' => $UserID,
            'EmailName' => 'EmailWithConditions',
            'Subject' => 'Email with conditions',
            'PlainContent' => '',
            'HTMLContent' => '<!--DECISION_START(1)--><p>for all others</p><!--DECISION_END(1)-->'
                . '<!--DECISION_START(2)--><p>for men</p><!--DECISION_END(2)-->'
                . '<!--DECISION_START(3)--><p>for women</p><!--DECISION_END(3)-->',
            'JsonContent' => '{
        "kt-decisions": [
          {
            "id": "1",
            "name": "Condition 1",
            "decision": {
              "segmentsOpAND": true,
              "segments": [
                {
                  "conditionsOpAND": true,
                  "conditions": [
                    {
                      "type": "Tag",
                      "condition": "has-not",
                      "entity": ' . $MaleTagID . ',
                      "op": "has not tagging",
                      "timeframe": "anytime",
                      "value": 0,
                      "action": "received",
                      "field": ' . $MaleTagID . '
                    },
                    {
                      "type": "Tag",
                      "condition": "has-not",
                      "entity": ' . $FemaleTagID . ',
                      "op": "has not tagging",
                      "timeframe": "anytime",
                      "value": 0,
                      "action": "received",
                      "field": ' . $FemaleTagID . '
                    }
                  ]
                }
              ]
            }
          },
          {
            "id": "2",
            "name": "Condition 2",
            "decision": {
              "segmentsOpAND": true,
              "segments": [
                {
                  "conditionsOpAND": true,
                  "conditions": [
                    {
                      "type": "Tag",
                      "condition": "has",
                      "entity": ' . $MaleTagID . ',
                      "op": "has tagging",
                      "timeframe": "anytime",
                      "value": 0,
                      "action": "received",
                      "field": ' . $MaleTagID . '
                    }
                  ]
                }
              ]
            }
          },
          {
            "id": "3",
            "name": "Condition 3",
            "decision": {
              "segmentsOpAND": true,
              "segments": [
                {
                  "conditionsOpAND": true,
                  "conditions": [
                    {
                      "type": "Tag",
                      "condition": "has",
                      "entity": ' . $FemaleTagID . ',
                      "op": "has tagging",
                      "timeframe": "anytime",
                      "value": 0,
                      "action": "received",
                      "field": ' . $FemaleTagID . '
                    }
                  ]
                }
              ]
            }
          }
        ]
      }',
            'CCEmail' => '<EMAIL>',
            'BCCEmail' => '<EMAIL>',
            'Version' => 1,
            'TurnOffTrackLink' => 1
        );
        $ConditionalEmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
        $ArrayConditionalEmail = Emails::FromID($UserID, $ConditionalEmailID)->GetData();


        $ArrayMaleSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $MaleSubscriberID, $ReferenceID);
        [
            ,
            $PlainContent,
            $HTMLContent
        ] = Personalization::PersonalizeEmail(
            $account,
            $CampaignID,
            $ArrayConditionalEmail,
            $ArraySignature,
            $ArrayMaleSubscriber,
            $ReferenceID,
            $livePreviewDomain
        );
        // we use strtok to cut tracking pixels (don't matter for condition check)
        $this->assertEquals(
            strtok($HTMLContent, "\n"),
            '<!--DECISION_START(2)--><p>for men</p><!--DECISION_END(2)-->',
            'conditions for men resolved properly'
        );
        $this->assertEquals(strtok($PlainContent, "\n"), 'for men', 'plain text for men meets expectations');

        $ArrayFemaleSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $FemaleSubscriberID, $ReferenceID);
        [
            ,
            $PlainContent,
            $HTMLContent
        ] = Personalization::PersonalizeEmail(
            $account,
            $CampaignID,
            $ArrayConditionalEmail,
            $ArraySignature,
            $ArrayFemaleSubscriber,
            $ReferenceID,
            $livePreviewDomain
        );

        $this->assertEquals(
            strtok($HTMLContent, "\n"),
            '<!--DECISION_START(3)--><p>for women</p><!--DECISION_END(3)-->',
            'conditions for women  resolved properly'
        );
        $this->assertEquals(strtok($PlainContent, "\n"), 'for women', 'plain text for women meets expectations');

        [
            ,
            $PlainContent,
            $HTMLContent
        ] = Personalization::PersonalizeEmail(
            $account,
            $CampaignID,
            $ArrayConditionalEmail,
            $ArraySignature,
            $ArraySubscriber,
            $ReferenceID,
            $livePreviewDomain
        );
        $this->assertEquals(
            strtok($HTMLContent, "\n"),
            '<!--DECISION_START(1)--><p>for all others</p><!--DECISION_END(1)-->',
            'conditions for all others resolved properly'
        );
        $this->assertEquals(
            strtok($PlainContent, "\n"),
            'for all others',
            'plain text for all others meets expectations'
        );
    }

    /*
     * helper
     */
    private function linkNumberOfURLinRevision($ArrayContent, $Link)
    {
        foreach ($ArrayContent as $id => $ArrayLink) {
            if ($ArrayLink['Link'] == $Link) {
                return $id;
            }
        }
        return -1;
    }

    private function findURLinRevision($ArrayContent, $Link)
    {
        return $this->linkNumberOfURLinRevision($ArrayContent, $Link) >= 0;
    }
}

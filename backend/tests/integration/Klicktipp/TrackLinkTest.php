<?php

namespace App\Tests\Integration\Klicktipp;

use App\Controller\TrackingController;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\ErrorPage\ErrorPageManager;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\NewsletterQueue;
use App\Klicktipp\Personalization;
use App\Klicktipp\Signatures;
use App\Klicktipp\Statistics;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\TestData\TestUser;
use App\Klicktipp\TestData\TestVariables;
use App\Klicktipp\Tracking;
use App\Klicktipp\TransactionalQueue;
use App\Klicktipp\TransactionEmails;
use Doctrine\DBAL\Connection;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;

class TrackLinkTest extends KernelTestCase
{
    /**
     * @var Connection
     */
    private $connection;

    protected function setUp(): void
    {
        $GLOBALS['app'] = self::bootKernel();
        $this->connection = $this->getContainer()->get('database_connection');
    }

    public function tearDown(): void
    {
        $this->connection = null;
    }


    public function testDrupalSimpleTest()
    {

        /* test callback function */
        global $lastcontent, $lastlink, $lastFallbackLink, $lasterror;

        $trackLinkRedirectCallback = function ($LinkURL, $FallbackURL, $error = 0) {
            global $lastlink, $lasterror, $lastFallbackLink;

            $lastFallbackLink = $FallbackURL;

            $lasterror = $error;
            if (empty($LinkURL)) {
                $lastlink = APP_URL;
            }
            elseif (parse_url($LinkURL)) {
                $lastlink = $LinkURL;
            }
            else {
                $lastlink = APP_URL;
            }
        };

        $trackOpenRedirect = function ($error = 0) {
            global $lasterror;

            $lasterror = $error;
        };

        $testTrackViewRedirect = function ($show_content, $show_subject = '', $error = 0) {
            global $lastcontent, $lasterror;

            $lasterror = $error;
            $lastcontent = $show_content;
        };

        /* load libraries */
        Libraries::include('track_link.inc');
        Libraries::include('queue.inc', '/tests/includes');

        /*
         * prepare = create all possible link types
         */

        $message = 'prepare data';

        TestVariables::setDefaults();

        $testUser = new TestUser($this->connection);
        $testUser->getEnterpriseUser();


        // get user
        $UserID = 1;
        $ArrayUser = (array) user_load($UserID);

        $ReferenceID = 0;
        $SecondReferenceID = 1;

        // create custom field
        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'FieldName' => "url field",
            'FieldTypeEnum' => CustomFields::TYPE_URL,
        );
        $URLCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertTrue(!empty($URLCustomFieldID), $message . ' custom field url');

        $URLCustomFieldParameter = "%Subscriber:CustomField$URLCustomFieldID%";
        $URLCustomFieldValue = "http://url.zauberlist.com";

        $WebsiteCustomFieldParameter = "%Subscriber:CustomFieldWebsite%";
        $WebsiteCustomFieldValue = "http://www.klicktipp.com";

        $Param2 = '%Subscriber:EmailAddress%';
        $NONexistingCustomFieldParameter = "%Subscriber:CustomField99999%";

        $LinkNoTrack = 'http://www.zauberlist.com/html';

        // campaign

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $UserID,
            'CampaignName' => 'Test campaign 1',
            'CampaignStatusEnum' => Campaigns::STATUS_READY,
            'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
            'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_CAMPAIGN,
        );
        $CampaignID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
        $OpenTag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_OPENED, $CampaignID);
        $ClickTag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_CLICKED, $CampaignID);
        $ViewTag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_VIEWED, $CampaignID);
        $ConversionTag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_CONVERTED, $CampaignID);

        // list

        $ArrayFieldAndValues = array(
            'Name' => 'test single optin list',
            'RelOwnerUserID' => $UserID,
            'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
        );
        $ListID = Lists::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($ListID > 0, $message . ' create list');
        $ArrayList = Lists::RetrieveListByID($UserID, $ListID);

        // subscriber

        $SubscriberAddress = "<EMAIL>";
        $SecondSubscriberAddress = "<EMAIL>";
        $Parameters = array(
            'UserID' => $UserID,
            'ListInformation' => $ArrayList,
            'EmailAddress' => $SubscriberAddress,
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SendConfirmationEmail' => false,
            'TriggerAutoResponders' => false,
            'OtherFields' => array(
                "CustomField$URLCustomFieldID" => $URLCustomFieldValue,
                "CustomFieldWebsite" => $WebsiteCustomFieldValue,
            ),
        );
        $result = Subscribers::Subscribe($Parameters);
        $SubscriberID = $result[1];

        // subscribe second time with new subscription and reference
        $Parameters['SubscriberID'] = $SubscriberID;
        $Parameters['EmailAddress'] = $SecondSubscriberAddress;
        $Parameters['ReferenceID'] = $SecondReferenceID;

        Subscribers::Subscribe($Parameters);

        $ArraySubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
        $this->assertTrue(!empty($ArraySubscriber), $message . ' ArraySubscriber' . $SubscriberID);

        // payment

        $PaymentData = [
            //'PaymentID' => $PaymentID,
            //'BuildID' => $BuildID,
            'Payment' => array(
                'GrossAmount' => 11900,
                'NetAmount' => 10000,
                'TaxAmount' => 1900,
                'Currency' => 'EUR',
                //'Payout' => 0,
                //'PayoutCurrency' => 'EUR',
                'ReceiptID' => 'IPN4711RECEIPT',
                'CreatedOn' => strtotime('2018-01-01 01:01'),
                //'SubscriptionID' => '',
            ),
            'Amount' => 22222,
            'Currency' => 'EUR',
            //'ProductName' => $Name,
            //'ProductEditURL' => $EditURL,
            //'IPN' => $ipn_data,
        ];
        $HistoryID = Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_PAYMENT,
            $PaymentData, $PaymentData['CreatedOn']);
        $PaymentData['HistoryID'] = $HistoryID;

        // links

        $linksnotrack = array(
            'AffiliateURL' => '%User:AffiliateURL%',
            'NoTrack' => "%Link:NoTrack($LinkNoTrack)%",
            'Confirm' => '%Link:Confirm%',
            'WebBrowser' => '%Link:WebBrowser%',
            'Unsubscribe' => '%Link:Unsubscribe%',
            'TrackOpen' => '%Link:TrackOpen%',
            'ReportSpam' => '%Link:ReportSpam%',
        );
        $linkstotrack = array(
            // the keys are the results after personalization; the values are the link parameters before
            'http://www.example.com/campaignHTMLlink' => 'http://www.example.com/campaignHTMLlink', // substring of the following
            // url custom field value provided
            "http://www.example.com/campaignHTMLlink?url=" . urlencode($URLCustomFieldValue) . "&param2=" . urlencode($SubscriberAddress) => "http://www.example.com/campaignHTMLlink?url=$URLCustomFieldParameter&param2=$Param2",
            "http://www.example.com/campaignHTMLlink/" . rawurlencode($URLCustomFieldValue) . "/" . rawurlencode($SubscriberAddress) => "http://www.example.com/campaignHTMLlink/$URLCustomFieldParameter/$Param2",
            $URLCustomFieldValue => $URLCustomFieldParameter,
            "http://www.example.com/campaignHTMLlink?url=" . urlencode($WebsiteCustomFieldValue) . "&param2=" . urlencode($SubscriberAddress) => "http://www.example.com/campaignHTMLlink?url=$WebsiteCustomFieldParameter&param2=$Param2",
            "http://www.example.com/campaignHTMLlink/" . rawurlencode($WebsiteCustomFieldValue) . "/" . rawurlencode($SubscriberAddress) => "http://www.example.com/campaignHTMLlink/$WebsiteCustomFieldParameter/$Param2",
            $WebsiteCustomFieldValue => $WebsiteCustomFieldParameter,
            'http://www.example.com/nosubstring' => 'http://www.example.com/nosubstring',
            $LinkNoTrack => $LinkNoTrack,
            # some special link encoding
            "http://www.example.com/index.php?redirect=https://example.com/campagin?tu_em=" . urlencode($SubscriberAddress) . "&cl=0383036373136323131303&bm=100&bmcl=5373735313236323131303&cp_name=Autoresponder&ag_name=RaK&bk=01" =>
                "http://www.example.com/index.php?redirect=https://example.com/campagin?tu_em=%Subscriber:EmailAddress%&cl=0383036373136323131303&bm=100&bmcl=5373735313236323131303&cp_name=Autoresponder&ag_name=RaK&bk=01",
            "http://www.example.com/index.php?redirect=https%3A%2F%2Fexample.com%2Fcampagin%3Ftu_em%3D" . $SubscriberAddress. "%26cl%3D0383036373136323131303%26bm%3D100%26bmcl%3D5373735313236323131303%26cp_name%3DAutoresponder%26ag_name%3DRaK%26bk%3D01" =>
                "http://www.example.com/index.php?redirect=https%3A%2F%2Fexample.com%2Fcampagin%3Ftu_em%3D%Subscriber:EmailAddress%%26cl%3D0383036373136323131303%26bm%3D100%26bmcl%3D5373735313236323131303%26cp_name%3DAutoresponder%26ag_name%3DRaK%26bk%3D01",
        );
        $links = array_merge($linksnotrack, $linkstotrack);

        // email

        $ArrayCampaign = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
        $EmailID = $ArrayCampaign['RelEmailID'];
        $this->assertTrue($EmailID > 0, $message . ' emailid');

        $EmailContent = '<a href="' . implode('">text</a><a href="', $links) . '">text</a><b>%Invoice:ReceiptID%</b>';
        /** @var Emails $ObjectEmail */
        $ObjectEmail = Emails::FromID($UserID, $EmailID);
        $ArrayEmail = $ObjectEmail->GetData();
        $ArrayEmail['RelUserID'] = $UserID;
        $ArrayEmail['EmailName'] = 'Email';
        $ArrayEmail['Subject'] = 'no email without subject';
        $ArrayEmail['PlainContent'] = 'we dont care about plain here';
        $ArrayEmail['HTMLContent'] = $EmailContent;
        $ObjectEmail->UpdateDB($ArrayEmail);
        $this->assertTrue($result[0], $message . ' email update');
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
        $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

        // simulate sending
        $result = Campaigns::ProcessCampaign($UserID, $CampaignID);
        _klicktipp_test_single_queue_cron_run('create_queue');
        $this->assertTrue($result, $message . ' email in queue');
        $TransactionalEmail = kt_fetch_array(db_query("SELECT * FROM {".TransactionalQueue::TABLE_NAME."} WHERE ".
            " RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelAutoResponderID = :RelAutoResponderID", array(
            ':RelOwnerUserID' => $UserID,
            ':RelSubscriberID' => $SubscriberID,
            ':RelAutoResponderID' => $CampaignID,
        )));
        $this->assertTrue($TransactionalEmail['StatusEnum'] == TransactionEmails::STATUS_PENDING, $message . ' campaign sending'.print_r($TransactionalEmail,1));
        TransactionEmails::SendSucceeded($ArrayCampaign, $ArrayCampaign['RelEmailID'], $SubscriberID, $ReferenceID);

        // signature (simplify default signature)

        $ArraySignature = Signatures::RetrieveSignatureByName($UserID, "");
        $ArraySignature['HTMLSignatureText'] = '';
        $ArraySignature['PlainSignatureText'] = '';
        $SignatureUpdated = Signatures::UpdateDB($ArraySignature);
        $this->assertTrue($SignatureUpdated, $message . ' simplify default signature');
        $RevisionID = Signatures::CreateRevision($UserID, $ArraySignature['SignatureID']);
        $ArraySignature = Signatures::RetrieveSignatureByRevisionID($RevisionID);

        DomainSet::CreateWhitelabelDomain($UserID, '1.zauberlist.com');
        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, '1.zauberlist.com');
        $domainset['Data']['alias'] = 'klick';
        $domainset['Data']['defaultsender'] = true;
        $domainset['Status'] = DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED;
        $domainset['validFrom'] = strtotime('-1 days');
        DomainSet::UpdateWhitelabelDomain($domainset['Domain'], $domainset);

        $domainset = DomainSet::RetrieveWhitelabelDomain($UserID, $domainset['Domain']);
        $domainset = DomainSet::FillSenderParameters($domainset);
        $this->assertTrue($domainset['appurl'] === 'https://klick.1.zauberlist.com/', "$message set whitelabel domain: ".$domainset['appurl']);

        $SenderDomain = DomainSet::SelectValidSenderDomainById($UserID, $EmailID);

        // create tracking links

        [$TMPSubject, $TMPPlainBody, $TMPHTMLBody, $ContentType] = Personalization::PersonalizeEmail($ArrayUser, $CampaignID, $ArrayEmail, $ArraySignature, $ArraySubscriber, $ReferenceID, $SenderDomain, $PaymentData);

        $show_content = '<h3 style="max-width:900px; padding:0 10px;text-align: center;">' . t('Subject') . ':&nbsp;' . $TMPSubject . '</h3><div class="email-preview" style="max-width:900px;padding:10px;">' . $TMPHTMLBody . '</div>';

        $result = preg_match_all("/href=\"([^\r\n]*)\"/iU", $TMPHTMLBody, $matches);

        $this->assertGreaterThan(0, $result, $message . ' content' . print_r(array(
                $result,
                $matches,
                htmlspecialchars($TMPHTMLBody),
                $ArrayRevision,
            ), 1));
        $tracklinks = array();
        $trackviews = array();
        foreach ($matches[1] as $l) {
            if ($pos = strpos($l, '/info/')) {
                $tracklinks[] = substr($l, $pos + 6);
            }
            if ($pos = strpos($l, '/web/')) {
                $trackviews[] = substr($l, $pos + 5);
            }
        }
        $this->assertTrue(count($tracklinks) == count($linkstotrack), $message . ' count' . print_r($tracklinks, 1));
        $this->assertTrue(count($trackviews) == 1, $message . ' count' . print_r($trackviews, 1));

        $result = preg_match_all("/src=\"([^\r\n]*)\"/iU", $TMPHTMLBody, $matches);

        $this->assertGreaterThan(0, $result, $message . ' content' . print_r(array(
                $result,
                $matches,
                htmlspecialchars($TMPHTMLBody),
                $ArrayRevision,
            ), 1));
        $openlinks = array();
        foreach ($matches[1] as $l) {
            if ($pos = strpos($l, '/images/')) {
                $openlinks[] = substr($l, $pos + 8);
            }
        }
        $this->assertTrue(count($openlinks) == 1, $message . ' count' . print_r($openlinks, 1));

        /*
         * klicktipp_track_open
         */

        $message = 'klicktipp_track_open callback';

        klicktipp_track_open(null, $trackOpenRedirect);
        $this->assertTrue($lasterror > 0, $message . ' callback error' . $lasterror);

        $message = 'klicktipp_track_open';

        klicktipp_track_open($openlinks[0], $trackOpenRedirect);
        $this->assertTrue($lasterror == 0, $message . ' callback error');

        // check results (click)
        $CampaignOpened = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
        $this->assertTrue($CampaignOpened['UniqueOpens'] == 1, $message . ' UniqueOpens' . print_r($CampaignOpened, 1));
        $this->assertTrue($CampaignOpened['TotalOpens'] == 1, $message . ' TotalOpens');
        $this->assertTrue($CampaignOpened['UniqueClicks'] == 0, $message . ' UniqueClicks');
        $this->assertTrue($CampaignOpened['TotalClicks'] == 0, $message . ' TotalClicks');
        $this->assertTrue($CampaignOpened['UniqueViewsOnBrowser'] == 0, $message . ' UniqueViewsOnBrowser');
        $this->assertTrue($CampaignOpened['TotalViewsOnBrowser'] == 0, $message . ' TotalViewsOnBrowser');
        $this->assertTrue($CampaignOpened['UniqueConversions'] == 0, $message . ' UniqueConversions');
        $this->assertTrue($CampaignOpened['TotalConversions'] == 0, $message . ' TotalConversions');
        $result = Subscribers::RetrieveTagging($UserID, $OpenTag['TagID'], $SubscriberID, $ReferenceID);
        $this->assertTrue(!empty($result), $message . ' RetrieveTagging');

        /*
         * \App\Klicktipp\Tracking::trackLinkClick()
         */

        $message = Tracking::class .  '::trackLinkClick';

        // No need for $trackLinkRedirectCallback anymore since we're using the return values directly
        foreach ($tracklinks as $index => $l) {
            [$redirectUrl, $logInfo] = self::getContainer()->get(Tracking::class)->trackLinkClick($l);

            // Directly test the returned values
            $error = $logInfo['error'] ?? 0;
            $fallbackURL = $logInfo['FallbackURL'] ?? 'https://klick.1.zauberlist.com/';

            $this->assertTrue($error == 0, $message . ' error ' . $index . " for link $l");
            $this->assertTrue(!empty($redirectUrl) && parse_url($redirectUrl), $message . ' redirect URL is valid');
            $this->assertTrue($fallbackURL === 'https://klick.1.zauberlist.com/', $message . ' fallback link: ' . $fallbackURL);
        }

        // check results (click)
        $CampaignClicked = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
        $this->assertTrue($CampaignClicked['UniqueOpens'] == 1, $message . ' UniqueOpens' . print_r($CampaignClicked, 1));
        $this->assertTrue($CampaignClicked['TotalOpens'] == 1, $message . ' TotalOpens');
        $this->assertTrue($CampaignClicked['UniqueClicks'] == 1, $message . ' UniqueClicks');
        $this->assertTrue($CampaignClicked['TotalClicks'] == count($tracklinks), $message . ' TotalClicks');
        $this->assertTrue($CampaignClicked['UniqueViewsOnBrowser'] == 0, $message . ' UniqueViewsOnBrowser');
        $this->assertTrue($CampaignClicked['TotalViewsOnBrowser'] == 0, $message . ' TotalViewsOnBrowser');
        $this->assertTrue($CampaignClicked['UniqueConversions'] == 0, $message . ' UniqueConversions');
        $this->assertTrue($CampaignClicked['TotalConversions'] == 0, $message . ' TotalConversions');
        $result = Subscribers::RetrieveTagging($UserID, $ClickTag['TagID'], $SubscriberID, $ReferenceID);
        $this->assertTrue(!empty($result), $message . ' RetrieveTagging');

        /*
         * klicktipp_browser_view
         */

        $message = 'klicktipp_browser_view callback';

        klicktipp_browser_view(null, $trackLinkRedirectCallback);
        $this->assertTrue($lasterror > 0, $message . ' callback error' . $lasterror);

        $message = 'klicktipp_browser_view';

        klicktipp_browser_view($trackviews[0], $testTrackViewRedirect);
        $this->assertTrue($lasterror == 0, $message . ' callback error' . htmlspecialchars($show_content));
        $this->assertTrue(strlen($lastcontent) == strlen($show_content), $message . ' show_content length' . htmlspecialchars($lastcontent));
        // there are differences due to use of MinihashCreate in EncryptURL (e.g unsubscribe), but this should be about 20 chars of 1750 total email length
        $similar = similar_text($lastcontent, $show_content);
        // 12 should be the maximum difference here cause we have 3 links with each 4 chars which could differ (random chars)
        $this->assertTrue(strlen($lastcontent) - $similar < 12, $message . ' show_content similar ' . strlen($lastcontent) . ' ' . $similar);
        $this->assertTrue(strpos($lastcontent, $PaymentData['Payment']['ReceiptID']) > 0, $message . ' ReceiptID');

        // check results (click)
        $CampaignViewed = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
        $this->assertTrue($CampaignViewed['UniqueOpens'] == 1, $message . ' UniqueOpens' . print_r($CampaignViewed, 1));
        $this->assertTrue($CampaignViewed['TotalOpens'] == 1, $message . ' TotalOpens');
        $this->assertTrue($CampaignViewed['UniqueClicks'] == 1, $message . ' UniqueClicks');
        $this->assertTrue($CampaignViewed['TotalClicks'] == count($tracklinks), $message . ' TotalClicks');
        $this->assertTrue($CampaignViewed['UniqueViewsOnBrowser'] == 1, $message . ' UniqueViewsOnBrowser');
        $this->assertTrue($CampaignViewed['TotalViewsOnBrowser'] == 1, $message . ' TotalViewsOnBrowser');
        $this->assertTrue($CampaignViewed['UniqueConversions'] == 0, $message . ' UniqueConversions');
        $this->assertTrue($CampaignViewed['TotalConversions'] == 0, $message . ' TotalConversions');
        $result = Subscribers::RetrieveTagging($UserID, $ViewTag['TagID'], $SubscriberID, $ReferenceID);
        $this->assertTrue(!empty($result), $message . ' RetrieveTagging');

        /*
         * klicktipp_track_conversion
         */

        $message = 'klicktipp_track_conversion';

        $CP = array(
            'UserID' => $UserID,
            'CampaignID' => $CampaignID,
        );
        $parts = explode('/', Core::EncryptURL($CP, 'conversion', '').',02');

        // w/o cookie/ip set
        $cookieName = Statistics::COOKIENAME_SUBSCRIBER . Core::CryptNumber($UserID);
        unset($_SERVER['REMOTE_ADDR']);
        unset($_COOKIE[$cookieName]);
        klicktipp_track_conversion($parts[1], $parts[2], $trackOpenRedirect);
        $this->assertTrue($lasterror == Tracking::KLICKTIPP_TRACKING_ERROR_SUBSCRIBER, $message . ' callback error '.$lasterror);

        // with cookie set
        $_COOKIE[$cookieName] = Core::CryptNumber($SubscriberID);
        klicktipp_track_conversion($parts[1], $parts[2], $trackOpenRedirect);
        $this->assertTrue($lasterror == 0, $message . ' callback error '.$lasterror);

        // check results (click)
        $CampaignConversion = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
        $this->assertTrue($CampaignConversion['UniqueOpens'] == 1, $message . ' UniqueOpens' . print_r($CampaignConversion, 1));
        $this->assertTrue($CampaignConversion['TotalOpens'] == 1, $message . ' TotalOpens');
        $this->assertTrue($CampaignConversion['UniqueClicks'] == 1, $message . ' UniqueClicks');
        $this->assertTrue($CampaignConversion['TotalClicks'] == count($tracklinks), $message . ' TotalClicks');
        $this->assertTrue($CampaignConversion['UniqueViewsOnBrowser'] == 1, $message . ' UniqueViewsOnBrowser');
        $this->assertTrue($CampaignConversion['TotalViewsOnBrowser'] == 1, $message . ' TotalViewsOnBrowser');
        $this->assertTrue($CampaignConversion['UniqueConversions'] == 1, $message . ' UniqueConversions');
        $this->assertTrue($CampaignConversion['TotalConversions'] == 1, $message . ' TotalConversions');
        $result = Subscribers::RetrieveTagging($UserID, $ConversionTag['TagID'], $SubscriberID, $ReferenceID);
        $this->assertTrue(!empty($result), $message . ' RetrieveTagging');

        $result = Subscribers::RetrieveTagging($UserID, $ConversionTag['TagID'], $SubscriberID, $SecondReferenceID);
        $this->assertTrue(empty($result), $message . ' no tagging with not affected ReferenceID');

        TransactionEmails::SendSucceeded($ArrayCampaign, $ArrayCampaign['RelEmailID'], $SubscriberID, $ReferenceID);
        [,, $TMPHTMLBodyRef1,] = Personalization::PersonalizeEmail($ArrayUser, $CampaignID, $ArrayEmail, $ArraySignature, $ArraySubscriber, 1, $SenderDomain, $PaymentData);
        $openlinksRef1 = [];
        preg_match_all("/src=\"([^\r\n]*)\"/iU", $TMPHTMLBodyRef1, $matches);
        foreach ($matches[1] as $l) {
            if ($pos = strpos($l, '/images/')) {
                $openlinksRef1[] = substr($l, $pos + 8);
            }
        }
        klicktipp_track_open($openlinksRef1[0], $trackOpenRedirect);
        klicktipp_track_conversion($parts[1], $parts[2], $trackOpenRedirect);

        $result = Subscribers::RetrieveTagging($UserID, $ConversionTag['TagID'], $SubscriberID, $ReferenceID);
        $this->assertTrue(!empty($result), $message . ' tagging with all affected references');

        $ConversionAmount = db_query("SELECT ConversionAmount FROM {".NewsletterQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelAutoResponderID = :RelAutoResponderID", array(
            ':RelOwnerUserID' => $UserID,
            ':RelSubscriberID' => $SubscriberID,
            ':RelAutoResponderID' => $CampaignID
        ))->fetchField();
        $this->assertTrue($ConversionAmount == 102, "$message ConversionAmount $ConversionAmount");

        // deleted subscriber should still return whitelabel redirect url instead of fallback
        $encoded = substr(Core::EncryptURL([
                'RevisionID' => $ArrayRevision['RevisionID'],
                'Linknumber' => 1,
                'CampaignID' => $CampaignID,
                'SubscriberID' => 999999,
                'ReferenceID' => 0,
                'HTML' => 1,
                'Preview' => 0,
            ], 'track_link', ''), 5);
        /** @var TrackingController $errorPageManager */
        $controller = self::getContainer()->get(TrackingController::class);
        $response = $controller->trackLinkClick($encoded, new Request());
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertStringContainsString($LinkNoTrack, $response->getTargetUrl());
    }
}

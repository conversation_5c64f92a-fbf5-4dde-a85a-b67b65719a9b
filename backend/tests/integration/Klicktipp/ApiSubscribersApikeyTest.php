<?php

namespace App\Tests\Integration\Klicktipp;

use App\Klicktipp\APIKey;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Dates;
use App\Klicktipp\LegacyApi\LegacyApiSubscriber;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\ServicesException;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\SubscriptionFormsCustom;
use App\Klicktipp\SubscriptionFormsOptimizePress;
use App\Klicktipp\SubscriptionFormsRaw;
use App\Klicktipp\Tag;
use App\Klicktipp\TestData\TestUser;
use Doctrine\DBAL\Connection;
use Exception;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class ApiSubscribersApikeyTest extends WebTestCase
{
    private ?Connection $connection;

    private KernelBrowser $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = static::createClient();
        $GLOBALS['app'] = $this->getContainer()->get('kernel');
        /** @var Connection $connection */
        $connection = $this->getContainer()->get('database_connection');
        $this->connection = $connection;
    }

    public function tearDown(): void
    {
        $this->connection = null;
    }

    /**
     * @throws Exception
     */
    public function testDrupalSimpleTest(): void
    {
        $this->assertNotNull($this->connection);
        $testUser = new TestUser($this->connection);
        $testUser->getAnonymousUser();
        $UserID = $testUser->getEnterpriseUser();
        $testUser->getPremiumUser();
        $testUser->loginUser($this->client, $UserID);

        variable_set(Dates::FORMAT_DMY, 'd.m.Y');
        variable_set(Dates::FORMAT_HIS, 'H:i:s');
        variable_set(Dates::FORMAT_DMYHIS, 'd.m.Y H:i:s');

        $ReferenceID = 0;

        $message = 'prepare data';

        /**************
         * NOTE
         * This uses the partner connector to test api function directly through the connector.
         * The partner connector does not need a session, so we overcome the session problem
         * in test scripts (see klicktipp_api_connector.inc)
         */

        Libraries::include('api.inc', '/tests/includes');
        /** @phpstan-ignore-next-line */
        [$connector, $partner, $customer, $developer_key, $customer_key, $useragent] = prepare_api_connector_test(
            $this,
            'simpletest1234'
        );

        if (!$useragent) {
            // dont proceed without proper user agent
            return;
        }

        // set klicktipp default timezone
        Dates::setTimezone();

        // get user
        $account = user_load_by_name(USERNAME_PLUS);
        $UserID = $account->uid;
        $this->assertTrue(
            user_access('klicktipp api', $account),
            $message . ' to ' . USERNAME_PLUS
        );

        // create tags
        $TagName1 = 'some tag name';
        $TagID1 = Tag::CreateManualTag($UserID, $TagName1);
        $this->assertTrue($TagID1 > 0, $message . ' tag 1');
        $TagID2 = Tag::CreateManualTag($UserID, 'some tag name 2');
        $this->assertTrue($TagID2 > 0, $message . ' tag 2');

        // create list 1 - Double-Optin - w/o pending page
        $ListName1 = 'ListDOIWO';
        $ArrayFieldAndValues = [
            'Name' => $ListName1,
            'RelOwnerUserID' => $UserID,
        ];
        $ListDOIWO = Lists::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($ListDOIWO > 0, $message . ' list ListDOIWO');

        // create list 2 - Double-Optin - with pending page
        $ListName2 = 'ListDOIWITH';
        $ArrayFieldAndValues = [
            'Name' => $ListName2,
            'RelOwnerUserID' => $UserID,
            'SubscriptionConfirmedPageURL' => 'https://www.example.com/ListDOIWITH',
            'SubscriptionConfirmationPendingPageURL' => 'https://www.example.com/ListDOIWITH',
        ];
        $ListDOIWITH = Lists::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($ListDOIWITH > 0, $message . ' list ListDOIWITH');

        // create list 3 - Single-Optin - w/o confirmation page
        $ListName3 = 'ListSOIWO';
        $ArrayFieldAndValues = [
            'Name' => $ListName3,
            'RelOwnerUserID' => $UserID,
            'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
        ];
        $ListSOIWO = Lists::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($ListSOIWO > 0, $message . ' list ListSOIWO');

        // create list 4 - Single-Optin - with confirmation page
        $ListName4 = 'ListSOIWITH';
        $ArrayFieldAndValues = [
            'Name' => $ListName4,
            'RelOwnerUserID' => $UserID,
            'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
            'SubscriptionConfirmedPageURL' => 'https://www.example.com/ListSOIWITH',
            'SubscriptionConfirmationPendingPageURL' => 'https://www.example.com/ListSOIWITH',
        ];
        $ListSOIWITH = Lists::InsertDB($ArrayFieldAndValues);
        $this->assertTrue($ListSOIWITH > 0, $message . ' list ListSOIWITH');

        // create one custom field
        $UserCustomFieldName = 'user field';
        $ArrayFieldAndValues = [
            'RelOwnerUserID' => $UserID,
            'FieldName' => $UserCustomFieldName,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        ];
        $UserCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertIsNumeric($UserCustomFieldID, $message . ' user field');
        // this is the user defined field
        $UserCustomFieldInformation = CustomFields::RetrieveCustomField(
            $UserCustomFieldID,
            $UserID
        );
        $this->assertTrue(
            is_array($UserCustomFieldInformation),
            $message . ' user field exists'
        );
        // create second custom field
        $ArrayFieldAndValues = [
            'RelOwnerUserID' => $UserID,
            'FieldName' => 'html field',
            'FieldTypeEnum' => CustomFields::TYPE_HTML,
        ];
        $HTMLCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
        $this->assertIsNumeric($HTMLCustomFieldID, $message . ' html field');
        // this is the user defined field
        $HTMLCustomFieldInformation = CustomFields::RetrieveCustomField(
            $HTMLCustomFieldID,
            $UserID
        );
        $this->assertTrue(
            is_array($HTMLCustomFieldInformation),
            $message . ' html field exists'
        );
        // this is the global field
        $GlobalCustomFieldInformation = CustomFields::RetrieveCustomField(
            'FirstName'
        );
        $this->assertTrue(
            is_array($GlobalCustomFieldInformation),
            $message . ' global field exists'
        );

        $UserFieldValue = 'UserFieldValue';
        // phpcs:ignore Generic.Files.LineLength.TooLong
        $HTMLFieldValue = "<ul style=\"margin-top:1em; margin-bottom:1.5em;\"><li style=\"margin-bottom:0.3em;\"><span style=\"font-family:'Courier New',sans-serif;color:#142D68; font-size:1.072em;\">manual_tags</span> – assoziatives Array manuelle Tags <span style=\"font-family:'Courier New',sans-serif;color:#142D68; font-size:1.072em;\">[tag_id] =&gt; [timestamp]</span></li><li style=\"margin-bottom:0.3em;\"><span style=\"font-family:'Courier New',sans-serif;color:#142D68; font-size:1.072em;\">smart_links</span> – assoziatives Array SmartLinks <span style=\"font-family:'Courier New',sans-serif;color:#142D68; font-size:1.072em;\">[tag_id] =&gt; [timestamp]</span></li><li style=\"margin-bottom:0.3em;\"><span style=\"font-family:'Courier New',sans-serif;color:#142D68; font-size:1.072em;\">emails_sent</span> – assoziatives Array „Newsletter / Autoresponder erhalten“ <span style=\"font-family:'Courier New',sans-serif;color:#142D68; font-size:1.072em;\">[Newsletter / Autoresponder ID] =&gt; [timestamp]</span></li></ul>";
        $GlobalFieldValue = 'GlobalFieldValue';

        // Create API-Keys
        $BuildID = APIKey::InsertDB([
            'RelOwnerUserID' => $UserID,
            'Name' => 'APItest1',
            'RelListID' => $ListDOIWO,
        ]);
        $this->assertTrue($BuildID > 0, $message . ' build 1');
        $APIKeyObject1 = APIKey::FromID($UserID, $BuildID);
        $this->assertNotEmpty($APIKeyObject1, $message . ' object');
        $this->assertNotEmpty(
            $APIKeyObject1->GetData('SmartTagID'),
            $message . ' SmartTagID'
        );
        $APIKey1 = Core::EncryptURL(
            ['UserID' => $UserID, 'BuildID' => $BuildID],
            'api_key'
        );

        $BuildID = APIKey::InsertDB([
            'RelOwnerUserID' => $UserID,
            'Name' => 'APItest2',
            'RelListID' => $ListDOIWITH,
            'AssignTagID' => $TagID1,
        ]);
        $this->assertTrue($BuildID > 0, $message . ' build 2');
        $APIKey2 = Core::EncryptURL(
            ['UserID' => $UserID, 'BuildID' => $BuildID],
            'api_key'
        );

        $BuildID = APIKey::InsertDB([
            'RelOwnerUserID' => $UserID,
            'Name' => 'APItest3',
            'RelListID' => $ListSOIWO,
        ]);
        $this->assertTrue($BuildID > 0, $message . ' build 3');
        $APIKey3 = Core::EncryptURL(
            ['UserID' => $UserID, 'BuildID' => $BuildID],
            'api_key'
        );

        $BuildID = APIKey::InsertDB([
            'RelOwnerUserID' => $UserID,
            'Name' => 'APItest4',
            'RelListID' => $ListSOIWITH,
            'AssignTagID' => $TagID1,
        ]);
        $this->assertTrue($BuildID > 0, $message . ' build 4');
        $APIKey4 = Core::EncryptURL(
            ['UserID' => $UserID, 'BuildID' => $BuildID],
            'api_key'
        );

        $BuildID = APIKey::InsertDB([
            'RelOwnerUserID' => $UserID,
            'Name' => 'APItest5',
            'RelListID' => $ListSOIWITH,
            'AssignTagID' => $TagID2,
        ]);
        $this->assertTrue($BuildID > 0, $message . ' build 5');
        $APIKey5 = Core::EncryptURL(
            ['UserID' => $UserID, 'BuildID' => $BuildID],
            'api_key'
        );

        $BuildID = SubscriptionFormsOptimizePress::InsertDB([
            'RelOwnerUserID' => $UserID,
            'Name' => 'OptimizePressTest',
            'RelListID' => $ListDOIWO,
            'AssignTagID' => $TagID1,
        ]);
        $this->assertTrue($BuildID > 0, $message . ' build optimizepress');
        $APIKeyOptimizePress = Core::EncryptURL(
            ['UserID' => $UserID, 'BuildID' => $BuildID],
            'api_key'
        );
        $SubscriptionFormsOptimizePress = APIKey::FromID($UserID, $BuildID);
        $this->assertNotEmpty(
            $SubscriptionFormsOptimizePress,
            $message . ' optimizepress object'
        );
        $this->assertNotEmpty(
            $SubscriptionFormsOptimizePress->GetData('SmartTagID'),
            $message . ' optimizepress SmartTagID'
        );

        // Create a raw form filled with all possible date/time/datetime widgets

        $UserCustomFieldID_WIDGET_DATE_LOCALE = CustomFields::Create(
            [
                'RelOwnerUserID' => $UserID,
                'FieldName' => 'WIDGET_DATE_LOCALE',
                'FieldTypeEnum' => CustomFields::TYPE_DATE,
            ]
        );
        $UserCustomFieldID_WIDGET_TIME_LOCALE = CustomFields::Create(
            [
                'RelOwnerUserID' => $UserID,
                'FieldName' => 'WIDGET_TIME_LOCALE',
                'FieldTypeEnum' => CustomFields::TYPE_TIME,
            ]
        );
        $UserCustomFieldID_WIDGET_DATETIME_LOCALE = CustomFields::Create(
            [
                'RelOwnerUserID' => $UserID,
                'FieldName' => 'WIDGET_DATETIME_LOCALE',
                'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
            ]
        );
        $UserCustomFieldID_WIDGET_TIMESTAMP_DATE = CustomFields::Create(
            [
                'RelOwnerUserID' => $UserID,
                'FieldName' => 'WIDGET_TIMESTAMP_DATE',
                'FieldTypeEnum' => CustomFields::TYPE_DATE,
            ]
        );
        $UserCustomFieldID_WIDGET_TIMESTAMP_DATETIME = CustomFields::Create(
            [
                'RelOwnerUserID' => $UserID,
                'FieldName' => 'WIDGET_TIMESTAMP_DATETIME',
                'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
            ]
        );
        $UserCustomFieldID_WIDGET_STRTOTIME_DATE = CustomFields::Create(
            [
                'RelOwnerUserID' => $UserID,
                'FieldName' => 'WIDGET_STRTOTIME_DATE',
                'FieldTypeEnum' => CustomFields::TYPE_DATE,
            ]
        );
        $UserCustomFieldID_WIDGET_STRTOTIME_DATETIME = CustomFields::Create(
            [
                'RelOwnerUserID' => $UserID,
                'FieldName' => 'WIDGET_STRTOTIME_DATETIME',
                'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
            ]
        );

        $RawDataFields = [
            'RelOwnerUserID' => $UserID,
            'Name' => 'Rawtest',
            'FormFields' => [
                $UserCustomFieldID_WIDGET_DATE_LOCALE => ['widget' => CustomFields::WIDGET_DATE_LOCALE],
                $UserCustomFieldID_WIDGET_TIME_LOCALE => ['widget' => CustomFields::WIDGET_TIME_LOCALE],
                $UserCustomFieldID_WIDGET_DATETIME_LOCALE => ['widget' => CustomFields::WIDGET_DATETIME_LOCALE],
                $UserCustomFieldID_WIDGET_TIMESTAMP_DATE => ['widget' => CustomFields::WIDGET_TIMESTAMP],
                $UserCustomFieldID_WIDGET_TIMESTAMP_DATETIME => ['widget' => CustomFields::WIDGET_TIMESTAMP],
                $UserCustomFieldID_WIDGET_STRTOTIME_DATE => ['widget' => CustomFields::WIDGET_STRTOTIME],
                $UserCustomFieldID_WIDGET_STRTOTIME_DATETIME => ['widget' => CustomFields::WIDGET_STRTOTIME],
            ],
            'RelListID' => $ListDOIWO,
        ];

        $BuildID = SubscriptionFormsRaw::InsertDB($RawDataFields);
        $this->assertTrue($BuildID > 0, $message . ' build raw');
        $APIKeyRaw = Core::EncryptURL(
            ['UserID' => $UserID, 'BuildID' => $BuildID],
            'api_key'
        );
        $SubscriptionFormsRaw = APIKey::FromID($UserID, $BuildID);
        $this->assertNotEmpty(
            $SubscriptionFormsRaw,
            $message . ' raw object'
        );
        $this->assertNotEmpty(
            $SubscriptionFormsRaw->GetData('SmartTagID'),
            $message . ' raw SmartTagID'
        );

        $BuildID = SubscriptionFormsCustom::InsertDB([
            'RelOwnerUserID' => $UserID,
            'Name' => 'CustomFormTest',
            'RelListID' => $ListDOIWO,
            'AssignTagID' => $TagID1,
        ]);
        $this->assertTrue($BuildID > 0, $message . ' build custom form');
        $APIKeyCustomForm = Core::EncryptURL(
            ['UserID' => $UserID, 'BuildID' => $BuildID],
            'api_key'
        );
        $SubscriptionFormsCustom = APIKey::FromID($UserID, $BuildID);
        $this->assertNotEmpty(
            $SubscriptionFormsCustom,
            $message . ' custom form object'
        );
        $this->assertNotEmpty(
            $SubscriptionFormsCustom->GetData('SmartTagID'),
            $message . ' custom form SmartTagID'
        );

        /*
         * _klicktippapi_resource_subscriber_subscribe_withkey
         */
        $message = 'LegacyApiSubscriber::subscribeWithKey';

        $email = '<EMAIL>';
        $fields = [
            'field' . $UserCustomFieldID => $UserFieldValue,
            'field' . $HTMLCustomFieldID => $HTMLFieldValue,
            'fieldFirstName' => $GlobalFieldValue,
        ];
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKey1,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'pending') > 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $SubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID
        );
        $this->assertTrue(
            $SubscriberInformation['EmailAddress'] == $email,
            $message . ' email'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $UserCustomFieldID] == $UserFieldValue,
            $message . ' field'
        );
        // check if html is stored as base64 encoding
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $HTMLCustomFieldID] == $HTMLFieldValue,
            $message . ' html field'
        );
        $this->assertTrue(
            $SubscriberInformation['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
            $message . ' status' . print_r($SubscriberInformation, true)
        );
        $Taggings = Subscribers::RetrieveTaggingsOfSubscriber(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID,
            true
        );
        $this->assertTrue(!in_array($TagID1, $Taggings), $message . ' tagging');
        $this->assertTrue(
            in_array($APIKeyObject1->GetData('SmartTagID'), $Taggings),
            $message . ' smart tagging'
        );

        //check pending page
        $param_hash = explode('pending/', $RedirectURL);
        $param_decoded = Core::DecryptURL($param_hash[1]);
        $this->assertTrue(
            $param_decoded['lid'] == $ListDOIWO,
            "$message $email redirect ListID"
        );
        $this->assertTrue(
            $param_decoded['sid'] == $Subscriber['SubscriberID'],
            "$message $email redirect SubscriberID"
        );
        $this->assertTrue(
            $param_decoded['uid'] == $UserID,
            "$message $email redirect UserID"
        );

        $email = '<EMAIL>';
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKey2,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'ListDOIWITH') > 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $SubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID
        );
        $this->assertTrue(
            $SubscriberInformation['EmailAddress'] == $email,
            $message . ' email'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $UserCustomFieldID] == $UserFieldValue,
            $message . ' field'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $HTMLCustomFieldID] == $HTMLFieldValue,
            $message . ' html field'
        );
        $this->assertTrue(
            $SubscriberInformation['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
            $message . ' status'
        );
        $result = db_query(
            "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID"
            . " AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $Subscriber['SubscriberID'],
                ':RelTagID' => $TagID1,
            ]
        )->fetchField();
        $this->assertTrue($result == 1, $message . ' tagging');

        $email = '<EMAIL>';
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKey3,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'thankyou') > 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $SubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID
        );
        $this->assertTrue(
            $SubscriberInformation['EmailAddress'] == $email,
            $message . ' email'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $UserCustomFieldID] == $UserFieldValue,
            $message . ' field'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $HTMLCustomFieldID] == $HTMLFieldValue,
            $message . ' html field'
        );
        $this->assertTrue(
            $SubscriberInformation['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            $message . ' status'
        );
        $result = db_query(
            "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID"
            . " AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $Subscriber['SubscriberID'],
                ':RelTagID' => $TagID1,
            ]
        )->fetchField();
        $this->assertTrue($result == 0, $message . ' tagging');
        //check thankyou page
        $param_hash = explode('thankyou/', $RedirectURL);
        $param_decoded = Core::DecryptURL($param_hash[1]);
        $this->assertTrue(
            $param_decoded['lid'] == $ListSOIWO,
            "$message $email redirect ListID"
        );
        $this->assertTrue(
            $param_decoded['sid'] == $Subscriber['SubscriberID'],
            "$message $email redirect SubscriberID"
        );
        $this->assertTrue(
            $param_decoded['uid'] == $UserID,
            "$message $email redirect UserID"
        );

        $email = '<EMAIL>';
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKey4,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'ListSOIWITH') > 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $SubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID
        );
        $this->assertTrue(
            $SubscriberInformation['EmailAddress'] == $email,
            $message . ' email'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $UserCustomFieldID] == $UserFieldValue,
            $message . ' field'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $HTMLCustomFieldID] == $HTMLFieldValue,
            $message . ' html field'
        );
        $this->assertTrue(
            $SubscriberInformation['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            $message . ' status'
        );
        $result = db_query(
            "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID"
            . " AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $Subscriber['SubscriberID'],
                ':RelTagID' => $TagID1,
            ]
        )->fetchField();
        $this->assertTrue($result == 1, $message . ' tagging');

        // check behaviour for OptimizePress form if spam protection is not activated
        $ArrayQueryParameters = Core::DecryptURL($APIKeyOptimizePress);
        $SubscriptionFormsOptimizePress = Listbuildings::FromID(
            $ArrayQueryParameters['UserID'],
            $ArrayQueryParameters['BuildID']
        );

        $this->assertIsNotBool($SubscriptionFormsOptimizePress, $message . ' object');
        $Data = $SubscriptionFormsOptimizePress->GetData();
        $Data['Settings']['UseSpamProtection'] = 0;
        $SubscriptionFormsOptimizePress->UpdateDB($Data);

        $email = '<EMAIL>';
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKeyOptimizePress,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'pending') > 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $SubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID
        );
        $this->assertTrue(
            Subscribers::IsSameEmailAddress(
                $SubscriberInformation['EmailAddress'],
                $email
            ),
            $message . ' email'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $UserCustomFieldID] == $UserFieldValue,
            $message . ' field'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $HTMLCustomFieldID] == $HTMLFieldValue,
            $message . ' html field'
        );
        $this->assertTrue(
            $SubscriberInformation['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
            $message . ' status'
        );
        $result = db_query(
            "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID"
            . " AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $Subscriber['SubscriberID'],
                ':RelTagID' => $TagID1,
            ]
        )->fetchField();
        $this->assertTrue($result == 1, $message . ' tagging');

        $Data['Settings']['UseSpamProtection'] = 1;
        $SubscriptionFormsOptimizePress->UpdateDB($Data);

        // check behaviour if spam protection is activated
        $email = '<EMAIL>';
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKeyOptimizePress,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'http') !== 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $this->assertTrue(
            !$Subscriber,
            $message . ' subscriber not subscribed'
        );

        $fields['fieldHomepage'] = 'something';
        $email = '<EMAIL>';
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKeyOptimizePress,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'http') !== 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $this->assertTrue(
            !$Subscriber,
            $message . ' subscriber not subscribed'
        );

        // FROM HERE ON CAPTCHA IS DEACTIVATED TO TEST BEHAVIOUS WHEN CAPTCHA WAS VALID
        // @see LegacyApiSubscriber::subscribeWithKey
        variable_set('activate_captcha_for_subscription_forms', 0);
        variable_set('e2eUnitTest', true);

        // check behaviour if spam protection is activated and subscriber is human
        $fields['fieldHomepage'] = 'klicktipp.com';
        $email = '<EMAIL>';
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKeyOptimizePress,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'pending') > 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $SubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID
        );
        $this->assertTrue(
            Subscribers::IsSameEmailAddress(
                $SubscriberInformation['EmailAddress'],
                $email
            ),
            $message . ' email'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $UserCustomFieldID] == $UserFieldValue,
            $message . ' field'
        );
        $this->assertTrue(
            $SubscriberInformation['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
            $message . ' status'
        );
        $result = db_query(
            "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID"
            . " AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $Subscriber['SubscriberID'],
                ':RelTagID' => $TagID1,
            ]
        )->fetchField();
        $this->assertTrue($result == 1, $message . ' tagging');

        // check if dsgvo checkbox is not activated, the subscription works as expected
        $email = '<EMAIL>';
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKeyCustomForm,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'pending') > 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $SubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID
        );
        $this->assertTrue(
            Subscribers::IsSameEmailAddress(
                $SubscriberInformation['EmailAddress'],
                $email
            ),
            $message . ' email'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $UserCustomFieldID] == $UserFieldValue,
            $message . ' field'
        );
        $this->assertTrue(
            $SubscriberInformation['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
            $message . ' status'
        );
        $result = db_query(
            "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID"
            . " AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $Subscriber['SubscriberID'],
                ':RelTagID' => $TagID1,
            ]
        )->fetchField();
        $this->assertTrue($result == 1, $message . ' tagging');

        // check behaviour if dsgvo checkbox is activated but not checked
        $ArrayQueryParameters = Core::DecryptURL($APIKeyCustomForm);
        $SubscriptionFormsCustom = Listbuildings::FromID(
            $ArrayQueryParameters['UserID'],
            $ArrayQueryParameters['BuildID']
        );
        $this->assertIsNotBool($SubscriptionFormsCustom, $message . ' object');
        $Data = $SubscriptionFormsCustom->GetData();
        $Data['Settings']['DisplayDSGVOCheckbox'] = 1;
        $SubscriptionFormsCustom->UpdateDB($Data);

        $email = '<EMAIL>';
        try {
            LegacyApiSubscriber::subscribeWithKey(
                $APIKeyCustomForm,
                $email,
                '',
                $fields
            );
            $this->fail($message . ' subscriber not subscribed');
        } catch (ServicesException $e) {
            $this->assertSame('subscription failed', $e->getMessage());
        }
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $this->assertTrue(
            !$Subscriber,
            $message . ' subscriber not subscribed'
        );

        // check behaviour if dsgvo checkbox is activated and subscriber is human
        $fields['DSGVOCheckbox'] = 'on';
        $email = '<EMAIL>';
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey(
            $APIKeyCustomForm,
            $email,
            '',
            $fields
        );
        $this->assertTrue(
            strpos($RedirectURL, 'pending') > 0,
            "$message $email redirect"
        );
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $SubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID
        );
        $this->assertTrue(
            Subscribers::IsSameEmailAddress(
                $SubscriberInformation['EmailAddress'],
                $email
            ),
            $message . ' email'
        );
        $this->assertTrue(
            $SubscriberInformation['CustomField' . $UserCustomFieldID] == $UserFieldValue,
            $message . ' field'
        );
        $this->assertTrue(
            $SubscriberInformation['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
            $message . ' status'
        );
        $result = db_query(
            "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID"
            . " AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $Subscriber['SubscriberID'],
                ':RelTagID' => $TagID1,
            ]
        )->fetchField();
        $this->assertTrue($result == 1, $message . ' tagging');

        $email = '<EMAIL>';
        $testdatetime = strtotime('00:57'); //time();
        $datetimetestdata = [
            [
                $UserCustomFieldID, // field id w/o 'field'
                $UserFieldValue, // input value
                $UserFieldValue, // expected output value
            ],
            [
                $UserCustomFieldID_WIDGET_DATE_LOCALE,
                Dates::formatDate(Dates::FORMAT_DMY, $testdatetime),
                strtotime(date('d.m.Y', $testdatetime)),
            ],
            [
                $UserCustomFieldID_WIDGET_TIME_LOCALE,
                Dates::formatDate(Dates::FORMAT_HIS, $testdatetime),
                date('H', $testdatetime) * 3600 + date(
                    'i',
                    $testdatetime
                ) * 60 + date('s', $testdatetime),
            ],
            [
                $UserCustomFieldID_WIDGET_TIME_LOCALE,
                '01:01:00',
                3660,
            ],
            [
                $UserCustomFieldID_WIDGET_TIME_LOCALE,
                '1:01am',
                3660,
            ],
            [
                $UserCustomFieldID_WIDGET_TIME_LOCALE,
                '12:00:00',
                43200,
            ],
            [
                $UserCustomFieldID_WIDGET_DATETIME_LOCALE,
                Dates::formatDate(Dates::FORMAT_DMYHIS, $testdatetime),
                $testdatetime,
            ],
            [
                $UserCustomFieldID_WIDGET_TIMESTAMP_DATE,
                $testdatetime,
                $testdatetime,
            ],
            [
                $UserCustomFieldID_WIDGET_TIMESTAMP_DATETIME,
                $testdatetime,
                $testdatetime,
            ],
            [
                $UserCustomFieldID_WIDGET_STRTOTIME_DATE,
                date('r', $testdatetime),
                $testdatetime,
            ],
            [
                $UserCustomFieldID_WIDGET_STRTOTIME_DATETIME,
                date('r', $testdatetime),
                $testdatetime,
            ],
        ];
        $ArrayCustomFields = CustomFields::RetrieveCustomFields($UserID);
        $ArrayCustomFieldsByID = [];
        foreach ($ArrayCustomFields as $cf) {
            $ArrayCustomFieldsByID[$cf['CustomFieldID']] = $cf;
        }

        foreach ($datetimetestdata as $dt) {
            $fields = [
                'field' . $dt[0] => $dt[1],
                'fieldHomepage' => 'klicktipp.com',
            ];
            LegacyApiSubscriber::subscribeWithKey(
                $APIKeyRaw,
                $email,
                '',
                $fields
            );
            $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
                $UserID,
                $email
            );
            $SubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields(
                $UserID,
                $Subscriber['SubscriberID'],
                $ReferenceID
            );
            $this->assertTrue(
                $SubscriberInformation['CustomField' . $dt[0]] == $dt[2],
                $message . ' field ' . $dt[0]
                    . ' (' . $ArrayCustomFieldsByID[$dt[0]]['FieldName'] . '): '
                    . $SubscriberInformation['CustomField' . $dt[0]] . ' == ' . $dt[2]
            );
        }
        $Taggings = Subscribers::RetrieveTaggingsOfSubscriber(
            $UserID,
            $Subscriber['SubscriberID'],
            $ReferenceID,
            true
        );
        $this->assertTrue(
            in_array($SubscriptionFormsRaw->GetData('SmartTagID'), $Taggings),
            $message . ' smart tagging raw'
        );

        //  ---- Test Form Field constraints: forbidden urls and max length

        $maxLength = 40;

        $formData = [
            'RelOwnerUserID' => $UserID,
            'Name' => 'CustomFormTest',
            'RelListID' => $ListDOIWO,
            'AssignTagID' => $TagID1,
            'BuildType' => Listbuildings::TYPE_FORM_CUSTOM,
            'FormFields' => [
                'FirstName' => [
                    'label' => 'Firstname',
                    'type' => 1,
                    'default_value' => '',
                    'max_length' => $maxLength,
                    'allow_urls' => 0,
                ],
            ],
        ];

        $BuildID = SubscriptionFormsCustom::InsertDB($formData);
        $formData['BuildID'] = $BuildID;
        $APIKeyCustomFormWithConstraints = Core::EncryptURL(
            ['UserID' => $UserID, 'BuildID' => $BuildID],
            'api_key'
        );

        $constraint = $message . ' - forbidden urls -';
        $constraintCheckEmail = '<EMAIL>';

        try {
            LegacyApiSubscriber::subscribeWithKey(
                $APIKeyCustomFormWithConstraints,
                $constraintCheckEmail,
                '',
                ['FirstName' => 'Go to https://go.to and be happy']
            );
        } catch (ServicesException $e) {
            $this->assertSame('subscription failed', $e->getMessage());
            $this->assertEquals(406, $e->getStatusCode());
            $data = $e->getData();
            $this->assertIsArray($data);
            $this->assertEquals(
                KLICKTIPPAPI_ERROR_FIELD_VALIDATION_FAILED,
                $data['error'],
                $constraint . ' error validation failed'
            );
            $this->assertTrue(
                is_int(strpos($data['error_message'], 'must not contain URLs')),
                $constraint . ' message mentions forbidden urls'
            );
            $this->assertEquals(
                $data['uid'],
                $UserID,
                $constraint . ' expected uid'
            );
        }

        $this->assertFalse(
            Subscription::RetrieveSubscriptionByEmailAddress(
                $UserID,
                $constraintCheckEmail
            ),
            $constraint . ' email does not exists'
        );

        $formData['FormFields']['FirstName']['allow_urls'] = true;

        SubscriptionFormsCustom::UpdateDB($formData);

        $constraint = $message . ' - allowed urls -';

        $result = LegacyApiSubscriber::subscribeWithKey(
            $APIKeyCustomFormWithConstraints,
            $constraintCheckEmail,
            '',
            ['FirstName' => 'Go to https://go.to and be happy']
        );
        $this->assertEquals(
            $result,
            filter_var($result, FILTER_VALIDATE_URL),
            $constraint . ' expect valid redirect url'
        );
        $this->assertNotEmpty(
            Subscription::RetrieveSubscriptionByEmailAddress(
                $UserID,
                $constraintCheckEmail
            ),
            $constraint . ' email exists'
        );

        $constraint = $message . ' - max length exceeded -';
        $constraintCheckEmail = '<EMAIL>';

        try {
            LegacyApiSubscriber::subscribeWithKey(
                $APIKeyCustomFormWithConstraints,
                $constraintCheckEmail,
                '',
                ['FirstName' => str_repeat('A', $maxLength + 1)]
            );
            $this->fail(
                $message . ' expect exception since firstname is too long'
            );
        } catch (ServicesException $e) {
            $this->assertEquals(
                406,
                $e->getStatusCode(),
                $constraint . ' http code 406'
            );
            $data = $e->getData();
            $this->assertIsArray($data);
            $this->assertEquals(
                KLICKTIPPAPI_ERROR_FIELD_VALIDATION_FAILED,
                $data['error'],
                $constraint . ' error validation failed'
            );
            $this->assertTrue(
                is_int(
                    strpos(
                        $data['error_message'],
                        'must not exceed maximum length'
                    )
                ),
                $constraint . ' message mentions maximum length'
            );
            $this->assertEquals(
                $data['uid'],
                $UserID,
                $constraint . ' expected uid in exception data'
            );
        }

        $this->assertFalse(
            Subscription::RetrieveSubscriptionByEmailAddress(
                $UserID,
                $constraintCheckEmail
            ),
            $constraint . ' email does not exists'
        );

        // increase limit to length of first name
        $formData['FormFields']['FirstName']['max_length'] = $maxLength + 1;
        SubscriptionFormsCustom::UpdateDB($formData);

        $constraint = $message . ' - max length not exceeded -';

        $result = LegacyApiSubscriber::subscribeWithKey(
            $APIKeyCustomFormWithConstraints,
            $constraintCheckEmail,
            '',
            ['FirstName' => str_repeat('A', $maxLength + 1)]
        );
        $this->assertEquals(
            $result,
            filter_var($result, FILTER_VALIDATE_URL),
            $constraint . ' expect valid redirect url'
        );
        $this->assertNotEmpty(
            Subscription::RetrieveSubscriptionByEmailAddress(
                $UserID,
                $constraintCheckEmail
            ),
            $constraint . ' email exists'
        );

        $formData['FormFields']['FirstName']['max_length'] = '';
        SubscriptionFormsCustom::UpdateDB($formData);

        $constraint = $message . ' - max length not defined -';
        $constraintCheckEmail = '<EMAIL>';

        $result = LegacyApiSubscriber::subscribeWithKey(
            $APIKeyCustomFormWithConstraints,
            $constraintCheckEmail,
            '',
            ['FirstName' => str_repeat('B', $maxLength * 100)]
        );
        $this->assertEquals(
            $result,
            filter_var($result, FILTER_VALIDATE_URL),
            $constraint . ' expect valid redirect url'
        );
        $this->assertNotEmpty(
            Subscription::RetrieveSubscriptionByEmailAddress(
                $UserID,
                $constraintCheckEmail
            ),
            $constraint . ' email exists'
        );

        // --- Connector Test

        //--- TestCase: $connector->signin

        //        $message = '$connector->signin: ';
        //
        //        $APIFields = array(
        //            'fieldFirstName' => 'First name',
        //            'fieldLastName' => 'Last name',
        //        );
        //
        //        $BuildID = APIKey::InsertDB(
        //          array('RelOwnerUserID' => $customer->uid, 'Name' => 'Api Connector SignIn'));
        //        $this->assertTrue($BuildID > 0, "$message create listbuilding apikey");
        //
        //        $ApiKey = Core::EncryptURL(array(
        //            'UserID' => $customer->uid,
        //            'BuildID' => $BuildID,
        //        ), 'api_key');
        //
        //
        //        $email = '<EMAIL>';
        //        $smsnumber = '';
        //        $connector->signin($ApiKey, $email, $APIFields);
        //        $errors = $connector->get_last_error();
        //        $this->assertTrue(empty($errors), $message . " expected no errors for signin(): " . $errors);
        //
        //        $sid = $connector->subscriber_search($email);
        //        $errors = $connector->get_last_error();
        //        $this->assertTrue(empty($errors),
        //          $message . " expected no errors for subscriber_search(): " . $errors);
        //
        //        $subscriber = $connector->subscriber_get($sid);
        //        $this->assertTrue(!empty($subscriber),
        //          "$message $email -> {$connector->get_last_error()}".print_r($subscriber,1));
        //
        //        $email = '<EMAIL>';
        //        $smsnumber = '004915208000001';
        //        $connector->signin($ApiKey, $email, $APIFields, $smsnumber);
        //        $sid = $connector->subscriber_search($email);
        //        $subscriber = $connector->subscriber_get($sid);
        //        $this->assertTrue(!empty($subscriber),
        //          "$message $email + $smsnumber -> {$connector->get_last_error()}".print_r($subscriber,1));
        //        $this->assertTrue($subscriber->sms_phone == $smsnumber, "$message smsnumber $smsnumber set");
        //
        //        /* TODO subscriber_search_by_smsnumber() needed
        //        $email = '';
        //        $smsnumber = '004915208000002';
        //        $connector->signin($ApiKey, $email, $APIFields, $smsnumber);
        //        $sid = $connector->subscriber_search($email);
        //        $subscriber = $connector->subscriber_get($sid);
        //        $this->assertTrue(!empty($subscriber),
        //          "$message smsnumber_only $smsnumber -> {$connector->get_last_error()}".print_r($subscriber,1));
        //        $this->assertTrue($subscriber->sms_phone == $smsnumber, "$message smsnumber $smsnumber set");
        //        $this->assertTrue($subscriber->email == $subscriber->id, "$message email address == subscriber id");
        //        */
        //
        //        $email = '<EMAIL>';
        //        $smsnumber = 'invalid';
        //        $connector->signin($ApiKey, $email, $APIFields, $smsnumber);
        //        $sid = $connector->subscriber_search($email);
        //        $subscriber = $connector->subscriber_get($sid);
        //        $this->assertTrue(empty($subscriber),
        //          "$message $email -> invalid sms number - {$connector->get_last_error()}".print_r($subscriber,1));
        //        $this->assertTrue($subscriber->sms_phone == '', "$message invalid smsnumber not set");
        //
        //        $email = '<EMAIL>';
        //        $smsnumber = '004915208000001';
        //        $connector->signin($ApiKey, $email, $APIFields, $smsnumber);
        //        $sid = $connector->subscriber_search($email);
        //        $subscriber = $connector->subscriber_get($sid);
        //        $this->assertTrue(!empty($subscriber),
        //     "$message re-subscribe $email + $smsnumber -> {$connector->get_last_error()}".print_r($subscriber,1));
        //        $this->assertTrue($subscriber->sms_phone == $smsnumber, "$message smsnumber $smsnumber set");
        //
        //        /* @see: TODO subscriber_search_by_smsnumber() needed
        //        $email = '<EMAIL>';
        //        $smsnumber = '004915208000002';
        //        $connector->signin($ApiKey, $email, $APIFields, $smsnumber);
        //        $sid = $connector->subscriber_search($email);
        //        $subscriber = $connector->subscriber_get($sid);
        //        $this->assertTrue(!empty($subscriber),
        //          "$message update dummy email by subscribe $email + $smsnumber
        //          -> {$connector->get_last_error()}".print_r($subscriber,1));
        //        $this->assertTrue($subscriber->email == $email,
        //          "$message dummy email for $smsnumber updated to $email");
        //         */
        //
        //        $email = '';
        //        $smsnumber = '';
        //        $redirect = $connector->signin($ApiKey, $email, $APIFields, $smsnumber);
        //        $this->assertTrue(empty($redirect),
        //          "$message no email, no smsnumber -> {$connector->get_last_error()}".print_r($subscriber,1));


        /*
         * _klicktippapi_resource_subscriber_untag_withkey
         */
        $message = 'LegacyApiSubscriber::untagWithKey';

        // unsubscribe with default redirect
        $email = '<EMAIL>';
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $RedirectURL = LegacyApiSubscriber::subscribeWithKey($APIKey5, $email);
        $this->assertNotEmpty($RedirectURL, "$message redirecturl");
        $result = db_query(
            "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID"
            . " AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $Subscriber['SubscriberID'],
                ':RelTagID' => $TagID2,
            ]
        )->fetchField();
        $this->assertTrue($result == 1, $message . ' tagging before');
        $result = LegacyApiSubscriber::untagWithKey($APIKey5, $email);
        $this->assertTrue($result, "$message result");
        $result = db_query(
            "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID"
            . " AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $Subscriber['SubscriberID'],
                ':RelTagID' => $TagID2,
            ]
        )->fetchField();
        $this->assertTrue($result == 0, $message . ' tagging after');

        /*
         * _klicktippapi_resource_subscriber_unsubscribe_withkey
         */
        $message = 'LegacyApiSubscriber::unsubscribeWithKey';

        // unsubscribe with default redirect
        $email = '<EMAIL>';
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $this->assertTrue(
            $Subscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            "$message $email status"
        );
        $_SERVER['REMOTE_ADDR'] = '127.0.0.1';
        $result = LegacyApiSubscriber::unsubscribeWithKey($APIKey4, $email);
        $this->assertTrue($result, "$message result");
        $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress(
            $UserID,
            $email
        );
        $this->assertTrue(
            $Subscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED,
            "$message $email status"
        );
    }
}

<?php

namespace App\JsonRpc\Framework;

use App\JsonRpc\Framework\Events\RpcRequestEvent;
use App\JsonRpc\Framework\Events\RpcResponseEvent;
use App\JsonRpc\JsonRpcServerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

class JsonRpcSymfonyBridge
{
    private EventDispatcherInterface $dispatcher;

    private JsonRpcServerInterface $server;

    private RequestStack $requestStack;

    public function __construct(
        EventDispatcherInterface $dispatcher,
        JsonRpcServerInterface $server,
        RequestStack $requestStack
    ) {
        $this->dispatcher = $dispatcher;
        $this->server = $server;
        $this->requestStack = $requestStack;
    }

    public function handle(Request $request): Response
    {
        $this->requestStack->push($request);

        /** @var RequestEvent $requestEvent */
        $requestEvent = $this->dispatcher->dispatch(
            new RpcRequestEvent($request),
            RpcRequestEvent::EVENT_NAME,
        );
        if ($requestEvent->getResponse()) {
            return $requestEvent->getResponse();
        }

        $response = new JsonResponse($this->server->handle($request->toArray()));

        /** @var RpcResponseEvent $responseEvent */
        $responseEvent = $this->dispatcher->dispatch(
            new RpcResponseEvent($request, $response),
            RpcResponseEvent::EVENT_NAME,
        );

        $this->requestStack->pop();

        return $responseEvent->getResponse();
    }
}

<?php

namespace App\Klicktipp;

use App\Klicktipp\ApiKey\ApiKeyLinkResolver;
use App\Klicktipp\Email\EmailAutomation\EmailAutomationLinkResolver;
use App\Klicktipp\Email\EmailNotification\EmailNotificationLinkResolver;
use App\Klicktipp\LandingPage\LandingPage;
use App\Klicktipp\LandingPage\LandingPageLinkResolver;
use App\Klicktipp\RequestSms\Listbuilding\RequestSmsListbuildingLinkResolver;
use App\Klicktipp\RequestSms\Nexmo\RequestSmsNexmoLinkResolver;
use App\Klicktipp\RequestSms\Twilio\RequestSmsTwilioLinkResolver;
use App\Klicktipp\RequestByMail\RequestByMailLinkResolver;
use App\Klicktipp\Signature\SignatureLinkResolver;
use App\Klicktipp\Sms\SmsAutomation\SmsAutomationLinkResolver;
use App\Klicktipp\TaggingPixel\TaggingPixelsLinkResolver;
use App\Ktast\KtastClient;
use Exception;
use Doctrine\DBAL\Exception as DoctrineDBALException;
use PDO;
use stdClass;
use Symfony\Component\HttpClient\HttpClient;

/**
 * Subscribers class
 *
 * This class holds static subscriber related functions
 **/
class Subscribers
{
    public const BOUNCETYPE_NOTBOUNCED = 0; // 'Not Bounced'
    public const BOUNCETYPE_SOFT = 1;      // 'Soft'
    public const BOUNCETYPE_HARD = 2;      // 'Hard'
    public const BOUNCETYPE_SPAM = 3;      // 'Spam'
    public const SUBSCRIPTIONSTATUS_OPTIN = 1; // 'Opt-In Pending'
    public const SUBSCRIPTIONSTATUS_SUBSCRIBED = 2; // 'Subscribed'
    public const SUBSCRIPTIONSTATUS_UNSUBSCRIBED = 3; // 'Unsubscribed'

    //update subscription
    public const DOI_PROCESS_USE_FROM_SUBSCRIPTION = 0; //default: use DOI process from current subscription
    // subscriber changed email, use DOI/Confirmation email from change email list
    // (specified by customer or default list)
    public const DOI_PROCESS_USE_CHANGE_EMAIL_LIST = 1;
    public const DOI_PROCESS_USE_SINGLE_OPTIN = 2; //customer changed subscriber email address -> no DoubleOptIn

    public const FALLBACK_IP_ADDRESS = '0.0.0.0';

    public const HISTORY_TEXT = 0;
    public const HISTORY_CREATED = 1; // NOT persistent/displayed
    public const HISTORY_UPDATED = 2; // NOT persistent/displayed
    public const HISTORY_OPTIN = 3; // NOT persistent/displayed
    public const HISTORY_SUBSCRIBED = 4; // NOT persistent/displayed
    public const HISTORY_UNSUBSCRIBED = 5;
    public const HISTORY_TAGGED = 6; // NOT persistent/displayed
    public const HISTORY_UNTAGGED = 7; // NOT persistent/displayed
    public const HISTORY_CUSTOMFIELDS = 8; // NOT persistent/displayed
    public const HISTORY_IMPORT = 9;
    public const HISTORY_UNSUBSCRIPTION_FEEDBACK = 10;
    public const HISTORY_SUBSCRIBER_FEEDBACK = 11;
    public const HISTORY_EMAIL_ADDRESS_CHANGED = 12;
    public const HISTORY_BOUNCED = 13; // NOT persistent/displayed
    public const HISTORY_BOUNCECHECK = 14;
    public const HISTORY_BOUNCERESET = 15;
    public const HISTORY_SOFTBOUNCE_RESOLVED = 16;
    public const HISTORY_HARDBOUNCE_RESOLVED = 17;
    public const HISTORY_SPAMBOUNCE_RESOLVED = 18;
    public const HISTORY_HATER = 19; // NOT persistent/displayed
    public const HISTORY_SENDFAILED = 20; // NOT persistent/displayed
    public const HISTORY_SMS_INBOUND = 21;
    public const HISTORY_SMSNUMBER_SUBSCRIBED = 22; // NOT persistent/displayed
    public const HISTORY_SMSNUMBER_UNSUBSCRIBED = 23; // NOT persistent/displayed
    public const HISTORY_SMSNUMBER_CHANGED = 24;
    public const HISTORY_SMSNUMBER_BOUNCED = 25; // NOT persistent/displayed
    public const HISTORY_SMSNUMBER_SOFTBOUNCE_RESOLVED = 26; // NOT persistent/displayed
    public const HISTORY_SMSNUMBER_HARDBOUNCE_RESOLVED = 27; // NOT persistent/displayed
    public const HISTORY_SMSNUMBER_SPAMBOUNCE_RESOLVED = 28; // NOT persistent/displayed
    public const HISTORY_SOFTBOUNCERESET = 29;
    public const HISTORY_OUTBOUND_CALL_FAILED = 30;
    public const HISTORY_SMSNUMBER_DELETED = 31;
    public const HISTORY_SMSNUMBER_CONFLICT = 32;
    public const HISTORY_FACEBOOK_AUDIENCE_EXPIRED_ACCESS = 33;
    public const HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED = 34;
    public const HISTORY_PENDING = 35; // NOT persistent/displayed
    public const HISTORY_PAUSED = 36; // NOT persistent/displayed
    public const HISTORY_PROCESSING = 37; // NOT persistent/displayed
    public const HISTORY_GOAL_REACHED = 38;
    public const HISTORY_GOAL_PASSED = 39;
    public const HISTORY_NOTIFICATION_INVALID_TO_EMAIL = 40;
    public const HISTORY_SPAMBOUNCERESET = 41;
    public const HISTORY_REQUEST_INFORMATION = 42;
    public const HISTORY_REQUEST_INFORMATION_FINISHED = 43;
    public const HISTORY_REQUEST_UPDATE = 44;
    public const HISTORY_REQUEST_UPDATE_FINISHED = 45;
    public const HISTORY_REQUEST_DELETE = 46;
    public const HISTORY_REQUEST_DELETE_FBAUDIENCE_ERROR = 47;
    public const HISTORY_REQUEST_OUTBOUND_CALL_FAILED = 48;
    public const HISTORY_FULLCONTACT_CALL_FAILED = 49;
    public const HISTORY_PAYMENT = 50;
    public const HISTORY_PAYMENT_INVOICE = 51;
    public const HISTORY_TRANSACTIONAL = 52;
    public const HISTORY_PAUSED_RESTART = 53; // NOT persistent
    public const HISTORY_STATE_MOVED = 54;
    public const HISTORY_WAITING_FOR_TEMPORAL_CONDITION = 55;


    const SUBSCRIBE_ERROR_INVALID_EMAIL_ADDRESS = 2;
    const SUBSCRIBE_ERROR_DUPLICATE_EMAIL_ADDRESS = 3;
    const SUBSCRIBE_ERROR_UNSUBSCRIBED_EMAIL_ADDRESS = 4;
    const SUBSCRIBE_ERROR_INVALID_USER_INFORMATION = 5;
    const SUBSCRIBE_ERROR_INVALID_LIST_INFORMATION = 6;
    const SUBSCRIBE_ERROR_CUSTOMFIELD_DATA = 7;
    const SUBSCRIBE_ERROR_INVALID_SUBSCRIBER = 8;
    const SUBSCRIBE_ERROR_DUPLICATE_PHONE_NUMBER = 9;
    const SUBSCRIBE_ERROR_UNSUBSCRIBED_PHONE_NUMBER = 10;
    const SUBSCRIBE_ERROR_INVALID_PHONE_NUMBER = 11;
    const SUBSCRIBE_ERROR_INTERNAL = 12;

    const DELETION_TYPE_OTHER = 'other';
    const DELETION_TYPE_API = 'api';
    const DELETION_TYPE_USER_CANCEL_HOOK = 'usercancelhook';
    const DELETION_TYPE_UNSUBSCRIPTION = 'unsubscription';
    const DELETION_TYPE_SUBSCRIBER_QUEUE = 'subscriberqueue';
    const DELETION_TYPE_SUBSCRIBER_DELETION_QUEUE = 'subscriberdeletionqueue';
    const DELETION_TYPE_PENDING_SUBSCRIBER_DELETION_QUEUE = 'pendingsubscriberdeletionqueue';
    const DELETION_TYPE_PRIVACY = 'privacy';
    const HistoryBounceDsnStatusTranslationKeys = [
        /*t(*/'subscriber::history::bounce::reason::4.0.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.7'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.8'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.1.9'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.2.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.2.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.2.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.2.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.2.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.3.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.3.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.3.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.3.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.3.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.3.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.3.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.4.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.4.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.4.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.4.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.4.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.4.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.4.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.4.7'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.5.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.5.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.5.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.5.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.5.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.5.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.5.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.7'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.8'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.9'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.6.10'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.7'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.8'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.9'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.10'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.11'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.12'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.13'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.14'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.15'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::4.7.16'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.0.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.7'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.8'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.1.9'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.2.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.2.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.2.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.2.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.2.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.3.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.3.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.3.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.3.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.3.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.3.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.3.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.4.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.4.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.4.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.4.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.4.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.4.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.4.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.4.7'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.5.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.5.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.5.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.5.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.5.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.5.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.5.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.7'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.8'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.9'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.6.10'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.0'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.1'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.2'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.3'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.4'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.5'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.6'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.7'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.8'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.9'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.10'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.11'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.12'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.13'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.14'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.15'/*)*/,
        /*t(*/'subscriber::history::bounce::reason::5.7.16'/*)*/,
    ];

    public static $DisplayBounceTypes = array(
        Subscribers::BOUNCETYPE_NOTBOUNCED => /*t(*/'Not Bounced'/*)*/,
        Subscribers::BOUNCETYPE_SOFT =>  /*t(*/'Soft'/*)*/,
        Subscribers::BOUNCETYPE_SPAM =>  /*t(*/'Spam'/*)*/,
        Subscribers::BOUNCETYPE_HARD =>  /*t(*/'Hard'/*)*/,
    );
    public static $TranslationBounceTypes = array(
        Subscribers::BOUNCETYPE_NOTBOUNCED =>  /*t(*/'Not Bounced'/*)*/,
        Subscribers::BOUNCETYPE_SOFT =>  /*t(*/'Soft bounce'/*)*/,
        Subscribers::BOUNCETYPE_SPAM =>  /*t(*/'Spam bounce'/*)*/,
        Subscribers::BOUNCETYPE_HARD =>  /*t(*/'Hard bounce'/*)*/,
    );
    public static $DisplaySubscriptionStatus = array(
        Subscribers::SUBSCRIPTIONSTATUS_OPTIN =>  /*t(*/'Opt-In Pending'/*)*/,
        Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED =>  /*t(*/'Subscribed'/*)*/,
        Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED =>  /*t(*/'Unsubscribed'/*)*/,
    );

    /**
     * Public Subscriber Fields
     *
     * Fields of table {subscribers} that are used as parameters in email/api/outbound.
     * Format is similar to CustomFields::$GlobalCustomFieldDefs
     */
    public static $PublicSubscriberFieldDefs = array(
        'SubscriberID' => array(
            'FieldID' => 'SubscriberID',
            'FieldName' => 'Subscriber ID',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'ApiFieldIdentifier' => 'id',
        ),
        // Email
        'EmailAddress' => array(
            'FieldID' => 'EmailAddress',
            'FieldName' => 'Email Address',
            'FieldTypeEnum' => CustomFields::TYPE_EMAIL,
            'ApiFieldIdentifier' => 'email',
        ),
        'SubscriptionDate' => array(
            'FieldID' => 'SubscriptionDate',
            'FieldName' => 'Subscription Date',
            'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
            'ApiFieldIdentifier' => 'date',
        ),
        'SubscriptionIP' => array(
            'FieldID' => 'SubscriptionIP',
            'FieldName' => 'SubscriptionIP',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'ApiFieldIdentifier' => 'optin_ip',
        ),
        'SubscriptionReferrer' => array(
            'FieldID' => 'SubscriptionReferrer',
            'FieldName' => 'Subscription Referrer',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'ApiFieldIdentifier' => 'referrer',
        ),
        'BounceType' => array(
            'FieldID' => 'BounceType',
            'FieldName' => 'Bounce Type',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'ApiFieldIdentifier' => 'bounce',
        ),
        'SubscriptionStatus' => array(
            'FieldID' => 'SubscriptionStatus',
            'FieldName' => 'Subscription Status',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'ApiFieldIdentifier' => 'status',
        ),
        'UnsubscriptionDate' => array(
            'FieldID' => 'UnsubscriptionDate',
            'FieldName' => 'Unsubscription Date',
            'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
            'ApiFieldIdentifier' => 'unsubscription',
        ),
        'UnsubscriptionIP' => array(
            'FieldID' => 'UnsubscriptionIP',
            'FieldName' => 'Unsubscription IP',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'ApiFieldIdentifier' => 'unsubscription_ip',
        ),
        'OptInDate' => array(
            'FieldID' => 'OptInDate',
            'FieldName' => 'Opt-In Date',
            'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
            'ApiFieldIdentifier' => 'optin',
        ),
        // SMS
        'PhoneNumber' => array(
            'FieldID' => 'PhoneNumber',
            'FieldName' => 'Phone Number',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'ApiFieldIdentifier' => 'sms_phone',
        ),
        'SMSSubscriptionDate' => array(
            'FieldID' => 'SMSSubscriptionDate',
            'FieldName' => 'SMS Subscription Date',
            'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
            'ApiFieldIdentifier' => 'sms_date',
        ),
        'SMSUnsubscriptionDate' => array(
            'FieldID' => 'SMSUnsubscriptionDate',
            'FieldName' => 'SMS Unsubscription Date',
            'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
            'ApiFieldIdentifier' => 'sms_unsubscription',
        ),
        'SubscriptionSMS' => array(
            'FieldID' => 'SubscriptionSMS',
            'FieldName' => 'Subscription SMS',
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'ApiFieldIdentifier' => 'sms_referrer',
        ),
    );

    /**
     * Returns subscriber data
     */
    public static function RetrieveSubscriber($UserID, $SubscriberID)
    {
        $result = db_query(
            "SELECT * FROM {subscribers} WHERE RelOwnerUserID = :RelOwnerUserID AND SubscriberID = :SubscriberID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':SubscriberID' => $SubscriberID
            )
        );
        if ($Subscriber = kt_fetch_array($result)) {
            return $Subscriber;
        } else {
            return false;
        }
    }

    /**
     * Returns all subscriber data for a given subscriber id and reference
     *
     * If not found, checks if subscriber was merged and search for it using
     * merge reference and new subscriber id
     */
    public static function RetrieveFullSubscriberWithMergeCheck($UserID, $SubscriberID, $ReferenceID)
    {
        $fullSubscriber = static::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);

        // if $fullSubscriber is found, we are done
        // if $ReferenceID is not 0, there is no fallback (only default reference changes during merge)
        if ($fullSubscriber || $ReferenceID != 0) {
            return $fullSubscriber;
        }

        // now try to get subscriberID and reference after merge. if found, retrieve full subscriber using it
        $newSubscriberIDAndReference = Reference::getSubscriberIDAndReferenceAfterMerge($UserID, $SubscriberID);
        if ($newSubscriberIDAndReference) {
            return static::RetrieveFullSubscriber(
                $UserID,
                $newSubscriberIDAndReference['RelSubscriberID'],
                $newSubscriberIDAndReference['ReferenceID']
            );
        }

        return false;
    }

    /**
     * Returns all subscriber data for a given subscriber id and reference
     * @deprecated use Subscriber::RetrieveSubscriptionByEmailAddress instead
     */
    public static function RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID)
    {
        // note: almost the same query is used in _klicktipp_subscriber_export_queue_worker (in case we need to adjust it, adjust both)
        // note: ReferenceID is passed back in order to simplify result processing. Sometimes used reference is an important informatin
        //       In same case (merge fallback) used reference id != passed reference id
        $result = db_query(
            "SELECT s.*, :ReferenceID as ReferenceID, mail.LastOpenDate, mail.LastOpenIP, mail.RelListID, mail.OptInDate, mail.OptInIP, mail.ConfirmationEmailDate, " .
            " mail.ContactInfo as EmailAddress, mail.SubscriptionStatus, mail.BounceType, " .
            " mail.SubscriptionDate, mail.SubscriptionIP, mail.UnsubscriptionDate, mail.UnsubscriptionIP, mail.SubscriptionReferrer, " .
            " sms.ContactInfo as PhoneNumber, sms.SubscriptionStatus as SMSSubscriptionStatus, sms.BounceType as SMSBounceType, " .
            " sms.SubscriptionDate as SMSSubscriptionDate, sms.UnsubscriptionDate as SMSUnsubscriptionDate, sms.SubscriptionReferrer as SubscriptionSMS " .
            " FROM {subscribers} s " .
            " LEFT JOIN (" .
            " SELECT maile.* FROM {subscription} maile  " .
            " INNER JOIN {subscription_reference} mailr ON mailr.ReferenceID = :ReferenceID AND mailr.RelOwnerUserID = maile.RelOwnerUserID AND mailr.RelSubscriberID = maile.RelSubscriberID AND mailr.SubscriptionType = maile.SubscriptionType AND mailr.ContactInfo = maile.ContactInfo AND mailr.Optional = 0 " .
            " ) AS mail ON mail.RelOwnerUserID = s.RelOwnerUserID AND mail.RelSubscriberID = s.SubscriberID AND mail.SubscriptionType = :mailSubscriptionType " .
            " LEFT JOIN (" .
            " SELECT smse.* FROM {subscription} smse  " .
            " INNER JOIN {subscription_reference} smsr ON smsr.ReferenceID = :ReferenceID AND smsr.RelOwnerUserID = smse.RelOwnerUserID AND smsr.RelSubscriberID = smse.RelSubscriberID AND smsr.SubscriptionType = smse.SubscriptionType AND smsr.ContactInfo = smse.ContactInfo AND smsr.Optional = 0 " .
            " ) AS sms ON sms.RelOwnerUserID = s.RelOwnerUserID AND sms.RelSubscriberID = s.SubscriberID AND sms.SubscriptionType = :smsSubscriptionType " .
            " WHERE s.RelOwnerUserID = :RelOwnerUserID AND s.SubscriberID = :SubscriberID ",
            array(
                ':RelOwnerUserID' => $UserID,
                ':SubscriberID' => $SubscriberID,
                ':mailSubscriptionType' => Subscription::SUBSCRIPTIONTYPE_EMAIL,
                ':smsSubscriptionType' => Subscription::SUBSCRIPTIONTYPE_SMS,
                ':ReferenceID' => $ReferenceID,
            )
        );
        return kt_fetch_array($result);
    }

    /**
     * Retrieve full subscriber. If subscriber record is missing, create one
     *
     * NOTE: use this methode only if subscriber existed in former time (e.g. if there are subscriptions with specified
     * $Subscriber id
     *
     * @param int $UserID
     * @param int $SubscriberID
     * @param int $ReferenceID
     *
     * @deprecated because it uses another deprecated method (Subscribers::RetrieveFullSubscriber)
     */
    public static function RetrieveFullSubscriberAndRestoreIfMissing($UserID, $SubscriberID, $ReferenceID)
    {
        $fullSubscriber = static::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
        if (!$fullSubscriber) {
            // it's not supposed to happen. Log it to analyse such cases
            $vars = array(
                '!function' => __FUNCTION__,
                '!file' => __FILE__,
                '!subscriberID' => $SubscriberID,
                '!userID' => $UserID,
            );
            watchdog(
                'kt-subscription',
                'deleted subscriber !subscriberID of user !userID with existing subscription',
                $vars,
                WATCHDOG_ERROR
            );

            $SubscriberValues = [
                'RelOwnerUserID' => $UserID,
                'SubscriberID' => $SubscriberID
            ];

            // Add to the database
            try {
                db_insert('subscribers')
                    ->fields($SubscriberValues)
                    ->execute();
            } catch (Exception $e) {
                // 1062 is duplicate entry, so SubscriberID exist again (maybe due a race condition).
                // In this case it would be ok
                if ($e->errorInfo[1] != 1062) {
                    $error = [
                        '!SubscriberValues' => $SubscriberValues,
                        '!Exception' => $e,
                    ];
                    Errors::unexpected('db_insert failed in ' . __METHOD__, $error);
                    return false;
                }
            };
            $fullSubscriber = static::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
        }

        return $fullSubscriber;
    }

    /**
     * Returns subscription (subscriber with custom field values and subscriber phone number)
     */
    /* @deprecated use Subscription::RetrieveSubscriptionByContactInfoAndReference */
    public static function RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID)
    {
        $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
        if (empty($FullSubscriberWithFields)) {
            return false;
        }

        $ArrayCustomFields = CustomFields::RetrieveCustomFields($UserID);
        if (!empty($ArrayCustomFields)) {
            foreach ($ArrayCustomFields as $EachField) {
                $FullSubscriberWithFields['CustomField' . $EachField['CustomFieldID']] = CustomFields::GetCustomFieldData(
                    $UserID,
                    $SubscriberID,
                    $EachField,
                    $ReferenceID
                );
            }
        }

        return $FullSubscriberWithFields;
    }

    /**
     * Returns subscribed tag information of a given subscriber
     *
     * @param $UserID
     * @param $TagID
     * @param $SubscriberID
     * @param $ReferenceID
     *
     * @return false|mixed
     */
    public static function RetrieveTagging($UserID, $TagID, $SubscriberID, $ReferenceID)
    {
        $result = db_query(
            "SELECT tt.*, tt.RelSubscriberID AS SubscriberID FROM {tagging} tt " .
            " INNER JOIN {tag} t ON t.RelOwnerUserID = tt.RelOwnerUserID AND tt.RelTagID = t.TagID " .
            " WHERE tt.RelOwnerUserID = :RelOwnerUserID AND tt.RelTagID = :RelTagID AND tt.RelSubscriberID = :RelSubscriberID " .
            " AND (t.MultiValue = 0 OR tt.ReferenceID = :ReferenceID) ",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelTagID' => $TagID,
                ':RelSubscriberID' => $SubscriberID,
                ':ReferenceID' => $ReferenceID
            )
        );
        if ($tagging = kt_fetch_array($result)) {
            // single value taggings have all references, so return the callers one (even they are technically stored with ref 0)
            $tagging['ReferenceID'] = $ReferenceID;
            return $tagging;
        } else {
            return false;
        }
    }

    /**
     * Returns all taggings of tag+subscriber (inddexed by reference ids)
     *
     * @param int $UserID
     * @param int $TagID
     * @param int $SubscriberID
     *
     * @return array
     */
    public static function RetrieveTaggings($UserID, $TagID, $SubscriberID)
    {
        return db_query(
            "SELECT * FROM {tagging} " .
            " WHERE RelOwnerUserID = :RelOwnerUserID AND RelTagID = :RelTagID AND RelSubscriberID = :RelSubscriberID ",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelTagID' => $TagID,
                ':RelSubscriberID' => $SubscriberID,
            ]
        )->fetchAllAssoc('ReferenceID', PDO::FETCH_ASSOC);
    }

    /**
     * Retrieve all subscriber taggings
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $ReferenceID
     * @param bool $TagIDsOnly
     *
     * @return array
     */
    public static function RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID, $TagIDsOnly = false)
    {
        $ArrayTaggings = array();

        $result = db_query(
            "SELECT tt.*, tt.RelSubscriberID AS SubscriberID FROM {tagging} tt " .
            " INNER JOIN {tag} t ON t.RelOwnerUserID = tt.RelOwnerUserID AND tt.RelTagID = t.TagID " .
            " WHERE tt.RelOwnerUserID = :RelOwnerUserID AND tt.RelSubscriberID = :RelSubscriberID " .
            " AND (t.MultiValue = 0 OR tt.ReferenceID = :ReferenceID) ",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':ReferenceID' => $ReferenceID,
            )
        );
        while ($tagging = kt_fetch_array($result)) {
            // single value taggings have all references, so return the callers one (even they are technically stored with ref 0)
            $tagging['ReferenceID'] = $ReferenceID;
            $ArrayTaggings[$tagging['RelTagID']] = $tagging;
        }

        if ($TagIDsOnly) {
            return array_keys($ArrayTaggings);
        } else {
            return $ArrayTaggings;
        }
    }

    /**
     * Creates a database query which retrieves all subscribers matching given tag conditions
     *
     * @note: Multivalue taggings - this supports newsletters and autoresponders only
     *
     * @param $UserID
     * @param int $TriggerTypEnum
     * @param array $RecipientLists
     * @param int $offset
     * @param int $size
     *
     * @return array|int
     */
    public static function GetTaggedSubscribersQuery(
        $UserID,
        $TriggerTypEnum,
        $RecipientLists = [],
        $offset = 0,
        $size = 0,
        $SubscriberID = 0
    ) {
        if ($TriggerTypEnum < 0) {
            // this does no longer support non campaign usage, use RetrieveTaggedSubscriberIDs instead
            return ['', []];
        }

        $checkSubsctiptions = !Campaigns::IsProcessflow($TriggerTypEnum);

        // LIMIT
        $limit = empty($size) ? "" : " LIMIT $offset, $size";

        // build query
        //@note: we need a join for every tag. Mysql limits the number of tables to join by 61, so we have a limit of 50 in tag selection forms.

        $TagJoin = '';
        if (empty($RecipientLists['TaggedWith'])) {
            $SELECTSubscriptionDate = "s.SubscriptionDate";
        } elseif (count($RecipientLists['TaggedWith']) == 1) {
            // with one tag TAG_HAS_ALL and TAG_HAS_ANY are the same
            $TagID = reset($RecipientLists['TaggedWith']);
            $SELECTSubscriptionDate = "tt.SubscriptionDate AS SubscriptionDate";
            $TagJoin = " INNER JOIN {tagging} tt ON tt.RelOwnerUserID = r.RelOwnerUserID " .
                "AND tt.RelSubscriberID = r.RelSubscriberID AND tt.RelTagID = $TagID ";
            $TagJoin .= " INNER JOIN {tag} t ON t.RelOwnerUserID = tt.RelOwnerUserID AND t.TagID = tt.RelTagID " .
                "AND (t.MultiValue = 0 OR tt.ReferenceID = r.ReferenceID) ";
        } elseif ($RecipientLists['OpTaggedWith'] == Campaigns::TAG_HAS_ALL) {
            // TAG_HAS_ALL: inner join over all tags
            $TagFromList = [];
            $i = 1;
            foreach ($RecipientLists['TaggedWith'] as $TagID) {
                // COALESCE = the first non null value in the list
                $TagFromList[] = "COALESCE(tt$i.SubscriptionDate, r.SubscriptionDate)";
                $TagJoin .= " INNER JOIN {tagging} tt$i ON tt$i.RelOwnerUserID = r.RelOwnerUserID " .
                    "AND tt$i.RelSubscriberID = r.RelSubscriberID AND tt$i.RelTagID = $TagID ";
                $TagJoin .= " INNER JOIN {tag} t$i ON t$i.RelOwnerUserID = tt$i.RelOwnerUserID " .
                    "AND t$i.TagID = tt$i.RelTagID AND (t$i.MultiValue = 0 OR tt$i.ReferenceID = r.ReferenceID) ";
                $i++;
            }
            // GREATEST(SubscriptionDate): the trigger was fullfilled with the LAST tagging
            $SELECTSubscriptionDate = "GREATEST(" . implode(',', $TagFromList) . ") AS SubscriptionDate";
        } else {
            // TAG_HAS_ANY: inner join over subquery with an inner join over all tags = the join is empty if no tag matches
            // MIN(SubscriptionDate): the trigger was fullfilled with the FIRST tagging
            $TagJoin =
                " INNER JOIN ( " .
                "  SELECT tts.RelOwnerUserID, tts.RelSubscriberID, MIN(tts.SubscriptionDate) AS SubscriptionDate, ts.MultiValue, tts.ReferenceID FROM {tagging} tts " .
                "  INNER JOIN {tag} ts ON ts.RelOwnerUserID = tts.RelOwnerUserID AND ts.TagID = tts.RelTagID " .
                "  WHERE tts.RelOwnerUserID = :RelOwnerUserID AND tts.RelTagID IN (" . implode(
                    ",",
                    $RecipientLists['TaggedWith']
                ) . ") ";

            if ($SubscriberID > 0) {
                $TagJoin .= " AND tts.RelSubscriberID = :RelSubscriberID";
                // for a single subscriber the group clause is about 100x slower and though causing problems in the send queue
            } else {
                $TagJoin .= " GROUP BY tts.RelSubscriberID, tts.ReferenceID";
            }

            $TagJoin .= " ) AS t ON t.RelOwnerUserID = r.RelOwnerUserID AND t.RelSubscriberID = r.RelSubscriberID " .
                "AND (t.MultiValue = 0 OR t.ReferenceID = r.ReferenceID)";

            $SELECTSubscriptionDate = "t.SubscriptionDate";
        }


        $query = 'SELECT r.RelSubscriberID as SubscriberID, r.ReferenceID ';
        if ($checkSubsctiptions) {
            $query .= ', ' . $SELECTSubscriptionDate . ', s.LastOpenDate';
        }
        $query .= ' FROM {subscription_reference} r ';
        if ($checkSubsctiptions) {
            $query .= ' INNER JOIN {subscription} s ON r.RelOwnerUserID = s.RelOwnerUserID AND ' .
                'r.RelSubscriberID = s.RelSubscriberID AND r.SubscriptionType = s.SubscriptionType AND ' .
                'r.ContactInfo = s.ContactInfo AND r.Optional = 0 ';
        }
        $query .= $TagJoin . ' WHERE r.RelOwnerUserID = :RelOwnerUserID ';
        $params = [':RelOwnerUserID' => $UserID];

        if ($SubscriberID) {
            $query .= ' AND r.RelSubscriberID = :RelSubscriberID ';
            $params[':RelSubscriberID'] = $SubscriberID;
        }
        if ($checkSubsctiptions) {
            $query .= ' AND s.SubscriptionStatus = :SubscriptionStatus AND s.BounceType != :BounceType ' .
                'AND s.SubscriptionType = :SubscriptionType ';
            $params += [
                ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
                ':BounceType' => Subscribers::BOUNCETYPE_HARD,
                ':SubscriptionType' => Campaigns::IsSMSCampaign(
                    $TriggerTypEnum
                ) ? Subscription::SUBSCRIPTIONTYPE_SMS : Subscription::SUBSCRIPTIONTYPE_EMAIL
            ];
        } else {
            $query .= ' AND r.Optional = 0 ';
        }
        if (!$SubscriberID) {
            $query .= ' ORDER BY r.RelSubscriberID ' . $limit;
        }


        return [$query, $params];
    }

    /**
     * Returns all subscribers matching given tag conditions
     *
     * @param string $DatabaseQuery a database query string suitable for db_query
     * @param array $DatabaseArgs database query args suitable for db_query
     * @param bool $FirstOnly
     * @param bool $AllData complete unique result set OR unique subscriber ids only
     * @param string $FilterCallback optional function which checks a
     * subscriber; this function is called before the negative checks of a
     * subscriber are checked; function's signature is
     * <pre>
     *    function($SubscriberObject): boolean
     * </pre>
     * use this param for additional subscriber checks especially if $maxTime
     *   shall be satisfied.
     *
     * @return array|int
     */
    public static function RetrieveFilteredSubscribers(
        $DatabaseQuery,
        $DatabaseArgs,
        $FirstOnly = false,
        $AllData = false,
        $FilterCallback = ''
    ) {
        $ArraySubscribers = array();

        if (!empty($DatabaseQuery)) {
            $result = db_query($DatabaseQuery, $DatabaseArgs);
            while ($Subscriber = $result->fetch()) {
                $filterOK = empty($FilterCallback) ? true : call_user_func($FilterCallback, $Subscriber);
                if ($filterOK) {
                    if ($AllData) {
                        // $AllData == TRUE shall group by SubscriberID/ReferenceID
                        $ArraySubscribers[$Subscriber->SubscriberID . '#' . $Subscriber->ReferenceID] = (array)$Subscriber;
                    } else {
                        // $AllData == FALSE shall group by SubscriberID
                        $ArraySubscribers[$Subscriber->SubscriberID] = $Subscriber->SubscriberID;
                    }
                    if ($FirstOnly) {
                        return current($ArraySubscribers);
                    }
                }
            }
        }

        if (empty($ArraySubscribers) && empty($AllData) && $FirstOnly) {
            // return (empty) subscriber id
            return 0;
        }

        return $ArraySubscribers;
    }

    /*
     * Check if 'NotTaggedWith' in $RecipientsLists matches the subscribers tagging
     */
    public static function NegativeConditionsMatching(
        $UserID,
        $SubscriberID,
        $ReferenceID,
        $RecipientLists,
        $Tagging = null
    ) {
        if (empty($RecipientLists) || empty($RecipientLists['NotTaggedWith'])) {
            // no negative conditions -> no matching
            return false;
        }

        if (is_null($Tagging)) {
            $Tagging = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID, true);
        }

        if ($RecipientLists['OpNotTaggedWith'] == Campaigns::TAG_HAS_NOT_ANY) {
            // NOT ANY, so we just need to check, if the intersection is really empty
            $intersect = array_intersect($RecipientLists['NotTaggedWith'], $Tagging);
            if (empty($intersect)) {
                // no matching negatives
                return false;
            }
        } else { // Campaigns::TAG_HAS_NOT_ALL
            // NOT ALL, so we need to check, if 'NotTaggedWith' contains anything beyond the subscribers tagging
            $diff = array_diff($RecipientLists['NotTaggedWith'], $Tagging);
            if (!empty($diff)) {
                // not all negatives
                return false;
            }
        }

        // if we come here negatives are matching
        return true;
    }

    /**
     * Returns all subscribers suitable for an automation
     *
     * @return array
     */
    public static function RetrieveProcessflowSubscribers($UserID, $offset, $size)
    {
        // get all runs (subscriber with all reference ids)
        $query = "SELECT DISTINCT RelSubscriberID AS SubscriberID, ReferenceID FROM {subscription_reference} " .
            " WHERE RelOwnerUserID = :RelOwnerUserID " .
            " ORDER BY RelSubscriberID LIMIT $offset, $size";

        $params = array(
            ':RelOwnerUserID' => $UserID,
        );

        return Subscribers::RetrieveFilteredSubscribers($query, $params, false, true);
    }

    /**
     * Returns all ACTIVE subscriber ids matching given tag and reference conditions
     *
     * @return array
     */
    public static function RetrieveTaggedSubscriberIDs(
        $UserID,
        $ReferenceID,
        $TaggedWith = [],
        $WithSubscriptionDate = false,
        $SubscriptionType = 0,
        $offset = 0,
        $size = 0
    ) {
        // active subscriber params
        $params = array(
            ':RelOwnerUserID' => $UserID,
            ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            ':BounceType' => Subscribers::BOUNCETYPE_HARD,
            ':ReferenceID' => $ReferenceID,
        );

        // filter for contact info type (email, sms, ...)
        if (!empty($SubscriptionType)) {
            $ANDSubscriptiontype = " AND s.SubscriptionType = :SubscriptionType";
            $params += array(
                ':SubscriptionType' => $SubscriptionType,
            );
        } else {
            $ANDSubscriptiontype = " AND s.SubscriptionType != :SubscriptionTypeNone";
            $params += array(
                ':SubscriptionTypeNone' => Subscription::SUBSCRIPTIONTYPE_NONE,
            );
        }

        // filter for taggings
        if (!empty($TaggedWith)) {
            $SELECTSubscriptionDate = "tt.SubscriptionDate";
            $JOINTagging = " INNER JOIN {tagging} tt ON tt.RelSubscriberID = s.RelSubscriberID AND tt.RelOwnerUserID = s.RelOwnerUserID AND tt.RelTagID IN (:RelTagID) ";
            $JOINTagging .= " INNER JOIN {tag} t ON tt.RelOwnerUserID = t.RelOwnerUserID AND t.TagID = tt.RelTagID AND (t.MultiValue = 0 OR tt.ReferenceID = r.ReferenceID) ";
            $params += array(
                ':RelTagID' => $TaggedWith,
            );
        } else {
            $SELECTSubscriptionDate = "s.SubscriptionDate";
            $JOINTagging = '';
        }

        $limit = empty($size) ? "" : " LIMIT $offset, $size";

        // build query
        $query = "SELECT DISTINCT(s.RelSubscriberID) AS SubscriberID, $SELECTSubscriptionDate FROM {subscription} s " .
            " INNER JOIN {subscription_reference} r ON r.RelOwnerUserID = s.RelOwnerUserID AND r.RelSubscriberID = s.RelSubscriberID AND r.SubscriptionType = s.SubscriptionType AND r.ContactInfo = s.ContactInfo " .
            $JOINTagging .
            " WHERE s.RelOwnerUserID = :RelOwnerUserID AND s.SubscriptionStatus = :SubscriptionStatus AND s.BounceType != :BounceType AND r.ReferenceID = :ReferenceID " .
            $ANDSubscriptiontype .
            " GROUP BY SubscriberID ORDER BY SubscriberID $limit";

        // build result set
        $ArraySubscriberIDs = array();

        $result = db_query($query, $params);
        while ($SubscriptionData = kt_fetch_array($result)) {
            if ($WithSubscriptionDate) {
                $ArraySubscriberIDs[] = $SubscriptionData;
            } else {
                $ArraySubscriberIDs[] = $SubscriptionData['SubscriberID'];
            }
        }

        return $ArraySubscriberIDs;
    }

    /**
     * retrieves subscribers from an audience that are valid for newsletter dispatch
     * @param $UserID
     * @param $AudienceID
     * @param $Offset
     * @param $Limit
     * @param $CanReceive
     * @return array
     */
    public static function RetrieveAudienceSubscribers($UserID, $AudienceID, $Offset = 0, $Limit = 0, $CanReceive = '')
    {
        // retrieve audience
        $Audience = VarSubscriberAudience::RetrieveAudience($UserID, $AudienceID);
        if (empty($Audience)) {
            return [];
        }
        // make use of subscriber search logic

        $fields = array(
            'main.RelSubscriberID AS SubscriberID',
            'main.ReferenceID',
            'sub.SubscriptionDate'
        );

        if ($CanReceive == 'email') {
            //only calculate active email subscriptions (subscribed and not bounced or soft bounced)
            $SearchMask = SubscribersSearch::SetSearchMaskToActiveEmailSubscriptions($Audience['Data']);
        } elseif ($CanReceive == 'sms') {
            //only calculate active sms subscriptions (subscribed and not bounced or soft bounced)
            $SearchMask = SubscribersSearch::SetSearchMaskToActiveSmsSubscriptions($Audience['Data']);
        } else {
            $SearchMask = $Audience['Data'];
        }

        $AudienceSearch = new SubscribersSearch($UserID, $SearchMask, $fields);

        // result should match return format of Subscribers::RetrieveTaggedSubscribers()
        // array(<index> => array("SubscriberID" => x, "SubscriptionDate" => y, "ReferenceID" => z)
        $Subscribers = $AudienceSearch->Search($Offset, $Limit);

        return $Subscribers;
    }

    /**
     * Returns total number of active subscribers
     *
     * @param $UserID
     *
     * @return int|false
     */
    public static function GetActiveTotal($UserID)
    {
        if (empty($UserID)) {
            return false;
        }

        return (int)db_query(
            "SELECT COUNT(DISTINCT(RelSubscriberID)) FROM {subscription} " .
            " WHERE RelOwnerUserID = :RelOwnerUserID AND SubscriptionStatus = :SubscriptionStatus AND "
            . "BounceType != :BounceType AND SubscriptionType != :SubscriptionTypeNone",
            array(
                ':RelOwnerUserID' => $UserID,
                ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
                ':BounceType' => Subscribers::BOUNCETYPE_HARD,
                ':SubscriptionTypeNone' => Subscription::SUBSCRIPTIONTYPE_NONE,
            )
        )->fetchField();
    }

    /**
     * Removes subscribers with given email addresses
     **/
    public static function RemoveSubscribersByEmailAddresses($UserID, $ArrayEmailAddresses)
    {
        // Check for required fields of this function - Start
        if (empty($UserID)) {
            return false;
        }
        if (empty($ArrayEmailAddresses)) {
            return false;
        }
        if (!is_array($ArrayEmailAddresses)) {
            $ArrayEmailAddresses = array($ArrayEmailAddresses);
        }
        // Check for required fields of this function - End

        foreach ($ArrayEmailAddresses as $EmailAddress) {
            $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, trim($EmailAddress));
            if ($SubscriberID) {
                Subscribers::RemoveSubscribersByID($UserID, $SubscriberID, Subscribers::DELETION_TYPE_OTHER);
            }
        }

        return true;
    }

    /**
     * Removes subscribers with given ids
     **/
    public static function RemoveSubscribersByID(
        $UserID,
        $ArraySubscriberIDs,
        $deletionType = self::DELETION_TYPE_OTHER
    ) {
        // Check for required fields of this function - Start
        if (empty($UserID)) {
            return false;
        }
        $account = user_load($UserID);
        if (empty($ArraySubscriberIDs)) {
            return false;
        }
        if (is_numeric($ArraySubscriberIDs)) {
            $ArraySubscriberIDs = array($ArraySubscriberIDs);
        }
        // Check for required fields of this function - End

        $logFileName = Core::GetDeletionLogFilename($UserID, $deletionType);
        foreach ($ArraySubscriberIDs as $Each) {
            $deleteAllData = Subscribers::SubscriberRequestedDeletion($UserID, $Each);
            if (!$deleteAllData) {
                // create backup file to possibly restore the subscribers after deletion
                Subscribers::LogSubscriberDeletion($UserID, $Each, $logFileName);
            }

            // remove bam assignments of subscriber
            // note: this must be called before custom fields are deleted
            VarBAMAssignment::DeleteAssignmentsOfSubscriber($UserID, $Each);

            kt_delete_rows(array(
                'RelOwnerUserID' => $UserID,
                'RelSubscriberID' => $Each
            ), '{tagging}');
            kt_delete_rows(array(
                'RelOwnerUserID' => $UserID,
                'RelSubscriberID' => $Each
            ), '{subscriber_history}');
            kt_delete_rows(array(
                'RelOwnerUserID' => $UserID,
                'RelSubscriberID' => $Each
            ), "{" . TransactionalQueue::TABLE_NAME . "}");
            kt_delete_rows(array(
                'RelOwnerUserID' => $UserID,
                'RelSubscriberID' => $Each
            ), "{" . AutoresponderQueue::TABLE_NAME . "}");
            kt_delete_rows(array(
                'RelOwnerUserID' => $UserID,
                'RelSubscriberID' => $Each
            ), "{" . NewsletterQueue::TABLE_NAME . "}");

            if ($deleteAllData) {
                //create job in subscriber_queue to remove the email address from all audiences
                //Note: facebook API errors will be stored in the history table with the old subscriber id
                //      -> that's why we need to remove all entries in {subscriber_history} first
                //Important: the subscriber needs to be retrieved for the email address, execute before deleting the subscriber
                // custom field values are required for a possible outbound request
                VarPrivacy::RemoveFromFacebookAudience($UserID, $Each);
                VarPrivacy::CreateOutboundJobOnDeletion($UserID, $Each);
            }

            if ($account) {
                VarSpamActivity::checkSubscriberDeletion($account, $Each);
            }

            kt_delete_rows(array(
                'RelOwnerUserID' => $UserID,
                'RelSubscriberID' => $Each
            ), '{subscription_reference}');
            kt_delete_rows(array(
                'RelOwnerUserID' => $UserID,
                'RelSubscriberID' => $Each
            ), '{subscription}');
            kt_delete_rows(array(
                'RelOwnerUserID' => $UserID,
                'RelSubscriberID' => $Each
            ), '{custom_field_values}');
            kt_delete_rows(array(
                'RelOwnerUserID' => $UserID,
                'SubscriberID' => $Each
            ), '{subscribers}');
            SubscriberDuplicates::deleteSubscriberRelatedDuplicates($UserID, $Each);
        }

        return true;
    }

    /**
     * Count pending subscribers
     *
     * @param int $userID
     * @param int $subscriberID
     * @param int $minOptInDate min opt-in-date to consinder (unix timestamp)
     *
     * @return int
     */
    public static function countPendingSubscriptions($userID, $subscriberID, $minOptInDate = 0)
    {
        $sql = 'SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :UserID AND RelSubscriberID = :SubscriberID AND OptInDate > :OptInDate';
        return (int)db_query($sql, [':UserID' => $userID, ':SubscriberID' => $subscriberID, ':OptInDate' => $minOptInDate])->fetchField();
    }

    /**
     * Removes subscribers with given ids
     **/
    public static function RemoveSubscribersOfUser($UserID)
    {
        // Check for required fields of this function - Start
        if (empty($UserID)) {
            return false;
        }
        // Check for required fields of this function - End

        $result = db_query(
            "SELECT SubscriberID FROM {subscribers} WHERE RelOwnerUserID = :RelOwnerUserID",
            array(':RelOwnerUserID' => $UserID)
        );
        $logFileName = Core::GetDeletionLogFilename($UserID, Subscribers::DELETION_TYPE_USER_CANCEL_HOOK);
        while ($Subscriber = kt_fetch_array($result)) {
            Subscribers::LogSubscriberDeletion($UserID, $Subscriber['SubscriberID'], $logFileName);
        }

        kt_delete_rows(array('RelOwnerUserID' => $UserID), '{subscribers}');
        kt_delete_rows(array('RelOwnerUserID' => $UserID), '{tagging}');
        kt_delete_rows(array('RelOwnerUserID' => $UserID), '{subscriber_history}');
        kt_delete_rows(array('RelOwnerUserID' => $UserID), "{" . TransactionalQueue::TABLE_NAME . "}");
        kt_delete_rows(array('RelOwnerUserID' => $UserID), "{" . AutoresponderQueue::TABLE_NAME . "}");
        kt_delete_rows(array('RelOwnerUserID' => $UserID), "{" . NewsletterQueue::TABLE_NAME . "}");
        kt_delete_rows(array('RelOwnerUserID' => $UserID), '{subscription_reference}');
        kt_delete_rows(array('RelOwnerUserID' => $UserID), '{subscription}');
        kt_delete_rows(array('RelOwnerUserID' => $UserID), '{custom_field_values}');
        SubscriberDuplicates::deleteUserRelatedDuplicates($UserID);

        return true;
    }

    /**
     * Removes subscribers with given ids
     *
     * @param int $deletionType @see Subscribers::DELETION_TYPE_* constants
     */
    public static function LogSubscriberDeletion($UserID, $SubscriberID, $LogFileName)
    {
        // Check for required fields of this function - Start
        if (empty($UserID)) {
            return false;
        }
        if (empty($SubscriberID)) {
            return false;
        }

        // get table prefix ('drupal_', but different in simpletest)
        $prefix = kt_db_prefix();

        $insert = '';

        $data = kt_fetch_array(
            db_query(
                "SELECT * FROM {subscribers} WHERE RelOwnerUserID = :RelOwnerUserID AND SubscriberID = :SubscriberID",
                array(
                    ':RelOwnerUserID' => $UserID,
                    ':SubscriberID' => $SubscriberID
                )
            )
        );
        if (empty($data)) {
            return false;
        }
        $fields = implode(", ", array_keys($data));
        $values = implode("', '", kt_escape_array($data));
        $insert .= "INSERT IGNORE INTO {$prefix}subscribers ($fields) VALUES ('$values');\n";

        $result = db_query(
            "SELECT * FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID
            )
        );
        while ($data = kt_fetch_array($result)) {
            // extract reference value to check
            $tagid = $data['RelTagID'];
            unset($data['RelTagID']);
            $fields = implode(", ", array_keys($data));
            $values = implode("', '", kt_escape_array($data));
            $insert .= "INSERT IGNORE INTO {$prefix}tagging ($fields, RelTagID) SELECT '$values', TagID FROM {$prefix}tag WHERE TagID = '$tagid' AND RelOwnerUserID = '$UserID';\n";
        }

        $result = db_query(
            "SELECT * FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID
            )
        );
        while ($data = kt_fetch_array($result)) {
            $fields = implode(", ", array_keys($data));
            $values = implode("', '", kt_escape_array($data));
            $insert .= "INSERT IGNORE INTO {$prefix}subscriber_history ($fields) VALUES ('$values');\n";
        }

        $result = db_query(
            "SELECT * FROM {" . TransactionalQueue::TABLE_NAME . "} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID
            )
        );
        while ($data = kt_fetch_array($result)) {
            // extract reference value to check
            $campaignid = $data['RelAutoResponderID'];
            unset($data['RelAutoResponderID']);
            $fields = implode(", ", array_keys($data));
            $values = implode("', '", kt_escape_array($data));
            $insert .= "INSERT IGNORE INTO {$prefix}" . TransactionalQueue::TABLE_NAME . " ($fields, RelAutoResponderID) SELECT '$values', CampaignID FROM {$prefix}campaigns WHERE CampaignID = '$campaignid' AND RelOwnerUserID = '$UserID';\n";
        }

        $result = db_query(
            "SELECT * FROM {" . AutoresponderQueue::TABLE_NAME . "} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID
            )
        );
        while ($data = kt_fetch_array($result)) {
            // extract reference value to check
            $campaignid = $data['RelAutoResponderID'];
            unset($data['RelAutoResponderID']);
            $fields = implode(", ", array_keys($data));
            $values = implode("', '", kt_escape_array($data));
            $insert .= "INSERT IGNORE INTO {$prefix}" . AutoresponderQueue::TABLE_NAME . " ($fields, RelAutoResponderID) SELECT '$values', CampaignID FROM {$prefix}campaigns WHERE CampaignID = '$campaignid' AND RelOwnerUserID = '$UserID';\n";
        }

        $result = db_query(
            "SELECT * FROM {" . NewsletterQueue::TABLE_NAME . "} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID
            )
        );
        while ($data = kt_fetch_array($result)) {
            // extract reference value to check
            $campaignid = $data['RelAutoResponderID'];
            unset($data['RelAutoResponderID']);
            $fields = implode(", ", array_keys($data));
            $values = implode("', '", kt_escape_array($data));
            $insert .= "INSERT IGNORE INTO {$prefix}" . NewsletterQueue::TABLE_NAME . " ($fields, RelAutoResponderID) SELECT '$values', CampaignID FROM {$prefix}campaigns WHERE CampaignID = '$campaignid' AND RelOwnerUserID = '$UserID';\n";
        }

        // --- log subscriptions
        $result = db_query(
            "SELECT * FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID
            )
        );
        while ($data = kt_fetch_array($result)) {
            $fields = implode(", ", array_keys($data));
            $values = implode("', '", kt_escape_array($data));
            $insert .= "INSERT IGNORE INTO {$prefix}subscription ($fields) VALUES ('$values');\n";
        }

        // --- log subscription_reference
        $result = db_query(
            "SELECT * FROM {subscription_reference} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID
            )
        );
        while ($data = kt_fetch_array($result)) {
            $fields = implode(", ", array_keys($data));
            $values = implode("', '", kt_escape_array($data));
            $insert .= "INSERT IGNORE INTO {$prefix}subscription_reference ($fields) VALUES ('$values');\n";
        }

        // --- log subscriber custom field values
        $result = db_query(
            "SELECT * FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID
            )
        );
        while ($data = kt_fetch_array($result)) {
            $fields = implode(", ", array_keys($data));
            $values = implode("', '", kt_escape_array($data));
            $insert .= "INSERT IGNORE INTO {$prefix}custom_field_values ($fields) VALUES ('$values');\n";
        }

        // append sql insert queries to e.g. /srv/www/klicktipp.vmdevel01.mailandbeyond.com/data/users/restore-250-20131224.sql
        $FileHandler = fopen($LogFileName, 'ab');
        if ($FileHandler) {
            fwrite($FileHandler, $insert);
            fclose($FileHandler);
        } else {
            watchdog(
                'subscriber deletion',
                'failed to write data to: !filename',
                array('!filename' => $LogFileName),
                WATCHDOG_ERROR
            );
            return false;
        }

        return true;
    }

    /**
     * Tag Subscriber
     *
     * @param $userID
     * @param $subscriberID
     * @param $tagID
     * @param $referenceID
     * @param bool $triggerAutoResponders
     * @param bool $instant
     *
     * @return bool
     */
    public static function TagSubscriber(
        $userID,
        $subscriberID,
        $tagID,
        $referenceID,
        $triggerAutoResponders = false,
        $instant = false
    ) {
        $usersReferenceIDs = null;

        // check if tag exists
        if (empty($tagID)) {
            return false;
        }
        $tag = Tag::RetrieveTag($userID, $tagID);
        if (empty($tag)) {
            return false;
        }

        if (!empty($tag['TagData'])) {
            $tagData = unserialize($tag['TagData']);
        }

        // Unsubscribe from behavior - we need to do the unsubscription before inserting the new tag
        if (!empty($tagData['OptInUnsubscribeFrom'])) {
            $isSourceTagMultiValue = !empty($tag['MultiValue']);

            foreach ($tagData['OptInUnsubscribeFrom'] as $UntagTagID) {
                $untagTag = Tag::RetrieveTag($userID, $UntagTagID);
                $isUntagTagMultiValue = empty($untagTag['MultiValue']) ? false : true;

                if ($isUntagTagMultiValue) {
                    if ($isSourceTagMultiValue) {
                        // cases covered:
                        // multi value --> multi value

                        $untagTagReferenceIDs = [$referenceID];
                    } else {
                        // cases covered:
                        // single value --> multi value

                        $untagTagReferenceIDs = ($usersReferenceIDs ??= Reference::getSubscriberRelatedIDs(
                            $userID,
                            $subscriberID
                        ));
                    }
                } else {
                    // cases covered:
                    // multi value --> single value
                    // single value --> single value
                    $untagTagReferenceIDs = [0];
                }

                foreach ($untagTagReferenceIDs as $untagTagReferenceID) {
                    Subscribers::UntagSubscriber($userID, $subscriberID, $untagTagReferenceID, $UntagTagID);
                }
            }
        }

        // single value taggings have all references, but need one place to be stored (ref 0)
        $intReferenceID = empty($tag['MultiValue']) ? 0 : $referenceID;

        db_query(
            "INSERT INTO {tagging} (RelSubscriberID, RelOwnerUserID, ReferenceID, RelTagID, SubscriptionDate) " .
            " VALUES (:RelSubscriberID, :RelOwnerUserID, :ReferenceID, :RelTagID, :SubscriptionDate) ON DUPLICATE KEY UPDATE SubscriptionDate = :SubscriptionDate",
            [
                ':RelSubscriberID' => $subscriberID,
                ':RelOwnerUserID' => $userID,
                ':ReferenceID' => $intReferenceID,
                ':RelTagID' => $tagID,
                ':SubscriptionDate' => time(),
            ]
        );

        // check if subscriber still exists (user could have been removed in the meantime in a different process)
        $subscriber = static::RetrieveSubscriber($userID, $subscriberID);
        if ($subscriber === false) {
            db_query(
                "DELETE FROM {tagging} WHERE RelSubscriberID = :RelSubscriberID AND RelOwnerUserID = :RelOwnerUserID " .
                "AND ReferenceID = :ReferenceID AND RelTagID = :RelTagID",
                [
                    ':RelSubscriberID' => $subscriberID,
                    ':RelOwnerUserID' => $userID,
                    ':ReferenceID' => $intReferenceID,
                    ':RelTagID' => $tagID,
                ]
            );

            return false;
        }

        // ensure there is a subscription reference for this tagging
        Subscriber::addReference($userID, $subscriberID, $referenceID);

        // Update statistics
        Subscribers::WriteHistory(
            $userID,
            $subscriberID,
            Subscribers::HISTORY_TAGGED,
            array('TagID' => $tagID, 'ReferenceID' => $referenceID)
        );

        // Trigger auto responders - Start {
        if ($triggerAutoResponders) {
            // autoresponders
            if ($instant && variable_get('klicktipp_instant_web_trigger_on', false)) {
                AutoResponders::RegisterAutoResponders(
                    $userID,
                    $referenceID,
                    $subscriberID,
                    $tagID
                );
            } else {
                SubscriberQueue::CreateQueueJob(
                    $userID,
                    $subscriberID,
                    SubscriberQueue::JOBTYPE_TRIGGER_BY_TAGGING,
                    [],
                    $referenceID,
                    $tagID
                );
            }


            // outbounds
            ToolOutbound::RegisterOutboundEvent($userID, $subscriberID, $tagID, $referenceID);
        }
        // Trigger auto responders - End }

        // Subscribe to behavior
        if (!empty($tagData['OptInSubscribeTo'])) {
            $isSourceTagMultiValue = !empty($tag['MultiValue']);

            foreach ($tagData['OptInSubscribeTo'] as $AddTagID) {
                $addTag = Tag::RetrieveTag($userID, $AddTagID);
                $isAddTagMultiValue = empty($addTag['MultiValue']) ? false : true;

                if ($isAddTagMultiValue) {
                    if ($isSourceTagMultiValue) {
                        // cases covered:
                        // multi value --> multi value

                        $addTagReferenceIDs = [$referenceID];
                    } else {
                        // cases covered:
                        // single value --> multi value

                        $addTagReferenceIDs = ($usersReferenceIDs ??= Reference::getSubscriberRelatedIDs(
                            $userID,
                            $subscriberID
                        ));
                    }
                } else {
                    // cases covered:
                    // multi value --> single value
                    // single value --> single value
                    $addTagReferenceIDs = [0];
                }

                foreach ($addTagReferenceIDs as $addTagReferenceID) {
                    Subscribers::TagSubscriber(
                        $userID,
                        $subscriberID,
                        $AddTagID,
                        $addTagReferenceID,
                        $triggerAutoResponders
                    );
                }
            }
        }

        // create assignments (idempotent action)
        VarBAMAssignment::CreateAssignmentsByTag($userID, $subscriberID, $tagID);

        return true;
    }

    /**
     * Untag Subscriber
     */
    public static function UntagSubscriber($UserID, $SubscriberID, $ReferenceID, $TagID)
    {
        // check if tag exists
        if (empty($TagID)) {
            return false;
        }
        $Tag = Tag::RetrieveTag($UserID, $TagID);
        if (empty($Tag)) {
            return false;
        }

        // single value taggings have all references, but are stored in one place (ref 0)
        $IntReferenceID = empty($Tag['MultiValue']) ? 0 : $ReferenceID;

        $result = kt_delete_rows(array(
            'RelOwnerUserID' => $UserID,
            'RelSubscriberID' => $SubscriberID,
            'RelTagID' => $TagID,
            'ReferenceID' => $IntReferenceID,
        ), '{tagging}');

        if ($result->rowCount() <= 0) {
            return false;
        }

        // Update statistics
        Subscribers::WriteHistory(
            $UserID,
            $SubscriberID,
            Subscribers::HISTORY_UNTAGGED,
            array('TagID' => $TagID, 'ReferenceID' => $ReferenceID)
        );

        // Update subscriber in auto responder queues - Start

        $Tagging = null; // we need tagging of subscriber (but get it only once, when its needed)

        // get all pending emails
        $result = db_query(
            "SELECT q.* FROM {" . AutoresponderQueue::TABLE_NAME . "} q " .
            " INNER JOIN {campaigns} c ON q.RelOwnerUserID = c.RelOwnerUserID AND q.RelAutoResponderID = c.CampaignID " .
            " WHERE q.RelOwnerUserID = :RelOwnerUserID AND q.RelSubscriberID = :RelSubscriberID AND q.ReferenceID = :ReferenceID " .
            " AND q.StatusEnum = :StatusEnum AND c.AutoResponderTriggerTypeEnum IN (:AutoResponderTriggerTypeEnum)",
            [
                ':RelOwnerUserID' => $UserID,
                ':ReferenceID' => $ReferenceID,
                ':RelSubscriberID' => $SubscriberID,
                ':StatusEnum' => TransactionEmails::STATUS_PENDING,
                ':AutoResponderTriggerTypeEnum' => Campaigns::$ArrayTriggerTypesAutoresponder,
            ]
        );
        while ($EachEmail = kt_fetch_array($result)) {
            // we need the complete unserialized dataset
            $ArrayAutoresponder = Campaigns::RetrieveCampaignByID((int) $EachEmail['RelAutoResponderID'], $UserID);

            if (
                AutoResponders::TerminateAutoResponder(
                    $ArrayAutoresponder,
                    $ReferenceID,
                    $SubscriberID,
                    $Tagging,
                    true
                ) == 0
            ) {
                AutoresponderQueue::RemoveFromQueue($EachEmail);
            }
        }
        // Update subscriber in auto responder queues - End

        // remove assignments
        VarBAMAssignment::RemoveAssignmentsByTag($UserID, $SubscriberID, $TagID);

        return true;
    }

    /*
     * Untag all subscribers from a tag (for tag deletion)
     */
    public static function UntagSubscribersOfTag($UserID, $TagID)
    {
        $result = db_query(
            "SELECT RelSubscriberID, ReferenceID FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID AND RelTagID = :RelTagID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelTagID' => $TagID,
            )
        );
        while ($Subscriber = kt_fetch_array($result)) {
            if (
                Subscribers::UntagSubscriber(
                    $UserID,
                    $Subscriber['RelSubscriberID'],
                    $Subscriber['ReferenceID'],
                    $TagID
                )
            ) {
                TransactionEmails::RegisterAutomations(
                    $UserID,
                    $Subscriber['RelSubscriberID'],
                    $Subscriber['ReferenceID']
                );
            }
        }
    }

    /**
     * Update full subscriber record with data of Email- or SMS-subscriptio
     * @param array $fullSubscriber
     * @param array $subscription
     */
    private static function mergeFullSubscriberWithSubscriptiion(array $fullSubscriber, array $subscription)
    {
        //    $result = db_query("SELECT s.*, mail.LastOpenDate, mail.LastOpenIP, mail.RelListID, mail.OptInDate, mail.OptInIP, mail.ConfirmationEmailDate, ".
        //      " mail.ContactInfo as EmailAddress, mail.SubscriptionStatus, mail.BounceType, ".
        //      " mail.SubscriptionDate, mail.SubscriptionIP, mail.UnsubscriptionDate, mail.UnsubscriptionIP, mail.SubscriptionReferrer, " .
        //      " sms.ContactInfo as PhoneNumber, sms.SubscriptionStatus as SMSSubscriptionStatus, sms.BounceType as SMSBounceType, ".
        //      " sms.SubscriptionDate as SMSSubscriptionDate, sms.UnsubscriptionDate as SMSUnsubscriptionDate, sms.SubscriptionReferrer as SubscriptionSMS ".
        //      " FROM {subscribers} s " .

        if ($subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
            $fullSubscriber['EmailAddress'] = $subscription['ContactInfo'];
            unset($subscription['ContactInfo']);
            $fullSubscriber = array_merge($fullSubscriber, $subscription);
        } elseif ($subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_SMS) {
            $fullSubscriber['PhoneNumber'] = $subscription['ContactInfo'];
            $fullSubscriber['SMSSubscriptionStatus'] = $subscription['SubscriptionStatus'];
            $fullSubscriber['SMSBounceType'] = $subscription['BounceType'];
            $fullSubscriber['SMSSubscriptionStatus'] = $subscription['SubscriptionStatus'];
            $fullSubscriber['SMSSubscriptionDate'] = $subscription['SubscriptionDate'];
            $fullSubscriber['SMSSubscriptionDate'] = $subscription['UnsubscriptionDate'];
            $fullSubscriber['SMSUnsubscriptionDate'] = $subscription['SubscriptionReferrer'];
        }
        return $fullSubscriber;
    }

    /**
     * validate input and return error messages for api subscriptions
     *
     * @param $Parameters
     * @param bool $AllErrors
     * @param int $UseSubscriptionProcess
     *
     * @return array
     * @use _klicktippapi_resource_subscriber_create, _klicktippapi_resource_subscriber_update
     */
    public static function SubscriptionParameterValidation(
        $Parameters,
        $AllErrors = true,
        $UseSubscriptionProcess = Subscribers::DOI_PROCESS_USE_FROM_SUBSCRIPTION
    ) {
        $errors = [];
        $warnings = [];

        //TODO multivalues: this will change to 'ContactInfo' + 'SubscriptionType' instead of 'EmailAddress' + 'PhoneNumber'

        // Default parameters
        $DefaultParameters = array(
            'UserID' => 0,
            'SubscriberID' => 0,
            'ReferenceID' => 0,
            'ListInformation' => array(),
            'OptInSubscribeTo' => 0,
            'EmailAddress' => '',
            'PhoneNumber' => '',
            'IPAddress' => self::FALLBACK_IP_ADDRESS,
            //OptIn IP address used for imported subscribers
            'SubscriptionReferrer' => '',
            'SubscriptionStatus' => 0,
            'SMSSubscriptionStatus' => 0,
            'OtherFields' => array(),
            'UpdateIfUnsubscribed' => false,
            'UpdatePhoneNumberIfUnsubscribed' => false,
            'SendConfirmationEmail' => false,
            'UpdateStatistics' => true,
            'TriggerAutoResponders' => false,
            'ListFormID' => 0,
            'ImportOptInDate' => 0,
            //OptInDate for imported subscribers from CSV file
            'ImportOptInIPAddress' => '',
            //OptInIP-Address for imported subscribers from CSV file
            'ImportConfirmationDate' => 0,
            //Confirmation Date for imported subscribers from CSV file
            'UpdatePrimarySubscription' => 0,
            // Updates primary Subscription, even if another subscription is specified (don't create new subscription with same reference=,
            'Optional' => 0,
            // is specified subscription optional for given reference
            'InstantTrigger' => false,
            // false = queue, true = instant execution
            'Imported' => null,
            // false => subscriber action
            // true => import (including add form)
            // null => auto-detection by listid and ip
        );
        $Parameters = array_merge($DefaultParameters, $Parameters);

        $SubscriberID = 0;
        $FullSubscriber = [];
        $ReferenceID = $Parameters['ReferenceID'];

        $SendConfirmationEmail = $Parameters['SendConfirmationEmail'];

        if (empty($Parameters['UserID'])) {
            // we assume the user has been checked before
            $errors[] = [
                'name' => 'userid',
                'reason' => t('user does not exist'),
                'ErrorCode' => KLICKTIPPAPI_ERROR_USER_NOT_FOUND
            ];
            // fatal error, we can't proceed
            return [false, $errors];
        }

        // get subscriber with email+sms information
        if (!empty($Parameters['SubscriberID'])) {
            $FullSubscriber = Subscribers::RetrieveFullSubscriber(
                $Parameters['UserID'],
                $Parameters['SubscriberID'],
                $ReferenceID
            );
            if (empty($FullSubscriber)) {
                $errors[] = [
                    'name' => 'subscriberid',
                    'reason' => t('subscriber does not exist'),
                    'ErrorCode' => Subscribers::SUBSCRIBE_ERROR_INVALID_SUBSCRIBER,
                ];
                // fatal error, we can't proceed
                return [false, $errors];
            };
            $SubscriberID = $FullSubscriber['SubscriberID'];
        }

        if (!is_array($Parameters['ListInformation']) || count($Parameters['ListInformation']) < 1) {
            if ($UseSubscriptionProcess == Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST) {
                // retrieve the list that the customer selected for change email
                // failover: list from subscription or default list
                $ArraySubscriberList = Lists::RetrieveChangeEmailList(
                    $Parameters['UserID'],
                    $FullSubscriber['RelListID']
                );
            } else {
                // if email address change is not expected, use list from subscription
                // failover (e.g. imports): default list
                $ArraySubscriberList = Lists::RetrieveListByIDOrDefault(
                    $Parameters['UserID'],
                    $FullSubscriber['RelListID']
                );
            }
        } else {
            $ArraySubscriberList = $Parameters['ListInformation'];
        }
        if (empty($ArraySubscriberList)) {
            $errors[] = [
                'name' => 'listid',
                'reason' => t('cannot be empty'),
                'ErrorCode' => KLICKTIPPAPI_ERROR_LIST_NOT_FOUND
            ];
            // fatal error, we can't proceed
            return [false, $errors];
        }

        $Parameters['Imported'] ??= (
            $ArraySubscriberList['ListID'] == 0 ||
            $Parameters['IPAddress'] === self::FALLBACK_IP_ADDRESS ||
            strpos($Parameters['IPAddress'], self::FALLBACK_IP_ADDRESS) === 0
        );

        //validate email address for update (not empty and changed)
        if (!empty($Parameters['EmailAddress'])) {
            //trim spaces since Subscribers::ValidateEmailAddress() -> filter_var() fails
            $Parameters['EmailAddress'] = trim($Parameters['EmailAddress']);

            if (empty($FullSubscriber['EmailAddress']) || $Parameters['EmailAddress'] != $FullSubscriber['EmailAddress']) {
                // check the new address
                if (!Subscribers::ValidateEmailAddress($Parameters['EmailAddress'], $Parameters['UserID'])) {
                    $errors[] = [
                        'name' => empty($Parameters['SubscriberID']) ? 'email' : 'newemail',
                        'reason' => t('must be a valid email address according to RFC 822'),
                        'ErrorCode' => KLICKTIPPAPI_ERROR_EMAILADDRESS_VALIDATION_FAILED
                    ];
                    if (!$AllErrors) {
                        return [false, $errors];
                    }
                }

                $ktastClient = new KtastClient(HttpClient::create());
                if (
                    $ktastClient->isSubscriberBlocked($Parameters['EmailAddress']) ||
                    db_query(
                        "SELECT 1 FROM {suppression_list} WHERE EmailAddress = :EmailAddress " .
                        " AND (SuppressionSourceEnum = :SpamTrap OR " .
                        " (SuppressionSourceEnum = :Account AND UserID = :UserID) " .
                        " OR SuppressionSourceEnum = :SpamBot) LIMIT 0,1",
                        array(
                            ':EmailAddress' => $Parameters['EmailAddress'],
                            ':SpamTrap' => SuppressionList::SUPPRESSION_LIST_SOURCE_SPAMTRAP,
                            ':Account' => SuppressionList::SUPPRESSION_LIST_SOURCE_ACCOUNT,
                            ':SpamBot' => SuppressionList::SUPPRESSION_LIST_SOURCE_SPAMBOT,
                            ':UserID' => $Parameters['UserID'],
                        )
                    )->fetchField()
                ) {
                    $errors[] = [
                        'name' => empty($Parameters['SubscriberID']) ? 'email' : 'newemail',
                        'reason' => t('is not acceptable for subscription'),
                        'ErrorCode' => KLICKTIPPAPI_ERROR_EMAILADDRESS_CHANGE_NOT_ALLOWED
                    ];
                    if (!$AllErrors) {
                        return [false, $errors];
                    }
                }
            }

            // check if subscriber exists
            $EmailSubscription = Subscription::RetrieveSubscriptionByEmailAddress(
                $Parameters['UserID'],
                $Parameters['EmailAddress']
            );
            if (!empty($EmailSubscription)) {
                if (!empty($SubscriberID)) {
                    // dont allow if email address already exists
                    if ($EmailSubscription['SubscriberID'] != $SubscriberID) {
                        $errors[] = [
                            'name' => empty($Parameters['SubscriberID']) ? 'email' : 'newemail',
                            'reason' => t('subscriber with this email address already exists'),
                            'ErrorCode' => KLICKTIPPAPI_ERROR_EMAILADDRESS_EXISTS
                        ];
                        if (!$AllErrors) {
                            return [false, $errors];
                        }
                    }
                } else {
                    $FullSubscriber = Subscribers::RetrieveFullSubscriberAndRestoreIfMissing(
                        $Parameters['UserID'],
                        $EmailSubscription['SubscriberID'],
                        $ReferenceID
                    );
                    if (empty($FullSubscriber)) {
                        $errors[] = [
                            'name' => 'subscriberid',
                            'reason' => t('subscriber does not exist and could not be restored'),
                            'ErrorCode' => Subscribers::SUBSCRIBE_ERROR_INVALID_SUBSCRIBER,
                        ];
                        // fatal error, we can't proceed
                        return [false, $errors];
                    };
                    $SubscriberID = $FullSubscriber['SubscriberID'];
                }
                if (!$Parameters['UpdatePrimarySubscription']) {
                    $FullSubscriber = static::mergeFullSubscriberWithSubscriptiion($FullSubscriber, $EmailSubscription);
                }
            }
        }

        //validate phone number for update (not empty and changed)
        if (!empty($Parameters['PhoneNumber'])) {
            $Parameters['PhoneNumber'] = Subscribers::FormatSMSNumber($Parameters['PhoneNumber']);

            if (empty($Parameters['PhoneNumber'])) {
                //Note: if a number is given, it must be valid, otherwise the formatted number will be empty -> delete
                $errors[] = [
                    'name' => empty($Parameters['SubscriberID']) ? 'smsnumber' : 'newsmsnumber',
                    'reason' => t('must be a valid mobile phone number in E.164 phone number format'),
                    'ErrorCode' => Subscribers::SUBSCRIBE_ERROR_INVALID_PHONE_NUMBER
                ];
                if (!$AllErrors) {
                    return [false, $errors];
                }
            }

            // check if subscriber exists
            $SmsSubscription = Subscription::RetrieveSubscriptionByPhoneNumber(
                $Parameters['UserID'],
                $Parameters['PhoneNumber']
            );
            if (!empty($SmsSubscription)) {
                if (!empty($SubscriberID)) {
                    // dont allow if subscriber already exists
                    if ($SmsSubscription['SubscriberID'] != $SubscriberID) {
                        $errors[] = [
                            'name' => empty($Parameters['SubscriberID']) ? 'smsnumber' : 'newsmsnumber',
                            'reason' => t('subscriber with this smsnumber already exists'),
                            'ErrorCode' => Subscribers::SUBSCRIBE_ERROR_DUPLICATE_PHONE_NUMBER
                        ];
                        if (!$AllErrors) {
                            return [false, $errors];
                        }
                    }
                } else {
                    $FullSubscriber = Subscribers::RetrieveFullSubscriberAndRestoreIfMissing(
                        $Parameters['UserID'],
                        $SmsSubscription['SubscriberID'],
                        $ReferenceID
                    );
                    $SubscriberID = $FullSubscriber['SubscriberID'];
                }

                if (!$Parameters['UpdatePrimarySubscription']) {
                    $FullSubscriber = static::mergeFullSubscriberWithSubscriptiion($FullSubscriber, $SmsSubscription);
                }
            }
        }

        // custom field validation
        $CustomFieldInformation = array();
        $CustomFieldsChanged = array();
        if (count($Parameters['OtherFields']) > 0) {
            foreach ($Parameters['OtherFields'] as $Key => $Value) {
                if (substr($Key, 0, 11) == 'CustomField') {
                    $CustomFieldID = substr($Key, 11);
                    $CustomField = CustomFields::RetrieveCustomField($CustomFieldID, $Parameters['UserID']);
                    if (!empty($CustomField)) {
                        // Custom fields: check validation - Start
                        $ArrayReturn = CustomFields::ValidateCustomFieldValue($CustomField, $Value);
                        if (!$ArrayReturn[0]) {
                            $errors[] = [
                                'name' => "field{$CustomField['CustomFieldID']}",
                                'reason' => t(
                                    KlickTippAPIUtils::GetCustomFieldValidationErrorMessage(
                                        $CustomField['FieldTypeEnum']
                                    )
                                ),
                                'ErrorCode' => Subscribers::SUBSCRIBE_ERROR_CUSTOMFIELD_DATA,
                                'ErrorData' => is_numeric(
                                    $CustomFieldID
                                ) ? $CustomField[$CustomFieldID]['FieldName'] : t(
                                    $CustomField[$CustomFieldID]['FieldName']
                                ),
                            ];
                            if (!$AllErrors) {
                                return [false, $errors];
                            }
                        } else {
                            $CustomFieldInformation[$CustomFieldID] = $CustomField;
                            $CustomFieldsChanged[$CustomFieldID] = $Value;
                        }
                    }
                }
            }
        }

        if (!empty($Parameters['OptInSubscribeTo'])) {
            // specified tag must exist and have valid category
            $ArrayTag = Tag::RetrieveTag($Parameters['UserID'], $Parameters['OptInSubscribeTo']);
            if (empty($ArrayTag)) {
                $errors[] = [
                    'name' => 'tagid',
                    'reason' => t('tag does not exist'),
                    'ErrorCode' => KLICKTIPPAPI_ERROR_TAG_NOT_FOUND,
                ];
                if (!$AllErrors) {
                    return [false, $errors];
                }
            }
        }

        // EmailAddress Status

        $OldSubscriptionStatus = empty($FullSubscriber['SubscriptionStatus']) ? 0 : $FullSubscriber['SubscriptionStatus'];

        $isSubscriptionNew = !empty($Parameters['EmailAddress']) && $Parameters['EmailAddress'] != $FullSubscriber['EmailAddress'] && !$Parameters['UpdatePrimarySubscription'];
        if ($isSubscriptionNew) {
            $setNewSubscriptionStatus = true;
        } elseif ($OldSubscriptionStatus == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED) {
            // different than the name implies UpdateIfUnsubscribed not only updates but also triggers resubscription
            $setNewSubscriptionStatus = $Parameters['UpdateIfUnsubscribed'];
        } else {
            $setNewSubscriptionStatus = $OldSubscriptionStatus != Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
        }

        if ($setNewSubscriptionStatus) {
            if (empty($Parameters['SubscriptionStatus'])) {
                if ($ArraySubscriberList['OptInModeEnum'] == Lists::OPTIN_MODE_SINGLE) {
                    $NewSubscriptionStatus = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
                } else {
                    $NewSubscriptionStatus = Subscribers::SUBSCRIPTIONSTATUS_OPTIN;
                }
            } else {
                $NewSubscriptionStatus = $Parameters['SubscriptionStatus'];
            }
        } else {
            $NewSubscriptionStatus = 0;
        }

        // Decide about sending confirmation email
        if ($SendConfirmationEmail) {
            if ($UseSubscriptionProcess == Subscribers::DOI_PROCESS_USE_SINGLE_OPTIN) {
                // single-optin
                $SendConfirmationEmail = false;
            } elseif ($ArraySubscriberList['OptInModeEnum'] == Lists::OPTIN_MODE_SINGLE) {
                // single-optin
                $SendConfirmationEmail = false;
            } elseif (empty($Parameters['EmailAddress'])) {
                // to send a confirmation email, we need to handle a new email address
                $SendConfirmationEmail = false;
            } elseif (!empty($ArraySubscriberList['ReSendConfirmationEmail'])) {
                //if checkbox is activated in DOI process, send confirmation email on every subscribe
                //implicit:$SendConfirmationEmail = TRUE;
            } elseif ($OldSubscriptionStatus == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED && $Parameters['EmailAddress'] == $FullSubscriber['EmailAddress']) {
                // to send a confirmation email, we need to handle a new email address
                $SendConfirmationEmail = false;
            } elseif ($UseSubscriptionProcess == Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST) {
                // send confirmation mail if user changed it
                //implicit:$SendConfirmationEmail = TRUE;
            } elseif (
                in_array(
                    $NewSubscriptionStatus,
                    [Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED]
                )
            ) {
                // do not send confirmation email, if we have a status change to non optin
                $SendConfirmationEmail = false;
            } elseif (date('Ymd', $FullSubscriber['ConfirmationEmailDate'] ?? 0) == date('Ymd')) {
                // only send confirmation mail if not already send one today (for same subscription)
                $SendConfirmationEmail = false;
            }
        }

        // check for update of unsubscribed email allowed
        if (!empty($FullSubscriber['EmailAddress']) && $Parameters['UpdateIfUnsubscribed'] == false && $FullSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED) {
            $SendConfirmationEmail = false;
            $warnings[] = [
                'name' => empty($Parameters['SubscriberID']) ? 'email' : 'newemail',
                'reason' => t('subscriber is unsubscribed from email'),
                'ErrorCode' => Subscribers::SUBSCRIBE_ERROR_UNSUBSCRIBED_EMAIL_ADDRESS
            ];
        }

        // PhoneNumber Status

        $OldSMSSubscriptionStatus = empty($FullSubscriber['SMSSubscriptionStatus']) ? 0 : $FullSubscriber['SMSSubscriptionStatus'];
        $NewSMSSubscriptionStatus = 0;
        if (
            $OldSMSSubscriptionStatus != Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED ||
            // in case below we are going to create a new subscription
            (!empty($Parameters['PhoneNumber']) && $Parameters['PhoneNumber'] != $FullSubscriber['PhoneNumber'] && !$Parameters['UpdatePrimarySubscription'])
        ) {
            if (empty($Parameters['SMSSubscriptionStatus'])) {
                $NewSMSSubscriptionStatus = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
            } else {
                $NewSMSSubscriptionStatus = $Parameters['SMSSubscriptionStatus'];
            }
        }

        // check for update of unsubscribed sms allowed
        if (!empty($FullSubscriber['PhoneNumber']) && $Parameters['UpdatePhoneNumberIfUnsubscribed'] == false && $FullSubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED) {
            $errors[] = [
                'name' => empty($Parameters['SubscriberID']) ? 'smsnumber' : 'newsmsnumber',
                'reason' => t('subscriber is unsubscribed from sms'),
                'ErrorCode' => Subscribers::SUBSCRIBE_ERROR_UNSUBSCRIBED_PHONE_NUMBER
            ];
            if (!$AllErrors) {
                return [false, $errors];
            }
        }

        // prepare referrer
        if (empty($Parameters['SubscriptionReferrer'])) {
            $SubscriptionReferrer = empty($FullSubscriber['SubscriptionReferrer']) ? '' : $FullSubscriber['SubscriptionReferrer'];
        } else {
            $SubscriptionReferrer = substr($Parameters['SubscriptionReferrer'], 0, 1024);
        };

        if (empty($Parameters['IPAddress'])) {
            $Parameters['IPAddress'] = $FullSubscriber['SubscriptionIP'] ?: $DefaultParameters['IPAddress'];
        }

        // prepare confirmation/optin date and ip
        if (empty($FullSubscriber['EmailAddress']) || $Parameters['EmailAddress'] != $FullSubscriber['EmailAddress'] || $NewSubscriptionStatus == Subscribers::SUBSCRIPTIONSTATUS_OPTIN) {
            $OptInDate = time();
            $OptInIP = $Parameters['IPAddress'];
        } else {
            $OptInDate = $FullSubscriber['OptInDate'];
            $OptInIP = $FullSubscriber['OptInIP'];
        }

        $ConfirmationDate = $OptInDate;

        if ($ArraySubscriberList['ListID'] == 0) {
            //only set via CSV import

            //we have a confirmation date from the CSV file
            if (!empty($Parameters['ImportConfirmationDate'])) {
                $ConfirmationDate = $Parameters['ImportConfirmationDate'];
            }

            if (!empty($Parameters['ImportOptInDate'])) {
                //we have an opt-in date from the CSV file
                $OptInDate = $Parameters['ImportOptInDate'];
            } else {
                //we do not have an opt-in date, use the confirmation date
                $OptInDate = $ConfirmationDate;
            }

            //use opt-in ip address from csv file or the subscription ip address
            if (!empty($Parameters['ImportOptInIPAddress']) && $NewSubscriptionStatus != Subscribers::SUBSCRIPTIONSTATUS_OPTIN) {
                $OptInIP = $Parameters['ImportOptInIPAddress'];
            }
        }

        // override parameters with calculated values
        $Parameters = array_merge($Parameters, [
            'SubscriberID' => $SubscriberID,
            'FullSubscriber' => $FullSubscriber,
            'ListInformation' => $ArraySubscriberList,
            'UpdateIfUnsubscribed' => isset($Parameters['UpdateIfUnsubscribed']) ? $Parameters['UpdateIfUnsubscribed'] : true,
            'UpdatePhoneNumberIfUnsubscribed' => isset($Parameters['UpdatePhoneNumberIfUnsubscribed']) ? $Parameters['UpdatePhoneNumberIfUnsubscribed'] : true,
            'SubscriptionReferrer' => $SubscriptionReferrer,
            'OldSubscriptionStatus' => $OldSubscriptionStatus,
            'NewSubscriptionStatus' => $NewSubscriptionStatus,
            'SendConfirmationEmail' => $SendConfirmationEmail,
            'OldSMSSubscriptionStatus' => $OldSMSSubscriptionStatus,
            'NewSMSSubscriptionStatus' => $NewSMSSubscriptionStatus,
            'OptInDate' => $OptInDate, // email only
            'OptInIP' => $OptInIP, // email only
            'ConfirmationDate' => $ConfirmationDate, // email only
            'CustomFieldInformation' => $CustomFieldInformation,
            'CustomFieldsChanged' => $CustomFieldsChanged,
            'UpdatePrimarySubscription' => $Parameters['UpdatePrimarySubscription'],
            'Optional' => $Parameters['Optional']
        ]);

        if (empty($errors)) {
            return [true, $Parameters, $warnings];
        } else {
            return [false, $errors];
        }
    }

    /**
     * Add Subscriber
     *
     * Note: $Parameters['EmailAddress'] is punycoded.
     *
     * @param $Parameters
     *
     * @return array
     */
    public static function Subscribe($Parameters)
    {
        // make sure we handle email addresses all in lower case (case insensitive)
        if (!empty($Parameters['EmailAddress'])) {
            $Parameters['EmailAddress'] = strtolower($Parameters['EmailAddress']);
        }
        // check and calc parameters
        $result = Subscribers::SubscriptionParameterValidation($Parameters, false);
        if (!$result[0]) {
            $error = current($result[1]);
            return array(
                false,
                $error['ErrorCode'],
                $error['ErrorData'],
                $error['reason'],
            );
        }
        $Parameters = $result[1];
        $warnings = $result[2];

        $SubscriberID = $Parameters['SubscriberID'];
        $FullSubscriber = $Parameters['FullSubscriber'];
        $ReferenceID = $Parameters['ReferenceID'];

        $DoesSubscriberExist = !empty($SubscriberID);

        // Parameter validation - End }

        $NewEmailSubscription = false;
        $NewSMSSubscription = false;

        // {subscriber}

        if ($DoesSubscriberExist) {
            $ReturnSubscriberID = $SubscriberID;
        } else {
            $SubscriberValues = [
                'RelOwnerUserID' => $Parameters['UserID'],
            ];

            // Add to the database
            try {
                $ReturnSubscriberID = db_insert('subscribers')
                    ->fields($SubscriberValues)
                    ->execute();
            } catch (Exception $e) {
                // this is some kind of race, as insert failed, but the subscriber didnt exist microseconds before
                $error = [
                    '!Parameters' => $Parameters,
                    '!SubscriberValues' => $SubscriberValues,
                    '!Exception' => $e,
                ];
                Errors::unexpected("db_insert failed in Subscribers::Subscribe", $error);
                // we cant continue here and hope the user gets the other (racing) response
                return [
                    false,
                    Subscribers::SUBSCRIBE_ERROR_INTERNAL,
                    'db_insert'
                ];
            };
        }

        // update subscriber email address

        $ContactInfoEmail = [];

        if (!empty($Parameters['EmailAddress'])) {
            if (!empty($FullSubscriber['EmailAddress']) && ($Parameters['UpdatePrimarySubscription'] || $FullSubscriber['EmailAddress'] == $Parameters['EmailAddress'])) {
                // the email address subscription status will be updated

                if ($Parameters['OldSubscriptionStatus'] != Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED && !empty($Parameters['NewSubscriptionStatus'])) {
                    $ContactInfoEmail = [
                        // unchanged
                        ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_EMAIL,
                        ':ContactInfo' => $FullSubscriber['EmailAddress'],
                        ':UnsubscriptionIP' => $FullSubscriber['UnsubscriptionIP'],
                        ':UnsubscriptionDate' => $FullSubscriber['UnsubscriptionDate'],
                        // new status
                        ':RelListID' => $Parameters['NewSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED ? $FullSubscriber['RelListID'] : $Parameters['ListInformation']['ListID'],
                        ':OptInDate' => $Parameters['OptInDate'],
                        ':OptInIP' => $Parameters['OptInIP'],
                        ':SubscriptionStatus' => $Parameters['NewSubscriptionStatus'],
                        ':SubscriptionDate' => $Parameters['NewSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED ? time(
                        ) : $FullSubscriber['SubscriptionDate'],
                        ':BounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
                        ':SubscriptionReferrer' => $Parameters['SubscriptionReferrer'],
                        ':SubscriptionIP' => $Parameters['IPAddress'],
                        ':ConfirmationEmailDate' => $Parameters['SendConfirmationEmail'] ? time(
                        ) : $FullSubscriber['ConfirmationEmailDate'],
                    ];

                    if ($Parameters['NewSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED) {
                        $NewEmailSubscription = true;
                    }
                } else {
                    // enough information to update subscription_reference
                    $ContactInfoEmail = [
                        ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_EMAIL,
                        ':ContactInfo' => $FullSubscriber['EmailAddress'],
                    ];
                }
            } else {
                //a new email address will be subscribed

                $ContactInfoEmail = [
                    ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_EMAIL,
                    ':ContactInfo' => $Parameters['EmailAddress'],
                    ':SubscriptionStatus' => $Parameters['NewSubscriptionStatus'],
                    ':SubscriptionDate' => ($Parameters['NewSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN) ? 0 : $Parameters['ConfirmationDate'],
                    ':BounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
                    ':RelListID' => $Parameters['ListInformation']['ListID'],
                    ':OptInDate' => $Parameters['OptInDate'],
                    ':OptInIP' => $Parameters['OptInIP'],
                    ':SubscriptionReferrer' => $Parameters['SubscriptionReferrer'],
                    ':SubscriptionIP' => $Parameters['IPAddress'],
                    ':ConfirmationEmailDate' => $Parameters['SendConfirmationEmail'] ? time() : 0,
                    ':UnsubscriptionIP' => self::FALLBACK_IP_ADDRESS,
                    ':UnsubscriptionDate' => 0,
                ];

                if ($Parameters['NewSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED) {
                    $NewEmailSubscription = true;
                }

                if ($Parameters['Imported']) {
                    Subscribers::WriteHistory(
                        $Parameters['UserID'],
                        $ReturnSubscriberID,
                        Subscribers::HISTORY_IMPORT,
                        $Parameters['EmailAddress']
                    );
                }
            }
        }

        // update subscriber phone number

        $ContactInfoSMS = [];

        if (!empty($Parameters['PhoneNumber'])) {
            if (!empty($FullSubscriber['PhoneNumber']) && ($Parameters['UpdatePrimarySubscription'] || $FullSubscriber['PhoneNumber'] == $Parameters['PhoneNumber'])) {
                // the phone number subscription will be updated

                if ($Parameters['OldSMSSubscriptionStatus'] != Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED && !empty($Parameters['NewSMSSubscriptionStatus'])) {
                    //either PENDING or UNSUBSCRIBED

                    $ContactInfoSMS = [
                        // unchanged
                        ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_SMS,
                        ':ContactInfo' => $FullSubscriber['PhoneNumber'],
                        ':UnsubscriptionIP' => $FullSubscriber['UnsubscriptionIP'],
                        ':UnsubscriptionDate' => $FullSubscriber['UnsubscriptionDate'],
                        // new status
                        ':RelListID' => $Parameters['NewSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED ? $FullSubscriber['RelListID'] : $Parameters['ListInformation']['ListID'],
                        ':OptInDate' => $Parameters['OptInDate'],
                        ':OptInIP' => $Parameters['OptInIP'],
                        ':SubscriptionStatus' => $Parameters['NewSMSSubscriptionStatus'],
                        ':SubscriptionDate' => time(),
                        ':BounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
                        ':SubscriptionReferrer' => $Parameters['SubscriptionReferrer'],
                        ':SubscriptionIP' => $Parameters['IPAddress'],
                        ':ConfirmationEmailDate' => 0,
                    ];

                    if ($Parameters['NewSMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED) {
                        $NewSMSSubscription = true;
                    }
                } else {
                    // enough information to update subscription_reference
                    $ContactInfoSMS = [
                        ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_SMS,
                        ':ContactInfo' => $FullSubscriber['PhoneNumber'],
                    ];
                }
            } else {
                //a new phone number will be subscribed

                //add phone number to database
                $ContactInfoSMS = [
                    ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_SMS,
                    ':ContactInfo' => $Parameters['PhoneNumber'],
                    ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
                    ':SubscriptionDate' => time(),
                    ':BounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
                    ':RelListID' => $Parameters['ListInformation']['ListID'],
                    ':OptInDate' => time(),
                    ':OptInIP' => $Parameters['IPAddress'],
                    ':SubscriptionReferrer' => $Parameters['SubscriptionReferrer'],
                    ':SubscriptionIP' => $Parameters['IPAddress'],
                    ':ConfirmationEmailDate' => 0,
                    ':UnsubscriptionIP' => self::FALLBACK_IP_ADDRESS,
                    ':UnsubscriptionDate' => 0,
                ];

                $NewSMSSubscription = true;
                /*TODO
                if ($Parameters['ListInformation']['ListID'] == 0) {
                Subscribers::WriteHistory($Parameters['UserID'], $ReturnSubscriberID, Subscribers::HISTORY_IMPORT, $Parameters['PhoneNumber']);
                }
                */
            }
        }

        /////////////////////////////////

        // {subscription}
        // insert/update subscription

        $ContactInfos = array_filter([$ContactInfoEmail, $ContactInfoSMS]);

        // If neither email nor phone number are specified AND subscriber is new, we create an empty subscription
        // if subscriber already exist, we don't create one, because the intention could by just to update custom field or tags
        if (empty($ContactInfos) && empty($Parameters['SubscriberID'])) {
            $ContactInfos[] = [
                ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_NONE,
                ':ContactInfo' => $ReturnSubscriberID,
                ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
                ':SubscriptionDate' => time(),
                ':BounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
                ':RelListID' => $Parameters['ListInformation']['ListID'],
                ':OptInDate' => time(),
                ':OptInIP' => $Parameters['IPAddress'],
                ':SubscriptionReferrer' => $Parameters['SubscriptionReferrer'],
                ':SubscriptionIP' => $Parameters['IPAddress'],
                ':ConfirmationEmailDate' => 0,
                ':UnsubscriptionIP' => self::FALLBACK_IP_ADDRESS,
                ':UnsubscriptionDate' => 0
            ];
        }


        foreach ($ContactInfos as $ContactInfo) {
            $SubscriptionValues = array_merge([
                ':RelOwnerUserID' => $Parameters['UserID'],
                ':RelSubscriberID' => $ReturnSubscriberID,
            ], $ContactInfo);

            // if :SubscriptionStatus is set, there is a subscription to create/update.
            // Otherwise we create/update only subscription-reference relation
            if (isset($ContactInfo[':SubscriptionStatus'])) {
                try {
                    // subscription
                    db_query(
                        "INSERT INTO {subscription} " .
                        " (RelOwnerUserID,  RelSubscriberID,   SubscriptionType, ContactInfo,  RelListID,  SubscriptionStatus,  OptInDate,  OptInIP, " .
                        " BounceType,  SubscriptionDate,   SubscriptionIP,  ConfirmationEmailDate,  UnsubscriptionDate, UnsubscriptionIP,  SubscriptionReferrer) " .
                        " VALUES " .
                        " (:RelOwnerUserID, :RelSubscriberID, :SubscriptionType, :ContactInfo, :RelListID, :SubscriptionStatus, :OptInDate, :OptInIP, " .
                        " :BounceType, :SubscriptionDate, :SubscriptionIP, :ConfirmationEmailDate, :UnsubscriptionDate, :UnsubscriptionIP, :SubscriptionReferrer) " .
                        " ON DUPLICATE KEY UPDATE " .
                        " RelListID = :RelListID, SubscriptionStatus = :SubscriptionStatus, BounceType = :BounceType, ".
                        " SubscriptionDate = :SubscriptionDate, SubscriptionIP = :SubscriptionIP, ConfirmationEmailDate = :ConfirmationEmailDate, " .
                        " UnsubscriptionDate = :UnsubscriptionDate, UnsubscriptionIP = :UnsubscriptionIP, SubscriptionReferrer = :SubscriptionReferrer ",
                        $SubscriptionValues
                    );
                    // subscription_reference
                } catch (Exception $e) {
                    // this is some kind of race, as insert failed, but the subscriber didnt exist microseconds before
                    $error = [
                        '!Parameters' => $Parameters,
                        '!SubscriptionValues' => $SubscriptionValues,
                        '!Exception' => $e,
                    ];
                    Errors::unexpected("INSERT on subscription failed in Subscribers::Subscribe", $error);
                    // we cant continue here and hope the user gets the other (racing) response
                    return [
                        false,
                        Subscribers::SUBSCRIBE_ERROR_INTERNAL,
                        $ContactInfoEmail,
                    ];
                };
            }
            Subscriber::assignSubscriptionToReference(
                $SubscriptionValues[':RelOwnerUserID'],
                $SubscriptionValues[':RelSubscriberID'],
                $SubscriptionValues[':SubscriptionType'],
                $SubscriptionValues[':ContactInfo'],
                $ReferenceID,
                $Parameters['Optional']
            );
        }

        // update custom field data
        if (!empty($Parameters['CustomFieldsChanged'])) {
            foreach ($Parameters['CustomFieldsChanged'] as $CustomFieldID => $Value) {
                CustomFields::UpdateCustomFieldData(
                    $Parameters['UserID'],
                    $ReturnSubscriberID,
                    $ReferenceID,
                    $Parameters['CustomFieldInformation'][$CustomFieldID],
                    $Value
                );
            }
        }

        if (!$DoesSubscriberExist) {
            if (SubscriberDuplicateDetectionIpAddress::isActive()) {
                SubscriberDuplicateDetectionIpAddress::detectAndStore(
                    [
                        'userID' => $Parameters['UserID'],
                        'subscriberID' => $ReturnSubscriberID,
                        'ip' => $Parameters['IPAddress']
                    ]
                );
            }
            if (!empty($ContactInfoEmail) && SubscriberDuplicateDetectionEmailSimilarity::isActive()) {
                SubscriberDuplicateDetectionEmailSimilarity::triggerDetecttion(
                    [
                        'userID' => $Parameters['UserID'],
                        'subscriberID' => $ReturnSubscriberID,
                        'email' => $ContactInfoEmail[':ContactInfo']
                    ]
                );
            }
        }

        if ($Parameters['UpdateStatistics'] && ($NewEmailSubscription || $NewSMSSubscription)) {
            foreach ($ContactInfos as $ContactInfo) {
                // count subscribe as open if this is a confirmed new subscription
                Statistics::RegisterLastOpen(
                    $Parameters['UserID'],
                    $ReturnSubscriberID,
                    $ContactInfo[':SubscriptionType'],
                    $ContactInfo[':ContactInfo'],
                    $Parameters['IPAddress'],
                    $Parameters['Imported']
                );
            }
        }

        // Send confirmation email - Start {
        if ($Parameters['SendConfirmationEmail']) {
            $NewFullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields(
                $Parameters['UserID'],
                $ReturnSubscriberID,
                $ReferenceID
            );
            if (empty($NewFullSubscriberWithFields)) {
                return array(
                    false,
                    Subscribers::SUBSCRIBE_ERROR_INVALID_LIST_INFORMATION,
                    'RetrieveFullSubscriberWithFields'
                );
            }

            if (
                !Emails::SendOptInEmail(
                    $Parameters['ListInformation'],
                    $NewFullSubscriberWithFields,
                    $ReferenceID,
                    $Parameters['UserID'],
                    $Parameters['ListFormID']
                )
            ) {
                return array(
                    false,
                    Subscribers::SUBSCRIBE_ERROR_INVALID_LIST_INFORMATION,
                    'SendOptInEmail'
                );
            }
        }
        // Send confirmation email - End }

        // 'UpdateStatistics' is always true, except in UpdateSubscription (no new subscriber)
        if ($Parameters['UpdateStatistics']) {
            $stats = array();

            if ($NewEmailSubscription) {
                $stats['TotalSubscriptions'] = 1;
            }
            if ($NewSMSSubscription) {
                $stats['TotalSMSSubscriptions'] = 1;
            }

            if (!empty($stats)) {
                Statistics::UpdateActivityStatistics($Parameters['UserID'], $stats);
            }
        }

        // re-tagging + history
        if ($Parameters['TriggerAutoResponders']) {
            if ($NewEmailSubscription) {
                // re-subscribe -> show unsubscription in history
                if (!empty($FullSubscriber['UnsubscriptionDate'])) {
                    Subscribers::WriteHistory(
                        $Parameters['UserID'],
                        $ReturnSubscriberID,
                        Subscribers::HISTORY_UNSUBSCRIBED,
                        array('ReSubscriptionDate' => time(), 'ReferenceID' => $ReferenceID),
                        $FullSubscriber['UnsubscriptionDate'] + 1
                    );
                } // make sure it appears after Email -> opened

                //the subscriber re-subscribed, remove possible privacy delete requests
                Subscribers::SubscriberRemoveDeleteRequests($Parameters['UserID'], $ReturnSubscriberID);
            }

            if ($NewEmailSubscription || $NewSMSSubscription) {
                // trigger on subscription event
                AutoResponders::RegisterAutoResponders($Parameters['UserID'], $ReferenceID, $ReturnSubscriberID);

                // trigger all existing
                $Taggings = Subscribers::RetrieveTaggingsOfSubscriber(
                    $Parameters['UserID'],
                    $ReturnSubscriberID,
                    $ReferenceID,
                    true
                );
                foreach ($Taggings as $TagID) {
                    // Trigger auto responders
                    AutoResponders::RegisterAutoResponders(
                        $Parameters['UserID'],
                        $ReferenceID,
                        $ReturnSubscriberID,
                        $TagID
                    );
                }
            }
        }

        // optin tagging + stats
        if ($Parameters['OptInSubscribeTo'] > 0) {
            // tagging (trigger autoresponders if set)
            Subscribers::TagSubscriber(
                $Parameters['UserID'],
                $ReturnSubscriberID,
                $Parameters['OptInSubscribeTo'],
                $ReferenceID,
                $Parameters['TriggerAutoResponders'],
                $Parameters['InstantTrigger']
            );
        }

        ToolPluginGeneral::subscriptionTrigger($Parameters['UserID'], $ReferenceID, $ReturnSubscriberID, $Parameters);

        // trigger automations as very last step
        TransactionEmails::RegisterAutomations($Parameters['UserID'], $ReturnSubscriberID, $ReferenceID, $Parameters['InstantTrigger']);

        $warning = current($warnings);
        return array(true, $ReturnSubscriberID, $warning['ErrorCode'] ?? null, $warning['ErrorData'] ?? null);
    }

    /**
     * Add Subscriber with valid PhoneNumber
     * @param $Parameters
     * @return array
     */
    public static function SubscribeWithPhoneNumberCheck($Parameters)
    {
        $UserID = $Parameters['UserID'];
        $ReferenceID = $Parameters['ReferenceID'];
        $SMSNumber = Subscribers::FormatSMSNumber($Parameters['PhoneNumber']);

        // reset phone number to subscribe without phone number
        $Parameters['PhoneNumber'] = "";
        $ArrayReturn = Subscribers::Subscribe($Parameters);

        if (!$ArrayReturn[0] || empty($SMSNumber)) {
            // subscription failed or given phone number from DigiStore is not a valid sms phone number
            return $ArrayReturn;
        }

        $SubscriberID = $ArrayReturn[1];

        $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
        if ($FullSubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED) {
            // subscriber has active phone number so do not update phone number
            return $ArrayReturn;
        }

        // check if given phone number already exists as active sms phone number in customer account
        // note: subscriber with given phone number may be different from ipn subscriber
        $SMSSubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $SMSNumber);
        if (!empty($SMSSubscriberID)) {
            $SMSFullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SMSSubscriberID, $ReferenceID);
            if ($SMSFullSubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED) {
                // given phone number is active for one subscriber so do not subscribe
                // History: SMS Phone Number could not be subscribed because of conflict with other subscriber
                Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_SMSNUMBER_CONFLICT, array(
                    'CurrentSubscriberID' => $SMSSubscriberID,
                    'CurrentEmailAddress' => $SMSFullSubscriber['EmailAddress'],
                ));
                return $ArrayReturn;
            }
            // check if given phone number already exists as unsubscribed sms phone number in customer account
            if ($SMSFullSubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED) {
                if ($SubscriberID != $SMSSubscriberID) {
                    // given phone number is unsubscribed in another subscribers record so delete it
                    // given phone number will then be subscribed to the ipn subscriber in Subscribers::UpdateSubscription()
                    Subscribers::UpdateSubscription($UserID, $SMSSubscriberID, array('PhoneNumber' => ''));
                    // History: SMS Phone Number has been deleted
                    Subscribers::WriteHistory($UserID, $SMSSubscriberID, Subscribers::HISTORY_SMSNUMBER_DELETED, array(
                        'OldPhoneNumber' => $SMSNumber,
                        'NewSubscriberID' => $SubscriberID,
                        'NewEmailAddress' => $FullSubscriber['EmailAddress'],
                    ));
                }
            }
        }

        // update phone number
        $result = Subscribers::UpdateSubscription($UserID, $SubscriberID, array(
            'PhoneNumber' => $SMSNumber,
            'UpdatePhoneNumberIfUnsubscribed' => true,
        ));

        return $ArrayReturn;
    }

    /**
     * Add Subscriber from SMS Inbound
     * this function favours the phone number for subscription cases as it belongs to the subscriber
     * the email address though can not be fully trusted
     * @param $Parameters
     * @return array
     */
    public static function SubscribeSMSInbound($Parameters)
    {
        $UserID = $Parameters['UserID'];
        $SenderNumber = $Parameters['PhoneNumber'];
        $SMSEmailAddress = $Parameters['EmailAddress'];
        $ReferenceID = $Parameters['ReferenceID'];

        $SubscriptionEmailAddress = ""; //email address that will be used to create/update the subscription

        // --- Subscription cases

        if (empty($SMSEmailAddress)) {
            // no email address found in the sms

            if ($SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $SenderNumber)) {
                //no email address in the sms but a subscriber with that phone number exists

                $Subscription = Subscription::RetrieveSubscriptionByIDAndReference(
                    $UserID,
                    $SubscriberID,
                    Subscription::SUBSCRIPTIONTYPE_EMAIL,
                    $ReferenceID
                );
                $SubscriptionEmailAddress = $Subscription['ContactInfo'];
            } else {
                //no email address in the sms and the phone number is unknown

                // write to watchdog that we could not determine an email address nor a subscriber
                watchdog(
                    'sms-inbound',
                    'SMS Inbound with no email address received',
                    array('!text' => $Parameters['SubscriptionReferrer']),
                    WATCHDOG_NOTICE
                );
            }
        } else {
            //email address found in the sms

            if ($SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $SMSEmailAddress)) {
                //existing subscriber with email address from SMS

                $SubscriptionEmailAddress = $SMSEmailAddress;

                //we have SubscriberID -> add or update phone number if it doesn't exists
                $result = Subscribers::UpdateSubscription($UserID, $SubscriberID, array('PhoneNumber' => $SenderNumber));
                if (!$result['Success']) {
                    //phone number update failed, the phone number is used by a different subscriber
                    //since we have an actual sms, update the phone number subscriber

                    $SubscriptionEmailAddress = "";
                }
            } elseif ($SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $SenderNumber)) {
                // unknown email address and existing subscriber found by phone number

                $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
                if (empty($FullSubscriber ['EmailAddress'])) {
                    // subscribe email address
                    $SubscriptionEmailAddress = $SMSEmailAddress;
                } else {
                    $SubscriptionEmailAddress = $SMSEmailAddress;
                    $ArrayFieldAndValues = array(
                        'EmailAddress' => $SMSEmailAddress,
                        'IPAddress' => $Parameters['IPAddress'],
                        'SubscriptionReferrer' => $Parameters['SubscriptionReferrer'],
                    );

                    // change email address
                    $result = Subscribers::UpdateSubscription(
                        $UserID,
                        $SubscriberID,
                        $ArrayFieldAndValues,
                        Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST
                    );

                    if (!$result['Success']) {
                        //email address update failed, use existing email address
                        $SubscriptionEmailAddress = $FullSubscriber['EmailAddress'];
                    }
                }
            } else {
                // unknown email address in sms and the phone number is unknown -> new subscription
                $SubscriptionEmailAddress = $SMSEmailAddress;
            }
        }

        $Parameters['EmailAddress'] = $SubscriptionEmailAddress;

        // subscribe
        $result = Subscribers::Subscribe($Parameters);
        return $result;
    }

    /**
     * Update subscriber information
     *
     * $UseSubscriptionProcess:
     *  - Subscribers::DOI_PROCESS_USE_FROM_SUBSCRIPTION: default, used if email address is not changed
     *  - Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST: email address is changed by subscriber or api, set in ChangeEmail-Dialog
     *  - Subscribers::DOI_PROCESS_USE_SINGLE_OPTIN: customer changes subscriber email, set in Subscriber-Edit if email address changed
     *
     * Note: $ArrayFieldAndValues['EmailAddress'] is punycoded
     * @return array{Success:bool, ErrorCode: int}
     */
    public static function UpdateSubscription(
        $UserID,
        $SubscriberID,
        $ArrayFieldAndValues,
        $UseSubscriptionProcess = Subscribers::DOI_PROCESS_USE_FROM_SUBSCRIPTION
    ) {
        if (empty($ArrayFieldAndValues)) {
            // nothing to do is not a failure
            return array('Success' => true, 'ErrorCode' => 0);
        }

        $Parameters = [
            'UserID' => $UserID,
            'SubscriberID' => $SubscriberID,
            'ReferenceID' => isset($ArrayFieldAndValues['ReferenceID']) ? $ArrayFieldAndValues['ReferenceID'] : 0,
            'EmailAddress' => $ArrayFieldAndValues['EmailAddress'],
            'PhoneNumber' => $ArrayFieldAndValues['PhoneNumber'],
            'OtherFields' => $ArrayFieldAndValues,
            'IPAddress' => $ArrayFieldAndValues['IPAddress'],
            'SubscriptionReferrer' => $ArrayFieldAndValues['SubscriptionReferrer'],
            'SendConfirmationEmail' => true,
            'UpdateIfUnsubscribed' => (isset($ArrayFieldAndValues['UpdateIfUnsubscribed'])) ? $ArrayFieldAndValues['UpdateIfUnsubscribed'] : true,
            'UpdatePhoneNumberIfUnsubscribed' => (isset($ArrayFieldAndValues['UpdatePhoneNumberIfUnsubscribed'])) ? $ArrayFieldAndValues['UpdatePhoneNumberIfUnsubscribed'] : true,
            'UpdateStatistics' => false, // dont count as new subscriber
            'TriggerAutoResponders' => true,
            'UpdatePrimarySubscription' => true,
            'InstantTrigger' => $ArrayFieldAndValues['InstantTrigger'] ?? false,
        ];

        [$success, $Params] = Subscribers::SubscriptionParameterValidation($Parameters, false, $UseSubscriptionProcess);
        if (!$success) {
            return array(
                'Success' => false,
                'ErrorCode' => reset($Params)['ErrorCode'],
            );
        }

        $ReferenceID = $Params['ReferenceID'];
        $subscriber = Subscriber::FromFullSubscriber($Params['FullSubscriber'], $ReferenceID);
        if (empty($subscriber)) {
            return array(
                'Success' => false,
                'ErrorCode' => Subscribers::SUBSCRIBE_ERROR_INVALID_SUBSCRIBER,
            );
        }

        // Change Email Address (if requested) - Start
        if (isset($ArrayFieldAndValues['EmailAddress'])) {
            $subscription = $subscriber->getPrimarySubscription(Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID);

            // allow to update non primary subscriptions
            if (
                isset($ArrayFieldAndValues['OldEmailAddress'])
                && (
                    !$subscription
                    || $ArrayFieldAndValues['OldEmailAddress'] != $subscription->getContactInfo()
                )
            ) {
                $arraySubscription = Subscription::RetrieveSubscriptionByEmailAddress(
                    $UserID,
                    $ArrayFieldAndValues['OldEmailAddress']
                );
                if ($arraySubscription) {
                    $subscription = new Subscription($arraySubscription);
                }
            }
            if ($subscription && $Params['EmailAddress'] != $subscription->getContactInfo()) {
                if (empty($Params['EmailAddress'])) {
                    // delete

                    // stop autoresponder
                    TransactionEmails::RemoveFromQueueByStatus(
                        $subscription->GetDBArray(),
                        TransactionEmails::STATUS_PENDING
                    );

                    $subscription->deleteWithSpamCheck(user_load($UserID));

                    Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_EMAIL_ADDRESS_CHANGED, [
                        'OldEmailAddress' => $subscription->getContactInfo(),
                        'NewEmailAddress' => '',
                        'ReferenceID' => $ReferenceID
                    ]);
                } elseif (
                    $subscription->getSubscriptionStatus(
                    ) == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED && $Params['SendConfirmationEmail']
                ) {
                    // stop autoresponder
                    TransactionEmails::RemoveFromQueueByStatus(
                        $subscription->GetDBArray(),
                        TransactionEmails::STATUS_PENDING
                    );

                    // we need to optin with new address, ... so change email address and unsubscribe
                    $subscription->updateDB([
                        'ContactInfo' => $Params['EmailAddress'],
                        'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED,
                        'UnsubscriptionDate' => time(),
                        'UnsubscriptionIP' => $Params['IPAddress'],
                        'ConfirmationEmailDate' => 0, // we had no optin for the new address
                    ]);

                    Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_EMAIL_ADDRESS_CHANGED, [
                        'OldEmailAddress' => $subscription->getContactInfo(),
                        'NewEmailAddress' => $Params['EmailAddress'],
                        'ReferenceID' => $ReferenceID
                    ]);
                } else {
                    // in case of
                    // unsubscribed  -> just change email address
                    // optin pending  -> just change email address
                    // subscribed + singleoptin  -> just change email address
                    $subscription->updateDB([
                        'ContactInfo' => $Params['EmailAddress'],
                        'ConfirmationEmailDate' => 0, // we had no optin for the new address
                    ]);

                    Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_EMAIL_ADDRESS_CHANGED, [
                        'OldEmailAddress' => $subscription->getContactInfo(),
                        'NewEmailAddress' => $Params['EmailAddress'],
                        'ReferenceID' => $ReferenceID
                    ]);

                    // update fields, but do not change the subscription status on Subscribe
                    $Params['SubscriptionStatus'] = $subscription->getSubscriptionStatus();
                }
            }
        }
        // Change Email Address (if requested) - End

        // Update/Delete subscriber phone number
        if (isset($ArrayFieldAndValues['PhoneNumber'])) {
            $subscription = $subscriber->getPrimarySubscription(Subscription::SUBSCRIPTIONTYPE_SMS, $ReferenceID);
            // allow to update non primary subscriptions
            if (
                isset($ArrayFieldAndValues['OldPhoneNumber']) && $ArrayFieldAndValues['OldPhoneNumber'] != $subscription->getContactInfo(
                )
            ) {
                $arraySubscription = Subscription::RetrieveSubscriptionByPhoneNumber(
                    $UserID,
                    $ArrayFieldAndValues['OldPhoneNumber']
                );
                if ($arraySubscription) {
                    $subscription = new Subscription($arraySubscription);
                }
            }

            if ($subscription && $Params['PhoneNumber'] != $subscription->getContactInfo()) {
                if (empty($Params['PhoneNumber'])) {
                    // delete

                    // remove from sms queues
                    TransactionEmails::RemoveFromQueueByStatus(
                        $subscription->GetDBArray(),
                        TransactionEmails::STATUS_PENDING
                    );

                    $subscription->deleteWithSpamCheck(user_load($UserID));

                    Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_SMSNUMBER_CHANGED, [
                        'OldPhoneNumber' => $subscription->getContactInfo(),
                        'NewPhoneNumber' => '',
                        'ReferenceID' => $ReferenceID
                    ]);
                } else {
                    //update if number has changed

                    $subscription->updateDB([
                        'ContactInfo' => $Params['PhoneNumber'],
                    ]);

                    Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_SMSNUMBER_CHANGED, [
                        'OldPhoneNumber' => $subscription->getContactInfo(),
                        'NewPhoneNumber' => $Params['PhoneNumber'],
                        'ReferenceID' => $ReferenceID
                    ]);

                    if ($Params['UpdatePhoneNumberIfUnsubscribed']) {
                        // handle the new phone number as a new subscription
                        $Params['SMSSubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
                    } else {
                        // update fields, but do not change the subscription status on Subscribe
                        $Params['SMSSubscriptionStatus'] = $subscription->getSubscriptionStatus();
                    }
                }
            }
        }

        // this will update
        // - custom fields
        // - send confirmation email and update optin data if email address changed in double optin list
        $result = Subscribers::Subscribe($Params);

        return array('Success' => $result[0], 'ErrorCode' => $result[1]);
    }

    /**
     * Validates email address format
     *
     * @param string $EmailAddress domain part is punycoded, local part may contain umlauts
     * @param int $UserID if set, we check e-mail against patterns specified by user
     *
     * @return boolean
     */
    public static function ValidateEmailAddress($EmailAddress, $UserID = 0)
    {
        if (!filter_var($EmailAddress, FILTER_VALIDATE_EMAIL)) {
            return false;
        }

        // if $UserID is not set, we don't need to check against user defined patter => TRUE
        // otherwise we return result of the check
        return empty($UserID) || Pattern::check(
            $EmailAddress,
                Pattern::getEmailPattern(VarEmailBlacklist::class . '::GetVariable', $UserID)
        );
    }

    public static function FormatSMSNumber($PhoneNumber)
    {
        if (empty($PhoneNumber)) {
            return '';
        }

        $matches = array(); //not used

        //replace leading '+' with '00'
        //replace '(0)' with ''
        //replace non-numeric chars with ''
        $regex = array('|^\+|', '|\(0\)|', '|[^0-9]|');
        $replace = array('00', '', '');

        $PhoneNumber = preg_replace($regex, $replace, trim($PhoneNumber));

        //number prefixes for countries are set in admin_settings
        //example for the german prefixes 0152, 0177, 0162: "0049:152,177,162\n"
        //additional country in new line
        //resulting $NumberPrefixes array: array('0049' => array('152', '177', '162'))
        $NumberPrefixes = variable_get('klicktipp_settings_sms_phonenumber_prefixes', array());

        if (!empty($NumberPrefixes)) {
            //array_walk callback
            $PrefixRegex = function (&$prefix) {
                //prefix must be at the beginning and can have a leading 0
                //example: '0177xxxx' as well as '177xxxx' will be replaced with '0049177xxxx'
                $prefix = "|^0?($prefix)|";
            };

            foreach ($NumberPrefixes as $country_code => $country_prefixes) {
                //create regex from prefixes
                //example result: array('|^0?(152)|', '|^0?(177)|', '|^0?(162)|')
                array_walk($country_prefixes, $PrefixRegex);

                //replace with country code and prefix (without leading 0)
                //$1 refers to digits in () of regex
                $replace = $country_code . '$1';
                $PhoneNumber = preg_replace($country_prefixes, $replace, $PhoneNumber);
            }
        }

        //check allowed combinations of international country codes format and if they start with '00'
        $regex_format = '/^00(9[976]\d|8[987530]\d|6[987]\d|5[90]\d|42\d|3[875]\d|' .
            '2[98654321]\d|9[8543210]|8[6421]|6[6543210]|5[87654321]|' .
            '4[987654310]|3[9643210]|2[70]|7|1)\d{1,14}$/';
        if (!preg_match($regex_format, $PhoneNumber, $matches)) { //0 = not found, FALSE = error
            return '';
        }

        if (strlen($PhoneNumber) > 40) {
            return '';
        }

        return $PhoneNumber;
    }

    /**
     * Unsubscribe the subscriber from the subscriber list
     * @param $userID
     * @param $contactInfo
     * @param int $campaignID
     * @param string $unsubscriptionIP
     *
     * @param int $subscriptionType
     *
     * @return array
     * @deprecated use Subscription::UnsubscribeSubscription
     *
     * $ListID === 0 means imported, so the default gets NULL
     *
     * $EmailAddress is punycoded!
     *
     */
    public static function Unsubscribe(
        $userID,
        $contactInfo,
        $campaignID = 0,
        $unsubscriptionIP = self::FALLBACK_IP_ADDRESS,
        $subscriptionType = null,
        $instant = false
    ) {
        $subscriptionType ??= Subscription::SUBSCRIPTIONTYPE_EMAIL;

        //@note: Multivalue - unsubscribe does not require a reference as the contact info is unique per user
        //TODO refactor to a new method in Subscription => Subscription::UnsubscribeSubscription()

        $subscription = Subscription::RetrieveSubscriptionByContactInfo($userID, $contactInfo, $subscriptionType);
        if (empty($subscription)) {
            if ($subscriptionType == Subscription::SUBSCRIPTIONTYPE_SMS) {
                return array(false, Subscribers::SUBSCRIBE_ERROR_INVALID_PHONE_NUMBER);
            } else { // Subscription::SUBSCRIPTIONTYPE_EMAIL
                return array(false, KLICKTIPPAPI_ERROR_SUBSCRIBER_EMAIL_NOT_FOUND);
            }
        }

        if ($subscription['SubscriptionStatus'] != Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED) {
            return array(false, KLICKTIPPAPI_ERROR_SUBSCRIBER_NOT_SUBSCRIBED);
        }

        // from here we know, we have a subscribed subscriber

        $lastConfirmationEmailDate = $subscription['ConfirmationEmailDate'];

        // 1. unsubscribe
        //TODO use Subscription::updateDB
        db_update('subscription')
            ->fields(array(
                'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED,
                'UnsubscriptionDate' => time(),
                'UnsubscriptionIP' => $unsubscriptionIP,
                'ConfirmationEmailDate' => 0,
            ))
            ->condition('RelOwnerUserID', $userID)
            ->condition('SubscriptionType', $subscriptionType)
            ->condition('ContactInfo', $contactInfo)
            ->execute();

        // 2. remove from autoresponder queues
        TransactionEmails::RemoveFromQueueByStatus($subscription, TransactionEmails::STATUS_PENDING);

        // 3. update stats
        if ($subscriptionType == Subscription::SUBSCRIPTIONTYPE_SMS) {
            //TODO DEV-2425 track (and trigger) unsubscription as open for all reference ids (see Unsubscribe)
            $ReferenceID = 0;
            TransactionEmails::RegisterAutomations($userID, $subscription['SubscriberID'], $ReferenceID, $instant);
        } else { // Subscription::SUBSCRIPTIONTYPE_EMAIL
            // automations are triggered by RegisterUnsubscriptionTrack
            Statistics::UpdateActivityStatistics($userID, ['TotalUnsubscriptions' => 1]);
            Statistics::RegisterUnsubscriptionTrack($userID, $subscription['SubscriberID'], $campaignID);

            if (!empty($lastConfirmationEmailDate)) {
                $listName = '';

                if (!empty($subscription['RelListID'])) {
                    $objectList = Lists::FromID($userID, $subscription['RelListID']);
                    if (!empty($objectList)) {
                        $listName = Lists::GetNameInDBArray($objectList->GetData());
                    }
                }

                // store the last ConfirmationEmailDate in the history
                Subscribers::WriteHistory(
                    $userID,
                    $subscription['SubscriberID'],
                    Subscribers::HISTORY_OPTIN,
                    t('Confirmation email sent to !email. !listName', [
                        '!email' => $contactInfo,
                        '!listName' => $listName
                    ]),
                    $lastConfirmationEmailDate
                );
            }
        }

        return array(true, 0);
    }

    /**
     * Unsubscribe the subscribers sms phone number
     * @deprecated use Subscription::UnsubscribeSubscription
     */
    public static function UnsubscribeSMSNumber($UserID, $PhoneNumber)
    {
        return Subscription::UnsubscribeSubscription(
            $UserID,
            $PhoneNumber,
            Subscription::SUBSCRIPTIONTYPE_SMS,
            0,
            self::FALLBACK_IP_ADDRESS
        );
    }

    /**
     * returns a dummy subscriber information
     * @param int $UserID
     * @param bool $withFieldNames If set to true, prefixes the dummy custom field values with their name.
     * @deprecated use Subscriber::SelectDummySubscription instead
     */
    public static function SelectDummySubscriber($UserID, bool $withFieldNames = false)
    {
        $account = user_load($UserID);

        // no data available -> create dummy
        $FullSubscriberWithFields = array(
            'SubscriberID' => 12345,
            'RelOwnerUserID' => $UserID,
            // Email
            'EmailAddress' => $account->mail,
            'BounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
            'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SubscriptionDate' => time(),
            'LastOpenDate' => time(),
            'LastOpenIP' => '127.0.0.1',
            'SubscriptionIP' => '127.0.0.1',
            'SubscriptionReferrer' => 'https://www.example.com/', // RFC 2606
            'UnsubscriptionDate' => time(),
            'UnsubscriptionIP' => '127.0.0.1',
            'OptInDate' => time(),
            'OptInIP' => '127.0.0.1',
            // SMS
            'PhoneNumber' => '**************', // http://www.frankgehtran.de/
            'SMSBounceType' => Subscribers::BOUNCETYPE_NOTBOUNCED,
            'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
            'SMSSubscriptionDate' => time(),
            'SubscriptionSMS' => 'This is the SMS text a user sent to subscribe to your list',
            'SMSUnsubscriptionDate' => time(),
            'IsDummy' => true
        );

        $customFields = CustomFields::RetrieveCustomFields($UserID);
        if (!empty($customFields)) {
            foreach ($customFields as $customField) {
                $key = 'CustomField' . $customField['CustomFieldID'];
                $FullSubscriberWithFields[$key] = static::getCustomFieldDummyValue($customField, $withFieldNames);
            }
        }

        return $FullSubscriberWithFields;
    }

    public static function selectZapierDummySubscriber(int $userId): array
    {
        return static::SelectDummySubscriber($userId, true);
    }

    /**
     * Returns a dummy value based on the type of the given custom field.
     *
     * @param array $customField
     * @param bool $withFieldName If set to true, prefixes the dummy value with the name of the custom field. This
     * is useful for e.g. Zapier so the user is aware of the name, without having to manually check the ids.
     *
     * @return string
     */
    private static function getCustomFieldDummyValue(array $customField, bool $withFieldName): string
    {
        switch ($customField['FieldTypeEnum']) {
            case CustomFields::TYPE_DATE:
            case CustomFields::TYPE_DATETIME:
                $customFieldValue = time();
                break;
            case CustomFields::TYPE_TIME:
                $customFieldValue = date('H') * 60 * 60 + date('i') * 60 + date('s');
                break;
            case CustomFields::TYPE_DECIMAL:
                $customFieldValue = 999;
                break;
            default:
                $customFieldValue = 'This is a single-line string';
                break;
        }

        if ($withFieldName) {
            return "[{$customField['FieldName']}]: $customFieldValue";
        }

        return $customFieldValue;
    }


    /**
     * Check subscriber limits
     *
     * return true if subscriber limit is exceeded
     * @param array $ArrayUserInformation
     * @param int|null $subscriberCount
     * @return bool
     * @throws \Doctrine\DBAL\Exception
     */
    public static function CheckSubscriberLimits(array $ArrayUserInformation, int &$subscriberCount = NULL): bool
    {
        $GroupInformation = UserGroups::RetrieveUserGroup($ArrayUserInformation['RelUserGroupID']);

        $UserID = empty($ArrayUserInformation['UserID']) ? $ArrayUserInformation['uid'] : $ArrayUserInformation['UserID'];
        $TotalSubscriberCount = static::getTotalCountOfUser($UserID);

        if (isset($subscriberCount)) {
            $subscriberCount = $TotalSubscriberCount;
        }

        return $TotalSubscriberCount > UserGroups::GetContactLimit((object)$ArrayUserInformation, $GroupInformation);
    }

    /**
     * Returns total number of subscribers for user with specified id
     *
     * @param int $userID
     *
     * @return int number of subscribers
     *
     * @throws \Doctrine\DBAL\Exception
     */
    public static function getTotalCountOfUser($userID): int
    {
        return (int)db_query(
            'SELECT COUNT(*) FROM {subscribers} WHERE RelOwnerUserID = :UserID',
            [':UserID' => $userID]
        )->fetchField();
    }

    /**
     * Write History
     *
     **/
    public static function WriteHistory(
        $OwnerUserID,
        $SubscriberID,
        $HistoryType,
        $HistoryData,
        $HistoryDate = 0,
        $RelIndexed = 0
    ) {
        if (empty($OwnerUserID) || empty($SubscriberID) || empty($HistoryData)) {
            return false;
        }

        // write only specific changes
        if (
            !in_array(
                $HistoryType,
                array(
                    Subscribers::HISTORY_TEXT,
                    Subscribers::HISTORY_IMPORT,
                    Subscribers::HISTORY_SUBSCRIBER_FEEDBACK,
                    Subscribers::HISTORY_UNSUBSCRIPTION_FEEDBACK,
                    Subscribers::HISTORY_OPTIN,
                    Subscribers::HISTORY_UNSUBSCRIBED,
                    Subscribers::HISTORY_EMAIL_ADDRESS_CHANGED,
                    Subscribers::HISTORY_BOUNCECHECK,
                    Subscribers::HISTORY_BOUNCERESET,
                    Subscribers::HISTORY_SOFTBOUNCERESET,
                    Subscribers::HISTORY_SPAMBOUNCERESET,
                    Subscribers::HISTORY_HARDBOUNCE_RESOLVED,
                    Subscribers::HISTORY_SOFTBOUNCE_RESOLVED,
                    Subscribers::HISTORY_SPAMBOUNCE_RESOLVED,
                    Subscribers::HISTORY_SMS_INBOUND,
                    Subscribers::HISTORY_SMSNUMBER_CHANGED,
                    Subscribers::HISTORY_SMSNUMBER_CONFLICT,
                    Subscribers::HISTORY_SMSNUMBER_DELETED,
                    Subscribers::HISTORY_OUTBOUND_CALL_FAILED,
                    Subscribers::HISTORY_FACEBOOK_AUDIENCE_EXPIRED_ACCESS,
                    Subscribers::HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED,
                    Subscribers::HISTORY_GOAL_REACHED,
                    Subscribers::HISTORY_GOAL_PASSED,
                    Subscribers::HISTORY_NOTIFICATION_INVALID_TO_EMAIL,
                    Subscribers::HISTORY_REQUEST_INFORMATION,
                    Subscribers::HISTORY_REQUEST_INFORMATION_FINISHED,
                    Subscribers::HISTORY_REQUEST_UPDATE,
                    Subscribers::HISTORY_REQUEST_UPDATE_FINISHED,
                    Subscribers::HISTORY_REQUEST_DELETE,
                    Subscribers::HISTORY_REQUEST_DELETE_FBAUDIENCE_ERROR,
                    Subscribers::HISTORY_REQUEST_OUTBOUND_CALL_FAILED,
                    Subscribers::HISTORY_FULLCONTACT_CALL_FAILED,
                    Subscribers::HISTORY_PAYMENT,
                    Subscribers::HISTORY_PAYMENT_INVOICE,
                    Subscribers::HISTORY_TRANSACTIONAL,
                    Subscribers::HISTORY_STATE_MOVED,
                )
            )
        ) {
            return false;
        }

        if (empty(Subscribers::RetrieveSubscriber($OwnerUserID, $SubscriberID))) {
            return false;
        }

        $ArrayFieldAndValues = array(
            'RelOwnerUserID' => $OwnerUserID,
            'RelSubscriberID' => $SubscriberID,
            'HistoryType' => $HistoryType,
            'HistoryData' => serialize($HistoryData),
            'HistoryDate' => empty($HistoryDate) ? time() : $HistoryDate,
            'RelIndexed' => $RelIndexed,
        );
        return kt_insert_row($ArrayFieldAndValues, '{subscriber_history}', 'HistoryIDreturn ');
    }

    /**
     * Read History
     * @param $ArrayReturnTypes : history types for subscriber history ($SubscriberID > 0) will be filtered in the form
     * @param array|int[] list of reference ids to filter for
     * @param string $ContactInfo Contact info to filter for (is used only if $SubscriptionType is also set)
     * @param int|null $SubscriptionType used in cobination with $ContactInfo
     **/
    public static function ReadHistory(
        $OwnerUserID,
        $SubscriberID = 0,
        $ArraySort = array('HistoryDate' => 'DESC'),
        $Start = 0,
        $Length = 10,
        $ArrayReturnTypes = array(),
        $References = [],
        $ContactInfo = '',
        $SubscriptionType = null
    ) {
        $HistoryArray = array();

        // read history

        if ($SubscriberID > 0) {
            $result = db_query(
                "SELECT HistoryID, HistoryType, HistoryData, HistoryDate FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
                array(
                    ':RelOwnerUserID' => $OwnerUserID,
                    ':RelSubscriberID' => $SubscriberID
                )
            );
        } else {
            $params = [
                ':RelOwnerUserID' => $OwnerUserID,
                ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_EMAIL,
            ];

            //return types
            $historytypes = '';
            if (!empty($ArrayReturnTypes)) {
                $historytypes = "AND sh.HistoryType IN (:HistoryTypes) ";
                $params[':HistoryTypes'] = $ArrayReturnTypes;
            }
            //else return all history types

            //order by
            $order = '';
            if (!empty($ArraySort)) {
                $fields = [];
                foreach ($ArraySort as $field => $dir) {
                    $fields[] = "$field $dir";
                }
                $order = "ORDER BY " . implode(',', $fields);
            }

            $query = "SELECT sh.HistoryType, sh.HistoryData, sh.HistoryDate, s.RelSubscriberID, s.ContactInfo AS EmailAddress FROM {subscriber_history} sh " .
                " LEFT JOIN {subscription} s ON sh.RelSubscriberID = s.RelSubscriberID AND sh.RelOwnerUserID = s.RelOwnerUserID AND s.SubscriptionType = :SubscriptionType " .
                " WHERE sh.RelOwnerUserID = :RelOwnerUserID $historytypes $order LIMIT $Start, $Length";

            //execute query
            $result = db_query($query, $params);
        }

        while ($history = kt_fetch_array($result)) {
            $history['HistoryData'] = unserialize((string) $history['HistoryData']);
            $history['ReferenceID'] = $history['HistoryData']['ReferenceID'] ?? 0 ?: 0;
            if (empty($References) || in_array($history['ReferenceID'], $References)) {
                $HistoryArray[] = $history;
            }
        }

        // add subscriber data

        if ($SubscriberID > 0) {
            $allSubscriptions = [];
            if (!empty($ContactInfo) && !empty($SubscriptionType)) {
                $singleSubscription = Subscription::RetrieveSubscriptionByContactInfo(
                    $OwnerUserID,
                    $ContactInfo,
                    $SubscriptionType
                );
                if ($singleSubscription) {
                    $allSubscriptions[] = $singleSubscription;
                }
            } else {
                $allSubscriptions = Subscriber::getAllSubscriptions($OwnerUserID, $SubscriberID);
            }
            foreach ($allSubscriptions as $subscription) {
                // don't display tecnical subscription
                if ($subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_NONE) {
                    continue;
                }

                if (
                    $subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL && !empty($subscription['ConfirmationEmailDate'])
                ) {
                    $listName = '';
                    $ObjectList = Lists::FromID($OwnerUserID, $subscription['RelListID']);
                    if (!empty($ObjectList)) {
                        $listName = Lists::GetNameInDBArray($ObjectList->GetData());
                    }

                    $HistoryArray[] = array(
                        'HistoryID' => 'subscription',
                        'HistoryType' => Subscribers::HISTORY_OPTIN,
                        'HistoryData' => t('Confirmation email sent to !email. !listName', [
                            '!email' => $subscription['ContactInfo'],
                            '!listName' => $listName
                        ]),
                        'HistoryDate' => $subscription['ConfirmationEmailDate']
                    );
                }

                // if the subscriber is subscribed, add the subscription date to the history
                if (
                    $subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL && !empty($subscription['SubscriptionDate'])
                ) {
                    $HistoryArray[] = array(
                        'HistoryID' => 'subscription',
                        'HistoryType' => Subscribers::HISTORY_SUBSCRIBED,
                        'HistoryData' => $subscription['ContactInfo'],
                        'HistoryDate' => $subscription['SubscriptionDate']
                    );
                }

                // if the subscriber is pending/unsubscribed, add the unsubscription date to the history
                if (
                    $subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL && in_array(
                        $subscription['SubscriptionStatus'],
                        array(Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, Subscribers::SUBSCRIPTIONSTATUS_OPTIN)
                    ) &&
                    !empty($subscription['UnsubscriptionDate'])
                ) {
                    $HistoryArray[] = array(
                        'HistoryID' => 'unsubscription',
                        'HistoryType' => Subscribers::HISTORY_UNSUBSCRIBED,
                        'HistoryData' => $subscription['ContactInfo'],
                        // make sure it appears after Email -> opened
                        'HistoryDate' => $subscription['UnsubscriptionDate'] + 1
                    );
                }

                // if the subscriber is subscribed for sms, add the subscription date to the history
                if (
                    $subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_SMS && $subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED &&
                    !empty($subscription['SubscriptionDate'])
                ) {
                    $HistoryArray[] = array(
                        'HistoryType' => Subscribers::HISTORY_SMSNUMBER_SUBSCRIBED,
                        'HistoryData' => $subscription['ContactInfo'],
                        'HistoryDate' => $subscription['SubscriptionDate']
                    );
                }

                // if the subscriber is unsubscribed from sms, add the unsubscription date to the history
                if (
                    $subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_SMS && $subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED &&
                    !empty($subscription['UnsubscriptionDate'])
                ) {
                    $HistoryArray[] = array(
                        'HistoryType' => Subscribers::HISTORY_SMSNUMBER_UNSUBSCRIBED,
                        'HistoryData' => "",
                        'HistoryDate' => $subscription['UnsubscriptionDate']
                    );
                }

                // --- START: check subscriber bounce status ---

                if ($subscription['BounceType'] == Subscribers::BOUNCETYPE_SOFT) {
                    $Suppression = kt_fetch_array(
                        db_query(
                            "SELECT LastValidation FROM {suppression_list} WHERE EmailAddress = :EmailAddress AND SuppressionSourceEnum = :SuppressionSourceEnum",
                            array(
                                ':EmailAddress' => $subscription['ContactInfo'],
                                ':SuppressionSourceEnum' => SuppressionList::SUPPRESSION_LIST_SOURCE_SOFTBOUNCED
                            )
                        )
                    );

                    if (!empty($Suppression['LastValidation'])) {
                        $DateOfSoftBounce = $Suppression['LastValidation'];
                    } else {
                        $FBL = kt_fetch_array(
                            db_query(
                                "SELECT DateOfReceive FROM {fbl_reports} WHERE EmailAddress = :EmailAddress AND FBLType = :FBLType ORDER BY DateOfReceive DESC",
                                array(
                                    ':EmailAddress' => $subscription['ContactInfo'],
                                    ':FBLType' => BounceEngine::PROCESS_FBL_TYPE_SOFT_BOUNCE
                                )
                            )
                        );
                        $DateOfSoftBounce = ($FBL && !empty($FBL['DateOfReceive'])) ? $FBL['DateOfReceive'] : 0;
                    }

                    if (!empty($DateOfSoftBounce)) {
                        $FBL = kt_fetch_array(
                            db_query(
                                "SELECT * FROM {fbl_reports} WHERE EmailAddress = :EmailAddress AND FBLType = :FBLType ORDER BY DateOfReceive DESC",
                                array(
                                    ':EmailAddress' => $subscription['ContactInfo'],
                                    ':FBLType' => BounceEngine::PROCESS_FBL_TYPE_SOFT_BOUNCE
                                )
                            )
                        );
                        $bounceReason = '';
                        if ($FBL) {
                            $FBLRecord = BounceEngine::GetFBLEmailContentArray($FBL['FBLEmail']);
                            if (isset($FBLRecord['dsnStatus'])) {
                                $bounceReason = ' (' . t('subscriber::history::bounce::reason::' . $FBLRecord['dsnStatus']) . ')';
                            }
                        }
                        $row = array(
                            'HistoryID' => 'softbounce',
                            'HistoryType' => Subscribers::HISTORY_BOUNCED,
                            'HistoryData' => t(/*ignore*/
                                Subscribers::$TranslationBounceTypes[Subscribers::BOUNCETYPE_SOFT]
                            )  . $bounceReason,
                            'HistoryDate' => $DateOfSoftBounce
                        );

                        if (user_access('administer klicktipp')) {
                            if ($FBL) {
                                $row['HistoryData'] .= ' ' . l(
                                    'details',
                                    APP_URL . "fbldetails/{$FBL['FBLID']}",
                                    array('attributes' => array('target' => '_blank'))
                                );
                            }
                        }
                        $HistoryArray[] = $row;
                    }
                } elseif ($subscription['BounceType'] == Subscribers::BOUNCETYPE_HARD) {
                    $t = 0;
                    $Suppression = kt_fetch_array(
                        db_query(
                            "SELECT LastValidation FROM {suppression_list} WHERE EmailAddress = :EmailAddress AND SuppressionSourceEnum = :SuppressionSourceEnum",
                            array(
                                ':EmailAddress' => $subscription['ContactInfo'],
                                ':SuppressionSourceEnum' => SuppressionList::SUPPRESSION_LIST_SOURCE_HARDBOUNCED
                            )
                        )
                    );

                    if (
                        $Suppression
                        && $Suppression['LastValidation'] > SuppressionList::SUPPRESSION_LIST_HARDBOUNCE_DONOTREVALIDATE
                    ) {
                        $DateOfHardBounce = $Suppression['LastValidation'];
                    } else {
                        $FBL = kt_fetch_array(
                            db_query(
                                "SELECT DateOfReceive FROM {fbl_reports} WHERE EmailAddress = :EmailAddress AND FBLType = :FBLType ORDER BY DateOfReceive DESC",
                                array(
                                    ':EmailAddress' => $subscription['ContactInfo'],
                                    ':FBLType' => BounceEngine::PROCESS_FBL_TYPE_HARD_BOUNCE
                                )
                            )
                        );

                        $DateOfHardBounce = $FBL['DateOfReceive'] ?? 0 ?: 0;
                    }

                    if (!empty($DateOfHardBounce)) {
                        $row = array(
                            'HistoryID' => 'hardbounce',
                            'HistoryType' => Subscribers::HISTORY_BOUNCED,
                            'HistoryData' => t(/*ignore*/
                                Subscribers::$TranslationBounceTypes[Subscribers::BOUNCETYPE_HARD]
                            ),
                            'HistoryDate' => $DateOfHardBounce
                        );
                        if (user_access('administer klicktipp')) {
                            $FBL = kt_fetch_array(
                                db_query(
                                    "SELECT * FROM {fbl_reports} WHERE EmailAddress = :EmailAddress AND FBLType = :FBLType ORDER BY DateOfReceive DESC",
                                    array(
                                        ':EmailAddress' => $subscription['ContactInfo'],
                                        ':FBLType' => BounceEngine::PROCESS_FBL_TYPE_HARD_BOUNCE
                                    )
                                )
                            );
                            if ($FBL) {
                                $row['HistoryData'] .= ' ' . l(
                                    'details',
                                    APP_URL . "fbldetails/{$FBL['FBLID']}",
                                    array('attributes' => array('target' => '_blank'))
                                );
                            }
                        }
                        $HistoryArray[] = $row;
                    }
                } elseif ($subscription['BounceType'] == Subscribers::BOUNCETYPE_SPAM) {
                    $FBL = kt_fetch_array(
                        db_query(
                            "SELECT FBLID, DateOfReceive FROM {fbl_reports} WHERE UserID = :UserID AND EmailAddress = :EmailAddress AND FBLType = :FBLType ORDER BY DateOfReceive DESC",
                            array(
                                ':UserID' => $subscription['RelOwnerUserID'],
                                ':EmailAddress' => $subscription['ContactInfo'],
                                ':FBLType' => BounceEngine::PROCESS_FBL_TYPE_SPAM_BOUNCE
                            )
                        )
                    );

                    if ($FBL && !empty($FBL['DateOfReceive'])) {
                        $row = array(
                            'HistoryID' => 'spambounce',
                            'HistoryType' => Subscribers::HISTORY_BOUNCED,
                            'HistoryData' => t(/*ignore*/
                                Subscribers::$TranslationBounceTypes[Subscribers::BOUNCETYPE_SPAM]
                            ),
                            'HistoryDate' => $FBL['DateOfReceive']
                        );
                        if (user_access('administer klicktipp')) {
                            if ($FBL) {
                                $row['HistoryData'] .= ' ' . l(
                                    'details',
                                    APP_URL . "fbldetails/{$FBL['FBLID']}",
                                    array('attributes' => array('target' => '_blank'))
                                );
                            }
                        }
                        $HistoryArray[] = $row;
                    }
                }

                // --- END: check subscriber bounce status ---

                //spam complaint?
                if ($subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED) {
                    $FBL = kt_fetch_array(
                        db_query(
                            "SELECT FBLID, DateOfReceive FROM {fbl_reports} WHERE UserID = :UserID AND EmailAddress = :EmailAddress AND FBLType = :FBLType",
                            array(
                                ':UserID' => $subscription['RelOwnerUserID'],
                                ':EmailAddress' => $subscription['ContactInfo'],
                                ':FBLType' => BounceEngine::PROCESS_FBL_TYPE_FBL
                            )
                        )
                    );

                    if ($FBL && !empty($FBL['DateOfReceive'])) {
                        $row = array(
                            'HistoryID' => 'spamcomplaint',
                            'HistoryType' => Subscribers::HISTORY_BOUNCED,
                            //Display as bounce
                            'HistoryData' => t("Spam complaint"),
                            'HistoryDate' => $FBL['DateOfReceive'],
                        );
                        if (user_access('administer klicktipp')) {
                            $row['HistoryData'] .= ' ' .
                                l(
                                    'details',
                                    APP_URL . "fbldetails/{$FBL['FBLID']}",
                                    array('attributes' => array('target' => '_blank'))
                                );
                        }
                        $HistoryArray[] = $row;
                    }
                }

                //is hater or spamtrap?
                $result = db_query(
                    "SELECT 1 FROM {suppression_list} WHERE EmailAddress = :EmailAddress " .
                    " AND (SuppressionSourceEnum = :SpamTrap OR " .
                    " (SuppressionSourceEnum = :Account AND UserID = :UserID)) LIMIT 0,1",
                    array(
                        ':EmailAddress' => $subscription['ContactInfo'],
                        ':SpamTrap' => SuppressionList::SUPPRESSION_LIST_SOURCE_SPAMTRAP,
                        ':Account' => SuppressionList::SUPPRESSION_LIST_SOURCE_ACCOUNT,
                        ':UserID' => $OwnerUserID,
                    )
                )->fetchField();
                if ($result) {
                    //email address is hater
                    $HistoryArray[] = array(
                        'HistoryID' => 'hater',
                        'HistoryType' => Subscribers::HISTORY_HATER,
                        //show as bounced in history
                        'HistoryData' => '',
                        'HistoryDate' => time()
                        //we don't have a date (LastValidation == 0), use current time, so it will also always be on top
                    );
                }
            }

            // read taggings

            //retrieve tags of subscriber
            $AllTags = Tag::GetTagsCache($OwnerUserID);
            $ReferenceDisplayNames = Reference::getDisplayNames($OwnerUserID);
            if (!empty($References)) {
                $ReferenceDisplayNames = array_intersect_key($ReferenceDisplayNames, array_flip($References));
            }
            foreach ($ReferenceDisplayNames as $referenceID => $displayName) {
                $SubscriberTags = Subscribers::RetrieveTaggingsOfSubscriber($OwnerUserID, $SubscriberID, $referenceID);
                foreach ($SubscriberTags as $stag) {
                    if (empty($AllTags[$stag['RelTagID']])) {
                        continue;
                    }

                    $Tag = $AllTags[$stag['RelTagID']];
                    $TagName = $Tag['TagName'];

                    if (empty($Tag['MultiValue'])) {
                        // don't show single value taggings multiple times
                        if ($referenceID > 0) {
                            continue;
                        }
                    } else {
                        // show reference name this way until DEV-2747 isn't donw
                        $TagName .= ' (' . $displayName . ')';
                    }

                    switch ($Tag['Category']) {
                        case Tag::CATEGORY_SENT:
                        case Tag::CATEGORY_OPENED:
                        case Tag::CATEGORY_CLICKED:
                        case Tag::CATEGORY_VIEWED:
                        case Tag::CATEGORY_CONVERTED:
                            $objCampaign = Campaigns::FromID($OwnerUserID, $Tag['EntityID']);
                            if ($objCampaign) {
                                $TagName = l(
                                    $TagName,
                                    $objCampaign->getEntityUrlEdit(),
                                    ['html' => true]
                                );
                            }
                            break;
                        case Tag::CATEGORY_OUTBOUND:
                            $outbound = MarketingTools::FromID($OwnerUserID, $Tag['EntityID']);
                            switch ($outbound->GetData('ToolType')) {
                                case MarketingTools::TOOL_TYPE_OUTBOUND:
                                    $TagName = l(
                                        $TagName,
                                        "tools/$OwnerUserID/outbound/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case MarketingTools::TOOL_TYPE_ZAPIER:
                                    $TagName = l(
                                        $TagName,
                                        "tools/$OwnerUserID/zapier/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                            }
                            break;
                        case Tag::CATEGORY_KAJABI_ACTIVATE:
                        case Tag::CATEGORY_KAJABI_DEACTIVATE:
                            $TagName = l(
                                $TagName,
                                "tools/$OwnerUserID/kajabi/{$Tag['EntityID']}/edit",
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_TAGGING_PIXEL:
                            $TagName = l(
                                $TagName,
                                (new TaggingPixelsLinkResolver())->linkEdit(
                                    $OwnerUserID,
                                    $Tag['EntityID']
                                ),
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_EMAIL_SENT:
                        case Tag::CATEGORY_EMAIL_OPENED:
                        case Tag::CATEGORY_EMAIL_CLICKED:
                        case Tag::CATEGORY_EMAIL_VIEWED:
                            $linkResolver = new EmailAutomationLinkResolver();
                            $TagName = l(
                                $TagName,
                                $linkResolver->linkEdit($OwnerUserID, $Tag['EntityID']),
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_SMS_SENT:
                        case Tag::CATEGORY_SMS_CLICKED:
                            $linkResolver = new SmsAutomationLinkResolver();
                            $TagName = l(
                                $TagName,
                                $linkResolver->linkEdit($OwnerUserID, $Tag['EntityID']),
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_AUTOMATION_STARTED:
                        case Tag::CATEGORY_AUTOMATION_FINISHED:
                            $objCampaign = CampaignsProcessFlow::FromID($OwnerUserID, $Tag['EntityID']);
                            if ($objCampaign) {
                                $TagName = l($TagName, $objCampaign->getEntityUrlEdit(), array('html' => true));
                            }
                            break;
                        case Tag::CATEGORY_LISTBUILDING_APIKEY:
                            $TagName = l(
                                $TagName,
                                (new ApiKeyLinkResolver())->linkEdit($OwnerUserID, $Tag['EntityID']),
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_LISTBUILDING_BUSINESSCARD:
                            $TagName = l(
                                $TagName,
                                "listbuilding/$OwnerUserID/businesscard/{$Tag['EntityID']}/edit",
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_LISTBUILDING_EVENT:
                            $TagName = l(
                                $TagName,
                                "listbuilding/$OwnerUserID/event/{$Tag['EntityID']}/edit",
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_LISTBUILDING_REQUEST:
                            $TagName = l(
                                $TagName,
                                (new RequestByMailLinkResolver())->linkEdit($OwnerUserID, $Tag['EntityID']),
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_LISTBUILDING_FORMS:
                        case Tag::CATEGORY_LISTBUILDING_PAYMENT:
                        case Tag::CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT:
                        case Tag::CATEGORY_LISTBUILDING_DEFERRED_PAYMENT:
                        case Tag::CATEGORY_LISTBUILDING_REFUNDED:
                        case Tag::CATEGORY_LISTBUILDING_CHARGEDBACK:
                        case Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED:
                        case Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED_LAST_DAY:
                        case Tag::CATEGORY_LISTBUILDING_REBILL_RESUMED:
                        case Tag::CATEGORY_PAYMENT_COMPLETED:
                        case Tag::CATEGORY_PAYMENT_EXPIRED:
                        case Tag::CATEGORY_LISTBUILDING_SMS:
                        case Tag::TAG_CATEGORY_LANDING_PAGE_SUBSCRIBED:
                            /** @var Listbuildings $lb */
                            $lb = Listbuildings::FromID($OwnerUserID, $Tag['EntityID']);
                            switch ($lb->GetData('BuildType')) {
                                case Listbuildings::TYPE_FORM_CUSTOM:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/form-custom/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_FORM_RAW:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/form-raw/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_FORM_INLINE:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/form-inline/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_FORM_LEADPAGES:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/form-leadpages/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_FORM_WISTIA:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/form-wistia/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_FORM_OPTIMIZEPRESS:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/form-optimizepress/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_FORM_THRIVE:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/form-thrive/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_WUFOO:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/wufoo/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_AFFILICON:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/affilicon/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_CLICKBANK:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/clickbank/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_DIGISTORE:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/digistore/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_PAYPAL:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/paypal/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_CLEVERBRIDGE:
                                    $TagName = l(
                                        $TagName,
                                        "listbuilding/$OwnerUserID/cleverbridge/{$Tag['EntityID']}/edit",
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_SMS_LISTBUILDING_GLOBAL:
                                    $TagName = l(
                                        $TagName,
                                        (new RequestSmsListbuildingLinkResolver())->linkEdit(
                                            $OwnerUserID,
                                            $Tag['EntityID']
                                        ),
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_SMS_LISTBUILDING_NEXMO:
                                    $TagName = l(
                                        $TagName,
                                        (new RequestSmsNexmoLinkResolver())->linkEdit(
                                            $OwnerUserID,
                                            $Tag['EntityID']
                                        ),
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_SMS_LISTBUILDING_TWILIO:
                                    $TagName = l(
                                        $TagName,
                                        (new RequestSmsTwilioLinkResolver())->linkEdit(
                                            $OwnerUserID,
                                            $Tag['EntityID']
                                        ),
                                        array('html' => true)
                                    );
                                    break;
                                case Listbuildings::TYPE_LANDING_PAGE:
                                    $landingPage = LandingPage::FromArray($lb->GetDBArray());
                                    $TagName = l(
                                        $TagName,
                                        (new LandingPageLinkResolver())->linkEdit(
                                            $landingPage->GetData('RelOwnerUserID'),
                                            $landingPage->GetData('BuildID')
                                        ),
                                        array('html' => true)
                                    );

                                    break;
                            }
                            break;
                        case Tag::CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION:
                            $TagName = l(
                                $TagName,
                                "listbuilding/$OwnerUserID/digistore/{$Tag['EntityID']}/edit",
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_FACEBOOK_AUDIENCE:
                            $TagName = l(
                                $TagName,
                                "contacts/$OwnerUserID/facebook-audience/{$Tag['EntityID']}/edit",
                                array('html' => true)
                            );
                            break;
                        case Tag::CATEGORY_PLUGIN_INBOUND_READY:
                        case Tag::CATEGORY_PLUGIN_INBOUND_STARTED:
                        case Tag::CATEGORY_PLUGIN_INBOUND_INPROGRESS:
                        case Tag::CATEGORY_PLUGIN_INBOUND_FINISHED:
                            $TagName = l($TagName, "plugins/$OwnerUserID/{$Tag['EntityID']}/edit", array('html' => true));
                            break;
                    }

                    $Value = array(
                        'HistoryType' => Subscribers::HISTORY_TAGGED,
                        'HistoryData' => $TagName,
                        'HistoryDate' => $stag['SubscriptionDate'],
                        'ReferenceID' => $referenceID
                    );

                    $HistoryArray[] = $Value;
                }
            }

            // gather history entries for processflow, autoresponder and newsletter
            foreach (
                [
                    TransactionalQueue::TABLE_NAME,
                    AutoresponderQueue::TABLE_NAME,
                    NewsletterQueue::TABLE_NAME
                ] as $tableName
            ) {
                $QueueEntries = TransactionEmails::RetrieveFailedTransactions(
                    $OwnerUserID,
                    $SubscriberID,
                    $References,
                    $tableName
                );
                self::CreateFailedTransactionEmailHistoryEntries($HistoryArray, $OwnerUserID, $QueueEntries);
            }
        } // if subscriber

        // add output text

        foreach ($HistoryArray as &$history) {
            switch ($history['HistoryType']) {
                case Subscribers::HISTORY_UNSUBSCRIPTION_FEEDBACK:
                    // convert line breaks into <br />
                    $history['Text'] = str_replace("\n", "<br />", check_plain($history['HistoryData']));
                    break;
                case Subscribers::HISTORY_SUBSCRIBER_FEEDBACK:
                    // convert line breaks into <br />
                    $history['Text'] = t(
                        "Subscriber feedback for email %emailname: ",
                        array('%emailname' => $history['HistoryData']['EmailName'])
                    ) . '<br/>' .
                        str_replace("\n", "<br />", $history['HistoryData']['Feedback']);
                    break;
                case Subscribers::HISTORY_UNSUBSCRIBED:
                    if (empty($history['HistoryData']['ReSubscriptionDate'])) {
                        $history['Text'] = "";
                    } else {
                        $history['Text'] = t(
                            "Re-Subscription at %date.",
                            array(
                                '%date' => Dates::formatDate(
                                    Dates::FORMAT_DMYHIS, (int) $history['HistoryData']['ReSubscriptionDate']
                                )
                            )
                        );
                    }
                    break;
                case Subscribers::HISTORY_EMAIL_ADDRESS_CHANGED:
                    $history['Text'] = t("Email address changed from !old to !new.", array(
                        '!old' => self::DepunycodeEmailAddress($history['HistoryData']['OldEmailAddress']),
                        '!new' => self::DepunycodeEmailAddress($history['HistoryData']['NewEmailAddress']),
                    ));
                    break;
                case Subscribers::HISTORY_BOUNCECHECK:
                    $bouncedAt = empty($history['HistoryData']['bouncedAt']) ? t('n/a') : Dates::formatDate(
                        Dates::FORMAT_DMYHIS, (int) $history['HistoryData']['bouncedAt']
                    );
                    if ($history['HistoryData']['success']) {
                        $history['Text'] = t(
                            "Hard bounce checked: email address exists (Bounce since !bouncedAt).",
                            array('!bouncedAt' => $bouncedAt)
                        );
                    } else {
                        $history['Text'] = t(
                            "Hard bounce checked: bounce confirmed (Bounce since !bouncedAt).",
                            array('!bouncedAt' => $bouncedAt)
                        );
                    }
                    break;
                case Subscribers::HISTORY_SOFTBOUNCERESET:
                    $bouncedAt = empty($history['HistoryData']['bouncedAt']) ? t('n/a') : Dates::formatDate(
                        Dates::FORMAT_DMYHIS, (int) $history['HistoryData']['bouncedAt']
                    );
                    $history['Text'] = t(
                        "Soft bounce reset by user (Bounce since !bouncedAt).",
                        array('!bouncedAt' => $bouncedAt)
                    );
                    break;
                case Subscribers::HISTORY_BOUNCERESET:
                    $bouncedAt = empty($history['HistoryData']['bouncedAt']) ? t('n/a') : Dates::formatDate(
                        Dates::FORMAT_DMYHIS, (int) $history['HistoryData']['bouncedAt']
                    );
                    $history['Text'] = t(
                        "Hard bounce reset by user (Bounce since !bouncedAt).",
                        array('!bouncedAt' => $bouncedAt)
                    );
                    break;
                case Subscribers::HISTORY_SPAMBOUNCERESET:
                    $bouncedAt = empty($history['HistoryData']) ? t('n/a') : Dates::formatDate(
                        Dates::FORMAT_DMYHIS, (int) $history['HistoryData']
                    );
                    $history['Text'] = t(
                        "Spam bounce reset by user (Bounce since !bouncedAt).",
                        array('!bouncedAt' => $bouncedAt)
                    );
                    break;
                case Subscribers::HISTORY_BOUNCED:
                    $history['Text'] = $history['HistoryData']; //display bounce type
                    break;
                case Subscribers::HISTORY_SOFTBOUNCE_RESOLVED:
                    $bouncedAt = empty($history['HistoryData']) ? t('n/a') : Dates::formatDate(
                        Dates::FORMAT_DMYHIS, (int) $history['HistoryData']
                    );
                    $history['Text'] = t(
                        "Soft bounce resolved (Bounce since !bouncedAt).",
                        array('!bouncedAt' => $bouncedAt)
                    );
                    break;
                case Subscribers::HISTORY_SPAMBOUNCE_RESOLVED:
                    $bouncedAt = empty($history['HistoryData']) ? t('n/a') : Dates::formatDate(
                        Dates::FORMAT_DMYHIS, (int) $history['HistoryData']
                    );
                    $history['Text'] = t(
                        "Spam bounce resolved (Bounce since !bouncedAt).",
                        array('!bouncedAt' => $bouncedAt)
                    );
                    break;
                case Subscribers::HISTORY_HARDBOUNCE_RESOLVED:
                    $bouncedAt = empty($history['HistoryData']) ? t('n/a') : Dates::formatDate(
                        Dates::FORMAT_DMYHIS, (int) $history['HistoryData']
                    );
                    $history['Text'] = t(
                        "Hard bounce resolved (Bounce since !bouncedAt).",
                        array('!bouncedAt' => $bouncedAt)
                    );
                    break;
                case Subscribers::HISTORY_HATER:
                    $history['Text'] = t("Blocked");
                    break;
                case Subscribers::HISTORY_SMSNUMBER_CHANGED:
                    $history['Text'] = t("SMS Phone number changed from !old to !new.", array(
                        '!old' => $history['HistoryData']['OldPhoneNumber'],
                        '!new' => $history['HistoryData']['NewPhoneNumber'],
                    ));
                    break;
                case Subscribers::HISTORY_SMSNUMBER_CONFLICT:
                    $history['Text'] = t(
                        "SMS Phone number could not be updated because of conflict with subscriber !link",
                        array(
                            '!link' => l(
                                self::DepunycodeEmailAddress($history['HistoryData']['CurrentEmailAddress']),
                                "subscriber/$OwnerUserID/edit/{$history['HistoryData']['CurrentSubscriberID']}",
                                array('html' => true)
                            ),
                        )
                    );
                    break;
                case Subscribers::HISTORY_SMSNUMBER_DELETED:
                    $history['Text'] = t("SMS Phone number !old was deleted and assigned to !link", array(
                        '!old' => $history['HistoryData']['OldPhoneNumber'],
                        '!link' => l(
                            self::DepunycodeEmailAddress($history['HistoryData']['NewEmailAddress']),
                            "subscriber/$OwnerUserID/edit/{$history['HistoryData']['NewSubscriberID']}",
                            array('html' => true)
                        ),
                    ));
                    break;
                case Subscribers::HISTORY_OUTBOUND_CALL_FAILED:
                    $history['Text'] = t(/*ignore*/ $history['HistoryData']['!event'], $history['HistoryData']);
                    break;
                case Subscribers::HISTORY_FACEBOOK_AUDIENCE_EXPIRED_ACCESS:
                    if ($history['HistoryData']['EventType'] == ToolFacebookAudience::EVENT_TYPE_REMOVE) {
                        $history['Text'] = t(
                            'Failed to remove contact from Facebook Audience !audience. Token for Facebook Account !account expired.',
                            array(
                                '!audience' => l(
                                    $history['HistoryData']['AudienceName'],
                                    "contacts/$OwnerUserID/facebook-audience"
                                ),
                                '!account' => $history['HistoryData']['FacebookAccountName'],
                            )
                        );
                    } else {
                        $history['Text'] = t(
                            'Failed to add contact to Facebook Audience !audience. Token for Facebook Account !account expired.',
                            array(
                                '!audience' => l(
                                    $history['HistoryData']['AudienceName'],
                                    "contacts/$OwnerUserID/facebook-audience"
                                ),
                                '!account' => $history['HistoryData']['FacebookAccountName'],
                            )
                        );
                    }

                    break;
                case Subscribers::HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED:
                    if ($history['HistoryData']['EventType'] == ToolFacebookAudience::EVENT_TYPE_REMOVE) {
                        $history['Text'] = t(
                            'Failed to remove contact from Facebook Audience !audience. Reason: !reason.',
                            array(
                                '!audience' => l(
                                    $history['HistoryData']['AudienceName'],
                                    "contacts/$OwnerUserID/facebook-audience"
                                ),
                                '!reason' => t(/*ignore*/ $history['HistoryData']['Reason']),
                            )
                        );
                    } else {
                        $history['Text'] = t(
                            'Failed to add contact to Facebook Audience !audience. Reason: !reason.',
                            array(
                                '!audience' => l(
                                    $history['HistoryData']['AudienceName'],
                                    "contacts/$OwnerUserID/facebook-audience"
                                ),
                                '!reason' => t(/*ignore*/ $history['HistoryData']['Reason']),
                            )
                        );
                    }

                    break;
                case Subscribers::HISTORY_GOAL_REACHED:
                    $history['Text'] = t('Goal !goal reached in campaign !name.', array(
                        '!goal' => $history['HistoryData']['StateName'],
                        '!name' => l(
                            $history['HistoryData']['CampaignName'],
                            "build/marketing-cockpit/{$history['HistoryData']['CampaignID']}"
                        ),
                    ));
                    $history['HistoryDate'] = strtotime('+1 second', $history['HistoryDate']);
                    break;
                case Subscribers::HISTORY_GOAL_PASSED:
                    $history['Text'] = t('Goal !goal passed in campaign !name.', array(
                        '!goal' => $history['HistoryData']['StateName'],
                        '!name' => l(
                            $history['HistoryData']['CampaignName'],
                            "build/marketing-cockpit/{$history['HistoryData']['CampaignID']}"
                        ),
                    ));
                    $history['HistoryDate'] = strtotime('+1 second', $history['HistoryDate']);
                    break;
                case Subscribers::HISTORY_NOTIFICATION_INVALID_TO_EMAIL:
                    $emailLinkResolver = new EmailNotificationLinkResolver();
                    $signatureLinkResolver = new SignatureLinkResolver();
                    $history['Text'] = t(
                        "Notififcation !email could not be sent due to empty to email in signature !signature",
                        array(
                            // NOTE: MigrationLinks -> will be migrated with email module
                            '!email' => l(
                                $history['HistoryData']['EmailName'],
                                $emailLinkResolver->linkEdit($OwnerUserID, $history['HistoryData']['EmailID']),
                                array('html' => true)
                            ),
                            '!signature' => l(
                                $history['HistoryData']['SignatureName'],
                                $signatureLinkResolver->linkEdit($OwnerUserID, $history['HistoryData']['RevisionID']),
                                array('html' => true)
                            ),
                        )
                    );
                    break;
                case Subscribers::HISTORY_REQUEST_INFORMATION:
                    $history['Text'] = t('The subscriber requested to view his information.');
                    break;
                case Subscribers::HISTORY_REQUEST_INFORMATION_FINISHED:
                    $history['Text'] = t('The subscriber received an email with his information.');
                    break;
                case Subscribers::HISTORY_REQUEST_UPDATE:
                    $history['Text'] = t('The subscriber requested to update his information.');
                    break;
                case Subscribers::HISTORY_REQUEST_UPDATE_FINISHED:
                    $history['Text'] = t('The subscriber request to update his information has been finished.');
                    break;
                case Subscribers::HISTORY_REQUEST_DELETE:
                    $history['Text'] = t('The subscriber requested to be deleted.');
                    break;
                case Subscribers::HISTORY_FULLCONTACT_CALL_FAILED:
                    $arrow = utf8_decode("&#x00BB;");
                    $TagName = t(
                        "state"
                    ) . " $arrow {$history['HistoryData']['StateName']} $arrow {$history['HistoryData']['Message']}";
                    $history['Text'] = l(
                        $TagName,
                        "build/marketing-cockpit/{$history['HistoryData']['CampaignID']}",
                        array('html' => true)
                    );
                    break;
                case Subscribers::HISTORY_PAYMENT:
                    $history['Text'] = t('Purchase of !product amount !amount !currency.', array(
                        '!amount' => klicktipp_number_format($history['HistoryData']['Amount'] / 100, 2),
                        '!currency' => $history['HistoryData']['Currency'],
                        '!product' => l(
                            $history['HistoryData']['ProductName'],
                            $history['HistoryData']['ProductEditURL'],
                            array('html' => true)
                        )
                    ));
                    break;
                case Subscribers::HISTORY_PAYMENT_INVOICE:
                    $history['Text'] = t('Invoice !email for payment of !product.', array(
                        '!email' => l(
                            $history['HistoryData']['EmailName'],
                            $history['HistoryData']['EmailEditURL'],
                            array('html' => true)
                        ),
                        '!product' => l(
                            $history['HistoryData']['ProductName'],
                            $history['HistoryData']['ProductEditURL'],
                            array('html' => true)
                        ),
                    ));
                    break;
                case Subscribers::HISTORY_IMPORT:
                    $history['Text'] = self::DepunycodeEmailAddress($history['HistoryData']);
                    break;
                case Subscribers::HISTORY_TRANSACTIONAL:
                    $referenceID = $history['HistoryData']['ReferenceID'];
                    $linkText = sprintf(
                        '%s (%s)',
                        $history['HistoryData']['EmailName'],
                        $ReferenceDisplayNames[$referenceID] ?? $referenceID
                    );
                    $history['Text'] = l(
                        $linkText,
                        "email/$OwnerUserID/redirect/{$history['HistoryData']['EmailID']}/edit",
                        array('html' => true)
                    );
                    break;
                case Subscribers::HISTORY_SUBSCRIBED:
                    $history['Text'] = Subscribers::DepunycodeEmailAddress($history['HistoryData']);
                    break;
                case Subscribers::HISTORY_STATE_MOVED:
                    $linkText = t(
                        'Subscriber moved from state "!sourceState" to state "!targetState" in campaign "!campaign".',
                        array(
                            '!sourceState' => $history['HistoryData']['sourceStateName'],
                            '!targetState' => $history['HistoryData']['targetStateName'],
                            '!campaign' => $history['HistoryData']['campaignName'],
                        )
                    );
                    $history['Text'] = $history['Text'] = l(
                        $linkText,
                        "build/marketing-cockpit/{$history['HistoryData']['campaignId']}",
                        array('html' => true)
                    );
                    break;
                case Subscribers::HISTORY_SMS_INBOUND:
                default:
                    $history['Text'] = $history['HistoryData'];
                    break;
            }
        }

        //sort by date
        klicktipp_uasort($HistoryArray, $ArraySort);

        return $HistoryArray;
    }

    private static function CreateFailedTransactionEmailHistoryEntries(&$HistoryArray, $UserID, $QueueEntries)
    {
        foreach ($QueueEntries as $QueueEntry) {
            $arrow = utf8_decode("&#x00BB;");
            $TagName = t("Email") . " $arrow {$QueueEntry['CampaignName']} $arrow ";
            // NOTE: MigrationLinks -> will be migrated with email module
            $editlink = "emails/$UserID/" . $QueueEntry['RelAutoResponderID'];
            switch ($QueueEntry['StatusReason']) {
                case TRANSACTION_REASON_SUBSCRIBER_MATCHED_NEGATIVES:
                    $TagName .= t("Not sent due to matching negative email conditions");
                    break;
                case TRANSACTION_REASON_TIMETOSEND_IS_PAST:
                    $TagName .= t("Not sent as time to sent from custom field laid in the past");
                    break;
                case TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE:
                    $TagName = t("Automation") . " $arrow {$QueueEntry['CampaignName']} $arrow " . t(
                        "Failed due to deleted automation state"
                    );
                    $objCampaign = CampaignsProcessFlow::FromID($UserID, $QueueEntry['RelAutoResponderID']);
                    if ($objCampaign) {
                        $editlink = "automations/$UserID/edit/{$QueueEntry['RelAutoResponderID']}/cockpit";
                    }
                    break;
            }

            $Value = array(
                'HistoryType' => Subscribers::HISTORY_SENDFAILED,
                'HistoryData' => l($TagName, $editlink, array('html' => true)),
                'HistoryDate' => $QueueEntry['TimeToSend'],
                'ReferenceID' => $QueueEntry['ReferenceID']
            );

            $HistoryArray[] = $Value;
        }
    }

    /**
     * Read pending entries of the transactional email queue
     * @param int $UserID
     * @param int $SubscriberID
     * @param array $ArraySort
     * @param array|int[] list of reference ids to filter for
     **/
    public static function ReadPendingHistory(
        $UserID,
        $SubscriberID,
        $ArraySort = array('HistoryDate' => 'DESC'),
        $ReferenceIDs = []
    ) {
        $HistoryArray = array();

        foreach (
            [
                TransactionalQueue::TABLE_NAME,
                AutoresponderQueue::TABLE_NAME,
                NewsletterQueue::TABLE_NAME
            ] as $tableName
        ) {
            self::CreatePendingTransactionalHistoryEntries(
                $HistoryArray,
                $UserID,
                $SubscriberID,
                $tableName,
                $ReferenceIDs
            );
        }

        // sort by date
        klicktipp_uasort($HistoryArray, $ArraySort);

        return $HistoryArray;
    }

    private static function CreatePendingTransactionalHistoryEntries(
        &$HistoryArray,
        $UserID,
        $SubscriberID,
        $tableName,
        $ReferenceIDs = []
    ) {
        $DisplayNames = Reference::getDisplayNames($UserID);

        $QueueEntries = TransactionEmails::RetrievePendingTransactions(
            $UserID,
            $SubscriberID,
            $ReferenceIDs,
            $tableName
        );

        foreach ($QueueEntries as $QueueEntry) {
            switch ($QueueEntry['StatusEnum']) {
                case TransactionEmails::STATUS_PAUSED:
                    // state is waiting for matching condition
                    $TimeToSend = time();
                    $HistoryType = Subscribers::HISTORY_PAUSED;
                    $message = t("is waiting for a matching condition");
                    break;
                case TransactionEmails::STATUS_PAUSED_QUEUE_JOB:
                    // state is waiting for a batch job to be completed
                    $TimeToSend = time();
                    $HistoryType = Subscribers::HISTORY_PROCESSING;
                    $message = t("is currently being processed");
                    break;
                case TransactionEmails::STATUS_PAUSED_AFTER:
                case TransactionEmails::STATUS_PAUSED_AFTER_NO:
                    // state is at an open end in an automation
                    $TimeToSend = $QueueEntry['TimeToSend'];
                    $HistoryType = Subscribers::HISTORY_PAUSED;
                    $message = t("is an open end");
                    break;
                case TransactionEmails::STATUS_PAUSED_RESTART:
                    // subscribers is paused at an restart action waiting for a trigger
                    // after which the start condition is checked for true
                    $TimeToSend = time(); // show on top
                    $HistoryType = Subscribers::HISTORY_PAUSED_RESTART;
                    $message = t("History::ProcessFlow::Restart::Waiting for the restart condition to be true");

                    if ($QueueEntry['TimeToSend'] > $TimeToSend) {
                        $TimeToSend = $QueueEntry['TimeToSend'];
                        $message = t("History::ProcessFlow::Restart::Pause for 5 minutes until restart condition will be checked");
                    }

                    break;
                case TransactionEmails::STATUS_WAITING_FOR_TEMPORAL_CONDITION:
                    $TimeToSend = $QueueEntry['TimeToSend'];
                    $HistoryType = Subscribers::HISTORY_WAITING_FOR_TEMPORAL_CONDITION;
                    $message = t(
                        'History::ProcessFlow::Waiting for temporal condition. Next check at !time',
                        ['!time' => date('d.m.Y H:i:s', $TimeToSend)]
                    );
                    break;
                case TransactionEmails::STATUS_PENDING:
                case TransactionEmails::STATUS_PENDING_AFTER:
                default:
                    $TimeToSend = $QueueEntry['TimeToSend'];
                    if ($TimeToSend < time()) {
                        // state is currently being processed
                        $HistoryType = Subscribers::HISTORY_PROCESSING;
                        $message = t("is currently being processed");
                    } else {
                        // state is scheduled to be executed
                        $HistoryType = Subscribers::HISTORY_PENDING;
                        $message = t(
                            "is scheduled to be executed. This can take between @min_delay and @max_delay minutes.",
                            [
                                '@min_delay' => variable_get('klicktipp_wts_min_multisenddelay', 0),
                                '@max_delay' => variable_get('klicktipp_wts_max_multisenddelay', 0)
                            ]
                        );
                    }
                    break;
            }

            $arrow = utf8_decode("&#x00BB;");
            $AutoResponderTriggerTypeEnum = $QueueEntry['AutoResponderTriggerTypeEnum'];

            // get campaign
            $ObjectCampaign = Campaigns::FromID($UserID, $QueueEntry['RelAutoResponderID']);
            if (!$ObjectCampaign instanceof Campaigns) {
                continue;
            }
            $ArrayCampaign = $ObjectCampaign->GetData();

            if (Campaigns::IsProcessflow($AutoResponderTriggerTypeEnum)) {
                // find state
                $State = CampaignsProcessFlow::FindState($ArrayCampaign, $QueueEntry['StateID']);

                if (empty($State)) {
                    //the subscriber is pending on a deleted state
                    //example:  the subsciber is waiting on a wait state -> stop campaign ->
                    //          delete wait state -> start campaign
                    //the campaign will fail for the subscriber when TimeToSend is reach
                    //do not show the pending entry since we do not have a name
                    continue;
                }

                // get name of state
                $StateName = CampaignsProcessFlow::GetStateName($State);
                $ReferenceName = ' (' . $DisplayNames[$QueueEntry['ReferenceID']] . ')';
                $TagName = t("History::Campaign") . ": {$ArrayCampaign['CampaignName']} $arrow " . t("History::Action") . " $StateName$ReferenceName $arrow $message";
                $HistoryLink = l(
                    $TagName,
                    "build/marketing-cockpit/" . $QueueEntry['RelAutoResponderID'],
                    array('html' => true)
                );
            } else {
                // is autoresponder, sms campaign or campaign
                $type = (Campaigns::IsSMSCampaign($AutoResponderTriggerTypeEnum)) ? t("SMS") : t("Email");
                $ReferenceName = empty($ArrayCampaign['SendToOnlyOneSubscription']) ? ' (' . $DisplayNames[$QueueEntry['ReferenceID']] . ')' : '';
                $TagName = "$type $arrow {$QueueEntry['CampaignName']}$ReferenceName $arrow $message";
                $HistoryLink = l($TagName, $ObjectCampaign->getEntityUrlEdit(), array('html' => true));
            }

            $HistoryArray[] = array(
                'HistoryType' => $HistoryType,
                'HistoryData' => array(
                    'SubscriberID' => $SubscriberID,
                    'CampaignID' => $QueueEntry['RelAutoResponderID'],
                    'Link' => $HistoryLink,
                ),
                'HistoryDate' => $TimeToSend,
                'ReferenceID' => $QueueEntry['ReferenceID'],
                'Text' => $HistoryLink,
            );
        }
    }

    /**
     * Add Subscriber Feedback
     *
     * collects subscriber feedbacks from unsubscriptions, spam report and abuse form
     *
     **/
    public static function AddSubscriberFeedback(
        $OwnerUserID,
        $SubscriberID,
        $EmailAddress,
        $CampaignID,
        $EmailID,
        $Feedback,
        $ReferenceID = 0
    ) {
        $Feedback = trim(check_plain($Feedback));
        if (empty($Feedback)) {
            return;
        }

        $ArrayEmail = Emails::RetrieveEmailByID($OwnerUserID, $EmailID);
        if (empty($ArrayEmail)) {
            return;
        }

        // write history
        $History = array(
            'Feedback' => substr($Feedback, 0, 16000),
            'EmailName' => $ArrayEmail['EmailName'],
            'EmailID' => $ArrayEmail['EmailID'],
            'CampaignID' => $CampaignID,
            'ReferenceID' => $ReferenceID
        );

        Subscribers::WriteHistory($OwnerUserID, $SubscriberID, Subscribers::HISTORY_SUBSCRIBER_FEEDBACK, $History);

        // inform support

        $SenderDomain = DomainSet::SelectValidSenderDomainById($OwnerUserID, $EmailID);

        $SupportFeedback = "<p>" . t("Subscriber") . ": " . Subscribers::DepunycodeEmailAddress($EmailAddress) . "</p>";
        $SupportFeedback .= "<p>" . t("Email: @name", array('@name' => $ArrayEmail['EmailName'])) . "</p>";
        $SupportFeedback .= "<p>" . t(
            "Link to email"
        ) . ": " . $SenderDomain['appurl'] . "email/$OwnerUserID/preview/{$ArrayEmail['EmailID']}/campaign/$CampaignID </p><hr />";
        $SupportFeedback .= check_markup($Feedback);

        Core::SendNotificationEmail(COREAPI_NOTIFY_EMAIL_UNSUBSCRIPTION_FEEDBACK, $SupportFeedback, $OwnerUserID);
    }

    // --- Privacy ---

    /**
     * Privacy Dashboard: Register a subscriber request
     * @param $UserID
     * @param $SubscriberID
     * @param $HistoryType
     * @param array $Data
     *
     * @return bool
     */
    static function SubscriberRequestRegister($UserID, $SubscriberID, $HistoryType, $Data = array())
    {
        $Settings = VarPrivacy::GetSettings($UserID);

        $ReferenceID = (empty($Data['ReferenceID'])) ? 0 : $Data['ReferenceID'];
        $HistoryData = array(
            'CampaignID' => (empty($Data['CampaignID'])) ? 0 : $Data['CampaignID'],
            'EmailID' => (empty($Data['EmailID'])) ? 0 : $Data['EmailID'],
            'ReferenceID' => $ReferenceID,
        );

        $HistoryDate = time();
        switch ($HistoryType) {
            case Subscribers::HISTORY_REQUEST_INFORMATION:
                // wait some time to avoid race condition on double click
                time_nanosleep(0, mt_rand(10000, 99999));
                if (VarPrivacy::HasSubscriberRequestedInformation($UserID, $SubscriberID)) {
                    //the subscriber has already requested his informormation and the task is still pending
                    //do not register a second request to not blow up the history
                    return true;
                }
                break;
            case Subscribers::HISTORY_REQUEST_UPDATE:
                $HistoryData['Fields'] = $Data;
                break;
            case Subscribers::HISTORY_REQUEST_DELETE:
                break;
            case Subscribers::HISTORY_REQUEST_INFORMATION_FINISHED:
            case Subscribers::HISTORY_REQUEST_UPDATE_FINISHED:
                //in case of auto send/update, make sure the finished entries come after the requests
                $HistoryDate += 1;
                break;
            default:
                return false;
        }

        //register the request in the subscriber history
        Subscribers::WriteHistory($UserID, $SubscriberID, $HistoryType, $HistoryData, $HistoryDate);

        //Auto tasks
        //Note: even if the auto tasks fail, the function returns TRUE, since the request was registered successfully
        if ($HistoryType == Subscribers::HISTORY_REQUEST_INFORMATION && !empty($Settings['AutoSendInfo'])) {
            //send information email automatically with the current template
            VarPrivacy::SendInformationEmail($UserID, $SubscriberID);
        } elseif ($HistoryType == Subscribers::HISTORY_REQUEST_UPDATE && !empty($Settings['AutoUpdate'])) {
            //automatically update the subscriber with the values he specified in the form (no validation by user, empty fields will be deleted)
            VarPrivacy::ProcessUpdateSubscriber($UserID, $SubscriberID, $HistoryData['Fields'], $ReferenceID);
        } elseif ($HistoryType == Subscribers::HISTORY_REQUEST_DELETE && !empty($Settings['AutoDelete'])) {
            //delete the subscriber immediately if he doesn't have one of the specified tags
            VarPrivacy::ProcessDeleteSubscriber(
                $UserID,
                $SubscriberID,
                $ReferenceID,
                Subscribers::DELETION_TYPE_UNSUBSCRIPTION
            );
        }

        return true;
    }

    /**
     * Privacy Dashboard: Check if a subscriber requested the deletion of all of his data
     * @param $UserID
     * @param $SubscriberID
     *
     * @return bool
     * @see: Subscribers::RemoveSubscribersByID()
     */
    static function SubscriberRequestedDeletion($UserID, $SubscriberID)
    {
        $query = "SELECT COUNT(*) FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND HistoryType = :HistoryType";
        $result = db_query($query, array(
            ':RelOwnerUserID' => $UserID,
            ':RelSubscriberID' => $SubscriberID,
            ':HistoryType' => Subscribers::HISTORY_REQUEST_DELETE
        ))->fetchField();

        return (!empty($result));
    }

    /**
     * Remove all privacy delete requests for a subscriber
     * UseCase: the subscriber requested deletion (instant unsubscribe), then re-subscribes and confirms
     * @param $UserID
     * @param $SubscriberID
     */
    static function SubscriberRemoveDeleteRequests($UserID, $SubscriberID)
    {
        $query = "DELETE FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND HistoryType = :HistoryType";
        db_query($query, array(
            ':RelOwnerUserID' => $UserID,
            ':RelSubscriberID' => $SubscriberID,
            ':HistoryType' => Subscribers::HISTORY_REQUEST_DELETE
        ));
    }

    /**
     * Remove white spaces, encode domain of given $emailAddress (punycode).
     *
     * @param $emailAddress String the last "@" is used to identify the start of
     *  the domain part; alls chars before are the local part
     *
     * @return mixed|string a lower case email address.
     */
    public static function PunycodeEmailAddress($emailAddress)
    {
        $emailAddress = trim($emailAddress);

        [$localPart, $domain] = Subscribers::SplitEmailAddressParts($emailAddress);
        if (!empty($domain)) {
            $domain = DomainUtils::punycodeDomain($domain);
            // $localPart: as of 2012 (https://tools.ietf.org/html/rfc6531) you can use international
            // characters above U+007F, encoded as UTF-8.
        }
        // TODO unclear whether it is necessary to use mb_strtolower() or not
        //  depends on the "equality" of email addresses with UTF-8 chars in local part, but
        //  rfc 6531 doesn't define "equality", so it depends on a mail server's implementation;
        //  therefor the local part is stored unchanged and only mb_strtolower()'ed
        //  for comparisons
        return $localPart . $domain;
    }

    /**
     * Splits a given email address into local part, domain and postfix.
     * This method works on invalid email addresses too.
     *
     * @param $emailAddress
     *
     * @return array with 2 elements: local part with trailing "@" if contained, domain
     */
    public static function SplitEmailAddressParts($emailAddress)
    {
        $localPart = $emailAddress;
        $domain = "";

        if (!empty($emailAddress)) {
            // only encode the domain, but not the user part (before @)!
            // because "<EMAIL>"@example.com is a valid address,
            // the last "@" is used to identify the domain's start
            $atPos = strrpos($emailAddress, "@");
            if ($atPos !== false) {
                $localPartLength = $atPos + 1;
                $localPart = substr($emailAddress, 0, $localPartLength);
                $domainLength = strlen($emailAddress) - $localPartLength;
                $domain = substr($emailAddress, $localPartLength, $domainLength);
            }
        }

        return [$localPart, $domain];
    }

    /**
     * Create human readable email address which is punycoded.
     *
     * @param $emailAddress
     *
     * @return string
     */
    public static function DepunycodeEmailAddress($emailAddress)
    {
        [$localPart, $domain] = Subscribers::SplitEmailAddressParts($emailAddress);
        if (!empty($domain)) {
            $domain = DomainUtils::depunycodeDomain($domain);
            // $localPart: as of 2012 (https://tools.ietf.org/html/rfc6531) you can use international
            // characters above U+007F, encoded as UTF-8.
        }
        return $localPart . $domain;
    }

    /**
     * @param stdClass $user
     * @param int|string $subscriberId Subscriber ID or SubscriberKey
     * @return stdClass
     * @throws \Doctrine\DBAL\Exception
     * @throws Exception
     */
    public static function retrieveFullSubscriberWithTaggings(stdClass $user, $subscriberId): stdClass
    {
        [$UserID, $SubscriberID, $ReferenceID] = KlickTippAPIUtils::DecodeSubscriberKey($user, $subscriberId);
        if (!$SubscriberID) {
            return services_error(t('There is no such subscriber.'), 404);
        }

        // get subscriber object with all data
        $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
        if (empty($FullSubscriberWithFields)) {
            return services_error(t('There is no such subscriber.'), 404);
        }

        $result = kt_query(
            "SELECT tt.RelTagID, tt.SubscriptionDate, tag.Category, tag.EntityID FROM {tagging} tt " .
            " INNER JOIN {tag} tag ON tt.RelOwnerUserID = tag.RelOwnerUserID AND tt.RelTagID = tag.TagID " .
            " WHERE tt.RelOwnerUserID = :RelOwnerUserID AND tt.RelSubscriberID = :RelSubscriberID  AND (tag.MultiValue = 0 OR tt.ReferenceID = :ReferenceID)",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $FullSubscriberWithFields['SubscriberID'],
                ':ReferenceID' => $ReferenceID,
            ]
        );
        while ($tagging = kt_fetch_array($result)) {
            $FullSubscriberWithFields['Taggings'][] = $tagging['RelTagID'];
            switch ($tagging['Category']) {
                case Tag::CATEGORY_MANUAL:
                    $FullSubscriberWithFields['ManualTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_SMARTLINK:
                    $FullSubscriberWithFields['SmartLinks'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_SENT:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['EmailsSent'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_OPENED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['EmailsOpened'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_CLICKED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['EmailsClicked'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_VIEWED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['EmailsViewed'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_CONVERTED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['Converted'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_OUTBOUND:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['Outbound'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_KAJABI_ACTIVATE:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['KajabiActivated'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_KAJABI_DEACTIVATE:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['KajabiDeactivated'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_TAGGING_PIXEL:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['TaggingPixelTriggered'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_EMAIL_SENT:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['NotificationEmailsSent'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_EMAIL_OPENED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['NotificationEmailsOpened'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_EMAIL_CLICKED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['NotificationEmailsClicked'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_EMAIL_VIEWED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['NotificationEmailsViewed'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_SMS_SENT:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['NotificationSmsSent'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_SMS_CLICKED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['NotificationSmsClicked'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_AUTOMATION_STARTED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['CampaignsStarted'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_AUTOMATION_FINISHED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['CampaignsFinished'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_APIKEY:
                case Tag::CATEGORY_LISTBUILDING_BUSINESSCARD:
                case Tag::CATEGORY_LISTBUILDING_EVENT:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['ApiSubscriptions'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_REQUEST:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['RequestSubscriptions'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_SMS:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['SmsSubscriptions'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_FORMS:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['FormSubscriptions'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_PAYMENT:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['PaymentCompletions'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_REFUNDED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['PaymentRefunds'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_CHARGEDBACK:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['PaymentChargebacks'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['RebillCancelations'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_REBILL_RESUMED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['RebillsResumptions'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED_LAST_DAY:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['RebillExpirations'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['DigistoreAffiliations'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['LandingPageSubscribed'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_PAYMENT_COMPLETED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['PaymentActivations'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_PAYMENT_EXPIRED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    $FullSubscriberWithFields['PaymentExpirations'][$tagging['EntityID']] = $tagging['SubscriptionDate'];
                    break;
                case Tag::CATEGORY_PLUGIN_INBOUND_READY:
                case Tag::CATEGORY_PLUGIN_INBOUND_STARTED:
                case Tag::CATEGORY_PLUGIN_INBOUND_INPROGRESS:
                case Tag::CATEGORY_PLUGIN_INBOUND_FINISHED:
                    $FullSubscriberWithFields['SmartTags'][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
                    break;
            }
        }

        $subscriber = (object)$FullSubscriberWithFields;
        KlickTippAPIUtils::FilterObject('subscriber', $subscriber);
        return $subscriber;
    }

    public static function IsSameEmailAddress($Email1, $Email2)
    {
        return mb_strtolower($Email1, "UTF-8") === mb_strtolower($Email2, "UTF-8");
    }

    public static function merge($userID, $toSubscriberID, $fromSubscriberID)
    {
        kt_begin_transaction();
        $mergeReferenceID = Reference::createMergeReference($userID, $fromSubscriberID);

        // ==================================== TAGGINGS ==================================================================

        // switch multi value taggings with default reference to merge reference
        db_query(
            'UPDATE {tagging} tagging INNER JOIN {tag} tag ' .
            'ON tagging.RelOwnerUserID = tag.RelOwnerUserID AND tagging.RelTagID = tag.TagID ' .
            'SET tagging.RelSubscriberID = :toSubscriberID, ReferenceID = :mergeReference ' .
            'WHERE tag.RelOwnerUserID = :userID AND tagging.RelSubscriberID = :fromSubscriberID AND tag.MultiValue = 1 AND tagging.ReferenceID = 0',
            [
                ':userID' => $userID,
                ':toSubscriberID' => $toSubscriberID,
                ':fromSubscriberID' => $fromSubscriberID,
                ':mergeReference' => $mergeReferenceID
            ]
        );

        // now delete all tags from duplicate which exist in primary subscriber and are not multivalue
        // or have same reference id
        db_query(
            'DELETE tfrom FROM {tagging} tfrom INNER JOIN {tagging} tto ' .
            'ON tfrom.`RelOwnerUserID` = tto.RelOwnerUserID AND tfrom.RelTagID = tto.RelTagID ' .
            'WHERE tfrom.RelOwnerUserID = :userID AND tfrom.RelSubscriberID = :fromSubscriberID AND tto.RelSubscriberID = :toSubscriberID ' .
            // tagging should be delete in 2 cases:
            // 1) it is an single value tag and target subscriber has  a tagging already
            // 2) it's an multi value tag and target subscriber has  a tagging with *same reference already
            'AND tfrom.ReferenceID = tto.ReferenceID',
            [':userID' => $userID, ':toSubscriberID' => $toSubscriberID, ':fromSubscriberID' => $fromSubscriberID]
        );

        kt_update_rows(
            ['RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID],
            '{tagging}'
        );

        // ==================================== CUSTOM VALUES  =============================================================

        // switch multi value custom values with default reference to merge reference

        $addressFields = ['Street1', 'Street2', 'City', 'State', 'Zip'];
        $targetSubscriberHasAddressValues = db_query(
            'SELECT 1 FROM {custom_field_values} WHERE RelOwnerUserID = :userID AND RelSubscriberID = :toSubscriberID AND RelFieldID IN (:addressFields) LIMIT 1',
            [':userID' => $userID, ':toSubscriberID' => $toSubscriberID, ':addressFields' => $addressFields]
        )->fetchField() == '1';

        // address fields build a logical group. we can't mix address values from duplicate and primary subscriber
        // so we delete all address values from duplicate if primary subscriber has at least one 1 address value
        if ($targetSubscriberHasAddressValues) {
            db_query(
                'DELETE FROM {custom_field_values} WHERE RelOwnerUserID = :userID AND RelSubscriberID = :fromSubscriberID AND RelFieldID IN (:addressFields)',
                [':userID' => $userID, ':fromSubscriberID' => $fromSubscriberID, ':addressFields' => $addressFields]
            );
        }

        // assign 0-reference values of duplicate to merge-reference and primary subscriber
        // this is necessary for all multi-value fields as single value fields with 0-reference are dropped
        // because of ARRAY_TYPE (multi-value w/o field def) ... get all single-value fields ...
        $singleValueFields = db_query(
            'SELECT CustomFieldID FROM {custom_fields} WHERE RelOwnerUserID = :userID AND MultiValue = 0',
            [':userID' => $userID]
        )->fetchCol();
        $singleValueFields = array_merge($singleValueFields, array_keys(CustomFields::$GlobalCustomFieldDefs));
        // ... then change all fields not in that set
        db_query(
            'UPDATE {custom_field_values}  ' .
            'SET RelSubscriberID = :toSubscriberID, ReferenceID = :mergeReference ' .
            'WHERE RelOwnerUserID = :userID AND RelSubscriberID = :fromSubscriberID AND ReferenceID = 0 ' .
            'AND RelFieldID NOT IN (:singleValueFields)',
            [
                ':userID' => $userID,
                ':toSubscriberID' => $toSubscriberID,
                ':fromSubscriberID' => $fromSubscriberID,
                ':mergeReference' => $mergeReferenceID,
                ':singleValueFields' => $singleValueFields
            ]
        );

        // now delete all values from duplicate which has a corresponding value in primary subscriber
        // these are not multivalues (all values having a 0-reference) or have the same reference id
        db_query(
            'DELETE vfrom FROM {custom_field_values} vfrom INNER JOIN {custom_field_values} vto ' .
            'ON vfrom.RelOwnerUserID = vto.RelOwnerUserID AND vfrom.RelFieldID = vto.RelFieldID ' .
            'WHERE vfrom.RelOwnerUserID = :userID AND vfrom.RelSubscriberID = :fromSubscriberID AND vto.RelSubscriberID = :toSubscriberID ' .
            'AND vfrom.ReferenceID = vto.ReferenceID',
            [':userID' => $userID, ':toSubscriberID' => $toSubscriberID, ':fromSubscriberID' => $fromSubscriberID]
        );

        kt_update_rows(
            ['RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID],
            '{custom_field_values}'
        );

        // ==================================== TRANSACTIONAL EMAIL QUEUE  =================================================

        kt_update_rows(
            ['ReferenceID' => $mergeReferenceID, 'RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID, 'ReferenceID' => 0],
            '{' . TransactionalQueue::TABLE_NAME . '}'
        );

        kt_update_rows(
            ['RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID],
            '{' . TransactionalQueue::TABLE_NAME . '}'
        );

        // ==================================== AUTORESPONDER QUEUE  =================================================

        kt_update_rows(
            ['ReferenceID' => $mergeReferenceID, 'RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID, 'ReferenceID' => 0],
            '{' . AutoresponderQueue::TABLE_NAME . '}'
        );

        kt_update_rows(
            ['RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID],
            '{' . AutoresponderQueue::TABLE_NAME . '}'
        );

        // ==================================== NEWSLETTER QUEUE  =================================================

        kt_update_rows(
            ['ReferenceID' => $mergeReferenceID, 'RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID, 'ReferenceID' => 0],
            '{' . NewsletterQueue::TABLE_NAME . '}'
        );

        kt_update_rows(
            ['RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID],
            '{' . NewsletterQueue::TABLE_NAME . '}'
        );

        // ==================================== SUBSCRIBER QUEUE  =================================================

        // delete subscriber id with same primary key except subscriber id (otherwise there will be conflicts during update)
        db_query(
            'DELETE qfrom FROM {subscriber_queue} qfrom INNER JOIN {subscriber_queue} qto ' .
            'ON qfrom.RelOwnerUserID = qto.RelOwnerUserID AND qfrom.JobType = qto.JobType AND qfrom.SeqNo = qto.SeqNo AND qfrom.ReferenceID = qto.ReferenceID ' .
            'WHERE qfrom.RelOwnerUserID = :userID AND qfrom.RelSubscriberID = :fromSubscriberID AND qto.RelSubscriberID = :toSubscriberID',
            [':userID' => $userID, ':toSubscriberID' => $toSubscriberID, ':fromSubscriberID' => $fromSubscriberID]
        );

        db_query(
            'UPDATE {subscriber_queue} SET ReferenceID = IF(ReferenceID = 0, :ReferenceID, ReferenceID), ' .
            'RelSubscriberID = :ToSubscriberID, ' .
            'ShardID = IF(ShardID < 0, ShardID, :ToSubscriberID % 1024) ' . // Shard < 0 => customer in own slice
            'WHERE RelOwnerUserID = :UserID AND RelSubscriberID = :FromSubscriberID',
            [
                ':ReferenceID' => $mergeReferenceID,
                ':ToSubscriberID' => $toSubscriberID,
                ':UserID' => $userID,
                ':FromSubscriberID' => $fromSubscriberID,
            ]
        );

        // ==================================== SUBSCRIBER HISTORY  =================================================

        kt_update_rows(
            ['RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID],
            '{subscriber_history}'
        );

        // ==================================== PAYMENTS  =================================================

        kt_update_rows(
            ['RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID],
            '{payments}'
        );

        // ==================================== FBL REPORTS  =================================================

        kt_update_rows(
            ['SubscriberID' => $toSubscriberID],
            ['SubscriberID' => $fromSubscriberID],
            '{fbl_reports}'
        );

        // ==================================== NPSCORE  =================================================

        kt_update_rows(
            ['SubscriberID' => $toSubscriberID],
            ['SubscriberID' => $fromSubscriberID],
            '{npscore}'
        );


        // ==================================== SUBSCRIPTIONS  =============================================================

        // SUBSCRIPTIONS PART 1: make sure there is at most 1 empty subscription with subscriberID of primary contact in ContactInfo

        $toEmptySubscription = Subscription::RetrieveSubscriptionByContactInfo(
            $userID,
            $toSubscriberID,
            Subscription::SUBSCRIPTIONTYPE_NONE
        );
        $fromEmptySubscription = Subscription::RetrieveSubscriptionByContactInfo(
            $userID,
            $fromSubscriberID,
            Subscription::SUBSCRIPTIONTYPE_NONE
        );

        if (!$toEmptySubscription && $fromEmptySubscription) {
            // move the empty subscription from duplicate to primary contact
            kt_update_rows(
                ['ContactInfo' => $toSubscriberID, 'RelSubscriberID' => $toSubscriberID],
                [
                    'RelOwnerUserID' => $userID,
                    'SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_NONE,
                    'ContactInfo' => $fromSubscriberID
                ],
                '{subscription}'
            );
        }
        if ($fromEmptySubscription) {
            // copy all references to empty subscription, but be aware of existing ones
            db_query(
                "UPDATE IGNORE {subscription_reference} SET ContactInfo = :to, RelSubscriberID = :to " .
                " WHERE RelOwnerUserID = :RelOwnerUserID AND SubscriptionType = :SubscriptionType AND ContactInfo = :from ",
                [
                    ':to' => $toSubscriberID,
                    ':RelOwnerUserID' => $userID,
                    ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_NONE,
                    ':from' => $fromSubscriberID,
                ]
            );
        }
        // remove obsolete empty subscription in duplicate
        kt_delete_rows(
            [
                'RelOwnerUserID' => $userID,
                'SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_NONE,
                'ContactInfo' => $fromSubscriberID
            ],
            '{subscription}'
        );
        kt_delete_rows(
            [
                'RelOwnerUserID' => $userID,
                'SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_NONE,
                'ContactInfo' => $fromSubscriberID
            ],
            '{subscription_reference}'
        );

        // SUBSCRIPTIONS PART 2: switch subscriptions assigned to 0-reference of duplicate to
        kt_update_rows(
            ['ReferenceID' => $mergeReferenceID, 'RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID, 'ReferenceID' => 0],
            '{subscription_reference}'
        );

        // SUBSCRIPTIONS PART 3: mark subscription of duplicate optional, if primary contact has subscription for same contact type and reference
        db_query(
            'UPDATE  {subscription_reference} rfrom INNER JOIN {subscription_reference} rto ' .
            'ON rfrom.RelOwnerUserID = rto.RelOwnerUserID AND rfrom.SubscriptionType = rto.SubscriptionType AND rfrom.ReferenceID = rto.ReferenceID ' .
            'SET rfrom.Optional = 1 ' .
            'WHERE rfrom.RelOwnerUserID = :userID AND rfrom.RelSubscriberID = :fromSubscriberID AND rto.RelSubscriberID = :toSubscriberID',
            [':userID' => $userID, ':toSubscriberID' => $toSubscriberID, ':fromSubscriberID' => $fromSubscriberID]
        );

        // SUBSCRIPTIONS PART 4: move all subscriptions to primary subscriber

        kt_update_rows(
            ['RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID],
            '{subscription}'
        );

        kt_update_rows(
            ['RelSubscriberID' => $toSubscriberID],
            ['RelOwnerUserID' => $userID, 'RelSubscriberID' => $fromSubscriberID],
            '{subscription_reference}'
        );

        kt_delete_rows(['RelOwnerUserID' => $userID, 'SubscriberID' => $fromSubscriberID], '{subscribers}');

        kt_commit_transaction();

        foreach (Reference::getSubscriberRelatedIDs($userID, $toSubscriberID) as $referenceID) {
            TransactionEmails::RegisterAutomations($userID, $toSubscriberID, $referenceID, true);
        }
    }

    /**
     * @param $referenceID
     * @param $relOwnerUserID
     * @param $subscriberID
     * @return void
     * @throws \Doctrine\DBAL\Exception
     */
    public static function startCampaigns($referenceID, $relOwnerUserID, $subscriberID)
    {
        TransactionEmails::GetCachedFullSubscriber(0, 0, 0, true);

        // get all active automations (all data, see Campaigns::FromID)
        $result2 = db_query(
            "SELECT * FROM {campaigns} c " .
            " INNER JOIN {campaign_stats} s ON s.RelCampaignID = c.CampaignID AND s.RelOwnerUserID = c.RelOwnerUserID " .
            " WHERE c.RelOwnerUserID = :RelOwnerUserID " .
            " AND c.AutoResponderTriggerTypeEnum IN (:AutoResponderTriggerTypeEnum)" .
            " AND c.CampaignStatusEnum IN (:CampaignStatusEnum)",
            [
                ':RelOwnerUserID' => $relOwnerUserID,
                ':AutoResponderTriggerTypeEnum' => [
                    Campaigns::TRIGGER_TYPE_PROCESSFLOW
                ],
                ':CampaignStatusEnum' => Campaigns::$ArrayCampaignStatiSending,
            ]
        );
        while ($eachCampaign = kt_fetch_array($result2)) {
            $objectCampaign = Campaigns::FromArray($eachCampaign);
            if (!empty($objectCampaign)) {
                $arrayCampaign = $objectCampaign->GetData();

                // check automation
                TransactionEmails::RunStarted($relOwnerUserID, $subscriberID, $referenceID, $arrayCampaign, false, false);
            }
        }

        // execute "virtual campaigns" (prequel of amember migration)
        $marketing_account = variable_get('klicktipp_marketing_account_id', 0);
        if ($marketing_account == $relOwnerUserID) {
            Libraries::include('vcampaigns.inc');
            klicktipp_vcampaigns_trigger($relOwnerUserID, $subscriberID, $referenceID);
        }
    }

    /**
     * @param int[] $types
     * @throws DoctrineDBALException
     */
    public static function countSubscriberFeedback(int $userId, array $types): int
    {
        $sql = "SELECT COUNT(*) FROM {subscriber_history} WHERE " .
            "RelOwnerUserID = :RelOwnerUserID AND HistoryType IN (:types)";

        return db_query($sql, [
            ':RelOwnerUserID' => $userId,
            ':types' => $types
        ])->fetchField() ?: 0;
    }
} // END class Subscribers

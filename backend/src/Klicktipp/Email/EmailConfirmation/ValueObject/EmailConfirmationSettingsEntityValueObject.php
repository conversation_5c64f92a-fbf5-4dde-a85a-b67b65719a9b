<?php

namespace App\Klicktipp\Email\EmailConfirmation\ValueObject;

use App\Klicktipp\EmailsConfirmationEmail;
use App\Klicktipp\OptInProcess\ValueObject\OptInProcessEntityLinksValueObject;
use stdClass;

class EmailConfirmationSettingsEntityValueObject
{
    public int $id;
    public string $name;
    public string $type = 'confirmation';
    public string $usageType = 'confirmation';
    public int $version = 0;
    public int $lastUpdated;
    public int $useNotes;
    public string $notes;
    public string $senderName;
    public string $senderEmail;
    public string $replyEmail;
    public string $ccEmail;
    public string $bccEmail;
    public string $senderDomain;
    public string $subject;
    public int $contentType;
    public string $plainContent;
    public string $plainFromHtml;
    public string $htmlContent;
    public int $deactivateHtmlEditor;
    public ?OptInProcessEntityLinksValueObject $links = null;

    /**
     * @param EmailsConfirmationEmail $entity
     * @param stdClass $account
     * @param string $defaultSenderEmail
     * @param string $defaultReplyEmail
     * @param string $defaultSenderDomain
     * @param string $plainFromHtml
     * @param OptInProcessEntityLinksValueObject|null $links
     * @return self
     */
    public static function create(
        EmailsConfirmationEmail $entity,
        stdClass $account,
        string $defaultSenderEmail,
        string $defaultReplyEmail,
        string $defaultSenderDomain,
        string $plainFromHtml,
        OptInProcessEntityLinksValueObject $links = null
    ): self {

        $fromName = $entity->GetData('FromName');
        if (empty($fromName)) {
            $fromName = $account->CompanyName ?: $account->FirstName . ' ' . $account->LastName;
        }

        $valueObject = new self();

        $valueObject->id = $entity->GetData('EmailID');
        $valueObject->name = $entity->GetData('EmailName');
        $valueObject->version = $entity->GetData('Version') ? 1 : 0;
        $valueObject->lastUpdated = $entity->GetData('LastUpdated') ?? 0;
        $valueObject->useNotes = empty($entity->GetData('Notes')) ? 0 : 1;
        $valueObject->notes = $entity->GetData('Notes') ?? '';
        $valueObject->senderName = $fromName;
        $valueObject->senderEmail = $entity->GetData('FromEmail') ?: $defaultSenderEmail;
        $valueObject->replyEmail = $entity->GetData('ReplyToEmail') ?: $defaultReplyEmail;
        $valueObject->ccEmail = $entity->GetData('CCEmail');
        $valueObject->bccEmail = $entity->GetData('BCCEmail');
        $valueObject->senderDomain = $entity->GetData('SenderDomain') ?: $defaultSenderDomain;
        $valueObject->subject = $entity->GetData('Subject');
        $valueObject->contentType = $entity->GetData('ContentTypeEnum');
        $valueObject->plainContent = $entity->GetData('PlainContent');
        $valueObject->plainFromHtml = $plainFromHtml;
        $valueObject->htmlContent = $entity->GetData('HTMLContent');
        $valueObject->deactivateHtmlEditor = $entity->GetData('DeactivateCKEditor') ? 1 : 0;
        $valueObject->links = $links;

        return $valueObject;
    }
}

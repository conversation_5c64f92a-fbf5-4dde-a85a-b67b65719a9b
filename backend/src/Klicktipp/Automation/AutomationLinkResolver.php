<?php

namespace App\Klicktipp\Automation;

use App\Klicktipp\Automation\ValueObject\AutomationEntityLinksValueObject;

class AutomationLinkResolver
{
    public function getLinks(
        int $userId,
        int $id
    ): AutomationEntityLinksValueObject {
        return AutomationEntityLinksValueObject::create(
            $this->linkStatistics($userId, $id),
            $this->linkEdit($userId, $id),
            $this->linkCockpit($id),
            $this->linkSchedule($userId, $id),
        );
    }

    public function linkOverview(int $userId): string
    {
        return APP_URL . "app/campaign/automation/$userId";
    }

    public function linkCreate(int $userId): string
    {
        return APP_URL . "app/campaign/automation/$userId/create-automation";
    }

    public function linkStatistics(int $userId, int $id): string
    {
        return APP_URL . "app/campaign/automation/$userId/$id/statistics";
    }

    public function linkEdit(int $userId, int $id): string
    {
        return APP_URL . "app/campaign/automation/$userId/$id/edit";
    }

    public function linkCockpit(int $id): string
    {
        return APP_URL . "build/marketing-cockpit/$id";
    }

    public function linkSchedule(int $userId, int $id): string
    {
        return APP_URL . "app/campaign/automation/$userId/$id/schedule";
    }
}

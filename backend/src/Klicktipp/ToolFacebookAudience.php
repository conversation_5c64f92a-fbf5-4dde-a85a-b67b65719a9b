<?php

namespace App\Klicktipp;

/*
 * Marketing Tool subclass for Outbound Events
 */

class ToolFacebookAudience extends MarketingTools
{
    const EVENT_TYPE_ADD = 0; // add contacts to Facebook audience
    const EVENT_TYPE_REMOVE = 1; // remove contacts from Facebook audience

    //translatable error codes
    const ERROR_NONE = /*t(*/'Successfull'/*)*/
    ;
    const ERROR_NO_EMAIL_ADDRESS = /*t(*/'Subscriber has no email address'/*)*/
    ;
    const ERROR_NO_SUBSCRIBER = /*t(*/'No subscriber selected'/*)*/
    ;
    const ERROR_INVALID_RESPONSE = /*t(*/'Guzzle error'/*)*/
    ;
    const ERROR_ACCESS_EXPIRED = /*t(*/'Facebook access expired'/*)*/
    ;
    const ERROR_INVALID_DATA = /*t(*/'Invalid subscriber data'/*)*/
    ;
    const ERROR_UNKNOWN_ERROR_CODE = /*t(*/'Unknown facebook error'/*)*/
    ;
    const ERROR_DEPRECATED_API = /*t(*/'Deprecated Facebook API version'/*)*/
    ;
    const ERROR_RATE_LIMIT_REACHED = /*t(*/'Rate limit reached'/*)*/
    ;
    const ERROR_REQUEST_TIMEOUT = /*t(*/'Request timeout'/*)*/
    ;
    const ERROR_USER_IS_NOT_APP_ADMIN = /*t(*/'App admin access required'/*)*/
    ;
    const ERROR_RUNTIME_TIMEOUT = /*t(*/'Runtime timeout'/*)*/
    ;

    const WARNING_UMLAUT_MESSAGE = /*t(*/'Add subscriber to facebook audience failure'/*)*/
    ;

    public static $ToolType = MarketingTools::TOOL_TYPE_FACEBOOK_AUDIENCE;

    public static $DefaultFields = array(
        'ToolType' => MarketingTools::TOOL_TYPE_FACEBOOK_AUDIENCE,
        'Deleted' => 0,
        'AssignTagID' => 0,
    );

    public static $DefaultDataFields = array(
        'SmartTagID' => 0,
        'Description' => '',
        'AudienceID' => '', //Facebook Audience ID
        'MarketingAccount' => '', //Facebook Marketing Account that contains the Audience
        'FacebookAccountID' => '', //Facebook Account with access to the Marketing account
        'FacebookAppID' => '', //ID of Facebook App used
        'Active' => 1, //exists in Facebook
        'Schema' => array(), //TODO additional fields like name, city etc.
    );

    public static function FromArray($DBArray)
    {
        $tool = new ToolFacebookAudience($DBArray);

        if (empty($tool->DataFlat)) {
            return false;
        }

        return $tool;
    }

    public static function InsertDB($Data)
    {
        $ToolID = parent::InsertDB($Data);
        if (empty($ToolID)) {
            return false;
        }

        // write all additional data with an update query (as we need the ToolID for that)

        $ToolObject = static::FromID($Data['RelOwnerUserID'], $ToolID);
        $Data = $ToolObject->GetData();

        $Data['SmartTagID'] = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_FACEBOOK_AUDIENCE,
            'EntityID' => $ToolID,
            'RelOwnerUserID' => $Data['RelOwnerUserID'],
        ));

        static::UpdateDB($Data);

        return $ToolID;
    }

    public static function GetEntitiesAsOptionsArray($UserID)
    {
        if (empty($UserID)) {
            return array();
        }

        $OptionsArray = array();

        $result = db_query(
            "SELECT * FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID AND ToolType = :ToolType",
            array(
                ':RelOwnerUserID' => $UserID,
                ':ToolType' => MarketingTools::TOOL_TYPE_FACEBOOK_AUDIENCE,
            )
        );

        while ($DBArray = kt_fetch_array($result)) {
            $ObjectTool = ToolFacebookAudience::FromArray($DBArray);
            if ($ObjectTool && $ObjectTool->GetData('Active')) {
                $ArrayTool = $ObjectTool->GetData();
                $OptionsArray[$ArrayTool['ToolID']] = $ArrayTool['Name'] . " ({$ArrayTool['MarketingAccount']})";
            }
        }

        natcasesort($OptionsArray);

        return $OptionsArray;
    }

    /**
     * Synchronize Facebook Audiences with ToolFacebookAudience's
     * Create new Audiences that have been created in Facebook
     * Set Audiences that have been deleted in Facebook to inactive
     * Update Names and Descriptions that have been changed in Facebook
     * @param $UserID
     * @param $FacebookAccount : facebook graph api result of /me (current facebook user)
     * @param $FBResult : facebook graph api result of /<MARKETING_ACCOUNT>/customeraudiences
     *                   for all the marketing accounts of the current Facebook Account
     *                    array(
     *                      'account_id' => MarketingAccountID
     *                      'name' => Name of Audience (editable in Facebook)
     *                      'description' => Description of Audience (editable in Facebook)
     *                      'subtype' => Facebook has multiple Audience types, we can only work with 'CUSTOM'
     *                    )
     */
    public static function Synchronize($UserID, $FacebookAccount, $FBResult)
    {
        if (empty($UserID) || empty($FacebookAccount) || empty($FBResult)) {
            return array();
        }

        $Data = array(
            'FacebookAccount' => $FacebookAccount,
            'Create' => array(),
            'Update' => array(),
            'Reactivate' => array(),
            'Deactivate' => array(),
            'Merge' => array(),
        );

        //get all connected audiences of the user
        $result = db_query(
            "SELECT * FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID AND ToolType = :ToolType",
            array(
                ':RelOwnerUserID' => $UserID,
                ':ToolType' => MarketingTools::TOOL_TYPE_FACEBOOK_AUDIENCE,
            )
        );


        //get the existing audiences to check if we need to create or update
        $UserAudiences = array();
        while ($DBArray = kt_fetch_array($result)) {
            $ObjectTool = ToolFacebookAudience::FromArray($DBArray);

            if ($ObjectTool) {
                $ArrayTool = $ObjectTool->GetData();
                $UserAudiences[$ArrayTool['AudienceID']] = $ArrayTool;
            }
        }

        //insert, update audiences for the current Facebook Account
        $FBAudiences = array();

        foreach ($FBResult as $audience) {
            if ($audience['subtype'] != 'CUSTOM' || empty($audience['id'])) {
                continue;
            }

            $audience['name'] = (empty($audience['name'])) ? $audience['id'] : $audience['name'];
            $audience['description'] = (empty($audience['description'])) ? '' : $audience['description'];
            $audience['account_id'] = (empty($audience['account_id'])) ? '' : $audience['account_id'];

            $existing = $UserAudiences[$audience['id']];

            if (empty($existing)) {
                //new audience has been added in Facebook

                $Data['Create'][] = array(
                    'RelOwnerUserID' => $UserID,
                    'AudienceID' => $audience['id'],
                    'Name' => $audience['name'],
                    'Description' => $audience['description'],
                    'MarketingAccount' => $audience['account_id'],
                    'FacebookAccountID' => $FacebookAccount['id'],
                    'FacebookAppID' => $FacebookAccount['AppID'],
                );
            } else {
                $ExistingName = $existing['Name'];
                $ExistingDescription = $existing['Description'];

                $existing['Name'] = $audience['name'];
                $existing['Description'] = $audience['description'];

                //the user either changed the facebook app or the facebook user -> merge facebook audience tool
                if ($existing['FacebookAppID'] != $FacebookAccount['AppID'] || $existing['FacebookAccountID'] != $FacebookAccount['id']) {
                    $existing['FacebookAccountID'] = $FacebookAccount['id'];
                    $existing['FacebookAppID'] = $FacebookAccount['AppID'];
                    $Data['Merge'][] = $existing;
                } else {
                    if (empty($existing['Active'])) {
                        $existing['Active'] = 1;
                        $Data['Reactivate'][] = $existing;
                    }

                    if ($ExistingName != $audience['name'] || $ExistingDescription != $audience['description']) {
                        $Data['Update'][] = $existing;
                    }
                }
            }

            //remember all audience ids that are currently in facebook so we can check for deleted ones
            $FBAudiences[] = $audience['id'];
        }

        //check for deleted audiences on facebook
        foreach ($UserAudiences as $audience_id => $DBArray) {
            //if a tool audience of the current facebook account cannot be found in the list of
            //audiences currently in facebook, it must have been deleted -> set inactive
            if (
                !empty($DBArray['Active']) && !in_array(
                    $audience_id,
                    $FBAudiences
                ) && $DBArray['FacebookAccountID'] == $FacebookAccount['id']
            ) {
                $DBArray['Active'] = 0;
                $Data['Deactivate'][] = $DBArray;
            }
        }

        return $Data;
    }

    /**
     * Add a subscriber to the facebook audience
     *
     * @see: https://developers.facebook.com/docs/marketing-api/audiences-api
     *
     * @param $SubscriberID
     * @param $ReferenceID
     *
     * @return array (boolean success, translatable error code, payload for simpletests)
     */
    public function AddSubscriber($SubscriberID, $ReferenceID)
    {
        $UserID = $this->DataFlat['RelOwnerUserID'];
        $AudienceID = $this->DataFlat['AudienceID'];

        //get the access token of the facebook account that this audience was connected with
        $AccessToken = $this->GetValidToken();

        if (!$AccessToken[0]) {
            return $AccessToken; //array(FALSE, ERROR_CODE)
        }

        $AccessToken = $AccessToken[1];

        // --- get subscriber data
        $data = array();

        //besides the email address other data like firstname, lastname etc can be specified
        //@see: https://developers.facebook.com/docs/marketing-api/audiences-api
        //the email address is requiered, but later the user can specify which customfield he also wants to use
        $schema = array('EMAIL');

        $Subscription = Subscription::RetrieveSubscriptionByIDAndReference(
            $UserID,
            $SubscriberID,
            Subscription::SUBSCRIPTIONTYPE_EMAIL,
            $ReferenceID
        );

        if ($Subscription) {
            if (empty($Subscription['ContactInfo'])) {
                Subscribers::WriteHistory(
                    $UserID,
                    $SubscriberID,
                    Subscribers::HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED,
                    array(
                        'AudienceID' => $this->DataFlat['ToolID'],
                        'Reason' => ToolFacebookAudience::ERROR_NO_EMAIL_ADDRESS,
                        'EventType' => ToolFacebookAudience::EVENT_TYPE_ADD,
                        'ReferenceID' => $ReferenceID
                    )
                );

                return array(false, ToolFacebookAudience::ERROR_NO_EMAIL_ADDRESS);
            }

            //the email address is requiered
            //facebook wants the data hashed
            //@see: https://developers.facebook.com/docs/marketing-api/audiences-api
            $subscriber_data[] = hash("sha256", Subscribers::DepunycodeEmailAddress($Subscription['ContactInfo']));

            //TODO: not used yet, add fields specified in schema
            //the schema can be later edited in the ToolAudience edit dialog and will be like this
            //array(
            // '<FACEBOOK_SCHEMA_NAME>' => '<CustomFieldID>',
            //  ...)

            $data[] = $subscriber_data;
        }

        // prepare api post

        if (empty($data)) {
            return array(false, ToolFacebookAudience::ERROR_NO_SUBSCRIBER);
        }

        Libraries::include('guzzle.inc');

        //NOTE: on the live system this variable is never set, there is no settings dialog
        //      for simpletest we can specify a dummy callback that returns expected results
        //      @see: klicktipp_httptest_callback() in callbacks.inc
        $version = Settings::get('klicktipp_facebook_audience_api_version');
        $url = variable_get('simpletest-facebook-graph-api', "https://graph.facebook.com/$version/$AudienceID/users");

        $postdata = array(
            'payload' => json_encode(array(
                'schema' => $schema,
                'data' => $data
            )),
            'access_token' => $AccessToken,
        );

        $response = guzzle_http_request($url, array(
            'timeout' => 10.0,
            'method' => 'POST',
            'data' => $postdata,
        ));

        $response_data = (empty($response->data)) ? array() : json_decode($response->data, true);

        if (!empty($response->error)) {
            //we got a http response error

            //check for timeouts
            $timeouts = array(HTTP_REQUEST_TIMEOUT, 408, 504); //-1, Request Time-out, Gateway Time-out
            if (in_array($response->code, $timeouts)) {
                return array(false, ToolFacebookAudience::ERROR_REQUEST_TIMEOUT);
            }

            if (empty($response_data['error'])) {
                //no error in the response data (from facebook), nothing we can catch

                Subscribers::WriteHistory(
                    $UserID,
                    $SubscriberID,
                    Subscribers::HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED,
                    array(
                        'AudienceID' => $this->DataFlat['ToolID'],
                        'Reason' => ToolFacebookAudience::ERROR_INVALID_RESPONSE,
                        'EventType' => ToolFacebookAudience::EVENT_TYPE_ADD,
                        'ReferenceID' => $ReferenceID
                    )
                );

                watchdog('FacebookAudience', 'FacebookAudience - Add Subscriber: Guzzle error', array(
                    '!response' => (array)$response,
                ), WATCHDOG_WARNING);

                klicktipp_log_umlaut_failure(
                    ToolFacebookAudience::WARNING_UMLAUT_MESSAGE,
                    $Subscription['ContactInfo'],
                    $url,
                    $postdata
                );

                return array(false, ToolFacebookAudience::ERROR_INVALID_RESPONSE, $postdata);
            }
        }

        //check for facebook error no matter what the http response code is
        //Note: facebook could send a 200 or a 400 (Bad Request) with it's own error code in the response data
        if (!empty($response_data['error'])) {
            //we have an error from facebook in the response data

            if ($response_data['error']['code'] == '190') {
                //the token expired for another reason then our expiration date
                $this->InvalidateToken();

                return [false, ToolFacebookAudience::ERROR_ACCESS_EXPIRED, $postdata];
            } elseif ($response_data['error']['code'] == '17') {
                //we reached the rate limit, suspend API calls for 5 minutes and re-queue items during that time
                $this->ThrottleApiCalls();

                return [false, ToolFacebookAudience::ERROR_RATE_LIMIT_REACHED, $postdata];
            } elseif ($response_data['error']['code'] == '272') {
                return [
                    false,
                    ToolFacebookAudience::ERROR_USER_IS_NOT_APP_ADMIN,
                    $postdata
                ];
            } elseif ($response_data['error']['code'] == '2635') {
                //the Facebook API version is deprecated

                Errors::unexpected('FacebookAudience - Facebook API version deprecated', [
                    '!response' => $response_data,
                ]);

                return [false, ToolFacebookAudience::ERROR_DEPRECATED_API, $postdata];
            } else {
                //possible other errors hardly documented on facebook
                //@see: https://developers.facebook.com/docs/marketing-api/error-reference
                //TODO: watch the logs

                watchdog('FacebookAudience', 'FacebookAudience - Add Subscriber: Unknow Facebook API error', [
                    '!response' => (array)$response,
                ], WATCHDOG_WARNING);

                klicktipp_log_umlaut_failure(
                    ToolFacebookAudience::WARNING_UMLAUT_MESSAGE,
                    $Subscription['ContactInfo'],
                    $url,
                    $postdata
                );

                //better do not notify the user
                return [false, ToolFacebookAudience::ERROR_UNKNOWN_ERROR_CODE, $postdata];
            }
        }

        //no errors so far, check for success from facebook

        //we only send one subscriber at a time, so facebook will return num_received = 1
        //if the data is mal-formed num_invalid_entries will be also 1, otherwise 0
        $success = $response_data['num_received'] - $response_data['num_invalid_entries'];

        if ($success != 1) {
            //the data was mal-formed, facebook should send us the data back

            watchdog('FacebookAudience', 'FacebookAudience - Add Subscriber: Invalid subscriber data', [
                '!invalid_entry_samples' => $response_data->invalid_entry_samples,
            ], WATCHDOG_WARNING);

            klicktipp_log_umlaut_failure(
                ToolFacebookAudience::WARNING_UMLAUT_MESSAGE,
                $Subscription['ContactInfo'],
                $url,
                $postdata
            );

            Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED, [
                'AudienceID' => $this->DataFlat['ToolID'],
                'Reason' => $response->status_message,
                'EventType' => ToolFacebookAudience::EVENT_TYPE_ADD,
                'ReferenceID' => $ReferenceID
            ]);

            return [false, ToolFacebookAudience::ERROR_INVALID_DATA, $postdata];
        }

        //tag subscriber with smart tag
        Subscribers::TagSubscriber($UserID, $SubscriberID, $this->DataFlat['SmartTagID'], $ReferenceID);

        return [true, ToolFacebookAudience::ERROR_NONE, $postdata];
    }

    /**
     * Remove a subscriber from the facebook audience
     *
     * @see: https://developers.facebook.com/docs/marketing-api/audiences-api
     *
     * @param $SubscriberID
     * @param $ReferenceID
     *
     * @return array (boolean success, translatable error code)
     */
    public function RemoveSubscriber($SubscriberID, $ReferenceID)
    {
        $UserID = $this->DataFlat['RelOwnerUserID'];
        $AudienceID = $this->DataFlat['AudienceID'];

        //get the access token of the facebook account that this audience was connected with
        $AccessToken = $this->GetValidToken();

        if (!$AccessToken[0]) {
            return $AccessToken; //array(FALSE, ERROR_CODE)
        }

        $AccessToken = $AccessToken[1];

        // --- get subscriber data
        $data = array();
        $schema = array('EMAIL_SHA256'); //the schema for DELETE is only 'EMAIL_SHA256'

        $Subscription = Subscription::RetrieveSubscriptionByIDAndReference(
            $UserID,
            $SubscriberID,
            Subscription::SUBSCRIPTIONTYPE_EMAIL,
            $ReferenceID
        );

        if ($Subscription) {
            if (empty($Subscription['ContactInfo'])) {
                Subscribers::WriteHistory(
                    $UserID,
                    $SubscriberID,
                    Subscribers::HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED,
                    array(
                        'AudienceID' => $this->DataFlat['ToolID'],
                        'Reason' => ToolFacebookAudience::ERROR_NO_EMAIL_ADDRESS,
                        'EventType' => ToolFacebookAudience::EVENT_TYPE_REMOVE,
                        'ReferenceID' => $ReferenceID
                    )
                );

                return array(false, ToolFacebookAudience::ERROR_NO_EMAIL_ADDRESS);
            }

            //only the email is needed
            //facebook wants the data hashed
            //@see: https://developers.facebook.com/docs/marketing-api/audiences-api
            $data[] = array(hash("sha256", Subscribers::DepunycodeEmailAddress($Subscription['ContactInfo'])));
        }

        // prepare api post

        if (empty($data)) {
            return array(false, ToolFacebookAudience::ERROR_NO_SUBSCRIBER);
        }

        Libraries::include('guzzle.inc');

        //NOTE: on the live system this variable is never set, there is no settings dialog
        //      for simpletest we can specify a dummy callback that returns expected results
        //      @see: klicktipp_httptest_callback() in callbacks.inc
        $version = Settings::get('klicktipp_facebook_audience_api_version');
        $url = variable_get('simpletest-facebook-graph-api', "https://graph.facebook.com/$version/$AudienceID/users");

        $postdata = array(
            'payload' => json_encode(array(
                'schema' => $schema,
                'data' => $data
            )),
            'access_token' => $AccessToken,
        );

        $response = guzzle_http_request($url, array(
            'timeout' => 10.0,
            'method' => 'POST',
            'data' => $postdata,
        ));

        $response_data = (empty($response->data)) ? array() : json_decode($response->data, true);

        if (!empty($response->error)) {
            //we got a http response error

            //check for timeouts
            $timeouts = array(HTTP_REQUEST_TIMEOUT, 408, 504); //-1, Request Time-out, Gateway Time-out
            if (in_array($response->code, $timeouts)) {
                return array(false, ToolFacebookAudience::ERROR_REQUEST_TIMEOUT);
            }

            if (empty($response_data['error'])) {
                //no error in the response data (from facebook), nothing we can catch

                Subscribers::WriteHistory(
                    $UserID,
                    $SubscriberID,
                    Subscribers::HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED,
                    array(
                        'AudienceID' => $this->DataFlat['ToolID'],
                        'Reason' => ToolFacebookAudience::ERROR_INVALID_RESPONSE,
                        'EventType' => ToolFacebookAudience::EVENT_TYPE_REMOVE,
                        'ReferenceID' => $ReferenceID
                    )
                );

                watchdog('FacebookAudience', 'FacebookAudience - Remove Subscriber: Guzzle error', array(
                    '!response' => (array)$response,
                ), WATCHDOG_WARNING);

                klicktipp_log_umlaut_failure(
                    ToolFacebookAudience::WARNING_UMLAUT_MESSAGE,
                    $Subscription['ContactInfo'],
                    $url,
                    $postdata
                );

                return array(false, ToolFacebookAudience::ERROR_INVALID_RESPONSE, $postdata);
            }
        }

        //check for facebook error no matter what the http response code is
        //Note: facebook could send a 200 or a 400 (Bad Request) with it's own error code in the response data
        if (!empty($response_data['error'])) {
            //we have an error from facebook in the response data

            if ($response_data['error']['code'] == '190') {
                //the token expired for another reason then our expiration date
                $this->InvalidateToken();

                return [false, ToolFacebookAudience::ERROR_ACCESS_EXPIRED, $postdata];
            } elseif ($response_data['error']['code'] == '17') {
                //we reached the rate limit, suspend API calls for 5 minutes and re-queue items during that time
                $this->ThrottleApiCalls();

                return [false, ToolFacebookAudience::ERROR_RATE_LIMIT_REACHED, $postdata];
            } elseif ($response_data['error']['code'] == '272') {
                return [
                    false,
                    ToolFacebookAudience::ERROR_USER_IS_NOT_APP_ADMIN,
                    $postdata
                ];
            } elseif ($response_data['error']['code'] == '2635') {
                //the Facebook API version is deprecated

                Errors::unexpected('FacebookAudience - Facebook API version deprecated', [
                    '!response' => $response_data,
                ]);

                return [false, ToolFacebookAudience::ERROR_DEPRECATED_API, $postdata];
            } else {
                //possible other errors hardly documented on facebook
                //@see: https://developers.facebook.com/docs/marketing-api/error-reference
                //TODO: watch the logs

                watchdog('FacebookAudience', 'FacebookAudience - Remove Subscriber: Unknow Facebook API error', [
                    '!response' => (array)$response,
                ], WATCHDOG_WARNING);

                klicktipp_log_umlaut_failure(
                    ToolFacebookAudience::WARNING_UMLAUT_MESSAGE,
                    $Subscription['ContactInfo'],
                    $url,
                    $postdata
                );

                //better do not notify the user
                return [false, ToolFacebookAudience::ERROR_UNKNOWN_ERROR_CODE, $postdata];
            }
        }

        //untag subscriber with smart tag
        Subscribers::UntagSubscriber($UserID, $SubscriberID, $ReferenceID, $this->DataFlat['SmartTagID']);

        return [true, ToolFacebookAudience::ERROR_NONE, $postdata];
    }

    public function RemoveSubscriberByEmailAddress($EmailAddress)
    {
        $AudienceID = $this->DataFlat['AudienceID'];

        if (empty($EmailAddress)) {
            return array(false, ToolFacebookAudience::ERROR_NO_SUBSCRIBER);
        }

        //get the access token of the facebook account that this audience was connected with
        $AccessToken = $this->GetValidToken();

        if (!$AccessToken[0]) {
            return $AccessToken; //array(FALSE, ERROR_CODE)
        }

        $AccessToken = $AccessToken[1];

        // prepare api post
        $data = array(array(hash("sha256", Subscribers::DepunycodeEmailAddress($EmailAddress))));
        $schema = array('EMAIL_SHA256'); //the schema for DELETE is only 'EMAIL_SHA256'

        Libraries::include('guzzle.inc');

        //NOTE: on the live system this variable is never set, there is no settings dialog
        //      for simpletest we can specify a dummy callback that returns expected results
        //      @see: klicktipp_httptest_callback() in callbacks.inc
        $version = Settings::get('klicktipp_facebook_audience_api_version');
        $url = variable_get('simpletest-facebook-graph-api', "https://graph.facebook.com/$version/$AudienceID/users");

        $postdata = array(
            'payload' => json_encode(array(
                'schema' => $schema,
                'data' => $data
            )),
            'access_token' => $AccessToken,
        );

        $response = guzzle_http_request($url, array(
            'timeout' => 10.0,
            'method' => 'POST',
            'data' => $postdata,
        ));

        $response_data = (empty($response->data)) ? array() : json_decode($response->data, true);

        if (!empty($response->error)) {
            //we got a http response error

            //check for timeouts
            $timeouts = array(HTTP_REQUEST_TIMEOUT, 408, 504); //-1, Request Time-out, Gateway Time-out
            if (in_array($response->code, $timeouts)) {
                return array(false, ToolFacebookAudience::ERROR_REQUEST_TIMEOUT);
            }

            if (empty($response_data['error'])) {
                //no error in the response data (from facebook), nothing we can catch

                klicktipp_log_umlaut_failure(
                    ToolFacebookAudience::WARNING_UMLAUT_MESSAGE,
                    $EmailAddress,
                    $url,
                    $postdata
                );

                return array(false, ToolFacebookAudience::ERROR_INVALID_RESPONSE, $postdata);
            }
        }

        //check for facebook error no matter what the http response code is
        //Note: facebook could send a 200 or a 400 (Bad Request) with it's own error code in the response data
        if (!empty($response_data['error'])) {
            //we have an error from facebook in the response data

            if ($response_data['error']['code'] == '190') {
                //the token expired for another reason then our expiration date
                $this->InvalidateToken();

                return array(false, ToolFacebookAudience::ERROR_ACCESS_EXPIRED);
            } elseif ($response_data['error']['code'] == '17') {
                //we reached the rate limit, suspend API calls for 5 minutes and re-queue items during that time
                return array(false, ToolFacebookAudience::ERROR_RATE_LIMIT_REACHED);
            } elseif ($response_data['error']['code'] == '272') {
                return array(false, ToolFacebookAudience::ERROR_USER_IS_NOT_APP_ADMIN);
            } elseif ($response_data['error']['code'] == '2635') {
                //the Facebook API version is deprecated

                Errors::unexpected('FacebookAudience - Facebook API version deprecated', array(
                    '!response' => $response_data,
                ));

                return array(false, ToolFacebookAudience::ERROR_DEPRECATED_API);
            } else {
                //possible other errors hardly documented on facebook
                //TODO: watch the logs

                watchdog(
                    'FacebookAudience',
                    'FacebookAudience - Remove Subscriber by email address: Unknow Facebook API error',
                    array(
                        '!response' => (array)$response,
                    ),
                    WATCHDOG_WARNING
                );

                klicktipp_log_umlaut_failure(
                    ToolFacebookAudience::WARNING_UMLAUT_MESSAGE,
                    $EmailAddress,
                    $url,
                    $postdata
                );

                //better do not notify the user
                return array(false, ToolFacebookAudience::ERROR_UNKNOWN_ERROR_CODE);
            }
        }

        return array(true, ToolFacebookAudience::ERROR_NONE);
    }

    /**
     *
     * Create a job for facebook audience events add and remove (triggered by automation)
     */
    /**
     * @param $UserID
     * @param $ToolID
     * @param $SubscriberID
     * @param $TransactionalData : ArrayCampaign, CurrentState
     * @param int $EventType
     */
    public static function TriggerProcessflowActionEvent(
        $UserID,
        $ToolID,
        $SubscriberID,
        $TransactionalData,
        $EventType = ToolFacebookAudience::EVENT_TYPE_ADD
    ) {
        $FacebookAudienceEvent = array(
            'RelOwnerUserID' => $UserID,
            'RelToolID' => $ToolID,
            'EventType' => $EventType,
            'SubscriberID' => $SubscriberID,
            'TransactionalData' => $TransactionalData,
        );

        //Note: use $ToolID as Sequence number in case 2 campaigns try to update an audience at the same time
        SubscriberQueue::CreateQueueJob(
            $UserID,
            $SubscriberID,
            SubscriberQueue::JOBTYPE_FACEBOOK_AUDIENCE,
            $FacebookAudienceEvent,
            0,
            $ToolID
        );
    }

    /**
     * Worker for ProcessFlow actions Facebook Audience add and remove
     * @param $data
     */
    public static function ProcessflowActionEventQueueWorker($data)
    {
        // error return argument only for tests
        $returnValue = [
            "success" => true,
            "message" => ""
        ];

        if (empty($data)) {
            $returnValue = [
                "success" => false,
                "message" => "no data"
            ];
            return $returnValue;
        }

        $UserID = $data['RelOwnerUserID'];
        $ToolID = $data['RelToolID'];
        $SubscriberID = $data['SubscriberID'];
        $EventType = $data['EventType']; //add or remove subscriber

        //needed data from TransactionEmails::RunTransactional to call TransactionEmails::RunJobFinished(), TransactionEmails::RunJobFailed()
        $CampaignID = $data['TransactionalData']['RelAutoResponderID'];
        $ReferenceID = $data['TransactionalData']['ReferenceID'];

        //get the transactional queue entry
        $TransactionEmail = TransactionEmails::RetrieveTransactionByID(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $CampaignID
        );

        if (empty($TransactionEmail)) {
            $error = array(
                '!SubscriberID' => $SubscriberID,
                '!CampaignID' => $CampaignID,
                '!UserID' => $UserID,
                '!data' => $data,
            );
            Errors::unexpected(
                "ProcessflowActionEventQueueWorker: Transactional queue item not found for User.",
                $error
            );
            $returnValue = [
                "success" => false,
                "message" => "no entry"
            ];
            return $returnValue;
        }

        //get the campaign
        $ObjectCampaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);

        if (empty($ObjectCampaign)) {
            $error = array(
                '!CampaignID' => $CampaignID,
                '!UserID' => $UserID,
                '!data' => $data,
            );
            Errors::unexpected("ProcessflowActionEventQueueWorker: Campaign not found for User.", $error);
            TransactionEmails::SetStatusFailed(
                TRANSACTION_REASON_CAMPAIGN_NOT_FOUND,
                $CampaignID,
                $UserID,
                $SubscriberID,
                $ReferenceID,
                TransactionalQueue::TABLE_NAME,
                $TransactionEmail['StateID']
            );
            $returnValue = [
                "success" => false,
                "message" => "no campaign found"
            ];
            return $returnValue;
        }
        $ArrayCampaign = $ObjectCampaign->GetData();

        // get state
        $CurrentState = CampaignsProcessFlow::FindState($ArrayCampaign, $TransactionEmail['StateID']);
        if (empty($CurrentState)) {
            // state not found
            $error = array(
                '!StateID' => $TransactionEmail['StateID'],
                '!CampaignID' => $CampaignID,
                '!UserID' => $UserID,
                '!data' => $data,
            );
            Errors::unexpected(
                "ProcessflowActionEventQueueWorker: State not found in Campaign for User.",
                $error
            );
            TransactionEmails::RunFailed(
                $ArrayCampaign,
                $SubscriberID,
                $ReferenceID,
                $TransactionEmail['StateID'],
                TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE
            );
            $returnValue = [
                "success" => false,
                "message" => "no state found"
            ];
            return $returnValue;
        }

        /** @var ToolFacebookAudience $ObjectAudience */
        $ObjectAudience = ToolFacebookAudience::FromID($UserID, $ToolID);

        if ($ObjectAudience) {
            //check if the campaign is active
            if (!in_array($ArrayCampaign['CampaignStatusEnum'], Campaigns::$ArrayCampaignStatiSending)) {
                //not active, set to pending with current timestamp so the state/job will be started immediately after restart of campaign
                TransactionEmails::RunJobFailed($SubscriberID, $ReferenceID, $ArrayCampaign, time());
                $returnValue = [
                    "success" => false,
                    "message" => "campaign not active"
                ];
                return $returnValue;
            }

            //either add or remove the subscriber
            if ($EventType == ToolFacebookAudience::EVENT_TYPE_REMOVE) {
                $result = $ObjectAudience->RemoveSubscriber($SubscriberID, $ReferenceID);
            } else {
                $result = $ObjectAudience->AddSubscriber($SubscriberID, $ReferenceID);
            }

            if (
                $result[0] || in_array(
                    $result[1],
                    array(
                        ToolFacebookAudience::ERROR_NO_EMAIL_ADDRESS,
                        ToolFacebookAudience::ERROR_NO_SUBSCRIBER,
                        ToolFacebookAudience::ERROR_INVALID_DATA
                    )
                )
            ) {
                //facebook api call without an access violation or a reached rate limit OR insufficient subscriber data
                // => proceed with the next state
                TransactionEmails::RunJobFinshed($SubscriberID, $ReferenceID, $ArrayCampaign, $CurrentState['next']);
                $returnValue["message"] = "campaign active";
            } elseif (
                in_array(
                    $result[1],
                    array(ToolFacebookAudience::ERROR_RATE_LIMIT_REACHED, ToolFacebookAudience::ERROR_REQUEST_TIMEOUT)
                )
            ) {
                //the rate limt was reached or request timeout -> re-queue randomly within 5 to 30 minutes

                //Note: rate limt was reached
                //A call that returns error code (17) (rate limit reached) will be blocked for a minute
                //During this time the max score will decay, being dropped to 0 after a maximum of 5 minutes.
                //@see: https://developers.facebook.com/docs/marketing-api/api-rate-limiting
                //also @see: $ObjectAudience->ThrottleApiCalls()

                $minutes = mt_rand(5, 30);
                $TimeToSend = strtotime("+$minutes minutes");
                TransactionEmails::RunJobFailed($SubscriberID, $ReferenceID, $ArrayCampaign, $TimeToSend);
                $returnValue = [
                    "success" => false,
                    "message" => "time out"
                ];
            } else {
                //the token expired for another reason then our expiration date (the token has already been invalidated by Add/RemoveSubscriber()),
                //the Facebook API version is deprecated: ERROR_DEPRECATED_API
                //invalid/unknown guzzle response: ERROR_INVALID_RESPONSE
                //unknow facebook response: ERROR_UNKNOWN_ERROR_CODE
                //facebook app misconfiguration: ERROR_USER_IS_NOT_APP_ADMIN

                if (empty($CurrentState['ignoreExpiredAccess'])) {
                    //wait until the user renewed his access token or updated the API version (try again in 1 day)
                    TransactionEmails::RunJobFailed($SubscriberID, $ReferenceID, $ArrayCampaign);
                    $returnValue = [
                        "success" => false,
                        "message" => "without ignoreExpiredAccess"
                    ];
                } else {
                    //ignore the expired access and proceed with the next state
                    TransactionEmails::RunJobFinshed(
                        $SubscriberID,
                        $ReferenceID,
                        $ArrayCampaign,
                        $CurrentState['next']
                    );

                    if ($result[1] == ToolFacebookAudience::ERROR_ACCESS_EXPIRED) {
                        //write a history entry only if the access expired

                        Subscribers::WriteHistory(
                            $UserID,
                            $SubscriberID,
                            Subscribers::HISTORY_FACEBOOK_AUDIENCE_EXPIRED_ACCESS,
                            array(
                                'AudienceID' => $ObjectAudience->GetData('ToolID'),
                                'AudienceName' => $ObjectAudience->GetData('Name'),
                                'FacebookAccountName' => $ObjectAudience->GetData('FacebookAccountName'),
                                'EventType' => $EventType,
                                'ReferenceID' => $ReferenceID
                            )
                        );
                    }

                    $returnValue["message"] = "with ignoreExpiredAccess";
                }
            }
        } else {
            //tool audience doesn't exit anymore, ignore and proceed with the next state
            TransactionEmails::RunJobFinshed($SubscriberID, $ReferenceID, $ArrayCampaign, $CurrentState['next']);
            $returnValue = [
                "success" => false,
                "message" => "tool missing"
            ];
            return $returnValue;
        }

        //trigger the automations for the subscriber
        TransactionEmails::RegisterAutomations($UserID, $SubscriberID, $ReferenceID);

        $returnValue["message"] .= " and registered";

        return $returnValue;
    }

    public static function PrivacyTaskQueueWorker($data)
    {
        // error return argument only for tests
        $returnValue = [
            "success" => true,
            "message" => ""
        ];

        if (empty($data)) {
            return;
        }

        $UserID = $data['RelOwnerUserID'];
        $SubscriberID = $data['RelSubscriberID'];
        $ToolIDs = $data['RelToolIDs'];
        $EmailAddress = $data['EmailAddress'];
        $MaxRuntime = (empty($data['MaxRuntime'])) ? SubscriberQueue::DEFAULT_MAX_RUNTIME : $data['MaxRuntime'];
        $ReferenceID = $data['ReferenceID'];

        $starttime = time();
        $NotFinished = $ToolIDs;

        foreach ($ToolIDs as $index => $ToolID) {
            //check runtime of script, requeue if needed
            if (($starttime + $MaxRuntime) < time()) {
                //max runtime for this job has been exceeded

                if (!empty($NotFinished)) {
                    //not all items were finished, create a new job with the remaining items
                    //TODO looks like we should do this using ProcessLog (all build in)
                    $Item = array(
                        'RelOwnerUserID' => $UserID,
                        'RelSubscriberID' => $SubscriberID,
                        'RelToolIDs' => $NotFinished,
                        'EmailAddress' => $EmailAddress
                    );
                    SubscriberQueue::CreateQueueJob(
                        $UserID,
                        $SubscriberID,
                        SubscriberQueue::JOBTYPE_FACEBOOK_AUDIENCE_PRIVACY_REMOVE,
                        $Item,
                        $ReferenceID
                    );
                    $returnValue = [
                        "success" => false,
                        "message" => "requeue due to time restrictions"
                    ];
                    return $returnValue;
                }
            }

            $ObjectAudience = ToolFacebookAudience::FromID($UserID, $ToolID);

            if ($ObjectAudience) {
                //remove the email address from the audience
                $result = $ObjectAudience->RemoveSubscriberByEmailAddress($EmailAddress);

                if (!$result[0]) {
                    //an error occured, update the privacy task for this email address and audience with the error message

                    //retrieve previous error
                    $query = "SELECT HistoryData FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND "
                        . "RelSubscriberID = :RelSubscriberID AND HistoryType = :HistoryType AND RelIndexed = :ToolID";
                    $Data = db_query($query, array(
                        ':RelOwnerUserID' => $UserID,
                        ':RelSubscriberID' => $SubscriberID,
                        ':HistoryType' => Subscribers::HISTORY_REQUEST_DELETE_FBAUDIENCE_ERROR,
                        ':ToolID' => $ToolID
                    ))->fetchField();

                    if (empty($Data)) {
                        //no previous error -> create a new history entry
                        $Data = array(
                            'email' => $EmailAddress,
                            'attempts' => 1,
                            'error' => $result[1],
                            'ReferenceID' => $ReferenceID
                        );
                        Subscribers::WriteHistory(
                            $UserID,
                            $SubscriberID,
                            Subscribers::HISTORY_REQUEST_DELETE_FBAUDIENCE_ERROR,
                            $Data,
                            0,
                            $ToolID
                        );
                    } else {
                        //there have been precious attempts -> increase count (info for user)
                        $Data = unserialize((string)$Data);
                        $Data = array(
                            'email' => $EmailAddress,
                            'attempts' => $Data['attempts'] + 1,
                            'error' => $result[1]
                        );

                        $query = "UPDATE {subscriber_history} SET HistoryData = :HistoryData, HistoryDate = :HistoryDate " .
                            "WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND HistoryType = :HistoryType AND RelIndexed = :ToolID";
                        db_query($query, array(
                            ':RelOwnerUserID' => $UserID,
                            ':SubscriberID' => $SubscriberID,
                            ':HistoryType' => Subscribers::HISTORY_REQUEST_DELETE_FBAUDIENCE_ERROR,
                            ':ToolID' => $ToolID,
                            ':HistoryData' => serialize($Data),
                            ':HistoryDate' => time(),
                        ));
                    }
                } else {
                    //email address was successfully removed from audience, remove possible error from history

                    $query = "DELETE FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND HistoryType = :HistoryType AND RelIndexed = :ToolID";
                    db_query($query, array(
                        ':RelOwnerUserID' => $UserID,
                        ':SubscriberID' => $SubscriberID,
                        ':HistoryType' => Subscribers::HISTORY_REQUEST_DELETE_FBAUDIENCE_ERROR,
                        ':ToolID' => $ToolID
                    ));
                }
            }

            unset($NotFinished[$index]);
        }

        return $returnValue;
    }

    public function GetValidToken()
    {
        $UserID = $this->DataFlat['RelOwnerUserID'];

        $AccessToken = VarFacebookAudience::GetVariable($UserID, array());

        if (empty($AccessToken['Token']) || empty($AccessToken['ExpiresAt']) || $AccessToken['ExpiresAt'] < time()) {
            return array(false, ToolFacebookAudience::ERROR_ACCESS_EXPIRED);
        }

        if (!empty($AccessToken['ThrottleUntil']) && $AccessToken['ThrottleUntil'] > time()) {
            return array(false, ToolFacebookAudience::ERROR_RATE_LIMIT_REACHED);
        }

        return array(true, $AccessToken['Token']);
    }

    public function InvalidateToken()
    {
        $UserID = $this->DataFlat['RelOwnerUserID'];

        $AccessToken = VarFacebookAudience::GetVariable($UserID, array());
        $AccessToken['ExpiresAt'] = 0;
        $AccessToken['Token'] = '';
        VarFacebookAudience::SetVariable($UserID, $AccessToken);
    }

    public function ThrottleApiCalls()
    {
        $UserID = $this->DataFlat['RelOwnerUserID'];

        $AccessToken = VarFacebookAudience::GetVariable($UserID, array());

        //A call that returns error code (17) (rate limit reached) will be blocked for a minute
        //During this time the max score will decay, being dropped to 0 after a maximum of 5 minutes.
        //@see: https://developers.facebook.com/docs/marketing-api/api-rate-limiting
        //set ThrottleUntil to +5 minutes so all subsequent calls during that time will be spread out over the next 30 minutes
        //@see: $this->ProcessflowActionEventQueueWorker();
        $AccessToken['ThrottleUntil'] = strtotime('+5 minute');
        VarFacebookAudience::SetVariable($UserID, $AccessToken);
    }

    public function GetEditURL($ToolID = 0)
    {
        return APP_URL . "contacts/{$this->DataFlat['RelOwnerUserID']}/facebook-audience";
    }

    public static function GetAddURL($UserID, $data = '')
    {
        return APP_URL . "contacts/$UserID/facebook-audience";
    }

    public static function DeleteUserAudiences($UserID)
    {
        $result = db_query(
            "SELECT * FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID AND ToolType = :ToolType",
            array(
                ':RelOwnerUserID' => $UserID,
                ':ToolType' => MarketingTools::TOOL_TYPE_FACEBOOK_AUDIENCE,
            )
        );

        while ($DBArray = kt_fetch_array($result)) {
            static::DeleteDB($DBArray);
        }
    }
}

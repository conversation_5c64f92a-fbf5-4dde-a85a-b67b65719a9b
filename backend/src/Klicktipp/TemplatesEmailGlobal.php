<?php

namespace App\Klicktipp;

class TemplatesEmailGlobal
{
    public static $APIPath = 'kt-templates-email-global';

    public static function klicktippapi_resource_definition()
    {
        return array(
            static::$APIPath => array(
                // GET /api/<enititypath>
                'index' => array(
                    'help' => 'Provides a list of all global email templates',
                    'callback' => array(get_called_class(), 'klicktippapi_index'),
                    'access callback' => 'services_access_menu',
                ),
            )
        );

        return $resource;
    }

    public static function klicktippapi_index()
    {
        $account = DatabaseTableCRUD::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        return self::browse();
    }

    public static function browse()
    {
        return array(
            'source' => 'https://' . KLICKTIPP_MAILCDN_DOMAIN . '/email_templates/',
            'templates' => array_values(TemplatesEmailGlobal::index()),
            'tags' => TemplatesEmailMetaData::tagIndex(),
            'usages' => TemplatesEmailMetaData::usageIndex(),
            'industries' => TemplatesEmailMetaData::industryIndex()
        );
    }

    public static function index()
    {
        return [
            "klicktipp-basic" => [
                "id" => "klicktipp-basic",
                "name" => t(/*#ee-template-name*/ "templatedetail::Basic template"),
                "tags" => [],
                "usage" => ["newsletter"],
                "industries" => ["marketing_and_design"]
            ],
            "klicktipp-plain" => [
                "id" => "klicktipp-plain",
                "name" => t(/*#ee-template-name*/ "templatedetail::Text-Template"),
                "tags" => [],
                "usage" => ["personal-note"],
                "industries" => ["business-services"]
            ],
            "online-fashion-store" => [
                "id" => "online-fashion-store",
                "name" => t(/*#ee-template-name*/ "eetmplname::online-fashion-store"),
                "tags" => [
                    "light",
                    "white",
                    "red",
                    "sans serif",
                    "three-columns",
                    "sell"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "mens-fashion" => [
                "id" => "mens-fashion",
                "name" => t(/*#ee-template-name*/ "eetmplname::mens-fashion"),
                "tags" => [
                    "light",
                    "blue",
                    "two-columns",
                    "sans serif",
                    "light blue",
                    "sell"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "diamond-events" => [
                "id" => "diamond-events",
                "name" => t(/*#ee-template-name*/ "eetmplname::diamond-events"),
                "tags" => [
                    "light",
                    "white",
                    "blue",
                    "serif",
                    "one-column",
                    "promote",
                    "dynamic countdown"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "big-deals" => [
                "id" => "big-deals",
                "name" => t(/*#ee-template-name*/ "eetmplname::big-deals"),
                "tags" => [
                    "black",
                    "blue",
                    "dark",
                    "animated",
                    "one-column",
                    "promote",
                    "sans serif"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "megaphone" => [
                "id" => "megaphone",
                "name" => t(/*#ee-template-name*/ "eetmplname::megaphone"),
                "tags" => [
                    "light",
                    "white",
                    "red",
                    "animated",
                    "one-column",
                    "promote",
                    "sans serif"
                ],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "reporter" => [
                "id" => "reporter",
                "name" => t(/*#ee-template-name*/ "eetmplname::reporter"),
                "tags" => [
                    "light",
                    "white",
                    "two-columns",
                    "promote",
                    "sans serif",
                    "light blue"
                ],
                "usage" => ["notification"],
                "industries" => ["computer-internet"]
            ],
            "infinity" => [
                "id" => "infinity",
                "name" => t(/*#ee-template-name*/ "eetmplname::infinity"),
                "tags" => [
                    "light",
                    "sans serif",
                    "light blue",
                    "light grey",
                    "three-columns",
                    "sell"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "top-street-wear" => [
                "id" => "top-street-wear",
                "name" => t(/*#ee-template-name*/ "eetmplname::top-street-wear"),
                "tags" => [
                    "orange",
                    "green",
                    "two-columns",
                    "sans serif",
                    "sell"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "eventus" => [
                "id" => "eventus",
                "name" => t(/*#ee-template-name*/ "eetmplname::eventus"),
                "tags" => [
                    "black",
                    "dark",
                    "yellow",
                    "two-columns",
                    "serif",
                    "promote"
                ],
                "usage" => ["events"],
                "industries" => ["fashion"]
            ],
            "mad-hatter" => [
                "id" => "mad-hatter",
                "name" => t(/*#ee-template-name*/ "eetmplname::mad-hatter"),
                "tags" => [
                    "purple",
                    "green",
                    "dark",
                    "two-columns",
                    "sans serif",
                    "sell"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "landoo" => [
                "id" => "landoo",
                "name" => t(/*#ee-template-name*/ "eetmplname::landoo"),
                "tags" => [
                    "grey",
                    "light",
                    "green",
                    "one-column",
                    "sans serif",
                    "communicate"
                ],
                "usage" => ["notification"],
                "industries" => ["computer-internet"]
            ],
            "freshbooks" => [
                "id" => "freshbooks",
                "name" => t(/*#ee-template-name*/ "eetmplname::freshbooks"),
                "tags" => [
                    "black",
                    "green",
                    "dark",
                    "two-columns",
                    "promote",
                    "sans serif"
                ],
                "usage" => ["product-launch"],
                "industries" => ["business-services"]
            ],
            "mobiland" => [
                "id" => "mobiland",
                "name" => t(/*#ee-template-name*/ "eetmplname::mobiland"),
                "tags" => [
                    "light",
                    "orange",
                    "blue",
                    "promote",
                    "sans serif",
                    "three-columns"
                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet"]
            ],
            "product-launch" => [
                "id" => "product-launch",
                "name" => t(/*#ee-template-name*/ "eetmplname::product-launch"),
                "tags" => [
                    "grey",
                    "light",
                    "one-column",
                    "sans serif",
                    "light blue",
                    "sell"
                ],
                "usage" => ["product-launch"],
                "industries" => ["travel-leisure"]
            ],
            "account-activation" => [
                "id" => "account-activation",
                "name" => t(/*#ee-template-name*/ "eetmplname::account-activation"),
                "tags" => [
                    "grey",
                    "light",
                    "yellow",
                    "one-column",
                    "sans serif",
                    "communicate"
                ],
                "usage" => ["transactional"],
                "industries" => ["computer-internet"]
            ],
            "order-confirmation" => [
                "id" => "order-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::order-confirmation"),
                "tags" => [
                    "black",
                    "light",
                    "white",
                    "one-column",
                    "sans serif",
                    "communicate"
                ],
                "usage" => ["transactional"],
                "industries" => ["business-services"]
            ],
            "minimalistic" => [
                "id" => "minimalistic",
                "name" => t(/*#ee-template-name*/ "eetmplname::minimalistic"),
                "tags" => [
                    "black",
                    "light",
                    "white",
                    "one-column",
                    "sans serif",
                    "communicate"
                ],
                "usage" => ["personal-note"],
                "industries" => ["business-services"]
            ],
            "registration-completed" => [
                "id" => "registration-completed",
                "name" => t(/*#ee-template-name*/ "eetmplname::registration-completed"),
                "tags" => [
                    "light",
                    "white",
                    "one-column",
                    "sans serif",
                    "communicate",
                    "light green"
                ],
                "usage" => ["notification"],
                "industries" => ["computer-internet"]
            ],
            "wireframe-grid" => [
                "id" => "wireframe-grid",
                "name" => t(/*#ee-template-name*/ "eetmplname::wireframe-grid"),
                "tags" => [
                    "grey",
                    "light",
                    "white",
                    "two-columns",
                    "promote",
                    "sans serif"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["business-services"]
            ],
            "wireframe-newsletter" => [
                "id" => "wireframe-newsletter",
                "name" => t(/*#ee-template-name*/ "eetmplname::wireframe-newsletter"),
                "tags" => [
                    "grey",
                    "light",
                    "white",
                    "two-columns",
                    "sans serif",
                    "communicate"
                ],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "wireframe-article" => [
                "id" => "wireframe-article",
                "name" => t(/*#ee-template-name*/ "eetmplname::wireframe-article"),
                "tags" => [
                    "grey",
                    "light",
                    "white",
                    "two-columns",
                    "sans serif",
                    "communicate"
                ],
                "usage" => ["newsletter"],
                "industries" => ["computer-internet"]
            ],
            "wireframe-launch" => [
                "id" => "wireframe-launch",
                "name" => t(/*#ee-template-name*/ "eetmplname::wireframe-launch"),
                "tags" => [
                    "grey",
                    "light",
                    "white",
                    "one-column",
                    "promote",
                    "sans serif"
                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet"]
            ],
            "hackister-simple" => [
                "id" => "hackister-simple",
                "name" => t(/*#ee-template-name*/ "eetmplname::hackister-simple"),
                "tags" => [
                    "orange",
                    "dark",
                    "one-column",
                    "promote",
                    "sans serif",
                    "dark grey"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "hackister-promo" => [
                "id" => "hackister-promo",
                "name" => t(/*#ee-template-name*/ "eetmplname::hackister-promo"),
                "tags" => [
                    "orange",
                    "dark",
                    "one-column",
                    "promote",
                    "sans serif",
                    "dark grey"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "hackister-newsletter" => [
                "id" => "hackister-newsletter",
                "name" => t(/*#ee-template-name*/ "eetmplname::hackister-newsletter"),
                "tags" => [
                    "orange",
                    "dark",
                    "two-columns",
                    "promote",
                    "sans serif",
                    "dark grey"
                ],
                "usage" => ["newsletter"],
                "industries" => ["computer-internet"]
            ],
            "hackister-grey" => [
                "id" => "hackister-grey",
                "name" => t(/*#ee-template-name*/ "eetmplname::hackister-grey"),
                "tags" => [
                    "orange",
                    "dark",
                    "two-columns",
                    "sans serif",
                    "sell",
                    "dark grey"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "countdown-to-elegance" => [
                "id" => "countdown-to-elegance",
                "name" => t(/*#ee-template-name*/ "eetmplname::countdown-to-elegance"),
                "tags" => [
                    "light",
                    "white",
                    "one-column",
                    "promote",
                    "sans serif",
                    "dynamic countdown",
                    "light green"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "summer-sale-countdown" => [
                "id" => "summer-sale-countdown",
                "name" => t(/*#ee-template-name*/ "eetmplname::summer-sale-countdown"),
                "tags" => [
                    "light",
                    "orange",
                    "one-column",
                    "sans serif",
                    "light blue",
                    "sell",
                    "dynamic countdown"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "birthday-greetings" => [
                "id" => "birthday-greetings",
                "name" => t(/*#ee-template-name*/ "eetmplname::birthday-greetings"),
                "tags" => [
                    "light",
                    "white",
                    "green",
                    "one-column",
                    "sans serif",
                    "sell"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "happy-birthday" => [
                "id" => "happy-birthday",
                "name" => t(/*#ee-template-name*/ "eetmplname::happy-birthday"),
                "tags" => [
                    "light",
                    "white",
                    "one-column",
                    "sans serif",
                    "light blue",
                    "sell"
                ],
                "usage" => ["personal-note"],
                "industries" => ["food-beverage"]
            ],
            "thank-you" => [
                "id" => "thank-you",
                "name" => t(/*#ee-template-name*/ "eetmplname::thank-you"),
                "tags" => [
                    "light",
                    "white",
                    "animated",
                    "one-column",
                    "sans serif",
                    "light blue",
                    "communications"
                ],
                "usage" => ["notification"],
                "industries" => ["business-services"]
            ],
            "rose-photography" => [
                "id" => "rose-photography",
                "name" => t(/*#ee-template-name*/ "eetmplname::rose-photography"),
                "tags" => [
                    "light",
                    "white",
                    "red",
                    "serif",
                    "four-columns",
                    "portfolio"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services"]
            ],
            "purple-wine" => [
                "id" => "purple-wine",
                "name" => t(/*#ee-template-name*/ "eetmplname::purple-wine"),
                "tags" => [
                    "light",
                    "white",
                    "purple",
                    "two-columns",
                    "sans serif",
                    "sell"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["food-beverage"]
            ],
            "sportsware" => [
                "id" => "sportsware",
                "name" => t(/*#ee-template-name*/ "eetmplname::sportsware"),
                "tags" => [
                    "grey",
                    "light",
                    "orange",
                    "one-column",
                    "sans serif",
                    "sell"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "fashion-promo" => [
                "id" => "fashion-promo",
                "name" => t(/*#ee-template-name*/ "eetmplname::fashion-promo"),
                "tags" => [
                    "fashion",
                    "two-columns",
                    "clothes",
                    "promotion",
                    "ecommerce",
                    "offer",
                    "promo",
                    "retail",
                    "store",
                    "white",
                    "grey"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "travel-guide" => [
                "id" => "travel-guide",
                "name" => t(/*#ee-template-name*/ "eetmplname::travel-guide"),
                "tags" => [
                    "black",
                    "dark",
                    "yellow",
                    "two-columns",
                    "sans serif",
                    "communicate"
                ],
                "usage" => ["newsletter"],
                "industries" => ["travel-leisure"]
            ],
            "fashion-zine" => [
                "id" => "fashion-zine",
                "name" => t(/*#ee-template-name*/ "eetmplname::fashion-zine"),
                "tags" => [
                    "black",
                    "light",
                    "white",
                    "serif",
                    "four-columns",
                    "communicate"
                ],
                "usage" => ["newsletter"],
                "industries" => ["fashion"]
            ],
            "the-coffee-corner-news" => [
                "id" => "the-coffee-corner-news",
                "name" => t(/*#ee-template-name*/ "eetmplname::the-coffee-corner-news"),
                "tags" => [
                    "black",
                    "dark",
                    "two-columns",
                    "inform",
                    "sans serif",
                    "beige"
                ],
                "usage" => ["newsletter"],
                "industries" => ["food-beverage"]
            ],
            "nowapp-deal" => [
                "id" => "nowapp-deal",
                "name" => t(/*#ee-template-name*/ "eetmplname::nowapp-deal"),
                "tags" => [
                    "light",
                    "blue",
                    "one-column",
                    "discount",
                    "light blue",
                    "slab"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "nowapp-news" => [
                "id" => "nowapp-news",
                "name" => t(/*#ee-template-name*/ "eetmplname::nowapp-news"),
                "tags" => [
                    "light",
                    "blue",
                    "one-column",
                    "communicate",
                    "light blue",
                    "slab"
                ],
                "usage" => ["newsletter"],
                "industries" => ["computer-internet"]
            ],
            "nowapp" => [
                "id" => "nowapp",
                "name" => t(/*#ee-template-name*/ "eetmplname::nowapp"),
                "tags" => [
                    "light",
                    "blue",
                    "one-column",
                    "promote",
                    "light blue",
                    "slab"
                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet"]
            ],
            "nowapp-keynote" => [
                "id" => "nowapp-keynote",
                "name" => t(/*#ee-template-name*/ "eetmplname::nowapp-keynote"),
                "tags" => [
                    "light",
                    "blue",
                    "one-column",
                    "inform",
                    "light blue",
                    "slab"
                ],
                "usage" => ["events"],
                "industries" => ["education"]
            ],
            "the-coffee-corner-is-open" => [
                "id" => "the-coffee-corner-is-open",
                "name" => t(/*#ee-template-name*/ "eetmplname::the-coffee-corner-is-open"),
                "tags" => [
                    "black",
                    "dark",
                    "inform",
                    "sans serif",
                    "beige",
                    "three-columns"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["food-beverage"]
            ],
            "the-coffee-corner-deal" => [
                "id" => "the-coffee-corner-deal",
                "name" => t(/*#ee-template-name*/ "eetmplname::the-coffee-corner-deal"),
                "tags" => [
                    "black",
                    "dark",
                    "sans serif",
                    "discount",
                    "beige",
                    "three-columns"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["food-beverage"]
            ],
            "the-coffee-corner-shop" => [
                "id" => "the-coffee-corner-shop",
                "name" => t(/*#ee-template-name*/ "eetmplname::the-coffee-corner-shop"),
                "tags" => [
                    "black",
                    "dark",
                    "two-columns",
                    "promote",
                    "sans serif",
                    "beige"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["food-beverage"]
            ],
            "the-coffee-corner-event" => [
                "id" => "the-coffee-corner-event",
                "name" => t(/*#ee-template-name*/ "eetmplname::the-coffee-corner-event"),
                "tags" => [
                    "black",
                    "dark",
                    "two-columns",
                    "sans serif",
                    "beige",
                    "announce"
                ],
                "usage" => ["events"],
                "industries" => ["food-beverage"]
            ],
            "green-events" => [
                "id" => "green-events",
                "name" => t(/*#ee-template-name*/ "eetmplname::green-events"),
                "tags" => [
                    "black",
                    "light",
                    "green",
                    "two-columns",
                    "inform",
                    "sans serif"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "green-events-next" => [
                "id" => "green-events-next",
                "name" => t(/*#ee-template-name*/ "eetmplname::green-events-next"),
                "tags" => [
                    "black",
                    "light",
                    "green",
                    "two-columns",
                    "inform",
                    "sans serif"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "green-events-news" => [
                "id" => "green-events-news",
                "name" => t(/*#ee-template-name*/ "eetmplname::green-events-news"),
                "tags" => [
                    "black",
                    "light",
                    "green",
                    "two-columns",
                    "inform",
                    "sans serif"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "green-events-coupon" => [
                "id" => "green-events-coupon",
                "name" => t(/*#ee-template-name*/ "eetmplname::green-events-coupon"),
                "tags" => [
                    "black",
                    "light",
                    "green",
                    "two-columns",
                    "sans serif",
                    "coupon"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "elegant-discount" => [
                "id" => "elegant-discount",
                "name" => t(/*#ee-template-name*/ "eetmplname::elegant-discount"),
                "tags" => [
                    "black",
                    "light",
                    "white",
                    "serif",
                    "four-columns",
                    "discount"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["transportation-storage"]
            ],
            "light-order-tracking" => [
                "id" => "light-order-tracking",
                "name" => t(/*#ee-template-name*/ "eetmplname::light-order-tracking"),
                "tags" => [
                    "black",
                    "light",
                    "white",
                    "four-columns",
                    "sans serif",
                    "tracking"
                ],
                "usage" => ["notification"],
                "industries" => ["transportation-storage"]
            ],
            "light-invoice" => [
                "id" => "light-invoice",
                "name" => t(/*#ee-template-name*/ "eetmplname::light-invoice"),
                "tags" => [
                    "black",
                    "light",
                    "white",
                    "invoice",
                    "four-columns",
                    "sans serif"
                ],
                "usage" => ["transactional"],
                "industries" => ["fashion"]
            ],
            "fall-collections" => [
                "id" => "fall-collections",
                "name" => t(/*#ee-template-name*/ "eetmplname::fall-collections"),
                "tags" => ["fashion"],
                "usage" => ["product-launch"],
                "industries" => ["fashion"],
            ],
            "travel-guide-confirmation" => [
                "id" => "travel-guide-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::travel-guide-confirmation"),
                "tags" => [
                    "black",
                    "dark",
                    "yellow",
                    "one-column",
                    "sans serif",
                    "confirmation"
                ],
                "usage" => ["newsletter"],
                "industries" => ["travel-leisure"]
            ],
            "travel-guide-survey" => [
                "id" => "travel-guide-survey",
                "name" => t(/*#ee-template-name*/ "eetmplname::travel-guide-survey"),
                "tags" => [
                    "black",
                    "dark",
                    "yellow",
                    "one-column",
                    "sans serif",
                    "survey"
                ],
                "usage" => ["newsletter"],
                "industries" => ["travel-leisure"]
            ],
            "ecommerce-bag" => [
                "id" => "ecommerce-bag",
                "name" => t(/*#ee-template-name*/ "eetmplname::ecommerce-bag"),
                "tags" => [
                    "blue",
                    "red",
                    "dark",
                    "two-columns",
                    "sans serif",
                    "ecommerce"
                ],
                "usage" => ["notification"],
                "industries" => ["fashion"]
            ],
            "spread-the-order" => [
                "id" => "spread-the-order",
                "name" => t(/*#ee-template-name*/ "eetmplname::spread-the-order"),
                "tags" => [
                    "red",
                    "dark",
                    "serif",
                    "one-column",
                    "light grey",
                    "ecommerce"
                ],
                "usage" => ["notification"],
                "industries" => ["computer-internet"]
            ],
            "password-notification" => [
                "id" => "password-notification",
                "name" => t(/*#ee-template-name*/ "eetmplname::password-notification"),
                "tags" => [
                    "light",
                    "two-columns",
                    "sans serif",
                    "light blue",
                    "notification"
                ],
                "usage" => ["notification"],
                "industries" => ["travel-leisure"]
            ],
            "booking-confirmation" => [
                "id" => "booking-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::booking-confirmation"),
                "tags" => [
                    "light",
                    "blue",
                    "two-columns",
                    "sans serif",
                    "light blue",
                    "confirmation"
                ],
                "usage" => ["transactional"],
                "industries" => ["travel-leisure"]
            ],
            "fashion-discount" => [
                "id" => "fashion-discount",
                "name" => t(/*#ee-template-name*/ "eetmplname::fashion-discount"),
                "tags" => [
                    "light",
                    "purple",
                    "sans serif",
                    "beige",
                    "three-columns",
                    "retention"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "basic-coupon" => [
                "id" => "basic-coupon",
                "name" => t(/*#ee-template-name*/ "eetmplname::basic-coupon"),
                "tags" => [
                    "light",
                    "purple",
                    "blue",
                    "sans serif",
                    "beige",
                    "three-columns",
                    "retention"
                ],
                "usage" => ["transactional"],
                "industries" => ["fashion"]
            ],
            "track-your-order" => [
                "id" => "track-your-order",
                "name" => t(/*#ee-template-name*/ "eetmplname::track-your-order"),
                "tags" => [
                    "light",
                    "purple",
                    "two-columns",
                    "sans serif",
                    "beige",
                    "confirmation"
                ],
                "usage" => ["transactional"],
                "industries" => ["fashion"]
            ],
            "fashion-lifestyle" => [
                "id" => "fashion-lifestyle",
                "name" => t(/*#ee-template-name*/ "eetmplname::fashion-lifestyle"),
                "tags" => [
                    "light",
                    "white",
                    "red",
                    "two-columns",
                    "serif",
                    "communicate"
                ],
                "usage" => ["newsletter"],
                "industries" => ["fashion"]
            ],
            "traveler" => [
                "id" => "traveler",
                "name" => t(/*#ee-template-name*/ "eetmplname::traveler"),
                "tags" => [
                    "dark",
                    "two-columns",
                    "serif",
                    "communicate",
                    "light grey",
                    "brown"
                ],
                "usage" => ["newsletter"],
                "industries" => ["travel-leisure"]
            ],
            "travel-agency" => [
                "id" => "travel-agency",
                "name" => t(/*#ee-template-name*/ "eetmplname::travel-agency"),
                "tags" => [
                    "blue",
                    "dark",
                    "two-columns",
                    "promote",
                    "sans serif",
                    "beige"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "ecommerce" => [
                "id" => "ecommerce",
                "name" => t(/*#ee-template-name*/ "eetmplname::ecommerce"),
                "tags" => [
                    "light",
                    "blue",
                    "red",
                    "sans serif",
                    "three-columns",
                    "sell"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "cookies" => [
                "id" => "cookies",
                "name" => t(/*#ee-template-name*/ "eetmplname::cookies"),
                "tags" => [
                    "grey",
                    "dark",
                    "serif",
                    "communicate",
                    "beige",
                    "three-columns"
                ],
                "usage" => ["newsletter"],
                "industries" => ["food-beverage"]
            ],
            "wellness" => [
                "id" => "wellness",
                "name" => t(/*#ee-template-name*/ "eetmplname::wellness"),
                "tags" => [
                    "light",
                    "two-columns",
                    "promote",
                    "sans serif",
                    "light blue",
                    "lilac"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "basic-ecommerce" => [
                "id" => "basic-ecommerce",
                "name" => t(/*#ee-template-name*/ "eetmplname::basic-ecommerce"),
                "tags" => [
                    "light",
                    "white",
                    "orange",
                    "two-columns",
                    "serif",
                    "communicate"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "freelancer-blog" => [
                "id" => "freelancer-blog",
                "name" => t(/*#ee-template-name*/ "eetmplname::freelancer-blog"),
                "tags" => [
                    "white",
                    "blue",
                    "dark",
                    "two-columns",
                    "serif",
                    "communicate"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["computer-internet"]
            ],
            "easter" => [
                "id" => "easter",
                "name" => t(/*#ee-template-name*/ "eetmplname::easter"),
                "tags" => [
                    "light",
                    "white",
                    "pink",
                    "holiday",
                    "animated",
                    "serif",
                    "one-column"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "lifestyle-blogger" => [
                "id" => "lifestyle-blogger",
                "name" => t(/*#ee-template-name*/ "eetmplname::lifestyle-blogger"),
                "tags" => [
                    "light",
                    "white",
                    "green",
                    "serif",
                    "one-column",
                    "inform",
                    "beige"
                ],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "elegant-invitation" => [
                "id" => "elegant-invitation",
                "name" => t(/*#ee-template-name*/ "eetmplname::elegant-invitation"),
                "tags" => [
                    "light",
                    "white",
                    "green",
                    "serif",
                    "one-column",
                    "invite"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "popping-gifts" => [
                "id" => "popping-gifts",
                "name" => t(/*#ee-template-name*/ "eetmplname::popping-gifts"),
                "tags" => [
                    "light",
                    "white",
                    "green",
                    "animated",
                    "serif",
                    "one-column",
                    "greetings"
                ],
                "usage" => ["personal-note"],
                "industries" => ["travel-leisure"]
            ],
            "love-is-in-the-air" => [
                "id" => "love-is-in-the-air",
                "name" => t(/*#ee-template-name*/ "eetmplname::love-is-in-the-air"),
                "tags" => [
                    "light",
                    "pink",
                    "red",
                    "holiday",
                    "animated",
                    "serif",
                    "one-column",
                    "invite",
                    "valentine's day"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "gaming-magazine" => [
                "id" => "gaming-magazine",
                "name" => t(/*#ee-template-name*/ "eetmplname::gaming-magazine"),
                "tags" => [
                    "light",
                    "white",
                    "green",
                    "one-column",
                    "inform",
                    "sans serif"
                ],
                "usage" => ["newsletter"],
                "industries" => ["media-entertainment"]
            ],
            "organic-rainbow" => [
                "id" => "organic-rainbow",
                "name" => t(/*#ee-template-name*/ "eetmplname::organic-rainbow"),
                "tags" => [
                    "light",
                    "orange",
                    "pink",
                    "red",
                    "green",
                    "one-column",
                    "promote",
                    "sans serif",
                    "light blue"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["food-beverage"]
            ],
            "racer" => [
                "id" => "racer",
                "name" => t(/*#ee-template-name*/ "eetmplname::racer"),
                "tags" => [
                    "light",
                    "white",
                    "red",
                    "one-column",
                    "inform",
                    "sans serif"
                ],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "app-launch" => [
                "id" => "app-launch",
                "name" => t(/*#ee-template-name*/ "eetmplname::app-launch"),
                "tags" => [
                    "light",
                    "white",
                    "blue",
                    "one-column",
                    "launch",
                    "slab"
                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet"]
            ],
            "jazz-concert" => [
                "id" => "jazz-concert",
                "name" => t(/*#ee-template-name*/ "eetmplname::jazz-concert"),
                "tags" => [
                    "black",
                    "dark",
                    "serif",
                    "one-column",
                    "lilac",
                    "book"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "dental-clinique" => [
                "id" => "dental-clinique",
                "name" => t(/*#ee-template-name*/ "eetmplname::dental-clinique"),
                "tags" => [
                    "light",
                    "white",
                    "green",
                    "one-column",
                    "promote",
                    "sans serif",
                    "non-animated"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["health-wellness"]
            ],
            "booth-invitation" => [
                "id" => "booth-invitation",
                "name" => t(/*#ee-template-name*/ "eetmplname::booth-invitation"),
                "tags" => [
                    "light",
                    "white",
                    "one-column",
                    "sans serif",
                    "light blue",
                    "announce"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services"]
            ],
            "online-education" => [
                "id" => "online-education",
                "name" => t(/*#ee-template-name*/ "eetmplname::online-education"),
                "tags" => [
                    "light",
                    "white",
                    "one-column",
                    "promote",
                    "sans serif",
                    "light blue"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["education"]
            ],
            "womens-rights" => [
                "id" => "womens-rights",
                "name" => t(/*#ee-template-name*/ "eetmplname::womens-rights"),
                "tags" => [
                    "black",
                    "dark",
                    "one-column",
                    "inform",
                    "sans serif",
                    "lilac"
                ],
                "usage" => ["newsletter"],
                "industries" => ["non-profit"]
            ],
            "real-estate-doctors" => [
                "id" => "real-estate-doctors",
                "name" => t(/*#ee-template-name*/ "eetmplname::real-estate-doctors"),
                "tags" => [
                    "light",
                    "white",
                    "green",
                    "serif",
                    "one-column",
                    "promote"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["real-estate"]
            ],
            "light-house" => [
                "id" => "light-house",
                "name" => t(/*#ee-template-name*/ "eetmplname::light-house"),
                "tags" => [
                    "light",
                    "white",
                    "blue",
                    "offer",
                    "serif",
                    "one-column"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "cinema-reviews" => [
                "id" => "cinema-reviews",
                "name" => t(/*#ee-template-name*/ "eetmplname::cinema-reviews"),
                "tags" => [
                    "black",
                    "green",
                    "dark",
                    "one-column",
                    "inform",
                    "handwritten"
                ],
                "usage" => ["newsletter"],
                "industries" => ["media-entertainment"]
            ],
            "food-delivery" => [
                "id" => "food-delivery",
                "name" => t(/*#ee-template-name*/ "eetmplname::food-delivery"),
                "tags" => [
                    "light",
                    "white",
                    "green",
                    "two-columns",
                    "promote",
                    "sans serif"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["food-beverage"]
            ],
            "food-app" => [
                "id" => "food-app",
                "name" => t(/*#ee-template-name*/ "eetmplname::food-app"),
                "tags" => [
                    "light",
                    "white",
                    "orange",
                    "two-columns",
                    "promote",
                    "handwritten"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["food-beverage"]
            ],
            "asian-restaurant" => [
                "id" => "asian-restaurant",
                "name" => t(/*#ee-template-name*/ "eetmplname::asian-restaurant"),
                "tags" => [
                    "black",
                    "red",
                    "dark",
                    "two-columns",
                    "serif",
                    "discount"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["food-beverage"]
            ],
            "black-friday-clothes" => [
                "id" => "black-friday-clothes",
                "name" => t(/*#ee-template-name*/ "eetmplname::black-friday-clothes"),
                "tags" => [
                    "black",
                    "green",
                    "dark",
                    "two-columns",
                    "sans serif",
                    "discount"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "app-promo" => [
                "id" => "app-promo",
                "name" => t(/*#ee-template-name*/ "eetmplname::app-promo"),
                "tags" => [
                    "blue",
                    "dark",
                    "animated",
                    "promote",
                    "sans serif",
                    "three-columns",
                    "dark grey"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "beauty" => [
                "id" => "beauty",
                "name" => t(/*#ee-template-name*/ "eetmplname::beauty"),
                "tags" => [
                    "light",
                    "pink",
                    "red",
                    "promote",
                    "sans serif",
                    "three-columns"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "real-estate" => [
                "id" => "real-estate",
                "name" => t(/*#ee-template-name*/ "eetmplname::real-estate"),
                "tags" => [
                    "light",
                    "white",
                    "blue",
                    "serif",
                    "promote",
                    "three-columns"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["real-estate"]
            ],
            "audy" => [
                "id" => "audy",
                "name" => t(/*#ee-template-name*/ "eetmplname::audy"),
                "tags" => [
                    "grey",
                    "light",
                    "red",
                    "two-columns",
                    "sans serif",
                    "launch"
                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet"]
            ],
            "wedding-makeup" => [
                "id" => "wedding-makeup",
                "name" => t(/*#ee-template-name*/ "eetmplname::wedding-makeup"),
                "tags" => [
                    "light",
                    "pink",
                    "purple",
                    "serif",
                    "one-column",
                    "promote"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services"]
            ],
            "vegas-attractions" => [
                "id" => "vegas-attractions",
                "name" => t(/*#ee-template-name*/ "eetmplname::vegas-attractions"),
                "tags" => [
                    "light",
                    "two-columns",
                    "inform",
                    "sans serif",
                    "borwn",
                    "beige"
                ],
                "usage" => ["newsletter"],
                "industries" => ["travel-leisure"]
            ],
            "fabulous-cars" => [
                "id" => "fabulous-cars",
                "name" => t(/*#ee-template-name*/ "eetmplname::fabulous-cars"),
                "tags" => [
                    "black",
                    "purple",
                    "dark",
                    "two-columns",
                    "inform",
                    "sans serif"
                ],
                "usage" => ["newsletter"],
                "industries" => ["automotive"]
            ],
            "halloween" => [
                "id" => "halloween",
                "name" => t(/*#ee-template-name*/ "eetmplname::halloween"),
                "tags" => [
                    "black",
                    "orange",
                    "dark",
                    "holiday",
                    "animated",
                    "two-columns",
                    "sans serif",
                    "halloween"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["media-entertainment"]
            ],
            "christmas" => [
                "id" => "christmas",
                "name" => t(/*#ee-template-name*/ "eetmplname::christmas"),
                "tags" => [
                    "light",
                    "white",
                    "red",
                    "holiday",
                    "animated",
                    "two-columns",
                    "sans serif",
                    "discount",
                    "christmas",
                    "ornaments"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["food-beverage"]
            ],
            "new-years-eve" => [
                "id" => "new-years-eve",
                "name" => t(/*#ee-template-name*/ "eetmplname::new-years-eve"),
                "tags" => [
                    "orange",
                    "purple",
                    "dark",
                    "holiday",
                    "serif",
                    "one-column"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "st-valentines-day" => [
                "id" => "st-valentines-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::st-valentines-day"),
                "tags" => [
                    "purple",
                    "dark",
                    "holiday",
                    "animated",
                    "two-columns",
                    "serif",
                    "fuchsia",
                    "valentine's day"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["computer-internet"]
            ],
            "green-summer" => [
                "id" => "green-summer",
                "name" => t(/*#ee-template-name*/ "eetmplname::green-summer"),
                "tags" => [
                    "orange",
                    "green",
                    "dark",
                    "one-column",
                    "discount",
                    "dark green"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["health-wellness"]
            ],
            "new-hackister-features" => [
                "id" => "new-hackister-features",
                "name" => t(/*#ee-template-name*/ "eetmplname::new-hackister-features"),
                "tags" => [
                    "light",
                    "orange",
                    "pink",
                    "red",
                    "two-columns",
                    "promote"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "freelance" => [
                "id" => "freelance",
                "name" => t(/*#ee-template-name*/ "eetmplname::freelance"),
                "tags" => [
                    "grey",
                    "light",
                    "white",
                    "blue",
                    "two-columns",
                    "propose"
                ],
                "usage" => ["transactional"],
                "industries" => ["business-services"]
            ],
            "finance" => [
                "id" => "finance",
                "name" => t(/*#ee-template-name*/ "eetmplname::finance"),
                "tags" => [
                    "purple",
                    "blue",
                    "green",
                    "dark",
                    "two-columns",
                    "download"
                ],
                "usage" => ["transactional"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "download-freebie" => [
                "id" => "download-freebie",
                "name" => t(/*#ee-template-name*/ "eetmplname::download-freebie"),
                "tags" => [
                    "grey",
                    "purple",
                    "blue",
                    "dark",
                    "two-columns",
                    "download"
                ],
                "usage" => ["download"],
                "industries" => ["education"]
            ],
            "transportation" => [
                "id" => "transportation",
                "name" => t(/*#ee-template-name*/ "eetmplname::transportation"),
                "tags" => [
                    "light",
                    "white",
                    "red",
                    "animated",
                    "two-columns",
                    "light blue",
                    "notify"
                ],
                "usage" => ["notification"],
                "industries" => ["transportation-storage"]
            ],
            "booking-hotel" => [
                "id" => "booking-hotel",
                "name" => t(/*#ee-template-name*/ "eetmplname::booking-hotel"),
                "tags" => [
                    "grey",
                    "green",
                    "dark",
                    "booking",
                    "four-columns",
                    "light blue",
                    "book now",
                    "hotel"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "dentist-reminder" => [
                "id" => "dentist-reminder",
                "name" => t(/*#ee-template-name*/ "eetmplname::dentist-reminder"),
                "tags" => [
                    "light",
                    "white",
                    "blue",
                    "light blue",
                    "three-columns",
                    "notify"
                ],
                "usage" => ["notification"],
                "industries" => ["health-wellness"]
            ],
            "education" => [
                "id" => "education",
                "name" => t(/*#ee-template-name*/ "eetmplname::education"),
                "tags" => [
                    "white",
                    "red",
                    "promote",
                    "four-columns",
                    "light blue",
                    "light grey"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["education"]
            ],
            "service-invoice" => [
                "id" => "service-invoice",
                "name" => t(/*#ee-template-name*/ "eetmplname::service-invoice"),
                "tags" => [
                    "light",
                    "white",
                    "green",
                    "invoice",
                    "three-columns",
                    "light green"
                ],
                "usage" => ["transactional"],
                "industries" => ["business-services"]
            ],
            "product-invoice" => [
                "id" => "product-invoice",
                "name" => t(/*#ee-template-name*/ "eetmplname::product-invoice"),
                "tags" => [
                    "grey",
                    "light",
                    "yellow",
                    "invoice",
                    "four-columns",
                    "light grey"
                ],
                "usage" => ["transactional"],
                "industries" => ["manufacturing"]
            ],
            "wingfly-university" => [
                "id" => "wingfly-university",
                "name" => t(/*#ee-template-name*/ "eetmplname::wingfly-university"),
                "tags" => ["white", "blue", "red", "three-columns", "university"],
                "usage" => ["newsletter"],
                "industries" => ["education"]
            ],
            "rowing-team" => [
                "id" => "rowing-team",
                "name" => t(/*#ee-template-name*/ "eetmplname::rowing-team"),
                "tags" => [
                    "team",
                    "soccer",
                    "match",
                    "football",
                    "game",
                    "four-columns",
                    "sans serif",
                    "inform",
                    "sport",
                    "blue",
                    "white"
                ],
                "usage" => ["events"],
                "industries" => ["health-wellness"]
            ],
            "lets-go-skating" => [
                "id" => "lets-go-skating",
                "name" => t(/*#ee-template-name*/ "eetmplname::lets-go-skating"),
                "tags" => [
                    "green",
                    "animated",
                    "sans serif",
                    "invite",
                    "one-column",
                    "skating",
                    "boy"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "save-the-date" => [
                "id" => "save-the-date",
                "name" => t(/*#ee-template-name*/ "eetmplname::save-the-date"),
                "tags" => [
                    "valentine's day",
                    "dating",
                    "love",
                    "party",
                    "invite",
                    "sans serif",
                    "two-columns",
                    "animated",
                    "holiday",
                    "pink",
                    "white"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "dinner-at-my-place" => [
                "id" => "dinner-at-my-place",
                "name" => t(/*#ee-template-name*/ "eetmplname::dinner-at-my-place"),
                "tags" => [
                    "blue",
                    "animated",
                    "sans serif",
                    "light blue",
                    "invite",
                    "one-column",
                    "donuts",
                    "invitation"
                ],
                "usage" => ["personal-note"],
                "industries" => ["food-beverage"]
            ],
            "back-to-school" => [
                "id" => "back-to-school",
                "name" => t(/*#ee-template-name*/ "eetmplname::back-to-school"),
                "tags" => [
                    "white",
                    "dark",
                    "promote",
                    "handwritten",
                    "discount",
                    "light blue",
                    "merchandising",
                    "school",
                    "four-columns"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["education"]
            ],
            "summer-end" => [
                "id" => "summer-end",
                "name" => t(/*#ee-template-name*/ "eetmplname::summer-end"),
                "tags" => [
                    "light",
                    "blue",
                    "yellow",
                    "holiday",
                    "animated",
                    "sans serif",
                    "dicount",
                    "watermelon",
                    "one-column"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["health-wellness"]
            ],
            "running-app" => [
                "id" => "running-app",
                "name" => t(/*#ee-template-name*/ "eetmplname::running-app"),
                "tags" => [
                    "light",
                    "orange",
                    "blue",
                    "sport",
                    "animated",
                    "sans serif",
                    "three-columns",
                    "notification",
                    "fitness"
                ],
                "usage" => ["transactional"],
                "industries" => ["health-wellness"]
            ],
            "halloween-party" => [
                "id" => "halloween-party",
                "name" => t(/*#ee-template-name*/ "eetmplname::halloween-party"),
                "tags" => [
                    "black",
                    "red",
                    "grunge",
                    "yellow",
                    "holiday",
                    "black friday",
                    "halloween",
                    "autumn",
                    "october",
                    "party"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["media-entertainment"]
            ],
            "garage-band" => [
                "id" => "garage-band",
                "name" => t(/*#ee-template-name*/ "eetmplname::garage-band"),
                "tags" => [
                    "grey",
                    "black",
                    "dark",
                    "animated",
                    "handwritten",
                    "three-columns",
                    "launch",
                    "music",
                    "sell tickets",
                    "merchandising"
                ],
                "usage" => ["product-launch"],
                "industries" => ["media-entertainment"]
            ],
            "print-service" => [
                "id" => "print-service",
                "name" => t(/*#ee-template-name*/ "eetmplname::print-service"),
                "tags" => [
                    "light",
                    "white",
                    "blue",
                    "green",
                    "two-columns",
                    "promote",
                    "sans serif",
                    "rainbow",
                    "paper"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services"]
            ],
            "cyber-monday" => [
                "id" => "cyber-monday",
                "name" => t(/*#ee-template-name*/ "eetmplname::cyber-monday"),
                "tags" => [
                    "white",
                    "purple",
                    "blue",
                    "digital",
                    "sales",
                    "two-columns",
                    "sans serif",
                    "tech",
                    "cyber monday",
                    "watch"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "black-friday-version-1" => [
                "id" => "black-friday-version-1",
                "name" => t(/*#ee-template-name*/ "eetmplname::black-friday-version-1"),
                "tags" => [
                    "black",
                    "white",
                    "handwritten",
                    "discount",
                    "black friday",
                    "shopping",
                    "black&white",
                    "arrow",
                    "clothes"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["fashion"]
            ],
            "newspaper" => [
                "id" => "newspaper",
                "name" => t(/*#ee-template-name*/ "eetmplname::newspaper"),
                "tags" => [
                    "grey",
                    "news",
                    "white",
                    "blue",
                    "sans serif",
                    "three-columns",
                    "journal",
                    "newspaper",
                    "magazine",
                    "articles"
                ],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "bed-breakfast-reservation" => [
                "id" => "bed-breakfast-reservation",
                "name" => t(/*#ee-template-name*/ "eetmplname::bed-breakfast-reservation"),
                "tags" => [
                    "white",
                    "pink",
                    "red",
                    "booking",
                    "holiday",
                    "two-columns",
                    "serif",
                    "hotel",
                    "bedroom",
                    "reservation",
                    "bnb"
                ],
                "usage" => ["transactional"],
                "industries" => ["travel-leisure"]
            ],
            "webinar" => [
                "id" => "webinar",
                "name" => t(/*#ee-template-name*/ "eetmplname::webinar"),
                "tags" => [
                    "learning",
                    "course",
                    "webinar",
                    "certification",
                    "academy",
                    "internet",
                    "light blue",
                    "sans serif",
                    "four-columns",
                    "grey",
                    "blue",
                    "white"
                ],
                "usage" => ["events"],
                "industries" => ["computer-internet"]
            ],
            "car-rental" => [
                "id" => "car-rental",
                "name" => t(/*#ee-template-name*/ "eetmplname::car-rental"),
                "tags" => [
                    "white",
                    "yellow",
                    "sans serif",
                    "three-columns",
                    "car",
                    "rent service",
                    "rental",
                    "rent"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["automotive"]
            ],
            "black-friday-version-2" => [
                "id" => "black-friday-version-2",
                "name" => t(/*#ee-template-name*/ "eetmplname::black-friday-version-2"),
                "tags" => [
                    "black",
                    "red",
                    "two-columns",
                    "sans serif",
                    "black friday",
                    "shopping",
                    "gold",
                    "promotion"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "santa-claus-letter" => [
                "id" => "santa-claus-letter",
                "name" => t(/*#ee-template-name*/ "eetmplname::santa-claus-letter"),
                "tags" => [
                    "white",
                    "red",
                    "gift",
                    "holiday",
                    "two-columns",
                    "sans serif",
                    "christmas",
                    "toys",
                    "letter"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "new-year-party" => [
                "id" => "new-year-party",
                "name" => t(/*#ee-template-name*/ "eetmplname::new-year-party"),
                "tags" => [
                    "black",
                    "white",
                    "red",
                    "holiday",
                    "handwritten",
                    "three-columns",
                    "party",
                    "new year",
                    "fireworks"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "christmas-store" => [
                "id" => "christmas-store",
                "name" => t(/*#ee-template-name*/ "eetmplname::christmas-store"),
                "tags" => [
                    "white",
                    "red",
                    "holiday",
                    "two-columns",
                    "handwritten",
                    "beige",
                    "christmas",
                    "new year",
                    "bird",
                    "snow"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["others"]
            ],
            "chatbot-agency" => [
                "id" => "chatbot-agency",
                "name" => t(/*#ee-template-name*/ "eetmplname::chatbot-agency"),
                "tags" => [
                    "white",
                    "pink",
                    "blue",
                    "sans serif",
                    "light-blue",
                    "bot",
                    "chat",
                    "software"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["computer-internet"]
            ],
            "snowboard" => [
                "id" => "snowboard",
                "name" => t(/*#ee-template-name*/ "eetmplname::snowboard"),
                "tags" => [
                    "light",
                    "sport",
                    "animated",
                    "one-column",
                    "sans serif",
                    "light blue",
                    "video",
                    "snowboard",
                    "winter",
                    "ski"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "winter-is-here" => [
                "id" => "winter-is-here",
                "name" => t(/*#ee-template-name*/ "eetmplname::winter-is-here"),
                "tags" => [
                    "mountain",
                    "three-columns",
                    "hiking",
                    "winter",
                    "snow",
                    "ecommerce",
                    "light blue",
                    "sans serif",
                    "animated",
                    "sport",
                    "yellow",
                    "blue",
                    "shop",
                    "light"
                ],
                "usage" => ["product-launch"],
                "industries" => ["travel-leisure"]
            ],
            "share-the-love" => [
                "id" => "share-the-love",
                "name" => t(/*#ee-template-name*/ "eetmplname::share-the-love"),
                "tags" => [
                    "black",
                    "purple",
                    "red",
                    "dark",
                    "holiday",
                    "sans serif",
                    "three-columns",
                    "love",
                    "heart",
                    "violet",
                    "valentine's day"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "st-valentine" => [
                "id" => "st-valentine",
                "name" => t(/*#ee-template-name*/ "eetmplname::st-valentine"),
                "tags" => [
                    "hearts",
                    "valentine's day",
                    "seasonal",
                    "heart",
                    "love",
                    "download",
                    "sans serif",
                    "one-column",
                    "holiday",
                    "red",
                    "pink"
                ],
                "usage" => ["personal-note"],
                "industries" => ["travel-leisure"]
            ],
            "color-of-the-year" => [
                "id" => "color-of-the-year",
                "name" => t(/*#ee-template-name*/ "eetmplname::color-of-the-year"),
                "tags" => [
                    "graphics",
                    "pantone",
                    "color",
                    "color of the year",
                    "photography",
                    "designer",
                    "design",
                    "coral",
                    "blog",
                    "illustration",
                    "trend",
                    "three-columns",
                    "magazine",
                    "slab",
                    "sans serif",
                    "pink",
                    "orange",
                    "white",
                    "light"
                ],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "crack-the-egg" => [
                "id" => "crack-the-egg",
                "name" => t(/*#ee-template-name*/ "eetmplname::crack-the-egg"),
                "tags" => [
                    "pink",
                    "holiday",
                    "animated",
                    "serif",
                    "one-column",
                    "light blue",
                    "mystery",
                    "sans serif"
                ],
                "usage" => ["seasonal-promotion", "mystery_email"],
                "industries" => ["others"]
            ],
            "freelancer-portfolio" => [
                "id" => "freelancer-portfolio",
                "name" => t(/*#ee-template-name*/ "eetmplname::freelancer-portfolio"),
                "tags" => [
                    "projects",
                    "two-columns",
                    "graphics",
                    "photography",
                    "designer",
                    "design",
                    "blog",
                    "illustration",
                    "slab",
                    "portfolio",
                    "sans serif",
                    "serif",
                    "red",
                    "pink",
                    "white",
                    "light",
                    "grey"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["computer-internet"]
            ],
            "download-app" => [
                "id" => "download-app",
                "name" => t(/*#ee-template-name*/ "eetmplname::download-app"),
                "tags" => [
                    "mobile",
                    "app",
                    "application",
                    "development",
                    "meditation",
                    "two-columns",
                    "designer",
                    "illustration",
                    "download",
                    "light blue",
                    "sans serif",
                    "serif",
                    "rounded",
                    "dark",
                    "white",
                    "grey"
                ],
                "usage" => ["download"],
                "industries" => ["computer-internet"]
            ],
            "charity" => [
                "id" => "charity",
                "name" => t(/*#ee-template-name*/ "eetmplname::charity"),
                "tags" => [
                    "support",
                    "children",
                    "org",
                    "three-columns",
                    "light blue",
                    "sans serif",
                    "serif",
                    "blue",
                    "clean",
                    "white",
                    "light",
                    "grey"
                ],
                "usage" => ["newsletter"],
                "industries" => ["non-profit"]
            ],
            "giving-day" => [
                "id" => "giving-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::giving-day"),
                "tags" => [
                    "giving day",
                    "charity",
                    "no profit",
                    "support",
                    "org",
                    "three-columns",
                    "sans serif",
                    "yellow",
                    "clean",
                    "white",
                    "light",
                    "grey"
                ],
                "usage" => ["events"],
                "industries" => ["non-profit"]
            ],
            "claim-your-prize" => [
                "id" => "claim-your-prize",
                "name" => t(/*#ee-template-name*/ "eetmplname::claim-your-prize"),
                "tags" => [
                    "light",
                    "white",
                    "pink",
                    "purple",
                    "yellow",
                    "serif",
                    "sans serif",
                    "discount",
                    "four-columns",
                    "prize",
                    "gift card"
                ],
                "usage" => ["notification"],
                "industries" => ["others"]
            ],
            "mardi-gras" => [
                "id" => "mardi-gras",
                "name" => t(/*#ee-template-name*/ "eetmplname::mardi-gras"),
                "tags" => [
                    "mardi gras",
                    "mask",
                    "event",
                    "dj",
                    "carnival",
                    "party",
                    "three-columns",
                    "sans serif",
                    "serif",
                    "dark",
                    "green",
                    "orange",
                    "black"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "carnival" => [
                "id" => "carnival",
                "name" => t(/*#ee-template-name*/ "eetmplname::carnival"),
                "tags" => [
                    "parade",
                    "costume",
                    "mask",
                    "event",
                    "mardi gras",
                    "carnival",
                    "party",
                    "three-columns",
                    "light blue",
                    "serif",
                    "dark",
                    "black"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            ///HERE
            "24-hours-only" => [
                "id" => "24-hours-only",
                "name" => t(/*#ee-template-name*/ "eetmplname::24-hours-only"),
                "tags" => [
                    "grey",
                    "light",
                    "orange",
                    "yellow",
                    "offer",
                    "sans serif",
                    "discount",
                    "three-columns",
                    "carnival",
                    "time sensitive",
                    "urgency"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["others"]
            ],
            "registration-completed-v2" => [
                "id" => "registration-completed-v2",
                "name" => t(/*#ee-template-name*/ "eetmplname::registration-completed-v2"),
                "tags" => [
                    "light",
                    "blue",
                    "yellow",
                    "two-columns",
                    "sans serif",
                    "light blue",
                    "confirmation",
                    "registration",
                    "activation",
                    "username"
                ],
                "usage" => ["notification"],
                "industries" => ["others"]
            ],
            "survey" => [
                "id" => "survey",
                "name" => t(/*#ee-template-name*/ "eetmplname::survey"),
                "tags" => [
                    "light",
                    "clean",
                    "blue",
                    "sport",
                    "sans serif",
                    "light blue",
                    "survey",
                    "fitness",
                    "gym",
                    "sports",
                    "question"
                ],
                "usage" => ["newsletter"],
                "industries" => ["health-wellness"]
            ],
            "welcome" => [
                "id" => "welcome",
                "name" => t(/*#ee-template-name*/ "eetmplname::welcome"),
                "tags" => [
                    "series",
                    "workflow",
                    "welcome",
                    "automation",
                    "welcome email",
                    "app",
                    "confirmation",
                    "digital",
                    "blue",
                    "white",
                    "light",
                    "grey"
                ],
                "usage" => ["notification"],
                "industries" => ["computer-internet"]
            ],
            "easter-bunny" => [
                "id" => "easter-bunny",
                "name" => t(/*#ee-template-name*/ "eetmplname::easter-bunny"),
                "tags" => [
                    "orange",
                    "green",
                    "offer",
                    "seasonal",
                    "illustration",
                    "easter",
                    "sale",
                    "gadget",
                    "bunny",
                    "creative"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["manufacturing"]
            ],
            "quotes-newsletter" => [
                "id" => "quotes-newsletter",
                "name" => t(/*#ee-template-name*/ "eetmplname::quotes-newsletter"),
                "tags" => [
                    "duotone",
                    "quotes",
                    "square",
                    "squared",
                    "colors",
                    "newsletter",
                    "typography",
                    "creative",
                    "two-columns",
                    "blog",
                    "simple",
                    "yellow"
                ],
                "usage" => ["newsletter"],
                "industries" => ["media-entertainment", "news-blog-and-magazines"]
            ],
            "spa-welcome-message" => [
                "id" => "spa-welcome-message",
                "name" => t(/*#ee-template-name*/ "eetmplname::spa-welcome-message"),
                "tags" => [
                    "spa",
                    "soft",
                    "wellness",
                    "resort",
                    "welcome message",
                    "onboarding",
                    "welcome",
                    "welcome email",
                    "confirmation",
                    "pink",
                    "clean",
                    "white",
                    "light"
                ],
                "usage" => ["events"],
                "industries" => ["travel-leisure"]
            ],
            "last-chance" => [
                "id" => "last-chance",
                "name" => t(/*#ee-template-name*/ "eetmplname::last-chance"),
                "tags" => [
                    "time",
                    "last chance",
                    "animation",
                    "limited time",
                    "promotional",
                    "hurry",
                    "hurry up",
                    "illustration",
                    "promotion",
                    "coupon",
                    "discount",
                    "animated",
                    "offer",
                    "promo",
                    "blue"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "bee-in-time" => [
                "id" => "bee-in-time",
                "name" => t(/*#ee-template-name*/ "eetmplname::bee-in-time"),
                "tags" => [
                    "technology",
                    "icons",
                    "features",
                    "product",
                    "time",
                    "creative",
                    "watch",
                    "promotion",
                    "launch",
                    "cool",
                    "blue",
                    "purple"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["media-entertainment"]
            ],
            "ticketo" => [
                "id" => "ticketo",
                "name" => t(/*#ee-template-name*/ "eetmplname::ticketo"),
                "tags" => [
                    "orange",
                    "ticket",
                    "sans serif",
                    "light blue",
                    "illustration",
                    "prizes",
                    "win",
                    "engagement",
                    "community",
                    "tickets"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "cryptonboarding" => [
                "id" => "cryptonboarding",
                "name" => t(/*#ee-template-name*/ "eetmplname::cryptonboarding"),
                "tags" => [
                    "grey",
                    "green",
                    "yellow",
                    "app",
                    "welcome",
                    "earn money",
                    "money",
                    "vault",
                    "cryptocurrency"
                ],
                "usage" => ["notification"],
                "industries" => ["computer-internet"]
            ],
            "welcome-customer" => [
                "id" => "welcome-customer",
                "name" => t(/*#ee-template-name*/ "eetmplname::welcome-customer"),
                "tags" => [
                    "transactional",
                    "neutral",
                    "set",
                    "kit",
                    "e-commerce",
                    "welcome",
                    "automation",
                    "welcome email",
                    "ecommerce",
                    "notification",
                    "store",
                    "shop"
                ],
                "usage" => ["e-commerce-set"],
                "industries" => ["others"]
            ],
            "store-categories" => [
                "id" => "store-categories",
                "name" => t(/*#ee-template-name*/ "eetmplname::store-categories"),
                "tags" => [
                    "browsing",
                    "products",
                    "transactional",
                    "neutral",
                    "set",
                    "kit",
                    "e-commerce",
                    "automation",
                    "ecommerce",
                    "notification",
                    "store",
                    "shop"
                ],
                "usage" => ["e-commerce-set"],
                "industries" => ["others"]
            ],
            "browsing-products" => [
                "id" => "browsing-products",
                "name" => t(/*#ee-template-name*/ "eetmplname::browsing-products"),
                "tags" => [
                    "transactional",
                    "neutral",
                    "set",
                    "kit",
                    "e-commerce",
                    "welcome",
                    "automation",
                    "welcome email",
                    "ecommerce",
                    "notification",
                    "store",
                    "shop"
                ],
                "usage" => ["e-commerce-set"],
                "industries" => ["others"]
            ],
            "promocode" => [
                "id" => "promocode",
                "name" => t(/*#ee-template-name*/ "eetmplname::promocode"),
                "tags" => [
                    "transactional",
                    "neutral",
                    "set",
                    "kit",
                    "e-commerce",
                    "welcome",
                    "automation",
                    "welcome email",
                    "ecommerce",
                    "notification",
                    "store",
                    "shop"
                ],
                "usage" => ["e-commerce-set"],
                "industries" => ["others"]
            ],
            "order-notification" => [
                "id" => "order-notification",
                "name" => t(/*#ee-template-name*/ "eetmplname::order-notification"),
                "tags" => [
                    "transactional",
                    "neutral",
                    "set",
                    "kit",
                    "e-commerce",
                    "welcome",
                    "automation",
                    "welcome email",
                    "ecommerce",
                    "notification",
                    "store",
                    "shop"
                ],
                "usage" => ["e-commerce-set"],
                "industries" => ["others"]
            ],
            "shipping-confirmation" => [
                "id" => "shipping-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::shipping-confirmation"),
                "tags" => ["shop", "e-commerce"],
                "usage" => ["e-commerce-set"],
                "industries" => ["others"]
            ],
            "abandoned-cart" => [
                "id" => "abandoned-cart",
                "name" => t(/*#ee-template-name*/ "eetmplname::abandoned-cart"),
                "tags" => ["shop", "e-commerce"],
                "usage" => ["e-commerce-set"],
                "industries" => ["others"]
            ],
            "barber-shop" => [
                "id" => "barber-shop",
                "name" => t(/*#ee-template-name*/ "eetmplname::barber-shop"),
                "tags" => [
                    "black",
                    "shop",
                    "store",
                    "yellow",
                    "discount",
                    "e-commerce",
                    "barber",
                    "small business"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services"]
            ],
            "travel-planner" => [
                "id" => "travel-planner",
                "name" => t(/*#ee-template-name*/ "eetmplname::travel-planner"),
                "tags" => [
                    "event",
                    "list",
                    "backpack",
                    "baggage",
                    "nomad",
                    "bag",
                    "travel",
                    "journey",
                    "luggage",
                    "planner"
                ],
                "usage" => ["personal-note"],
                "industries" => ["travel-leisure"]
            ],
            "personal-presentation" => [
                "id" => "personal-presentation",
                "name" => t(/*#ee-template-name*/ "eetmplname::personal-presentation"),
                "tags" => [
                    "elegant",
                    "clean",
                    "message",
                    "professional",
                    "presentation",
                    "curriculum",
                    "personal",
                    "cv"
                ],
                "usage" => ["personal-note"],
                "industries" => ["business-services"]
            ],
            "presentation-email-that-rocks" => [
                "id" => "presentation-email-that-rocks",
                "name" => t(/*#ee-template-name*/ "eetmplname::presentation-email-that-rocks"),
                "tags" => [
                    "business",
                    "professional",
                    "presentation",
                    "cv",
                    "milestones",
                    "freelancer",
                    "curriculum vitae",
                    "job",
                    "work"
                ],
                "usage" => ["personal-note"],
                "industries" => ["business-services"]
            ],
            "introduce-yourself" => [
                "id" => "introduce-yourself",
                "name" => t(/*#ee-template-name*/ "eetmplname::introduce-yourself"),
                "tags" => [
                    "business",
                    "professional",
                    "presentation",
                    "cv",
                    "milestones",
                    "freelancer",
                    "curriculum vitae",
                    "job",
                    "work"
                ],
                "usage" => ["personal-note"],
                "industries" => ["business-services"]
            ],
            "curriculum-vitae" => [
                "id" => "curriculum-vitae",
                "name" => t(/*#ee-template-name*/ "eetmplname::curriculum-vitae"),
                "tags" => [
                    "business",
                    "professional",
                    "presentation",
                    "cv",
                    "milestones",
                    "freelancer",
                    "curriculum vitae",
                    "job",
                    "work"
                ],
                "usage" => ["personal-note"],
                "industries" => ["business-services"]
            ],
            "qr-code" => [
                "id" => "qr-code",
                "name" => t(/*#ee-template-name*/ "eetmplname::qr-code"),
                "tags" => [
                    "blue",
                    "simple",
                    "light blue",
                    "app",
                    "qr code",
                    "qr",
                    "code"
                ],
                "usage" => ["product-launch", "product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "mad-for-gaming" => [
                "id" => "mad-for-gaming",
                "name" => t(/*#ee-template-name*/ "eetmplname::mad-for-gaming"),
                "tags" => [
                    "fancy",
                    "gaming",
                    "videogames",
                    "color",
                    "blog",
                    "magazine",
                    "game",
                    "colorful",
                    "modern",
                    "cool",
                    "fresh"
                ],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "news-journal" => [
                "id" => "news-journal",
                "name" => t(/*#ee-template-name*/ "eetmplname::news-journal"),
                "tags" => [
                    "news",
                    "press",
                    "contrast",
                    "newspaper",
                    "articles",
                    "blog",
                    "typography",
                    "digest",
                    "textmagazine",
                    "black and white"
                ],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "travel-portal" => [
                "id" => "travel-portal",
                "name" => t(/*#ee-template-name*/ "eetmplname::travel-portal"),
                "tags" => [
                    "blue",
                    "light blue",
                    "creative",
                    "travel",
                    "places",
                    "leisure",
                    "city",
                    "travel agency",
                    "explore"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "peace-day" => [
                "id" => "peace-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::peace-day"),
                "tags" => [
                    "light",
                    "clean",
                    "simple",
                    "holiday",
                    "party",
                    "seasonal",
                    "illustration",
                    "event",
                    "peace",
                    "peace day",
                    "season"
                ],
                "usage" => ["events"],
                "industries" => ["others"]
            ],
            "the-conference" => [
                "id" => "the-conference",
                "name" => t(/*#ee-template-name*/ "eetmplname::the-conference"),
                "tags" => [
                    "red",
                    "cool",
                    "modern",
                    "rich",
                    "business",
                    "glitch",
                    "convention",
                    "conference",
                    "meeting",
                    "trendy"
                ],
                "usage" => ["events"],
                "industries" => ["others"]
            ],
            "coffee-beans" => [
                "id" => "coffee-beans",
                "name" => t(/*#ee-template-name*/ "eetmplname::coffee-beans"),
                "tags" => [
                    "cream",
                    "cool",
                    "offer",
                    "discount",
                    "coupon",
                    "brown",
                    "coffee break",
                    "break",
                    "espresso",
                    "coffee"
                ],
                "usage" => ["product-promotion"],
                "industries" => ["food-beverage"]
            ],
            "podcast" => [
                "id" => "podcast",
                "name" => t(/*#ee-template-name*/ "eetmplname::podcast"),
                "tags" => [
                    "cream",
                    "cool",
                    "rich",
                    "creative",
                    "colors",
                    "freelancer",
                    "podcast",
                    "drawn",
                    "audio",
                    "hand drawn"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["media-entertainment"]
            ],
            "fashion-collection-x" => [
                "id" => "fashion-collection-x",
                "name" => t(/*#ee-template-name*/ "eetmplname::fashion-collection-x"),
                "tags" => [
                    "accessories",
                    "collection",
                    "woman",
                    "products",
                    "e-commerce",
                    "fashion",
                    "clothes",
                    "sans serif",
                    "modern",
                    "shop",
                    "white",
                    "black",
                    "elegant"
                ],
                "usage" => ["product-launch"],
                "industries" => ["fashion"]
            ],
            "fashion-week-app" => [
                "id" => "fashion-week-app",
                "name" => t(/*#ee-template-name*/ "eetmplname::fashion-week-app"),
                "tags" => [
                    "dark",
                    "app",
                    "mobile",
                    "fashion",
                    "events",
                    "geometric"
                ],
                "usage" => ["events"],
                "industries" => ["fashion"]
            ],
            "trial-ended" => [
                "id" => "trial-ended",
                "name" => t(/*#ee-template-name*/ "eetmplname::trial-ended"),
                "tags" => [
                    "saas",
                    "illustration",
                    "app",
                    "welcome",
                    "creative",
                    "graphic design",
                    "trial",
                    "computer & internet",
                    "designers",
                    "trial endend",
                    "undraw"
                ],
                "usage" => ["welcome_series"],
                "industries" => ["marketing_and_design"]
            ],
            "welcome-to-the-webinar" => [
                "id" => "welcome-to-the-webinar",
                "name" => t(/*#ee-template-name*/ "eetmplname::welcome-to-the-webinar"),
                "tags" => [
                    "saas",
                    "webinar",
                    "video",
                    "app",
                    "welcome",
                    "community",
                    "graphic design",
                    "computer & internet",
                    "designers",
                    "learn",
                    "speaker"
                ],
                "usage" => ["welcome_series"],
                "industries" => ["marketing_and_design"]
            ],
            "upgrade-to-a-higher-plan" => [
                "id" => "upgrade-to-a-higher-plan",
                "name" => t(/*#ee-template-name*/ "eetmplname::upgrade-to-a-higher-plan"),
                "tags" => [
                    "saas",
                    "illustration",
                    "app",
                    "welcome",
                    "computer & internet",
                    "undraw",
                    "plans",
                    "upsell",
                    "upgrade"
                ],
                "usage" => ["welcome_series"],
                "industries" => ["marketing_and_design"]
            ],
            "get-started" => [
                "id" => "get-started",
                "name" => t(/*#ee-template-name*/ "eetmplname::get-started"),
                "tags" => [
                    "saas",
                    "video",
                    "illustration",
                    "app",
                    "welcome",
                    "creative",
                    "tutorial",
                    "computer & internet",
                    "undraw",
                    "get started"
                ],
                "usage" => ["welcome_series"],
                "industries" => ["marketing_and_design"]
            ],
            "features-discovery" => [
                "id" => "features-discovery",
                "name" => t(/*#ee-template-name*/ "eetmplname::features-discovery"),
                "tags" => [
                    "app",
                    "welcome",
                    "features",
                    "icons",
                    "computer & internet",
                    "undraw",
                    "illustrated",
                    "how to",
                    "dashboard",
                    "discover"
                ],
                "usage" => ["welcome_series"],
                "industries" => ["marketing_and_design"]
            ],
            "first-welcome" => [
                "id" => "first-welcome",
                "name" => t(/*#ee-template-name*/ "eetmplname::first-welcome"),
                "tags" => [
                    "profile",
                    "customize",
                    "asian",
                    "undraw",
                    "computer & internet",
                    "graphic design",
                    "tool",
                    "creative",
                    "welcome",
                    "app",
                    "illustration",
                    "saas"
                ],
                "usage" => ["welcome_series"],
                "industries" => ["marketing_and_design"]
            ],
            "re-engagement" => [
                "id" => "re-engagement",
                "name" => t(/*#ee-template-name*/ "eetmplname::re-engagement"),
                "tags" => [
                    "saas",
                    "illustration",
                    "app",
                    "welcome",
                    "computer & internet",
                    "undraw",
                    "engage",
                    "reactivate",
                    "re-engagement"
                ],
                "usage" => ["welcome_series"],
                "industries" => ["marketing_and_design"]
            ],
            "you-are-in-good-company" => [
                "id" => "you-are-in-good-company",
                "name" => t(/*#ee-template-name*/ "eetmplname::you-are-in-good-company"),
                "tags" => [
                    "flat",
                    "discover",
                    "undraw",
                    "designers",
                    "computer & internet",
                    "graphic design",
                    "tool",
                    "service",
                    "creative",
                    "welcome",
                    "app",
                    "illustration",
                    "saas"
                ],
                "usage" => ["welcome_series"],
                "industries" => ["marketing_and_design"]
            ],
            "every-drop-counts" => [
                "id" => "every-drop-counts",
                "name" => t(/*#ee-template-name*/ "eetmplname::every-drop-counts"),
                "tags" => [
                    "africa",
                    "wave",
                    "donations",
                    "non-profit",
                    "discover",
                    "video",
                    "sans serif",
                    "modern",
                    "red",
                    "blue",
                    "white",
                    "light"
                ],
                "usage" => ["newsletter"],
                "industries" => ["non-profit"]
            ],
            "toofast-videogame" => [
                "id" => "toofast-videogame",
                "name" => t(/*#ee-template-name*/ "eetmplname::toofast-videogame"),
                "tags" => [
                    "get it",
                    "earth",
                    "speed",
                    "race",
                    "dust",
                    "watch video",
                    "videogames",
                    "video",
                    "car",
                    "brown",
                    "beige",
                    "sans serif",
                    "dark",
                    "blue"
                ],
                "usage" => ["product-launch"],
                "industries" => ["media-entertainment"]
            ],
            "onechance-nonprofit" => [
                "id" => "onechance-nonprofit",
                "name" => t(/*#ee-template-name*/ "eetmplname::onechance-nonprofit"),
                "tags" => [
                    "kids",
                    "watercolor",
                    "how it works",
                    "donations",
                    "non-profit",
                    "discover",
                    "slab",
                    "brown",
                    "beige",
                    "sans serif",
                    "green",
                    "white",
                    "light"
                ],
                "usage" => ["newsletter"],
                "industries" => ["non-profit"]
            ],
            "breast-cancer-awareness" => [
                "id" => "breast-cancer-awareness",
                "name" => t(/*#ee-template-name*/ "eetmplname::breast-cancer-awareness"),
                "tags" => [
                    "unicorn",
                    "donation",
                    "wearitpink",
                    "breast cancer",
                    "breast",
                    "fundraising",
                    "cancer",
                    "non-profit",
                    "now",
                    "bold",
                    "creative",
                    "sans serif",
                    "yellow",
                    "pink",
                    "light"
                ],
                "usage" => ["events"],
                "industries" => ["non-profit"]
            ],
            "dublin" => [
                "id" => "dublin",
                "name" => t(/*#ee-template-name*/ "eetmplname::dublin"),
                "tags" => [
                    "beer",
                    "ireland",
                    "tourist guide",
                    "city",
                    "creative",
                    "event",
                    "serif",
                    "colorful",
                    "yellow",
                    "dark",
                    "green",
                    "white"
                ],
                "usage" => ["events", "service-promotion"],
                "industries" => ["computer-internet", "travel-leisure"]
            ],
            "halloween-2019" => [
                "id" => "halloween-2019",
                "name" => t(/*#ee-template-name*/ "eetmplname::halloween-2019"),
                "tags" => [
                    "black",
                    "orange",
                    "store",
                    "dark",
                    "yellow",
                    "handwritten",
                    "halloween",
                    "creative",
                    "illustrated",
                    "creepy"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["manufacturing", "media-entertainment", "others"]
            ],
            "halloween-anti-spam" => [
                "id" => "halloween-anti-spam",
                "name" => t(/*#ee-template-name*/ "eetmplname::halloween-anti-spam"),
                "tags" => [
                    "black",
                    "orange",
                    "dark",
                    "yellow",
                    "halloween",
                    "software",
                    "illustration",
                    "illustrated",
                    "creepy",
                    "antivirus"
                ],
                "usage" => [
                    "events",
                    "seasonal-promotion",
                    "service-promotion",
                    "mystery_email"
                ],
                "industries" => [
                    "computer-internet",
                    "education",
                    "media-entertainment",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "halloween-party-event" => [
                "id" => "halloween-party-event",
                "name" => t(/*#ee-template-name*/ "eetmplname::halloween-party-event"),
                "tags" => [
                    "black",
                    "blue",
                    "red",
                    "dark",
                    "invite",
                    "halloween",
                    "party",
                    "animation",
                    "discover",
                    "get tickets",
                    "led"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => [
                    "media-entertainment",
                    "news-blog-and-magazines",
                    "travel-leisure",
                    "others"
                ]
            ],
            "halloween-app-discount" => [
                "id" => "halloween-app-discount",
                "name" => t(/*#ee-template-name*/ "eetmplname::halloween-app-discount"),
                "tags" => [
                    "android",
                    "ios",
                    "features",
                    "app",
                    "software",
                    "autumn",
                    "halloween",
                    "download",
                    "discount",
                    "orange",
                    "white",
                    "light",
                    "grey",
                    "minimal"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "halloween-pastry" => [
                "id" => "halloween-pastry",
                "name" => t(/*#ee-template-name*/ "eetmplname::halloween-pastry"),
                "tags" => [
                    "recipes",
                    "cook",
                    "pastry",
                    "candy",
                    "kitchen",
                    "food",
                    "shop now",
                    "blog",
                    "articles",
                    "halloween",
                    "ecommerce",
                    "orange",
                    "white",
                    "light",
                    "minimal"
                ],
                "usage" => ["events"],
                "industries" => ["food-beverage"]
            ],
            "halloween-movie-premier" => [
                "id" => "halloween-movie-premier",
                "name" => t(/*#ee-template-name*/ "eetmplname::halloween-movie-premier"),
                "tags" => [
                    "book tickets",
                    "theater",
                    "joker",
                    "release",
                    "premier",
                    "movie",
                    "trailer",
                    "cinema",
                    "film",
                    "halloween",
                    "dark",
                    "red",
                    "clean",
                    "black"
                ],
                "usage" => ["product-launch"],
                "industries" => ["media-entertainment"]
            ],
            "sandwich-day" => [
                "id" => "sandwich-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::sandwich-day"),
                "tags" => [
                    "light",
                    "white",
                    "orange",
                    "red",
                    "discount",
                    "creative",
                    "food",
                    "fast food",
                    "sandwich",
                    "food and beverage",
                    "sandwich day"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["food-beverage", "others"]
            ],
            "fundraising" => [
                "id" => "fundraising",
                "name" => t(/*#ee-template-name*/ "eetmplname::fundraising"),
                "tags" => [
                    "fundraising",
                    "life saving",
                    "research",
                    "data",
                    "colofrul",
                    "women power",
                    "minimalistic",
                    "life save",
                    "illustrated",
                    "undraw",
                    "donation",
                    "breast cancer",
                    "fundraising",
                    "non-profit",
                    "community",
                    "creative",
                    "support",
                    "yellow",
                    "pink",
                    "white",
                    "light",
                    "grey"
                ],
                "usage" => ["events", "newsletter"],
                "industries" => ["non-profit"]
            ],
            "black-friday-cosmetics" => [
                "id" => "black-friday-cosmetics",
                "name" => t(/*#ee-template-name*/ "eetmplname::black-friday-cosmetics"),
                "tags" => [
                    "palette",
                    "cosmetics",
                    "lipstick",
                    "pastel",
                    "brush",
                    "woman",
                    "products",
                    "e-commerce",
                    "black friday",
                    "discount",
                    "sales",
                    "shop",
                    "elegant"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["cosmetics"]
            ],
            "sports-e-commerce-black-friday" => [
                "id" => "sports-e-commerce-black-friday",
                "name" => t(/*#ee-template-name*/ "eetmplname::sports-e-commerce-black-friday"),
                "tags" => [
                    "dynamic",
                    "protein supplements",
                    "see offers",
                    "diagonal",
                    "shoe",
                    "shop now",
                    "products",
                    "e-commerce",
                    "sale",
                    "gym",
                    "fashion",
                    "black friday",
                    "discount",
                    "sans serif",
                    "sport",
                    "dark",
                    "black"
                ],
                "usage" => ["product-promotion", "seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "black-friday-discount" => [
                "id" => "black-friday-discount",
                "name" => t(/*#ee-template-name*/ "eetmplname::black-friday-discount"),
                "tags" => [
                    "elegant",
                    "shop",
                    "offer",
                    "discount",
                    "ecommerce",
                    "tech",
                    "product",
                    "technology",
                    "product promotion",
                    "black friday"
                ],
                "usage" => ["product-promotion", "seasonal-promotion"],
                "industries" => ["media-entertainment"]
            ],
            "cyber-monday-promo" => [
                "id" => "cyber-monday-promo",
                "name" => t(/*#ee-template-name*/ "eetmplname::cyber-monday-promo"),
                "tags" => [
                    "dark",
                    "sans serif",
                    "tech",
                    "gaming",
                    "glitch",
                    "bold",
                    "cyber monday",
                    "deals"
                ],
                "usage" => ["product-promotion", "seasonal-promotion"],
                "industries" => ["media-entertainment"]
            ],
            "cyber-monday-minimal" => [
                "id" => "cyber-monday-minimal",
                "name" => t(/*#ee-template-name*/ "eetmplname::cyber-monday-minimal"),
                "tags" => [
                    "minimal",
                    "discount",
                    "ecommerce",
                    "sports",
                    "cyber monday",
                    "healthylife",
                    "basketball"
                ],
                "usage" => ["product-promotion", "seasonal-promotion"],
                "industries" => ["health-wellness", "others"]
            ],
            "nps-survey" => [
                "id" => "nps-survey",
                "name" => t(/*#ee-template-name*/ "eetmplname::nps-survey"),
                "tags" => [
                    "grey",
                    "light",
                    "white",
                    "yellow",
                    "saas",
                    "survey",
                    "data",
                    "net promoter score",
                    "boxed",
                    "nps",
                    "web app"
                ],
                "usage" => ["transactional"],
                "industries" => ["computer-internet", "media-entertainment"]
            ],
            "nps" => [
                "id" => "nps",
                "name" => t(/*#ee-template-name*/ "eetmplname::nps"),
                "tags" => [
                    "light",
                    "blue",
                    "survey",
                    "net promoter score",
                    "nps",
                    "social network",
                    "social app",
                    "emojis"
                ],
                "usage" => ["notification", "transactional"],
                "industries" => ["computer-internet", "media-entertainment", "others"]
            ],
            "thanksgiving-party" => [
                "id" => "thanksgiving-party",
                "name" => t(/*#ee-template-name*/ "eetmplname::thanksgiving-party"),
                "tags" => [
                    "orange",
                    "yellow",
                    "holiday",
                    "autumn",
                    "illustration",
                    "family",
                    "food",
                    "delivery",
                    "thanksgiving",
                    "menu",
                    "calligraphy"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["food-beverage"]
            ],
            "thanksgiving-day" => [
                "id" => "thanksgiving-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::thanksgiving-day"),
                "tags" => [
                    "pumpkin pie",
                    "turkey",
                    "light orange",
                    "strong",
                    "cooking",
                    "thanksgiving",
                    "recipes",
                    "cook",
                    "creative",
                    "event",
                    "autumn",
                    "book",
                    "sans serif",
                    "handwritten",
                    "colorful",
                    "orange",
                    "light"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["food-beverage"]
            ],
            "small-business-saturday-deal" => [
                "id" => "small-business-saturday-deal",
                "name" => t(/*#ee-template-name*/ "eetmplname::small-business-saturday-deal"),
                "tags" => [
                    "grocery",
                    "veggies",
                    "smallbusinesssaturday",
                    "product promotion",
                    "food",
                    "e-commerce",
                    "seasonal",
                    "black friday",
                    "brown",
                    "discount",
                    "offer",
                    "dark",
                    "store",
                    "shop",
                    "orange"
                ],
                "usage" => ["product-promotion", "seasonal-promotion"],
                "industries" => ["food-beverage"]
            ],
            "toys-wishlist" => [
                "id" => "toys-wishlist",
                "name" => t(/*#ee-template-name*/ "eetmplname::toys-wishlist"),
                "tags" => [
                    "joyful",
                    "santa claus",
                    "kids",
                    "illustrated",
                    "season",
                    "list",
                    "e-commerce",
                    "creative",
                    "seasonal",
                    "winter",
                    "toys",
                    "christmas",
                    "light blue",
                    "sans serif",
                    "handwritten",
                    "holiday",
                    "colorful",
                    "yellow",
                    "red",
                    "orange",
                    "white",
                    "light"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["manufacturing", "others"]
            ],
            "christmas-cupcakes" => [
                "id" => "christmas-cupcakes",
                "name" => t(/*#ee-template-name*/ "eetmplname::christmas-cupcakes"),
                "tags" => ["christmas"],
                "usage" => ["events", "product-promotion", "seasonal-promotion"],
                "industries" => ["food-beverage"]
            ],
            "hanukkah" => [
                "id" => "hanukkah",
                "name" => t(/*#ee-template-name*/ "eetmplname::hanukkah"),
                "tags" => [
                    "reminder",
                    "hanukkah",
                    "joyful",
                    "animation",
                    "event",
                    "invitation",
                    "party",
                    "light blue",
                    "serif",
                    "colorful",
                    "yellow",
                    "blue",
                    "light"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure", "others"]
            ],
            "hanukkah-party" => [
                "id" => "hanukkah-party",
                "name" => t(/*#ee-template-name*/ "eetmplname::hanukkah-party"),
                "tags" => [
                    "party",
                    "christmas",
                    "hanukkah",
                    "december",
                    "jerusalem",
                    "judaism",
                    "candles"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["food-beverage", "travel-leisure", "others"]
            ],
            "new-year-2020-invitation" => [
                "id" => "new-year-2020-invitation",
                "name" => t(/*#ee-template-name*/ "eetmplname::new-year-2020-invitation"),
                "tags" => [
                    "happy new year",
                    "new years eve",
                    "strong",
                    "minimalistic",
                    "food and beverage",
                    "bold",
                    "animation",
                    "event",
                    "new year",
                    "gold",
                    "invitation",
                    "party",
                    "sans serif",
                    "yellow",
                    "dark",
                    "white",
                    "black",
                    "grey",
                    "elegant"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["fashion", "food-beverage", "travel-leisure", "others"]
            ],
            "new-year-party-2020" => [
                "id" => "new-year-party-2020",
                "name" => t(/*#ee-template-name*/ "eetmplname::new-year-party-2020"),
                "tags" => [
                    "modern",
                    "party",
                    "christmas",
                    "event",
                    "geometric",
                    "newyear",
                    "abstract"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure", "others"]
            ],
            "christmas-super-sale" => [
                "id" => "christmas-super-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::christmas-super-sale"),
                "tags" => [
                    "faith",
                    "holy",
                    "celebration",
                    "joy",
                    "season",
                    "sale",
                    "seasonal",
                    "christmas",
                    "greetings",
                    "holiday",
                    "gift",
                    "simple",
                    "red",
                    "light"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["cosmetics"]
            ],
            "holiday-deals" => [
                "id" => "holiday-deals",
                "name" => t(/*#ee-template-name*/ "eetmplname::holiday-deals"),
                "tags" => [
                    "faith",
                    "celebration",
                    "joy",
                    "season",
                    "animation",
                    "sale",
                    "seasonal",
                    "christmas",
                    "discount",
                    "animated",
                    "holiday",
                    "gift",
                    "red",
                    "blue"
                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["manufacturing", "transportation-storage", "others"]
            ],
            "happy-2020" => [
                "id" => "happy-2020",
                "name" => t(/*#ee-template-name*/ "eetmplname::happy-2020"),
                "tags" => [
                    "light",
                    "red",
                    "simple",
                    "gift",
                    "holiday",
                    "new year",
                    "seasonal",
                    "sale",
                    "joy",
                    "celebration"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure", "others"]
            ],
            "chinese-new-year" => [
                "id" => "chinese-new-year",
                "name" => t(/*#ee-template-name*/ "eetmplname::chinese-new-year"),
                "tags" => [
                    "red",
                    "yellow",
                    "simple",
                    "holiday",
                    "new year",
                    "celebration",
                    "chinese new year",
                    "tradition",
                    "china"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => [
                    "media-entertainment",
                    "news-blog-and-magazines",
                    "others"
                ]
            ],
            "year-of-the-rat" => [
                "id" => "year-of-the-rat",
                "name" => t(/*#ee-template-name*/ "eetmplname::year-of-the-rat"),
                "tags" => [
                    "year of the rat",
                    "photographic",
                    "chinese",
                    "roboto",
                    "new york restaurant",
                    "chinese restaurant",
                    "restaurant",
                    "chinese new year",
                    "joyful",
                    "strong",
                    "colofrul",
                    "food and beverage",
                    "creative",
                    "download",
                    "slab",
                    "coupon",
                    "discount",
                    "animated",
                    "yellow",
                    "red",
                    "white",
                    "light"
                ],
                "usage" => ["events", "service-promotion"],
                "industries" => ["food-beverage", "travel-leisure", "others"]
            ],
            "innovative-ideas" => [
                "id" => "innovative-ideas",
                "name" => t(/*#ee-template-name*/ "eetmplname::innovative-ideas"),
                "tags" => [
                    "light",
                    "blue",
                    "green",
                    "business",
                    "simple",
                    "meeting",
                    "proposal",
                    "company",
                    "clear",
                    "businessman"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services", "computer-internet", "others"]
            ],
            "meeting-confirmation" => [
                "id" => "meeting-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::meeting-confirmation"),
                "tags" => [
                    "clean",
                    "blue",
                    "yellow",
                    "simple",
                    "confirmation",
                    "characters",
                    "services",
                    "laundry service",
                    "meeting confirmation",
                    "laundry"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services", "others"]
            ],
            "perfumery-valentine" => [
                "id" => "perfumery-valentine",
                "name" => t(/*#ee-template-name*/ "eetmplname::perfumery-valentine"),
                "tags" => [
                    "elegant",
                    "light",
                    "white",
                    "pink",
                    "red",
                    "colorful",
                    "sans serif",
                    "flat",
                    "minimalistic",
                    "cosmetics",
                    "strawberry red"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["others", "cosmetics"]
            ],
            "restaurant-discount" => [
                "id" => "restaurant-discount",
                "name" => t(/*#ee-template-name*/ "eetmplname::restaurant-discount"),
                "tags" => [
                    "valentine's day",
                    "restaurant",
                    "joyful",
                    "food and beverage",
                    "food",
                    "creative",
                    "love",
                    "slab",
                    "discount",
                    "sans serif",
                    "colorful",
                    "pink",
                    "white",
                    "light"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["food-beverage", "others"]
            ],
            "super-bowl-2020" => [
                "id" => "super-bowl-2020",
                "name" => t(/*#ee-template-name*/ "eetmplname::super-bowl-2020"),
                "tags" => [
                    "sport",
                    "game",
                    "super bowl",
                    "championship",
                    "american football"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure", "others", "sports"]
            ],
            "invitation-event-series" => [
                "id" => "invitation-event-series",
                "name" => t(/*#ee-template-name*/ "eetmplname::invitation-event-series"),
                "tags" => [
                    "texture",
                    "panelist",
                    "pattern",
                    "media",
                    "location",
                    "reserve your spot",
                    "speaker",
                    "presentation",
                    "technology",
                    "time",
                    "event",
                    "webinar",
                    "invite",
                    "sans serif",
                    "digital",
                    "blue",
                    "white",
                    "light"
                ],
                "usage" => ["events", "event_set"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "invitation-pro-event-series" => [
                "id" => "invitation-pro-event-series",
                "name" => t(/*#ee-template-name*/ "eetmplname::invitation-pro-event-series"),
                "tags" => [
                    "texture",
                    "panelist",
                    "pattern",
                    "media",
                    "location",
                    "reserve your spot",
                    "speaker",
                    "presentation",
                    "technology",
                    "time",
                    "event",
                    "webinar",
                    "invite",
                    "sans serif",
                    "digital",
                    "blue",
                    "white",
                    "light"
                ],
                "usage" => ["events", "event_set"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "marketing_and_design"
                ]
            ],
            "confirmation-event-series" => [
                "id" => "confirmation-event-series",
                "name" => t(/*#ee-template-name*/ "eetmplname::confirmation-event-series"),
                "tags" => [
                    "texture",
                    "panelist",
                    "pattern",
                    "media",
                    "location",
                    "reserve your spot",
                    "speaker",
                    "presentation",
                    "technology",
                    "time",
                    "event",
                    "webinar",
                    "confirmation",
                    "sans serif",
                    "thank you",
                    "digital",
                    "blue",
                    "white",
                    "light"
                ],
                "usage" => ["events", "event_set"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "marketing_and_design"
                ]
            ],
            "reminder-event-series" => [
                "id" => "reminder-event-series",
                "name" => t(/*#ee-template-name*/ "eetmplname::reminder-event-series"),
                "tags" => [
                    "map",
                    "texture",
                    "pattern",
                    "media",
                    "location",
                    "reminder",
                    "speaker",
                    "presentation",
                    "technology",
                    "time",
                    "event",
                    "webinar",
                    "sans serif",
                    "digital",
                    "countdown",
                    "blue",
                    "white",
                    "light"
                ],
                "usage" => ["event_set"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "news-blog-and-magazines",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "my-love" => [
                "id" => "my-love",
                "name" => t(/*#ee-template-name*/ "eetmplname::my-love"),
                "tags" => [
                    "frame",
                    "memories",
                    "moments",
                    "polaroid",
                    "hearth",
                    "handdrawn",
                    "picture",
                    "valentine's day",
                    "mistery",
                    "joyful",
                    "pastel",
                    "drawn",
                    "personal",
                    "illustration",
                    "love",
                    "gift",
                    "colorful",
                    "yellow",
                    "green",
                    "red",
                    "pink"
                ],
                "usage" => ["events", "personal-note", "seasonal-promotion"],
                "industries" => ["others"]
            ],
            "follow-up-event-series" => [
                "id" => "follow-up-event-series",
                "name" => t(/*#ee-template-name*/ "eetmplname::follow-up-event-series"),
                "tags" => [
                    "report",
                    "follow up",
                    "summary",
                    "recap",
                    "texture",
                    "pattern",
                    "media",
                    "reserve your spot",
                    "speaker",
                    "presentation",
                    "technology",
                    "time",
                    "event",
                    "webinar",
                    "sans serif",
                    "photo",
                    "thank you",
                    "digital",
                    "blue",
                    "white",
                    "light"
                ],
                "usage" => ["event_set"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "news-blog-and-magazines",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "most-read-articles" => [
                "id" => "most-read-articles",
                "name" => t(/*#ee-template-name*/ "eetmplname::most-read-articles"),
                "tags" => [
                    "news",
                    "business",
                    "simple",
                    "magazine",
                    "articles",
                    "creative",
                    "year in review"
                ],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines"],
            ],
            "weekly-magazine" => [
                "id" => "weekly-magazine",
                "name" => t(/*#ee-template-name*/ "eetmplname::weekly-magazine"),
                "tags" => [
                    "news",
                    "orange",
                    "clean",
                    "simple",
                    "magazine",
                    "blog",
                    "fashion",
                    "newsletter",
                    "year in review"
                ],
                "usage" => ["newsletter"],
                "industries" => ["fashion"]
            ],
            "year-in-review" => [
                "id" => "year-in-review",
                "name" => t(/*#ee-template-name*/ "eetmplname::year-in-review"),
                "tags" => [
                    "news",
                    "simple",
                    "fashion",
                    "cosmetics",
                    "year in review",
                    "multipurpose"
                ],
                "usage" => ["events", "newsletter"],
                "industries" => ["cosmetics"]
            ],
            "pet-shop" => [
                "id" => "pet-shop",
                "name" => t(/*#ee-template-name*/ "eetmplname::pet-shop"),
                "tags" => [
                    "orange",
                    "simple",
                    "dogs",
                    "pets",
                    "cats",
                    "pet shop",
                    "vet",
                    "veterinary"
                ],
                "usage" => ["newsletter", "product-promotion", "service-promotion"],
                "industries" => ["others", "pets_and_animal_care"]
            ],
            "colorful-invoice" => [
                "id" => "colorful-invoice",
                "name" => t(/*#ee-template-name*/ "eetmplname::colorful-invoice"),
                "tags" => [
                    "business",
                    "simple",
                    "invoice",
                    "bill",
                    "colourful",
                    "bookkeeping",
                    "accounting"
                ],
                "usage" => ["notification", "transactional"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "news-blog-and-magazines",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "legal-studio" => [
                "id" => "legal-studio",
                "name" => t(/*#ee-template-name*/ "eetmplname::legal-studio"),
                "tags" => [
                    "corporate",
                    "simple",
                    "bookkeeping",
                    "accounting",
                    "multipurpose",
                    "law"
                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services", "education"]
            ],
            "mardi-gras-parade" => [
                "id" => "mardi-gras-parade",
                "name" => t(/*#ee-template-name*/ "eetmplname::mardi-gras-parade"),
                "tags" => [
                    "green",
                    "colorful",
                    "party",
                    "carnival",
                    "mask",
                    "joyful",
                    "mardi gras",
                    "stars",
                    "tape"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "fathers-day-photography" => [
                "id" => "fathers-day-photography",
                "name" => t(/*#ee-template-name*/ "eetmplname::fathers-day-photography"),
                "tags" => [
                    "fatherhood",
                    "camera",
                    "father",
                    "father's day",
                    "sale",
                    "photography",
                    "photo",
                    "simple",
                    "father's day campaign",
                    "product promotion",
                    "shop",
                    "celebration",
                    "promotion"
                ],
                "usage" => ["product-promotion", "service-promotion"],
                "industries" => ["manufacturing"]
            ],
            "fathers-day-electronics" => [
                "id" => "fathers-day-electronics",
                "name" => t(/*#ee-template-name*/ "eetmplname::fathers-day-electronics"),
                "tags" => [
                    "elegant",
                    "black",
                    "music",
                    "technology",
                    "father",
                    "electronic",
                    "headphone",
                    "minimalist"
                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["computer-internet", "electronics"]
            ],
            "order-received" => [
                "id" => "order-received",
                "name" => t(/*#ee-template-name*/ "eetmplname::order-received"),
                "tags" => [
                    "rose",
                    "illustration",
                    "summary",
                    "texture",
                    "fun",
                    "invoice",
                    "sans serif",
                    "pattern",
                    "colorful",
                    "order",
                    "blue",
                    "light",
                    "playful",
                    "green"
                ],
                "usage" => ["transactional"],
                "industries" => ["fashion", "others"]
            ],
            "code-activation" => [
                "id" => "code-activation",
                "name" => t(/*#ee-template-name*/ "eetmplname::code-activation"),
                "tags" => [
                    "rose",
                    "illustration",
                    "texture",
                    "fun",
                    "sans serif",
                    "pattern",
                    "verification",
                    "code",
                    "colorful",
                    "blue",
                    "light",
                    "playful",
                    "green"
                ],
                "usage" => ["transactional"],
                "industries" => ["others"]
            ],
            "payment-received" => [
                "id" => "payment-received",
                "name" => t(/*#ee-template-name*/ "eetmplname::payment-received"),
                "tags" => [
                    "rose",
                    "illustration",
                    "texture",
                    "fun",
                    "invoice",
                    "sans serif",
                    "pattern",
                    "payment",
                    "colorful",
                    "blue",
                    "light",
                    "playful",
                    "green"
                ],
                "usage" => ["notification", "transactional"],
                "industries" => ["others"]
            ],
            "message-received" => [
                "id" => "message-received",
                "name" => t(/*#ee-template-name*/ "eetmplname::message-received"),
                "tags" => [
                    "rose",
                    "illustration",
                    "message",
                    "texture",
                    "fun",
                    "sans serif",
                    "pattern",
                    "inbox",
                    "colorful",
                    "notification",
                    "blue",
                    "light",
                    "playful",
                    "green"
                ],
                "usage" => ["notification", "transactional"],
                "industries" => ["others"]
            ],
            "verify-email-address" => [
                "id" => "verify-email-address",
                "name" => t(/*#ee-template-name*/ "eetmplname::verify-email-address"),
                "tags" => [
                    "rose",
                    "email",
                    "illustration",
                    "texture",
                    "fun",
                    "sans serif",
                    "pattern",
                    "colorful",
                    "blue",
                    "light",
                    "playful",
                    "green"
                ],
                "usage" => ["notification", "transactional"],
                "industries" => ["others"]
            ],
            "international-womens-day" => [
                "id" => "international-womens-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::international-womens-day"),
                "tags" => [
                    "fashion",
                    "sale",
                    "woman",
                    "celebration",
                    "8",
                    "special sale",
                    "march",
                    "8 march",
                    "women",
                    "international women's day",
                    "female",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["cosmetics"]
            ],
            "non-profit-dinner" => [
                "id" => "non-profit-dinner",
                "name" => t(/*#ee-template-name*/ "eetmplname::non-profit-dinner"),
                "tags" => [
                    "pink",
                    "simple",
                    "woman",
                    "celebration",
                    "8 march",
                    "international women's day",
                    "non-profit",
                    "npo",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["food-beverage", "others"]
            ],
            "womens-day" => [
                "id" => "womens-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::womens-day"),
                "tags" => [
                    "pink",
                    "fashion",
                    "sale",
                    "woman",
                    "8 march",
                    "international women's day",
                    "female",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["cosmetics"]
            ],
            "8-march" => [
                "id" => "8-march",
                "name" => t(/*#ee-template-name*/ "eetmplname::8-march"),
                "tags" => [
                    "purple",
                    "event",
                    "woman",
                    "celebration",
                    "8 march",
                    "international women's day",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["non-profit"]
            ],
            "fathers-day" => [
                "id" => "fathers-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::fathers-day"),
                "tags" => ["fashion", "sale", "celebration", "father's day",],
                "usage" => ["events", "newsletter", "seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "happy-international-womens-day" => [
                "id" => "happy-international-womens-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::happy-international-womens-day"),
                "tags" => [
                    "pink",
                    "holiday",
                    "event",
                    "celebration",
                    "8 march",
                    "women",
                    "international women's day",
                    "festival",
                    "fundraising",

                ],
                "usage" => [
                    "events",
                    "newsletter",
                    "personal-note",
                    "seasonal-promotion"
                ],
                "industries" => ["non-profit"]
            ],
            "womens-day-event" => [
                "id" => "womens-day-event",
                "name" => t(/*#ee-template-name*/ "eetmplname::womens-day-event"),
                "tags" => [
                    "business",
                    "event",
                    "8 march",
                    "international women's day",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => [
                    "business-services",
                    "non-profit",
                    "marketing_and_design"
                ]
            ],
            "flowers-and-butterflies" => [
                "id" => "flowers-and-butterflies",
                "name" => t(/*#ee-template-name*/ "eetmplname::flowers-and-butterflies"),
                "tags" => [],
                "usage" => ["events", "personal-note", "seasonal-promotion"],
                "industries" => ["others"]
            ],
            "good-luck-to-you" => [
                "id" => "good-luck-to-you",
                "name" => t(/*#ee-template-name*/ "eetmplname::good-luck-to-you"),
                "tags" => [
                    "white",
                    "discount",
                    "light",
                    "e-commerce",
                    "sans serif",
                    "event",
                    "saint patrick's day",
                    "yellow",
                    "fortune",
                    "colorful",
                    "saint patrick's day",
                    "good luck",
                    "strong",
                    "creative",
                    "store",
                    "green",

                ],
                "usage" => ["events", "seasonal-promotion", "mystery_email"],
                "industries" => ["fashion"]
            ],
            "short-invite-survey-serie" => [
                "id" => "short-invite-survey-serie",
                "name" => t(/*#ee-template-name*/ "eetmplname::short-invite-survey-serie"),
                "tags" => [
                    "white",
                    "illustration",
                    "survey",
                    "digital",
                    "technology",
                    "sans serif",
                    "questionnaire",
                    "megaphone",
                    "opinion",
                    "questions",
                    "blue",
                    "emoji",
                    "orange",
                    "light",

                ],
                "usage" => ["transactional", "survey_management_set"],
                "industries" => [
                    "business-services",
                    "computer-internet",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "emoji-invite-survey-serie" => [
                "id" => "emoji-invite-survey-serie",
                "name" => t(/*#ee-template-name*/ "eetmplname::emoji-invite-survey-serie"),
                "tags" => [
                    "white",
                    "illustration",
                    "survey",
                    "digital",
                    "technology",
                    "sans serif",
                    "questionnaire",
                    "megaphone",
                    "opinion",
                    "questions",
                    "blue",
                    "emoji",
                    "orange",
                    "light",

                ],
                "usage" => ["transactional", "survey_management_set"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "questionnaire-invite-survey-serie" => [
                "id" => "questionnaire-invite-survey-serie",
                "name" => t(/*#ee-template-name*/ "eetmplname::questionnaire-invite-survey-serie"),
                "tags" => [
                    "white",
                    "illustration",
                    "survey",
                    "digital",
                    "technology",
                    "sans serif",
                    "questionnaire",
                    "megaphone",
                    "opinion",
                    "questions",
                    "blue",
                    "emoji",
                    "orange",
                    "light",

                ],
                "usage" => [
                    "service-promotion",
                    "transactional",
                    "survey_management_set"
                ],
                "industries" => [
                    "business-services",
                    "computer-internet",
                    "media-entertainment",
                    "non-profit",
                    "marketing_and_design"
                ]
            ],
            "thank-you-survey-serie" => [
                "id" => "thank-you-survey-serie",
                "name" => t(/*#ee-template-name*/ "eetmplname::thank-you-survey-serie"),
                "tags" => [
                    "white",
                    "illustration",
                    "survey",
                    "digital",
                    "technology",
                    "sans serif",
                    "light",
                    "thumbs up",
                    "questionnaire",
                    "opinion",
                    "questions",
                    "blue",
                    "emoji",
                    "orange",
                    "confirmation",

                ],
                "usage" => ["transactional", "survey_management_set"],
                "industries" => [
                    "business-services",
                    "computer-internet",
                    "media-entertainment",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "questionnaire-results-survey-serie" => [
                "id" => "questionnaire-results-survey-serie",
                "name" => t(/*#ee-template-name*/ "eetmplname::questionnaire-results-survey-serie"),
                "tags" => [
                    "white",
                    "illustration",
                    "survey",
                    "digital",
                    "technology",
                    "sans serif",
                    "chart",
                    "pool",
                    "questionnaire",
                    "opinion",
                    "questions",
                    "data",
                    "blue",
                    "emoji",
                    "orange",
                    "light",

                ],
                "usage" => ["transactional", "survey_management_set"],
                "industries" => [
                    "business-services",
                    "computer-internet",
                    "non-profit",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "mystery-flash-sale" => [
                "id" => "mystery-flash-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::mystery-flash-sale"),
                "tags" => [
                    "white",
                    "light",
                    "serif",
                    "sale",
                    "memphis",
                    "counter",
                    "flash sale",
                    "sans serif",
                    "e-commerce",
                    "shop",
                    "colorful",
                    "light blue",
                    "creative",

                ],
                "usage" => ["mystery_email"],
                "industries" => [
                    "automotive",
                    "business-services",
                    "computer-internet",
                    "education",
                    "fashion",
                    "health-wellness",
                    "food-beverage",
                    "manufacturing",
                    "media-entertainment",
                    "news-blog-and-magazines",
                    "non-profit",
                    "real-estate",
                    "transportation-storage",
                    "travel-leisure",
                    "others",
                    "marketing_and_design",
                    "cosmetics",
                    "sports",
                    "pets_and_animal_care"
                ]
            ],
            "spring-deals" => [
                "id" => "spring-deals",
                "name" => t(/*#ee-template-name*/ "eetmplname::spring-deals"),
                "tags" => ["shop", "green", "flowers", "sale", "spring",],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["fashion", "manufacturing"]
            ],
            "spring-sale" => [
                "id" => "spring-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::spring-sale"),
                "tags" => [
                    "pink",
                    "green",
                    "simple",
                    "flowers",
                    "sale",
                    "spring",

                ],
                "usage" => [
                    "events",
                    "newsletter",
                    "product-promotion",
                    "seasonal-promotion"
                ],
                "industries" => ["fashion", "manufacturing"]
            ],
            "fresh-deals" => [
                "id" => "fresh-deals",
                "name" => t(/*#ee-template-name*/ "eetmplname::fresh-deals"),
                "tags" => ["pink", "green", "flowers", "sale", "spring",],
                "usage" => ["events", "product-promotion", "seasonal-promotion"],
                "industries" => ["fashion", "manufacturing"]
            ],
            "april-fools-day" => [
                "id" => "april-fools-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::april-fools-day"),
                "tags" => [
                    "white",
                    "light",
                    "minimalistic",
                    "april fools' day",
                    "funny",
                    "just kidding",
                    "flash sale",
                    "boxed",
                    "sans serif",
                    "e-commerce",
                    "grey",
                    "colorful",
                    "elegant",
                    "blue",
                    "orange",

                ],
                "usage" => [
                    "product-launch",
                    "product-promotion",
                    "seasonal-promotion"
                ],
                "industries" => ["computer-internet", "manufacturing", "others"]
            ],
            "animated-easter-free-delivery" => [
                "id" => "animated-easter-free-delivery",
                "name" => t(/*#ee-template-name*/ "eetmplname::animated-easter-free-delivery"),
                "tags" => [
                    "shipping",
                    "transportation",
                    "shipment",
                    "faq",
                    "spring green",
                    "easter egg",
                    "joyful",
                    "colorful",
                    "creative",
                    "tracking",
                    "bunny",
                    "easter",
                    "sans serif",
                    "pastel",
                    "animation",
                    "laundry",
                    "delivery",
                    "orange",
                    "white",
                    "light",
                    "animated",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["others"]
            ],
            "car-hire" => [
                "id" => "car-hire",
                "name" => t(/*#ee-template-name*/ "eetmplname::car-hire"),
                "tags" => ["green", "simple", "car", "automotive", "dealer",],
                "usage" => ["newsletter", "service-promotion"],
                "industries" => ["automotive"]
            ],
            "test-drive" => [
                "id" => "test-drive",
                "name" => t(/*#ee-template-name*/ "eetmplname::test-drive"),
                "tags" => [
                    "black",
                    "white",
                    "simple",
                    "car",
                    "automotive",
                    "dealer",
                    "test drive",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["automotive"]
            ],
            "special-easter" => [
                "id" => "special-easter",
                "name" => t(/*#ee-template-name*/ "eetmplname::special-easter"),
                "tags" => [
                    "simple",
                    "promotion",
                    "easter",
                    "sale",
                    "bunny",
                    "colourful",
                    "egg",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["others"]
            ],
            "easter-day" => [
                "id" => "easter-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::easter-day"),
                "tags" => [
                    "simple",
                    "easter",
                    "sale",
                    "bunny",
                    "celebration",
                    "colourful",
                    "egg",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["fashion", "others"]
            ],
            "school-activities" => [
                "id" => "school-activities",
                "name" => t(/*#ee-template-name*/ "eetmplname::school-activities"),
                "tags" => [
                    "school",
                    "color",
                    "activities",
                    "highlights",
                    "chalkboard",
                    "calendar activities",
                    "calendar",

                ],
                "usage" => ["newsletter"],
                "industries" => ["education"]
            ],
            "easter-comics" => [
                "id" => "easter-comics",
                "name" => t(/*#ee-template-name*/ "eetmplname::easter-comics"),
                "tags" => [
                    "colorful",
                    "easter",
                    "sale",
                    "celebration",
                    "comics",
                    "eggs",

                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["others"]
            ],
            "business-newsletter" => [
                "id" => "business-newsletter",
                "name" => t(/*#ee-template-name*/ "eetmplname::business-newsletter"),
                "tags" => [
                    "blue",
                    "business",
                    "simple",
                    "professional",
                    "company",
                    "businessman",

                ],
                "usage" => ["newsletter"],
                "industries" => ["business-services"]
            ],
            "dish-of-the-week" => [
                "id" => "dish-of-the-week",
                "name" => t(/*#ee-template-name*/ "eetmplname::dish-of-the-week"),
                "tags" => [
                    "simple",
                    "digest",
                    "food",
                    "recipes",
                    "restaurant",
                    "beverage",

                ],
                "usage" => ["newsletter"],
                "industries" => ["food-beverage", "news-blog-and-magazines"]
            ],
            "animated-teaser" => [
                "id" => "animated-teaser",
                "name" => t(/*#ee-template-name*/ "eetmplname::animated-teaser"),
                "tags" => [
                    "discount",
                    "animation",
                    "teaser",
                    "sweepstakes",
                    "mystery deal",
                    "lottery",
                    "mystery box",

                ],
                "usage" => ["product-promotion", "service-promotion"],
                "industries" => ["others"]
            ],
            "multi-purpose-business" => [
                "id" => "multi-purpose-business",
                "name" => t(/*#ee-template-name*/ "eetmplname::multi-purpose-business"),
                "tags" => [
                    "orange",
                    "blue",
                    "business",
                    "company",
                    "colourful",
                    "accounting",
                    "multipurpose",

                ],
                "usage" => ["newsletter", "service-promotion"],
                "industries" => ["business-services", "others"]
            ],
            "green-sale" => [
                "id" => "green-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::green-sale"),
                "tags" => ["green", "simple", "discount", "earth day", "eco",],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["fashion", "others"]
            ],
            "earth-day" => [
                "id" => "earth-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::earth-day"),
                "tags" => ["green", "sale", "earth day", "eco",],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["fashion", "others"]
            ],
            "be-the-change" => [
                "id" => "be-the-change",
                "name" => t(/*#ee-template-name*/ "eetmplname::be-the-change"),
                "tags" => ["green", "simple", "earth day", "eco", "short",],
                "usage" => ["seasonal-promotion"],
                "industries" => ["non-profit", "others"]
            ],
            "emergency-announcement" => [
                "id" => "emergency-announcement",
                "name" => t(/*#ee-template-name*/ "eetmplname::emergency-announcement"),
                "tags" => [
                    "white",
                    "non-profit",
                    "light",
                    "crisis email",
                    "minimalistic",
                    "prevention",
                    "protection",
                    "news",
                    "coronavirus",
                    "sans serif",
                    "light blue",

                ],
                "usage" => ["events", "notification", "personal-note"],
                "industries" => ["health-wellness", "non-profit"]
            ],
            "looking-for-the-perfect-mothers-day-gift" => [
                "id" => "looking-for-the-perfect-mothers-day-gift",
                "name" => t(/*#ee-template-name*/ "eetmplname::looking-for-the-perfect-mothers-day-gift"),
                "tags" => [
                    "clean",
                    "simple",
                    "flowers",
                    "holiday",
                    "fashion",
                    "sale",
                    "floral",
                    "special offer",
                    "mother's day",

                ],
                "usage" => ["events", "product-promotion", "seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "fly-free-offer" => [
                "id" => "fly-free-offer",
                "name" => t(/*#ee-template-name*/ "eetmplname::fly-free-offer"),
                "tags" => [
                    "store",
                    "blue",
                    "animated",
                    "discount",
                    "light blue",
                    "coupon",
                    "fashion",
                    "special offer",
                    "night",
                    "balloon",

                ],
                "usage" => ["events", "product-launch", "seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "a-gift-for-mom-a-gift-for-you" => [
                "id" => "a-gift-for-mom-a-gift-for-you",
                "name" => t(/*#ee-template-name*/ "eetmplname::a-gift-for-mom-a-gift-for-you"),
                "tags" => ["discount", "promotion", "special offer",],
                "usage" => [
                    "events",
                    "product-launch",
                    "product-promotion",
                    "seasonal-promotion"
                ],
                "industries" => ["fashion", "cosmetics"]
            ],
            "cinco-de-mayo-restaurant-deal" => [
                "id" => "cinco-de-mayo-restaurant-deal",
                "name" => t(/*#ee-template-name*/ "eetmplname::cinco-de-mayo-restaurant-deal"),
                "tags" => [
                    "deal",
                    "holiday",
                    "food",
                    "restaurant",
                    "colourful",
                    "special offer",
                    "fiesta",
                    "meal deal",
                    "mexican",
                    "cinco de mayo",

                ],
                "usage" => [
                    "events",
                    "product-launch",
                    "product-promotion",
                    "seasonal-promotion",
                    "service-promotion"
                ],
                "industries" => ["food-beverage"]
            ],
            "mom-i-love-you" => [
                "id" => "mom-i-love-you",
                "name" => t(/*#ee-template-name*/ "eetmplname::mom-i-love-you"),
                "tags" => [
                    "shop",
                    "cosmetics",
                    "celebration",
                    "women",
                    "international women's day",
                    "mother's day",

                ],
                "usage" => [
                    "events",
                    "product-launch",
                    "product-promotion",
                    "seasonal-promotion",
                    "service-promotion"
                ],
                "industries" => ["cosmetics"]
            ],
            "memorial-day-event" => [
                "id" => "memorial-day-event",
                "name" => t(/*#ee-template-name*/ "eetmplname::memorial-day-event"),
                "tags" => [
                    "colorful",
                    "party",
                    "event",
                    "celebration",
                    "memorial",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure", "others"]
            ],
            "memorial-day-party" => [
                "id" => "memorial-day-party",
                "name" => t(/*#ee-template-name*/ "eetmplname::memorial-day-party"),
                "tags" => ["colorful", "party", "celebration", "memorial",],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure", "others"]
            ],
            "cinco-de-mayo-fiesta" => [
                "id" => "cinco-de-mayo-fiesta",
                "name" => t(/*#ee-template-name*/ "eetmplname::cinco-de-mayo-fiesta"),
                "tags" => [
                    "colorful",
                    "food",
                    "recipes",
                    "celebration",
                    "festival",
                    "cinco de mayo",
                    "mexico",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure", "others"]
            ],
            "la-cantina" => [
                "id" => "la-cantina",
                "name" => t(/*#ee-template-name*/ "eetmplname::la-cantina"),
                "tags" => [
                    "simple",
                    "sale",
                    "food",
                    "celebration",
                    "cinco de mayo",
                    "mexico",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["food-beverage", "travel-leisure", "others"]
            ],
            "school-day-program" => [
                "id" => "school-day-program",
                "name" => t(/*#ee-template-name*/ "eetmplname::school-day-program"),
                "tags" => [
                    "colorful",
                    "school",
                    "university",
                    "children",
                    "kids",
                    "weekly program",
                    "education",

                ],
                "usage" => [
                    "events",
                    "newsletter",
                    "notification",
                    "service-promotion"
                ],
                "industries" => ["education", "non-profit", "others"]
            ],
            "treat-mom-like-gold" => [
                "id" => "treat-mom-like-gold",
                "name" => t(/*#ee-template-name*/ "eetmplname::treat-mom-like-gold"),
                "tags" => [
                    "promo",
                    "magazine",
                    "fashion",
                    "spa",
                    "mother's day",
                    "self-care",
                    "feminine",
                    "gifts",
                    "mom",

                ],
                "usage" => [
                    "events",
                    "product-launch",
                    "product-promotion",
                    "seasonal-promotion"
                ],
                "industries" => ["cosmetics"]
            ],
            "fashion-mom" => [
                "id" => "fashion-mom",
                "name" => t(/*#ee-template-name*/ "eetmplname::fashion-mom"),
                "tags" => [
                    "fashion",
                    "sale",
                    "celebration",
                    "women",
                    "international women's day",
                    "mother's day",

                ],
                "usage" => [
                    "events",
                    "product-promotion",
                    "seasonal-promotion",
                    "service-promotion"
                ],
                "industries" => ["fashion"]
            ],
            "latest-posts" => [
                "id" => "latest-posts",
                "name" => t(/*#ee-template-name*/ "eetmplname::latest-posts"),
                "tags" => [
                    "news",
                    "blue",
                    "simple",
                    "digest",
                    "influencer",
                    "social",
                    "latest posts",

                ],
                "usage" => ["newsletter"],
                "industries" => ["media-entertainment", "news-blog-and-magazines"]
            ],
            "pawsome-treats-for-national-pet-week" => [
                "id" => "pawsome-treats-for-national-pet-week",
                "name" => t(/*#ee-template-name*/ "eetmplname::pawsome-treats-for-national-pet-week"),
                "tags" => [
                    "promotion",
                    "blog",
                    "dogs",
                    "pets",
                    "cats",
                    "pet food",
                    "new product",
                    "national pet week",

                ],
                "usage" => [
                    "events",
                    "product-launch",
                    "product-promotion",
                    "seasonal-promotion"
                ],
                "industries" => ["pets_and_animal_care"]
            ],
            "birthday-mystery-gift" => [
                "id" => "birthday-mystery-gift",
                "name" => t(/*#ee-template-name*/ "eetmplname::birthday-mystery-gift"),
                "tags" => ["gift", "birthday", "surprise", "present", "giftbox",],
                "usage" => ["events", "personal-note"],
                "industries" => ["others"]
            ],
            "taco-bout-delicious" => [
                "id" => "taco-bout-delicious",
                "name" => t(/*#ee-template-name*/ "eetmplname::taco-bout-delicious"),
                "tags" => [
                    "promo",
                    "party",
                    "invitation",
                    "restaurant",
                    "cinco de mayo",
                    "food & beverage",
                    "celebrations",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["food-beverage", "travel-leisure", "others"]
            ],
            "fiesta-fiesta" => [
                "id" => "fiesta-fiesta",
                "name" => t(/*#ee-template-name*/ "eetmplname::fiesta-fiesta"),
                "tags" => [
                    "food",
                    "food and beverage",
                    "celebration",
                    "restaurant",
                    "beverage",
                    "fiesta",
                    "cinco de mayo",
                    "may",

                ],
                "usage" => ["events", "seasonal-promotion", "service-promotion"],
                "industries" => ["food-beverage"]
            ],
            "national-salad-month" => [
                "id" => "national-salad-month",
                "name" => t(/*#ee-template-name*/ "eetmplname::national-salad-month"),
                "tags" => [
                    "light",
                    "food",
                    "recipes",
                    "food and beverage",
                    "national salad month",
                    "healthy",
                    "vegetables",
                    "greens",
                    "salad",
                    "recipe",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => [
                    "health-wellness",
                    "food-beverage",
                    "news-blog-and-magazines"
                ]
            ],
            "video-editor-curriculum-vitae" => [
                "id" => "video-editor-curriculum-vitae",
                "name" => t(/*#ee-template-name*/ "eetmplname::video-editor-curriculum-vitae"),
                "tags" => [
                    "professional",
                    "personal",
                    "cv",
                    "freelancer",
                    "curriculum vitae",
                    "services",
                    "job search",
                    "videographer",
                    "self promotion",
                    "video editor",

                ],
                "usage" => ["personal-note", "service-promotion"],
                "industries" => [
                    "business-services",
                    "media-entertainment",
                    "travel-leisure",
                    "others"
                ]
            ],
            "video-conference-invitation" => [
                "id" => "video-conference-invitation",
                "name" => t(/*#ee-template-name*/ "eetmplname::video-conference-invitation"),
                "tags" => [
                    "invite",
                    "video",
                    "personal",
                    "conference",
                    "call",
                    "video call",
                    "video conference",

                ],
                "usage" => ["notification", "personal-note", "service-promotion"],
                "industries" => ["others", "marketing_and_design"]
            ],
            "video-call" => [
                "id" => "video-call",
                "name" => t(/*#ee-template-name*/ "eetmplname::video-call"),
                "tags" => [
                    "business",
                    "simple",
                    "invitation",
                    "conference",
                    "meeting",
                    "meeting confirmation",
                    "video call",
                    "remote",

                ],
                "usage" => ["notification", "personal-note", "service-promotion"],
                "industries" => ["computer-internet", "others"]
            ],
            "influencer" => [
                "id" => "influencer",
                "name" => t(/*#ee-template-name*/ "eetmplname::influencer"),
                "tags" => [
                    "news",
                    "light",
                    "simple",
                    "digest",
                    "influencer",
                    "social",
                    "latest posts",

                ],
                "usage" => ["service-promotion"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "delicious" => [
                "id" => "delicious",
                "name" => t(/*#ee-template-name*/ "eetmplname::delicious"),
                "tags" => [
                    "red",
                    "simple",
                    "magazine",
                    "blog",
                    "digest",
                    "food",
                    "recipes",
                    "food and beverage",
                    "influencer",
                    "social",
                    "zine",

                ],
                "usage" => ["newsletter"],
                "industries" => ["food-beverage", "news-blog-and-magazines"]
            ],
            "hello-there-cv" => [
                "id" => "hello-there-cv",
                "name" => t(/*#ee-template-name*/ "eetmplname::hello-there-cv"),
                "tags" => [
                    "creative",
                    "professional",
                    "curriculum vitae",
                    "work",
                    "resume",
                    "experience",
                    "photographer",

                ],
                "usage" => ["service-promotion"],
                "industries" => [
                    "media-entertainment",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "pink-pet-shop" => [
                "id" => "pink-pet-shop",
                "name" => t(/*#ee-template-name*/ "eetmplname::pink-pet-shop"),
                "tags" => [
                    "minimal",
                    "pink",
                    "ecommerce",
                    "cat",
                    "dog",
                    "petweek",
                    "animal",

                ],
                "usage" => ["newsletter", "seasonal-promotion", "service-promotion"],
                "industries" => ["pets_and_animal_care"]
            ],
            "meeting-invitation" => [
                "id" => "meeting-invitation",
                "name" => t(/*#ee-template-name*/ "eetmplname::meeting-invitation"),
                "tags" => [
                    "invitation",
                    "video",
                    "meeting",
                    "meeting confirmation",
                    "video call",
                    "remote",
                    "conference call",

                ],
                "usage" => ["personal-note", "service-promotion"],
                "industries" => ["computer-internet", "others"]
            ],
            "properties-digest" => [
                "id" => "properties-digest",
                "name" => t(/*#ee-template-name*/ "eetmplname::properties-digest"),
                "tags" => [
                    "light",
                    "gallery",
                    "home",
                    "contact us",
                    "digest",
                    "agent",
                    "property",
                    "listings",
                    "sans serif",
                    "discover",
                    "real estate",
                    "read article",
                    "house",
                    "testimonial",
                    "orange",
                    "green",

                ],
                "usage" => ["service-promotion", "real_estate_set"],
                "industries" => ["real-estate"]
            ],
            "listings" => [
                "id" => "listings",
                "name" => t(/*#ee-template-name*/ "eetmplname::listings"),
                "tags" => [
                    "light",
                    "orange",
                    "green",
                    "sans serif",
                    "discover",
                    "house",
                    "real estate",
                    "listings",
                    "property",
                    "home",
                    "view property",

                ],
                "usage" => ["service-promotion", "real_estate_set"],
                "industries" => ["business-services", "real-estate"]
            ],
            "welcome-to-real-estate" => [
                "id" => "welcome-to-real-estate",
                "name" => t(/*#ee-template-name*/ "eetmplname::welcome-to-real-estate"),
                "tags" => [
                    "welcome",
                    "light",
                    "get started",
                    "make an offer",
                    "search",
                    "home",
                    "contact us",
                    "property",
                    "sans serif",
                    "real estate",
                    "house",
                    "orange",
                    "green",

                ],
                "usage" => ["service-promotion", "real_estate_set"],
                "industries" => ["business-services", "real-estate"]
            ],
            "home-sold" => [
                "id" => "home-sold",
                "name" => t(/*#ee-template-name*/ "eetmplname::home-sold"),
                "tags" => [
                    "light",
                    "find",
                    "search",
                    "home",
                    "property",
                    "listings",
                    "sans serif",
                    "discover",
                    "real estate",
                    "house",
                    "orange",
                    "confirmation",
                    "green",

                ],
                "usage" => ["service-promotion", "real_estate_set"],
                "industries" => ["real-estate"]
            ],
            "schedule-a-visit" => [
                "id" => "schedule-a-visit",
                "name" => t(/*#ee-template-name*/ "eetmplname::schedule-a-visit"),
                "tags" => [
                    "map",
                    "address",
                    "schedule a visit",
                    "select date",
                    "home",
                    "agent",
                    "property",
                    "sans serif",
                    "real estate",
                    "house",
                    "orange",
                    "light",
                    "location",
                    "green",

                ],
                "usage" => ["service-promotion", "real_estate_set"],
                "industries" => ["real-estate"]
            ],
            "confirmation-visit" => [
                "id" => "confirmation-visit",
                "name" => t(/*#ee-template-name*/ "eetmplname::confirmation-visit"),
                "tags" => [
                    "email",
                    "map",
                    "google maps",
                    "telephone",
                    "address",
                    "agent",
                    "property",
                    "sans serif",
                    "real estate",
                    "add to calendar",
                    "house",
                    "light",
                    "orange",
                    "confirmation",
                    "location",
                    "green",

                ],
                "usage" => ["service-promotion", "real_estate_set"],
                "industries" => ["real-estate"]
            ],
            "from-the-real-estate-blog" => [
                "id" => "from-the-real-estate-blog",
                "name" => t(/*#ee-template-name*/ "eetmplname::from-the-real-estate-blog"),
                "tags" => [
                    "blog",
                    "articles",
                    "home",
                    "property",
                    "sans serif",
                    "discover",
                    "real estate",
                    "read article",
                    "house",
                    "orange",
                    "light",
                    "green",

                ],
                "usage" => ["service-promotion", "real_estate_set"],
                "industries" => ["real-estate"]
            ],
            "fast-wheels" => [
                "id" => "fast-wheels",
                "name" => t(/*#ee-template-name*/ "eetmplname::fast-wheels"),
                "tags" => [
                    "car",
                    "automotive",
                    "repair",
                    "car dealer",
                    "dark there",
                    "auto",

                ],
                "usage" => ["product-launch", "service-promotion"],
                "industries" => ["automotive"]
            ],
            "car-dealer" => [
                "id" => "car-dealer",
                "name" => t(/*#ee-template-name*/ "eetmplname::car-dealer"),
                "tags" => ["car", "automotive", "car dealer", "auto",],
                "usage" => ["product-launch"],
                "industries" => ["automotive"]
            ],
            "love-you-mom" => [
                "id" => "love-you-mom",
                "name" => t(/*#ee-template-name*/ "eetmplname::love-you-mom"),
                "tags" => [
                    "gift",
                    "service",
                    "floral",
                    "love",
                    "mother's day",
                    "flourish",
                    "plant",
                    "product",
                    "flower",
                    "house",
                    "mom",
                    "holiday",
                    "promotion",
                    "decoration",

                ],
                "usage" => ["events", "seasonal-promotion", "service-promotion"],
                "industries" => ["others"]
            ],
            "graphic-designer-portfolio" => [
                "id" => "graphic-designer-portfolio",
                "name" => t(/*#ee-template-name*/ "eetmplname::graphic-designer-portfolio"),
                "tags" => [
                    "portfolio",
                    "self promotion",
                    "ui",
                    "web designer",
                    "curriculum vitae",
                    "designer",
                    "web design",
                    "ux",
                    "works",
                    "graphic designer",
                    "freelancer",
                    "projects",
                    "creative",
                    "services",

                ],
                "usage" => ["personal-note", "service-promotion"],
                "industries" => ["computer-internet", "others", "marketing_and_design"]
            ],
            "get-in-touch" => [
                "id" => "get-in-touch",
                "name" => t(/*#ee-template-name*/ "eetmplname::get-in-touch"),
                "tags" => [
                    "portfolio",
                    "self promotion",
                    "service promotion",
                    "skills",
                    "web designer",
                    "works",
                    "graphic designer",
                    "freelancer",
                    "colorful",
                    "projects",
                    "creative",
                    "resume",
                    "services",

                ],
                "usage" => ["personal-note", "service-promotion"],
                "industries" => ["computer-internet", "others", "marketing_and_design"]
            ],
            "web-designer-cv" => [
                "id" => "web-designer-cv",
                "name" => t(/*#ee-template-name*/ "eetmplname::web-designer-cv"),
                "tags" => [
                    "cv",
                    "portfolio",
                    "profile",
                    "self promotion",
                    "introduction",
                    "service promotion",
                    "skills",
                    "presentation",
                    "web designer",
                    "curriculum vitae",
                    "freelancer",
                    "experience",
                    "creative",
                    "education",
                    "resume",

                ],
                "usage" => ["personal-note", "service-promotion"],
                "industries" => ["computer-internet", "others", "marketing_and_design"]
            ],
            "blue-cv" => [
                "id" => "blue-cv",
                "name" => t(/*#ee-template-name*/ "eetmplname::blue-cv"),
                "tags" => [
                    "cv",
                    "portfolio",
                    "self promotion",
                    "curriculum",
                    "service promotion",
                    "skills",
                    "web designer",
                    "curriculum vitae",
                    "freelancer",
                    "projects",
                    "creative",
                    "experience",
                    "resume",

                ],
                "usage" => ["personal-note", "service-promotion"],
                "industries" => ["computer-internet", "others", "marketing_and_design"]
            ],
            "introduce-your-latest-work" => [
                "id" => "introduce-your-latest-work",
                "name" => t(/*#ee-template-name*/ "eetmplname::introduce-your-latest-work"),
                "tags" => [
                    "cv",
                    "portfolio",
                    "projects",
                    "work",
                    "job",
                    "presentation",
                    "ui",
                    "curriculum vitae",
                    "works",
                    "ux",
                    "professional",
                    "freelancer",
                    "resume",

                ],
                "usage" => ["personal-note", "service-promotion"],
                "industries" => ["computer-internet", "others", "marketing_and_design"]
            ],
            "love-has-no-gender" => [
                "id" => "love-has-no-gender",
                "name" => t(/*#ee-template-name*/ "eetmplname::love-has-no-gender"),
                "tags" => [
                    "rainbow",
                    "coronavirus",
                    "pride events",
                    "lgbtq",
                    "pride parade",
                    "lgbt",
                    "pride",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["computer-internet", "non-profit"]
            ],
            "love-is-in-the-air-2020" => [
                "id" => "love-is-in-the-air-2020",
                "name" => t(/*#ee-template-name*/ "eetmplname::love-is-in-the-air-2020"),
                "tags" => [
                    "pink",
                    "colorful",
                    "rainbow",
                    "love",
                    "event",
                    "coronavirus",
                    "pride events",
                    "lgbtq",
                    "pride parade",
                    "lgbt",
                    "pride",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["computer-internet"]
            ],
            "love-is-love" => [
                "id" => "love-is-love",
                "name" => t(/*#ee-template-name*/ "eetmplname::love-is-love"),
                "tags" => [
                    "rainbow",
                    "product launch",
                    "seasonal promotion",
                    "makeup",
                    "pride",
                    "lgbt",
                    "lgbtq",
                    "love",
                    "e-commerce",
                    "shop",
                    "retail",
                    "cosmetics",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["cosmetics"]
            ],
            "rock-dads-world" => [
                "id" => "rock-dads-world",
                "name" => t(/*#ee-template-name*/ "eetmplname::rock-dads-world"),
                "tags" => [
                    "promotion",
                    "sale",
                    "technology",
                    "father's day",
                    "gifts",
                    "special offer",
                    "dad",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => [
                    "computer-internet",
                    "manufacturing",
                    "media-entertainment"
                ]
            ],
            "love-your-self" => [
                "id" => "love-your-self",
                "name" => t(/*#ee-template-name*/ "eetmplname::love-your-self"),
                "tags" => [
                    "promo",
                    "products",
                    "tutorial",
                    "self-care",
                    "hair",
                    "lgbt",
                    "makeup",
                    "body",
                    "beauty",
                    "pride day",
                    "face",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["cosmetics"]
            ],
            "all-souls-welcome-2020" => [
                "id" => "all-souls-welcome-2020",
                "name" => t(/*#ee-template-name*/ "eetmplname::all-souls-welcome-2020"),
                "tags" => [
                    "party",
                    "rainbow",
                    "hearth",
                    "lgbtq",
                    "lgbt",
                    "pride",
                    "souls",
                    "gay pride",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "seafood-restaurant" => [
                "id" => "seafood-restaurant",
                "name" => t(/*#ee-template-name*/ "eetmplname::seafood-restaurant"),
                "tags" => [
                    "blue",
                    "food",
                    "food and beverage",
                    "restaurant",
                    "sea",
                    "seafood",
                    "fish",
                    "waves",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["food-beverage", "travel-leisure", "others"]
            ],
            "my-webinar" => [
                "id" => "my-webinar",
                "name" => t(/*#ee-template-name*/ "eetmplname::my-webinar"),
                "tags" => [
                    "business",
                    "webinar",
                    "conference",
                    "calendar",
                    "register",
                    "talk",
                    "website",
                    "speakers",

                ],
                "usage" => ["events", "notification"],
                "industries" => ["computer-internet", "others"]
            ],
            "watch-for-this-offer-just-in-time-for-fathers-day" => [
                "id" => "watch-for-this-offer-just-in-time-for-fathers-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::watch-for-this-offer-just-in-time-for-fathers-day"),
                "tags" => [],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["computer-internet", "manufacturing", "electronics"]
            ],
            "what-daddy-really-wants" => [
                "id" => "what-daddy-really-wants",
                "name" => t(/*#ee-template-name*/ "eetmplname::what-daddy-really-wants"),
                "tags" => [
                    "gift",
                    "ecommerce",
                    "father's day",
                    "dad",
                    "electronics",
                    "devices",
                    "daddy",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["manufacturing", "electronics"]
            ],
            "fathers-day-sale" => [
                "id" => "fathers-day-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::fathers-day-sale"),
                "tags" => [
                    "tech",
                    "celebration",
                    "father's day campaign",
                    "father's day",
                    "dad",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["others"]
            ],
            "fathers-day-tech" => [
                "id" => "fathers-day-tech",
                "name" => t(/*#ee-template-name*/ "eetmplname::fathers-day-tech"),
                "tags" => [
                    "blue",
                    "tech",
                    "celebration",
                    "father's day campaign",
                    "father's day",
                    "dad",

                ],
                "usage" => ["events", "product-promotion", "seasonal-promotion"],
                "industries" => ["electronics"]
            ],
            "hairdresser" => [
                "id" => "hairdresser",
                "name" => t(/*#ee-template-name*/ "eetmplname::hairdresser"),
                "tags" => [
                    "style",
                    "simple",
                    "brown",
                    "cut",
                    "salon",
                    "hair",
                    "hairdresser",
                    "beauty salon",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["health-wellness", "others"]
            ],
            "fathers-day-in-style" => [
                "id" => "fathers-day-in-style",
                "name" => t(/*#ee-template-name*/ "eetmplname::fathers-day-in-style"),
                "tags" => [
                    "business",
                    "simple",
                    "discount",
                    "sale",
                    "celebration",
                    "father's day",

                ],
                "usage" => ["events", "product-promotion", "seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "fathers-day-blue" => [
                "id" => "fathers-day-blue",
                "name" => t(/*#ee-template-name*/ "eetmplname::fathers-day-blue"),
                "tags" => [
                    "simple",
                    "discount",
                    "sale",
                    "celebration",
                    "father's day",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["computer-internet", "electronics"]
            ],
            "we-work-outside-of-the-box" => [
                "id" => "we-work-outside-of-the-box",
                "name" => t(/*#ee-template-name*/ "eetmplname::we-work-outside-of-the-box"),
                "tags" => [
                    "digital",
                    "design",
                    "creative",
                    "marketing",
                    "agency",
                    "strategy",
                    "strategic",
                    "innovation",
                    "branding",
                    "web",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services", "others", "marketing_and_design"]
            ],
            "apologize-email" => [
                "id" => "apologize-email",
                "name" => t(/*#ee-template-name*/ "eetmplname::apologize-email"),
                "tags" => [
                    "red",
                    "sport",
                    "coupon",
                    "running",
                    "mistake",
                    "coupon code",
                    "apologize",

                ],
                "usage" => ["apologize_email"],
                "industries" => ["fashion", "health-wellness", "sports"]
            ],
            "apologize-fashion" => [
                "id" => "apologize-fashion",
                "name" => t(/*#ee-template-name*/ "eetmplname::apologize-fashion"),
                "tags" => ["mistake", "coupon code", "apologize",],
                "usage" => ["apologize_email"],
                "industries" => ["fashion"]
            ],
            "a-gift-daddy-will-love" => [
                "id" => "a-gift-daddy-will-love",
                "name" => t(/*#ee-template-name*/ "eetmplname::a-gift-daddy-will-love"),
                "tags" => [
                    "blue",
                    "discount",
                    "promotion",
                    "technology",
                    "father's day",
                    "electronics",
                    "vr",
                    "devices",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["computer-internet", "electronics"]
            ],
            "dont-forget-about-me" => [
                "id" => "dont-forget-about-me",
                "name" => t(/*#ee-template-name*/ "eetmplname::dont-forget-about-me"),
                "tags" => [
                    "clean",
                    "retail",
                    "modern",
                    "shoes",
                    "simple",
                    "sport",
                    "ecommerce",
                    "fashion",
                    "trainers",
                    "abandoned cart",
                    "sneakers",

                ],
                "usage" => ["abandoned_cart"],
                "industries" => ["fashion", "health-wellness", "sports"]
            ],
            "hey-you-forgot-something" => [
                "id" => "hey-you-forgot-something",
                "name" => t(/*#ee-template-name*/ "eetmplname::hey-you-forgot-something"),
                "tags" => [
                    "discount",
                    "shopping",
                    "promotion",
                    "fashion",
                    "reminder",
                    "special offer",
                    "abandoned cart",
                    "clothing",
                    "forgot to check out",
                    "shopping cart",
                    "online shopping",

                ],
                "usage" => ["abandoned_cart"],
                "industries" => ["fashion"]
            ],
            "forgot-password" => [
                "id" => "forgot-password",
                "name" => t(/*#ee-template-name*/ "eetmplname::forgot-password"),
                "tags" => [
                    "rose",
                    "illustration",
                    "texture",
                    "fun",
                    "sans serif",
                    "pattern",
                    "colorful",
                    "blue",
                    "light",
                    "playful",
                    "key",
                    "green",
                    "password",

                ],
                "usage" => ["notification", "transactional"],
                "industries" => ["others"]
            ],
            "share-your-feedback" => [
                "id" => "share-your-feedback",
                "name" => t(/*#ee-template-name*/ "eetmplname::share-your-feedback"),
                "tags" => [
                    "rose",
                    "illustration",
                    "texture",
                    "fun",
                    "sans serif",
                    "pattern",
                    "colorful",
                    "blue",
                    "light",
                    "feedback",
                    "playful",
                    "green",

                ],
                "usage" => ["notification", "transactional"],
                "industries" => ["others"]
            ],
            "fashion-store" => [
                "id" => "fashion-store",
                "name" => t(/*#ee-template-name*/ "eetmplname::fashion-store"),
                "tags" => [
                    "store",
                    "discount",
                    "ecommerce",
                    "fashion",
                    "abandoned cart",
                    "clothing",
                    "carta",
                    "abandoned",
                    "recovery",

                ],
                "usage" => ["product-promotion", "abandoned_cart"],
                "industries" => ["fashion"]
            ],
            "birthday-candle" => [
                "id" => "birthday-candle",
                "name" => t(/*#ee-template-name*/ "eetmplname::birthday-candle"),
                "tags" => [
                    "photo",
                    "blue",
                    "birthday",
                    "gift",
                    "discount",
                    "seasonal",
                    "gif",
                    "event",
                    "party",
                    "dark",
                    "surprise",
                    "cupcake",
                    "special",
                    "candle",
                    "promotional",
                    "simple",
                    "appreciation",
                    "cake",
                    "celebrate",
                    "clean",
                    "present",
                    "special offer",
                    "black",
                    "animated",

                ],
                "usage" => ["events", "personal-note", "mystery_email"],
                "industries" => ["others"]
            ],
            "let-them-eat-cake" => [
                "id" => "let-them-eat-cake",
                "name" => t(/*#ee-template-name*/ "eetmplname::let-them-eat-cake"),
                "tags" => [
                    "birthday",
                    "promotion",
                    "special offer",
                    "discount code",
                    "perks",
                    "loyalty",
                    "customer appreciation",

                ],
                "usage" => ["events", "personal-note", "mystery_email"],
                "industries" => ["others"]
            ],
            "still-shopping" => [
                "id" => "still-shopping",
                "name" => t(/*#ee-template-name*/ "eetmplname::still-shopping"),
                "tags" => [
                    "colorful",
                    "shopping",
                    "promotion",
                    "fashion",
                    "special offer",
                    "abandoned cart",
                    "clothing",
                    "shopping cart",
                    "online shopping",
                    "webshop",

                ],
                "usage" => ["transactional", "abandoned_cart"],
                "industries" => ["fashion"]
            ],
            "lucky-you" => [
                "id" => "lucky-you",
                "name" => t(/*#ee-template-name*/ "eetmplname::lucky-you"),
                "tags" => [
                    "shop",
                    "pink",
                    "store",
                    "ecommerce",
                    "violet",
                    "fashion",
                    "e-commerce",
                    "abandoned cart",
                    "tshirt",

                ],
                "usage" => ["abandoned_cart"],
                "industries" => ["fashion"]
            ],
            "we-help-brands-connect-with-people" => [
                "id" => "we-help-brands-connect-with-people",
                "name" => t(/*#ee-template-name*/ "eetmplname::we-help-brands-connect-with-people"),
                "tags" => [
                    "minimal",
                    "modern",
                    "design",
                    "professional",
                    "marketing",
                    "introduction",
                    "monochrome",
                    "business services",
                    "work with us",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["marketing_and_design"]
            ],
            "interior-design-studio" => [
                "id" => "interior-design-studio",
                "name" => t(/*#ee-template-name*/ "eetmplname::interior-design-studio"),
                "tags" => [
                    "minimal",
                    "elegant",
                    "black&white",
                    "design",
                    "boxed",
                    "studio",
                    "architecture",
                    "interior design",
                    "lines",
                    "interior",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["marketing_and_design"]
            ],
            "happy-birthday-baloons" => [
                "id" => "happy-birthday-baloons",
                "name" => t(/*#ee-template-name*/ "eetmplname::happy-birthday-baloons"),
                "tags" => [
                    "happy birthday",
                    "black friday",
                    "promotion",
                    "violet",
                    "celebration",
                    "balloon",
                    "special offer",
                    "discount code",
                    "loyalty",
                    "customer appreciation",
                    "glamour",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["others"]
            ],
            "interior-design-company" => [
                "id" => "interior-design-company",
                "name" => t(/*#ee-template-name*/ "eetmplname::interior-design-company"),
                "tags" => [
                    "architecture",
                    "white",
                    "minimalistic",
                    "serif",
                    "black",
                    "photo",
                    "contact us",
                    "bold",
                    "sans serif",
                    "design",
                    "edgy",
                    "interior design",
                    "dark",
                    "minimal",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["marketing_and_design"]
            ],
            "marketing-agency" => [
                "id" => "marketing-agency",
                "name" => t(/*#ee-template-name*/ "eetmplname::marketing-agency"),
                "tags" => [
                    "business",
                    "simple",
                    "portfolio",
                    "design",
                    "agency",
                    "company",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["marketing_and_design"]
            ],
            "best-ideas" => [
                "id" => "best-ideas",
                "name" => t(/*#ee-template-name*/ "eetmplname::best-ideas"),
                "tags" => [
                    "blue",
                    "business",
                    "simple",
                    "portfolio",
                    "design",
                    "agency",
                    "company",
                    "services",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["marketing_and_design"]
            ],
            "your-special-birthday-bonus" => [
                "id" => "your-special-birthday-bonus",
                "name" => t(/*#ee-template-name*/ "eetmplname::your-special-birthday-bonus"),
                "tags" => [
                    "birthday",
                    "discount",
                    "celebration",
                    "special offer",
                    "education",
                    "cake",
                    "educational courses",
                    "online courses",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["education"]
            ],
            "we-love-a-good-birthday" => [
                "id" => "we-love-a-good-birthday",
                "name" => t(/*#ee-template-name*/ "eetmplname::we-love-a-good-birthday"),
                "tags" => [
                    "birthday",
                    "discount",
                    "celebration",
                    "special offer",
                    "education",
                    "educational courses",
                    "online courses",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["education", "others"]
            ],
            "happy-red-white-sale-day" => [
                "id" => "happy-red-white-sale-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::happy-red-white-sale-day"),
                "tags" => [
                    "holiday",
                    "promotion",
                    "sale",
                    "celebrations",
                    "special offer",
                    "independence day",
                    "4th of july",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["others"]
            ],
            "financial" => [
                "id" => "financial",
                "name" => t(/*#ee-template-name*/ "eetmplname::financial"),
                "tags" => [
                    "business",
                    "simple",
                    "money",
                    "finance",
                    "bank",
                    "loan",

                ],
                "usage" => ["product-promotion"],
                "industries" => ["business-services", "others", "financial-money"]
            ],
            "independence-day-sale" => [
                "id" => "independence-day-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::independence-day-sale"),
                "tags" => [
                    "seasonal promotion",
                    "fashion",
                    "special offer",
                    "ecommerce",
                    "sale",
                    "festive",
                    "4th of july",
                    "stars and stripes",
                    "usa",
                    "blue red white",
                    "events",
                    "independence day",
                    "celebration",
                    "colourful",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "happy-video-games-day" => [
                "id" => "happy-video-games-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::happy-video-games-day"),
                "tags" => [
                    "modern",
                    "tech",
                    "gaming",
                    "glitch effect",
                    "computer games",
                    "dark mode",
                    "videogames",
                    "cyber",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["computer-internet", "media-entertainment"]
            ],
            "game-on" => [
                "id" => "game-on",
                "name" => t(/*#ee-template-name*/ "eetmplname::game-on"),
                "tags" => [
                    "gaming",
                    "special offer",
                    "videogames",
                    "gamer",
                    "top 10",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "news-blog-and-magazines"
                ]
            ],
            "level-up-your-gaming" => [
                "id" => "level-up-your-gaming",
                "name" => t(/*#ee-template-name*/ "eetmplname::level-up-your-gaming"),
                "tags" => [
                    "event",
                    "gaming",
                    "special offer",
                    "videogames",
                    "gamer",

                ],
                "usage" => ["events"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "news-blog-and-magazines"
                ]
            ],
            "agency-template" => [
                "id" => "agency-template",
                "name" => t(/*#ee-template-name*/ "eetmplname::agency-template"),
                "tags" => [
                    "meet the team",
                    "marketer",
                    "glow",
                    "brands",
                    "light",
                    "team",
                    "marketing",
                    "branding",
                    "introduction",
                    "innovation",
                    "strategic",
                    "designer",
                    "work with us",
                    "design",
                    "agency",
                    "minimal",
                    "services",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services", "others", "marketing_and_design"]
            ],
            "fundraising-non-profit" => [
                "id" => "fundraising-non-profit",
                "name" => t(/*#ee-template-name*/ "eetmplname::fundraising-non-profit"),
                "tags" => [
                    "simple",
                    "children",
                    "donation",
                    "kids",
                    "fundraising",
                    "volunteer",
                    "volunteering",

                ],
                "usage" => ["events"],
                "industries" => ["non-profit"]
            ],
            "charity-donation" => [
                "id" => "charity-donation",
                "name" => t(/*#ee-template-name*/ "eetmplname::charity-donation"),
                "tags" => [
                    "children",
                    "non-profit",
                    "fundraising",
                    "volunteer",
                    "volunteering",

                ],
                "usage" => ["events"],
                "industries" => ["non-profit"]
            ],
            "fundraising-initiative" => [
                "id" => "fundraising-initiative",
                "name" => t(/*#ee-template-name*/ "eetmplname::fundraising-initiative"),
                "tags" => [
                    "children",
                    "non-profit",
                    "fundraising",
                    "volunteer",
                    "volunteering",

                ],
                "usage" => ["events"],
                "industries" => ["non-profit"]
            ],
            "celebrate-freedom" => [
                "id" => "celebrate-freedom",
                "name" => t(/*#ee-template-name*/ "eetmplname::celebrate-freedom"),
                "tags" => [
                    "hotel",
                    "travel",
                    "agency",
                    "special offer",
                    "independence day",
                    "4th of july",
                    "traveling",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "4th-of-july-2" => [
                "id" => "4th-of-july-2",
                "name" => t(/*#ee-template-name*/ "eetmplname::4th-of-july-2"),
                "tags" => [
                    "travel",
                    "agency",
                    "special offer",
                    "independence day",
                    "4th of july",
                    "traveling",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "online-courses" => [
                "id" => "online-courses",
                "name" => t(/*#ee-template-name*/ "eetmplname::online-courses"),
                "tags" => [
                    "simple",
                    "course",
                    "book",
                    "udemy",
                    "enroll",
                    "courses",
                    "books",
                    "online courses",
                    "study",
                    "learn",
                    "school",
                    "coursera",
                    "education",

                ],
                "usage" => ["newsletter", "service-promotion"],
                "industries" => ["education"]
            ],
            "rent-a-car" => [
                "id" => "rent-a-car",
                "name" => t(/*#ee-template-name*/ "eetmplname::rent-a-car"),
                "tags" => [
                    "blue",
                    "simple",
                    "car",
                    "automotive",
                    "car dealer",
                    "auto",
                    "rent a car",
                    "car rental",
                    "fleet",
                    "cars",

                ],
                "usage" => ["newsletter", "service-promotion"],
                "industries" => ["automotive"]
            ],
            "emojis-speak-louder-than-words" => [
                "id" => "emojis-speak-louder-than-words",
                "name" => t(/*#ee-template-name*/ "eetmplname::emojis-speak-louder-than-words"),
                "tags" => [
                    "discount",
                    "shopping",
                    "celebration",
                    "emoji",
                    "special offer",
                    "clothing",
                    "emoji world day",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["fashion", "others"]
            ],
            "emoji-world-day-email-template" => [
                "id" => "emoji-world-day-email-template",
                "name" => t(/*#ee-template-name*/ "eetmplname::emoji-world-day-email-template"),
                "tags" => [
                    "purple",
                    "discount",
                    "fashion",
                    "emoji",
                    "special offer",
                    "clothing",
                    "emoji world day",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "happy-emoji-day-sale" => [
                "id" => "happy-emoji-day-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::happy-emoji-day-sale"),
                "tags" => [
                    "shop",
                    "sale",
                    "celebration",
                    "colourful",
                    "flash sale",
                    "special offer",
                    "blue red white",
                    "happy",
                    "emoji day",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["others"]
            ],
            "independence-day-trip" => [
                "id" => "independence-day-trip",
                "name" => t(/*#ee-template-name*/ "eetmplname::independence-day-trip"),
                "tags" => [
                    "sale",
                    "travel",
                    "explore",
                    "celebrate",
                    "adventure",
                    "4th of july",
                    "independence",
                    "destinations",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "thanks-for-signing-up" => [
                "id" => "thanks-for-signing-up",
                "name" => t(/*#ee-template-name*/ "eetmplname::thanks-for-signing-up"),
                "tags" => [
                    "gift",
                    "thank you",
                    "discount",
                    "welcome",
                    "sale",
                    "coupon code",

                ],
                "usage" => ["transactional", "thank-you", "welcome"],
                "industries" => ["fashion"]
            ],
            "thank-you-for-your-purchase" => [
                "id" => "thank-you-for-your-purchase",
                "name" => t(/*#ee-template-name*/ "eetmplname::thank-you-for-your-purchase"),
                "tags" => [
                    "store",
                    "invoice",
                    "ecommerce",
                    "shopping",
                    "e-commerce",
                    "special offer",
                    "online shopping",
                    "webshop",
                    "thank you email",
                    "purchase confirmation",
                    "purchase",

                ],
                "usage" => ["thank-you", "welcome"],
                "industries" => ["fashion"]
            ],
            "thank-you-email" => [
                "id" => "thank-you-email",
                "name" => t(/*#ee-template-name*/ "eetmplname::thank-you-email"),
                "tags" => [
                    "thank you",
                    "coupon",
                    "welcome",
                    "sale",
                    "e-commerce",
                    "order",
                    "purchase confirmation",
                    "receipt",

                ],
                "usage" => ["service-promotion", "thank-you"],
                "industries" => ["food-beverage"]
            ],
            "book-a-music-lesson" => [
                "id" => "book-a-music-lesson",
                "name" => t(/*#ee-template-name*/ "eetmplname::book-a-music-lesson"),
                "tags" => [
                    "booking",
                    "music",
                    "school",
                    "meeting",
                    "lesson",
                    "appointment",
                    "schedule",
                    "notes",
                    "guitar",
                    "scheduling",

                ],
                "usage" => ["events", "notification", "service-promotion"],
                "industries" => ["media-entertainment"]
            ],
            "day-spa-reservation" => [
                "id" => "day-spa-reservation",
                "name" => t(/*#ee-template-name*/ "eetmplname::day-spa-reservation"),
                "tags" => [
                    "relax",
                    "massage",
                    "purple",
                    "appointment",
                    "welness",
                    "health",
                    "flower",
                    "elegant",
                    "spa",
                    "confirmation",
                    "reservation",
                    "green",
                    "booking",
                    "treatments",

                ],
                "usage" => ["notification", "thank-you"],
                "industries" => ["health-wellness"]
            ],
            "thank-you-for-subscribing" => [
                "id" => "thank-you-for-subscribing",
                "name" => t(/*#ee-template-name*/ "eetmplname::thank-you-for-subscribing"),
                "tags" => [
                    "pink",
                    "thank you",
                    "software",
                    "app",
                    "welcome email",
                    "welcome",
                    "get started",
                    "thank you email",
                    "process",

                ],
                "usage" => ["notification", "thank-you", "welcome"],
                "industries" => ["computer-internet"]
            ],
            "back-to-school-sale" => [
                "id" => "back-to-school-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::back-to-school-sale"),
                "tags" => [
                    "discount",
                    "children",
                    "kids",
                    "colourful",
                    "flash sale",
                    "education",
                    "bright",
                    "back to school",
                    "stationery",
                    "supplies",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["education"]
            ],
            "back-to-school-discount" => [
                "id" => "back-to-school-discount",
                "name" => t(/*#ee-template-name*/ "eetmplname::back-to-school-discount"),
                "tags" => [
                    "discount",
                    "children",
                    "learning",
                    "kids",
                    "sale",
                    "simple",
                    "colorful",
                    "back to school",
                    "promotion",
                    "school",
                    "education",
                    "last chance",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["education"]
            ],
            "happy-book-lovers-day" => [
                "id" => "happy-book-lovers-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::happy-book-lovers-day"),
                "tags" => [
                    "modern",
                    "discount",
                    "ecommerce",
                    "sale",
                    "book lovers day",
                    "reading",
                    "bookshop",
                    "reviews",
                    "bookstore",
                    "retail",
                    "books",
                    "quotes",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["news-blog-and-magazines", "others"]
            ],
            "book-lovers-day-2020" => [
                "id" => "book-lovers-day-2020",
                "name" => t(/*#ee-template-name*/ "eetmplname::book-lovers-day-2020"),
                "tags" => [],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["news-blog-and-magazines", "others"]
            ],
            "book-lovers" => [
                "id" => "book-lovers",
                "name" => t(/*#ee-template-name*/ "eetmplname::book-lovers"),
                "tags" => [
                    "platform",
                    "ebooks",
                    "holiday",
                    "ecommerce",
                    "books",
                    "book lovers day",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => [
                    "media-entertainment",
                    "news-blog-and-magazines",
                    "others"
                ]
            ],
            "take-a-look-read-a-book" => [
                "id" => "take-a-look-read-a-book",
                "name" => t(/*#ee-template-name*/ "eetmplname::take-a-look-read-a-book"),
                "tags" => [
                    "book",
                    "design",
                    "books",
                    "book lovers day",
                    "ebooks",
                    "book store",
                    "guide",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => [
                    "media-entertainment",
                    "news-blog-and-magazines",
                    "others"
                ]
            ],
            "back-to-class-and-looking-cool" => [
                "id" => "back-to-class-and-looking-cool",
                "name" => t(/*#ee-template-name*/ "eetmplname::back-to-class-and-looking-cool"),
                "tags" => [
                    "discount",
                    "coupon",
                    "promotion",
                    "sale",
                    "devices",
                    "back to school",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["education"]
            ],
            "back-to-school-discount-code" => [
                "id" => "back-to-school-discount-code",
                "name" => t(/*#ee-template-name*/ "eetmplname::back-to-school-discount-code"),
                "tags" => [
                    "colorful",
                    "sales",
                    "school",
                    "promotion",
                    "illustration",
                    "backpack",
                    "activities",
                    "discount code",
                    "back to school",
                    "special day",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["education"]
            ],
            "room-reservation" => [
                "id" => "room-reservation",
                "name" => t(/*#ee-template-name*/ "eetmplname::room-reservation"),
                "tags" => [
                    "booking",
                    "reservation",
                    "joyful",
                    "customer appreciation",
                    "special day",
                    "room",
                    "trip",

                ],
                "usage" => ["notification", "transactional"],
                "industries" => ["real-estate", "travel-leisure"]
            ],
            "gdpr" => [
                "id" => "gdpr",
                "name" => t(/*#ee-template-name*/ "eetmplname::gdpr"),
                "tags" => [
                    "subscription",
                    "simple",
                    "updates",
                    "gaming",
                    "privacy",
                    "terms",
                    "terms of service",
                    "gdpr",
                    "conditions",

                ],
                "usage" => ["gdpr"],
                "industries" => ["media-entertainment", "others"]
            ],
            "learning-without-limits" => [
                "id" => "learning-without-limits",
                "name" => t(/*#ee-template-name*/ "eetmplname::learning-without-limits"),
                "tags" => [
                    "business",
                    "invitation",
                    "webinar",
                    "learning",
                    "event",
                    "registration",
                    "conference",
                    "on-line",
                    "virtual",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["education", "others"]
            ],
            "flight-confirmation" => [
                "id" => "flight-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::flight-confirmation"),
                "tags" => [
                    "ticket",
                    "confirmation",
                    "reservation",
                    "travel",
                    "airplane",
                    "flight confirmation",
                    "flight",

                ],
                "usage" => ["notification"],
                "industries" => ["travel-leisure"]
            ],
            "opt-in-again" => [
                "id" => "opt-in-again",
                "name" => t(/*#ee-template-name*/ "eetmplname::opt-in-again"),
                "tags" => [
                    "subscription",
                    "creative",
                    "terms of service",
                    "gdpr",
                    "resubscribe",
                    "opt in",
                    "subscribe",

                ],
                "usage" => ["notification", "gdpr"],
                "industries" => ["others"]
            ],
            "minimalist-room-confirmation" => [
                "id" => "minimalist-room-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::minimalist-room-confirmation"),
                "tags" => [
                    "minimal",
                    "confirmation",
                    "hotel",
                    "reservation",
                    "travel",
                    "minimalist",
                    "room",

                ],
                "usage" => ["notification"],
                "industries" => ["travel-leisure"]
            ],
            "join-our-webinar-event" => [
                "id" => "join-our-webinar-event",
                "name" => t(/*#ee-template-name*/ "eetmplname::join-our-webinar-event"),
                "tags" => [
                    "business",
                    "invitation",
                    "webinar",
                    "learning",
                    "event",
                    "conference",
                    "meeting",
                    "education",
                    "virtual",
                    "others",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["computer-internet", "education"]
            ],
            "register-to-our-webinar" => [
                "id" => "register-to-our-webinar",
                "name" => t(/*#ee-template-name*/ "eetmplname::register-to-our-webinar"),
                "tags" => [
                    "business",
                    "invitation",
                    "webinar",
                    "learning",
                    "event",
                    "conference",
                    "meeting",
                    "education",
                    "virtual",
                    "others",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["computer-internet", "education"]
            ],
            "next-webinar-in-2-days" => [
                "id" => "next-webinar-in-2-days",
                "name" => t(/*#ee-template-name*/ "eetmplname::next-webinar-in-2-days"),
                "tags" => [
                    "virtual",
                    "learning",
                    "speakers",
                    "meeting",
                    "webinar",
                    "event",
                    "invitation",
                    "conference",
                    "business",
                    "month",
                    "agenda",
                    "calendar",
                    "education",
                    "others",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["computer-internet", "education"]
            ],
            "webinar-invite" => [
                "id" => "webinar-invite",
                "name" => t(/*#ee-template-name*/ "eetmplname::webinar-invite"),
                "tags" => [
                    "invite",
                    "webinar",
                    "events",
                    "education",
                    "virtual",
                    "online business",
                    "online",
                    "business growth",

                ],
                "usage" => ["service-promotion"],
                "industries" => [
                    "computer-internet",
                    "education",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "flight-details" => [
                "id" => "flight-details",
                "name" => t(/*#ee-template-name*/ "eetmplname::flight-details"),
                "tags" => [
                    "booking",
                    "airplane",
                    "flight confirmation",
                    "airline",
                    "check-in",

                ],
                "usage" => ["notification", "transactional"],
                "industries" => ["travel-leisure"]
            ],
            "your-flight-confirmation" => [
                "id" => "your-flight-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::your-flight-confirmation"),
                "tags" => [
                    "booking",
                    "airplane",
                    "flight confirmation",
                    "airline",
                    "check-in",

                ],
                "usage" => ["notification", "transactional"],
                "industries" => ["travel-leisure"]
            ],
            "all-in-for-equality" => [
                "id" => "all-in-for-equality",
                "name" => t(/*#ee-template-name*/ "eetmplname::all-in-for-equality"),
                "tags" => [
                    "business",
                    "promotion",
                    "prize",
                    "women",
                    "celebrations",
                    "contest",
                    "equality",
                    "women's equality day",
                    "women's rights",
                    "giveaway",
                    "special event",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["fashion", "news-blog-and-magazines"]
            ],
            "all-your-music-in-one-place" => [
                "id" => "all-your-music-in-one-place",
                "name" => t(/*#ee-template-name*/ "eetmplname::all-your-music-in-one-place"),
                "tags" => [
                    "modern",
                    "subscription",
                    "music",
                    "service promotion",
                    "dark mode",
                    "audiophiles",
                    "music app",
                    "soft light",
                    "app promotion",
                    "app launch",
                    "neon",

                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet", "media-entertainment"]
            ],
            "labor-yay-sale" => [
                "id" => "labor-yay-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::labor-yay-sale"),
                "tags" => [
                    "orange",
                    "sales",
                    "holiday",
                    "promotion",
                    "seasonal",
                    "sale",
                    "code",
                    "discount code",
                    "labor",
                    "labour",
                    "labor day",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["health-wellness", "others", "cosmetics"]
            ],
            "green-app" => [
                "id" => "green-app",
                "name" => t(/*#ee-template-name*/ "eetmplname::green-app"),
                "tags" => [
                    "blue",
                    "green",
                    "subscription",
                    "download",
                    "application",
                    "app",
                    "app launch",
                    "new app",
                    "app store",
                    "tablet",

                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet", "others"]
            ],
            "fresh-out-of-the-box" => [
                "id" => "fresh-out-of-the-box",
                "name" => t(/*#ee-template-name*/ "eetmplname::fresh-out-of-the-box"),
                "tags" => [
                    "subscription",
                    "promote",
                    "application",
                    "app",
                    "app launch",
                    "app store",

                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet", "media-entertainment", "others"]
            ],
            "online-school-presentation" => [
                "id" => "online-school-presentation",
                "name" => t(/*#ee-template-name*/ "eetmplname::online-school-presentation"),
                "tags" => [
                    "school",
                    "course",
                    "learning",
                    "learn",
                    "e-learning",
                    "online course",

                ],
                "usage" => [
                    "product-promotion",
                    "service-promotion",
                    "educational_set"
                ],
                "industries" => ["education"]
            ],
            "best-app-for-quick-recipes" => [
                "id" => "best-app-for-quick-recipes",
                "name" => t(/*#ee-template-name*/ "eetmplname::best-app-for-quick-recipes"),
                "tags" => [
                    "cooking",
                    "list",
                    "pricing table",
                    "product launch",
                    "app",
                    "subscription",
                    "app launch",
                    "food",
                    "features",
                    "video",
                    "recipes",
                    "promotion",
                    "business",
                    "others",

                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet", "food-beverage"]
            ],
            "online-courses-app" => [
                "id" => "online-courses-app",
                "name" => t(/*#ee-template-name*/ "eetmplname::online-courses-app"),
                "tags" => [
                    "school",
                    "course",
                    "learning",
                    "learn",
                    "e-learning",
                    "online course",

                ],
                "usage" => ["educational_set"],
                "industries" => ["education"]
            ],
            "labor-day-weekend-is-coming-soon" => [
                "id" => "labor-day-weekend-is-coming-soon",
                "name" => t(/*#ee-template-name*/ "eetmplname::labor-day-weekend-is-coming-soon"),
                "tags" => [
                    "music",
                    "sale",
                    "electronics",
                    "discount code",
                    "labor day",
                    "discounts",
                    "instruments",
                    "labour day",
                    "offer",

                ],
                "usage" => ["product-promotion", "seasonal-promotion"],
                "industries" => ["media-entertainment", "electronics"]
            ],
            "enrollment-confirmation" => [
                "id" => "enrollment-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::enrollment-confirmation"),
                "tags" => [
                    "school",
                    "course",
                    "learning",
                    "learn",
                    "e-learning",
                    "online course",

                ],
                "usage" => ["notification", "transactional", "educational_set"],
                "industries" => ["education"]
            ],
            "the-greatest-app-ever" => [
                "id" => "the-greatest-app-ever",
                "name" => t(/*#ee-template-name*/ "eetmplname::the-greatest-app-ever"),
                "tags" => [
                    "testimonials",
                    "subscription",
                    "tracking",
                    "promotion",
                    "app",
                    "sports",
                    "features",
                    "product launch",
                    "health",
                    "others",
                    "app launch",

                ],
                "usage" => ["product-launch", "product-promotion"],
                "industries" => ["computer-internet"]
            ],
            "e-learning-blog" => [
                "id" => "e-learning-blog",
                "name" => t(/*#ee-template-name*/ "eetmplname::e-learning-blog"),
                "tags" => [
                    "school",
                    "course",
                    "learning",
                    "blog",
                    "learn",
                    "e-learning",
                    "online course",
                    "post",
                    "article",

                ],
                "usage" => ["educational_set"],
                "industries" => ["education"]
            ],
            "browse-by-categories" => [
                "id" => "browse-by-categories",
                "name" => t(/*#ee-template-name*/ "eetmplname::browse-by-categories"),
                "tags" => [
                    "school",
                    "course",
                    "learning",
                    "learn",
                    "e-learning",
                    "online course",

                ],
                "usage" => ["educational_set"],
                "industries" => ["education"]
            ],
            "labor-day-sale-sale-sale" => [
                "id" => "labor-day-sale-sale-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::labor-day-sale-sale-sale"),
                "tags" => [
                    "sales",
                    "promotion",
                    "sale",
                    "electronics",
                    "discount code",
                    "others",
                    "labor",
                    "labor day",
                    "discounts",
                    "labour day",

                ],
                "usage" => ["product-promotion", "seasonal-promotion"],
                "industries" => ["electronics"]
            ],
            "women-equality-day" => [
                "id" => "women-equality-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::women-equality-day"),
                "tags" => [
                    "modern",
                    "magazine",
                    "blog",
                    "support",
                    "neutral",
                    "equality",
                    "article",
                    "women for women",
                    "women's equality day",
                    "fundraising",
                    "women's rights",

                ],
                "usage" => ["events", "newsletter"],
                "industries" => ["news-blog-and-magazines"]
            ],
            "job-finder" => [
                "id" => "job-finder",
                "name" => t(/*#ee-template-name*/ "eetmplname::job-finder"),
                "tags" => [
                    "job",
                    "work",
                    "listing",
                    "hire",
                    "position",
                    "opportunity",
                    "job posting",
                    "hiring",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["others", "hr"]
            ],
            "purple-account-activation" => [
                "id" => "purple-account-activation",
                "name" => t(/*#ee-template-name*/ "eetmplname::purple-account-activation"),
                "tags" => [
                    "purple",
                    "confirmation",
                    "notification",
                    "download",
                    "app",
                    "activation",
                    "finance",
                    "receipt",
                    "account",

                ],
                "usage" => ["notification", "transactional", "download", "activation"],
                "industries" => ["computer-internet"]
            ],
            "restaurant-reservation-confirmation" => [
                "id" => "restaurant-reservation-confirmation",
                "name" => t(/*#ee-template-name*/ "eetmplname::restaurant-reservation-confirmation"),
                "tags" => [
                    "notification",
                    "reservation",
                    "transactional",
                    "food",
                    "restaurant",

                ],
                "usage" => ["notification", "transactional", "confirmation"],
                "industries" => ["food-beverage", "travel-leisure"]
            ],
            "membership-activation" => [
                "id" => "membership-activation",
                "name" => t(/*#ee-template-name*/ "eetmplname::membership-activation"),
                "tags" => [
                    "blue",
                    "confirmation",
                    "notification",
                    "music",
                    "activation",
                    "electronic",
                    "account",
                    "club",
                    "sound",
                    "membership",

                ],
                "usage" => ["events", "notification", "activation", "confirmation"],
                "industries" => ["media-entertainment"]
            ],
            "wild-blog" => [
                "id" => "wild-blog",
                "name" => t(/*#ee-template-name*/ "eetmplname::wild-blog"),
                "tags" => [],
                "usage" => ["newsletter"],
                "industries" => ["news-blog-and-magazines", "pets_and_animal_care"]
            ],
            "labor-day-savings" => [
                "id" => "labor-day-savings",
                "name" => t(/*#ee-template-name*/ "eetmplname::labor-day-savings"),
                "tags" => [
                    "deal",
                    "holiday",
                    "promotional",
                    "automotive",
                    "cars",
                    "labor day",
                    "savings",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["automotive"]
            ],
            "its-womans-equality-day" => [
                "id" => "its-womans-equality-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::its-womans-equality-day"),
                "tags" => [
                    "blog",
                    "discounts",
                    "women",
                    "skin products",
                    "women's equality day",
                    "promo code",
                    "event related promotion",
                    "girls",
                    "discount code",
                    "seasonal offer",
                    "video",
                    "products",
                    "promotion",
                    "store",
                    "others",

                ],
                "usage" => ["events", "seasonal-promotion"],
                "industries" => ["others"]
            ],
            "fall-in-love-with-this-seasons-collection" => [
                "id" => "fall-in-love-with-this-seasons-collection",
                "name" => t(/*#ee-template-name*/ "eetmplname::fall-in-love-with-this-seasons-collection"),
                "tags" => [
                    "autumn",
                    "seasonal promotion",
                    "interior design",
                    "furniture",
                    "new collection",
                    "gradients",
                    "fall",
                    "home decor",
                    "fall colours",
                    "warm",

                ],
                "usage" => [
                    "product-launch",
                    "product-promotion",
                    "seasonal-promotion"
                ],
                "industries" => ["manufacturing", "others", "marketing_and_design"]
            ],
            "pet-shop-labor-day-offer" => [
                "id" => "pet-shop-labor-day-offer",
                "name" => t(/*#ee-template-name*/ "eetmplname::pet-shop-labor-day-offer"),
                "tags" => [
                    "dogs",
                    "pets",
                    "cats",
                    "pet shop",
                    "pet food",
                    "labor day",
                    "pet toy",
                    "treats",

                ],
                "usage" => ["product-promotion", "seasonal-promotion"],
                "industries" => ["pets_and_animal_care"]
            ],
            "local-services-promotion" => [
                "id" => "local-services-promotion",
                "name" => t(/*#ee-template-name*/ "eetmplname::local-services-promotion"),
                "tags" => [
                    "platform",
                    "small business",
                    "professional",
                    "services",
                    "coronavirus",
                    "minimalist",
                    "emergency",
                    "local business",
                    "local",

                ],
                "usage" => ["service-promotion"],
                "industries" => ["business-services", "others"]
            ],
            "free-for-all" => [
                "id" => "free-for-all",
                "name" => t(/*#ee-template-name*/ "eetmplname::free-for-all"),
                "tags" => [
                    "wellness",
                    "on-line",
                    "learning",
                    "baking",
                    "interests",
                    "crafts",
                    "hobbies",
                    "classes",
                    "diy",
                    "stayinghome",
                    "courses",
                    "freebie",
                    "self-care",

                ],
                "usage" => ["newsletter", "transactional"],
                "industries" => ["computer-internet", "education"]
            ],
            "new-app-launch" => [
                "id" => "new-app-launch",
                "name" => t(/*#ee-template-name*/ "eetmplname::new-app-launch"),
                "tags" => [
                    "purple",
                    "product launch",
                    "fashion",
                    "ecommerce",
                    "app launch",
                    "app promotion",
                    "app features",
                    "new product",
                    "app",
                    "retail",
                    "coral",
                    "shopping",

                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet", "media-entertainment"]
            ],
            "brand-new-view" => [
                "id" => "brand-new-view",
                "name" => t(/*#ee-template-name*/ "eetmplname::brand-new-view"),
                "tags" => [
                    "clean",
                    "modern",
                    "pastel",
                    "new product",
                    "product launch",
                    "new collection",
                    "glasses",
                    "collection launch",
                    "coming soon",
                    "eyewear",

                ],
                "usage" => ["product-launch", "product-promotion"],
                "industries" => ["fashion", "health-wellness", "others"]
            ],
            "stay-comfortable-at-home" => [
                "id" => "stay-comfortable-at-home",
                "name" => t(/*#ee-template-name*/ "eetmplname::stay-comfortable-at-home"),
                "tags" => [
                    "marketing",
                    "coronavirus",
                    "special offer",
                    "furniture",
                    "decor",

                ],
                "usage" => ["product-promotion"],
                "industries" => [
                    "news-blog-and-magazines",
                    "others",
                    "marketing_and_design"
                ]
            ],
            "new-fall-collection-its-here" => [
                "id" => "new-fall-collection-its-here",
                "name" => t(/*#ee-template-name*/ "eetmplname::new-fall-collection-its-here"),
                "tags" => [
                    "clothes",
                    "fashion",
                    "clothing",
                    "others",
                    "event related promotion",
                    "fall",
                    "brand",
                    "models",
                    "fall collection",

                ],
                "usage" => ["newsletter", "product-promotion", "seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "fall-must-haves" => [
                "id" => "fall-must-haves",
                "name" => t(/*#ee-template-name*/ "eetmplname::fall-must-haves"),
                "tags" => [
                    "clothes",
                    "fashion",
                    "clothing",
                    "others",
                    "offer",
                    "fall",
                    "brand",
                    "fall collection",
                    "menswear",
                    "product list",

                ],
                "usage" => ["newsletter", "product-promotion", "seasonal-promotion"],
                "industries" => ["fashion"]
            ],
            "international-podcast-day" => [
                "id" => "international-podcast-day",
                "name" => t(/*#ee-template-name*/ "eetmplname::international-podcast-day"),
                "tags" => [
                    "blue",
                    "red",
                    "modern",
                    "duotone",
                    "podcast",
                    "audio",
                    "radio",
                    "on air",
                    "international podcast day",
                    "entertainment",

                ],
                "usage" => ["events", "seasonal-promotion", "service-promotion"],
                "industries" => [
                    "computer-internet",
                    "media-entertainment",
                    "news-blog-and-magazines"
                ]
            ],
            "hiring-simplified" => [
                "id" => "hiring-simplified",
                "name" => t(/*#ee-template-name*/ "eetmplname::hiring-simplified"),
                "tags" => [
                    "fresh",
                    "modern",
                    "job",
                    "job posting",
                    "recruitment",
                    "hiring software",
                    "talent",

                ],
                "usage" => ["welcome"],
                "industries" => ["hr"]
            ],
            "find-your-talent" => [
                "id" => "find-your-talent",
                "name" => t(/*#ee-template-name*/ "eetmplname::find-your-talent"),
                "tags" => [
                    "fresh",
                    "modern",
                    "job",
                    "job posting",
                    "recruitment",
                    "hiring software",
                    "talent",

                ],
                "usage" => ["welcome"],
                "industries" => ["hr"]
            ],
            "introducing-mindfulness-app" => [
                "id" => "introducing-mindfulness-app",
                "name" => t(/*#ee-template-name*/ "eetmplname::introducing-mindfulness-app"),
                "tags" => [
                    "business",
                    "meditation",
                    "app",
                    "features",
                    "product launch",
                    "relax",
                    "health",
                    "others",
                    "app launch",
                    "brand",
                    "mindfulness",
                    "calm",

                ],
                "usage" => ["product-launch"],
                "industries" => ["computer-internet"],
            ],
            "wow-its-time-to-treat-yourself" => [
                "id" => "wow-its-time-to-treat-yourself",
                "name" => t(/*#ee-template-name*/ "eetmplname::wow-its-time-to-treat-yourself"),
                "tags" => [
                    "men",
                    "promotion",
                    "clothes",
                    "fashion",
                    "welcome",
                    "kids",
                    "women",
                    "clothing",
                    "discount code",
                    "others",
                    "discounts",
                    "promo code",

                ],
                "usage" => ["welcome"],
                "industries" => ["fashion"]
            ],
            "new-podcast-new-tips" => [
                "id" => "new-podcast-new-tips",
                "name" => t(/*#ee-template-name*/ "eetmplname::new-podcast-new-tips"),
                "tags" => ["cool", "design", "colors", "podcast", "audio",],
                "usage" => ["seasonal-promotion"],
                "industries" => ["computer-internet", "media-entertainment"]
            ],
            "colorful-podcast" => [
                "id" => "colorful-podcast",
                "name" => t(/*#ee-template-name*/ "eetmplname::colorful-podcast"),
                "tags" => [
                    "cool",
                    "modern",
                    "video",
                    "design",
                    "colors",
                    "podcast",
                    "audio",

                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["computer-internet", "media-entertainment"]
            ],
            "fall-journey" => [
                "id" => "fall-journey",
                "name" => t(/*#ee-template-name*/ "eetmplname::fall-journey"),
                "tags" => [
                    "cool",
                    "travel",
                    "marketing",
                    "travel agency",
                    "traveling",
                    "fall traveling",
                    "vacation",
                    "fall season travels",

                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["travel-leisure"]
            ],
            "no-tricks-only-treats" => [
                "id" => "no-tricks-only-treats",
                "name" => t(/*#ee-template-name*/ "eetmplname::no-tricks-only-treats"),
                "tags" => [
                    "minimal",
                    "halloween",
                    "food",
                    "pastry",
                    "playful",
                    "treats",
                    "food delivery",

                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["food-beverage"]
            ],
            "halloween-horror-games-sale" => [
                "id" => "halloween-horror-games-sale",
                "name" => t(/*#ee-template-name*/ "eetmplname::halloween-horror-games-sale"),
                "tags" => [
                    "halloween",
                    "tech",
                    "sale",
                    "gaming",
                    "special offer",
                    "vr",
                    "computer games",
                    "app promotion",
                    "scary",
                    "horror",
                    "game sale",

                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["media-entertainment"]
            ],
            "the-ultimate-guide-to-a-stress-free-thanksgiving" => [
                "id" => "the-ultimate-guide-to-a-stress-free-thanksgiving",
                "name" => t(/*#ee-template-name*/ "eetmplname::the-ultimate-guide-to-a-stress-free-thanksgiving"),
                "tags" => [
                    "minimal",
                    "clean",
                    "store",
                    "holiday",
                    "autumn",
                    "food",
                    "thanksgiving",
                    "cooking",
                    "turkey",
                    "chalkboard",
                    "recipe",
                    "fall",
                    "groceries",
                    "ingredients",
                    "grocery store",

                ],
                "usage" => ["seasonal-promotion"],
                "industries" => ["food-beverage"]
            ],


            /*
   * the following templates contain a lot of BEE references or unwanted content
   * Do not activate them!
   *
   */

            //      "bee-pro-updates" => [
            //        "id" => "bee-pro-updates",
            //        "name" => t(/*#ee-template-name*/"eetmplname::bee-pro-updates"),
            //        "tags" => [
            //          "purple",
            //          "cool",
            //          "subscription",
            //          "articles",
            //          "creative",
            //          "newsletter",
            //          "updates",
            //          "digest",
            //
            //        ],
            //        "usage" => ["newsletter"],
            //        "industries" => ["computer-internet"]
            //      ],
            //      "bee-pro-agency" => [
            //        "id" => "bee-pro-agency",
            //        "name" => t(/*#ee-template-name*/"eetmplname::bee-pro-agency"),
            //        "tags" => ["marketing", "agency", ],
            //        "usage" => ["product-promotion"],
            //        "industries" => ["computer-internet"]
            //      ],
            //      "saas-tutorial" => [
            //        "id" => "saas-tutorial",
            //        "name" => t(/*#ee-template-name*/"eetmplname::saas-tutorial"),
            //        "tags" => [
            //          "platform",
            //          "saas",
            //          "digital",
            //          "product",
            //          "features",
            //          "feature",
            //          "workshop",
            //          "tutorial",
            //          "service",
            //          "startup",
            //          "tool",
            //
            //        ],
            //        "usage" => ["notification"],
            //        "industries" => ["computer-internet"]
            //      ],
            //      "add-on" => [
            //        "id" => "add-on",
            //        "name" => t(/*#ee-template-name*/"eetmplname::add-on"),
            //        "tags" => [
            //          "surprise",
            //          "friends",
            //          "employee",
            //          "family",
            //          "invites",
            //          "get-together",
            //          "project",
            //          "fun",
            //          "dream job",
            //          "events",
            //          "startup",
            //          "job",
            //          "personal",
            //          "presentation",
            //          "event",
            //          "love",
            //          "team",
            //          "party",
            //          "invite",
            //          "happy birthday",
            //
            //        ],
            //        "usage" => ["product-launch"],
            //        "industries" => ["computer-internet"]
            //      ],
            //      "plugin" => [
            //        "id" => "plugin",
            //        "name" => t(/*#ee-template-name*/"eetmplname::plugin"),
            //        "tags" => [
            //          "testimonials",
            //          "platform",
            //          "saas",
            //          "digital",
            //          "illustration",
            //          "features",
            //          "drawings",
            //          "authority",
            //          "build",
            //          "embed",
            //          "featured",
            //
            //        ],
            //        "usage" => ["product-launch"],
            //        "industries" => ["computer-internet"]
            //      ],
            //      "first-steps-for-a-blogger" => [
            //        "id" => "first-steps-for-a-blogger",
            //        "name" => t(/*#ee-template-name*/"eetmplname::first-steps-for-a-blogger"),
            //        "tags" => [],
            //        "usage" => ["newsletter"],
            //        "industries" => ["news-blog-and-magazines"]
            //      ],
            //      "stop-the-spread" => [
            //        "id" => "stop-the-spread",
            //        "name" => t(/*#ee-template-name*/"eetmplname::stop-the-spread"),
            //        "tags" => [
            //          "white",
            //          "non-profit",
            //          "light",
            //          "crisis email",
            //          "minimalistic",
            //          "prevention",
            //          "protection",
            //          "news",
            //          "coronavirus",
            //          "sans serif",
            //          "light blue",
            //
            //        ],
            //        "usage" => ["events", "tutorial"],
            //        "industries" => ["health-wellness", "non-profit"]
            //      ],
            //      "support-fundraising" => [
            //        "id" => "support-fundraising",
            //        "name" => t(/*#ee-template-name*/"eetmplname::support-fundraising"),
            //        "tags" => [
            //          "white",
            //          "non-profit",
            //          "light",
            //          "crisis email",
            //          "minimalistic",
            //          "prevention",
            //          "protection",
            //          "news",
            //          "coronavirus",
            //          "sans serif",
            //          "light blue",
            //
            //        ],
            //        "usage" => ["events"],
            //        "industries" => ["health-wellness", "non-profit"]
            //      ],
        ];
    }
}

<?php

namespace App\Klicktipp\RequestSms\ValueObject;

use App\Klicktipp\AngularApi\ValueObject\Factory\MagicSelectItemFactory;
use App\Klicktipp\AngularApi\ValueObject\MagicSelectItemValueObject;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\SMSListbuildings;

final class RequestSmsEntityValueObject
{
    public int $id;
    public string $name;
    public string $type;
    /** @var array<string> */
    public array $metaLabels;
    public string $notes;
    public int $relListId;
    public ?MagicSelectItemValueObject $optInSubscribeTo;
    public string $inboundNumber;
    public string $keyword;
    public RequestSmsEntityLinksValueObject $links;

    /**
     * @param SMSListbuildings $entity
     * @param RequestSmsEntityLinksValueObject $links
     * @param array<string> $metaLabels
     * @return self
     */
    public static function create(
        SMSListbuildings $entity,
        RequestSmsEntityLinksValueObject $links,
        array $metaLabels = []
    ): self {
        $assignTagId = (int)($entity->GetData('AssignTagID') ?: 0);
        $magicSelectItem = !empty($assignTagId)
            ? MagicSelectItemFactory::create(['ids' => [$assignTagId], 'new' => []])
            : null;

        $varcharIndexed = explode(":", $entity->GetData('VarcharIndexed'));
        if (empty($varcharIndexed)) {
            $inboundNumber = '';
            $keyword = '';
        } else {
            $inboundNumber = $varcharIndexed[0] ?: '';
            $keyword = $varcharIndexed[1] ?: '';
        }

        $valueObject = new self();
        $valueObject->id = (int)$entity->GetData('BuildID');
        $valueObject->type = Listbuildings::$APIIndexFilterTypes[$entity->GetData('BuildType')];
        $valueObject->name = $entity->GetData('Name');
        $valueObject->notes = $entity->GetData('Notes');
        $valueObject->metaLabels = $metaLabels;
        $valueObject->relListId = $entity->GetData('RelListID');
        $valueObject->optInSubscribeTo = $magicSelectItem;
        $valueObject->inboundNumber = $inboundNumber;
        $valueObject->keyword = $keyword;
        $valueObject->links = $links;
        return $valueObject;
    }
}

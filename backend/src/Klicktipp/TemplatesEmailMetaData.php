<?php

namespace App\Klicktipp;

class TemplatesEmailMetaData
{
    public static function tagIndex()
    {
        //<#ee-template-tag>Tag name to filter email editor templates.
        // Do not include 'eetmpltag::' in translation' </#>

        return [
            ["id" => "light", "label" => t(/*#ee-template-tag*/ "eetmpltag::light")],
            ["id" => "white", "label" => t(/*#ee-template-tag*/ "eetmpltag::white")],
            ["id" => "red", "label" => t(/*#ee-template-tag*/ "eetmpltag::red")],
            [
                "id" => "sans serif",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::sans serif")
            ],
            ["id" => "sell", "label" => t(/*#ee-template-tag*/ "eetmpltag::sell")],
            ["id" => "new", "label" => t(/*#ee-template-tag*/ "eetmpltag::new")],
            ["id" => "blue", "label" => t(/*#ee-template-tag*/ "eetmpltag::blue")],
            [
                "id" => "light blue",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::light blue")
            ],
            ["id" => "serif", "label" => t(/*#ee-template-tag*/ "eetmpltag::serif")],
            [
                "id" => "one-column",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::one-column")
            ],
            [
                "id" => "promote",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::promote")
            ],
            [
                "id" => "dynamic countdown",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dynamic countdown")
            ],
            ["id" => "black", "label" => t(/*#ee-template-tag*/ "eetmpltag::black")],
            ["id" => "dark", "label" => t(/*#ee-template-tag*/ "eetmpltag::dark")],
            [
                "id" => "animated",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::animated")
            ],
            [
                "id" => "light grey",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::light grey")
            ],
            [
                "id" => "orange",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::orange")
            ],
            ["id" => "green", "label" => t(/*#ee-template-tag*/ "eetmpltag::green")],
            [
                "id" => "yellow",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::yellow")
            ],
            [
                "id" => "purple",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::purple")
            ],
            ["id" => "grey", "label" => t(/*#ee-template-tag*/ "eetmpltag::grey")],
            [
                "id" => "communicate",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::communicate")
            ],
            [
                "id" => "light green",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::light green")
            ],
            [
                "id" => "dark grey",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dark grey")
            ],
            [
                "id" => "communications",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::communications")
            ],
            [
                "id" => "portfolio",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::portfolio")
            ],
            [
                "id" => "fashion",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fashion")
            ],
            [
                "id" => "clothes",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::clothes")
            ],
            [
                "id" => "promotion",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::promotion")
            ],
            [
                "id" => "ecommerce",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::ecommerce")
            ],
            ["id" => "offer", "label" => t(/*#ee-template-tag*/ "eetmpltag::offer")],
            ["id" => "promo", "label" => t(/*#ee-template-tag*/ "eetmpltag::promo")],
            [
                "id" => "retail",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::retail")
            ],
            ["id" => "store", "label" => t(/*#ee-template-tag*/ "eetmpltag::store")],
            [
                "id" => "inform",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::inform")
            ],
            ["id" => "beige", "label" => t(/*#ee-template-tag*/ "eetmpltag::beige")],
            [
                "id" => "discount",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::discount")
            ],
            ["id" => "slab", "label" => t(/*#ee-template-tag*/ "eetmpltag::slab")],
            [
                "id" => "announce",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::announce")
            ],
            [
                "id" => "coupon",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::coupon")
            ],
            [
                "id" => "tracking",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::tracking")
            ],
            [
                "id" => "invoice",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::invoice")
            ],
            [
                "id" => "confirmation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::confirmation")
            ],
            [
                "id" => "survey",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::survey")
            ],
            [
                "id" => "notification",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::notification")
            ],
            [
                "id" => "retention",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::retention")
            ],
            ["id" => "brown", "label" => t(/*#ee-template-tag*/ "eetmpltag::brown")],
            ["id" => "lilac", "label" => t(/*#ee-template-tag*/ "eetmpltag::lilac")],
            ["id" => "pink", "label" => t(/*#ee-template-tag*/ "eetmpltag::pink")],
            [
                "id" => "holiday",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::holiday")
            ],
            [
                "id" => "invite",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::invite")
            ],
            [
                "id" => "greetings",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::greetings")
            ],
            [
                "id" => "launch",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::launch")
            ],
            ["id" => "book", "label" => t(/*#ee-template-tag*/ "eetmpltag::book")],
            [
                "id" => "non-animated",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::non-animated")
            ],
            [
                "id" => "handwritten",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::handwritten")
            ],
            ["id" => "borwn", "label" => t(/*#ee-template-tag*/ "eetmpltag::borwn")],
            [
                "id" => "halloween",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::halloween")
            ],
            [
                "id" => "christmas",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::christmas")
            ],
            [
                "id" => "ornaments",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::ornaments")
            ],
            [
                "id" => "fuchsia",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fuchsia")
            ],
            [
                "id" => "dark green",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dark green")
            ],
            [
                "id" => "propose",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::propose")
            ],
            [
                "id" => "download",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::download")
            ],
            [
                "id" => "notify",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::notify")
            ],
            [
                "id" => "booking",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::booking")
            ],
            [
                "id" => "book now",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::book now")
            ],
            ["id" => "hotel", "label" => t(/*#ee-template-tag*/ "eetmpltag::hotel")],
            [
                "id" => "university",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::university")
            ],
            ["id" => "team", "label" => t(/*#ee-template-tag*/ "eetmpltag::team")],
            [
                "id" => "soccer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::soccer")
            ],
            ["id" => "match", "label" => t(/*#ee-template-tag*/ "eetmpltag::match")],
            [
                "id" => "football",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::football")
            ],
            ["id" => "game", "label" => t(/*#ee-template-tag*/ "eetmpltag::game")],
            ["id" => "sport", "label" => t(/*#ee-template-tag*/ "eetmpltag::sport")],
            [
                "id" => "skating",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::skating")
            ],
            ["id" => "boy", "label" => t(/*#ee-template-tag*/ "eetmpltag::boy")],
            [
                "id" => "dating",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dating")
            ],
            ["id" => "love", "label" => t(/*#ee-template-tag*/ "eetmpltag::love")],
            ["id" => "party", "label" => t(/*#ee-template-tag*/ "eetmpltag::party")],
            [
                "id" => "donuts",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::donuts")
            ],
            [
                "id" => "invitation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::invitation")
            ],
            [
                "id" => "merchandising",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::merchandising")
            ],
            [
                "id" => "school",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::school")
            ],
            [
                "id" => "dicount",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dicount")
            ],
            [
                "id" => "watermelon",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::watermelon")
            ],
            [
                "id" => "fitness",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fitness")
            ],
            [
                "id" => "grunge",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::grunge")
            ],
            [
                "id" => "black friday",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::black friday")
            ],
            [
                "id" => "autumn",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::autumn")
            ],
            [
                "id" => "october",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::october")
            ],
            ["id" => "music", "label" => t(/*#ee-template-tag*/ "eetmpltag::music")],
            [
                "id" => "sell tickets",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::sell tickets")
            ],
            [
                "id" => "rainbow",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::rainbow")
            ],
            ["id" => "paper", "label" => t(/*#ee-template-tag*/ "eetmpltag::paper")],
            [
                "id" => "digital",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::digital")
            ],
            ["id" => "sales", "label" => t(/*#ee-template-tag*/ "eetmpltag::sales")],
            ["id" => "tech", "label" => t(/*#ee-template-tag*/ "eetmpltag::tech")],
            [
                "id" => "cyber monday",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::cyber monday")
            ],
            ["id" => "watch", "label" => t(/*#ee-template-tag*/ "eetmpltag::watch")],
            [
                "id" => "shopping",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::shopping")
            ],
            [
                "id" => "black&white",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::black&white")
            ],
            ["id" => "arrow", "label" => t(/*#ee-template-tag*/ "eetmpltag::arrow")],
            ["id" => "news", "label" => t(/*#ee-template-tag*/ "eetmpltag::news")],
            [
                "id" => "journal",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::journal")
            ],
            [
                "id" => "newspaper",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::newspaper")
            ],
            [
                "id" => "magazine",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::magazine")
            ],
            [
                "id" => "articles",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::articles")
            ],
            [
                "id" => "bedroom",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::bedroom")
            ],
            [
                "id" => "reservation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::reservation")
            ],
            ["id" => "bnb", "label" => t(/*#ee-template-tag*/ "eetmpltag::bnb")],
            [
                "id" => "learning",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::learning")
            ],
            [
                "id" => "course",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::course")
            ],
            [
                "id" => "webinar",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::webinar")
            ],
            [
                "id" => "certification",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::certification")
            ],
            [
                "id" => "academy",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::academy")
            ],
            [
                "id" => "internet",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::internet")
            ],
            ["id" => "car", "label" => t(/*#ee-template-tag*/ "eetmpltag::car")],
            [
                "id" => "rent service",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::rent service")
            ],
            [
                "id" => "rental",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::rental")
            ],
            ["id" => "rent", "label" => t(/*#ee-template-tag*/ "eetmpltag::rent")],
            ["id" => "gold", "label" => t(/*#ee-template-tag*/ "eetmpltag::gold")],
            ["id" => "gift", "label" => t(/*#ee-template-tag*/ "eetmpltag::gift")],
            ["id" => "toys", "label" => t(/*#ee-template-tag*/ "eetmpltag::toys")],
            [
                "id" => "letter",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::letter")
            ],
            [
                "id" => "new year",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::new year")
            ],
            [
                "id" => "fireworks",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fireworks")
            ],
            ["id" => "bird", "label" => t(/*#ee-template-tag*/ "eetmpltag::bird")],
            ["id" => "snow", "label" => t(/*#ee-template-tag*/ "eetmpltag::snow")],
            [
                "id" => "light-blue",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::light-blue")
            ],
            ["id" => "bot", "label" => t(/*#ee-template-tag*/ "eetmpltag::bot")],
            ["id" => "chat", "label" => t(/*#ee-template-tag*/ "eetmpltag::chat")],
            [
                "id" => "software",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::software")
            ],
            ["id" => "video", "label" => t(/*#ee-template-tag*/ "eetmpltag::video")],
            [
                "id" => "snowboard",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::snowboard")
            ],
            [
                "id" => "winter",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::winter")
            ],
            ["id" => "ski", "label" => t(/*#ee-template-tag*/ "eetmpltag::ski")],
            [
                "id" => "mountain",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mountain")
            ],
            [
                "id" => "three-columns",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::three-columns")
            ],
            [
                "id" => "hiking",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hiking")
            ],
            ["id" => "shop", "label" => t(/*#ee-template-tag*/ "eetmpltag::shop")],
            ["id" => "heart", "label" => t(/*#ee-template-tag*/ "eetmpltag::heart")],
            [
                "id" => "violet",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::violet")
            ],
            [
                "id" => "hearts",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hearts")
            ],
            [
                "id" => "seasonal",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::seasonal")
            ],
            [
                "id" => "graphics",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::graphics")
            ],
            [
                "id" => "pantone",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pantone")
            ],
            ["id" => "color", "label" => t(/*#ee-template-tag*/ "eetmpltag::color")],
            [
                "id" => "color of the year",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::color of the year")
            ],
            [
                "id" => "photography",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::photography")
            ],
            [
                "id" => "designer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::designer")
            ],
            [
                "id" => "design",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::design")
            ],
            ["id" => "coral", "label" => t(/*#ee-template-tag*/ "eetmpltag::coral")],
            ["id" => "blog", "label" => t(/*#ee-template-tag*/ "eetmpltag::blog")],
            [
                "id" => "illustration",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::illustration")
            ],
            ["id" => "trend", "label" => t(/*#ee-template-tag*/ "eetmpltag::trend")],
            [
                "id" => "mystery",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mystery")
            ],
            [
                "id" => "projects",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::projects")
            ],
            [
                "id" => "two-columns",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::two-columns")
            ],
            [
                "id" => "mobile",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mobile")
            ],
            ["id" => "app", "label" => t(/*#ee-template-tag*/ "eetmpltag::app")],
            [
                "id" => "application",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::application")
            ],
            [
                "id" => "development",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::development")
            ],
            [
                "id" => "meditation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::meditation")
            ],
            [
                "id" => "rounded",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::rounded")
            ],
            [
                "id" => "support",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::support")
            ],
            [
                "id" => "children",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::children")
            ],
            ["id" => "org", "label" => t(/*#ee-template-tag*/ "eetmpltag::org")],
            ["id" => "clean", "label" => t(/*#ee-template-tag*/ "eetmpltag::clean")],
            [
                "id" => "giving day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::giving day")
            ],
            [
                "id" => "charity",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::charity")
            ],
            [
                "id" => "no profit",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::no profit")
            ],
            [
                "id" => "four-columns",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::four-columns")
            ],
            ["id" => "prize", "label" => t(/*#ee-template-tag*/ "eetmpltag::prize")],
            [
                "id" => "gift card",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::gift card")
            ],
            ["id" => "mask", "label" => t(/*#ee-template-tag*/ "eetmpltag::mask")],
            ["id" => "event", "label" => t(/*#ee-template-tag*/ "eetmpltag::event")],
            ["id" => "dj", "label" => t(/*#ee-template-tag*/ "eetmpltag::dj")],
            [
                "id" => "carnival",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::carnival")
            ],
            [
                "id" => "parade",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::parade")
            ],
            [
                "id" => "costume",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::costume")
            ],
            [
                "id" => "mardi gras",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mardi gras")
            ],
            [
                "id" => "time sensitive",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::time sensitive")
            ],
            [
                "id" => "urgency",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::urgency")
            ],
            [
                "id" => "registration",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::registration")
            ],
            [
                "id" => "activation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::activation")
            ],
            [
                "id" => "username",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::username")
            ],
            ["id" => "gym", "label" => t(/*#ee-template-tag*/ "eetmpltag::gym")],
            [
                "id" => "sports",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::sports")
            ],
            [
                "id" => "question",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::question")
            ],
            [
                "id" => "series",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::series")
            ],
            [
                "id" => "workflow",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::workflow")
            ],
            [
                "id" => "welcome",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::welcome")
            ],
            [
                "id" => "automation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::automation")
            ],
            [
                "id" => "welcome email",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::welcome email")
            ],
            [
                "id" => "easter",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::easter")
            ],
            ["id" => "sale", "label" => t(/*#ee-template-tag*/ "eetmpltag::sale")],
            [
                "id" => "gadget",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::gadget")
            ],
            ["id" => "bunny", "label" => t(/*#ee-template-tag*/ "eetmpltag::bunny")],
            [
                "id" => "creative",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::creative")
            ],
            [
                "id" => "duotone",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::duotone")
            ],
            [
                "id" => "quotes",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::quotes")
            ],
            [
                "id" => "square",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::square")
            ],
            [
                "id" => "squared",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::squared")
            ],
            [
                "id" => "colors",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::colors")
            ],
            [
                "id" => "newsletter",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::newsletter")
            ],
            [
                "id" => "typography",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::typography")
            ],
            [
                "id" => "simple",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::simple")
            ],
            ["id" => "spa", "label" => t(/*#ee-template-tag*/ "eetmpltag::spa")],
            ["id" => "soft", "label" => t(/*#ee-template-tag*/ "eetmpltag::soft")],
            [
                "id" => "wellness",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::wellness")
            ],
            [
                "id" => "resort",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::resort")
            ],
            [
                "id" => "welcome message",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::welcome message")
            ],
            [
                "id" => "onboarding",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::onboarding")
            ],
            ["id" => "time", "label" => t(/*#ee-template-tag*/ "eetmpltag::time")],
            [
                "id" => "last chance",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::last chance")
            ],
            [
                "id" => "animation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::animation")
            ],
            [
                "id" => "limited time",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::limited time")
            ],
            [
                "id" => "promotional",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::promotional")
            ],
            ["id" => "hurry", "label" => t(/*#ee-template-tag*/ "eetmpltag::hurry")],
            [
                "id" => "hurry up",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hurry up")
            ],
            [
                "id" => "technology",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::technology")
            ],
            ["id" => "icons", "label" => t(/*#ee-template-tag*/ "eetmpltag::icons")],
            [
                "id" => "features",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::features")
            ],
            [
                "id" => "product",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::product")
            ],
            ["id" => "cool", "label" => t(/*#ee-template-tag*/ "eetmpltag::cool")],
            [
                "id" => "ticket",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::ticket")
            ],
            [
                "id" => "prizes",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::prizes")
            ],
            ["id" => "win", "label" => t(/*#ee-template-tag*/ "eetmpltag::win")],
            [
                "id" => "engagement",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::engagement")
            ],
            [
                "id" => "community",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::community")
            ],
            [
                "id" => "tickets",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::tickets")
            ],
            [
                "id" => "earn money",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::earn money")
            ],
            ["id" => "money", "label" => t(/*#ee-template-tag*/ "eetmpltag::money")],
            ["id" => "vault", "label" => t(/*#ee-template-tag*/ "eetmpltag::vault")],
            [
                "id" => "cryptocurrency",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::cryptocurrency")
            ],
            [
                "id" => "transactional",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::transactional")
            ],
            [
                "id" => "neutral",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::neutral")
            ],
            ["id" => "set", "label" => t(/*#ee-template-tag*/ "eetmpltag::set")],
            ["id" => "kit", "label" => t(/*#ee-template-tag*/ "eetmpltag::kit")],
            [
                "id" => "e-commerce",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::e-commerce")
            ],
            [
                "id" => "browsing",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::browsing")
            ],
            [
                "id" => "products",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::products")
            ],
            [
                "id" => "barber",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::barber")
            ],
            [
                "id" => "small business",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::small business")
            ],
            ["id" => "list", "label" => t(/*#ee-template-tag*/ "eetmpltag::list")],
            [
                "id" => "backpack",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::backpack")
            ],
            [
                "id" => "baggage",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::baggage")
            ],
            ["id" => "nomad", "label" => t(/*#ee-template-tag*/ "eetmpltag::nomad")],
            ["id" => "bag", "label" => t(/*#ee-template-tag*/ "eetmpltag::bag")],
            [
                "id" => "travel",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::travel")
            ],
            [
                "id" => "journey",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::journey")
            ],
            [
                "id" => "luggage",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::luggage")
            ],
            [
                "id" => "planner",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::planner")
            ],
            [
                "id" => "elegant",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::elegant")
            ],
            [
                "id" => "message",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::message")
            ],
            [
                "id" => "professional",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::professional")
            ],
            [
                "id" => "presentation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::presentation")
            ],
            [
                "id" => "curriculum",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::curriculum")
            ],
            [
                "id" => "personal",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::personal")
            ],
            ["id" => "cv", "label" => t(/*#ee-template-tag*/ "eetmpltag::cv")],
            [
                "id" => "business",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::business")
            ],
            [
                "id" => "milestones",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::milestones")
            ],
            [
                "id" => "freelancer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::freelancer")
            ],
            [
                "id" => "curriculum vitae",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::curriculum vitae")
            ],
            ["id" => "job", "label" => t(/*#ee-template-tag*/ "eetmpltag::job")],
            ["id" => "work", "label" => t(/*#ee-template-tag*/ "eetmpltag::work")],
            [
                "id" => "qr code",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::qr code")
            ],
            ["id" => "qr", "label" => t(/*#ee-template-tag*/ "eetmpltag::qr")],
            ["id" => "code", "label" => t(/*#ee-template-tag*/ "eetmpltag::code")],
            [
                "id" => "subscription",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::subscription")
            ],
            [
                "id" => "updates",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::updates")
            ],
            [
                "id" => "digest",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::digest")
            ],
            ["id" => "fancy", "label" => t(/*#ee-template-tag*/ "eetmpltag::fancy")],
            [
                "id" => "gaming",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::gaming")
            ],
            [
                "id" => "videogames",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::videogames")
            ],
            [
                "id" => "colorful",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::colorful")
            ],
            [
                "id" => "modern",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::modern")
            ],
            ["id" => "fresh", "label" => t(/*#ee-template-tag*/ "eetmpltag::fresh")],
            ["id" => "press", "label" => t(/*#ee-template-tag*/ "eetmpltag::press")],
            [
                "id" => "contrast",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::contrast")
            ],
            [
                "id" => "textmagazine",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::textmagazine")
            ],
            [
                "id" => "black and white",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::black and white")
            ],
            [
                "id" => "marketing",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::marketing")
            ],
            [
                "id" => "agency",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::agency")
            ],
            [
                "id" => "platform",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::platform")
            ],
            ["id" => "saas", "label" => t(/*#ee-template-tag*/ "eetmpltag::saas")],
            [
                "id" => "feature",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::feature")
            ],
            [
                "id" => "workshop",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::workshop")
            ],
            [
                "id" => "tutorial",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::tutorial")
            ],
            [
                "id" => "service",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::service")
            ],
            [
                "id" => "startup",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::startup")
            ],
            ["id" => "tool", "label" => t(/*#ee-template-tag*/ "eetmpltag::tool")],
            [
                "id" => "surprise",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::surprise")
            ],
            [
                "id" => "friends",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::friends")
            ],
            [
                "id" => "employee",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::employee")
            ],
            [
                "id" => "family",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::family")
            ],
            [
                "id" => "invites",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::invites")
            ],
            [
                "id" => "get-together",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::get-together")
            ],
            [
                "id" => "project",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::project")
            ],
            ["id" => "fun", "label" => t(/*#ee-template-tag*/ "eetmpltag::fun")],
            [
                "id" => "dream job",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dream job")
            ],
            [
                "id" => "events",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::events")
            ],
            [
                "id" => "happy birthday",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::happy birthday")
            ],
            [
                "id" => "testimonials",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::testimonials")
            ],
            [
                "id" => "drawings",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::drawings")
            ],
            [
                "id" => "authority",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::authority")
            ],
            ["id" => "build", "label" => t(/*#ee-template-tag*/ "eetmpltag::build")],
            ["id" => "embed", "label" => t(/*#ee-template-tag*/ "eetmpltag::embed")],
            [
                "id" => "featured",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::featured")
            ],
            [
                "id" => "places",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::places")
            ],
            [
                "id" => "leisure",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::leisure")
            ],
            ["id" => "city", "label" => t(/*#ee-template-tag*/ "eetmpltag::city")],
            [
                "id" => "travel agency",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::travel agency")
            ],
            [
                "id" => "explore",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::explore")
            ],
            ["id" => "peace", "label" => t(/*#ee-template-tag*/ "eetmpltag::peace")],
            [
                "id" => "peace day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::peace day")
            ],
            [
                "id" => "season",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::season")
            ],
            ["id" => "rich", "label" => t(/*#ee-template-tag*/ "eetmpltag::rich")],
            [
                "id" => "glitch",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::glitch")
            ],
            [
                "id" => "convention",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::convention")
            ],
            [
                "id" => "conference",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::conference")
            ],
            [
                "id" => "meeting",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::meeting")
            ],
            [
                "id" => "trendy",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::trendy")
            ],
            ["id" => "cream", "label" => t(/*#ee-template-tag*/ "eetmpltag::cream")],
            [
                "id" => "coffee break",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::coffee break")
            ],
            ["id" => "break", "label" => t(/*#ee-template-tag*/ "eetmpltag::break")],
            [
                "id" => "espresso",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::espresso")
            ],
            [
                "id" => "coffee",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::coffee")
            ],
            [
                "id" => "podcast",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::podcast")
            ],
            ["id" => "drawn", "label" => t(/*#ee-template-tag*/ "eetmpltag::drawn")],
            ["id" => "audio", "label" => t(/*#ee-template-tag*/ "eetmpltag::audio")],
            [
                "id" => "hand drawn",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hand drawn")
            ],
            [
                "id" => "accessories",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::accessories")
            ],
            [
                "id" => "collection",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::collection")
            ],
            ["id" => "woman", "label" => t(/*#ee-template-tag*/ "eetmpltag::woman")],
            [
                "id" => "geometric",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::geometric")
            ],
            [
                "id" => "graphic design",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::graphic design")
            ],
            ["id" => "trial", "label" => t(/*#ee-template-tag*/ "eetmpltag::trial")],
            [
                "id" => "computer & internet",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::computer & internet")
            ],
            [
                "id" => "designers",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::designers")
            ],
            [
                "id" => "trial endend",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::trial endend")
            ],
            [
                "id" => "undraw",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::undraw")
            ],
            ["id" => "learn", "label" => t(/*#ee-template-tag*/ "eetmpltag::learn")],
            [
                "id" => "speaker",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::speaker")
            ],
            ["id" => "plans", "label" => t(/*#ee-template-tag*/ "eetmpltag::plans")],
            [
                "id" => "upsell",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::upsell")
            ],
            [
                "id" => "upgrade",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::upgrade")
            ],
            [
                "id" => "get started",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::get started")
            ],
            [
                "id" => "illustrated",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::illustrated")
            ],
            [
                "id" => "how to",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::how to")
            ],
            [
                "id" => "dashboard",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dashboard")
            ],
            [
                "id" => "discover",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::discover")
            ],
            [
                "id" => "profile",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::profile")
            ],
            [
                "id" => "customize",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::customize")
            ],
            ["id" => "asian", "label" => t(/*#ee-template-tag*/ "eetmpltag::asian")],
            [
                "id" => "engage",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::engage")
            ],
            [
                "id" => "reactivate",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::reactivate")
            ],
            [
                "id" => "re-engagement",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::re-engagement")
            ],
            ["id" => "flat", "label" => t(/*#ee-template-tag*/ "eetmpltag::flat")],
            [
                "id" => "africa",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::africa")
            ],
            ["id" => "wave", "label" => t(/*#ee-template-tag*/ "eetmpltag::wave")],
            [
                "id" => "donations",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::donations")
            ],
            [
                "id" => "get it",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::get it")
            ],
            ["id" => "earth", "label" => t(/*#ee-template-tag*/ "eetmpltag::earth")],
            ["id" => "speed", "label" => t(/*#ee-template-tag*/ "eetmpltag::speed")],
            ["id" => "race", "label" => t(/*#ee-template-tag*/ "eetmpltag::race")],
            ["id" => "dust", "label" => t(/*#ee-template-tag*/ "eetmpltag::dust")],
            [
                "id" => "watch video",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::watch video")
            ],
            ["id" => "kids", "label" => t(/*#ee-template-tag*/ "eetmpltag::kids")],
            [
                "id" => "watercolor",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::watercolor")
            ],
            [
                "id" => "how it works",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::how it works")
            ],
            [
                "id" => "unicorn",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::unicorn")
            ],
            [
                "id" => "donation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::donation")
            ],
            [
                "id" => "wearitpink",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::wearitpink")
            ],
            [
                "id" => "breast cancer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::breast cancer")
            ],
            [
                "id" => "breast",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::breast")
            ],
            [
                "id" => "cancer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::cancer")
            ],
            ["id" => "now", "label" => t(/*#ee-template-tag*/ "eetmpltag::now")],
            ["id" => "bold", "label" => t(/*#ee-template-tag*/ "eetmpltag::bold")],
            ["id" => "beer", "label" => t(/*#ee-template-tag*/ "eetmpltag::beer")],
            [
                "id" => "ireland",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::ireland")
            ],
            [
                "id" => "tourist guide",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::tourist guide")
            ],
            [
                "id" => "creepy",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::creepy")
            ],
            [
                "id" => "antivirus",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::antivirus")
            ],
            [
                "id" => "get tickets",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::get tickets")
            ],
            ["id" => "led", "label" => t(/*#ee-template-tag*/ "eetmpltag::led")],
            [
                "id" => "android",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::android")
            ],
            ["id" => "ios", "label" => t(/*#ee-template-tag*/ "eetmpltag::ios")],
            [
                "id" => "minimal",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::minimal")
            ],
            [
                "id" => "recipes",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::recipes")
            ],
            ["id" => "cook", "label" => t(/*#ee-template-tag*/ "eetmpltag::cook")],
            [
                "id" => "pastry",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pastry")
            ],
            ["id" => "candy", "label" => t(/*#ee-template-tag*/ "eetmpltag::candy")],
            [
                "id" => "kitchen",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::kitchen")
            ],
            ["id" => "food", "label" => t(/*#ee-template-tag*/ "eetmpltag::food")],
            [
                "id" => "shop now",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::shop now")
            ],
            [
                "id" => "book tickets",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::book tickets")
            ],
            [
                "id" => "theater",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::theater")
            ],
            ["id" => "joker", "label" => t(/*#ee-template-tag*/ "eetmpltag::joker")],
            [
                "id" => "release",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::release")
            ],
            [
                "id" => "premier",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::premier")
            ],
            ["id" => "movie", "label" => t(/*#ee-template-tag*/ "eetmpltag::movie")],
            [
                "id" => "trailer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::trailer")
            ],
            [
                "id" => "cinema",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::cinema")
            ],
            ["id" => "film", "label" => t(/*#ee-template-tag*/ "eetmpltag::film")],
            [
                "id" => "fast food",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fast food")
            ],
            [
                "id" => "sandwich",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::sandwich")
            ],
            [
                "id" => "food and beverage",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::food and beverage")
            ],
            [
                "id" => "sandwich day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::sandwich day")
            ],
            [
                "id" => "life saving",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::life saving")
            ],
            [
                "id" => "research",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::research")
            ],
            ["id" => "data", "label" => t(/*#ee-template-tag*/ "eetmpltag::data")],
            [
                "id" => "colofrul",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::colofrul")
            ],
            [
                "id" => "women power",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::women power")
            ],
            [
                "id" => "minimalistic",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::minimalistic")
            ],
            [
                "id" => "life save",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::life save")
            ],
            [
                "id" => "palette",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::palette")
            ],
            [
                "id" => "cosmetics",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::cosmetics")
            ],
            [
                "id" => "lipstick",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::lipstick")
            ],
            [
                "id" => "pastel",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pastel")
            ],
            ["id" => "brush", "label" => t(/*#ee-template-tag*/ "eetmpltag::brush")],
            [
                "id" => "dynamic",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dynamic")
            ],
            [
                "id" => "protein supplements",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::protein supplements")
            ],
            [
                "id" => "see offers",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::see offers")
            ],
            [
                "id" => "diagonal",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::diagonal")
            ],
            ["id" => "shoe", "label" => t(/*#ee-template-tag*/ "eetmpltag::shoe")],
            [
                "id" => "product promotion",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::product promotion")
            ],
            ["id" => "deals", "label" => t(/*#ee-template-tag*/ "eetmpltag::deals")],
            [
                "id" => "healthylife",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::healthylife")
            ],
            [
                "id" => "basketball",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::basketball")
            ],
            [
                "id" => "net promoter score",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::net promoter score")
            ],
            ["id" => "boxed", "label" => t(/*#ee-template-tag*/ "eetmpltag::boxed")],
            ["id" => "nps", "label" => t(/*#ee-template-tag*/ "eetmpltag::nps")],
            [
                "id" => "web app",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::web app")
            ],
            [
                "id" => "social network",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::social network")
            ],
            [
                "id" => "social app",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::social app")
            ],
            [
                "id" => "emojis",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::emojis")
            ],
            [
                "id" => "delivery",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::delivery")
            ],
            [
                "id" => "thanksgiving",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::thanksgiving")
            ],
            ["id" => "menu", "label" => t(/*#ee-template-tag*/ "eetmpltag::menu")],
            [
                "id" => "calligraphy",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::calligraphy")
            ],
            [
                "id" => "pumpkin pie",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pumpkin pie")
            ],
            [
                "id" => "turkey",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::turkey")
            ],
            [
                "id" => "light orange",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::light orange")
            ],
            [
                "id" => "strong",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::strong")
            ],
            [
                "id" => "cooking",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::cooking")
            ],
            [
                "id" => "grocery",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::grocery")
            ],
            [
                "id" => "veggies",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::veggies")
            ],
            [
                "id" => "smallbusinesssaturday",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::smallbusinesssaturday")
            ],
            [
                "id" => "joyful",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::joyful")
            ],
            [
                "id" => "santa claus",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::santa claus")
            ],
            [
                "id" => "reminder",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::reminder")
            ],
            [
                "id" => "hanukkah",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hanukkah")
            ],
            [
                "id" => "december",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::december")
            ],
            [
                "id" => "jerusalem",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::jerusalem")
            ],
            [
                "id" => "judaism",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::judaism")
            ],
            [
                "id" => "candles",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::candles")
            ],
            [
                "id" => "happy new year",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::happy new year")
            ],
            [
                "id" => "new years eve",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::new years eve")
            ],
            [
                "id" => "newyear",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::newyear")
            ],
            [
                "id" => "abstract",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::abstract")
            ],
            ["id" => "faith", "label" => t(/*#ee-template-tag*/ "eetmpltag::faith")],
            ["id" => "holy", "label" => t(/*#ee-template-tag*/ "eetmpltag::holy")],
            [
                "id" => "celebration",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::celebration")
            ],
            ["id" => "joy", "label" => t(/*#ee-template-tag*/ "eetmpltag::joy")],
            [
                "id" => "chinese new year",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::chinese new year")
            ],
            [
                "id" => "tradition",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::tradition")
            ],
            ["id" => "china", "label" => t(/*#ee-template-tag*/ "eetmpltag::china")],
            [
                "id" => "year of the rat",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::year of the rat")
            ],
            [
                "id" => "photographic",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::photographic")
            ],
            [
                "id" => "chinese",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::chinese")
            ],
            [
                "id" => "roboto",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::roboto")
            ],
            [
                "id" => "new york restaurant",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::new york restaurant")
            ],
            [
                "id" => "chinese restaurant",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::chinese restaurant")
            ],
            [
                "id" => "restaurant",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::restaurant")
            ],
            [
                "id" => "proposal",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::proposal")
            ],
            [
                "id" => "company",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::company")
            ],
            ["id" => "clear", "label" => t(/*#ee-template-tag*/ "eetmpltag::clear")],
            [
                "id" => "businessman",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::businessman")
            ],
            [
                "id" => "characters",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::characters")
            ],
            [
                "id" => "services",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::services")
            ],
            [
                "id" => "laundry service",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::laundry service")
            ],
            [
                "id" => "meeting confirmation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::meeting confirmation")
            ],
            [
                "id" => "laundry",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::laundry")
            ],
            [
                "id" => "strawberry red",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::strawberry red")
            ],
            [
                "id" => "valentine's day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::valentine's day")
            ],
            [
                "id" => "super bowl",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::super bowl")
            ],
            [
                "id" => "championship",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::championship")
            ],
            [
                "id" => "american football",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::american football")
            ],
            [
                "id" => "texture",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::texture")
            ],
            [
                "id" => "panelist",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::panelist")
            ],
            [
                "id" => "pattern",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pattern")
            ],
            ["id" => "media", "label" => t(/*#ee-template-tag*/ "eetmpltag::media")],
            [
                "id" => "location",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::location")
            ],
            [
                "id" => "reserve your spot",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::reserve your spot")
            ],
            [
                "id" => "thank you",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::thank you")
            ],
            ["id" => "map", "label" => t(/*#ee-template-tag*/ "eetmpltag::map")],
            [
                "id" => "countdown",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::countdown")
            ],
            ["id" => "frame", "label" => t(/*#ee-template-tag*/ "eetmpltag::frame")],
            [
                "id" => "memories",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::memories")
            ],
            [
                "id" => "moments",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::moments")
            ],
            [
                "id" => "polaroid",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::polaroid")
            ],
            [
                "id" => "hearth",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hearth")
            ],
            [
                "id" => "handdrawn",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::handdrawn")
            ],
            [
                "id" => "picture",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::picture")
            ],
            [
                "id" => "mistery",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mistery")
            ],
            [
                "id" => "report",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::report")
            ],
            [
                "id" => "follow up",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::follow up")
            ],
            [
                "id" => "summary",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::summary")
            ],
            ["id" => "recap", "label" => t(/*#ee-template-tag*/ "eetmpltag::recap")],
            ["id" => "photo", "label" => t(/*#ee-template-tag*/ "eetmpltag::photo")],
            [
                "id" => "year in review",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::year in review")
            ],
            [
                "id" => "multipurpose",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::multipurpose")
            ],
            ["id" => "dogs", "label" => t(/*#ee-template-tag*/ "eetmpltag::dogs")],
            ["id" => "pets", "label" => t(/*#ee-template-tag*/ "eetmpltag::pets")],
            ["id" => "cats", "label" => t(/*#ee-template-tag*/ "eetmpltag::cats")],
            [
                "id" => "pet shop",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pet shop")
            ],
            ["id" => "vet", "label" => t(/*#ee-template-tag*/ "eetmpltag::vet")],
            [
                "id" => "veterinary",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::veterinary")
            ],
            ["id" => "bill", "label" => t(/*#ee-template-tag*/ "eetmpltag::bill")],
            [
                "id" => "colourful",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::colourful")
            ],
            [
                "id" => "bookkeeping",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::bookkeeping")
            ],
            [
                "id" => "accounting",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::accounting")
            ],
            [
                "id" => "corporate",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::corporate")
            ],
            ["id" => "law", "label" => t(/*#ee-template-tag*/ "eetmpltag::law")],
            ["id" => "stars", "label" => t(/*#ee-template-tag*/ "eetmpltag::stars")],
            ["id" => "tape", "label" => t(/*#ee-template-tag*/ "eetmpltag::tape")],
            [
                "id" => "fatherhood",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fatherhood")
            ],
            [
                "id" => "camera",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::camera")
            ],
            [
                "id" => "father",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::father")
            ],
            [
                "id" => "father's day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::father's day")
            ],
            [
                "id" => "father's day campaign",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::father's day campaign")
            ],
            ["id" => "8", "label" => t(/*#ee-template-tag*/ "eetmpltag::8")],
            [
                "id" => "special sale",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::special sale")
            ],
            ["id" => "march", "label" => t(/*#ee-template-tag*/ "eetmpltag::march")],
            [
                "id" => "8 march",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::8 march")
            ],
            ["id" => "women", "label" => t(/*#ee-template-tag*/ "eetmpltag::women")],
            [
                "id" => "international women's day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::international women's day")
            ],
            [
                "id" => "female",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::female")
            ],
            ["id" => "npo", "label" => t(/*#ee-template-tag*/ "eetmpltag::npo")],
            [
                "id" => "festival",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::festival")
            ],
            [
                "id" => "fundraising",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fundraising")
            ],
            [
                "id" => "fortune",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fortune")
            ],
            [
                "id" => "saint patrick's day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::saint patrick's day")
            ],
            [
                "id" => "good luck",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::good luck")
            ],
            [
                "id" => "questionnaire",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::questionnaire")
            ],
            [
                "id" => "megaphone",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::megaphone")
            ],
            [
                "id" => "opinion",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::opinion")
            ],
            [
                "id" => "questions",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::questions")
            ],
            ["id" => "emoji", "label" => t(/*#ee-template-tag*/ "eetmpltag::emoji")],
            [
                "id" => "thumbs up",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::thumbs up")
            ],
            ["id" => "chart", "label" => t(/*#ee-template-tag*/ "eetmpltag::chart")],
            ["id" => "pool", "label" => t(/*#ee-template-tag*/ "eetmpltag::pool")],
            [
                "id" => "memphis",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::memphis")
            ],
            [
                "id" => "counter",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::counter")
            ],
            [
                "id" => "flash sale",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::flash sale")
            ],
            [
                "id" => "flowers",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::flowers")
            ],
            [
                "id" => "spring",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::spring")
            ],
            [
                "id" => "april fools' day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::april fools' day")
            ],
            ["id" => "funny", "label" => t(/*#ee-template-tag*/ "eetmpltag::funny")],
            [
                "id" => "just kidding",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::just kidding")
            ],
            [
                "id" => "shipping",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::shipping")
            ],
            [
                "id" => "transportation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::transportation")
            ],
            [
                "id" => "shipment",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::shipment")
            ],
            ["id" => "faq", "label" => t(/*#ee-template-tag*/ "eetmpltag::faq")],
            [
                "id" => "spring green",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::spring green")
            ],
            [
                "id" => "easter egg",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::easter egg")
            ],
            [
                "id" => "automotive",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::automotive")
            ],
            [
                "id" => "dealer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dealer")
            ],
            [
                "id" => "test drive",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::test drive")
            ],
            ["id" => "egg", "label" => t(/*#ee-template-tag*/ "eetmpltag::egg")],
            [
                "id" => "activities",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::activities")
            ],
            [
                "id" => "highlights",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::highlights")
            ],
            [
                "id" => "chalkboard",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::chalkboard")
            ],
            [
                "id" => "calendar activities",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::calendar activities")
            ],
            [
                "id" => "calendar",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::calendar")
            ],
            [
                "id" => "comics",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::comics")
            ],
            ["id" => "eggs", "label" => t(/*#ee-template-tag*/ "eetmpltag::eggs")],
            [
                "id" => "beverage",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::beverage")
            ],
            [
                "id" => "teaser",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::teaser")
            ],
            [
                "id" => "sweepstakes",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::sweepstakes")
            ],
            [
                "id" => "mystery deal",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mystery deal")
            ],
            [
                "id" => "lottery",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::lottery")
            ],
            [
                "id" => "mystery box",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mystery box")
            ],
            [
                "id" => "earth day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::earth day")
            ],
            ["id" => "eco", "label" => t(/*#ee-template-tag*/ "eetmpltag::eco")],
            ["id" => "short", "label" => t(/*#ee-template-tag*/ "eetmpltag::short")],
            [
                "id" => "crisis email",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::crisis email")
            ],
            [
                "id" => "prevention",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::prevention")
            ],
            [
                "id" => "protection",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::protection")
            ],
            [
                "id" => "coronavirus",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::coronavirus")
            ],
            [
                "id" => "floral",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::floral")
            ],
            [
                "id" => "special offer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::special offer")
            ],
            [
                "id" => "mother's day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mother's day")
            ],
            ["id" => "night", "label" => t(/*#ee-template-tag*/ "eetmpltag::night")],
            [
                "id" => "balloon",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::balloon")
            ],
            ["id" => "deal", "label" => t(/*#ee-template-tag*/ "eetmpltag::deal")],
            [
                "id" => "fiesta",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fiesta")
            ],
            [
                "id" => "meal deal",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::meal deal")
            ],
            [
                "id" => "mexican",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mexican")
            ],
            [
                "id" => "cinco de mayo",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::cinco de mayo")
            ],
            [
                "id" => "memorial",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::memorial")
            ],
            [
                "id" => "mexico",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mexico")
            ],
            [
                "id" => "weekly program",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::weekly program")
            ],
            [
                "id" => "education",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::education")
            ],
            [
                "id" => "self-care",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::self-care")
            ],
            [
                "id" => "feminine",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::feminine")
            ],
            ["id" => "gifts", "label" => t(/*#ee-template-tag*/ "eetmpltag::gifts")],
            ["id" => "mom", "label" => t(/*#ee-template-tag*/ "eetmpltag::mom")],
            [
                "id" => "influencer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::influencer")
            ],
            [
                "id" => "social",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::social")
            ],
            [
                "id" => "latest posts",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::latest posts")
            ],
            [
                "id" => "pet food",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pet food")
            ],
            [
                "id" => "new product",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::new product")
            ],
            [
                "id" => "national pet week",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::national pet week")
            ],
            [
                "id" => "birthday",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::birthday")
            ],
            [
                "id" => "present",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::present")
            ],
            [
                "id" => "giftbox",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::giftbox")
            ],
            [
                "id" => "food & beverage",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::food & beverage")
            ],
            [
                "id" => "celebrations",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::celebrations")
            ],
            ["id" => "may", "label" => t(/*#ee-template-tag*/ "eetmpltag::may")],
            [
                "id" => "national salad month",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::national salad month")
            ],
            [
                "id" => "healthy",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::healthy")
            ],
            [
                "id" => "vegetables",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::vegetables")
            ],
            [
                "id" => "greens",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::greens")
            ],
            ["id" => "salad", "label" => t(/*#ee-template-tag*/ "eetmpltag::salad")],
            [
                "id" => "recipe",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::recipe")
            ],
            [
                "id" => "job search",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::job search")
            ],
            [
                "id" => "videographer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::videographer")
            ],
            [
                "id" => "self promotion",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::self promotion")
            ],
            [
                "id" => "video editor",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::video editor")
            ],
            ["id" => "call", "label" => t(/*#ee-template-tag*/ "eetmpltag::call")],
            [
                "id" => "video call",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::video call")
            ],
            [
                "id" => "video conference",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::video conference")
            ],
            [
                "id" => "remote",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::remote")
            ],
            ["id" => "zine", "label" => t(/*#ee-template-tag*/ "eetmpltag::zine")],
            [
                "id" => "resume",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::resume")
            ],
            [
                "id" => "experience",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::experience")
            ],
            [
                "id" => "photographer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::photographer")
            ],
            ["id" => "cat", "label" => t(/*#ee-template-tag*/ "eetmpltag::cat")],
            ["id" => "dog", "label" => t(/*#ee-template-tag*/ "eetmpltag::dog")],
            [
                "id" => "petweek",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::petweek")
            ],
            [
                "id" => "animal",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::animal")
            ],
            [
                "id" => "conference call",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::conference call")
            ],
            [
                "id" => "gallery",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::gallery")
            ],
            ["id" => "home", "label" => t(/*#ee-template-tag*/ "eetmpltag::home")],
            [
                "id" => "contact us",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::contact us")
            ],
            ["id" => "agent", "label" => t(/*#ee-template-tag*/ "eetmpltag::agent")],
            [
                "id" => "property",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::property")
            ],
            [
                "id" => "listings",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::listings")
            ],
            [
                "id" => "real estate",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::real estate")
            ],
            [
                "id" => "read article",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::read article")
            ],
            ["id" => "house", "label" => t(/*#ee-template-tag*/ "eetmpltag::house")],
            [
                "id" => "testimonial",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::testimonial")
            ],
            [
                "id" => "view property",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::view property")
            ],
            [
                "id" => "make an offer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::make an offer")
            ],
            [
                "id" => "search",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::search")
            ],
            ["id" => "find", "label" => t(/*#ee-template-tag*/ "eetmpltag::find")],
            [
                "id" => "address",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::address")
            ],
            [
                "id" => "schedule a visit",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::schedule a visit")
            ],
            [
                "id" => "select date",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::select date")
            ],
            ["id" => "email", "label" => t(/*#ee-template-tag*/ "eetmpltag::email")],
            [
                "id" => "google maps",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::google maps")
            ],
            [
                "id" => "telephone",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::telephone")
            ],
            [
                "id" => "add to calendar",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::add to calendar")
            ],
            [
                "id" => "repair",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::repair")
            ],
            [
                "id" => "car dealer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::car dealer")
            ],
            [
                "id" => "dark there",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dark there")
            ],
            ["id" => "auto", "label" => t(/*#ee-template-tag*/ "eetmpltag::auto")],
            [
                "id" => "flourish",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::flourish")
            ],
            ["id" => "plant", "label" => t(/*#ee-template-tag*/ "eetmpltag::plant")],
            [
                "id" => "flower",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::flower")
            ],
            [
                "id" => "decoration",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::decoration")
            ],
            ["id" => "ui", "label" => t(/*#ee-template-tag*/ "eetmpltag::ui")],
            [
                "id" => "web designer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::web designer")
            ],
            [
                "id" => "web design",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::web design")
            ],
            ["id" => "ux", "label" => t(/*#ee-template-tag*/ "eetmpltag::ux")],
            ["id" => "works", "label" => t(/*#ee-template-tag*/ "eetmpltag::works")],
            [
                "id" => "graphic designer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::graphic designer")
            ],
            [
                "id" => "service promotion",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::service promotion")
            ],
            [
                "id" => "skills",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::skills")
            ],
            [
                "id" => "introduction",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::introduction")
            ],
            [
                "id" => "pride events",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pride events")
            ],
            ["id" => "lgbtq", "label" => t(/*#ee-template-tag*/ "eetmpltag::lgbtq")],
            [
                "id" => "pride parade",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pride parade")
            ],
            ["id" => "lgbt", "label" => t(/*#ee-template-tag*/ "eetmpltag::lgbt")],
            ["id" => "pride", "label" => t(/*#ee-template-tag*/ "eetmpltag::pride")],
            [
                "id" => "product launch",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::product launch")
            ],
            [
                "id" => "seasonal promotion",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::seasonal promotion")
            ],
            [
                "id" => "makeup",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::makeup")
            ],
            ["id" => "dad", "label" => t(/*#ee-template-tag*/ "eetmpltag::dad")],
            ["id" => "hair", "label" => t(/*#ee-template-tag*/ "eetmpltag::hair")],
            ["id" => "body", "label" => t(/*#ee-template-tag*/ "eetmpltag::body")],
            [
                "id" => "beauty",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::beauty")
            ],
            [
                "id" => "pride day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pride day")
            ],
            ["id" => "face", "label" => t(/*#ee-template-tag*/ "eetmpltag::face")],
            ["id" => "souls", "label" => t(/*#ee-template-tag*/ "eetmpltag::souls")],
            [
                "id" => "gay pride",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::gay pride")
            ],
            ["id" => "sea", "label" => t(/*#ee-template-tag*/ "eetmpltag::sea")],
            [
                "id" => "seafood",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::seafood")
            ],
            ["id" => "fish", "label" => t(/*#ee-template-tag*/ "eetmpltag::fish")],
            ["id" => "waves", "label" => t(/*#ee-template-tag*/ "eetmpltag::waves")],
            [
                "id" => "register",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::register")
            ],
            ["id" => "talk", "label" => t(/*#ee-template-tag*/ "eetmpltag::talk")],
            [
                "id" => "website",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::website")
            ],
            [
                "id" => "speakers",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::speakers")
            ],
            [
                "id" => "electronics",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::electronics")
            ],
            [
                "id" => "devices",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::devices")
            ],
            ["id" => "daddy", "label" => t(/*#ee-template-tag*/ "eetmpltag::daddy")],
            ["id" => "style", "label" => t(/*#ee-template-tag*/ "eetmpltag::style")],
            ["id" => "cut", "label" => t(/*#ee-template-tag*/ "eetmpltag::cut")],
            ["id" => "salon", "label" => t(/*#ee-template-tag*/ "eetmpltag::salon")],
            [
                "id" => "hairdresser",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hairdresser")
            ],
            [
                "id" => "beauty salon",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::beauty salon")
            ],
            [
                "id" => "strategy",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::strategy")
            ],
            [
                "id" => "strategic",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::strategic")
            ],
            [
                "id" => "innovation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::innovation")
            ],
            [
                "id" => "branding",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::branding")
            ],
            ["id" => "web", "label" => t(/*#ee-template-tag*/ "eetmpltag::web")],
            [
                "id" => "running",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::running")
            ],
            [
                "id" => "mistake",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mistake")
            ],
            [
                "id" => "coupon code",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::coupon code")
            ],
            [
                "id" => "apologize",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::apologize")
            ],
            ["id" => "vr", "label" => t(/*#ee-template-tag*/ "eetmpltag::vr")],
            ["id" => "shoes", "label" => t(/*#ee-template-tag*/ "eetmpltag::shoes")],
            [
                "id" => "trainers",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::trainers")
            ],
            [
                "id" => "abandoned cart",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::abandoned cart")
            ],
            [
                "id" => "sneakers",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::sneakers")
            ],
            [
                "id" => "clothing",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::clothing")
            ],
            [
                "id" => "forgot to check out",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::forgot to check out")
            ],
            [
                "id" => "shopping cart",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::shopping cart")
            ],
            [
                "id" => "online shopping",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::online shopping")
            ],
            ["id" => "rose", "label" => t(/*#ee-template-tag*/ "eetmpltag::rose")],
            [
                "id" => "playful",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::playful")
            ],
            ["id" => "key", "label" => t(/*#ee-template-tag*/ "eetmpltag::key")],
            [
                "id" => "password",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::password")
            ],
            [
                "id" => "feedback",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::feedback")
            ],
            [
                "id" => "electronic",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::electronic")
            ],
            [
                "id" => "headphone",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::headphone")
            ],
            [
                "id" => "minimalist",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::minimalist")
            ],
            ["id" => "order", "label" => t(/*#ee-template-tag*/ "eetmpltag::order")],
            [
                "id" => "verification",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::verification")
            ],
            [
                "id" => "payment",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::payment")
            ],
            ["id" => "inbox", "label" => t(/*#ee-template-tag*/ "eetmpltag::inbox")],
            ["id" => "carta", "label" => t(/*#ee-template-tag*/ "eetmpltag::carta")],
            [
                "id" => "abandoned",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::abandoned")
            ],
            [
                "id" => "recovery",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::recovery")
            ],
            ["id" => "gif", "label" => t(/*#ee-template-tag*/ "eetmpltag::gif")],
            [
                "id" => "cupcake",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::cupcake")
            ],
            [
                "id" => "special",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::special")
            ],
            [
                "id" => "candle",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::candle")
            ],
            [
                "id" => "appreciation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::appreciation")
            ],
            ["id" => "cake", "label" => t(/*#ee-template-tag*/ "eetmpltag::cake")],
            [
                "id" => "celebrate",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::celebrate")
            ],
            [
                "id" => "discount code",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::discount code")
            ],
            ["id" => "perks", "label" => t(/*#ee-template-tag*/ "eetmpltag::perks")],
            [
                "id" => "loyalty",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::loyalty")
            ],
            [
                "id" => "customer appreciation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::customer appreciation")
            ],
            [
                "id" => "webshop",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::webshop")
            ],
            [
                "id" => "tshirt",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::tshirt")
            ],
            [
                "id" => "monochrome",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::monochrome")
            ],
            [
                "id" => "business services",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::business services")
            ],
            [
                "id" => "work with us",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::work with us")
            ],
            [
                "id" => "studio",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::studio")
            ],
            [
                "id" => "architecture",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::architecture")
            ],
            ["id" => "lines", "label" => t(/*#ee-template-tag*/ "eetmpltag::lines")],
            [
                "id" => "interior",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::interior")
            ],
            [
                "id" => "glamour",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::glamour")
            ],
            ["id" => "edgy", "label" => t(/*#ee-template-tag*/ "eetmpltag::edgy")],
            [
                "id" => "interior design",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::interior design")
            ],
            [
                "id" => "educational courses",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::educational courses")
            ],
            [
                "id" => "online courses",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::online courses")
            ],
            [
                "id" => "independence day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::independence day")
            ],
            [
                "id" => "finance",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::finance")
            ],
            ["id" => "bank", "label" => t(/*#ee-template-tag*/ "eetmpltag::bank")],
            ["id" => "loan", "label" => t(/*#ee-template-tag*/ "eetmpltag::loan")],
            [
                "id" => "festive",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::festive")
            ],
            [
                "id" => "stars and stripes",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::stars and stripes")
            ],
            ["id" => "usa", "label" => t(/*#ee-template-tag*/ "eetmpltag::usa")],
            [
                "id" => "blue red white",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::blue red white")
            ],
            [
                "id" => "glitch effect",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::glitch effect")
            ],
            [
                "id" => "computer games",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::computer games")
            ],
            [
                "id" => "dark mode",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::dark mode")
            ],
            ["id" => "cyber", "label" => t(/*#ee-template-tag*/ "eetmpltag::cyber")],
            ["id" => "gamer", "label" => t(/*#ee-template-tag*/ "eetmpltag::gamer")],
            [
                "id" => "top 10",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::top 10")
            ],
            [
                "id" => "meet the team",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::meet the team")
            ],
            [
                "id" => "marketer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::marketer")
            ],
            ["id" => "glow", "label" => t(/*#ee-template-tag*/ "eetmpltag::glow")],
            [
                "id" => "brands",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::brands")
            ],
            [
                "id" => "volunteer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::volunteer")
            ],
            [
                "id" => "volunteering",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::volunteering")
            ],
            [
                "id" => "traveling",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::traveling")
            ],
            ["id" => "udemy", "label" => t(/*#ee-template-tag*/ "eetmpltag::udemy")],
            [
                "id" => "enroll",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::enroll")
            ],
            [
                "id" => "courses",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::courses")
            ],
            ["id" => "books", "label" => t(/*#ee-template-tag*/ "eetmpltag::books")],
            ["id" => "study", "label" => t(/*#ee-template-tag*/ "eetmpltag::study")],
            [
                "id" => "coursera",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::coursera")
            ],
            [
                "id" => "rent a car",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::rent a car")
            ],
            [
                "id" => "car rental",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::car rental")
            ],
            ["id" => "fleet", "label" => t(/*#ee-template-tag*/ "eetmpltag::fleet")],
            ["id" => "cars", "label" => t(/*#ee-template-tag*/ "eetmpltag::cars")],
            [
                "id" => "emoji world day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::emoji world day")
            ],
            ["id" => "happy", "label" => t(/*#ee-template-tag*/ "eetmpltag::happy")],
            [
                "id" => "emoji day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::emoji day")
            ],
            [
                "id" => "adventure",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::adventure")
            ],
            [
                "id" => "4th of july",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::4th of july")
            ],
            [
                "id" => "independence",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::independence")
            ],
            [
                "id" => "destinations",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::destinations")
            ],
            [
                "id" => "thank you email",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::thank you email")
            ],
            [
                "id" => "purchase confirmation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::purchase confirmation")
            ],
            [
                "id" => "purchase",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::purchase")
            ],
            [
                "id" => "receipt",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::receipt")
            ],
            [
                "id" => "lesson",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::lesson")
            ],
            [
                "id" => "appointment",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::appointment")
            ],
            [
                "id" => "schedule",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::schedule")
            ],
            ["id" => "notes", "label" => t(/*#ee-template-tag*/ "eetmpltag::notes")],
            [
                "id" => "guitar",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::guitar")
            ],
            [
                "id" => "scheduling",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::scheduling")
            ],
            ["id" => "relax", "label" => t(/*#ee-template-tag*/ "eetmpltag::relax")],
            [
                "id" => "massage",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::massage")
            ],
            [
                "id" => "welness",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::welness")
            ],
            [
                "id" => "health",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::health")
            ],
            [
                "id" => "treatments",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::treatments")
            ],
            [
                "id" => "process",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::process")
            ],
            [
                "id" => "bright",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::bright")
            ],
            [
                "id" => "back to school",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::back to school")
            ],
            [
                "id" => "stationery",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::stationery")
            ],
            [
                "id" => "supplies",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::supplies")
            ],
            [
                "id" => "book lovers day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::book lovers day")
            ],
            [
                "id" => "reading",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::reading")
            ],
            [
                "id" => "bookshop",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::bookshop")
            ],
            [
                "id" => "reviews",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::reviews")
            ],
            [
                "id" => "bookstore",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::bookstore")
            ],
            [
                "id" => "ebooks",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::ebooks")
            ],
            [
                "id" => "book store",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::book store")
            ],
            ["id" => "guide", "label" => t(/*#ee-template-tag*/ "eetmpltag::guide")],
            [
                "id" => "special day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::special day")
            ],
            ["id" => "room", "label" => t(/*#ee-template-tag*/ "eetmpltag::room")],
            ["id" => "trip", "label" => t(/*#ee-template-tag*/ "eetmpltag::trip")],
            [
                "id" => "privacy",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::privacy")
            ],
            ["id" => "terms", "label" => t(/*#ee-template-tag*/ "eetmpltag::terms")],
            [
                "id" => "terms of service",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::terms of service")
            ],
            ["id" => "gdpr", "label" => t(/*#ee-template-tag*/ "eetmpltag::gdpr")],
            [
                "id" => "conditions",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::conditions")
            ],
            [
                "id" => "on-line",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::on-line")
            ],
            [
                "id" => "virtual",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::virtual")
            ],
            [
                "id" => "airplane",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::airplane")
            ],
            [
                "id" => "flight confirmation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::flight confirmation")
            ],
            [
                "id" => "flight",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::flight")
            ],
            [
                "id" => "resubscribe",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::resubscribe")
            ],
            [
                "id" => "opt in",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::opt in")
            ],
            [
                "id" => "subscribe",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::subscribe")
            ],
            [
                "id" => "others",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::others")
            ],
            ["id" => "month", "label" => t(/*#ee-template-tag*/ "eetmpltag::month")],
            [
                "id" => "agenda",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::agenda")
            ],
            [
                "id" => "online business",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::online business")
            ],
            [
                "id" => "online",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::online")
            ],
            [
                "id" => "business growth",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::business growth")
            ],
            [
                "id" => "airline",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::airline")
            ],
            [
                "id" => "check-in",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::check-in")
            ],
            [
                "id" => "contest",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::contest")
            ],
            [
                "id" => "equality",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::equality")
            ],
            [
                "id" => "women's equality day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::women's equality day")
            ],
            [
                "id" => "women's rights",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::women's rights")
            ],
            [
                "id" => "giveaway",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::giveaway")
            ],
            [
                "id" => "special event",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::special event")
            ],
            [
                "id" => "audiophiles",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::audiophiles")
            ],
            [
                "id" => "music app",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::music app")
            ],
            [
                "id" => "soft light",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::soft light")
            ],
            [
                "id" => "app promotion",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::app promotion")
            ],
            [
                "id" => "app launch",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::app launch")
            ],
            ["id" => "neon", "label" => t(/*#ee-template-tag*/ "eetmpltag::neon")],
            ["id" => "labor", "label" => t(/*#ee-template-tag*/ "eetmpltag::labor")],
            [
                "id" => "labour",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::labour")
            ],
            [
                "id" => "labor day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::labor day")
            ],
            [
                "id" => "new app",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::new app")
            ],
            [
                "id" => "app store",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::app store")
            ],
            [
                "id" => "tablet",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::tablet")
            ],
            [
                "id" => "e-learning",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::e-learning")
            ],
            [
                "id" => "online course",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::online course")
            ],
            [
                "id" => "pricing table",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pricing table")
            ],
            [
                "id" => "discounts",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::discounts")
            ],
            [
                "id" => "instruments",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::instruments")
            ],
            [
                "id" => "labour day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::labour day")
            ],
            ["id" => "post", "label" => t(/*#ee-template-tag*/ "eetmpltag::post")],
            [
                "id" => "article",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::article")
            ],
            [
                "id" => "women for women",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::women for women")
            ],
            [
                "id" => "listing",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::listing")
            ],
            ["id" => "hire", "label" => t(/*#ee-template-tag*/ "eetmpltag::hire")],
            [
                "id" => "position",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::position")
            ],
            [
                "id" => "opportunity",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::opportunity")
            ],
            [
                "id" => "job posting",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::job posting")
            ],
            [
                "id" => "hiring",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hiring")
            ],
            [
                "id" => "account",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::account")
            ],
            ["id" => "club", "label" => t(/*#ee-template-tag*/ "eetmpltag::club")],
            ["id" => "sound", "label" => t(/*#ee-template-tag*/ "eetmpltag::sound")],
            [
                "id" => "membership",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::membership")
            ],
            [
                "id" => "savings",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::savings")
            ],
            [
                "id" => "skin products",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::skin products")
            ],
            [
                "id" => "promo code",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::promo code")
            ],
            [
                "id" => "event related promotion",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::event related promotion")
            ],
            ["id" => "girls", "label" => t(/*#ee-template-tag*/ "eetmpltag::girls")],
            [
                "id" => "seasonal offer",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::seasonal offer")
            ],
            [
                "id" => "furniture",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::furniture")
            ],
            [
                "id" => "new collection",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::new collection")
            ],
            [
                "id" => "gradients",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::gradients")
            ],
            ["id" => "fall", "label" => t(/*#ee-template-tag*/ "eetmpltag::fall")],
            [
                "id" => "home decor",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::home decor")
            ],
            [
                "id" => "fall colours",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fall colours")
            ],
            ["id" => "warm", "label" => t(/*#ee-template-tag*/ "eetmpltag::warm")],
            [
                "id" => "pet toy",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::pet toy")
            ],
            [
                "id" => "treats",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::treats")
            ],
            [
                "id" => "emergency",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::emergency")
            ],
            [
                "id" => "local business",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::local business")
            ],
            ["id" => "local", "label" => t(/*#ee-template-tag*/ "eetmpltag::local")],
            [
                "id" => "baking",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::baking")
            ],
            [
                "id" => "interests",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::interests")
            ],
            [
                "id" => "crafts",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::crafts")
            ],
            [
                "id" => "hobbies",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hobbies")
            ],
            [
                "id" => "classes",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::classes")
            ],
            ["id" => "diy", "label" => t(/*#ee-template-tag*/ "eetmpltag::diy")],
            [
                "id" => "stayinghome",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::stayinghome")
            ],
            [
                "id" => "freebie",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::freebie")
            ],
            [
                "id" => "app features",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::app features")
            ],
            [
                "id" => "glasses",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::glasses")
            ],
            [
                "id" => "collection launch",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::collection launch")
            ],
            [
                "id" => "coming soon",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::coming soon")
            ],
            [
                "id" => "eyewear",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::eyewear")
            ],
            ["id" => "decor", "label" => t(/*#ee-template-tag*/ "eetmpltag::decor")],
            ["id" => "brand", "label" => t(/*#ee-template-tag*/ "eetmpltag::brand")],
            [
                "id" => "models",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::models")
            ],
            [
                "id" => "fall collection",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fall collection")
            ],
            [
                "id" => "menswear",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::menswear")
            ],
            [
                "id" => "product list",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::product list")
            ],
            ["id" => "radio", "label" => t(/*#ee-template-tag*/ "eetmpltag::radio")],
            [
                "id" => "on air",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::on air")
            ],
            [
                "id" => "international podcast day",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::international podcast day")
            ],
            [
                "id" => "entertainment",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::entertainment")
            ],
            [
                "id" => "recruitment",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::recruitment")
            ],
            [
                "id" => "hiring software",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::hiring software")
            ],
            [
                "id" => "talent",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::talent")
            ],
            [
                "id" => "mindfulness",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::mindfulness")
            ],
            ["id" => "calm", "label" => t(/*#ee-template-tag*/ "eetmpltag::calm")],
            ["id" => "men", "label" => t(/*#ee-template-tag*/ "eetmpltag::men")],
            [
                "id" => "fall traveling",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fall traveling")
            ],
            [
                "id" => "vacation",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::vacation")
            ],
            [
                "id" => "fall season travels",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::fall season travels")
            ],
            [
                "id" => "food delivery",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::food delivery")
            ],
            ["id" => "scary", "label" => t(/*#ee-template-tag*/ "eetmpltag::scary")],
            [
                "id" => "horror",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::horror")
            ],
            [
                "id" => "game sale",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::game sale")
            ],
            [
                "id" => "groceries",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::groceries")
            ],
            [
                "id" => "ingredients",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::ingredients")
            ],
            [
                "id" => "grocery store",
                "label" => t(/*#ee-template-tag*/ "eetmpltag::grocery store")
            ],
        ];
    }

    public static function usageIndex()
    {
        //<#ee-template-usage>Usage category name to filter email editor templates.
        //Do not include 'eetmplusage::' in translation' </#>

        return [
            [
                "id" => "product-promotion",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Product Promotion")
            ],
            [
                "id" => "events",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Events")
            ],
            [
                "id" => "seasonal-promotion",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Seasonal Promotion")
            ],
            [
                "id" => "newsletter",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Newsletter")
            ],
            [
                "id" => "notification",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Notification")
            ],
            [
                "id" => "product-launch",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Product Launch")
            ],
            [
                "id" => "transactional",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Transactional")
            ],
            [
                "id" => "personal-note",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Personal Note")
            ],
            [
                "id" => "service-promotion",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Service Promotion")
            ],
            [
                "id" => "download",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Download")
            ],
            [
                "id" => "mystery_email",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Mystery Email")
            ],
            [
                "id" => "e-commerce-set",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::E-commerce Set")
            ],
            [
                "id" => "welcome_series",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Welcome Series Set")
            ],
            [
                "id" => "event_set",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Event Set")
            ],
            [
                "id" => "survey_management_set",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Survey Management Set")
            ],
            [
                "id" => "tutorial",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Tutorial")
            ],
            [
                "id" => "real_estate_set",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Real Estate Set")
            ],
            [
                "id" => "apologize_email",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Apologize Email")
            ],
            [
                "id" => "abandoned_cart",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Abandoned Cart")
            ],
            [
                "id" => "thank-you",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Thank you")
            ],
            [
                "id" => "welcome",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Welcome")
            ],
            [
                "id" => "gdpr",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::gdpr")
            ],
            [
                "id" => "educational_set",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Educational Set")
            ],
            [
                "id" => "activation",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Activation")
            ],
            [
                "id" => "confirmation",
                "label" => t(/*#ee-template-usage*/ "eetmplusage::Confirmation")
            ],
        ];
    }

    public static function industryIndex()
    {
        //<#ee-template-industry>Industry category name to filter email editor templates.
        // Do not include 'eetmplindustry::' in translation' </#>

        return [
            [
                "id" => "fashion",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Fashion")
            ],
            [
                "id" => "travel-leisure",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Travel & Leisure")
            ],
            [
                "id" => "news-blog-and-magazines",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::News, Blog & Magazines")
            ],
            [
                "id" => "computer-internet",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Computer & Internet")
            ],
            [
                "id" => "business-services",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Business Services")
            ],
            [
                "id" => "food-beverage",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Food & Beverage")
            ],
            [
                "id" => "education",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Education")
            ],
            [
                "id" => "transportation-storage",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Transportation & Storage")
            ],
            [
                "id" => "media-entertainment",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Media & Entertainment")
            ],
            [
                "id" => "health-wellness",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Health & Wellness")
            ],
            [
                "id" => "non-profit",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Non Profit")
            ],
            [
                "id" => "real-estate",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Real Estate")
            ],
            [
                "id" => "automotive",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Automotive")
            ],
            [
                "id" => "manufacturing",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Manufacturing")
            ],
            [
                "id" => "others",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Others")
            ],
            [
                "id" => "marketing_and_design",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Marketing & Design")
            ],
            [
                "id" => "cosmetics",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Cosmetics")
            ],
            [
                "id" => "sports",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Sports")
            ],
            [
                "id" => "pets_and_animal_care",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Pets and Animal Care")
            ],
            [
                "id" => "electronics",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Electronics")
            ],
            [
                "id" => "financial-money",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::Financial - Money")
            ],
            [
                "id" => "hr",
                "label" => t(/*#ee-template-industry*/ "eetmplindustry::HR")
            ],
        ];
    }
}

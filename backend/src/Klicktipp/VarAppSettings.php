<?php

namespace App\Klicktipp;

use function db_query;

/**
 * Angular application settings of the user
 */
class VarAppSettings extends UserVariablesTyped
{
    public static $VarType = UserVariables::VARTYPE_APP_SETTINGS;

    /**
     * Email Editor Manual Save Button
     */
    const EE_MANUALSAVEBUTTON = 0;

    private static $AvailableAppSettings = [
        'EE_MANUALSAVEBUTTO' => [
            'value' => self::EE_MANUALSAVEBUTTON,
            'angularApiField' => 'EmailEditorManualSaveButton',
        ],
    ];

    /**
     * Get a single app setting or the default value.
     * If no default is set, will return NULL as default value.
     *
     * This will first validate if a setting with the given SeqNo exists.
     *
     * @param int $UserID
     * @param int $Setting Actually the SeqNo for the DB entry
     * @param null $Default
     *
     * @return mixed|null
     */
    public static function GetAppSetting($UserID, $Setting, $Default = null)
    {
        if (!static::ValidateAppSetting($Setting)) {
            return $Default;
        }
        $result = db_query(
            " SELECT Data FROM {user_variables}
              WHERE RelOwnerUserID = :RelOwnerUserID AND
              VarType = :VarType AND
              SeqNo = :SeqNo
              LIMIT 1",
            [
                ':RelOwnerUserID' => $UserID,
                ':VarType' => static::$VarType,
                ':SeqNo' => $Setting,
            ]
        )->fetchField();

        return empty($result) ? $Default : unserialize((string)$result);
    }

    /**
     * Set a given value for an app setting.
     * This will first validate if a setting with the given SeqNo exists and
     * return false if it doesn't.
     *
     * @param int $UserID
     * @param int $Setting Actually the SeqNo for the DB entry
     * @param mixed $Value
     *
     * @return bool
     * @see static::SetVariable
     */
    public static function SetAppSetting($UserID, $Setting, $Value)
    {
        if (!static::ValidateAppSetting($Setting)) {
            return false;
        }
        db_query(
            "INSERT INTO {user_variables} (RelOwnerUserID, VarType, SeqNo, RelIndexed, VarcharIndexed, Data)
             VALUES (:RelOwnerUserID, :VarType, :SeqNo, :RelIndexed, :VarcharIndexed, :Data)
             ON DUPLICATE KEY UPDATE Data = :Data",
            [
                ':RelOwnerUserID' => $UserID,
                ':VarType' => static::$VarType,
                ':SeqNo' => $Setting,
                ':RelIndexed' => 0,
                ':VarcharIndexed' => '',
                ':Data' => serialize($Value),
            ]
        );
        return true;
    }

    /**
     * Validate given app setting against list of available settings.
     * This can only perform a simple validation: does any app setting with the
     * given SeqNo exist?
     *
     * @param $Setting
     *
     * @return bool
     */
    public static function ValidateAppSetting($Setting)
    {
        return array_search(
            $Setting,
            array_column(static::$AvailableAppSettings, 'value')
        ) !== false;
    }

    /**
     * Get all of a users AppSettings and values in an Angular specific format
     *
     * @param $UserID
     *
     * @return array
     */
    public static function GetAvailableAppSettingsForAngular($UserID)
    {
        $AppSettings = [];
        foreach (static::$AvailableAppSettings as $AppSetting => $Details) {
            $AppSettings[$Details['angularApiField']] = VarAppSettings::GetAppSetting(
                $UserID,
                $Details['value']
            );
        }
        $AppSettings['EntityMetaLabelsEnabled'] = true;
        $AppSettings['EntityNotesEnabled'] = true;
        return $AppSettings;
    }
}

<?php

namespace App\Klicktipp;

use App\Klicktipp\Cli\CliCronSend;
use App\Klicktipp\Customfield\Queue\CustomfieldQueueWorker;
use App\Klicktipp\LandingPage\DomainCheck\LandingPageDomainCheckWorker;
use App\Klicktipp\PreGeneration\Exception\PreGenerationException;
use App\Klicktipp\Subscriber\MoveSubscriberWorker;
use App\Klicktipp\Tag\Queue\TagQueueWorker;
use App\Tests\Integration\Klicktipp\ProcessLogTest;
use PDOException;
use Pheanstalk\Exception as PheanstalkException;

class QueueCreator
{
    /**
     * $data = array(
     *   'RelOwnerUserID' => ...,
     *   'CampaignID' => ...,
     *   'TimeToSend' => ...,
     *   'IsSplitTest' => ...,
     *   'TotalActive' => ...,
     *   'Size2ndTranche' => ...,
     *   'QueueOffset' => ...,
     *   'QueueSlice' => ...,
     * );
     */
    public static function createQueueWorker(array $data): void
    {
        ProcessLog::QueueItemStarted($data);

        $ObjectCampaign = Campaigns::FromID($data['RelOwnerUserID'], $data['CampaignID']);
        if (empty($ObjectCampaign)) {
            $data['message'] = 'QueueCreator::createQueueWorker campaign not found';
            ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
            return;
        }

        // worker data (for first slice)
        $jobdata = array(
            'RelOwnerUserID' => $data['RelOwnerUserID'],
            'CampaignID' => $data['CampaignID'],
            'TimeToSend' => $data['TimeToSend'],
            'IsSplitTest' => $data['IsSplitTest'],
            'Size2ndTranche' => $data['Size2ndTranche'],
            'TotalActive' => $data['TotalActive'],
            '#offset' => $data['QueueOffset'],
            '#size' => $data['QueueSlice'],
        );

        try {
            if (empty($data['#childLogID'])) {
                if ($ObjectCampaign->GetData('SendToOnlyOneSubscription')) {
                    $data['#childLogID'] = ProcessLog::Create('send_queue_to_one', $data['CampaignID']);
                } else {
                    $data['#childLogID'] = ProcessLog::Create('send_queue', $data['CampaignID']);
                }
            }

            if ($data['TotalActive'] < variable_get('klicktipp_sq_slice_min', 1000)) {
                // dont use the queue for "small" accounts
                $jobdata = ProcessLog::AddItem($data['#childLogID'], $jobdata);
                // process immediately
                SendQueue::QueueWorker($jobdata, time(), 0);
            } else {
                // create a queue job for every chunk "slice"
                $offset = $data['QueueOffset'];
                while ($offset < $data['TotalActive']) {
                    $data['QueueOffset'] = $offset;
                    $jobdata['#offset'] = $offset;
                    if ($offset + $data['QueueSlice'] > $data['TotalActive']) {
                        // dont make the last chunk smaller than the other chunks
                        $jobdata['#size'] = 2 * $data['QueueSlice'];
                        // make it the last chunk
                        $offset = $data['TotalActive'] + 1;
                    } else {
                        $offset += $data['QueueSlice'];
                    }
                    ProcessLog::AddQueueItem($data['#childLogID'], $jobdata);
                }
            }
        } catch (PDOException | PheanstalkException $e) {
            // we got a db or beanstalk error, maybe a connection timeout -> try to requeue
            ProcessLog::ReQueueItem('create_queue', $data);
            return;
        } catch (PreGenerationException $e) {
            ProcessLog::QueueItemFailed(ProcessLog::FAILED_PREGENERATION_ERROR, $data);
            return;
        }

        // Log the process
        ProcessLog::QueueItemProcessed($data);
    }

    /**
     * Implementation for Drupal hook_cron_queue_info
     *
     * Note: In Symfony this is only used for unit test (migration simpletest)
     *       TODO: Not all queue workers have been moved to Symfony yet since not all simpletests have been migrated
     *       The Queue System in Symfony will be different, so this function will be migrated
     *
     * Parameters:
     * 'worker callback' => the worker function
     * 'klicktipp_finished' => callback for finished batch jobs (default: _klicktipp_batch_finished)
     * 'klicktipp_batchlimit' => limit (of data items) to execute interactive, leave 0 to always process in drupal queue
     * 'klicktipp_queue_slice' => number of data items processed per queue job
     * 'klicktipp_notify' => notify user if processed finished (e.g. called with batch_set)
     * 'klicktipp_title' => title to show if interactive start (queue or batch)
     * 'klicktipp_init_message' => first message to show if interactive
     * 'klicktipp_progress_message' => progress message to show if interactive
     * 'klicktipp_error_message' =>  => error message to show if interactive
     *
     * @param bool|null $forSymfonyUnitTestOnly
     *      at least one queue is only used in Symfony Unit test and will fail in Drupal
     *
     * @return array
     */
    public static function queueInfo(?bool $forSymfonyUnitTestOnly = false): array
    {
        // ---------------------------- tag_subscribers, untag_subscribers
        $queues['tag_subscribers'] = array(
            'worker callback' => 'klicktipp_subscriber_tagging_queue',
            // klicktipp params
            'klicktipp_batchlimit' => 200, //200 taggings
            'klicktipp_queue_slice' => 200, //200 taggings per queue job
            'klicktipp_title' => t('Processing subscriber tagging.'),
            'klicktipp_init_message' => t('Initializing subscriber tagging.'),
            'klicktipp_progress_message' => t('Completed @current of @total taggings.'),
        );
        $queues['untag_subscribers'] = array(
            'worker callback' => 'klicktipp_subscriber_tagging_queue',
            // klicktipp params
            'klicktipp_batchlimit' => 200, //200 untaggings
            'klicktipp_queue_slice' => 200, //200 untaggings per queue job
            'klicktipp_title' => t('Processing subscriber untagging.'),
            'klicktipp_init_message' => t('Initializing subscriber untagging.'),
            'klicktipp_progress_message' => t('Completed @current of @total untaggings.'),
        );

        // ----------------------------
        // switch_multivalue_taggings, switch_multivalue_to_singlevalue_taggings, switch_multivalue_custom_fields
        $queues['switch_multivalue_taggings'] = array(
            'worker callback' => 'switch_multivalue_taggings_queue',
            // klicktipp params
            'klicktipp_batchlimit' => 50000, // 50000 subscribers
            'klicktipp_queue_slice' => 1000, // 1000 subscribers per queue job
            'klicktipp_title' => t('Processing switch to multivalue tagging.'),
            'klicktipp_init_message' => t('Initializing switch to multivalue tagging.'),
            'klicktipp_progress_message' => t('Completed @current of @total taggings.'),
        );
        $queues['switch_multivalue_to_singlevalue_taggings'] = array(
            'worker callback' => [new TagQueueWorker(), 'switchMultiToSingleValue'],
        );
        $queues['switch_multivalue_custom_fields'] = array(
            'worker callback' => 'switch_multivalue_custom_fields_queue',
            // klicktipp params
            'klicktipp_batchlimit' => 50000, // 50000 subscribers
            'klicktipp_queue_slice' => 1000, // 1000 subscribers per queue job
            'klicktipp_title' => t('Processing switch to multivalue field.'),
            'klicktipp_init_message' => t('Initializing switch to multivalue field.'),
            'klicktipp_progress_message' => t('Completed @current of @total fields.'),
        );

        $queues['switch_multivalue_to_singlevalue_custom_fields'] = array(
            'worker callback' => [new CustomfieldQueueWorker(), 'switchMultiToSingleValue'],
        );


        // ---------------------------- delete_subscribers
        $queues['delete_subscribers'] = array(
            'worker callback' => 'klicktipp_subscriber_deletion_queue',
            // klicktipp params
            'klicktipp_batchlimit' => 2000, // upper limit for interactive import with progress bar
            'klicktipp_queue_slice' => 100, //100 deletions per queue job
            'klicktipp_title' => t('Processing subscriber deletion.'),
            'klicktipp_init_message' => t('Initializing subscriber deletion.'),
            'klicktipp_progress_message' => t('Completed @current of @total deletions.'),
        );

        // ---------------------------- delete_subscriptions
        $queues['delete_subscriptions'] = array(
            'worker callback' => 'klicktipp_subscription_deletion_queue',
            // klicktipp params
            'klicktipp_batchlimit' => 2000, // upper limit for interactive import with progress bar
            'klicktipp_queue_slice' => 100, //100 deletions per queue job
            'klicktipp_title' => t('Processing subscription deletion.'),
            'klicktipp_init_message' => t('Initializing subscription deletion.'),
            'klicktipp_progress_message' => t('Completed @current of @total subscription deletions.'),
        );

        // ------- detect duplicates by disconvering similar email addresses --------
        $queues['email_similarity'] = [
            'worker callback' => SubscriberDuplicateDetectionEmailSimilarity::class . '::detectAndStore'
        ];

        // ---------------------------- import_subscribers
        $queues['import_subscribers'] = array(
            'worker callback' => 'klicktipp_subscriber_import_queue',
            // klicktipp params
            'klicktipp_finished' => '_klicktipp_subscriber_import_batch_finished_wrapper',
            'klicktipp_batchlimit' => 2000, // upper limit for interactive import with progress bar
            'klicktipp_queue_slice' => 100, //100 imports per queue job
            'klicktipp_title' => t('Processing subscriber import.'),
            'klicktipp_init_message' => t('Initializing subscriber import.'),
            'klicktipp_progress_message' => t('Completed @current of @total imports.'),
        );

        // ---------------------------- send_queue
        $queues['send_queue'] = array(
            'worker callback' => SendQueue::class . '::QueueWorker',
        );
        $queues['send_queue_to_one'] = array(
            // "send to only one subscription": same worker, different tube
            'worker callback' => SendQueueToOne::class . '::QueueWorker',
        );

        // ---------------------------- send nexmo sms
        $queues['send_nexmo_sms'] = array(
            'worker callback' => SendQueue::class . '::sendNexmoSmsQueue',
        );

        // ---------------------------- send twilio sms
        $queues['send_twilio_sms'] = array(
            'worker callback' => SendQueue::class . '::sendTwilioSmsQueue',
        );

        // ---------------------------- create_queue
        $queues['create_queue'] = array(
            'worker callback' => QueueCreator::class . '::createQueueWorker',
        );

        // ---------------------------- transactional_queue
        $queues['transactional_queue'] = array(
            'worker callback' => TransactionalQueue::class . '::QueueWorker',
        );

        // ---------------------------- autoresponder_queue
        $queues['autoresponder_queue'] = array(
            'worker callback' => AutoresponderQueue::class . '::QueueWorker',
        );

        // ---------------------------- newsletter_queue
        $queues['newsletter_queue'] = array(
            'worker callback' => NewsletterQueue::class . '::QueueWorker',
        );

        // ---------------------------- newsletter_pregeneration_queue
        $queues[NewsletterPreGenerationQueue::QUEUENAME] = array(
            'worker callback' => NewsletterPreGenerationQueue::class . '::QueueWorker',
        );

        // ---------------------------- newsletter_pregenerated_queue
        $queues[NewsletterPregeneratedQueue::QUEUENAME] = array(
            'worker callback' => NewsletterPregeneratedQueue::class . '::QueueWorker',
        );

        // ---------------------------- subscriber_queue
        $queues['subscriber_queue'] = array(
            'worker callback' => SubscriberQueue::class . '::highPriorityQueue',
        );

        // ---------------------------- subscriber_queue low prio
        $queues['subscriber_lowprio_queue'] = array(
            'worker callback' => SubscriberQueue::class . '::lowPriorityQueue',
        );

        // ---------------------------- winner_queue
        $queues['winner_queue'] = array(
            'worker callback' => CliCronSend::class . '::winnerQueueWorker',
        );

        // ---------------------------- move_customers_in_separate_slices
        $queues['move_customers_in_separate_slices'] = array(
            'worker callback' => 'move_customers_in_separate_slices_queue',
            // klicktipp params
            'klicktipp_batchlimit' => 100, //100 customers
            'klicktipp_queue_slice' => 1, //1 customer per queue job
            'klicktipp_title' => t('Processing user move to/from own slices.'),
            'klicktipp_init_message' => t('Initializing user move to/from own slices.'),
            'klicktipp_progress_message' => t('Completed @current of @total users.'),
        );

        // ---------------------------- bouncecheck
        $queues['bouncecheck'] = array(
            'worker callback' => '_klicktipp_bouncecheck_queue',
        );
        $queues['softbouncecheck'] = array(
            'worker callback' => '_klicktipp_softbouncecheck_queue',
        );

        // ---------------------------- whitelabeldomaincheck
        $queues['whitelabeldomaincheck'] = array(
            'worker callback' => DomainSet::class . '::CheckWhitelabelDomain',
        );

        // ---------------------------- landing_page_domain_check
        $queues['landing_page_domain_check'] = array(
            'worker callback' => [new LandingPageDomainCheckWorker(), 'execute'],
        );

        // ---------------------------- tag_passive_subscribers
        $queues['tag_passive_subscribers'] = array(
            'worker callback' => VarAgedSubscribers::class . '::InactiveSubscribersQueueWorker',
        );

        // ---------------------------- senderscore
        $queues['senderscore_queue'] = array(
            'worker callback' => '_klicktipp_senderscore_queue',
        );

        // ---------------------------- zy0de_queue
        $queues['zy0de_queue'] = array(
            'worker callback' => '_klicktipp_zy0de_queue',
        );

        // ---------------------------- request_forwarders_queue
        $queues['request_forwarders_queue'] = array(
            'worker callback' => '_klicktipp_request_forwarders_queue',
        );

        // ---------------------------- newsletter delete
        $queues['delete_newsletter_queue'] = array(
            'worker callback' => '_klicktipp_delete_newsletter_queue',
        );

        // ---------------------------- taggings delete
        $queues['delete_taggings'] = array(
            'worker callback' => '_klicktipp_delete_taggings',
        );

        // ---------------------------- export subscribers
        $queues['export_subscribers'] = array(
            'worker callback' => '_klicktipp_subscriber_export_queue',
        );

        // ---------------------------- import subscribers
        $queues['import_subscribers_validate'] = array(
            'worker callback' => VarImport::class . '::ValidationQueueWorker',
        );

        $queues['import_subscribers_import'] = array(
            'worker callback' => VarImport::class . '::ImportQueueWorker',
        );

        // ---------------------------- user chicklets
        $queues['chicklet_update'] = array(
            'worker callback' => SubscriptionForms::class . '::UpdateChickletS3',
        );

        // ---------------------------- outbound events
        $queues['outbound_event'] = array(
            'worker callback' => ToolOutbound::class . '::OutboundEventWorker',
        );

        // ---------------------------- subscriber_limit_queue
        $queues['subscriber_limit_queue'] = array(
            'worker callback' => '_klicktipp_subscriber_limit_queue',
        );

        // ---------------------------- npscore
        $queues['npscore_queue'] = array(
            'worker callback' => '_klicktipp_npscore_queue',
        );

        // ---------------------------- delete_pending_subscribers_queue
        $queues['delete_pending_subscribers_queue'] = array(
            'worker callback' => '_klicktipp_delete_pending_subscribers_queue',
        );

        // ---------------------------- stop_inactive_users_queue
        $queues['stop_inactive_users_queue'] = array(
            'worker callback' => '_klicktipp_stop_inactive_users_queue',
        );

        // ---------------------------- aggregate_lead_value_from_payments
        $queues['aggregate_lead_value_from_payments'] = array(
            'worker callback' => '_klicktipp_aggregate_lead_value_from_payments_queue',
        );

        // ---------------------------- active_payments
        $queues['active_payments_queue'] = array(
            'worker callback' => '_klicktipp_active_payments_queue',
        );

        $queues['export_subscriber_duplicates'] = array(
            'worker callback' => '_klicktipp_subscriber_duplicates_export_queue',
        );

        $queues['move_subscribers'] = array(
            'worker callback' => [new MoveSubscriberWorker(), 'moveSubscribers'],
        );

        // ----------------- sync of addons with digistore --------------------------------

        $queues['addon_status_sync'] = array(
            'worker callback' => '_klicktipp_addon_status_sync_queue',
        );

        if ($forSymfonyUnitTestOnly) {
            // this queue is only used in the symfony unit test and
            // leads to an error in the Druapl simpletests if present

            // --- TODO: ProcessLogTest.php
            $queues['processlog_test_queue'] = [
                'worker callback' => ProcessLogTest::class . '::processLogTestQueueWorker',
            ];
        }

        return $queues;
    }
}

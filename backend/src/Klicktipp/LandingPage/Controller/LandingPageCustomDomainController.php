<?php

namespace App\Klicktipp\LandingPage\Controller;

use App\Klicktipp\AngularApi\ValueObject\Response\ApiResponseErrorValueObject;
use App\Klicktipp\DomainSet;
use App\Klicktipp\DomainUtils;
use App\Klicktipp\LandingPage\Mapper\LandingPageResponseCustomDomainFilterOptionsMapper;
use App\Klicktipp\LandingPage\Validators\Exception\LandingPageValidationDuplicateDomainNameException;
use App\Klicktipp\LandingPage\Validators\Exception\LandingPageValidationInvalidDomainNameException;
use App\Klicktipp\LandingPage\Validators\InvalidCustomDomainLandingPageValidator;
use Doctrine\DBAL\Exception;
use stdClass;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;

class LandingPageCustomDomainController extends AbstractController
{
    private InvalidCustomDomainLandingPageValidator $landingPageCustomDomainValidator;

    public function __construct(
        InvalidCustomDomainLandingPageValidator $landingPageCustomDomainValidator
    ) {
        $this->landingPageCustomDomainValidator = $landingPageCustomDomainValidator;
    }

    /**
     * @param stdClass $account
     * @param LandingPageResponseCustomDomainFilterOptionsMapper $landingPageResponseCustomDomainFilterOptionsMapper
     * @param array<mixed> $data
     * @return JsonResponse
     * @throws Exception
     */
    public function create(
        stdClass $account,
        LandingPageResponseCustomDomainFilterOptionsMapper $landingPageResponseCustomDomainFilterOptionsMapper,
        array $data
    ): JsonResponse {
        $userID = $account->uid;
        $customDomainName = strtolower(trim(check_plain($data['domain']), ". \t\n\r\0\x0B"));

        if ($landingPageResponseCustomDomainFilterOptionsMapper->domainLimitReached($account)) {
            return new JsonResponse(
                [
                    ApiResponseErrorValueObject::create(
                        'landing_page::domain_limit_reached',
                        t('LandingPage::Api::Error::The limit of custom domains has been reached.')
                    )
                ],
                406
            );
        }

        try {
            $this->landingPageCustomDomainValidator->validate(
                $customDomainName
            );
        } catch (LandingPageValidationInvalidDomainNameException $e) {
            return new JsonResponse(
                [
                    ApiResponseErrorValueObject::create(
                        'landing_page::invalid_domain',
                        t('LandingPage::Api::Error::The custom domain name is invalid.')
                    )
                ],
                406
            );
        } catch (LandingPageValidationDuplicateDomainNameException $e) {
            return new JsonResponse(
                [
                    ApiResponseErrorValueObject::create(
                        'landing_page::duplicate_domain',
                        t('LandingPage::Api::Error::The custom domain name already exists.')
                    )
                ],
                406
            );
        }

        $customDomainName = DomainUtils::punycodeDomain($customDomainName);

        if (!$customDomainName) {
            return new JsonResponse(
                [
                    ApiResponseErrorValueObject::create(
                        'landing_page::invalid_domain',
                        t('LandingPage::Api::Error::The custom domain name is invalid.')
                    )
                ],
                406
            );
        }

        DomainSet::CreateWhitelabelDomain($userID, $customDomainName);

        $updateData = [
            'Status' => 0,
            'validFrom' => 0,
            'RelOwnerUserID' => $userID,
            'Data' => [
                'dkimOnly' => true,
                'alias' => '',
                'hosts' => [],
                'hostdata' => [],
            ]
        ];
        DomainSet::UpdateWhitelabelDomain($customDomainName, $updateData);

        $customDomain = (array) DomainSet::RetrieveDomain($userID, $customDomainName);

        $data = [
            'data' => [
                'newDomain' => $customDomain['DomainID'],
                'filterOptions' => [
                    'customDomainOptions' =>
                        $landingPageResponseCustomDomainFilterOptionsMapper->getCustomDomainOptions(
                            $account
                        ),
                ]
            ]
        ];
        return new JsonResponse($data);
    }
}

<?php

namespace App\Klicktipp;

/*
 * Marketing Tool subclass for Countdown
 */

class ToolCountdown extends MarketingTools
{
    const TERMINATION_TYPE_FIXED = 1;
    const TERMINATION_TYPE_FROM_CUSTOMFIELD = 2;
    const TERMINATION_TYPE_FROM_TAGGING = 3;

    public static $ToolType = MarketingTools::TOOL_TYPE_COUNTDOWN;

    //translatable countdown termination type names
    public static $DisplayTerminationType = array(
        ToolCountdown::TERMINATION_TYPE_FIXED => /*t(*/'Fixed date'/*)*/,
        ToolCountdown::TERMINATION_TYPE_FROM_CUSTOMFIELD => /*t(*/'Date from custom field'/*)*/,
        ToolCountdown::TERMINATION_TYPE_FROM_TAGGING => /*t(*/'Date of tagging'/*)*/,
    );

    public static $DefaultDataFields = array(
        'Template' => 'round_orange',
        'TerminationType' => ToolCountdown::TERMINATION_TYPE_FIXED,
        'TerminationDateTime' => 0,
        'TerminationCustomFieldID' => 0,
        'TerminationTagIDs' => array(),
        'TerminationTagDuration' => 0,
        'UseCustomExpiredImage' => 0,
        'CustomExpiredImage' => '',
        'UseTargetURL' => 1,
        'TargetURL' => '',
        'TargetURLExpired' => '',
        'UseSubscriberParameter' => 1,
        'UseEmailParameter' => 1,
        'SubscriberParameterName' => '',
        'EmailParameterName' => '',
        'Notes' => ''
    );

    public static $APIIndexFilterTypes = array(
        MarketingTools::TOOL_TYPE_COUNTDOWN => 'countdown',
    );

    public static function FromID($UserID, $SerialID)
    {
        if (empty($UserID) || empty($SerialID)) {
            return false;
        }

        //retrieve countdown only if not marked as deleted
        $result = db_query(
            "SELECT * FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID AND ToolID = :ToolID AND Deleted = :NotDeleted",
            array(
                ':RelOwnerUserID' => $UserID,
                ':ToolID' => $SerialID,
                ':NotDeleted' => MarketingTools::TOOL_DELETE_STATUS_NOTDELETED
            )
        );
        $DBArray = kt_fetch_array($result);

        if (empty($DBArray)) {
            return false;
        }

        return static::FromArray($DBArray);
    }

    public static function FromID_IncludeDeleted($UserID, $SerialID)
    {
        return parent::FromID($UserID, $SerialID);
    }

    public static function FromArray($DBArray)
    {
        $countdown = new ToolCountdown($DBArray);

        if (empty($countdown->DataFlat)) {
            return false;
        }

        return $countdown;
    }

    //CountDowns won't be physically deleted since they could be still embedded in emails
    //override parent static methode
    //set the Deleted flag to 1 -> invisible to user but the data set is still in the database
    public static function DeleteDB($Data)
    {
        if (empty($Data['RelOwnerUserID']) || empty($Data['ToolID'])) {
            return false;
        }

        //set deleted flag but do not actually delete since there could be still links out there
        db_query(
            "UPDATE {marketing_tools} SET Deleted = :Deleted WHERE RelOwnerUserID = :RelOwnerUserID AND ToolID = :ToolID",
            array(
                ':Deleted' => MarketingTools::TOOL_DELETE_STATUS_DELETED,
                ':RelOwnerUserID' => $Data['RelOwnerUserID'],
                ':ToolID' => $Data['ToolID'],
            )
        );

        //Note: parent::DeleteDB is not called so we have to remove labels and the name "manually"

        //remove labelings from labeled table
        MetaLabels::RemoveAllLabelsFromEntity($Data['RelOwnerUserID'], static::$EntityBaseType, $Data['ToolID']);

        //remove name from named table
        DatabaseNamedTable::DeleteName($Data['RelOwnerUserID'], static::$EntityBaseType, $Data['ToolID']);

        return true;
    }

    // --- object functions ---

    /**
     * Calculate the remaining time of the countdown
     *
     * @param $SubscriberID
     * @param $ReferenceID
     *
     * @return int: time in seconds
     */
    public function GetTime($SubscriberID, $ReferenceID)
    {
        $UserID = $this->DataFlat['RelOwnerUserID'];

        if ($this->DataFlat['TerminationType'] == ToolCountdown::TERMINATION_TYPE_FROM_CUSTOMFIELD) {
            $FieldInformation = CustomFields::RetrieveCustomField($this->DataFlat['TerminationCustomFieldID'], $UserID);
            $datetime = empty($FieldInformation) ? 0 : CustomFields::GetCustomFieldData(
                $UserID,
                $SubscriberID,
                $FieldInformation,
                $ReferenceID
            );
        } elseif ($this->DataFlat['TerminationType'] == ToolCountdown::TERMINATION_TYPE_FROM_TAGGING) {
            $SubscriptionDate = 0;
            foreach ($this->DataFlat['TerminationTagIDs'] as $TagID) {
                $Tagging = Subscribers::RetrieveTagging($UserID, $TagID, $SubscriberID, $ReferenceID);
                if ($Tagging['SubscriptionDate'] > $SubscriptionDate) {
                    //for multiple tags, the latest tagging will be used
                    $SubscriptionDate = $Tagging['SubscriptionDate'];
                }
            }

            $datetime = (empty($SubscriptionDate)) ? 0 : $SubscriptionDate + $this->DataFlat['TerminationTagDuration'];
        } else {
            $datetime = $this->DataFlat['TerminationDateTime'];
        }

        return (int)$datetime - time();
    }

    public function GetEditURL($CampaignID = 0)
    {
        return APP_URL . "tools/{$this->DataFlat['RelOwnerUserID']}/countdown/{$this->DataFlat['ToolID']}/edit";
    }

    public static function GetAddURL($UserID, $data = '')
    {
        return APP_URL . "tools/$UserID/countdown/add";
    }

    /**
     * Check if an entity is used in this object
     * @param $EntityID
     * @param $EntityClass
     * @param string $Op action to check dependencies for
     * @param bool $forAngular
     * @return array (
     *     EntityID => Edit link
     * )
     */
    public function GetDependencies($EntityID, $EntityClass, $Op = 'delete', bool $forAngular = false)
    {
        if (empty($EntityID) || empty($EntityClass)) {
            return array();
        }
        //Note: for payments we do not need to call parent::GetDependencies() since AssignedTagID and RelListID are not used
        $dependencies = array();

        $ToolID = $this->GetData('ToolID');
        $Name = $this->GetData('Name');

        switch ($EntityClass) {
            case Tag::class:
                //manual tags assigned as optional taggings

                $Tags = $this->GetData('TerminationTagIDs');

                foreach ($Tags as $TagID) {
                    if ($TagID == $EntityID) {
                        if ($forAngular) {
                            $dependencies[/*t(*/'dependency:countdown:TerminationTag'/*)*/][$ToolID] = [
                                'id' => $ToolID,
                                'type' => static::getApiType(),
                                'name' => $Name,
                                'editLink' => $this->GetEditURL()
                            ];
                        } else {
                            $dependencies[/*t(*/'dependency:countdown:TerminationTag'/*)*/][$ToolID] = l(
                                $Name,
                                $this->GetEditURL(),
                                array('attributes' => array('target' => '_blank'))
                            );
                        }
                    }
                }

                break;
            case CustomFields::class:
                //DOI processes

                if ($this->GetData('TerminationCustomFieldID') == $EntityID) {
                    if ($forAngular) {
                        $dependencies[/*t(*/'dependency:countdown:TerminationTag'/*)*/][$ToolID] = [
                            'id' => $ToolID,
                            'type' => static::getApiType(),
                            'name' => $Name,
                            'editLink' => $this->GetEditURL()
                        ];
                    } else {
                        $dependencies[/*t(*/'dependency:countdown:CustomField'/*)*/][$ToolID] = l(
                            $Name,
                            $this->GetEditURL(),
                            array('attributes' => array('target' => '_blank'))
                        );
                    }
                }

                break;
        }

        return $dependencies;
    }

    /**
     * determine url parameters for redirect url
     * user can select if he wants to transfer SubscriberID and EmailAddress
     * legacy and default parameters are: SubscriberID, listid, email
     * we need to keep the legacy parameters as customers' scripts may still rely on them
     *
     * @param $ArrayCountdown
     * @param $FullSubscriber
     *
     * @return array
     */
    public static function GetRedirectParameters($ArrayCountdown, $FullSubscriber)
    {
        if (!isset($ArrayCountdown['UseTargetURL'])) {
            // user has never specifically selected which parameters to use, so transfer legacy parameters
            return array(
                'SubscriberID' => $FullSubscriber['SubscriberID'],
                'listid' => $FullSubscriber['ListID'],
                'email' => $FullSubscriber['EmailAddress'],
            );
        }
        $Parameters = array();
        if (!empty($ArrayCountdown['UseSubscriberParameter']) && !empty($ArrayCountdown['SubscriberParameterName'])) {
            $Parameters[$ArrayCountdown['SubscriberParameterName']] = $FullSubscriber['SubscriberID'];
        }
        if (!empty($ArrayCountdown['UseEmailParameter']) && !empty($ArrayCountdown['EmailParameterName'])) {
            $Parameters[$ArrayCountdown['EmailParameterName']] = $FullSubscriber['EmailAddress'];
        }
        return $Parameters;
    }

    /**
     * @return string
     */
    public function getPreviewImage(): string
    {
        $baseUrl = klicktipp_get_setting('marketing_tools_emailcountdown_ckeditor_preview_image');
        return "$baseUrl/" . $this->GetData('Template') . '.gif';
    }

    public static function createNewInstance(int $userId): self
    {
        $dbArray = array_merge(self::$DefaultFields, [
            'Name' => '',
            'ToolID' => 0,
            'ToolType' => self::$ToolType,
            'RelOwnerUserID' => $userId
        ]);
        return new self($dbArray);
    }
}

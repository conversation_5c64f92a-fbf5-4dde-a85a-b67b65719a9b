<?php

namespace App\Klicktipp;

class ClickBank extends PaymentIPNs
{
    // To make the Link encryption for Clickbank stronger, use this as BuildID param
    const IPN_PASSPHRASE = 77;

    const EVENT_PAYMENT = 'SALE'; // The purchase of a standard product or the initial purchase of recurring billing product.
    const EVENT_PAYMENT_TEST = 'TEST_SALE';
    const EVENT_REBILL = 'BILL'; // A rebill for a recurring billing product.
    const EVENT_REFUND = 'RFND'; // The refunding of a standard or recurring billing product. Recurring billing products that are refunded also result in a "CANCEL-REBILL" action.
    const EVENT_CHARGEBACK = 'CGBK'; // A chargeback for a standard or recurring product.
    const EVENT_ECHECK_CHARGEBACK = 'INSF'; // An eCheck chargeback for a standard or recurring product.
    const EVENT_REBILL_CANCELED = 'CANCEL-REBILL'; // The cancellation of a recurring billing product. Recurring billing products that are canceled do not result in any other action.
    const EVENT_REBILL_RESUMED = 'UNCANCEL-REBILL'; // Reversing the cancellation of a recurring billing product.
    const EVENT_TEST = 'TEST';

    public static $ListbuildingType = Listbuildings::TYPE_CLICKBANK;

    //BuildType => allowed types in index_advanced filter and readable api name for customers
    public static $APIIndexFilterTypes = array(
        Listbuildings::TYPE_CLICKBANK => 'payment-clickbank',
    );

    public static $DefaultDataFields = array(
        'OptInSubscribeTo' => array( //assign tags based on payment event
            ClickBank::EVENT_PAYMENT => 0,
            ClickBank::EVENT_REFUND => 0,
            ClickBank::EVENT_CHARGEBACK => 0,
            ClickBank::EVENT_REBILL_CANCELED => 0,
            ClickBank::EVENT_REBILL_RESUMED => 0,
        ),
        'ProductID' => 0,
        'SmartTagID' => 0,
        'RefundSmartTagID' => 0,
        'ChargebackSmartTagID' => 0,
        'RebillCanceledSmartTagID' => 0,
        'RebillResumedSmartTagID' => 0,
        'PaymentCompletedSmartTagID' => 0,
        'PaymentExpiredSmartTagID' => 0,
        'InvoiceEmailID' => 0,
        'LeadValueFrom' => PaymentIPNs::LEADVALUE_FROM_DEFAULT,
        'SubscriptionLength' => PaymentIPNs::SUBSCRIPTION_LIFETIME,
        'RequirePaidBillingStatus' => 0,
    );

    public static function FromArray($DBArray)
    {
        $listbuilding = new ClickBank($DBArray);

        if (empty($listbuilding->DataFlat)) {
            return false;
        }

        return $listbuilding;
    }

    public static function InsertDB($Data)
    {
        $Data['BuildType'] = static::$ListbuildingType;

        $BuildID = parent::InsertDB($Data);
        if (empty($BuildID)) {
            return false;
        }

        // write all additional data with an update query (as we need the BuildID for that)

        /** @var Listbuildings $ObjectListbuilding */
        $ObjectListbuilding = static::FromID($Data['RelOwnerUserID'], $BuildID);
        $Data = $ObjectListbuilding->GetData();

        $Data['RefundSmartTagID'] = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_LISTBUILDING_REFUNDED,
            'EntityID' => $BuildID,
            'RelOwnerUserID' => $Data['RelOwnerUserID'],
        ));
        $Data['ChargebackSmartTagID'] = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_LISTBUILDING_CHARGEDBACK,
            'EntityID' => $BuildID,
            'RelOwnerUserID' => $Data['RelOwnerUserID'],
        ));
        $Data['RebillCanceledSmartTagID'] = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED,
            'EntityID' => $BuildID,
            'RelOwnerUserID' => $Data['RelOwnerUserID'],
        ));
        $Data['RebillResumedSmartTagID'] = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_LISTBUILDING_REBILL_RESUMED,
            'EntityID' => $BuildID,
            'RelOwnerUserID' => $Data['RelOwnerUserID'],
        ));

        static::UpdateDB($Data);

        return $BuildID;
    }

    public static function DeleteDB($Data)
    {
        // make sure, we have all data available (not required for general DeleteDB)
        /** @var Listbuildings $ObjectListbuilding */
        $ObjectListbuilding = static::FromID($Data['RelOwnerUserID'], $Data['BuildID']);
        if (!$ObjectListbuilding) {
            return false;
        }
        $Data = $ObjectListbuilding->GetData();

        $result = parent::DeleteDB($Data);
        if (!$result) {
            return false;
        }

        Tag::DeleteTag($Data['RefundSmartTagID'], $Data['RelOwnerUserID']);
        Tag::DeleteTag($Data['ChargebackSmartTagID'], $Data['RelOwnerUserID']);
        Tag::DeleteTag($Data['RebillCanceledSmartTagID'], $Data['RelOwnerUserID']);
        Tag::DeleteTag($Data['RebillResumedSmartTagID'], $Data['RelOwnerUserID']);

        return $result;
    }

    public static function FromMerchantAndProductID($MerchantName, $ProductID = 0)
    {
        // enforce the default empty ProductID
        if (empty($ProductID)) {
            $ProductID = '';
        }

        // Note: ORDER BY solution is not possible here: ProductID is in Data Field
        $query = "SELECT * FROM {listbuilding} WHERE BuildType = :BuildType AND VarcharIndexed = :VarcharIndexed";
        $params = array(
            ':BuildType' => static::$ListbuildingType,
            ':VarcharIndexed' => $MerchantName,
        );
        $result = db_query($query, $params);

        $returnDBArray = array();
        while ($DBArray = kt_fetch_array($result)) {
            $data = unserialize((string) $DBArray['Data']);

            // prefer the concrete product when requested
            if ($data['ProductID'] == $ProductID) {
                $returnDBArray = $DBArray;
                break;
            }
            // catchall is only for cases when no product is found
            if ($data['ProductID'] == '') {
                $returnDBArray = $DBArray;
            }
        }

        if (empty($returnDBArray)) {
            return false;
        }

        return static::FromArray($returnDBArray);
    }

    public static function CheckDuplicate($UserID, $MerchantName, $ProductID = 0)
    {
        // enforce the default empty ProductID
        if (empty($ProductID)) {
            $ProductID = '';
        }

        //check if the merchant name already exists in another account
        if (static::CheckDuplicateMerchant($UserID, $MerchantName)) {
            return true;
        }

        $ObjectProduct = static::FromMerchantAndProductID($MerchantName, $ProductID);
        if (empty($ObjectProduct)) {
            return false;
        }

        $data = $ObjectProduct->GetData();

        return $data['ProductID'] == $ProductID;
    }

    public static function CreateApiKey($UserID)
    {
        return strtoupper(substr(hash('sha256', $UserID . '_likilsk878_' . Hash::MINIHASHSECRET), 0, 16));
    }

    public function GetEditURL($CampaignID = 0)
    {
        return APP_URL . "listbuilding/{$this->DataFlat['RelOwnerUserID']}/clickbank/{$this->DataFlat['BuildID']}/edit";
    }

    public static function GetAddURL($UserID, $data = '')
    {
        return APP_URL . "listbuilding/$UserID/clickbank/add";
    }

    public function GetSmartTags($forConditions = false)
    {
        if ($forConditions) {
            return array(
                'bought' => $this->GetData('SmartTagID'),
                'refunded' => $this->GetData('RefundSmartTagID'),
                'chargedback' => $this->GetData('ChargebackSmartTagID'),
                'canceled' => $this->GetData('RebillCanceledSmartTagID'),
                'resumed' => $this->GetData('RebillResumedSmartTagID'),
                'completed' => $this->GetData('PaymentCompletedSmartTagID'),
                'expired' => $this->GetData('PaymentExpiredSmartTagID'),
            );
        }

        return array(
            'SmartTagID' => $this->GetData('SmartTagID'),
            'RefundSmartTagID' => $this->GetData('RefundSmartTagID'),
            'ChargebackSmartTagID' => $this->GetData('ChargebackSmartTagID'),
            'RebillCanceledSmartTagID' => $this->GetData('RebillCanceledSmartTagID'),
            'RebillResumedSmartTagID' => $this->GetData('RebillResumedSmartTagID'),
            'PaymentCompletedSmartTagID' => $this->GetData('PaymentCompletedSmartTagID'),
            'PaymentExpiredSmartTagID' => $this->GetData('PaymentExpiredSmartTagID'),
        );
    }
}

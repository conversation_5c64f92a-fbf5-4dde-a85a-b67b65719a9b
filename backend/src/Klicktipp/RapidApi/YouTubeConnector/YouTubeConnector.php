<?php

namespace App\Klicktipp\RapidApi\YouTubeConnector;

use App\Klicktipp\RapidApi\Exception\RapiApiGuzzleException;
use App\Klicktipp\RapidApi\Exception\RapidApiNoApiKeyException;
use App\Klicktipp\RapidApi\Exception\RapidApiRateLimitException;
use App\Klicktipp\RapidApi\Exception\RapidApiResponseCodeException;
use App\Klicktipp\RapidApi\RapidApiConnector;
use App\Klicktipp\RapidApi\YouTubeConnector\ValueObject\YouTubeConnectorSearchRequestValueObject;
use stdClass;

class YouTubeConnector extends RapidApiConnector
{
    public const RAPIDAPI_HOST = 'youtube-v31.p.rapidapi.com';

    /**
     * @throws RapidApiNoApiKeyException
     * @throws RapiApiGuzzleException
     * @throws RapidApiResponseCodeException
     * @throws RapidApiRateLimitException
     */
    public function search(YouTubeConnectorSearchRequestValueObject $data): stdClass
    {

        $apiKey = variable_get('klicktipp_settings_youtube_api_key', '');

        $url = 'https://youtube-v31.p.rapidapi.com/search';

        $requestData = [];
        foreach (get_object_vars($data) as $key => $value) {
            if (isset($value)) {
                $requestData[$key] = $data->$key;
            }
        }

        return $this->makeGetRequest(
            $apiKey,
            $url,
            self::RAPIDAPI_HOST,
            $requestData
        );
    }

    /**
     * @param string[] $videoIds
     * @throws RapidApiNoApiKeyException
     * @throws RapiApiGuzzleException
     * @throws RapidApiResponseCodeException
     * @throws RapidApiRateLimitException
     */
    public function videos(array $videoIds): stdClass
    {

        $apiKey = variable_get('klicktipp_settings_youtube_api_key', '');

        $url = 'https://youtube-v31.p.rapidapi.com/videos';

        $requestData = [
            'part' => 'statistics,contentDetails',
            'fields' => 'items(id, statistics,contentDetails(duration))',
            'id' => implode(',', $videoIds),
            'maxResults' => '50',
        ];

        return $this->makeGetRequest(
            $apiKey,
            $url,
            self::RAPIDAPI_HOST,
            $requestData
        );
    }

    /**
     * @throws RapidApiNoApiKeyException
     * @throws RapiApiGuzzleException
     * @throws RapidApiResponseCodeException
     * @throws RapidApiRateLimitException
     */
    public function channels(string $searchTerm): stdClass
    {
        $apiKey = variable_get('klicktipp_settings_youtube_api_key', '');

        $url = 'https://youtube-v31.p.rapidapi.com/videos';

        $requestData = [
            'part' => 'id',
            'forUsername' => $searchTerm,
            'fields' => 'items(id)',
            'maxResults' => '50',
        ];

        return $this->makeGetRequest(
            $apiKey,
            $url,
            self::RAPIDAPI_HOST,
            $requestData
        );
    }
}

<?php

namespace App\Klicktipp;

use App\Klicktipp\CSA\CSAException;
use App\Klicktipp\CSA\CSAIpDeleteException;
use App\Klicktipp\DNS\Dmarc\Validator;
use App\Klicktipp\DNS\DnsRecordNotFoundException;
use App\Klicktipp\DNS\DnsResolver;
use App\Klicktipp\Mail\KlicktippMail;
use Exception;
use stdClass;

class DomainSet
{
    public const WHITELABELDOMAIN_STATUS_NEW = 0; // just created or deactivated by support
    public const WHITELABELDOMAIN_STATUS_ACTIVE = 1; // activated by support (dns not verified)
    public const WHITELABELDOMAIN_STATUS_VERIFIED = 2; // verified

    public const EXPECTED_TTL = 3600;

    // nameserver to use if responsible nameserver refuse to answer
    public const FALLBACK_NAMESERVERS = [
        // Google
        '*******',
        '*******',
        // OpenDNS
        '**************',
        '**************'
    ];

    private static Validator $dmarcValidator;

    /**
     * @var bool was a fallback nameserver used for DNS queries ?
     */
    private static $fallbackNameserversUsed = false;

    public static $SharedIpDomains = array(
        'klick-tipp.com',
        'klickmail.com.br',
    );

    protected static $digFunction = [DomainSet::class, 'CallDIG'];

    protected static $keysFunction = [DomainSet::class, 'GetWhitelabelDNSKeys'];

    /**
     * @var int id of marketing account
     */
    protected static $marketingAccountId;

    /**
     * @var bool|null null means uninitialised
     */
    protected static $isSenderMxCheckActive;

    /**
     * @var int tag id to tag subscriber with, if some sender address domains don't have MX record
     */
    protected static $senderMxCheckTagId;

    /**
     * @var array definition of custom field to write sender address without mx record into
     */
    protected static $senderMxCheckCustomField;

    public static function CreateWhitelabelDomain($RelOwnerUserID, $Domain)
    {
        // check required values
        if (empty($RelOwnerUserID) || empty($Domain)) {
            return false;
        }

        $Domainset = kt_fetch_array(
            db_query("SELECT * FROM {whitelabel_domains} WHERE Domain = :Domain", array(':Domain' => $Domain))
        );
        if (!empty($Domainset)) {
            return false;
        }

        // new domain: insert
        $ArrayFieldAndValues = array(
            'Domain' => $Domain,
            'RelOwnerUserID' => $RelOwnerUserID,
            'Status' => DomainSet::WHITELABELDOMAIN_STATUS_NEW,
            'validFrom' => 0,
            'Data' => '',
        );
        kt_insert_row($ArrayFieldAndValues, '{whitelabel_domains}');

        return true;
    }

    /**
     * Retrieves a list of all domains of user (whitelabel and dkim ones)
     *
     * @param int $RelOwnerUserID
     * @param string $Domain if not empty, result contains specified domain only
     * @param bool $considerDkimOnlyDomains if false, response contains ony full whitelabel domains
     *
     * @return array
     */
    public static function RetrieveDomains($RelOwnerUserID = 0, $Domain = '', $considerDkimOnlyDomains = true)
    {
        $ArrayDomains = array();

        if (empty($RelOwnerUserID)) {
            $result = db_query("SELECT * FROM {whitelabel_domains}");
        } else {
            if (empty($Domain)) {
                $result = db_query("SELECT * FROM {whitelabel_domains} WHERE RelOwnerUserID = :RelOwnerUserID", array(
                    ':RelOwnerUserID' => $RelOwnerUserID
                ));
            } else {
                $result = db_query(
                    "SELECT * FROM {whitelabel_domains} WHERE RelOwnerUserID = :RelOwnerUserID AND Domain = :Domain",
                    array(
                        ':RelOwnerUserID' => $RelOwnerUserID,
                        ':Domain' => $Domain
                    )
                );
            }
        }

        while ($Domainset = kt_fetch_array($result)) {
            $Domainset['Data'] = (empty($Domainset['Data'])) ? array() : unserialize((string) $Domainset['Data']);
            if (!$considerDkimOnlyDomains && !empty($Domainset['Data']['dkimOnly'])) {
                continue;
            }
            $ArrayDomains[$Domainset['Domain']] = $Domainset;
        }

        return $ArrayDomains;
    }

    /**
     * Retrieves whitelabel domains of an user
     *
     * @param int $RelOwnerUserID
     * @param string $Domain if not empty, result contains specified domain only
     *
     * @return array
     */
    public static function RetrieveWhitelabelDomains($RelOwnerUserID = 0, $Domain = '')
    {
        return static::RetrieveDomains($RelOwnerUserID, $Domain, false);
    }

    /**
     * Retrieve a sender domain. Result can be a full whitelabel domain or "dkim-only" one
     *
     * @param int $userID
     * @param string $domain
     * @param bool $considerDkimOnlyDomains if false, response contains ony full whitelabel domains
     *
     * @return false|array domainset (if found), otherwise false
     */
    public static function RetrieveDomain($userID, $domain, $considerDkimOnlyDomains = true)
    {
        // check required values
        if (empty($userID) || empty($domain)) {
            return false;
        }

        $domainsets = DomainSet::RetrieveDomains($userID, $domain, $considerDkimOnlyDomains);
        return reset($domainsets);
    }

    /**
     * @param $RelOwnerUserID
     * @param $Domain
     * @param bool $NotifyUser
     * @param string $caller
     * @return mixed
     */
    public static function RetrieveWhitelabelDomain($RelOwnerUserID, $Domain, $NotifyUser = false, $caller = '')
    {
        $Domainset = static::RetrieveDomain($RelOwnerUserID, $Domain, false);
        if (empty($Domainset) && $NotifyUser) {
            // unexpected error
            $error = array(
                '!UserID' => $RelOwnerUserID,
                '!function' => $caller,
                '%name' => $Domain,
            );
            Errors::unexpected(
                "Error: RetrieveWhitelabelDomain '%name' in !function for User !UserID.",
                $error,
                true
            ); //notify user
            return false;
        }

        return $Domainset;
    }

    public static function UpdateWhitelabelDomain($Domain, $ArrayFieldAndValues)
    {
        // decide whether we use a transactional pool
        $countNormalHosts = static::countNonConfirmationHosts($ArrayFieldAndValues);
        $ArrayFieldAndValues['Data']['useTransactionalPool'] = 0;
        if ($countNormalHosts >= 1 && $countNormalHosts < count($ArrayFieldAndValues['Data']['hosts'])) {
            // there is at least one (but not all) host(s) configured for the transactional pool
            $ArrayFieldAndValues['Data']['useTransactionalPool'] = 1;
        }

        db_update('whitelabel_domains')
            ->fields(array(
                'Status' => $ArrayFieldAndValues['Status'],
                'validFrom' => $ArrayFieldAndValues['validFrom'],
                'Data' => (empty($ArrayFieldAndValues['Data'])) ? '' : serialize($ArrayFieldAndValues['Data']),
                'RelOwnerUserID' => $ArrayFieldAndValues['RelOwnerUserID'],
            ))
            ->condition('Domain', $Domain)
            ->execute();

        static::setSenderStatusForRelatedAddresses($ArrayFieldAndValues);

        static::GenerateVirtualMTAConfig();

        UserCache::remove($ArrayFieldAndValues['RelOwnerUserID'], UserCache::CACHE_OVERVIEW);

        return true;
    }

    /**
     * Flag addresses with corresponding domains as (not) verified bases on domain status
     *
     * @param array $domainDefinition
     */
    public static function setSenderStatusForRelatedAddresses($domainDefinition)
    {
        $userID = $domainDefinition['RelOwnerUserID'];
        $addresses = VarAdditionalAddresses::GetAddresses($userID);
        $senderDomainName = $domainDefinition['Domain'];
        $changed = false;
        foreach ($addresses as &$address) {
            $emailAddress = $address['email'];

            if (preg_match('/[@\.]' . preg_quote($senderDomainName) . '$/', $emailAddress)) {
                $senderStatus = $domainDefinition['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED ?
                    VarAdditionalAddresses::SENDER_STATUS_OK :
                    VarAdditionalAddresses::SENDER_STATUS_NOT_VERIFIED;
                $address['SenderStatus'] = $senderStatus;
                $changed = true;
            }
        }

        if ($changed) {
            VarAdditionalAddresses::SetAddresses($userID, $addresses);
        }
    }

    public static function DeleteWhitelabelDomains($RelOwnerUserID = 0, $Domain = '')
    {
        if (empty($RelOwnerUserID)) {
            return false;
        } else {
            if (empty($Domain)) {
                db_delete('whitelabel_domains')
                    ->condition('RelOwnerUserID', $RelOwnerUserID)
                    ->execute();
            } else {
                $domainset = DomainSet::RetrieveDomain($RelOwnerUserID, $Domain);
                if (empty($domainset)) {
                    return false;
                }

                if (!static::deleteHosts($domainset)) {
                    return false;
                }

                db_delete('whitelabel_domains')
                    ->condition('RelOwnerUserID', $RelOwnerUserID)
                    ->condition('Domain', $Domain)
                    ->execute();

                static::deleteRelatedSenderAddresses($domainset);
            }
            UserCache::remove($RelOwnerUserID, UserCache::CACHE_OVERVIEW);
        }

        return true;
    }

    private static function deleteCsaHosts(array $domainset)
    {
        foreach ($domainset['Data']['hosts'] as $host => $ip) {
            $currentIpAddress = static::getHostIp($domainset, $host);
            if ($currentIpAddress) {
                CSAClient::deleteIp($currentIpAddress, $host . '.' . $domainset['Domain']);
            }
        }
    }

    /**
     * Deletes all host of given domain set. Please consider description of DomainSet::deleteHost
     *
     * @param array $domainset
     *
     * @return bool
     *
     * @throws CSAIpDeleteException if a host is registere in CSA system and could not be deleted
     * @see static::deleteHost()
     *
     */
    public static function deleteHosts(array &$domainset)
    {
        foreach (array_keys($domainset['Data']['hosts']) as $host) {
            if (!static::deleteHost($domainset, $host)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Deletes a single host. In particular it
     *   - removes host IP from sender score
     *   - removes directory used for email transfer script
     *   - removes host related data key from domain record
     *   - does NOT (ATTENTION) store changed record in database
     *
     * @param array $domainset
     * @param string $host
     *
     * @return bool
     *
     * @throws CSAIpDeleteException if host is registered in csa and could not be deleted
     */
    public static function deleteHost(array &$domainset, string $host): bool
    {
        $domain = $domainset['Domain'];

        if (static::isCsaSyncRequired($domainset)) {
            $currentIpAddress = static::getHostIp($domainset, $host);
            if ($currentIpAddress) {
                CSAClient::deleteIp($currentIpAddress, $host . '.' . $domain);
            }
        }

        $whitelabelTargetDir = SEND_METHOD_SAVETODISK_DIR . 'public/passthru/' . $host . '.' . $domain;
        if (is_dir($whitelabelTargetDir) && !rmdir($whitelabelTargetDir)) {
            watchdog(
                'send_engine',
                'Could not remove directory %directory.',
                ['%directory' => $whitelabelTargetDir],
                WATCHDOG_ERROR,
            );
            return false;
        }

        // remove ip from senderscore config
        Libraries::include('senderscore.inc');
        klicktipp_senderscore_set_host($host . '.' . $domain);

        unset($domainset['Data']['hosts'][$host]);
        unset($domainset['Data']['hostdata'][$host]);

        if (empty($domainset['Data']['hosts'])) {
            $domainset['Data']['dkimOnly'] = true;
        }

        return true;
    }

    /**
     * Deletes all host ips of a domain at CSA
     *
     * @param array $domainset
     *
     * @return void
     * @throws \App\Klicktipp\CSA\CSAIpDeleteException
     */
    public static function deleteHostIpsAtCSA(array &$domainset)
    {
        $domain = $domainset['Domain'];

        foreach (array_keys($domainset['Data']['hosts']) as $host) {
            $currentIpAddress = static::getHostIp($domainset, $host);
            if ($currentIpAddress) {
                CSAClient::deleteIp($currentIpAddress, $host . '.' . $domain);
                unset($domainset['Data']['hostdata'][$host]['csa']);
            }
        }
    }

    /**
     * Deletes host ips of all domains of an user at CSA
     *
     * @param array $domainset
     *
     * @return void
     * @throws \App\Klicktipp\CSA\CSAIpDeleteException
     */
    public static function deleteUserHostIpsAtCSA(int $userID)
    {
        $domains = DomainSet::RetrieveDomains($userID);
        foreach ($domains as $domain) {
            static::deleteHostIpsAtCSA($domain);
            static::UpdateWhitelabelDomain($domain['Domain'], $domain);
        }
    }

    /**
     * Registers all host ips of a domain at CSA
     *
     * @param array $domainset
     *
     * @return void
     * @throws \App\Klicktipp\CSA\CSAException
     */
    public static function registerHostIpsAtCSA(array &$domainset)
    {
        $domain = $domainset['Domain'];

        foreach (array_keys($domainset['Data']['hosts']) as $host) {
            $currentIpAddress = static::getHostIp($domainset, $host);
            if ($currentIpAddress) {
                $domainset['Data']['hostdata'][$host]['csa'] =
                    CSAClient::registerIp($currentIpAddress, $host . '.' . $domain);
            }
        }
    }

    /**
     * Registers all host ips of a domain at CSA
     *
     * @param array $domainset
     *
     * @return void
     * @throws \App\Klicktipp\CSA\CSAException
     */
    public static function registerUserHostIpsAtCSA(int $userID)
    {
        $domains = DomainSet::RetrieveDomains($userID);

        foreach ($domains as $domain) {
            if (empty($domain['Data']['no-csa-header'])) {
                static::registerHostIpsAtCSA($domain);
                static::UpdateWhitelabelDomain($domain['Domain'], $domain);
            }
        }
    }

    /**
     * Removes sender addresses related to whitelabel domain
     *
     * @param array $domainDefinition
     */
    private static function deleteRelatedSenderAddresses($domainDefinition)
    {
        $userID = $domainDefinition['RelOwnerUserID'];
        $addresses = VarAdditionalAddresses::GetAddresses($userID);
        $senderDomainName = $domainDefinition['Domain'];

        $changed = false;
        foreach ($addresses as &$address) {
            $emailAddress = $address['email'];

            if (preg_match('/[@\.]' . preg_quote($senderDomainName) . '$/', $emailAddress)) {
                $address['SenderStatus'] = VarAdditionalAddresses::SENDER_STATUS_NO_DOMAIN;
                $changed = true;
            }
        }

        if ($changed) {
            VarAdditionalAddresses::SetAddresses($userID, $addresses);
        }
    }

    public static function SetWhitelabelDomainDefault($RelOwnerUserID, $Domain)
    {
        $ArrayDomains = static::RetrieveWhitelabelDomains($RelOwnerUserID);

        foreach ($ArrayDomains as $Domainset) {
            if ($Domainset['Domain'] == $Domain) {
                $Domainset['Data']['defaultsender'] = true;
                static::UpdateWhitelabelDomain($Domainset['Domain'], $Domainset);
            } elseif ($Domainset['Data']['defaultsender']) {
                $Domainset['Data']['defaultsender'] = false;
                static::UpdateWhitelabelDomain($Domainset['Domain'], $Domainset);
            }
        }
    }

    private static function getRelatedSenderAddressesSubdomains($domainDefinition)
    {
        $userID = $domainDefinition['RelOwnerUserID'];
        $addresses = VarAdditionalAddresses::GetAddresses($userID);
        $senderDomainName = $domainDefinition['Domain'];
        $result = [];

        foreach ($addresses as &$address) {
            $emailAddress = $address['email'];

            if (preg_match('/[@\.]' . preg_quote($senderDomainName) . '$/', $emailAddress)) {
                $emailDomain = substr($emailAddress, strrpos($emailAddress, '@') + 1);
                if ($emailDomain != $senderDomainName) {
                    $result[$emailDomain] = $emailDomain;
                }
            }
        }

        return array_values($result);
    }

    /**
     * Get filename of DKIM key (return version for dns record check)
     */
    public static function GetWhitelabelDKIMLocation($domain)
    {
        $old_key_file = "/var/cache/pubkeys/dkimproxy/{$domain}.pub.pem";
        $old_pkey_file = "/var/cache/pubkeys/dkimproxy/{$domain}.key.pem";
        $old_record_file = "/var/cache/pubkeys/dkimproxy/{$domain}_dns_dkim_TXT_record.txt";
        $new_key_file = DKIM_CONFIG_DIR . "pubkeys/$domain.key.pem";
        $new_pkey_file = DKIM_CONFIG_DIR . "privatekeys/$domain.key.pem";
        $new_record_file = DKIM_CONFIG_DIR . "pubtxt/$domain.key.pem";
        if (file_exists($new_pkey_file)) {
            return $new_record_file;
        } elseif (file_exists($old_pkey_file)) {
            // try to copy files
            @copy($old_key_file, $new_key_file);
            @copy($old_pkey_file, $new_pkey_file);
            @copy($old_record_file, $new_record_file);
            // success?
            if (file_exists($new_pkey_file)) {
                return $new_record_file;
            }
            // cant copy, so use old file
            return $old_record_file;
        }
        return false;
    }

    public static function GetWhitelabelDNSKeys($domainset, $appdomain = KLICKTIPP_DNS_VALIDATION_DOMAIN)
    {
        static $dnskeys;

        $domain = $domainset['Domain'];
        $mxhost = empty($domainset['Data']['mxhost']) ? 'bounces' : $domainset['Data']['mxhost'];
        $whitelabel_appdomain = variable_get('klicktipp_whitelabel_appdomain', 'wl.strold.io') . ".";
        foreach ($domainset['Data']['hostdata'] as $host => $data) {
            if ($data['subscriberarea']) {
                $whitelabel_appdomain = "$host.$domain.";
                break;
            }
        }

        $mxvalue = [];
        foreach ($domainset['Data']['hosts'] as $host => $data) {
            $mxvalue[] = "$host.$domain.";
        }

        $dkimDomain = variable_get('klicktipp_shared_dkimdomain', 'strold.io');

        $dkimValue = false;
        $dkimLocation = static::GetWhitelabelDKIMLocation($domain);
        if ($dkimLocation) {
            $dkimValue = @file_get_contents($dkimLocation);
        }
        if (empty($dnskeys[$domain])) {
            $dnskeys[$domain] = array(
                'whitelabel_appdomain' => $whitelabel_appdomain,
                'mxalias' => "$mxhost.$domain.",
                'mxdomain' => "cbounces.$appdomain.", // OLD
                'mxvalue' => $mxvalue, // NEW
                'spfinclude' => "spf.$appdomain",
                'spf2include' => "spf2.$appdomain",
                'dkimhost' => "ktdkim._domainkey.$mxhost.$domain.",
                'dkimdomain' => "ktdkim._domainkey.$domain.",
                'dkimvalue' => $dkimValue,
                'individual_subdomain' => static::EncodeSharedSubDomain($domainset['RelOwnerUserID'], $domain),
                'dkim_clickhost' => 'klick.' . $dkimDomain . '.',
                'dkim_cnames' => [
                    'ktdkim1._domainkey.' . $domain => 'ktdkim1.' . $dkimDomain . '.',
                    'ktdkim2._domainkey.' . $domain => 'ktdkim2.' . $dkimDomain . '.',
                ],
            );
        }
        return $dnskeys[$domain];
    }

    public static function NormalizeDKIMValue($value)
    {
        // remove multiline variations of DNS TXT values (see http://www.zytrax.com/books/dns/ch8/txt.html)
        // this needs not to be a valid TXT value, but just some normal form to compare two TXT values
        return str_replace(
            array("\n", ' ', '(', ')', '"', '\;'),
            array('', '', '', '', '', ';'),
            $value
        );
    }

    public static function CallDIG($key, $type = '', $nameserver = array(), $timeout = 1, int $tries = 1)
    {
        // according to dig manpage timeout < 1 gets increased to 1. defaul
        $timeoutOpt = $timeout ? '+time=' . intval($timeout) : '';

        $triesOpt = '+tries=' . $tries;

        // get nameserver ($type == NS) or sender host ($type = empty, meaning A)
        if (empty($nameserver)) {
            return @shell_exec("dig $key $type $timeoutOpt $triesOpt");
        } else {
            $r = '';
            // try all nameservers
            foreach (array_merge($nameserver, static::GetFallbackNameservers()) as $ns) {
                $r = @shell_exec("dig @$ns $key $type $timeoutOpt $triesOpt");
                if (preg_match('/;; ANSWER SECTION:\n/', $r, $matches)) {
                    // answer section found, return this
                    static::$fallbackNameserversUsed = in_array($ns, static::FALLBACK_NAMESERVERS, true);
                    return $r;
                }
            }
            // no answer section found, return last
            return $r;
        }
    }

    /**
     * Process checks for dkim cnames (sender domains)
     *
     * @param array $domainset
     * @param array $dnskeys
     * @param array $nameservers
     *
     * @return array
     */
    public static function DkimCheck(&$domainset, $dnskeys, $nameservers, bool $forceWhitelabelCheck = false)
    {
        $checkResults = [];
        $success = true;
        $maxTTL = 0;
        foreach ($dnskeys['dkim_cnames'] as $domain => $value) {
            $expected = $obtained = [
                'host' => $domain,
                'type' => 'CNAME'
            ];
            $expected += [
                'value' => is_array($value) ? reset($value) : $value,
                'ttl' => static::EXPECTED_TTL
            ];

            $result = ['success' => true];
            static::VerifyCNAME($domain, $value, $nameservers, $result);
            if (!empty($result['ttl'])) {
                $obtained += [
                    'ttl' => $result['ttl'],
                    'value' => $result['value']
                ];
                $maxTTL = max($maxTTL, $result['ttl']);
            }
            $checkResults[] = [
                'expected' => $expected,
                'obtained' => $obtained,
                'status' => empty($result['errors']),
                'dkimStatus' => empty($result['errors']),
                'description' => empty($result['errors']) ? '' : $result['errors']["cname][$domain"],
                'errors' => $result['errors']
            ];
            $success = $success && $result['success'];
        }
        $domainset['Data']['hasDkimCnames'] = $success;
        if (!$success) {
            $oldCheckResults = [];
            $success = true;
            foreach ([$dnskeys['dkimdomain'], $dnskeys['dkimhost']] as $host) {
                $dkimResult = ['success' => true];

                $expected = $obtained = [
                    'host' => $host,
                    'type' => 'TXT/DKIM'
                ];

                $expected += [
                    'ttl' => static::EXPECTED_TTL,
                    'value' => trim($dnskeys['dkimvalue'], '"')
                ];

                static::VerifyDkimTxtRecord($host, $dnskeys['dkimvalue'], $nameservers, $dkimResult);

                // if old check is not successful, there is no fallback => we only show results of new check
                if (!$dkimResult['success']) {
                    $success = false;
                    break;
                }

                $obtained += [
                    'ttl' => $dkimResult['ttl'],
                    'value' => trim($dnskeys['dkimvalue'], '"')
                ];

                $oldCheckResults[] = [
                    'expected' => $expected,
                    'obtained' => $obtained,
                    'deprecated' => true,
                    'advice' => 'deprecated-cname-record',
                    'status' => true,
                    'dkimStatus' => true,
                    'description' => ''
                ];
            }
            if ($success) {
                $checkResults = array_map(
                    fn($result) => array_merge(
                    // remove FALSE-Flags, since fallback to text records is successful
                        array_diff_key($result, ['status' => false, 'dkimStatus' => false]),
                        ['advice' => 'cname-record-for-dkim'] // in case of fallback cname records are recommended
                    ),
                    $checkResults
                );
                array_push($checkResults, ...$oldCheckResults);
            }
        }

        $value = $dnskeys['dkim_clickhost'];
        $individualSubDomain = $dnskeys['individual_subdomain'];
        $expected = $obtained = [
            'host' => $individualSubDomain,
            'type' => 'CNAME'
        ];
        $expected += [
            'value' => $value,
            'ttl' => static::EXPECTED_TTL
        ];

        $result = ['success' => true];
        static::VerifyCNAME($individualSubDomain, $value, $nameservers, $result);
        if (!empty($result['ttl'])) {
            $obtained += [
                'ttl' => $result['ttl'],
                'value' => $result['value']
            ];
        }
        $individualSubDomainResult = [
            'expected' => $expected,
            'obtained' => $obtained,
            'status' => empty($result['errors']) ? true : null, // in case of mailservers this cname is optional
            'dkimStatus' => empty($result['errors']),
            'description' => empty($result['errors']) ? '' : $result['errors']["cname][$individualSubDomain"],
            'errors' => $result['errors']
        ];
        if (!empty($result['errors']) && (!$domainset['Data']['dkimOnly'] || $forceWhitelabelCheck)) {
            $individualSubDomainResult['advice'] = 'alternate-click-host'; // in case of mailsever this cname is recommended
        }

        array_push($checkResults, $individualSubDomainResult);
        return $checkResults;
    }

    /**
     * @param array $domainset
     * @return array
     */
    public static function DNSCheckWithUpdate(&$domainset, bool $forceWhitelabelCheck = false, bool $checkSenderMXRecors = false)
    {
        $currentStatus = $domainset['Status'];
        // does domain support new approach of DKIM-Validation ?
        $oldCnamesStatus = !empty($domainset['Data']['hasDkimCnames']);
        $oldDmarcStatus = !empty($domainset['Data']['hasDmarcRecord']);
        $results = static::DNSCheck($domainset, $forceWhitelabelCheck, $checkSenderMXRecors);

        $statusField = empty($domainset['Data']['dkimOnly']) ? 'status' : 'dkimStatus';
        $success = empty($results['error']) && // no nameservers case
            !isset(array_column($results, $statusField, $statusField)[false]);

        // status of dkim cnames changed => affects emails (x-dkim-options header)
        $updateRequired = $oldCnamesStatus !== !empty($domainset['Data']['hasDkimCnames'])
            || $oldDmarcStatus !== !empty($domainset['Data']['hasDmarcRecord']);
        $newStatus = $success ? DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED : DomainSet::WHITELABELDOMAIN_STATUS_ACTIVE;
        if ($newStatus != $currentStatus) {
            $updateRequired = true;
            $domainset['Status'] = $newStatus;
            $depunycodedDomain = DomainUtils::depunycodeDomain($domainset['Domain']);
            if ($newStatus == DomainSet::WHITELABELDOMAIN_STATUS_ACTIVE) {
                $message = t(
                    'Your sending address domain @domain has been disabled because the DNS records are not set correctly. Check the status of the DNS records via  <a href="/user/@userID/domains"/>Domain Management</a> and set the records for your domain in your hosting portal. You can find instructions <a href="">here</a>.',
                    ['@domain' => $depunycodedDomain, '@userID' => $domainset['RelOwnerUserID']]
                );
                drupal_set_message($message, 'error');
                if ($currentStatus == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED) {
                    watchdog('send_engine', 'Domain %domain (DEACTIVATED) check failed (user !uid)', array(
                        '%domain' => $domainset['Domain'],
                        '!uid' => $domainset['RelOwnerUserID'],
                        '!result' => $results,
                    ), WATCHDOG_ERROR);

                    $account = user_load($domainset['RelOwnerUserID']);
                    KlicktippMail::sendConfiguredUserNotification(
                        $account,
                        $domainset['Data']['dkimOnly'] ?
                            COREAPI_NOTIFY_EMAIL_USER_SENDERDOMAIN_CHECK :
                            COREAPI_NOTIFY_EMAIL_USER_WHITELABELDOMAIN_CHECK,
                        [
                            '!firstName' => $account->FirstName,
                            '!domain' => $depunycodedDomain,
                            '!url' => url(
                                'domain/' . preg_replace(
                                    '/\./',
                                    '/',
                                    DomainSet::EncodeSharedSubDomain($account->uid, $domainset['Domain']),
                                    1
                                ),
                                ['absolute' => true]
                            ),
                            '!userID' => $account->uid
                        ],
                        'whitelabel_misconfiguration'
                    );
                }

            } else {
                $maxTTL = array_reduce(
                    $results,
                    fn($maxTTL, $item) => max($maxTTL, $item['obtained']['ttl']),
                    0
                );
                // wait three times the ttl before using this domain, so every nameserver knows all changes
                $domainset['validFrom'] = time() + 2 * $maxTTL;
                $message = t(
                    'The DNS record for your domain @domain was successfully checked. The DNS records have been set correctly. You can use your domain as a sending address domain after the TTL expires from !date onwards and use email addresses running on this domain as sending addresses. You can find all information about TTL <a href="">here</a>. The remaining TTL is displayed in brackets behind the status of the records.',
                    [
                        '@domain' => $depunycodedDomain,
                        '!date' => Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $domainset['validFrom'])
                    ]
                );
                drupal_set_message($message);
                VarNewSharedDomains::activate($domainset['RelOwnerUserID']);
            }
        }
        if ($updateRequired) {
            static::UpdateWhitelabelDomain($domainset['Domain'], $domainset);
        }

        return $results;
    }

    /**
     * @param array $domainset
     * @return array
     */
    public static function DNSCheck(&$domainset, bool $forceWhitelabelCheck = false, bool $checkSenderMXRecors = false)
    {
        $domain = $domainset['Domain'];
        $nameservers = static::GetNameservers($domain);
        if (empty($nameservers)) {
            return [
                'error' => t(
                    'Unfortunately, no name servers could be found for the specified domain. A DNS check without a correctly configured name server is unfortunately not possible.'
                )
            ];
        }

        if ($checkSenderMXRecors) {
            static::checkSenderMXRecords($domainset, $nameservers);
        }

        static::$fallbackNameserversUsed = false;
        $dnskeys = call_user_func(static::$keysFunction, $domainset);
        $depunycodedDomain = DomainUtils::depunycodeDomain($domain);

        $result = self::DkimCheck($domainset, $dnskeys, $nameservers, $forceWhitelabelCheck);
        self::enrichResultWithDmarcInfo($domainset, $result, $nameservers);
        if (!empty($domainset['Data']['dkimOnly']) && !$forceWhitelabelCheck) {
            return $result;
        }

        $cnameResult = [];
        $expected = $obtained = [
            'host' => $domainset['Data']['alias'] . '.' . $domain,
            'type' => 'CNAME'
        ];
        $expected += [
            'ttl' => static::EXPECTED_TTL,
            'value' => $dnskeys['whitelabel_appdomain']
        ];
        static::VerifyAliasCNAME($domainset, $dnskeys, $nameservers, $cnameResult);
        if (!empty($cnameResult['ttl'])) {
            $obtained += [
                'ttl' => $cnameResult['ttl'],
                'value' => $cnameResult['value']
            ];
        }

        $result[] = [
            'expected' => $expected,
            'obtained' => $obtained,
            'status' => empty($cnameResult['errors']),
            'description' => empty($cnameResult['errors']) ? '' : $cnameResult['errors']["cname][DomainAlias"],
            'errors' => $cnameResult['errors'],
        ];

        //// check a records of sender hosts

        $hostResults = [];
        static::VerifyHost($domain, $domainset['Data']['hosts'], $hostResults, $nameservers);
        foreach ($domainset['Data']['hosts'] as $host => $ip) {
            $fullHost = "$host.$domain";
            $expected = $obtained = [
                'host' => $fullHost,
                'type' => 'A'
            ];

            $expected += [
                'ttl' => static::EXPECTED_TTL,
                'value' => $ip
            ];
            $description = '';
            if (isset($hostResults['errors']["hosts][$host"])) {
                $description .= $hostResults['errors']["hosts][$host"];
            } elseif (isset($hostResults['errors'][$fullHost])) {
                $description .= $hostResults['errors'][$fullHost];
            }
            if (isset($hostResults['ttl'][$host])) {
                $obtained += [
                    'ttl' => $hostResults['ttl'][$host],
                    'value' => $hostResults['value'][$host]
                ];
            }
            $result[] = [
                'expected' => $expected,
                'obtained' => $obtained,
                'status' => empty($description),
                'description' => $description,
                'errors' => $hostResults['errors'],
            ];
        }

        $mxAliasSpfResult = [];
        $expected = $obtained = [
            'host' => $dnskeys['mxalias'],
            'type' => 'TXT/SPF'
        ];

        $expected += [
            'ttl' => static::EXPECTED_TTL,
            'value' => 'v=spf1 a mx ~all'
        ];
        static::VerifyMXAliasSPF($domain, $dnskeys, $nameservers, $mxAliasSpfResult);
        $obtained += [
            'ttl' => $mxAliasSpfResult['ttl'],
        ];
        if ($mxAliasSpfResult['value']) {
            $obtained['value'] = trim($mxAliasSpfResult['value'], '"');
        }
        $result[] = [
            'expected' => $expected,
            'obtained' => $obtained,
            'status' => empty($mxAliasSpfResult['errors']),
            'description' => empty($mxAliasSpfResult['errors']) ? '' : implode(' ', $mxAliasSpfResult['errors']),
            'errors' => $mxAliasSpfResult['errors'],
        ];

        $mxResults = [];
        static::VerifyMX($dnskeys, $mxAliasSpfResult['spfnewstyle'], $nameservers, $mxResults);
        $priority = 0;
        if (
            $mxAliasSpfResult['spfnewstyle'] ||
            !$mxAliasSpfResult['spf1'] // if no mx alias spf was found, we suggest new-style MX-records
        ) {
            $mxValues = array_map(
                fn($host) => "$host.$domain.",
                array_keys($domainset['Data']['hosts']),
            );
        } else {
            $mxValues = [$dnskeys['mxdomain']];
        }
        foreach ($mxValues as $mxValue) {
            $expected = $obtained = [
                'host' => $dnskeys['mxalias'],
                'type' => 'MX',
            ];
            $expected += [
                'ttl' => static::EXPECTED_TTL,
                'value' => $mxValue,
                'priority' => $priority += 10,
            ];
            if (!empty($mxResults['ttl']["mx.$mxValue"])) {
                $status = true;
                $description = '';
                $obtained += [
                    'ttl' => $mxResults['ttl']["mx.$mxValue"],
                    'value' => $mxValue
                ];
            } else {
                $status = false;
                $description = $mxResults['errors']["mx.$mxValue"];
            }
            $result[] = [
                'expected' => $expected,
                'obtained' => $obtained,
                'status' => $status,
                'description' => $description,
                'errors' => [$mxResults['errors']["mx.$mxValue"]],
            ];
        }

        return $result;
    }

    /**
     * @param array $domainset
     * @param array $dnskeys
     * @param array $nameservers
     * @param array $results
     */
    protected static function VerifyAliasCNAME($domainset, $dnskeys, $nameservers, &$results)
    {
        $domain = $domainset['Domain'];
        $DomainAlias = $domainset['Data']['alias'];
        // there should be at least one host
        if (empty($DomainAlias)) {
            $results['success'] = false;
            $results['errors']['cname][DomainAlias'] = t(
                'No domain alias defined for domain %domain.',
                array('%domain' => $domain)
            );
        } elseif (!empty($nameservers)) {
            // dig @ns3.klick-tipp.com. mail.endlich-ohne-schufa.info. CNAME
            // $r = @shell_exec("dig @$nameserver $DomainAlias.$domain. CNAME");
            $r = call_user_func(static::$digFunction, "$DomainAlias.$domain.", 'CNAME', $nameservers);
            $results['debug'][] = $r;

            // ;; ANSWER SECTION:\nmail.endlich-ohne-schufa.info. 86400 IN    CNAME   whitelabel.klick-tipp.com.
            if (preg_match('/;; ANSWER SECTION:\n(\S+)\s+(\S+)\s+IN\s+CNAME\s+(.*)\n/', $r, $matches)) {
                $key = $matches[1];
                $ttl = $matches[2];
                $value = $matches[3];

                $results['ttl'] = $ttl;
                $results['value'] = $value;
                if ($ttl > $results['maxttl']) {
                    $results['maxttl'] = $ttl;
                }

                // OLD: klick.customerdomain.tld. 86400 IN  CNAME   whitelabel.klick-tipp.com.
                // NEW: klick.customerdomain.tld. 86400 IN  CNAME   wl.strold.io
                // FUTURE: klick.customerdomain.tld. 86400 IN   CNAME   host1.customerdomain.tld.
                $cnamehosts = [
                    "whitelabel." . KLICKTIPP_DNS_VALIDATION_DOMAIN . ".", // OLD
                    $dnskeys['whitelabel_appdomain'], // NEW
                ];
                foreach ($domainset['Data']['hosts'] as $host => $ip) {
                    $cnamehosts[] = "$host.$domain."; // FUTURE
                }
                if (!in_array($value, $cnamehosts)) {
                    $results['success'] = false;
                    $results['errors']["cname][DomainAlias"] = t(
                        "CNAME record for domain '%domain' found, but points to '%value'.",
                        array('%domain' => "$DomainAlias.$domain", '%value' => $value)
                    );
                    $results['debug'][] = t('cname debug !result', array('!result' => $r));
                }
            } else {
                $results['success'] = false;
                $results['errors']["cname][DomainAlias"] = t(
                    'No CNAME record for domain %domain found.',
                    array('%domain' => "$DomainAlias.$domain")
                );
                $results['debug'][] = t('cname debug !result', array('!result' => $r));
            }
        }
    }

    /**
     * @param string $domain
     * @param array $dnskeys
     * @param array $nameservers
     * @param array $results
     */
    protected static function VerifyMXAliasSPF($domain, $dnskeys, $nameservers, &$results)
    {
        if (!empty($nameservers)) {
            // dig @ns3.klick-tipp.com. endlich-ohne-schufa.info. TXT
            // $r = @shell_exec("dig @$nameserver {$dnskeys['mxalias']} TXT");
            $r = call_user_func(static::$digFunction, $dnskeys['mxalias'], "TXT", $nameservers);

            // bounces.endlich-ohne-schufa.info. 86400 IN TXT "v=spf2.0/pra include:_spf.klick-tipp.com -all"
            if (preg_match_all('/^(\S+)\s+(\S+)\s+IN\s+TXT\s+(.*)$/m', $r, $matches, PREG_SET_ORDER)) {
                $results['TXT'] = $matches;
            } else {
                $results['TXT'] = [];
                $results['success'] = false;
                $results['errors'][] = t('No TXT for domain %domain found.', array('%domain' => $dnskeys['mxalias']));
                $results['debug'][] = t('TXT debug !result', array('!result' => $r));
            }

            $spf1found = false;
            $spfnewstyle = true;
            foreach ($results['TXT'] as $txtset) {
                /* OLD: $txtset = Array
                 (
                 [0] => endlich-ohne-schufa.info. 86400 IN  TXT "v=spf1 include:_spf.klick-tipp.com -all"
                 [1] => endlich-ohne-schufa.info.
                 [2] => 86400
                 [3] => "v=spf1 include:_spf.klick-tipp.com -all"
                 )
                 */
                /* FUTURE: $txtset = Array
                 (
                 [0] => endlich-ohne-schufa.info. 86400 IN  TXT "v=spf1 a mx ~all"
                 [1] => endlich-ohne-schufa.info.
                 [2] => 86400
                 [3] => "v=spf1 a mx ~all"
                 )
                 */
                $txt = trim($txtset[3], '"');
                if (
                    preg_match(
                        '/v\=spf1\s+(\+*a\s*|a:\S+\s*|\+*mx\s*|\+*ptr\s*|\+*ip\S+\s*|\+*include:\S+\s*)+\s+[\-|\~]+all/',
                        $txt
                    )
                ) {
                    if (strpos($txt, ' include:' . $dnskeys['spfinclude'])) {
                        $spf1found = true;
                        $spfnewstyle = false;
                        if ($txtset[2] > $results['maxttl']) {
                            $results['maxttl'] = $txtset[2];
                        }
                    } elseif (strpos($txt, ' mx')) {
                        $spf1found = true;
                        if ($txtset[2] > $results['maxttl']) {
                            $results['maxttl'] = $txtset[2];
                        }
                    }
                    $results['ttl'] = $txtset[2];
                    $results['value'] = $txtset[3];
                }
            }
            if ($spf1found) {
                $results['spf1'] = true;
            } else {
                $results['spf1'] = false;
                $results['success'] = false;
                $results['errors']['spf1'] = t(
                    'No SPF for domain %domain found.',
                    array('%domain' => $dnskeys['mxalias'])
                );
                $results['debug'][] = t('TXT debug !result', array('!result' => $r));
            }
            $results['spfnewstyle'] = $spfnewstyle;
            if (!$spfnewstyle) {
                watchdog('spf_newstyle_check', 'Domain %domain uses old SPF style', array(
                    '%domain' => $dnskeys['mxalias'],
                    '!result' => print_r($results, true),
                ), WATCHDOG_INFO);
            }
        }
    }


    /**
     * @param array $dnskeys
     * @param bool $spfnewstyle
     * @param array $nameservers
     * @param array $results
     */
    protected static function VerifyMX($dnskeys, $spfnewstyle, $nameservers, &$results)
    {
        if (!empty($nameservers)) {
            if ($spfnewstyle) {
                //FUTURE: spf w/ mx, multiple MX point to each of the hosts
                // dig @ns3.klick-tipp.com. bounces.endlich-ohne-schufa.info. MX
                // $r = @shell_exec("dig @$nameserver {$dnskeys['mxalias']} MX");
                $r = call_user_func(static::$digFunction, $dnskeys['mxalias'], "MX", $nameservers);
                // we shall get multiple results (one for each host)
                $results['mx'] = true;
                foreach ($dnskeys['mxvalue'] as $host) {
                    // Example DNS MX entry:
                    //    bounces.endlich-ohne-schufa.info. 86400 IN MX 10 host1.endlich-ohne-schufa.info
                    //
                    // Important parts would be:
                    //    ttl = 86400
                    //    priority = 10
                    //
                    if (
                        preg_match(
                            '/^' . preg_quote($dnskeys['mxalias']) . '\s+(\S+)\s+IN\s+MX\s+(\S+)\s+' . preg_quote(
                                $host
                            ) . '$/im',
                            $r,
                            $matches
                        )
                    ) {
                        $ttl = $matches[1];

                        if ($ttl > $results['maxttl']) {
                            $results['maxttl'] = $ttl;
                        }
                        $results['ttl']['mx.' . $host] = $matches[1];
                    } else {
                        $results['mx'] = false;
                        $results['success'] = false;

                        $results['errors']['mx.' . $host] = t('No MX for host %domain found.', ['%domain' => $host]);
                        $results['debug'][] = t('MX debug !result', ['!result' => $r]);
                    }
                }
            } else {
                //OLD spf w/o mx, MX points to mxalias
                // dig @ns3.klick-tipp.com. bounces.endlich-ohne-schufa.info. MX
                // $r = @shell_exec("dig @$nameserver {$dnskeys['mxalias']} MX");
                $r = call_user_func(static::$digFunction, $dnskeys['mxalias'], "MX", $nameservers);

                // Example DNS MX entry:
                //    ;; ANSWER SECTION:\nbounces.endlich-ohne-schufa.info. 86400 IN MX 10 cbounces.klick-tipp.com.
                //
                // Important parts would be:
                //    key = bounces.endlich-ohne-schufa.info.
                //    ttl = 86400
                //    priority = 10
                //
                if (
                    preg_match(
                        '/;; ANSWER SECTION:\n(\S+)\s+(\S+)\s+IN\s+MX\s+(\S+)\s+' . preg_quote(
                            $dnskeys['mxdomain']
                        ) . '\n/i',
                        $r,
                        $matches
                    )
                ) {
                    $ttl = $matches[2];

                    if ($ttl > $results['maxttl']) {
                        $results['maxttl'] = $ttl;
                    }
                    $results['ttl']['mx.' . $dnskeys['mxdomain']] = $ttl;
                    $results['mx'] = true;
                } else {
                    $results['success'] = false;
                    $results['errors']['mx.' . $dnskeys['mxdomain']] = t(
                        'No MX for domain %domain found.',
                        ['%domain' => $dnskeys['mxdomain']]
                    );
                    $results['debug'][] = t('MX debug !result', ['!result' => $r]);
                }
            }
        }
    }

    /**
     * @param string $domain domain whose DKIM we need to verify
     * @param array|string[] $dkimCnames keys are hosts, values are CNAME values
     * @param array $nameserver nameserver to send DNS queries against
     * @param array $results result to extend
     */
    protected static function VerifyCNAME($domain, $expectedValue, $nameserver, &$results)
    {
        $r = call_user_func(static::$digFunction, $domain, "CNAME", $nameserver);

        $results['debug'][] = $r;

        // ;; ANSWER SECTION:\nmail.endlich-ohne-schufa.info. 86400 IN    CNAME   whitelabel.klick-tipp.com.
        if (preg_match('/;; ANSWER SECTION:\n(\S+)\s+(\S+)\s+IN\s+CNAME\s+(.*)\n/', $r, $matches)) {
            $ttl = $matches[2];
            $value = $matches[3];

            $results['ttl'] = $ttl;
            $results['value'] = $value;
            if ($ttl > $results['maxttl']) {
                $results['maxttl'] = $ttl;
            }

            if ($value !== $expectedValue && (!is_array($expectedValue) || !in_array($value, $expectedValue))) {
                $results['success'] = false;
                $results['errors']["cname][$domain"] = t(
                    "CNAME record for domain '%domain' found, but points to '%value'.",
                    array('%domain' => $domain, '%value' => $value)
                );
                $results['debug'][] = t('cname debug !result', array('!result' => $r));
            }
        } else {
            $results['success'] = false;
            $results['errors']["cname][$domain"] = t(
                'No CNAME record for domain %domain found.',
                array('%domain' => $domain)
            );
            $results['debug'][] = t('cname debug !result', array('!result' => $r));
        }
    }

    /**
     * @param string $domain domain whose DKIM we need to verify
     * @param string $dkimValue dkim value stored in configuration
     * @param array $nameserver nameserver to send DNS queries against
     * @param array $results result to extend
     */
    protected static function VerifyDkimTxtRecord($domain, $dkimValue, $nameserver, &$results)
    {
        if (!empty($nameserver)) {
            // dig @ns3.klick-tipp.com. ktdkim._domainkey.bounces.endlich-ohne-schufa.info TXT
            // $r = @shell_exec("dig TXT @$nameserver $host");
            $r = call_user_func(static::$digFunction, $domain, "TXT", $nameserver);

            // ;; ANSWER SECTION:\nktdkim._domainkey.endlich-ohne-schufa.info. 3600 IN TXT    "v=DKIM1\; k=rsa\; p=" "MIIBI9w0BAQEFAA...zrqDkrrQ8wY/E" "FgqloFgiCewy...Hgp6w8" "\;"
            if (preg_match('/;; ANSWER SECTION:\n(\S+)\s+(\S+)\s+IN\s+TXT\s+(.*)\n/', $r, $matches)) {
                $key = $matches[1];
                $ttl = $matches[2];
                $value = $matches[3];

                $results['ttl'] = $ttl;
                $results['value'] = $value;

                if ($ttl > $results['maxttl']) {
                    $results['maxttl'] = $ttl;
                }

                // normal form is
                // v=DKIM1;k=rsa;p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCg...QAB;
                $value_theirs = rtrim(DomainSet::NormalizeDKIMValue($value), ';');
                $value_ours = rtrim(DomainSet::NormalizeDKIMValue($dkimValue), ';');

                if (strcmp($value_theirs, $value_ours) != 0) {
                    $results['success'] = false;
                    $results['errors']['dkim.' . $domain] = t(
                        'Different DKIM entry for domain %domain found. (!value)',
                        array('%domain' => $domain, '!value' => $value)
                    );
                    $results['debug'][] = t('DKIM TXT debug !result', array('!result' => $r));
                }
            } else {
                $results['success'] = false;
                $results['errors']['dkim.' . $domain] = t(
                    'No DKIM entry for domain %domain found.',
                    array('%domain' => $domain)
                );
                $results['debug'][] = t('DKIM TXT debug !result', array('!result' => $r));
            }
        }
    }

    public static function VerifyHost($domain, $ArrayHosts, &$results, $nameservers = [])
    {
        foreach ($ArrayHosts as $host => $ip) {
            $formindex = "$host.$domain";

            // dig host.endlich-ohne-schufa.info.
            // this needs to ask the local nameserver, as sender hosts are defined locally only
            // $r = @shell_exec("dig $host.$domain.");
            $r = call_user_func(static::$digFunction, "$host.$domain.", 'A', $nameservers);
            $results['debug'][] = $r;

            // ;; ANSWER SECTION:\nhost1.top10-rankings.com. 86346    IN  A   **************
            if (preg_match('/;; ANSWER SECTION:\n(\S+)\s+(\S+)\s+IN\s+A\s+(.*)\n/', $r, $matches)) {
                $key = $matches[1];
                $ttl = $matches[2];
                $value = $matches[3];

                $results['ttl'][$host] = $ttl;
                $results['value'][$host] = $ip;
                if (!filter_var($value, FILTER_VALIDATE_IP)) {
                    $results['success'] = false;
                    $results['errors']["hosts][$host"] = t(
                        "A record for domain '%domain' found, but not connected.",
                        array('%domain' => "$host.$domain", '%value' => $value)
                    );
                    $results['debug'][] = t('host debug !result', array('!result' => $r));
                } elseif (ip2long($value) != ip2long($ip)) {
                    $results['success'] = false;
                    $results['errors']["hosts][$host"] = t(
                        "A record for domain '%domain' found, but IP %value is different (expected: %ip).",
                        array('%domain' => "$host.$domain", '%value' => $value, '%ip' => $ip)
                    );
                    $results['debug'][] = t('host debug !result', array('!result' => $r));
                }
                $results['hosts'][$host] = $value;
            } else {
                $results['success'] = false;
                $results['errors'][$formindex] = t(
                    'No A record for domain %domain found.',
                    array('%domain' => "$host.$domain")
                );
                $results['debug'][] = t('host debug !result', array('!result' => $r));
            }
        }
    }

    public static function CheckWhitelabelDomain($data)
    {
        $Domain = $data['Domain'];
        $RelOwnerUserID = $data['RelOwnerUserID'];

        // get latest domain config
        $domainset = DomainSet::RetrieveDomain($RelOwnerUserID, $Domain);
        if (empty($domainset)) {
            return false;
        }

        if ($domainset['Status'] != DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED) {
            return;
        }

        // verified domain -> re-validate
        DomainSet::DNSCheckWithUpdate($domainset, false, true);
    }

    public static function GetValidSenderDomains(
        $ArrayUser,
        $ignoreTTL = false,
        $ArrayValidStatus = array(DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED)
    ) {
        // return TRUE, if the defined statuses are malformed, so use klicktipp domain
        if (!is_array($ArrayValidStatus)) {
            return true;
        }

        // return TRUE, if Klicktipp is the only valid sender domain (here: non whitelabel user)
        if (!user_access('use whitelabel domain', (object)$ArrayUser)) {
            return true;
        }

        // get user domains
        $ArrayDomains = DomainSet::RetrieveWhitelabelDomains($ArrayUser['uid']);

        // return TRUE, if Klicktipp is the only valid sender domain (here: no whitelabel domains defined)
        if (empty($ArrayDomains)) {
            return true;
        }

        // filter valid domains
        $SenderDomains = static::filterValidDomains($ArrayDomains, $ignoreTTL, $ArrayValidStatus);

        // return TRUE, if Klicktipp is the only valid sender domain (here: no whitelabel domains verified and user privilege given)
        if (empty($SenderDomains)) {
            return !empty($ArrayUser['UserPrivileges']['UseKlicktippAsFallbackSenderDomain']);
        }

        // return valid senders
        return $SenderDomains;
    }

    /**
     * Retrieves a list of all valid domains. List can contained whitelabel domains and dkimOnly ones
     *
     * @param int $userID
     * @param bool $ignoreTTL
     * @param array $validStatus
     *
     * @return array
     */
    public static function GetValidDomains(
        $userID,
        bool $ignoreTTL = false,
        array $validStatus = [DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED]
    ) {
        return static::filterValidDomains(DomainSet::RetrieveDomains($userID), $ignoreTTL, $validStatus);
    }

    /**
     * Accepts a list of domains and retrievess valid ones
     *
     * @param array $domains
     * @param bool $ignoreTTL if true, doesn't consider validFrom-time
     * @param array $validStatus
     *
     * @return array
     */
    private static function filterValidDomains(array $domains, bool $ignoreTTL, array $validStatus)
    {
        $result = [];

        foreach ($domains as $domainset) {
            if (!in_array($domainset['Status'], $validStatus)) {
                continue;
            }
            if ($ignoreTTL || $domainset['validFrom'] < time()) {
                $result[$domainset['Domain']] = $domainset;
            }
        }

        return $result;
    }

    public static function GetPixelUrls($account)
    {
        $ArrayValidStatus = array(
            DomainSet::WHITELABELDOMAIN_STATUS_NEW,
            DomainSet::WHITELABELDOMAIN_STATUS_ACTIVE,
            DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED,
        );

        // are there whitelabel domains?
        $ValidSenderDomainSets = DomainSet::GetValidSenderDomains((array)$account, true, $ArrayValidStatus);

        $DomainSets = array();
        if (is_array($ValidSenderDomainSets)) {
            // for users with valid white label domains

            $DomainSets = $ValidSenderDomainSets;
        } elseif ($ValidSenderDomainSets === true) {
            // for users which should use the shared domains including fallback

            $SharedDomainSet = DomainSet::SelectDomainSet($account->uid);
            if (!empty($SharedDomainSet)) {
                $DomainSets = array($SharedDomainSet);
            }
        }

        // arrange in distinct structure: array(domain => https://alias.domain/)
        $Urls = array();
        foreach ($DomainSets as $DomainSet) {
            $Domain = $DomainSet['Domain'];
            $Alias = $DomainSet['Data']['alias'];
            $Urls[$DomainSet['Domain']] = 'https://' . rtrim($Alias . '.' . $Domain, '/') . '/';
        }

        return $Urls;
    }

    public static function SelectValidSenderDomainById($UserID, $EmailID = 0)
    {
        $account = user_load($UserID);
        $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
        if (empty($ArrayEmail)) {
            $ArrayEmail = array();
        }
        $SenderDomains = DomainSet::GetValidSenderDomains((array)$account);
        return DomainSet::SelectSenderDomain($account->uid, $SenderDomains, $ArrayEmail);
    }

    public static function SelectSenderDomain(
        $UserID = 0,
        $SenderDomains = true,
        $ArrayEmail = [],
        $ArraySignature = []
    ) {
        // choose one from email or random
        $domainset = DomainSet::SelectDomainSet(
            $UserID,
            $SenderDomains,
            $ArrayEmail['SenderDomain'],
            $ArraySignature['SenderDomain'] ?? '',
            $ArrayEmail['FromEmail'] ?? ''
        );

        if (empty($domainset)) {
            // no valid whitelabel or shared domains -> use old klicktipp failover
            return DomainSet::FillSenderParameters(null, $UserID, '', $ArrayEmail['FromEmail']);
        }

        // select one random host as sender
        $host = null;
        if (!empty($domainset['Data']['hosts'])) {
            $host = array_rand($domainset['Data']['hosts']);
        }
        return DomainSet::FillSenderParameters($domainset, $UserID, $host, $ArrayEmail['FromEmail']);
    }

    /**
     * @param $UserID
     * @param array|bool $SenderDomains array, if user has some sender domains (whitelabel case)
     *                                  TRUE, if there are no whitelabel domains and fallback to shared is allowed per configuration
     *                                  FALSE, if there are no whitelabel domains and fallback to shared is NOT allowed per configuration
     * @param $EmailDomain
     * @param $DispatchProfileDomain
     *
     * @return array|false|mixed
     */
    public static function SelectDomainSet(
        $UserID = 0,
        $SenderDomains = true,
        $EmailDomain = '',
        $DispatchProfileDomain = '',
        string $fromEmailAddress = ''
    ) {
        $domainset = array();

        if (is_array($SenderDomains)) {
            $regex = '/[.@](' . implode('|', array_map('preg_quote', array_keys($SenderDomains))) . ')$/';
            preg_match($regex, $fromEmailAddress, $matches);
            if ($matches) {
                $domainset = $SenderDomains[$matches[1]];
            } elseif (!empty($SenderDomains[$EmailDomain])) {
                // choose one from email (if set)
                $domainset = $SenderDomains[$EmailDomain];
            } elseif (!empty($SenderDomains[$DispatchProfileDomain])) {
                // choose one from dispatch profile (if set)
                $domainset = $SenderDomains[$DispatchProfileDomain];
            } else {
                // otherwise choose default (if set)
                foreach ($SenderDomains as $ds) {
                    if ($ds['Data']['defaultsender']) {
                        $domainset = $ds;
                        break;
                    }
                }
            }
            // otherwise choose first
            if (empty($domainset)) {
                $domainset = reset($SenderDomains);
            }
        }

        if (empty($domainset)) {
            // no valid whitelabel domains -> use klicktipp (privileges are checked before)
            $domainset = static::GetSharedSenderDomains($UserID);
        }

        return $domainset;
    }

    /*
     * select shared sender domain for user id
     */
    public static function GetSharedSenderDomains(
        $UserID,
        $reset = false,
        $shared_senderdomain_user = '',
        $shared_senderdomain_domains = '',
        $new_shared_senderdomain_domains = ''
    ) {
        static $SharedDomains = [];
        static $NewSharedDomains = [];
        static $IsMisconfigured = false;
        if ($reset) {
            $SharedDomains = [];
            $NewSharedDomains = [];
            $IsMisconfigured = false;
        }
        $ModDomains = static::GetModDomains($shared_senderdomain_domains, false, $reset);
        if (empty($ModDomains)) {
            // misconfiguration
            return [];
        }

        $NewModDomains = static::GetModDomains($new_shared_senderdomain_domains, true, $reset);
        if (empty($NewModDomains)) {
            $NewModDomains = $ModDomains;
        }

        // numbers of old and new domains have to be equal
        if (count($ModDomains) != count($NewModDomains)) {
            return [];
        }

        if (empty($SharedDomains)) {
            if (empty($shared_senderdomain_user)) {
                $shared_senderdomain_user = Settings::get('shared_senderdomain_user');
            }

            $AllSharedDomains = DomainSet::RetrieveWhitelabelDomains($shared_senderdomain_user);

            $flippedModDomains = array_flip($ModDomains);
            $SharedDomains = array_intersect_key($AllSharedDomains, $flippedModDomains);

            $flippedNewModDomains = array_flip($NewModDomains);
            $NewSharedDomains = array_intersect_key($AllSharedDomains, $flippedNewModDomains);

            // some configured domains are not available in shared user account
            $IsMisconfigured = count($SharedDomains) != count($flippedModDomains) ||
                count($NewSharedDomains) != count($flippedNewModDomains);
        }

        if ($IsMisconfigured) {
            return [];
        }

        // shared_senderdomain_domains has format ["ktsend.com","ktemail.com"]
        $divisor = count($ModDomains);

        // choose domain by mod of user id
        $mod = $UserID % $divisor;

        if (VarNewSharedDomains::isActive($UserID)) {
            $domain = $NewModDomains[$mod];
            return $NewSharedDomains[$domain];
        }

        $domain = $ModDomains[$mod];
        return $SharedDomains[$domain];
    }

    /**
     * @param bool $reset reset static cache
     */
    public static function GetModDomains($shared_senderdomain_domains = '', $new = false, $reset = false)
    {
        static $ModDomains = [];
        if ($reset) {
            $ModDomains = [];
        }
        if (!isset($ModDomains[$new])) {
            if (empty($shared_senderdomain_domains)) {
                $shared_senderdomain_domains = Settings::get(
                    $new ? 'new_shared_senderdomain_domains' : 'shared_senderdomain_domains'
                );
            }
            $ModDomains[$new] = json_decode($shared_senderdomain_domains);
        }

        return $ModDomains[$new] ?? [];
    }

    /**
     * Check if given domain is shared one
     *
     * @param string $domain
     *
     * @return bool true if domain is shared. Otherwise false
     */
    public static function IsDomainShared($domain)
    {
        return in_array($domain, static::GetModDomains()) || in_array($domain, static::GetModDomains('', true));
    }

    public static function FillSenderParameters($domainset = '', $userID = 0, $host = '', $from = '')
    {
        $alias = null;
        $bounces = null;
        $isDomainShared = Domainset::IsDomainShared($domainset['Domain']);
        $dkimDomain = static::getDomainByEmailAddress($userID, $from);

        if (!empty($dkimDomain['Data']['dkimOnly'])) {
            $depunycodedDomain = DomainUtils::depunycodeDomain($dkimDomain['Domain']);
            $alias = DomainSet::EncodeSharedSubDomain($userID, $depunycodedDomain);
            if (
                // bounce- abuse- and unsubscribe- e-mail-addresses with non ASCII-Domains are not working
                // Janos is clarifying this issue with PostMastery
                $depunycodedDomain == $dkimDomain['Domain']
            ) {
                $bounces = $alias;
            }
        }

        if (empty($domainset)) {
            // failover: this is used only, if we have no valid config of shared senders

            $FAILOVER_DOMAIN = variable_get('klicktipp_shared_sender_domain', KLICKTIPP_DOMAIN);

            $Sender = array(
                'domain' => $FAILOVER_DOMAIN,
                'fromDomain' => $FAILOVER_DOMAIN,
                'host' => '',
                'bounces' => $bounces ?: variable_get('klicktipp_shared_return_path', BOUNCE_CATCHALL_DOMAIN),
                // == "bounces.".KLICKTIPP_DOMAIN,
                'alias' => $alias ?: variable_get('klicktipp_shared_alias', KLICKTIPP_DOMAIN),
                'x-mailer' => X_MAILER,
                'sender' => "verifications@$FAILOVER_DOMAIN",
                'abuse' => "abuse@$FAILOVER_DOMAIN",
                'fbl' => variable_get('klicktipp_shared_fbl_address', "fbl@$FAILOVER_DOMAIN"),
                'unsubscribe' => variable_get('klicktipp_aliases_unsubscribe', 'abmelden') . "@$FAILOVER_DOMAIN",
                'no-csa-header' => 0,
                'use-transactional-pool' => false,
            );
        } else {
            // basic sender definition from the whitelabel domain definitions
            $Sender = array(
                'domain' => $domainset['Domain'],
                // host = postfix sender host
                'host' => $host . (empty($host) ? '' : '.') . $domainset['Domain'],
                // bounces = MX
                'bounces' => $bounces ?: $domainset['Data']['mxhost'] . '.' . $domainset['Domain'],
                // alias = CNAME
                'alias' => $alias ?: $domainset['Data']['alias'] . '.' . $domainset['Domain'],
                'x-mailer' => $domainset['Domain'],
                // csa header
                'no-csa-header' => empty($domainset['Data']['no-csa-header']) ? 0 : 1,
                // transactional pool
                'use-transactional-pool' => !empty($domainset['Data']['useTransactionalPool']),
                'hasDkimCnames' => !empty($domainset['Data']['hasDkimCnames'])
            );
            if (!empty($userID) && $isDomainShared && static::useIndividualSubdomains($userID)) {
                $Sender['fromDomain'] = DomainSet::EncodeSharedSubDomain($userID, $domainset['Domain']);
            } else {
                $Sender['fromDomain'] = $domainset['Domain'];
            }
            // the following values are based upon
            $Sender['abuse'] = "abuse@" . $Sender['bounces'];
            $Sender['fbl'] = "fbl@" . $Sender['bounces'];
            $Sender['unsubscribe'] = "unsubscribe@" . $Sender['bounces'];

            // if From: is from whitelabel domain, then Sender: = From: (will suppress Sender:)
            if (strpos($from, "@{$domainset['Domain']}")) {
                $Sender['sender'] = $from;
            } else {
                $Sender['sender'] = "postmaster@" . $Sender['bounces'];
            }
        }

        $Sender['appurl'] = 'https://' . $Sender['alias'] . '/';
        $Sender['abuseurl'] = $Sender['appurl'] . 'abuse/';

        return $Sender;
    }

    public static function getDomainByEmailAddress($userID, $emailAddress)
    {
        if (!$userID || !$emailAddress) {
            return false;
        }

        $cache = &drupal_static(__METHOD__, []);
        $cacheKey = $userID . $emailAddress;
        if (!isset($cache[$cacheKey])) {
            $emailDomain = substr($emailAddress, strrpos($emailAddress, '@') + 1);
            $cache[$cacheKey] = static::getDomainByName($userID, $emailDomain);
        }

        return $cache[$cacheKey];
    }

    public static function getDomainByName($userID, $domainName)
    {
        $rootDomain = DomainUtils::getRootDomain($domainName);

        $domain = DomainSet::RetrieveDomain($userID, $rootDomain);
        if (is_array($domain) && $domain['Status'] != DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED) {
            $domain = false;
        }

        return $domain;
    }

    /**
     * Generate PowerMTA configuration
     */
    public static function MapExternal2InternalIP($ip)
    {
        static $mapping = [];

        // extract and cache mapping config (see admin settings aliases)
        if (empty($mapping)) {
            $klicktipp_pmta_ip_mapping = trim(variable_get('klicktipp_pmta_ip_mapping', ''));
            if (!empty($klicktipp_pmta_ip_mapping)) {
                $lines = explode("\n", $klicktipp_pmta_ip_mapping);
                foreach ($lines as $no => $line) {
                    // format is 213.227.171=172.17.171 (first 3 bytes only)
                    [$external, $internal] = explode("=", $line);
                    $mapping[trim($external)] = trim($internal);
                }
            }
        }

        [$ip1, $ip2, $ip3, $ip4] = explode('.', $ip);
        $internal = $mapping[implode('.', [$ip1, $ip2, $ip3])];
        if (empty($internal)) {
            // no mapping found: return external as fallback
            return $ip;
        } else {
            // found: return internal triple with added last byte
            return $internal . '.' . $ip4;
        }
    }

    /**
     * Generate PowerMTA configuration
     */
    public static function GenerateVirtualMTAConfig()
    {
        // save config to /srv/www/klick-tipp.com/vmtas/
        $VMTADir = VIRTUAL_MTA_CONFIG_DIR;

        // save dkim config to /srv/www/klick-tipp.com/dkim/
        $DKIMDir = DKIM_CONFIG_DIR;
        $DKIMconfig = "dkim_config";

        $ArrayDomains = static::RetrieveDomains();
        if (!empty($ArrayDomains)) {
            try {
                $FileWriterConfig = new FileWriterWithTmpFile(PMTA_VMTA_CONFIG_FILE, VIRTUAL_MTA_CONFIG_TMP_DIR);
                $FileWriterConfigInternal = new FileWriterWithTmpFile(
                    PMTA_VMTA_CONFIG_INTERNAL_FILE,
                    VIRTUAL_MTA_CONFIG_TMP_DIR
                );
                $FileWriterPools = new FileWriterWithTmpFile(PMTA_VMTA_POOLS_FILE, VIRTUAL_MTA_CONFIG_TMP_DIR);
                $FileWriterChecklist = new FileWriterWithTmpFile(PMTA_VMTA_CHECKLIST_FILE, VIRTUAL_MTA_CONFIG_TMP_DIR);

                $FileWriterPools->write(variable_get('klicktipp_pmta_shared_vmta_pools', '') . "\n\n");

                //IP-Adresse | Domain | Hostname Mailserver | Tracking Domain | Return Path Domain
                $FileWriterChecklist->write(
                    "IP-Adresse\tDomain\tHostname Mailserver\tTracking Domain\tReturn Path Domain\tLink\tDKIM\tEmail\n"
                );

                // prepare ip blocks
                $byip = array();
                // 188.172.250.*
                for ($i = 0; $i < 256; $i++) {
                    $byip["188.172.250.$i"] = array();
                }
                // 188.172.224.*
                // 188.172.225.*
                // 188.172.226.*
                // 188.172.227.*
                for ($k = 224; $k < 228; $k++) {
                    for ($i = 0; $i < 256; $i++) {
                        $byip["188.172.$k.$i"] = array();
                    }
                }
                // **************.*
                for ($i = 0; $i < 256; $i++) {
                    $byip["213.227.171.$i"] = array();
                }
                $noip = 'no-ip';
                $byip[$noip] = array();

                // prepare senderdomains
                $senders = array();

                foreach ($ArrayDomains as $domainset) {
                    if (!empty($domainset['Data']['dkimOnly'])) {
                        continue;
                    }

                    $domain = $domainset['Domain'];
                    $uid = $domainset['RelOwnerUserID'];

                    $valid = $domainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED;
                    $dkimkey_exists = !empty(static::GetWhitelabelDKIMLocation($domain));

                    $sender = static::FillSenderParameters($domainset);
                    $bounces = $sender['bounces'];
                    $alias = $sender['alias'];
                    $use_transactional_pool = $domainset['Data']['useTransactionalPool'];

                    $ArrayHosts = $domainset['Data']['hosts'];
                    $ArrayHostsData = $domainset['Data']['hostdata'];

                    // senderdomains
                    if ($valid) {
                        $senders[] = $bounces;
                    }

                    // header POOLS
                    if ($valid) {
                        $FileWriterPools->write("<virtual-mta-pool $domain>\n");
                    }

                    $email = user_load($uid)->mail ?? '';
                    foreach ($ArrayHosts as $host => $ip) {
                        $has_ip = ($host != $ip);
                        $ip = $has_ip ? $ip : $noip;
                        $transactional = $use_transactional_pool && $ArrayHostsData[$host]['confirmation'];
                        if ($has_ip && $valid) {
                            $internal = static::MapExternal2InternalIP($ip);
                            // content CONFIG (ek 2019-12-06: remove if anexia load balancer is in place)
                            $FileWriterConfig->write("<virtual-mta $host.$domain>\n");
                            $FileWriterConfig->write("  smtp-source-host $ip $host.$domain\n");
                            $FileWriterConfig->write("</virtual-mta>\n\n");
                            // content CONFIG INTERNAL
                            $FileWriterConfigInternal->write("<virtual-mta $host.$domain>\n");
                            $FileWriterConfigInternal->write("  smtp-source-host $internal $host.$domain\n");
                            $FileWriterConfigInternal->write("</virtual-mta>\n\n");
                            if (!$transactional) {
                                // content POOLS
                                $FileWriterPools->write("  virtual-mta $host.$domain\n");
                            }
                        }
                        // content CHECKLIST
                        //IP-Adresse | Domain | Hostname Mailserver | Tracking Domain | Return Path Domain | Link | DKIM
                        $link = url("/user/$uid/hostconfigure/$domain/$host", array('absolute' => true));
                        $byip[$ip][] = "$ip\t$domain\t$host.$domain\t$alias\t$bounces\t$link\t" . ($dkimkey_exists ? 'active' : 'NOT GENERATED') . "\t" . $email . "\n";
                    }

                    if ($valid) {
                        // footer POOLS
                        $FileWriterPools->write("</virtual-mta-pool>\n\n");
                    }

                    // TRANSACTIONAL POOLS
                    if ($valid && $use_transactional_pool) {
                        // header TRANSACTIONAL POOLS
                        $FileWriterPools->write("<virtual-mta-pool $domain-transactional>\n");

                        foreach ($ArrayHosts as $host => $ip) {
                            $has_ip = ($host != $ip);
                            $transactional = $ArrayHostsData[$host]['confirmation'];
                            if ($has_ip && $transactional) {
                                // content POOLS
                                $FileWriterPools->write("  virtual-mta $host.$domain\n");
                            }
                        }

                        // footer TRANSACTIONAL POOLS
                        $FileWriterPools->write("</virtual-mta-pool>\n\n");
                    }
                }

                // content CHECKLIST
                foreach ($byip as $ip => $lines) {
                    if (empty($lines)) {
                        $FileWriterChecklist->write("$ip\t\t\t\t\t\t\t\n");
                    } else {
                        foreach ($lines as $line) {
                            $FileWriterChecklist->write($line);
                        }
                    }
                }

                // senderdomains
                $senderdomains = variable_get(
                    'klicktipp_shared_return_path',
                    BOUNCE_CATCHALL_DOMAIN
                ); // == "bounces.".KLICKTIPP_DOMAIN
                $senderdomains .= ', ' . implode(', ', $senders);
                // CONFIG external ips (ek 2019-12-06: remove if anexia load balancer is in place)
                $FileWriterConfig->write("\n\n");
                $FileWriterConfig->write("# senderdomains macro\n");
                $FileWriterConfig->write("domain-macro senderdomains $senderdomains\n\n");
                $FileWriterConfig->write("# senderdomains config\n");
                $FileWriterConfig->write("<domain \$" . "senderdomains>\n");
                $FileWriterConfig->write("type pipe\n");
                $FileWriterConfig->write("command  \"/usr/local/bin/pmta-mail-wrapper.sh bounce\"\n");
                $FileWriterConfig->write("</domain>\n\n");
                // CONFIG internal ips
                $FileWriterConfigInternal->write("\n\n");
                $FileWriterConfigInternal->write("# senderdomains macro\n");
                $FileWriterConfigInternal->write("domain-macro senderdomains $senderdomains\n\n");
                $FileWriterConfigInternal->write("# senderdomains config\n");
                $FileWriterConfigInternal->write("<domain \$" . "senderdomains>\n");
                $FileWriterConfigInternal->write("type pipe\n");
                $FileWriterConfigInternal->write("command  \"/usr/local/bin/pmta-mail-wrapper.sh bounce\"\n");
                $FileWriterConfigInternal->write("</domain>\n\n");

                $FileWriterChecklist->finalize();
                $FileWriterPools->finalize();
                $FileWriterConfig->finalize();
                $FileWriterConfigInternal->finalize();
            } catch (Exception $e) {
                // cant create files
                $error = array(
                    '!dir' => $VMTADir,
                    '!message' => $e->getMessage()
                );
                Errors::unexpected(
                    "Error: PMTA config generation - failed to write-create files in dir !dir.",
                    $error
                );
            }

            //dkim config
            $alldomains = array();
            $missing_domains = array();

            try {
                $FileWriterDkimConfig = new FileWriterWithTmpFile("$DKIMDir/$DKIMconfig", DKIM_CONFIG_TMP_DIR);
                // add config for shared ip domains
                foreach (static::$SharedIpDomains as $domain) {
                    $filename = static::GetWhitelabelDKIMLocation($domain);
                    if ($filename !== false) {
                        // see BOUNCE_CATCHALL_DOMAIN for name of bounces host
                        $bounces = "bounces.$domain";

                        $FileWriterDkimConfig->write("domain-key default,$bounces,/etc/pmta/dkim/$domain.key.pem\n");
                        $FileWriterDkimConfig->write("domain-key default,$domain,/etc/pmta/dkim/$domain.key.pem\n");

                        $alldomains[] = $domain;
                    } else {
                        $missing_domains[] = $domain;
                    }
                }

                // add config for whitelabel domains
                foreach ($ArrayDomains as $domainset) {
                    $domain = $domainset['Domain'];
                    if (!empty($domainset['Data']['dkimOnly'])) {
                        $alldomains[] = $domain;
                        continue;
                    }

                    $valid = $domainset['Status'] == DomainSet::WHITELABELDOMAIN_STATUS_VERIFIED;

                    $filename = static::GetWhitelabelDKIMLocation($domain);
                    if ($filename !== false) {
                        $sender = static::FillSenderParameters($domainset);
                        $bounces = $sender['bounces'];

                        if ($valid) {
                            $FileWriterDkimConfig->write("domain-key ktdkim,$bounces,/etc/pmta/dkim/$domain.key.pem\n");
                            $FileWriterDkimConfig->write("domain-key ktdkim,$domain,/etc/pmta/dkim/$domain.key.pem\n");
                        }

                        $alldomains[] = $domain;
                    } else {
                        $missing_domains[] = $domain;
                    }
                }
                $FileWriterDkimConfig->finalize();

                // remove dkim keys for deleted domains
                if (is_dir(DKIM_CONFIG_DIR . "pubkeys")) {
                    if ($dh = opendir(DKIM_CONFIG_DIR . "pubkeys")) {
                        while (($file = readdir($dh)) !== false) {
                            $domain = str_replace('.key.pem', '', $file, $count);
                            if ($count && !in_array($domain, $alldomains)) {
                                // domain has been deleted, so delete dkim files
                                unlink(DKIM_CONFIG_DIR . "pubkeys/$domain.key.pem");
                                unlink(DKIM_CONFIG_DIR . "privatekeys/$domain.key.pem");
                                unlink(DKIM_CONFIG_DIR . "pubtxt/$domain.key.pem");
                            }
                        }
                        closedir($dh);
                    }
                }

                // write missing domains to file
                $FileWriterMissingDomains = new FileWriterWithTmpFile("$DKIMDir/missing_domains", DKIM_CONFIG_TMP_DIR);
                if (empty($missing_domains)) {
                    $FileWriterMissingDomains->write("No DKIM key missing for any domain.\n");
                } else {
                    $FileWriterMissingDomains->write("There is no DKIM key for these domains:\n");
                    $FileWriterMissingDomains->write(implode("\n", $missing_domains));
                }
                $FileWriterMissingDomains->finalize();
            } catch (Exception $e) {
                // cant create files
                $error = array(
                    '!dir' => $DKIMDir,
                    '!message' => $e->getMessage()
                );
                Errors::unexpected(
                    "Error: DKIM config generation - failed to write-create files in dir !dir.",
                    $error
                );
            }
        }

        // pmta email forwarders
        ProcessLog::CreateUniqueQueueItem('request_forwarders_queue');
    }

    /**
     * @param string $domain
     * @return array
     */
    protected static function GetNameservers($domain)
    {
        $digResult = call_user_func(static::$digFunction, $domain, 'NS', [], 2, 2);

        // ;; ANSWER SECTION:\nendlich-ohne-schufa.info. 756    IN  NS  ns4.klick-tipp.com.
        preg_match_all('/^\S+\s+\S+\s+IN\s+NS\s+(.*)$/m', $digResult, $matches);

        return $matches[1];
    }

    /**
     * Retrieves fallback nameserver to use in case responsible nameservers
     * refuse to answer. NOTE: In order to avoid requesting always same server first the
     * list is sorted randomly
     *
     * @return string[]
     */
    protected static function GetFallbackNameservers()
    {
        $fallbackNameservers = static::FALLBACK_NAMESERVERS;
        shuffle($fallbackNameservers);
        return $fallbackNameservers;
    }

    /**
     * indicated if fallback servers were used
     *
     * @return bool
     */
    public static function wereFallbackNameserversUsed()
    {
        return static::$fallbackNameserversUsed;
    }

    /**
     * Sets function to use for processing of dig calls (useful for tests to prevent real dig calls)
     *
     * @param callable $digFunction
     */
    public static function setDigFunction(callable $digFunction)
    {
        static::$digFunction = $digFunction;
    }

    /**
     * Sets a function to use for retrieving dns keys (useful for tests)
     *
     * @param callable $keysFunction
     */
    public static function setDnsKeysFunction(callable $keysFunction)
    {
        static::$keysFunction = $keysFunction;
    }

    /**
     * Retrieve all domains appiable for sender addresses of specified account
     *
     * @param stdClass $account
     * @param bool $includeMxDomains
     *
     * @return array|string[]
     */
    public static function RetrieveSenderDomainNames($account, $includeMxDomains = true)
    {
        $user = (array)$account;

        $shared = false;
        $result = [];
        $allDomainDefinitions = static::GetValidSenderDomains($user);
        if (!is_array($allDomainDefinitions) || empty($allDomainDefinitions)) {
            $allDomainDefinitions = []; // just in case static::GetValidSenderDomains return TRUE
            $sharedDomain = static::GetSharedSenderDomains($user['uid']);
            if (!empty($sharedDomain)) {
                $allDomainDefinitions[] = $sharedDomain;
            }
            $shared = true;
        }
        foreach ($allDomainDefinitions as $domainDefinition) {
            if ($domainDefinition['Status'] != static::WHITELABELDOMAIN_STATUS_VERIFIED) {
                continue;
            }
            if ($shared) {
                $subdomain = DomainSet::EncodeSharedSubDomain($user['uid'], $domainDefinition['Domain']);
                $result[] = $subdomain;
            } else {
                array_push(
                    $result,
                    ...static::RetrieveWhitelabelSenderDomainNames($domainDefinition, $includeMxDomains)
                );
            }
        }

        return $result;
    }


    /**
     * Retrieves domain names usable for sender email addresses
     *
     * @param array $domainDefinition
     * @param bool $includeMxDomains
     *
     * @return array|string[]
     */
    public static function RetrieveWhitelabelSenderDomainNames($domainDefinition, $includeMxDomains = true)
    {
        $domains = [];
        $domain = $domainDefinition['Domain'];
        $domains[] = $domain;
        if ($includeMxDomains) {
            $mxDomain = $domainDefinition['Data']['mxhost'] . '.' . $domain;
            $domains[] = $mxDomain;
        }
        return $domains;
    }

    /**
     * Return shared subdomain for user
     * @param $UserID
     * @param $Domain
     *
     * @return string
     */
    public static function EncodeSharedSubDomain($UserID, $Domain)
    {
        return 'www' . Core::CryptNumber($UserID) . ".$Domain";
    }

    /**
     * Get UserID and domain from shared subdomain
     * @param $SubDomain
     *
     * @return array: [int: decoded UserID, string: domain]
     */
    public static function DecodeSharedSubDomain($SubDomain)
    {
        $parts = explode('.', $SubDomain);
        $subdomain = array_shift($parts);

        // don't consider prefix, take only numbers on the end of subdoman.
        // prefix is currently hardcoded, but it may change in feature
        // so we don't check for exact prefix
        if (preg_match('/[\d]+$/', $subdomain, $matches)) {
            $UserID = Core::DecryptNumber($matches[0]);
            return [$UserID, implode('.', $parts)];
        }

        return [null, ''];
    }


    /**
     * @param stdClass $user
     *
     * @return array|bool if true => fallback to shared domains
     */
    public static function getCachedSenderDomains(stdClass $user)
    {
        $cache = &drupal_static(__METHOD__, []);
        return $cache[$user->uid] ??= DomainSet::GetValidSenderDomains((array)$user);
    }

    /**
     * Checks if user specified by id is a shared sender
     *
     * An user is shared sender if:
     *   - he is not allowed to use whitelabel domains
     *      OR
     *   - he doesn't habe any whitelabel domains
     *   - none of existing domain is valid AND flag
     *     "Use shared domain as fallback sender" is set
     *
     * @param stdClass $user
     *
     * @return bool
     */
    public static function isSharedSender(stdClass $user)
    {
        return !is_array(static::getCachedSenderDomains($user));
    }

    /**
     * Checks, if are fields needed for whitelabel domains are set
     *
     * @param array $domainset
     *
     * @return bool
     */
    public static function areWhitelabelFieldsSet(array $domain)
    {
        return empty(array_filter(['hosts', 'alias', 'mxhost'], fn($field) => empty($domain['Data'][$field])));
    }


    /**
     * Use new shared domain for user
     *
     * Yes, if
     *  - some new domains are specified AND
     *  - Last user of old domains is specified AND
     *  - UserID is higher than specified id of last old domain user
     *
     *
     * @param int $userID
     *
     * @return bool
     */
    public static function useIndividualSubdomains($userID): bool
    {
        return
            !empty(static::GetModDomains('', true)) &&
            (
                $userID > variable_get('last_user_without_subdomains', PHP_INT_MAX) ||
                VarNewSharedDomains::isActive($userID)
            );
    }

    public static function checkSenderMXRecords($domainset, $nameservers): void
    {
        if (empty($nameservers)) {
            return;
        }

        if (!static::isSenderMxCheckActive()) {
            return;
        }

        $user = user_load($domainset['RelOwnerUserID']);
        if (!$user) {
            return;
        }

        $subscriberID = Subscription::RetrieveSubscriberIDByEmailAddress(static::$marketingAccountId, $user->mail);
        if (!$subscriberID) {
            return;
        }

        $customValue = CustomFields::GetCustomFieldData(
            static::$marketingAccountId,
            $subscriberID,
            static::$senderMxCheckCustomField,
            0
        );

        $domainWithoutMx = [];
        if (!empty($customValue)) {
            $domainWithoutMx = array_flip(explode(',', $customValue));
        }

        $subscriberIsTagged = (bool)Subscribers::RetrieveTagging(
            static::$marketingAccountId,
            static::$senderMxCheckTagId,
            $subscriberID,
            0
        );

        $updateCustomField = false;

        $domains = [$domainset['Domain']];
        array_push($domains, ...static::getRelatedSenderAddressesSubdomains($domainset));

        foreach ($domains as $domain) {
            // according to DEV-3890 only needed for DKIM Domains
            if (empty($domainset['Data']['dkimOnly'])) {
                $mxRecordMissing = false;
            } else {
                $r = call_user_func(static::$digFunction, $domain, 'MX', $nameservers);
                $mxRecordMissing = !preg_match('/^' . preg_quote($domain) . '\.\s+\S+\s+IN\s+MX\s+\S+\s+\S+$/im', $r);
            }

            $domainStored = isset($domainWithoutMx[$domain]);
            if ($mxRecordMissing) {
                if (!$domainStored) {
                    $domainWithoutMx[$domain] = 0;
                    $updateCustomField = true;
                }
            } else {
                if ($domainStored) {
                    unset($domainWithoutMx[$domain]);
                    $updateCustomField = true;
                }
            }
        }

        $tagSubscriber = !empty($domainWithoutMx);
        if ($subscriberIsTagged && !$tagSubscriber) {
            Subscribers::UntagSubscriber(static::$marketingAccountId, $subscriberID, 0, static::$senderMxCheckTagId);
        } elseif (!$subscriberIsTagged && $tagSubscriber) {
            Subscribers::TagSubscriber(static::$marketingAccountId, $subscriberID, static::$senderMxCheckTagId, 0);
        }
        if ($updateCustomField) {
            CustomFields::UpdateCustomFieldData(
                static::$marketingAccountId,
                $subscriberID,
                0,
                static::$senderMxCheckCustomField,
                implode(',', array_keys($domainWithoutMx))
            );
        }
    }

    private static function isSenderMxCheckActive(): bool
    {
        if (static::$isSenderMxCheckActive === null) {
            static::initSenderMxCheck();
        }
        return static::$isSenderMxCheckActive;
    }

    private static function initSenderMxCheck(): void
    {
        $marketingAccountId = (int)variable_get('klicktipp_marketing_account_id');
        if ($marketingAccountId < 1) {
            static::$isSenderMxCheckActive = false;
            return;
        }
        $settings = variable_get('klicktipp_domain_mx_check', []);

        $isActive = $settings['active'] ?? false;
        if (!$isActive) {
            static::$isSenderMxCheckActive = false;
            return;
        }

        $tagId = (int)$settings['tag_id'];
        if (!Tag::FromID($marketingAccountId, $tagId)) {
            static::$isSenderMxCheckActive = false;
            return;
        }

        $customFieldId = (int)$settings['custom_field_id'];
        $customField = CustomFields::RetrieveCustomField($customFieldId, $marketingAccountId);
        if (!$customField) {
            static::$isSenderMxCheckActive = false;
            return;
        }

        static::$isSenderMxCheckActive = true;
        static::$marketingAccountId = $marketingAccountId;
        static::$senderMxCheckTagId = $tagId;
        static::$senderMxCheckCustomField = $customField;
    }

    /**
     * @param array $domainset see DomainSet::RetrieveDomain
     * @param string $hostname hostname (without root domain, only subdomain part
     * @param string $ipAddress ip string (e.g. *************)
     *
     * @return void
     *
     * @throws CSAException
     */
    public static function setHost($domainset, $hostname, $ipAddress)
    {
        $currentIpAddress = static::getHostIp($domainset, $hostname);

        // =========================== update CSA tokens ======================================

        $fullHostname = $hostname . '.' . $domainset['Domain'];
        if ($currentIpAddress != $ipAddress) {
            if (static::isCsaSyncRequired($domainset)) {
                $domainset['Data']['hostdata'][$hostname]['csa'] = CSAClient::registerIp($ipAddress, $fullHostname);
                if ($currentIpAddress) {
                    CSAClient::deleteIp($currentIpAddress, $fullHostname);
                }
            } else {
                $ipinfo = CSAClient::getIpInfo($ipAddress);
                if (!empty($ipinfo)) {
                    //t('ip @ip already listed on CSA');
                    throw new CSAException(
                        'ip @ip already listed on CSA',
                        CSAException::CODE_UNEXPECTED_RESPONSE_CODE,
                        ['@ip' => $ipAddress]
                    );
                }
            }
        }

        $domainset['Data']['hosts'][$hostname] = $ipAddress;
        DomainSet::UpdateWhitelabelDomain($domainset['Domain'], $domainset);
    }

    private static function getHostIp($domainset, $hostname)
    {
        return $domainset['Data']['hosts'][$hostname] ?? null;
    }

    /**
     * Is it possible to execute operations related to hosts ?
     *
     * @param array $domainset
     *
     * @return bool
     */
    public static function hostsAlterable(array $domainset): bool
    {
        return CSAClient::isActive() || !static::isCsaSyncRequired($domainset);
    }

    /**
     * Is it possibley to set/unset no-csa-header flag
     *
     * @param array $domainset
     *
     * @return bool
     */
    public static function csaHeaderFlagAlterable(array $domainset): bool
    {
        return CSAClient::isActive() ||
            empty($domainset['Data']['hosts']);
    }

    /**
     * @param array $domainset
     *
     * @return bool
     */
    public static function isCsaSyncRequired(array $domainset): bool
    {
        return empty($domainset['Data']['no-csa-header']);
    }

    /**
     * @param array{
     *     Domain: string,
     *     Data: array<string, scalar>
     * } $domainset
     * @param array{
     *     expected: array{
     *         host: scalar,
     *         type: scalar,
     *         value: scalar,
     *         ttl: scalar
     *     },
     *     obtained: array{
     *         host: scalar,
     *         type: scalar,
     *         value: scalar,
     *         ttl: scalar
     *     },
     *     status: bool,
     *     dkimStatus: bool,
     *     description: string
     * }[] $result
     * @param string[] $nameservers
     */
    private static function enrichResultWithDmarcInfo(array &$domainset, array &$result, array $nameservers): void
    {
        $dnsResolver = new DnsResolver();

        $domain = '_dmarc.' . $domainset['Domain'];

        $obtained = [
            'host' => $domain,
            'type' => 'TXT',
        ];

        $statusIfMissing = variable_get('klicktipp_invalidate_domains_without_dmarc') ? false : null;
        $status = true;
        try {
            $records = $dnsResolver->getAllTXT($domain, $nameservers);
            if (count($records) > 1) {
                $status = $statusIfMissing;
            } elseif (!self::getDmarcValidator()->validate($records[0]->value)->isValid()) {
                $status = $statusIfMissing;
            }
            $obtained['value'] = implode('\n', array_map(fn($record) => $record->value, $records));
            $obtained['ttl'] = implode('\n', array_map(fn($record) => $record->ttl, $records));
        } catch (DnsRecordNotFoundException $e) {
            $status = $statusIfMissing;
        }
        $domainset['Data']['hasDmarcRecord'] = (bool)$status;

        $result[] = [
            'expected' => [
                'host' => $domain,
                'type' => 'TXT',
                'value' => 'v=DMARC1; p=none',
                'ttl' => static::EXPECTED_TTL,
            ],
            'obtained' => $obtained,
            'status' => $status,
            'dkimStatus' => $status,
            'description' => '',
        ];
    }

    /**
     * @param array $domainset
     * @return int
     */
    public static function countNonConfirmationHosts(array $domainset): int
    {
        $countNormalHosts = count($domainset['Data']['hosts'] ?? []);
        foreach ($domainset['Data']['hostdata'] as $data) {
            if ($data['confirmation']) {
                $countNormalHosts--;
            }
        }
        return $countNormalHosts;
    }

    /**
     * @param array $domainset
     * @return bool
     */
    public static function hasNonConfirmationHosts(array $domainset): bool
    {
        return static::countNonConfirmationHosts($domainset ) > 0;
    }

    private static function getDmarcValidator(): Validator
    {
        return self::$dmarcValidator ??= new Validator();
    }
}

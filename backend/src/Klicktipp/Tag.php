<?php

namespace App\Klicktipp;

use App\Klicktipp\Tag\Queue\ValueObject\TaggingDbValueObject;
use App\Klicktipp\Tag\TagLinkResolver;
use DatabaseStatementInterface;
use Doctrine\DBAL\Exception;
use stdClass;

/**
 * Tag class
 *
 * This class holds all tag related functions
 **/
class Tag extends DatabaseTableLabeled
{
    public const CATEGORY_MANUAL = 1; // manual; Entity is a Taxonomy
    public const CATEGORY_SMARTLINK = 2; // manual with smart link; Entity is a Taxonomy

    // newsletter autoresponder
    public const CATEGORY_SENT = 10;
    public const CATEGORY_OPENED = 11;
    public const CATEGORY_CLICKED = 12;
    public const CATEGORY_VIEWED = 13;
    public const CATEGORY_CONVERTED = 14;

    public const CATEGORY_KAJABI_ACTIVATE = 20;
    public const CATEGORY_KAJABI_DEACTIVATE = 21;
    public const CATEGORY_OUTBOUND = 22;
    public const CATEGORY_TAGGING_PIXEL = 23;

    // processlog campaign
    public const CATEGORY_EMAIL_SENT = 30;
    public const CATEGORY_EMAIL_OPENED = 31;
    public const CATEGORY_EMAIL_CLICKED = 32;
    public const CATEGORY_EMAIL_VIEWED = 33;

    public const CATEGORY_SMS_SENT = 40;
    public const CATEGORY_SMS_CLICKED = 42;

    public const CATEGORY_AUTOMATION_STARTED = 50;
    public const CATEGORY_AUTOMATION_FINISHED = 51;

    public const CATEGORY_LISTBUILDING_APIKEY = 60;
    public const CATEGORY_LISTBUILDING_REQUEST = 61;
    public const CATEGORY_LISTBUILDING_SMS = 62;
    public const CATEGORY_LISTBUILDING_FORMS = 63;
    public const CATEGORY_LISTBUILDING_FACEBOOK = 64;
    public const CATEGORY_LISTBUILDING_BUSINESSCARD = 65;
    public const CATEGORY_LISTBUILDING_EVENT = 66;
    public const CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED = 67;

    public const CATEGORY_LISTBUILDING_PAYMENT = 70;
    public const CATEGORY_LISTBUILDING_REFUNDED = 71;
    public const CATEGORY_LISTBUILDING_CHARGEDBACK = 72;
    public const CATEGORY_LISTBUILDING_REBILL_CANCELED = 73;
    public const CATEGORY_LISTBUILDING_REBILL_RESUMED = 74;
    public const CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION = 75;
    public const CATEGORY_LISTBUILDING_REBILL_CANCELED_LAST_DAY = 76;
    public const CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT = 77;
    public const CATEGORY_LISTBUILDING_DEFERRED_PAYMENT = 78;

    public const CATEGORY_PLUGIN_INBOUND_READY = 81;
    public const CATEGORY_PLUGIN_INBOUND_STARTED = 82;
    public const CATEGORY_PLUGIN_INBOUND_INPROGRESS = 83;
    public const CATEGORY_PLUGIN_INBOUND_FINISHED = 84;

    public const CATEGORY_PAYMENT_COMPLETED = 95;
    public const CATEGORY_PAYMENT_EXPIRED = 96;

    public const CATEGORY_FACEBOOK_AUDIENCE = 100;

    public static $DBTableName = 'tag';
    public static $DBSerialField = 'TagID';
    public static $EntityBaseType = DatabaseNamedTable::BASETYPE_TAG;
    public static $DBTypeField = 'Category';

    public static $TagCategory = Tag::CATEGORY_MANUAL;
    public static $EntityClass = Tag::class;

    public static $RequiredFieldsOnInsert = array(
        'RelOwnerUserID',
        'EntityID',
        'Category',
    );

    public static $RequiredFieldsOnUpdate = array(
        'TagID',
        'RelOwnerUserID',
    );

    public static $DefaultFields = array(
        'TagData' => '',
        'SmartLink' => '',
        'MultiValue' => 1,
    );

    // on Import, copy the following fields @see DatabaseTableCRUD::Import
    public static $ImportFields = [
        'MultiValue'
    ];

    public static function FromID($UserID, $SerialID)
    {
        if (empty($UserID) || empty($SerialID)) {
            return false;
        }

        $DBArray = Tag::RetrieveTag($UserID, $SerialID, true);
        if (empty($DBArray)) {
            return false;
        }

        return static::FromArray($DBArray);
    }

    public static function FromArray($DBArray)
    {
        switch ($DBArray['Category']) {
            case Tag::CATEGORY_SMARTLINK:
                $entity = new TagCategorySmartLink($DBArray);
                break;
            case Tag::CATEGORY_SENT:
                $entity = new TagCategoryCampaignSent($DBArray);
                break;
            case Tag::CATEGORY_OPENED:
                $entity = new TagCategoryCampaignOpened($DBArray);
                break;
            case Tag::CATEGORY_CLICKED:
                $entity = new TagCategoryCampaignClicked($DBArray);
                break;
            case Tag::CATEGORY_VIEWED:
                $entity = new TagCategoryCampaignViewed($DBArray);
                break;
            case Tag::CATEGORY_CONVERTED:
                $entity = new TagCategoryCampaignConverted($DBArray);
                break;
            case Tag::CATEGORY_KAJABI_ACTIVATE:
                $entity = new TagCategoryOutboundKajabiActivated($DBArray);
                break;
            case Tag::CATEGORY_KAJABI_DEACTIVATE:
                $entity = new TagCategoryOutboundKajabiDeactivated($DBArray);
                break;
            case Tag::CATEGORY_OUTBOUND:
                $entity = new TagCategoryOutboundActivated($DBArray);
                break;
            case Tag::CATEGORY_TAGGING_PIXEL:
                $entity = new TagCategoryTaggingPixel($DBArray);
                break;
            case Tag::CATEGORY_EMAIL_SENT:
                $entity = new TagCategoryEmailSent($DBArray);
                break;
            case Tag::CATEGORY_EMAIL_OPENED:
                $entity = new TagCategoryEmailOpened($DBArray);
                break;
            case Tag::CATEGORY_EMAIL_CLICKED:
                $entity = new TagCategoryEmailClicked($DBArray);
                break;
            case Tag::CATEGORY_EMAIL_VIEWED:
                $entity = new TagCategoryEmailViewed($DBArray);
                break;
            case Tag::CATEGORY_SMS_SENT:
                $entity = new TagCategorySMSSent($DBArray);
                break;
            case Tag::CATEGORY_SMS_CLICKED:
                $entity = new TagCategorySMSClicked($DBArray);
                break;
            case Tag::CATEGORY_AUTOMATION_STARTED:
                $entity = new TagCategoryAutonmationStarted($DBArray);
                break;
            case Tag::CATEGORY_AUTOMATION_FINISHED:
                $entity = new TagCategoryAutonmationFinished($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_APIKEY:
                $entity = new TagCategoryAPIKey($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_BUSINESSCARD:
                $entity = new TagCategoryBusinessCard($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_EVENT:
                $entity = new TagCategoryEvent($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_REQUEST:
                $entity = new TagCategoryRequest($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_SMS:
                $entity = new TagCategorySMSListbuilding($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_FORMS:
                $entity = new TagCategoryForms($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_PAYMENT:
                $entity = new TagCategoryPayment($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT:
                $entity = new TagCategorySubsequentPayment($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_DEFERRED_PAYMENT:
                $entity = new TagCategoryDeferredPayment($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_REFUNDED:
                $entity = new TagCategoryRefunded($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_CHARGEDBACK:
                $entity = new TagCategoryChargedback($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED:
                $entity = new TagCategoryRebillCanceled($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED_LAST_DAY:
                $entity = new TagCategoryRebillCanceledLastDay($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_REBILL_RESUMED:
                $entity = new TagCategoryRebillResumed($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION:
                $entity = new TagCategoryDigistoreAffiliation($DBArray);
                break;
            case Tag::CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED:
                $entity = new TagCategoryLandingPageSubscribed($DBArray);
                break;
            case Tag::CATEGORY_PAYMENT_COMPLETED:
                $entity = new TagCategoryPaymentCompleted($DBArray);
                break;
            case Tag::CATEGORY_PAYMENT_EXPIRED:
                $entity = new TagCategoryPaymentExpired($DBArray);
                break;
            case Tag::CATEGORY_FACEBOOK_AUDIENCE:
                $entity = new TagCategoryFacebookAudience($DBArray);
                break;
            case Tag::CATEGORY_PLUGIN_INBOUND_READY:
                $entity = new TagCategoryPluginInboundReady($DBArray);
                break;
            case Tag::CATEGORY_PLUGIN_INBOUND_STARTED:
                $entity = new TagCategoryPluginInboundStarted($DBArray);
                break;
            case Tag::CATEGORY_PLUGIN_INBOUND_INPROGRESS:
                $entity = new TagCategoryPluginInboundInProgress($DBArray);
                break;
            case Tag::CATEGORY_PLUGIN_INBOUND_FINISHED:
                $entity = new TagCategoryPluginInboundFinished($DBArray);
                break;

            default:
                // fall through - create "manual" tag
                $entity = new Tag($DBArray);
        }

        if (empty($entity->DataFlat)) {
            return false;
        }

        return $entity;
    }

    public static function InsertDB($Data)
    {
        return Tag::CreateManualTag(
            $Data['RelOwnerUserID'],
            $Data['TagName'],
            '',
            '',
            $Data[DatabaseTableLabeled::LABEL_DATA_KEY]
        );
    }

    public static function UpdateDB($DataFlat)
    {
        return Tag::UpdateTag($DataFlat);
    }

    public static function DeleteDB($Data)
    {
        return Tag::DeleteTag($Data['TagID'], $Data['RelOwnerUserID']);
    }

    public static function DeleteOfUser($UserID)
    {
        return Tag::DeleteOfUser($UserID);
    }

    // DatabaseTableCRUD

    public static $APIPath = 'kt-tag';

    public static $APIFields = array(
        'TagID' => array(
            'id' => 'tagid',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'TagName' => array(
            'id' => 'name',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Category' => array(
            'id' => 'category',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'MetaLabels' => array(
            'id' => 'metalabels',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM,
        ),
    );

    /**
    * @param array{'types': array<string>} $filter
    * @return array|mixed
    * @throws \ServicesException
     */
    public static function klicktippapi_index_advanced($filter)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        if (!empty($filter['types'])) {
            $types = array_keys(array_intersect(self::$APIIndexFilterTypes, $filter['types']));
        } else {
            $types = [static::$TagCategory];
        }

        $tags = self::GetTagsCache($account->uid);

        $entities = array();
        foreach ($tags as $tagId => $tag) {
            if (in_array($tag['Category'], $types, false)) {
                $entities[$tagId] = array(
                    'id' => $tagId,
                    'name' => $tag['EntityName'],
                    'type' => static::$APIIndexFilterTypes[$tag['Category']],
                );
            }
        }

        return array(
            'data' => $entities,
            'filter' => $filter,
            'pager' => array()
        );
    }

    public static function FilterObject($data)
    {
        $result = parent::FilterObject($data);

        foreach ($data as $key => $value) {
            if (isset(static::$APIFields[$key])) {
                switch ($key) {
                    case 'Category':
                        $result[static::$APIFields[$key]['id']] = Tag::$DisplayCategoryTypes[$value];
                        $result['type'] = static::$APIIndexFilterTypes[static::$TagCategory];
                        break;
                }
            }
        }
        return $result;
    }

    public static function UnFilterObject($data, $result = array(), $isObjectNew = false)
    {
        $result = parent::UnFilterObject($data, $result);

        foreach (static::$APIFields as $key => $def) {
            if (isset($data[$def['id']])) {
                switch ($key) {
                    case 'Category':
                        unset($result[$key]); //do not change the category via api
                        unset($result['type']);
                        break;
                }
            }
        }

        return $result;
    }

    public static function GetEntitiesAsOptionsArray($UserID)
    {
        if (empty($UserID)) {
            return array();
        }

        $AllTags = Tag::GetTagsCache($UserID);
        $ArrayTags = array();
        foreach ($AllTags as $tagid => $tag) {
            if ($tag['Category'] == static::$TagCategory) {
                $ArrayTags[$tagid] = $tag['EntityName'];
            }
        }

        natcasesort($ArrayTags);

        return $ArrayTags;
    }

    public static function GetNameForOptionsArray($DBArray)
    {
        return static::GetTagName($DBArray);
    }

    public static function SetNameInDBArray(&$DBArray, $NewName)
    {
        $DBArray['TagName'] = $NewName;
    }

    //Note: the comments help the translation parser
    //      'action:Manual Tag' is used in Tag::GetTagFilter()
    public const TAG_CATEGORY_MANUAL = /*t(*/'Manual Tag'/*)*/; //t('action:Manual Tag')
    public const TAG_CATEGORY_SMARTLINK = /*t(*/'SmartLink'/*)*/; //t('action:SmartLink')
    public const TAG_CATEGORY_SENT = /*t(*/'Sent'/*)*/; //t('action:Sent')
    public const TAG_CATEGORY_OPENED = /*t(*/'Opened'/*)*/; //t('action:Opened')
    public const TAG_CATEGORY_CLICKED = /*t(*/'Link clicked'/*)*/; //t('action:Link clicked')
    public const TAG_CATEGORY_VIEWED = /*t(*/'Browser view'/*)*/; //t('action:Browser view')
    public const TAG_CATEGORY_CONVERTED = /*t(*/'Conversion'/*)*/; //t('action:Conversion')
    public const TAG_CATEGORY_KAJABI_ACTIVATE = /*t(*/'Kajabi Activation'/*)*/; //t('action:Kajabi Activation')
    public const TAG_CATEGORY_KAJABI_DEACTIVATE = /*t(*/'Kajabi Deactivation'/*)*/; //t('action:Kajabi Deactivation')
    public const TAG_CATEGORY_OUTBOUND = /*t(*/'Activation'/*)*/; //t('action:Activation')
    public const TAG_CATEGORY_TAGGING_PIXEL = /*t(*/'Pixel Tracked'/*)*/; //t('action:Pixel Tracked')
    public const TAG_CATEGORY_EMAIL_SENT = /*t(*/'Email Sent'/*)*/; //t('action:Email Sent')
    public const TAG_CATEGORY_EMAIL_OPENED = /*t(*/'Email Opened'/*)*/; //t('action:Email Opened')
    public const TAG_CATEGORY_EMAIL_CLICKED = /*t(*/'Email Clicked'/*)*/; //t('action:Email Clicked')
    public const TAG_CATEGORY_EMAIL_VIEWED = /*t(*/'Email Viewed'/*)*/; //t('action:Email Viewed')
    public const TAG_CATEGORY_SMS_SENT = /*t(*/'SMS Sent'/*)*/; //t('action:SMS Sent')
    public const TAG_CATEGORY_SMS_CLICKED = /*t(*/'SMS Clicked'/*)*/; //t('action:SMS Clicked')
    public const TAG_CATEGORY_AUTOMATION_STARTED = /*t(*/'Automation Started'/*)*/; //t('action:Automation Started')
    public const TAG_CATEGORY_AUTOMATION_FINISHED = /*t(*/'Automation Finished'/*)*/; //t('action:Automation Finished')
    public const TAG_CATEGORY_LISTBUILDING_APIKEY = /*t(*/'Subscribed by API'/*)*/; //t('action:Subscribed by API')
    //t('action:Subscribed by Business Card')
    public const TAG_CATEGORY_LISTBUILDING_BUSINESSCARD = /*t(*/'Subscribed by Business Card'/*)*/;
    public const TAG_CATEGORY_LISTBUILDING_EVENT = /*t(*/'Subscribed by Event'/*)*/; //t('action:Subscribed by Event')
    //t('action:Subscribed by Email Request')
    public const TAG_CATEGORY_LISTBUILDING_REQUEST = /*t(*/'Subscribed by Email Request'/*)*/;
    public const TAG_CATEGORY_LISTBUILDING_SMS = /*t(*/'Subscribed by SMS'/*)*/; //t('action:Subscribed by SMS')
    public const TAG_CATEGORY_LISTBUILDING_FORMS = /*t(*/'Subscribed by Form'/*)*/; //t('action:Subscribed by Form')
    public const TAG_CATEGORY_LANDING_PAGE_SUBSCRIBED =
        /*t(*/'Subscribed by Landing Page'/*)*/; //t('action:Subscribed by Landing Page')
    public const TAG_CATEGORY_LISTBUILDING_PAYMENT =
        /*t(*/'Subscribed by Payment'/*)*/; //t('action:Subscribed by Payment')
    //t('action:Subsequent Payment')
    public const TAG_CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT = /*t(*/'Subsequent Payment'/*)*/;
    public const TAG_CATEGORY_LISTBUILDING_DEFERRED_PAYMENT =
        /*t(*/'Deferred Payment'/*)*/; //t('action:Deferred Payment')
    public const TAG_CATEGORY_LISTBUILDING_REFUNDED = /*t(*/'Payment Refunded'/*)*/; //t('action:Payment Refunded')
    public const TAG_CATEGORY_LISTBUILDING_CHARGEDBACK =
        /*t(*/'Payment Chargedback'/*)*/; //t('action:Payment Chargedback')
    public const TAG_CATEGORY_LISTBUILDING_REBILL_CANCELED = /*t(*/'Rebill Canceled'/*)*/; //t('action:Rebill Canceled')
    //t('action:Rebill Canceled Last Day')
    public const TAG_CATEGORY_LISTBUILDING_REBILL_CANCELED_LAST_DAY = /*t(*/'Rebill Canceled Last Day'/*)*/;
    public const TAG_CATEGORY_LISTBUILDING_REBILL_RESUMED = /*t(*/'Rebill Resumed'/*)*/; //t('action:Rebill Resumed')
    //t('action:Digistore Affiliation')
    public const TAG_CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION = /*t(*/'Digistore Affiliation'/*)*/;
    public const TAG_CATEGORY_PAYMENT_COMPLETED = /*t(*/'Payment Completed'/*)*/; //t('action:Payment Completed')
    public const TAG_CATEGORY_PAYMENT_EXPIRED = /*t(*/'Payment Expired'/*)*/; //t('action:Payment Expired')
    public const TAG_CATEGORY_FACEBOOK_AUDIENCE = /*t(*/'Facebook Audience'/*)*/; //t('action:Facebook Audience')
    public const TAG_CATEGORY_PLUGIN_INBOUND_READY =
        /*t(*/'Plugin Inbound Ready'/*)*/; //t('action:Plugin Inbound Ready')
    //t('action:Plugin Inbound Started')
    public const TAG_CATEGORY_PLUGIN_INBOUND_STARTED = /*t(*/'Plugin Inbound Started'/*)*/;
    //t('action:Plugin Inbound In Progress')
    public const TAG_CATEGORY_PLUGIN_INBOUND_INPROGRESS = /*t(*/'Plugin Inbound In Progress'/*)*/;
    //t('action:Plugin Inbound Finished')
    public const TAG_CATEGORY_PLUGIN_INBOUND_FINISHED = /*t(*/'Plugin Inbound Finished'/*)*/;
    //Note: for new Tags the comments help the translation parser
    //      'action:Manual Tag' is used in Tag::GetTagFilter()

    public static $DisplayCategoryTypes = array(
        Tag::CATEGORY_MANUAL => Tag::TAG_CATEGORY_MANUAL,
        Tag::CATEGORY_SMARTLINK => Tag::TAG_CATEGORY_SMARTLINK,
        Tag::CATEGORY_SENT => Tag::TAG_CATEGORY_SENT,
        Tag::CATEGORY_OPENED => Tag::TAG_CATEGORY_OPENED,
        Tag::CATEGORY_CLICKED => Tag::TAG_CATEGORY_CLICKED,
        Tag::CATEGORY_VIEWED => Tag::TAG_CATEGORY_VIEWED,
        Tag::CATEGORY_CONVERTED => Tag::TAG_CATEGORY_CONVERTED,
        Tag::CATEGORY_OUTBOUND => Tag::TAG_CATEGORY_OUTBOUND,
        Tag::CATEGORY_KAJABI_ACTIVATE => Tag::TAG_CATEGORY_KAJABI_ACTIVATE,
        Tag::CATEGORY_KAJABI_DEACTIVATE => Tag::TAG_CATEGORY_KAJABI_DEACTIVATE,
        Tag::CATEGORY_TAGGING_PIXEL => Tag::TAG_CATEGORY_TAGGING_PIXEL,
        Tag::CATEGORY_EMAIL_SENT => Tag::TAG_CATEGORY_EMAIL_SENT,
        Tag::CATEGORY_EMAIL_OPENED => Tag::TAG_CATEGORY_EMAIL_OPENED,
        Tag::CATEGORY_EMAIL_CLICKED => Tag::TAG_CATEGORY_EMAIL_CLICKED,
        Tag::CATEGORY_EMAIL_VIEWED => Tag::TAG_CATEGORY_EMAIL_VIEWED,
        Tag::CATEGORY_SMS_SENT => Tag::TAG_CATEGORY_SMS_SENT,
        Tag::CATEGORY_SMS_CLICKED => Tag::TAG_CATEGORY_SMS_CLICKED,
        Tag::CATEGORY_AUTOMATION_STARTED => Tag::TAG_CATEGORY_AUTOMATION_STARTED,
        Tag::CATEGORY_AUTOMATION_FINISHED => Tag::TAG_CATEGORY_AUTOMATION_FINISHED,
        Tag::CATEGORY_LISTBUILDING_APIKEY => Tag::TAG_CATEGORY_LISTBUILDING_APIKEY,
        Tag::CATEGORY_LISTBUILDING_BUSINESSCARD => Tag::TAG_CATEGORY_LISTBUILDING_BUSINESSCARD,
        Tag::CATEGORY_LISTBUILDING_EVENT => Tag::TAG_CATEGORY_LISTBUILDING_EVENT,
        Tag::CATEGORY_LISTBUILDING_REQUEST => Tag::TAG_CATEGORY_LISTBUILDING_REQUEST,
        Tag::CATEGORY_LISTBUILDING_SMS => Tag::TAG_CATEGORY_LISTBUILDING_SMS,
        Tag::CATEGORY_LISTBUILDING_FORMS => Tag::TAG_CATEGORY_LISTBUILDING_FORMS,
        Tag::CATEGORY_LISTBUILDING_PAYMENT => Tag::TAG_CATEGORY_LISTBUILDING_PAYMENT,
        Tag::CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT => Tag::TAG_CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT,
        Tag::CATEGORY_LISTBUILDING_DEFERRED_PAYMENT => Tag::TAG_CATEGORY_LISTBUILDING_DEFERRED_PAYMENT,
        Tag::CATEGORY_LISTBUILDING_REFUNDED => Tag::TAG_CATEGORY_LISTBUILDING_REFUNDED,
        Tag::CATEGORY_LISTBUILDING_CHARGEDBACK => Tag::TAG_CATEGORY_LISTBUILDING_CHARGEDBACK,
        Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED => Tag::TAG_CATEGORY_LISTBUILDING_REBILL_CANCELED,
        Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED_LAST_DAY => Tag::TAG_CATEGORY_LISTBUILDING_REBILL_CANCELED_LAST_DAY,
        Tag::CATEGORY_LISTBUILDING_REBILL_RESUMED => Tag::TAG_CATEGORY_LISTBUILDING_REBILL_RESUMED,
        Tag::CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION => Tag::TAG_CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION,
        Tag::CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED => Tag::TAG_CATEGORY_LANDING_PAGE_SUBSCRIBED,
        Tag::CATEGORY_PAYMENT_COMPLETED => Tag::TAG_CATEGORY_PAYMENT_COMPLETED,
        Tag::CATEGORY_PAYMENT_EXPIRED => Tag::TAG_CATEGORY_PAYMENT_EXPIRED,
        Tag::CATEGORY_FACEBOOK_AUDIENCE => Tag::TAG_CATEGORY_FACEBOOK_AUDIENCE,
        Tag::CATEGORY_PLUGIN_INBOUND_READY => Tag::TAG_CATEGORY_PLUGIN_INBOUND_READY,
        Tag::CATEGORY_PLUGIN_INBOUND_STARTED => Tag::TAG_CATEGORY_PLUGIN_INBOUND_STARTED,
        Tag::CATEGORY_PLUGIN_INBOUND_INPROGRESS => Tag::TAG_CATEGORY_PLUGIN_INBOUND_INPROGRESS,
        Tag::CATEGORY_PLUGIN_INBOUND_FINISHED => Tag::TAG_CATEGORY_PLUGIN_INBOUND_FINISHED,
    );

    public static $APIIndexFilterTypes = array(
        Tag::CATEGORY_MANUAL => 'tag',
        Tag::CATEGORY_SMARTLINK => 'smartlink',
    );

    /**
     * Get entity class
     */
    public static function GetEntityClass()
    {
        return static::$EntityClass;
    }

    /**
     * Get entity object for given (smart)tag.
     * @param $UserID
     * @param $TagID
     * @return bool|Tag
     */
    public static function GetEntityForTag($UserID, $TagID)
    {
        /** @var Tag $TagObject */
        $TagObject = Tag::FromID($UserID, $TagID);
        if ($TagObject) {
            switch ($TagObject->GetData('Category')) {
                case Tag::CATEGORY_MANUAL:
                case Tag::CATEGORY_SMARTLINK:
                    return $TagObject;
                    break;
                default:
                    $Classname = $TagObject->GetEntityClass();
                    return $Classname::FromID($UserID, $TagObject->GetData('EntityID'));
                    break;
            }
        }

        return false;
    }

    public function GetEntityName()
    {
        $terms = &drupal_static('klicktipp_tag_terms', array());

        $EntityID = $this->GetData('EntityID');

        if (!isset($terms[$EntityID])) {
            // entity is taxonomy
            $terms[$EntityID] = taxonomy_term_load($EntityID);
        }
        return empty($terms[$EntityID]) ? '' : $terms[$EntityID]->name;
    }

    public function GetEntityDescription()
    {
        $terms = &drupal_static('klicktipp_tag_terms', array());

        $EntityID = $this->GetData('EntityID');

        if (!isset($terms[$EntityID])) {
            // entity is taxonomy
            $terms[$EntityID] = taxonomy_term_load($EntityID);
        }
        return empty($terms[$EntityID]) ? '' : $terms[$EntityID]->description;
    }

    public function BuildTagName($EntityName)
    {
        return $EntityName;
    }

    /**
     * Decorates depending on tag type. Current implementation prefixes name by category
     *
     * @param string $name
     * @param ?int $category category (by default static::$TagCategory)
     *
     * @return string prefixed name
     */
    public static function decorateName(string $name): string
    {
        static $arrow;
        if (empty($arrow)) {
            $arrow = utf8_decode("&#x00BB;");
        }

        return t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[static::$TagCategory]) . " $arrow $name";
    }

    /**
     * Check if an entity is used by other entities in {tags}
     * @param $UserID
     * @param $EntityID : Id of entity to check
     * @param $EntityClass : class of entity to check
     * @param $Dependencies : array() collected dependencies from other base tables
     * @return array(
     *   'message' => array(
     *     EntityID => Edit link
     *     ...
     *  )
     * )
     */
    public static function CheckDependenciesForBaseTable(
        $UserID,
        $EntityID,
        $EntityClass,
        $Dependencies = array(),
        $Op = 'delete',
        $forAngular = false
    ) {
        $result = db_query(
            "SELECT TagID FROM {tag} WHERE RelOwnerUserID = :RelOwnerUserID AND Category = :manual",
            array(
                ':RelOwnerUserID' => $UserID,
                ':manual' => Tag::CATEGORY_MANUAL
            )
        );

        while ($TagID = $result->fetchField()) {
            /* @var Tag $Object */
            $Object = Tag::FromID($UserID, $TagID);

            // Race condition, tag is deleted already
            if (!$Object) {
                continue;
            }

            $deps = $Object->GetDependencies($EntityID, $EntityClass, $Op, $forAngular);

            if (!empty($deps)) {
                foreach ($deps as $message => $entities) {
                    foreach ($entities as $id => $link) {
                        $Dependencies[$message][$id] = $link;
                    }
                }
            }
        }

        return $Dependencies;
    }

    /**
     * Check if an entity is used in this object
     * @param $EntityID
     * @param $EntityClass
     * @param string $Op action to check dependencies for
     * @param bool $forAngular
     * @return array (
     *     EntityID => Edit link
     * )
     */
    public function GetDependencies($EntityID, $EntityClass, $Op = 'delete', bool $forAngular = false)
    {
        if (empty($EntityID) || empty($EntityClass)) {
            return array();
        }

        $dependencies = array();

        $TagID = $this->GetData('TagID');
        $Name = $this->GetEntityName();
        $TagData = $this->GetData('TagData');

        switch ($EntityClass) {
            case Tag::class:
                if (in_array($EntityID, $TagData['OptInSubscribeTo'] ?? [])) {
                    if ($forAngular) {
                        $dependencies[/*t(*/'dependency:tag:OptInSubscribeTo'/*)*/][$TagID] = [
                            'id' => $TagID,
                            'type' => static::getApiType(),
                            'name' => $Name,
                            'editLink' => $this->GetEditURL()
                        ];
                    } else {
                        $dependencies[/*t(*/'dependency:tag:OptInSubscribeTo'/*)*/][$TagID] = l(
                            $Name,
                            $this->GetEditURL(),
                            array('attributes' => array('target' => '_blank'))
                        );
                    }
                }
                if (in_array($EntityID, $TagData['OptInUnsubscribeFrom'] ?? [])) {
                    if ($forAngular) {
                        $dependencies[/*t(*/'dependency:tag:OptInUnsubscribeFrom'/*)*/][$TagID] = [
                            'id' => $TagID,
                            'type' => static::getApiType(),
                            'name' => $Name,
                            'editLink' => $this->GetEditURL()
                        ];
                    } else {
                        $dependencies[/*t(*/'dependency:tag:OptInUnsubscribeFrom'/*)*/][$TagID] = l(
                            $Name,
                            $this->GetEditURL(),
                            array('attributes' => array('target' => '_blank'))
                        );
                    }
                }

                break;
        }

        return $dependencies;
    }

    ///////////////////////////////////
    // old

    /**
     * Creates a new tag
     * @param $Parameters
     * @return DatabaseStatementInterface|false|int|null
     */
    public static function CreateTag($Parameters)
    {
        // Check required values - Start
        $ArrayFieldAndValues = array();
        foreach (Tag::$RequiredFieldsOnInsert as $EachField) {
            if (empty($Parameters[$EachField])) {
                return false;
            } else {
                $ArrayFieldAndValues[$EachField] = $Parameters[$EachField];
            }
        }

        foreach (Tag::$DefaultFields as $EachField => $Value) {
            if (!isset($Parameters[$EachField])) {
                $ArrayFieldAndValues[$EachField] = $Value;
            } else {
                if ($EachField == 'TagData') {
                    if (empty($Parameters[$EachField])) {
                        $ArrayFieldAndValues[$EachField] = $Value;
                    } else {
                        $ArrayFieldAndValues[$EachField] = serialize($Parameters[$EachField]);
                    }
                } else {
                    $ArrayFieldAndValues[$EachField] = $Parameters[$EachField];
                }
            }
        }
        // Check required values - End

        $ArrayFieldAndValues['CreatedOn'] = time();
        $NewTagID = kt_insert_row($ArrayFieldAndValues, '{tag}', 'TagID');

        // reset cache
        Tag::ResetTagsCache($ArrayFieldAndValues['RelOwnerUserID']);

        return $NewTagID;
    }

    /**
     * Creates a manual tag
     * @param $UserID
     * @param $Name
     * @param string $Description
     * @param string $TagData
     * @param array<mixed>|null $Labels
     * @param int $isMultiValue All tags are by default mutlivalue
     * @return DatabaseStatementInterface|false|int|null
     */
    public static function CreateManualTag(
        $UserID,
        $Name,
        $Description = '',
        $TagData = '',
        $Labels = null,
        $isMultiValue = 1
    ) {
        // this is the vocab to create the term in
        $vid = variable_get('klicktipp_manual_tag_vocab', 0);

        if (empty($UserID) || empty($Name)) {
            return false;
        }

        $TagName = substr(kt_strip_utf8mb4($Name), 0, 255);// Term Name (user input)

        $term = new stdClass();
        $term->name = $TagName;
        $term->vid = $vid; // Voacabulary ID
        $term->description = $Description; //Term Description (user input),

        if (taxonomy_term_save($term) != SAVED_NEW) {
            return false;
        }

        $Parameters = array(
            'Category' => Tag::CATEGORY_MANUAL,
            'RelOwnerUserID' => $UserID,
            'EntityID' => $term->tid,
            'TagData' => $TagData,
            'SmartLink' => '',
            'MultiValue' => (int)$isMultiValue,
        );

        $NewTagID = Tag::CreateTag($Parameters);

        if (!empty($NewTagID)) {
            //update named table and add labels if insert was successful

            DatabaseNamedTable::UpdateName(
                $UserID,
                DatabaseNamedTable::BASETYPE_TAG,
                Tag::CATEGORY_MANUAL,
                $NewTagID,
                $TagName
            );

            if (!empty($Labels)) {
                MetaLabels::AddLabelList(
                    $UserID,
                    $Labels,
                    DatabaseNamedTable::BASETYPE_TAG,
                    Tag::CATEGORY_MANUAL,
                    $NewTagID
                );
            }
        }

        return $NewTagID;
    }

    /**
     * Creates a manual tag by name (user input) if name is not an existing TagID
     * @param $UserID
     * @param $ArrayTagNamesOrIDs : array ( <existing tag id>, <another existing>, <new tag name>)
     * @param bool $CheckSmartTags : do not create manual tags from SmartTag names @see klicktipp_campaign
     * @param null $Labels
     * @param int $isMultiValue
     * @param string $TagData : pass optional TagData for new created tags here
     * @return array|bool $ArrayTagNamesOrIDs with array_keys preserved, TagName replaced by created TagID, existing TagIDs stay untouched
     *         breaks and returns FALSE if at least on Tag in $ArrayTagNamesOrIDs could not be created, all previously created Tags exist but will not be returned
     */
    public static function CreateManualTagByTagName(
        $UserID,
        $ArrayTagNamesOrIDs,
        $CheckSmartTags = false,
        $Labels = null,
        $isMultiValue = 1,
        $TagData = ''
    ) {
        if (empty($ArrayTagNamesOrIDs)) {
            //nothing to do but not an error
            return true;
        }

        if (!is_array($ArrayTagNamesOrIDs)) {
            return false;
        }

        //$CheckSmartTags == TRUE -> the user can also select SmartTags in a
        //  MagicSelect for campaigns -> do not create manual tags from SmartTag names
        $Categories = $CheckSmartTags ? [] : [
            Tag::CATEGORY_MANUAL,
            Tag::CATEGORY_SMARTLINK
        ];
        $ExistingTags = Tag::RetrieveTagsByCategory($UserID, $Categories);

        $AlreadyCreatedTagNames = [];
        $MultiValueTags = [];
        foreach ($ExistingTags as $tag) {
            $AlreadyCreatedTagNames[$tag['TagName']] = $tag['TagID'];
            if ($tag['MultiValue']) {
                $MultiValueTags[$tag['TagID']] = $tag['TagID'];
            }
        }

        $ReturnOptInSubscribeTo = array(); // returned Tags, with same keys as in $ArrayTagNamesOrIDs

        foreach ($ArrayTagNamesOrIDs as $key => $TagNameOrID) {
            if (empty($TagNameOrID)) {
                // probably no user input
                $ReturnOptInSubscribeTo[$key] = 0;
                continue;
            }

            if (empty($ExistingTags[$TagNameOrID])) {
                //$TagNameOrID not found in existing tags => TagName to create new tag

                if (empty($AlreadyCreatedTagNames[$TagNameOrID])) {
                    //a Tag for this TagName has not been created in this call yet
                    $CreatedTagID = Tag::CreateManualTag(
                        $UserID,
                        $TagNameOrID,
                        '',
                        $TagData,
                        $Labels,
                        $isMultiValue
                    ); //create tag

                    if (!$CreatedTagID) {
                        return false;
                    }

                    $ReturnOptInSubscribeTo[$key] = $CreatedTagID; //assign new tag to key

                    //store newly created TagID for this TagName
                    $AlreadyCreatedTagNames[$TagNameOrID] = $CreatedTagID;
                } else {
                    //tag with this name has already been created in this call, assign the ID

                    $alreadyCreatedTagID = $AlreadyCreatedTagNames[$TagNameOrID];

                    if (isset($MultiValueTags[$alreadyCreatedTagID]) != !empty($isMultiValue)) {
                        // the user wants to create a single value tag with a name of a multi value tag or
                        // a multi value tag with a single value tag name
                        return false;
                    }

                    $ReturnOptInSubscribeTo[$key] = $AlreadyCreatedTagNames[$TagNameOrID];
                }
            } else {
                //$TagNameOrID is an existing TagID, return untouched
                $ReturnOptInSubscribeTo[$key] = $TagNameOrID;
            }
        }

        return $ReturnOptInSubscribeTo;
    }

    /**
     * Creates a mart link
     **/
    public static function CreateSmartLink(
        $UserID,
        $Name,
        $Description,
        $SmartLink,
        $TagData = '',
        $Labels = null,
        $isMultiValue = 1
    ) {
        // this is the vocab to create the term in
        $vid = variable_get('klicktipp_manual_tag_vocab', 0);

        if (empty($UserID) || empty($Name)) {
            return false;
        }

        $TagName = substr(kt_strip_utf8mb4($Name), 0, 255);// Term Name (user input)

        $term = new stdClass();
        $term->name = $TagName;
        $term->vid = $vid; // Voacabulary ID
        $term->description = $Description; //Term Description (user input),

        if (taxonomy_term_save($term) != SAVED_NEW) {
            return false;
        }

        $Parameters = array(
            'Category' => Tag::CATEGORY_SMARTLINK,
            'RelOwnerUserID' => $UserID,
            'EntityID' => $term->tid,
            'TagData' => $TagData,
            'SmartLink' => $SmartLink,
            'MultiValue' => $isMultiValue,
        );

        $NewTagID = Tag::CreateTag($Parameters);

        if (!empty($NewTagID)) {
            //update named table and add labels if insert was successful

            DatabaseNamedTable::UpdateName(
                $UserID,
                DatabaseNamedTable::BASETYPE_TAG,
                Tag::CATEGORY_SMARTLINK,
                $NewTagID,
                $TagName
            );

            if (!empty($Labels)) {
                MetaLabels::AddLabelList(
                    $UserID,
                    $Labels,
                    DatabaseNamedTable::BASETYPE_TAG,
                    Tag::CATEGORY_SMARTLINK,
                    $NewTagID
                );
            }
        }

        return $NewTagID;
    }

    /**
     * Get tag name of entity
     * @param $ArrayTag
     * @return string
     */
    public static function GetTagName($ArrayTag)
    {
        $ArrayTags = Tag::GetTagsCache($ArrayTag['RelOwnerUserID']);
        return empty($ArrayTags[$ArrayTag['TagID']]) ? '' : $ArrayTags[$ArrayTag['TagID']]['TagName'];
    }

    /**
     * Get tag description of entity
     **/
    public static function GetTagDescription($ArrayTag)
    {
        $ArrayTags = Tag::GetTagsCache($ArrayTag['RelOwnerUserID']);
        return empty($ArrayTags[$ArrayTag['TagID']]) ? '' : $ArrayTags[$ArrayTag['TagID']]['TagDescription'];
    }

    /**
     * Retrieve tag.
     * Note: Labels are not retrieved!
     **/
    public static function RetrieveTag($UserID, $TagID, $AllData = false)
    {
        if ($AllData) {
            $ArrayTags = Tag::GetTagsCache($UserID);
            if (!empty($ArrayTags[$TagID])) {
                return $ArrayTags[$TagID];
            }
        } else {
            $result = db_query("SELECT * FROM {tag} WHERE RelOwnerUserID = :RelOwnerUserID AND TagID = :TagID", array(
                ':RelOwnerUserID' => $UserID,
                ':TagID' => $TagID
            ));
            if ($tag = kt_fetch_array($result)) {
                return $tag;
            }
        }

        return false;
    }

    /**
     * Retrieve tag.
     * Note: Labels are not retrieved!
     **/
    public static function RetrieveTagOfEntity($UserID, $Category, $EntityID)
    {
        if (
            $tag = kt_fetch_array(
                db_query(
                    "SELECT * FROM {tag} WHERE RelOwnerUserID = :RelOwnerUserID AND Category = :Category AND EntityID = :EntityID LIMIT 0,1",
                    array(
                        ':RelOwnerUserID' => $UserID,
                        ':Category' => $Category,
                        ':EntityID' => $EntityID
                    )
                )
            )
        ) {
            return $tag;
        } else {
            return false;
        }
    }

    public static function RetrieveTagIdByName($UserID, $TagName)
    {
        if (empty($UserID) || empty($TagName)) {
            return false;
        }

        $result = kt_query(
            "SELECT tag.TagID FROM {taxonomy_term_data} term
                            INNER JOIN {tag} tag ON term.tid = tag.EntityID AND tag.RelOwnerUserID = %d AND tag.Category IN (%d, %d)
                            WHERE term.name = '%s'",
            $UserID,
            Tag::CATEGORY_MANUAL,
            Tag::CATEGORY_SMARTLINK,
            $TagName
        );

        $Tag = kt_fetch_array($result);

        return (empty($Tag)) ? false : $Tag['TagID'];
    }

    /**
     * Retrieve tag data by name, cached since multiple calls
     * @param $userID
     * @param $tagName
     *
     * @return array
     */
    public static function RetrieveTagByNameCached($userID, $tagName): array
    {
        if (empty($userID) || empty($tagName)) {
            return [];
        }

        $allTags = Tag::GetTagsCache($userID);

        $exists = array_filter($allTags, function ($tag) use ($tagName) {
            return $tag['TagName'] == $tagName;
        });

        return empty($exists) ? [] : array_shift($exists);
    }

    /**
     * Retrieve manual tags
     * @param $UserID
     * @param bool $includeSmartLinks
     * @return array
     */
    public static function RetrieveManualTags($UserID, bool $includeSmartLinks = true)
    {
        $AllTags = self::GetTagsCache($UserID);

        $categories = $includeSmartLinks ? [self::CATEGORY_MANUAL, self::CATEGORY_SMARTLINK] : [self::CATEGORY_MANUAL];

        $ArrayTags = array();
        foreach ($AllTags as $tagid => $tag) {
            if (in_array((int)$tag['Category'], $categories, true)) {
                $ArrayTags[$tagid] = $tag;
            }
        }

        return $ArrayTags;
    }

    /**
     * Retrieve tags as an array used in form select elements
     */
    public static function RetrieveTagsAsOptionsArray(
        $userID,
        $categories = array(),
        $excludeEntity = 0,
        $excludeTags = array()
    ) {
        $arrayTags = Tag::GetTagsCache($userID);

        $options = array('' => '');
        foreach ($arrayTags as $tagID => $arrayTag) {
            if (!empty($categories) && !in_array($arrayTag['Category'], $categories)) {
                continue;
            }

            //with $excludeEntity we just want to exclude Campaign-Tags (campaign form)
            if (
                $arrayTag['EntityID'] == $excludeEntity &&
                in_array($arrayTag['Category'], CampaignsNewsletter::RELATED_TAG_CATEGORIES)
            ) {
                continue;
            }

            if (in_array($tagID, $excludeTags)) {
                continue;
            }

            if ($arrayTag['Category'] == Tag::CATEGORY_SMARTLINK) {
                $arrayTag['TagName'] = SmartLink::decorateName($arrayTag['TagName']);
            }

            $options[$tagID] = $arrayTag['TagName'];
        }

        return $options;
    }

    /**
     * Retrieve tags by categories
     *
     * @deprecated
     * TODO remove after smart link migration (one call left in Statistics::RegisterLinkClickTrack)
     */
    public static function RetrieveTagsByCategory($UserID, $Categories = array())
    {
        $AllTags = Tag::GetTagsCache($UserID);

        if (empty($Categories)) {
            return $AllTags;
        }

        // filter categories
        $ArrayTags = array();
        foreach ($AllTags as $tag) {
            if (in_array($tag['Category'], $Categories)) {
                $ArrayTags[$tag['TagID']] = $tag;
            }
        }

        return $ArrayTags;
    }

    /**
     * Returns total number of subscribers of a tag
     * @param $UserID
     * @param $TagID
     * @return int
     */
    public static function RetrieveCount($UserID, $TagID)
    {
        if (empty($UserID) || empty($TagID)) {
            return 0;
        }

        return (int)db_query(
            "SELECT COUNT(DISTINCT(RelSubscriberID)) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID AND RelTagID = :RelTagID",
            array(
                ':RelTagID' => $TagID,
                ':RelOwnerUserID' => $UserID,
            )
        )->fetchField();
    }

    /**
     * Get filter for tag magic select
     */
    public static function GetTagFilter($UserID, $excludeEntity = 0)
    {
        $TagFilter = array();

        $TagFilter[t('Filter: Manual Tags')] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));

        $CATEGORY_AUTOMATION_STARTED = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_AUTOMATION_STARTED));
        if (count($CATEGORY_AUTOMATION_STARTED) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_AUTOMATION_STARTED]))
            )] = $CATEGORY_AUTOMATION_STARTED;
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_AUTOMATION_FINISHED]))
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_AUTOMATION_FINISHED));
        }

        $CATEGORY_EMAIL_SENT = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_EMAIL_SENT));
        if (count($CATEGORY_EMAIL_SENT) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_EMAIL_SENT]))
            )] = $CATEGORY_EMAIL_SENT;
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_EMAIL_OPENED]))
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_EMAIL_OPENED));
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_EMAIL_CLICKED]))
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_EMAIL_CLICKED));
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_EMAIL_VIEWED]))
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_EMAIL_VIEWED));
        }

        $TagFilter[t(
            'Filter: SmartTag >> @tag',
            array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_SENT]))
        )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_SENT), $excludeEntity);
        $TagFilter[t(
            'Filter: SmartTag >> @tag',
            array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_OPENED]))
        )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_OPENED), $excludeEntity);
        $TagFilter[t(
            'Filter: SmartTag >> @tag',
            array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_CLICKED]))
        )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_CLICKED), $excludeEntity);
        $TagFilter[t(
            'Filter: SmartTag >> @tag',
            array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_VIEWED]))
        )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_VIEWED), $excludeEntity);

        $CATEGORY_SMS_SENT = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_SMS_SENT));
        if (count($CATEGORY_SMS_SENT) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_SMS_SENT]))
            )] = $CATEGORY_SMS_SENT;
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_SMS_CLICKED]))
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_SMS_CLICKED));
        }

        $CATEGORY_SMARTLINK = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_SMARTLINK));
        if (count($CATEGORY_SMARTLINK) > 0) {
            $TagFilter[t('Filter: SmartLinks')] = $CATEGORY_SMARTLINK;
        }

        $CATEGORY_TAGGING_PIXEL = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_TAGGING_PIXEL));
        if (count($CATEGORY_TAGGING_PIXEL) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_TAGGING_PIXEL]))
            )] = $CATEGORY_TAGGING_PIXEL;
        };

        $TagFilter[t(
            'Filter: SmartTag >> @tag',
            array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_CONVERTED]))
        )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_CONVERTED), $excludeEntity);

        $CATEGORY_OUTBOUND = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_OUTBOUND));
        if (count($CATEGORY_OUTBOUND) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_OUTBOUND]))
            )] = $CATEGORY_OUTBOUND;
        };
        $CATEGORY_KAJABI_ACTIVATE = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_KAJABI_ACTIVATE));
        if (count($CATEGORY_KAJABI_ACTIVATE) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_KAJABI_ACTIVATE]))
            )] = $CATEGORY_KAJABI_ACTIVATE;
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_KAJABI_DEACTIVATE]))
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_KAJABI_DEACTIVATE));
        };

        // plugins: one filter for each plugin (npt category as the user may not know the category)
        $CATEGORY_PLUGIN_INBOUND = Tag::RetrieveTagsAsOptionsArray(
            $UserID,
            array(
                Tag::CATEGORY_PLUGIN_INBOUND_READY,
                Tag::CATEGORY_PLUGIN_INBOUND_STARTED,
                Tag::CATEGORY_PLUGIN_INBOUND_INPROGRESS,
                Tag::CATEGORY_PLUGIN_INBOUND_FINISHED,
            )
        );
        foreach ($CATEGORY_PLUGIN_INBOUND as $tagId => $tagName) {
            if (!empty($tagId) && !empty($tagName)) {
                $pluginName = TagCategoryPlugin::getPluginNameOfTag($UserID, $tagId)        ;
                if (!empty($pluginName)) {
                    $TagFilter[t('Filter: SmartTag >> @tag', array('@tag' => $pluginName))][$tagId] = $tagName;
                }
            };
        }

        $CATEGORY_LISTBUILDING_FORMS = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_LISTBUILDING_FORMS));
        if (count($CATEGORY_LISTBUILDING_FORMS) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_FORMS]))
            )] = $CATEGORY_LISTBUILDING_FORMS;
        };

        $CATEGORY_LISTBUILDING_SMS = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_LISTBUILDING_SMS));
        if (count($CATEGORY_LISTBUILDING_SMS) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_SMS]))
            )] = $CATEGORY_LISTBUILDING_SMS;
        };

        $CATEGORY_LISTBUILDING_APIKEY = Tag::RetrieveTagsAsOptionsArray(
            $UserID,
            array(Tag::CATEGORY_LISTBUILDING_APIKEY)
        );
        if (count($CATEGORY_LISTBUILDING_APIKEY) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_APIKEY]))
            )] = $CATEGORY_LISTBUILDING_APIKEY;
        };

        $CATEGORY_LISTBUILDING_BUSINESSCARD = Tag::RetrieveTagsAsOptionsArray(
            $UserID,
            array(Tag::CATEGORY_LISTBUILDING_BUSINESSCARD)
        );
        if (count($CATEGORY_LISTBUILDING_BUSINESSCARD) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/
                        'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_BUSINESSCARD]
                    )
                )
            )] = $CATEGORY_LISTBUILDING_BUSINESSCARD;
        };

        $CATEGORY_LISTBUILDING_EVENT = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_LISTBUILDING_EVENT));
        if (count($CATEGORY_LISTBUILDING_EVENT) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_EVENT]))
            )] = $CATEGORY_LISTBUILDING_EVENT;
        };

        $CATEGORY_LISTBUILDING_REQUEST = Tag::RetrieveTagsAsOptionsArray(
            $UserID,
            array(Tag::CATEGORY_LISTBUILDING_REQUEST)
        );
        if (count($CATEGORY_LISTBUILDING_REQUEST) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_REQUEST])
                )
            )] = $CATEGORY_LISTBUILDING_REQUEST;
        };

        $CATEGORY_LISTBUILDING_PAYMENT = Tag::RetrieveTagsAsOptionsArray(
            $UserID,
            array(Tag::CATEGORY_LISTBUILDING_PAYMENT)
        );
        if (count($CATEGORY_LISTBUILDING_PAYMENT) > 1) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_PAYMENT])
                )
            )] = $CATEGORY_LISTBUILDING_PAYMENT;
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/
                        'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT]
                    )
                )
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, [Tag::CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT]);
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/
                        'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_DEFERRED_PAYMENT]
                    )
                )
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, [Tag::CATEGORY_LISTBUILDING_DEFERRED_PAYMENT]);
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_REFUNDED])
                )
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_LISTBUILDING_REFUNDED));
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/
                        'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_CHARGEDBACK]
                    )
                )
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_LISTBUILDING_CHARGEDBACK));
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/
                        'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED]
                    )
                )
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED));
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/
                        'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED_LAST_DAY]
                    )
                )
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED_LAST_DAY));
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/
                        'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_REBILL_RESUMED]
                    )
                )
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_LISTBUILDING_REBILL_RESUMED));
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_PAYMENT_COMPLETED]))
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_PAYMENT_COMPLETED));
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array('@tag' => t(/*ignore*/ 'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_PAYMENT_EXPIRED]))
            )] = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_PAYMENT_EXPIRED));
        };

        $CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION = Tag::RetrieveTagsAsOptionsArray(
            $UserID,
            array(Tag::CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION)
        );
        if (count($CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/
                        'action:' . Tag::$DisplayCategoryTypes[Tag::CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION]
                    )
                )
            )] = $CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION;
        }
        $CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED = self::RetrieveTagsAsOptionsArray(
            $UserID,
            array(self::CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED)
        );
        if (count($CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED) > 0) {
            $TagFilter[t(
                'Filter: SmartTag >> @tag',
                array(
                    '@tag' => t(/*ignore*/
                        'action:' . self::$DisplayCategoryTypes[self::CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED]
                    )
                )
            )] = $CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED;
        }

        return $TagFilter;
    }

    /**
     * Retrieve (and create) the tags cache
     */
    public static function GetTagsCache($UserID)
    {
        if (empty($UserID)) {
            return array();
        }

        $AllTags = &drupal_static(__FUNCTION__ . $UserID);

        // try to read from cache table
        if (empty($AllTags)) {
            $AllTags = UserCache::get($UserID, UserCache::CACHE_ALLTAGS) ?: [];
        }

        // create cache
        if (empty($AllTags)) {
            $result = kt_query("SELECT * FROM {tag} WHERE RelOwnerUserID = %d ORDER BY TagID DESC", $UserID);
            while ($tag = kt_fetch_array($result)) {
                $TagObject = Tag::FromArray($tag);

                $tag['EntityName'] = $TagObject->GetEntityName();
                // add "named" entities only (otherwise its an inconsistency)
                if (!empty($tag['EntityName'])) {
                    $tag['TagName'] = $TagObject->BuildTagName($tag['EntityName']);
                    $tag['TagDescription'] = $TagObject->GetEntityDescription();
                    $tag['TagData'] = empty($tag['TagData']) ? array() : unserialize($tag['TagData']);

                    /** @var array<int|string> $AllTags */
                    $AllTags[$tag['TagID']] = $tag;
                }
            }

            UserCache::set($UserID, UserCache::CACHE_ALLTAGS, $AllTags);
        }

        return $AllTags;
    }

    /**
     * Reset the tags cache
     */
    public static function ResetTagsCache($UserID)
    {
        drupal_static_reset('klicktipp_tag_terms');
        drupal_static_reset('klicktipp_tag_entitynames_' . Campaigns::class);
        drupal_static_reset('klicktipp_tag_entitynames_' . MarketingTools::class);
        drupal_static_reset('klicktipp_tag_entitynames_' . Emails::class);
        drupal_static_reset('klicktipp_tag_entitynames_' . Listbuildings::class);
        drupal_static_reset("GetTagsCache$UserID");
        UserCache::remove($UserID, UserCache::CACHE_ALLTAGS);
    }

    /**
     * Update tag
     *
     * changes the text fields of the tag in the referenced entity
     **/
    public static function UpdateTag($ArrayTag)
    {
        if (empty($ArrayTag['TagID']) || empty($ArrayTag['RelOwnerUserID'])) {
            return false;
        }

        $Tag = Tag::RetrieveTag($ArrayTag['RelOwnerUserID'], $ArrayTag['TagID']);

        if (
            !$Tag || !in_array($Tag['Category'], array(
                Tag::CATEGORY_MANUAL,
                Tag::CATEGORY_SMARTLINK
            ))
        ) {
            return false;
        }

        if (empty($ArrayTag['TagData'])) {
            $ArrayTag['TagData'] = '';
        } else {
            $ArrayTag['TagData'] = serialize($ArrayTag['TagData']);
        }


        // this is the vocab the term was created in
        $vid = variable_get('klicktipp_manual_tag_vocab', 0);

        $ArrayTag['TagName'] = kt_strip_utf8mb4($ArrayTag['TagName']);
        if (!empty($ArrayTag['TagName']) && !empty($Tag['EntityID'])) {
            $term = new stdClass();

            //given an ID AND a name, the term will be update (no tid and the term will be deleted!)
            $term->tid = $Tag['EntityID'];

            $term->name = substr($ArrayTag['TagName'], 0, 255);// Term Name (user input)
            $term->vid = $vid; // Voacabulary ID
            $term->description = $ArrayTag['TagDescription']; //Term Description (user input),

            if (taxonomy_term_save($term) != SAVED_UPDATED) {
                return false;
            }

            //parent::UpdateDB is not called, update name in named table here
            DatabaseNamedTable::UpdateName(
                $ArrayTag['RelOwnerUserID'],
                DatabaseNamedTable::BASETYPE_TAG,
                $Tag['Category'],
                $ArrayTag['TagID'],
                $ArrayTag['TagName']
            );
        }

        $SmartLinkURL = ($Tag['Category'] == Tag::CATEGORY_SMARTLINK) ? $ArrayTag['SmartLink'] : '';

        $metaLabels = static::ExtractLabels($ArrayTag);

        db_update('tag')
            ->fields(array(
                'TagData' => $ArrayTag['TagData'],
                'SmartLink' => $SmartLinkURL,
                'MultiValue' => isset($ArrayTag['MultiValue']) ? $ArrayTag['MultiValue'] : 0,
            ))
            ->condition('TagID', $ArrayTag['TagID'])
            ->execute();

        // no parent::UpdateDB() call, so do it here
        if (isset($metaLabels)) {
            MetaLabels::SetLabelsOfEntity(
                $ArrayTag['RelOwnerUserID'],
                DatabaseNamedTable::BASETYPE_TAG,
                $Tag['Category'],
                $ArrayTag['TagID'],
                $metaLabels
            );
        }


        // reset cache
        Tag::ResetTagsCache($ArrayTag['RelOwnerUserID']);

        return true;
    }

    /**
     * Delete tag
     **/
    public static function DeleteTag($TagID, $UserID)
    {
        if (!$UserID || !$TagID) {
            return false;
        }

        $Tag = Tag::RetrieveTag($UserID, $TagID);
        if (empty($Tag)) {
            return false;
        }

        // remove taggings and transactional emails
        Subscribers::UntagSubscribersOfTag($UserID, $TagID);

        // remove from user settings
        if (VarAgedSubscribers::GetVariable($UserID, 0) == $TagID) {
            VarAgedSubscribers::SetTag($UserID, 0);
        }

        if (!db_delete('tag')->condition('TagID', $TagID)->execute()) {
            return false;
        }

        MetaLabels::RemoveAllLabelsFromEntity($UserID, static::$EntityBaseType, $TagID);

        if (
            $Tag && in_array($Tag['Category'], array(
                Tag::CATEGORY_MANUAL,
                Tag::CATEGORY_SMARTLINK
            ))
        ) {
            //remove tag id from Users OptInSubscribeTo, OptInUnsubscribeFrom
            $result = kt_query(
                "SELECT TagID, TagData FROM {tag} WHERE RelOwnerUserID = %d AND Category IN (%d, %d) AND TagData != ''",
                $UserID,
                Tag::CATEGORY_MANUAL,
                Tag::CATEGORY_SMARTLINK
            );

            while ($tag = kt_fetch_array($result)) {
                $TagData = unserialize((string) $tag['TagData']);
                // both fields can be null/not set
                $TagData['OptInSubscribeTo'] ??= [];
                $TagData['OptInUnsubscribeFrom'] ??= [];

                // bot fields can contain scalar values
                $TagData['OptInSubscribeTo'] = (array)$TagData['OptInSubscribeTo'];
                $TagData['OptInUnsubscribeFrom'] = (array)$TagData['OptInUnsubscribeFrom'];

                $update = false;
                $key = array_search($TagID, $TagData['OptInSubscribeTo']);
                if ($key !== false) {
                    unset($TagData['OptInSubscribeTo'][$key]);
                    $update = true;
                }

                $key = array_search($TagID, $TagData['OptInUnsubscribeFrom']);
                if ($key !== false) {
                    unset($TagData['OptInUnsubscribeFrom'][$key]);
                    $update = true;
                }

                if ($update) {
                    if (empty($TagData['OptInSubscribeTo'])) {
                        unset($TagData['OptInSubscribeTo']);
                    }

                    if (empty($TagData['OptInUnsubscribeFrom'])) {
                        unset($TagData['OptInUnsubscribeFrom']);
                    }

                    if (empty($TagData)) {
                        $TagData = '';
                    } else {
                        $TagData = serialize($TagData);
                    }


                    //update TagData
                    db_update('tag')
                        ->fields(array(
                            'TagData' => $TagData,
                        ))
                        ->condition('TagID', $tag['TagID'])
                        ->execute();
                }
            }

            // --- listbuildings and marketing_tools
            // remove the AssignTagID from column AssignTagID in {listbuilding} and {marketing_tools}
            Listbuildings::ReleaseTag($UserID, $TagID);
            MarketingTools::ReleaseTag($UserID, $TagID);
            // remove assignments linked to this tag
            VarBAMAssignment::DeleteAssignmentsOfTag($UserID, $TagID);

            // --- subscription forms
            //remove tag from OptInSubscribeTo of subscription forms
            // TODO REMOVEOLDFORMS

            $result = db_query("SELECT ListFormID, Data FROM {listforms} WHERE RelOwnerUserID = :RelOwnerUserID", array(
                ':RelOwnerUserID' => $UserID,
            ));

            while ($form = kt_fetch_array($result)) {
                if (!empty($form['Data'])) {
                    $Data = unserialize((string) $form['Data']);

                    if ($Data['OptInSubscribeTo'] == $TagID) {
                        unset($Data['OptInSubscribeTo']);

                        db_query(
                            "UPDATE {listforms} SET Data = :Data WHERE RelOwnerUserID = :RelOwnerUserID AND ListFormID = :ListFormID",
                            array(
                                ':Data' => (empty($Data)) ? '' : serialize($Data),
                                ':RelOwnerUserID' => $UserID,
                                ':ListFormID' => $form['ListFormID'],
                            )
                        );
                    }
                }
            }

            // --- delete from statistic tools

            $result = db_query(
                "SELECT * FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID AND ToolType = :ToolType",
                array(
                    ':RelOwnerUserID' => $UserID,
                    ':ToolType' => MarketingTools::TOOL_TYPE_STATISTICS,
                )
            );

            while ($DBArray = kt_fetch_array($result)) {
                /** @var ToolStatistics $ObjectStatistic */
                $ObjectStatistic = ToolStatistics::FromArray($DBArray);

                $ArrayStatistic = $ObjectStatistic->GetData();

                $index = array_search($TagID, $ArrayStatistic['Tags']);

                if ($index !== false) {
                    unset($ArrayStatistic['Tags'][$index]);
                    ToolStatistics::UpdateDB($ArrayStatistic);
                }
            }

            if (VarEmailPreviewSubscriber::GetVariable($UserID, null) == $TagID) {
                VarEmailPreviewSubscriber::DeleteVariable($UserID);
            }

            // --- delete taxonomy term

            if (taxonomy_term_delete($Tag['EntityID']) != SAVED_DELETED) {
                return false;
            }

            // --- delete name in named table
            DatabaseNamedTable::DeleteName($UserID, DatabaseNamedTable::BASETYPE_TAG, $TagID);
        }

        // reset cache
        Tag::ResetTagsCache($UserID);

        return true;
    }

    /**
     * Delete tags of user
     **/
    public static function DeleteTagsOfUser($UserID)
    {
        if (!$UserID) {
            return false;
        }

        $result = kt_query(
            "SELECT EntityID FROM {tag} WHERE RelOwnerUserID = %d AND Category IN (%d, %d)",
            $UserID,
            Tag::CATEGORY_MANUAL,
            Tag::CATEGORY_SMARTLINK
        );
        while ($tag = kt_fetch_array($result)) {
            //delete the taxonomy of every user tag
            taxonomy_term_delete($tag['EntityID']);
        }

        //delete user tags
        db_delete('tag')
            ->condition('RelOwnerUserID', $UserID)
            ->execute();
        db_delete('tagging')
            ->condition('RelOwnerUserID', $UserID)
            ->execute();

        // reset cache
        Tag::ResetTagsCache($UserID);

        return true;
    }

    public static function CheckDuplicateName($UserID, $Name)
    {
        $sql = "SELECT tag.TagID, term.name FROM {taxonomy_term_data} term " .
            "INNER JOIN {tag} tag ON term.tid = tag.EntityID AND tag.RelOwnerUserID = :userId AND tag.Category IN (:categories) " .
            "WHERE term.name = :name";


        $result = db_query($sql, [
            ':userId' => $UserID,
            ':categories' => [self::CATEGORY_MANUAL, self::CATEGORY_SMARTLINK],
            ':name' => $Name
        ]);

        // the database cannot compare tags case-sensitively, so do it here
        // Note: the could be multiple tags found (tag, Tag, TAG, etc...)
        while ($DBArray = kt_fetch_array($result)) {
            if ($Name === $DBArray['name']) {
                return $DBArray['TagID'];
            }
        }

        return 0;
    }

    /**
     * @deprecated use TagLinkResolver
     */
    public function GetEditURL($CampaignID = 0)
    {
        return (new TagLinkResolver())->linkEdit($this->DataFlat['RelOwnerUserID'], $this->DataFlat['TagID']);
    }

    /**
     * @deprecated use TagLinkResolver
     */
    public static function GetAddURL($UserID, $data = '')
    {
        return (new TagLinkResolver())->linkCreate($UserID);
    }


    public static function GetStatistics($UserID, $TagID, $From = 0, $To = 0)
    {
        if (empty($To)) {
            $To = time();
        }

        if ($To < $From) {
            return array();
        }

        $stats = array(
            'total' => 0,
            'year' => array(),
        );

        $result = db_query(
            "SELECT SubscriptionDate FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID AND RelTagID = :TagID AND (SubscriptionDate BETWEEN :From AND :To)",
            array(
                ':RelOwnerUserID' => $UserID,
                ':TagID' => $TagID,
                ':From' => $From,
                ':To' => $To
            )
        );

        while ($s = kt_fetch_array($result)) {
            $y = date("Y", (int) $s['SubscriptionDate']);
            $m = date("n", (int) $s['SubscriptionDate']);
            $d = date("j", (int) $s['SubscriptionDate']);
            $h = date("G", (int) $s['SubscriptionDate']);

            $stats['total']++;
            $stats['year'][$y]['total']++;
            $stats['year'][$y]['month'][$m]['total']++;
            $stats['year'][$y]['month'][$m]['day'][$d]['total']++;
            $stats['year'][$y]['month'][$m]['day'][$d]['hour'][$h]++;
        }

        return $stats;
    }

    public function GetSmartTags($forConditions = false)
    {
        if ($forConditions) {
            //do not call on base class
            return array(
                'received' => $this->GetData('TagID')
            );
        }

        return array(
            'SmartTagID' => $this->GetData('TagID'),
        );
    }

    /**
     * Get all data needed for the email condition dialog for
     * Manual Tag conditions
     * @param $UserID
     *
     * @return array
     */
    public static function GetConditionData($UserID)
    {
        //<#coptag>Email Editor Condition option for manual tags</#>
        //<#cactiontag>Email Editor Condition verb for manual tags</#>

        return array(
            'formData' => array(
                'condition' => array(
                    array(
                        'id' => 'has',
                        'label' => t(/*#coptag*/ 'condition::Contact has manual tag'),
                        'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_TAGGING,
                        'hasEntity' => true,
                    ),
                    array(
                        'id' => 'has-not',
                        'label' => t(/*#coptag*/ 'condition::Contact has not manual tag'),
                        'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_NOT_TAGGING,
                        'hasEntity' => true,
                    ),
                    array(
                        'id' => 'has-any',
                        'label' => t(/*#coptag*/ 'condition::Contact has any manual tag'),
                        'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_TAGGING,
                        'hasEntity' => false,
                    ),
                    array(
                        'id' => 'has-not-any',
                        'label' => t(/*#coptag*/ 'condition::Contact has not any manual tag'),
                        'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_NOT_TAGGING,
                        'hasEntity' => false,
                    ),
                ),
                'entity' => static::GetConditionEntities($UserID),
                'action' => array(
                    array(
                        'id' => 'received',
                        'label' => t(/*#cactiontag*/ 'condition::received')
                    )
                ),
                'timeframe' => Personalization::GetEditorConditionTimeframes()
            ),
            'default' => array(
                //form field data
                'type' => 'tagging',
                'condition' => 'has',
                'entity' => 0,
                'entityType' => static::class,
                'action' => 'received',
                'timeframe' => 'anytime',
                //internal data for CampaignsProcessFlow::CheckCondition()
                'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_TAGGING,
                'field' => 0,
                'value' => 0
            )
        );
    }

    /**
     * Get all Manual Tags to be used in email conditions
     * @param $UserID
     *
     * @return array
     */
    public static function GetConditionEntities($UserID)
    {
        $result = static::GetEntitiesAsOptionsArray($UserID);

        $entities = array();
        foreach ($result as $id => $name) {
            $entities[] = array(
                'id' => $id,
                'name' => $name,
                'type' => static::class,
                //Note: Manual Tags are their own SmartTags
                'actions' => array(
                    'received' => $id
                )
            );
        }

        return $entities;
    }

    public static function GetConditionSmartTagCategory($action)
    {
        $actions = array(
            'received' => Tag::CATEGORY_MANUAL
        );

        return (empty($actions[$action])) ? false : $actions[$action];
    }

    /**
     * Whether subscriber has at least 1 tagging with given tag categories
     *
     * @param int $userID
     * @param int $subscriberID
     * @param array $categories
     *
     * @return bool
     */
    public static function subscriberHasCategoryTaggings($userID, $subscriberID, array $categories = [])
    {
        $tagIds = static::RetrieveTagsByCategory($userID, $categories);
        if (empty($tagIds)) {
            return false;
        }
        return (bool)db_query(
            "SELECT 1 FROM {tagging} " .
            "WHERE RelTagID IN (:tagIDs) AND RelOwnerUserID = :userID AND RelSubscriberID = :subscriberID LIMIT 1",
            [
                ':userID' => $userID,
                ':subscriberID' => $subscriberID,
                ':tagIDs' => array_keys($tagIds)
            ]
        )->fetchField();
    }

    public static function createNewInstance(int $userId): self
    {
        $dbArray = array_merge(self::$DefaultFields, [
            'TagID' => 0,
            'RelOwnerUserID' => $userId,
            'TagName' => '',
            'Category' => self::CATEGORY_MANUAL
        ]);

        return new self($dbArray);
    }

    /**
     * @return TaggingDbValueObject[]
     * @throws Exception
     */
    public function getTaggingsForSwitchToSingleValue(): array
    {
        // if we already have multiple references tagged with this tag, use youngest Tag for single value
        $result = db_query("SELECT RelOwnerUserID, RelSubscriberID, RelTagID, MAX(SubscriptionDate) as SubscriptionDate FROM {tagging} ".
            " WHERE RelOwnerUserID = :RelOwnerUserID AND RelTagID = :RelTagID GROUP BY RelOwnerUserID, RelSubscriberID, RelTagID", [
            ':RelOwnerUserID' => $this->GetData('RelOwnerUserID'),
            ':RelTagID' => $this->GetData('TagID'),
        ]);

        $taggings = [];
        if ($result = $result->fetchAll(PDO::FETCH_ASSOC)) {
            foreach ($result as $tagging) {
                $taggings[] = TaggingDbValueObject::createFromDbResult($tagging);
            }
        }

        return $taggings;
    }
}

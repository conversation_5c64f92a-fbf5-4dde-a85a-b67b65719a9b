<?php

namespace App\Klicktipp;

/**
 * UserVariables class
 *
 * This class holds searchable user data
 **/
class UserVariables extends DatabaseTableWithData
{
    // VarType
    const VARTYPE_NA = '';
    const VARTYPE_AFFILIATE_NAME = 'affiliate-name';
    const VARTYPE_AFFILIATE_PIXEL = 'affiliate-pixel';
    const VARTYPE_AGEDSUBSCRIBERS = 'aged-subscribers';
    const VARTYPE_AMEMBERID = 'amemberid';
    const VARTYPE_AMEMBER_PRODUCT_ID = 'amember-product-id';
    const VARTYPE_BAM_ASSIGNMENT = 'bam-assignment';
    const VARTYPE_CLEVERBRIDGE = 'cleverbridge';
    const VARTYPE_CONSULTANT = 'consultant';
    const VARTYPE_CONVENTION = 'convention';
    const VARTYPE_DATAPROCESSINGORDER = 'dpo';
    const VARTYPE_EMAIL_BLACKLIST = 'email-blacklist';
    const VARTYPE_FACEBOOK_AUDIENCE = 'facebook-audience';
    const VARTYPE_FULLCONTACT = 'fullcontact';
    const VARTYPE_NOTIFICATION_MAIL = 'notification-mail';
    const VARTYPE_PRIVACY = 'privacy';
    const VARTYPE_PROCESSFLOW = 'processflow';
    const VARTYPE_SALES_EMPLOYEE = 'sales-employee';
    const VARTYPE_SUBSCRIBER_AUDIENCE = 'subscriber-audience';
    const VARTYPE_ADDITIONAL_SENDER_ADDRESSES = 'additional-sender-addresses';
    const VARTYPE_DMARC_SENDER_ALIAS = 'dmarc-sender-alias';
    const VARTYPE_PLUGINDATA = 'plugindata';
    const VARTYPE_FEATURE_MESSAGES = 'feature-messages';
    const VARTYPE_TIER = 'tier';
    const VARTYPE_APP_SETTINGS = 'app-settings';
    const VARTYPE_MILLIONVERIFER_USAGE = 'millionverifier_usage';
    const VARTYPE_SPAM_ACTIVITY = 'spam-activity';
    const VARTYPE_IMPORT = 'import';
    const VARTYPE_EMAIL_PREVIEW_SUBSCRIBER = 'email-preview-subscriber';
    const VARTYPE_NEW_SHARED_DOMAINS = 'new-shared-domains';
    const VARTYPE_CUSTOMER_NOTES = 'customer-notes';
    const VARTYPE_UTM_PARAMETERS = 'utm-parameters';

    public static $VarType = UserVariables::VARTYPE_NA;

    public static $DBTableName = 'user_variables';

    public static $DBSerialField = 'VarType';

    //set $EntityBaseType to empty, because this entity should not be labeled or named
    public static $EntityBaseType = '';

    public static $RequiredFieldsOnInsert = array(
        'RelOwnerUserID',
        'VarType',
    );

    public static $RequiredFieldsOnUpdate = array(
        'RelOwnerUserID',
        'VarType',
        'SeqNo',
    );

    public static function FromID($UserID, $SerialID)
    {
        if (empty($UserID) || !static::checkSerialID($SerialID)) {
            return false;
        }

        // get db set
        // Note: use VarType since SeqNo (SerialField) is not of type serial
        $tablename = '{' . static::$DBTableName . '}';
        $userfield = static::$DBUserField;
        $serialfield = static::$DBSerialField;
        $result = db_query(
            "SELECT * FROM $tablename WHERE $userfield = :RelOwnerUserID AND $serialfield = :SerialID AND VarType = :VarType",
            [
                ':RelOwnerUserID' => $UserID,
                ':SerialID' => $SerialID,
                ':VarType' => static::$VarType
            ]
        );
        $DBArray = kt_fetch_array($result);

        if (empty($DBArray)) {
            return false;
        }

        return static::FromArray($DBArray);
    }

    public static function FromArray($DBArray)
    {
        switch ($DBArray['VarType']) {
            case UserVariables::VARTYPE_AFFILIATE_NAME:
                $entity = new VarAffiliateName($DBArray);
                break;
            case UserVariables::VARTYPE_AFFILIATE_PIXEL:
                $entity = new VarAffiliatePixel($DBArray);
                break;
            case UserVariables::VARTYPE_AGEDSUBSCRIBERS:
                $entity = new VarAgedSubscribers($DBArray);
                break;
            case UserVariables::VARTYPE_AMEMBERID:
                $entity = new VarAmemberID($DBArray);
                break;
            case UserVariables::VARTYPE_AMEMBER_PRODUCT_ID:
                $entity = new VarAmemberProductID($DBArray);
                break;
            case UserVariables::VARTYPE_BAM_ASSIGNMENT:
                $entity = new VarBAMAssignment($DBArray);
                break;
            case UserVariables::VARTYPE_CLEVERBRIDGE:
                $entity = new VarCleverbridge($DBArray);
                break;
            case UserVariables::VARTYPE_CONSULTANT:
                $entity = new VarConsultant($DBArray);
                break;
            case UserVariables::VARTYPE_CONVENTION:
                $entity = new VarConvention($DBArray);
                break;
            case UserVariables::VARTYPE_FACEBOOK_AUDIENCE:
                $entity = new VarFacebookAudience($DBArray);
                break;
            case UserVariables::VARTYPE_FULLCONTACT:
                $entity = new VarFullContact($DBArray);
                break;
            case UserVariables::VARTYPE_IMPORT:
                $entity = new VarImport($DBArray);
                break;
            case UserVariables::VARTYPE_NOTIFICATION_MAIL:
                $entity = new VarNotificationEmail($DBArray);
                break;
            case UserVariables::VARTYPE_PROCESSFLOW:
                $entity = new VarProcessflow($DBArray);
                break;
            case UserVariables::VARTYPE_SALES_EMPLOYEE:
                $entity = new VarSalesEmployee($DBArray);
                break;
            case UserVariables::VARTYPE_SUBSCRIBER_AUDIENCE:
                $entity = new VarSubscriberAudience($DBArray);
                break;
            case UserVariables::VARTYPE_MILLIONVERIFER_USAGE:
                $entity = new VarMillionVerifierUsage($DBArray);
                break;
            case UserVariables::VARTYPE_SPAM_ACTIVITY:
                $entity = new VarSpamActivity($DBArray);
                break;
            case UserVariables::VARTYPE_EMAIL_PREVIEW_SUBSCRIBER:
                $entity = new VarEmailPreviewSubscriber($DBArray['RelOwnerUserID']);
                break;
            case UserVariables::VARTYPE_UTM_PARAMETERS:
                $entity = new VarUtmParameters($DBArray);
                break;
            case VarSegment::VARTYPE_SEGMENT_LAST_LOGIN:
            case VarSegment::VARTYPE_SEGMENT_ACTIVE_CAMPAIGNS:
            case VarSegment::VARTYPE_SEGMENT_CONFIRMED_CONTACTS:
            case VarSegment::VARTYPE_SEGMENT_EMAILS_SENT:
            case VarSegment::VARTYPE_SEGMENT_AFFILIATE_NEW_LEADS:
            case VarSegment::VARTYPE_SEGMENT_AFFILIATE_NEW_CUSTOMERS:
            case VarSegment::VARTYPE_SEGMENT_SUBSCRIBER_LIMIT:
                //Note: add all segment types here
                $entity = VarSegment::FromArray($DBArray);
                if (!$entity) {
                    return false;
                }
                break;
            default:
                $entity = new UserVariables($DBArray);
                break;
        }

        if (empty($entity->DataFlat)) {
            return false;
        }

        return $entity;
    }

    public static function InsertDB($Data)
    {
        $Data['VarType'] = static::$VarType;

        // there is no serial, so this will return 0 always
        return parent::InsertDB($Data);
    }

    // DatabaseTableCRUD

    public static $APIPath = 'kt-variable';

    public static $APIFields = array(
        'VarType' => array(
            'id' => 'name',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'SeqNo' => array(
            'id' => 'id',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'RelIndexed' => array(
            'id' => 'int',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'VarcharIndexed' => array(
            'id' => 'string',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Data' => array(
            'id' => 'data',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
    );

    public static function klicktippapi_index_advanced($filter)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        $entities = array();

        // get db sets
        $result = db_query("SELECT * FROM {user_variables} WHERE RelOwnerUserID = :RelOwnerUserID", array(
            ':RelOwnerUserID' => $account->uid,
        ));
        while ($DBArray = kt_fetch_array($result)) {
            $entities[$DBArray['VarType']][$DBArray['SeqNo']] = array(
                'name' => $DBArray['VarType'],
                'id' => $DBArray['SeqNo'],
                'int' => $DBArray['RelIndexed'],
                'string' => $DBArray['VarcharIndexed'],
                'data' => unserialize((string) $DBArray['Data']),
            );
        }

        return $entities;
    }

    /**
     * Check if an entity is used by any UserVariable
     * @param $UserID
     * @param $EntityID : Id of entity to check
     * @param $EntityClass : class of entity to check
     * @param $Dependencies : array() collected dependencies from other base tables
     * @return array(
     *   'message' => array(
     *     EntityID => Edit link
     *     ...
     *  )
     * )
     */
    public static function CheckDependenciesForBaseTable(
        $UserID,
        $EntityID,
        $EntityClass,
        $Dependencies = array(),
        $Op = 'delete',
        $forAngular = false
    ) {
        //add the types that have dependencies, implement GetDependencies() for that class
        $VarTypesToCheck = [
            UserVariables::VARTYPE_IMPORT,
            UserVariables::VARTYPE_EMAIL_PREVIEW_SUBSCRIBER
        ];

        $result = db_query(
            "SELECT * FROM {user_variables} WHERE RelOwnerUserID = :RelOwnerUserID AND VarType IN (:Types)",
            array(
                ':RelOwnerUserID' => $UserID,
                ':Types' => $VarTypesToCheck,
            )
        );

        while ($DBArray = kt_fetch_array($result)) {
            $Object = UserVariables::FromArray($DBArray);
            if (!empty($Object)) {
                $deps = $Object->GetDependencies($EntityID, $EntityClass, $Op, $forAngular);

                if (!empty($deps)) {
                    foreach ($deps as $message => $entities) {
                        foreach ($entities as $id => $link) {
                            $Dependencies[$message][$id] = $link;
                        }
                    }
                }
            }
        }

        return $Dependencies;
    }
}

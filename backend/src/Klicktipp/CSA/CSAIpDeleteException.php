<?php

namespace App\Klicktipp\CSA;

use stdClass;

class CSAIpDeleteException extends CSAException
{
    public function __construct(string $ip, string $host, stdClass $response)
    {
        parent::__construct(
            'Could not delete token for ip @ip and host @host: unexpected response code @code',
            CSAException::CODE_UNEXPECTED_RESPONSE_CODE,
            ['@ip' => $ip, '@host' => $host, '@code' => $response->code, '@response' => print_r($response, true)]
        );
    }
}

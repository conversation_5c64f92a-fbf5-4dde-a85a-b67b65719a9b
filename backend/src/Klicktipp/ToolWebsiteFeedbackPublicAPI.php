<?php

namespace App\Klicktipp;

class ToolWebsiteFeedbackPublicAPI extends ToolWebsiteFeedback
{
    public static function FromArray($DBArray)
    {
        $entity = new ToolWebsiteFeedbackPublicAPI($DBArray);

        if (empty($entity->DataFlat)) {
            return false;
        }

        return $entity;
    }

    // DatabaseTableCRUD

    public static $APIFields = [
        'ToolID' => [
            'id' => 'toolid',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ],
        'Name' => [
            'id' => 'name',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ],
        'ToolType' => [
            'id' => 'type',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ],
        'MetaLabels' => [
            'id' => 'metalabels',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM,
        ],
    ];

    public static function klicktippapi_resource_definition()
    {
        $resource = parent::klicktippapi_resource_definition();

        // GET /api/<enititypath>/<id>
        $resource[static::$APIPath]['retrieve'] = [
            'help' => 'Retrieves an entity',
            'callback' => [get_called_class(), 'klicktippapi_retrieve'],
            'access callback' => 'services_access_menu',
            'args' => [
                [
                    'name' => 'id',
                    'type' => 'string',
                    'description' => 'The id of the entity.',
                    'source' => ['path' => '0'],
                    'optional' => false,
                ],
            ],
        ];

        // PUT /api/<enititypath>/<id> + body(data)
        // we dont allow customers to update the entity, as it might change results unexpectedly
        unset($resource[static::$APIPath]['update']);

        // POST /api/<enititypath>/response + body(data)
        $resource[static::$APIPath]['actions']['response'] = [
            'help' => 'Receive feedback',
            'callback' => [get_called_class(), 'klicktippapi_response'],
            'access callback' => 'services_access_menu',
            'args' => [
                [
                    'name' => 'data',
                    'type' => 'string',
                    'description' => 'The response data.',
                    'source' => 'data',
                    'optional' => false,
                ],
            ],
        ];

        return $resource;
    }

    public static function klicktippapi_retrieve($id)
    {
        if (is_numeric($id)) {
            // called with tool id
            return parent::klicktippapi_retrieve($id);
        }

        $ArrayParameters = Core::DecryptURL($id);
        $ToolID = $ArrayParameters['ToolID'];
        $UserID = $ArrayParameters['UserID'];
        $action = $ArrayParameters['action'];

        /** @var ToolWebsiteFeedback $ToolObject */
        $ToolObject = ToolWebsiteFeedback::FromID($UserID, $ToolID);
        if (empty($ToolObject)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        // display form
        if ($ToolObject->GetData('version') == 1) {
            $optimized = $_GET['optimized'];
            $style = $_GET['bodystyle'];
            $ToolObject->PrintFeedbackFormV1(
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_REFERER'],
                $optimized,
                $style
            );
        } else {
            $ToolObject->PrintFeedbackFormV3(
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_REFERER']
            );
        }

        // we wont get here
        return '';
    }

    public static function klicktippapi_response($data)
    {
        $id = $data['fsid'];
        $content = $data['fscontent'];

        $ArrayParameters = Core::DecryptURL($id);
        $ToolID = $ArrayParameters['ToolID'];
        $UserID = $ArrayParameters['UserID'];
        $action = $ArrayParameters['action'];

        if ($action != ToolSplittestClub::FEEDBACK_ACTION_RESPONSE) {
            return services_error(t('Invalid request.'), 406);
        }

        /** @var ToolWebsiteFeedback $ToolObject */
        $ToolObject = ToolWebsiteFeedback::FromID($UserID, $ToolID);
        if (empty($ToolObject)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        // process feedback submit
        if ($ToolObject->GetData('version') == 1) {
            $ToolObject->ProcessFeedbackV1(
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_REFERER'],
                $content
            );
        } else {
            $ToolObject->ProcessFeedbackV3(
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_REFERER'],
                $content
            );
        }

        // we wont get here
        return '';
    }
}

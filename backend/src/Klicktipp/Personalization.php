<?php

namespace App\Klicktipp;

use App\Klicktipp\LandingPage\LandingPage;
use App\Klicktipp\Personalization\ValueObject\Editor\Response\PersonalizationResponseEditorConditionOptionValueObject;

class Personalization
{
    const SIGNATURE_LINK_SOURCE_PLAIN = '0';
    const SIGNATURE_LINK_SOURCE_HTML = '1';
    const SIGNATURE_LINK_SOURCE_TRANSACTIONAL = '2';

    public static $LanguageAliasesForSubscriberTags = array(
        'SubscriberID' => /*t(*/'Subscriber ID'/*)*/,
        'SubscriberKey' => /*t(*/'Contact-Key'/*)*/,
        // Email
        'EmailAddress' => /*t(*/'Email Address'/*)*/,
        'BounceType' => /*t(*/'Bounce Type'/*)*/,
        'SubscriptionStatus' => /*t(*/'Subscription Status'/*)*/,
        'SubscriptionDate' => /*t(*/'Subscription Date'/*)*/,
        'SubscriptionIP' => /*t(*/'Subscription IP'/*)*/,
        'SubscriptionReferrer' => /*t(*/'Subscription Referrer'/*)*/,
        'UnsubscriptionDate' => /*t(*/'Unsubscription Date'/*)*/,
        'UnsubscriptionIP' => /*t(*/'Unsubscription IP'/*)*/,
        'OptInDate' => /*t(*/'Opt-In Date'/*)*/,
        // SMS
        'PhoneNumber' => /*t(*/'Phone Number'/*)*/,
        'SMSSubscriptionStatus' => /*t(*/'SMS Subscription Status'/*)*/,
        'SMSBounceType' => /*t(*/'SMS Bounce Type'/*)*/,
        'SMSSubscriptionDate' => /*t(*/'SMS Subscription Date'/*)*/,
        'SMSUnsubscriptionDate' => /*t(*/'SMS Unsubscription Date'/*)*/,
        'SubscriptionSMS' => /*t(*/'Subscription SMS'/*)*/,
    );
    public static $LanguageAliasesForLinkTags = array(
        '%Link:Confirm%' => /*t(*/'Opt-In Confirmation Link'/*)*/,
        '%Link:Unsubscribe%' => /*t(*/'Unsubscription Link'/*)*/,
        '%Link:SubscriberInfo%' => /*t(*/'Subscriber Info Link'/*)*/,
        '%Link:SubscriberUpdate%' => /*t(*/'Subscriber Update Link'/*)*/,
        '%Link:SubscriberArea%' => /*t(*/'Subscriber Area Link'/*)*/,
        '%Link:WebBrowser%' => /*t(*/'View In Web Browser Link'/*)*/,
        '%Link:NoTrack(URL)%' => /*t(*/'No-Tracking Link'/*)*/,
        '%Link:ChangeEmailAddress%' => /*t(*/'Change Email Address Link'/*)*/,
        '%User:AffiliateURL%' => /*t(*/'Affiliate URL'/*)*/,
    );
    public static $LanguageAliasesForLinkTagsHTML = array(
        '%Link:Confirm%' => /*t(*/'<a href="%Link:Confirm%">Confirm subscription</a>'
        /*)*/,
        '%Link:Unsubscribe%' => /*t(*/'If you do not wish to receive further emails, please click <a href="%Link:Unsubscribe%">here</a> to unsubscribe from our newsletter.'
        /*)*/,
        '%Link:SubscriberInfo%' => /*t(*/'Click <a href="%Link:SubscriberInfo%">here</a> to request information about the data collected from you.'
        /*)*/,
        '%Link:SubscriberUpdate%' => /*t(*/'Click <a href="%Link:SubscriberUpdate%">here</a> to update your information.'
        /*)*/,
        //'%Link:SubscriberArea%' => 'Subscriber Area Link',
        '%Link:WebBrowser%' => /*t(*/'Click <a href="%Link:WebBrowser%">here</a> to open this email in your web browser.'
        /*)*/,
        '%Link:ChangeEmailAddress%' => /*t(*/'Click <a href="%Link:ChangeEmailAddress%">here</a> to change your email address.'
        /*)*/,
        '%Link:NoTrack(URL)%' => /*t(*/'<a href="%Link:NoTrack(URL)%">The description of your link (<b>no URL</b>, please!)</a>'
        /*)*/,
        '%User:AffiliateURL%' => /*t(*/'<a href="%User:AffiliateURL%">E-Mail Marketing by Klick-Tipp</a>'
        /*)*/,
        '!Wufoo' => /*t(*/'<a href="!Wufoo">The description of your link (<b>no URL</b>, please!)</a>'
        /*)*/,
        '!LandingPage' => /*t(*/'<a href="!LandingPage">The description of your link (<b>no URL</b>, please!)</a>'
        /*)*/,
        '!Default' => /*t(*/'<a href="!Default">The description of your link (<b>no URL</b>, please!)</a>'
        /*)*/,
    );
    public static $LanguageAliasesForUserTags = array(
        '%User:AffiliateID%' => /*t(*/'Affiliate ID'/*)*/,
        '%User:FirstName%' => /*t(*/'First Name'/*)*/,
        '%User:LastName%' => /*t(*/'Last Name'/*)*/,
        '%User:EmailAddress%' => /*t(*/'Email Address'/*)*/,
        '%User:CompanyName%' => /*t(*/'Company Name'/*)*/,
        '%User:Website%' => /*t(*/'Website URL'/*)*/,
        '%User:Street%' => /*t(*/'Street'/*)*/,
        '%User:City%' => /*t(*/'City'/*)*/,
        '%User:State%' => /*t(*/'State'/*)*/,
        '%User:Zip%' => /*t(*/'Zip'/*)*/,
        '%User:Country%' => /*t(*/'Country'/*)*/,
        '%User:Phone%' => /*t(*/'Phone'/*)*/,
        '%User:Fax%' => /*t(*/'Fax'/*)*/,
        '%User:TimeZone%' => /*t(*/'Time Zone'/*)*/,
        '%User:Signature%' => /*t(*/'Signature'/*)*/,
    );
    public static $LanguageAliasesForInvoiceTags = array(
        '%Invoice:Amount%' => /*t(*/'Amount'/*)*/,
        '%Invoice:GrossAmount%' => /*t(*/'Gross Amount'/*)*/,
        '%Invoice:NetAmount%' => /*t(*/'Net Amount'/*)*/,
        '%Invoice:TaxAmount%' => /*t(*/'Tax Amount'/*)*/,
        '%Invoice:Currency%' => /*t(*/'Currency'/*)*/,
        '%Invoice:ReceiptID%' => /*t(*/'Receipt ID'/*)*/,
        '%Invoice:CreatedOn%' => /*t(*/'Created On'/*)*/,
    );

    public static function GetSubscriberPersonalizationTags($ArrayExcludeList = array(), $AccessSMS = false)
    {
        // Loop each list and retrieve fields - Start
        $ArraySubscriptionFields = array();

        if (!is_array($ArrayExcludeList)) {
            $ArrayExcludeList = [];
        }

        // subscriber/subscription fields
        $vals = array(
            array('Field' => 'SubscriberID'),
            array('Field' => 'SubscriberKey'),
            // Email
            array('Field' => 'EmailAddress'),
            array('Field' => 'BounceType'),
            array('Field' => 'SubscriptionStatus'),
            array('Field' => 'SubscriptionDate'),
            array('Field' => 'SubscriptionIP'),
            array('Field' => 'UnsubscriptionDate'),
            array('Field' => 'UnsubscriptionIP'),
            array('Field' => 'OptInDate'),
        );
        if ($AccessSMS) {
            // SMS
            $vals[] = array('Field' => 'PhoneNumber');
            $vals[] = array('Field' => 'SMSSubscriptionStatus');
            $vals[] = array('Field' => 'SMSBounceType');
            $vals[] = array('Field' => 'SMSSubscriptionDate');
            $vals[] = array('Field' => 'SMSUnsubscriptionDate');
            $vals[] = array('Field' => 'SubscriptionSMS');
        };

        foreach ($vals as $EachField) {
            if (in_array($EachField['Field'], $ArrayExcludeList)) {
                continue;
            }
            $alias = Personalization::$LanguageAliasesForSubscriberTags[$EachField['Field']];
            if (!empty($alias)) {
                $ArraySubscriptionFields["%Subscriber:{$EachField['Field']}%"] = t(/*ignore*/ $alias);
            } else {
                $ArraySubscriptionFields["%Subscriber:{$EachField['Field']}%"] = $EachField['Field'];
            }
        }

        return $ArraySubscriptionFields;
    }

    public static function GetCustomFieldPersonalizationTags($UserID, $ArrayExcludeList = array())
    {
        $ArrayCustomFields = array();

        // custom fields categorized
        $AllCustomFields = CustomFields::RetrieveCustomFieldsCategories($UserID);

        foreach ($AllCustomFields['CategoryFields'] as $CategoryName => $CategoryFields) {
            if ($CategoryName == 'general') {
                $CategoryName = t('Subscriber Custom fields - category General');
            } else {
                $CategoryName = t('Subscriber Custom fields - category !category', array('!category' => $CategoryName));
            }

            foreach ($CategoryFields as $EachField) {
                if (in_array($EachField['CustomFieldID'], $ArrayExcludeList)) {
                    continue;
                }

                $FieldID = "%Subscriber:CustomField{$EachField['CustomFieldID']}%";

                if ($EachField['IsGlobal']) {
                    $ArrayCustomFields[t('Subscriber Global fields')][$FieldID] = CustomFields::GetFieldName(
                        $EachField
                    );
                } else {
                    $ArrayCustomFields[$CategoryName][$FieldID] = ($EachField['FieldName'] != '') ? $EachField['FieldName'] : t(
                        'Custom field:'
                    ) . $EachField['CustomFieldID'];
                }
            }

            // Sort fields in category
            // Note: global fields have the same order throughout klicktipp, do not sort
            if (is_array($ArrayCustomFields[$CategoryName])) {
                natcasesort($ArrayCustomFields[$CategoryName]);
            }
        }

        return $ArrayCustomFields;
    }

    public static function GetPersonalizationLinkTags($Category = 'Campaign')
    {
        if ($Category == 'Opt') {
            return array_map('t', array(
                '%Link:Confirm%' => Personalization::$LanguageAliasesForLinkTags['%Link:Confirm%'],
                '%User:AffiliateURL%' => Personalization::$LanguageAliasesForLinkTags['%User:AffiliateURL%'],
            ));
        } elseif ($Category == 'SMS') {
            return array_map('t', array(
                '%Link:SubscriberInfo%' => Personalization::$LanguageAliasesForLinkTags['%Link:SubscriberInfo%'],
                '%Link:SubscriberUpdate%' => Personalization::$LanguageAliasesForLinkTags['%Link:SubscriberUpdate%'],
                '%User:AffiliateURL%' => Personalization::$LanguageAliasesForLinkTags['%User:AffiliateURL%'],
                '%Link:NoTrack(URL)%' => Personalization::$LanguageAliasesForLinkTags['%Link:NoTrack(URL)%'],
            ));
        } elseif ($Category == 'subscriber_info') {
            return array_map('t', array(
                '%Link:Unsubscribe%' => Personalization::$LanguageAliasesForLinkTags['%Link:Unsubscribe%'],
                '%Link:SubscriberUpdate%' => Personalization::$LanguageAliasesForLinkTags['%Link:SubscriberUpdate%'],
                '%Link:ChangeEmailAddress%' => Personalization::$LanguageAliasesForLinkTags['%Link:ChangeEmailAddress%'],
                '%User:AffiliateURL%' => Personalization::$LanguageAliasesForLinkTags['%User:AffiliateURL%'],
            ));
        } else {
            return array_map('t', array(
                '%Link:Unsubscribe%' => Personalization::$LanguageAliasesForLinkTags['%Link:Unsubscribe%'],
                '%Link:SubscriberInfo%' => Personalization::$LanguageAliasesForLinkTags['%Link:SubscriberInfo%'],
                '%Link:SubscriberUpdate%' => Personalization::$LanguageAliasesForLinkTags['%Link:SubscriberUpdate%'],
                '%Link:ChangeEmailAddress%' => Personalization::$LanguageAliasesForLinkTags['%Link:ChangeEmailAddress%'],
                '%User:AffiliateURL%' => Personalization::$LanguageAliasesForLinkTags['%User:AffiliateURL%'],
                '%Link:WebBrowser%' => Personalization::$LanguageAliasesForLinkTags['%Link:WebBrowser%'],
                //              '%Link:SubscriberArea%'=> Personalization::$LanguageAliasesForLinkTags['%Link:SubscriberArea%']), // temporarely disabled, see http://www.pivotaltracker.com/story/show/5021894
                '%Link:NoTrack(URL)%' => Personalization::$LanguageAliasesForLinkTags['%Link:NoTrack(URL)%'],
            ));
        }
    }

    public static function GetPersonalizationHtmlLinkTags($UserID)
    {
        $HTMLLinkTags = array(
            '%Link:Confirm%' => Personalization::$LanguageAliasesForLinkTagsHTML['%Link:Confirm%'],
            '%Link:Unsubscribe%' => Personalization::$LanguageAliasesForLinkTagsHTML['%Link:Unsubscribe%'],
            '%Link:SubscriberInfo%' => Personalization::$LanguageAliasesForLinkTagsHTML['%Link:SubscriberInfo%'],
            '%Link:SubscriberUpdate%' => Personalization::$LanguageAliasesForLinkTagsHTML['%Link:SubscriberUpdate%'],
            '%Link:ChangeEmailAddress%' => Personalization::$LanguageAliasesForLinkTagsHTML['%Link:ChangeEmailAddress%'],
            '%User:AffiliateURL%' => Personalization::$LanguageAliasesForLinkTagsHTML['%User:AffiliateURL%'],
            '%Link:WebBrowser%' => Personalization::$LanguageAliasesForLinkTagsHTML['%Link:WebBrowser%'],
            //                '%Link:SubscriberArea%'=> Personalization::$LanguageAliasesForLinkTagsHTML['%Link:SubscriberArea%']), // temporarely disabled, see http://www.pivotaltracker.com/story/show/5021894
            '%Link:NoTrack(URL)%' => Personalization::$LanguageAliasesForLinkTagsHTML['%Link:NoTrack(URL)%'],
            '!Default' => Personalization::$LanguageAliasesForLinkTagsHTML['!Default'],
        );

        //translated links
        $HTMLLinkTags = array_map('t', $HTMLLinkTags);

        //add wufoo links
        //Note: Wufoo-Links are already translated
        $HTMLLinkTags += Personalization::GetPersonalizationWufooTags($UserID, true);
        //add landing page links
        //Note: Landing page links are already translated
        $HTMLLinkTags += Personalization::getPersonalizationLandingPageTags($UserID, true);
        //add email countdowns as html
        $HTMLLinkTags += Personalization::GetPersonalizationClockTags($UserID, true);
        //add email plugins as html
        $HTMLLinkTags += Personalization::GetPersonalizationPluginTags($UserID, 'html');
        //add signatures as html
        $HTMLLinkTags += Personalization::GetPersonalizationSignatureTags($UserID, true);

        return array('LinkReplacements' => $HTMLLinkTags);
    }

    public static function GetPersonalizationUserTags($ForConfirmation = false)
    {
        if ($ForConfirmation) {
            $ArrayAvailableUserTags = array(
                '%User:FirstName%' => Personalization::$LanguageAliasesForUserTags['%User:FirstName%'],
                '%User:LastName%' => Personalization::$LanguageAliasesForUserTags['%User:LastName%'],
                '%User:EmailAddress%' => Personalization::$LanguageAliasesForUserTags['%User:EmailAddress%'],
                '%User:CompanyName%' => Personalization::$LanguageAliasesForUserTags['%User:CompanyName%'],
                //          '%User:Website%'=> Personalization::$LanguageAliasesForUserTags['%User:Website%'],
                '%User:Street%' => Personalization::$LanguageAliasesForUserTags['%User:Street%'],
                '%User:City%' => Personalization::$LanguageAliasesForUserTags['%User:City%'],
                '%User:State%' => Personalization::$LanguageAliasesForUserTags['%User:State%'],
                '%User:Zip%' => Personalization::$LanguageAliasesForUserTags['%User:Zip%'],
                '%User:Country%' => Personalization::$LanguageAliasesForUserTags['%User:Country%'],
                '%User:Phone%' => Personalization::$LanguageAliasesForUserTags['%User:Phone%'],
                '%User:Fax%' => Personalization::$LanguageAliasesForUserTags['%User:Fax%'],
                //'%User:TimeZone%' => Personalization::$LanguageAliasesForUserTags['%User:TimeZone%'],
            );
        } else {
            $ArrayAvailableUserTags = array(
                '%User:AffiliateURL%' => Personalization::$LanguageAliasesForUserTags['%User:AffiliateURL%'],
            );
        }

        //translate labels (array_map('t', ...)
        return array_map('t', $ArrayAvailableUserTags);
    }

    public static function GetPersonalizationFullContactTag($UserID)
    {
        $account = user_load($UserID);
        if (!user_access('use whitelabel domain', $account)) {
            return [];
        }
        return [
            '%Subscriber:FullContact%' => t('FullContact Data'),
        ];
    }

    public static function GetPersonalizationWufooTags($UserID, $AsHtmlLink = false)
    {
        $ArrayAvailableUserTags = array();

        $query = "SELECT BuildID, Name FROM {listbuilding} WHERE RelOwnerUserID = :RelOwnerUserID AND BuildType = :BuildType ORDER BY Name ASC";
        $result = db_query($query, array(':RelOwnerUserID' => $UserID, ':BuildType' => Listbuildings::TYPE_WUFOO));

        while ($Wufoo = kt_fetch_array($result)) {
            $ArrayQueryParameters = array(
                'BuildID' => $Wufoo['BuildID'],
                'UserID' => $UserID,
            );
            $apikey = Core::EncryptURL($ArrayQueryParameters, 'api_key');
            $link = "%Link:Application%/wufoo/$apikey/%Subscriber:Secret%";

            if ($AsHtmlLink) {
                $ArrayAvailableUserTags[$link] = t(/*ignore*/
                    Personalization::$LanguageAliasesForLinkTagsHTML['!Wufoo'],
                    array('!Wufoo' => $link)
                );
            } else {
                $ArrayAvailableUserTags[$link] = $Wufoo['Name'];
            }
        }

        return $ArrayAvailableUserTags;
    }

    public static function GetPersonalizationClockTags($UserID, $AsHtmlLink = false)
    {
        $ArrayAvailableUserTags = array();

        $query = "SELECT ToolID, Name FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID AND ToolType = :ToolType AND Deleted = :NotDeleted ORDER BY Name";
        $params = array(
            ':RelOwnerUserID' => $UserID,
            ':ToolType' => MarketingTools::TOOL_TYPE_COUNTDOWN,
            ':NotDeleted' => MarketingTools::TOOL_DELETE_STATUS_NOTDELETED,
        );

        $result = db_query($query, $params);

        while ($countdown = kt_fetch_array($result)) {
            $ToolID = $countdown['ToolID'];

            //placeholder for plain text
            $placeholder = "%Link:ClockLinkedImage($ToolID)%";

            if ($AsHtmlLink) {
                //wrap placeholder with span to replace it with an image in CKEditor
                $ArrayAvailableUserTags[$placeholder] = "<span data-clock='1'>$placeholder</span>";
            } else {
                $ArrayAvailableUserTags[$placeholder] = $countdown['Name'];
            }
        }

        return $ArrayAvailableUserTags;
    }

    public static function GetPersonalizationPluginTags($UserID, $Purpose, $EmailType = -1)
    {
        return ToolPluginGeneral::GetCustomFields($UserID, $Purpose, $EmailType);
    }

    public static function GetPersonalizationSignatureTags($UserID, $AsHtmlLink = false)
    {
        $ArrayAvailableUserTags = array();

        $Signatures = Signatures::RetrieveSignatures($UserID);
        foreach ($Signatures as $signature) {
            if (empty(array_filter($signature['vCard']))) {
                // all vcard values are empty
                continue;
            }
            //placeholder for plain text
            $SignatureID = $signature['SignatureID'];
            $placeholder = "%Link:vCard({$SignatureID})%";
            if ($AsHtmlLink) {
                //wrap placeholder with span to replace it with an image in CKEditor
                $text = t('You can download the !vcard or scan the following qr code.', array(
                    '!vcard' => "<a href=\"%Link:vCardURL({$SignatureID})%\">" . t('vCard') . "</a>",
                ));
                $ArrayAvailableUserTags[$placeholder] = "$text<br /><span data-qrcode='$SignatureID'>$placeholder</span>";
            } else {
                $ArrayAvailableUserTags[$placeholder] = Signatures::GetNameForOptionsArray($signature);
            }
        }
        return $ArrayAvailableUserTags;
    }

    /**
    * @param int $userId
    * @param bool $asHtmlLink
    * @return array<mixed>
    * @throws \Doctrine\DBAL\Exception
     */
    public static function getPersonalizationLandingPageTags($userId, $asHtmlLink = false): array
    {
        $ArrayAvailableUserTags = array();

        foreach (LandingPage::getOfUser($userId) as $landingPage) {
            if (empty($landingPage->getFullDomain())) {
                // don't show links to landing pages without a domain
                continue;
            }
            //placeholder for plain text
            $id = $landingPage->GetData('BuildID');
            $placeholder = "%Link:LandingPage($id)%";
            if ($asHtmlLink) {
                $ArrayAvailableUserTags[$placeholder] = t(/*ignore*/
                    self::$LanguageAliasesForLinkTagsHTML['!LandingPage'],
                    array('!LandingPage' => $placeholder)
                );
            } else {
                $ArrayAvailableUserTags[$placeholder] = $landingPage->GetData('Name');
            }
        }
        return $ArrayAvailableUserTags;
    }

    public static function GetPersonalizationInvoiceTags()
    {
        $ArrayAvailableInvoiceTags = array(
            '%Invoice:Amount%' => Personalization::$LanguageAliasesForInvoiceTags['%Invoice:Amount%'],
            '%Invoice:GrossAmount%' => Personalization::$LanguageAliasesForInvoiceTags['%Invoice:GrossAmount%'],
            '%Invoice:NetAmount%' => Personalization::$LanguageAliasesForInvoiceTags['%Invoice:NetAmount%'],
            '%Invoice:TaxAmount%' => Personalization::$LanguageAliasesForInvoiceTags['%Invoice:TaxAmount%'],
            '%Invoice:Currency%' => Personalization::$LanguageAliasesForInvoiceTags['%Invoice:Currency%'],
            '%Invoice:ReceiptID%' => Personalization::$LanguageAliasesForInvoiceTags['%Invoice:ReceiptID%'],
            '%Invoice:CreatedOn%' => Personalization::$LanguageAliasesForInvoiceTags['%Invoice:CreatedOn%'],
        );

        //translate labels (array_map('t', ...)
        return array_map('t', $ArrayAvailableInvoiceTags);
    }


    public static function GetTagsFor($Mode, $account, $EmailType = -1, $UsableAsInvoice = false)
    {
        $UserID = $account->uid;
        $AccessSMS = user_access('access sms marketing', $account);

        if ($EmailType < 0) {
            $EmailType = Emails::GetEmailTypeFromMode($Mode);
        }

        $account_tags[t('Account information')] = [
            '%User:AffiliateID%' => Personalization::$LanguageAliasesForUserTags['%User:AffiliateID%'],
        ];

        if ($Mode == 'signature') {
            $array_subscription_tags = Personalization::GetSubscriberPersonalizationTags(
                array(
                    // Email
                    'BounceType',
                    'SubscriptionStatus',
                    'UnsubscriptionDate',
                    'UnsubscriptionIP',
                    // SMS
                    //'PhoneNumber',
                    'SMSSubscriptionStatus',
                    'SMSBounceType',
                    //'SMSSubscriptionDate',
                    'SMSUnsubscriptionDate',
                    //'SubscriptionSMS',
                ),
                $AccessSMS
            );
            $array_campaign_link_tags = Personalization::GetPersonalizationLinkTags('Campaign');
            $array_fullcontact_tag = Personalization::GetPersonalizationFullContactTag($UserID);
            $array_wufoo_tags = Personalization::GetPersonalizationWufooTags($UserID);
            $array_landing_page_tags = Personalization::getPersonalizationLandingPageTags($UserID);
            $array_signature_tags = Personalization::GetPersonalizationSignatureTags($UserID);
            $array_plugins_tags = Personalization::GetPersonalizationPluginTags($UserID, 'dropdown', $EmailType);

            $array_content_tags = array();
            $array_content_tags[t('Email information')] = $array_subscription_tags;

            //add categorized custom field tags
            $array_customfield_tags = Personalization::GetCustomFieldPersonalizationTags($UserID);
            foreach ($array_customfield_tags as $translated_category_name => $category_fields) {
                $array_content_tags[$translated_category_name] = $category_fields;
            }

            $array_content_tags[t('Links')] = $array_campaign_link_tags;
            $array_content_tags[t('FullContact')] = $array_fullcontact_tag;
            $array_content_tags[t('Wufoo Subscription forms')] = $array_wufoo_tags;
            $array_content_tags[t('Landing pages')] = $array_landing_page_tags;
            $array_content_tags[t('Plugins')] = $array_plugins_tags;
            $array_content_tags[t('vCards')] = $array_signature_tags;
            $array_content_tags += $account_tags;
        } elseif ($Mode == 'confirmation') {
            $array_subscription_tags = Personalization::GetSubscriberPersonalizationTags(
                array(
                    // Email
                    'BounceType',
                    'SubscriptionStatus',
                    'SubscriptionIP',
                    'UnsubscriptionDate',
                    'UnsubscriptionIP',
                    'SubscriptionDate',
                    'LastOpenDate',
                    // SMS
                    //'PhoneNumber',
                    'SMSSubscriptionStatus',
                    'SMSBounceType',
                    //'SMSSubscriptionDate',
                    'SMSUnsubscriptionDate',
                    //'SubscriptionSMS',
                ),
                $AccessSMS
            );
            $array_opt_tags = Personalization::GetPersonalizationLinkTags('Opt');
            $array_fullcontact_tag = Personalization::GetPersonalizationFullContactTag($UserID);
            $array_wufoo_tags = Personalization::GetPersonalizationWufooTags($UserID);
            $array_landing_page_tags = Personalization::getPersonalizationLandingPageTags($UserID);
            $array_signature_tags = Personalization::GetPersonalizationSignatureTags($UserID);

            $array_content_tags = array();
            $array_content_tags[t('Email information')] = $array_subscription_tags;

            //add categorized custom field tags
            $array_customfield_tags = Personalization::GetCustomFieldPersonalizationTags($UserID);
            foreach ($array_customfield_tags as $translated_category_name => $category_fields) {
                $array_content_tags[$translated_category_name] = $category_fields;
            }

            $array_content_tags[t('Confirmation links')] = $array_opt_tags;
            $array_content_tags[t('FullContact')] = $array_fullcontact_tag;
            $array_content_tags[t('Wufoo Subscription forms')] = $array_wufoo_tags;
            $array_content_tags[t('Landing pages')] = $array_landing_page_tags;
            $array_content_tags[t('vCards')] = $array_signature_tags;
            $array_content_tags += $account_tags;
        } elseif ($Mode == 'sms') {
            $array_subscription_tags = Personalization::GetSubscriberPersonalizationTags(
                array(
                    // Email
                    'BounceType',
                    'SubscriptionStatus',
                    'SubscriptionDate',
                    'SubscriptionIP',
                    'UnsubscriptionDate',
                    'UnsubscriptionIP',
                    'OptInDate',
                    'LastOpenDate',
                    // SMS
                    //'PhoneNumber',
                    'SMSSubscriptionStatus',
                    'SMSBounceType',
                    //'SMSSubscriptionDate',
                    'SMSUnsubscriptionDate',
                    //'SubscriptionSMS',
                ),
                $AccessSMS
            );
            $array_campaign_link_tags = Personalization::GetPersonalizationLinkTags('SMS');
            $array_plugins_tags = Personalization::GetPersonalizationPluginTags($UserID, 'SMS', $EmailType);

            $array_content_tags = array();
            $array_content_tags[t('SMS information')] = $array_subscription_tags;

            //add categorized custom field tags
            $array_customfield_tags = Personalization::GetCustomFieldPersonalizationTags($UserID);
            foreach ($array_customfield_tags as $translated_category_name => $category_fields) {
                $array_content_tags[$translated_category_name] = $category_fields;
            }

            $array_content_tags[t('Links')] = $array_campaign_link_tags;
            $array_content_tags[t('Plugins')] = $array_plugins_tags;
        } elseif ($Mode == 'subscriber_info') {
            $array_campaign_link_tags = Personalization::GetPersonalizationLinkTags('subscriber_info');
            $array_fullcontact_tag = Personalization::GetPersonalizationFullContactTag($UserID);
            $array_wufoo_tags = Personalization::GetPersonalizationWufooTags($UserID);
            $array_landing_page_tags = Personalization::getPersonalizationLandingPageTags($UserID);
            $array_signature_tags = Personalization::GetPersonalizationSignatureTags($UserID);

            $array_content_tags = array();

            //add categorized custom field tags
            $array_customfield_tags = Personalization::GetCustomFieldPersonalizationTags($UserID);
            foreach ($array_customfield_tags as $translated_category_name => $category_fields) {
                $array_content_tags[$translated_category_name] = $category_fields;
            }

            $array_content_tags[t('Links')] = $array_campaign_link_tags;
            $array_content_tags[t('FullContact')] = $array_fullcontact_tag;
            $array_content_tags[t('Wufoo Subscription forms')] = $array_wufoo_tags;
            $array_content_tags[t('Landing pages')] = $array_landing_page_tags;
            $array_content_tags[t('vCards')] = $array_signature_tags;
        } elseif ($Mode == 'automation') {
            //processflow set field action

            $array_subscription_tags = Personalization::GetSubscriberPersonalizationTags(
                array(),
                $AccessSMS
            );

            $array_content_tags = array();
            $array_content_tags[t('Email / SMS information')] = $array_subscription_tags;

            //add categorized custom field tags
            $array_customfield_tags = Personalization::GetCustomFieldPersonalizationTags($UserID);
            foreach ($array_customfield_tags as $translated_category_name => $category_fields) {
                $array_content_tags[$translated_category_name] = $category_fields;
            }
            $array_content_tags += $account_tags;
        } else {
            $array_subscription_tags = Personalization::GetSubscriberPersonalizationTags(
                array(
                    // Email
                    'BounceType',
                    'SubscriptionStatus',
                    'UnsubscriptionDate',
                    'UnsubscriptionIP',
                    'OptInDate',
                    'LastOpenDate',
                    // SMS
                    //'PhoneNumber',
                    'SMSSubscriptionStatus',
                    'SMSBounceType',
                    //'SMSSubscriptionDate',
                    'SMSUnsubscriptionDate',
                    //'SubscriptionSMS',
                ),
                $AccessSMS
            );
            $array_campaign_link_tags = Personalization::GetPersonalizationLinkTags('Campaign');
            $array_fullcontact_tag = Personalization::GetPersonalizationFullContactTag($UserID);
            $array_wufoo_tags = Personalization::GetPersonalizationWufooTags($UserID);
            $array_landing_page_tags = Personalization::getPersonalizationLandingPageTags($UserID);
            $array_countdown_tags = Personalization::GetPersonalizationClockTags($UserID);
            $array_plugins_tags = Personalization::GetPersonalizationPluginTags($UserID, 'dropdown', $EmailType);
            $array_signature_tags = Personalization::GetPersonalizationSignatureTags($UserID);

            $array_content_tags = array();
            $array_content_tags[t('Email information')] = $array_subscription_tags;

            //add categorized custom field tags
            $array_customfield_tags = Personalization::GetCustomFieldPersonalizationTags($UserID);
            foreach ($array_customfield_tags as $translated_category_name => $category_fields) {
                $array_content_tags[$translated_category_name] = $category_fields;
            }

            $array_content_tags[t('Links')] = $array_campaign_link_tags;
            $array_content_tags[t('FullContact')] = $array_fullcontact_tag;
            $array_content_tags[t('Wufoo Subscription forms')] = $array_wufoo_tags;
            $array_content_tags[t('Landing pages')] = $array_landing_page_tags;
            $array_content_tags[t('Countdowns')] = $array_countdown_tags;
            $array_content_tags[t('Plugins')] = $array_plugins_tags;
            $array_content_tags[t('vCards')] = $array_signature_tags;

            if ($UsableAsInvoice) {
                $array_content_tags[t('Invoice')] = Personalization::GetPersonalizationInvoiceTags();
            }
            $array_content_tags += $account_tags;
        }

        //remove empty categories
        //Note: array_filter without a callback (2. param) will remove empty entries
        $array_content_tags = array_filter($array_content_tags);

        return $array_content_tags;
    }

    /**
     * Get the personalization tags for the new editor depending on the EmailType
     * @param $account
     * @param $EmailType
     *
     * @return array
     */
    public static function GetEditorTags($account, $EmailType)
    {
        //<#ee>Email editor placeholder dialog</#>

        $Tags = [];
        $Links = [];
        $CustomAddons = [];


        $Groups = array(
            array(
                'id' => 'contact-tags',
                'name' => t(/*#ee::group::name*/ 'Recipient Data'),
                'descr' => t(/*#ee::group::description*/
                    'First name, last name and other predefined and custom fields of your contacts'
                ),
                'weight' => 1
            ),
            array(
                'id' => 'account-tags',
                'name' => t(/*#ee::group::name*/ 'Sender Data'),
                'descr' => t(/*#ee::group::description*/ 'Your own contact details and vCards'),
                'weight' => 2
            ),
            array(
                'id' => 'misc-tags',
                'name' => t(/*#ee::group::name*/ 'Other Data'),
                'descr' => t(/*#ee::group::description*/ 'Countdowns, Invoice data'),
                'weight' => 3
            ),
            array(
                'id' => 'system-links',
                'name' => t(/*#ee::group::name*/ 'System Links'),
                'descr' => t(/*#ee::group::description*/ 'GDPR relevant links, Browser View, Partner Link'),
                'weight' => 4
            ),
            array(
                'id' => 'vcard-links',
                'name' => t(/*#ee::group::name*/ 'vCard Links'),
                'descr' => t(/*#ee::group::description*/ 'Links to download the vCards in your account'),
                'weight' => 5
            ),
            array(
                'id' => 'wufoo-links',
                'name' => t(/*#ee::group::name*/ 'Wufoo Forms'),
                'descr' => t(/*#ee::group::description*/ 'Links to your Wufoo Forms'),
                'weight' => 6
            ),
            array(
                'id' => 'landing-page-links',
                'name' => t(/*#ee::group::name*/ 'Landing pages'),
                'descr' => t(/*#ee::group::description*/ 'Links to your Landing pages'),
                'weight' => 6
            )
        );

        $Categories = array(
            array(
                'id' => 'global-fields',
                'name' => t(/*#ee::category::name*/ 'Global Fields'),
                'weight' => 1
            ),
            array(
                'id' => 'custom-fields',
                'name' => t(/*#ee::category::name*/ 'Custom Fields'),
                'weight' => 2
            ),
            array(
                'id' => 'email',
                'name' => t(/*#ee::category::name*/ 'Email information'),
                'weight' => 3
            ),
            array(
                'id' => 'sms',
                'name' => t(/*#ee::category::name*/ 'SMS information'),
                'weight' => 4
            ),
            array(
                'id' => 'account',
                'name' => t(/*#ee::category::name*/ 'Account information'),
                'weight' => 5
            ),
            array(
                'id' => 'vcard-qrcode',
                'name' => t(/*#ee::category::name*/ 'vCards'),
                'weight' => 6
            ),
            array(
                'id' => 'countdowns',
                'name' => t(/*#ee::category::name*/ 'Countdowns'),
                'weight' => 7
            ),
            array(
                'id' => 'invoice',
                'name' => t(/*#ee::category::name*/ 'Invoice data'),
                'weight' => 8
            ),
            array(
                'id' => 'system-links',
                'name' => t(/*#ee::category::name*/ 'System Links'),
                'weight' => 9
            ),
            array(
                'id' => 'vcard-links',
                'name' => t(/*#ee::category::name*/ 'vCard Links'),
                'weight' => 10
            ),
            array(
                'id' => 'wufoo-links',
                'name' => t(/*#ee::category::name*/ 'Wufoo Forms'),
                'weight' => 11
            ),
            array(
                'id' => 'landing-page-links',
                'name' => t(/*#ee::category::name*/ 'Landing pages'),
                'weight' => 12
            ),
            array(
                'id' => 'plugins',
                'name' => t(/*#ee::category::name*/ 'Plugins'),
                'weight' => 13
            )
        );

        // --- group: contact-tags, category: global-fields AND custom-fields

        $AllCustomFields = CustomFields::RetrieveCustomFields($account->uid, true);

        foreach ($AllCustomFields as $field) {
            $placeholder = "%Subscriber:CustomField{$field['CustomFieldID']}%";

            if ($field['IsGlobal']) {
                $Tags[] = array(
                    'label' => CustomFields::GetNameForOptionsArray($field),
                    'placeholder' => $placeholder,
                    'plain' => $placeholder,
                    'ckeditor' => [
                        'html' => $placeholder,
                        // no difference between on and off ckeditor, nothing to toggle
                        'on' => '',
                        'off' => '',
                    ],
                    'group' => array(
                        'contact-tags' => array('global-fields')
                    )
                );
            } else {
                $subcategory = $field['FieldParameters']['FieldCategory'] ?? t("General");

                $Tags[] = array(
                    'label' => CustomFields::GetNameForOptionsArray($field),
                    'placeholder' => $placeholder,
                    'plain' => $placeholder,
                    'ckeditor' => [
                        'html' => $placeholder,
                        // no difference between on and off ckeditor, nothing to toggle
                        'on' => '',
                        'off' => '',
                    ],
                    'group' => array(
                        'contact-tags' => array('custom-fields')
                    ),
                    'subcategory' => $subcategory
                );
            }
        }

        // --- group: contact-tags, category: email

        $SubscriptionTags = array(
            'SubscriberID',
            'SubscriberKey',
            'EmailAddress',
            'BounceType',
            'OptInDate',
            'SubscriptionStatus',
            'SubscriptionDate',
            'SubscriptionIP',
            'UnsubscriptionDate',
            'UnsubscriptionIP',
        );

        foreach ($SubscriptionTags as $tag) {
            $category = array('email');

            if ($tag === 'SubscriberID') {
                //show the subscriber id in both categories email and sms
                $category[] = 'sms';
            }

            $Tags[] = array(
                'label' => t(/*ignore*/ Personalization::$LanguageAliasesForSubscriberTags[$tag]),
                'placeholder' => "%Subscriber:$tag%",
                'plain' => "%Subscriber:$tag%",
                'ckeditor' => [
                    'html' => "%Subscriber:$tag%",
                    // no difference between on and off ckeditor, nothing to toggle
                    'on' => '',
                    'off' => '',
                ],
                'group' => array(
                    'contact-tags' => $category
                )
            );
        }

        //add Full Contact Data if user has access
        if (user_access('use whitelabel domain', $account)) {
            $Tags[] = array(
                'label' => t('FullContact Data'),
                'placeholder' => "%Subscriber:FullContact%",
                'plain' => "%Subscriber:FullContact%",
                'ckeditor' => [
                    'html' => "%Subscriber:FullContact%",
                    // no difference between on and off ckeditor, nothing to toggle
                    'on' => '',
                    'off' => '',
                ],
                'group' => array(
                    'contact-tags' => array('email')
                )
            );
        }

        // --- group: contact-tags, category: sms

        if (user_access('access sms marketing', $account)) {
            $SmsSubscriptionTags = array(
                'PhoneNumber',
                'SMSSubscriptionStatus',
                'SMSBounceType',
                'SMSSubscriptionDate',
                'SMSUnsubscriptionDate',
                'SubscriptionSMS'
            );

            foreach ($SmsSubscriptionTags as $tag) {
                $Tags[] = array(
                    'label' => t(/*ignore*/ Personalization::$LanguageAliasesForSubscriberTags[$tag]),
                    'placeholder' => "%Subscriber:$tag%",
                    'plain' => "%Subscriber:$tag%",
                    'ckeditor' => [
                        'html' => "%Subscriber:$tag%",
                        // no difference between on and off ckeditor, nothing to toggle
                        'on' => '',
                        'off' => '',
                    ],
                    'group' => array(
                        'contact-tags' => array('sms')
                    )
                );
            }
        }

        // --- group: account-tags, category: account

        $AccountTags = array(
            '%User:FirstName%',
            '%User:LastName%',
            '%User:EmailAddress%',
            '%User:CompanyName%',
            '%User:Website%',
            '%User:Street%',
            '%User:City%',
            '%User:State%',
            '%User:Zip%',
            '%User:Country%',
            '%User:Phone%',
            '%User:Fax%',
            '%User:Signature%',
            '%User:AffiliateID%',
        );

        foreach ($AccountTags as $tag) {
            $Tags[] = array(
                'label' => t(/*ignore*/ Personalization::$LanguageAliasesForUserTags[$tag]),
                'placeholder' => $tag,
                'plain' => $tag,
                'ckeditor' => [
                    'html' => $tag,
                    // no difference between on and off ckeditor, nothing to toggle
                    'on' => '',
                    'off' => '',
                ],
                'group' => array(
                    'account-tags' => array('account')
                ),
            );
        }

        // --- group: account-tags, category: vcard-qrcode
        // --- group: vcard-links, category: vcard-links

        $Signatures = Signatures::RetrieveSignatures($account->uid);

        foreach ($Signatures as $signature) {
            $vCard = Signatures::GenerateVCard($signature);
            if (empty($vCard)) {
                // all vcard values are empty
                continue;
            }

            $SignatureID = $signature['SignatureID'];
            $vCardUrl = Signatures::GenerateVCardUrl($vCard);
            $vCardLink = "<a href=\"%Link:vCardURL({$SignatureID})%\">" . t('vCard') . "</a>";

            $ckeditorHTML = t('You can download the !vcard or scan the following qr code.', array(
                '!vcard' => $vCardLink
            ));
            $ckeditorHTML = "$ckeditorHTML<br /><span data-qrcode='$SignatureID'>%Link:vCard($SignatureID)%</span>";

            //the code used in the email for the qr code
            //<span data-qrcode='$SignatureID'> in the ckeditor html will be replaced with the qr code html
            $qrCodeHtml = <<<HTML
<div style="height:200px;">
<a href="%Link:vCardURL({$SignatureID})%"
   style="display:inline-block; background: center no-repeat url($vCardUrl);background-size:contain;height:100%;width:100%;color:transparent;">
</a>
</div>
HTML;

            $Tags[] = array(
                'id' => $SignatureID,
                'label' => Signatures::GetNameForOptionsArray($signature),
                // no placeholder in DD email, customAddon used
                'placeholder' => 'Please us the signature component to insert a signature.',
                // in plain text, the vCard URL is used
                'plain' => "%Link:vCardURL({$SignatureID})%",
                'ckeditor' => [
                    // the html used in rich emails
                    'html' => $ckeditorHTML,
                    // when ckeditor is on, replace off value (that exists in html)
                    'on' => '<img src="' . $vCardUrl . '" style="height:200px;"/>',
                    // when ckeditor is off, replace on value with off value (equals html used in email)
                    'off' => "<span data-qrcode='$SignatureID'>%Link:vCard($SignatureID)%</span>",
                ],
                'group' => array(
                    'account-tags' => array('vcard-qrcode')
                ),
            );

            $Links[] = array(
                'id' => $SignatureID,
                'label' => Signatures::GetNameForOptionsArray($signature),
                // href and title are used in DD editor to insert a link
                // the qrcode will not be inserted, customAddon must be used
                'href' => "%Link:vCardURL({$SignatureID})%",
                'title' => t('Download the vCard.'),
                'plain' => "%Link:vCardURL({$SignatureID})%",
                'ckeditor' => [
                    // the html used in rich emails
                    'html' => $vCardLink,
                    // there is no difference between on and off ckeditor, nothing to toggle
                    'on' => '',
                    'off' => '',
                ],
                'group' => array(
                    'vcard-links' => array('vcard-links')
                ),
            );

            $CustomAddons[] = array(
                'id' => $SignatureID,
                'component' => 'vcard',
                'label' => Signatures::GetNameForOptionsArray($signature),
                // html that is inserted by the customAddOn
                'html' => $qrCodeHtml,
                // the customAddOn dialog has a button to copy only the link to the vcard
                'button_link' => "%Link:vCardURL({$SignatureID})%",
            );
        }

        // --- group: misc-tags, category: countdowns

        $query = "SELECT * FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID AND ToolType = :ToolType AND Deleted = :NotDeleted ORDER BY Name";
        $params = array(
            ':RelOwnerUserID' => $account->uid,
            ':ToolType' => MarketingTools::TOOL_TYPE_COUNTDOWN,
            ':NotDeleted' => MarketingTools::TOOL_DELETE_STATUS_NOTDELETED,
        );

        $result = db_query($query, $params);

        while ($countdown = kt_fetch_array($result)) {
            $objCountdown = ToolCountdown::FromArray($countdown);

            if (!$objCountdown) {
                continue;
            }

            $toolID = $objCountdown->GetData('ToolID');
            $previewImage = $objCountdown->getPreviewImage();

            //placeholder for plain text
            $placeholder = "%Link:ClockLinkedImage($toolID)%";
            $html = '<span data-clock="1" style="">' . $placeholder . '</span>';

            $Tags[] = array(
                'id' => $toolID,
                'label' => $countdown['Name'],
                'placeholder' => t('Please use the countown component to insert a countdown.'),
                'plain' => $placeholder,
                'ckeditor' => [
                    // the html used in rich emails
                    'html' => $html,
                    // when ckeditor is on, replace off value (that exists in html)
                    'on' => '<img data-id="' . $toolID . '" src="' . $previewImage . '"/>',
                    // when ckeditor is off, replace on value with off value (equals html used in email)
                    'off' => $placeholder,
                ],
                'group' => array(
                    'misc-tags' => array('countdowns')
                ),
            );

            $CustomAddons[] = array(
                'id' => $toolID,
                'component' => 'countdown',
                'label' => $countdown['Name'],
                // the customAddOn will add the preview image
                'html' => $html,
                // the customAddOn dialog has no button
                'button_link' => "",
            );
        }

        // --- group: misc-tags, category: plugins

        $Tags = array_merge($Tags, ToolPluginGeneral::GetCustomFields($account->uid, 'ddeditor-tags', $EmailType));
        $Links = array_merge($Links, ToolPluginGeneral::GetCustomFields($account->uid, 'ddeditor-links', $EmailType));
        $CustomAddons = array_merge(
            $CustomAddons,
            ToolPluginGeneral::GetCustomFields($account->uid, 'ddeditor-addons', $EmailType)
        );

        // --- group: misc-tags, category: invoice

        if ($EmailType == Emails::TYPE_AUTOMATIONEMAIL) {
            //Note: only automation emails can be used as invoice emails

            $invoiceTags = array(
                '%Invoice:Amount%',
                '%Invoice:GrossAmount%',
                '%Invoice:NetAmount%',
                '%Invoice:TaxAmount%',
                '%Invoice:Currency%',
                '%Invoice:ReceiptID%',
                '%Invoice:CreatedOn%',
            );

            foreach ($invoiceTags as $tag) {
                $Tags[] = array(
                    'label' => t(/*ignore*/ Personalization::$LanguageAliasesForInvoiceTags[$tag]),
                    'placeholder' => $tag,
                    'plain' => $tag,
                    'ckeditor' => [
                        'html' => $tag,
                        // no difference between on and off ckeditor, nothing to toggle
                        'on' => '',
                        'off' => '',
                    ],
                    'group' => array(
                        'misc-tags' => array('invoice')
                    ),
                );
            }
        }

        // --- group: system-links, category: system-links

        $systemlinks = array(
            '%Link:SubscriberInfo%' => t('Request information about the data collected from you.'),
            '%Link:SubscriberUpdate%' => t('Update your information.'),
            '%Link:ChangeEmailAddress%' => t('Change your email address.'),
            '%User:AffiliateURL%' => t('E-Mail Marketing by Klick-Tipp'),
            '%Link:WebBrowser%' => t('Open this email in your web browser.'),
            '%Link:NoTrack(URL)%' => t('The description of your link.'),
        );

        if ($EmailType == Emails::TYPE_CONFIRMATION) {
            $systemlinks['%Link:Confirm%'] = t('Confirm subscription');
        } else {
            $systemlinks['%Link:Unsubscribe%'] = t('I do not wish to receive further emails.');
        }

        $ckeditorLinks = Personalization::GetPersonalizationHtmlLinkTags($account->uid);

        foreach ($systemlinks as $link => $title) {
            $Links[] = array(
                'label' => t(/*ignore*/ Personalization::$LanguageAliasesForLinkTags[$link]),
                'href' => $link,
                'title' => $title,
                'group' => array(
                    'system-links' => array('system-links')
                ),
                'plain' => $link,
                'ckeditor' => [
                    'html' => $ckeditorLinks['LinkReplacements'][$link],
                    // no difference between on and off ckeditor, nothing to toggle
                    'on' => '',
                    'off' => '',
                ],
            );
        }

        // --- group: wufoo-links, category: wufoo-links

        $query = "SELECT BuildID, Name FROM {listbuilding} WHERE RelOwnerUserID = :RelOwnerUserID AND BuildType = :BuildType ORDER BY Name ASC";
        $result = db_query($query, array(':RelOwnerUserID' => $account->uid, ':BuildType' => Listbuildings::TYPE_WUFOO));

        while ($Wufoo = kt_fetch_array($result)) {
            $ArrayQueryParameters = array(
                'BuildID' => $Wufoo['BuildID'],
                'UserID' => $account->uid,
            );
            $apikey = Core::EncryptURL($ArrayQueryParameters, 'api_key');
            $link = "%Link:Application%/wufoo/$apikey/%Subscriber:Secret%";

            $Links[] = array(
                'label' => $Wufoo['Name'],
                'href' => $link,
                'title' => t('Fill out this Wufoo Form.'),
                'group' => array(
                    'wufoo-links' => array('wufoo-links')
                ),
                'plain' => $link,
                'ckeditor' => [
                    'html' => $link,
                    // no difference between on and off ckeditor, nothing to toggle
                    'on' => '',
                    'off' => '',
                ],
            );
        }

        // --- group: landing-page-links, category: landing-page-links

        foreach (LandingPage::getOfUser($account->uid) as $landingPage) {
            if (empty($landingPage->getFullDomain())) {
                // don't show links to landing pages without a domain
                continue;
            }
            //placeholder for plain text
            $id = $landingPage->GetData('BuildID');
            $placeholder = "%Link:LandingPage($id)%";

            $Links[] = array(
                'label' => $landingPage->GetData('Name'),
                'href' => $placeholder,
                'title' => t('Visit my landing page'),
                'group' => array(
                    'landing-page-links' => array('landing-page-links')
                ),
                'plain' => $placeholder,
                'ckeditor' => [
                    'html' => t(/*ignore*/
                        self::$LanguageAliasesForLinkTagsHTML['!LandingPage'],
                        array('!LandingPage' => $placeholder)
                    ),
                    // no difference between on and off ckeditor, nothing to toggle
                    'on' => '',
                    'off' => '',
                ]
            );
        }

        return array(
            'groups' => $Groups,
            'categories' => $Categories,
            'tags' => $Tags,
            'links' => $Links,
            'customaddons' => $CustomAddons,
        );
    }

    /**
    * @return array<PersonalizationResponseEditorConditionOptionValueObject>
     */
    public static function getEditorConditionOptions(): array
    {
        return [
            PersonalizationResponseEditorConditionOptionValueObject::create(
                Tag::class,
                t('condition::Manual Tag'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                TagCategorySmartLink::class,
                t('condition::SmartLink'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                CampaignsProcessFlow::class,
                t('condition::Automation'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                EmailsAutomationEmail::class,
                t('condition::Emails (Automation)'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                EmailsAutomationSMS::class,
                t('condition::SMS (Automation)'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                CampaignsNewsletter::class,
                t('condition::Newsletter/Autoresponder'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                Requests::class,
                t('condition::Request By Email'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                SMSListbuildings::class,
                t('condition::Request By SMS'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                APIKey::class,
                t('condition::API Key'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                BusinessCardReader::class,
                t('condition::Business Card Reader'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                Event::class,
                t('condition::Business Card Reader Event'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                SubscriptionFormsCustom::class,
                t('condition::Subscription form'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                PaymentIPNs::class,
                t('condition::Product'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                PaymentRefund::class,
                t('condition::Refund'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                PaymentChargeback::class,
                t('condition::Chargeback'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                PaymentSubsequent::class,
                t('condition::Subsequent Payment'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                PaymentDeferred::class,
                t('condition::Deferred Payment'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                PaymentRebill::class,
                t('condition::Rebill'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                PaymentRebillStatus::class,
                t('condition::Rebill Status'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                PaymentAffiliation::class,
                t('condition::Digistore Affiliate'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                ToolOutbound::class,
                t('condition::Outbound'),
                'tagging'
            ),
            PersonalizationResponseEditorConditionOptionValueObject::create(
                LandingPage::class,
                t('condition::Landing Page'),
                'tagging'
            ),
        ];
    }

    public static function GetEditorConditionData($UserID, $OptionClass)
    {
        if (!class_exists($OptionClass) || !method_exists($OptionClass, 'GetConditionData')) {
            return array();
        }

        return $OptionClass::GetConditionData($UserID);
    }

    public static function GetEditorConditionTimeframes()
    {
        return array(
            array(
                'id' => 'anytime',
                'label' => t('condition::at any time'),
                'value' => 0
            ),
            array(
                'id' => '24h',
                'label' => t('condition::within last 24 hours'),
                'value' => 24 * 60 * 60
            ),
            array(
                'id' => '3d',
                'label' => t('condition::within last 3 days'),
                'value' => 3 * 24 * 60 * 60
            ),
            array(
                'id' => '30d',
                'label' => t('condition::within last 30 days'),
                'value' => 30 * 24 * 60 * 60
            ),
            array(
                'id' => '90d',
                'label' => t('condition::within last 90 days'),
                'value' => 90 * 24 * 60 * 60
            ),
            array(
                'id' => '1y',
                'label' => t('condition::within last year'),
                'value' => 364 * 24 * 60 * 60
            ),
        );
    }

    /*
     * PersonalizeEmail
     *  Adds Signatures, Tracking and other Add-Ons and replaces all %-Parameters by the User/Subscriber-Data
     *
     * This is a performance critical function, so the idea is to not fetch, what we dont need - like this:
     *  1. Prepare the email parts itself
     *  2. Extract all %-parameters from the email parts
     *  3. Cache results from 1+2, so several emails from the same campaign should do this only once
     *  3. Get the Replacements values of all %-parameters used and cache user values as emails from the same campaign have the same user
     *  4. Do the Replacements with str_replace
     *
     * Be aware of call parameters, as not all values need to be given:
     *  $account: the user object loaded with user_load
     *  $ArrayEmail: we need all data, but do cache results
     *  $ArraySignature: we need all data, but do cache results
     *  $ArraySubscription: only subscription fields with no custom field values
     */
    public static function PersonalizeEmail(
        $account,
        $CampaignID,
        $ArrayEmail,
        $ArraySignature,
        $FullSubscriber,
        $ReferenceID,
        $SenderDomain,
        $PaymentData = [],
        $ReportSpamEnabled = false,
        $IsPreview = false,
        $IsConfirmation = false,
        $ListFormID = 0,
        $ListID = 0,
        $SendTransactional = false
    ) {
        /* prepare and cache email data */
        static $EmailData;

        if (empty($EmailData[$ArrayEmail['EmailID']])) {
            // adjust content type
            $ContentType = Emails::CONTENT_TYPE_PLAIN;
            if (!empty($ArrayEmail['HTMLContent'])) {
                $ContentType = $ArrayEmail['ContentTypeEnum'];
            }

            // get email revision
            $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

            $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['Subject']);

            if ($ContentType == Emails::CONTENT_TYPE_PLAIN) {
                $Decisions = [];
                $ContentHTML = '';
                $ContentPlain = $ArrayEmail['PlainContent'];
                $Params = Personalization::PersonalizeExtractSingle($ContentPlain, $Params);
            } else {
                $Decisions = Emails::GetDecisions($ArrayEmail);
                $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent'], $Params);
                $ContentPlain = trim($ArrayEmail['PlainContent']);

                // this case can occur with old editor only
                if (!empty($ContentPlain)) {
                    $Params = Personalization::PersonalizeExtractSingle($ContentPlain, $Params);
                }
                // plain content can be empty with both new and old editor
                // if there are decision (new editor only), we have to calculate $ContentPlain for each subscriber separately (see some lines below)
                elseif (!$Decisions) {
                    Libraries::include("htmlfilter.inc");
                    $ContentPlain = _klicktipp_html_to_text($ArrayEmail['HTMLContent']);
                    // we don't need to call PersonalizeExtractSingle, because all parameters are contained in HTMLContent already
                }
                // if there are decision, we have to calculate $ContentPlain for each subscriber separately (see some lines below)
                $ContentHTML = $Decisions ? null : $ArrayEmail['HTMLContent'];
            };

            // cache (campaign only)
            if (!$IsConfirmation) {
                $EmailData[$ArrayEmail['EmailID']] = array(
                    $ContentPlain,
                    $ContentHTML,
                    $Params,
                    $ContentType,
                    $ArrayRevision,
                    $Decisions
                );
            }
        } else {
            // get cache
            list($ContentPlain, $ContentHTML, $Params, $ContentType, $ArrayRevision, $Decisions) = $EmailData[$ArrayEmail['EmailID']];
        }

        // now we calculate html by subscriber decisions (new editor only). Since plain text for new editor is always calculated from html,
        // we have to calculate it either
        if ($Decisions) {
            $ContentHTML = Personalization::ResolveEmailDecisions(
                $ArrayEmail,
                $FullSubscriber['SubscriberID'],
                $ReferenceID,
                $Decisions
            );
            Libraries::include("htmlfilter.inc");
            $ContentPlain = _klicktipp_html_to_text($ContentHTML);
        }

        $useTransactionalSignature = false;
        if ($IsConfirmation) {
            $ContentPlain = Personalization::PersonalizePrepareContent(
                $ContentPlain,
                $Params,
                $ArrayRevision,
                [],
                false,
                true,
                false,
                '',
                $ArrayEmail['Version']
            );
            if ($ContentType != Emails::CONTENT_TYPE_PLAIN) {
                // html
                $ContentHTML = Personalization::PersonalizePrepareContent(
                    $ContentHTML,
                    $Params,
                    $ArrayRevision,
                    [],
                    false,
                    false,
                    false,
                    '',
                    $ArrayEmail['Version']
                );
            }
        } else {
            // add signature to plain content
            // add signature, report spam links to html content

            //turn off link tracking for preview emails or if the user wants to
            $TurnOffTrackLink = ($IsPreview || $ArrayEmail['TurnOffTrackLink']);

            $useTransactionalSignature = static::useTransactionalSignature($SendTransactional, $ArraySignature);
            if ($useTransactionalSignature) {
                Libraries::include("htmlfilter.inc");
                $ArraySignature['HTMLSignatureText'] = $ArraySignature['TransactionalHTMLSignatureText'];
                $ArraySignature['PlainSignatureText'] = _klicktipp_html_to_text(
                    $ArraySignature['TransactionalHTMLSignatureText']
                );
                // links

                $ArraySignature['HTMLSignature'] = $ArraySignature['PlainSignature'] = $ArraySignature['TransactionalHTMLSignature'];
            }

            // plain always
            $ContentPlain = Personalization::PersonalizePrepareContent(
                $ContentPlain,
                $Params,
                $ArrayRevision,
                $ArraySignature,
                $TurnOffTrackLink,
                true,
                false,
                '',
                $ArrayEmail['Version'],
                $useTransactionalSignature
            );
            $Params = Personalization::PersonalizeExtractSingle($ArraySignature['PlainSignatureText'], $Params);

            if ($ContentType != Emails::CONTENT_TYPE_PLAIN) {
                // html
                $ReportSpamPreviewText = trim($ArrayEmail['ReportSpamPreview']);
                $ContentHTML = Personalization::PersonalizePrepareContent(
                    $ContentHTML,
                    $Params,
                    $ArrayRevision,
                    $ArraySignature,
                    $TurnOffTrackLink,
                    false,
                    $ReportSpamEnabled,
                    $ReportSpamPreviewText,
                    $ArrayEmail['Version'],
                    $useTransactionalSignature
                );
                $Params = Personalization::PersonalizeExtractSingle($ArraySignature['HTMLSignatureText'], $Params);
            }
        }

        // get parameter Replacements
        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $account,
            $ArrayEmail,
            $CampaignID,
            $FullSubscriber,
            $ReferenceID,
            $IsPreview,
            $SenderDomain,
            $PaymentData,
            $ListFormID,
            false,
            $ListID,
            $useTransactionalSignature
        );

        // personalize email parts
        //Note: %System:HTMLLinebreak% is a system placeholder added after every linebreak (\n)
        //      in Paragraph CustomField Values @see Personalization::PersonalizeAssign()
        //      For plain content, the placeholder is replaced with an empty string
        //      For html content, the placeholder ist replaced with a <br />, to force a linebreak as well
        $Replacements['%System:HTMLLinebreak%'] = '';
        $TMPSubject = Personalization::PersonalizeReplace($ArrayEmail['Subject'], $Replacements);
        $TMPPlainBody = Personalization::PersonalizeReplace($ContentPlain, $Replacements);
        if ($ContentType == Emails::CONTENT_TYPE_PLAIN) {
            $TMPHTMLBody = '';
        } else {
            $Replacements['%System:HTMLLinebreak%'] = '<br />';
            $Replacements['%Email:Subject%'] = $TMPSubject;
            $TMPHTMLBody = Personalization::PersonalizeReplace($ContentHTML, $Replacements);
        }

        return array(
            $TMPSubject,
            $TMPPlainBody,
            $TMPHTMLBody,
            $ContentType
        );
    }

    /**
     * PersonalizeSMS
     *  Adds Tracking and other Add-Ons and replaces all %-Parameters by the User/Subscriber-Data
     *
     * @param $account
     * @param $CampaignID
     * @param $ArrayEmail
     * @param $FullSubscriber
     * @param $ReferenceID
     * @param bool $IsPreview
     *
     * @return mixed|string|string[]|null
     */
    public static function PersonalizeSMS(
        $account,
        $CampaignID,
        $ArrayEmail,
        $FullSubscriber,
        $ReferenceID,
        $IsPreview = false
    ) {
        /* prepare and cache email data */
        static $EmailData;

        if (empty($EmailData[$ArrayEmail['EmailID']])) {
            // get email revision
            $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);

            $Params = array();

            $ContentPlain = $ArrayEmail['PlainContent'];
            $Params = Personalization::PersonalizeExtractSingle($ContentPlain, $Params);

            $EmailData[$ArrayEmail['EmailID']] = array(
                $ContentPlain,
                $Params,
                $ArrayRevision
            );
        } else {
            // get cache
            list($ContentPlain, $Params, $ArrayRevision) = $EmailData[$ArrayEmail['EmailID']];
        }

        // prepare tracking links
        $ContentPlain = Personalization::PersonalizePrepareContent(
            $ContentPlain,
            $Params,
            $ArrayRevision,
            array(),
            $ArrayEmail['TurnOffTrackLink']
        );

        // get parameter Replacements
        $SenderDomain = DomainSet::SelectSenderDomain();
        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $account,
            $ArrayEmail,
            $CampaignID,
            $FullSubscriber,
            $ReferenceID,
            $IsPreview,
            $SenderDomain,
            [],
            0,
            true
        );

        // personalize email parts
        $TMPPlainBody = Personalization::PersonalizeReplace($ContentPlain, $Replacements);

        return $TMPPlainBody;
    }

    public static function PersonalizePrepareContent(
        $Content,
        &$Params,
        $ArrayRevision,
        $ArraySignature,
        $TurnOffTrackLink = 0,
        $IsPlain = true,
        $ReportSpamEnabled = false,
        $ReportSpamPreviewText = '',
        $Version = 0,
        $SendTransactional = false
    ) {
        // replace NULL from database
        $Content = empty($Content) ? '' : $Content;

        // add signature
        if (!empty($ArraySignature)) {
            if (empty($Version)) {
                //old email type

                if ($IsPlain) {
                    $Content .= "\n\n" . $ArraySignature['PlainSignatureText'];
                } else {
                    $Content .= "<br /><br />\n" . $ArraySignature['HTMLSignatureText'] . "\n\n";
                }
            } else {
                //new email type
                //the signature will be inserted for the placeholder %User:Signature%
                //Note: %User:Signature% is not required, if it is not present, the signature
                //will not be inserted. All required signature fields are in the email content (validated).

                if (Emails::DetectTagInContent($Content, '%User:Signature%')) {
                    if ($IsPlain) {
                        $Content = str_replace('%User:Signature%', $ArraySignature['PlainSignatureText'], $Content);
                    } else {
                        $Content = str_replace('%User:Signature%', $ArraySignature['HTMLSignatureText'], $Content);
                    }
                }
            }
        }

        // add pre-header, spam report links and tracking pixel (for html campaign mails only)
        if (!$IsPlain) {
            $ktheader = '';
            if ($ReportSpamPreviewText) {
                $ktheader .= '<div class="preheader" style="display:none !important;visibility:hidden;mso-hide:all;' .
                    'opacity:0;color:transparent;height:0;width:0;max-height:0;max-width:0;overflow:hidden;">' .
                    $ReportSpamPreviewText . '</div>';
            }
            if ($ReportSpamEnabled) {
                // get texts
                $SpamReportText = variable_get('klicktipp_spamreport_text', 'Report as spam');
                $UnsubscribeText = variable_get('klicktipp_spamreport_unsubscribe', 'Unsubscribe');
                $BrowserViewText = variable_get('klicktipp_spamreport_browserview', 'View E-Mail in Web-Browser');

                $GmailSeparator = trim(variable_get('klicktipp_spamreport_separator', ''));
                if (!empty($GmailSeparator)) {
                    $GmailSeparator = "<br /><br />" . $GmailSeparator;
                }

                $ktheader .= '<span class="ktmailheader">' . "\n" .
                    '<a href="%Link:WebBrowser%">' . $BrowserViewText .
                    '</a>' . "\n" .
                    ' &#124; ' .
                    '<a href="%Link:Unsubscribe%">' . $UnsubscribeText . '</a>' . "\n" .
                    ' &#124; ' .
                    '<a href="%Link:ReportSpam%">' . $SpamReportText . '</a>' . "\n" .
                    '</span>' . "\n" .
                    $GmailSeparator . "\n" .
                    '<br /><br />' . "\n\n";
            }

            if ($ktheader) {
                $count = 0;
                if (strpos($Content, '<body') !== false) {
                    // if a body tag is present, place the header right after it
                    // Note: the Bee Editor generates full html, putting our header right at the beginning,
                    //       mobile styles won't work
                    // RegEx: after '<body' match 0 or more of not '>' characters until '>'
                    $Content = preg_replace('/<body[^>]*>/', "$0$ktheader", $Content, 1, $count);
                }
                if (!$count) {
                    // fallback if no replacement took place
                    $Content = $ktheader . $Content;
                }

                $Params[] = '%Link:WebBrowser%';
                $Params[] = '%Link:Unsubscribe%';
                $Params[] = '%Link:ReportSpam%';
            }

            // append tracking pixel
            $Content .= "\n<img src=\"%Link:TrackOpen%\" width=\"1\" height=\"1\" alt=\"\" />\n\n";
            $Params[] = '%Link:TrackOpen%';
        }

        // plugin content injections (e.g. d+d editor preview replacements)
        if (strpos($Content, '%PluginField:') !== false) {
            // inject value of %PluginField:<plugin id>:<field id>%
            $Content = Plugin::inject_placeholder_replacements($Content, $Params);
        }

        // no tracking links
        // because the no-tracking-link may contain parameters itself, we need to do the replacement right now
        if (stripos($Content, '%Link:NoTrack') !== false) {
            // Replaces "%Link:NoTrack(URL-with-%Parameter%)%" by "URL-with-%Parameter%".
            // If there is "%Link:NoTrack(URL)" and "URL" in the source, we need to protect the no-tracking link to be encoded by the second rule.
            // So we actually put "%Link:NoTrack(http://www.example.com)%" into "LinkNoTrackLinkNoTracktp://www.example.com" and finish it later (see PersonalizeAssign).
            //Note: even if the customer turned off link tracking, there could be %Link:NoTrack(URL)% links in the content -> remove the placeholder
            $Content = preg_replace('/%Link:NoTrack\(ht([^\)]*)\)%/', 'LinkNoTrackLinkNoTrack$1', $Content);
        }

        if (empty($TurnOffTrackLink)) {
            // set link tracking if the customer did not turn it off in the email dialog (via a checkbox)

            if (!empty($ArrayRevision)) {
                if ($IsPlain) {
                    $ArrayContent = unserialize($ArrayRevision['PlainContent']);
                    $Content = Personalization::PersonalizeLinkByRevision(
                        $Content,
                        $ArrayContent,
                        'track_link',
                        '0',
                        $ArrayRevision['RevisionID'],
                        $Params
                    );
                } else {
                    $ArrayContent = unserialize($ArrayRevision['HTMLContent']);
                    $Content = Personalization::PersonalizeLinkByRevision(
                        $Content,
                        $ArrayContent,
                        'track_link',
                        '1',
                        $ArrayRevision['RevisionID'],
                        $Params
                    );
                }
            }
            if (!empty($ArraySignature)) {
                if ($IsPlain) {
                    $ArrayContent = unserialize($ArraySignature['PlainSignature']);
                    if ($SendTransactional) {
                        // in transactional signature we have only 1 field (for html actualy)
                        // but format of plain links differs from html ones
                        array_walk($ArrayContent, fn(&$link) => $link['FullLink'] = $link['Link']);
                    }
                    $Content = Personalization::PersonalizeLinkByRevision(
                        $Content,
                        $ArrayContent,
                        'track_sign_link',
                        '0',
                        $ArraySignature['RevisionID'],
                        $Params,
                        $SendTransactional
                    );
                } else {
                    $ArrayContent = unserialize($ArraySignature['HTMLSignature']);
                    $Content = Personalization::PersonalizeLinkByRevision(
                        $Content,
                        $ArrayContent,
                        'track_sign_link',
                        '1',
                        $ArraySignature['RevisionID'],
                        $Params,
                        $SendTransactional
                    );
                }
            }
        }

        return $Content;
    }

    //Note: This function won't be called if the customer turned off link tracking for a given email
    //@see: PersonalizeEmail() and PersonalizePrepareContent()
    public static function PersonalizeLinkByRevision(
        $Content,
        $ArrayContent,
        $code,
        $html,
        $revision,
        &$Params,
        $SendTransactional = false
    ) {
        $ArrayContent = $ArrayContent ?: [];
        // sort links by length: Longer strings shall be applied first, so we wont destroy long links by smaller substrings.
        uasort($ArrayContent, function ($link1, $link2) {
            return strlen($link2['Link']) - strlen($link1['Link']);
        });

        // this prepares tracking links for every link in revision
        foreach ($ArrayContent as $id => $ArrayLink) {
            $link = $ArrayLink['Link'];
            $isSystemLink = preg_match('/%Link:(?!ClockLink).*%/', $link);
            $isAffiliateUrl = strcasecmp('%User:AffiliateURL%', $link) === 0;
            $isAnchorLink = !empty($link) && $link[0] === '#';

            if (!$isSystemLink && !$isAffiliateUrl && !$isAnchorLink) {
                // encode parameters for easy decoding in PersonalizeAssign
                $p = "%Tracking-{$code}-{$html}-{$revision}-{$id}-" . intval($SendTransactional) . "-link%";
                $count = 0;
                $Content = str_replace(trim($ArrayLink['FullLink']), $p, $Content, $count);
                if ($count > 0) {
                    $Params[] = $p;
                }
            }
        }
        return $Content;
    }

    public static function PersonalizeExtractSingle($String, $Params = array())
    {
        if (
            preg_match_all(
                '/%Subscriber:[^%]+%|%User:[^%]+%|%Invoice:[^%]+%|%Link:[^%]+%|%Tracking-[^%]+%|%Data[^%]+%|%PluginField:[^%]+%/U',
                $String,
                $matches
            ) > 0
        ) {
            $Params = array_merge($Params, $matches[0]);
        }
        return $Params;
    }

    /**
     * @param $Params
     * @param $account
     * @param $ArrayEmail
     * @param $CampaignID
     * @param $FullSubscriber (better is FullSubscriberWithFields, but it will check for fields)
     * @param $ReferenceID
     * @param $IsPreview
     * @param $SenderDomain
     * @param array $PaymentData
     * @param string $ListFormID
     * @param bool $ShortLinks
     * @param int $ListID
     *
     * @return array
     */
    public static function PersonalizeAssign(
        $Params,
        $account,
        $ArrayEmail,
        $CampaignID,
        $FullSubscriber,
        $ReferenceID,
        $IsPreview,
        $SenderDomain,
        $PaymentData = [],
        $ListFormID = '',
        $ShortLinks = false,
        $ListID = 0,
        $SendTransactional = false
    ) {
        $Replacements = array();

        // make all parameters to keys
        $Params = array_fill_keys($Params, '');

        // we use the account as an array here
        if (!is_array($account)) {
            $account = (array)$account;
        }

        // all dates in default timezone (we may use subscriber timezone in the future)
        Dates::setTimezone();

        // all texts in user language
        klicktipp_set_language($account);

        /* prepare and cache user data */
        static $UserData;

        if (empty($UserData[$account['uid']])) {
            // get all simply fields at once
            $Replacements['%User:FirstName%'] = $account['FirstName'];
            $Replacements['%User:LastName%'] = $account['LastName'];
            $Replacements['%User:EmailAddress%'] = Subscribers::DepunycodeEmailAddress($account['mail']);
            $Replacements['%User:CompanyName%'] = $account['CompanyName'];
            $Replacements['%User:Website%'] = $account['Website'];
            $Replacements['%User:Street%'] = $account['Street'];
            $Replacements['%User:City%'] = $account['City'];
            $Replacements['%User:State%'] = $account['State'];
            $Replacements['%User:Zip%'] = $account['Zip'];
            $Replacements['%User:Phone%'] = $account['Phone'];
            $Replacements['%User:Fax%'] = $account['Fax'];
            //Note: we don't offer the %User:TimeZone% to the user nowhere in the application
            //      $account['dst'] is also an object and cannot be translated
            //$Replacements['%User:TimeZone%'] = t(/*ignore*/$account['dst']);
            $Replacements['%User:KlickTippAddress%'] = $account['mail'];

            $CustomFieldsOfUser = CustomFields::RetrieveCustomFields(
                $account['uid'],
                true,
                array('CustomFieldID' => 'ASC'),
                true
            );
            $MarketingTools = array();
            $Signatures = array();

            // cache
            $UserData[$account['uid']] = array(
                $Replacements,
                $CustomFieldsOfUser,
                $MarketingTools,
                $Signatures
            );
        } else {
            // get cache
            [$Replacements, $CustomFieldsOfUser, $MarketingTools, $Signatures] = $UserData[$account['uid']];
        }

        // get more complex (user) fields on request only
        if (isset($Params['%User:Country%']) && !isset($Replacements['%User:Country%'])) {
            $Replacements['%User:Country%'] = t(/*ignore*/ Personalization::PersonalizeGetCountry($account['Country']));
            $UserData[$account['uid']] = array(
                $Replacements,
                $CustomFieldsOfUser,
                $MarketingTools,
                $Signatures
            );
        }

        if (isset($Params['%User:AffiliateID%']) && !isset($Replacements['%User:AffiliateID%'])) {
            $Replacements['%User:AffiliateID%'] = _klicktipp_get_amemberid($account['name'], $account);
            $UserData[$account['uid']] = array(
                $Replacements,
                $CustomFieldsOfUser,
                $MarketingTools,
                $Signatures
            );
        }


        if (isset($Params['%User:AffiliateURL%']) && !isset($Replacements['%User:AffiliateURL%'])) {
            // prevent unnecessary amember requests
            $Replacements['%User:AffiliateID%'] ??= _klicktipp_get_amemberid($account['name'], $account);
            $Replacements['%User:AffiliateURL%'] = variable_get(
                'amember_affiliate_url',
                'http://www.klick-tipp.com/'
            ) . $Replacements['%User:AffiliateID%'];
            $UserData[$account['uid']] = array(
                $Replacements,
                $CustomFieldsOfUser,
                $MarketingTools,
                $Signatures
            );
        }

        $CachedReplacements = $Replacements;

        // prepare Subscriber
        foreach ($FullSubscriber as $key => $value) {
            $field = '%Subscriber:' . $key . '%';
            if (isset($Params[$field])) {
                if (
                    in_array($key, array(
                        // Email
                        'OptInDate',
                        'SubscriptionDate',
                        'UnsubscriptionDate',
                        // SMS
                        'SMSSubscriptionDate',
                        'SMSUnsubscriptionDate',
                    ))
                ) {
                    $Replacements[$field] = Dates::formatDate(Dates::FORMAT_DMY, (int) $value);
                } elseif ($key === 'EmailAddress') {
                    $Replacements[$field] = Subscribers::DepunycodeEmailAddress($value);
                } elseif (substr($key, 0, 11) == 'CustomField') {
                    $CustomFieldInformation = $CustomFieldsOfUser[substr($key, 11)];
                    if (!empty($CustomFieldInformation)) {
                        $Replacements[$field] = CustomFields::FormatCustomFieldData($CustomFieldInformation, $value);
                        if ($CustomFieldInformation['FieldTypeEnum'] == CustomFields::TYPE_PARAGRAPH) {
                            //the linebreaks of multiline values (only paragrahp, not type html) should be applied in html emails also.
                            //add %System:HTMLLinebreak% before every \n, which will be replaced with
                            //<br /> for html emails and '' for plain emails, @see PersonalizeEmail
                            $Replacements[$field] = str_replace(
                                "\n",
                                "%System:HTMLLinebreak%\n",
                                $Replacements[$field]
                            );
                        }
                    }
                } elseif ($key === 'SubscriptionReferrer' && $ArrayEmail['EmailType'] != Emails::TYPE_PRIVACYEMAIL) {
                    //we do not want to insert the SubscriptionReferrer URL into the email anymore
                    //since it could contain a black listed URL
                    $Replacements[$field] = t('Subscribed by Subscription Form');
                } else {
                    $Replacements[$field] = $value;
                }
            }
        }

        // custom fields
        foreach ($CustomFieldsOfUser as $key => $CustomFieldInformation) {
            // hidden fields have placeholder format %Subscriber:$key%
            $field = ($CustomFieldInformation['IsHidden']) ? "%Subscriber:$key%" : "%Subscriber:CustomField$key%";
            // if requested and not already set
            if (isset($Params[$field]) && !isset($Replacements[$field])) {
                $value = CustomFields::GetCustomFieldData(
                    $account['uid'],
                    $FullSubscriber['SubscriberID'],
                    $CustomFieldInformation,
                    $ReferenceID
                );
                $Replacements[$field] = CustomFields::FormatCustomFieldData($CustomFieldInformation, $value);

                // One may notice, that this section is duplicated several lines above. This is, because things got pretty inconsistent in here
                // If one passes CustomFields visa $FullSubscriber (see the methods argument list), the field is handled by the block above
                // and this block is completely skipped (`!isset($Replacements[$field])`). On the other hand, if the custom fields are missing
                // in `$FullSubscriber`, this block covers custom field handling.
                // Both approaches are in use, the latter one at least for transactional mails.
                if ($CustomFieldInformation['FieldTypeEnum'] == CustomFields::TYPE_PARAGRAPH) {
                    //the linebreaks of multiline values (only paragrahp, not type html) should be applied in html emails also.
                    //add %System:HTMLLinebreak% before every \n, which will be replaced with
                    //<br /> for html emails and '' for plain emails, @see PersonalizeEmail
                    $Replacements[$field] = str_replace("\n", "%System:HTMLLinebreak%\n", $Replacements[$field]);
                }
            }
        }

        $field = '%Subscriber:Secret%';
        if (isset($Params[$field])) {
            $ArrayQueryParameters = array(
                'UserID' => $account['uid'],
                'SubscriberID' => $FullSubscriber['SubscriberID'],
            );
            $Replacements[$field] = Core::EncryptURL($ArrayQueryParameters, 'subscriber_secret');
        }

        $field = '%Subscriber:SubscriberKey%';
        if (isset($Params[$field])) {
            $ArrayQueryParameters = array(
                'UserID' => $account['uid'],
                'SubscriberID' => $FullSubscriber['SubscriberID'],
                'ReferenceID' => $ReferenceID,
            );
            $Replacements[$field] = Core::EncryptURL($ArrayQueryParameters, 'subscriber_key');
        }

        // payment
        if (isset($Params['%Invoice:Amount%'])) {
            $Replacements['%Invoice:Amount%'] = klicktipp_number_format(intval($PaymentData['Amount']) / 100, 2);
        }
        if (isset($Params['%Invoice:Currency%']) && !empty($PaymentData['Currency'])) {
            $Replacements['%Invoice:Currency%'] = $PaymentData['Currency'];
        }
        if (isset($Params['%Invoice:GrossAmount%'])) {
            $Replacements['%Invoice:GrossAmount%'] = klicktipp_number_format(
                intval($PaymentData['Payment']['GrossAmount']) / 100,
                2
            );
        }
        if (isset($Params['%Invoice:NetAmount%'])) {
            $Replacements['%Invoice:NetAmount%'] = klicktipp_number_format(
                intval($PaymentData['Payment']['NetAmount']) / 100,
                2
            );
        }
        if (isset($Params['%Invoice:TaxAmount%'])) {
            $Replacements['%Invoice:TaxAmount%'] = klicktipp_number_format(
                intval($PaymentData['Payment']['TaxAmount']) / 100,
                2
            );
        }
        if (isset($Params['%Invoice:ReceiptID%']) && !empty($PaymentData['Payment']['ReceiptID'])) {
            $Replacements['%Invoice:ReceiptID%'] = $PaymentData['Payment']['ReceiptID'];
        }
        if (isset($Params['%Invoice:CreatedOn%']) && !empty($PaymentData['Payment']['CreatedOn'])) {
            $Replacements['%Invoice:CreatedOn%'] = Dates::formatDate(Dates::FORMAT_DMY, (int) $PaymentData['Payment']['CreatedOn']);
        }

        // create links
        $ArrayQueryParameters = array(
            'EmailID' => $ArrayEmail['EmailID'],
            'CampaignID' => $CampaignID,
            'SubscriberID' => $FullSubscriber['SubscriberID'],
            'ListID' => $ListID,
            'Preview' => $IsPreview ? '1' : '',
            'ListFormID' => $ListFormID,
            'EmailAddress' => $FullSubscriber['EmailAddress'],
            'UserID' => $account['uid'],
            'ShortLink' => $ShortLinks,
            'HistoryID' => empty($PaymentData['HistoryID']) ? 0 : $PaymentData['HistoryID'],
            'ReferenceID' => $ReferenceID,
            'SendTransactional' => $SendTransactional
        );

        // links from user content
        $depunycodedAppUrl = $SenderDomain['appurl'];
        if (isset($Params['%Link:Application%'])) {
            $Replacements['%Link:Application%'] = rtrim($depunycodedAppUrl, '/');
        } // APP_URL without slash
        if (isset($Params['%Link:Confirm%'])) {
            $Replacements['%Link:Confirm%'] = Core::EncryptURL(
                $ArrayQueryParameters,
                'opt_confirm',
                $depunycodedAppUrl,
                $IsPreview
            );
        }
        if (isset($Params['%Link:Unsubscribe%'])) {
            $Replacements['%Link:Unsubscribe%'] = Core::EncryptURL(
                $ArrayQueryParameters,
                'unsubscribe',
                $depunycodedAppUrl,
                $IsPreview
            );
        }
        if (isset($Params['%Link:SubscriberInfo%'])) {
            $Replacements['%Link:SubscriberInfo%'] = Core::EncryptURL(
                $ArrayQueryParameters,
                'subscriber_info',
                $depunycodedAppUrl,
                $IsPreview
            );
        }
        if (isset($Params['%Link:SubscriberUpdate%'])) {
            $Replacements['%Link:SubscriberUpdate%'] = Core::EncryptURL(
                $ArrayQueryParameters,
                'subscriber_update',
                $depunycodedAppUrl,
                $IsPreview
            );
        }
        if (isset($Params['%Link:WebBrowser%'])) {
            $Replacements['%Link:WebBrowser%'] = Core::EncryptURL(
                $ArrayQueryParameters,
                'web_browser',
                $depunycodedAppUrl
            );
        }
        if (isset($Params['%Link:SubscriberArea%'])) {
            $Replacements['%Link:SubscriberArea%'] = '';
        } // not (yet) supported
        if (isset($Params['%Link:ChangeEmailAddress%'])) {
            $Replacements['%Link:ChangeEmailAddress%'] = Core::EncryptURL(
                $ArrayQueryParameters,
                'change_email',
                $depunycodedAppUrl,
                $IsPreview
            );
        }

        // auto generated links
        if (isset($Params['%Link:ReportSpam%'])) {
            $Replacements['%Link:ReportSpam%'] = Core::EncryptURL(
                $ArrayQueryParameters,
                'spam_report',
                $depunycodedAppUrl,
                $IsPreview
            );
        }
        if (isset($Params['%Link:TrackOpen%'])) {
            $Replacements['%Link:TrackOpen%'] = Core::EncryptURL(
                $ArrayQueryParameters,
                'track_open',
                $depunycodedAppUrl,
                $IsPreview
            );
        }

        // tracking links are coded in PersonalizeLinkByRevision, so decoding is easy here
        // "%Tracking-{$code}-{$html}-{$revisionid}-{$linknumber}-link%"

        foreach ($Params as $key => $v) {
            if (strpos($key, '%Tracking') === 0) {
                [, $code, $html, $revisionid, $linknumber, $transactional] = explode('-', $key);
                $ArrayQueryParameters['HTML'] = $transactional ? Personalization::SIGNATURE_LINK_SOURCE_TRANSACTIONAL : ($html == '1' ? '1' : '');
                $ArrayQueryParameters['RevisionID'] = $revisionid;
                $ArrayQueryParameters['Linknumber'] = $linknumber;
                if ($html == '1') {
                    $Replacements[$key] = 'href="' . Core::EncryptURL(
                        $ArrayQueryParameters,
                        $code,
                        $depunycodedAppUrl
                    ) . '"';
                } else {
                    $Replacements[$key] = Core::EncryptURL($ArrayQueryParameters, $code, $depunycodedAppUrl);
                }
            } elseif (strpos($key, '%Link:ClockImage') === 0 || strpos($key, '%Link:ClockLinkedImage') === 0) {
                //Countdown Tool

                //get ToolID by removing all non-numeric chars, %Link:ClockLink(4711)% => 4711
                $ToolID = preg_replace('|[^0-9]|', '', $key);

                //add ToolID to encrypt parameters
                $ArrayQueryParameters['ToolID'] = $ToolID;

                if (!isset($MarketingTools[$ToolID])) {
                    $c = ToolCountdown::FromID_IncludeDeleted($account['uid'], $ToolID);
                    if (!empty($c)) {
                        // this may happen, if the email has been imported from another account

                        $MarketingTools[$ToolID] = $c->GetData();

                        // update cache
                        $UserData[$account['uid']] = array(
                            $CachedReplacements,
                            $CustomFieldsOfUser,
                            $MarketingTools,
                            $Signatures
                        );
                    }
                }

                if ($IsPreview) {
                    $preview_image = Settings::get(
                        'marketing_tools_emailcountdown_s3url'
                    ) . "/TEMPLATE/1/TEMPLATE_120.gif";
                    $clockURL = str_replace('TEMPLATE', $MarketingTools[$ToolID]['Template'], $preview_image);
                    $infoURL = $MarketingTools[$ToolID]['TargetURL'];
                } else {
                    $clockURL = Core::EncryptURL($ArrayQueryParameters, 'clock', $depunycodedAppUrl);
                    $infoURL = Core::EncryptURL($ArrayQueryParameters, 'info_url', $depunycodedAppUrl);
                }

                if (strpos($key, '%Link:ClockLinkedImage') === 0) {
                    if (!isset($MarketingTools[$ToolID])) {
                        $Replacements[$key] = '';
                    } elseif ($ArrayEmail['ContentTypeEnum'] == Emails::CONTENT_TYPE_PLAIN) {
                        if (empty($MarketingTools[$ToolID]['TargetURL'])) {
                            $Replacements[$key] = $clockURL;
                        } else {
                            //if a target URL is set, insert it instead of the image URL
                            $Replacements[$key] = $infoURL;
                        }
                    } else {
                        $Replacements[$key] = '<img src="' . $clockURL . '" />';

                        if (!empty($MarketingTools[$ToolID]['TargetURL'])) {
                            //only include an a-tag if a target URL is set
                            $Replacements[$key] = '<a href="' . $infoURL . '">' . $Replacements[$key] . '</a>';
                        }
                    }
                } else {
                    //deprecated
                    //old countdown placeholders looked like this: <img src="%Link:ClockImage(4711)%"/>

                    if (!isset($MarketingTools[$ToolID])) {
                        $Replacements[$key] = '';
                    } else {
                        $Replacements[$key] = $clockURL;
                    }
                }
            } elseif (strpos($key, '%Link:vCard') === 0) {
                // get SignatureID by removing all non-numeric chars, %Link:vCard(4711)% => 4711
                $SignatureID = preg_replace('|[^0-9]|', '', $key);
                $ArrayQueryParameters['SignatureID'] = $SignatureID;
                if (!isset($Signatures[$SignatureID])) {
                    // retrieve and cache signature
                    $Signature = Signatures::RetrieveSignatureByID($account['uid'], $SignatureID);
                    if (!empty($Signature)) {
                        $Signatures[$SignatureID] = $Signature;
                        // update cache
                        $UserData[$account['uid']] = array(
                            $CachedReplacements,
                            $CustomFieldsOfUser,
                            $MarketingTools,
                            $Signatures
                        );
                    }
                }

                $url = Core::EncryptURL(
                    $ArrayQueryParameters,
                    'signature_vcard',
                    $depunycodedAppUrl,
                    $IsPreview
                );
                if (!isset($Signatures[$SignatureID]) || empty(array_filter($Signatures[$SignatureID]['vCard']))) {
                    $Replacements[$key] = '';
                } elseif ($ArrayEmail['ContentTypeEnum'] == Emails::CONTENT_TYPE_PLAIN) {
                    $Replacements[$key] = $url;
                } else {
                    if (strpos($key, '%Link:vCardURL') === 0) {
                        //the placeholder in the editor inserts a text with a link to the vCard
                        $Replacements[$key] = $url;
                    } else {
                        $vCard = Signatures::GenerateVCard($Signatures[$SignatureID]);
                        $QRCodeLink = Signatures::GenerateVCardUrl($vCard);
                        $Replacements[$key] = "<a href=\"$url\"><img src=\"$QRCodeLink\" width=\"200\" height=\"200\" /></a>";
                    }
                }
            } elseif (strpos($key, '%Link:LandingPage') === 0) {
                // get LandingPage id  by removing all non-numeric chars, %Link:LandingPage(4711)% => 4711
                $id = preg_replace('|[^0-9]|', '', $key);
                $landingPage = LandingPage::FromID($account['uid'], $id);
                if ($landingPage instanceof LandingPage) {
                    $url = $landingPage->getFullDomain();
                    if (!empty($url)) {
                        // TODO: add subscriberKey (not MVP)
                        $Replacements[$key] = "https://$url";
                    }
                }
            } elseif (strpos($key, '%Link:Redirect') === 0) {
                // get ToolID by removing all non-numeric chars, %Link:Redirect(4711)% => 4711
                $ToolID = preg_replace('|[^0-9]|', '', $key);
                // add ToolID to encrypt parameters
                $ArrayQueryParameters['ToolID'] = $ToolID;
                $Replacements[$key] = Core::EncryptURL($ArrayQueryParameters, 'plugin_redirect_params', $depunycodedAppUrl, $IsPreview);
            } elseif (strpos($key, '%Subscriber:CustomFieldPlugin:') === 0) {
                // value of %Subscriber:CustomFieldPlugin:Link-4711% has key Link-4711 in {custom_field_values}
                if ($IsPreview) {
                    $ArrayAvailablePluginTags = Personalization::GetPersonalizationPluginTags(
                        $account['uid'],
                        'preview'
                    );
                    $value = $ArrayAvailablePluginTags[$key];
                } else {
                    // get 'Link-4711' from $key
                    $entitykey = Plugin::unwrap_key('Subscriber:CustomFieldPlugin:', $key);
                    // get 'Link' from $key and update field information with subtype data
                    [$subtype,] = explode('-', $entitykey);
                    $ArrayFieldInformation = array_merge(
                        $CustomFieldsOfUser['Plugin'],
                        CustomFields::$GlobalCustomFieldArrayDefs[$subtype]
                    );
                    // get and format value
                    $value = CustomFields::GetCustomFieldData(
                        $account['uid'],
                        $FullSubscriber['SubscriberID'],
                        CustomFields::$GlobalCustomFieldDefs['Plugin'],
                        $ReferenceID,
                        $entitykey
                    );
                    $value = CustomFields::FormatCustomFieldData($ArrayFieldInformation, $value);
                }
                $Replacements[$key] = $value;
            }
        }

        // add %Link:NoTrack replacement (see PersonalizePrepareContent)
        // put "LinkNoTrackLinkNoTracktp://www.example.com" into "http://www.example.com"
        $Replacements['LinkNoTrackLinkNoTracktp'] = 'http';

        // replace all not found % parameters by blank
        foreach ($Params as $key => $v) {
            if (!isset($Replacements[$key])) {
                $Replacements[$key] = '';
            }
        }

        return $Replacements;
    }

    public static function PersonalizeReplace($Content, $Replacements)
    {
        // replace parameters
        $Content = str_replace(array_keys($Replacements), array_values($Replacements), $Content);

        // avoid single lines with just a a single point (means SMTP end of email)
        $Content = preg_replace('/^\./m', ' .', $Content);

        return $Content;
    }

    public static function PersonalizeGetCountry($country)
    {
        if ($pos = strpos(PERSONALIZATION_ISO3166, $country)) {
            if ($closing = strpos(PERSONALIZATION_ISO3166, '"', $pos + 4)) {
                return substr(PERSONALIZATION_ISO3166, $pos + 4, $closing - $pos - 4);
            }
        }

        return '';
    }

    /**
     * Return the email's HTML content with decisions resolved
     * @param array $ArrayEmail
     * @param int $SubscriberID
     * @poram int $ReferenceID
     * @param array $decisions if not set (not extracted outside) will be extracted by the methode
     *
     * @return string
     */
    public static function ResolveEmailDecisions($ArrayEmail, $SubscriberID, $ReferenceID, array $decisions = null)
    {
        if ($decisions === null) {
            $decisions = Emails::GetDecisions($ArrayEmail);
        }

        if (empty($decisions)) {
            return $ArrayEmail['HTMLContent'];
        }

        $ContentHTML = $ArrayEmail['HTMLContent'];
        $UserID = $ArrayEmail['RelUserID'];

        foreach ($decisions as $decision) {
            if (!CampaignsProcessFlow::CheckDecision($decision['decision'], $UserID, $SubscriberID, $ReferenceID)) {
                //decision is FALSE, remove the content inside the decision enclosure

                $id = $decision['id'];

                //create regex for decision enclosure
                //<!--DECISION_START($id)-->
                //<p>Content from email editor...</p>
                //<!--DECISION_END($id)-->
                $regex = "|<!--DECISION_START" . preg_quote("($id)") . "-->";
                $regex .= '[\s\S]*?';
                $regex .= "<!--DECISION_END" . preg_quote("($id)") . "-->|i";

                //replace decision enclosure with nothing
                $resolvedContent = preg_replace($regex, '', $ContentHTML);

                if (isset($resolvedContent)) {
                    //on error preg_replace returns NULL
                    //make sure we do not return empty email content
                    $ContentHTML = $resolvedContent;
                }
            }
            //else: condition is TRUE, keep the content in the email
        }

        return $ContentHTML;
    }

    private static function useTransactionalSignature(bool $isSendModeTransactional, ?array $signature): bool
    {
        return $isSendModeTransactional &&
            !empty($signature['UseInTransactionalEmails']) &&
            !empty($signature['TransactionalHTMLSignatureText']);
    }
}

/* ISO 3166 country codes
 *  from http://dev.maxmind.com/geoip/legacy/codes/iso3166/
 *  this is the data geoip uses to return country names, so we need only one version to translate
 */
define(
    'PERSONALIZATION_ISO3166',
    <<<COUNTRIES
A1,"Anonymous Proxy"
A2,"Satellite Provider"
O1,"Other Country"
AD,"Andorra"
AE,"United Arab Emirates"
AF,"Afghanistan"
AG,"Antigua and Barbuda"
AI,"Anguilla"
AL,"Albania"
AM,"Armenia"
AO,"Angola"
AP,"Asia/Pacific Region"
AQ,"Antarctica"
AR,"Argentina"
AS,"American Samoa"
AT,"Austria"
AU,"Australia"
AW,"Aruba"
AX,"Aland Islands"
AZ,"Azerbaijan"
BA,"Bosnia and Herzegovina"
BB,"Barbados"
BD,"Bangladesh"
BE,"Belgium"
BF,"Burkina Faso"
BG,"Bulgaria"
BH,"Bahrain"
BI,"Burundi"
BJ,"Benin"
BL,"Saint Bartelemey"
BM,"Bermuda"
BN,"Brunei Darussalam"
BO,"Bolivia"
BQ,"Bonaire, Saint Eustatius and Saba"
BR,"Brazil"
BS,"Bahamas"
BT,"Bhutan"
BV,"Bouvet Island"
BW,"Botswana"
BY,"Belarus"
BZ,"Belize"
CA,"Canada"
CC,"Cocos (Keeling) Islands"
CD,"Congo, The Democratic Republic of the"
CF,"Central African Republic"
CG,"Congo"
CH,"Switzerland"
CI,"Cote d'Ivoire"
CK,"Cook Islands"
CL,"Chile"
CM,"Cameroon"
CN,"China"
CO,"Colombia"
CR,"Costa Rica"
CU,"Cuba"
CV,"Cape Verde"
CW,"Curacao"
CX,"Christmas Island"
CY,"Cyprus"
CZ,"Czech Republic"
DE,"Germany"
DJ,"Djibouti"
DK,"Denmark"
DM,"Dominica"
DO,"Dominican Republic"
DZ,"Algeria"
EC,"Ecuador"
EE,"Estonia"
EG,"Egypt"
EH,"Western Sahara"
ER,"Eritrea"
ES,"Spain"
ET,"Ethiopia"
EU,"Europe"
FI,"Finland"
FJ,"Fiji"
FK,"Falkland Islands (Malvinas)"
FM,"Micronesia, Federated States of"
FO,"Faroe Islands"
FR,"France"
GA,"Gabon"
GB,"United Kingdom"
GD,"Grenada"
GE,"Georgia"
GF,"French Guiana"
GG,"Guernsey"
GH,"Ghana"
GI,"Gibraltar"
GL,"Greenland"
GM,"Gambia"
GN,"Guinea"
GP,"Guadeloupe"
GQ,"Equatorial Guinea"
GR,"Greece"
GS,"South Georgia and the South Sandwich Islands"
GT,"Guatemala"
GU,"Guam"
GW,"Guinea-Bissau"
GY,"Guyana"
HK,"Hong Kong"
HM,"Heard Island and McDonald Islands"
HN,"Honduras"
HR,"Croatia"
HT,"Haiti"
HU,"Hungary"
ID,"Indonesia"
IE,"Ireland"
IL,"Israel"
IM,"Isle of Man"
IN,"India"
IO,"British Indian Ocean Territory"
IQ,"Iraq"
IR,"Iran, Islamic Republic of"
IS,"Iceland"
IT,"Italy"
JE,"Jersey"
JM,"Jamaica"
JO,"Jordan"
JP,"Japan"
KE,"Kenya"
KG,"Kyrgyzstan"
KH,"Cambodia"
KI,"Kiribati"
KM,"Comoros"
KN,"Saint Kitts and Nevis"
KP,"Korea, Democratic People's Republic of"
KR,"Korea, Republic of"
KW,"Kuwait"
KY,"Cayman Islands"
KZ,"Kazakhstan"
LA,"Lao People's Democratic Republic"
LB,"Lebanon"
LC,"Saint Lucia"
LI,"Liechtenstein"
LK,"Sri Lanka"
LR,"Liberia"
LS,"Lesotho"
LT,"Lithuania"
LU,"Luxembourg"
LV,"Latvia"
LY,"Libyan Arab Jamahiriya"
MA,"Morocco"
MC,"Monaco"
MD,"Moldova, Republic of"
ME,"Montenegro"
MF,"Saint Martin"
MG,"Madagascar"
MH,"Marshall Islands"
MK,"Macedonia"
ML,"Mali"
MM,"Myanmar"
MN,"Mongolia"
MO,"Macao"
MP,"Northern Mariana Islands"
MQ,"Martinique"
MR,"Mauritania"
MS,"Montserrat"
MT,"Malta"
MU,"Mauritius"
MV,"Maldives"
MW,"Malawi"
MX,"Mexico"
MY,"Malaysia"
MZ,"Mozambique"
NA,"Namibia"
NC,"New Caledonia"
NE,"Niger"
NF,"Norfolk Island"
NG,"Nigeria"
NI,"Nicaragua"
NL,"Netherlands"
NO,"Norway"
NP,"Nepal"
NR,"Nauru"
NU,"Niue"
NZ,"New Zealand"
OM,"Oman"
PA,"Panama"
PE,"Peru"
PF,"French Polynesia"
PG,"Papua New Guinea"
PH,"Philippines"
PK,"Pakistan"
PL,"Poland"
PM,"Saint Pierre and Miquelon"
PN,"Pitcairn"
PR,"Puerto Rico"
PS,"Palestinian Territory"
PT,"Portugal"
PW,"Palau"
PY,"Paraguay"
QA,"Qatar"
RE,"Reunion"
RO,"Romania"
RS,"Serbia"
RU,"Russian Federation"
RW,"Rwanda"
SA,"Saudi Arabia"
SB,"Solomon Islands"
SC,"Seychelles"
SD,"Sudan"
SE,"Sweden"
SG,"Singapore"
SH,"Saint Helena"
SI,"Slovenia"
SJ,"Svalbard and Jan Mayen"
SK,"Slovakia"
SL,"Sierra Leone"
SM,"San Marino"
SN,"Senegal"
SO,"Somalia"
SR,"Suriname"
SS,"South Sudan"
ST,"Sao Tome and Principe"
SV,"El Salvador"
SX,"Sint Maarten"
SY,"Syrian Arab Republic"
SZ,"Swaziland"
TC,"Turks and Caicos Islands"
TD,"Chad"
TF,"French Southern Territories"
TG,"Togo"
TH,"Thailand"
TJ,"Tajikistan"
TK,"Tokelau"
TL,"Timor-Leste"
TM,"Turkmenistan"
TN,"Tunisia"
TO,"Tonga"
TR,"Turkey"
TT,"Trinidad and Tobago"
TV,"Tuvalu"
TW,"Taiwan"
TZ,"Tanzania, United Republic of"
UA,"Ukraine"
UG,"Uganda"
UM,"United States Minor Outlying Islands"
US,"United States"
UY,"Uruguay"
UZ,"Uzbekistan"
VA,"Holy See (Vatican City State)"
VC,"Saint Vincent and the Grenadines"
VE,"Venezuela"
VG,"Virgin Islands, British"
VI,"Virgin Islands, U.S."
VN,"Vietnam"
VU,"Vanuatu"
WF,"Wallis and Futuna"
WS,"Samoa"
YE,"Yemen"
YT,"Mayotte"
ZA,"South Africa"
ZM,"Zambia"
ZW,"Zimbabwe"
COUNTRIES
);

/* country translations, needed for translation parser
t("Anonymous Proxy");
t("Satellite Provider");
t("Other Country");
t("Andorra");
t("United Arab Emirates");
t("Afghanistan");
t("Antigua and Barbuda");
t("Anguilla");
t("Albania");
t("Armenia");
t("Angola");
t("Asia/Pacific Region");
t("Antarctica");
t("Argentina");
t("American Samoa");
t("Austria");
t("Australia");
t("Aruba");
t("Aland Islands");
t("Azerbaijan");
t("Bosnia and Herzegovina");
t("Barbados");
t("Bangladesh");
t("Belgium");
t("Burkina Faso");
t("Bulgaria");
t("Bahrain");
t("Burundi");
t("Benin");
t("Saint Bartelemey");
t("Bermuda");
t("Brunei Darussalam");
t("Bolivia");
t("Bonaire, Saint Eustatius and Saba");
t("Brazil");
t("Bahamas");
t("Bhutan");
t("Bouvet Island");
t("Botswana");
t("Belarus");
t("Belize");
t("Canada");
t("Cocos (Keeling) Islands");
t("Congo, The Democratic Republic of the");
t("Central African Republic");
t("Congo");
t("Switzerland");
t("Cote d'Ivoire");
t("Cook Islands");
t("Chile");
t("Cameroon");
t("China");
t("Colombia");
t("Costa Rica");
t("Cuba");
t("Cape Verde");
t("Curacao");
t("Christmas Island");
t("Cyprus");
t("Czech Republic");
t("Germany");
t("Djibouti");
t("Denmark");
t("Dominica");
t("Dominican Republic");
t("Algeria");
t("Ecuador");
t("Estonia");
t("Egypt");
t("Western Sahara");
t("Eritrea");
t("Spain");
t("Ethiopia");
t("Europe");
t("Finland");
t("Fiji");
t("Falkland Islands (Malvinas)");
t("Micronesia, Federated States of");
t("Faroe Islands");
t("France");
t("Gabon");
t("United Kingdom");
t("Grenada");
t("Georgia");
t("French Guiana");
t("Guernsey");
t("Ghana");
t("Gibraltar");
t("Greenland");
t("Gambia");
t("Guinea");
t("Guadeloupe");
t("Equatorial Guinea");
t("Greece");
t("South Georgia and the South Sandwich Islands");
t("Guatemala");
t("Guam");
t("Guinea-Bissau");
t("Guyana");
t("Hong Kong");
t("Heard Island and McDonald Islands");
t("Honduras");
t("Croatia");
t("Haiti");
t("Hungary");
t("Indonesia");
t("Ireland");
t("Israel");
t("Isle of Man");
t("India");
t("British Indian Ocean Territory");
t("Iraq");
t("Iran, Islamic Republic of");
t("Iceland");
t("Italy");
t("Jersey");
t("Jamaica");
t("Jordan");
t("Japan");
t("Kenya");
t("Kyrgyzstan");
t("Cambodia");
t("Kiribati");
t("Comoros");
t("Saint Kitts and Nevis");
t("Korea, Democratic People's Republic of");
t("Korea, Republic of");
t("Kuwait");
t("Cayman Islands");
t("Kazakhstan");
t("Lao People's Democratic Republic");
t("Lebanon");
t("Saint Lucia");
t("Liechtenstein");
t("Sri Lanka");
t("Liberia");
t("Lesotho");
t("Lithuania");
t("Luxembourg");
t("Latvia");
t("Libyan Arab Jamahiriya");
t("Morocco");
t("Monaco");
t("Moldova, Republic of");
t("Montenegro");
t("Saint Martin");
t("Madagascar");
t("Marshall Islands");
t("Macedonia");
t("Mali");
t("Myanmar");
t("Mongolia");
t("Macao");
t("Northern Mariana Islands");
t("Martinique");
t("Mauritania");
t("Montserrat");
t("Malta");
t("Mauritius");
t("Maldives");
t("Malawi");
t("Mexico");
t("Malaysia");
t("Mozambique");
t("Namibia");
t("New Caledonia");
t("Niger");
t("Norfolk Island");
t("Nigeria");
t("Nicaragua");
t("Netherlands");
t("Norway");
t("Nepal");
t("Nauru");
t("Niue");
t("New Zealand");
t("Oman");
t("Panama");
t("Peru");
t("French Polynesia");
t("Papua New Guinea");
t("Philippines");
t("Pakistan");
t("Poland");
t("Saint Pierre and Miquelon");
t("Pitcairn");
t("Puerto Rico");
t("Palestinian Territory");
t("Portugal");
t("Palau");
t("Paraguay");
t("Qatar");
t("Reunion");
t("Romania");
t("Serbia");
t("Russian Federation");
t("Rwanda");
t("Saudi Arabia");
t("Solomon Islands");
t("Seychelles");
t("Sudan");
t("Sweden");
t("Singapore");
t("Saint Helena");
t("Slovenia");
t("Svalbard and Jan Mayen");
t("Slovakia");
t("Sierra Leone");
t("San Marino");
t("Senegal");
t("Somalia");
t("Suriname");
t("South Sudan");
t("Sao Tome and Principe");
t("El Salvador");
t("Sint Maarten");
t("Syrian Arab Republic");
t("Swaziland");
t("Turks and Caicos Islands");
t("Chad");
t("French Southern Territories");
t("Togo");
t("Thailand");
t("Tajikistan");
t("Tokelau");
t("Timor-Leste");
t("Turkmenistan");
t("Tunisia");
t("Tonga");
t("Turkey");
t("Trinidad and Tobago");
t("Tuvalu");
t("Taiwan");
t("Tanzania, United Republic of");
t("Ukraine");
t("Uganda");
t("United States Minor Outlying Islands");
t("United States");
t("Uruguay");
t("Uzbekistan");
t("Holy See (Vatican City State)");
t("Saint Vincent and the Grenadines");
t("Venezuela");
t("Virgin Islands, British");
t("Virgin Islands, U.S.");
t("Vietnam");
t("Vanuatu");
t("Wallis and Futuna");
t("Samoa");
t("Yemen");
t("Mayotte");
t("South Africa");
t("Zambia");
t("Zimbabwe");
*/

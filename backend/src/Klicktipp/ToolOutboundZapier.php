<?php

namespace App\Klicktipp;

/*
 * Marketing Tool subclass for Outbound Events
 */

class ToolOutboundZapier extends ToolOutbound
{
    public static $ToolType = MarketingTools::TOOL_TYPE_ZAPIER;

    public static $DefaultFields = array(
        'ToolType' => MarketingTools::TOOL_TYPE_ZAPIER,
        'Deleted' => 0,
        'AssignTagID' => 0,
    );

    public static $DefaultDataFields = array(
        'ActivationURL' => '',
        'ActivationTagID' => '',
        'ActivationSmartTagID' => 0,
    );

    public static $APIIndexFilterTypes = array(
        MarketingTools::TOOL_TYPE_ZAPIER => 'zapier',
    );

    public static function FromArray($DBArray)
    {
        $tool = new ToolOutboundZapier($DBArray);

        if (empty($tool->DataFlat)) {
            return false;
        }

        return $tool;
    }

    public static function InsertDB($Data)
    {
        $ToolID = parent::InsertDB($Data);
        if (empty($ToolID)) {
            return false;
        }

        // write all additional data with an update query (as we need the ToolID for that)

        $ToolObject = static::FromID($Data['RelOwnerUserID'], $ToolID);
        $Data = $ToolObject->GetData();

        $Data['ActivationSmartTagID'] = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_OUTBOUND,
            'EntityID' => $ToolID,
            'RelOwnerUserID' => $Data['RelOwnerUserID'],
        ));

        static::UpdateDB($Data);

        return $ToolID;
    }

    public static function UpdateDB($Data)
    {
        static::WriteOutboundEvents(
            $Data['RelOwnerUserID'],
            $Data['ToolID'],
            $Data['ActivationTagID'],
            static::EVENT_TYPE_ACTIVATION
        );

        return parent::UpdateDB($Data);
    }

    public static function DeleteDB($Data)
    {
        // make sure, we have all data available (not required for general DeleteDB)
        $ToolObject = static::FromID($Data['RelOwnerUserID'], $Data['ToolID']);
        if (!$ToolObject) {
            return false;
        }
        $Data = $ToolObject->GetData();

        $result = parent::DeleteDB($Data);
        if (!$result) {
            return false;
        }

        static::WriteOutboundEvents($Data['RelOwnerUserID'], $Data['ToolID']);

        Tag::DeleteTag($Data['ActivationSmartTagID'], $Data['RelOwnerUserID']);

        return $result;
    }

    // for zapier outbound always send all subscriber fields
    // use api field identifiers as keys
    public static function ConvertToApiKeys($UserID, $Subscriber)
    {
        // fill parameters to send
        $parameters = array();

        foreach (Subscribers::$PublicSubscriberFieldDefs as $fieldid => $field) {
            if ($fieldid == 'SubscriptionStatus') {
                $value = Subscribers::$DisplaySubscriptionStatus[$Subscriber[$fieldid]];
            } elseif ($fieldid == 'BounceType') {
                $value = Subscribers::$DisplayBounceTypes[$Subscriber[$fieldid]];
            } else {
                $value = $Subscriber[$fieldid];
            }

            $parameters[$field['ApiFieldIdentifier']] = empty($value) ? '' : $value;
        }

        $AllCustomFields = CustomFields::RetrieveCustomFields($UserID);

        foreach ($AllCustomFields as $CustomField) {
            $value = $Subscriber['CustomField' . $CustomField['CustomFieldID']];
            $parameters['field' . $CustomField['CustomFieldID']] = empty($value) ? '' : $value;
        }

        return $parameters;
    }

    /* object functions */

    public function ExecuteOutboundEvent($OutboundEvent, $FullSubscriberWithFields)
    {
        $ReferenceID = $OutboundEvent['ReferenceID'];

        // don't tag for Test Outbounds (user dialogue) as OutboundEvent will be empty
        // Note: this check caused big problems when done AFTER a timed out "CallOutbound" (see DEV-591)
        $called_from_test_form = !empty($OutboundEvent['test']);

        // fill parameters and send outbound request
        $response = $this->CallOutbound($FullSubscriberWithFields);

        // analyse results
        if (!$called_from_test_form) {
            // don't tag for Test Outbounds

            if (empty($response->error)) {
                Subscribers::TagSubscriber(
                    $OutboundEvent['RelOwnerUserID'],
                    $OutboundEvent['SubscriberID'],
                    $this->GetData('ActivationSmartTagID'),
                    $ReferenceID,
                    true
                );
                TransactionEmails::RegisterAutomations(
                    $OutboundEvent['RelOwnerUserID'],
                    $OutboundEvent['SubscriberID'],
                    $ReferenceID
                );
            } elseif ($response->error == 'Too Many Requests') {
                // requeue and log
                $LogID = ProcessLog::CreateSlicedQueueItems('outbound_event', [$OutboundEvent], 30);
                $error = array(
                    '!uid' => $OutboundEvent['RelOwnerUserID'],
                    '!data' => $OutboundEvent,
                    '!err' => $response->error,
                    '!request' => $response->request,
                    '!response' => print_r($response, 1),
                    '!logid' => $LogID,
                );
                Errors::typedUnexpected('zapier', 'Zapier Trigger failed (requeued): uid !uid !err', $error);
            } else {
                // invalid url?
                $error = array(
                    '!uid' => $OutboundEvent['RelOwnerUserID'],
                    '!data' => $OutboundEvent,
                    '!err' => $response->error,
                    '!request' => $response->request,
                    '!response' => print_r($response, 1),
                );
                Errors::typedUnexpected('zapier', "Zapier Trigger failed: uid !uid !err", $error, false);

                // inform customer
                Subscribers::WriteHistory(
                    $OutboundEvent['RelOwnerUserID'],
                    $OutboundEvent['SubscriberID'],
                    Subscribers::HISTORY_OUTBOUND_CALL_FAILED,
                    array(
                        '!event' => 'Zapier Trigger failed (@response)',
                        '!code' => $response->code,
                        '!error' => $response->error,
                        '@response' => $response->error,
                        'ReferenceID' => $ReferenceID
                    )
                );
            }
        }

        // used for test purposes
        return $response;
    }

    /**
     * send out outbound request to zapier
     * by default, all fields are sent to zapier
     * used for privacy dashboard outbound which is sent after subscriber deletion
     * @param $Subscriber
     * @return object
     */
    public function CallOutbound($FullSubscriberWithFields)
    {
        $UserID = $this->DataFlat['RelOwnerUserID'];
        // convert the field keys to the api field identifiers
        $parameters = static::ConvertToApiKeys($UserID, $FullSubscriberWithFields);
        // do the outbound request
        $url = $this->GetData('ActivationURL');
        $response = drupal_http_request($url, array(
            'headers' => array('Content-Type' => 'application/x-www-form-urlencoded'),
            'method' => 'POST',
            'data' => http_build_query($parameters, '', '&'),
        ));
        return $response;
    }

    public function GetEditURL($CampaignID = 0)
    {
        return APP_URL . "tools/{$this->DataFlat['RelOwnerUserID']}/zapier/{$this->DataFlat['ToolID']}/edit";
    }

    public static function GetAddURL($UserID, $data = '')
    {
        return APP_URL . "tools/$UserID/zapier/add";
    }

    public function GetSmartTags($forConditions = false)
    {
        if ($forConditions) {
            return array(
                'triggered' => $this->GetData('ActivationSmartTagID'),
            );
        }

        return array(
            'ActivationSmartTagID' => $this->GetData('ActivationSmartTagID'),
        );
    }

    public static function klicktipp_subscriber_dummy_subscriber()
    {
        $UserID = Subaccount::FindMainAccount();

        if (empty($UserID)) {
            drupal_not_found();
            exit;
        }

        return static::getDummySubscriber($UserID);
    }

    public static function getDummySubscriber(int $userId): array
    {
        $dummySubscriber = Subscribers::SelectDummySubscriber($userId);

        // use api field identifiers as keys
        return ToolOutboundZapier::ConvertToApiKeys($userId, $dummySubscriber);
    }
}

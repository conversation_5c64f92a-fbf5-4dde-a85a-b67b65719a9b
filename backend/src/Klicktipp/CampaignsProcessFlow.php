<?php

namespace App\Klicktipp;

use App\Klicktipp\AngularApi\ValueObject\ApiResponseLocalPathValueObject;
use App\Klicktipp\AngularApi\ValueObject\CreateButtonValueObject;
use App\Klicktipp\Automation\AutomationLinkResolver;
use App\Klicktipp\Email\EmailAutomation\EmailAutomationLinkResolver;
use App\Klicktipp\Email\EmailNotification\EmailNotificationLinkResolver;
use App\Klicktipp\Email\Overview\EmailOverviewLinkResolver;
use App\Klicktipp\Email\Overview\ValueObject\EmailOverviewResponseEntityValueObject;
use App\Klicktipp\Includes\UtilsNumber;
use App\Klicktipp\LandingPage\LandingPage;
use App\Klicktipp\Sms\SmsAutomation\SmsAutomationLinkResolver;
use App\Klicktipp\Sms\SmsNotification\SmsNotificationLinkResolver;
use Doctrine\DBAL\Exception;
use stdClass;

/**
 * ProcessFlow class
 *
 * NOTE: see corresponding client file app/klicktipp/campaign/campaign.ts
 *
 * @phpstan-import-type CampaignArray from Campaigns
 * @phpstan-import-type RecipientListArray from Campaigns
 * @phpstan-import-type State from TransactionEmails
 */
class CampaignsProcessFlow extends Campaigns
{
    public static $CampaignType = Campaigns::TRIGGER_TYPE_PROCESSFLOW;

    public static $UserGroupLimitField = 'LimitAutomations';

    const PROCESSFLOW_STATE_TYPE_START = 'start';
    const PROCESSFLOW_STATE_TYPE_EXIT = 'exit';
    const PROCESSFLOW_STATE_TYPE_RESTART = 'restart';
    const PROCESSFLOW_STATE_TYPE_DECISION = 'decision';
    const PROCESSFLOW_STATE_TYPE_GOAL = 'goal';
    const PROCESSFLOW_STATE_TYPE_ACTION = 'action'; // obsolete
    const PROCESSFLOW_STATE_TYPE_SENDEMAIL = 'email';
    const PROCESSFLOW_STATE_TYPE_SENDSMS = 'sms';
    const PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL = 'notify by email';
    const PROCESSFLOW_STATE_TYPE_NOTIFYSMS = 'notify by sms';
    const PROCESSFLOW_STATE_TYPE_OUTBOUND = 'outbound';
    const PROCESSFLOW_STATE_TYPE_TAGGING = 'tagging';
    const PROCESSFLOW_STATE_TYPE_UNTAGGING = 'untagging';
    const PROCESSFLOW_STATE_TYPE_UNSUBSCRIBE = 'unsubscribe';
    const PROCESSFLOW_STATE_TYPE_SETFIELD = 'setfield';
    const PROCESSFLOW_STATE_TYPE_WAIT = 'wait';
    const PROCESSFLOW_STATE_TYPE_GOTO = 'goto';
    const PROCESSFLOW_STATE_TYPE_STARTAUTOMATION = 'start automation';
    const PROCESSFLOW_STATE_TYPE_STOPAUTOMATION = 'stop automation';
    const PROCESSFLOW_STATE_TYPE_SPLITTEST = 'splittest';
    const PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD = 'facebook audience add';
    const PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_REMOVE = 'facebook audience remove';
    const PROCESSFLOW_STATE_TYPE_DETECT_NAME = 'detect name';
    const PROCESSFLOW_STATE_TYPE_DETECT_GENDER = 'detect gender';
    const PROCESSFLOW_STATE_TYPE_FULLCONTACT = 'fullcontact';

    const PROCESSFLOW_WAIT_TYPE_IMMEDIATELY = 'immediately'; // dont wait
    const PROCESSFLOW_WAIT_TYPE_SECONDS = 'seconds'; // wait for ... seconds
    const PROCESSFLOW_WAIT_TYPE_MINUTES = 'minutes';
    const PROCESSFLOW_WAIT_TYPE_HOURS = 'hours';
    const PROCESSFLOW_WAIT_TYPE_DAYS = 'days';
    const PROCESSFLOW_WAIT_TYPE_WEEKS = 'weeks';
    const PROCESSFLOW_WAIT_TYPE_MONTHS = 'months';
    const PROCESSFLOW_WAIT_TYPE_YEARS = 'years';
    const PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD = 'from field'; // wait until date from field
    const PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MINUTES = 'customfield minutes';
    const PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_HOURS = 'customfield hours';
    const PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_DAYS = 'customfield days';
    const PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_WEEKS = 'customfield weeks';
    const PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MONTHS = 'customfield months';
    const PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_YEARS = 'customfield years';
    const PROCESSFLOW_WAIT_TYPE_BIRTHDAY = 'birthday'; // wait until next birthday (from field birthday)
    const PROCESSFLOW_WAIT_TYPE_CALENDAR = 'calendar'; // wait until next holiday
    const PROCESSFLOW_WAIT_TYPE_LIMITER = 'limiter'; // wait for time constraint with max subscribers per period

    const PROCESSFLOW_CONDITION_IS_ACTIVE_EMAIL = 'is active email subscriber';
    const PROCESSFLOW_CONDITION_IS_NOT_ACTIVE_EMAIL = 'is not active email subscriber';
    const PROCESSFLOW_CONDITION_IS_ACTIVE_SMS = 'is active sms subscriber';
    const PROCESSFLOW_CONDITION_IS_NOT_ACTIVE_SMS = 'is not active sms subscriber';

    //general tagging
    const PROCESSFLOW_CONDITION_HAS_TAGGING = 'has tagging';
    const PROCESSFLOW_CONDITION_HAS_NOT_TAGGING = 'has not tagging';
    // manual tag
    const PROCESSFLOW_CONDITION_IS_TAGGED = 'tagged with';
    const PROCESSFLOW_CONDITION_IS_NOT_TAGGED = 'not tagged with';
    // smart link
    const PROCESSFLOW_CONDITION_SMARTLINK_CLICKED = 'smart link clicked';
    const PROCESSFLOW_CONDITION_SMARTLINK_NOT_CLICKED = 'smart link not clicked';
    // campaign (newsletter, autoresponder) tags
    const PROCESSFLOW_CONDITION_CAMPAIGN_SENT = 'campaign sent';
    const PROCESSFLOW_CONDITION_CAMPAIGN_NOT_SENT = 'campaign not sent';
    const PROCESSFLOW_CONDITION_CAMPAIGN_OPENED = 'campaign opened';
    const PROCESSFLOW_CONDITION_CAMPAIGN_NOT_OPENED = 'campaign not opened';
    const PROCESSFLOW_CONDITION_CAMPAIGN_CLICKED = 'campaign clicked';
    const PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CLICKED = 'campaign not clicked';
    const PROCESSFLOW_CONDITION_CAMPAIGN_VIEWED = 'campaign viewed';
    const PROCESSFLOW_CONDITION_CAMPAIGN_NOT_VIEWED = 'campaign not viewed';
    const PROCESSFLOW_CONDITION_CAMPAIGN_CONVERTED = 'campaign converted';
    const PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CONVERTED = 'campaign not converted';
    // tools
    const PROCESSFLOW_CONDITION_KAJABI_ACTIVATED = 'kajabi activated';
    const PROCESSFLOW_CONDITION_KAJABI_NOT_ACTIVATED = 'kajabi not activated';
    const PROCESSFLOW_CONDITION_KAJABI_DEACTIVATED = 'kajabi deactivated';
    const PROCESSFLOW_CONDITION_KAJABI_NOT_DEACTIVATED = 'kajabi not deactivated';
    const PROCESSFLOW_CONDITION_OUTBOUND_ACTIVATED = 'outbound activated';
    const PROCESSFLOW_CONDITION_OUTBOUND_NOT_ACTIVATED = 'outbound not activated';
    const PROCESSFLOW_CONDITION_TAGGING_PIXEL_ACTIVATED = 'tagging pixel activated';
    const PROCESSFLOW_CONDITION_TAGGING_PIXEL_NOT_ACTIVATED = 'tagging pixel not activated';
    // automation emails
    const PROCESSFLOW_CONDITION_EMAIL_SENT = 'email sent';
    const PROCESSFLOW_CONDITION_EMAIL_NOT_SENT = 'email not sent';
    const PROCESSFLOW_CONDITION_EMAIL_OPENED = 'email opened';
    const PROCESSFLOW_CONDITION_EMAIL_NOT_OPENED = 'email not opened';
    const PROCESSFLOW_CONDITION_EMAIL_CLICKED = 'email clicked';
    const PROCESSFLOW_CONDITION_EMAIL_NOT_CLICKED = 'email not clicked';
    const PROCESSFLOW_CONDITION_EMAIL_VIEWED = 'email viewed';
    const PROCESSFLOW_CONDITION_EMAIL_NOT_VIEWED = 'email not viewed';
    // automation sms
    const PROCESSFLOW_CONDITION_SMS_SENT = 'sms sent';
    const PROCESSFLOW_CONDITION_SMS_NOT_SENT = 'sms not sent';
    const PROCESSFLOW_CONDITION_SMS_CLICKED = 'sms clicked';
    const PROCESSFLOW_CONDITION_SMS_NOT_CLICKED = 'sms not clicked';
    // automations
    const PROCESSFLOW_CONDITION_AUTOMATION_STARTED = 'automation started';
    const PROCESSFLOW_CONDITION_AUTOMATION_NOT_STARTED = 'automation not started';
    const PROCESSFLOW_CONDITION_AUTOMATION_FINISHED = 'automation finished';
    const PROCESSFLOW_CONDITION_AUTOMATION_NOT_FINISHED = 'automation not finished';
    // listbuilding
    const PROCESSFLOW_CONDITION_LISTBUILDING_APIKEY = 'subscribed by apikey';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_APIKEY = 'not subscribed by apikey';
    const PROCESSFLOW_CONDITION_LISTBUILDING_BUSINESSCARD = 'subscribed by businesscard';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_BUSINESSCARD = 'not subscribed by businesscard';
    const PROCESSFLOW_CONDITION_LISTBUILDING_EVENT = 'subscribed by event';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_EVENT = 'not subscribed by event';
    const PROCESSFLOW_CONDITION_LISTBUILDING_REQUEST = 'subscribed by request';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_REQUEST = 'not subscribed by request';
    const PROCESSFLOW_CONDITION_LISTBUILDING_SMS = 'subscribed by SMS';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_SMS = 'not subscribed by SMS';
    const PROCESSFLOW_CONDITION_LISTBUILDING_FORMS = 'subscribed by form';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_FORMS = 'not subscribed by form';
    // payments
    const PROCESSFLOW_CONDITION_LISTBUILDING_PAYMENT = 'subscribed by payment';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_PAYMENT = 'not subscribed by payment';
    const PROCESSFLOW_CONDITION_LISTBUILDING_SUBSEQUENT_PAYMENT = 'subsequent payment';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOT_SUBSEQUENT_PAYMENT = 'not subsequent payment';
    const PROCESSFLOW_CONDITION_LISTBUILDING_DEFFERED_PAYMENT = 'deferred payment';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOT_DEFFERED_PAYMENT = 'not deferred payment';
    const PROCESSFLOW_CONDITION_LISTBUILDING_REFUNDED = 'payment refunded';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOT_REFUNDED = 'not payment refunded';
    const PROCESSFLOW_CONDITION_LISTBUILDING_CHARGEDBACK = 'payment charged back';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOT_CHARGEDBACK = 'not payment charged back';
    const PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_CANCELED = 'rebill canceled';
    const PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_CANCELED_LAST_DAY = 'rebill canceled last day';
    const PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_CANCELED = 'not rebill canceled';
    const PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_RESUMED = 'rebill resumed';
    const PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_RESUMED = 'not rebill resumed';
    const PROCESSFLOW_CONDITION_LISTBUILDING_DIGISTORE_AFFILIATION = 'digistore affiliate';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NO_DIGISTORE_AFFILIATION = 'no digistore affiliate';
    const PROCESSFLOW_CONDITION_LISTBUILDING_LANDING_PAGE_SUBSCRIBED = 'subscribed via landing page';
    const PROCESSFLOW_CONDITION_LISTBUILDING_NOT_LANDING_PAGE_SUBSCRIBED = 'not subscribed via landing page';
    const PROCESSFLOW_CONDITION_PAYMENT_COMPLETED = 'payment completed';
    const PROCESSFLOW_CONDITION_PAYMENT_EXPIRED = 'payment expired';
    // audiences
    const PROCESSFLOW_CONDITION_FACEBOOK_AUDIENCE = 'assigned to facebook audience';
    const PROCESSFLOW_CONDITION_NOT_FACEBOOK_AUDIENCE = 'not assigned to facebook audience';
    // plugins
    const PROCESSFLOW_CONDITION_PLUGIN_INBOUND_READY = 'plugin is ready';
    const PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_READY = 'plugin is not ready';
    const PROCESSFLOW_CONDITION_PLUGIN_INBOUND_STARTED = 'plugin is started';
    const PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_STARTED = 'plugin is not started';
    const PROCESSFLOW_CONDITION_PLUGIN_INBOUND_INPROGRESS = 'plugin is in progress';
    const PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_INPROGRESS = 'plugin is not in progress';
    const PROCESSFLOW_CONDITION_PLUGIN_INBOUND_FINISHED = 'plugin is finished';
    const PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_FINISHED = 'plugin is not finished';
    // fields
    const PROCESSFLOW_CONDITION_FIELD_IS_EMPTY = 'is empty';
    const PROCESSFLOW_CONDITION_FIELD_IS_NOT_EMPTY = 'is not empty';
    const PROCESSFLOW_CONDITION_FIELD_IS = 'is equal';
    const PROCESSFLOW_CONDITION_FIELD_IS_NOT = 'is not equal';
    const PROCESSFLOW_CONDITION_FIELD_CONTAINS = 'contains';
    const PROCESSFLOW_CONDITION_FIELD_CONTAINS_NOT = 'does not contain';
    const PROCESSFLOW_CONDITION_FIELD_STARTS_WITH = 'starts with';
    const PROCESSFLOW_CONDITION_FIELD_STARTS_NOT_WITH = 'does not start with';
    const PROCESSFLOW_CONDITION_FIELD_ENDS_WITH = 'ends with';
    const PROCESSFLOW_CONDITION_FIELD_ENDS_NOT_WITH = 'does not end with';
    const PROCESSFLOW_CONDITION_FIELD_LESS_THAN = 'is less than';
    const PROCESSFLOW_CONDITION_FIELD_LESS_EQUAL_THAN = 'is less or equal than';
    const PROCESSFLOW_CONDITION_FIELD_GREATER_THAN = 'is greater than';
    const PROCESSFLOW_CONDITION_FIELD_GREATER_EQUAL_THAN = 'is greater or equal than';
    const PROCESSFLOW_CONDITION_FIELD_BEFORE = 'is before';
    const PROCESSFLOW_CONDITION_FIELD_AFTER = 'is after';
    const PROCESSFLOW_CONDITION_FIELD_IS_WEEKDAY = 'is weekday';
    const PROCESSFLOW_CONDITION_FIELD_IS_MONTH = 'is month';
    const PROCESSFLOW_CONDITION_FIELD_IS_DAY_OF_MONTH = 'is day of month';
    const PROCESSFLOW_CONDITION_FIELD_IS_HOLIDAY = 'is holiday';
    const PROCESSFLOW_CONDITION_FIELD_BEFORE_TIME = 'is before time';
    const PROCESSFLOW_CONDITION_FIELD_AFTER_TIME = 'is after time';

    // datetime
    const PROCESSFLOW_CONDITION_TODAY_IS = 'today is equal';
    const PROCESSFLOW_CONDITION_TODAY_IS_NOT = 'today is not equal';
    const PROCESSFLOW_CONDITION_TODAY_BEFORE = 'today is before';
    const PROCESSFLOW_CONDITION_TODAY_AFTER = 'today is after';
    const PROCESSFLOW_CONDITION_TODAY_IS_WEEKDAY = 'today is weekday';
    const PROCESSFLOW_CONDITION_TODAY_IS_MONTH = 'today is month';
    const PROCESSFLOW_CONDITION_TODAY_IS_DAY_OF_MONTH = 'today is day of month';
    const PROCESSFLOW_CONDITION_TODAY_IS_HOLIDAY = 'today is holiday';
    const PROCESSFLOW_CONDITION_TODAY_BEFORE_TIME = 'today is before time';
    const PROCESSFLOW_CONDITION_TODAY_AFTER_TIME = 'today is after time';

    // user segments (only for marketing account)
    const PROCESSFLOW_CONDITION_SEGMENT_IN = 'is in segment';
    const PROCESSFLOW_CONDITION_SEGMENT_NOT_IN = 'is not in segment';

    // validation errors
    const PROCESSFLOW_VALIDATION_INFO_CONDITION_EMPTY = 'condition is empty'; // obsolete
    const PROCESSFLOW_VALIDATION_INFO_GOAL_CONDITION_EMPTY = 'goal condition is empty';
    const PROCESSFLOW_VALIDATION_INFO_GOAL_NO_NEXT_STATE = 'goal has no following action';
    const PROCESSFLOW_VALIDATION_INFO_CONDITION_SEGMENT_EMPTY = 'empty segment in condition';
    const PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_TAG = 'invalid tag in condition';
    const PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_TAG_DURATION = 'invalid tag duration in condition';
    const PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_FIELD = 'invalid field in condition';
    const PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_OP = 'invalid op in condition';
    const PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_VALUE = 'invalid value in condition';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_CONTENT = 'no content for send action';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_SUBJECT = 'email has no subject';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_CONTENT = 'email has no content';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NOT_PUBLISHED = 'email not published';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_SMS_NO_CONTENT = 'SMS has no content';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_NOTIFY_NO_RECEIVER = 'notification has no valid receiver';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_OUTBOUND = 'invalid outbound in outbound action';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_ACTIVATION_URL = 'invalid activation url in outbound action';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_TAG = 'invalid tag in action';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD = 'invalid field in action';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_TAG_FIELD = 'invalid tag or field in action';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_TARGET = 'no target in goto action';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_CAMPAIGN = 'no automation in automation action';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_SPLITTEST = 'invalid splittest';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_INVALID_TESTDURATION = 'invalid splittest duration';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANTS_MISSING = 'splittest variant missing';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_NO_VARIANT_ENTITY = 'invalid splittest entity';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_INVALID_WEIGHT = 'invalid splittest variant weight';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_INVALID_TAG = 'invalid splittest variant tag';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_WEIGHTS_NOT_100 = 'invalid splittest weights';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID = 'invalid action'; // obsolete
    const PROCESSFLOW_VALIDATION_ERROR_NODE_INVALID = 'invalid node';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_EXIT_HAS_NEXT = 'exit has next state';
    const PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_START_ITSELF = 'automation cant start itself';
    const PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_STOP_ITSELF = 'automation cant stop itself';
    const PROCESSFLOW_VALIDATION_ERROR_INVALID_TIME_DELAY = 'invalid time delay in wait';
    const PROCESSFLOW_VALIDATION_ERROR_INVALID_FIELD_TYPE = 'invalid field type';
    const PROCESSFLOW_VALIDATION_ERROR_INVALID_DELAY_TYPE = 'invalid delay type';
    const PROCESSFLOW_VALIDATION_ERROR_INVALID_LIMITER_PERIOD = 'invalid limiter period';
    const PROCESSFLOW_VALIDATION_ERROR_INVALID_WEEKDAY_CONSTRAINT = 'invalid weekday constraint';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_VALUE = 'invalid field value';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_FIELD_VALUE_OUTOFRANGE = 'field value out of range';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_COPY_FIELD = 'invalid copy field';
    const PROCESSFLOW_VALIDATION_ERROR_START_NOT_FOUND = 'start not found';
    const PROCESSFLOW_VALIDATION_ERROR_POSSIBLE_ENDLESS_LOOP = 'possible endless loop';
    const PROCESSFLOW_VALIDATION_ERROR_UNREACHABLE_NODES = 'unreachable nodes';
    const PROCESSFLOW_VALIDATION_ERROR_NO_ACTION_FOUND = 'no action found';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_OPERATOR = 'invalid field operator';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FACEBOOK_AUDIENCE = 'invalid facebook audience';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_FACEBOOK_AUDIENCE_NOT_CONNECTED = 'facebook audience not connected';
    const PROCESSFLOW_VALIDATION_ERROR_ACTION_FULLCONTACT_APIKEY_MISSING = 'full contact api key missing';
    const PROCESSFLOW_VALIDATION_ERROR_INVALID_CALENDAR = 'invalid calendar selected';
    const PROCESSFLOW_VALIDATION_ERROR_SEGMENT_INVALID = 'invalid user segment selected';
    const PROCESSFLOW_VALIDATION_WARNING_POSSIBLE_DELAY = 'possible delay';

    const PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR = ':';

    const PROCESSFLOW_GOTO_LOOP_DETECTION_MAX_PATHS = 400000;

    public static $ConvertDelayType = array(
        CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_IMMEDIATELY => Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY,
        CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_SECONDS => Campaigns::TRIGGER_TIME_TYPE_SECONDS,
        CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MINUTES => Campaigns::TRIGGER_TIME_TYPE_MINUTES,
        CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_HOURS => Campaigns::TRIGGER_TIME_TYPE_HOURS,
        CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_DAYS => Campaigns::TRIGGER_TIME_TYPE_DAYS,
        CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_WEEKS => Campaigns::TRIGGER_TIME_TYPE_WEEKS,
        CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MONTHS => Campaigns::TRIGGER_TIME_TYPE_MONTHS,
        CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD => Campaigns::TRIGGER_TYPE_DATETIME,
        CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_BIRTHDAY => Campaigns::TRIGGER_TYPE_BIRTHDAY,
    );

    public static $MatchTgaConditionToCategory = array(
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_IS_TAGGED => Tag::CATEGORY_MANUAL,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_IS_NOT_TAGGED => Tag::CATEGORY_MANUAL,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_SMARTLINK_CLICKED => Tag::CATEGORY_SMARTLINK,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_SMARTLINK_NOT_CLICKED => Tag::CATEGORY_SMARTLINK,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_SENT => Tag::CATEGORY_SENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_SENT => Tag::CATEGORY_SENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_OPENED => Tag::CATEGORY_OPENED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_OPENED => Tag::CATEGORY_OPENED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_CLICKED => Tag::CATEGORY_CLICKED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CLICKED => Tag::CATEGORY_CLICKED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_VIEWED => Tag::CATEGORY_VIEWED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_VIEWED => Tag::CATEGORY_VIEWED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_CONVERTED => Tag::CATEGORY_CONVERTED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CONVERTED => Tag::CATEGORY_CONVERTED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_KAJABI_ACTIVATED => Tag::CATEGORY_KAJABI_ACTIVATE,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_KAJABI_NOT_ACTIVATED => Tag::CATEGORY_KAJABI_ACTIVATE,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_KAJABI_DEACTIVATED => Tag::CATEGORY_KAJABI_DEACTIVATE,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_KAJABI_NOT_DEACTIVATED => Tag::CATEGORY_KAJABI_DEACTIVATE,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_OUTBOUND_ACTIVATED => Tag::CATEGORY_OUTBOUND,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_OUTBOUND_NOT_ACTIVATED => Tag::CATEGORY_OUTBOUND,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_TAGGING_PIXEL_ACTIVATED => Tag::CATEGORY_TAGGING_PIXEL,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_TAGGING_PIXEL_NOT_ACTIVATED => Tag::CATEGORY_TAGGING_PIXEL,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_EMAIL_SENT => Tag::CATEGORY_EMAIL_SENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_EMAIL_NOT_SENT => Tag::CATEGORY_EMAIL_SENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_EMAIL_OPENED => Tag::CATEGORY_EMAIL_OPENED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_EMAIL_NOT_OPENED => Tag::CATEGORY_EMAIL_OPENED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_EMAIL_CLICKED => Tag::CATEGORY_EMAIL_CLICKED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_EMAIL_NOT_CLICKED => Tag::CATEGORY_EMAIL_CLICKED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_EMAIL_VIEWED => Tag::CATEGORY_EMAIL_VIEWED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_EMAIL_NOT_VIEWED => Tag::CATEGORY_EMAIL_VIEWED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_SMS_SENT => Tag::CATEGORY_SMS_SENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_SMS_NOT_SENT => Tag::CATEGORY_SMS_SENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_SMS_CLICKED => Tag::CATEGORY_SMS_CLICKED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_SMS_NOT_CLICKED => Tag::CATEGORY_SMS_CLICKED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_AUTOMATION_STARTED => Tag::CATEGORY_AUTOMATION_STARTED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_AUTOMATION_NOT_STARTED => Tag::CATEGORY_AUTOMATION_STARTED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_AUTOMATION_FINISHED => Tag::CATEGORY_AUTOMATION_FINISHED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_AUTOMATION_NOT_FINISHED => Tag::CATEGORY_AUTOMATION_FINISHED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_APIKEY => Tag::CATEGORY_LISTBUILDING_APIKEY,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_APIKEY => Tag::CATEGORY_LISTBUILDING_APIKEY,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_BUSINESSCARD => Tag::CATEGORY_LISTBUILDING_BUSINESSCARD,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_BUSINESSCARD => Tag::CATEGORY_LISTBUILDING_BUSINESSCARD,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_EVENT => Tag::CATEGORY_LISTBUILDING_EVENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_EVENT => Tag::CATEGORY_LISTBUILDING_EVENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_REQUEST => Tag::CATEGORY_LISTBUILDING_REQUEST,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_REQUEST => Tag::CATEGORY_LISTBUILDING_REQUEST,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_SMS => Tag::CATEGORY_LISTBUILDING_SMS,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_SMS => Tag::CATEGORY_LISTBUILDING_SMS,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_FORMS => Tag::CATEGORY_LISTBUILDING_FORMS,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_FORMS => Tag::CATEGORY_LISTBUILDING_FORMS,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_PAYMENT => Tag::CATEGORY_LISTBUILDING_PAYMENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_PAYMENT => Tag::CATEGORY_LISTBUILDING_PAYMENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_REFUNDED => Tag::CATEGORY_LISTBUILDING_REFUNDED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_SUBSEQUENT_PAYMENT => Tag::CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_SUBSEQUENT_PAYMENT => Tag::CATEGORY_LISTBUILDING_SUBSEQUENT_PAYMENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_DEFFERED_PAYMENT => Tag::CATEGORY_LISTBUILDING_DEFERRED_PAYMENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_DEFFERED_PAYMENT => Tag::CATEGORY_LISTBUILDING_DEFERRED_PAYMENT,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_REFUNDED => Tag::CATEGORY_LISTBUILDING_REFUNDED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_CHARGEDBACK => Tag::CATEGORY_LISTBUILDING_CHARGEDBACK,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_CHARGEDBACK => Tag::CATEGORY_LISTBUILDING_CHARGEDBACK,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_CANCELED => Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_CANCELED => Tag::CATEGORY_LISTBUILDING_REBILL_CANCELED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_RESUMED => Tag::CATEGORY_LISTBUILDING_REBILL_RESUMED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_RESUMED => Tag::CATEGORY_LISTBUILDING_REBILL_RESUMED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_DIGISTORE_AFFILIATION => Tag::CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NO_DIGISTORE_AFFILIATION => Tag::CATEGORY_LISTBUILDING_DIGISTORE_AFFILIATION,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_LANDING_PAGE_SUBSCRIBED => Tag::CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_LANDING_PAGE_SUBSCRIBED => Tag::CATEGORY_LISTBUILDING_LANDING_PAGE_SUBSCRIBED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PAYMENT_COMPLETED => Tag::CATEGORY_PAYMENT_COMPLETED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PAYMENT_EXPIRED => Tag::CATEGORY_PAYMENT_EXPIRED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_FACEBOOK_AUDIENCE => Tag::CATEGORY_FACEBOOK_AUDIENCE,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_NOT_FACEBOOK_AUDIENCE => Tag::CATEGORY_FACEBOOK_AUDIENCE,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_READY => Tag::CATEGORY_PLUGIN_INBOUND_READY,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_READY => Tag::CATEGORY_PLUGIN_INBOUND_READY,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_STARTED => Tag::CATEGORY_PLUGIN_INBOUND_STARTED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_STARTED => Tag::CATEGORY_PLUGIN_INBOUND_STARTED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_INPROGRESS => Tag::CATEGORY_PLUGIN_INBOUND_INPROGRESS,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_INPROGRESS => Tag::CATEGORY_PLUGIN_INBOUND_INPROGRESS,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_FINISHED => Tag::CATEGORY_PLUGIN_INBOUND_FINISHED,
        CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_FINISHED => Tag::CATEGORY_PLUGIN_INBOUND_FINISHED,
    );

    // validation error translations
    // Note: register strings in angular todolist component
    public static $DisplayValidationError = array(
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_INFO_CONDITION_EMPTY =>
        /*t(*/'flowchart::message::todo::condition is empty'/*)*/, // obsolete
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_INFO_GOAL_CONDITION_EMPTY =>
        /*t(*/'flowchart::message::todo::goal condition is empty'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_INFO_GOAL_NO_NEXT_STATE =>
        /*t(*/'flowchart::message::todo::goal has no following action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_INFO_CONDITION_SEGMENT_EMPTY =>
        /*t(*/'flowchart::message::todo::empty segment in condition'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_TAG =>
        /*t(*/'flowchart::message::todo::invalid tag in condition'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_TAG_DURATION =>
        /*t(*/'flowchart::message::todo::invalid tag duration in condition'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_FIELD =>
        /*t(*/'flowchart::message::todo::invalid field in condition'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_OP =>
        /*t(*/'flowchart::message::todo::invalid op in condition'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_VALUE =>
        /*t(*/'flowchart::message::todo::invalid value in condition'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_CONTENT =>
        /*t(*/'flowchart::message::todo::no content for send action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_CONTENT . ':' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL =>
        /*t(*/'flowchart::message::todo::no email in send action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_CONTENT . ':' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDSMS =>
        /*t(*/'flowchart::message::todo::no sms in send action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_CONTENT . ':' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL =>
        /*t(*/'flowchart::message::todo::no notify email send action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_CONTENT . ':' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS =>
        /*t(*/'flowchart::message::todo::no notify sms send action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_SUBJECT =>
        /*t(*/'flowchart::message::todo::email has no subject'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_CONTENT =>
        /*t(*/'flowchart::message::todo::email has no content'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NOT_PUBLISHED =>
        /*t(*/'flowchart::message::todo::email not published'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SMS_NO_CONTENT =>
        /*t(*/'flowchart::message::todo::SMS has no content'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NOTIFY_NO_RECEIVER =>
        /*t(*/'flowchart::message::todo::notification has no valid receiver'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_OUTBOUND =>
        /*t(*/'flowchart::message::todo::invalid outbound in outbound action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_ACTIVATION_URL =>
        /*t(*/'flowchart::message::todo::invalid activation url in outbound action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_TAG =>
        /*t(*/'flowchart::message::todo::invalid tag in action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD =>
        /*t(*/'flowchart::message::todo::invalid field in action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_TAG_FIELD =>
        /*t(*/'flowchart::message::todo::invalid tag or field in action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_TARGET =>
        /*t(*/'flowchart::message::todo::no target in goto action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_CAMPAIGN =>
        /*t(*/'flowchart::message::todo::no automation in automation action'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_SPLITTEST =>
        /*t(*/'flowchart::message::todo::invalid splittest'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_INVALID_TESTDURATION =>
        /*t(*/'flowchart::message::todo::invalid splittest duration'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANTS_MISSING =>
        /*t(*/'flowchart::message::todo::splittest variant missing'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_NO_VARIANT_ENTITY =>
        /*t(*/'flowchart::message::todo::invalid splittest entity'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_INVALID_WEIGHT =>
        /*t(*/'flowchart::message::todo::invalid splittest variant weight'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_INVALID_TAG =>
        /*t(*/'flowchart::message::todo::invalid splittest variant tag'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_WEIGHTS_NOT_100 =>
        /*t(*/'flowchart::message::todo::invalid splittest weights'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID =>
        /*t(*/'flowchart::message::todo::invalid action'/*)*/, // obsolete
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_NODE_INVALID =>
        /*t(*/'flowchart::message::todo::invalid node'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EXIT_HAS_NEXT =>
        /*t(*/'flowchart::message::todo::exit has next state'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_START_ITSELF =>
        /*t(*/'flowchart::message::todo::automation cant start itself'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_STOP_ITSELF =>
        /*t(*/'flowchart::message::todo::automation cant stop itself'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_TIME_DELAY =>
        /*t(*/'flowchart::message::todo::invalid time delay in wait'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_FIELD_TYPE =>
        /*t(*/'flowchart::message::todo::invalid field type'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_DELAY_TYPE =>
        /*t(*/'flowchart::message::todo::invalid delay type'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_LIMITER_PERIOD =>
        /*t(*/'flowchart::message::todo::invalid limiter period'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_WEEKDAY_CONSTRAINT =>
        /*t(*/'flowchart::message::todo::invalid weekday constraint'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_VALUE =>
        /*t(*/'flowchart::message::todo::invalid field value'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_FIELD_VALUE_OUTOFRANGE =>
        /*t(*/'flowchart::message::todo::field value out of range'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_COPY_FIELD =>
        /*t(*/'flowchart::message::todo::invalid copy field'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_START_NOT_FOUND =>
        /*t(*/'flowchart::message::todo::start not found'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_POSSIBLE_ENDLESS_LOOP =>
        /*t(*/'flowchart::message::todo::possible endless loop'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_UNREACHABLE_NODES =>
        /*t(*/'flowchart::message::todo::unreachable nodes'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_NO_ACTION_FOUND =>
        /*t(*/'flowchart::message::todo::no action found'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_OPERATOR =>
        /*t(*/'flowchart::message::todo::invalid field operator'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FACEBOOK_AUDIENCE =>
        /*t(*/'flowchart::message::todo::invalid facebook audience'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_FACEBOOK_AUDIENCE_NOT_CONNECTED =>
        /*t(*/'flowchart::message::todo::facebook audience not connected'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_FULLCONTACT_APIKEY_MISSING =>
        /*t(*/'flowchart::message::todo::full contact api key missing'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_CALENDAR =>
        /*t(*/'flowchart::message::todo::Invalid calendar selected'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_SEGMENT_INVALID =>
        /*t(*/'flowchart::message::todo::Invalid user segment selected'/*)*/,
        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_WARNING_POSSIBLE_DELAY =>
        /*t(*/'flowchart::message::todo::Goto state could cause a possible delay'/*)*/,
    );

    public static $APIIndexFilterTypes = array(
        Campaigns::TRIGGER_TYPE_PROCESSFLOW => 'automation',
    );

    public static $DefaultDataFields = array(
        // processflow
        'ProcessFlow' => array(
            'start' => 1,
            'goals' => array(),
            'states' => array(
                array(
                    'id' => 1,
                    'name' => '',
                    'type' => 'start',
                    'next' => 0,
                    'nextNo' => 0,
                ),
            ),
        ),
        // send multiple times
        'MultipleSendFlag' => 0,
        // smart tag ids
        'AutomationStartedSmartTagID' => 0,
        'AutomationFinishedSmartTagID' => 0,
        'Notes' => '',
    );

    /*
     * @see Campaigns::$RecipientCalculationBatchSizeVariableName
     * @see Campaigns::$RecipientCalculationMaxTimeVariableName
     */
    public static $RecipientCalculationBatchSizeVariableName = 'klicktipp_automation_start_recipient_calculation_batch_size';
    public static $RecipientCalculationMaxTimeVariableName = 'klicktipp_automation_start_recipient_calculation_max_time';

    public static array $CustomerAccountsForSegmentsCache = [];

    public static function FromArray($DBArray)
    {
        $entity = new CampaignsProcessFlow($DBArray);

        if (empty($entity->DataFlat)) {
            return false;
        }

        return $entity;
    }

    public static function InsertDB($Data)
    {
        // Create a new record in campaigns table - Start
        $Data['AutoResponderTriggerTypeEnum'] = static::$CampaignType;
        $Data['CreatedOn'] = time();

        $NewCampaignID = parent::InsertDB($Data);
        if (!$NewCampaignID) {
            return false;
        }
        // Create a new record in campaigns table - End

        // write all additional data with an update query (as we need the CampaignID for that)

        $CampaignObject = static::FromID($Data['RelOwnerUserID'], $NewCampaignID);
        $Data = $CampaignObject->GetData();

        // Create tags - Start
        $Data['AutomationStartedSmartTagID'] = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_AUTOMATION_STARTED,
            'EntityID' => $NewCampaignID,
            'RelOwnerUserID' => $Data['RelOwnerUserID'],
        ));
        $Data['AutomationFinishedSmartTagID'] = Tag::CreateTag(array(
            'Category' => Tag::CATEGORY_AUTOMATION_FINISHED,
            'EntityID' => $NewCampaignID,
            'RelOwnerUserID' => $Data['RelOwnerUserID'],
        ));
        // Create tags - End

        static::UpdateDB($Data);

        return $NewCampaignID;
    }

    public static function UpdateDB($DataFlat)
    {
        // find unreachables nodes and remove them
        $found = static::CollectReachableNodes($DataFlat, $DataFlat['ProcessFlow']['start']);
        if (count($found) != count($DataFlat['ProcessFlow']['states'])) {
            // unreachables found -> copy found states only
            $newstates = array();
            foreach ($DataFlat['ProcessFlow']['states'] as $state) {
                if (in_array($state['id'], $found)) {
                    $newstates[] = $state;
                }
            }
            $DataFlat['ProcessFlow']['states'] = $newstates;
        }

        // simplify empty start state
        if (static::CheckForEmptyStart($DataFlat)) {
            foreach ($DataFlat['ProcessFlow']['states'] as &$state) {
                if ($state['id'] == $DataFlat['ProcessFlow']['start']) {
                    // restore init value, see $DefaultDataFields
                    unset($state['segments']);
                }
            };
        }

        // create/duplicate splittests
        foreach ($DataFlat['ProcessFlow']['states'] as &$state) {
            if ($state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SPLITTEST) {
                if ($state['splittestID'] == 'new') {
                    $NewID = SplitTests::InsertDB(array(
                        'RelOwnerUserID' => $DataFlat['RelOwnerUserID'],
                        'RelCampaignID' => $DataFlat['CampaignID'],
                    ));

                    $state['splittestID'] = ($NewID) ? $NewID : 'error';
                } elseif (strpos($state['splittestID'], 'copy:') === 0) {
                    $SplittestID = intval(str_replace('copy:', '', $state['splittestID']));

                    $NewID = SplitTests::DuplicateSplittest(
                        $DataFlat['RelOwnerUserID'],
                        $SplittestID,
                        $DataFlat['CampaignID']
                    );

                    $state['splittestID'] = ($NewID) ? $NewID : 'error';
                }
            }
        }

        return parent::UpdateDB($DataFlat);
    }

    public static function DeleteDB($Data)
    {
        // make sure, we have all data available (not required for general DeleteDB)
        $CampaignObject = static::FromID($Data['RelOwnerUserID'], $Data['CampaignID']);
        if (!$CampaignObject) {
            return false;
        }
        $Data = $CampaignObject->GetData();

        if (parent::DeleteDB($Data)) {
            // delete splittests
            SplitTests::RemoveSplittests($Data);

            // delete system tags of campaign
            Tag::DeleteTag($Data['AutomationStartedSmartTagID'], $Data['RelOwnerUserID']);
            Tag::DeleteTag($Data['AutomationFinishedSmartTagID'], $Data['RelOwnerUserID']);

            //remove all goal history entries for this campaign
            CampaignsProcessFlow::ClearGoalsHistory($Data['RelOwnerUserID'], $Data['CampaignID']);

            return true;
        }

        return false;
    }

    /**
     * count entities
     *
     * @param $UserID
     * @return int
     */
    public static function GetCount($UserID): int
    {
        return (int)db_query(
            "SELECT COUNT(*) FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID AND AutoResponderTriggerTypeEnum = :TriggerType",
            array(
                ':RelOwnerUserID' => $UserID,
                ':TriggerType' => static::$CampaignType,
            )
        )->fetchField();
    }

    /**
     * @param int $userId
     * @return int
     * @throws \Doctrine\DBAL\Exception
     */
    public static function getActiveAndScheduledCampaignCount(int $userId): int
    {
        return (int)db_query(
            "SELECT COUNT(*) FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID " .
            "AND AutoResponderTriggerTypeEnum = :TriggerType AND (CampaignStatusEnum IN (:sending) OR " .
            "(CampaignStatusEnum = :ready AND ScheduleTypeEnum != :notScheduled))",
            array(
                ':RelOwnerUserID' => $userId,
                ':TriggerType' => Campaigns::TRIGGER_TYPE_PROCESSFLOW,
                ':sending' => Campaigns::$ArrayCampaignStatiSending,
                ':ready' => Campaigns::STATUS_READY,
                ':notScheduled' => Campaigns::SCHEDULE_TYPE_NOTSCHEDULED
            )
        )->fetchField();
    }

    public static function IsActive($DBArray)
    {
        if ($DBArray['CampaignStatusEnum'] == Campaigns::STATUS_DRAFT) {
            return false;
        }

        if ($DBArray['CampaignStatusEnum'] == Campaigns::STATUS_READY && $DBArray['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED) {
            return false;
        }

        return true;
    }

    // DatabaseTableCRUD

    public static $APIPath = 'kt-automation';

    public static $APIFields = array(
        'CampaignID' => array(
            'id' => 'campaignid',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'CampaignName' => array(
            'id' => 'name',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'AutoResponderTriggerTypeEnum' => array(
            'id' => 'type',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'ProcessFlow' => array(
            'id' => 'processflow',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM,
        ),
        'CampaignStatusEnum' => array(
            'id' => 'status',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'ScheduleTypeEnum' => array(
            'id' => 'schedule',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'Statistics' => array(
            'id' => 'statistics',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM,
        ),
        'Plugins' => array(
            'id' => 'plugins',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM,
        ),
        'AutomationStartedSmartTagID' => array(
            'id' => Tag::TAG_CATEGORY_AUTOMATION_STARTED,
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'AutomationFinishedSmartTagID' => array(
            'id' => Tag::TAG_CATEGORY_AUTOMATION_FINISHED,
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'MultipleSendFlag' => array(
            'id' => 'multiSend',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        )
    );

    public static function klicktippapi_resource_definition()
    {
        $resource = parent::klicktippapi_resource_definition();
        // POST /api/<path>/validate + body(data)
        $resource[static::$APIPath]['actions']['validate'] = array(
            'help' => 'Validate automation',
            'callback' => array(get_called_class(), 'klicktippapi_validate'),
            'access callback' => 'services_access_menu',
            'args' => array(
                array(
                    'name' => 'campaignid',
                    'type' => 'int',
                    'description' => 'The id of the automation.',
                    'source' => array('data' => 'campaignid'),
                    'optional' => false,
                ),
            )
        );

        // POST /api/<path>/subscribers-waiting + body(data)
        $resource[static::$APIPath]['actions']['subscribers-waiting'] = array(
            'help' => 'Subscribers waiting for action',
            'callback' => array(
                get_called_class(),
                'klicktippapi_subscribers_waiting'
            ),
            'access callback' => 'services_access_menu',
            'args' => array(
                array(
                    'name' => 'campaignid',
                    'type' => 'int',
                    'description' => 'The id of the automation.',
                    'source' => array('data' => 'campaignid'),
                    'optional' => false,
                ),
            )
        );

        // POST /api/<path>/conversion + body(data)
        $resource[static::$APIPath]['actions']['conversion'] = array(
            'help' => 'Campaign Conversion',
            'callback' => array(
                get_called_class(),
                'klicktippapi_conversion'
            ),
            'access callback' => 'services_access_menu',
            'args' => array(
                array(
                    'name' => 'campaignid',
                    'type' => 'int',
                    'description' => 'The id of the automation.',
                    'source' => array('data' => 'campaignid'),
                    'optional' => false,
                ),
                array(
                    'name' => 'from',
                    'type' => 'int',
                    'description' => 'start of range (timestamp)',
                    'source' => array('data' => 'from'),
                    'optional' => true,
                    'default value' => 0,
                ),
                array(
                    'name' => 'to',
                    'type' => 'int',
                    'description' => 'end of range (timestamp)',
                    'source' => array('data' => 'to'),
                    'optional' => true,
                    'default value' => REQUEST_TIME,
                ),
            )
        );

        return $resource;
    }

    public static function klicktippapi_retrieve($id)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        /** @var DatabaseTableCRUD $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);
        if (empty($ObjectEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        // add stats where defined
        $Data = $ObjectEntity->GetData();
        $Data['Statistics'] = $ObjectEntity->GetTaggingStats();
        $Data['Plugins'] = Plugin::get_plugins_for_conditions($account);

        // make plugin conditions unique
        static::decorateConditionsOfPlugins($Data);

        $ObjectAPIEntity = $ObjectEntity->FilterObject($Data);

        // Everything went right.
        return $ObjectAPIEntity;
    }

    //TODO: temprary until we found the bug that sets states to NULL, remove after
    public static function klicktippapi_update($id, $data)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        /** @var DatabaseTableCRUD $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);
        if (empty($ObjectEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        if (!static::AllowUpdate($ObjectEntity->GetData(), $data)) {
            return services_error(t('entity update failed. Automation is active.'), 406);
        }

        $DBArray = $ObjectEntity->UnFilterObject($data, $ObjectEntity->GetData());

        // remove unique decorator from plugin conditions in cockpit
        static::undecorateConditionsOfPlugins($DBArray);

        //TODO: BUG ANALISIS - this block is the only difference to the parent
        if (empty($DBArray['ProcessFlow']['states'])) {
            $data_before = $ObjectEntity->GetDBArray();

            Errors::unexpected(
                'CampaignsProcessflow: API update with empty states. CampaignID: !cid, UserID: !uid',
                array(
                    '!uid' => $DBArray['RelOwnerUserID'],
                    '!cid' => $DBArray['CampaignID'],
                    '!data_before' => $data_before['Data'],
                )
            );

            return services_error(t('entity update failed.'), 406);
        }
        //END BUG ANALISIS

        if ($DBArray) {
            if (!$ObjectEntity->UpdateDB($DBArray)) {
                return services_error(t('entity update failed.'), 406);
            }
        } else {
            return services_error(t('entity update failed.'), 406);
        }

        // Everything went right.
        return true;
    }

    public static function klicktippapi_validate($campaignid)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        $ObjectCampaign = static::FromID($account->uid, $campaignid);
        if (empty($ObjectCampaign)) {
            return services_error(t('Automation not found.'), 404);
        }

        $result = static::ValidateAutomation($ObjectCampaign->GetData());

        $todos = [];
        foreach ($result as $todo) {
            $isWarning = $todo[1] === self::PROCESSFLOW_VALIDATION_WARNING_POSSIBLE_DELAY;
            $todos[] = array(
                'id' => $todo[0],
                'message' => (empty(CampaignsProcessFlow::$DisplayValidationError[$todo[1]])) ? '' : CampaignsProcessFlow::$DisplayValidationError[$todo[1]],
                'variantID' => (!isset($todo[2])) ? -1 : $todo[2],
                'entityType' => (!isset($todo[3])) ? 'state' : $todo[3],
                'entityID' => (!isset($todo[4])) ? 0 : $todo[4],
                'entityName' => (!isset($todo[5])) ? '' : $todo[5],
                'isWarning' => $isWarning
            );
        }

        return $todos;
    }

    public static function klicktippapi_subscribers_waiting($campaignid)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        $ObjectCampaign = static::FromID($account->uid, $campaignid);
        if (empty($ObjectCampaign)) {
            return services_error(t('Automation not found.'), 404);
        }

        return TransactionEmails::RetrievePendingSubscribersOfAutomation($ObjectCampaign->GetData());
    }

    public static function klicktippapi_conversion($campaignid, $from = 0, $to = REQUEST_TIME)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account) || !user_access('use whitelabel domain', $account)) {
            return services_error(t('API access denied.'), 403);
        }

        $ObjectCampaign = static::FromID($account->uid, $campaignid);
        if (empty($ObjectCampaign)) {
            return services_error(t('Automation not found.'), 404);
        }

        return Statistics::RetrieveCampaignConversion($account->uid, $campaignid, $from, $to);
    }

    public static function GetEntitiesAsOptionsArray($UserID)
    {
        if (empty($UserID)) {
            return array();
        }
        $OptionsArray = array();

        // get db sets
        $result = db_query(
            "SELECT * FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID AND AutoResponderTriggerTypeEnum = :TriggerType",
            array(
                ':RelOwnerUserID' => $UserID,
                ':TriggerType' => static::$CampaignType,
            )
        );
        while ($DBArray = kt_fetch_array($result)) {
            // put in as <entity id> => <entity name>
            $OptionsArray[$DBArray[static::$DBSerialField]] = check_plain(static::GetNameForOptionsArray($DBArray));
        }

        natcasesort($OptionsArray);

        return $OptionsArray;
    }

    public static function FilterObject($data)
    {
        $result = parent::FilterObject($data);

        foreach ($data as $key => $value) {
            if (isset(static::$APIFields[$key])) {
                switch ($key) {
                    case 'ProcessFlow':
                        $result[static::$APIFields[$key]['id']] = $value;
                        break;
                }
            }
        }
        return $result;
    }

    public static function UnFilterObject($data, $result = array(), $isObjectNew = false)
    {
        $result = parent::UnFilterObject($data, $result);

        foreach (static::$APIFields as $key => $def) {
            if (isset($data[$def['id']])) {
                switch ($key) {
                    case 'ProcessFlow':
                        //TODO validate
                        $result[$key] = $data[$def['id']];
                        break;
                }
            }
        }

        return $result;
    }

    /**
     * make plugin conditions unique in cockpit
     * @param array<mixed> $Data
     */
    protected static function decorateConditionsOfPlugins(array &$Data):void
    {
      foreach ($Data['ProcessFlow']['states'] as &$state) {
        switch ($state['type']) {
          case static::PROCESSFLOW_STATE_TYPE_START:
          case static::PROCESSFLOW_STATE_TYPE_DECISION:
          case static::PROCESSFLOW_STATE_TYPE_GOAL:
            if (!empty($state['segments'])) {
              // check segments
              foreach ($state['segments'] as &$segment) {
                if (!empty($segment['conditions'])) {
                  // check conditions
                  foreach ($segment['conditions'] as &$condition) {
                    switch ($condition['op']) {
                      case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_READY:
                      case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_STARTED:
                      case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_INPROGRESS:
                      case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_FINISHED:
                      case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_READY:
                      case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_STARTED:
                      case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_INPROGRESS:
                      case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_FINISHED:
                        // condition on smart tag of plugin:
                        // we need to prefix with plugin id (see conditions_for_plugin)
                        // the condition field is a plugin smart tag that has a reference to the tool object
                        // the VarCharIndexed field of the tags table is the plugin id
                        $smartTagId = $condition['field'];
                        $pluginId = db_query(<<<SQL
  SELECT m.VarCharIndexed FROM {tag} t 
  INNER JOIN {marketing_tools} m ON m.ToolID = t.EntityID AND t.RelOwnerUserID = m.RelOwnerUserID 
  WHERE TagID = :tagid AND t.RelOwnerUserID = :uid
  SQL
                          , [':tagid' => $smartTagId, ':uid' => $Data['RelOwnerUserID']])->fetchField();
                        if ($pluginId) {
                          $condition['op'] = $pluginId .CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR. $condition['op'];
                        };
                        break;
                    }
                  }
                }
              }
            }
            break;
        }
      }
    }

    /**
     * remove unique decorator from plugin conditions in cockpit
     * @param array<mixed> $Data
     */
    protected static function undecorateConditionsOfPlugins(array &$Data): void
    {
      foreach ($Data['ProcessFlow']['states'] as &$state) {
        switch ($state['type']) {
          case static::PROCESSFLOW_STATE_TYPE_START:
          case static::PROCESSFLOW_STATE_TYPE_DECISION:
          case static::PROCESSFLOW_STATE_TYPE_GOAL:
            if (!empty($state['segments'])) {
              // check segments
              foreach ($state['segments'] as &$segment) {
                if (!empty($segment['conditions'])) {
                  // check conditions
                  foreach ($segment['conditions'] as &$condition) {
                    list (, $op) = explode(CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR, $condition['op']);
                    if ($op) {
                      switch ($op) {
                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_READY:
                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_STARTED:
                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_INPROGRESS:
                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_FINISHED:
                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_READY:
                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_STARTED:
                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_INPROGRESS:
                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_FINISHED:
                          // condition on smart tag of plugin:
                          // strip the pluginid (see decorateConditionsOfPlugins)
                          $condition['op'] = $op;
                          break;
                      };
                    }
                  }
                }
              }
            }
            break;
        }
      }
    }

    public static function FindState($ArrayCampaign, $id)
    {
        foreach ($ArrayCampaign['ProcessFlow']['states'] as $state) {
            if ($state['id'] == $id) {
                return $state;
            }
        };
        return false;
    }

    public static function CollectReachableNodes($ArrayCampaign, $stateid, $visited = array())
    {
        $state = static::FindState($ArrayCampaign, $stateid);
        if (empty($state)) {
            // 0 or invalid node: stop search
            return $visited;
        }

        if (in_array($stateid, $visited)) {
            // we have been already here: stop search
            return $visited;
        }

        // add node as reachable (setting as key guaranties unique values)
        $visited[$stateid] = $stateid;

        // go to next
        switch ($state['type']) {
            case static::PROCESSFLOW_STATE_TYPE_EXIT:
            case static::PROCESSFLOW_STATE_TYPE_RESTART:
                // no next node
                break;
            case static::PROCESSFLOW_STATE_TYPE_DECISION:
                // two next nodes
                $visited = static::CollectReachableNodes($ArrayCampaign, $state['next'], $visited);
                $visited = static::CollectReachableNodes($ArrayCampaign, $state['nextNo'], $visited);
                break;
            default:
                // one next node
                $visited = static::CollectReachableNodes($ArrayCampaign, $state['next'], $visited);
                break;
        }

        return $visited;
    }

    /**
     * Search for endless loops in the automation.
     * This is based on the fact, that an if-condition terminates every loop, because
     * as the process comes to such an node twice in a run, it stops (pending for next trigger).
     */
    public static function CheckForEndlessLoops($ArrayCampaign, $stateid, $gotoid, $count)
    {
        if ($stateid == $gotoid) {
            // found the goto -> fail
            return false;
        }

        if ($count <= 0) {
            // avoid an endless recursion of this function
            // if we checked more nodes than present in the automation, then we had a goto somewhere in the path
            // but then there is another path (as all gotos create a path), that does not include our node, and so on
            // as there is no real case with such a long path, we must stop to avoid an endless loop
            return false;
        }

        $state = static::FindState($ArrayCampaign, $stateid);
        if (empty($state)) {
            // 0 or invalid node: stop search -> ok
            return true;
        }

        if (in_array($state['type'], [static::PROCESSFLOW_STATE_TYPE_EXIT, static::PROCESSFLOW_STATE_TYPE_RESTART])) {
            // if we run into an exit, we have no loop in this path
            return true;
        }

        if ($state['type'] == static::PROCESSFLOW_STATE_TYPE_DECISION) {
            // if we run into a decision, we have a defined "waiting" point
            return true;
        }

        // check next node in path
        return static::CheckForEndlessLoops($ArrayCampaign, $state['next'], $gotoid, $count - 1);
    }

    /**
     * check whether start condition is empty
     * @param $ArrayCampaign
     * @return bool
     */
    public static function CheckForEmptyStart($ArrayCampaign)
    {
        // check start state exists
        $state = static::FindState($ArrayCampaign, $ArrayCampaign['ProcessFlow']['start']);
        if (empty($state)) {
            // this is a destroyed workflow, so dont proceed with advanced actions here
            return false;
        }
        if ($state['type'] != static::PROCESSFLOW_STATE_TYPE_START) {
            // this is a destroyed workflow, so dont proceed with advanced actions here
            return false;
        }

        // this is the how campaigns are created
        if (empty($state['segments'])) {
            return true;
        }

        // check for empty condition structure - all segments with no condition
        $noofempties = 0;
        foreach ($state['segments'] as $segment) {
            if (empty($segment['conditions'])) {
                $noofempties++;
            } else {
                // check for a segment with all empty conditions
                $noofemptieconds = 0;
                foreach ($segment['conditions'] as $condition) {
                    if (empty($condition['op'])) {
                        $noofemptieconds++;
                    }
                }
                if ($noofemptieconds == count($segment['conditions'])) {
                    $noofempties++;
                }
            }
        }
        return $noofempties == count($state['segments']);
    }

    /**
     * check whether campaign is startable
     * @param $ArrayCampaign
     * @return array returns to-do-list
     */
    public static function ValidateAutomation($ArrayCampaign)
    {
        $TodoList = array();

        // check start state exists
        $startstate = static::FindState($ArrayCampaign, $ArrayCampaign['ProcessFlow']['start']);
        if (empty($startstate)) {
            $TodoList[] = array(
                $ArrayCampaign['ProcessFlow']['start'],
                static::PROCESSFLOW_VALIDATION_ERROR_START_NOT_FOUND
            );
        }
        // check first action exists
        $firststate = static::FindState($ArrayCampaign, $startstate['next']);
        if (empty($firststate)) {
            $TodoList[] = array(
                $ArrayCampaign['ProcessFlow']['start'],
                static::PROCESSFLOW_VALIDATION_ERROR_NO_ACTION_FOUND
            );
        }

        // check for unreachable nodes
        $found = static::CollectReachableNodes($ArrayCampaign, $ArrayCampaign['ProcessFlow']['start']);
        if (count($found) != count($ArrayCampaign['ProcessFlow']['states'])) {
            $TodoList[] = array(
                $ArrayCampaign['ProcessFlow']['start'],
                static::PROCESSFLOW_VALIDATION_ERROR_UNREACHABLE_NODES
            );
        }

        foreach ($ArrayCampaign['ProcessFlow']['states'] as $state) {
            switch ($state['type']) {
                case static::PROCESSFLOW_STATE_TYPE_START:
                case static::PROCESSFLOW_STATE_TYPE_DECISION:
                case static::PROCESSFLOW_STATE_TYPE_GOAL:
                    //check if a goal has a next state
                    if ($state['type'] == static::PROCESSFLOW_STATE_TYPE_GOAL && empty($state['next'])) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_INFO_GOAL_NO_NEXT_STATE
                        );
                    }

                    if (empty($state['segments'])) {
                        if ($state['type'] == static::PROCESSFLOW_STATE_TYPE_GOAL) {
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_INFO_GOAL_CONDITION_EMPTY
                            );
                        }
                        //else: this is the "startable by other campaigns" condition
                    } else {
                        // check segments
                        foreach ($state['segments'] as $segment) {
                            if (empty($segment['conditions'])) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_INFO_CONDITION_SEGMENT_EMPTY
                                );
                            } else {
                                // check conditions
                                foreach ($segment['conditions'] as $condition) {
                                    switch ($condition['op']) {
                                        case static::PROCESSFLOW_CONDITION_IS_ACTIVE_EMAIL:
                                        case static::PROCESSFLOW_CONDITION_IS_NOT_ACTIVE_EMAIL:
                                        case static::PROCESSFLOW_CONDITION_IS_ACTIVE_SMS:
                                        case static::PROCESSFLOW_CONDITION_IS_NOT_ACTIVE_SMS:
                                            // no parameters, nothing to check
                                            break;
                                        case CampaignsProcessFlow::PROCESSFLOW_CONDITION_SEGMENT_IN:
                                        case CampaignsProcessFlow::PROCESSFLOW_CONDITION_SEGMENT_NOT_IN:
                                            $userSegments = VarSegment::getSegments(
                                                $ArrayCampaign['RelOwnerUserID'],
                                                true
                                            );
                                            if (!$userSegments[$condition['value2']]) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_SEGMENT_INVALID,
                                                );
                                            }
                                            break;
                                        case static::PROCESSFLOW_CONDITION_IS_TAGGED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_SENT:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_OPENED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_VIEWED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_CONVERTED:
                                        case static::PROCESSFLOW_CONDITION_KAJABI_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_KAJABI_DEACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_OUTBOUND_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_SENT:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_OPENED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_VIEWED:
                                        case static::PROCESSFLOW_CONDITION_SMS_SENT:
                                        case static::PROCESSFLOW_CONDITION_SMS_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_AUTOMATION_STARTED:
                                        case static::PROCESSFLOW_CONDITION_AUTOMATION_FINISHED:
                                        case static::PROCESSFLOW_CONDITION_SMARTLINK_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_TAGGING_PIXEL_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_APIKEY:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_BUSINESSCARD:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_EVENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REQUEST:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_SMS:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_FORMS:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_SUBSEQUENT_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_DEFFERED_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REFUNDED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_CHARGEDBACK:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_CANCELED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_RESUMED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_DIGISTORE_AFFILIATION:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_LANDING_PAGE_SUBSCRIBED:
                                        case static::PROCESSFLOW_CONDITION_PAYMENT_COMPLETED:
                                        case static::PROCESSFLOW_CONDITION_PAYMENT_EXPIRED:
                                        case static::PROCESSFLOW_CONDITION_FACEBOOK_AUDIENCE:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_READY:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_STARTED:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_INPROGRESS:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_FINISHED:
                                        case static::PROCESSFLOW_CONDITION_IS_NOT_TAGGED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_SENT:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_OPENED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_VIEWED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CONVERTED:
                                        case static::PROCESSFLOW_CONDITION_KAJABI_NOT_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_KAJABI_NOT_DEACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_OUTBOUND_NOT_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_NOT_SENT:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_NOT_OPENED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_NOT_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_NOT_VIEWED:
                                        case static::PROCESSFLOW_CONDITION_SMS_NOT_SENT:
                                        case static::PROCESSFLOW_CONDITION_SMS_NOT_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_AUTOMATION_NOT_STARTED:
                                        case static::PROCESSFLOW_CONDITION_AUTOMATION_NOT_FINISHED:
                                        case static::PROCESSFLOW_CONDITION_SMARTLINK_NOT_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_TAGGING_PIXEL_NOT_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_APIKEY:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_BUSINESSCARD:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_EVENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_REQUEST:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_SMS:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_FORMS:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_SUBSEQUENT_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_DEFFERED_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_REFUNDED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_CHARGEDBACK:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_CANCELED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_RESUMED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NO_DIGISTORE_AFFILIATION:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_LANDING_PAGE_SUBSCRIBED:
                                        case static::PROCESSFLOW_CONDITION_NOT_FACEBOOK_AUDIENCE:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_READY:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_STARTED:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_INPROGRESS:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_FINISHED:
                                            // $condition['field'] needs to be a valid tagid
                                            if ($condition['field'] === '') {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_TAG
                                                );
                                            } elseif ($condition['field'] > 0) {
                                                $ArrayTag = Tag::RetrieveTag(
                                                    $ArrayCampaign['RelOwnerUserID'],
                                                    $condition['field']
                                                );
                                                if (empty($ArrayTag)) {
                                                    $TodoList[] = array(
                                                        $state['id'],
                                                        static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_TAG
                                                    );
                                                }
                                            }
                                            // value is "second between 0 and one year", 366 * 24 * 60 * 60 = 31622400
                                            if ((int)$condition['value'] < 0 || $condition['value'] > 31622400) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_TAG_DURATION
                                                );
                                            }
                                            break;

                                        case static::PROCESSFLOW_CONDITION_TODAY_IS:
                                        case static::PROCESSFLOW_CONDITION_TODAY_IS_NOT:
                                        case static::PROCESSFLOW_CONDITION_TODAY_BEFORE:
                                        case static::PROCESSFLOW_CONDITION_TODAY_AFTER:
                                            if (
                                                empty($condition['value']) || $condition['value'] != date(
                                                    'Y-m-d',
                                                    strtotime($condition['value'])
                                                )
                                            ) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    $condition['op'] . ':' . static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_VALUE,
                                                );
                                            }
                                            break;
                                        case static::PROCESSFLOW_CONDITION_TODAY_IS_HOLIDAY:
                                            //$condition['value'] is an array of calendar ids
                                            $invalidCalendar = false;
                                            if (!empty($condition['value'])) {
                                                foreach ($condition['value'] as $cid) {
                                                    if (!ToolCalendar::FromID($ArrayCampaign['RelOwnerUserID'], $cid)) {
                                                        $invalidCalendar = true;
                                                        break;
                                                    }
                                                }
                                            }
                                            if (empty($condition['value']) || $invalidCalendar) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_INVALID_CALENDAR
                                                );
                                            }
                                            break;
                                        case static::PROCESSFLOW_CONDITION_TODAY_IS_WEEKDAY:
                                        case static::PROCESSFLOW_CONDITION_TODAY_IS_MONTH:
                                        case static::PROCESSFLOW_CONDITION_TODAY_IS_DAY_OF_MONTH:
                                            if (
                                                !CampaignsProcessFlow::CheckDatetimeConstraints(
                                                    $condition['op'],
                                                    $condition['value']
                                                )
                                            ) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_VALUE
                                                );
                                            }
                                            break;

                                        case static::PROCESSFLOW_CONDITION_TODAY_BEFORE_TIME:
                                        case static::PROCESSFLOW_CONDITION_TODAY_AFTER_TIME:
                                            $value = CustomFields::ConvertCustomFieldDataFromWidget(
                                                $condition['value'],
                                                CustomFields::WIDGET_TIME_SECONDSOFDAY
                                            );
                                            if ($value < 0 || $value >= 86400) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_VALUE
                                                );
                                            }

                                            break;
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_EMPTY:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_NOT_EMPTY:
                                            // $condition['field'] needs to be a valid customfieldid
                                            $CustomFields = TransactionEmails::GetCachedCustomFields(
                                                $ArrayCampaign['RelOwnerUserID']
                                            );
                                            $ArrayCustomField = $CustomFields[$condition['field']] ?? null;
                                            if (empty($ArrayCustomField)) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_FIELD
                                                );
                                            }

                                            break;

                                        case static::PROCESSFLOW_CONDITION_FIELD_IS:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_NOT:
                                        case static::PROCESSFLOW_CONDITION_FIELD_BEFORE_TIME:
                                        case static::PROCESSFLOW_CONDITION_FIELD_AFTER_TIME:
                                            //check if field exists, value not empty and has right format for field type
                                            CampaignsProcessFlow::ValidateCustomField(
                                                $ArrayCampaign['RelOwnerUserID'],
                                                $condition['field'],
                                                $condition['value'],
                                                $state,
                                                $TodoList
                                            );
                                            break;
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_HOLIDAY:
                                            //$condition['value'] is an array of calendar ids
                                            $invalidCalendar = false;
                                            if (!empty($condition['value'])) {
                                                foreach ($condition['value'] as $cid) {
                                                    if (!ToolCalendar::FromID($ArrayCampaign['RelOwnerUserID'], $cid)) {
                                                        $invalidCalendar = true;
                                                        break;
                                                    }
                                                }
                                            }
                                            if (empty($condition['value']) || $invalidCalendar) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_INVALID_CALENDAR
                                                );
                                            }
                                            break;
                                        case static::PROCESSFLOW_CONDITION_FIELD_CONTAINS:
                                        case static::PROCESSFLOW_CONDITION_FIELD_CONTAINS_NOT:
                                        case static::PROCESSFLOW_CONDITION_FIELD_STARTS_WITH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_STARTS_NOT_WITH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_ENDS_WITH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_ENDS_NOT_WITH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_LESS_THAN:
                                        case static::PROCESSFLOW_CONDITION_FIELD_LESS_EQUAL_THAN:
                                        case static::PROCESSFLOW_CONDITION_FIELD_GREATER_THAN:
                                        case static::PROCESSFLOW_CONDITION_FIELD_GREATER_EQUAL_THAN:
                                            //check if constant value is not empty
                                            if (!isset($condition['value']) || $condition['value'] === '') {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_VALUE
                                                );
                                            }
                                            break;

                                        case static::PROCESSFLOW_CONDITION_FIELD_BEFORE:
                                        case static::PROCESSFLOW_CONDITION_FIELD_AFTER:
                                            $CustomFields = TransactionEmails::GetCachedCustomFields(
                                                $ArrayCampaign['RelOwnerUserID']
                                            );
                                            $ArrayCustomField = $CustomFields[$condition['field']] ?? null;

                                            if (empty($ArrayCustomField)) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_FIELD,
                                                );
                                            } else {
                                                //check if field exists, value not empty and has right format for field type
                                                $check = strtotime($condition['value']);

                                                if (
                                                    $check === false || $check < strtotime(
                                                        '-99 years'
                                                    ) || $check > strtotime('+99 years')
                                                ) {
                                                    $TodoList[] = array(
                                                        $state['id'],
                                                        static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_VALUE
                                                    );
                                                }
                                            }

                                            break;

                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_WEEKDAY:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_MONTH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_DAY_OF_MONTH:
                                            // $condition['field'] needs to be a valid customfieldid
                                            $CustomFields = TransactionEmails::GetCachedCustomFields(
                                                $ArrayCampaign['RelOwnerUserID']
                                            );
                                            $ArrayCustomField = $CustomFields[$condition['field']] ?? null;
                                            if (empty($ArrayCustomField)) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_FIELD
                                                );
                                            }

                                            if (
                                                !CampaignsProcessFlow::CheckDatetimeConstraints(
                                                    $condition['op'],
                                                    $condition['value']
                                                )
                                            ) {
                                                $TodoList[] = array(
                                                    $state['id'],
                                                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_VALUE
                                                );
                                            }

                                            break;
                                        default:
                                            $TodoList[] = array(
                                                $state['id'],
                                                static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_OP
                                            );
                                            break;
                                    }
                                }
                            }
                        }
                    }
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNSUBSCRIBE:
                    // no parameters -> nothing to check
                    break;

                /** @noinspection PhpMissingBreakStatementInspection */
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_RESTART:
                    if (!empty($state['tagID'])) {
                        $ArrayTag = Tag::RetrieveTag($ArrayCampaign['RelOwnerUserID'], $state['tagID']);
                        if (empty($ArrayTag)) {
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_TAG
                            );
                        }
                    }
                // Note: intentional fall through to case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT:
                    if (!empty($state['next'])) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_EXIT_HAS_NEXT
                        );
                    };
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDSMS:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS:
                    // $state['emailID'] needs to be a valid email id
                    $ArrayEmail = Emails::RetrieveEmailByID($ArrayCampaign['RelOwnerUserID'], $state['emailID']);
                    if (empty($ArrayEmail)) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_CONTENT . ':' . $state['type'],
                        );
                    } else {
                        // emails: subject + body
                        if (
                            in_array($state['type'], array(
                                CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL,
                                CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL
                            ))
                        ) {
                            static::ValidateAutomationEmail($ArrayEmail, $state, $TodoList);
                        }

                        // sms: body
                        if (
                            in_array($state['type'], array(
                                CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDSMS,
                                CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS
                            ))
                        ) {
                            static::ValidateAutomationSMS($ArrayEmail, $state, $TodoList);
                        }
                    }

                    // notification email: valid receiver email in $state['notifyReceiverEmail']
                    if ($state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL) {
                        if ($state['notifyReceiverEmail'] != Signatures::SIGNATURE_DISPATCH_PROFILE) {
                            if (
                                !Subscribers::ValidateEmailAddress(
                                    Subscribers::PunycodeEmailAddress($state['notifyReceiverEmail'])
                                )
                            ) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_NOTIFY_NO_RECEIVER
                                );
                            }
                        }
                    }

                    // notification sms: valid receiver phone number in $state['notifyReceiverEmail']
                    if ($state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS) {
                        $pn = Subscribers::FormatSMSNumber($state['notifyReceiverEmail']);
                        if (empty($pn)) {
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_ERROR_ACTION_NOTIFY_NO_RECEIVER
                            );
                        }
                    }
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_OUTBOUND:
                    // $state['outboundID'] needs to be a valid outbound id
                    /** @var ToolOutbound $ObjectOutbound */
                    $ObjectOutbound = ToolOutbound::FromID($ArrayCampaign['RelOwnerUserID'], $state['outboundID']);
                    if (empty($ObjectOutbound)) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_OUTBOUND
                        );
                    } else {
                        static::ValidateOutbound($ObjectOutbound->GetData(), $state, $TodoList);
                    };
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_TAGGING:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNTAGGING:
                    // $state['tagID'] needs to be a valid tagid
                    $ArrayTag = Tag::RetrieveTag($ArrayCampaign['RelOwnerUserID'], $state['tagID']);
                    if (empty($ArrayTag)) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_TAG
                        );
                    }
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SETFIELD:
                    //NOTE: CampaignsProcessFlow::ValidateCustomField() checks if the field exist, if the value is not empty and fits the custom field format

                    switch ($state['customFieldOp']) {
                        case CustomFields::CALCULATE_OP_VALUE:
                        case CustomFields::CALCULATE_OP_TEXT_PREPEND:
                        case CustomFields::CALCULATE_OP_TEXT_APPEND:
                            CampaignsProcessFlow::ValidateCustomField(
                                $ArrayCampaign['RelOwnerUserID'],
                                $state['customFieldID'],
                                $state['customFieldValue'],
                                $state,
                                $TodoList
                            );
                            break;
                        case CustomFields::CALCULATE_OP_TEXT_REPLACE:
                            //customFieldValue contains the value that should be replaced
                            //customFieldValue2 contains the value to replace customFieldValue with (can be empty, no need to validate)

                            CampaignsProcessFlow::ValidateCustomField(
                                $ArrayCampaign['RelOwnerUserID'],
                                $state['customFieldID'],
                                $state['customFieldValue'],
                                $state,
                                $TodoList
                            );
                            break;

                        case CustomFields::CALCULATE_OP_COPY:
                            //customFieldValue: contains the if of the field to copy from
                            //customFieldValue2: N/A

                            $CustomFields = TransactionEmails::GetCachedCustomFields($ArrayCampaign['RelOwnerUserID']);
                            $CopyFieldInformation = $CustomFields[$state['customFieldValue']] ?? null;

                            if (empty($CopyFieldInformation)) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_COPY_FIELD,
                                );
                            }
                            break;

                        case CustomFields::CALCULATE_OP_NUMBER_ADD:
                        case CustomFields::CALCULATE_OP_NUMBER_SUBTRACT:
                            //customFieldValue: number (unsigned) to add or subtract
                            //customFieldValue2: N/A

                            if (
                                CampaignsProcessFlow::ValidateCustomField(
                                    $ArrayCampaign['RelOwnerUserID'],
                                    $state['customFieldID'],
                                    $state['customFieldValue'],
                                    $state,
                                    $TodoList
                                )
                            ) {
                                if ($state['customFieldValue'] < 0 || $state['customFieldValue'] > 9999999) {
                                    $TodoList[] = array(
                                        $state['id'],
                                        static::PROCESSFLOW_VALIDATION_ERROR_ACTION_FIELD_VALUE_OUTOFRANGE,
                                    );
                                }
                            }

                            break;
                        case CustomFields::CALCULATE_OP_NUMBER_RANDOM:
                            //customFieldValue: lower end of range for random
                            //customFieldValue2: upper end of range for random

                            $max_number = 9999;

                            if (
                                CampaignsProcessFlow::ValidateCustomField(
                                    $ArrayCampaign['RelOwnerUserID'],
                                    $state['customFieldID'],
                                    $state['customFieldValue'],
                                    $state,
                                    $TodoList
                                )
                            ) {
                                if ($state['customFieldValue'] < -$max_number || $state['customFieldValue'] > $max_number) {
                                    //user/internal error: minimum out of range
                                    $TodoList[] = array(
                                        $state['id'],
                                        static::PROCESSFLOW_VALIDATION_ERROR_ACTION_FIELD_VALUE_OUTOFRANGE,
                                    );
                                }
                            }

                            if (
                                CampaignsProcessFlow::ValidateCustomField(
                                    $ArrayCampaign['RelOwnerUserID'],
                                    $state['customFieldID'],
                                    $state['customFieldValue2'],
                                    $state,
                                    $TodoList
                                )
                            ) {
                                if ($state['customFieldValue2'] < -$max_number || $state['customFieldValue2'] > $max_number) {
                                    //user/internal error: maximum out of range (10 chars)
                                    $TodoList[] = array(
                                        $state['id'],
                                        static::PROCESSFLOW_VALIDATION_ERROR_ACTION_FIELD_VALUE_OUTOFRANGE,
                                    );
                                }
                            }

                            if ($state['customFieldValue'] >= $state['customFieldValue2']) {
                                //user error: maximum <= minimum
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_VALUE,
                                );
                            }

                            break;
                        case CustomFields::CALCULATE_OP_DATETIME_NOW:
                        case CustomFields::CALCULATE_OP_DATETIME_INCREASE:
                        case CustomFields::CALCULATE_OP_DATETIME_DECREASE:
                            //customFieldValue contains a relative strtotime string (unsigned) => 3 days 12:00
                            //customFieldValue2 contains the time operator for datetime fields (use now || current time, set fix time, round to full hour)
                            //delayDayOfWeek contains the weekday constraint (array of weekdays with 1==Monday)

                            $CustomFields = TransactionEmails::GetCachedCustomFields($ArrayCampaign['RelOwnerUserID']);
                            $ArrayCustomField = $CustomFields[$state['customFieldID']] ?? null;

                            if (empty($ArrayCustomField)) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD,
                                );

                                break;
                            }

                            $check = strtotime($state['customFieldValue']);

                            if (
                                $check === false || $check < strtotime('-99 years') || $check > strtotime(
                                    '+99 years'
                                )
                            ) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_VALUE,
                                );
                            }

                            if ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATETIME || $ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
                                //user error: no weekday selected

                                if (
                                    !CampaignsProcessFlow::CheckDatetimeConstraints(
                                        CampaignsProcessFlow::PROCESSFLOW_CONDITION_FIELD_IS_WEEKDAY,
                                        $state['delayDayOfWeek']
                                    )
                                ) { //constrain weekdays
                                    $TodoList[] = array(
                                        $state['id'],
                                        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_WEEKDAY_CONSTRAINT,
                                    );
                                }
                            }

                            if ($state['type'] == CustomFields::TYPE_DATETIME) {
                                //check if a valid time option is set for datetime fields

                                $time_options = array(
                                    CustomFields::CALCULATE_OP_DATETIME_TIME_NOW,
                                    CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND,
                                    CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_FLOOR,
                                    CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_CEIL,
                                    CustomFields::CALCULATE_OP_VALUE
                                );

                                $str_to_time = explode(' ', $state['customFieldValue']);

                                if (!in_array($state['customFieldValue2'], $time_options)) {
                                    $TodoList[] = array(
                                        $state['id'],
                                        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_OPERATOR,
                                    );
                                } elseif (
                                    $state['customFieldValue2'] == CustomFields::CALCULATE_OP_VALUE && strtotime(
                                        $str_to_time[2]
                                    ) === false
                                ) {
                                    //the time option for datetime field is set to fix value => there must be time string in customFieldValue2
                                    $TodoList[] = array(
                                        $state['id'],
                                        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_VALUE,
                                    );
                                }
                            }

                            break;
                        default:
                            //invalid or no field operation set
                            if (empty($state['customFieldID'])) {
                                $TodoList[] = array(
                                    $state['id'],
                                    CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD,
                                );
                            }
                    }

                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT:
                    switch ($state['delayType']) {
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_IMMEDIATELY: //delay from current time
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MINUTES:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_HOURS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_DAYS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_WEEKS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MONTHS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_YEARS:
                            $max_delay = ($state['delayType'] == CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MONTHS || $state['delayType'] == CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_YEARS) ? 99 : 999;

                            if (
                                !is_numeric($state['delayTime']) || !is_int(
                                    $state['delayTime'] + 0
                                ) || $state['delayTime'] < 0 || $state['delayTime'] > $max_delay
                            ) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_INVALID_TIME_DELAY
                                );
                            }

                            break;

                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MINUTES:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_HOURS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_DAYS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_WEEKS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MONTHS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_YEARS:
                            $CustomFields = TransactionEmails::GetCachedCustomFields($ArrayCampaign['RelOwnerUserID']);
                            $ArrayCustomField = $CustomFields[$state['delayField']] ?? null;
                            if (empty($ArrayCustomField)) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD
                                );
                            } elseif ($ArrayCustomField['FieldTypeEnum'] != CustomFields::TYPE_DATETIME) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_INVALID_FIELD_TYPE
                                );
                            }

                            $max_delay = ($state['delayType'] == CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MONTHS || $state['delayType'] == CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_YEARS) ? 99 : 999;

                            if (
                                !is_numeric($state['delayTime']) || !is_int(
                                    $state['delayTime'] + 0
                                ) || $state['delayTime'] < -$max_delay || $state['delayTime'] > $max_delay
                            ) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_INVALID_TIME_DELAY
                                );
                            }

                            break;

                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_BIRTHDAY:
                            $CustomFields = TransactionEmails::GetCachedCustomFields($ArrayCampaign['RelOwnerUserID']);
                            $ArrayCustomField = $CustomFields[$state['delayField']] ?? null;
                            if (empty($ArrayCustomField)) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD
                                );
                            } elseif ($ArrayCustomField['FieldTypeEnum'] != CustomFields::TYPE_DATE) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_INVALID_FIELD_TYPE
                                );
                            }
                            break;
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CALENDAR:
                            $invalidCalendar = false;
                            if (!empty($state['calendarIDs'])) {
                                foreach ($state['calendarIDs'] as $cid) {
                                    if (!ToolCalendar::FromID($ArrayCampaign['RelOwnerUserID'], $cid)) {
                                        $invalidCalendar = true;
                                        break;
                                    }
                                }
                            }
                            if (empty($state['calendarIDs']) || $invalidCalendar) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_INVALID_CALENDAR
                                );
                            }
                            break;
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_LIMITER:
                            //we just need to check if we have a valid limiterPeriod and limiterValue
                            //delayHours,..., delayMonths as well as excludeHolidays have UI checks and default values

                            if (
                                intval($state['limiterValue']) <= 0 || !in_array($state['limiterPeriod'], array(
                                    CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_HOURS,
                                    CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_DAYS,
                                    CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_WEEKS,
                                    CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MONTHS,
                                    CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_YEARS,
                                ))
                            ) {
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_INVALID_LIMITER_PERIOD,
                                );
                            }
                            break;
                        default:
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_ERROR_INVALID_DELAY_TYPE
                            );
                    }
                    break;
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_GOTO:
                    $target = static::FindState($ArrayCampaign, $state['next']);
                    if (empty($target)) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_TARGET
                        );
                    } elseif (
                        !static::CheckForEndlessLoops(
                            $ArrayCampaign,
                            $state['next'],
                            $state['id'],
                            count($ArrayCampaign['ProcessFlow']['states'])
                        )
                    ) {
                        // found an endless loops
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_POSSIBLE_ENDLESS_LOOP
                        );
                    }
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STARTAUTOMATION:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STOPAUTOMATION:
                    // $state['campaignID'] needs to be a different to automation
                    if ($ArrayCampaign['CampaignID'] == $state['campaignID']) {
                        if ($state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STARTAUTOMATION) {
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_START_ITSELF
                            );
                        } else {
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_STOP_ITSELF
                            );
                        }
                    }

                    // $state['campaignID'] needs to be a valid automation id
                    /** @var Campaigns $ObjectCampaign */
                    $ObjectCampaign = Campaigns::FromID($ArrayCampaign['RelOwnerUserID'], $state['campaignID']);
                    if (empty($ObjectCampaign)) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_CAMPAIGN
                        );
                    } elseif (
                        !$ObjectCampaign->IsProcessflow(
                            $ObjectCampaign->getData('AutoResponderTriggerTypeEnum')
                        )
                    ) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_CAMPAIGN
                        );
                    };
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SPLITTEST:
                    // $state['splittestID'] needs to be a valid splittest id
                    /** @var SplitTests $ObjectSplittest */
                    $ObjectSplittest = SplitTests::FromID($ArrayCampaign['RelOwnerUserID'], $state['splittestID']);
                    if (empty($ObjectSplittest)) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_SPLITTEST
                        );
                    } else {
                        $ArraySplittest = $ObjectSplittest->GetData();

                        if ($ArraySplittest['RelCampaignID'] != $ArrayCampaign['CampaignID']) {
                            // splittest from another campaign (inconsistency = bug)
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_SPLITTEST
                            );
                        };

                        if ($ArraySplittest['TestDuration'] < 1 || $ArraySplittest['TestDuration'] > 99999999) {
                            // maximium is limited to 8 diggits, which is 1157 days
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_INVALID_TESTDURATION
                            );
                        };

                        if (count($ArraySplittest['Variants']) < 2) {
                            // at least 2 variants
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANTS_MISSING
                            );
                        } else {
                            $sumweights = 0;
                            foreach ($ArraySplittest['Variants'] as $variant_id => $variant) {
                                // check variant action

                                $EntityObject = $ObjectSplittest->GetVariantEntity($variant_id);
                                if (empty($EntityObject)) {
                                    // at least 2 variants
                                    $TodoList[] = array(
                                        $state['id'],
                                        static::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_NO_VARIANT_ENTITY,
                                        $variant_id
                                    );
                                } else {
                                    if (get_class($EntityObject) == CampaignsProcessFlow::class) {
                                        // $state['campaignID'] needs to be a different to automation
                                        if ($ArrayCampaign['CampaignID'] == $EntityObject->GetData('CampaignID')) {
                                            $TodoList[] = array(
                                                $state['id'],
                                                static::PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_START_ITSELF,
                                                $variant_id
                                            );
                                        }
                                    } elseif (get_class($EntityObject) == EmailsAutomationEmail::class) {
                                        static::ValidateAutomationEmail(
                                            $EntityObject->GetData(),
                                            $state,
                                            $TodoList,
                                            $variant_id
                                        );
                                    } elseif (get_class($EntityObject) == EmailsAutomationSMS::class) {
                                        static::ValidateAutomationSMS(
                                            $EntityObject->GetData(),
                                            $state,
                                            $TodoList,
                                            $variant_id
                                        );
                                    } elseif (get_class($EntityObject) == ToolOutboundGeneral::class) {
                                        static::ValidateOutbound(
                                            $EntityObject->GetData(),
                                            $state,
                                            $TodoList,
                                            $variant_id
                                        );
                                    } elseif (get_class($EntityObject) == Tag::class) {
                                        // no checks
                                    } else {
                                        // wrong entity class: same error as if no entity given
                                        $TodoList[] = array(
                                            $state['id'],
                                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_NO_VARIANT_ENTITY,
                                            $variant_id
                                        );
                                    }
                                }

                                // check variant weight

                                if ($variant['Weight'] < 1 || $variant['Weight'] > 99) {
                                    // at least 2 variants
                                    $TodoList[] = array(
                                        $state['id'],
                                        static::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_INVALID_WEIGHT
                                    );
                                } else {
                                    $sumweights += $variant['Weight'];
                                }

                                // check variant taggings

                                $ArrayTag = Tag::RetrieveTag($ArrayCampaign['RelOwnerUserID'], $variant['TagID']);
                                if (empty($ArrayTag)) {
                                    $TodoList[] = array(
                                        $state['id'],
                                        static::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_INVALID_TAG,
                                        $variant_id
                                    );
                                }
                            } // end foreach variant

                            if ($sumweights != 100) {
                                // at least 2 variants
                                $TodoList[] = array(
                                    $state['id'],
                                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_WEIGHTS_NOT_100
                                );
                            }
                        }
                    }
                    break;
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_REMOVE:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD:
                    // $state['audienceID'] needs to be a valid audienceID
                    $ObjectAudience = ToolFacebookAudience::FromID(
                        $ArrayCampaign['RelOwnerUserID'],
                        $state['audienceID']
                    );
                    if (!$ObjectAudience) {
                        $TodoList[] = array(
                            $state['id'],
                            static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FACEBOOK_AUDIENCE
                        );
                    } else {
                        $AccessToken = $ObjectAudience->GetValidToken();
                        if (!$AccessToken[0] && $AccessToken[1] == ToolFacebookAudience::ERROR_ACCESS_EXPIRED) {
                            $TodoList[] = array(
                                $state['id'],
                                static::PROCESSFLOW_VALIDATION_ERROR_ACTION_FACEBOOK_AUDIENCE_NOT_CONNECTED,
                                -1,
                                ToolFacebookAudience::class,
                                $ObjectAudience->GetData('ToolID'),
                                $ObjectAudience->GetData('Name'),
                            );
                        }
                    }
                    break;
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DETECT_NAME:
                    $CustomFields = TransactionEmails::GetCachedCustomFields($ArrayCampaign['RelOwnerUserID']);
                    $ArrayCustomField = (empty($state['customFieldID'])) ? array() : $CustomFields[$state['customFieldID']];
                    if (empty($ArrayCustomField) || $ArrayCustomField['FieldTypeEnum'] != CustomFields::TYPE_SINGLE) {
                        $TodoList[] = array(
                            $state['id'],
                            CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD,
                        );
                    }
                    if (!empty($state['customField2ID'])) {
                        //detecting the last name is optional
                        $ArrayCustomField = (empty($state['customField2ID'])) ? array() : $CustomFields[$state['customField2ID']];
                        if (empty($ArrayCustomField) || $ArrayCustomField['FieldTypeEnum'] != CustomFields::TYPE_SINGLE) {
                            $TodoList[] = array(
                                $state['id'],
                                CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD,
                            );
                        }
                    }
                    break;
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DETECT_GENDER:
                    $CustomFields = TransactionEmails::GetCachedCustomFields($ArrayCampaign['RelOwnerUserID']);
                    $ArrayCustomField = (empty($state['customFieldID'])) ? array() : $CustomFields[$state['customFieldID']];
                    if (empty($ArrayCustomField) || $ArrayCustomField['FieldTypeEnum'] != CustomFields::TYPE_SINGLE) {
                        //no source field selected
                        $TodoList[] = array(
                            $state['id'],
                            CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD,
                        );
                    }

                    //check tag by gender (all tags are optional)
                    $emptyTags = true;
                    $TagIDs = array($state['tagID'], $state['tag2ID'], $state['tag3ID']);
                    foreach ($TagIDs as $TagID) {
                        if (!empty($TagID)) {
                            $emptyTags = false;
                            $ArrayTag = Tag::RetrieveTag($ArrayCampaign['RelOwnerUserID'], $TagID);
                            if (empty($ArrayTag)) {
                                $TodoList[] = array(
                                    $state['id'],
                                    CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_TAG,
                                );
                            }
                        }
                    }
                    //check write value by gender (optional)
                    if (!empty($state['customField2ID'])) {
                        //user wants to write a value by gender but specified an invalid custom field
                        $ArrayCustomField = $CustomFields[$state['customField2ID']];
                        if (empty($ArrayCustomField)) {
                            //target field not found
                            $TodoList[] = [
                                $state['id'],
                                CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD,
                            ];
                        }
                    }
                    if (empty($state['customField2ID']) && $emptyTags) {
                        //the user did not select a target field nor a tag
                        $TodoList[] = array(
                            $state['id'],
                            CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_TAG_FIELD,
                        );
                    } elseif (
                        !empty($state['customField2ID']) && empty($state['customFieldValue']) &&
                        empty($state['customFieldValue2']) && empty($state['customFieldValue3'])
                    ) {
                        //the user wants to write values into a field but did not specify values
                        $TodoList[] = array(
                            $state['id'],
                            CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_VALUE,
                        );
                    }
                    break;
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT:
                    $VarFullContact = VarFullContact::GetVariable($ArrayCampaign['RelOwnerUserID'], []);
                    if (empty($VarFullContact['apiKey'])) {
                        $TodoList[] = array(
                            $state['id'],
                            CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_FULLCONTACT_APIKEY_MISSING,
                        );
                    }
                    break;
                default:
                    $TodoList[] = array(
                        $state['id'],
                        CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_NODE_INVALID
                    );
                    break;
            }
        }

        $possibleDelayStateIds = self::ValidateAutomationPossibleDelay($ArrayCampaign);

        foreach ($possibleDelayStateIds as $delayStateId) {
            $TodoList[] = array(
                $delayStateId,
                self::PROCESSFLOW_VALIDATION_WARNING_POSSIBLE_DELAY
            );
        }

        return $TodoList;
    }

    /**
     * @param array<array<mixed>> $elements
     * @param array<mixed> $current
     * @param array<int> $visited
     * @param array<int, array<int>> $loops
     * @param int|null $startingGotoId
     * @return void
     */
    public static function explorePaths(array $elements, array $current, array &$visited, array &$loops, ?int $startingGotoId, int &$pathCount): void
    {
        $pathCount++;

        if ($pathCount >= self::PROCESSFLOW_GOTO_LOOP_DETECTION_MAX_PATHS) {
            return;
        }

        // if we've visited this node before in the current path, it is a loop
        if (in_array($current['id'], $visited)) {
            if ($startingGotoId !== null && $current['id'] === $startingGotoId) {
                // only add to loops if it is the same 'goto' element we started with
                $loops[] = $visited;
            }
            return;
        }

        $visited[] = $current['id'];

        // if it's a 'goto' element: start or continue tracking for loops
        if (isset($current['type']) && $current['type'] === 'goto') {
            if ($startingGotoId === null) {
                $startingGotoId = $current['id'];
            }

            if (isset($current['next']) && $current['next'] && isset($elements[$current['next']]) && is_array($elements[$current['next']])) {
                self::explorePaths($elements, $elements[$current['next']], $visited, $loops, $startingGotoId, $pathCount);
            }
        } else {
            if (isset($current['next']) && $current['next'] && isset($elements[$current['next']]) && is_array($elements[$current['next']])) {
                self::explorePaths($elements, $elements[$current['next']], $visited, $loops, $startingGotoId, $pathCount);
            }
            if (isset($current['nextNo']) && $current['nextNo'] && isset($elements[$current['nextNo']]) && is_array($elements[$current['nextNo']])) {
                self::explorePaths($elements, $elements[$current['nextNo']], $visited, $loops, $startingGotoId, $pathCount);
            }
        }

        array_pop($visited);
    }

    /**
     * Checks if there is a PROCESSFLOW_STATE_TYPE_SENDEMAIL action in the campaign
     * @param array<string, array<string, array<string, array<string>>>> $arrayCampaign
     */
    public static function hasEmailAction(array $arrayCampaign): bool
    {
        foreach ($arrayCampaign['ProcessFlow']['states'] as $element) {
            if (
                is_array($element)
                && isset($element['type'])
                && $element['type'] == self::PROCESSFLOW_STATE_TYPE_SENDEMAIL
            ) {
                return true;
            }
        }
        return false;
    }

    /**
     * Returns an array of stateIds (goto states) that could cause a delay in the process flow
     * @param array<string, array<string, array<string, array<string>>>> $arrayCampaign
     * @return array<int>
     */
    public static function validateAutomationPossibleDelay(array $arrayCampaign): array
    {
        // if there are no email send actions we don't need to check for loops at all
        if (!self::hasEmailAction($arrayCampaign)) {
            return [];
        }

        $elements = array_column($arrayCampaign['ProcessFlow']['states'], null, 'id');
        $loops = [];
        $pathCount = 0;

        foreach ($elements as $element) {
            if ($pathCount >= self::PROCESSFLOW_GOTO_LOOP_DETECTION_MAX_PATHS) {
                watchdog(
                    'campaigns',
                    'Reached maximum number of paths to explore for campaign @campaignId',
                    ['@campaignId' => $arrayCampaign['CampaignID']]
                );
                break;
            }
            if ($element['type'] === 'goto') {
                $visited = [];
                self::explorePaths($elements, $element, $visited, $loops, null, $pathCount);
            }
        }

        foreach ($loops as $key => $loop) {
            $emailInLoop = false;
            foreach ($loop as $stateId) {
                if ($elements[$stateId]['type'] === self::PROCESSFLOW_STATE_TYPE_SENDEMAIL) {
                    $emailInLoop = true;
                    break;
                }
            }
            if (!$emailInLoop) {
                unset($loops[$key]);
            }
        }
        return array_column($loops, 0);
    }

    /**
     * check whether an automation email is valid
     * @param $ArrayEmail
     * @param $TodoList
     * @param $SplittestVariant : array index [0..n] of the splittest variant sending the email
     */
    public static function ValidateAutomationEmail($ArrayEmail, $state, &$TodoList, $SplittestVariant = -1)
    {
        if (strlen(trim($ArrayEmail['Subject'])) == 0) {
            $TodoList[] = array(
                $state['id'],
                static::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_SUBJECT,
                $SplittestVariant,
                ($ArrayEmail['EmailType'] == Emails::TYPE_AUTOMATIONEMAIL) ? EmailsAutomationEmail::class : EmailsNotificationEmail::class,
                $ArrayEmail['EmailID'],
                $ArrayEmail['EmailName'],
            );
        };

        if ($ArrayEmail['Version']) {
            //new email editor

            $LatestRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail, true);
            if (empty($LatestRevision)) {
                //the email has never been published
                $TodoList[] = array(
                    $state['id'],
                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NOT_PUBLISHED,
                    $SplittestVariant,
                    ($ArrayEmail['EmailType'] == Emails::TYPE_AUTOMATIONEMAIL) ? EmailsAutomationEmail::class : EmailsNotificationEmail::class,
                    $ArrayEmail['EmailID'],
                    $ArrayEmail['EmailName'],
                );
            }
        } else {
            //old email editor

            if ($ArrayEmail['ContentTypeEnum'] == Emails::CONTENT_TYPE_PLAIN) {
                if (strlen(trim($ArrayEmail['PlainContent'])) == 0) {
                    $TodoList[] = array(
                        $state['id'],
                        static::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_CONTENT,
                        $SplittestVariant,
                        ($ArrayEmail['EmailType'] == Emails::TYPE_AUTOMATIONEMAIL) ? EmailsAutomationEmail::class : EmailsNotificationEmail::class,
                        $ArrayEmail['EmailID'],
                        $ArrayEmail['EmailName'],
                    );
                }
            } else {
                if (strlen(trim($ArrayEmail['HTMLContent'])) == 0) {
                    $TodoList[] = array(
                        $state['id'],
                        static::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_CONTENT,
                        $SplittestVariant,
                        ($ArrayEmail['EmailType'] == Emails::TYPE_AUTOMATIONEMAIL) ? EmailsAutomationEmail::class : EmailsNotificationEmail::class,
                        $ArrayEmail['EmailID'],
                        $ArrayEmail['EmailName'],
                    );
                }
            }
        }
    }

    /**
     * check whether an automation sms is valid
     * @param $ArrayEmail
     * @param $TodoList
     * @param $SplittestVariant : array index [0..n] of the splittest variant sending the sms
     */
    public static function ValidateAutomationSMS($ArrayEmail, $state, &$TodoList, $SplittestVariant = -1)
    {
        if (strlen(trim($ArrayEmail['PlainContent'])) == 0) {
            $TodoList[] = array(
                $state['id'],
                static::PROCESSFLOW_VALIDATION_ERROR_ACTION_SMS_NO_CONTENT,
                $SplittestVariant,
                ($ArrayEmail['EmailType'] == Emails::TYPE_AUTOMATIONSMS) ? EmailsAutomationSMS::class : EmailsNotificationSMS::class,
                $ArrayEmail['EmailID'],
                $ArrayEmail['EmailName'],
            );
        }
    }

    /**
     * check whether an outbound is valid
     * @param $ArrayEmail
     * @param $TodoList ,
     * @param $SplittestVariant : array index [0..n] of the splittest variant triggering the outbound
     */
    public static function ValidateOutbound($ArrayOutbound, $state, &$TodoList, $SplittestVariant = -1)
    {
        if ($ArrayOutbound['ToolType'] == MarketingTools::TOOL_TYPE_PLUGIN) {
            // a plugin has multiple outbounds which are predefined, so we do not validate user input
            return;
        }

        if (empty($ArrayOutbound['ActivationURL'])) {
            $TodoList[] = array(
                $state['id'],
                static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_ACTIVATION_URL,
                $SplittestVariant,
                ToolOutboundGeneral::class,
                $ArrayOutbound['ToolID'],
                $ArrayOutbound['Name'],
            );
        }
    }

    /**
     * validate custom field (exists, value is not empty and in the right format for the field type)
     * @param $UserID
     * @param $CustomFieldID
     * @param $value : the value to be checked
     * @param $state : for state id and type
     * @param $TodoList ,
     */
    public static function ValidateCustomField($UserID, $CustomFieldID, $value, $state, &$TodoList)
    {
        $CustomFields = TransactionEmails::GetCachedCustomFields($UserID);
        $ArrayCustomField = $CustomFields[$CustomFieldID] ?? null;

        if (empty($ArrayCustomField)) {
            if ($state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SETFIELD) {
                $TodoList[] = array(
                    $state['id'],
                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD,
                );
            } else {
                $TodoList[] = array(
                    $state['id'],
                    static::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_FIELD,
                );
            }

            return false;
        } elseif (
            $state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SETFIELD &&
            in_array($state['customFieldOp'], array(
                CustomFields::CALCULATE_OP_VALUE,
                CustomFields::CALCULATE_OP_NUMBER_ADD,
                CustomFields::CALCULATE_OP_NUMBER_SUBTRACT,
                CustomFields::CALCULATE_OP_TEXT_REPLACE
            ))
        ) {
            $error = CustomFields::ValidateCustomFieldValue($ArrayCustomField, $value, true);

            if (CustomFields::isEmpty($ArrayCustomField, $value) || !$error[0]) {
                $TodoList[] = array(
                    $state['id'],
                    static::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD_VALUE,
                );
                return false;
            }
        }

        return true;
    }

    /**
     * @param CampaignArray $campaign
     * @param int $subscriberId
     * @param int $referenceId
     */
    public static function hasStartTag(array $campaign, int $subscriberId, int $referenceId): bool
    {
        return static::hasAutomationTag(
            $campaign,
            $subscriberId,
            $referenceId,
            'AutomationStartedSmartTagID'
        );
    }

    /**
     * @param CampaignArray $campaign
     * @param int $subscriberId
     * @param int $referenceId
     * @param string $tagType
     * @return bool
     */
    public static function hasAutomationTag(array $campaign, int $subscriberId, int $referenceId, string $tagType): bool
    {
        $ArrayTaggings = TransactionEmails::GetCachedTagging(
            $campaign['RelOwnerUserID'],
            $subscriberId,
            $referenceId,
            false
        );
        return !empty($ArrayTaggings[$campaign[$tagType]]);
    }

    /**
     * Traverse the processflow tree with a callback function to process the entities
     *
     * @param array $Results data structure to collect items
     * @param callable $Callback
     *   callback_function($ObjectCampaign, $key, $category, $Results)
     *   Note: this needs to return the (modified) $Results array
     *   Note: define the callback key parameter as call-by-reference (&$key) to modify the tree
     * @return array always returns the modfied $Results
     */
    public function TraverseProcessFlow($Results, $Callback)
    {
        foreach ($this->DataFlat['ProcessFlow']['states'] as &$state) {
            switch ($state['type']) {
                case static::PROCESSFLOW_STATE_TYPE_START:
                case static::PROCESSFLOW_STATE_TYPE_DECISION:
                case static::PROCESSFLOW_STATE_TYPE_GOAL:
                    if (!empty($state['segments'])) {
                        // check segments
                        foreach ($state['segments'] as &$segment) {
                            if (!empty($segment['conditions'])) {
                                // check conditions
                                foreach ($segment['conditions'] as &$condition) {
                                    switch ($condition['op']) {
                                        case static::PROCESSFLOW_CONDITION_IS_TAGGED:
                                        case static::PROCESSFLOW_CONDITION_IS_NOT_TAGGED:
                                        case static::PROCESSFLOW_CONDITION_SMARTLINK_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_SMARTLINK_NOT_CLICKED:
                                            $Results = call_user_func_array($Callback, array(
                                                $this,
                                                &$condition['field'],
                                                'tags',
                                                $Results
                                            ));
                                            break;
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_SENT:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_OPENED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_VIEWED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_CONVERTED:
                                        case static::PROCESSFLOW_CONDITION_KAJABI_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_KAJABI_DEACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_OUTBOUND_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_SENT:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_OPENED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_VIEWED:
                                        case static::PROCESSFLOW_CONDITION_SMS_SENT:
                                        case static::PROCESSFLOW_CONDITION_SMS_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_AUTOMATION_STARTED:
                                        case static::PROCESSFLOW_CONDITION_AUTOMATION_FINISHED:
                                        case static::PROCESSFLOW_CONDITION_TAGGING_PIXEL_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_APIKEY:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_BUSINESSCARD:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REQUEST:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_SMS:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_FORMS:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_SUBSEQUENT_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_DEFFERED_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REFUNDED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_CHARGEDBACK:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_CANCELED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_RESUMED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_DIGISTORE_AFFILIATION:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_LANDING_PAGE_SUBSCRIBED:
                                        case static::PROCESSFLOW_CONDITION_PAYMENT_COMPLETED:
                                        case static::PROCESSFLOW_CONDITION_PAYMENT_EXPIRED:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_READY:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_STARTED:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_INPROGRESS:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_FINISHED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_SENT:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_OPENED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_VIEWED:
                                        case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CONVERTED:
                                        case static::PROCESSFLOW_CONDITION_KAJABI_NOT_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_KAJABI_NOT_DEACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_OUTBOUND_NOT_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_NOT_SENT:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_NOT_OPENED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_NOT_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_EMAIL_NOT_VIEWED:
                                        case static::PROCESSFLOW_CONDITION_SMS_NOT_SENT:
                                        case static::PROCESSFLOW_CONDITION_SMS_NOT_CLICKED:
                                        case static::PROCESSFLOW_CONDITION_AUTOMATION_NOT_STARTED:
                                        case static::PROCESSFLOW_CONDITION_AUTOMATION_NOT_FINISHED:
                                        case static::PROCESSFLOW_CONDITION_TAGGING_PIXEL_NOT_ACTIVATED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_APIKEY:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_BUSINESSCARD:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_REQUEST:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_SMS:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_FORMS:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_SUBSEQUENT_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_DEFFERED_PAYMENT:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_REFUNDED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_CHARGEDBACK:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_CANCELED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_RESUMED:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NO_DIGISTORE_AFFILIATION:
                                        case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_LANDING_PAGE_SUBSCRIBED:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_READY:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_STARTED:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_INPROGRESS:
                                        case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_FINISHED:
                                            $Results = call_user_func_array($Callback, array(
                                                $this,
                                                &$condition['field'],
                                                'smarttags',
                                                $Results,
                                                $state
                                            ));
                                            break;
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_NOT:
                                        case static::PROCESSFLOW_CONDITION_FIELD_CONTAINS:
                                        case static::PROCESSFLOW_CONDITION_FIELD_CONTAINS_NOT:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_EMPTY:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_NOT_EMPTY:
                                        case static::PROCESSFLOW_CONDITION_FIELD_STARTS_WITH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_STARTS_NOT_WITH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_ENDS_WITH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_ENDS_NOT_WITH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_LESS_THAN:
                                        case static::PROCESSFLOW_CONDITION_FIELD_LESS_EQUAL_THAN:
                                        case static::PROCESSFLOW_CONDITION_FIELD_GREATER_THAN:
                                        case static::PROCESSFLOW_CONDITION_FIELD_GREATER_EQUAL_THAN:
                                        case static::PROCESSFLOW_CONDITION_FIELD_BEFORE:
                                        case static::PROCESSFLOW_CONDITION_FIELD_AFTER:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_WEEKDAY:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_MONTH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_DAY_OF_MONTH:
                                        case static::PROCESSFLOW_CONDITION_FIELD_BEFORE_TIME:
                                        case static::PROCESSFLOW_CONDITION_FIELD_AFTER_TIME:
                                            $Results = call_user_func_array($Callback, array(
                                                $this,
                                                &$condition['field'],
                                                'customfields',
                                                $Results
                                            ));
                                            break;
                                        case static::PROCESSFLOW_CONDITION_FIELD_IS_HOLIDAY:
                                        case static::PROCESSFLOW_CONDITION_TODAY_IS_HOLIDAY:
                                            $Results = call_user_func_array($Callback, array(
                                                $this,
                                                &$condition['value'],
                                                'calendar',
                                                $Results
                                            ));
                                            break;
                                        case static::PROCESSFLOW_CONDITION_SEGMENT_IN:
                                        case static::PROCESSFLOW_CONDITION_SEGMENT_NOT_IN:
                                            $Results = call_user_func_array($Callback, array(
                                                $this,
                                                &$condition['value2'],
                                                'segment',
                                                $Results
                                            ));
                                            break;
                                    }
                                }
                            }
                        }
                    }
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDSMS:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS:
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['emailID'],
                        'emails',
                        $Results
                    ));
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_OUTBOUND:
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['outboundID'],
                        'outbounds',
                        $Results
                    ));
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_TAGGING:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNTAGGING:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_RESTART:
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['tagID'],
                        'tags',
                        $Results
                    ));
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SETFIELD:
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['customFieldID'],
                        'customfields',
                        $Results
                    ));

                    if ($state['customFieldOp'] == CustomFields::CALCULATE_OP_COPY) {
                        $Results = call_user_func_array($Callback, array(
                            $this,
                            &$state['customFieldValue'], //id of the field to copy from
                            'customfields',
                            $Results
                        ));
                    }

                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT:
                    switch ($state['delayType']) {
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_BIRTHDAY:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_YEARS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MONTHS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_WEEKS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_DAYS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_HOURS:
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MINUTES:
                            $Results = call_user_func_array($Callback, array(
                                $this,
                                &$state['delayField'],
                                'customfields',
                                $Results
                            ));
                            break;
                        case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CALENDAR:
                            $Results = call_user_func_array($Callback, array(
                                $this,
                                &$state['calendarIDs'],
                                'calendar',
                                $Results
                            ));
                            break;
                    }
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STARTAUTOMATION:
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STOPAUTOMATION:
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['campaignID'],
                        'campaigns',
                        $Results
                    ));
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SPLITTEST:
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['splittestID'],
                        'splittests',
                        $Results
                    ));
                    /** @var SplitTests $ObjectSplittest */
                    $ObjectSplittest = SplitTests::FromID($this->GetData('RelOwnerUserID'), $state['splittestID']);
                    if (!empty($ObjectSplittest)) {
                        foreach ($ObjectSplittest->GetData('Variants') as $index => $variant) {
                            // process variant entities
                            $EntityObject = $ObjectSplittest->GetVariantEntity($index);
                            if (!empty($EntityObject)) {
                                // process action
                                switch (get_class($EntityObject)) {
                                    case CampaignsProcessFlow::class:
                                        $Results = call_user_func_array($Callback, array(
                                            $this,
                                            &$variant['EntityID'],
                                            'campaigns',
                                            $Results
                                        ));
                                        break;
                                    case EmailsAutomationEmail::class:
                                    case EmailsAutomationSMS::class:
                                        $Results = call_user_func_array($Callback, array(
                                            $this,
                                            &$variant['EntityID'],
                                            'emails',
                                            $Results
                                        ));
                                        break;
                                    case ToolOutboundGeneral::class:
                                        $Results = call_user_func_array($Callback, array(
                                            $this,
                                            &$variant['EntityID'],
                                            'outbounds',
                                            $Results
                                        ));
                                        break;
                                    case Tag::class:
                                        $Results = call_user_func_array($Callback, array(
                                            $this,
                                            &$variant['EntityID'],
                                            'tags',
                                            $Results
                                        ));
                                        break;
                                }
                            }
                            // process tagging count ids
                            /** @var Tag $TagObject */
                            $TagObject = Tag::FromID($this->GetData('RelOwnerUserID'), $variant['TagID']);
                            if (!empty($TagObject)) {
                                // process action
                                switch (get_class($TagObject)) {
                                    case Tag::class:
                                    case SmartLink::class:
                                        $Results = call_user_func_array($Callback, array(
                                            $this,
                                            &$variant['TagID'],
                                            'tags',
                                            $Results
                                        ));
                                        break;
                                    default:
                                        $Results = call_user_func_array($Callback, array(
                                            $this,
                                            &$variant['TagID'],
                                            'smarttags',
                                            $Results,
                                            $state
                                        ));
                                        break;
                                }
                            }
                        } // end foreach variant
                    }
                    break;
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD:
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['audienceID'],
                        'facebook audience',
                        $Results
                    ));
                    break;
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DETECT_NAME:
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['customFieldID'],
                        'customfields',
                        $Results
                    ));
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['customField2ID'],
                        'customfields',
                        $Results
                    ));
                    break;
                case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DETECT_GENDER:
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['customFieldID'],
                        'customfields',
                        $Results
                    ));
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['customField2ID'],
                        'customfields',
                        $Results
                    ));
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['tagID'],
                        'tags',
                        $Results
                    ));
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['tag2ID'],
                        'tags',
                        $Results
                    ));
                    $Results = call_user_func_array($Callback, array(
                        $this,
                        &$state['tag3ID'],
                        'tags',
                        $Results
                    ));
                    break;
            }
        }

        return $Results;
    }

    /**
     * Check a processflow condition
     *
     * @param $condition
     * @param $isAnd
     * @param $UserID
     * @param $SubscriberID
     *
     * @param $ReferenceID
     *
     * @return bool
     */
    public static function CheckCondition($condition, $isAnd, $UserID, $SubscriberID, $ReferenceID)
    {
        switch ($condition['op']) {
            case static::PROCESSFLOW_CONDITION_IS_ACTIVE_EMAIL:
            case static::PROCESSFLOW_CONDITION_IS_NOT_ACTIVE_EMAIL:
                /*
                field: --
                value: --
                 */
                $FullSubscriber = TransactionEmails::GetCachedFullSubscriber($UserID, $SubscriberID, $ReferenceID);
                $result = $FullSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED &&
                    $FullSubscriber['BounceType'] != Subscribers::BOUNCETYPE_HARD;
                return ($condition['op'] == static::PROCESSFLOW_CONDITION_IS_ACTIVE_EMAIL) ? $result : !$result;
                break;

            case static::PROCESSFLOW_CONDITION_IS_ACTIVE_SMS:
            case static::PROCESSFLOW_CONDITION_IS_NOT_ACTIVE_SMS:
                /*
                  field: --
                  value: --
                 */
                $FullSubscriber = TransactionEmails::GetCachedFullSubscriber($UserID, $SubscriberID, $ReferenceID);
                $result = $FullSubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED &&
                    $FullSubscriber['SMSBounceType'] != Subscribers::BOUNCETYPE_HARD;
                return ($condition['op'] == static::PROCESSFLOW_CONDITION_IS_ACTIVE_SMS) ? $result : !$result;
                break;
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_SEGMENT_IN:
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_SEGMENT_NOT_IN:
                // check if the subscriber is a customer in the marketing account and if he is/not is in a certain segment

                $ownerAccount = user_load($UserID);
                if (!VarSegment::canAccess($ownerAccount)) {
                    return false;
                }

                if (!isset(static::$CustomerAccountsForSegmentsCache[$SubscriberID])) {
                    $FullSubscriber = TransactionEmails::GetCachedFullSubscriber($UserID, $SubscriberID, $ReferenceID);
                    $account = user_load_by_mail($FullSubscriber['EmailAddress']);
                    static::$CustomerAccountsForSegmentsCache[$SubscriberID] = $account;
                }

                $customerAccount = static::$CustomerAccountsForSegmentsCache[$SubscriberID];

                if (!$customerAccount) {
                    // subscriber is not a customer
                    return false;
                }

                // get all the segments in the owner account ($UserID) and check them on the customer account
                $userSegments = VarSegment::getUserSegments($customerAccount, $UserID);

                if ($condition['op'] == CampaignsProcessFlow::PROCESSFLOW_CONDITION_SEGMENT_NOT_IN) {
                    return !$userSegments[$condition['value2']]['value'];
                }

                return $userSegments[$condition['value2']]['value'];

            case static::PROCESSFLOW_CONDITION_HAS_TAGGING:
            case static::PROCESSFLOW_CONDITION_IS_TAGGED:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_SENT:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_OPENED:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_CLICKED:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_VIEWED:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_CONVERTED:
            case static::PROCESSFLOW_CONDITION_KAJABI_ACTIVATED:
            case static::PROCESSFLOW_CONDITION_KAJABI_DEACTIVATED:
            case static::PROCESSFLOW_CONDITION_OUTBOUND_ACTIVATED:
            case static::PROCESSFLOW_CONDITION_EMAIL_SENT:
            case static::PROCESSFLOW_CONDITION_EMAIL_OPENED:
            case static::PROCESSFLOW_CONDITION_EMAIL_CLICKED:
            case static::PROCESSFLOW_CONDITION_EMAIL_VIEWED:
            case static::PROCESSFLOW_CONDITION_SMS_SENT:
            case static::PROCESSFLOW_CONDITION_SMS_CLICKED:
            case static::PROCESSFLOW_CONDITION_AUTOMATION_STARTED:
            case static::PROCESSFLOW_CONDITION_AUTOMATION_FINISHED:
            case static::PROCESSFLOW_CONDITION_SMARTLINK_CLICKED:
            case static::PROCESSFLOW_CONDITION_TAGGING_PIXEL_ACTIVATED:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_APIKEY:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_BUSINESSCARD:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_EVENT:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_REQUEST:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_SMS:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_FORMS:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_PAYMENT:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_SUBSEQUENT_PAYMENT:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_DEFFERED_PAYMENT:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_REFUNDED:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_CHARGEDBACK:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_CANCELED:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_RESUMED:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_DIGISTORE_AFFILIATION:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_LANDING_PAGE_SUBSCRIBED:
            case static::PROCESSFLOW_CONDITION_PAYMENT_COMPLETED:
            case static::PROCESSFLOW_CONDITION_PAYMENT_EXPIRED:
            case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_READY:
            case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_STARTED:
            case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_INPROGRESS:
            case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_FINISHED:
                /*
                 field: tag id
                 value: timespan 0..31622400
                */
                $ArrayTaggings = TransactionEmails::GetCachedTagging($UserID, $SubscriberID, $ReferenceID, false);
                if ($condition['field'] > 0) {
                    // specific tag in condition
                    $tagging = $ArrayTaggings[$condition['field']] ?? null;
                    if (!empty($tagging)) {
                        if ($condition['value'] > 0) {
                            // entity tagged within given timespan
                            if (time() - $tagging['SubscriptionDate'] <= $condition['value']) {
                                return true;
                            }
                        } else {
                            // entity tagged ever
                            return true;
                        }
                    }
                } else {
                    // 'any' tag in condition
                    if ($condition['op'] == static::PROCESSFLOW_CONDITION_HAS_TAGGING) {
                        $EntityClass = $condition['entityType'];
                        if (class_exists($EntityClass)) {
                            $category = $EntityClass::GetConditionSmartTagCategory($condition['action']);
                        }
                    } else {
                        //original mapping for flowchart
                        $category = static::$MatchTgaConditionToCategory[$condition['op']];
                    }

                    if (!empty($category)) {
                        foreach ($ArrayTaggings as $tagging) {
                            if ($tagging['Category'] == $category) {
                                if ($condition['value'] > 0) {
                                    // category tagged within given timespan
                                    if (time() - $tagging['SubscriptionDate'] <= $condition['value']) {
                                        return true;
                                    }
                                } else {
                                    // category tagged ever
                                    return true;
                                }
                            }
                        }
                    }
                }
                // no tagging found
                return false;
                break;

            case static::PROCESSFLOW_CONDITION_HAS_NOT_TAGGING:
            case static::PROCESSFLOW_CONDITION_IS_NOT_TAGGED:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_SENT:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_OPENED:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CLICKED:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_VIEWED:
            case static::PROCESSFLOW_CONDITION_CAMPAIGN_NOT_CONVERTED:
            case static::PROCESSFLOW_CONDITION_KAJABI_NOT_ACTIVATED:
            case static::PROCESSFLOW_CONDITION_KAJABI_NOT_DEACTIVATED:
            case static::PROCESSFLOW_CONDITION_OUTBOUND_NOT_ACTIVATED:
            case static::PROCESSFLOW_CONDITION_EMAIL_NOT_SENT:
            case static::PROCESSFLOW_CONDITION_EMAIL_NOT_OPENED:
            case static::PROCESSFLOW_CONDITION_EMAIL_NOT_CLICKED:
            case static::PROCESSFLOW_CONDITION_EMAIL_NOT_VIEWED:
            case static::PROCESSFLOW_CONDITION_SMS_NOT_SENT:
            case static::PROCESSFLOW_CONDITION_SMS_NOT_CLICKED:
            case static::PROCESSFLOW_CONDITION_AUTOMATION_NOT_STARTED:
            case static::PROCESSFLOW_CONDITION_AUTOMATION_NOT_FINISHED:
            case static::PROCESSFLOW_CONDITION_SMARTLINK_NOT_CLICKED:
            case static::PROCESSFLOW_CONDITION_TAGGING_PIXEL_NOT_ACTIVATED:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_APIKEY:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_BUSINESSCARD:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_EVENT:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_REQUEST:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_SMS:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_FORMS:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOTBY_PAYMENT:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_SUBSEQUENT_PAYMENT:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_DEFFERED_PAYMENT:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_REFUNDED:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_CHARGEDBACK:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_CANCELED:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_REBILL_NOT_RESUMED:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NO_DIGISTORE_AFFILIATION:
            case static::PROCESSFLOW_CONDITION_LISTBUILDING_NOT_LANDING_PAGE_SUBSCRIBED:
            case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_READY:
            case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_STARTED:
            case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_INPROGRESS:
            case static::PROCESSFLOW_CONDITION_PLUGIN_INBOUND_NOT_FINISHED:
                /*
                  field: tag id
                  value: timespan 0..31622400
                 */
                $ArrayTaggings = TransactionEmails::GetCachedTagging($UserID, $SubscriberID, $ReferenceID, false);
                if ($condition['field'] > 0) {
                    $tagging = $ArrayTaggings[$condition['field']] ?? null;
                    if (!empty($tagging)) {
                        if ($condition['value'] > 0) {
                            // entity tagged within given timespan
                            if (time() - $tagging['SubscriptionDate'] <= $condition['value']) {
                                return false;
                            }
                        } else {
                            // entity tagged ever
                            return false;
                        }
                    }
                } else {
                    if ($condition['op'] == static::PROCESSFLOW_CONDITION_HAS_NOT_TAGGING) {
                        $EntityClass = $condition['entityType'];
                        if (class_exists($EntityClass)) {
                            $category = $EntityClass::GetConditionSmartTagCategory($condition['action']);
                        }
                    } else {
                        //original mapping for flowchart
                        $category = static::$MatchTgaConditionToCategory[$condition['op']];
                    }

                    if (!empty($category)) {
                        foreach ($ArrayTaggings as $tagging) {
                            if ($tagging['Category'] == $category) {
                                if ($condition['value'] > 0) {
                                    // category tagged within given timespan
                                    if (time() - $tagging['SubscriptionDate'] <= $condition['value']) {
                                        return false;
                                    }
                                } else {
                                    // category tagged ever
                                    return false;
                                }
                            }
                        }
                    }
                }
                // no tagging found
                return true;
                break;

            // check custom field
            case static::PROCESSFLOW_CONDITION_FIELD_IS:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return ($FieldValue == $value);
            case static::PROCESSFLOW_CONDITION_FIELD_IS_NOT:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return ($FieldValue != $value);
            case static::PROCESSFLOW_CONDITION_FIELD_IS_EMPTY:
                [$FieldValue] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (empty(trim($FieldValue)));
            case static::PROCESSFLOW_CONDITION_FIELD_IS_NOT_EMPTY:
                [$FieldValue] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (!empty(trim($FieldValue)));
            case static::PROCESSFLOW_CONDITION_FIELD_CONTAINS:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (mb_strpos($FieldValue, $value) !== false);
            case static::PROCESSFLOW_CONDITION_FIELD_CONTAINS_NOT:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (mb_strpos($FieldValue, $value) === false);
            case static::PROCESSFLOW_CONDITION_FIELD_STARTS_WITH:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (mb_strpos($FieldValue, $value) === 0);
            case static::PROCESSFLOW_CONDITION_FIELD_STARTS_NOT_WITH:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (mb_strpos($FieldValue, $value) !== 0);
            case static::PROCESSFLOW_CONDITION_FIELD_ENDS_WITH:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return ($value == mb_substr($FieldValue, -mb_strlen($value)));
            case static::PROCESSFLOW_CONDITION_FIELD_ENDS_NOT_WITH:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return ($value != mb_substr($FieldValue, -mb_strlen($value)));
            case static::PROCESSFLOW_CONDITION_FIELD_LESS_THAN:
                [$FieldValue, $value, $type, $original] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (intval($FieldValue) < intval($value));
            case static::PROCESSFLOW_CONDITION_FIELD_LESS_EQUAL_THAN:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (intval($FieldValue) <= intval($value));
            case static::PROCESSFLOW_CONDITION_FIELD_GREATER_THAN:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (intval($FieldValue) > intval($value));
            case static::PROCESSFLOW_CONDITION_FIELD_GREATER_EQUAL_THAN:
                [$FieldValue, $value] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                return (intval($FieldValue) >= intval($value));
            case static::PROCESSFLOW_CONDITION_FIELD_BEFORE:
                [$FieldValue, $value, $FieldType] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                $value = ($FieldType == CustomFields::TYPE_DATE) ? date('Y-m-d', strtotime($value)) : date(
                    'Y-m-d H:i',
                    strtotime($value)
                );
                return ($FieldValue < $value);
            case static::PROCESSFLOW_CONDITION_FIELD_BEFORE_TIME:
                [$FieldValue, $value, $FieldType] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                if ($FieldType == CustomFields::TYPE_DATETIME) {
                    $value = date("Y-m-d $value");
                }
                return ($FieldValue < $value);
            case static::PROCESSFLOW_CONDITION_FIELD_AFTER:
                [$FieldValue, $value, $FieldType] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                $value = ($FieldType == CustomFields::TYPE_DATE) ? date('Y-m-d', strtotime($value)) : date(
                    'Y-m-d H:i',
                    strtotime($value)
                );
                return ($FieldValue > $value);
            case static::PROCESSFLOW_CONDITION_FIELD_AFTER_TIME:
                [$FieldValue, $value, $FieldType] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                if ($FieldType == CustomFields::TYPE_DATETIME) {
                    $value = date("Y-m-d $value");
                }
                return ($FieldValue > $value);
            case static::PROCESSFLOW_CONDITION_FIELD_IS_WEEKDAY:
                [$FieldValue, $value, $FieldType, $SubscriberField] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                if (!isset($SubscriberField)) {
                    //otherwise the current date would be checked
                    return false;
                }
                $weekday = is_numeric($SubscriberField) ? date('N', $SubscriberField) : false; // ISO-8601 day of week is 1 (monday) bis 7 (sunday)
                return CampaignsProcessFlow::CheckDatetimeConstraints($condition['op'], $value, $weekday);
            case static::PROCESSFLOW_CONDITION_FIELD_IS_MONTH:
                [$FieldValue, $value, $FieldType, $SubscriberField] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                if (!isset($SubscriberField)) {
                    //otherwise the current date would be checked
                    return false;
                }
                $month = is_numeric($SubscriberField) ? date('n', $SubscriberField) : false;
                return CampaignsProcessFlow::CheckDatetimeConstraints($condition['op'], $value, $month);
            case static::PROCESSFLOW_CONDITION_FIELD_IS_DAY_OF_MONTH:
                [$FieldValue, $value, $FieldType, $SubscriberField] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                if (!isset($SubscriberField)) {
                    //otherwise the current date would be checked
                    return false;
                }
                $day = is_numeric($SubscriberField) ? date('j', $SubscriberField) : false;
                return CampaignsProcessFlow::CheckDatetimeConstraints($condition['op'], $value, $day);
            case static::PROCESSFLOW_CONDITION_FIELD_IS_HOLIDAY:
                [$FieldValue, $value, $FieldType, $SubscriberField] = CampaignsProcessFlow::GetConditionFieldParams(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $condition
                );
                //$value is an array of ToolCalendarIDs
                if (empty($value)) {
                    //no calendars specified => no holidays
                    return false;
                }
                return ToolCalendar::IsHoliday($UserID, $value, strtotime($FieldValue));

            // check date/time conditions
            /*
            field: always '0'
            value: value to compare against today's date
             */
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS:
                //value format: 'Y-m-d'
                return ($condition['value'] == date('Y-m-d'));
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_NOT:
                //value format: 'Y-m-d'
                return ($condition['value'] != date('Y-m-d'));
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_BEFORE:
                //value format: 'Y-m-d'
                return ($condition['value'] > date('Y-m-d'));
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_AFTER:
                //value format: 'Y-m-d'
                return ($condition['value'] < date('Y-m-d'));
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_WEEKDAY:
                //value format: [1,...,7]
                $weekday = date('N'); // ISO-8601 day of week is 1 (monday) bis 7 (sunday)
                return CampaignsProcessFlow::CheckDatetimeConstraints($condition['op'], $condition['value'], $weekday);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_MONTH:
                //value format: [1,...,12]
                $month = date('n');
                return CampaignsProcessFlow::CheckDatetimeConstraints($condition['op'], $condition['value'], $month);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_DAY_OF_MONTH:
                //value format: [1,...,31]
                $day = date('j');
                return CampaignsProcessFlow::CheckDatetimeConstraints($condition['op'], $condition['value'], $day);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_HOLIDAY:
                //$value is an array of ToolCalendarIDs

                if (empty($condition['value'])) {
                    //no calendars specified => no holidays
                    return false;
                }
                return ToolCalendar::IsHoliday($UserID, $condition['value'], time());
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_BEFORE_TIME:
                //value format: 'H:i'
                return ($condition['value'] > date('H:i'));
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_AFTER_TIME:
                //value format: 'H:i'
                return ($condition['value'] < date('H:i'));
            default:
        }

        // no condition op -> as if nothing is there
        // AND: return TRUE, OR: return FALSE
        return $isAnd;
    }

    /**
     * @param array<mixed> $failedConditions
     * @return bool
     */
    public static function CheckSegment(
        $segment,
        $isAnd,
        $UserID,
        $SubscriberID,
        $ReferenceID,
        array &$failedConditions = []
    ) {
        if (empty($segment['conditions'])) {
            // no conditions -> as if nothing is there
            // AND: return TRUE, OR: return FALSE
            return $isAnd;
        }

        $return = null;
        $failedConditions = ['and' => [], 'or' => []];

        // check segments
        $allTempConditions = [];
        foreach ($segment['conditions'] as $condition) {
            $temporal = false;
            if (TransactionEmails::isConditionTypeTemporal($condition['op'])) {
                $allTempConditions[] = $condition;
                $temporal = true;
            }
            $result = static::CheckCondition(
                $condition,
                $segment['conditionsOpAND'],
                $UserID,
                $SubscriberID,
                $ReferenceID
            );
            if ($segment['conditionsOpAND']) {
                if (!$result) {
                    if ($temporal === false) {
                        $failedConditions['and'][] = $condition;
                    }
                    // AND: if any condition fails, the segment fails
                    $return ??= false;
                }
                $merged = array_merge($failedConditions['and'], $allTempConditions);
                $failedConditions['and'] = array_map('unserialize', array_unique(array_map('serialize', $merged)));
            } else {
                if ($result) {
                    // OR: if any condition succeeds, the segment succeeds
                    $return ??= true;
                } else {
                    if ($temporal === false) {
                        $failedConditions['or'][] = $condition;
                    }
                }
                $merged = array_merge($failedConditions['or'], $allTempConditions);
                $failedConditions['or'] = array_map('unserialize', array_unique(array_map('serialize', $merged)));
            }
        }

        // AND: all conditions were true, so the segment is
        // OR: all conditions were false, so the segment is
        return $return ?? $segment['conditionsOpAND'];
    }

    /**
     * @param array<mixed> $failedSegments
     * @return bool
     */
    public static function CheckDecision($state, $UserID, $SubscriberID, $ReferenceID, array &$failedSegments = [])
    {
        if (empty($state['segments'])) {
            // works as a filter
            // no segments -> decision is true
            return true;
        }

        $return = null;
        $failedSegments = ['and' => [], 'or' => []];

        // check segments
        foreach ($state['segments'] as $segment) {
            $failedConditions = [];
            $result = static::CheckSegment(
                $segment,
                $state['segmentsOpAND'],
                $UserID,
                $SubscriberID,
                $ReferenceID,
                $failedConditions
            );

            if ($state['segmentsOpAND']) {
                if (!$result) {
                    $failedSegments['and'][] = $failedConditions;
                    //$nextPossibleTime = max($nextPossibleTime, $nextPossiblePartialMatchTime);
                    // AND: if any segment fails, the decision fails
                    $return ??= false;
                    //return false;
                }
            } else {
                if ($result) {
                    // OR: if any segment succeeds, the decision succeeds
                    $return ??= true;
                    //return true;
                } else {
                    $failedSegments['or'][] = $failedConditions;
                }
            }
        };

        // AND: all segments were true, so the decision is
        // OR: all segments were false, so the decision is
        return $return ?? $state['segmentsOpAND'];
    }

    /**
     * Check if the start condition is given
     *
     * @param $ArrayCampaign
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $ForceStart true, if this is called form a "start automation" action
     * @param bool $createTemporalEntries
     *
     * @return array
     */
    public static function CheckStartState(
        $ArrayCampaign,
        $SubscriberID,
        $ReferenceID,
        $ForceStart = false,
        bool $createTemporalEntries = true
    ) {
        $failedSegments = [];
        $ActionState = 0;
        $TimeToSend = time();

        if ($ArrayCampaign['MultipleSendFlag']) {
            // check if subscriber state is newer than last campaign finish
            $ArrayTaggings = TransactionEmails::GetCachedTagging(
                $ArrayCampaign['RelOwnerUserID'],
                $SubscriberID,
                $ReferenceID,
                false
            );
            $tagging = $ArrayTaggings[$ArrayCampaign['AutomationFinishedSmartTagID']] ?? null;
            if (!empty($tagging)) {
                // dont restart within a configured delay after last run
                $TimeToSend = max($tagging['SubscriptionDate'] + static::getMultisendDelayMinutes() * 60, time());
            }
        }

        $state = static::FindState($ArrayCampaign, $ArrayCampaign['ProcessFlow']['start']);

        if (!empty($state)) {
            if (static::CheckForEmptyStart($ArrayCampaign)) {
                // condition is empty
                if ($ForceStart) {
                    // excecute "start automation" action
                    $ActionState = $state['next'];
                }
            } elseif (
                static::CheckDecision(
                    $state,
                    $ArrayCampaign['RelOwnerUserID'],
                    $SubscriberID,
                    $ReferenceID,
                    $failedSegments
                )
            ) {
                // condition is not empty and evaluates to true
                $ActionState = $state['next'];
            } elseif (
                    $createTemporalEntries &&
                    !$ArrayCampaign['MultipleSendFlag'] &&
                    ($nextPossibleTime = TransactionEmails::calcNextPossibleMatch(
                        $failedSegments,
                        [
                            'UserID' => $ArrayCampaign['RelOwnerUserID'],
                            'SubscriberID' => $SubscriberID,
                            'ReferenceID' => $ReferenceID
                        ]
                    )) > 0
            ) {
                TransactionEmails::createTemporalDBEntry(
                    $ArrayCampaign['RelOwnerUserID'],
                    $ArrayCampaign['CampaignID'],
                    $SubscriberID,
                    $ReferenceID,
                    $nextPossibleTime
                );
                $TimeToSend = 0;
            }
        }
        return array($ActionState, $TimeToSend, $failedSegments);
    }

    /**
     * Check if the campaign has goals, the subscriber hasn't reached a goal yet and the goal condtion is met
     *
     * @param $GoalReached
     * @param $ArrayCampaign
     * @param $SubscriberID
     * @param $ReferenceID
     * @param int $currentStateID
     * @param int $nextPossibleTime
     * @return ?State goal state || array() if not applicable
     * @throws Exception
     */
    // phpcs:ignore PSR1.Methods.CamelCapsMethodName
    public static function CheckGoals(
        $GoalReached,
        $ArrayCampaign,
        $SubscriberID,
        $ReferenceID,
        $currentStateID,
        &$nextPossibleTime = 0
    ) {
        if ($GoalReached) {
            return null;
        }

        $GoalStateIDs = $ArrayCampaign['ProcessFlow']['goals'];

        if (empty($GoalStateIDs)) {
            return null;
        }

        $possibleGoalTimes = [];
        foreach ($GoalStateIDs as $StateID) {
            $state = static::FindState($ArrayCampaign, $StateID);
            $failedSegments = [];

            if (
                $state && $state['next'] > 0 && static::CheckDecision(
                    $state,
                    $ArrayCampaign['RelOwnerUserID'],
                    $SubscriberID,
                    $ReferenceID,
                    $failedSegments
                )
            ) {
                return $state;
            } elseif (!empty($failedSegments)) {
                // if the goal condition is not met, the next possible time is calculated
                $possibleGoalTime = TransactionEmails::calcNextPossibleMatch(
                    $failedSegments,
                    [
                        'UserID' => $ArrayCampaign['RelOwnerUserID'],
                        'SubscriberID' => $SubscriberID,
                        'ReferenceID' => $ReferenceID
                    ]
                );
                if ($possibleGoalTime > 0) {
                    $possibleGoalTimes[] = $possibleGoalTime;
                }
            }
        }

        if ($possibleGoalTimes) {
            $nextPossibleTime = min($possibleGoalTimes);
            $allowedTypes = [static::PROCESSFLOW_STATE_TYPE_START];
            if (in_array(self::FindState($ArrayCampaign, $currentStateID)['type'], $allowedTypes)) {
                TransactionEmails::createTemporalDBEntry(
                    $ArrayCampaign['RelOwnerUserID'],
                    $ArrayCampaign['CampaignID'],
                    $SubscriberID,
                    $ReferenceID,
                    $nextPossibleTime,
                    $currentStateID
                );
            }
        }

        return null;
    }

    /**
     * Remove all goal history entries for this campaign (delete campaign)
     * @param $ArrayCampaign
     * @param $SubscriberID
     */
    public static function ClearGoalsHistory($UserID, $CampaignID)
    {
        if (empty($UserID) || empty($CampaignID)) {
            return;
        }

        db_query(
            "DELETE FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND RelIndexed = :RelIndexed AND HistoryType IN (:Types)",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelIndexed' => $CampaignID,
                ':Types' => array(
                    Subscribers::HISTORY_GOAL_REACHED,
                    Subscribers::HISTORY_GOAL_PASSED
                ),
            )
        );
    }

    /**
     * Remove all goal history entries for this campaign and subscriber (restart multiple send)
     * @param $ArrayCampaign
     * @param $SubscriberID
     */
    public static function ClearGoalsHistoryOfSubscriber($UserID, $CampaignID, $SubscriberID)
    {
        if (empty($UserID) || empty($CampaignID) || empty($SubscriberID)) {
            return;
        }

        //remove goal history for a specific subscriber (restart multiple send)

        db_query(
            "DELETE FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND RelIndexed = :RelIndexed AND RelSubscriberID = :RelSubscriberID AND HistoryType IN (:Types)",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelIndexed' => $CampaignID,
                ':RelSubscriberID' => $SubscriberID,
                ':Types' => array(
                    Subscribers::HISTORY_GOAL_REACHED,
                    Subscribers::HISTORY_GOAL_PASSED
                ),
            )
        );
    }

    /**
     * @param $Constraint : weekday, month, day of month
     * @param $Allowed : array of integers representing weekdays, months or days of month
     * @param $Value : value to be checked against the allowed value
     * @return bool: if value is omitted, the allowed values are validated
     */
    public static function CheckDatetimeConstraints($Constraint, $Allowed, $Value = null)
    {
        switch ($Constraint) {
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_FIELD_IS_WEEKDAY:
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_WEEKDAY:
                $Allowed = array_values(
                    array_intersect(array(
                        1,
                        2,
                        3,
                        4,
                        5,
                        6,
                        7
                    ), $Allowed)
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_FIELD_IS_MONTH:
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_MONTH:
                $Allowed = array_values(
                    array_intersect(array(
                        1,
                        2,
                        3,
                        4,
                        5,
                        6,
                        7,
                        8,
                        9,
                        10,
                        11,
                        12
                    ), $Allowed)
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_FIELD_IS_DAY_OF_MONTH:
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_DAY_OF_MONTH:
                $Allowed = array_values(
                    array_intersect(array(
                        1,
                        2,
                        3,
                        4,
                        5,
                        6,
                        7,
                        8,
                        9,
                        10,
                        11,
                        12,
                        13,
                        14,
                        15,
                        16,
                        17,
                        18,
                        19,
                        20,
                        21,
                        22,
                        23,
                        24,
                        25,
                        26,
                        27,
                        28,
                        29,
                        30,
                        31
                    ), $Allowed)
                );
                break;
            default:
                $Allowed = array();
                break;
        }

        if (!isset($Value)) {
            return (!empty($Allowed));
        }

        return (!empty($Allowed) && in_array($Value, $Allowed));
    }

    /**
     * convenience function to check if a processflow campaign with the same name already exists
     *
     * @param int $RelOwnerUserID
     * @param string $Name
     * @return int
     */
    public static function CheckDuplicateName($RelOwnerUserID, $Name): int
    {
        $query = "SELECT CampaignId FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID AND AutoResponderTriggerTypeEnum IN (:TriggerType) AND CampaignName = :Name LIMIT 0,1";

        $params = array(
            ':RelOwnerUserID' => $RelOwnerUserID,
            ':TriggerType' => array(
                Campaigns::TRIGGER_TYPE_PROCESSFLOW
            ),
            ':Name' => $Name,
        );


        return (int)db_query($query, $params)->fetchField();
    }

    /**
     * klicktippapi_update: perform checks if the entity can be updated
     * @param $DBArray
     *
     * @return bool
     */
    public static function AllowUpdate($DBArray, &$UpdateData)
    {
        return !CampaignsProcessFlow::IsActive($DBArray);
    }

    /**
     * Checks if the automation limit has been exceeded
     * Ovwrites Core::LimitExceeded($account)
     * @return array(TRUE/FALSE, message)
     */
    public static function CheckLimitExceeded($account)
    {
        $UserGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID);

        $limit = $UserGroup['Data'][CampaignsProcessFlow::$UserGroupLimitField];

        if ($limit && $limit != 'unlimited') {
            $count = CampaignsProcessFlow::GetCount($account->uid);

            if ($count >= $limit) {
                return array(
                    true,
                    t("Automation limit exceeded. Please upgrade your account.")
                );
            }
        }

        return array(false, '');
    }

    /** @param array{name: string, type: string, id: string} $State */
    public static function GetStateName($State)
    {
        $DisplayStateName = array(
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_START => t('processflow-action-type:start'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT => t('processflow-action-type:exit'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_RESTART => t('processflow-action-type:restart'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DECISION => t('processflow-action-type:decision'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_GOAL => t('processflow-action-type:goal'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_ACTION => t('processflow-action-type:action'), // obsolete
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL => t('processflow-action-type:email'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDSMS => t('processflow-action-type:sms'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL => t('processflow-action-type:notify by email'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS => t('processflow-action-type:notify by sms'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_OUTBOUND => t('processflow-action-type:outbound'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_TAGGING => t('processflow-action-type:tagging'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNTAGGING => t('processflow-action-type:untagging'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNSUBSCRIBE => t('processflow-action-type:unsubscribe'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SETFIELD => t('processflow-action-type:setfield'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT => t('processflow-action-type:wait'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_GOTO => t('processflow-action-type:goto'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STARTAUTOMATION => t(
                'processflow-action-type:start automation'
            ),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STOPAUTOMATION => t('processflow-action-type:stop automation'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SPLITTEST => t('processflow-action-type:splittest'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD => t(
                'processflow-action-type:facebook audience add'
            ),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_REMOVE => t(
                'processflow-action-type:facebook audience remove'
            ),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DETECT_NAME => t('processflow-action-type:detect name'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DETECT_GENDER => t('processflow-action-type:detect gender'),
            CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT => t('processflow-action-type:fullcontact'),
        );

        return (empty($State['name'])) ? $DisplayStateName[$State['type']] . " #{$State['id']}" : $State['name'];
    }

    /**
     * @param $CampaignID
     *
     * @return string
     * @deprecated use getEntityUrlEdit
     * Note: this is used in klicktipp/callbacks/callbacks.inc -> klicktipp_redirect_by_entity
     *       remove after all entity classes implemented getEntityUrlEdit
     */
    public function GetEditURL($CampaignID = 0): string
    {
        return $this->getEntityUrlEdit();
    }

    public static function getUrlOverview(int $userId = 0): string
    {
        return APP_URL . "app/campaign/automation/" . ($userId ?: 'me');
    }

    public static function getUrlCreate(int $userId = 0): string
    {
        return APP_URL . "app/campaign/automation/" . ($userId ?: 'me') . "/create-automation";
    }

    public function getEntityUrlEdit(): string
    {
        return APP_URL . "app/campaign/automation/{$this->GetData('RelOwnerUserID')}/{$this->GetData('CampaignID')}/edit";
    }

    public function getEntityUrlCockpit(): string
    {
        return APP_URL . "app/campaign/automation/{$this->GetData('RelOwnerUserID')}/{$this->GetData('CampaignID')}/cockpit";
    }

    /**
     * @param int $userId
     * @param bool $reset
     * @return array
     * @throws \Doctrine\DBAL\Exception
     */
    public static function getOverviewStats(int $userId): array
    {

        // get/set cache (get all data for this user at once and cache results)

        $cachedStats = UserCache::get($userId, UserCache::CACHE_AUTOMATION_OVERVIEW_STATS);
        if (empty($cachedStats)) {
            $stats = Statistics::RetrieveSmartTagStatistics($userId, [
                Tag::CATEGORY_AUTOMATION_STARTED,
                Tag::CATEGORY_AUTOMATION_FINISHED,
            ]);

            //@note: Multivalue taggings - count all campaign runs
            $stats['paused_after'] = db_query(
                "SELECT RelAutoResponderID, COUNT(*) as count FROM {" . TransactionalQueue::TABLE_NAME . "} " .
                " WHERE RelOwnerUserID = :RelOwnerUserID AND StatusEnum IN (:StatusEnum) GROUP BY RelAutoResponderID",
                array(
                    ':RelOwnerUserID' => $userId,
                    ':StatusEnum' => array(
                        TransactionEmails::STATUS_PAUSED_AFTER,
                        TransactionEmails::STATUS_PAUSED_AFTER_NO
                    )
                )
            )->fetchAllKeyed();

            $campaigns = static::getOfUser($userId);

            $cachedStats = [];
            foreach ($campaigns as $campaign) {
                $campaignId = $campaign->GetData('CampaignID');

                $started = $stats[Tag::CATEGORY_AUTOMATION_STARTED][$campaignId] ?? 0;
                $finished = $stats[Tag::CATEGORY_AUTOMATION_FINISHED][$campaignId] ?? 0;
                if ($campaign->GetData('MultipleSendFlag')) {
                    // on MultipleSend all started taggings are deleted after finishing the campaign
                    $started += $finished;
                }
                $pausedAfter = $stats['paused_after'][$campaignId];
                $active = $started - $finished - $pausedAfter;

                $cachedStats[$campaignId] = [
                    'started' => $started,
                    'finished' => $finished,
                    'active' => $active,
                ];
            }

            klicktipp_user_cache_set($userId, Core::USERCACHE_AUTOMATION_OVERVIEW_STATS, $cachedStats);
        }

        return $cachedStats;
    }

    /**
     * Get smart tag and similar stats
     * @param bool $cached
     * @return array
     */
    public function GetTaggingStats($cached = true): array
    {
        $campaignId = $this->GetData('CampaignID');
        $userId = $this->GetData('RelOwnerUserID');

        // --- get smart tagging stats

        $query = "SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :userId AND RelTagID = :tagId";

        $started = (int)db_query($query, [
            ':userId' => $userId,
            ':tagId' => $this->GetData('AutomationStartedSmartTagID')
        ])->fetchField();

        $finished = (int)db_query($query, [
            ':userId' => $userId,
            ':tagId' => $this->GetData('AutomationFinishedSmartTagID')
        ])->fetchField();

        // --- get status stats

        $statusQuery = "SELECT COUNT(*) FROM {" . TransactionalQueue::TABLE_NAME . "} " .
            "WHERE RelOwnerUserID = :userId AND RelAutoResponderID = :campaignId AND StatusEnum IN (:statusEnum)";

        $params = [
            ':userId' => $userId,
            ':campaignId' => $campaignId,
            ':statusEnum' => [
                TransactionEmails::STATUS_PAUSED_AFTER,
                TransactionEmails::STATUS_PAUSED_AFTER_NO,
            ]
        ];
        $pausedAfter = (int)db_query($statusQuery, $params)->fetchField();

        $params[':statusEnum'] = [TransactionEmails::STATUS_PAUSED_QUEUE_JOB];
        $pausedJob = db_query($statusQuery, $params)->fetchField();

        $params[':statusEnum'] = [TransactionEmails::STATUS_FAILED];
        $failed = db_query($statusQuery, $params)->fetchField();

        // --- get campaign conversion
        $conversion = Statistics::RetrieveCampaignConversion(
            $userId,
            $campaignId
        ) ?: Statistics::$DEFAULT_CAMPAIGN_CONVERSION;

        // --- build stats

        if ($this->GetData('MultipleSendFlag')) {
            // on MultipleSend all started taggings are deleted after finishing the campaign
            $started += $finished;
        }

        return array(
            'started' => $started,
            'finished' => $finished,
            'active' => $started - $finished - $pausedAfter,
            'failed' => $failed,
            'onopenend' => $pausedAfter,
            'waitforjob' => $pausedJob,
            'conversion' => $conversion,
            'converted' => $conversion['conversion'],
        );
    }

    /**
     * Check custom field condition
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $condition : for field id
     *
     * @return array(formatted field value, condition value, field type, unformatted field value)
     */
    public static function GetConditionFieldParams($UserID, $SubscriberID, $ReferenceID, $condition)
    {
        $ArrayCustomFields = TransactionEmails::GetCachedCustomFields($UserID);
        $SubscriberField = CustomFields::GetCustomFieldData(
            $UserID,
            $SubscriberID,
            $ArrayCustomFields[$condition['field']] ?? null,
            $ReferenceID
        );
        $FieldType = $ArrayCustomFields[$condition['field']]['FieldTypeEnum'] ?? null;
        $ConditionValue = $condition['value'];

        switch ($FieldType) {
            case CustomFields::TYPE_DATE:
                $FieldValue = (empty($SubscriberField)) ? '' : date('Y-m-d', $SubscriberField);
                break;
            case CustomFields::TYPE_TIME:
                $FieldValue = (empty($SubscriberField)) ? '' : CustomFields::ConvertCustomFieldDataToWidget(
                    $SubscriberField,
                    CustomFields::WIDGET_TIME_SECONDSOFDAY
                );
                break;
            case CustomFields::TYPE_DATETIME:
                $FieldValue = (empty($SubscriberField)) ? '' : date("Y-m-d H:i", $SubscriberField);
                break;
            case CustomFields::TYPE_NUMBER:
                $FieldValue = (empty($SubscriberField)) ? 0 : intval($SubscriberField);
                break;
            case CustomFields::TYPE_DECIMAL:
                // the condition value is comparable in php (condition decimal is "1,23" not "1.23"), so convert both to int
                $FieldValue = (empty($SubscriberField)) ? 0 : intval($SubscriberField);
                $ConditionValue = UtilsNumber::formatFloatFromInput((string)$ConditionValue);
                $ConditionValue = CustomFields::ConvertCustomFieldDataFromWidget(
                    $ConditionValue,
                    CustomFields::WIDGET_DECIMAL
                );
                break;
            case CustomFields::TYPE_EMAIL:
                // precautionally normalize both values to the same format
                $FieldValue = (empty($SubscriberField)) ? "" : mb_strtolower(
                    Subscribers::DepunycodeEmailAddress($SubscriberField),
                    "UTF-8"
                );
                $ConditionValue = (empty($ConditionValue)) ? "" : mb_strtolower(
                    Subscribers::DepunycodeEmailAddress($ConditionValue),
                    "UTF-8"
                );
                break;
            default:
                $FieldValue = strtolower($SubscriberField);
                // Note: field values are stored with check_plain in the DB
                $ConditionValue = check_plain(strtolower($ConditionValue));
                break;
        }

        return array($FieldValue, $ConditionValue, $FieldType, $SubscriberField);
    }

    /**
     * Check if an entity is used in this object
     * @param $EntityID
     * @param $EntityClass
     * @param string $Op action to check dependencies for
     * @param bool $forAngular
     * @return array (
     *     EntityID => Edit link
     * )
     */
    public function GetDependencies($EntityID, $EntityClass, $Op = 'delete', bool $forAngular = false)
    {
        if (empty($EntityID) || empty($EntityClass)) {
            return array();
        }

        $dependencies = array();
        $SmartTags = array();

        switch ($EntityClass) {
            case Tag::class:
                $findTags = function (
                    $ObjectCampaign,
                    &$key,
                    $category,
                    $dependencies = array()
                ) use (
                    $EntityID,
                    $forAngular,
                    $EntityClass
                ) {
                    if ($category == 'tags' && $key == $EntityID) {
                        $CampaignID = $ObjectCampaign->GetData('CampaignID');
                        $Name = $ObjectCampaign->GetData('CampaignName');

                        if ($forAngular) {
                            $dependencies[/*t(*/'dependency:processflow:Tag'/*)*/][$CampaignID] = [
                                'id' => $CampaignID,
                                'type' => static::getApiType(),
                                'name' => $Name,
                                'editLink' => $this->getEntityUrlEdit()
                            ];
                        } else {
                            $dependencies[/*t(*/'dependency:processflow:Tag'/*)*/][$CampaignID] = l(
                                $Name,
                                $this->getEntityUrlEdit(),
                                array('attributes' => array('target' => '_blank'))
                            );
                        }
                    }

                    return $dependencies;
                };

                $dependencies = $this->TraverseProcessFlow($dependencies, $findTags);

                break;

            case CustomFields::class:
                $findCustomFields = function (
                    $ObjectCampaign,
                    &$key,
                    $category,
                    $dependencies = array()
                ) use (
                    $EntityID,
                    $forAngular,
                    $EntityClass
                ) {
                    if ($category == 'customfields' && $key == $EntityID) {
                        $CampaignID = $ObjectCampaign->GetData('CampaignID');
                        $Name = $ObjectCampaign->GetData('CampaignName');

                        if ($forAngular) {
                            $dependencies[/*t(*/'dependency:processflow:CustomField'/*)*/][$CampaignID] = [
                                'id' => $CampaignID,
                                'type' => static::getApiType(),
                                'name' => $Name,
                                'editLink' => $this->getEntityUrlEdit()
                            ];
                        } else {
                            $dependencies[/*t(*/'dependency:processflow:CustomField'/*)*/][$CampaignID] = l(
                                $Name,
                                $this->getEntityUrlEdit(),
                                array('attributes' => array('target' => '_blank'))
                            );
                        }
                    }

                    return $dependencies;
                };

                $dependencies = $this->TraverseProcessFlow($dependencies, $findCustomFields);

                break;

            case CampaignsProcessFlow::class:
                $DeleteCampaign = CampaignsProcessFlow::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeleteCampaign) {
                    break;
                }

                $SmartTags = array(
                    $DeleteCampaign->GetData('AutomationStartedSmartTagID'),
                    $DeleteCampaign->GetData('AutomationFinishedSmartTagID'),
                );

                $findAutomations = function (
                    $ObjectCampaign,
                    &$key,
                    $category,
                    $dependencies = array()
                ) use (
                    $EntityID,
                    $forAngular,
                    $EntityClass
                ) {
                    if ($category == 'campaigns' && $key == $EntityID) {
                        $CampaignID = $ObjectCampaign->GetData('CampaignID');
                        $Name = $ObjectCampaign->GetData('CampaignName');

                        if ($forAngular) {
                            $dependencies[/*t(*/'dependency:processflow:Automation'/*)*/][$CampaignID] = [
                                'id' => $CampaignID,
                                'type' => static::getApiType(),
                                'name' => $Name,
                                'editLink' => $this->getEntityUrlEdit()
                            ];
                        } else {
                            $dependencies[/*t(*/'dependency:processflow:Automation'/*)*/][$CampaignID] = l(
                                $Name,
                                $this->getEntityUrlEdit(),
                                array('attributes' => array('target' => '_blank'))
                            );
                        }
                    }

                    return $dependencies;
                };

                $dependencies = $this->TraverseProcessFlow($dependencies, $findAutomations);

                break;

            case EmailsAutomationEmail::class:
            case EmailsAutomationSMS::class:
            case EmailsNotificationEmail::class:
            case EmailsNotificationSMS::class:
                if ($EntityClass == EmailsAutomationEmail::class || $EntityClass == EmailsAutomationSMS::class) {
                    $DeleteEmail = $EntityClass::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                    if (!$DeleteEmail) {
                        break;
                    }

                    if ($EntityClass == EmailsAutomationEmail::class) {
                        $SmartTags = array(
                            $DeleteEmail->GetData('EmailSentSmartTagID'),
                            $DeleteEmail->GetData('EmailOpenedSmartTagID'),
                            $DeleteEmail->GetData('EmailClickedSmartTagID'),
                            $DeleteEmail->GetData('EmailViewedSmartTagID'),
                        );
                    } else {
                        $SmartTags = array(
                            $DeleteEmail->GetData('SMSSentSmartTagID') .
                            $DeleteEmail->GetData('SMSClickedSmartTagID'),
                        );
                    }
                }

                $findEmails = function (
                    $ObjectCampaign,
                    &$key,
                    $category,
                    $dependencies = array()
                ) use (
                    $EntityID,
                    $forAngular,
                    $EntityClass
                ) {
                    if ($category == 'emails' && $key == $EntityID) {
                        $CampaignID = $ObjectCampaign->GetData('CampaignID');
                        $Name = $ObjectCampaign->GetData('CampaignName');

                        if ($forAngular) {
                            $dependencies[/*t(*/'dependency:processflow:Emails'/*)*/][$CampaignID] = [
                                'id' => $CampaignID,
                                'type' => static::getApiType(),
                                'name' => $Name,
                                'editLink' => $this->getEntityUrlEdit()
                            ];
                        } else {
                            $dependencies[/*t(*/'dependency:processflow:Emails'/*)*/][$CampaignID] = l(
                                $Name,
                                $this->getEntityUrlEdit(),
                                array('attributes' => array('target' => '_blank'))
                            );
                        }
                    }

                    return $dependencies;
                };

                $dependencies = $this->TraverseProcessFlow($dependencies, $findEmails);

                break;

            case Affilicon::class:
                $DeleteProduct = Affilicon::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeleteProduct) {
                    break;
                }

                $SmartTags = array(
                    $DeleteProduct->GetData('SmartTagID'),
                    $DeleteProduct->GetData('RefundSmartTagID'),
                    $DeleteProduct->GetData('ChargebackSmartTagID'),
                    $DeleteProduct->GetData('PaymentCompletedSmartTagID'),
                    $DeleteProduct->GetData('PaymentExpiredSmartTagID'),
                );
                break;
            case ClickBank::class:
                $DeleteProduct = ClickBank::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeleteProduct) {
                    break;
                }

                $SmartTags = array(
                    $DeleteProduct->GetData('SmartTagID'),
                    $DeleteProduct->GetData('RefundSmartTagID'),
                    $DeleteProduct->GetData('ChargebackSmartTagID'),
                    $DeleteProduct->GetData('RebillCanceledSmartTagID'),
                    $DeleteProduct->GetData('RebillResumedSmartTagID'),
                    $DeleteProduct->GetData('PaymentCompletedSmartTagID'),
                    $DeleteProduct->GetData('PaymentExpiredSmartTagID'),
                );
                break;
            case DigiStore::class:
                $DeleteProduct = DigiStore::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeleteProduct) {
                    break;
                }

                $SmartTags = array(
                    $DeleteProduct->GetData('SmartTagID'),
                    $DeleteProduct->GetData('RefundSmartTagID'),
                    $DeleteProduct->GetData('ChargebackSmartTagID'),
                    $DeleteProduct->GetData('RebillCanceledSmartTagID'),
                    $DeleteProduct->GetData('RebillCanceledLastDaySmartTagID'),
                    $DeleteProduct->GetData('RebillResumedSmartTagID'),
                    $DeleteProduct->GetData('PaymentCompletedSmartTagID'),
                    $DeleteProduct->GetData('PaymentExpiredSmartTagID'),
                    $DeleteProduct->GetData('AffiliationSmartTagID'),
                );
                break;
            case PayPal::class:
                $DeleteProduct = PayPal::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeleteProduct) {
                    break;
                }

                $SmartTags = array(
                    $DeleteProduct->GetData('SmartTagID'),
                    $DeleteProduct->GetData('RefundSmartTagID'),
                    $DeleteProduct->GetData('ChargebackSmartTagID'),
                    $DeleteProduct->GetData('SubscriptionCanceledSmartTagID'),
                    $DeleteProduct->GetData('PaymentCompletedSmartTagID'),
                    $DeleteProduct->GetData('PaymentExpiredSmartTagID'),
                );

                break;
            case CampaignsNewsletter::class:
            case CampaignsNewsletterEmail::class:
            case CampaignsNewsletterSms::class:
            case CampaignsAutoresponderEmail::class:
            case CampaignsAutoresponderEmailDatetime::class:
            case CampaignsAutoresponderEmailBirthday::class:
            case CampaignsAutoresponderSms::class:
            case CampaignsAutoresponderSmsDatetime::class:
            case CampaignsAutoresponderSmsBirthday::class:
                //smart tags
                $DeleteNewsletter = CampaignsNewsletter::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeleteNewsletter) {
                    break;
                }

                $SmartTags = array(
                    $DeleteNewsletter->GetData('SentSmartTagID'),
                    $DeleteNewsletter->GetData('OpenedSmartTagID'),
                    $DeleteNewsletter->GetData('ClickedSmartTagID'),
                    $DeleteNewsletter->GetData('ViewedSmartTagID'),
                    $DeleteNewsletter->GetData('ConvertedSmartTagID'),
                );
                break;
            case ToolOutboundKajabi::class:
            case ToolOutboundGeneral::class:
            case ToolOutboundZapier::class:
                $DeleteOutbound = $EntityClass::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeleteOutbound) {
                    break;
                }

                $SmartTags = array(
                    $DeleteOutbound->GetData('ActivationSmartTagID'),
                );

                if ($EntityClass == ToolOutboundKajabi::class) {
                    $SmartTags[] = $DeleteOutbound->GetData('DeactivationSmartTagID');
                }

                if ($EntityClass == ToolOutboundKajabi::class) {
                    $findOutbounds = function (
                        $ObjectCampaign,
                        &$key,
                        $category,
                        $dependencies = array()
                    ) use (
                        $EntityID,
                        $forAngular,
                        $EntityClass
                    ) {
                        if ($category == 'outbounds' && $key == $EntityID) {
                            $CampaignID = $ObjectCampaign->GetData('CampaignID');
                            $Name = $ObjectCampaign->GetData('CampaignName');

                            if ($forAngular) {
                                $dependencies[/*t(*/'dependency:processflow:Outbounds'/*)*/][$CampaignID] = [
                                    'id' => $CampaignID,
                                    'type' => static::getApiType(),
                                    'name' => $Name,
                                    'editLink' => $this->getEntityUrlEdit()
                                ];
                            } else {
                                $dependencies[/*t(*/'dependency:processflow:Outbounds'/*)*/][$CampaignID] = l(
                                    $Name,
                                    $this->getEntityUrlEdit(),
                                    array('attributes' => array('target' => '_blank'))
                                );
                            }
                        }

                        return $dependencies;
                    };

                    $dependencies = $this->TraverseProcessFlow($dependencies, $findOutbounds);
                }

                break;
            case ToolPluginGeneral::class:
                /** @var ToolPluginGeneral $DeletePlugin */
                $DeletePlugin = $EntityClass::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeletePlugin) {
                    break;
                }

                $SmartTags = $DeletePlugin->GetSmartTags();

                $findOutbounds = function (
                    $ObjectCampaign,
                    &$key,
                    $category,
                    $dependencies = array()
                ) use (
                    $EntityID,
                    $forAngular,
                    $EntityClass
                ) {
                    if ($category == 'outbounds' && $key == $EntityID) {
                        $CampaignID = $ObjectCampaign->GetData('CampaignID');
                        $Name = $ObjectCampaign->GetData('CampaignName');

                        if ($forAngular) {
                            $dependencies[/*t(*/'dependency:processflow:Outbounds'/*)*/][$CampaignID] = [
                                'id' => $CampaignID,
                                'type' => static::getApiType(),
                                'name' => $Name,
                                'editLink' => $this->getEntityUrlEdit()
                            ];
                        } else {
                            $dependencies[/*t(*/'dependency:processflow:Outbounds'/*)*/][$CampaignID] = l(
                                $Name,
                                $this->getEntityUrlEdit(),
                                array('attributes' => array('target' => '_blank'))
                            );
                        }
                    }

                    return $dependencies;
                };

                $dependencies = $this->TraverseProcessFlow($dependencies, $findOutbounds);

                break;
            case SubscriptionFormsCustom::class:
            case SubscriptionFormsInline::class:
            case SubscriptionFormsLeadpages::class:
            case SubscriptionFormsOptimizePress::class:
            case SubscriptionFormsRaw::class:
            case SubscriptionFormsWidget::class:
            case SubscriptionFormsWistia::class:
            case APIKey::class:
            case SMSListbuildings::class:
            case Wufoo::class:
            case ToolTaggingPixel::class:
                $DeleteObject = $EntityClass::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeleteObject) {
                    break;
                }

                $SmartTags = array(
                    $DeleteObject->GetData('SmartTagID'),
                );

                break;

            case ToolCalendar::class:
                $findCalendars = function (
                    $ObjectCampaign,
                    &$key,
                    $category,
                    $dependencies = array()
                ) use (
                    $EntityID,
                    $forAngular,
                    $EntityClass
                ) {
                    if ($category == 'calendar' && in_array($EntityID, $key)) {
                        $CampaignID = $ObjectCampaign->GetData('CampaignID');
                        $Name = $ObjectCampaign->GetData('CampaignName');

                        if ($forAngular) {
                            $dependencies[/*t(*/'dependency:processflow:Calendar'/*)*/][$CampaignID] = [
                                'id' => $CampaignID,
                                'type' => static::getApiType(),
                                'name' => $Name,
                                'editLink' => $this->getEntityUrlEdit()
                            ];
                        } else {
                            $dependencies[/*t(*/'dependency:processflow:Calendar'/*)*/][$CampaignID] = l(
                                $Name,
                                $this->getEntityUrlEdit(),
                                array('attributes' => array('target' => '_blank'))
                            );
                        }
                    }

                    return $dependencies;
                };

                $dependencies = $this->TraverseProcessFlow($dependencies, $findCalendars);


                break;

            case VarFacebookAudience::class:
                //check if any campaign uses the facebook audience action
                //since the user wants to remove the facebook connection, the entity id
                //of the audience doesn't matter

                $findFBAudiences = function (
                    $ObjectCampaign,
                    &$key,
                    $category,
                    $dependencies = array()
                ) use (
                    $EntityID,
                    $forAngular,
                    $EntityClass
                ) {
                    if ($category == 'facebook audience') {
                        $CampaignID = $ObjectCampaign->GetData('CampaignID');
                        $Name = $ObjectCampaign->GetData('CampaignName');

                        if ($forAngular) {
                            $dependencies[/*t(*/'dependency:processflow:FacebookAudience'/*)*/][$CampaignID] = [
                                'id' => $CampaignID,
                                'type' => static::getApiType(),
                                'name' => $Name,
                                'editLink' => $this->getEntityUrlEdit()
                            ];
                        } else {
                            $dependencies[/*t(*/'dependency:processflow:FacebookAudience'/*)*/][$CampaignID] = l(
                                $Name,
                                $this->getEntityUrlEdit(),
                                array('attributes' => array('target' => '_blank'))
                            );
                        }
                    }

                    return $dependencies;
                };


                $dependencies = $this->TraverseProcessFlow($dependencies, $findFBAudiences);

                break;
            case LandingPage::class:
                $DeleteObject = LandingPage::FromID($this->GetData('RelOwnerUserID'), $EntityID);
                if (!$DeleteObject) {
                    break;
                }

                // Note: there will be more smart tags to check for landing pages
                $SmartTags = array(
                    $DeleteObject->GetData('SmartTagID'),
                );

                break;
        }

        if (!empty($SmartTags)) {
            $findSmartTags = function (
                $ObjectCampaign,
                &$key,
                $category,
                $dependencies = array()
            ) use (
                $SmartTags,
                $forAngular,
                $EntityClass
            ) {
                if ($category == 'smarttags' && in_array($key, $SmartTags)) {
                    $CampaignID = $ObjectCampaign->GetData('CampaignID');
                    $Name = $ObjectCampaign->GetData('CampaignName');

                    if ($forAngular) {
                        $dependencies[/*t(*/'dependency:processflow:SmartTag'/*)*/][$CampaignID] = [
                            'id' => $CampaignID,
                            'type' => static::getApiType(),
                            'name' => $Name,
                            'editLink' => $this->getEntityUrlEdit()
                        ];
                    } else {
                        $dependencies[/*t(*/'dependency:processflow:SmartTag'/*)*/][$CampaignID] = l(
                            $Name,
                            $this->getEntityUrlEdit(),
                            array('attributes' => array('target' => '_blank'))
                        );
                    }
                }

                return $dependencies;
            };

            $dependencies = $this->TraverseProcessFlow($dependencies, $findSmartTags);
        }

        // check user segments
        if (in_array($EntityClass, VarSegment::$segmentGroups)) {
            $findSegments = function ($ObjectCampaign, &$key, $category, $dependencies = array()) use ($EntityID) {
                if ($category == 'segment' && $key == $EntityID) {
                    $CampaignID = $ObjectCampaign->GetData('CampaignID');
                    $Name = $ObjectCampaign->GetData('CampaignName');

                    $dependencies[/*t(*/'dependency:processflow:Condition:UserSegments'/*)*/][$CampaignID] = l(
                        $Name,
                        $this->GetEditURL(),
                        array('attributes' => array('target' => '_blank'))
                    );
                }

                return $dependencies;
            };

            $dependencies = $this->TraverseProcessFlow($dependencies, $findSegments);
        }

        return $dependencies;
    }

    public function GetSmartTags($forConditions = false)
    {
        if ($forConditions) {
            return array(
                'started' => $this->GetData('AutomationStartedSmartTagID'),
                'finished' => $this->GetData('AutomationFinishedSmartTagID')
            );
        }

        return array(
            'AutomationStartedSmartTagID' => $this->GetData('AutomationStartedSmartTagID'),
            'AutomationFinishedSmartTagID' => $this->GetData('AutomationFinishedSmartTagID'),
        );
    }

    /**
     * import entity
     * @param int $Importer
     * @param array $ExportedEntityData
     * @param string $NewObjectPrefix
     * @return bool|\DatabaseStatementInterface|int
     */
    public static function Import($Importer, $ExportedEntityData, $NewObjectPrefix = 'Import')
    {
        $NewCampaignID = parent::Import($Importer, $ExportedEntityData, $NewObjectPrefix);
        if (!$NewCampaignID) {
            return false;
        }

        // write all additional data with an update query

        /** @var CampaignsProcessFlow $Object */
        $Object = static::FromID($Importer, $NewCampaignID);
        $NewData = $Object->GetData();

        // the entities within the processflow need to be replaced later
        $NewData['ProcessFlow'] = $ExportedEntityData['ProcessFlow'];

        // copy multiple send only, if user is allowed to
        $account = user_load($Importer);
        $NewData['MultipleSendFlag'] = $ExportedEntityData['MultipleSendFlag'];

        // copy notes
        $NewData['Notes'] = $ExportedEntityData['Notes'];

        static::UpdateDB($NewData);

        return $NewCampaignID;
    }

    /**
     * @return array
     * @throws \Doctrine\DBAL\Exception
     */
    public function InitializeSendDateMinMaxRecipients(): array
    {
        $MinEstimatedRecipients = 0;
        // Note: this code is called BEFORE the modal is displayed
        $MaxEstimatedRecipients = db_query(
            "SELECT COUNT(DISTINCT RelSubscriberID, ReferenceID) FROM {subscription_reference} WHERE RelOwnerUserID = :RelOwnerUserID",
            [':RelOwnerUserID' => $this->GetData('RelOwnerUserID')]
        )->fetchField();
        // works as a countdown

        return [
            'min' => $MinEstimatedRecipients,
            'max' => $MaxEstimatedRecipients,
            'offset' => $MaxEstimatedRecipients,
            'continue' => 1
        ];
    }

    public function FetchSenddateSubscribers($UserID, $RefreshOffset, $RefreshSize)
    {
        $ThisCampaign = $this;

        $filter = function ($Subscriber) use ($UserID, $ThisCampaign) {
            // $Subscriber is object of { SubscriberID, SubscriptionDate, ReferenceID }
            $result = $ThisCampaign->SubscriberNotProcessedYetInCampaign(
                $UserID,
                $Subscriber->SubscriberID,
                $Subscriber->ReferenceID
            );
            // avoid expensive start condition check if possible
            if ($result) {
                [$ActionState, $TimeToSend] = CampaignsProcessFlow::CheckStartState(
                    $ThisCampaign->GetData(),
                    $Subscriber->SubscriberID,
                    $Subscriber->ReferenceID,
                    false,
                    false
                );
                $result = !empty($ActionState);
            }
            return $result;
        };

        // get all runs (subscriber with all reference ids)
        $query = "SELECT DISTINCT RelSubscriberID AS SubscriberID, ReferenceID FROM {subscription_reference} " .
            " WHERE RelOwnerUserID = :RelOwnerUserID " .
            " ORDER BY RelSubscriberID LIMIT $RefreshOffset, $RefreshSize";

        $params = array(
            ':RelOwnerUserID' => $UserID,
        );

        // $AllData == FALSE will group by SubscriberID
        $Subscribers = Subscribers::RetrieveFilteredSubscribers($query, $params, false, false, $filter);

        return count($Subscribers);
    }

    /**
     * Get all data needed for the email condition dialog for
     * Automation conditions
     * @param $UserID
     *
     * @return array
     */
    public static function GetConditionData($UserID)
    {
        //<#coptag>Email Editor Condition option for automations</#>
        //<#cactiontag>Email Editor Condition verb for automations</#>

        return array(
            'formData' => array(
                'condition' => array(
                    array(
                        'id' => 'has',
                        'label' => t(/*#coptag*/ 'condition::Contact has automation'),
                        'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_TAGGING,
                        'hasEntity' => true,
                    ),
                    array(
                        'id' => 'has-not',
                        'label' => t(/*#coptag*/ 'condition::Contact has not automation'),
                        'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_NOT_TAGGING,
                        'hasEntity' => true,
                    ),
                    array(
                        'id' => 'has-any',
                        'label' => t(/*#coptag*/ 'condition::Contact has any automation'),
                        'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_TAGGING,
                        'hasEntity' => false,
                    ),
                    array(
                        'id' => 'has-not-any',
                        'label' => t(/*#coptag*/ 'condition::Contact has not any automation'),
                        'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_NOT_TAGGING,
                        'hasEntity' => false,
                    ),
                ),
                'entity' => static::GetConditionEntities($UserID),
                'action' => array(
                    array(
                        'id' => 'started',
                        'label' => t(/*#cactiontag*/ 'condition::started')
                    ),
                    array(
                        'id' => 'finished',
                        'label' => t(/*#cactiontag*/ 'condition::finished')
                    )
                ),
                'timeframe' => Personalization::GetEditorConditionTimeframes()
            ),
            'default' => array(
                //form field data
                'type' => 'tagging',
                'condition' => 'has',
                'entity' => 0,
                'entityType' => static::class,
                'action' => 'started',
                'timeframe' => 'anytime',
                //internal data for CampaignsProcessFlow::CheckCondition()
                'op' => CampaignsProcessFlow::PROCESSFLOW_CONDITION_HAS_TAGGING,
                'field' => 0,
                'value' => 0
            )
        );
    }

    /**
     * Get all Automations to be used in email conditions
     * @param $UserID
     *
     * @return array
     */
    public static function GetConditionEntities($UserID)
    {
        // get db sets
        $result = db_query(
            "SELECT * FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID AND AutoResponderTriggerTypeEnum = :TriggerType",
            array(
                ':RelOwnerUserID' => $UserID,
                ':TriggerType' => Campaigns::TRIGGER_TYPE_PROCESSFLOW,
            )
        );

        $entities = array();
        while ($DBArray = kt_fetch_array($result)) {
            // @var DatabaseTableWithData $ObjectEntity
            $ObjectEntity = static::FromArray($DBArray);

            if ($ObjectEntity) {
                $data = $ObjectEntity->GetData();

                $entities[] = array(
                    'id' => $data[static::$DBSerialField],
                    'name' => static::GetNameForOptionsArray($data),
                    'type' => get_class($ObjectEntity),
                    'actions' => $ObjectEntity->GetSmartTags(true)
                );
            }
        }

        return $entities;
    }

    public static function GetConditionSmartTagCategory($action)
    {
        $actions = array(
            'started' => Tag::CATEGORY_AUTOMATION_STARTED,
            'finished' => TAG::CATEGORY_AUTOMATION_FINISHED
        );

        return (empty($actions[$action])) ? false : $actions[$action];
    }

    /**
     * Retrieves a delay of multiple sends inside of configured range
     *
     * @return int
     * @link https://app.ktlocal.com/admin/config/klicktipp/profiler#edit-web-transactional-send
     *       form were range is configured
     *
     * @see klicktipp_profiler_form
     *
     */
    public static function getMultisendDelayMinutes(): int
    {
        return rand(
            variable_get('klicktipp_wts_min_multisenddelay', 0),
            variable_get('klicktipp_wts_max_multisenddelay', 0)
        );
    }

    /**
     * collect all email, sms, notification email, notification sms in the processflow - callback
     */
    public static function traverseCollectEmails($ObjectCampaign, $key, $category, $Entities)
    {
        $UserID = $ObjectCampaign->GetData('RelOwnerUserID');

        if (empty($key)) {
            return $Entities;
        }
        if (is_scalar($key) && !empty($Entities[$key])) {
            return $Entities;
        }

        if ($category == 'emails') {
            $Object = Emails::FromID($UserID, $key);

            if ($Object) {
                $Entities[$key] = $Object;
            }
        }

        return $Entities;
    }

    /**
     *  - callback
     *
     * @param CampaignsProcessFlow $ObjectCampaign
     * @param $key
     * @param $category
     * @param array $Entities
     * @return array
     */
    /**
     * Create and replace automation split tests (actions) in copied automation
     * @param $ObjectCampaign
     * @param $key
     * @param $category
     * @param $Entities
     *
     * @return mixed
     */
    public static function traverseCopySplitTest($ObjectCampaign, &$key, $category, $Entities)
    {
        switch ($category) {
            case 'splittests':
                $SplittestObject = SplitTests::FromID($ObjectCampaign->GetData('RelOwnerUserID'), $key);
                if ($SplittestObject) {
                    // already set new campaign id, so "Import" creates the right relation
                    $Data = $SplittestObject->GetData();
                    $Data['RelCampaignID'] = $ObjectCampaign->GetData('CampaignID');
                    // clone split test and assign to copied
                    $key = SplitTests::Import($ObjectCampaign->GetData('RelOwnerUserID'), $Data);
                } else {
                    // on error: invalidate split test in copied campaign
                    $key = 0;
                }
                break;
            default:
                break;
        }

        // return
        return $Entities;
    }

    /**
     * @return array
     * @throws \ServicesException
     */
    public function angularApiConvertTo(): array
    {
        $entity = $this->GetData();

        $Labels = $this->GetMetaLabels(false);
        $stats = $this->GetTaggingStats();

        $sendDateTime = '';
        if ($entity['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_FUTURE) {
            $sendDateTime = date('Y-m-d H:i:00', $entity['SendDatetime']);
        }

        return [
            'id' => (int)$entity['CampaignID'],
            'name' => $entity['CampaignName'],
            'type' => Campaigns::$APIIndexFilterTypes[$entity['AutoResponderTriggerTypeEnum']],
            'lastUpdated' => (int)$entity['LastUpdated'],
            'metaLabels' => $Labels,
            'multipleSend' => empty($entity['MultipleSendFlag']) ? 0 : 1,
            'scheduleType' => (int)$entity['ScheduleTypeEnum'],
            'sendDateTime' => $sendDateTime,
            'estimatedRecipients' => (int)$entity['EstimatedRecipients'],
            'stats' => [
                'active' => (int)$stats['active'] ?? 0,
                'started' => (int)$stats['started'] ?? 0,
                'finished' => (int)$stats['finished'] ?? 0,
                'onOpenEnd' => (int)$stats['onopenend'] ?? 0,
                'failed' => (int)$stats['failed'] ?? 0,
            ],
            'notes' => $entity['Notes'] ?: '',
            'useNotes' => empty($entity['Notes']) ? 0 : 1,
        ];
    }

    /**
     * @param array $entity
     * @return void
     */
    protected function angularApiConvertFrom(array $entity)
    {
        $data = $this->GetData();

        $isCreate = empty($data['CampaignID']);
        $canEdit = $isCreate || static::canEdit($data);

        if (isset($entity['name'])) {
            $data['CampaignName'] = kt_strip_utf8mb4($entity['name']);
        }

        if (isset($entity['notes'])) {
            $data['Notes'] = kt_strip_utf8mb4($entity['notes']);
        }

        if (isset($entity['metaLabels']) && is_array($entity['metaLabels'])) {
            $data['MetaLabels'] = $entity['metaLabels'];
        }

        if ($canEdit && $data['MultipleSendFlag'] && isset($entity['multipleSend']) && empty($entity['multipleSend'])) {
            // only allow deactivating the old multiple send flag
            $data['MultipleSendFlag'] = 0;
        }

        if (isset($entity['scheduleType']) && $this->canEditSendDate()) {
            switch ($entity['scheduleType']) {
                case Campaigns::SCHEDULE_TYPE_IMMEDIATE:
                    $data['SendDatetime'] = time();
                    break;
                case Campaigns::SCHEDULE_TYPE_FUTURE:
                    $data['SendDatetime'] = strtotime($entity['sendDateTime']);
                    break;
                case Campaigns::SCHEDULE_TYPE_NOTSCHEDULED:
                default:
                    $data['SendDatetime'] = 0;
                    break;
            }

            $data['ScheduleTypeEnum'] = $entity['scheduleType'];
        }

        // update the object
        $this->SetData($data);
    }

    /**
     * @param stdClass $account
     * @return array[]
     * @throws \Doctrine\DBAL\Exception
     */
    public static function angularApiOverview(stdClass $account): array
    {
        // get db sets

        $result = static::getOfUser($account->uid);

        $stats = static::getOverviewStats($account->uid);

        $linkResolver = new AutomationLinkResolver();

        $entities = [];
        /** @var CampaignsProcessFlow $ObjectEntity */
        foreach ($result as $ObjectEntity) {
            $data = $ObjectEntity->GetData();
            $campaignId = (int)$data['CampaignID'];

            $entity = array(
                'id' => $campaignId,
                'name' => $data['CampaignName'],
                'type' => Campaigns::$APIIndexFilterTypes[$data['AutoResponderTriggerTypeEnum']],
                'status' => $ObjectEntity->getCampaignStatus(),
                'displayStatus' => $ObjectEntity->getCampaignStatus(true),
                'active' => (int)$stats[$campaignId]['active'] ?? 0,
                'finished' => (int)$stats[$campaignId]['finished'] ?? 0,
                'started' => (int)$stats[$campaignId]['started'] ?? 0,
                'linkName' => $linkResolver->linkEdit($account->uid, $campaignId),
                'linkStatistics' => $linkResolver->linkStatistics($account->uid, $campaignId)
            );

            $smartTags = $ObjectEntity->GetSmartTags();

            $entity = array_merge($entity, $smartTags);

            $entities[] = $entity;
        }

        $limitMessage = '';
        $UserGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID);
        $maxAutomations = $UserGroup['Data'][static::$UserGroupLimitField];
        if (!empty($maxAutomations) && $maxAutomations !== UserGroups::LIMIT_UNLIMITED) {
            $count = self::getActiveAndScheduledCampaignCount($account->uid);
            if ($count > $maxAutomations) {
                // after a downgrade, a user could have more automations than allowed, do not show numbers
                $limitMessage = t('Automation::Overview::Message::Maximum number of automations reached.');
            } else {
                $limitMessage = t('Automation::Overview::Message::!count of !max automations created.', [
                    '!count' => $count,
                    '!max' => $maxAutomations
                ]);
            }
        }

        return array(
            'data' => [
                'entities' => $entities,
                'createTypes' => [
                    CreateButtonValueObject::create(
                        t('Automation::Overview::Button::Create automation'),
                        $linkResolver->linkCreate($account->uid)
                    )
                ],
                'messages' => [
                    'automationLimit' => $limitMessage,
                ],
            ]
        );
    }

    /**
     * @param stdClass $account
     * @param int $id
     * @return array[]|false|mixed
     * @throws \ServicesException
     */
    public static function angularApiSettingsRetrieve(stdClass $account, int $id)
    {
        if (!empty($id)) {
            // get db sets

            $objEntity = static::FromID($account->uid, $id);

            if (
                !$objEntity instanceof self ||
                (int) $objEntity->GetData('AutoResponderTriggerTypeEnum') !== self::$CampaignType
            ) {
                return services_error(t('There is no entity with such an id.'), 404);
            }

            $isCreate = false;
        } else {
            // $id == 0, get the data for the create dialog
            $objEntity = static::FromArray([
                'RelOwnerUserID' => $account->uid,
                'CampaignID' => 0,
                'CampaignName' => '',
                'AutoResponderTriggerTypeEnum' => static::$CampaignType,
            ]);

            $isCreate = true;
        }

        $data = $objEntity->GetData();
        $entity = $objEntity->angularApiConvertTo();

        $isDraft = $data['CampaignStatusEnum'] == Campaigns::STATUS_DRAFT;
        $canEditSendDate = $objEntity->canEditSendDate();

        $statusMessage = $objEntity->getReadableStatus();
        $showCancelSendingButton = klicktipp_user_edit_access(
                $account,
                Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER
            ) &&
            (in_array($data['CampaignStatusEnum'], Campaigns::$ArrayCampaignStatiSending) ||
                ($data['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
                    $data['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_IMMEDIATE));

        $canEdit = $isCreate || static::canEdit($data);

        $defaultLocalPath = 'edit';
        if (!$isDraft) {
            $defaultLocalPath = 'statistics';
        }

        $estimateSendDate = $canEditSendDate ? $objEntity->InitializeSendDateMinMaxRecipients() : [
            'min' => 0,
            'max' => 0,
            'offset' => 0,
            'continue' => 0
        ];

        $campaignEmails = $objEntity->collectCampaignEmails();

        $todoListAll = $objEntity->getTodoList();
        $todoList = [];
        $warningList = [];
        foreach ($todoListAll as $todo) {
            if ($todo['isWarning']) {
                $warningList[] = $todo;
            } else {
                $todoList[] = $todo;
            }
        }

        if (!empty($data['MultipleSendFlag'])) {
            // old multisend
            $multisendDelay = CampaignsProcessFlow::getMultisendDelayMinutes();
            $multiSendDescr = t('There is a delay between subsequent runs of !minutes minutes', array('!minutes' => $multisendDelay));
        } else {
            // new multi send, check if a restart action is in the automation
            $hasRestartAction = false;
            foreach ($data['ProcessFlow']['states'] as $state) {
                if ($state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_RESTART) {
                    $hasRestartAction = true;
                    break;
                }
            }

            if ($hasRestartAction) {
                $multiSendDescr = t('ProcessFlow::MultiSend::This automation contains a restart action.');
            } else {
                $multiSendDescr = t('ProcessFlow::MultiSend::If you want to restart this campaign, please use the restart action.');
            }
        }

        $linkResolver = new AutomationLinkResolver();

        return [
            'data' => [
                'entity' => $entity,
                'filterOptions' => [
                    'sendDateScheduleTypes' => [
                        [
                            'value' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
                            'text' => t('Now')
                        ],
                        [
                            'value' => Campaigns::SCHEDULE_TYPE_FUTURE,
                            'text' => t('On given date and time')
                        ],
                        [
                            'value' => Campaigns::SCHEDULE_TYPE_NOTSCHEDULED,
                            'text' => t('Pause')
                        ],
                    ],
                    'estimateSendDate' => $estimateSendDate,
                    'campaignEmails' => $campaignEmails,
                    'todoList' => $todoList,
                    'warningList' => $warningList
                ],
                'displayOptions' => [
                    'accessAudience' => false,
                    'expandAudience' => false,
                    'accessSplittest' => false,
                    'accessConversionPixel' => false,
                    'accessMultipleSend' => !$isCreate && !empty($data['MultipleSendFlag']),
                    'canEdit' => $canEdit,
                    'accessTagCondition' => false,
                    'expandTagCondition' => false,
                    'showTagConditionWith' => false,
                    'showTagConditionNotWith' => false,
                    'canCreateSplittest' => false,
                    'showStatusBar' => true,
                    'showGeneralStats' => $entity['stats']['started'] > 0,
                    'showOpenStats' => false,
                    'showClickStats' => false,
                    'showConversionStats' => false,
                    'showLinkStats' => false,
                    'showCancelSendingButton' => $showCancelSendingButton,
                    'showSpamWarning' => false,
                    'showSplitTestStats' => false,
                    'showSplitTestDuration' => false,
                    'showSplitTestWinnerBox' => false,
                    'showSplitTestOpenStats' => false,
                    'showSplitTestClicksStats' => false,
                    'showSplitTestConversionStats' => false,
                    'showSplitTestRevenueStats' => false,
                    'showSplitTestEmailOverview' => false,
                    'showSplitTestSendDateButton' => false,
                    'showSplitTestEmailEditButtons' => false,
                    'showCampaignEmails' => !empty($campaignEmails),
                    'showTodoList' => $canEditSendDate && !empty($todoList),
                    'showWarningList' => $canEditSendDate && !empty($warningList),
                    'canEditMultipleSend' => false,
                    // only edit the multisend flag when the campaign is already multisend (old multisend)
                    'canEditAutomationMultipleSend' => $canEdit && !empty($data['MultipleSendFlag']),
                    'showAutomationMultipleSendCheckbox' => !empty($data['MultipleSendFlag']),
                    'canEditReceiverEmail' => false,
                    'canEditDelay' => false,
                    'canEditBirthdayDelay' => false,
                    'sendDateCanEdit' => $canEditSendDate,
                    'sendDateReadyToSend' => $canEditSendDate && empty($todoList),
                    'sendDateCanAccess' => klicktipp_user_edit_access(
                        $account,
                        Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER
                    )
                ],
                'messages' => [
                    'statusBarMessage' => $statusMessage,
                    'multipleSendDescription' => $multiSendDescr
                ],
                'localPaths' => [
                    ApiResponseLocalPathValueObject::create(
                        'statistics',
                        t('Campaign::Automation::Affix::View'),
                        $linkResolver->linkStatistics($account->uid, $id),
                        !$isCreate
                    ),
                    ApiResponseLocalPathValueObject::create(
                        'edit',
                        t('Campaign::Automation::Affix::Edit'),
                        $linkResolver->linkEdit($account->uid, $id),
                        !$isCreate
                    ),
                    ApiResponseLocalPathValueObject::create(
                        'cockpit',
                        t('Campaign::Automation::Affix::Marketing Cockpit'),
                        $linkResolver->linkCockpit($id),
                        !$isCreate,
                        true
                    ),
                    ApiResponseLocalPathValueObject::create(
                        'schedule',
                        t('Campaign::Automation::Affix::Schedule'),
                        $linkResolver->linkSchedule($account->uid, $id),
                        $canEditSendDate
                    ),
                ],
                'defaultLocalPath' => $defaultLocalPath
            ]
        ];
    }

    /**
     * @param stdClass $account
     * @param array $data
     * @return \false[][]
     * @throws \ServicesException
     */
    public static function angularApiCreate(stdClass $account, array $data): array
    {
        $objEntity = static::FromArray([
            'RelOwnerUserID' => $account->uid,
            'CampaignName' => '',
            'AutoResponderTriggerTypeEnum' => static::$CampaignType,
            'CampaignStatusEnum' => Campaigns::STATUS_DRAFT
        ]);

        if (!empty($data['copyFromId'])) {
            $copyEntity = static::FromID($account->uid, $data['copyFromId']);

            if (!$copyEntity) {
                $tmpObj = static::FromArray([
                    'RelOwnerUserID' => $account->uid
                ]);
                static::angularApiError([
                    [
                        'code' => static::ANGULAR_API_ERROR_COPY_NOT_FOUND,
                        'message' => $tmpObj->validatorGetMessage(static::ANGULAR_API_ERROR_COPY_NOT_FOUND)
                    ]
                ]);
            }

            $newData = $objEntity->GetData();
            $copyData = $copyEntity->GetData();

            $newData['CampaignName'] = t("Automation::Duplicate of: !name [!datetime]", [
                '!name' => $copyData['CampaignName'],
                '!datetime' => Dates::formatDate(Dates::FORMAT_DMYHIS, time()),
            ]);

            // only copy the process flow and the multi send flag from data, ignore stats
            $newData['ProcessFlow'] = $copyData['ProcessFlow'];
            $newData['MultipleSendFlag'] = $copyData['MultipleSendFlag'];
            $newData['Notes'] = $copyData['Notes'];
            $newData[DatabaseTableLabeled::LABEL_DATA_KEY] = $copyEntity->GetMetaLabels(false);

            $objEntity->SetData($newData);
        } else {
            $objEntity->angularApiConvertFrom($data['entity'] ?? []);
        }

        $errors = $objEntity->validate();

        if (!empty($errors)) {
            static::angularApiError($errors);
        }

        $createData = $objEntity->GetData();

        $newId = static::InsertDB($createData);

        if (!$newId) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_CREATE_FAILED,
                    'message' => $objEntity->validatorGetMessage(static::ANGULAR_API_ERROR_CREATE_FAILED)
                ]
            ]);
        }

        if (!empty($data['copyFromId'])) {
            // also copy split tests if exist
            $objEntity = static::FromID($account->uid, $newId);
            if ($objEntity) {
                $entities = array();
                $objEntity->TraverseProcessFlow(
                    $entities,
                    '\App\KlickTipp\CampaignsProcessFlow::traverseCopySplitTest'
                );
                CampaignsProcessFlow::UpdateDB($objEntity->GetData());
            }
        }

        return [
            'data' => [
                'id' => (int)$newId,
            ]
        ];
    }

    /**
     * @param stdClass $account
     * @param int $id
     * @param array $entity
     * @return false|\int[][]|mixed
     * @throws \ServicesException
     */
    public static function angularApiSettingsSave(stdClass $account, int $id, array $data)
    {
        $objEntity = static::FromID($account->uid, $id);

        if (empty($objEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        $objEntity->angularApiConvertFrom($data['entity']);

        $errors = $objEntity->validate();

        if (!empty($errors)) {
            static::angularApiError($errors);
        }

        $createData = $objEntity->GetData();

        if (!static::UpdateDB($createData)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPDATE_FAILED,
                    'message' => $objEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPDATE_FAILED)
                ]
            ]);
        }

        return [
            'data' => [
                'id' => $id,
            ]
        ];
    }

    /**
     * Cancel sending of newsletter
     * @param $account
     * @param $id
     * @return array[]
     * @throws \ServicesException
     */
    public static function angularApiCancelSending(stdClass $account, int $id): array
    {
        if (!klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER)) {
            return services_error(t('API access denied.'), 403);
        }

        if (!Campaigns::StopCampaign($account->uid, $id)) {
            $objEntity = static::FromID($account->uid, $id);

            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_CANCEL_SENDING,
                    'message' => $objEntity->validatorGetMessage(static::ANGULAR_API_ERROR_CANCEL_SENDING)
                ]
            ]);
        }

        return [
            'data' => [
                'id' => $id,
                'message' => t("Automation successfully stopped.")
            ]
        ];
    }

    /**
     * Set the send date of the newsletter
     * @param stdClass $account
     * @param int $id
     * @param array $data
     * @return array
     * @throws \ServicesException
     * @throws Exception
     */
    public static function angularApiSetSendDate(stdClass $account, int $id, array $data): array
    {
        $objEntity = static::FromID($account->uid, $id);

        if (!$objEntity instanceof self) {
            services_error(t('There is no entity with such an id.'), 404);
        }

        $entity = $objEntity->GetData();

        if (
            ($entity['CampaignStatusEnum'] === Campaigns::STATUS_READY &&
                $entity['ScheduleTypeEnum'] === Campaigns::SCHEDULE_TYPE_IMMEDIATE) ||
            in_array($entity['CampaignStatusEnum'], Campaigns::$ArrayCampaignStatiSending, true)
        ) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_SEND_DATE_ALREADY_SENT,
                    'message' => $objEntity->validatorGetMessage(static::ANGULAR_API_ERROR_SEND_DATE_ALREADY_SENT)
                ]
            ]);
        }

        // if schedule type is not paused (not scheduled), check if the limit is reached
        if (!empty($data['entity']['scheduleType'])) {
            $userGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID);
            $limit = $userGroup['Data'][static::$UserGroupLimitField];
            if ($limit !== UserGroups::LIMIT_UNLIMITED && $objEntity->scheduledLimitReached($limit)) {
                static::angularApiError([
                    [
                        'code' => static::ANGULAR_API_ERROR_LIMIT_EXCEEDED,
                        'message' => $objEntity->validatorGetMessage(static::ANGULAR_API_ERROR_LIMIT_EXCEEDED)
                    ]
                ]);
            }
        }

        $objEntity->angularApiConvertFrom($data['entity']);

        $errors = $objEntity->validate();

        if (!empty($errors)) {
            static::angularApiError($errors);
        }

        $updateData = $objEntity->GetData();

        if ($updateData['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED) {
            $updateData['CampaignStatusEnum'] = Campaigns::STATUS_DRAFT;
        } else {
            $updateData['CampaignStatusEnum'] = Campaigns::STATUS_READY;
        }

        if (!static::UpdateDB($updateData)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_SEND_DATE_UPDATE_FAILED,
                    'message' => $objEntity->validatorGetMessage(static::ANGULAR_API_ERROR_SEND_DATE_UPDATE_FAILED)
                ]
            ]);
        }

        $message = t('Send date successfully updated.');

        if (
            $updateData['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
            $updateData['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_IMMEDIATE
        ) {
            if (Campaigns::ProcessCampaign($account->uid, $id)) {
                $message = t('Campaign successfully started.');
            }
        }

        return [
            'data' => [
                'id' => $id,
                'message' => $message
            ]
        ];
    }

    /**
     * @param stdClass $account
     * @param int $id
     * @param array $data
     * @return array[]
     * @throws \ServicesException
     */
    public static function angularApiEstimateRecipients(stdClass $account, int $id, array $data): array
    {
        $objEntity = static::FromID($account->uid, $id);

        if (!$objEntity instanceof self) {
            services_error(t('There is no entity with such an id.'), 404);
        }

        $userGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID);
        $limit = $userGroup['Data'][static::$UserGroupLimitField];
        if ($limit !== UserGroups::LIMIT_UNLIMITED && $objEntity->scheduledLimitReached($limit)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_LIMIT_EXCEEDED,
                    'message' => $objEntity->validatorGetMessage(static::ANGULAR_API_ERROR_LIMIT_EXCEEDED)
                ]
            ]);
        }

        [$MinEstimatedRecipients, $MaxEstimatedRecipients, $TimerShallContinue, $NextRefreshOffset] =
            $objEntity->CalculateSenddateMinMaxRecipients(
                $account->uid,
                intval($data['data']['offset']) ?: 0,
                intval($data['data']['min']) ?: 0,
                intval($data['data']['max']) ?: 0
            );

        return [
            'data' => [
                'min' => $MinEstimatedRecipients,
                'max' => $MaxEstimatedRecipients,
                'offset' => $NextRefreshOffset,
                'continue' => $TimerShallContinue ? 1 : 0,
            ]
        ];
    }

    /**
     * @param stdClass $account
     * @param int $id
     * @return array
     * @throws \ServicesException
     */
    public static function angularApiDelete(stdClass $account, int $id): array
    {
        $objEntity = static::FromID($account->uid, $id);

        if (empty($objEntity)) {
            services_error(t('There is no entity with such an id.'), 404);
        }

        if (!static::DeleteDB($objEntity->GetData())) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_DELETE_FAILED,
                    'message' => $objEntity->validatorGetMessage(static::ANGULAR_API_ERROR_DELETE_FAILED)
                ]
            ]);
        }

        return [
            'data' => [
                'id' => 0,
                'message' => t(
                    'Automation::Delete::Automation %name successfully deleted.',
                    array('%name' => $objEntity->GetData('CampaignName'))
                )
            ]
        ];
    }

    protected function validatorPrepareMessages(): void
    {
        parent::validatorPrepareMessages();
        $this->validatorMessages[static::ANGULAR_API_ERROR_LIMIT_EXCEEDED] = t(
            'AngularApi::Error::Automation::Limit exceeded'
        );
    }

    /**
     * Create free value tags in TaggedWith and/or NotTaggedWith if necessary
     *
     * @return bool
     */
    protected function createRecipientsTags(): bool
    {
        $data = $this->GetData();

        $account = user_load($data['RelOwnerUserID']);

        $isAr = Campaigns::IsAutoresponder($data['AutoResponderTriggerTypeEnum']);

        $recipients = $data['RecipientLists'];
        //Note: last param: TRUE -> do not create manual tags from smart tag id @see: Tag::CreateManualTagByTagName
        if (!empty($recipients['TaggedWith'])) {
            $recipients['TaggedWith'] = Tag::CreateManualTagByTagName($account->uid, $recipients['TaggedWith'], true);
        }
        if (!empty($recipients['NotTaggedWith'])) {
            $recipients['NotTaggedWith'] = Tag::CreateManualTagByTagName(
                $account->uid,
                $recipients['NotTaggedWith'],
                true
            );
        }

        $multipleSendTags = $data['MultipleSendTags'];
        if ($isAr && !empty($multipleSendTags)) {
            $multipleSendTags = Tag::CreateManualTagByTagName($account->uid, $multipleSendTags, true);
        }

        // reset cache
        Tag::ResetTagsCache($account->uid);

        if ($recipients['TaggedWith'] === false || $recipients['NotTaggedWith'] === false || $data['MultipleSendTags'] === false) {
            //error creating at least 1 tag
            $error = array(
                '!UserID' => $account->uid,
                '!function' => __FUNCTION__,
                '!before' => $data,
                '!after' => $recipients,
                '!multipleSendTagsAfter' => $multipleSendTags,
            );
            Errors::unexpected("Error: CreateManualTagByTagName for User !UserID", $error);

            return false;
        }

        $data['RecipientLists'] = $recipients;
        $data['MultipleSendTags'] = $multipleSendTags;
        $this->SetData($data);

        return true;
    }

    /**
     * @param bool $asDisplayStatus
     *
     * @return string
     */
    public function getCampaignStatus(bool $asDisplayStatus = false): string
    {
        switch ($this->GetData('CampaignStatusEnum')) {
            case Campaigns::STATUS_READY:
                if ($this->GetData('ScheduleTypeEnum') == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED) {
                    $status = 'paused';
                    $displayStatus = t('Campaigns::Status::Paused');
                } elseif ($this->GetData('ScheduleTypeEnum') == Campaigns::SCHEDULE_TYPE_FUTURE) {
                    $status = 'scheduled';
                    $displayStatus = t('Campaigns::Status::Scheduled');
                } else {
                    $status = 'active';
                    $displayStatus = t('Campaigns::Status::Active');
                }
                break;
            case Campaigns::STATUS_DRAFT:
                $status = 'draft';
                $displayStatus = t('Campaigns::Status::Draft');
                break;
            default:
                $status = 'active';
                $displayStatus = t('Campaigns::Status::Active');
                break;
        }

        return $asDisplayStatus ? $displayStatus : $status;
    }

    /**
     * @return string
     */
    public function getReadableStatus(): string
    {
        $data = $this->GetData();

        // SendProcessFinishedOn
        $SendProcessFinishedOn = '';
        if ($data['CampaignStatusEnum'] == Campaigns::STATUS_DRAFT) {
            $SendProcessFinishedOn = t('Status: This automation has not been started yet.');
        } elseif (
            $data['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
            $data['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_IMMEDIATE
        ) {
            $SendProcessFinishedOn = t('Status: This automation is scheduled for now.');
        } elseif (
            $data['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
            $data['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_FUTURE
        ) {
            $SendProcessFinishedOn = t(
                'Status: This automation is scheduled for !date.',
                array('!date' => Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $data['SendDatetime']))
            );
        } elseif (
            $data['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
            $data['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED
        ) {
            $SendProcessFinishedOn = t('Status: This automation has been canceled.');
        } elseif (in_array($data['CampaignStatusEnum'], Campaigns::$ArrayCampaignStatiSending)) {
            $SendProcessFinishedOn = t('Status: This automation is active.');
        } else {
            $SendProcessFinishedOn = t(
                'Status: Sent on !date',
                array('!date' => Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $data['SendProcessFinishedOn']))
            );
        }

        return $SendProcessFinishedOn;
    }

    /**
     * @return array
     * @throws \ServicesException
     */
    public function collectCampaignEmails(): array
    {
        $result = array();
        $result = $this->TraverseProcessFlow($result, '\App\KlickTipp\CampaignsProcessFlow::traverseCollectEmails');
        $emails = array();

        $linkResolver = new EmailOverviewLinkResolver(
            new EmailAutomationLinkResolver(),
            new EmailNotificationLinkResolver(),
            new SmsAutomationLinkResolver(),
            new SmsNotificationLinkResolver()
        );

        if (!empty($result)) {
            foreach ($result as $objEmail) {
                if (!$objEmail instanceof EmailsAndSms) {
                    continue;
                }

                $stats = $objEmail->GetTaggingStats();
                $type = $objEmail->GetData('EmailType');

                $clicked = (int)($stats['clicked'] ?? 0);

                if (
                    in_array($type, [
                        Emails::TYPE_AUTOMATIONSMS,
                        Emails::TYPE_NEWSLETTERSMS
                    ], true)
                ) {
                    $opened = -1; //shows dash in table
                    $openRate = -1.0; //shows dash in table
                    $clickRate = $stats['sent'] > 0 ? round(100 * $clicked / $stats['sent'], 1) : 0.0;
                } else {
                    $opened = (int)($stats['opened'] ?? 0);
                    $openRate = $stats['sent'] > 0 ? round(100 * $opened / $stats['sent'], 1) : 0.0;
                    $clickRate = $opened > 0 ? round(100 * $clicked / $opened, 1) : 0.0;
                }

                //$emails[] = $objEmail->angularApiOverviewConvertTo();

                $emails[] = EmailOverviewResponseEntityValueObject::create(
                    (int)$objEmail->GetData('EmailID'),
                    $objEmail->GetName(),
                    Emails::$APIIndexFilterTypes[$type],
                    t(/*ignore*/ Emails::$DisplayEmailType[$type]),
                    (int) $stats['sent'],
                    $opened,
                    $openRate,
                    $clicked,
                    $clickRate,
                    $linkResolver->linkEdit($objEmail),
                    $linkResolver->linkStatistics($objEmail)
                );
            }
        }

        return $emails;
    }

    public function getTodoList(): array
    {
        $data = $this->GetData();
        $UserID = $data['RelOwnerUserID'];
        $CampaignID = $data['CampaignID'];
        $todoList = CampaignsProcessFlow::ValidateAutomation($data);

        $items = [];
        if (!empty($todoList)) {
            foreach ($todoList as $item) {
                $state = CampaignsProcessFlow::FindState($data, $item[0]);
                $message = t(/*ignore*/ CampaignsProcessFlow::$DisplayValidationError[$item[1]]);
                $isWarning = false;

                switch ($item[1]) {
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_INFO_CONDITION_EMPTY:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_INFO_CONDITION_SEGMENT_EMPTY:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_TAG:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_FIELD:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_CONDITION_INVALID_OP:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_TIME_DELAY:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_FIELD_TYPE:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_INVALID_DELAY_TYPE:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EXIT_HAS_NEXT:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_START_ITSELF:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_STOP_ITSELF:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_CONTENT:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_CAMPAIGN:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NO_TARGET:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_ACTIVATION_URL:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_OUTBOUND:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_NOTIFY_NO_RECEIVER:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_TAG:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_FIELD:
                        $automationLinkResolbver = new AutomationLinkResolver();
                        $url = $automationLinkResolbver->linkCockpit($CampaignID);
                        break;
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_SUBJECT:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_CONTENT:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NOT_PUBLISHED:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SMS_NO_CONTENT:
                        switch ($state['type']) {
                            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL:
                                $emailLinkResolver = new EmailAutomationLinkResolver();
                                $url = $emailLinkResolver->linkEdit($UserID, $state['emailID']);
                                break;
                            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDSMS:
                                $emailLinkResolver = new SmsAutomationLinkResolver();
                                $url = $emailLinkResolver->linkEdit($UserID, $state['emailID']);
                                break;
                            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL:
                                $emailLinkResolver = new EmailNotificationLinkResolver();
                                $url = $emailLinkResolver->linkEdit($UserID, $state['emailID']);
                                break;
                            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS:
                                $emailLinkResolver = new SmsNotificationLinkResolver();
                                $url = $emailLinkResolver->linkEdit($UserID, $state['emailID']);
                                break;
                            default:
                                $url = '';
                                break;
                        }

                        break;
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_WARNING_POSSIBLE_DELAY:
                        $isWarning = true;
                        $url = '';
                        break;
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_NODE_INVALID:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_START_NOT_FOUND:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_UNREACHABLE_NODES:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_NO_ACTION_FOUND:
                    case CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_POSSIBLE_ENDLESS_LOOP:
                    default:
                        $url = '';
                        break;
                }

                $items[] = [
                    'name' => CampaignsProcessFlow::GetStateName($state),
                    'message' => $message,
                    'url' => $url,
                    'isWarning' => $isWarning
                ];
            }
        }

        return $items;
    }

    /**
     * @throws Exception
     */
    public function scheduledLimitReached(int $limit): bool
    {
        $status = $this->getCampaignStatus();

        if ($status === 'scheduled') {
            // the automation is already scheduled so within the current limit
            // the user can reschedule
            return false;
        }

        // count active and scheduled campaigns
        if ($limit <= self::getActiveAndScheduledCampaignCount($this->GetData('RelOwnerUserID'))) {
            return true;
        }

        return false;
    }

    /**
     * @param array $DBArray
     * @return bool
     */
    public static function canEdit(array $DBArray): bool
    {
        if (
            !klicktipp_user_edit_access(
                user_load($DBArray['RelOwnerUserID']),
                Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER
            )
        ) {
            return false;
        }
        return (int)$DBArray['CampaignStatusEnum'] === self::STATUS_DRAFT ||
            ((int)$DBArray['CampaignStatusEnum'] === self::STATUS_READY &&
                (int)$DBArray['ScheduleTypeEnum'] !== self::SCHEDULE_TYPE_IMMEDIATE);
    }

    public function reterminateQueueItems(): int
    {
        return db_update(TransactionalQueue::TABLE_NAME)
            ->fields(['TimeToSend' => time()])
            ->condition('RelOwnerUserID', $this->GetData('RelOwnerUserID'))
            ->condition('RelAutoResponderID', $this->GetData('CampaignID'))
            ->condition('StatusEnum', TransactionEmails::STATUS_WAITING_FOR_TEMPORAL_CONDITION)
            ->execute();
    }

    /**
     * Retrive a list of tags to prefilter subscribers.
     *
     * For now, prefiltering is only supported for tagsconditions and only if
     *  - all segments are combined with AND
     *  - all conditions in a segment are combined with AND
     *
     * @return RecipientListArray|null
     */
    public function getRecipientList(): ?array
    {
        $data = $this->GetData();
        $state = static::FindState($data, $data['ProcessFlow']['start']);

        $segments = $state['segments'] ?? [];

        $opTaggedWith = Campaigns::TAG_HAS_ALL;

        if (empty($state['segmentsOpAND']) && count($segments) > 1) {
            $opTaggedWith = Campaigns::TAG_HAS_ANY;
        }

        $taggedWith = [];
        foreach ($segments as $segment) {
            $conditions = $segment['conditions'] ?? [];

            // multiple segments combined with OR only supported in case of one condition per segment
            if ($opTaggedWith == Campaigns::TAG_HAS_ANY && count($conditions) > 1) {
                return null;
            }

            if (empty($segment['conditionsOpAND']) && count($conditions) > 1) {
                if (count($segments) == 1) {
                    $opTaggedWith = Campaigns::TAG_HAS_ANY;
                } else {
                    return null;
                }
            }

            foreach ($conditions as $condition) {
                if ($condition['op'] !== self::PROCESSFLOW_CONDITION_IS_TAGGED) {
                    if ($opTaggedWith == Campaigns::TAG_HAS_ANY) {
                        return null;
                    }
                    continue;
                }
                $taggedWith[$condition['field']] = $condition['field'];
            }
        }

        if ($taggedWith) {
            return [
                'OpTaggedWith' => $opTaggedWith,
                'TaggedWith' => $taggedWith,
                'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
                'NotTaggedWith' => [],
            ];
        }

        return null;
    }
}

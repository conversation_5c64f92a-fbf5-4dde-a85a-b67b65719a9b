<?php

namespace App\Klicktipp;

/**
 * Class SubscriberDuplicateEvents
 *
 * Contains detection type independent event constants and event related db logic
 */
class SubscriberDuplicateEvents
{
    const PROBABILITY_MIDDLE = 20;
    const PROBABILITY_HIGH = 40;
    const PROBABILITY_VERY_HIGH = 60;
    const PROBABILITY_MAXIMUM = 80;
    const PROBABILITY_MANUAL = 101;

    protected static $typeMap = [
        SubscriberDuplicateDetectionIpAddress::TYPE => 'ip',
        SubscriberDuplicateDetectionCookie::TYPE => 'cookie',
        SubscriberDuplicateDetectionEmailSimilarity::TYPE => 'email-similarity',
        SubscriberDuplicateDetectionManual::TYPE => 'manual',
    ];

    /**
     * @param int $statusCode one of STATUS_*-constants
     *
     * @return string name of status if statusCode is known, otherwise 'unknown'
     */
    public static function getTypeName($statusCode)
    {
        if (isset(static::$typeMap[$statusCode])) {
            return static::$typeMap[$statusCode];
        }
        return 'unknown';
    }

    /**
     * Delete all events for a duplicate
     *
     * @param int $duplicateID
     */
    public static function deleteEvents($duplicateID)
    {
        kt_delete_rows(
            ['DupID' => $duplicateID],
            '{subscriber_duplicate_events}'
        );
    }

    /**
     * Inserts (or updates if exist) a duplicate event
     *
     * @param array $duplicateEvent
     */
    public static function storeEvent(array $duplicateEvent)
    {
        if (empty($duplicateEvent['Data']) || !is_array($duplicateEvent['Data'])) {
            // don't use JSON_FORCE_OBJECT here since it converts inner array also to objects
            $json = '{}';
        } else {
            $json = json_encode($duplicateEvent['Data']);
        }
        db_query(
            'INSERT INTO {subscriber_duplicate_events} ' .
            '(`DupID`, `Type`, `Probability`, `Date`, `Data`) ' .
            'VALUES (:dupID, :type, :probability,  :date, :json) ' .
            'ON DUPLICATE KEY UPDATE `Date` =  :date, `Data`= JSON_MERGE_PRESERVE(COALESCE(`Data`, :empty_json), :json) ',
            [
                ':dupID' => $duplicateEvent['DupID'],
                ':type' => $duplicateEvent['Type'],
                ':probability' => $duplicateEvent['Probability'],
                ':status' => $duplicateEvent['Status'],
                ':date' => $duplicateEvent['Date'],
                ':json' => $json,
                ':empty_json' => '{}'
            ]
        );
    }
}

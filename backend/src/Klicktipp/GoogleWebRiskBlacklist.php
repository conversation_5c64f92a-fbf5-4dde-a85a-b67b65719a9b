<?php

namespace App\Klicktipp;

use App\Klicktipp\Cache\DrupalPsrCacheItemPoolAdapter;
use Exception;
use Google\Cloud\WebRisk\V1\ThreatType;
use Google\Cloud\WebRisk\V1\WebRiskServiceClient;

/**
 * Checks urls by using Google Web Risk API
 */
class GoogleWebRiskBlacklist extends UrlBlacklist
{
    const TYPE = 'webrisk';

    const THREAT_TYPES = [
        ThreatType::MALWARE,
        ThreatType::SOCIAL_ENGINEERING,
        ThreatType::UNWANTED_SOFTWARE
    ];

    /**
     * @var WebRiskServiceClient
     */
    private static $client;

    /**
     * @return WebRiskServiceClient
     * @throws \Google\ApiCore\ValidationException
     */
    private static function getClient()
    {
        if (!static::$client) {
            static::$client = new WebRiskServiceClient(
                [
                    'credentials' => Settings::get('klicktipp_blacklist_webrisk_credentials'),
                    'credentialsConfig' => [
                        'enableCaching' => true,
                        'authCache' => new DrupalPsrCacheItemPoolAdapter(static::class)
                    ]
                ]
            );
        }
        return static::$client;
    }

    /**
     * Indicates if url is blacklisted
     *
     * @see https://en.wikipedia.org/wiki/Domain_Name_System-based_Blackhole_List
     *
     * @param string $url
     *
     * @return array first element is a bool result (TRUE if blacklisted), second one is list type, third one is full TXT string and last one is a lookup url (if exists)
     */
    static function isUrlBlacklisted($url)
    {
        $default = [false, static::TYPE, '', ''];
        try {
            $response = static::getClient()->searchUris($url, static::THREAT_TYPES);
        } catch (Exception $exception) {
            watchdog('kt-blacklist', 'google webrisk api exception !message', ['!exception' => $exception]);
            return $default;
        }

        if ($response->hasThreat()) {
            $lookupUrl = '';
            $lookupEndppoint = Settings::get('klicktipp_blacklist_webrisk_check_url');
            if ($lookupEndppoint) {
                $lookupUrl = $lookupEndppoint . '?' . http_build_query(['url' => $url]);
            }

            return [true, static::TYPE, '', $lookupUrl];
        }

        return $default;
    }
}

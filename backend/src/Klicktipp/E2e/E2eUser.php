<?php

namespace App\Klicktipp\E2e;

use App\Klicktipp\Campaigns;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\MarketingTools;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\Reference;
use App\Klicktipp\Signatures;
use App\Klicktipp\SplitTests;
use App\Klicktipp\Statistics;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\UserCache;
use App\Klicktipp\UserVariables;
use App\Klicktipp\VarAdditionalAddresses;
use App\Klicktipp\VarTier;
use Exception;
use stdClass;

class E2eUser extends E2e
{
    protected stdClass $account;

    /**
     * @throws Exception
     */
    public function __construct(string $username)
    {
        if (strpos(KLICKTIPP_DOMAIN, '.klicktipp.com') !== false) {
            // show a not found to give no indication that this file exists
            throw new Exception("Not found");
        }

        if (strpos($username, self::DEV_USERNAME_PREFIX) !== 0) {
            // Access is done by username since permissions of the user could change for test cases
            throw new Exception("Access denied: Use user with name prefix " . self::DEV_USERNAME_PREFIX);
        }

        $account = user_load_by_name($username);
        if (!$account || !$account->uid) {
            throw new Exception("Access denied: User not found '$username'");
        }

        $this->account = $account;
    }

    public function prepare(array $data): array
    {
        $steps = $data['data']['testCaseDataCreation'];

        if (empty($steps)) {
            // Noting to do
            return [
                'state' => 'COMPLETE'
            ];
        }

        $currentStep = $data['data']['currentStep'] ?? 0;
        $leftover = $data['data']['leftover'] ?? [];

        foreach ($steps as $index => $step) {
            if ($index < $currentStep) {
                // step has already been executed
                continue;
            }

            $class = $step['class'];
            if (!class_exists($class)) {
                // TODO: just because of Reset and Account
                $class = __NAMESPACE__ . '\\' . $step['class'];
            }

            $params = $step['params'] ?? [];

            if (class_exists($class)) {
                try {
                    $e2eData = new $class($this);

                    $leftover = $e2eData->execute($params, $leftover);

                    if (!empty($leftover)) {
                        $data['data']['currentStep'] = $index;
                        $data['data']['leftover'] = $leftover;

                        return [
                            'state' => 'PROGRESS',
                            'data' => $data
                        ];
                    }
                } catch (Exception $e) {
                    return [
                        'state' => 'ERROR',
                        'message' => $e->getMessage()
                    ];
                }
            } else {
                return [
                    'state' => 'ERROR',
                    'message' => "Data creation step '{$step['class']}' not found"
                ];
            }
        }

        return [
            'state' => 'COMPLETE',
        ];
    }

    public function getAccount(): stdClass
    {
        return $this->account;
    }

    public function getUserID(): int
    {
        return $this->account->uid;
    }

    /**
     * @param array $params
     * @param array $leftover
     * @return array
     * @throws \Doctrine\DBAL\Exception
     */
    public function setup(array $params, array $leftover = []): array
    {
        $e2eConfig = new E2eConfig();

        $defaultArgs = [
            'userGroupId' => 31,
            'roleNames' => ['Enterprise'],
        ];

        $args = array_merge($defaultArgs, $params);

        $userId = $this->getUserID();

        // set the digistore_receipt
        $account = $this->getAccount();
        user_save($account, ['digistore_receipt' => KLICKTIPP_DOMAIN . "//e2e-user-invoice-url-dummy"]);

        // remove roles of users from previous test case
        db_query('DELETE FROM {users_roles} WHERE uid = :userId', [
            'userId' => $userId
        ]);

        // add roles of test case

        $roles = $e2eConfig->getAvailableRoles();
        foreach ($roles as $role) {
            if (in_array($role['name'], $args['roleNames'])) {
                db_insert('users_roles')
                    ->fields(array(
                        'uid' => $userId,
                        'rid' => $role['id'],
                    ))
                    ->execute();
            }
        }

        if ($args['userGroupId']) {
            // set UserGroup
            db_query("UPDATE {users} SET RelUserGroupID = :groupId WHERE uid = :userId", [
                'groupId' => $args['userGroupId'],
                'userId' => $userId
            ]);

            $userGroups = $e2eConfig->getAvailableGroups();
            $userGroup = $userGroups[$args['userGroupId']] ?? [];
            if (!empty($userGroup['tierInAccount']) && !empty($args['tierInAccount'])) {
                VarTier::SetTier($userId, $args['tierInAccount']);
            }
        }

        // clear user cache
        UserCache::clear($userId);
        UserCache::remove($userId, UserCache::CACHE_ACCOUNT_ACCESS_INFO);

        return [];
    }

    /**
     * Remove all data from user (optimized from klicktipp module hook user_cancel)
     * Add necessary data like default signature, DOI etc (optimized from klicktipp module hook user_insert)
     * @return void
     */
    public function reset(array $leftover = []): array
    {
        $account = $this->getAccount();
        $userId = $this->getUserID();

        // delete all listbuildings
        Listbuildings::DeleteOfUser($userId);
        MarketingTools::DeleteOfUser($userId);

        Tag::DeleteTagsOfUser($userId);
        Subscribers::RemoveSubscribersOfUser($userId);
        Reference::DeleteOfUser($userId);

        Emails::DeleteOfUser($userId);
        Signatures::DeleteOfUser($userId);

        DomainSet::DeleteWhitelabelDomains($userId);

        // delete transaction data first, as all other functions are less efficient in doing so
        TransactionEmails::DeleteTransactionEmailsOfUser($userId);
        Campaigns::DeleteOfUser($userId);
        SplitTests::DeleteOfUser($userId);

        Lists::DeleteOfUser($userId);

        CustomFields::DeleteOfUser($userId);

        // stats last
        Statistics::DeleteStatisticsOfUser($userId);

        kt_delete_rows(array('UserID' => $userId), '{fbl_reports}');

        //delete user inbound sms numbers
        kt_delete_rows(array('RelOwnerUserID' => $userId), '{smsnumbers}');

        MetaLabels::DeleteOfUser($userId);

        UserVariables::DeleteOfUser($userId);

        $SignatureID = Signatures::CreateDefaultSignature($userId);
        Signatures::CreateRevision($userId, $SignatureID);

        // create default subscription process
        Lists::InsertDB(array(
            'Name' => '',
            'RelOwnerUserID' => $userId
        ));

        Reference::createDefaultReference($userId);
        VarAdditionalAddresses::SetAddresses(
            $account->uid,
            [['email' => $account->mail, 'verified' => 1, 'valid' => 1]]
        );

        // clear user cache
        UserCache::clear($userId);

        return [];
    }
}

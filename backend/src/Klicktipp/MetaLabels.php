<?php

namespace App\Klicktipp;

use stdClass;
use Doctrine\DBAL\Exception as DoctrineDBALException;

class MetaLabels extends DatabaseTableCRUD
{
    const TYPE = 'metalabel';

    public static $APIPath = 'kt-metalabels';

    public static $DBTableName = 'meta_labels';

    public static $DBSerialField = 'MetaLabelID';

    public static $DBUserField = 'RelOwnerUserID';

    public static $APIFields = array(
        'MetaLabelID' => array(
            'id' => 'id',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'Type' => array(
            'id' => 'type',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Name' => array(
            'id' => 'name',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Description' => array(
            'id' => 'description',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
    );

    public static $APIIndexFilterTypes = array(
        MetaLabels::TYPE => MetaLabels::TYPE,
    );

    public static $RequiredFieldsOnInsert = array(
        'RelOwnerUserID',
        'Name',
    );

    public static $RequiredFieldsOnUpdate = array(
        'MetaLabelID',
        'RelOwnerUserID',
    );

    public static $DefaultFields = array(
        'Description' => '',
    );

    public static $PageSizes = KLICKTIPP_PAGER_PAGE_SIZES;

    public static function FromArray($DBArray)
    {
        $entity = new MetaLabels($DBArray);

        if (empty($entity->DataFlat)) {
            return false;
        }

        //we need a type in for the cockpit
        $entity->DataFlat['Type'] = MetaLabels::TYPE;

        return $entity;
    }

    /**
     * delete label from database and remove all labelings
     *
     * @param $Data
     * @return bool
     */
    public static function DeleteDB($Data)
    {
        $result = parent::DeleteDB($Data);

        if (!empty($result)) {
            $query = "DELETE FROM {meta_labeled} WHERE RelOwnerUserID = :RelOwnerUserID AND RelMetaLabelID = :RelMetaLabelID";

            db_query($query, array(
                'RelOwnerUserID' => $Data['RelOwnerUserID'],
                'RelMetaLabelID' => $Data['MetaLabelID'],
            ));
        }

        return $result;
    }

    public static function FilterObject($data)
    {
        $result = parent::FilterObject($data);

        foreach ($data as $key => $value) {
            if (isset(static::$APIFields[$key])) {
                switch ($key) {
                    case 'Type':
                        //the type is needed in the cockpit
                        $result[static::$APIFields[$key]['id']] = MetaLabels::TYPE;
                        break;
                }
            }
        }

        return $result;
    }

    public static function UnFilterObject($data, $result = [], $isObjectNew = false)
    {
        $result = parent::UnFilterObject($data, $result);

        foreach (static::$APIFields as $key => $def) {
            if (isset($data[$def['id']])) {
                switch ($key) {
                    case 'Type':
                        //the type is only needed in the cockpit
                        unset($result[$key]);
                }
            }
        }

        return $result;
    }

    /**
     * Replace (update) labels of entity
     * Compares new set of labels with existing labels of entity and adds/removes
     * @param $UserID
     * @param $BaseType
     * @param $EntityType
     * @param $EntityID
     * @param $UpdateMetaLabels
     *
     * @return bool
     */
    public static function SetLabelsOfEntity($UserID, $BaseType, $EntityType, $EntityID, $UpdateMetaLabels)
    {
        if (empty($UserID) || empty($EntityID) || !isset($UpdateMetaLabels)) {
            return false;
        }

        if (empty(DatabaseNamedTable::$EntityBaseTypeMap[$BaseType])) {
            return false;
        }

        $oldLabels = static::GetLabelsOfEntity($UserID, $BaseType, $EntityID);

        $newLabels = array_diff($UpdateMetaLabels, $oldLabels);

        if (!empty($newLabels)) {
            static::AddLabelList($UserID, $newLabels, $BaseType, $EntityType, $EntityID);
        }

        $removedLabels = array_diff($oldLabels, $UpdateMetaLabels);

        if (!empty($removedLabels)) {
            static::RemoveLabelsFromEntity($UserID, $BaseType, $EntityID, $removedLabels);
            static::DeleteUnusedLabels($UserID);
        }

        static::ResetLabelsOfEntityCache($UserID, $BaseType, $EntityID);

        return true;
    }

    /**
     * Returns all labels (names) of an entity
     * @param $UserID
     * @param $BaseType
     * @param $EntityID
     *
     * @return array
     */
    public static function GetLabelsOfEntity($UserID, $BaseType, $EntityID, $AsOptionsArray = false)
    {
        $entitylabels = &drupal_static('metalabelsGetLabelsOfEntity', array());

        if (empty($UserID) || empty($EntityID)) {
            return [];
        }

        if (empty(DatabaseNamedTable::$EntityBaseTypeMap[$BaseType])) {
            return [];
        }

        if (!isset($entitylabels[$UserID][$BaseType])) {
            // get and cache all labels for this base type as e.g. klicktippapi_index_advanced needs all labels
            $query = "SELECT label.Name, labeled.RelEntityID FROM {meta_labeled} labeled " .
                "INNER JOIN {meta_labels} label ON labeled.RelOwnerUserID = label.RelOwnerUserID AND labeled.RelMetaLabelID = label.MetaLabelID " .
                "WHERE labeled.RelOwnerUserID = :UserID AND labeled.BaseType = :BaseType";
            $result = db_query($query, [
                ':BaseType' => $BaseType,
                ':UserID' => $UserID,
            ]);

            while ($label = kt_fetch_array($result)) {
                $entitylabels[$UserID][$BaseType][$label['RelEntityID']][$label['Name']] = check_plain($label['Name']);
            }
        }
        $LabelList = $entitylabels[$UserID][$BaseType][$EntityID];

        if (empty($LabelList)) {
            // make sure, that we get an array as result (to ensure valid array ops)
            return [];
        }
        return ($AsOptionsArray) ? $LabelList : array_keys($LabelList);
    }

    /*
     * clear cache for entity in GetLabelsOfEntity
     */
    public static function ResetLabelsOfEntityCache($UserID, $BaseType, $EntityID)
    {
        $entitylabels = &drupal_static('metalabelsGetLabelsOfEntity', array());
        // reset cache for all entities of base type, as cache read works that way
        unset($entitylabels[$UserID][$BaseType]);
    }

    /**
     * Add a label by name to an entity
     * @param $UserID
     * @param $LabelName
     * @param $BaseType
     * @param $EntityType
     * @param $EntityID
     *
     * @return bool
     */
    public static function AddLabel($UserID, $LabelName, $BaseType, $EntityType, $EntityID)
    {
        if (empty($UserID) || empty($LabelName) || empty($EntityID)) {
            return false;
        }

        if (empty(DatabaseNamedTable::$EntityBaseTypeMap[$BaseType])) {
            return false;
        }

        $LabelName = is_numeric($LabelName) ? "_$LabelName" : $LabelName;

        // get meta label id (may not exist yet)
        $metaLabelId = db_query(
            "SELECT MetaLabelID FROM {meta_labels} WHERE RelOwnerUserID = :UserID and Name = :LabelName",
            [
                ':UserID' => $UserID,
                ':LabelName' => $LabelName
            ]
        )->fetchField();

        if (empty($metaLabelId)) {
            //meta label does not exist -> create

            $Data = array(
                'RelOwnerUserID' => $UserID,
                'Name' => $LabelName,
            );
            $metaLabelId = kt_insert_row($Data, '{meta_labels}');

            if (empty($metaLabelId)) {
                return false;
            }
        }

        $query = "INSERT IGNORE INTO {meta_labeled} (RelOwnerUserID, RelMetaLabelID, BaseType, EntityType, RelEntityID) VALUES (:RelOwnerUserID, :RelMetaLabelID, :BaseType, :EntityType, :RelEntityID)";
        db_query($query, [
            ':RelOwnerUserID' => $UserID,
            ':RelMetaLabelID' => $metaLabelId,
            ':BaseType' => $BaseType,
            ':EntityType' => $EntityType,
            ':RelEntityID' => $EntityID,
        ]);

        static::ResetLabelsOfEntityCache($UserID, $BaseType, $EntityID);

        return true;
    }

    /**
     * Add labels by name to an entity
     * @param $UserID
     * @param $MetaLabels
     * @param $BaseType
     * @param $EntityType
     * @param $EntityID
     *
     * @return bool
     */
    public static function AddLabelList($UserID, $MetaLabels, $BaseType, $EntityType, $EntityID)
    {
        if (empty($MetaLabels)) {
            return false;
        }

        //Note: validation is done in AddLabel()
        foreach ($MetaLabels as $Name) {
            static::AddLabel($UserID, $Name, $BaseType, $EntityType, $EntityID);
        }

        return true;
    }

    /**
     * Remove labels by name from entity
     * @param $UserID
     * @param $BaseType
     * @param $EntityID
     * @param $Labels array of strings
     */
    protected static function RemoveLabelsFromEntity($UserID, $BaseType, $EntityID, $Labels)
    {
        if (empty($UserID) || empty($EntityID) || empty($Labels)) {
            return;
        }

        if (empty(DatabaseNamedTable::$EntityBaseTypeMap[$BaseType])) {
            return;
        }

        db_query(
            'DELETE mlr FROM {meta_labeled} mlr '
            . 'INNER JOIN {meta_labels} ml ON ml.RelOwnerUserID = mlr.RelOwnerUserID AND ml.MetaLabelID = mlr.RelMetaLabelID '
            . 'WHERE mlr.RelOwnerUserID = :UserID '
            . ' AND mlr.BaseType = :BaseType '
            . ' AND mlr.RelEntityID = :EntityID '
            . ' AND ml.Name IN (:LabelNames) ',
            [
                ':LabelNames' => $Labels,
                ':UserID' => $UserID,
                ':BaseType' => $BaseType,
                ':EntityID' => $EntityID,
            ]
        );

        static::ResetLabelsOfEntityCache($UserID, $BaseType, $EntityID);

        //clean up labels
        static::DeleteUnusedLabels($UserID);
    }

    /**
     * Remove all labels from an entity
     * @param $UserID
     * @param $BaseType
     * @param $EntityID
     *
     * @return bool
     */
    public static function RemoveAllLabelsFromEntity($UserID, $BaseType, $EntityID)
    {
        if (empty($UserID) || empty($EntityID)) {
            return false;
        }

        if (empty(DatabaseNamedTable::$EntityBaseTypeMap[$BaseType])) {
            return false;
        }

        db_query(
            'DELETE FROM {meta_labeled} WHERE RelOwnerUserID = :RelOwnerUserID AND RelEntityID = :RelEntityID AND BaseType = :BaseType',
            [
                ':RelOwnerUserID' => $UserID,
                ':RelEntityID' => $EntityID,
                ':BaseType' => $BaseType
            ]
        );

        static::ResetLabelsOfEntityCache($UserID, $BaseType, $EntityID);

        //clean up labels
        static::DeleteUnusedLabels($UserID);

        return true;
    }

    /**
     * Clean up labels, remove all labels that are not assigned
     * @param $UserID
     *
     * @return bool
     */
    public static function DeleteUnusedLabels($UserID)
    {
        // find labels of the given user which are unused
        $sqlQuery = "DELETE FROM {meta_labels} WHERE RelOwnerUserID = :UserID " .
            "AND ((SELECT COUNT(*) FROM {meta_labeled} labeled WHERE labeled.RelMetaLabelID = MetaLabelID AND labeled.RelOwnerUserID = :UserID) = 0)";
        db_query($sqlQuery, [':UserID' => $UserID]);

        return true;
    }

    /**
     * Clean up labels /labelings when user is deleted
     * @param $UserID
     *
     * @return bool
     */
    public static function DeleteOfUser($UserID)
    {
        db_query(
            "DELETE FROM {meta_labeled} WHERE RelOwnerUserID = :RelOwnerUserID",
            [':RelOwnerUserID' => $UserID]
        );

        db_query(
            "DELETE FROM {meta_labels} WHERE RelOwnerUserID = :RelOwnerUserID",
            [':RelOwnerUserID' => $UserID]
        );

        return true;
    }

    public static function klicktippapi_resource_definition()
    {
        $resource = parent::klicktippapi_resource_definition();

        // POST /api/<path>/add + body(data)
        $resource[static::$APIPath]['actions']['add'] = [
            'help' => 'Add labels to entities',
            'callback' => [get_called_class(), 'klicktippapi_add_label'],
            'access callback' => 'services_access_menu',
            'args' => [
                [
                    'name' => 'data',
                    'type' => 'array',
                    'description' => 'The meta_labeled data',
                    'source' => 'data',
                    'optional' => false,
                ],
            ],
        ];

        // POST /api/<path>/remove + body(data)
        $resource[static::$APIPath]['actions']['remove'] = [
            'help' => 'Remove labels from entities',
            'callback' => [get_called_class(), 'klicktippapi_remove_label'],
            'access callback' => 'services_access_menu',
            'args' => [
                [
                    'name' => 'data',
                    'type' => 'array',
                    'description' => 'The meta_labeled data',
                    'source' => 'data',
                    'optional' => false,
                ],
            ],
        ];

        // POST /api/<path>/search + body(data)
        $resource[static::$APIPath]['actions']['search'] = [
            'help' => 'Entity search by name, label, type',
            'callback' => [get_called_class(), 'klicktippapi_search'],
            'access callback' => 'services_access_menu',
            'args' => [
                [
                    'name' => 'data',
                    'type' => 'array',
                    'description' => 'The meta_labeled data',
                    'source' => 'data',
                    'optional' => false,
                ],
            ],
        ];

        // POST /api/<path>/types
        $resource[static::$APIPath]['actions']['metatypes'] = [
            'help' => 'Return grouped entity types for search filter',
            'callback' => [
                get_called_class(),
                'klicktippapi_metatypes',
            ],
            'access callback' => 'services_access_menu',
            'args' => [],
        ];

        return $resource;
    }

    /**
     * POST /api/<path>/index_advanced + body(data)
     *
     * @param $filter
     *
     * @return array|bool|mixed
     * @throws \ServicesException
     */
    public static function klicktippapi_index_advanced($filter)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        $UserID = $account->uid;

        $result = db_query("SELECT * FROM {meta_labels} WHERE RelOwnerUserID = :RelOwnerUserID", [
            ':RelOwnerUserID' => $UserID,
        ]);

        $labels = [];
        while ($DBArray = kt_fetch_array($result)) {
            $label = [
                'id' => $DBArray['MetaLabelID'],
                'type' => MetaLabels::TYPE,
                'name' => $DBArray['Name'],
                'description' => $DBArray['Description'],
            ];
            $labels[$DBArray['MetaLabelID']] = $label;
        }

        return [
            'data' => $labels,
        ];
    }

    /**
     * @param stdClass $account
     * @return array[]
     * @throws \Doctrine\DBAL\Exception
     */
    public static function angularApiIndexAdvanced(stdClass $account): array
    {
        $UserID = $account->uid;

        $result = db_query("SELECT * FROM {meta_labels} WHERE RelOwnerUserID = :RelOwnerUserID", [
            ':RelOwnerUserID' => $UserID,
        ]);

        $labels = [];
        while ($DBArray = kt_fetch_array($result)) {
            $labels[] = [
                'id' => $DBArray['Name'],
                'type' => MetaLabels::TYPE,
                'name' => $DBArray['Name'],
                'description' => $DBArray['Description'],
            ];
        }

        return [
            'data' => $labels,
        ];
    }

    /**
     * Add labels to multiple entities without having to retrieve and update the objects
     * @param $data
     *
     * @return bool|mixed
     */
    public static function klicktippapi_add_label($data)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        $UserID = $account->uid;

        //array of arrays(id: int, baseType: int, entityType: int)
        $entities = $data['entities'];

        //array of label names
        $labels = $data['labels'];

        if (empty($entities) || empty($labels)) {
            //nothing to do, but no need to throw an error
            return true;
        }

        foreach ($entities as $entity) {
            //AddLabelList() will validate the input
            static::AddLabelList($UserID, $labels, $entity['baseType'], $entity['entityType'], $entity['id']);
        }

        return true;
    }

    /**
     * Remove labels from multiple entities without having to retrieve and update the objects
     * @param $data
     *
     * @return bool|mixed
     */
    public static function klicktippapi_remove_label($data)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        $UserID = $account->uid;

        //array of arrays(id: int, baseType: int, entityType: int)
        $entities = $data['entities'];

        //array of label names
        $labels = $data['labels'];

        if (empty($entities) || empty($labels)) {
            //nothing to do, but no need to throw an error
            return true;
        }
        foreach ($entities as $entity) {
            static::RemoveLabelsFromEntity($UserID, $entity['baseType'], $entity['id'], $labels);
        }

        return true;
    }

    /**
     * @param $data
     *
     * @return array|bool|mixed
     */
    public static function klicktippapi_search($data)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        $UserID = $account->uid;

        //validate search mask

        $entityTypes = $data['types'];
        if (!is_array($entityTypes)) {
            $entityTypes = [];
        }

        $label = (empty($data['label']) || !is_numeric($data['label'])) ? 0 : $data['label'];

        $entityName = $data['name'];

        //validate pager
        $allowedPageSizes = explode(',', static::$PageSizes);

        $PagerPage = $data['pager']['page'];
        if (empty($PagerPage) || !is_numeric($PagerPage) || $PagerPage <= 1) {
            $PagerPage = 1;
        } else {
            $PagerPage = intval($PagerPage);
        }
        $PagerSize = $data['pager']['size'];
        if (
            empty($PagerSize) || !is_numeric($PagerSize)
            || !in_array($PagerSize, $allowedPageSizes)
        ) {
            $PagerSize = intval($allowedPageSizes[0]);
        } else {
            $PagerSize = intval($PagerSize);
        }

        //execute the search
        list($TotalCount, $SearchResult) = MetaLabels::Search(
            $UserID,
            $entityName,
            $entityTypes,
            $label,
            $PagerPage,
            $PagerSize
        );

        $mask = $data;
        $mask['pager']['total'] = $TotalCount;
        $mask['pager']['page'] = $PagerPage;
        $mask['pager']['pages'] = ceil($TotalCount / $PagerSize);
        $mask['pager']['size'] = $PagerSize;
        $mask['pager']['sizes'] = $allowedPageSizes;

        //return search result and the search mask with updated pager information
        return ['data' => $SearchResult, 'mask' => $mask];
    }

    /**
     * Creates a structured/grouped list of all entity types to use in the search filter
     * Note: This is an ordered one dimensional array to be used in the cockpits entity selector
     *        the layout is done there
     * @return array|bool|mixed
     */
    public static function klicktippapi_metatypes()
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        $GroupedEntities = array(
            //ContactCloud
            array(
                'id' => 'contactcloud',
                'name' => t('Contact Cloud'),
            ),
            array(
                'id' => 'custom-fields',
                'name' => t('Custom fields'),
                'parent' => 'contactcloud',
                'children' => array(
                    array(CustomFields::TYPE_SINGLE, CustomFields::class),
                    array(CustomFields::TYPE_PARAGRAPH, CustomFields::class),
                    array(CustomFields::TYPE_EMAIL, CustomFields::class),
                    array(CustomFields::TYPE_NUMBER, CustomFields::class),
                    array(CustomFields::TYPE_URL, CustomFields::class),
                    array(CustomFields::TYPE_DATE, CustomFields::class),
                    array(CustomFields::TYPE_TIME, CustomFields::class),
                    array(CustomFields::TYPE_DATETIME, CustomFields::class),
                    array(CustomFields::TYPE_HTML, CustomFields::class),
                    array(CustomFields::TYPE_DECIMAL, CustomFields::class),
                ),
            ),
            array(
                'id' => 'facebook',
                'name' => t('Facebook'),
                'parent' => 'contactcloud',
                'children' => array(
                    array(MarketingTools::TOOL_TYPE_FACEBOOK_AUDIENCE, MarketingTools::class),
                ),
            ),
            //Listbuilding
            array(
                'id' => 'listbuilding',
                'name' => t('Listbuilding'),
            ),
            // Listbuilding -> Klick-Tipp
            array(
                'id' => 'klick-tipp',
                'name' => t('Klick-Tipp'),
                'parent' => 'listbuilding',
                'children' => array(
                    array(Listbuildings::TYPE_FORM_CUSTOM, Listbuildings::class),
                    array(Listbuildings::TYPE_FORM_INLINE, Listbuildings::class),
                    array(Listbuildings::TYPE_FORM_WIDGET, Listbuildings::class),
                    array(Listbuildings::TYPE_FORM_RAW, Listbuildings::class),
                    array(Listbuildings::TYPE_REQUESTS, Listbuildings::class),
                    array(Listbuildings::TYPE_API, Listbuildings::class),
                    array(
                        Listbuildings::TYPE_SMS_LISTBUILDING_GLOBAL,
                        Listbuildings::class,
                    ),
                    array(Listbuildings::TYPE_LANDING_PAGE, Listbuildings::class),
                ),
            ),
            // Listbuilding -> Payment Provider
            array(
                'id' => 'payments',
                'name' => t('Payment provider'),
                'parent' => 'listbuilding',
                'children' => array(
                    array(Listbuildings::TYPE_DIGISTORE, Listbuildings::class),
                    array(Listbuildings::TYPE_AFFILICON, Listbuildings::class),
                    array(Listbuildings::TYPE_CLICKBANK, Listbuildings::class),
                    array(Listbuildings::TYPE_PAYPAL, Listbuildings::class),
                    array(Listbuildings::TYPE_CLEVERBRIDGE, Listbuildings::class),
                ),
            ),
            // Listbuilding -> Integrations
            array(
                'id' => 'integrations',
                'name' => t('Integrations'),
                'parent' => 'listbuilding',
                'children' => array(
                    array(Listbuildings::TYPE_WUFOO, Listbuildings::class),
                    array(
                        Listbuildings::TYPE_SMS_LISTBUILDING_NEXMO,
                        Listbuildings::class,
                    ),
                    array(
                        Listbuildings::TYPE_SMS_LISTBUILDING_TWILIO,
                        Listbuildings::class,
                    ),
                    array(Listbuildings::TYPE_FORM_LEADPAGES, Listbuildings::class),
                    array(Listbuildings::TYPE_FORM_OPTIMIZEPRESS, Listbuildings::class),
                    array(Listbuildings::TYPE_FORM_WISTIA, Listbuildings::class),
                    array(Listbuildings::TYPE_FORM_THRIVE, Listbuildings::class),
                    array(Listbuildings::TYPE_BUSINESSCARD, Listbuildings::class),
                ),
            ),
            //Marketing Tools -> Splittest-Club
            array(
                'id' => 'splittestclub',
                'name' => t('Splittest-Club Conversion Tools'),
                'children' => array(
                    array(
                        MarketingTools::TOOL_TYPE_WEBSITE_SPLITTEST,
                        MarketingTools::class,
                    ),
                    array(
                        MarketingTools::TOOL_TYPE_WEBSITE_EXITLIGHTBOX,
                        MarketingTools::class,
                    ),
                    array(
                        MarketingTools::TOOL_TYPE_WEBSITE_FEEDBACK,
                        MarketingTools::class,
                    ),
                    array(
                        MarketingTools::TOOL_TYPE_WEBSITE_ONETIMEOFFER,
                        MarketingTools::class,
                    ),
                    array(
                        MarketingTools::TOOL_TYPE_WEBSITE_SOCIALPROOF,
                        MarketingTools::class,
                    ),
                ),
            ),
            //Automatisierung
            array(
                'id' => 'automation',
                'name' => t('Automatization'),
                'children' => array(
                    array(Tag::CATEGORY_MANUAL, Tag::class),
                    array(Tag::CATEGORY_SMARTLINK, Tag::class),
                    array(MarketingTools::TOOL_TYPE_TAGGINGPIXEL, MarketingTools::class),
                    array(MarketingTools::TOOL_TYPE_STATISTICS, MarketingTools::class),
                    array(MarketingTools::TOOL_TYPE_OUTBOUND, MarketingTools::class),
                    array(MarketingTools::TOOL_TYPE_KAJABI, MarketingTools::class),
                    array(MarketingTools::TOOL_TYPE_ZAPIER, MarketingTools::class),
                    array(0, Lists::class),
                    array(0, Signatures::class),
                    array(MarketingTools::TOOL_TYPE_CALENDAR, MarketingTools::class),
                    array(MarketingTools::TOOL_TYPE_WEBINAR, MarketingTools::class),
                ),
            ),
            //Campaigns
            array(
                'id' => 'automations',
                'name' => t('Automations'),
                'children' => array(
                    array(
                        CampaignsProcessFlow::TRIGGER_TYPE_PROCESSFLOW,
                        CampaignsProcessFlow::class,
                    ),
                    array(MarketingTools::TOOL_TYPE_TEMPLATE, MarketingTools::class),
                ),
            ),
            array(
                'id' => 'emails-sms',
                'name' => t('Emails / SMS'),
                'children' => array(
                    array(Emails::TYPE_AUTOMATIONEMAIL, EmailsAutomationEmail::class),
                    array(Emails::TYPE_AUTOMATIONSMS, EmailsAutomationSMS::class),
                    array(Emails::TYPE_NOTIFICATIONEMAIL, EmailsNotificationEmail::class),
                    array(Emails::TYPE_NOTIFICATIONSMS, EmailsNotificationSMS::class),
                ),
            ),
            array(
                'id' => 'newsletter',
                'name' => t('Campaigns'),
                'children' => array(
                    array(Campaigns::TRIGGER_TYPE_CAMPAIGN, CampaignsNewsletterEmail::class),
                    array(Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, CampaignsNewsletterSms::class),
                ),
            ),
            array(
                'id' => 'autoresponder',
                'name' => t('Autoresponders'),
                'children' => array(
                    array(
                        Campaigns::TRIGGER_TYPE_SUBSCRIPTION,
                        CampaignsAutoresponderEmail::class,
                    ),
                    array(
                        Campaigns::TRIGGER_TYPE_DATETIME,
                        CampaignsAutoresponderEmailDatetime::class,
                    ),
                    array(
                        Campaigns::TRIGGER_TYPE_BIRTHDAY,
                        CampaignsAutoresponderEmailBirthday::class,
                    ),
                    array(
                        Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION,
                        CampaignsAutoresponderSms::class,
                    ),
                    array(
                        Campaigns::TRIGGER_TYPE_SMS_DATETIME,
                        CampaignsAutoresponderSmsDatetime::class,
                    ),
                    array(
                        Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY,
                        CampaignsAutoresponderSmsBirthday::class,
                    ),
                ),
            ),
            array(
                'id' => 'campaign-tools',
                'name' => t('Campaign Tools'),
                'children' => array(
                    array(MarketingTools::TOOL_TYPE_COUNTDOWN, MarketingTools::class)
                )
            ),
        );

        $EntityAccess = array(
            ToolWebinar::class => user_access('access tool webinar', $account)
        );

        $Options = array();
        foreach ($GroupedEntities as $item) {
            $id = $item['id'];
            $pid = $item['parent'];

            if (!empty($pid) && empty($Options[$pid])) {
                //misconfiguration, parents must be declared already
                continue;
            }

            $depth = (empty($pid)) ? 0 : $Options[$pid]['depth'] + 1;

            $Options[$id] = array(
                'id' => $id,
                'name' => $item['name'],
                'type' => 'metatype',
                'depth' => $depth,
                'group' => array(),
            );

            if (!empty($item['children'])) {
                foreach ($item['children'] as $Entity) {
                    $EntityType = $Entity[0];
                    $EntityClass = $Entity[1];
                    $BaseType = $EntityClass::$EntityBaseType;
                    $FilterTypes = $EntityClass::$APIIndexFilterTypes;
                    $ApiType = $FilterTypes[$EntityType];
                    $cid = "$BaseType-$EntityType";

                    if (isset($EntityAccess[$EntityClass]) && !$EntityAccess[$EntityClass]) {
                        //user has no access to this entity, do not display in the filter
                        continue;
                    }

                    $Options[$id]['group'][] = $cid;

                    $Options[$cid] = array(
                        'id' => $cid,
                        'depth' => $depth + 1,
                        'type' => 'metatype',
                        'baseType' => $BaseType,
                        'entityType' => $EntityType,
                        'serviceType' => $ApiType,
                    );
                }
            }

            while (!empty($pid)) {
                $Options[$pid]['group'] = array_merge($Options[$pid]['group'], $Options[$id]['group']);

                $pid = $Options[$pid]['parent'];
            }
        }

        return [
            'data' => $Options,
        ];
    }

    /**
     * Build the search query based on the specified filter, execute it and count total result
     * @param $UserID
     * @param string $Name : %LIKE% entity name
     * @param array $Types : array of arrays(baseType: int, entityType: int)
     * @param int $LabelID : ID of Label, 0 == any label, -1 == no label at all
     * @param int $PagerPage
     * @param int $PagerSize
     *
     * @return array
     */
    public static function Search($UserID, $Name = '', $Types = array(), $LabelID = 0, $PagerPage = 1, $PagerSize = 10)
    {
        //selector for query '*' == get result, 'COUNT(*)' == result count
        $Selector = '*';

        $Conditions = array(
            'n.RelOwnerUserID = :UserID'
        );

        $LabelJoin = '';

        $Parameters = array(
            ':UserID' => $UserID
        );

        //add conditions
        if (!empty($Name)) {
            $Conditions[] = 'n.Name LIKE :Name';
            $Parameters[':Name'] = "%$Name%";
        }

        if (!empty($Types)) {
            $t = 1;
            $TypeConditions = array();
            foreach ($Types as $Type) {
                $TypeConditions[] = "(n.BaseType = :Base$t AND n.EntityType = :Type$t)";
                $Parameters[":Base$t"] = $Type['baseType'];
                $Parameters[":Type$t"] = $Type['entityType'];
                $t++;
            }
            $Conditions[] = '(' . implode(' OR ', $TypeConditions) . ')';
        }

        if (!empty($LabelID)) {
            if ($LabelID == -1) {
                $Selector = 'n.*, l.RelMetaLabelID';
                $LabelJoin = "LEFT JOIN {meta_labeled} l ON l.RelOwnerUserID = n.RelOwnerUserID AND l.BaseType = n.BaseType AND l.RelEntityID = n.RelEntityID";
                $Conditions[] = 'l.RelMetaLabelID IS NULL';
            } else {
                $Selector = 'n.*';
                $LabelJoin = "INNER JOIN {meta_labeled} l ON l.RelOwnerUserID = n.RelOwnerUserID AND l.BaseType = n.BaseType " .
                    "AND l.RelEntityID = n.RelEntityID AND l.RelMetaLabelID = :LabelID";
                $Parameters[":LabelID"] = $LabelID;
            }
        }

        $Conditions = implode(' AND ', $Conditions);

        //Pager
        $Limit = 'LIMIT ' . (($PagerPage - 1) * $PagerSize) . ', ' . $PagerSize;

        //build search query
        $query = "SELECT $Selector FROM {meta_named} n $LabelJoin WHERE $Conditions ORDER BY Name ASC $Limit";

        //execute query
        $result = db_query($query, $Parameters);

        //build search result
        $Entities = array();
        while ($Item = kt_fetch_array($result)) {
            $EntityClass = DatabaseNamedTable::$EntityBaseTypeMap[$Item['BaseType']];
            if (empty($EntityClass)) {
                continue;
            }

            /** @var DatabaseTableWithData $ObjectEntity */
            $ObjectEntity = $EntityClass::FromID($UserID, $Item['RelEntityID']);
            if (!empty($ObjectEntity)) {
                $ArrayEntity = $ObjectEntity->GetData();
                $Labels = $ObjectEntity->GetMetaLabels(false);
                $ApiTypes = $EntityClass::$APIIndexFilterTypes;
                $Entities[] = array(
                    'id' => $ArrayEntity[$EntityClass::$DBSerialField],
                    'name' => $Item['Name'],
                    'type' => $ApiTypes[$Item['EntityType']],
                    'labels' => $Labels,
                    'baseType' => $Item['BaseType'],
                    'entityType' => $Item['EntityType']
                );
            }
        }

        //build count query
        $query = "SELECT COUNT(*) FROM {meta_named} n $LabelJoin WHERE $Conditions";
        $count = db_query($query, $Parameters)->fetchField();

        return array($count, $Entities);
    }

    public static function GetEntitiesAsOptionsArray($UserID)
    {
        if (empty($UserID)) {
            return array();
        }

        $OptionsArray = array();

        // get db sets
        $result = db_query("SELECT * FROM {meta_labels} WHERE RelOwnerUserID = :RelOwnerUserID", array(
            ':RelOwnerUserID' => $UserID,
        ));
        while ($DBArray = kt_fetch_array($result)) {
            // put in as <entity name> => <entity name>
            $Name = check_plain($DBArray['Name']);
            $OptionsArray[$Name] = $Name;
        }

        natcasesort($OptionsArray);

        return $OptionsArray;
    }

    /**
     * @param int $userID
     * @return MetaLabels[]
     * @throws DoctrineDBALException
     */
    public static function getMetaLabels(int $userID): array
    {
        $result = db_query("SELECT * FROM {meta_labels} WHERE RelOwnerUserID = :RelOwnerUserID", [
            ':RelOwnerUserID' => $userID,
        ]);
        $labels = [];
        while ($DBArray = kt_fetch_array($result)) {
            $labels[$DBArray['MetaLabelID']] = static::fromArray($DBArray);
        }
        return $labels;
    }

    /**
     * @param int $userId
     * @return self
     */
    public static function createNewInstance(int $userId): self
    {
        $dbArray = array_merge(self::$DefaultFields, [
            'MetaLabelID' => 0,
            'RelOwnerUserID' => $userId,
            'Name' => '',
            'Description' => ''
        ]);
        return new self($dbArray);
    }


    public function save(): MetaLabels
    {
        unset($this->DataFlat['Type']);
        // @phpstan-ignore return.type
        return parent::save();
    }

    /**
     * @param int $UserID
     * @param string $Name
     * @return int
     * @throws DoctrineDBALException
     */
    public static function CheckDuplicateName($UserID, $Name): int
    {
        $query = <<<SQL
            SELECT MetaLabelID
              FROM {meta_labels}
             WHERE RelOwnerUserID = :RelOwnerUserID
               AND Name = :Name
             LIMIT 0,1
        SQL;

        $params = array(
            ':RelOwnerUserID' => $UserID,
            ':Name' => $Name,
        );

        return (int)db_query($query, $params)->fetchField() ?: 0;
    }
}

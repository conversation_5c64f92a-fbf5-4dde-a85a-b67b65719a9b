<?php

namespace App\Klicktipp\Personalization\ValueObject\Editor\Response;

final class PersonalizationResponsePlaceholdersLinksValueObject
{
    public string $id;
    public string $label;
    public string $href;
    public string $title;
    /** @var array<string, array<string>> */
    public array $group;
    public string $plain;
    public PersonalizationResponsePlaceholdersCkeditorValueObject $ckeditor;

    /**
     * @param string $id
     * @param string $label
     * @param string $href
     * @param string $title
     * @param array<string, array<string>> $group
     * @param string $plain
     * @param PersonalizationResponsePlaceholdersCkeditorValueObject $ckeditor
     * @return self
     */
    public static function create(
        string $id,
        string $label,
        string $href,
        string $title,
        array $group,
        string $plain,
        PersonalizationResponsePlaceholdersCkeditorValueObject $ckeditor
    ): self {
        $valueObject = new self();

        $valueObject->id = $id;
        $valueObject->label = $label;
        $valueObject->href = $href;
        $valueObject->title = $title;
        $valueObject->group = $group;
        $valueObject->plain = $plain;
        $valueObject->ckeditor = $ckeditor;

        return $valueObject;
    }

    /**
     * @param array{
     *     'id'?: string|int,
     *     'label': string,
     *     'href': string,
     *     'title': string,
     *     'group': array<string, array<string>>,
     *     'plain': string,
     *     'ckeditor': array{
     *         'html': string,
     *          'on': string,
     *          'off': string
     *     }
     * } $data
     * @return self
     */
    public static function createFromPersonalization(array $data): self
    {
        $valueObject = new self();

        $valueObject->id = (string)($data['id'] ?? '');
        $valueObject->label = $data['label'] ?: '';
        $valueObject->href = $data['href'] ?: '';
        $valueObject->title = $data['title'] ?: '';
        $valueObject->group = $data['group'] ?: [];
        $valueObject->plain = $data['plain'] ;
        $valueObject->ckeditor = PersonalizationResponsePlaceholdersCkeditorValueObject::create(
            $data['ckeditor']['html'],
            $data['ckeditor']['on'],
            $data['ckeditor']['off']
        );

        return $valueObject;
    }
}

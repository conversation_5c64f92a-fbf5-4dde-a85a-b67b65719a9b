<?php

namespace App\Klicktipp;

use ArgumentCountError;
use Exception;
use InvalidArgumentException;

use function PHPUnit\Framework\isNull;

class Plugin
{
    public const PLUGIN_VARIABLE_TYPE_STATIC = "Static";
    public const PLUGIN_VARIABLE_TYPE_KEY = "Key";
    public const PLUGIN_VARIABLE_TYPE_INBOUND = "Inbound";
    public const PLUGIN_VARIABLE_TYPE_CALC = "Calc";
    public const PLUGIN_VARIABLE_TYPE_VALIDATE = "Validate"; # validate inbound data
    public const PLUGIN_VARIABLE_TYPE_READ = "Read";
    public const PLUGIN_VARIABLE_TYPE_READ_IF_EXISTS = "ReadIfExists";
    public const PLUGIN_VARIABLE_TYPE_WRITE = "Write";
    public const PLUGIN_VARIABLE_TYPE_DELETE = "Delete";
    public const PLU<PERSON><PERSON>_VARIABLE_TYPE_POP = 'Pop';
    public const PLUGIN_VARIABLE_TYPE_REPEAT = 'Repeat';
    // from other entities
    public const PLUGIN_VARIABLE_TYPE_SMARTTAG = "SmartTag";
    public const PLUGIN_VARIABLE_TYPE_UNTAG_SMARTTAG = "UntagSmartTag";
    public const PLUGIN_VARIABLE_TYPE_CUSTOMFIELD = "CustomField";
    public const PLUGIN_VARIABLE_TYPE_TAG = "Tag";
    public const PLUGIN_VARIABLE_TYPE_LIST = "List"; // not used

    // dialog fields ( PLUGIN_VARIABLE_TYPE_X correspond to CustomFields::TYPE_X)
    public const PLUGIN_WIDGET_TYPE_SINGLE = 'text';
    public const PLUGIN_WIDGET_TYPE_PARAGRAPH = 'textarea';
    public const PLUGIN_WIDGET_TYPE_NUMBER = 'integer';
    public const PLUGIN_WIDGET_TYPE_URL = 'url';
    public const PLUGIN_WIDGET_TYPE_DATE = 'date';
    public const PLUGIN_WIDGET_TYPE_TIME = 'time';
    public const PLUGIN_WIDGET_TYPE_DATETIME = 'datetime';
    public const PLUGIN_WIDGET_TYPE_HTML = 'html';
    public const PLUGIN_WIDGET_TYPE_DECIMAL = 'decimal';
    public const PLUGIN_WIDGET_TYPE_DROPDOWN = 'dropdown';
    public const PLUGIN_WIDGET_TYPE_CHECKBOX = 'checkbox';
    // custom fields for widgets ( usually PLUGIN_WIDGET_TYPE_X correspond to CustomFields::TYPE_X)
    /** @var array<string, int> $CustomFieldTypeMapping */
    public static array $CustomFieldTypeMapping = [
        Plugin::PLUGIN_WIDGET_TYPE_SINGLE => CustomFields::TYPE_SINGLE,
        Plugin::PLUGIN_WIDGET_TYPE_PARAGRAPH => CustomFields::TYPE_PARAGRAPH,
        Plugin::PLUGIN_WIDGET_TYPE_NUMBER => CustomFields::TYPE_NUMBER,
        Plugin::PLUGIN_WIDGET_TYPE_URL => CustomFields::TYPE_URL,
        Plugin::PLUGIN_WIDGET_TYPE_DATE => CustomFields::TYPE_DATE,
        Plugin::PLUGIN_WIDGET_TYPE_TIME => CustomFields::TYPE_TIME,
        Plugin::PLUGIN_WIDGET_TYPE_DATETIME => CustomFields::TYPE_DATETIME,
        Plugin::PLUGIN_WIDGET_TYPE_HTML => CustomFields::TYPE_HTML,
        Plugin::PLUGIN_WIDGET_TYPE_DECIMAL => CustomFields::TYPE_DECIMAL,
        // special widgets
        Plugin::PLUGIN_WIDGET_TYPE_CHECKBOX => CustomFields::TYPE_SINGLE,
        Plugin::PLUGIN_WIDGET_TYPE_DROPDOWN => CustomFields::TYPE_SINGLE,
    ];

    const PLUGIN_CONNECT_METHOD_LOGIN_CREATE_REDIRECT = "LoginCreateRedirect";
    const PLUGIN_CONNECT_METHOD_LOGIN_CREATE = "LC";
    const PLUGIN_CONNECT_METHOD_LOGIN_CREATE_EDIT = "LCE";
    const PLUGIN_CONNECT_METHOD_LOGIN_REBOUND_REDIRECT = "LoginReboundRedirect";
    const PLUGIN_CONNECT_METHOD_INBOUND = "ConnectInbound";

    public const PLUGIN_CREATE_METHOD_DIALOG = "";
    public const PLUGIN_CREATE_METHOD_INCLUDE = "Include";
    public const PLUGIN_CREATE_METHOD_EXTERNAL = "External";
    public const PLUGIN_CREATE_METHOD_ADDON = "AddOn";

    // hooks
    public const PLUGIN_HOOK_TYPE_CONNECT = "hook_connect";
    public const PLUGIN_HOOK_TYPE_REBOUND = "hook_rebound";
    public const PLUGIN_HOOK_TYPE_AFTER_REBOUND = "hook_after_rebound";
    public const PLUGIN_HOOK_TYPE_INBOUND = "hook_inbound";
    public const PLUGIN_HOOK_TYPE_BEFORE_OUTBOUND = "hook_before_outbound";
    public const PLUGIN_HOOK_TYPE_OUTBOUND = "hook_outbound";
    public const PLUGIN_HOOK_TYPE_REDIRECT = "hook_redirect";
    public const PLUGIN_HOOK_TYPE_SUBSCRIPTION_TRIGGER = "hook_subscription_trigger";
    public const PLUGIN_HOOK_TYPE_TAG_TRIGGER = "hook_tag_trigger";
    public const PLUGIN_HOOK_TYPE_DISCONNECT = "hook_disconnect";

    // conditions
    const CONDITION_PLUGIN_ACTION_READY = 'ready';
    const CONDITION_PLUGIN_ACTION_STARTED = 'started';
    const CONDITION_PLUGIN_ACTION_IN_PROGRESS = 'inprogress';
    const CONDITION_PLUGIN_ACTION_FINISHED = 'finished';

    // same as flowchart.constants.ts
    const CONDITION_PLUGIN_READY = 'plugin is ready';
    const CONDITION_PLUGIN_NOT_READY = 'plugin is not ready';
    const CONDITION_PLUGIN_STARTED = 'plugin is started';
    const CONDITION_PLUGIN_NOT_STARTED = 'plugin is not started';
    const CONDITION_PLUGIN_IN_PROGRESS = 'plugin is in progress';
    const CONDITION_PLUGIN_NOT_IN_PROGRESS = 'plugin is not in progress';
    const CONDITION_PLUGIN_FINISHED = 'plugin is finished';
    const CONDITION_PLUGIN_NOT_FINISHED = 'plugin is not finished';

    const CONDITION_PLUGIN_OP_HAS = 'has';
    const CONDITION_PLUGIN_OP_HAS_NOT = 'has not';
    const CONDITION_PLUGIN_OP_ANY = 'any';
    const CONDITION_PLUGIN_OP_NOT_ANY = 'not any';

    const ENTITY_SERVICE_TYPE_PLUGIN = 'plugin';

    const YAMLFILE_DEFAULT = 'plugin_includes/default.yml';

    // plugin configuration
    // Note: as drupal variables, so we have it cached

    public static function get_plugins()
    {
        /* an array of
          <stringid> => [
              'PluginID' => <stringid>,
              'PluginName' => <string>,
              'PluginNo' => <int>,
            ]
        */
        return variable_get('klicktipp_plugins', []);
    }

    public static function get_plugin($pluginid, $reset = false)
    {
        // make sure it is defined (and not deleted)
        $var_plugins = static::get_plugins();
        if ($reset || !isset($var_plugins[$pluginid])) {
            // get defined substitutions
            $plugin = yaml_parse(variable_get("klicktipp_plugins_{$pluginid}_substitutions", ""));
            // replace hard defaults
            $plugin['PluginID'] = $pluginid;
            if (empty($plugin['YamlFile'])) {
                $plugin['YamlFile'] = static::YAMLFILE_DEFAULT;
            }
            // read from json file
            $yaml = file_get_contents($plugin['YamlFile']);
            if ($yaml) {
                $plugin_from_file = yaml_parse($yaml);
                if ($plugin_from_file) {
                    // overwrite file data with substitutions
                    $plugin = array_replace_recursive($plugin_from_file, $plugin);
                }
            }
        } else {
            // get cached values
            $plugin = variable_get("klicktipp_plugins_$pluginid", []);
        }
        return $plugin;
    }

    public static function get_plugins_by_permission($account, $forcreate = false)
    {
        $var_plugins = static::get_plugins();
        $plugins = [];
        foreach ($var_plugins as $id => $p) {
            $plugin = static::get_plugin($id);
            // general plugin access for create, individual access for edit
            if (klicktipp_plugin_access($account, $plugin, $forcreate)) {
                $plugins[$id] = $plugin;
            }
        };
        return $plugins;
    }

    public static function get_plugins_for_conditions($account)
    {
        $var_plugins = static::get_plugins_by_permission($account);
        $plugins = [];
        foreach ($var_plugins as $id => $plugin) {
            [$ConditionTypes, $ConditionOps] = static::conditions_for_plugin($plugin);
            if (!empty($ConditionTypes)) {
                $plugins[$id] = [
                    'conditionTypes' => $ConditionTypes,
                    'conditionOps' => $ConditionOps,
                ];
            }
        };
        return $plugins;
    }

    /**
     * get plugin data for iPass: plugin , globals and environment
     * @param string $pluginid
     * @return array<string, mixed>
     */
    public static function getPluginForIPass(string $pluginid): array
    {
        $plugin = static ::get_plugin($pluginid);
        // things we always need
        $data = [
            'pluginId' => $pluginid,
            'plugin' => $plugin,
            'appUrl' => APP_URL,
            'appSalt' => KLICKTIPP_SALT,
            'appTime' => time(),
        ];
        // Globals
        if (isset($plugin['Globals'])) {
            foreach ($plugin['Globals'] as $key => $param) {
                switch ($param['type']) {
                    case 'value': // constant
                        $data[$key] = $param['value'];
                        break;
                    case 'env': // from environment
                        $data[$key] = getenv($param['field']);
                        break;
                    // Add other cases for e.g. variable_get.
                }
            }
        }

        return $data;
    }

    // generate CONDITION_TYPES, CONDITION_OPS for flowchart.conditions.ts
    public static function conditions_for_plugin($plugin)
    {
        if (empty($plugin['Conditions'])) {
            return [[], []];
        }

        // see CONDITION_TYPES, CONDITION_OPS in flowchart.conditions.ts
        $conditionTypes = [
            'label' => $plugin['Conditions']['label'],
            'createType' => static::ENTITY_SERVICE_TYPE_PLUGIN,
        ];
        $conditionOps = [];

        $opsActionsIs = [];
        $opsActionsIsNot = [];

        static::applyToSmartTags(
            $plugin,
            function ($key, $field) use ($plugin, &$conditionTypes, &$conditionOps, &$opsActionsIs, &$opsActionsIsNot) {
                if (empty($field['condition'])) {
                    return [];
                }
                $entitykey = static::unwrap_key('Tool:', $key);
                $condition = $field['condition'];

                // make ops unique in cockpit
                $opIs = $plugin['PluginID'] . CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR;
                $opIsNot = $plugin['PluginID'] . CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR;

                switch ($condition['action']) {
                    case static::CONDITION_PLUGIN_ACTION_READY:
                        $opIs .= static::CONDITION_PLUGIN_READY;
                        $opIsNot .= static::CONDITION_PLUGIN_NOT_READY;
                        break;
                    case static::CONDITION_PLUGIN_ACTION_STARTED:
                        $opIs .= static::CONDITION_PLUGIN_STARTED;
                        $opIsNot .= static::CONDITION_PLUGIN_NOT_STARTED;
                        break;
                    case static::CONDITION_PLUGIN_ACTION_IN_PROGRESS:
                        $opIs .= static::CONDITION_PLUGIN_IN_PROGRESS;
                        $opIsNot .= static::CONDITION_PLUGIN_NOT_IN_PROGRESS;
                        break;
                    case static::CONDITION_PLUGIN_ACTION_FINISHED:
                        $opIs .= static::CONDITION_PLUGIN_FINISHED;
                        $opIsNot .= static::CONDITION_PLUGIN_NOT_FINISHED;
                        break;
                    default:
                        break;
                }

                // conditions
                $conditionTypes['actions'][$condition['action']] = $condition['verb'];
                $opsActionsIs[] = $opIs;
                $opsActionsIsNot[] = $opIsNot;

                // see CONDITION_OPS in flowchart.conditions.ts
                $conditionOps[$opIs] = [
                    'type' => $plugin['PluginID'],
                    'source' => static::ENTITY_SERVICE_TYPE_PLUGIN,
                    'subtype' => "plugin-{$plugin['PluginID']}",
                    'field' => $entitykey,
                    'action' => $condition['action'],
                    'condition' => [
                        'withEntity' => 'has',
                        'withoutEntity' => 'any',
                    ],
                ];
                $conditionOps[$opIsNot] = [
                    'type' => $plugin['PluginID'],
                    'source' => static::ENTITY_SERVICE_TYPE_PLUGIN,
                    'subtype' => "plugin-{$plugin['PluginID']}",
                    'field' => $entitykey,
                    'action' => $condition['action'],
                    'condition' => [
                        'withEntity' => 'has not',
                        'withoutEntity' => 'not any',
                    ],
                ];

                return [];
            }
        );

        // see CONDITION_TYPES in flowchart.conditions.ts
        $labels = $plugin['Conditions']['operations'];
        $conditionTypes['operations'] = [];
        foreach (
            [
                static::CONDITION_PLUGIN_OP_HAS => $opsActionsIs,
                static::CONDITION_PLUGIN_OP_HAS_NOT => $opsActionsIsNot,
                static::CONDITION_PLUGIN_OP_ANY => $opsActionsIs,
                static::CONDITION_PLUGIN_OP_NOT_ANY => $opsActionsIsNot,
            ] as $operation => $actions
        ) {
            $conditionTypes['operations'][] = [
                'value' => $operation,
                'label' => $labels[$operation],
                'actions' => $actions,
            ];
        }

        return [$conditionTypes, $conditionOps];
    }

    // methods using pluginid

    /**
     * Insert/Update plugin configuration
     * Stores substitutions, combines it with contents of the json file and updates cache and index
     * @param $pluginid
     * @array $substitutions Dict with substitutions of entries in "YamlFile", something like
     * {
     * "PluginID": "default",
     * "PluginName": "Plugin Name",
     * "YamlFile": "plugin_includes\/default.yml",
     * "Class": "ToolPluginGeneral"
     * }
     */
    public static function update_plugin($pluginid, $substitutions)
    {
        // insert/update plugin substitutions
        $substitutions['PluginID'] = $pluginid;
        $yaml = yaml_emit($substitutions);
        variable_set("klicktipp_plugins_{$pluginid}_substitutions", $yaml);

        // cache plugin
        $plugin = static::get_plugin($pluginid, true);
        variable_set("klicktipp_plugins_$pluginid", $plugin);

        // insert/update plugin index
        $var_plugins = static::get_plugins();
        // keep pluginno or insert new
        $pluginno = empty($var_plugins[$pluginid]['PluginNo']) ? count(
            $var_plugins
        ) + 1 : $var_plugins[$pluginid]['PluginNo'];
        $var_plugins[$pluginid] = [
            'PluginID' => $pluginid,
            'PluginName' => $substitutions['PluginName'],
            'PluginNo' => $pluginno,
        ];
        variable_set("klicktipp_plugins", $var_plugins);
    }

    public static function delete_plugin($pluginid)
    {
        //TODO delete instances

        // remove plugin config
        variable_del("klicktipp_plugins_$pluginid");
        // remove it from index
        $var_plugins = static::get_plugins();
        unset($var_plugins[$pluginid]);
        variable_set("klicktipp_plugins", $var_plugins);
    }

    public static function get_pluginno($pluginid)
    {
        // used in VarPluginData to have a numeric id
        $var_plugins = static::get_plugins();
        return $var_plugins[$pluginid]['PluginNo'];
    }

    public static function get_plugin_var_byid($pluginid, $key)
    {
        $plugin = static::get_plugin($pluginid);
        return static::get_plugin_var($plugin, $key);
    }

    // methods using plugin

    public static function get_plugin_var($plugin, $key)
    {
        return $plugin[$key];
    }

    /** strtr with special treatment of unset parameters
     * @param string|null $string
     * @param array<string, string> $parameters
     * @param bool $removeUnset
     *
     * @deprecated use PluginParameters::replace($string, $removeUnset)
     */
    public static function replace_parameters(?string $string, array $parameters, bool $removeUnset = true)
    {
        if (is_null($string) || $string === '') {
            // it is convenient to accept null as an unset value, but treat it as an empty string
            // note: do not use empty() here as "0" should be treated as "0"
            return '';
        }

        if (isset($parameters[$string])) {
            return $parameters[$string];
        }

        // Substitute parameter patterns
        $replaced = strtr($string, ['%#' => '#escaped#']);
        $replaced = strtr($replaced, $parameters);
        if ($removeUnset) {
            /** @var string $replaced */
            $replaced = preg_replace('/%\w+:\w+%/', '', $replaced);
            $replaced ??= ''; // safeguard against preg_replace() returning null on error
        }
        return strtr($replaced, ['#escaped#' => '%']);
    }

    /**
     * create a set of parameters from entities (%<entity>:<key>%) for string replacements (strtr)
     * this is usually called with a specific hook or its "Variables"
     * @param array<string, array<string, mixed>> $variables
     * @return array<string, string>
     *
     * TODO refactor to PluginParameters and make %-strings to class consts
     */
    public static function getKeys(array $variables = []): array
    {
        // all the keys we usually need (and have)
        $keys = [
            '%Plugin:PluginID%' => '%Plugin:PluginID%',
            '%Plugin:PluginNo%' => '%Plugin:PluginNo%',
            '%Plugin:PluginName%' => '%Plugin:PluginName%',
            '%Tool:ToolID%' => '%Tool:ToolID%',
            '%Tool:Name%' => '%Tool:Name%',
            '%Tool:RelListID%' => '%Tool:RelListID%',
            '%Tool:AssignTagID%' => '%Tool:AssignTagID%',
            '%User:UserID%' => '%User:UserID%',
            '%Subscriber:SubscriberID%' => '%Subscriber:SubscriberID%',
            '%Subscriber:ReferenceID%' => '%Subscriber:ReferenceID%',
        ];
        // $variables is function specific (from Hooks)
        foreach ($variables as $key => $field) {
            $keys[$key] = empty($field['default']) ? $key : $field['default'];
            // special case: %Subscriber:FullName% need FirstName and LastName
            if ($key == '%Subscriber:FullName%') {
                $keys['%Subscriber:CustomFieldFirstName%'] = $keys['%Subscriber:CustomFieldFirstName%'] ??
                    '%Subscriber:CustomFieldFirstName%';
                $keys['%Subscriber:CustomFieldLastName%'] = $keys['%Subscriber:CustomFieldLastName%'] ??
                    '%Subscriber:CustomFieldLastName%';
            }
        };
        return $keys;
    }

    /**
     * @param array<string, mixed> $hook
     * @return array<int|string, mixed>
     */
    public static function getAllHookVariables(array $hook = []): array
    {
        $variables = [];
        static::applyToVariable($hook, function ($key, $field) use (&$variables) {
            $variables[$key] = $field;
        });
        return $variables;
    }

    /**
     * get an external reference id from parameters (or 0 if undefined)
     * @param array $plugin
     * @param array $parameters
     * @return false|int
     *
     * TODO refactor to private PluginParameters::getExtReferenceId(...)
     */
    public static function getExtReferenceId(array $plugin, array $parameters): ?int
    {
        // Not all plugins support references, therefore we only handle them, if set.
        if (!empty($parameters['%Subscriber:ExtReferenceID%'])) {
            // References are saved a separate database table for external plugins, therefore we need to
            // create an entity, which will return a referenceId that can be used for our subscriber.
            return Reference::getOrCreate([
                'RelOwnerUserID' => $parameters['%User:UserID%'],
                'NumberRange' => 'plugin-' . $plugin['PluginID'],
                'ExtReferenceID' => $parameters['%Subscriber:ExtReferenceID%']
            ]);
        }

        return false;
    }

    /**
     * get custom field information from a key in a variables definition
     * @param string $key
     * @param array<string, string> $parameters
     * @return array<string, mixed>|null
     *
     * TODO refactor to private PluginParameters::getCustomFieldInformation(...)
     */
    public static function getCustomFieldInformation(string $key, array $parameters): ?array
    {
        // any plugin field requested?
        if (strpos($key, '%Subscriber:CustomFieldPlugin:') === 0) {
            // get 'Link-%Tool:ToolID%' from $key
            $entityKey = Plugin::unwrap_key('Subscriber:CustomFieldPlugin:', $key);
            // replace parametsrs, so key is like 'Link-4711'
            $entityKey = Plugin::replace_parameters($entityKey, $parameters);
            // get 'Link' from $key and update field information with subType data
            [$subType,] = explode('-', $entityKey);
            $fieldInformation = array_merge(
                CustomFields::$GlobalCustomFieldDefs['Plugin'],
                CustomFields::$GlobalCustomFieldArrayDefs[$subType]
            );
            $fieldInformation['CustomFieldID'] = $entityKey;
            return $fieldInformation;
        }
        // any other custom field requested?
        if (strpos($key, '%Subscriber:CustomField') === 0) {
            // get 'Firstname' or '%Tool:ToolID%' from $key
            $entityKey = Plugin::unwrap_key('Subscriber:CustomField', $key);
            // replace parametsrs, so key is like 'Firstname' or '4711'
            $entityKey = Plugin::replace_parameters($entityKey, $parameters);
            $fieldInformation = CustomFields::RetrieveCustomField(
                $entityKey,
                intval($parameters['%User:UserID%'])
            );
            if (is_array($fieldInformation)) {
                $fieldInformation['CustomFieldID'] = $entityKey;
                return $fieldInformation;
            }
        }
        // no custom field requested or found
        return null;
    }

    /** check if the parameter is a valid numeric array index
     * @param string $str
     * @return bool
     */
    public static function isValidArrayIndex(string $str): bool
    {
        if (!is_numeric($str) || !ctype_digit($str)) {
            return false;
        }

        // Cast the string to an integer and compare it to the original string
        // to check for leading zeros. Also ensure it's not negative.
        return ($str === strval(intval($str))) && ($str === "0" || $str[0] !== "0");
    }

    /** applies $callback to all "SmartTags" in $structure and collects the results
    * @param array<string, mixed> $structure
    * @param callable $callback
    * @return array<int, mixed>|null
    */
    public static function applyToSmartTags(array $structure, callable $callback): ?array
    {
        return static::applyToVariable($structure, function ($key, $field) use ($callback) {
            if ($field['type'] == Plugin::PLUGIN_VARIABLE_TYPE_SMARTTAG) {
                return $callback($key, $field);
            }
        });
    }

    /** applies $callback to all "Variables" in $structure and collects the results (recursivly)
     * @param array<string, array<string, mixed>> $structure
     * @param callable $callback
     * @return array<int, mixed>|null
     */
    public static function applyToVariable(array $structure, callable $callback): ?array
    {
        $results = [];
        // recursion through all sub elements
        foreach ($structure as $subKey => $subElement) {
            if (in_array($subKey, ['Variables', 'validate', 'process'])) {
                foreach ($structure[$subKey] as $key => $field) {
                    // Anwenden des Callbacks auf das SmartTag
                    $results[] = $callback($key, $field);
                }
                // there are no 'Variables' below 'Variables', so no recursion here
                continue;
            }
            if (is_array($subElement)) {
                $result = static::applyToVariable($subElement, $callback);
                $results = is_array($result) ? array_merge($result, $results) : $results;
            }
        }
        return $results;
    }

    /**
     * Regex evaluate a value from the request (inbound)
     * @param string $value the value to evaluate
     * @param string $regex the regex declaration of the field
     * @param array<string, string> $parameters other evaluated values
     * @return string|null
     */
    public static function applyRegex(string $value, string $regex, array $parameters = []): ?string
    {
        // regex (in case the regex contains parameters, we need to replace them quoted)
        $quoted = array_map('preg_quote', array_filter($parameters, 'is_scalar'));
        $regex = strtr($regex, $quoted);
        // apply regex
        $matches = [];
        if (preg_match($regex, $value, $matches)) {
            $value = $matches[1];
        } else {
            return null;
        }
        // return the regex match result
        // as regex matched, we return at least an empty value (instead of null)
        return empty($value) ? '' : $value;
    }

    /**
     * Sanitize a value from the request (inbound)
     * @param string $value the value to sanitize
     * @param mixed $sanitize the sanitize function
     * @return string
     */
    public static function sanitize(string $value, $sanitize): string
    {
        switch ($sanitize) {
            case 'int':
                return (string)intval($value);
            case 'url':
                return drupal_strip_dangerous_protocols($value);
            default:
                return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        }
    }

    /**
     * Retrieve, regex evaluate and sanitize a value from the request (inbound)
     * @param bool $pop
     * @param string $fieldpath (piped) path to field
     * @param array<string, mixed> $request the request
     * @param array<string, string> $parameters other evaluated values
     * @return array|string|null
     */
    public static function fetchFieldFromRequest(bool $pop, string $fieldpath, array &$request, array $parameters = [])
    {
        if ($pop) {
            $value = &$request;
        } else {
            $value = $request;
        }
        // extract piped keys (structure hierarchy)
        $keys = explode('|', $fieldpath);
        foreach ($keys as $key) {
            // json ?
            if (is_string($value) && $decoded = json_decode($value, true)) {
                $value = $decoded;
            }
            // object ?
            if (is_object($value)) {
                $value = (array)$value;
            }
            // array anyway ?
            if (!is_array($value)) {
                // requested sub structure is missing, so field is missing, too
                if ($pop) {
                    $value = null;
                } else {
                    $value = '';
                }
                break;
            };

            // key in structure might be a value
            $key = static::replace_parameters($key, $parameters);
            if (static::isValidArrayIndex($key)) {
                $key = intval($key);
            };

            // value is the current field value, if we stop now
            if ($pop) {
                $value = &$value[$key];
            } else {
                $value = $value[$key] ?? '';
            }
        }
        if ($pop) {
            return array_pop($value);
        } else {
            return $value;
        }
    }

    /**
     * Retrieve, regex evaluate and sanitize a value from the request (inbound)
     * @param bool $pop
     * @param array<string, mixed> $field declaration of the field
     * @param array<string, mixed> $request the request
     * @param array<string, string> $parameters other evaluated values
     * @return string|null
     */
    private static function getSanitizedInboundField(
        bool $pop,
        array $field,
        array &$request,
        array $parameters = []
    ): ?string {
        $encoded = false;
        if (isset($field['pattern']) && is_string($field['pattern'])) {
            // internal string with placeholders
            $result = static::replace_parameters($field['pattern'], $parameters);
        } elseif (isset($field['path']) && is_numeric($field['path'])) {
            // inbound "path"
            $result = arg($field['path']) ?? '';
        } elseif (isset($field['field']) && is_string($field['field'])) {
            // extarnal field from request structure
            $fieldpath = Plugin::replace_parameters($field['field'], $parameters);
            $result = static::fetchFieldFromRequest($pop, $fieldpath, $request, $parameters);
            if ($result === null) {
                return null;
            }
            // stringify
            if (is_scalar($result)) {
                $result = (string)$result;
            } else {
                $result = json_encode($result);
                if ($result === false) {
                    return null;
                }
                // remember to have a structure as value
                $encoded = true;
            }
        } else {
            // no pattern, no field, so it gets filled by side effects
            return null;
        }
        // regex evaluate
        if (isset($field['regex']) && is_string($field['regex'])) {
            $result = Plugin::applyRegex($result, $field['regex'], $parameters);
            if ($result === null || $result === '') {
                return $result;
            }
            if ($encoded) {
                // if we had a structure and fetched a string from it (with the regex), decode it
                $temp = json_decode('"' . $result . '"', true);
                // ... unless its weird
                if (is_string($temp)) {
                    $result = $temp;
                }
            }
            // sanitize (pattern are already sanitized - beware of double sanitizing)
            if (!isset($field['pattern'])) {
                $result = static::sanitize($result, $field['sanitize']);
            }
        }
        return $result;
    }

    /**
     * Retrieve, regex evaluate and sanitize a value from the request (inbound)
     * @param array<string, mixed> $field declaration of the field
     * @param array<string, mixed> $request the request
     * @param array<string, string> $parameters other evaluated values
     * @return string|null
     */
    public static function getInboundField(array $field, array $request, array $parameters = []): ?string
    {
        return self::getSanitizedInboundField(false, $field, $request, $parameters);
    }

    /**
     * Retrieve, regex evaluate and sanitize a value from the request (inbound).
     * The field ist then popped from the $request structure (e.g. to get the next value from a list).
     * @param array<string, mixed> $field declaration of the field
     * @param array<string, mixed> $request the request
     * @param array<string, string> $parameters other evaluated values
     * @return string|null
     */
    public static function popInboundField(array $field, array &$request, array $parameters = []): ?string
    {
        return self::getSanitizedInboundField(true, $field, $request, $parameters);
    }

    /**
     * Retrieve and regex check a value from the request (inbound)
     * @param string $key
     * @param array<string, mixed> $field declaration of the field
     * @param array<string, mixed> $request the request
     * @param array<string, string> $parameters other evaluated values
     * @param string $result the sanitized result of the regex
     * @return bool
     */
    public static function validateInboundField(
        string $key,
        array $field,
        array $request,
        array $parameters = [],
        string &$result = ''
    ): bool {
        if (empty($field['regex']) || !is_string($field['regex'])) {
            return false;
        }
        if (isset($field['pattern']) && is_string($field['pattern'])) {
            // internal string with placeholders
            $result = static::replace_parameters($field['pattern'], $parameters);
        } elseif (isset($field['path']) && is_numeric($field['path'])) {
            // inbound "path"
            $result = arg($field['path']) ?? '';
        } elseif (isset($field['field']) && is_string($field['field'])) {
            // extarnal field from request structure
            $fieldpath = Plugin::replace_parameters($field['field'], $parameters);
            $result = static::fetchFieldFromRequest(false, $fieldpath, $request, $parameters);
            // stringify
            if (is_scalar($result)) {
                $result = (string)$result;
            } else {
                $result = json_encode($result);
                if ($result === false) {
                    return false;
                }
            }
        } else {
            // a key alredy filled in parameters
            $result = $parameters[$key];
        }
        // regex evaluate
        $result = Plugin::applyRegex($result, $field['regex'], $parameters);
        // applyRegex returns null if regex fails
        return $result !== null;
    }

    // makes "something" out of "%<wrapper>something%"
    public static function unwrap_key($wrapper, $key)
    {
        if (preg_match('/^%' . $wrapper . '(.*)%$/', $key, $matches)) {
            return $matches[1];
        }
        // no wrapper, no change
        return $key;
    }

    // helper to calc an MySQL int(11) hash from a string
    public static function hash_from_string($string)
    {
        // MySQL allows max 2^31 in int(11)
        return crc32($string) >> 1;
    }

    /*
     * create guzzle request of
        'Request' => [
          ...
          'url' => '...',
          'request' => [
            // parameters for guzzle_http_request
            'method' => 'PUT',
            'auth' => 'bearer',
            'bearer_token' => '%UserVariable:Token%',
            'timeout' => 10.0,
            'data' => [
              'outbound_webhook_url' => APP_URL.'/api/plugin/inbound',
              'integration_data' => [
                "klicktipp_entity_url" => APP_URL.'plugins/%User:UserID%/%Tool:ToolID%/edit',
                "klicktipp_entity_name" => "%Tool:Name%",
              ],
            ],
          ]
        ],
     */
    public static function create_guzzle_request($request, $parameters)
    {
        $ReboundURL = static::replace_parameters($request['url'], $parameters->getParameters());
        if (empty($request['request']['headers']["User-Agent"])) {
            $request['request']['headers']["User-Agent"] = "KlickTipp/1.0";
        }
        if (empty($request['request']['headers']["Accept"])) {
            $request['request']['headers']["Accept"] = "application/json";
        }
        $options = [];
        foreach ($request['request'] as $key => $data) {
            $options[$key] = empty($data) ? '' : $parameters->deepReplace($data);
        };
        return [$ReboundURL, $options];
    }

    // helper to do "late" markup injections in email content
    public static function inject_placeholder_replacements($Content, $Params)
    {
        foreach ($Params as $key) {
            if (strpos($key, '%PluginField:') === 0) {
                // value of %PluginField:<plugin id>:<field id>%
                $entitykey = Plugin::unwrap_key('PluginField:', $key);
                [$pluginid, $pluginkey] = explode(':', $entitykey);
                $plugin = Plugin::get_plugin($pluginid);
                if (!empty($plugin[$pluginkey])) {
                    $Content = str_replace($key, trim($plugin[$pluginkey]), $Content);
                }
            }
        }
        return $Content;
    }

    /*
     * Helper to sanitize function calls
     */
    public static function calL_function($callable, $param_array)
    {
        // allow static class function and some php basics
        if (is_callable(["App\Klicktipp\Plugin", $callable])) {
            try {
                return call_user_func_array(["App\Klicktipp\Plugin", $callable], $param_array);
            } catch (ArgumentCountError | Exception $e) {
                // log exception
                (new PluginParameters([]))->error(
                    "Plugin::calL_function ($callable) failed with !message",
                    ["!message" => $e->getMessage(), $callable, $param_array]
                );
            }
        } elseif (
            in_array($callable, [
                'date',
                'hash',
                'hash_hmac',
                'strtotime'
            ])
        ) {
            try {
                return call_user_func_array($callable, $param_array);
            } catch (ArgumentCountError | Exception $e) {
                // log exception
                (new PluginParameters([]))->error(
                    "Plugin::calL_function ($callable) failed with !message",
                    ["!message" => $e->getMessage(), $callable, $param_array]
                );
            }
        }
        // deny anything we do not know (including "eval")
        return false;
    }

    /* PLUGIN METHODS (callables used in plugin yaml), e.g.:
      Variables:
        '%Temp:CalcHash%':
          type: Calc
          method: pmValidationCreateTimestampHash
          parameter:
            - '%App:Time%'
            - sha512
            - '%App:Salt%'
    */

    public static function pmGetCustomFieldNameFromId(string $customFieldId): string
    {
        $CustomField = CustomFields::RetrieveCustomField($customFieldId);
        return CustomFields::GetFieldName($CustomField) ?? '';
    }

    /**
     * @param string $uid
     * @param array<int> $allowedCustomFields
     * @param int|null $multiValue
     * @return array<string, string>
     */
    public static function pmGetOptionsFromFilteredCustomFields(
        string $uid,
        array $allowedCustomFields,
        ?int $multiValue = null
    ): array {
        if (!is_numeric($uid)) {
            return [];
        }
        $uid = intval($uid);

        $customFields = CustomFields::retrieveCustomFieldsFilteredByType($uid, $allowedCustomFields, $multiValue);

        $customFieldOptions = ['' => t("Please select")];
        foreach ($customFields as $Each) {
            /** @var string|int $customFieldID */
            $customFieldID = $Each['CustomFieldID'];
            $index = strval($customFieldID); // convert the ID so we don't get mixed types for the indices
            $customFieldOptions[$index] = CustomFields::GetFieldName($Each);
        }

        return $customFieldOptions;
    }

    public static function pmGetCookie(string $name): string
    {
        return $_COOKIE[$name] ?? '';
    }

    public static function pmSetCookie(
        string $name,
        string $value,
        ?string $expire = null,
        string $domain = KLICKTIPP_DOMAIN
    ): void {
        if (!$value) {
            return;
        }

        if ($expire) {
            $expire = intval(strtotime($expire));
        } else {
            $expire = 0;
        }

        setcookie($name, $value, [
            'expires' => $expire,
            'path' => '/',
            'domain' => $domain,
            'secure' => true, // likely required for samesite=None cookies
            'httponly' => false,
            'samesite' => 'None'
        ]);
    }

    /**
     * @param int $UserID
     * @param int|string|null $SubscriberID
     * @param int|string|null $ReferenceID
     * @param int|string $FieldID
     * @return string
     */
    public static function pmGetCustomFieldData(
        int $UserID,
        $SubscriberID,
        $ReferenceID,
        $FieldID
    ): string {
        $FieldInformation = Plugin::getCustomFieldInformation(strval($FieldID), []);
        if (!$FieldInformation) {
            return '';
        }

        $value = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);

        if (!$value || !is_string($value)) {
            return '';
        }

        return $value;
    }

    /**
     * used to update usercreated and plugin custom fields
     * @param int $UserID
     * @param int|string|null $SubscriberID
     * @param int|string|null $ReferenceID
     * @param int|string $FieldID
     * @param string $Value
     * @return void
     */
    public static function pmUpdateCustomFieldData(
        int $UserID,
        $SubscriberID,
        $ReferenceID,
        $FieldID,
        string $Value
    ): void {
        // parameters are already replaced, calc took care of that; so we can just pass [] here
        $FieldInformation = Plugin::getCustomFieldInformation(strval($FieldID), []);
        if (!$FieldInformation) {
            trigger_error("no field information");
            return;
        }

        $subscriberStatusChanged =
            CustomFields::UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value);

        if ($subscriberStatusChanged) {
            // we are directly (not through Subscriber::subscribe()) updating a custom field
            // that can potentially be used as a starting condition for a campaign
            // so we need to make sure that the relevant triggers are run
            TransactionEmails::RegisterAutomations(
                $UserID,
                $SubscriberID,
                $ReferenceID
            );
        }
    }

    public static function pmGetSubscriberKey(string $userId, string $subscriberId, string $referenceId): string
    {
        return Core::EncryptURL(
            [
                'UserID' => $userId,
                'SubscriberID' => $subscriberId,
                'ReferenceID' => $referenceId
            ],
            'subscriber_key'
        );
    }

    public static function pmGetSubscriberIdFromSubscriberKey(string $subscriberKey): string
    {
        $result = Core::DecryptURL($subscriberKey);

        if (!$result || !isset($result['SubscriberID'])) {
            return '';
        }

        return $result['SubscriberID'];
    }

    public static function pmGetReferenceIdFromSubscriberKey(string $subscriberKey): string
    {
        $result = Core::DecryptURL($subscriberKey);

        if (!$result || !isset($result['ReferenceID'])) {
            return '';
        }

        return $result['ReferenceID'];
    }

    public static function pmCalculate(string $expression): int
    {
        $expression = preg_replace('/\s/', '', $expression) ?? '';
        $expression = str_replace('^', '**', $expression);

        // we only allow digits, operators, modulo and parentheses
        // we intentionally do not allow anything else - specifically letters or the $ sign - because it opens up attack vectors
        // we also allow "&" and "|" for boolean operations
        // "!" is allowed for negation
        // "?:" for elvis operator
        // "<" and ">" for comparison
        // double quotes are allowed to notate an potentially empty numeric string
        if (!preg_match('#^[0-9+\-*/().%&|=!?:"<>]+$#', $expression)) {
            return 0; // TODO: throw an exception instead
        }

        try {
            return (int)(eval("return ({$expression});"));
        } catch (\Throwable $e) {
            return 0;
        }
    }

    // needed as extra function because we don't allow letters in pmCalculate() for security reasons
    public static function pmStringsAreEqual(string $string1, string $string2): int
    {
        return (int)($string1 === $string2);
    }

    // Method for request validation (see usages of "%Temp:CalcHash%").
    // Validates that an issued hash is not older than 1 hour.
    public static function pmValidationCreateTimestampHash($timestamp, $algo, $salt) {
        return $timestamp.'z'.hash($algo, $timestamp.','.$salt);
    }
    public static function pmValidationTimestampHash($algo, $hash, $salt) {
        [$timestamp, $hashed] = explode('z', $hash, 2);
        if ($timestamp < time()-3600 || $timestamp > time()) {
            return 'too old';
        }
        return static::pmValidationCreateTimestampHash($timestamp, $algo, $salt);
    }
    // Method for token expiration (see usages of "%UserVariable:TokenExpiration%").
    public static function pmCalcTokenExpiration($duration, $default) {
        return $duration > 0 ? time() + $duration - 3 : $default;
    }
    public static function pmCalcIsPast($expiration) {
        return $expiration <= time() ? "T" : "F";
    }
    /*
     * Method for required field with default (see usages of "%Temp:FirstName%").
     */
    public static function pmRequiredDefault($required, $default) {
        return empty($required) ? $default : $required;
    }
    // Method for safe user hashing (timed hash, see usages of "%Temp:TimedUserHash%").
    public static function pmEncryptUserAndTime($uid, $hash = '', $time = 0)
    {
        // if hash has been calculated, return last result
        if (!empty($hash)) {
            return $hash;
        }
        // calculate hash
        $time = $time ?: time();
        return Core::EncryptURL(['UserID' => $uid, 'time' => $time], 'timed_user_hash');
    }
    public static function pmDecryptTimedUserHashCheck(string $hash): string
    {
        $result = Core::DecryptURL($hash);
        return $result['intime'] ? '1' : '0';
    }
    public static function pmDecryptTimedUserHash(string $hash): string
    {
        $result = Core::DecryptURL($hash);
        return $result['UserID'];
    }
    public static function pmSplitFullname(string $fullName, string $part): string
    {
        static $names = [];
        if (!isset($names[$fullName])) {
            $names[$fullName] = CustomFields::splitFullname($fullName);
        }
        return $names[$fullName][$part];
    }
    // simple concat as calc function (implode cant be called with call_user_func_array)
    public static function concat($arg) {
        return implode(func_get_args());
    }

}

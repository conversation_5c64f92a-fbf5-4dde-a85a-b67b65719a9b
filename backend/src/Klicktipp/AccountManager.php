<?php

namespace App\Klicktipp;

use App\Backoff\SpecificExceptionsDecider;
use App\Klicktipp\Account\Event\AccountChangeItem;
use App\Klicktipp\Account\Event\AccountCreatedEvent;
use App\Klicktipp\Account\Event\AccountUpdatedEvent;
use App\Security\Keycloak\Exception\CreateUserFailureException;
use App\Security\Keycloak\Exception\DeleteUserFailureException;
use App\Security\Keycloak\Exception\UpdateUserFailureException;
use App\Security\Keycloak\Exception\UserNotFoundException;
use App\Security\Keycloak\KeycloakApi;
use App\Security\Keycloak\MagicLinkFailureException;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DoctrineDBALException;
use STS\Backoff\Backoff;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Throwable;
use stdClass;

class AccountManager
{
    /**
     * @var int
     */
    public const DRUPAL_AUTHENTICATED_RID = 2;

    /**
     * @var string
     */
    public const DRUPAL_AUTHENTICATED_ROLE = 'authenticated user';

    private Connection $connection;

    private EventDispatcherInterface $eventDispatcher;

    private array $usersCached = [];

    /**
     * @var bool should the sequences table be cleaned up?
     */
    private bool $cleanupSequences = false;

    /**
     * @var string[]
     */
    public static array $userFields = [
        'uid',
        'RelUserGroupID',
        'name',
        'pass',
        'mail',
        'type',
        'theme',
        'signature',
        'signature_format',
        'created',
        'access',
        'login',
        'status',
        'FirstName',
        'LastName',
        'CompanyName',
        'Website',
        'Street',
        'City',
        'State',
        'Zip',
        'Country',
        'Phone',
        'Fax',
        'timezone',
        'language',
        'picture',
        'init',
        'data',
        'ktdata',
        'amemberid',
        'dst',
        'ktdata',
    ];

    /**
     * @var string[]
     */
    public static array $ktDataFields = [
        'Unsubscriptions',
        'CustomFieldsCategoryTabs',
        'AdditionalSenderPhoneNumber',
        'UserPrivileges',
        'UserSettings',
        'TwoFactorAuthorization',
        'SubscriberLimitStatus',
        'cleverbridge_id',
        'cleverbridge_cancellation',
        'cleverbridge_update',
        'digistore_affiliate_name',
        'digistore_cancellation',
        'digistore_update',
        'digistore_receipt',
        'digistore_affiliation',
        'profile_userdata',
        'profile_memberid',
        'profile_lastaccess',
        'firstLogin',
    ];

    public function __construct(
        Connection $connection,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->connection = $connection;
        $this->eventDispatcher = $eventDispatcher;
    }

    /**
     * @throws DoctrineDBALException
     */
    public function __destruct()
    {
        if ($this->cleanupSequences) {
            $this->cleanupSequences();
        }
    }

    /**
     * @param int $id
     * @param bool $reset reset cache ?
     *
     * @return stdClass|false
     *
     * @throws \Doctrine\DBAL\Exception
     */
    public function getAccount(int $id, bool $reset = false)
    {
        if (!$reset && isset($this->usersCached[$id])) {
            return $this->usersCached[$id];
        }

        return $this->loadByQuery(
            'SELECT * FROM drupal_users WHERE uid = :uid',
            ['uid' => $id]
        );
    }

    /**
     * @param string $name
     *
     * @return stdClass|false
     *
     * @throws DoctrineDBALException
     */
    public function getAccountByName(string $name)
    {
        return $this->loadByQuery(
            'SELECT * FROM drupal_users WHERE name = :name',
            ['name' => $name]
        );
    }

    /**
     * @param string $email
     *
     * @return stdClass|false
     *
     * @throws DoctrineDBALException
     */
    public function getAccountByEmail(string $email)
    {
        return $this->loadByQuery(
            'SELECT * FROM drupal_users WHERE mail = :email',
            ['email' => $email]
        );
    }

    /**
     * @param string $query
     * @param array $params
     *
     * @return false|stdClass
     *
     * @throws DoctrineDBALException
     */
    private function loadByQuery(string $query, array $params)
    {
        $data = $this->connection->fetchAssociative($query, $params);
        if (!$data) {
            return false;
        }
        $account = (object)$data;

        $account->tier = VarTier::GetTier($account->uid);

        // port of klicktipp_user_load
        if ($account->ktdata && $data = unserialize($account->ktdata)) {
            foreach ($data as $key => $value) {
                if (!empty($key) && !isset($account->$key)) {
                    $account->$key = $value;
                }
            }
        }

        $account->roles = $this->getRoles($account->uid);

        return $this->usersCached[$account->uid] = $account;
    }

    /**
     * @param int $userID
     *
     * @return array<int, string>
     *
     * @throws DoctrineDBALException
     */
    private function getRoles(int $userID)
    {
        /** @var array<int, string> $storedRoles */
        $storedRoles = $this->connection->fetchAllKeyValue(
            'SELECT r.rid, name FROM `drupal_users_roles` ur ' .
            'INNER JOIN `drupal_role` r ON ur.`uid` = :uid AND ur.`rid` = r.`rid`  ' .
            ' ORDER BY weight',
            ['uid' => $userID]
        );

        return [static::DRUPAL_AUTHENTICATED_RID => static::DRUPAL_AUTHENTICATED_ROLE] + $storedRoles;
    }

    /**
     * @param ?stdClass $account
     * @params array $edit
     *
     * @throws DoctrineDBALException
     */
    public function saveAccount(?stdClass $account, array $edit = [])
    {
        // Note: remove this block as far as we switched to keycloak
        if (isset($edit['pass']) && strlen(trim($edit['pass'])) > 0) {
            $edit['pass'] = user_hash_password(trim($edit['pass']));
            // Abort if the hashing failed and returned false.
            if (!$edit['pass']) {
                return false;
            }
        } else {
            // Avoid overwriting an existing password with a blank password.
            unset($edit['pass']);
        }

        $account ??= new stdClass();

        $updatedFields = [];
        foreach ($edit as $key => $value) {
            $oldValue = $account->$key;
            if ($oldValue === $value) {
                continue;
            }
            $changeItem = new AccountChangeItem();
            $changeItem->field = $key;
            $changeItem->oldValue = $oldValue;
            $changeItem->newValue = $value;
            $updatedFields[] = $changeItem;

            $account->$key = $value;
        }
        $params = (array)$account;
        $this->prepareKtDataForSave($params);
        $params = $this->sanitizeFields($params);

        if (isset($params['uid'])) {
            $uid = $params['uid'];
            $this->connection->update('drupal_users', $params, ['uid' => $uid]);
            $event = new AccountUpdatedEvent();
            $event->changes = $updatedFields;
        } else {
            $uid = $params['uid'] = $this->nextId();
            $this->connection->insert('drupal_users', $params);
            $account->uid = $uid;
            $event = new AccountCreatedEvent();
        }

        $event->account = $account;
        $this->eventDispatcher->dispatch($event);

        return $this->getAccount($uid, true);
    }

    /**
     * @see \DatabaseConnection_mysql::nextId
     *
     * @return int
     *
     * @throws DoctrineDBALException
     */
    public function nextId()
    {
        $existingId = $this->connection->fetchOne('SELECT MAX(uid) FROM drupal_users');
        $this->connection->executeQuery('INSERT INTO drupal_sequences () VALUES ()');
        $newId = $this->connection->lastInsertId();
        // This should only happen after an import or similar event.
        if ($existingId >= $newId) {
            // If we INSERT a value manually into the sequences table, on the next
            // INSERT, MySQL will generate a larger value. However, there is no way
            // of knowing whether this value already exists in the table. MySQL
            // provides an INSERT IGNORE which would work, but that can mask problems
            // other than duplicate keys. Instead, we use INSERT ... ON DUPLICATE KEY
            // UPDATE in such a way that the UPDATE does not do anything. This way,
            // duplicate keys do not generate errors but everything else does.
            $this->connection->executeQuery(
                'INSERT INTO drupal_sequences (value) VALUES (:value) ',
                ['value' => $existingId]
            );
            $this->connection->executeQuery('INSERT INTO drupal_sequences () VALUES ()');
            $newId = $this->connection->lastInsertId();
        }
        $this->cleanupSequences = true;
        return (int)$newId;
    }

    /**
     * @see \DatabaseConnection_mysql::nextIdDelete
     *
     * @throws DoctrineDBALException
     */
    private function cleanupSequences(): void
    {
        // While we want to clean up the table to keep it up from occupying too
        // much storage and memory, we must keep the highest value in the table
        // because InnoDB  uses an in-memory auto-increment counter as long as the
        // server runs. When the server is stopped and restarted, InnoDB
        // reinitializes the counter for each table for the first INSERT to the
        // table based solely on values from the table so deleting all values would
        // be a problem in this case. Also, TRUNCATE resets the auto increment
        // counter.
        try {
            $maxId = $this->connection->executeQuery('SELECT MAX(value) FROM drupal_sequences')->fetchOne();
            $this->connection->executeQuery(
                'DELETE FROM drupal_sequences WHERE value < :value',
                ['value' => $maxId]
            );
        } catch (Throwable $e) {
            // During testing, this function is called from shutdown with the
            // simpletest prefix stored in $this->connection, and those tables are gone
            // by the time shutdown is called so we need to ignore the database
            // errors. There is no problem with completely ignoring errors here: if
            // these queries fail, the sequence will work just fine, just use a bit
            // more database storage and memory.
        }
    }


    private function sanitizeFields(array $data): array
    {
        return array_intersect_key($data, array_flip(self::$userFields));
    }

    private function prepareKtDataForSave(array &$account)
    {
        $ktdata = [];
        if (!empty($account['ktdata'])) {
            $ktdata = unserialize($account['ktdata']) ?: [];
        }

        // amember cache flag
        $ktdata['profile_lastaccess'] = $account['profile_lastaccess'] ?? 0;
        unset($account['profile_lastaccess']);

        // clear old cache data
        $ktdata['profile_payments'] = '';

        foreach (static::$ktDataFields as $field) {
            if (isset($account[$field])) {
                $ktdata[$field] = $account[$field];
                unset($account[$field]);
            }
        }

        return $account['ktdata'] = serialize($ktdata);
    }

    public static function hasAccess(stdClass $account, string $featureId): bool
    {
        $launchDarkly = new FeatureToggle($account);
        return $launchDarkly->featureToggle($featureId);
    }

    // checks Drupal permission for a feature
    // if false, an upsell can be shown instead of giving no access to the page
    // see: klicktipp.module -> klicktipp_feature_access()
    // Note: try not to use this function, use hasAccess() instead (launch darkly flag)
    public static function hasFeatureDrupalAccess(stdClass $account, string $permission): bool
    {
        if (user_access($permission)) {
            // user or the support has access to the feature
            return true;
        }
        if (Subaccount::FindMainAccount() === $account->uid) {
            // this is a subaccount access, so check if main account has access
            if (user_access($permission, $account)) {
                return true;
            }
        }

        // show upsell
        return true;
    }

    public static function isAdmin(stdClass $account): bool
    {
        return user_access('klicktipp admin role', $account);
    }

    public static function isSupport(?stdClass $account = null): bool
    {
        return user_access('administer klicktipp', $account);
    }

    public static function canImport(stdClass $account): bool
    {
        if (static::isSupport() || user_access('import without domain', $account)) {
            return true;
        }

        if (user_access('use whitelabel domain', $account)) {
            // don't use empty here, return value can be true, if fallback to shared is active
            return is_array(DomainSet::GetValidSenderDomains((array)$account, true));
        }

        return $account->created <
            strtotime(sprintf('-%u days', variable_get('klicktipp_min_age_of_standard_account', 45))) &&
            !empty(DomainSet::GetValidDomains($account->uid, true));
    }

    public static function accessBeacon(stdClass $account): bool
    {
        if (user_access('suppress beamer for automated testing', $account)) {
            // the account is an e2e test user
            // do not show the beamer or other marketing widgets, since popups could disrupt tests
            return false;
        }

        if (static::hasAccess($account, 'show-user-like')) {
            // user who should see the userlike widget cannot see the beacon,
            // no matter if UserLike is configured or not
            return false;
        }

        if (static::hasAccess($account, 'show-beacon')) {
            return !empty(variable_get('klicktipp_helpscout_scripts_beacon', ''));
        }

        return false;
    }

    public static function accessUserLike(stdClass $account): bool
    {
        if (user_access('suppress beamer for automated testing', $account)) {
            // the account is an e2e test user
            // do not show the beamer or other marketing widgets, since popups could disrupt tests
            return false;
        }

        if (static::hasAccess($account, 'show-user-like')) {
            return !empty(variable_get('klicktipp_helpscout_scripts_user_like', ''));
        }

        return false;
    }

    public static function getHelpScoutWidget(stdClass $account, ?stdClass $userData = null): string
    {
        $script = '';

        if (static::accessUserLike($account)) {
            $script = variable_get('klicktipp_helpscout_scripts_user_like', '');
        } elseif (static::accessBeacon($account)) {
            $script = variable_get('klicktipp_helpscout_scripts_beacon', '');
        }

        if (!empty($script)) {
            // $account is the main account and $userData could come from a sub account
            $userData = $userData ?? $account;

            // there will be a javascript error if custom fields will be prefilled with an empty string
            $accountType = (empty($userData->uid)) ? ' ' : UserGroups::$DisplayAccountCategory[UserGroups::GetAccountCategory(
                $account
            )];
            $username = (empty($userData->uid)) ? ' ' : $userData->name;
            $fullName = (empty($userData->uid)) ? '' : "{$userData->FirstName} {$userData->LastName}";
            $emailAddress = (empty($userData->uid)) ? '' : $userData->mail;
            $script = str_replace(
                ['%name%', '%username%', '%email%', '%accountType%'],
                [$fullName, $username, $emailAddress, $accountType],
                $script
            );
        }

        return $script;
    }

    /**
     * Creates an user in keycloak by using data of drupal account. It' static because it's used in a drupal context.
     * After getting rid of drupal it can be refactored in order to be instance methode and use keycloak api client
     * via dependency injection.
     *
     * @params KeycloakApi $keycloakApiClient
     * @param stdClass $account drupal account. Assumed not to have an uid (not save in db yet)
     *
     * @return string keycloak user identifier
     *
     * @throws \Exception
     */
    public static function createKeycloakUser(
        KeycloakApi $keycloakApiClient,
        stdClass $account,
        bool $emailVerified = true
    ): string {
        return self::getBackoff('Failed to create user in Keycloak', CreateUserFailureException::class)
            ->run(
                fn() => $keycloakApiClient->createUser(
                    $account->name,
                    $account->mail,
                    $account->FirstName ?? '',
                    $account->LastName ?? '',
                    $emailVerified
                )
            );
    }

    /**
     * @param KeycloakApi $keycloakApiClient
     * @param string $email email of the user. should be the same as in keycloak
     *
     * @return void
     * @throws \Exception
     */
    public static function triggerKeycloakEmailVerification(
        KeycloakApi $keycloakApiClient,
        string $email
    ): void {
        self::getBackoff('Failed to trigger verification e-mail', MagicLinkFailureException::class)
            ->run(fn() => $keycloakApiClient->generateMagicLink($email, true));
    }

    /**
     * Updates an user in keycloak by using data of drupal account. It' static because it's used in a drupal context.
     * After getting rid of drupal it can be refactored in order to be instance methode and use keycloak api client
     * via dependency injection
     * @param KeycloakApi $keycloakApiClient
     * @param string $keycloakUserId identifier of the keycloak user. See self::createKeycloakUser
     * @param stdClass $account drupal account. Assumed to have an uid (alredy stored in drupal db)
     * @param int|null $mainAccountId only needed for subaccounts
     *
     * @return void
     *
     * @throws \Exception
     */
    public static function updateKeycloakUser(
        KeycloakApi $keycloakApiClient,
        string $keycloakUserId,
        stdClass $account,
        ?int $mainAccountId = null
    ): void {
        self::getBackoff('Failed to update user in Keycloak', UpdateUserFailureException::class)
            ->run(
                fn() => $keycloakApiClient->updateUser(
                    $keycloakUserId,
                    [
                        'username' => $account->name,
                        'email' => $account->mail,
                        'firstName' => $account->FirstName,
                        'lastName' => $account->LastName,
                        'attributes' => [
                            'app_uid' => $account->uid,
                            'app_mainaccountid' => $mainAccountId ?: $account->uid,
                        ]
                    ],
                )
            );
    }

    public static function deleteKeycloakUser(
        KeycloakApi $keycloakApiClient,
        stdClass $account
    ): void {
        self::getBackoff(
            'Failed to delete user in Keycloak',
            DeleteUserFailureException::class,
            UserNotFoundException::class
        )
            ->run(static function () use ($keycloakApiClient, $account) {
                $keycloakUserId = $keycloakApiClient->getUserIdByName($account->name);
                if ($keycloakUserId) {
                    $keycloakApiClient->deleteUser($keycloakUserId);
                }
            });
    }

    /**
     * @param string $errorMessage
     * @param class-string<\Throwable> $retryableExceptions
     *
     * @return Backoff
     */
    private static function getBackoff(string $errorMessage, ...$retryableExceptions): Backoff
    {
        return (new Backoff())
            ->setStrategy('exponential')
            ->setMaxAttempts(3)
            ->enableJitter()
            ->setDecider(new SpecificExceptionsDecider(...$retryableExceptions))
            ->setErrorHandler(
                fn(Throwable $exception, int $attempt, int $maxAttempts) => watchdog(
                    'keycloak',
                    $errorMessage . ' @message (Attempt !attempt of !maxAttempts). Exception: @exception',
                    [
                        '@message' => $exception->getMessage(),
                        '!attempt' => $attempt,
                        '!maxAttempts' => $maxAttempts,
                        '@exception' => $exception->__toString()
                    ],
                    WATCHDOG_WARNING,
                )
            );
    }
}

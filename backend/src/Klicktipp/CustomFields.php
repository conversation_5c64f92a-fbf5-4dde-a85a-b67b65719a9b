<?php

namespace App\Klicktipp;

use App\Klicktipp\Customfield\CustomfieldLinkResolver;
use App\Klicktipp\Customfield\Queue\ValueObject\CustomfieldValueDbValueObject;
use App\Klicktipp\Includes\UtilsNumber;
use DateTime;
use Exception;

/**
 * CustomFields class
 *
 * This class holds all custom field related functions
 **/
class CustomFields extends DatabaseTableLabeled
{
    const TYPE_SINGLE = 1;
    const TYPE_PARAGRAPH = 2;
    const TYPE_DROPDOWN = 3;
    const TYPE_CHECKBOX = 4;
    const TYPE_EMAIL = 5;
    const TYPE_NUMBER = 6;
    const TYPE_URL = 7;
    const TYPE_DATE = 8;
    const TYPE_TIME = 9;
    const TYPE_DATETIME = 10;
    const TYPE_HTML = 11;
    const TYPE_DECIMAL = 12;
    const TYPE_ARRAY = 13; // array of strings

    /** @deprecated  */
    const TYPE_STRING_SINGLE = 'single';
    /** @deprecated  */
    const TYPE_STRING_PARAGRAPH = 'paragraph';
    /** @deprecated  */
    const TYPE_STRING_DROPDOWN = 'dropdown';
    /** @deprecated  */
    const TYPE_STRING_CHECKBOX = 'checkbox';
    /** @deprecated  */
    const TYPE_STRING_EMAIL = 'email';
    /** @deprecated  */
    const TYPE_STRING_NUMBER = 'number';
    /** @deprecated  */
    const TYPE_STRING_URL = 'url';
    /** @deprecated  */
    const TYPE_STRING_DATE = 'date';
    /** @deprecated  */
    const TYPE_STRING_TIME = 'time';
    /** @deprecated  */
    const TYPE_STRING_DATETIME = 'datetime';
    /** @deprecated  */
    const TYPE_STRING_HTML = 'html';
    /** @deprecated  */
    const TYPE_STRING_DECIMAL = 'decimal';

    const TYPES_WITH_LENGTH_LIMIT = [
        self::TYPE_SINGLE,
        self::TYPE_HTML,
        self::TYPE_PARAGRAPH,
        self::TYPE_URL,
    ];

    const TYPES_WITH_URL_CHECK = [
        self::TYPE_SINGLE,
        self::TYPE_PARAGRAPH,
    ];


    const CALCULATE_OP_VALUE = 'set field value';
    const CALCULATE_OP_COPY = 'copy from field';
    const CALCULATE_OP_COPY_SUBSCRIBER_FIELDS = 'copy from subscriber';
    const CALCULATE_OP_EMPTY = 'empty field';
    const CALCULATE_OP_TEXT_PREPEND = 'prepend string';
    const CALCULATE_OP_TEXT_APPEND = 'append string';
    const CALCULATE_OP_TEXT_REPLACE = 'replace string';
    const CALCULATE_OP_TEXT_HASH_CRC32 = 'generate CRC32 hash'; //8 chars
    const CALCULATE_OP_TEXT_HASH_MD5 = 'generate MD5 hash'; //32 chars
    const CALCULATE_OP_TEXT_HASH_SHA1 = 'generate SHA1 hash'; // 40 chars
    const CALCULATE_OP_TEXT_HASH_SHA256 = 'generate SHA256 hash'; //64 chars
    const CALCULATE_OP_TEXT_HASH_SHA384 = 'generate SHA384 hash'; //96 chars
    const CALCULATE_OP_TEXT_HASH_SHA512 = 'generate SHA512 hash'; //128 chars
    const CALCULATE_OP_NUMBER_ADD = 'add number';
    const CALCULATE_OP_NUMBER_SUBTRACT = 'subtract number';
    const CALCULATE_OP_NUMBER_RANDOM = 'random number';
    const CALCULATE_OP_URL_TRUNCATE_QUERY = 'truncate url query';
    const CALCULATE_OP_URL_TRUNCATE_PATH = 'truncate url path';
    const CALCULATE_OP_DATETIME_NOW = 'datetime now';
    const CALCULATE_OP_DATETIME_INCREASE = 'datetime increase';
    const CALCULATE_OP_DATETIME_DECREASE = 'datetime decrease';
    const CALCULATE_OP_DATETIME_NEXT_FIRST_OF_MONTH_FROM_NOW = 'now next month';
    const CALCULATE_OP_DATETIME_NEXT_FIRST_OF_YEARS_FROM_NOW = 'now next year';
    const CALCULATE_OP_DATETIME_NEXT_FIRST_OF_MONTH_FROM_FIELD = 'field next month';
    const CALCULATE_OP_DATETIME_NEXT_FIRST_OF_YEARS_FROM_FIELD = 'field next year';
    const CALCULATE_OP_DATETIME_ULTIMO_MONTH = 'ultimo';
    const CALCULATE_OP_DATETIME_EASTER = 'easter date';
    const CALCULATE_OP_DATETIME_TIME_NOW = 'time now';
    const CALCULATE_OP_DATETIME_TIME_FULLHOUR_FLOOR = 'floor full hour';
    const CALCULATE_OP_DATETIME_TIME_FULLHOUR_CEIL = 'ceil full hour';
    const CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND = 'round full hour';

    const GENDER_MALE = 'M';
    const GENDER_FEMALE = 'F';
    const GENDER_UNISEX = 'U';

    //max length of custom field value text,
    //determined by DB (field type text == 64k)
    const VALUE_MAX_LENGTH = 64 * 1024 - 1;

    public static $FieldType = CustomFields::TYPE_SINGLE;

    public static $DBTableName = 'custom_fields';
    public static $DBSerialField = 'CustomFieldID';
    public static $DBTypeField = 'FieldTypeEnum';
    public static $EntityBaseType = DatabaseNamedTable::BASETYPE_CUSTOM_FIELDS;

    public static $RequiredFieldsOnInsert = array(
        'RelOwnerUserID',
        'FieldName',
        'FieldTypeEnum',
    );

    public static $RequiredFieldsOnUpdate = array(
        'CustomFieldID',
        'RelOwnerUserID',
    );

    public static $DefaultFields = array(
        'FieldName' => '',
        'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        'FieldDefaultValue' => '',
        'FieldOptions' => '',
        'FieldCopyTo' => '',
        'FieldParameters' => '',
        'MultiValue' => 1,
    );

    //translatable custom field type names
    /** @deprecated  */
    public static $DisplayFieldType = array(
        CustomFields::TYPE_SINGLE => CustomFields::TYPE_STRING_SINGLE,
        CustomFields::TYPE_PARAGRAPH => CustomFields::TYPE_STRING_PARAGRAPH,
        CustomFields::TYPE_DROPDOWN => CustomFields::TYPE_STRING_DROPDOWN,
        CustomFields::TYPE_CHECKBOX => CustomFields::TYPE_STRING_CHECKBOX,
        CustomFields::TYPE_EMAIL => CustomFields::TYPE_STRING_EMAIL,
        CustomFields::TYPE_NUMBER => CustomFields::TYPE_STRING_NUMBER,
        CustomFields::TYPE_URL => CustomFields::TYPE_STRING_URL,
        CustomFields::TYPE_DATE => CustomFields::TYPE_STRING_DATE,
        CustomFields::TYPE_TIME => CustomFields::TYPE_STRING_TIME,
        CustomFields::TYPE_DATETIME => CustomFields::TYPE_STRING_DATETIME,
        CustomFields::TYPE_HTML => CustomFields::TYPE_STRING_HTML,
        CustomFields::TYPE_DECIMAL => CustomFields::TYPE_STRING_DECIMAL,
    );

    public static $APIIndexFilterTypes = array(
        CustomFields::TYPE_SINGLE => 'field-single',
        CustomFields::TYPE_PARAGRAPH => 'field-paragraph',
        CustomFields::TYPE_EMAIL => 'field-email',
        CustomFields::TYPE_NUMBER => 'field-number',
        CustomFields::TYPE_URL => 'field-url',
        CustomFields::TYPE_DATE => 'field-date',
        CustomFields::TYPE_TIME => 'field-time',
        CustomFields::TYPE_DATETIME => 'field-datetime',
        CustomFields::TYPE_HTML => 'field-html',
        CustomFields::TYPE_DECIMAL => 'field-decimal',
    );

    /**
     * Provide a list of custom field types usable for the customer
     * Note: deprecated (dropdown, checkbox) or hidden (array) types are not included
     * @return string[]
     */
    public static function getAvailableTypesTranslated(): array
    {
        return array(
            self::TYPE_SINGLE => t('Single line'),
            self::TYPE_PARAGRAPH => t('Paragraph text'),
            self::TYPE_EMAIL => t('Email address'),
            self::TYPE_NUMBER => t('Numbers'),
            self::TYPE_DECIMAL => t('Decimal'),
            self::TYPE_URL => t('URL'),
            self::TYPE_DATE => t('Date field'),
            self::TYPE_TIME => t('Time field'),
            self::TYPE_DATETIME => t('Datetime field'),
            self::TYPE_HTML => t('HTML field'),
        );
    }

    public function SetDBArray($DBArray)
    {
        // override to extract FieldParameters
        if (!empty($DBArray['FieldParameters']) && is_string($DBArray['FieldParameters'])) {
            $DBArray['FieldParameters'] = unserialize($DBArray['FieldParameters']);
        }

        parent::SetDBArray($DBArray);
    }

    public static function FromID($UserID, $CustomFieldID)
    {
        if (empty($UserID) || empty($CustomFieldID)) {
            return false;
        }

        if (!empty(CustomFields::$GlobalCustomFieldDefs[$CustomFieldID])) {
            $CustomField = CustomFields::$GlobalCustomFieldDefs[$CustomFieldID];
            $CustomField['RelOwnerUserID'] = $UserID;
            return CustomFields::FromArray($CustomField);
        }

        return parent::FromID($UserID, $CustomFieldID);
    }

    public static function FromArray($DBArray)
    {
        switch ($DBArray['FieldTypeEnum']) {
            case CustomFields::TYPE_SINGLE:
                $entity = new CustomFieldsTypeSingle($DBArray);
                break;
            case CustomFields::TYPE_PARAGRAPH:
                $entity = new CustomFieldsTypeParagraph($DBArray);
                break;
            case CustomFields::TYPE_DROPDOWN:
                $entity = new CustomFieldsTypeDropdown($DBArray);
                break;
            case CustomFields::TYPE_CHECKBOX:
                $entity = new CustomFieldsTypeCheckbox($DBArray);
                break;
            case CustomFields::TYPE_EMAIL:
                $entity = new CustomFieldsTypeEmail($DBArray);
                break;
            case CustomFields::TYPE_NUMBER:
                $entity = new CustomFieldsTypeNumber($DBArray);
                break;
            case CustomFields::TYPE_URL:
                $entity = new CustomFieldsTypeURL($DBArray);
                break;
            case CustomFields::TYPE_DATE:
                $entity = new CustomFieldsTypeDate($DBArray);
                break;
            case CustomFields::TYPE_TIME:
                $entity = new CustomFieldsTypeTime($DBArray);
                break;
            case CustomFields::TYPE_DATETIME:
                $entity = new CustomFieldsTypeDatetime($DBArray);
                break;
            case CustomFields::TYPE_HTML:
                $entity = new CustomFieldsTypeHtml($DBArray);
                break;
            case CustomFields::TYPE_DECIMAL:
                $entity = new CustomFieldsTypeDecimal($DBArray);
                break;
            default:
                // create "single"
                $entity = new CustomFields($DBArray);
                break;
        }

        if (empty($entity->DataFlat)) {
            return false;
        }

        return $entity;
    }

    public static function InsertDB($Data)
    {
        $Data['FieldTypeEnum'] ??= static::$FieldType;
        $Data['MultiValue'] ??= 1;
        return CustomFields::Create($Data);
    }

    public static function UpdateDB($DataFlat)
    {
        // Global custom field can not be changed
        if ($DataFlat['IsGlobal']) {
            return false;
        }

        // Check if field is his own FieldCopyTo
        if ($DataFlat['FieldCopyTo'] == $DataFlat['CustomFieldID']) {
            $DataFlat['FieldCopyTo'] = '';
        }

        //prepare parameters for database
        $DataFlat['FieldParameters'] = empty($DataFlat['FieldParameters']) ? '' : serialize(
            $DataFlat['FieldParameters']
        );

        return parent::UpdateDB($DataFlat);
    }

    public static function DeleteDB($Data)
    {
        $Field = CustomFields::RetrieveCustomField($Data['CustomFieldID'], $Data['RelOwnerUserID']);
        if ($Field == false) {
            return false;
        }

        // Global custom field can not be changed
        if ($Field['IsGlobal']) {
            return false;
        }

        $result = parent::DeleteDB($Data);
        if ($result) {
            // Delete custom_field values
            db_delete('custom_field_values')
                ->condition('RelFieldID', $Field['CustomFieldID'])
                ->condition('RelOwnerUserID', $Field['RelOwnerUserID'])
                ->execute();

            // stop autoresponders with trigger time type from custom fields
            $result = db_query(
                "SELECT CampaignID FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID " .
                " AND AutoResponderTriggerTypeEnum IN (:AutoResponderTriggerTypeEnum) " .
                " AND CampaignStatusEnum IN (:CampaignStatusEnum)",
                array(
                    ':RelOwnerUserID' => $Field['RelOwnerUserID'],
                    ':AutoResponderTriggerTypeEnum' => array(
                        Campaigns::TRIGGER_TYPE_DATETIME,
                        Campaigns::TRIGGER_TYPE_BIRTHDAY,
                        Campaigns::TRIGGER_TYPE_SMS_DATETIME,
                        Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY
                    ),
                    ':CampaignStatusEnum' => Campaigns::$ArrayCampaignStatiSending
                )
            );
            while ($EachCampaign = kt_fetch_array($result)) {
                $CampaignObject = Campaigns::FromID($Field['RelOwnerUserID'], $EachCampaign['CampaignID']);
                if (
                    $CampaignObject->GetData('DelayByCustomFieldDatetimeFieldID') == $Field['CustomFieldID'] ||
                    $CampaignObject->GetData('BirthdayCustomFieldID') == $Field['CustomFieldID']
                ) {
                    Campaigns::StopCampaign($Field['RelOwnerUserID'], $EachCampaign['CampaignID']);
                }
            }

            //remove custom field from privacy information email template if used
            VarPrivacy::OnDeleteCustomField($Field['RelOwnerUserID'], $Field['CustomFieldID']);

            // stop automations with trigger time type from custom fields
            //TODO
        }

        return true;
    }

    public static function DeleteOfUser($UserID)
    {
        $result = parent::DeleteOfUser($UserID);
        if ($result) {
            db_delete('custom_field_values')
                ->condition('RelOwnerUserID', $UserID)
                ->execute();
        }

        return $result;
    }

    // DatabaseTableCRUD

    public static $APIPath = 'kt-field';

    public static $APIFields = array(
        'CustomFieldID' => array(
            'id' => 'id',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'FieldName' => array(
            'id' => 'name',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'MetaLabels' => array(
            'id' => 'metalabels',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM,
        ),
    );

    public static function klicktippapi_index_advanced($filter)
    {
        $account = static::FindMainAccountForAPI();
        if (empty($account)) {
            return services_error(t('API access denied.'), 403);
        }

        //get the custom field types @see: DatabaseTableCRUD::IndexFilter
        $filter = static::IndexFilter($filter);

        $result = array();

        $fields = CustomFields::RetrieveCustomFields(
            $account->uid,
            false,
            array('CustomFieldID' => 'ASC'),
            false,
            true
        );
        foreach ($fields as $f) {
            if (!in_array($f['FieldTypeEnum'], $filter['Types'])) {
                continue;
            }

            if ($f['IsGlobal']) {
                $result[$f['CustomFieldID']] = array(
                    'id' => $f['CustomFieldID'],
                    'name' => CustomFields::GetFieldName($f),
                    'type' => CustomFields::$APIIndexFilterTypes[$f['FieldTypeEnum']],
                    'category' => t("General"),
                );
            } else {
                $ObjectField = CustomFields::FromArray($f);
                $Labels = $ObjectField->GetMetaLabels(false);

                $params = (empty($f['FieldParameters'])) ? array() : unserialize($f['FieldParameters']);
                $result[$f['CustomFieldID']] = array(
                    'id' => $f['CustomFieldID'],
                    'name' => CustomFields::GetFieldName($f),
                    'type' => CustomFields::$APIIndexFilterTypes[$f['FieldTypeEnum']],
                    'category' => (empty($params['FieldCategory'])) ? t("General") : $params['FieldCategory'],
                    'metalabels' => $Labels
                );
            }
        }

        return array(
            'data' => $result,
        );
    }

    /**
     * @deprecated
     */
    public static function getFieldName($ArrayCustomField)
    {
        if ($ArrayCustomField['IsGlobal']) {
            //@see $GlobalCustomFieldDefs for registering global field names for translation parser
            return t(/*ignore*/ $ArrayCustomField['FieldName']);
        }

        //field name was specified by user, do not translate
        return $ArrayCustomField['FieldName'];
    }

    public function fieldName(): string
    {
        $name = $this->GetData('FieldName');
        //@see $GlobalCustomFieldDefs for registering global field names for translation parser
        return $this->isGlobal() ? t(/*ignore*/ $name) : $name;
    }

    public static function GetFieldTypeTranslation($ArrayCustomField)
    {
        $translations = [
            CustomFields::TYPE_SINGLE => t('Single line'),
            CustomFields::TYPE_PARAGRAPH => t('Paragraph text'),
            CustomFields::TYPE_DROPDOWN => t('Drop down'),
            CustomFields::TYPE_CHECKBOX => t('Checkboxes'),
            CustomFields::TYPE_EMAIL => t('Email address'),
            CustomFields::TYPE_NUMBER => t('Numbers'),
            CustomFields::TYPE_DECIMAL => t('Decimal'),
            CustomFields::TYPE_URL => t('URL'),
            CustomFields::TYPE_DATE => t('Date field'),
            CustomFields::TYPE_TIME => t('Time field'),
            CustomFields::TYPE_DATETIME => t('Datetime field'),
            CustomFields::TYPE_HTML => t('HTML field'),
        ];

        if (!$translations[$ArrayCustomField['FieldTypeEnum']]) {
            return $ArrayCustomField['FieldTypeEnum'];
        }

        return $translations[$ArrayCustomField['FieldTypeEnum']];
    }

    public static function GetEntitiesAsOptionsArray($UserID)
    {
        if (empty($UserID)) {
            return array();
        }

        $OptionsArray = array();

        $fields = CustomFields::RetrieveCustomFields($UserID);
        foreach ($fields as $f) {
            $OptionsArray[$f['CustomFieldID']] = CustomFields::GetFieldName($f);
        }

        return $OptionsArray;
    }

    public static function GetNameForOptionsArray($DBArray)
    {
        return CustomFields::GetFieldName($DBArray);
    }

    public static function SetNameInDBArray(&$DBArray, $NewName)
    {
        if (!$DBArray['IsGlobal']) {
            $DBArray['FieldName'] = $NewName;
        }
    }

    /**
     * import entity
     * @param int $Importer
     * @param array $ExportedEntityData
     * @param string $NewObjectPrefix
     * @return bool|\DatabaseStatementInterface|int
     */
    public static function Import($Importer, $ExportedEntityData, $NewObjectPrefix = 'Import')
    {
        if ($ExportedEntityData['IsGlobal']) {
            return $ExportedEntityData['CustomFieldID'];
        }

        $NewFieldID = parent::Import($Importer, $ExportedEntityData, $NewObjectPrefix);
        if (!$NewFieldID) {
            return false;
        }

        // write all additional data with an update query

        /** @var CustomFields $Object */
        $Object = static::FromID($Importer, $NewFieldID);
        $NewData = $Object->GetData();

        $NewData['FieldTypeEnum'] = $ExportedEntityData['FieldTypeEnum'];
        $NewData['FieldDefaultValue'] = $ExportedEntityData['FieldDefaultValue'];
        $NewData['FieldOptions'] = $ExportedEntityData['FieldOptions'];
        $NewData['FieldCopyTo'] = is_numeric(
            $ExportedEntityData['FieldCopyTo']
        ) ? '' : $ExportedEntityData['FieldCopyTo'];
        $NewData['FieldParameters'] = $ExportedEntityData['FieldParameters'];

        static::UpdateDB($NewData);

        return $NewFieldID;
    }

    ////////////////////////////////////////////////
    // old

    const REQUESTTYPE_REQUESTS = 1;
    const REQUESTTYPE_FACEBOOK = 2;

    const WIDGET_NONE = 0;
    const WIDGET_DATE_3SELECTS = 1; // OLD subscription forms TYPE_DATE
    const WIDGET_DATE_DATEPICKER = 2; // klicktipp forms TYPE_DATE
    const WIDGET_DATE_LOCALE = 3; // subscription forms TYPE_DATE
    const WIDGET_TIME_2SELECTS = 10; // klicktipp forms TYPE_TIME
    const WIDGET_TIME_LOCALE = 11; // subscription forms TYPE_TIME
    const WIDGET_TIME_SECONDSOFDAY = 12; // 0...86399 <=> 'H:i'
    const WIDGET_CHECKBOXES = 20;
    const WIDGET_TIMESTAMP = 21; // subscription forms TYPE_DATE, TYPE_TIME, TYPE_DATETIME
    const WIDGET_STRTOTIME = 22; // subscription forms TYPE_DATE, TYPE_TIME, TYPE_DATETIME
    const WIDGET_DATETIME = 30; // klicktipp forms TYPE_DATETIME
    const WIDGET_DATETIME_5SELECTS = 31; // OLD subscription forms TYPE_DATETIME
    const WIDGET_DATETIME_LOCALE = 32; // subscription forms TYPE_DATETIME
    const WIDGET_NUMBER = 33; // subscription forms TYPE_NUMBER
    const WIDGET_DECIMAL = 34; // subscription forms TYPE_DECIMAL

    /**
     * Global Custom Fields
     *
     * NOTE: length of "CustomFieldID" is limited to 15 chars in database schema (custom_field_values/RelFieldID)
     *
     * MIG Translation of FieldName
     */
    public static $GlobalCustomFieldDefs = [
        'FirstName' => array(
            'CustomFieldID' => 'FirstName',
            'FieldName' => /*t(*/'Firstname'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array(
                'FacebookName' => 'first_name',
                'RequestsName' => 'Vorname',
            ),
        ),
        'LastName' => array(
            'CustomFieldID' => 'LastName',
            'FieldName' => /*t(*/'Lastname'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array(
                'FacebookName' => 'last_name',
                'RequestsName' => 'Nachname',
            ),
        ),
        'CompanyName' => array(
            'CustomFieldID' => 'CompanyName',
            'FieldName' => /*t(*/'Company name'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array('RequestsName' => 'Firma'),
        ),
        'Street1' => array(
            'CustomFieldID' => 'Street1',
            'FieldName' => /*t(*/'Street 1'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array('RequestsName' => 'Strasse'),
        ),
        'Street2' => array(
            'CustomFieldID' => 'Street2',
            'FieldName' => /*t(*/'Street 2'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
        ),
        'City' => array(
            'CustomFieldID' => 'City',
            'FieldName' => /*t(*/'City'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array('RequestsName' => 'Stadt'),
        ),
        'State' => array(
            'CustomFieldID' => 'State',
            'FieldName' => /*t(*/'State'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
        ),
        'Zip' => array(
            'CustomFieldID' => 'Zip',
            'FieldName' => /*t(*/'Zip'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array('RequestsName' => 'PLZ'),
        ),
        'Country' => array(
            'CustomFieldID' => 'Country',
            'FieldName' => /*t(*/'Country'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array('RequestsName' => 'Land'),
        ),
        'PrivatePhone' => array(
            'CustomFieldID' => 'PrivatePhone',
            'FieldName' => /*t(*/'Private Phone'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
        ),
        'MobilePhone' => array(
            'CustomFieldID' => 'MobilePhone',
            'FieldName' => /*t(*/'Mobile Phone'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
        ),
        'Phone' => array(
            'CustomFieldID' => 'Phone',
            'FieldName' => /*t(*/'Phone'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array('RequestsName' => 'Telefon'),
        ),
        'Fax' => array(
            'CustomFieldID' => 'Fax',
            'FieldName' => /*t(*/'Fax'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array('RequestsName' => 'Fax'),
        ),
        'Website' => array(
            'CustomFieldID' => 'Website',
            'FieldName' => /*t(*/'Website'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_URL,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array('RequestsName' => 'Homepage'),
        ),
        'Birthday' => array(
            'CustomFieldID' => 'Birthday',
            'FieldName' => /*t(*/'Birthday'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_DATE,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
            'FieldParameters' => array('RequestsName' => 'Geburtstag'),
        ),
        'FullContact' => array(
            'CustomFieldID' => 'FullContact',
            'FieldName' => /*t(*/'FullContact'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_PARAGRAPH,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => true,
        ),
        //store digistore affiliates, format: JSON
        //array('vendor id 1' => 'affiliate name', 'vendor id 2' => 'affiliate name',...)
        //@see: klicktipp_subscriber_digistore_affiliates()
        //@see: \App\Klicktipp\Tracking::trackLinkClick()
        'Digit' => array(
            'CustomFieldID' => 'Digit',
            'FieldName' => /*t(*/'Digit'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_PARAGRAPH,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => true,
        ),
        // Leadvalue
        'LeadValue' => array(
            'CustomFieldID' => 'LeadValue',
            'FieldName' => /*t(*/'LeadValue'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_DECIMAL,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => false,
        ),
        'DrupalUserID' => array(
            'CustomFieldID' => 'DrupalUserID',
            'FieldName' => /*t(*/'DrupalUserID'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => true,
        ),
        'BCROCRResult' => array(
            'CustomFieldID' => 'BCROCRResult',
            'FieldName' => /*t(*/'BCROCRResult'/*)*/,
            'FieldTypeEnum' => CustomFields::TYPE_PARAGRAPH,
            'MultiValue' => 0,
            'IsGlobal' => true,
            'IsHidden' => true,
        ),
        // TYPE_ARRAY uses multiple fields (with special keys) to store the array values
        'Plugin' => array(
            'CustomFieldID' => 'Plugin',
            'FieldName' => 'Plugin',
            'FieldTypeEnum' => CustomFields::TYPE_ARRAY,
            'MultiValue' => 1,
            'IsGlobal' => true,
            'IsHidden' => true,
        ),
    ];

    // sub types for TYPE_ARRAY
    // @note: key should not exceed 5 chars due to db field limitation
    public static $GlobalCustomFieldArrayDefs = [
        // 'Link-<toolid>' is reserved for plugin link values
        'Link' => [
            'FieldTypeEnum' => CustomFields::TYPE_URL,
        ],
        // 'Image-<toolid>' is reserved for plugin image values
        'Image' => [
            'FieldTypeEnum' => CustomFields::TYPE_URL,
        ],
        // 'ExtID-<toolid>' is reserved for plugin external id values
        'ExtID' => [
            'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
        ],
        // 'IntID-<toolid>' is reserved for app-internal id values like a subscriber id
        'IntID' => [
            'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
        ],
        // 'Time-<toolid>' is reserved for plugin event time values
        'Time' => [
            'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
        ],
        // 'Dur-<toolid>' is reserved for plugin event duration values
        'Dur' => [
            'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
        ],
        // 'Val-<toolid>' is reserved for generic numeric values
        'Val' => [
            'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
        ],
        // 'sVal-<toolid>' is reserved for generic numeric values (single value)
        'sVal' => [
            'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
            'MultiValue' => 0,
        ],
        // 'Text-<toolid>' is reserved for plugin event text values
        'Text' => [
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        ],
        // 'Rate-<toolid>' is reserved for plugin event rating (decimal) values
        'Rate' => [
            'FieldTypeEnum' => CustomFields::TYPE_DECIMAL,
        ],
        // 'Sid-<toolid>' is reserved for plugin event subscriber id values
        'Sid' => [
            'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
        ],

    ];

    /**
     * Returns a single custom field matching given criterias
     *
     * @param int|string $CustomFieldID Field if to retrieve
     * @param int|null $RelOwnerUserID Owner of field. If given, ownership will be checked
     *
     * @return array|bool
     *
     */
    public static function RetrieveCustomField($CustomFieldID, $RelOwnerUserID = 0)
    {
        // global custom field?
        if (isset(CustomFields::$GlobalCustomFieldDefs[$CustomFieldID])) {
            return CustomFields::$GlobalCustomFieldDefs[$CustomFieldID];
        }

        $ArrayValues = array(':CustomFieldID' => $CustomFieldID);

        // check ownership if given
        $RelOwnerUserIDPart = '';
        if (!empty($RelOwnerUserID)) {
            $RelOwnerUserIDPart = 'AND RelOwnerUserID = :RelOwnerUserID';
            $ArrayValues[':RelOwnerUserID'] = $RelOwnerUserID;
        }

        $DBArray = db_query(
"SELECT * FROM {custom_fields} WHERE CustomFieldID = :CustomFieldID $RelOwnerUserIDPart ORDER BY CustomFieldID ASC LIMIT 0,1",
            $ArrayValues)->fetchAssoc();

        if ($DBArray) {
            $ObjectField = CustomFields::FromArray($DBArray);
            if (!empty($ObjectField)) {
                return $ObjectField->GetData();
            }
        }

        $PluginCustomFields = ToolPluginGeneral::GetCustomFields($RelOwnerUserID, 'cockpit');
        if (isset($PluginCustomFields[$CustomFieldID])) {
            return $PluginCustomFields[$CustomFieldID];
        }

        // if there are no custom fields, return false
        return false;
    }

    public static function RetrieveCustomFieldByName($RequestsType, $FieldName, $RelOwnerUserID)
    {
        $ArrayCustomFields = CustomFields::RetrieveCustomFields($RelOwnerUserID, true);
        foreach ($ArrayCustomFields as $EachField) {
            switch ($RequestsType) {
                case CustomFields::REQUESTTYPE_REQUESTS:
                    if (!empty($EachField['FieldParameters']['RequestsName'])) {
                        if ($EachField['FieldParameters']['RequestsName'] == $FieldName) {
                            return $EachField;
                        }
                    }
                    break;
                case CustomFields::REQUESTTYPE_FACEBOOK:
                    if (!empty($EachField['FieldParameters']['FacebookName'])) {
                        if ($EachField['FieldParameters']['FacebookName'] == $FieldName) {
                            return $EachField;
                        }
                    }
                    break;
            }
        }

        // not found
        return false;
    }

    /**
     * Returns all custom fields matching given criterias
     *
     * @param int $RelOwnerUserID Owner of fields to retrieve
     * @return array
     *
     **/
    public static function RetrieveCustomFields(
        $RelOwnerUserID,
        $unpack = false,
        $ArrayOrder = array('CustomFieldID' => 'ASC'),
        $includeHidden = false,
        $includePlugins = false
    ) {
        // global custom fields
        $AllCustomFields = CustomFields::$GlobalCustomFieldDefs;

        foreach ($AllCustomFields as $key => $field) {
            if (!$includeHidden && $field['IsHidden']) {
                // remove hidden fields from global custom fields
                unset($AllCustomFields[$key]);
            }
        }

        // plugin custom fields
        if ($includePlugins) {
            $AllCustomFields = array_merge(
                $AllCustomFields,
                ToolPluginGeneral::GetCustomFields($RelOwnerUserID, 'cockpit')
            );
        }

        // user custom fields
        $ArrayFields = array('*');
        $ArrayFromTables = array('{custom_fields}');
        $ArrayCriterias = array('RelOwnerUserID' => $RelOwnerUserID);

        $ArrayCustomFields = kt_get_rows($ArrayFields, $ArrayFromTables, $ArrayCriterias, $ArrayOrder ?? []);

        if ($unpack) {
            foreach ($ArrayCustomFields as $ArrayCustomField) {
                if (!empty($ArrayCustomField['FieldParameters']) && !$ArrayCustomField['IsGlobal']) {
                    //unserialize CustomField FieldParameters, GlobalFields FieldParameters are not serialized
                    $ArrayCustomField['FieldParameters'] = unserialize($ArrayCustomField['FieldParameters']);
                }

                // make it addressable by fieldid as string
                $AllCustomFields["{$ArrayCustomField['CustomFieldID']}"] = $ArrayCustomField;
            }
        } else {
            $AllCustomFields = array_merge($AllCustomFields, $ArrayCustomFields);
        }

        return $AllCustomFields;
    }

    /**
     * @param int $uid
     * @param array<int> $allowedTypes
     * @param int|null $multiValue
     * @return array<string, array<string, mixed>>
     */
    public static function retrieveCustomFieldsFilteredByType(
        int $uid,
        array $allowedTypes,
        ?int $multiValue = null
    ): array {
        // allowed values for $multivalue are 0, 1 or null
        // 0: fields must be single value
        // 1: fields must be multi value
        // null: no filter
        if (!is_null($multiValue) && $multiValue !== 0 && $multiValue !== 1) {
            $multiValue = null;
        }
        $filteredCustomFields = [];
        $CustomFields = CustomFields::RetrieveCustomFields($uid, true);
        foreach ($CustomFields as $index => $Each) {
            if (in_array($Each['FieldTypeEnum'], $allowedTypes)) {
                if (is_null($multiValue)) {
                    $filteredCustomFields[$index] = $Each;
                } else {
                    if ($Each['MultiValue'] == $multiValue) {
                        $filteredCustomFields[$index] = $Each;
                    }
                }
            }
        }

        return $filteredCustomFields;
    }

    public static function RetrieveCustomFieldsCategories(
        $RelOwnerUserID,
        $ArrayOrder = array('CustomFieldID' => 'ASC'),
        $includeHidden = false
    ) {
        $ArrayCategories = array(
            'AllCustomFields' => array(),
            'CategoryNames' => array(),
            'CategoryFields' => array(
                'general' => array(),
            ),
        );

        $AllCustomFields = CustomFields::RetrieveCustomFields($RelOwnerUserID, true, $ArrayOrder, $includeHidden);

        $categories = array();
        foreach ($AllCustomFields as $field) {
            $ArrayCategories['AllCustomFields'][$field['CustomFieldID']] = $field;

            $cat = $field['FieldParameters']['FieldCategory'] ?? null;

            if (empty($cat)) {
                $ArrayCategories['CategoryFields']['general'][$field['CustomFieldID']] = $field;
            } else {
                $categories[$cat] = $cat;

                if (!isset($ArrayCategories['CategoryFields'][$cat])) {
                    $ArrayCategories['CategoryFields'][$cat] = array();
                }

                $ArrayCategories['CategoryFields'][$cat][$field['CustomFieldID']] = $field;
            }
        }

        $ArrayCategories['CategoryNames'] = $categories;

        return $ArrayCategories;
    }

    /**
     * Returns custom field options as an array
     *
     * <code>
     * <?php
     * $ArrayCustomFieldOptions = CustomFields::GetOptionsAsArray('[[Turkey]||[tr]]*,,,[[Germany]||[de]]');
     * ?>
     * </code>
     *
     * @param string $CustomFieldOptions Value of FieldOptions field of a custom field
     * @return array
     *
     */
    public static function GetOptionsAsArray($CustomFieldOptions)
    {
        $ReturnArrayCustomFieldOption = array();
        foreach (explode(',,,', $CustomFieldOptions) as $EachOption) {
            $EachOption = str_replace('[[', '', $EachOption);
            $IsSelected = 'false';

            if (substr($EachOption, strlen($EachOption) - 1, strlen($EachOption)) == '*') {
                $IsSelected = 'true';
                $EachOption = str_replace(']]*', '', $EachOption);
            } else {
                $EachOption = str_replace(']]', '', $EachOption);
            }
            $ArrayEachOption = explode(']||[', $EachOption);
            $ReturnArrayCustomFieldOption[] = array(
                'label' => $ArrayEachOption[0],
                'value' => str_replace(array('\\', '\''), array(
                    '\\\\',
                    '\\\''
                ), $ArrayEachOption[1]),
                'is_selected' => $IsSelected,
            );
        }
        return $ReturnArrayCustomFieldOption;
    }

    /**
     * Returns custom field option as a formatted string
     *
     * <code>
     * <?php
     * $ArrayCustomFieldOptionString = CustomFields::GetOptionAsString('Turkey','tr',true);
     * ?>
     * </code>
     *
     * @param string $OptionLabel Label of an option
     * @param string $OptionValue Value of an option
     * @param boolean $IsSelected Determines if the option is selected or not
     * @return string
     *
     */
    public static function GetOptionAsString($OptionLabel, $OptionValue, $IsSelected)
    {
        return '[[' . $OptionLabel . ']||[' . $OptionValue . ']]' . ($IsSelected == true ? '*' : '');
    }

    public static function GetSelectedOptionsString($OptionsString, $SelectedOptions)
    {
        $NewOptionsString = '';

        $Data = array();
        $tmp_options = explode(',,,', $OptionsString);
        $tmp_counter = 0;
        foreach ($tmp_options as $each_option) {
            $tmp_option = explode(']||[', $each_option);
            $Data['OptionLabel'][$tmp_counter] = trim($tmp_option[0], '[');
            $Data['OptionValue'][$tmp_counter] = trim($tmp_option[1], ']');
            $tmp_counter++;
        }
        $Data['OptionSelected'] = explode(',', $SelectedOptions);
        foreach ($Data['OptionLabel'] as $Key => $Value) {
            $IsSelected = (in_array($Key, $Data['OptionSelected']) ? true : false);
            $NewOptionsString .= CustomFields::GetOptionAsString($Value, $Data['OptionValue'][$Key], $IsSelected);
            $NewOptionsString .= ',,,';
        } // foreach
        $NewOptionsString = substr($NewOptionsString, 0, strlen($NewOptionsString) - 3);

        return $NewOptionsString;
    }

    /**
     * Creates a new custom field with given values
     *
     * @param array $ArrayFieldAndValues Values of new custom field
     * @return boolean|integer ID of new custom field
     *
     **/
    public static function Create($ArrayFieldAndValues)
    {
        // Check required values - Start
        foreach (CustomFields::$RequiredFieldsOnInsert as $EachField) {
            if (empty($ArrayFieldAndValues[$EachField])) {
                return false;
            }
        }
        // Check required values - End

        // Check if any value is missing. if yes, give default values - Start
        foreach (CustomFields::$DefaultFields as $EachField => $EachValue) {
            if (!isset($ArrayFieldAndValues[$EachField])) {
                $ArrayFieldAndValues[$EachField] = $EachValue;
            }
        }
        // Check if any value is missing. if yes, give default values - End

        // avoid PDO due to long field names
        $ArrayFieldAndValues['FieldName'] = substr($ArrayFieldAndValues['FieldName'], 0, 250);

        //Check if a field with the same name already exists for the current user
        $already_exists = db_query(
            "SELECT COUNT(*) FROM {custom_fields} WHERE RelOwnerUserID = :RelOwnerUserID AND FieldName = :FieldName",
            array(
                ':RelOwnerUserID' => $ArrayFieldAndValues['RelOwnerUserID'],
                ':FieldName' => $ArrayFieldAndValues['FieldName']
            )
        )->fetchField();
        if ($already_exists > 0) {
            return false;
        }

        //prepare parameters for database
        if (!empty($ArrayFieldAndValues['FieldParameters'])) {
            $ArrayFieldAndValues['FieldParameters'] = serialize($ArrayFieldAndValues['FieldParameters']);
        } else {
            $ArrayFieldAndValues['FieldParameters'] = '';
        }

        //extract the labels first, otherwise kt_insert_row is looking for the MetaLabels column in the database
        $metaLabels = static::ExtractLabels($ArrayFieldAndValues);

        // Create a new record in custom_fields table - Start
        $NewCustomFieldID = kt_insert_row($ArrayFieldAndValues, '{custom_fields}', 'CustomFieldID');
        // Create a new record in custom_fields table - End

        if (!empty($NewCustomFieldID)) {
            //update named table and add labels if insert was successful

            DatabaseNamedTable::UpdateName(
                $ArrayFieldAndValues['RelOwnerUserID'],
                static::$EntityBaseType,
                $ArrayFieldAndValues['FieldTypeEnum'],
                $NewCustomFieldID,
                $ArrayFieldAndValues['FieldName']
            );

            if (!empty($metaLabels)) {
                MetaLabels::AddLabelList(
                    $ArrayFieldAndValues['RelOwnerUserID'],
                    $metaLabels,
                    static::$EntityBaseType,
                    $ArrayFieldAndValues['FieldTypeEnum'],
                    $NewCustomFieldID
                );
            }
        }

        return $NewCustomFieldID;
    }

    /**
     * @param $FieldInformation
     * @param $Value
     *
     * @return bool indicates if subscriber state changed
     */
    public static function isEmpty($FieldInformation, $Value)
    {
        switch ($FieldInformation['FieldTypeEnum']) {
            case CustomFields::TYPE_SINGLE:
            case CustomFields::TYPE_PARAGRAPH:
            case CustomFields::TYPE_NUMBER:
            case CustomFields::TYPE_DECIMAL:
            case CustomFields::TYPE_ARRAY:
                //0 is not empty
                return ($Value === "" || !isset($Value));
                break;
            default:
                return (empty($Value));
                break;
        }
    }

    /**
     * Write Custom Field Data
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $FieldInformation
     * @param $Value
     *
     * @return bool indicates if subscriber state changed
     */
    public static function UpdateCustomFieldData($UserID, $SubscriberID, $ReferenceID, $FieldInformation, $Value): bool
    {
        $subscriberChanged = false;
        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_ARRAY) {
            $FieldInformation['FieldTypeEnum'] = CustomFields::TYPE_PARAGRAPH;
            foreach ($Value as $entitykey => $entityvalue) {
                $FieldInformation['CustomFieldID'] = $entitykey;
                $subscriberChanged = CustomFields::UpdateCustomFieldData(
                    $UserID,
                    $SubscriberID,
                    $ReferenceID,
                    $FieldInformation,
                    $entityvalue
                ) || $subscriberChanged;
            }
            return $subscriberChanged;
        }

        if (!CustomFields::isEmpty($FieldInformation, $Value)) {
            $Value = kt_strip_utf8mb4($Value);
        };

        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_HTML) {
            // revert previous check_plain()
            $Value = decode_entities($Value);
            // base64 encode html markup
            $Value = base64_encode($Value);
        }

        kt_begin_transaction();

        // single value fields have all references, but need one place to be stored (ref 0)
        $IntReferenceID = empty($FieldInformation['MultiValue']) ? 0 : $ReferenceID;

        // get id of existing value
        $StoredValue = db_query(
            "SELECT ValueID, ValueText FROM {custom_field_values}" .
            " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID = :RelFieldID" .
            " AND ReferenceID = :ReferenceID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelSubscriberID' => $SubscriberID,
                ':RelFieldID' => $FieldInformation['CustomFieldID'],
                ':ReferenceID' => $IntReferenceID,
            )
        )->fetchAssoc();

        if (empty($StoredValue)) {
            if (!CustomFields::isEmpty($FieldInformation, $Value)) {
                $subscriberChanged = db_query(
                    'INSERT IGNORE INTO {custom_field_values} ' .
                    '(RelOwnerUserID, RelFieldID, RelSubscriberID, ReferenceID, ValueText, SubscriptionDate) ' .
                    'VALUES ' .
                    '(:RelOwnerUserID, :RelFieldID, :RelSubscriberID, :ReferenceID, :ValueText, :SubscriptionDate)',
                    array(
                        ':RelOwnerUserID' => $UserID,
                        ':RelFieldID' => $FieldInformation['CustomFieldID'],
                        ':RelSubscriberID' => $SubscriberID,
                        ':ReferenceID' => $IntReferenceID,
                        ':ValueText' => $Value,
                        ':SubscriptionDate' => time(),
                    )
                )->rowCount() > 0;

                // ensure there is a subscription reference for this value
                Subscriber::addReference($UserID, $SubscriberID, $ReferenceID);
            }
        } elseif (CustomFields::isEmpty($FieldInformation, $Value)) {
            $subscriberChanged = db_query(
                "DELETE FROM {custom_field_values} WHERE ValueID = :ValueID AND RelOwnerUserID = :RelOwnerUserID",
                array(':RelOwnerUserID' => $UserID, ':ValueID' => $StoredValue['ValueID'])
            )->rowCount() > 0;
        } else {
            if ($StoredValue['ValueText'] != $Value) {
                $subscriberChanged = db_query(
                    "UPDATE {custom_field_values} SET ValueText = :ValueText, SubscriptionDate = :SubscriptionDate " .
                    " WHERE ValueID = :ValueID AND RelOwnerUserID = :RelOwnerUserID",
                    array(
                        ':RelOwnerUserID' => $UserID,
                        ':ValueID' => $StoredValue['ValueID'],
                        ':ValueText' => $Value,
                        ':SubscriptionDate' => time(),
                    )
                )->rowCount() > 0;
            }

            // ensure there is a subscription reference for this value
            // @note: this is needed for insert only, but single value fields store only one value,
            // so most reference inserts are updates on that value
            if (empty($FieldInformation['MultiValue'])) {
                Subscriber::addReference($UserID, $SubscriberID, $ReferenceID);
            }
        }

        // FieldCopyTo
        // @note: this is an insert only, as a copy is there for the reason to be processed (so an update may be counterproductive)
        if (!empty($FieldInformation['FieldCopyTo']) && !CustomFields::isEmpty($FieldInformation, $Value)) {
            $FieldInformationTarget = CustomFields::RetrieveCustomField($FieldInformation['FieldCopyTo'], $UserID);
            $isSourceFieldMultiValue = !empty($FieldInformation['MultiValue']);
            $isTargetFieldMultiValue = !empty($FieldInformationTarget['MultiValue']);

            if ($isTargetFieldMultiValue) {
                if ($isSourceFieldMultiValue) {
                    // cases covered:
                    // multi value --> multi value

                    $TargetReferenceIDs = [$ReferenceID];
                } else {
                    // cases covered:
                    // single value --> multi value

                    $TargetReferenceIDs = Reference::getReferenceIDsOfUser($UserID);
                }
            } else {
                // cases covered:
                // multi value --> single value
                // single value --> single value
                $TargetReferenceIDs = [0];
            }

            foreach ($TargetReferenceIDs as $TargetReferenceID) {
                $subscriberChanged = db_query(
                    'INSERT IGNORE INTO {custom_field_values} ' .
                    '(RelOwnerUserID, RelFieldID, RelSubscriberID, ReferenceID, ValueText, SubscriptionDate) ' .
                    'VALUES ' .
                    '(:RelOwnerUserID, :RelFieldID, :RelSubscriberID, :ReferenceID, :ValueText, :SubscriptionDate)',
                    array(
                        ':RelOwnerUserID' => $UserID,
                        ':RelFieldID' => $FieldInformation['FieldCopyTo'],
                        ':RelSubscriberID' => $SubscriberID,
                        ':ReferenceID' => $TargetReferenceID,
                        ':ValueText' => $Value,
                        ':SubscriptionDate' => time(),
                    )
                )->rowCount() > 0 || $subscriberChanged;

                // reset cache
                CustomFields::GetCustomFieldData($UserID, $SubscriberID, [], $TargetReferenceID, '', true);
            }
        }

        kt_commit_transaction();

        // reset cache
        CustomFields::GetCustomFieldData($UserID, $SubscriberID, [], $ReferenceID, '', true);

        return $subscriberChanged;
    }

    /**
     * Read Custom Field Data
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $FieldInformation
     * @param $ReferenceID
     * @param string $ArrayFieldID
     * @param bool $reset
     *
     * @return false|mixed|string
     */
    public static function GetCustomFieldData(
        $UserID,
        $SubscriberID,
        $FieldInformation,
        $ReferenceID,
        $ArrayFieldID = '',
        $reset = false
    ) {
        static $CustomFieldData = [];

        // reset for this subscriber only
        if ($reset) {
            if (empty($SubscriberID)) {
                // reset all
                $CustomFieldData = [];
            } else {
                // reset subscriber
                unset($CustomFieldData[$SubscriberID]);
            }
            return '';
        }

        // get and cache all data from one subscriber
        if (!isset($CustomFieldData[$SubscriberID])) {
            $CustomFieldData[$SubscriberID] = array();

            // get all data at once
            $result = db_query(
                "SELECT RelFieldID, ValueText, ReferenceID FROM {custom_field_values} " .
                " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID ",
                array(
                    ':RelOwnerUserID' => $UserID,
                    ':RelSubscriberID' => $SubscriberID,
                )
            );
            while ($cfd = kt_fetch_array($result)) {
                $CustomFieldData[$SubscriberID][$cfd['RelFieldID']][$cfd['ReferenceID']] = $cfd['ValueText'];
            }
        }

        // single value fields have all references, but are stored in one place (ref 0)
        $IntReferenceID = empty($FieldInformation['MultiValue']) ? 0 : $ReferenceID;
        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_ARRAY) {
            $Value = $CustomFieldData[$SubscriberID][$ArrayFieldID][$IntReferenceID] ?? null;
        } else {
            $Value = $CustomFieldData[$SubscriberID][$FieldInformation['CustomFieldID']][$IntReferenceID] ?? null;
        }
        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_HTML) {
            $Value = base64_decode($Value);
        }

        if (CustomFields::isEmpty($FieldInformation, $Value)) {
            return '';
        }
        return $Value;
    }

    /**
     * Note: email address values are not saved punycoded, in order to be able
     * to apply substring searches (punycode format doesn't support this)
     *
     * @param array $FieldInformation
     * @param mixed $value
     *
     * @return array|false|mixed|string
     */
    public static function FormatCustomFieldData($FieldInformation, $value)
    {
        if (empty($value)) {
            return '';
        }
        if ($FieldInformation['CustomFieldID'] == "FullContact") {
            // special case to format FullContact field
            // note that FieldTypeEnum is paragraph
            $value = (empty($value)) ? [] : drupal_json_decode($value);
            return CustomFields::FormatFullContactData($value);
        }
        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
            return Dates::formatDate(Dates::FORMAT_DMY, (int) $value);
        }
        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_TIME) {
            return CustomFields::ConvertCustomFieldDataToWidget($value, CustomFields::WIDGET_TIME_SECONDSOFDAY);
        }
        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
            return Dates::formatDate(Dates::FORMAT_DMYHI, (int) $value);
        }
        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_DROPDOWN) {
            $ArrayCustomFieldChoices = CustomFields::GetOptionsAsArray($FieldInformation['FieldOptions']);

            foreach ($ArrayCustomFieldChoices as $ArrayEachChoice) {
                if ($value == $ArrayEachChoice['value']) {
                    return htmlspecialchars($ArrayEachChoice['label']);
                }
            }

            return $value; // value not found in choices
        }
        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_CHECKBOX) {
            $ArrayCustomFieldChoices = CustomFields::GetOptionsAsArray($FieldInformation['FieldOptions']);

            $result = array();
            foreach ($ArrayCustomFieldChoices as $ArrayEachChoice) {
                if (in_array($ArrayEachChoice['value'], $value)) {
                    $result[] = htmlspecialchars($ArrayEachChoice['label']);
                }
            }

            return implode(", ", $result);
        }
        if ($FieldInformation['FieldTypeEnum'] == CustomFields::TYPE_DECIMAL) {
            return klicktipp_number_format($value / 100, 2);
        }

        return $value;
    }

    /**
     * @param $FullContactData
     * @return string
     */
    public static function FormatFullContactData($FullContactField)
    {
        // convert the old person v2 api to person enrich api structure
        $FullContactField = CustomFields::ConvertFullContactData($FullContactField);

        $FullContactData = $FullContactField['data'];

        if (empty($FullContactData)) {
            return t("No FullContact data available for subscriber");
        }

        $html = "";
        if (!empty($FullContactData['details']['photos'][0]['value'])) {
            $url = $FullContactData['details']['photos'][0]['value'];
            $html .= "<img src='$url' height='200' alt='' />";
        }
        $html .= "<div>";
        if (!empty($FullContactData['details']['name']['given'])) {
            $html .= t("FirstName") . ": {$FullContactData['details']['name']['given']}";
            $html .= "<br />";
        }
        if (!empty($FullContactData['details']['name']['family'])) {
            $html .= t("LastName") . ": {$FullContactData['details']['name']['family']}";
            $html .= "<br />";
        }
        if (!empty($FullContactData['details']['age']['value'])) {
            $html .= t("Age") . ": {$FullContactData['details']['age']['value']}";
            $html .= "<br />";
        }
        if (!empty($FullContactData['details']['gender'])) {
            $html .= t("Gender") . ": {$FullContactData['details']['gender']}";
            $html .= "<br />";
        }
        if (!empty($FullContactData['details']['locations'][0]['city'])) {
            $html .= t("City") . ": {$FullContactData['details']['locations'][0]['city']}";
            $html .= "<br />";
        }
        if (!empty($FullContactData['details']['locations'][0]['region'])) {
            $html .= t("State") . ": {$FullContactData['details']['locations'][0]['region']}";
            $html .= "<br />";
        }
        if (!empty($FullContactData['details']['locations'][0]['country'])) {
            $html .= t("Country") . ": {$FullContactData['details']['locations'][0]['country']}";
            $html .= "<br />";
        }
        if (!empty($FullContactData['details']['urls'])) {
            foreach ($FullContactData['details']['urls'] as $website) {
                $html .= t("Website") . ": {$website['value']}";
                $html .= "<br />";
            }
        }
        if (!empty($FullContactData['details']['profiles'])) {
            foreach ($FullContactData['details']['profiles'] as $profile) {
                $html .= "{$profile['service']}: {$profile['url']}";
                $html .= "<br />";
            }
        }
        if (!empty($FullContactData['details']['employment'])) {
            foreach ($FullContactData['details']['employment'] as $organization) {
                $startDate = "";
                if (!empty($organization['start'])) {
                    // start is provided only through higher plans at full contact, it is called "Employment History"

                    $start = $organization['start'];

                    $day = !empty($start['day']) ? $start['day'] : "";
                    $month = !empty($start['month']) ? $start['month'] : "";
                    $year = !empty($start['year']) ? $start['year'] : "";

                    if (!empty($day) && !empty($month) && !empty($year)) {
                        $startDate = $day . "." . $month . "." . $year;
                    } elseif (!empty($month) && !empty($year)) {
                        $startDate = $month . "." . $year;
                    } elseif (!empty($year)) {
                        $startDate = $year;
                    }
                }

                $endDate = "";
                if (!empty($organization['end'])) {
                    // end is provided only through higher plans at full contact, it is called "Employment History"

                    $end = $organization['end'];

                    $day = !empty($end['day']) ? $end['day'] : "";
                    $month = !empty($end['month']) ? $end['month'] : "";
                    $year = !empty($end['year']) ? $end['year'] : "";

                    if (!empty($day) && !empty($month) && !empty($year)) {
                        $endDate = $day . "." . $month . "." . $year;
                    } elseif (!empty($month) && !empty($year)) {
                        $endDate = $month . "." . $year;
                    } elseif (!empty($year)) {
                        $endDate = $year;
                    }
                }

                $html .= "{$organization['name']}: {$organization['title']}";

                if (!empty($startDate) && !empty($endDate)) {
                    $html .= " ($startDate - $endDate)";
                } elseif (!empty($startDate)) {
                    $html .= " ($startDate)";
                } elseif (!empty($endDate)) {
                    $html .= " ($endDate)";
                }

                $html .= "<br />";
            }
        }
        $html .= "</div>";
        return $html;
    }

    /**
     * This function will convert from v2 to enrich api version.
     *
     * Thereby only the fields used in our application will be converted. Because we are only
     * converting on the fly, we will have the old data for later adjustment still stored.
     *
     * Fields from old api v2 used in new version (enrich) are:
     *
     *  photos.[0].url
     *  contactInfo.givenName
     *  contactInfo.familyName
     *  contactInfo.websites[n].url
     *  demographics.age
     *  demographics.gender
     *  demographics.locationDeduced.city.name
     *  demographics.locationDeduced.state.name
     *  demographics.locationDeduced.country.name
     *  demographics.locationDeduced.country.code
     *  socialProfiles.[n].typeName
     *  socialProfiles.[n].typeId
     *  socialProfiles.[n].url
     *  organisations[n].name
     *  organisations[n].title
     *  organisations[n].startDate
     *  organisations[n].endDate
     *
     * @param $FullContactField array
     * @return array
     */
    public static function ConvertFullContactData($FullContactField)
    {
        $EnrichApiStructure = array();

        if (!is_array($FullContactField) || empty($FullContactField['data'])) {
            return array();
        }

        $FullContactData = $FullContactField['data'];

        if (!empty($FullContactField['apiVersion']) && $FullContactField['apiVersion'] == 3) {
            // we got the new structure already

            $EnrichApiStructure = $FullContactData;
        } else {
            if (!empty($FullContactData['photos'][0]['url'])) {
                $EnrichApiStructure['details']['photos'][0]['value'] = $FullContactData['photos'][0]['url'];
            }

            if (!empty($FullContactData['contactInfo']['givenName'])) {
                $EnrichApiStructure['details']['name']['given'] = $FullContactData['contactInfo']['givenName'];
            }

            if (!empty($FullContactData['contactInfo']['familyName'])) {
                $EnrichApiStructure['details']['name']['family'] = $FullContactData['contactInfo']['familyName'];
            }

            if (
                !empty($FullContactData['contactInfo']['websites']) && is_array(
                    $FullContactData['contactInfo']['websites']
                )
            ) {
                foreach ($FullContactData['contactInfo']['websites'] as $Website) {
                    $EnrichApiStructure['details']['urls'][] = array('value' => $Website['url']);
                }
            }

            if (!empty($FullContactData['demographics']['age'])) {
                $EnrichApiStructure['details']['age']['value'] = $FullContactData['demographics']['age'];
            }

            if (!empty($FullContactData['demographics']['gender'])) {
                $EnrichApiStructure['details']['gender'] = $FullContactData['demographics']['gender'];
            }

            if (!empty($FullContactData['demographics']['locationDeduced']['city']['name'])) {
                $EnrichApiStructure['details']['locations'][0]['city'] = $FullContactData['demographics']['locationDeduced']['city']['name'];
            }

            if (!empty($FullContactData['demographics']['locationDeduced']['state']['name'])) {
                $EnrichApiStructure['details']['locations'][0]['region'] = $FullContactData['demographics']['locationDeduced']['state']['name'];
            }

            if (!empty($FullContactData['demographics']['locationDeduced']['country']['name'])) {
                $EnrichApiStructure['details']['locations'][0]['country'] = $FullContactData['demographics']['locationDeduced']['country']['name'];
            }

            if (!empty($FullContactData['demographics']['locationDeduced']['country']['code'])) {
                $EnrichApiStructure['details']['locations'][0]['countryCode'] = $FullContactData['demographics']['locationDeduced']['country']['code'];
            }

            if (!empty($FullContactData['socialProfiles']) && is_array($FullContactData['socialProfiles'])) {
                foreach ($FullContactData['socialProfiles'] as $SocialProfile) {
                    if (!empty($SocialProfile['typeId']) && !empty($SocialProfile['url'])) {
                        $EnrichApiStructure['details']['profiles'][$SocialProfile['typeId']] = array(
                            'url' => $SocialProfile['url'],
                            'service' => $SocialProfile['typeId'],
                        );
                    }
                }
            }

            if (!empty($FullContactData['organizations']) && is_array($FullContactData['organizations'])) {
                $EnrichApiStructure['details']['employment'] = array();
                foreach ($FullContactData['organizations'] as $Organization) {
                    $employment = array();

                    if (!empty($Organization['name'])) {
                        $employment['name'] = $Organization['name'];
                    }

                    if (!empty($Organization['title'])) {
                        $employment['title'] = $Organization['title'];
                    }

                    if (isset($Organization['current'])) {
                        $employment['current'] = $Organization['current'];
                    }

                    // start and end is provided only through higher plans at full contact, it is called "Employment History"
                    if (!empty($Organization['startDate'])) {
                        $start = CustomFields::ConvertDateIntoFullContactStructure($Organization['startDate']);
                        if (!empty($start)) {
                            $employment['start'] = $start;
                        }
                    }
                    if (!empty($Organization['endDate'])) {
                        $end = CustomFields::ConvertDateIntoFullContactStructure($Organization['endDate']);
                        if (!empty($end)) {
                            $employment['end'] = $end;
                        }
                    }

                    $EnrichApiStructure['details']['employment'][] = $employment;
                }
            }
        }

        $FullContactField['data'] = $EnrichApiStructure;

        return $FullContactField;
    }

    protected static function ConvertDateIntoFullContactStructure($date)
    {
        $convertedDate = array();

        if (DateTime::createFromFormat('!Y', $date)) {
            $dateYear = $date;
        } elseif ($dateTime = DateTime::createFromFormat('!Y-m', $date)) {
            $dateYear = $dateTime->format('Y');
            $dateMonth = $dateTime->format('n');
        } elseif ($dateTime = DateTime::createFromFormat('!Y-m-d', $date)) {
            $dateYear = $dateTime->format('Y');
            $dateMonth = $dateTime->format('n');
            $dateDay = $dateTime->format('j');
        }

        if (isset($dateYear)) {
            $convertedDate['year'] = $dateYear;
        }
        if (isset($dateMonth)) {
            $convertedDate['month'] = $dateMonth;
        }
        if (isset($dateDay)) {
            $convertedDate['day'] = $dateDay;
        }

        return $convertedDate;
    }

    public static function ConvertCustomFieldDataFromWidget($Value, $Widget = CustomFields::WIDGET_NONE)
    {
        switch ($Widget) {
            case CustomFields::WIDGET_DATE_3SELECTS:
                // date widget: consists of 3 select fields for day, month and year, input format: array
                $Value = strtotime(implode('.', $Value));
                break;
            case CustomFields::WIDGET_DATE_DATEPICKER:
                // date widget: consists of textfield with javascript widget, input format: date string
                //convert the date string provided by the datepicker to a timestamp
                $Value = Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $Value);
                break;
            case CustomFields::WIDGET_DATE_LOCALE:
                // date widget: consists of textfield, input format: locale date string
                $Value = Core::DateFormatToTimestamp(Dates::FORMAT_DMY, $Value);
                break;
            case CustomFields::WIDGET_TIME_2SELECTS:
                // time widget: consists of 2 select fields for hours and minutes, input format: array
                $Value = strtotime(
                    '1970-01-01 ' . implode(':', $Value) . ':00 GMT'
                ); // example "1970-01-01 12:00:00 GMT" => 43200 (seconds of the day)
                break;
            case CustomFields::WIDGET_TIME_SECONDSOFDAY:
                // time widget: string "H:i"
                $Value = strtotime(
                    "1970-01-01 $Value:00 GMT"
                ); // example "1970-01-01 12:00:00 GMT" => 43200 (seconds of the day)
                break;
            case CustomFields::WIDGET_TIME_LOCALE:
                // time widget: consists of textfield, input format: locale time string, e.g. 14:42:00, or 2:42pm
                $Value = strtotime(
                    '1970-01-01 ' . $Value . ' GMT'
                ); // example "1970-01-01 12:00:00 GMT" => 43200 (seconds of the day)
                break;
            case CustomFields::WIDGET_CHECKBOXES:
                // checkboxes: array of the selected checkboxes
                $Value = implode('||||', $Value);
                break;
            case CustomFields::WIDGET_TIMESTAMP:
                // the widget consists of a textfield, input format: int (timestamp)
                $Value = (is_numeric($Value) && (int)$Value == $Value) ? $Value : '';
                break;
            case CustomFields::WIDGET_STRTOTIME:
                // the widget consists of a textfield, input format: string (php strtotime format)
                $Value = strtotime($Value);
                break;
            case CustomFields::WIDGET_DATETIME:
                //the datetime widget consists of a textfield for the date (datepicker) and 2 dropdowns for hours and minutes
                $Datetimestamp = Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $Value['date']);
                $hours = empty($Value['hours']) ? '00' : $Value['hours'];
                $minutes = empty($Value['minutes']) ? '00' : $Value['minutes'];
                $Value = strtotime(date("Y-m-d {$hours}:{$minutes}:00", $Datetimestamp));
                break;
            case CustomFields::WIDGET_DATETIME_5SELECTS:
                if ($Value[3] < 0) {
                    // php 8 allows negative values for hours, it could disturb our time calculation in campaigns
                    return false;
                }
                //the datetime widget consists of a textfield for the date (datepicker) and 2 dropdowns for hours and minutes
                $Value = strtotime("{$Value[2]}-{$Value[1]}-{$Value[0]} {$Value[3]}:{$Value[4]}:00");
                break;
            case CustomFields::WIDGET_DATETIME_LOCALE:
                // datetime widget: consists of textfield, input format: locale datetime string
                $Value = Core::DateFormatToTimestamp(Dates::FORMAT_DMYHIS, $Value);
                break;
            case CustomFields::WIDGET_DECIMAL:
                // the widget consists of a textfield, input format: -99,99 (, or . by settings)
                // we only allow 2 fractional digits and store the value as int in the DB (multiply by 100)
                $Value = (empty($Value)) ? 0 : intval(UtilsNumber::formatStringToFloat((string)$Value) * 100);
                break;
        }

        return $Value;
    }

    public static function ConvertCustomFieldDataToWidget($Value, $Widget = CustomFields::WIDGET_NONE)
    {
        switch ($Widget) {
            case CustomFields::WIDGET_DATE_3SELECTS:
                // date widget: consists of 3 select fields for day, month and year, input format: timestamp
                if (is_numeric($Value)) {
                    $Value = (int)$Value;
                    $Value = array(
                        date('d', $Value),
                        date('m', $Value),
                        date('Y', $Value),
                    );
                } else {
                    $Value = array(false, false, false);
                }
                break;
            case CustomFields::WIDGET_DATE_DATEPICKER:
                 $Value = is_numeric($Value) ?
                     date(variable_get(Dates::FORMAT_DMY_DATEPICKER_PHP, "d.m.Y"), (int)$Value) :
                     false;
                break;
            case CustomFields::WIDGET_DATE_LOCALE:
                // date widget: consists of textfield, input format: locale date string
                $Value = Dates::formatDate(Dates::FORMAT_DMY, (int) $Value);
                break;
            case CustomFields::WIDGET_TIME_2SELECTS:
                // time widget: consists of 2 select fields for hours and minutes,
                // input format: timestamp (seconds of the day < 86400)
                $Value = (int)$Value;
                $Value = array(
                    floor($Value / 3600),
                    floor(($Value % 3600) / 60),
                );
                break;
            case CustomFields::WIDGET_TIME_SECONDSOFDAY:
                // date widget: 'H:i', input format: timestamp (seconds of the day < 86400)
                $Value = (int)$Value;
                $Value = sprintf("%02d:%02d", floor($Value / 3600), floor($Value % 3600 / 60));
                break;
            case CustomFields::WIDGET_TIME_LOCALE:
                // time widget: consists of textfield, input format: locale time string
                $Value = Dates::formatDate(Dates::FORMAT_HIS, (int) $Value);
                break;
            case CustomFields::WIDGET_CHECKBOXES:
                // checkboxes: array of the selected checkboxes
                $Value = explode('||||', $Value);
                break;
            case CustomFields::WIDGET_DATETIME:
                // the datetime widget consists of a textfield for the date (datepicker)
                // and 2 dropdowns for hours and minutes
                if (is_numeric($Value)) {
                    $Value = (int)$Value;
                    $Value = array(
                        'date' => date(variable_get(Dates::FORMAT_DMY_DATEPICKER_PHP, "d.m.Y"), $Value),
                        'hours' => date("H", $Value),
                        'minutes' => date("i", $Value),
                    );
                } else {
                    $Value = array('date' => false, 'hours' => false, 'minutes' => false);
                }
                break;
            case CustomFields::WIDGET_DATETIME_5SELECTS:
                //the datetime widget consists of 5 dropdowns (day, month, year, hours, minutes)
                if (is_numeric($Value)) {
                    $Value = (int)$Value;
                    $Value = array(
                        date('d', $Value), //day
                        date('m', $Value), //month
                        date('Y', $Value), //year
                        date("H", $Value), //hours
                        date("i", $Value), //minutes
                    );
                } else {
                    $Value = array(false, false, false, false, false);
                }
                break;
            case CustomFields::WIDGET_DATETIME_LOCALE:
                // datetime widget: consists of textfield, input format: locale datetime string
                $Value = Dates::formatDate(Dates::FORMAT_DMYHIS, $Value);
                break;
            case CustomFields::WIDGET_STRTOTIME:
                // the widget consists of a textfield, input format: string (php strtotime format)
                // convert to ISO 8601 (excample: 2004-02-12T15:19:21+00:00)
                $Value = is_numeric($Value) ? date('c', (int)$Value) : false;
                break;
            case CustomFields::WIDGET_TIMESTAMP:
                // the widget consists of a textfield, input format: int (timestamp)
                // nothing to format, return given value
                break;
            case CustomFields::WIDGET_NUMBER:
                // display empty number value with 0
                $Value = (empty($Value)) ? 0 : $Value;
                break;
            case CustomFields::WIDGET_DECIMAL:
                // display empty number value with 0,00
                $Value = klicktipp_number_format((int)$Value / 100, 2);
                break;
        }

        return $Value;
    }

    /**
     * Caculate custom field data for setField action in ProcessFlow.
     *
     * Note: email address values are not saved punycoded, in order to be able
     * to apply substring searches (punycode format doesn't support this)
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $FieldInformation
     * @param $op : string -> operation to calculate the new value
     * @param int $param1 : [optional] additional param for certain $ops
     * @param int $param2 : [optional] additional param for certain $ops
     * @param array $delayDayOfWeek : [optional] array(1..7) -> constrain dates to certain weekdays
     * @param int $now : timestamp -> used for testing
     *
     * @return bool|int|string|void
     */
    public static function CalculateCustomFieldData(
        $account,
        $SubscriberID,
        $ReferenceID,
        $FieldInformation,
        $op,
        $param1 = 0,
        $param2 = 0,
        $delayDayOfWeek = [],
        $now = 0
    ) {
        if (empty($FieldInformation)) {
            return false;
        }

        if (empty($now)) {
            $now = time();
        }

        $UserID = $account->uid;
        $FieldType = $FieldInformation['FieldTypeEnum'];
        $OldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        $NewValue = $OldValue;

        switch ($op) {
            case CustomFields::CALCULATE_OP_VALUE:
                //set field to a specific value

                switch ($FieldType) {
                    case CustomFields::TYPE_DATE:
                    case CustomFields::TYPE_DATETIME:
                        //param1 format: 'Y-m-d' e.g. 'Y-m-d H:i'
                        $NewValue = strtotime("$param1", $now);
                        break;
                    case CustomFields::TYPE_TIME:
                        //param1 format: 'H:i'
                        $NewValue = CustomFields::ConvertCustomFieldDataFromWidget(
                            $param1,
                            CustomFields::WIDGET_TIME_SECONDSOFDAY
                        );
                        break;
                    case CustomFields::TYPE_NUMBER:
                        //param1 format: int
                        $NewValue = intval($param1);
                        break;
                    case CustomFields::TYPE_DECIMAL:
                        //param1 format: 99,99
                        $NewValue = UtilsNumber::formatFloatFromInput((string)$param1);
                        $NewValue = CustomFields::ConvertCustomFieldDataFromWidget(
                            $NewValue,
                            CustomFields::WIDGET_DECIMAL
                        );
                        break;
                    case CustomFields::TYPE_SINGLE:
                    case CustomFields::TYPE_PARAGRAPH:
                    case CustomFields::TYPE_HTML:
                        if (substr_count($param1, '%') >= 2) {
                            $param1 = CustomFields::PersonalizeCustomFieldData(
                                $account,
                                $SubscriberID,
                                $ReferenceID,
                                $param1
                            );
                        }
                        $NewValue = $param1;
                        break;
                    default:
                        //param1 format: string
                        $NewValue = $param1;
                        break;
                }
                break;
            case CustomFields::CALCULATE_OP_COPY:
                // copy from another custom field
                //param1 format: string - '4711', 'FirstName'

                $ArrayCustomFields = TransactionEmails::GetCachedCustomFields($UserID);
                $CopyFieldInformation = $ArrayCustomFields[$param1] ?? null;

                if (!empty($CopyFieldInformation)) {
                    //Note: for automations the compatibility of fields is validated in CampaignsProcessFlow::ValidateAutomation()
                    $NewValue = CustomFields::GetCustomFieldData(
                        $UserID,
                        $SubscriberID,
                        $CopyFieldInformation,
                        $ReferenceID
                    );

                    // Note $FieldType == type of target field
                    //      $CopyFieldInformation['FieldTypeEnum'] == type of source field
                    if ((int) $FieldType === CustomFields::TYPE_DATETIME) {
                        if ((int) $CopyFieldInformation['FieldTypeEnum'] === CustomFields::TYPE_DATE) {
                            // copy a date field into a datetime field
                            // use date from date field, keep time from datetime field
                            $NewValue = is_scalar($NewValue) ? (int) $NewValue : 0;
                            $OldValue = is_scalar($OldValue) ? (int) $OldValue : 0;
                            $NewValue = strtotime(
                                date(
                                    'Y-m-d ' . (empty($OldValue) ? '00:00:00' : date('H:i:00', $OldValue)),
                                    $NewValue
                                )
                            );
                        } elseif ((int) $CopyFieldInformation['FieldTypeEnum'] === CustomFields::TYPE_TIME) {
                            // copy a time field into a datetime field
                            // keep date from datetime field, use time from time field
                            $NewValue = is_scalar($NewValue) ? (int) $NewValue : 0;
                            $OldValue = is_scalar($OldValue) ? (int) $OldValue : 0;
                            $NewValue = CustomFields::ConvertCustomFieldDataToWidget(
                                $NewValue,
                                CustomFields::WIDGET_TIME_SECONDSOFDAY
                            );
                            $NewValue = strtotime(date("Y-m-d $NewValue:00", $OldValue ?: 0));
                        }
                    } elseif ((int) $FieldType === CustomFields::TYPE_DATE) {
                        // copy a datetime field into a date field
                        if ((int) $CopyFieldInformation['FieldTypeEnum'] === CustomFields::TYPE_DATETIME) {
                            $NewValue = is_scalar($NewValue) ? (int) $NewValue : 0;
                            $NewValue = strtotime(date('Y-m-d 00:00:00', $NewValue)) ;
                        }
                    } elseif ((int) $FieldType === CustomFields::TYPE_TIME) {
                        // copy a datetime field into a time field
                        if ((int)$CopyFieldInformation['FieldTypeEnum'] === CustomFields::TYPE_DATETIME) {
                            $NewValue = is_scalar($NewValue) ? (int) $NewValue : 0;
                            $NewValue = CustomFields::ConvertCustomFieldDataFromWidget(
                                date('H:i', $NewValue),
                                CustomFields::WIDGET_TIME_SECONDSOFDAY
                            );
                        }
                    }
                }
                break;
            case CustomFields::CALCULATE_OP_COPY_SUBSCRIBER_FIELDS:
                //copy a subscriber field into a custom field
                //param1: string - subscriber field to copy into custom field

                $NewValue = '';

                $SubscriberFields = array(
                    'SubscriberID',
                    'EmailAddress',
                    'SubscriptionStatus',
                    'BounceType',
                    'SubscriptionDate',
                    'SubscriberIP',
                    'SubscriptionIP',
                    'OptInIP',
                    'UnsubscriptionDate',
                    'OptInDate',
                    'SubscriptionReferrer',
                    'PhoneNumber',
                    'SMSSubscriptionStatus',
                    'SMSBounceType',
                    'SMSSubscriptionDate',
                    'SMSUnsubscriptionDate',
                    'SubscriptionSMS',
                );

                if (in_array($param1, $SubscriberFields)) {
                    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);

                    if (!empty($FullSubscriber)) {
                        if ($param1 == 'BounceType' || $param1 == 'SMSBounceType') {
                            $NewValue = t(/*ignore*/ Subscribers::$TranslationBounceTypes[$FullSubscriber[$param1]]);
                        } elseif ($param1 == 'SubscriptionStatus' || $param1 == 'SMSSubscriptionStatus') {
                            $NewValue = t(/*ignore*/ Subscribers::$DisplaySubscriptionStatus[$FullSubscriber[$param1]]);
                        } elseif ($param1 == 'EmailAddress') {
                            $NewValue = Subscribers::DepunycodeEmailAddress($FullSubscriber[$param1]);
                        } elseif (in_array($param1, array('SubscriptionIP', 'SubscriberIP', 'OptInIP'))) {
                            if ($param1 == 'SubscriberIP') {
                                $parseIP = $FullSubscriber['LastOpenIP'];
                            } else {
                                $parseIP = $FullSubscriber[$param1];
                            }

                            $regexIPv4 = '/(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]\d|\d)/';
                            $match = array();
                            $NewValue = '0.0.0.0';
                            preg_match($regexIPv4, $parseIP, $match);
                            if ($match[0] != '0.0.0.0' && filter_var($match[0], FILTER_VALIDATE_IP) !== false) {
                                $NewValue = $match[0];
                            } else {
                                $regexIPv6 = '/(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))/';
                                preg_match($regexIPv6, $parseIP, $match);
                                if (filter_var($match[0], FILTER_VALIDATE_IP) !== false) {
                                    $NewValue = $match[0];
                                }
                            }
                        } elseif (!empty($FullSubscriber[$param1])) {
                            $NewValue = $FullSubscriber[$param1];
                        }
                    }
                }
                break;
            case CustomFields::CALCULATE_OP_EMPTY:
                //reset custom field
                $NewValue = '';
                break;
            case CustomFields::CALCULATE_OP_TEXT_PREPEND:
                //prepend string to custom field value
                if (substr_count($param1, '%') >= 2) {
                    $param1 = CustomFields::PersonalizeCustomFieldData($account, $SubscriberID, $ReferenceID, $param1);
                }
                $NewValue = $param1 . $OldValue;
                break;
            case CustomFields::CALCULATE_OP_TEXT_APPEND:
                //append string to custom field value
                if (substr_count($param1, '%') >= 2) {
                    $param1 = CustomFields::PersonalizeCustomFieldData($account, $SubscriberID, $ReferenceID, $param1);
                }
                $NewValue = $OldValue . $param1;
                break;
            case CustomFields::CALCULATE_OP_TEXT_REPLACE:
                //replace string value in custom field
                //param1: string to replace
                //param2: string to replace with
                if (substr_count($param2, '%') >= 2) {
                    $param2 = CustomFields::PersonalizeCustomFieldData($account, $SubscriberID, $ReferenceID, $param2);
                }
                $NewValue = str_replace($param1, $param2, $OldValue);
                break;
            case CustomFields::CALCULATE_OP_TEXT_HASH_CRC32:
                $NewValue = hash('crc32', $OldValue);
                break;
            case CustomFields::CALCULATE_OP_TEXT_HASH_MD5:
                $NewValue = hash('md5', $OldValue);
                break;
            case CustomFields::CALCULATE_OP_TEXT_HASH_SHA1:
                $NewValue = hash('sha1', $OldValue);
                break;
            case CustomFields::CALCULATE_OP_TEXT_HASH_SHA256:
                $NewValue = hash('sha256', $OldValue);
                break;
            case CustomFields::CALCULATE_OP_TEXT_HASH_SHA384:
                $NewValue = hash('sha384', $OldValue);
                break;
            case CustomFields::CALCULATE_OP_TEXT_HASH_SHA512:
                $NewValue = hash('sha512', $OldValue);
                break;
            case CustomFields::CALCULATE_OP_NUMBER_ADD:
                //add number to custom field value
                //param1: number to add
                switch ($FieldType) {
                    case CustomFields::TYPE_DECIMAL:
                        //param1 format: 99,99
                        $ParamValue = UtilsNumber::formatFloatFromInput($param1);
                        $ParamValue = CustomFields::ConvertCustomFieldDataFromWidget(
                            $ParamValue,
                            CustomFields::WIDGET_DECIMAL
                        );
                        break;
                    case CustomFields::TYPE_NUMBER:
                    default:
                        //param1 format: int
                        $ParamValue = intval($param1);
                        break;
                }
                $NewValue = (empty($OldValue)) ? $ParamValue : ($OldValue + $ParamValue);
                break;
            case CustomFields::CALCULATE_OP_NUMBER_SUBTRACT:
                //subtract number to custom field value
                //param1: (positive) number to subtract
                switch ($FieldType) {
                    case CustomFields::TYPE_DECIMAL:
                        //param1 format: 99,99
                        $ParamValue = UtilsNumber::formatFloatFromInput($param1);
                        $ParamValue = CustomFields::ConvertCustomFieldDataFromWidget(
                            $ParamValue,
                            CustomFields::WIDGET_DECIMAL
                        );
                        break;
                    case CustomFields::TYPE_NUMBER:
                    default:
                        //param1 format: int
                        $ParamValue = intval($param1);
                        break;
                }
                $NewValue = (empty($OldValue)) ? -$ParamValue : ($OldValue - $ParamValue);
                break;
            case CustomFields::CALCULATE_OP_NUMBER_RANDOM:
                //generate a random number
                //param1: min value
                //param2: max value
                $NewValue = null;
                if (is_numeric($param1) && is_numeric($param2)) {
                    $NewValue = mt_rand($param1, $param2);
                }
                break;
            case CustomFields::CALCULATE_OP_URL_TRUNCATE_QUERY:
                //truncate the query part of the url in the custom field
                $url = parse_url($OldValue);
                $path = (empty($url['path']) || $url['path'] == '/') ? '' : $url['path'];
                $scheme = (empty($url['scheme'])) ? '' : $url['scheme'] . ':';
                $NewValue = "$scheme//{$url['host']}$path";
                break;
            case CustomFields::CALCULATE_OP_URL_TRUNCATE_PATH:
                //truncate the path from the url in the custom field
                $url = parse_url($OldValue);
                $scheme = (empty($url['scheme'])) ? '' : $url['scheme'] . ':';
                $NewValue = "$scheme//{$url['host']}";
                break;
            case CustomFields::CALCULATE_OP_DATETIME_NOW:
                //set a date/datetime based on the current time
                //param1: signed strtotime string, e.g. '+0 days', '+3 hours', '+5 years'
                //param2: CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND, CALCULATE_OP_DATETIME_TIME_FULLHOUR_FLOOR, CALCULATE_OP_DATETIME_TIME_FULLHOUR_CEIL
                //$delayDayOfWeek: if not empty, calculated dates will be increased by 1 day until they match a weekday specified in the array

                //calculate +X minutes, hours, days ... from now
                $NewValue = CustomFields::CalculateCustomFieldDatetimeValue($param1, $now, $delayDayOfWeek);
                break;

            case CustomFields::CALCULATE_OP_DATETIME_INCREASE:
            case CustomFields::CALCULATE_OP_DATETIME_DECREASE:
                //set a date/datetime based on the date/time in the custom field
                //param1: signed strtotime string, e.g. '+0 days', '-3 hours', '+5 years'
                //param2: option to calculate time (current, fixed, round, floor, ceil)
                //$delayDayOfWeek: if not empty, calculated dates will be increase by 1 day until they match a weekday specified in the array

                //$param is signed, either "+3 days" or "-3 days"
                //for backward compatibility, remove the sign and set it according to $op
                $param1 = str_replace(array('+', '-'), '', $param1);
                $sign = ($op == CustomFields::CALCULATE_OP_DATETIME_DECREASE) ? '-' : '+';

                //calculate X minutes, hours, days ... from value in field
                $NewValue = CustomFields::CalculateCustomFieldDatetimeValue(
                    $sign . $param1,
                    $OldValue,
                    $delayDayOfWeek
                );

                if ($FieldType == CustomFields::TYPE_DATETIME) {
                    $NewValue = CustomFields::CalculateCustomFieldRoundTime($param2, $NewValue, $FieldType);
                }

                break;
            case CustomFields::CALCULATE_OP_DATETIME_TIME_NOW:
                //set time field to current time
                $NewValue = CustomFields::ConvertCustomFieldDataFromWidget(
                    date('H:i', $now),
                    CustomFields::WIDGET_TIME_SECONDSOFDAY
                );
                break;
            case CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_FLOOR:
            case CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_CEIL:
            case CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND:
                //round time in field (time field only)
                $NewValue = CustomFields::CalculateCustomFieldRoundTime($op, $OldValue, $FieldType);
                break;
            case CustomFields::CALCULATE_OP_DATETIME_EASTER:
                //calculate easter for value in custom field
                $NewValue = strtotime(date('Y-m-d 00:00:00', easter_date(date('Y', $OldValue))));
                break;
            case CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_MONTH_FROM_NOW:
                $month = date('m', strtotime('next month', $now));
                $NewValue = strtotime(date("Y-$month-01 00:00:00", $now), $now);
                break;
            case CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_YEARS_FROM_NOW:
                $year = date('Y', strtotime('next year', $now));
                $NewValue = strtotime(date("$year-01-01 00:00:00", $now), $now);
                break;
            case CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_MONTH_FROM_FIELD:
                $NewValue = strtotime('first day of next month 00:00:00', $OldValue);
                break;
            case CustomFields::CALCULATE_OP_DATETIME_NEXT_FIRST_OF_YEARS_FROM_FIELD:
                $NewValue = strtotime(date("Y-01-01 00:00:00", strtotime('next year', $OldValue)));
                break;
            case CustomFields::CALCULATE_OP_DATETIME_ULTIMO_MONTH:
                $NewValue = strtotime(date("Y-m-t 00:00:00", $OldValue), $OldValue);
                if (date('N', $NewValue) == 6 || date('N', $NewValue) == 7) {
                    $NewValue = strtotime('last friday 00:00:00', $NewValue);
                }
                break;
            default:
                return false;
        }

        return $NewValue;
    }

    public static function PersonalizeCustomFieldData($account, $SubscriberID, $ReferenceID, $Value)
    {
        static $Subscribers;

        if (!isset($Subscribers[$SubscriberID])) {
            $Subscribers[$SubscriberID] = Subscribers::RetrieveFullSubscriber(
                $account->uid,
                $SubscriberID,
                $ReferenceID
            );
        }

        $FullSubscriber = $Subscribers[$SubscriberID];

        //find placeholders (subset of Personalization::PersonalizeExtractSingle)
        if (!preg_match_all('/%Subscriber:[^%]+%/U', $Value, $matches)) {
            return $Value;
        }

        $Params = $matches[0];

        //dummy data used to replace %Link:...% and %Data...% placeholders, which are not included here
        $ArrayEmail = array();
        $CampaignID = 0;
        $SenderDomain = array();

        $Replacements = Personalization::PersonalizeAssign(
            $Params,
            $account,
            $ArrayEmail,
            $CampaignID,
            $FullSubscriber,
            $ReferenceID,
            false,
            $SenderDomain
        );

        // replace parameters
        $Value = str_replace(array_keys($Replacements), array_values($Replacements), $Value);

        // in PersonalizeAssign every \n in paragraph custom field values is replaced with
        // %System:HTMLLinebreak%\n, those will be replaced to <br /> when inserted into a html email
        // here we just write into a custom field, so remove the placeholder
        return str_replace('%System:HTMLLinebreak%', '', $Value);
    }

    /**
     * @param $stringToTime : '+3 months' || '-2 days 13:50' for datetime fields
     * @param $now : timestamp
     * @param array $delayDayOfWeek : if not empty, calculated dates will be increased by 1 day until they match a weekday specified in the array
     * @return int: timestamp
     */
    public static function CalculateCustomFieldDatetimeValue($stringToTime, $now, $delayDayOfWeek = array())
    {
        // prevent TypeError in PHP8
        if ($now === "") {
            return 0;
        }

        //apply increase/decrease
        $NewValue = strtotime($stringToTime, $now);

        if ($NewValue === false) {
            return 0;
        }

        if (!empty($delayDayOfWeek)) {
            for ($w = 0; $w < 7; $w++) {
                $weekday = strtotime("+$w days", $NewValue);
                if (in_array(date('N', $weekday), $delayDayOfWeek)) {
                    $NewValue = $weekday;
                    break;
                }
            }
        }

        return $NewValue;
    }

    /**
     * Round a time in a time field or the time of an datetime field value to full hours
     * @param $op
     *  CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND: round (minutes 0..29 round down, 30..59 round up)
     *  CALCULATE_OP_DATETIME_TIME_FULLHOUR_FLOOR: set the minutes to 00
     *  CALCULATE_OP_DATETIME_TIME_FULLHOUR_CEIL: round to next hour
     * @param $value :
     *  TYPE_TIME => seconds of day 0...86399
     *  TYPE_DATETIME => timestamp
     * @param $FieldType : TYPE_TIME || TYPE_DATETIME
     * @return int
     */
    public static function CalculateCustomFieldRoundTime($op, $value, $FieldType)
    {
        if ($FieldType != CustomFields::TYPE_DATETIME && $FieldType != CustomFields::TYPE_TIME) {
            //only for time and datetime fields
            return $value;
        }

        if ($op == CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_ROUND) {
            $value = round($value / 3600) * 3600;
        } elseif ($op == CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_FLOOR) {
            $value = floor($value / 3600) * 3600;
        } elseif ($op == CustomFields::CALCULATE_OP_DATETIME_TIME_FULLHOUR_CEIL) {
            $value = ceil($value / 3600) * 3600;
        }

        if ($FieldType == CustomFields::TYPE_TIME && $value >= 86400) {
            $value = $value - 86400;
        }

        return $value;
    }

    /**
     * recognize a firstname from a given email address
     *
     * @conditions
     * firstname will only be overwritten if it matches the already stored firstname
     * this is done to recognize a potential lastname
     * a stored lastname will never be overwritten
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $FieldFirstNameID
     * @param int $FieldLastNameID
     *
     * @return bool
     */
    public static function CalculateNameOfSubscriber(
        $UserID,
        $SubscriberID,
        $ReferenceID,
        $FieldFirstNameID,
        $FieldLastNameID = 0
    ) {
        if (empty($FieldFirstNameID)) {
            return false;
        }
        $ArrayCustomFields = TransactionEmails::GetCachedCustomFields($UserID);
        $FieldInformation = $ArrayCustomFields[$FieldFirstNameID];
        if (empty($FieldInformation)) {
            return false;
        }
        $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
        if (empty($FullSubscriberWithFields)) {
            return false;
        }
        $names = CustomFields::RecognizeNames($FullSubscriberWithFields['EmailAddress']);
        if (empty($names)) {
            // no names were recognized
            return false;
        }
        $ExistingFirstName = $FullSubscriberWithFields["CustomField$FieldFirstNameID"];
        if (
            !CustomFields::isEmpty($FieldInformation, $ExistingFirstName) &&
            mb_strtolower($ExistingFirstName) != mb_strtolower($names['FirstName'])
        ) {
            // recognized firstname does not match stored firstname so do not overwrite
            return false;
        }
        // set firstname
        $subscriberStateChanged = CustomFields::UpdateCustomFieldData(
            $UserID,
            $SubscriberID,
            $ReferenceID,
            $FieldInformation,
            $names['FirstName']
        );
        $FieldInformation2 = (empty($FieldLastNameID)) ? array() : $ArrayCustomFields[$FieldLastNameID];
        $ExistingLastName = $FullSubscriberWithFields["CustomField$FieldLastNameID"];
        if (
            !empty($names['LastName']) &&
            !empty($FieldInformation2) &&
            CustomFields::isEmpty($FieldInformation2, $ExistingLastName)
        ) {
            // set lastname if user selected to set it and no last name is already stored
            $subscriberStateChanged = CustomFields::UpdateCustomFieldData(
                $UserID,
                $SubscriberID,
                $ReferenceID,
                $FieldInformation2,
                $names['LastName']
            ) || $subscriberStateChanged;
        }
        return $subscriberStateChanged;
    }

    /**
     * recognize the gender of a given firstname
     *
     * @conditions
     * source field must have stored firstname in it
     * existing values in target field will not be overwritten
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $SourceFieldID
     * @param $TargetFieldID
     * @param array $fieldValues
     * @param array $tagIDs
     *
     * @return bool
     */
    public static function CalculateGenderFromFirstname(
        $UserID,
        $SubscriberID,
        $ReferenceID,
        $SourceFieldID,
        $TargetFieldID,
        $fieldValues,
        $tagIDs
    ) {
        if (empty($SourceFieldID)) {
            return false;
        }

        $ArrayCustomFields = TransactionEmails::GetCachedCustomFields($UserID);
        $FieldInformation = $ArrayCustomFields[$SourceFieldID] ?? null;

        if (empty($FieldInformation)) {
            //source field not found
            return false;
        }

        //get the first name from source field
        $firstName = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformation, $ReferenceID);
        if (CustomFields::isEmpty($FieldInformation, $firstName)) {
            // source field has no stored firstname
            return false;
        }

        //retrieve the gender by firstname
        $ArrayFirstName = CustomFields::RetrieveFirstNameAndGender($firstName);
        if (empty($ArrayFirstName['gender'])) {
            //no gender could be retrieved, nothing we can do
            return false;
        }

        $gender = $ArrayFirstName['gender'];

        //write gender to custom field if given
        if (!empty($TargetFieldID)) {
            $FieldInformation2 = $ArrayCustomFields[$TargetFieldID];
            if (!empty($FieldInformation2)) {
                //check if gender already exist, do not overwrite
                $existingGender = CustomFields::GetCustomFieldData(
                    $UserID,
                    $SubscriberID,
                    $FieldInformation2,
                    $ReferenceID
                );
                if (CustomFields::isEmpty($FieldInformation2, $existingGender)) {
                    // target field is empty, save new gender

                    $genders = array(
                        CustomFields::GENDER_MALE => (empty($fieldValues[0])) ? "" : $fieldValues[0],
                        CustomFields::GENDER_FEMALE => (empty($fieldValues[1])) ? "" : $fieldValues[1],
                        CustomFields::GENDER_UNISEX => (empty($fieldValues[2])) ? "" : $fieldValues[2]
                    );

                    if ($genders[$gender]) {
                        $subscriberStatusChanged = CustomFields::UpdateCustomFieldData(
                            $UserID,
                            $SubscriberID,
                            $ReferenceID,
                            $FieldInformation2,
                            $genders[$gender]
                        );
                        if ($subscriberStatusChanged) {
                            TransactionEmails::RegisterAutomations($UserID, $SubscriberID, $ReferenceID);
                        }
                    }
                }
            }
        }

        //tag subscriber by gender if tags are given

        switch ($gender) {
            case CustomFields::GENDER_MALE:
                if (!empty($tagIDs[0])) {
                    Subscribers::TagSubscriber($UserID, $SubscriberID, $tagIDs[0], $ReferenceID, true);
                }
                break;
            case CustomFields::GENDER_FEMALE:
                if (!empty($tagIDs[1])) {
                    Subscribers::TagSubscriber($UserID, $SubscriberID, $tagIDs[1], $ReferenceID, true);
                }
                break;
            case CustomFields::GENDER_UNISEX:
            default:
                if (!empty($tagIDs[2])) {
                    Subscribers::TagSubscriber($UserID, $SubscriberID, $tagIDs[2], $ReferenceID, true);
                }
                break;
        }

        return true;
    }

    /**
     * Updates custom field information
     *
     * @param array $ArrayFieldAndValues Values of new custom field information
     * @param int $CustomFieldID Custom field id
     * @return boolean
     *
     * @deprecated use UpdateDB
     **/
    public static function Update($ArrayFieldAndValues, $CustomFieldID)
    {
        $CustomFieldInformation = CustomFields::RetrieveCustomField(
            $CustomFieldID,
            $ArrayFieldAndValues['RelOwnerUserID']
        );
        if ($CustomFieldInformation == false) {
            return false;
        }

        $DBArray = array_merge($CustomFieldInformation, $ArrayFieldAndValues);

        return static::UpdateDB($DBArray);
    }

    /**
     * Returns html code of a custom field
     *
     * <code>
     * <?php
     * $ArrayCustomFieldInformation = CustomFields::RetrieveField(3);
     * CustomFields::GenerateCustomFieldHTMLCode($ArrayCustomFieldInformation);
     * ?>
     * </code>
     *
     * @param array $ArrayCustomField -> CustomField data
     * @param string $Value -> ($DisplayInDialog == TRUE) ? no longer used (@see $ArrayCustomField['CustomFieldParams']['default_value']) : value of the subscriber
     * @param string $Style -> additional styles
     * @param bool $minimalversion -> use minimal code
     * @param bool $DisplayInDialog -> return HTML for a dialog or a subscription form
     * @param int|null $referenceID id of reference the value belongs to
     * @return string -> HTML
     *
     **/
    public static function GenerateCustomFieldHTMLCode(
        $ArrayCustomField,
        $Value = '',
        $Style = '',
        $minimalversion = false,
        $DisplayInDialog = false,
        $referenceID = null
    ) {
        $CustomFieldHTMLCode = '';

        //used in subscription form
        $Label = CustomFields::GetFieldName($ArrayCustomField);
        $default = ($DisplayInDialog) ? $Value : $ArrayCustomField['CustomFieldParams']['default_value'];

        $fieldName = 'FormValue_Fields[CustomField' . $ArrayCustomField['CustomFieldID'];
        $fieldID = 'FormValue_CustomField' . $ArrayCustomField['CustomFieldID'];
        if ($referenceID !== null) {
            $fieldName .= '_refid_' . $referenceID;
            $fieldID .= '_' . $referenceID;
        }
        $fieldName .= ']';

        // hidden is now an info in the form field definition, so any of the fields can be hidden and set with a default value -> TODO!
        if ($ArrayCustomField['CustomFieldParams']['hidden']) {
            //TODO: for CustomFields::TYPE_DATETIME, CustomFields::WIDGET_TIME_2SELECTS and CustomFields::WIDGET_CHECKBOXES the hidden field value will be "array"
            $CustomFieldHTMLCode = '<input type="hidden" name="' . $fieldName . '" value="' . $default . '"' . $fieldID . '" />';

            return $CustomFieldHTMLCode;
        } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_TIME) {
            $default = (!is_array($default)) ? explode(':', $default) : $default;
            $hour = $default[0];
            $minute = $default[1];
            $hours_options_html = '';
            $minutes_options_html = '';
            for ($h = 0; $h < 24; $h++) {
                $formatted = sprintf("%02d", $h); //leading 0
                $selected = ($formatted == $hour) ? 'selected="selected" ' : '';
                $hours_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
            }
            for ($m = 0; $m < 60; $m++) {
                $formatted = sprintf("%02d", $m); //leading 0
                $selected = ($formatted == $minute) ? 'selected="selected" ' : '';
                $minutes_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
            }

            if ($DisplayInDialog) {
                $CustomFieldHTMLCode .= '<select class="form-control" name="' . $fieldName . '[]">' . $hours_options_html . '</select><span> : </span>';
                $CustomFieldHTMLCode .= '<select class="form-control" name="' . $fieldName . '[]">' . $minutes_options_html . '</select>';
            } else {
                $CustomFieldHTMLCode = '<div style="clear:both;" class="form-small">';
                $CustomFieldHTMLCode .= '<span class="form-label">' . $Label . '</span><select name="' . $fieldName . '[]">' . $hours_options_html . '</select> : ';
                $CustomFieldHTMLCode .= '<select name="' . $fieldName . '[]">' . $minutes_options_html . '</select>';
                $CustomFieldHTMLCode .= '</div><br />';
            }
        } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
            $default = (is_array($default)) ? strtotime(
                implode('.', $default)
            ) : CustomFields::ConvertCustomFieldDataFromWidget($default, CustomFields::WIDGET_DATE_DATEPICKER);
            $year = '';
            $month = '';
            $day = '';
            if (!empty($default)) {
                $year = date("Y", $default);
                $month = date("m", $default);
                $day = date("d", $default);
            }
            $cur_year = date("Y");
            $year_from = (empty($ArrayCustomField['CustomFieldParams']['widget']['year_from'])) ? ($cur_year - 3) : $ArrayCustomField['CustomFieldParams']['widget']['year_from'];
            $year_to = (empty($ArrayCustomField['CustomFieldParams']['widget']['year_to'])) ? ($cur_year + 3) : $ArrayCustomField['CustomFieldParams']['widget']['year_to'];
            $year_options_html = '';
            $month_options_html = '';
            $day_options_html = '';


            for ($y = $year_from; $y <= $year_to; $y++) {
                $selected = ($y == $year) ? 'selected="selected" ' : '';
                $year_options_html .= '<option value="' . $y . '" ' . $selected . '>' . $y . '</option>';
            }
            for ($m = 1; $m < 13; $m++) {
                $formatted = sprintf("%02d", $m); //leading 0
                $selected = ($formatted == $month) ? 'selected="selected" ' : '';
                $month_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
            }
            for ($d = 1; $d < 32; $d++) {
                $formatted = sprintf("%02d", $d); //leading 0
                $selected = ($formatted == $day) ? 'selected="selected" ' : '';
                $day_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
            }

            if ($DisplayInDialog) {
                $CustomFieldHTMLCode = '<input type="text" size="12" class="form-control klicktipp-datepicker" name="' . $fieldName . '" value="' . $Value . '" id="' . $fieldName . '" />';
            } else {
                $CustomFieldHTMLCode = '<div style="clear:both;" class="form-small">';
                $CustomFieldHTMLCode .= '<span class="form-label">' . $Label . '</span><br /><select name="' . $fieldName . '[]"><option value="">' . t(
                    "Day"
                ) . '</option>' . $day_options_html . '</select> / ';
                $CustomFieldHTMLCode .= '<select name="' . $fieldName . '[]"><option value="">' . t(
                    "Month"
                ) . '</option>' . $month_options_html . '</select> / ';
                $CustomFieldHTMLCode .= '<select name="' . $fieldName . '[]"><option value="">' . t(
                    "Year"
                ) . '</option>' . $year_options_html . '</select>';
                $CustomFieldHTMLCode .= '</div><br />';
            }
        } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
            if (!$DisplayInDialog) {
                //the date of datetime is displayed as 3 selects only in a subscription forms
                //there are no default values
                //the user can select range of year options

                $cur_year = date("Y");
                $year_from = (empty($ArrayCustomField['CustomFieldParams']['widget']['year_from'])) ? ($cur_year - 3) : $ArrayCustomField['CustomFieldParams']['widget']['year_from'];
                $year_to = (empty($ArrayCustomField['CustomFieldParams']['widget']['year_to'])) ? ($cur_year + 3) : $ArrayCustomField['CustomFieldParams']['widget']['year_to'];
                $year_options_html = '';
                $month_options_html = '';
                $day_options_html = '';

                for ($y = $year_from; $y <= $year_to; $y++) {
                    $year_options_html .= '<option value="' . $y . '">' . $y . '</option>';
                }
                for ($m = 1; $m < 13; $m++) {
                    $formatted = sprintf("%02d", $m); //leading 0
                    $month_options_html .= '<option value="' . $formatted . '">' . $formatted . '</option>';
                }
                for ($d = 1; $d < 32; $d++) {
                    $formatted = sprintf("%02d", $d); //leading 0
                    $day_options_html .= '<option value="' . $formatted . '">' . $formatted . '</option>';
                }
            } else {
                //in klicktipp (Drupal) forms display the current value of the field
                $date = $default['date'];
                $hours = $default['hours'];
                $minutes = $default['minutes'];
            }

            //the 2 time dropdowns are the same for both displays
            $hours_options_html = '';
            $minutes_options_html = '';
            for ($h = 0; $h < 24; $h++) {
                $formatted = sprintf("%02d", $h); //leading 0
                $selected = ($formatted == $hours) ? 'selected="selected" ' : '';
                $hours_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
            }
            for ($m = 0; $m < 60; $m++) {
                $formatted = sprintf("%02d", $m); //leading 0
                $selected = ($formatted == $minutes) ? 'selected="selected" ' : '';
                $minutes_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
            }

            if ($DisplayInDialog) {
                $CustomFieldHTMLCode = '<div class="klicktipp-widget-datetime">';
                $CustomFieldHTMLCode .= '<input type="text" size="12" class="form-control klicktipp-datepicker" name="' . $fieldName . '[date]" value="' . $date . '" id="' . $fieldID . '" />';
                $CustomFieldHTMLCode .= '<span class="datetime">&nbsp;' . t('at(time)') . '&nbsp;</span>';
                $CustomFieldHTMLCode .= '<select class="form-control datetime-hours" name="' . $fieldName . '[hours]">' . $hours_options_html . '</select><span class="datetime"> : </span>';
                $CustomFieldHTMLCode .= '<select class="form-control datetime-minutes" name="' . $fieldName . '[minutes]">' . $minutes_options_html . '</select></div>';
            } else {
                $CustomFieldHTMLCode = '<div style="clear:both;width:320px;" class="form-small">';
                $CustomFieldHTMLCode .= '<span class="form-label">' . $Label . '</span><br /><select name="' . $fieldName . '[0]"><option value="">' . t(
                    "Day"
                ) . '</option>' . $day_options_html . '</select> / ';
                $CustomFieldHTMLCode .= '<select name="' . $fieldName . '[1]"><option value="">' . t(
                    "Month"
                ) . '</option>' . $month_options_html . '</select> / ';
                $CustomFieldHTMLCode .= '<select name="' . $fieldName . '[2]"><option value="">' . t(
                    "Year"
                ) . '</option>' . $year_options_html . '</select>&nbsp;&dash;&nbsp;';
                $CustomFieldHTMLCode .= '<select name="' . $fieldName . '[3]"><option value="">' . t(
                    "Hours"
                ) . '</option>' . $hours_options_html . '</select> : ';
                $CustomFieldHTMLCode .= '<select name="' . $fieldName . '[4]"><option value="">' . t(
                    "Minutes"
                ) . '</option>' . $minutes_options_html . '</select>';
                $CustomFieldHTMLCode .= '</div><br />';
            }
        } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_URL) {
            if ($DisplayInDialog) {
                if ($ArrayCustomField['CustomFieldID'] == 'Website') {
                    $CustomFieldHTMLCode = '<input type="text" class="form-control" name="' . $fieldName . '" value="' . $Value . '" id="' . $fieldID . '" />';
                } else {
                    //ckfinder path and script
                    $path_to_CKF = APP_URL . "ckfinder";

                    $ckfinder_button = array(
                        '#title' => t('Upload a file'),
                        '#value' => '#',
                        '#attributes' => array(
                            'data-event-click' => 'js_select_customfield_url_' . $ArrayCustomField['CustomFieldID'],
                        ),
                        '#suffix' => "
            <script type='text/javascript'>
              window['js_select_customfield_url_{$ArrayCustomField['CustomFieldID']}'] = function(element, args) {

                var href = '$path_to_CKF/ckfinder.html?action=js&func=js_customfield_set_url_{$ArrayCustomField['CustomFieldID']}';
                window.open(href, 'CKFinder', 'resizeable=yes');

                element.attr('href', 'JavaScript:void(0)');
                return false;
              }

              window['js_customfield_set_url_{$ArrayCustomField['CustomFieldID']}'] = function(url) {
                if (url != '') {
                  $('#$fieldID').val(url);
                }
              }
            </script>
          "
                    );

                    $CustomFieldHTMLCode = '<div class="ckfinder-select" style="padding-top:0;"><div style="width:67%;">';
                    $CustomFieldHTMLCode .= '<input type="text" class="form-control input-large" name="' . $fieldName . '" value="' . $Value . '" id="' . $fieldID . '" />';
                    $CustomFieldHTMLCode .= '</div>&nbsp;' . theme(
                        'klicktipp_edit_button',
                        array('element' => $ckfinder_button)
                    ) . '</div>';
                }
            } else {
                if ($minimalversion) {
                    $CustomFieldHTMLCode = '<input type="text" name="' . $fieldName . '" value="' . $Label . '" id="' . $fieldID . '" /><br />';
                } else {
                    $CustomFieldHTMLCode = '<div style="clear:both;" class="form-small">';
                    $CustomFieldHTMLCode .= '<input type="text" name="' . $fieldName . '" id="' . $fieldID . '" value="' . $Label . '" class="' . $Style . ' form-input form-fieldwidth" />';
                    $CustomFieldHTMLCode .= '</div>';
                }
            }
        } elseif (
            in_array(
                $ArrayCustomField['FieldTypeEnum'],
                array(
                    CustomFields::TYPE_SINGLE,
                    CustomFields::TYPE_EMAIL,
                    CustomFields::TYPE_NUMBER,
                    CustomFields::TYPE_DECIMAL
                )
            )
        ) {
            if ($DisplayInDialog) {
                $CustomFieldHTMLCode = '<input type="text" class="form-control" name="' . $fieldName . '" value="' . $Value . '" id="' . $fieldID . '" />';
            } else {
                if ($minimalversion) {
                    $CustomFieldHTMLCode = '<input type="text" name="' . $fieldName . '" value="' . $Label . '" id="' . $fieldID . '" /><br />';
                } else {
                    $CustomFieldHTMLCode = '<div style="clear:both;" class="form-small">';
                    $CustomFieldHTMLCode .= '<input type="text" name="' . $fieldName . '" id="' . $fieldID . '" value="' . $Label . '" class="' . $Style . ' form-input form-fieldwidth" />';
                    $CustomFieldHTMLCode .= '</div>';
                }
            }
        } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_PARAGRAPH || $ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_HTML) {
            if ($DisplayInDialog) {
                $CustomFieldHTMLCode .= '<textarea id="' . $fieldID . '" class="form-control" name="' . $fieldName . '" >' . $Value . '</textarea>';
            } else {
                if ($minimalversion) {
                    $CustomFieldHTMLCode .= '<textarea id="' . $fieldID . '" name="' . $fieldName . '" ></textarea>';
                } else {
                    $CustomFieldHTMLCode = '<div style="clear:both;" class="form-small">';
                    $CustomFieldHTMLCode .= '<span class="form-label">' . $Label . '</span><br /><textarea id="' . $fieldID . '" name="' . $fieldName . '" class="' . $Style . ' form-fieldwidth" style="height:100px;" ></textarea>';
                    $CustomFieldHTMLCode .= '</div>';
                }
            }
        } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DROPDOWN) {
            $ArrayCustomFieldChoices = CustomFields::GetOptionsAsArray($ArrayCustomField['FieldOptions']);

            if ($minimalversion) {
                $CustomFieldHTMLCode = '<select name="' . $fieldName . '" id="' . $fieldID . '" >';
            } else {
                if ($DisplayInDialog) {
                    $CustomFieldHTMLCode = '<select name="' . $fieldName . '" id="' . $fieldID . '" class="form-control">';
                } else {
                    $CustomFieldHTMLCode = '<div style="clear:both;" class="form-small">';
                    $CustomFieldHTMLCode .= '<span class="form-label">' . $Label . '&nbsp</span><select name="' . $fieldName . '" id="' . $fieldID . '" class="' . $Style . ' form-input form-fieldwidth" >';
                }
            }

            foreach ($ArrayCustomFieldChoices as $ArrayEachChoice) {
                $Selected = (!empty($default) && $default == $ArrayEachChoice['value']) ? 'selected="selected"' : '';

                if ($minimalversion || $DisplayInDialog) {
                    $CustomFieldHTMLCode .= '<option value="' . htmlspecialchars(
                        $ArrayEachChoice['value']
                    ) . '" ' . $Selected . '>' . htmlspecialchars($ArrayEachChoice['label']) . '</option>' . "\n";
                } else {
                    $CustomFieldHTMLCode .= '<option value="' . htmlspecialchars(
                        $ArrayEachChoice['value']
                    ) . '" ' . $Selected . ' class="' . $Style . ' form-fieldwidth">' . htmlspecialchars(
                        $ArrayEachChoice['label']
                    ) . '</option>' . "\n";
                }
            }

            if ($minimalversion || $DisplayInDialog) {
                $CustomFieldHTMLCode .= '</select>';
            } else {
                $CustomFieldHTMLCode .= '</select><br /></div>';
            }
        } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_CHECKBOX) {
            $ArrayCustomFieldChoices = CustomFields::GetOptionsAsArray($ArrayCustomField['FieldOptions']);

            $CustomFieldHTMLCode = '';
            if (!$minimalversion && !$DisplayInDialog) {
                $CustomFieldHTMLCode .= '<div style="clear:both;" class="form-small">';
            } else {
                if ($DisplayInDialog) {
                    $CustomFieldHTMLCode .= '<div>';
                }
            }


            foreach ($ArrayCustomFieldChoices as $ArrayEachChoice) {
                if (!empty($default)) {
                    $Checked = (in_array($ArrayEachChoice['value'], $default)) ? 'checked="checked"' : '';
                }
                if (!$minimalversion && !$DisplayInDialog) {
                    $CustomFieldHTMLCode .= '<input type="checkbox" name="' . $fieldName . '[]" value="' . htmlspecialchars(
                        $ArrayEachChoice['value']
                    ) . '" id="' . $fieldID . '" ' . $Checked . ' /> ' . htmlspecialchars(
                        $ArrayEachChoice['label']
                    ) . '<br />' . "\n";
                } else {
                    $CustomFieldHTMLCode .= '<span><input type="checkbox" name="' . $fieldName . '[]" value="' . htmlspecialchars(
                        $ArrayEachChoice['value']
                    ) . '" id="' . $fieldID . '" ' . $Checked . ' class="' . $Style . '" /> ' . htmlspecialchars(
                        $ArrayEachChoice['label']
                    ) . '</span><br />' . "\n";
                }
            }

            if (!$minimalversion && !$DisplayInDialog) {
                $CustomFieldHTMLCode .= '</div><br />';
            } else {
                if ($DisplayInDialog) {
                    $CustomFieldHTMLCode .= '</div>';
                }
            }
        }

        return $CustomFieldHTMLCode;
    }

    /**
     * Validates the provided value against custom field validation rule
     *
     * @param array $ArrayCustomField
     * @param string $CustomFieldValue
     * @param string $CheckStrtotime : check strtotime value 'Y-m-d H:i', 'Y-m-d', 'H:i'
     * @return array
     *
     */
    public static function ValidateCustomFieldValue($ArrayCustomField, $CustomFieldValue, $CheckStrtotime = false)
    {
        if (!empty($CustomFieldValue)) {
            $toString = (string)$CustomFieldValue;
            if (strlen($toString) > static::VALUE_MAX_LENGTH) {
                //Note: max length is determined by DB (field type text == 64k)
                //Note: strlen() counts bytes, so 1 multibyte character of 3 bytes is counted as 3
                return array(
                    false,
                    'Custom field value exceeds max size of 64k',
                    t('Custom field value exceeds max size of 64k')
                );
            }

            if ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_NUMBER) {
                if (!is_numeric($CustomFieldValue) || !is_int($CustomFieldValue + 0)) {
                    return array(
                        false,
                        'Custom field value is not a numeric value',
                        t('Custom field value is not a numeric value')
                    );
                }
            } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DECIMAL) {
                $minus = substr_count($CustomFieldValue, '-');

                if (
                    preg_replace('/[^0-9\-,\.]/', '', $CustomFieldValue) != $CustomFieldValue ||
                    $minus > 1 || ($minus == 1 && strpos($CustomFieldValue, '-') != 0)
                ) {
                    // decimal contains illegal characters OR multiple - OR the - is not the first character
                    // Note: allow multiple . and , since we don't know yet, what the thousands separator is
                    //       What the decimal point is or if the value has a thousand separator doesn't matter at this point
                    //       the value will be formatted by UtilsNumber::formatFloatFromInput later

                    return array(
                        false,
                        'Custom field value is not a decimal value',
                        t('Custom field value is not a decimal value')
                    );
                }
            } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_EMAIL) {
                // Note: email addresses are not saved punycoded, in order to be able
                //  to apply substring searches (punycode format doesn't support this)
                if (!Subscribers::ValidateEmailAddress(Subscribers::PunycodeEmailAddress($CustomFieldValue))) {
                    return array(
                        false,
                        'Custom field value is not an email address',
                        t('Custom field value is not an email address')
                    );
                }
            } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_URL) {
                //allow http://, https://, ftp://, feed://
                if (!valid_url($CustomFieldValue, true)) {
                    return array(
                        false,
                        'Custom field value is not an URL address',
                        t('Custom field value is not an URL address')
                    );
                }
            } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
                // There's no need to check date format with strtotime. It will cause several problems.
                if ($CheckStrtotime) {
                    // check strtotime value 'Y-m-d'
                    if ($CustomFieldValue != date('Y-m-d', strtotime($CustomFieldValue))) {
                        return array(false, 'Invalid strtotime value (Y-m-d)', t('Invalid strtotime value (Y-m-d)'));
                    }
                } elseif (
                    $CustomFieldValue != CustomFields::ConvertCustomFieldDataFromWidget(
                        $CustomFieldValue,
                        CustomFields::WIDGET_TIMESTAMP
                    )
                ) {
                    // a timestamp is expected
                    return array(
                        false,
                        'Custom field value has invalid date format',
                        t('Custom field value has invalid date format')
                    );
                }
            } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_TIME) {
                if ($CheckStrtotime) {
                    // check strtotime value 'H:i'
                    $check = CustomFields::ConvertCustomFieldDataFromWidget(
                        $CustomFieldValue,
                        CustomFields::WIDGET_TIME_SECONDSOFDAY
                    );
                    if (
                        $CustomFieldValue != CustomFields::ConvertCustomFieldDataToWidget(
                            $check,
                            CustomFields::WIDGET_TIME_SECONDSOFDAY
                        )
                    ) {
                        return array(false, 'Invalid strtotime value (H:i)', t('Invalid strtotime value (H:i)'));
                    }
                } elseif (
                    $CustomFieldValue != CustomFields::ConvertCustomFieldDataFromWidget(
                        $CustomFieldValue,
                        CustomFields::WIDGET_TIMESTAMP
                    )
                ) {
                    // a timestamp is expected
                    return array(
                        false,
                        'Custom field value has invalid time format',
                        t('Custom field value has invalid time format')
                    );
                }
            } elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
                // There's no need to check date format with strtotime. It will cause several problems.
                if ($CheckStrtotime) {
                    // check strtotime value 'Y-m-d H:i'
                    if ($CustomFieldValue != date('Y-m-d H:i', strtotime($CustomFieldValue))) {
                        return array(
                            false,
                            'Invalid strtotime value (Y-m-d H:i)',
                            t('Invalid strtotime value (Y-m-d H:i)')
                        );
                    }
                } elseif (
                    $CustomFieldValue != CustomFields::ConvertCustomFieldDataFromWidget(
                        $CustomFieldValue,
                        CustomFields::WIDGET_TIMESTAMP
                    )
                ) {
                    // a timestamp is expected
                    return array(
                        false,
                        'Custom field value has invalid datetime format',
                        t('Custom field value has invalid datetime format')
                    );
                }
            }
        }

        return array(true);
    }

    /**
     * @param int $forType
     * @return int[]
     */
    public static function getMergeCompatibilityFieldTypes(int $forType): array
    {
        switch ($forType) {
            case self::TYPE_DECIMAL:
            case self::TYPE_SINGLE:
                return [
                    self::TYPE_SINGLE,
                    self::TYPE_PARAGRAPH,
                ];
            case self::TYPE_PARAGRAPH:
                return [
                    self::TYPE_PARAGRAPH,
                ];
            case self::TYPE_DROPDOWN:
            case self::TYPE_CHECKBOX:
                return [
                    self::TYPE_SINGLE,
                    self::TYPE_PARAGRAPH,
                    self::TYPE_DROPDOWN,
                    self::TYPE_CHECKBOX,
                ];
            case self::TYPE_EMAIL:
                return [
                    self::TYPE_EMAIL,
                    self::TYPE_SINGLE,
                    self::TYPE_PARAGRAPH,
                ];
            case self::TYPE_NUMBER:
                return [
                    self::TYPE_NUMBER,
                    self::TYPE_DECIMAL,
                    self::TYPE_SINGLE,
                    self::TYPE_PARAGRAPH,
                ];
            case self::TYPE_URL:
                return [
                    self::TYPE_URL,
                    self::TYPE_SINGLE,
                    self::TYPE_PARAGRAPH,
                ];
            case self::TYPE_DATE:
            case self::TYPE_DATETIME:
                return [
                    self::TYPE_DATE,
                    self::TYPE_DATETIME,
                    self::TYPE_SINGLE,
                    self::TYPE_PARAGRAPH,
                ];
            case self::TYPE_TIME:
                return [
                    self::TYPE_TIME,
                    self::TYPE_SINGLE,
                    self::TYPE_PARAGRAPH,
                ];
            default:
                return [];
        }
    }

    public static function GetMergeCompatibilityFieldOptions($ArraySourceField, $ArrayTargetField)
    {
        $SourceOptions = CustomFields::GetOptionsAsArray($ArraySourceField['FieldOptions']);
        $TargetOptions = CustomFields::GetOptionsAsArray($ArrayTargetField['FieldOptions']);

        $SourceOptionValues = array();
        foreach ($SourceOptions as $option) {
            $SourceOptionValues[] = $option['value'];
        }

        $TargetOptionValues = array();
        foreach ($TargetOptions as $option) {
            $TargetOptionValues[] = $option['value'];
        }

        $conflicts = array_diff($SourceOptionValues, $TargetOptionValues);

        //return a conflict if not all values of the SourceOptions are present in the TargetOptions
        return empty($conflicts);
    }

    /**
     * Merge SourceFieldValues into TargetFieldValues
     * Deletes SourceField
     * TODO: replace SourceFields in forms
     *
     * @param array $ArrayMergeInformation
     *  RelOwnerUserID: int
     *  TargetFieldID: int
     *  SourceFieldID: int
     *  ValuePriority: string 'source'|'target'
     *
     *
     */
    public static function Merge($ArrayMergeInformation)
    {
        // global custom field?
        if (isset(CustomFields::$GlobalCustomFieldDefs[$ArrayMergeInformation['SourceFieldID']])) {
            return array(
                'Success' => false,
                'ErrorCode' => 1,
                'Message' => "Merge from global field not allowed",
            );
        }

        $required_values = array(
            "RelOwnerUserID",
            "TargetFieldID",
            "SourceFieldID",
            "ValuePriority"
        );
        foreach ($required_values as $required) {
            if (empty($ArrayMergeInformation[$required])) {
                return array(
                    'Success' => false,
                    'ErrorCode' => 2,
                    'Message' => "Merge information missing",
                );
            }
        }

        $UserID = $ArrayMergeInformation['RelOwnerUserID'];
        $SourceFieldID = $ArrayMergeInformation['SourceFieldID'];
        $TargetFieldID = $ArrayMergeInformation['TargetFieldID'];

        //check if both fields exist and retrieve both type enums
        $AllCustomFields = CustomFields::RetrieveCustomFieldsCategories($UserID);
        $AllCustomFields = $AllCustomFields['AllCustomFields'];

        if (empty($AllCustomFields[$SourceFieldID])) {
            return array(
                'Success' => false,
                'ErrorCode' => 3,
                'Message' => "Source field does not exist",
            );
        }
        if (empty($AllCustomFields[$TargetFieldID])) {
            return array(
                'Success' => false,
                'ErrorCode' => 4,
                'Message' => "Target field does not exist",
            );
        }

        if (
            !in_array(
                $AllCustomFields[$TargetFieldID]['FieldTypeEnum'],
                CustomFields::GetMergeCompatibilityFieldTypes($AllCustomFields[$SourceFieldID]['FieldTypeEnum'])
            )
        ) {
            return array(
                'Success' => false,
                'ErrorCode' => 5,
                'Message' => "FieldTypeEnums incompatible",
            );
        }

        if ($AllCustomFields[$TargetFieldID]['FieldTypeEnum'] == CustomFields::TYPE_DROPDOWN || $AllCustomFields[$TargetFieldID]['FieldTypeEnum'] == CustomFields::TYPE_CHECKBOX) {
            if (
                !CustomFields::GetMergeCompatibilityFieldOptions(
                    $AllCustomFields[$SourceFieldID],
                    $AllCustomFields[$TargetFieldID]
                )
            ) {
                return array(
                    'Success' => false,
                    'ErrorCode' => 6,
                    'Message' => "FieldOptions incompatible",
                );
            }
        }

        $isSourceFieldMultiValue = !empty($AllCustomFields[$SourceFieldID]['MultiValue']);
        $isTargetFieldMultiValue = !empty($AllCustomFields[$TargetFieldID]['MultiValue']);

        if ($isTargetFieldMultiValue) {
            if ($isSourceFieldMultiValue) {
                // cases covered:
                // multi value --> multi value

                $TargetReferenceIDs = []; // if empty fill later with reference ID of the source
            } else {
                // cases covered:
                // single value --> multi value

                $TargetReferenceIDs = Reference::getReferenceIDsOfUser($UserID);
            }
        } else {
            // cases covered:
            // multi value --> single value
            // single value --> single value
            $TargetReferenceIDs = [0];
        }

        //get Subscriber SourceField values
        $merged = 0;
        $result = db_query(
            "SELECT * FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelFieldID = :RelFieldID",
            array(
                ':RelOwnerUserID' => $UserID,
                ':RelFieldID' => $SourceFieldID
            )
        );
        while ($FieldValue = kt_fetch_array($result)) {
            if ($ArrayMergeInformation['ValuePriority'] == 'target') {
                if (
                    db_query(
                        "SELECT 1 FROM {custom_field_values} cv" .
                        " LEFT JOIN {custom_fields} cf ON cf.RelOwnerUserID = cv.RelOwnerUserID AND cf.CustomFieldID = cv.RelFieldID " .
                        " WHERE cv.RelOwnerUserID = :RelOwnerUserID AND cv.RelSubscriberID = :RelSubscriberID AND cv.RelFieldID = :RelFieldID" .
                        " AND (cf.CustomFieldID IS NULL OR cf.MultiValue = 0 OR cv.ReferenceID = :ReferenceID)",
                        array(
                            ':RelOwnerUserID' => $UserID,
                            ':RelFieldID' => $TargetFieldID,
                            ':RelSubscriberID' => $FieldValue['RelSubscriberID'],
                            ':ReferenceID' => $FieldValue['ReferenceID'],
                        )
                    )->fetchField()
                ) {
                    continue; // while
                }
            }

            if (empty($TargetReferenceIDs)) {
                $ReferenceIDs = [$FieldValue['ReferenceID']];
            } else {
                $ReferenceIDs = $TargetReferenceIDs;
            }

            foreach ($ReferenceIDs as $ReferenceID) {
                CustomFields::UpdateCustomFieldData(
                    $UserID,
                    $FieldValue['RelSubscriberID'],
                    $ReferenceID,
                    $AllCustomFields[$TargetFieldID],
                    $FieldValue['ValueText']
                );
                $merged++;
            }
        }

        //TODO: replace all source fields with target fields in user forms

        // copy meta labels ...
        $SourceLabels = CustomFields::FromID($UserID, $SourceFieldID)->GetMetaLabels(false);
        $TargetLabels = CustomFields::FromID($UserID, $TargetFieldID)->GetMetaLabels(false);
        $MetaLabels = array_unique(array_merge($TargetLabels, $SourceLabels));
        MetaLabels::SetLabelsOfEntity(
            $UserID,
            DatabaseNamedTable::BASETYPE_CUSTOM_FIELDS,
            $AllCustomFields[$TargetFieldID]['FieldTypeEnum'],
            $TargetFieldID,
            $MetaLabels
        );

        static::DeleteDB(array(
            'RelOwnerUserID' => $UserID,
            'CustomFieldID' => $SourceFieldID
        ));

        return array(
            'Success' => true,
            'ErrorCode' => 0,
            'Message' => "Merge successful",
            'AffectedValues' => $merged,
        );
    }

    public static function CheckDuplicateName($UserID, $Name)
    {
        $query = "SELECT CustomFieldID FROM {custom_fields} WHERE" .
            " RelOwnerUserID = :RelOwnerUserID AND FieldName = :Name LIMIT 0,1";

        $params = array(
            ':RelOwnerUserID' => $UserID,
            ':Name' => $Name,
        );

        return db_query($query, $params)->fetchField() ?: 0;
    }

    /**
     * creates a firstname
     * duplicate firstnames are not allowed and will be ignored on insert
     * @param $firstName
     * @param $gender
     * @param bool $isRecursion
     * @return void
     */
    public static function CreateFirstname($firstName, $gender, $isRecursion = false)
    {
        if (
            empty($firstName) || empty($gender) ||
            !in_array(
                $gender,
                array(CustomFields::GENDER_MALE, CustomFields::GENDER_FEMALE, CustomFields::GENDER_UNISEX)
            )
        ) {
            return;
        }
        db_query(
            "INSERT IGNORE INTO {firstnames} (firstname, gender) VALUES (:firstname, :gender)",
            array(
                ':firstname' => mb_strtolower($firstName),
                ':gender' => $gender,
            )
        );
        if (!$isRecursion) {
            // replace ä with ae, ü with ue and ö with oe
            $paraphrase = str_replace(array("ä", "ü", "ö"), array("ae", "ue", "oe"), $firstName);
            if ($paraphrase != $firstName) {
                // the name changed so write the paraphrase to the db as well
                // this should in itself never be called twice but guarantee it
                CustomFields::CreateFirstname($paraphrase, $gender, true);
            }
        }
    }

    /**
     * retrieve entire db array from firstnames table
     * @param $name
     * @return array
     */
    public static function RetrieveFirstNameAndGender($name)
    {
        $DBArray = kt_fetch_array(
            db_query("SELECT * FROM {firstnames} WHERE firstname = :firstname", array(
                ':firstname' => $name,
            ))
        );
        return (empty($DBArray)) ? [] : $DBArray;
    }

    /**
     * @param $names
     * @return array
     */
    public static function RetrieveSpecifiedFirstNames($names)
    {
        $firstNames = array();
        $result = db_query("SELECT * FROM {firstnames} WHERE firstname IN (:firstnames)", array(
            ':firstnames' => $names,
        ));
        while ($DBArray = kt_fetch_array($result)) {
            $firstNames[$DBArray['firstname']] = $DBArray['gender'];
        }
        return $firstNames;
    }

    /**
     * split a given full name (firts and lastname) into parts and try to recognize what is a first name
     * @param string $fullName
     * @return array<string, string>|null
     */
    public static function splitFullname(string $fullName): ?array
    {
        $parts = explode(" ", $fullName);
        $firstnames = [];
        $lastnames = [];
        $allFirstNames = CustomFields::RetrieveSpecifiedFirstNames($parts);
        for ($i = 0; $i < count($parts); $i++) {
            if (!empty($parts[$i])) {
                if (isset($allFirstNames[mb_strtolower($parts[$i])])) {
                    $firstnames[] = $parts[$i];
                } else {
                    $lastnames[] = $parts[$i];
                }
            }
        }
        return [
            'firstnames' => implode(' ', $firstnames),
            'lastnames' => implode(' ', $lastnames)
        ];
    }

    /**
     * split a given email address into parts and try to recognize a first name and possibly a surname
     * @conditions
     * local part is lowercased and searched for firstnames from DB table
     * local part will be split by delimiters dot, underscore and dash
     * split by dash at last to recognize double names
     * @param $EmailAddress
     * @return array
     */
    public static function RecognizeNames($EmailAddress)
    {
        $parts = explode("@", $EmailAddress);
        // remove all numbers from local part and lowercase it
        $localPart = preg_replace('/[0-9]+/', '', mb_strtolower($parts[0]));

        // check if local part itself already is a firstname
        $firstName = CustomFields::RetrieveFirstNameAndGender($localPart);
        if (!empty($firstName)) {
            return array(
                'FirstName' => CustomFields::FormatName($localPart)
            );
        }
        $dotParts = explode(".", $localPart);
        $names = CustomFields::IdentifyNames($dotParts);
        if (!empty($names)) {
            return $names;
        }
        $underscoreParts = explode("_", $localPart);
        $names = CustomFields::IdentifyNames($underscoreParts);
        if (!empty($names)) {
            return $names;
        }
        // split by dash last to detect double names
        $dashParts = explode("-", $localPart);
        $names = CustomFields::IdentifyNames($dashParts);
        if (!empty($names)) {
            return $names;
        }
        return array();
    }

    /**
     * try to identify a firstname and possibly a lastname in the given parts
     * @conditions
     * identifies a lastname if local part contains of exactly two parts after split by delimiter
     * number of splitted parts can be greater than 2, e.g. <EMAIL>
     * lastname must have at least two characters
     * lastname must not contain a special char other than a dash
     * @param $parts
     * @return array
     */
    public static function IdentifyNames($parts)
    {
        if (empty($parts)) {
            return array();
        }
        $firstNames = CustomFields::RetrieveSpecifiedFirstNames($parts);
        for ($i = 0; $i < count($parts); $i++) {
            if (!empty($parts[$i]) && isset($firstNames[$parts[$i]])) {
                $names = array(
                    'FirstName' => CustomFields::FormatName($parts[$i]),
                );
                if (count($parts) != 2) {
                    // only return recognized firstname
                    return $names;
                }
                // local part has two parts, try to recognize lastname
                // interpret remaining part out of the two as lastname
                $lastName = ($i == 0) ? $parts[1] : $parts[0];
                // remove "-" from lastname to check with regex if lastname contains special char
                $tmplastName = str_replace("-", "", $lastName);
                if (preg_match("/\P{L}/u", $tmplastName)) {
                    // last name contains a special character other than "-" and therefore is invalid
                    // only return already recognized firstname in this case
                    return $names;
                }
                if (strlen($lastName) >= 2) {
                    // a lastname must have at least two characters
                    $names['LastName'] = CustomFields::FormatName($lastName);
                }
                return $names;
            }
        }
        return array();
    }

    /**
     * format name with an uppercase letter first
     * multiple names are expected to be concatenated by a dash (-)
     * @param $name
     * @return string
     */
    public static function FormatName($name)
    {
        $parts = explode("-", $name);
        $name = ucfirst($parts[0]);
        for ($i = 1; $i < count($parts); $i++) {
            // this is at least a double name
            $name .= "-" . ucfirst($parts[$i]);
        }
        return $name;
    }

    /**
     * @deprecated
     */
    public function getEditURL($CampaignID = 0): string
    {
        return (new CustomfieldLinkResolver())->linkEdit(
            $this->GetData('RelOwnerUserID'),
            $this->GetData('CustomFieldID')
        );
    }

    /**
     * @deprecated
     */
    public static function getAddURL($UserID, $data = ''): string
    {
        return (new CustomfieldLinkResolver())->linkOverview($UserID);
    }

    public function isGlobal(): bool
    {
        return !empty($this->DataFlat['IsGlobal']);
    }

    /**
     * Returns all (including global) customfields of the user as objects
     * @param int $userId
     * @return CustomFields[]
     */
    public static function getFields(int $userId): array
    {
        $entities = [];
        foreach (self::RetrieveCustomFields($userId, true) as $customField) {
            $entities[] = self::FromArray($customField);
        }

        return $entities;
    }

    public static function createNewInstance(int $userId): self
    {
        $dbArray = array_merge(self::$DefaultFields, [
            'CustomFieldID' => 0,
            'RelOwnerUserID' => $userId
        ]);

        return new self($dbArray);
    }

    /**
     * @return CustomfieldValueDbValueObject[]
     * @throws Exception
     */
    public function getFieldValuesForSwitchToSingleValue(): array
    {
        // get the latest field value from subscriber references for all subscribers
        $result = db_query("SELECT * FROM {custom_field_values} " .
            " WHERE RelOwnerUserID = :RelOwnerUserID AND RelFieldID = :RelFieldID ORDER BY SubscriptionDate ASC  ", [
            ':RelOwnerUserID' => $this->GetData('RelOwnerUserID'),
            ':RelFieldID' => $this->GetData('CustomFieldID'),
        ])->fetchAllAssoc('RelSubscriberID', PDO::FETCH_ASSOC);

        $fieldValues = [];
        foreach ($result as $value) {
            $fieldValues[] = CustomfieldValueDbValueObject::createFromDbResult($value);
        }

        return $fieldValues;
    }
} // END class Lists

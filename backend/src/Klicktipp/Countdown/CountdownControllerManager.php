<?php

namespace App\Klicktipp\Countdown;

use App\Klicktipp\AngularApi\Controller\Exception\ApiControllerException;
use App\Klicktipp\AngularApi\Validators\Exception\ApiValidationExceptionCollectionException;
use App\Klicktipp\AngularApi\ValueObject\ApiResponseOptionIntValueObject;
use App\Klicktipp\AngularApi\ValueObject\ApiResponseOptionStringValueObject;
use App\Klicktipp\Countdown\Exception\CountdownNotFoundException;
use App\Klicktipp\Countdown\Validators\CountdownCustomImageValidator;
use App\Klicktipp\Countdown\Validators\CountdownNameValidator;
use App\Klicktipp\Countdown\Validators\CountdownTargetUrlExpiredValidator;
use App\Klicktipp\Countdown\Validators\CountdownTargetUrlValidator;
use App\Klicktipp\Countdown\Validators\CountdownTerminationTypeCustomFieldValidator;
use App\Klicktipp\Countdown\Validators\CountdownTerminationTypeFixedValidator;
use App\Klicktipp\Countdown\Validators\CountdownTerminationTypeTagsValidator;
use App\Klicktipp\Countdown\ValueObject\CountdownTemplateOptionValueObject;
use App\Klicktipp\Countdown\ValueObject\Request\CountdownUpdateRequestValueObject;
use App\Klicktipp\Settings;
use App\Klicktipp\Tag;
use App\Klicktipp\Tag\TagManager;
use App\Klicktipp\TagCategoryAll;
use App\Klicktipp\ToolCountdown;
use App\Klicktipp\Exception\DatabaseException;
use Symfony\Component\HttpFoundation\Response;

class CountdownControllerManager
{
    private CountdownManager $countdownManager;
    private CountdownValidator $validator;
    private TagManager $tagManager;

    public function __construct(
        CountdownManager $manager,
        CountdownValidator $validator,
        TagManager $tagManager
    ) {
        $this->countdownManager = $manager;
        $this->validator = $validator;
        $this->tagManager = $tagManager;
    }

    /**
     * @throws ApiValidationExceptionCollectionException
     * @throws DatabaseException
     */
    public function create(int $userId, CountdownUpdateRequestValueObject $data): ToolCountdown
    {
        $countdown = $this->countdownManager->instanciateNewCountdown($userId);
        $countdown = $this->updateFromRequest($countdown, $data, $userId);
        $this->validator->validate($countdown, [
            CountdownNameValidator::class
        ]);
        return $this->countdownManager->update($countdown);
    }

    /**
     * @throws ApiControllerException
     * @throws ApiValidationExceptionCollectionException
     * @throws DatabaseException
     */
    public function copy(int $userId, int $copyId, CountdownUpdateRequestValueObject $data): ToolCountdown
    {
        try {
            $countdown = $this->countdownManager->getCountdown($userId, $copyId);
            $countdown = $this->updateFromRequest($countdown, $data, $userId);
            $this->validator->validate($countdown, [
                CountdownNameValidator::class,
                CountdownTerminationTypeFixedValidator::class,
                CountdownTerminationTypeTagsValidator::class,
                CountdownTerminationTypeCustomFieldValidator::class,
                CountdownCustomImageValidator::class,
                CountdownTargetUrlValidator::class,
                CountdownTargetUrlExpiredValidator::class
            ]);
            // create new entity
            $countdown->set('ToolID', 0);
            return $this->countdownManager->update($countdown);
        } catch (CountdownNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'countdown-copy-failed',
                t('Countdown::Copy::Error::The Countdown could not be copied.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }
    }

    /**
     * @throws ApiControllerException
     * @throws ApiValidationExceptionCollectionException
     * @throws DatabaseException
     */
    public function update(int $userId, int $id, CountdownUpdateRequestValueObject $data): ToolCountdown
    {
        try {
            $countdown = $this->countdownManager->getCountdown($userId, $id);
            $countdown = $this->updateFromRequest($countdown, $data, $userId);
            $this->validator->validate($countdown, [
                CountdownNameValidator::class,
                CountdownTerminationTypeFixedValidator::class,
                CountdownTerminationTypeTagsValidator::class,
                CountdownTerminationTypeCustomFieldValidator::class,
                CountdownCustomImageValidator::class,
                CountdownTargetUrlValidator::class,
                CountdownTargetUrlExpiredValidator::class
            ]);
            return $this->countdownManager->update($countdown);
        } catch (CountdownNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'countdown-update-failed',
                t('Countdown::Update::Error::The Countdown could not be updated.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }
    }

    /**
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function retrieve(int $userId, int $id, int $copyId): ToolCountdown
    {
        try {
            if (empty($id)) {
                if (empty($copyId)) {
                    $entity = $this->countdownManager->instanciateNewCountdown($userId);
                } else {
                    $entity = $this->countdownManager->getCountdown($userId, $copyId);
                    $entity->set('ToolID', 0);
                    $entity->set('Name', '');
                }
            } else {
                $entity = $this->countdownManager->getCountdown($userId, $id);
            }
            return $entity;
        } catch (CountdownNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'countdown-not-found',
                t('Countdown::Retrieve::Error::The Countdown could not be found.'),
                Response::HTTP_NOT_FOUND
            );
        }
    }

    /**
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function delete(int $userId, int $id): string
    {
        try {
            $countdown = $this->countdownManager->delete($userId, $id);
            return $countdown->GetData('Name');
        } catch (CountdownNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'countdown-delete-failed',
                t('Countdown::Delete::Error::The Countdown does not exist anymore.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }
    }

    /**
     * @return CountdownTemplateOptionValueObject[]
     */
    public function getTemplateOptions(): array
    {
        return [
            CountdownTemplateOptionValueObject::create(
                'round_orange',
                t('Countdown::Template::Option::orange'),
                $this->getPreviewSrc('round_orange')
            ),
            CountdownTemplateOptionValueObject::create(
                'round_purple',
                t('Countdown::Template::Option::purple'),
                $this->getPreviewSrc('round_purple')
            ),
            CountdownTemplateOptionValueObject::create(
                'round_blue',
                t('Countdown::Template::Option::blue'),
                $this->getPreviewSrc('round_blue')
            ),
            CountdownTemplateOptionValueObject::create(
                'round_green',
                t('Countdown::Template::Option::green'),
                $this->getPreviewSrc('round_green')
            ),
            CountdownTemplateOptionValueObject::create(
                'round_lightgreen',
                t('Countdown::Template::Option::lightgreen'),
                $this->getPreviewSrc('round_lightgreen')
            ),
            CountdownTemplateOptionValueObject::create(
                'round_turquoise',
                t('Countdown::Template::Option::turquoise'),
                $this->getPreviewSrc('round_turquoise')
            ),
            CountdownTemplateOptionValueObject::create(
                'round_grey',
                t('Countdown::Template::Option::grey'),
                $this->getPreviewSrc('round_grey')
            )
        ];
    }

    /**
     * @return ApiResponseOptionIntValueObject[]
     */
    public function getTerminationTypeOptions(): array
    {
        return [
            ApiResponseOptionIntValueObject::create(
                ToolCountdown::TERMINATION_TYPE_FIXED,
                t('Countdown::TerminationType::Option::Fixed date')
            ),
            ApiResponseOptionIntValueObject::create(
                ToolCountdown::TERMINATION_TYPE_FROM_CUSTOMFIELD,
                t('Countdown::TerminationType::Option::Date from custom field')
            ),
            ApiResponseOptionIntValueObject::create(
                ToolCountdown::TERMINATION_TYPE_FROM_TAGGING,
                t('Countdown::TerminationType::Option::Date of tagging')
            )
        ];
    }

    /**
     * @return ApiResponseOptionStringValueObject[]
     */
    public function getTagCategoryOptions(): array
    {
        /** @var ApiResponseOptionStringValueObject[] $tagCategoryOptions */
        $tagCategoryOptions = [];

        foreach (Tag::$DisplayCategoryTypes as $category => $label) {
            $tagCategoryOptions[] = ApiResponseOptionStringValueObject::create(
                TagCategoryAll::$APIIndexFilterTypes[$category],
                t('Filter: SmartTag >> @tag', array('@tag' => t(/*ignore*/ 'action:' . $label)))
            );
        }

        return $tagCategoryOptions;
    }

    /**
     * @throws DatabaseException
     */
    public function checkLimitReached(int $userId): int
    {
        return $this->countdownManager->checkLimitReached($userId);
    }

    private function getPreviewSrc(string $template): string
    {
        return Settings::get('marketing_tools_emailcountdown_s3url') . "/$template/1/{$template}_120.gif";
    }

    /**
     * @throws DatabaseException
     */
    private function updateFromRequest(
        ToolCountdown $countdown,
        CountdownUpdateRequestValueObject $data,
        int $userId
    ): ToolCountdown {
        $countdown->set('Name', $data->name);
        $countdown->set('Notes', $data->notes);
        $countdown->set('MetaLabels', $data->metaLabels);
        $countdown->set('Template', $data->template);

        $terminationType = $data->terminationType;
        $countdown->set('TerminationType', $terminationType);

        $tagDuration = 0;
        $tagDurationDays = (int)$data->terminationTagDurationDays;
        $tagDurationHours = (int)$data->terminationTagDurationHours;

        if (!empty($tagDurationDays) && !empty($tagDurationHours)) {
            $tagDuration = $tagDurationDays * 86400;
            $tagDuration += $tagDurationHours * 3600;
        }

        switch ($terminationType) {
            case ToolCountdown::TERMINATION_TYPE_FIXED:
                $terminationDateTime = strtotime((string)$data->terminationDateTime);
                $countdown->set('TerminationDateTime', $terminationDateTime);
                $countdown->set('TerminationCustomFieldID', '');
                $countdown->set('TerminationTagIDs', []);
                $countdown->set('TerminationTagDuration', 0);
                break;
            case ToolCountdown::TERMINATION_TYPE_FROM_TAGGING:
                if (isset($data->terminationTagIDs)) {
                    $terminationTagIDs = $data->terminationTagIDs->ids ?: [];
                    if (!empty($data->terminationTagIDs->new)) {
                        $newTerminationTagIDs = $this->tagManager->autoCreateTagsByName(
                            $userId,
                            $data->terminationTagIDs->new
                        );
                        if (!empty($newTerminationTagIDs)) {
                            $terminationTagIDs = array_merge($terminationTagIDs, $newTerminationTagIDs);
                        }
                    }
                    $countdown->set('TerminationTagIDs', $terminationTagIDs);
                }
                $countdown->set('TerminationTagDuration', $tagDuration);
                $countdown->set('TerminationDateTime', 0);
                $countdown->set('TerminationCustomFieldID', '');
                break;
            case ToolCountdown::TERMINATION_TYPE_FROM_CUSTOMFIELD:
                $countdown->set('TerminationCustomFieldID', $data->terminationCustomFieldID);
                $countdown->set('TerminationDateTime', 0);
                $countdown->set('TerminationTagIDs', []);
                $countdown->set('TerminationTagDuration', 0);
                break;
        }

        $countdown->set('CustomExpiredImage', $data->customExpiredImage);
        $countdown->set('UseCustomExpiredImage', empty($data->customExpiredImage) ? 0 : 1);
        $countdown->set('TargetURL', $data->targetURL);
        $countdown->set('UseTargetURL', empty($data->targetURL) ? 0 : 1);
        $countdown->set('TargetURLExpired', $data->targetURLExpired);
        $countdown->set('SubscriberParameterName', $data->subscriberParameterName);
        $countdown->set('UseSubscriberParameter', empty($data->subscriberParameterName) ? 0 : 1);
        $countdown->set('EmailParameterName', $data->emailParameterName);
        $countdown->set('UseEmailParameter', empty($data->emailParameterName) ? 0 : 1);

        return $countdown;
    }
}

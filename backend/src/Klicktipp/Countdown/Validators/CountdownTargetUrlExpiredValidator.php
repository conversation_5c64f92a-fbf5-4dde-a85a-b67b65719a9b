<?php

namespace App\Klicktipp\Countdown\Validators;

use App\Klicktipp\BlacklistHandler;
use App\Klicktipp\Countdown\Validators\Exception\CountdownValidationEmptyTargetUrlException;
use App\Klicktipp\Countdown\Validators\Exception\CountdownValidationTargetUrlExpiredBlacklistException;
use App\Klicktipp\Countdown\Validators\Exception\CountdownValidationTargetUrlExpiredException;
use App\Klicktipp\ToolCountdown;

class CountdownTargetUrlExpiredValidator implements CountdownValidatorInterface
{
    public function validate(ToolCountdown $entity): void
    {
        $targetUrl = $entity->GetData('TargetURL');

        if (empty($targetUrl)) {
            throw new CountdownValidationEmptyTargetUrlException();
        }

        $targetUrlExpired = $entity->GetData('TargetURLExpired');

        if (!valid_url($targetUrlExpired, true)) {
            throw new CountdownValidationTargetUrlExpiredException();
        } elseif (BlacklistHandler::isUrlBlacklisted($targetUrlExpired)) {
            throw new CountdownValidationTargetUrlExpiredBlacklistException();
        }
    }

    public function eligible(ToolCountdown $entity): bool
    {
        return !empty($entity->GetData('TargetURLExpired'));
    }
}

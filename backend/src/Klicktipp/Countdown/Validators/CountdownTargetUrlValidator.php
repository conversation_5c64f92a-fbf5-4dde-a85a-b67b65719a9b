<?php

namespace App\Klicktipp\Countdown\Validators;

use App\Klicktipp\BlacklistHandler;
use App\Klicktipp\Countdown\Validators\Exception\CountdownValidationTargetUrlBlacklistException;
use App\Klicktipp\Countdown\Validators\Exception\CountdownValidationTargetUrlException;
use App\Klicktipp\ToolCountdown;

class CountdownTargetUrlValidator implements CountdownValidatorInterface
{
    public function validate(ToolCountdown $entity): void
    {
        $targetUrl = $entity->GetData('TargetURL');

        if (!valid_url($targetUrl, true)) {
            throw new CountdownValidationTargetUrlException();
        } elseif (BlacklistHandler::isUrlBlacklisted($targetUrl)) {
            throw new CountdownValidationTargetUrlBlacklistException();
        }
    }

    public function eligible(ToolCountdown $entity): bool
    {
        return !empty($entity->GetData('TargetURL'));
    }
}

<?php

namespace App\Klicktipp\Countdown\ValueObject;

use App\Klicktipp\AngularApi\ValueObject\Factory\MagicSelectItemFactory;
use App\Klicktipp\AngularApi\ValueObject\MagicSelectItemValueObject;
use App\Klicktipp\ToolCountdown;

final class CountdownEntityValueObject
{
    public string $id;
    public string $name;
    public string $notes;
    public string $type;
    /** @var array<string> */
    public array $metaLabels;
    public string $template;
    public int $terminationType;
    public ?string $terminationDateTime;
    public ?string $terminationCustomFieldID;
    public ?MagicSelectItemValueObject $terminationTagIDs;
    public ?int $terminationTagDurationDays;
    public ?int $terminationTagDurationHours;
    public ?string $customExpiredImage;
    public ?string $targetURL;
    public ?string $targetURLExpired;
    public ?string $subscriberParameterName;
    public ?string $emailParameterName;

    public CountdownEntityLinksValueObject $links;

    /**
     * @param ToolCountdown $entity
     * @param CountdownEntityLinksValueObject $links
     * @param array<string> $metaLabels
     * @return self
     */
    public static function create(
        ToolCountdown $entity,
        CountdownEntityLinksValueObject $links,
        array $metaLabels = []
    ): self {
        $tagIds = $entity->GetData('TerminationTagIDs') ?? [];
        $magicSelectItem = null;
        if (!empty($tagIds)) {
            $magicSelectItem = MagicSelectItemFactory::create(['ids' => [$tagIds], 'new' => []]);
        }

        $terminationDateTime = $entity->GetData('TerminationDateTime');

        $terminationTagDuration = (int)$entity->GetData('TerminationTagDuration');
        $terminationTagDurationDays = 0;
        $terminationTagDurationHours = 1;

        if (!empty($terminationTagDuration)) {
            $terminationTagDurationDays = (int)($terminationTagDuration / 86400);
            $terminationTagDurationHours = (int)($terminationTagDuration % 86400 / 3600);
        }

        $valueObject = new self();
        $valueObject->id = $entity->GetData('ToolID');
        $valueObject->name = $entity->GetData('Name');
        $valueObject->notes = $entity->GetData('Notes') ?? '';
        $valueObject->metaLabels = $metaLabels;
        $valueObject->template = $entity->GetData('Template') ?? '';
        $valueObject->terminationType = $entity->GetData('TerminationType') ?? 0;
        $valueObject->terminationDateTime = $terminationDateTime ? date('Y-m-d H:i:00', $terminationDateTime) : null;
        $valueObject->terminationCustomFieldID = $entity->GetData('TerminationCustomFieldID') ?? null;
        $valueObject->terminationTagIDs = $magicSelectItem;
        $valueObject->terminationTagDurationDays = $terminationTagDurationDays;
        $valueObject->terminationTagDurationHours = $terminationTagDurationHours;
        $valueObject->customExpiredImage = $entity->GetData('CustomExpiredImage') ?? null;
        $valueObject->targetURL = $entity->GetData('TargetURL') ?? null;
        $valueObject->targetURLExpired = $entity->GetData('TargetURLExpired') ?? null;
        $valueObject->subscriberParameterName = $entity->GetData('SubscriberParameterName') ?? null;
        $valueObject->emailParameterName = $entity->GetData('EmailParameterName') ?? null;
        $valueObject->type = ToolCountdown::$APIIndexFilterTypes[ToolCountdown::$ToolType];
        $valueObject->links = $links;

        return $valueObject;
    }
}

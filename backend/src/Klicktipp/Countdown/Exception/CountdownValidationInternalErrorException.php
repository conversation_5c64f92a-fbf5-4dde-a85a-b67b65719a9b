<?php

namespace App\Klicktipp\Countdown\Exception;

use App\Klicktipp\Countdown\Validators\Exception\CountdownValidationException;
use App\Klicktipp\Countdown\Validators\ValueObject\CountdownValidationItemValueObject;

class CountdownValidationInternalErrorException extends CountdownValidationException
{
    public function __construct()
    {
        parent::__construct('Countdown::Update::Internal error');
        $this->validationData[] = CountdownValidationItemValueObject::create(
            'countdown-internal-error',
            CountdownValidationItemValueObject::TYPE_ERROR,
            t('Countdown::Validation::Error::An internal server error occurred')
        );
    }

    public function getTranslationKey(): string
    {
        return /*t(*/'Countdown::Validation::Error::Internal error.'/*)*/;
    }

    public function getExceptionIdentifier(): string
    {
        return 'countdown-internal-error';
    }
}

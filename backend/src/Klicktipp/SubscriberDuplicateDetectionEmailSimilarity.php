<?php

namespace App\Klicktipp;

use DrupalQueue;
use Elasticsearch\Common\Exceptions\Missing404Exception;
use Exception;
use Generator;

class SubscriberDuplicateDetectionEmailSimilarity extends SubscriberDuplicateDetectionAbstract
{
    /**
     * @var int
     */
    const TYPE = 3;

    private const CRITERION_SAME_LOCALPART = 'same-localpart';
    private const CRITERION_SAME_LOCALPART_PLUS = 'same-localpart-plus';
    private const CRITERION_SIMILAR_LOCALPART = 'similar-localpart';
    private const CRITERION_GOOGLE_ALIAS = 'google-alias';

    /**
     * Maps criteria to probability
     *
     * NOTE: map is ordered, so criteria with higher probability come first
     *
     * @var array
     */
    private static $criterionProbabilityMap = [
        self::CRITERION_GOOGLE_ALIAS => SubscriberDuplicateEvents::PROBABILITY_MAXIMUM,
        self::CRITERION_SAME_LOCALPART_PLUS => SubscriberDuplicateEvents::PROBABILITY_VERY_HIGH,
        self::CRITERION_SAME_LOCALPART => SubscriberDuplicateEvents::PROBABILITY_HIGH,
        self::CRITERION_SIMILAR_LOCALPART => SubscriberDuplicateEvents::PROBABILITY_MIDDLE
    ];

    /**
     * @var \nodespark\DESConnector\ClientInterface
     */
    private static $esClient;

    /**
     * @var string
     */
    private static $esIndex;

    /**
     * @var string[] domains going to same gmail-mailbox in case of same localpart
     */
    private static $goolgeDomains = ['gmail.com', 'googlemail.com'];

    /**
     * Triggers duplicate detection by checking similar email addresses
     *
     * If email similarity is configured to run  asynchronous, this method puts a message into
     * related queue and detection is processed by a queue worker in a background job. Otherwise
     * detection logic is executed immediately
     *
     * @param array $parameters
     */
    public static function triggerDetecttion(array $parameters)
    {
        if (!variable_get('klicktipp_duplicate_asynchronous_email_similarity_detection', true)) {
            static::detectAndStore($parameters);
            return;
        }

        $queue = DrupalQueue::get('email_similarity');
        $queue->createQueue();
        $queue->createItem($parameters);
    }


    /**
     * Detects duplicates by email similarity
     *
     * @param array $parameters expected keys are userID, subscriberID and email
     * @return Generator
     */
    public static function detectDuplicates(array $parameters)
    {
        $client = static::getClient();

        // elasticsearch connector not yet configured
        if (!$client) {
            return yield from [];
        }

        $userID = $parameters['userID'];
        $subscriberID = $parameters['subscriberID'];
        $email = $parameters['email'];
        $lastAt = strrpos($email, '@');
        $localpart = substr($email, 0, $lastAt);
        $domain = substr($email, $lastAt + 1);
        $localpartWithoutPlus = substr($localpart, 0, strrpos($localpart, '+') ?: PHP_INT_MAX);

        //
        if (in_array($domain, static::$goolgeDomains)) {
            $domainFilter = ['terms' => ['domain' => static::$goolgeDomains]];
            $prefixCriterion = static::CRITERION_GOOGLE_ALIAS;
        } else {
            $domainFilter = ['term' => ['domain' => $domain]];
            $prefixCriterion = static::CRITERION_SAME_LOCALPART_PLUS;
        }

        $searchParams = [
            'index' => static::$esIndex,
            'body' => [
                'query' => [
                    'bool' => [
                        'filter' => [
                            'term' => ['user_id' => $userID]
                        ],
                        'must_not' => [
                            'term' => ['subscriber_id' => $subscriberID]
                        ],
                        'should' => [

                            // exact localpart match
                            [
                                'term' => [
                                    'localpart' => [
                                        '_name' => static::CRITERION_SAME_LOCALPART,
                                        'value' => $localpart
                                    ]
                                ]
                            ],

                            // similar localpart. we compare the whole localpart as well as tokens.
                            // Tokens allow us eg. to detect similarity between "first.second" and "second.first"
                            [
                                'multi_match' => [
                                    '_name' => static::CRITERION_SIMILAR_LOCALPART,
                                    'fields' => ['localpart', 'localpart-tokenized'],
                                    'query' => $localpart,
                                    'fuzziness' => 'AUTO',
                                    'minimum_should_match' => '100%'
                                ]
                            ],

                            // plus aliases. exact handling differs between google and others
                            [
                                'bool' => [
                                    'filter' => [
                                        $domainFilter,
                                    ],
                                    'should' => [
                                        // if <NAME_EMAIL>, find <EMAIL>
                                        ['term' => ['localpart' => $localpartWithoutPlus]],
                                        // if <NAME_EMAIL> or <EMAIL>, find <EMAIL>
                                        ['prefix' => ['localpart' => $localpartWithoutPlus . '+']],
                                    ],
                                    "minimum_should_match" => 1,
                                    "_name" => $prefixCriterion
                                ]
                            ]
                        ],
                        "minimum_should_match" => 1
                    ]
                ]
            ]
        ];
        try {
            //$json = json_encode($searchParams, JSON_PRETTY_PRINT);
            $response = $client->search($searchParams);
        } catch (Missing404Exception $e) {
            watchdog(
                'kt-email-similartiy',
                'index %index not found. disable email similarity detection',
                ['index' => static::$esIndex],
                WATCHDOG_ERROR
            );
            static::disable();
            return yield from [];
        } catch (Exception $e) {
            watchdog('kt-email-similarity', 'unexpected exception', ['index' => static::$esIndex], WATCHDOG_ERROR);
            return yield from [];
        }

        foreach ($response->getRawResults() as $hit) {
            $probability = static::calculateProbabilityByEsResult($hit, $domain);
            $source = $hit['_source'];
            $duplicateSubscriberID = $source['subscriber_id'];

            $buildData = fn($email, $dupEmail) => [
                'similar_emails' => [
                    [
                        'email' => $email,
                        'dupEmail' => $dupEmail,
                        // in 'probability'-column we store just highest probability
                        // in additional data we store probability per similar email pair
                        // (for debug purposes right now; maybe we need it for more in future)
                        'probability' => $probability
                    ]
                ]
            ];

            yield static::createEvent(
                $userID,
                $subscriberID,
                $duplicateSubscriberID,
                $probability,
                $buildData($email, $source['email'])
            );


            yield static::createEvent(
                $userID,
                $duplicateSubscriberID,
                $subscriberID,
                $probability,
                $buildData($source['email'], $email)
            );
        }
    }

    /**
     * @param array $hit single hit of elasticsearch query
     * @param string $domain domain part of email address
     */
    private static function calculateProbabilityByEsResult(array $hit, $domain)
    {
        // 'matched_queries' is supposed to be set
        foreach (static::$criterionProbabilityMap as $criterion => $probability) {
            if (!in_array($criterion, $hit['matched_queries'])) {
                continue;
            }

            // same mailbox in google (+ aliases or gmail <=> googlemail)
            if (
                in_array($criterion, [static::CRITERION_SAME_LOCALPART, static::CRITERION_SAME_LOCALPART_PLUS])
                && in_array($domain, static::$goolgeDomains) && in_array(
                    $hit['_source']['domain'],
                    static::$goolgeDomains
                )
            ) {
                return SubscriberDuplicateEvents::PROBABILITY_MAXIMUM;
            }

            return $probability;
        }

        // fallback, just for case of some index misconfiguration etc.
        return SubscriberDuplicateEvents::PROBABILITY_MIDDLE;
    }

    private static function getClient()
    {
        if (static::$esClient === null) {
            $cluster = variable_get('klicktipp_duplicate_detection_es_cluster');
            $index = variable_get('klicktipp_duplicate_detection_es_index');
            if (empty($cluster) || empty($index)) {
                return static::$esClient = false;
            }
            $client = elasticsearch_connector_get_client_by_id($cluster);
            if (empty($client)) {
                return static::$esClient = false;
            }
            static::$esClient = $client;
            static::$esIndex = $index;
        }

        return static::$esClient;
    }
}

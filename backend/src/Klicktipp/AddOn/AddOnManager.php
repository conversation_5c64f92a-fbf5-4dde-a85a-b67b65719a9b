<?php

namespace App\Klicktipp\AddOn;

use App\Digistore\DigistoreApi;
use App\Digistore\DigistoreApiException;
use App\Klicktipp\AccountManager;
use App\Klicktipp\AddOn\Exception\AddOnContingentExceededException;
use App\Klicktipp\AddOn\Exception\AddOnControllerException;
use App\Klicktipp\AddOn\Exception\AddOnEntityAddOnNotRemovableException;
use App\Klicktipp\AddOn\Exception\AddOnEntityAlreadyActivatedException;
use App\Klicktipp\AddOn\Exception\AddOnEntityNotElegibleException;
use App\Klicktipp\AddOn\Exception\AddOnInvalidOptionException;
use App\Klicktipp\AddOn\Exception\AddOnNotAvailableForAccountException;
use App\Klicktipp\AddOn\Exception\AddOnNotFoundException;
use App\Klicktipp\AddOn\ValueObject\AddOnAccountDetailsValueObject;
use App\Klicktipp\AddOn\ValueObject\AddOnOption;
use App\Klicktipp\AddOn\ValueObject\AddOnUpsellPageValueObject;
use App\Klicktipp\AddonEntity\AddonDbEntityHandler;
use App\Klicktipp\AddonEntity\AddonEntity;
use App\Klicktipp\AddonEntity\AddonEntityException;
use App\Klicktipp\AddonEntity\AddonEntityHandlerInterface;
use App\Klicktipp\Digistore\CloseCrmAddonClient;
use App\Klicktipp\Digistore\DigistoreAccountClient;
use App\Klicktipp\Digistore\DigistorePaymentClient;
use App\Klicktipp\Digistore\Exception\DigistoreInvalidPaymentDateException;
use App\Klicktipp\Digistore\Exception\DigistoreInvalidPaymentIntervalException;
use App\Klicktipp\Digistore\Exception\DigistoreMissingOrderException;
use App\Klicktipp\Digistore\Exception\DigistorePaymentIntervalMissmatchException;
use App\Klicktipp\Digistore\Exception\DigistoreUnsupportedAddonException;
use App\Klicktipp\Digistore\LandingPageAddonClient;
use App\Klicktipp\Digistore\ValueObject\DigistoreAccountDetailsValueObject;
use App\Klicktipp\LandingPage\LandingPageAddOnPlusManager;
use App\Klicktipp\LandingPage\LandingPageLinkResolver;
use App\Klicktipp\Plugins\CloseCrmAddOnManager;
use Exception;
use stdClass;
use Symfony\Component\HttpFoundation\Response;

class AddOnManager
{
    public const ADDON_LANDINGPAGE_PLUS = 'landing-page-plus';
    public const ADDON_CLOSECRM = 'closecrm'; // PluginID

    /** @var array<string, AddOnManagerInterface> */
    private array $addOnManagers = [];
    private DigistoreAccountClient $digistoreAccountClient;
    private AddonEntityHandlerInterface $addonEntityHandler;

    public function __construct(
        LandingPageAddOnPlusManager $landingPageAddOnPlusManager,
        CloseCrmAddOnManager $closeAddOnManager,
        DigistoreAccountClient $digistoreClient,
        AddonEntityHandlerInterface $addonEntityHandler
    ) {
        $this->addAddOnManager($landingPageAddOnPlusManager);
        $this->addAddOnManager($closeAddOnManager);
        $this->digistoreAccountClient = $digistoreClient;
        $this->addonEntityHandler = $addonEntityHandler;
    }

    // Note: for use in Drupal (e.g. plugins for now)
    public static function noInjectionFactory(): self
    {
        $digistoreApi = DigistoreApi::connect(getenv('KLICKTIPP_DIGISTORE_API_KEY'));
        $paymentClient = new DigistorePaymentClient($digistoreApi);
        $addonEntityHandler = new AddonDbEntityHandler();
        return new self(
            new LandingPageAddOnPlusManager(
                new LandingPageLinkResolver(),
                new LandingPageAddonClient($digistoreApi, $paymentClient),
                $addonEntityHandler
            ),
            new CloseCrmAddOnManager(
                new CloseCrmAddonClient($digistoreApi, $paymentClient),
                $addonEntityHandler
            ),
            new DigistoreAccountClient($digistoreApi, $paymentClient),
            $addonEntityHandler
        );
    }

    private function addAddOnManager(AddOnManagerInterface $addOnManager): void
    {
        $this->addOnManagers[$addOnManager->getId()] = $addOnManager;
    }

    /**
     * @throws AddOnNotFoundException
     */
    public function getAddOnManager(string $addOnId): AddOnManagerInterface
    {
        if (!isset($this->addOnManagers[$addOnId])) {
            throw new AddOnNotFoundException($addOnId);
        }
        return $this->addOnManagers[$addOnId];
    }

    public function getUpsellPageUrl(string $addOnName, int $userId, int $entityId): string
    {
        return APP_URL . "app/addon/$addOnName/$userId/$entityId";
    }

    public function getSuccessUrl(string $addOnId, int $userId, int $entityId): string
    {
        return $this->getAddOnManager($addOnId)->getRedirectUrls($userId, $entityId)->success;
    }

    /**
     * @throws AddOnControllerException
     */
    public function getUpsellPage(string $addOnId, stdClass $account, int $entityId): AddOnUpsellPageValueObject
    {
        try {
            if (!$this->isAvailable($account)) {
                throw new AddOnNotAvailableForAccountException($addOnId, $account);
            }

            $addOn = $this->getAddOnManager($addOnId);

            $accountDetails = $this->digistoreAccountClient->getAccountDetails($account);
            $upsellOptions = $addOn->getUpsellOptions($accountDetails, $account);

            global $user;
            $isAdmin = AccountManager::isAdmin($user);

            return $addOn->getUpsellPage(
                $this->getAccountDetails($accountDetails),
                $upsellOptions,
                $account,
                $entityId,
                $isAdmin
            );
        } catch (AddOnNotFoundException $e) {
            throw new AddOnControllerException(
                $e->getMessage(),
                'add-on-not-found',
                t('AddOn::UpsellPage::Error::The requested AddOn could not be found.'),
                Response::HTTP_NOT_FOUND
            );
        } catch (
            DigistoreMissingOrderException |
            DigistorePaymentIntervalMissmatchException |
            DigistoreApiException |
            DigistoreInvalidPaymentIntervalException |
            DigistoreInvalidPaymentDateException $e
        ) {
            throw new AddOnControllerException(
                $e->getMessage(),
                'add-on-config-error',
                t('AddOn::UpsellPage::Error::This AddOn is not available for your account.'),
                Response::HTTP_FORBIDDEN
            );
        } catch (AddOnNotAvailableForAccountException $e) {
            throw new AddOnControllerException(
                $e->getMessage(),
                'addons-not-available',
                t('AddOn::UpsellPage::Error::AddOns are not available for this account.'),
                Response::HTTP_FORBIDDEN
            );
        }
    }

    /**
     * @param stdClass $account
     * @return array<string, AddOnOption[]>
     */
    public function getAccountUpsellOptions(stdClass $account): array
    {
        $accountDetails = $this->digistoreAccountClient->getAccountDetails($account);
        return $this->getAllUpsellOptions($accountDetails, $account);
    }

    /**
     * @param stdClass $account
     * @return array<string, AddOnOption[]>
     */
    public function getAllUpsellOptions(DigistoreAccountDetailsValueObject $accountDetails, stdClass $account): array
    {
        return array_map(fn($m) => $m->getUpsellOptions($accountDetails, $account), $this->addOnManagers);
    }

    //Note: this function is also used to validate upgrade request data
    //      the user could have multiple tabs with the upsell dialog open
    public function getAccountDetails(
        DigistoreAccountDetailsValueObject $digistoreAccountDetails
    ): AddOnAccountDetailsValueObject {

        switch ($digistoreAccountDetails->getTerm()) {
            case '1_month':
                $term = t('DigiStore::Payment::Interval::monthly');
                break;
            case '12_month':
                $term = t('DigiStore::Payment::Interval::annually');
                break;
            case '24_month':
                $term = t('DigiStore::Payment::Interval::biannually');
                break;
            default:
                $term = '';
        }

        return AddOnAccountDetailsValueObject::create(
            $digistoreAccountDetails->getProductName(),
            $digistoreAccountDetails->getTier(),
            $term,
            $digistoreAccountDetails->getPrice(),
            $digistoreAccountDetails->getPriceMonthly(),
            $digistoreAccountDetails->getNextPaymentDate()->getTimestamp()
        );
    }

    /**
     * @param string $addOnId
     * @param stdClass $account
     * @param AddOnOption $option
     *
     * @return void
     * @throws AddOnControllerException
     */
    public function upgrade(
        string $addOnId,
        stdClass $account,
        AddOnOption $option,
        int $entityId = 0
    ): void {
        $logException = fn($e) =>
            watchdog(
                'addons',
                'Exception while upgrading AddOn !addOnId for account !userId',
                ['!addOnId' => $addOnId, '!userId' => $account->uid, '@exception' => $e->__toString()],
                WATCHDOG_ERROR
            );
        try {
            if (!$this->isAvailable($account)) {
                throw new AddOnNotAvailableForAccountException($addOnId, $account);
            }

            $addOn = $this->getAddOnManager($addOnId);

            $availableOptions = $addOn->getUpsellOptions(
                $this->digistoreAccountClient->getAccountDetails($account),
                $account
            );

            $this->validateOption($addOnId, $option, $availableOptions);

            $addOn->saveAddon($account, $option->id);

            $addOn->onUpgrade($account, $entityId);
        } catch (
            DigistoreMissingOrderException |
            DigistorePaymentIntervalMissmatchException |
            DigistoreInvalidPaymentIntervalException |
            DigistoreUnsupportedAddonException |
            DigistoreApiException |
            DigistoreInvalidPaymentDateException |
            AddOnNotFoundException $e
        ) {
            $logException($e);
            throw new AddOnControllerException(
                $e->getMessage(),
                'upgrade-failed',
                t('AddOn::UpsellPage::Error::The requested AddOn could not be added.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        } catch (AddOnInvalidOptionException $e) {
            $logException($e);
            throw new AddOnControllerException(
                $e->getMessage(),
                'invalid-option',
                t('AddOn::UpsellPage::Error::The form is outdated, please try again.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        } catch (AddOnNotAvailableForAccountException $e) {
            $logException($e);
            throw new AddOnControllerException(
                $e->getMessage(),
                'addons-not-available',
                t('AddOn::UpsellPage::Error::AddOns are not available for this account.'),
                Response::HTTP_FORBIDDEN
            );
        }
    }

    /**
     * @param string $addOnId
     * @param AddOnOption $option
     * @param AddOnOption[] $availableOptions
     * @return void
     * @throws AddOnInvalidOptionException
     */
    public function validateOption(string $addOnId, AddOnOption $option, array $availableOptions): void
    {
        foreach ($availableOptions as $availableOption) {
            if ($availableOption->id === $option->id) {
                if (
                    $availableOption->name !== $option->name ||
                    $availableOption->description !== $option->description ||
                    $availableOption->amount !== $option->amount ||
                    $availableOption->instantPayment !== $option->instantPayment ||
                    $availableOption->price !== $option->price ||
                    $availableOption->newPrice !== $option->newPrice ||
                    $availableOption->newPriceMonthly !== $option->newPriceMonthly
                ) {
                    throw new AddOnInvalidOptionException($addOnId, $option, $availableOptions);
                }
                return;
            }
        }
        throw new AddOnInvalidOptionException($addOnId, $option, $availableOptions);
    }

    /**
     * @param string $addOnId
     * @param stdClass $account
     *
     * @return int
     *
     * @throws AddOnNotFoundException
     */
    public function getContingent(string $addOnId, stdClass $account): int
    {
        return $this->getAddOnManager($addOnId)->getContingent($account);
    }

    /**
     * @param string $addOnId
     * @param int $userId
     * @return int
     * @throws AddOnNotFoundException
     * @throws AddonEntityException
     */
    public function getUsage(string $addOnId, int $userId): int
    {
        $addOnType = $this->getAddOnManager($addOnId)->getAddonType();
        return $this->addonEntityHandler->getUsage($addOnType, $userId);
    }

    /**
     * @throws AddOnEntityAlreadyActivatedException
     * @throws AddOnEntityNotElegibleException
     * @throws AddOnContingentExceededException
     * @throws AddOnNotFoundException
     * @throws AddonEntityException
     */
    public function activateAddOnForEntity(
        string $addOnId,
        stdClass $account,
        int $entityId,
        int $entityType,
        bool $forAdmin = false
    ): void {

        $addOnType = $this->getAddOnManager($addOnId)->getAddonType();

        $addOnStatus = $this->addonEntityHandler->getStatus($account->uid, $addOnType, $entityType, $entityId);

        if ($addOnStatus === AddonEntity::STATUS_NONE) {
            throw new AddOnEntityNotElegibleException($addOnId, $entityId, (string)$entityType);
        }

        if ($addOnStatus === AddonEntity::STATUS_COMPLETE) {
            throw new AddOnEntityAlreadyActivatedException($addOnId, $entityId, (string)$entityType);
        }

        $usage = $this->getUsage(
            $addOnId,
            $account->uid
        );

        $contingent = $this->getContingent(
            $addOnId,
            $account
        );

        if ($usage >= $contingent) {
            throw new AddOnContingentExceededException($addOnId, $contingent, $usage);
        }

        $this->addonEntityHandler->setStatus(
            $account->uid,
            $addOnType,
            $entityType,
            $entityId,
            $forAdmin ? AddonEntity::STATUS_COMPLETE_ADMIN : AddonEntity::STATUS_COMPLETE
        );
    }

    /**
     * @throws AddOnEntityAddOnNotRemovableException
     * @throws AddOnNotFoundException
     */
    public function removeAddOnForEntity(
        string $addOnId,
        int $userId,
        int $entityId,
        int $entityType
    ): void {
        $addOnType = $this->getAddOnManager($addOnId)->getAddonType();
        $addOnStatus = $this->getAddOnStatusForEntity(
            $addOnId,
            $userId,
            $entityId,
            $entityType
        );

        if ($addOnStatus !== AddonEntity::STATUS_INCOMPLETE) {
            throw new AddOnEntityAddOnNotRemovableException(
                $addOnId,
                $entityId,
                (string)$entityType,
                $addOnStatus
            );
        }
        $this->addonEntityHandler->remove($userId, $addOnType, $entityType, $entityId);
    }

    /**
     * @param stdClass $account
     *
     * @return array<string, array<int, int>>
     */
    public function listOrderedProducts(stdClass $account): array
    {
        // filter out products with order quantity 0
        $result = array_map(fn($m) => array_filter($m->getOrderedQuantities($account)), $this->addOnManagers);
        // filter out addons without ordered products
        return array_filter($result);
    }

    /**
     * @throws AddonEntityException
     * @throws AddOnNotFoundException
     */
    public function setAddonStatusOfEntity(
        string $addOnId,
        int $userId,
        int $entityId,
        int $entityType,
        int $status
    ): void {
        $addOnType = $this->getAddOnManager($addOnId)->getAddonType();
        $this->addonEntityHandler->setStatus($userId, $addOnType, $entityType, $entityId, $status);
    }

    /**
     * @param string $addOnId
     * @param int $userId
     * @param int $entityId
     * @param int $entityType
     * @return int
     */
    public function getAddOnStatusForEntity(
        string $addOnId,
        int $userId,
        int $entityId = 0,
        int $entityType = AddonEntity::ENTITY_TYPE_NOENTITY
    ): int {
        try {
            $addOnType = $this->getAddOnManager($addOnId)->getAddonType();
            return $this->addonEntityHandler->getStatus($userId, $addOnType, $entityType, $entityId);
        } catch (AddonEntityException | AddOnNotFoundException $e) {
            return AddonEntity::STATUS_NONE;
        }
    }

    /**
     * Handles unrecognized differences between user contingents and state of addon entities
     *
     * @throws AddonEntityException
     */
    public function syncEntitiesStatusIfNeeded(stdClass $account): void
    {
        // prevent expensive api calls
        if (!$this->addonEntityHandler->userHasAddOns($account->uid)) {
            return;
        }
        $this->syncEntitiesStatus($account);
    }

    /**
     * Handles unrecognized differences between user contingents and state of addon entities
     *
     * @throws AddonEntityException
     */
    public function syncEntitiesStatus(stdClass $account): void
    {
        foreach ($this->addOnManagers as $addOnManager) {
            $addOnType = $addOnManager->getAddonType();
            $contingent = $addOnManager->getContingent($account);
            $completed = $this->addonEntityHandler->countCompletedEntities($addOnType, $account->uid);

            if ($completed > $contingent) {
                // missing payment or some unsynced cancellation
                $entities = $this->addonEntityHandler
                    ->getEntitiesToCancel($addOnType, $account->uid, $completed - $contingent);
                foreach ($entities as $entity) {
                    $this->addonEntityHandler->setStatus(
                        $account->uid,
                        $addOnType,
                        $entity->getEntityType(),
                        $entity->getEntityId(),
                        AddonEntity::STATUS_CANCELED
                    );
                    watchdog(
                        'addons',
                        'canceled entity !id (user !userID)',
                        [
                            '!id' => $entity->getEntityId(),
                            '!userID' => $entity->getUserId(),
                            '@entity' => print_r($entity, true)
                        ]
                    );
                    $addOnManager->onCancel($entity);
                }
            } elseif ($contingent > $completed) {
                // there is a payment we didn't receive for some reason
                $entities = $this->addonEntityHandler
                    ->getEntitiesToComplete($addOnType, $account->uid, $contingent - $completed);
                foreach ($entities as $entity) {
                    $this->addonEntityHandler->setStatus(
                        $account->uid,
                        $addOnType,
                        $entity->getEntityType(),
                        $entity->getEntityId(),
                        AddonEntity::STATUS_COMPLETE
                    );
                    watchdog(
                        'addons',
                        'completed entity !id (user !userID)',
                        [
                            '!id' => $entity->getEntityId(),
                            '!userID' => $entity->getUserId(),
                            '@entity' => print_r($entity, true)
                        ],
                    );

                    $addOnManager->onComplete($entity);
                }
            }
        }
    }

    /**
     * @param stdClass $account
     *
     * @return bool
     */
    public function isAvailable(stdClass $account): bool
    {
        try {
            // throws exceptions if
            // - no amemberid is found
            // - no payment found
            // - product not found
            // - product is not a digistore product
            $this->digistoreAccountClient->getAccountDetails($account);
            return true;
        } catch (Exception $e) {
            watchdog(
                'addons',
                'Addons not available for user !userId',
                ['!userId' => $account->uid, '@exception' => $e->__toString()]
            );
            return false;
        }
    }
}

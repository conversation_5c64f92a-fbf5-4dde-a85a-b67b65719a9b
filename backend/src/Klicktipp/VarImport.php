<?php

namespace App\Klicktipp;

use App\Klicktipp\Includes\UtilsNumber;
use stdClass;

class VarImport extends UserVariables
{
    const STATUS_INIT = 'init';
    const STATUS_ASSIGN = 'assign';
    const STATUS_UPLOAD = 'upload';
    const STATUS_VALIDATING = 'validating';
    const STATUS_VALIDATED = 'validated';
    const STATUS_IMPORTING = 'importing';
    const STATUS_DONE = 'done';
    const STATUS_EXPIRED = 'expired';
    const STATUS_DENIED = 'denied';

    //Note: if other than private://, report download links might not work
    const FILE_ROOT = 'private://';
    // directory name in ./data/users (private://) where import files will be stored
    const ENV_DATA_USERS_DIRECTORY = KLICKTIPP_SUBSCRIBER_IMPORT_DATA_USERS_DIR;
    // a server script will delete files in the above directory after
    // the specified days + 5 days
    // Note: the import itself will be invalid for update after the specified days
    //       the 5 days is just a buffer to not delete to early
    const ENV_VALID_UNTIL_DAYS = KLICKTIPP_SUBSCRIBER_IMPORT_FILES_VALID_UNTIL_DAYS;

    const FILE_STATUS_UPLOADED = 0;
    const FILE_STATUS_VALIDATED = 1;
    const FILE_STATUS_IMPORTED = 2;

    const VALIDATION_OK = 0;
    const VALIDATION_WARNING = 1;
    const VALIDATION_ERROR = 2;
    const VALIDATION_TRASH_EMAIL = 3;
    const VALIDATION_API_WAITING = 4;
    const VALIDATION_CAUTION = 5;
    const VALIDATION_GLOBAL_BOUNCE = 6;
    const VALIDATION_HATER = 7;

    const IMPORT_WARNING = 1;
    const IMPORT_ERROR = 2;


    const EMAIL_VALIDATION_LEVEL_FORMAT = 'format';
    const EMAIL_VALIDATION_LEVEL_KLICKTIPP = 'klicktipp';
    const EMAIL_VALIDATION_LEVEL_EXTERNAL = 'MillionVerifier';

    //replace invalid values in the csv date with this indicator
    //to tag those subscribers with a user selected warning-tag
    const WARNING_TAG_INDICATOR = "d2FybmluZy10YWc=";

    //Note: this value determines the upload chunk size as well as
    //      the job size for validating and importing
    //      the value will be stored in the object, so changes will only
    //      apply for imports created after the change
    const CHUNK_SIZE = 100;

    const DETECT_FORMAT_MIN_SCORE = 50;

    /**
     * Any date older than this timestamp is considered invalid.
     */
    const MAX_DATETIME_TIMESTAMP = -62169987208;

    const ANGULAR_API_ERROR_FILE_REQUIRED = 'import::file_required';
    const ANGULAR_API_ERROR_STATUS = 'import::invalid_status';
    const ANGULAR_API_ERROR_NO_VALUES = 'import::no_values';
    const ANGULAR_API_ERROR_TOO_MANY_VALUES = 'import::too_many_values';
    const ANGULAR_API_ERROR_NO_FIELDS = 'import::no_fields';
    const ANGULAR_API_ERROR_INVALID_SESSION = 'import::invalid_session';
    const ANGULAR_API_ERROR_IMPORT_FAILED = 'import::import_failed';
    const ANGULAR_API_ERROR_EXPIRED = 'import::expired';
    const ANGULAR_API_ERROR_CHUNK_SIZE_EXCEEDED = 'import::chunk_size_exceeded';


    public static $VarType = UserVariables::VARTYPE_IMPORT;

    //Note: Adapted to make SeqNo the SerialID
    public static $DBSerialField = 'SeqNo';

    public static $APIPath = 'kt-import';

    public static $RequiredFieldsOnInsert = array(
        'SeqNo',
        'RelOwnerUserID',
        'VarType',
    );

    public static $DefaultDataFields = array(
        'Name' => '',
        'Filename' => '',
        'Status' => VarImport::STATUS_ASSIGN,
        'Delimiter' => '',
        'Enclosure' => '',
        'Header' => false,
        'HeaderRow1' => [], //@deprecated
        'HeaderRow2' => [], //@deprecated
        'ImportFields' => [],
        'ChunkSize' => VarImport::CHUNK_SIZE,
        'Session' => '',
        'CreatedOn' => 0,
        'ExpiresOn' => 0,
        'ValidationStartedOn' => 0,
        'ValidationFinishedOn' => 0,
        'ImportStartedOn' => 0,
        'ImportFinishedOn' => 0,
        'AssignTags' => [],
        'WarningTags' => [],
        'ListID' => 0,
        'ImportWarnings' => true,
        'SkipExternalValidation' => false,
        'NoSubscriptionImport' => false, // only to warn the user
        'Files' => [], //[<filename> => <file status>]
        'Stats' => [
            //'validation' => ['valid' => int, 'warnings' => int, 'errors' => int, 'download' => string]
            //'import' => ['success' => int, 'errors' => int, 'download' => string]
        ],
    );

    public static $APIFields = array(
        'SeqNo' => array(
            'id' => 'id',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'VarType' => array(
            'id' => 'type',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Name' => array(
            'id' => 'name',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Status' => array(
            'id' => 'status',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Filename' => array(
            'id' => 'file',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Delimiter' => array(
            'id' => 'delimiter',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Enclosure' => array(
            'id' => 'enclosure',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'Header' => array(
            'id' => 'header',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_BOOLEAN,
        ),
        'ImportFields' => array(
            'id' => 'importFields',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM, //assoc array
        ),
        'Session' => array(
            'id' => 'session',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_STRING,
        ),
        'ChunkSize' => array(
            'id' => 'chunkSize',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'CreatedOn' => array(
            'id' => 'createdOn',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'ExpiresOn' => array(
            'id' => 'expiresOn',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'AssignTags' => array(
            'id' => 'assignTags',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM, // number[]
        ),
        'WarningTags' => array(
            'id' => 'warningTags',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM, // number[]
        ),
        'ListID' => array(
            'id' => 'listID',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_INT,
        ),
        'ImportWarnings' => array(
            'id' => 'importWarnings',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_BOOLEAN,
        ),
        'SkipExternalValidation' => array(
            'id' => 'skipExternalValidation',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_BOOLEAN,
        ),
        'NoSubscriptionImport' => array(
            'id' => 'noSubscriptionImport',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_BOOLEAN,
        ),
        'Stats' => array(
            'id' => 'stats',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM, //assoc array
        ),
        'AccountTags' => array(
            'id' => 'accountTags',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM, //assoc array
        ),
        'AccountLists' => array(
            'id' => 'accountLists',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM, //assoc array
        ),
        'FieldDefinitions' => array(
            'id' => 'fieldDefinitions',
            'type' => DatabaseTableCRUD::API_FIELDTYPE_CUSTOM, //assoc array
        ),
    );

    //Note: Adapted to make SeqNo the SerialID
    public static function InsertDB($Data)
    {
        $tablename = '{' . static::$DBTableName . '}';
        $userfield = static::$DBUserField;
        $serialfield = static::$DBSerialField;

        $sql = "SELECT MAX($serialfield) FROM $tablename WHERE $userfield = :UserID AND VarType = :type ";

        $maxSeqNo = (int)db_query($sql, array(
            ':UserID' => $Data[$userfield],
            ':type' => static::$VarType,
        ))->fetchField();

        $newSeqNo = empty($maxSeqNo) ? 1 : $maxSeqNo + 1;

        $Data[$serialfield] = $newSeqNo;

        $Data['CreatedOn'] = time();

        //there is no (real) serial, so this will return 0 always
        parent::InsertDB($Data);

        return $newSeqNo;
    }

    public static function Index($UserID): array
    {
        $tablename = '{' . static::$DBTableName . '}';
        $userfield = static::$DBUserField;

        $sql = "SELECT * FROM $tablename WHERE $userfield = :RelOwnerUserID AND VarType = :Type";

        $result = db_query($sql, array(
            ':RelOwnerUserID' => $UserID,
            ':Type' => static::$VarType
        ));

        $entities = array();

        while ($DBArray = kt_fetch_array($result)) {
            /** @var VarImport $ObjectEntity */
            $ObjectEntity = static::FromArray($DBArray);

            if ($ObjectEntity) {
                $entity = $ObjectEntity->GetData();

                if ($ObjectEntity->HasExpired()) {
                    $entity['Status'] = static::STATUS_EXPIRED;
                }

                $entities[$DBArray[static::$DBSerialField]] = $entity;
            }
        }

        return $entities;
    }

    /**
     * @param stdClass $account
     *
     * @return array
     */
    public static function angularApiOverview(stdClass $account): array
    {
        $entities = array();

        $index = static::Index($account->uid);

        foreach ($index as $entity) {
            $entities[] = array(
                'id' => (int)$entity['SeqNo'],
                'type' => $entity['VarType'],
                'name' => $entity['Name'],
                'displayType' => static::GetDisplayStatus($entity),
                'status' => $entity['Status'],
                'createdOn' => Dates::formatDate(Dates::FORMAT_DMYHI, (int) $entity['CreatedOn']),
                'expiresOn' => empty($entity['ExpiresOn']) ? '' : Dates::formatDate(Dates::FORMAT_DMYHI, (int) $entity['ExpiresOn']),
            );
        }

        return $entities;
    }

    /**
     * @param stdClass $account
     * @param array $data
     *
     * @return int
     * @throws \ServicesException
     */
    public static function angularApiCreate(stdClass $account, array $data): int
    {
        $validationObj = static::FromArray([
            'RelOwnerUserID' => $account->uid
        ]);

        $limit_exceeded = static::CheckLimitExceeded($account);
        if ($limit_exceeded[0]) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_LIMIT_EXCEEDED,
                    'message' => $limit_exceeded[1]
                ]
            ]);
        }

        $headerValues = '';
        $headerValues2 = '';

        if (empty($data['copyFromID'])) {
            if (empty($data['name'])) {
                static::angularApiError([
                    [
                        'code' => static::ANGULAR_API_ERROR_NAME_REQUIRED,
                        'message' => $validationObj->validatorGetMessage(static::ANGULAR_API_ERROR_NAME_REQUIRED)
                    ]
                ]);
            }

            if (static::CheckDuplicateName($account->uid, $data['name'])) {
                static::angularApiError([
                    [
                        'code' => static::ANGULAR_API_ERROR_NAME_DUPLICATE,
                        'message' => $validationObj->validatorGetMessage(static::ANGULAR_API_ERROR_NAME_DUPLICATE)
                    ]
                ]);
            }

            if (empty($data['file'])) {
                static::angularApiError([
                    [
                        'code' => static::ANGULAR_API_ERROR_FILE_REQUIRED,
                        'message' => $validationObj->validatorGetMessage(static::ANGULAR_API_ERROR_FILE_REQUIRED)
                    ]
                ]);
            }

            //Note: Only the following fields can be set via create
            $create = array(
                'name' => $data['name'],
                'file' => $data['file'],
                'delimiter' => $data['delimiter'],
                'enclosure' => $data['enclosure'],
                'header' => $data['header'],
                'importFields' => $data['importFields'],
            );

            $headerValues = $data['headerValues'];
            $headerValues2 = $data['headerValues2'];
        } else {
            $ObjectImport = VarImport::FromID($account->uid, $data['copyFromID']);

            if (!$ObjectImport) {
                static::angularApiError([
                    [
                        'code' => static::ANGULAR_API_ERROR_COPY_NOT_FOUND,
                        'message' => $validationObj->validatorGetMessage(static::ANGULAR_API_ERROR_COPY_NOT_FOUND)
                    ]
                ]);
            }

            $create = static::FilterObject($ObjectImport->GetData());

            $create['name'] = t("Import::Copy of !name (!date)", [
                '!name' => $create['name'],
                '!date' => date('d.m.Y H:i:s', time())
            ]);

            $create['status'] = self::STATUS_INIT;

            unset($create['id']);
            unset($create['type']);
            unset($create['file']);
            unset($create['session']);
            unset($create['chunkSize']);
            unset($create['createdOn']);
            unset($create['expiresOn']);
            unset($create['fieldDefinitions']);
            unset($create['accountList']);
            unset($create['accountTags']);
            unset($create['stats']);
        }

        $id = 0;
        $DBArray = static::UnFilterObject($create, [], true);
        if ($DBArray) {
            $DBArray[static::$DBUserField] = $account->uid;
            $id = static::InsertDB($DBArray);
        }
        if (empty($id)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_CREATE_FAILED,
                    'message' => $validationObj->validatorGetMessage(static::ANGULAR_API_ERROR_CREATE_FAILED)
                ]
            ]);
        }

        if (!empty($headerValues)) {
            /** @var VarImport $objImport */
            $objImport = static::FromID($account->uid, $id);
            if ($objImport) {
                $objImport->CreateReportFilesHeader($headerValues, $headerValues2);
            }
        }

        return $id;
    }

    /**
     * @param stdClass $account
     * @param int $id
     *
     * @return array
     * @throws \ServicesException
     */
    public static function angularApiSettingsRetrieve(stdClass $account, int $id): array
    {
        /** @var DatabaseTableCRUD $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);
        if (empty($ObjectEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        // add stats where defined
        $Data = $ObjectEntity->GetData();
        $Data['Statistics'] = $ObjectEntity->GetTaggingStats();

        return $ObjectEntity->FilterObject($Data);
    }

    /**
     * @param stdClass $account
     * @param int $id
     * @param array $entity
     *
     * @return array
     * @throws \ServicesException
     */
    public static function angularApiFileInfo(stdClass $account, int $id, array $entity): array
    {
        /** @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);
        if (empty($ObjectEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        if (empty($entity['name'])) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_NAME_REQUIRED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_NAME_REQUIRED)
                ]
            ]);
        } else {
            $duplicateID = static::CheckDuplicateName($account->uid, $entity['name']);
            if ($duplicateID && $duplicateID != $id) {
                static::angularApiError([
                    [
                        'code' => static::ANGULAR_API_ERROR_NAME_DUPLICATE,
                        'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_NAME_DUPLICATE)
                    ]
                ]);
            }
        }

        if (empty($entity['file'])) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_FILE_REQUIRED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_FILE_REQUIRED)
                ]
            ]);
        }

        if ($ObjectEntity->GetData('Status') != static::STATUS_INIT) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_STATUS,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_STATUS)
                ]
            ]);
        }

        //Note: Only the following fields can be set via this action
        $update = array(
            'name' => $entity['name'],
            'file' => $entity['file'],
            'delimiter' => $entity['delimiter'],
            'enclosure' => $entity['enclosure'],
            'header' => $entity['header'],
            'importFields' => $entity['importFields'],
        );

        $DBArray = $ObjectEntity->UnFilterObject($update, $ObjectEntity->GetData());

        $DBArray['Status'] = static::STATUS_ASSIGN;

        if (!$ObjectEntity->UpdateDB($DBArray)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPDATE_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPDATE_FAILED)
                ]
            ]);
        }

        /* @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);

        if (empty($ObjectEntity)) {
            //unlikely
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPDATE_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPDATE_FAILED)
                ]
            ]);
        }

        $ObjectEntity->CreateReportFilesHeader($entity['headerValues'] ?? [], $entity['headerValues2'] ?? []);

        return $ObjectEntity->FilterObject($ObjectEntity->GetData());
    }

    /**
     * @param stdClass $account
     * @param array $field
     * @param array $values
     *
     * @return array
     * @throws \ServicesException
     */
    public static function angularApiValidateField(stdClass $account, array $field, array $values): array
    {
        $validatorObj = static::FromArray([
            'RelOwnerUserID' => $account->uid
        ]);

        if (empty($values)) {
            // TODO: temporary (until migration of email dialogs) to get the validation message

            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_NO_VALUES,
                    'message' => $validatorObj->validatorGetMessage(static::ANGULAR_API_ERROR_NO_VALUES)
                ]
            ]);
        }

        $maxIDs = 100;

        if (count($values) > $maxIDs) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_TOO_MANY_VALUES,
                    'message' => $validatorObj->validatorGetMessage(static::ANGULAR_API_ERROR_TOO_MANY_VALUES)
                ]
            ]);
        }

        $values = array_unique(array_filter($values));

        $samples = count($values);
        $valid = 0;
        $detectedFormats = [];

        foreach ($values as $value) {
            $res = static::ValidationImportField($account->uid, $field, $value);

            if (!empty($res['format'])) {
                $detectedFormats[$res['format']] = empty($detectedFormats[$res['format']]) ? 1 : $detectedFormats[$res['format']]++;
            }

            if ($res['status'] == static::VALIDATION_OK) {
                $valid++;
            }
        }

        $suggestedFormat = '';
        if (!empty($detectedFormats)) {
            $suggestedFormat = array_search(max($detectedFormats), $detectedFormats);
        }

        $message = [];
        $status = 'ok';
        if (count($detectedFormats) > 1) {
            $message[] = t(
                'Import::InstantFieldValidation::Multiple formats detected. To ensure the best result, make sure to use the same format in this column.'
            );
            $status = 'warning';
        }

        if (empty($valid)) {
            $message[] = t('Import::InstantFieldValidation::None of the values are valid for this field.');
            $status = 'error';
        } elseif ($valid < $samples) {
            $message[] = t('Import::InstantFieldValidation::Not all values are valid for this field.');
            $status = 'warning';
        }

        // Everything went right.
        return [
            'field' => $field,
            'suggestedFormat' => $suggestedFormat,
            'status' => $status,
            'message' => implode(' ', $message),
        ];
    }

    /**
     * @param stdClass $account
     * @param array $Header
     * @param array $Header2
     *
     * @return array
     */
    public static function angularApiDetectFormat(stdClass $account, array $Header, array $Header2): array
    {
        return static::DetectFormat($account->uid, $Header, $Header2);
    }

    /**
     * @param $account
     * @param $id
     * @param $entity
     *
     * @return array
     * @throws \ServicesException
     *
     */
    public static function angularApiAssignFields($account, $id, $entity): array
    {
        /** @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);
        if (empty($ObjectEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        if ($ObjectEntity->GetData('Status') != static::STATUS_ASSIGN) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_STATUS,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_STATUS)
                ]
            ]);
        }

        if (empty($entity['importFields'])) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_NO_FIELDS,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_NO_FIELDS)
                ]
            ]);
        }

        if (empty($entity['session'])) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_INVALID_SESSION,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_INVALID_SESSION)
                ]
            ]);
        }

        //Note: Only the fields and the upload session can be set via this action
        $update = array(
            'importFields' => $entity['importFields'],
            'session' => $entity['session'],
            'skipExternalValidation' => $entity['skipExternalValidation'],
            'noSubscriptionImport' => $entity['noSubscriptionImport'],
        );

        $DBArray = $ObjectEntity->UnFilterObject($update, $ObjectEntity->GetData());

        $DBArray['Status'] = static::STATUS_UPLOAD;

        $DBArray['ExpiresOn'] = strtotime("+" . static::ENV_VALID_UNTIL_DAYS . " days 23:59:59");

        if (!$ObjectEntity->UpdateDB($DBArray)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPDATE_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPDATE_FAILED)
                ]
            ]);
        }

        /* @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);

        if (empty($ObjectEntity)) {
            //unlikely
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPDATE_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPDATE_FAILED)
                ]
            ]);
        }

        return $ObjectEntity->FilterObject($ObjectEntity->GetData());
    }

    /**
     * @param stdClass $account
     * @param int $id
     * @param $id
     * @param array $entity
     *
     * @return array
     * @throws \ServicesException
     */
    public static function angularApiImport(stdClass $account, int $id, array $entity): array
    {
        /** @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);
        if (empty($ObjectEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        if ($ObjectEntity->GetData('Status') != static::STATUS_VALIDATED) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_STATUS,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_STATUS)
                ]
            ]);
        }

        if (!empty($entity['listID'])) {
            $ObjectList = Lists::FromID($account->uid, $entity['listID']);
            if (empty($ObjectList)) {
                static::angularApiError([
                    [
                        'code' => static::ANGULAR_API_ERROR_LIST_NOT_FOUND,
                        'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_LIST_NOT_FOUND)
                    ]
                ]);
            }
        }

        //Note: Only the assignTags and the listID can be set via this action
        $update = array(
            'assignTags' => empty($entity['assignTags']) ? [] : $entity['assignTags'],
            'warningTags' => empty($entity['warningTags']) ? [] : $entity['warningTags'],
            'listID' => empty($entity['listID']) ? 0 : $entity['listID']
        );

        $DBArray = $ObjectEntity->UnFilterObject($update, $ObjectEntity->GetData());

        if (!$ObjectEntity->UpdateDB($DBArray)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPDATE_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPDATE_FAILED)
                ]
            ]);
        }

        $ObjectEntity->SetData($DBArray);

        if (!$ObjectEntity->ImportCreateQueueJobs()) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_IMPORT_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_IMPORT_FAILED)
                ]
            ]);
        }

        /* @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);

        if (empty($ObjectEntity)) {
            //unlikely
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPDATE_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPDATE_FAILED)
                ]
            ]);
        }

        return $ObjectEntity->FilterObject($ObjectEntity->GetData());
    }

    /**
     * @param stdClass $account
     * @param int $id
     *
     * @return array
     * @throws \ServicesException
     */
    public static function angularApiCancel(stdClass $account, int $id): array
    {
        /** @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);
        if (empty($ObjectEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        if ($ObjectEntity->HasExpired()) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_EXPIRED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_EXPIRED)
                ]
            ]);
        }

        $DBArray = $ObjectEntity->CancelStatus();

        if (!$ObjectEntity->UpdateDB($DBArray)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPDATE_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPDATE_FAILED)
                ]
            ]);
        }

        /* @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);

        if (empty($ObjectEntity)) {
            //unlikely
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPDATE_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPDATE_FAILED)
                ]
            ]);
        }

        return $ObjectEntity->FilterObject($ObjectEntity->GetData());
    }

    /**
     * @param stdClass $account
     * @param int $id
     * @param string $session
     * @param string $data
     * @param bool $isLast
     *
     * @return array|int
     * @throws \ServicesException
     */
    public static function angularApiPush(
        stdClass $account,
        int $id,
        string $session,
        string $data,
        bool $isLast = false
    ): int {
        /** @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);
        if (empty($ObjectEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        if ($ObjectEntity->GetData('Status') != static::STATUS_UPLOAD) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_STATUS,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_STATUS)
                ]
            ]);
        }

        if ($ObjectEntity->GetData('Session') != $session) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_INVALID_SESSION,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_INVALID_SESSION)
                ]
            ]);
        }

        $json = json_decode($data, true);
        if ($json === false) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_INVALID_JSON,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_INVALID_JSON)
                ]
            ]);
        }

        if (empty($json)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_NO_DATA,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_NO_DATA)
                ]
            ]);
        }

        if (count($json) > $ObjectEntity->GetData('ChunkSize')) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_CHUNK_SIZE_EXCEEDED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_CHUNK_SIZE_EXCEEDED)
                ]
            ]);
        }

        $result = $ObjectEntity->SaveUploadFile($data, $isLast);

        if (empty($result)) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_UPLOAD_FAILED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_UPLOAD_FAILED)
                ]
            ]);
        }

        // Everything went right.
        return $result;
    }

    /**
     * @param stdClass $account
     * @param int $id
     *
     * @return array
     * @throws \ServicesException
     */
    public static function angularApiProgress(stdClass $account, int $id): array
    {
        /** @var VarImport $ObjectEntity */
        $ObjectEntity = static::FromID($account->uid, $id);
        if (empty($ObjectEntity)) {
            return services_error(t('There is no entity with such an id.'), 404);
        }

        if ($ObjectEntity->HasExpired()) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_EXPIRED,
                    'message' => $ObjectEntity->validatorGetMessage(static::ANGULAR_API_ERROR_EXPIRED)
                ]
            ]);
        }

        return $ObjectEntity->Progress();
    }

    /**
     * @param stdClass $account
     * @param int $id
     *
     * @return bool[]
     * @throws \ServicesException
     */
    public static function angularApiDelete(stdClass $account, int $id): bool
    {
        $objEntity = static::FromID($account->uid, $id);

        if (empty($objEntity)) {
            services_error(t('There is no entity with such an id.'), 404);
        }

        if (!static::DeleteDB($objEntity->GetData())) {
            static::angularApiError([
                [
                    'code' => static::ANGULAR_API_ERROR_DELETE_FAILED,
                    'message' => $objEntity->validatorGetMessage(static::ANGULAR_API_ERROR_DELETE_FAILED)
                ]
            ]);
        }

        return true;
    }

    public static function FilterObject($data)
    {
        if (!empty($data['ExpiresOn']) && $data['ExpiresOn'] <= time()) {
            $data['Status'] = static::STATUS_EXPIRED;
        }

        $result = parent::FilterObject($data);

        foreach ($data as $key => $value) {
            if (isset(static::$APIFields[$key])) {
                switch ($key) {
                    case 'Session':
                        // browser generated hash to make sure we are in the same upload session
                        // do not provide the value via api
                        unset($result[static::$APIFields[$key]['id']]);
                        break;
                }
            }
        }

        //add assign field definition only when in status ASSIGN, EXPIRED or DONE
        if (in_array($data['Status'], [static::STATUS_ASSIGN, static::STATUS_EXPIRED, static::STATUS_DONE])) {
            $result['fieldDefinitions'] = static::GetImportFields($data['RelOwnerUserID']);
        } else {
            $result['fieldDefinitions'] = [];
        }

        //add statistics only when available
        if (in_array($data['Status'], [static::STATUS_VALIDATED, static::STATUS_DONE, static::STATUS_DENIED])) {
            $result['stats'] = $data['Stats'];

            $tags = static::GetAccountManualTags($data['RelOwnerUserID']);

            $result['accountTags'] = [];
            foreach ($tags as $tag) {
                $result['accountTags'][] = array(
                    'id' => $tag['TagID'],
                    'name' => $tag['TagName'],
                );
            }

            $lists = static::GetAccountLists($data['RelOwnerUserID']);

            $result['accountLists'] = [];
            foreach ($lists as $entity) {
                $result['accountLists'][] = array(
                    'id' => $entity['ListID'],
                    'name' => Lists::GetNameForOptionsArray($entity),
                    'isSingleOptIn' => $entity['OptInModeEnum'] == Lists::OPTIN_MODE_SINGLE ? 1 : 0,
                );
            }
        } else {
            $result['stats'] = [];
            $result['accountTags'] = [];
            $result['accountLists'] = [];
        }

        return $result;
    }

    public static function UnFilterObject($data, $result = array(), $isObjectNew = false)
    {
        $result = parent::UnFilterObject($data, $result);

        foreach (static::$APIFields as $key => $def) {
            if (isset($data[$def['id']])) {
                switch ($key) {
                    case 'Delimiter':
                    case 'Enclosure':
                        $result[$key] = empty($data[$def['id']]) ? static::$DefaultDataFields[$key] : $data[$def['id']];
                        break;
                    case 'Header':
                    case 'SkipExternalValidation':
                    case 'NoSubscriptionImport':
                        $result[$key] = !empty($data[$def['id']]);
                        break;
                    case 'ImportFields':
                        if (empty($data[$def['id']]) || !is_array($data[$def['id']])) {
                            $result[$key] = array();
                        } else {
                            $result[$key] = $data[$def['id']];
                        }
                        break;
                    case 'AssignTags':
                    case 'WarningTags':
                        if (!empty($data[$def['id']]) && is_array($data[$def['id']])) {
                            $result[$key] = array_values(array_filter($data[$def['id']], 'is_numeric'));
                        } else {
                            $result[$key] = [];
                        }
                        break;
                    case 'Status':
                        if ($data[$def['id']] == self::STATUS_INIT) {
                            //for create copyFromID we need to set the status to init
                            $result[$key] = self::STATUS_INIT;
                        } else {
                            //setting the status to any other value is not allowed
                            unset($result[$key]);
                        }
                        break;
                    case 'SeqNo':
                    case 'VarType':
                    case 'ChunkSize':
                    case 'CreatedOn':
                    case 'ExpiresOn':
                    case 'AccountTags':
                    case 'AccountLists':
                    case 'FieldDefinitions':
                        //these fields should not be updated from the outside
                        unset($result[$key]);
                        break;
                }
            }
        }

        return $result;
    }

    /**
     * @return void
     */
    protected function validatorPrepareMessages(): void
    {
        parent::validatorPrepareMessages();

        $this->validatorMessages = array_merge($this->validatorMessages, [
            static::ANGULAR_API_ERROR_FILE_REQUIRED => t('AngularApi::Import::Error::File required'),
            static::ANGULAR_API_ERROR_STATUS => t('AngularApi::Import::Error::Invalid status'),
            static::ANGULAR_API_ERROR_NO_VALUES => t('AngularApi::Import::Error::No values'),
            static::ANGULAR_API_ERROR_TOO_MANY_VALUES => t('AngularApi::Import::Error::Too many values'),
            static::ANGULAR_API_ERROR_NO_FIELDS => t('AngularApi::Import::Error::No fields assigned'),
            static::ANGULAR_API_ERROR_INVALID_SESSION => t('AngularApi::Import::Error::No session value'),
            static::ANGULAR_API_ERROR_IMPORT_FAILED => t('AngularApi::Import::Error::Starting the import failed'),
            static::ANGULAR_API_ERROR_EXPIRED => t('AngularApi::Import::Error::Import has expired'),
            static::ANGULAR_API_ERROR_CHUNK_SIZE_EXCEEDED => t('AngularApi::Import::Error::Chunk size exceeded'),
        ]);
    }

    public static function GetDisplayStatus($DBArray)
    {
        switch ($DBArray['Status']) {
            case self::STATUS_INIT:
                return t('ImportStatus::Init');
            case self::STATUS_ASSIGN:
                return t('ImportStatus::Assign fields');
            case self::STATUS_UPLOAD:
                return t('ImportStatus::Uploading');
            case self::STATUS_VALIDATING:
                return t('ImportStatus::Validating');
            case self::STATUS_VALIDATED:
                return t('ImportStatus::Validated');
            case self::STATUS_IMPORTING:
                return t('ImportStatus::Importing');
            case self::STATUS_DONE:
                return t('ImportStatus::Done');
            case self::STATUS_DENIED:
                return t('ImportStatus::Denied');
            case self::STATUS_EXPIRED:
                return t('ImportStatus::Expired');
            default:
                return $DBArray['Status'];
        }
    }

    /**
     * Check if an entity is used in this object
     *
     * @param mixed $EntityID identifier of entity to check dependencies for
     * @param string $EntityClass class of entity to check dependencies for
     * @param string $Op action to check dependencies for
     * @param bool $forAngular
     * @return array
     */
    public function GetDependencies($EntityID, $EntityClass, $Op = 'delete', bool $forAngular = false)
    {
        if (empty($EntityID) || empty($EntityClass)) {
            return array();
        }

        if ($this->GetData('Status') != static::STATUS_IMPORTING) {
            return array();
        }

        $ImportID = $this->GetData('SeqNo');
        $Name = $this->GetData('Name');

        $dependencies = array();

        switch ($EntityClass) {
            case Tag::class:
                if (
                    in_array($EntityID, $this->GetData('AssignTags')) || in_array(
                        $EntityID,
                        $this->GetData('WarningTags')
                    )
                ) {
                    if ($forAngular) {
                        $dependencies[/*t(*/'dependency:import:Tag'/*)*/][$ImportID] = [
                            'id' => $ImportID,
                            'type' => static::getApiType(),
                            'name' => $Name,
                            'editLink' => $this->GetEditURL()
                        ];
                    } else {
                        $dependencies[/*t(*/'dependency:import:Tag'/*)*/][$ImportID] = l(
                            $Name,
                            $this->GetEditURL(),
                            array('attributes' => array('target' => '_blank'))
                        );
                    }
                }

                break;
            case Lists::class:
                if ($this->GetData('ListID') == $EntityID) {
                    if ($forAngular) {
                        $dependencies[/*t(*/'dependency:import:List'/*)*/][$ImportID] = [
                            'id' => $ImportID,
                            'type' => static::getApiType(),
                            'name' => $Name,
                            'editLink' => $this->GetEditURL()
                        ];
                    } else {
                        $dependencies[/*t(*/'dependency:import:List'/*)*/][$ImportID] = l(
                            $Name,
                            $this->GetEditURL(),
                            array('attributes' => array('target' => '_blank'))
                        );
                    }
                }

                break;
        }

        return $dependencies;
    }

    /**
     * Create the validation and error report files with the header of the upload file
     * @param array $headerValues
     * @param array $headerValues2
     * @return void
     */
    public function CreateReportFilesHeader(array $headerValues, array $headerValues2): void
    {
        $data = $this->GetData();

        if (!$data['Header']) {
            // the import file had no header
            return;
        }

        $delimiter = chr(59); //semicolon
        $enclosure = chr(34); //double quotes

        $isKTTemplate = static::DetectFormatKlicktipp($data['RelOwnerUserID'], $headerValues, $headerValues2);

        //create the user directory for exports and/or check if it is writable
        $path = static::GetFilepath($data['RelOwnerUserID'], $data['SeqNo']);
        if (!file_prepare_directory($path, FILE_CREATE_DIRECTORY || FILE_MODIFY_PERMISSIONS)) {
            return;
        }

        // always overwrite
        $reportFile = fopen($this->GetValidationReportFilename(), 'w');

        // if there are validation warnings/errors, we add 2 columns to original import file
        // these columns need a header, otherwise the file cannot be re-imported
        $headerValues[] = t('Import::ReportFile::Header::KlickTipp error');
        $headerValues[] = t('Import::ReportFile::Header::KlickTipp error description');

        if ($reportFile) {
            fputcsv($reportFile, $headerValues, $delimiter, $enclosure);
            if (!empty($isKTTemplate['score'])) {
                fputcsv($reportFile, $headerValues2, $delimiter, $enclosure);
            }
            fclose($reportFile);
        }

        // always overwrite
        $reportFile = fopen($this->GetErrorReportFilename(), 'w');

        if ($reportFile) {
            fputcsv($reportFile, $headerValues, $delimiter, $enclosure);
            if (!empty($isKTTemplate['score'])) {
                fputcsv($reportFile, $headerValues2, $delimiter, $enclosure);
            }
            fclose($reportFile);
        }
    }

    public function SaveUploadFile($Data, $isLast = false)
    {
        $DBArray = $this->GetData();

        $UserID = $DBArray['RelOwnerUserID'];
        $ImportID = $DBArray['SeqNo'];

        $path = static::GetFilepath($UserID, $ImportID);

        if (empty($Data) || empty($path)) {
            return 0;
        }

        //create the user directory for exports and/or check if it is writable
        if (!file_prepare_directory($path, FILE_CREATE_DIRECTORY || FILE_MODIFY_PERMISSIONS)) {
            return 0;
        }

        //unique filename
        $findex = count($DBArray['Files']) + 1;
        $filename = "{$UserID}_{$ImportID}_" . time() . '_' . "$findex.json";

        if (!file_put_contents($this->GetChunkFilename($filename), $Data)) {
            return 0;
        }

        $DBArray['Files'][$filename] = VarImport::FILE_STATUS_UPLOADED;

        if (!$this->UpdateDB($DBArray)) {
            return 0;
        }

        $this->SetData($DBArray);

        if ($isLast && !$this->ValidationCreateQueueJobs()) {
            return 0;
        }

        return count($DBArray['Files']);
    }

    public function SaveValidationReport($ForFilename, $Data)
    {
        //use same filename of upload chunk and add validation marker and csv extension
        //Note: filename of upload chungk: {$UserID}_{$ImportID}_" . time() . '_' . "$findex.json
        $filename = $this->GetChunkValidationFilename($ForFilename);

        return self::SaveReportAsCSV($filename, $Data);
    }

    public function SaveImportErrorReport($ForFilename, $Data)
    {
        //use same filename of upload chunk and add validation marker and csv extension
        //Note: filename of upload chungk: {$UserID}_{$ImportID}_" . time() . '_' . "$findex.json
        $filename = $this->GetChunkErrorFilename($ForFilename);

        return self::SaveReportAsCSV($filename, $Data);
    }

    public function SaveReportAsCSV($Filename, $Data)
    {
        $DBArray = $this->GetData();

        $UserID = $DBArray['RelOwnerUserID'];
        $ImportID = $DBArray['SeqNo'];

        $path = static::GetFilepath($UserID, $ImportID);

        if (empty($Data) || empty($path)) {
            return false;
        }

        //create the user directory for exports and/or check if it is writable
        if (!file_prepare_directory($path, FILE_CREATE_DIRECTORY || FILE_MODIFY_PERMISSIONS)) {
            return false;
        }

        $file = fopen($Filename, 'a'); //open for write (append)

        if (!$file) {
            return false;
        }

        $delimiter = chr(59); //semicolon
        $enclosure = chr(34); //double quotes

        foreach ($Data as $row) {
            fputcsv($file, $row, $delimiter, $enclosure);
        }

        fclose($file);

        return true;
    }

    public function ValidationCreateQueueJobs()
    {
        $DBArray = $this->GetData();

        $DBArray['Status'] = static::STATUS_VALIDATING;
        $DBArray['ValidationStartedOn'] = time();

        if (!$this->UpdateDB($DBArray)) {
            return false;
        }

        $LogID = ProcessLog::Create('import_subscribers_validate');

        $addedItems = 0;
        foreach ($DBArray['Files'] as $filename => $status) {
            if ($status == static::FILE_STATUS_UPLOADED) {
                $item = array(
                    'UserID' => $DBArray['RelOwnerUserID'],
                    'ImportID' => $DBArray['SeqNo'],
                    'ImportFile' => $filename,
                    'Session' => $DBArray['Session'],
                );

                ProcessLog::AddQueueItem($LogID, $item);

                $addedItems++;
            }
        }

        if (empty($addedItems)) {
            $DBArray['Status'] = static::STATUS_VALIDATED;
            if (empty($DBArray['ValidationStartedOn'])) {
                $DBArray['ValidationStartedOn'] = time();
            }

            $DBArray['ValidationFinishedOn'] = time();

            if (!$this->UpdateDB($DBArray)) {
                return false;
            }
        }

        return true;
    }

    public static function ValidationQueueWorker($data)
    {
        ProcessLog::QueueItemStarted($data);

        $UserID = $data['UserID'];
        $ImportID = $data['ImportID'];
        $ImportFile = $data['ImportFile'];
        $Session = $data['Session'];

        /** @var VarImport $ObjectImport */
        $ObjectImport = static::FromID($UserID, $ImportID);

        if (
            !$ObjectImport ||
            $ObjectImport->GetData('Status') != static::STATUS_VALIDATING ||
            $ObjectImport->GetData('Session') != $Session
        ) {
            //this is OK, the import was deleted or canceled by the user
            //Note: we need to check the session in case jobs have been created, user cancels,
            //      re-uploads other files and gets into STATUS_VALIDATING before
            //      the first jobs have been canceled
            $data['message'] = "Import aborted by user ($UserID, $ImportID, $ImportFile, $Session)";
            ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
            return;
        }

        $DBArray = $ObjectImport->GetData();

        $NewStatus = static::FILE_STATUS_VALIDATED;
        $EmailValidationLevel = ($DBArray['SkipExternalValidation']) ? VarImport::EMAIL_VALIDATION_LEVEL_KLICKTIPP : VarImport::EMAIL_VALIDATION_LEVEL_EXTERNAL;

        if ($DBArray['Files'][$ImportFile] == static::FILE_STATUS_UPLOADED) {
            $filename = $ObjectImport->GetChunkFilename($ImportFile);

            $json = file_get_contents($filename);

            if (empty($json)) {
                $data['message'] = "Error reading import chunk for validation ($UserID, $ImportID, $ImportFile)";
                ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
                return;
            }

            $chunk = json_decode($json, true);

            if (empty($chunk)) {
                $data['message'] = "Error decoding import chunk json for validation ($UserID, $ImportID, $ImportFile)";
                ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
                return;
            }

            $validRows = array();
            $markedRows = array();
            foreach ($chunk as $row) {
                $error = static::VALIDATION_OK;
                $messages = array();
                $sanitizedRow = $row;

                $checkEmptyRow = array_filter($row);
                if (empty($checkEmptyRow)) {
                    // the row has no values
                    continue;
                }

                foreach ($DBArray['ImportFields'] as $field) {
                    if (empty($field['id'])) {
                        continue;
                    }

                    $index = $field['column'];
                    $result = VarImport::ValidationImportField($UserID, $field, $row[$index], $EmailValidationLevel);
                    if ($result['status'] != static::VALIDATION_OK) {
                        if ($result['field']['id'] != 'EmailAddress') {
                            //replace the value with the warning tag indicator
                            //so we can tag the subscriber if the user wants to
                            //Note: for static::VALIDATION_ERROR it doesn't matter, the whole row will be ignored
                            $sanitizedRow[$index] = static::WARNING_TAG_INDICATOR;
                        }

                        //do not overwrite status error with status warning
                        $error = $error == static::VALIDATION_ERROR ? static::VALIDATION_ERROR : $result['status'];
                        $messages[] = $result['message'];
                    }
                }

                //add our error/warning flag to the end
                $sanitizedRow[] = $error == static::VALIDATION_OK ? static::VALIDATION_OK : static::VALIDATION_WARNING;

                if ($error == static::VALIDATION_OK) {
                    //no warning no error in the row
                    $validRows[] = $sanitizedRow;
                } else {
                    $displayError = t("Contact will not imported");
                    if ($error != static::VALIDATION_ERROR) {
                        //we can still import the sanitized row
                        $validRows[] = $sanitizedRow;

                        if ($error == static::VALIDATION_WARNING) {
                            $displayError = t("Contact will be partially imported");
                        } else {
                            // MillionVerifier: multiple status codes
                            // email address did not verify, but the user has access to import anyway
                            $displayError = t("Contact will imported with caution");
                        }
                    }

                    //add error type, messages and error enum (for stats) as 3 new columns at the end
                    $row[] = $displayError;
                    $row[] = implode(" | ", $messages);
                    $row[] = $error;
                    $markedRows[] = $row;
                }
            }

            //overwrite the upload chunk file with the sanitized data
            if (!file_put_contents($filename, json_encode($validRows))) {
                $data['message'] = "Error writing sanitized import chunk json after validation ($UserID, $ImportID, $ImportFile)";
                ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
                return;
            }

            //write validation info file
            if (!empty($markedRows)) {
                if (!$ObjectImport->SaveValidationReport($ImportFile, $markedRows)) {
                    $data['message'] = "Error writing validation report for import chunk ($UserID, $ImportID, $ImportFile)";
                    ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
                    return;
                }
            }

            static::UpdateFileStatus($UserID, $ImportID, $ImportFile, static::FILE_STATUS_UPLOADED, $NewStatus);
        }
        //else: OK, the import might have been canceled

        /** @var VarImport $ObjectImport */
        $ObjectImport = static::FromID($UserID, $ImportID);

        if (!$ObjectImport) {
            //this could mean that the serialized Data has been destroyed by UPDATE REPLACE
            //log a hint in the watchdog
            $data['message'] = 'Error retrieving import after updating file status to VALIDATED.';
            ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
            return;
        }

        $DBArray = $ObjectImport->GetData();

        $done = array_filter($DBArray['Files'], function ($item) use ($NewStatus) {
            return $item == $NewStatus;
        });

        $total = count($DBArray['Files']);
        $finished = count($done);

        $lastFile = ($total == $finished);

        $complete = ProcessLog::QueueItemProcessed($data);

        if ($lastFile || $complete) {
            if (!$complete || !$lastFile) {
                //unlikely, but log in case it happens

                watchdog(
                    'subscriber-import',
                    'Import Queue mismatch: Last queue item complete != all files validated.',
                    array(
                        '!isLastFile' => $lastFile,
                        '!isProcessedComplete' => $complete,
                        '!total' => $total,
                        '!validated' => $finished,
                        '!import' => $DBArray
                    ),
                    WATCHDOG_WARNING
                );
            }

            $ObjectImport->ValidationFinalize();
        }
    }

    public static function ValidationImportField(
        $UserID,
        $Field,
        $Value,
        $Level = VarImport::EMAIL_VALIDATION_LEVEL_FORMAT
    ) {
        static $lastDateFormat = '';

        $fieldFormat = '';

        $column = $Field['header'];
        if (empty($column)) {
            //no header given in the csv
            $column = t("Column !csvindex", ['!csvindex' => $Field['column'] + 1]);
        }

        $result = array(
            'field' => $Field,
            'status' => static::VALIDATION_OK,
            'message' => '',
            'format' => ''
        );

        if (empty($Value)) {
            //empty value, validation OK
            return $result;
        }

        if ($Field['type'] == 'subscription') {
            switch ($Field['id']) {
                case 'Hash':
                    //see Subscription::EncodeExportKey
                    $decoded = Subscription::DecodeExportKey($Value);

                    if ($decoded['u'] != $UserID) {
                        $result = array(
                            'field' => $Field,
                            'status' => static::VALIDATION_ERROR,
                            'message' => t(
                                'Import::FieldValidation::KlickTippHash::!column: Subscription not found in account.',
                                [
                                    '!column' => $column
                                ]
                            ),
                        );
                    }

                    break;

                case 'SubscriberID':
                    $sql = "SELECT COUNT(*) FROM {subscribers} WHERE RelOwnerUserID = :UserID AND SubscriberID = :sid";

                    $found = db_query($sql, array(
                        ':UserID' => $UserID,
                        ':sid' => $Value
                    ))->fetchField();

                    if (empty($found)) {
                        $result = array(
                            'field' => $Field,
                            'status' => static::VALIDATION_ERROR,
                            'message' => t(
                                'Import::FieldValidation::SubscriberID::!column: Subscriber not found in account',
                                [
                                    '!column' => $column
                                ]
                            ),
                        );
                    }

                    break;

                case 'EmailAddress':
                    $status = static::ValidationEmailAddress($Value, $Level, $UserID);

                    if ($status != static::VALIDATION_OK) {
                        switch ($status) {
                            case static::VALIDATION_TRASH_EMAIL:
                                $message = t(
                                    "Import::FieldValidation::EmailAddress::!column: Untrustworthy email address.",
                                    [
                                        '!column' => $column
                                    ]
                                );
                                // do not import this email address
                                // Note: for enterprise, this case would be static::VALIDATION_CAUTION
                                $status = static::VALIDATION_ERROR;
                                break;
                            case static::VALIDATION_GLOBAL_BOUNCE:
                                $message = t(
                                    "Import::FieldValidation::EmailAddress::!column: E-mail address cannot receive e-mails.",
                                    [
                                        '!column' => $column
                                    ]
                                );
                                // do not import this email address
                                // Note: enterprise user can deactivate global bounce, then this address would have gone through MV
                                $status = static::VALIDATION_ERROR;
                                break;
                            case static::VALIDATION_HATER:
                                $message = t(
                                    "Import::FieldValidation::EmailAddress::!column: E-mail address detected as suspicious.",
                                    [
                                        '!column' => $column
                                    ]
                                );
                                // do not import this email address anywhere, since it is on our hater list
                                $status = static::VALIDATION_ERROR;
                                break;
                            case static::VALIDATION_CAUTION:
                                $message = t(
                                    "Import::FieldValidation::EmailAddress::!column: Email address was not verified.",
                                    [
                                        '!column' => $column
                                    ]
                                );
                                break;
                            case static::VALIDATION_API_WAITING:
                                $status = static::VALIDATION_CAUTION;
                                $message = t(
                                    "Import::FieldValidation::EmailAddress::!column: Email address could not be verified. Try again.",
                                    [
                                        '!column' => $column
                                    ]
                                );
                                break;
                            case static::VALIDATION_ERROR:
                            default:
                                $message = t(
                                    "Import::FieldValidation::EmailAddress::!column: Invalid email address format.",
                                    [
                                        '!column' => $column
                                    ]
                                );
                                break;
                        }

                        $result = array(
                            'field' => $Field,
                            'status' => $status,
                            'message' => $message,
                        );
                    }

                    break;

                case 'PhoneNumber':
                    //will return our sms number format or '' if no valid sms number can be extracted
                    $SMSNumber = Subscribers::FormatSMSNumber($Value);

                    if (empty($SMSNumber)) {
                        $result = array(
                            'field' => $Field,
                            'status' => static::VALIDATION_ERROR,
                            'message' => t(
                                'Import::FieldValidation::PhoneNumber::!column: Unsupported SMS number format',
                                [
                                    '!column' => $column
                                ]
                            ),
                        );
                    }

                    break;

                case 'ImportOptInDate':
                case 'ImportConfirmationDate':
                    $dateFormat = static::GetDateFormatFromDateString($Value, $lastDateFormat);
                    if ($dateFormat == '') {
                        $result = array(
                            'field' => $Field,
                            'status' => static::VALIDATION_WARNING,
                            'message' => t('Import::FieldValidation::Dates::!column: Unsupported date/time format', [
                                '!column' => $column
                            ]),
                        );
                    } else {
                        $lastDateFormat = $dateFormat;
                        $fieldFormat = $dateFormat;
                    }

                    break;

                case 'ImportOptInIPAddress':
                case 'IPAddress':
                    if (!filter_var($Value, FILTER_VALIDATE_IP)) {
                        $result = array(
                            'field' => $Field,
                            'status' => static::VALIDATION_WARNING,
                            'message' => t(
                                'Import::FieldValidation::IPAddress::!column: Unsupported IP address format',
                                [
                                    '!column' => $column
                                ]
                            ),
                        );
                    }

                    break;

                case 'SubscriptionReferrer':
                    if (strlen($Value) > 1025) {
                        $result = array(
                            'field' => $Field,
                            'status' => static::VALIDATION_WARNING,
                            'message' => t(
                                'Import::FieldValidation::Referrer::!column: Value exceeds 1025 characters',
                                [
                                    '!column' => $column
                                ]
                            ),
                        );
                    }

                    break;

                default:
                    //Nothing to validate
                    break;
            }
        } elseif ($Field['type'] == 'custom' || $Field['type'] == 'global') {
            if ($Field['type'] == 'custom') {
                static $CustomFields;

                if (!isset($CustomFields)) {
                    $CustomFields = static::GetAccountCustomFields($UserID);
                }

                $ArrayField = $CustomFields[$Field['id']];
            } else {
                $ArrayField = CustomFields::$GlobalCustomFieldDefs[$Field['id']];
            }

            if (empty($ArrayField)) {
                return array(
                    'field' => $Field,
                    'status' => static::VALIDATION_WARNING,
                    'message' => t('Import::FieldValidation::CustomFields::!column: Custom field does not exist', [
                        '!column' => $column
                    ]),
                    'format' => ''
                );
            }

            if (
                in_array(
                    $ArrayField['FieldTypeEnum'],
                    [CustomFields::TYPE_DATE, CustomFields::TYPE_DATETIME, CustomFields::TYPE_TIME]
                )
            ) {
                $dateFormat = static::GetDateFormatFromDateString($Value, $lastDateFormat);

                $timestamp = CustomFields::ConvertCustomFieldDataFromWidget($Value, CustomFields::WIDGET_STRTOTIME);
                if ($dateFormat == '' || $timestamp === false || self::MAX_DATETIME_TIMESTAMP >= $timestamp) {
                    $result = array(
                        'field' => $Field,
                        'status' => static::VALIDATION_WARNING,
                        'message' => t('Import::FieldValidation::Dates::!column: Unsupported date/time format', [
                            '!column' => $column
                        ]),
                    );
                } else {
                    $lastDateFormat = $dateFormat;
                    $fieldFormat = $dateFormat;
                }
            } else {
                $result = CustomFields::ValidateCustomFieldValue($ArrayField, $Value, true);

                if (!$result[0]) {
                    $result = array(
                        'field' => $Field,
                        'status' => static::VALIDATION_WARNING,
                        'message' => "$column: " . $result[2],
                    );
                }
            }
        }
        //else: Tagging, nothing to validate

        $result['format'] = $fieldFormat;

        return $result;
    }

    /**
     * Check if the given email address is a valid email address
     *
     * @param string $EmailAddress
     * @param string $Level
     * @param int $UserID
     *
     * @return int
     */
    public static function ValidationEmailAddress(
        string $EmailAddress,
        string $Level = VarImport::EMAIL_VALIDATION_LEVEL_FORMAT,
        int $UserID = 0
    ): int {
        switch ($Level) {
            case VarImport::EMAIL_VALIDATION_LEVEL_KLICKTIPP:
                if (!filter_var(trim($EmailAddress), FILTER_VALIDATE_EMAIL)) {
                    return static::VALIDATION_ERROR;
                }

                if (!Subscribers::ValidateEmailAddress($EmailAddress, $UserID)) {
                    return static::VALIDATION_TRASH_EMAIL;
                }

                break;
            case VarImport::EMAIL_VALIDATION_LEVEL_EXTERNAL:
                static $handleAsWarning;

                if (!isset($handleAsWarning) && !empty($UserID)) {
                    $account = user_load($UserID);
                    $handleAsWarning = user_access('use whitelabel domain', $account);
                }

                if (!filter_var(trim($EmailAddress), FILTER_VALIDATE_EMAIL)) {
                    return static::VALIDATION_ERROR;
                }

                /* var ApiMillionVerifier $MV */
                $MV = new ApiMillionVerifier($UserID);

                $status = $MV->ValidateEmailAddress($EmailAddress);
                switch ($status) {
                    case ApiMillionVerifier::STATUS_MV_INVALID:
                    case ApiMillionVerifier::STATUS_MV_DISPOSABLE:
                    case ApiMillionVerifier::STATUS_KT_LIMIT:
                        return ($handleAsWarning) ? static::VALIDATION_CAUTION : static::VALIDATION_TRASH_EMAIL;
                    case ApiMillionVerifier::STATUS_MV_INVALID_DB:
                        return static::VALIDATION_GLOBAL_BOUNCE;
                    case ApiMillionVerifier::STATUS_MV_INVALID_HATER:
                        return static::VALIDATION_HATER;
                    case ApiMillionVerifier::STATUS_KT_APILIMIT:
                        return static::VALIDATION_API_WAITING;
                }

                break;

            case VarImport::EMAIL_VALIDATION_LEVEL_FORMAT:
            default:
                if (!filter_var(trim($EmailAddress), FILTER_VALIDATE_EMAIL)) {
                    return static::VALIDATION_ERROR;
                }
        }

        return static::VALIDATION_OK;
    }

    public function ValidationFinalize()
    {
        $DBArray = $this->GetData();

        // --- create 1 downloadable validation file from parts

        $delimiter = chr(59); //semicolon
        $enclosure = chr(34); //double quotes

        $path = static::GetFilepath($DBArray['RelOwnerUserID'], $DBArray['SeqNo']);

        //create the user directory for exports and/or check if it is writable
        if (!file_prepare_directory($path, FILE_CREATE_DIRECTORY || FILE_MODIFY_PERMISSIONS)) {
            //TODO: error status
            return;
        }

        $stats = [
            'valid' => 0,
            'warnings' => 0,
            'errors' => 0,
            'download' => ''
        ];

        $reportFilename = $this->GetValidationReportFilename();

        $reportFile = fopen($reportFilename, 'a'); //open for write (append)

        if (!$reportFile) {
            //TODO: error status
            return;
        }

        foreach ($DBArray['Files'] as $file => $status) {
            $json = file_get_contents($this->GetChunkFilename($file));

            if (!empty($json) && $valid = json_decode($json, true)) {
                $stats['valid'] += count($valid);
            }

            //same filename as upload chunk with added validation marker and csv extension
            $chunkFilename = $this->GetChunkValidationFilename($file);

            if (file_exists($chunkFilename)) {
                //Note: if an upload chunk did not contain errors or warnings, there won't be a file

                $chunkFile = fopen($chunkFilename, 'r'); //open read

                if (!$chunkFile) {
                    //TODO: error status
                    continue;
                }

                while (($row = fgetcsv($chunkFile, 0, $delimiter, $enclosure)) !== false) {
                    if (!empty($row)) {
                        //remove the last column with the error enum
                        $status = array_pop($row);

                        if ($status == static::VALIDATION_ERROR) {
                            $stats['errors']++;
                        } elseif ($status != static::VALIDATION_OK) {
                            $stats['warnings']++;
                        }

                        fputcsv($reportFile, $row, $delimiter, $enclosure);
                    }
                }

                fclose($chunkFile);
            }
        }

        fclose($reportFile);

        // --- send email to customer

        $this->SendNotificationEmail(static::STATUS_VALIDATED);
        // --- update status

        $DBArray['Status'] = static::STATUS_VALIDATED;
        $DBArray['ValidationFinishedOn'] = time();

        if (!empty($stats['errors']) || !empty($stats['warnings'])) {
            $stats['download'] = file_create_url($reportFilename);
        }

        $stats['valid'] -= $stats['warnings'];
        $DBArray['Stats']['validation'] = $stats;

        $total = $stats['valid'] + $stats['warnings'] + $stats['errors'];

        $objectMV = new ApiMillionVerifier($DBArray['RelOwnerUserID']);
        $settings = $objectMV->GetSettings();
        if (
            $stats['errors'] / $total >= $settings['denyThreshold'] &&
            $stats['errors'] >= $settings['denyThresholdMinDenies']
        ) {
            $DBArray['Status'] = static::STATUS_DENIED;
        }

        $this->UpdateDB($DBArray);
    }

    /**
     * Update import file status after a queue job has processed it
     * @param $UserID
     * @param $ImportID
     * @param $ImportFile
     * @param $CurrentStatus
     * @param $NewStatus
     */
    public static function UpdateFileStatus($UserID, $ImportID, $ImportFile, $CurrentStatus, $NewStatus)
    {
        // due to parallel jobs, there could be race conditions with read -> update data -> write
        // so instead we do an update replace
        // the serialized filename + status is a unique string in the Data field

        $needle = serialize(array($ImportFile => $CurrentStatus));
        $replace = serialize(array($ImportFile => $NewStatus));

        //trim the array from the serialized string
        $needle = str_replace(['a:1:{', '}'], '', $needle);
        $replace = str_replace(['a:1:{', '}'], '', $replace);

        $tablename = '{' . static::$DBTableName . '}';
        $userfield = static::$DBUserField;
        $serialfield = static::$DBSerialField;

        $sql = "UPDATE $tablename SET DATA = REPLACE(DATA, :Needle, :Replace)
                WHERE $userfield = :UserID AND $serialfield = :ImportID";

        db_query($sql, array(
            ':UserID' => $UserID,
            ':ImportID' => $ImportID,
            ':Needle' => $needle,
            ':Replace' => $replace
        ));
    }

    public function ImportCreateQueueJobs()
    {
        $DBArray = $this->GetData();

        $DBArray['Status'] = static::STATUS_IMPORTING;
        $DBArray['ImportStartedOn'] = time();

        if (!$this->UpdateDB($DBArray)) {
            return false;
        }

        $LogID = ProcessLog::Create('import_subscribers_import');

        $addedItems = 0;
        foreach ($DBArray['Files'] as $filename => $status) {
            if ($status == static::FILE_STATUS_VALIDATED) {
                $item = array(
                    'UserID' => $DBArray['RelOwnerUserID'],
                    'ImportID' => $DBArray['SeqNo'],
                    'ImportFile' => $filename,
                    'Session' => $DBArray['Session'],
                );

                ProcessLog::AddQueueItem($LogID, $item);

                $addedItems++;
            }
        }

        if (empty($addedItems)) {
            $DBArray['Status'] = static::STATUS_DONE;
            if (empty($DBArray['ImportStartedOn'])) {
                $DBArray['ImportStartedOn'] = time();
            }

            $DBArray['ImportFinishedOn'] = time();

            if (!$this->UpdateDB($DBArray)) {
                //Note: in this case, the status will not be set to STATUS_IMPORTING
                //      so the jobs will terminate themselves without doing anything
                return false;
            }
        }

        return true;
    }

    public static function ImportQueueWorker($data)
    {
        ProcessLog::QueueItemStarted($data);

        $UserID = $data['UserID'];
        $ImportID = $data['ImportID'];
        $ImportFile = $data['ImportFile'];
        $Session = $data['Session'];

        /* @var VarImport $ObjectImport */
        $ObjectImport = static::FromID($UserID, $ImportID);

        if (
            !$ObjectImport ||
            $ObjectImport->GetData('Status') != static::STATUS_IMPORTING ||
            $ObjectImport->GetData('Session') != $Session
        ) {
            //this is OK, the import was deleted or canceled by the user
            //Note: we need to check the session in case jobs have been created, user cancels,
            //      re-uploads other files, validates and gets into STATUS_IMPORTING before
            //      the first jobs have been canceled
            $data['message'] = "Import aborted by user ($UserID, $ImportID, $ImportFile, $Session)";
            ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
            return;
        }

        $DBArray = $ObjectImport->GetData();

        $NewStatus = static::FILE_STATUS_IMPORTED;

        if ($DBArray['Files'][$ImportFile] == static::FILE_STATUS_VALIDATED) {
            $filename = $ObjectImport->GetChunkFilename($ImportFile);

            $json = file_get_contents($filename);

            if (empty($json)) {
                $data['message'] = "Error reading import chunk for import ($UserID, $ImportID, $ImportFile)";
                ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
                return;
            }

            $chunk = json_decode($json, true);

            if (empty($chunk)) {
                $data['message'] = "Error decoding import chunk json for import ($UserID, $ImportID, $ImportFile)";
                ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
                return;
            }

            if (!empty($DBArray['ListID'])) {
                $objectList = Lists::FromID($UserID, $DBArray['ListID']);

                if (!$objectList) {
                    $data['message'] = "Error loading import doi process ($UserID, $ImportID, {$DBArray['ListID']})";
                    ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
                    return;
                }

                $arrayListInformation = $objectList->GetData();
            } else {
                //user is allowed to use single optin and chose to
                $arrayListInformation = array(
                    'ListID' => 0,
                    'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
                );
            }

            $errorRows = [];

            foreach ($chunk as $row) {
                $ImportValues = [
                    'SubscriberID' => 0,
                    'ReferenceID' => 0,
                    'Optional' => 0,
                    'EmailAddress' => '',
                    'PhoneNumber' => '',
                    'ImportOptInDate' => 0,
                    'ImportOptInIPAddress' => '',
                    'ImportConfirmationDate' => 0,
                    'SubscriptionReferrer' => '',
                    'OtherFields' => [],
                    'Taggings' => [],
                ];

                foreach ($DBArray['ImportFields'] as $field) {
                    $index = $field['column'];
                    $value = $row[$index];

                    if (empty($value)) {
                        continue;
                    }

                    if ($value != static::WARNING_TAG_INDICATOR) {
                        //the original value failed the validation but the subscriber can still be imported
                        //Note: $ImportValues is passed by reference
                        VarImport::ImportPrepare($UserID, $field, $row[$index], $ImportValues);
                    }
                }

                //the last column in the row is our error/warning flag
                $error = array_pop($row);

                if ($error == static::VALIDATION_WARNING && empty($DBArray['ImportWarnings'])) {
                    //the user chose not to import contacts with warnings
                    continue;
                }

                $Parameters = [
                    'UserID' => $UserID,
                    'SubscriberID' => $ImportValues['SubscriberID'],
                    'ReferenceID' => $ImportValues['ReferenceID'],
                    'ListInformation' => $arrayListInformation,
                    'OptInSubscribeTo' => 0,
                    'EmailAddress' => $ImportValues['EmailAddress'],
                    'PhoneNumber' => $ImportValues['PhoneNumber'],
                    'IPAddress' => '0.0.0.0',
                    //OptIn IP address used for imported subscribers
                    'SubscriptionReferrer' => $ImportValues['SubscriptionReferrer'],
                    'SubscriptionStatus' => 0,
                    'SMSSubscriptionStatus' => 0,
                    'OtherFields' => $ImportValues['OtherFields'],
                    'UpdateIfUnsubscribed' => false,
                    'UpdatePhoneNumberIfUnsubscribed' => false,
                    'SendConfirmationEmail' => true,
                    'UpdateStatistics' => true,
                    'TriggerAutoResponders' => true,
                    'ListFormID' => 0,
                    'ImportOptInDate' => $ImportValues['ImportOptInDate'],
                    //OptInDate for imported subscribers from CSV file
                    'ImportOptInIPAddress' => $ImportValues['ImportOptInIPAddress'],
                    //OptInIP-Address for imported subscribers from CSV file
                    'ImportConfirmationDate' => $ImportValues['ImportConfirmationDate'],
                    //Confirmation Date for imported subscribers from CSV file
                    'UpdatePrimarySubscription' => 0,
                    // Updates primary Subscription, even if another subscription is specified (don't create new subscription with same reference=,
                    'Optional' => $ImportValues['Optional'],
                    // is specified subscription optional for given reference
                    'Imported' => true,
                ];

                $result = Subscribers::Subscribe($Parameters);

                if ($result[0]) {
                    $tagIDs = [];

                    if (!empty($ImportValues['Tagging'])) {
                        $tagIDs = array_unique($ImportValues['Tagging']);
                    }

                    if (!empty($DBArray['AssignTags'])) {
                        $tagIDs = array_merge($tagIDs, $DBArray['AssignTags']);
                    }

                    if ($error == static::VALIDATION_WARNING && !empty($DBArray['WarningTags'])) {
                        $tagIDs = array_merge($tagIDs, $DBArray['WarningTags']);
                    }

                    if (!empty($tagIDs)) {
                        $SubscriberID = $result[1];
                        foreach ($tagIDs as $tagID) {
                            Subscribers::TagSubscriber(
                                $UserID,
                                $SubscriberID,
                                $tagID,
                                $ImportValues['ReferenceID'],
                                true
                            );
                        }
                    }
                    $warningMessages = [];
                    if ($result[2]) {
                        $warningMessages[] = t('Import::Warning !message', [
                            '!message' => static::getErrorMessage($result[2])
                        ]);
                    }
                    if (!empty($ImportValues['CreateTagError'])) {
                        $warningMessages[] = $ImportValues['CreateTagError'];
                    }

                    if (!empty($warningMessages)) {
                        $row[] = implode('; ', $warningMessages);
                        $row[] = static::IMPORT_WARNING;
                        $errorRows[] = $row;
                    }
                } else {
                    //add 1 column with error description to row
                    $errorMessage = static::getErrorMessage($result[1]);

                    if (!empty($ImportValues['CreateTagError'])) {
                        $errorMessage .= "; " . $ImportValues['CreateTagError'];
                    }

                    $row[] = $errorMessage;
                    $row[] = static::IMPORT_ERROR;
                    $errorRows[] = $row;
                }
            }

            static::UpdateFileStatus($UserID, $ImportID, $ImportFile, static::FILE_STATUS_VALIDATED, $NewStatus);

            if (!empty($errorRows)) {
                if (!$ObjectImport->SaveImportErrorReport($ImportFile, $errorRows)) {
                    $data['message'] = "Error writing error report for import chunk ($UserID, $ImportID, $ImportFile)";
                    ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
                    return;
                }
            }
        }

        /* @var VarImport $ObjectImport */
        $ObjectImport = VarImport::FromID($UserID, $ImportID);

        if (!$ObjectImport) {
            //this could mean that the serialized Data has been destroyed by UPDATE REPLACE
            //log a hint in the watchdog
            $data['message'] = 'Error retrieving import after updating file status to IMPORTED.';
            ProcessLog::QueueItemFailed(ProcessLog::FAILED_CANCELED, $data);
            return;
        }

        $DBArray = $ObjectImport->GetData();

        $done = array_filter($DBArray['Files'], function ($item) use ($NewStatus) {
            return $item == $NewStatus;
        });

        $total = count($DBArray['Files']);
        $finished = count($done);

        $lastFile = ($total == $finished);

        $complete = ProcessLog::QueueItemProcessed($data);

        if ($lastFile || $complete) {
            if (!$complete || !$lastFile) {
                //unlikely, but log in case it happens

                //TODO error status
                watchdog(
                    'subscriber-import',
                    'Import Queue mismatch: Last queue item complete != all files imported.',
                    array(
                        '!isLastFile' => $lastFile,
                        '!isProcessedComplete' => $complete,
                        '!total' => $total,
                        '!imported' => $finished,
                        '!import' => $DBArray
                    ),
                    WATCHDOG_WARNING
                );
            }

            $ObjectImport->ImportFinalize();
        }
    }

    private static function getErrorMessage($errorCode)
    {
        switch ($errorCode) {
            case Subscribers::SUBSCRIBE_ERROR_INVALID_EMAIL_ADDRESS:
                return t('Import::Error::Invalid email address');
            case Subscribers::SUBSCRIBE_ERROR_DUPLICATE_EMAIL_ADDRESS:
                return t('Import::Error::Email address exist in another subscriber');
            case Subscribers::SUBSCRIBE_ERROR_UNSUBSCRIBED_EMAIL_ADDRESS:
                return t('Import::Error::Email address is already unsubscribed');
            case Subscribers::SUBSCRIBE_ERROR_INVALID_USER_INFORMATION:
                return t('Import::Error::Invalid user');
            case Subscribers::SUBSCRIBE_ERROR_INVALID_LIST_INFORMATION:
                return t('Import::Error::Invalid DOI information');
            case Subscribers::SUBSCRIBE_ERROR_CUSTOMFIELD_DATA:
                return t('Import::Error::Invalid custom field data');
            case Subscribers::SUBSCRIBE_ERROR_INVALID_SUBSCRIBER:
                return t('Import::Error::Invalid subscriber');
            case Subscribers::SUBSCRIBE_ERROR_DUPLICATE_PHONE_NUMBER:
                return t('Import::Error::Phone number already used in another subscriber');
            case Subscribers::SUBSCRIBE_ERROR_UNSUBSCRIBED_PHONE_NUMBER:
                return t('Import::Error::Phone number is already unsubscribed');
            case Subscribers::SUBSCRIBE_ERROR_INVALID_PHONE_NUMBER:
                return t('Import::Error::Invalid phone number');
            case Subscribers::SUBSCRIBE_ERROR_INTERNAL:
            default:
                return t('Import::Error::Unexpected error');
        }
    }

    public static function ImportPrepare($UserID, $Field, $Value, &$ImportValues)
    {
        if ($Field['type'] == 'subscription') {
            switch ($Field['id']) {
                case 'Hash':
                    //see Subscription::EncodeExportKey
                    $decoded = Subscription::DecodeExportKey($Value);

                    if ($decoded['u'] == $UserID) {
                        if ($decoded['t'] == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
                            $ImportValues['EmailAddress'] = $decoded['c'];
                        } elseif ($decoded['t'] == Subscription::SUBSCRIPTIONTYPE_SMS) {
                            $ImportValues['PhoneNumber'] = $decoded['c'];
                        }

                        $ImportValues['SubscriberID'] = $decoded['s'] ?? null;
                        $ImportValues['ReferenceID'] = $decoded['r'] ?? 0;
                        $ImportValues['Optional'] = $decoded['o'] ?? 0;

                        //set a flag that the hash has been used so fields like
                        //EmailAddress, SubscriberID won't be overwritten by later columns
                        //Note: the hash overwrites all previously set values
                        $ImportValues['HashFound'] = true;
                    }

                    break;

                case 'SubscriberID':
                case 'EmailAddress':
                case 'PhoneNumber':
                    if (empty($ImportValues[$Field['id']])) {
                        //Note: SubscriberID, EmailAddress and PhoneNumber could have already been set via the hash
                        //      which is the more reliable value
                        $ImportValues[$Field['id']] = $Value;
                    }

                    break;

                case 'ImportOptInDate':
                case 'ImportConfirmationDate':
                    //If format does not contain the character ! then portions of the generated time which are not specified in format will be set to the current system time.
                    //@see http://www.php.net/manual/de/datetime.createfromformat.php
                    $DateFormat = "!{$Field['format']}";
                    $DateObject = date_create_from_format($DateFormat, $Value);

                    if ($DateObject) {
                        $ImportValues[$Field['id']] = date_timestamp_get($DateObject);
                    }

                    break;

                case 'ImportOptInIPAddress':
                case 'IPAddress':
                case 'SubscriptionReferrer':
                    $ImportValues[$Field['id']] = $Value;
                    break;
            }
        } elseif ($Field['type'] == 'custom' || $Field['type'] == 'global') {
            static $CustomFields;

            if (!isset($CustomFields)) {
                $CustomFields = static::GetAccountCustomFields($UserID);
            }

            $ArrayField = $CustomFields[$Field['id']];

            if (
                in_array(
                    $ArrayField['FieldTypeEnum'],
                    [CustomFields::TYPE_DATE, CustomFields::TYPE_DATETIME, CustomFields::TYPE_TIME]
                )
            ) {
                //If format does not contain the character ! then portions of the generated time which are not specified in format will be set to the current system time.
                //@see http://www.php.net/manual/de/datetime.createfromformat.php
                $DateFormat = "!{$Field['format']}";
                $DateObject = date_create_from_format($DateFormat, $Value);

                if ($DateObject) {
                    $ImportValues['OtherFields']["CustomField{$Field['id']}"] = date_timestamp_get($DateObject);
                }
            } elseif ($ArrayField['FieldTypeEnum'] == CustomFields::TYPE_DECIMAL) {
                $Value = UtilsNumber::formatFloatFromInput((string)$Value);
                $ImportValues['OtherFields']["CustomField{$Field['id']}"] = CustomFields::ConvertCustomFieldDataFromWidget(
                    $Value,
                    CustomFields::WIDGET_DECIMAL
                );
            } else {
                $ImportValues['OtherFields']["CustomField{$Field['id']}"] = $Value;
            }
        } elseif ($Field['type'] == 'tag') {
            //assign selected tag if $Value is not empty
            if (!empty(trim($Value))) {
                $ImportValues['Tagging'][] = $Field['id'];
            }
        } elseif ($Field['type'] === 'tag-create') {
            // $Value contains a list of tag names separated by $Field['format']
            // Tags that do not exists will be created
            // all tags will then be assigned to the subscriber
            $tagNames = explode($Field['format'] ?? ',', trim($Value));
            if (!empty($tagNames)) {
                $tagNames = array_filter(array_map(static function ($value) {
                    return trim(is_numeric($value) ? "_$value" : $value);
                }, $tagNames));
                $tagIDs = Tag::CreateManualTagByTagName($UserID, $tagNames, true);
                if (is_array($tagIDs) && !empty($tagIDs)) {
                    $tags = Tag::RetrieveManualTags($UserID);
                    $createdTagNames = [];
                    foreach ($tagIDs as $tagId) {
                        if (!empty($tags[$tagId])) {
                            $createdTagNames[] = $tags[$tagId]['Name'];
                            $ImportValues['Tagging'][] = $tagId;
                        }
                    }
                    $notCreated = array_diff($tagNames, $createdTagNames);
                    if (!empty($notCreated)) {
                        $ImportValues['CreateTagError'] = t('Import::Error::TagCreate::!tags could not be created', [
                            '!tags' => implode(', ', $notCreated)
                        ]);
                    }
                }
            }
        }
    }

    public function ImportFinalize()
    {
        $DBArray = $this->GetData();

        // --- create 1 downloadable validation file from parts

        $delimiter = chr(59); //semicolon
        $enclosure = chr(34); //double quotes

        $path = static::GetFilepath($DBArray['RelOwnerUserID'], $DBArray['SeqNo']);

        //create the user directory for exports and/or check if it is writable
        if (!file_prepare_directory($path, FILE_CREATE_DIRECTORY || FILE_MODIFY_PERMISSIONS)) {
            //TODO: error status
            return;
        }

        $reportFilename = $this->GetErrorReportFilename();

        $reportFile = fopen($reportFilename, 'a'); //open for write (append)

        if (!$reportFile) {
            //TODO: error status
            return;
        }

        $total = 0;
        $warnings = 0;
        $errors = 0;

        foreach ($DBArray['Files'] as $file => $status) {
            $json = file_get_contents($this->GetChunkFilename($file));

            if (!empty($json) && $valid = json_decode($json, true)) {
                $total += count($valid);
            }

            //same filename as upload chunk with added validation marker and csv extension
            $chunkFilename = $this->GetChunkErrorFilename($file);

            if (file_exists($chunkFilename)) {
                //Note: if an upload chunk did not contain errors or warnings, there won't be a file

                $chunkFile = fopen($chunkFilename, 'r'); //open read

                if (!$chunkFile) {
                    //TODO: error status
                    continue;
                }

                while (($row = fgetcsv($chunkFile, 0, $delimiter, $enclosure)) !== false) {
                    if (!empty($row)) {
                        array_pop($row) == static::IMPORT_WARNING ? $warnings++ : $errors++;


                        fputcsv($reportFile, $row, $delimiter, $enclosure);
                    }
                }

                fclose($chunkFile);
            }
        }

        fclose($reportFile);

        $stats = [
            'success' => $total - $errors - $warnings,
            'errors' => $errors,
            'warnings' => $warnings,
            'download' => ''
        ];

        if (!empty($errors) || !empty($warnings)) {
            $stats['download'] = file_create_url($reportFilename);
        }

        // --- send email to customer

        $this->SendNotificationEmail(static::STATUS_DONE);

        // --- update status

        $DBArray['Status'] = static::STATUS_DONE;
        $DBArray['ImportFinishedOn'] = time();
        $DBArray['Stats']['import'] = $stats;

        $this->UpdateDB($DBArray);
    }

    public function CancelStatus()
    {
        $DBArray = $this->GetData();

        //Note: do not reset file settings and assign fields
        //      the user could upload the same file and still has all settings
        $DBArray['Status'] = static::STATUS_INIT;
        $DBArray['ExpiresOn'] = 0;
        $DBArray['Files'] = array();
        $DBArray['Session'] = '';

        return $DBArray;
    }

    public function Progress()
    {
        $DBArray = $this->GetData();

        $current = 0;
        $total = 0;
        $start = 0;
        $estimated = 0;

        $files = $DBArray['Files'];

        if ($DBArray['Status'] == static::STATUS_VALIDATING) {
            $done = array_filter($files, function ($item) {
                return $item == VarImport::FILE_STATUS_VALIDATED;
            });

            $current = count($done);
            $total = count($files);
            $start = $DBArray['ValidationStartedOn'];
        } elseif ($DBArray['Status'] == static::STATUS_IMPORTING) {
            $done = array_filter($files, function ($item) {
                return $item == VarImport::FILE_STATUS_IMPORTED;
            });

            $current = count($done);
            $total = count($files);
            $start = $DBArray['ImportStartedOn'];
        }

        $finished = ($current >= $total);

        if (!empty($start) && !empty($current) && !empty($total) && !$finished) {
            $now = time();
            $elapsed = $now - $start;
            $estimated = ceil($elapsed * $total / $current);
        }

        return array(
            'status' => $DBArray['Status'],
            'current' => $current,
            'total' => $total,
            'start' => empty($start) ? '' : Dates::formatDate(Dates::FORMAT_DMYHI, (int) $start),
            'estimated' => $estimated,
            'finished' => $finished
        );
    }

    public function HasExpired()
    {
        $expiresOn = $this->GetData('ExpiresOn');

        //Note: an import can only expire if files have been uploaded
        return !empty($expiresOn) && $expiresOn <= time();
    }


    public static function GetFilepath($UserID, $ImportID)
    {
        if (empty($UserID) || empty($ImportID)) {
            return '';
        }

        //group import files to keep subdirectory count low
        $userSlice = floor($UserID / 100) * 100;

        return static::FILE_ROOT . static::ENV_DATA_USERS_DIRECTORY . "/$userSlice/$UserID/$ImportID";
    }

    public function GetChunkFilename($ChunkFilename): string
    {
        $DBArray = $this->GetData();
        $path = static::GetFilepath($DBArray['RelOwnerUserID'], $DBArray['SeqNo']);
        return "$path/$ChunkFilename";
    }

    public function GetValidationReportFilename(): string
    {
        $DBArray = $this->GetData();
        $path = static::GetFilepath($DBArray['RelOwnerUserID'], $DBArray['SeqNo']);
        $date = date('Y_m_d_h_i_s', $DBArray['CreatedOn']);
        $name = static::SanitizeFileName($DBArray['Name']);
        return "$path/{$name}_{$date}_validation_report.csv";
    }

    public function GetErrorReportFilename(): string
    {
        $DBArray = $this->GetData();
        $path = static::GetFilepath($DBArray['RelOwnerUserID'], $DBArray['SeqNo']);
        $date = date('Y_m_d_h_i_s', $DBArray['CreatedOn']);
        $name = static::SanitizeFileName($DBArray['Name']);
        return "$path/{$name}_{$date}_error_report.csv";
    }

    public function GetChunkValidationFilename($ChunkFilename): string
    {
        $DBArray = $this->GetData();
        $path = static::GetFilepath($DBArray['RelOwnerUserID'], $DBArray['SeqNo']);
        return "$path/{$ChunkFilename}_validation.csv";
    }

    public function GetChunkErrorFilename($ChunkFilename): string
    {
        $DBArray = $this->GetData();
        $path = static::GetFilepath($DBArray['RelOwnerUserID'], $DBArray['SeqNo']);
        return "$path/{$ChunkFilename}_error.csv";
    }

    public function SanitizeFileName($filename): string
    {
        return preg_replace('/[^a-z0-9-_]/', '_', strtolower(kt_strip_utf8mb4($filename)));
    }

    public static function CheckDuplicateName($UserID, $Name)
    {
        $index = static::Index($UserID);

        foreach ($index as $import) {
            if ($import['Name'] == $Name) {
                return $import[static::$DBSerialField];
            }
        }

        return false;
    }

    public function GetEditURL($CampaignID = 0)
    {
        $UserID = $this->GetData('RelOwnerUserID');
        $ImportID = $this->GetData('SeqNo');
        return APP_URL . "/application/$UserID/import/$ImportID";
    }

    public static function DetectFormat($UserID, $Header, $Header2 = array())
    {
        $Scores = array();

        //check if it's the new KlickTipp export (hashed field ids in $Header2 (2. row)
        $klicktipp = static::DetectFormatKlicktipp($UserID, $Header, $Header2);
        if (empty($klicktipp['score'])) {
            //just in case, the first header has been removed
            $klicktipp = static::DetectFormatKlicktipp($UserID, $Header, $Header);
        }

        if ($klicktipp['score'] >= static::DETECT_FORMAT_MIN_SCORE) {
            $Scores[] = $klicktipp;
        }

        //check if any other template fits

        $templates = static::GetTemplateDefinitions();

        foreach ($templates as $template) {
            $score = 0;
            $assigned = array();
            foreach ($template['ImportFields'] as $field) {
                $index = array_search($field['Label'], $Header);

                if ($index !== false) {
                    //simple scoring:
                    //count the matching header label length to calculate a score
                    $score += strlen($field['Label']);

                    $target = $field['AssignField'];
                    if (empty($assigned[$target])) {
                        $assigned[$target] = array(
                            'header' => $field['Label'],
                            'column' => $index,
                            'id' => $field['AssignField'],
                            'type' => $field['Type'],
                            'format' => empty($field['Format']) ? '' : $field['Format']
                        );
                    }
                    //else: the field has already been assign with a higher priority
                }
            }

            if ($score >= static::DETECT_FORMAT_MIN_SCORE) {
                $Scores[] = array(
                    'name' => $template['Name'],
                    'score' => $score,
                    'importFields' => array_values($assigned),
                    'header2' => 0
                );
            }
        }

        //sort descending by score
        //Note: <=> is PHP 7+
        usort($Scores, function ($item1, $item2) {
            return $item2['score'] <=> $item1['score'];
        });

        return $Scores;
    }

    public static function DetectFormatKlicktipp($UserID, $Header = array(), $Header2 = array())
    {
        static $importFields;

        if (!isset($importFields)) {
            $importFields = static::GetImportFields($UserID);
        }

        $assigned = [];
        $score = 0;
        foreach ($importFields as $field) {
            $index = array_search($field['csvHash'], $Header2);

            if ($index !== false) {
                //use a high score for each csvHash matched
                $score += 100;

                if (empty($assigned[$field['csvHash']])) {
                    $assigned[$field['csvHash']] = [
                        'header' => $Header[$index],
                        'column' => $index,
                        'id' => $field['id'],
                        'type' => $field['type'],
                        'format' => empty($field['format']) ? '' : $field['format']
                    ];
                }
                //else: multiple columns with the same hash exist in the csv (ignore latter)
            }
        }

        return [
            'name' => 'KlickTipp',
            'score' => $score,
            'importFields' => array_values($assigned),
            'header2' => 1
        ];
    }

    public static function GetImportFields($UserID)
    {
        //subscription fields

        $fields = [
            [
                'id' => 'Hash',
                'label' => t('KlickTipp ID'),
                'type' => 'subscription',
                'csvHash' => base64_encode(json_encode(['s', 'ktid'])),
                'fieldType' => CustomFields::TYPE_SINGLE,
                'formatOptions' => [
                    '' => t('ImportFieldFormat::KlickTipp ID')
                ]
            ],
            [
                'id' => 'SubscriberID',
                'label' => t('Contact ID'),
                'type' => 'subscription',
                'csvHash' => base64_encode(json_encode(['s', 'sid'])),
                'fieldType' => CustomFields::TYPE_NUMBER,
                'formatOptions' => [
                    '' => t('ImportFieldFormat::Contact ID')
                ]
            ],
            [
                'id' => 'EmailAddress',
                'label' => t('Email Address'),
                'type' => 'subscription',
                'csvHash' => base64_encode(json_encode(['s', 'email'])),
                'fieldType' => CustomFields::TYPE_EMAIL,
                'formatOptions' => [
                    '' => t('ImportFieldFormat::Email Address')
                ]
            ],
            [
                'id' => 'PhoneNumber',
                'label' => t('SMS Phone number'),
                'type' => 'subscription',
                'csvHash' => base64_encode(json_encode(['s', 'phone'])),
                'fieldType' => 'phone',
                'formatOptions' => [
                    '' => t('ImportFieldFormat::SMS Phone number')
                ]
            ],
            [
                'id' => 'ImportOptInDate',
                'label' => t('Opt-In Date'),
                'type' => 'subscription',
                'csvHash' => base64_encode(json_encode(['s', 'oidate'])),
                'fieldType' => CustomFields::TYPE_DATETIME,
                'formatOptions' => static::GetDateFormats(),
                'format' => variable_get(Dates::FORMAT_DMYHIS, 'd.m.Y H:i:s')
            ],
            [
                'id' => 'ImportOptInIPAddress',
                'label' => t('Opt-In IP'),
                'type' => 'subscription',
                'csvHash' => base64_encode(json_encode(['s', 'oiip'])),
                'fieldType' => 'ip',
                'formatOptions' => [
                    '' => t('ImportFieldFormat::Opt-In IP')
                ],
            ],
            [
                'id' => 'ImportConfirmationDate',
                'label' => t('Confirmation Date'),
                'type' => 'subscription',
                'csvHash' => base64_encode(json_encode(['s', 'condate'])),
                'fieldType' => CustomFields::TYPE_DATETIME,
                'formatOptions' => static::GetDateFormats(),
                'format' => variable_get(Dates::FORMAT_DMYHIS, 'd.m.Y H:i:s')
            ],
            [
                'id' => 'IPAddress',
                'label' => t('Confirmation IP'),
                'type' => 'subscription',
                'csvHash' => base64_encode(json_encode(['s', 'conip'])),
                'fieldType' => 'ip',
                'formatOptions' => [
                    '' => t('ImportFieldFormat::Confirmation IP')
                ]
            ],
            [
                'id' => 'SubscriptionReferrer',
                'label' => t('Referrer'),
                'type' => 'subscription',
                'category' => '',
                'csvHash' => base64_encode(json_encode(['s', 'refer'])),
                //Note: we cannot force valid urls here
                'fieldType' => CustomFields::TYPE_SINGLE,
                'formatOptions' => [
                    '' => t('ImportFieldFormat::Referrer')
                ]
            ],

        ];

        // global/custom fields

        $AllCustomFields = static::GetAccountCustomFields($UserID);

        $DateFormats = static::GetDateFormats();

        foreach ($AllCustomFields as $field) {
            $cat = '';
            $fieldParameters = $field['FieldParameters'];
            if (is_array($fieldParameters)) {
                $cat = $fieldParameters['FieldCategory'] ?? '';
            }

            switch ($field['FieldTypeEnum']) {
                case CustomFields::TYPE_DATE:
                    $formatOptions = $DateFormats;
                    $format = variable_get(Dates::FORMAT_DMY, 'd.m.Y');
                    break;
                case CustomFields::TYPE_DATETIME:
                    $formatOptions = $DateFormats;
                    $format = variable_get(Dates::FORMAT_DMYHIS, 'd.m.Y H:i:s');
                    break;
                case CustomFields::TYPE_TIME:
                    $formatOptions = $DateFormats;
                    $format = 'H:i';
                    break;
                default:
                    $formatOptions = array('' => CustomFields::GetFieldTypeTranslation($field));
                    $format = '';
            }

            $fields[] = array(
                'id' => $field['CustomFieldID'],
                'label' => CustomFields::GetFieldName($field),
                'type' => $field['IsGlobal'] ? 'global' : 'custom',
                'category' => empty($cat) ? '' : $cat,
                'csvHash' => base64_encode(json_encode(['f', $field['CustomFieldID']])),
                'fieldType' => $field['FieldTypeEnum'],
                'formatOptions' => $formatOptions,
                'format' => $format
            );
        }

        // tags

        $Tags = static::GetAccountManualTags($UserID);

        foreach ($Tags as $tag) {
            $fields[] = array(
                'id' => $tag['TagID'],
                'label' => $tag['TagName'],
                'type' => 'tag',
                'category' => '',
                'csvHash' => base64_encode(json_encode(['t', $tag['TagID']])),
                'fieldType' => CustomFields::TYPE_SINGLE, //in the csv, the value can be anything
                'formatOptions' => [
                    '' => t('ImportFieldFormat::Tag ID')
                ]
            );
        }

        return $fields;
    }

    public static function GetTemplateDefinitions()
    {
        // detection value == 0, use length of label as value
        // some fields have the same AssignField -> first in array takes precedent (only dialog suggestion)

        return [
            [
                'Name' => 'GetResponse',
                'ImportFields' => [
                    [
                        'Label' => 'id', //example VT654QF
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'campaign',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'name',
                        'AssignField' => 'FirstName', //could also be full name
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'email',
                        'AssignField' => 'EmailAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'sign_up',
                        'AssignField' => 'ImportConfirmationDate',
                        'Type' => 'subscription',
                        'Format' => 'Y-m-d H:i:s'
                    ],
                    [
                        'Label' => 'optin', // just "single" or "double"
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'origin', // just the source "api" etc.
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'ip',
                        'AssignField' => 'IPAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'score',
                        'AssignField' => 'LeadValue',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'engagement_score',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'custom:birthdate',
                        'AssignField' => 'Birthday',
                        'Type' => 'field',
                        'Format' => 'Y-m-d'
                    ],
                    [
                        'Label' => 'custom:city',
                        'AssignField' => 'City',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'city', //older format
                        'AssignField' => 'City',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:comment',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'custom:company',
                        'AssignField' => 'Company',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:country',
                        'AssignField' => 'Country',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'country', //older format
                        'AssignField' => 'Country',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:fax',
                        'AssignField' => 'Fax',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:gender',
                        'AssignField' => 'Gender',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:home_phone',
                        'AssignField' => 'PrivatePhone',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:http_referer',
                        'AssignField' => 'SubscriptionReferrer',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'custom_http_referer', //older format
                        'AssignField' => 'SubscriptionReferrer',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'custom:mobile_phone',
                        'AssignField' => 'PhoneNumber',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'custom:phone',
                        'AssignField' => 'Phone',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:postal_code',
                        'AssignField' => 'Zip',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'postal_code', //older format
                        'AssignField' => 'Zip',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:ref',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'custom:state',
                        'AssignField' => 'State',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'region', //older format
                        'AssignField' => 'State',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:street',
                        'AssignField' => 'Street1',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:url',
                        'AssignField' => 'Website',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'custom:work_phone',
                        'AssignField' => 'Phone',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'last_update',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'geo:ip',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'geo:latitude',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'geo:longitude',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'geo:country',
                        'AssignField' => 'Country',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'geo:region',
                        'AssignField' => 'State',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'geo:city',
                        'AssignField' => 'City',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'geo:continent_code',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'geo:country_code',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'geo:postal_code',
                        'AssignField' => 'Zip',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'geo:region_code',
                        'AssignField' => 'Zip',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'geo:dma_code',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'geo:time_zone',
                        'AssignField' => '',
                    ],
                ]
            ],

            // -------------------------------------------------------------------------

            [
                'Name' => 'MailChimp',
                'ImportFields' => [
                    [
                        'Label' => 'Email Address',
                        'AssignField' => 'EmailAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'First Name',
                        'AssignField' => 'FirstName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Vorname', //old german format
                        'AssignField' => 'FirstName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Last Name',
                        'AssignField' => 'LastName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Nachname', //old german format
                        'AssignField' => 'LastName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Address',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Phone Number',
                        'AssignField' => 'PhoneNumber',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Birthday',
                        'AssignField' => 'Birthday',
                        'Type' => 'field',
                        'Format' => 'Y-m-d'
                    ],
                    [
                        'Label' => 'MEMBER_RATING',
                        'AssignField' => 'LeadValue',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'CONFIRM_TIME',
                        'AssignField' => 'ImportConfirmationDate',
                        'Type' => 'subscription',
                        'Format' => 'Y-m-d H:i:s'
                    ],
                    [
                        'Label' => 'CONFIRM_IP',
                        'AssignField' => 'IPAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'OPTIN_TIME',
                        'AssignField' => 'ImportOptInDate',
                        'Type' => 'subscription',
                        'Format' => 'Y-m-d H:i:s'
                    ],
                    [
                        'Label' => 'OPTIN_IP',
                        'AssignField' => 'ImportOptInIPAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'LATITUDE',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'LONGITUDE',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'GMTOFF',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'DSTOFF',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'TIMEZONE',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'CC',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'REGION',
                        'AssignField' => 'State',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'LAST_CHANGED',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'LEID',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'EUID',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'NOTES',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'TAGS',
                        'AssignField' => '',
                    ],
                ]
            ],

            // -------------------------------------------------------------------------

            [
                'Name' => 'CleverReach',
                'ImportFields' => [
                    [
                        'Label' => 'id',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'email',
                        'AssignField' => 'EmailAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'activated',
                        'AssignField' => 'ImportConfirmationDate',
                        'Type' => 'subscription',
                        'Format' => 'Y-m-d H:i:s'
                    ],
                    [
                        'Label' => 'registered',
                        'AssignField' => 'ImportOptInDate',
                        'Type' => 'subscription',
                        'Format' => 'Y-m-d H:i:s'
                    ],
                    [
                        'Label' => 'deactivated',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'points',
                        'AssignField' => 'LeadValue',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'imported',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'bounced',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'last_location',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'last_client',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'source',
                        'AssignField' => 'SubscriptionReferrer',
                        'Type' => 'subscription',
                    ],
                ]
            ],
            [
                'Name' => 'MailPoet',
                //Note: dates contain german month names!!!
                'ImportFields' => [
                    [
                        'Label' => 'E-Mail',
                        'AssignField' => 'EmailAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Vorname',
                        'AssignField' => 'FirstName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Nachname',
                        'AssignField' => 'LastName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'IP-Adresse',
                        'AssignField' => 'IPAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Datum des Eintrags',
                        'AssignField' => 'ImportConfirmationDate',
                        'Type' => 'subscription',
                        'Format' => 'j. F Y H:i'
                    ],
                ]
            ],
            [
                'Name' => 'Aweber',
                //Note: date format 06/18/20 7:13am EDT
                'ImportFields' => [
                    [
                        'Label' => 'Email',
                        'AssignField' => 'EmailAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Name', //full name
                        'AssignField' => 'FirstName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Name 1', //old, also only full name
                        'AssignField' => 'FirstName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Message Number',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Date Added',
                        'AssignField' => 'ImportConfirmationDate',
                        'Type' => 'subscription',
                        'Format' => 'm/d/y H:ia T'
                    ],
                    [
                        'Label' => 'Inactive Date',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Last Followup Date',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Stop Time',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Stop Status',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Status',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Verified',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Misc',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Ad Tracking',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'IP Address',
                        'AssignField' => 'IPAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Add URL',
                        'AssignField' => 'SubscriptionReferrer',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Web Form URL',
                        'AssignField' => 'SubscriptionReferrer',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Country',
                        'AssignField' => 'Country',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Region',
                        'AssignField' => 'State',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'City',
                        'AssignField' => 'City',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Postal Code',
                        'AssignField' => 'Zip',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Postal',
                        'AssignField' => 'Zip',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Latitude',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Longitude',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'DMA Code',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Area Code',
                        'AssignField' => 'Zip',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Tags',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Contact message',
                        'AssignField' => '',
                    ],
                ]
            ],
            [
                'Name' => 'KlickTipp', //before column hash
                'ImportFields' => [
                    [
                        'Label' => t('KlickTipp ID'),
                        'AssignField' => 'Hash',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'KlickTipp ID',
                        'AssignField' => 'Hash',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => t('ContactInfo'),
                        'AssignField' => '',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'ContactInfo',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('SubscriptionType'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'SubscriptionType',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('Context'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Context',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('Optional'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Optional',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('Contact ID'),
                        'AssignField' => 'SubscriberID',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Contact ID',
                        'AssignField' => 'SubscriberID',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => t('Email Address'),
                        'AssignField' => 'EmailAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Email Address',
                        'AssignField' => 'EmailAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => t('Opt-In Date'),
                        'AssignField' => 'ImportOptInDate',
                        'Type' => 'subscription',
                        'Format' => variable_get(Dates::FORMAT_DMYHIS, 'd.m.Y H:i:s')
                    ],
                    [
                        'Label' => 'Opt-In Date',
                        'AssignField' => 'ImportOptInDate',
                        'Type' => 'subscription',
                        'Format' => variable_get(Dates::FORMAT_DMYHIS, 'd.m.Y H:i:s')
                    ],
                    [
                        'Label' => t('Opt-In IP'),
                        'AssignField' => 'ImportOptInIPAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Opt-In IP',
                        'AssignField' => 'ImportOptInIPAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => t('Confirmation Date'),
                        'AssignField' => 'ImportConfirmationDate',
                        'Type' => 'subscription',
                        'Format' => variable_get(Dates::FORMAT_DMYHIS, 'd.m.Y H:i:s')
                    ],
                    [
                        'Label' => 'Confirmation Date',
                        'AssignField' => 'ImportConfirmationDate',
                        'Type' => 'subscription',
                        'Format' => variable_get(Dates::FORMAT_DMYHIS, 'd.m.Y H:i:s')
                    ],
                    [
                        'Label' => t('Confirmation IP'),
                        'AssignField' => 'IPAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Confirmation IP',
                        'AssignField' => 'IPAddress',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => t('Verification'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Verification',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('Referrer'),
                        'AssignField' => 'SubscriptionReferrer',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'Referrer',
                        'AssignField' => 'SubscriptionReferrer',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => t('Subscription Status'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Subscription Status',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('Bounce status'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Bounce status',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('Unsubscription Date'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Unsubscription Date',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('Unsubscription IP'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Unsubscription IP',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('Unsubscription Status'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'Unsubscription Status',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('SMS Phone number'),
                        'AssignField' => 'PhoneNumber',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => 'SMS Phone number',
                        'AssignField' => 'PhoneNumber',
                        'Type' => 'subscription',
                    ],
                    [
                        'Label' => t('SMS Subscription status'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'SMS Subscription status',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('SMS Subscription date'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'SMS Subscription date',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('SMS Unsubscription date'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'SMS Unsubscription date',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('SMS Bounce status'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'SMS Bounce status',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('SMS Subscription message'),
                        'AssignField' => '',
                    ],
                    [
                        'Label' => 'SMS Subscription message',
                        'AssignField' => '',
                    ],
                    [
                        'Label' => t('Firstname'),
                        'AssignField' => 'FirstName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Firstname',
                        'AssignField' => 'FirstName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Lastname'),
                        'AssignField' => 'LastName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Lastname',
                        'AssignField' => 'LastName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Company name'),
                        'AssignField' => 'CompanyName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Company name',
                        'AssignField' => 'CompanyName',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Street 1'),
                        'AssignField' => 'Street1',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Street 1',
                        'AssignField' => 'Street1',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Street 2'),
                        'AssignField' => 'Street2',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Street 2',
                        'AssignField' => 'Street2',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('City'),
                        'AssignField' => 'City',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'City',
                        'AssignField' => 'City',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('State'),
                        'AssignField' => 'State',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'State',
                        'AssignField' => 'State',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Zip'),
                        'AssignField' => 'Zip',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Zip',
                        'AssignField' => 'Zip',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Country'),
                        'AssignField' => 'Country',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Country',
                        'AssignField' => 'Country',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Private Phone'),
                        'AssignField' => 'PrivatePhone',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Private Phone',
                        'AssignField' => 'PrivatePhone',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Mobile Phone'),
                        'AssignField' => 'MobilePhone',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Mobile Phone',
                        'AssignField' => 'MobilePhone',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Phone'),
                        'AssignField' => 'Phone',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Phone',
                        'AssignField' => 'Phone',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Fax'),
                        'AssignField' => 'Fax',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Fax',
                        'AssignField' => 'Fax',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Website'),
                        'AssignField' => 'Website',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'Website',
                        'AssignField' => 'Website',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => t('Birthday'),
                        'AssignField' => 'Birthday',
                        'Type' => 'field',
                        'Format' => variable_get(Dates::FORMAT_DMY, 'd.m.Y')
                    ],
                    [
                        'Label' => 'Birthday',
                        'AssignField' => 'Birthday',
                        'Type' => 'field',
                        'Format' => variable_get(Dates::FORMAT_DMY, 'd.m.Y')
                    ],
                    [
                        'Label' => t('LeadValue'),
                        'AssignField' => 'LeadValue',
                        'Type' => 'field'
                    ],
                    [
                        'Label' => 'LeadValue',
                        'AssignField' => 'LeadValue',
                        'Type' => 'field'
                    ],
                ]
            ],
        ];
    }

    public static function GetDateFormats()
    {
        //TODO more formats, with month names (in german)

        //j. F Y H:i
        //m/d/y H:ia T

        $formats = [
            'Y-m-d H:i:s' => t('ImportDateFormat::Y-m-d H:i:s (!example)'),
            'Y-m-d H:i' => t('ImportDateFormat::Y-m-d H:i (!example)'),

            'd.m.Y H:i:s' => t('ImportDateFormat::d.m.Y H:i:s (!example)'),
            'd.m.Y H:i' => t('ImportDateFormat::d.m.Y H:i (!example)'),


            'Y-m-d' => t('ImportDateFormat::Y-m-d (!example)'),

            'd.m.Y' => t('ImportDateFormat::d.m.Y (!example)'),

            'H:i:s' => t('ImportDateFormat::H:i:s (!example)'),
            'H:i' => t('ImportDateFormat::H:i (!example)'),

            'm/d/Y' => t('ImportDateFormat::m/d/Y (!example)'),

            'Y/m/d' => t('ImportDateFormat::Y/m/d (!example)'),
        ];

        foreach ($formats as $format => &$label) {
            //Note: we replace !example manually to make it easy for the translation parser
            $label = str_replace('!example', date($format), $label);
        }

        return $formats;
    }

    public static function GetDateFormatFromDateString($DateString, $SuggestedFormat = '')
    {
        if (!empty($SuggestedFormat)) {
            //If format does not contain the character ! then portions of the generated time which are not specified in format will be set to the current system time.
            //@see http://www.php.net/manual/de/datetime.createfromformat.php
            $DateFormat = "!$SuggestedFormat";
            $DateObject = date_create_from_format($DateFormat, $DateString);

            if ($DateObject !== false) {
                //the $DateString matches the suggested format
                return $SuggestedFormat;
            }
        }

        $checkFormats = array_keys(static::GetDateFormats());

        foreach ($checkFormats as $format) {
            //If format does not contain the character ! then portions of the generated time which are not specified in format will be set to the current system time.
            //@see http://www.php.net/manual/de/datetime.createfromformat.php
            $DateFormat = "!$format";
            $DateObject = date_create_from_format($DateFormat, $DateString);

            if ($DateObject !== false) {
                return $format;
            }
        }

        return '';
    }

    // --- external data, needed to be mocked for unit tests ---

    public static function GetAccountManualTags($UserID): array
    {
        $AllTags = Tag::GetTagsCache($UserID);

        $ArrayTags = array();
        foreach ($AllTags as $tagid => $tag) {
            if ($tag['Category'] == Tag::CATEGORY_MANUAL) {
                $ArrayTags[$tagid] = $tag;
            }
        }

        return $ArrayTags;
    }

    public static function GetAccountLists($UserID): array
    {
        return Lists::RetrieveLists($UserID);
    }

    public static function GetAccountCustomFields($UserID): array
    {
        return CustomFields::RetrieveCustomFields($UserID, true, array('FieldName' => 'ASC'));
    }

    public function SendNotificationEmail($Status)
    {
        $DBArray = $this->GetData();

        $UserID = $DBArray['RelOwnerUserID'];
        $account = user_load($UserID);
        $to = $account->mail;
        $from = variable_get('site_mail', '<EMAIL>');
        $lang = user_preferred_language($account);

        if ($Status == static::STATUS_VALIDATED) {
            $params = [
                'subject' => t('Import::Notification::Subject::Validated'),
                'body' => t('Import::Notification::Body::Validated !name !link', [
                    '!name' => $DBArray['Name'],
                    '!link' => url("application/$UserID/import/{$DBArray['RelOwnerUserID']}/{$DBArray['SeqNo']}", [
                        'absolute' => true
                    ])
                ])
            ];
        } elseif ($Status == static::STATUS_DONE) {
            $params = [
                'subject' => t('Import::Notification::Subject::Finished'),
                'body' => t('Import::Notification::Body::Finished !name !link', [
                    '!name' => $DBArray['Name'],
                    '!link' => url("application/$UserID/import/{$DBArray['RelOwnerUserID']}/{$DBArray['SeqNo']}", [
                        'absolute' => true
                    ])
                ])
            ];
        } else {
            //wrong status to send an email
            return;
        }

        drupal_mail('klicktipp', 'import', $to, $lang, $params, $from);
    }
}

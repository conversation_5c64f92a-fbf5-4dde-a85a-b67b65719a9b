<?php

namespace App\Klicktipp\Sms\SmsNotification\Controller;

use App\Klicktipp\AngularApi\ValueObject\ApiResponseEntitySuccessValueObject;
use App\Klicktipp\AngularApi\ValueObject\Response\ApiResponseErrorValueObject;
use App\Klicktipp\AngularApi\ValueObject\Response\ApiResponseValueObject;
use App\Klicktipp\Campaign\Exception\CampaignNotFoundException;
use App\Klicktipp\Personalization\PersonalizationDataResolver;
use App\Klicktipp\Sms\Exception\SmsNotFoundException;
use App\Klicktipp\Sms\SmsAccessGuard;
use App\Klicktipp\Sms\SmsContentResolver;
use App\Klicktipp\Sms\SmsEditor\SmsEditorWizardLinksResolver;
use App\Klicktipp\Sms\SmsNotification\SmsNotificationLinkResolver;
use App\Klicktipp\Sms\SmsNotification\SmsNotificationLocalPathsResolver;
use App\Klicktipp\Sms\SmsNotification\SmsNotificationManager;
use App\Klicktipp\Sms\SmsNotification\ValueObject\Settings\Request\SmsNotificationSettingsRequestCreateValueObject;
use App\Klicktipp\Sms\SmsNotification\ValueObject\Settings\Request\SmsNotificationSettingsRequestUpdateValueObject;
use App\Klicktipp\Sms\SmsNotification\ValueObject\Settings\Response\SmsNotificationResponseCopyFromSmsValueObject;
use App\Klicktipp\Sms\SmsNotification\ValueObject\Settings\Response\SmsNotificationSettingsResponseMessagesValueObject;
use App\Klicktipp\Sms\SmsNotification\ValueObject\Settings\Response\SmsNotificationSettingsResponseValueObject;
use App\Klicktipp\Sms\SmsNotification\ValueObject\Settings\SmsNotificationSettingsEntityValueObject;
use App\Klicktipp\Sms\SmsProviderResolver;
use App\Klicktipp\Sms\Validators\Exception\SmsValidationExceptionCollectionException;
use App\Klicktipp\Sms\Validators\Exception\SmsValidationExceptionInterface;
use App\Klicktipp\Sms\Validators\ValueObject\Response\SmsResponseValidationErrorValueObject;
use App\Klicktipp\Sms\ValueObject\Settings\Request\Factory\SmsSettingsRequestEntityFactory;
use App\Klicktipp\Sms\ValueObject\Settings\SmsSettingsDisplayOptionsValueObject;
use App\Klicktipp\Sms\ValueObject\Settings\SmsSettingsFilterOptionsValueObject;
use App\Klicktipp\SplitTest\Exception\SplitTestNotFoundException;
use stdClass;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class SmsNotificationSettingsController extends AbstractController
{
    public function retrieve(
        SmsNotificationManager $smsManager,
        SmsAccessGuard $accessGuard,
        PersonalizationDataResolver $personalizationDataResolver,
        SmsNotificationLinkResolver $linkResolver,
        SmsNotificationLocalPathsResolver $localPathsResolver,
        SmsProviderResolver $providerResolver,
        SmsEditorWizardLinksResolver $editorWizardLinksResolver,
        SmsContentResolver $contentResolver,
        stdClass $account,
        int $id
    ): JsonResponse {

        $isCreate = $id === 0;

        try {
            if ($isCreate) {
                $sms = $smsManager->instantiateNewSms($account->uid);
            } else {
                $sms = $smsManager->getSms($account->uid, $id);
            }
        } catch (SmsNotFoundException $e) {
            return $this->json(ApiResponseErrorValueObject::createFromException($e), $e->getHttpCode());
        }

        $usedSenderName = $providerResolver->getUsedSenderName(
            $sms->GetData('FromName') ?? '',
            $providerResolver->getAvailableSenders($account),
            $sms->GetData('SMSProvider') ?? ''
        );

        $providerOption = $providerResolver->getProviderOptions($account);
        $defaultProvider = empty($providerOption) ? '' : $providerOption[0]->value;

        $entity = SmsNotificationSettingsEntityValueObject::createFromEmailsNotificationSms(
            $sms,
            $linkResolver->getLinks($account->uid, $id),
            $usedSenderName,
            $providerResolver->getDefaultSenderName($account),
            $defaultProvider,
            $editorWizardLinksResolver->getUsageType($sms),
            $sms->GetMetaLabels(false)
        );

        $validation = $contentResolver->getReadyForAnalysisValidation($sms);

        $displayOptions = new SmsSettingsDisplayOptionsValueObject();
        $displayOptions->accessSmsMarketing = $accessGuard->canAccessSmsMarketing($account);
        $displayOptions->hasProvider = $accessGuard->hasProvider($account);
        $displayOptions->accessSpamScore = empty($validation);

        $filterOptions = SmsSettingsFilterOptionsValueObject::create(
            $providerOption,
            $providerResolver->getAvailableSenders($account),
            $personalizationDataResolver->getEmailPlaceholderByEmail($account, $sms),
            $sms->getCampaignProcessFlowUsages()
        );

        $messages = SmsNotificationSettingsResponseMessagesValueObject::create(
            ''
        );

        $localPaths = $localPathsResolver->getLocalPaths(
            $account,
            $sms,
            $linkResolver
        );
        try {
            $wizardLinks = $editorWizardLinksResolver->getWizardLinks($sms);
        } catch (CampaignNotFoundException | SplitTestNotFoundException $e) {
            return $this->json(ApiResponseErrorValueObject::createFromException($e), $e->getHttpCode());
        }

        return new JsonResponse(
            ApiResponseValueObject::create(
                SmsNotificationSettingsResponseValueObject::create(
                    $entity,
                    $displayOptions,
                    $filterOptions,
                    $messages,
                    $localPaths,
                    $localPathsResolver->getDefaultLocalPath(),
                    $wizardLinks,
                    $validation
                )
            )
        );
    }

    /**
     * @param SmsNotificationManager $smsManager
     * @param stdClass $account
     * @param array{
     *     entity: array<string, string|int|array<string>>
     * } $data
     * @return JsonResponse
     */
    public function create(
        SmsNotificationManager $smsManager,
        stdClass $account,
        array $data
    ): JsonResponse {

        try {
            $request = SmsSettingsRequestEntityFactory::create(
                $data['entity']
            );

            $createData = SmsNotificationSettingsRequestCreateValueObject::createFromRequestEntity($request);

            $email = $smsManager->createSms($account->uid, $createData);

            return new JsonResponse(
                ApiResponseValueObject::create(
                    ApiResponseEntitySuccessValueObject::create($email->GetData('EmailID'))
                )
            );
        } catch (SmsValidationExceptionCollectionException $e) {
            return $this->json(
                array_map(
                    static fn(SmsValidationExceptionInterface $e) => SmsResponseValidationErrorValueObject::createFromValidationException($e),
                    $e->getExceptions(),
                ),
                Response::HTTP_NOT_ACCEPTABLE,
            );
        }
    }

    /**
     * @param SmsNotificationManager $smsManager
     * @param stdClass $account
     * @param int $id
     * @param array{
     *     entity: array<string, string|int|array<string>>
     * } $data
     * @return JsonResponse
     */
    public function save(
        SmsNotificationManager $smsManager,
        stdClass $account,
        int $id,
        array $data
    ): JsonResponse {
        try {
            $request = SmsSettingsRequestEntityFactory::create(
                $data['entity'],
            );

            $updateData = SmsNotificationSettingsRequestUpdateValueObject::createFromRequestEntity($request);

            $sms = $smsManager->updateSms($account->uid, $id, $updateData);

            return new JsonResponse(
                ApiResponseValueObject::create(
                    ApiResponseEntitySuccessValueObject::create($sms->GetData('EmailID'))
                )
            );
        } catch (SmsNotFoundException $e) {
            return $this->json(ApiResponseErrorValueObject::createFromException($e), $e->getHttpCode());
        } catch (SmsValidationExceptionCollectionException $e) {
            return $this->json(
                array_map(
                    static fn(SmsValidationExceptionInterface $e) => SmsResponseValidationErrorValueObject::createFromValidationException($e),
                    $e->getExceptions(),
                ),
                Response::HTTP_NOT_ACCEPTABLE,
            );
        }
    }

    public function copySmsRetrieve(
        SmsNotificationManager $smsNotificationManager,
        SmsProviderResolver $providerResolver,
        stdClass $account,
        int $id
    ): JsonResponse {
        try {
            $sms = $smsNotificationManager->getSms($account->uid, $id);
        } catch (SmsNotFoundException $e) {
            return $this->json(ApiResponseErrorValueObject::createFromException($e), $e->getHttpCode());
        }

        $usedSenderName = $providerResolver->getUsedSenderName(
            $sms->GetData('FromName') ?? '',
            $providerResolver->getAvailableSenders($account),
            $sms->GetData('SMSProvider') ?? ''
        );

        $entity = SmsNotificationSettingsEntityValueObject::createFromEmailsNotificationSms(
            $sms,
            null,
            $usedSenderName,
            '',
            '',
            '',
            $sms->GetMetaLabels(false)
        );

        return new JsonResponse(
            ApiResponseValueObject::create(
                SmsNotificationResponseCopyFromSmsValueObject::create($entity)
            )
        );
    }
}

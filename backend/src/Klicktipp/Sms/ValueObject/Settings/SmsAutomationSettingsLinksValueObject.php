<?php

namespace App\Klicktipp\Sms\ValueObject\Settings;

use App\Klicktipp\Sms\ValueObject\SmsAutomationEntitySearchLinksValueObject;

class SmsAutomationSettingsLinksValueObject
{
    public string $searchSent;
    public string $searchClicked;
    public string $searchNotClicked;

    public static function create(
        SmsAutomationEntitySearchLinksValueObject $searchLinks
    ): self {
        $valueObject = new self();

        $valueObject->searchSent = $searchLinks->sent;
        $valueObject->searchClicked = $searchLinks->clicked;
        $valueObject->searchNotClicked = $searchLinks->notClicked;

        return $valueObject;
    }
}

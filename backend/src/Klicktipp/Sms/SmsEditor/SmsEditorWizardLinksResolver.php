<?php

namespace App\Klicktipp\Sms\SmsEditor;

use App\Klicktipp\Automation\AutomationLinkResolver;
use App\Klicktipp\Campaign\CampaignLinkResolver;
use App\Klicktipp\Campaign\CampaignManager;
use App\Klicktipp\Campaign\Exception\CampaignNotFoundException;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\Emails;
use App\Klicktipp\EmailsNewsletterSMS;
use App\Klicktipp\EmailsSMS;
use App\Klicktipp\Sms\SmsEditor\ValueObject\SmsEditorWizardLinksAutomationValueObject;
use App\Klicktipp\Sms\SmsEditor\ValueObject\SmsEditorWizardLinksCampaignValueObject;
use App\Klicktipp\Sms\SmsEditor\ValueObject\SmsEditorWizardLinksEmailValueObject;
use App\Klicktipp\Sms\SmsEditor\ValueObject\SmsEditorWizardLinksValueObject;
use App\Klicktipp\Sms\SmsLinkResolver;
use App\Klicktipp\SplitTest\Exception\SplitTestNotFoundException;
use App\Klicktipp\SplitTest\SplitTestManager;

class SmsEditorWizardLinksResolver
{
    private CampaignManager $campaignManager;
    private SplitTestManager $splitTestManager;
    private CampaignLinkResolver $campaignLinkResolver;
    private AutomationLinkResolver $automationLinkResolver;
    private SmsLinkResolver $smsLinkResolver;

    public function __construct(
        CampaignManager $campaignManager,
        SplitTestManager $splitTestManager,
        CampaignLinkResolver $campaignLinkResolver,
        AutomationLinkResolver $automationLinkResolver,
        SmsLinkResolver $smsLinkResolver
    ) {
        $this->campaignManager = $campaignManager;
        $this->splitTestManager = $splitTestManager;
        $this->campaignLinkResolver = $campaignLinkResolver;
        $this->automationLinkResolver = $automationLinkResolver;
        $this->smsLinkResolver = $smsLinkResolver;
    }

    /**
     * @throws CampaignNotFoundException
     * @throws SplitTestNotFoundException
     */
    public function getWizardLinks(EmailsSMS $sms): SmsEditorWizardLinksValueObject
    {
        $userId = $sms->GetData('RelUserID');
        $smsId = $sms->GetData('EmailID');

        if ($sms instanceof EmailsNewsletterSMS) {
            $campaign = $this->campaignManager->getCampaignByEmailId($userId, $smsId);
            $splitTestId = $campaign->GetData('SplittestID');
            $splitTestLinks = null;
            if (!empty($splitTestId)) {
                $splitTest = $this->splitTestManager->getSplitTest($userId, $splitTestId);
                $splitTestLinks = $this->campaignLinkResolver->getSplitTestSmsLinks($campaign, $splitTest);
            }

            $wizardLinks = SmsEditorWizardLinksValueObject::createForCampaign(
                SmsEditorWizardLinksCampaignValueObject::createForCampaign(
                    $this->campaignLinkResolver->getLinks($campaign),
                    $this->campaignLinkResolver->getOverviewLink($campaign),
                    $this->campaignLinkResolver->getCreateLinkLink($campaign)
                ),
                $splitTestLinks
            );
        } else {
            $wizardLinks = SmsEditorWizardLinksValueObject::createForAutomation(
                SmsEditorWizardLinksAutomationValueObject::createForAutomation(
                    $this->automationLinkResolver->linkOverview($userId),
                    $this->automationLinkResolver->linkCreate($userId)
                ),
                SmsEditorWizardLinksEmailValueObject::createForEmail(
                    $this->smsLinkResolver->linkOverview($userId)
                )
            );
        }

        return $wizardLinks;
    }

    public function getUsageType(EmailsSMS $sms, CampaignsNewsletter $campaign = null): string
    {
        switch ($sms->GetData('EmailType')) {
            case Emails::TYPE_NEWSLETTERSMS:
                if ($campaign) {
                    $isSplitTest = !empty($campaign->GetData('SplittestID'));
                    if ($campaign->isNewsletterCampaign()) {
                        $usageType = ($isSplitTest) ? 'newsletter-split-test' : 'newsletter';
                    } else {
                        $usageType = ($isSplitTest) ? 'autoresponder-split-test' : 'autoresponder';
                    }
                } else {
                    $usageType = 'not-specified';
                }
                break;
            case Emails::TYPE_AUTOMATIONSMS:
                $usageType = 'automation';
                break;
            case Emails::TYPE_NOTIFICATIONSMS:
                $usageType = 'notification';
                break;
            default:
                $usageType = 'unknown';
        }

        return $usageType;
    }
}

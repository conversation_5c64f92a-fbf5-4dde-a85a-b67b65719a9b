<?php

namespace App\Klicktipp\OptInProcess;

use App\Klicktipp\AngularApi\AngularApi;
use App\Klicktipp\AngularApi\Controller\Exception\ApiControllerException;
use App\Klicktipp\AngularApi\Validators\Exception\ApiValidationExceptionCollectionException;
use App\Klicktipp\AngularApi\ValueObject\ApiResponseDependenciesValueObject;
use App\Klicktipp\AngularApi\ValueObject\ApiResponseOptionIntValueObject;
use App\Klicktipp\Email\EmailConfirmation\EmailConfirmationManager;
use App\Klicktipp\Email\Exception\EmailNotFoundException;
use App\Klicktipp\Exception\DatabaseException;
use App\Klicktipp\Lists;
use App\Klicktipp\OptInProcess\Exception\OptInProcessDeleteDependencyException;
use App\Klicktipp\OptInProcess\Exception\OptInProcessNotFoundException;
use App\Klicktipp\OptInProcess\Validators\OptInProcessValidatorName;
use App\Klicktipp\OptInProcess\Validators\OptInProcessValidatorRedirectUrls;
use App\Klicktipp\OptInProcess\ValueObject\Request\Update\OptInProcessRequestUpdateValueObject;
use App\Klicktipp\OptInProcess\ValueObject\Response\Index\OptInProcessResponseIndexEntityValueObject;
use App\Klicktipp\OptInProcess\ValueObject\Response\Overview\OptInProcessResponseOverviewEntityValueObject;
use App\Klicktipp\ToolPlugin\ToolPluginManager;
use stdClass;
use Symfony\Component\HttpFoundation\Response;

class OptInProcessControllerManager
{
    private OptInProcessManager $optInProcessManager;
    private OptInProcessLinkResolver $optInProcessLinkResolver;
    private OptInProcessAccessGuard $optInProcessAccessGuard;
    private EmailConfirmationManager $emailConfirmationManager;
    private OptInProcessValidator $optInProcessValidator;
    private ToolPluginManager $toolPluginManager;

    public function __construct(
        OptInProcessManager $optInProcessManager,
        OptInProcessLinkResolver $optInProcessLinkResolver,
        OptInProcessAccessGuard $optInProcessAccessGuard,
        EmailConfirmationManager $emailConfirmationManager,
        OptInProcessValidator $optInProcessValidator,
        ToolPluginManager $toolPluginManager
    ) {
        $this->optInProcessManager = $optInProcessManager;
        $this->optInProcessLinkResolver = $optInProcessLinkResolver;
        $this->optInProcessAccessGuard = $optInProcessAccessGuard;
        $this->emailConfirmationManager = $emailConfirmationManager;
        $this->optInProcessValidator = $optInProcessValidator;
        $this->toolPluginManager = $toolPluginManager;
    }

    /**
     * @return OptInProcessResponseOverviewEntityValueObject[]
     * @throws DatabaseException
     */
    public function overview(int $userId): array
    {
        $entities = [];
        foreach ($this->optInProcessManager->getIndex($userId) as $entity) {
            $entities[] = OptInProcessResponseOverviewEntityValueObject::create(
                $entity->id,
                $entity->name,
                $entity->type,
                empty($entity->createdOn) ? '' : AngularApi::formatterDate($entity->createdOn),
                $entity->isSingleOptIn,
                $entity->isChangeEmail,
                $entity->isResendConfirmation,
                $entity->isDefault,
                $this->optInProcessLinkResolver->getLinks(
                    $entity->userId,
                    $entity->id,
                    $entity->emailId
                )
            );
        }

        return $entities;
    }

    /**
     * @return OptInProcessResponseIndexEntityValueObject[]
     * @throws DatabaseException
     */
    public function index(int $userId): array
    {
        $entities = [];
        foreach ($this->optInProcessManager->getIndex($userId) as $indexItem) {
            $entities[] = OptInProcessResponseIndexEntityValueObject::createFromIndexValueObject(
                $indexItem,
                $this->optInProcessLinkResolver->linkEdit(
                    $indexItem->userId ?? 0,
                    $indexItem->id ?? 0
                )
            );
        }

        return $entities;
    }

    /**
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function retrieve(int $userId, int $id, int $copyId = 0): Lists
    {
        $isCreate = $id === 0;

        try {
            if ($isCreate) {
                if (!empty($copyId)) {
                    try {
                        $optinProcess = $this->optInProcessManager->getOptInProcess($userId, $copyId);
                        $optinProcess
                            ->set('ListID', 0)
                            ->set('Name', '')
                            ->set('RelOptInConfirmationEmailID', 0)
                            ->set('UseAsChangeEmailList', 0);
                        return $optinProcess;
                    } catch (OptInProcessNotFoundException $e) {
                        throw new ApiControllerException(
                            $e->getMessage(),
                            'optin-process-copy-not-found',
                            t('OptInProcess::Retrieve::Error::The source OptInProcess could not be found.'),
                            Response::HTTP_NOT_FOUND
                        );
                    }
                }

                return $this->optInProcessManager->instantiateNewOptInProcess($userId);
            }
            return $this->optInProcessManager->getOptInProcess($userId, $id);
        } catch (OptInProcessNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'optin-process-not-found',
                t('OptInProcess::Retrieve::Error::The OptInProcess could not be found.'),
                Response::HTTP_NOT_FOUND
            );
        }
    }

    /**
     * @throws ApiValidationExceptionCollectionException
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function create(
        stdClass $account,
        OptInProcessRequestUpdateValueObject $data
    ): Lists {

        $optInProcess = $this->optInProcessManager->instantiateNewOptInProcess($account->uid);
        $optInProcess = $this->updateFromSettings($account, $optInProcess, $data, false);

        if ($optInProcess->isChangeEmailList()) {
            try {
                $changeEmailProcess = $this->optInProcessManager->getChangeEmailProcess($account->uid);
                $this->optInProcessManager->updatePreviousChangeEmailProcess($changeEmailProcess);
            } catch (OptInProcessNotFoundException $e) {
                throw new ApiControllerException(
                    $e->getMessage(),
                    'optin-process-change-email-not-found',
                    t('OptInProcess::Create::Error::The previous change email list could not be found.'),
                    Response::HTTP_NOT_ACCEPTABLE
                );
            }
        }

        $this->optInProcessValidator->validate($optInProcess, [
            OptInProcessValidatorName::class
        ]);

        $this->optInProcessManager->update($optInProcess);

        return $optInProcess;
    }

    /**
     * @throws ApiValidationExceptionCollectionException
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function copy(
        stdClass $account,
        int $copyId,
        OptInProcessRequestUpdateValueObject $data
    ): Lists {

        try {
            $optInProcess = $this->optInProcessManager->getOptInProcess($account->uid, $copyId);

            $data->useForChangeEmail = false;

            $optInProcess = $this->updateFromSettings($account, $optInProcess, $data, false);

            $this->optInProcessValidator->validate($optInProcess, [
                OptInProcessValidatorName::class,
                OptInProcessValidatorRedirectUrls::class
            ]);

            // the optin process is valid for create, copy the confirmation email

            $copyEmail = $this->emailConfirmationManager->getEmail(
                $account->uid,
                $optInProcess->GetData('RelOptInConfirmationEmailID')
            );
            $copyEmail
                ->set('EmailID', 0)
                ->set('EmailName', t('Confirmation Email: @name', [
                    '@name' => $optInProcess->GetData('Name')
                ]));
            $copyEmail->save();

            // create a new optin process
            $optInProcess
                ->set('ListID', 0)
                ->set('RelOptInConfirmationEmailID', $copyEmail->GetData('EmailID'));
            $this->optInProcessManager->update($optInProcess);

            return $optInProcess;
        } catch (OptInProcessNotFoundException | EmailNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'optin-process-copy-failed',
                t('OptInProcess::Update::Error::The OptInProcess could not be copied.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }
    }

    /**
     * @throws ApiValidationExceptionCollectionException
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function update(
        stdClass $account,
        int $id,
        OptInProcessRequestUpdateValueObject $data
    ): Lists {
        try {
            $optInProcess = $this->optInProcessManager->getOptInProcess($account->uid, $id);
            $defaultProcessId = $this->optInProcessManager->getDefaultProcessId($account->uid);
            $nameBeforeUpdate = $optInProcess->GetData('Name');
            $isCurrentChangeEmailList = $optInProcess->isChangeEmailList();

            $optInProcess = $this->updateFromSettings(
                $account,
                $optInProcess,
                $data,
                $defaultProcessId === (int)$optInProcess->GetData('ListID')
            );

            $this->optInProcessValidator->validate($optInProcess, [
                OptInProcessValidatorName::class,
                OptInProcessValidatorRedirectUrls::class
            ]);

            $changeEmailProcess = null;
            if (!$isCurrentChangeEmailList && $optInProcess->isChangeEmailList()) {
                try {
                    $changeEmailProcess = $this->optInProcessManager->getChangeEmailProcess($account->uid);
                } catch (OptInProcessNotFoundException $e) {
                    throw new ApiControllerException(
                        $e->getMessage(),
                        'optin-process-change-email-not-found',
                        t('OptInProcess::Create::Error::The previous change email list could not be found.'),
                        Response::HTTP_NOT_ACCEPTABLE
                    );
                }
            }

            $this->optInProcessManager->update($optInProcess);

            if ($changeEmailProcess) {
                $this->optInProcessManager->updatePreviousChangeEmailProcess($changeEmailProcess);
            }

            if (!empty($nameBeforeUpdate) && $nameBeforeUpdate !== $optInProcess->GetData('Name')) {
                $email = $this->emailConfirmationManager->getEmail(
                    $account->uid,
                    $optInProcess->GetData('RelOptInConfirmationEmailID')
                );
                $email->set('Name', t('Confirmation Email: @name', [
                    '@name' => $optInProcess->GetData('Name')
                ]));
            }

            return $optInProcess;
        } catch (OptInProcessNotFoundException | EmailNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'optin-process-update-failed',
                t('OptInProcess::Update::Error::The OptInProcess could not be updated.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }
    }

    /**
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function dependencies(int $userId, int $id): ApiResponseDependenciesValueObject
    {
        try {
            //just to check if the tag still exists
            $optInProcess = $this->optInProcessManager->getOptInProcess($userId, $id);

            $result = [];
            foreach ($this->optInProcessManager->checkDeleteDependencies($optInProcess) as $key => $v) {
                // Note: the key contains the untranslated category, translate here (legacy code)
                $result[t(/*ignore*/ $key)] = $v;
            }
            return ApiResponseDependenciesValueObject::createFromCheckDependencies($result);
        } catch (OptInProcessNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'optin-process-dependencies-failed',
                t('OptInProcess::Dependencies::Error::The optin process does not exist anymore.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }
    }

    /**
     * @throws DatabaseException
     * @throws OptInProcessNotFoundException
     */
    public function getChangeEmailProcess(int $userId): Lists
    {
        return $this->optInProcessManager->getChangeEmailProcess($userId);
    }

    /**
     * @return ApiResponseOptionIntValueObject[]
     * @throws DatabaseException
     */
    public function getReferralLinks(int $userId): array
    {
        $referralLinks = [];
        foreach ($this->toolPluginManager->getTools($userId, 'referral') as $referralLink) {
            $referralLinks[] = ApiResponseOptionIntValueObject::create(
                $referralLink->getData('ToolID'),
                $referralLink->getData('Name')
            );
        }
        return $referralLinks;
    }

    /**
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function delete(int $userId, int $id): string
    {
        try {
            $optInProcess = $this->optInProcessManager->getOptInProcess($userId, $id);
            $defaultProcessId = $this->optInProcessManager->getDefaultProcessId($userId);

            if ($defaultProcessId === (int)$optInProcess->GetData('ListID')) {
                throw new ApiControllerException(
                    'OptInProcess::Delete::Error::Default OptInProcess cannot be deleted',
                    'optin-process-delete-default',
                    t('OptInProcess::Delete::Error::The default optin process cannot be deleted.'),
                    Response::HTTP_NOT_ACCEPTABLE
                );
            }

            $this->optInProcessManager->delete($optInProcess);
            return $optInProcess->GetData('Name');
        } catch (OptInProcessNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'optin-process-delete-failed',
                t('OptInProcess::Delete::Error::The optin process does not exist anymore.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        } catch (OptInProcessDeleteDependencyException $e) {
            throw new ApiControllerException(
                'OptInProcess::Delete::Error::OptInProcess has dependencies',
                'optin-process-delete-dependencies',
                t('OptInProcess::Delete::Error::The optin process is still in use.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }
    }

    private function updateFromSettings(
        stdClass $account,
        Lists $optInProcess,
        OptInProcessRequestUpdateValueObject $data,
        bool $isDefaultProcess
    ): Lists {
        $pending = null;
        if (isset($data->pendingUrl)) {
            if (empty($data->pendingUrl)) {
                $pending = [
                    'UseSubscriberParameter' => 0,
                    'UseListParameter' => 0,
                    'UseEmailParameter' => 0,
                    'UseSubscriberKeyParameter' => 0,
                    'SubscriberParameterName' => '',
                    'ListParameterName' => '',
                    'EmailParameterName' => '',
                    'SubscriberKeyParameterName' => ''
                ];
            } elseif (isset($data->pending)) {
                $pending = [
                    'UseSubscriberParameter' => empty($data->pending->subscriberParameterName) ? 0 : 1,
                    'UseListParameter' => empty($data->pending->listParameterName) ? 0 : 1,
                    'UseEmailParameter' => empty($data->pending->emailParameterName) ? 0 : 1,
                    'UseSubscriberKeyParameter' => empty($data->pending->subscriberKeyParameterName) ? 0 : 1,
                    'SubscriberParameterName' => $data->pending->subscriberParameterName ?: '',
                    'ListParameterName' => $data->pending->listParameterName ?: '',
                    'EmailParameterName' => $data->pending->emailParameterName ?: '',
                    'SubscriberKeyParameterName' => $data->pending->subscriberKeyParameterName ?: ''
                ];
            }
        }

        $confirmed = null;
        if (isset($data->confirmedUrl)) {
            if (empty($data->confirmedUrl)) {
                $confirmed = [
                    'UseSubscriberParameter' => 0,
                    'UseListParameter' => 0,
                    'UseEmailParameter' => 0,
                    'UseSubscriberKeyParameter' => 0,
                    'UseReferralLinkParameter' => 0,
                    'SubscriberParameterName' => '',
                    'ListParameterName' => '',
                    'EmailParameterName' => '',
                    'SubscriberKeyParameterName' => '',
                    'ReferralLinkName' => '',
                    'ReferralLinkToolID' => 0
                ];
            } elseif (isset($data->confirmed)) {
                $confirmed = [
                    'UseSubscriberParameter' => empty($data->confirmed->subscriberParameterName) ? 0 : 1,
                    'UseListParameter' => empty($data->confirmed->listParameterName) ? 0 : 1,
                    'UseEmailParameter' => empty($data->confirmed->emailParameterName) ? 0 : 1,
                    'UseSubscriberKeyParameter' => empty($data->confirmed->subscriberKeyParameterName) ? 0 : 1,
                    'UseReferralLinkParameter' => empty($data->confirmed->referralLinkId) ? 0 : 1,
                    'SubscriberParameterName' => $data->confirmed->subscriberParameterName ?: '',
                    'ListParameterName' => $data->confirmed->listParameterName ?: '',
                    'EmailParameterName' => $data->confirmed->emailParameterName ?: '',
                    'SubscriberKeyParameterName' => $data->confirmed->subscriberKeyParameterName ?: '',
                    'ReferralLinkName' => $data->confirmed->referralLinkParameterName ?: '',
                    'ReferralLinkToolID' => $data->confirmed->referralLinkId ?: 0
                ];
            }
        }

        if (!$isDefaultProcess) {
            $optInProcess->set('Name', $data->name);
        }

        $optInProcess
            ->set('Notes', $data->notes)
            ->set('MetaLabels', $data->metaLabels)
            ->set('SubscriptionConfirmationPendingPageURL', $data->pendingUrl)
            ->set('SubscriptionConfirmedPageURL', $data->confirmedUrl)
            ->set('Pending', $pending)
            ->set('Confirmed', $confirmed)
            ->set('UseAsChangeEmailList', empty($data->useForChangeEmail) ? 0 : 1)
            ->set('PendingSubscribersDelay', $data->pendingSubscribersDelay);

        if ($this->optInProcessAccessGuard->canEditSingleOptIn($account)) {
            $optInProcess->set('OptInModeEnum', empty($data->optInMode) ? 0 : 1);
        }

        if (!empty($optInProcess->GetData('OptInModeEnum'))) {
            $optInProcess->set('ReSendConfirmationEmail', 0);
        } elseif ($this->optInProcessAccessGuard->canEditResendConfirmation($account)) {
            $optInProcess->set('ReSendConfirmationEmail', empty($data->resendConfirmationEmail) ? 0 : 1);
        }

        return $optInProcess;
    }
}

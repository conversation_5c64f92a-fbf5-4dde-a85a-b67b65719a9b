<?php

namespace App\Klicktipp\Validators\SpamScore\Exception;

use App\Klicktipp\Validators\SpamScore\ValueObject\SpamScoreMalformedLinkValueObject;

class SpamScoreValidationMalformedLinksException extends SpamScoreValidationException
{
    /** @var SpamScoreMalformedLinkValueObject[] */
    private array $links;

    /**
     * @param SpamScoreMalformedLinkValueObject[] $links
     */
    public function __construct(array $links)
    {
        $this->links = $links;
        parent::__construct("Validator::SpamScore::Malformed links found.");
    }

    /**
     * @return SpamScoreMalformedLinkValueObject[]
     */
    public function getLinks(): array
    {
        return $this->links;
    }
}

<?php

namespace App\Klicktipp;

use App\Klicktipp\PreGeneration\PreGenerationHandler;
use App\Klicktipp\Redis\CampaignRestartRedisRateLimitService;
use App\Klicktipp\Redis\EmailRedisRateLimitService;
use DateTime;
use DateTimeZone;
use Doctrine\DBAL\Exception;
use PDOException;
use stdClass;
use Throwable;

/**
 * @phpstan-import-type CampaignArray from Campaigns
 * @phpstan-import-type QueueItemArray from BufferTableQueue
 * @phpstan-type TransactionalQueueEntry array{
 *     StateID: int,
 *     RelOwnerUserID: int,
 *     RelSubscriberID: int,
 *     RelAutoResponderID: int,
 *     StatusEnum: TransactionEmails::STATUS_*,
 *     TimeToSend: int,
 *     ReferenceID: int,
 *     GoalReached: int
 * }
 * @phpstan-type State array{
 *     id: int,
 *     next: int,
 *     nextNo: int,
 *     type: CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_*
 * }
 */
class TransactionEmails
{
    public const STATUS_PENDING = 0;
    public const STATUS_SENDING = 1;
    public const STATUS_SENT = 2;
    public const STATUS_FAILED = 3;
    public const STATUS_PAUSED = 4;
    public const STATUS_PAUSED_AFTER = 5;
    public const STATUS_PAUSED_AFTER_NO = 6;
    public const STATUS_PAUSED_QUEUE_JOB = 7;
    public const STATUS_STOPPED = 8;
    public const STATUS_PENDING_AFTER = 9;
    public const STATUS_STOPPED_AFTER = 10;
    public const STATUS_PAUSED_RESTART = 11;
    public const STATUS_PREGENERATED = 12;
    public const STATUS_DISCARDED = 13;
    public const STATUS_WAITING_FOR_TEMPORAL_CONDITION = 14;
    public const TRANSACTION_TYPE_OTHER = 0;
    public const TRANSACTION_TYPE_SUBSCRIBER_QUEUE = 1;
    private static ?EmailRedisRateLimitService $emailRedisRateLimitService;
    private static ?CampaignRestartRedisRateLimitService $campaignRestartRedisRateLimitService;

    /**
     * @return false|TransactionalQueueEntry
     */
    public static function RetrieveTransactionByID($UserID, $SubscriberID, $ReferenceID, $CampaignID)
    {
        $tableName = Campaigns::GetQueueTableNameById($UserID, $CampaignID);

        /** @var TransactionalQueueEntry|false $result */
        $result = db_query(
            "SELECT * FROM {{$tableName}} " .
            " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID AND ReferenceID = :ReferenceID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelAutoResponderID' => $CampaignID,
                ':RelSubscriberID' => $SubscriberID,
                ':ReferenceID' => $ReferenceID,
            ]
        )->fetchAssoc();
        return $result;
    }

    public static function RetrieveFailedTransactions(
        $UserID,
        $SubscriberID,
        $ReferenceIDs,
        $tableName,
        $statusArray = []
    ) {
        //retrieve failed entries
        $sql = "SELECT q.RelAutoResponderID, q.TimeToSend, q.StatusReason, q.ReferenceID, c.CampaignName FROM {{$tableName}} q " .
            " INNER JOIN {campaigns} c ON c.RelOwnerUserID = q.RelOwnerUserID AND c.CampaignID = q.RelAutoResponderID " .
            " WHERE q.RelOwnerUserID = :RelOwnerUserID AND q.RelSubscriberID = :RelSubscriberID AND q.StatusEnum = :StatusEnum AND q.StatusReason IN (:StatusReason)";

        if (empty($statusArray)) {
            $statusArray = [
                TRANSACTION_REASON_SUBSCRIBER_MATCHED_NEGATIVES,
                TRANSACTION_REASON_TIMETOSEND_IS_PAST,
                TRANSACTION_REASON_TIMETOSEND_IS_Y2K38,
                TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE
            ];
        }

        $params = array(
            ':RelOwnerUserID' => $UserID,
            ':RelSubscriberID' => $SubscriberID,
            ':StatusEnum' => TransactionEmails::STATUS_FAILED,
            ':StatusReason' => $statusArray
        );

        if (!empty($ReferenceIDs)) {
            $sql .= ' AND q.ReferenceID IN (:ReferenceIDs)';
            $params[':ReferenceIDs'] = $ReferenceIDs;
        }

        $result = db_query($sql, $params);

        $entries = [];

        while ($QueueEntry = kt_fetch_array($result)) {
            $entries[] = $QueueEntry;
        }

        return $entries;
    }

    public static function RetrievePendingTransactions(
        $UserID,
        $SubscriberID,
        $ReferenceIDs,
        $tableName,
        $statusArray = []
    ) {
        $fields = [
            'q.RelAutoResponderID',
            'q.TimeToSend',
            'q.StatusEnum',
            'q.ReferenceID',
            'c.CampaignName',
            'c.AutoResponderTriggerTypeEnum'
        ];
        // @todo refactor by using inheritance
        if ($tableName == TransactionalQueue::TABLE_NAME) {
            $fields[] = 'q.StateID';
        }
        $sql = "SELECT " . implode(', ', $fields) . " FROM {{$tableName}} q " .
            " INNER JOIN {campaigns} c ON c.RelOwnerUserID = q.RelOwnerUserID AND c.CampaignID = q.RelAutoResponderID " .
            " WHERE q.RelOwnerUserID = :RelOwnerUserID AND q.RelSubscriberID = :RelSubscriberID AND q.StatusEnum IN (:StatusEnum)";

        if (empty($statusArray)) {
            $statusArray = [
                TransactionEmails::STATUS_PENDING,
                TransactionEmails::STATUS_PENDING_AFTER,
                TransactionEmails::STATUS_PAUSED,
                TransactionEmails::STATUS_PAUSED_AFTER,
                TransactionEmails::STATUS_PAUSED_AFTER_NO,
                TransactionEmails::STATUS_PAUSED_QUEUE_JOB,
                TransactionEmails::STATUS_PAUSED_RESTART,
                TransactionEmails::STATUS_WAITING_FOR_TEMPORAL_CONDITION
            ];
        }

        $params = array(
            ':RelOwnerUserID' => $UserID,
            ':RelSubscriberID' => $SubscriberID,
            ':StatusEnum' => $statusArray
        );

        if (!empty($ReferenceIDs)) {
            $sql .= ' AND q.ReferenceID IN (:ReferenceIDs)';
            $params[':ReferenceIDs'] = $ReferenceIDs;
        }

        $entries = [];

        $result = db_query($sql, $params);

        while ($QueueEntry = kt_fetch_array($result)) {
            $entries[] = $QueueEntry;
        }

        return $entries;
    }

    /**
    * @param $ArrayCampaign
    * @return array<int, int>
    * @throws \Doctrine\DBAL\Exception
     */
    public static function RetrievePendingSubscribersOfAutomation($ArrayCampaign)
    {
        $ArrayCounts = [];

        // dont send datetime ar, if time to sent is past
        if (!Campaigns::IsProcessflow($ArrayCampaign['AutoResponderTriggerTypeEnum'])) {
            return $ArrayCounts;
        }

        // check for exiting queue entry
        //@note: Multivalue taggings - count all campaign runs
        $result = db_query(
            "SELECT StateID, COUNT(StateID) AS StateCount  FROM {" . TransactionalQueue::TABLE_NAME . "} " .
            " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND StatusEnum IN (:StatusEnum) " .
            " GROUP BY StateID",
            [
                ':RelOwnerUserID' => $ArrayCampaign['RelOwnerUserID'],
                ':RelAutoResponderID' => $ArrayCampaign['CampaignID'],
                ':StatusEnum' => [
                    TransactionEmails::STATUS_PENDING,
                    TransactionEmails::STATUS_PENDING_AFTER,
                    TransactionEmails::STATUS_PAUSED,
                    TransactionEmails::STATUS_PAUSED_RESTART,
                    TransactionEmails::STATUS_PAUSED_AFTER,
                    TransactionEmails::STATUS_PAUSED_AFTER_NO,
                    TransactionEmails::STATUS_PAUSED_QUEUE_JOB,
                    TransactionEmails::STATUS_STOPPED,
                    TransactionEmails::STATUS_STOPPED_AFTER,
                ],
            ]
        );
        while ($QueueEntry = kt_fetch_array($result)) {
            $ArrayCounts[(int)$QueueEntry['StateID']] = (int)$QueueEntry['StateCount'];
        }

        return $ArrayCounts;
    }

  /**
   * Register automations trigger to a subscriber
   *
   * @param $userID
   * @param $subscriberID
   * @param $referenceID
   * @param $instant Skip Queue
   */
    public static function RegisterAutomations($userID, $subscriberID, $referenceID, $instant = false)
    {
        if ($instant && variable_get('klicktipp_instant_web_trigger_on', false)) {
            Subscribers::startCampaigns($referenceID, $userID, $subscriberID);
            return;
        }
        SubscriberQueue::CreateQueueJob(
            $userID,
            $subscriberID,
            SubscriberQueue::JOBTYPE_TRIGGER_QUEUE,
            [],
            $referenceID
        );
    }

    /**
     * Register autoresponder trigger to a subscriber
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $TimeToSend
     * @param $ArrayCampaign
     * @param $ReferenceID
     * @param bool $UpdateIfSent
     * @param bool $SendToOnlyOneSubscription
     *  Is this the first run of a "send to one" newsletter, set it to TRUE, which ensures we create only one queue entry.
     *  If we want to "send to a second one if bounced", then set it to FALSE.
     *
     * @return bool
     */
    public static function RegisterIntoQueue(
        $UserID,
        $SubscriberID,
        $TimeToSend,
        $ArrayCampaign,
        $ReferenceID,
        $UpdateIfSent = false,
        $SendToOnlyOneSubscription = false
    ) {
        if (Campaigns::IsProcessflow($ArrayCampaign['AutoResponderTriggerTypeEnum'])) {
            // use RunStarted for automations
            return false;
        }

        // don't send datetime ar, if time to sent is past
        if (
            $ArrayCampaign['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_DATETIME ||
            $ArrayCampaign['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_DATETIME
        ) {
            if ($TimeToSend < time()) {
                TransactionEmails::CreateFailed(
                    $ArrayCampaign,
                    $SubscriberID,
                    $ReferenceID,
                    time(),
                    TRANSACTION_REASON_TIMETOSEND_IS_PAST
                );
                return false;
            }
        }

        // don't send, if time to sent is smaller than 0
        if ($TimeToSend < 0) {
            TransactionEmails::CreateFailed(
                $ArrayCampaign,
                $SubscriberID,
                $ReferenceID,
                time(),
                TRANSACTION_REASON_TIMETOSEND_IS_Y2K38
            );
            return false;
        }

        // start transaction to avoid race conditions and multiple inserts of same queue entry
        self::beginTransaction();

        $tableName = Campaigns::GetQueueTableNameByType($ArrayCampaign['AutoResponderTriggerTypeEnum']);

        // check for exiting queue entry
        if ($SendToOnlyOneSubscription) {
            // Newsletter to one subscription per contact: ensure we have only one subscription in the queue no matter what reference
            $result = db_query(
                "SELECT StatusEnum FROM {{$tableName}} " .
                " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelAutoResponderID = :RelAutoResponderID",
                [
                    ':RelOwnerUserID' => $UserID,
                    ':RelSubscriberID' => $SubscriberID,
                    ':RelAutoResponderID' => $ArrayCampaign['CampaignID'],
                ]
            );
        } else {
            $result = db_query(
                "SELECT StatusEnum, StatusReason FROM {{$tableName}} " .
                " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelAutoResponderID = :RelAutoResponderID AND ReferenceID = :ReferenceID",
                [
                    ':RelOwnerUserID' => $UserID,
                    ':RelSubscriberID' => $SubscriberID,
                    ':ReferenceID' => $ReferenceID,
                    ':RelAutoResponderID' => $ArrayCampaign['CampaignID'],
                ]
            );
        }
        $TransactionalQueueEntry = kt_fetch_array($result);
        if (!empty($TransactionalQueueEntry)) {
            $preventInsert = true;
            switch ($ArrayCampaign['AutoResponderTriggerTypeEnum']) {
                case Campaigns::TRIGGER_TYPE_DATETIME:
                case Campaigns::TRIGGER_TYPE_SMS_DATETIME:
                case Campaigns::TRIGGER_TYPE_BIRTHDAY:
                case Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY:
                    // reset ar from custom field if pending (or explicitly requested)
                    $ResetAR = $UpdateIfSent || $TransactionalQueueEntry['StatusEnum'] == TransactionEmails::STATUS_PENDING;
                    break;
                case Campaigns::TRIGGER_TYPE_SUBSCRIPTION:
                case Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION:
                    // reset ar if explicitly requested only (sent+pending only, not failed)
                    $ResetAR = $UpdateIfSent && in_array(
                        $TransactionalQueueEntry['StatusEnum'],
                        [
                                TransactionEmails::STATUS_SENT,
                                TransactionEmails::STATUS_PENDING,
                            ]
                    );
                    break;
                default: // Campaigns::TRIGGER_TYPE_CAMPAIGN (newsletter)
                    $ResetAR =
                        // user stopped pre-generation explicitly or implicitly (e.g. by e-amil template change)
                        // and started again
                        $TransactionalQueueEntry['StatusEnum'] == TransactionEmails::STATUS_DISCARDED ||
                        // user stopped pre-generation explicitly or implicitly (e.g. by e-amil template change)
                        // because of a race condition (cached campaign states in a worker) entry was pre-generated
                        // *after* campaign stop/restart of pregeneration
                        $TransactionalQueueEntry['StatusEnum'] == TransactionEmails::STATUS_PREGENERATED;
                    break;
            }

            if ($ResetAR) {
                $EmailID = SplitTests::GetNextSplittestVariantForNewsletter($ArrayCampaign, $SubscriberID);

                // reset queue entry
                $updatedRows = db_update($tableName)
                    ->fields([
                        'TimeToSend' => $TimeToSend,
                        'StatusEnum' => TransactionEmails::STATUS_PENDING,
                        'StatusReason' => 0,
                        'RelEmailID' => $EmailID,
                        'ModOfCampaignID' => static::CalcShard($UserID, $ArrayCampaign['CampaignID']),
                    ])
                    ->condition('RelOwnerUserID', $UserID)
                    ->condition('RelSubscriberID', $SubscriberID)
                    ->condition('ReferenceID', $ReferenceID)
                    ->condition('RelAutoResponderID', $ArrayCampaign['CampaignID'])
                    ->execute();

                // race condition. outdated entry was deleted by another process. do insert
                if ($updatedRows <= 0) {
                    $preventInsert = false;
                }
            }

            self::commitTransaction();

            if ($preventInsert) {
                return false;
            }
        }

        // insert
        $EmailID = SplitTests::GetNextSplittestVariantForNewsletter($ArrayCampaign, $SubscriberID);
        $ArrayFieldnValues = [
            'RelOwnerUserID' => $UserID,
            'RelSubscriberID' => $SubscriberID,
            'ReferenceID' => $ReferenceID,
            'TimeToSend' => $TimeToSend,
            'RelAutoResponderID' => $ArrayCampaign['CampaignID'],
            'StatusEnum' => TransactionEmails::STATUS_PENDING,
            'StatusReason' => 0,
            'RelEmailID' => $EmailID,
        ];
        try {
            $result = TransactionEmails::InsertWithMod($ArrayFieldnValues, $tableName);
        } catch (PDOException $e) {
            // RACE
            // if the insert fails, we have a another job that performs the same task
            // as we lost the race, we shall just quit
            $result = false;
        }

        self::commitTransaction();

        return $result;
    }

    /**
     * @return int number of affected rows
     */
    public static function Update($ArrayFieldAndValues, $ArrayCriterias, $tableName): int
    {
        return kt_update_rows($ArrayFieldAndValues, $ArrayCriterias, "{{$tableName}}");
    }

    /* calc shard id
     * calc the mod to 1024, so we can divide the number of slices by multiples of 2
     * except for customers with own shards
     */
    public static function CalcShard($UserID, $CampaignID)
    {
        $existing_customers_in_separate_slices = Settings::get("klicktipp_customers_in_separate_slices");
        if (in_array($UserID, $existing_customers_in_separate_slices)) {
            $ShardID = -1 * intval($UserID);
        } else {
            $ShardID = $CampaignID % 1024;
        }
        return $ShardID;
    }

    /* insert and updates with mod slices (simulating sharding)
     *
     * @throw \PDOException
     */
    public static function InsertWithMod(&$ArrayFieldnValues, $tableName)
    {
        $ArrayFieldnValues['ModOfCampaignID'] = static::CalcShard(
            $ArrayFieldnValues['RelOwnerUserID'],
            $ArrayFieldnValues['RelAutoResponderID']
        );
        $ArrayFieldnValues['CreateTime'] = time();

        $result = db_insert($tableName)->fields($ArrayFieldnValues)->execute();

        // db_insert results in \InsertQuery. Value of 'return'-option is \Database::RETURN_INSERT_ID (see \InsertQuery::__construct)
        // in case of success result is 0. In same cases result can be NULL (e.g if $ArrayFieldnValues is empty)
        if ($result === '0') {
            return true;
        }
        Errors::typedUnexpected(
            'kt-unexpected-db-result',
            __METHOD__ . ': unexpected insert !result(!type)',
            ['!result' => $result, '!type' => gettype($result)]
        );
        return false;
    }

    /**
     * Register a single automation to a subscriber
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $CampaignID
     * @param bool $ForceStart
     */
    public static function StartAutomation($UserID, $SubscriberID, $ReferenceID, $CampaignID, $ForceStart = false)
    {
        $ArrayCampaign = TransactionEmails::GetCachedCampaign($UserID, $CampaignID);
        if (empty($ArrayCampaign)) {
            return;
        }
        if (!Campaigns::IsProcessflow($ArrayCampaign['AutoResponderTriggerTypeEnum'])) {
            return;
        }
        if (!in_array($ArrayCampaign['CampaignStatusEnum'], Campaigns::$ArrayCampaignStatiSending)) {
            return;
        }

        TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ArrayCampaign, false, $ForceStart);
    }

    public static function CheckForRestartAutomation(int $userId, int $subscriberId, int $referenceId, int $campaignId, array $restartState): int
    {

        $arrayCampaign = TransactionEmails::GetCachedCampaign($userId, $campaignId);
        if (empty($arrayCampaign)) {
            return 0;
        }

        if (!Campaigns::IsProcessflow($arrayCampaign['AutoResponderTriggerTypeEnum'])) {
            return 0;
        }
        if (!in_array($arrayCampaign['CampaignStatusEnum'], Campaigns::$ArrayCampaignStatiSending)) {
            return 0;
        }

        [$nextStateId] = CampaignsProcessFlow::CheckStartState($arrayCampaign, $subscriberId, $referenceId, true);
        if (!empty($nextStateId)) {
            $nextState = CampaignsProcessFlow::FindState($arrayCampaign, $nextStateId);

            if (!$nextState) {
                // very unlikely but who knows
                TransactionEmails::RunFailed($arrayCampaign, $subscriberId, $referenceId, $nextState, TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE);
                return 0;
            }

            // clear the goal history so goals can be reached again after restart
            CampaignsProcessFlow::ClearGoalsHistoryOfSubscriber($userId, $campaignId, $subscriberId);

            if (!empty($restartState['tagID'])) {
                Subscribers::TagSubscriber($userId, $subscriberId, $restartState['tagID'], $referenceId, true);
            }
        }

        return $nextStateId;
    }

    /**
     * Unregister a single automation to a subscriber
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $CampaignID
     */
    public static function StopAutomation($UserID, $SubscriberID, $ReferenceID, $CampaignID)
    {
        // get queue entry
        $QueueEntry = TransactionEmails::RetrieveTransactionByID($UserID, $SubscriberID, $ReferenceID, $CampaignID);
        if (empty($QueueEntry)) {
            return;
        }

        // already stopped? -> avoid triggering "actionless" action
        if ($QueueEntry['StatusEnum'] == TransactionEmails::STATUS_SENT) {
            return;
        }

        /** @var CampaignsProcessFlow $ObjectCamnpaign */
        $ObjectCamnpaign = Campaigns::FromID($UserID, $CampaignID);
        if (empty($ObjectCamnpaign)) {
            return;
        }
        if (!Campaigns::IsProcessflow($ObjectCamnpaign->GetData('AutoResponderTriggerTypeEnum'))) {
            return;
        }
        if (!in_array($ObjectCamnpaign->GetData('CampaignStatusEnum'), Campaigns::$ArrayCampaignStatiSending)) {
            return;
        }

        // get subscriber
        $FullSubscriber = TransactionEmails::GetCachedFullSubscriber($UserID, $SubscriberID, $ReferenceID);
        if (empty($FullSubscriber)) {
            return;
        }

        TransactionEmails::RunSucceeded($ObjectCamnpaign->GetData(), $FullSubscriber, $ReferenceID);
    }

    /**
     * create or update queue entry for automation
     * called after:
     * - an automation has been (re-)started
     * - a subscriber has been triggered
     * - an automation has been started by another campaign or splittest
     * so:
     * - we will create a new queue entry if there is none
     * - we reset a PAUSED or STOPPED state, if set
     * - if a campaign has changed, we will reset the SENT state on multiple sents
     * - if a campaign has changed, we will reset the PAUSED_AFTER state if a state has been added
     * - if the queue status has changed, we will check for goals
     *
     * @param $UserID
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $ArrayCampaign
     * @param bool $CampaignChanged
     * @param bool $ForceStart
     */
    public static function RunStarted(
        $UserID,
        $SubscriberID,
        $ReferenceID,
        $ArrayCampaign,
        $CampaignChanged = false,
        $ForceStart = false
    ) {
        // start transaction to avoid race conditions and multiple inserts of same queue entry
        self::beginTransaction(self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE);

        // check for exiting queue entry
        $TransactionalQueueEntry = static::getTransactionalQueueEntry(
            $UserID,
            $SubscriberID,
            $ArrayCampaign['CampaignID'],
            $ReferenceID
        );

        // check for goals will be set, if subscriber is pending or has a state, where he can be set to pending
        $CheckForGoals = false;

        if (!empty($TransactionalQueueEntry)) {
            switch ($TransactionalQueueEntry['StatusEnum']) {
                case TransactionEmails::STATUS_PENDING_AFTER:
                    // if we have a change within a custom field which is the base for
                    // for a waiting action, do a recalculation if the custom field changes
                    $state = CampaignsProcessFlow::FindState($ArrayCampaign, $TransactionalQueueEntry['StateID']);
                    if (empty($state)) {
                        self::commitTransaction(self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE);

                        // state has been removed, subscriber state is lost
                        TransactionEmails::RunFailed(
                            $ArrayCampaign,
                            $SubscriberID,
                            $ReferenceID,
                            $TransactionalQueueEntry['StateID'],
                            TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE
                        );
                        return;
                    }
                    if ($state['type'] === CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT) {
                        if (
                            in_array($state['delayType'], [
                                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD,
                                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_HOURS,
                                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MINUTES,
                                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_DAYS,
                                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_WEEKS,
                                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MONTHS,
                                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_YEARS,
                                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_BIRTHDAY,
                            ])
                        ) {
                            $currentTimeToSend = $TransactionalQueueEntry['TimeToSend'];
                            $newTimeToSend = TransactionEmails::CalcTransactionalWait(
                                $state,
                                time(),
                                $TransactionalQueueEntry['RelSubscriberID'],
                                $ReferenceID,
                                $UserID
                            );

                            // CalcTransactionalWait can return a string, so this check is necessary
                            if ($newTimeToSend <= 0) {
                                $newTimeToSend = time();
                            }

                            if ($newTimeToSend != $currentTimeToSend) {
                                db_update(TransactionalQueue::TABLE_NAME)
                                    ->fields([
                                        'TimeToSend' => $newTimeToSend,
                                    ])
                                    ->condition('RelOwnerUserID', $UserID)
                                    ->condition('RelSubscriberID', $TransactionalQueueEntry['RelSubscriberID'])
                                    ->condition('RelAutoResponderID', $TransactionalQueueEntry['RelAutoResponderID'])
                                    ->condition('ReferenceID', $TransactionalQueueEntry['ReferenceID'])
                                    ->execute();
                            }
                        }
                    }
                    // check for goals, as subscriber can be set to pending if goal is reached
                    $CheckForGoals = true;
                    break;
                case TransactionEmails::STATUS_PENDING:
                    $CheckForGoals = true;
                    if (
                        CampaignsProcessFlow::FindState(
                            $ArrayCampaign,
                            $ArrayCampaign['ProcessFlow']['start']
                        )['next'] == $TransactionalQueueEntry['StateID']
                    ) {
                        // if a subscriber repeatedly runs into a multisend compaign,
                        // we have to consider the restart delay (see DEV-4069)
                        return $TransactionalQueueEntry['TimeToSend'] <= time() ||
                            (
                                (
                                    // for campaigns without multisend, we have to consider 5 mintes delay
                                    // of restart action
                                    !$ArrayCampaign['MultipleSendFlag'] &&
                                    // other delays can be skipped
                                    $TransactionalQueueEntry['TimeToSend'] - time() > 300
                                )
                            );
                    }
                    break;
                case TransactionEmails::STATUS_PAUSED_QUEUE_JOB:
                    // check for goals, as subscriber can be set to pending if goal is reached
                    $CheckForGoals = true;
                    break;
                case TransactionEmails::STATUS_PAUSED_AFTER:
                case TransactionEmails::STATUS_PAUSED_AFTER_NO:
                    if ($CampaignChanged) {
                        // check if automation has been completed
                        $state = CampaignsProcessFlow::FindState($ArrayCampaign, $TransactionalQueueEntry['StateID']);
                        if (empty($state)) {
                            self::commitTransaction(self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE);

                            // state has been removed, subscriber state is lost
                            TransactionEmails::RunFailed(
                                $ArrayCampaign,
                                $SubscriberID,
                                $ReferenceID,
                                $TransactionalQueueEntry['StateID'],
                                TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE
                            );
                            return;
                        }

                        // state has been added recently
                        $next = ($TransactionalQueueEntry['StatusEnum'] == TransactionEmails::STATUS_PAUSED_AFTER) ? $state['next'] : $state['nextNo'];
                        if (!empty($next)) {
                            db_update(TransactionalQueue::TABLE_NAME)
                                ->fields([
                                    'StatusEnum' => TransactionEmails::STATUS_PENDING,
                                    'StateID' => $next,
                                    'ModOfCampaignID' => static::CalcShard(
                                        $UserID,
                                        $TransactionalQueueEntry['RelAutoResponderID']
                                    ),
                                ])
                                ->condition('RelOwnerUserID', $UserID)
                                ->condition('RelSubscriberID', $TransactionalQueueEntry['RelSubscriberID'])
                                ->condition('RelAutoResponderID', $TransactionalQueueEntry['RelAutoResponderID'])
                                ->condition('ReferenceID', $TransactionalQueueEntry['ReferenceID'])
                                ->execute();
                        }
                    }
                    // check for goals, as subscriber can be set to pending if goal is reached
                    $CheckForGoals = true;
                    break;
                case TransactionEmails::STATUS_PAUSED:
                    // retrigger paused state, but put a delay of at least 5 minutes after pausing
                    // to avoid problems with endless loops
                    $state = CampaignsProcessFlow::FindState($ArrayCampaign, $TransactionalQueueEntry['StateID']);
                    $newStatus = $state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_START
                        ? TransactionEmails::STATUS_WAITING_FOR_TEMPORAL_CONDITION
                        : TransactionEmails::STATUS_PENDING;
                    db_update(TransactionalQueue::TABLE_NAME)
                        ->fields([
                            'StatusEnum' => $newStatus,
                            'TimeToSend' => max($TransactionalQueueEntry['TimeToSend'] + 300, time()),
                            'ModOfCampaignID' => static::CalcShard(
                                $UserID,
                                $TransactionalQueueEntry['RelAutoResponderID']
                            ),
                        ])
                        ->condition('RelOwnerUserID', $UserID)
                        ->condition('RelSubscriberID', $TransactionalQueueEntry['RelSubscriberID'])
                        ->condition('RelAutoResponderID', $TransactionalQueueEntry['RelAutoResponderID'])
                        ->condition('ReferenceID', $TransactionalQueueEntry['ReferenceID'])
                        ->execute();
                    $CheckForGoals = true;
                    break;
                case TransactionEmails::STATUS_PAUSED_RESTART:
                    $state = CampaignsProcessFlow::FindState($ArrayCampaign, $TransactionalQueueEntry['StateID']);
                    if (empty($state)) {
                        self::commitTransaction(self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE);
                        // state has been removed, subscriber state is lost
                        TransactionEmails::RunFailed($ArrayCampaign, $SubscriberID, $ReferenceID, $TransactionalQueueEntry['StateID'], TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE);
                        return;
                    }

                    if (
                        $TransactionalQueueEntry['TimeToSend'] <= time() &&
                        $TransactionalQueueEntry['TimeToSend'] != TransactionEmails::STATUS_PENDING
                    ) {
                        // re-trigger paused restart state to check if the start condition is true
                        $restartStateId = TransactionEmails::CheckForRestartAutomation(
                            $UserID,
                            $TransactionalQueueEntry['RelSubscriberID'],
                            $TransactionalQueueEntry['ReferenceID'],
                            $TransactionalQueueEntry['RelAutoResponderID'],
                            $state
                        );

                        if (!empty($restartStateId)) {
                            $fields = [
                                'StatusEnum' => TransactionEmails::STATUS_PENDING,
                                'TimeToSend' => time(),
                                'ModOfCampaignID' => static::CalcShard(
                                    $UserID,
                                    $TransactionalQueueEntry['RelAutoResponderID']
                                ),
                            ];
                            if (!self::startStateHasTemporalCondition($ArrayCampaign)) {
                                $fields['StateID'] = $restartStateId;
                                $fields['GoalReached'] = 0;
                            }
                            db_update(TransactionalQueue::TABLE_NAME)
                                ->fields($fields)
                                ->condition('RelOwnerUserID', $UserID)
                                ->condition('RelSubscriberID', $TransactionalQueueEntry['RelSubscriberID'])
                                ->condition('RelAutoResponderID', $TransactionalQueueEntry['RelAutoResponderID'])
                                ->condition('ReferenceID', $TransactionalQueueEntry['ReferenceID'])
                                ->execute();
                            //$CheckForGoals = FALSE; // the pending state run will check the goals
                        }
                    }
                    break;
                case TransactionEmails::STATUS_STOPPED:
                    $state = CampaignsProcessFlow::FindState($ArrayCampaign, $TransactionalQueueEntry['StateID']);
                    if (empty($state)) {
                        self::commitTransaction(self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE);
                        // state has been removed, subscriber state is lost
                        TransactionEmails::RunFailed(
                            $ArrayCampaign,
                            $SubscriberID,
                            $ReferenceID,
                            $TransactionalQueueEntry['StateID'],
                            TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE
                        );
                        return;
                    }
                    $timeToSend = $TransactionalQueueEntry['TimeToSend'];
                    if (
                        $state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT &&
                        $state['delayType'] == CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_LIMITER
                    ) {
                        // retrigger limiter with current time to immediately recalculate limits
                        $timeToSend = time();
                    }
                    db_update(TransactionalQueue::TABLE_NAME)
                        ->fields([
                            'StatusEnum' => TransactionEmails::STATUS_PENDING,
                            'TimeToSend' => $timeToSend,
                            'ModOfCampaignID' => static::CalcShard(
                                $UserID,
                                $TransactionalQueueEntry['RelAutoResponderID']
                            ),
                        ])
                        ->condition('RelOwnerUserID', $UserID)
                        ->condition('RelSubscriberID', $TransactionalQueueEntry['RelSubscriberID'])
                        ->condition('RelAutoResponderID', $TransactionalQueueEntry['RelAutoResponderID'])
                        ->condition('ReferenceID', $TransactionalQueueEntry['ReferenceID'])
                        ->execute();
                    $CheckForGoals = true;
                    break;
                case TransactionEmails::STATUS_STOPPED_AFTER:
                    db_update(TransactionalQueue::TABLE_NAME)
                        ->fields([
                            'StatusEnum' => TransactionEmails::STATUS_PENDING_AFTER,
                            'ModOfCampaignID' => static::CalcShard(
                                $UserID,
                                $TransactionalQueueEntry['RelAutoResponderID']
                            ),
                        ])
                        ->condition('RelOwnerUserID', $UserID)
                        ->condition('RelSubscriberID', $TransactionalQueueEntry['RelSubscriberID'])
                        ->condition('RelAutoResponderID', $TransactionalQueueEntry['RelAutoResponderID'])
                        ->condition('ReferenceID', $TransactionalQueueEntry['ReferenceID'])
                        ->execute();
                    $CheckForGoals = true;
                    break;
                case TransactionEmails::STATUS_SENT:
                    if ($CampaignChanged && $ArrayCampaign['MultipleSendFlag']) {
                        // if campaign has changed, we need to check for new subscriber states

                        // MultipleSendFlag has been switched on, so we need to remove older 'sent' states
                        TransactionEmails::RemoveFromQueueForMultipleSend(
                            $ArrayCampaign,
                            $TransactionalQueueEntry['RelSubscriberID'],
                            $TransactionalQueueEntry['ReferenceID']
                        );

                        // state cleaned: a new queue entry will be created below
                        $TransactionalQueueEntry = false;
                    }
                    break;
                case TransactionEmails::STATUS_WAITING_FOR_TEMPORAL_CONDITION:
                    // a trigger could mean, that a non temporal contidition is met und entry has to move further
                    // before time condition is met, so let's check again.
                    // We check at most every 5 minutes, analogous to paused entries
                    $nextCheck = time() + 300;
                    if ($nextCheck < $TransactionalQueueEntry['TimeToSend']) {
                        db_update(TransactionalQueue::TABLE_NAME)
                            ->fields([
                                'TimeToSend' => $nextCheck,
                            ])
                            ->condition('RelOwnerUserID', $UserID)
                            ->condition('RelSubscriberID', $TransactionalQueueEntry['RelSubscriberID'])
                            ->condition('RelAutoResponderID', $TransactionalQueueEntry['RelAutoResponderID'])
                            ->condition('ReferenceID', $TransactionalQueueEntry['ReferenceID'])
                            ->execute();
                    }
                    return;
                case TransactionEmails::STATUS_SENDING:
                case TransactionEmails::STATUS_FAILED:
                    // already processing or failed, so dont change the state
                    self::commitTransaction(self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE);
                    return;
                default:
                    // unknown state: we dont know, if we can process it at all
                    self::commitTransaction(self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE);
                    return;
            }
        }

        if (empty($TransactionalQueueEntry)) {
            // check for start condition and start
            [$ActionState, $TimeToSend, $failedSegments] = CampaignsProcessFlow::CheckStartState(
                $ArrayCampaign,
                $SubscriberID,
                $ReferenceID,
                $ForceStart
            );
            if (!empty($ActionState)) {
                $TransactionalQueueEntry = [
                    'RelOwnerUserID' => $UserID,
                    'RelSubscriberID' => $SubscriberID,
                    'ReferenceID' => $ReferenceID,
                    'TimeToSend' => $TimeToSend,
                    'RelAutoResponderID' => $ArrayCampaign['CampaignID'],
                    'StatusEnum' => TransactionEmails::STATUS_PENDING,
                    'StatusReason' => 0,
                    'StateID' => $ActionState,
                    'GoalReached' => 0,
                ];
                try {
                    $result = TransactionEmails::InsertWithMod(
                        $TransactionalQueueEntry,
                        TransactionalQueue::TABLE_NAME
                    );
                } catch (PDOException $e) {
                    // RACE
                    // if the insert fails, we have a another job that performs the same task
                    // as we lost the race, we shall just quit
                    self::commitTransaction(self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE);

                    // Get the existing queue entry.
                    $queueEntry = static::getTransactionalQueueEntry(
                        $UserID,
                        $SubscriberID,
                        $ArrayCampaign['CampaignID'],
                        $ReferenceID
                    );

                    static::logTransactionalQueueRaceCondition($e, $TransactionalQueueEntry, $queueEntry);

                    return;
                }
                if ($result) {
                    self::campaignStartedAfter($UserID, $ArrayCampaign['CampaignID'], $SubscriberID, $ReferenceID);

                    // check for goals, if subscriber is pending "now"
                    $CheckForGoals = ($TimeToSend <= time());
                }
            } elseif (
                (
                    $nextPossibleTime = self::calcNextPossibleMatch(
                        $failedSegments,
                        [
                            'UserID' => $UserID,
                            'SubscriberID' => $SubscriberID,
                            'ReferenceID' => $ReferenceID
                        ]
                    )
                ) > 0
            ) {
                self::createTemporalDBEntry(
                    $ArrayCampaign['RelOwnerUserID'],
                    $ArrayCampaign['CampaignID'],
                    $SubscriberID,
                    $ReferenceID,
                    $nextPossibleTime
                );
            }
        }

        //check if the campaign has goals, the subscriber hasn't reached a goal yet and the goal condition is met
        if ($CheckForGoals) {
            self::checkAndProcessGoal($TransactionalQueueEntry, $ArrayCampaign, $ActionState ?? null);
        }

        self::commitTransaction(self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE);
    }

    /**
     * @param TransactionalQueueEntry $queueItem
     * @param CampaignArray $campaign
     * @param int|null $stateId
     * @param int $nextPossibleTime
     * @return ?State processed goal state or null if not applicable
     * @throws Exception
     */
    private static function checkAndProcessGoal(
        array &$queueItem,
        array $campaign,
        int $stateId = null,
        int &$nextPossibleTime = 0
    ): ?array {
        $goalState = CampaignsProcessFlow::CheckGoals(
            $queueItem['GoalReached'],
            $campaign,
            $queueItem['RelSubscriberID'],
            $queueItem['ReferenceID'],
            $stateId ?? $queueItem['StateID'],
            $nextPossibleTime
        );
        if (!empty($goalState)) {
            TransactionEmails::ProcessGoal($queueItem, $campaign, $goalState);
            return $goalState;
        }
        return null;
    }

    /**
     * Executes actions after a subscribers runs into a campaign
     */
    public static function campaignStartedAfter(
        int $userId,
        int $campaignId,
        int $subscriberId,
        int $referenceId
    ): void {
        CampaignsStats::increment(
            ['TotalRecipients'],
            $campaignId,
            $userId
        );

        // tag subscriber "automation started"
        $tag = Tag::RetrieveTagOfEntity(
            $userId,
            Tag::CATEGORY_AUTOMATION_STARTED,
            $campaignId
        );
        if ($tag) {
            Subscribers::TagSubscriber($userId, $subscriberId, $tag['TagID'], $referenceId, true);
            TransactionEmails::RegisterAutomations($userId, $subscriberId, $referenceId);
        }
    }

    /**
     * @param int $relOwnerUserID
     * @param int $campaignID
     * @param int $subscriberID
     * @param int $referenceID
     * @param int $nextPossibleTime
     * @param int $stateID
     * @return void
     * @throws Exception
     */
    public static function createTemporalDBEntry(
        $relOwnerUserID,
        $campaignID,
        $subscriberID,
        $referenceID,
        $nextPossibleTime,
        $stateID = 1
    ) {
        $table = '{' . TransactionalQueue::TABLE_NAME . '}';
        $sql = "INSERT INTO $table SET TimeToSend = :TimeToSend, StatusEnum = :StatusEnum, " .
            "RelOwnerUserID = :RelOwnerUserID, RelSubscriberID = :RelSubscriberID, ReferenceID = :ReferenceID, " .
            "RelAutoResponderID = :RelAutoResponderID, StateID = :StateID, GoalReached = 0 " .
            "ON DUPLICATE KEY UPDATE TimeToSend = :TimeToSend, StateID = :StateID, StatusEnum = :StatusEnum, " .
            "GoalReached = CASE WHEN StateID = 1 THEN 0 ELSE GoalReached END";

        db_query($sql, [
            ':TimeToSend' => $nextPossibleTime, // time, when the restart trigger check starts
            ':StatusEnum' => TransactionEmails::STATUS_WAITING_FOR_TEMPORAL_CONDITION,
            ':RelSubscriberID' => $subscriberID,
            ':RelAutoResponderID' => $campaignID,
            ':RelOwnerUserID' => $relOwnerUserID,
            ':ReferenceID' => $referenceID,
            ':StateID' => $stateID,
        ]);
    }


    /**
     * @param self::TRANSACTION_TYPE_* $type
     *
     * @return void
     */
    private static function beginTransaction(int $type = self::TRANSACTION_TYPE_OTHER): void
    {
        if (self::transactionsActive($type)) {
            kt_begin_transaction();
        }
    }

    /**
     * @param self::TRANSACTION_TYPE_* $type
     *
     * @return void
     */
    private static function commitTransaction(int $type = self::TRANSACTION_TYPE_OTHER): void
    {
        if (self::transactionsActive($type)) {
            kt_commit_transaction();
        }
    }

    /**
     * @param self::TRANSACTION_TYPE_* $type
     *
     * @return bool
     */
    private static function transactionsActive(int $type = self::TRANSACTION_TYPE_OTHER): bool
    {
        switch ($type) {
            case self::TRANSACTION_TYPE_SUBSCRIBER_QUEUE:
                $variable = 'klicktipp_transaction_emails_db_transaction_runstarted';
                break;
            case self::TRANSACTION_TYPE_OTHER:
            default:
                $variable = 'klicktipp_transaction_emails_db_transaction';
                break;
        }
        return (bool)variable_get($variable, true);
    }

    /**
     * @param int $userId
     * @param int $subscriberId
     * @param int $campaignId
     * @param int $referenceId
     * @return false|TransactionalQueueEntry
     * @throws \Doctrine\DBAL\Exception
     */
    private static function getTransactionalQueueEntry($userId, $subscriberId, $campaignId, $referenceId)
    {
        /** @var TransactionalQueueEntry|false $entry */
        $entry = kt_fetch_array(
            db_query(
                "SELECT * FROM {" . TransactionalQueue::TABLE_NAME . "} " .
                " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelAutoResponderID = :RelAutoResponderID AND ReferenceID = :ReferenceID",
                [
                    ':RelOwnerUserID' => $userId,
                    ':RelSubscriberID' => $subscriberId,
                    ':RelAutoResponderID' => $campaignId,
                    ':ReferenceID' => $referenceId,
                ]
            )
        );
        return $entry;
    }

    /**
     * @param Throwable $e
     * @param mixed $expectedQueueEntry
     * @param mixed $actualQueueEntry
     * @return void
     */
    public static function logTransactionalQueueRaceCondition(
        Throwable $e,
        $expectedQueueEntry,
        $actualQueueEntry
    ): void {
        // If this happens the trigger was processed by another job/request, but the resulting queue entry is the same.
        // As the result is the same, we can just log a warning.
        if (static::areQueueEntriesEqual($expectedQueueEntry, $actualQueueEntry)) {
            watchdog(
                'kt-unexpected-db-result',
                __METHOD__ . ': DEBUG InsertWithMod exception',
                [
                    '!actualQueueEntry' => $actualQueueEntry,
                    '!expectedQueueEntry' => $expectedQueueEntry,
                    '!exception' => $e
                ],
                WATCHDOG_WARNING
            );

            return;
        }

        // DEBUG: if both jobs fail, we won't have a automation start tag
        Errors::typedUnexpected(
            'kt-unexpected-db-result',
            __METHOD__ . ': DEBUG InsertWithMod exception',
            [
                '!actualQueueEntry' => $actualQueueEntry,
                '!expectedQueueEntry' => $expectedQueueEntry,
                '!exception' => $e
            ]
        );
    }

    private static function areQueueEntriesEqual($queueEntry1, $queueEntry2): bool
    {
        if (!is_array($queueEntry1) || !is_array($queueEntry2)) {
            return false;
        }

        // Other fields are ignored for comparison. E.g. CreateTime, etc.
        $checkedFields = array_flip([
            'RelOwnerUserID',
            'RelSubscriberID',
            'RelAutoResponderID',
            'StatusEnum',
            'StatusReason',
            'StateID',
            'GoalReached',
            'ModOfCampaignID',
            'ReferenceID',
        ]);

        $filteredEntry1 = array_intersect_key($queueEntry1, $checkedFields);
        $filteredEntry2 = array_intersect_key($queueEntry2, $checkedFields);

        return $filteredEntry1 == $filteredEntry2;
    }

    public static function RunSucceeded($ArrayAutoResponder, $ArraySubscriber, $ReferenceID, $CurrentState = -1)
    {
        self::beginTransaction();

        // update user stats
        /* not evaluated now; keep for future use
         $ArrayActivities = array(
         'TotalSentEmail'   =>  1,
         );
         Statistics::UpdateListActivityStatistics(0, $ArrayAutoResponder['RelOwnerUserID'], $ArrayActivities);
         */

        // update campaign stats
        CampaignsStats::increment(
            ['TotalSent'],
            $ArrayAutoResponder['CampaignID'],
            $ArrayAutoResponder['RelOwnerUserID']
        );

        // tag subscriber "automation finished"
        Subscribers::TagSubscriber(
            $ArrayAutoResponder['RelOwnerUserID'],
            $ArraySubscriber['SubscriberID'],
            $ArrayAutoResponder['AutomationFinishedSmartTagID'],
            $ReferenceID,
            true
        );

        if (!empty($ArrayAutoResponder['MultipleSendFlag'])) {
            // multiple send: untag start tag and remove queue entry
            TransactionEmails::RemoveFromQueueForMultipleSend(
                $ArrayAutoResponder,
                $ArraySubscriber['SubscriberID'],
                $ReferenceID
            );
        } else {
            // finished
            $Values = [
                'StatusEnum' => TransactionEmails::STATUS_SENT,
            ];
            if ($CurrentState >= 0) {
                $Values['StateID'] = $CurrentState;
            }
            TransactionEmails::Update(
                $Values,
                [
                    'RelSubscriberID' => $ArraySubscriber['SubscriberID'],
                    'RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                    'RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                    'ReferenceID' => $ReferenceID,
                ],
                TransactionalQueue::TABLE_NAME
            );
        }

        // propagate changes to other campaigns
        TransactionEmails::RegisterAutomations(
            $ArrayAutoResponder['RelOwnerUserID'],
            $ArraySubscriber['SubscriberID'],
            $ReferenceID
        );

        self::commitTransaction();
    }

    public static function RunPaused($SubscriberID, $ReferenceID, $ArrayAutoResponder, $CurrentState)
    {
        TransactionEmails::Update(
            [
                'TimeToSend' => time(), // time that shows up in history
                'StatusEnum' => TransactionEmails::STATUS_PAUSED,
                'StateID' => $CurrentState,
            ],
            [
                'RelSubscriberID' => $SubscriberID,
                'RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                'RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                'ReferenceID' => $ReferenceID,
            ],
            TransactionalQueue::TABLE_NAME
        );
    }

    /**
     * @phpstan-ignore-next-line
     * @param array $arrayCampaign
     * @return bool
     */
    public static function startStateHasTemporalCondition(array $arrayCampaign): bool
    {
        $startState = CampaignsProcessFlow::FindState($arrayCampaign, $arrayCampaign['ProcessFlow']['start']);
        foreach ($startState['segments'] as $segment) {
            foreach ($segment['conditions'] as $condition) {
                if (self::isConditionTypeTemporal($condition['op'])) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Set the TransactionalQueue to status TransactionEmails::STATUS_PAUSED_RESTART if not already set
     * This will wait for the next trigger for the subscriber and then check if the start condtion is true
     * @param $subscriberId
     * @param $referenceId
     * @param $arrayCampaign
     * @param $currentState
     * @return void
     * @throws \Doctrine\DBAL\Exception
     */
    public static function RunPausedForRestart(int $subscriberId, int $referenceId, array $arrayCampaign, int $currentState)
    {
        $timeToSend = time();
        $timeToSendDelayed5Min = strtotime('+5 minutes');
        $startConditionContainsTemporalCondition = false;
        [$nextStateId, $nextTimeToSend] = CampaignsProcessFlow::CheckStartState(
            $arrayCampaign,
            $subscriberId,
            $referenceId,
            true
        );
        $newStatus = TransactionEmails::STATUS_PAUSED_RESTART;
        $newState = $currentState;
        $goalsSql = '';
        if (!empty($nextStateId)) {
            if (
                self::applyRateLimit(
                    $arrayCampaign['RelOwnerUserID'],
                    $subscriberId,
                    $referenceId,
                    $arrayCampaign['CampaignID'],
                    $nextStateId
                )
            ) {
                return;
            }
            $timeToSend = $timeToSendDelayed5Min;
            if (self::startStateHasTemporalCondition($arrayCampaign)) {
                $startState = CampaignsProcessFlow::FindState($arrayCampaign, $arrayCampaign['ProcessFlow']['start']);
                $newStatus = TransactionEmails::STATUS_PENDING;
                $newState = $startState['next'];
                $goalsSql = ', GoalReached = 0 ';
                $startConditionContainsTemporalCondition = true;
            }
        }

        if ($nextTimeToSend > 0) {
            $table = '{' . TransactionalQueue::TABLE_NAME . '}';
            $sql = "UPDATE $table SET TimeToSend = :TimeToSend, StatusEnum = :StatusEnum, StateID = :StateID " .
                $goalsSql .
                "WHERE RelOwnerUserID = :RelOwnerUserID " .
                "AND RelSubscriberID = :RelSubscriberID " .
                "AND ReferenceID = :ReferenceID " .
                "AND RelAutoResponderID = :RelAutoResponderID AND StatusEnum != :PausedRestart";

            $args = [
                ':TimeToSend' => $timeToSend, // time, when the restart trigger check starts
                ':StatusEnum' => $newStatus,
                ':StateID' => $newState,
                ':RelSubscriberID' => $subscriberId,
                ':RelAutoResponderID' => $arrayCampaign['CampaignID'],
                ':RelOwnerUserID' => $arrayCampaign['RelOwnerUserID'],
                ':ReferenceID' => $referenceId,
                ':PausedRestart' => TransactionEmails::STATUS_PAUSED_RESTART,
            ];

            db_query($sql, $args);
        }
        if ($nextTimeToSend == 0 || $startConditionContainsTemporalCondition) {
            $thisState = CampaignsProcessFlow::FindState($arrayCampaign, $currentState);
            // clear the goal history so goals can be reached again after restart
            CampaignsProcessFlow::ClearGoalsHistoryOfSubscriber(
                $arrayCampaign['RelOwnerUserID'],
                $arrayCampaign['CampaignID'],
                $subscriberId
            );

            if (!empty($thisState['tagID'])) {
                Subscribers::TagSubscriber(
                    $arrayCampaign['RelOwnerUserID'],
                    $subscriberId,
                    $thisState['tagID'],
                    $referenceId,
                    true
                );
            }
        }
    }

    /**
     * Set the current transactional queue entry to state STATUS_PAUSED_QUEUE_JOB
     * A state in the processflow needs to execute a queue job -> wait the job is done
     * Note: The queue worker must set the status of the current transactional queue entry back to STATUS_PENDING
     *
     * @param int $RelSubscriberID
     * @param $ReferenceID
     * @param array $ArrayAutoResponder
     * @param $CurrentState
     *
     * @see: TransactionEmails::RunJobFinshed(), TransactionEmails::RunJobFailed()
     */
    public static function RunJobStarted($RelSubscriberID, $ReferenceID, $ArrayAutoResponder, $CurrentState)
    {
        TransactionEmails::Update(
            [
                'TimeToSend' => time(), // time that shows up in history
                'StatusEnum' => TransactionEmails::STATUS_PAUSED_QUEUE_JOB,
                'StateID' => $CurrentState,
            ],
            [
                'RelSubscriberID' => $RelSubscriberID,
                'RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                'RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                'ReferenceID' => $ReferenceID,
            ],
            TransactionalQueue::TABLE_NAME
        );
    }

    /**
     * Change the status of the queue item to "Sending"
     *
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $CampaignID
     * @param $UserID
     * @param $CurrentState
     */
    public static function RunJobSending($SubscriberID, $ReferenceID, $CampaignID, $UserID, $CurrentState, $tableName)
    {
        $values = ['StatusEnum' => TransactionEmails::STATUS_SENDING];
        // @todo Refectory by using ingeritance
        if ($tableName == TransactionalQueue::TABLE_NAME) {
            $values['StateID'] = $CurrentState;
        }
        TransactionEmails::Update(
            $values,
            [
                'RelSubscriberID' => $SubscriberID,
                'RelAutoResponderID' => $CampaignID,
                'RelOwnerUserID' => $UserID,
                'ReferenceID' => $ReferenceID,
            ],
            $tableName
        );
    }

    /**
     * Set the current transactional queue entry to state STATUS_PENDING or STATUS_PAUSED_AFTER
     * A state in the processflow needed to execute a queue job -> the job is finsihed
     * Note: tThe ransactional queue has to jump to the next state or wait on an open end
     *
     * @param int $SubscriberID
     * @param $ReferenceID
     * @param $ArrayAutoResponder
     * @param $NextState : id of the following state
     */
    public static function RunJobFinshed($SubscriberID, $ReferenceID, $ArrayAutoResponder, $NextState)
    {
        if (empty($NextState)) {
            // open end found -> PAUSE until an action is added behind the current state
            TransactionEmails::Update(
                [
                    'TimeToSend' => time(),
                    'StatusEnum' => TransactionEmails::STATUS_PAUSED_AFTER,
                ],
                [
                    'RelSubscriberID' => $SubscriberID,
                    'RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                    'RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                    'ReferenceID' => $ReferenceID,
                ],
                TransactionalQueue::TABLE_NAME
            );


            return;
        } else {
            //jump to the next state, wait 1 minute for optimized processing

            TransactionEmails::Update(
                [
                    'TimeToSend' => strtotime('+1 minute'),
                    'StatusEnum' => TransactionEmails::STATUS_PENDING,
                    'StateID' => $NextState,
                    'ModOfCampaignID' => static::CalcShard(
                        $ArrayAutoResponder['RelOwnerUserID'],
                        $ArrayAutoResponder['CampaignID']
                    ),
                ],
                [
                    'RelSubscriberID' => $SubscriberID,
                    'RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                    'RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                    'ReferenceID' => $ReferenceID,
                ],
                TransactionalQueue::TABLE_NAME
            );
        }
    }

    /**
     * Set the current transactional queue entry to state STATUS_PENDING, the state id remains the same
     * Note: A state in the processflow needs to execute a queue job -> the job failed => try again at $TimeToSend
     *
     * @param int $SubscriberID
     * @param int $ReferenceID
     * @param array $ArrayAutoResponder
     * @param int $TimeToSend : try executing the state (and job) at that time;
     *   default: +1 day
     * @param int $StateID
     */
    public static function RunJobFailed($SubscriberID, $ReferenceID, $ArrayAutoResponder, $TimeToSend = 0, $StateID = 0)
    {
        $ArrayFieldAndValues = [
            'TimeToSend' => (empty($TimeToSend)) ? strtotime('+1 day') : $TimeToSend,
            'StatusEnum' => TransactionEmails::STATUS_PENDING,
            'ModOfCampaignID' => static::CalcShard(
                $ArrayAutoResponder['RelOwnerUserID'],
                $ArrayAutoResponder['CampaignID']
            ),
        ];

        if ($StateID > 0) {
            $ArrayFieldAndValues['StateID'] = $StateID;
        }

        TransactionEmails::Update(
            $ArrayFieldAndValues,
            [
                'RelSubscriberID' => $SubscriberID,
                'RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                'RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                'ReferenceID' => $ReferenceID,
            ],
            TransactionalQueue::TABLE_NAME
        );
    }

    /**
     * A run failed, make sure everything is consistent
     *
     * @param $ArrayAutoResponder
     * @param $SubscriberID
     * @param $ReferenceID
     * @param $CurrentState
     * @param $StatusReason
     */
    public static function RunFailed($ArrayAutoResponder, $SubscriberID, $ReferenceID, $CurrentState, $StatusReason)
    {
        self::beginTransaction();

        // update campaign stats
        CampaignsStats::increment(
            ['TotalFailed'],
            $ArrayAutoResponder['CampaignID'],
            $ArrayAutoResponder['RelOwnerUserID']
        );

        // tag subscriber "automation finished"
        if (!empty($ArrayAutoResponder['AutomationFinishedSmartTagID'])) {
            // Note: the run can fail because the campaign does not exist anymore (queue job)
            Subscribers::TagSubscriber(
                $ArrayAutoResponder['RelOwnerUserID'],
                $SubscriberID,
                $ArrayAutoResponder['AutomationFinishedSmartTagID'],
                $ReferenceID,
                true
            );
        }

        if (!empty($SubscriberID)) {
            if (!empty($ArrayAutoResponder['MultipleSendFlag'])) {
                TransactionEmails::RemoveFromQueueForMultipleSend($ArrayAutoResponder, $SubscriberID, $ReferenceID);
            } else {
                static::SetStatusFailed(
                    $StatusReason,
                    $ArrayAutoResponder['CampaignID'],
                    $ArrayAutoResponder['RelOwnerUserID'],
                    $SubscriberID,
                    $ReferenceID,
                    TransactionalQueue::TABLE_NAME,
                    $CurrentState
                );
            }
            TransactionEmails::RegisterAutomations($ArrayAutoResponder['RelOwnerUserID'], $SubscriberID, $ReferenceID);
        }

        self::commitTransaction();
    }

    public static function SendSucceeded(
        $ArrayAutoResponder,
        $EmailID,
        $SubscriberID,
        $ReferenceID,
        $AgedCheck = 0,
        $IsSMS = true
    ) {
        self::beginTransaction();

        if (Campaigns::IsProcessflow($ArrayAutoResponder['AutoResponderTriggerTypeEnum'])) {
            // automation email or sms

            // get email tag
            if ($IsSMS) {
                $tag = Tag::RetrieveTagOfEntity(
                    $ArrayAutoResponder['RelOwnerUserID'],
                    Tag::CATEGORY_SMS_SENT,
                    $EmailID
                );
            } else {
                $tag = Tag::RetrieveTagOfEntity(
                    $ArrayAutoResponder['RelOwnerUserID'],
                    Tag::CATEGORY_EMAIL_SENT,
                    $EmailID
                );
            }
            if ($tag) {
                Subscribers::TagSubscriber(
                    $ArrayAutoResponder['RelOwnerUserID'],
                    $SubscriberID,
                    $tag['TagID'],
                    $ReferenceID,
                    true
                );
                TransactionEmails::RegisterAutomations(
                    $ArrayAutoResponder['RelOwnerUserID'],
                    $SubscriberID,
                    $ReferenceID
                );
            }
        } else {
            // campaign/autoresponder email or sms
            $ArrayFieldAndValues = [
                'StatusEnum' => TransactionEmails::STATUS_SENT,
                'RelEmailID' => $EmailID
            ];

            if (!empty($AgedCheck)) {
                $ArrayFieldAndValues['AgedCheck'] = $AgedCheck;
            }

            TransactionEmails::Update(
                $ArrayFieldAndValues,
                [
                    'RelSubscriberID' => $SubscriberID,
                    'RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                    'RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                    'ReferenceID' => $ReferenceID,
                ],
                Campaigns::GetQueueTableNameByType($ArrayAutoResponder['AutoResponderTriggerTypeEnum'])
            );

            // get campaign tag
            $tag = Tag::RetrieveTagOfEntity(
                $ArrayAutoResponder['RelOwnerUserID'],
                Tag::CATEGORY_SENT,
                $ArrayAutoResponder['CampaignID']
            );

            // Tag Subsciber
            if (
                $tag &&
                (
                    $ArrayAutoResponder['GenerateDatetime'] <= 0 ||
                    // in case of pre-generation sent-tag may be set alredy by link-clicks or tracking pixel
                    // in this case tagging time MUST NOT be overwritten
                    !Subscribers::RetrieveTagging(
                        $ArrayAutoResponder['RelOwnerUserID'],
                        $tag['TagID'],
                        $SubscriberID,
                        $ReferenceID
                    )
                )
            ) {
                Subscribers::TagSubscriber(
                    $ArrayAutoResponder['RelOwnerUserID'],
                    $SubscriberID,
                    $tag['TagID'],
                    $ReferenceID,
                    true
                );
            }

            // update campaign stats
            CampaignsStats::increment(
                ['TotalSent'],
                $ArrayAutoResponder['CampaignID'],
                $ArrayAutoResponder['RelOwnerUserID']
            );

            // update user stats
            /* not evaluated now; keep for future use
             $ArrayActivities = array(
             'TotalSentEmail' =>  1,
             );
             Statistics::UpdateListActivityStatistics(0, $ArrayAutoResponder['RelOwnerUserID'], $ArrayActivities);
             */

            // multiple send
            if (!empty($ArrayAutoResponder['MultipleSendFlag'])) {
                // untag all given tags from precondition
                foreach ($ArrayAutoResponder['MultipleSendTags'] as $tagid) {
                    Subscribers::UntagSubscriber(
                        $ArrayAutoResponder['RelOwnerUserID'],
                        $SubscriberID,
                        $ReferenceID,
                        $tagid
                    );
                }

                $queueClass = Campaigns::GetQueueClassByType($ArrayAutoResponder['AutoResponderTriggerTypeEnum']);

                // now remove just send email from queue
                $queueClass::RemoveFromQueue([
                    'RelSubscriberID' => $SubscriberID,
                    'RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                    'RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                    'ReferenceID' => $ReferenceID,
                ]);
            }

            TransactionEmails::RegisterAutomations($ArrayAutoResponder['RelOwnerUserID'], $SubscriberID, $ReferenceID);
        }

        self::commitTransaction();
    }


    /**
     * @param array{'SendDatetime': int, 'CampaignID': int, 'RelOwnerUserID': int} $newsletter
     * @param array{'SubscriberID': int, 'ReferenceID': int} $subscriber
     *
     * @return void
     */
    public static function preGenerationSucceeded(array $newsletter, array $subscriber): void
    {
        $affectedRows = static::Update(
            [
                'TimeToSend' => $newsletter['SendDatetime'],
                'StatusEnum' => TransactionEmails::STATUS_PREGENERATED,
            ],
            [
                'RelSubscriberID' => $subscriber['SubscriberID'],
                'RelAutoResponderID' => $newsletter['CampaignID'],
                'RelOwnerUserID' => $newsletter['RelOwnerUserID'],
                'ReferenceID' => $subscriber['ReferenceID'],
                'StatusEnum' => TransactionEmails::STATUS_SENDING,
            ],
            NewsletterQueue::TABLE_NAME
        );
        if ($affectedRows <= 0) {
            watchdog(
                'pre-generation',
                'Could not mark queue item as pre-generated. It probably was changed by a parallel process ',
                ['@subscriber' => print_r($subscriber, true), '@newsletter' => print_r($newsletter, true)],
                WATCHDOG_WARNING
            );
        }
    }

    /**
     * @param $StatusReason
     * @param $CampaignID
     * @param int $SubscriberID
     *
     * @param $ReferenceID
     * @param int $CurrentState
     *
     * @return bool return true if an automation should continue
     */
    public static function SetStatusFailed(
        $StatusReason,
        $CampaignID,
        $UserID,
        $SubscriberID,
        $ReferenceID,
        $tableName,
        $CurrentState = 0
    ) {
        $values = [
            'TimeToSend' => time(), // time that shows up in history
            'StatusEnum' => TransactionEmails::STATUS_FAILED,
            'StatusReason' => $StatusReason
        ];
        if ($CurrentState > 0) {
            $values['StateID'] = $CurrentState;
        }

        TransactionEmails::Update(
            $values,
            [
                'RelOwnerUserID' => $UserID,
                'RelSubscriberID' => $SubscriberID,
                'RelAutoResponderID' => $CampaignID,
                'ReferenceID' => $ReferenceID,
            ],
            $tableName
        );

        return true;
    }

    /**
     * @param CampaignArray $campaign
     * @param QueueItemArray $queueEntry
     * @param string $queueTable
     * @param int $state
     * @return void
     * @throws Exception
     */
    public static function handleMissingSubscriber(
        array $campaign,
        array $queueEntry,
        string $queueTable,
        int $state = 0
    ): void
    {
        TransactionEmails::SetStatusFailed(
            TRANSACTION_REASON_SUBSCRIBER_NOT_FOUND,
            $campaign['CampaignID'],
            $campaign['RelOwnerUserID'],
            (int)$queueEntry['RelSubscriberID'],
            $queueEntry['ReferenceID'],
            $queueTable,
            $state
        );
        CampaignsStats::increment(
            ['TotalFailed'],
            $campaign['CampaignID'],
            $campaign['RelOwnerUserID']
        );
        watchdog(
            'queue_missing_subscriber',
            'Subscriber !subscriberID not found. Set status of queue item to failed',
            [
                '!subscriberID' => $queueEntry['RelSubscriberID'],
                '!userID' => $campaign['RelOwnerUserID'],
                '!campaignID' => $campaign['CampaignID'],

            ],
            WATCHDOG_WARNING
        );
    }

    /**
     * @param $StatusReason
     * @param array $ArrayAutoResponder
     * @param int $SubscriberID
     *
     * @param $ReferenceID
     * @param int $CurrentState
     * @param bool $transfered
     *
     * @return bool return true if an automation should continue
     */
    public static function SendFailed(
        $StatusReason,
        $ArrayAutoResponder,
        $SubscriberID,
        $ReferenceID,
        $CurrentState = 0,
        $transfered = false
    ) {
        if (Campaigns::IsProcessflow($ArrayAutoResponder['AutoResponderTriggerTypeEnum'])) {
            if (
                in_array($StatusReason, [
                    // these reasons can NOT be ignored in automations:
                    TRANSACTION_REASON_OWNER_NOT_FOUND,
                    TRANSACTION_REASON_CAMPAIGN_NOT_FOUND,
                    TRANSACTION_REASON_SUBSCRIBER_NOT_FOUND,
                    // these reasons are failed creations:
                    TRANSACTION_REASON_TIMETOSEND_IS_PAST,
                ])
            ) {
                TransactionEmails::RunFailed(
                    $ArrayAutoResponder,
                    $SubscriberID,
                    $ReferenceID,
                    $CurrentState,
                    $StatusReason
                );
                return false;
            }
            return true;
        }

        self::beginTransaction();

        $tableName = Campaigns::GetQueueTableNameByType($ArrayAutoResponder['AutoResponderTriggerTypeEnum']);
        static::SetStatusFailed(
            $StatusReason,
            $ArrayAutoResponder['CampaignID'],
            $ArrayAutoResponder['RelOwnerUserID'],
            $SubscriberID,
            $ReferenceID,
            $tableName,
            $CurrentState
        );

        if ($transfered && !CampaignsNewsletter::isPreGenerating($ArrayAutoResponder)) {
            // these are transferred bounces, which count as original bounces for the campaign -
            // they need to look like they are sent, but bounced (see SendSucceeded)

            // Tag Subsciber
            if ($SubscriberID) {
                $tag = Tag::RetrieveTagOfEntity(
                    $ArrayAutoResponder['RelOwnerUserID'],
                    Tag::CATEGORY_SENT,
                    $ArrayAutoResponder['CampaignID']
                );
                if ($tag) {
                    Subscribers::TagSubscriber(
                        $ArrayAutoResponder['RelOwnerUserID'],
                        $SubscriberID,
                        $tag['TagID'],
                        $ReferenceID,
                        true
                    );
                    TransactionEmails::RegisterAutomations(
                        $ArrayAutoResponder['RelOwnerUserID'],
                        $SubscriberID,
                        $ReferenceID
                    );
                }
            }

            // update campaign stats
            CampaignsStats::increment(
                ['TotalSent'],
                $ArrayAutoResponder['CampaignID'],
                $ArrayAutoResponder['RelOwnerUserID']
            );
        } else {
            // all other reasons (like spambounce) are local, so they just count as failed

            // update campaign stats
            CampaignsStats::increment(
                ['TotalFailed'],
                $ArrayAutoResponder['CampaignID'],
                $ArrayAutoResponder['RelOwnerUserID']
            );
        }

        self::commitTransaction();

        return true;
    }

    public static function CreateFailed($ArrayAutoResponder, $SubscriberID, $ReferenceID, $TimeToSend, $StatusReason)
    {
        // start transaction to avoid race conditions and multiple inserts of same queue entry
        self::beginTransaction();

        $tableName = Campaigns::GetQueueTableNameByType($ArrayAutoResponder['AutoResponderTriggerTypeEnum']);

        // check for exiting queue entry
        $TransactionalQueueEntry = kt_fetch_array(
            db_query(
                "SELECT StatusEnum FROM {{$tableName}} " .
                " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelAutoResponderID = :RelAutoResponderID AND ReferenceID = :ReferenceID",
                [
                    ':RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                    ':RelSubscriberID' => $SubscriberID,
                    ':RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                    ':ReferenceID' => $ReferenceID,
                ]
            )
        );

        if (empty($TransactionalQueueEntry)) {
            // insert as failed
            $ArrayFieldnValues = [
                'RelOwnerUserID' => $ArrayAutoResponder['RelOwnerUserID'],
                'RelSubscriberID' => $SubscriberID,
                'ReferenceID' => $ReferenceID,
                'TimeToSend' => time(), // time that shows up in history
                'RelAutoResponderID' => $ArrayAutoResponder['CampaignID'],
                'StatusEnum' => TransactionEmails::STATUS_FAILED,
                'StatusReason' => $StatusReason,
            ];
            try {
                TransactionEmails::InsertWithMod($ArrayFieldnValues, $tableName);
            } catch (PDOException $e) {
                // RACE
                // if the insert fails, we have a another job that performs the same task
                // as we lost the race, we shall just quit
                self::commitTransaction();
                return;
            }

            CampaignsStats::increment(
                ['TotalFailed'],
                $ArrayAutoResponder['CampaignID'],
                $ArrayAutoResponder['RelOwnerUserID']
            );
        } else {
            if (
                in_array(
                    $TransactionalQueueEntry['StatusEnum'],
                    [TransactionEmails::STATUS_PENDING, TransactionEmails::STATUS_PENDING_AFTER]
                )
            ) {
                // update pending
                TransactionEmails::SendFailed(
                    $StatusReason,
                    $ArrayAutoResponder,
                    $SubscriberID,
                    $ReferenceID,
                    0,
                    false
                );
            } else {
                // entry exists and is not pending -> do nothing
            }
        }

        self::commitTransaction();
    }

    /**
     * Remove all queue entries of subscriber with selected queue status.
     * Used to stop autoresponders in case of unsubscribe and re-subscribe/unbounce.
     *
     * @param $Subscription
     * @param int $StatusEnum (pending or failed)
     */
    public static function RemoveFromQueueByStatus($Subscription, $StatusEnum = TransactionEmails::STATUS_PENDING)
    {
        // There might be zero, one or more references to o contact info, so we need to stop all (inner join subscription_reference)
        $query = "SELECT q.* FROM {:tableName} q " .
            " INNER JOIN {subscription_reference} r ON q.RelOwnerUserID = r.RelOwnerUserID AND q.RelSubscriberID = r.RelSubscriberID AND q.ReferenceID = r.ReferenceID AND r.ContactInfo = :ContactInfo AND r.SubscriptionType = :SubscriptionType " .
            " INNER JOIN {campaigns} c ON q.RelOwnerUserID = c.RelOwnerUserID AND q.RelAutoResponderID = c.CampaignID " .
            " WHERE q.RelOwnerUserID = :RelOwnerUserID AND q.RelSubscriberID = :RelSubscriberID  " .
            " AND q.StatusEnum = :StatusEnum AND c.AutoResponderTriggerTypeEnum IN (:AutoResponderTriggerTypeEnum)";

        // for autoresponder
        $triggerTypesEmail = [
            Campaigns::TRIGGER_TYPE_SUBSCRIPTION,
            Campaigns::TRIGGER_TYPE_DATETIME,
            Campaigns::TRIGGER_TYPE_BIRTHDAY,
        ];

        $triggerTypesSMS = [
            Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION,
            Campaigns::TRIGGER_TYPE_SMS_DATETIME,
            Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY,
        ];

        $params = [
            ':RelOwnerUserID' => $Subscription['RelOwnerUserID'],
            ':RelSubscriberID' => $Subscription['SubscriberID'],
            ':ContactInfo' => $Subscription['ContactInfo'],
            ':SubscriptionType' => $Subscription['SubscriptionType'],
            ':StatusEnum' => $StatusEnum,
            ':AutoResponderTriggerTypeEnum' => $Subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_SMS ? $triggerTypesSMS : $triggerTypesEmail,
        ];

        // Since drupal does not allow table name substitution through parameters,
        // we do it the classic way.
        $query = str_replace(':tableName', AutoresponderQueue::TABLE_NAME, $query);

        $result = db_query($query, $params);
        while ($Entry = kt_fetch_array($result)) {
            AutoresponderQueue::RemoveFromQueue($Entry);
        }

        // for newsletter_queue
        $triggerTypesEmail = [
            Campaigns::TRIGGER_TYPE_CAMPAIGN,
        ];

        $triggerTypesSMS = [
            Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN,
        ];

        $params[':AutoResponderTriggerTypeEnum'] = $Subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_SMS ? $triggerTypesSMS : $triggerTypesEmail;

        // Since drupal does not allow table name substitution through parameters,
        // we do it the classic way.
        $query = str_replace(':tableName', NewsletterQueue::TABLE_NAME, $query);

        $result = db_query($query, $params);
        while ($Entry = kt_fetch_array($result)) {
            NewsletterQueue::RemoveFromQueue($Entry);
        }
    }

    /**
     * multiple send: untag start tag and remove queue entry
     *
     * @param array $ArrayCampaign
     * @param int $SubscriberID
     * @param $ReferenceID
     */
    public static function RemoveFromQueueForMultipleSend($ArrayCampaign, $SubscriberID, $ReferenceID)
    {
        Subscribers::UntagSubscriber(
            $ArrayCampaign['RelOwnerUserID'],
            $SubscriberID,
            $ReferenceID,
            $ArrayCampaign['AutomationStartedSmartTagID']
        );
        TransactionalQueue::RemoveFromQueue([
            'RelSubscriberID' => $SubscriberID,
            'RelAutoResponderID' => $ArrayCampaign['CampaignID'],
            'RelOwnerUserID' => $ArrayCampaign['RelOwnerUserID'],
            'ReferenceID' => $ReferenceID,
        ]);

        //remove goal history entries for this campaign and subscriber
        CampaignsProcessFlow::ClearGoalsHistoryOfSubscriber(
            $ArrayCampaign['RelOwnerUserID'],
            $ArrayCampaign['CampaignID'],
            $SubscriberID
        );
    }

    /**
     * processflow has been stopped, so disable all queue entries to remove them from PENDING query
     * sets STATUS_PENDING to STATUS_STOPPED and STATUS_PENDING_AFTER to STATUS_STOPPED_AFTER
     * Note: these entries are re-enabled by RunStarted
     *
     * @param $UserID
     * @param $CampaignID
     */
    public static function DisableProcessflowInQueue($UserID, $CampaignID)
    {
        if (($CampaignID > 0) && ($UserID > 0)) {
            //@note: Multivalue - stop all campaign runs
            db_update(TransactionalQueue::TABLE_NAME)
                ->fields([
                    'StatusEnum' => TransactionEmails::STATUS_STOPPED,
                ])
                ->condition('RelOwnerUserID', $UserID)
                ->condition('RelAutoResponderID', $CampaignID)
                ->condition('StatusEnum', TransactionEmails::STATUS_PENDING)
                ->execute();

            db_update(TransactionalQueue::TABLE_NAME)
                ->fields([
                    'StatusEnum' => TransactionEmails::STATUS_STOPPED_AFTER,
                ])
                ->condition('RelOwnerUserID', $UserID)
                ->condition('RelAutoResponderID', $CampaignID)
                ->condition('StatusEnum', TransactionEmails::STATUS_PENDING_AFTER)
                ->execute();
        }
    }

    public static function DeleteCampaignQueueEntries($UserID, $AutoResponder, $AutoResponderDeleted = true)
    {
        $AutoResponderID = $AutoResponder['CampaignID'];

        $tableName = Campaigns::GetQueueTableNameByType($AutoResponder['AutoResponderTriggerTypeEnum']);

        if (($AutoResponderID > 0) && ($UserID > 0)) {
            if ($AutoResponderDeleted) {
                kt_delete_rows([
                    'RelAutoResponderID' => $AutoResponderID,
                    'RelOwnerUserID' => $UserID,
                ], "{{$tableName}}");
            } else {
                // Autoresponder is stopped, we need 'Sent' and 'Failed' on re-send
                kt_delete_rows([
                    'RelAutoResponderID' => $AutoResponderID,
                    'RelOwnerUserID' => $UserID,
                    'StatusEnum' => TransactionEmails::STATUS_PENDING,
                ], "{{$tableName}}");
            }
        }
    }

    public static function DeleteTransactionEmailsOfUser($UserID)
    {
        if ($UserID > 0) {
            kt_delete_rows(['RelOwnerUserID' => $UserID], "{" . TransactionalQueue::TABLE_NAME . "}");
            kt_delete_rows(['RelOwnerUserID' => $UserID], "{" . AutoresponderQueue::TABLE_NAME . "}");
            kt_delete_rows(['RelOwnerUserID' => $UserID], "{" . NewsletterQueue::TABLE_NAME . "}");
        }
    }

    /////////////////////////// transactional_send

    /*
     * Campaign cache for transactional send
     *
     * @return CampaignArray
     */
    public static function GetCachedCampaign($UserID, $CampaignID, $reset = false)
    {
        static $CachedResponders = [];
        if ($reset) {
            $CachedResponders = [];
            return [];
        }

        if (!isset($CachedResponders[$CampaignID])) {
            // read campaign
            $ObjectCamnpaign = Campaigns::FromID($UserID, $CampaignID);
            $ArrayAutoResponder = empty($ObjectCamnpaign) ? [] : $ObjectCamnpaign->GetData();
            // cache result
            $CachedResponders[$CampaignID] = $ArrayAutoResponder;
        } else {
            // get from cache
            $ArrayAutoResponder = $CachedResponders[$CampaignID];
        }

        return $ArrayAutoResponder;
    }

    /*
     * Email cache for transactional send
     */
    public static function GetCachedEmail($UserID, $EmailID, $reset = false)
    {
        static $CachedEmail = [];
        if ($reset) {
            $CachedEmail = [];
            return false;
        }

        if (!isset($CachedEmail[$EmailID])) {
            $CachedEmail[$EmailID] = Emails::RetrieveEmailByID($UserID, $EmailID);
        }
        return $CachedEmail[$EmailID];
    }

    /*
     * Domain cache for transactional send
     */
    public static function GetCachedSenderDomain($ArrayUser, $reset = false)
    {
        static $CachedSenderDomains = [];
        if ($reset) {
            $CachedSenderDomains = [];
            return false;
        }

        if (!isset($CachedSenderDomains[$ArrayUser['uid']])) {
            $SenderDomains = DomainSet::GetValidSenderDomains($ArrayUser);
            $CachedSenderDomains[$ArrayUser['uid']] = $SenderDomains;
        }
        return $CachedSenderDomains[$ArrayUser['uid']];
    }

    /*
     * Subscriber cache for register/run transactional
     */
    public static function GetCachedFullSubscriber($UserID, $SubscriberID, $ReferenceID, $reset = false)
    {
        static $CachedFullSubscriber = [];

        $hash = $SubscriberID . '#' . $ReferenceID;
        if ($reset) {
            TransactionEmails::GetCachedTagging($UserID, $SubscriberID, $ReferenceID, false, true);
            CustomFields::GetCustomFieldData($UserID, $SubscriberID, [], $ReferenceID, '', true);
            if (empty($SubscriberID)) {
                // all
                $CachedFullSubscriber = [];
                return false;
            } else {
                // update single subscriber
                unset($CachedFullSubscriber[$hash]);
            }
        }

        if (!isset($CachedFullSubscriber[$hash])) {
            $CachedFullSubscriber[$hash] = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
        }
        return $CachedFullSubscriber[$hash];
    }

    /*
     * Tagging cache for register/run transactional
     */
    public static function GetCachedTagging($UserID, $SubscriberID, $ReferenceID, $TagIDsOnly = true, $reset = false)
    {
        static $CachedTagging = [];
        if ($reset) {
            $CachedTagging = [];
            return [];
        }

        $hash = $SubscriberID . '#' . $ReferenceID;
        if (!isset($CachedTagging[$hash])) {
            // we only cache one subscriber tagging to avoid a memory overflow
            // reset all, if subscriber changes
            $CachedTagging = [];

            // get taggings - same as RetrieveTaggingsOfSubscriber, but join tag.Category
            $ArrayTaggings = [];
            $result = db_query(
                "SELECT tt.*, tt.RelSubscriberID AS SubscriberID, t.Category FROM {tagging} tt " .
                " INNER JOIN {tag} t ON tt.RelOwnerUserID = t.RelOwnerUserID AND t.TagID = tt.RelTagID " .
                " WHERE tt.RelOwnerUserID = :RelOwnerUserID AND tt.RelSubscriberID = :RelSubscriberID " .
                " AND (t.MultiValue = 0 OR tt.ReferenceID = :ReferenceID)",
                [
                    ':RelOwnerUserID' => $UserID,
                    ':RelSubscriberID' => $SubscriberID,
                    ':ReferenceID' => $ReferenceID,
                ]
            );
            while ($tagging = kt_fetch_array($result)) {
                // single value taggings have all references, so return the callers one (even they are technically stored with ref 0)
                $tagging['ReferenceID'] = $ReferenceID;
                $ArrayTaggings[$tagging['RelTagID']] = $tagging;
            }
            $CachedTagging[$hash] = $ArrayTaggings;
        }

        if ($TagIDsOnly) {
            $ArrayTaggings = array_keys($CachedTagging[$hash]);
        } else {
            $ArrayTaggings = $CachedTagging[$hash];
        }

        return $ArrayTaggings;
    }

    /*
     * CustomField cache for register/run transactional
     */
    public static function GetCachedCustomFields($UserID, $reset = false)
    {
        static $CachedFields;

        if ($reset) {
            unset($CachedFields[$UserID]);
            return [];
        }

        if (!isset($CachedFields[$UserID])) {
            $CachedFields[$UserID] = CustomFields::RetrieveCustomFields(
                $UserID,
                true,
                array('CustomFieldID' => 'ASC'),
                false,
                true
            );
        }

        return $CachedFields[$UserID];
    }

    /*
     * Send copy to verifications
     */
    public static function SendCopyToVerifications($ArrayEachEmail, $UserID, $ArrayEmail)
    {
        $TotalSent = db_query(
            "SELECT TotalSent FROM {campaign_stats} WHERE RelOwnerUserID = :RelOwnerUserID AND RelCampaignID = :CampaignID",
            [
                ':RelOwnerUserID' => $UserID,
                ':CampaignID' => $ArrayEachEmail['RelAutoResponderID'],
            ]
        )->fetchField();
        if ($TotalSent == 200) {
            $Message = "<p>" . t("Email name: @name", ['@name' => $ArrayEmail['EmailName']]) . "</p>";
            $Message .= "<p>" . t(
                "Link to email"
            ) . ": " . APP_URL . "email/$UserID/preview/{$ArrayEmail['EmailID']}/campaign/{$ArrayEachEmail['RelAutoResponderID']} </p><hr />";
            $Message .= "<p>" . t("Plain") . ":</p><hr />";
            $Message .= str_replace("\n", "<br />", $ArrayEmail['PlainContent']) . "<hr />";
            $Message .= "<p>" . t("HTML") . ":</p><hr />";
            $Message .= $ArrayEmail['HTMLContent'];

            Core::SendNotificationEmail(COREAPI_NOTIFY_EMAIL_BCC_EMAIL_REVISION, $Message, $UserID);
        }
    }

    public static function RunNextTransactional(
        $ArrayEachEmail,
        $account,
        $ArrayCampaign,
        $CurrentState,
        $NextState,
        $FullSubscriber,
        $Tagging,
        $TimeToSend = 0,
        $IsNextNo = false
    ) {
        if (empty($CurrentState)) {
            // unrecoverable error
            TransactionEmails::RunFailed(
                $ArrayCampaign,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE
            );
            Errors::unexpected('RunTransactional: !reason', [
                '!state' => $CurrentState,
                '!next' => $NextState,
                '!reason' => TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE,
                '!uid' => $account->uid,
                '!campaignid' => $ArrayCampaign['CampaignID'],
                '!campaign' => $ArrayCampaign,
                '!subscriber' => $FullSubscriber,
            ]);
            return;
        }

        if (empty($NextState)) {
            // open end found -> PAUSE until an action is added behind the current state
            TransactionEmails::Update(
                [
                    'TimeToSend' => empty($TimeToSend) ? time() : $TimeToSend,
                    'StatusEnum' => $IsNextNo ? TransactionEmails::STATUS_PAUSED_AFTER_NO : TransactionEmails::STATUS_PAUSED_AFTER,
                    'StateID' => $CurrentState,
                ],
                [
                    'RelSubscriberID' => $FullSubscriber['SubscriberID'],
                    'RelAutoResponderID' => $ArrayCampaign['CampaignID'],
                    'RelOwnerUserID' => $ArrayCampaign['RelOwnerUserID'],
                    'ReferenceID' => $ArrayEachEmail['ReferenceID'],
                ],
                TransactionalQueue::TABLE_NAME
            );


            return;
        }

        if ($TimeToSend > 0) {
            // run next state after time delay
            db_update(TransactionalQueue::TABLE_NAME)
                ->fields([
                    'TimeToSend' => $TimeToSend,
                    // wait state is pushed to the queue with dedicated status
                    'StatusEnum' => ($CurrentState == $NextState) ? TransactionEmails::STATUS_PENDING_AFTER : TransactionEmails::STATUS_PENDING,
                    'StateID' => $NextState,
                    'ModOfCampaignID' => static::CalcShard(
                        $ArrayCampaign['RelOwnerUserID'],
                        $ArrayCampaign['CampaignID']
                    ),
                ])
                ->condition('RelOwnerUserID', $ArrayCampaign['RelOwnerUserID'])
                ->condition('RelSubscriberID', $FullSubscriber['SubscriberID'])
                ->condition('ReferenceID', $ArrayEachEmail['ReferenceID'])
                ->condition('RelAutoResponderID', $ArrayCampaign['CampaignID'])
                ->execute();
        } else {
            // run next state immediately

            // subscriber state may have changed in last action, so update
            $FullSubscriber = TransactionEmails::GetCachedFullSubscriber(
                $ArrayEachEmail['RelOwnerUserID'],
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                true
            );
            TransactionEmails::RunTransactional(
                $ArrayEachEmail,
                $account,
                $ArrayCampaign,
                $NextState,
                $FullSubscriber,
                $Tagging
            );
        }
    }

    /**
     * Run the process until the next "processable" action
     */
    public static function RunTransactional(
        $ArrayEachEmail,
        $account,
        $ArrayCampaign,
        $CurrentState,
        $FullSubscriber,
        $Tagging,
        $reset = false
    ) {
        static $visited = [];
        if ($reset) {
            // reset visited if not called recursively (see transactional_send vs RunNextTransactional)
            $visited = [];
        }

        if (empty($CurrentState)) {
            // unrecoverable error
            TransactionEmails::RunFailed(
                $ArrayCampaign,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE
            );
            Errors::unexpected('RunTransactional: !reason', [
                '!state' => $CurrentState,
                '!reason' => TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE,
                '!uid' => $account->uid,
                '!campaignid' => $ArrayCampaign['CampaignID'],
                '!campaign' => $ArrayCampaign,
                '!subscriber' => $FullSubscriber,
            ]);
            return;
        }

        if (empty($FullSubscriber)) {
            static::handleMissingSubscriber(
                $ArrayCampaign,
                $ArrayEachEmail,
                TransactionalQueue::TABLE_NAME,
                $CurrentState
            );
            return;
        }

        // get state
        $state = CampaignsProcessFlow::FindState($ArrayCampaign, $CurrentState);
        if (empty($state)) {
            // state not found
            TransactionEmails::RunFailed(
                $ArrayCampaign,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE
            );
            return;
        }

        $nextPossibleTimeGoal = 0;
        //check if the campaign has goals, the subscriber hasn't reached a goal yet and the goal condtion is met
        if (
            !in_array(
                $state['type'],
                [
                    CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_START,
                    CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_RESTART
                ]
            )
        ) {
            $GoalState = self::checkAndProcessGoal(
                $ArrayEachEmail,
                $ArrayCampaign,
                $CurrentState,
                $nextPossibleTimeGoal
            );
            if (!empty($GoalState)) {
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $GoalState['next'],
                    $FullSubscriber,
                    $Tagging
                );
                return;
            }
        }

        // special case: decision
        if ($state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DECISION) {
            $failedSegments = [];
            $nextYes = CampaignsProcessFlow::CheckDecision(
                $state,
                $account->uid,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $failedSegments
            );
            $next = $nextYes ? $state['next'] : $state['nextNo'];
            // this is an endless loop only, if we came here before and went the same path
            if (in_array($CurrentState, $visited) && in_array($next, $visited)) {
                // endless loop, check if condition only fails because of temporal conditions,
                // if yes create temporal queue entry
                if (
                    (
                    $nextPossibleTime = TransactionEmails::calcNextPossibleMatch(
                        $failedSegments,
                        [
                            'UserID' => $ArrayCampaign['RelOwnerUserID'],
                            'SubscriberID' => $FullSubscriber['SubscriberID'],
                            'ReferenceID' => $ArrayEachEmail['ReferenceID']
                        ]
                    )
                    ) > 0 || $nextPossibleTimeGoal > 0
                ) {
                    $nextPossibleTime = ($nextPossibleTime == 0)
                        ? $nextPossibleTimeGoal
                        : (($nextPossibleTimeGoal == 0)
                            ? $nextPossibleTime : min(
                                $nextPossibleTime,
                                $nextPossibleTimeGoal
                            ));
                    TransactionEmails::createTemporalDBEntry(
                        $ArrayCampaign['RelOwnerUserID'],
                        $ArrayCampaign['CampaignID'],
                        $FullSubscriber['SubscriberID'],
                        $ArrayEachEmail['ReferenceID'],
                        $nextPossibleTime,
                        $state['id']
                    );
                } else {
                    TransactionEmails::RunPaused(
                        $FullSubscriber['SubscriberID'],
                        $ArrayEachEmail['ReferenceID'],
                        $ArrayCampaign,
                        $CurrentState
                    );
                }
                return;
            }
            $visited[] = $CurrentState;

            // run into next
            TransactionEmails::RunNextTransactional(
                $ArrayEachEmail,
                $account,
                $ArrayCampaign,
                $CurrentState,
                $next,
                $FullSubscriber,
                $Tagging,
                0,
                !$nextYes
            );
            return;
        }

        // detect endless loop
        if (in_array($CurrentState, $visited)) {
            TransactionEmails::RunPaused(
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $ArrayCampaign,
                $CurrentState
            );
            return;
        }
        $visited[] = $CurrentState;

        // execute actions
        switch ($state['type']) {
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_START:
                $timeToSend = time();
                [$nextStateId, , $failedSegments] = CampaignsProcessFlow::CheckStartState(
                    $ArrayCampaign,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    true
                );

                $nextPossibleTime = self::calcNextPossibleMatch(
                    $failedSegments,
                    [
                        'UserID' => $ArrayCampaign['RelOwnerUserID'],
                        'SubscriberID' => $FullSubscriber['SubscriberID'],
                        'ReferenceID' => $ArrayEachEmail['ReferenceID']
                    ]
                );

                if (!empty($nextStateId)) {
                    // if restart rate limit is reached, set this on pending until possible again
                    if (
                        self::applyRateLimit(
                            $ArrayCampaign['RelOwnerUserID'],
                            $FullSubscriber['SubscriberID'],
                            $ArrayEachEmail['ReferenceID'],
                            $ArrayCampaign['CampaignID'],
                            $nextStateId
                        )
                    ) {
                        break;
                    }
                    $table = '{' . TransactionalQueue::TABLE_NAME . '}';
                    $sql = "UPDATE $table SET TimeToSend = :TimeToSend, StatusEnum = :StatusEnum, StateID = :StateID " .
                        "WHERE RelOwnerUserID = :RelOwnerUserID " .
                        "AND RelSubscriberID = :RelSubscriberID " .
                        "AND ReferenceID = :ReferenceID " .
                        "AND RelAutoResponderID = :RelAutoResponderID " .
                        "AND GoalReached = 0";

                    db_query(
                        $sql,
                        [
                            ':TimeToSend' => $timeToSend, // time, when the restart trigger check starts
                            ':StatusEnum' => TransactionEmails::STATUS_PENDING,
                            ':StateID' => $nextStateId,
                            ':RelSubscriberID' => $FullSubscriber['SubscriberID'],
                            ':RelAutoResponderID' => $ArrayCampaign['CampaignID'],
                            ':RelOwnerUserID' => $ArrayCampaign['RelOwnerUserID'],
                            ':ReferenceID' => $ArrayEachEmail['ReferenceID'],
                        ]
                    );
                    if (
                        !CampaignsProcessFlow::hasStartTag(
                            $ArrayCampaign,
                            $FullSubscriber['SubscriberID'],
                            $ArrayEachEmail['ReferenceID']
                        )
                    ) {
                        self::campaignStartedAfter(
                            $ArrayCampaign['RelOwnerUserID'],
                            $ArrayCampaign['CampaignID'],
                            $FullSubscriber['SubscriberID'],
                            $ArrayEachEmail['ReferenceID']
                        );
                    }
                } elseif ($nextPossibleTime > 0) {
                    $table = '{' . TransactionalQueue::TABLE_NAME . '}';
                    $sql = "UPDATE $table SET TimeToSend = :TimeToSend, StatusEnum = :StatusEnum " .
                        "WHERE RelOwnerUserID = :RelOwnerUserID " .
                        "AND RelSubscriberID = :RelSubscriberID " .
                        "AND ReferenceID = :ReferenceID " .
                        "AND RelAutoResponderID = :RelAutoResponderID " .
                        "AND GoalReached = 0";

                    db_query($sql, [
                        ':TimeToSend' => $nextPossibleTime, // time, when the restart trigger check starts
                        ':StatusEnum' => TransactionEmails::STATUS_WAITING_FOR_TEMPORAL_CONDITION,
                        ':RelSubscriberID' => $FullSubscriber['SubscriberID'],
                        ':RelAutoResponderID' => $ArrayCampaign['CampaignID'],
                        ':RelOwnerUserID' => $ArrayCampaign['RelOwnerUserID'],
                        ':ReferenceID' => $ArrayEachEmail['ReferenceID']
                    ]);
                } elseif (
                    CampaignsProcessFlow::hasStartTag(
                        $ArrayCampaign,
                        $FullSubscriber['SubscriberID'],
                        $ArrayEachEmail['ReferenceID']
                    )
                ) {
                    // subscriber does not match conditions, but is in campaign already
                    TransactionEmails::RunPaused(
                        $FullSubscriber['SubscriberID'],
                        $ArrayEachEmail['ReferenceID'],
                        $ArrayCampaign,
                        $CurrentState
                    );
                } else {
                    // subscriber is not in campaign and does not match conditins anymore
                    TransactionalQueue::RemoveFromQueue($ArrayEachEmail);
                }
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL:
                if (empty($state['ignoreSubscriberStatus'])) {
                    $Subscription = Subscriber::FullSubscriberToEmailSubscription($FullSubscriber);
                    [$success, $reason, $transfered] = BounceEngine::CheckValidSubscriber(
                        (array)$account,
                        $Subscription,
                        $state['emailID'],
                        $ArrayCampaign['CampaignID']
                    );
                    if ($success == false) {
                        TransactionEmails::RunPaused(
                            $FullSubscriber['SubscriberID'],
                            $ArrayEachEmail['ReferenceID'],
                            $ArrayCampaign,
                            $CurrentState
                        );
                        return;
                    }
                }
                if (
                    TransactionEmails::SendTransactionalEmail(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $FullSubscriber,
                        $Tagging,
                        $state['emailID'],
                        $CurrentState
                    )
                ) {
                    // no unrecoverable error, so continue
                    TransactionEmails::RunNextTransactional(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $CurrentState,
                        $state['next'],
                        $FullSubscriber,
                        $Tagging
                    );
                };
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDSMS:
                if (empty($state['ignoreSubscriberStatus'])) {
                    $Subscription = Subscriber::FullSubscriberToSMSSubscription($FullSubscriber);
                    [$success, $reason, $transfered] = BounceEngine::CheckValidSMSSubscriber(
                        (array)$account,
                        $Subscription,
                        $ArrayCampaign['CampaignID']
                    );
                    if ($success == false) {
                        TransactionEmails::RunPaused(
                            $FullSubscriber['SubscriberID'],
                            $ArrayEachEmail['ReferenceID'],
                            $ArrayCampaign,
                            $CurrentState
                        );
                        return;
                    }
                }
                if (
                    TransactionEmails::SendTransactionalSMS(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $FullSubscriber,
                        $Tagging,
                        $state['emailID'],
                        $CurrentState
                    )
                ) {
                    // no unrecoverable error, so continue
                    TransactionEmails::RunNextTransactional(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $CurrentState,
                        $state['next'],
                        $FullSubscriber,
                        $Tagging
                    );
                };
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL:
                if (
                    TransactionEmails::SendTransactionalNotifyEmail(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $FullSubscriber,
                        $Tagging,
                        $state['emailID'],
                        $state['notifyReceiverEmail'],
                        $CurrentState
                    )
                ) {
                    // no unrecoverable error, so continue
                    TransactionEmails::RunNextTransactional(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $CurrentState,
                        $state['next'],
                        $FullSubscriber,
                        $Tagging
                    );
                };
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS:
                if (
                    TransactionEmails::SendTransactionalNotifySMS(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $FullSubscriber,
                        $Tagging,
                        $state['emailID'],
                        $state['notifyReceiverEmail'],
                        $CurrentState
                    )
                ) {
                    // no unrecoverable error, so continue
                    TransactionEmails::RunNextTransactional(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $CurrentState,
                        $state['next'],
                        $FullSubscriber,
                        $Tagging
                    );
                };
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_OUTBOUND:
                /** @var ToolOutbound $ObjectTool */
                $ObjectTool = ToolOutbound::FromID($account->uid, $state['outboundID']);
                if ($ObjectTool) {
                    $ObjectTool->TriggerOutboundEvent($FullSubscriber['SubscriberID'], $ArrayEachEmail['ReferenceID']);
                }
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_TAGGING:
                Subscribers::TagSubscriber(
                    $account->uid,
                    $FullSubscriber['SubscriberID'],
                    $state['tagID'],
                    $ArrayEachEmail['ReferenceID'],
                    true
                );
                TransactionEmails::RegisterAutomations(
                    $account->uid,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID']
                );
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNTAGGING:
                Subscribers::UntagSubscriber(
                    $account->uid,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $state['tagID']
                );
                TransactionEmails::RegisterAutomations(
                    $account->uid,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID']
                );
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SETFIELD:
                if (!empty($state['customFieldID'])) {
                    $ArrayCustomFields = TransactionEmails::GetCachedCustomFields($account->uid);
                    if (!empty($ArrayCustomFields[$state['customFieldID']])) {
                        $UpdateValue = CustomFields::CalculateCustomFieldData(
                            $account,
                            $FullSubscriber['SubscriberID'],
                            $ArrayEachEmail['ReferenceID'],
                            $ArrayCustomFields[$state['customFieldID']],
                            $state['customFieldOp'],
                            $state['customFieldValue'],
                            $state['customFieldValue2'],
                            $state['delayDayOfWeek'],
                            time()
                        );
                        if ($UpdateValue !== false) {
                            $subscriberStateChanged = CustomFields::UpdateCustomFieldData(
                                $account->uid,
                                $FullSubscriber['SubscriberID'],
                                $ArrayEachEmail['ReferenceID'],
                                $ArrayCustomFields[$state['customFieldID']],
                                $UpdateValue
                            );
                            if ($subscriberStateChanged) {
                                TransactionEmails::RegisterAutomations(
                                    $account->uid,
                                    $FullSubscriber['SubscriberID'],
                                    $ArrayEachEmail['ReferenceID']
                                );
                            }
                        }
                    }
                }
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT:
                if ($ArrayEachEmail['StatusEnum'] == TransactionEmails::STATUS_PENDING_AFTER) {
                    // change status to pending so status STATUS_PENDING_AFTER is reset
                    $ArrayEachEmail['StatusEnum'] = TransactionEmails::STATUS_PENDING;
                    // process state after the wait state immediately
                    TransactionEmails::RunNextTransactional(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $CurrentState,
                        $state['next'],
                        $FullSubscriber,
                        $Tagging
                    );
                    break;
                }
                if ($state['delayType'] == CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_LIMITER) {
                    // wait state contains a limiter
                    $result = TransactionEmails::CalcTransactionalLimiter(
                        $account->uid,
                        $ArrayCampaign['CampaignID'],
                        $state,
                        time()
                    );
                    if ($result[0]) {
                        //the limit has not been reached and a time to send ($result[1]) within the limiter period has been calculated
                        //set the status to STATUS_PENDING_AFTER
                        //the subscriber will wait at this state until the time to send has been reached and then proceed with the next state
                        TransactionEmails::RunNextTransactional(
                            $ArrayEachEmail,
                            $account,
                            $ArrayCampaign,
                            $CurrentState,
                            $CurrentState,
                            $FullSubscriber,
                            $Tagging,
                            $result[1]
                        );
                    } else {
                        //the limit for the current period has been reached
                        //the time to send will be set to the start of the next period ($result[1]) and the status will remain STATUS_PENDING
                        //the subscriber will wait at this state until the time to send has been reached and then the limiter will be checked again
                        TransactionEmails::RunJobFailed(
                            $FullSubscriber['SubscriberID'],
                            $ArrayEachEmail['ReferenceID'],
                            $ArrayCampaign,
                            $result[1]
                        );
                    }
                } else {
                    // simulate an autoresponder to use calculateSendTime
                    $TimeToSend = TransactionEmails::CalcTransactionalWait(
                        $state,
                        time(),
                        $FullSubscriber['SubscriberID'],
                        $ArrayEachEmail['ReferenceID'],
                        $account->uid
                    );
                    if ($TimeToSend <= 0) {
                        // invalid wait time: just go on
                        TransactionEmails::RunNextTransactional(
                            $ArrayEachEmail,
                            $account,
                            $ArrayCampaign,
                            $CurrentState,
                            $state['next'],
                            $FullSubscriber,
                            $Tagging
                        );
                    } else {
                        // continue after wait time at current wait state
                        TransactionEmails::RunNextTransactional(
                            $ArrayEachEmail,
                            $account,
                            $ArrayCampaign,
                            $CurrentState,
                            $CurrentState,
                            $FullSubscriber,
                            $Tagging,
                            $TimeToSend
                        );
                    }
                }
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNSUBSCRIBE:
                Subscribers::Unsubscribe($account->uid, $FullSubscriber['EmailAddress']);
                Subscribers::UnsubscribeSMSNumber($account->uid, $FullSubscriber['PhoneNumber']);
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_GOTO:
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STARTAUTOMATION:
                TransactionEmails::StartAutomation(
                    $account->uid,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $state['campaignID'],
                    true
                );
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STOPAUTOMATION:
                TransactionEmails::StopAutomation(
                    $account->uid,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $state['campaignID']
                );
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SPLITTEST:
                if (
                    TransactionEmails::ProcessSplittest(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $FullSubscriber,
                        $Tagging,
                        $state['splittestID'],
                        $CurrentState
                    )
                ) {
                    // no unrecoverable error, so continue
                    TransactionEmails::RunNextTransactional(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $CurrentState,
                        $state['next'],
                        $FullSubscriber,
                        $Tagging
                    );
                };
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD:
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_REMOVE:
                if ($state['type'] == CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD) {
                    $EventType = ToolFacebookAudience::EVENT_TYPE_ADD;
                } else {
                    $EventType = ToolFacebookAudience::EVENT_TYPE_REMOVE;
                }

                /** @var ToolFacebookAudience $ObjectAudience */
                $ObjectAudience = ToolFacebookAudience::FromID($account->uid, $state['audienceID']);

                if ($ObjectAudience) {
                    $AccessToken = $ObjectAudience->GetValidToken();

                    if ($AccessToken[0] || $AccessToken[1] != ToolFacebookAudience::ERROR_ACCESS_EXPIRED) {
                        //Note: GetValidToken() can also return ToolFacebookAudience::ERROR_RATE_LIMIT_REACHED,
                        //      but the limit could be reseted by the time the job is processed

                        //pause the transactional and wait for the following api call to finish
                        TransactionEmails::RunJobStarted(
                            $ArrayEachEmail['RelSubscriberID'],
                            $ArrayEachEmail['ReferenceID'],
                            $ArrayCampaign,
                            $CurrentState
                        );

                        //create queue entry for Facebook API call
                        //the queue worker will set the status back to pending by calling
                        //TransactionEmails::RunJobFinshed()
                        //  the api call was successful or we got an access expired but the user ignores it
                        //TransactionEmails::RunJobFailed()
                        //  the api call returned an access expired, wait 1 day and try again
                        //Note: the worker will invalidate the token, so we won't get here again until it's reactivated
                        ToolFacebookAudience::TriggerProcessflowActionEvent(
                            $account->uid,
                            $state['audienceID'],
                            $FullSubscriber['SubscriberID'],
                            $ArrayEachEmail,
                            $EventType
                        );
                        return;
                    } elseif (empty($state['ignoreExpiredAccess'])) {
                        //wait till we have a valid access token (try again in 1 day)
                        //Note: the job was never started, but TransactionEmails::RunJobFailed() sets the status to pending with a delay of 1 day

                        TransactionEmails::RunJobFailed(
                            $FullSubscriber['SubscriberID'],
                            $ArrayEachEmail['ReferenceID'],
                            $ArrayCampaign,
                            0,
                            $state['id']
                        );
                        return;
                    } else {
                        //the user decided to ignore the expired access
                        //write a history entry and skip this action

                        Subscribers::WriteHistory(
                            $account->uid,
                            $FullSubscriber['SubscriberID'],
                            Subscribers::HISTORY_FACEBOOK_AUDIENCE_EXPIRED_ACCESS,
                            [
                                'AudienceID' => $ObjectAudience->GetData('ToolID'),
                                'AudienceName' => $ObjectAudience->GetData('Name'),
                                'FacebookAccountName' => $ObjectAudience->GetData('FacebookAccountName'),
                                'EventType' => $EventType,
                                'ReferenceID' => $ArrayEachEmail['ReferenceID']
                            ]
                        );
                    }
                }

                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );

                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT:
                TransactionEmails::RunSucceeded(
                    $ArrayCampaign,
                    $FullSubscriber,
                    $ArrayEachEmail['ReferenceID'],
                    $CurrentState
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_RESTART:
                // pause the run until the next trigger
                // which will check if the
                TransactionEmails::RunPausedForRestart(
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $ArrayCampaign,
                    $CurrentState
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_GOAL:
                //the subscriber reached the goal the natural way, just jump to the next state
                TransactionEmails::ProcessGoal($ArrayEachEmail, $ArrayCampaign, $state, true);
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DETECT_NAME:
                $Updated = CustomFields::CalculateNameOfSubscriber(
                    $account->uid,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $state['customFieldID'],
                    $state['customField2ID']
                );
                if ($Updated) {
                    TransactionEmails::RegisterAutomations(
                        $account->uid,
                        $FullSubscriber['SubscriberID'],
                        $ArrayEachEmail['ReferenceID']
                    );
                }
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DETECT_GENDER:
                CustomFields::CalculateGenderFromFirstname(
                    $account->uid,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $state['customFieldID'],
                    $state['customField2ID'],
                    [
                        $state['customFieldValue'],
                        $state['customFieldValue2'],
                        $state['customFieldValue3']
                    ],
                    [$state['tagID'], $state['tag2ID'], $state['tag3ID']]
                );
                TransactionEmails::RunNextTransactional(
                    $ArrayEachEmail,
                    $account,
                    $ArrayCampaign,
                    $CurrentState,
                    $state['next'],
                    $FullSubscriber,
                    $Tagging
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT:
                $VarFullContact = VarFullContact::GetVariable($ArrayCampaign['RelOwnerUserID'], []);
                if (empty($VarFullContact['apiKey'])) {
                    // user has no fullcontact credentials
                    TransactionEmails::RunNextTransactional(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $CurrentState,
                        $state['next'],
                        $FullSubscriber,
                        $Tagging
                    );
                    break;
                }
                //pause the transactional and wait for the following api call to finish
                TransactionEmails::RunJobStarted(
                    $ArrayEachEmail['RelSubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $ArrayCampaign,
                    $CurrentState
                );
                //needed data for the queue worker to call TransactionEmails::RunFinished or TransactionEmails::RunPaused
                $TransactionalData = array(
                    'ArrayCampaignID' => $ArrayCampaign['CampaignID'],
                    'ReferenceID' => $ArrayEachEmail['ReferenceID'],
                );
                // create subscriber queue entry for FullContact API call
                // the queue worker will set the status back to pending by calling TransactionEmails::RunJobFinshed()
                FullContactQueueWorker::triggerProcessflowActionEvent(
                    $account->uid,
                    $ArrayCampaign['CampaignID'],
                    $FullSubscriber['SubscriberID'],
                    $TransactionalData,
                    $state['ignoreRateLimit'],
                    $state['overwriteData']
                );
                break;
            case CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DECISION:
                // decisisons are already processed, so this is an error
            default:
                // unknown state type
                TransactionEmails::RunFailed(
                    $ArrayCampaign,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $CurrentState,
                    TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATETYPE
                );
                Errors::unexpected('RunTransactional: !reason', [
                    '!state' => $CurrentState,
                    '!subscriberid' => $ArrayEachEmail['RelSubscriberID'],
                    '!reason' => TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATETYPE,
                    '!uid' => $account->uid,
                    '!campaignid' => $ArrayCampaign['CampaignID'],
                    '!campaign' => $ArrayCampaign,
                    '!subscriber' => $FullSubscriber,
                ]);
                break;
        }
    }


    /**
     * @param int $userID
     * @param int $subscriberID
     * @param int $referenceID
     * @param int $campaignID
     * @return bool
     */
    public static function applyRateLimit(
        int $userID,
        int $subscriberID,
        int $referenceID,
        int $campaignID,
        int $stateID
    ): bool {
        $campaignRestartRedisRateLimitService = self::getCampaignRestartRedisRateLimitService();
        if (
            // record action suffessfull => limit not reached
            $campaignRestartRedisRateLimitService->recordAction(
                [
                    'userId' => $userID,
                    'subscriberId' => $subscriberID,
                    'referenceId' => $referenceID,
                    'campaignId' => $campaignID
                ]
            )
        ) {
            return false;
        }
        watchdog(
            'automation_restart',
            'UserID: !uid - rate limit reached for campaign restarts campaignID: !campaignId ' .
            'SubscriberID: !subscriberId ReferenceID: !referenceId',
            array(
                '!uid' => $userID,
                '!subscriberId' => $subscriberID,
                '!referenceId' => $referenceID,
                '!campaignId' => $campaignID
            ),
            WATCHDOG_NOTICE
        );

//        // if mismatch, send email to support
//        $account = user_load($userID);
//        $SupportContent = 'Campaign restart rate limit reached, <br>';
//        $SupportContent .= 'User: ' . $account->name . '<br>';
//        $SupportContent .= 'UserID: ' . $userID . '<br>';
//        $SupportContent .= 'CampaignID: ' . $campaignID . '<br>';
//        $SupportContent .= 'User E-Mail: ' . $account->email . '<br>';
//        $SupportContent .= 'Subscriber: ' . $subscriberID . '<br>';
//
//        Core::SendNotificationEmail(
//            PROCESSFLOW_NOTIFY_CAMPAIGN_RESTART_RATE_LIMIT_REACHED,
//            $SupportContent,
//            $userID
//        );

        $delay = (int) ceil(
            $campaignRestartRedisRateLimitService->nextActionAllowedIn(
                [
                    'userId' => $userID,
                    'subscriberId' => $subscriberID,
                    'referenceId' => $referenceID,
                    'campaignId' => $campaignID
                ]
            )
        ); //delay 24h in minutes from oldest action in rate limit window size

        $delay = max($delay, 300);
        TransactionEmails::Update(
            [
                'StatusEnum' => TransactionEmails::STATUS_PENDING,
                'TimeToSend' => strtotime("+$delay seconds"),
                'StateID' => $stateID
            ],
            [
                'RelSubscriberID' => $subscriberID,
                'ReferenceID' => $referenceID,
                'RelOwnerUserID' => $userID,
                'RelAutoResponderID' => $campaignID
            ],
            TransactionalQueue::TABLE_NAME
        );
        return true;
    }

    public static function getCampaignRestartRedisRateLimitService(): CampaignRestartRedisRateLimitService
    {
        if (!isset(self::$campaignRestartRedisRateLimitService)) {
            self::$campaignRestartRedisRateLimitService = new CampaignRestartRedisRateLimitService(
                KLICKTIPP_REDIS_RATE_LIMIT_DSN
            );
        }
        return self::$campaignRestartRedisRateLimitService;
    }

    /**
     * Process a splittest: choose a splittest variant or winner action and excecute
     */
    public static function ProcessSplittest(
        $ArrayEachEmail,
        $account,
        $ArrayCampaign,
        $FullSubscriber,
        $Tagging,
        $TestID,
        $CurrentState
    ) {
        $EntityObject = false;

        $ReferenceID = $ArrayEachEmail['ReferenceID'];

        // updating splittest stats should be treated as a single transaction
        self::beginTransaction();

        // read the splittest data from db, so we have the current state
        /** @var SplitTests $SplittestObject */
        $SplittestObject = SplitTests::FromID($account->uid, $TestID);
        if (!empty($SplittestObject)) {
            // check whether the contact already got an email (any reference)
            $Variant = $SplittestObject->FindVariantOfSubscriber($ArrayCampaign, $FullSubscriber['SubscriberID']);

            // calc email version
            $EntityObject = $SplittestObject->GetNextSplittestVariant($Variant);
            if (!empty($EntityObject)) {
                // write the splittest data from db, so other processes get the current state
                $SplittestObject->UpdateDB($SplittestObject->GetData());
            }
        }

        self::commitTransaction();

        // result == TRUE will stop the automation for this subscriber
        // misconfigured splittest should not stop the automation
        // we halt on serious user, campaign and subscriber problems only
        $result = true;

        if (!empty($EntityObject)) {
            // process action
            switch (get_class($EntityObject)) {
                case CampaignsProcessFlow::class:
                    TransactionEmails::StartAutomation(
                        $account->uid,
                        $FullSubscriber['SubscriberID'],
                        $ReferenceID,
                        $EntityObject->GetData('CampaignID'),
                        true
                    );
                    break;
                case EmailsAutomationEmail::class:
                    $result = TransactionEmails::SendTransactionalEmail(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $FullSubscriber,
                        $Tagging,
                        $EntityObject->GetData('EmailID'),
                        $CurrentState
                    );
                    break;
                case EmailsAutomationSMS::class:
                    $result = TransactionEmails::SendTransactionalSMS(
                        $ArrayEachEmail,
                        $account,
                        $ArrayCampaign,
                        $FullSubscriber,
                        $Tagging,
                        $EntityObject->GetData('EmailID'),
                        $CurrentState
                    );
                    break;
                case ToolOutboundGeneral::class:
                    /** @var ToolOutbound $EntityObject */
                    $EntityObject->TriggerOutboundEvent($FullSubscriber['SubscriberID'], $ReferenceID);
                    break;
                case Tag::class:
                    Subscribers::TagSubscriber(
                        $account->uid,
                        $FullSubscriber['SubscriberID'],
                        $EntityObject->GetData('TagID'),
                        $ReferenceID,
                        true
                    );
                    TransactionEmails::RegisterAutomations(
                        $account->uid,
                        $FullSubscriber['SubscriberID'],
                        $ReferenceID
                    );
                    break;
            }
        }

        return $result;
    }

    /**
     * ProcessGoal updates the subscriber state, that he has reached a goal
     *
     * @param $ArrayEachEmail
     * @param $ArrayCampaign
     * @param $state
     * @param bool $Passed
     */
    public static function ProcessGoal(&$ArrayEachEmail, $ArrayCampaign, $state, $Passed = false)
    {
        // update state for calling procedure
        $ArrayEachEmail['GoalReached'] = 1;

        // update the transactional queue
        db_update(TransactionalQueue::TABLE_NAME)
            ->fields([
                'StatusEnum' => TransactionEmails::STATUS_PENDING,
                'StateID' => $state['next'],
                'TimeToSend' => time(),
                'GoalReached' => 1,
                'ModOfCampaignID' => static::CalcShard($ArrayEachEmail['RelOwnerUserID'], $ArrayCampaign['CampaignID']),
            ])
            ->condition('RelOwnerUserID', $ArrayEachEmail['RelOwnerUserID'])
            ->condition('RelSubscriberID', $ArrayEachEmail['RelSubscriberID'])
            ->condition('ReferenceID', $ArrayEachEmail['ReferenceID'])
            ->condition('RelAutoResponderID', $ArrayCampaign['CampaignID'])
            ->execute();

        // update the history set
        $HistoryData = [
            'CampaignID' => $ArrayCampaign['CampaignID'],
            'CampaignName' => $ArrayCampaign['CampaignName'],
            'StateID' => $state['id'],
            'StateName' => CampaignsProcessFlow::GetStateName($state),
            'ReferenceID' => $ArrayEachEmail['ReferenceID']
        ];

        if ($Passed) {
            //the subscriber reached the goal the natural way
            $HistoryType = Subscribers::HISTORY_GOAL_PASSED;
        } else {
            // the subscriber jumped to the goal
            $HistoryType = Subscribers::HISTORY_GOAL_REACHED;
        }

        Subscribers::WriteHistory(
            $ArrayEachEmail['RelOwnerUserID'],
            $ArrayEachEmail['RelSubscriberID'],
            $HistoryType,
            $HistoryData,
            0,
            $ArrayCampaign['CampaignID']
        );
    }

    /**
     * Check if Email/SMS has already been send in the last X hours. Delay if
     * positive.
     * In case of Emails rate limit applies
     *
     * @param $ArrayEachEmail
     * @param $TagCategory
     * @param $EmailID
     * @param int $CurrentState
     *
     * @return bool
     */
    public static function AlreadySentTest($ArrayEachEmail, $TagCategory, $EmailID, $CurrentState = 0)
    {
      // check if rate limit for sending the same email/sms to the same subscriber is reached
        $emailRedisRateLimitService = self::getEmailRedisRateLimitService();
        if (
            $emailRedisRateLimitService->isActionAllowed(
                [
                    'userId' => $ArrayEachEmail['RelOwnerUserID'],
                    'subscriberId' => $ArrayEachEmail['RelSubscriberID'],
                    'emailId' => $EmailID
                ]
            )
        ) {
                return false;
        }
        watchdog(
            'send_engine',
            'UserID: !uid - rate limit reached for sending EmailID: !emailId to SubscriberID: !subscriberId ',
            array(
            '!uid' => $ArrayEachEmail['RelOwnerUserID'],
            '!emailId' => $EmailID,
            '!subscriberId' => $ArrayEachEmail['RelSubscriberID']
            ),
            WATCHDOG_NOTICE
        );
        $delay = (int) ceil(
            $emailRedisRateLimitService->nextActionAllowedIn(
                [
                    'userId' => $ArrayEachEmail['RelOwnerUserID'],
                    'subscriberId' => $ArrayEachEmail['RelSubscriberID'],
                    'emailId' => $EmailID
                ]
            ) / 60
        ); //delay 24h in minutes from oldest action in rate limit window size

        TransactionEmails::Update(
            [
                'StatusEnum' => TransactionEmails::STATUS_PENDING,
                'TimeToSend' => strtotime("+$delay minutes"),
                'StateID' => $CurrentState,
                'ModOfCampaignID' => static::CalcShard(
                    $ArrayEachEmail['RelOwnerUserID'],
                    $ArrayEachEmail['RelAutoResponderID']
                ),
            ],
            [
                'RelSubscriberID' => $ArrayEachEmail['RelSubscriberID'],
                'ReferenceID' => $ArrayEachEmail['ReferenceID'],
                'RelAutoResponderID' => $ArrayEachEmail['RelAutoResponderID'],
                'RelOwnerUserID' => $ArrayEachEmail['RelOwnerUserID'],
            ],
            TransactionalQueue::TABLE_NAME
        );
        return true;
    }

    /**
     * @param $ArrayEachEmail
     * @param $account
     * @param $ArrayAutoResponder
     * @param $FullSubscriber
     * @param $Tagging
     * @param $EmailID
     * @param int $CurrentState
     *
     * @return bool return true if an automation should continue
     */
    public static function SendTransactionalSMS(
        $ArrayEachEmail,
        $account,
        $ArrayAutoResponder,
        $FullSubscriber,
        $Tagging,
        $EmailID,
        $CurrentState = 0
    ) {
        if (!user_access('access sms marketing', $account)) {
            $tableName = Campaigns::GetQueueTableNameByType($ArrayAutoResponder['AutoResponderTriggerTypeEnum']);
            return TransactionEmails::SetStatusFailed(
                TRANSACTION_REASON_NO_SMS_USER,
                $ArrayAutoResponder['CampaignID'],
                $ArrayAutoResponder['RelOwnerUserID'],
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $tableName
            );
        }

        $ArrayUser = (array)$account;
        $IsPF = Campaigns::IsProcessflow($ArrayAutoResponder['AutoResponderTriggerTypeEnum']);

        // Retrieve email information - Start
        $ArrayEmail = TransactionEmails::GetCachedEmail($account->uid, $EmailID);
        if (empty($ArrayEmail)) {
            return TransactionEmails::SendFailed(
                TRANSACTION_REASON_EMAIL_NOT_FOUND,
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                false
            );
        }
        // Retrieve email information - End

        // check bounces - Start
        $Subscription = Subscriber::FullSubscriberToSMSSubscription($FullSubscriber);
        [$success, $reason, $transfered] = BounceEngine::CheckValidSMSSubscriber(
            $ArrayUser,
            $Subscription,
            $ArrayAutoResponder['CampaignID']
        );
        if ($success == false) {
            return TransactionEmails::SendFailed(
                $reason,
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                $transfered
            );
        }
        // check bounces - End

        // if already sent this sms today, then delay campaign - Start
        if ($IsPF) {
            if (TransactionEmails::AlreadySentTest($ArrayEachEmail, Tag::CATEGORY_SMS_SENT, $EmailID, $CurrentState)) {
                return false;
            }
        }
        // if already sent this sms today, then delay campaign - End

        // Prepare and send the SMS - Start
        $ArrayResult = Emails::SendSMS(
            $FullSubscriber['PhoneNumber'],
            $account,
            $ArrayAutoResponder['CampaignID'],
            $FullSubscriber,
            $ArrayEachEmail['ReferenceID'],
            $ArrayEmail,
            false,
            false
        );
        // Prepare and send the SMS - End

        // Report send status and errors - Start
        if ($ArrayResult[0] == false) {
            return TransactionEmails::SendFailed(
                $ArrayResult[2],
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                false
            );
        } else {
            TransactionEmails::SendSucceeded(
                $ArrayAutoResponder,
                $EmailID,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID']
            );

            TransactionEmails::SendCopyToVerifications($ArrayEachEmail, $account->uid, $ArrayEmail);
        }
        // Report send status and errors - End

        return true;
    }

    /**
     * @param $ArrayEachEmail
     * @param $account
     * @param $ArrayAutoResponder
     * @param $FullSubscriber
     * @param $Tagging
     * @param $EmailID
     * @param int $CurrentState
     *
     * @return bool return true if an automation should continue
     */
    public static function SendTransactionalEmail(
        $ArrayEachEmail,
        $account,
        $ArrayAutoResponder,
        $FullSubscriber,
        $Tagging,
        $EmailID,
        $CurrentState = 0
    ) {
        $ArrayUser = (array)$account;
        $IsPF = Campaigns::IsProcessflow($ArrayAutoResponder['AutoResponderTriggerTypeEnum']);
        $IsAR = Campaigns::IsAutoresponder($ArrayAutoResponder['AutoResponderTriggerTypeEnum']);
        $IsNL = Campaigns::IsNewsletter($ArrayAutoResponder['AutoResponderTriggerTypeEnum']);

        $tableName = Campaigns::GetQueueTableNameByType($ArrayAutoResponder['AutoResponderTriggerTypeEnum']);

        // User sender hosts check - Start
        $SenderDomains = TransactionEmails::GetCachedSenderDomain($ArrayUser);
        if (empty($SenderDomains)) {
            $fields = [
                'StatusEnum' => TransactionEmails::STATUS_PENDING,
                'TimeToSend' => strtotime('+6 hours'),
                'ModOfCampaignID' => static::CalcShard(
                    $ArrayEachEmail['RelOwnerUserID'],
                    $ArrayEachEmail['RelAutoResponderID']
                ),
            ];
            // @todo refactor by using inheritance
            if ($tableName == TransactionalQueue::TABLE_NAME) {
                $fields['StateID'] = $CurrentState;
            }
            TransactionEmails::Update(
                $fields,
                [
                    'RelSubscriberID' => $ArrayEachEmail['RelSubscriberID'],
                    'RelAutoResponderID' => $ArrayEachEmail['RelAutoResponderID'],
                    'RelOwnerUserID' => $ArrayEachEmail['RelOwnerUserID'],
                    'ReferenceID' => $ArrayEachEmail['ReferenceID'],
                ],
                $tableName
            );

            return false;
        }
        // User sender hosts check - End

        // Retrieve email information - Start
        $ArrayEmail = TransactionEmails::GetCachedEmail($account->uid, $EmailID);
        if (empty($ArrayEmail)) {
            return TransactionEmails::SendFailed(
                TRANSACTION_REASON_EMAIL_NOT_FOUND,
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                false
            );
        }
        // Retrieve email information - End

        // check bounces - Start
        $Subscription = Subscriber::FullSubscriberToEmailSubscription($FullSubscriber);
        [$success, $reason, $transfered] = BounceEngine::CheckValidSubscriber(
            $ArrayUser,
            $Subscription,
            $EmailID,
            $ArrayAutoResponder['CampaignID']
        );
        if ($success == false) {
            return TransactionEmails::SendFailed(
                $reason,
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                $transfered
            );
        }
        // check bounces - End

        // find right signature for subscriber - Start
        $ArraySignature = Signatures::FindSignatureByTag(
            $ArrayEmail,
            $Tagging,
            $ArrayAutoResponder['RecipientLists']['TaggedWith']
        );
        if (empty($ArraySignature)) {
            return TransactionEmails::SendFailed(
                TRANSACTION_REASON_SIGNATURE_NOT_FOUND,
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                false
            );
        }
        // find right signature for subscriber - End

        // if already sent this email today, then delay campaign - Start
        if ($IsPF) {
            if (
                TransactionEmails::AlreadySentTest(
                    $ArrayEachEmail,
                    Tag::CATEGORY_EMAIL_SENT,
                    $EmailID,
                    $CurrentState
                )
            ) {
                return false;
            }
        }
        // if already sent this email today, then delay campaign - End

        // if already sent 8 ar emails today, then delay autoresponders - Start
        if (empty($ArrayUser['UserPrivileges']['UnlimitedEmailsPerDay']) && ($IsPF || $IsAR)) {
            //@note: Multivalue taggings - count all campaign runs
            $emails2sameSubscriber = (int)kt_query(
                "SELECT COUNT(*) FROM {" . AutoresponderQueue::TABLE_NAME . "} q " .
                " INNER JOIN {campaigns} c ON c.CampaignID = q.RelAutoResponderID AND c.RelOwnerUserID = q.RelOwnerUserID AND c.AutoResponderTriggerTypeEnum IN (%d, %d) " .
                " WHERE q.RelOwnerUserID = %d AND q.RelSubscriberID = %d AND StatusEnum = %d AND TimeToSend > %d",
                Campaigns::TRIGGER_TYPE_SUBSCRIPTION,
                Campaigns::TRIGGER_TYPE_DATETIME,
                $ArrayEachEmail['RelOwnerUserID'],
                $ArrayEachEmail['RelSubscriberID'],
                TransactionEmails::STATUS_SENT,
                strtotime("-1 day")
            )->fetchField();

            /*
             * @todo $IsPF relates to currently delivered e-mail. We should count all e-mails
             * (appart from newsletter, birthday and confirmation ones)
             */
            if ($IsPF && $emails2sameSubscriber <= 7) {
                // add sent automation emails
                //@note: Multivalue taggings - count all campaign runs
                $AutomationEmails2sameSubscriber = (int)db_query(
                    "SELECT COUNT(*) FROM {tagging} t " .
                    " INNER JOIN {tag} tt ON tt.TagID = t.RelTagID AND tt.RelOwnerUserID = t.RelOwnerUserID AND tt.Category = :Category " .
                    " WHERE t.RelOwnerUserID = :RelOwnerUserID AND t.RelSubscriberID = :RelSubscriberID AND t.SubscriptionDate > :SubscriptionDate ",
                    [
                        ':Category' => Tag::CATEGORY_EMAIL_SENT,
                        ':RelOwnerUserID' => $ArrayEachEmail['RelOwnerUserID'],
                        ':RelSubscriberID' => $ArrayEachEmail['RelSubscriberID'],
                        ':SubscriptionDate' => strtotime("-1 day"),
                    ]
                )->fetchField();

                $emails2sameSubscriber += $AutomationEmails2sameSubscriber;
            }
            if ($emails2sameSubscriber > 7) {
                $fields = [
                    'StatusEnum' => TransactionEmails::STATUS_PENDING,
                    'TimeToSend' => strtotime('+1 day'),
                    'ModOfCampaignID' => static::CalcShard(
                        $ArrayEachEmail['RelOwnerUserID'],
                        $ArrayEachEmail['RelAutoResponderID']
                    ),
                ];
                // @todo refactor by using inheritance
                if ($tableName == TransactionalQueue::TABLE_NAME) {
                    $fields['StateID'] = $CurrentState;
                }
                TransactionEmails::Update(
                    $fields,
                    [
                        'RelSubscriberID' => $ArrayEachEmail['RelSubscriberID'],
                        'RelAutoResponderID' => $ArrayEachEmail['RelAutoResponderID'],
                        'RelOwnerUserID' => $ArrayEachEmail['RelOwnerUserID'],
                        'ReferenceID' => $ArrayEachEmail['ReferenceID'],
                    ],
                    $tableName
                );

                return false;
            }
        }
        // if already sent 8 ar emails today, then delay autoresponders - End

        $targetDir = null;
        $sentTime = 0;
        if ($IsNL && CampaignsNewsletter::isPreGenerating($ArrayAutoResponder)) {
            $targetDir = PreGenerationHandler::createDirectory($ArrayAutoResponder);
            $sentTime = $ArrayAutoResponder['SendDatetime'];
        }
        // send the email
        $ArrayResult = Emails::SendEmail(
            $FullSubscriber['EmailAddress'],
            $ArrayUser,
            $ArrayAutoResponder['CampaignID'],
            $FullSubscriber,
            $ArrayEachEmail['ReferenceID'],
            $ArrayEmail,
            $ArraySignature,
            $IsPF || $IsAR ? 'ar' : 'nl',
            !empty($ArrayEmail['ReportSpamEnabled']),
            false,
            $SenderDomains,
            0,
            false,
            [],
            0,
            $sentTime,
            0,
            $targetDir
        );


        // Report send status and errors - Start
        if ($ArrayResult[0] == false) {
            return TransactionEmails::SendFailed(
                $ArrayResult[2],
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                false
            );
        } else {
            if ($IsNL && CampaignsNewsletter::isPreGenerating($ArrayAutoResponder)) {
                TransactionEmails::preGenerationSucceeded($ArrayAutoResponder, $FullSubscriber);
            } else {
                $emailRedisRateLimitService = self::getEmailRedisRateLimitService();

                // register the action in the rate limit service
                $emailRedisRateLimitService->recordAction(
                    [
                        'userId' => $ArrayEachEmail['RelOwnerUserID'],
                        'subscriberId' => $ArrayEachEmail['RelSubscriberID'],
                        'emailId' => $EmailID
                    ]
                );

                // set the time this subscriber will be age checked, but dont, if
                // - its not a newsletter
                // - its an SMS
                // - its not send to the subscriber himself (ReceiverEmail)
                // - we have a subscriber action in the last 6 month
                $agedCheck = 0;
                if ($IsNL) {
                    $agedCheck = static::getAgedCheck($FullSubscriber, $account);
                }

                TransactionEmails::SendSucceeded(
                    $ArrayAutoResponder,
                    $EmailID,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $agedCheck,
                    false
                );

                TransactionEmails::SendCopyToVerifications($ArrayEachEmail, $account->uid, $ArrayEmail);
            }
        }
        // Report send status and errors - End

        return true;
    }

    public static function getEmailRedisRateLimitService(): EmailRedisRateLimitService
    {
        if (!isset(self::$emailRedisRateLimitService)) {
            self::$emailRedisRateLimitService = new EmailRedisRateLimitService(
                KLICKTIPP_REDIS_RATE_LIMIT_DSN
            );
        }
        return self::$emailRedisRateLimitService;
    }

    /**
     * @param scalar[] $fullSubscriber
     * @param stdClass $account
     *
     * @return int
     */
    public static function getAgedCheck(array $fullSubscriber, stdClass $account): int
    {
        if (
            $fullSubscriber['LastOpenDate'] >= strtotime('-6 month') ||
            user_access('use whitelabel domain', $account)
        ) {
            return 0;
        }

        // this is the distribution for checks of aged subscribers
        $agedCheckDist = [
            0 => strtotime('+8 hours'),
            1 => strtotime('+9 hours'),
            2 => strtotime('+10 hours'),
            3 => strtotime('+12 hours'),
            4 => strtotime('+14 hours'),
            5 => strtotime('+17 hours'),
            6 => strtotime('+20 hours'),
            7 => strtotime('+23 hours'),
            8 => strtotime('+27 hours'),
            9 => strtotime('+31 hours'),
        ];

        // from 8 to 32 hours after sending
        return $agedCheckDist[array_rand($agedCheckDist)] + mt_rand(0, 3600);
    }

    /**
     * @param $ArrayEachEmail
     * @param $account
     * @param $ArrayAutoResponder
     * @param $FullSubscriber
     * @param $Tagging
     * @param $EmailID
     * @param $ReceiverPhoneNumber
     * @param int $CurrentState
     *
     * @return bool return true if an automation should continue
     */
    public static function SendTransactionalNotifySMS(
        $ArrayEachEmail,
        $account,
        $ArrayAutoResponder,
        $FullSubscriber,
        $Tagging,
        $EmailID,
        $ReceiverPhoneNumber,
        $CurrentState = 0
    ) {
        if (!user_access('access sms marketing', $account)) {
            return TransactionEmails::SetStatusFailed(
                TRANSACTION_REASON_NO_SMS_USER,
                $ArrayAutoResponder['CampaignID'],
                $ArrayAutoResponder['RelOwnerUserID'],
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                TransactionalQueue::TABLE_NAME
            );
        }

        $ArrayUser = (array)$account;

        // Retrieve email information - Start
        $ArrayEmail = TransactionEmails::GetCachedEmail($account->uid, $EmailID);
        if (empty($ArrayEmail)) {
            return TransactionEmails::SendFailed(
                TRANSACTION_REASON_EMAIL_NOT_FOUND,
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                false
            );
        }
        // Retrieve email information - End

        // check bounces - Start
        if (!Campaigns::IsProcessflow($ArrayAutoResponder['AutoResponderTriggerTypeEnum'])) {
            $Subscription = Subscriber::FullSubscriberToSMSSubscription($FullSubscriber);
            [$success, $reason, $transfered] = BounceEngine::CheckValidSMSSubscriber(
                $ArrayUser,
                $Subscription,
                $ArrayAutoResponder['CampaignID']
            );
            if ($success == false) {
                return TransactionEmails::SendFailed(
                    $reason,
                    $ArrayAutoResponder,
                    $FullSubscriber['SubscriberID'],
                    $ArrayEachEmail['ReferenceID'],
                    $CurrentState,
                    $transfered
                );
            }
        }
        // check bounces - End

        // Prepare and send the SMS - Start
        $ArrayResult = Emails::SendSMS(
            $ReceiverPhoneNumber,
            $account,
            $ArrayAutoResponder['CampaignID'],
            $FullSubscriber,
            $ArrayEachEmail['ReferenceID'],
            $ArrayEmail,
            false,
            false
        );
        // Prepare and send the email - End

        // Report send status and errors - Start
        if ($ArrayResult[0] == false) {
            return TransactionEmails::SendFailed(
                $ArrayResult[2],
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID'],
                $CurrentState,
                false
            );
        } else {
            TransactionEmails::SendSucceeded(
                $ArrayAutoResponder,
                $EmailID,
                $FullSubscriber['SubscriberID'],
                $ArrayEachEmail['ReferenceID']
            );
        }
        // Report send status and errors - End

        return true;
    }

    /**
     * @param $TransactionalQueueEntry
     * @param $account
     * @param $ArrayAutoResponder
     * @param $FullSubscriber
     * @param $Tagging
     * @param $EmailID
     * @param $ReceiverEmail
     * @param $isSimpletest
     *
     * @return bool return true if an automation should continue
     */
    public static function SendTransactionalNotifyEmail(
        $TransactionalQueueEntry,
        $account,
        $ArrayAutoResponder,
        $FullSubscriber,
        $Tagging,
        $EmailID,
        $ReceiverEmail,
        $CurrentState = 0,
        $isSimpletest = false
    ) {
        $ArrayUser = (array)$account;

        // User sender hosts check - Start
        $SenderDomains = TransactionEmails::GetCachedSenderDomain($ArrayUser);
        if (empty($SenderDomains)) {
            TransactionEmails::Update(
                [
                    'StatusEnum' => TransactionEmails::STATUS_PENDING,
                    'TimeToSend' => strtotime('+6 hours'),
                    'StateID' => $CurrentState,
                    'ModOfCampaignID' => static::CalcShard(
                        $TransactionalQueueEntry['RelOwnerUserID'],
                        $TransactionalQueueEntry['RelAutoResponderID']
                    ),
                ],
                [
                    'RelSubscriberID' => $TransactionalQueueEntry['RelSubscriberID'],
                    'RelAutoResponderID' => $TransactionalQueueEntry['RelAutoResponderID'],
                    'RelOwnerUserID' => $TransactionalQueueEntry['RelOwnerUserID'],
                    'ReferenceID' => $TransactionalQueueEntry['ReferenceID'],
                ],
                Campaigns::GetQueueTableNameByType($ArrayAutoResponder["AutoResponderTriggerTypeEnum"])
            );

            return false;
        }
        // User sender hosts check - End

        // Retrieve email information - Start
        $ArrayEmail = TransactionEmails::GetCachedEmail($account->uid, $EmailID);
        if (empty($ArrayEmail)) {
            return TransactionEmails::SendFailed(
                TRANSACTION_REASON_EMAIL_NOT_FOUND,
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $TransactionalQueueEntry['ReferenceID'],
                $CurrentState,
                false
            );
        }
        // Retrieve email information - End

        // check bounces - Start
        if (!Campaigns::IsProcessflow($ArrayAutoResponder['AutoResponderTriggerTypeEnum'])) {
            $Subscription = Subscriber::FullSubscriberToEmailSubscription($FullSubscriber);
            [$success, $reason, $transfered] = BounceEngine::CheckValidSubscriber(
                $ArrayUser,
                $Subscription,
                $EmailID,
                $ArrayAutoResponder['CampaignID']
            );
            if ($success == false) {
                return TransactionEmails::SendFailed(
                    $reason,
                    $ArrayAutoResponder,
                    $FullSubscriber['SubscriberID'],
                    $TransactionalQueueEntry['ReferenceID'],
                    $CurrentState,
                    $transfered
                );
            }
        }
        // check bounces - End

        // find right signature for subscriber
        $ArraySignature = Signatures::FindSignatureByTag(
            $ArrayEmail,
            $Tagging,
            $ArrayAutoResponder['RecipientLists']['TaggedWith']
        );
        if (empty($ArraySignature)) {
            return TransactionEmails::SendFailed(
                TRANSACTION_REASON_SIGNATURE_NOT_FOUND,
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $TransactionalQueueEntry['ReferenceID'],
                $CurrentState,
                false
            );
        }
        if ($ReceiverEmail == Signatures::SIGNATURE_DISPATCH_PROFILE) {
            // determine the receiver email dynamically based on tags
            // thus retrieve the signature by tags regardless of the email configuration
            $SignatureByTags = Signatures::FindSignatureByTag(array(
                'RelUserID' => $ArrayEmail['RelUserID']
            ), $Tagging);
            if (!empty($SignatureByTags['ToEmail'])) {
                $ReceiverEmail = $SignatureByTags['ToEmail'];
            }
        }

        // Prepare and send the email - Start
        $ArrayResult = Emails::SendEmail(
            $ReceiverEmail,
            $account,
            $ArrayAutoResponder['CampaignID'],
            $FullSubscriber,
            $TransactionalQueueEntry['ReferenceID'],
            $ArrayEmail,
            $ArraySignature,
            'ar',
            false,
            false,
            $SenderDomains,
            0,
            $isSimpletest,
            [],
            0,
            0,
            $CurrentState
        );
        // Prepare and send the email - End
        if ($isSimpletest) {
            return $ArrayResult;
        }

        // Report send status and errors - Start
        if ($ArrayResult[0] == false) {
            return TransactionEmails::SendFailed(
                $ArrayResult[2],
                $ArrayAutoResponder,
                $FullSubscriber['SubscriberID'],
                $TransactionalQueueEntry['ReferenceID'],
                $CurrentState,
                false
            );
        } else {
            TransactionEmails::SendSucceeded(
                $ArrayAutoResponder,
                $EmailID,
                $FullSubscriber['SubscriberID'],
                $TransactionalQueueEntry['ReferenceID'],
                0,
                false
            );
        }
        // Report send status and errors - End

        return true;
    }

    public static function CalcSlicesFromRemainder($remainder)
    {
        // ignore jobs divisor, so configuration changes take
        $divisor = variable_get('klicktipp_wts_slices', 0);

        // get query mod parameter
        if (
            in_array($divisor, array(2, 4, 8, 16, 32, 64, 128, 256, 512, 1024)) && ($remainder >= 0) && ($remainder < $divisor)
        ) {
            // calc all matching remainder of the ModOfCampaignID column (0..1023)
            $range = 1024 / $divisor;
            $lower = $remainder * $range;
            $upper = $lower + $range;

            return [$remainder, $divisor, $lower, $upper];
        }

        // test or parameters not set well
        return [0, 0, 0, 0];
    }

    /**
     * @param int $time
     * @param array<array{
     *           op: string,
     *           field: int,
     *           value: string|array<int>,
     *           value2: mixed
     *       }> $conditions
     * @param int $userID
     * @param int $subscriberID
     * @param int $referenceID
     * @phpstan-return DateTime|0
     * @throws \Exception
 */
    private static function applyConditions(
        int $time,
        array $conditions,
        int $userID,
        int $subscriberID,
        int $referenceID
    ) {
        $date = (new DateTime('@' . $time))->setTimezone(new DateTimeZone(date_default_timezone_get()));

        // Check for conflicting conditions before proceeding
        if (self::hasConflictingConditions($conditions)) {
            return 0; // If the conditions conflict, return 0 immediately
        }

        $dateLastResult = new DateTime('0001-01-01 00:00:00');
        $maxDate = (new DateTime())->modify('+5 years');
        $maxIterations = 100000; // Prevent infinite loops
        $i = 1;

        while ($date != $dateLastResult) {
            // Endless Loop Protection
            if ($i > $maxIterations) {
                watchdog(
                    'temporary_conditions   ',
                    'Endless loop detected in applyConditions.' .
                    ' UserID: !uid, SubscriberID: !subscriberId, ReferenceID: !referenceId',
                    [
                        '!uid' => $userID,
                        '!subscriberId' => $subscriberID,
                        '!referenceId' => $referenceID,
                    ],
                    WATCHDOG_WARNING
                );
                return 0;
            }
            // Check if the resulting date is within the next 5 years
            if ($date > $maxDate) {
                return 0; // If it's more than 5 years ahead, return 0
            }
            $dateLastResult = clone $date;
            // Apply each condition
            foreach ($conditions as $condition) {
                $date = self::applyCondition($date, $condition, $userID, $subscriberID, $referenceID);
                if ($date === 0) {
                    return 0;
                }
            }
            $i++;
        }

        // Check if the date is valid (handles cases like Feb 31st)
        if (!$date->getTimestamp()) {
            return 0; // If the date is invalid, return 0
        }

        return $date;
    }

    /**
     * @param array<array{
     *            op: string,
     *            field: int,
     *            value: string|array<int>,
     *            value2: mixed
     *        }> $conditions
      * @return bool
     */
    private static function hasConflictingConditions(array $conditions): bool
    {
        $minTime = "00:00";
        $maxTime = "23:59";
        $includedDates = [];
        $excludedDates = [];
        $includedDaysOfMonth = [];
        $includedMonths = [];
        $includedWeekdays = [];
        $beforeDate = null;
        $afterDate = null;

        foreach ($conditions as $condition) {
            switch ($condition['op']) {
                case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_AFTER_TIME:
                    if (is_string($condition['value'])) {
                        $minTime = max($minTime, $condition['value']);
                    }
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_BEFORE_TIME:
                    if (is_string($condition['value'])) {
                        $maxTime = min($maxTime, $condition['value']);
                    }
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS:
                    assert(is_string($condition['value']));
                    $includedDates[] = $condition['value'];
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_NOT:
                    assert(is_string($condition['value']));
                    $excludedDates[] = $condition['value'];
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_DAY_OF_MONTH:
                    assert(is_array($condition['value']));
                    $includedDaysOfMonth = array_merge($includedDaysOfMonth, $condition['value']);
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_MONTH:
                    assert(is_array($condition['value']));
                    $includedMonths = array_merge($includedMonths, $condition['value']);
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_WEEKDAY:
                    assert(is_array($condition['value']));
                    $includedWeekdays = array_merge($includedWeekdays, $condition['value']);
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_BEFORE:
                    assert(is_string($condition['value']));
                    $beforeDate = $condition['value'];
                    break;

                case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_AFTER:
                    assert(is_string($condition['value']));
                    $afterDate = $condition['value'];
                    break;
            }
        }

        $minTimeMinutes = self::timeToMinutes($minTime);
        $maxTimeMinutes = self::timeToMinutes($maxTime);

        if ($minTimeMinutes > $maxTimeMinutes && $maxTimeMinutes > 0) {
            return true;
        }

        foreach ($includedDates as $date) {
            if (in_array($date, $excludedDates)) {
                return true;
            }
        }

        if ($beforeDate && $afterDate) {
            try {
                $before = new DateTime($beforeDate);
                $after = new DateTime($afterDate);
                if ($after >= $before) {
                    return true;
                }
            } catch (\Exception $e) {
                return true;
            }
        }

        if (!empty($includedDaysOfMonth)) {
            $includedDaysOfMonth = array_unique($includedDaysOfMonth);
            foreach ($includedDaysOfMonth as $day) {
                if ($day < 1 || $day > 31) {
                    return true;
                }
            }
        }

        if (!empty($includedMonths)) {
            $includedMonths = array_unique($includedMonths);
            foreach ($includedMonths as $month) {
                if ($month < 1 || $month > 12) {
                    return true;
                }
            }
        }

        if (!empty($includedWeekdays)) {
            $includedWeekdays = array_unique($includedWeekdays);
            foreach ($includedWeekdays as $weekday) {
                if ($weekday < 1 || $weekday > 7) {
                    return true;
                }
            }
        }

        foreach ($includedDates as $date) {
            try {
                $dateObj = new DateTime($date);

                if (!empty($includedDaysOfMonth) && !in_array((int)$dateObj->format('j'), $includedDaysOfMonth)) {
                    return true;
                }

                if (!empty($includedMonths) && !in_array((int)$dateObj->format('n'), $includedMonths)) {
                    return true;
                }

                if (!empty($includedWeekdays) && !in_array((int)$dateObj->format('w'), $includedWeekdays)) {
                    return true;
                }
            } catch (\Exception $e) {
                return true;
            }
        }

        return false;
    }

    /**
     * Convert time string (HH:mm) to minutes since midnight
     * @param string $time
     * @return int
     */
    private static function timeToMinutes(string $time): int
    {
        list($hours, $minutes) = array_map('intval', explode(':', $time));
        return $hours * 60 + $minutes;
    }

    /**
     * @param DateTime $date
     * @param array{
     *             op: string,
     *             field: int,
     *             value: string|array<int>,
     *             value2: mixed
     *         } $condition
     * @param int $userID
     * @param int $subscriberID
     * @param int $referenceID
     * @phpstan-return DateTime|0
     * @throws \Exception
     */
    private static function applyCondition(
        DateTime $date,
        array $condition,
        int $userID,
        int $subscriberID,
        int $referenceID
    ) {
        switch ($condition['op']) {
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS:
                assert(is_string($condition['value']));
                return self::applyOnDate($date, $condition['value']);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_NOT:
                assert(is_string($condition['value']));
                return self::applyOnExcludedDate($date, $condition['value']);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_BEFORE_TIME:
                assert(is_string($condition['value']));
                return self::applyEarlierThan($date, $condition['value']);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_AFTER_TIME:
                assert(is_string($condition['value']));
                return self::applyAfterTime($date, $condition['value']);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_DAY_OF_MONTH:
                assert(is_array($condition['value']));
                return self::applyOnDaysOfMonth($date, $condition['value']);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_MONTH:
                assert(is_array($condition['value']));
                return self::applyOnMonths($date, $condition['value']);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_WEEKDAY:
                assert(is_array($condition['value']));
                return self::applyOnWeekdays($date, $condition['value']);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_BEFORE:
                assert(is_string($condition['value']));
                return self::applyOnTodayIsBefore($date, $condition['value']);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_AFTER:
                assert(is_string($condition['value']));
                return self::applyOnTodayIsAfter($date, $condition['value']);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_HOLIDAY:
                assert(is_array($condition['value']));
                return self::applyOnIsHoliday($date, $condition['value'], $userID);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_FIELD_BEFORE:
                return self::applyOnCustomFieldBefore($date, $condition, $userID, $subscriberID, $referenceID);
            case CampaignsProcessFlow::PROCESSFLOW_CONDITION_FIELD_AFTER:
                return self::applyOnCustomFieldAfter($date, $condition, $userID, $subscriberID, $referenceID);
            default:
                watchdog(
                    'temporal-condition',
                    'Unknown condition type: !conditionType',
                    ['!conditionType' => $condition['op']],
                    WATCHDOG_WARNING
                );
                return 0;
        }
    }

    /**
     * @param DateTime $date
     * @param string $time
     * @return DateTime
     * @throws \Exception
     */
    private static function applyAfterTime($date, $time): DateTime
    {
        $dateTime = $date->format('H:i');
        if ($dateTime <= $time) {
            $date->setTime((int)explode(':', $time)[0], (int)explode(':', $time)[1]);
            $date->modify('+1 minute'); // Move a bit after the specified time
        }
        return $date;
    }

    /**
     * @param DateTime $date
     * @param string $targetDate
     * @phpstan-return DateTime|0
     * @throws \Exception
     */
    private static function applyOnDate(DateTime $date, string $targetDate)
    {
        if ($date->format('Y-m-d') === $targetDate) {
            return $date;
        }

        $today = (new DateTime())->setTime(0, 0);
        $applyOnDateTime = (new DateTime($targetDate))->setTime(0, 0);

        if ($today > $applyOnDateTime) {
            return 0;
        }

        return $applyOnDateTime;
    }

    /**
     * @param Datetime $date
     * @param string $excludedDate
     * @phpstan-return DateTime
     * @throws \Exception
     */
    private static function applyOnExcludedDate(DateTime $date, string $excludedDate)
    {
        $excludedDateTime = new DateTime($excludedDate);

        if ($date->format('Y-m-d') == $excludedDateTime->format('Y-m-d')) {
            $date->modify('+1 day')->setTime(0, 0);
        }
        return $date;
    }

    /**
     * @param Datetime $date
     * @param array<int> $calendarIDs
     * @param int $userID
     * @phpstan-return DateTime
     */
    private static function applyOnIsHoliday(DateTime $date, array $calendarIDs, int $userID)
    {
        if (!ToolCalendar::IsHoliday($userID, $calendarIDs, $date->getTimestamp())) {
            $date->modify('+1 day')->setTime(0, 0);
        }
        return $date;
    }

    /**
     * @param DateTime $date
     * @param string $compareDate
     * @phpstan-return DateTime|0
     * @throws \Exception
     */
    private static function applyOnTodayIsBefore(DateTime $date, string $compareDate)
    {
        $compareDateTime = new DateTime($compareDate);
        $compareDateTime->setTime(0, 0);
        $today = new DateTime('today');

        if ($compareDateTime < $today) {
            return 0;
        }

        if ($date > $compareDateTime) {
            return $compareDateTime;
        }
        return $date;
    }

    /**
     * @param DateTime $date
     * @param string $compareDate
     * @return DateTime
     * @throws \Exception
     */
    private static function applyOnTodayIsAfter(DateTime $date, string $compareDate): DateTime
    {
        $compareDateTime = new DateTime($compareDate);
        $compareDateTime->setTime(0, 0);

        if ($date < $compareDateTime) {
            return $compareDateTime;
        }
        return $date;
    }

    /**
     * @param DateTime $date
     * @param array<int> $days
     * @return DateTime
     * @throws \Exception
     */
    private static function applyOnDaysOfMonth(DateTime $date, array $days): DateTime
    {
        $targetDay = (int)$date->format('j');
        sort($days);

        foreach ($days as $day) {
            if ((int) $day >= $targetDay) {
                $date->setDate((int)$date->format('Y'), (int)$date->format('m'), $day);
                $date->setTime(0, 0);
                return self::validateAndAdjustDate($date);
            }
        }
        $date->modify('first day of next month');
        $date->setDate((int)$date->format('Y'), (int)$date->format('m'), $days[0]);
        $date->setTime(0, 0);
        return self::validateAndAdjustDate($date);
    }

    /**
     * @param DateTime $date
     * @param array<int> $months
     * @return DateTime
     * @throws \Exception
     */
    private static function applyOnMonths(DateTime $date, array $months): DateTime
    {
        $currentMonth = (int) $date->format('n');
        $currentYear = (int) $date->format('Y');

        sort($months);

        foreach ($months as $month) {
            if ($month == $currentMonth) {
                return self::validateAndAdjustDate($date);
            }
            if ($month > $currentMonth) {
                $date->setDate($currentYear, $month, 1)->setTime(0, 0);
                return self::validateAndAdjustDate($date);
            }
        }
        $date->setDate($currentYear + 1, $months[0], 1)->setTime(0, 0);
        return self::validateAndAdjustDate($date);
    }

    /**
     * @param DateTime $date
     * @param string $time
     * @return DateTime
     * @throws \Exception
     */
    private static function applyEarlierThan(DateTime $date, string $time): DateTime
    {
        $currentTime = $date->format('H:i');
        if ($currentTime >= $time) {
            $date->modify('+1 day')->setTime(0, 0);
        }
        return $date;
    }

    /**
     * @param DateTime $date
     * @param array<int> $weekdays
     * @return DateTime
     * @throws \Exception
     */
    private static function applyOnWeekdays(DateTime $date, array $weekdays): DateTime
    {
        $currentWeekday = $date->format('N');
        if (!in_array($currentWeekday, $weekdays)) {
            while (!in_array($date->format('N'), $weekdays)) {
                $date->modify('+1 day');
            }
            $date->setTime(0, 0);
        }
        return self::validateAndAdjustDate($date);
    }

    /**
     * @param DateTime $date
     * @param array{
     *              op: string,
     *              field: int,
     *              value: string|array<int>,
     *              value2: mixed
     *          } $condition
     * @param int $userID
     * @param int $subscriberID
     * @param int $referenceID
     * @phpstan-return DateTime
     * @throws \Exception
     */
    private static function applyOnCustomFieldBefore(
        DateTime $date,
        array $condition,
        int $userID,
        int $subscriberID,
        int $referenceID
    ) {
        [$fieldValue, $value, $FieldType] = CampaignsProcessFlow::GetConditionFieldParams(
            $userID,
            $subscriberID,
            $referenceID,
            $condition
        );
        $valueFromDate = ($FieldType == CustomFields::TYPE_DATE) ? date(
            'Y-m-d',
            strtotime($value, $date->getTimestamp())
        ) : date(
            'Y-m-d H:i',
            strtotime($value, $date->getTimestamp())
        );
        if ($fieldValue < $valueFromDate) {
            return self::validateAndAdjustDate($date);
        }
        $difference = strtotime($fieldValue) - strtotime($valueFromDate);
        $date->modify(sprintf('+%d seconds', $difference + 60));
        return self::validateAndAdjustDate($date);
    }

    /**
     * @param DateTime $date
     * @param array{
     *              op: string,
     *              field: int,
     *              value: string|array<int>,
     *              value2: mixed
     *          } $condition
     * @param int $userID
     * @param int $subscriberID
     * @param int $referenceID
     * @phpstan-return DateTime|0
     * @throws \Exception
     */
    private static function applyOnCustomFieldAfter(
        DateTime $date,
        array $condition,
        int $userID,
        int $subscriberID,
        int $referenceID
    ) {
        [$fieldValue, $value, $FieldType] = CampaignsProcessFlow::GetConditionFieldParams(
            $userID,
            $subscriberID,
            $referenceID,
            $condition
        );
        $valueFromDate = ($FieldType == CustomFields::TYPE_DATE) ? date(
            'Y-m-d',
            strtotime($value, $date->getTimestamp())
        ) : date(
            'Y-m-d H:i',
            strtotime($value, $date->getTimestamp())
        );
        if ($fieldValue > $valueFromDate) {
            return self::validateAndAdjustDate($date);
        }
        return 0;
    }

    /**
     * Function to validate and adjust the date if it's invalid (e.g., February 31st)
     * If the date is invalid, move to the next valid date (like 1st of next month)
     *
     * @param DateTime $date
     * @return DateTime
     * @throws \Exception
     */
    private static function validateAndAdjustDate(DateTime $date): DateTime
    {
        if (!$date->getTimestamp()) {
            $date->modify('first day of next month');
        }
        return $date;
    }

    /**
     * @param array<array{
     *           op: string,
     *           field: int,
     *           value: string|array<int>,
     *           value2: mixed
     *       }> $conditions
     * @param int $subscriberID
     * @param int $referenceID
     * @param int $userID
     * @param ?int $startTime if null, current time is used
     * @return int
     * @throws Exception
     * @throws \Exception
     */
    public static function calculateNextMatchingCombinedConditionTime(
        array $conditions,
        int $subscriberID,
        int $referenceID,
        int $userID,
        ?int $startTime = null
    ): int {
        $nextValidDate = self::applyConditions($startTime ?? time(), $conditions, $userID, $subscriberID, $referenceID);

        if (!($nextValidDate instanceof DateTime) && $nextValidDate === 0) {
            return 0;
        }

        return $nextValidDate->getTimestamp();
    }

    /**
     * @phpstan-ignore-next-line
     **/
    private static function processSegment(array $segment): array
    {
        $andCombinations = [];
        $orCombinations = [];

        if (!empty($segment['and'])) {
            $andCombinations[] = $segment['and'];
        }

        if (!empty($segment['or'])) {
            $orCombinations = $segment['or'];
        }

        return [
            'and' => $andCombinations,
            'or'  => $orCombinations,
        ];
    }

    /**
     * @phpstan-ignore-next-line
     **/
    private static function combineOrConditions(array $segments): array
    {
        $orConditions = [];

        foreach ($segments as $segment) {
            if (!empty($segment['or'])) {
                if (empty($orConditions)) {
                    foreach ($segment['or'] as $condition) {
                        $orConditions[] = [$condition]; // Keep the condition wrapped in an array
                    }
                } else {
                    $temp = [];

                    foreach ($orConditions as $existing) {
                        foreach ($segment['or'] as $new) {
                            $temp[] = array_merge($existing, [$new]);
                        }
                    }
                    $orConditions = $temp;
                }
            }
        }
        return $orConditions;
    }

    /**
     * Calculates the next possible match based on the failed segments and data.
     *
     * @param array<string,
     *               array<array<string,
     *                          array<array<string, mixed>>|array<array<string, mixed>>
     *                    >>
     *        > $failedSegments
     *  @param array<string, mixed> $data
     * @param ?int $startTime if null current time is used
     * @return int
     * @throws Exception
     */
    public static function calcNextPossibleMatch(array $failedSegments, array $data, ?int $startTime = null): int
    {
        $finalCombinations = [];

        if (!empty($failedSegments['and'])) {
            /** @var array<array{op: string, field: int, value: array<int>|string, value2: mixed}> $allAndConditions */
            $allAndConditions = [];
            foreach ($failedSegments['and'] as $segment) {
                if (!empty($segment['and'])) {
                    foreach ($segment['and'] as $andCondition) {
                        /** @var array{op: string, field: int, value: array<int>|string, value2: mixed} $andCondition */
                        if (isset($andCondition['op'])) {
                            $allAndConditions[] = $andCondition;
                        }
                    }
                }
            }

            $orSegments = [];
            foreach ($failedSegments['and'] as $segment) {
                if (!empty($segment['or'])) {
                    $orSegments[] = $segment['or'];
                }
            }

            /** @var array<array<array{op: string, field: int, value: array<int>|string, value2: mixed}>> $orCombinations */
            $orCombinations = [[]];
            foreach ($orSegments as $orSegment) {
                /** @var array<array<array{op: string, field: int, value: array<int>|string, value2: mixed}>> $newCombinations */
                $newCombinations = [];
                foreach ($orCombinations as $existing) {
                    foreach ($orSegment as $orCondition) {
                        /** @var array{op: string, field: int, value: array<int>|string, value2: mixed} $orCondition */
                        if (isset($orCondition['op'])) {
                            $newCombinations[] = array_merge($existing, [$orCondition]);
                        }
                    }
                }
                $orCombinations = $newCombinations;
            }

            foreach ($orCombinations as $orCombination) {
                $finalCombinations[] = array_merge($orCombination, $allAndConditions);
            }
        } elseif (!empty($failedSegments['or'])) {
            foreach ($failedSegments['or'] as $segment) {
                if (!empty($segment['and'])) {
                    /** @var array<array{op: string, field: int, value: array<int>|string, value2: mixed}> $andConditions */
                    $andConditions = [];
                    foreach ($segment['and'] as $andCondition) {
                        /** @var array{op: string, field: int, value: array<int>|string, value2: mixed} $andCondition */
                        if (isset($andCondition['op'])) {
                            $andConditions[] = $andCondition;
                        }
                    }
                    $finalCombinations[] = $andConditions;
                } elseif (!empty($segment['or'])) {
                    foreach ($segment['or'] as $orCondition) {
                        /** @var array{op: string, field: int, value: array<int>|string, value2: mixed} $orCondition */
                        if (isset($orCondition['op'])) {
                            $finalCombinations[] = [$orCondition];
                        }
                    }
                }
            }
        }

        /** @var array<array<array{op: string, field: int, value: array<int>|string, value2: mixed}>> $finalCombinations */
        foreach ($finalCombinations as $k => $conditionGroup) {
            if (!self::isConditionGroupValid($conditionGroup)) {
                unset($finalCombinations[$k]);
            }
        }

        if (empty($finalCombinations)) {
            return 0;
        }

        $nextPossibleTime = 0;
        foreach ($finalCombinations as $conditionGroup) {
            $currentGroupTime = self::calculateNextMatchingCombinedConditionTime(
                $conditionGroup,
                is_numeric($data['SubscriberID']) ? (int)$data['SubscriberID'] : 0,
                is_numeric($data['ReferenceID']) ? (int)$data['ReferenceID'] : 0,
                is_numeric($data['UserID']) ? (int)$data['UserID'] : 0,
                $startTime
            );

            if ($currentGroupTime === 0) {
                continue;
            }

            if ($nextPossibleTime === 0) {
                $nextPossibleTime = $currentGroupTime;
            } else {
                $nextPossibleTime = min($nextPossibleTime, $currentGroupTime);
            }
        }

        return $nextPossibleTime;
    }

    /**
     * @param string $conditionType
     * @return bool
     */
    public static function isConditionTypeTemporal(string $conditionType): bool
    {
        $tempConditions = [
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_NOT,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_BEFORE,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_AFTER,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_WEEKDAY,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_MONTH,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_DAY_OF_MONTH,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_IS_HOLIDAY,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_BEFORE_TIME,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_TODAY_AFTER_TIME,
            // custom fields
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_FIELD_BEFORE,
            CampaignsProcessFlow::PROCESSFLOW_CONDITION_FIELD_AFTER,
        ];

        if (!in_array($conditionType, $tempConditions)) {
            return false;
        }
        return true;
    }

    /**
     * @param array<array{
     *          op: string,
     *          field: int,
     *          value: string|array<int>,
     *          value2: mixed
     *      }> $conditionGroup
     * @return bool
     */
    private static function isConditionGroupValid($conditionGroup): bool
    {
        foreach ($conditionGroup as $condition) {
            if (!self::isConditionTypeTemporal($condition['op'])) {
                return false;
            }
        }
        return true;
    }

    public static function CalcTransactionalWait($state, $now, $SubscriberID, $ReferenceID, $UserID)
    {
        $TimeToSend = $now;

        //we do not allow a send time more than 5 years into the future
        //also fallback for impossible dates like the 31. of November
        $max_delay = strtotime('+5 years', $TimeToSend);

        if (
            in_array($state['delayType'], [
                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD,
                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_HOURS,
                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MINUTES,
                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_DAYS,
                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_WEEKS,
                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MONTHS,
                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_YEARS,
                CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_BIRTHDAY,
            ])
        ) {
            $ArrayCustomFields = TransactionEmails::GetCachedCustomFields($UserID);
            $TimeToSend = CustomFields::GetCustomFieldData(
                $UserID,
                $SubscriberID,
                $ArrayCustomFields[$state['delayField']],
                $ReferenceID
            );

            if (!is_numeric($TimeToSend)) {
                //invalid datetime in custom field
                return 0; // continue immetiatly -> RunNextTransactional()
            }
        }

        $sign = '+';
        $TriggerTime = intval($state['delayTime']);
        if ($TriggerTime < 0) {
            $sign = '-';
            $TriggerTime = abs($TriggerTime);
        }

        switch ($state['delayType']) {
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MINUTES:
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MINUTES:
                $TimeToSend = strtotime($sign . $TriggerTime . ' minutes', $TimeToSend);
                break;
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_HOURS:
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_HOURS:
                $TimeToSend = strtotime($sign . $TriggerTime . ' hours', $TimeToSend);
                break;
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_DAYS:
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_DAYS:
                $TimeToSend = strtotime($sign . $TriggerTime . ' days', $TimeToSend);
                break;
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_WEEKS:
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_WEEKS:
                $TimeToSend = strtotime($sign . $TriggerTime . ' weeks', $TimeToSend);
                break;
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MONTHS:
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_MONTHS:
                $TimeToSend = strtotime($sign . $TriggerTime . ' months', $TimeToSend);
                break;
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_YEARS:
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_YEARS:
                $TimeToSend = strtotime($sign . $TriggerTime . ' years', $TimeToSend);
                break;
            default:
                break;
        }

        if ($state['delayType'] == CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_BIRTHDAY) {
            // wait until next birthday (from field birthday)

            $TimeDelay = intval($state['delaySecondsOfDay']);
            $delaySecondsOfDay = '00:00:00';
            if ($TimeDelay >= 0 && $TimeDelay <= 24 * 60 * 60) {
                $hours = sprintf('%02d', intval($TimeDelay / 3600));
                $minutes = date('i', $TimeDelay);
                $delaySecondsOfDay = "$hours:$minutes:00";
            }

            // calc birthday this year from date defined in custom fields
            $TimeToSend = strtotime(date('Y', $now) . date("-m-d $delaySecondsOfDay", $TimeToSend));

            // if day is gone this year, send next year at same hour
            // (but send email, if today is the subscribers birthday even if the current time is past the delaySecondsOfDay )
            if ($TimeToSend < strtotime(date('Y-m-d 00:00:00', $now))) {
                return strtotime('+1 year', $TimeToSend);
            }

            return $TimeToSend;
        } elseif ($state['delayType'] == CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CALENDAR) {
            $CheckDate = $TimeToSend;

            $delaySecondsOfDay = date("H:i:s", $TimeToSend);
            $TimeDelay = intval($state['delaySecondsOfDay']);
            if ($TimeDelay >= 0 && $TimeDelay <= 24 * 60 * 60) {
                // a send time is set
                $hours = sprintf('%02d', intval($TimeDelay / 3600));
                $minutes = date('i', $TimeDelay);
                $delaySecondsOfDay = "$hours:$minutes:00";

                //if the next holiday would be today and the send time is before the current time
                //start looking for the next holiday as of tomorrow
                if (strtotime("now $delaySecondsOfDay", $CheckDate) < $now) {
                    $CheckDate = strtotime("+1 day", $CheckDate);
                }
            }

            //get the next holiday including the check date
            $NextHoliday = ToolCalendar::GetNextHoliday($UserID, $state['calendarIDs'], $CheckDate, 5);

            if (!empty($NextHoliday)) {
                //we found a holiday, set the date
                $TimeToSend = strtotime("$NextHoliday $delaySecondsOfDay");
            } else {//
                //no holiday found within the next 5 years (misconfiguration)
                //fallback: send today/tomorrow at send time
                $TimeToSend = strtotime("now $delaySecondsOfDay", $CheckDate);
            }

            return $TimeToSend;
        } else {
            // get time delay

            $TimeDelay = intval($state['delaySecondsOfDay']);

            if ($TimeDelay >= 0 && $TimeDelay <= 24 * 60 * 60) {
                $hours = sprintf('%02d', intval($TimeDelay / 3600));
                $minutes = date('i', $TimeDelay);

                if (
                    strtotime(date("Y-m-d {$hours}:{$minutes}:00", $TimeToSend)) < strtotime(
                        date("Y-m-d H:i:s", $TimeToSend)
                    )
                ) {
                    //the current time (H:i) is later than the requested send time ($delaySecondsOfDay)
                    //set $delaySecondsOfDay and delay by 1 day
                    $TimeToSend = strtotime("+1 day {$hours}:{$minutes}:00", $TimeToSend);
                } else {
                    //set seconds of day
                    $TimeToSend = strtotime("now {$hours}:{$minutes}:00", $TimeToSend);
                }

                if ($TimeToSend < $now) {
                    //if a date in a custom field (+ delay) is in the past and has delaySecondsOfDay, send tomorrow from today
                    $TimeToSend = strtotime("+1 day {$hours}:{$minutes}:00", $now);
                }
            }

            //make sure the value from the cockpit is an array
            $state['delayMonth'] = (is_numeric(
                $state['delayMonth']
            )) ? array($state['delayMonth']) : $state['delayMonth'];
            $state['delayDayOfMonth'] = (is_numeric(
                $state['delayDayOfMonth']
            )) ? array($state['delayDayOfMonth']) : $state['delayDayOfMonth'];
            $state['delayDayOfWeek'] = (is_numeric(
                $state['delayDayOfWeek']
            )) ? array($state['delayDayOfWeek']) : $state['delayDayOfWeek'];

            $allowedMonths = array_values(array_intersect(range(1, 12), $state['delayMonth'] ?? []));
            $allowedDaysOfMonth = array_values(array_intersect(range(1, 31), $state['delayDayOfMonth'] ?? []));
            $allowedWeekdays = array_values(array_intersect(range(1, 7), $state['delayDayOfWeek'] ?? []));

            if (empty($allowedMonths)) {
                //no constraints for month set -> all months are allowed
                $allowedMonths = range(1, 12);
            }

            if (empty($allowedDaysOfMonth)) {
                //no constraints for days of month set -> all days are allowed
                $allowedDaysOfMonth = range(1, 31);
            }

            if (empty($allowedWeekdays)) {
                //no constraints for weekday set -> all weekdays are allowed
                $allowedWeekdays = range(1, 7);
            }

            while ($TimeToSend < $max_delay) {
                if (
                    in_array(date('n', $TimeToSend), $allowedMonths) &&
                    in_array(date('j', $TimeToSend), $allowedDaysOfMonth) &&
                    in_array(date('N', $TimeToSend), $allowedWeekdays) &&
                    (empty($state['calendarIDs']) || !ToolCalendar::IsHoliday(
                        $UserID,
                        $state['calendarIDs'],
                        $TimeToSend
                    ))
                ) {
                    // found valid time that fulfills waiting conditions
                    // !ToolCalendar::IsHoliday checks if time is a day in the calendar that the user may wants to exclude
                    break;
                }

                $TimeToSend = strtotime("+1 day", $TimeToSend);
            }
        }

        return $TimeToSend;
    }

    /**
     * Checks if the limit has been reached and returns status and time to send for RunTransactional()
     * @param $UserID
     * @param $CampaignID
     * @param $state
     * @param $timestamp
     *
     * @return array(bool passed through the limiter, int timestamp time to send)
     */
    public static function CalcTransactionalLimiter($UserID, $CampaignID, $state, $timestamp)
    {
        //check if limit has been reached, create/update/reset limiter
        [$LimitReached, $NextPeriod] = TransactionEmails::LimiterCalcLimitReached(
            $UserID,
            $CampaignID,
            $state,
            $timestamp
        );

        if ($LimitReached) {
            //limit has already been reached
            //requeue the subscriber at the start of the next period with STATUS_PENDING
            //the subscriber will run trough the same process at the beginning of the next period
            return [false, $NextPeriod];
        }

        //the limit has not been reached yet

        //calculate next possible time to send in the current period based on wait settings
        $TimeToSend = TransactionEmails::LimiterCalcNextTimeToSend($UserID, $state, $timestamp, $NextPeriod);

        //check if the time to send is in the current period
        //Note: $TimeToSend will be 0 if no time could be found inside the current period
        if (empty($TimeToSend)) {
            //no time to send could be found inside the current period
            //requeue the subscriber at the start of the next period with STATUS_PENDING
            //the subscriber will run trough the same process at the beginning of the next period
            return [false, $NextPeriod];
        }

        //the time to send is in the current period, the subscriber has passed the limiter

        //increase the limiter count for the current period
        db_query(
            "UPDATE {limiter} SET Count = Count + 1 WHERE RelOwnerUserID = :RelOwnerUserID " .
            "AND RelCampaignID = :RelCampaignID AND StateID = :StateID",
            [
                ':RelOwnerUserID' => $UserID,
                ':RelCampaignID' => $CampaignID,
                ':StateID' => $state['id'],
            ]
        );

        //set the time to send in the transactional queue and the status to STATUS_PENDING_AFTER
        //Note: the subscriber will wait at the wait/limiter state until $TimeToSend is reached
        //      then proceed with the next state
        return [true, $TimeToSend];
    }

    /**
     * Check if the limit of the current limiter period has been reached already
     * Creates database entry for the limiter if it doesn't exist yet
     * Updates database entry if period is over or has been changed and resets limit count
     * @param $UserID
     * @param $CampaignID
     * @param $State
     * @param $Timestamp
     *
     * @return array(bool LimitReached, int timestamp of start of next period)
     */
    public static function LimiterCalcLimitReached($UserID, $CampaignID, $State, $Timestamp)
    {
        switch ($State['limiterPeriod']) {
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_HOURS:
                $currentPeriod = strtotime(date("Y-m-d H:00:00", $Timestamp));
                $nextPeriod = strtotime("+1 hour", $currentPeriod);
                break;
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_DAYS:
                $currentPeriod = strtotime("now 00:00:00", $Timestamp);
                $nextPeriod = strtotime("+1 day", $currentPeriod);
                break;
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_WEEKS:
                $currentPeriod = strtotime("monday this week", $Timestamp);
                $nextPeriod = strtotime("+1 week", $currentPeriod);
                break;
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_MONTHS:
                $currentPeriod = strtotime(date("Y-m-01 00:00:00", $Timestamp));
                $nextPeriod = strtotime("+1 month", $currentPeriod);
                break;
            case CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_YEARS:
            default:
                $currentPeriod = strtotime(date("Y-01-01 00:00:00", $Timestamp));
                $nextPeriod = strtotime("+1 year", $currentPeriod);
                break;
        }

        //get the current status of the limiter

        $DBArray = kt_fetch_array(
            db_query(
                "SELECT * FROM {limiter} WHERE RelOwnerUserID = :RelOwnerUserID " .
                "AND RelCampaignID = :RelCampaignID AND StateID = :StateID",
                [
                    ':RelOwnerUserID' => $UserID,
                    ':RelCampaignID' => $CampaignID,
                    ':StateID' => $State['id'],
                ]
            )
        );

        if (empty($DBArray) || $DBArray['Timestamp'] != $currentPeriod) {
            //we did not find the limiter (first subscriber ever to run into this limiter) OR
            //the limiter period has passed or has been changed -> reset to current period with count 0

            db_query(
                "INSERT INTO {limiter} (RelOwnerUserID, RelCampaignID, StateID, Count, Timestamp) " .
                "VALUES (:RelOwnerUserID, :RelCampaignID, :StateID, :Count, :Timestamp) " .
                "ON DUPLICATE KEY UPDATE Count = :Count, Timestamp = :Timestamp",
                array(
                    ':RelOwnerUserID' => $UserID,
                    ':RelCampaignID' => $CampaignID,
                    ':StateID' => $State['id'],
                    ':Count' => 0,
                    ':Timestamp' => $currentPeriod,
                )
            );
            // limiter has just been reset, so limit cannot be reached yet
            return [false, $nextPeriod];
        }

        //check if the limit has been reached
        $LimitReached = ($DBArray['Count'] >= $State['limiterValue']);

        return [$LimitReached, $nextPeriod];
    }

    /**
     * Calculate the next possible time to send within the current limiter period based on wait conditions (delayMonth, delayDayOfWeek,..., excludeHoliday)
     * @param $UserID
     * @param $state : the wait state containing the limiter
     * @param $TimeToSend : timestamp from which to start calculating
     * @param $NextPeriod : timestamp start of next period
     *
     * @return false|int: false only if strtotime() fails, a timestamp within the current limiter period or 0 if time to send is not in the current period
     */
    public static function LimiterCalcNextTimeToSend($UserID, $state, $TimeToSend, $NextPeriod)
    {
        //validate limiter settings

        //make sure the value from the cockpit is an array
        $state['delayHours'] = (is_numeric($state['delayHours'])) ? array($state['delayHours']) : $state['delayHours'];
        $state['delayDayOfMonth'] = (is_numeric(
            $state['delayDayOfMonth']
        )) ? array($state['delayDayOfMonth']) : $state['delayDayOfMonth'];
        $state['delayDayOfWeek'] = (is_numeric(
            $state['delayDayOfWeek']
        )) ? array($state['delayDayOfWeek']) : $state['delayDayOfWeek'];
        $state['delayWeek'] = (is_numeric($state['delayWeek'])) ? array($state['delayWeek']) : $state['delayWeek'];
        $state['delayMonth'] = (is_numeric($state['delayMonth'])) ? array($state['delayMonth']) : $state['delayMonth'];

        $allowedHours = array_values(array_intersect(range(0, 23), $state['delayHours'] ?? []));
        $allowedDaysOfMonth = array_values(array_intersect(range(1, 31), $state['delayDayOfMonth'] ?? []));
        $allowedWeekdays = array_values(array_intersect(range(1, 7), $state['delayDayOfWeek'] ?? []));
        $allowedWeeks = array_values(array_intersect(range(1, 52), $state['delayWeek'] ?? []));
        $allowedMonths = array_values(array_intersect(range(1, 12), $state['delayMonth'] ?? []));

        if (empty($allowedHours)) {
            //no constraints for hours set -> all hours are allowed
            $allowedHours = range(0, 23);
        }
        if (empty($allowedDaysOfMonth)) {
            //no constraints for days of month set -> all days are allowed
            $allowedDaysOfMonth = range(1, 31);
        }
        if (empty($allowedWeekdays)) {
            //no constraints for weekday set -> all weekdays are allowed
            $allowedWeekdays = range(1, 7);
        }
        if (empty($allowedWeeks)) {
            //no constraints for weeks set -> all weeks are allowed
            $allowedWeeks = range(1, 53);
        }
        if (empty($allowedMonths)) {
            //no constraints for month set -> all months are allowed
            $allowedMonths = range(1, 12);
        }

        //find the next possible time to send based on the limiter conditions

        //Note: we only have to find a time to send in the current period
        //      if the time to send is greater than the start of the next period, we can stop
        while ($TimeToSend < $NextPeriod) {
            if (
                in_array(date('G', $TimeToSend), $allowedHours) &&
                in_array(date('j', $TimeToSend), $allowedDaysOfMonth) &&
                in_array(date('N', $TimeToSend), $allowedWeekdays) &&
                in_array(date('W', $TimeToSend), $allowedWeeks) &&
                in_array(date('n', $TimeToSend), $allowedMonths)
            ) {
                //the conditions for hour, day, weekday, week and months are met

                //check if the time to send is excluded from calendars
                if (
                    empty($state['calendarIDs']) || !ToolCalendar::IsHoliday(
                        $UserID,
                        $state['calendarIDs'],
                        $TimeToSend
                    )
                ) {
                    //there is no calendar specified or the date is not in any calendar
                    //we found our time to send
                    break;
                } else {
                    //the calculated $TimeToSend is an excluded day from a calendar
                    //move the possible time to send to the next day at 00:00:00
                    //Note: we do not need to check the remaining hours of the excluded day
                    //      but have to check every possible hour of the next day
                    $firstHour = min($allowedHours);
                    $TimeToSend = strtotime("+1 day $firstHour:00:00");
                }
            } else {
                //the conditions for hour, day, weekday, week and months are NOT met

                // if all hours are allowed we can increase by one day otherwise by one hour
                $time = (count($allowedHours) == 24) ? "+1 day" : date(
                    "Y-m-d H:00:00",
                    strtotime("+1 hour", $TimeToSend)
                );
                $TimeToSend = strtotime($time, $TimeToSend);
            }
        }

        //we either found a time to send in the current period or the time to send has exceeded the period

        if ($TimeToSend >= $NextPeriod) {
            //no time to send found in the current period
            return 0;
        }

        return $TimeToSend;
    }

    /**
     * @return int[] an array of subscriber ids that have been moved
     * @throws Exception
     */
    public static function getSubscriberIdsInState(
        int $userId,
        int $campaignId,
        int $stateId
    ): array {
        $tableName = Campaigns::GetQueueTableNameById($userId, $campaignId);

        return db_query(
            <<<SQL
                SELECT RelSubscriberID
                FROM {{$tableName}}
                WHERE RelOwnerUserID = :RelOwnerUserID 
                  AND RelAutoResponderID = :RelAutoResponderID 
                  AND StateID = :StateID
            SQL,
            [
                ':RelOwnerUserID' => $userId,
                ':RelAutoResponderID' => $campaignId,
                ':StateID' => $stateId,
            ]
        )->fetchAll(\PDO::FETCH_COLUMN);
    }

    /**
     * @throws Exception
     */
    public static function moveSubscribersInState(int $userId, int $campaignId, int $oldStateId, int $newStateId): void
    {
        $tableName = Campaigns::GetQueueTableNameById($userId, $campaignId);

        db_query(
            <<<SQL
                UPDATE {{$tableName}}
                SET StateID = :NewStateId, StatusEnum = :StatusEnum, TimeToSend = :timeToSend
                WHERE RelOwnerUserID = :RelOwnerUserID 
                  AND RelAutoResponderID = :RelAutoResponderID 
                  AND StateID = :OldStateID
            SQL,
            [
                ':NewStateId' => $newStateId,
                ':StatusEnum' => self::STATUS_PENDING,
                ':timeToSend' => time(),
                ':RelOwnerUserID' => $userId,
                ':RelAutoResponderID' => $campaignId,
                ':OldStateID' => $oldStateId,
            ]
        );
    }
}

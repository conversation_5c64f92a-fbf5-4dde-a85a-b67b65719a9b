<?php

namespace App\Klicktipp\MetaLabel;

use App\Klicktipp\MetaLabel\ValueObject\MetaLabelEntityLinksValueObject;

class MetaLabelLinkResolver
{
    public function getLinks(
        int $userId,
        string $id
    ): MetaLabelEntityLinksValueObject {
        return MetaLabelEntityLinksValueObject::create(
            $this->linkEdit($userId, $id)
        );
    }

    public function linkOverview(int $userId): string
    {
        return APP_URL . "app/label/overview/$userId";
    }

    public function linkCreate(int $userId): string
    {
        return APP_URL . "app/label/settings/$userId/create";
    }

    public function linkEdit(int $userId, string $id): string
    {
        return APP_URL . "app/label/settings/$userId/$id";
    }
}

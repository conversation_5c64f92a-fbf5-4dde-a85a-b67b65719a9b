<?php

namespace App\Klicktipp\MetaLabel\ValueObject\Request;

use App\Klicktipp\AngularApi\Exception\ApiInvalidArgumentTypeException;
use App\Klicktipp\AngularApi\Exception\ApiMissingArgumentException;

final class MetaLabelRequestUpdateFactory
{
    /**
     * @param array<mixed> $data
     * @return MetaLabelRequestUpdateValueObject
     * @throws ApiInvalidArgumentTypeException
     * @throws ApiMissingArgumentException
     */
    public static function create(array $data): MetaLabelRequestUpdateValueObject
    {
        $request = new MetaLabelRequestUpdateValueObject();

        if (!isset($data['entity'])) {
            throw new ApiMissingArgumentException('entity');
        }
        if (!is_array($data['entity'])) {
            throw new ApiInvalidArgumentTypeException('entity', 'array');
        }
        $entity = $data['entity'];

        if (!isset($entity['name'])) {
            throw new ApiMissingArgumentException('name');
        }
        if (!is_string($entity['name'])) {
            throw new ApiInvalidArgumentTypeException('name', 'string');
        }
        $request->name = $entity['name'];

        if (isset($entity['notes'])) {
            if (!is_string($entity['notes'])) {
                throw new ApiInvalidArgumentTypeException('notes', 'string');
            }
            $request->notes = $entity['notes'];
        }

        return $request;
    }
}

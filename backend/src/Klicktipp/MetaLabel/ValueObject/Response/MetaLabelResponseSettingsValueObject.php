<?php

namespace App\Klicktipp\MetaLabel\ValueObject\Response;

use App\Klicktipp\MetaLabel\ValueObject\MetaLabelEntityValueObject;

final class MetaLabelResponseSettingsValueObject
{
    public MetaLabelEntityValueObject $entity;
    public MetaLabelResponseSettingsLinksValueObject $links;

    public static function create(
        MetaLabelEntityValueObject $entity,
        MetaLabelResponseSettingsLinksValueObject $links
    ): self {
        $valueObject = new self();
        $valueObject->entity = $entity;
        $valueObject->links = $links;
        return $valueObject;
    }
}

<?php

namespace App\Klicktipp\MetaLabel\ValueObject\Response;

use App\Klicktipp\MetaLabel\ValueObject\MetaLabelEntityLinksValueObject;

final class MetaLabelResponseUpdateValueObject
{
    public int $id;
    public MetaLabelEntityLinksValueObject $links;

    public static function create(
        int $id,
        MetaLabelEntityLinksValueObject $links
    ): self {
        $valueObject = new self();
        $valueObject->id = $id;
        $valueObject->links = $links;
        return $valueObject;
    }
}

<?php

namespace App\Klicktipp\MetaLabel\ValueObject\Response;

use App\Klicktipp\AngularApi\ValueObject\CreateButtonValueObject;

final class MetaLabelResponseOverviewValueObject
{
    /** @var MetaLabelResponseOverviewEntityValueObject[] */
    public array $entities;
    /** @var CreateButtonValueObject[] */
    public array $createTypes;

    /**
     * @param array<MetaLabelResponseOverviewEntityValueObject> $entities
     * @param array<CreateButtonValueObject> $createTypes
     * @return self
     */
    public static function create(array $entities, array $createTypes): self
    {
        $valueObject = new self();
        $valueObject->entities = $entities;
        $valueObject->createTypes = $createTypes;
        return $valueObject;
    }
}

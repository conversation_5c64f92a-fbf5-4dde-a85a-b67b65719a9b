<?php

namespace App\Klicktipp\MetaLabel\ValueObject\Response;

use App\Klicktipp\MetaLabel\ValueObject\MetaLabelEntityLinksValueObject;

final class MetaLabelResponseOverviewEntityValueObject
{
    public int $id;
    public string $name;
    public MetaLabelEntityLinksValueObject $links;

    public static function create(
        int $id,
        string $name,
        MetaLabelEntityLinksValueObject $links
    ): self {
        $valueObject = new self();
        $valueObject->id = $id;
        $valueObject->name = $name;
        $valueObject->links = $links;
        return $valueObject;
    }
}

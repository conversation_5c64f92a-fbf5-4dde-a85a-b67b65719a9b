<?php

namespace App\Klicktipp\MetaLabel\Exception;

use Exception;
use Throwable;

abstract class MetaLabelException extends Exception
{
    private int $userId;
    private string $id;

    public function __construct(int $userId, string $id, string $message = '', Throwable $previous = null)
    {
        parent::__construct(
            $message ?: strtr(
                'Unknown exception for MetaLabel with id !id for user !uid',
                [
                    '!uid' => $userId,
                    '!id' => $id,
                ]
            ),
            0,
            $previous
        );
        $this->userId = $userId;
        $this->id = $id;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getId(): string
    {
        return $this->id;
    }
}

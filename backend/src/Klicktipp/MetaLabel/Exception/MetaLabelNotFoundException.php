<?php

namespace App\Klicktipp\MetaLabel\Exception;

class MetaLabelNotFoundException extends MetaLabelException
{
    public function __construct(int $userId, string $id)
    {
        parent::__construct(
            $userId,
            $id,
            strtr(
                'MetaLabel with id !id for user !uid not found.',
                [
                    '!id' => $id,
                    '!uid' => $userId,
                ]
            )
        );
    }
}

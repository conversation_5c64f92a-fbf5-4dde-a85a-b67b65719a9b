<?php

namespace App\Klicktipp\MetaLabel;

use App\Klicktipp\AngularApi\Validators\Exception\ApiValidationExceptionCollectionException;
use App\Klicktipp\MetaLabel\Validators\Exception\MetaLabelValidationExceptionInterface;
use App\Klicktipp\MetaLabel\Validators\MetaLabelValidatorInterface;
use App\Klicktipp\MetaLabels;

class MetaLabelValidator
{
    /** @var iterable<MetaLabelValidatorInterface> */
    private iterable $validators;

    /**
     * @param iterable<MetaLabelValidatorInterface> $validators
     */
    public function __construct(iterable $validators)
    {
        $this->validators = $validators;
    }

    /**
     * @param MetaLabels $metaLabel
     * @param array<string> $useValidatorsByClass
     * @return void
     * @throws ApiValidationExceptionCollectionException<MetaLabelValidationExceptionInterface>
     */
    public function validate(MetaLabels $metaLabel, array $useValidatorsByClass = []): void
    {
        if (empty($this->validators) || empty($useValidatorsByClass)) {
            return;
        }

        /** @var ApiValidationExceptionCollectionException<MetaLabelValidationExceptionInterface> $collection */
        $collection = new ApiValidationExceptionCollectionException();

        foreach ($this->validators as $validator) {
            if (!in_array(get_class($validator), $useValidatorsByClass, true)) {
                continue;
            }

            try {
                $validator->validate($metaLabel);
            } catch (MetaLabelValidationExceptionInterface $e) {
                $collection->addException($e);
            }
        }

        if ($collection->hasExceptions()) {
            throw $collection;
        }
    }
}

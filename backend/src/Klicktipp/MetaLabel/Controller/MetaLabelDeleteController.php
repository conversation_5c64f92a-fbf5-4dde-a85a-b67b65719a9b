<?php

namespace App\Klicktipp\MetaLabel\Controller;

use App\Klicktipp\AngularApi\Controller\ApiController;
use App\Klicktipp\AngularApi\Controller\Exception\ApiControllerException;
use App\Klicktipp\AngularApi\ValueObject\ApiResponseEntityDeleteSuccessValueObject;
use App\Klicktipp\AngularApi\ValueObject\Response\ApiResponseValueObject;
use App\Klicktipp\MetaLabel\MetaLabelControllerManager;
use Exception;
use stdClass;
use Symfony\Component\HttpFoundation\JsonResponse;

class MetaLabelDeleteController extends ApiController
{
    /**
     * @param MetaLabelControllerManager $manager
     * @param stdClass $account
     * @param string $id
     * @return JsonResponse
     */
    public function __invoke(
        MetaLabelControllerManager $manager,
        stdClass $account,
        string $id
    ): JsonResponse {
        try {
            $name = $manager->delete($account->uid, $id);
            return $this->json(
                ApiResponseValueObject::create(
                    ApiResponseEntityDeleteSuccessValueObject::create(
                        t('MetaLabel::Delete::Meta Label !name successfully deleted.', [
                            '!name' => $name
                        ])
                    )
                )
            );
        } catch (ApiControllerException $e) {
            return $this->getErrorResponse($e);
        } catch (Exception $e) {
            return $this->getExceptionResponse($e);
        }
    }
}

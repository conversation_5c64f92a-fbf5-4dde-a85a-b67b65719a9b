<?php

namespace App\Klicktipp\MetaLabel\Controller;

use App\Klicktipp\AngularApi\Controller\ApiController;
use App\Klicktipp\AngularApi\ValueObject\CreateButtonValueObject;
use App\Klicktipp\AngularApi\ValueObject\Response\ApiResponseValueObject;
use App\Klicktipp\MetaLabel\MetaLabelControllerManager;
use App\Klicktipp\MetaLabel\MetaLabelLinkResolver;
use App\Klicktipp\MetaLabel\ValueObject\Response\MetaLabelResponseOverviewValueObject;
use Exception;
use stdClass;
use Symfony\Component\HttpFoundation\JsonResponse;

class MetaLabelOverviewController extends ApiController
{
    /**
     * @param MetaLabelControllerManager $manager
     * @param MetaLabelLinkResolver $linkResolver
     * @param stdClass $account
     * @return JsonResponse
     */
    public function __invoke(
        MetaLabelControllerManager $manager,
        MetaLabelLinkResolver $linkResolver,
        stdClass $account
    ): JsonResponse {
        try {
            return $this->json(
                ApiResponseValueObject::create(
                    MetaLabelResponseOverviewValueObject::create(
                        $manager->getOverviewMetaLabels($account->uid),
                        [
                            CreateButtonValueObject::create(
                                t('MetaLabel::Overview::Button::Create field'),
                                $linkResolver->linkCreate($account->uid),
                            )
                        ]
                    )
                )
            );
        } catch (Exception $e) {
            return $this->getExceptionResponse($e);
        }
    }
}

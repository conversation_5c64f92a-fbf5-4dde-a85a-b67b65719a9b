<?php

namespace App\Klicktipp\MetaLabel\Controller;

use App\Klicktipp\AngularApi\Controller\ApiController;
use App\Klicktipp\AngularApi\Controller\Exception\ApiControllerException;
use App\Klicktipp\AngularApi\Validators\Exception\ApiValidationExceptionCollectionException;
use App\Klicktipp\MetaLabel\MetaLabelControllerManager;
use App\Klicktipp\MetaLabel\MetaLabelLinkResolver;
use App\Klicktipp\MetaLabel\ValueObject\Request\MetaLabelRequestUpdateFactory;
use App\Klicktipp\MetaLabel\ValueObject\Response\MetaLabelResponseUpdateValueObject;
use stdClass;
use Symfony\Component\HttpFoundation\JsonResponse;
use Exception;

class MetaLabelCreateController extends ApiController
{
    /**
     * @param MetaLabelControllerManager $manager
     * @param MetaLabelLinkResolver $linkResolver
     * @param stdClass $account
     * @param array<mixed> $data
     * @return JsonResponse
     */
    public function __invoke(
        MetaLabelControllerManager $manager,
        MetaLabelLinkResolver $linkResolver,
        stdClass $account,
        array $data
    ): JsonResponse {
        try {
            $request = MetaLabelRequestUpdateFactory::create($data);
            $metaLabel = $manager->create($account->uid, $request);
            return $this->json(
                MetaLabelResponseUpdateValueObject::create(
                    $metaLabel->GetData('MetaLabelID'),
                    $linkResolver->getLinks($account->uid, $metaLabel->GetData('MetaLabelID'))
                )
            );
        } catch (ApiValidationExceptionCollectionException $e) {
            return $this->getValidationErrorResponse($e);
        } catch (ApiControllerException $e) {
            return $this->getErrorResponse($e);
        } catch (Exception $e) {
            return $this->getExceptionResponse($e);
        }
    }
}

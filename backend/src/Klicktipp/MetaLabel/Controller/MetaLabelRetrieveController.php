<?php

namespace App\Klicktipp\MetaLabel\Controller;

use App\Klicktipp\AngularApi\Controller\ApiController;
use App\Klicktipp\AngularApi\Controller\Exception\ApiControllerException;
use App\Klicktipp\AngularApi\ValueObject\Response\ApiResponseValueObject;
use App\Klicktipp\MetaLabel\MetaLabelControllerManager;
use App\Klicktipp\MetaLabel\MetaLabelLinkResolver;
use App\Klicktipp\MetaLabel\ValueObject\MetaLabelEntityValueObject;
use App\Klicktipp\MetaLabel\ValueObject\Response\MetaLabelResponseSettingsLinksValueObject;
use App\Klicktipp\MetaLabel\ValueObject\Response\MetaLabelResponseSettingsValueObject;
use Exception;
use stdClass;
use Symfony\Component\HttpFoundation\JsonResponse;

class MetaLabelRetrieveController extends ApiController
{
    /**
     * @param MetaLabelControllerManager $manager
     * @param MetaLabelLinkResolver $linkResolver
     * @param stdClass $account
     * @param string $id
     * @return JsonResponse
     */
    public function __invoke(
        MetaLabelControllerManager $manager,
        MetaLabelLinkResolver $linkResolver,
        stdClass $account,
        string $id
    ): JsonResponse {
        try {
            $metaLabel = $manager->retrieve($account->uid, $id);
            $entity = MetaLabelEntityValueObject::create(
                $metaLabel,
                $linkResolver->getLinks($account->uid, $id)
            );
            $links = new MetaLabelResponseSettingsLinksValueObject();
            $links->overview = $linkResolver->linkOverview($account->uid);
            return $this->json(
                ApiResponseValueObject::create(
                    MetaLabelResponseSettingsValueObject::create(
                        $entity,
                        $links
                    )
                )
            );
        } catch (ApiControllerException $e) {
            return $this->getErrorResponse($e);
        } catch (Exception $e) {
            return $this->getExceptionResponse($e);
        }
    }
}

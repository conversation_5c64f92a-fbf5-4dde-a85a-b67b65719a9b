<?php

namespace App\Klicktipp\MetaLabel;

use App\Klicktipp\AngularApi\Controller\Exception\ApiControllerException;
use App\Klicktipp\AngularApi\Validators\Exception\ApiValidationExceptionCollectionException;
use App\Klicktipp\Exception\DatabaseException;
use App\Klicktipp\MetaLabel\Exception\MetaLabelNotFoundException;
use App\Klicktipp\MetaLabel\Validators\MetaLabelNameValidator;
use App\Klicktipp\MetaLabel\ValueObject\Request\MetaLabelRequestUpdateValueObject;
use App\Klicktipp\MetaLabel\ValueObject\Response\MetaLabelResponseOverviewEntityValueObject;
use App\Klicktipp\MetaLabels;
use Symfony\Component\HttpFoundation\Response;

class MetaLabelControllerManager
{
    private MetaLabelManager $metaLabelManager;
    private MetaLabelValidator $metaLabelValidator;
    private MetaLabelLinkResolver $metaLabelLinkResolver;

    public function __construct(
        MetaLabelManager $metaLabelManager,
        MetaLabelValidator $metaLabelValidator,
        MetaLabelLinkResolver $metaLabelLinkResolver
    ) {
        $this->metaLabelManager = $metaLabelManager;
        $this->metaLabelValidator = $metaLabelValidator;
        $this->metaLabelLinkResolver = $metaLabelLinkResolver;
    }

    /**
     * @param int $userId
     * @return MetaLabelResponseOverviewEntityValueObject[]
     * @throws DatabaseException
     */
    public function getOverviewMetaLabels(int $userId): array
    {
        $metaLabels = [];
        foreach ($this->metaLabelManager->getMetaLabels($userId) as $metaLabel) {
            $metaLabels[] = MetaLabelResponseOverviewEntityValueObject::create(
                (int)$metaLabel->GetData('MetaLabelID'),
                (string)$metaLabel->GetData('Name'),
                $this->metaLabelLinkResolver->getLinks($userId, (string)$metaLabel->GetData('MetaLabelID'))
            );
        }
        return $metaLabels;
    }

    /**
     * @param int $userId
     * @param MetaLabelRequestUpdateValueObject $data
     * @return MetaLabels
     * @throws ApiValidationExceptionCollectionException
     * @throws DatabaseException
     */
    public function create(
        int $userId,
        MetaLabelRequestUpdateValueObject $data
    ): MetaLabels {
        $metaLabel = $this->metaLabelManager->instanciateNewField($userId);
        $metaLabel
            ->set('Name', $data->name)
            ->set('Description', $data->notes)
            ->set('RelOwnerUserID', $userId);
        $this->metaLabelValidator->validate($metaLabel, [
            MetaLabelNameValidator::class
        ]);
        $this->metaLabelManager->update($metaLabel);
        return $metaLabel;
    }

    /**
     * @param int $userId
     * @param string $id
     * @param MetaLabelRequestUpdateValueObject $data
     * @return MetaLabels
     * @throws ApiControllerException
     * @throws ApiValidationExceptionCollectionException
     * @throws DatabaseException
     */
    public function update(
        int $userId,
        string $id,
        MetaLabelRequestUpdateValueObject $data
    ): MetaLabels {
        try {
            $metaLabel = $this->metaLabelManager->getMetaLabel($userId, $id);
            $metaLabel
                ->set('Name', $data->name)
                ->set('Description', $data->notes);
            $this->metaLabelValidator->validate($metaLabel, [
                MetaLabelNameValidator::class
            ]);
            $this->metaLabelManager->update($metaLabel);
            return $metaLabel;
        } catch (MetaLabelNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'metalabel-update-failed',
                t('MetaLabel::Update::Error::The Meta Label could not be updated.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }
    }

    /**
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function retrieve(int $userId, string $id): MetaLabels
    {
        try {
            if (empty($id)) {
                $entity = $this->metaLabelManager->instanciateNewField($userId);
            } else {
                $entity = $this->metaLabelManager->getMetaLabel($userId, $id);
            }
            return $entity;
        } catch (MetaLabelNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'metalabel-not-found',
                t('MetaLabel::Retrieve::Error::The Meta Label could not be found.'),
                Response::HTTP_NOT_FOUND
            );
        }
    }

    /**
     * @throws ApiControllerException
     * @throws DatabaseException
     */
    public function delete(int $userId, string $id): string
    {
        try {
            $metaLabel = $this->metaLabelManager->getMetaLabel($userId, $id);
            $this->metaLabelManager->delete($metaLabel);
            return $metaLabel->GetData('Name');
        } catch (MetaLabelNotFoundException $e) {
            throw new ApiControllerException(
                $e->getMessage(),
                'metalabel-delete-failed',
                t('MetaLabel::Delete::Error::The Meta Label does not exist anymore.'),
                Response::HTTP_NOT_ACCEPTABLE
            );
        }
    }
}

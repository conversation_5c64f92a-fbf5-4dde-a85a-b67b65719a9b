<?php

namespace App\Klicktipp\MetaLabel\Validators;

use App\Klicktipp\Exception\DatabaseException;
use App\Klicktipp\MetaLabel\MetaLabelManager;
use App\Klicktipp\MetaLabel\Validators\Exception\MetaLabelValidationDuplicateNameException;
use App\Klicktipp\MetaLabel\Validators\Exception\MetaLabelValidationEmptyNameException;
use App\Klicktipp\MetaLabel\Validators\Exception\MetaLabelValidationInternalErrorException;
use App\Klicktipp\MetaLabel\Validators\Exception\MetaLabelValidationNameLengthExceededException;
use App\Klicktipp\MetaLabels;

class MetaLabelNameValidator implements MetaLabelValidatorInterface
{
    private MetaLabelManager $manager;

    public function __construct(MetaLabelManager $manager)
    {
        $this->manager = $manager;
    }

    public function validate(MetaLabels $entity): void
    {
        $name = $entity->GetData('Name');

        if (empty($name)) {
            throw new MetaLabelValidationEmptyNameException();
        }

        if (strlen($name) > 250) {
            throw new MetaLabelValidationNameLengthExceededException();
        }

        try {
            $userId = (int)$entity->GetData('RelOwnerUserID');
            $entityId = (int)$entity->GetData('MetaLabelID');
            $existingId = $this->manager->checkDuplicateName($userId, $name);
            if (!empty($existingId) && $existingId !== $entityId) {
                throw new MetaLabelValidationDuplicateNameException();
            }
        } catch (DatabaseException $e) {
            throw new MetaLabelValidationInternalErrorException();
        }
    }

    public function eligible(MetaLabels $entity): bool
    {
        return true;
    }
}

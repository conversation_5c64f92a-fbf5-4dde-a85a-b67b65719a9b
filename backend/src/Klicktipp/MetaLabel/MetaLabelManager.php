<?php

namespace App\Klicktipp\MetaLabel;

use App\Klicktipp\MetaLabel\Exception\MetaLabelNotFoundException;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\Exception\DatabaseException;
use Doctrine\DBAL\Exception as DoctrineDBALException;

class MetaLabelManager
{
    /**
     * @param int $userId
     * @param string $id
     * @return MetaLabels
     * @throws DatabaseException
     * @throws MetaLabelNotFoundException
     */
    public function getMetaLabel(int $userId, string $id): MetaLabels
    {
        try {
            $entity = MetaLabels::FromID($userId, $id);
        } catch (DoctrineDBALException $e) {
            throw new DatabaseException($e);
        }

        if (!$entity instanceof MetaLabels) {
            throw new MetaLabelNotFoundException($userId, $id);
        }
        return $entity;
    }

    /**
     * @param int $userId
     * @return array<MetaLabels>
     * @throws DatabaseException
     */
    public function getMetaLabels(int $userId): array
    {
        try {
            $entities = MetaLabels::getMetaLabels($userId);
        } catch (DoctrineDBALException $e) {
            throw new DatabaseException($e);
        }
        return $entities;
    }

    /**
     * @param int $userId
     * @return MetaLabels
     */
    public function instanciateNewField(int $userId): MetaLabels
    {
        return MetaLabels::createNewInstance($userId);
    }

    /**
     * @param MetaLabels $metaLabel
     * @return MetaLabels
     * @throws DatabaseException
     */
    public function update(MetaLabels $metaLabel): MetaLabels
    {
        try {
            $metaLabel->save();
        } catch (DoctrineDBALException $e) {
            throw new DatabaseException($e);
        }

        return $metaLabel;
    }

    /**
     * @param MetaLabels $apiKey
     * @return void
     * @throws DatabaseException
     */
    public function delete(MetaLabels $apiKey): void
    {
        try {
            $apiKey->delete();
        } catch (DoctrineDBALException $e) {
            throw new DatabaseException($e);
        }
    }

    /**
     * @throws DatabaseException
     */
    public function checkDuplicateName(int $userId, string $name): int
    {
        try {
            return MetaLabels::CheckDuplicateName($userId, $name);
        } catch (DoctrineDBALException $e) {
            throw new DatabaseException($e);
        }
    }
}

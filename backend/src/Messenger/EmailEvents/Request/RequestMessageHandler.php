<?php

namespace App\Messenger\EmailEvents\Request;

use App\Klicktipp\CustomFields;
use App\Klicktipp\HtmlFilter;
use App\Klicktipp\Lists;
use App\Klicktipp\Requests;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\TransactionEmails;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

class RequestMessageHandler implements MessageHandlerInterface
{
    private HtmlFilter $htmlFilter;

    public function __construct(HtmlFilter $htmlFilter)
    {
        $this->htmlFilter = $htmlFilter;
    }

    public function __invoke(RequestMessage $message): void
    {
        $bodyPartToAnalyze = $message->bodyPlain;
        if (empty($bodyPartToAnalyze)) {
            $bodyPartToAnalyze = $this->htmlFilter->htmlToText($message->bodyHtml);
        }

        $request = Requests::FromEmailAddress($message->to);
        if (!$request instanceof Requests) {
            watchdog(
                'email-events',
                'request not found',
                ['!message' => print_r($message, true)],
                WATCHDOG_WARNING,
            );
            return;
        }

        $requestData = $request->GetData();

        if (str_contains($message->subject, $requestData['ReqByEmailSubscriptionCommand'])) {
            $this->subscribe(
                $requestData,
                $message->from,
                $bodyPartToAnalyze,
            );
            return;
        }
        if (str_contains($message->subject, $requestData['ReqByEmailUnsubscriptionCommand'])) {
            $this->unsubscribe(
                $requestData,
                $message->from,
            );
            return;
        }

        watchdog(
            'email-events',
            'no command found',
            ['!message' => print_r($message, true)],
            WATCHDOG_WARNING,
        );
    }

    /**
     * @param array<string, scalar> $request
     */
    private function subscribe(array $request, string $from, string $content): void
    {
        $SubscriberEmailAddress = $from;
        $SubscriberListID = $request['RelListID'];

        //TODO DEV-2414 ReferenceID shall come from $ArrayRequest
        $ReferenceID = 0;

        /** @var array{RelOwnerUserID: int, ListID: int} $ArraySubscriberList */
        $ArraySubscriberList = Lists::RetrieveList(
            ['*'],
            [
                'RelOwnerUserID' => $request['RelOwnerUserID'],
                'ListID' => $SubscriberListID,
            ],
            true,
        );
        if (empty($ArraySubscriberList)) {
            // impossible!
            watchdog(
                'email-events',
                'list not found',
                [
                    '!listid' => $SubscriberListID,
                    '!RelOwnerUserID' => $request['RelOwnerUserID'],
                ],
                WATCHDOG_ERROR,
            );
            return;
        }

        // extract parameters from body
        // every line in the body using "key = value\n" will go into a different custom field,
        // if "key" is defined as a custom field in this list
        // special case: if a custom field with type=line,validation=email is configured,
        // then this is taken as the subscriber email address
        $ArrayOtherFields = [];
        $lines = explode("\n", $content);
        foreach ($lines as $l) {
            $l = quoted_printable_decode($l);
            if (($pos = strpos($l, '=')) === false) {
                continue;
            }
            // split into key and value
            // key is everything before the first '=', value is everything after the first '='
            $key = check_plain(trim(substr($l, 0, $pos), " \t\n\r\0\x0B\xA0"));
            $value = check_plain(trim(substr($l, $pos + 1)));

            if (empty($key)) {
                continue;
            }

            /** @var array{FieldTypeEnum: int, CustomFieldID: int} $ArrayCustomField */
            $ArrayCustomField = CustomFields::RetrieveCustomFieldByName(
                CustomFields::REQUESTTYPE_REQUESTS,
                $key,
                $ArraySubscriberList['RelOwnerUserID'],
            );
            if ($ArrayCustomField) {
                // matching custom field found
                if (
                    $ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATE
                    && filter_var($value, FILTER_VALIDATE_INT) === false
                ) {
                    $value = CustomFields::ConvertCustomFieldDataFromWidget(
                        $value,
                        CustomFields::WIDGET_DATE_LOCALE,
                    );
                }
                $ArrayOtherFields['CustomField' . $ArrayCustomField['CustomFieldID']] = $value;
                if ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_EMAIL) {
                    // configured as email, so use this as the subscriber email address
                    $SubscriberEmailAddress = $value;
                }
            }
        }

        // SUBSCRIBE
        $subscribeResult = Subscribers::Subscribe([
            'UserID' => $ArraySubscriberList['RelOwnerUserID'],
            'ListInformation' => $ArraySubscriberList,
            'OptInSubscribeTo' => $request['AssignTagID'],
            'EmailAddress' => $SubscriberEmailAddress,
            'IPAddress' => '0.0.0.0 - By Email Request',
            'SubscriptionReferrer' => '',
            'OtherFields' => $ArrayOtherFields,
            'UpdateIfUnsubscribed' => true,
            'SendConfirmationEmail' => true,
            'UpdateStatistics' => true,
            'TriggerAutoResponders' => true,
        ]);


        if (!$subscribeResult[0]) {
            watchdog(
                'email-events',
                'Subscription failed',
                [
                    '!request' => print_r($request, true),
                    '!list' => print_r($ArraySubscriberList, true),
                    '!return' => print_r($subscribeResult, true),
                ],
                WATCHDOG_ERROR,
            );
            return;
        }

        // call the pending/thankyou url with all parameters
        $FullSubscriber = Subscribers::RetrieveFullSubscriber(
            $ArraySubscriberList['RelOwnerUserID'],
            $subscribeResult[1],
            $ReferenceID,
        );
        if ($FullSubscriber) {
            Subscribers::TagSubscriber(
                $FullSubscriber['RelOwnerUserID'],
                $FullSubscriber['SubscriberID'],
                $request['SmartTagID'],
                $ReferenceID,
                true,
            );
            TransactionEmails::RegisterAutomations(
                $FullSubscriber['RelOwnerUserID'],
                $FullSubscriber['SubscriberID'],
                $ReferenceID,
            );

            $url = Lists::GetSubscriptionRedirect(
                $ArraySubscriberList['RelOwnerUserID'],
                $FullSubscriber,
                $ArraySubscriberList,
                $ReferenceID,
            );
            drupal_http_request($url);
        }
    }

    /**
     * @param array<string, mixed> $request
     */
    private function unsubscribe(array $request, string $from): void
    {
        if (!Subscribers::ValidateEmailAddress($from)) {
            watchdog(
                'email-events',
                'Unsubscription failed. invalid email',
                [
                    '!email' => $from,
                    '!request' => print_r($request, true),
                ],
                WATCHDOG_WARNING
            );
            return;
        }

        [$success, $error] = Subscription::UnsubscribeSubscription(
            $request['RelOwnerUserID'],
            $from,
            Subscription::SUBSCRIPTIONTYPE_EMAIL,
        );

        if (!$success) {
            watchdog(
                'email-events',
                'Unsubscription failed',
                [
                    '!error' => $error,
                    '!email' => $from,
                    '!request' => print_r($request, true),
                ],
                WATCHDOG_ERROR
            );
        }
    }
}

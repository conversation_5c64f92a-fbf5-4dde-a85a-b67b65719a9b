<?php

namespace App\Controller\Api;

use App\Klicktipp\CampaignsProcessFlow;
use Doctrine\DBAL\Exception;
use ServicesException;
use stdClass;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

/**
 * API-Endpoints for Campaign Newsletter Email
 */
class CampaignProcessFlowController extends AbstractController
{
    /**
     * Retrieve all necessary data for the automation overview dialog
     *
     * @throws ServicesException|Exception
     */
    public function overview(stdClass $account): array
    {
        return CampaignsProcessFlow::angularApiOverview($account);
    }

    /**
     * Retrieve all necessary data for the automation settings/view/sentdate dialog
     *
     * @throws ServicesException
     */
    public function settingsRetrieve(stdClass $account, int $id): array
    {
        return CampaignsProcessFlow::angularApiSettingsRetrieve($account, $id);
    }

    /**
     * Create an automation
     *
     * @throws ServicesException
     */
    public function create(stdClass $account, array $data): array
    {
        return CampaignsProcessFlow::angularApiCreate($account, $data);
    }

    /**
     * Save the automation settings
     *
     * @throws ServicesException
     */
    public function settingsSave(stdClass $account, int $id, array $data): array
    {
        return CampaignsProcessFlow::angularApiSettingsSave($account, $id, $data);
    }

    /**
     * Stop the automation for editing
     * @throws ServicesException
     */
    public function cancelSending(stdClass $account, int $id): array
    {
        return CampaignsProcessFlow::angularApiCancelSending($account, $id);
    }

    /**
     * Start or set the start date of the automation
     * @throws ServicesException
     */
    public function sendDateSave(stdClass $account, int $id, array $data): array
    {
        return CampaignsProcessFlow::angularApiSetSendDate($account, $id, $data);
    }

    /**
     * Retrieve number of contacts that will start the automation
     * @throws ServicesException
     */
    public function sendDateEstimate(stdClass $account, int $id, array $data): array
    {
        return CampaignsProcessFlow::angularApiEstimateRecipients($account, $id, $data);
    }

    /**
     * Delete an automation
     * @throws ServicesException
     */
    public function delete(stdClass $account, int $id): array
    {
        return CampaignsProcessFlow::angularApiDelete($account, $id);
    }

    /**
     * Retrieve all entities that use the SmartTags of a campaign
     * @throws ServicesException
     */
    public function dependencies(stdClass $account, int $id): array
    {
        return CampaignsProcessFlow::angularApiGetDependencies($account, $id);
    }
}

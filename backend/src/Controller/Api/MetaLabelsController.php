<?php

namespace App\Controller\Api;

use App\Klicktipp\MetaLabels;
use stdClass;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

/**
 * API-Endpoints for Meta Labels
 */
class MetaLabelsController extends AbstractController
{
    /**
     * Retrieve all meta labels
     */
    public function index_advanced(stdClass $account): array
    {
        return MetaLabels::angularApiIndexAdvanced($account);
    }
}

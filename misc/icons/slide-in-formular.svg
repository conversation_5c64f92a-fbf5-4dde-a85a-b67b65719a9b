<svg width="173" height="173" viewBox="0 0 173 173" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_21_14)">
<rect x="12.7734" y="8.01953" width="148" height="148" rx="20" fill="url(#paint0_linear_21_14)" shape-rendering="crispEdges"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M94.9482 66.0195C94.9482 61.6445 91.3232 58.0195 86.9482 58.0195H54.9482C50.4482 58.0195 46.9482 61.6445 46.9482 66.0195V98.0195C46.9482 102.52 50.4482 106.02 54.9482 106.02H86.9482C91.3232 106.02 94.9482 102.52 94.9482 98.0195V89.3987H116.31L109.466 96.3362C108.247 97.4612 108.247 99.4299 109.466 100.555C110.591 101.774 112.56 101.774 113.685 100.555L125.685 88.5549C126.903 87.4299 126.903 85.4612 125.685 84.3362L113.685 72.3362C112.56 71.1174 110.591 71.1174 109.466 72.3362C108.247 73.4612 108.247 75.4299 109.466 76.5549L116.31 83.3987H94.9482V66.0195ZM88.9482 83.3987V74.0195H52.9482V98.0195C52.9482 99.1445 53.8232 100.02 54.9482 100.02H86.9482C87.9482 100.02 88.9482 99.1445 88.9482 98.0195V89.3987H81.6221C79.9346 89.3987 78.6221 88.0862 78.6221 86.3987C78.6221 84.8049 79.9346 83.3987 81.6221 83.3987H88.9482Z" fill="#6E779A"/>
</g>
<defs>
<filter id="filter0_d_21_14" x="0.773438" y="0.0195312" width="172" height="172" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_21_14"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_21_14" result="shape"/>
</filter>
<linearGradient id="paint0_linear_21_14" x1="86.7734" y1="8.01953" x2="86.7734" y2="156.02" gradientUnits="userSpaceOnUse">
<stop stop-color="#FEFEFE"/>
<stop offset="1" stop-color="#F0F0F0"/>
</linearGradient>
</defs>
</svg>

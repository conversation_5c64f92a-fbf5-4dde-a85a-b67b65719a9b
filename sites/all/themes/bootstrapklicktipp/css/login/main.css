@import url(https://use.typekit.net/tju7zqi.css);
.glyph-action:before {
    content: "\E917";
}
.glyph-choice:before {
    content: "\E916";
}
.glyph-delay:before,
.glyph-timer:before,
.glyph-wait:before {
    content: "\E90E";
}
.glyph-arrow-up:before {
    content: "\E91C";
}
.glyph-arrow-right:before {
    content: "\E91B";
}
.glyph-arrow-down:before {
    content: "\E919";
}
.glyph-arrow-left:before {
    content: "\E91A";
}
.glyph-arrow-up-bold:before {
    content: "\E91C";
}
.glyph-arrow-right-bold:before {
    content: "\E91B";
}
.glyph-arrow-down-bold:before {
    content: "\E919";
}
.glyph-arrow-left-bold:before {
    content: "\E91A";
}
.glyph-arrow-left-light:before {
    content: "\EA02";
}
.glyph-arrow-right-light:before {
    content: "\EA03";
}
.glyph-checkmark-bold:before,
.glyph-checkmark:before {
    content: "\E91D";
}
.glyph-plus-bold:before,
.glyph-plus:before {
    content: "\E920";
}
.glyph-minus-bold:before,
.glyph-minus:before {
    content: "\E91F";
}
.glyph-cross-bold:before,
.glyph-cross:before {
    content: "\E91E";
}
.glyph-cr-play:before {
    content: "\E928";
}
.glyph-cr-plus:before {
    content: "\E929";
}
.glyph-cr-minus:before {
    content: "\E925";
}
.glyph-cr-checkmark:before {
    content: "\E924";
}
.glyph-sq-arrow-down:before {
    content: "\E938";
}
.glyph-sq-arrow-left:before {
    content: "\E939";
}
.glyph-sq-arrow-right:before {
    content: "\E93A";
}
.glyph-sq-arrow-up:before {
    content: "\E93B";
}
.glyph-sq-arrow-cross:before {
    content: "\E93C";
}
.glyph-sq-arrow-plus:before {
    content: "\E93F";
}
.glyph-sq-arrow-minus:before {
    content: "\E93E";
}
.glyph-happy:before {
    content: "\E940";
}
.glyph-neutral:before {
    content: "\E9341";
}
.glyph-sad:before {
    content: "\E942";
}
.glyph-triangle-down:before {
    content: "\E94C";
}
.glyph-triangle-left:before {
    content: "\E94D";
}
.glyph-triangle-right:before {
    content: "\E94E";
}
.glyph-triangle-up:before {
    content: "\E94F";
}
.glyph-pointer-up:before {
    content: "\E96D";
}
.glyph-pointer-right:before {
    content: "\E96C";
}
.glyph-pointer-down:before {
    content: "\E96A";
}
.glyph-pointer-left:before {
    content: "\E96B";
}
.glyph-pointer-up-2:before {
    content: "\E95E";
}
.glyph-pointer-right-2:before {
    content: "\E95D";
}
.glyph-pointer-down-2:before {
    content: "\E95B";
}
.glyph-pointer-left-2:before {
    content: "\E95C";
}
.glyph-cursor-move:before {
    content: "\E92C";
}
.glyph-resize:before {
    content: "\E932";
}
.glyph-maximize:before,
.glyph-minimize:before {
    content: "\E92C";
}
.glyph-star:before {
    content: "\E60F";
}
.glyph-heart:before {
    content: "\E60D";
}
.glyph-link:before {
    content: "\E618";
}
.glyph-save:before {
    content: "\E918";
}
.glyph-hyperlink:before {
    content: "\E618";
}
.glyph-edit:before {
    content: "\E981";
}
.glyph-edit-2:before {
    content: "\E980";
}
.glyph-thumb-down:before {
    content: "\E8DB";
}
.glyph-thumb-up:before {
    content: "\E8DC";
}
.glyph-download:before {
    content: "\E92F";
}
.glyph-download-cloud:before {
    content: "\E966";
}
.glyph-upload:before {
    content: "\E943";
}
.glyph-upload-cloud:before {
    content: "\E967";
}
.glyph-email:before {
    content: "\E931";
}
.glyph-email-bell:before {
    content: "\E990";
}
.glyph-phone:before {
    content: "\E964";
}
.glyph-help:before {
    content: "\E955";
}
.glyph-trash:before,
.glyph-trashbin:before {
    content: "\E933";
}
.glyph-grid:before {
    content: "\E934";
}
.glyph-settings:before {
    content: "\E935";
}
.glyph-settings-2:before {
    content: "\E927";
}
.glyph-settings-3:before {
    content: "\E907";
}
.glyph-gear:before {
    content: "\E935";
}
.glyph-book:before {
    content: "\E937";
}
.glyph-graduation-cap:before {
    content: "\E94B";
}
.glyph-folder:before {
    content: "\E949";
}
.glyph-folder-play:before {
    content: "\E948";
}
.glyph-folder-music:before {
    content: "\E946";
}
.glyph-folder-images:before {
    content: "\E944";
}
.glyph-laptop:before {
    content: "\E951";
}
.glyph-tv:before {
    content: "\E947";
}
.glyph-world:before {
    content: "\E945";
}
.glyph-location:before {
    content: "\E93A";
}
.glyph-reload:before {
    content: "\E93D";
}
.glyph-search:before {
    content: "\E927";
}
.glyph-search-2:before {
    content: "\E92B";
}
.glyph-menu:before {
    content: "\E926";
}
.glyph-menu-dropdown:before {
    content: "\E952";
}
.glyph-menu-cr-vertical:before {
    content: "\E963";
}
.glyph-bell:before {
    content: "\E950";
}
.glyph-quote:before {
    content: "\E972";
}
.glyph-alert:before {
    content: "\E950";
}
.glyph-lock:before {
    content: "\E9F9";
}
.glyph-unlock:before {
    content: "\E9FA";
}
.glyph-send:before {
    content: "\E94A";
}
.glyph-pin:before {
    content: "\E962";
}
.glyph-mappin:before {
    content: "\E95E";
}
.glyph-key:before {
    content: "\E96A";
}
.glyph-warning-2:before,
.glyph-warning:before {
    content: "\E9FD";
}
.glyph-rocket:before {
    content: "\E976";
}
.glyph-document:before {
    content: "\E970";
}
.glyph-reply-all:before {
    content: "\E973";
}
.glyph-reply:before {
    content: "\E974";
}
.glyph-evernote:before {
    content: "\E977";
}
.glyph-hangouts:before {
    content: "\E978";
}
.glyph-skype-cr:before {
    content: "\E979";
}
.glyph-skype:before {
    content: "\E97A";
}
.glyph-vine-cr:before {
    content: "\E97B";
}
.glyph-vine:before {
    content: "\E97C";
}
.glyph-yelp:before {
    content: "\E97D";
}
.glyph-facebook-cr:before {
    content: "\E97E";
}
.glyph-facebook:before {
    content: "\E97F";
}
.glyph-flickr-cr:before {
    content: "\E980";
}
.glyph-flickr:before {
    content: "\E981";
}
.glyph-instagram-cr:before {
    content: "\E982";
}
.glyph-instagram:before {
    content: "\E983";
}
.glyph-linkedin-cr:before {
    content: "\E984";
}
.glyph-linkedin:before {
    content: "\E985";
}
.glyph-pinterest-cr:before {
    content: "\E986";
}
.glyph-pinterest:before {
    content: "\E987";
}
.glyph-sand-clock:before {
    content: "\E988";
}
.glyph-tumblr:before {
    content: "\E989";
}
.glyph-twitter-cr:before {
    content: "\E98A";
}
.glyph-twitter:before {
    content: "\E98B";
}
.glyph-vimeo-cr:before {
    content: "\E98C";
}
.glyph-vimeo:before {
    content: "\E98D";
}
.glyph-youtube-cr:before {
    content: "\E98E";
}
.glyph-youtube:before {
    content: "\E98F";
}
.glyph-feather:before {
    content: "\E990";
}
.glyph-connection:before {
    content: "\E991";
}
.glyph-world-location:before {
    content: "\E998";
}
.glyph-amazon:before {
    content: "\EA87";
}
.glyph-google:before {
    content: "\EA88";
}
.glyph-google-cr:before {
    content: "\EA8A";
}
.glyph-google-plus:before {
    content: "\EA8B";
}
.glyph-google-plus-cr:before {
    content: "\EA8D";
}
.glyph-google-hangouts:before {
    content: "\EA8E";
}
.glyph-facebook-sq:before {
    content: "\EA91";
}
.glyph-telegram:before {
    content: "\EA95";
}
.glyph-rss:before {
    content: "\EA9B";
}
.glyph-rss-sq:before {
    content: "\EA9C";
}
.glyph-reddit:before {
    content: "\EAC6";
}
.glyph-chart-1:before {
    content: "\E90F";
}
.glyph-chart-2:before {
    content: "\E910";
}
.glyph-chart-3:before {
    content: "\E911";
}
.glyph-chart-4:before {
    content: "\E912";
}
.glyph-chart-5:before {
    content: "\E913";
}
.glyph-chart-6:before {
    content: "\E914";
}
.glyph-chart-7:before {
    content: "\E915";
}
.glyph-ab-testing:before,
.glyph-abtesting:before {
    content: "\E900";
}
.glyph-contact-update:before {
    content: "\E902";
}
.glyph-contact-delete:before {
    content: "\E901";
}
.glyph-sms-bell:before {
    content: "\E908";
}
.glyph-sms:before {
    content: "\E903";
}
.glyph-tag:before {
    content: "\E909";
}
.glyph-tag-add:before {
    content: "\E90A";
}
.glyph-tag-delete:before {
    content: "\E90C";
}
.glyph-user:before {
    content: "\E90D";
}
.glyph-return:before {
    content: "\E906";
}
.glyph-flowchart:before {
    content: "\E904";
}
.glyph-outbound:before {
    content: "\E905";
}
.glyph-target:before {
    content: "\E952";
}
.glyph-email-send:before {
    content: "\E954";
}
.glyph-email-open:before {
    content: "\E971";
}
.glyph-email-click:before {
    content: "\E953";
}
.glyph-sms-send:before {
    content: "\E975";
}
.glyph-sms-open:before {
    content: "\E978";
}
.glyph-sms-click:before {
    content: "\E977";
}
.glyph-click:before {
    content: "\E97A";
}
.glyph-send-2:before {
    content: "\E979";
}
.glyph-calendar:before {
    content: "\E97C";
}
.glyph-calendar-clock:before {
    content: "\E97D";
}
.glyph-calendar-clock-2:before {
    content: "\E97E";
}
.glyph-email-sign:before {
    content: "\E982";
}
.glyph-stop:before {
    content: "\E989";
}
.glyph-stop-hand:before {
    content: "\E984";
}
.glyph-medal:before {
    content: "\E985";
}
.glyph-trophy:before {
    content: "\E98D";
}
.glyph-number-sign:before {
    content: "\E986";
}
.glyph-sandclock:before {
    content: "\E988";
}
.glyph-text:before {
    content: "\E98A";
}
.glyph-text-multiple:before {
    content: "\E98C";
}
.glyph-url:before {
    content: "\E98E";
}
.glyph-automation:before {
    content: "\E995";
}
.glyph-automation-stop:before {
    content: "\E996";
}
.glyph-enlarge:before {
    content: "\E994";
}
.glyph-reduce:before {
    content: "\E993";
}
.glyph-form-fields:before {
    content: "\E992";
}
.glyph-funnel:before {
    content: "\E997";
}
.glyph-door-exit:before {
    content: "\E999";
}
.glyph-pixel-search:before {
    content: "\E99A";
}
.glyph-newsletter-sms:before {
    content: "\E99D";
}
.glyph-newsletter-email:before {
    content: "\E99E";
}
.glyph-tag-image:before {
    content: "\E99B";
}
.glyph-tag-click:before {
    content: "\E99C";
}
.glyph-email-gear:before {
    content: "\E99F";
}
.glyph-sms-gear:before {
    content: "\E9A0";
}
.glyph-view:before {
    content: "\E97B";
}
.glyph-duplicate:before {
    content: "\E9A1";
}
.glyph-flowchart-expand-all:before {
    content: "\E9A2";
}
.glyph-flowchart-collapse-all:before {
    content: "\E9A3";
}
.glyph-flowchart-collapse-to-no:before {
    content: "\E9A4";
}
.glyph-flowchart-collapse-to-yes:before {
    content: "\E9A5";
}
.glyph-zoom-in:before {
    content: "\E9A7";
}
.glyph-zoom-out:before {
    content: "\E9A8";
}
.glyph-zoom-reset:before {
    content: "\E9A9";
}
.glyph-open-window:before {
    content: "\E9C4";
}
.glyph-flag:before {
    content: "\E9AA";
}
.glyph-klicky-run:before {
    content: "\E9A6";
}
.glyph-klicky-warning:before {
    content: "\E9C1";
}
.glyph-klicky-cross:before {
    content: "\E9C2";
}
.glyph-klicky-info:before {
    content: "\E9C3";
}
.glyph-wufoo:before {
    content: "\E9AB";
}
.glyph-wistia:before {
    content: "\E9AC";
}
.glyph-twilio:before {
    content: "\E9AD";
}
.glyph-thrivethemes:before {
    content: "\E9AE";
}
.glyph-paypal:before {
    content: "\E9AF";
}
.glyph-optimizepress:before {
    content: "\E9B0";
}
.glyph-nexmo:before {
    content: "\E9B1";
}
.glyph-leadpages:before {
    content: "\E9B2";
}
.glyph-digistore:before {
    content: "\E9B3";
}
.glyph-clickbank:before {
    content: "\E9B4";
}
.glyph-affilicon:before {
    content: "\E9B5";
}
.glyph-cardreaderpro:before {
    content: "\E9B6";
}
.glyph-form-widget:before {
    content: "\E9BD";
}
.glyph-form-custom:before {
    content: "\E9BB";
}
.glyph-form-inline:before {
    content: "\E9BC";
}
.glyph-form-raw:before {
    content: "\E9BE";
}
.glyph-form-combo:before {
    content: "\E9B9";
}
.glyph-api-key:before {
    content: "\E9B8";
}
.glyph-facebook-button:before {
    content: "\E9BA";
}
.glyph-goal:before,
.glyph-race-flag:before {
    content: "\E9C6";
}
.glyph-wave-flag:before {
    content: "\E9C5";
}
.glyph-bug:before {
    content: "\E9C8";
}
.glyph-briefcase:before {
    content: "\E956";
}
.glyph-building:before {
    content: "\E965";
}
.glyph-google-logo:before {
    content: "\E9C9";
}
.glyph-social-contact:before {
    content: "\E9CE";
}
.glyph-identity-person:before {
    content: "\E9D1";
}
.glyph-identity-email:before {
    content: "\E9D3";
}
.glyph-identity-search:before {
    content: "\E9D0";
}
.glyph-identity-gender:before {
    content: "\E9D2";
}
.glyph-identity-send:before {
    content: "\E9DF";
}
.glyph-flag-money:before {
    content: "\E9D5";
}
.glyph-decimal:before {
    content: "\E9D4";
}
.glyph-decimal-dot:before {
    content: "\E9D6";
}
.glyph-decimal-comma:before {
    content: "\E9D7";
}
.glyph-gender:before {
    content: "\E9D9";
}
.glyph-user-fields:before {
    content: "\E9D8";
}
.glyph-html:before {
    content: "\E9CD";
}
.glyph-editor-maximize:before {
    content: "\E9DA";
}
.glyph-editor-guides:before {
    content: "\E9DB";
}
.glyph-editor-code:before {
    content: "\E9DC";
}
.glyph-editor-ruler:before {
    content: "\E9DD";
}
.glyph-editor-desktop:before {
    content: "\E9DE";
}
.glyph-editor-tablet:before {
    content: "\E9DF";
}
.glyph-editor-mobile:before {
    content: "\E9E0";
}
.glyph-editor-undo:before {
    content: "\E9E3";
}
.glyph-editor-redo:before {
    content: "\E9E2";
}
.glyph-editor-section-default:before {
    content: "\E9E4";
}
.glyph-editor-section-custom:before {
    content: "\E9E5";
}
.glyph-editor-section-row:before {
    content: "\E9FC";
}
.glyph-editor-section-column:before {
    content: "\E9E6";
}
.glyph-editor-section-grid:before {
    content: "\E9E7";
}
.glyph-editor-media-image:before {
    content: "\E9EE";
}
.glyph-editor-media-gallery:before {
    content: "\E9EF";
}
.glyph-editor-media-icon:before {
    content: "\E9ED";
}
.glyph-editor-media-map:before {
    content: "\E9F1";
}
.glyph-editor-media-video:before {
    content: "\E9F6";
}
.glyph-editor-media-social:before {
    content: "\E9F4";
}
.glyph-editor-button:before {
    content: "\E9E8";
}
.glyph-editor-automation:before {
    content: "\E9E9";
}
.glyph-editor-contact:before {
    content: "\E9EA";
}
.glyph-editor-form:before {
    content: "\E9EB";
}
.glyph-editor-headline:before {
    content: "\E9EC";
}
.glyph-editor-text:before {
    content: "\E9F5";
}
.glyph-editor-quote:before {
    content: "\E9F2";
}
.glyph-editor-separator:before {
    content: "\E9F3";
}
.glyph-editor-template-kt:before {
    content: "\EA00";
}
.glyph-editor-template-user:before {
    content: "\E9FF";
}
.glyph-editor-spacer:before {
    content: "\E9FB";
}
[class*="glyph-"].glyphcolor-primary:before,
[class*="icon-"].glyphcolor-primary:before {
    color: #4d92ff !important;
}
.centerbox {
    max-width: 1200px;
}
.centerbox,
.centerbox.medium {
    display: block;
    margin-left: auto;
    margin-right: auto;
}
.centerbox.medium {
    max-width: 960px;
}
.centerbox.small {
    display: block;
    margin-left: auto;
    margin-right: auto;
    max-width: 720px;
}
.frame.up {
    padding-top: 60px;
}
.frame.down {
    padding-bottom: 60px;
}
.clearfix {
    clear: both;
}
.clearfix:after {
    display: block;
    visibility: hidden;
    clear: both;
    height: 0;
    content: " ";
    font-size: 0;
}
a,
abbr,
acronym,
address,
applet,
article,
aside,
audio,
b,
big,
blockquote,
body,
canvas,
caption,
center,
cite,
code,
dd,
del,
details,
dfn,
div,
dl,
dt,
em,
embed,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
html,
i,
iframe,
img,
ins,
kbd,
label,
legend,
li,
mark,
menu,
nav,
object,
ol,
output,
p,
pre,
q,
ruby,
s,
samp,
section,
small,
span,
strike,
strong,
sub,
summary,
sup,
table,
tbody,
td,
tfoot,
th,
thead,
time,
tr,
tt,
u,
ul,
var,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}
*,
:after,
:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}
body {
    line-height: 1;
}
blockquote,
q {
    quotes: none;
}
blockquote:after,
blockquote:before,
q:after,
q:before {
    content: "";
    content: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
a:active,
a:focus,
a:hover {
    outline: 0;
}
@font-face {
    font-family: glyphs;
    src: url(fonts/glyphs.eot);
    src: url(fonts/glyphs.eot) format("embedded-opentype"), url(fonts/glyphs.ttf) format("truetype"), url(fonts/glyphs.woff) format("woff"), url(fonts/glyphs.svg) format("svg");
    font-weight: 400;
    font-style: normal;
}
a,
a:link,
a:visited {
    color: #4d92ff;
    text-decoration: none;
}
a:hover {
    color: #1f65d3;
    -webkit-transition: color 0.15s ease-out;
    -o-transition: color 0.15s ease-out;
    transition: color 0.15s ease-out;
}
p {
    margin: 0 0 1.325em;
    line-height: 1.325em;
    font-weight: 300;
}
p:last-child,
p:last-of-type {
    margin-bottom: 0;
}
strong {
    font-weight: 600;
}
h1,
h2,
h3,
h4,
h5 {
    font-family: soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    color: #1c2c3e;
    font-weight: 700;
}
h1.alternate-headline,
h1.is-style-quote-style-headline,
h2.alternate-headline,
h2.is-style-quote-style-headline,
h3.alternate-headline,
h3.is-style-quote-style-headline,
h4.alternate-headline,
h4.is-style-quote-style-headline,
h5.alternate-headline,
h5.is-style-quote-style-headline {
    font-family: museo-slab, Georgia, serif;
    font-weight: 200;
    color: #3376de;
    padding-top: 1.25em;
    padding-bottom: 1.2em;
    border-top: 1px solid #4d92ff;
    border-bottom: 1px solid #4d92ff;
}
.page-title,
[class*="page-title-headline"],
h1 {
    font-size: 45px;
    font-weight: 800;
    line-height: 50.175px;
}
@media screen and (max-width: 660px) {
    .page-title,
    [class*="page-title-headline"],
    h1 {
        font-size: 30px;
    }
}
h2 {
    font-size: 36px;
    line-height: 48.6px;
}
@media screen and (max-width: 660px) {
    h2 {
        font-size: 26px;
    }
}
h3 {
    font-size: 24px;
    line-height: 32.4px;
}
@media screen and (max-width: 660px) {
    h3 {
        font-size: 20px;
    }
}
h4 {
    font-size: 20px;
    line-height: 27px;
}
@media screen and (max-width: 660px) {
    h4 {
        font-size: 18px;
    }
}
h5 {
    font-size: 16px;
    line-height: 21.6px;
}
@media screen and (max-width: 660px) {
    h5 {
        font-size: 16px;
    }
}
.dropcap-counter-reset,
.page-section {
    counter-reset: dropcap-counter;
}
.dropcap {
    counter-increment: dropcap-counter;
}
.dropcap:before {
    font-family: museo-slab, Georgia, serif;
    font-size: 100px;
    line-height: 0.75em;
    color: #4d92ff;
    font-weight: 900;
    margin-top: -0.75em;
    display: inline-block;
    margin-right: 8px;
}
.dropcap.numeric:before {
    content: counter(dropcap-counter);
}
.dropcap.alpha:before {
    content: counter(dropcap-counter, upper-alpha);
}
@media screen and (max-width: 660px) {
    .dropcap {
        margin-top: 50px;
    }
}
.body-elegant,
.body-feature,
.style-elegant,
[class*="body-feature"] {
    font-size: 20px;
    line-height: 1.3em;
    font-weight: 300;
    margin-bottom: 38px;
}
.body-footnote,
.body-note,
[class*="body-footnote"],
[class*="body-note"] {
    font-size: 14px;
    line-height: 1.3em;
    font-weight: 400;
    color: #7c8b9f;
}
.body-footnote,
[class*="body-footnote"] {
    font-style: italic;
}
.page-subtitle {
    font-size: 28px;
    font-weight: 300;
    margin: 0 auto;
}
@media screen and (max-width: 1200px) {
    .page-subtitle {
        font-size: 24px;
    }
}
@media only screen and (min-device-width: ) and (max-device-width: ) and (-webkit-min-device-pixel-ratio: ) {
    .page-subtitle {
        font-size: 19px;
        line-height: 1.25em;
    }
}
@media only screen and (min-device-width: 300px) and (max-device-width: 660px) {
    .page-subtitle {
        font-size: 19px;
        line-height: 1.25em;
    }
}
.max-width-textblock,
.textblock,
[class*="textblock"] {
    display: block;
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
}
.max-width-textblock.headline,
.textblock.headline,
[class*="textblock"].headline {
    max-width: 1024px;
}
.textblock-small,
.textblock.small {
    max-width: 420px;
}
.textblock-medium,
.textblock.medium {
    max-width: 860px;
}
.smallblock {
    display: block;
    max-width: 360px;
    margin-left: auto;
    margin-right: auto;
}
blockquote[class*="elegant"] {
    background-color: #f2f6ff;
    font-family: museo-slab, Georgia, serif;
    font-size: 26px;
    line-height: 1.33em;
    padding: 38px;
    border-radius: 3px;
    position: relative;
}
blockquote[class*="elegant"]:before {
    display: block;
    content: "";
    width: 26px;
    height: 26px;
    position: absolute;
    left: 38px;
    bottom: -24px;
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMS41ODMiIGhlaWdodD0iMzEuNTgzIiB2aWV3Qm94PSIwIDAgMzEuNTgzIDMxLjU4MyI+PGRlZnM+PHN0eWxlPi5he2ZpbGw6I2YyZjZmZjt9PC9zdHlsZT48L2RlZnM+PHBhdGggY2xhc3M9ImEiIGQ9Ik02NzUuMDY2LDI1MjIuNjcxdjMxLjU4M2wzMS41ODMtMzEuNTgzWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTY3NS4wNjYgLTI1MjIuNjcxKSIvPjwvc3ZnPg==)
        no-repeat 50%;
    background-size: contain;
}
blockquote[class*="elegant"],
blockquote[class*="elegant"] p {
    font-weight: 200;
}
blockquote[class*="elegant"] cite {
    display: block;
    font-family: soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    font-size: 17px;
    line-height: 1em;
    font-weight: 800;
    color: #4d92ff;
    text-align: right;
}
[class*="bg-"] blockquote[class*="elegant"] {
    background-color: #fff;
}
[class*="bg-"] blockquote[class*="elegant"]:before {
    background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMS41ODMiIGhlaWdodD0iMzEuNTgzIiB2aWV3Qm94PSIwIDAgMzEuNTgzIDMxLjU4MyI+PGRlZnM+PHN0eWxlPi5he2ZpbGw6I2ZmZjt9PC9zdHlsZT48L2RlZnM+PHBhdGggY2xhc3M9ImEiIGQ9Ik02NzUuMDY2LDI1MjIuNjcxdjMxLjU4M2wzMS41ODMtMzEuNTgzWiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTY3NS4wNjYgLTI1MjIuNjcxKSIvPjwvc3ZnPg==)
        no-repeat 50%;
}
.type-primary {
    color: #4d92ff !important;
}
.type-white {
    color: #fff !important;
}
.type-accent {
    color: #ffa20f !important;
}
.type-dark {
    color: #1c2c3e !important;
}
.type-success {
    color: #35905d !important;
}
.type-error {
    color: #d64956 !important;
}
[data-aos][data-aos][data-aos-duration="50"],
body[data-aos-duration="50"] [data-aos] {
    -webkit-transition-duration: 50ms;
    -o-transition-duration: 50ms;
    transition-duration: 50ms;
}
[data-aos][data-aos][data-aos-delay="50"],
body[data-aos-delay="50"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="50"].aos-animate,
body[data-aos-delay="50"] [data-aos].aos-animate {
    -webkit-transition-delay: 50ms;
    -o-transition-delay: 50ms;
    transition-delay: 50ms;
}
[data-aos][data-aos][data-aos-duration="100"],
body[data-aos-duration="100"] [data-aos] {
    -webkit-transition-duration: 0.1s;
    -o-transition-duration: 0.1s;
    transition-duration: 0.1s;
}
[data-aos][data-aos][data-aos-delay="100"],
body[data-aos-delay="100"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="100"].aos-animate,
body[data-aos-delay="100"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.1s;
    -o-transition-delay: 0.1s;
    transition-delay: 0.1s;
}
[data-aos][data-aos][data-aos-duration="150"],
body[data-aos-duration="150"] [data-aos] {
    -webkit-transition-duration: 0.15s;
    -o-transition-duration: 0.15s;
    transition-duration: 0.15s;
}
[data-aos][data-aos][data-aos-delay="150"],
body[data-aos-delay="150"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="150"].aos-animate,
body[data-aos-delay="150"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.15s;
    -o-transition-delay: 0.15s;
    transition-delay: 0.15s;
}
[data-aos][data-aos][data-aos-duration="200"],
body[data-aos-duration="200"] [data-aos] {
    -webkit-transition-duration: 0.2s;
    -o-transition-duration: 0.2s;
    transition-duration: 0.2s;
}
[data-aos][data-aos][data-aos-delay="200"],
body[data-aos-delay="200"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="200"].aos-animate,
body[data-aos-delay="200"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.2s;
    -o-transition-delay: 0.2s;
    transition-delay: 0.2s;
}
[data-aos][data-aos][data-aos-duration="250"],
body[data-aos-duration="250"] [data-aos] {
    -webkit-transition-duration: 0.25s;
    -o-transition-duration: 0.25s;
    transition-duration: 0.25s;
}
[data-aos][data-aos][data-aos-delay="250"],
body[data-aos-delay="250"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="250"].aos-animate,
body[data-aos-delay="250"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.25s;
    -o-transition-delay: 0.25s;
    transition-delay: 0.25s;
}
[data-aos][data-aos][data-aos-duration="300"],
body[data-aos-duration="300"] [data-aos] {
    -webkit-transition-duration: 0.3s;
    -o-transition-duration: 0.3s;
    transition-duration: 0.3s;
}
[data-aos][data-aos][data-aos-delay="300"],
body[data-aos-delay="300"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="300"].aos-animate,
body[data-aos-delay="300"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.3s;
    -o-transition-delay: 0.3s;
    transition-delay: 0.3s;
}
[data-aos][data-aos][data-aos-duration="350"],
body[data-aos-duration="350"] [data-aos] {
    -webkit-transition-duration: 0.35s;
    -o-transition-duration: 0.35s;
    transition-duration: 0.35s;
}
[data-aos][data-aos][data-aos-delay="350"],
body[data-aos-delay="350"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="350"].aos-animate,
body[data-aos-delay="350"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.35s;
    -o-transition-delay: 0.35s;
    transition-delay: 0.35s;
}
[data-aos][data-aos][data-aos-duration="400"],
body[data-aos-duration="400"] [data-aos] {
    -webkit-transition-duration: 0.4s;
    -o-transition-duration: 0.4s;
    transition-duration: 0.4s;
}
[data-aos][data-aos][data-aos-delay="400"],
body[data-aos-delay="400"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="400"].aos-animate,
body[data-aos-delay="400"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.4s;
    -o-transition-delay: 0.4s;
    transition-delay: 0.4s;
}
[data-aos][data-aos][data-aos-duration="450"],
body[data-aos-duration="450"] [data-aos] {
    -webkit-transition-duration: 0.45s;
    -o-transition-duration: 0.45s;
    transition-duration: 0.45s;
}
[data-aos][data-aos][data-aos-delay="450"],
body[data-aos-delay="450"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="450"].aos-animate,
body[data-aos-delay="450"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.45s;
    -o-transition-delay: 0.45s;
    transition-delay: 0.45s;
}
[data-aos][data-aos][data-aos-duration="500"],
body[data-aos-duration="500"] [data-aos] {
    -webkit-transition-duration: 0.5s;
    -o-transition-duration: 0.5s;
    transition-duration: 0.5s;
}
[data-aos][data-aos][data-aos-delay="500"],
body[data-aos-delay="500"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="500"].aos-animate,
body[data-aos-delay="500"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.5s;
    -o-transition-delay: 0.5s;
    transition-delay: 0.5s;
}
[data-aos][data-aos][data-aos-duration="550"],
body[data-aos-duration="550"] [data-aos] {
    -webkit-transition-duration: 0.55s;
    -o-transition-duration: 0.55s;
    transition-duration: 0.55s;
}
[data-aos][data-aos][data-aos-delay="550"],
body[data-aos-delay="550"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="550"].aos-animate,
body[data-aos-delay="550"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.55s;
    -o-transition-delay: 0.55s;
    transition-delay: 0.55s;
}
[data-aos][data-aos][data-aos-duration="600"],
body[data-aos-duration="600"] [data-aos] {
    -webkit-transition-duration: 0.6s;
    -o-transition-duration: 0.6s;
    transition-duration: 0.6s;
}
[data-aos][data-aos][data-aos-delay="600"],
body[data-aos-delay="600"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="600"].aos-animate,
body[data-aos-delay="600"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.6s;
    -o-transition-delay: 0.6s;
    transition-delay: 0.6s;
}
[data-aos][data-aos][data-aos-duration="650"],
body[data-aos-duration="650"] [data-aos] {
    -webkit-transition-duration: 0.65s;
    -o-transition-duration: 0.65s;
    transition-duration: 0.65s;
}
[data-aos][data-aos][data-aos-delay="650"],
body[data-aos-delay="650"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="650"].aos-animate,
body[data-aos-delay="650"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.65s;
    -o-transition-delay: 0.65s;
    transition-delay: 0.65s;
}
[data-aos][data-aos][data-aos-duration="700"],
body[data-aos-duration="700"] [data-aos] {
    -webkit-transition-duration: 0.7s;
    -o-transition-duration: 0.7s;
    transition-duration: 0.7s;
}
[data-aos][data-aos][data-aos-delay="700"],
body[data-aos-delay="700"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="700"].aos-animate,
body[data-aos-delay="700"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.7s;
    -o-transition-delay: 0.7s;
    transition-delay: 0.7s;
}
[data-aos][data-aos][data-aos-duration="750"],
body[data-aos-duration="750"] [data-aos] {
    -webkit-transition-duration: 0.75s;
    -o-transition-duration: 0.75s;
    transition-duration: 0.75s;
}
[data-aos][data-aos][data-aos-delay="750"],
body[data-aos-delay="750"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="750"].aos-animate,
body[data-aos-delay="750"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.75s;
    -o-transition-delay: 0.75s;
    transition-delay: 0.75s;
}
[data-aos][data-aos][data-aos-duration="800"],
body[data-aos-duration="800"] [data-aos] {
    -webkit-transition-duration: 0.8s;
    -o-transition-duration: 0.8s;
    transition-duration: 0.8s;
}
[data-aos][data-aos][data-aos-delay="800"],
body[data-aos-delay="800"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="800"].aos-animate,
body[data-aos-delay="800"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.8s;
    -o-transition-delay: 0.8s;
    transition-delay: 0.8s;
}
[data-aos][data-aos][data-aos-duration="850"],
body[data-aos-duration="850"] [data-aos] {
    -webkit-transition-duration: 0.85s;
    -o-transition-duration: 0.85s;
    transition-duration: 0.85s;
}
[data-aos][data-aos][data-aos-delay="850"],
body[data-aos-delay="850"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="850"].aos-animate,
body[data-aos-delay="850"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.85s;
    -o-transition-delay: 0.85s;
    transition-delay: 0.85s;
}
[data-aos][data-aos][data-aos-duration="900"],
body[data-aos-duration="900"] [data-aos] {
    -webkit-transition-duration: 0.9s;
    -o-transition-duration: 0.9s;
    transition-duration: 0.9s;
}
[data-aos][data-aos][data-aos-delay="900"],
body[data-aos-delay="900"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="900"].aos-animate,
body[data-aos-delay="900"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.9s;
    -o-transition-delay: 0.9s;
    transition-delay: 0.9s;
}
[data-aos][data-aos][data-aos-duration="950"],
body[data-aos-duration="950"] [data-aos] {
    -webkit-transition-duration: 0.95s;
    -o-transition-duration: 0.95s;
    transition-duration: 0.95s;
}
[data-aos][data-aos][data-aos-delay="950"],
body[data-aos-delay="950"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="950"].aos-animate,
body[data-aos-delay="950"] [data-aos].aos-animate {
    -webkit-transition-delay: 0.95s;
    -o-transition-delay: 0.95s;
    transition-delay: 0.95s;
}
[data-aos][data-aos][data-aos-duration="1000"],
body[data-aos-duration="1000"] [data-aos] {
    -webkit-transition-duration: 1s;
    -o-transition-duration: 1s;
    transition-duration: 1s;
}
[data-aos][data-aos][data-aos-delay="1000"],
body[data-aos-delay="1000"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1000"].aos-animate,
body[data-aos-delay="1000"] [data-aos].aos-animate {
    -webkit-transition-delay: 1s;
    -o-transition-delay: 1s;
    transition-delay: 1s;
}
[data-aos][data-aos][data-aos-duration="1050"],
body[data-aos-duration="1050"] [data-aos] {
    -webkit-transition-duration: 1.05s;
    -o-transition-duration: 1.05s;
    transition-duration: 1.05s;
}
[data-aos][data-aos][data-aos-delay="1050"],
body[data-aos-delay="1050"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1050"].aos-animate,
body[data-aos-delay="1050"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.05s;
    -o-transition-delay: 1.05s;
    transition-delay: 1.05s;
}
[data-aos][data-aos][data-aos-duration="1100"],
body[data-aos-duration="1100"] [data-aos] {
    -webkit-transition-duration: 1.1s;
    -o-transition-duration: 1.1s;
    transition-duration: 1.1s;
}
[data-aos][data-aos][data-aos-delay="1100"],
body[data-aos-delay="1100"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1100"].aos-animate,
body[data-aos-delay="1100"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.1s;
    -o-transition-delay: 1.1s;
    transition-delay: 1.1s;
}
[data-aos][data-aos][data-aos-duration="1150"],
body[data-aos-duration="1150"] [data-aos] {
    -webkit-transition-duration: 1.15s;
    -o-transition-duration: 1.15s;
    transition-duration: 1.15s;
}
[data-aos][data-aos][data-aos-delay="1150"],
body[data-aos-delay="1150"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1150"].aos-animate,
body[data-aos-delay="1150"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.15s;
    -o-transition-delay: 1.15s;
    transition-delay: 1.15s;
}
[data-aos][data-aos][data-aos-duration="1200"],
body[data-aos-duration="1200"] [data-aos] {
    -webkit-transition-duration: 1.2s;
    -o-transition-duration: 1.2s;
    transition-duration: 1.2s;
}
[data-aos][data-aos][data-aos-delay="1200"],
body[data-aos-delay="1200"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1200"].aos-animate,
body[data-aos-delay="1200"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.2s;
    -o-transition-delay: 1.2s;
    transition-delay: 1.2s;
}
[data-aos][data-aos][data-aos-duration="1250"],
body[data-aos-duration="1250"] [data-aos] {
    -webkit-transition-duration: 1.25s;
    -o-transition-duration: 1.25s;
    transition-duration: 1.25s;
}
[data-aos][data-aos][data-aos-delay="1250"],
body[data-aos-delay="1250"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1250"].aos-animate,
body[data-aos-delay="1250"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.25s;
    -o-transition-delay: 1.25s;
    transition-delay: 1.25s;
}
[data-aos][data-aos][data-aos-duration="1300"],
body[data-aos-duration="1300"] [data-aos] {
    -webkit-transition-duration: 1.3s;
    -o-transition-duration: 1.3s;
    transition-duration: 1.3s;
}
[data-aos][data-aos][data-aos-delay="1300"],
body[data-aos-delay="1300"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1300"].aos-animate,
body[data-aos-delay="1300"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.3s;
    -o-transition-delay: 1.3s;
    transition-delay: 1.3s;
}
[data-aos][data-aos][data-aos-duration="1350"],
body[data-aos-duration="1350"] [data-aos] {
    -webkit-transition-duration: 1.35s;
    -o-transition-duration: 1.35s;
    transition-duration: 1.35s;
}
[data-aos][data-aos][data-aos-delay="1350"],
body[data-aos-delay="1350"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1350"].aos-animate,
body[data-aos-delay="1350"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.35s;
    -o-transition-delay: 1.35s;
    transition-delay: 1.35s;
}
[data-aos][data-aos][data-aos-duration="1400"],
body[data-aos-duration="1400"] [data-aos] {
    -webkit-transition-duration: 1.4s;
    -o-transition-duration: 1.4s;
    transition-duration: 1.4s;
}
[data-aos][data-aos][data-aos-delay="1400"],
body[data-aos-delay="1400"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1400"].aos-animate,
body[data-aos-delay="1400"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.4s;
    -o-transition-delay: 1.4s;
    transition-delay: 1.4s;
}
[data-aos][data-aos][data-aos-duration="1450"],
body[data-aos-duration="1450"] [data-aos] {
    -webkit-transition-duration: 1.45s;
    -o-transition-duration: 1.45s;
    transition-duration: 1.45s;
}
[data-aos][data-aos][data-aos-delay="1450"],
body[data-aos-delay="1450"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1450"].aos-animate,
body[data-aos-delay="1450"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.45s;
    -o-transition-delay: 1.45s;
    transition-delay: 1.45s;
}
[data-aos][data-aos][data-aos-duration="1500"],
body[data-aos-duration="1500"] [data-aos] {
    -webkit-transition-duration: 1.5s;
    -o-transition-duration: 1.5s;
    transition-duration: 1.5s;
}
[data-aos][data-aos][data-aos-delay="1500"],
body[data-aos-delay="1500"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1500"].aos-animate,
body[data-aos-delay="1500"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.5s;
    -o-transition-delay: 1.5s;
    transition-delay: 1.5s;
}
[data-aos][data-aos][data-aos-duration="1550"],
body[data-aos-duration="1550"] [data-aos] {
    -webkit-transition-duration: 1.55s;
    -o-transition-duration: 1.55s;
    transition-duration: 1.55s;
}
[data-aos][data-aos][data-aos-delay="1550"],
body[data-aos-delay="1550"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1550"].aos-animate,
body[data-aos-delay="1550"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.55s;
    -o-transition-delay: 1.55s;
    transition-delay: 1.55s;
}
[data-aos][data-aos][data-aos-duration="1600"],
body[data-aos-duration="1600"] [data-aos] {
    -webkit-transition-duration: 1.6s;
    -o-transition-duration: 1.6s;
    transition-duration: 1.6s;
}
[data-aos][data-aos][data-aos-delay="1600"],
body[data-aos-delay="1600"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1600"].aos-animate,
body[data-aos-delay="1600"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.6s;
    -o-transition-delay: 1.6s;
    transition-delay: 1.6s;
}
[data-aos][data-aos][data-aos-duration="1650"],
body[data-aos-duration="1650"] [data-aos] {
    -webkit-transition-duration: 1.65s;
    -o-transition-duration: 1.65s;
    transition-duration: 1.65s;
}
[data-aos][data-aos][data-aos-delay="1650"],
body[data-aos-delay="1650"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1650"].aos-animate,
body[data-aos-delay="1650"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.65s;
    -o-transition-delay: 1.65s;
    transition-delay: 1.65s;
}
[data-aos][data-aos][data-aos-duration="1700"],
body[data-aos-duration="1700"] [data-aos] {
    -webkit-transition-duration: 1.7s;
    -o-transition-duration: 1.7s;
    transition-duration: 1.7s;
}
[data-aos][data-aos][data-aos-delay="1700"],
body[data-aos-delay="1700"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1700"].aos-animate,
body[data-aos-delay="1700"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.7s;
    -o-transition-delay: 1.7s;
    transition-delay: 1.7s;
}
[data-aos][data-aos][data-aos-duration="1750"],
body[data-aos-duration="1750"] [data-aos] {
    -webkit-transition-duration: 1.75s;
    -o-transition-duration: 1.75s;
    transition-duration: 1.75s;
}
[data-aos][data-aos][data-aos-delay="1750"],
body[data-aos-delay="1750"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1750"].aos-animate,
body[data-aos-delay="1750"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.75s;
    -o-transition-delay: 1.75s;
    transition-delay: 1.75s;
}
[data-aos][data-aos][data-aos-duration="1800"],
body[data-aos-duration="1800"] [data-aos] {
    -webkit-transition-duration: 1.8s;
    -o-transition-duration: 1.8s;
    transition-duration: 1.8s;
}
[data-aos][data-aos][data-aos-delay="1800"],
body[data-aos-delay="1800"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1800"].aos-animate,
body[data-aos-delay="1800"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.8s;
    -o-transition-delay: 1.8s;
    transition-delay: 1.8s;
}
[data-aos][data-aos][data-aos-duration="1850"],
body[data-aos-duration="1850"] [data-aos] {
    -webkit-transition-duration: 1.85s;
    -o-transition-duration: 1.85s;
    transition-duration: 1.85s;
}
[data-aos][data-aos][data-aos-delay="1850"],
body[data-aos-delay="1850"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1850"].aos-animate,
body[data-aos-delay="1850"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.85s;
    -o-transition-delay: 1.85s;
    transition-delay: 1.85s;
}
[data-aos][data-aos][data-aos-duration="1900"],
body[data-aos-duration="1900"] [data-aos] {
    -webkit-transition-duration: 1.9s;
    -o-transition-duration: 1.9s;
    transition-duration: 1.9s;
}
[data-aos][data-aos][data-aos-delay="1900"],
body[data-aos-delay="1900"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1900"].aos-animate,
body[data-aos-delay="1900"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.9s;
    -o-transition-delay: 1.9s;
    transition-delay: 1.9s;
}
[data-aos][data-aos][data-aos-duration="1950"],
body[data-aos-duration="1950"] [data-aos] {
    -webkit-transition-duration: 1.95s;
    -o-transition-duration: 1.95s;
    transition-duration: 1.95s;
}
[data-aos][data-aos][data-aos-delay="1950"],
body[data-aos-delay="1950"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="1950"].aos-animate,
body[data-aos-delay="1950"] [data-aos].aos-animate {
    -webkit-transition-delay: 1.95s;
    -o-transition-delay: 1.95s;
    transition-delay: 1.95s;
}
[data-aos][data-aos][data-aos-duration="2000"],
body[data-aos-duration="2000"] [data-aos] {
    -webkit-transition-duration: 2s;
    -o-transition-duration: 2s;
    transition-duration: 2s;
}
[data-aos][data-aos][data-aos-delay="2000"],
body[data-aos-delay="2000"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2000"].aos-animate,
body[data-aos-delay="2000"] [data-aos].aos-animate {
    -webkit-transition-delay: 2s;
    -o-transition-delay: 2s;
    transition-delay: 2s;
}
[data-aos][data-aos][data-aos-duration="2050"],
body[data-aos-duration="2050"] [data-aos] {
    -webkit-transition-duration: 2.05s;
    -o-transition-duration: 2.05s;
    transition-duration: 2.05s;
}
[data-aos][data-aos][data-aos-delay="2050"],
body[data-aos-delay="2050"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2050"].aos-animate,
body[data-aos-delay="2050"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.05s;
    -o-transition-delay: 2.05s;
    transition-delay: 2.05s;
}
[data-aos][data-aos][data-aos-duration="2100"],
body[data-aos-duration="2100"] [data-aos] {
    -webkit-transition-duration: 2.1s;
    -o-transition-duration: 2.1s;
    transition-duration: 2.1s;
}
[data-aos][data-aos][data-aos-delay="2100"],
body[data-aos-delay="2100"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2100"].aos-animate,
body[data-aos-delay="2100"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.1s;
    -o-transition-delay: 2.1s;
    transition-delay: 2.1s;
}
[data-aos][data-aos][data-aos-duration="2150"],
body[data-aos-duration="2150"] [data-aos] {
    -webkit-transition-duration: 2.15s;
    -o-transition-duration: 2.15s;
    transition-duration: 2.15s;
}
[data-aos][data-aos][data-aos-delay="2150"],
body[data-aos-delay="2150"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2150"].aos-animate,
body[data-aos-delay="2150"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.15s;
    -o-transition-delay: 2.15s;
    transition-delay: 2.15s;
}
[data-aos][data-aos][data-aos-duration="2200"],
body[data-aos-duration="2200"] [data-aos] {
    -webkit-transition-duration: 2.2s;
    -o-transition-duration: 2.2s;
    transition-duration: 2.2s;
}
[data-aos][data-aos][data-aos-delay="2200"],
body[data-aos-delay="2200"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2200"].aos-animate,
body[data-aos-delay="2200"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.2s;
    -o-transition-delay: 2.2s;
    transition-delay: 2.2s;
}
[data-aos][data-aos][data-aos-duration="2250"],
body[data-aos-duration="2250"] [data-aos] {
    -webkit-transition-duration: 2.25s;
    -o-transition-duration: 2.25s;
    transition-duration: 2.25s;
}
[data-aos][data-aos][data-aos-delay="2250"],
body[data-aos-delay="2250"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2250"].aos-animate,
body[data-aos-delay="2250"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.25s;
    -o-transition-delay: 2.25s;
    transition-delay: 2.25s;
}
[data-aos][data-aos][data-aos-duration="2300"],
body[data-aos-duration="2300"] [data-aos] {
    -webkit-transition-duration: 2.3s;
    -o-transition-duration: 2.3s;
    transition-duration: 2.3s;
}
[data-aos][data-aos][data-aos-delay="2300"],
body[data-aos-delay="2300"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2300"].aos-animate,
body[data-aos-delay="2300"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.3s;
    -o-transition-delay: 2.3s;
    transition-delay: 2.3s;
}
[data-aos][data-aos][data-aos-duration="2350"],
body[data-aos-duration="2350"] [data-aos] {
    -webkit-transition-duration: 2.35s;
    -o-transition-duration: 2.35s;
    transition-duration: 2.35s;
}
[data-aos][data-aos][data-aos-delay="2350"],
body[data-aos-delay="2350"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2350"].aos-animate,
body[data-aos-delay="2350"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.35s;
    -o-transition-delay: 2.35s;
    transition-delay: 2.35s;
}
[data-aos][data-aos][data-aos-duration="2400"],
body[data-aos-duration="2400"] [data-aos] {
    -webkit-transition-duration: 2.4s;
    -o-transition-duration: 2.4s;
    transition-duration: 2.4s;
}
[data-aos][data-aos][data-aos-delay="2400"],
body[data-aos-delay="2400"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2400"].aos-animate,
body[data-aos-delay="2400"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.4s;
    -o-transition-delay: 2.4s;
    transition-delay: 2.4s;
}
[data-aos][data-aos][data-aos-duration="2450"],
body[data-aos-duration="2450"] [data-aos] {
    -webkit-transition-duration: 2.45s;
    -o-transition-duration: 2.45s;
    transition-duration: 2.45s;
}
[data-aos][data-aos][data-aos-delay="2450"],
body[data-aos-delay="2450"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2450"].aos-animate,
body[data-aos-delay="2450"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.45s;
    -o-transition-delay: 2.45s;
    transition-delay: 2.45s;
}
[data-aos][data-aos][data-aos-duration="2500"],
body[data-aos-duration="2500"] [data-aos] {
    -webkit-transition-duration: 2.5s;
    -o-transition-duration: 2.5s;
    transition-duration: 2.5s;
}
[data-aos][data-aos][data-aos-delay="2500"],
body[data-aos-delay="2500"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2500"].aos-animate,
body[data-aos-delay="2500"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.5s;
    -o-transition-delay: 2.5s;
    transition-delay: 2.5s;
}
[data-aos][data-aos][data-aos-duration="2550"],
body[data-aos-duration="2550"] [data-aos] {
    -webkit-transition-duration: 2.55s;
    -o-transition-duration: 2.55s;
    transition-duration: 2.55s;
}
[data-aos][data-aos][data-aos-delay="2550"],
body[data-aos-delay="2550"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2550"].aos-animate,
body[data-aos-delay="2550"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.55s;
    -o-transition-delay: 2.55s;
    transition-delay: 2.55s;
}
[data-aos][data-aos][data-aos-duration="2600"],
body[data-aos-duration="2600"] [data-aos] {
    -webkit-transition-duration: 2.6s;
    -o-transition-duration: 2.6s;
    transition-duration: 2.6s;
}
[data-aos][data-aos][data-aos-delay="2600"],
body[data-aos-delay="2600"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2600"].aos-animate,
body[data-aos-delay="2600"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.6s;
    -o-transition-delay: 2.6s;
    transition-delay: 2.6s;
}
[data-aos][data-aos][data-aos-duration="2650"],
body[data-aos-duration="2650"] [data-aos] {
    -webkit-transition-duration: 2.65s;
    -o-transition-duration: 2.65s;
    transition-duration: 2.65s;
}
[data-aos][data-aos][data-aos-delay="2650"],
body[data-aos-delay="2650"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2650"].aos-animate,
body[data-aos-delay="2650"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.65s;
    -o-transition-delay: 2.65s;
    transition-delay: 2.65s;
}
[data-aos][data-aos][data-aos-duration="2700"],
body[data-aos-duration="2700"] [data-aos] {
    -webkit-transition-duration: 2.7s;
    -o-transition-duration: 2.7s;
    transition-duration: 2.7s;
}
[data-aos][data-aos][data-aos-delay="2700"],
body[data-aos-delay="2700"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2700"].aos-animate,
body[data-aos-delay="2700"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.7s;
    -o-transition-delay: 2.7s;
    transition-delay: 2.7s;
}
[data-aos][data-aos][data-aos-duration="2750"],
body[data-aos-duration="2750"] [data-aos] {
    -webkit-transition-duration: 2.75s;
    -o-transition-duration: 2.75s;
    transition-duration: 2.75s;
}
[data-aos][data-aos][data-aos-delay="2750"],
body[data-aos-delay="2750"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2750"].aos-animate,
body[data-aos-delay="2750"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.75s;
    -o-transition-delay: 2.75s;
    transition-delay: 2.75s;
}
[data-aos][data-aos][data-aos-duration="2800"],
body[data-aos-duration="2800"] [data-aos] {
    -webkit-transition-duration: 2.8s;
    -o-transition-duration: 2.8s;
    transition-duration: 2.8s;
}
[data-aos][data-aos][data-aos-delay="2800"],
body[data-aos-delay="2800"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2800"].aos-animate,
body[data-aos-delay="2800"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.8s;
    -o-transition-delay: 2.8s;
    transition-delay: 2.8s;
}
[data-aos][data-aos][data-aos-duration="2850"],
body[data-aos-duration="2850"] [data-aos] {
    -webkit-transition-duration: 2.85s;
    -o-transition-duration: 2.85s;
    transition-duration: 2.85s;
}
[data-aos][data-aos][data-aos-delay="2850"],
body[data-aos-delay="2850"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2850"].aos-animate,
body[data-aos-delay="2850"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.85s;
    -o-transition-delay: 2.85s;
    transition-delay: 2.85s;
}
[data-aos][data-aos][data-aos-duration="2900"],
body[data-aos-duration="2900"] [data-aos] {
    -webkit-transition-duration: 2.9s;
    -o-transition-duration: 2.9s;
    transition-duration: 2.9s;
}
[data-aos][data-aos][data-aos-delay="2900"],
body[data-aos-delay="2900"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2900"].aos-animate,
body[data-aos-delay="2900"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.9s;
    -o-transition-delay: 2.9s;
    transition-delay: 2.9s;
}
[data-aos][data-aos][data-aos-duration="2950"],
body[data-aos-duration="2950"] [data-aos] {
    -webkit-transition-duration: 2.95s;
    -o-transition-duration: 2.95s;
    transition-duration: 2.95s;
}
[data-aos][data-aos][data-aos-delay="2950"],
body[data-aos-delay="2950"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="2950"].aos-animate,
body[data-aos-delay="2950"] [data-aos].aos-animate {
    -webkit-transition-delay: 2.95s;
    -o-transition-delay: 2.95s;
    transition-delay: 2.95s;
}
[data-aos][data-aos][data-aos-duration="3000"],
body[data-aos-duration="3000"] [data-aos] {
    -webkit-transition-duration: 3s;
    -o-transition-duration: 3s;
    transition-duration: 3s;
}
[data-aos][data-aos][data-aos-delay="3000"],
body[data-aos-delay="3000"] [data-aos] {
    -webkit-transition-delay: 0s;
    -o-transition-delay: 0s;
    transition-delay: 0s;
}
[data-aos][data-aos][data-aos-delay="3000"].aos-animate,
body[data-aos-delay="3000"] [data-aos].aos-animate {
    -webkit-transition-delay: 3s;
    -o-transition-delay: 3s;
    transition-delay: 3s;
}
[data-aos] {
    pointer-events: none;
}
[data-aos].aos-animate {
    pointer-events: auto;
}
[data-aos][data-aos][data-aos-easing="linear"],
body[data-aos-easing="linear"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.25, 0.25, 0.75, 0.75);
    -o-transition-timing-function: cubic-bezier(0.25, 0.25, 0.75, 0.75);
    transition-timing-function: cubic-bezier(0.25, 0.25, 0.75, 0.75);
}
[data-aos][data-aos][data-aos-easing="ease"],
body[data-aos-easing="ease"] [data-aos] {
    -webkit-transition-timing-function: ease;
    -o-transition-timing-function: ease;
    transition-timing-function: ease;
}
[data-aos][data-aos][data-aos-easing="ease-in"],
body[data-aos-easing="ease-in"] [data-aos] {
    -webkit-transition-timing-function: ease-in;
    -o-transition-timing-function: ease-in;
    transition-timing-function: ease-in;
}
[data-aos][data-aos][data-aos-easing="ease-out"],
body[data-aos-easing="ease-out"] [data-aos] {
    -webkit-transition-timing-function: ease-out;
    -o-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
}
[data-aos][data-aos][data-aos-easing="ease-in-out"],
body[data-aos-easing="ease-in-out"] [data-aos] {
    -webkit-transition-timing-function: ease-in-out;
    -o-transition-timing-function: ease-in-out;
    transition-timing-function: ease-in-out;
}
[data-aos][data-aos][data-aos-easing="ease-in-back"],
body[data-aos-easing="ease-in-back"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.6, -0.28, 0.735, 0.045);
    -o-transition-timing-function: cubic-bezier(0.6, -0.28, 0.735, 0.045);
    transition-timing-function: cubic-bezier(0.6, -0.28, 0.735, 0.045);
}
[data-aos][data-aos][data-aos-easing="ease-out-back"],
body[data-aos-easing="ease-out-back"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    -o-transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
[data-aos][data-aos][data-aos-easing="ease-in-out-back"],
body[data-aos-easing="ease-in-out-back"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    -o-transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
[data-aos][data-aos][data-aos-easing="ease-in-sine"],
body[data-aos-easing="ease-in-sine"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
    -o-transition-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
    transition-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
}
[data-aos][data-aos][data-aos-easing="ease-out-sine"],
body[data-aos-easing="ease-out-sine"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.39, 0.575, 0.565, 1);
    -o-transition-timing-function: cubic-bezier(0.39, 0.575, 0.565, 1);
    transition-timing-function: cubic-bezier(0.39, 0.575, 0.565, 1);
}
[data-aos][data-aos][data-aos-easing="ease-in-out-sine"],
body[data-aos-easing="ease-in-out-sine"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.445, 0.05, 0.55, 0.95);
    -o-transition-timing-function: cubic-bezier(0.445, 0.05, 0.55, 0.95);
    transition-timing-function: cubic-bezier(0.445, 0.05, 0.55, 0.95);
}
[data-aos][data-aos][data-aos-easing="ease-in-quad"],
body[data-aos-easing="ease-in-quad"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    -o-transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
}
[data-aos][data-aos][data-aos-easing="ease-out-quad"],
body[data-aos-easing="ease-out-quad"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    -o-transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
[data-aos][data-aos][data-aos-easing="ease-in-out-quad"],
body[data-aos-easing="ease-in-out-quad"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
    -o-transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
    transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
}
[data-aos][data-aos][data-aos-easing="ease-in-cubic"],
body[data-aos-easing="ease-in-cubic"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    -o-transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
}
[data-aos][data-aos][data-aos-easing="ease-out-cubic"],
body[data-aos-easing="ease-out-cubic"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    -o-transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
[data-aos][data-aos][data-aos-easing="ease-in-out-cubic"],
body[data-aos-easing="ease-in-out-cubic"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
    -o-transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
    transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
}
[data-aos][data-aos][data-aos-easing="ease-in-quart"],
body[data-aos-easing="ease-in-quart"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    -o-transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
}
[data-aos][data-aos][data-aos-easing="ease-out-quart"],
body[data-aos-easing="ease-out-quart"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    -o-transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
[data-aos][data-aos][data-aos-easing="ease-in-out-quart"],
body[data-aos-easing="ease-in-out-quart"] [data-aos] {
    -webkit-transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
    -o-transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
    transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
}
@media screen {
    html:not(.no-js) [data-aos^="fade"][data-aos^="fade"] {
        opacity: 0;
        transition-property: opacity, -webkit-transform;
        -webkit-transition-property: opacity, -webkit-transform;
        -o-transition-property: opacity, -o-transform;
        transition-property: opacity, transform;
        transition-property: opacity, transform, -webkit-transform, -o-transform;
        transition-property: opacity, transform, -webkit-transform;
    }
    html:not(.no-js) [data-aos^="fade"][data-aos^="fade"].aos-animate {
        opacity: 1;
        -webkit-transform: none;
        -o-transform: none;
        transform: none;
    }
    html:not(.no-js) [data-aos="fade-up"] {
        -webkit-transform: translate3d(0, 100px, 0);
        transform: translate3d(0, 100px, 0);
    }
    html:not(.no-js) [data-aos="fade-down"] {
        -webkit-transform: translate3d(0, -100px, 0);
        transform: translate3d(0, -100px, 0);
    }
    html:not(.no-js) [data-aos="fade-right"] {
        -webkit-transform: translate3d(-100px, 0, 0);
        transform: translate3d(-100px, 0, 0);
    }
    html:not(.no-js) [data-aos="fade-left"] {
        -webkit-transform: translate3d(100px, 0, 0);
        transform: translate3d(100px, 0, 0);
    }
    html:not(.no-js) [data-aos="fade-up-right"] {
        -webkit-transform: translate3d(-100px, 100px, 0);
        transform: translate3d(-100px, 100px, 0);
    }
    html:not(.no-js) [data-aos="fade-up-left"] {
        -webkit-transform: translate3d(100px, 100px, 0);
        transform: translate3d(100px, 100px, 0);
    }
    html:not(.no-js) [data-aos="fade-down-right"] {
        -webkit-transform: translate3d(-100px, -100px, 0);
        transform: translate3d(-100px, -100px, 0);
    }
    html:not(.no-js) [data-aos="fade-down-left"] {
        -webkit-transform: translate3d(100px, -100px, 0);
        transform: translate3d(100px, -100px, 0);
    }
    html:not(.no-js) [data-aos^="zoom"][data-aos^="zoom"] {
        opacity: 0;
        transition-property: opacity, -webkit-transform;
        -webkit-transition-property: opacity, -webkit-transform;
        -o-transition-property: opacity, -o-transform;
        transition-property: opacity, transform;
        transition-property: opacity, transform, -webkit-transform, -o-transform;
        transition-property: opacity, transform, -webkit-transform;
    }
    html:not(.no-js) [data-aos^="zoom"][data-aos^="zoom"].aos-animate {
        opacity: 1;
        -webkit-transform: translateZ(0) scale(1);
        transform: translateZ(0) scale(1);
    }
    html:not(.no-js) [data-aos="zoom-in"] {
        -webkit-transform: scale(0.6);
        -o-transform: scale(0.6);
        transform: scale(0.6);
    }
    html:not(.no-js) [data-aos="zoom-in-up"] {
        -webkit-transform: translate3d(0, 100px, 0) scale(0.6);
        transform: translate3d(0, 100px, 0) scale(0.6);
    }
    html:not(.no-js) [data-aos="zoom-in-down"] {
        -webkit-transform: translate3d(0, -100px, 0) scale(0.6);
        transform: translate3d(0, -100px, 0) scale(0.6);
    }
    html:not(.no-js) [data-aos="zoom-in-right"] {
        -webkit-transform: translate3d(-100px, 0, 0) scale(0.6);
        transform: translate3d(-100px, 0, 0) scale(0.6);
    }
    html:not(.no-js) [data-aos="zoom-in-left"] {
        -webkit-transform: translate3d(100px, 0, 0) scale(0.6);
        transform: translate3d(100px, 0, 0) scale(0.6);
    }
    html:not(.no-js) [data-aos="zoom-out"] {
        -webkit-transform: scale(1.2);
        -o-transform: scale(1.2);
        transform: scale(1.2);
    }
    html:not(.no-js) [data-aos="zoom-out-up"] {
        -webkit-transform: translate3d(0, 100px, 0) scale(1.2);
        transform: translate3d(0, 100px, 0) scale(1.2);
    }
    html:not(.no-js) [data-aos="zoom-out-down"] {
        -webkit-transform: translate3d(0, -100px, 0) scale(1.2);
        transform: translate3d(0, -100px, 0) scale(1.2);
    }
    html:not(.no-js) [data-aos="zoom-out-right"] {
        -webkit-transform: translate3d(-100px, 0, 0) scale(1.2);
        transform: translate3d(-100px, 0, 0) scale(1.2);
    }
    html:not(.no-js) [data-aos="zoom-out-left"] {
        -webkit-transform: translate3d(100px, 0, 0) scale(1.2);
        transform: translate3d(100px, 0, 0) scale(1.2);
    }
    html:not(.no-js) [data-aos^="slide"][data-aos^="slide"] {
        transition-property: -webkit-transform;
        -webkit-transition-property: -webkit-transform;
        -o-transition-property: -o-transform;
        transition-property: transform;
        transition-property: transform, -webkit-transform, -o-transform;
        transition-property: transform, -webkit-transform;
        visibility: hidden;
    }
    html:not(.no-js) [data-aos^="slide"][data-aos^="slide"].aos-animate {
        visibility: visible;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
    html:not(.no-js) [data-aos="slide-up"] {
        -webkit-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
    html:not(.no-js) [data-aos="slide-down"] {
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
    html:not(.no-js) [data-aos="slide-right"] {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
    html:not(.no-js) [data-aos="slide-left"] {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }
    html:not(.no-js) [data-aos^="flip"][data-aos^="flip"] {
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        transition-property: -webkit-transform;
        -webkit-transition-property: -webkit-transform;
        -o-transition-property: -o-transform;
        transition-property: transform;
        transition-property: transform, -webkit-transform, -o-transform;
        transition-property: transform, -webkit-transform;
    }
    html:not(.no-js) [data-aos="flip-left"] {
        -webkit-transform: perspective(2500px) rotateY(-100deg);
        transform: perspective(2500px) rotateY(-100deg);
    }
    html:not(.no-js) [data-aos="flip-left"].aos-animate {
        -webkit-transform: perspective(2500px) rotateY(0);
        transform: perspective(2500px) rotateY(0);
    }
    html:not(.no-js) [data-aos="flip-right"] {
        -webkit-transform: perspective(2500px) rotateY(100deg);
        transform: perspective(2500px) rotateY(100deg);
    }
    html:not(.no-js) [data-aos="flip-right"].aos-animate {
        -webkit-transform: perspective(2500px) rotateY(0);
        transform: perspective(2500px) rotateY(0);
    }
    html:not(.no-js) [data-aos="flip-up"] {
        -webkit-transform: perspective(2500px) rotateX(-100deg);
        transform: perspective(2500px) rotateX(-100deg);
    }
    html:not(.no-js) [data-aos="flip-up"].aos-animate {
        -webkit-transform: perspective(2500px) rotateX(0);
        transform: perspective(2500px) rotateX(0);
    }
    html:not(.no-js) [data-aos="flip-down"] {
        -webkit-transform: perspective(2500px) rotateX(100deg);
        transform: perspective(2500px) rotateX(100deg);
    }
    html:not(.no-js) [data-aos="flip-down"].aos-animate {
        -webkit-transform: perspective(2500px) rotateX(0);
        transform: perspective(2500px) rotateX(0);
    }
}
body.barlow,
body.barlow h1,
body.barlow h2,
body.barlow h3,
body.barlow h4,
body.barlow h5 {
    font-family: Barlow, Lucida Sans Unicode, Lucida Grande, sans-serif;
}
body.source-sans,
body.source-sans h1,
body.source-sans h2,
body.source-sans h3,
body.source-sans h4,
body.source-sans h5 {
    font-family: Source Sans Pro, Lucida Sans Unicode, Lucida Grande, sans-serif;
}
body.lato,
body.lato h1,
body.lato h2,
body.lato h3,
body.lato h4,
body.lato h5 {
    font-family: Lato, Lucida Sans Unicode, Lucida Grande, sans-serif;
}
body.nunito,
body.nunito h1,
body.nunito h2,
body.nunito h3,
body.nunito h4,
body.nunito h5 {
    font-family: Nunito Sans, Lucida Sans Unicode, Lucida Grande, sans-serif;
}
body.overpass,
body.overpass h1,
body.overpass h2,
body.overpass h3,
body.overpass h4,
body.overpass h5 {
    font-family: Overpass, Lucida Sans Unicode, Lucida Grande, sans-serif;
}
[class*="button-"].block,
button.block {
    display: block;
    width: 100%;
    max-width: 100%;
}
[class*="button-"].xlarge,
button.xlarge {
    padding-top: calc(0.5em - 2px);
    padding-bottom: 0.5em;
    min-height: 65px;
    font-size: 26px;
    line-height: 65px;
}
[class*="button-"].large,
button.large {
    padding-top: calc(0.5em - 2px);
    padding-bottom: 0.5em;
    min-height: 55px;
    font-size: 22px;
    line-height: 55px;
}
[class*="button-"].medium,
button.medium {
    padding-top: calc(0.425em - 2px);
    padding-bottom: 0.425em;
    min-height: 45px;
    font-size: 18px;
    line-height: 45px;
}
[class*="button-"].xmedium,
button.xmedium {
    font-size: 14px;
}
[class*="button-"].small,
button.small {
    padding-top: calc(0.375em - 2px);
    padding-bottom: 0.375em;
    min-height: 24px;
    font-size: 13px;
    line-height: 24px;
}
[class*="button-"].small[class*="glyph-"]:before,
[class*="button-"].small[class*="icon-"]:before,
button.small[class*="glyph-"]:before,
button.small[class*="icon-"]:before {
    font-size: 0.875em;
}
[class*="button-"].xsmall,
button.xsmall {
    padding-top: calc(0.5em - 2px);
    padding-bottom: 0.5em;
    min-height: 20px;
    font-size: 13px;
    line-height: 20px;
}
[class*="button-"].xsmall[class*="glyph-"]:before,
[class*="button-"].xsmall[class*="icon-"]:before,
button.xsmall[class*="glyph-"]:before,
button.xsmall[class*="icon-"]:before {
    font-size: 1em;
}
.button-primary {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #ffa20f;
    border: none;
    -webkit-box-shadow: inset 0 -3px 0 0 #db8600;
    box-shadow: inset 0 -3px 0 0 #db8600;
}
.button-primary[class*="glyph-"]:before,
.button-primary[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
    color: #d57a00;
}
.button-primary:hover {
    color: #fff !important;
    background: #ef9300;
    -webkit-box-shadow: inset 0 -3px 0 0 #d18000;
    box-shadow: inset 0 -3px 0 0 #d18000;
    -webkit-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.button-secondary {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #4d92ff;
    border: none;
    -webkit-box-shadow: inset 0 -3px 0 0 #2479ff;
    box-shadow: inset 0 -3px 0 0 #2479ff;
}
.button-secondary[class*="glyph-"]:before,
.button-secondary[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
    color: #1f65d3;
}
.button-secondary:hover {
    color: #fff !important;
    background: #2479ff;
    -webkit-box-shadow: inset 0 -3px 0 0 #2266d1;
    box-shadow: inset 0 -3px 0 0 #2266d1;
    -webkit-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.button-tertiary {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #4d92ff !important;
    background: #fff;
    border: none;
    -webkit-box-shadow: inset 0 0 0 1px #4d92ff;
    box-shadow: inset 0 0 0 1px #4d92ff;
}
.button-tertiary[class*="glyph-"]:before,
.button-tertiary[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
    color: #4d92ff;
}
.button-tertiary:hover {
    color: #1f65d3 !important;
    -webkit-box-shadow: inset 0 0 0 1px #1f65d3;
    box-shadow: inset 0 0 0 1px #1f65d3;
    -webkit-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.button-disabled {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #aec4ea;
    border: none;
    -webkit-box-shadow: inset 0 -3px 0 0 #8eace2;
    box-shadow: inset 0 -3px 0 0 #8eace2;
    cursor: not-allowed;
    opacity: 0.75;
}
.button-disabled[class*="glyph-"]:before,
.button-disabled[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
    color: #1c2c3e;
}
.button-accent {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.button-accent[class*="glyph-"]:before,
.button-accent[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
}
.button-cta {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #ffa20f;
    -webkit-box-shadow: inset 0 -2px 0 0 #d57a00;
    box-shadow: inset 0 -2px 0 0 #d57a00;
    position: relative;
    min-height: 55px;
    padding: calc(0.5em - 2px) 85px 0.5em 1.125em;
    font-size: 22px;
}
.button-cta[class*="glyph-"]:before,
.button-cta[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
}
.button-cta:hover {
    color: #fff !important;
    background: #ffb238;
    -webkit-transition: all 0.2s ease-in;
    -o-transition: all 0.2s ease-in;
    transition: all 0.2s ease-in;
}
.button-cta:before {
    font-family: glyphs;
    content: "\E979";
    height: 60px;
    line-height: 60px;
    text-align: center;
    color: #fff;
    top: 50%;
    margin-top: -30px;
    z-index: 1;
}
.button-cta:after,
.button-cta:before {
    display: block;
    width: 60px;
    position: absolute;
    right: 0;
}
.button-cta:after {
    content: "";
    height: 100%;
    background-color: rgba(213, 122, 0, 0.65);
    -webkit-box-shadow: inset 0 -2px 0 0 #c16e00;
    box-shadow: inset 0 -2px 0 0 #c16e00;
    top: 0;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
@media screen and (max-width: 600px) {
    .button-cta.large,
    .button-cta.xlarge {
        font-size: 1.85rem;
    }
}
@media screen and (max-width: 500px) {
    .button-cta.large,
    .button-cta.medium,
    .button-cta.xlarge {
        font-size: 1.35rem;
    }
}
@media screen and (max-width: 320px) {
    .button-cta.large,
    .button-cta.medium,
    .button-cta.xlarge {
        font-size: 1.15rem;
    }
}
.button-cta.default {
    font-size: 16px;
    padding-right: 1.125em;
}
.button-cta.default:after,
.button-cta.default:before {
    display: none;
}
.button-cta.medium {
    font-size: 18px;
}
.button-cta.small {
    font-size: 14px;
}
.button-cta.no-icon {
    padding-right: 1.125em;
}
.button-cta.no-icon:after,
.button-cta.no-icon:before {
    display: none;
}
@media screen and (max-width: 375px) {
    .button-cta {
        font-size: 18px;
    }
}
.button-text {
    font-weight: 500;
    text-decoration: underline;
    color: #7996b4;
    background: none;
    cursor: pointer;
    opacity: 0.65;
    font-size: 14px !important;
}
.button-text:hover {
    color: #1c2c3e;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.button-link {
    display: inline-block;
    font-size: 18px;
    color: #4d92ff;
}
.button-link.backward:before,
.button-link.forward:before {
    display: inline-block;
    font-family: glyphs;
    font-size: 1.15em;
    margin-right: 0.325em;
    margin-top: 2px;
    vertical-align: top;
}
.button-link.forward:before {
    content: "\EA01";
}
.youtube-play-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
}
.youtube-play {
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100px;
    height: 70px;
    background-image: url(images/youtube-play.png);
    background-position: top;
    background-repeat: no-repeat;
    background-size: contain;
    cursor: pointer;
    -webkit-animation: button-grow 1s infinite;
    -o-animation: button-grow 1s infinite;
    animation: button-grow 1s infinite;
    -o-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.youtube-play.large {
    width: 100px;
    height: 100px;
}
.youtube-play.center {
    top: 50%;
    left: 50%;
}
.youtube-play.orange {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOTgiIGhlaWdodD0iMTk4LjAwMSIgdmlld0JveD0iMCAwIDE5OCAxOTguMDAxIj4KICA8cGF0aCBpZD0iU3VidHJhY3Rpb25fMjEiIGRhdGEtbmFtZT0iU3VidHJhY3Rpb24gMjEiIGQ9Ik05OSwxOThhOTkuNzMsOTkuNzMsMCwwLDEtMTkuOTUyLTIuMDExLDk4LjQ1Niw5OC40NTYsMCwwLDEtMzUuNC0xNC45QTk5LjI5LDk5LjI5LDAsMCwxLDcuNzgsMTM3LjUzNmE5OC41LDk4LjUsMCwwLDEtNS43NjktMTguNTg0LDk5Ljk2NSw5OS45NjUsMCwwLDEsMC0zOS45LDk4LjQ1Nyw5OC40NTcsMCwwLDEsMTQuOS0zNS40QTk5LjI5LDk5LjI5LDAsMCwxLDYwLjQ2NSw3Ljc4LDk4LjUsOTguNSwwLDAsMSw3OS4wNDgsMi4wMTFhOTkuOTY1LDk5Ljk2NSwwLDAsMSwzOS45LDAsOTguNDU3LDk4LjQ1NywwLDAsMSwzNS40LDE0LjlBOTkuMjg5LDk5LjI4OSwwLDAsMSwxOTAuMjIsNjAuNDY1YTk4LjUsOTguNSwwLDAsMSw1Ljc2OSwxOC41ODMsOTkuOTY3LDk5Ljk2NywwLDAsMSwwLDM5LjksOTguNDU4LDk4LjQ1OCwwLDAsMS0xNC45LDM1LjQsOTkuMjg4LDk5LjI4OCwwLDAsMS00My41NTcsMzUuODY5LDk4LjUsOTguNSwwLDAsMS0xOC41ODMsNS43NjlBOTkuNzMyLDk5LjczMiwwLDAsMSw5OSwxOThaTTczLjUwOCw0OS4zM2E2LjEsNi4xLDAsMCwwLTQuMTgsMS42OTJBNS45MjQsNS45MjQsMCwwLDAsNjcuNSw1NS4zMzl2ODcuMzIyYTUuOTIzLDUuOTIzLDAsMCwwLDEuODI5LDQuMzE3LDYuMSw2LjEsMCwwLDAsNC4xODEsMS42OTIsNS45MTMsNS45MTMsMCwwLDAsMi45NjctLjhsNzYuNDA3LTQzLjY2MWE2LDYsMCwwLDAsMC0xMC40MTlMNzYuNDc3LDUwLjEzQTUuOTE3LDUuOTE3LDAsMCwwLDczLjUwOCw0OS4zM1oiIGZpbGw9IiNlMThhMDAiLz4KPC9zdmc+Cg==);
}
@-webkit-keyframes button-grow {
    0% {
        -webkit-transform: scale(1);
    }
    50% {
        -webkit-transform: scale(1.15);
    }
    to {
        -webkit-transform: scale(1);
    }
}
@-o-keyframes button-grow {
    0% {
        -o-transform: scale(1);
    }
    50% {
        -o-transform: scale(1.15);
    }
    to {
        -o-transform: scale(1);
    }
}
@keyframes button-grow {
    0% {
        -webkit-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1);
    }
    50% {
        -webkit-transform: scale(1.15);
        -o-transform: scale(1.15);
        transform: scale(1.15);
    }
    to {
        -webkit-transform: scale(1);
        -o-transform: scale(1);
        transform: scale(1);
    }
}
.youtube-play.on-hover {
    -webkit-animation: none;
    -o-animation: none;
    animation: none;
    top: -15%;
}
.youtube-play.on-hover,
.youtube-play.on-hover:hover {
    -o-transform: translate(0);
    -webkit-transform: translate(0);
    transform: translate(0);
}
.youtube-play.on-hover:hover {
    -webkit-animation: button-grow 1s infinite;
    -o-animation: button-grow 1s infinite;
    animation: button-grow 1s infinite;
}
@media screen and (max-width: 420px) {
    .youtube-play {
        width: 90px;
        height: 60px;
    }
}
.ui-group {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
}
.ui-group .ui-group-item {
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    height: 45px;
    line-height: 45px;
}
.ui-group .ui-group-item .ui-select {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.ui-group .ui-action,
.ui-group .ui-action[class*="button-"] {
    padding-top: 0;
    padding-bottom: 0;
    height: 45px;
    min-height: 45px;
    line-height: 45px;
    z-index: 10;
}
.ui-group .ui-action:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
@media screen and (max-width: 420px) {
    .ui-group.responsive {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
    .ui-group.responsive .ui-action,
    .ui-group.responsive .ui-group-item {
        -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    }
    .ui-group.responsive .ui-group-item > * {
        display: block;
    }
    .ui-group.responsive .ui-group-item .ui-select {
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
    }
    .ui-group.responsive .ui-action {
        margin-top: 12px;
    }
    .ui-group.responsive .ui-action:last-child {
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
    }
}
.ui-box {
    padding: 24px;
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    position: relative;
}
.ui-box .ui-box-close {
    width: 1rem;
    height: 1rem;
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    z-index: 5;
}
.ui-box .ui-box-close:before {
    font-size: 0.75rem;
    line-height: 1rem;
    text-shadow: 1px 1px 0 hsla(0, 0%, 100%, 0.65);
}
.ui-box .ui-box-close:hover {
    -o-transform: scale(1);
    -webkit-transform: scale(1);
    transform: scale(1);
}
.ui-box .ui-box-close:hover:before {
    color: #1f65d3;
}
.ui-box.padding {
    padding: 2rem;
}
.ui-box.padding-small {
    padding: 1rem;
}
.ui-box[class*="border-"]:after {
    display: block;
    content: "";
    position: absolute;
}
.ui-box[class*="border-"][class*="-blue"]:after,
.ui-box[class*="border-"][class*="-primary"]:after {
    background-color: #4d92ff;
}
.ui-box[class*="border-"][class*="-purple"]:after,
.ui-box[class*="border-"][class*="secondary"]:after {
    background-color: #d68d00;
}
.ui-box[class*="border-left"]:after,
.ui-box[class*="border-right"]:after {
    width: 0.25rem;
    height: 100%;
    top: 0;
}
.ui-box[class*="border-bottom"]:after,
.ui-box[class*="border-top"]:after {
    width: 100%;
    height: 4px;
    left: 0;
}
.ui-box[class*="border-left"]:after {
    left: 0;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
}
.ui-box[class*="border-right"]:after {
    right: 0;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
}
.ui-box[class*="border-bottom"]:after {
    bottom: 0;
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
}
.ui-box[class*="border-top"]:after {
    top: 0;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
}
.ui-box.float {
    padding: 24px;
}
.ui-box.float,
.ui-box.menu {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    -webkit-box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
}
.ui-box.menu {
    background-color: #f2f6ff;
    display: inline-block;
    padding: 1rem;
    width: auto;
    height: auto;
}
.ui-box.menu[class*="pointer-"]:after,
.ui-box.menu[class*="pointer-"]:before {
    display: block;
    font-family: glyphs;
    width: 16px;
    font-size: 26px;
    line-height: 32px;
    text-align: left;
    position: absolute;
}
.ui-box.menu[class*="pointer-"]:before {
    color: #f2f6ff;
    text-align: left;
    z-index: -1;
}
.ui-box.menu[class*="pointer-"]:after {
    color: #c1cfe6;
    z-index: -2;
}
.ui-box.menu.pointer-white[class*="pointer-"]:before {
    color: #fff;
}
.ui-box.menu.pointer-left:after,
.ui-box.menu.pointer-left:before,
.ui-box.menu.pointer-right:after,
.ui-box.menu.pointer-right:before {
    top: 12px;
}
.ui-box.menu.pointer-left.center:after,
.ui-box.menu.pointer-left.center:before,
.ui-box.menu.pointer-right.center:after,
.ui-box.menu.pointer-right.center:before {
    -o-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    top: 50%;
}
.ui-box.menu.pointer-down:after,
.ui-box.menu.pointer-down:before,
.ui-box.menu.pointer-up:after,
.ui-box.menu.pointer-up:before {
    left: 12px;
}
.ui-box.menu.pointer-down.center:after,
.ui-box.menu.pointer-down.center:before,
.ui-box.menu.pointer-up.center:after,
.ui-box.menu.pointer-up.center:before {
    -o-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    left: 50%;
    margin-left: -8px;
}
.ui-box.menu.pointer-left:after,
.ui-box.menu.pointer-left:before {
    content: glyph(pointer-left);
}
.ui-box.menu.pointer-left:before {
    left: -12px;
}
.ui-box.menu.pointer-left:after {
    left: -14px;
}
.ui-box.menu.pointer-right:after,
.ui-box.menu.pointer-right:before {
    content: glyph(pointer-right);
}
.ui-box.menu.pointer-right:before {
    right: -5px;
}
.ui-box.menu.pointer-right:after {
    right: -7px;
}
.ui-box.menu.pointer-up:after,
.ui-box.menu.pointer-up:before {
    content: "\E95E";
}
.ui-box.menu.pointer-up:before {
    top: -18px;
}
.ui-box.menu.pointer-up:after {
    top: -20px;
}
.ui-box.menu.pointer-down:after,
.ui-box.menu.pointer-down:before {
    content: "\E95B";
}
.ui-box.menu.pointer-down:before {
    bottom: -16px;
}
.ui-box.menu.pointer-down:after {
    bottom: -18px;
}
.ui-box.card {
    padding: 2rem;
}
.ui-box.card .card-head {
    position: relative;
}
.ui-box.card .card-head[class*="glyph-"]:before,
.ui-box.card .card-head[class*="icon-"]:before {
    width: 4rem;
    height: 4rem;
    font-size: 2.45rem;
    line-height: 4rem;
    text-align: center;
    position: absolute;
    top: 50%;
    left: -5rem;
    margin-top: -2rem;
}
.ui-box.card .card-toggle {
    cursor: pointer;
}
.ui-box.card .card-content {
    margin: 0;
}
.ui-box.card[class*="-blue"] .card-head[class*="glyph-"]:before,
.ui-box.card[class*="-blue"] .card-head[class*="icon-"]:before,
.ui-box.card[class*="-primary"] .card-head[class*="glyph-"]:before,
.ui-box.card[class*="-primary"] .card-head[class*="icon-"]:before {
    color: #4d92ff;
}
.ui-box.card[class*="-purple"] .card-head[class*="glyph-"]:before,
.ui-box.card[class*="-purple"] .card-head[class*="icon-"]:before,
.ui-box.card[class*="secondary"] .card-head[class*="glyph-"]:before,
.ui-box.card[class*="secondary"] .card-head[class*="icon-"]:before {
    color: #ffa20f;
}
.ui-box.card.shadow:hover {
    -webkit-box-shadow: 0 0 0 4px rgba(6, 49, 116, 0.25), 0 4px 1px -2px rgba(6, 49, 116, 0.25);
    box-shadow: 0 0 0 4px rgba(6, 49, 116, 0.25), 0 4px 1px -2px rgba(6, 49, 116, 0.25);
    border: 1px solid #c1cfe6;
}
.ui-box.card.shadow[class*="-blue"]:hover,
.ui-box.card.shadow[class*="-primary"]:hover {
    -webkit-box-shadow: 0 0 0 4px rgba(77, 146, 255, 0.175), 0 4px 1px -2px rgba(77, 146, 255, 0.15);
    box-shadow: 0 0 0 4px rgba(77, 146, 255, 0.175), 0 4px 1px -2px rgba(77, 146, 255, 0.15);
    border: 1px solid #8db8fa;
}
.ui-box.card.shadow[class*="-purple"]:hover,
.ui-box.card.shadow[class*="secondary"]:hover {
    -webkit-box-shadow: 0 0 0 4px rgba(255, 162, 15, 0.175), 0 4px 1px -2px rgba(255, 162, 15, 0.15);
    box-shadow: 0 0 0 4px rgba(255, 162, 15, 0.175), 0 4px 1px -2px rgba(255, 162, 15, 0.15);
    border: 1px solid #f4b450;
}
.ui-box.app {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    background-color: #f2f6ff;
    padding: 2rem;
    -webkit-box-shadow: 0 3px 0 0 rgba(6, 49, 116, 0.25);
    box-shadow: 0 3px 0 0 rgba(6, 49, 116, 0.25);
}
.ui-box.app .ui-box-footer,
.ui-box.app .ui-box-header {
    position: relative;
}
.ui-box.app .ui-box-footer:before,
.ui-box.app .ui-box-header:before {
    display: block;
    content: "";
    width: calc(100% + 4rem);
    height: 1px;
    background-color: #d6e2f7;
    position: absolute;
    left: -2rem;
}
.ui-box.app .ui-box-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1.25rem;
}
.ui-box.app .ui-box-header:before {
    bottom: 0;
}
.ui-box.app .ui-box-footer {
    text-align: center;
    margin-top: 1.5rem;
    margin-bottom: -2rem;
}
.ui-box.app .ui-box-footer:before {
    top: 0;
}
.ui-box.app .ui-box-title {
    font-size: 1em;
    color: #4d92ff;
    margin: 0;
    text-shadow: 1px 1px 0 hsla(0, 0%, 100%, 0.5);
}
.ui-box.app .ui-autocomplete {
    background-color: #fff;
}
.ui-box.app .button-text.block {
    background-color: hsla(0, 0%, 100%, 0.65);
}
.ui-box.app .button-text.block:hover {
    background-color: #fff;
    -webkit-transition: background-color 0.15s ease-out;
    -o-transition: background-color 0.15s ease-out;
    transition: background-color 0.15s ease-out;
}
.ui-box.app:first-of-type {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.ui-box.app:last-of-type {
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
}
.ui-box.notification {
    padding: 15px 24px;
    -webkit-box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
}
.ui-box.glow,
.ui-box.notification {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
}
.ui-box.glow {
    padding: 24px;
    -webkit-box-shadow: 0 0 8px 0 rgba(214, 226, 247, 0.75);
    box-shadow: 0 0 8px 0 rgba(214, 226, 247, 0.75);
}
.ui-box.glow:hover {
    border-color: #8db8fa;
    -webkit-box-shadow: 0 0 10px 0 rgba(193, 207, 230, 0.9);
    box-shadow: 0 0 10px 0 rgba(193, 207, 230, 0.9);
    -webkit-transition: all 0.2s ease-out;
    -o-transition: all 0.2s ease-out;
    transition: all 0.2s ease-out;
}
.ui-box.category-box {
    padding-left: 78.75px;
}
.ui-box.category-box > a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    height: 100%;
}
.ui-box.category-box > a .content {
    -ms-flex-item-align: center;
    align-self: center;
}
.ui-box.category-box:before {
    display: block;
    content: "";
    width: 35px;
    height: 35px;
    background-size: contain;
    position: absolute;
    top: 50%;
    left: 24px;
    margin-top: -17.5px;
}
.ui-box.category-box h3 {
    font-size: 16px;
    margin-bottom: 0.125em;
}
.ui-box.category-box p {
    font-size: 14px;
    color: #385575;
}
.ui-box.category-box.sector-agencies:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-marketing:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-fitness:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-medical-practices:before {
    background: url(images/icons/medical-practices.svg) no-repeat 50%;
}
.ui-box.category-box.sector-therapists:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-handicraft:before {
    background: url(images/icons/handicraft.svg) no-repeat 50%;
}
.ui-box.category-box.sector-alternative-power:before {
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI0LjEuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCAyMDAgMjAwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCAyMDAgMjAwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6I0NDREJGNTt9Cgkuc3Qxe2ZpbGw6IzRGODdFMDt9Cjwvc3R5bGU+CjxnIGlkPSJyZW5ld2FibGUtZW5lcmd5LXNvbGFyLXBvd2VyIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjI1IDAuMjUpIj4KCTxnIGlkPSJQYXRoXzEwNDc5Ij4KCQk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMTYxLjQsODIuOWwtNDQuMSw1MS40Yy0xLDEuMi0wLjgsMywwLjMsNGMwLjUsMC40LDEuMSwwLjcsMS44LDAuN0gxMzl2NDQuOGw0NC4xLTUxLjRjMS0xLjIsMC44LTMtMC4zLTQKCQkJYy0wLjUtMC40LTEuMS0wLjctMS44LTAuN2gtMTkuNlY4Mi45eiIvPgoJPC9nPgoJPGcgaWQ9IlBhdGhfMTA0ODAiPgoJCTxwYXRoIGNsYXNzPSJzdDEiIGQ9Ik0xMDUuMywxNzcuNWMtMjkuMiwwLTU1LjYtMTYuMS02OS00MS45Yy0xLjMtMi41LTAuMy01LjUsMi4xLTYuN2MyLjUtMS4zLDUuNS0wLjMsNi43LDIuMQoJCQljMTEuNywyMi41LDM0LjgsMzYuNiw2MC4yLDM2LjZjMS43LDAsMy41LTAuMSw1LjMtMC4yYzIuOC0wLjIsNS4yLDEuOSw1LjQsNC42YzAuMiwyLjgtMS45LDUuMi00LjYsNS40CgkJCUMxMDkuMywxNzcuNSwxMDcuMywxNzcuNSwxMDUuMywxNzcuNUMxMDUuMywxNzcuNSwxMDUuMywxNzcuNSwxMDUuMywxNzcuNXoiLz4KCTwvZz4KCTxnIGlkPSJQYXRoXzEwNDgxIj4KCQk8cGF0aCBjbGFzcz0ic3QxIiBkPSJNMTY2LjcsNjUuNmMtMS43LDAtMy4zLTAuOC00LjItMi4zQzE1Miw0Ni45LDEzNSwzNS43LDExNS44LDMyLjdjLTIuNy0wLjQtNC42LTMtNC4yLTUuNwoJCQljMC40LTIuNywzLTQuNiw1LjctNC4yYzIyLjEsMy40LDQxLjYsMTYuMiw1My42LDM1YzEuNSwyLjMsMC44LDUuNC0xLjUsNi45QzE2OC42LDY1LjMsMTY3LjYsNjUuNiwxNjYuNyw2NS42eiIvPgoJPC9nPgoJPGcgaWQ9IkVsbGlwc2VfNzkwIj4KCQk8cGF0aCBjbGFzcz0ic3QxIiBkPSJNNjAuNiw4Mi4zYy0xMiwwLTIxLjgtOS44LTIxLjgtMjEuOHM5LjgtMjEuOCwyMS44LTIxLjhjMTIsMCwyMS44LDkuOCwyMS44LDIxLjhTNzIuNiw4Mi4zLDYwLjYsODIuM3oKCQkJIE02MC42LDQ4LjdjLTYuNSwwLTExLjgsNS4zLTExLjgsMTEuOFM1NCw3Mi4zLDYwLjYsNzIuM3MxMS44LTUuMywxMS44LTExLjhTNjcuMSw0OC43LDYwLjYsNDguN3oiLz4KCTwvZz4KCTxnIGlkPSJMaW5lXzM5MyI+CgkJPHBhdGggY2xhc3M9InN0MSIgZD0iTTYwLjYsMzEuOWMtMi44LDAtNS0yLjItNS01VjE1LjdjMC0yLjgsMi4yLTUsNS01czUsMi4yLDUsNXYxMS4yQzY1LjYsMjkuNyw2My4zLDMxLjksNjAuNiwzMS45eiIvPgoJPC9nPgoJPGcgaWQ9IkxpbmVfMzk0Ij4KCQk8cGF0aCBjbGFzcz0ic3QxIiBkPSJNMTA1LjQsNjUuNUg5NC4yYy0yLjgsMC01LTIuMi01LTVzMi4yLTUsNS01aDExLjJjMi44LDAsNSwyLjIsNSw1UzEwOC4xLDY1LjUsMTA1LjQsNjUuNXoiLz4KCTwvZz4KCTxnIGlkPSJMaW5lXzM5NSI+CgkJPHBhdGggY2xhc3M9InN0MSIgZD0iTTYwLjYsMTEwLjRjLTIuOCwwLTUtMi4yLTUtNVY5NC4xYzAtMi44LDIuMi01LDUtNXM1LDIuMiw1LDV2MTEuMkM2NS42LDEwOC4xLDYzLjMsMTEwLjQsNjAuNiwxMTAuNHoiLz4KCTwvZz4KCTxnIGlkPSJMaW5lXzM5NiI+CgkJPHBhdGggY2xhc3M9InN0MSIgZD0iTTI3LDY1LjVIMTUuOGMtMi44LDAtNS0yLjItNS01czIuMi01LDUtNUgyN2MyLjgsMCw1LDIuMiw1LDVTMjkuNyw2NS41LDI3LDY1LjV6Ii8+Cgk8L2c+Cgk8ZyBpZD0iTGluZV8zOTciPgoJCTxwYXRoIGNsYXNzPSJzdDEiIGQ9Ik0zNi44LDQxLjhjLTEuMywwLTIuNi0wLjUtMy41LTEuNWwtNy45LTcuOWMtMi0yLTItNS4xLDAtNy4xYzItMiw1LjEtMiw3LjEsMGw3LjksNy45YzIsMiwyLDUuMSwwLDcuMQoJCQlDMzkuNCw0MS4zLDM4LjEsNDEuOCwzNi44LDQxLjh6Ii8+Cgk8L2c+Cgk8ZyBpZD0iTGluZV8zOTgiPgoJCTxwYXRoIGNsYXNzPSJzdDEiIGQ9Ik04NC4zLDQxLjhjLTEuMywwLTIuNi0wLjUtMy41LTEuNWMtMi0yLTItNS4xLDAtNy4xbDcuOS03LjljMi0yLDUuMS0yLDcuMSwwYzIsMiwyLDUuMSwwLDcuMWwtNy45LDcuOQoJCQlDODYuOSw0MS4zLDg1LjYsNDEuOCw4NC4zLDQxLjh6Ii8+Cgk8L2c+Cgk8ZyBpZD0iTGluZV8zOTkiPgoJCTxwYXRoIGNsYXNzPSJzdDEiIGQ9Ik05Mi4yLDk3LjJjLTEuMywwLTIuNi0wLjUtMy41LTEuNWwtNy45LTcuOWMtMi0yLTItNS4xLDAtNy4xYzItMiw1LjEtMiw3LjEsMGw3LjksNy45YzIsMiwyLDUuMSwwLDcuMQoJCQlDOTQuOCw5Ni43LDkzLjUsOTcuMiw5Mi4yLDk3LjJ6Ii8+Cgk8L2c+Cgk8ZyBpZD0iTGluZV80MDAiPgoJCTxwYXRoIGNsYXNzPSJzdDEiIGQ9Ik0yOC45LDk3LjJjLTEuMywwLTIuNi0wLjUtMy41LTEuNWMtMi0yLTItNS4xLDAtNy4xbDcuOS03LjljMi0yLDUuMS0yLDcuMSwwYzIsMiwyLDUuMSwwLDcuMWwtNy45LDcuOQoJCQlDMzEuNCw5Ni43LDMwLjEsOTcuMiwyOC45LDk3LjJ6Ii8+Cgk8L2c+Cgk8ZyBpZD0iUGF0aF8xMDQ4MiI+CgkJPHBhdGggY2xhc3M9InN0MSIgZD0iTTEzOSwxODguOGMtMC42LDAtMS4yLTAuMS0xLjctMC4zYy0yLTAuNy0zLjMtMi42LTMuMy00LjdWMTQ0aC0xNC42Yy0xLjgsMC0zLjYtMC43LTUtMS44CgkJCWMtMS42LTEuMy0yLjYtMy4yLTIuOC01LjNjLTAuMi0yLjEsMC41LTQuMSwxLjgtNS43bDQ0LjItNTEuNGMxLjQtMS42LDMuNi0yLjIsNS41LTEuNGMyLDAuNywzLjMsMi42LDMuMyw0Ljd2MzkuOEgxODEKCQkJYzEuOCwwLDMuNiwwLjcsNSwxLjhjMy4zLDIuOCwzLjcsNy43LDAuOSwxMUwxNDIuNywxODdDMTQxLjgsMTg4LjEsMTQwLjQsMTg4LjgsMTM5LDE4OC44eiBNMTM5LDEzNGMyLjgsMCw1LDIuMiw1LDV2MzEuMwoJCQlsMzIuMi0zNy41aC0xNC44Yy0yLjgsMC01LTIuMi01LTVWOTYuNEwxMjQuMSwxMzRIMTM5eiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo=)
        no-repeat 50%;
}
.ui-box.category-box.sector-energy-suppliers:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-car-dealerships:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-entertainer:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-photography:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-beverages-food:before {
    background: url(images/icons/beverages-food.svg) no-repeat 50%;
}
.ui-box.category-box.sector-real-estate:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-farmers:before {
    background: url(images/icons/farmers.svg) no-repeat 50%;
}
.ui-box.category-box.sector-engineering:before {
    background: url(images/icons/engineering.svg) no-repeat 50%;
}
.ui-box.category-box.sector-speakers:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-experts:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-social-employers:before {
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI0LjEuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCAyMDAgMjAwIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCAyMDAgMjAwOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+Cgkuc3Qwe2ZpbGw6I0NDREJGNTt9Cgkuc3Qxe2ZpbGw6IzRGODdFMDt9Cjwvc3R5bGU+CjxnIGlkPSJtdWx0aXBsZS11c2Vycy0xIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjUgMC41KSI+Cgk8ZyBpZD0iRWxsaXBzZV84MDIiPgoJCTxjaXJjbGUgY2xhc3M9InN0MCIgY3g9IjM5IiBjeT0iNzEuMSIgcj0iMjAuOCIvPgoJCTxwYXRoIGNsYXNzPSJzdDEiIGQ9Ik0zOSw5Ni45Yy0xNC4yLDAtMjUuOC0xMS42LTI1LjgtMjUuOGMwLTE0LjIsMTEuNi0yNS44LDI1LjgtMjUuOHMyNS44LDExLjYsMjUuOCwyNS44CgkJCUM2NC44LDg1LjQsNTMuMiw5Ni45LDM5LDk2Ljl6IE0zOSw1NS4zYy04LjcsMC0xNS44LDcuMS0xNS44LDE1LjhTMzAuMyw4Ni45LDM5LDg2LjlzMTUuOC03LjEsMTUuOC0xNS44UzQ3LjcsNTUuMywzOSw1NS4zeiIvPgoJPC9nPgoJPGcgaWQ9IlBhdGhfMTA1MjgiPgoJCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik02NS41LDEyNmMwLTE0LjYtMTEuOS0yNi41LTI2LjUtMjYuNVMxMi41LDExMS40LDEyLjUsMTI2djIyLjdoMTJsMy4yLDM3LjhoMjIuN2wzLjItMzcuOGgxMlYxMjZ6Ii8+CgkJPHBhdGggY2xhc3M9InN0MSIgZD0iTTUwLjMsMTkxLjVIMjcuNmMtMi42LDAtNC44LTItNS00LjZsLTIuOC0zMy4yaC03LjRjLTIuOCwwLTUtMi4yLTUtNVYxMjZjMC0xNy40LDE0LjEtMzEuNSwzMS41LTMxLjUKCQkJczMxLjUsMTQuMSwzMS41LDMxLjV2MjIuN2MwLDIuOC0yLjIsNS01LDVoLTcuNGwtMi44LDMzLjJDNTUuMSwxODkuNSw1Mi45LDE5MS41LDUwLjMsMTkxLjV6IE0zMi4yLDE4MS41aDEzLjVsMi44LTMzLjIKCQkJYzAuMi0yLjYsMi40LTQuNiw1LTQuNmg3VjEyNmMwLTExLjgtOS42LTIxLjUtMjEuNS0yMS41cy0yMS41LDkuNi0yMS41LDIxLjV2MTcuN2g3YzIuNiwwLDQuOCwyLDUsNC42TDMyLjIsMTgxLjV6Ii8+Cgk8L2c+Cgk8ZyBpZD0iRWxsaXBzZV84MDMiPgoJCTxjaXJjbGUgY2xhc3M9InN0MCIgY3g9IjE2MCIgY3k9IjcxLjEiIHI9IjIwLjgiLz4KCQk8cGF0aCBjbGFzcz0ic3QxIiBkPSJNMTYwLDk2LjljLTE0LjIsMC0yNS44LTExLjYtMjUuOC0yNS44YzAtMTQuMiwxMS42LTI1LjgsMjUuOC0yNS44czI1LjgsMTEuNiwyNS44LDI1LjgKCQkJQzE4NS44LDg1LjQsMTc0LjIsOTYuOSwxNjAsOTYuOXogTTE2MCw1NS4zYy04LjcsMC0xNS44LDcuMS0xNS44LDE1LjhzNy4xLDE1LjgsMTUuOCwxNS44czE1LjgtNy4xLDE1LjgtMTUuOAoJCQlTMTY4LjcsNTUuMywxNjAsNTUuM3oiLz4KCTwvZz4KCTxnIGlkPSJQYXRoXzEwNTI5Ij4KCQk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMTg2LjUsMTI2YzAtMTQuNi0xMS45LTI2LjUtMjYuNS0yNi41Yy0xNC42LDAtMjYuNSwxMS45LTI2LjUsMjYuNXYyMi43aDEybDMuMiwzNy44aDIyLjdsMy4yLTM3LjhoMTIKCQkJTDE4Ni41LDEyNnoiLz4KCQk8cGF0aCBjbGFzcz0ic3QxIiBkPSJNMTcxLjQsMTkxLjVoLTIyLjdjLTIuNiwwLTQuOC0yLTUtNC42bC0yLjgtMzMuMmgtNy40Yy0yLjgsMC01LTIuMi01LTVWMTI2YzAtMTcuNCwxNC4xLTMxLjUsMzEuNS0zMS41CgkJCWMxNy40LDAsMzEuNSwxNC4xLDMxLjUsMzEuNXYwbDAsMjIuN2MwLDEuMy0wLjUsMi42LTEuNSwzLjVzLTIuMiwxLjUtMy41LDEuNWgtNy40bC0yLjgsMzMuMkMxNzYuMSwxODkuNSwxNzQsMTkxLjUsMTcxLjQsMTkxLjV6CgkJCSBNMTUzLjMsMTgxLjVoMTMuNWwyLjgtMzMuMmMwLjItMi42LDIuNC00LjYsNS00LjZoN2wwLTE3Ljd2MGMwLTExLjgtOS42LTIxLjUtMjEuNS0yMS41Yy0xMS44LDAtMjEuNSw5LjYtMjEuNSwyMS41djE3LjdoNwoJCQljMi42LDAsNC44LDIsNSw0LjZMMTUzLjMsMTgxLjV6IE0xOTEuNSwxMjZMMTkxLjUsMTI2TDE5MS41LDEyNnoiLz4KCTwvZz4KCTxnIGlkPSJFbGxpcHNlXzgwNCI+CgkJPHBhdGggY2xhc3M9InN0MSIgZD0iTTk5LjUsNTEuNWMtMTIuMSwwLTIyLTkuOS0yMi0yMmMwLTEyLjEsOS45LTIyLDIyLTIyczIyLDkuOSwyMiwyMkMxMjEuNSw0MS43LDExMS42LDUxLjUsOTkuNSw1MS41egoJCQkgTTk5LjUsMTcuNWMtNi42LDAtMTIsNS40LTEyLDEyYzAsNi42LDUuNCwxMiwxMiwxMmM2LjYsMCwxMi01LjQsMTItMTJDMTExLjUsMjIuOSwxMDYuMSwxNy41LDk5LjUsMTcuNXoiLz4KCTwvZz4KCTxnIGlkPSJQYXRoXzEwNTMwIj4KCQk8cGF0aCBjbGFzcz0ic3QxIiBkPSJNMTA3LjEsMTM0LjhIOTJjLTIuNSwwLTQuNy0xLjktNS00LjRsLTMuMy0yOS42aC02LjljLTIuOCwwLTUtMi4yLTUtNVY3Ni44YzAtMTUuMywxMi40LTI3LjcsMjcuNy0yNy43aDAKCQkJYzE1LjMsMCwyNy43LDEyLjQsMjcuNywyNy43djE4LjljMCwyLjgtMi4yLDUtNSw1aC02LjlsLTMuMywyOS42QzExMS44LDEzMi44LDEwOS42LDEzNC44LDEwNy4xLDEzNC44eiBNOTYuNCwxMjQuOGg2LjJsMy4zLTI5LjYKCQkJYzAuMy0yLjUsMi40LTQuNCw1LTQuNGg2LjNWNzYuOGMwLTkuOC03LjktMTcuNy0xNy43LTE3LjdoMGMtOS44LDAtMTcuNyw3LjktMTcuNywxNy43djEzLjloNi4zYzIuNSwwLDQuNywxLjksNSw0LjRMOTYuNCwxMjQuOHoKCQkJIi8+Cgk8L2c+CjwvZz4KPC9zdmc+Cg==)
        no-repeat 50%;
}
.ui-box.category-box.sector-tax-consultants:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-management-consultants:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-associations-clubs:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-insurance:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-box.category-box.sector-software:before {
    background: url(data:image/svg+xml;base64,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)
        no-repeat 50%;
}
.ui-scroller {
    overflow: hidden;
}
.ui-scroller::-webkit-scrollbar {
    display: none;
}
.ui-scroller {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.ui-scroller.vertical {
    overflow-y: scroll;
}
.ui-scroller.horizontal {
    overflow-x: scroll;
}
.ui-scroller.dropdown {
    max-height: 360px;
}
.featured-post {
    position: relative;
    overflow: hidden;
}
.featured-post > a {
    display: block;
}
.featured-post .content {
    padding: 15px;
    z-index: 3;
}
.featured-post .content,
.featured-post .content:before {
    display: block;
    width: 100%;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    position: absolute;
    left: 0;
    bottom: 0;
}
.featured-post .content:before {
    content: "";
    background-image: -o-linear-gradient(rgba(28, 44, 62, 0) 0, rgba(28, 44, 62, 0.5) 35%, rgba(28, 44, 62, 0.925) 75%, rgba(28, 44, 62, 0.975) 100%);
    background-image: -webkit-linear-gradient(rgba(28, 44, 62, 0), rgba(28, 44, 62, 0.5) 35%, rgba(28, 44, 62, 0.925) 75%, rgba(28, 44, 62, 0.975));
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(28, 44, 62, 0)), color-stop(35%, rgba(28, 44, 62, 0.5)), color-stop(75%, rgba(28, 44, 62, 0.925)), to(rgba(28, 44, 62, 0.975)));
    background-image: linear-gradient(rgba(28, 44, 62, 0), rgba(28, 44, 62, 0.5) 35%, rgba(28, 44, 62, 0.925) 75%, rgba(28, 44, 62, 0.975));
    height: calc(100% + 60px);
    z-index: -1;
}
.featured-post .content .description,
.featured-post .content .title {
    font-weight: 800;
}
.featured-post .content .title {
    font-size: 12px;
    text-transform: uppercase;
    color: #ffa20f;
}
.featured-post .content .description {
    font-size: 16px;
    font-weight: 800;
    color: #fff;
}
.featured-post .background-image {
    width: 100%;
    height: 100%;
    border-radius: 3px;
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
}
.featured-post .background-image img {
    -o-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    display: block;
    width: auto;
    height: 100%;
    position: absolute;
    top: 0;
    left: 50%;
    -webkit-transition: all 0.15s ease-in;
    -o-transition: all 0.15s ease-in;
    transition: all 0.15s ease-in;
}
.featured-post .background-image.position-right img {
    -o-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
    left: auto;
    right: -30%;
}
.featured-post .background-image.position-left img {
    -o-transform: translateX(0);
    -webkit-transform: translateX(0);
    transform: translateX(0);
    left: -30%;
}
.featured-post .background-image.portrait img {
    width: 100%;
    height: auto;
}
.featured-post.has-statistics:before {
    display: block;
    content: "";
    width: 100%;
    height: 100%;
    background-color: #1c2c3e;
    border-radius: 3px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    opacity: 0;
}
.featured-post.has-statistics .case-study-results {
    width: 90%;
    max-width: 90%;
    position: absolute;
    top: 50%;
    left: 5%;
    z-index: 3;
    opacity: 0;
    -o-transform: translateY(-1000px);
    -webkit-transform: translateY(-1000px);
    transform: translateY(-1000px);
}
.featured-post.has-statistics .case-study-results .case-study-result .result-data {
    color: #fff !important;
}
.featured-post.has-statistics .case-study-results .case-study-result .result-description {
    color: #ffa20f !important;
}
.featured-post:hover.has-statistics:before {
    opacity: 0.75;
    -webkit-transition-delay: 0.5s;
    -o-transition-delay: 0.5s;
    transition-delay: 0.5s;
    -webkit-transition: all 0.2s ease-out;
    -o-transition: all 0.2s ease-out;
    transition: all 0.2s ease-out;
}
.featured-post:hover.has-statistics .content {
    text-align: center;
    -webkit-transition-delay: 0.4s;
    -o-transition-delay: 0.4s;
    transition-delay: 0.4s;
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}
.featured-post:hover.has-statistics .case-study-results {
    opacity: 1;
    -o-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transition-delay: 1.5s;
    -o-transition-delay: 1.5s;
    transition-delay: 1.5s;
    -webkit-transition: all 0.5s ease-out;
    -o-transition: all 0.5s ease-out;
    transition: all 0.5s ease-out;
}
.featured-post:hover .background-image img {
    height: 105%;
    top: -2.5%;
    -webkit-transition: all 0.3s ease-out;
    -o-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}
.featured-post:hover .background-image.portrait img {
    width: 105%;
    height: auto;
}
.alert,
.ui-alert {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    padding: 15px 24px;
    -webkit-box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    margin: 24px auto;
    font-size: 14px;
    position: relative;
}
.alert .close,
.alert .ui-close,
.ui-alert .close,
.ui-alert .ui-close {
    display: block;
    width: 10px;
    height: 10px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
}
.alert .close:before,
.alert .ui-close:before,
.ui-alert .close:before,
.ui-alert .ui-close:before {
    display: block;
    font-family: glyphs;
    content: "\E91E";
    width: 100%;
    height: 100%;
    font-size: 9px;
    text-align: center;
    line-height: 10px;
    color: #aec4ea;
}
.alert .close:hover:before,
.alert .ui-close:hover:before,
.ui-alert .close:hover:before,
.ui-alert .ui-close:hover:before {
    color: #7996b4;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.alert .title,
.ui-alert .title {
    display: block;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.325em;
}
.alert .message,
.alert .message.block,
.ui-alert .message,
.ui-alert .message.block {
    display: block;
}
.alert .message.medium,
.ui-alert .message.medium {
    font-size: 1.125em;
}
.alert .button-warning,
.ui-alert .button-warning {
    margin-top: 1rem;
}
.alert .message,
.alert .ui-badge,
.ui-alert .message,
.ui-alert .ui-badge {
    vertical-align: middle;
}
.alert .ui-badge + .message,
.ui-alert .ui-badge + .message {
    margin-left: 1.25rem;
}
.alert[class*="glyph-"],
.alert[class*="icon-"],
.ui-alert[class*="glyph-"],
.ui-alert[class*="icon-"] {
    padding-left: 4rem;
}
.alert[class*="glyph-"]:before,
.alert[class*="icon-"]:before,
.ui-alert[class*="glyph-"]:before,
.ui-alert[class*="icon-"]:before {
    -o-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    display: table-cell;
    width: 2rem;
    height: 2rem;
    line-height: 2rem;
    font-size: 1.25rem;
    position: absolute;
    top: 50%;
    left: 1rem;
}
.alert:not(.style-modal).alert-danger,
.alert:not(.style-modal).error,
.alert:not(.style-modal).success,
.alert:not(.style-modal).warning,
.ui-alert:not(.style-modal).alert-danger,
.ui-alert:not(.style-modal).error,
.ui-alert:not(.style-modal).success,
.ui-alert:not(.style-modal).warning {
    padding-left: 50px;
    border-left-width: 4px;
    border-left-style: solid;
}
.alert:not(.style-modal).alert-danger:before,
.alert:not(.style-modal).error:before,
.alert:not(.style-modal).success:before,
.alert:not(.style-modal).warning:before,
.ui-alert:not(.style-modal).alert-danger:before,
.ui-alert:not(.style-modal).error:before,
.ui-alert:not(.style-modal).success:before,
.ui-alert:not(.style-modal).warning:before {
    display: block;
    font-family: glyphs;
    width: 20px;
    height: 20px;
    font-size: 8px;
    text-align: center;
    line-height: 21px;
    position: absolute;
    left: 15px;
    top: 50%;
    border-radius: 100%;
    margin-top: -10px;
}
.alert:not(.style-modal).alert-danger,
.alert:not(.style-modal).error,
.ui-alert:not(.style-modal).alert-danger,
.ui-alert:not(.style-modal).error {
    border-left-color: #d64956;
}
.alert:not(.style-modal).alert-danger:before,
.alert:not(.style-modal).error:before,
.ui-alert:not(.style-modal).alert-danger:before,
.ui-alert:not(.style-modal).error:before {
    content: "\E91E";
    color: #fff;
    background-color: #d64956;
}
.alert:not(.style-modal).warning,
.ui-alert:not(.style-modal).warning {
    border-left-color: #f4b450;
}
.alert:not(.style-modal).warning:before,
.ui-alert:not(.style-modal).warning:before {
    content: "\E9C7";
    color: #fff;
    background-color: #f4b450;
}
.alert:not(.style-modal).success,
.ui-alert:not(.style-modal).success {
    border-left-color: #35905d;
}
.alert:not(.style-modal).success:before,
.ui-alert:not(.style-modal).success:before {
    content: "\E91D";
    color: #fff;
    background-color: #35905d;
}
.alert.close,
.alert.no-close .ui-close,
.ui-alert.close,
.ui-alert.no-close .ui-close {
    display: none;
}
.alert.style-modal,
.ui-alert.style-modal {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    -webkit-box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    padding: 1.5rem 2rem;
}
.alert.style-modal:before,
.ui-alert.style-modal:before {
    display: block;
    font-family: glyphs;
    content: "";
    width: 1.5em;
    height: 1.5em;
    line-height: 1.5em;
    text-align: center;
    font-size: 1em;
    position: absolute;
    top: 1.5rem;
    left: 2rem;
    margin-top: -2px;
    color: #637d9b;
}
.alert.style-modal .title,
.ui-alert.style-modal .title {
    margin-left: 1.75rem;
    color: #385575;
}
.alert.style-modal .message,
.ui-alert.style-modal .message {
    color: #1c2c3e;
}
.alert.style-modal a:hover,
.ui-alert.style-modal a:hover {
    color: #4d92ff;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.alert.system-alert,
.ui-alert.system-alert {
    padding: 1.5rem;
}
.alert + .alert,
.alert + .ui-alert,
.ui-alert + .alert,
.ui-alert + .ui-alert {
    margin-top: 1rem;
}
.alert button,
.ui-alert button {
    border: none;
    background: none;
    font-size: 0;
}
.ui-notification {
    display: block;
    width: 100%;
    height: auto;
    text-align: center;
    position: relative;
}
.ui-notification p {
    display: block;
    width: auto;
    margin: 0 0 1.25rem;
    font-size: 1.25rem;
    line-height: 1.125em;
    color: #385575;
}
.ui-notification h1,
.ui-notification h2,
.ui-notification h3 {
    color: #ffa20f;
    margin-top: 0.5em;
}
.ui-notification h1 + p,
.ui-notification h2 + p,
.ui-notification h3 + p {
    margin-top: 1.25em;
}
.ui-notification.system-message {
    padding-top: 3rem;
}
.ui-notification.system-message figure {
    height: auto;
    width: 18.75rem;
    margin: 0 auto;
}
.ui-notification.system-message .klicky {
    width: 6rem;
    height: auto;
}
.ui-notification.system-message.small .klicky {
    width: 4rem;
}
.ui-dropdown-menu {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    -webkit-box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    display: inline-block;
    width: auto;
    min-width: 9rem;
    height: auto;
    max-height: 30rem;
    margin: 0;
    padding: 0.5em 0;
    font-size: 0.925rem;
    position: absolute;
    top: 45px;
    left: 0;
    z-index: 5000;
    overflow-y: scroll;
}
.ui-dropdown-menu::-webkit-scrollbar {
    display: none;
}
.ui-dropdown-menu h1,
.ui-dropdown-menu h2,
.ui-dropdown-menu h3,
.ui-dropdown-menu h4,
.ui-dropdown-menu h5 {
    display: block;
    font-weight: 700;
    font-size: 0.8rem;
    padding: 0 1.5em;
    text-transform: uppercase;
    margin-bottom: 0;
    color: #637d9b;
    letter-spacing: 1px;
}
.ui-dropdown-menu h1 + [class*="list-"],
.ui-dropdown-menu h1 + p,
.ui-dropdown-menu h2 + [class*="list-"],
.ui-dropdown-menu h2 + p,
.ui-dropdown-menu h3 + [class*="list-"],
.ui-dropdown-menu h3 + p,
.ui-dropdown-menu h4 + [class*="list-"],
.ui-dropdown-menu h4 + p,
.ui-dropdown-menu h5 + [class*="list-"],
.ui-dropdown-menu h5 + p {
    margin-top: 0.5rem;
}
.ui-dropdown-menu .menu-group h2,
.ui-dropdown-menu .menu-group h3,
.ui-dropdown-menu .select-group h2,
.ui-dropdown-menu .select-group h3 {
    display: block;
    width: 100%;
    padding: 0 2rem;
    margin-bottom: 1rem;
    border: none;
}
.ui-dropdown-menu .menu-group + .menu-group,
.ui-dropdown-menu .menu-group + .select-group,
.ui-dropdown-menu .select-group + .menu-group,
.ui-dropdown-menu .select-group + .select-group {
    margin-top: 2rem;
}
.ui-dropdown-menu .menu-group:not(:first-child) h2,
.ui-dropdown-menu .menu-group:not(:first-child) h3,
.ui-dropdown-menu .select-group:not(:first-child) h2,
.ui-dropdown-menu .select-group:not(:first-child) h3 {
    border-top: 1px solid #d6e2f7;
    padding-top: 2rem;
}
.ui-dropdown-menu .ui-dropdown-option {
    display: block;
    width: auto;
    padding: 0.25rem 2.5rem 0.25rem 1rem;
    font-weight: 400;
    cursor: pointer;
    white-space: nowrap;
    position: relative;
    clear: both;
}
.ui-dropdown-menu .ui-dropdown-option .ui-tag,
.ui-dropdown-menu .ui-dropdown-option .ui-tool,
.ui-dropdown-menu .ui-dropdown-option .ui-toolbar,
.ui-dropdown-menu .ui-dropdown-option [class*="button-"] {
    display: inline-block;
    vertical-align: top;
    float: right;
    margin-right: -1em;
}
.ui-dropdown-menu .ui-dropdown-option .ui-toolbar .ui-tool {
    float: none;
    margin-right: 0;
}
.ui-dropdown-menu .ui-dropdown-option .selection-match {
    font-weight: 600;
    color: #1c2c3e;
}
.ui-dropdown-menu .ui-dropdown-option:hover {
    color: #fff;
    background: #4d92ff;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.ui-dropdown-menu .ui-dropdown-option.selected {
    position: relative;
    color: #3376de;
    font-weight: 600;
    background-color: #f2f6ff;
}
.ui-dropdown-menu .ui-dropdown-option.selected:before {
    display: block;
    font-family: glyphs;
    content: "\E91D";
    width: 1.5rem;
    height: 1.5rem;
    color: #4d92ff;
    font-size: 1rem;
    line-height: 1.5rem;
    text-align: center;
    position: absolute;
    top: 50%;
    right: 0.5em;
    margin-top: -0.75rem;
}
.ui-dropdown-menu .ui-dropdown-option .ui-checkbox {
    position: absolute;
    right: 0.125em;
    top: 50%;
    margin-top: -0.7125rem;
}
.ui-dropdown-menu .ui-dropdown-option h1,
.ui-dropdown-menu .ui-dropdown-option h2,
.ui-dropdown-menu .ui-dropdown-option h3,
.ui-dropdown-menu .ui-dropdown-option h4,
.ui-dropdown-menu .ui-dropdown-option h5 {
    padding: 0;
}
.ui-dropdown-menu.list-blocks .list-item,
.ui-dropdown-menu .list-blocks .list-item,
.ui-dropdown-menu.list-blocks > div,
.ui-dropdown-menu .list-blocks > div,
.ui-dropdown-menu.list-blocks > li,
.ui-dropdown-menu .list-blocks > li {
    border-color: #d6e2f7;
}
.ui-dropdown-menu.numbers {
    min-width: 15rem;
    padding: 0.25rem;
    text-align: left;
}
.ui-dropdown-menu.numbers .ui-dropdown-option {
    display: block;
    width: 2rem;
    height: 2rem;
    padding: 0;
    border-radius: 3px;
    text-align: center;
    font-size: 1rem;
    line-height: 2rem;
    float: left;
    clear: none;
}
.ui-dropdown-menu.numbers .ui-dropdown-option.selected:before {
    display: none;
}
.ui-dropdown-menu.numbers .ui-dropdown-option:hover {
    font-weight: 600;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.ui-dropdown {
    display: inline-block;
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align: top;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.ui-dropdown .dropdown-toggle,
.ui-dropdown .ui-dropdown-toggle {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    width: auto;
    height: 45px;
    padding: 0 45px 0 1.25em;
    font-weight: 500;
    font-size: 0.925rem;
    line-height: 45px;
    cursor: pointer;
    white-space: nowrap;
}
.ui-dropdown .dropdown-toggle:before,
.ui-dropdown .ui-dropdown-toggle:before {
    font-family: glyphs;
    content: "\E919";
    width: 45px;
    height: 45px;
    line-height: 2.5rem;
    text-align: center;
    color: #8db8fa;
    position: absolute;
    top: 0;
    right: 0;
}
.ui-dropdown .dropdown-toggle:hover:before,
.ui-dropdown .ui-dropdown-toggle:hover:before {
    color: #4d92ff;
    -webkit-transition: color 0.1s ease-out;
    -o-transition: color 0.1s ease-out;
    transition: color 0.1s ease-out;
}
.ui-dropdown .ui-dropdown-menu {
    display: none;
}
.ui-dropdown.is-open .dropdown-toggle {
    z-index: 6000;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.ui-dropdown.is-open .ui-dropdown-menu {
    display: block;
    margin-top: -1px;
    border-top-left-radius: 0;
}
.ui-dropdown.disabled .dropdown-toggle {
    opacity: 0.65;
    cursor: not-allowed;
}
.ui-dropdown.disabled.is-open .dropdown-toggle {
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
}
.ui-dropdown.disabled.is-open .ui-dropdown-menu {
    display: none !important;
}
.ui-dropdown .dropdown-toggle.is-open,
.ui-dropdown .ui-dropdown-toggle.is-open {
    z-index: 6000;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.ui-dropdown .dropdown-toggle.is-open + .ui-dropdown-menu,
.ui-dropdown .ui-dropdown-toggle.is-open + .ui-dropdown-menu {
    display: block;
    margin-top: -1px;
    border-top-left-radius: 0;
}
.ui-dropdown .dropdown-toggle.disabled,
.ui-dropdown .ui-dropdown-toggle.disabled {
    opacity: 0.65;
    cursor: not-allowed;
    border-bottom-left-radius: 45px;
    border-bottom-right-radius: 45px;
}
.ui-dropdown .dropdown-toggle.disabled + .ui-dropdown-menu,
.ui-dropdown .ui-dropdown-toggle.disabled + .ui-dropdown-menu {
    display: none;
}
.ui-dropdown + .ui-dropdown {
    margin-left: 1rem;
}
.ui-dropdown:first-child {
    margin-left: 0;
}
.ui-dropdown.fullwidth {
    display: block;
    width: 100%;
}
.ui-dropdown.fullwidth .dropdown-toggle,
.ui-dropdown.fullwidth .ui-dropdown-menu {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
}
.ui-dropdown.style-text .dropdown-toggle {
    height: auto;
    background: none;
    border: none;
    padding: 0;
    line-height: 1em;
}
.ui-dropdown.style-text .dropdown-toggle:before {
    display: inline;
    width: auto;
    height: auto;
    line-height: 1.25em;
    text-align: right;
    position: static;
    float: right;
    margin-left: 0.25em;
}
.ui-dropdown.style-text .ui-dropdown-menu {
    top: 1.5em;
}
.ui-dropdown.no-icon .dropdown-toggle:before {
    display: none;
}
.ui-dropdown.no-radius-left .dropdown-toggle {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.ui-dropdown.no-radius-right .dropdown-toggle {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
@media screen and (max-width: 1280px) {
    .ui-dropdown .dropdown-toggle {
        height: 45px;
        font-weight: 500;
    }
    .ui-dropdown .dropdown-toggle:before {
        right: 0.05rem;
    }
}
.dropdown-arrow {
    display: inline-block;
}
.dropdown-arrow,
.dropdown-arrow:before {
    width: 1rem;
    height: 1rem;
    text-align: center;
}
.dropdown-arrow:before {
    display: block;
    content: "";
    font-family: glyphs;
    content: "\E919";
    font-size: 0.65em;
    color: #7996b4;
}
.ui-select {
    background: #fff;
    border-radius: 3px;
    border: none;
    -webkit-box-shadow: inset 0 0 0 1px #c1cfe6;
    box-shadow: inset 0 0 0 1px #c1cfe6;
    background-color: #f2f6ff;
    display: inline-block;
    width: auto;
    padding: 0;
    position: relative;
}
.ui-select .ui-select-toggle {
    display: block;
    width: auto;
    padding-left: 1.125em;
    padding-right: 1.125em;
    height: 45px;
    line-height: 45px;
    cursor: pointer;
    white-space: nowrap;
}
.ui-select .ui-select-toggle .dropdown-arrow {
    display: inline-block;
}
.ui-select .ui-select-toggle .dropdown-arrow:before {
    display: block;
}
.ui-select .ui-select-toggle:hover .dropdown-arrow:before {
    color: #4d92ff;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.ui-select .ui-select-menu {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    -webkit-box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    display: none;
    width: auto;
    height: auto;
    min-width: 180px;
    max-width: 400px;
    padding: 0.5rem;
    position: absolute;
    top: 45px;
    background-color: #fff;
    left: 0;
    z-index: 5;
    margin-top: -1px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.ui-select .ui-select-menu .select-option {
    padding: 0.5rem 1.5rem;
    height: auto;
    line-height: 1.125em;
    list-style: none;
    white-space: nowrap;
}
.ui-select .ui-select-menu .select-option:hover {
    cursor: pointer;
}
.ui-select .ui-select-menu .select-option,
.ui-select .ui-select-menu .select-option a {
    color: #637d9b;
}
.ui-select .ui-select-menu .select-option:hover,
.ui-select .ui-select-menu .select-option a:hover {
    color: #1c2c3e;
    font-weight: 500;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.ui-select .ui-select-menu .select-option.is-active,
.ui-select .ui-select-menu .select-option.is-active a {
    font-weight: 500;
    color: #1c2c3e;
}
.ui-select .ui-select-menu .select-option.is-active a:before {
    display: inline;
    font-family: glyphs;
    content: "\E91D";
    color: #4d92ff;
    font-size: 12px;
    margin-right: 6px;
}
.ui-select .ui-select-menu.has-scroller {
    padding-bottom: 0;
}
.ui-select .ui-select-menu.has-scroller:after,
.ui-select .ui-select-menu.has-scroller:before {
    display: block;
    content: "";
    position: absolute;
}
.ui-select .ui-select-menu.has-scroller:before {
    background-image: -o-linear-gradient(hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, 0.85) 60%, #fff);
    background-image: -webkit-linear-gradient(hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, 0.85) 60%, #fff);
    background-image: -webkit-gradient(linear, left top, left bottom, from(hsla(0, 0%, 100%, 0)), color-stop(60%, hsla(0, 0%, 100%, 0.85)), to(#fff));
    background-image: linear-gradient(hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, 0.85) 60%, #fff);
    width: 100%;
    height: 2rem;
    left: 0;
    bottom: 0;
}
.ui-select .ui-select-menu.has-scroller:after {
    width: 1rem;
    height: 1rem;
    font-family: glyphs;
    content: "\E919";
    font-size: 0.65em;
    line-height: 1rem;
    text-align: center;
    color: #7996b4;
    left: 50%;
    bottom: 6px;
    margin-left: -0.5rem;
    z-index: 1;
}
.ui-select .ui-select-menu.has-scroller .select-option:last-of-type {
    margin-bottom: 2rem;
}
.ui-select.block .ui-select-toggle .dropdown-arrow {
    position: absolute;
    top: 2px;
    right: 1rem;
}
.ui-select.block .ui-select-toggle .dropdown-arrow:before {
    color: #4d92ff;
}
.ui-select.block .ui-select-menu {
    width: 100%;
}
.ui-select.shadow {
    -webkit-box-shadow: inset 0 0 0 1px #c1cfe6, 0 2px 3px 0 rgba(154, 176, 216, 0.55);
    box-shadow: inset 0 0 0 1px #c1cfe6, 0 2px 3px 0 rgba(154, 176, 216, 0.55);
}
.ui-select.gradient {
    background-image: -o-linear-gradient(#f2f6ff, #e5eeff);
    background-image: -webkit-linear-gradient(#f2f6ff, #e5eeff);
    background-image: -webkit-gradient(linear, left top, left bottom, from(#f2f6ff), to(#e5eeff));
    background-image: linear-gradient(#f2f6ff, #e5eeff);
}
.ui-select.is-open .ui-select-menu {
    display: block;
}
@media screen and (max-width: 420px) {
    .ui-select .ui-select-menu {
        width: 100%;
        min-width: 100%;
        max-width: 100%;
        z-index: 12;
    }
}
.user-portrait,
.user-thumbnail {
    width: 160px;
    list-style: none;
}
.user-portrait .image,
.user-thumbnail .image {
    display: block;
    width: 160px;
    height: 0;
    padding-top: 160px;
    border-radius: 100%;
    position: relative;
    overflow: hidden;
}
.user-portrait .image img,
.user-thumbnail .image img {
    -o-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    display: block;
    width: 100%;
    height: auto;
    position: absolute;
    top: 0;
    left: 50%;
}
.user-portrait .name,
.user-thumbnail .name {
    text-transform: uppercase;
    font-weight: 800;
    font-size: 12px;
    color: #1c2c3e;
    text-align: center;
    margin-top: 10px;
}
.user-portrait .title,
.user-thumbnail .title {
    font-size: 0.825em;
    text-align: center;
}
.user-portrait.large,
.user-thumbnail.large {
    width: 200px;
}
.user-portrait.large .image,
.user-thumbnail.large .image {
    width: 200px;
    padding-top: 200px;
}
.user-portrait.small,
.user-thumbnail.small {
    width: 120px;
}
.user-portrait.small .image,
.user-thumbnail.small .image {
    width: 120px;
    padding-top: 120px;
}
.user-portrait.xsmall,
.user-thumbnail.xsmall {
    width: 90px;
}
.user-portrait.xsmall .image,
.user-thumbnail.xsmall .image {
    width: 90px;
    padding-top: 90px;
}
.portraits-grid,
.thumbnails-grid {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.portraits-grid .user-portrait,
.portraits-grid .user-thumbnail,
.thumbnails-grid .user-portrait,
.thumbnails-grid .user-thumbnail {
    margin: 10px 20px;
}
@media screen and (max-width: 660px) {
    .portraits-grid,
    .thumbnails-grid {
        display: block;
    }
    .portraits-grid .user-portrait,
    .portraits-grid .user-thumbnail,
    .thumbnails-grid .user-portrait,
    .thumbnails-grid .user-thumbnail {
        margin: 10px auto;
    }
}
.list-default {
    padding-left: 0;
}
.list-default li {
    list-style: circle inside;
}
.is-style-list-checkmarks,
.is-style-list-checkmarks-elegant,
.list-checkmarks,
.list-checkmarks-elegant {
    padding-left: 0;
}
.is-style-list-checkmarks-elegant li,
.is-style-list-checkmarks li,
.list-checkmarks-elegant li,
.list-checkmarks li {
    font-weight: 300;
    line-height: 1.35em;
    margin-bottom: 1em;
    padding-left: 40px;
    list-style: none;
    position: relative;
}
.is-style-list-checkmarks-elegant li strong,
.is-style-list-checkmarks li strong,
.list-checkmarks-elegant li strong,
.list-checkmarks li strong {
    color: #1c2c3e;
}
.is-style-list-checkmarks-elegant li:before,
.is-style-list-checkmarks li:before,
.list-checkmarks-elegant li:before,
.list-checkmarks li:before {
    display: block;
    font-family: glyphs;
    content: "\E91D";
    width: 30px;
    height: 30px;
    line-height: 30px;
    position: absolute;
    top: 0;
    left: 0;
    text-align: center;
    font-size: 14px;
    color: #35905d;
}
.is-style-list-checkmarks-elegant li:last-child,
.is-style-list-checkmarks li:last-child,
.list-checkmarks-elegant li:last-child,
.list-checkmarks li:last-child {
    margin-bottom: 0;
}
.is-style-list-checkmarks-elegant li,
.list-checkmarks-elegant li {
    font-size: 20px;
    line-height: 22px;
    font-weight: 200;
}
.is-style-list-checkmarks-elegant li:before,
.list-checkmarks-elegant li:before {
    content: "\E91D";
    width: 22px;
    height: 22px;
    line-height: 22px;
    border-radius: 100%;
    font-size: 10px;
    color: #4d92ff;
    border: 1px solid #4d92ff;
}
p + .is-style-list-checkmarks-elegant,
p + .list-checkmarks-elegant {
    margin-top: 38px;
}
.is-style-list-dropcaps,
.list-dropcaps {
    padding-left: 0;
    counter-reset: list-dropcap-counter;
}
.is-style-list-dropcaps li,
.list-dropcaps li {
    counter-increment: list-dropcap-counter;
    list-style: none;
    padding-left: 40px;
    margin-bottom: 1.5em;
    position: relative;
    font-weight: 200;
}
.is-style-list-dropcaps li strong,
.list-dropcaps li strong {
    color: #1c2c3e;
    font-weight: 600;
    font-size: 18px;
}
.is-style-list-dropcaps li:before,
.list-dropcaps li:before {
    display: block;
    font-family: museo-slab, Georgia, serif;
    content: counter(list-dropcap-counter);
    width: 50px;
    height: 50px;
    line-height: 50px;
    position: absolute;
    top: -18px;
    left: 0;
    font-size: 45px;
    font-weight: 800;
    color: #4d92ff;
}
.is-style-list-dropcaps li:last-child,
.list-dropcaps li:last-child {
    margin-bottom: 0;
}
.login-process .password-suggestions {
    display: none;
    visibility: hidden;
}

.login-process .password-suggestions,
.ui-popup {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    -webkit-box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    display: block;
    padding: 30px;
    font-size: 0.875rem;
    text-indent: 0;
    position: absolute;
    z-index: 500;
    white-space: nowrap;
}
.login-process .password-suggestions:after,
.ui-popup:after {
    display: block;
    font-family: glyphs;
    color: #fff;
    position: absolute;
    z-index: 510;
    width: 16px;
    height: 16px;
    font-size: 17px;
    line-height: 16px;
    text-align: center;
}
.login-process .top.password-suggestions:after,
.ui-popup.top:after {
    text-shadow: 0 1px 0 #d6e2f7, -1px 0 0 #d6e2f7, 2px 0 0 #d6e2f7, 3px 3px 3px rgba(31, 101, 211, 0.15);
    bottom: -13px;
    left: 16px;
    content: "\E95B";
}
.login-process .bottom.password-suggestions:after,
.ui-popup.bottom:after {
    text-shadow: 1px 0 0 #d6e2f7, -1px 0 0 #d6e2f7;
    top: -11px;
    left: 16px;
    content: "\E95E";
}
.login-process .left.password-suggestions:after,
.ui-popup.left:after {
    text-shadow: 0 1px 0 #d6e2f7, -1px 0 0 #d6e2f7;
    top: 50%;
    left: -13px;
    content: "\E95C";
    margin-top: -8px;
}
.login-process .right.password-suggestions:after,
.ui-popup.right:after {
    text-shadow: 2px 0 0 #d6e2f7, 3px 3px 3px rgba(31, 101, 211, 0.15);
    top: 50%;
    right: -10px;
    content: "\E95D";
    margin-top: -8px;
}
.login-process .password-suggestions:after,
.ui-popup.top-left:after {
    text-shadow: 0 1px 0 #d6e2f7, -1px 0 0 #d6e2f7;
    content: "\E95C";
    left: -15px;
    top: 16px;
}
.login-process .top-right.password-suggestions:after,
.ui-popup.top-right:after {
    text-shadow: 2px 0 0 #d6e2f7, 3px 3px 3px rgba(31, 101, 211, 0.15);
    right: -10px;
    content: "\E95D";
    top: 16px;
}
.login-process .bottom-right.password-suggestions:after,
.ui-popup.bottom-right:after {
    text-shadow: 1px 0 0 #d6e2f7, -1px 0 0 #d6e2f7;
    content: "\E95E";
    top: -11px;
    right: 16px;
}
.note-popup {
    background: #fff;
    border: 1px solid #d6e2f7;
    border-radius: 3px;
    -webkit-box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    background-color: #f2f6ff;
    display: inline-block;
    padding: 1rem;
    width: auto;
    height: auto;
    position: relative;
    text-align: left;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}
.note-popup[class*="pointer-"]:after,
.note-popup[class*="pointer-"]:before {
    display: block;
    font-family: glyphs;
    width: 16px;
    font-size: 26px;
    line-height: 32px;
    text-align: left;
    position: absolute;
}
.note-popup[class*="pointer-"]:before {
    color: #f2f6ff;
    text-align: left;
    z-index: -1;
}
.note-popup[class*="pointer-"]:after {
    color: #c1cfe6;
    z-index: -2;
}
.note-popup.pointer-white[class*="pointer-"]:before {
    color: #fff;
}
.note-popup.pointer-left:after,
.note-popup.pointer-left:before,
.note-popup.pointer-right:after,
.note-popup.pointer-right:before {
    top: 12px;
}
.note-popup.pointer-left.center:after,
.note-popup.pointer-left.center:before,
.note-popup.pointer-right.center:after,
.note-popup.pointer-right.center:before {
    -o-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    top: 50%;
}
.note-popup.pointer-down:after,
.note-popup.pointer-down:before,
.note-popup.pointer-up:after,
.note-popup.pointer-up:before {
    left: 12px;
}
.note-popup.pointer-down.center:after,
.note-popup.pointer-down.center:before,
.note-popup.pointer-up.center:after,
.note-popup.pointer-up.center:before {
    -o-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    left: 50%;
    margin-left: -8px;
}
.note-popup.pointer-left:after,
.note-popup.pointer-left:before {
    content: glyph(pointer-left);
}
.note-popup.pointer-left:before {
    left: -12px;
}
.note-popup.pointer-left:after {
    left: -14px;
}
.note-popup.pointer-right:after,
.note-popup.pointer-right:before {
    content: glyph(pointer-right);
}
.note-popup.pointer-right:before {
    right: -5px;
}
.note-popup.pointer-right:after {
    right: -7px;
}
.note-popup.pointer-up:after,
.note-popup.pointer-up:before {
    content: "\E95E";
}
.note-popup.pointer-up:before {
    top: -18px;
}
.note-popup.pointer-up:after {
    top: -20px;
}
.note-popup.pointer-down:after,
.note-popup.pointer-down:before {
    content: "\E95B";
}
.note-popup.pointer-down:before {
    bottom: -16px;
}
.note-popup.pointer-down:after {
    bottom: -18px;
}
.note-popup p {
    color: #1c2c3e;
    font: 600 10px/13px;
    letter-spacing: 0;
    margin-bottom: 0;
}
.note-popup:after,
.note-popup:before {
    content: "";
    position: absolute;
    display: block;
    -webkit-clip-path: polygon(0 0, 0 100%, 100% 50%);
    clip-path: polygon(0 0, 0 100%, 100% 50%);
}
.note-popup:before {
    top: 1px;
    bottom: 1px;
    width: 28px;
    right: -28px;
    z-index: 10;
    background-color: #f2f6ff;
}
.note-popup:after {
    top: 0;
    bottom: 0;
    right: -30px;
    width: 30px;
    background-color: #d6e2f7;
    -webkit-box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
    box-shadow: 3px 3px 6px 0 rgba(141, 184, 250, 0.375);
}
.note-popup .popup-image {
    position: relative;
    width: 42px;
}
.note-popup .popup-image .image {
    width: 100%;
    height: auto;
}
.values-slider {
    position: relative;
    padding-top: 1px;
    margin: 50px 0;
}
.values-slider.ui-widget.ui-widget-content {
    border: none;
    background-color: #e5eeff;
    height: 7px;
    border-radius: 3px;
}
.values-slider.ui-slider .ui-slider-range {
    background-color: #3b81f0;
    position: absolute;
    top: 0;
    bottom: 0;
    border-radius: 10px 0 0 10px;
}
.values-slider.ui-slider-horizontal .ui-slider-handle {
    background: #fff 0 0 no-repeat padding-box;
    -webkit-box-shadow: 0 3px 6px rgba(63, 114, 193, 0.11);
    box-shadow: 0 3px 6px rgba(63, 114, 193, 0.11);
    border: 1px solid #c1cfe6;
    margin-top: -9px;
    margin-left: -11px;
    position: relative;
    border-radius: 16px;
    width: 24px;
    height: 24px;
    outline: none;
    z-index: 10;
    cursor: pointer;
}
.values-slider.ui-slider-horizontal .ui-slider-handle:after,
.values-slider.ui-slider-horizontal .ui-slider-handle:before {
    content: "\E91B";
    font-family: glyphs;
    color: #c1cfe6;
    font-size: 8px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.values-slider.ui-slider-horizontal .ui-slider-handle:before {
    content: "\E91A";
    padding-right: 8px;
}
.values-slider.ui-slider-horizontal .ui-slider-handle:after {
    content: "\E91B";
    padding-left: 8px;
}
.values-slider .lines {
    width: 100%;
    height: 12px;
    margin-top: 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}
.values-slider .lines span {
    display: block;
    width: 2px;
    height: 100%;
    background-color: #ccdbf5;
}
.slider-handle-text {
    position: absolute;
    top: -55px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transform: translateX(-50%);
    font: 300 37px/47px Soleil;
    letter-spacing: -0.74px;
    color: #082446;
    opacity: 1;
}
.air-top {
    padding-top: 1rem;
}
.air-bottom {
    padding-bottom: 1rem;
}
.air-top-xlarge {
    padding-top: 3rem !important;
}
.air-bottom-xlarge {
    padding-bottom: 3rem !important;
}
.air-top-large {
    padding-top: 2rem !important;
}
.air-bottom-large {
    padding-bottom: 2rem !important;
}
.air-top-medium {
    padding-top: 1.5rem !important;
}
.air-bottom-medium {
    padding-bottom: 1.5rem !important;
}
.air-top-small {
    padding-top: 0.5rem !important;
}
.air-bottom-small {
    padding-bottom: 0.5rem !important;
}
.margin-top {
    margin-top: 38px !important;
}
.margin-bottom {
    margin-bottom: 38px !important;
}
.margin-left {
    margin-left: 38px !important;
}
.margin-right {
    margin-right: 38px !important;
}
.margin-top-xsmall {
    margin-top: 15px !important;
}
.margin-bottom-xsmall {
    margin-bottom: 15px !important;
}
.margin-top-small {
    margin-top: 24px !important;
}
.margin-bottom-small {
    margin-bottom: 24px !important;
}
.margin-top-medium {
    margin-top: 50px !important;
}
.margin-bottom-medium {
    margin-bottom: 50px !important;
}
.margin-top-large {
    margin-top: 80px !important;
}
.margin-bottom-large {
    margin-bottom: 80px !important;
}
.margin-top-xlarge {
    margin-top: 150px !important;
}
.margin-bottom-xlarge {
    margin-bottom: 150px !important;
}
.frame {
    padding: 38px;
}
.frame-left {
    padding-left: 38px;
}
.frame-right {
    padding-right: 38px;
}
.frame-top {
    padding-top: 38px;
}
.frame-bottom {
    padding-bottom: 38px;
}
.frame-small {
    padding: 24px;
}
.frame-left-small {
    padding-left: 24px;
}
.frame-right-small {
    padding-right: 24px;
}
.frame-top-small {
    padding-top: 24px;
}
.frame-bottom-small {
    padding-bottom: 24px;
}
.frame-medium {
    padding: 50px;
}
.frame-left-medium {
    padding-left: 50px;
}
.frame-right-medium {
    padding-right: 50px;
}
.frame-top-medium {
    padding-top: 50px;
}
.frame-bottom-medium {
    padding-bottom: 50px;
}
.frame-large {
    padding: 80px;
}
.frame-left-large {
    padding-left: 80px;
}
.frame-right-large {
    padding-right: 80px;
}
.frame-top-large {
    padding-top: 80px;
}
.frame-bottom-large {
    padding-bottom: 80px;
}
@media screen and (max-width: 660px) {
    .frame-left.not-on-mobile {
        padding-left: 0;
    }
    .frame-right.not-on-mobile {
        padding-right: 0;
    }
    .frame-top.not-on-mobile {
        padding-top: 0;
    }
    .frame-bottom.not-on-mobile {
        padding-bottom: 0;
    }
    .frame-small.not-on-mobile {
        padding: 0;
    }
    .frame-left-small.not-on-mobile {
        padding-left: 0;
    }
    .frame-right-small.not-on-mobile {
        padding-right: 0;
    }
    .frame-top-small.not-on-mobile {
        padding-top: 0;
    }
    .frame-bottom-small.not-on-mobile {
        padding-bottom: 0;
    }
    .frame-medium.not-on-mobile {
        padding: 0;
    }
    .frame-left-medium.not-on-mobile {
        padding-left: 0;
    }
    .frame-right-medium.not-on-mobile {
        padding-right: 0;
    }
    .frame-top-medium.not-on-mobile {
        padding-top: 0;
    }
    .frame-bottom-medium.not-on-mobile {
        padding-bottom: 0;
    }
    .frame-large.not-on-mobile {
        padding: 0;
    }
    .frame-left-large.not-on-mobile {
        padding-left: 0;
    }
    .frame-right-large.not-on-mobile {
        padding-right: 0;
    }
    .frame-top-large.not-on-mobile {
        padding-top: 0;
    }
    .frame-bottom-large.not-on-mobile {
        padding-bottom: 0;
    }
}
@media screen and (min-width: 800px) {
    .shift-up-small {
        margin-top: -50px !important;
    }
    .shift-up-medium {
        margin-top: -80px !important;
    }
    .shift-up-large {
        margin-top: -150px !important;
    }
    .shift-down-small {
        margin-bottom: -50px !important;
    }
    .shift-down-medium {
        margin-bottom: -80px !important;
    }
    .shift-down-large {
        margin-bottom: -150px !important;
    }
}
.hide {
    display: none !important;
}
.outline-dashed-light {
    border: 2px dashed #d6e2f7;
    border-radius: 3px;
    display: inline-block;
    padding: 25px;
}
@media screen and (max-width: 660px) {
    .hide-on-mobile {
        display: none !important;
    }
}
@media screen and (max-width: 960px) {
    .hide-on-smallscreen {
        display: none !important;
    }
}
.show-on-mobile {
    display: none !important;
}
@media screen and (max-width: 660px) {
    .show-on-mobile {
        display: block !important;
    }
}
.bg-white {
    background-color: #fff;
}
.bg-default,
.bg-light,
.bg-main {
    background-color: #f2f6ff;
}
.bg-medium {
    background-color: #e5eeff;
}
.bg-primary {
    background-color: #4d92ff;
}
.bg-dark {
    background-color: #1c2c3e;
}
.align-left {
    text-align: left !important;
}
.align-right {
    text-align: right !important;
}
.align-center {
    text-align: center !important;
}
.float-right {
    float: right;
}
.float-left {
    float: left;
}
hr {
    border: none;
    border-top: 1px solid #d6e2f7;
}
hr[class*="primary-rule"] {
    border-top: 2px solid #4d92ff;
    margin: 24px 0 !important;
}
hr[class*="primary-rule-no-margins"] {
    border-top: 2px solid #4d92ff;
    margin: 0 !important;
}
.hrule {
    position: relative;
}
.hrule:before {
    display: block;
    content: "";
    width: 100%;
    height: 1px;
    background-color: #d6e2f7;
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -0.5px;
}
figure figcaption {
    font-size: 14px;
    line-height: 1.3em;
    font-weight: 400;
    color: #7c8b9f;
    margin-top: 24px;
}
figure.square {
    display: block;
    width: 100%;
    height: 0;
    margin: 0;
    padding-top: 100%;
    position: relative;
    overflow: hidden;
}
figure.square img {
    -o-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: auto;
    height: 100%;
    position: absolute;
    top: 0;
    left: 50%;
}
figure.square.medium {
    width: 75%;
    padding-top: 75%;
}
figure.square.small {
    width: 50%;
    padding-top: 50%;
}
figure.square.xsmall {
    width: 30%;
    padding-top: 30%;
}
@media screen and (max-width: 660px) {
    figure.square.responsive {
        width: 220px;
        padding-top: 220px;
        margin-left: auto;
        margin-right: auto;
    }
}
.main-page-width {
    padding-left: 40px;
    padding-right: 40px;
}
@media screen and (min-width: 1200px) {
    .main-page-width {
        padding-left: calc((100% - 1080px) / 2);
        padding-right: calc((100% - 1080px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .main-page-width {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.grid {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: left;
    -ms-flex-pack: left;
    justify-content: left;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: -5px;
    margin-left: -5px;
}
.grid .grid-item {
    margin: 5px;
    position: relative;
}
.grid.thirds .grid-item {
    -ms-flex-preferred-size: calc(33.33333% - 10px);
    flex-basis: calc(33.33333% - 10px);
    -ms-flex-line-pack: center;
    align-content: center;
}
.grid.squares .grid-item {
    height: 0;
}
.grid.squares.default .grid-item {
    -ms-flex-preferred-size: calc(25% - 10px);
    flex-basis: calc(25% - 10px);
    padding-top: calc(25% - 10px);
}
.grid.squares.default .grid-item.double {
    -ms-flex-preferred-size: calc(50% - 10px);
    flex-basis: calc(50% - 10px);
}
.grid.squares.default .grid-item.triple {
    -ms-flex-preferred-size: calc(66.66667% - 10px);
    flex-basis: calc(66.66667% - 10px);
}
@media screen and (max-width: 960px) {
    .grid.squares.default .grid-item,
    .grid.squares.default .grid-item.triple,
    .grid.thirds .grid-item {
        -ms-flex-preferred-size: calc(50% - 10px);
        flex-basis: calc(50% - 10px);
    }
}
@media screen and (max-width: 660px) {
    .grid.thirds {
        margin-left: 0;
    }
    .grid.thirds .grid-item {
        -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    }
    .grid.squares.default {
        margin-left: 0;
    }
    .grid.squares.default .grid-item {
        padding-top: 50%;
        margin-left: 0;
        margin-right: 0;
    }
    .grid.squares.default .grid-item,
    .grid.squares.default .grid-item.double,
    .grid.squares.default .grid-item.triple {
        -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    }
}
@media screen and (max-width: 375px) {
    .grid.squares.default .grid-item {
        padding-top: 75%;
    }
}
.flex-align-items-center,
.vertical-align-items {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.flex-columns {
    clear: both;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
}
.flex-columns:after {
    display: block;
    visibility: hidden;
    clear: both;
    height: 0;
    content: " ";
    font-size: 0;
}
.flex-columns .column:not(:first-child) {
    margin-left: 32px;
}
.flex-columns.two {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.flex-columns.two .column {
    -ms-flex-preferred-size: calc(50% - 16px);
    flex-basis: calc(50% - 16px);
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
}
.flex-columns.three {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.flex-columns.three .column {
    -ms-flex-preferred-size: 28%;
    flex-basis: 28%;
    margin: 20px !important;
}
.flex-columns.four {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.flex-columns.four .column {
    -ms-flex-preferred-size: 23%;
    flex-basis: 23%;
    margin: 20px !important;
}
@media screen and (max-width: 860px) {
    .flex-columns {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
    .flex-columns .column {
        -ms-flex-preferred-size: 50%;
        flex-basis: 50%;
        -webkit-box-flex: 0;
        -ms-flex-positive: 0;
        flex-grow: 0;
        margin-left: 0 !important;
    }
    .flex-columns.three .column {
        -ms-flex-preferred-size: 42%;
        flex-basis: 42%;
    }
}
@media screen and (max-width: 660px) {
    .flex-columns {
        display: block;
    }
    .flex-columns .column {
        -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
        padding-left: 0;
        padding-right: 0;
        margin-left: 0;
        margin-bottom: 32px;
    }
    .flex-columns .column:last-child {
        margin-bottom: 0;
    }
    .flex-columns.three {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }
    .flex-columns.three .column {
        width: 100%;
        margin: 0 auto 20px !important;
    }
}
button,
input[type="submit"] {
    font-family: soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
}
form.ui-form {
    clear: both;
}
form.ui-form:after {
    display: block;
    visibility: hidden;
    clear: both;
    height: 0;
    content: " ";
    font-size: 0;
}
form.ui-form .inline {
    display: inline-block !important;
}
form.ui-form .required:before {
    display: inline-block;
    width: auto;
    height: inherit;
    font-size: 1.5em;
    line-height: inherit;
    vertical-align: middle;
    content: "*";
    color: #4d92ff;
    margin-right: 0.5rem;
}
.ui-form input:not([type="submit"]),
.ui-form textarea {
    outline: none;
    padding: 0 1em;
    height: 45px;
    border: 1px solid #c1cfe6;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-size: 18px;
    font-family: soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    vertical-align: middle;
    color: #1c2c3e;
    background: #f2f6ff;
    border-radius: 3px;
    width: 100%;
    font-weight: 500;
    font-size: 1rem;
    -webkit-appearance: none;
}
.ui-form input:not([type="submit"]):hover,
.ui-form textarea:hover {
    background: #fcfdff;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.ui-form input:not([type="submit"]):focus,
.ui-form textarea:focus {
    background-color: #fff;
    -webkit-box-shadow: 0 0 5px 2px rgba(141, 184, 250, 0.5);
    box-shadow: 0 0 5px 2px rgba(141, 184, 250, 0.5);
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.ui-form input:not([type="submit"])::-webkit-input-placeholder,
.ui-form textarea::-webkit-input-placeholder {
    color: #ccdbf5;
    text-transform: none;
    font-style: italic;
    font-weight: 500;
}
.ui-form input:not([type="submit"]):-moz-placeholder,
.ui-form input:not([type="submit"])::-moz-placeholder,
.ui-form textarea:-moz-placeholder,
.ui-form textarea::-moz-placeholder {
    color: #ccdbf5;
    text-transform: none;
    font-style: italic;
    font-weight: 500;
}
.ui-form input:not([type="submit"]):-ms-input-placeholder,
.ui-form textarea:-ms-input-placeholder {
    color: #ccdbf5;
    text-transform: none;
    font-style: italic;
    font-weight: 500;
}
.ui-form input:not([type="submit"]):-webkit-autofill,
.ui-form textarea:-webkit-autofill {
    -webkit-box-shadow: inset 0 0 0 1000px #fff;
}
.ui-form input:not([type="submit"]).inline,
.ui-form textarea.inline {
    display: inline;
    width: auto;
}
.ui-form input:not([type="submit"]).form-control.error,
.ui-form textarea.form-control.error {
    border-color: #d64956;
}
.ui-form input:not([type="submit"]) + input {
    margin-top: 1rem;
}
.ui-form input[type="search"] {
    -webkit-appearance: none;
}
.ui-form input[type="search"]:focus,
.ui-form input[type="search"]:hover {
    background: #fff;
    -webkit-transition: background 0.15s ease-out;
    -o-transition: background 0.15s ease-out;
    transition: background 0.15s ease-out;
}
.ui-form textarea {
    display: block;
    width: 100%;
    height: auto;
    min-height: 10rem;
    padding: 0.75rem;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    resize: none;
}
.ui-form textarea.large {
    min-height: 20rem;
}
.ui-form label {
    display: block;
    margin-bottom: 0.35em;
    font-weight: 500;
    color: #637d9b;
    text-align: left;
}
.ui-form label[for*="checkbox"],
.ui-form label[for*="radio"] {
    display: inline-block;
}
input + .ui-form label {
    margin-top: 1rem;
}
.ui-form label.inline {
    display: inline-block;
    width: auto;
}
.ui-form fieldset {
    position: relative;
}
.ui-form fieldset legend {
    color: #4d92ff;
    font-weight: 400;
    font-size: 1.25rem;
}
.ui-form fieldset h2,
.ui-form fieldset h3,
.ui-form fieldset h4,
.ui-form fieldset legend {
    margin-bottom: 0.75em;
}
.ui-form fieldset input:not([type="checkbox"]):not([type="radio"]):not([type="submit"]) {
    width: 100%;
}
.ui-form fieldset input:not([type="checkbox"]):not([type="radio"]):not([type="submit"]).inline {
    width: auto;
}
.ui-form fieldset select {
    width: 100%;
}
.ui-form fieldset .fieldgroup + .ui-form fieldset .fieldgroup {
    margin-bottom: 1rem;
}
.ui-form fieldset.section {
    margin-bottom: 2.5rem;
}
.ui-form fieldset.section.no-label {
    margin-bottom: 0;
}
.ui-form fieldset.note {
    padding: 1.5rem 0;
}
.ui-form fieldset.note *,
.ui-form fieldset.note .type-note {
    font-size: 0.925rem;
    line-height: 1.35em;
}
.ui-form .ui-form-element + .ui-form-element,
.ui-form fieldset + fieldset {
    margin-top: 1.125em;
}
.ui-form .form-section + .form-section {
    margin-top: 50px;
}
.ui-form input[type="submit"] {
    border: none;
}
.ui-form input[type="hidden"] + label {
    margin-top: 0;
}
.ui-form .ui-form-group {
    display: block;
    margin-top: 1.5rem;
}
.ui-form .input-group {
    position: relative;
}
.ui-form .input-group .input-group-icon {
    display: inline-block;
    width: 35px;
    height: 35px;
    line-height: 35px;
    text-align: center;
    position: absolute;
    left: 5px;
    top: 50%;
    margin-top: -17.5px;
}
.ui-form .input-group .input-group-icon:before {
    display: block;
    content: "";
    font-family: glyphs;
    width: 100%;
    height: 100%;
    color: #4d92ff;
    content: "\E9F9";
    position: absolute;
    top: 0;
    left: 0;
}
.ui-form .input-group .input-group-icon.view {
    left: auto;
    right: 35px;
}
.ui-form .input-group .input-group-icon.view:before {
    content: "\e97b";
    color: #949CBC;
    cursor: pointer;
}
.ui-form .input-group .input-group-icon.view:hover:before,
.ui-form .input-group .input-group-icon.view.active:before {
    color: #4d92ff;
}
.ui-form .input-group .input-group-icon.phone:before {
    content: "\E9E0";
}
.ui-form .input-group .input-group-icon.phone-code:before {
    content: "\E986";
}
.ui-form .input-group .form-validation {
    -o-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    right: -45px;
}
.ui-form .input-group .input-validation-indicator {
    width: 35px;
    height: 35px;
    background-image: url(data:image/png;base64,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);
    background-size: contain;
    background-repeat: no-repeat;
}
.ui-form .input-group .input-validation-indicator.blank {
    background-image: none;
}
.ui-form .input-group .input-validation-indicator.question-mark {
    background-image: url(data:image/png;base64,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);
}
.ui-form .input-group .input-validation-indicator.error {
    background-image: url(images/password-requirements-error.png);
}
.ui-form .input-group .input-validation-indicator.error-full {
    background-image: url(data:image/png;base64,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);
}
.ui-form .input-group .input-validation-indicator.warning {
    background-image: url(images/password-requirements-warning.png);
}
.ui-form .input-group .input-validation-indicator.success {
    background-image: url(data:image/png;base64,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);
}
.ui-form .input-group .input-validation-indicator.password {
    cursor: pointer;
}
.ui-form .input-group + .input-group {
    margin-top: 24px;
}
.ui-form .input-group.has-icon input:not([type="submit"]) {
    padding-left: 45px;
}
.ui-form .input-group.password-parent {
    margin-bottom: 1.25em;
}
.ui-form .password-validation-requirements {
    display: none;
}
.ui-form .password-validation-requirements.is-visible {
    display: block;
}
.ui-checkbox {
    width: auto;
    /*height: 22px;*/
    position: relative;
    cursor: pointer;
}
.ui-checkbox input[type="checkbox"],
.ui-checkbox input[type="radio"] {
    display: none !important;
}
.ui-checkbox input[type="checkbox"] + label,
.ui-checkbox input[type="radio"] + label {
    display: inline-block;
    /*height: 22px;*/
    padding: 0 0 0 33px;
    margin: 0;
    white-space: normal;
    cursor: pointer;
    vertical-align: top;
    line-height: 22px;
    text-align: left;
    font-size: 1rem;
    font-weight: 400;
    color: #637d9b;
}
.ui-checkbox input[type="checkbox"] + label:before,
.ui-checkbox input[type="radio"] + label:before {
    -o-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    display: block;
    font-family: glyphs;
    content: "\E91D";
    width: 22px;
    height: 22px;
    font-size: 12px;
    line-height: 22px;
    background: transparent;
    color: #fff;
    text-align: center;
    position: absolute;
    left: 0;
    top: 50%;
    z-index: 5;
}
.ui-checkbox input[type="checkbox"] + label:after,
.ui-checkbox input[type="radio"] + label:after {
    -o-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    display: block;
    content: "";
    width: 22px;
    height: 22px;
    text-align: left;
    position: absolute;
    left: 0;
    top: 50%;
    -webkit-box-shadow: inset 0 0 0 1px #7996b4;
    box-shadow: inset 0 0 0 1px #7996b4;
    background: #fff;
    border-radius: 2px;
}
.ui-checkbox input[type="checkbox"] + label:not(.disabled):hover,
.ui-checkbox input[type="radio"] + label:not(.disabled):hover {
    color: #7996b4;
    -webkit-transition: color 0.25s ease-out;
    -o-transition: color 0.25s ease-out;
    transition: color 0.25s ease-out;
}
.ui-checkbox input[type="checkbox"] + label:not(.disabled):hover:after,
.ui-checkbox input[type="radio"] + label:not(.disabled):hover:after {
    -webkit-box-shadow: inset 0 0 0 2px #7996b4;
    box-shadow: inset 0 0 0 2px #7996b4;
    -webkit-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.ui-checkbox input[type="checkbox"]:checked + label:before,
.ui-checkbox input[type="radio"]:checked + label:before {
    color: #fff;
}
.ui-checkbox input[type="checkbox"]:checked + label:after,
.ui-checkbox input[type="radio"]:checked + label:after {
    -webkit-box-shadow: none;
    box-shadow: none;
    background: #4d92ff;
}
.ui-checkbox input[type="checkbox"]:checked + label:not(.disabled):hover:after,
.ui-checkbox input[type="radio"]:checked + label:not(.disabled):hover:after {
    -webkit-box-shadow: inset 0 0 0 2px #4d92ff;
    box-shadow: inset 0 0 0 2px #4d92ff;
}
.ui-checkbox input[type="checkbox"]:disabled + label,
.ui-checkbox input[type="radio"]:disabled + label {
    color: color(ui-primary);
    cursor: not-allowed;
}
.ui-checkbox input[type="checkbox"]:disabled + label:before,
.ui-checkbox input[type="radio"]:disabled + label:before {
    display: none;
}
.ui-checkbox input[type="checkbox"]:disabled + label:after,
.ui-checkbox input[type="radio"]:disabled + label:after {
    opacity: 0.525;
}
.ui-checkbox input[type="checkbox"]:checked:disabled + label,
.ui-checkbox input[type="radio"]:checked:disabled + label {
    color: #637d9b;
}
.ui-checkbox input[type="checkbox"]:checked:disabled + label:before,
.ui-checkbox input[type="radio"]:checked:disabled + label:before {
    display: block;
    color: #fff;
}
.ui-checkbox input[type="checkbox"]:checked:disabled + label:after,
.ui-checkbox input[type="radio"]:checked:disabled + label:after {
    opacity: 0.525;
}
.ui-checkbox .radio:after,
.ui-checkbox .radio:before,
.ui-checkbox input[type="radio"] + label:after,
.ui-checkbox input[type="radio"] + label:before {
    border-radius: 90px;
}
.ui-checkbox .radio:before,
.ui-checkbox input[type="radio"] + label:before {
    width: 7px;
    height: 7px;
    content: "";
    left: 8px;
    background: #fff !important;
}
.ui-checkbox.no-label .checkmark,
.ui-checkbox.no-label input[type="checkbox"] + label {
    text-indent: -100000px;
}
.ui-checkbox.small input[type="checkbox"] + label,
.ui-checkbox.small input[type="radio"] + label {
    width: 16px;
    height: 16px;
    line-height: 16px;
    padding-left: 26px;
    font-size: 1.15rem;
}
.ui-checkbox.small input[type="checkbox"] + label:before,
.ui-checkbox.small input[type="radio"] + label:before {
    width: 16px;
    height: 16px;
    font-size: 12px;
    line-height: 16px;
    left: 0;
    margin-top: 1px;
    top: 50%;
}
.ui-checkbox.small input[type="checkbox"] + label:after,
.ui-checkbox.small input[type="radio"] + label:after {
    width: 16px;
    height: 16px;
}
.ui-checkbox.medium .checkmark,
.ui-checkbox.medium input[type="checkbox"] + label,
.ui-checkbox.medium input[type="radio"] + label {
    width: 26px;
    height: 26px;
    line-height: 26px;
    padding-left: 36px;
    font-size: 1.35rem;
}
.ui-checkbox.medium .checkmark:before,
.ui-checkbox.medium input[type="checkbox"] + label:before,
.ui-checkbox.medium input[type="radio"] + label:before {
    width: 26px;
    height: 26px;
    font-size: 3rem;
    line-height: 26px;
    left: -6px;
    margin-top: -4px;
    top: 50%;
}
.ui-checkbox.medium .checkmark:after,
.ui-checkbox.medium input[type="checkbox"] + label:after,
.ui-checkbox.medium input[type="radio"] + label:after {
    width: 26px;
    height: 26px;
    margin-top: -0.25rem;
}
@media only screen and (min-device-width: ) and (max-device-width: ) and (-webkit-min-device-pixel-ratio: ) {
    .ui-checkbox .checkmark,
    .ui-checkbox input[type="checkbox"] + label,
    .ui-checkbox input[type="radio"] + label {
        width: auto;
        height: 16px;
        line-height: 16px;
        padding-left: 24px;
        font-size: 1.5rem;
    }
    .ui-checkbox .checkmark:before,
    .ui-checkbox input[type="checkbox"] + label:before,
    .ui-checkbox input[type="radio"] + label:before {
        width: 16px;
        height: 16px;
        font-size: 2.25rem;
        line-height: 16px;
        left: -4px;
        text-indent: -100px;
        margin-top: -8px;
    }
    .ui-checkbox .checkmark:after,
    .ui-checkbox input[type="checkbox"] + label:after,
    .ui-checkbox input[type="radio"] + label:after {
        width: 16px;
        height: 16px;
        border-radius: 100%;
        position: absolute;
        left: 0;
        margin-top: -0.5rem;
    }
    .ui-checkbox.medium .checkmark,
    .ui-checkbox.medium input[type="checkbox"] + label,
    .ui-checkbox.medium input[type="radio"] + label {
        width: auto;
        height: 16px;
        line-height: 16px;
        padding-left: 24px;
        font-size: 1.15rem;
    }
    .ui-checkbox.medium .checkmark:before,
    .ui-checkbox.medium input[type="checkbox"] + label:before,
    .ui-checkbox.medium input[type="radio"] + label:before {
        width: 16px;
        height: 16px;
        font-size: 2.25rem;
        line-height: 16px;
        left: -4px;
        margin-top: -9px;
    }
    .ui-checkbox.medium .checkmark:after,
    .ui-checkbox.medium input[type="checkbox"] + label:after,
    .ui-checkbox.medium input[type="radio"] + label:after {
        width: 16px;
        height: 16px;
        margin-top: -0.55rem;
    }
}
@media only screen and (min-device-width: 300px) and (max-device-width: 660px) {
    .ui-checkbox .checkmark,
    .ui-checkbox input[type="checkbox"] + label,
    .ui-checkbox input[type="radio"] + label {
        width: auto;
        height: 16px;
        line-height: 16px;
        padding-left: 24px;
        font-size: 1.5rem;
    }
    .ui-checkbox .checkmark:before,
    .ui-checkbox input[type="checkbox"] + label:before,
    .ui-checkbox input[type="radio"] + label:before {
        width: 16px;
        height: 16px;
        font-size: 2.25rem;
        line-height: 16px;
        left: -4px;
        text-indent: -100px;
        margin-top: -8px;
    }
    .ui-checkbox .checkmark:after,
    .ui-checkbox input[type="checkbox"] + label:after,
    .ui-checkbox input[type="radio"] + label:after {
        width: 16px;
        height: 16px;
        border-radius: 100%;
        position: absolute;
        left: 0;
        margin-top: -0.5rem;
    }
    .ui-checkbox.medium .checkmark,
    .ui-checkbox.medium input[type="checkbox"] + label,
    .ui-checkbox.medium input[type="radio"] + label {
        width: auto;
        height: 16px;
        line-height: 16px;
        padding-left: 24px;
        font-size: 1.15rem;
    }
    .ui-checkbox.medium .checkmark:before,
    .ui-checkbox.medium input[type="checkbox"] + label:before,
    .ui-checkbox.medium input[type="radio"] + label:before {
        width: 16px;
        height: 16px;
        font-size: 2.25rem;
        line-height: 16px;
        left: -4px;
        margin-top: -9px;
    }
    .ui-checkbox.medium .checkmark:after,
    .ui-checkbox.medium input[type="checkbox"] + label:after,
    .ui-checkbox.medium input[type="radio"] + label:after {
        width: 16px;
        height: 16px;
        margin-top: -0.55rem;
    }
}
@media screen and (max-width: 420px) {
    .ui-checkbox.medium .checkmark,
    .ui-checkbox.medium input[type="checkbox"] + label,
    .ui-checkbox.medium input[type="radio"] + label {
        font-size: 1rem;
    }
}
.template-login .page-section {
    padding: 38px;
}
.login-process .password-suggestions,
.login-process .password-validation-requirements {
    width: 280px;
    top: 24px;
    right: -340px;
    font-weight: 700;
    font-size: 16px;
    white-space: normal;
}
.login-process .password-suggestions ul,
.login-process .password-validation-requirements ul {
    font-size: 14px;
    font-weight: 400;
    margin-top: 1rem;
    white-space: normal;
}
.login-process .input-group:hover + .password-suggestions,
.login-process .input-group:hover + .password-validation-requirements {
    display: block;
}
.login-process .password-confirm:not(input),
.login-process .password-strength {
    display: none;
}
.login-process input[type="submit"] {
    width: 100%;
    margin-bottom: 0.5rem;
}
.login-process #edit-skip,
.login-process .link-button {
    font-size: 16px;
}
@media screen and (max-width: 1023px) {
    .login-process .password-validation-requirements.password-suggestions,
    .login-process .password-validation-requirements.ui-popup {
        width: 100%;
        position: static;
        margin-top: 24px;
    }
    .login-process .password-validation-requirements.password-suggestions:after,
    .login-process .password-validation-requirements.ui-popup:after {
        content: "\E95E";
        top: 84px;
        left: 50%;
        margin-left: -8px;
        text-shadow: 1px 0 0 #d6e2f7, -1px 0 0 #d6e2f7;
    }
}
.form-progress {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -ms-flex-line-pack: center;
    align-content: center;
}
.form-progress .form-progress-step {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    color: #C5CADE;
    height: 45px;
    margin-bottom: 50px;
    position: relative;
    background-color: #FFF;
    box-shadow: 0 0 0 10px #FFF;
}
.form-progress .form-progress-step .step-number {
    font-size: 32px;
    margin-right: 0.25em;
    font-weight: 600;
    margin-top: -3px;
}
.form-progress .form-progress-step .step-label {
    font-size: 18px;
}
.form-progress .form-progress-step:before {
    display: block;
    content: "";
    width: 100px;
    height: 1px;
    position: absolute;
    top: 50%;
    margin-top: -1px;
    left: -100px;
    background-color: #C5CADE;
    z-index: -1;
}
.form-progress .form-progress-step.completed {
    color: #35905d;
    padding-right: 24px;
}
.form-progress .form-progress-step.completed:after {
    display: block;
    content: "";
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 21px;
    font-family: glyphs;
    content: "\E91D";
    border-radius: 100%;
    font-size: 9px;
    color: #fff;
    background-color: #35905d;
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -10px;
}
.form-progress .form-progress-step.completed:before {
    background-color: #35905d;
}
.form-progress .form-progress-step.active {
    color: #4A4C55;
}
.form-progress .form-progress-step.active:before {
    background-color: #4A4C55;
}
.form-progress .form-progress-step:first-child:before {
    display: none;
}
@media screen and (max-width: 960px) {
    .form-progress .form-progress-step .step-number {
        font-size: 22px;
    }
    .form-progress .form-progress-step .step-label {
        font-size: 17px;
    }
}
@media screen and (max-width: 800px) {
    .form-progress {
        display: none;
    }
}
.kt-infographic-login {
    width: 100%;
    height: 280px;
    background: url(images/kt-infographic-login.png) no-repeat 50%;
    background-size: contain;
    color: #4d92ff;
}
@media screen and (max-width: 960px) {
    .kt-infographic-login {
        height: 200px;
    }
}
.type-elegant {
    font-size: 21px;
}
body {
    font-family: soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.35em;
    color: #385575;
    margin: 0;
    padding-top: 70px;
    overflow-x: hidden;
    overscroll-behavior-y: none;
}
body.template-case-study {
    padding-top: 50px;
}
@media only screen and (min-device-width: ) and (max-device-width: ) and (-webkit-min-device-pixel-ratio: ) {
    body {
        padding-top: 50px;
    }
}
.main-content {
    padding-left: 40px;
    padding-right: 40px;
    min-height: calc(100vh - 151px);
}
@media screen and (min-width: 1200px) {
    .main-content {
        padding-left: calc((100% - 1080px) / 2);
        padding-right: calc((100% - 1080px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .main-content {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.page-title {
    margin-bottom: 38px;
}
.page-subtitle {
    display: block;
    max-width: 1024px;
}
.page-header {
    position: relative;
}
.page-header.frontpage {
    padding-left: 40px;
    padding-right: 40px;
    position: relative;
}
@media screen and (min-width: 1200px) {
    .page-header.frontpage {
        padding-left: calc((100% - 1080px) / 2);
        padding-right: calc((100% - 1080px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .page-header.frontpage {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.page-header.frontpage .wrapper {
    display: block;
    width: 100%;
    position: relative;
}
.page-header.frontpage .main-headline-container {
    padding: 120px 0 80px;
}
.page-header.frontpage .main-headline-container h1 {
    font-size: 56px;
    line-height: 1.25em;
    font-weight: 700;
}
.page-header.frontpage .main-headline-container .description {
    display: block;
    max-width: 520px;
    margin-top: 24px;
    margin-bottom: 24px;
    font-size: 22px;
    line-height: 1.35em;
}
.page-header.frontpage .video-graphic {
    position: absolute;
    bottom: -50px;
    right: -50px;
    width: 650px;
    height: 650px;
    z-index: -1;
    margin-top: 50px;
}
.page-header.frontpage .portraits-wall {
    z-index: 2;
}
.page-header.frontpage .portraits-wall .user-portrait a {
    display: block;
    width: 100%;
    height: 100%;
}
.page-header.frontpage .portraits-wall .user-portrait:first-child {
    -o-transform: translate(-340px, -220px);
    -webkit-transform: translate(-340px, -220px);
    transform: translate(-340px, -220px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(2) {
    -o-transform: translate(-182.5px, -260px);
    -webkit-transform: translate(-182.5px, -260px);
    transform: translate(-182.5px, -260px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(3) {
    -o-transform: translate(-542.5px, -226px);
    -webkit-transform: translate(-542.5px, -226px);
    transform: translate(-542.5px, -226px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(4) {
    -o-transform: translate(132.5px, -232px);
    -webkit-transform: translate(132.5px, -232px);
    transform: translate(132.5px, -232px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(5) {
    -o-transform: translate(290px, -240px);
    -webkit-transform: translate(290px, -240px);
    transform: translate(290px, -240px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(6) {
    -o-transform: translate(447.5px, -170px);
    -webkit-transform: translate(447.5px, -170px);
    transform: translate(447.5px, -170px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(7) {
    -o-transform: translate(425px, 5px);
    -webkit-transform: translate(425px, 5px);
    transform: translate(425px, 5px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(8) {
    -o-transform: translate(582.5px, 140px);
    -webkit-transform: translate(582.5px, 140px);
    transform: translate(582.5px, 140px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(9) {
    -o-transform: translate(470px, 230px);
    -webkit-transform: translate(470px, 230px);
    transform: translate(470px, 230px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(10) {
    -o-transform: translate(290px, 207.5px);
    -webkit-transform: translate(290px, 207.5px);
    transform: translate(290px, 207.5px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(11) {
    -o-transform: translate(110px, 252.5px);
    -webkit-transform: translate(110px, 252.5px);
    transform: translate(110px, 252.5px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(12) {
    -o-transform: translate(-70px, 225.5px);
    -webkit-transform: translate(-70px, 225.5px);
    transform: translate(-70px, 225.5px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(13) {
    -o-transform: translate(-250px, 245.75px);
    -webkit-transform: translate(-250px, 245.75px);
    transform: translate(-250px, 245.75px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(14) {
    -o-transform: translate(-385px, 117.5px);
    -webkit-transform: translate(-385px, 117.5px);
    transform: translate(-385px, 117.5px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(15) {
    -o-transform: translate(-565px, 207.5px);
    -webkit-transform: translate(-565px, 207.5px);
    transform: translate(-565px, 207.5px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(16) {
    -o-transform: translate(-520px, 27.5px);
    -webkit-transform: translate(-520px, 27.5px);
    transform: translate(-520px, 27.5px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(17) {
    -o-transform: translate(-452.5px, -107.5px);
    -webkit-transform: translate(-452.5px, -107.5px);
    transform: translate(-452.5px, -107.5px);
}
.page-header.frontpage .portraits-wall .user-portrait:nth-child(18) {
    -o-transform: translate(-610px, -107.5px);
    -webkit-transform: translate(-610px, -107.5px);
    transform: translate(-610px, -107.5px);
}
@media screen and (max-width: 1200px) {
    .page-header.frontpage .main-headline-container h1 {
        font-size: 42px;
    }
    .page-header.frontpage .main-headline-container .description {
        max-width: 380px;
        font-size: 18px;
    }
    .page-header.frontpage .video-graphic {
        right: -20px;
        width: 600px;
        height: 600px;
    }
}
@media screen and (max-width: 960px) {
    .page-header.frontpage .main-headline-container {
        padding: 80px 0 50px;
    }
    .page-header.frontpage .video-graphic {
        right: -30px;
        width: 500px;
        height: 500px;
    }
}
@media screen and (max-width: 800px) {
    .page-header.frontpage .main-headline-container {
        padding: 50px 0 24px;
        text-align: center;
    }
    .page-header.frontpage .main-headline-container h1 {
        font-size: 42px;
    }
    .page-header.frontpage .main-headline-container .description {
        max-width: 560px;
        font-size: 18px;
        margin-left: auto;
        margin-right: auto;
    }
    .page-header.frontpage .video-graphic {
        display: none;
    }
}
@media screen and (max-width: 375px) {
    .page-header.frontpage .main-headline-container {
        padding: 24px 0;
    }
    .page-header.frontpage .main-headline-container h1 {
        font-size: 38px;
    }
    .page-header.frontpage .main-headline-container .description {
        font-size: 16px;
    }
}
.content-section.arrow-flow:before,
.page-section.arrow-flow:before {
    display: block;
    font-family: glyphs;
    content: "\E96A";
    width: 120px;
    height: 50px;
    line-height: 50px;
    position: absolute;
    left: 50%;
    margin-left: -60px;
    text-align: center;
    font-size: 70px;
    color: #e5eeff;
}
.content-section.arrow-flow.down:before,
.page-section.arrow-flow.down:before {
    bottom: -38px;
}
.content-section.arrow-flow.up:before,
.page-section.arrow-flow.up:before {
    top: -8px;
}
@media screen and (max-width: 660px) {
    .content-section.arrow-flow:before,
    .page-section.arrow-flow:before {
        font-size: 45px;
        height: 30px;
        line-height: 30px;
    }
    .content-section.arrow-flow.down:before,
    .page-section.arrow-flow.down:before {
        bottom: -26px;
    }
    .content-section.arrow-flow.up:before,
    .page-section.arrow-flow.up:before {
        top: -4px;
    }
}
.content-section.bg-dark.arrow-flow.up:before,
.content-section.bg-default.arrow-flow.up:before,
.content-section.bg-light.arrow-flow.up:before,
.content-section.bg-main.arrow-flow.up:before,
.content-section.bg-medium.arrow-flow.up:before,
.page-section.bg-dark.arrow-flow.up:before,
.page-section.bg-default.arrow-flow.up:before,
.page-section.bg-light.arrow-flow.up:before,
.page-section.bg-main.arrow-flow.up:before,
.page-section.bg-medium.arrow-flow.up:before {
    color: #fff;
}
.content-section.bg-default.arrow-flow.down:before,
.content-section.bg-light.arrow-flow.down:before,
.content-section.bg-main.arrow-flow.down:before,
.page-section.bg-default.arrow-flow.down:before,
.page-section.bg-light.arrow-flow.down:before,
.page-section.bg-main.arrow-flow.down:before {
    color: #f2f6ff;
}
.content-section.bg-dark.arrow-flow.down:before,
.page-section.bg-dark.arrow-flow.down:before {
    color: #1c2c3e;
}
.content-section:not([class*="bg-"]) + .content-section:not([class*="bg-"]),
.content-section:not([class*="bg-"]) + .page-section:not([class*="bg-"]),
.content-section[class*="bg-"] + .content-section[class*="bg-"],
.content-section[class*="bg-"] + .page-section[class*="bg-"],
.page-section:not([class*="bg-"]) + .content-section:not([class*="bg-"]),
.page-section:not([class*="bg-"]) + .page-section:not([class*="bg-"]),
.page-section[class*="bg-"] + .content-section[class*="bg-"],
.page-section[class*="bg-"] + .page-section[class*="bg-"] {
    padding-top: 0;
}
.content-section.fake-fullwidth,
.page-section.fake-fullwidth {
    position: relative;
}
.content-section.fake-fullwidth:after,
.page-section.fake-fullwidth:after {
    display: block;
    content: "";
    width: 500%;
    height: 100%;
    position: absolute;
    top: 0;
    left: -250%;
    z-index: -1;
}
.content-section.fake-fullwidth.bg-default:after,
.content-section.fake-fullwidth.bg-light:after,
.content-section.fake-fullwidth.bg-main:after,
.page-section.fake-fullwidth.bg-default:after,
.page-section.fake-fullwidth.bg-light:after,
.page-section.fake-fullwidth.bg-main:after {
    background-color: #f2f6ff;
}
.content-section.fake-fullwidth.bg-dark:after,
.page-section.fake-fullwidth.bg-dark:after {
    background-color: #082446;
}
.content-section.fake-fullwidth.bg-white:after,
.page-section.fake-fullwidth.bg-white:after {
    background-color: #fff;
}
.page-section {
    position: relative;
    padding-top: 60px;
    padding-bottom: 60px;
}
.page-section h1,
.page-section h2,
.page-section h3,
.page-section h4,
.page-section h5 {
    margin-bottom: 1em;
}
@media screen and (max-width: 660px) {
    .page-section h1,
    .page-section h2,
    .page-section h3,
    .page-section h4,
    .page-section h5 {
        margin-bottom: 0.875em;
    }
}
.page-section > h1,
.page-section > h2 {
    text-align: center;
}
.page-section ol + p,
.page-section p + ul,
.page-section ul + p,
ul + .page-section p {
    margin-top: 1.325em;
}
@media only screen and (min-device-width: ) and (max-device-width: ) and (-webkit-min-device-pixel-ratio: ) {
    .page-section {
        padding-top: 40px;
        padding-bottom: 40px;
    }
    .page-section + .page-section {
        margin-top: 38px;
    }
}
@media only screen and (min-device-width: 300px) and (max-device-width: 660px) {
    .page-section {
        padding-top: 40px;
        padding-bottom: 40px;
    }
    .page-section + .page-section {
        margin-top: 38px;
    }
}
.content-section {
    position: relative;
}
.main-footer {
    text-align: center;
}
.brand {
    display: block;
    width: 130px;
    height: 50px;
    background: url(images/KT-logo-white.svg) no-repeat 50%;
    background-size: contain;
    font-size: 0;
}
@media screen and (max-width: 1024px) {
    .brand {
        height: 50px;
    }
}
@media screen and (max-width: 660px) {
    .brand {
        width: 110px;
    }
}
.main-header {
    padding-left: 40px;
    padding-right: 40px;
    clear: both;
    width: 100%;
    height: 50px;
    background-color: #082446;
    -webkit-box-shadow: 0 2px 4px 0 rgba(6, 49, 116, 0.16);
    box-shadow: 0 2px 4px 0 rgba(6, 49, 116, 0.16);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 500;
}
@media screen and (min-width: 1200px) {
    .main-header {
        padding-left: calc((100% - 1080px) / 2);
        padding-right: calc((100% - 1080px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .main-header {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.main-header:after {
    display: block;
    visibility: hidden;
    clear: both;
    height: 0;
    content: " ";
    font-size: 0;
}
.main-header .brand {
    float: left;
}
.main-header .main-menu {
    display: none;
}
.main-header .kt-contact-phone {
    display: inline-block;
    float: right;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    line-height: 50px;
    margin-right: 30px;
}
.main-header .kt-contact-phone:before {
    display: inline;
    font-family: glyphs;
    content: "\E968";
    margin-right: 10px;
    color: #4d92ff;
    font-size: 13px;
}
.main-header .button-cta {
    display: none;
    font-size: 16px;
    line-height: 18px;
    height: auto;
    min-height: 10px;
    float: right;
    padding-right: 50px;
    margin-top: 9px;
    cursor: pointer;
}
.main-header .button-cta:after,
.main-header .button-cta:before {
    height: 100%;
    width: 40px;
    top: 0;
    line-height: 28px;
    margin-top: 0;
}
.main-header .case-study-main-header,
.main-header .frontpage-main-header {
    clear: both;
    height: 50px;
}
.main-header .case-study-main-header:after,
.main-header .frontpage-main-header:after {
    display: block;
    visibility: hidden;
    clear: both;
    height: 0;
    content: " ";
    font-size: 0;
}
.main-header .case-study-main-header .brand {
    float: left;
}
.main-header .case-study-main-header .button-cta {
    display: inline-block;
}
.main-header .frontpage-main-header {
    height: 70px;
    position: relative;
}
.main-header .frontpage-main-header .brand {
    float: none;
    background: url(images/KT-logo.svg) no-repeat 50%;
    width: 160px;
    position: absolute;
    top: 50%;
    left: 50%;
    -o-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.main-header .frontpage-main-header .mobile-menu-toggle {
    display: none;
    position: absolute;
    top: 24px;
    right: 0;
}
.main-header .frontpage-main-header .main-menu {
    display: inline-block;
    width: auto;
}
.main-header .frontpage-main-header .main-menu ul li {
    display: inline-block;
    height: 70px;
    margin-right: 15px;
}
.main-header .frontpage-main-header .main-menu ul li a {
    height: 70px;
    color: #7c8b9f;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 13px;
    line-height: 70px;
    letter-spacing: 0.125em;
}
.main-header .frontpage-main-header .main-menu ul li a:hover {
    color: #4d92ff;
    -webkit-transition: color 0.15s ease-out;
    -o-transition: color 0.15s ease-out;
    transition: color 0.15s ease-out;
}
.main-header .frontpage-main-header .main-menu ul li a.login-link:before {
    display: block;
    content: "";
    display: inline-block;
    font-family: glyphs;
    content: "\E9F9";
    font-size: 12px;
    margin-right: 0.25em;
}
.main-header .frontpage-main-header .main-menu ul li:last-child {
    margin-right: 0;
}
.main-header .frontpage-main-header .main-menu.company {
    float: left;
}
.main-header .frontpage-main-header .main-menu.consumer {
    float: right;
}
@media screen and (max-width: 1024px) {
    .main-header {
        height: 50px;
    }
}
@media screen and (max-width: 960px) {
    .main-header .frontpage-main-header .brand {
        width: 140px;
        top: 50%;
        left: 0;
        -o-transform: translateY(-50%);
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
    }
    .main-header .frontpage-main-header .main-menu ul li {
        margin-right: 6px;
    }
    .main-header .frontpage-main-header .main-menu ul li a {
        font-size: 11px;
    }
    .main-header .frontpage-main-header .main-menu.company {
        float: right;
    }
    .main-header .frontpage-main-header .main-menu.consumer {
        float: right;
        padding-right: 10px;
    }
}
@media screen and (max-width: 800px) {
    .main-header .frontpage-main-header .main-menu-wrapper {
        width: 100%;
        height: auto;
        max-height: 0;
        background-color: #fff;
        position: fixed;
        left: 0;
        top: 70px;
        -webkit-box-shadow: 0 4px 6px 0 rgba(6, 49, 116, 0.25);
        box-shadow: 0 4px 6px 0 rgba(6, 49, 116, 0.25);
        z-index: 500;
        overflow: hidden;
        -webkit-transition: all 0.25s ease-in;
        -o-transition: all 0.25s ease-in;
        transition: all 0.25s ease-in;
    }
    .main-header .frontpage-main-header .main-menu {
        width: 100vw;
        float: none !important;
    }
    .main-header .frontpage-main-header .main-menu ul {
        display: block;
    }
    .main-header .frontpage-main-header .main-menu ul li {
        display: block;
        width: 100%;
        height: auto;
        padding-top: 1em;
        padding-bottom: 1em;
        text-align: center;
        opacity: 0;
        -webkit-transition: all 0.25s ease-in;
        -o-transition: all 0.25s ease-in;
        transition: all 0.25s ease-in;
    }
    .main-header .frontpage-main-header .main-menu ul li a {
        height: auto;
        display: block;
        font-size: 16px;
        line-height: 1.2em;
    }
    .main-header .frontpage-main-header .main-menu.consumer {
        padding-bottom: 30px;
    }
    .main-header .frontpage-main-header .mobile-menu-toggle {
        display: block;
    }
    .main-header .frontpage-main-header .mobile-menu-toggle.is-open + .main-menu-wrapper {
        max-height: 2000px;
        -webkit-transition-delay: 1s;
        -o-transition-delay: 1s;
        transition-delay: 1s;
        -webkit-transition: all 2s ease-out;
        -o-transition: all 2s ease-out;
        transition: all 2s ease-out;
    }
    .main-header .frontpage-main-header .mobile-menu-toggle.is-open + .main-menu-wrapper ul li {
        opacity: 1;
        -webkit-transition-delay: 1s;
        -o-transition-delay: 1s;
        transition-delay: 1s;
        -webkit-transition: all 0.25s ease-in;
        -o-transition: all 0.25s ease-in;
        transition: all 0.25s ease-in;
    }
}
@media screen and (max-width: 660px) {
    .main-header .kt-contact-phone {
        display: none;
    }
    .main-header .button-cta {
        font-size: 13px;
        padding-left: 10px;
        padding-right: 10px;
    }
    .main-header .button-cta:after,
    .main-header .button-cta:before {
        display: none;
    }
}
@media screen and (max-width: 330px) {
    .main-header .button-cta {
        font-size: 10px;
    }
}
.mobile-menu-toggle {
    width: 20px;
    height: 18px;
    -webkit-transition: 0.5s ease-in-out;
    -o-transition: 0.5s ease-in-out;
    transition: 0.5s ease-in-out;
    cursor: pointer;
}
.mobile-menu-toggle,
.mobile-menu-toggle span {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
}
.mobile-menu-toggle span {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: #637d9b;
    border-radius: 9px;
    opacity: 1;
    left: 0;
    -webkit-transition: 0.25s ease-in-out;
    -o-transition: 0.25s ease-in-out;
    transition: 0.25s ease-in-out;
}
.mobile-menu-toggle span:first-child {
    top: 0;
}
.mobile-menu-toggle span:nth-child(2),
.mobile-menu-toggle span:nth-child(3) {
    top: 8px;
}
.mobile-menu-toggle span:nth-child(4) {
    top: 16px;
}
.mobile-menu-toggle.is-open span:first-child {
    top: 18px;
    width: 0;
    left: 50%;
}
.mobile-menu-toggle.is-open span:nth-child(2) {
    -webkit-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}
.mobile-menu-toggle.is-open span:nth-child(3) {
    -webkit-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.mobile-menu-toggle.is-open span:nth-child(4) {
    top: 18px;
    width: 0;
    left: 50%;
}
.template-case-study .main-header .frontpage-main-header {
    display: none;
}
.error404 .main-header,
.page-template-editorial .main-header,
.page-template-landing .main-header,
.template-frontpage .main-header,
.template-login .main-header,
.template-mission .main-header {
    background-color: #fff;
    -webkit-box-shadow: none;
    box-shadow: none;
    height: 70px;
}
.error404 .main-header .case-study-main-header,
.page-template-editorial .main-header .case-study-main-header,
.page-template-landing .main-header .case-study-main-header,
.template-frontpage .main-header .case-study-main-header,
.template-login .main-header .case-study-main-header,
.template-mission .main-header .case-study-main-header {
    display: none;
}
.template-login .main-header {
    height: 70px;
}
.template-login .main-header .frontpage-main-header {
    height: 70px;
}
.template-login .main-header .frontpage-main-header .brand {
    left: 50%;
    -o-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
.page-template-landing .main-menu-wrapper {
    display: none;
}
.main-footer {
    clear: both;
    padding: 30px 40px;
}
.main-footer:after {
    display: block;
    visibility: hidden;
    clear: both;
    height: 0;
    content: " ";
    font-size: 0;
}
@media screen and (min-width: 1200px) {
    .main-footer {
        padding-left: calc((100% - 1080px) / 2);
        padding-right: calc((100% - 1080px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .main-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.main-footer .widget {
    display: none;
}
.main-footer .copyright,
.main-footer .footer-nav {
    display: inline-block;
    font-size: 14px;
}
.main-footer .copyright {
    float: left;
    line-height: 24px;
}
.main-footer .footer-nav {
    float: right;
}
.main-footer .footer-nav ul li {
    display: inline-block;
    margin-right: 15px;
}

.main-footer .footer-nav ul li a {
    color: #385575;
}
.main-footer .footer-nav ul li:last-child {
    margin-right: 0;
}
@media screen and (max-width: 960px) {
    .main-footer .copyright,
    .main-footer .footer-nav {
        display: block;
        float: none;
        text-align: center;
    }
    .main-footer .footer-nav {
        margin-top: 15px;
    }
}
.man-gesturing-to-headline {
    height: 100%;
    position: relative;
}
.man-gesturing-to-headline img {
    display: block;
    width: 140%;
    max-width: 140%;
    height: auto;
    position: absolute;
    bottom: -60px;
    right: 0;
    z-index: 1;
}
.special-headline.layout-reference-01 {
    font-size: 48px;
    line-height: 60px;
}
.special-headline.layout-reference-01 span {
    display: block;
    font-size: 3em;
    line-height: 0.875;
    font-weight: 900;
}
.page-template-landing {
    padding-top: 0;
}
.page-template-landing .main-header {
    position: relative;
    background-color: #f2f6ff;
}
.page-template-landing .main-content {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.page-template-landing .page-header {
    padding-top: 150px;
}
.page-template-landing .page-section {
    padding-left: 40px;
    padding-right: 40px;
}
@media screen and (min-width: 1200px) {
    .page-template-landing .page-section {
        padding-left: calc((100% - 860px) / 2);
        padding-right: calc((100% - 860px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .page-template-landing .page-section {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.template-frontpage .main-content {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.template-frontpage .page-header .portraits-wall {
    width: 100%;
    height: 680px;
    position: relative;
}
.template-frontpage .page-header .portraits-wall .user-portrait {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -40px;
    margin-left: -40px;
    cursor: pointer;
}
.template-frontpage .page-header .portraits-wall .user-portrait .image {
    overflow: hidden;
}
.template-frontpage .page-header .portraits-wall .youtube-play-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    -o-transform: translate(-480px, -50%);
    -webkit-transform: translate(-480px, -50%);
    transform: translate(-480px, -50%);
    cursor: pointer;
    z-index: -1;
}
.template-frontpage .page-section {
    padding-left: 40px;
    padding-right: 40px;
}
@media screen and (min-width: 1200px) {
    .template-frontpage .page-section {
        padding-left: calc((100% - 1080px) / 2);
        padding-right: calc((100% - 1080px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .template-frontpage .page-section {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.template-frontpage .main-footer .widget {
    display: none;
}
#vktext h1,
#vktext h2 {
    text-align: center;
}
#vktext h1 {
    margin-bottom: 15px;
}
#vktext h2 {
    font-size: 26px;
}
#vktext img {
    display: block;
    width: 100%;
    height: auto;
}
#vktext img.contact-cloud-doodle {
    width: 140px;
    position: absolute;
    bottom: -60px;
    right: -10px;
    z-index: 5;
}
#vktext .textblock-no-image-centered {
    width: 66%;
    margin: 40px auto;
}
#vktext .textblock-no-image-centered p {
    hyphen: auto;
}
#vktext .margin-big-bottom {
    margin-bottom: 38px;
}
#vktext .row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: left;
    -ms-flex-pack: left;
    justify-content: left;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    margin-left: -20px;
}
#vktext [class*="col-"] {
    position: relative;
    margin-left: 10px;
    margin-right: 10px;
    -ms-flex-item-align: center;
    align-self: center;
}
#vktext .col-md-6 {
    -ms-flex-preferred-size: calc(50% - 20px);
    flex-basis: calc(50% - 20px);
}
#vktext .col-md-8 {
    -ms-flex-preferred-size: calc(66.66667% - 20px);
    flex-basis: calc(66.66667% - 20px);
}
#vktext .col-md-4 {
    -ms-flex-preferred-size: calc(33.33333% - 20px);
    flex-basis: calc(33.33333% - 20px);
}
#vktext .splittest > div:first-child,
#vktext .splittest > div:nth-child(2) {
    margin-bottom: 33%;
}
#vktext .splittest > div:nth-child(3) {
    margin-bottom: 75%;
}
#vktext .splittest > div:nth-child(4) {
    margin-bottom: 45%;
}
#vktext .splittest > div:nth-child(5) {
    margin-bottom: 35%;
}
#vktext .fanpage {
    position: relative;
}
#vktext .fanpage .fanpage-left,
#vktext .fanpage .fanpage-right {
    width: calc(50% - 20px);
    position: absolute;
}
#vktext .fanpage .fanpage-left {
    left: 0;
    bottom: 50px;
}
#vktext .fanpage .fanpage-right {
    right: 0;
    top: 50px;
}
#vktext .text-centered {
    text-align: center;
}
@media screen and (max-width: 960px) {
    #vktext .fanpage .fanpage-right {
        top: 0;
    }
    #vktext .fanpage .fanpage-left {
        bottom: 0;
    }
}
@media screen and (max-width: 810px) {
    #vktext .fanpage {
        position: relative;
    }
    #vktext .fanpage .fanpage-left,
    #vktext .fanpage .fanpage-right {
        width: 100% !important;
        position: static !important;
    }
    #vktext .textblock-no-image-centered,
    #vktext [class*="col-"] {
        width: 100% !important;
    }
    #vktext [class^="col-md"] {
        margin-left: 0;
    }
    #vktext .row {
        display: block;
        margin-left: 0 !important;
    }
    #vktext img {
        width: 80%;
        height: auto;
        margin: 35px auto;
    }
    #vktext img.contact-cloud-doodle {
        display: none;
    }
    #vktext .splittest > div {
        margin-bottom: 1.325em !important;
    }
    #vktext .button.blue {
        width: 300px;
        height: 51px;
        background-size: 903px 51px;
    }
}
#splittest-homepage-video {
    display: block;
    margin-top: 35px;
}
.template-mission .main-content {
    padding-left: 0 !important;
    padding-right: 0 !important;
}
.template-mission .page-content {
    padding-left: 40px;
    padding-right: 40px;
    padding-bottom: 60px;
}
@media screen and (min-width: 1200px) {
    .template-mission .page-content {
        padding-left: calc((100% - 1080px) / 2);
        padding-right: calc((100% - 1080px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .template-mission .page-content {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.template-mission .page-section > h1,
.template-mission .page-section > h2,
.template-mission .page-section > h3 {
    text-align: left;
    margin-bottom: 0.5em;
}
.template-mission .page-section p + h1,
.template-mission .page-section p + h2 {
    margin-top: 1.5em;
}
.template-mission .page-section:first-of-type {
    padding-top: 0;
}
.template-mission .klicktipp-mission {
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 3rem;
    background-color: #f4f8ff;
    position: relative;
}
.template-mission .klicktipp-mission .centerbox {
    padding-left: 40px;
    padding-right: 40px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
}
@media screen and (min-width: 1200px) {
    .template-mission .klicktipp-mission .centerbox {
        max-width: 1080px;
    }
}
@media screen and (max-width: 1024px) {
    .template-mission .klicktipp-mission .centerbox {
        padding-left: 20px;
        padding-right: 20px;
    }
}
@media screen and (max-width: 780px) {
    .template-mission .klicktipp-mission {
        padding-left: 30px;
        padding-right: 30px;
    }
}
.template-mission .klicktipp-mission .centerbox {
    padding-top: 2rem;
    padding-bottom: 3rem;
}
.template-mission .klicktipp-mission .mission-quote {
    font-size: 2.5rem;
    line-height: 1.2em;
    font-weight: 100;
    color: #4a698d;
    text-align: left;
    margin-bottom: 1.5rem;
    margin-left: 300px;
}
.template-mission .klicktipp-mission .mission-quote strong {
    font-weight: 600;
}
.template-mission .klicktipp-mission .mission-cite {
    display: block;
    margin-left: 300px;
}
.template-mission .klicktipp-mission .mission-cite strong {
    color: #1c2c3e;
}
.template-mission .klicktipp-mission figure {
    display: block;
    width: 300px;
    height: 300px;
    background-image: url(images/mario_wolosz.png);
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    bottom: 0;
    left: 0;
    margin-left: -1rem;
}
.template-mission .klicktipp-mission:before {
    display: none;
    content: "";
    width: 120px;
    height: 80px;
    background-image: url(/content_includes/img/kt-icon.png);
    background-position: 0;
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    top: 50%;
    left: 2.5rem;
    margin-top: -40px;
    border-right: 2px solid #fff;
}
.template-mission .poverty-of-time {
    border-radius: 0.222222222rem;
    padding: 3rem;
    margin-top: 24px;
    margin-bottom: 3rem;
    background-color: #f4f8ff;
    border: none;
    font-size: 1.25rem;
    line-height: 1.4em;
    font-weight: 200;
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
}
.template-mission .poverty-of-time .content,
.template-mission .poverty-of-time figure {
    -ms-flex-item-align: center;
    align-self: center;
}
.template-mission .poverty-of-time .content {
    -ms-flex-preferred-size: 68%;
    flex-basis: 68%;
    font-size: 1.15em;
    font-weight: 200;
}
.template-mission .poverty-of-time figure {
    -ms-flex-preferred-size: 28%;
    flex-basis: 28%;
    height: 0;
    padding-top: 32%;
    border-radius: 3px;
    background-color: #fff;
    background-image: url(images/mario_on_bridge_8b31ade1.jpg);
    background-position: bottom;
    background-repeat: no-repeat;
    background-size: cover;
    -webkit-box-shadow: inset 0 0 0 10px #fff;
    box-shadow: inset 0 0 0 10px #fff;
}
@media screen and (max-width: 960px) {
    .template-mission .poverty-of-time {
        display: block;
        padding: 40px;
    }
    .template-mission .poverty-of-time .content,
    .template-mission .poverty-of-time figure {
        width: 100%;
    }
    .template-mission .poverty-of-time .content {
        font-size: 16px;
    }
    .template-mission .poverty-of-time figure {
        margin: 24px auto 0;
        width: 400px;
        height: 560px;
    }
}
@media screen and (max-width: 660px) {
    .template-mission .poverty-of-time {
        padding: 20px;
    }
    .template-mission .poverty-of-time figure {
        width: 280px;
        height: 370px;
    }
}
@media screen and (max-width: 1280px) {
    .template-mission .klicktipp-mission .centerbox {
        padding-top: 0.75rem;
        padding-bottom: 2rem;
    }
    .template-mission .klicktipp-mission .mission-quote {
        font-size: 2.125rem;
        line-height: 1.2em;
        margin-left: 230px;
        margin-bottom: 0.75rem;
    }
    .template-mission .klicktipp-mission .mission-cite {
        margin-left: 230px;
    }
    .template-mission .klicktipp-mission .mission-cite p,
    .template-mission .klicktipp-mission .mission-cite stron {
        font-size: 0.875rem;
        line-height: 1.1em;
    }
    .template-mission .klicktipp-mission figure {
        width: 224px;
        height: 224px;
    }
}
@media screen and (max-width: 1040px) {
    .template-mission .content-viewer {
        padding-left: 0;
    }
    .template-mission .content-navigation {
        display: none;
    }
    .template-mission .centerbox[class*="grid-"] {
        width: 100%;
        max-width: 100%;
    }
}
@media screen and (max-width: 780px) {
    .template-mission .klicktipp-mission .mission-cite,
    .template-mission .klicktipp-mission .mission-quote {
        margin-left: 0;
    }
    .template-mission .klicktipp-mission figure {
        display: none;
    }
}
@media screen and (max-width: 720px) {
    .template-mission .klicktipp-mission .mission-quote {
        font-size: 1.625rem;
    }
}
@media screen and (max-width: 480px) {
    .template-mission .klicktipp-mission .mission-quote {
        font-size: 1.375rem;
    }
}
.template-case-study-blocks .page-title,
.template-case-study .page-title {
    font-size: 60px;
    text-transform: uppercase;
    font-weight: 900;
    margin: 0 0 20px;
}
@media screen and (max-width: 1200px) {
    .template-case-study-blocks .page-title,
    .template-case-study .page-title {
        font-size: 52px;
        line-height: 1em;
        margin-bottom: 0.25em;
    }
}
@media screen and (max-width: 1024px) {
    .template-case-study-blocks .page-title,
    .template-case-study .page-title {
        font-size: 46px;
        line-height: 1em;
        margin-bottom: 0.25em;
    }
}
@media only screen and (min-device-width: ) and (max-device-width: ) and (-webkit-min-device-pixel-ratio: ) {
    .template-case-study-blocks .page-title,
    .template-case-study .page-title {
        font-size: 30px;
    }
}
@media only screen and (min-device-width: 300px) and (max-device-width: 660px) {
    .template-case-study-blocks .page-title,
    .template-case-study .page-title {
        font-size: 30px;
    }
}
.template-case-study-blocks .page-header,
.template-case-study .page-header {
    display: none;
}
.template-case-study-blocks .main-content,
.template-case-study .main-content {
    padding-left: 0;
    padding-right: 0;
}
.template-case-study-blocks .page-section,
.template-case-study .page-section {
    padding-left: 40px;
    padding-right: 40px;
}
@media screen and (min-width: 1200px) {
    .template-case-study-blocks .page-section,
    .template-case-study .page-section {
        padding-left: calc((100% - 1080px) / 2);
        padding-right: calc((100% - 1080px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .template-case-study-blocks .page-section,
    .template-case-study .page-section {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.template-case-study-blocks .page-section h2,
.template-case-study .page-section h2 {
    text-align: center;
}
.template-case-study-blocks .page-section figure img,
.template-case-study .page-section figure img {
    display: block;
    width: 100%;
    height: auto;
    margin: 0;
}
.template-case-study-blocks .page-section .content p img,
.template-case-study .page-section .content p img {
    display: block;
    margin: 0 auto;
}
@media screen and (max-width: 1200px) {
    .template-case-study-blocks .page-section .content p img,
    .template-case-study .page-section .content p img {
        width: 100%;
        height: auto;
        margin-left: auto;
    }
}
.template-case-study-blocks .page-section:not([class*="bg-"]) + .page-section:not([class*="bg-"]),
.template-case-study .page-section:not([class*="bg-"]) + .page-section:not([class*="bg-"]) {
    padding-top: 0;
}
.template-case-study-blocks .page-section a[href*="provenexpert"],
.template-case-study .page-section a[href*="provenexpert"] {
    display: block;
    text-align: center;
}
.template-case-study-blocks .page-section a[href*="provenexpert"] img,
.template-case-study .page-section a[href*="provenexpert"] img {
    display: block;
    width: 100%;
    max-width: 465px;
    height: auto;
    margin: 0 auto;
}
.template-case-study-blocks .main-footer .widget,
.template-case-study .main-footer .widget {
    display: none;
}
.template-case-study-blocks blockquote,
.template-case-study blockquote {
    display: block;
    position: relative;
}
.template-case-study-blocks blockquote:after,
.template-case-study-blocks blockquote:before,
.template-case-study blockquote:after,
.template-case-study blockquote:before {
    display: block;
    content: "";
    width: 45px;
    height: 35px;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1My4wNDIiIGhlaWdodD0iMzcuODg3IiB2aWV3Qm94PSIwIDAgNTMuMDQyIDM3Ljg4NyI+CiAgPHBhdGggaWQ9IlBhdGhfMTA1MTIiIGRhdGEtbmFtZT0iUGF0aCAxMDUxMiIgZD0iTTU5LjI1Myw1MS44ODdINDcuODg3TDQwLjMxLDM2LjczMlYxNEg2My4wNDJWMzYuNzMySDUxLjY3NlptLTMwLjMxLDBIMTcuNTc3TDEwLDM2LjczMlYxNEgzMi43MzJWMzYuNzMySDIxLjM2NloiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0xMCAtMTQpIiBmaWxsPSIjY2NkYmY1Ii8+Cjwvc3ZnPgo=);
    background-size: contain;
    background-repeat: no-repeat;
    position: absolute;
}
.template-case-study-blocks blockquote:before,
.template-case-study blockquote:before {
    left: -55px;
    top: -24px;
}
.template-case-study-blocks blockquote:after,
.template-case-study blockquote:after {
    -o-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    right: -55px;
    bottom: -24px;
}
@media screen and (max-width: 660px) {
    .template-case-study-blocks blockquote:after,
    .template-case-study-blocks blockquote:before,
    .template-case-study blockquote:after,
    .template-case-study blockquote:before {
        display: none;
    }
}
.case-study-header {
    padding-left: 40px;
    padding-right: 40px;
    height: 320px;
    background-color: #3376de;
    position: relative;
    overflow: hidden;
}
@media screen and (min-width: 1200px) {
    .case-study-header {
        padding-left: calc((100% - 1080px) / 2);
        padding-right: calc((100% - 1080px) / 2);
    }
}
@media screen and (max-width: 1024px) {
    .case-study-header {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.case-study-header:before {
    padding-left: 40px;
    padding-right: 40px;
    margin-left: auto;
    margin-right: auto;
    -o-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    display: block;
    content: "";
    width: 100%;
    height: 100%;
    background-color: rgba(51, 118, 222, 0.75);
    -webkit-box-shadow: inset 0 0 60px 0 #1f65d3;
    box-shadow: inset 0 0 60px 0 #1f65d3;
    position: absolute;
    top: 0;
    left: 50%;
    z-index: 1;
}
@media screen and (min-width: 1200px) {
    .case-study-header:before {
        max-width: 1080px;
    }
}
@media screen and (max-width: 1024px) {
    .case-study-header:before {
        padding-left: 20px;
        padding-right: 20px;
    }
}
.case-study-header .case-study-header-background {
    width: 100%;
    height: 100%;
    position: relative;
    background-size: 1440px;
    background-repeat: no-repeat;
    background-position: center 50px;
    background-color: #1f65d3;
}
.case-study-header .case-study-header-background img {
    display: block;
    width: 100%;
    height: auto;
    margin: 0 auto;
    mix-blend-mode: hard-light;
}
.case-study-header .case-study-header-background:after,
.case-study-header .case-study-header-background:before {
    display: block;
    content: "";
    background-image: -o-linear-gradient(-90deg, rgba(51, 118, 222, 0) 0, #3376de 40%, #3376de);
    background-image: -webkit-linear-gradient(-90deg, rgba(51, 118, 222, 0), #3376de 40%, #3376de);
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(51, 118, 222, 0)), color-stop(40%, #3376de), to(#3376de));
    background-image: -webkit-linear-gradient(right, rgba(51, 118, 222, 0), #3376de 40%, #3376de);
    background-image: -o-linear-gradient(right, rgba(51, 118, 222, 0) 0, #3376de 40%, #3376de);
    background-image: linear-gradient(-90deg, rgba(51, 118, 222, 0), #3376de 40%, #3376de);
    width: 200px;
    height: 100%;
    position: absolute;
    top: 0;
    z-index: 2;
}
.case-study-header .case-study-header-background:before {
    left: -110px;
}
.case-study-header .case-study-header-background:after {
    -o-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    right: -110px;
}
.case-study-header .page-heading {
    display: block;
    width: 100%;
    text-align: center;
    position: absolute;
    left: 0;
    bottom: 40px;
    z-index: 5;
}
.case-study-header .page-heading .page-subtitle,
.case-study-header .page-heading .page-title {
    color: #fff;
}
.case-study-header .page-heading .page-title {
    line-height: 1.2em;
    margin-bottom: 10px;
}
.case-study-header .page-heading .page-subtitle {
    display: block;
    max-width: 1024px;
    margin: 0 auto;
}
@media screen and (max-width: 1100px) {
    .case-study-header .case-study-header-background img {
        width: 150%;
        margin-left: -25%;
    }
}
@media screen and (max-width: 1024px) {
    .case-study-header {
        height: 280px;
    }
}
@media screen and (max-width: 960px) {
    .case-study-header {
        height: auto;
        padding-top: 56px;
        background-color: #e5eeff;
    }
    .case-study-header:before {
        display: none;
    }
    .case-study-header .page-heading {
        padding-left: 20px;
        padding-right: 20px;
        position: static;
    }
    .case-study-header .page-heading .page-title {
        color: #1c2c3e;
    }
    .case-study-header .page-heading .page-subtitle {
        color: #385575;
    }
    .case-study-header .case-study-header-background {
        display: none;
    }
}
@media screen and (max-width: 660px) {
    .case-study-header {
        padding-top: 40px;
    }
    .case-study-header .page-heading {
        padding-left: 0;
        padding-right: 0;
    }
}
@media screen and (max-width: 660px) {
    .speakers-quote .portraits-grid .user-portrait:first-child {
        margin-top: 0 !important;
    }
}
.case-study-hero,
.proven-expert-reviews {
    padding-top: 40px;
    padding-bottom: 40px;
    overflow: visible;
}
.case-study-hero .container,
.proven-expert-reviews .container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
}
.case-study-hero .case-study-media,
.case-study-hero .case-study-summary,
.proven-expert-reviews .case-study-media,
.proven-expert-reviews .case-study-summary {
    width: 50%;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-item-align: center;
    align-self: center;
}
.case-study-hero .case-study-media a,
.case-study-hero .case-study-summary a,
.proven-expert-reviews .case-study-media a,
.proven-expert-reviews .case-study-summary a {
    width: 465px;
    margin: 0 auto;
}
.case-study-hero .case-study-summary,
.proven-expert-reviews .case-study-summary {
    height: 100%;
    padding-left: 60px;
    position: relative;
}
.case-study-hero .case-study-summary .summary-statement,
.proven-expert-reviews .case-study-summary .summary-statement {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 1.325em;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
}
.case-study-hero .case-study-summary .summary-statement .icon,
.proven-expert-reviews .case-study-summary .summary-statement .icon {
    -ms-flex-preferred-size: 40px !important;
    flex-basis: 40px !important;
    z-index: 1;
}
.case-study-hero .case-study-summary .summary-statement .icon i:before,
.proven-expert-reviews .case-study-summary .summary-statement .icon i:before {
    color: #4d92ff;
}
.case-study-hero .case-study-summary .summary-statement .description,
.proven-expert-reviews .case-study-summary .summary-statement .description {
    -ms-flex-preferred-size: 100% !important;
    flex-basis: 100% !important;
    margin-left: 0 !important;
    z-index: 1;
}
.case-study-hero .case-study-summary .summary-statement:last-child,
.proven-expert-reviews .case-study-summary .summary-statement:last-child {
    margin-bottom: 0;
}
.case-study-hero .case-study-summary:before,
.proven-expert-reviews .case-study-summary:before {
    display: none;
    content: "";
    width: 100%;
    height: calc(100% + 160px);
    background: url(images/cs_hero_arrow_fader_white.png) no-repeat 100%;
    background-size: contain;
    position: absolute;
    top: -80px;
    right: 50px;
    z-index: 0;
}
.case-study-hero .case-study-media,
.case-study-hero .proven-expert-media,
.proven-expert-reviews .case-study-media,
.proven-expert-reviews .proven-expert-media {
    height: 304px;
    max-height: 304px;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 6px;
    position: relative;
    overflow: visible;
    cursor: inherit;
}
.case-study-hero .case-study-media figure,
.case-study-hero .proven-expert-media figure,
.proven-expert-reviews .case-study-media figure,
.proven-expert-reviews .proven-expert-media figure {
    margin: 0;
    border-radius: 6px;
    overflow: hidden;
}
.case-study-hero .case-study-media iframe,
.case-study-hero .proven-expert-media iframe,
.proven-expert-reviews .case-study-media iframe,
.proven-expert-reviews .proven-expert-media iframe {
    display: block;
    width: 380px;
    height: 213.75px;
}
.case-study-hero .case-study-media .video-info,
.case-study-hero .case-study-media .video-speakers,
.case-study-hero .proven-expert-media .video-info,
.case-study-hero .proven-expert-media .video-speakers,
.proven-expert-reviews .case-study-media .video-info,
.proven-expert-reviews .case-study-media .video-speakers,
.proven-expert-reviews .proven-expert-media .video-info,
.proven-expert-reviews .proven-expert-media .video-speakers {
    position: absolute;
    bottom: 15px;
    left: 15px;
    z-index: 486;
}
.case-study-hero .case-study-media .video-info .video-title,
.case-study-hero .case-study-media .video-info h4,
.case-study-hero .case-study-media .video-speakers .video-title,
.case-study-hero .case-study-media .video-speakers h4,
.case-study-hero .proven-expert-media .video-info .video-title,
.case-study-hero .proven-expert-media .video-info h4,
.case-study-hero .proven-expert-media .video-speakers .video-title,
.case-study-hero .proven-expert-media .video-speakers h4,
.proven-expert-reviews .case-study-media .video-info .video-title,
.proven-expert-reviews .case-study-media .video-info h4,
.proven-expert-reviews .case-study-media .video-speakers .video-title,
.proven-expert-reviews .case-study-media .video-speakers h4,
.proven-expert-reviews .proven-expert-media .video-info .video-title,
.proven-expert-reviews .proven-expert-media .video-info h4,
.proven-expert-reviews .proven-expert-media .video-speakers .video-title,
.proven-expert-reviews .proven-expert-media .video-speakers h4 {
    color: #ffa20f;
    font-size: 10px;
    text-transform: uppercase;
    margin-bottom: 0;
}
.case-study-hero .case-study-media .video-info .video-speaker,
.case-study-hero .case-study-media .video-info .video-subtitle,
.case-study-hero .case-study-media .video-speakers .video-speaker,
.case-study-hero .case-study-media .video-speakers .video-subtitle,
.case-study-hero .proven-expert-media .video-info .video-speaker,
.case-study-hero .proven-expert-media .video-info .video-subtitle,
.case-study-hero .proven-expert-media .video-speakers .video-speaker,
.case-study-hero .proven-expert-media .video-speakers .video-subtitle,
.proven-expert-reviews .case-study-media .video-info .video-speaker,
.proven-expert-reviews .case-study-media .video-info .video-subtitle,
.proven-expert-reviews .case-study-media .video-speakers .video-speaker,
.proven-expert-reviews .case-study-media .video-speakers .video-subtitle,
.proven-expert-reviews .proven-expert-media .video-info .video-speaker,
.proven-expert-reviews .proven-expert-media .video-info .video-subtitle,
.proven-expert-reviews .proven-expert-media .video-speakers .video-speaker,
.proven-expert-reviews .proven-expert-media .video-speakers .video-subtitle {
    color: #fff;
    font-weight: 800;
}
.case-study-hero .case-study-media .video-info .video-subtitle,
.case-study-hero .case-study-media .video-info .video-title,
.case-study-hero .proven-expert-media .video-info .video-subtitle,
.case-study-hero .proven-expert-media .video-info .video-title,
.proven-expert-reviews .case-study-media .video-info .video-subtitle,
.proven-expert-reviews .case-study-media .video-info .video-title,
.proven-expert-reviews .proven-expert-media .video-info .video-subtitle,
.proven-expert-reviews .proven-expert-media .video-info .video-title {
    padding: 6px;
}
.case-study-hero .case-study-media .video-info .video-title,
.case-study-hero .proven-expert-media .video-info .video-title,
.proven-expert-reviews .case-study-media .video-info .video-title,
.proven-expert-reviews .proven-expert-media .video-info .video-title {
    display: inline-block;
    width: auto;
    color: #fff;
    font-size: 14px;
    background-color: #ffa20f;
}
.case-study-hero .case-study-media .video-info .video-subtitle,
.case-study-hero .proven-expert-media .video-info .video-subtitle,
.proven-expert-reviews .case-study-media .video-info .video-subtitle,
.proven-expert-reviews .proven-expert-media .video-info .video-subtitle {
    background-color: #1c2c3e;
}
.case-study-hero .case-study-media .video-poster,
.case-study-hero .proven-expert-media .video-poster,
.proven-expert-reviews .case-study-media .video-poster,
.proven-expert-reviews .proven-expert-media .video-poster {
    display: block;
    width: 100%;
    height: 100%;
    background-size: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}
.case-study-hero .case-study-media .video-poster:before,
.case-study-hero .proven-expert-media .video-poster:before,
.proven-expert-reviews .case-study-media .video-poster:before,
.proven-expert-reviews .proven-expert-media .video-poster:before {
    background-image: -o-linear-gradient(rgba(28, 44, 62, 0) 0, rgba(28, 44, 62, 0.625) 50%, #1c2c3e 100%);
    background-image: -webkit-linear-gradient(rgba(28, 44, 62, 0), rgba(28, 44, 62, 0.625) 50%, #1c2c3e);
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(28, 44, 62, 0)), color-stop(50%, rgba(28, 44, 62, 0.625)), to(#1c2c3e));
    background-image: linear-gradient(rgba(28, 44, 62, 0), rgba(28, 44, 62, 0.625) 50%, #1c2c3e);
    display: none;
    content: "";
    width: 100%;
    height: 120px;
    position: absolute;
    left: 0;
    bottom: 4px;
    -webkit-box-shadow: 0 4px 0 0 #000;
    box-shadow: 0 4px 0 0 #000;
}
.case-study-hero .case-study-media-player,
.case-study-hero .proven-expert-media-player,
.proven-expert-reviews .case-study-media-player,
.proven-expert-reviews .proven-expert-media-player {
    width: 540px;
    max-width: 600px;
    max-height: 304px;
    height: 304px;
    position: absolute;
    -webkit-box-shadow: 0 6px 16px 0 rgba(6, 49, 116, 0.25);
    box-shadow: 0 6px 16px 0 rgba(6, 49, 116, 0.25);
    border-radius: 6px;
    overflow: visible;
    cursor: pointer;
    z-index: 488;
}
.case-study-hero .case-study-media-player.floating,
.case-study-hero .proven-expert-media-player.floating,
.proven-expert-reviews .case-study-media-player.floating,
.proven-expert-reviews .proven-expert-media-player.floating {
    position: fixed;
    max-width: 600px;
    max-height: 337px;
    z-index: 475;
}
.case-study-hero .case-study-media-player.floating .video-info .video-subtitle,
.case-study-hero .case-study-media-player.floating .video-info .video-title,
.case-study-hero .proven-expert-media-player.floating .video-info .video-subtitle,
.case-study-hero .proven-expert-media-player.floating .video-info .video-title,
.proven-expert-reviews .case-study-media-player.floating .video-info .video-subtitle,
.proven-expert-reviews .case-study-media-player.floating .video-info .video-title,
.proven-expert-reviews .proven-expert-media-player.floating .video-info .video-subtitle,
.proven-expert-reviews .proven-expert-media-player.floating .video-info .video-title {
    padding: 3px 6px;
}
.case-study-hero .case-study-media-player.floating .video-info .video-title,
.case-study-hero .proven-expert-media-player.floating .video-info .video-title,
.proven-expert-reviews .case-study-media-player.floating .video-info .video-title,
.proven-expert-reviews .proven-expert-media-player.floating .video-info .video-title {
    font-size: 10px;
}
.case-study-hero .case-study-media-player.floating .video-info .video-subtitle,
.case-study-hero .proven-expert-media-player.floating .video-info .video-subtitle,
.proven-expert-reviews .case-study-media-player.floating .video-info .video-subtitle,
.proven-expert-reviews .proven-expert-media-player.floating .video-info .video-subtitle {
    font-size: 13px;
}
.case-study-hero .case-study-media-player .video-play-animation,
.case-study-hero .proven-expert-media-player .video-play-animation,
.proven-expert-reviews .case-study-media-player .video-play-animation,
.proven-expert-reviews .proven-expert-media-player .video-play-animation {
    -o-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 487;
}
.case-study-hero .case-study-media-player .youtube-play,
.case-study-hero .proven-expert-media-player .youtube-play,
.proven-expert-reviews .case-study-media-player .youtube-play,
.proven-expert-reviews .proven-expert-media-player .youtube-play {
    -o-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: 488;
    top: 50%;
    left: 50%;
    margin-top: -35px;
    margin-left: -50px;
}
.case-study-hero .case-study-media-player .video-controls,
.case-study-hero .proven-expert-media-player .video-controls,
.proven-expert-reviews .case-study-media-player .video-controls,
.proven-expert-reviews .proven-expert-media-player .video-controls {
    color: #1c2c3e;
    display: none;
    position: absolute;
    text-align: center;
    top: -9px;
    right: -26px;
    z-index: 495;
}
.case-study-hero .case-study-media-player .video-controls > div,
.case-study-hero .proven-expert-media-player .video-controls > div,
.proven-expert-reviews .case-study-media-player .video-controls > div,
.proven-expert-reviews .proven-expert-media-player .video-controls > div {
    padding: 0;
}
.case-study-hero .case-study-media-player .video-controls .video-close,
.case-study-hero .case-study-media-player .video-controls .video-maximize,
.case-study-hero .proven-expert-media-player .video-controls .video-close,
.case-study-hero .proven-expert-media-player .video-controls .video-maximize,
.proven-expert-reviews .case-study-media-player .video-controls .video-close,
.proven-expert-reviews .case-study-media-player .video-controls .video-maximize,
.proven-expert-reviews .proven-expert-media-player .video-controls .video-close,
.proven-expert-reviews .proven-expert-media-player .video-controls .video-maximize {
    width: 20px;
    height: 20px;
    font-size: 24px;
    font-weight: 300;
    line-height: 20px;
    border-radius: 100%;
}
.case-study-hero .case-study-media-player .video-controls .video-close:before,
.case-study-hero .case-study-media-player .video-controls .video-maximize:before,
.case-study-hero .proven-expert-media-player .video-controls .video-close:before,
.case-study-hero .proven-expert-media-player .video-controls .video-maximize:before,
.proven-expert-reviews .case-study-media-player .video-controls .video-close:before,
.proven-expert-reviews .case-study-media-player .video-controls .video-maximize:before,
.proven-expert-reviews .proven-expert-media-player .video-controls .video-close:before,
.proven-expert-reviews .proven-expert-media-player .video-controls .video-maximize:before {
    display: inline-block;
    width: 100%;
    height: 100%;
}
.case-study-hero .case-study-media-player .video-controls .video-close:hover:before,
.case-study-hero .case-study-media-player .video-controls .video-maximize:hover:before,
.case-study-hero .proven-expert-media-player .video-controls .video-close:hover:before,
.case-study-hero .proven-expert-media-player .video-controls .video-maximize:hover:before,
.proven-expert-reviews .case-study-media-player .video-controls .video-close:hover:before,
.proven-expert-reviews .case-study-media-player .video-controls .video-maximize:hover:before,
.proven-expert-reviews .proven-expert-media-player .video-controls .video-close:hover:before,
.proven-expert-reviews .proven-expert-media-player .video-controls .video-maximize:hover:before {
    color: #1c2c3e;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.case-study-hero .case-study-media-player .video-controls .video-maximize:before,
.case-study-hero .proven-expert-media-player .video-controls .video-maximize:before,
.proven-expert-reviews .case-study-media-player .video-controls .video-maximize:before,
.proven-expert-reviews .proven-expert-media-player .video-controls .video-maximize:before {
    font-weight: 600;
    font-size: 16px;
}
.case-study-hero .case-study-media-player .video-player,
.case-study-hero .case-study-media-player .video-player iframe,
.case-study-hero .proven-expert-media-player .video-player,
.case-study-hero .proven-expert-media-player .video-player iframe,
.proven-expert-reviews .case-study-media-player .video-player,
.proven-expert-reviews .case-study-media-player .video-player iframe,
.proven-expert-reviews .proven-expert-media-player .video-player,
.proven-expert-reviews .proven-expert-media-player .video-player iframe {
    height: 100%;
    width: 100%;
}
.case-study-hero .case-study-media-player .video-poster,
.case-study-hero .proven-expert-media-player .video-poster,
.proven-expert-reviews .case-study-media-player .video-poster,
.proven-expert-reviews .proven-expert-media-player .video-poster {
    z-index: 485;
}
.case-study-hero .case-study-media-player .video-speakers,
.case-study-hero .proven-expert-media-player .video-speakers,
.proven-expert-reviews .case-study-media-player .video-speakers,
.proven-expert-reviews .proven-expert-media-player .video-speakers {
    z-index: 490;
}
.case-study-hero .case-study-media-player.is-playing .video-info,
.case-study-hero .case-study-media-player.is-playing .video-play-animation,
.case-study-hero .case-study-media-player.is-playing .video-speakers,
.case-study-hero .case-study-media-player.is-playing .youtube-play-wrapper,
.case-study-hero .proven-expert-media-player.is-playing .video-info,
.case-study-hero .proven-expert-media-player.is-playing .video-play-animation,
.case-study-hero .proven-expert-media-player.is-playing .video-speakers,
.case-study-hero .proven-expert-media-player.is-playing .youtube-play-wrapper,
.proven-expert-reviews .case-study-media-player.is-playing .video-info,
.proven-expert-reviews .case-study-media-player.is-playing .video-play-animation,
.proven-expert-reviews .case-study-media-player.is-playing .video-speakers,
.proven-expert-reviews .case-study-media-player.is-playing .youtube-play-wrapper,
.proven-expert-reviews .proven-expert-media-player.is-playing .video-info,
.proven-expert-reviews .proven-expert-media-player.is-playing .video-play-animation,
.proven-expert-reviews .proven-expert-media-player.is-playing .video-speakers,
.proven-expert-reviews .proven-expert-media-player.is-playing .youtube-play-wrapper {
    display: none;
}
.case-study-hero .proven-expert-media,
.proven-expert-reviews .proven-expert-media {
    width: 50%;
    height: 234px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-item-align: center;
    align-self: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.case-study-hero .proven-expert-media-player,
.proven-expert-reviews .proven-expert-media-player {
    width: 415px;
    height: 234px;
}
@media screen and (max-width: 1024px) {
    .case-study-hero .case-study-summary,
    .proven-expert-reviews .case-study-summary {
        padding-left: 50px;
    }
}
@media screen and (max-width: 960px) {
    .case-study-hero .container,
    .proven-expert-reviews .container {
        display: block;
        position: relative;
        z-index: 5;
    }
    .case-study-hero .container .case-study-media,
    .case-study-hero .container .case-study-summary,
    .proven-expert-reviews .container .case-study-media,
    .proven-expert-reviews .container .case-study-summary {
        display: block;
    }
    .case-study-hero .container .case-study-media,
    .proven-expert-reviews .container .case-study-media {
        width: 100%;
    }
    .case-study-hero .container .case-study-media .case-study-media-player,
    .proven-expert-reviews .container .case-study-media .case-study-media-player {
        position: relative;
        margin: 0 auto;
    }
    .case-study-hero .container .case-study-summary,
    .proven-expert-reviews .container .case-study-summary {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        width: 100%;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        padding: 0;
        margin-top: 40px;
    }
    .case-study-hero .container .case-study-summary h3,
    .proven-expert-reviews .container .case-study-summary h3 {
        width: 100%;
        text-align: center;
    }
    .case-study-hero .container .case-study-summary .summary-statement,
    .proven-expert-reviews .container .case-study-summary .summary-statement {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        display: block;
        padding: 0 15px;
    }
    .case-study-hero .container .case-study-summary .summary-statement:first-child,
    .proven-expert-reviews .container .case-study-summary .summary-statement:first-child {
        padding-left: 0;
    }
    .case-study-hero .container .case-study-summary .summary-statement .description,
    .case-study-hero .container .case-study-summary .summary-statement .icon,
    .proven-expert-reviews .container .case-study-summary .summary-statement .description,
    .proven-expert-reviews .container .case-study-summary .summary-statement .icon {
        text-align: center;
    }
    .case-study-hero .container .case-study-summary .summary-statement .icon,
    .proven-expert-reviews .container .case-study-summary .summary-statement .icon {
        margin-bottom: 15px;
    }
    .case-study-hero .container .case-study-summary .summary-statement .icon i,
    .proven-expert-reviews .container .case-study-summary .summary-statement .icon i {
        font-size: 36px;
    }
}
@media screen and (max-width: 660px) {
    .case-study-hero,
    .proven-expert-reviews {
        overflow: visible;
    }
    .case-study-hero .container .case-study-media .video-poster,
    .case-study-hero .container .case-study-media .video-speakers,
    .proven-expert-reviews .container .case-study-media .video-poster,
    .proven-expert-reviews .container .case-study-media .video-speakers {
        display: block;
    }
    .case-study-hero .container .case-study-summary,
    .proven-expert-reviews .container .case-study-summary {
        display: block;
        text-align: center;
    }
    .case-study-hero .container .case-study-summary:before,
    .proven-expert-reviews .container .case-study-summary:before {
        display: none;
    }
    .case-study-hero .container .case-study-summary .summary-statement,
    .proven-expert-reviews .container .case-study-summary .summary-statement {
        display: block;
    }
    .case-study-hero .container .case-study-summary .summary-statement .icon i:before,
    .proven-expert-reviews .container .case-study-summary .summary-statement .icon i:before {
        font-size: 30px;
    }
    .case-study-hero .container .case-study-summary .summary-statement .description,
    .proven-expert-reviews .container .case-study-summary .summary-statement .description {
        width: 100%;
        max-width: 450px;
        margin: 0 auto !important;
    }
}
.proven-expert-reviews .proven-expert-widget {
    width: 50%;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-item-align: center;
    align-self: center;
}
.proven-expert-reviews .proven-expert-widget a {
    width: 100%;
    margin: 0 auto;
}
@media screen and (max-width: 960px) {
    .proven-expert-reviews .container {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }
    .proven-expert-reviews .container .proven-expert-widget {
        margin-bottom: 24px;
    }
    .proven-expert-reviews .container .proven-expert-widget a {
        max-width: 415px;
    }
}
@media screen and (max-width: 660px) {
    .proven-expert-reviews .proven-expert-media,
    .proven-expert-reviews .proven-expert-widget {
        width: 100%;
        max-height: 300px;
    }
    .proven-expert-reviews .proven-expert-media {
        width: 75%;
    }
    .proven-expert-reviews .proven-expert-media .proven-expert-media-player {
        width: 100%;
        height: 0;
        padding-top: 56.25%;
    }
    .proven-expert-reviews .proven-expert-media .proven-expert-media-player .video-player,
    .proven-expert-reviews .proven-expert-media .proven-expert-media-player .youtube-play-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        margin-bottom: 30px;
    }
}
@media screen and (max-width: 530px) {
    .proven-expert-reviews .proven-expert-media {
        width: 100%;
    }
}
@media screen and (max-width: 450px) {
    .proven-expert-reviews .proven-expert-widget {
        margin-bottom: 0 !important;
    }
}
.case-study-section[class*="bg-"] {
    padding-top: 60px;
    padding-bottom: 60px;
}
.case-study-section .content p.p1 {
    text-align: center;
}
.case-study-section .content img.aligncenter {
    display: block;
    margin: 0 auto;
}
.case-study-section .column {
    -ms-flex-item-align: center;
    align-self: center;
    -ms-flex-line-pack: center;
    align-content: center;
}
.case-study-section .column img {
    display: block;
    width: 100%;
    max-width: 420px;
    height: auto;
}
@media screen and (max-width: 660px) {
    .case-study-section .column img {
        max-width: 320px;
    }
}
.case-study-problems,
.case-study-solutions {
    position: relative;
}
.case-study-problems .case-study-problem.column,
.case-study-problems .case-study-solution.column,
.case-study-solutions .case-study-problem.column,
.case-study-solutions .case-study-solution.column {
    -ms-flex-preferred-size: 32%;
    flex-basis: 32%;
}
.case-study-problems .case-study-problem .icon,
.case-study-problems .case-study-problem .image,
.case-study-problems .case-study-solution .icon,
.case-study-problems .case-study-solution .image,
.case-study-solutions .case-study-problem .icon,
.case-study-solutions .case-study-problem .image,
.case-study-solutions .case-study-solution .icon,
.case-study-solutions .case-study-solution .image {
    text-align: center;
}
.case-study-problems .case-study-problem .icon img,
.case-study-problems .case-study-problem .image img,
.case-study-problems .case-study-solution .icon img,
.case-study-problems .case-study-solution .image img,
.case-study-solutions .case-study-problem .icon img,
.case-study-solutions .case-study-problem .image img,
.case-study-solutions .case-study-solution .icon img,
.case-study-solutions .case-study-solution .image img {
    display: block;
    max-width: 60px;
    height: auto;
    margin: 0 auto;
}
.case-study-problems .case-study-problem .icon [class*="glyph-"],
.case-study-problems .case-study-problem .icon [class*="icon-"],
.case-study-problems .case-study-problem .icon i,
.case-study-problems .case-study-problem .image [class*="glyph-"],
.case-study-problems .case-study-problem .image [class*="icon-"],
.case-study-problems .case-study-problem .image i,
.case-study-problems .case-study-solution .icon [class*="glyph-"],
.case-study-problems .case-study-solution .icon [class*="icon-"],
.case-study-problems .case-study-solution .icon i,
.case-study-problems .case-study-solution .image [class*="glyph-"],
.case-study-problems .case-study-solution .image [class*="icon-"],
.case-study-problems .case-study-solution .image i,
.case-study-solutions .case-study-problem .icon [class*="glyph-"],
.case-study-solutions .case-study-problem .icon [class*="icon-"],
.case-study-solutions .case-study-problem .icon i,
.case-study-solutions .case-study-problem .image [class*="glyph-"],
.case-study-solutions .case-study-problem .image [class*="icon-"],
.case-study-solutions .case-study-problem .image i,
.case-study-solutions .case-study-solution .icon [class*="glyph-"],
.case-study-solutions .case-study-solution .icon [class*="icon-"],
.case-study-solutions .case-study-solution .icon i,
.case-study-solutions .case-study-solution .image [class*="glyph-"],
.case-study-solutions .case-study-solution .image [class*="icon-"],
.case-study-solutions .case-study-solution .image i {
    text-align: center;
    font-size: 0;
}
.case-study-problems .case-study-problem .icon [class*="glyph-"]:before,
.case-study-problems .case-study-problem .icon [class*="icon-"]:before,
.case-study-problems .case-study-problem .icon i:before,
.case-study-problems .case-study-problem .image [class*="glyph-"]:before,
.case-study-problems .case-study-problem .image [class*="icon-"]:before,
.case-study-problems .case-study-problem .image i:before,
.case-study-problems .case-study-solution .icon [class*="glyph-"]:before,
.case-study-problems .case-study-solution .icon [class*="icon-"]:before,
.case-study-problems .case-study-solution .icon i:before,
.case-study-problems .case-study-solution .image [class*="glyph-"]:before,
.case-study-problems .case-study-solution .image [class*="icon-"]:before,
.case-study-problems .case-study-solution .image i:before,
.case-study-solutions .case-study-problem .icon [class*="glyph-"]:before,
.case-study-solutions .case-study-problem .icon [class*="icon-"]:before,
.case-study-solutions .case-study-problem .icon i:before,
.case-study-solutions .case-study-problem .image [class*="glyph-"]:before,
.case-study-solutions .case-study-problem .image [class*="icon-"]:before,
.case-study-solutions .case-study-problem .image i:before,
.case-study-solutions .case-study-solution .icon [class*="glyph-"]:before,
.case-study-solutions .case-study-solution .icon [class*="icon-"]:before,
.case-study-solutions .case-study-solution .icon i:before,
.case-study-solutions .case-study-solution .image [class*="glyph-"]:before,
.case-study-solutions .case-study-solution .image [class*="icon-"]:before,
.case-study-solutions .case-study-solution .image i:before {
    font-size: 32px;
    color: #4d92ff;
}
.case-study-problems .case-study-problem .image,
.case-study-problems .case-study-solution .image,
.case-study-solutions .case-study-problem .image,
.case-study-solutions .case-study-solution .image {
    height: 32px;
}
.case-study-problems .case-study-problem .image img,
.case-study-problems .case-study-solution .image img,
.case-study-solutions .case-study-problem .image img,
.case-study-solutions .case-study-solution .image img {
    display: block;
    width: auto;
    height: 32px;
    margin: 0 auto;
}
.case-study-problems .case-study-problem .title,
.case-study-problems .case-study-solution .title,
.case-study-solutions .case-study-problem .title,
.case-study-solutions .case-study-solution .title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    max-width: 280px;
    min-height: 54px;
    margin: 1em auto;
    text-align: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
@media screen and (max-width: 660px) {
    .case-study-problems .case-study-problem .title,
    .case-study-problems .case-study-solution .title,
    .case-study-solutions .case-study-problem .title,
    .case-study-solutions .case-study-solution .title {
        min-height: auto;
    }
}
.case-study-problems .case-study-problem .icon [class*="glyph-"]:before,
.case-study-problems .case-study-problem .icon [class*="icon-"]:before,
.case-study-problems .case-study-problem .icon i:before {
    color: #d64956;
}
.case-study-problems:before {
    display: block;
    font-family: glyphs;
    content: "\E96A";
    width: 120px;
    height: 50px;
    line-height: 50px;
    position: absolute;
    bottom: -38px;
    left: 50%;
    margin-left: -60px;
    text-align: center;
    font-size: 70px;
    color: #e5eeff;
}
@media screen and (max-width: 660px) {
    .case-study-problems:before {
        font-size: 45px;
        height: 30px;
        line-height: 30px;
        bottom: -27px;
    }
    .case-study-problems.down:before {
        bottom: -26px;
    }
}
.case-study-result,
.stat-feature {
    text-align: center;
    padding: 0 30px;
    vertical-align: middle;
    margin: 0 !important;
}
.case-study-result .result-data,
.case-study-result .stat-value,
.stat-feature .result-data,
.stat-feature .stat-value {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin: 0 0 8px;
    font-size: 66px;
    line-height: 0.875em;
    font-weight: 800;
    color: #3376de;
    white-space: nowrap;
}
.case-study-result .result-data span.symbol,
.case-study-result .stat-value span.symbol,
.stat-feature .result-data span.symbol,
.stat-feature .stat-value span.symbol {
    display: inline-block;
    font-size: 0.625em;
}
.case-study-result .result-data span:first-of-type,
.case-study-result .stat-value span:first-of-type,
.stat-feature .result-data span:first-of-type,
.stat-feature .stat-value span:first-of-type {
    margin-right: 3px;
}
.case-study-result .result-data span:last-of-type,
.case-study-result .stat-value span:last-of-type,
.stat-feature .result-data span:last-of-type,
.stat-feature .stat-value span:last-of-type {
    margin-left: 3px;
}
.case-study-result .result-description,
.case-study-result .stat-description,
.stat-feature .result-description,
.stat-feature .stat-description {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    max-width: 210px;
    min-height: 35px;
    margin: 0 auto;
    color: #1c2c3e;
    text-transform: uppercase;
    font-size: 14px;
    line-height: 16px;
    font-weight: 800;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.case-study-result.no-data,
.stat-feature.no-data {
    padding-top: 0;
}
.case-study-result.no-data .result-description,
.stat-feature.no-data .result-description {
    margin: 0 0 12px;
    font-size: 22px;
    line-height: 1.1em;
    font-weight: 600;
    color: #3376de;
    text-transform: capitalize;
}
.case-study-results .results-showcase,
.case-study-results .stats-showcase {
    max-width: 960px;
    margin: 0 auto;
    text-align: center;
}
.case-study-results .results-showcase .case-study-result,
.case-study-results .results-showcase .stat-feature,
.case-study-results .stats-showcase .case-study-result,
.case-study-results .stats-showcase .stat-feature {
    display: inline-block;
    width: auto;
}
.case-study-results.small .results-showcase .case-study-result {
    padding: 0 20px;
}
.case-study-results.small .results-showcase .case-study-result .result-data {
    margin: 0 0 8px;
    font-size: 38px;
    line-height: 0.5em;
}
.case-study-results.small .results-showcase .case-study-result .result-description {
    font-size: 10px;
    line-height: 14px;
}
@media screen and (max-width: 760px) {
    .case-study-results .results-showcase .case-study-result {
        margin-bottom: 30px !important;
    }
    .case-study-results .results-showcase .case-study-result:last-child {
        margin-bottom: 0 !important;
    }
}
@media screen and (max-width: 720px) {
    .case-study-results {
        padding-top: 0;
    }
    .case-study-results .results-showcase > .wp-block-group__inner-container {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
    }
    .case-study-results .results-showcase > .wp-block-group__inner-container .case-study-result {
        width: 85%;
        margin-right: 0;
        -ms-flex-item-align: center;
        align-self: center;
    }
    .case-study-results .results-showcase > .wp-block-group__inner-container .case-study-result .result-description {
        max-width: 90%;
    }
}
@media screen and (max-width: 660px) {
    .case-study-results .results-showcase .case-study-result {
        display: block;
        width: 100%;
        padding: 0 10px;
    }
    .case-study-results .results-showcase .case-study-result .result-data {
        font-size: 48px;
    }
    .case-study-results .results-showcase .case-study-result .result-description {
        max-width: calc(100% - 60px);
        margin-left: auto !important;
        margin-right: auto !important;
    }
}
.case-study-testimonials {
    padding-top: 30px;
}
.case-study-testimonials blockquote {
    margin-bottom: 60px;
    padding-top: 30px;
    padding-left: 140px;
    position: relative;
}
.case-study-testimonials blockquote h4 {
    display: block;
    max-width: 400px;
    margin-bottom: 10px;
}
.case-study-testimonials blockquote p:not(.byline) {
    margin-bottom: 0.5em;
}
.case-study-testimonials blockquote p.byline {
    font-size: 18px;
    font-weight: 700;
    color: #1c2c3e;
}
.case-study-testimonials blockquote .user-portrait {
    position: absolute;
    top: 0;
    left: 20px;
}
.case-study-testimonials blockquote:after {
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    width: 150px;
    height: 12px;
    background-image: url(images/5-stars_41ea68cb.jpg);
    background-position: 0;
    right: auto;
    left: 140px;
    top: 0;
}
.case-study-testimonials blockquote:last-child {
    margin-bottom: 0;
}
@media screen and (max-width: 660px) {
    .case-study-testimonials blockquote {
        padding-left: 0;
        padding-top: 140px;
    }
    .case-study-testimonials blockquote h4 {
        max-width: 100%;
        text-align: center;
    }
    .case-study-testimonials blockquote .user-portrait {
        left: 50%;
        top: 30px;
        -o-transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
    }
    .case-study-testimonials blockquote p.byline {
        text-align: center;
    }
    .case-study-testimonials blockquote:after {
        display: block;
        width: 72px;
        left: 50%;
        margin-left: -36px;
    }
}
.case-study-trust-validation h2,
.case-study-trust-validation h3,
.case-study-trust-validation h4 {
    text-align: center;
    margin-bottom: 20px;
}
.case-study-trust-validation .logos-gallery {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    text-align: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.case-study-trust-validation .logos-gallery img {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    width: auto;
    height: 70px;
    margin: 0 15px;
}
.case-study-trust-validation .logos-gallery img:last-child {
    margin-right: 0;
}
.case-study-trust-validation.bg-medium .logos-gallery img {
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    mix-blend-mode: multiply;
}
@media screen and (max-width: 960px) {
    .case-study-trust-validation .logos-gallery {
        display: block;
    }
}
@media screen and (max-width: 660px) {
    .case-study-trust-validation .logos-gallery {
        text-align: left;
        margin-left: 8.5%;
    }
    .case-study-trust-validation .logos-gallery img {
        margin-right: 0;
        height: 90px;
    }
}
.case-study-gdpr-compliance:before {
    display: block;
    content: "";
    display: none !important;
    width: 150px;
    height: 150px;
    background-image: url(images/gdpr-badge.png);
    background-size: contain;
    background-position: 50%;
    background-repeat: no-repeat;
    position: absolute;
    top: -50px;
    left: 50%;
    margin-left: -75px;
}
.case-study-gdpr-compliance .bio {
    margin-top: 20px;
    padding-left: 20px;
    border-left: 3px solid #4d92ff;
}
.case-study-gdpr-compliance .bio h4 {
    font-size: 15px;
    margin-bottom: 0.5em;
}
.case-study-gdpr-compliance .bio p {
    font-size: 0.875em;
    font-style: italic;
}
.case-study-gdpr-compliance .column:nth-child(2) {
    position: relative;
}
.case-study-gdpr-compliance .column:nth-child(2) .bio {
    position: absolute;
    right: 0;
    bottom: 0;
}
@media screen and (max-width: 660px) {
    .case-study-gdpr-compliance:before {
        display: block;
        content: "";
        width: 100px;
        height: 100px;
        top: -30px;
        margin-left: -50px;
    }
    .case-study-gdpr-compliance .bio {
        padding-top: 20px;
        padding-left: 0;
        border-left: none;
        position: relative !important;
    }
    .case-study-gdpr-compliance .bio h4 {
        text-align: center;
    }
    .case-study-gdpr-compliance .bio:before {
        display: block;
        content: "";
        width: 80px;
        height: 3px;
        background-color: #4d92ff;
        position: absolute;
        top: 0;
        left: 50%;
        margin-left: -40px;
    }
    .case-study-gdpr-compliance .column:nth-child(2) {
        position: relative;
    }
    .case-study-gdpr-compliance .column:nth-child(2) .bio {
        position: static;
    }
}
.case-study-personality {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    -ms-flex-line-pack: center;
    align-content: center;
    padding-top: 60px !important;
    display: block;
    position: relative;
    padding-top: 140px;
    margin-top: 40px;
}
.case-study-personality .personality-portrait,
.case-study-personality .personality-statement {
    width: 50%;
}
.case-study-personality .personality-portrait {
    position: relative;
}
.case-study-personality .personality-portrait figure {
    width: 100%;
    height: 100%;
    margin-bottom: 0 !important;
    background-size: cover;
    background-position: 50%;
    position: absolute;
    top: 0;
    left: 0;
}
.case-study-personality .personality-statement {
    background-color: #1c2c3e;
    padding: 60px;
    text-align: center;
}
.case-study-personality .personality-statement h2 {
    color: #fff;
    font-size: 32px;
    margin-bottom: 10px;
}
.case-study-personality .personality-statement h4 {
    color: #4d92ff;
    font-size: 16px;
    text-transform: uppercase;
}
.case-study-personality .personality-statement .quote {
    display: block;
    padding: 0 50px;
    color: #fff;
    text-align: left;
    margin-top: 50px;
    font-size: 19px;
    line-height: 1.35em;
    margin-bottom: 20px;
}
.case-study-personality .personality-statement .quote ul {
    margin: 20px auto;
    padding-left: 22px;
}
.case-study-personality .personality-statement figure {
    text-align: right;
    margin-top: 15px;
}
.case-study-personality .personality-statement img {
    display: inline-block;
    width: 160px;
    margin-bottom: 1em;
    height: auto;
}
.case-study-personality .personality-portrait,
.case-study-personality .personality-statement {
    width: 100%;
}
.case-study-personality .personality-portrait {
    width: 180px;
    height: 180px;
    border-radius: 100%;
    overflow: hidden;
    position: absolute;
    top: -10px;
    left: 50%;
    margin-left: -90px;
}
.case-study-personality .personality-statement {
    padding-top: 130px;
}
@media screen and (max-width: 1200px) {
    .case-study-personality .personality-statement {
        padding: 40px;
    }
    .case-study-personality .personality-statement .quote {
        padding: 0;
    }
    .case-study-personality .personality-statement .signature {
        margin-bottom: 20px;
    }
}
@media screen and (max-width: 960px) {
    .case-study-personality {
        display: block;
        position: relative;
        padding-top: 140px;
        margin-top: 40px;
    }
    .case-study-personality .personality-portrait,
    .case-study-personality .personality-statement {
        width: 100%;
    }
    .case-study-personality .personality-portrait {
        width: 180px;
        height: 180px;
        border-radius: 100%;
        overflow: hidden;
        position: absolute;
        top: -10px;
        left: 50%;
        margin-left: -90px;
    }
    .case-study-personality .personality-statement {
        padding-top: 130px;
    }
}
@media screen and (max-width: 660px) {
    .case-study-personality {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }
}
.case-study-preview {
    height: auto;
    position: relative;
    -ms-flex-item-align: stretch;
    align-self: stretch;
    border: 1px solid #ccdbf5;
    -webkit-box-shadow: 4px 4px 0 0 #e5eeff, 8px 8px 0 0 #f2f6ff;
    box-shadow: 4px 4px 0 0 #e5eeff, 8px 8px 0 0 #f2f6ff;
}
.case-study-preview a {
    display: block;
}
.case-study-preview .call-to-action,
.case-study-preview .content {
    position: absolute;
    z-index: 2;
}
.case-study-preview .content {
    width: calc(100% - 40px);
    top: 20px;
    left: 20px;
}
.case-study-preview .content .title {
    font-size: 24px;
    line-height: 1.25em;
    font-weight: 700;
    color: #fff;
}
.case-study-preview .content .description {
    margin-top: 10px;
    font-size: 16px;
    font-weight: 400;
    color: #fff;
}
.case-study-preview .button-cta {
    display: block;
    width: calc(100% - 40px);
    position: absolute;
    left: 20px;
    bottom: 20px;
    z-index: 5;
}
.case-study-preview .call-to-action {
    width: 100%;
    height: auto;
    padding: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #1f65d3;
    text-align: left;
    background-color: #fff;
    left: 0;
    bottom: 0;
}
.case-study-preview .call-to-action a {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #ffa20f;
    -webkit-box-shadow: inset 0 -2px 0 0 #d57a00;
    box-shadow: inset 0 -2px 0 0 #d57a00;
    position: relative;
}
.case-study-preview .call-to-action a[class*="glyph-"]:before,
.case-study-preview .call-to-action a[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
}
.case-study-preview .call-to-action a:hover {
    color: #fff !important;
    background: #ffb238;
    -webkit-transition: all 0.2s ease-in;
    -o-transition: all 0.2s ease-in;
    transition: all 0.2s ease-in;
}
.case-study-preview .call-to-action a:before {
    display: block;
    font-family: glyphs;
    content: "\E979";
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    color: #fff;
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -30px;
    z-index: 1;
}
.case-study-preview .call-to-action a:after {
    display: block;
    content: "";
    width: 60px;
    height: 100%;
    background-color: rgba(213, 122, 0, 0.65);
    -webkit-box-shadow: inset 0 -2px 0 0 #c16e00;
    box-shadow: inset 0 -2px 0 0 #c16e00;
    position: absolute;
    top: 0;
    right: 0;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
@media screen and (max-width: 600px) {
    .case-study-preview .call-to-action a.large,
    .case-study-preview .call-to-action a.xlarge {
        font-size: 1.85rem;
    }
}
@media screen and (max-width: 500px) {
    .case-study-preview .call-to-action a.large,
    .case-study-preview .call-to-action a.medium,
    .case-study-preview .call-to-action a.xlarge {
        font-size: 1.35rem;
    }
}
@media screen and (max-width: 320px) {
    .case-study-preview .call-to-action a.large,
    .case-study-preview .call-to-action a.medium,
    .case-study-preview .call-to-action a.xlarge {
        font-size: 1.15rem;
    }
}
.case-study-preview .call-to-action:before {
    display: none;
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #ffa20f;
    -webkit-box-shadow: inset 0 -2px 0 0 #d57a00;
    box-shadow: inset 0 -2px 0 0 #d57a00;
    position: relative;
    font-family: glyphs;
    content: "\E979";
    width: 35px;
    height: 35px;
    padding: 0;
    line-height: 35px;
    text-align: center;
    position: absolute;
    top: 13px;
    right: 19px;
}
.case-study-preview .call-to-action:before[class*="glyph-"]:before,
.case-study-preview .call-to-action:before[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
}
.case-study-preview .call-to-action:before:hover {
    color: #fff !important;
    background: #ffb238;
    -webkit-transition: all 0.2s ease-in;
    -o-transition: all 0.2s ease-in;
    transition: all 0.2s ease-in;
}
.case-study-preview .call-to-action:before:before {
    display: block;
    font-family: glyphs;
    content: "\E979";
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    color: #fff;
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -30px;
    z-index: 1;
}
.case-study-preview .call-to-action:before:after {
    display: block;
    content: "";
    width: 60px;
    height: 100%;
    background-color: rgba(213, 122, 0, 0.65);
    -webkit-box-shadow: inset 0 -2px 0 0 #c16e00;
    box-shadow: inset 0 -2px 0 0 #c16e00;
    position: absolute;
    top: 0;
    right: 0;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
@media screen and (max-width: 600px) {
    .case-study-preview .call-to-action:before.large,
    .case-study-preview .call-to-action:before.xlarge {
        font-size: 1.85rem;
    }
}
@media screen and (max-width: 500px) {
    .case-study-preview .call-to-action:before.large,
    .case-study-preview .call-to-action:before.medium,
    .case-study-preview .call-to-action:before.xlarge {
        font-size: 1.35rem;
    }
}
@media screen and (max-width: 320px) {
    .case-study-preview .call-to-action:before.large,
    .case-study-preview .call-to-action:before.medium,
    .case-study-preview .call-to-action:before.xlarge {
        font-size: 1.15rem;
    }
}
.case-study-preview .background-image {
    display: block;
    width: 100%;
    height: auto;
    overflow: hidden;
    position: relative;
}
.case-study-preview .background-image:before {
    display: block;
    content: "";
    background-image: -o-linear-gradient(rgba(28, 44, 62, 0.925), rgba(28, 44, 62, 0.375));
    background-image: -webkit-linear-gradient(rgba(28, 44, 62, 0.925), rgba(28, 44, 62, 0.375));
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(28, 44, 62, 0.925)), to(rgba(28, 44, 62, 0.375)));
    background-image: linear-gradient(rgba(28, 44, 62, 0.925), rgba(28, 44, 62, 0.375));
    width: 100%;
    height: 100%;
    opacity: 1;
    position: absolute;
    top: 0;
    left: 0;
}
.case-study-preview .background-image img {
    display: block;
    width: 100%;
    height: auto;
}
.case-studies-previews .preview-grid > .wp-block-group__inner-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    -ms-flex-line-pack: center;
    align-content: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.case-studies-previews .preview-grid > .wp-block-group__inner-container .wp-block-image {
    width: 20%;
    margin-left: 12px;
    margin-right: 12px;
    cursor: pointer;
}
.section-with-background {
    position: relative;
    height: auto;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}
.section-with-background .content {
    -o-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    width: 480px;
    height: auto;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 5;
}
.section-with-background .subtitle,
.section-with-background .title,
.section-with-background p {
    color: #fff;
}
.section-with-background .background-image {
    width: 100%;
    height: 350px;
    position: relative;
    overflow: hidden;
    background-color: #1f65d3;
}
.section-with-background .background-image:before {
    display: block;
    content: "";
    width: 100%;
    height: 100%;
    background-color: rgba(31, 101, 211, 0.75);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
}
.section-with-background .background-image img {
    display: block;
    width: 100%;
    height: auto;
    position: absolute;
    left: 50%;
    top: 50%;
    -o-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    mix-blend-mode: multiply;
}
@media screen and (max-width: 860px) {
    .section-with-background .background-image {
        height: 320px;
    }
    .section-with-background .background-image img {
        width: auto;
        height: 100%;
    }
}
@media screen and (max-width: 660px) {
    .section-with-background {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }
    .section-with-background .background-image {
        height: 420px;
    }
    .section-with-background .content {
        width: calc(100% - 80px);
    }
}
.call-to-action,
.cta-disclaimer {
    text-align: center;
}
.cta-disclaimer {
    font-size: 16px;
    font-weight: 600;
    margin-top: 10px;
}
.cta-disclaimer:before {
    display: inline;
    font-family: glyphs;
    content: "\E91D";
    font-size: 11px;
    margin-right: 6px;
    color: #35905d;
}
.case-study-speakers > .wp-block-group__inner-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}
.case-study-speakers > .wp-block-group__inner-container .case-study-speaker {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 0px;
    flex: 1 1 0;
    display: block;
    max-width: 180px;
    padding: 0 1em;
    text-align: center;
}
.case-study-speakers > .wp-block-group__inner-container .case-study-speaker div,
.case-study-speakers > .wp-block-group__inner-container .case-study-speaker figure {
    display: block;
    float: none;
}
.case-study-speakers > .wp-block-group__inner-container .case-study-speaker figure {
    border-radius: 100%;
    overflow: hidden;
}
.case-study-speakers > .wp-block-group__inner-container .case-study-speaker div {
    margin-top: 1em;
    font-size: 14px;
    text-transform: uppercase;
    color: #1c2c3e;
    font-weight: 800;
}
.case-study-speakers > .wp-block-group__inner-container .case-study-speaker div p {
    font-size: 14px;
}
@media screen and (max-width: 720px) {
    .case-study-speakers > .wp-block-group__inner-container {
        display: block;
    }
    .case-study-speakers > .wp-block-group__inner-container .case-study-speaker {
        margin: 0 auto 30px;
        text-align: center;
    }
}
.feedback-form .stcfeed-form textarea {
    display: block;
    font-family: soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    width: 100%;
    min-height: 120px;
    margin: 30px auto;
    font-size: 16px;
    line-height: 1.2em;
    padding: 12px;
    color: #1c2c3e;
    background-color: #e5eeff;
    border-color: #c1cfe6;
    border-radius: 3px;
    outline: none;
    resize: none;
}
.feedback-form .stcfeed-buttonsrow {
    clear: both;
    text-align: center;
}
.feedback-form .stcfeed-buttonsrow:after {
    display: block;
    visibility: hidden;
    clear: both;
    height: 0;
    content: " ";
    font-size: 0;
}
.feedback-form .stcfeed-buttonsrow .stcfeed-submit {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #ffa20f;
    border: none;
    -webkit-box-shadow: inset 0 -3px 0 0 #db8600;
    box-shadow: inset 0 -3px 0 0 #db8600;
    font-family: soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
}
.feedback-form .stcfeed-buttonsrow .stcfeed-submit[class*="glyph-"]:before,
.feedback-form .stcfeed-buttonsrow .stcfeed-submit[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
    color: #d57a00;
}
.feedback-form .stcfeed-buttonsrow .stcfeed-submit:hover {
    color: #fff !important;
    background: #ef9300;
    -webkit-box-shadow: inset 0 -3px 0 0 #d18000;
    box-shadow: inset 0 -3px 0 0 #d18000;
    -webkit-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.price-page h1,
.price-page h2 {
    font: 700 1.6rem/35px soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    letter-spacing: -0.52px;
    color: #1c2c3e;
    margin-bottom: 90px;
    text-align: center;
}
.price-page h2 {
    margin-bottom: 3rem;
}
.price-page .values-slider {
    margin-bottom: 140px;
}
.price-page .button-link:hover {
    cursor: pointer;
}
.customers-slider .login-process .password-suggestions,
.customers-slider .ui-popup,
.login-process .customers-slider .password-suggestions {
    display: none;
    text-align: center;
    top: 40px;
    right: -15px;
    width: 426px;
    white-space: inherit;
}
.customers-slider .login-process .password-suggestions p,
.customers-slider .ui-popup p,
.login-process .customers-slider .password-suggestions p {
    overflow-wrap: break-word;
    margin-bottom: 1rem;
}
.customers-slider .login-process .password-suggestions .icon,
.customers-slider .ui-popup .icon,
.login-process .customers-slider .password-suggestions .icon {
    margin-bottom: 0.5rem;
}
.customers-slider .login-process .password-suggestions .icon-phone:before,
.customers-slider .ui-popup .icon-phone:before,
.login-process .customers-slider .password-suggestions .icon-phone:before {
    content: "\E968";
    font-family: glyphs;
}
.price-table {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 80px;
    position: relative;
}
.price-table .note-popup {
    left: 0;
    position: absolute;
    top: 120px;
    width: 150px;
    left: -110px;
    z-index: 10;
}
.price-table .ui-select {
    margin-top: 1rem;
    margin-bottom: 2rem;
}
.price-table.overlay:after {
    top: -120px;
}
.price-table-item .button-primary {
    height: 45px;
    line-height: 45px;
    padding-top: 0;
    padding-bottom: 0;
}
.price-table-item .top {
    min-height: 480px;
    width: 100%;
    -webkit-box-shadow: 0 3px 6px rgba(63, 114, 193, 0.07);
    box-shadow: 0 3px 6px rgba(63, 114, 193, 0.07);
    border: 1px solid #ccdbf5;
    padding: 65px 38px 38px;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    position: relative;
}
.price-table-item .top [class^="button-"] {
    width: 100%;
    margin-top: auto;
}
.price-table-item:first-child {
    width: calc(33.33% - 6px);
}
.price-table-item:first-child .top {
    border-radius: 3px 0 0 3px;
    border-left: 1px solid #ccdbf5;
    border-top: 1px solid #ccdbf5;
    border-bottom: 1px solid #ccdbf5;
}
.price-table-item:first-child .top:after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    width: 21px;
    background: transparent -webkit-gradient(linear, left top, right top, from(#fff), to(#e8f0fd)) 0 0 no-repeat padding-box;
    background: transparent -webkit-linear-gradient(left, #fff, #e8f0fd) 0 0 no-repeat padding-box;
    background: transparent -o-linear-gradient(left, #fff 0, #e8f0fd 100%) 0 0 no-repeat padding-box;
    background: transparent linear-gradient(90deg, #fff, #e8f0fd) 0 0 no-repeat padding-box;
}
.price-table-item:last-child {
    width: calc(33.33% - 6px);
}
.price-table-item:last-child .top {
    border-radius: 0 3px 3px 0;
    border: 1px solid #ccdbf5;
    border-left: none;
}
.price-table-item:last-child .top:before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 21px;
    background: transparent -webkit-gradient(linear, right top, left top, from(#fff), to(#e8f0fd)) 0 0 no-repeat padding-box;
    background: transparent -webkit-linear-gradient(right, #fff, #e8f0fd) 0 0 no-repeat padding-box;
    background: transparent -o-linear-gradient(right, #fff 0, #e8f0fd 100%) 0 0 no-repeat padding-box;
    background: transparent linear-gradient(-90deg, #fff, #e8f0fd) 0 0 no-repeat padding-box;
}
.price-table-item.popular {
    width: calc(33.33% + 12px);
}
.price-table-item.popular .top {
    min-height: 510px;
    margin-top: -15px;
    padding-top: 80px;
    padding-bottom: 53px;
    border-color: #3b81f0;
    border-radius: 3px;
    -webkit-box-shadow: 0 3px 6px rgba(63, 114, 193, 0.07);
    box-shadow: 0 3px 6px rgba(63, 114, 193, 0.07);
}
.price-table-item .top p {
    color: #385575;
    margin-bottom: 2rem;
    overflow: hidden;
    line-height: 21px;
    max-height: 63px;
    text-align: left;
    margin-right: -1em;
    padding-right: 1em;
}
.overlay:after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    -webkit-transition: 0.2s ease-in-out;
    -o-transition: 0.2s ease-in-out;
    transition: 0.2s ease-in-out;
    background-color: hsla(0, 0%, 100%, 0);
    z-index: -1;
}
.overlay.show:after {
    background-color: hsla(0, 0%, 100%, 0.8);
    z-index: 1;
}
.table-item-title {
    text-align: center;
    font: 900 normal 1.1em/1.33 soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    letter-spacing: 2.16px;
    color: #082446;
    text-transform: uppercase;
    margin-bottom: 1rem;
}
.popular .table-item-title {
    -webkit-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
}
.popular [class^="button-"] {
    margin-bottom: -17px;
}
.popular p {
    -webkit-transform: translateY(10px);
    -o-transform: translateY(10px);
    transform: translateY(10px);
}
.price {
    text-align: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline;
    margin-bottom: 8px;
}
.price .number {
    text-align: center;
    font: 300 normal 3.4rem/1.33 soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    letter-spacing: -1.08px;
    color: #1c2c3e;
    opacity: 1;
    margin: 0 3px;
}
.price .currency {
    display: inline-block;
    text-align: center;
    font: 400 1.4rem/1.36 soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    letter-spacing: -0.44px;
    color: #082446;
    opacity: 1;
    -ms-flex-item-align: start;
    align-self: flex-start;
    margin-top: 10px;
}
.price .month {
    color: #082446;
    font: 400 1.25rem/1.36 soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
}
.price.old {
    color: #d64956;
    margin-bottom: -10px;
}
.price.old .number {
    color: inherit;
    font-size: 1.6rem;
    font-weight: 700;
    position: relative;
}
.price.old .number:after {
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%) rotate(-30deg);
    -o-transform: translate(-50%, -50%) rotate(-30deg);
    transform: translate(-50%, -50%) rotate(-30deg);
    content: "";
    position: absolute;
    display: block;
    height: 2px;
    width: calc(100% + 5px);
    background-color: #d64956;
    -webkit-box-shadow: 0 0 6px #fff;
    box-shadow: 0 0 6px #fff;
}
.price.old .currency {
    color: inherit;
    font-size: 0.7rem;
    margin-top: 2px;
}
.price.old .month {
    color: inherit;
    font-size: 0.55rem;
}
.ui-select.contacts {
    display: none;
}
.benefits {
    margin-top: 38px;
    padding: 0 50px;
}
.benefits.popular {
    margin-top: 26px;
}
.benefits ul {
    list-style: none;
    overflow: hidden;
    margin: 0 0 1rem;
    -webkit-transition: 0.2s ease-in-out;
    -o-transition: 0.2s ease-in-out;
    transition: 0.2s ease-in-out;
}
.benefits ul li {
    font: 400 14px/1.33 soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    position: relative;
    padding-bottom: 10px;
    padding-left: 25px;
}
.benefits ul li.minus:before,
.benefits ul li.plus:before {
    position: absolute;
    left: 0;
    top: 0;
    content: "\E91D";
    font-family: glyphs;
    color: #35905d;
    margin-right: 8px;
}
.benefits ul li.minus {
    color: #aec4ea;
}
.benefits ul li.minus:before {
    content: "\E91E";
    color: #aec4ea;
}
.benefits.active ul {
    max-height: 300px !important;
}
.benefits .btn-wraper {
    padding: 0 0 0 50px;
}
.benefits .btn-wraper .link {
    position: relative;
    padding: 0 0 0 25px;
}
.benefits .btn-wraper .link:before {
    position: absolute;
    left: 0;
    top: 0;
    content: "\E93A";
    font-family: glyphs;
    margin-right: 8px;
}
.benefits-list {
    width: 100%;
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3;
    display: none;
    margin-top: 1.5rem;
}
.benefits-list .benefits-toggle {
    display: block;
}
.benefits-list .btn-wraper,
.benefits-list ul {
    padding: 0;
}
.benefits-list .benefits {
    max-height: 0;
    overflow: hidden;
    -webkit-transition: 0.2s ease-in-out;
    -o-transition: 0.2s ease-in-out;
    transition: 0.2s ease-in-out;
    margin: 0;
    padding: 0;
}
.benefits-list .benefits.is-open {
    max-height: 500px;
    margin-top: 1rem;
}
.link {
    font: 400 normal 0.87rem/1.4 soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    letter-spacing: 0;
    -webkit-transition: 0.2s ease-in-out;
    -o-transition: 0.2s ease-in-out;
    transition: 0.2s ease-in-out;
    color: #3b81f0;
}
.link:hover {
    cursor: pointer;
}
img.klicky {
    position: absolute;
    top: 0;
    left: 50%;
    margin-top: -20px;
    -webkit-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}
img.klicky.arms {
    width: 102px;
    height: auto;
}
img.klicky.wrench {
    width: 84px;
    height: 131px;
}
img.klicky.zen {
    width: 67px;
    height: 144px;
}
.top-badge {
    width: 100px;
    height: 100px;
    position: absolute;
    right: 0;
    top: 0;
    overflow: hidden;
}
.top-badge-text {
    margin: -12px 12px;
    top: 50%;
    left: 50%;
    position: absolute;
    display: inline-block;
    background-color: #3b81f0;
    color: #fff;
    text-align: center;
    font: 900 15px/19px soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    letter-spacing: 0.3px;
    text-transform: uppercase;
    border: 1px solid #3376de;
    padding: 6px 0;
    width: 300px;
    width: 142px;
    -webkit-transform: translate(-50%, -50%) rotate(45deg);
    -o-transform: translate(-50%, -50%) rotate(45deg);
    transform: translate(-50%, -50%) rotate(45deg);
}
.testimonials {
    position: relative;
}
.testimonials-slider {
    max-width: 810px;
    width: 100%;
    margin: 0 auto 3rem;
}
.testimonials-slider .slick-list {
    min-height: 300px;
}
.testimonials-slider .button-play-wraper {
    z-index: 10;
    width: 450px;
    position: absolute;
    bottom: 0;
    -webkit-transform: translateY(50%);
    -o-transform: translateY(50%);
    transform: translateY(50%);
    -webkit-box-align: center;
    -ms-flex-align: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    left: 120px;
}
.testimonials-slider .button-play-wraper:hover {
    cursor: pointer;
}
.testimonials-slider .button-play-wraper:hover .icon:before {
    color: #d57a00;
}
.testimonials-slider .button-play-wraper .icon {
    width: 125px;
    height: 125px;
    border-radius: 50%;
    border: 15px solid #fff;
    background-color: #fff;
    display: inline-block;
}
.testimonials-slider .button-play-wraper .icon:before {
    content: "\E928";
    font-family: glyphs;
    font-size: 95px;
    line-height: 95px;
    color: #d68d00;
    width: 125px;
    height: 125px;
    border-radius: 50%;
    -webkit-transition: 0.2s ease-in-out;
    -o-transition: 0.2s ease-in-out;
    transition: 0.2s ease-in-out;
}
.testimonials-slider-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    position: relative;
}
.testimonials-slider-picture {
    position: relative;
    z-index: 1;
    margin-top: 1.5rem;
}
.testimonials-slider-picture .img-wraper {
    width: 207px;
    height: 207px;
    background-color: #ccc;
    overflow: hidden;
    position: relative;
    border-radius: 50%;
}
.testimonials-slider-picture .img-wraper img {
    position: absolute;
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: 50% 50%;
    object-position: 50% 50%;
    width: 100%;
    height: 100%;
}
.testimonials-slider-picture .button-play {
    display: inline-block;
    font: 600 16px/20px soleil, Lucida Sans Unicode, Lucida Grande, sans-serif;
    letter-spacing: 0;
    color: #3b81f0;
    opacity: 1;
}
.testimonials-slider blockquote {
    margin-left: 72px;
}
.testimonials-slider blockquote p {
    overflow: hidden;
    line-height: 32px;
    max-height: 96px;
    text-align: left;
    margin-right: -1em;
    padding-right: 1em;
}
.testimonials-slider .slick-arrow {
    color: #aec4ea;
    -webkit-transition: 0.3s ease-in-out;
    -o-transition: 0.3s ease-in-out;
    transition: 0.3s ease-in-out;
    position: absolute;
    top: 35%;
    width: 40px;
    z-index: 1;
    height: 40px;
    font-size: 40px;
}
.testimonials-slider .slick-arrow:hover {
    cursor: pointer;
}
.testimonials-slider .slick-arrow:hover:before {
    color: tomato;
}
.testimonials-slider .slick-arrow.next {
    right: -72px;
    text-align: right;
}
.testimonials-slider .slick-arrow.next:before {
    content: "\EA03";
    font-family: glyphs;
    -webkit-transition: 0.2s ease-in-out;
    -o-transition: 0.2s ease-in-out;
    transition: 0.2s ease-in-out;
}
.testimonials-slider .slick-arrow.prev {
    left: -72px;
}
.testimonials-slider .slick-arrow.prev:before {
    content: "\EA02";
    font-family: glyphs;
    -webkit-transition: 0.2s ease-in-out;
    -o-transition: 0.2s ease-in-out;
    transition: 0.2s ease-in-out;
}
.testimonials-slider .slick-arrow.slick-disabled {
    opacity: 0;
}
@media screen and (max-width: 960px) {
    .testimonials-slider-item {
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        overflow: hidden;
    }
    .testimonials-slider blockquote {
        margin-left: 0;
    }
    .testimonials-slider blockquote p {
        max-height: unset;
    }
    .testimonials-slider .slick-arrow {
        top: unset;
        bottom: 0;
    }
    .testimonials-slider .slick-arrow.next {
        right: 0;
    }
    .testimonials-slider .slick-arrow.prev {
        left: unset;
        right: 40px;
    }
    .testimonials-slider .button-play-wraper {
        width: 100%;
        position: relative;
        left: unset;
        -webkit-transform: none;
        -o-transform: none;
        transform: none;
        margin-top: 1.5rem;
    }
    .testimonials-slider .button-play-wraper .icon {
        width: 90px;
        height: 90px;
    }
    .testimonials-slider .button-play-wraper .icon:before {
        font-size: 60px;
        line-height: 60px;
        width: 90px;
        height: 90px;
    }
    .testimonials-slider .button-play-wraper .button-play {
        text-align: center;
    }
    .testimonials-slider-picture {
        margin-bottom: -1rem;
    }
    .testimonials-slider-picture .img-wraper {
        width: 60px;
        height: 60px;
    }
    .benefits-list {
        display: block;
    }
    .price-page .values-slider {
        margin-bottom: 50px;
    }
    .price-table {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
    .price-table:after {
        top: 0;
    }
    .price-table-item.popular,
    .price-table-item:first-child,
    .price-table-item:last-child {
        width: 100%;
    }
    .price-table-item.popular .top,
    .price-table-item:first-child .top,
    .price-table-item:last-child .top {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-direction: row;
        flex-direction: row;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        margin: 0;
        padding-left: 33%;
        min-height: unset;
        padding-top: 38px;
    }
    .price-table-item.popular .ui-select,
    .price-table-item.popular [class^="button-"],
    .price-table-item:first-child .ui-select,
    .price-table-item:first-child [class^="button-"],
    .price-table-item:last-child .ui-select,
    .price-table-item:last-child [class^="button-"] {
        width: calc(50% - 5px);
    }
    .price-table-item:first-child .top {
        border-radius: 3px 3px 0 0;
        border-left: 1px solid #ccdbf5;
        border-bottom: none;
    }
    .price-table-item:first-child .top:after {
        background: transparent -webkit-gradient(linear, left top, left bottom, from(#fff), to(#e8f0fd)) 0 0 no-repeat padding-box;
        background: transparent -webkit-linear-gradient(top, #fff, #e8f0fd) 0 0 no-repeat padding-box;
        background: transparent -o-linear-gradient(top, #fff 0, #e8f0fd 100%) 0 0 no-repeat padding-box;
        background: transparent linear-gradient(180deg, #fff, #e8f0fd) 0 0 no-repeat padding-box;
        bottom: 0;
        top: unset;
        width: 100%;
        height: 20px;
    }
    .price-table-item.popular .top {
        border-radius: 0;
    }
    .price-table-item:last-child .top {
        border-radius: 0 0 3px 3px;
        border-left: 1px solid #ccdbf5;
        border-top: none;
    }
    .price-table-item:last-child .top:before {
        background: transparent -webkit-gradient(linear, left bottom, left top, from(#fff), to(#e8f0fd)) 0 0 no-repeat padding-box;
        background: transparent -webkit-linear-gradient(bottom, #fff, #e8f0fd) 0 0 no-repeat padding-box;
        background: transparent -o-linear-gradient(bottom, #fff 0, #e8f0fd 100%) 0 0 no-repeat padding-box;
        background: transparent linear-gradient(0deg, #fff, #e8f0fd) 0 0 no-repeat padding-box;
        top: 0;
        width: 100%;
        height: 20px;
    }
    .price-table .table-item-title {
        text-align: left;
    }
    .price-table .popular .table-item-title,
    .price-table .popular p {
        -webkit-transform: none;
        -o-transform: none;
        transform: none;
    }
    .price-table .popular [class^="button-"] {
        margin-bottom: 0;
    }
    .price-table .title-price {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-flex: 1;
        -ms-flex: 1 0 100%;
        flex: 1 0 100%;
        -webkit-box-align: baseline;
        -ms-flex-align: baseline;
        align-items: baseline;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
    }
    .price-table .title-price .price:not(.old) .number {
        font-size: 2.5rem;
        font-weight: 700;
    }
    .price-table .title-price .price.old {
        margin: 0 10px 0 auto;
    }
    .price-table .ui-select {
        margin-bottom: 0;
    }
    .price-table-item p {
        margin-bottom: 1.5rem;
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1;
    }
    .price-table-item .ui-select {
        -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
        order: 2;
    }
    .price-table-item [class^="button-"] {
        -webkit-box-ordinal-group: 4;
        -ms-flex-order: 3;
        order: 3;
    }
    .klicky-wraper {
        position: absolute;
        left: 0;
        right: calc(67% + 10px);
        top: 0;
        bottom: 0;
    }
    .klicky-wraper img.klicky {
        position: absolute;
        top: 50%;
        left: 50%;
        margin-top: 0;
        -webkit-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
    }
}
@media screen and (max-width: 660px) {
    .testimonials-slider .button-play-wraper {
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-top: 0;
    }
    .testimonials-slider-picture .icon {
        width: 30px;
        height: 30px;
        border: 3px solid #fff;
    }
    .testimonials-slider-picture .icon:before {
        font-size: 24px;
        line-height: 24px;
        width: 30px;
        height: 30px;
    }
    .price-page h1 {
        margin-bottom: 2rem;
    }
    .price-table-item.popular .top,
    .price-table-item:first-child .top,
    .price-table-item:last-child .top {
        padding-left: 38px;
    }
    .price-table .klicky-wraper {
        display: none;
    }
    .ui-select.contacts {
        display: block;
    }
    .price-table .price-table-item .ui-select {
        margin-bottom: 1rem;
    }
    .price-table .price-table-item .button-primary,
    .price-table .price-table-item .button-tertiary,
    .price-table .price-table-item .ui-select {
        width: 100%;
    }
    .price-table .title-price {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-pack: start;
        -ms-flex-pack: start;
        justify-content: flex-start;
    }
    .price-table .title-price .price.old {
        margin-left: 0;
    }
    .price-table .title-price .table-item-title {
        width: 100%;
    }
}
@media screen and (max-width: 375px) {
    .price-table .price-table-item .top {
        padding: 30px 20px;
    }
}
.wp-block-spacer.is-style-default-space {
    height: 38px !important;
}
.wp-block-spacer.is-style-small-space {
    height: 50px !important;
}
.wp-block-spacer.is-style-medium-space {
    height: 80px !important;
}
.wp-block-spacer.is-style-large-space {
    height: 150px !important;
}
@media screen and (max-width: 960px) {
    .wp-block-spacer.is-style-large-space {
        height: 50px !important;
    }
}
@media screen and (max-width: 660px) {
    .wp-block-spacer.is-style-default-space,
    .wp-block-spacer.is-style-large-space,
    .wp-block-spacer.is-style-medium-space {
        height: 24px !important;
    }
}
@media only screen and (min-device-width: ) and (max-device-width: ) and (-webkit-min-device-pixel-ratio: ) {
    .wp-block-cover,
    .wp-block-image {
        min-height: 320px;
    }
}
@media only screen and (min-device-width: 300px) and (max-device-width: 660px) {
    .wp-block-cover,
    .wp-block-image {
        min-height: 320px;
    }
}
.wp-block-image {
    margin: 0;
}
.wp-block-image figure figcaption {
    margin-top: 24px;
}
.wp-block-image.is-style-background-image-float {
    position: relative;
}
.wp-block-image.is-style-background-image-float img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
}
@media screen and (max-width: 960px) {
    .wp-block-image img {
        max-width: 100%;
        height: auto;
    }
}
.wp-block-button.call-to-action {
    text-align: center;
    margin-top: 40px;
    margin-bottom: 40px;
}
.wp-block-button.call-to-action .wp-block-button__link {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #ffa20f;
    -webkit-box-shadow: inset 0 -2px 0 0 #d57a00;
    box-shadow: inset 0 -2px 0 0 #d57a00;
    position: relative;
    min-height: 55px;
    font-size: 22px;
}
.wp-block-button.call-to-action .wp-block-button__link[class*="glyph-"]:before,
.wp-block-button.call-to-action .wp-block-button__link[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
}
.wp-block-button.call-to-action .wp-block-button__link:hover {
    color: #fff !important;
    background: #ffb238;
    -webkit-transition: all 0.2s ease-in;
    -o-transition: all 0.2s ease-in;
    transition: all 0.2s ease-in;
}
.wp-block-button.call-to-action .wp-block-button__link:before {
    display: block;
    font-family: glyphs;
    content: "\E979";
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    color: #fff;
    position: absolute;
    top: 50%;
    right: 0;
    margin-top: -30px;
    z-index: 1;
}
.wp-block-button.call-to-action .wp-block-button__link:after {
    display: block;
    content: "";
    width: 60px;
    height: 100%;
    background-color: rgba(213, 122, 0, 0.65);
    -webkit-box-shadow: inset 0 -2px 0 0 #c16e00;
    box-shadow: inset 0 -2px 0 0 #c16e00;
    position: absolute;
    top: 0;
    right: 0;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}
@media screen and (max-width: 600px) {
    .wp-block-button.call-to-action .wp-block-button__link.large,
    .wp-block-button.call-to-action .wp-block-button__link.xlarge {
        font-size: 1.85rem;
    }
}
@media screen and (max-width: 500px) {
    .wp-block-button.call-to-action .wp-block-button__link.large,
    .wp-block-button.call-to-action .wp-block-button__link.medium,
    .wp-block-button.call-to-action .wp-block-button__link.xlarge {
        font-size: 1.35rem;
    }
}
@media screen and (max-width: 320px) {
    .wp-block-button.call-to-action .wp-block-button__link.large,
    .wp-block-button.call-to-action .wp-block-button__link.medium,
    .wp-block-button.call-to-action .wp-block-button__link.xlarge {
        font-size: 1.15rem;
    }
}
.wp-block-button.is-style-button-primary .wp-block-button__link {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #ffa20f;
    border: none;
    -webkit-box-shadow: inset 0 -3px 0 0 #db8600;
    box-shadow: inset 0 -3px 0 0 #db8600;
}
.wp-block-button.is-style-button-primary .wp-block-button__link[class*="glyph-"]:before,
.wp-block-button.is-style-button-primary .wp-block-button__link[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
    color: #d57a00;
}
.wp-block-button.is-style-button-primary .wp-block-button__link:hover {
    color: #fff !important;
    background: #ef9300;
    -webkit-box-shadow: inset 0 -3px 0 0 #d18000;
    box-shadow: inset 0 -3px 0 0 #d18000;
    -webkit-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.wp-block-button.is-style-button-secondary .wp-block-button__link {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #fff !important;
    background: #4d92ff;
    border: none;
    -webkit-box-shadow: inset 0 -3px 0 0 #2479ff;
    box-shadow: inset 0 -3px 0 0 #2479ff;
}
.wp-block-button.is-style-button-secondary .wp-block-button__link[class*="glyph-"]:before,
.wp-block-button.is-style-button-secondary .wp-block-button__link[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
    color: #1f65d3;
}
.wp-block-button.is-style-button-secondary .wp-block-button__link:hover {
    color: #fff !important;
    background: #2479ff;
    -webkit-box-shadow: inset 0 -3px 0 0 #2266d1;
    box-shadow: inset 0 -3px 0 0 #2266d1;
    -webkit-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.wp-block-button.is-style-button-outline .wp-block-button__link {
    display: inline-block;
    width: auto;
    height: auto;
    min-height: 35px;
    padding: calc(0.5em - 2px) 1.125em 0.5em;
    text-align: center;
    -webkit-box-shadow: none;
    box-shadow: none;
    outline: none;
    border-radius: 3px;
    font-weight: 600;
    font-size: 16px;
    line-height: 35px;
    vertical-align: baseline;
    cursor: pointer;
    -webkit-transition: all 0.15s ease-out;
    -o-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
    color: #4d92ff !important;
    background: #fff;
    border: none;
    -webkit-box-shadow: inset 0 0 0 1px #4d92ff;
    box-shadow: inset 0 0 0 1px #4d92ff;
}
.wp-block-button.is-style-button-outline .wp-block-button__link[class*="glyph-"]:before,
.wp-block-button.is-style-button-outline .wp-block-button__link[class*="icon-"]:before {
    display: inline-block;
    margin-right: 0.25em;
    font-size: 0.725em;
    line-height: inherit;
    vertical-align: baseline;
    margin-left: -0.25em;
    margin-top: 0.125em;
    color: #4d92ff;
}
.wp-block-button.is-style-button-outline .wp-block-button__link:hover {
    color: #1f65d3 !important;
    -webkit-box-shadow: inset 0 0 0 1px #1f65d3;
    box-shadow: inset 0 0 0 1px #1f65d3;
    -webkit-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.wp-block-columns:last-child {
    margin-bottom: 0;
}
@media screen and (min-width: 781px) and (max-width: 661px) {
    .wp-block-columns .wp-block-column {
        -ms-flex-preferred-size: calc(50% - 16px) !important;
        flex-basis: calc(50% - 16px) !important;
        -webkit-box-flex: 0;
        -ms-flex-positive: 0;
        flex-grow: 0;
    }
}
@media screen and (max-width: 661px) {
    .wp-block-columns .wp-block-column {
        -ms-flex-preferred-size: 100% !important;
        flex-basis: 100% !important;
    }
    .wp-block-columns .wp-block-column + .wp-block-column {
        margin-top: 38px;
    }
}
.blocks-gallery-grid .blocks-gallery-image,
.blocks-gallery-grid .blocks-gallery-item,
.wp-block-gallery .blocks-gallery-image,
.wp-block-gallery .blocks-gallery-item {
    width: calc((100% - 16px) / 2);
    margin: 0 16px 16px 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
}
.blocks-gallery-grid,
.is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid,
.wp-block-gallery {
    width: 100%;
}
.is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item {
    position: relative;
}
.is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item figure {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
}
.is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item figure img {
    display: block;
    width: auto;
    max-width: 200%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 50%;
    -o-transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
}
.is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item:nth-child(2n) {
    width: calc(70% - 10px);
    height: 0;
    padding-top: calc(36.51163% - 5.21595px);
}
.is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item:nth-child(odd) {
    width: calc(30% - 10px);
    height: 0;
    padding-top: calc(37.52988% - 12.50996px);
}
@media screen and (max-width: 660px) {
    .is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item:nth-child(2n),
    .is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item:nth-child(odd) {
        width: 100%;
        height: auto;
        padding-top: 0;
        margin-right: 0;
    }
    .is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item:nth-child(2n) figure,
    .is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item:nth-child(odd) figure {
        width: 100%;
        height: auto;
        position: static;
        overflow: visible;
    }
    .is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item:nth-child(2n) figure img,
    .is-style-custom-image-gallery.columns-2.two-thirds .blocks-gallery-grid .blocks-gallery-item:nth-child(odd) figure img {
        display: block;
        width: 100%;
        height: auto;
        position: static;
        -webkit-transform: none;
        -o-transform: none;
        transform: none;
    }
}
.page-section .pewl {
    background-color: transparent;
    border-color: #c1cfe6;
    border-radius: 4px;
}
.page-section .pewl a,
.page-section .pewl div,
.page-section .pewl p,
.page-section .pewl span {
    font-family: soleil, Lucida Sans Unicode, Lucida Grande, sans-serif !important;
}
.page-section .pewl .pew-content > .pew-left > div {
    position: relative;
}
.page-section .pewl .pew-content > .pew-left > div:before {
    display: block;
    content: "";
    width: 100%;
    height: 100%;
    background: url(images/KT-icon.png) no-repeat 50%;
    background-size: contain;
    position: absolute;
    top: 0;
    left: 0;
}
.page-section .pewl .pew-content > .pew-left > div img {
    opacity: 0;
}
.page-section .pewl .pew-content > .pew-middle {
    border-color: #c1cfe6;
    background-color: #fff;
}
.page-section .pewl .pew-content > .pew-middle > div .pew-left {
    border-right-color: #c1cfe6 !important;
}
.page-section .pewl .pew-content > .pew-right > div {
    border-color: #c1cfe6;
    background-color: #fff;
}
.page-section .pewl .pew-content > .pew-right > div:before {
    border-right-color: #c1cfe6;
}

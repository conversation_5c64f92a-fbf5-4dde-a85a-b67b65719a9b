@CHARSET "UTF-8";

.element-invisible {
    display: none;
}

.clear {
    clear: both;
}

thead th,
th {
    border-bottom: none;
}

tbody {
    border-top: none;
}

tr.even, tr.odd {
    background-color: transparent;
    border-bottom: none;
    padding: 0;
}

table.table-buttons tr td {
    vertical-align: middle;
}
table.table-wrap {
    table-layout: fixed;
}
table.table-wrap tr td {
    word-break: break-word;
}

.table > thead > tr > td.active,
.table > tbody > tr > td.active,
.table > tfoot > tr > td.active,
.table > thead > tr > th.active,
.table > tbody > tr > th.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > tbody > tr.active > td,
.table > tfoot > tr.active > td,
.table > thead > tr.active > th,
.table > tbody > tr.active > th,
.table > tfoot > tr.active > th {
    background-color: transparent;
}

.table > thead > tr.success > td.active,
.table > tbody > tr.success > td.active,
.table > tfoot > tr.success > td.active,
.table > thead > tr.success > th.active,
.table > tbody > tr.success > th.active,
.table > tfoot > tr.success > th.active {
    background-color: #dff0d8;
}

.table tr.even.success:hover,
.table tr.odd.success:hover,
.table tr.even.success:hover > td.active,
.table tr.odd.success:hover > td.active {
    background-color: #d0e9c6;
}

.table > thead > tr.warning > td.active,
.table > tbody > tr.warning > td.active,
.table > tfoot > tr.warning > td.active,
.table > thead > tr.warning > th.active,
.table > tbody > tr.warning > th.active,
.table > tfoot > tr.warning > th.active,
.table > thead > tr.warning.active > td,
.table > tbody > tr.warning.active > td,
.table > tfoot > tr.warning.active > td,
.table > thead > tr.warning.active > th,
.table > tbody > tr.warning.active > th,
.table > tfoot > tr.warning.active > th {
    background-color: #FCF8E3;
}

.table tr:hover > td.active,
.table tr:hover > td.active,
.table-hover > tbody > tr.active:hover > td,
.table-hover > tbody > tr.active:hover > th,
.table-hover > tbody > tr:hover > .active,
.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover {
    background-color: #f5f5f5;
}

.table tr.even.warning:hover,
.table tr.odd.warning:hover,
.table tr.even.warning:hover > td.active,
.table tr.odd.warning:hover > td.active {
    background-color: #faf2cc;
}

.table > thead > tr.info > td.active,
.table > tbody > tr.info > td.active,
.table > tfoot > tr.info > td.active,
.table > thead > tr.info > th.active,
.table > tbody > tr.info > th.active,
.table > tfoot > tr.info > th.active,
.table > tbody > tr.info > td,
.table > thead > tr.info.active > td,
.table > tbody > tr.info.active > td,
.table > tfoot > tr.info.active > td,
.table > thead > tr.info.active > th,
.table > tbody > tr.info.active > th,
.table > tfoot > tr.info.active > th {
    background-color: #d9edf7;
}

.table tr.even.info:hover,
.table tr.odd.info:hover,
.table tr.even.info:hover > td,
.table tr.odd.info:hover > td,
.table tr.even.info:hover > td.active,
.table tr.odd.info:hover > td.active {
    background-color: #c4e3f3;
}

.table.searchresulttable tr.even:hover,
.table.searchresulttable tr.even:hover,
.table.searchresulttable tr.even:hover > td,
.table.searchresulttable tr.odd:hover > td,
.table.searchresulttable tr.even:hover > td.active,
.table.searchresulttable tr.odd:hover > td.active {
  background-color: #d9d9d9;
}

/* TABLE DRAG*/

body.drag {
    cursor: move;
}
.draggable a.tabledrag-handle {
    cursor: move;
    overflow: hidden;
    text-decoration: none;
}
a.tabledrag-handle:hover {
    text-decoration: none;
}
a.tabledrag-handle .handle {
    background: url(../../../../../misc/draggable.png) no-repeat 0px 0px;
    height: 13px;
    width: 13px;
}
a.tabledrag-handle-hover .handle {
    background-position: 0px -20px;
}

.klicktipp-tabledrag {
    height: auto;
}

.klicktipp-tabledrag table {
    margin-bottom: 6px;
}

.table-drag-warning {
    margin-bottom: 6px;
    margin-top: 20px;
}

.tabledrag-changed {
    color: #e09010;
}

.draggable a.tabledrag-handle {
    float: none;
    display: inline-block;
    padding-left: 0;
    padding-right: 0;
    margin-left: 0;
    margin-right: 0;
}

.tabledrag-toggle-weight {
    display: none;
}

#signature-overview .draggable.warning .tabledrag-handle,
#signature-overview .draggable.warning .tabledrag-changed {
    display: none;
}

.klicktipp-tabledrag td span.tabledrag-changed,
.table-drag-warning span.tabledrag-changed {
    padding-left: 5px;
}


input[type="checkbox"],
.form-checkboxes {
    margin: 2px 5px 0 0;
    vertical-align: text-top
}

input[type="checkbox"].checkbox-multiline {
    display:inline;
    vertical-align:top;
    margin-top:3px;
}

input[type="checkbox"].checkbox-multiline + label.option {
    display:inline-block;
    width:90%;
}

input[type="checkbox"].checkbox-multiline + label.option .quickhelp-icon {
    position: relative;
    margin-left: 5px;
}

label.option {
    margin-bottom: 0;
}

input[type="radio"] {
    margin: 3px 5px 0 0;
    vertical-align: text-top
}

.form-type-radios .form-type-radio {
    display: inline-block;
    margin: 0 10px 0 0;
}

h1 {
    margin: 0 0 20px 0;
}

p.klicktipp-info {
    position: relative;
    display: inline-block;
    margin-bottom: 0;
}

.klicktipp-grid-row {
    margin-bottom: 15px;
}

body.has-sidebar form .klicktipp-grid-row,
.klicktipp-grid-row > .form-group {
    margin-bottom: 0;
}

.sub-headline {
    position: relative;
    font-size: 26px;
    font-weight: bold;
    margin-bottom: 0;
    padding-top: 15px;
    text-align: center;
}

.additional-info {
    color: #999999;
    font-size: 0.7em;
}

p.important {
    font-weight: bold;
    font-size: 16px;
}

.top-margin-1-5em {
    margin-top: 1.5em;
}

.top-margin-2-0em {
    margin-top: 2.0em;
}

.top-margin-2-5em {
    margin-top: 2.5em;
}

.bottom-margin-1-5em {
    margin-bottom: 1.5em;
}

.bottom-margin-2-0em {
    margin-bottom: 2.0em;
}

.bottom-margin-2-5em {
    margin-bottom: 2.5em;
}

.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-positive {
    color: #47a447;
}

.text-negative {
    color: #d2322d;
}

.table-col-right {
    text-align: right;
}

.table-col-left {
    text-align: left;
}

.table-col-center {
    text-align: center;
}

.table > thead > tr > th.align-top {
    vertical-align: top;
}

.table > thead > tr > th.table-col-fit,
.table > tbody > tr > td.table-col-fit {
    width: 1px;
    white-space: nowrap;
    padding-left: 15px;
    padding-right: 15px;
}

.table > thead > tr > th.table-col-fit.word-break {
    white-space: normal;
    width: 110px;
}

.table > thead > tr > th.table-col-quickhelp .quickhelp-icon,
.table > tbody > tr > td.table-col-quickhelp .quickhelp-icon {
    position: relative;
    right: inherit;
    top: inherit;
    left: inherit;
    bottom: inherit;
    margin: 1px 0 0 5px;
}

.quickhelp-inline .quickhelp-icon {
    position: relative;
    display: inline-block;
    margin: 0 0 0 5px;
}

.tagging-dropdown-quickhelp {
    position: relative;
    display: inline-block;
}

.tagging-dropdown-quickhelp .form-group select.form-control {
    max-width: none;
}

.tagging-dropdown-quickhelp .quickhelps {
    display: block;
    margin: -3px 0 0;
    position: absolute;
    right: 0;
    top: 50%;
}

.import-quickhelp-wrapper {
  position: relative;
}
#edit-enablesmartimport-wrapper {
  display: inline-block;
}
#quickhelp-automation-template-import-smart-toggle {
  position: relative;
}

.caret {
    margin-left: 4px;
}

label {
    position: relative;
    max-width: none;
}

label.option {
    font-weight: normal;
}

.form-required {
    color: #FF0000;
}

p.fieldset-description {
    position: relative;
    color: #737373;
}

p.fieldset-description .quickhelp-icon {
    position: relative;
    margin: 0 0 0 5px;
}

a, a:focus, a.active, li a.active {
    outline: none;
    color: #2885d1;
}

a:hover {
    color: #206cac;
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control,
.klicktipp-item.form-control {
    background-color: #EEEEEE;
    cursor: default;
}

.klicktipp-item {
    height: auto;
}

.klicktipp-item.form-control.error {
    padding: 6px 12px;
}

.btn-row a,
.btn-row a:hover,
.btn-row a:active,
.btn-row a:focus {
    text-decoration: none;
}

/* hide the following elements on page load to prevent flashing,
   the visibility will be conrtrolled by JavaScript
*/
.subscription-date,
.subscription-date-until,
.confirmation-date,
.confirmation-date-until,
[class^="date-normal-"],
[class*=" date-normal-"],
[class^="date-until-"],
[class*=" date-until-"],
[class^="jstoggle-"],
[class*=" jstoggle-"] {
    display: none;
}

/* Overview filter */

.overview-filter {
    position: relative;
}

.overview-filter + * {
    clear: both;
}

.overview-filter label {
    display: block;
}

.overview-filter .overview-filter-item {
    position: relative;
    display: inline-block;
    float: left;
    margin-right: 10px;
}

.overview-filter .overview-filter-item select {
    width: auto;
    max-width: none;
}

/* Klicktipp History */

.history-even {
    position: relative;
    background-color: #f7f7f7;
    border-bottom: 1px solid #dbdbdb;
    padding: 0 0 25px 15px;
}

.history-odd {
    position: relative;
    border-bottom: 1px solid #dbdbdb;
    padding: 0 0 25px 15px;
}

.history-headline {
    position: relative;
    color: #212121;
    font-size: 22px;
    margin: 0;
    padding: 0 0 25px 15px;
    line-height: 24px;
}

.history-date {
    position: relative;
    float: right;
    font-size: 16px;
    color: #676767;
    font-family: "Lucida Grande","Lucida Sans Unicode","Helvetica Neue",Helvetica,Arial,sans-serif;
    line-height: 32px;
    padding: 0 10px 0 0;
}

/* Progress bar*/

.progress {
    background-color: #F5F5F5;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
    height: 20px;
    margin-bottom: 20px;
    overflow: hidden;
}

.progress .bar {
    background-color: #428BCA;
    box-shadow: 0 -1px 0 rgba(0, 0, 0, 0.15) inset;
    color: #FFFFFF;
    float: left;
    font-size: 12px;
    height: 100%;
    line-height: 20px;
    text-align: center;
    transition: width 0.6s ease 0s;
    width: 0;
    border: none;
    margin: 0;
}

.progress.progress-info .bar {
    animation: 2s linear 0s normal none infinite progress-bar-stripes;
}

.progress-striped .bar.filled {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, rgba(0, 0, 0, 0) 25%, rgba(0, 0, 0, 0) 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, rgba(0, 0, 0, 0) 75%, rgba(0, 0, 0, 0));
    background-size: 40px 40px;
    border-bottom: none;
    height: 100%;
    width: 0;
}

.progress .percentage {

    content: "0%";

}

.progress .percentage.throbber-progress-bar {
    background: url("../../../../../misc/img/throbber_progress_bar.gif") no-repeat scroll right 2px rgba(0, 0, 0, 0);
    margin-right: 2px;
    padding-right: 20px;
}

.ajax-progress {
    display: none;
}

#klicktipp-subscriber-search-form .ajax-progress {
  display: block;
  position: absolute;
  left: calc(50% - 100px);
  top: 150px;
  width: 200px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  padding: 10px 15px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  z-index: 1000;
  animation: ProgressFadeInOpacity 2s;
}
@keyframes ProgressFadeInOpacity {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
#klicktipp-subscriber-search-form .button-row {
  padding-bottom: 50px;
  position: relative;
  overflow: visible;
}

#klicktipp-subscriber-search-form .create-buttons .button-row {
  padding-bottom: 0;
}

#klicktipp-subscriber-search-form .klicktipp-pager {
  position: relative;
  overflow: visible;
}
#klicktipp-subscriber-search-form .ajax-progress .message {
  text-align: center;
}
#klicktipp-subscriber-search-form .ajax-progress .throbber {
  background: url("../../../../../misc/img/throbber_progress_bar.gif") no-repeat center;

}

.create-buttons .button-row a {
  margin-bottom: 5px;
}

.create-buttons .right{
  float: right;
}

.searchresulttable th {
  white-space: nowrap;
}

.progress-disabled {
    cursor: default !important;
}

/* Content Divider * * * * * */

.content-divider {
    padding-top: 36px;
    margin-top: 0;
    background: transparent url(../../../../../misc/img/divider.png) no-repeat center top;
}

/*
* MENU
*/

/* a body top margin is set by admin-menu.css with !important, remove */
body.admin-menu {
    margin-top: 0px !important;
}

/* the admin menu will be the first element and should overlap the top menu */
#admin-menu {
    position: fixed;
    top: 0px;
    z-index: 2000;
}

#other-account-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #ffa210;
    color: black;
    font-weight: 500;
    z-index: 1999;
    line-height: normal;
}

body.other-account-spam-activity #other-account-bar {
    background-color: #ff430b;
}

#other-account-bar ul {
    display: flex;
    font-family: "Helvetica Neue", sans-serif;
    font-size: 11px;
    justify-content: space-around;
    height: 30px;
    align-items: center;
    padding: 0;
    margin: 0;
}

#other-account-bar ul li {
    display: inline-block;
    padding: 0;
    margin: 0;
}

#other-account-bar ul.subaccount {
    justify-content: right;
}

#other-account-bar ul.subaccount li {
    margin-right: 15px;
}

#other-account-bar .subAccountUsername {
    background-color: #eeeeee;
    border-radius: 2px;
    margin-left: 4px;
    padding: 5px;
}

.dropdown-menu .divider {
    margin: 4px 0;
}

body.has-sidebar #top-menu .container,
body.has-sidebar #footer-menu .container {
    padding-left: 0;
    padding-right: 15px;
}

#top-menu div.navbar-collapse {
    position: relative;
    padding-left: 0;
    padding-right: 0;
    margin-left: -13px;
}

#top-menu-beamer-mobile {
  position: relative;
  display: none;
  float: right;
  padding: 15px;
}

body.admin-menu #top-menu {
    top: 20px;
}

body.other-account #top-menu {
    top: 30px;
}

body.admin-menu.other-account #other-account-bar {
    top: 20px;
}

body.admin-menu.other-account #top-menu {
    top: 50px;
}

body #container {
    padding-top: 70px;
    margin-bottom: 20px;
}

body.admin-menu #container {
    padding-top: 90px;
}

body.other-account #container {
    padding-top: 90px;
}

body.admin-menu.other-account #container {
    padding-top: 110px;
}

/* remove padding from the brand */
.navbar > .container .navbar-brand {
    margin: 0;
    padding: 0;
    float: none;
}

.navbar > .container .navbar-brand img {
    margin-top: 10px;
}

.navbar-logo-toggle {
    float: right;
    position: relative;
    z-index: 10;
}

.navbar-custom {
    background-color: #ffffff;
}

/* End User login */

.panel-heading a b.caret {
    border-bottom-color: #2885d1;
    border-top-color: #2885d1;
}

.panel-heading a:hover b.caret {
    border-bottom-color: #206cac;
    border-top-color: #206cac;
}

.panel-body {
    padding-bottom: 0;
}

/* menu account info */

#account-info-wrapper {
    display: inline-block;
    height: 45px;
    margin-left: 10px;
    padding-right: 100px;
    position: relative;
}

#account-info {
    display: inline-block;
    height: 45px;
    position: relative;
    text-align: left;
}

#account-info .account-info-category {
    font-weight: normal;
    left: 130px;
    margin-left: 0;
    position: absolute;
    top: 0;
    width: 100px;
    color: #f9b90a;
}

#account-info-batches {
    background: url("../../../../../misc/img/icons_sprite.png") no-repeat scroll 0 -626px rgba(0, 0, 0, 0);
    display: inline-block;
    height: 45px;
    margin: 0;
    position: relative;
    width: 120px;
}

#account-info-batches.klick-tipp-account-beginner {
    background-position: -96px -626px;
}

#account-info-batches.klick-tipp-account-standard {
    background-position: -72px -626px;
}

#account-info-batches.klick-tipp-account-premium {
    background-position: -48px -626px;
}

#account-info-batches.klick-tipp-account-platinum {
    background-position: -24px -626px;
}

#account-info-batches.klick-tipp-account-enterprise {
    background-position: 0px -626px;
}

#account-info-batches .upsell_link {
    position: absolute;
    margin: 0;
    padding: 0;
    top: 0;
    width: 24px;
    height: 45px;
    display: none;
}

#account-info-batches .upsell_link.klick-tipp-account-standard {
    left: 23px;
}

#account-info-batches .upsell_link.klick-tipp-account-premium {
    left: 47px;
}

#account-info-batches .upsell_link.klick-tipp-account-platinum {
    left: 71px;
}

#account-info-batches .upsell_link.klick-tipp-account-enterprise {
    left: 95px;
}

#account-info-batches.klick-tipp-account-beginner .upsell_link.klick-tipp-account-standard,
#account-info-batches.klick-tipp-account-beginner .upsell_link.klick-tipp-account-premium,
#account-info-batches.klick-tipp-account-beginner .upsell_link.klick-tipp-account-platinum,
#account-info-batches.klick-tipp-account-beginner .upsell_link.klick-tipp-account-enterprise,
#account-info-batches.klick-tipp-account-standard .upsell_link.klick-tipp-account-premium,
#account-info-batches.klick-tipp-account-standard .upsell_link.klick-tipp-account-platinum,
#account-info-batches.klick-tipp-account-standard .upsell_link.klick-tipp-account-enterprise,
#account-info-batches.klick-tipp-account-premium .upsell_link.klick-tipp-account-platinum,
#account-info-batches.klick-tipp-account-premium .upsell_link.klick-tipp-account-enterprise,
#account-info-batches.klick-tipp-account-platinum .upsell_link.klick-tipp-account-enterprise {
    display: block;
}

#account-info-batches .upsell_link:hover {
    background: transparent url("../../../../../misc/img/icons_sprite.png") no-repeat 0px -626px;
    opacity: 0.5;
}

/* Breadcrumb
 * The Newsletter dialog has a form with a select element to switch between newsletters
 * display inline, no margins, a bit smalller than .input-sm
 */

body.has-sidebar .breadcrumb-row {
    padding-left: 0;
}

.breadcrumb > li + li:before {
    content: '';
}

.breadcrumb li i {
    margin: 2px 3px 0 0;
}

.breadcrumb form {
    display: inline-block;
}

.breadcrumb form .form-group {
    margin: 0;
}

.breadcrumb form select {
    height: 25px;
    line-height: 25px;
    font-size: 14px;
    border-radius: 3px;
    padding: 2px 5px;
}

/* Footer menu*/

/*remove round corners and padding*/

#footer-menu {
    margin: 0;
    z-index: 1000;
}

#footer-menu.navbar {
    border-radius: 0;
}

/*
#footer-menu.navbar .navbar-inner .container .copyright {
  display: inline-block;
  line-height: 20px;
  padding: 9px 0;
  font-size: 13px;
  color: #999999;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
*/

/* Klick-Tipp Icons */

/* the following rule is from bootstrap 2.3.2, since glyphicons are now an extra component in bootstrap 3
 * TODO: check if we need glyphicons at all, we should use our own icons
 */
[class^="icon-"],
[class*=" icon-"] {
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-top: 1px;
    *margin-right: .3em;
    line-height: 14px;
    vertical-align: text-top;
    background-image: url("../../../../../misc/img/icons_sprite.png");
    background-position: 14px 14px;
    background-repeat: no-repeat;
}

.icon-beamer {
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512'><path d='M224 512c35.32 0 63.97-28.65 63.97-64H160.03c0 35.35 28.65 64 63.97 64zm215.39-149.71c-19.32-20.76-55.47-51.99-55.47-154.29 0-77.7-54.48-139.9-127.94-155.16V32c0-17.67-14.32-32-31.98-32s-31.98 14.33-31.98 32v20.84C118.56 68.1 64.08 130.3 64.08 208c0 102.3-36.15 133.53-55.47 154.29-6 6.45-8.66 14.16-8.61 21.71.11 16.4 12.98 32 32.1 32h383.8c19.12 0 32-15.6 32.1-32 .05-7.55-2.61-15.27-8.61-21.71z' fill='%23bbbbbb'></path></svg>");
    background-position: unset;
}

.icon-status-active {
    background-position: 0px -20px;
    margin: 3px 5px 0 0;
}

.icon-status-pending {
    background-position: -20px -20px;
    margin: 3px 5px 0 0;
}

.icon-status-unsubscribed {
    background-position: -40px -20px;
    margin: 3px 5px 0 0;
}

.icon-status-spamcomplaint {
    background-position: -60px -20px;
    margin: 3px 5px 0 0;
}

.icon-status-notbounced {
    background-position: 0px 0px;
    margin: 3px 5px 0 0;
}

.icon-status-paused {
  background-position: -80px 0px;
  margin: 3px 5px 0 0;
}

.icon-status-play {
  background-position: -100px 0px;
  margin: 3px 5px 0 0;
}

.icon-no-icon {
  background-position: 50px 50px;
  margin: 3px 5px 0 0;
}

.icon-status-softbounce {
    background-position: -20px 0px;
    margin: 3px 5px 0 0;
}

.icon-status-spambounce {
    background-position: -60px 0px;
    margin: 3px 5px 0 0;
}

.icon-status-hardbounce {
    background-position: -40px 0px;
    margin: 3px 5px 0 0;
}

.icon-email-opens {
    background-position: 0px -260px;
}

.icon-email-unopens {
    background-position: -20px -263px;
}

.icon-email-clicked {
    background-position: -40px -260px;
}

.icon-email-notclicked {
    background-position: -60px -260px;
}

.icon-play-tv {
    background-position: 0 -220px;
    height: 18px;
    margin: 0 10px 0 0;
    width: 22px;
}

.icon-bullet-hook {
    background-position: 0 -280px;
    height: 16px;
    margin: 0 11px 0 -29px;
    width: 19px;
}

.icon-bullet-hook-small {
    background-position: -20px -280px;
    height: 16px;
    margin: 2px 5px 0 24px;
    width: 19px;
}

.icon-history-bugfix {
    background-position: -20px -300px;
    margin: 3px 10px 0 0;
    height: 20px;
    width: 20px;
}

.icon-history-milestone {
    background-position: -40px -300px;
    margin: 3px 10px 0 0;
    height: 20px;
    width: 20px;
}

.icon-history-feature {
    background-position: 0px -300px;
    margin: 3px 10px 0 0;
    height: 20px;
    width: 20px;
}

.icon-star {
    background-position: -40px -80px;
}

.icon-widget-bottom-right {
    background-position: 0px -400px;
    width: 25px;
    height: 20px;
}

.icon-widget-bottom-left {
    background-position: -30px -400px;
    width: 25px;
    height: 20px;
}

.icon-widget-left {
    background-position: -60px -400px;
    width: 25px;
    height: 20px;
}

.icon-widget-right {
    background-position: -90px -400px;
    width: 25px;
    height: 20px;
}

/* Breadcrumb spacer */
.icon-breadcrumb {
    background-position: 0px -100px;
}

/* Order page */

.icon-grey-info {
    background-position: 0px -240px;
}

.icon-green-hook {
    background-position: -20px -240px;
}

.icon-red-cross {
    background-position: -40px -240px;
}

/* Top-Menu Icons */
.icon-top-menu-order {
    background-position: 0px -180px;
    width: 17px;
    margin: 0 5px 0 0;
}

.icon-top-menu-login {
    background-position: -20px -180px;
    margin: 0 5px 0 0;
}

.icon-top-menu-search {
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><path d='M385.1 419.1C349.7 447.2 304.8 464 256 464s-93.7-16.8-129.1-44.9l80.4-80.4c14.3 8.4 31 13.3 48.8 13.3s34.5-4.8 48.8-13.3l80.4 80.4zm68.1 .2C489.9 374.9 512 318.1 512 256s-22.1-118.9-58.8-163.3L465 81c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0L419.3 58.8C374.9 22.1 318.1 0 256 0S137.1 22.1 92.7 58.8L81 47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L58.8 92.7C22.1 137.1 0 193.9 0 256s22.1 118.9 58.8 163.3L47 431c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l11.8-11.8C137.1 489.9 193.9 512 256 512s118.9-22.1 163.3-58.8L431 465c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-11.8-11.8zm-34.1-34.1l-80.4-80.4c8.4-14.3 13.3-31 13.3-48.8s-4.8-34.5-13.3-48.8l80.4-80.4C447.2 162.3 464 207.2 464 256s-16.8 93.7-44.9 129.1zM385.1 92.9l-80.4 80.4c-14.3-8.4-31-13.3-48.8-13.3s-34.5 4.8-48.8 13.3L126.9 92.9C162.3 64.8 207.2 48 256 48s93.7 16.8 129.1 44.9zM173.3 304.8L92.9 385.1C64.8 349.7 48 304.8 48 256s16.8-93.7 44.9-129.1l80.4 80.4c-8.4 14.3-13.3 31-13.3 48.8s4.8 34.5 13.3 48.8zM208 256a48 48 0 1 1 96 0 48 48 0 1 1 -96 0z' fill='%23bbbbbb'></path></svg>");
    background-position: 0 0;
    height: 14px;
    width: 14px;
    cursor: pointer;
    opacity: 0.75;
}

.icon-link {
    background-position: 0px -357px;
    margin: 0;
    width: 20px;
    height:20px;
}

.icon-video-link {
    background-position: -20px -357px;
    margin: 0;
    width: 20px;
    height:20px;
}

/* Radio button */

.radio-button-black {
    width: 17px;
    height: 17px;
    cursor: pointer;
    display: inline-block;
    position: relative;
    top: 2px;
    background: transparent url("../../../../../misc/img/icons_sprite.png") scroll no-repeat 0 -320px;
}

.radio-button-black:hover {
    background-position: -20px -340px;
}

.radio-button-black:active,
.radio-button-black.selected { background-position: -40px -340px; }

.radio-button-purple {
    width: 17px;
    height: 17px;
    cursor: pointer;
    display: inline-block;
    position: relative;
    top: 2px;
    background: transparent url("../../../../../misc/img/icons_sprite.png") scroll no-repeat 0 -340px;
}

.radio-button-purple:hover {
    background-position: -20px -340px;
}

.radio-button-purple:active,
.radio-button-purple.selected { background-position: -40px -340px; }

/* caret to expand */

.expander-caret-black {
    width: 17px;
    height: 13px;
    cursor: pointer;
    display: inline-block;
    position: relative;
    background: transparent url("../../../../../misc/img/icons_sprite.png") scroll no-repeat -60px -320px;
}

.expander-caret-black:active,
.expander-caret-black.selected { background-position: -80px -320px; }

.expander-caret-purple {
    width: 17px;
    height: 13px;
    cursor: pointer;
    display: inline-block;
    position: relative;
    background: transparent url("../../../../../misc/img/icons_sprite.png") scroll no-repeat -60px -340px;
}

.expander-caret-purple:active,
.expander-caret-purple.selected { background-position: -80px -340px; }

#top-menu ul.navbar-nav li a.top-menu-login:hover .icon-top-menu-login,
#top-menu ul.navbar-nav li a.top-menu-login:active .icon-top-menu-login {
    background-position: -40px -180px;
}

#top-menu ul.navbar-nav li a,
#top-menu ul.navbar-nav li a:focus {
    color: #BBBBBB;
}

#top-menu ul.navbar-nav li a:hover,
#top-menu ul.navbar-nav li a:active {
    color: #DADADA;
}

#top-menu ul.dropdown-menu li a,
#top-menu ul.dropdown-menu li a:focus {
    outline: none;
    color: #2885D1;
    padding: 3px 25px 3px 20px;
}

#top-menu ul.dropdown-menu li a:hover {
    color: #206CAC;
    background-color: #EEEEEE;
}

#top-menu ul.dropdown-menu li a.active {
    color: #FFFFFF;
    background-color: #2885D1;
}

.navbar-nav > li > a.top-menu-login {
    color: #FFAA3b;
}

.navbar-nav > li > a.top-menu-login:hover {
    color: #FFFFFF;
}

.navbar-nav > li > a.top-menu-search:hover .icon-top-menu-search {
    opacity: 1;
}

/* --- begin: styles for third level top menu --- */
#top-menu .dropdown-submenu {
  position: relative;
}

#top-menu .dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  -webkit-border-radius: 0 6px 6px 0;
  -moz-border-radius: 0 6px 0;
  border-radius: 0 6px 6px 0;
}

#top-menu .dropdown-submenu:hover > .dropdown-menu {
  display: block;
}

#top-menu .dropdown-submenu.pull-left {
  float: none;
}

#top-menu .dropdown-submenu.pull-left > .dropdown-menu {
  left: -100%;
  margin-left: 10px;
  -webkit-border-radius: 6px 0 6px 6px;
  -moz-border-radius: 6px 0 6px 6px;
  border-radius: 6px 0 6px 6px;
}

#top-menu .dropdown-submenu > a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #2885D1;
  margin-top: 5px;
  margin-right: -15px;
}

#top-menu .dropdown-submenu-toggle.active:after, #top-menu .dropdown-submenu-toggle.active:hover:after {
  border-left-color: #FFFFFF;
}

/* --- end: styles for third level top menu ---  */

/* TODO: use icons from sprite, adjust position */
.trend-neutral {
    background: transparent url(../../../../../misc/img/icons_sprite.png) no-repeat -19px -37px;
    padding: 0 0 0 15px;
}

.trend-up {
    background: transparent url(../../../../../misc/img/icons_sprite.png) no-repeat -39px -37px;
    padding: 0 0 0 15px;
}

.trend-down {
    background: transparent url(../../../../../misc/img/icons_sprite.png) no-repeat 1px -37px;
    padding: 0 0 0 15px;
}

.kt-anchor {
    margin-top: -60px;
    display: block;
    position: absolute;
}

.kt-anchor-admin {
    position: relative;
    height: 20px;
    width: 350px;
    font-size: 10px;
    margin: 30px 0 0 0;
}

/* --- Affix anchors --- TODO: check if we use them */
div.anchor-wrapper {
    position: relative;
    display: inline-block;
}

div.anchor-wrapper .anchor {
    position: absolute;
    top: 0;
    left: 0;
    margin-top: -70px;
}



/* Klick-Tipp submit buttons with icons */

/* FIX: missing class in release candidate TODO check for bootstrap 3 updates/release
 * TODO: also check if needed
*/

.btn:focus {
    outline: none;
}

.btn-submit,
.btn-submit.active {
    background-repeat: no-repeat;
    background-position: 10px 6px;
    padding-left: 29px;
    background-image: url(../../../../../misc/img/icons_sprite.png);
    outline: none;
    text-decoration: none;
    height: 31px;
    line-height: 18px;
    vertical-align: middle;
    box-shadow: none;
}

a.btn-link:hover,
a.btn-link:focus,
a.btn-link:active {
    outline: none;
    text-decoration: none;
}

.btn-submit:active,
a.btn-submit:active {
    background-image: url(../../../../../misc/img/icons_sprite.png);
}

.btn-icon-sendmail-black, .btn-icon-sendmail-black.active {
    background-position: -292px -3px;
}

.btn-icon-tag-black, .btn-icon-tag-black.active {
    background-position: -292px -70px;
}

.btn-icon-export-black, .btn-icon-export-black.active {
    background-position: -292px -139px;
}

.btn-icon-unsubscribe-black, .btn-icon-unsubscribe-black.active {
    background-position: -291px -207px;
}

.btn-icon-delete-black, .btn-icon-delete-black.active {
    background-position: -291px -274px;
}

.btn-icon-search-black, .btn-icon-search-black.active {
    background-position: -292px -343px;
}

.btn-icon-reload-black, .btn-icon-reload-black.active {
    background-position: -292px -410px;
}

.btn-icon-cancel-black, .btn-icon-cancel-black.btn-icon-cancel-black.active {
    background-position: -291px -478px;
}

.btn-icon-arrow-black, .btn-icon-arrow-black.active {
    background-position: -292px -547px;
}

.btn-icon-edit-black, .btn-icon-edit-black.active {
    background-position: -292px -614px;
}

.btn-icon-import-black, .btn-icon-import-black.active {
    background-position: -292px -682px;
}

.btn-icon-preview-black, .btn-icon-preview-black.active {
    background-position: -291px -750px;
    padding-left: 32px;
}

.btn-icon-continue-black, .btn-icon-continue-black.active {
    background-position: -292px -818px;
}

.btn-icon-upload-black, .btn-icon-upload-black.active {
    background-position: -290px -884px;
    padding-left: 33px;
}

.btn-icon-download-black, .btn-icon-download-black.active {
    background-position: -290px -956px;
    padding-left: 33px;
}

.btn-icon-pause-black, .btn-icon-pause-black.active {
    background-position: -290px -1022px;
    padding-left: 33px;
}

.btn-icon-resume-black, .btn-icon-resume-black.active {
    background-position: -290px -1090px;
    padding-left: 33px;
}

.btn-icon-back-black, .btn-icon-back-black.active {
    background-position: -292px -1158px;
}

.btn-icon-expand-black, .btn-icon-expand-black.active {
    background-position: -290px -1226px;
}

.btn-icon-fullscreen-black, .btn-icon-fullscreen-black.active {
    background-position: -290px -1294px;
}

.btn-icon-lock-black, .btn-icon-lock-black.active {
    background-position: -292px -1361px;
}

.btn-icon-duplicate-black, .btn-icon-duplicate-black.active {
    background-position: -292px -1430px;
}

.btn-icon-contents-black, .btn-icon-contents-black.active {
    background-position: -292px -1498px;
}

.btn-icon-previous-black, .btn-icon-previous-black.active {
    background-position: -292px -1565px;
}

.btn-icon-next-black, .btn-icon-next-black.active {
    background-position: -292px -1633px;
}

.btn-icon-in-black, .btn-icon-in-black.active {
    background-position: -292px -1768px;
}

.btn-icon-out-black, .btn-icon-out-black.active {
    background-position: -292px -1839px;
}

.btn-icon-settings-black, .btn-icon-settings-black.active {
    background-position: -292px -1907px;
}

.btn-icon-add-black, .btn-icon-add-black.active {
    background-position: -292px -1975px;
}
.btn-icon-merge-black,
.btn-icon-merge-black.active {
  background-position: -292px -2042px;
}
.btn-icon-change-black,
.btn-icon-change-black.active {
  background-position: -292px -2110px;
}

.btn-icon-sendmail-black:active {
    background-position: -292px -2px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-tag-black:active {
    background-position: -292px -69px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-export-black:active {
    background-position: -292px -138px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-unsubscribe-black:active {
    background-position: -291px -206px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-delete-black:active {
    background-position: -291px -273px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-search-black:active {
    background-position: -292px -342px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-reload-black:active {
    background-position: -292px -409px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-cancel-black:active {
    background-position: -291px -477px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-arrow-black:active {
    background-position: -292px -546px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-edit-black:active {
    background-position: -292px -613px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-import-black:active {
    background-position: -292px -681px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-preview-black:active {
    background-position: -291px -749px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-continue-black:active {
    background-position: -292px -817px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-upload-black:active {
    background-position: -290px -883px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-download-black:active {
    background-position: -290px -955px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-pause-black:active {
    background-position: -290px -1021px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-resume-black:active {
    background-position: -290px -1089px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-back-black:active {
    background-position: -292px -1157px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-expand-black:active {
    background-position: -290px -1225px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-fullscreen-black:active {
    background-position: -290px -1293px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-lock-black:active {
    background-position: -292px -1360px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-duplicate-black:active {
    background-position: -292px -1429px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-contents-black:active {
    background-position: -292px -1497px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-previous-black:active {
    background-position: -292px -1564px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-next-black:active {
    background-position: -292px -1632px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-in-black:active {
    background-position: -292px -1767px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-out-black:active {
    background-position: -292px -1838px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-settings-black:active {
    background-position: -292px -1907px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-add-black:active {
    background-position: -292px -1975px;
    padding-top: 7px;
    padding-bottom: 5px;
}
.btn-icon-merge-black:active {
    background-position: -292px -2042px;
    padding-top: 7px;
    padding-bottom: 5px;
}
.btn-icon-change-black:active {
    background-position: -292px -2110px;
    padding-top: 7px;
    padding-bottom: 5px;
}
.btn-icon-sendmail-white, .btn-icon-sendmail-white.active {
    background-position: -292px -37px;
}

.btn-icon-tag-white, .btn-icon-tag-white.active {
    background-position: -292px -104px;
}

.btn-icon-export-white, .btn-icon-export-white.active {
    background-position: -292px -173px;
}

.btn-icon-unsubscribe-white, .btn-icon-unsubscribe-white.active {
    background-position: -291px -241px;
}

.btn-icon-delete-white, .btn-icon-delete-white.active {
    background-position: -291px -308px;
}

.btn-icon-search-white, .btn-icon-search-white.active {
    background-position: -292px -377px;
}

.btn-icon-reload-white, .btn-icon-reload-white.active {
    background-position: -292px -445px;
}

.btn-icon-cancel-white, .btn-icon-cancel-white.active {
    background-position: -291px -512px;
}

.btn-icon-arrow-white, .btn-icon-arrow-white.active {
    background-position: -292px -581px;
}

.btn-icon-edit-white, .btn-icon-edit-white.active {
    background-position: -292px -648px;
}

.btn-icon-import-white, .btn-icon-import-white.active {
    background-position: -292px -716px;
}

.btn-icon-preview-white, .btn-icon-preview-white.active {
    background-position: -291px -784px;
    padding-left: 32px;
}

.btn-icon-continue-white, .btn-icon-continue-white.active {
    background-position: -292px -852px;
}

.btn-icon-upload-white, .btn-icon-upload-white.active {
    background-position: -290px -918px;
    padding-left: 33px;
}

.btn-icon-download-white, .btn-icon-download-white.active {
    background-position: -290px -990px;
    padding-left: 33px;
}

.btn-icon-pause-white, .btn-icon-pause-white.active {
    background-position: -290px -1056px;
    padding-left: 33px;
}

.btn-icon-resume-white, .btn-icon-resume-white.active {
    background-position: -290px -1124px;
    padding-left: 33px;
}

.btn-icon-back-white, .btn-icon-back-white.active {
    background-position: -292px -1192px;
}

.btn-icon-expand-white, .btn-icon-optimalsize-white.active {
    background-position: -290px -1260px;
}

.btn-icon-fullscreen-white, .btn-icon-fullscreen-white.active {
    background-position: -290px -1328px;
}

.btn-icon-lock-white, .btn-icon-lock-white.active {
    background-position: -292px -1395px;
}

.btn-icon-duplicate-white, .btn-icon-duplicate-white.active {
    background-position: -292px -1464px;
}

.btn-icon-contents-white, .btn-icon-contents-white.active {
    background-position: -292px -1532px;
}

.btn-icon-previous-white, .btn-icon-previous-white.active {
    background-position: -292px -1599px;
}

.btn-icon-next-white, .btn-icon-next-white.active {
    background-position: -292px -1668px;
}

.btn-icon-in-white, .btn-icon-in-white.active {
    background-position: -292px -1803px;
}

.btn-icon-out-white, .btn-icon-out-white.active {
    background-position: -292px -1873px;
}

.btn-icon-settings-white, .btn-icon-settings-white.active {
    background-position: -292px -1941px;
}

.btn-icon-add-white, .btn-icon-add-white.active {
    background-position: -292px -2009px;
}
.btn-icon-merge-white,
.btn-icon-merge-white.active {
  background-position: -292px -2076px;
}
.btn-icon-change-white,
.btn-icon-change-white.active {
  background-position: -292px -2144px;
}

.btn-icon-sendmail-white:active {
    background-position: -292px -36px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-tag-white:active {
    background-position: -292px -103px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-export-white:active {
    background-position: -292px -172px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-unsubscribe-white:active {
    background-position: -291px -240px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-delete-white:active {
    background-position: -291px -307px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-search-white:active {
    background-position: -292px -376px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-reload-white:active {
    background-position: -292px -444px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-cancel-white:active {
    background-position: -291px -511px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-arrow-white:active {
    background-position: -292px -580px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-edit-white:active {
    background-position: -292px -647px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-import-white:active {
    background-position: -292px -715px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-preview-white:active {
    background-position: -291px -783px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-continue-white:active {
    background-position: -292px -851px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-upload-white:active {
    background-position: -290px -917px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-download-white:active {
    background-position: -290px -989px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-pause-white:active {
    background-position: -290px -1055px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-resume-white:active {
    background-position: -290px -1123px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-back-white:active {
    background-position: -292px -1191px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-expand-white:active {
    background-position: -290px -1259px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-fullscreen-white:active {
    background-position: -290px -1327px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-lock-white:active {
    background-position: -292px -1394px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-duplicate-white:active {
    background-position: -292px -1463px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-contents-white:active {
    background-position: -292px -1531px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-previous-white:active {
    background-position: -292px -1598px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-next-white:active {
    background-position: -292px -1667px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-in-white:active {
    background-position: -292px -1802px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-out-white:active {
    background-position: -292px -1872px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-settings-white:active {
    background-position: -292px -1941px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-add-white:active {
    background-position: -292px -2009px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-merge-white:active {
    background-position: -292px -2076px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-icon-change-white:active {
    background-position: -292px -2144px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.btn-grey, .btn-grey:focus, .btn-grey.active {
    background-color: #ffffff;
    border-color: #cccccc;
    color: #333333;
}

.btn-grey:hover,
.btn-grey:active {
    background-color: #EBEBEB;
    border-color: #ADADAD;
    color: #333333;
}

.btn-red, .btn-red:focus, .btn-red.active {
    background-color: #D9534F;
    border-color: #D43F3A;
    color: #FFFFFF;
}

.btn-red:hover,
.btn-red:active {
    background-color: #D2322D;
    border-color: #AC2925;
    color: #FFFFFF;
}

.btn-green, .btn-green:focus, .btn-green.active {
    background-color: #5CB85C;
    border-color: #4CAE4C;
    color: #FFFFFF;
}

.btn-green:hover,
.btn-green:active {
    background-color: #47A447;
    border-color: #398439;
    color: #FFFFFF;
}

.btn-blue, .btn-blue:focus, .btn-blue.active {
    background-color: #428BCA;
    border-color: #428BCA;
    color: #FFFFFF;
}

.btn-blue:hover,
.btn-blue:active {
    background-color: #3276B1;
    border-color: #285E8E;
    color: #FFFFFF;
}

.btn-orange, .btn-orange:focus, .btn-orange.active {
    background-color: #FC960F;
    border-color: #EC7B0D;
    color: #FFFFFF;
}

.btn-orange:hover,
.btn-orange:active {
    background-color: #FC840F;
    border-color: #E17205;
    color: #FFFFFF;
}

td .btn-table {
    margin-right: 5px;
}

td .btn-table:last-child {
    margin-right: 0px;
}


/* Google Signin Button
  IMPORTANT: Do not change the styles, @see https://developers.google.com/identity/branding-guidelines
  Note: Include Font: https://fonts.googleapis.com/css?family=Roboto:500
*/

.google-signin-button {
    position: relative;
    display: inline-block;
    width: auto;
    height: 42px;
}

.google-signin-button .google-signin-logo {
    position: absolute;
    display: block;
    left: 0;
    top: 0;
    width: 50px;
    height: 42px;
    background: transparent url(/misc/img/google_signin_button.png) no-repeat 0 0;
    z-index: 2;
}

.google-signin-button .google-signin-button-end {
    position: absolute;
    display: block;
    right: 0;
    top: 0;
    width: 8px;
    height: 42px;
    background: transparent url(/misc/img/google_signin_button.png) no-repeat -47px 0;
    z-index: 3;
}

.google-signin-button .google-signin-text {
    position: relative;
    display: inline-block;
    width: auto;
    height: 42px;
    line-height: 42px;
    background: transparent url(/misc/img/google_signin_button.png) repeat-x 0 -50px;
    margin: 0 8px 0 50px;
    z-index: 1;
    color: #ffffff;
    font-family: Roboto,arial,sans-serif;
    font-weight: 500;
    size: 14px;
    letter-spacing: .21px;
}

/* Splittest-Club Feedback Formular Button */
.stcfeed-submit,
.stcfeed-submit:focus,
.stcfeed-submit.active {
    -moz-user-select: none;
    background-color: #428BCA;
    border: 1px solid #428BCA;
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: normal;
    margin: 15px 0;
    padding: 6px 12px 6px 29px;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    white-space: nowrap;
    color: #FFFFFF;
    background-image: url("../../../../../misc/img/icons_sprite.png");
    background-position: -292px -37px;
    background-repeat: no-repeat;
    box-shadow: none;
    height: 31px;
    line-height: 18px;
    outline: medium none;
}

.stcfeed-submit:hover {
    background-color: #3276B1;
    border-color: #285E8E;
}

.stcfeed-submit:active {
    background-position: -292px -36px;
    padding-top: 7px;
    padding-bottom: 5px;
}

.stcfeed-input {
    background-color: #FFFFFF;
    background-image: none;
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    color: #555555;
    display: block;
    font-size: 14px;
    line-height: 1.42857;
    padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
    vertical-align: middle;
    width: 100%;
}

/* NPS score feedback form */

#ScoreSelection > .form-radios {
    position: relative;
    display: block;
}

#ScoreSelection > .form-radios label {
    cursor: pointer;
    color: #333333;
    font-weight: bold;
}

#ScoreSelection > .form-radios > .form-item.form-type-radio {
    width: 61px;
    height: 36px;
    display: inline-block;
    position: relative;
    padding: 6px 14px;
    float: left;
    cursor: pointer;
    line-height: 1.42857;
    color: #ffffff;
    background: transparent url("../../../../../misc/img/npscore_sprite.png") scroll no-repeat -61px 0;
}

#ScoreSelection > .form-radios > .form-item.form-type-radio.selected {
    background-position: -61px -36px;
}

#ScoreSelection > .form-radios > .form-item.form-type-radio:first-child.selected {
    background-position: 0 -36px;
}

#ScoreSelection > .form-radios > .form-item.form-type-radio:last-child.selected {
    background-position: -610px -36px;
}

#ScoreSelection > .form-radios > .form-item.form-type-radio input {
    display: none;
}

#ScoreSelection > .form-radios > .form-item.form-type-radio label > .radio-button-purple {
    top: 4px;
    margin: 0 7px 0 0;
}

#ScoreSelection > .form-radios > .form-item.form-type-radio.selected label > .radio-button-purple {
    background-position: -40px -340px;
}


#ScoreSelection > .form-radios > .form-item.form-type-radio:first-child {
    background-position: 0 0;
}

#ScoreSelection > .form-radios > .form-item.form-type-radio:last-child {
    padding: 6px 11px;
    background-position: -610px 0;
}

#ScoreSelection > .form-radios > .form-item.form-type-radio:last-child label > .radio-button-purple {
    margin: 0 6px 0 0;
}



/*TableSort*/
.icon-tablesort-up {
    background-position: -15px -55px;
}

a:hover .icon-tablesort-up {
    background-position: -55px -55px;
}

.icon-tablesort-down {
    background-position: 5px -55px;
}

a:hover .icon-tablesort-down {
    background-position: -35px -55px;
}

/*Tooltip*/
.tooltip-inner {
    max-width: 400px;
}

.tooltip.fade.in {
  z-index: 2010;
}

/*
--------------------------------------------------
  Sidebar
--------------------------------------------------
*/

/* TODO: get old styles from bootstrap 2.3 docs sidenav */

#sidebar {
    padding: 0;
    margin: 0;
}

/* on scroll position 0, affix is placed relative at top: 0 in the container */
#sidebar .sidenav.affix-top {
    top: 0px;;
}

/* on scrolling, affix is placed fixed at top: 85px related to the document */
#sidebar .sidenav.affix {
    top: 80px;;
}

#sidebar .sidenav.affix-bottom {
    position: absolute;
}

#sidebar .sidenav {
    margin-top: 0px;
    background-color: #FFFFFF;
    border-radius: 6px 6px 6px 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.067);
    margin: 0;
    padding: 0;
    width: 191px;
}

#sidebar .sidenav li a {
    padding-left: 10px;
    padding-right: 10px;
    border: 1px solid #E5E5E5;
    border-bottom: none;
}

#sidebar .sidenav li.active a,
#sidebar .sidenav li.active a.active {
    background-color: #2885d1;
    color: #FFFFFF;
}

#sidebar .sidenav li:first-child a {
    border-radius: 6px 6px 0 0;
}

#sidebar .sidenav li:last-child a {
    border-radius: 0 0 6px 6px;
    border-bottom: 1px solid #E5E5E5;
}

/*
--------------------------------------------------
  Footer
--------------------------------------------------
*/

.footer {
    text-align: center;
    padding: 30px 0;
    margin-top: 70px;
    border-top: 1px solid #e5e5e5;
    background-color: #f5f5f5;
}

#footer-menu ul.navbar-nav li a,
#footer-menu ul.navbar-nav li a:focus,
.navbar-inverse .navbar-text {
    color: #BBBBBB;
}

#footer-menu ul.navbar-nav li a:hover,
#footer-menu ul.navbar-nav li a:active {
    color: #DADADA;
}

#footer-menu.with-help-block {
    border-left: none;
    border-right: none;
    border-top-color: #bfdaed;
}

#footer-menu #help-block .container {
    padding: 0 15px;
}

body.has-sidebar #footer-menu #help-block .container {
    padding: 0;
}
#footer-menu #help-block {
    background-color: #f1f8fd;
    border-bottom: 1px solid #222;
    padding: 30px 0;
}

#footer-menu #help-block li.excluded,
#footer-menu #help-block .help-block-show-referrers,
#footer-menu #help-block .help-block-admin-referrers {
    display: none;
}

#footer-menu #help-block .help-block-admin-referrer-path.excluded {
    opacity: 0.5;
}

#footer-menu #help-block.help-block-editing li.excluded,
#footer-menu #help-block.help-block-editing .help-block-admin-referrers {
    display: block;
}

#footer-menu #help-block.help-block-editing .help-block-show-referrers {
    display: inline-block;
}

#footer-menu #help-block li i.help-edit-icon {
    position: relative;
    display: none;
    margin-left: 5px;
    cursor: pointer;
}
#footer-menu #help-block.help-block-editing li i.help-edit-icon { display: inline-block; }


.contact-help-block h3,
#footer-menu #help-block h3 {
    margin: 0 0 12px 0;
    color: #333333;
    font-size: 18px;
    font-weight: bold;
}

.contact-help-block ul,
#footer-menu #help-block ul {
    padding: 0;
    list-style: none;
}

.contact-help-block ul {
    margin-bottom: 0;
}

.contact-help-block li,
#footer-menu #help-block li {
    margin: 0;
    padding: 3px 0 4px 25px;
    border-bottom: 1px solid #dcdfe1;
    line-height: 20px;
    vertical-align: top;
}

.contact-help-block li {
    border: none;
}

.contact-help-block li .icon-link,
#footer-menu #help-block li .icon-link,
#footer-menu #help-block li .icon-video-link {
    margin: 4px 5px 0 -25px;
    line-height: 20px;
    vertical-align: top;
}

#footer-menu #help-block li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.contact-help-block a,
.contact-help-block a:active,
.contact-help-block a:visited,
.contact-help-block a:focus,
#footer-menu #help-block a,
#footer-menu #help-block a:active,
#footer-menu #help-block a:visited,
#footer-menu #help-block a:focus {
    display: inline-block;
    color: #2885d1;
    padding: 0;
    margin: 0;
    line-height: 20px;
    vertical-align: middle;
    font-size: 14px;
    text-decoration: none;
    outline: none;
}

.contact-help-block a:hover,
#footer-menu #help-block a:hover {
    outline: none;
    text-decoration: underline;
    color: #206cac;
}

#footer-menu #help-block p.contact-form-link {
    padding-top: 10px;
    margin-bottom: 0;
    font-size: 14px;
    line-height: 20px;
}

#footer-menu #help-block p.contact-form-link a {
    vertical-align: top;
}

/* --- help overview --- */

.help-overview ul.help-overview-links {
    padding-left: 0;
}

.help-overview li {
    position: relative;
    list-style-type: none;
    padding-left: 25px;
}

.help-overview li .icon-link {
    position: absolute;
    left: 0;
    top: 3px;
}

.help-overview li.help-overview-links-more {
    padding: 5px 0px;
}

/* --- help feedback form --- */

.help-article-feedback-box {
    border-radius: 5px;
    border: 1px solid #cccccc;
    background-color: #fafafa;
    padding: 25px;
    margin-top: 35px;
    margin-bottom: 35px;
}

.help-article-feedback-box #helpful-yes,
.help-article-feedback-box #helpful-no {
    width: 60px;
}

.help-article-feedback-box h4 {
    font-size: 16px;
    margin: 0 0 15px 0;
}

.help-article-feedback-box h5 {
    margin-top: 0;
}

.help-article-feedback-box .button-row {
    margin-bottom: 0;
}

.help-article-feedback-box textarea,
.help-article-feedback-box label {
    display: block;
    width: 100%;
}

.help-article-feedback-box .btn-blue,
.help-article-feedback-box .btn-blue:focus,
.help-article-feedback-box .btn-blue.active {
    background-color: #5CB85C;
    border-color: #4CAE4C;
    color: #FFFFFF;
}

.help-article-feedback-box .btn-blue:hover,
.help-article-feedback-box .btn-blue:active {
    background-color: #47A447;
    border-color: #398439;
    color: #FFFFFF;
}


/*
 * jQuery UI Datepicker 1.9.0
 *
 * Copyright 2012-10-11, AUTHORS.txt (http://jqueryui.com/about)
 * Dual licensed under the MIT or GPL Version 2 licenses.
 * http://jquery.org/license
 *
 * http://jqueryui.com/datepicker/
 */
.ui-datepicker {
    display: none;
    padding: 0.2em 0.2em 0;
    width: 17em;
}

.ui-datepicker.ui-widget {
    z-index: 9999 !important;
}

.ui-datepicker .ui-datepicker-header {
    background-color: #F5F5F5;
    border: 0 none;
    color: #808080;
    font-weight: bold;
    padding: 4px 0;
    position: relative;
    width: 100%;
}

.ui-datepicker .ui-datepicker-prev, .ui-datepicker .ui-datepicker-next {
    height: 1.8em;
    position: absolute;
    top: -3px;
    width: 1.8em;
}

.ui-datepicker .ui-datepicker-prev-hover, .ui-datepicker .ui-datepicker-next-hover {
}

.ui-datepicker .ui-datepicker-prev {
    left: 2px;
}

.ui-datepicker .ui-datepicker-next {
    right: 2px;
}

.ui-datepicker .ui-datepicker-prev-hover {
}

.ui-datepicker .ui-datepicker-next-hover {
}

.ui-datepicker .ui-datepicker-prev span, .ui-datepicker .ui-datepicker-next span {
    display: block;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    position: absolute;
    top: 50%;
}

.ui-datepicker .ui-datepicker-title {
    line-height: 1.8em;
    margin: 0 2.3em;
    text-align: center;
}

.ui-datepicker .ui-datepicker-title select {
    font-size: 1em;
    font-weight: normal;
    margin: 1px 0;
}

.ui-datepicker select.ui-datepicker-month-year {
    width: 100%;
}

.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {
    width: 49%;
}

.ui-datepicker table {
    border-collapse: collapse;
    font-size: 0.9em;
    margin: 0 0 0.4em;
    width: 100%;
}

.ui-datepicker th {
    border: 0 none;
    font-weight: bold;
    padding: 0.7em 0.3em;
    text-align: center;
}

.ui-datepicker td {
    border: 0 none;
    padding: 1px;
}

.ui-datepicker td span, .ui-datepicker td a {
    display: block;
    padding: 0.2em;
    text-align: right;
    text-decoration: none;
}

.ui-datepicker .ui-datepicker-buttonpane {
    background-image: none;
    border-bottom: 0 none;
    border-left: 0 none;
    border-right: 0 none;
    margin: 0.7em 0 0;
    padding: 0 0.2em;
}

.ui-datepicker .ui-datepicker-buttonpane button {
    cursor: pointer;
    float: right;
    margin: 0.5em 0.2em 0.4em;
    overflow: visible;
    padding: 0.2em 0.6em 0.3em;
    width: auto;
}

.ui-datepicker .ui-datepicker-buttonpane button.ui-datepicker-current {
    float: left;
}

.ui-datepicker.ui-datepicker-multi {
    width: auto;
}

.ui-datepicker-multi .ui-datepicker-group {
    float: left;
}

.ui-datepicker-multi .ui-datepicker-group table {
    margin: 0 auto 0.4em;
    width: 95%;
}

.ui-datepicker-multi-2 .ui-datepicker-group {
    width: 50%;
}

.ui-datepicker-multi-3 .ui-datepicker-group {
    width: 33.3%;
}

.ui-datepicker-multi-4 .ui-datepicker-group {
    width: 25%;
}

.ui-datepicker-multi .ui-datepicker-group-last .ui-datepicker-header {
    border-left-width: 0;
}

.ui-datepicker-multi .ui-datepicker-group-middle .ui-datepicker-header {
    border-left-width: 0;
}

.ui-datepicker-multi .ui-datepicker-buttonpane {
    clear: left;
}

.ui-datepicker-row-break {
    clear: both;
    font-size: 0;
    width: 100%;
}

.ui-datepicker-rtl {
    direction: rtl;
}

.ui-datepicker-rtl .ui-datepicker-prev {
    left: auto;
    right: 2px;
}

.ui-datepicker-rtl .ui-datepicker-next {
    left: 2px;
    right: auto;
}

.ui-datepicker-rtl .ui-datepicker-prev:hover {
    left: auto;
    right: 1px;
}

.ui-datepicker-rtl .ui-datepicker-next:hover {
    left: 1px;
    right: auto;
}

.ui-datepicker-rtl .ui-datepicker-buttonpane {
    clear: right;
}

.ui-datepicker-rtl .ui-datepicker-buttonpane button {
    float: left;
}

.ui-datepicker-rtl .ui-datepicker-buttonpane button.ui-datepicker-current {
    float: right;
}

.ui-datepicker-rtl .ui-datepicker-group {
    float: right;
}

.ui-datepicker-rtl .ui-datepicker-group-last .ui-datepicker-header {
    border-left-width: 1px;
    border-right-width: 0;
}

.ui-datepicker-rtl .ui-datepicker-group-middle .ui-datepicker-header {
    border-left-width: 1px;
    border-right-width: 0;
}

.ui-datepicker-cover {
    display: block;
    height: 200px;
    left: -4px;
    position: absolute;
    top: -4px;
    width: 200px;
    z-index: -1;
}

.ui-datepicker th {
    color: gray;
    font-weight: bold;
}

.ui-datepicker-today a:hover {
    background-color: #808080;
    color: #FFFFFF;
}

.ui-datepicker-today a {
    background-color: #BFBFBF;
    cursor: pointer;
    margin-bottom: 0;
    padding: 0 4px;
}

.ui-datepicker td a {
    border: 0 none;
    margin-bottom: 0;
}

.ui-datepicker td:hover {
    color: #FFFFFF;
}

.ui-datepicker td .ui-state-default {
    background: none repeat scroll 0 0 transparent;
    border: 0 none;
    color: gray;
    filter: none;
    margin-bottom: 0;
    padding: 5px;
    text-align: center;
    box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
}

/*background: -moz-linear-gradient(center top , #FCEEC1, #EEDC94) repeat-x scroll 0 0 #EEDC94;*/
.ui-datepicker td .ui-state-highlight {
    background: #fceec1; /* Old browsers */
    background: -moz-linear-gradient(top, #fceec1 0%, #eedc94 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #fceec1), color-stop(100%, #eedc94)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #fceec1 0%, #eedc94 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #fceec1 0%, #eedc94 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #fceec1 0%, #eedc94 100%); /* IE10+ */
    background: linear-gradient(to bottom, #fceec1 0%, #eedc94 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fceec1', endColorstr='#eedc94', GradientType=0); /* IE6-9 */
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    border-radius: 4px 4px 4px 4px;
    color: #404040;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

.ui-datepicker td .ui-state-active {
    background: none repeat scroll 0 0 #BFBFBF;
    border-radius: 4px 4px 4px 4px;
    color: #FFFFFF;
    margin-bottom: 0;
}

/*background: -moz-linear-gradient(center top , #049CDB, #0064CD) repeat-x scroll 0 0 #0064CD;*/
.ui-datepicker td .ui-state-hover {
    background: #049cdb; /* Old browsers */
    background: -moz-linear-gradient(top, #049cdb 0%, #0064cd 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #049cdb), color-stop(100%, #0064cd)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #049cdb 0%, #0064cd 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #049cdb 0%, #0064cd 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #049cdb 0%, #0064cd 100%); /* IE10+ */
    background: linear-gradient(to bottom, #049cdb 0%, #0064cd 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#049cdb', endColorstr='#0064cd', GradientType=0); /* IE6-9 */
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    border-radius: 4px 4px 4px 4px;
    color: #FFFFFF;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}

/*TODO icon needed for prev, next arrows, JAN*/
.ui-datepicker-header span.ui-icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-top: 0px;
    *margin-right: .3em;
    line-height: 14px;
    vertical-align: text-top;
    background-image: url("../../../../../misc/img/icons_sprite.png");
    background-position: 14px 14px;
    background-repeat: no-repeat;
    overflow: hidden;
    text-indent: -99999px;
    cursor: pointer;
}

.ui-datepicker-header span.ui-icon-circle-triangle-w {
    background-position: -20px -160px;
}

.ui-datepicker-header span.ui-icon-circle-triangle-e {
    background-position: 0px -160px;
}

.ui-corner-all {
    border-radius: 4px 4px 4px 4px;
}

.ui-widget-content {
    background: url("../../../../../misc/img/datepicker_bg.png") repeat-x scroll 50% 50% #FFFFFF;
    border: 1px solid #AAAAAA;
    color: #404040;
}

.ui-datepicker tbody {
    border: none;
}

/* KLICKTIPP DIALOGS */
/* general */

.mouse-normal {
    cursor: default;
}

/* buttons have no margins, if in a button row, add a margin to the right */
.button-row {
    margin-bottom: 15px;
}

.button-row .btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

/*general form styles*/
form input[type=text],
form input[type=password],
form .textfield-suffix,
form .klicktipp-item,
form#klicktipp-email-edit-form textarea.form-control {
    width: 50%;
    margin: 0;
}

form .form-control.klicktipp-item.tagging-pixel-snippet,
form .form-control.klicktipp-item.conversion-pixel-snippet{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


form#klicktipp-tools-gif-maker-create-form .gif-maker-sequence .form-group {
  float: left;
  margin-right: 15px;
}

form#klicktipp-tools-gif-maker-create-form .gif-maker-sequence .form-control.video-start-time {
  width: 8em;
}

form#klicktipp-tools-gif-maker-create-form .gif-maker-sequence .form-control.gif-duration {
  width: 4em;
}


form td input[type="text"] {
    width: 100%;
}

form div.klicktipp-magicselect {
    width: 100%;
}

form div.klicktipp-magicselect-short {
    width: 50%;
}

.klicktipp-magicselect.ms-tag-color-positive .ms-sel-item,
.klicktipp-magicselect.ms-tag-color-positive .ms-sel-item:hover {
    background-color: #47a447;
    color: #ffffff;
}

.klicktipp-magicselect.ms-tag-color-negative .ms-sel-item,
.klicktipp-magicselect.ms-tag-color-negative .ms-sel-item:hover {
    background-color: #d2322d;
    color: #ffffff;
}

.klicktipp-magicselect.ms-tag-color-positive .ms-sel-ctn .ms-sel-item .ms-close-btn,
.klicktipp-magicselect.ms-tag-color-negative .ms-sel-ctn .ms-sel-item .ms-close-btn {
    background-image: url("../../../../../misc/img/magicselect_item_close.png");
}

/* Bootstrap select selectable optgroup */

.kt-optgroup-select-wrapper {
    display: block;
}

.kt-optgroup-select .selected-option {
    min-width: 150px;
    text-align: left
}

.kt-optgroup-select .dropdown-toggle {
    background-color: #eeeeee;
    color: #999999;
}

.kt-optgroup-select button.btn-default.dropdown-toggle:hover,
.kt-optgroup-select button.btn-default.dropdown-toggle:active,
.btn-group.open.kt-optgroup-select button.btn-default.dropdown-toggle {
    background: #eeeeee -moz-linear-gradient(top, #f1f1f1, #e3e3e3);
    background: #eeeeee -webkit-linear-gradient(top, #f1f1f1, #e3e3e3);
    background: #eeeeee linear-gradient(to bottom, #f1f1f1, #e3e3e3);
    color: #333333;
    box-shadow: none;
    border-color: #cccccc;
}

.kt-optgroup-select .selected-option:hover {
    background-color: #ffffff;
    border-color: #cccccc;
}

.kt-optgroup-select ul.dropdown-menu {
    max-height: 210px;
    overflow-y: scroll;
}

.kt-optgroup-select ul.dropdown-menu li.option-group > a{
    font-weight: bold;
}

.kt-optgroup-select ul.dropdown-menu li.option {
    padding-left: 10px;
}

form input[type="text"].error,
form input[type="text"].error,
textarea.error,
select.error,
.kt-optgroup-select.error button {
    border-color: #EE5555;
}

/* validation error for CKEditors */
textarea.jquery_ckeditor.error + span[id^="cke_edit"] {
    border-color: #EE5555;
}

textarea.cke-textarea {
    height: 400px;
}

.ktfanpage-cke-wrapper {
    padding-top: 15px;
}

body.has-sidebar form input[type=text],
body.has-sidebar form input[type=password],
body.has-sidebar form .textfield-suffix,
body.has-sidebar form .klicktipp-item,
body.has-sidebar form#klicktipp-email-edit-form textarea.form-control {
    width: 67%;
}

body.has-sidebar form .klicktipp-grid-row .col-md-6 input[type=text] {
    width: 100%;
}

body.has-sidebar form input[type=text].input-short,
body.has-sidebar form input[type=password].input-short,
body.has-sidebar form .klicktipp-item.input-short,
.input-group.input-short {
    width: 100px;
}

body.has-sidebar form div.klicktipp-magicselect {
    width: 100%;
}

body.has-sidebar form div.klicktipp-magicselect-short {
    width: 67%;
}

form select.form-control {
    margin: 0;
    width: auto;
    max-width: 50%;
    background-color: #FFFFFF;
    padding: 6px 11px 6px 8px;
    font-size: 15px;
}

form select.form-control option.option-group {
    font-weight: bold;
}

form select.form-control option.option {
    padding-left: 10px;
}

form div.input-group.select select.form-control {
    margin-left: -3px;
}

form div.input-group.select .input-group-addon {
    padding: 6px 13px 6px 12px;
}

form select.form-control option {
    padding-right: 10px;
}

body.has-sidebar form select.form-control {
    max-width: 67%;
}

form label {
    margin: 0 0 5px 0;
    cursor: default;
}

form input.magicselect {
    width: 100%;
    max-width: none;
    visibility: hidden;
}

form p {
    margin-bottom: 20px;
}

form dl dt.item-label {
    display: inline-block;
    width: 25%;
    padding-right: 15px;
    float: left;
}

form dl dd.item-value {
    display: inline-block;
    width: 75%;
}

form div.input-group {
    width: 50%;
}

body.has-sidebar div.input-group {
    width: 67%;
}

form .field-suffix {
    display:inline-block;
}

body.has-sidebar form .field-suffix input[type=text],
form .field-suffix form .field-suffix input[type=text]{
    width:100%;
}

.element-row > * {
    display: inline-block;
    margin: 0;
    vertical-align: middle;
}

.element-row label {
    margin: 0 0 5px 0;
    cursor: default;
    display: block;
}

.element-row input[type=text],
form .element-row input[type=text],
.element-row .klicktipp-item,
form .element-row .klicktipp-item {
    width: 100%;
    max-width: none;
}

form input[type=text].input-short,
form input[type=password].input-short,
form td .element-row .input-short,
body.has-sidebar form input[type=text].input-short,
body.has-sidebar form input[type=password].input-short,
body.has-sidebar form td .element-row .input-short {
    width: 100px;
}

form input[type=text].input-medium,
form input[type=password].input-medium,
form td .element-row .input-medium,
form .klicktipp-item.input-medium,
body.has-sidebar form input[type=text].input-medium,
body.has-sidebar form input[type=password].input-medium,
body.has-sidebar form td .element-row .input-medium,
body.has-sidebar form .klicktipp-item.input-medium,
body.has-sidebar form .klicktipp-grid-row .col-md-12 .klicktipp-item.input-medium-large {
    width: 33%;
}

form input[type=text].input-medium-large,
form input[type=password].input-medium-large,
form td .element-row .input-medium-large,
form .klicktipp-item.input-medium-large,
body.has-sidebar form input[type=text].input-medium-large,
body.has-sidebar form input[type=password].input-medium-large,
body.has-sidebar form td .element-row .input-medium-large,
body.has-sidebar form .klicktipp-item.input-medium-large,
body.has-sidebar form .klicktipp-grid-row .col-md-6 .klicktipp-item.input-medium-large {
    width: 75%;
}

form input[type=text].input-large,
form input[type=password].input-large,
form td .element-row .input-large,
form .klicktipp-item.input-large,
body.has-sidebar form input[type=text].input-large,
body.has-sidebar form input[type=password].input-large,
body.has-sidebar form td .element-row .input-large,
body.has-sidebar form .klicktipp-item.input-large {
    width: 100%;
}

.label-for-inline-select {
    padding-top: 7px;
}

body.has-sidebar form .klicktipp-grid-row .subscriber-profile-image {
    height: 205px;
    width: auto;
    margin-bottom: 5px;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 4px;
    display: inline-block;
    line-height: 1.42857;
    max-width: 100%;
    padding: 4px;
    transition: all 0.2s ease-in-out 0s;
}

body.has-sidebar form .klicktipp-grid-row .social-media-icon {
    height: auto;
    margin: 0 4px 4px 0;
    border-radius: 4px;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    display: inline-block;
    line-height: 1.42857;
    max-width: 100%;
    padding: 4px;
    transition: all 0.2s ease-in-out 0s;
}

/* subscriber search ajax waiting throbber */

.subscriber-search-throbber-counts,
.subscriber-search-throbber-pager {
  position: relative;
  width:16px;
  height:34px;
  display: inline-block;
  background: url("../../../../../misc/img/throbber.gif") 0px 34px;
}

.subscriber-search-throbber-counts {
  top: 12px;
  margin-top: -34px;
}

.subscriber-search-throbber-pager {
  left:50%;
  margin-bottom: 15px;
  margin-left: -8px;
}


/* pager page size */

.klicktipp-pager {
    margin-bottom: 20px;
}

.klicktipp-pager .pagination-wrapper {
    text-align: center;
}

.klicktipp-pager .pagination {
    margin: 0;
}

.pager-pagesize {
    margin: 0 0 7px 0;
    min-height: 18px;
}

.pager-pagesize:last-child {
    margin: 0px;
}

.pager-pagesize .pager-pagesize-items {
    display: block;
    text-align: center;
}

.pager-pagesize .pager-pagesize-items span.badge,
.pager-pagesize .pager-pagesize-items a.badge {
    margin-left: 3px;
}

.pager-pagesize .pager-pagesize-items span.badge:first-child,
.pager-pagesize .pager-pagesize-items a.badge:first-child {
    margin-left: 0px;
}

a.badge.pager-size-link {
    background-color: #2885d1;
    color: #ffffff;
}

a.badge.pager-size-link:hover {
    background-color: #206cac;
}

span.badge.pager-size-active {
    background-color: #cccccc;
    color: #ffffff;
}

td span.splittestlabel {
    display: inline-block;
    vertical-align: top;
    padding: 0.4em 0.6em;
    margin-left: 10px;
}

td span.ar-status {
    display: inline-block;
    vertical-align: top;
    padding: 0.4em 0.6em;
}

.tagged-labels {
    font-size: 1.3em;
    height: auto;
    padding-bottom: 9px;
}

.tag-as-label {
    display: inline-block;
    margin-right: 6px;
}

.tag-as-label.positive {
    background-color: #47a447;
}

.tag-as-label.negative {
    background-color: #d2322d;
}

.ar-status.label-default {
    background-color: #999999;
}

.ar-status.label-warning {
    background-color: #E16A00;
}

.ar-status.label-success {
    background-color: #35CB6A;
}

.ar-status.label-info {
    background-color: #539AC9;
}

textarea {
    width: 100%;
    resize: none;
}

/* KlickTipp Search */

.klicktipp-search-item {
    margin: 0 0 20px 0;
}

.klicktipp-search-item .klicktipp-search-item-title,
.klicktipp-search-item .klicktipp-search-item-snippet {
    margin: 0;
}

.klicktipp-search-item .klicktipp-search-item-url {
    margin: 0;
    color: #006621;
}

/*email/campaign dialog*/

/* edit email: copy content, background for autocomplete */

#autocomplete {
    position: absolute;
    max-height: 300px;
    overflow: auto;
    background-color: #FFFFFF;
    border: 1px solid #555555;
    z-index: 500;
}

#autocomplete ul {
    padding: 5px;
}

#autocomplete ul li {
    padding: 0px 5px;
    cursor: default;
}

#autocomplete ul li:hover {
    background-color: #3875D7;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#3875D7', endColorstr='#2A62BC', GradientType=0);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, color-stop(20%, #3875D7), color-stop(90%, #2A62BC));
    background-image: -webkit-linear-gradient(top, #3875D7 20%, #2A62BC 90%);
    background-image: -moz-linear-gradient(top, #3875D7 20%, #2A62BC 90%);
    background-image: -o-linear-gradient(top, #3875D7 20%, #2A62BC 90%);
    background-image: linear-gradient(#3875D7 20%, #2A62BC 90%);
    color: #fff;
}

#autocomplete ul li .copy-email-highlight {
    font-weight: bold;
    color: #000000;
}

#autocomplete ul li:hover .copy-email-highlight {
    color: #ffc016;
}

#autocomplete ul li .copy-email-name {
    display: block;
    font-weight: normal;
    font-size: 14px;
}

#autocomplete .copy-email-subject {
    display: block;
    font-weight: normal;
    font-size: 12px;
    color: #777777;
    padding-bottom: 5px;
}

#autocomplete ul li:hover span {
    color: #ffffff;
}

html.js input.form-autocomplete {
    background: #FFFFFF url("../../../../../misc/img/throbber.gif") no-repeat scroll 99% 0px;
}

html.js input.form-autocomplete.throbbing {
    background-position: 99% -34px;
}

.edit-DeactivateCKEditor-wrapper {
    margin-top: -15px;
}

.klicktipp-block.campaign-statistics-actions .form-group {
    font-weight: bold;
    font-size: 20px;
    margin-bottom: 0;
}

.klicktipp-block.campaign-statistics-box {
    background-color: #F7F7F9;
    border: 1px solid #DDDDDD;
    padding-top: 15px;
}

.klicktipp-block.campaign-statistics-box .form-group {
    margin-bottom: 5px;
}

/* weekday checkboxes */

#TriggerTimeDayOfWeekTable > .form-checkboxes {
    position:relative;
    display: block;
}

#TriggerTimeDayOfWeekTable > .form-checkboxes label {
    cursor: pointer;
}

#TriggerTimeDayOfWeekTable > .form-checkboxes > .form-item.form-type-checkbox {
    position: relative;
    display: inline-block;
    cursor: pointer;
    border: 1px solid #ac2925;
    border-right: 0;
    padding: 6px 12px;
    float: left;
    line-height: 1.42857;
    background-color: #d2322d;
    color: #ffffff;
}

#TriggerTimeDayOfWeekTable > .form-checkboxes > .form-item.form-type-checkbox.weekday-selected:hover {
    background-color: #47a447;
    border-color: #398439;
}

#TriggerTimeDayOfWeekTable > .form-checkboxes > .form-item.form-type-checkbox:first-child {
    border-radius: 4px 0 0 4px;
    -moz-border-radius: 4px 0 0 4px;
    -webkit-border-radius: 4px 0 0 4px;
}

#TriggerTimeDayOfWeekTable > .form-checkboxes > .form-item.form-type-checkbox:last-child {
    border-radius: 0 4px 4px 0;
    -moz-border-radius: 0 4px 4px 0;
    -webkit-border-radius: 0 4px 4px 0;
    border-right: 1px solid #dddddd;
}

#TriggerTimeDayOfWeekTable > .form-checkboxes > .form-item.form-type-checkbox.weekday-selected {
    background-color: #5cb85c;
    border-color: #398439;
    color: #fff;
}

#TriggerTimeDayOfWeekTable > .form-checkboxes > .form-item.form-type-checkbox input.form-checkbox {
    display: none;
}

#DayOfWeekTimeDelayTable span.time {
    position: relative;
    display: inline-block;
    line-height: 1.42857;
    padding: 6px 12px 6px 7px;
    vertical-align: top;
    margin: 0;
}

/* Enhanced date */

.enhanced-date {
    margin-bottom: 10px;
}

.enhanced-date > * {
    display: inline-block;
    margin: 0;
    vertical-align: bottom;
}

.enhanced-date label {
    margin-bottom: 5px;
    display: block;
}

.enhanced-date label .quickhelp-icon {
    position: relative;
    margin: 2px 0 0 5px;
}

.enhanced-date div:first-child {
    width: 180px;
}

.enhanced-date div select,
.enhanced-date div select.form-control,
form .enhanced-date select.form-control,
form .element-row select.form-control {
    width: 100%;
    max-width: none;
    margin: 0;
}

.enhanced-date div input {
    margin: 0;
    width: 100px;
    text-align: center;
}

.enhanced-date div:first-child + div input,
.enhanced-date label:first-child + div + div input {
    position: relative;
    margin-bottom: 0;
    vertical-align: middle;
}

.enhanced-date div:first-child + div + div,
.enhanced-date label:first-child + div + div + div {
    display: inline-block;
    width: auto;
    height: 34px;
    min-width: 16px;
    padding: 0px;
    margin: 0px 9px 0px 5px;
    font-size: 14px;
    font-weight: normal;
    line-height: 32px;
    text-align: center;
    vertical-align: middle;
}

.enhanced-date div:first-child + div + div + div input,
.enhanced-date label:first-child + div + div + div + div input {
    position: relative;
    margin-bottom: 0;
    vertical-align: middle;
}

.cf-time,
.select-time {
    position: relative;
    display: inline-block;
}

.cf-time select,
form .enhanced-date .cf-time select.form-control {
    display: inline-block;
    width: 65px;
    margin-bottom: 0;
    vertical-align: middle;
}

.select-time > div {
    display: inline-block;
    width: 65px;
    vertical-align: middle;
}

.select-time label {
    display: block;
}

.select-time label .quickhelp-icon {
    position: absolute;
    right: auto;
    margin-left: 10px;
}

.select-time > div {
    display: inline-block;
    width: auto;
}

.select-time > div > select,
body.has-sidebar form .select-time > div > select.form-control {
    width: auto;
    max-width: none;
}

.cf-time span,
.select-time span {
    display: inline-block;
    width: auto;
    height: 34px;
    min-width: 14px;
    padding: 0px;
    margin: 0px 9px 0px 5px;
    font-size: 14px;
    font-weight: normal;
    line-height: 32px;
    text-align: center;
    vertical-align: middle;
}

.select-time span.time {
    vertical-align: top;
}

/* Klicktipp custom field widget datetime */
.klicktipp-widget-datetime {
    display: block;
}

.klicktipp-widget-datetime .form-group {
    display: inline-block;
}

.klicktipp-widget-datetime .form-control.klicktipp-datepicker {
    display: inline-block;
    width: 100px;
}

.klicktipp-widget-datetime .datetime-hours,
.klicktipp-widget-datetime select.form-control.datetime-hours {
    display: inline-block;
    width: auto;
    max-width: none;
}

.klicktipp-widget-datetime .datetime-minutes,
.klicktipp-widget-datetime select.form-control.datetime-minutes {
    display: inline-block;
    width: auto;
    max-width: none;
}

.klicktipp-widget-datetime span.datetime {
    padding: 0px;
    margin: 0px 5px;
}

.klicktipp-widget-datetime-search.enhanced-date .klicktipp-widget-datetime {
    display: inline-block;
    width: auto;
}

.klicktipp-widget-datetime-search.enhanced-date .search-and {
    padding: 0px;
    margin: 0px 8px;
    width: auto;
    height: 34px;
    font-size: 14px;
    font-weight: bold;
    line-height: 32px;
    text-align: center;
    vertical-align: middle;
}

/* Klicktipp custom field widget dropdown */

table.edit-dropdown-options-table {
    margin-bottom: 15px;
}

table.edit-dropdown-options-table .input-dropdown-option {
    width: 230px;
    margin: 0 10px 5px 0;
    height: 31px;
}

table.edit-dropdown-options-table .btn-remove-dropdown-option {
    padding-left: 18px;
    margin: 0 0 5px 0;
}

form .row .row-element input,
body.has-sidebar form .row .row-element input {
    width: 100%;
    max-width: none;
}

form .row .row-element select,
body.has-sidebar form .row .row-element select {
    width: auto;
    max-width: none;
}

form .row .row-element {
    margin-right: -25px;
}

form .row .row-element.hide-label label {
    visibility: hidden;
    height: 16px;
}

/* button next to a select - @see: email line break */

form .linebreak-combo {
    position: relative;
    padding-top: 25px;
}

form .linebreak-combo > div {
    display: inline-block;
    vertical-align: top;
}

form .linebreak-combo label {
    position: absolute;
    left: 0;
    top: 0;
}

body.has-sidebar form .linebreak-combo select,
form .linebreak-combo select {
    width: 100%;
    max-width: none;
}

form .linebreak-combo .linebreak-button .btn {
    height: 34px;
}

/* button next to a textfield for CKFinder select image url */

form .ckfinder-select {
    position: relative;
    padding-top: 25px;
}

form .ckfinder-select > div {
    display: inline-block;
    vertical-align: top;
    width: 50%;
}

form .ckfinder-select label {
    position: absolute;
    left: 0;
    top: 0;
}

form .ckfinder-select input,
body.has-sidebar form .ckfinder-select input {
    width: 100%;
}

form .ckfinder-select .btn {
    height: 34px;
}

/* Slider */
form .slider {
    display: block;
}

.slider .tooltip-inner {
    margin-top: -20px;
}

/* remove max width from dropdown */

.select-no-max-width select {
  width: auto !important;
  max-width: 400px !important;
}

#ajax-wrapper .admin-menu-icon {
  display: none;
}

/* Listbuilding Icons */

.klicktipp-icon-grid {
    position: relative;
    display: block;
    text-align: center;
}

.klicktipp-icon-grid-title {
    position: relative;
    display: block;
    background: #ffffff url(https://assets.klicktipp.com/content_includes/frontpage/divider-02-mit-verlauf-zum-rand.png) no-repeat scroll center center;
    margin: 40px 0 50px 0;
}

.klicktipp-icon-grid-title h3 {
    position: relative;
    display: inline-block;
    text-align: center;
    margin: 0;
    padding: 0 15px;
    font-size: 32px;
    line-height: 29px;
    vertical-align: middle;
    background-color: #ffffff;
}

.klicktipp-icon-grid-icons {
    position: relative;
    display: inline-block;
}

.listbuilding-icon-title {
    text-align: center;
    font-size: 15px;
}

.listbuilding-icon-wrapper {
    position: relative;
    display: inline-block;
    margin: 0 10px;
}

.listbuilding-icon-title .quickhelp-icon {
    margin: 2px 0 0 5px;
    position: relative;
}

[class^="kt-icon-"],
[class^="stc-icon-"] {
    position: relative;
    display: inline-block;
    border: none;
}

.cut-text {
    overflow: hidden;
    text-overflow: ellipsis;
}
.cut-text.display-email {
    max-width: 43ch;
    white-space: nowrap;
    display: inline-block;
    vertical-align: bottom;
}

@media (min-width: 1200px) {

.klicktipp-icon-grid-icons {
    width: 1130px;
}

[class^="kt-icon-"] {
  width: 204px;
  height: 204px;
  background: transparent url(../../../../../misc/img/kt_icons_sprite_large.png) scroll no-repeat 0px 0px;
}

[class^="stc-icon-"] {
    width: 204px;
    height: 204px;
    background: transparent url(../../../../../misc/img/stc_icons_sprite_large.png) scroll no-repeat 0px 0px;
}

.listbuilding-icon-wrapper {
    height: 260px;
}

.kt-icon-digimember { background-position: 0px 0px; }
.kt-icon-digimember:hover { background-position: 0px -204px; }
.kt-icon-digimember:active { background-position: 0px -408px; }

.kt-icon-leadpages { background-position: -204px 0px; }
.kt-icon-leadpages:hover { background-position: -204px -204px; }
.kt-icon-leadpages:active { background-position: -204px -408px; }

.kt-icon-optimizepress { background-position: -408px 0px; }
.kt-icon-optimizepress:hover { background-position: -408px -204px; }
.kt-icon-optimizepress:active { background-position: -408px -408px; }

.kt-icon-optinmonster { background-position: -612px 0px; }
.kt-icon-optinmonster:hover { background-position: -612px -204px; }
.kt-icon-optinmonster:active { background-position: -612px -408px; }

.kt-icon-profitbuilder { background-position: -816px 0px; }
.kt-icon-profitbuilder:hover { background-position: -816px -204px; }
.kt-icon-profitbuilder:active { background-position: -816px -408px; }

.kt-icon-unbounce { background-position: -1020px 0px; }
.kt-icon-unbounce:hover { background-position: -1020px -204px; }
.kt-icon-unbounce:active { background-position: -1020px -408px; }

.kt-icon-ninjapopups { background-position: -1224px 0px; }
.kt-icon-ninjapopups:hover { background-position: -1224px -204px; }
.kt-icon-ninjapopups:active { background-position: -1224px -408px; }

.kt-icon-thrivethemes { background-position: -1428px 0px; }
.kt-icon-thrivethemes:hover { background-position: -1428px -204px; }
.kt-icon-thrivethemes:active { background-position: -1428px -408px; }

.kt-icon-instabuilder { background-position: -1632px 0px; }
.kt-icon-instabuilder:hover { background-position: -1632px -204px; }
.kt-icon-instabuilder:active { background-position: -1632px -408px; }

.kt-icon-raw { background-position: -1836px 0px; }
.kt-icon-raw:hover { background-position: -1836px -204px; }
.kt-icon-raw:active { background-position: -1836px -408px; }

.kt-icon-klicktipp { background-position: -2040px 0px; }
.kt-icon-klicktipp:hover { background-position: -2040px -204px; }
.kt-icon-klicktipp:active { background-position: -2040px -408px; }

.kt-icon-fbbutton { background-position: -2244px 0px; }
.kt-icon-fbbutton:hover { background-position: -2244px -204px; }
.kt-icon-fbbutton:active { background-position: -2244px -408px; }

.kt-icon-combobox { background-position: -2448px 0px; }
.kt-icon-combobox:hover { background-position: -2448px -204px; }
.kt-icon-combobox:active { background-position: -2448px -408px; }

.kt-icon-apikey { background-position: -2652px 0px; }
.kt-icon-apikey:hover { background-position: -2652px -204px; }
.kt-icon-apikey:active { background-position: -2652px -408px; }

.kt-icon-wufoo { background-position: -2856px 0px; }
.kt-icon-wufoo:hover { background-position: -2856px -204px; }
.kt-icon-wufoo:active { background-position: -2856px -408px; }

.kt-icon-email { background-position: -3060px 0px; }
.kt-icon-email:hover { background-position: -3060px -204px; }
.kt-icon-email:active { background-position: -3060px -408px; }

.kt-icon-sms { background-position: -3264px 0px; }
.kt-icon-sms:hover { background-position: -3264px -204px; }
.kt-icon-sms:active { background-position: -3264px -408px; }

.kt-icon-fanpage { background-position: -3468px 0px; }
.kt-icon-fanpage:hover { background-position: -3468px -204px; }
.kt-icon-fanpage:active { background-position: -3468px -408px; }

.kt-icon-digistore { background-position: -3672px 0px; }
.kt-icon-digistore:hover { background-position: -3672px -204px; }
.kt-icon-digistore:active { background-position: -3672px -408px; }

/*
.kt-icon-digistore { background-position: -6324px 0px; }
.kt-icon-digistore:hover { background-position: -6324px -204px; }
.kt-icon-digistore:active { background-position: -6324px -408px; }
*/

.kt-icon-affilicon { background-position: -3876px 0px; }
.kt-icon-affilicon:hover { background-position: -3876px -204px; }
.kt-icon-affilicon:active { background-position: -3876px -408px; }

.kt-icon-inline { background-position: -4080px 0px; }
.kt-icon-inline:hover { background-position: -4080px -204px; }
.kt-icon-inline:active { background-position: -4080px -408px; }

.kt-icon-clickbank { background-position: -4284px 0px; }
.kt-icon-clickbank:hover { background-position: -4284px -204px; }
.kt-icon-clickbank:active { background-position: -4284px -408px; }

.kt-icon-paypal { background-position: -4488px 0px; }
.kt-icon-paypal:hover { background-position: -4488px -204px; }
.kt-icon-paypal:active { background-position: -4488px -408px; }

.kt-icon-cleverbridge { background-position: -4692px 0px; }
.kt-icon-cleverbridge:hover { background-position: -4692px -204px; }
.kt-icon-cleverbridge:active { background-position: -4692px -408px; }

.kt-icon-shopware { background-position: -4896px 0px; }
.kt-icon-shopware:hover { background-position: -4896px -204px; }
.kt-icon-shopware:active { background-position: -4896px -408px; }

.kt-icon-jvzoo { background-position: -5100px 0px; }
.kt-icon-jvzoo:hover { background-position: -5100px -204px; }
.kt-icon-jvzoo:active { background-position: -5100px -408px; }

.kt-icon-shareit { background-position: -5304px 0px; }
.kt-icon-shareit:hover { background-position: -5304px -204px; }
.kt-icon-shareit:active { background-position: -5304px -408px; }

.kt-icon-stripe { background-position: -5508px 0px; }
.kt-icon-stripe:hover { background-position: -5508px -204px; }
.kt-icon-stripe:active { background-position: -5508px -408px; }

.kt-icon-magento { background-position: -5712px 0px; }
.kt-icon-magento:hover { background-position: -5712px -204px; }
.kt-icon-magento:active { background-position: -5712px -408px; }

.kt-icon-wistia { background-position: -5916px 0px; }
.kt-icon-wistia:hover { background-position: -5916px -204px; }
.kt-icon-wistia:active { background-position: -5916px -408px; }

.kt-icon-woocommerce { background-position: -6120px 0px; }
.kt-icon-woocommerce:hover { background-position: -6120px -204px; }
.kt-icon-woocommerce:active { background-position: -6120px -408px; }

.kt-icon-widget { background-position: -7346px 0px; }
.kt-icon-widget:hover { background-position: -7346px -204px; }
.kt-icon-widget:active { background-position: -7346px -408px; }

.kt-icon-terminpilot { background-position: -7550px 0px; }
.kt-icon-terminpilot:hover { background-position: -7550px -204px; }
.kt-icon-terminpilot:active { background-position: -7550px -408px; }

.kt-icon-businesscard { background-position: -7754px 0px; }
.kt-icon-businesscard:hover { background-position: -7754px -204px; }
.kt-icon-businesscard:active { background-position: -7754px -408px; }

.kt-icon-gambio { background-position: -7958px 0px; }
.kt-icon-gambio:hover { background-position: -7958px -204px; }
.kt-icon-gambio:active { background-position: -7958px -408px; }

.kt-icon-nexmo { background-position: -8162px 0px; }
.kt-icon-nexmo:hover { background-position: -8162px -204px; }
.kt-icon-nexmo:active { background-position: -8162px -408px; }

.kt-icon-twilio { background-position: -8366px 0px; }
.kt-icon-twilio:hover { background-position: -8366px -204px; }
.kt-icon-twilio:active { background-position: -8366px -408px; }

.kt-icon-event { background-position: -8568px 0px; }
.kt-icon-event:hover { background-position: -8568px -204px; }
.kt-icon-event:active { background-position: -8568px -408px; }

.kt-icon-elopage { background-position: -8775px 0px; }
.kt-icon-elopage:hover { background-position: -8775px -204px; }
.kt-icon-elopage:active { background-position: -8775px -408px; }

/* Splittest-Club icons */

.stc-icon-splittest { background-position: 0px 0px; }
.stc-icon-splittest:hover { background-position: 0px -204px; }
.stc-icon-splittest:active { background-position: 0px -408px; }

.stc-icon-feedback { background-position: -204px 0px; }
.stc-icon-feedback:hover { background-position: -204px -204px; }
.stc-icon-feedback:active { background-position: -204px -408px; }

.stc-icon-exitlightbox { background-position: -408px 0px; }
.stc-icon-exitlightbox:hover { background-position: -408px -204px; }
.stc-icon-exitlightbox:active { background-position: -408px -408px; }

.stc-icon-socialproof { background-position: -612px 0px; }
.stc-icon-socialproof:hover { background-position: -612px -204px; }
.stc-icon-socialproof:active { background-position: -612px -408px; }

.stc-icon-onetimeoffer { background-position: -816px 0px; }
.stc-icon-onetimeoffer:hover { background-position: -816px -204px; }
.stc-icon-onetimeoffer:active { background-position: -816px -408px; }

}

@media (min-width: 993px) and (max-width: 1199px) {

.klicktipp-icon-grid-icons {
    width: 930px;
}

[class^="kt-icon-"] {
  background: transparent url(../../../../../misc/img/kt_icons_sprite_medium.png) scroll no-repeat 0px 0px;
  width: 164px;
  height: 164px;
}

[class^="stc-icon-"] {
    background: transparent url(../../../../../misc/img/stc_icons_sprite_medium.png) scroll no-repeat 0px 0px;
    width: 164px;
    height: 164px;
}

.listbuilding-icon-wrapper {
    height: 220px;
}

.kt-icon-digimember { background-position: 0px 0px; }
.kt-icon-digimember:hover { background-position: 0px -164px; }
.kt-icon-digimember:active { background-position: 0px -328px; }

.kt-icon-leadpages { background-position: -164px 0px; }
.kt-icon-leadpages:hover { background-position: -164px -164px; }
.kt-icon-leadpages:active { background-position: -164px -328px; }

.kt-icon-optimizepress { background-position: -328px 0px; }
.kt-icon-optimizepress:hover { background-position: -328px -164px; }
.kt-icon-optimizepress:active { background-position: -328px -328px; }

.kt-icon-optinmonster { background-position: -492px 0px; }
.kt-icon-optinmonster:hover { background-position: -492px -164px; }
.kt-icon-optinmonster:active { background-position: -492px -328px; }

.kt-icon-profitbuilder { background-position: -656px 0px; }
.kt-icon-profitbuilder:hover { background-position: -656px -164px; }
.kt-icon-profitbuilder:active { background-position: -656px -328px; }

.kt-icon-unbounce { background-position: -820px 0px; }
.kt-icon-unbounce:hover { background-position: -820px -164px; }
.kt-icon-unbounce:active { background-position: -820px -328px; }

.kt-icon-ninjapopups { background-position: -984px 0px; }
.kt-icon-ninjapopups:hover { background-position: -984px -164px; }
.kt-icon-ninjapopups:active { background-position: -984px -328px; }

.kt-icon-thrivethemes { background-position: -1148px 0px; }
.kt-icon-thrivethemes:hover { background-position: -1148px -164px; }
.kt-icon-thrivethemes:active { background-position: -1148px -328px; }

.kt-icon-instabuilder { background-position: -1312px 0px; }
.kt-icon-instabuilder:hover { background-position: -1312px -164px; }
.kt-icon-instabuilder:active { background-position: -1312px -328px; }

.kt-icon-raw { background-position: -1476px 0px; }
.kt-icon-raw:hover { background-position: -1476px -164px; }
.kt-icon-raw:active { background-position: -1476px -328px; }

.kt-icon-klicktipp { background-position: -1640px 0px; }
.kt-icon-klicktipp:hover { background-position: -1640px -164px; }
.kt-icon-klicktipp:active { background-position: -1640px -328px; }

.kt-icon-fbbutton { background-position: -1804px 0px; }
.kt-icon-fbbutton:hover { background-position: -1804px -164px; }
.kt-icon-fbbutton:active { background-position: -1804px -328px; }

.kt-icon-combobox { background-position: -1968px 0px; }
.kt-icon-combobox:hover { background-position: -1968px -164px; }
.kt-icon-combobox:active { background-position: -1968px -328px; }

.kt-icon-apikey { background-position: -2132px 0px; }
.kt-icon-apikey:hover { background-position: -2132px -164px; }
.kt-icon-apikey:active { background-position: -2132px -328px; }

.kt-icon-wufoo { background-position: -2296px 0px; }
.kt-icon-wufoo:hover { background-position: -2296px -164px; }
.kt-icon-wufoo:active { background-position: -2296px -328px; }

.kt-icon-email { background-position: -2460px 0px; }
.kt-icon-email:hover { background-position: -2460px -164px; }
.kt-icon-email:active { background-position: -2460px -328px; }

.kt-icon-sms { background-position: -2624px 0px; }
.kt-icon-sms:hover { background-position: -2624px -164px; }
.kt-icon-sms:active { background-position: -2624px -328px; }

.kt-icon-fanpage { background-position: -2788px 0px; }
.kt-icon-fanpage:hover { background-position: -2788px -164px; }
.kt-icon-fanpage:active { background-position: -2788px -328px; }

.kt-icon-digistore { background-position: -2952px 0px; }
.kt-icon-digistore:hover { background-position: -2952px -164px; }
.kt-icon-digistore:active { background-position: -2952px -328px; }

/*
.kt-icon-digistore { background-position: -5062px 0px; }
.kt-icon-digistore:hover { background-position: -5062px -164px; }
.kt-icon-digistore:active { background-position: -5062px -328px; }
*/

.kt-icon-affilicon { background-position: -3116px 0px; }
.kt-icon-affilicon:hover { background-position: -3116px -164px; }
.kt-icon-affilicon:active { background-position: -3116px -328px; }

.kt-icon-inline { background-position: -3279px 0px; }
.kt-icon-inline:hover { background-position: -3279px -164px; }
.kt-icon-inline:active { background-position: -3279px -328px; }

.kt-icon-clickbank { background-position: -3440px 0px; }
.kt-icon-clickbank:hover { background-position: -3440px -164px; }
.kt-icon-clickbank:active { background-position: -3440px -328px; }

.kt-icon-paypal { background-position: -3604px 0px; }
.kt-icon-paypal:hover { background-position: -3604px -164px; }
.kt-icon-paypal:active { background-position: -3604px -328px; }

.kt-icon-cleverbridge { background-position: -3772px 0px; }
.kt-icon-cleverbridge:hover { background-position: -3772px -164px; }
.kt-icon-cleverbridge:active { background-position: -3772px -328px; }

.kt-icon-shopware { background-position: -3926px 0px; }
.kt-icon-shopware:hover { background-position: -3926px -164px; }
.kt-icon-shopware:active { background-position: -3926px -328px; }

.kt-icon-jvzoo { background-position: -4090px 0px; }
.kt-icon-jvzoo:hover { background-position: -4090px -164px; }
.kt-icon-jvzoo:active { background-position: -4090px -328px; }

.kt-icon-shareit { background-position: -4254px 0px; }
.kt-icon-shareit:hover { background-position: -4254px -164px; }
.kt-icon-shareit:active { background-position: -4254px -328px; }

.kt-icon-stripe { background-position: -4418px 0px; }
.kt-icon-stripe:hover { background-position: -4418px -164px; }
.kt-icon-stripe:active { background-position: -4418px -328px; }

.kt-icon-magento { background-position: -4574px 0px; }
.kt-icon-magento:hover { background-position: -4574px -164px; }
.kt-icon-magento:active { background-position: -4574px -328px; }

.kt-icon-wistia { background-position: -4736px 0px; }
.kt-icon-wistia:hover { background-position: -4736px -164px; }
.kt-icon-wistia:active { background-position: -4736px -328px; }

.kt-icon-woocommerce { background-position: -4898px 0px; }
.kt-icon-woocommerce:hover { background-position: -4898px -164px; }
.kt-icon-woocommerce:active { background-position: -4898px -328px; }

.kt-icon-widget { background-position: -5870px 0px; }
.kt-icon-widget:hover { background-position: -5870px -164px; }
.kt-icon-widget:active { background-position: -5870px -328px; }

.kt-icon-terminpilot { background-position: -6034px 0px; }
.kt-icon-terminpilot:hover { background-position: -6034px -164px; }
.kt-icon-terminpilot:active { background-position: -6034px -328px; }

.kt-icon-businesscard { background-position: -6194px 0px; }
.kt-icon-businesscard:hover { background-position: -6194px -164px; }
.kt-icon-businesscard:active { background-position: -6194px -328px; }

.kt-icon-gambio { background-position: -6356px 0px; }
.kt-icon-gambio:hover { background-position: -6356px -164px; }
.kt-icon-gambio:active { background-position: -6356px -328px; }

.kt-icon-nexmo { background-position: -6518px 0px; }
.kt-icon-nexmo:hover { background-position: -6518px -164px; }
.kt-icon-nexmo:active { background-position: -6518px -328px; }

.kt-icon-twilio { background-position: -6684px 0px; }
.kt-icon-twilio:hover { background-position: -6684px -164px; }
.kt-icon-twilio:active { background-position: -6684px -328px; }

.kt-icon-event { background-position: -6843px 0px; }
.kt-icon-event:hover { background-position: -6843px -164px; }
.kt-icon-event:active { background-position: -6843px -328px; }

.kt-icon-elopage { background-position: -7002px 0px; }
.kt-icon-elopage:hover { background-position: -7002px -204px; }
.kt-icon-elopage:active { background-position: -7002px -408px; }

/* Splittest-Club icons */

.stc-icon-splittest { background-position: 0px 0px; }
.stc-icon-splittest:hover { background-position: 0px -164px; }
.stc-icon-splittest:active { background-position: 0px -328px; }

.stc-icon-feedback { background-position: -164px 0px; }
.stc-icon-feedback:hover { background-position: -164px -164px; }
.stc-icon-feedback:active { background-position: -164px -328px; }

.stc-icon-exitlightbox { background-position: -328px 0px; }
.stc-icon-exitlightbox:hover { background-position: -328px -164px; }
.stc-icon-exitlightbox:active { background-position: -328px -328px; }

.stc-icon-socialproof { background-position: -492px 0px; }
.stc-icon-socialproof:hover { background-position: -492px -164px; }
.stc-icon-socialproof:active { background-position: -492px -328px; }

.stc-icon-onetimeoffer { background-position: -656px 0px; }
.stc-icon-onetimeoffer:hover { background-position: -656px -164px; }
.stc-icon-onetimeoffer:active { background-position: -656px -328px; }

}

@media screen and (min-width: 993px) and (max-width: 1200px) {

.cut-text {
    max-width: 30ch;
}
.cut-text.display-email {
    max-width: 35ch;
}
}

@media screen and (min-width: 426px) and (max-width: 767px) {

.cut-text {
    max-width: 50ch;
}
.cut-text.display-email {
    max-width: 50ch;
}

#other-account-bar .hide-phone {
    display: none;
}
}

@media screen and (max-width: 425px) {

  .cut-text {
    max-width: 15ch;
  }

  .cut-text.display-email {
    max-width: 25ch;
  }

}
@media screen and (max-width: 320px) {

  .cut-text.display-email {
    max-width: 15ch;
  }

  #other-account-bar .hide-phone {
    display: none;
  }

}

@media (max-width: 992px) {

.klicktipp-icon-grid-icons {
    width: 100%;
    max-width: 620px;
}

#other-account-bar .hide-tablet {
    display: none;
}

[class^="kt-icon-"] {
  background: transparent url(../../../../../misc/img/kt_icons_sprite_small.png) scroll no-repeat 0px 0px;
  width: 94px;
  height: 94px;
}

[class^="stc-icon-"] {
    background: transparent url(../../../../../misc/img/stc_icons_sprite_small.png) scroll no-repeat 0px 0px;
    width: 94px;
    height: 94px;
}

.listbuilding-icon-wrapper {
    height: 144px;
}

.listbuilding-icon-title,
.break {
    display: none;
}

.kt-icon-digimember { background-position: 0px 0px; }
.kt-icon-digimember:hover { background-position: 0px -94px; }
.kt-icon-digimember:active { background-position: 0px -188px; }

.kt-icon-leadpages { background-position: -94px 0px; }
.kt-icon-leadpages:hover { background-position: -94px -94px; }
.kt-icon-leadpages:active { background-position: -94px -188px; }

.kt-icon-optimizepress { background-position: -188px 0px; }
.kt-icon-optimizepress:hover { background-position: -188px -94px; }
.kt-icon-optimizepress:active { background-position: -188px -188px; }

.kt-icon-optinmonster { background-position: -282px 0px; }
.kt-icon-optinmonster:hover { background-position: -282px -94px; }
.kt-icon-optinmonster:active { background-position: -282px -188px; }

.kt-icon-profitbuilder { background-position: -376px 0px; }
.kt-icon-profitbuilder:hover { background-position: -376px -94px; }
.kt-icon-profitbuilder:active { background-position: -376px -188px; }

.kt-icon-unbounce { background-position: -470px 0px; }
.kt-icon-unbounce:hover { background-position: -470px -94px; }
.kt-icon-unbounce:active { background-position: -470px -188px; }

.kt-icon-ninjapopups { background-position: -564px 0px; }
.kt-icon-ninjapopups:hover { background-position: -564px -94px; }
.kt-icon-ninjapopups:active { background-position: -564px -188px; }

.kt-icon-thrivethemes { background-position: -658px 0px; }
.kt-icon-thrivethemes:hover { background-position: -658px -94px; }
.kt-icon-thrivethemes:active { background-position: -658px -188px; }

.kt-icon-instabuilder { background-position: -752px 0px; }
.kt-icon-instabuilder:hover { background-position: -752px -94px; }
.kt-icon-instabuilder:active { background-position: -752px -188px; }

.kt-icon-raw { background-position: -846px 0px; }
.kt-icon-raw:hover { background-position: -846px -94px; }
.kt-icon-raw:active { background-position: -846px -188px; }

.kt-icon-klicktipp { background-position: -940px 0px; }
.kt-icon-klicktipp:hover { background-position: -940px -94px; }
.kt-icon-klicktipp:active { background-position: -940px -188px; }

.kt-icon-fbbutton { background-position: -1034px 0px; }
.kt-icon-fbbutton:hover { background-position: -1034px -94px; }
.kt-icon-fbbutton:active { background-position: -1034px -188px; }

.kt-icon-combobox { background-position: -1128px 0px; }
.kt-icon-combobox:hover { background-position: -1128px -94px; }
.kt-icon-combobox:active { background-position: -1128px -188px; }

.kt-icon-apikey { background-position: -1222px 0px; }
.kt-icon-apikey:hover { background-position: -1222px -94px; }
.kt-icon-apikey:active { background-position: -1222px -188px; }

.kt-icon-wufoo { background-position: -1316px 0px; }
.kt-icon-wufoo:hover { background-position: -1316px -94px; }
.kt-icon-wufoo:active { background-position: -1316px -188px; }

.kt-icon-email { background-position: -1410px 0px; }
.kt-icon-email:hover { background-position: -1410px -94px; }
.kt-icon-email:active { background-position: -1410px -188px; }

.kt-icon-sms { background-position: -1504px 0px; }
.kt-icon-sms:hover { background-position: -1504px -94px; }
.kt-icon-sms:active { background-position: -1504px -188px; }

.kt-icon-fanpage { background-position: -1598px 0px; }
.kt-icon-fanpage:hover { background-position: -1598px -94px; }
.kt-icon-fanpage:active { background-position: -1598px -188px; }

.kt-icon-digistore { background-position: -1692px 0px; }
.kt-icon-digistore:hover { background-position: -1692px -94px; }
.kt-icon-digistore:active { background-position: -1692px -188px; }

/*
.kt-icon-digistore { background-position: -2914px 0px; }
.kt-icon-digistore:hover { background-position: -2914px -94px; }
.kt-icon-digistore:active { background-position: -2914px -188px; }
*/

.kt-icon-affilicon { background-position: -1786px 0px; }
.kt-icon-affilicon:hover { background-position: -1786px -94px; }
.kt-icon-affilicon:active { background-position: -1786px -188px; }

.kt-icon-inline { background-position: -1880px 0px; }
.kt-icon-inline:hover { background-position: -1880px -94px; }
.kt-icon-inline:active { background-position: -1880px -188px; }

.kt-icon-clickbank { background-position: -1974px 0px; }
.kt-icon-clickbank:hover { background-position: -1974px -94px; }
.kt-icon-clickbank:active { background-position: -1974px -188px; }

.kt-icon-paypal { background-position: -2068px 0px; }
.kt-icon-paypal:hover { background-position: -2068px -94px; }
.kt-icon-paypal:active { background-position: -2068px -188px; }

.kt-icon-cleverbridge { background-position: -2162px 0px; }
.kt-icon-cleverbridge:hover { background-position: -2162px -94px; }
.kt-icon-cleverbridge:active { background-position: -2162px -188px; }

.kt-icon-shopware { background-position: -2256px 0px; }
.kt-icon-shopware:hover { background-position: -2256px -94px; }
.kt-icon-shopware:active { background-position: -2256px -188px; }

.kt-icon-jvzoo { background-position: -2350px 0px; }
.kt-icon-jvzoo:hover { background-position: -2350px -94px; }
.kt-icon-jvzoo:active { background-position: -2350px -188px; }

.kt-icon-shareit { background-position: -2444px 0px; }
.kt-icon-shareit:hover { background-position: -2444px -94px; }
.kt-icon-shareit:active { background-position: -2444px -188px; }

.kt-icon-stripe { background-position: -2538px 0px; }
.kt-icon-stripe:hover { background-position: -2538px -94px; }
.kt-icon-stripe:active { background-position: -2538px -188px; }

.kt-icon-magento { background-position: -2632px 0px; }
.kt-icon-magento:hover { background-position: -2632px -94px; }
.kt-icon-magento:active { background-position: -2632px -188px; }

.kt-icon-wistia { background-position: -2726px 0px; }
.kt-icon-wistia:hover { background-position: -2726px -94px; }
.kt-icon-wistia:active { background-position: -2726px -188px; }

.kt-icon-woocommerce { background-position: -2820px 0px; }
.kt-icon-woocommerce:hover { background-position: -2820px -94px; }
.kt-icon-woocommerce:active { background-position: -2820px -188px; }

.kt-icon-widget { background-position: -3382px 0px; }
.kt-icon-widget:hover { background-position: -3382px -94px; }
.kt-icon-widget:active { background-position: -3382px -188px; }

.kt-icon-terminpilot { background-position: -3476px 0px; }
.kt-icon-terminpilot:hover { background-position: -3476px -94px; }
.kt-icon-terminpilot:active { background-position: -3476px -188px; }

.kt-icon-businesscard { background-position: -3570px 0px; }
.kt-icon-businesscard:hover { background-position: -3570px -94px; }
.kt-icon-businesscard:active { background-position: -3570px -188px; }

.kt-icon-gambio { background-position: -3664px 0px; }
.kt-icon-gambio:hover { background-position: -3664px -94px; }
.kt-icon-gambio:active { background-position: -3664px -188px; }

.kt-icon-nexmo { background-position: -3758px 0px; }
.kt-icon-nexmo:hover { background-position: -3758px -94px; }
.kt-icon-nexmo:active { background-position: -3758px -188px; }

.kt-icon-twilio { background-position: -3852px 0px; }
.kt-icon-twilio:hover { background-position: -3852px -94px; }
.kt-icon-twilio:active { background-position: -3852px -188px; }

.kt-icon-event { background-position: -3947px 0px; }
.kt-icon-event:hover { background-position: -3947px -94px; }
.kt-icon-event:active { background-position: -3947px -188px; }

.kt-icon-elopage { background-position: -4040px 0px; }
.kt-icon-elopage:hover { background-position: -4040px -204px; }
.kt-icon-elopage:active { background-position: -4040px -408px; }

/* Splittest-Club icons */

.stc-icon-splittest { background-position: 0px 0px; }
.stc-icon-splittest:hover { background-position: 0px -94px; }
.stc-icon-splittest:active { background-position: 0px -188px; }

.stc-icon-feedback { background-position: -94px 0px; }
.stc-icon-feedback:hover { background-position: -94px -94px; }
.stc-icon-feedback:active { background-position: -94px -188px; }

.stc-icon-exitlightbox { background-position: -188px 0px; }
.stc-icon-exitlightbox:hover { background-position: -188px -94px; }
.stc-icon-exitlightbox:active { background-position: -188px -188px; }

.stc-icon-socialproof { background-position: -282px 0px; }
.stc-icon-socialproof:hover { background-position: -282px -94px; }
.stc-icon-socialproof:active { background-position: -282px -188px; }

.stc-icon-onetimeoffer { background-position: -376px 0px; }
.stc-icon-onetimeoffer:hover { background-position: -376px -94px; }
.stc-icon-onetimeoffer:active { background-position: -376px -188px; }

}

/* Color-Picker Spectrum adjustments*/

.sp-container {
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border-radius: 4px;
}

.sp-container .sp-picker-container .sp-input-container .sp-input {
    background-color: #ffffff;
    background-image: none;
    border: 1px solid #cccccc;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    color: #555555;
    display: inline-block;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px !important;
    height: 30px;
    line-height: 20px;
    margin: 0 0 5px;
    padding: 4px 6px;
    transition: border 0.2s linear 0s, box-shadow 0.2s linear 0s;
    vertical-align: middle;
}

.sp-container .sp-picker-container .sp-input-container .sp-input:focus {
    border-color: #66afe9;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset, 0 0 8px rgba(102, 175, 233, 0.6);
    outline: 0 none;
}

.sp-button-container,
.sp-input-container {
    display: none;
}

/* Klick-Tipp Quick-Help */
.quickhelp-icon {
    position: absolute;
    top: 0px;
    right: 0px;
    cursor: pointer;
    margin: 2px -22px 0 0;
}

.icon-quickhelp {
    background-position: 0px -80px;
}

.icon-quickhelp-admin {
    background-position: -20px -80px;
}

.icon-quickhelp-edit {
    background-position: -298px -624px;
    margin-bottom: 10px;
}

#automation-view-statistics-chart,
#email-view-statistics-chart {
    position: relative;
}

#automation-view-statistics-chart .google-chart-label,
#email-view-statistics-chart .google-chart-label {
    white-space: pre;
}

#automation-view-statistics-chart .quickhelp-google-chart,
#email-view-statistics-chart .quickhelp-google-chart {
    position: absolute;
    display: inline-block;
    display: none;
}

#automation-view-statistics-chart .quickhelp-automation-active {
    left:173px;
    top: 30px;
}

#automation-view-statistics-chart .quickhelp-automation-started {
    left:173px;
    top: 70px;
}

#automation-view-statistics-chart .quickhelp-automation-finished {
    left:173px;
    top: 110px;
}

#automation-view-statistics-chart .quickhelp-automation-onopenend {
    left:173px;
    top: 150px;
}

#automation-view-statistics-chart .quickhelp-automation-failed {
    left:173px;
    top: 190px;
}

#email-view-statistics-chart .quickhelp-email-sent {
    left:23px;
    top: 30px;
}

#email-view-statistics-chart .quickhelp-email-opened {
    left:23px;
    top: 62px;
}

#email-view-statistics-chart .quickhelp-email-clicked {
    left:23px;
    top: 94px;
}

#email-view-statistics-chart .quickhelp-email-bounced {
    left:23px;
    top: 126px;
}

#email-view-statistics-chart .quickhelp-email-spam-bounced {
    left:23px;
    top: 158px;
}



.popover.fade {
    z-index: -1010;
}

.popover.fade.in {
    z-index: 2010;
}

.popover-title {
    font-weight: bold;
}

.popover-content {
    font-weight: normal;
}

.popover.right .arrow {
    border-right-color: rgba(0, 0, 0, 0.15);
}

/* Settings box with preview */

.preview-box {
    background-color: #FFFFFF;
    border-color: #DDDDDD;
    border-radius: 4px 4px 0 0;
    border-width: 1px;
    border-style: solid;
    box-shadow: none;
    margin-left: 0;
    margin-right: 0;
    padding: 15px;
    position: relative;
}

.preview-box.preview-desktop {
    position:relative;
    min-height:300px;
    overflow:hidden;
    background: #FFFFFF url(../../../../../misc/img/login_bg_day.jpg) no-repeat scroll center bottom;
}

.preview-box.preview-box-center {
    text-align: center;
}

.preview-box.no-settings {
    border-radius: 4px;
}

.preview-box.append {
    border-radius: 0;
    margin-top: -16px;
}

.preview-box.append.no-settings {
    border-radius: 0 0 4px 4px;
}

.settings-box {
    background-color: #F7F7F9;
    border-color: #DDDDDD;
    border-radius: 0 0 4px 4px;
    border-width: 1px;
    border-style: solid;
    margin-left: 0;
    margin-right: 0;
    margin-top: -16px;
    padding: 15px;
}

/* Modal confirms */

.modal-dialog.modal-confirm .modal-header {
    background-color: #D9534F;
    border-radius: 4px 4px 0 0;
    color: #ffffff;
}

.modal-dialog.modal-info .modal-header {
    background-color: #5BC0DE;
    border-radius: 4px 4px 0 0;
    color: #ffffff;
}

.modal-content {
    background-color: transparent;
}

.modal-body {
    background-color: #FFFFFF;
}

.modal-header {
    padding: 15px 20px;
}

.modal-body > *:last-child {
    margin-bottom: 0;
}

.modal-footer {
    text-align: left;
    margin-top: 0;
    background-color: #e6e6e6;
    border-radius: 0 0 4px 4px;
    border-top: none;
    padding: 12px 20px;
}

.modal-footer .btn {
    margin-bottom: 0;
    margin-right: 10px;
}

.modal-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin: 0 5px 0 0;
    line-height: 20px;
    background-image: url("../../../../../misc/img/icons_sprite.png");
    background-position: 0px -120px;
    background-repeat: no-repeat;
    vertical-align: bottom;
}

.modal-icon-confirm {
    background-position: 0px -120px;
}

.modal-icon-info {
    background-position: -20px -120px;
}

.modal-icon-close {
    background-position: -40px -120px;
    float: right;
    margin-top: 5px;
    margin-right: 0;
    cursor: pointer;
    opacity: 0.7;
}

.modal-icon-close:hover {
    opacity: 1;
}

.modal-throbber-wrapper {
  margin-top: 18px !important;
  margin-bottom: 15px !important;
}

.modal-throbber {
  position: relative;
  width:16px;
  height:24px;
  margin: 0 5px -2px 0;
  display: inline-block;
  background: url("../../../../../misc/img/throbber.gif") 0px 34px;
}

/*Login form, not a modal, just the layout */

.modal-dialog.modal-login {
    position: relative;
    z-index: 10;
    width: 33%;
    text-align: left;
}

.modal-icon-login {
    background-position: -60px -120px;
}

.modal-dialog.modal-login .modal-header {
    background-color: #89B30B;
    border-radius: 4px 4px 0 0;
    color: #FFFFFF;
    padding: 14px 35px;
}

.modal-dialog.modal-login .modal-header h4 {
    font-size: 22px;
}

.modal-dialog.modal-login .modal-content {
    -webkit-box-shadow: 0 5px 30px rgba(0, 0, 0, 0.25);
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.25);
}

.modal-dialog.modal-login .modal-messages {
    background-color: #FCFFF1;
    padding: 20px 20px 0px 20px;
}

.modal-dialog.modal-login .modal-messages .alert {
    margin: 0;
}

.modal-dialog.modal-login .modal-body {
    background-color: #FCFFF1;
    color: #384807;
    padding: 17px 35px;
}

.modal-dialog.modal-login .modal-body input[type=text],
.modal-dialog.modal-login .modal-body input[type=password] {
    color: #757968;
    width: 100%;
}

.modal-dialog.modal-login .modal-body form .edit-pass-wrapper:last-child,
.modal-dialog.modal-login .modal-body form .edit-name-wrapper:last-child,
.modal-dialog.modal-login .modal-body form .edit-remember-me-wrapper:last-child {
    margin-bottom: 0;
}

.modal-dialog.modal-login .modal-body form .edit-remember-me-wrapper {
    padding-left: 2px;
}

.modal-dialog.modal-login .modal-footer {
    background-color: #FFE9CC;
    color: #EC7B0D;
    padding: 17px 35px;
}

.modal-dialog.modal-login .modal-footer {
    text-align: center;
}

.modal-dialog.modal-login .modal-footer .forgot-credentials {
    margin-top: 10px;
}

.modal-dialog.modal-login .modal-footer .forgot-credentials a,
.modal-dialog.modal-login .modal-footer .forgot-credentials a:focus,
.modal-dialog.modal-login .modal-footer .forgot-credentials a:active,
.modal-dialog.modal-login .modal-footer .forgot-credentials a.active {
    color: #FC960F;
}

/* KlickTipp Help Popup with video (Modal) */

#kt-help-popup .modal-dialog {
    width: 853px;
    margin: 160px auto 30px;
}

#kt-help-popup .modal-content {
    background-color: transparent;
    border: none;
    box-shadow: none;
}

#kt-help-popup .modal-body {
    padding: 0;
    background-color: transparent;
    border: none;
}

#kt-help-popup .modal-footer {
    background-color: transparent;
    border: none;
    text-align: center;
}

#kt-help-popup .modal-close {
    position: absolute;
    width: 63px;
    height: 63px;
    top: 0;
    right: 0;
    margin: -31px -31px 0 0;
    background: transparent url(/misc/img/modal_close_sprite.png) no-repeat scroll 0px 0px;
    cursor: pointer;
    z-index: 100;
}

#kt-help-popup .modal-unpin {
    position: absolute;
    width: 63px;
    height: 63px;
    top: 0;
    right: 0;
    margin: -40px 40px 0 0;
    background: transparent url(/misc/img/modal_close_sprite.png) no-repeat scroll 0px 0px;
    cursor: pointer;
    z-index: 100;
}

#kt-help-popup .modal-close:hover {
    background-position: -63px 0px ;
}

#kt-help-popup .modal-close:active {
    background-position: -126px 0px;
}

#kt-help-popup iframe {
    width: 853px;
    height: 480px;
}

form#klicktipp-contact-form > div > div.form-group > label {
    margin-top: 5px;
    font-size: 16px;
}

.kt-fixed-icon-group {
    position: fixed;
    display: block;
    top: 90px;
    right: 20px;
}

.kt-fixed-icon-bg {
    position: relative;
    display: block;
    cursor: pointer;
    width: 76px;
    height: 55px;
    background: transparent url(https://assets.klicktipp.com/content_includes/styleguide/icons/youtube_sprite.png) no-repeat scroll -76px -66px;
}

.dialog-video-icon-popup {
    position: absolute;
    display: none;
    top: 0;
    right: 0;
}

.kt-fixed-icon-bg:hover .dialog-video-icon-popup { display: block; }

.dialog-video-icon-popup .dialog-video-icon-popup-links {
    position: relative;
    display: block;
    min-height: 55px;
    margin: 0 90px 0 0;
    border: 1px solid rgb(221, 221, 221);
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    -webkit-box-shadow: 2px 2px 5px 0px rgba(6, 6, 6, 0.1);
    -moz-box-shadow: 2px 2px 5px 0px rgba(6, 6, 6, 0.1);
    box-shadow: 2px 2px 5px 0px rgba(6, 6, 6, 0.1);
    padding: 5px 15px;
    background-color: #f5f5f5;
}

.dialog-video-icon-popup .dialog-video-icon-popup-link {
    position: relative;
    display: block;
    line-height: 20px;
    white-space: nowrap;
    padding: 5px;
    margin-top: 5px;
    border-bottom: 1px solid rgb(221, 221, 221);
}

.dialog-video-icon-popup .dialog-video-icon-popup-link a {
    display: block !important;
    font-size: 14px !important;
    color: #999999 !important;
    margin: 0 !important;
    padding: 0 !important;
}

.dialog-video-icon-popup .dialog-video-icon-popup-link a:hover {
    color: #999999 !important;
}

.dialog-video-icon-popup .dialog-video-icon-popup-link:last-child {
    border-bottom: none;
}

.dialog-video-icon-popup-links .dialog-video-icon-popup-arrow {
    position: absolute;
    display: block;
    width: 16px;
    height: 16px;
    top: 27px;
    right: 0;
    margin: -8px -8px 0 0;
    background-color: #f5f5f5;
    border: 1px solid rgb(221, 221, 221);
    border-left: none;
    border-top: none;
    -webkit-box-shadow: 2px 2px 5px 0px rgba(6, 6, 6, 0.1);
    -moz-box-shadow: 2px 2px 5px 0px rgba(6, 6, 6, 0.1);
    box-shadow: 2px 2px 5px 0px rgba(6, 6, 6, 0.1);
    -ms-transform: rotate(-45deg); /* IE 9 */
    -webkit-transform: rotate(-45deg); /* Chrome, Safari, Opera */
    transform: rotate(-45deg);
}


.kt-video-popup {
    position:fixed;
    top:100px;
    left:100px;
    border: 3px solid gold;
    z-index: 10000;
}

.kt-video-popup-header {
    position: relative;
    height:30px;
    background-color:gold;
}

.kt-video-popup-closer {
    position: relative;
    display: inline-block;
    padding: 2px;
    float: right;
    cursor: pointer;
}

.kt-video-popup-content {
    position: relative;
    background-color:#ffffff;
}

/* YouTube Help Video styles */

.kt-help-video .video-section .kt-tv-video-container {
    display: block;
    position: relative;
    margin: 0 auto;
    width: 100%;
    background: transparent url(/misc/img/tv_800x450.png) scroll no-repeat center;
    background-size: contain;
}

.kt-help-video .video-section .kt-tv-video-container .kt-800-450-tv {
    width: 100%;
    height: auto;
    background: none;
}

.kt-help-video .video-section .kt-tv-video-container .kt-800-450-tv .kt-800-450-video {
    position: relative;
    width: 100%;
    height: 0;
    margin: 0 auto;
    padding-top: calc(100% / 1.777777778);
}

.kt-help-video .video-section .kt-tv-video-container .kt-800-450-tv .kt-800-450-video iframe,
.kt-help-video .video-section .kt-tv-video-container .kt-800-450-tv .kt-800-450-video .wistia_embed,
.kt-help-video .video-section .kt-tv-video-container .kt-800-450-tv .kt-800-450-video img {
    width: 81%;
    height: 81%;
    position: absolute;
    left: 0;
    right: 0;
    margin: 0 auto;
    top: 50%;
    -moz-transform: translateY(-59%);
    -o-transform: translateY(-59%);
    -ms-transform: translateY(-59%);
    -webkit-transform: translateY(-59%);
    transform: translateY(-59%);
}

@media screen and (max-width: 880px) {
    .kt-help-video .video-section .kt-tv-video-container .kt-800-450-tv .kt-800-450-video iframe,
    .kt-help-video .video-section .kt-tv-video-container .kt-800-450-tv .kt-800-450-video .wistia_embed,
    .kt-help-video .video-section .kt-tv-video-container .kt-800-450-tv .kt-800-450-video img {
        transform: translateY(-50%);
        width: 100%;
        height: 100%;
    }
}

@-webkit-keyframes button-grow {
    0%   {
        -webkit-transform: scale( 1 );
    }
    50% {
        -webkit-transform: scale( 1.25 );
    }
    100% {
        -webkit-transform: scale( 1 );
    }
}

@-moz-keyframes button-grow {
    0%   {
        -moz-transform: scale( 1 );
    }
    50% {
        -moz-transform: scale( 1.25 );
    }
    100% {
        -moz-transform: scale( 1 );
    }
}

@-o-keyframes button-grow {
    0%   {
        -o-transform: scale( 1 );
    }
    50% {
        -o-transform: scale( 1.25 );
    }
    100% {
        -o-transform: scale( 1 );
    }
}

@keyframes button-grow {
    0%   {
        transform: scale( 1 );
    }
    50% {
        transform: scale( 1.25 );
    }
    100% {
        transform: scale( 1 );
    }
}

.youtube-play {
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 10;
    width: 120px;
    height: 90px;
    background-image: url("/content_includes/img/webinar/ccp/youtube-play.png");
    background-position: top center;
    background-repeat: no-repeat;
    background-size: contain;
    cursor: pointer;
    -webkit-animation: button-grow 1s infinite;
    -moz-animation: button-grow 1s infinite;
    -o-animation: button-grow 1s infinite;
    animation: button-grow 1s infinite;
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.youtube-play.on-hover {
    -webkit-animation: none;
    -moz-animation: none;
    -o-animation: none;
    animation: none;
    top: -15%;
    -moz-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
}

.youtube-play.on-hover:hover {
    -webkit-animation: button-grow 1s infinite;
    -moz-animation: button-grow 1s infinite;
    -o-animation: button-grow 1s infinite;
    animation: button-grow 1s infinite;
    -moz-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
}

@media screen and (max-width: 420px) {
    .youtube-play {
        width: 90px;
        height: 60px;
    }
}

/* Consultant Marketplace styles */

form#klicktipp-consultant-settings-form .form-type-checkbox {
    display: inline-table;
}

form#klicktipp-consultant-marketplace-form .klicktipp-grid-row .klicktipp-block input,
form#klicktipp-consultant-marketplace-form .klicktipp-grid-row .klicktipp-block select {
    width: 100%;
    max-width: 100%;
}

form#klicktipp-consultant-marketplace-form .klicktipp-grid-row .klicktipp-pager {
    margin-top: 35px;
}

form#klicktipp-consultant-marketplace-form .klicktipp-grid-row .klicktipp-pager .pagination span {
    color: #2885d1;
    outline: none;
    cursor: pointer;
}

form#klicktipp-consultant-marketplace-form .klicktipp-grid-row .klicktipp-pager .pagination .active span {
    color: #ffffff;
}

form#klicktipp-consultant-marketplace-form .klicktipp-grid-row .klicktipp-pager .pagination .disabled span {
    color: #dddddd;
    cursor: not-allowed;
}

form#klicktipp-consultant-marketplace-form .klicktipp-grid-row .klicktipp-pager .pagination span:hover {
    text-decoration: underline;
}

form .klicktipp-grid-row #edit-consultants {
    margin-top: 25px;
}

@media (max-width: 991px) {

    form .klicktipp-grid-row #edit-consultants {
        text-align: center;
    }

}

form .klicktipp-grid-row #edit-consultants a {
    color: #4b4b4b;
    position: relative;
    display: inline-block;
    vertical-align: top;
    text-decoration: none;
}

form .klicktipp-grid-row #edit-consultants .ct-item {
    width: 201px;
    height: 287px;
    border: 1px #cacaca solid;
    margin: 0 7px 10px 0;
    padding: 20px 10px 0 10px;
    border-radius: 8px;
    background-color: #ffffff;
}

form .klicktipp-grid-row #edit-consultants .ct-item:hover,
form .klicktipp-grid-row #edit-consultants .ct-item:active {
    background-color: #fcfcfc;
}

form .klicktipp-grid-row #edit-consultants .img-container {
    text-align: center;
    margin-bottom: 20px;
}

form .klicktipp-grid-row #edit-consultants .img-circle {
    width: 100px;
    height: 100px;
}

form .klicktipp-grid-row #edit-consultants .ct-details span {
    display: block;
    text-align: center;
    line-height: 19px;
    font-size: 13px;
}

form .klicktipp-grid-row #edit-consultants .ct-details span:not(:last-child) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

form .klicktipp-grid-row #edit-consultants .ct-name {
    color: #5d5d5d;
    font-size: 16px !important;
    margin-bottom: 10px;
}

form .klicktipp-grid-row #edit-consultants .ct-company {
    color: #939393;
    margin-bottom: 5px;
}

form .klicktipp-grid-row #edit-consultants .ct-city {
    color: #939393;
    margin-bottom: 12px;
}

form .klicktipp-grid-row #edit-consultants .ct-service {
    color: #5d5d5d;
    max-height: 54px;
    overflow: hidden;
}

form#klicktipp-consultant-form .klicktipp-block h2 {
    font-family: "Helvetica Neue", Helvetica, "Lucida Sans Unicode", Arial, sans-serif;
    font-size: 30px;
    font-weight: bold;
    line-height: 30px;
    margin: 30px 0;
    color: #000000;
}

form#klicktipp-consultant-form .klicktipp-block h3 {
    font-family: "Helvetica Neue", Helvetica, "Lucida Sans Unicode", Arial, sans-serif;
    font-size: 24px;
    line-height: 35px;
    margin-bottom: 20px;
    color: #000000;
    font-weight: bold;
}

form#klicktipp-consultant-form .klicktipp-block p {
    font-size: 16px;
    line-height: 28px;
    font-family: "Lucida Grande", "Lucida Sans Unicode", "Helvetica Neue", Helvetica, Arial, sans-serif;
    margin: 0 0 30px 0;
}

form#klicktipp-consultant-form .klicktipp-block .consultant-details {
    font-size: 17px;
    font-weight: bold;
    line-height: 28px;
    font-family: "Lucida Grande", "Lucida Sans Unicode", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

form#klicktipp-consultant-form .klicktipp-block #consultant-info * {
    display: block;
    text-align: center;
}

form#klicktipp-consultant-form .klicktipp-block #consultant-info img {
    margin: 20px auto 20px auto;
}

form#klicktipp-consultant-form .klicktipp-block #consultant-info .name {
    font-size: 18px;
    color: #4b4b4b;
    margin-bottom: 5px;
}

form#klicktipp-consultant-form .klicktipp-block #consultant-info .company,
form#klicktipp-consultant-form .klicktipp-block #consultant-info .phone,
form#klicktipp-consultant-form .klicktipp-block #consultant-info .city {
    color: #939393;
    font-size: 14px;
    margin-bottom: 5px;
}

form#klicktipp-consultant-form .klicktipp-block #consultant-info #contact-link {
    margin-top: 20px;
}

form#klicktipp-consultant-form .klicktipp-block hr {
    margin: 0 0 30px 0;
}

form#klicktipp-consultant-form .klicktipp-block .kt-tv-video-container {
    margin: 10px 0 30px 0;
}

form#klicktipp-consultant-form .modal-header {
    background-color: #fc960f;
    border-radius: 4px 4px 0 0;
}

form#klicktipp-consultant-form .modal-header .modal-title {
    color: #ffffff;
}

form#klicktipp-consultant-form .modal-footer #edit-submit,
form#klicktipp-consultant-form .modal-footer #edit-submit:focus,
form#klicktipp-consultant-form .modal-footer #edit-submit.active {
    background-color: #fc960f;
    border-color: #ec7b0d;
    color: #ffffff;
}

form#klicktipp-consultant-form .modal-footer #edit-submit:hover {
    background-color: #ec7b0d;
}

@media (max-width: 699px) {

    form#klicktipp-consultant-form .klicktipp-block h3,
    form#klicktipp-consultant-form .klicktipp-block #edit-consultants {
        text-align: center;
    }

}


/* Consultant Marketplace styles */

/*
#######################################
# Dialog specific styles
#######################################

/*
Email dialog
*/

/* campaign overview */

#ctrl-campaign-spam-alert {
    margin-bottom: 50px;
}

#ctrl-campaign-spam-alert .spam-alert-content {
    position: relative;
    background: url("../../../../../misc/img/email_statistics_sprite.png") no-repeat scroll 10px -351px transparent;
    min-height: 68px;
    padding: 0 10px 0 110px;
}

#ctrl-campaign-spam-alert .spam-alert-content h3 {
    margin-top: 10px;
    font-size: 23px;
}

#ctrl-campaign-spam-alert .spam-alert-content p {
    font-size: 14px;
    line-height: 17px;
}

#ctrl-campaign-status.row {
    position: relative;
    background-color: #F4F4F4;
    margin: 0;
    height: 50px;
}

#ctrl-campaign-status .traffic-light {
    background: url("../../../../../misc/img/email_statistics_sprite.png") no-repeat scroll 10px 0 transparent;
    bottom: 5px;
    height: 54px;
    margin: 0 auto;
    position: relative;
    width: 42px;
}

#ctrl-campaign-status .status-text {
    position: relative;
    font-weight: normal;
    font-size: 16px;
    line-height: 50px;
    vertical-align: middle;
}

#ctrl-campaign-statistics-chart {
    position: relative;
    margin: 30px 0;
    height: 340px; /* default is 7 bars */
}
#ctrl-campaign-statistics-chart.chart-bars-4 {
    height: 220px;
}
#ctrl-campaign-statistics-chart.chart-bars-5 {
    height: 260px;
}
#ctrl-campaign-statistics-chart.chart-bars-8 {
    height: 380px;
}
#ctrl-campaign-statistics-chart.chart-bars-9 {
    height: 410px;
}

#ctrl-campaign-send-score {
    position: relative;
    margin: 0 0 40px 0;
}

#ctrl-campaign-status .buttons {
    text-align: right;
    padding: 10px 0 0 0;
    margin: 0 -7px 0 0;
}
#ctrl-campaign-status .buttons.quickhelp-inline .quickhelp-icon {
    margin-top: 3px;
}

#ctrl-campaign-send-score .send-score-envelopes {
    position: relative;
    background: url("../../../../../misc/img/email_statistics_sprite.png") no-repeat scroll 0 -54px transparent;
    height: 112px;
    width: 880px;
    margin: 0 auto;
}

#ctrl-campaign-send-score .envelope {
    position: absolute;
    left: 0px;
    top: 50px;
    width: 174px;
    height: 32px;
    line-height: 32px;
    vertical-align: middle;
    font-size: 30px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
}

#ctrl-campaign-send-score .send-score-envelopes .send-score-sent {
    left: 55px;
}

#ctrl-campaign-send-score .send-score-envelopes .send-score-bounced {
    left: 346px;
}

#ctrl-campaign-send-score .send-score-envelopes .send-score-delivered {
    left: 640px;
}

#ctrl-campaign-send-score .send-score-labels {
    position: relative;
    width: 880px;
    margin: 5px auto;
}

#ctrl-campaign-send-score .send-score-label {
    position: relative;
    display: inline-block;
    width: 215px;
    line-height: 32px;
    font-size: 23px;
    color: #000000;
    vertical-align: middle;
    text-align: center;
}

#ctrl-campaign-send-score .send-score-labels .send-score-label.send-score-sent {
    margin: 0 0 0 35px;
}

#ctrl-campaign-send-score .send-score-labels .send-score-label.send-score-bounced {
    margin: 0 0 0 72px;
}

#ctrl-campaign-send-score .send-score-labels .send-score-label.send-score-delivered {
    margin: 0 0 0 74px;
}

#ctrl-campaign-send-score .send-score-labels .send-score-bounced-descr {
    position: relative;
    color: #7F7F7F;
    font-size: 14px;
    line-height: 18px;
    text-align: center;
}

#ctrl-campaign-opens,
#ctrl-campaign-clicks,
#ctrl-campaign-conversions {
    position: relative;
    height: 265px;
    margin: 0 0 40px 0;
}

#ctrl-campaign-opens .donut-chart,
#ctrl-campaign-clicks .donut-chart,
#ctrl-campaign-conversions .donut-chart {
    position: relative;
    display: inline-block;
    z-index: 100;
}

#ctrl-campaign-opens .donut-chart-info,
#ctrl-campaign-clicks .donut-chart-info,
#ctrl-campaign-conversions .donut-chart-info {
    position: relative;
    display: inline-block;
    background: url("../../../../../misc/img/email_statistics_sprite.png") no-repeat scroll 0 -167px transparent;
    width: 572px;
    height: 112px;
    position: absolute;
    margin: 73px 0 0 -45px;
}

.donut-chart-info .data-positive,
.donut-chart-info .data-negative {
    font-weight: bold;
    font-size: 16px;
    line-height: 32px;
    vertical-align: middle;
    color: #ffffff;
}

.donut-chart-info .data-box {
    position: relative;
    width: 100px;
    height: 32px;
    display: inline-block;
    text-align: center;
    margin: 0 15px 6px 0;
}

#ctrl-campaign-opens .donut-chart-info .data-positive .data-box {
    background-color: #C274A9;
    margin-top: 20px;
}

#ctrl-campaign-opens .donut-chart-info .data-negative .data-box {
    background-color: #6bc4d1;
}

#ctrl-campaign-clicks .donut-chart-info .data-positive .data-box {
    background-color: #A3D374;
    margin-top: 20px;
}

#ctrl-campaign-clicks .donut-chart-info .data-negative .data-box {
    background-color: #C274A9;
}

#ctrl-campaign-conversions .donut-chart-info .data-positive .data-box {
    background-color: #6fd16f;
    margin-top: 20px;
}

#ctrl-campaign-conversions .donut-chart-info .data-negative .data-box {
    background-color: #C274A9;
}

.donut-chart-info .data-total {
    position: relative;
    line-height: 71px;
    vertical-align: middle;
    background: url("../../../../../misc/img/email_statistics_sprite.png") no-repeat scroll 0 -279px transparent;
    padding: 0 0 0 30px;
    font-size: 23px;
    color: #000000;
    margin: 20px 0 0 0;
}

.donut-chart-info .data-label {
    position: relative;
    display: inline-block;
    height: 32px;
    line-height: 32px;
}

.donut-chart-info .btn {
    height: 32px;
    padding-top: 5px;
    padding-bottom: 4px;
    vertical-align: top;
}

.donut-chart-info .btn i {
    margin-right: 7px;
    margin-top: 2px;
}

.donut-chart-info .chart-title {
    position: absolute;
    font-size: 23px;
    font-weight: bold;
    top: -48px;
}

#ctrl-campaign-most-clicked-links {
    position: relative;
    margin: 0 0 40px 0;
}

/* splittest email */

#ctrl-campaign-splittest-status {
    position: relative;
    margin: 0 0 40px 0;
}

#ctrl-campaign-splittest-status #splittest-status-testsize {
    margin-top: 30px;
}

#ctrl-campaign-splittest-status .panel-heading {
    font-weight: bold;
    font-size: 18px;
    color: #000000;
    text-align: center;
}

#ctrl-campaign-splittest-status .splittest-status-testsize-value {
    padding-bottom: 15px;
    font-weight: normal;
    font-size: 18px;
    color: #000000;
    text-align: center;
}

#ctrl-campaign-splittest-status #splittest-status-start .splittest-start-watch {
    position: relative;
    background: url("../../../../../misc/img/splittest_statistics_sprite.png") no-repeat scroll 0 0 transparent;
    display: block;
    height: 146px;
    width: 124px;
    margin: 0 auto;
}

#ctrl-campaign-splittest-status #splittest-status-stop .splittest-stop-watch {
    position: relative;
    background: url("../../../../../misc/img/splittest_statistics_sprite.png") no-repeat scroll -124px 0 transparent;
    display: block;
    height: 146px;
    width: 124px;
    margin: 0 auto;
}

#ctrl-campaign-splittest-status .splittest-arrow {
    position: relative;
    background: url("../../../../../misc/img/splittest_statistics_sprite.png") no-repeat scroll 0 -146px transparent;
    display: block;
    height: 31px;
    width: 33px;
    margin: 0 auto;
    margin-top: 65px;
}

.splittest-separator,
.form-element-separator {
    background: url("../../../../../misc/img/splittest_statistics_sprite.png") no-repeat scroll 0 -254px transparent;
    display: block;
    height: 1px;
    position: relative;
    width: 834px;
    margin: 0 auto 40px auto;
}

.form-element-separator {
    margin: 30px auto 25px;
}

#ctrl-campaign-splittest-status .splittest-watch-title {
    position: relative;
    font-size: 23px;
    font-weight: bold;
    margin: 0;
    padding-top: 45px;
    text-align: center;
}

#ctrl-campaign-splittest-status .splittest-watch-title.green {
    color: #5DB05D;
}

#ctrl-campaign-splittest-status .splittest-watch-title.red {
    color: #D35245;
}

#ctrl-campaign-splittest-status .splittest-watch-date {
    color: #000000;
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    text-align: center;
}

#ctrl-campaign-splittest-status .splittest-watch-time {
    color: #585858;
    font-size: 14px;
    margin: 0;
    text-align: center;

}

#ctrl-campaign-splittest-winner {
    position: relative;
    margin: 0 0 40px 0;
}

#ctrl-campaign-splittest-winner .splittest-winner-trophy {
    position: relative;
    background: url("../../../../../misc/img/splittest_statistics_sprite.png") no-repeat scroll 0 -177px transparent;
    display: block;
    height: 77px;
    width: 69px;
    margin: 0 auto;
}

#ctrl-campaign-splittest-winner .splittest-winner-headline {
    position: relative;
    color: #4A8CDE;
    font-size: 23px;
    font-weight: bold;
    text-align: center;
    padding-bottom: 5px;
}

#ctrl-campaign-splittest-winner .splittest-winner-email {
    position: relative;
    text-align: center;
    border: 2px solid #4A8CDE;
    font-size: 18px;
    color: #000000;
    line-height: 32px;
    border-radius: 4px;
}

#ctrl-campaign-splittest-opens-chart,
#ctrl-campaign-splittest-clicks-chart {
    position: relative;
    margin: 15px 0;
    min-height: 130px;
}

#ctrl-campaign-splittest-links {
    position: relative;
    margin: 40px 0;
}

/* Contact export */

.edit-GlobalCustomFieldAll-wrapper {
    position: relative;
}

.edit-GlobalCustomFieldAll-wrapper label,
.edit-SubscriptionAll-wrapper label,
.edit-CustomFieldAll-wrapper label {
    font-weight: bold;
}

/* Fanpage */
a.fanpage-connect-help:link,
a.fanpage-connect-help:active,
a.fanpage-connect-help:hover,
a.fanpage-connect-help:focus {
    position: absolute;
    display: block;
    left: 75%;
    top: 30px;
    width: 200px;
    height: 180px;
    background: transparent url('../../../../../misc/img/fanpage_help_icon.png') no-repeat scroll 0px 0px;
    outline: none;
    text-decoration: none;
}

a.fanpage-connect-help span {
    position: relative;
    top: 130px;
}

/* Formbuilder */

table#subscription-fields .form-type-item {
    margin: 0;
}

table#subscription-fields .klicktipp-item {
    width: 100%;
}

#formbuilder-preview { display:inline-block; }

#formbuilder-preview .ktv2-form-links,
#formbuilder-preview .ktv2-form-chicklet,
#formbuilder-preview .kt-affiliate-link,
.no-text-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.ktv2-fbbutton-error {
    margin-top: 50px;
}

/* Klicktipp Resize */

#klicktipp-resize {
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    margin: -13px 0 0 -13px;
    background: #ffffff url(../../../../../misc/img/icons_sprite.png) scroll no-repeat 3px -377px;
    border: 1px solid #cccccc;
    border-radius: 14px;
    box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
    height: 27px;
    width: 27px;
    z-index: 1000;
    cursor: ew-resize;
}

#klicktipp-resize:hover {
    background-color: #ebebeb;
}

.color-templates-wrapper {}
.color-templates {
    display:inline-block;
    width: 50px;
}
.color-template {
    position: relative;
    display: inline-block;
    margin: 0 10px 5px 0;
    border: 0;
    border-radius: 3px;
    -webkit-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3);
    -moz-box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3);
    box-shadow: 0px 0px 3px 0px rgba(0,0,0,0.3);
    cursor:pointer;
}

.color-template span {
    position: relative;
    display: block;
    width: 20px;
    height: 10px;
    margin: 10px;
    border-radius: 3px;
}


/* ----- TEMPLATE FILES ----- */

#content.whitelabel {
    margin-bottom: 25px;
}

#whitelabel-footer,
#whitelabel-footer span {
    text-align: center;
    margin: 25px 0px;
    color: #cccccc;
    font-size: 12px;
    line-height: 16px;
    vertical-align: middle;
}

#whitelabel-footer span.footer-separator {
    padding: 0px 10px;
}

#whitelabel-footer a,
#whitelabel-footer a:visited,
#whitelabel-footer a:active,
#whitelabel-footer a:focus {
    color: #cccccc;
    font-size: 12px;
    line-height: 16px;
    vertical-align: middle;
    text-decoration: none;
    display: inline-block;
}

#whitelabel-footer a:hover {
    text-decoration: underline;
    color: #bbbbbb;
}

/* ----- Book module -----*/

.book-navigation {
    background-color: #F5F5F5;
    padding: 4px 0;
    margin-top: 25px;
    margin-bottom: 25px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.book-navigation .button-left {
    text-align: left;
}

.book-navigation .button-left .btn {
    margin-left: -11px;
}

.book-navigation .button-center {
    text-align: center;
}

.book-navigation .button-right {
    text-align: right;
}

.book-navigation .button-right .btn {
    text-align: right;
    margin-right: -11px;
}

.book-navigation .btn.disabled {
    background-color: #999999;
    border: #555555;
}

/*
* Responsive design
* overwrite styles based on viewport width
*/

span.hidden-lg,
span.hidden-md,
span.hidden-sm,
span.hidden-xs,
.hidden-xxs {
    display: inherit !important;
}

/* ------- FEEDBACK BUTTON ------- */

#feedbackButton a {
  display: block;
  position: fixed;
  top: 33%;
  left: 0;
  width: 31px;
  height: 135.58px;
  background-image: url(/misc/img/feedback_button.png);
  background-size: 31px 135.58px;
  z-index: 1001;
}

/* large devices ( >= 1200 pixel )
------------------------- */
@media (min-width: 1200px) {

    span.hidden-lg {
        display: none !important;
    }

}

/* medium devices ( 993 to 1199 pixel )
------------------------- */
@media (min-width: 993px) and (max-width: 1199px) {

    #sidebar .sidenav {
        width: 238px;
    }

    #account-info-wrapper {
        padding-right: 0;
    }

    #account-info-wrapper .account-info-category, #account-info-batches {
        display: none;
    }

    span.hidden-md {
        display: none !important;
    }

    /*.navbar > .container .navbar-brand img {*/
    /*    background-image: url("/misc/img/kt_logo_icon_36x32.png");*/
    /*    width: 0;*/
    /*    height: 0;*/
    /*    padding: 32px 40px 0 0;*/
    /*    background-repeat: no-repeat;*/
    /*    background-size: contain;*/
    /*}*/
    /* size down the footer menu links */
    #footer-menu .navbar-nav > li > a {
        padding: 5px 8px;
        font-size: 12px;
    }

    #footer-menu .navbar-text {
        font-size: 12px;
    }

    #footer-menu ul.navbar-nav {
        margin-top: 8px;
    }

    .modal-dialog.modal-login {
        width: 40%;
    }

}

/* small devices ( 768 to 992 pixel)
------------------------- */
@media (min-width: 768px) and (max-width: 992px) {

    #sidebar .sidenav {
        width: 190px;
    }

    #account-info-wrapper {
        display: none;
    }

    span.hidden-sm {
        display: none !important;
    }

    /* size down the footer menu links */
    #footer-menu .navbar-nav > li > a {
        padding: 5px 8px;
        font-size: 12px;
    }

    #footer-menu .navbar-text {
        font-size: 12px;
    }

    form dl dt.item-label {
        width: 33%;
    }

    form dl dd.item-value {
        width: 66%;
    }

    .modal-dialog.modal-login {
        width: 50%;
    }

    .navbar > .container .navbar-brand {
        display: none;
    }
    /* help videos */
    #kt-help-popup .modal-dialog {
        width: 640px;
    }

    #kt-help-popup iframe {
        width: 640px;
        height: 360px;
    }

    .kt-fixed-icon-group {
        top: 140px;
    }

    .kt-fixed-icon-bg {
        width: 54px;
        height: 39px;
        background-position: -76px -27px;
    }

    .dialog-video-icon-popup .dialog-video-icon-popup-links {
        min-height: 27px;
        margin: 0 70px 0 0;
    }

    .dialog-video-icon-popup-links .dialog-video-icon-popup-arrow {
        top: 20px;
    }

    /* cut off text */
    .cut-text {
      max-width: 25ch;
    }

    .cut-text.display-email {
      max-width: 45ch;
    }

}

/* Extra small devices ( < 767 Pixel )
------------------------- */
@media (max-width: 767px) {

  .create-buttons .right{
    float: none;
  }

    #sidebar {
        margin-bottom: 20px;
        padding-left: 15px;
        padding-right: 15px;
    }

    #sidebar .sidenav.affix,
    #sidebar .sidenav.affix-top {
        position: relative;
        width: auto;
    }

    #account-info-wrapper {
        display: none;
    }

    span.hidden-xs {
        display: none !important;
    }

    /* hide admin menu, remove the top margin */
    #admin-menu {
        display: none;
    }

    body.admin-menu #container {
        margin-top: 70px;
        padding-top: 0px;
    }

    body.admin-menu #top-menu {
        top: 0px;
    }

    body.has-sidebar .breadcrumb-row {
        padding-left: 15px;
    }

    #top-menu {
        position: absolute;
    }

    #top-menu .dropdown-submenu ul.dropdown-menu {
      display: block;
      padding-left: 15px;
      background-color: #********;
    }

    #top-menu .dropdown-submenu a.dropdown-submenu-toggle {
      margin-bottom: 5px;
      color: #BBBBBB;
    }

    #top-menu ul.dropdown-menu li a {
      color: #BBBBBB;
    }

    #top-menu ul.dropdown-menu li a.active {
      background-color: transparent;
    }

    #top-menu .dropdown-submenu > a:after {
      display: none;
    }

    #top-menu .dropdown-menu .divider {
      background-color: #6a6a6a !important;
    }

    #top-menu div.navbar-collapse.navbar-responsive-collapse {
      max-height: 85vh;
    }

    #top-menu-beamer {
      display: none;
    }

    #top-menu-beamer-mobile {
      display: block;
    }

    body.user-login #top-menu {
        position: fixed;
    }

    #top-menu .container,
    #footer-menu .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    #top-menu div.navbar-collapse {
        padding-left: 15px;
        padding-right: 15px;
        margin-left: 0;
    }

    /* hide support beacon on mobile screens */
    #beacon-container {
      display: none;
    }

    /* add a margin to the content container */
    #container {
        margin-left: 10px;
        margin-right: 10px;
    }

    /* center the brand logo */
    .navbar-logo-toggle {
        width: 100%;
        text-align: center;
    }

    /* hide breadcrumb on small devices */
    .breadcrumb-wrapper {
        display: none;
    }

    /* remove floats and align toggle button with copyright */
    #footer-menu .navbar-text {
        float: none;
    }

    #footer-menu ul.pull-right {
        float: none !important;
    }

    #footer-menu .navbar-toggle {
        margin-top: -7px;
        margin-bottom: 0;
    }

    .button-row .btn {
        display: block;
        width: 100%;
        margin: 0 0 5px 0;
        padding-left: 15px;
    }

    .button-row > a .btn {
        margin-bottom: 5px;
    }

    .button-row > .btn:last-child {
        margin-bottom: 0px;
    }

    .button-row > a:last-child .btn {
        margin-bottom: 0px;
    }

    .btn-sm-icon-only {
        font-size: 0px;
        padding-left: 22px;
    }

    .btn-sm-icon-only.btn-icon-duplicate-black {
        background-position: -290px -1430px;
    }

    .btn-sm-icon-only.btn-icon-duplicate-black:active {
        background-position: -290px -1429px;
    }

    .btn-sm-icon-only.btn-icon-edit-black {
        background-position: -290px -614px;
    }

    .btn-sm-icon-only.btn-icon-edit-black:active {
        background-position: -290px -613px;
    }

    .btn-sm-icon-only.btn-icon-delete-black {
        background-position: -288px -274px;
    }

    .btn-sm-icon-only.btn-icon-delete-black:active {
        background-position: -288px -273px;
    }

    .btn-sm-icon-only.btn-icon-preview-black {
        background-position: -290px -750px;
    }

    .btn-sm-icon-only.btn-icon-preview-black:active {
        background-position: -290px -749px;
    }

    .enhanced-date div:first-child {
        width: 100%;
    }

    .enhanced-date label:first-child + div,
    .enhanced-date div:first-child,
    .element-row label:first-child + div
    .element-row div:first-child {
        display: block;
        margin-bottom: 5px;
    }

    form input[type=text],
    form input[type=password],
    body.has-sidebar form input[type=text],
    body.has-sidebar form input[type=password],
    form .klicktipp-item {
        width: 100%;
        margin: 0;
        max-width: none;
    }

    form div.klicktipp-magicselect {
        width: 100%;
    }

    body.has-sidebar form div.klicktipp-magicselect-short {
        width: 100%;
    }

    form select.form-control,
    body.has-sidebar form select.form-control {
        margin: 0;
        width: 100%;
        max-width: none;
    }

    form div.input-group {
        width: 100%;
    }

    form .enhanced-date input[type=text],
    form .element-row input[type=text] {
        width: 100px;
        max-width: none;
    }

    .label-for-inline-select {
        padding-top: 0px;
    }

    form dl dt.item-label {
        display: block;
        width: 100%;
        padding-right: 0px;
        float: none;
    }

    form dl dd.item-value {
        display: block;
        width: 100%;
    }

    .modal-dialog.modal-login {
        width: 95%;
    }

    /* help videos */

    #kt-help-popup .modal-dialog {
        width: 480px;
    }

    #kt-help-popup iframe {
        width: 480px;
        height: 270px;
    }

    .kt-fixed-icon-group {
        top: 70px;
    }

    .kt-fixed-icon-bg {
        width: 38px;
        height: 27px;
        background-position: -76px 0px;
    }

    .dialog-video-icon-popup .dialog-video-icon-popup-links {
        min-height: 27px;
        margin: 0 54px 0 0;
    }

    .dialog-video-icon-popup-links .dialog-video-icon-popup-arrow {
        top: 13px
    }

}

@media (max-width: 500px) {

    /* help videos */

    .kt-fixed-icon-group { display: none; }

}

/* Extra extra small devices ( < 380 Pixel )
------------------------- */
@media (max-width: 380px) {

    .hidden-xxs {
        display: none !important;
    }

    .visible-xxs,
    .visible-xxs.hidden-xs {
        display: inherit !important;
    }

    .enhanced-date.show-until > label:first-child + div + .cf-time select:last-child,
    .enhanced-date.show-until > div:first-child + .cf-time select:last-child {
        margin-left: -1px;
        -webkit-border-radius: 0 4px 4px 0;
        -moz-border-radius: 0 4px 4px 0;
        border-radius: 0 4px 4px 0;
    }

    .enhanced-date.show-until > label:first-child + div + .cf-time + div + .cf-time select:first-child,
    .enhanced-date.show-until > div:first-child + .cf-time + div + .cf-time select:first-child {
        -webkit-border-radius: 4px 0 0 4px;
        -moz-border-radius: 4px 0 0 4px;
        border-radius: 4px 0 0 4px;
    }

    .enhanced-date.show-until > label:first-child + div + .cf-time,
    .enhanced-date.show-until > div:first-child + .cf-time {
        margin-bottom: 5px;
    }

    .enhanced-date div:first-child + div.cf-time + div,
    .enhanced-date label:first-child + div + div.cf-time + div {
        margin-left: 5px;
        margin-bottom: 5px;
        -webkit-border-radius: 4px 4px 4px 4px;
        -moz-border-radius: 4px 4px 4px 4px;
        border-radius: 4px 4px 4px 4px;
    }

    form select {
        max-width: 240px;
    }

    #account-info-wrapper {
        display: none;
    }

}


/* ProcessFlow */

.color-palette-picker {
    position: relative;
    display:inline-block;
    height:25px;
    width:25px;
    border-radius:2px;
    cursor:pointer;
}

.palette-color {
    position: relative;
    display:inline-block;
    width:20px;
    height:20px;
    margin:2px;
    cursor:pointer;
}

.bg- {
    box-shadow: inset 0 0 0 2px #c1cfe6;
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.bg-:before {
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    display: block;
    content: "";
    width: 200%;
    height: 2px;
    background-color: #c1cfe6;
    position: absolute;
    top: 5px;
    left: -6px;
}

.bg-teal-light {
    background-color: #6ECED9;
}

.bg-teal-dark {
    background-color: #0097AA;
}

.bg-peach-light {
    background-color: #E6AB9D;
}

.bg-peach-dark {
    background-color: #BC5F5F;
}

.bg-pink-light {
    background-color: #E286A6;
}

.bg-pink-dark {
    background-color: #C64873;
}

.bg-violet-light {
    background-color: #8D98D8;
}

.bg-violet-dark {
    background-color: #505EAF;
}

.bg-blue-light {
    background-color: #86B2DE;
}

.bg-blue-dark {
    background-color: #4182D9;
}

.bg-silver-light {
    background-color: #A4B3BA;
}

.bg-silver-dark {
    background-color: #6C818D;
}

.bg-brown-light {
    background-color: #B7A29B;
}

.bg-brown-dark {
    background-color: #9B6F60;
}

.bg-yellow-light {
    background-color: #E5B05C;
}

.bg-yellow-dark {
    background-color: #DE8225;
}

.bg-green-light {
    background-color: #7EAD7A;
}

.bg-green-dark {
    background-color: #367C44;
}

.bg-coldgreen-light {
    background-color: #62A8A0;
}

.bg-coldgreen-dark {
    background-color: #2C7466;
}

/* --- Glyphs --- */

@font-face {
    font-family: 'glyphs';
    src: url("/build/fonts/glyphs.eot");
    src: url("/build/fonts/glyphs.eot") format("embedded-opentype"), url("/build/fonts/glyphs.ttf") format("truetype"), url("/build/fonts/glyphs.woff") format("woff"), url("/build/fonts/glyphs.svg") format("svg");
    font-weight: normal;
    font-style: normal;
}

*[class*="glyph-"] {
    display: inline;
    color: inherit;
    vertical-align: middle;
    line-height: inherit;
    font-size: inherit;
    font-family: "glyphs";
    font-weight: normal;
    text-align: right;
    padding-right: 0.25rem;
}

.table-glyph {
    color: #5D9BFB;
    font-size: 2.5rem;
    background-color: transparent !important;
}

.glyph-action:before {
    content: "";
}

.glyph-choice:before {
    content: "";
}

.glyph-timer:before {
    content: "";
}

.glyph-delay:before {
    content: "";
}

.glyph-wait:before {
    content: "";
}

.glyph-arrow-up:before {
    content: "";
}

.glyph-arrow-right:before {
    content: "";
}

.glyph-arrow-down:before {
    content: "";
}

.glyph-arrow-left:before {
    content: "";
}

.glyph-arrow-up-bold:before {
    content: "";
}

.glyph-arrow-right-bold:before {
    content: "";
}

.glyph-arrow-down-bold:before {
    content: "";
}

.glyph-arrow-left-bold:before {
    content: "";
}

.glyph-checkmark:before {
    content: "";
}

.glyph-checkmark-bold:before {
    content: "";
}

.glyph-plus:before {
    content: "";
}

.glyph-plus-bold:before {
    content: "";
}

.glyph-minus:before {
    content: "";
}

.glyph-minus-bold:before {
    content: "";
}

.glyph-cross:before {
    content: "";
}

.glyph-cross-bold:before {
    content: "";
}

.glyph-cr-play:before {
    content: "";
}

.glyph-cr-plus:before {
    content: "";
}

.glyph-cr-minus:before {
    content: "";
}

.glyph-cr-checkmark:before {
    content: "";
}

.glyph-sq-arrow-down:before {
    content: "";
}

.glyph-sq-arrow-left:before {
    content: "";
}

.glyph-sq-arrow-right:before {
    content: "";
}

.glyph-sq-arrow-up:before {
    content: "";
}

.glyph-sq-arrow-cross:before {
    content: "";
}

.glyph-sq-arrow-plus:before {
    content: "";
}

.glyph-sq-arrow-minus:before {
    content: "";
}

.glyph-happy:before {
    content: "";
}

.glyph-neutral:before {
    content: "󩍁";
}

.glyph-sad:before {
    content: "";
}

.glyph-triangle-down:before {
    content: "";
}

.glyph-triangle-left:before {
    content: "";
}

.glyph-triangle-right:before {
    content: "";
}

.glyph-triangle-up:before {
    content: "";
}

.glyph-pointer-up:before {
    content: "";
}

.glyph-pointer-right:before {
    content: "";
}

.glyph-pointer-down:before {
    content: "";
}

.glyph-pointer-left:before {
    content: "";
}

.glyph-pointer-up-2:before {
    content: "";
}

.glyph-pointer-right-2:before {
    content: "";
}

.glyph-pointer-down-2:before {
    content: "";
}

.glyph-pointer-left-2:before {
    content: "";
}

.glyph-cursor-move:before {
    content: "";
}

.glyph-resize:before {
    content: "";
}

.glyph-minimize:before {
    content: "";
}

.glyph-maximize:before {
    content: "";
}

.glyph-star:before {
    content: "";
}

.glyph-heart:before {
    content: "";
}

.glyph-link:before {
    content: "";
}

.glyph-save:before {
    content: "";
}

.glyph-hyperlink:before {
    content: "";
}

.glyph-edit:before {
    content: "";
}

.glyph-edit-2:before {
    content: "";
}

.glyph-thumb-down:before {
    content: "";
}

.glyph-thumb-up:before {
    content: "";
}

.glyph-download:before {
    content: "";
}

.glyph-download-cloud:before {
    content: "";
}

.glyph-upload:before {
    content: "";
}

.glyph-upload-cloud:before {
    content: "";
}

.glyph-email:before {
    content: "";
}

.glyph-email-bell:before {
    content: "";
}

.glyph-phone:before {
    content: "";
}

.glyph-help:before {
    content: "";
}

.glyph-trash:before {
    content: "";
}

.glyph-trashbin:before {
    content: "";
}

.glyph-grid:before {
    content: "";
}

.glyph-settings:before {
    content: "";
}

.glyph-settings-2:before {
    content: "";
}

.glyph-settings-3:before {
    content: "";
}

.glyph-gear:before {
    content: "";
}

.glyph-book:before {
    content: "";
}

.glyph-graduation-cap:before {
    content: "";
}

.glyph-folder:before {
    content: "";
}

.glyph-folder-play:before {
    content: "";
}

.glyph-folder-music:before {
    content: "";
}

.glyph-folder-images:before {
    content: "";
}

.glyph-laptop:before {
    content: "";
}

.glyph-tv:before {
    content: "";
}

.glyph-world:before {
    content: "";
}

.glyph-location:before {
    content: "";
}

.glyph-reload:before {
    content: "";
}

.glyph-search:before {
    content: "";
}

.glyph-search-2:before {
    content: "";
}

.glyph-menu:before {
    content: "";
}

.glyph-menu-dropdown:before {
    content: "";
}

.glyph-menu-cr-vertical:before {
    content: "";
}

.glyph-bell:before {
    content: "";
}

.glyph-quote:before {
    content: "";
}

.glyph-alert:before {
    content: "";
}

.glyph-lock:before {
    content: "";
}

.glyph-unlock:before {
    content: "";
}

.glyph-send:before {
    content: "";
}

.glyph-pin:before {
    content: "";
}

.glyph-mappin:before {
    content: "";
}

.glyph-key:before {
    content: "";
}

.glyph-warning:before {
    content: "";
}

.glyph-warning-2:before {
    content: "";
}

.glyph-rocket:before {
    content: "";
}

.glyph-document:before {
    content: "";
}

.glyph-reply-all:before {
    content: "";
}

.glyph-reply:before {
    content: "";
}

.glyph-evernote:before {
    content: "";
}

.glyph-hangouts:before {
    content: "";
}

.glyph-skype-cr:before {
    content: "";
}

.glyph-skype:before {
    content: "";
}

.glyph-vine-cr:before {
    content: "";
}

.glyph-vine:before {
    content: "";
}

.glyph-yelp:before {
    content: "";
}

.glyph-facebook-cr:before {
    content: "";
}

.glyph-facebook:before {
    content: "";
}

.glyph-flickr-cr:before {
    content: "";
}

.glyph-flickr:before {
    content: "";
}

.glyph-instagram-cr:before {
    content: "";
}

.glyph-instagram:before {
    content: "";
}

.glyph-linkedin-cr:before {
    content: "";
}

.glyph-linkedin:before {
    content: "";
}

.glyph-pinterest-cr:before {
    content: "";
}

.glyph-pinterest:before {
    content: "";
}

.glyph-sand-clock:before {
    content: "";
}

.glyph-tumblr:before {
    content: "";
}

.glyph-twitter-cr:before {
    content: "";
}

.glyph-twitter:before {
    content: "";
}

.glyph-vimeo-cr:before {
    content: "";
}

.glyph-vimeo:before {
    content: "";
}

.glyph-youtube-cr:before {
    content: "";
}

.glyph-youtube:before {
    content: "";
}

.glyph-feather:before {
    content: "";
}

.glyph-connection:before {
    content: "";
}

.glyph-world-location:before {
    content: "";
}

.glyph-amazon:before {
    content: "";
}

.glyph-google:before {
    content: "";
}

.glyph-google-cr:before {
    content: "";
}

.glyph-google-plus:before {
    content: "";
}

.glyph-google-plus-cr:before {
    content: "";
}

.glyph-google-hangouts:before {
    content: "";
}

.glyph-facebook-sq:before {
    content: "";
}

.glyph-telegram:before {
    content: "";
}

.glyph-rss:before {
    content: "";
}

.glyph-rss-sq:before {
    content: "";
}

.glyph-reddit:before {
    content: "";
}

.glyph-chart-1:before {
    content: "";
}

.glyph-chart-2:before {
    content: "";
}

.glyph-chart-3:before {
    content: "";
}

.glyph-chart-4:before {
    content: "";
}

.glyph-chart-5:before {
    content: "";
}

.glyph-chart-6:before {
    content: "";
}

.glyph-chart-7:before {
    content: "";
}

.glyph-ab-testing:before {
    content: "";
}

.glyph-abtesting:before {
    content: "";
}

.glyph-contact-update:before {
    content: "";
}

.glyph-contact-delete:before {
    content: "";
}

.glyph-sms-bell:before {
    content: "";
}

.glyph-sms:before {
    content: "";
}

.glyph-tag:before {
    content: "";
}

.glyph-tag-add:before {
    content: "";
}

.glyph-tag-delete:before {
    content: "";
}

.glyph-user:before {
    content: "";
}

.glyph-return:before {
    content: "";
}

.glyph-flowchart:before {
    content: "";
}

.glyph-outbound:before {
    content: "";
}

.glyph-target:before {
    content: "";
}

.glyph-email-send:before {
    content: "";
}

.glyph-email-open:before {
    content: "";
}

.glyph-email-click:before {
    content: "";
}

.glyph-sms-send:before {
    content: "";
}

.glyph-sms-open:before {
    content: "";
}

.glyph-sms-click:before {
    content: "";
}

.glyph-click:before {
    content: "";
}

.glyph-send-2:before {
    content: "";
}

.glyph-calendar:before {
    content: "";
}

.glyph-calendar-clock:before {
    content: "";
}

.glyph-calendar-clock-2:before {
    content: "";
}

.glyph-email-sign:before {
    content: "";
}

.glyph-stop:before {
    content: "";
}

.glyph-stop-hand:before {
    content: "";
}

.glyph-medal:before {
    content: "";
}

.glyph-trophy:before {
    content: "";
}

.glyph-number-sign:before {
    content: "";
}

.glyph-sandclock:before {
    content: "";
}

.glyph-text:before {
    content: "";
}

.glyph-text-multiple:before {
    content: "";
}

.glyph-url:before {
    content: "";
}

.glyph-automation:before {
    content: "";
}

.glyph-automation-stop:before {
    content: "";
}

.glyph-enlarge:before {
    content: "";
}

.glyph-reduce:before {
    content: "";
}

.glyph-form-fields:before {
    content: "";
}

.glyph-funnel:before {
    content: "";
}

.glyph-door-exit:before {
    content: "";
}

.glyph-pixel-search:before {
    content: "";
}

.glyph-newsletter-sms:before {
    content: "";
}

.glyph-newsletter-email:before {
    content: "";
}

.glyph-tag-image:before {
    content: "";
}

.glyph-tag-click:before {
    content: "";
}

.glyph-email-gear:before {
    content: "";
}

.glyph-sms-gear:before {
    content: "";
}

.glyph-view:before {
    content: "";
}

.glyph-duplicate:before {
    content: "";
}

.glyph-flowchart-expand-all:before {
    content: "";
}

.glyph-flowchart-collapse-all:before {
    content: "";
}

.glyph-flowchart-collapse-to-no:before {
    content: "";
}

.glyph-flowchart-collapse-to-yes:before {
    content: "";
}

.glyph-zoom-in:before {
    content: "";
}

.glyph-zoom-out:before {
    content: "";
}

.glyph-zoom-reset:before {
    content: "";
}

.glyph-open-window:before {
    content: "";
}

.glyph-flag:before {
    content: "";
}

.glyph-klicky-run:before {
    content: "";
}

.glyph-klicky-warning:before {
    content: "";
}

.glyph-klicky-cross:before {
    content: "";
}

.glyph-klicky-info:before {
    content: "";
}

.glyph-wufoo:before {
    content: "";
}

.glyph-wistia:before {
    content: "";
}

.glyph-twilio:before {
    content: "";
}

.glyph-thrivethemes:before {
    content: "";
}

.glyph-paypal:before {
    content: "";
}

.glyph-optimizepress:before {
    content: "";
}

.glyph-nexmo:before {
    content: "";
}

.glyph-leadpages:before {
    content: "";
}

.glyph-digistore:before {
    content: "";
}

.glyph-clickbank:before {
    content: "";
}

.glyph-affilicon:before {
    content: "";
}

.glyph-cardreaderpro:before {
    content: "";
}

.glyph-form-widget:before {
    content: "";
}

.glyph-form-custom:before {
    content: "";
}

.glyph-form-inline:before {
    content: "";
}

.glyph-form-raw:before {
    content: "";
}

.glyph-form-combo:before {
    content: "";
}

.glyph-api-key:before {
    content: "";
}

.glyph-facebook-button:before {
    content: "";
}

.glyph-goal:before {
    content: "";
}

.glyph-race-flag:before {
    content: "";
}

.glyph-wave-flag:before {
    content: "";
}

/* Helpscout Beacon minimize button */

#beacon-container.minimized .BeaconFabButtonFrame {
  right: -30px !important;

}

#beacon-container.minimized #hsb-minimize > button {
  display: none !important;
}

#beacon-container #hsb-minimize {
  position: fixed;
  right: 22px;
  bottom: 85px;
  z-index: 1049;
}

#beacon-container #hsb-minimize > button {
  display: block;
  padding: 0px 2px 3px 2px;
  height: 20px;
  width: 20px;

  color: white;
  background-color: rgb(255, 162, 15);
  border-radius: 50%;
  border-style: none;
}

#beacon-container #hsb-minimize > button:hover {
  background-color: rgb(247, 154, 7);
}

#beacon-container #hsb-minimize > button > img {
  display: block;
  width: 100%;

  transform: scale(0.6);
}

@media (max-height: 740px) {
  #beacon-container #hsb-minimize {
    right: 10px !important;
    bottom: 70px !important;
  }
}

.main-application-menu.navbar > .container .navbar-brand  {
    display: block;
}

.main-application-menu {
    .navbar-brand>img {
        display: inline-block;
    }

/* Our browser window for testing is 1042px wide, if we change this to for example 1200, there are tests failures */
@media (max-width: 1040px) {
        .navbar-logo-toggle {
            width: 100%;
            text-align: center;
        }

        #top-menu-beamer-mobile {
            display: block;
        }

        /* Override the default Bootstrap breakpoint for small screens */
        /* https://stackoverflow.com/a/36289507 */
        .navbar-header {
            float: none;
        }
        .navbar-left,.navbar-right {
            float: none !important;
        }
        .navbar-toggle {
            display: block;
        }
        .navbar-collapse {
            border-top: 1px solid transparent;
            box-shadow: inset 0 1px 0 rgba(255,255,255,0.1);
        }
        .navbar-fixed-top {
            top: 0;
            border-width: 0 0 1px;
        }
        .navbar-collapse.collapse {
            display: none!important;
        }
        .navbar-nav {
            float: none!important;
            margin-top: 7.5px;
        }
        .navbar-nav>li {
            float: none;
        }
        .navbar-nav>li>a {
            padding-top: 10px;
            padding-bottom: 10px;
        }
        .collapse.in{
            display:block !important;
        }
    }

}

a.magic-select-clear {
    margin-left: 10px;
}
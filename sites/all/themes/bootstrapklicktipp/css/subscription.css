@CHARSET "UTF-8";

/*
#######################################
# Klick-Tipp subscription pages
#######################################
*/

/* Confirmation Page */

#subscription-container .pending-headline {
    position: relative;
    width: 604px;
    height: 63px;
    margin: 0 auto 20px auto;
    background: transparent url(../../../../../misc/img/optin_confirmation_sprite.png) no-repeat scroll 0px 0px;
}

body.lang-pt-br #subscription-container .pending-headline {
    position: relative;
    width: 627px;
    height: 63px;
    margin: 0 auto 20px auto;
    background: transparent url(../../../../../misc/img/optin_confirmation_sprite_br.png) no-repeat scroll 0px 0px;
}

#subscription-container .img-envelope {
    position: relative;
    display: inline-block;
    width: 512px;
    height: 437px;
    background: transparent url(../../../../../misc/img/optin_confirmation_sprite.png) no-repeat scroll 0px -176px;
}

body.lang-pt-br #subscription-container .img-envelope {
    position: relative;
    display: inline-block;
    width: 512px;
    height: 437px;
    background: transparent url(../../../../../misc/img/optin_confirmation_sprite_br.png) no-repeat scroll 0px -176px;
}

#subscription-container .user-info {
    position: absolute;
    left: 100px;
    top: 114px;
    font-size: 13px;
    font-family: arial;
    color: #000000;
    text-align: left;
    width: 323px;
    height: 17px;
    overflow: hidden;
    white-space: nowrap;
}

#subscription-container ol {
    position: relative;
    height: 475px;
    display: table-cell;
    vertical-align: middle;
    padding-left: 20px;
    font-size: 16px;
}

#subscription-container ol li {
    font-weight: bold;
}

#subscription-container ol li p {
    font-weight: normal;
}

#subscription-container ol li p.bold {
    font-weight: bold;
}

#subscription-container ol li .form-group:last-child {
    margin-bottom: 0px;
}

#subscription-container .vcard-button {
    position: relative;
    display: inline-block;
    width: 409px;
    height: 61px;
    background: transparent url(../../../../../misc/img/optin_confirmation_sprite.png) scroll no-repeat 0px -613px;
    border: none;
    margin: 10px 0px 5px 0px;
}

#subscription-container .vcard-button:hover {
    background-position: -409px -613px;
}

#subscription-container .vcard-button:active {
    background-position: -818px -613px;
}

body.lang-pt-br #subscription-container .vcard-button {
    position: relative;
    display: inline-block;
    width: 409px;
    height: 61px;
    background: transparent url(../../../../../misc/img/optin_confirmation_sprite_br.png) scroll no-repeat 0px -613px;
    border: none;
    margin: 10px 0px 5px 0px;
}

body.lang-pt-br #subscription-container .vcard-button:hover {
    background-position: -409px -613px;
}

body.lang-pt-br #subscription-container .vcard-button:active {
    background-position: -818px -613px;
}

/* Responsive */

@media (min-width: 1200px) {

    #subscription-container .hide-lg-md {
        display: none;
    }

    #subscription-container .hide-sm-xs {
        display: block;
    }

}

@media (min-width: 993px) and (max-width: 1200px) {

    #subscription-container .hide-lg-md {
        display: none;
    }

    #subscription-container .hide-sm-xs {
        display: block;
    }

    #subscription-container .img-envelope {
        width: 399px;
        height: 337px;
        margin-top: 40px;
        background-position: -512px -176px;
    }

    body.lang-pt-br #subscription-container .img-envelope {
        width: 399px;
        height: 337px;
        margin-top: 40px;
        background-position: -512px -176px;
    }

    #subscription-container .user-info {
        left: 83px;
        top: 89px;
        font-size: 12px;
    }

    body.lang-pt-br #subscription-container .user-info {
        left: 80px;
        top: 87px;
        font-size: 12px;
    }

}

@media (min-width: 768px) and (max-width: 992px) {

    #subscription-container .hide-lg-md {
        display: block;
    }

    #subscription-container .hide-sm-xs {
        display: none;
    }

    #subscription-container .img-envelope {
        width: 512px;
        height: 437px;
        background-position: 0px -176px;
        margin: 10px 0 20px 0;
    }

    body.lang-pt-br #subscription-container .img-envelope {
        width: 512px;
        height: 437px;
        background-position: 0px -176px;
        margin: 10px 0 20px 0;
    }

    #subscription-container .user-info {
        left: 100px;
        top: 114px;
        font-size: 13px;
    }

}

@media (max-width: 767px) {

    #subscription-container .hide-lg-md {
        display: block;
    }

    #subscription-container .hide-sm-xs {
        display: none;
    }

    #subscription-container .pending-headline {
        width: 342px;
        height: 113px;
        background-position: 0px -63px;
        margin-bottom: 50px;
    }

    body.lang-pt-br #subscription-container .pending-headline {
        width: 342px;
        height: 113px;
        background-position: 0px -63px;
        margin-bottom: 50px;
    }

    #subscription-container .img-envelope {
        width: 353px;
        height: 300px;
        background-position: -911px -176px;
        margin: 20px 0;
    }

    body.lang-pt-br #subscription-container .img-envelope {
        width: 353px;
        height: 300px;
        background-position: -911px -176px;
        margin: 20px 0;
    }

    #subscription-container .user-info {
        left: 73px;
        top: 79px;
        font-size: 12px;
    }

    #subscription-container .vcard-button {
        width: 329px;
        height: 49px;
        background-position: 0px -674px;
    }

    #subscription-container .vcard-button:hover {
        background-position: -329px -674px;
    }

    #subscription-container .vcard-button:active {
        background-position: -658px -674px;
    }

    body.lang-pt-br #subscription-container .vcard-button {
        width: 329px;
        height: 49px;
        background-position: 0px -674px;
    }

    body.lang-pt-br #subscription-container .vcard-button:hover {
        background-position: -329px -674px;
    }

    body.lang-pt-br #subscription-container .vcard-button:active {
        background-position: -658px -674px;
    }

}

@media (max-width: 380px) {
}

/* Thankyou Page */

#thankyou-container {
    font-size: 16px;
}

#thankyou-container .thankyou-headline {
    position: relative;
    width: 483px;
    height: 63px;
    margin: 0 auto 30px auto;
    background: transparent url(../../../../../misc/img/optin_thankyou_sprite.png) no-repeat scroll 0 -50px;
}

body.lang-pt-br #thankyou-container .thankyou-headline {
    position: relative;
    width: 798px;
    height: 62px;
    margin: 0 auto 30px auto;
    background: transparent url(../../../../../misc/img/optin_thankyou_sprite_br.png) no-repeat scroll 0px 0px;
}

#thankyou-container .vertical-align {
    position: relative;
    height: 365px;
    display: table-cell;
    vertical-align: middle;
    padding-left: 0px;
    font-size: 16px;
}

#thankyou-container .padding-left {
    padding-left: 0px;
}

#thankyou-container .vcard-button {
    position: relative;
    display: inline-block;
    width: 409px;
    height: 61px;
    background: transparent url(../../../../../misc/img/optin_thankyou_sprite.png) scroll no-repeat 0px -706px;
    border: none;
    margin: 10px 0px 5px 0px;
}

#thankyou-container .vcard-button:hover {
    background-position: -409px -706px;
}

#thankyou-container .vcard-button:active {
    background-position: -818px -706px;
}

body.lang-pt-br #thankyou-container .vcard-button {
    position: relative;
    display: inline-block;
    width: 409px;
    height: 61px;
    background: transparent url(../../../../../misc/img/optin_thankyou_sprite_br.png) scroll no-repeat 0px -706px;
    border: none;
    margin: 10px 0px 5px 0px;
}

body.lang-pt-br #thankyou-container .vcard-button:hover {
    background-position: -409px -706px;
}

body.lang-pt-br #thankyou-container .vcard-button:active {
    background-position: -818px -706px;
}

#thankyou-container .img-screen {
    position: relative;
    width: 578px;
    height: 427px;
    background: transparent url(../../../../../misc/img/optin_thankyou_sprite.png) scroll no-repeat 0px -279px;
    margin: 0 0 25px 0;
}

body.lang-pt-br #thankyou-container .img-screen {
    position: relative;
    width: 578px;
    height: 427px;
    background: transparent url(../../../../../misc/img/optin_thankyou_sprite_br.png) scroll no-repeat 0px -279px;
    margin: 0 0 25px 0;
}

#thankyou-container .user-info {
    position: absolute;
    left: 100px;
    top: 180px;
}

#thankyou-container .user-info .item {
    position: relative;
    font-size: 13px;
    line-height: 18px;
    vertical-align: middle;
    font-family: arial;
    font-weight: normal;
    color: #000000;
    text-align: left;
    width: 200px;
    height: 18px;
    overflow: hidden;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

#thankyou-container .user-info .item.header {
    margin-left: -20px;
    font-size: 13px;
}

#thankyou-container .user-info .item.bold {
    font-weight: bold;
}

#thankyou-container .user-info .item.address {
    margin-bottom: 5px;
}

#thankyou-container .user-info .item.email {
    margin-bottom: 15px;
    margin-top: 10px;
}

#thankyou-container .unsubscribe {
    margin: 25px 0;
    line-height: 16px;
    vertical-align: middle;
    padding-left: 20px;
    background: transparent url(../../../../../misc/img/optin_thankyou_sprite.png) scroll no-repeat 0 -815px;
}

body.lang-pt-br #thankyou-container .unsubscribe {
    margin: 25px 0;
    line-height: 16px;
    vertical-align: middle;
    padding-left: 20px;
    background: transparent url(../../../../../misc/img/optin_thankyou_sprite_br.png) scroll no-repeat 0 -815px;
}

/* general */
.bold {
    font-weight: bold;
}

.lowlight {
    color: #888888;
    font-size: 14px;
}

.spam-info {
    margin-bottom: 25px;
}

/* Responsive */

@media (min-width: 1200px) {

    #thankyou-container .hide-lg-md {
        display: none;
    }

    #thankyou-container .hide-sm-xs {
        display: block;
    }

}

@media (min-width: 993px) and (max-width: 1200px) {

    #thankyou-container .hide-lg-md {
        display: none;
    }

    #thankyou-container .hide-sm-xs {
        display: block;
    }

    #thankyou-container .img-screen {
        width: 501px;
        height: 415px;
        background-position: -578px -279px;
    }

    body.lang-pt-br #thankyou-container .img-screen {
        width: 501px;
        height: 415px;
        background-position: -578px -279px;
    }

    #thankyou-container .user-info {
        left: 110px;
        position: absolute;
        top: 168px;
    }

    #thankyou-container .vertical-align {
        height: 355px;
    }

}

@media (min-width: 768px) and (max-width: 992px) {

    #thankyou-container .hide-lg-md {
        display: block;
    }

    #thankyou-container .hide-sm-xs {
        display: none;
    }

    #thankyou-container .vertical-align {
        height: 220px;
        padding-left: 50px;
    }

    #thankyou-container .padding-left {
        padding-left: 50px;
    }

}

@media (max-width: 767px) {

    #thankyou-container .hide-lg-md {
        display: block;
    }

    #thankyou-container .hide-sm-xs {
        display: none;
    }

    #thankyou-container .thankyou-headline {
        width: 270px;
        height: 87px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 30px;
        background-position: 0px -152px;
    }

    body.lang-pt-br #thankyou-container .thankyou-headline {
        width: 378px;
        height: 100px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 30px;
        background-position: 0px -65px;
    }

    #thankyou-container .vcard-button {
        width: 329px;
        height: 49px;
        background-position: 0px -767px;
        margin: 15px 0;
    }

    #thankyou-container .vcard-button:hover {
        background-position: -329px -767px;
    }

    #thankyou-container .vcard-button:active {
        background-position: -658px -767px;
    }

    body.lang-pt-br #thankyou-container .vcard-button {
        width: 329px;
        height: 49px;
        background-position: 0px -767px;
        margin: 15px 0;
    }

    body.lang-pt-br #thankyou-container .vcard-button:hover {
        background-position: -329px -767px;
    }

    body.lang-pt-br #thankyou-container .vcard-button:active {
        background-position: -658px -767px;
    }

    #thankyou-container .img-screen {
        width: 441px;
        height: 410px;
        background-position: -1079px -279px;
    }

    body.lang-pt-br #thankyou-container .img-screen {
        width: 441px;
        height: 410px;
        background-position: -1079px -279px;
    }

    #thankyou-container .user-info {
        left: 128px;
        position: absolute;
        top: 163px;
    }

    #thankyou-container .vertical-align {
        height: 220px;
        padding-left: 36px;
    }

    #thankyou-container .padding-left {
        padding-left: 36px;
    }

}

@media (max-width: 380px) {

    #thankyou-container .thankyou-headline {
        width: 270px;
        height: 111px;
        margin-left: auto;
        margin-right: auto;
        background-position: 0px -152px;
    }
    
    body.lang-pt-br #thankyou-container .thankyou-headline {
        width: 378px;
        height: 100px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 30px;
        background-position: 0px -65px;
    }

}
<?php
/**
 * @file
 * Default theme implementation to display a single Drupal page.
 *
 * The doctype, html, head and body tags are not in this template. Instead they
 * can be found in the html.tpl.php template in this directory.
 *
 * Available variables:
 *
 * General utility variables:
 * - $base_path: The base URL path of the Drupal installation. At the very
 *   least, this will always default to /.
 * - $directory: The directory the template is located in, e.g. modules/system
 *   or themes/bartik.
 * - $is_front: TRUE if the current page is the front page.
 * - $logged_in: TRUE if the user is registered and signed in.
 * - $is_admin: TRUE if the user has permission to access administration pages.
 *
 * Site identity:
 * - $front_page: The URL of the front page. Use this instead of $base_path,
 *   when linking to the front page. This includes the language domain or
 *   prefix.
 * - $logo: The path to the logo image, as defined in theme configuration.
 * - $site_name: The name of the site, empty when display has been disabled
 *   in theme settings.
 * - $site_slogan: The slogan of the site, empty when display has been disabled
 *   in theme settings.
 *
 * Navigation:
 * - $main_menu (array): An array containing the Main menu links for the
 *   site, if they have been configured.
 * - $secondary_menu (array): An array containing the Secondary menu links for
 *   the site, if they have been configured.
 * - $breadcrumb: The breadcrumb trail for the current page.
 *
 * Page content (in order of occurrence in the default page.tpl.php):
 * - $title_prefix (array): An array containing additional output populated by
 *   modules, intended to be displayed in front of the main title tag that
 *   appears in the template.
 * - $title: The page title, for use in the actual HTML content.
 * - $title_suffix (array): An array containing additional output populated by
 *   modules, intended to be displayed after the main title tag that appears in
 *   the template.
 * - $messages: HTML for status and error messages. Should be displayed
 *   prominently.
 * - $tabs (array): Tabs linking to any sub-pages beneath the current page
 *   (e.g., the view and edit tabs when displaying a node).
 * - $action_links (array): Actions local to the page, such as 'Add menu' on the
 *   menu administration interface.
 * - $feed_icons: A string of all feed icons for the current page.
 * - $node: The node object, if there is an automatically-loaded node
 *   associated with the page, and the node ID is the second argument
 *   in the page's path (e.g. node/12345 and node/12345/revisions, but not
 *   comment/reply/12345).
 *
 * Regions:
 * - $page['help']: Dynamic help text, mostly for admin pages.
 * - $page['highlighted']: Items for the highlighted content region.
 * - $page['content']: The main content of the current page.
 * - $page['sidebar_first']: Items for the first sidebar.
 * - $page['sidebar_second']: Items for the second sidebar.
 * - $page['header']: Items for the header region.
 * - $page['footer']: Items for the footer region.
 *
 * @see bootstrap_preprocess_page()
 * @see template_preprocess()
 * @see template_preprocess_page()
 * @see bootstrap_process_page()
 * @see template_process()
 * @see html.tpl.php
 *
 * @ingroup themeable
 */
?>

<?php print $GoogleScripts; ?>

<?php if ($showOtherAccountInfoBar) : ?>
    <div id="other-account-bar">
            <?php if ($showOtherAccountSupportInfo) : ?>
                <ul>
                    <li class="hide-tablet hide-phone">UserID: <?php print $otherAccountUserId; ?></li>
                    <li class="hide-phone">E-Mail: <?php print $otherAccountEmailAddress; ?></li>
                    <li class="hide-tablet hide-phone">ProductID: <?php print $otherAccountProductId; ?></li>
                    <li>Tier: <?php print $otherAccountTier; ?></li>
                    <li>Term: <?php print $otherAccountTerm; ?></li>
                    <li class="hide-tablet hide-phone">GroupID: <?php print $otherAccountGroupId; ?></li>
                    <li class="hide-tablet hide-phone">AmemberID: <?php print $otherAccountAMemberId; ?></li>
                    <li class="hide-tablet hide-phone">SpamActivity: <?php print $otherAccountSpamActivity; ?></li>
                    <li>Username:
                        <span class="subAccountUsername"><?php print $otherAccountUsername; ?></span>
                    </li>
                </ul>
            <?php else : ?>
                <ul class="subaccount">
                    <li><?php print $otherAccountMessage; ?>
                        <span class="subAccountUsername"><?php print $otherAccountUsername; ?></span>
                    </li>
                    <?php if (!empty($switchAccountsLink)): ?>
                    <li>
                        <a href="<?php print $switchAccountsLink['href']; ?>"><?php print $switchAccountsLink['title']; ?></a>
                    </li>
                  <?php endif; ?>
                </ul>
            <?php endif; ?>
        </span>

    </div>
<?php endif; ?>

<nav id="top-menu" class="navbar navbar-default navbar-fixed-top navbar-inverse main-application-menu" role="navigation">
  <div class="container">
    <div class="navbar-logo-toggle">
      <button type="button" class="navbar-toggle" data-toggle="collapse"
              data-target=".navbar-responsive-collapse">
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      <div id="top-menu-beamer-mobile"><a class="kt-beamer display-beamer" href="Javascript:void(0);"><div class="icon-beamer"></div></a></div>
      <a class="navbar-brand" href="/"><img src="<?php print $logo; ?>" style="height:30px;"/></a>
    </div>
    <div class="collapse navbar-collapse navbar-responsive-collapse">
      <?php print $MainMenu; ?>
      <?php print $SearchForm; ?>
    </div>
  </div>
</nav>

<div id="container" class="container">

  <?php if ($isLogin) : ?>

    <div class="row">
      <div id="content" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

        <div class="modal-dialog modal-login">
          <div class="modal-content">
            <div class="modal-header">
              <h4 class="modal-title"><?php print $title; ?></h4>
            </div>

            <?php if ($messages) : ?>
              <div class="modal-messages"><?php print $messages; ?></div>
            <?php endif; ?>

            <?php print render($page['content']); ?>

          </div>
        </div>
      </div>
    </div>


  <?php else : ?>

    <?php if ($breadcrumb && !$HideBreadcrumb ) : ?>
      <div class="breadcrumb-wrapper row">
        <?php print $breadcrumb; ?>
      </div>
    <?php endif; ?>

    <div class="row">
      <?php if (!empty($LocalTasks)): ?>
        <div id="sidebar" class="col-lg-2 col-md-3 col-sm-3 col-xs-12">
          <ul class="nav nav-list sidenav">
            <?php print $LocalTasks; ?>
          </ul>
        </div>
      <?php endif; ?>

      <div id="content"
           class="<?php print (empty($LocalTasks)) ? 'col-lg-12 col-md-12 col-sm-12 col-xs-12' : 'col-lg-10 col-md-9 col-sm-9 col-xs-12'; ?>">
        <?php print $messages; ?>
        <?php if (!empty($title) && !$HideTitle ): ?>
          <h1><?php print $title; ?></h1>
        <?php endif; ?>
        <?php print render($page['content_top']); ?>
        <?php print render($page['content']); ?>
        <?php print render($page['content_bottom']); ?>
      </div>
    </div>

    <?php if (!empty($page['page_closure'])): ?>
      <div id="footer" class="footer">
        <?php print render($page['page_closure']); ?>
      </div>
    <?php endif; ?>

  <?php endif; ?>

</div>

<?php $withHelpBlock = (empty($page['help_block'])) ? '' : ' with-help-block';?>
<nav id="footer-menu" class="navbar navbar-default navbar-inverse<?php print $withHelpBlock; ?>" role="navigation">
  <?php if ( !empty($page['help_block']) ) : ?>
    <div id="help-block">
      <div class="container">
        <?php print render($page['help_block']); ?>
      </div>
    </div>
  <?php endif; ?>
  <div class="container">
    <div class="navbar-copyright-toggle">
      <button type="button" class="navbar-toggle" data-toggle="collapse"
              data-target=".navbar-responsive-collapse-footer">
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      <p class="navbar-text"><?php print $copyright; ?></p>
    </div>
    <div class="collapse navbar-collapse  navbar-responsive-collapse-footer">
      <?php print $FooterMenu; ?>
    </div>
  </div>
</nav>

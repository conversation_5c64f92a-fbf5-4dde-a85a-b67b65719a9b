<?php

use App\Klicktipp\Settings;
use App\Klicktipp\UserGroups;

/**
 * Implements hook_css_alter().
 */
function bootstrapklicktipp_css_alter(&$css) {

  //Bootstrap CSS via CDN
  $bootstrap_cdn_css = variable_get(KLICKTIPP_THEME_BOOTSTRAP_CDN_CSS, 'https://maxcdn.bootstrapcdn.com/bootstrap/3.3.2/css/bootstrap.min.css');

  $css[$bootstrap_cdn_css] = array(
    'data' => $bootstrap_cdn_css,
    'type' => 'external',
    'every_page' => TRUE,
    'media' => 'all',
    'preprocess' => FALSE,
    'group' => CSS_THEME,
    'browsers' => array('IE' => TRUE, '!IE' => TRUE),
    'weight' => -2,
  );

  unset($css['misc/ui/jquery.ui.datepicker.css']);
  unset($css['misc/ui/jquery.ui.theme.css']);
  unset($css['misc/ui/jquery.ui.core.css']);
  unset($css['modules/system/system.base.css']);
  unset($css['modules/system/system.menus.css']);
  unset($css['modules/system/system.messages.css']);
  unset($css['modules/system/system.theme.css']);

}

/**
 * Implements hook_js_alter().
 */
function bootstrapklicktipp_js_alter(&$js) {

  global $user;

  //Bootstrap JavaScript via CDN
  $bootstrap_cdn_js = variable_get(KLICKTIPP_THEME_BOOTSTRAP_CDN_JAVASCRIPT, 'https://maxcdn.bootstrapcdn.com/bootstrap/3.3.2/js/bootstrap.min.js');

  $js[$bootstrap_cdn_js] = array(
    'data' => $bootstrap_cdn_js,
    'type' => 'external',
    'group' => JS_DEFAULT,
    'every_page' => TRUE,
    'weight' => -100,
    'scope' => 'header',
    'cache' => TRUE,
    'defer' => FALSE,
    'preprocess' => TRUE,
    'version' => NULL,
  );

  if ($user->uid) {

    //for authenticated users, add CKEditor, MagicSelects and D3 library
    //add scripts in a way that they will be cached but not aggregated

    //add CKEditor + jQuery adapter before bootstrap
    $weight = -200;

    //add Magicselect and D3

    $magicselect_class = 'misc/magicsuggest-class.js';
    $js[$magicselect_class] = array(
      'data' => $magicselect_class,
      'type' => 'file',
      'group' => JS_DEFAULT,
      'every_page' => TRUE,
      'weight' => $weight++,
      'scope' => 'header',
      'cache' => TRUE,
      'defer' => FALSE,
      'preprocess' => FALSE,
      'version' => NULL,
    );

    $magicselect = 'misc/magicsuggest.js';
    $js[$magicselect] = array(
      'data' => $magicselect,
      'type' => 'file',
      'group' => JS_DEFAULT,
      'every_page' => TRUE,
      'weight' => $weight++,
      'scope' => 'header',
      'cache' => TRUE,
      'defer' => FALSE,
      'preprocess' => FALSE,
      'version' => NULL,
    );

    $d3_library = 'misc/d3.v2.min.js';
    $js[$d3_library] = array(
      'data' => $d3_library,
      'type' => 'file',
      'group' => JS_DEFAULT,
      'every_page' => TRUE,
      'weight' => $weight++,
      'scope' => 'header',
      'cache' => TRUE,
      'defer' => FALSE,
      'preprocess' => FALSE,
      'version' => NULL,
    );

    $ckeditor_cdn = variable_get(KLICKTIPP_THEME_CKEDITOR_CDN_EDITOR, 'https://cdn.ckeditor.com/4.4.2/full/ckeditor.js');
    $ckeditor_jquery_adapter_cdn = variable_get(KLICKTIPP_THEME_CKEDITOR_CDN_JQUERY_ADAPTER, 'https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.4.2/adapters/jquery.js');

    $js[$ckeditor_cdn] = array(
      'data' => $ckeditor_cdn,
      'type' => 'external',
      'group' => JS_DEFAULT,
      'every_page' => TRUE,
      'weight' => 0,
      'scope' => 'header',
      'cache' => TRUE,
      'defer' => FALSE,
      'preprocess' => FALSE,
      'version' => NULL,
    );

    $js[$ckeditor_jquery_adapter_cdn] = array(
      'data' => $ckeditor_jquery_adapter_cdn,
      'type' => 'external',
      'group' => JS_DEFAULT,
      'every_page' => TRUE,
      'weight' => 1,
      'scope' => 'header',
      'cache' => TRUE,
      'defer' => FALSE,
      'preprocess' => FALSE,
      'version' => NULL,
    );

  }

}

/*
 * Page Layout
 */

/**
 * Implements hook_preprocess_html().
 *
 * @see html.tpl.php
 */
function bootstrapklicktipp_preprocess_html(&$variables) {

  global $user;

  // set hardcoded favicon
  $favicon = APP_URL . "misc/img/favicon.svg";
  $type = theme_get_setting('favicon_mimetype');
  drupal_add_html_head_link(array('rel' => 'shortcut icon', 'href' => drupal_strip_dangerous_protocols($favicon), 'type' => $type));

  $variables['head'] = drupal_add_html_head();
  $variables['GoogleTagManagerHeader'] = "";
  if (!empty($user->uid)) {
    // only include google tag manager user script if user is logged in
    $variables['GoogleTagManagerHeader'] = str_replace('%UserID%', $user->uid, Settings::get('klicktipp_theme_google_header_user_script'));
  }
  $variables['GoogleTagManagerHeader'] .= Settings::get('klicktipp_theme_google_header_script');

  //add classes to body-tag
  $body_class = array();

  //add a body class to identify the language used to change styles that depend on the language (example: images with text)
  //example:
  //button.order-now {background-image: url(order_button_sprite.png);} //default (language: de)
  //body.lang-pt-br button.order-now {background-image: url(order_button_sprite_br.png);} //language: pt-br
  $body_class[] = "lang-" . strtolower($variables['language']->language);

  //get local task to determine if page has an affix
  $PrimaryTasks = menu_primary_local_tasks();
  $SecondaryTasks = menu_secondary_local_tasks();
  if (!empty($PrimaryTasks) || !empty($SecondaryTasks) )
    $body_class[] = "has-sidebar";

  //check if the user is logged out and on the login/reset password page => theme login page
  $q = preg_replace('/[^a-z0-9\/]/', '', strtolower($_GET['q']));
  if (($q == 'user' || $q == 'user/password' || $q == 'user/twofactorauth' || $q == 'user/login') && !$user->uid) {
    $body_class[] = "user-login";
  }
  elseif ($user->uid){
    $info = UserGroups::GetAccountAccessInfo();
    if ($info['supportAccess'] || $info['otherAccountAccess']) {
      $body_class[] = 'other-account';
      if ($info['spamActivity']) {
        $body_class[] = 'other-account-spam-activity';
      }
    }
  }

  // theme the page without the Bootstrap container and row structure so we can use the full width of the browser
  //'ciapi_theme_landing_page' is set by ciapi_styleguide_landingpage() -> ciapi_set_variable()
  $theme_landingpage = &drupal_static('ciapi_theme_landing_page');
  if ( $theme_landingpage ) {
    $body_class[] = "styleguide-landingpage";
  }


  $variables['body_class'] = implode(' ', $body_class);
}

/**
 * Implements hook_preprocess_page().
 *
 * @see page.tpl.php
 */
function bootstrapklicktipp_preprocess_page(&$variables) {

  global $user;

  $isFirstLogin = arg(0) == 'login' && arg(2) != 'change-password';

  $PrimaryTasks = menu_primary_local_tasks();
  $SecondaryTasks = menu_secondary_local_tasks();
  $variables['LocalTasks'] = render($PrimaryTasks) . render($SecondaryTasks); //get menu_local_task for the affix;
  $variables['MainMenu'] = klicktipp_menu_main(0, $isFirstLogin);
  $variables['FooterMenu'] = klicktipp_menu_footer();

  $variables['copyright'] = t("KlickTipp !copy @year. All rights reserved", array('!copy' => '&copy;', '@year' => date('Y')));

  //theme the login page if
  // - the user is logged out and navigates to 'user' or 'user/twofactorauth'
  // - the user (logged in or out) navigates to user/password, @see klicktipp_form_alter() -> 'user_pass'
  $q = preg_replace('/[^a-z0-9\/]/', '', strtolower($_GET['q']));
  $variables['isLogin'] = (($q == 'user' || $q == 'user/twofactorauth' || $q == 'user/login') && !$user->uid) || $q == 'user/password';

  //Google scripts
  $variables['GoogleScripts'] = Settings::get(KLICKTIPP_THEME_GOOGLE_SCRIPTS);

  //hide breadcrumb and or title
  //set in content includes -> klicktipp_content_include_filter is called very early and drupal overwrites our reset with the default breadcrumb
  global $HideBreadcrumb, $HideTitle;
  $variables['HideBreadcrumb'] = $HideBreadcrumb;
  $variables['HideTitle'] = $HideTitle;

  /**
   * Change path of logo to point to same file as angular apps
   * effectively overwriting logo changes from admin backend.
   * Same has been already implemented in angular apps
   **/
  //$variables['logo'] = base_path() . 'misc/img/KT_Logo_White_130x32.png';

  $info = UserGroups::GetAccountAccessInfo();
  $variables['logo'] = $info['logo'];
  $variables['showOtherAccountInfoBar'] = $user->uid && !$variables['isLogin'] && ($info['supportAccess'] || $info['otherAccountAccess']);
  $variables['showOtherAccountSupportInfo'] = $info['supportAccess'];
  $variables['otherAccountMessage'] = t('AccountInfoBar::You are working in the account of');
  if (!empty($info['switchAccountsLink'])) {
    $variables['switchAccountsLink'] = $info['switchAccountsLink'];
  }
  $variables['otherAccountUsername'] = $info['username'];
  $variables['otherAccountUserId'] = $info['userId'];
  $variables['otherAccountEmailAddress'] = $info['emailAddress'];
  $variables['otherAccountProductId'] = $info['productID'];
  $variables['otherAccountTier'] = $info['tier'];
  $variables['otherAccountTerm'] = $info['term'];
  $variables['otherAccountGroupId'] = $info['groupId'];
  $variables['otherAccountAMemberId'] = $info['aMemberId'];
  $variables['otherAccountSpamActivity'] = $info['spamActivity'] ? 'YES' : 'NO';

}

/**
 * Theme local tasks as links for the affix
 * @param $variables
 * @return string
 */
function bootstrapklicktipp_menu_local_task($variables) {

  $link = $variables['element']['#link'];
  $link_text = $link['title'];
  $classes = array();

  if (!empty($variables['element']['#active'])) {
    // Add text to indicate active tab for non-visual users.
    $active = '<span class="element-invisible">' . t('(active tab)') . '</span>';

    // If the link does not contain HTML already, check_plain() it now.
    // After we set 'html'=TRUE the link will not be sanitized by l().
    if (empty($link['localized_options']['html'])) {
      $link['title'] = check_plain($link['title']);
    }
    $link['localized_options']['html'] = TRUE;
    $link_text = t('!local-task-title!active', array('!local-task-title' => $link['title'], '!active' => $active));

    $classes[] = 'active';
  }

  return '<li class="' . implode(' ', $classes) . '">' . l($link_text, $link['href'], $link['localized_options']) . "</li>\n";
}

/**
 * Format the breadcrumb
 */

function bootstrapklicktipp_breadcrumb($variables) {

  $breadcrumb = $variables['breadcrumb'];

  if (empty($breadcrumb))
    return '';

  if (count($breadcrumb) == 1)
    return '<div class="breadcrumb-row col-md-12"><ul class="breadcrumb"><li class="active">' . $breadcrumb[0] . '</li></ul></div>';

  $active = array_pop($breadcrumb);

  return '<div class="breadcrumb-row col-md-12"><ul class="breadcrumb col-md-12"><li>' .
  implode('</li><li><i class="icon-breadcrumb"></i>', $breadcrumb) .
  '</li><li class="active"><i class="icon-breadcrumb"></i>' . $active . '</ul></div>';
}

/**
 * Format the status messages
 */
function bootstrapklicktipp_status_messages($variables) {

  $display = $variables['display'];

  $output = "";

  foreach (drupal_get_messages($display) as $type => $messages) {

    $msg_type = '';
    $attributes = '';
    $tooltip = '';
    switch ($type) {
      case 'status':
        $msg_type = '-success';
        break;
      case 'error':
        $msg_type = '-danger';
        break;
      case 'warning':
        $msg_type = '-warning';
        break;
      case 'info':
        $msg_type = '-info';
        break;
      default:
        //Klick-Tipp message type
        //add a tooltip and a click-event to the close button (x)
        //the click event will send an AJAX request to set a "Remind me later" flag @see js_dpo_remind_me_later() in /misc/script.js
        //the message additional data contains the user id
        $custom_type = explode(':', $type); //"<MESSAGE-CUSTOM-TYPE>:<MESSAGE-ADDITIONAL-DATA>"
        if ($custom_type[0] == KLICKTIPP_ACCOUNT_DATA_PROCESSING_ORDER_ID) {
          $msg_type = '-warning';
          $attributes = 'data-event-click="js_dpo_remind_me_later" data-event-click-args="' . $custom_type[1] . '" data-tooltip-title="' . t("Remind me later") . '"';
          $tooltip = "has-tooltip";
        }
        else if ($custom_type[0] == KLICKTIPP_ACCOUNT_TWOFACTORAUTH_REMINDER) {
          $msg_type = '-warning';
          $attributes = 'data-event-click="js_twofacorauth_remind_me_later" data-event-click-args="' . $custom_type[1] . '" data-tooltip-title="' . t("Remind me later") . '"';
          $tooltip = "has-tooltip";
        }
        else
          $msg_type = '';
    }

    $output .= "<div class='alert alert$msg_type'><button type='button' class='close $tooltip' data-dismiss='alert' $attributes>&times;</button>";
    foreach ($messages as $message) {
      $output .= $message . '<br />';
    }
    $output .= "</div>";
  }

  return $output;

}

/*
 * Form elements
 */

/**
 * Return a themed form element.
 *
 * @param $variables
 *   An associative array containing the properties of the element.
 *   Properties used: title, description, id, required
 * @return
 *   A string representing the form element.
 *
 */
function bootstrapklicktipp_form_element($variables) {

  $element = &$variables['element'];

  // This function is invoked as theme wrapper, but the rendered form element
  // may not necessarily have been processed by form_builder().
  $element += array(
    '#title_display' => 'before',
  );

  // Add element #id for #type 'item'.
  if (isset($element['#markup']) && !empty($element['#id'])) {
    $attributes['id'] = $element['#id'];
  }
  else if ( !empty($element['#id']) ) {
    $attributes['id'] = $element['#id'] . '-wrapper';
  }
  // Add element's #type and #name as class to aid with JS/CSS selectors.
  $attributes['class'] = array('form-group', 'form-item');
  if (!empty($element['#type'])) {
    $attributes['class'][] = 'form-type-' . strtr($element['#type'], '_', '-');
  }
  if (!empty($element['#name'])) {
    $attributes['class'][] = 'form-item-' . strtr($element['#name'], array(' ' => '-', '_' => '-', '[' => '-', ']' => ''));
  }
  // Add a class for disabled elements to facilitate cross-browser styling.
  if (!empty($element['#attributes']['disabled'])) {
    $attributes['class'][] = 'form-disabled';
  }

  //Add Drupal6 wrapper id as css class
  //Note: Required for JavaScripts
  //@see: klicktipp_element_info() and klicktipp_form_clean_id()
  if (!empty($element['#wrapper_class'])) {
    $attributes['class'][] = $element['#wrapper_class'];
  }

  $output = '<div' . drupal_attributes($attributes) . '>' . "\n";

  // If #title is not set, we don't display any label or required marker.
  if (!isset($element['#title'])) {
    $element['#title_display'] = 'none';
  }
  $prefix = (isset($element['#field_prefix'])) ? '<span class="input-group-addon">' . $element['#field_prefix'] . '</span>' : '';
  $suffix = (isset($element['#field_suffix'])) ? '<span class="input-group-addon">' . $element['#field_suffix'] . '</span>' : '';

  switch ($element['#title_display']) {
    case 'before':
    case 'invisible':
      $output .= ' ' . theme('form_element_label', $variables);
      if (!empty($prefix) || !empty($suffix))
        $output .= '<div class="input-group">' . $prefix . $element['#children'] . $suffix . '</div>';
      else
        $output .= ' ' . $prefix . $element['#children'] . $suffix . "\n";
      break;

    case 'after':
      if (!empty($prefix) || !empty($suffix))
        $output .= '<div class="input-group">' . $prefix . $element['#children'] . $suffix . '</div>';
      else
        $output .= ' ' . $prefix . $element['#children'] . $suffix;
      $output .= ' ' . theme('form_element_label', $variables) . "\n";
      break;

    case 'none':
    case 'attribute':
      // Output no label and no required marker, only the children.
      if (!empty($prefix) || !empty($suffix))
        $output .= '<div class="input-group">' . $prefix . $element['#children'] . $suffix . '</div>';
      else
        $output .= ' ' . $prefix . $element['#children'] . $suffix . "\n";
      break;
  }

  if (!empty($element['#description'])) {
    $output .= ' <p class="help-block">' . $element['#description'] . "</p>\n";
  }

  $output .= "</div>\n";

  return $output;

}

/**
 * Return a themed label for a form element.
 *
 * @param $variables
 *   An associative array containing the properties of the element.
 *   Properties used: title, description, id, required
 * @return
 *   A string representing the label.
 *
 * KlickTipp:
 * - added #quickhelp
 */
function bootstrapklicktipp_form_element_label($variables) {

  $element = $variables['element'];
  // This is also used in the installer, pre-database setup.
  $t = get_t();

  // If title and required marker are both empty, output no label.
  if ((!isset($element['#title']) || $element['#title'] === '') && empty($element['#required'])) {
    return '';
  }

  // If the element is required, a required marker is appended to the label.
  $required = !empty($element['#required']) ? theme('form_required_marker', array('element' => $element)) : '';

  $title = filter_xss_admin($element['#title']);

  $attributes = array();
  // Style the label as class option to display inline with the element.
  if ($element['#title_display'] == 'after') {
    $attributes['class'] = 'option';
  }
  // Show label only to screen readers to avoid disruption in visual flows.
  elseif ($element['#title_display'] == 'invisible') {
    $attributes['class'] = 'element-invisible';
  }

  if (!empty($element['#id'])) {
    $attributes['for'] = $element['#id'];
  }

  $quickhelp = (empty($element['#quickhelp'])) ? '' : theme('klicktipp_quickhelp', $element);

  // The leading whitespace helps visually separate fields from inline labels.
  return ' <label' . drupal_attributes($attributes) . '>' .
  $t('!title !required', array('!title' => $title, '!required' => $required)) . "$quickhelp</label>\n";

}

/**
 * Theme a fieldset element
 *
 * Uses the Bootstrap accordion style for collapsability
 */

function bootstrapklicktipp_fieldset($variables) {
  $element = $variables['element'];

  $id = uniqid('fieldset-'); //to open/close
  if (!empty($element['#title'])) {
    if (!empty($element['#collapsible'])) {
      $header = '<div class="panel-heading"><a class="accordion-toggle" data-toggle="collapse" href="#' . $id . '">' . $element['#title'] . '<b class="caret"></b></a></div>';
    } else {
      $header = '<div class="panel-heading"><span class="accordion-toggle mouse-normal">' . $element['#title'] . '</span></div>';
    }
  }

  $collapsed = (empty($element['#collapsed'])) ? " in" : '';
  _form_set_class($element, array('form-group', 'panel-group'));
  $attributes = drupal_attributes($element['#attributes']);
  $content = (!empty($element['#children'])) ? $element['#children'] : '';
  $description = (!empty($element['#description'])) ? '<p class="fieldset-description">' . $element['#description'] . '</p>' : '';


  $html = '
    <div ' . $attributes . '>
      <div class="panel panel-default">
          ' . $header . '
        <div id="' . $id . '" class="panel-collapse collapse' . $collapsed . '">
          <div class="panel-body">' . $description . $content . '</div>
        </div>
      </div>
    </div>
  ';

  return $html;
}

/**
 * Format a textfield.
 *
 * @param $element
 *   An associative array containing the properties of the element.
 *   Properties used:  title, value, description, size, maxlength, required, attributes autocomplete_path
 * @return
 *   A themed HTML string representing the textfield.
 *
 * @ingroup themeable
 */
function bootstrapklicktipp_textfield($variables) {

  $element = $variables['element'];
  $element['#attributes']['type'] = 'text';
  element_set_attributes($element, array('id', 'name', 'value', 'size', 'maxlength'));

  $class = array('form-control', 'form-text');
  if (isset($element['#datepicker'])) {
    $class[] = 'klicktipp-datepicker';
    $class[] = 'input-short';
  }

  _form_set_class($element, $class);

  $extra = '';
  if ($element['#autocomplete_path'] && !empty($element['#autocomplete_input'])) {
    drupal_add_library('system', 'drupal.autocomplete');
    $element['#attributes']['class'][] = 'form-autocomplete';

    $attributes = array();
    $attributes['type'] = 'hidden';
    $attributes['id'] = $element['#autocomplete_input']['#id'];
    $attributes['value'] = $element['#autocomplete_input']['#url_value'];
    $attributes['disabled'] = 'disabled';
    $attributes['class'][] = 'autocomplete';
    $extra = '<input' . drupal_attributes($attributes) . ' />';
  }

  $output = '<input' . drupal_attributes($element['#attributes']) . ' />';

  return $output . $extra;

}

/**
 * Format a password field.
 *
 * add the Bootstrap form element class
 */
function bootstrapklicktipp_password($variables) {

  _form_set_class($variables['element'], array('form-control'));

  return theme_password($variables);

}

/**
 * Format a dropdown menu or scrolling selection box.
 *
 * add the Bootstrap form element class
 */
function bootstrapklicktipp_select($variables) {

  _form_set_class($variables['element'], array('form-control'));

  return theme_select($variables);

}

/*
 * Theme a select element with selectable option groups as a Bootstarp dropdown button
 * - #options must be flat array
 *    array(
 *      'groupA' => 'Group A',
 *      '1' => 'Option 1',
 *      '2' => 'Option 2',
 *      'groupB' => 'Group B',
 *      '3' => 'Option 3',
 *      '4' => 'Option 4',
 *    )
 * - #optgroups is an array containing the ids of the group
 *    array('groupA', 'groupB')
 * - optgroups can be styled with li.option-group
 * - group options can be styled with li.option
 * - Note: HTML is allowed for option titles -> make sure no user input is displayed
 *
 * - overwrites theme_klicktipp_select_selectable_optgroup()
 */
function bootstrapklicktipp_klicktipp_select_selectable_optgroup($variables) {

  $element = $variables['element'];
  $id = $element['#id'];

  // array_key_exists() accommodates the rare event where $element['#value'] is NULL.
  // isset() fails in this situation.
  $value_valid = isset($element['#value']) || array_key_exists('#value', $element);

  $options = '';
  $selected = t('Please select');
  $optgroups = (empty($element['#optgroups'])) ? array() : $element['#optgroups'];
  foreach ($element['#options'] as $key => $choice) {

    $key = (string) $key;
    if ($value_valid && $element['#value'] === $key) {
      $selected = $choice;
    }

    $onclick = "$('#$id').val('$key');$('#{$id}-selected').text('$choice');$('#$id').trigger('change')";
    $class = (in_array($key, $optgroups)) ? 'option-group' : 'option';

    $options .= '<li class="' . $class . '"><a href="#" onclick="' . $onclick . '">' . $choice . '</a></li>';

  }

  element_set_attributes($element, array('id', 'name', 'value'));

  if (form_get_error($element)) {
    $error_css = " error";
    $selected = t('Please select');
  }

  $output = '
    <div class="kt-optgroup-select-wrapper"><div class="btn-group kt-optgroup-select' . $error_css . '">
      <button id="' . $id . '-selected" type="button" class="btn btn-default selected-option" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">' . $selected . '</button>
      <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <span class="caret"></span>
      </button>
      <ul class="dropdown-menu">' . $options . '</ul>
    </div>
    <input type="hidden" ' . drupal_attributes($element['#attributes']) . ' />
    </div>
  ';

  return $output;

}

/**
 * Format a textarea.
 *
 * add the Bootstrap form element class

 */
function bootstrapklicktipp_textarea($variables) {

  _form_set_class($variables['element'], array('form-control'));

  return theme_textarea($variables);
}

/*
 * Add a quickhelp to the captcha
 */
function bootstrapklicktipp_captcha($variables) {

  $element = $variables['element'];

  $captcha = array(
    '#title' => t('CAPTCHA'),
    '#value' => $element['#children'],
    '#quickhelp' => 'captcha',
    '#attributes' => array('class' => 'captcha'),
  );

  return theme('klicktipp_block', $captcha);

}

//add Bootstrap table classes

function bootstrapklicktipp_table($variables) {

  $variables['attributes']['class'][] = 'table table-hover';

  $e2eId = $variables['attributes']['data-e2e-id'];

  if (!empty($e2eId)) {

    if (!empty($variables['header'])) {
      foreach ($variables['header'] as $index => &$item) {
        if (!is_array($item)) {
          $item = ['data' => $item];
        }
        if (empty($item['data-e2e-id'])) {
          $item['data-e2e-id'] = "$e2eId-h$index";
        }
      }
    }

    if (!empty($variables['rows'])) {

      foreach ($variables['rows'] as $ri => &$row) {

        // Note: the data structure is different for drag and drop tables
        //       add 'dragdrop' => TRUE, @see signatures overview
        $editRow = ($variables['dragdrop'] || $variables['dataRow']) ? $row['data'] : $row;

        foreach($editRow as $ci => &$cell) {
          if (!is_array($cell)) {
            $cell = ['data' => $cell];
          }
          if (empty($cell['data-e2e-id'])) {
            $cell['data-e2e-id'] = "$e2eId-r$ri-c$ci";
          }
        }

        if ($variables['dragdrop'] || $variables['dataRow']) {
          $row['data'] = $editRow;
        }
        else {
          $row = $editRow;
        }

      }
    }

  }

  return theme_table($variables);

}

function bootstrapklicktipp_tablesort_indicator($variables) {
  if ($variables['style'] == "asc") {
    return '<i class="icon-tablesort-down has-tooltip" data-toggle="tooltip" data-title="' . t('sort ascending') . '"></i>';
  } else {
    return '<i class="icon-tablesort-up has-tooltip" data-toggle="tooltip" data-title="' . t('sort descending') . '"></i>';
  }
}

/*
 * Helper
 */

function bootstrapklicktipp_klicktipp_quickhelp($variables) {

  $element = $variables['element'];

  $quickhelp = '';

  if (!empty($element['#quickhelp'])) {
    //$quickhelp = theme('klicktipp_quickhelp', $element);
    $help = klicktipp_quickhelp($element['#quickhelp']);

    //if quickhelp is inactive (no help text), do not show anything to customers
    if (!empty($help['help_text']) || user_access('administer klicktipp')) {

      $id = $help['id'];
      $title = $element['#title']; //use the elements label for title

      $content = (empty($help['help_text'])) ? t("No help text defined") : $help['help_text'];

      //admins see a link to edit the quick help
      $edit = (user_access('administer klicktipp')) ? "<div>" . l("<i class='icon-quickhelp-edit pull-right'></i>", "admin/structure/translate/quickhelp/$id/edit", array('attributes' => array('target' => '_blank'), 'html' => TRUE)) . "</div>" : '';

      //show a different icon for aktive/inactive quickhelps
      $status = (!empty($help['help_text'])) ? 'icon-quickhelp' : 'icon-quickhelp-admin';

      //html for the icon
      $tooltip = t('Click for more information.');
      $quickhelp = '<i id="quickhelp-' . $id . '" class="quickhelp-icon ' . $status . ' has-tooltip" data-title="' . $title . '" data-quickhelp=".quickhelp-' . $id . '" data-tooltip-title="' . $tooltip . '"></i>';

      //store html content in invisible div
      //the data-content attribute doesn't take html, the popover script will include it
      $quickhelp .= '<div class="quickhelp-' . $id . ' hide" style="display: none;">' . $content . $edit . '</div>';

    }

  }

  return $quickhelp;

}

/**
 * If a string exceeds the maximum display length ($max_length), shorten it to $max_length and add '...'
 * A tooltip with the complete string is added
 * If a maximum display length for mobiles ($max_length_mobile) is given this shortened string will be only shown on mobile phones
 */
function bootstrapklicktipp_klicktipp_compact_string($variables) {

  $string = check_plain($variables['string']);
  $max_length = $variables['max_length'];
  $max_length_mobile = (!isset($variables['max_length_mobile'])) ? 0 : $variables['max_length_mobile'];

  if (empty($max_length))
    return $string;

  $mobile = "";
  if (strlen($string) > $max_length_mobile) {
    //show this extra short string only on mobile phones
    $mobile = '<span class="has-tooltip visible-xxs hidden-xs hidden-sm hidden-md hidden-lg" data-title="' . $string . '">' . substr($string, 0, $max_length_mobile) . '...</span>';
  }

  $desktop = (empty($mobile)) ? '' : 'hidden-xxs'; //hide the complete or shortened string on mobile phones if $max_length_mobile is set
  if (strlen($string) > $max_length) {
    $shortened = '<span class="has-tooltip ' . $desktop . '" data-title="' . $string . '">' . substr($string, 0, $max_length) . '...</span>';
  } else {
    $shortened = '<span class="' . $desktop . '">' . $string . '</span>';
  }

  return $shortened . $mobile;

}

/**
 * Theme a modal trigger button
 */
function bootstrapklicktipp_klicktipp_modal_button($variables) {

  $element = $variables['element'];
  $image = (!isset($variables['image'])) ? 'btn-icon-edit-white btn-blue' : $variables['image'];

  //store the modal dialog id for JavaScripts
  $element['#attributes']['data-modal-id'] = $element['#value'];

  //the a klicktipp button with $modal = TRUE
  $output = theme('klicktipp_button', array('element' => $element, 'image' => $image, 'modal' => TRUE));

  return $output;

}

function bootstrapklicktipp_klicktipp_modal_link($variables) {

  $element = $variables['element'];

  //store the modal dialog id for JavaScripts
  $element['#attributes']['data-modal-id'] = $element['#value'];

  //the a klicktipp button with $modal = TRUE
  $output = theme('klicktipp_link', array('element' => $element, 'modal' => TRUE));

  return $output;

}

/**
 * Theme function for a modal dialog cancel button
 * on click this button closes the modal dialog via JavaScript
 */
function bootstrapklicktipp_klicktipp_modal_cancel_button($variables) {

  $element = $variables['element'];

  $element['#attributes']['data-dismiss'] = 'modal';
  $element['#value'] = '#';

  return theme('klicktipp_cancel_button', $element);

}

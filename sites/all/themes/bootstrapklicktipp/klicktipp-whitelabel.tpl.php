<!DOCTYPE html>
<html lang="<?php print $language->language; ?>">
<head>

  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="robots" content="noindex, nofollow">
  <meta name="google" content="notranslate">

  <!-- safari -->
  <link rel="mask-icon" type="image/svg+xml" sizes="any" href="<?php print APP_URL; ?>misc/img/favicon_safari.svg" />
  <!-- 180x180 -->
  <link rel="apple-touch-icon" href="<?php print APP_URL; ?>misc/img/apple-touch-icon-180x180.png" />
  <link rel="manifest" href="<?php print APP_URL; ?>misc/manifest.json" />

  <title><?php print $head_title; ?></title>
  <?php print $head; ?>
  <?php print $styles; ?>
  <?php print $scripts; ?>

  <script type="text/javascript">
    <?php /* Needed to avoid Flash of Unstyle Content in IE */ ?>

  </script>

</head>

<body class="<?php print $body_class; ?>">

  <?php print $GoogleScripts; ?>

  <nav id="top-menu" class="navbar navbar-default navbar-fixed-top <?php print $navbar_style; ?>" role="navigation">
    <div class="container">
      <div class="navbar-logo-toggle">
        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-responsive-collapse">
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        <?php print $brand; ?>
      </div>
      <div class="collapse navbar-collapse navbar-responsive-collapse">
        <?php print $MainMenu; ?>
      </div>
    </div>
  </nav>

  <div id="container" class="container">

    <div class="row">
      <div id="content" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 whitelabel">

        <?php print $messages; ?>

        <?php if (!empty($title)): ?>
        <h1><?php print $title; ?></h1>
        <?php endif; ?>

        <?php print $content; ?>

      </div>
    </div>

  </div>

  <nav id="footer-menu" class="navbar navbar-default" role="navigation">
    <div class="container">
      <div id="whitelabel-footer">
        <?php print $FooterMenu; ?>
      </div>
    </div>
  </nav>

</body>

</html>

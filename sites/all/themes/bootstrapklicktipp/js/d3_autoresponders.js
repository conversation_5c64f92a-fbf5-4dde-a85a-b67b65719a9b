var D3_Collabsible_Force = function ($) {

    var D3_Collabsible_Force = function (selector, w, h, mode) {

        if (mode == 'fullscreen') {
            this.Resize();
            $(window).bind('resize', this.Resize.bind(this));
        }
        else {
            this.w = w;
            this.h = h;
        }

        this.default_node_size = 15;
        this.default_tag_size = 5;
        this.default_link_distance = 50;
        this.default_charge = -250;
        this.CurrentZoom = 1;
        this.CurrentTranslation = [0, 0];
        this.LastTranslation = [0, 0];

        d3.select(selector).selectAll("svg").remove();

        this.Zoom = d3.behavior.zoom({
            click: true,
            dblclick: false,
            touch: true,
            wheel: false
        })
            .scaleExtent([0.5, 3])
            .on("zoom", this.MouseControl.bind(this));

        this.SVG = d3.select(selector).append("svg:svg")
            .attr('width', this.w)
            .attr('height', this.h)
            .call(this.Zoom)
            .on("dblclick.zoom", null/*this.ShowAll.bind(this)*/)
        ;

        this.svg = this.SVG.append("svg:g")
            .style("stroke", "#999")
            .style("fill", "#fff")
            .attr('width', this.w)
            .attr('height', this.h);

        this.force = d3.layout.force()
            .on("tick", this.tick.bind(this))
            .charge(-250)
            .linkDistance(this.default_link_distance)
            .size([this.w, this.h]); //h, w

        this.node = 0;
        this.link = 0;

        this.getLinkArrow = function (link) {

            if (!link)
                return 'none'; //no arrow

            if (link['Target'] == 0)
                return this['link_arrows']['child_link'][link['TagOP']]; //child link

            return this['link_arrows']['ar_link'][link['TagOP']]; //AR link

        }

        this.getLinkCross = function (link, pos) {

            if (!link || link['TagOP'] < 3)
                return 'none';

            return 'url(#not-tagged-' + pos + ')'; //link not tagged with ( pos 'start' || 'end')

        }

        //define which markers (arrows) to use for OpTaggeWith, OpNotTaggedWith
        this.link_arrows = {
            'child_link': { //from AR to Tag
                '1': 'url(#tag-arrow-and)', //has tag OR (Campaigns::TAG_HAS_ALL)
                '2': 'url(#tag-arrow-or)', //has tag OR (Campaigns::TAG_HAS_ANY)
                '3': 'url(#tag-arrow-or)', //has not tag OR (Campaigns::TAG_HAS_NOT_ALL)
                '4': 'url(#tag-arrow-and)', //has not tag AND (Campaigns::TAG_HAS_NOT_ANY)
            },
            'ar_link': { //from Tag to AR
                1: 'url(#arrow-and)', //has tag OR (Campaigns::TAG_HAS_ALL)
                2: 'url(#arrow-or)', //has tag OR (Campaigns::TAG_HAS_ANY)
                3: 'url(#arrow-or)', //has not tag OR (Campaigns::TAG_HAS_NOT_ALL)
                4: 'url(#arrow-and)', //has not tag AND (Campaigns::TAG_HAS_NOT_ANY)
            }
        };

        this.PopOver = $("#d3-popover");
        this.PopOverHeader = $("#d3-popover .popover-title");
        this.PopOverContent = $("#d3-popover .popover-content");

    }

    D3_Collabsible_Force.prototype.update = function (json) {

        this.svg.selectAll("*").remove();

        if (json)
            this.json = json;

        this.json.fixed = true;
        this.json.x = this.w / 2;
        this.json.y = this.h / 2;

        var nodes = this.flatten(this.json);
        var links = d3.layout.tree().links(nodes);

        //add links between nodes
        var NodesByID = {};
        for (var n in nodes) {
            var nid = nodes[n]['nodeid'];
            NodesByID[nid] = nodes[n];
        }

        //remove root links
        var test = links;
        links = [];
        for (var lnk in test) {
            if (test[lnk]['source']['nodeid'] == 'root') {
                continue;
            }

            //arrow marker for child link
            if (test[lnk]['target']['links'][0]['Target'] == 0 && !test[lnk]['TagArrow']) {
                test[lnk]['TagArrow'] = this.getLinkArrow(test[lnk]['target']['links'][0]);
                test[lnk]['hasArrow'] = this.getLinkCross(test[lnk]['target']['links'][0], 'start');
                test[lnk]['css'] = (!test[lnk]['source']['css']) ? [] : test[lnk]['source']['css'];
                var arrow = (test[lnk]['target']['links'][0]['TagOP'] == 1 || test[lnk]['target']['links'][0]['TagOP'] == 4 ) ? 'marker-arrow-and' : 'marker-arrow-or';
                test[lnk]['css'].push(arrow);
            }

            links.push(test[lnk]);

        }

        for (var n in nodes) {
            //add link
            var node_links = nodes[n]['links'];

            if (node_links) {
                for (var nl in node_links) {

                    if (node_links[nl]['Target'] > 0) {

                        var css = nodes[n]['css'];
                        var c = (node_links[nl]['TagOP'] == 1 || node_links[nl]['TagOP'] == 4 ) ? 'marker-arrow-and' : 'marker-arrow-or';
                        css.push(c);

                        var l = {
                            'css': css,
                            'source': nodes[n],
                            'target': NodesByID[node_links[nl]['Target']],
                            'hasArrow': this.getLinkArrow(node_links[nl]),
                            'TagArrow': this.getLinkCross(node_links[nl], 'end')
                        }
                        links.push(l);
                    }
                }
            }
        }

        // Restart the force layout.
        this.force
            .nodes(nodes)
            .links(links)
            .start();

        // Update the nodes…
        this.node = this.svg.selectAll("circle.node")
            .data(nodes, function (d) {
                return d.id;
            })

        // Enter any new nodes.
        var ref = this;
        this.node.enter().append("svg:circle")
            .attr("class", function (d) {
                if (!d.css)
                    d.css = [];
                return 'node ' + d.css.join(' ');
            })
            .attr("cx", function (d) {
                return d.x;
            })
            .attr("cy", function (d) {
                return d.y;
            })
            .attr("r", function (d) {
                return d.children ? ref.default_node_size : ref.default_tag_size;
            })
            .on("click", function (d) {
                ref.GotoAutoresponder(d);
            })
            .on("dblclick", function (d) {
                ref.ZoomToNode(d);
            })
            .on("mouseover", this.mouseover.bind(this))
            .on("mouseout", this.mouseout.bind(this))
            .call(this.force.drag);

        // Exit any old nodes.
        this.node.exit().remove();

        this.svg.append("svg:marker")
            .attr('id', 'arrow-and')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_node_size + 12)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-arrow-and')
            .append("svg:path").attr('d', 'M 0 0 L 10 5 L 0 10 z');

        this.svg.append("svg:marker")
            .attr('id', 'arrow-or')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_node_size + 12)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-arrow-or')
            .append("svg:path").attr('d', 'M 0 0 L 10 5 L 0 10 z');

        this.svg.append("svg:marker")
            .attr('id', 'arrow-and-dimmed')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_node_size + 12)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-arrow-dimmed')
            .append("svg:path").attr('d', 'M 0 0 L 10 5 L 0 10 z');

        this.svg.append("svg:marker")
            .attr('id', 'arrow-or-dimmed')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_node_size + 12)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-arrow-dimmed')
            .append("svg:path").attr('d', 'M 0 0 L 10 5 L 0 10 z');

        this.svg.append("svg:marker")
            .attr('id', 'tag-arrow-and')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_node_size - 32)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-arrow-and')
            .append("svg:path").attr('d', 'M 10 0 L 0 5 L 10 10 z');

        this.svg.append("svg:marker")
            .attr('id', 'tag-arrow-or')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_node_size - 32)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-arrow-or')
            .append("svg:path").attr('d', 'M 10 0 L 0 5 L 10 10 z');

        this.svg.append("svg:marker")
            .attr('id', 'tag-arrow-and-dimmed')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_node_size - 32)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-arrow-dimmed')
            .append("svg:path").attr('d', 'M 10 0 L 0 5 L 10 10 z');

        this.svg.append("svg:marker")
            .attr('id', 'tag-arrow-or-dimmed')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_node_size - 32)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-arrow-dimmed')
            .append("svg:path").attr('d', 'M 10 0 L 0 5 L 10 10 z');

        this.svg.append("svg:marker")
            .attr('id', 'not-tagged-start')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_link_distance / 2)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-cross')
            .append("svg:path").attr('d', 'M 0 0 L 10 10 M 0 10 L 10 0');

        this.svg.append("svg:marker")
            .attr('id', 'not-tagged-end')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', -this.default_link_distance / 2)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-cross')
            .append("svg:path").attr('d', 'M 0 0 L 10 10 M 0 10 L 10 0');

        this.svg.append("svg:marker")
            .attr('id', 'not-tagged-start-dimmed')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', this.default_link_distance / 2)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-cross-dimmed')
            .append("svg:path").attr('d', 'M 0 0 L 10 10 M 0 10 L 10 0');

        this.svg.append("svg:marker")
            .attr('id', 'not-tagged-end-dimmed')
            .attr('viewBox', '0 0 10 10')
            .attr('refX', -this.default_link_distance / 2)
            .attr('refY', 5)
            .attr('markerUnits', 'strokeWidth')
            .attr('markerWidth', 8)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .attr('class', 'marker-cross-dimmed')
            .append("svg:path").attr('d', 'M 0 0 L 10 10 M 0 10 L 10 0');

        //Update the links…
        this.link = this.svg.selectAll("line.link")
            .data(links, function (d) {
                return d.target.id;
            });

        // Enter any new links.
        this.link.enter().insert("svg:line", ".node")
            .attr("class", function (d) {
                if (!d.css)
                    d.css = (d.source.css) ? d.source.css : [];
                if ($.inArray('link', d.css) == -1)
                    d.css.unshift('link');
                return d.css.join(' ');
            })
            .attr("x1", function (d) {
                return d.source.x;
            })
            .attr("y1", function (d) {
                return d.source.y;
            })
            .attr("x2", function (d) {
                return d.target.x;
            })
            .attr("y2", function (d) {
                return d.target.y;
            })
            .attr("marker-end", function (d) {
                return d.hasArrow;
            })
            .attr("marker-start", function (d) {
                return d.TagArrow;
            });

        // Exit any old links.
        this.link.exit().remove();

        return this;

    }

    D3_Collabsible_Force.prototype.tick = function () {

        this.link.attr("x1", function (d) {
            return d.source.x;
        })
            .attr("y1", function (d) {
                return d.source.y;
            })
            .attr("x2", function (d) {
                return d.target.x;
            })
            .attr("y2", function (d) {
                return d.target.y;
            });

        this.node.attr("cx", function (d) {
            return d.x;
        });
        this.node.attr("cy", function (d) {
            return d.y;
        });

    }

    /*
     // Color leaf nodes orange, and packages white or blue.
     D3_Collabsible_Force.prototype.color = function (d) {
     return d._children ? "#3182bd" : d.children ? d.color : "#00FFFF";
     }


     // Toggle children on click.
     D3_Collabsible_Force.prototype.click = function (d) {

     }
     */

    D3_Collabsible_Force.prototype.mouseover = function (d) {

        if (d.chains && d.chains.length > 0) {
            var c = d.chains.join("):not(.ar-chain-");
            this.svg.selectAll("*:not(.ar-chain-" + c + ")").classed('ar-chain-dimmed', true);

            var dimm = {
                'url(#arrow-and)': 'url(#arrow-and-dimmed)',
                'url(#arrow-or)': 'url(#arrow-or-dimmed)',
                'url(#tag-arrow-and)': 'url(#tag-arrow-and-dimmed)',
                'url(#tag-arrow-or)': 'url(#tag-arrow-or-dimmed)',
                'url(#not-tagged-start)': 'url(#not-tagged-start-dimmed)',
                'url(#not-tagged-end)': 'url(#not-tagged-end-dimmed)'
            }

            for (var n in dimm) {
                this.svg.selectAll("*[marker-end='" + n + "']:not(.ar-chain-" + c + ")").attr('marker-end', dimm[n]);
                this.svg.selectAll("*[marker-start='" + n + "']:not(.ar-chain-" + c + ")").attr('marker-start', dimm[n]);
            }

        }

        this.PopOverHeader.text(d.name);
        this.PopOverContent.text(d.info);

        var pos = {};
        if (d3.event.layerX > (this.w / 2) && !$('#key-popover').is(":visible")) {
            pos['left'] = 0;
            pos['right'] = 'auto';
        }
        else {
            pos['left'] = 'auto';
            pos['right'] = 0;
        }

        if (d3.event.layerY > (this.h / 2)) {
            pos['bottom'] = 'auto';
            pos['top'] = 0;
        }
        else {
            pos['top'] = 'auto';
            pos['bottom'] = 0;
        }

        this.PopOver.css(pos).show();

    };

    D3_Collabsible_Force.prototype.mouseout = function (d) {

        this.PopOver.hide();

        this.svg.selectAll("*.ar-chain-dimmed").classed('ar-chain-dimmed', false);

        var dimm = {
            'url(#arrow-and-dimmed)': 'url(#arrow-and)',
            'url(#arrow-or-dimmed)': 'url(#arrow-or)',
            'url(#tag-arrow-and-dimmed)': 'url(#tag-arrow-and)',
            'url(#tag-arrow-or-dimmed)': 'url(#tag-arrow-or)',
            'url(#not-tagged-start-dimmed)': 'url(#not-tagged-start)',
            'url(#not-tagged-end-dimmed)': 'url(#not-tagged-end)'
        }

        for (var n in dimm) {
            this.svg.selectAll("*[marker-end='" + n + "']").attr('marker-end', dimm[n]);
            this.svg.selectAll("*[marker-start='" + n + "']").attr('marker-start', dimm[n]);
        }

    };

    // Returns a list of all nodes under the root.
    D3_Collabsible_Force.prototype.flatten = function (root) {
        var nodes = [], i = 0;

        function recurse(node) {
            if (node.children) node.size = node.children.reduce(function (p, v) {
                return p + recurse(v);
            }, 0);
            if (!node.id) node.id = ++i;
            nodes.push(node);
            return node.size;
        }

        root.size = recurse(root);
        return nodes;
    }

    D3_Collabsible_Force.prototype.MouseControl = function () {

        if (d3.event.sourceEvent.type == "DOMMouseScroll" || d3.event.sourceEvent.type == "mousewheel") {
            //deactivate MouseScroll
            this.Zoom.translate(this.CurrentTranslation);

        }
        else {

            var trans = d3.event.translate;

            this.CurrentTranslation = trans;
            this.Transform(this.CurrentTranslation, this.CurrentZoom);

        }

    }

    D3_Collabsible_Force.prototype.Transform = function (trans, zoom) {

        this.svg.attr("transform", "scale(" + zoom + ") translate(" + trans + ")");

        $("#edit-ZoomSlider").slider('setValue', Math.round(zoom * 100));

    }

    D3_Collabsible_Force.prototype.ZoomBySlider = function (zoom) {

        var bbox = this.svg[0][0].getBBox();
        //var bcbox = this.svg[0][0].getBoundingClientRect();

        var trans = this.CurrentTranslation;

        var scaledCenterX = (this.w / zoom) / 2;
        var scaledCenterY = (this.h / zoom) / 2;

        var scaledTransX = (trans[0] / this.CurrentZoom) * zoom;
        var scaledTransY = (trans[1] / this.CurrentZoom) * zoom;

        var x = -(bbox.x + (bbox.width / 2) - scaledCenterX);
        var y = -(bbox.y + (bbox.height / 2) - scaledCenterY);

        this.CurrentZoom = zoom;
        this.CurrentTranslation = [x, y];

        this.Transform(this.CurrentTranslation, this.CurrentZoom);

        this.Zoom.translate(this.CurrentTranslation).scale(this.CurrentZoom);

    }

    D3_Collabsible_Force.prototype.ZoomIn = function () {

        this.CurrentZoom += 0.1;
        this.ZoomBySlider(this.CurrentZoom);
    }

    D3_Collabsible_Force.prototype.ZoomOut = function () {

        this.CurrentZoom -= 0.1;
        this.ZoomBySlider(this.CurrentZoom);

    }

    D3_Collabsible_Force.prototype.ShowAll = function () {

        var bbox = this.svg[0][0].getBBox();

        var zoom_w = (this.w - 40) / bbox.width;
        var zoom_h = (this.h - 40) / bbox.height;
        var zoom = Math.min(3, Math.min(zoom_w, zoom_h));
        zoom = Math.max(0.5, zoom);

        var scaledCenterX = (this.w / zoom) / 2;
        var scaledCenterY = (this.h / zoom) / 2;

        var x = -(bbox.x + (bbox.width / 2) - scaledCenterX);
        var y = -(bbox.y + (bbox.height / 2) - scaledCenterY);

        this.CurrentZoom = zoom;
        this.CurrentTranslation = [x, y];
        this.Transform(this.CurrentTranslation, this.CurrentZoom);

        this.Zoom.translate(this.CurrentTranslation).scale(this.CurrentZoom);

    }

    D3_Collabsible_Force.prototype.ZoomToNode = function (node) {

        var zoom = 1;

        var scaledCenterX = (this.w / zoom) / 2;
        var scaledCenterY = (this.h / zoom) / 2;

        var x = -(node.x + (node.size / 2) - scaledCenterX);
        var y = -(node.y + (node.size / 2) - scaledCenterY);

        this.CurrentZoom = zoom;
        this.CurrentTranslation = [x, y];
        this.Transform(this.CurrentTranslation, this.CurrentZoom);
        this.Zoom.translate(this.CurrentTranslation).scale(this.CurrentZoom);

        d3.event.stopPropagation();

    }

    D3_Collabsible_Force.prototype.Resize = function (node) {

        this.w = $(window).width() - 20;
        this.h = $(window).height() - 40 - $('#controls').outerHeight();

        this.w = Math.max(800, this.w);
        this.h = Math.max(400, this.h);

        if (this.SVG) {
            this.SVG.attr('width', this.w).attr('height', this.h);
        }

        if (this.svg) {
            this.svg.attr('width', this.w).attr('height', this.h);
        }

        if (this.force) {
            this.force.size([this.w, this.h]);
        }

    }

    D3_Collabsible_Force.prototype.GotoAutoresponder = function (node) {

        var id = parseInt(node.nodeid);

        if (id > 0) {

            var edit = ($.inArray('ar-status-active', node.css) == -1 ) ? '/edit' : '';
            window.open("/emails/me/" + id + edit);

        }

        d3.event.stopPropagation();
        d3.event.preventDefault();
    }

    D3_Collabsible_Force.prototype.ShowKey = function () {

        $('#key-popover').toggle();
    }

    return D3_Collabsible_Force;

}(window.jQuery);


/* D3 Bullet Charts */

/*D3AutoresponderBullets = new D3_Bullet_Chart("#Autoresponder-Bullets");

 var bullets = [
 {"title":"Revenue","subtitle":"US$, in thousands","ranges":[150,225,300],"measures":[220,270],"markers":[250]},
 {"title":"Profit","subtitle":"%","ranges":[20,25,30],"measures":[21,23],"markers":[26]},
 {"title":"Order Size","subtitle":"US$, average","ranges":[350,500,600],"measures":[100,320],"markers":[550]},
 {"title":"New Customers","subtitle":"count","ranges":[1400,2000,2500],"measures":[1000,1650],"markers":[2100]},
 {"title":"Satisfaction","subtitle":"out of 5","ranges":[3.5,4.25,5],"measures":[3.2,4.7],"markers":[4.4]}
 ];

 D3AutoresponderBullets.update(' . json_encode($Bullets) . ');


 $Bullets[] = array(
 'title' => $autoresponder['CampaignName'],
 'subtitle' => empty($autoresponder['SimulationTime']) ? '---' : \App\Klicktipp\Dates::formatDate(\App\Klicktipp\Dates::FORMAT_DMYHIS, (int) $autoresponder['SimulationTime']),
 'ranges' => array((int)$autoresponder['TotalUnsubscriptions'], (int)$autoresponder['TotalSent']),
 'measures' => array((int)$autoresponder['UniqueClicks'] , (int)$autoresponder['UniqueOpens'] ),
 'markers' => array(0),
 );

 */

var D3_Init_Bullet = function () {

//Chart design based on the recommendations of Stephen Few. Implementation
//based on the work of Clint Ivy, Jamie Love, and Jason Davies.
//http://projects.instantcognition.com/protovis/bulletchart/
    d3.bullet = function () {
        var orient = "left", // TODO top & bottom
            reverse = false,
            duration = 0,
            ranges = bulletRanges,
            markers = bulletMarkers,
            measures = bulletMeasures,
            width = 380,
            height = 30,
            tickFormat = null;

        // For each small multiple…
        function bullet(g) {
            g.each(function (d, i) {
                var rangez = ranges.call(this, d, i).slice().sort(d3.descending),
                    markerz = markers.call(this, d, i).slice().sort(d3.descending),
                    measurez = measures.call(this, d, i).slice().sort(d3.descending),
                    g = d3.select(this);

                // Compute the new x-scale.
                var x1 = d3.scale.linear()
                    .domain([0, Math.max(rangez[0], markerz[0], measurez[0])])
                    .range(reverse ? [width, 0] : [0, width]);

                // Retrieve the old x-scale, if this is an update.
                var x0 = this.__chart__ || d3.scale.linear()
                    .domain([0, Infinity])
                    .range(x1.range());

                // Stash the new scale.
                this.__chart__ = x1;

                // Derive width-scales from the x-scales.
                var w0 = bulletWidth(x0),
                    w1 = bulletWidth(x1);

                // Update the range rects.
                var range = g.selectAll("rect.range")
                    .data(rangez);

                range.enter().append("rect")
                    .attr("class", function (d, i) {
                        return "range s" + i;
                    })
                    .attr("width", w0)
                    .attr("height", height)
                    .attr("x", reverse ? x0 : 0)
                    .transition()
                    .duration(duration)
                    .attr("width", w1)
                    .attr("x", reverse ? x1 : 0);

                range.transition()
                    .duration(duration)
                    .attr("x", reverse ? x1 : 0)
                    .attr("width", w1)
                    .attr("height", height);

                // Update the measure rects.
                var measure = g.selectAll("rect.measure")
                    .data(measurez);

                measure.enter().append("rect")
                    .attr("class", function (d, i) {
                        return "measure s" + i;
                    })
                    .attr("width", w0)
                    .attr("height", height / 3)
                    .attr("x", reverse ? x0 : 0)
                    .attr("y", height / 3)
                    .transition()
                    .duration(duration)
                    .attr("width", w1)
                    .attr("x", reverse ? x1 : 0);

                measure.transition()
                    .duration(duration)
                    .attr("width", w1)
                    .attr("height", height / 3)
                    .attr("x", reverse ? x1 : 0)
                    .attr("y", height / 3);

                // Update the marker lines.
                var marker = g.selectAll("line.marker")
                    .data(markerz);

                marker.enter().append("line")
                    .attr("class", "marker")
                    .attr("x1", x0)
                    .attr("x2", x0)
                    .attr("y1", height / 6)
                    .attr("y2", height * 5 / 6)
                    .transition()
                    .duration(duration)
                    .attr("x1", x1)
                    .attr("x2", x1);

                marker.transition()
                    .duration(duration)
                    .attr("x1", x1)
                    .attr("x2", x1)
                    .attr("y1", height / 6)
                    .attr("y2", height * 5 / 6);

                // Compute the tick format.
                var format = tickFormat || x1.tickFormat(8);

                // Update the tick groups.
                var tick = g.selectAll("g.tick")
                    .data(x1.ticks(8), function (d) {
                        return this.textContent || format(d);
                    });

                // Initialize the ticks with the old scale, x0.
                var tickEnter = tick.enter().append("g")
                    .attr("class", "tick")
                    .attr("transform", bulletTranslate(x0))
                    .style("opacity", 1e-6);

                tickEnter.append("line")
                    .attr("y1", height)
                    .attr("y2", height * 7 / 6);

                tickEnter.append("text")
                    .attr("text-anchor", "middle")
                    .attr("dy", "1em")
                    .attr("y", height * 7 / 6)
                    .text(format);

                // Transition the entering ticks to the new scale, x1.
                tickEnter.transition()
                    .duration(duration)
                    .attr("transform", bulletTranslate(x1))
                    .style("opacity", 1);

                // Transition the updating ticks to the new scale, x1.
                var tickUpdate = tick.transition()
                    .duration(duration)
                    .attr("transform", bulletTranslate(x1))
                    .style("opacity", 1);

                tickUpdate.select("line")
                    .attr("y1", height)
                    .attr("y2", height * 7 / 6);

                tickUpdate.select("text")
                    .attr("y", height * 7 / 6);

                // Transition the exiting ticks to the new scale, x1.
                tick.exit().transition()
                    .duration(duration)
                    .attr("transform", bulletTranslate(x1))
                    .style("opacity", 1e-6)
                    .remove();
            });
            d3.timer.flush();
        }

        // left, right, top, bottom
        bullet.orient = function (x) {
            if (!arguments.length) return orient;
            orient = x;
            reverse = orient == "right" || orient == "bottom";
            return bullet;
        };

        // ranges (bad, satisfactory, good)
        bullet.ranges = function (x) {
            if (!arguments.length) return ranges;
            ranges = x;
            return bullet;
        };

        // markers (previous, goal)
        bullet.markers = function (x) {
            if (!arguments.length) return markers;
            markers = x;
            return bullet;
        };

        // measures (actual, forecast)
        bullet.measures = function (x) {
            if (!arguments.length) return measures;
            measures = x;
            return bullet;
        };

        bullet.width = function (x) {
            if (!arguments.length) return width;
            width = x;
            return bullet;
        };

        bullet.height = function (x) {
            if (!arguments.length) return height;
            height = x;
            return bullet;
        };

        bullet.tickFormat = function (x) {
            if (!arguments.length) return tickFormat;
            tickFormat = x;
            return bullet;
        };

        bullet.duration = function (x) {
            if (!arguments.length) return duration;
            duration = x;
            return bullet;
        };

        return bullet;
    };

    function bulletRanges(d) {
        return d.ranges;
    }

    function bulletMarkers(d) {
        return d.markers;
    }

    function bulletMeasures(d) {
        return d.measures;
    }

    function bulletTranslate(x) {
        return function (d) {
            return "translate(" + x(d) + ",0)";
        };
    }

    function bulletWidth(x) {
        var x0 = x(0);
        return function (d) {
            return Math.abs(x(d) - x0);
        };
    }

};

var D3_Bullet_Chart = function ($) {

    var D3_Bullet_Chart = function (selector) {

        D3_Init_Bullet();

        var w = $(selector).width();
        var h = 50;

        this.margin = {top: 5, right: 20, bottom: 20, left: 120},
            this.width = w - this.margin.left - this.margin.right,
            this.height = h - this.margin.top - this.margin.bottom;

        this.chart = d3.bullet()
            .width(this.width)
            .height(this.height);

        this.SVG = d3.select(selector).selectAll("svg");


    }

    D3_Bullet_Chart.prototype.update = function (json) {

        var svg = this.SVG.data(json)
            .enter().append("svg")
            .attr("class", "bullet")
            .attr("width", this.width + this.margin.left + this.margin.right)
            .attr("height", this.height + this.margin.top + this.margin.bottom)
            .append("g")
            .attr("transform", "translate(" + this.margin.left + "," + this.margin.top + ")")
            .call(this.chart);

        var title = svg.append("g")
            .style("text-anchor", "end")
            .attr("transform", "translate(-6," + this.height / 2 + ")");

        title.append("text")
            .attr("class", "title")
            .text(function (d) {
                return d.title;
            });

        title.append("text")
            .attr("class", "subtitle")
            .attr("dy", "1em")
            .text(function (d) {
                return d.subtitle;
            });


    }

    return D3_Bullet_Chart;

}(window.jQuery);
<?php

namespace nodespark\DESConnector\Elasticsearch\Aggregations\Bucket;

use nodespark\DESConnector\Elasticsearch\Aggregations\AggregationInterface;

/**
 * Class Filter
 *
 * @package nodespark\DESConnector\Elasticsearch\Aggregations\Bucket
 */
class Filter implements AggregationInterface
{
    const TYPE = 'filter';

    // TODO: To be implemented as special case!

  public function getName() {
    // TODO: Implement getName() method.
  }
}

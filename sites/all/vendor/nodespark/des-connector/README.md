# DESConnector

[![Build Status](https://travis-ci.org/nodespark/des-connector.svg?branch=5.x)](https://travis-ci.org/nodespark/des-connector)

Drupal Elasticsearch Connector for <PERSON><PERSON><PERSON> provides and abstraction of the Elasticsearch-PHP library (https://github.com/elastic/elasticsearch-php).
This library will be used in combination with Elasticsearch Connector module for Dr<PERSON>al (https://www.drupal.org/project/elasticsearch_connector)
and will allow for better and faster D7 and D8 development.


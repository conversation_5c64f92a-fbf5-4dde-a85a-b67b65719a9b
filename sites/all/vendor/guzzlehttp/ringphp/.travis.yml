language: php

cache:
    directories:
        - $HOME/.composer/cache/files

php:
  - 5.4
  - 5.5
  - 5.6
  - 7.0
  - 7.1
  - 7.2
  - hhvm
  - nightly

env:
  global:
    - TEST_COMMAND="composer test"

matrix:
  allow_failures:
    - php: hhvm
    - php: nightly
  fast_finish: true
  include:
    - php: 5.4
      env: COMPOSER_FLAGS="--prefer-stable --prefer-lowest"

before_install:
  - if [[ $COVERAGE != true ]]; then phpenv config-rm xdebug.ini || true; fi

install:
  # To be removed when this issue will be resolved: https://github.com/composer/composer/issues/5355
  - if [[ "$COMPOSER_FLAGS" == *"--prefer-lowest"* ]]; then travis_retry composer update --prefer-dist --no-interaction --prefer-stable --quiet; fi
  - travis_retry composer update ${COMPOSER_FLAGS} --prefer-dist --no-interaction

before_script:
  - ~/.nvm/nvm.sh install v0.6.14
  - ~/.nvm/nvm.sh run v0.6.14

script:
  - $TEST_COMMAND

{"name": "react/promise", "description": "A lightweight implementation of CommonJS Promises/A for PHP", "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://sorgalla.com/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://clue.engineering/", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://wyrihaximus.net/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://cboden.dev/", "email": "<EMAIL>"}], "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "autoload": {"psr-4": {"React\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "autoload-dev": {"psr-4": {"React\\Promise\\": ["tests/", "tests/fixtures/"]}}, "keywords": ["promise", "promises"]}
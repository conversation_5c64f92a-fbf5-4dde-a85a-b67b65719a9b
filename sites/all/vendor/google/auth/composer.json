{"name": "google/auth", "type": "library", "description": "Google Auth Library for PHP", "keywords": ["google", "oauth2", "authentication"], "homepage": "http://github.com/google/google-auth-library-php", "license": "Apache-2.0", "support": {"docs": "https://googleapis.github.io/google-auth-library-php/main/"}, "require": {"php": "^7.1||^8.0", "firebase/php-jwt": "^5.5||^6.0", "guzzlehttp/guzzle": "^6.2.1|^7.0", "guzzlehttp/psr7": "^1.7|^2.0", "psr/http-message": "^1.0", "psr/cache": "^1.0|^2.0|^3.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "squizlabs/php_codesniffer": "^3.5", "phpunit/phpunit": "^7.5||^9.0.0", "phpspec/prophecy-phpunit": "^1.1||^2.0", "sebastian/comparator": ">=1.2.3", "phpseclib/phpseclib": "^2.0.31||^3.0", "kelvinmo/simplejwt": "0.7.0"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "autoload-dev": {"psr-4": {"Google\\Auth\\Tests\\": "tests"}}}
<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/backend.proto

namespace GPBMetadata\Google\Api;

class Backend
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ab4040a18676f6f676c652f6170692f6261636b656e642e70726f746f12" .
            "0a676f6f676c652e61706922310a074261636b656e6412260a0572756c65" .
            "7318012003280b32172e676f6f676c652e6170692e4261636b656e645275" .
            "6c6522e0020a0b4261636b656e6452756c6512100a0873656c6563746f72" .
            "180120012809120f0a076164647265737318022001280912100a08646561" .
            "646c696e6518032001280112140a0c6d696e5f646561646c696e65180420" .
            "012801121a0a126f7065726174696f6e5f646561646c696e651805200128" .
            "0112410a10706174685f7472616e736c6174696f6e18062001280e32272e" .
            "676f6f676c652e6170692e4261636b656e6452756c652e50617468547261" .
            "6e736c6174696f6e12160a0c6a77745f61756469656e6365180720012809" .
            "480012160a0c64697361626c655f61757468180820012808480022650a0f" .
            "506174685472616e736c6174696f6e12200a1c504154485f5452414e534c" .
            "4154494f4e5f554e535045434946494544100012140a10434f4e5354414e" .
            "545f414444524553531001121a0a16415050454e445f504154485f544f5f" .
            "41444452455353100242100a0e61757468656e7469636174696f6e426e0a" .
            "0e636f6d2e676f6f676c652e617069420c4261636b656e6450726f746f50" .
            "015a45676f6f676c652e676f6c616e672e6f72672f67656e70726f746f2f" .
            "676f6f676c65617069732f6170692f73657276696365636f6e6669673b73" .
            "657276696365636f6e666967a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/documentation.proto

namespace GPBMetadata\Google\Api;

class Documentation
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0af6030a1e676f6f676c652f6170692f646f63756d656e746174696f6e2e" .
            "70726f746f120a676f6f676c652e61706922a1010a0d446f63756d656e74" .
            "6174696f6e120f0a0773756d6d617279180120012809121f0a0570616765" .
            "7318052003280b32102e676f6f676c652e6170692e50616765122c0a0572" .
            "756c657318032003280b321d2e676f6f676c652e6170692e446f63756d65" .
            "6e746174696f6e52756c65121e0a16646f63756d656e746174696f6e5f72" .
            "6f6f745f75726c18042001280912100a086f766572766965771802200128" .
            "09225b0a11446f63756d656e746174696f6e52756c6512100a0873656c65" .
            "63746f7218012001280912130a0b6465736372697074696f6e1802200128" .
            "09121f0a176465707265636174696f6e5f6465736372697074696f6e1803" .
            "2001280922490a0450616765120c0a046e616d65180120012809120f0a07" .
            "636f6e74656e7418022001280912220a0873756270616765731803200328" .
            "0b32102e676f6f676c652e6170692e5061676542740a0e636f6d2e676f6f" .
            "676c652e6170694212446f63756d656e746174696f6e50726f746f50015a" .
            "45676f6f676c652e676f6c616e672e6f72672f67656e70726f746f2f676f" .
            "6f676c65617069732f6170692f73657276696365636f6e6669673b736572" .
            "76696365636f6e666967a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


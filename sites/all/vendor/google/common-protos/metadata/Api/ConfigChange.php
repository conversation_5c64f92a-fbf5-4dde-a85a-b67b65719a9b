<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/config_change.proto

namespace GPBMetadata\Google\Api;

class ConfigChange
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ab1030a1e676f6f676c652f6170692f636f6e6669675f6368616e67652e" .
            "70726f746f120a676f6f676c652e6170692297010a0c436f6e6669674368" .
            "616e6765120f0a07656c656d656e7418012001280912110a096f6c645f76" .
            "616c756518022001280912110a096e65775f76616c756518032001280912" .
            "2b0a0b6368616e67655f7479706518042001280e32162e676f6f676c652e" .
            "6170692e4368616e67655479706512230a07616476696365731805200328" .
            "0b32122e676f6f676c652e6170692e416476696365221d0a064164766963" .
            "6512130a0b6465736372697074696f6e1802200128092a4f0a0a4368616e" .
            "676554797065121b0a174348414e47455f545950455f554e535045434946" .
            "494544100012090a0541444445441001120b0a0752454d4f564544100212" .
            "0c0a084d4f444946494544100342710a0e636f6d2e676f6f676c652e6170" .
            "694211436f6e6669674368616e676550726f746f50015a43676f6f676c65" .
            "2e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c65617069" .
            "732f6170692f636f6e6669676368616e67653b636f6e6669676368616e67" .
            "65a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


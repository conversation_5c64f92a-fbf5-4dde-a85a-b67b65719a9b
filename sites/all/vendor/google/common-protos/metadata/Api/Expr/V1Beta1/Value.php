<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/value.proto

namespace GPBMetadata\Google\Api\Expr\V1Beta1;

class Value
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        \GPBMetadata\Google\Protobuf\Struct::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0aa4070a23676f6f676c652f6170692f657870722f763162657461312f76" .
            "616c75652e70726f746f1217676f6f676c652e6170692e657870722e7631" .
            "62657461311a1c676f6f676c652f70726f746f6275662f7374727563742e" .
            "70726f746f22bd030a0556616c756512300a0a6e756c6c5f76616c756518" .
            "012001280e321a2e676f6f676c652e70726f746f6275662e4e756c6c5661" .
            "6c7565480012140a0a626f6f6c5f76616c7565180220012808480012150a" .
            "0b696e7436345f76616c7565180320012803480012160a0c75696e743634" .
            "5f76616c7565180420012804480012160a0c646f75626c655f76616c7565" .
            "180520012801480012160a0c737472696e675f76616c7565180620012809" .
            "480012150a0b62797465735f76616c756518072001280c480012380a0a65" .
            "6e756d5f76616c756518092001280b32222e676f6f676c652e6170692e65" .
            "7870722e763162657461312e456e756d56616c75654800122c0a0c6f626a" .
            "6563745f76616c7565180a2001280b32142e676f6f676c652e70726f746f" .
            "6275662e416e79480012360a096d61705f76616c7565180b2001280b3221" .
            "2e676f6f676c652e6170692e657870722e763162657461312e4d61705661" .
            "6c7565480012380a0a6c6973745f76616c7565180c2001280b32222e676f" .
            "6f676c652e6170692e657870722e763162657461312e4c69737456616c75" .
            "65480012140a0a747970655f76616c7565180f20012809480042060a046b" .
            "696e6422280a09456e756d56616c7565120c0a0474797065180120012809" .
            "120d0a0576616c7565180220012805223b0a094c69737456616c7565122e" .
            "0a0676616c75657318012003280b321e2e676f6f676c652e6170692e6578" .
            "70722e763162657461312e56616c756522a9010a084d617056616c756512" .
            "380a07656e747269657318012003280b32272e676f6f676c652e6170692e" .
            "657870722e763162657461312e4d617056616c75652e456e7472791a630a" .
            "05456e747279122b0a036b657918012001280b321e2e676f6f676c652e61" .
            "70692e657870722e763162657461312e56616c7565122d0a0576616c7565" .
            "18022001280b321e2e676f6f676c652e6170692e657870722e7631626574" .
            "61312e56616c7565426b0a1b636f6d2e676f6f676c652e6170692e657870" .
            "722e76316265746131420a56616c756550726f746f50015a3b676f6f676c" .
            "652e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c656170" .
            "69732f6170692f657870722f763162657461313b65787072f80101620670" .
            "726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


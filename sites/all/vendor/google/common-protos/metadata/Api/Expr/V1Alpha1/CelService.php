<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1alpha1/cel_service.proto

namespace GPBMetadata\Google\Api\Expr\V1Alpha1;

class CelService
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Client::initOnce();
        \GPBMetadata\Google\Api\Expr\V1Alpha1\ConformanceService::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0acb040a2a676f6f676c652f6170692f657870722f7631616c706861312f" .
            "63656c5f736572766963652e70726f746f1218676f6f676c652e6170692e" .
            "657870722e7631616c706861311a32676f6f676c652f6170692f65787072" .
            "2f7631616c706861312f636f6e666f726d616e63655f736572766963652e" .
            "70726f746f32d2020a0a43656c5365727669636512670a05506172736512" .
            "262e676f6f676c652e6170692e657870722e7631616c706861312e506172" .
            "7365526571756573741a272e676f6f676c652e6170692e657870722e7631" .
            "616c706861312e5061727365526573706f6e7365220dda410a63656c5f73" .
            "6f7572636512680a05436865636b12262e676f6f676c652e6170692e6578" .
            "70722e7631616c706861312e436865636b526571756573741a272e676f6f" .
            "676c652e6170692e657870722e7631616c706861312e436865636b526573" .
            "706f6e7365220eda410b7061727365645f65787072125a0a044576616c12" .
            "252e676f6f676c652e6170692e657870722e7631616c706861312e457661" .
            "6c526571756573741a262e676f6f676c652e6170692e657870722e763161" .
            "6c706861312e4576616c526573706f6e73652203da41001a15ca41126365" .
            "6c2e676f6f676c65617069732e636f6d42720a1c636f6d2e676f6f676c65" .
            "2e6170692e657870722e7631616c70686131420f43656c53657276696365" .
            "50726f746f50015a3c676f6f676c652e676f6c616e672e6f72672f67656e" .
            "70726f746f2f676f6f676c65617069732f6170692f657870722f7631616c" .
            "706861313b65787072f80101620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


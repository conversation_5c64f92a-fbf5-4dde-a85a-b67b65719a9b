<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1alpha1/explain.proto

namespace GPBMetadata\Google\Api\Expr\V1Alpha1;

class Explain
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Expr\V1Alpha1\Value::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ae9020a26676f6f676c652f6170692f657870722f7631616c706861312f" .
            "6578706c61696e2e70726f746f1218676f6f676c652e6170692e65787072" .
            "2e7631616c7068613122ab010a074578706c61696e122f0a0676616c7565" .
            "7318012003280b321f2e676f6f676c652e6170692e657870722e7631616c" .
            "706861312e56616c7565123e0a0a657870725f737465707318022003280b" .
            "322a2e676f6f676c652e6170692e657870722e7631616c706861312e4578" .
            "706c61696e2e45787072537465701a2b0a084578707253746570120a0a02" .
            "696418012001280312130a0b76616c75655f696e6465781802200128053a" .
            "021801426f0a1c636f6d2e676f6f676c652e6170692e657870722e763161" .
            "6c70686131420c4578706c61696e50726f746f50015a3c676f6f676c652e" .
            "676f6c616e672e6f72672f67656e70726f746f2f676f6f676c6561706973" .
            "2f6170692f657870722f7631616c706861313b65787072f8010162067072" .
            "6f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


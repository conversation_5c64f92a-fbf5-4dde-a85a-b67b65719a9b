<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/quota.proto

namespace GPBMetadata\Google\Api;

class Quota
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0aa5050a16676f6f676c652f6170692f71756f74612e70726f746f120a67" .
            "6f6f676c652e617069225d0a0551756f746112260a066c696d6974731803" .
            "2003280b32162e676f6f676c652e6170692e51756f74614c696d6974122c" .
            "0a0c6d65747269635f72756c657318042003280b32162e676f6f676c652e" .
            "6170692e4d657472696352756c652291010a0a4d657472696352756c6512" .
            "100a0873656c6563746f72180120012809123d0a0c6d65747269635f636f" .
            "73747318022003280b32272e676f6f676c652e6170692e4d657472696352" .
            "756c652e4d6574726963436f737473456e7472791a320a104d6574726963" .
            "436f737473456e747279120b0a036b6579180120012809120d0a0576616c" .
            "75651802200128033a0238012295020a0a51756f74614c696d6974120c0a" .
            "046e616d6518062001280912130a0b6465736372697074696f6e18022001" .
            "280912150a0d64656661756c745f6c696d697418032001280312110a096d" .
            "61785f6c696d697418042001280312110a09667265655f74696572180720" .
            "01280312100a086475726174696f6e180520012809120e0a066d65747269" .
            "63180820012809120c0a04756e697418092001280912320a0676616c7565" .
            "73180a2003280b32222e676f6f676c652e6170692e51756f74614c696d69" .
            "742e56616c756573456e74727912140a0c646973706c61795f6e616d6518" .
            "0c200128091a2d0a0b56616c756573456e747279120b0a036b6579180120" .
            "012809120d0a0576616c75651802200128033a023801426c0a0e636f6d2e" .
            "676f6f676c652e617069420a51756f746150726f746f50015a45676f6f67" .
            "6c652e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c6561" .
            "7069732f6170692f73657276696365636f6e6669673b7365727669636563" .
            "6f6e666967a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


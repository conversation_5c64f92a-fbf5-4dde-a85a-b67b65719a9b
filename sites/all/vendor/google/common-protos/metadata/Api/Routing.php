<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/routing.proto

namespace GPBMetadata\Google\Api;

class Routing
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
google/api/routing.proto
google.api google/protobuf/descriptor.proto"G
RoutingRule8
routing_parameters (2.google.api.RoutingParameter"8
RoutingParameter
field (	

path_template (	Bj
com.google.apiBRoutingProtoPZAgoogle.golang.org/genproto/googleapis/api/annotations;annotations�GAPIbproto3'
        , true);

        static::$is_initialized = true;
    }
}


<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/date.proto

namespace GPBMetadata\Google\Type;

class Date
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0abe010a16676f6f676c652f747970652f646174652e70726f746f120b67" .
            "6f6f676c652e7479706522300a0444617465120c0a047965617218012001" .
            "2805120d0a056d6f6e7468180220012805120b0a03646179180320012805" .
            "425d0a0f636f6d2e676f6f676c652e7479706542094461746550726f746f" .
            "50015a34676f6f676c652e676f6c616e672e6f72672f67656e70726f746f" .
            "2f676f6f676c65617069732f747970652f646174653b64617465f80101a2" .
            "0203475450620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


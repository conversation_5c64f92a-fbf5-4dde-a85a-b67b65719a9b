<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/quaternion.proto

namespace GPBMetadata\Google\Type;

class Quaternion
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ade010a1c676f6f676c652f747970652f7175617465726e696f6e2e7072" .
            "6f746f120b676f6f676c652e7479706522380a0a5175617465726e696f6e" .
            "12090a017818012001280112090a017918022001280112090a017a180320" .
            "01280112090a0177180420012801426f0a0f636f6d2e676f6f676c652e74" .
            "797065420f5175617465726e696f6e50726f746f50015a40676f6f676c65" .
            "2e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c65617069" .
            "732f747970652f7175617465726e696f6e3b7175617465726e696f6ef801" .
            "01a20203475450620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


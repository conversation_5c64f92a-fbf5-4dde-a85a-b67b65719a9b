<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/policy.proto

namespace GPBMetadata\Google\Iam\V1;

class Policy
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Type\Expr::initOnce();
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0afd060a1a676f6f676c652f69616d2f76312f706f6c6963792e70726f74" .
            "6f120d676f6f676c652e69616d2e76311a1c676f6f676c652f6170692f61" .
            "6e6e6f746174696f6e732e70726f746f22510a06506f6c696379120f0a07" .
            "76657273696f6e18012001280512280a0862696e64696e67731804200328" .
            "0b32162e676f6f676c652e69616d2e76312e42696e64696e67120c0a0465" .
            "74616718032001280c224e0a0742696e64696e67120c0a04726f6c651801" .
            "20012809120f0a076d656d6265727318022003280912240a09636f6e6469" .
            "74696f6e18032001280b32112e676f6f676c652e747970652e4578707222" .
            "80010a0b506f6c69637944656c746112330a0e62696e64696e675f64656c" .
            "74617318012003280b321b2e676f6f676c652e69616d2e76312e42696e64" .
            "696e6744656c7461123c0a1361756469745f636f6e6669675f64656c7461" .
            "7318022003280b321f2e676f6f676c652e69616d2e76312e417564697443" .
            "6f6e66696744656c746122bd010a0c42696e64696e6744656c746112320a" .
            "06616374696f6e18012001280e32222e676f6f676c652e69616d2e76312e" .
            "42696e64696e6744656c74612e416374696f6e120c0a04726f6c65180220" .
            "012809120e0a066d656d62657218032001280912240a09636f6e64697469" .
            "6f6e18042001280b32112e676f6f676c652e747970652e4578707222350a" .
            "06416374696f6e12160a12414354494f4e5f554e53504543494649454410" .
            "0012070a034144441001120a0a0652454d4f5645100222bd010a10417564" .
            "6974436f6e66696744656c746112360a06616374696f6e18012001280e32" .
            "262e676f6f676c652e69616d2e76312e4175646974436f6e66696744656c" .
            "74612e416374696f6e120f0a077365727669636518022001280912170a0f" .
            "6578656d707465645f6d656d62657218032001280912100a086c6f675f74" .
            "79706518042001280922350a06416374696f6e12160a12414354494f4e5f" .
            "554e535045434946494544100012070a034144441001120a0a0652454d4f" .
            "564510024283010a11636f6d2e676f6f676c652e69616d2e7631420b506f" .
            "6c69637950726f746f50015a30676f6f676c652e676f6c616e672e6f7267" .
            "2f67656e70726f746f2f676f6f676c65617069732f69616d2f76313b6961" .
            "6df80101aa0213476f6f676c652e436c6f75642e49616d2e5631ca021347" .
            "6f6f676c655c436c6f75645c49616d5c5631620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}


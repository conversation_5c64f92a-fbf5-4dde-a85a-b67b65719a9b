{"name": "google/common-protos", "type": "library", "description": "Google API Common Protos for PHP", "keywords": ["google"], "homepage": "https://github.com/googleapis/common-protos-php", "license": "Apache-2.0", "require": {"google/protobuf": "^3.6.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36||^8.5", "sami/sami": "*"}, "autoload": {"psr-4": {"Google\\Api\\": "src/Api", "Google\\Cloud\\": "src/Cloud", "Google\\Iam\\": "src/Iam", "Google\\Rpc\\": "src/Rpc", "Google\\Type\\": "src/Type", "GPBMetadata\\Google\\Api\\": "metadata/Api", "GPBMetadata\\Google\\Cloud\\": "metadata/Cloud", "GPBMetadata\\Google\\Iam\\": "metadata/Iam", "GPBMetadata\\Google\\Logging\\": "metadata/Logging", "GPBMetadata\\Google\\Rpc\\": "metadata/Rpc", "GPBMetadata\\Google\\Type\\": "metadata/Type"}}}
<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/source_info.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Source information used to create a Service Config
 *
 * Generated from protobuf message <code>google.api.SourceInfo</code>
 */
class SourceInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * All files used during config generation.
     *
     * Generated from protobuf field <code>repeated .google.protobuf.Any source_files = 1;</code>
     */
    private $source_files;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Protobuf\Any[]|\Google\Protobuf\Internal\RepeatedField $source_files
     *           All files used during config generation.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\SourceInfo::initOnce();
        parent::__construct($data);
    }

    /**
     * All files used during config generation.
     *
     * Generated from protobuf field <code>repeated .google.protobuf.Any source_files = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getSourceFiles()
    {
        return $this->source_files;
    }

    /**
     * All files used during config generation.
     *
     * Generated from protobuf field <code>repeated .google.protobuf.Any source_files = 1;</code>
     * @param \Google\Protobuf\Any[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSourceFiles($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Protobuf\Any::class);
        $this->source_files = $arr;

        return $this;
    }

}


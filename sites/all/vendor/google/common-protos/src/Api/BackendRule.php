<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/backend.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A backend rule provides configuration for an individual API element.
 *
 * Generated from protobuf message <code>google.api.BackendRule</code>
 */
class BackendRule extends \Google\Protobuf\Internal\Message
{
    /**
     * Selects the methods to which this rule applies.
     * Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
     *
     * Generated from protobuf field <code>string selector = 1;</code>
     */
    private $selector = '';
    /**
     * The address of the API backend.
     *
     * Generated from protobuf field <code>string address = 2;</code>
     */
    private $address = '';
    /**
     * The number of seconds to wait for a response from a request. The default
     * varies based on the request protocol and deployment environment.
     *
     * Generated from protobuf field <code>double deadline = 3;</code>
     */
    private $deadline = 0.0;
    /**
     * Minimum deadline in seconds needed for this method. Calls having deadline
     * value lower than this will be rejected.
     *
     * Generated from protobuf field <code>double min_deadline = 4;</code>
     */
    private $min_deadline = 0.0;
    /**
     * The number of seconds to wait for the completion of a long running
     * operation. The default is no deadline.
     *
     * Generated from protobuf field <code>double operation_deadline = 5;</code>
     */
    private $operation_deadline = 0.0;
    /**
     * Generated from protobuf field <code>.google.api.BackendRule.PathTranslation path_translation = 6;</code>
     */
    private $path_translation = 0;
    protected $authentication;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $selector
     *           Selects the methods to which this rule applies.
     *           Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
     *     @type string $address
     *           The address of the API backend.
     *     @type float $deadline
     *           The number of seconds to wait for a response from a request. The default
     *           varies based on the request protocol and deployment environment.
     *     @type float $min_deadline
     *           Minimum deadline in seconds needed for this method. Calls having deadline
     *           value lower than this will be rejected.
     *     @type float $operation_deadline
     *           The number of seconds to wait for the completion of a long running
     *           operation. The default is no deadline.
     *     @type int $path_translation
     *     @type string $jwt_audience
     *           The JWT audience is used when generating a JWT ID token for the backend.
     *           This ID token will be added in the HTTP "authorization" header, and sent
     *           to the backend.
     *     @type bool $disable_auth
     *           When disable_auth is false,  a JWT ID token will be generated with the
     *           value from [BackendRule.address][google.api.BackendRule.address] as jwt_audience, overrode to the HTTP
     *           "Authorization" request header and sent to the backend.
     *           When disable_auth is true, a JWT ID token won't be generated and the
     *           original "Authorization" HTTP header will be preserved. If the header is
     *           used to carry the original token and is expected by the backend, this
     *           field must be set to true to preserve the header.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Backend::initOnce();
        parent::__construct($data);
    }

    /**
     * Selects the methods to which this rule applies.
     * Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
     *
     * Generated from protobuf field <code>string selector = 1;</code>
     * @return string
     */
    public function getSelector()
    {
        return $this->selector;
    }

    /**
     * Selects the methods to which this rule applies.
     * Refer to [selector][google.api.DocumentationRule.selector] for syntax details.
     *
     * Generated from protobuf field <code>string selector = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setSelector($var)
    {
        GPBUtil::checkString($var, True);
        $this->selector = $var;

        return $this;
    }

    /**
     * The address of the API backend.
     *
     * Generated from protobuf field <code>string address = 2;</code>
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * The address of the API backend.
     *
     * Generated from protobuf field <code>string address = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setAddress($var)
    {
        GPBUtil::checkString($var, True);
        $this->address = $var;

        return $this;
    }

    /**
     * The number of seconds to wait for a response from a request. The default
     * varies based on the request protocol and deployment environment.
     *
     * Generated from protobuf field <code>double deadline = 3;</code>
     * @return float
     */
    public function getDeadline()
    {
        return $this->deadline;
    }

    /**
     * The number of seconds to wait for a response from a request. The default
     * varies based on the request protocol and deployment environment.
     *
     * Generated from protobuf field <code>double deadline = 3;</code>
     * @param float $var
     * @return $this
     */
    public function setDeadline($var)
    {
        GPBUtil::checkDouble($var);
        $this->deadline = $var;

        return $this;
    }

    /**
     * Minimum deadline in seconds needed for this method. Calls having deadline
     * value lower than this will be rejected.
     *
     * Generated from protobuf field <code>double min_deadline = 4;</code>
     * @return float
     */
    public function getMinDeadline()
    {
        return $this->min_deadline;
    }

    /**
     * Minimum deadline in seconds needed for this method. Calls having deadline
     * value lower than this will be rejected.
     *
     * Generated from protobuf field <code>double min_deadline = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setMinDeadline($var)
    {
        GPBUtil::checkDouble($var);
        $this->min_deadline = $var;

        return $this;
    }

    /**
     * The number of seconds to wait for the completion of a long running
     * operation. The default is no deadline.
     *
     * Generated from protobuf field <code>double operation_deadline = 5;</code>
     * @return float
     */
    public function getOperationDeadline()
    {
        return $this->operation_deadline;
    }

    /**
     * The number of seconds to wait for the completion of a long running
     * operation. The default is no deadline.
     *
     * Generated from protobuf field <code>double operation_deadline = 5;</code>
     * @param float $var
     * @return $this
     */
    public function setOperationDeadline($var)
    {
        GPBUtil::checkDouble($var);
        $this->operation_deadline = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.google.api.BackendRule.PathTranslation path_translation = 6;</code>
     * @return int
     */
    public function getPathTranslation()
    {
        return $this->path_translation;
    }

    /**
     * Generated from protobuf field <code>.google.api.BackendRule.PathTranslation path_translation = 6;</code>
     * @param int $var
     * @return $this
     */
    public function setPathTranslation($var)
    {
        GPBUtil::checkEnum($var, \Google\Api\BackendRule_PathTranslation::class);
        $this->path_translation = $var;

        return $this;
    }

    /**
     * The JWT audience is used when generating a JWT ID token for the backend.
     * This ID token will be added in the HTTP "authorization" header, and sent
     * to the backend.
     *
     * Generated from protobuf field <code>string jwt_audience = 7;</code>
     * @return string
     */
    public function getJwtAudience()
    {
        return $this->readOneof(7);
    }

    /**
     * The JWT audience is used when generating a JWT ID token for the backend.
     * This ID token will be added in the HTTP "authorization" header, and sent
     * to the backend.
     *
     * Generated from protobuf field <code>string jwt_audience = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setJwtAudience($var)
    {
        GPBUtil::checkString($var, True);
        $this->writeOneof(7, $var);

        return $this;
    }

    /**
     * When disable_auth is false,  a JWT ID token will be generated with the
     * value from [BackendRule.address][google.api.BackendRule.address] as jwt_audience, overrode to the HTTP
     * "Authorization" request header and sent to the backend.
     * When disable_auth is true, a JWT ID token won't be generated and the
     * original "Authorization" HTTP header will be preserved. If the header is
     * used to carry the original token and is expected by the backend, this
     * field must be set to true to preserve the header.
     *
     * Generated from protobuf field <code>bool disable_auth = 8;</code>
     * @return bool
     */
    public function getDisableAuth()
    {
        return $this->readOneof(8);
    }

    /**
     * When disable_auth is false,  a JWT ID token will be generated with the
     * value from [BackendRule.address][google.api.BackendRule.address] as jwt_audience, overrode to the HTTP
     * "Authorization" request header and sent to the backend.
     * When disable_auth is true, a JWT ID token won't be generated and the
     * original "Authorization" HTTP header will be preserved. If the header is
     * used to carry the original token and is expected by the backend, this
     * field must be set to true to preserve the header.
     *
     * Generated from protobuf field <code>bool disable_auth = 8;</code>
     * @param bool $var
     * @return $this
     */
    public function setDisableAuth($var)
    {
        GPBUtil::checkBool($var);
        $this->writeOneof(8, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getAuthentication()
    {
        return $this->whichOneof("authentication");
    }

}


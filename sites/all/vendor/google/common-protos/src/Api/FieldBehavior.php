<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/field_behavior.proto

namespace Google\Api;

use UnexpectedValueException;

/**
 * An indicator of the behavior of a given field (for example, that a field
 * is required in requests, or given as output but ignored as input).
 * This **does not** change the behavior in protocol buffers itself; it only
 * denotes the behavior and may affect how API tooling handles the field.
 * Note: This enum **may** receive new values in the future.
 *
 * Protobuf type <code>google.api.FieldBehavior</code>
 */
class FieldBehavior
{
    /**
     * Conventional default for enums. Do not use this.
     *
     * Generated from protobuf enum <code>FIELD_BEHAVIOR_UNSPECIFIED = 0;</code>
     */
    const FIELD_BEHAVIOR_UNSPECIFIED = 0;
    /**
     * Specifically denotes a field as optional.
     * While all fields in protocol buffers are optional, this may be specified
     * for emphasis if appropriate.
     *
     * Generated from protobuf enum <code>OPTIONAL = 1;</code>
     */
    const OPTIONAL = 1;
    /**
     * Denotes a field as required.
     * This indicates that the field **must** be provided as part of the request,
     * and failure to do so will cause an error (usually `INVALID_ARGUMENT`).
     *
     * Generated from protobuf enum <code>REQUIRED = 2;</code>
     */
    const REQUIRED = 2;
    /**
     * Denotes a field as output only.
     * This indicates that the field is provided in responses, but including the
     * field in a request does nothing (the server *must* ignore it and
     * *must not* throw an error as a result of the field's presence).
     *
     * Generated from protobuf enum <code>OUTPUT_ONLY = 3;</code>
     */
    const OUTPUT_ONLY = 3;
    /**
     * Denotes a field as input only.
     * This indicates that the field is provided in requests, and the
     * corresponding field is not included in output.
     *
     * Generated from protobuf enum <code>INPUT_ONLY = 4;</code>
     */
    const INPUT_ONLY = 4;
    /**
     * Denotes a field as immutable.
     * This indicates that the field may be set once in a request to create a
     * resource, but may not be changed thereafter.
     *
     * Generated from protobuf enum <code>IMMUTABLE = 5;</code>
     */
    const IMMUTABLE = 5;

    private static $valueToName = [
        self::FIELD_BEHAVIOR_UNSPECIFIED => 'FIELD_BEHAVIOR_UNSPECIFIED',
        self::OPTIONAL => 'OPTIONAL',
        self::REQUIRED => 'REQUIRED',
        self::OUTPUT_ONLY => 'OUTPUT_ONLY',
        self::INPUT_ONLY => 'INPUT_ONLY',
        self::IMMUTABLE => 'IMMUTABLE',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}


<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: example.proto

namespace GPBMetadata\Google\ApiCore\Tests\Unit;

class Example
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0a85010a0d6578616d706c652e70726f746f1219676f6f676c652e617069" .
            "636f72652e74657374732e756e6974220b0a094d794d6573736167654244" .
            "ca0219476f6f676c655c417069436f72655c54657374735c556e6974e202" .
            "254750424d657461646174615c476f6f676c655c417069436f72655c5465" .
            "7374735c556e6974620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}


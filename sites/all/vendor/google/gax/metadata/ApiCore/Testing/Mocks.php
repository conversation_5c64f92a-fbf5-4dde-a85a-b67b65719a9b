<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: Testing/mocks.proto

namespace GPBMetadata\ApiCore\Testing;

class Mocks
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\FieldMask::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        \GPBMetadata\Google\Protobuf\Struct::initOnce();
        \GPBMetadata\Google\Protobuf\Wrappers::initOnce();
        $pool->internalAddGeneratedFile(
            '
�	
Testing/mocks.protogoogle.apicore.testinggoogle/protobuf/timestamp.protogoogle/protobuf/duration.protogoogle/protobuf/struct.protogoogle/protobuf/wrappers.proto"4
MockRequest

page_token (	
	page_size ("�
MockResponse
name (	
number (
resources_list (	
next_page_token (	M

resources_map (26.google.apicore.testing.MockResponse.ResourcesMapEntry3
ResourcesMapEntry
key (	
value (	:8"�
MockRequestBody
name (	
number (
repeated_field (	?
nested_message (2\'.google.apicore.testing.MockRequestBody0
bytes_value (2.google.protobuf.BytesValue1
duration_value (2.google.protobuf.Duration.

field_mask (2.google.protobuf.FieldMask0
int64_value (2.google.protobuf.Int64Value.

list_value	 (2.google.protobuf.ListValue2
string_value
 (2.google.protobuf.StringValue-
struct_value (2.google.protobuf.Struct3
timestamp_value (2.google.protobuf.Timestamp+
value_value
 (2.google.protobuf.Value
field_1 (	H 
field_2 (	H 
field_3 (	H B
oneof_fieldB7�Google\\ApiCore\\Testing�GPBMetadata\\ApiCore\\Testingbproto3'
        , true);

        static::$is_initialized = true;
    }
}


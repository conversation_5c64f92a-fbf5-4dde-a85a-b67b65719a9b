<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/empty.proto

namespace GPBMetadata\Google\Protobuf;

class GPBEmpty
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
google/protobuf/empty.protogoogle.protobuf"
EmptyB}
com.google.protobufB
EmptyProtoPZ.google.golang.org/protobuf/types/known/emptypb��GPB�Google.Protobuf.WellKnownTypesbproto3'
        , true);

        static::$is_initialized = true;
    }
}


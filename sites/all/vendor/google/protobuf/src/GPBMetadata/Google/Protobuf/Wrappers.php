<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/wrappers.proto

namespace GPBMetadata\Google\Protobuf;

class Wrappers
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
google/protobuf/wrappers.protogoogle.protobuf"
DoubleValue
value ("

FloatValue
value ("

Int64Value
value ("
UInt64Value
value ("

Int32Value
value ("
UInt32Value
value (
"
	BoolValue
value ("
StringValue
value (	"

BytesValue
value (B�
com.google.protobufB
WrappersProtoPZ1google.golang.org/protobuf/types/known/wrapperspb��GPB�Google.Protobuf.WellKnownTypesbproto3'
        , true);

        static::$is_initialized = true;
    }
}


<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/field_mask.proto

namespace GPBMetadata\Google\Protobuf;

class FieldMask
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
 google/protobuf/field_mask.protogoogle.protobuf"
	FieldMask
paths (	B�
com.google.protobufBFieldMaskProtoPZ2google.golang.org/protobuf/types/known/fieldmaskpb��GPB�Google.Protobuf.WellKnownTypesbproto3'
        , true);

        static::$is_initialized = true;
    }
}


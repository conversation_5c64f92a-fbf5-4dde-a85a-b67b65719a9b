<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/duration.proto

namespace GPBMetadata\Google\Protobuf;

class Duration
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            '
�
google/protobuf/duration.protogoogle.protobuf"*
Duration
seconds (
nanos (B�
com.google.protobufB
DurationProtoPZ1google.golang.org/protobuf/types/known/durationpb��GPB�Google.Protobuf.WellKnownTypesbproto3'
        , true);

        static::$is_initialized = true;
    }
}


<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1beta1/webrisk.proto

namespace GPBMetadata\Google\Cloud\Webrisk\V1Beta1;

class Webrisk
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Api\Client::initOnce();
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        $pool->internalAddGeneratedFile(
            '
�
*google/cloud/webrisk/v1beta1/webrisk.protogoogle.cloud.webrisk.v1beta1google/api/client.protogoogle/api/field_behavior.protogoogle/protobuf/timestamp.proto"�
ComputeThreatListDiffRequestB
threat_type (2(.google.cloud.webrisk.v1beta1.ThreatTypeB�A

version_token (`
constraints (2F.google.cloud.webrisk.v1beta1.ComputeThreatListDiffRequest.ConstraintsB�A�
Constraints
max_diff_entries (
max_database_entries (M
supported_compressions (2-.google.cloud.webrisk.v1beta1.CompressionType"�
ComputeThreatListDiffResponse_

response_type (2H.google.cloud.webrisk.v1beta1.ComputeThreatListDiffResponse.ResponseTypeE
	additions (22.google.cloud.webrisk.v1beta1.ThreatEntryAdditionsC
removals (21.google.cloud.webrisk.v1beta1.ThreatEntryRemovals
new_version_token (V
checksum (2D.google.cloud.webrisk.v1beta1.ComputeThreatListDiffResponse.Checksum9
recommended_next_diff (2.google.protobuf.Timestamp
Checksum
sha256 ("B
ResponseType
RESPONSE_TYPE_UNSPECIFIED 
DIFF	
RESET"j
SearchUrisRequest
uri (	B�AC
threat_types (2(.google.cloud.webrisk.v1beta1.ThreatTypeB�A"�
SearchUrisResponseJ
threat (2:.google.cloud.webrisk.v1beta1.SearchUrisResponse.ThreatUri|
	ThreatUri>
threat_types (2(.google.cloud.webrisk.v1beta1.ThreatType/
expire_time (2.google.protobuf.Timestamp"o
SearchHashesRequest
hash_prefix (C
threat_types (2(.google.cloud.webrisk.v1beta1.ThreatTypeB�A"�
SearchHashesResponseN
threats (2=.google.cloud.webrisk.v1beta1.SearchHashesResponse.ThreatHash8
negative_expire_time (2.google.protobuf.Timestamp�

ThreatHash>
threat_types (2(.google.cloud.webrisk.v1beta1.ThreatType
hash (/
expire_time (2.google.protobuf.Timestamp"�
ThreatEntryAdditions;

raw_hashes (2\'.google.cloud.webrisk.v1beta1.RawHashesD
rice_hashes (2/.google.cloud.webrisk.v1beta1.RiceDeltaEncoding"�
ThreatEntryRemovals=
raw_indices (2(.google.cloud.webrisk.v1beta1.RawIndicesE
rice_indices (2/.google.cloud.webrisk.v1beta1.RiceDeltaEncoding"

RawIndices
indices ("4
	RawHashes
prefix_size (

raw_hashes ("k
RiceDeltaEncoding
first_value (
rice_parameter (
entry_count (
encoded_data (*e

ThreatType
THREAT_TYPE_UNSPECIFIED 
MALWARE
SOCIAL_ENGINEERING
UNWANTED_SOFTWARE*F
CompressionType 
COMPRESSION_TYPE_UNSPECIFIED 
RAW
RICE2�
WebRiskServiceV1Beta1�
ComputeThreatListDiff:.google.cloud.webrisk.v1beta1.ComputeThreatListDiffRequest;.google.cloud.webrisk.v1beta1.ComputeThreatListDiffResponse"P���" /v1beta1/threatLists:computeDiff�A%threat_type,version_token,constraints�

SearchUris/.google.cloud.webrisk.v1beta1.SearchUrisRequest0.google.cloud.webrisk.v1beta1.SearchUrisResponse"/���/v1beta1/uris:search�Auri,threat_types�
SearchHashes1.google.cloud.webrisk.v1beta1.SearchHashesRequest2.google.cloud.webrisk.v1beta1.SearchHashesResponse"9���/v1beta1/hashes:search�Ahash_prefix,threat_typesJ�Awebrisk.googleapis.com�A.https://www.googleapis.com/auth/cloud-platformB�
com.google.webrisk.v1beta1BWebRiskProtoPZ:cloud.google.com/go/webrisk/apiv1beta1/webriskpb;webriskpb�GCWR�Google.Cloud.WebRisk.V1Beta1�Google\\Cloud\\WebRisk\\V1beta1�Google::Cloud::WebRisk::V1beta1bproto3'
        , true);

        static::$is_initialized = true;
    }
}


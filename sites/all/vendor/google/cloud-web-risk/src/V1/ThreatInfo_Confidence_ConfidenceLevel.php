<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1/webrisk.proto

namespace Google\Cloud\WebRisk\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\WebRisk\V1\ThreatInfo\Confidence\ConfidenceLevel instead.
     * @deprecated
     */
    class ThreatInfo_Confidence_ConfidenceLevel {}
}
class_exists(ThreatInfo\Confidence\ConfidenceLevel::class);
@trigger_error('Google\Cloud\WebRisk\V1\ThreatInfo_Confidence_ConfidenceLevel is deprecated and will be removed in the next major release. Use Google\Cloud\WebRisk\V1\ThreatInfo\Confidence\ConfidenceLevel instead', E_USER_DEPRECATED);


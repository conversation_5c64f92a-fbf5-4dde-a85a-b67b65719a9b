<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1/webrisk.proto

namespace Google\Cloud\WebRisk\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\WebRisk\V1\ComputeThreatListDiffRequest\Constraints instead.
     * @deprecated
     */
    class ComputeThreatListDiffRequest_Constraints {}
}
class_exists(ComputeThreatListDiffRequest\Constraints::class);
@trigger_error('Google\Cloud\WebRisk\V1\ComputeThreatListDiffRequest_Constraints is deprecated and will be removed in the next major release. Use Google\Cloud\WebRisk\V1\ComputeThreatListDiffRequest\Constraints instead', E_USER_DEPRECATED);


<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1/webrisk.proto

namespace Google\Cloud\WebRisk\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\WebRisk\V1\ThreatDiscovery\Platform instead.
     * @deprecated
     */
    class ThreatDiscovery_Platform {}
}
class_exists(ThreatDiscovery\Platform::class);
@trigger_error('Google\Cloud\WebRisk\V1\ThreatDiscovery_Platform is deprecated and will be removed in the next major release. Use Google\Cloud\WebRisk\V1\ThreatDiscovery\Platform instead', E_USER_DEPRECATED);


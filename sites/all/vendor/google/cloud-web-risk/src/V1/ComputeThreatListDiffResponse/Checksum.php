<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1/webrisk.proto

namespace Google\Cloud\WebRisk\V1\ComputeThreatListDiffResponse;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The expected state of a client's local database.
 *
 * Generated from protobuf message <code>google.cloud.webrisk.v1.ComputeThreatListDiffResponse.Checksum</code>
 */
class Checksum extends \Google\Protobuf\Internal\Message
{
    /**
     * The SHA256 hash of the client state; that is, of the sorted list of all
     * hashes present in the database.
     *
     * Generated from protobuf field <code>bytes sha256 = 1;</code>
     */
    private $sha256 = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $sha256
     *           The SHA256 hash of the client state; that is, of the sorted list of all
     *           hashes present in the database.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Webrisk\V1\Webrisk::initOnce();
        parent::__construct($data);
    }

    /**
     * The SHA256 hash of the client state; that is, of the sorted list of all
     * hashes present in the database.
     *
     * Generated from protobuf field <code>bytes sha256 = 1;</code>
     * @return string
     */
    public function getSha256()
    {
        return $this->sha256;
    }

    /**
     * The SHA256 hash of the client state; that is, of the sorted list of all
     * hashes present in the database.
     *
     * Generated from protobuf field <code>bytes sha256 = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setSha256($var)
    {
        GPBUtil::checkString($var, False);
        $this->sha256 = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Checksum::class, \Google\Cloud\WebRisk\V1\ComputeThreatListDiffResponse_Checksum::class);


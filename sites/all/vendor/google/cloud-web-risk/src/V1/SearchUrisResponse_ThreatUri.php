<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1/webrisk.proto

namespace Google\Cloud\WebRisk\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\WebRisk\V1\SearchUrisResponse\ThreatUri instead.
     * @deprecated
     */
    class SearchUrisResponse_ThreatUri {}
}
class_exists(SearchUrisResponse\ThreatUri::class);
@trigger_error('Google\Cloud\WebRisk\V1\SearchUrisResponse_ThreatUri is deprecated and will be removed in the next major release. Use Google\Cloud\WebRisk\V1\SearchUrisResponse\ThreatUri instead', E_USER_DEPRECATED);


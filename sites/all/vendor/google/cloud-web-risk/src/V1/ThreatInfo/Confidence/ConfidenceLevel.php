<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1/webrisk.proto

namespace Google\Cloud\WebRisk\V1\ThreatInfo\Confidence;

use UnexpectedValueException;

/**
 * Enum representation of confidence.
 *
 * Protobuf type <code>google.cloud.webrisk.v1.ThreatInfo.Confidence.ConfidenceLevel</code>
 */
class ConfidenceLevel
{
    /**
     * Default.
     *
     * Generated from protobuf enum <code>CONFIDENCE_LEVEL_UNSPECIFIED = 0;</code>
     */
    const CONFIDENCE_LEVEL_UNSPECIFIED = 0;
    /**
     * Less than 60% confidence that the URI is unsafe.
     *
     * Generated from protobuf enum <code>LOW = 1;</code>
     */
    const LOW = 1;
    /**
     * Between 60% and 80% confidence that the URI is unsafe.
     *
     * Generated from protobuf enum <code>MEDIUM = 2;</code>
     */
    const MEDIUM = 2;
    /**
     * Greater than 80% confidence that the URI is unsafe.
     *
     * Generated from protobuf enum <code>HIGH = 3;</code>
     */
    const HIGH = 3;

    private static $valueToName = [
        self::CONFIDENCE_LEVEL_UNSPECIFIED => 'CONFIDENCE_LEVEL_UNSPECIFIED',
        self::LOW => 'LOW',
        self::MEDIUM => 'MEDIUM',
        self::HIGH => 'HIGH',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ConfidenceLevel::class, \Google\Cloud\WebRisk\V1\ThreatInfo_Confidence_ConfidenceLevel::class);


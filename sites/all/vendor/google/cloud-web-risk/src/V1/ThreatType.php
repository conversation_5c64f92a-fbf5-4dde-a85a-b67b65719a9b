<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1/webrisk.proto

namespace Google\Cloud\WebRisk\V1;

use UnexpectedValueException;

/**
 * The type of threat. This maps directly to the threat list a threat may
 * belong to.
 *
 * Protobuf type <code>google.cloud.webrisk.v1.ThreatType</code>
 */
class ThreatType
{
    /**
     * No entries should match this threat type. This threat type is unused.
     *
     * Generated from protobuf enum <code>THREAT_TYPE_UNSPECIFIED = 0;</code>
     */
    const THREAT_TYPE_UNSPECIFIED = 0;
    /**
     * Malware targeting any platform.
     *
     * Generated from protobuf enum <code>MALWARE = 1;</code>
     */
    const MALWARE = 1;
    /**
     * Social engineering targeting any platform.
     *
     * Generated from protobuf enum <code>SOCIAL_ENGINEERING = 2;</code>
     */
    const SOCIAL_ENGINEERING = 2;
    /**
     * Unwanted software targeting any platform.
     *
     * Generated from protobuf enum <code>UNWANTED_SOFTWARE = 3;</code>
     */
    const UNWANTED_SOFTWARE = 3;
    /**
     * A list of extended coverage social engineering URIs targeting any
     * platform.
     *
     * Generated from protobuf enum <code>SOCIAL_ENGINEERING_EXTENDED_COVERAGE = 4;</code>
     */
    const SOCIAL_ENGINEERING_EXTENDED_COVERAGE = 4;

    private static $valueToName = [
        self::THREAT_TYPE_UNSPECIFIED => 'THREAT_TYPE_UNSPECIFIED',
        self::MALWARE => 'MALWARE',
        self::SOCIAL_ENGINEERING => 'SOCIAL_ENGINEERING',
        self::UNWANTED_SOFTWARE => 'UNWANTED_SOFTWARE',
        self::SOCIAL_ENGINEERING_EXTENDED_COVERAGE => 'SOCIAL_ENGINEERING_EXTENDED_COVERAGE',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}


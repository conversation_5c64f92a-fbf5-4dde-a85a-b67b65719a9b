<?php
/*
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * GENERATED CODE WARNING
 * Generated by gapic-generator-php from the file
 * https://github.com/googleapis/googleapis/blob/master/google/cloud/webrisk/v1/webrisk.proto
 * Updates to the above are reflected here through a refresh process.
 */

namespace Google\Cloud\WebRisk\V1\Gapic;

use Google\ApiCore\ApiException;
use Google\ApiCore\CredentialsWrapper;
use Google\ApiCore\GapicClientTrait;
use Google\ApiCore\LongRunning\OperationsClient;
use Google\ApiCore\OperationResponse;
use Google\ApiCore\PathTemplate;
use Google\ApiCore\RequestParamsHeaderDescriptor;
use Google\ApiCore\RetrySettings;
use Google\ApiCore\Transport\TransportInterface;
use Google\ApiCore\ValidationException;
use Google\Auth\FetchAuthTokenInterface;
use Google\Cloud\WebRisk\V1\ComputeThreatListDiffRequest;
use Google\Cloud\WebRisk\V1\ComputeThreatListDiffRequest\Constraints;
use Google\Cloud\WebRisk\V1\ComputeThreatListDiffResponse;
use Google\Cloud\WebRisk\V1\CreateSubmissionRequest;
use Google\Cloud\WebRisk\V1\SearchHashesRequest;
use Google\Cloud\WebRisk\V1\SearchHashesResponse;
use Google\Cloud\WebRisk\V1\SearchUrisRequest;
use Google\Cloud\WebRisk\V1\SearchUrisResponse;
use Google\Cloud\WebRisk\V1\Submission;
use Google\Cloud\WebRisk\V1\SubmitUriRequest;
use Google\Cloud\WebRisk\V1\ThreatDiscovery;
use Google\Cloud\WebRisk\V1\ThreatInfo;
use Google\Cloud\WebRisk\V1\ThreatType;
use Google\LongRunning\Operation;

/**
 * Service Description: Web Risk API defines an interface to detect malicious URLs on your
 * website and in client applications.
 *
 * This class provides the ability to make remote calls to the backing service through method
 * calls that map to API methods. Sample code to get started:
 *
 * ```
 * $webRiskServiceClient = new Google\Cloud\WebRisk\V1\WebRiskServiceClient();
 * try {
 *     $threatType = Google\Cloud\WebRisk\V1\ThreatType::THREAT_TYPE_UNSPECIFIED;
 *     $constraints = new Google\Cloud\WebRisk\V1\ComputeThreatListDiffRequest\Constraints();
 *     $response = $webRiskServiceClient->computeThreatListDiff($threatType, $constraints);
 * } finally {
 *     $webRiskServiceClient->close();
 * }
 * ```
 *
 * Many parameters require resource names to be formatted in a particular way. To
 * assist with these names, this class includes a format method for each type of
 * name, and additionally a parseName method to extract the individual identifiers
 * contained within formatted names that are returned by the API.
 *
 * This service has a new (beta) implementation. See {@see
 * \Google\Cloud\WebRisk\V1\Client\WebRiskServiceClient} to use the new surface.
 */
class WebRiskServiceGapicClient
{
    use GapicClientTrait;

    /** The name of the service. */
    const SERVICE_NAME = 'google.cloud.webrisk.v1.WebRiskService';

    /** The default address of the service. */
    const SERVICE_ADDRESS = 'webrisk.googleapis.com';

    /** The default port of the service. */
    const DEFAULT_SERVICE_PORT = 443;

    /** The name of the code generator, to be included in the agent header. */
    const CODEGEN_NAME = 'gapic';

    /** The default scopes required by the service. */
    public static $serviceScopes = [
        'https://www.googleapis.com/auth/cloud-platform',
    ];

    private static $projectNameTemplate;

    private static $pathTemplateMap;

    private $operationsClient;

    private static function getClientDefaults()
    {
        return [
            'serviceName' => self::SERVICE_NAME,
            'apiEndpoint' =>
                self::SERVICE_ADDRESS . ':' . self::DEFAULT_SERVICE_PORT,
            'clientConfig' =>
                __DIR__ . '/../resources/web_risk_service_client_config.json',
            'descriptorsConfigPath' =>
                __DIR__ .
                '/../resources/web_risk_service_descriptor_config.php',
            'gcpApiConfigPath' =>
                __DIR__ . '/../resources/web_risk_service_grpc_config.json',
            'credentialsConfig' => [
                'defaultScopes' => self::$serviceScopes,
            ],
            'transportConfig' => [
                'rest' => [
                    'restClientConfigPath' =>
                        __DIR__ .
                        '/../resources/web_risk_service_rest_client_config.php',
                ],
            ],
        ];
    }

    private static function getProjectNameTemplate()
    {
        if (self::$projectNameTemplate == null) {
            self::$projectNameTemplate = new PathTemplate('projects/{project}');
        }

        return self::$projectNameTemplate;
    }

    private static function getPathTemplateMap()
    {
        if (self::$pathTemplateMap == null) {
            self::$pathTemplateMap = [
                'project' => self::getProjectNameTemplate(),
            ];
        }

        return self::$pathTemplateMap;
    }

    /**
     * Formats a string containing the fully-qualified path to represent a project
     * resource.
     *
     * @param string $project
     *
     * @return string The formatted project resource.
     */
    public static function projectName($project)
    {
        return self::getProjectNameTemplate()->render([
            'project' => $project,
        ]);
    }

    /**
     * Parses a formatted name string and returns an associative array of the components in the name.
     * The following name formats are supported:
     * Template: Pattern
     * - project: projects/{project}
     *
     * The optional $template argument can be supplied to specify a particular pattern,
     * and must match one of the templates listed above. If no $template argument is
     * provided, or if the $template argument does not match one of the templates
     * listed, then parseName will check each of the supported templates, and return
     * the first match.
     *
     * @param string $formattedName The formatted name string
     * @param string $template      Optional name of template to match
     *
     * @return array An associative array from name component IDs to component values.
     *
     * @throws ValidationException If $formattedName could not be matched.
     */
    public static function parseName($formattedName, $template = null)
    {
        $templateMap = self::getPathTemplateMap();
        if ($template) {
            if (!isset($templateMap[$template])) {
                throw new ValidationException(
                    "Template name $template does not exist"
                );
            }

            return $templateMap[$template]->match($formattedName);
        }

        foreach ($templateMap as $templateName => $pathTemplate) {
            try {
                return $pathTemplate->match($formattedName);
            } catch (ValidationException $ex) {
                // Swallow the exception to continue trying other path templates
            }
        }

        throw new ValidationException(
            "Input did not match any known format. Input: $formattedName"
        );
    }

    /**
     * Return an OperationsClient object with the same endpoint as $this.
     *
     * @return OperationsClient
     */
    public function getOperationsClient()
    {
        return $this->operationsClient;
    }

    /**
     * Resume an existing long running operation that was previously started by a long
     * running API method. If $methodName is not provided, or does not match a long
     * running API method, then the operation can still be resumed, but the
     * OperationResponse object will not deserialize the final response.
     *
     * @param string $operationName The name of the long running operation
     * @param string $methodName    The name of the method used to start the operation
     *
     * @return OperationResponse
     */
    public function resumeOperation($operationName, $methodName = null)
    {
        $options = isset($this->descriptors[$methodName]['longRunning'])
            ? $this->descriptors[$methodName]['longRunning']
            : [];
        $operation = new OperationResponse(
            $operationName,
            $this->getOperationsClient(),
            $options
        );
        $operation->reload();
        return $operation;
    }

    /**
     * Constructor.
     *
     * @param array $options {
     *     Optional. Options for configuring the service API wrapper.
     *
     *     @type string $apiEndpoint
     *           The address of the API remote host. May optionally include the port, formatted
     *           as "<uri>:<port>". Default 'webrisk.googleapis.com:443'.
     *     @type string|array|FetchAuthTokenInterface|CredentialsWrapper $credentials
     *           The credentials to be used by the client to authorize API calls. This option
     *           accepts either a path to a credentials file, or a decoded credentials file as a
     *           PHP array.
     *           *Advanced usage*: In addition, this option can also accept a pre-constructed
     *           {@see \Google\Auth\FetchAuthTokenInterface} object or
     *           {@see \Google\ApiCore\CredentialsWrapper} object. Note that when one of these
     *           objects are provided, any settings in $credentialsConfig will be ignored.
     *     @type array $credentialsConfig
     *           Options used to configure credentials, including auth token caching, for the
     *           client. For a full list of supporting configuration options, see
     *           {@see \Google\ApiCore\CredentialsWrapper::build()} .
     *     @type bool $disableRetries
     *           Determines whether or not retries defined by the client configuration should be
     *           disabled. Defaults to `false`.
     *     @type string|array $clientConfig
     *           Client method configuration, including retry settings. This option can be either
     *           a path to a JSON file, or a PHP array containing the decoded JSON data. By
     *           default this settings points to the default client config file, which is
     *           provided in the resources folder.
     *     @type string|TransportInterface $transport
     *           The transport used for executing network requests. May be either the string
     *           `rest` or `grpc`. Defaults to `grpc` if gRPC support is detected on the system.
     *           *Advanced usage*: Additionally, it is possible to pass in an already
     *           instantiated {@see \Google\ApiCore\Transport\TransportInterface} object. Note
     *           that when this object is provided, any settings in $transportConfig, and any
     *           $apiEndpoint setting, will be ignored.
     *     @type array $transportConfig
     *           Configuration options that will be used to construct the transport. Options for
     *           each supported transport type should be passed in a key for that transport. For
     *           example:
     *           $transportConfig = [
     *               'grpc' => [...],
     *               'rest' => [...],
     *           ];
     *           See the {@see \Google\ApiCore\Transport\GrpcTransport::build()} and
     *           {@see \Google\ApiCore\Transport\RestTransport::build()} methods for the
     *           supported options.
     *     @type callable $clientCertSource
     *           A callable which returns the client cert as a string. This can be used to
     *           provide a certificate and private key to the transport layer for mTLS.
     * }
     *
     * @throws ValidationException
     */
    public function __construct(array $options = [])
    {
        $clientOptions = $this->buildClientOptions($options);
        $this->setClientOptions($clientOptions);
        $this->operationsClient = $this->createOperationsClient($clientOptions);
    }

    /**
     * Gets the most recent threat list diffs. These diffs should be applied to
     * a local database of hashes to keep it up-to-date. If the local database is
     * empty or excessively out-of-date, a complete snapshot of the database will
     * be returned. This Method only updates a single ThreatList at a time. To
     * update multiple ThreatList databases, this method needs to be called once
     * for each list.
     *
     * Sample code:
     * ```
     * $webRiskServiceClient = new Google\Cloud\WebRisk\V1\WebRiskServiceClient();
     * try {
     *     $threatType = Google\Cloud\WebRisk\V1\ThreatType::THREAT_TYPE_UNSPECIFIED;
     *     $constraints = new Google\Cloud\WebRisk\V1\ComputeThreatListDiffRequest\Constraints();
     *     $response = $webRiskServiceClient->computeThreatListDiff($threatType, $constraints);
     * } finally {
     *     $webRiskServiceClient->close();
     * }
     * ```
     *
     * @param int         $threatType   Required. The threat list to update. Only a single ThreatType should be
     *                                  specified per request. If you want to handle multiple ThreatTypes, you must
     *                                  make one request per ThreatType.
     *                                  For allowed values, use constants defined on {@see \Google\Cloud\WebRisk\V1\ThreatType}
     * @param Constraints $constraints  Required. The constraints associated with this request.
     * @param array       $optionalArgs {
     *     Optional.
     *
     *     @type string $versionToken
     *           The current version token of the client for the requested list (the
     *           client version that was received from the last successful diff).
     *           If the client does not have a version token (this is the first time calling
     *           ComputeThreatListDiff), this may be left empty and a full database
     *           snapshot will be returned.
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return \Google\Cloud\WebRisk\V1\ComputeThreatListDiffResponse
     *
     * @throws ApiException if the remote call fails
     */
    public function computeThreatListDiff(
        $threatType,
        $constraints,
        array $optionalArgs = []
    ) {
        $request = new ComputeThreatListDiffRequest();
        $request->setThreatType($threatType);
        $request->setConstraints($constraints);
        if (isset($optionalArgs['versionToken'])) {
            $request->setVersionToken($optionalArgs['versionToken']);
        }

        return $this->startCall(
            'ComputeThreatListDiff',
            ComputeThreatListDiffResponse::class,
            $optionalArgs,
            $request
        )->wait();
    }

    /**
     * Creates a Submission of a URI suspected of containing phishing content to
     * be reviewed. If the result verifies the existence of malicious phishing
     * content, the site will be added to the [Google's Social Engineering
     * lists](https://support.google.com/webmasters/answer/6350487/) in order to
     * protect users that could get exposed to this threat in the future. Only
     * allowlisted projects can use this method during Early Access. Please reach
     * out to Sales or your customer engineer to obtain access.
     *
     * Sample code:
     * ```
     * $webRiskServiceClient = new Google\Cloud\WebRisk\V1\WebRiskServiceClient();
     * try {
     *     $formattedParent = $webRiskServiceClient->projectName('[PROJECT]');
     *     $submission = new Submission();
     *     $response = $webRiskServiceClient->createSubmission($formattedParent, $submission);
     * } finally {
     *     $webRiskServiceClient->close();
     * }
     * ```
     *
     * @param string     $parent       Required. The name of the project that is making the submission. This
     *                                 string is in the format "projects/{project_number}".
     * @param Submission $submission   Required. The submission that contains the content of the phishing report.
     * @param array      $optionalArgs {
     *     Optional.
     *
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return \Google\Cloud\WebRisk\V1\Submission
     *
     * @throws ApiException if the remote call fails
     */
    public function createSubmission(
        $parent,
        $submission,
        array $optionalArgs = []
    ) {
        $request = new CreateSubmissionRequest();
        $requestParamHeaders = [];
        $request->setParent($parent);
        $request->setSubmission($submission);
        $requestParamHeaders['parent'] = $parent;
        $requestParams = new RequestParamsHeaderDescriptor(
            $requestParamHeaders
        );
        $optionalArgs['headers'] = isset($optionalArgs['headers'])
            ? array_merge($requestParams->getHeader(), $optionalArgs['headers'])
            : $requestParams->getHeader();
        return $this->startCall(
            'CreateSubmission',
            Submission::class,
            $optionalArgs,
            $request
        )->wait();
    }

    /**
     * Gets the full hashes that match the requested hash prefix.
     * This is used after a hash prefix is looked up in a threatList
     * and there is a match. The client side threatList only holds partial hashes
     * so the client must query this method to determine if there is a full
     * hash match of a threat.
     *
     * Sample code:
     * ```
     * $webRiskServiceClient = new Google\Cloud\WebRisk\V1\WebRiskServiceClient();
     * try {
     *     $threatTypes = [];
     *     $response = $webRiskServiceClient->searchHashes($threatTypes);
     * } finally {
     *     $webRiskServiceClient->close();
     * }
     * ```
     *
     * @param int[] $threatTypes  Required. The ThreatLists to search in. Multiple ThreatLists may be
     *                            specified.
     *                            For allowed values, use constants defined on {@see \Google\Cloud\WebRisk\V1\ThreatType}
     * @param array $optionalArgs {
     *     Optional.
     *
     *     @type string $hashPrefix
     *           A hash prefix, consisting of the most significant 4-32 bytes of a SHA256
     *           hash. For JSON requests, this field is base64-encoded.
     *           Note that if this parameter is provided by a URI, it must be encoded using
     *           the web safe base64 variant (RFC 4648).
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return \Google\Cloud\WebRisk\V1\SearchHashesResponse
     *
     * @throws ApiException if the remote call fails
     */
    public function searchHashes($threatTypes, array $optionalArgs = [])
    {
        $request = new SearchHashesRequest();
        $request->setThreatTypes($threatTypes);
        if (isset($optionalArgs['hashPrefix'])) {
            $request->setHashPrefix($optionalArgs['hashPrefix']);
        }

        return $this->startCall(
            'SearchHashes',
            SearchHashesResponse::class,
            $optionalArgs,
            $request
        )->wait();
    }

    /**
     * This method is used to check whether a URI is on a given threatList.
     * Multiple threatLists may be searched in a single query.
     * The response will list all requested threatLists the URI was found to
     * match. If the URI is not found on any of the requested ThreatList an
     * empty response will be returned.
     *
     * Sample code:
     * ```
     * $webRiskServiceClient = new Google\Cloud\WebRisk\V1\WebRiskServiceClient();
     * try {
     *     $uri = 'uri';
     *     $threatTypes = [];
     *     $response = $webRiskServiceClient->searchUris($uri, $threatTypes);
     * } finally {
     *     $webRiskServiceClient->close();
     * }
     * ```
     *
     * @param string $uri          Required. The URI to be checked for matches.
     * @param int[]  $threatTypes  Required. The ThreatLists to search in. Multiple ThreatLists may be
     *                             specified.
     *                             For allowed values, use constants defined on {@see \Google\Cloud\WebRisk\V1\ThreatType}
     * @param array  $optionalArgs {
     *     Optional.
     *
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return \Google\Cloud\WebRisk\V1\SearchUrisResponse
     *
     * @throws ApiException if the remote call fails
     */
    public function searchUris($uri, $threatTypes, array $optionalArgs = [])
    {
        $request = new SearchUrisRequest();
        $request->setUri($uri);
        $request->setThreatTypes($threatTypes);
        return $this->startCall(
            'SearchUris',
            SearchUrisResponse::class,
            $optionalArgs,
            $request
        )->wait();
    }

    /**
     * Submits a URI suspected of containing malicious content to be reviewed.
     * Returns a google.longrunning.Operation which, once the review is complete,
     * is updated with its result. You can use the [Pub/Sub API]
     * (https://cloud.google.com/pubsub) to receive notifications for the returned
     * Operation. If the result verifies the existence of malicious content, the
     * site will be added to the [Google's Social Engineering lists]
     * (https://support.google.com/webmasters/answer/6350487/) in order to
     * protect users that could get exposed to this threat in the future. Only
     * allowlisted projects can use this method during Early Access. Please reach
     * out to Sales or your customer engineer to obtain access.
     *
     * Sample code:
     * ```
     * $webRiskServiceClient = new Google\Cloud\WebRisk\V1\WebRiskServiceClient();
     * try {
     *     $formattedParent = $webRiskServiceClient->projectName('[PROJECT]');
     *     $submission = new Submission();
     *     $operationResponse = $webRiskServiceClient->submitUri($formattedParent, $submission);
     *     $operationResponse->pollUntilComplete();
     *     if ($operationResponse->operationSucceeded()) {
     *         $result = $operationResponse->getResult();
     *         // doSomethingWith($result)
     *     } else {
     *         $error = $operationResponse->getError();
     *         // handleError($error)
     *     }
     *     // Alternatively:
     *     // start the operation, keep the operation name, and resume later
     *     $operationResponse = $webRiskServiceClient->submitUri($formattedParent, $submission);
     *     $operationName = $operationResponse->getName();
     *     // ... do other work
     *     $newOperationResponse = $webRiskServiceClient->resumeOperation($operationName, 'submitUri');
     *     while (!$newOperationResponse->isDone()) {
     *         // ... do other work
     *         $newOperationResponse->reload();
     *     }
     *     if ($newOperationResponse->operationSucceeded()) {
     *         $result = $newOperationResponse->getResult();
     *         // doSomethingWith($result)
     *     } else {
     *         $error = $newOperationResponse->getError();
     *         // handleError($error)
     *     }
     * } finally {
     *     $webRiskServiceClient->close();
     * }
     * ```
     *
     * @param string     $parent       Required. The name of the project that is making the submission. This
     *                                 string is in the format "projects/{project_number}".
     * @param Submission $submission   Required. The submission that contains the URI to be scanned.
     * @param array      $optionalArgs {
     *     Optional.
     *
     *     @type ThreatInfo $threatInfo
     *           Provides additional information about the submission.
     *     @type ThreatDiscovery $threatDiscovery
     *           Provides additional information about how the submission was discovered.
     *     @type RetrySettings|array $retrySettings
     *           Retry settings to use for this call. Can be a {@see RetrySettings} object, or an
     *           associative array of retry settings parameters. See the documentation on
     *           {@see RetrySettings} for example usage.
     * }
     *
     * @return \Google\ApiCore\OperationResponse
     *
     * @throws ApiException if the remote call fails
     */
    public function submitUri($parent, $submission, array $optionalArgs = [])
    {
        $request = new SubmitUriRequest();
        $requestParamHeaders = [];
        $request->setParent($parent);
        $request->setSubmission($submission);
        $requestParamHeaders['parent'] = $parent;
        if (isset($optionalArgs['threatInfo'])) {
            $request->setThreatInfo($optionalArgs['threatInfo']);
        }

        if (isset($optionalArgs['threatDiscovery'])) {
            $request->setThreatDiscovery($optionalArgs['threatDiscovery']);
        }

        $requestParams = new RequestParamsHeaderDescriptor(
            $requestParamHeaders
        );
        $optionalArgs['headers'] = isset($optionalArgs['headers'])
            ? array_merge($requestParams->getHeader(), $optionalArgs['headers'])
            : $requestParams->getHeader();
        return $this->startOperationsCall(
            'SubmitUri',
            $optionalArgs,
            $request,
            $this->getOperationsClient()
        )->wait();
    }
}

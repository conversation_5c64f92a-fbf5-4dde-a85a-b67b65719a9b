<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1/webrisk.proto

namespace Google\Cloud\WebRisk\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\WebRisk\V1\ComputeThreatListDiffResponse\Checksum instead.
     * @deprecated
     */
    class ComputeThreatListDiffResponse_Checksum {}
}
class_exists(ComputeThreatListDiffResponse\Checksum::class);
@trigger_error('Google\Cloud\WebRisk\V1\ComputeThreatListDiffResponse_Checksum is deprecated and will be removed in the next major release. Use Google\Cloud\WebRisk\V1\ComputeThreatListDiffResponse\Checksum instead', E_USER_DEPRECATED);


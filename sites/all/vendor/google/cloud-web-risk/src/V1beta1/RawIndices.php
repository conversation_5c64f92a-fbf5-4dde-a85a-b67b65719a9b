<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1beta1/webrisk.proto

namespace Google\Cloud\WebRisk\V1beta1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A set of raw indices to remove from a local list.
 *
 * Generated from protobuf message <code>google.cloud.webrisk.v1beta1.RawIndices</code>
 */
class RawIndices extends \Google\Protobuf\Internal\Message
{
    /**
     * The indices to remove from a lexicographically-sorted local list.
     *
     * Generated from protobuf field <code>repeated int32 indices = 1;</code>
     */
    private $indices;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $indices
     *           The indices to remove from a lexicographically-sorted local list.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Webrisk\V1Beta1\Webrisk::initOnce();
        parent::__construct($data);
    }

    /**
     * The indices to remove from a lexicographically-sorted local list.
     *
     * Generated from protobuf field <code>repeated int32 indices = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getIndices()
    {
        return $this->indices;
    }

    /**
     * The indices to remove from a lexicographically-sorted local list.
     *
     * Generated from protobuf field <code>repeated int32 indices = 1;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setIndices($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::INT32);
        $this->indices = $arr;

        return $this;
    }

}


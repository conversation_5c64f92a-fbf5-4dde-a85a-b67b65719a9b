<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/webrisk/v1beta1/webrisk.proto

namespace Google\Cloud\WebRisk\V1beta1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\WebRisk\V1beta1\SearchHashesResponse\ThreatHash instead.
     * @deprecated
     */
    class SearchHashesResponse_ThreatHash {}
}
class_exists(SearchHashesResponse\ThreatHash::class);
@trigger_error('Google\Cloud\WebRisk\V1beta1\SearchHashesResponse_ThreatHash is deprecated and will be removed in the next major release. Use Google\Cloud\WebRisk\V1beta1\SearchHashesResponse\ThreatHash instead', E_USER_DEPRECATED);


{"name": "google/cloud-web-risk", "description": "Google Cloud Web Risk Client for PHP", "license": "Apache-2.0", "minimum-stability": "stable", "autoload": {"psr-4": {"Google\\Cloud\\WebRisk\\": "src", "GPBMetadata\\Google\\Cloud\\Webrisk\\": "metadata"}}, "autoload-dev": {"psr-4": {"Google\\Cloud\\WebRisk\\Tests\\": "tests"}}, "extra": {"component": {"id": "cloud-web-risk", "path": "WebRisk", "entry": null, "target": "googleapis/google-cloud-php-web-risk.git"}}, "require": {"php": ">=7.4", "google/gax": "^1.19.1"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "suggest": {"ext-grpc": "Enables use of gRPC, a universal high-performance RPC framework created by Google.", "ext-protobuf": "Provides a significant increase in throughput over the pure PHP protobuf implementation. See https://cloud.google.com/php/grpc for installation instructions."}}
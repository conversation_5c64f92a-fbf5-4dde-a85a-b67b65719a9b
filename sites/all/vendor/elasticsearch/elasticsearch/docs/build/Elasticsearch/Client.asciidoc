

[[Elasticsearch_Client]]
=== Elasticsearch\Client



Class Client


*Methods*

The class defines the following methods:

* <<Elasticsearch_Clientinfo_info,`info()`>>
* <<Elasticsearch_Clientping_ping,`ping()`>>
* <<Elasticsearch_Clientget_get,`get()`>>
* <<Elasticsearch_ClientgetSource_getSource,`getSource()`>>
* <<Elasticsearch_Clientdelete_delete,`delete()`>>
* <<Elasticsearch_Clientcount_count,`count()`>>
* <<Elasticsearch_ClientcountPercolate_countPercolate,`countPercolate()`>>
* <<Elasticsearch_Clientpercolate_percolate,`percolate()`>>
* <<Elasticsearch_Clientmpercolate_mpercolate,`mpercolate()`>>
* <<Elasticsearch_Clienttermvectors_termvectors,`termvectors()`>>
* <<Elasticsearch_Clientmtermvectors_mtermvectors,`mtermvectors()`>>
* <<Elasticsearch_Clientexists_exists,`exists()`>>
* <<Elasticsearch_Clientmget_mget,`mget()`>>
* <<Elasticsearch_Clientmsearch_msearch,`msearch()`>>
* <<Elasticsearch_Clientcreate_create,`create()`>>
* <<Elasticsearch_Clientbulk_bulk,`bulk()`>>
* <<Elasticsearch_Clientindex_index,`index()`>>
* <<Elasticsearch_Clientreindex_reindex,`reindex()`>>
* <<Elasticsearch_Clientsuggest_suggest,`suggest()`>>
* <<Elasticsearch_Clientexplain_explain,`explain()`>>
* <<Elasticsearch_Clientsearch_search,`search()`>>
* <<Elasticsearch_ClientsearchShards_searchShards,`searchShards()`>>
* <<Elasticsearch_ClientsearchTemplate_searchTemplate,`searchTemplate()`>>
* <<Elasticsearch_Clientscroll_scroll,`scroll()`>>
* <<Elasticsearch_ClientclearScroll_clearScroll,`clearScroll()`>>
* <<Elasticsearch_Clientupdate_update,`update()`>>
* <<Elasticsearch_ClientgetScript_getScript,`getScript()`>>
* <<Elasticsearch_ClientdeleteScript_deleteScript,`deleteScript()`>>
* <<Elasticsearch_ClientputScript_putScript,`putScript()`>>
* <<Elasticsearch_ClientgetTemplate_getTemplate,`getTemplate()`>>
* <<Elasticsearch_ClientdeleteTemplate_deleteTemplate,`deleteTemplate()`>>
* <<Elasticsearch_ClientputTemplate_putTemplate,`putTemplate()`>>
* <<Elasticsearch_ClientfieldStats_fieldStats,`fieldStats()`>>
* <<Elasticsearch_ClientrenderSearchTemplate_renderSearchTemplate,`renderSearchTemplate()`>>
* <<Elasticsearch_Clientindices_indices,`indices()`>>
* <<Elasticsearch_Clientcluster_cluster,`cluster()`>>
* <<Elasticsearch_Clientnodes_nodes,`nodes()`>>
* <<Elasticsearch_Clientsnapshot_snapshot,`snapshot()`>>
* <<Elasticsearch_Clientcat_cat,`cat()`>>
* <<Elasticsearch_Clientingest_ingest,`ingest()`>>
* <<Elasticsearch_Clienttasks_tasks,`tasks()`>>
* <<Elasticsearch_Client-call-_call,`__call()`>>
* <<Elasticsearch_ClientextractArgument_extractArgument,`extractArgument()`>>




[[Elasticsearch_Clientinfo_info]]
.`info()`
****
[source,php]
----
/*
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->info($params);
----
****



[[Elasticsearch_Clientping_ping]]
.`ping()`
****
[source,php]
----
/*
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->ping($params);
----
****



[[Elasticsearch_Clientget_get]]
.`get()`
****
[source,php]
----
/*
$params['id']              = (string) The document ID (Required)
       ['index']           = (string) The name of the index (Required)
       ['type']            = (string) The type of the document (use `_all` to fetch the first document matching the ID across all types) (Required)
       ['ignore_missing']  = ??
       ['fields']          = (list) A comma-separated list of fields to return in the response
       ['parent']          = (string) The ID of the parent document
       ['preference']      = (string) Specify the node or shard the operation should be performed on (default: random)
       ['realtime']        = (boolean) Specify whether to perform the operation in realtime or search mode
       ['refresh']         = (boolean) Refresh the shard containing the document before performing the operation
       ['routing']         = (string) Specific routing value
       ['_source']         = (list) True or false to return the _source field or not, or a list of fields to return
       ['_source_exclude'] = (list) A list of fields to exclude from the returned _source field
       ['_source_include'] = (list) A list of fields to extract and return from the _source field
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->get($params);
----
****



[[Elasticsearch_ClientgetSource_getSource]]
.`getSource()`
****
[source,php]
----
/*
$params['id']             = (string) The document ID (Required)
       ['index']          = (string) The name of the index (Required)
       ['type']           = (string) The type of the document (use `_all` to fetch the first document matching the ID across all types) (Required)
       ['ignore_missing'] = ??
       ['parent']         = (string) The ID of the parent document
       ['preference']     = (string) Specify the node or shard the operation should be performed on (default: random)
       ['realtime']       = (boolean) Specify whether to perform the operation in realtime or search mode
       ['refresh']        = (boolean) Refresh the shard containing the document before performing the operation
       ['routing']        = (string) Specific routing value
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->getSource($params);
----
****



[[Elasticsearch_Clientdelete_delete]]
.`delete()`
****
[source,php]
----
/*
$params['id']           = (string) The document ID (Required)
       ['index']        = (string) The name of the index (Required)
       ['type']         = (string) The type of the document (Required)
       ['consistency']  = (enum) Specific write consistency setting for the operation
       ['parent']       = (string) ID of parent document
       ['refresh']      = (boolean) Refresh the index after performing the operation
       ['replication']  = (enum) Specific replication type
       ['routing']      = (string) Specific routing value
       ['timeout']      = (time) Explicit operation timeout
       ['version_type'] = (enum) Specific version type
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->delete($params);
----
****



[[Elasticsearch_Clientcount_count]]
.`count()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of indices to restrict the results
       ['type']               = (list) A comma-separated list of types to restrict the results
       ['min_score']          = (number) Include only documents with a specific `_score` value in the result
       ['preference']         = (string) Specify the node or shard the operation should be performed on (default: random)
       ['routing']            = (string) Specific routing value
       ['source']             = (string) The URL-encoded query definition (instead of using the request body)
       ['body']               = (array) A query to restrict the results (optional)
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->count($params);
----
****



[[Elasticsearch_ClientcountPercolate_countPercolate]]
.`countPercolate()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of indices to restrict the results
       ['type']               = (list) A comma-separated list of types to restrict the results
       ['id']                 = (string) ID of document
       ['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['preference']         = (string) Specify the node or shard the operation should be performed on (default: random)
       ['routing']            = (string) Specific routing value
       ['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['body']               = (array) A query to restrict the results (optional)
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['percolate_index']    = (string) The index to count percolate the document into. Defaults to index.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->countPercolate($params);
----
****



[[Elasticsearch_Clientpercolate_percolate]]
.`percolate()`
****
[source,php]
----
/*
$params['index']        = (string) The name of the index with a registered percolator query (Required)
       ['type']         = (string) The document type (Required)
       ['prefer_local'] = (boolean) With `true`, specify that a local shard should be used if available, with `false`, use a random shard (default: true)
       ['body']         = (array) The document (`doc`) to percolate against registered queries; optionally also a `query` to limit the percolation to specific registered queries
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->percolate($params);
----
****



[[Elasticsearch_Clientmpercolate_mpercolate]]
.`mpercolate()`
****
[source,php]
----
/*
$params['index']              = (string) Default index for items which don't provide one
       ['type']               = (string) Default document type for items which don't provide one
       ['ignore_unavailable'] = (boolean) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (boolean) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->mpercolate($params);
----
****



[[Elasticsearch_Clienttermvectors_termvectors]]
.`termvectors()`
****
[source,php]
----
/*
$params['index']            = (string) Default index for items which don't provide one
       ['type']             = (string) Default document type for items which don't provide one
       ['term_statistics']  = (boolean) Specifies if total term frequency and document frequency should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['field_statistics'] = (boolean) Specifies if document count, sum of document frequencies and sum of total term frequencies should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['fields']           = (list) A comma-separated list of fields to return. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['offsets']          = (boolean) Specifies if term offsets should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['positions']        = (boolean) Specifies if term positions should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['payloads']         = (boolean) Specifies if term payloads should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\".
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->termvectors($params);
----
****



[[Elasticsearch_Clientmtermvectors_mtermvectors]]
.`mtermvectors()`
****
[source,php]
----
/*
$params['index']            = (string) Default index for items which don't provide one
       ['type']             = (string) Default document type for items which don't provide one
       ['ids']              = (list) A comma-separated list of documents ids. You must define ids as parameter or set \"ids\" or \"docs\" in the request body
       ['term_statistics']  = (boolean) Specifies if total term frequency and document frequency should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['field_statistics'] = (boolean) Specifies if document count, sum of document frequencies and sum of total term frequencies should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['fields']           = (list) A comma-separated list of fields to return. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['offsets']          = (boolean) Specifies if term offsets should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['positions']        = (boolean) Specifies if term positions should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\"."
       ['payloads']         = (boolean) Specifies if term payloads should be returned. Applies to all returned documents unless otherwise specified in body \"params\" or \"docs\".
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->mtermvectors($params);
----
****



[[Elasticsearch_Clientexists_exists]]
.`exists()`
****
[source,php]
----
/*
$params['id']         = (string) The document ID (Required)
       ['index']      = (string) The name of the index (Required)
       ['type']       = (string) The type of the document (use `_all` to fetch the first document matching the ID across all types) (Required)
       ['parent']     = (string) The ID of the parent document
       ['preference'] = (string) Specify the node or shard the operation should be performed on (default: random)
       ['realtime']   = (boolean) Specify whether to perform the operation in realtime or search mode
       ['refresh']    = (boolean) Refresh the shard containing the document before performing the operation
       ['routing']    = (string) Specific routing value
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->exists($params);
----
****



[[Elasticsearch_Clientmget_mget]]
.`mget()`
****
[source,php]
----
/*
$params['index']           = (string) The name of the index
       ['type']            = (string) The type of the document
       ['fields']          = (list) A comma-separated list of fields to return in the response
       ['parent']          = (string) The ID of the parent document
       ['preference']      = (string) Specify the node or shard the operation should be performed on (default: random)
       ['realtime']        = (boolean) Specify whether to perform the operation in realtime or search mode
       ['refresh']         = (boolean) Refresh the shard containing the document before performing the operation
       ['routing']         = (string) Specific routing value
       ['body']            = (array) Document identifiers; can be either `docs` (containing full document information) or `ids` (when index and type is provided in the URL.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->mget($params);
----
****



[[Elasticsearch_Clientmsearch_msearch]]
.`msearch()`
****
[source,php]
----
/*
$params['index']       = (list) A comma-separated list of index names to use as default
       ['type']        = (list) A comma-separated list of document types to use as default
       ['search_type'] = (enum) Search operation type
       ['body']        = (array|string) The request definitions (metadata-search request definition pairs), separated by newlines
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->msearch($params);
----
****



[[Elasticsearch_Clientcreate_create]]
.`create()`
****
[source,php]
----
/*
$params['index']        = (string) The name of the index (Required)
       ['type']         = (string) The type of the document (Required)
       ['id']           = (string) Specific document ID (when the POST method is used)
       ['consistency']  = (enum) Explicit write consistency setting for the operation
       ['parent']       = (string) ID of the parent document
       ['refresh']      = (boolean) Refresh the index after performing the operation
       ['replication']  = (enum) Specific replication type
       ['routing']      = (string) Specific routing value
       ['timeout']      = (time) Explicit operation timeout
       ['timestamp']    = (time) Explicit timestamp for the document
       ['ttl']          = (duration) Expiration time for the document
       ['version']      = (number) Explicit version number for concurrency control
       ['version_type'] = (enum) Specific version type
       ['body']         = (array) The document
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->create($params);
----
****



[[Elasticsearch_Clientbulk_bulk]]
.`bulk()`
****
[source,php]
----
/*
$params['index']       = (string) Default index for items which don't provide one
       ['type']        = (string) Default document type for items which don't provide one
       ['consistency'] = (enum) Explicit write consistency setting for the operation
       ['refresh']     = (boolean) Refresh the index after performing the operation
       ['replication'] = (enum) Explicitly set the replication type
       ['fields']      = (list) Default comma-separated list of fields to return in the response for updates
       ['body']        = (array) The document
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->bulk($params);
----
****



[[Elasticsearch_Clientindex_index]]
.`index()`
****
[source,php]
----
/*
$params['index']        = (string) The name of the index (Required)
       ['type']         = (string) The type of the document (Required)
       ['id']           = (string) Specific document ID (when the POST method is used)
       ['consistency']  = (enum) Explicit write consistency setting for the operation
       ['op_type']      = (enum) Explicit operation type
       ['parent']       = (string) ID of the parent document
       ['refresh']      = (boolean) Refresh the index after performing the operation
       ['replication']  = (enum) Specific replication type
       ['routing']      = (string) Specific routing value
       ['timeout']      = (time) Explicit operation timeout
       ['timestamp']    = (time) Explicit timestamp for the document
       ['ttl']          = (duration) Expiration time for the document
       ['version']      = (number) Explicit version number for concurrency control
       ['version_type'] = (enum) Specific version type
       ['body']         = (array) The document
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->index($params);
----
****



[[Elasticsearch_Clientreindex_reindex]]
.`reindex()`
****
[source,php]
----
/*
$params['refresh']             = (boolean) Should the effected indexes be refreshed?
       ['timeout']             = (time) Time each individual bulk request should wait for shards that are unavailable
       ['consistency']         = (enum) Explicit write consistency setting for the operation
       ['wait_for_completion'] = (boolean) Should the request should block until the reindex is complete
       ['requests_per_second'] = (float) The throttle for this request in sub-requests per second. 0 means set no throttle
       ['body']                = (array) The search definition using the Query DSL and the prototype for the index request (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->reindex($params);
----
****



[[Elasticsearch_Clientsuggest_suggest]]
.`suggest()`
****
[source,php]
----
/*
$params['index']          = (list) A comma-separated list of index names to restrict the operation; use `_all` or empty string to perform the operation on all indices
       ['ignore_indices'] = (enum) When performed on multiple indices, allows to ignore `missing` ones
       ['preference']     = (string) Specify the node or shard the operation should be performed on (default: random)
       ['routing']        = (string) Specific routing value
       ['source']         = (string) The URL-encoded request definition (instead of using request body)
       ['body']           = (array) The request definition
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->suggest($params);
----
****



[[Elasticsearch_Clientexplain_explain]]
.`explain()`
****
[source,php]
----
/*
$params['id']                       = (string) The document ID (Required)
       ['index']                    = (string) The name of the index (Required)
       ['type']                     = (string) The type of the document (Required)
       ['analyze_wildcard']         = (boolean) Specify whether wildcards and prefix queries in the query string query should be analyzed (default: false)
       ['analyzer']                 = (string) The analyzer for the query string query
       ['default_operator']         = (enum) The default operator for query string query (AND or OR)
       ['df']                       = (string) The default field for query string query (default: _all)
       ['fields']                   = (list) A comma-separated list of fields to return in the response
       ['lenient']                  = (boolean) Specify whether format-based query failures (such as providing text to a numeric field) should be ignored
       ['lowercase_expanded_terms'] = (boolean) Specify whether query terms should be lowercased
       ['parent']                   = (string) The ID of the parent document
       ['preference']               = (string) Specify the node or shard the operation should be performed on (default: random)
       ['q']                        = (string) Query in the Lucene query string syntax
       ['routing']                  = (string) Specific routing value
       ['source']                   = (string) The URL-encoded query definition (instead of using the request body)
       ['_source']                  = (list) True or false to return the _source field or not, or a list of fields to return
       ['_source_exclude']          = (list) A list of fields to exclude from the returned _source field
       ['_source_include']          = (list) A list of fields to extract and return from the _source field
       ['body']                     = (string) The URL-encoded query definition (instead of using the request body)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->explain($params);
----
****



[[Elasticsearch_Clientsearch_search]]
.`search()`
****
[source,php]
----
/*
$params['index']                    = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices
       ['type']                     = (list) A comma-separated list of document types to search; leave empty to perform the operation on all types
       ['analyzer']                 = (string) The analyzer to use for the query string
       ['analyze_wildcard']         = (boolean) Specify whether wildcard and prefix queries should be analyzed (default: false)
       ['default_operator']         = (enum) The default operator for query string query (AND or OR)
       ['df']                       = (string) The field to use as default where no field prefix is given in the query string
       ['explain']                  = (boolean) Specify whether to return detailed information about score computation as part of a hit
       ['fields']                   = (list) A comma-separated list of fields to return as part of a hit
       ['from']                     = (number) Starting offset (default: 0)
       ['ignore_indices']           = (enum) When performed on multiple indices, allows to ignore `missing` ones
       ['indices_boost']            = (list) Comma-separated list of index boosts
       ['lenient']                  = (boolean) Specify whether format-based query failures (such as providing text to a numeric field) should be ignored
       ['lowercase_expanded_terms'] = (boolean) Specify whether query terms should be lowercased
       ['preference']               = (string) Specify the node or shard the operation should be performed on (default: random)
       ['q']                        = (string) Query in the Lucene query string syntax
       ['query_cache']              = (boolean) Enable query cache for this request
       ['request_cache']            = (boolean) Enable request cache for this request
       ['routing']                  = (list) A comma-separated list of specific routing values
       ['scroll']                   = (duration) Specify how long a consistent view of the index should be maintained for scrolled search
       ['search_type']              = (enum) Search operation type
       ['size']                     = (number) Number of hits to return (default: 10)
       ['sort']                     = (list) A comma-separated list of <field>:<direction> pairs
       ['source']                   = (string) The URL-encoded request definition using the Query DSL (instead of using request body)
       ['_source']                  = (list) True or false to return the _source field or not, or a list of fields to return
       ['_source_exclude']          = (list) A list of fields to exclude from the returned _source field
       ['_source_include']          = (list) A list of fields to extract and return from the _source field
       ['stats']                    = (list) Specific 'tag' of the request for logging and statistical purposes
       ['suggest_field']            = (string) Specify which field to use for suggestions
       ['suggest_mode']             = (enum) Specify suggest mode
       ['suggest_size']             = (number) How many suggestions to return in response
       ['suggest_text']             = (text) The source text for which the suggestions should be returned
       ['timeout']                  = (time) Explicit operation timeout
       ['version']                  = (boolean) Specify whether to return document version as part of a hit
       ['body']                     = (array|string) The search definition using the Query DSL
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->search($params);
----
****



[[Elasticsearch_ClientsearchShards_searchShards]]
.`searchShards()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices
       ['type']               = (list) A comma-separated list of document types to search; leave empty to perform the operation on all types
       ['preference']         = (string) Specify the node or shard the operation should be performed on (default: random)
       ['routing']            = (string) Specific routing value
       ['local']              = (bool) Return local information, do not retrieve the state from master node (default: false)
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->searchShards($params);
----
****



[[Elasticsearch_ClientsearchTemplate_searchTemplate]]
.`searchTemplate()`
****
[source,php]
----
/*
$params['index']                    = (list) A comma-separated list of index names to search; use `_all` or empty string to perform the operation on all indices
       ['type']                     = (list) A comma-separated list of document types to search; leave empty to perform the operation on all types
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->searchTemplate($params);
----
****



[[Elasticsearch_Clientscroll_scroll]]
.`scroll()`
****
[source,php]
----
/*
$params['scroll_id'] = (string) The scroll ID for scrolled search
       ['scroll']    = (duration) Specify how long a consistent view of the index should be maintained for scrolled search
       ['body']      = (string) The scroll ID for scrolled search
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->scroll($params);
----
****



[[Elasticsearch_ClientclearScroll_clearScroll]]
.`clearScroll()`
****
[source,php]
----
/*
$params['scroll_id'] = (string) The scroll ID for scrolled search
       ['scroll']    = (duration) Specify how long a consistent view of the index should be maintained for scrolled search
       ['body']      = (string) The scroll ID for scrolled search
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->clearScroll($params);
----
****



[[Elasticsearch_Clientupdate_update]]
.`update()`
****
[source,php]
----
/*
$params['id']                = (string) Document ID (Required)
       ['index']             = (string) The name of the index (Required)
       ['type']              = (string) The type of the document (Required)
       ['consistency']       = (enum) Explicit write consistency setting for the operation
       ['fields']            = (list) A comma-separated list of fields to return in the response
       ['lang']              = (string) The script language (default: mvel)
       ['parent']            = (string) ID of the parent document
       ['refresh']           = (boolean) Refresh the index after performing the operation
       ['replication']       = (enum) Specific replication type
       ['retry_on_conflict'] = (number) Specify how many times should the operation be retried when a conflict occurs (default: 0)
       ['routing']           = (string) Specific routing value
       ['script']            = () The URL-encoded script definition (instead of using request body)
       ['timeout']           = (time) Explicit operation timeout
       ['timestamp']         = (time) Explicit timestamp for the document
       ['ttl']               = (duration) Expiration time for the document
       ['version_type']      = (number) Explicit version number for concurrency control
       ['body']              = (array) The request definition using either `script` or partial `doc`
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->update($params);
----
****



[[Elasticsearch_ClientgetScript_getScript]]
.`getScript()`
****
[source,php]
----
/*
$params['id']   = (string) The script ID (Required)
       ['lang'] = (string) The script language (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->getScript($params);
----
****



[[Elasticsearch_ClientdeleteScript_deleteScript]]
.`deleteScript()`
****
[source,php]
----
/*
$params['id']   = (string) The script ID (Required)
       ['lang'] = (string) The script language (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->deleteScript($params);
----
****



[[Elasticsearch_ClientputScript_putScript]]
.`putScript()`
****
[source,php]
----
/*
$params['id']   = (string) The script ID (Required)
       ['lang'] = (string) The script language (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->putScript($params);
----
****



[[Elasticsearch_ClientgetTemplate_getTemplate]]
.`getTemplate()`
****
[source,php]
----
/*
$params['id']   = (string) The search template ID (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->getTemplate($params);
----
****



[[Elasticsearch_ClientdeleteTemplate_deleteTemplate]]
.`deleteTemplate()`
****
[source,php]
----
/*
$params['id']   = (string) The search template ID (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->deleteTemplate($params);
----
****



[[Elasticsearch_ClientputTemplate_putTemplate]]
.`putTemplate()`
****
[source,php]
----
/*
$params['id']   = (string) The search template ID (Required)
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->putTemplate($params);
----
****



[[Elasticsearch_ClientfieldStats_fieldStats]]
.`fieldStats()`
****
[source,php]
----
/*
$params['index']              = (list) A comma-separated list of indices to restrict the results
       ['fields']             = (list) A comma-separated list of fields for to get field statistics for (min value, max value, and more)
       ['level']              = (enum) Defines if field stats should be returned on a per index level or on a cluster wide level
       ['ignore_unavailable'] = (bool) Whether specified concrete indices should be ignored when unavailable (missing or closed)
       ['allow_no_indices']   = (bool) Whether to ignore if a wildcard indices expression resolves into no concrete indices. (This includes `_all` string or when no indices have been specified)
       ['expand_wildcards']   = (enum) Whether to expand wildcard expression to concrete indices that are open, closed or both.
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->fieldStats($params);
----
****



[[Elasticsearch_ClientrenderSearchTemplate_renderSearchTemplate]]
.`renderSearchTemplate()`
****
[source,php]
----
/*
$params['id']                 = (string) ID of the template to render
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->renderSearchTemplate($params);
----
****



[[Elasticsearch_Clientindices_indices]]
.`indices()`
****
[source,php]
----
/*
Operate on the Indices Namespace of commands
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->indices();
----
****



[[Elasticsearch_Clientcluster_cluster]]
.`cluster()`
****
[source,php]
----
/*
Operate on the Cluster namespace of commands
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cluster();
----
****



[[Elasticsearch_Clientnodes_nodes]]
.`nodes()`
****
[source,php]
----
/*
Operate on the Nodes namespace of commands
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->nodes();
----
****



[[Elasticsearch_Clientsnapshot_snapshot]]
.`snapshot()`
****
[source,php]
----
/*
Operate on the Snapshot namespace of commands
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->snapshot();
----
****



[[Elasticsearch_Clientcat_cat]]
.`cat()`
****
[source,php]
----
/*
Operate on the Cat namespace of commands
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->cat();
----
****



[[Elasticsearch_Clientingest_ingest]]
.`ingest()`
****
[source,php]
----
/*
Operate on the Ingest namespace of commands
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->ingest();
----
****



[[Elasticsearch_Clienttasks_tasks]]
.`tasks()`
****
[source,php]
----
/*
Operate on the Tasks namespace of commands
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->tasks();
----
****



[[Elasticsearch_Client-call-_call]]
.`__call()`
****
[source,php]
----
/*
Catchall for registered namespaces
       ['body']  = (array) Request body
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->__call($name,$arguments);
----
****



[[Elasticsearch_ClientextractArgument_extractArgument]]
.`extractArgument()`
****
[source,php]
----
/*
*/

$params = [
    // ...
];

$client = ClientBuilder::create()->build();
$response = $client->extractArgument($params,$arg);
----
****



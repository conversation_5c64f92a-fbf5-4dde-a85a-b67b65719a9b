

[[Elasticsearch_ClientBuilder]]
=== Elasticsearch\ClientBuilder



Class ClientBuilder


*Methods*

The class defines the following methods:

* <<Elasticsearch_ClientBuildercreate_create,`create()`>>
* <<Elasticsearch_ClientBuilderfromConfig_fromConfig,`fromConfig()`>>
* <<Elasticsearch_ClientBuilderdefaultHandler_defaultHandler,`defaultHandler()`>>
* <<Elasticsearch_ClientBuildermultiHandler_multiHandler,`multiHandler()`>>
* <<Elasticsearch_ClientBuildersingleHandler_singleHandler,`singleHandler()`>>
* <<Elasticsearch_ClientBuilderdefaultLogger_defaultLogger,`defaultLogger()`>>
* <<Elasticsearch_ClientBuildersetConnectionFactory_setConnectionFactory,`setConnectionFactory()`>>
* <<Elasticsearch_ClientBuildersetConnectionPool_setConnectionPool,`setConnectionPool()`>>
* <<Elasticsearch_ClientBuildersetEndpoint_setEndpoint,`setEndpoint()`>>
* <<Elasticsearch_ClientBuilderregisterNamespace_registerNamespace,`registerNamespace()`>>
* <<Elasticsearch_ClientBuildersetTransport_setTransport,`setTransport()`>>
* <<Elasticsearch_ClientBuildersetHandler_setHandler,`setHandler()`>>
* <<Elasticsearch_ClientBuildersetLogger_setLogger,`setLogger()`>>
* <<Elasticsearch_ClientBuildersetTracer_setTracer,`setTracer()`>>
* <<Elasticsearch_ClientBuildersetSerializer_setSerializer,`setSerializer()`>>
* <<Elasticsearch_ClientBuildersetHosts_setHosts,`setHosts()`>>
* <<Elasticsearch_ClientBuildersetRetries_setRetries,`setRetries()`>>
* <<Elasticsearch_ClientBuildersetSelector_setSelector,`setSelector()`>>
* <<Elasticsearch_ClientBuildersetSniffOnStart_setSniffOnStart,`setSniffOnStart()`>>
* <<Elasticsearch_ClientBuildersetSSLCert_setSSLCert,`setSSLCert()`>>
* <<Elasticsearch_ClientBuildersetSSLKey_setSSLKey,`setSSLKey()`>>
* <<Elasticsearch_ClientBuildersetSSLVerification_setSSLVerification,`setSSLVerification()`>>
* <<Elasticsearch_ClientBuilderbuild_build,`build()`>>



[[Elasticsearch_ClientBuildercreate_create]]
.`create()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuilderfromConfig_fromConfig]]
.`fromConfig()`
****
[source,php]
----
/*
    Build a new client from the provided config.  Hash keys
should correspond to the method name e.g. ['connectionPool']
corresponds to setConnectionPool().
    ['body']  = (array) Request body
*/

----
****



[[Elasticsearch_ClientBuilderdefaultHandler_defaultHandler]]
.`defaultHandler()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildermultiHandler_multiHandler]]
.`multiHandler()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersingleHandler_singleHandler]]
.`singleHandler()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuilderdefaultLogger_defaultLogger]]
.`defaultLogger()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetConnectionFactory_setConnectionFactory]]
.`setConnectionFactory()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetConnectionPool_setConnectionPool]]
.`setConnectionPool()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetEndpoint_setEndpoint]]
.`setEndpoint()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuilderregisterNamespace_registerNamespace]]
.`registerNamespace()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetTransport_setTransport]]
.`setTransport()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetHandler_setHandler]]
.`setHandler()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetLogger_setLogger]]
.`setLogger()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetTracer_setTracer]]
.`setTracer()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetSerializer_setSerializer]]
.`setSerializer()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetHosts_setHosts]]
.`setHosts()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetRetries_setRetries]]
.`setRetries()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetSelector_setSelector]]
.`setSelector()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetSniffOnStart_setSniffOnStart]]
.`setSniffOnStart()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetSSLCert_setSSLCert]]
.`setSSLCert()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetSSLKey_setSSLKey]]
.`setSSLKey()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuildersetSSLVerification_setSSLVerification]]
.`setSSLVerification()`
****
[source,php]
----
/*
*/

----
****



[[Elasticsearch_ClientBuilderbuild_build]]
.`build()`
****
[source,php]
----
/*
*/

----
****



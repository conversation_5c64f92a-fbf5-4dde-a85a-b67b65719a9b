
[[ElasticsearchPHP_Endpoints]]
== Reference - Endpoints

This is a complete list of namespaces and their associated endpoints.

NOTE: This is auto-generated documentation

* <<Elasticsearch_Client, Elasticsearch\Client>>
* <<Elasticsearch_ClientBuilder, Elasticsearch\ClientBuilder>>
* <<Elasticsearch_Namespaces_CatNamespace, Elasticsearch\Namespaces\CatNamespace>>
* <<Elasticsearch_Namespaces_ClusterNamespace, Elasticsearch\Namespaces\ClusterNamespace>>
* <<Elasticsearch_Namespaces_IndicesNamespace, Elasticsearch\Namespaces\IndicesNamespace>>
* <<Elasticsearch_Namespaces_IngestNamespace, Elasticsearch\Namespaces\IngestNamespace>>
* <<Elasticsearch_Namespaces_NodesNamespace, Elasticsearch\Namespaces\NodesNamespace>>
* <<Elasticsearch_Namespaces_SnapshotNamespace, Elasticsearch\Namespaces\SnapshotNamespace>>
* <<Elasticsearch_Namespaces_TasksNamespace, Elasticsearch\Namespaces\TasksNamespace>>
include::Elasticsearch/Client.asciidoc[]
include::Elasticsearch/ClientBuilder.asciidoc[]
include::Elasticsearch/Namespaces/CatNamespace.asciidoc[]
include::Elasticsearch/Namespaces/ClusterNamespace.asciidoc[]
include::Elasticsearch/Namespaces/IndicesNamespace.asciidoc[]
include::Elasticsearch/Namespaces/IngestNamespace.asciidoc[]
include::Elasticsearch/Namespaces/NodesNamespace.asciidoc[]
include::Elasticsearch/Namespaces/SnapshotNamespace.asciidoc[]
include::Elasticsearch/Namespaces/TasksNamespace.asciidoc[]

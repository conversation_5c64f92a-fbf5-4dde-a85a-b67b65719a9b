== Breaking changes from 2.x

- Indices/Analyze Endpoint: `filters` and `char_filters` URI parameters have been renamed to `filter` and `char_filter` respectively
- SearchExists endpoint has been removed (use `size=0` and `terminate_after=1` instead)
- Warmers have been removed because they are no longer useful
- Indices/Optimize Endpoint has been removed (use `_forcemerge` instead)
- MoreLikeThis (MLT) endpoint has been removed
- DeleteByQuery endpoint has been removed.
- Tasks/List and Tasks/Get are now separate endpoints (see: link:http://github.com/elasticsearch/elasticsearch-php/commit/752d5a2[e0cc5f9])
- Client requires PHP 5.6.6 or higher

=== Deprecations

- Percolator endpoints are deprecated and will be removed in Elasticsearch 6.0

=== Internal BWC Breaks

- Namespace injection has changed slightly.  If you use custom namespaces, you'll need to update your code (see: Add better ability to inject namespaces link:http://github.com/elasticsearch/elasticsearch-php/commit/b1a27b7[b1a27b7])
- Endpoints no longer use the Transport directly.  If you use custom endpoints, you'll need to do some minor
refactoring (see: Refactor to remove Transport dependence in endpoints link:http://github.com/elasticsearch/elasticsearch-php/commit/ecd454c[ecd454c])
- To facilitate testing and other features, the `ConnectionInterface` has expanded to obtain some more methods (link:http://github.com/elasticsearch/elasticsearch-php/commit/8bcf1a8[getPath()], link:http://github.com/elasticsearch/elasticsearch-php/commit/586fbdb[getUserPass()], link:http://github.com/elasticsearch/elasticsearch-php/commit/445fdea[getHost()])

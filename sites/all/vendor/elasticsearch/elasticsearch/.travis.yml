language: php

jdk:
  - oraclejdk8

addons:
  apt:
    packages:
      - oracle-java8-installer

branches:
  except:
    - 0.4

sudo: true

matrix:
  fast_finish: true
  include:
    - php: 5.6
      env: ES_VERSION="5.0" TEST_BUILD_REF="origin/5.0"
    - php: 5.6
      env: ES_VERSION="5.5" TEST_BUILD_REF="origin/5.5"

    - php: 7.0
      env: ES_VERSION="5.0" TEST_BUILD_REF="origin/5.0"
    - php: 7.0
      env: ES_VERSION="5.5" TEST_BUILD_REF="origin/5.5"

    - php: 7.1
      env: ES_VERSION="5.0" TEST_BUILD_REF="origin/5.0"
    - php: 7.1
      env: ES_VERSION="5.5" TEST_BUILD_REF="origin/5.5"

    - php: 7.2
      env: ES_VERSION="5.0" TEST_BUILD_REF="origin/5.0"
    - php: 7.2
      env: ES_VERSION="5.5" TEST_BUILD_REF="origin/5.5"

    - php: 7.3
      env: ES_VERSION="5.0" TEST_BUILD_REF="origin/5.0"
    - php: 7.3
      env: ES_VERSION="5.5" TEST_BUILD_REF="origin/5.5"

env:
  global:
    - ES_TEST_HOST=http://localhost:9200
    - JAVA_HOME="/usr/lib/jvm/java-8-oracle/jre"

before_install:
  - sudo update-java-alternatives -s java-8-oracle
  - ./travis/download_and_run_es.sh

install:
  - composer install --prefer-dist

before_script:
  - if [ $TRAVIS_PHP_VERSION = '7.3' ]; then PHPUNIT_FLAGS="--coverage-clover ./build/logs/clover.xml"; fi
  - php util/RestSpecRunner.php
  - php util/EnsureClusterAlive.php

script:
  - vendor/bin/phpunit $PHPUNIT_FLAGS
  - vendor/bin/phpunit -c phpunit-integration.xml --group sync $PHPUNIT_FLAGS

after_script:
  - if [ $TRAVIS_PHP_VERSION = '7.3' ]; then php vendor/bin/coveralls; fi

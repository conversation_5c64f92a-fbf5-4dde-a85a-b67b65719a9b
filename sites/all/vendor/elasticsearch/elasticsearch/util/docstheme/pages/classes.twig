{% extends "layout/base.twig" %}

{% from "macros.twig" import class_item, replace_backslash, back_to_forward, sanitize %}

{% block title %}
[[{{ sanitize(replace_backslash("ElasticsearchPHP_Endpoints")) }}]]
== {{ "Reference - Endpoints" }}
{% endblock %}

{% block content %}

This is a complete list of namespaces and their associated endpoints.

NOTE: This is auto-generated documentation

{% for class in classes if not class.interface %}
* <<{{ replace_backslash(class) }}, {{ class }}>>
{% else %}
* There are no endpoints available.
{% endfor %}
{% for class in classes if not class.interface %}
include::{{ back_to_forward(class) }}.asciidoc[]
{% else %}
{% endfor %}
{% endblock %}



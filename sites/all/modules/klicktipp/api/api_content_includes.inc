<?php

use App\Klicktipp\Amember;
use App\Klicktipp\Core;
use App\Klicktipp\Errors;
use App\Klicktipp\Libraries;
use App\Klicktipp\Plugin;
use App\Klicktipp\Subaccount;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;
use App\Klicktipp\ToolWebinar;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\VarDataProcessingOrder;
use App\Klicktipp\VarPluginData;

/**
 * @phpstan-import-type PaymentArray from Amember
 *
 * Utility functions for content include files
 *
 * Functions:
 *
 * -- string ciapi_base64EncodeArray ( $Array ): encode an array with json_encode and base64_encode
 * -- array ciapi_base64DecodeArray ( $base64 ): decode an base64_encoded JSON string back to an array
 * -- array ciapi_get_webinar_partner_invitation_dates (): calculate dates for affiliates when to send webinar invitation newsletter TODO: use GetNExtWebinars()
 * -- array ciapi_get_product_info_by_groupid ( $GroupID): Get product info by GroupID to display on upgrade page
 * -- array ciapi_get_current_user_product ($account): Get current product of a user with possible upgrade products
 * -- bool ciapi_user_has_product ($UserID, $aMemberProductID, $CheckExpired): Check if a user has a certain product by aMemberProductID
 * -- string ciapi_generate_partner_pdf ( $amember_id, $content, $pdf_filename ): Create an PDF from partner programm content (HTML) and upload it to S3
 * -- string ciapi_node_get_title ( $nid = 0 ): Get the title of a specific node, default: current node title
 * * -- misc ciapi_klicktipp_get_setting( $key ): get a klciktipp setting with default (use instead of variable_get())
 */

/**
 * Encode an array with json_encode, then encode the JSON string with base64_encode
 * @param array $Array : the array to be encoded
 * @return string base64 encoded string
 */
function ciapi_base64EncodeArray($Array) {

  $Array = (empty($Array)) ? array() : $Array;

  $Array['hash'] = MinihashCreate(json_encode($Array));

  return trim(base64_encode(json_encode($Array)), '='); //remove base64 padding ('=') which is not needed for decoding in PHP

}

/**
 * Decode an base64_encoded JSON string back to an array
 * @param string $base64 : base64_encoded JSON string
 * @return array decoded array
 */
function ciapi_base64DecodeArray($base64) {

  if (empty($base64))
    return array();

  $decoded = json_decode(base64_decode($base64), TRUE);
  $hash = $decoded['hash'];
  unset($decoded['hash']);

  return (!MinihashCheck($hash, json_encode($decoded))) ? array() : $decoded;

}

/**
 * Calculate dates for affiliates when to send webinar invitation newsletter
 * TODO: use GetNExtWebinars()
 */
function ciapi_get_webinar_partner_invitation_dates() {

  $week_day = date('N');

  $next = 0;
  $send_first = 0;
  $send_second = 0;

  switch ($week_day) {
    case 1: //Montag
      $next = strtotime("next thursday");
      $send_first = strtotime("last tuesday", $next);
      $send_second = strtotime("last wednesday", $next);
      break;
    case 3: //Mittwoch
    case 4: //Donnerstag
    case 5: //Freitag
    case 6: //Samstag
      $next = strtotime("next tuesday");
      $send_first = strtotime("last sunday", $next);
      $send_second = strtotime("last monday", $next);
      break;
    case 2: //Dienstag
      $hour = date('H');
      if ($hour <= 12) {
        $next = strtotime("next thursday");
        $send_first = strtotime("last tuesday", $next);
        $send_second = strtotime("last wednesday", $next);
      } else {
        $next = strtotime("next tuesday");
        $send_first = strtotime("last sunday", $next);
        $send_second = strtotime("last monday", $next);
      }
      break;
    case 7: //Sonntag
      $hour = date('H');
      if ($hour <= 12) {
        $next = strtotime("next tuesday");
        $send_first = strtotime("last sunday", $next);
        $send_second = strtotime("last monday", $next);
      } else {
        $next = strtotime("next thursday");
        $send_first = strtotime("last tuesday", $next);
        $send_second = strtotime("last wednesday", $next);
      }
      break;
  }

  //for translation script
  //t('Monday'), t('Tuesday'), t('Wednesday'), t('Thursday'), t('Friday'), t('Saturday'), t('Sunday')
  //t('January'), t('February'), t('March'), t('April'), t('May'), t('June'), t('July'), t('August'), t('September'), t('October'), t('November'), t('December')
  $dates = array(
    'NextWebinarWeekDay' => t(date('l', $next)), //translated weekday
    'NextWebinarDate' => date('d. ', $next) . t(date('F', $next)) . date(" Y", $next), //example: 01. January 1970 (translated month)
    'FirstInvitationWeekDay' => t(date('l', $send_first)),
    'FirstInvitationDate' => date('d. ', $send_first) . t(date('F', $send_first)) . date(" Y", $send_first),
    'SecondInvitationWeekDay' => t(date('l', $send_second)),
    'SecondInvitationDate' => date('d. ', $send_second) . t(date('F', $send_second)) . date(" Y", $send_second),
  );

  return $dates;

}

/**
 * Return the rendered webinar registration form
 * @param $UserID
 * @param $ToolID
 */
function ciapi_get_webinar_signin_form($UserID, $ToolID) {

  $ObjectWebinar = ToolWebinar::FromID($UserID, $ToolID);
  if (empty($ObjectWebinar)) {
    //dummy form for development

    $SignInForm = '<style> .webinar-select {display:none;}</style>' .
      '<form target="_blank" class="ui-form invitation-list" action="/frontpage" method="post" id="klicktipp-tools-webinar-signin-form" accept-charset="UTF-8"><div><input class="edit-WebinarRegisterDate" type="hidden" name="WebinarRegisterDate" value="1527766200">' .
      '<script type="text/javascript">' .
      'window[\'WebinarDateOptions\'] = [{"value":1527766200,"date":"31.05.2018","duration":"13:30 - 16:30 Uhr","seats":"noch <span class=\'places-number\'>6<\/span> Pl\u00e4tze verf\u00fcgbar","selected":true,"inactive":false},{"value":1527930000,"date":"02.06.2018","duration":"11:00 - 14:00 Uhr","seats":"noch <span class=\'places-number\'>106<\/span> Pl\u00e4tze verf\u00fcgbar","selected":false,"inactive":false},{"value":1528185600,"date":"05.06.2018","duration":"10:00 - 13:00 Uhr","seats":"noch <span class=\'places-number\'>232<\/span> Pl\u00e4tze verf\u00fcgbar","selected":false,"inactive":false},{"value":1528218000,"date":"05.06.2018","duration":"19:00 - 22:00 Uhr","seats":"noch <span class=\'places-number\'>246<\/span> Pl\u00e4tze verf\u00fcgbar","selected":false,"inactive":false}];' .
      '</script>' .
      '<input class="edit-form-build-id" type="hidden" name="form_build_id" value="form-MV770piKiC3KGsF1RU3uuKHan5MWfsLySALWuW86tBs">' .
      '<input type="hidden" name="form_token" value="G8WOrR3t862bYnNfalAEG2j2BNkRgFbwSlDhUsy7Yug">' .
      '<input class="edit-form-id" type="hidden" name="form_id" value="klicktipp_tools_webinar_signin_form">' .
      '<div id="edit-webinarregisteremail-wrapper" class="form-group form-item form-type-textfield form-item-WebinarRegisterEmail edit-WebinarRegisterEmail-wrapper">' .
      '<input class="webinar-optin-email auto-focus edit-WebinarRegisterEmail form-control form-text" placeholder="Ihre E-Mail-Adresse" type="text" id="edit-webinarregisteremail" name="WebinarRegisterEmail" value="" size="60" maxlength="128">' .
      '</div>' .
      '<div class="submit-info"><input class="webinar-btn-register btn-eintragen button-submit yellow btn btn-submit btn-icon-arrow-white btn-green form-submit" type="submit" id="edit-submit" name="op" value="In die Gästeliste eintragen"></div></div></form>';

  }
  else {

    Libraries::include('tools_webinar.inc', '/forms');
    $account = user_load($UserID);

    $aMemberID = 0;
    $PrefillEmailAddress = '';
    if ( !empty($_GET['phash']) ) {
      // --- decode affiliate link params ---

      $phash = check_plain($_GET['phash']);

      //check if an email address is given via phash, preset it in the email textfield @see splittest-club goto.ph
      $params = (empty($phash)) ? array() : ciapi_base64DecodeArray($phash);
      $PrefillEmailAddress = (empty($params['goto_email'])) ? '' : check_plain($params['goto_email']);

      //include Affiliate information in the subscription form to store in CustomFields, used for special offer order links @see webinar_offer.inc
      $aMemberID = (empty($params['goto_id'])) ? 0 : $params['goto_id'];

    }

    if ( empty($PrefillEmailAddress && !empty($_GET['email'])) ) {
      $PrefillEmailAddress = check_plain($_GET['email']);
    }

    $SignInForm = drupal_get_form('klicktipp_tools_webinar_signin_form', $account, $ToolID, $PrefillEmailAddress, $aMemberID);
    $SignInForm = '<style> .webinar-select {display:none;}</style>' . render($SignInForm);

  }

  drupal_add_js("/content_includes/js/webinar/ccp/webinar.js", array(
    'type' => 'file',
    'scope' => 'header',
    'weight' => 1000,
    'preprocess' => FALSE,
  ));

  echo $SignInForm;

}

/**
 * get the customer lifetime value of a product by DigiStore ProductID
 * @param $ProductID
 * @return float
 */
function ciapi_get_product_customer_lifetime_value($ProductID) {
  $ProductInfo = ciapi_get_product_info_by_digistoreid($ProductID);
  return (empty($ProductInfo['lifetimeValue'])) ? 0 : $ProductInfo['lifetimeValue'];
}

/**
 * get product info by DigiStore ProductID
 * @param $ProductID
 * @return array
 */
function ciapi_get_product_info_by_digistoreid($ProductID) {

  $ProductInfo = array(
    '36467' => array(
      'productName' => 'Klick-Tipp Standard 10.000',
      'amemberID' => 101,
      'lifetimeValue' => 42.79,
    ),
    '38479' => array(
      'productName' => 'Klick-Tipp Standard 10.000, Webinar-Sonderangebot',
      'amemberID' => 140,
      'lifetimeValue' => 42.79,
    ),
    '36469' => array(
      'productName' => 'Klick-Tipp Standard 10.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 102,
      'lifetimeValue' => 42.79,
    ),
    '36471' => array(
      'productName' => 'Klick-Tipp Standard 10.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 103,
      'lifetimeValue' => 42.79,
    ),
    '36473' => array(
      'productName' => 'Klick-Tipp Premium 10.000',
      'amemberID' => 104,
      'lifetimeValue' => 65.24,
    ),
    '38481' => array(
      'productName' => 'Klick-Tipp Premium 10.000, Webinar-Sonderangebot',
      'amemberID' => 139,
      'lifetimeValue' => 65.24,
    ),
    '36475' => array(
      'productName' => 'Klick-Tipp Premium 10.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 105,
      'lifetimeValue' => 65.24,
    ),
    '36477' => array(
      'productName' => 'Klick-Tipp Premium 10.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 106,
      'lifetimeValue' => 65.24,
    ),
    '36479' => array(
      'productName' => 'Klick-Tipp Deluxe 10.000',
      'amemberID' => 107,
      'lifetimeValue' => 69.63,
    ),
    '38483' => array(
      'productName' => 'Klick-Tipp Deluxe 10.000, Webinar-Sonderangebot',
      'amemberID' => 138,
      'lifetimeValue' => 69.63,
    ),
    '36481' => array(
      'productName' => 'Klick-Tipp Deluxe 10.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 108,
      'lifetimeValue' => 69.63,
    ),
    '36483' => array(
      'productName' => 'Klick-Tipp Deluxe 10.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 109,
      'lifetimeValue' => 69.63,
    ),
    '36485' => array(
      'productName' => 'Klick-Tipp Enterprise 10.000',
      'amemberID' => 110,
      'lifetimeValue' => 387.73,
    ),
    '54847' => array(
      'productName' => 'Klick-Tipp Enterprise 10.000, Webinar-Sonderangebot',
      'amemberID' => 141,
      'lifetimeValue' => 387.73,
    ),
    '36487' => array(
      'productName' => 'Klick-Tipp Enterprise 10.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 111,
      'lifetimeValue' => 387.73,
    ),
    '36489' => array(
      'productName' => 'Klick-Tipp Enterprise 10.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 112,
      'lifetimeValue' => 387.73,
    ),
    '36491' => array(
      'productName' => 'Klick-Tipp Enterprise 10.000',
      'amemberID' => 113,
      'lifetimeValue' => 490.32,
    ),
    '37079' => array(
      'productName' => 'Klick-Tipp Enterprise 10.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 114,
      'lifetimeValue' => 490.32,
    ),
    '37081' => array(
      'productName' => 'Klick-Tipp Enterprise 10.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 115,
      'lifetimeValue' => 490.32,
    ),
    '37083' => array(
      'productName' => 'Klick-Tipp Enterprise 25.000',
      'amemberID' => 116,
      'lifetimeValue' => 827.16,
    ),
    '54849' => array(
      'productName' => 'Klick-Tipp Enterprise 25.000, Webinar-Sonderangebot',
      'amemberID' => 142,
      'lifetimeValue' => 827.16,
    ),
    '37085' => array(
      'productName' => 'Klick-Tipp Enterprise 25.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 117,
      'lifetimeValue' => 827.16,
    ),
    '37087' => array(
      'productName' => 'Klick-Tipp Enterprise 25.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 118,
      'lifetimeValue' => 827.16,
    ),
    '37089' => array(
      'productName' => 'Klick-Tipp Enterprise 50.000',
      'amemberID' => 119,
      'lifetimeValue' => 967.84,
    ),
    '54851' => array(
      'productName' => 'Klick-Tipp Enterprise 50.000, Webinar-Sonderangebot',
      'amemberID' => 143,
      'lifetimeValue' => 967.84,
    ),
    '37091' => array(
      'productName' => 'Klick-Tipp Enterprise 50.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 120,
      'lifetimeValue' => 967.84,
    ),
    '37093' => array(
      'productName' => 'Klick-Tipp Enterprise 50.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 121,
      'lifetimeValue' => 967.84,
    ),
    '37095' => array(
      'productName' => 'Klick-Tipp Enterprise 100.000',
      'amemberID' => 122,
      'lifetimeValue' => 1831.19,
    ),
    '37097' => array(
      'productName' => 'Klick-Tipp Enterprise 100.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 123,
      'lifetimeValue' => 1831.19,
    ),
    '37099' => array(
      'productName' => 'Klick-Tipp Enterprise 100.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 124,
      'lifetimeValue' => 1831.19,
    ),
    '159039' => array(
      'productName' => 'Klick-Tipp Enterprise 150.000',
      'amemberID' => 150,
      'lifetimeValue' => 1036.22,
    ),
    '37101' => array(
      'productName' => 'Klick-Tipp Enterprise 250.000',
      'amemberID' => 125,
      'lifetimeValue' => 3620.59,
    ),
    '37103' => array(
      'productName' => 'Klick-Tipp Enterprise 250.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 126,
      'lifetimeValue' => 3620.59,
    ),
    '37105' => array(
      'productName' => 'Klick-Tipp Enterprise 250.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 127,
      'lifetimeValue' => 3620.59,
    ),
    '37107' => array(
      'productName' => 'Klick-Tipp Enterprise 500.000',
      'amemberID' => 128,
      'lifetimeValue' => 4473.96,
    ),
    '37109' => array(
      'productName' => 'Klick-Tipp Enterprise 500.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 129,
      'lifetimeValue' => 4473.96,
    ),
    '37113' => array(
      'productName' => 'Klick-Tipp Enterprise 500.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 130,
      'lifetimeValue' => 4473.96,
    ),
    '37115' => array(
      'productName' => 'Klick-Tipp Enterprise 1.000.000',
      'amemberID' => 131,
      // no lifetime value calculated yet
      'lifetimeValue' => 0,
    ),
    '37119' => array(
      'productName' => 'Klick-Tipp Enterprise 1.000.000, Jahreszahlung (10 Prozent Rabatt)',
      'amemberID' => 132,
      // no lifetime value calculated yet
      'lifetimeValue' => 0,
    ),
    '37121' => array(
      'productName' => 'Klick-Tipp Enterprise 1.000.000, Zwei-Jahreszahlung (20 Prozent Rabatt)',
      'amemberID' => 133,
      // no lifetime value calculated yet
      'lifetimeValue' => 0,
    ),
    '111167' => array(
      'productName' => 'Certified Consultant Seminar Executive',
      'amemberID' => 147,
      'lifetimeValue' => 182.02,
    ),
    '111179' => array(
      'productName' => 'Certified Consultant Seminar Executive Mastermind',
      'amemberID' => 148,
      'lifetimeValue' => 273.81,
    ),
  );

  return $ProductInfo[$ProductID];

}

/**
 * Get product info by GroupID to display on upgrade page
 * for description see first product in array
 * @param int $GroupID
 */
function ciapi_get_product_info_by_groupid($GroupID) {

  $GroupProductInfo = array(
    '6' => array(
      'ProductName' => 'Klick-Tipp Standard 10.000', //product name
      'ProductInfo' => '10.000 Kontakte', //info text
      'AmemberProductID' => array( //every group id can have up to 3 products
        'monthly' => 101, //product id for monthly payment
        'annually' => 102, //product id for annual payment
        'biannually' => 103, //product id for biannual payment
      ),
      'PricePerMonth' => array(
        'monthly' => "27", //product price per month for monthly payment
        'annually' => "25", //product price per month for annual payment
        'biannually' => "20", //product price per month for biannual payment
      ),
      'UpgradeOptions' => array(
        'Default' => array(11, 23, 31), //GroupIDs the customer can upgrade to
        'SubscriberLimitExceeded' => array(31), //if the subscriber limit is exceeded, the customer can only upgrade to these groups
      ),
    ),
    '9' => array(
      'ProductName' => 'Klick-Tipp Standard 20.000',
      'ProductInfo' => '20.000 Kontakte',
      'AmemberProductID' => array(
        'monthly' => 36,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "54",
        'annually' => 0,
      ),
      'UpgradeOptions' => array(
        'Default' => array(31),
        'SubscriberLimitExceeded' => array(31),
      ),
    ),
    '10' => array(
      'ProductName' => 'Klick-Tipp Einsteiger 200',
      'ProductInfo' => '200 Kontakte',
      'AmemberProductID' => array(
        'monthly' => 37,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "9,95",
        'annually' => 0,
        'biannually' => 0,
      ),
      'UpgradeOptions' => array(
        'Default' => array(6, 11, 23),
        'SubscriberLimitExceeded' => array(6, 11, 23),
      ),
    ),
    '11' => array(
      'ProductName' => 'Klick-Tipp Premium 10.000',
      'ProductInfo' => '10.000 Kontakte &#149; Newsletter-Splittesting &#149; API-Schnittstelle',
      'AmemberProductID' => array(
        'monthly' => 104,
        'annually' => 105,
        'biannually' => 106,
      ),
      'PricePerMonth' => array(
        'monthly' => "47",
        'annually' => "42",
        'biannually' => "35",
      ),
      'UpgradeOptions' => array(
        'Default' => array(23, 31),
        'SubscriberLimitExceeded' => array(31),
      ),
    ),
    '13' => array(
      'ProductName' => 'Klick-Tipp Premium 20.000',
      'ProductInfo' => '20.000 Kontakte &#149; Newsletter-Splittesting &#149; API-Schnittstelle',
      'AmemberProductID' => array(
        'monthly' => 43,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "94",
        'annually' => 0,
        'biannually' => 0,
      ),
      'UpgradeOptions' => array(
        'Default' => array(24),
        'SubscriberLimitExceeded' => array(24),
      ),
    ),
    '16' => array(
      'ProductName' => 'Klick-Tipp Deluxe 10.000', //old
      'ProductInfo' => '10.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club',
      'AmemberProductID' => array(
        'monthly' => 61,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "67",
        'annually' => 0,
        'biannually' => 0,
      ),
      'UpgradeOptions' => array(
        'Default' => array(31, 33, 24),
        'SubscriberLimitExceeded' => array(33),
      ),
    ),
    '17' => array(
      'ProductName' => 'Klick-Tipp Deluxe 20.000',
      'ProductInfo' => '20.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club',
      'AmemberProductID' => array(
        'monthly' => 57,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "140",
        'annually' => 0,
        'biannually' => 0,
      ),
      'UpgradeOptions' => array(
        'Default' => array(24),
        'SubscriberLimitExceeded' => array(24),
      ),
    ),
    '18' => array(
      'ProductName' => 'Klick-Tipp Deluxe 30.000',
      'ProductInfo' => '30.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club',
      'AmemberProductID' => array(
        'monthly' => 58,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "210",
        'annually' => 0,
        'biannually' => 0,
      ),
      'UpgradeOptions' => array(
        'Default' => array(25),
        'SubscriberLimitExceeded' => array(25),
      ),
    ),
    '19' => array(
      'ProductName' => 'Klick-Tipp Deluxe 50.000',
      'ProductInfo' => '50.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club',
      'AmemberProductID' => array(
        'monthly' => 59,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "350",
        'annually' => 0,
        'biannually' => 0,
      ),
      'UpgradeOptions' => array(
        'Default' => array(26),
        'SubscriberLimitExceeded' => array(26),
      ),
    ),
    '20' => array(
      'ProductName' => 'Klick-Tipp Deluxe 100.000',
      'ProductInfo' => '100.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club',
      'AmemberProductID' => array(
        'monthly' => 74,
        'annually' => 84,
        'biannually' => 95,
      ),
      'PricePerMonth' => array(
        'monthly' => "499",
        'annually' => "450",
        'biannually' => "375",
      ),
      'UpgradeOptions' => array(
        'Default' => array(27),
        'SubscriberLimitExceeded' => array(27),
      ),
    ),
    '21' => array(
      'ProductName' => 'Klick-Tipp Premium 40.000',
      'ProductInfo' => '40.000 Kontakte, Newsletter-Splittesting &#149; API-Schnittstelle',
      'AmemberProductID' => array(
        'monthly' => 65,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "200",
        'annually' => 0,
        'biannually' => 0,
      ),
      'UpgradeOptions' => array(
        'Default' => array(25),
        'SubscriberLimitExceeded' => array(25),
      ),
    ),
    '22' => array(
      'ProductName' => 'Klick-Tipp Affiliate', // CleverBridge Affiliate
      'ProductInfo' => 'Klick-Tipp Partnerprogramm',
      'AmemberProductID' => array(
        'monthly' => 67,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "0",
        'annually' => "0",
        'biannually' => "0",
      ),
      'UpgradeOptions' => array(
        'Default' => array(6, 11, 23),
        'SubscriberLimitExceeded' => array(6, 11, 23),
      ),
    ),
    '23' => array(
      'ProductName' => 'Klick-Tipp Deluxe 10.000',
      'ProductInfo' => '10.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club',
      'AmemberProductID' => array(
        'monthly' => 107,
        'annually' => 108,
        'biannually' => 109,
      ),
      'PricePerMonth' => array(
        'monthly' => "67",
        'annually' => "60",
        'biannually' => "50",
      ),
      'UpgradeOptions' => array(
        'Default' => array(31, 33, 24),
        'SubscriberLimitExceeded' => array(33),
      ),
    ),
    '24' => array(
      'ProductName' => 'Klick-Tipp Enterprise 25.000',
      'ProductInfo' => '25.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club &#149; 3 Dedizierte Mailserver',
      'AmemberProductID' => array(
        'monthly' => 116,
        'annually' => 117,
        'biannually' => 118,
      ),
      'PricePerMonth' => array(
        'monthly' => "179",
        'annually' => "161",
        'biannually' => "135",
      ),
      'UpgradeOptions' => array(
        'Default' => array(25),
        'SubscriberLimitExceeded' => array(25),
      ),
    ),
    '25' => array(
      'ProductName' => 'Klick-Tipp Enterprise 50.000',
      'ProductInfo' => '50.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club &#149; 5 Dedizierte Mailserver',
      'AmemberProductID' => array(
        'monthly' => 119,
        'annually' => 120,
        'biannually' => 121,
      ),
      'PricePerMonth' => array(
        'monthly' => "289",
        'annually' => "260",
        'biannually' => "217",
      ),
      'UpgradeOptions' => array(
        'Default' => array(26),
        'SubscriberLimitExceeded' => array(26),
      ),
    ),
    '26' => array(
      'ProductName' => 'Klick-Tipp Enterprise 100.000',
      'ProductInfo' => '100.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club &#149; 10 Dedizierte Mailserver',
      'AmemberProductID' => array(
        'monthly' => 122,
        'annually' => 123,
        'biannually' => 124,
      ),
      'PricePerMonth' => array(
        'monthly' => "499",
        'annually' => "450",
        'biannually' => "375",
      ),
      'UpgradeOptions' => array(
        'Default' => array(27),
        'SubscriberLimitExceeded' => array(27),
      ),
    ),
    '27' => array(
      'ProductName' => 'Klick-Tipp Enterprise 250.000',
      'ProductInfo' => '250.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club &#149; 25 Dedizierte Mailserver',
      'AmemberProductID' => array(
        'monthly' => 125,
        'annually' => 126,
        'biannually' => 127,
      ),
      'PricePerMonth' => array(
        'monthly' => "999",
        'annually' => "900",
        'biannually' => "750",
      ),
      'UpgradeOptions' => array(
        'Default' => array(28),
        'SubscriberLimitExceeded' => array(28),
      ),
    ),
    '28' => array(
      'ProductName' => 'Klick-Tipp Enterprise 500.000',
      'ProductInfo' => '500.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club &#149; 40 Dedizierte Mailserver',
      'AmemberProductID' => array(
        'monthly' => 128,
        'annually' => 129,
        'biannually' => 130,
      ),
      'PricePerMonth' => array(
        'monthly' => "1499",
        'annually' => "1350",
        'biannually' => "1125",
      ),
      'UpgradeOptions' => array(
        'Default' => array(29),
        'SubscriberLimitExceeded' => array(29),
      ),
    ),
    '29' => array(
      'ProductName' => 'Klick-Tipp Enterprise 1.000.000',
      'ProductInfo' => '1.000.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club &#149; 60 Dedizierte Mailserver',
      'AmemberProductID' => array(
        'monthly' => 131,
        'annually' => 132,
        'biannually' => 133,
      ),
      'PricePerMonth' => array(
        'monthly' => "2499",
        'annually' => "2250",
        'biannually' => "1871",
      ),
      'UpgradeOptions' => array(
        'Default' => array(),
        'SubscriberLimitExceeded' => array(),
      ),
    ),
    '30' => array(
      'ProductName' => 'Klick-Tipp Affiliate', // DigiStore Affiliate
      'ProductInfo' => 'Klick-Tipp Partnerprogramm',
      'AmemberProductID' => array(
        'monthly' => 137,
        'annually' => 0,
        'biannually' => 0,
      ),
      'PricePerMonth' => array(
        'monthly' => "0",
        'annually' => "0",
        'biannually' => "0",
      ),
      'UpgradeOptions' => array(
        'Default' => array(6, 11, 23),
        'SubscriberLimitExceeded' => array(6, 11, 23),
      ),
    ),
    '31' => array(
      'ProductName' => 'Klick-Tipp Enterprise 10.000',
      'ProductInfo' => '10.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club &#149; 1 Dedizierter Mailserver',
      'AmemberProductID' => array(
        'monthly' => 110,
        'annually' => 111,
        'biannually' => 112,
      ),
      'PricePerMonth' => array(
        'monthly' => "149",
        'annually' => "134",
        'biannually' => "127",
      ),
      'UpgradeOptions' => array(
        'Default' => array(33),
        'SubscriberLimitExceeded' => array(33),
      ),
    ),
    '32' => array(
      'ProductName' => 'Klick-Tipp Enterprise 10.000', // with two mailservers
      'ProductInfo' => '10.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club &#149; 2 Dedizierte Mailserver',
      'AmemberProductID' => array(
        'monthly' => 113,
        'annually' => 114,
        'biannually' => 115,
      ),
      'PricePerMonth' => array(
        'monthly' => "199",
        'annually' => "179",
        'biannually' => "169",
      ),
      'UpgradeOptions' => array(
        'Default' => array(24),
        'SubscriberLimitExceeded' => array(24),
      ),
    ),
    '33' => array(
      'ProductName' => 'Klick-Tipp Enterprise 15.000',
      'ProductInfo' => '15.000 Kontakte &#149; Newsletter- & Follow-up-eMail-Splittesting &#149; API-Schnittstelle &#149; Listbuilding mit Facebook &#149; Splittest-Club &#149; 2 Dedizierte Mailserver',
      'AmemberProductID' => array(
        'monthly' => 113,
        'annually' => 114,
        'biannually' => 115,
      ),
      'PricePerMonth' => array(
        'monthly' => "199",
        'annually' => "179",
        'biannually' => "169",
      ),
      'UpgradeOptions' => array(
        'Default' => array(24),
        'SubscriberLimitExceeded' => array(24),
      ),
    ),
  );

  return $GroupProductInfo[$GroupID];

}

/**
 * Get current product of a user with possible upgrade products
 * Enter description here ...
 * @param stdClass $account
 */
function ciapi_get_current_user_product($account) {

  if (!isset($account)) {
    global $user;
    $account = $user;
  }

  $GroupID = $account->RelUserGroupID;

  $Product = ciapi_get_product_info_by_groupid($GroupID);

  //check if the user pays annually
  if (!empty($Product)) {
    $Product['AnnualPayment'] = ciapi_user_has_product($account->uid, $Product['AmemberProductID']['annually'], TRUE);
    $Product['BiAnnualPayment'] = ciapi_user_has_product($account->uid, $Product['AmemberProductID']['biannually'], TRUE);
    $Product['MonthlyPayment'] = ($Product['AnnualPayment'] || $Product['BiAnnualPayment'] ) ? FALSE : TRUE;
  }

  //check subscriber limit
  $LimitExceeded = Subscribers::CheckSubscriberLimits((array)$account);
  $Product['UpgradeProducts'] = ($LimitExceeded) ? $Product['UpgradeOptions']['SubscriberLimitExceeded'] : $Product['UpgradeOptions']['Default'];

  return $Product;

}

/**
 * Check if a user has certain products by aMemberProductIDs
 * @param $UserID
 * @param array $aMemberProductIDs
 * @return bool
 */
function ciapi_user_has_product($UserID, $aMemberProductIDs, $CheckExpired = FALSE) {
  $access_timestamp = _amember_user_has_product($UserID, $aMemberProductIDs, $CheckExpired);
  return (!empty($access_timestamp));
}

/**
 * check if the main account has access to email marketing formula product, also known as BAM
 * the email marketing formula is a one-time payment with lifetime access
 * active consultants have access to the email marketing formula as well
 * @param $UserID - deprecated
 * @return bool
 */
function ciapi_user_has_email_marketing_formula_product($UserID = 0) {
  $mainAccountID = Subaccount::FindMainAccount();
  $account = user_load($mainAccountID);
  $consultant_rid = variable_get('klicktipp_consultant_roleid', 0);
  $pids = ciapi_klicktipp_get_setting("email_marketing_formula_productids");
  return (user_has_role($consultant_rid, $account) || ciapi_user_has_product($mainAccountID, $pids));
}

/**
 * find the logged in user considering the main account, subaccount and support user
 * @return bool|mixed
 */
function ciapi_find_main_account() {
  return klicktipp_find_mainaccount();
}

//---------

/*
 * Partner program
 */

/**
 * Create an PDF from partner programm content (HTML) and upload it to S3
 * @param int $amember_id : aMemberID to create the S3-URL and to create affiliate links in the PDF
 * @param string $content : HTML to create the PDF
 * @param string $pdf_filename : name of the created PDF
 */
function ciapi_generate_partner_pdf($amember_id, $content, $pdf_filename) {

  Libraries::include('s3_filemanager.inc');

  if (empty($amember_id))
    return ''; //no amemberID -> no upload

  Libraries::loadDOMPDF();

  //DOMPDF needs valid HTML
  $output = "<html><head></head><body>$content</body></html>";

  //create pdf with DOMPDF
  //DOMPDF BUG: DOMPDF starts an output buffer but does not close it
  $ob_level_start = ob_get_level(); //count output buffers before using DOMPDF
  $dompdf = dompdf_render_html(utf8_decode($output));

  //get the rendered PDF
  $pdf_content = $dompdf->output();

  $ob_level_end = ob_get_level(); //count output buffers after using DOMPDF

  if ($ob_level_end > $ob_level_start)
    ob_end_clean(); //if DOMPDF leaves his buffer open, close it

  //create temp pdf file on the server to upload it to S3
  //file_directory_temp gets the tmp-path from the settings (defaults to /files/tmp)
  $pdf_tmp_file = tempnam(file_directory_temp(), "pdf_");
  if (!empty($pdf_tmp_file)) {
    if (file_put_contents($pdf_tmp_file, $pdf_content) === FALSE) {
      watchdog("ciapi", "Generate partner PDF: Error while writing PDF content to tmp file. Function: file_put_contents()", NULL, WATCHDOG_ERROR);
      unlink($pdf_tmp_file);
      return '';
    }
  } else {
    watchdog("ciapi", "Generate partner PDF: Error while creating tmp file for S3 upload. Function: tempnam()", NULL, WATCHDOG_ERROR);
    return '';
  }

  //relative S3 path for current partner
  $s3_dest = "pdf/pp/$amember_id/$pdf_filename";

  //upload pdf file to S3
  if (!_s3_file_manager(S3_FILE_ACTION_UPLOAD, $pdf_tmp_file, $s3_dest)) {
    watchdog("ciapi", "Generate partner PDF: Error while uploading to S3. Filename: !filename, aMemberID: !amember", array('!filename' => $pdf_filename, '!amember' => $amember_id), WATCHDOG_ERROR);

    //remove temp pdf file
    unlink($pdf_tmp_file);
    return '';

  }

  //remove temp pdf file
  unlink($pdf_tmp_file);


  //return complete path to PDF on S3
  $bucket = _s3_get_setting(S3_SETTINGS_BUCKET);
  $endpoint = _s3_get_setting(S3_SETTINGS_ENDPOINT);
  return "https://$bucket.$endpoint/$s3_dest";

}

//get the title of a specific node
//default: current node title
function ciapi_node_get_title($nid = 0) {

  if (empty ($nid))
    return check_plain(drupal_get_title());
  else
    return check_plain(kt_query('SELECT title FROM {node} WHERE nid = %d', $nid)->fetchField());

}

/*
 * output a themed (TODO) YouTube video
 * store the the YouTube video URL in $_POST['help-video']
 *
 * - $_POST['help-video'] will be used in the klicktipp help block to display videos of manual pages in a modal dialog
 * - when links to manual pages are created for the help block, we use klicktipp_filter() in klicktipp_get_help_video()
 *   to execute the content_include of the node
 * -> if the include contains a ciapi_get_help_video($YouTubeURL) call, $_POST['help-video'] is set with the YouTube video URL
 * -> klicktipp_get_help_video() can access $_POST['help-video'] to get the YouTube video URL
 * => if a URL is available, the link in the help block will open a modal dialog with the video instead of redirecting to the manual page
 * @see: klicktipp_add_help_link() and klicktipp_get_help_video()
 *
 * TODO: support wistia videos
 */
function ciapi_get_help_video($YouTubeURL, $on_hover_animation = TRUE, $backwards_compatibility = TRUE, $width = 800, $height = 450) {

  //set the YouTube URL for klicktipp_get_help_video()
  $hasHelpVideo = &drupal_static('klicktipp-help-manual-video');
  $hasHelpVideo = $YouTubeURL;

  // get the part after the last slash of the url
  $URLParts = explode('/', $YouTubeURL);
  $URLLastPart = end($URLParts);
  // cut all the parameters of the url
  $videoParams = explode('?', $URLLastPart);
  $videoID = $videoParams[0];

  // output the themed video
  $ctx = stream_context_create(array(
      'http' => array(
        'timeout' => 3
      )
    )
  );
  $thumbnail = @file_get_contents('https://i.ytimg.com/vi/' . $videoID . '/maxresdefault.jpg', 0, $ctx);
  if (empty($thumbnail)) {
    $thumbnail = @file_get_contents(APP_URL . "misc/demo_thumbnail.jpg");
  }
  $thumbnail = base64_encode($thumbnail);

  drupal_add_js('content_includes/js/libs/youtube-one-click.js', array(
    'type' => 'file',
    'group' => JS_DEFAULT,
    'every_page' => FALSE,
    'scope' => 'header',
    'cache' => TRUE,
    'defer' => FALSE,
    'preprocess' => FALSE,
    'version' => NULL,
  ));

  $youtube_class = 'youtube-play ';

  if ($on_hover_animation) {
    $youtube_class .= 'on-hover';
  }

  $youtube_styles = '';
  $general_styles = '';
  $video_styles = '';

  if ($backwards_compatibility) {
    $youtube_styles = "position: absolute;
                        margin: auto;
                        top: 0;
                        left: 0;
                        bottom: 0;
                        right: 0;
                        z-index: 10;
                        width: 120px;
                        height: 90px;
                        background-image: url(\"/content_includes/img/webinar/ccp/youtube-play.png\");
                        background-position: top center;
                        background-repeat: no-repeat;
                        background-size: contain;
                        cursor: pointer;
                        -webkit-animation: button-grow 1s infinite;
                        -moz-animation: button-grow 1s infinite;
                        -o-animation: button-grow 1s infinite;
                        animation: button-grow 1s infinite;
                        -moz-transform: translate(-50%, -50%);
                        -o-transform: translate(-50%, -50%);
                        -ms-transform: translate(-50%, -50%);
                        -webkit-transform: translate(-50%, -50%);
                        transform: translate(-50%, -50%);";

    $general_styles = '<style>
                      @-webkit-keyframes button-grow {
                        0%   {
                          -webkit-transform: scale( 1 );
                        }
                        50% {
                          -webkit-transform: scale( 1.25 );
                        }
                        100% {
                          -webkit-transform: scale( 1 );
                        }
                      }

                      @-moz-keyframes button-grow {
                        0%   {
                          -moz-transform: scale( 1 );
                        }
                        50% {
                          -moz-transform: scale( 1.25 );
                        }
                        100% {
                          -moz-transform: scale( 1 );
                        }
                      }

                      @-o-keyframes button-grow {
                        0%   {
                          -o-transform: scale( 1 );
                        }
                        50% {
                          -o-transform: scale( 1.25 );
                        }
                        100% {
                          -o-transform: scale( 1 );
                        }
                      }

                      @keyframes button-grow {
                        0%   {
                          transform: scale( 1 );
                        }
                        50% {
                          transform: scale( 1.25 );
                        }
                        100% {
                          transform: scale( 1 );
                        }
                      }

                      iframe {
                        width: 100%;
                        height: 100%;
                      }
                      </style>';

    $umbau_specific = '<style>
                          #invitation-video img {
                            width: 100%;
                            height: 100%;
                          }
                         </style>';

    $video_styles = '<style>
                      .kt-tv-video-container {
                        display: block;
                        position: relative;
                        margin: 0 auto;
                        width: 100%;
                        background: transparent url(/content_includes/img/handbuch/tv_800x450.png) scroll no-repeat center;
                        background-size: contain;
                      }

                      .kt-tv-video-container .kt-800-450-tv {
                        width: 100%;
                        height: auto;
                        background: none;
                      }

                      .kt-tv-video-container .kt-800-450-tv .kt-800-450-video {
                        position: relative;
                        width: 100%;
                        height: 0;
                        margin: 0 auto;
                        padding-top: calc(100% / 1.777777778);
                      }

                      .kt-tv-video-container .kt-800-450-tv .kt-800-450-video iframe,
                      .kt-tv-video-container .kt-800-450-tv .kt-800-450-video .wistia_embed,
                      .kt-tv-video-container .kt-800-450-tv .kt-800-450-video img {
                        width: 81%;
                        height: 81%;
                        position: absolute;
                        left: 0;
                        right: 0;
                        margin: 0 auto;
                        top: 50%;
                        -moz-transform: translateY(-59%);
                        -o-transform: translateY(-59%);
                        -ms-transform: translateY(-59%);
                        -webkit-transform: translateY(-59%);
                        transform: translateY(-59%);
                      }

                      @media screen and (max-width: 880px) {
                        .kt-tv-video-container .kt-800-450-tv .kt-800-450-video iframe,
                        .kt-tv-video-container .kt-800-450-tv .kt-800-450-video .wistia_embed,
                        .kt-tv-video-container .kt-800-450-tv .kt-800-450-video img {
                          transform: translateY(-50%);
                          width: 100%;
                          height: 100%;
                        }
                      }

                      .kt-tv-divider {
                        width: 100%;
                        background: transparent url(https://assets.klicktipp.com/content_includes/frontpage/divider-kt-fernseher.png) scroll no-repeat;
                        background-size: contain;
                        background-position: center;
                      }
                      </style>';
  }

  return "$general_styles
            $umbau_specific
            $video_styles
            <div style='$youtube_styles' class='$youtube_class' data-type='play-video' data-video-src='$YouTubeURL'></div>
            <img class='video-thumbnail' src='data:image/jpg;base64,$thumbnail'/>";

}

//@param $ValidUntil: if empty, the URL is valid until calling DigiStoreAPI deleteBuyUrl()
function ciapi_digistore_personalize_cart_url ( $ProductID, $account, $ValidUntil = '1h', $SendHttpRequestCallback = '_klicktipp_digistore_api_http_request') {

  Libraries::include('digistore.inc');

  $ApiKey = variable_get('klicktipp_settings_digistore_api_key', '');

  if (empty($ProductID)) {
    return '';
  }

  if (!$account) {
    return "https://www.digistore24.com/product/$ProductID";
  }

  $Buyer = array(
    'email' => Subscribers::DepunycodeEmailAddress($account->mail), //if a customer wants to upgrade/downgrade he must use the same email address
    'readonly_keys' => 'email', //'email', //customer cannot change email in digistore; other options: 'all', 'email_and_name', 'none'
    'first_name' => empty($account->FirstName) ? '' : $account->FirstName,
    'last_name' => empty($account->LastName) ? '' : $account->LastName,
    'company' => empty($account->CompanyName) ? '' : $account->CompanyName,
    'street' => empty($account->Street) ? '' : $account->Street,
    'city' => empty($account->City) ? '' : $account->City,
    'zipcode' => empty($account->Zip) ? '' : $account->Zip,
    'state' => empty($account->State) ? '' : $account->State,
    'country' => empty($account->Country) ? '' : $account->Country,
    'phone_no' => empty($account->Phone) ? '' : $account->Phone,
    'tax_id' => '',
  );

  $PostData = [
    'buyer' => $Buyer
  ];

  $PostData['product_id'] = $ProductID;
  $PostData['valid_until'] = $ValidUntil;

  //not used (yet)
  /*
  array(
      'payment_plan' => array(
          'first_amount'
          'other_amounts'
          'number_of_installments'
          'first_billing_interval'
          'other_billing_intervals'
      ),
      'tracking' => array(
          'custom'
          'affiliate'
          'campaignkey'
          'trackingkey'
      ),
      'urls' => array(
          'thankyou_url'
          'fallback_url'
      ),
      'placeholders' => array(),
      'settings' => array(
          'orderform_id'
          'img'
      ),
  )
  */

  // call the DigiStore24 API
  // _klicktipp_digistore_api_http_request($account, $api_key, $func, $data = array(), $mockFunctionForTest = NULL)
  $DigiStoreResult = call_user_func($SendHttpRequestCallback, $account, $ApiKey, 'createBuyUrl',$PostData);

  if ( !empty($DigiStoreResult['data']['url']) ) {
    //success: we got an URL back from the API
    return $DigiStoreResult['data']['url'];
  }

  //error: we did not get an URL (error in account data, product_id or other parameters)

  $error = array(
    '!UserID' => $account->uid,
    '!ProductID' => $ProductID,
    '!AccountData' => $Buyer,
    '!ValidUntil' => $ValidUntil,
    '!Response' => $DigiStoreResult,
    '!Error' => $DigiStoreResult['message'],
  );

  $message = "Digistore24 API error: Invalid API data for User !UserID and ProductID !ProductID - !Error";

  Errors::unexpected($message, $error);

  return "https://www.digistore24.com/product/$ProductID";
}

/**
 * Get current active digistore payment from amember payments
 * @param stdClass $account
 * @return PaymentArray
 */
function ciapi_digistore_get_current_user_payment($account): array
{
  return Amember::getCurrentPayment($account) ?: ['begin_date' => 0];
}

function ciapi_digistore_get_affiliate_name_by_affiliate_cookie() {

  $aff_id = $_COOKIE['amember_aff_id'];

  if ( !empty($aff_id) ) {

    $amember_user = amember_rpc_get_user($aff_id);

    if ( !empty($amember_user) && !empty($amember_user['digistore_affiliate_name']) ) {
      return $amember_user['digistore_affiliate_name'];
    }

  }

  return '';

}

/**
 * get a users digistore affiliate name by amemberid
 * @param $aff_id
 * @return string
 */
function ciapi_digistore_getAffiliateNameByAffID($aff_id) {
  if (!empty($aff_id)) {
    $amember_user = amember_rpc_get_user($aff_id);
    if (!empty($amember_user) && !empty($amember_user['digistore_affiliate_name'])) {
      return $amember_user['digistore_affiliate_name'];
    }
  }
  return '';
}

/**
 * a user cannot be it's own affiliate -> set blocker cookie @see crm/goto.php
 * this function is called for every logged in user @see block "Affiliate blocker cookie"
 * Note: this is obsolete due to digistore having its own protection (DEV-1549 ek 2020-01-31)
 * but the cookie is still used for determining unauth partner access
 */
function ciapi_digistore_set_affiliate_blocker_cookie() {

  global $user;

  if ( $user->uid > 0 ) {

    $aMemberID = _klicktipp_get_amemberid($user->name, $user);

    if ( $aMemberID > 0 ) {
      setcookie('amember_partner_id', $aMemberID, strtotime('+1 year'), '/', '.' . KLICKTIPP_DOMAIN, TRUE);
    }

  }

}

/**
 * Get all active digistore marketplace entries
 * @return string
 */
function ciapi_digistore_get_marketplace_entries() {

  Libraries::include('guzzle.inc');

  $entries = variable_get('digistore_marketplace_entries', 0);
  $timestamp = variable_get('digistore_marketplace_entries_timestamp', 0);
  $ApiKey = variable_get('klicktipp_settings_digistore_api_key', '');

  if ($timestamp >= strtotime("-1 day") || empty($ApiKey)) {
    // entries were already fetched within the last 24 hours
    return $entries;
  }
  variable_set('digistore_marketplace_entries_timestamp', time());

  $guzzleHeaders = [
    'X-DS-API-KEY' => $ApiKey,
    'Accept' => 'application/json',
  ];

  $response = guzzle_http_request("https://www.digistore24.com/api/call/statsMarketplace?lang=de", [
    'method' => 'GET',
    'headers' => $guzzleHeaders
  ]);

  if (!empty($response->error) || $response->code != 200) {
    // call to digistore was unsucessful so write to watchdog and return
    Errors::unexpected('CIAPI: Error requesting DigiStore Marketplace Entries', array(
      '!data' => $response->data,
      '!code' => $response->code,
      '!error' => $response->error,
    ));
    return $entries;
  }

  $response = drupal_json_decode($response->data);

  if ($response['result'] == 'success') {
    if (!empty($response['data']['count'])) {
      $entries = $response['data']['count'];
      variable_set('digistore_marketplace_entries', $entries);
    }
    else {
      Errors::unexpected('CIAPI: DigiStore Marketplace Entries value is empty', array(
        '!data' => $response->data,
        '!entries' => $response['data']['count'],
      ));
    }
  }
  else {
    Errors::unexpected('CIAPI: DigiStore Marketplace Entries could not be fetched', array(
      '!data' => $response->data,
      '!code' => $response->code,
      '!error' => $response->error,
    ));
  }

  return $entries;

}

function ciapi_klicktipp_get_setting($key) {

  switch ($key) {
    case 'data_processing_order_product_id':
      return variable_get('klicktipp_account_dpo_digistore_product_id', '');
    case 'digistore_api_key':
      return variable_get('klicktipp_settings_digistore_api_key', '');
    case 'email_marketing_formula_productids':
      return array_map('trim', explode(',', variable_get('amember_email_marketing_formula_productids', [])));
    default:
      return variable_get($key, '');
  }

}

/**
 * form to collect feedback of articles in the manual
 * will be used on all manual pages except the manual overview
 * note: clicks on the buttons yes or no are tracked by google tagging manager
 */
function ciapi_klicktipp_article_feedback_form($form, $form_state) {

  $weight = 0;

  $form = array();

  $form['Feedback'] = array(
    '#type' => 'textarea',
    '#title' => t('How can we improve it?'),
    '#default_value' => '',
    '#weight' => $weight++,
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['Submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_email_submit',
    '#value' => t('Send feedback'),
    '#weight' => $weight++,
  );

  return $form;

}

/**
 * send collected feedback of the reader to helpscout mailbox
 */
function ciapi_klicktipp_article_feedback_form_submit($form, &$form_state) {

  if (empty($form_state['values']['Feedback'])) {
    drupal_set_message(t('Your feedback is very important to us.'));
    return;
  }

  global $user;
  $Message = $form_state['values']['Feedback'];
  $HelpScoutMailbox = 77813; // https://secure.helpscout.net/mailbox/b597807c5539d5b9/
  $HelpScoutUser = 127150; // tickets will be assigned to Johannes Friedrich
  $email = empty($user->mail) ? '<EMAIL>' : Subscribers::DepunycodeEmailAddress($user->mail);

  $NewConversation = array(
    "subject" => "Handbuch Feedback: " . ciapi_node_get_title(),
    "mailboxId" => $HelpScoutMailbox,
    "type" => "email",
    "status" => "active",
    "assignTo" => $HelpScoutUser,
    "tags" => array(
      "Handbuch Feedback"
    ),
    "customer" => array(
      "email" => $email,
      "firstName" => $user->FirstName,
      "lastName" => $user->LastName,
    ),
    "threads" => array(
      array(
        "type" => "customer",
        "text" => $Message,
        "customer" => array(
          "email" => $email,
        ),
      ),
    ),
  );

  Libraries::include('helpscout.inc');

  $helpscoutApiToken = klicktipp_helpscout_api_get_token();

  if (empty($helpscoutApiToken)) {
    Errors::unexpected("Helpscout Feedback: API error occured", array('!message' => 'no valid API token found'));
  } else {
    // documentation: https://developer.helpscout.com/mailbox-api/endpoints/conversations/create/
    $response = drupal_http_request('https://api.helpscout.net/v2/conversations', array(
      'method' => 'POST',
      'headers' => array(
        'Content-Type' => 'application/json',
        'Authorization' =>  "{$helpscoutApiToken['token_type']} {$helpscoutApiToken['access_token']}",
      ),
      'data' => drupal_json_encode($NewConversation),
    ));

    // we should always have a valid token so we should not get
    // a 401 if so, or something else goes wrong, this is caught here
    if ($response->code != '201') {
      $error = array(
        '!code' => $response->code,
        '!error' => $response->error,
        '!message' => $response->status_message,
        '!request' => $response->request,
        '!headers' => $response->headers,
        '!data' => $response->data,
      );
      Errors::unexpected('Helpscout Feedback: API error occured', $error);
    }
  }

  drupal_set_message(t('Your feedback is very important to us.'));

}

/**
 * Retrieve a subscriber by email address from the marketing account (usually 'klicktipp', id: 380)
 * @param $EmailAddress
 * @return array subscriber
 */
function ciapi_klicktipp_get_subscriber_by_email($EmailAddress, $ReferenceID = 0) {
  $UserID = variable_get('klicktipp_marketing_account_id', 0);
  $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, Subscribers::PunycodeEmailAddress($EmailAddress));
  return Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
}

/**
 * Retrieve a subscriber by subscriber id from the marketing account (usually 'klicktipp', id: 380)
 * @param $SubscriberID
 * @return array subscriber
 */
function ciapi_klicktipp_get_subscriber_by_id($SubscriberID, $ReferenceID = 0) {
  $UserID = variable_get('klicktipp_marketing_account_id', 0);
  return Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
}

/**
 * Retrieve a subscribers taggings from the marketing account (usually 'klicktipp', id: 380)
 * @param $SubscriberID
 * @param bool $TagIDsOnly
 * @return array taggings
 */
function ciapi_klicktipp_get_taggings_of_subscriber($SubscriberID, $TagIDsOnly = FALSE) {
  $UserID = variable_get('klicktipp_marketing_account_id', 0);
  $ReferenceID = 0;
  return Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID, $TagIDsOnly);
}

/**
 * Tag a subscriber in the marketing account (usually 'klicktipp', id: 380)
 * @param $SubscriberID
 * @param $TagID
 * @param bool $TriggerAutoResponders
 * @return bool
 */
function ciapi_klicktipp_tag_subscriber($SubscriberID, $TagID, $TriggerAutoResponders = FALSE, $ReferenceID = 0) {
  $UserID = variable_get('klicktipp_marketing_account_id', 0);
  $result = Subscribers::TagSubscriber($UserID, $SubscriberID, $TagID, $ReferenceID, $TriggerAutoResponders);
  TransactionEmails::RegisterAutomations($UserID, $SubscriberID, $ReferenceID, true);
  return $result;
}

/**
 * Create the subscriber secret for a subscriber in the marketing account (usually 'klicktipp', id: 380)
 * @param $SubscriberID
 * @return string
 */
function ciapi_klicktipp_encode_subscriber_secret($SubscriberID) {
  $UserID = variable_get('klicktipp_marketing_account_id', 0);
  return Core::EncryptURL(array('UserID' => $UserID, 'SubscriberID' => $SubscriberID), 'subscriber_secret');
}

/**
 * Retrieve total number of subscribers of a tag from the marketing account (usually 'klicktipp', id: 380)
 * @param $TagID
 * @return int
 */
function ciapi_klicktipp_get_tag_count($TagID) {
  $UserID = variable_get('klicktipp_marketing_account_id', 0);
  return Tag::RetrieveCount($UserID, $TagID);
}

/**
 * Retrieve digistore affiliate name of a user
 * @param $UserID
 * @return string
 */
function ciapi_klicktipp_get_digistore_affiliate_name($UserID) {
  $account = user_load($UserID);
  return (empty($account->digistore_affiliate_name)) ? "" : $account->digistore_affiliate_name;
}

/*
* Sometimes some components of a book page should not be displayed depending on the contents of the book page (content_includes file)
* To hide the component, insert the following line into the content_includes file:
* <?php ciapi_set_variable($name, TRUE); ?> whereby $name is the name of the component, e.g. 'hide_book_navigation_feedback_form'
* @note: used as a getter as ciapi_set_variable($name)
*/
function ciapi_set_variable($name, $value = null) {

  $stored_value = &drupal_static($name);

  if (!is_null($value) ) {
    $stored_value = $value;
  }

  return $stored_value;

}

/**
 * Wrapper for klicktipp_noindex_nofollow() to use in content_includes
 * Set a meta tag for search engine to not index the current page or follow any links on it
 */
function ciapi_noindex_nofollow() {
  klicktipp_noindex_nofollow();
}

/*
* Enterprise customers can book coachings with the support team
* To show the available coachings to the customer, insert the following line into the content_includes file:
* <?php echo ciapi_enterprise_coachings(); ?>
*/
function ciapi_enterprise_coachings() {

  // the enterprise customer himself, his subaccounts and agency can book coachings
  $UserID = Subaccount::FindMainAccount();
  if (empty($UserID)) {
    drupal_set_message(t('You have tried to access a site that is only for enterprise customers. Please login using the form below.'));
    drupal_goto('user');
    exit;
  }
  $account = user_load($UserID);

  if (!user_access('use whitelabel domain', $account)) {
    // no enterprise customer
    return t("You are not an enterprise customer. Please check your subscription!");
  }

  $data = http_build_query(array(
    'vorname' => $account->FirstName,
    'nachname' => $account->LastName,
    'email' => Subscribers::DepunycodeEmailAddress($account->mail),
    'telefon' => $account->Phone,
  ), '', '&');
  // is enterprise customer and can therefore book as many coachings as he want
  return "<iframe style='margin: 0px auto;' src='https://www.termininfo.net/e/klick-tipp/onboarding-fuer-klick-tipp-enterprise-kunden?{$data}' name='iframe-terminpilot' width='100%' height='1000' frameborder='0' scrolling='yes' align='aus'></iframe>";

}

/**
 * Display the klicktipp manual overview
 * @param string $forCategory: level 1 taxonomy name of manual vocabulary (if empty display top 5 pages of each category)
 * @param string $overview: path of the index page (used for category links)
 * @return string: theme manual overview
 */
function ciapi_get_manual_overview($forCategory = '', $overview = 'handbuch' ) {
  Libraries::include('help_block.inc');
  return klicktipp_help_manual_overview($forCategory, $overview);
}

/**
 * Display content for the custom 403 page
 * Case 1: the main account reaches this page and has not confirmed the dpo -> show confirmation form
 * Case 2: a subaccount reaches this page and the main account has not confirmed the dpo -> show message
 * Case 3: the main account has confirmed the dpo -> normal 403 error
 * @see content_includes/403keinzugriff.inc
 * @return string
 */
function ciapi_403_data_processing_order() {

  global $user;

  $mainaccount = Subaccount::FindMainAccount();
  $account = user_load($mainaccount);
  $isActive = variable_get('klicktipp_data_processing_order_check', 0);

  if ($isActive && !empty($account->uid) && !VarDataProcessingOrder::HasAccess($account->uid)) {

    if ($mainaccount == $user->uid) {
      //this is the main account -> show dpo confirmation form
      Libraries::include('account_dpo.inc', '/forms');
      $form = drupal_get_form('klicktipp_account_dpo_form', $account);
      return drupal_render($form);
    }
    else {
      //display a message for the subaccount that the main account has to confirm first
      return 'subaccount';
    }

  }


  //normal 403
  return '';


}

/**
 * Displays the page title in the styleguide format and removes the drupal title from the page template
 * @param string $title
 * @return string
 */
function ciapi_styleguide_set_title($title = '') {

  if ( empty($title) ) {
    $title = check_plain(drupal_get_title());
  }

  drupal_set_title('');

  return "<h1>$title</h1>";
}

/**
 * Display a youtube video within a television graphic and make it available in the help block
 * @param $url
 */
function ciapi_styleguide_television_for_video($url, $justframe = FALSE, $backwards_compatibility = FALSE) {
  if ($justframe) {
    return ciapi_get_help_video($url, FALSE, $backwards_compatibility);
  }

  return '<div class="kt-tv-video-container">
            <div class="kt-800-450-tv">
              <div class="kt-800-450-video">
                ' . ciapi_get_help_video("$url") . '
              </div>
            </div>
          </div>';
}

/**
 * theme a page without the themes containers
 * @see klicktipp_marketing_preprocess_page() in klicktipp_marketing module
 * Note: if possible, replace with ciapi_marketing_page()
 */
function ciapi_styleguide_landingpage() {
  ciapi_set_variable('ciapi_theme_marketing_page', 'klicktipp_landingpage');
}

/**
 * Check if we are on staging
 * @return bool
 * @deprecated use ciapi_is_production instead
 */
function ciapi_is_staging() {
  //@note: this does not, what it states
  return KLICKTIPP_ENVIRONMENT !== 'prod';
}

/**
 * Check if we are on production
 * @return bool
 */
function ciapi_is_production() {
  return KLICKTIPP_ENVIRONMENT == 'prod';
}
/**
 * Theme a content include (node) with a marketing template @see klicktipp_marketing module
 * @param string $template: template as specified in klicktipp_marketing_theme()
 * @param array $config: array(
 *    'menu' => array(
 *      'main' => rendered html for the main menu || '' for no main menu || do not set to show default menu
 *      'footer' => rendered html for the footer menu || '' for no footer menu || do not set to show default footer menu
 *    ),
 *    'exclude' => array(
 *      'css' => array of css path+filename that should be remove from previous drupal_add_css() adds
 *      'javascript' => array of javascript path+filename that should be remove from previous drupal_add_js() adds
 *    ),
 *    'variables' => array(
 *      'headline' => 'This is a headline', //will provide $headline in the template file,
 *      ...
 *    ),
 *    'debug' => boolean, will provide debug information like what css and js files are include via drupal_add_css/js
 *  )
 */
function ciapi_marketing_page($template = 'klicktipp_marketing_page', $config = array()) {

  //provides theming function for re-use purposes
  require_once drupal_get_path('module', 'klicktipp_marketing') . "/klicktipp_marketing_template.inc";

  //@see bootstrapklicktipp_preprocess_page() in themes/bootstrapklicktipp/template.php
  ciapi_set_variable('ciapi_theme_marketing_page', $template);
  ciapi_set_variable('ciapi_theme_marketing_page_config', $config);

}

/**
 * plugin helpers
 */
function ciapi_get_plugin($pluginid) {
  return Plugin::get_plugin($pluginid);
}

function ciapi_get_plugin_var($pluginid, $key) {
  $plugin = Plugin::get_plugin($pluginid);
  return Plugin::get_plugin_var($plugin, $key);
}

function ciapi_plugin_access($account, $plugin) {
  return klicktipp_plugin_access($account, $plugin);
}

function ciapi_plugin_connected($account, $plugin) {
  return !empty(VarPluginData::GetPluginDataByPluginID($plugin['PluginID'], $account->uid));
}

function ciapi_plugin_create_timestamp_hash($algo = 'sha512', $salt = KLICKTIPP_SALT) {
  return Plugin::pmValidationCreateTimestampHash(time(), $algo, $salt);
}

function ciapi_get_addons_for_upgrade(string $billingInterval, int $digistoreId, \stdClass $account) {
  return Amember::getAddonsForUpgrade($billingInterval, $digistoreId, $account);
}
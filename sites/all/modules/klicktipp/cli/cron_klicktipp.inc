<?php

use App\Klicktipp\ApiMillionVerifier;
use App\Klicktipp\AutoresponderQueue;
use App\Klicktipp\AutoResponders;
use App\Klicktipp\Bee\AccessTokenManager;
use App\Klicktipp\BounceEngine;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Dates;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Errors;
use App\Klicktipp\Includes\Xhprof;
use App\Klicktipp\LandingPage\DomainCheck\LandingPageDomainCheckCron;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\NewsletterPregeneratedQueue;
use App\Klicktipp\NewsletterPreGenerationQueue;
use App\Klicktipp\NewsletterQueue;
use App\Klicktipp\PaymentIPNs;
use App\Klicktipp\ProcessLog;
use App\Klicktipp\QueueCreator;
use App\Klicktipp\LegacyQueueWorker\ActivePaymentsQueueWorker;
use App\Klicktipp\RabbitMq\RabbitMqSubscriberQueueService;
use App\Klicktipp\Requests;
use App\Klicktipp\SendQueue;
use App\Klicktipp\SendQueueToOne;
use App\Klicktipp\Settings;
use App\Klicktipp\SubscriberDuplicateEvents;
use App\Klicktipp\SubscriberDuplicates;
use App\Klicktipp\SubscriberQueue;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\SubscriptionForms;
use App\Klicktipp\SuppressionList;
use App\Klicktipp\Tag;
use App\Klicktipp\TransactionalQueue;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\UserCache;
use App\Klicktipp\UserGroups;
use App\Klicktipp\VarAgedSubscribers;
use GuzzleHttp\Exception\GuzzleException;

Libraries::include('xhprof.inc');


const SPAMBOT_SUPPRESION_DEPREICATION_PERIOD = 30;
/**
 * klicktipp nightly cron
 * this cron runs once a day BEFORE backup/deployment
 * - dont remove lot of datasets here, as they will be missing in the backup
 */
function klicktipp_nightly_cron() {

  // Log the process - Start {
  $data = ProcessLog::StartProcesslog('nightly_cron');
  // Log the process - End }

  /////////////////////
  // make sure user 0 exists

  if (!db_query("SELECT COUNT(*) FROM {users} WHERE uid = :uid", array(':uid' => 0))->fetchField()) {
    db_insert('users')->fields(array('name' => ''))->execute();
    kt_query("UPDATE {users} SET uid = 0 WHERE name='' LIMIT 1");
  }

  ///////////////////// whitelabel domain check
  // 1. create a domain check job for every whitelabel domain
  // 2. check if allowed count of sender ips is exceeded

  $CountIPs = array();

  $ArrayDomains = DomainSet::RetrieveDomains();
  foreach ($ArrayDomains as $domainset) {
    // 1. create queue entry
    $queue = DrupalQueue::get('whitelabeldomaincheck');
    try {
      $queue->createQueue();
      $queue->createItem($domainset);
    } catch(\Exception $exception) {
      watchdog(
        'send_engine',
        'Stop queueing whitelabeldomaincheck domain jobs due to beanstalk error',
        [],
        WATCHDOG_ERROR
      );
      Errors::logException($exception, 'beanstalk');
      break;
    }

    // 2.a. ccunt sender ips
    $CountIPs[$domainset['RelOwnerUserID']] += count($domainset['Data']['hosts'] ?? []);
  }
  // 2.b. check maximum allowed sender ips
  $UsersToCheck = array();
  foreach ($CountIPs as $uid => $count) {
    $account = user_load($uid);
    if (empty($account)) {
      continue;
    }
    $LimitSenderIPs = 0;
    $UserGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID);
    if ($account->status && !empty($UserGroup)) {
      $LimitSenderIPs = $UserGroup['Data']['LimitSenderIPs'];
    }
    if ($count > $LimitSenderIPs) {
      $message = $account->status ?
        /*t(*/'User !name (!uid) has !count hosts configured (allowed !allowed).'/*)*/ :
        /*t(*/'Inactive User !name (!uid) has !count hosts configured.'/*)*/;
      $UsersToCheck[] = l(
        t($message,
          array(
            '!name' => $account->name,
            '!uid' => $uid,
            '!count' => $count,
            '!allowed' => $LimitSenderIPs,
          )),
        "user/$uid/domains",
        array(
          'absolute' => TRUE,
        ));
    }
  }
  // 2.c. send notification email
  if (!empty($UsersToCheck)) {
    // inform admins about problems
    $Message = "<p>These users may have exceeded their limit of (whitelabel) sender IPs</p>";
    $Message .= '<ul><li>' . implode('</li><li>', $UsersToCheck) . '</li></ul>';
    Core::SendNotificationEmail(COREAPI_NOTIFY_EMAIL_SENDERIP_CHECK, $Message);
  }

  ///////////////////// landing page domain check
  (new LandingPageDomainCheckCron())->execute();

  ///////////////////// subscriber limit
  // check subscriber count of all customers and compare it to their respective subscriber limit
  // filter out customers who are about to reach the subscriber limit in a queue worker
  // @see: _klicktipp_subscriber_limit_queue_worker() in includes/marketing.inc
  $SubscriberLimitFieldID = variable_get('klicktipp_marketing_subscriber_limit_custom_field_id', 0);
  if (!empty($SubscriberLimitFieldID)) {
    $QueueData = array();

    // add new products for tier check in queue worker
    $newProductGroups = UserGroups::retrieveIDsOfGroupsWithTierInAccount();

    $result = db_query(
      "SELECT u.uid, g.LimitSubscribers FROM {users} u " .
      "INNER JOIN {user_groups} g ON g.UserGroupID = u.RelUserGroupID AND " .
      "(g.LimitSubscribers > 0 OR u.RelUserGroupID IN (:newProductGroups)) " .
      "WHERE u.status = 1 AND u.access > 0",
      [':newProductGroups' => $newProductGroups]
    );
    while ($DBArray = kt_fetch_array($result)) {
      $QueueData[] = array(
        'uid' => $DBArray['uid'],
        'LimitSubscribers' => $DBArray['LimitSubscribers'],
      );
    }
    if (!empty($QueueData)) {
      ProcessLog::CreateSlicedQueueItems('subscriber_limit_queue', $QueueData, 100);
    }
  }

  ///////////////////// tag inactive subscribers
  $input = [];
  foreach (VarAgedSubscribers::GetUserIDs() as $userID) {
    // first element => userID, second one => last tagged subscriber id (always 0 at the beginning)
    $input[] = [$userID, 0];
  }

  if (!empty($input)) {
    ProcessLog::CreateSlicedQueueItems('tag_passive_subscribers', $input, 10);
  }

  ///////////////////// optin pending
  // delete pending subscribers after a defined delay
  $SliceSize = variable_get('klicktipp_delete_pending_subscribers_queue_limit', 0);
  if (!empty($SliceSize)) {
    $QueueData = array();
    // get all lists from active customers
    $result = db_query("SELECT l.ListID, l.RelOwnerUserID, l.Data FROM {subscriber_lists} l " .
      "INNER JOIN {users} u ON u.uid = l.RelOwnerUserID " .
      "WHERE u.status = 1 AND u.access > 0");
    while ($DBArray = kt_fetch_array($result)) {
      $Data = (empty($DBArray['Data'])) ? array() : unserialize($DBArray['Data']);
      if (!empty($Data['PendingSubscribersDelay'])) {
        // user has defined a delay to delete his pending subscribers
        $QueueData[] = array(
          'UserID' => $DBArray['RelOwnerUserID'],
          'ListID' => $DBArray['ListID'],
          'PendingSubscribersDelay' => $Data['PendingSubscribersDelay'],
        );
      }
    }
    if (!empty($QueueData)) {
      ProcessLog::CreateSlicedQueueItems('delete_pending_subscribers_queue', $QueueData, $SliceSize);
    }
  }


  ///////////////////// inactive users
  // stop campaigns of inactive users
  _klicktipp_stop_inactive_users_cron();

  // --- clean up MillionVerifier cache (remove old entries)
  watchdog('nightly-cron',
    'MillionVerifier: Clean up cache start',
    [], WATCHDOG_INFO);
  $start = time();
  $MV = new ApiMillionVerifier(0);
  $countDeleted = $MV->CleanUpCache();
  watchdog('nightly-cron',
    "MillionVerifier: Clean up cache finished; deleted :count entries in :time seconds",
    [
      ':count' => $countDeleted,
      ':time' => time() - $start
    ], WATCHDOG_INFO);

  $countDeleted = removeOutdatedFromSuppressionList(SPAMBOT_SUPPRESION_DEPREICATION_PERIOD);
  watchdog('nightly-cron',
    "Deleted :count outdated spambot entries from suppression list",
    [
      ':count' => $countDeleted,
    ], WATCHDOG_INFO);


  // Log the process - Start {
  ProcessLog::FinishProcesslog($data);
  // Log the process - End }
}

/**
 * removes all spambot related entries in Suppression list that are older then
 *
 * @var int $period days as of now we set it to 30 days
 *
 * @return int number of deleted rows
 */
function removeOutdatedFromSuppressionList($period) {

    $result = db_query(
      "DELETE FROM {suppression_list} WHERE SuppressionSourceEnum = :SourceSpambot AND LastValidation < :LastValidation",
      [
        ':SourceSpambot' => SuppressionList::SUPPRESSION_LIST_SOURCE_SPAMBOT,
        ':LastValidation' => strtotime("-$period days")
      ]
    );

    return $result->rowCount();
}

/**
 * stop campaigns of inactive users
 * this is cron helper to make it testable in unittests
 */
function _klicktipp_stop_inactive_users_cron() {

  $retention_period = variable_get('klicktipp_stop_campaigns_after_day', 34);
  if (!empty($retention_period)) {
    $retention_date = date("Y-m-d", strtotime("- $retention_period days"));
    $QueueData = array();

    // get all active campaigns from inactive customers with the last payment expired more than 30 days ago
    $payments = issimpletest() ? '{amember_payments}' : 'amember_payments';
    $result = db_query("SELECT c.RelOwnerUserID, c.CampaignID FROM {campaigns} c " .
      " INNER JOIN {users} u ON u.uid = c.RelOwnerUserID AND u.status = 0 AND u.RelUserGroupID > 0 " .
      " INNER JOIN $payments p ON p.member_id = u.amemberid AND p.expire_date < :lifetime".
      " WHERE c.CampaignStatusEnum IN (:CampaignStatusEnum) ".
      " GROUP BY p.member_id HAVING MAX(p.expire_date) < :sometimeinthepast ".
      " LIMIT 0,100",
      array(
        ':CampaignStatusEnum' => Campaigns::$ArrayCampaignStatiSending,
        ':lifetime' => '2037-12-31',
        ':sometimeinthepast' => $retention_date
      )
    );

    while ($DBArray = kt_fetch_array($result)) {
      $QueueData[] = array(
        'UserID' => $DBArray['RelOwnerUserID'],
        'CampaignID' => $DBArray['CampaignID'],
      );
    }
    if (!empty($QueueData)) {
      ProcessLog::CreateSlicedQueueItems('stop_inactive_users_queue', $QueueData, 10);
    }
  }
}

/**
 * klicktipp maintenance cron
 * this cron runs to delete newsletter
 */
function klicktipp_newsletter_deletion_cron() {

  $data = ProcessLog::StartProcesslog("newsletter_deletion_cron");

  // counters for data to be logged
  $starttime = time();
  $campaigns_to_consider = 0;
  $campaigns_to_delete = 0;

  $maximum_campaigns_to_delete = Settings::get('klicktipp_nl_deletion_cron_campaign_to_delete');
  $last_considered_newsletter_send_time = Settings::get('klicktipp_nl_deletion_cron_last_newsletter_send_time');

  // campaigns: delete queue from sent campaigns, that were sent 3 days before, the newest first (by campaign id)
  $QueueData = [];
  $result = db_query("SELECT CampaignID, RelOwnerUserID, SendProcessFinishedOn FROM {campaigns} WHERE CampaignStatusEnum = :CampaignStatusEnum " .
    " AND AutoResponderTriggerTypeEnum IN (:AutoResponderTriggerTypeEnum) ".
    " AND SendProcessFinishedOn > 0 AND SendProcessFinishedOn >= :LastConsideredNewsletterSendTime AND SendProcessFinishedOn < :SendProcessFinishedOn " .
    " ORDER BY SendProcessFinishedOn ASC", [
    ':CampaignStatusEnum' => Campaigns::STATUS_SENT,
    ':AutoResponderTriggerTypeEnum' => [Campaigns::TRIGGER_TYPE_CAMPAIGN, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN],
    ':LastConsideredNewsletterSendTime' => $last_considered_newsletter_send_time,
    ':SendProcessFinishedOn' => strtotime('-72 hours')
  ]);
  while (count($QueueData) < $maximum_campaigns_to_delete && $ArrayCampaign = kt_fetch_array($result)) {
    $count = db_query("SELECT 1 FROM {".NewsletterQueue::TABLE_NAME."} WHERE RelAutoResponderID = :RelAutoResponderID AND RelOwnerUserID = :RelOwnerUserID LIMIT 0,1", array(
      ':RelAutoResponderID' => $ArrayCampaign['CampaignID'],
      ':RelOwnerUserID' => $ArrayCampaign['RelOwnerUserID']
    ))->fetchField();
    $campaigns_to_consider++;
    if ($count > 0) {
      if (empty($QueueData)) {
        $last_considered_newsletter_send_time = $ArrayCampaign['SendProcessFinishedOn'];
      }
      $QueueData[] = $ArrayCampaign;
      $campaigns_to_delete++;
    }
  }
  if (!empty($QueueData)) {
    variable_set('klicktipp_nl_deletion_cron_last_newsletter_send_time', $last_considered_newsletter_send_time);
    ProcessLog::CreateSlicedQueueItems('delete_newsletter_queue', $QueueData);
  }

  ProcessLog::FinishProcesslog($data);

  $cron_duration_in_sec = (time() - $starttime);
  $LogData = [
    '!cron_duration_in_sec' => $cron_duration_in_sec,
    '!maximum_campaigns_to_delete' => $maximum_campaigns_to_delete,
    '!campaigns_to_consider' => $campaigns_to_consider,
    '!campaigns_to_delete' => $campaigns_to_delete,
    '!last_considered_newsletter_send_time' => $last_considered_newsletter_send_time
  ];

  watchdog(
    "newsletter_deletion_cron",
    'cron_duration_in_sec=!cron_duration_in_sec; maximum_campaigns_to_delete=!maximum_campaigns_to_delete; campaigns_to_consider=!campaigns_to_consider; campaigns_to_delete=!campaigns_to_delete',
    $LogData,
    WATCHDOG_INFO,
    ''
  );

}

function klicktipp_tmp_transaction_queue_obsolete_items_deletion_cron() {
  $data = ProcessLog::StartProcesslog('tmp_transaction_queue_obsolete_items_deletion_cron');

  $starttime = time();

  $watchdogType = 'transactional_queue_obsolete_items_deletion_cron';

  $maximumCampaignsToDelete = (int)variable_get('klicktipp_transactional_queue_cron_campaigns_to_delete', 0);
  $maximumEntriesToDelete = (int)variable_get('klicktipp_transactional_queue_cron_entries_to_delete', 0);
  if ($maximumCampaignsToDelete < 1 || $maximumEntriesToDelete < 1) {
    watchdog(
      $watchdogType,
      'don\' proceed since either campaign limit (!maximum_campaigns_to_delete) or item limit (!maximum_entries_to_delete) is < 1',
      [
        '!maximum_campaigns_to_delete' => $maximumCampaignsToDelete,
        '!maximum_entries_to_delete' => $maximumEntriesToDelete
      ],
      WATCHDOG_INFO,
    );
    ProcessLog::FinishProcesslog($data);
    return;
  }
  $lastCampaignIdVar = 'klicktipp_transactional_queue_obsolete_items_deletion_cron_last_campaign_id';
  $lastConsideredCampaignID = variable_get($lastCampaignIdVar, 0);
  $campaigns = db_query(
    'SELECT CampaignID, RelOwnerUserID FROM {campaigns} ' .
    'WHERE CampaignID > :LastCampaignID AND AutoResponderTriggerTypeEnum IN (:TriggerTypeEnums) ' .
    'ORDER BY CampaignID ASC ' .
    'LIMIT ' . $maximumCampaignsToDelete,
    [':LastCampaignID' => $lastConsideredCampaignID, ':TriggerTypeEnums' => Campaigns::$ArrayTriggerTypesAutoresponder]
  )->fetchAll(PDO::FETCH_ASSOC);


  $processedItemBlocks = 0;
  $processedCampaigns = 0;
  while ($processedItemBlocks < $maximumCampaignsToDelete) {
    $campaign = current($campaigns);
    if ($campaign === FALSE) {
      break;
    }
    $deleted = TransactionalQueue::DeleteCampaignItems($campaign['CampaignID'], $campaign['RelOwnerUserID'], $maximumEntriesToDelete);
    $processedItemBlocks++;
    if ($deleted < $maximumEntriesToDelete) {
      $processedCampaigns++;
      variable_set($lastCampaignIdVar, $campaign['CampaignID']);
      next($campaigns);
    }
  }

  ProcessLog::FinishProcesslog($data);

  watchdog(
    $watchdogType,
    'cron_duration_in_sec=!cron_duration_in_sec; maximum_campaigns_to_delete=!maximum_campaigns_to_delete; maximum_entries_to_delete=!maximum_entries_to_delete; processed_campaigns=!processed_campaigns',
    [
      '!cron_duration_in_sec' => time() - $starttime,
      '!maximum_campaigns_to_delete' => $maximumCampaignsToDelete,
      '!maximum_entries_to_delete' => $maximumEntriesToDelete,
      '!processed_campaigns' => $processedCampaigns,
      '!last_processed_campaign' => $campaigns[$processedCampaigns - 1] ?? []
    ],
    WATCHDOG_INFO,
  );
}

function klicktipp_tmp_autoresponder_queue_obsolete_items_deletion_cron() {
  $data = ProcessLog::StartProcesslog('tmp_autoresponder_queue_obsolete_items_deletion_cron');

  $starttime = time();

  $watchdogType = 'autoresponder_queue_obsolete_items_deletion_cron';

  $maximumCampaignsToDelete = (int)variable_get('klicktipp_autoresponder_queue_cron_campaigns_to_delete', 0);
  $maximumEntriesToDelete = (int)variable_get('klicktipp_autoresponder_queue_cron_entries_to_delete', 0);
  if ($maximumCampaignsToDelete < 1 || $maximumEntriesToDelete < 1) {
    watchdog(
      $watchdogType,
      'don\' proceed since either campaign limit (!maximum_campaigns_to_delete) or item limit (!maximum_entries_to_delete) is < 1',
      [
        '!maximum_campaigns_to_delete' => $maximumCampaignsToDelete,
        '!maximum_entries_to_delete' => $maximumEntriesToDelete
      ],
      WATCHDOG_INFO,
    );
    ProcessLog::FinishProcesslog($data);
    return;
  }
  $lastCampaignIdVar = 'klicktipp_autoresponder_queue_obsolete_items_deletion_cron_last_campaign_id';
  $lastConsideredCampaignID = variable_get($lastCampaignIdVar, 0);
  $campaigns = db_query(
    'SELECT CampaignID, RelOwnerUserID FROM {campaigns} ' .
    'WHERE CampaignID > :LastCampaignID AND AutoResponderTriggerTypeEnum = :TriggerTypeEnum ' .
    'ORDER BY CampaignID ASC ' .
    'LIMIT ' . $maximumCampaignsToDelete,
    [':LastCampaignID' => $lastConsideredCampaignID, ':TriggerTypeEnum' => Campaigns::TRIGGER_TYPE_PROCESSFLOW]
  )->fetchAll(PDO::FETCH_ASSOC);


  $processedItemBlocks = 0;
  $processedCampaigns = 0;
  while ($processedItemBlocks < $maximumCampaignsToDelete) {
    $campaign = current($campaigns);
    if ($campaign === FALSE) {
      break;
    }
    $deleted = AutoresponderQueue::DeleteCampaignItems($campaign['CampaignID'], $campaign['RelOwnerUserID'], $maximumEntriesToDelete);
    $processedItemBlocks++;
    if ($deleted < $maximumEntriesToDelete) {
      $processedCampaigns++;
      variable_set($lastCampaignIdVar, $campaign['CampaignID']);
      next($campaigns);
    }
  }

  ProcessLog::FinishProcesslog($data);

  watchdog(
    $watchdogType,
    'cron_duration_in_sec=!cron_duration_in_sec; maximum_campaigns_to_delete=!maximum_campaigns_to_delete; maximum_entries_to_delete=!maximum_entries_to_delete; processed_campaigns=!processed_campaigns',
    [
      '!cron_duration_in_sec' => time() - $starttime,
      '!maximum_campaigns_to_delete' => $maximumCampaignsToDelete,
      '!maximum_entries_to_delete' => $maximumEntriesToDelete,
      '!processed_campaigns' => $processedCampaigns,
      '!last_processed_campaign' => $campaigns[$processedCampaigns - 1] ?? []
    ],
    WATCHDOG_INFO,
  );
}

/**
 * klicktipp maintenance cron
 * this cron runs to delete old newsletter tags
 */
function klicktipp_newsletter_tags_deletion_cron() {

  $data = ProcessLog::StartProcesslog("newsletter_tags_deletion_cron");

  // counters for data to be logged
  $starttime = time();
  $campaigns_to_consider = 0;
  $taggings_to_consider= 0;
  $taggings_to_delete = 0;

  $maximum_campaigns_to_delete = Settings::get('klicktipp_nl_tags_deletion_cron_campaign_to_delete');
  $last_considered_newsletter_send_time = Settings::get('klicktipp_nl_tags_deletion_cron_last_newsletter_send_time');

  // delete taggings from sent campaigns, that were sent 6 months before

  $QueueData = [];
  $result = db_query("SELECT CampaignID, RelOwnerUserID, SendProcessFinishedOn FROM {campaigns} WHERE CampaignStatusEnum = :CampaignStatusEnum " .
    " AND AutoResponderTriggerTypeEnum IN (:AutoResponderTriggerTypeEnum) " .
    " AND SendProcessFinishedOn > 0 AND SendProcessFinishedOn >= :LastConsideredNewsletterSendTime AND SendProcessFinishedOn < :SendProcessFinishedOn " .
    " ORDER BY SendProcessFinishedOn ASC", [
    ':CampaignStatusEnum' => Campaigns::STATUS_SENT,
    ':AutoResponderTriggerTypeEnum' => [Campaigns::TRIGGER_TYPE_CAMPAIGN, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN],
    ':LastConsideredNewsletterSendTime' => $last_considered_newsletter_send_time,
    ':SendProcessFinishedOn' => strtotime('-6 months')
  ]);
  while (count($QueueData) < $maximum_campaigns_to_delete && $ArrayCampaign = kt_fetch_array($result)) {
    $campaigns_to_consider++;
    foreach (CampaignsNewsletter::RELATED_TAG_CATEGORIES as $cat) {
      // see Tag::RetrieveTagOfEntity
      $taggings_to_consider++;
      // select all tags of the campaign and smarttag category which still have tagging for deletion (not untagging)
      if ($Tag = kt_fetch_array(db_query("SELECT tt.* FROM {tag} tt ".
        " INNER JOIN {tagging} ta ON tt.TagID = ta.RelTagID AND tt.RelOwnerUserID = ta.RelOwnerUserID ".
        " WHERE tt.RelOwnerUserID = :RelOwnerUserID AND tt.Category = :Category AND tt.EntityID = :EntityID LIMIT 0,1", array(
        ':RelOwnerUserID' => $ArrayCampaign['RelOwnerUserID'],
        ':Category' => $cat,
        ':EntityID' => $ArrayCampaign['CampaignID']
      )))) {
        if (empty($QueueData)) {
          $last_considered_newsletter_send_time = $ArrayCampaign['SendProcessFinishedOn'];
        }
        $QueueData[] = $Tag;
        $taggings_to_delete++;
      }
    }
  }
  if (!empty($QueueData)) {
    variable_set('klicktipp_nl_tags_deletion_cron_last_newsletter_send_time', $last_considered_newsletter_send_time);
    ProcessLog::CreateSlicedQueueItems('delete_taggings', $QueueData);
  }

  ProcessLog::FinishProcesslog($data);

  $cron_duration_in_sec = (time() - $starttime);
  $LogData = [
    '!cron_duration_in_sec' => $cron_duration_in_sec,
    '!maximum_campaigns_to_delete' => $maximum_campaigns_to_delete,
    '!campaigns_to_consider' => $campaigns_to_consider,
    '!taggings_to_consider' => $taggings_to_consider,
    '!taggings_to_delete' => $taggings_to_delete,
    '!last_considered_newsletter_send_time' => $last_considered_newsletter_send_time
  ];

  watchdog(
    "newsletter_tags_deletion_cron",
    'cron_duration_in_sec=!cron_duration_in_sec; maximum_campaigns_to_delete=!maximum_campaigns_to_delete; campaigns_to_consider=!campaigns_to_consider; taggings_to_consider=!taggings_to_consider; taggings_to_delete=!taggings_to_delete',
    $LogData,
    WATCHDOG_INFO,
    ''
  );

}

/**
 * klicktipp daily cron
 * this cron runs once a day AFTER backup/deployment
 */
define ('KLICKTIPP_PROFILER_MORNING_CRON', 'morning_cron');

function klicktipp_morning_cron() {

  // Log the process - Start {
  $data = ProcessLog::StartProcesslog('morning_cron');
  // Log the process - End }

  // counters for data, that the profiler cant distinguish (not different php calls)
  $starttime = time();
  if (variable_get('klicktipp_mcp_enabled', 0)) {
    // start profiling
    Xhprof::start(KLICKTIPP_PROFILER_MORNING_CRON);
  }

  /////////////////////
  // re-terminate birthday autoresponders
  AutoResponders::ReterminateBirthdayAutoResponders();

  /////////////////////
  // payments

  // make sure that a payments history entry is written for the day
  PaymentIPNs::GetCurrencyConversionFactor(time());

  // check active payments (but check first if this queue is empty)
  if ($beanstalk_queue = new BeanstalkdQueue(NULL, TRUE)) {
    if (_klicktipp_beanstalk_queue_empty($beanstalk_queue, 'active_payments_queue')) {
      _klicktipp_create_active_payment_jobs();
    }
  }
  else {
    Errors::unexpected("klicktipp_morning_cron unable to connect to beanstalk");
  }

  /////////////////////

  // delete old processlog data
  db_delete('processlog')
    ->condition('started', strtotime("-8 days"), '<')
    ->execute();

  // delete old data from fbl reports
  db_delete('fbl_reports')
    ->condition('DateOfReceive', strtotime("-90 days"), '<')
    ->execute();

  /////////////////////

  //remove entries older than 30 days, transfer totals, update paths
  klicktipp_help_log_update();

  /*
   * count customers of klicktipp marketing account and store them in a file on the disk
   * @use: calculate TPC (tickets per customer) for NPS (Net Promomoter Score) statistics
   * @see: function klicktipp_npscore_overview_form in forms/npscore.inc
   * @note: function klicktipp_npscore_calculate_customercount calculates yesterdays customer count
   */
  $helpscout_webhook_secret_key = variable_get('klicktipp_helpscout_nps_webhook_secret_key', '');
  if (!empty($helpscout_webhook_secret_key)) {
    // only run this, if HelpScout webhook is activated
    Libraries::include("npscore.inc", "/forms");
    klicktipp_npscore_calculate_customercount();
  }

  // Log the process - Start {
  ProcessLog::FinishProcesslog($data);
  // Log the process - End }

  /////////////////////
  // klicktipp monitor report

  if (module_exists('prod_check')) {
    Libraries::include("prod_check.inc");

    klicktipp_monitor_cron();
  }

  if (variable_get('klicktipp_mcp_enabled', 0)) {
    // stop profiler and log results
    $seconds_needed = (time() - $starttime);
    Xhprof::stop(KLICKTIPP_PROFILER_MORNING_CRON, TRUE);
    $PerfData = array(
      '!seconds_needed' => $seconds_needed,
    );
    watchdog('perf_morning_cron', 'morning cron took !seconds_needed s', $PerfData, WATCHDOG_INFO, '');
  }
}

/**
 * klicktipp every 10 minutes cron
 */
function klicktipp_every10minutes_cron() {
  /* this cron runs once every 10 minutes */
  // Log the process
  $data = ProcessLog::StartProcesslog('every10minutes_cron');

  /////////////////////
  // create zy0.de spam trap fetch job
  // dont create jobs, if old once are not finished
  if (variable_get('klicktipp_zy0de_enabled', 0)) {
    ProcessLog::CreateUniqueQueueItem('zy0de_queue');
  };

  ///////////////////// senderscore
  // create a random senderscore job every 10 minutes
  Libraries::include("senderscore.inc");
  klicktipp_senderscore_cron_run();

  ///////////////////// update chicklet
  // create jobs to update the chicklet.js on S3 for every User where the active subscriber count changed
  SubscriptionForms::CheckChickletUpdate();

  ///////////////////// cleanup processlog jobs
  // try to recover broken beanstalk jobs
  if ($queue = new BeanstalkdQueue(NULL, TRUE)) {
    klicktipp_finalize_queue_jobs($queue);
  }
  else {
    Errors::unexpected("klicktipp_every10minutes_cron unable to connect to beanstalk");
  }

  // remove old sessions
  // replacement of "_drupal_session_garbage_collection"
  $lifetime = (int) ini_get('session.gc_maxlifetime');
  db_delete('sessions')
    ->condition('timestamp', time() - $lifetime, '<')
    ->execute();

  // Log the process
  ProcessLog::FinishProcesslog($data);
}

/**
 * check the beanstalk queue for active jobs
 */
function _klicktipp_beanstalk_queue_empty($beanstalk_queue, $queue_name = '') {
  try {
    $result = $beanstalk_queue->statsTube($queue_name);
    if (empty($result)) {
      // return "not empty" on error
      return FALSE;
    }
    $result = reset($result)->getArrayCopy();
    foreach ($result as $key => $val) {
      if (in_array($key, ['current-jobs-urgent', 'current-jobs-ready', 'current-jobs-reserved', 'current-jobs-delayed'])) {
        if ($val > 0) {
          // queue is "not empty"
          return FALSE;
        }
      }
    }
  } catch (Exception $e) {
    // return "not empty" on error
    return FALSE;
  }
  // queue is empty
  return TRUE;
}

/**
 * hardening of the beanstalk queue
 * try to run finalization ops, if the queue items were lost
 */
function klicktipp_finalize_queue_jobs($beanstalk_queue) {

  // the beanstalk queues
  $queues = array_keys(QueueCreator::queueInfo());

  // read committed
  // find not terminated (newest one active longer than an hour, so we not only process the oldest ones)
  $result = db_query("SELECT * FROM {processlog} WHERE started <= :started AND finished = 0 ORDER BY started DESC", array(':started' => strtotime("-1 hours")));
  while ($Log = kt_fetch_array($result)) {
    $logdata = empty($Log['Data']) ? [] : unserialize($Log['Data']);

    if (in_array($Log['QueueName'], $queues)) {
      ///// Check if this job is still in the beanstalk queue
      // Actually, we cant do that. Beanstalk allows to view the next ready job in a tube,
      // but not to search or iterate over the jobs. So we need to wait for the tube
      // to be empty. Then we know: the job will never finish.
      if (_klicktipp_beanstalk_queue_empty($beanstalk_queue, $Log['QueueName'])) {
        // no jobs in this tube, so the current one will never finish

        // do the finalization code from the worker
        switch ($Log['QueueName']) {
          // NOTE: please add queues alphabetically
          case 'bouncecheck':
            // _klicktipp_bouncecheck_queue_worker: no final op
            break;
          case 'chicklet_update':
            // SubscriptionForms::UpdateChickletS3: no final op
            break;
          case 'create_queue':
            // QueueCreator::createQueueWorker: no final op
            break;
          case 'delete_taggings':
            // _klicktipp_delete_taggings_worker: no final op
            break;
          case 'delete_newsletter_queue':
            // _klicktipp_delete_newsletter_queue_worker: no final op
            break;
          case 'email_similarity':
            break;
          case 'export_subscriber_duplicates':
            // _klicktipp_subscriber_duplicates_export_queue: no final up
            break;
          case 'export_subscribers':
            // _klicktipp_subscriber_export_queue_worker: create csv TODO ?
            // It may be not a good idea to send an imcomplete csv to the customer.
            // At least we should notify us about the broken job ...
            $error = [
              '!log' => $Log,
              '!data' => $logdata,
            ];
            watchdog('kt-hardening',"Export Subscribers: found broken job. User might miss email with CSV.", $error, WATCHDOG_ERROR);
            break;
          case 'npscore_queue':
            // _klicktipp_npscore_queue_worker: no final op
            break;
          case 'outbound_event':
            // ToolOutbound::OutboundEventWorker: no final op
            break;
          case 'request_forwarders_queue':
            // _klicktipp_request_forwarders_queue_worker: no final op
            break;
          case 'send_nexmo_sms':
            // SendQueue::sendNexmoSmsQueue: no final op
            break;
          case 'send_twilio_sms':
            // SendQueue::sendTwilioSmsQueue: no final op
            break;
          case 'send_queue':
            // SendQueue::QueueWorker: put campaign in next state
            //TODO unfortunately we dont have the user in process_log, so get it by entity id only
            $CampaignID = $Log['EntityID'];
            $ArrayCampaign = Campaigns::RetrieveCampaignByID($CampaignID);
            SendQueue::FinalizeSendQueue($ArrayCampaign);
            break;
          case 'send_queue_to_one':
            // SendQueueToOne::QueueWorker: put campaign in next state
            //TODO unfortunately we dont have the user in process_log, so get it by entity id only
            $CampaignID = $Log['EntityID'];
            $ArrayCampaign = Campaigns::RetrieveCampaignByID($CampaignID);
            SendQueueToOne::FinalizeSendQueue($ArrayCampaign);
            break;
          case 'senderscore_queue':
            // _klicktipp_senderscore_queue_worker: no final op
            break;
          case 'softbouncecheck':
            // _klicktipp_softbouncecheck_queue_worker: no final op
            break;
          case 'subscriber_limit_queue':
            // _klicktipp_subscriber_limit_queue_worker: no final op
            break;
          case 'subscriber_queue':
            // SubscriberQueue::QueueWorker: no final op
            break;
          case 'tag_subscribers':
            // klicktipp_subscriber_tagging_queue_worker: no final op
            // There is an user notification at the end of the job for interactive batch only.
            break;
          case TransactionalQueue::QUEUENAME:
            // TransactionalQueue::QueueWorker: no final op
            // - semaphore unlocks itself
            break;
          case AutoresponderQueue::QUEUENAME:
            // AutoresponderQueue::QueueWorker: no final op
            // - semaphore unlocks itself
            break;
          case NewsletterQueue::QUEUENAME:
            // NewsletterQueue::QueueWorker: no final op
            // - semaphore unlocks itself
            break;
          case 'whitelabeldomaincheck':
            // DomainSet::CheckWhitelabelDomain: no final op
            break;
          case 'tag_passive_subscribers':
            // VarAgedSubscribers::TagAgedSubscribers: no final op
            break;
          case 'zy0de_queue':
            // _klicktipp_zy0de_queue_worker: no final op
            break;
          case 'import_subscribers_validate':
          case 'import_subscribers_import':
            // sometimes jobs get burried so the validation/import process never finished
            // just re-try the job
            ProcessLog::ReQueueItem($Log['QueueName'], $logdata['jobdata']);
            watchdog('kt-hardening', "Import hardening for !queueName", [
              '!queueName' => $Log['QueueName'],
              '!data' => $logdata
            ], WATCHDOG_ERROR);
            break;
          default:
            $error = [
              '!LogID' => $Log['LogID'],
              '!queuename' => $Log['QueueName'],
              '!data' => $Log,
            ];
            watchdog('kt-hardening',"processlog no hardening for !queuename: LogID !LogID", $error, WATCHDOG_ERROR);
            break;
        }
        // we will now finalize the jobs (even we lost it)
        ProcessLog::Finalize($Log['LogID'], $logdata);
        // log failure
        $error = [
          '!LogID' => $Log['LogID'],
          '!queuename' => $Log['QueueName'],
          '!data' => $Log,
        ];
        watchdog('kt-hardening',"processlog item from !queuename lost: LogID !LogID", $error, WATCHDOG_ERROR);
      }
    }
    else {
      // non beanstalk processlog ( = cron)
      // every job outside the queue system should be finished within minutes

      // finalize the job
      ProcessLog::Finalize($Log['LogID'], $logdata);
      // log failure
      $error = [
        '!LogID' => $Log['LogID'],
        '!queuename' => $Log['QueueName'],
        '!data' => $Log,
      ];
      watchdog('kt-hardening', "processlog cron !queuename lost: LogID !LogID", $error, WATCHDOG_ERROR);
    }
  }
}

/**
 * klicktipp hourly cron
 */
function klicktipp_hourly_cron() {

  /////////////////////
  // delete old data from short urls
  $ShortUrlMaxActiveLinks = Settings::get('klicktipp_shorturls_max_active_links');
  $lastid = db_query("SELECT MAX(LinkID) FROM {emails_shortlink}")->fetchField();
  $firstid = $lastid - intval(0.9 * $ShortUrlMaxActiveLinks);
  db_query("DELETE FROM {emails_shortlink} WHERE LinkID < :LinkID", array(':LinkID' => $firstid));

  /////////////////////
  // collect stats for transactional queue
  $customer_slices = Settings::get('klicktipp_customers_in_separate_slices');
  $divisor = variable_get('klicktipp_wts_slices', 0);
  if (in_array($divisor, array(2,4,8,16,32,64,128,256,512,1024))) {

    // transactional_email_queue
    $campaigns_trans = [];
    $slices_trans = [];
    $total_trans = 0;
    // get lower limit of what we expect to be executed in one run
    // if a campaign has more pending than this value, it is able to block the queue
    $slowlimit = 60 * variable_get('klicktipp_wts_eps', 100);
    // almost the same query as in transactional send, but with count only
    //@note: Multivalue taggings - count all campaign runs
    $BigQueryResults = db_query("SELECT q.RelOwnerUserID, q.RelAutoResponderID, q.ModOfCampaignID, COUNT(*) as count FROM {".TransactionalQueue::TABLE_NAME."} q USE INDEX (idx_status_tts_owner) " .
      " INNER JOIN {campaigns} c ON c.CampaignID = q.RelAutoResponderID AND c.RelOwnerUserID = q.RelOwnerUserID " .
      " INNER JOIN {users} u ON u.uid = q.RelOwnerUserID AND u.status = 1 " .
      " WHERE q.StatusEnum IN (:StatusEnum) AND q.TimeToSend <= :TimeToSend AND c.CampaignStatusEnum IN (:CampaignStatusEnum) " .
      " GROUP BY q.RelAutoResponderID",
      [
        ':StatusEnum' => [TransactionEmails::STATUS_PENDING, TransactionEmails::STATUS_PENDING_AFTER],
        ':TimeToSend' => time(),
        ':CampaignStatusEnum' => Campaigns::$ArrayCampaignStatiSending,
      ]);
    while ($ArrayQueueData = kt_fetch_array($BigQueryResults)) {
      $dbmod = $ArrayQueueData['ModOfCampaignID'];
      if ($dbmod < 0) {
        // customer slices
        $remainder = $dbmod;
        if (!in_array(-1 * $remainder, $customer_slices)) {
          watchdog('trans_queue_error', TransactionalQueue::TABLE_NAME.': data in abandoned slice !remainder', ['!remainder' => $remainder], WATCHDOG_ERROR);
        }
      }
      else {
        $remainder = intval(floor($divisor * $dbmod / 1024));
      }
      $slices_trans[$remainder] += $ArrayQueueData['count'];
      $total_trans += $ArrayQueueData['count'];
      // monitor potentially blocking campaigns
      if ($ArrayQueueData['count'] > $slowlimit || $remainder < 0) {
        $campaigns_trans[$ArrayQueueData['RelAutoResponderID']] = [
          'remainder' => $remainder,
          'uid' => $ArrayQueueData['RelOwnerUserID'],
          'count' => $ArrayQueueData['count'],
        ];
      }
    }

    // autoresponder_queue
    $campaigns_autoresponder = [];
    $slices_autoresponder = [];
    $total_autoresponder = 0;
    // get lower limit of what we expect to be executed in one run
    // if a campaign has more pending than this value, it is able to block the queue
    $slowlimit = 60 * variable_get('klicktipp_wts_eps', 100);
    // almost the same query as in transactional send, but with count only
    //@note: Multivalue taggings - count all campaign runs
    $BigQueryResults = db_query("SELECT q.RelOwnerUserID, q.RelAutoResponderID, q.ModOfCampaignID, COUNT(*) as count FROM {".AutoresponderQueue::TABLE_NAME."} q USE INDEX (idx_status_tts_owner) " .
      " INNER JOIN {campaigns} c ON c.CampaignID = q.RelAutoResponderID AND c.RelOwnerUserID = q.RelOwnerUserID " .
      " INNER JOIN {users} u ON u.uid = q.RelOwnerUserID AND u.status = 1 " .
      " WHERE q.StatusEnum = :StatusEnum AND q.TimeToSend <= :TimeToSend AND c.CampaignStatusEnum IN (:CampaignStatusEnum) " .
      " GROUP BY q.RelAutoResponderID",
      [
        ':StatusEnum' => TransactionEmails::STATUS_PENDING,
        ':TimeToSend' => time(),
        ':CampaignStatusEnum' => Campaigns::$ArrayCampaignStatiSending,
      ]);
    while ($ArrayQueueData = kt_fetch_array($BigQueryResults)) {
      $dbmod = $ArrayQueueData['ModOfCampaignID'];
      if ($dbmod < 0) {
        // customer slices
        $remainder = $dbmod;
        if (!in_array(-1 * $remainder, $customer_slices)) {
          watchdog('autoresponder_queue_error', AutoresponderQueue::TABLE_NAME.': data in abandoned slice !remainder', ['!remainder' => $remainder], WATCHDOG_ERROR);
        }
      }
      else {
        $remainder = intval(floor($divisor * $dbmod / 1024));
      }
      $slices_autoresponder[$remainder] += $ArrayQueueData['count'];
      $total_autoresponder += $ArrayQueueData['count'];
      // monitor potentially blocking campaigns
      if ($ArrayQueueData['count'] > $slowlimit || $remainder < 0) {
        $campaigns_autoresponder[$ArrayQueueData['RelAutoResponderID']] = [
          'remainder' => $remainder,
          'uid' => $ArrayQueueData['RelOwnerUserID'],
          'count' => $ArrayQueueData['count'],
        ];
      }
    }

    // newsletter_queue
    $campaigns_newsletter = [];
    $slices_newsletter = [];
    $total_newsletter = 0;
    // get lower limit of what we expect to be executed in one run
    // if a campaign has more pending than this value, it is able to block the queue
    $slowlimit = 60 * variable_get('klicktipp_wts_eps', 100);
    // almost the same query as in transactional send, but with count only
    //@note: Multivalue taggings - count all campaign runs
    $BigQueryResults = db_query("SELECT q.RelOwnerUserID, q.RelAutoResponderID, q.ModOfCampaignID, COUNT(*) as count FROM {".NewsletterQueue::TABLE_NAME."} q USE INDEX (idx_status_tts_owner) " .
      " INNER JOIN {campaigns} c ON c.CampaignID = q.RelAutoResponderID AND c.RelOwnerUserID = q.RelOwnerUserID " .
      " INNER JOIN {users} u ON u.uid = q.RelOwnerUserID AND u.status = 1 " .
      " WHERE q.StatusEnum = :StatusEnum AND q.TimeToSend <= :TimeToSend AND c.CampaignStatusEnum IN (:CampaignStatusEnum) " .
      " GROUP BY q.RelAutoResponderID",
      [
        ':StatusEnum' => TransactionEmails::STATUS_PENDING,
        ':TimeToSend' => time(),
        ':CampaignStatusEnum' => Campaigns::$ArrayCampaignStatiSending,
      ]);
    while ($ArrayQueueData = kt_fetch_array($BigQueryResults)) {
      $dbmod = $ArrayQueueData['ModOfCampaignID'];
      if ($dbmod < 0) {
        // customer slices
        $remainder = $dbmod;
        if (!in_array(-1 * $remainder, $customer_slices)) {
          watchdog('newsletter_queue_error', NewsletterQueue::TABLE_NAME.': data in abandoned slice !remainder', ['!remainder' => $remainder], WATCHDOG_ERROR);
        }
      }
      else {
        $remainder = intval(floor($divisor * $dbmod / 1024));
      }
      $slices_newsletter[$remainder] += $ArrayQueueData['count'];
      $total_newsletter += $ArrayQueueData['count'];
      // monitor potentially blocking campaigns
      if ($ArrayQueueData['count'] > $slowlimit || $remainder < 0) {
        $campaigns_newsletter[$ArrayQueueData['RelAutoResponderID']] = [
          'remainder' => $remainder,
          'uid' => $ArrayQueueData['RelOwnerUserID'],
          'count' => $ArrayQueueData['count'],
        ];
      }
    }

    // trigger_queue
    $slices_trigger = [];
    $total_trigger = 0;
    // count by userid and recalc slice (see _klicktipp_trigger_queue_worker)
    // TODO: RabbitMQ Version
    $BigQueryResults = db_query("SELECT q.ShardID, COUNT(*) AS count FROM {subscriber_queue} q " .
      " WHERE JobType = :JobType GROUP BY q.ShardID ORDER BY q.ShardID",
      array(
        ':JobType' => SubscriberQueue::JOBTYPE_TRIGGER_QUEUE
      )
    );
    while ($ArrayQueueData = kt_fetch_array($BigQueryResults)) {
      $dbmod = $ArrayQueueData['ShardID'];
      if ($dbmod < 0) {
        // customer slices
        $remainder = $dbmod;
        if (!in_array(-1 * $remainder, $customer_slices)) {
          watchdog('trans_queue_error', 'subscriber_queue: data in abandoned slice !remainder', ['!remainder' => $remainder], WATCHDOG_ERROR);
        }
      }
      else {
        $remainder = intval(floor($divisor * $dbmod / 1024));
      }
      $slices_trigger[$remainder] += $ArrayQueueData['count'];
      $total_trigger += $ArrayQueueData['count'];
    }

    // output
    $ArrayFieldnValues = [
      '!total' => $total_trans + $total_autoresponder + $total_newsletter,
      '!total_trans' => $total_trans,
      '!total_autoresponder' => $total_autoresponder,
      '!total_newsletter' => $total_newsletter,
      '!slices_trans' => $slices_trans,
      '!slices_autoresponder' => $slices_autoresponder,
      '!slices_newsletter' => $slices_newsletter,
      '!campaigns_trans' => $campaigns_trans,
      '!campaigns_autoresponder' => $campaigns_autoresponder,
      '!campaigns_newsletter' => $campaigns_newsletter,
      '!total_trigger' => $total_trigger,
      '!slices_trigger' => $slices_trigger,
    ];
    watchdog('trans_queue_watch', 'Transactional Send !total to process ', $ArrayFieldnValues, WATCHDOG_INFO, '');
  }
}

/**
 * this cron runs once a minute
 */
function klicktipp_minutely_cron() {

  ///////////////////// bounce check
  // create a number of random bounce check job every minute
  $queue = DrupalQueue::get('bouncecheck');
  $queue->createQueue();
  $bouncecheck_limit = Settings::get('klicktipp_bouncecheck_limit');
  $result = kt_query("SELECT * FROM {suppression_list} " .
    " WHERE SuppressionSourceEnum = %d AND LastValidation < %d AND LastValidation <> %d " .
    " AND SuppressionID <= (SELECT RAND()) * (SELECT MAX(SuppressionID) FROM {suppression_list}) LIMIT $bouncecheck_limit",
    SuppressionList::SUPPRESSION_LIST_SOURCE_HARDBOUNCED, strtotime('-32 days'), SuppressionList::SUPPRESSION_LIST_HARDBOUNCE_DONOTREVALIDATE
  );
  while ($ArraySuppressionList = kt_fetch_array($result)) {
    $DataArray = array(
      'UserID' => 0,
      'EmailAddress' => $ArraySuppressionList['EmailAddress'],
      'OmitBounceCheck' => FALSE,
      'NotifyUser' => FALSE,
    );
    try {
      $queue->createItem($DataArray);
    } catch(\Exception $exception) {
      watchdog(
        'send_engine',
        'Stop queueing bouncecheck domain jobs due to beanstalk error',
        [],
        WATCHDOG_ERROR
      );
      Errors::logException($exception, 'beanstalk');
      break;
    }
  }

  ///////////////////// soft bounce check
  // create a number of random soft bounce resets in a job every minute
  if (time() > strtotime(date('Y-m-d 08:00')) && time() < strtotime(date('Y-m-d 21:00:00'))) {
    ProcessLog::CreateUniqueQueueItem('softbouncecheck');
  }

  ///////////////////// request forwarders check
  // check and create request forwarders file
  if (!file_exists(REQUEST_FORWARDERS_DIR . REQUEST_FORWARDERS_FILE)) {
    ProcessLog::CreateUniqueQueueItem('request_forwarders_queue');
  }

}

/*
 * a cron running every minute to create one job for each slice
 */
function klicktipp_transactional_cron() {

  // cleanup transactional queue
  // we can check at this point, that there are no running queue jobs for the transactional_queue
  // so lets cleanup all "sending" entries, as there shouldn't be any

  if ($queue = new BeanstalkdQueue(NULL, TRUE)) {
    if (_klicktipp_beanstalk_queue_empty($queue, TransactionalQueue::QUEUENAME)) {
      // no beanstalk jobs running or pending
      try {
        db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} q SET q.StatusEnum = :StatusEnumFailed, q.StatusReason = :StatusReason " .
          " WHERE q.StatusEnum = :StatusEnumSending", [
          ':StatusEnumFailed' => TransactionEmails::STATUS_FAILED,
          ':StatusReason' => TRANSACTION_REASON_HARDENING,
          ':StatusEnumSending' => TransactionEmails::STATUS_SENDING,
        ]);
      } catch (PDOException $e) {
        // there is a deadlock condition somewhere - this query is not important, but we should know (and avoid)
        Errors::unexpected('klicktipp_transactional_cron pdo on hardening', array('exception' => $e));
      }
    }
    if (
      _klicktipp_beanstalk_queue_empty($queue, NewsletterQueue::QUEUENAME) &&
      _klicktipp_beanstalk_queue_empty($queue, NewsletterPreGenerationQueue::QUEUENAME)
    ) {
      try {
        db_query("UPDATE {".NewsletterQueue::TABLE_NAME."} q SET q.StatusEnum = :StatusEnumFailed, q.StatusReason = :StatusReason " .
          " WHERE q.StatusEnum = :StatusEnumSending", [
          ':StatusEnumFailed' => TransactionEmails::STATUS_FAILED,
          ':StatusReason' => TRANSACTION_REASON_HARDENING,
          ':StatusEnumSending' => TransactionEmails::STATUS_SENDING,
        ]);
      } catch (PDOException $e) {
        // there is a deadlock condition somewhere - this query is not important, but we should know (and avoid)
        Errors::unexpected('klicktipp_transactional_cron pdo on hardening (newsletter)', array('exception' => $e));
      }
    }
    if (_klicktipp_beanstalk_queue_empty($queue, AutoresponderQueue::QUEUENAME)) {
      try {
        db_query("UPDATE {".AutoresponderQueue::TABLE_NAME."} q SET q.StatusEnum = :StatusEnumFailed, q.StatusReason = :StatusReason " .
          " WHERE q.StatusEnum = :StatusEnumSending", [
          ':StatusEnumFailed' => TransactionEmails::STATUS_FAILED,
          ':StatusReason' => TRANSACTION_REASON_HARDENING,
          ':StatusEnumSending' => TransactionEmails::STATUS_SENDING,
        ]);
      } catch (PDOException $e) {
        // there is a deadlock condition somewhere - this query is not important, but we should know (and avoid)
        Errors::unexpected('klicktipp_transactional_cron pdo on hardening (autoresponder)', array('exception' => $e));
      }
    }
  }
  else {
    Errors::unexpected("klicktipp_transactional_cron unable to connect to beanstalk");
  }

  // create one job per slice (if they are not already stacking up)
  TransactionalQueue::PushQueueJobs();
  NewsletterQueue::PushQueueJobs();
  AutoresponderQueue::PushQueueJobs();
  NewsletterPreGenerationQueue::PushQueueJobs();
  NewsletterPregeneratedQueue::PushQueueJobs();
  SubscriberQueue::PushQueueJobs();
  SubscriberQueue::PushLowPrioQueueJobs();
}


/*
 * move customers to/from own slices
 */
function move_customers_in_separate_slices_worker($data) {
  ProcessLog::QueueItemStarted($data);

  $items = empty($data['#items']) ? [$data] : $data['#items'];
  $data['#itemcount'] = 0;

  foreach ($items as $item) {

    kt_begin_transaction();
    if ($item['add']) {
      // add to customer slices

      // see TransactionEmails::CalcShard
      // note: beware of index "idx_mod_status_tts" as it needs to modify itself (copies table)

      db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} USE INDEX (idx_owner_status_autoresponder) SET ModOfCampaignID = -RelOwnerUserID ".
        " WHERE RelOwnerUserID = :RelOwnerUserID  AND StatusEnum IN (:StatusEnum)", [
          ':RelOwnerUserID' => $item['uid'],
          ':StatusEnum' => [TransactionEmails::STATUS_PENDING, TransactionEmails::STATUS_PENDING_AFTER],
        ]);

      db_query("UPDATE {".AutoresponderQueue::TABLE_NAME."} USE INDEX (idx_owner_status_autoresponder) SET ModOfCampaignID = -RelOwnerUserID ".
        " WHERE RelOwnerUserID = :RelOwnerUserID  AND StatusEnum IN (:StatusEnum)", [
        ':RelOwnerUserID' => $item['uid'],
        ':StatusEnum' => [TransactionEmails::STATUS_PENDING],
      ]);

      db_query("UPDATE {".NewsletterQueue::TABLE_NAME."} USE INDEX (idx_owner_status_autoresponder) SET ModOfCampaignID = -RelOwnerUserID ".
        " WHERE RelOwnerUserID = :RelOwnerUserID  AND StatusEnum IN (:StatusEnum)", [
        ':RelOwnerUserID' => $item['uid'],
        ':StatusEnum' => [TransactionEmails::STATUS_PENDING],
      ]);
    }
    else {
      // remove from customer slices

      // see TransactionEmails::CalcShard
      // note: beware of index "idx_mod_status_tts" as it needs to modify itself (copies table)

      db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} USE INDEX (idx_owner_status_autoresponder) SET ModOfCampaignID = MOD(RelAutoResponderID, 1024) ".
        " WHERE RelOwnerUserID = :RelOwnerUserID  AND StatusEnum IN (:StatusEnum)", [
        ':RelOwnerUserID' => $item['uid'],
        ':StatusEnum' => [TransactionEmails::STATUS_PENDING, TransactionEmails::STATUS_PENDING_AFTER],
      ]);

      db_query("UPDATE {".AutoresponderQueue::TABLE_NAME."} USE INDEX (idx_owner_status_autoresponder) SET ModOfCampaignID = MOD(RelAutoResponderID, 1024) ".
        " WHERE RelOwnerUserID = :RelOwnerUserID  AND StatusEnum IN (:StatusEnum)", [
        ':RelOwnerUserID' => $item['uid'],
        ':StatusEnum' => [TransactionEmails::STATUS_PENDING, TransactionEmails::STATUS_PENDING_AFTER],
      ]);

      db_query("UPDATE {".NewsletterQueue::TABLE_NAME."} USE INDEX (idx_owner_status_autoresponder) SET ModOfCampaignID = MOD(RelAutoResponderID, 1024) ".
        " WHERE RelOwnerUserID = :RelOwnerUserID  AND StatusEnum IN (:StatusEnum)", [
        ':RelOwnerUserID' => $item['uid'],
        ':StatusEnum' => [TransactionEmails::STATUS_PENDING],
      ]);
    }
    kt_commit_transaction();

    $data['#itemcount']++;
  }

  ProcessLog::QueueItemProcessed($data);
}

/**
 * subscriber export worker
 */
function _klicktipp_subscriber_export_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  $filename = "{$data['URI']}.part{$data['Part']}";
  $file = fopen($filename, 'a'); //open for write (append)

  $CustomFields = null;

  if ($file) {

    foreach ($data['SubscriptionIDs'] as $sub) {

      $Subscription = Subscription::PrepareExport($data['UserID'], $sub['ContactInfo'], $sub['SubscriptionType'], $sub['ReferenceID'], $data['Header']);

      if (empty($Subscription)) {
        continue;
      }

      // --- format subscription data ---

      $Subscription['OptInDate'] = (empty($Subscription['OptInDate'])) ? '' : Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $Subscription['OptInDate']);
      $Subscription['SubscriptionDate'] = (empty($Subscription['SubscriptionDate'])) ? '' : Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $Subscription['SubscriptionDate']);
      $Subscription['UnsubscriptionDate'] = (empty($Subscription['UnsubscriptionDate'])) ? '' : Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $Subscription['UnsubscriptionDate']);
      $Subscription['SubscriptionStatus'] = (empty($Subscription['SubscriptionStatus'])) ? '' : t(/*ignore*/Subscribers::$DisplaySubscriptionStatus[$Subscription['SubscriptionStatus']]);
      $Subscription['BounceType'] = (empty($Subscription['BounceType'])) ? '' : t(/*ignore*/Subscribers::$TranslationBounceTypes[$Subscription['BounceType']]);

      foreach ($Subscription as $field => $value) {

        if (strpos($field, 'CustomField') === 0) {

          $id = str_replace('CustomField', '', $field);

          if (!isset($CustomFields)) {
            $CustomFields = CustomFields::RetrieveCustomFields($data['UserID'], TRUE);
          }

          switch ($CustomFields[$id]['FieldTypeEnum']) {
            case CustomFields::TYPE_DATE:
              $Subscription[$field] = empty($Subscription[$field]) ? '' : Dates::formatDate(Dates::FORMAT_DMY, (int) $Subscription[$field]);
              break;
            case CustomFields::TYPE_DATETIME:
              $Subscription[$field] = empty($Subscription[$field]) ? '' : Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $Subscription[$field]);
              break;
            case CustomFields::TYPE_TIME:
              $Subscription[$field] = empty($Subscription[$field]) ? '' : CustomFields::ConvertCustomFieldDataToWidget($Subscription[$field], CustomFields::WIDGET_TIME_SECONDSOFDAY);
              break;
            default:
              $Subscription[$field] = empty($Subscription[$field]) ? '' : $Subscription[$field];
              break;
          }

        }

      }

      fputcsv($file, $Subscription, $data['Delimiter'], $data['Encloser']);

    }

    fclose($file);

    rename($filename, "$filename.csv");

  }
  else {

    ProcessLog::QueueItemFailed(ProcessLog::FAILED_REQUEUE, $data);

    return;

  }

  // --- check if all parts were created
  $data['#itemcount'] = count($data['SubscriberIDs'] ?? []);
  $complete = ProcessLog::QueueItemProcessed($data);

  if ($complete) {
    // last batch item
    // create export file (for write, only if it does not exists)
    // send download link to customer

    $exportfile = fopen($data['URI'], 'x');
    if (empty($exportfile)) {
      $error = array(
        '!filename' => $data['URI'],
        '!data' => $data,
      );
      Errors::unexpected("Klick-Tipp CRON: export_subscribers fopen !filename failed", $error);
      return;
    }

    // --- write CSV headers ---

    fputcsv($exportfile, array_values($data['Header']), $data['Delimiter'], $data['Encloser']);

    if (!empty($data['Header2'])) {
      //Header2 contains hashes to identify fields for re-import
      fputcsv($exportfile, array_values($data['Header2']), $data['Delimiter'], $data['Encloser']);
    }

    for ($part = 1; $part <= $data['TotalParts']; $part++) {

      $content = file_get_contents("{$data['URI']}.part{$part}.csv");
      if (empty($content)) {
        $error = array(
          '!filename' => "{$data['URI']}.part{$part}.csv",
          '!data' => $data,
        );
        Errors::unexpected("Klick-Tipp CRON: export_subscribers part file !filename is empty", $error);
        return;
      }

      fwrite($exportfile, $content);
      unlink("{$data['URI']}.part{$part}.csv");

    }

    fclose($exportfile);

    $account = user_load($data['UserID']);

    $QueryParameters = array(
      'UserID' => $data['UserID'],
      'URI' => "export/{$data['Filename']}",
    );

    $link = Core::EncryptURL($QueryParameters, 'download');
    $time = Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $data['Time']);

    $params = array(
      'subject' => variable_get('klicktipp_export_subject', ''),
      'body' => variable_get('klicktipp_export_body', ''),
      'placeholders' => array(
        '!username' => $account->name,
        '!site' => variable_get('site_name', 'Drupal'),
        '!date' => $time,
        '!download_link' => $link,
      ),
    );

    $message = drupal_mail('klicktipp', 'export', $data['EmailAddress'], user_preferred_language($account), $params, variable_get('site_mail', '<EMAIL>'));
    if (empty($message['result'])) {
      $error = array(
        '!filename' => $data['Filename'],
        '!data' => $data,
        '!message' => $message,
      );
      Errors::unexpected("Klick-Tipp CRON: export_subscribers send email for !filename failed", $error);
      return;
    }

  }

}

function _klicktipp_subscriber_duplicates_export_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  $select = ['e.Date',  'e.Type', 'd.Status', 'd.RelSubscriberID', 'd.DuplicateSubscriberID'];
  $where =  ['EventID IN (:eventIDs)'];
  $customFields = ['FirstName','LastName', 'City', 'Zip', 'Street1', 'Street2'];

  $customFieldJoins = [];
  foreach ($customFields as $customField) {
    $select[] = "sub_$customField.ValueText as Subscriber$customField";
    $select[] = "dup_$customField.ValueText as Duplicate$customField";
    $customFieldJoins[] = "LEFT JOIN {custom_field_values} as sub_$customField "
      . "ON d.RelOwnerUserID = sub_$customField.RelOwnerUserID "
      . "AND d.RelSubscriberID = sub_$customField.RelSubscriberID "
      . "AND sub_$customField.RelFieldID = '$customField'";
    $customFieldJoins[] = " LEFT JOIN {custom_field_values} as dup_$customField "
      . "ON d.RelOwnerUserID = dup_$customField.RelOwnerUserID "
      . "AND d.DuplicateSubscriberID = dup_$customField.RelSubscriberID "
      . "AND dup_$customField.RelFieldID = '$customField'";
  }

  $sql = 'SELECT ' . implode(', ', $select) . ' FROM {subscriber_duplicates} d INNER JOIN {subscriber_duplicate_events} e USING(DupID) '
    . implode(' ', $customFieldJoins) . ' WHERE ' . implode(' AND ', $where);

  $dbResult = db_query($sql, [':eventIDs' => $data['eventIDs']]);
  $file = fopen($data['filePath'], 'a');
  while($record = $dbResult->fetchAssoc()) {
    $record['Date'] = date('Y-m-d H:i:s', $record['Date']);
    $record['Type'] = SubscriberDuplicates::getStatusName((int)$record['Type']);
    $record['Status'] = SubscriberDuplicateEvents::getTypeName((int)$record['Status']);
    fputcsv($file, $record);
  }
  fclose($file);
  ProcessLog::QueueItemProcessed($data);
}

/**
 * request_forwarders queue worker
 */
function _klicktipp_request_forwarders_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  Requests::CreateRequestForwarders();

  ProcessLog::QueueItemProcessed($data);
}

/**
 * delete newsletter queue worker
 *
 *   $data = array(
 *     'RelOwnerUserID' => ...
 *     'CampaignID' => $ArrayCampaign['CampaignID'],
 *   )
 */
function _klicktipp_delete_newsletter_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  $Limit = Settings::get('klicktipp_newsletter_entries_to_delete');

  kt_query("DELETE FROM {".NewsletterQueue::TABLE_NAME."} WHERE RelAutoResponderID = %d AND RelOwnerUserID = %d LIMIT $Limit",
    $data['CampaignID'], $data['RelOwnerUserID']);

  ProcessLog::QueueItemProcessed($data);
}

/**
 * delete taggings worker
 *
 *   $data = array(
 *     'TagID' => ...,
 *     'RelOwnerUserID' => ...
 *   )
 */
function _klicktipp_delete_taggings_worker($data) {
  ProcessLog::QueueItemStarted($data);

  $Limit = Settings::get('klicktipp_newsletter_tag_entries_to_delete');

  kt_query("DELETE FROM {tagging} WHERE RelTagID = %d LIMIT $Limit", $data['TagID']);

  ProcessLog::QueueItemProcessed($data);
}

/**
 * bouncecheck worker: re-validate one email address und un-bounce subscriber on success
 *
 *   $data = array(
 *     'UserID' => ...
 *     'EmailAddress' => ...,
 *     'OmitBounceCheck' => FALSE, if check by verify-email.org
 *     'NotifyUser' => TRUE, then show result to user
 *   )
 */
function _klicktipp_bouncecheck_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  if (!empty($data['#items'])) {
    $data['#itemcount'] = 0;
    foreach ($data['#items'] as $item) {
      BounceEngine::ProcessBouncecheck($item['UserID'], $item['EmailAddress'], $item['OmitBounceCheck'], $item['NotifyUser']);
      $data['#itemcount']++;
    }
  }
  else {
    BounceEngine::ProcessBouncecheck($data['UserID'], $data['EmailAddress'], $data['OmitBounceCheck'], $data['NotifyUser']);
  }

  ProcessLog::QueueItemProcessed($data);
}

/**
 * soft bouncecheck worker: re-validate a number of email address
 *
 * $data = array()
 */
function _klicktipp_softbouncecheck_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  // reset a number of random soft bounces that are older than the blocking period
  $period = Settings::get('klicktipp_softbounce_blocking');
  $bouncecheck_limit = Settings::get('klicktipp_softbouncecheck_limit');
  $result = kt_query("SELECT * FROM {suppression_list} " .
    " WHERE SuppressionSourceEnum = %d AND LastValidation < %d ORDER BY RAND() LIMIT $bouncecheck_limit",
    SuppressionList::SUPPRESSION_LIST_SOURCE_SOFTBOUNCED, strtotime("-$period days")
  );
  while ($ArraySuppressionList = kt_fetch_array($result)) {
    (new SuppressionList())->removeFromSuppressionList($ArraySuppressionList['EmailAddress'], Subscribers::BOUNCETYPE_SOFT);
    $data['#itemcount']++;
  };
  // log unprocessed soft bounces
  $data['#unprocessed'] = db_query("SELECT COUNT(*) FROM {suppression_list} " .
    " WHERE SuppressionSourceEnum = :SuppressionSourceEnum AND LastValidation < :LastValidation",
    array(
      ':SuppressionSourceEnum' => SuppressionList::SUPPRESSION_LIST_SOURCE_SOFTBOUNCED,
      ':LastValidation' => strtotime("-$period days"),
    )
  )->fetchField();

  ProcessLog::QueueItemProcessed($data);
}

/**
 * subscriber tagging queue worker
 * can be used to tag or untag a subscriber
 *
 *   $data = array(
 *     'UserID' => ...,
 *     'SubscriberID' => ...
 *     'TagID' => ...
 *     'Mode' => Subscribers::HISTORY_TAGGED or Subscribers::HISTORY_UNTAGGED
 *   )
 */
function klicktipp_subscriber_tagging_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  if (!empty($data['#items'])) {
    $data['#itemcount'] = 0;
    foreach ($data['#items'] as $item) {
      $ReferenceID = empty($item['ReferenceID']) ? 0 : $item['ReferenceID'];
      if ($item['Mode'] == Subscribers::HISTORY_UNTAGGED) {
        Subscribers::UntagSubscriber($item['UserID'], $item['SubscriberID'], $ReferenceID, $item['TagID']);
      }
      else {
        Subscribers::TagSubscriber($item['UserID'], $item['SubscriberID'], $item['TagID'], $ReferenceID, TRUE);
      }

      // trigger automations
      TransactionEmails::RegisterAutomations($item['UserID'], $item['SubscriberID'], $ReferenceID);

      $data['#itemcount']++;
    }
  }
  else {
    $uid = $data['UserID'];
    $ReferenceID = empty($data['ReferenceID']) ? 0 : $data['ReferenceID'];
    if ($data['Mode'] == Subscribers::HISTORY_UNTAGGED) {
      Subscribers::UntagSubscriber($uid, $data['SubscriberID'], $ReferenceID, $data['TagID']);
    }
    else {
      Subscribers::TagSubscriber($uid, $data['SubscriberID'], $data['TagID'], $ReferenceID, TRUE);
    }

    // trigger automations
    TransactionEmails::RegisterAutomations($uid, $data['SubscriberID'], $ReferenceID);
  }


  if (ProcessLog::QueueItemProcessed($data)) {
    // last job processed
    if (!empty($data['klicktipp_notify']) && !empty($uid)) {
      $message = ($item['Mode'] == Subscribers::HISTORY_TAGGED) ? /*t(*/'Subscriber tagging completed.'/*)*/ : /*t(*/'Subscriber untagging completed.'/*)*/;
      klicktipp_set_message($uid, $message);
    }
  }
}

/**
 * subscriber deletion queue worker
 *
 *   $data = array(
 *     'UserID' => ...,
 *     'SubscriberID' => ...
 *   )
 */
function klicktipp_subscriber_deletion_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  $QueueData = empty($data['#items']) ? [$data] : $data['#items'];

  $data['#itemcount'] = 0;

  $UserID = 0;
  $SubscriberIDs = [];
  foreach ($QueueData as $item) {
    $UserID = $item['UserID'];
    $SubscriberIDs[] = $item['SubscriberID'];
  }

  $result = Subscribers::RemoveSubscribersByID($UserID, $SubscriberIDs, Subscribers::DELETION_TYPE_SUBSCRIBER_DELETION_QUEUE);
  if ($result) {
    $data['#itemcount'] = count($QueueData);
    // make sure all list/subscriber displays are current
    UserCache::clear($UserID);
  }
  else {
    klicktipp_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }

  if (ProcessLog::QueueItemProcessed($data)) {
    // last job processed
    if (!empty($data['klicktipp_notify']) && !empty($UserID)) {
      klicktipp_set_message($UserID, 'Subscriber deletion completed.');
    }
  }

}

/**
 * subscription deletion queue worker
 *
 *   $data = array(
 *     'UserID' => ...,
 *     'SubscriberID' => ...
 *     'SubscriptionType' => ...
 *     'ContactInfo' => ...
 *   )
 */
function klicktipp_subscription_deletion_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  $QueueData = empty($data['#items']) ? [$data] : $data['#items'];

  $data['#itemcount'] = 0;

  $UserID = reset($QueueData)['UserID'];
  $account = user_load($UserID);

  foreach ($QueueData as $item) {
    $subscriptionData = Subscription::RetrieveSubscriptionByContactInfo($UserID, $item['ContactInfo'], $item['SubscriptionType']);
    if ($subscriptionData) {
      $subscription = new Subscription($subscriptionData);
      $subscription->deleteWithSpamCheck($account);
    }
    $data['#itemcount']++;
  }

  // make sure all list/subscriber displays are current
  UserCache::clear($UserID);

  if (ProcessLog::QueueItemProcessed($data)) {
    // last job processed
    if (!empty($data['klicktipp_notify']) && !empty($UserID)) {
      klicktipp_set_message($UserID, 'Subscription deletion completed.');
    }
  }

}

/**
 * Part of morning cron
 * update/cleanup the page view statistic of the help block
 */
function klicktipp_help_log_update() {

  $started = time();

  Libraries::include("help_block.inc");

  $today = strtotime("today 00:00:00");
  $yesterday = strtotime("yesterday 00:00:00");

  $result = db_query("SELECT * FROM {helplog} WHERE timestamp = :timestamp", array(
    ':timestamp' => $yesterday,
  ));

  while ($merge = kt_fetch_array($result)) {

    //insert or update in case pages were visited between midnight and the cron
    //borrowed from statistics module (statistics.php)
    db_merge('helplog')
      ->key(array('path' => $merge['path'], 'timestamp' => $today))
      ->fields(array(
        'daycount' => 0,
        'totalcount' => $merge['totalcount'],
      ))
      ->expression('daycount', 'daycount + 0')
      ->expression('totalcount', 'daycount + ' . $merge['totalcount'])
      ->execute();


    if (time() - $started > 60) {
      Errors::unexpected("CRON - klicktipp_help_log_update(): Transfer totals did not finish in time. Statistics were not updated.", array());
      return;
    }

  }

  //delete log entries older than 30 days

  db_query("DELETE FROM {helplog} WHERE timestamp < :timestamp", array(
    ':timestamp' => strtotime("-30 days 00:00:00"),
  ));

  //update statistics

  if (time() - $started <= 45) {

    $updated = klicktipp_help_update_statistics();
    variable_set('klicktipp_help_paths', $updated);

  }
  else {
    Errors::unexpected("CRON - klicktipp_help_log_update(): Not enough time to update statistics.", array());
  }

}

/**
 * @param $data
 * delete pending subscribers queue worker
 *
 * $data = array(
 *   'UserID' => ...,
 *   'ListID' => ...
 *   'PendingSubscribersDelay' => ...
 * )
 */
function _klicktipp_delete_pending_subscribers_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  if (empty($data['#items'])) {
    ProcessLog::QueueItemProcessed($data);
    return;
  }

  foreach ($data['#items'] as $item) {
    $data['#itemcount']++;

    // prevent all pending subscribers from being deleted if the delay is unexpectedly undefined
    $delay = (empty($item['PendingSubscribersDelay'])) ? 3 : $item['PendingSubscribersDelay'];

    // get all pending subscribers that should be deleted
    $sql = <<<SQL
      SELECT RelSubscriberID,SubscriptionType,ContactInfo FROM {subscription}
      WHERE RelOwnerUserID = :RelOwnerUserID
      AND RelListID = :RelListID
      AND SubscriptionStatus = :SubscriptionStatus
      AND OptInDate <= :OptInDate
    SQL;
    $result = db_query($sql, array(
      ':RelOwnerUserID' => $item['UserID'],
      ':RelListID' => $item['ListID'],
      ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      ':OptInDate' => strtotime("-{$delay} days"),
    ));

    $DeleteSubscriberIDs = array();
    while ($row = kt_fetch_array($result)) {

      // check if the subscriber has any other subscriptions
      $sql = <<<SQL
        SELECT COUNT(1)
        FROM {subscription}
        WHERE RelOwnerUserID = :RelOwnerUserID
        AND RelSubscriberID = :RelSubscriberID
      SQL;
      $countSubscriptions = db_query($sql, array(
        ':RelOwnerUserID' => $item['UserID'],
        ':RelSubscriberID' => $row['RelSubscriberID']
      ))->fetchField();

      if (intval($countSubscriptions) === 1) {
        // if this is the only subscription of the subscriber, mark for deletion
        $DeleteSubscriberIDs[] = $row['RelSubscriberID'];
      } else {
        // delete only this subscription
        $subscription = new Subscription([
          'RelOwnerUserID' => $item['UserID'],
          'SubscriptionType' => $row['SubscriptionType'],
          'ContactInfo' => $row['ContactInfo'],
        ]);
        $subscription->deleteFromDB();
      }
    }
    if (!empty($DeleteSubscriberIDs)) {
      Subscribers::RemoveSubscribersByID($item['UserID'], $DeleteSubscriberIDs, Subscribers::DELETION_TYPE_PENDING_SUBSCRIBER_DELETION_QUEUE);
    }
  }

  ProcessLog::QueueItemProcessed($data);
}

/**
 * stop campaignsfrom inactive users queue worker
 * @param $data
 *
 * $data = array(
 *   'UserID' => ...,
 *   'CampaignID' => ...
 * )
 */
function _klicktipp_stop_inactive_users_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  if (empty($data['#items'])) {
    ProcessLog::QueueItemProcessed($data);
    return;
  }

  foreach ($data['#items'] as $item) {
    $data['#itemcount']++;

    Campaigns::StopCampaign($item['UserID'], $item['CampaignID']);
  }

  ProcessLog::QueueItemProcessed($data);
}

/**
 * check if subscriptions are active (have a valid and not expired payment)
 *
 * $item = array(
 *   'UserID' => ...
 *   'BuildID' => ...
 *   'SubscriberID' => ...
 * )
 * @param array<mixed> $data
 * @param int $starttime
 * @param int $runtime
 * @param int $now
 * @return void
 * @throws Exception
 */
function _klicktipp_active_payments_queue_worker(array $data, int $starttime, int $runtime = 55, int $now = 0) {
  ActivePaymentsQueueWorker::run($data, $starttime, $runtime, $now);
}

/**
 * create queue jobs for active payments check (called in morning cron)
 */
function _klicktipp_create_active_payment_jobs() {
  $LogID = 0;

  // all payments that expired since last 'klicktipp_active_payments_last_check_timestamp' will be checked for active payments
  $NextCheckDate = time();
  if (variable_get('klicktipp_active_payments_rebuild_smart_tags', 1)) {
    // rebuild all
    $result = db_query("SELECT DISTINCT RelOwnerUserID AS UserID, RelSubscriberID AS SubscriberID, RelBuildID AS BuildID FROM {payments} ORDER BY RelBuildID");
  }
  else {
    // check all since last check
    $result = db_query("SELECT DISTINCT RelOwnerUserID AS UserID, RelSubscriberID AS SubscriberID, RelBuildID AS BuildID FROM {payments} " .
      " WHERE ExpireDate > :LastCheckDate AND ExpireDate <= :NowDate ORDER BY RelBuildID ",
      [
        ':LastCheckDate' => variable_get('klicktipp_active_payments_last_check_timestamp', PaymentIPNs::SUBSCRIPTION_LIFETIME_EXPIRE_DATE),
        ':NowDate' => $NextCheckDate,
      ]);
  }
  $QueueData = [];
  while ($payment = kt_fetch_array($result)) {
    $QueueData[] = $payment;
    if (count($QueueData) > 10000) {
      // avoid memory overflow: create jobs and clear memory
      $LogID = ProcessLog::CreateSlicedQueueItems('active_payments_queue', $QueueData, variable_get('klicktipp_active_payments_queue_limit', 100));
      $QueueData = [];
    }
  }
  if (!empty($QueueData)) {
    // create queue jobs to update smart tags
    $LogID = ProcessLog::CreateSlicedQueueItems('active_payments_queue', $QueueData, variable_get('klicktipp_active_payments_queue_limit', 100));
  }

  // update date for next check
  variable_set('klicktipp_active_payments_rebuild_smart_tags', 0);
  variable_set('klicktipp_active_payments_last_check_timestamp', $NextCheckDate);

  // for test purposes
  return $LogID;
}

/**
 * Assign all references to a just switched multi value tag
 * @param $data array {tagging} dataset
 */
function switch_multivalue_taggings_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  $items = empty($data['#items']) ? [$data]: $data['#items'];
  $data['#itemcount'] = 0;

  foreach ($items as $tagging0) {

    // delete 0 ref (which is the single value, but may or not be a multi value set)
    db_query("DELETE FROM {tagging} ".
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID AND ReferenceID = 0 ", [
      ':RelOwnerUserID' => $tagging0['RelOwnerUserID'],
      ':RelSubscriberID' => $tagging0['RelSubscriberID'],
      ':RelTagID' => $tagging0['RelTagID'],
    ]);

    // add all references
    db_query(<<<SQL
INSERT IGNORE INTO {tagging} (RelSubscriberID, RelOwnerUserID, ReferenceID, RelTagID, SubscriptionDate)
SELECT :RelSubscriberID, :RelOwnerUserID, ReferenceID, :RelTagID, :SubscriptionDate FROM {subscription_reference}
WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID
SQL
      , [
        ':RelOwnerUserID' => $tagging0['RelOwnerUserID'],
        ':RelSubscriberID' => $tagging0['RelSubscriberID'],
        ':RelTagID' => $tagging0['RelTagID'],
        ':SubscriptionDate' => $tagging0['SubscriptionDate'],
      ]);

    $data['#itemcount']++;
  }

  ProcessLog::QueueItemProcessed($data);
}

/**
 *
 * @param $data array {tagging} single, flat tagging data
 */
function switch_multivalue_to_singlevalue_taggings_queue_worker($data) {
  // we require input data to be a single, flat tagging array
  $data['#itemcount'] = 1;
  ProcessLog::QueueItemStarted($data);

  // delete all taggings with this TagID
  db_query("DELETE FROM {tagging} " .
    " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelTagID = :RelTagID ", [
    ':RelOwnerUserID' => $data['RelOwnerUserID'],
    ':RelSubscriberID' => $data['RelSubscriberID'],
    ':RelTagID' => $data['RelTagID'],
  ]);

  // add taggings back in for all references with the given SubscriptionDate (supposed to be the youngest)
  db_query(
    "INSERT IGNORE INTO {tagging} (RelSubscriberID, RelOwnerUserID, ReferenceID, RelTagID, SubscriptionDate) " .
    "VALUES(:RelSubscriberID, :RelOwnerUserID, :ReferenceID, :RelTagID, :SubscriptionDate) ", [
    ':RelOwnerUserID' => $data['RelOwnerUserID'],
    ':RelSubscriberID' => $data['RelSubscriberID'],
    ':ReferenceID' => 0,
    ':RelTagID' => $data['RelTagID'],
    ':SubscriptionDate' => $data['SubscriptionDate'],
  ]);

  ProcessLog::QueueItemProcessed($data);
}

/**
 * Assign all references to a just switched multi value custom field
 * @param $data array {custom_field_values} dataset
 */
function switch_multivalue_custom_fields_queue_worker($data) {
  ProcessLog::QueueItemStarted($data);

  $items = empty($data['#items']) ? [$data]: $data['#items'];
  $data['#itemcount'] = 0;

  foreach ($items as $field0) {

    // We have a 0-ref from the single value (which is always stored in ref 0), but the subscriber may or may not have a 0-ref for multi values.
    // So instead of checking whether we need it, we just delete it. If the subscriber has a 0-ref, then the next query will insert it again.
    db_query("DELETE FROM {custom_field_values} ".
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID = :RelFieldID AND ReferenceID = 0 ", [
      ':RelOwnerUserID' => $field0['RelOwnerUserID'],
      ':RelSubscriberID' => $field0['RelSubscriberID'],
      ':RelFieldID' => $field0['RelFieldID'],
    ]);

    // add all references
    db_query(<<<SQL
INSERT IGNORE INTO {custom_field_values} (RelOwnerUserID, RelFieldID, RelSubscriberID, ReferenceID, ValueText, SubscriptionDate)
SELECT :RelOwnerUserID, :RelFieldID, :RelSubscriberID, ReferenceID, :ValueText, :SubscriptionDate FROM {subscription_reference}
WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID
SQL
      , [
        ':RelOwnerUserID' => $field0['RelOwnerUserID'],
        ':RelSubscriberID' => $field0['RelSubscriberID'],
        ':RelFieldID' => $field0['RelFieldID'],
        ':SubscriptionDate' => $field0['SubscriptionDate'],
        ':ValueText' => $field0['ValueText'],
      ]);

    $data['#itemcount']++;
  }

  ProcessLog::QueueItemProcessed($data);
}

/**
 * custom field value processing after switching a multi value custom field to single value
 *
 * @param array $lastValue last custom value of multi value field
 */
function switch_multivalue_to_singlevalue_custom_fields_queue_worker($lastValue) {
  ProcessLog::QueueItemStarted($lastValue);

  $items = empty($lastValue['#items']) ? [$lastValue] : $lastValue['#items'];
  $lastValue['#itemcount'] = 0;

  foreach ($items as $field) {
    if ($field['ReferenceID'] > 0) {
      db_query("INSERT INTO {custom_field_values} (RelOwnerUserID, RelSubscriberID, RelFieldID, ReferenceID, ValueText, SubscriptionDate) ".
        "VALUES(:UserID, :SubscriberID, :FieldID, 0, :ValueText, :SubscriptionDate) " .
        "ON DUPLICATE KEY UPDATE ValueText = :ValueText, SubscriptionDate = :SubscriptionDate", [
        ':UserID' => $field['RelOwnerUserID'],
        ':SubscriberID' => $field['RelSubscriberID'],
        ':FieldID' => $field['RelFieldID'],
        ':ValueText' => $field['ValueText'],
        ':SubscriptionDate' => $field['SubscriptionDate'],
      ]);
    }

    db_query("DELETE FROM {custom_field_values} WHERE ".
      "RelOwnerUserID = :UserID AND RelSubscriberID = :SubscriberID AND RelFieldID = :FieldID AND ReferenceID > 0 ", [
      ':UserID' => $field['RelOwnerUserID'],
      ':SubscriberID' => $field['RelSubscriberID'],
      ':FieldID' => $field['RelFieldID']
    ]);

    $lastValue['#itemcount']++;
  }

  ProcessLog::QueueItemProcessed($lastValue);
}

/**
 * @throws \GuzzleHttp\Exception\GuzzleException
 */
function klicktipp_bee_token_update_cron(): void
{
  try {
    AccessTokenManager::updateEmailEditorAccessToken();
    watchdog(
      "bee_token_update_cron",
      'Token for the email editor successfully updated.',
      [],
      WATCHDOG_INFO,
      ''
    );
  } catch (GuzzleException $e) {
    watchdog(
      "bee_token_update_cron",
      'Error updating the email editor token: ' . $e->getMessage(),
      [],
      WATCHDOG_ERROR,
      ''
    );
  }

  try {
    AccessTokenManager::updateLandingPageEditorAccessToken();
    watchdog(
      "bee_token_update_cron",
      'Token for the langing page editor successfully updated.',
      [],
      WATCHDOG_INFO,
      ''
    );
  } catch (GuzzleException $e) {
    watchdog(
      "bee_token_update_cron",
      'Error updating the landing page editor token: ' . $e->getMessage(),
      [],
      WATCHDOG_ERROR,
      ''
    );
  }

  if (isset($e)) {
    throw $e;
  }
}

/**
 * Triggers syncronisation of addon status for users with addons
 */
function klicktipp_sync_digistore_addon_status_cron()
{
    $batchSize = variable_get(
      'klicktipp_sync_digistore_addon_status_cron_batch_size',
      50
    );
    $lastUserID = 0;
    $sql = 'SELECT DISTINCT RelOwnerUserID FROM {addon_entities} ' .
      'WHERE RelOwnerUserID > :userID ORDER BY RelOwnerUserID ASC';

    $queue = DrupalQueue::get('addon_status_sync');
    $queue->createQueue();
    while (
      $userIDs = db_query_range($sql, 0, $batchSize, [':userID' => $lastUserID])
        ->fetchCol()
    ) {
      $lastUserID = end($userIDs);
      $queue->createItem($userIDs);
    }
}
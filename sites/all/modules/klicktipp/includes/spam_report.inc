<?php

/**
 * Ask Subscriber for feedback after clicking "Report Spam" in email
 *
 * The feedback will be stored in the Subscriber-History,
 * and an E-Mail with the Feedback will be sent to the support system
 *
 */

use App\Klicktipp\BounceEngine;
use App\Klicktipp\Core;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\Subscribers;

define("KLICKTIPP_SPAMREPORT_FEEDBACK_SPAMREPORT_TITLE", /*t(*/'Report Spam'/*)*/);
define("KLICKTIPP_SPAMREPORT_FEEDBACK_MESSAGE", /*t(*/'We would like to improve our service for you. Your feedback is highly appreciated.'/*)*/);
define("KLICKTIPP_SPAMREPORT_FEEDBACK_QUESTION", /*t(*/'Why would you like to report the e-mail "@subject" from @company as spam?'/*)*/); // Warum m�chten Sie die E-Mail "[Betreff]" von [Unterneh<PERSON>] / [Vorname] [Nachname] als Spam melden?
define("KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_TITLE", /*t(*/'Unsubscription Completed'/*)*/);
define("KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_MESSAGE", /*t(*/'Your email address is unsubscribed from our mailing list.'/*)*/);

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_spamreport_feedback($p, $action = '', $redirect_callback = 'klicktipp_spamreport_feedback_redirect') {

  if (empty($p)) {
    //EXIT 3: Script called with no or false parameters, just show the unsubscription confirmation
    call_user_func($redirect_callback, t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_TITLE), t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_MESSAGE));
    return;
  }

  $ArrayParameters = Core::DecryptURL($p);
  $EmailID = $ArrayParameters['EmailID'];
  // Retrieve User
  $UserID = db_query("SELECT RelUserID FROM {emails} WHERE EmailID = :EmailID", array(':EmailID' => $EmailID))->fetchField();

  if ($action == 'ok') {
    //EXIT 1: User gave feedback, say thank you
    call_user_func($redirect_callback, t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_TITLE), t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_MESSAGE), '', $UserID);
    return;
  }
  elseif ($action == 'cancel') {
    //EXIT 2: User canceled -> thanks and goodbye
    call_user_func($redirect_callback, t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_SPAMREPORT_TITLE), t('Your request to unsubscribe has been canceled.'), '', $UserID);
    return;
  }

  if (!empty($UserID)) {
    $account = user_load($UserID);
  }
  if (empty($UserID) || empty($account)) {
    //EXIT 5: User not found, just show the unsubscription confirmation
    call_user_func($redirect_callback, t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_TITLE), t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_MESSAGE));
    return;
  }

  // Retrieve Email
  $ArrayEmail = Emails::RetrieveEmailByID($account->uid, $EmailID);
  if ($ArrayEmail == FALSE) {
    //EXIT 4: Email not found, just show the unsubscription confirmation
    call_user_func($redirect_callback, t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_TITLE), t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_CONFIRM_MESSAGE), '', $UserID);
    return;
  }

  // show feedback form
  $form = drupal_get_form('klicktipp_spamreport_feedback_form', $ArrayParameters, $ArrayEmail, (array) $account, $p);
  $output = t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_SPAMREPORT_TITLE) . drupal_render($form);
  call_user_func($redirect_callback, t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_SPAMREPORT_TITLE), $output, t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_SPAMREPORT_TITLE), $UserID);

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_spamreport_feedback_redirect($message_title, $message, $page_title = '', $UserID = 0) {
  // same as 'klicktipp_display_whitelabel_message', but without check_plain
  drupal_set_title(empty($page_title) ? $message_title : $page_title);
  print theme('klicktipp_whitelabel', array(
    'message' => $message_title,
    'content' => $message,
    'uid' => $UserID,
  ));
  exit; // just display the message and leave script
}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_spamreport_feedback_form($form, $form_state, $ArrayParameters, $ArrayEmail, $ArrayUserInformation, $p) {

  $weight = 0;

  $form = array();

  $userinfo = $ArrayUserInformation['FirstName'] . ' ' . $ArrayUserInformation['LastName'];
  $userinfo = empty($ArrayUserInformation['CompanyName']) ? $userinfo : $ArrayUserInformation['CompanyName'] . ' / ' . $userinfo;

  $SenderDomain = DomainSet::SelectValidSenderDomainById($ArrayUserInformation['uid'], $ArrayEmail['EmailID']);

  $form['Params'] = array(
    '#type' => 'value',
    '#value' => $ArrayParameters,
  );
  $form['UserInformation'] = array(
    '#type' => 'value',
    '#value' => $ArrayUserInformation,
  );
  $form['SpamReportHash'] = array(
    '#type' => 'value',
    '#value' => $p,
  );

  $form['Intro'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_info',
    '#value' => t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_MESSAGE),
    '#weight' => $weight++,
  );

  $form['Feedback'] = array(
    '#type' => 'textarea',
    '#title' => t(/*ignore*/KLICKTIPP_SPAMREPORT_FEEDBACK_QUESTION, array(
      '@subject' => $ArrayEmail['Subject'],
      '@company' => $userinfo,
    )),
    '#default_value' => '',
    '#weight' => $weight++,
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['Submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_unsubscribe_submit',
    '#value' => t('Report Spam'),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t('Cancel'),
    '#value' => $SenderDomain['appurl'] . variable_get('klicktipp_aliases_spam_report', 'spam') . '/' . $p . '/cancel',
    '#weight' => $weight++,
  );

  return $form;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_spamreport_feedback_form_submit($form, &$form_state) {

  $ArrayParameters = $form_state['values']['Params'];
  $Preview = $ArrayParameters['Preview'];

  $ArrayUserInformation = $form_state['values']['UserInformation'];
  $p = $form_state['values']['SpamReportHash'];

  $SenderDomain = DomainSet::SelectValidSenderDomainById($ArrayUserInformation['uid'], $ArrayParameters['EmailID']);

  // prepare redirect (we wont show any errors to the subscriber)
  $form_state['redirect'] = $SenderDomain['appurl'] . variable_get('klicktipp_aliases_spam_report', 'spam') . '/' . $p . '/ok';

  if (empty($Preview)) {

    //get subscriber data
    $ReferenceID = $ArrayParameters['ReferenceID'];
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($ArrayUserInformation['uid'], $ArrayParameters['SubscriberID'], $ReferenceID);
    if (empty($FullSubscriber)) {
      return;
    }

    // 1. unsubscribe and add to stats and protocols

    BounceEngine::RegisterSpamComplaint($ArrayUserInformation['uid'], $ArrayParameters['SubscriberID'], $FullSubscriber['EmailAddress'],
      "Subscriber used spam report form. Feedback: " . check_plain($form_state['values']['Feedback']),
      $ArrayParameters['EmailID'], $ArrayParameters['CampaignID'], $ArrayParameters['ListID'], $_SERVER['REMOTE_ADDR']);

    // 2. write to history & inform support by email

    Subscribers::AddSubscriberFeedback($ArrayUserInformation['uid'], $ArrayParameters['SubscriberID'],
      $FullSubscriber['EmailAddress'], $ArrayParameters['CampaignID'], $ArrayParameters['EmailID'],
      $form_state['values']['Feedback'], $ReferenceID);

  }
}

<?php

use App\Klicktipp\AutoresponderQueue;
use App\Klicktipp\BlacklistHandler;
use App\Klicktipp\BounceEngine;
use App\Klicktipp\Campaigns;
use App\Klicktipp\Core;
use App\Klicktipp\Libraries;
use App\Klicktipp\NewsletterQueue;
use App\Klicktipp\Subscribers;
use App\Klicktipp\ToolWebinar;
use App\Klicktipp\TransactionalQueue;
use App\Klicktipp\TransactionEmails;

define("KLICKTIPP_DATABASE_CLONE_SECURED", "klicktipp_database_clone_secured");

/**
 * hook_prod_check_alter()
 *
 * The hook receives the default functions divided into 7 categories:
 *
 *  - settings
 *  - server
 *  - performance
 *  - security
 *  - modules
 *  - seo
 *  - prod_mon
 *  - perf_data
 *
 * 'prod_mon' & 'perf_data' are special categories that will only be used by the
 * accompanying Production monitor module.
 *
 * Your function that implements the actual check must accept 1 string parameter
 * and return an array using the prod_check_execute_check() function.
 *
 */

/**
 * hook_prod_check_alter is implemented in klicktipp.module and calls this function
 */
function _klicktipp_prod_check(&$checks) {
  // Add klicktipp category.
  $checks['klicktipp'] = array(
    'title' => 'Klicktipp Monitor',
    'description' => 'Checks for the Klicktipp application.',
    'functions' => array(
      '_klicktipp_prod_check_processlog' => 'Processlog',
      '_klicktipp_prod_check_web_aged' => 'Aged Subscribers',
      '_klicktipp_prod_check_bouncelog' => 'Bounces',
      '_klicktipp_prod_check_sendperformance' => 'Send performance',
      '_klicktipp_prod_check_trans_queue_watch' => 'Transactional queue watch',
      '_klicktipp_prod_check_webinar' => 'Webinar subscriptions',
      '_klicktipp_prod_check_content_include_api' => 'Content includes API',
      '_klicktipp_prod_check_testlog' => 'Testlog',
      '_klicktipp_prod_check_klicktipplog' => 'Klicktipp log',
      '_klicktipp_prod_check_queue_consistency' => 'Transactional queue consistency',
      '_klicktipp_prod_check_spamrating' => 'Spam rating log',
      '_klicktipp_prod_check_blacklistlog' => 'Blacklists log',
      '_klicktipp_prod_check_blacklist_redirects_log' => 'Blacklist redirects log'
    ),
  );


  // later
  //unset($checks['settings']);
  //unset($checks['server']);
  unset($checks['modules']);

  // not needed
  unset($checks['seo']);

  // not working on klick-tipp.com
  unset($checks['security']);

}

/**
 * show details of a watchdog entry
 */
function klicktipp_prod_check_watchdog($form, $form_state, $id) {
  $severity = watchdog_severity_levels();
  $result = db_query('SELECT w.*, u.name, u.uid FROM {watchdog} w INNER JOIN {users} u ON w.uid = u.uid WHERE w.wid = :id', array(':id' => $id))->fetchObject();
  if ($dblog = $result) {
    $rows = array(
      array(
        array('data' => t('Type'), 'header' => TRUE),
        $dblog->type,
      ),
      array(
        array('data' => t('Date'), 'header' => TRUE),
        format_date($dblog->timestamp, 'long'),
      ),
      array(
        array('data' => t('User'), 'header' => TRUE),
        theme('username', array('account' => $dblog)),
      ),
      array(
        array('data' => t('Location'), 'header' => TRUE),
        l($dblog->location, $dblog->location),
      ),
      array(
        array('data' => t('Referrer'), 'header' => TRUE),
        l($dblog->referer, $dblog->referer),
      ),
      array(
        array('data' => t('Message'), 'header' => TRUE),
        theme('dblog_message', array('event' => $dblog)),
      ),
      array(
        array('data' => t('Severity'), 'header' => TRUE),
        $severity[$dblog->severity],
      ),
      array(
        array('data' => t('Hostname'), 'header' => TRUE),
        check_plain($dblog->hostname),
      ),
      array(
        array('data' => t('Operations'), 'header' => TRUE),
        $dblog->link,
      ),
    );
    $build['dblog_table'] = array(
      '#theme' => 'table',
      '#rows' => $rows,
      '#attributes' => array('class' => array('dblog-event')),
    );

    $vars = unserialize($dblog->variables);
    if (is_array($vars)) {
      $build['vars'] = array(
        '#type' => 'markup',
        '#value' => "<pre>" . print_r($vars, TRUE) . "</pre>",
      );
    }

    return $build;
  }
  else {
    return '';
  }

}

/**
 * show details of a fbl report
 */
function klicktipp_prod_check_fblreport($form, $form_state, $FBLID) {
  $result = kt_fetch_array(db_query("SELECT * FROM {fbl_reports} WHERE FBLID = :FBLID", array(':FBLID' => $FBLID)));
  if (empty($result)) {
    drupal_not_found();
  }

  $weight = 1;

  $form = array();

  $form['FBLEmail'] = array(
    '#type' => 'markup',
    '#title' => 'Report',
    '#value' => '<pre>' . check_plain(BounceEngine::GetFBLEmailContent($result['FBLEmail'])) .'</pre>',
    '#weight' => $weight++,
  );

  return $form;
}

/*
 * Processlog errors
 */
function _klicktipp_prod_check_processlog_add_queue_data(&$ProcessCodes, $queuename, $attr, $action = '', $value = 0) {
  if (empty($ProcessCodes)) {
    $ProcessCodes = array();
  }
  if (empty($ProcessCodes[$queuename])) {
    $ProcessCodes[$queuename] = array();
  }
  switch ($action) {
    case 'sum':
      $ProcessCodes[$queuename][$attr] = empty($ProcessCodes[$queuename][$attr]) ? $value : $ProcessCodes[$queuename][$attr] + $value;
      break;
    case 'max':
      $ProcessCodes[$queuename][$attr] = empty($ProcessCodes[$queuename][$attr]) ? $value : max($ProcessCodes[$queuename][$attr], $value);
      break;
    default: // 'count'
      $ProcessCodes[$queuename][$attr] = empty($ProcessCodes[$queuename][$attr]) ? 1 : $ProcessCodes[$queuename][$attr] + 1;
      break;
  }
}

define('KTPRODCHECK_ALERTRED_PHPMAXMEM', 250); // MB
define('KTPRODCHECK_ALERTRED_BOUNCES_UNPROCESSED', 20000);
define('KTPRODCHECK_ALERTRED_MAXTIME_LONGRUNNER', 230);
define('KTPRODCHECK_ALERTRED_MAXTIME_RUNNER', 59);
define('KTPRODCHECK_ALERTRED_MAXQTIME', 599); // 10 minutes
define('KTPRODCHECK_ALERTRED_MAXQTIME_MEDIUM', 3599); // 1 hour



function _klicktipp_prod_check_processlog($caller = 'internal') {
  if (!module_exists('dblog')) {
    return FALSE;
  }

  $check = array();
  $title = 'Processlog';
  $path = 'admin/reports/dblog';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $error = array();

  // find not terminated
  $result = db_query("SELECT * FROM {processlog} WHERE started <= :started AND finished = 0", array(':started' => strtotime("-6 hours")));
  while ($pentry = kt_fetch_array($result)) {
    $var = array(
      '!LogID' => $pentry['LogID'],
      '!started' => date('Y-m-d H:i:s', $pentry['started']),
      '!queuename' => $pentry['QueueName'],
      'processlog' => $pentry,
    );
    $error[] = t('Process !queuename (id:!LogID) at !started did not terminate!', $var);
  }

  // get "new" processlog
  $count = 0;
  $stats = '';
  $limit = 200000; // more throw an outofmemory (at 450MB php mem limit)
  $ProcessCodes = array();
  $result = db_query("SELECT * FROM {processlog} WHERE started > :started AND finished > 0 ORDER BY LogID DESC LIMIT $limit", array(':started' => strtotime("-1 day")));
  while ($pentry = kt_fetch_array($result)) {
    _klicktipp_prod_check_processlog_add_queue_data($ProcessCodes, $pentry['QueueName'], 'count');
    $queuetime = $pentry['finished'] - $pentry['started'];
    _klicktipp_prod_check_processlog_add_queue_data($ProcessCodes, $pentry['QueueName'], 'queue-time-max', 'max', $queuetime);
    $data = empty($pentry['Data']) ? array() : unserialize($pentry['Data']);
    if (!empty($data['timing'])) {
      _klicktipp_prod_check_processlog_add_queue_data($ProcessCodes, $pentry['QueueName'], 'job-time-sum', 'sum', $data['timing']['sum']);
      _klicktipp_prod_check_processlog_add_queue_data($ProcessCodes, $pentry['QueueName'], 'job-time-max', 'max', $data['timing']['max']);
    }
    if (!empty($data['itemcount'])) {
      _klicktipp_prod_check_processlog_add_queue_data($ProcessCodes, $pentry['QueueName'], 'job-items-sum', 'sum', $data['itemcount']['sum']);
      _klicktipp_prod_check_processlog_add_queue_data($ProcessCodes, $pentry['QueueName'], 'job-items-max', 'max', $data['itemcount']['max']);
    }
    if (!empty($data['unprocessed'])) {
      _klicktipp_prod_check_processlog_add_queue_data($ProcessCodes, $pentry['QueueName'], 'job-unprocessed-max', 'max', $data['unprocessed']['max']);
    }
    if (!empty($data['mem'])) {
      _klicktipp_prod_check_processlog_add_queue_data($ProcessCodes, $pentry['QueueName'], 'job-mem-max', 'max', $data['mem']);
      _klicktipp_prod_check_processlog_add_queue_data($ProcessCodes, $pentry['QueueName'], 'job-mem-diff', 'max', $data['memdiff']);
    }
    // aware of out of memory
    $count++;
    $laststarted = date("Y-m-d H:i:s", $pentry['started']);
  }
  if ($count >= $limit) {
    $stats .= "<div style='color:red;'>Incomplete due to memory limit (showing from $laststarted)</div>";
  }
  // always create stats as error (so we get them displayed)
  $stats .= '<table border="1"><tr><th>Process</th><th>#jobs</th><th>total #items</th><th>max #items</th><th>max #unprocessed</th><th>max mem</th><th>mem diff</th><th>total job time</th><th>max job time</th><th>max q time</th></tr>';
  foreach ($ProcessCodes as $queuename => $values) {
    $stats .= "<tr>";
    $stats .= "<td>$queuename</td>";
    // #jobs
    $stats .= "<td>{$values['count']}</td>";
    // total #items
    if ($queuename == 'bounce_cron' && empty($values['job-items-sum'])) {
      $stats .= "<td style=\"color:red;\">{$values['job-items-sum']}</td>";
    }
    else {
      $stats .= "<td>{$values['job-items-sum']}</td>";
    }
    // max #items
    $stats .= "<td>{$values['job-items-max']}</td>";
    // max #unprocessed
    if ($queuename == 'bounce_cron' && $values['job-unprocessed-max'] > KTPRODCHECK_ALERTRED_BOUNCES_UNPROCESSED) {
      $stats .= "<td style=\"color:red;\">{$values['job-unprocessed-max']}</td>";
    }
    else {
      $stats .= "<td>{$values['job-unprocessed-max']}</td>";
    }
    // max mem + mem diff
    if (empty($values['job-mem-max'])) {
      $stats .= "<td></td>";
      $stats .= "<td></td>";
    }
    else {
      $mb = round($values['job-mem-max']/1048576,1);
      $stats .= ($mb > KTPRODCHECK_ALERTRED_PHPMAXMEM ? '<td style="color:red;">' : '<td>') . $mb . " MB</td>";
      $mb = round($values['job-mem-diff']/1048576,1);
      $stats .= '<td>' . $mb . " MB</td>";
    }
    // total job time
    $stats .= "<td>{$values['job-time-sum']}</td>";
    // max job time
    if (in_array($queuename, ['send_queue', 'morning_cron'])) {
      $stats .= ($values['job-time-max'] > KTPRODCHECK_ALERTRED_MAXTIME_LONGRUNNER ? '<td style="color:red;">' : '<td>') . "{$values['job-time-max']}</td>";
    }
    else {
      $stats .= ($values['job-time-max'] > KTPRODCHECK_ALERTRED_MAXTIME_RUNNER ? '<td style="color:red;">' : '<td>') . "{$values['job-time-max']}</td>";
    }
    // max q time
    // alert by priority (see baseconfig)
    $q = "beanstalk_queue_$queuename";
    $prio = isset($GLOBALS ['conf'][$q]['priority']) ? $GLOBALS['conf'][$q]['priority'] : 1000;
    if ($prio >= 1000) {
      // low prio: no alert
      $stats .= '<td>' . "{$values['queue-time-max']}</td>";
    }
    elseif ($prio >= 900) {
      // medium prio
      $stats .= ($values['queue-time-max'] > KTPRODCHECK_ALERTRED_MAXQTIME_MEDIUM ? '<td style="color:red;">' : '<td>') . "{$values['queue-time-max']}</td>";
    }
    else {
      // high prio
      $stats .= ($values['queue-time-max'] > KTPRODCHECK_ALERTRED_MAXQTIME ? '<td style="color:red;">' : '<td>') . "{$values['queue-time-max']}</td>";
    }
    $stats .= "</tr>";
  }
  $stats .= "</table>";
  $error[] = strtr('Processlog Statistics<br>!stats', array('!stats' => $stats));

  // add usage
  $UsageCodes = array(
    'unsubscribe' => 0,
    'ifeedback' => 0,
  );
  $result = kt_query("SELECT * FROM {watchdog} WHERE type = 'kt-usage' AND timestamp > %d", strtotime("-1 day"));
  while ($pentry = kt_fetch_array($result)) {
    $UsageCodes[$pentry['message']]++;
  }

  $error[] = strtr('Usage Statistics for deprecated scripts<br><pre>!stats</pre>', array('!stats' => print_r($UsageCodes, TRUE)));

  $check['_klicktipp_prod_check_processlog'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No processlog errors reported.'),
    '#value_nok' => count($error) > 2 ? t('Processlog errors reported!') : t('Status is OK.'),
    '#description_ok' => t('Status is OK.'),
    '#description_nok' => strtr('Messages <br><ul><li>@details</li></ul>', array(
        '@details' => implode('</li><li>', $error),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;
}

// Aged subscribers
function _klicktipp_prod_check_web_aged($caller = 'internal') {

  $check = array();
  $title = 'Aged Subscribers';
  $path = 'emails';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $error = array();
  $info = array();
  $timespan = strtotime('-48 hours');

  /* get all relevant data of campaign/autoresponder within the last 48 hours */
  //TODO LastActivityDateTime does no longer exist
  /*
   $result = db_query("SELECT q.RelAutoResponderID, q.RelOwnerUserID, c.CampaignName, c.UniqueOpens, c.TotalSent, c.TotalUnsubscriptions, c.AutoResponderTriggerTypeEnum, ".
   " SUM(CASE WHEN q.StatusReason = %d THEN 1 ELSE 0 END) AS AgedUnsubscribes," .
   " COUNT(q.StatusEnum) AS Count FROM {transactional_email_queue} q ".
   " INNER JOIN {campaigns} c ON q.RelOwnerUserID = c.RelOwnerUserID AND q.RelAutoResponderID = c.CampaignID AND c.LastActivityDateTime > %d".
   " WHERE q.StatusEnum = %d GROUP BY q.RelAutoResponderID",
   TRANSACTION_REASON_SUBSCRIBER_AGED_PROCESSED, $timespan, TransactionEmails::STATUS_SENT);

   while ($c = kt_fetch_array($result)) {
   if (empty($c['AgedUnsubscribes']))
   continue;
   // format stats
   $formatted = t('<tr><td>!campaign</td><td>!link</td><td>!AgedUnsubscribes</td><td>!AgedChecksOpen</td><td>!TotalSent</td><td>!UniqueOpens</td><td>!TotalUnsubscriptions</td></tr>', array(
   '!campaign' => $c['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_CAMPAIGN ? 'NL' : 'AR',
   '!link' => implode(prod_check_link_array(substr($c['CampaignName'],0,20), $path."/{$c['RelOwnerUserID']}/{$c['RelAutoResponderID']}")),
   '!TotalSent' => $c['TotalSent'],
   '!UniqueOpens' => $c['UniqueOpens'],
   '!TotalUnsubscriptions' => $c['TotalUnsubscriptions'],
   '!AgedUnsubscribes' => $c['AgedUnsubscribes'],
   ));
   // report if more than 1 %
   if ($c['AgedUnsubscribes'] > ceil(0.01 * floatval(variable_get(KLICKTIPP_AGED_MAXFACTOR, 1)) * $c['TotalSent']))
   $error[] = $formatted;
   else
   $info[] = $formatted;
   };
   */

  $check['_klicktipp_prod_check_web_aged'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No aged subscriber check errors reported (sent emails evaluated from !date).', array('!date' => date('Y-m-d H:i:s', $timespan))),
    '#value_nok' => t('Aged subscriber check errors reported (sent emails evaluated from !date).', array('!date' => date('Y-m-d H:i:s', $timespan))),
    '#description_ok' => strtr('<table><tr><td></td><td></td><td>Aged Unsubscribes</td><td>Aged Checks Open</td><td>Total Sent</td><td>Unique Opens</td><td>Total Unsubscriptions</td></tr>@details</table>', array(
        '@details' => implode('', $info),
      )
    ),
    '#description_nok' => strtr('@count aged check errors have been reported! <br>' .
      'Errors<table><tr><td></td><td></td><td>Aged Unsubscribes</td><td>Aged Checks Open</td><td>Total Sent</td><td>Unique Opens</td><td>Total Unsubscriptions</td></tr>@errors</table>' .
      'Details<table><tr><td></td><td></td><td>Aged Unsubscribes</td><td>Aged Checks Open</td><td>Total Sent</td><td>Unique Opens</td><td>Total Unsubscriptions</td></tr>@details</table>',
      array(
        '@count' => count($error),
        '@errors' => implode('', $error),
        '@details' => implode('', $info),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;
}

// Bouncelog errors
function _klicktipp_prod_check_bouncelog($caller = 'internal') {
  if (!module_exists('dblog')) {
    return;
  }

  $check = array();
  $title = 'Bouncelog';
  $path = 'admin/reports/dblog';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $error = array();
  $codes = array(
    BounceEngine::BOUNCELOG_SPAMBOUNCE => 0,
    BounceEngine::BOUNCELOG_HARDBOUNCE => 0,
    BounceEngine::BOUNCELOG_SOFTBOUNCE => 0,
    BounceEngine::BOUNCELOG_OTHERBOUNCE => 0,
    BounceEngine::BOUNCELOG_MIMEDECODING => 0,
    BounceEngine::BOUNCELOG_BLACKLISTING => 0,
    BounceEngine::BOUNCELOG_BOUNCECHECK => 0,
    BounceEngine::BOUNCELOG_SPAMTRAP => 0,
  );

  $result = db_query("SELECT * FROM {watchdog} WHERE type = :type AND timestamp > :timestamp ORDER BY timestamp DESC LIMIT 100000", array(
    ':type' => 'bouncelog',
    ':timestamp' => strtotime('-1 day')
  ));
  while ($pentry = kt_fetch_array($result)) {
    // count type
    $vars = unserialize($pentry['variables']);
    $codes[$vars['!type']]++;
    $vars['!link'] = l('details', PRODCHECK_BASEURL . "wddetails/{$pentry['wid']}");

    // watch mime decoding errors
    if ($vars['!type'] == BounceEngine::BOUNCELOG_MIMEDECODING) {
      $vars['!data'] = implode('|', $vars['!data']);
      $error[] = t('Mime decoding error !data !link', $vars);
    }

    // watch "other"
    if ($vars['!type'] == BounceEngine::BOUNCELOG_OTHERBOUNCE) {
      $error[] = t('Unexpected !type !data !link', $vars);
    }

    // watch "spam trap"
    if ($vars['!type'] == BounceEngine::BOUNCELOG_SPAMTRAP) {
      $error[] = t('!data !link', $vars);
    }

    // watch "deliverable"
    /*
     if ($vars['!type'] == BounceEngine::BOUNCELOG_BOUNCECHECK)
     if ($vars['!data'] == BounceEngine::PROCESS_BOUNCE_TYPE_DELIVERABLE)
     $error[] = t('Bouncecheck !data !link', $vars);
     */

    // watch "blacklisting"
    if ($vars['!type'] == BounceEngine::BOUNCELOG_BLACKLISTING) {
      $RawEmailContent = $vars['!raw'];
      if (empty($RawEmailContent)) {
        continue;
      }
      $error[] = t('Hardbounce Reset by "!data" would now bounce !link', $vars);
    }
  }

  $check['_klicktipp_prod_check_bouncelog'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No bouncelog errors reported.'),
    '#value_nok' => t('Bouncelog errors reported!'),
    '#description_ok' => t('Status is OK.'),
    '#description_nok' => strtr('@count bouncelog errors have been reported! Check watchdog !link <br><ul><li>@details</li></ul>', array(
        '@count' => count($error),
        '!link' => implode(prod_check_link_array('bouncelog', $path)),
        '@details' => implode('</li><li>', $error),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;
}

// Send performance
function _klicktipp_prod_check_sendperformance($caller = 'internal') {
  if (!module_exists('dblog')) {
    return;
  }

  $check = array();
  $title = 'Send performance';
  $path = 'admin/reports/dblog';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $ProcessCodes = array(
    '!TotalProcessed' => 0,
    '!QueueRuns' => 0,
    '!MaxProcessed' => 0,
    '!QueueRunsAtMax' => 0,
    '!TotalProcessedAtMax' => 0,
    '!AverageAtMaxProcessed' => 0,
  );

  $error = array();

  // get send performance logs from watchdog
  $result = db_query("SELECT * FROM {watchdog} WHERE type = :type AND severity = :severity AND timestamp > :timestamp", array(
    ':type' => 'xhprof',
    ':severity' => WATCHDOG_INFO,
    ':timestamp' => strtotime('-1 day')
  ));
  while ($pentry = kt_fetch_array($result)) {
    $vars = unserialize($pentry['variables']);
    // profile written?
    if (!empty($vars['!url'])) {
      $vars['!ProcessTime'] = date('Y-m-d H:i:s', $pentry['timestamp']);
      $vars['!ProfilerLink'] = l('Profiler results', $vars['!url']);
      $error[] = t(/*ignore*/$pentry['message'].' at !ProcessTime !ProfilerLink ', $vars);
    }
  }

  $ProcessCodes['@count'] = count($error);
  $ProcessCodes['@details'] = implode('</li><li>', $error);

  $check['_klicktipp_prod_check_sendperformance'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No send errors reported.'),
    '#value_nok' => t('Send errors reported!'),
    '#description_ok' => strtr('@count slow runs have been reported! <br><ul><li>@details</li></ul>', $ProcessCodes),
    '#description_nok' => strtr('@count slow runs have been reported! <br><ul><li>@details</li></ul>', $ProcessCodes),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;
}

/**
 * calc blocking campaigns
 * - stats are aggregated by hourly cron, so we have one data point (history period) per hour
 * - list all campaigns in the last "history" periods, that have more pending than the queue may process
 * - output as html
 */
function _klicktipp_blocking_campaigns($history = 24) {

  $divisor = variable_get('klicktipp_wts_slices', 0);
  if (!in_array($divisor, array(2,4,8,16,32,64,128,256,512,1024))) {
    return 'No slices defined';
  }

  // get the last 5 queue watch logs from watchdog
  $result = db_query("SELECT * FROM {watchdog} WHERE type = :type AND severity = :severity ORDER BY timestamp DESC LIMIT 0,$history", array(
    ':type' => 'trans_queue_watch',
    ':severity' => WATCHDOG_INFO,
  ));
  $first = TRUE;
  $periods = array();
  $slices = array();
  while ($pentry = kt_fetch_array($result)) {
    // extract campaign stats
    $vars = unserialize($pentry['variables']);
    $last = $vars['!campaigns_trans'] + $vars['!campaigns_autoresponder'] + $vars['!campaigns_newsletter'];
    foreach($last as $campaignid => $data) {
      $remainder = $data['remainder'];
      // last pending count
      if ($first) {
        $campaign = Campaigns::FromID($data['uid'], $campaignid);
        if ($campaign) {
          $data['campaignname'] = $campaign->GetData('CampaignName');
          $data['type'] = $campaign->GetData('AutoResponderTriggerTypeEnum');
          $slices[$remainder][$campaignid] = $data;
        }
      }
      // remember previous counts
      $periods[$campaignid]++;
    }
    $first = FALSE;
  }

  ksort($slices);

  $stats = "<table border='1'><tr><th>Slice (mod 1024)</th><th>Type</th><th>Campaign</th><th>User</th><th>Pending</th><th>Periods ($history)</th></tr>";
  foreach ($slices as $slice => $value) {
    if ($slice < 0) {
      // customer slices
      $lower = $upper = $slice;
    }
    else {
      [$remainder, $divisor, $lower, $upper] = TransactionEmails::CalcSlicesFromRemainder($slice);
    }

    $rowcount = count($value);
    $first = TRUE;

    foreach($value as $campaignid => $data)  {
      $stats .= '<tr style="border:1px solid;">';
      if ($first) {
        $stats .= "<td rowspan='$rowcount'>$slice ($lower..$upper)</td>";
      }
      $style = '';
      $attr = array('target' => '_blank');
      switch ($data['type']) {
        case Campaigns::TRIGGER_TYPE_CAMPAIGN:
        case Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN:
          $link = l("{$data['campaignname']} ($campaignid)", "emails/{$data['uid']}/$campaignid", array('absolute' => TRUE, 'attributes' => $attr));
          $stats .= "<td$style>newsletter</td>";
          break;
        case Campaigns::TRIGGER_TYPE_SUBSCRIPTION:
        case Campaigns::TRIGGER_TYPE_DATETIME:
        case Campaigns::TRIGGER_TYPE_BIRTHDAY:
        case Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION:
        case Campaigns::TRIGGER_TYPE_SMS_DATETIME:
        case Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY:
          if ($periods[$campaignid] > 0.8 * $history) {
            $style = ' style="color:red;"';
            $attr['style'] = "color:red";
          }
          $objCampaign = Campaigns::FromID($data['uid'], $campaignid);
          $editUrl = '';
          if ($objCampaign) {
            $editUrl = $objCampaign->getEntityUrlEdit();
          }
          $link = l("{$data['campaignname']} ($campaignid)", $editUrl, array('absolute' => TRUE, 'attributes' => $attr));
          $stats .= "<td$style>autoresponder</td>";
          break;
        case Campaigns::TRIGGER_TYPE_PROCESSFLOW:
          if ($periods[$campaignid] > 0.8 * $history) {
            $style = ' style="color:red;"';
            $attr['style'] = "color:red";
          }
          $objCampaign = \App\Klicktipp\CampaignsProcessFlow::FromID($data['uid'], $campaignid);
          if ($objCampaign) {
            $link = l("{$data['campaignname']} ($campaignid)", $objCampaign->getEntityUrlEdit(), array('absolute' => TRUE, 'attributes' => $attr));
          }
          $stats .= "<td$style>automation</td>";
          break;
        default:
          $link = 'error';
          break;
      }
      $stats .= "<td$style>$link</td>";
      $stats .= "<td$style>{$data['uid']}</td>";
      $stats .= "<td$style>{$data['count']}</td>";
      $percent = ($history > 0) ? floor($periods[$campaignid] * 100 / $history) : "";
      $stats .= "<td$style>$percent %</td>";
      $stats .= "</tr>";
      $first = FALSE;
    }
  }
  $stats .= "</table>";

  return $stats;
}


function _klicktipp_prod_check_trans_queue_watch($caller = 'internal') {
  if (!module_exists('dblog')) {
    return;
  }

  $check = array();
  $title = 'Transactional queue watch';
  $path = 'admin/reports/dblog';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $error = array();

  $stats = _klicktipp_blocking_campaigns();
  $error[] = strtr('Blocking Campaign Statistics<br>!stats', array('!stats' => $stats));

  $check['_klicktipp_prod_check_trans_queue_watch'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No processlog errors reported.'),
    '#value_nok' => count($error) > 2 ? t('Processlog errors reported!') : t('Status is OK.'),
    '#description_ok' => t('Status is OK.'),
    '#description_nok' => strtr('Messages <br><ul><li>@details</li></ul>', array(
        '@details' => implode('</li><li>', $error),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;
}

/**
 * @param string $caller
 * @param int $RelOwnerUserID 380=klicktipp marketing account
 * @param int $WebinarID 2286=klicktipp webinar
 *
 * @return array
 */
function _klicktipp_prod_check_webinar($caller = 'internal', $RelOwnerUserID = 380, $WebinarID = 2286) {

  $Result = array(
    'caller' => $caller,
    'messages' => array(),
    'Registrations' => array(
      'yesterday' => array(),
      'upcoming' => array(),
      'lastweek' => array(),
    ),
    'Participations' => array(
      'yesterday' => array(),
      'lastweek' => array(),
    ),
    'Customer' => array(
      'yesterday' => 0,
      'lastweek' => 0,
    ),
    'ExistingCustomer' => array(
      'yesterday' => 0,
      'lastweek' => 0,
    ),
    'ReRegistrations' => 0,
    'Trouble' => array(),
    'Recording' => 0,
    'WebinarTags' => 0,
  );

  //get the webinar

  $ObjectWebinar = ToolWebinar::FromID($RelOwnerUserID, $WebinarID);

  if ( !$ObjectWebinar ) {
    $Result['messages'][] = "<p>Webinar does not exist.</p>";
    return _klicktipp_prod_check_webinar_result($Result);
  }

  $ArrayWebinar = $ObjectWebinar->GetData();

  //@note Multivalues: $ReferenceID in marketing account is always 0
  $ReferenceID = 0;

  // --- prepare checks

  //
  $RegistrationTagID = $ArrayWebinar['Tags']['Registration']['id'];
  $ReRegistrationTagID = $ArrayWebinar['Tags']['Re-Registration']['id'];
  $ParticipationTagID = $ArrayWebinar['Tags']['Participation']['id'];
  $CustomerTagID = $ArrayWebinar['Tags']['Customer']['id'];
  $TroubleshootingTagID = $ArrayWebinar['Tags']['Troubleshooting']['id'];
  $RecordingTagID = $ArrayWebinar['Tags']['Recording']['id'];

  $WebinarStartCustomFieldID = $ArrayWebinar['CustomFields']['WebinarStart']['id'];

  $yesterday_midnight = strtotime('yesterday 00:00:00');
  $today_midnight = strtotime('now 00:00:00');
  $last_week_midnight = strtotime('-8 days 00:00:00');

  // --- validate settings

  if ( empty($RegistrationTagID) ) {
    $Result['messages'][] = "<p>Webinar has no registration tag defined</p>";
    return _klicktipp_prod_check_webinar_result($Result);
  }

  if ( empty($WebinarStartCustomFieldID) ) {
    $Result['messages'][] = "<p>Webinar has no registration custom field defined</p>";
    return _klicktipp_prod_check_webinar_result($Result);
  }

  // --- get registrations

  $WebinarStartTimes = array(); //store the webinar start times 'SubscriberID' => timestamp

  //registrations for yesterdays webinars
  $result = db_query('SELECT RelSubscriberID, ValueText FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelFieldID = :FieldID AND ReferenceID = :ReferenceID AND (ValueText BETWEEN :RangeFrom AND :RangeTo)', array(
    ':RelOwnerUserID' => $RelOwnerUserID,
    ':FieldID' => $WebinarStartCustomFieldID,
    ':ReferenceID' => $ReferenceID,
    ':RangeFrom' => $yesterday_midnight,
    ':RangeTo' => $today_midnight,
  ));

  while ( $s = kt_fetch_array($result) ) {

    $WebinarStartTimes[$s['RelSubscriberID']] = $s['ValueText'];

    $taggings = Subscribers::RetrieveTaggingsOfSubscriber($RelOwnerUserID, $s['RelSubscriberID'], $ReferenceID);

    foreach ( $taggings as $tagging ) {
      $Result['Registrations']['yesterday'][$s['RelSubscriberID']][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
    }

  }

  //registrations for upcoming webinars
  $result = db_query('SELECT RelSubscriberID, ValueText FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelFieldID = :FieldID AND ReferenceID = :ReferenceID AND ValueText > :RangeFrom', array(
    ':RelOwnerUserID' => $RelOwnerUserID,
    ':FieldID' => $WebinarStartCustomFieldID,
    ':ReferenceID' => $ReferenceID,
    ':RangeFrom' => $today_midnight,
  ));

  while ( $s = kt_fetch_array($result) ) {

    $WebinarStartTimes[$s['RelSubscriberID']] = $s['ValueText'];

    $Result['Registrations']['upcoming'][] = $s['RelSubscriberID'];
  }

  //registrations for last weeks webinars
  //Note: the whole process with scarcity and webinar recording takes 1 week, check how many new customer from that day
  $result = db_query('SELECT RelSubscriberID, ValueText FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelFieldID = :FieldID AND ReferenceID = :ReferenceID AND (ValueText BETWEEN :RangeFrom AND :RangeTo)', array(
    ':RelOwnerUserID' => $RelOwnerUserID,
    ':FieldID' => $WebinarStartCustomFieldID,
    ':ReferenceID' => $ReferenceID,
    ':RangeFrom' => $last_week_midnight,
    ':RangeTo' => strtotime('+24 hours', $last_week_midnight),
  ));

  while ( $s = kt_fetch_array($result) ) {

    $WebinarStartTimes[$s['RelSubscriberID']] = $s['ValueText'];

    $taggings = Subscribers::RetrieveTaggingsOfSubscriber($RelOwnerUserID, $s['RelSubscriberID'], $ReferenceID);

    foreach ( $taggings as $tagging ) {
      $Result['Registrations']['lastweek'][$s['RelSubscriberID']][$tagging['RelTagID']] = $tagging['SubscriptionDate'];
    }

  }

  // --- get participations

  //participations of yesterdays webinars
  foreach( $Result['Registrations']['yesterday'] as $sid => $taggings ) {

    if ( !empty($taggings[$ParticipationTagID]) && $taggings[$ParticipationTagID] >= $yesterday_midnight && $taggings[$ParticipationTagID] <= $today_midnight ) {
      $Result['Participations']['yesterday'][] = $sid;
    }

    if ( !empty($taggings[$ReRegistrationTagID]) ) {
      $Result['ReRegistrations']++;
    }

    if ( !empty($taggings[$CustomerTagID]) && $taggings[$CustomerTagID] >= $yesterday_midnight && $taggings[$CustomerTagID] <= $today_midnight ) {
      $Result['Customer']['yesterday']++;
    }

    if ( !empty($taggings[$CustomerTagID]) && $taggings[$CustomerTagID] < $yesterday_midnight ) {
      $Result['ExistingCustomer']['yesterday']++;
    }

    if ( !empty($taggings[$TroubleshootingTagID]) && $taggings[$TroubleshootingTagID] >= $yesterday_midnight && $taggings[$TroubleshootingTagID] <= $today_midnight ) {
      $Result['Trouble'][] = $sid;
    }

  }

  //participations of yesterdays webinars
  foreach( $Result['Registrations']['lastweek'] as $sid => $taggings ) {

    if ( !empty($taggings[$ParticipationTagID]) && $taggings[$ParticipationTagID] >= $last_week_midnight && $taggings[$ParticipationTagID] <= strtotime('+24 hours', $last_week_midnight) ) {
      $Result['Participations']['lastweek'][] = $sid;
    }

    if ( !empty($taggings[$CustomerTagID]) && $taggings[$CustomerTagID] >= $last_week_midnight && $taggings[$CustomerTagID] <= $yesterday_midnight ) {
      $Result['Customer']['lastweek']++;
    }

    if ( !empty($taggings[$CustomerTagID]) && $taggings[$CustomerTagID] < $last_week_midnight ) {
      $Result['ExistingCustomer']['lastweek']++;
    }

    if ( !empty($taggings[$RecordingTagID]) && $taggings[$RecordingTagID] >= $last_week_midnight && $taggings[$RecordingTagID] <= $yesterday_midnight ) {
      $Result['Recording']['lastweek']++;
    }

  }

  // --- check autoresponder send times

  $AutoresponderIDs = array();
  $WebinarTagIDs = array(); //tag ids that will be assigned during the webinar
  $WebinarFirstAR = 0;
  $WebinarLastAR = 0;
  $now = time();
  foreach ($ArrayWebinar['Events'] as $event) {

    //collect all autoresponder ids that are send during the whole webinar period
    $AutoresponderIDs[] = $event['ar']['id'];

    //collect all tags that are assigned during the webinar video -> used later for the taggings check
    if ( !empty($event['tag']) ) {
      $WebinarTagIDs[] = $event['tag'];
    }

    if ( $event['trigger']['base'] == 2 && !empty($event['trigger']['strtotime'])) { //base == 2 -> on webinar start

      $sendtime = strtotime($event['trigger']['strtotime'], $now);

      if ( empty($WebinarFirstAR) || $sendtime < $WebinarFirstAR ) {
        $WebinarFirstAR = $sendtime;
      }
      elseif ( empty($WebinarLastAR) || $sendtime > $WebinarLastAR ) {
        $WebinarLastAR = $sendtime;
      }

    }

  }

  $WebinarFirstAR = $WebinarFirstAR - $now; //relative time before the webinar start
  $WebinarLastAR = $WebinarLastAR - $now; //relative time after the webinar start

  $registered_subscribers = array_unique(array_merge(array_keys($Result['Registrations']['yesterday']), $Result['Registrations']['upcoming']));

  foreach ( $registered_subscribers as $sid ) {

    $FullSubscriber = array();

    $webinar_range_from = $WebinarStartTimes[$sid] + $WebinarFirstAR;
    $webinar_range_to = $WebinarStartTimes[$sid] + $WebinarLastAR;

    //@note: Multivalue taggings - count all campaign runs
    $ar_count = db_query("SELECT COUNT(*) FROM {".AutoresponderQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND ReferenceID = :ReferenceID AND RelAutoresponderID IN (:ARs) AND (TimeToSend BETWEEN :RangeFrom AND :RangeTo)", array(
      ':RelOwnerUserID' => $RelOwnerUserID,
      ':SubscriberID' => $sid,
      ':ReferenceID' => $ReferenceID,
      ':ARs' => $AutoresponderIDs,
      ':RangeFrom' => $webinar_range_from,
      ':RangeTo' => $webinar_range_to,
    ))->fetchField();

    if (empty($ar_count)) {

      $FullSubscriber = Subscribers::RetrieveFullSubscriber($RelOwnerUserID, $sid, $ReferenceID);

      switch ($FullSubscriber['SubscriptionStatus']) {
        case Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED:
          if ($FullSubscriber['BounceType'] == Subscribers::BOUNCETYPE_NOTBOUNCED) {
            $reason = 'Unknown: The subscribers are active and should receive emails.';
          }
          else {
            $reason = 'Bounce: The subscribers have either a soft or hard bounce.';
          }
          break;
        case Subscribers::SUBSCRIPTIONSTATUS_OPTIN:
          //$reason = 'Pending: The subscribers have not yet confirmed their subscription.';
          $reason = ''; //do not log pending subscribers
          break;
        case Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED:
          $reason = 'Unsubscribed: The subscribers have unsubscribed.';
          break;
        default:
          $reason = 'Unexpected reason: Please check and update the prod check.';

      }

      if ( !empty($reason) ) {
        $Result['NoAutoresponders'][$reason][] = array(
          'SubscriberID' => $FullSubscriber['SubscriberID'],
          'EmailAddress' => Subscribers::DepunycodeEmailAddress($FullSubscriber['EmailAddress']),
        );
      }

    }

    // --- check taggings during the webinar

    if ( in_array($sid, $Result['Participations']['yesterday']) ) {

      $tag_count = db_query("SELECT COUNT(*) FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelTagID IN (:TagIDs) AND ReferenceID = :ReferenceID AND (SubscriptionDate BETWEEN :RangeFrom AND :RangeTo)", array(
        ':RelOwnerUserID' => $RelOwnerUserID,
        ':SubscriberID' => $sid,
        ':TagIDs' => $WebinarTagIDs,
        ':ReferenceID' => $ReferenceID,
        ':RangeFrom' => $webinar_range_from,
        ':RangeTo' => $webinar_range_to,
      ))->fetchField();

      if (empty($tag_count)) {

        if (empty($FullSubscriber)) {
          //do not retrieve the subscription again
          $FullSubscriber = Subscribers::RetrieveFullSubscriber($RelOwnerUserID, $sid, $ReferenceID);
        }

        $Result['NoWebinarTags'][] = array(
          'SubscriberID' => $FullSubscriber['SubscriberID'],
          'EmailAddress' => Subscribers::DepunycodeEmailAddress($FullSubscriber['EmailAddress']),
        );

      }
      else {
        $Result['WebinarTags'] += $tag_count;
      }

    }

  }



  // --- create statistics

  $Result['messages'][] = '<p>Statistics for yesterday webinars</p><ul>';
  if ( count($Result['Registrations']['yesterday']) > 0 ) {
    $Result['messages'][] = strtr('<li>!count contacts registered for yesterdays webinars, !recount (!repercent%) re-registered.</li>', array(
      '!count' => count($Result['Registrations']['yesterday']),
      '!recount' => $Result['ReRegistrations'],
      '!repercent' => round($Result['ReRegistrations'] / count($Result['Registrations']['yesterday']) * 100),
    ));
    $Result['messages'][] = strtr('<li>!count (!percent%) contacts participated in yesterdays webinars.</li>', array(
      '!count' => count($Result['Participations']['yesterday']),
      '!percent' => round(count($Result['Participations']['yesterday']) / count($Result['Registrations']['yesterday']) * 100),
    ));
    $Result['messages'][] = strtr('<li>!count (!percent%) participants became customers in yesterdays webinars, !excount (!expercent%) were customers before the webinar.</li>', array(
      '!count' => $Result['Customer']['yesterday'],
      '!percent' => round($Result['Customer']['yesterday'] / count($Result['Participations']['yesterday']) * 100),
      '!excount' => $Result['ExistingCustomer']['yesterday'],
      '!expercent' => round($Result['ExistingCustomer']['yesterday'] / count($Result['Participations']['yesterday']) * 100),
    ));
    $Result['messages'][] = strtr('<li>!count webinar video tags were assigned in yesterdays webinar.</li>', array(
      '!count' => $Result['WebinarTags'],
    ));

    $Result['messages'][] = strtr('<li>!count participants had trouble in yesterdays webinar and clicked on the troubleshoot link.</li>', array(
      '!count' => count($Result['Trouble']),
    ));

  }
  else {
    $Result['messages'][] = '<li>No contacts registered for yesterdays webinars.</li>';
  }

  $Result['messages'][] = '</ul>';

  $Result['messages'][] = '<p>Statistics for the webinars 1 week ago.</p><ul>';
  if ( count($Result['Registrations']['lastweek']) > 0 ) {
    $Result['messages'][] = strtr('<li>!count contacts registered for webinars 1 week ago.</li>', array(
      '!count' => count($Result['Registrations']['lastweek']),
    ));
    $Result['messages'][] = strtr('<li>!count (!percent%) contacts participated in the webinars 1 week ago.</li>', array(
      '!count' => count($Result['Participations']['lastweek']),
      '!percent' => round(count($Result['Participations']['lastweek']) / count($Result['Registrations']['lastweek']) * 100),
    ));
    $Result['messages'][] = strtr('<li>!count (!percent%) participants became customers within 1 week after the webinars 1 week ago, !excount (!expercent%) were customers before the webinar.</li>', array(
      '!count' => $Result['Customer']['lastweek'],
      '!percent' => round($Result['Customer']['lastweek'] / count($Result['Participations']['lastweek']) * 100),
      '!excount' => $Result['ExistingCustomer']['lastweek'],
      '!expercent' => round($Result['ExistingCustomer']['lastweek'] / count($Result['Participations']['lastweek']) * 100),
    ));
    $Result['messages'][] = strtr('<li>!count (!percent%) of the registered contacts watched the recording of the webinars 1 week ago.</li>', array(
      '!count' => $Result['Recording'],
      '!percent' => round($Result['Recording'] / count($Result['Registrations']['lastweek']) * 100),
    ));
  }
  else {
    $Result['messages'][] = '<li>No contacts registered for webinars one week ago.</li>';
  }

  $Result['messages'][] = '</ul>';

  $Result['messages'][] = '<p>Statistics for upcoming webinars</p><ul>';
  $Result['messages'][] = strtr('<li>!count contacts registered for upcoming webinars.</li>', array(
    '!count' => count($Result['Registrations']['upcoming']),
  ));

  $Result['messages'][] = '</ul>';

  if ( !empty($Result['NoAutoresponders']) ) {

    $Result['messages'] = ((empty($Result['messages']))) ? array() : $Result['messages'];

    $Result['messages'][] = '<p>No autoresponders have been scheduled for the following subscribers.</p><ul>';

    foreach ($Result['NoAutoresponders'] as $reason => $subscribers ) {

      $Result['messages'][] = "<li style='font-weight:bold;'>$reason</li>";

      foreach ( $subscribers as $FullSubscriber ) {
        $Result['messages'][] = strtr("<li><a href='!APPURLsubscriber/$RelOwnerUserID/edit/!id' target='_blank'>!email</a></li>", array(
          '!id' => $FullSubscriber['SubscriberID'],
          '!email' => $FullSubscriber['EmailAddress'],
          '!APPURL' => APP_URL,
        ));
      }
    }

    $Result['messages'][] = '</ul>';

  }

  if ( !empty($Result['NoWebinarTags']) ) {

    $Result['messages'] = ((empty($Result['messages']))) ? array() : $Result['messages'];

    $Result['messages'][] = '<p>No tags have been assigned during the webinar for the following subscribers.</p><ul>';

    foreach ($Result['NoWebinarTags'] as $FullSubscriber ) {

      $Result['messages'][] = strtr("<li><a href='!APPURLsubscriber/$RelOwnerUserID/edit/!id' target='_blank'>!email</a></li>", array(
        '!id' => $FullSubscriber['SubscriberID'],
        '!email' => $FullSubscriber['EmailAddress'],
        '!APPURL' => APP_URL,
      ));
    }

    $Result['messages'][] = '</ul>';

  }

  return _klicktipp_prod_check_webinar_result($Result);

}

function _klicktipp_prod_check_webinar_result($Result) {

  $check = array();
  $title = 'Webinar subscriptions';
  $path = 'webinar';
  $caller = $Result['caller'];
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $messages = '';
  foreach($Result['messages'] as $msg ) {
    $messages .= $msg;
  }

  $check['_klicktipp_prod_check_webinar'] = array(
    '#title' => $title,
    '#state' => FALSE,
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No webinar statistics available.'),
    '#value_nok' => t('Webinar statistics.'),
    '#description_ok' => '',
    '#description_nok' => $messages,
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;

}

// Content include API errors
function _klicktipp_prod_check_content_include_api($caller = 'internal') {

  $check = array();
  $title = 'Content include API (ciapi)';
  $path = '';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $error = array();

  /*
   * TEST: Check for content_include_api errors (watchdog type 'ciapi')
   */

  $result = db_query("SELECT message, timestamp, variables FROM {watchdog} WHERE type = :type AND timestamp > :timestamp", array(
    ':type' => 'ciapi',
    ':timestamp' => strtotime('-1 day')
  ));
  while ($ciapi = kt_fetch_array($result)) {
    $ciapi['message'] = strtr($ciapi['message'], unserialize($ciapi['variables']));
    $error[] = "Date: " . date("Y-m-d H:i:s", $ciapi['timestamp']) . "<br />" . $ciapi['message'];
  }

  $check['_klicktipp_prod_check_ciapi'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No content include API errors reported.'),
    '#value_nok' => t('Content include API errors reported!'),
    '#description_ok' => t('Status is OK.'),
    '#description_nok' => strtr('@count content include API errors have been reported! <ul><li>@details</li></ul>', array(
        '@count' => count($error),
        '@details' => implode('</li><li>', $error),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;
}

// Klicktipp test errors
function _klicktipp_prod_check_testlog($caller = 'internal') {
  if (!module_exists('dblog')) {
    return;
  }

  $check = array();
  $title = 'Testlog';
  $path = 'admin/reports/dblog';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $error = array();
  $count = 0;

  $result = db_query("SELECT message, timestamp, variables, severity FROM {watchdog} WHERE type = :type AND timestamp > :timestamp", array(
    ':type' => 'klicktipp_tests',
    ':timestamp' => strtotime('-1 day')
  ));
  while ($testlog = kt_fetch_array($result)) {
    $count++;
    $testlog['message'] = strtr($testlog['message'], unserialize($testlog['variables']));
    if ($testlog['severity'] == WATCHDOG_ERROR) {
      $error[] = "Date: " . date("Y-m-d H:i:s", $testlog['timestamp']) . "<br />" . $testlog['message'];
    }
  }
  if (empty($count)) {
    $error[] = "No tests running. Date: " . date("Y-m-d H:i:s");
  }

  $check['_klicktipp_prod_check_testlog'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No test log errors reported.'),
    '#value_nok' => t('Test log errors reported!'),
    '#description_ok' => t('Status is OK. @count simpltests executed.', array(
        '@count' => $count,
      )
    ),
    '#description_nok' => strtr('@count simpltests executed. @errorcount failed! <ul><li>@details</li></ul>', array(
        '@count' => $count,
        '@errorcount' => count($error),
        '@details' => implode('</li><li>', $error),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;
}

// misc klicktipp log messages
function _klicktipp_prod_check_klicktipplog($caller = 'internal') {
  if (!module_exists('dblog')) {
    return;
  }

  $check = array();
  $title = 'Klicktipp log';
  $path = 'admin/reports/dblog';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $CheckTypes = array(
    'senderscore',
    'kt-controller',
    'kt-subscription',
    'kt-hardening',
    'ciapi',
    'php',
    'send_engine',
    'sms-inbound',
    'bounce_q',
    'amember rpc',
    'trans_queue_error',
    'youtube/rapidapi', //error with the api
    'youtube/rapidapi/limit' //monthly limit reached (1 entry enough)
//    'DecryptURL' => '',
  );
  $error = array();
  $sms_inbound_count = 0;

  // check all messages from the last 24 hours
  $result = db_query("SELECT * FROM {watchdog} WHERE timestamp > :timestamp AND type in (:types) LIMIT 0,1000",
    [':timestamp' => strtotime("-1 day"), ':types' => $CheckTypes]);
  while ($pentry = kt_fetch_array($result)) {
    // check for type
    $vars = unserialize($pentry['variables']);
    $vars['!wdlink'] = l('details', PRODCHECK_BASEURL . "wddetails/{$pentry['wid']}");
    $vars['!wddate'] = date('Y-m-d H:i:s', $pentry['timestamp']);

    // limit number of listed errors (avoid out of memory)
    if (count($error) > 500) {
      $error[] = t('--- more than 500 errors ---');
      break;
    }

    if ($pentry['type'] == 'DecryptURL') {
//      $error[] = t('DecryptURL @query !wdlink', $vars);
      //TODO there are masses of encryption errors caused by deleted entities
      //TODO filter these and show others
    }
    elseif ($pentry['type'] == 'senderscore') {
      if (empty($vars['!host']['failover']) && empty($vars['!host']['whitelabel'])) {
        // only show error if not failover/whitelabel
        $error[] = t('Senderscore !hostname (!IP) fetch failed at !date.', $vars);
      }
    }
    elseif ($pentry['type'] == 'sms-inbound') {
      $sms_inbound_count++;
      // show one error entry with the amount of warnings generated
      $error['smsinbound'] = t('!count sms inbounds without email address have been reported!', array(
        '!count' => $sms_inbound_count,
      ));
    }
    elseif ($pentry['type'] == 'youtube/rapidapi/limit') {
      //one error entry is enough that the monthy limit has been reached
      $error['youtube/rapidapi/limit'] = $pentry['message'];
    }
    elseif (in_array($pentry['type'], $CheckTypes)) {
      $error[] = t(/*ignore*/$pentry['message'] . ' !wddate !wdlink', $vars);
    }
    else {
      // show all errors with level error or higher
      if ($pentry['severity'] <= WATCHDOG_ERROR) {
        $error[] = t(/*ignore*/$pentry['message'] . ' !wddate !wdlink', $vars);
      }
    }
  }

  $check['_klicktipp_prod_check_klicktipplog'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No Klicktipp errors reported.'),
    '#value_nok' => t('Klicktipp errors reported!'),
    '#description_ok' => t('Status is OK.'),
    '#description_nok' => strtr('@count Klicktipp errors have been reported! Check watchdog !link <br><ul><li>@details</li></ul>', array(
        '@count' => count($error),
        '!link' => implode(prod_check_link_array('klicktipplog', $path)),
        '@details' => implode('</li><li>', $error),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;
}

// Transactional queue consistency
function _klicktipp_prod_check_queue_consistency($caller = 'internal') {

  $title = 'Transactional/Autoresponder/Newsletter queue consistency';
  $check = [];
  $error = [];

  /*
   * TEST: Check transactional_email_queue if all data belongs to the same user
   * This query ensures, that queue creation does not mixup data, which can be caused by programming errors in the caching functions.
   * THE QUERY EATS A LOT OF TIME AND MEMORY, SO WE HAVE TO LIMIT IT
   */
  $result = kt_query("SELECT q.*, c.CampaignID, c.AutoResponderTriggerTypeEnum, s.SubscriberID " .
    " FROM {".TransactionalQueue::TABLE_NAME."} q USE INDEX (idx_status_tts_owner)" .
    " LEFT JOIN {campaigns} c ON c.CampaignID = q.RelAutoResponderID AND c.RelOwnerUserID = q.RelOwnerUserID " .
    " LEFT JOIN {subscribers} s ON s.SubscriberID = q.RelSubscriberID AND s.RelOwnerUserID = q.RelOwnerUserID " .
    " WHERE q.StatusEnum = %d AND q.TimeToSend >= %d AND q.TimeToSend <= %d AND (c.CampaignID IS NULL OR s.SubscriberID IS NULL)" .
    " ORDER BY RAND() LIMIT 0,100000",
    TransactionEmails::STATUS_SENT, strtotime('-1 days'), strtotime('-2 days'));
  while ($q = kt_fetch_array($result)) {
    $data = [
      '!RelAutoResponderID' => $q['RelAutoResponderID'],
      '!RelSubscriberID' => $q['RelSubscriberID'],
      '!ReferenceID' => $q['ReferenceID'],
      '!status' => $q['StatusEnum'] == TransactionEmails::STATUS_SENT ? 'sent' : 'failed',
      '!date' => date('Y-m-d H:i:s', $q['TimeToSend']),
      '!uid' => $q['RelOwnerUserID'],
    ];
    if (empty($q['CampaignID'])) {
      $error[] = strtr("Queue !RelSubscriberID !RelAutoResponderID !date !status: RelAutoResponderID !RelAutoResponderID not in User !uid", $data);
    }
    if (empty($q['SubscriberID'])) {
      $error[] = strtr("Queue !RelSubscriberID !RelAutoResponderID !date !status: RelSubscriberID !RelSubscriberID not in User !uid", $data);
    }
    if (!Campaigns::IsProcessflow($q['AutoResponderTriggerTypeEnum'])) {
      $error[] = strtr("Transactional queue holds newsletter or autoresponder entries: !RelSubscriberID (SubscriberID), !RelAutoResponderID (CampaignID), !RelEmailID (EmailID), !uid (UserID) and !ReferenceID (ReferenceID)", $data);
    }
    if (count($error) > 100) {
      $error[] = "Transactional queue consistency - User check terminated after 100 errors";
      break;
    }
  }

  /*
 * TEST: Check autoresponder_queue if all data belongs to the same user
 * This query ensures, that queue creation does not mixup data, which can be caused by programming errors in the caching functions.
 * THE QUERY EATS A LOT OF TIME AND MEMORY, SO WE HAVE TO LIMIT IT
 */
  $result = kt_query("SELECT q.*, c.CampaignID, c.AutoResponderTriggerTypeEnum, s.SubscriberID, e.EmailID " .
    " FROM {".AutoresponderQueue::TABLE_NAME."} q USE INDEX (idx_status_tts_owner)" .
    " LEFT JOIN {campaigns} c ON c.CampaignID = q.RelAutoResponderID AND c.RelOwnerUserID = q.RelOwnerUserID " .
    " LEFT JOIN {subscribers} s ON s.SubscriberID = q.RelSubscriberID AND s.RelOwnerUserID = q.RelOwnerUserID " .
    " LEFT JOIN {emails} e ON e.EmailID = q.RelEmailID AND e.RelUserID = q.RelOwnerUserID " .
    " WHERE q.StatusEnum = %d AND q.TimeToSend >= %d AND q.TimeToSend <= %d AND (c.CampaignID IS NULL OR s.SubscriberID IS NULL OR e.EmailID IS NULL)" .
    " ORDER BY RAND() LIMIT 0,100000",
    TransactionEmails::STATUS_SENT, strtotime('-1 days'), strtotime('-2 days'));
  while ($q = kt_fetch_array($result)) {
    $data = [
      '!RelAutoResponderID' => $q['RelAutoResponderID'],
      '!RelSubscriberID' => $q['RelSubscriberID'],
      '!ReferenceID' => $q['ReferenceID'],
      '!RelEmailID' => $q['RelEmailID'],
      '!status' => $q['StatusEnum'] == TransactionEmails::STATUS_SENT ? 'sent' : 'failed',
      '!date' => date('Y-m-d H:i:s', $q['TimeToSend']),
      '!uid' => $q['RelOwnerUserID'],
    ];
    if (empty($q['CampaignID'])) {
      $error[] = strtr("Queue !RelSubscriberID !RelAutoResponderID !date !status: RelAutoResponderID !RelAutoResponderID not in User !uid", $data);
    }
    if (empty($q['SubscriberID'])) {
      $error[] = strtr("Queue !RelSubscriberID !RelAutoResponderID !date !status: RelSubscriberID !RelSubscriberID not in User !uid", $data);
    }
    if (empty($q['EmailID'])) {
      $error[] = strtr("Queue !RelSubscriberID !RelAutoResponderID !date !status: RelEmailID !RelEmailID not in User !uid", $data);
    }
    if (!Campaigns::IsAutoresponder($q['AutoResponderTriggerTypeEnum'])) {
      $error[] = strtr("Autoresponder queue holds newsletter or processflow entries: !RelSubscriberID (SubscriberID), !RelAutoResponderID (CampaignID), !RelEmailID (EmailID), !uid (UserID) and !ReferenceID (ReferenceID)", $data);
    }
    if (count($error) > 100) {
      $error[] = "Autoresponder queue consistency - User check terminated after 100 errors";
      break;
    }
  }

  /*
   * TEST: Check newsletter_queue if all data belongs to the same user
   * This query ensures, that queue creation does not mixup data, which can be caused by programming errors in the caching functions.
   * THE QUERY EATS A LOT OF TIME AND MEMORY, SO WE HAVE TO LIMIT IT
   */
  $result = kt_query("SELECT q.*, c.CampaignID, c.AutoResponderTriggerTypeEnum, s.SubscriberID, e.EmailID " .
    " FROM {".NewsletterQueue::TABLE_NAME."} q USE INDEX (idx_status_tts_owner)" .
    " LEFT JOIN {campaigns} c ON c.CampaignID = q.RelAutoResponderID AND c.RelOwnerUserID = q.RelOwnerUserID " .
    " LEFT JOIN {subscribers} s ON s.SubscriberID = q.RelSubscriberID AND s.RelOwnerUserID = q.RelOwnerUserID " .
    " LEFT JOIN {emails} e ON e.EmailID = q.RelEmailID AND e.RelUserID = q.RelOwnerUserID " .
    " WHERE q.StatusEnum = %d AND q.TimeToSend >= %d AND q.TimeToSend <= %d AND (c.CampaignID IS NULL OR s.SubscriberID IS NULL OR e.EmailID IS NULL)" .
    " ORDER BY RAND() LIMIT 0,100000",
    TransactionEmails::STATUS_SENT, strtotime('-1 days'), strtotime('-2 days'));
  while ($q = kt_fetch_array($result)) {
    $data = [
      '!RelAutoResponderID' => $q['RelAutoResponderID'],
      '!RelSubscriberID' => $q['RelSubscriberID'],
      '!ReferenceID' => $q['ReferenceID'],
      '!RelEmailID' => $q['RelEmailID'],
      '!status' => $q['StatusEnum'] == TransactionEmails::STATUS_SENT ? 'sent' : 'failed',
      '!date' => date('Y-m-d H:i:s', $q['TimeToSend']),
      '!uid' => $q['RelOwnerUserID'],
    ];
    if (empty($q['CampaignID'])) {
      $error[] = strtr("Queue !RelSubscriberID !RelAutoResponderID !date !status: RelAutoResponderID !RelAutoResponderID not in User !uid", $data);
    }
    if (empty($q['SubscriberID'])) {
      $error[] = strtr("Queue !RelSubscriberID !RelAutoResponderID !date !status: RelSubscriberID !RelSubscriberID not in User !uid", $data);
    }
    if (empty($q['EmailID'])) {
      $error[] = strtr("Queue !RelSubscriberID !RelAutoResponderID !date !status: RelEmailID !RelEmailID not in User !uid", $data);
    }
    if (!Campaigns::IsNewsletter($q['AutoResponderTriggerTypeEnum'])) {
      $error[] = strtr("Newsletter queue holds autoresponder or processflow entries: !RelSubscriberID (SubscriberID), !RelAutoResponderID (CampaignID), !RelEmailID (EmailID), !uid (UserID) and !ReferenceID (ReferenceID)", $data);
    }
    if (count($error) > 100) {
      $error[] = "Newsletter queue consistency - User check terminated after 100 errors";
      break;
    }
  }

  /*
   * TEST: Check if all "queue creating" campaign have jobs in queue
   */
  $count = db_query("SELECT COUNT(*) FROM {campaigns} c " .
    " LEFT JOIN {processlog} p ON p.QueueName IN (:QueueNames) AND p.EntityID = c.CampaignID AND p.JobsUnprocessed > 0 " .
    " WHERE CampaignStatusEnum IN (:CampaignStatusEnum) AND p.LogID IS NULL",
    array(
      ':QueueNames' => ['send_queue', 'send_queue_to_one'],
      ':CampaignStatusEnum' => [
        Campaigns::STATUS_CREATE_QUEUE,
        Campaigns::STATUS_CREATE_SPLITTEST_QUEUE,
        Campaigns::STATUS_CREATE_WINNER_QUEUE,
      ]
    ))->fetchField();
  if ($count > 0) {
    $error[] = strtr("!count campaigns creating email without jobs in queue", ['!count' => $count]);
  }

  /*
   * TEST: Check if no autoresponders are in state "create winner" or "sent"
   */
  $count = db_query("SELECT COUNT(*) FROM {campaigns} " .
    " WHERE AutoResponderTriggerTypeEnum NOT IN (:AutoResponderTriggerTypeEnum) AND CampaignStatusEnum IN (:CampaignStatusEnum)",
    [
      ':AutoResponderTriggerTypeEnum' => [
        Campaigns::TRIGGER_TYPE_CAMPAIGN,
        Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN
      ],
      ':CampaignStatusEnum' => [
        Campaigns::STATUS_CREATE_WINNER_QUEUE,
        Campaigns::STATUS_SENT,
      ]
    ])->fetchField();

  if ($count > 0) {
    $error[] = strtr("!count newsletter in impossible states", ['!count' => $count]);
  }

  $check['_klicktipp_prod_check_queue_consistency'] = [
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No queue consistency errors reported.'),
    '#value_nok' => t('Transactional / Autoresponder / Newsletter queue consistency errors reported!'),
    '#description_ok' => t('Status is OK.'),
    '#description_nok' => strtr('@count transactional / autoresponder / newsletter queue consistency errors have been reported! <ul><li>@details</li></ul>', [
        '@count' => count($error),
        '@details' => implode('</li><li>', $error),
      ]
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  ];

  return prod_check_execute_check($check, $caller);
}

// spam rating log messages
function _klicktipp_prod_check_spamrating($caller = 'internal') {
  if (!module_exists('dblog')) {
    return;
  }

  $check = array();
  $title = 'spamrating log';
  $path = 'admin/reports/dblog';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $ratings = array();
  $error = array();

  // check all messages from the last 24 hours
  $result = db_query("SELECT * FROM {watchdog} WHERE type = :type AND timestamp > :timestamp", array(
    ':type' => 'spamrating',
    ':timestamp' => strtotime("-1 day")
  ));

  //get existing quick helps for spamassassin messages
  $SpamassassinQuickhelps = variable_get(KLICKTIPP_SPAMASSASSIN_QUICKHELPS, array());

  while ($pentry = kt_fetch_array($result)) {
    // check for type
    $vars = unserialize($pentry['variables']);
    foreach ($vars['!scores']['Scores'] as $Each) {

      if (!_klicktipp_prod_check_spamrating_has_quickhelp($Each['Description'], $SpamassassinQuickhelps)) {
        $ratings[$Each['Description'] . ' = Score ' . $Each['Point']]++;
      } //log spamassassin message
    }
  }
  arsort($ratings);
  foreach ($ratings as $key => $count) {
    $error[] = "$key ($count x)";
  }

  $check['_klicktipp_prod_check_spamrating'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No spam ratings reported.'),
    '#value_nok' => t('Spam ratings reported!'),
    '#description_ok' => t('Status is OK.'),
    '#description_nok' => strtr('The following ratings have been reported! Check watchdog <br><ul><li>@details</li></ul>', array(
        '@details' => implode('</li><li>', $error),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  $return = prod_check_execute_check($check, $caller);

  return $return;
}

// klicktipp blacklisted domains
function _klicktipp_prod_check_blacklistlog($caller = 'internal') {
  if (!module_exists('dblog')) {
    return;
  }

  $check = array();
  $title = 'Blacklist log';
  $path = 'admin/reports/dblog';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $error = array();

  // check all messages from the last 24 hours. We are interested in WATCHDOG_INFO only. Other types are used in case of unexpected behaviour
  // (e.g. timeouts, network errors, unexpected response from DNSBL lists) and are not relevant here
  $result = db_query("SELECT * FROM {watchdog} WHERE timestamp > :timestamp AND severity = :severity AND type = 'kt-blacklist' LIMIT 1000",
    [':timestamp' => strtotime("-1 day"), ':severity' => WATCHDOG_INFO]);
  $domains = [];
  while ($pentry = kt_fetch_array($result)) {
    // check for type
    $vars = unserialize($pentry['variables']);

    $domain = $vars['!domain'];

    // don't list same domain twice
    if (isset($domains[$domain])) {
      continue;
    }

    $detailLinks = [l('details', PRODCHECK_BASEURL . "wddetails/{$pentry['wid']}")];
    $vars['!wddate'] = date('Y-m-d H:i:s', $pentry['timestamp']);
    $vars['!wdlocation'] = $pentry['location'];

    $userID = 0;
    if (!empty($vars['additional_info']['UserID'])) {
      $userID = $vars['additional_info']['UserID'];
    }
    if ($userID) {
      if (!empty($vars['additional_info']['EmailID'])) {
        $emailID = $vars['additional_info']['EmailID'];
        $detailLinks[] = l(t('E-mail') . ' ' . $emailID, PRODCHECK_BASEURL . "email/{$userID}/redirect/{$emailID}/edit");
      }
      if (!empty($vars['additional_info']['ListID'])) {
        $listID = $vars['additional_info']['ListID'];
        $detailLinks[] = l(t('Subscription process') . ' ' . $listID, PRODCHECK_BASEURL . "lists/{$userID}/{$listID}/edit");
      }
      if (!empty($vars['additional_info']['ToolID'])) {
        $toolID = $vars['additional_info']['ToolID'];
        // splittest
        if (strpos($pentry['location'], '/api/split')) {
          $detailLinks[] = l(t('Splittest') . ' ' . $toolID, PRODCHECK_BASEURL . "marketingtools/{$userID}/splittest/{$toolID}/edit");
        } else {
          $detailLinks[] = l(t('Countdown') . ' ' . $toolID, PRODCHECK_BASEURL . "/tools/{$userID}/countdown/{$toolID}/edit");
        }
      }

    }

    $vars['!detaillinks'] = implode(' | ', $detailLinks);

    $error[] = t('!domain !wddate !wdlocation  !detaillinks', $vars);

    // remember domain
    $domains[$domain] = true;

    // limit number of listed domains (avoid out of memory)
    if (count($error) > 500) {
      $error[] = t('--- more than 500 errors ---');
      break;
    }
  }

  $check['_klicktipp_prod_check_blacklistlog'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No blacklisted domain reported.'),
    '#value_nok' => t('blacklisted domain reported!'),
    '#description_ok' => t('Status is OK.'),
    '#description_nok' => strtr('@count Blacklist errors have been reported! Check watchdog !link <br><ul><li>@details</li></ul>', array(
        '@count' => count($domains),
        '!link' => implode(prod_check_link_array('blacklistlog', $path)),
        '@details' => implode('</li><li>', $error),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  return prod_check_execute_check($check, $caller);
}

// klicktipp blacklisted redirects (e.g. hits)
function _klicktipp_prod_check_blacklist_redirects_log($caller = 'internal') {
  if (!module_exists('dblog')) {
    return;
  }

  $check = array();
  $title = 'Blacklist redirects log log';
  $path = 'admin/reports/dblog';
  if ($caller != 'internal') {
    $path = PRODCHECK_BASEURL . $path;
  }

  $error = array();

  // check all messages from the last 24 hours. We are interested in WATCHDOG_INFO only. Other types are used in case of unexpected behaviour
  // (e.g. timeouts, network errors, unexpected response from DNSBL lists) and are not relevant here
  $result = db_query(
    "SELECT * FROM {watchdog} WHERE timestamp > :timestamp AND severity = :severity AND type = :type LIMIT 1000",
    [':timestamp' => strtotime("-1 day"), ':severity' => WATCHDOG_INFO, ':type' => BlacklistHandler::WATCHDOG_TYPE_BLACKLIST_REDIRECT]);
  $urls = [];
  while ($pentry = kt_fetch_array($result)) {
    // check for type
    $vars = unserialize($pentry['variables']);

    $url = $vars['!url'];

    // don't list same domain twice
    if (isset($urls[$url])) {
      continue;
    }

    $detailLinks = [l('details', PRODCHECK_BASEURL . "wddetails/{$pentry['wid']}")];
    $vars['!wddate'] = date('Y-m-d H:i:s', $pentry['timestamp']);
    $vars['!wdlocation'] = $pentry['location'];

    $userID = 0;
    if (!empty($vars['additional_info']['UserID'])) {
      $userID = $vars['additional_info']['UserID'];
    }
    if ($userID) {
      if (!empty($vars['additional_info']['EmailID'])) {
        $emailID = $vars['additional_info']['EmailID'];
        // NOTE: MigrationLinks -> will be migrated with email module
        $detailLinks[] = l(t('E-mail') . ' ' . $emailID, PRODCHECK_BASEURL . "email/{$userID}/redirect/{$emailID}/edit");
      }
      if (!empty($vars['additional_info']['ListID'])) {
        $listID = $vars['additional_info']['ListID'];
        $detailLinks[] = l(t('Subscription process') . ' ' . $listID, PRODCHECK_BASEURL . "lists/{$userID}/{$listID}/edit");
      }
      if (!empty($vars['additional_info']['ToolID'])) {
        $toolID = $vars['additional_info']['ToolID'];
        // splittest
        if (strpos($pentry['location'], '/api/split')) {
          $detailLinks[] = l(t('Splittest') . ' ' . $toolID, PRODCHECK_BASEURL . "marketingtools/{$userID}/splittest/{$toolID}/edit");
        } else {
          $detailLinks[] = l(t('Countdown') . ' ' . $toolID, PRODCHECK_BASEURL . "/tools/{$userID}/countdown/{$toolID}/edit");
        }
      }

    }

    $vars['!detaillinks'] = implode(' | ', $detailLinks);

    $error[] = t(/*ignore*/$pentry['message'] . ' !wddate !wdlocation  !detaillinks', $vars);

    // remember domain
    $urls[$url] = true;

    // limit number of listed domains (avoid out of memory)
    if (count($error) > 500) {
      $error[] = t('--- more than 500 errors ---');
      break;
    }
  }

  $check['_klicktipp_prod_check_blacklist_redirects_log'] = array(
    '#title' => $title,
    '#state' => empty($error),
    '#severity' => ($caller == 'nagios') ? NAGIOS_STATUS_CRITICAL : PROD_CHECK_REQUIREMENT_ERROR,
    '#value_ok' => t('No shortener redirect problems reported.'),
    '#value_nok' => t('shortener redirect problems reported!'),
    '#description_ok' => t('Status is OK.'),
    '#description_nok' => strtr('@count Shortener redirect problems have been reported! Check watchdog !link <br><ul><li>@details</li></ul>', array(
        '@count' => count($urls),
        '!link' => implode(prod_check_link_array('shortener-redirects-log', $path)),
        '@details' => implode('</li><li>', $error),
      )
    ),
    '#nagios_key' => 'KT',
    '#nagios_type' => 'state',
  );

  return prod_check_execute_check($check, $caller);
}

/*
 * check if a spamassassin message has a quickhelp
 */
function _klicktipp_prod_check_spamrating_has_quickhelp($SpamDescription, $Quickhelps) {

  foreach ($Quickhelps as $message_match) {
    //use same enconding as the description @see: Emails::ParseRawSPAMRating
    if (strpos($SpamDescription, mb_convert_encoding($message_match, 'HTML-ENTITIES', "UTF-8, ISO-8859-1, ISO-8859-15")) !== FALSE) {
      return TRUE;
    } //spamassassin message already has a quick help, do not log spamassassin message
  }

  return FALSE;

}

//when using a cloned database from the production servers on the VMs, some settings and URLs must be changed
//only call this function on zauberlist.com @see: klicktipp_init()
//this function sets a variable to indicate that the database has been secured
function _klicktipp_secure_database_clone() {

  $isSecured = variable_get(KLICKTIPP_DATABASE_CLONE_SECURED, 0);

  if (empty($isSecured)) {

    //set the flag to indicate that the database has been secured
    variable_set(KLICKTIPP_DATABASE_CLONE_SECURED, 1);

    //helper function to change variables and output message with changes
    $dbclone_variable_set = function ($variable_key, $value) {

      $original = variable_get($variable_key, '[not set]');

      variable_set($variable_key, $value);
      $msg = t('Config variable "!key" changed<br />from "!orig"<br />to "!value"<br /><br />', array(
        '!key' => $variable_key,
        '!orig' => $original,
        '!value' => $value,
      ));

      drupal_set_message($msg);

    };

    //Klick-Tipp API URL
    $dbclone_variable_set('webinar_settings_api_url', APP_URL . "api");

    //Affiliate URLS (not dangerous)
    $dbclone_variable_set('amember_affiliate_url', APP_URL);

    //deactivate TwoFactorAuth @see: klicktipp_twofactorauth_is_available() in twofactorauth.inc
    $dbclone_variable_set('klicktipp_twofactorauth_settings_nexmo_key', '');
    $dbclone_variable_set('klicktipp_twofactorauth_settings_nexmo_secret', '');

    // --- replace klick-tipp.com domain in subscription forms and Facebook-Buttons ---
    // TODO: remove after subscription forms have been moved to {listbuildings}

    //callback for array_walk() to replace klick-tipp.com domain
    $dbclone_str_replace = function (&$array_value) {
      $array_value = str_replace(array(
        'www.klick-tipp.com/de/',
        'www.klick-tipp.com/'
      ), KLICKTIPP_DOMAIN . '/', $array_value);
    };

    //get all subscription forms/facebook buttons
    $result = kt_query("SELECT * FROM {listforms} WHERE 1");

    while ($form = kt_fetch_array($result)) {

      if (!empty($form['Data'])) {

        $data = unserialize($form['Data']);

        if (!empty($data)) {

          array_walk($data['HTMLCode'], $dbclone_str_replace);
          array_walk($data['FacebookCode'], $dbclone_str_replace);#

          $data['SourceCode'] = str_replace(array(
            'www.klick-tipp.com/de/',
            'www.klick-tipp.com/'
          ), KLICKTIPP_DOMAIN . '/', $data['SourceCode']);
          $data['FacebookSource'] = str_replace(array(
            'www.klick-tipp.com/de/',
            'www.klick-tipp.com/'
          ), KLICKTIPP_DOMAIN . '/', $data['FacebookSource']);

          $data['FBWallpostLink'] = ''; //not needed anymore
          $data['FBWallpostImg'] = ''; //not needed anymore

          $data = serialize($data);

          kt_query("UPDATE {listforms} SET Data = '%s' WHERE ListFormID = %d AND RelOwnerUserID = %d", $data, $form['ListFormID'], $form['RelOwnerUserID']);

        }

      }

    }

  }

}

/**
 * klicktipp monitor mail
 * see prod_check_status
 */
function klicktipp_monitor_cron() {

  // this is a job that can not use processlog, so do mem and time usage myself
  $starttime = time();
  $startmem = memory_get_peak_usage(FALSE);


  $url = url('admin/reports/prod-check', array('absolute' => TRUE));
  $output = "<h2><a href=\"$url\" target=\"_blank\">$url</a></h2>\n";

  $functions = _prod_check_functions();

  foreach ($functions as $set => $data) {
    $header = '<h3>' . $data['title'] . '</h3>' . "\n";
    $header .= '<p><em>' . $data['description'] . '</em></p>' . "\n";
    foreach ($data['functions'] as $function => $title) {
      // get status
      $check = call_user_func($function);
      if (is_array($check) && !empty($check)) {
        foreach ($check as $f => $v) {
          if ($f == 'prod_check_page_compression') {
            // we use varnish, so we dont use page compression
            continue;
          };
          if ($f == 'prod_check_block_cache') {
            // we dont have cacheable blocks
            continue;
          };
          if ($v['severity'] > 0) {
            // output header only once and if there is something to report only
            $output .= $header;
            $header = '';
            // add report
            $output .= "<h4>{$v['title']} ($f)</h4>\n";
            $output .= '<p>' . $v['description'] . '</p>' . "\n";
          }
        }
      }
    }
  }

  // output time and memory consumption
  $timeconsumed = time() - $starttime;
  $mem = memory_get_peak_usage(FALSE);
  $mem_mb = round($mem/1048576,1);
  $memdiff = $mem - $startmem;
  $diff_mb = round($memdiff/1048576,1);
  $output .= "<h4>prod_check mem and time usage</h4>\n";
  $output .= "<p>runtime: $timeconsumed s</p>\n";
  $output .= "<p>mem: $mem_mb MB (+ $diff_mb MB)</p>\n";

  // send emails to admins
  Core::SendNotificationEmail('klicktipp_notify_monitor', $output, 0, 'Klicktipp Monitor report');
}

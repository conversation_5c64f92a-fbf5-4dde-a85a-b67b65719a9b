<?php

/* HOW TO CHANGE THE KLICK-TIPP MENU

1. change KT_MENU_MAIN to a new menu machine name
2. adjust the menu in kt_install_menu_create_menu below
3. write a hook_update, that executes kt_install_menu_create_menu
4. translate the new menu
5. switch to new menu in klicktipp settings

*/

function kt_install_menu_create_menu() {
  ///////////////////////////////////////////////////
  // menu
  $no_plid = 0;
  $has_children = 1;
  $has_no_children = 0;
  $hidden = 0;
  $expanded = 1;
  $not_expanded = 1;
  $customized = 1; // if not set _menu_navigation_links_rebuild will remove these items later
  $weight = 0;

  // menu authenticated
  menu_save(array(
    'menu_name' => KT_MENU_MAIN,
    'title' => 'Menu restructure with Sublevels', // if you change KT_MENU_MAIN, give it a name we can identify in settings
    'description' => 'Klicktipp Menu',
  ));

  // first level, ContactCloud
  $plid = kt_install_menu_create_menu_item('user/me', 'Kontakte', '', KT_MENU_MAIN, $no_plid, $weight++,
    'menu', $hidden, $has_children, $expanded, $customized);
  // second level, Contacts
  $plidSub = kt_install_menu_create_menu_item('subscribers/me', 'Kontakte verwalten', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('contacts/me/add', 'Kontakt hinzufügen', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/import', 'Import (CSV)', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('contacts/me/facebook-audience', 'Facebook Audience Sync', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('subscribers/me/duplicates', 'Doppelte Kontakte', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Stammsatz-Felder
  $plidSub = kt_install_menu_create_menu_item('application/me/customfield/overview/me', 'Contact Record', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/customfield/settings/me/create', 'Create Custom Fields', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  $plidSub = kt_install_menu_create_menu_item('contacts/me/feedback', '-Feedback', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('contacts/me/feedback', 'Unsubscription Feedback', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('contacts/me/sms', 'SMS Feedback', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
// second level, Blacklist
  kt_install_menu_create_menu_item('contacts/me/blacklist', '-Blacklist', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);

  // first level, Listbuilding
  $plid = kt_install_menu_create_menu_item('listbuilding/me', 'Listbuilding', '', KT_MENU_MAIN, $no_plid, $weight++,
    'menu', $hidden, $has_children, $expanded, $customized);
  // second level, Listbuilding
  $plidSub = kt_install_menu_create_menu_item('listbuilding/me', 'Listbuilding', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('listbuilding/me/create', 'Create Listbuilding', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Marketing Tools
  $plidSub = kt_install_menu_create_menu_item('marketingtools/me', 'Marketing Tools', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('marketingtools/me/create', 'Create Marketing Tool', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Double-Opt-In-Prozesse
  kt_install_menu_create_menu_item('application/me/landing-page/settings/me', 'Landingpages', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  $plidSub = kt_install_menu_create_menu_item('application/me/double-opt-in/overview/me', 'Double-Opt-In Processes', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/double-opt-in/settings/me/create', 'Create Double-Opt-In Process', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Austragungsprozesse
  $plidSub = kt_install_menu_create_menu_item('unsubscriptions/me', 'Unsubscription Processes', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('unsubscriptions/me', 'Unsubscription messages', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('unsubscriptions/me/url', 'Unsubscription URLs', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);

  // first level, Automatisierung
  $plid = kt_install_menu_create_menu_item('automations/me', 'Automation', '', KT_MENU_MAIN, $no_plid, $weight++,
    'menu', $hidden, $has_children, $expanded, $customized);
  // second level, Signaturen / Absenderprofile
  $plidSub = kt_install_menu_create_menu_item('application/me/signature/overview/me', 'Signatures / Sender Profile', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/signature/settings/me/create', 'Create Signature', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Tags
  $plidSub = kt_install_menu_create_menu_item('application/me/tag/overview/me', 'Tags', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/tag/settings/me/create', 'Create Tag', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Tagging-Pixel
  $plidSub = kt_install_menu_create_menu_item('tools/me/taggingpixels', 'Tagging Pixels', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('app/tool/tagging-pixel/settings/me/create', 'Create Tagging Pixel', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, SmartLinks
  $plidSub = kt_install_menu_create_menu_item('application/me/smartlink/overview/me', 'SmartLinks', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/smartlink/settings/me/create', 'Create SmartLink', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Outbounds
  $plidSub = kt_install_menu_create_menu_item('tools/me/outbound', '-Outbounds', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('tools/me/outbound/add', 'Create Outbound', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('tools/me/kajabi/add', 'Create Kajabi Activation', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('tools/me/zapier/add', 'Create Zapier Trigger', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Kalender
  kt_install_menu_create_menu_item('cockpit/me/calendar', '-Calendar', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Countdowns
  $plidSub = kt_install_menu_create_menu_item('tools/me/countdowns', 'Countdowns', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('tools/me/countdown/add', 'Create Countdown', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);

  // first level, Kampagnen
  $plid = kt_install_menu_create_menu_item('application/me/campaign/automation/me', 'Campaigns', '', KT_MENU_MAIN, $no_plid, $weight++,
    'menu', $hidden, $has_children, $expanded, $customized);
  // second level, Kampagnen
  $plidSub = kt_install_menu_create_menu_item('application/me/campaign/automation/me', 'Campaigns', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/campaign/automation/me/create-automation', 'Create Campaign', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Kampagnen-Vorlagen
  $plidSub = kt_install_menu_create_menu_item('tools/me/template', 'Campaign Templates', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('tools/me/template/add', 'Create Campaign Template', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, BAM-Kampagnen-Vorlagen
  kt_install_menu_create_menu_item('automations/me/bam', 'Overview BAM Automation Templates', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Follow-up-Kampagnen
  $plidSub = kt_install_menu_create_menu_item('application/me/campaign/autoresponder/me', '-Follow-Up Campaigns', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/campaign/autoresponder-email/me/create', 'Create Follow-Up E-Mail', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/campaign/autoresponder-sms/me/create', 'Create Follow-Up SMS', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/campaign/autoresponder-email-birthday/me/create', 'Create Birthday Follow-Up E-Mail', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/campaign/autoresponder-sms-birthday/1/create', 'Create Birthday Follow-Up SMS', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Newsletter
  $plidSub = kt_install_menu_create_menu_item('application/me/campaign/newsletter/me', '-Newsletter', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/campaign/newsletter-email/me/create', 'Create E-Mail Newsletter', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/campaign/newsletter-sms/me/create', 'Create SMS Newsletter', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, E-Mails / SMS
  $plidSub = kt_install_menu_create_menu_item('application/me/email/overview/me', '-E-Mails / SMS', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/email/automation-email/me/create', 'Create Campaign E-Mail', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/email/automation-sms/me/create', 'Create Campaign SMS', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/email/notification-email/me/create', '-Create Notification E-Mail', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('application/me/email/notification-sms/me/create', 'Create Notification SMS', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  //second level, E-Mail Templates (no subslevel)
  kt_install_menu_create_menu_item('application/me/email/editor/me/templates', '-E-Mail Templates', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);

  // first level, Tools
  $plid = kt_install_menu_create_menu_item('automations/me', 'Tools', '', KT_MENU_MAIN, $no_plid, $weight++,
    'menu', $hidden, $has_children, $expanded, $customized);
  // second level, Labels
  kt_install_menu_create_menu_item('cockpit/me/metalabel', 'Labels', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Meta-Suche
  kt_install_menu_create_menu_item('cockpit/me/metasearch', 'Meta Search', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Analytics-Reports
  $plidSub = kt_install_menu_create_menu_item('tools/me/statistics', '-Analytics Reports', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('tools/me/statistics/add', 'Create Statistic', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Youtube-Content-Analyse
  kt_install_menu_create_menu_item('application/me/youtube-analyzer/me', 'Youtube Content Analysis', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Privacy Dashboard
  kt_install_menu_create_menu_item('user/me/privacy', '-Privacy Dashboard', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);

  // first level, Partnerprogramm
  $plid = kt_install_menu_create_menu_item('partnerprogramm', 'Affiliates', '', KT_MENU_MAIN, $no_plid, $weight++,
    'menu', $hidden, $has_children, $expanded, $customized);
  // second level, Werbemittel
  kt_install_menu_create_menu_item('partnerprogramm', 'Advertising Material', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, KlickTipp Convention
  kt_install_menu_create_menu_item('klick-tipp-convention-startseite', 'KlickTipp Convention', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Consultant-Mitgliederbereich
  kt_install_menu_create_menu_item('certified-consultants/mitgliederbereich', 'Consultants', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Marktplatz
  kt_install_menu_create_menu_item('consultants', 'Marketplace', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);

  // first level, Mein Konto
  $plid = kt_install_menu_create_menu_item('user/me/edit', 'My Account', '', KT_MENU_MAIN, $no_plid, $weight++,
    'menu', $hidden, $has_children, $expanded, $customized);
  // second level, Persönliche Daten / AVV
  kt_install_menu_create_menu_item('user/me/personal-information', 'Personal Information / AVV', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  // second level, Einstellungen
  $plidSub = kt_install_menu_create_menu_item('user/me/edit', 'Settings', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('user/me/edit', 'User Settings', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('user/me/sender', 'E-mail delivery', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('login/me/change-password', 'Change Password', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('user/klicktipp-twofactorauth', 'Two-Factor-Authorization', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('user/me/domains', 'Domains', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('user/me/marketingcockpit', 'Campaign Editor', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('user/me/consultant', 'Marketplace Profile', '', KT_MENU_MAIN, $plidSub, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('user/me/digistore-invoice', 'My Digistore Invoices', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('user/me/subaccount/switch', 'Subaccounts', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('bestellen', '-Upgrade', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('email-marketing-formel', 'BAM! Upgrade', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
// this link will be changed by the help beacon javascript code to open the beacon
  kt_install_menu_create_menu_item('http://###beacon', '-Support', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);
  kt_install_menu_create_menu_item('user/logout', 'Logout', '', KT_MENU_MAIN, $plid, $weight++,
    'menu', $hidden, $has_no_children, $not_expanded, $customized);

  // finish menu cache
  menu_cache_clear_all();
}


function kt_install_menu_create_menu_item($link_path, $title, $desc, $menu_name, $plid = NULL, $weight = 0, $module = 'menu', $hidden = 0, $has_children = 0, $expanded = 0, $customized = 0) {
  $menu_item = array(
    'link_path' => $link_path,
    'link_title' => $title,
    'menu_name' => $menu_name,
    'weight' => $weight,
    'module' => $module,
    'has_children' => $has_children,
    'expanded' => $expanded,
    'customized' => $customized
  );

  if ($hidden) {
    $menu_item['hidden'] = $hidden;
  }

  if (isset($plid)) {
    $menu_item['plid'] = $plid;
  }

  $menu_item['options'] = array('attributes' => array('title' => ''));

  return menu_link_save($menu_item);
}

<?php

//TODO try to replace country codes table with ISO 3166-1 alpha2
//TODO try to replace states codes with ISO 3166-2 (see https://en.wikipedia.org/wiki/ISO_3166-2)
//Note: there is only timezone_identifiers_list() in php (<= 7) to get country names from 3166-1,
// so there is no real shortcut to a list like those below.
//TODO the codes below are a little bit outdated, so update it or use a web souzrce for that

/**
 * get countries for account profile
 */
function klicktipp_account_get_countries_inner() {
  static $countries;

  if (isset($countries)) {
    return $countries;
  }

  $countries = array();
  $c = amember_rpc_get_countries();
  foreach ($c as $country => $item) {
    $countries[$country] = t(/*ignore*/$item['title']);
  }

  return $countries;
}

/**
 * get states for account profile
 */
function _klicktipp_account_get_states_inner($forcountry = '') {

  $states = array('' => t('Select state'));
  $s = amember_rpc_get_states($forcountry);
  foreach ($s as $state => $item) {
    if (empty($forcountry) /*all*/ || $forcountry == $item['country']) {
      $states[$state] = t(/*ignore*/$item['title']);
    }
  }
  return $states;
}

/**
 * get state code for a state string (digistore)
 */
function klicktipp_get_state_code($state) {
  if (!empty($state)) {
    $s = amember_rpc_get_states();
    foreach ($s as $code => $item) {
      if (t(/*ignore*/$item['title']) == $state) {
        return $code;
      }
    }
  }

  return '';
}

/**
 * replacement for table `splittes_amember`.`amember_countries`
 */
function amember_rpc_get_countries() {

  $amember_countries = array(
    'US' => array('title' => /*t(*/'USA'/*)*/, 'weight' => '10'),
    'CA' => array('title' => /*t(*/'Kanada'/*)*/, 'weight' => '5'),
    'AF' => array('title' => /*t(*/'Afghanistan'/*)*/, 'weight' => '0'),
    'AL' => array('title' => /*t(*/'Albania'/*)*/, 'weight' => '0'),
    'DZ' => array('title' => /*t(*/'Algeria'/*)*/, 'weight' => '0'),
    'AS' => array('title' => /*t(*/'American Samoa'/*)*/, 'weight' => '0'),
    'AD' => array('title' => /*t(*/'Andorra'/*)*/, 'weight' => '0'),
    'AO' => array('title' => /*t(*/'Angola'/*)*/, 'weight' => '0'),
    'AI' => array('title' => /*t(*/'Anguilla'/*)*/, 'weight' => '0'),
    'AQ' => array('title' => /*t(*/'Antarctica'/*)*/, 'weight' => '0'),
    'AG' => array('title' => /*t(*/'Antigua and Barbuda'/*)*/, 'weight' => '0'),
    'AR' => array('title' => /*t(*/'Argentina'/*)*/, 'weight' => '0'),
    'AM' => array('title' => /*t(*/'Armenia'/*)*/, 'weight' => '0'),
    'AW' => array('title' => /*t(*/'Aruba'/*)*/, 'weight' => '0'),
    'AU' => array('title' => /*t(*/'Australia'/*)*/, 'weight' => '0'),
    'AT' => array('title' => /*t(*/'Österreich'/*)*/, 'weight' => '14'),
    'AZ' => array('title' => /*t(*/'Azerbaijan'/*)*/, 'weight' => '0'),
    'BS' => array('title' => /*t(*/'Bahamas'/*)*/, 'weight' => '0'),
    'BH' => array('title' => /*t(*/'Bahrain'/*)*/, 'weight' => '0'),
    'BD' => array('title' => /*t(*/'Bangladesh'/*)*/, 'weight' => '0'),
    'BB' => array('title' => /*t(*/'Barbados'/*)*/, 'weight' => '0'),
    'BY' => array('title' => /*t(*/'Belarus'/*)*/, 'weight' => '0'),
    'BE' => array('title' => /*t(*/'Belgium'/*)*/, 'weight' => '0'),
    'BZ' => array('title' => /*t(*/'Belize'/*)*/, 'weight' => '0'),
    'BJ' => array('title' => /*t(*/'Benin'/*)*/, 'weight' => '0'),
    'BM' => array('title' => /*t(*/'Bermuda'/*)*/, 'weight' => '0'),
    'BT' => array('title' => /*t(*/'Bhutan'/*)*/, 'weight' => '0'),
    'BO' => array('title' => /*t(*/'Bolivia'/*)*/, 'weight' => '0'),
    'BA' => array('title' => /*t(*/'Bosnia and Herzegovina'/*)*/, 'weight' => '0'),
    'BW' => array('title' => /*t(*/'Botswana'/*)*/, 'weight' => '0'),
    'BV' => array('title' => /*t(*/'Bouvet Island'/*)*/, 'weight' => '0'),
    'BR' => array('title' => /*t(*/'Brazil'/*)*/, 'weight' => '0'),
    'IO' => array('title' => /*t(*/'British Indian Ocean Territory'/*)*/, 'weight' => '0'),
    'BN' => array('title' => /*t(*/'Brunei'/*)*/, 'weight' => '0'),
    'BG' => array('title' => /*t(*/'Bulgaria'/*)*/, 'weight' => '0'),
    'BF' => array('title' => /*t(*/'Burkina Faso'/*)*/, 'weight' => '0'),
    'BI' => array('title' => /*t(*/'Burundi'/*)*/, 'weight' => '0'),
    'KH' => array('title' => /*t(*/'Cambodia'/*)*/, 'weight' => '0'),
    'CM' => array('title' => /*t(*/'Cameroon'/*)*/, 'weight' => '0'),
    'CV' => array('title' => /*t(*/'Cape Verde'/*)*/, 'weight' => '0'),
    'KY' => array('title' => /*t(*/'Cayman Islands'/*)*/, 'weight' => '0'),
    'CF' => array('title' => /*t(*/'Central African Republic'/*)*/, 'weight' => '0'),
    'TD' => array('title' => /*t(*/'Chad'/*)*/, 'weight' => '0'),
    'CL' => array('title' => /*t(*/'Chile'/*)*/, 'weight' => '0'),
    'CN' => array('title' => /*t(*/'China'/*)*/, 'weight' => '0'),
    'CX' => array('title' => /*t(*/'Christmas Island'/*)*/, 'weight' => '0'),
    'CC' => array('title' => /*t(*/'Cocos (Keeling) Islands'/*)*/, 'weight' => '0'),
    'CO' => array('title' => /*t(*/'Colombia'/*)*/, 'weight' => '0'),
    'KM' => array('title' => /*t(*/'Comoros'/*)*/, 'weight' => '0'),
    'CG' => array('title' => /*t(*/'Congo'/*)*/, 'weight' => '0'),
    'CK' => array('title' => /*t(*/'Cook Islands'/*)*/, 'weight' => '0'),
    'CR' => array('title' => /*t(*/'Costa Rica'/*)*/, 'weight' => '0'),
    'CI' => array('title' => /*t(*/"Cote d'Ivoire"/*)*/, 'weight' => '0'),
    'HR' => array('title' => /*t(*/'Croatia (Hrvatska)'/*)*/, 'weight' => '0'),
    'CU' => array('title' => /*t(*/'Cuba'/*)*/, 'weight' => '0'),
    'CY' => array('title' => /*t(*/'Cyprus'/*)*/, 'weight' => '0'),
    'CZ' => array('title' => /*t(*/'Czech Republic'/*)*/, 'weight' => '0'),
    'CD' => array('title' => /*t(*/'Congo (DRC)'/*)*/, 'weight' => '0'),
    'DK' => array('title' => /*t(*/'Denmark'/*)*/, 'weight' => '0'),
    'DJ' => array('title' => /*t(*/'Djibouti'/*)*/, 'weight' => '0'),
    'DM' => array('title' => /*t(*/'Dominica'/*)*/, 'weight' => '0'),
    'DO' => array('title' => /*t(*/'Dominican Republic'/*)*/, 'weight' => '0'),
    'TP' => array('title' => /*t(*/'East Timor'/*)*/, 'weight' => '0'),
    'EC' => array('title' => /*t(*/'Ecuador'/*)*/, 'weight' => '0'),
    'EG' => array('title' => /*t(*/'Egypt'/*)*/, 'weight' => '0'),
    'SV' => array('title' => /*t(*/'El Salvador'/*)*/, 'weight' => '0'),
    'GQ' => array('title' => /*t(*/'Equatorial Guinea'/*)*/, 'weight' => '0'),
    'ER' => array('title' => /*t(*/'Eritrea'/*)*/, 'weight' => '0'),
    'EE' => array('title' => /*t(*/'Estonia'/*)*/, 'weight' => '0'),
    'ET' => array('title' => /*t(*/'Ethiopia'/*)*/, 'weight' => '0'),
    'FK' => array(
      'title' => /*t(*/'Falkland Islands (Islas Malvinas)',
      'weight' => '0'
    ),
    'FO' => array('title' => /*t(*/'Faroe Islands'/*)*/, 'weight' => '0'),
    'FJ' => array('title' => /*t(*/'Fiji Islands'/*)*/, 'weight' => '0'),
    'FI' => array('title' => /*t(*/'Finland'/*)*/, 'weight' => '0'),
    'FR' => array('title' => /*t(*/'France'/*)*/, 'weight' => '0'),
    'GF' => array('title' => /*t(*/'French Guiana'/*)*/, 'weight' => '0'),
    'PF' => array('title' => /*t(*/'French Polynesia'/*)*/, 'weight' => '0'),
    'TF' => array(
      'title' => /*t(*/'French Southern and Antarctic Lands'/*)*/,
      'weight' => '0'
    ),
    'GA' => array('title' => /*t(*/'Gabon'/*)*/, 'weight' => '0'),
    'GM' => array('title' => /*t(*/'Gambia'/*)*/, 'weight' => '0'),
    'GE' => array('title' => /*t(*/'Georgia'/*)*/, 'weight' => '0'),
    'DE' => array('title' => /*t(*/'Deutschland'/*)*/, 'weight' => '15'),
    'GH' => array('title' => /*t(*/'Ghana'/*)*/, 'weight' => '0'),
    'GI' => array('title' => /*t(*/'Gibraltar'/*)*/, 'weight' => '0'),
    'GR' => array('title' => /*t(*/'Greece'/*)*/, 'weight' => '0'),
    'GL' => array('title' => /*t(*/'Greenland'/*)*/, 'weight' => '0'),
    'GD' => array('title' => /*t(*/'Grenada'/*)*/, 'weight' => '0'),
    'GP' => array('title' => /*t(*/'Guadeloupe'/*)*/, 'weight' => '0'),
    'GU' => array('title' => /*t(*/'Guam'/*)*/, 'weight' => '0'),
    'GT' => array('title' => /*t(*/'Guatemala'/*)*/, 'weight' => '0'),
    'GN' => array('title' => /*t(*/'Guinea'/*)*/, 'weight' => '0'),
    'GW' => array('title' => /*t(*/'Guinea-Bissau'/*)*/, 'weight' => '0'),
    'GY' => array('title' => /*t(*/'Guyana'/*)*/, 'weight' => '0'),
    'HT' => array('title' => /*t(*/'Haiti'/*)*/, 'weight' => '0'),
    'HM' => array(
      'title' => /*t(*/'Heard Island and McDonald Islands'/*)*/,
      'weight' => '0'
    ),
    'HN' => array('title' => /*t(*/'Honduras'/*)*/, 'weight' => '0'),
    'HK' => array('title' => /*t(*/'Hong Kong SAR'/*)*/, 'weight' => '0'),
    'HU' => array('title' => /*t(*/'Hungary'/*)*/, 'weight' => '0'),
    'IS' => array('title' => /*t(*/'Iceland'/*)*/, 'weight' => '0'),
    'IN' => array('title' => /*t(*/'India'/*)*/, 'weight' => '0'),
    'ID' => array('title' => /*t(*/'Indonesia'/*)*/, 'weight' => '0'),
    'IR' => array('title' => /*t(*/'Iran'/*)*/, 'weight' => '0'),
    'IQ' => array('title' => /*t(*/'Iraq'/*)*/, 'weight' => '0'),
    'IE' => array('title' => /*t(*/'Ireland'/*)*/, 'weight' => '0'),
    'IL' => array('title' => /*t(*/'Israel'/*)*/, 'weight' => '0'),
    'IT' => array('title' => /*t(*/'Italy'/*)*/, 'weight' => '0'),
    'JM' => array('title' => /*t(*/'Jamaica'/*)*/, 'weight' => '0'),
    'JP' => array('title' => /*t(*/'Japan'/*)*/, 'weight' => '0'),
    'JO' => array('title' => /*t(*/'Jordan'/*)*/, 'weight' => '0'),
    'KZ' => array('title' => /*t(*/'Kazakhstan'/*)*/, 'weight' => '0'),
    'KE' => array('title' => /*t(*/'Kenya'/*)*/, 'weight' => '0'),
    'KI' => array('title' => /*t(*/'Kiribati'/*)*/, 'weight' => '0'),
    'KR' => array('title' => /*t(*/'Korea'/*)*/, 'weight' => '0'),
    'KW' => array('title' => /*t(*/'Kuwait'/*)*/, 'weight' => '0'),
    'KG' => array('title' => /*t(*/'Kyrgyzstan'/*)*/, 'weight' => '0'),
    'LA' => array('title' => /*t(*/'Laos'/*)*/, 'weight' => '0'),
    'LV' => array('title' => /*t(*/'Latvia'/*)*/, 'weight' => '0'),
    'LB' => array('title' => /*t(*/'Lebanon'/*)*/, 'weight' => '0'),
    'LS' => array('title' => /*t(*/'Lesotho'/*)*/, 'weight' => '0'),
    'LR' => array('title' => /*t(*/'Liberia'/*)*/, 'weight' => '0'),
    'LY' => array('title' => /*t(*/'Libya'/*)*/, 'weight' => '0'),
    'LI' => array('title' => /*t(*/'Liechtenstein'/*)*/, 'weight' => '0'),
    'LT' => array('title' => /*t(*/'Lithuania'/*)*/, 'weight' => '0'),
    'LU' => array('title' => /*t(*/'Luxembourg'/*)*/, 'weight' => '0'),
    'MO' => array('title' => /*t(*/'Macao SAR'/*)*/, 'weight' => '0'),
    'MK' => array('title' => /*t(*/'Macedonia'/*)*/, 'weight' => '0'),
    'MG' => array('title' => /*t(*/'Madagascar'/*)*/, 'weight' => '0'),
    'MW' => array('title' => /*t(*/'Malawi'/*)*/, 'weight' => '0'),
    'MY' => array('title' => /*t(*/'Malaysia'/*)*/, 'weight' => '0'),
    'MV' => array('title' => /*t(*/'Maldives'/*)*/, 'weight' => '0'),
    'ML' => array('title' => /*t(*/'Mali'/*)*/, 'weight' => '0'),
    'MT' => array('title' => /*t(*/'Malta'/*)*/, 'weight' => '0'),
    'MH' => array('title' => /*t(*/'Marshall Islands'/*)*/, 'weight' => '0'),
    'MQ' => array('title' => /*t(*/'Martinique'/*)*/, 'weight' => '0'),
    'MR' => array('title' => /*t(*/'Mauritania'/*)*/, 'weight' => '0'),
    'MU' => array('title' => /*t(*/'Mauritius'/*)*/, 'weight' => '0'),
    'YT' => array('title' => /*t(*/'Mayotte'/*)*/, 'weight' => '0'),
    'MX' => array('title' => /*t(*/'Mexico'/*)*/, 'weight' => '0'),
    'FM' => array('title' => /*t(*/'Micronesia'/*)*/, 'weight' => '0'),
    'MD' => array('title' => /*t(*/'Moldova'/*)*/, 'weight' => '0'),
    'MC' => array('title' => /*t(*/'Monaco'/*)*/, 'weight' => '0'),
    'MN' => array('title' => /*t(*/'Mongolia'/*)*/, 'weight' => '0'),
    'MS' => array('title' => /*t(*/'Montserrat'/*)*/, 'weight' => '0'),
    'MA' => array('title' => /*t(*/'Morocco'/*)*/, 'weight' => '0'),
    'MZ' => array('title' => /*t(*/'Mozambique'/*)*/, 'weight' => '0'),
    'MM' => array('title' => /*t(*/'Myanmar'/*)*/, 'weight' => '0'),
    'NA' => array('title' => /*t(*/'Namibia'/*)*/, 'weight' => '0'),
    'NR' => array('title' => /*t(*/'Nauru'/*)*/, 'weight' => '0'),
    'NP' => array('title' => /*t(*/'Nepal'/*)*/, 'weight' => '0'),
    'NL' => array('title' => /*t(*/'Netherlands'/*)*/, 'weight' => '0'),
    'AN' => array('title' => /*t(*/'Netherlands Antilles'/*)*/, 'weight' => '0'),
    'NC' => array('title' => /*t(*/'New Caledonia'/*)*/, 'weight' => '0'),
    'NZ' => array('title' => /*t(*/'New Zealand'/*)*/, 'weight' => '0'),
    'NI' => array('title' => /*t(*/'Nicaragua'/*)*/, 'weight' => '0'),
    'NE' => array('title' => /*t(*/'Niger'/*)*/, 'weight' => '0'),
    'NG' => array('title' => /*t(*/'Nigeria'/*)*/, 'weight' => '0'),
    'NU' => array('title' => /*t(*/'Niue'/*)*/, 'weight' => '0'),
    'NF' => array('title' => /*t(*/'Norfolk Island'/*)*/, 'weight' => '0'),
    'KP' => array('title' => /*t(*/'North Korea'/*)*/, 'weight' => '0'),
    'MP' => array('title' => /*t(*/'Northern Mariana Islands'/*)*/, 'weight' => '0'),
    'NO' => array('title' => /*t(*/'Norway'/*)*/, 'weight' => '0'),
    'OM' => array('title' => /*t(*/'Oman'/*)*/, 'weight' => '0'),
    'PK' => array('title' => /*t(*/'Pakistan'/*)*/, 'weight' => '0'),
    'PW' => array('title' => /*t(*/'Palau'/*)*/, 'weight' => '0'),
    'PA' => array('title' => /*t(*/'Panama'/*)*/, 'weight' => '0'),
    'PG' => array('title' => /*t(*/'Papua New Guinea'/*)*/, 'weight' => '0'),
    'PY' => array('title' => /*t(*/'Paraguay'/*)*/, 'weight' => '0'),
    'PE' => array('title' => /*t(*/'Peru'/*)*/, 'weight' => '0'),
    'PH' => array('title' => /*t(*/'Philippines'/*)*/, 'weight' => '0'),
    'PN' => array('title' => /*t(*/'Pitcairn Islands'/*)*/, 'weight' => '0'),
    'PL' => array('title' => /*t(*/'Poland'/*)*/, 'weight' => '0'),
    'PT' => array('title' => /*t(*/'Portugal'/*)*/, 'weight' => '0'),
    'PR' => array('title' => /*t(*/'Puerto Rico'/*)*/, 'weight' => '0'),
    'QA' => array('title' => /*t(*/'Qatar'/*)*/, 'weight' => '0'),
    'RE' => array('title' => /*t(*/'Reunion'/*)*/, 'weight' => '0'),
    'RO' => array('title' => /*t(*/'Romania'/*)*/, 'weight' => '0'),
    'RU' => array('title' => /*t(*/'Russia'/*)*/, 'weight' => '0'),
    'RW' => array('title' => /*t(*/'Rwanda'/*)*/, 'weight' => '0'),
    'WS' => array('title' => /*t(*/'Samoa'/*)*/, 'weight' => '0'),
    'SM' => array('title' => /*t(*/'San Marino'/*)*/, 'weight' => '0'),
    'ST' => array('title' => /*t(*/'Sao Tome and Principe'/*)*/, 'weight' => '0'),
    'SA' => array('title' => /*t(*/'Saudi Arabia'/*)*/, 'weight' => '0'),
    'SN' => array('title' => /*t(*/'Senegal'/*)*/, 'weight' => '0'),
    'YU' => array('title' => /*t(*/'Serbia and Montenegro'/*)*/, 'weight' => '0'),
    'SC' => array('title' => /*t(*/'Seychelles'/*)*/, 'weight' => '0'),
    'SL' => array('title' => /*t(*/'Sierra Leone'/*)*/, 'weight' => '0'),
    'SG' => array('title' => /*t(*/'Singapore'/*)*/, 'weight' => '0'),
    'SK' => array('title' => /*t(*/'Slovakia'/*)*/, 'weight' => '0'),
    'SI' => array('title' => /*t(*/'Slovenia'/*)*/, 'weight' => '0'),
    'SB' => array('title' => /*t(*/'Solomon Islands'/*)*/, 'weight' => '0'),
    'SO' => array('title' => /*t(*/'Somalia'/*)*/, 'weight' => '0'),
    'ZA' => array('title' => /*t(*/'South Africa'/*)*/, 'weight' => '0'),
    'GS' => array(
      'title' => /*t(*/'South Georgia and the South Sandwich Islands'/*)*/,
      'weight' => '0'
    ),
    'ES' => array('title' => /*t(*/'Spain'/*)*/, 'weight' => '0'),
    'LK' => array('title' => /*t(*/'Sri Lanka'/*)*/, 'weight' => '0'),
    'SH' => array('title' => /*t(*/'St. Helena'/*)*/, 'weight' => '0'),
    'KN' => array('title' => /*t(*/'St. Kitts and Nevis'/*)*/, 'weight' => '0'),
    'LC' => array('title' => /*t(*/'St. Lucia'/*)*/, 'weight' => '0'),
    'PM' => array('title' => /*t(*/'St. Pierre and Miquelon'/*)*/, 'weight' => '0'),
    'VC' => array('title' => /*t(*/'St. Vincent and the Grenadines'/*)*/, 'weight' => '0'),
    'SD' => array('title' => /*t(*/'Sudan'/*)*/, 'weight' => '0'),
    'SR' => array('title' => /*t(*/'Suriname'/*)*/, 'weight' => '0'),
    'SJ' => array('title' => /*t(*/'Svalbard and Jan Mayen'/*)*/, 'weight' => '0'),
    'SZ' => array('title' => /*t(*/'Swaziland'/*)*/, 'weight' => '0'),
    'SE' => array('title' => /*t(*/'Sweden'/*)*/, 'weight' => '0'),
    'CH' => array('title' => /*t(*/'Schweiz'/*)*/, 'weight' => '13'),
    'SY' => array('title' => /*t(*/'Syria'/*)*/, 'weight' => '0'),
    'TW' => array('title' => /*t(*/'Taiwan'/*)*/, 'weight' => '0'),
    'TJ' => array('title' => /*t(*/'Tajikistan'/*)*/, 'weight' => '0'),
    'TZ' => array('title' => /*t(*/'Tanzania'/*)*/, 'weight' => '0'),
    'TH' => array('title' => /*t(*/'Thailand'/*)*/, 'weight' => '0'),
    'TG' => array('title' => /*t(*/'Togo'/*)*/, 'weight' => '0'),
    'TK' => array('title' => /*t(*/'Tokelau'/*)*/, 'weight' => '0'),
    'TO' => array('title' => /*t(*/'Tonga'/*)*/, 'weight' => '0'),
    'TT' => array('title' => /*t(*/'Trinidad and Tobago'/*)*/, 'weight' => '0'),
    'TN' => array('title' => /*t(*/'Tunisia'/*)*/, 'weight' => '0'),
    'TR' => array('title' => /*t(*/'Turkey'/*)*/, 'weight' => '0'),
    'TM' => array('title' => /*t(*/'Turkmenistan'/*)*/, 'weight' => '0'),
    'TC' => array('title' => /*t(*/'Turks and Caicos Islands'/*)*/, 'weight' => '0'),
    'TV' => array('title' => /*t(*/'Tuvalu'/*)*/, 'weight' => '0'),
    'UG' => array('title' => /*t(*/'Uganda'/*)*/, 'weight' => '0'),
    'UA' => array('title' => /*t(*/'Ukraine'/*)*/, 'weight' => '0'),
    'AE' => array('title' => /*t(*/'United Arab Emirates'/*)*/, 'weight' => '0'),
    'GB' => array('title' => /*t(*/'United Kingdom'/*)*/, 'weight' => '0'),
    'UM' => array(
      'title' => /*t(*/'United States Minor Outlying Islands'/*)*/,
      'weight' => '0'
    ),
    'UY' => array('title' => /*t(*/'Uruguay'/*)*/, 'weight' => '0'),
    'UZ' => array('title' => /*t(*/'Uzbekistan'/*)*/, 'weight' => '0'),
    'VU' => array('title' => /*t(*/'Vanuatu'/*)*/, 'weight' => '0'),
    'VA' => array('title' => /*t(*/'Vatican City'/*)*/, 'weight' => '0'),
    'VE' => array('title' => /*t(*/'Venezuela'/*)*/, 'weight' => '0'),
    'VN' => array('title' => /*t(*/'Viet Nam'/*)*/, 'weight' => '0'),
    'VG' => array('title' => /*t(*/'Virgin Islands (British)'/*)*/, 'weight' => '0'),
    'VI' => array('title' => /*t(*/'Virgin Islands'/*)*/, 'weight' => '0'),
    'WF' => array('title' => /*t(*/'Wallis and Futuna'/*)*/, 'weight' => '0'),
    'YE' => array('title' => /*t(*/'Yemen'/*)*/, 'weight' => '0'),
    'ZM' => array('title' => /*t(*/'Zambia'/*)*/, 'weight' => '0'),
    'ZW' => array('title' => /*t(*/'Zimbabwe'/*)*/, 'weight' => '0')
  );

  //TODO allow to modify by baseconfig (e.g for klickmail)
  // define ('STATES_OVVERIDE', array('DE' => array('title' => 'Germany', 'weight' => '0)));
  // $amember_countries = array_merge($amember_countries, STATES_OVVERIDE);

  // sort by 'weight' DESC
  klicktipp_uasort($amember_countries, array('weight' => 'DESC'));

  return $amember_countries;
}


/**
 * replacement for table `splittes_amember`.`amember_states`
 */
function amember_rpc_get_states($forcountry = '') {

  $amember_states = array(
    'AD-ALV' => array(
      'country' => 'AD',
      'title' => /*t(*/'Andorra la Vella'/*)*/,
      'tag' => '0'
    ),
    'AD-CAN' => array('country' => 'AD', 'title' => /*t(*/'Canillo'/*)*/, 'tag' => '0'),
    'AD-ENC' => array('country' => 'AD', 'title' => /*t(*/'Encamp'/*)*/, 'tag' => '0'),
    'AD-ESE' => array(
      'country' => 'AD',
      'title' => /*t(*/'Escaldes-Engordany'/*)*/,
      'tag' => '0'
    ),
    'AD-LMA' => array('country' => 'AD', 'title' => /*t(*/'La Massana'/*)*/, 'tag' => '0'),
    'AD-ORD' => array('country' => 'AD', 'title' => /*t(*/'Ordino'/*)*/, 'tag' => '0'),
    'AD-SJL' => array(
      'country' => 'AD',
      'title' => /*t(*/'Sant Julia de LOA'/*)*/,
      'tag' => '0'
    ),
    'AE-AJ' => array('country' => 'AE', 'title' => /*t(*/"'Ajman"/*)*/, 'tag' => '0'),
    'AE-AZ' => array('country' => 'AE', 'title' => /*t(*/'Abu Zaby'/*)*/, 'tag' => '0'),
    'AE-DU' => array('country' => 'AE', 'title' => /*t(*/'Dubayy'/*)*/, 'tag' => '0'),
    'AE-FU' => array('country' => 'AE', 'title' => /*t(*/'Al Fujayrah'/*)*/, 'tag' => '0'),
    'AE-RK' => array(
      'country' => 'AE',
      'title' => /*t(*/"R'as al Khaymah"/*)*/,
      'tag' => '0'
    ),
    'AE-SH' => array(
      'country' => 'AE',
      'title' => /*t(*/'Ash Shariqah'/*)*/,
      'tag' => '0'
    ),
    'AE-UQ' => array(
      'country' => 'AE',
      'title' => /*t(*/'Umm al Qaywayn'/*)*/,
      'tag' => '0'
    ),
    'AF-BAL' => array(
      'country' => 'AF',
      'title' => /*t(*/'Balkh province'/*)*/,
      'tag' => '0'
    ),
    'AF-BAM' => array(
      'country' => 'AF',
      'title' => /*t(*/'Bamian province'/*)*/,
      'tag' => '0'
    ),
    'AF-BDG' => array(
      'country' => 'AF',
      'title' => /*t(*/'Badghis province'/*)*/,
      'tag' => '0'
    ),
    'AF-BDS' => array(
      'country' => 'AF',
      'title' => /*t(*/'Badakhshan province'/*)*/,
      'tag' => '0'
    ),
    'AF-BGL' => array(
      'country' => 'AF',
      'title' => /*t(*/'Baghlan province'/*)*/,
      'tag' => '0'
    ),
    'AF-FRA' => array(
      'country' => 'AF',
      'title' => /*t(*/'Farah province'/*)*/,
      'tag' => '0'
    ),
    'AF-FYB' => array(
      'country' => 'AF',
      'title' => /*t(*/'Faryab province'/*)*/,
      'tag' => '0'
    ),
    'AF-GHA' => array(
      'country' => 'AF',
      'title' => /*t(*/'Ghazni province'/*)*/,
      'tag' => '0'
    ),
    'AF-GHO' => array(
      'country' => 'AF',
      'title' => /*t(*/'Ghowr province'/*)*/,
      'tag' => '0'
    ),
    'AF-HEL' => array(
      'country' => 'AF',
      'title' => /*t(*/'Helmand province'/*)*/,
      'tag' => '0'
    ),
    'AF-HER' => array(
      'country' => 'AF',
      'title' => /*t(*/'Herat province'/*)*/,
      'tag' => '0'
    ),
    'AF-JOW' => array(
      'country' => 'AF',
      'title' => /*t(*/'Jowzjan province'/*)*/,
      'tag' => '0'
    ),
    'AF-KAB' => array(
      'country' => 'AF',
      'title' => /*t(*/'Kabul province'/*)*/,
      'tag' => '0'
    ),
    'AF-KAN' => array(
      'country' => 'AF',
      'title' => /*t(*/'Kandahar province'/*)*/,
      'tag' => '0'
    ),
    'AF-KAP' => array(
      'country' => 'AF',
      'title' => /*t(*/'Kapisa province'/*)*/,
      'tag' => '0'
    ),
    'AF-KDZ' => array(
      'country' => 'AF',
      'title' => /*t(*/'Kondoz province'/*)*/,
      'tag' => '0'
    ),
    'AF-KHO' => array(
      'country' => 'AF',
      'title' => /*t(*/'Khost province'/*)*/,
      'tag' => '0'
    ),
    'AF-KNR' => array(
      'country' => 'AF',
      'title' => /*t(*/'Konar province'/*)*/,
      'tag' => '0'
    ),
    'AF-LAG' => array(
      'country' => 'AF',
      'title' => /*t(*/'Laghman province'/*)*/,
      'tag' => '0'
    ),
    'AF-LOW' => array(
      'country' => 'AF',
      'title' => /*t(*/'Lowgar province'/*)*/,
      'tag' => '0'
    ),
    'AF-NAN' => array(
      'country' => 'AF',
      'title' => /*t(*/'Nangrahar province'/*)*/,
      'tag' => '0'
    ),
    'AF-NIM' => array(
      'country' => 'AF',
      'title' => /*t(*/'Nimruz province'/*)*/,
      'tag' => '0'
    ),
    'AF-NUR' => array(
      'country' => 'AF',
      'title' => /*t(*/'Nurestan province'/*)*/,
      'tag' => '0'
    ),
    'AF-ORU' => array(
      'country' => 'AF',
      'title' => /*t(*/'Oruzgan province'/*)*/,
      'tag' => '0'
    ),
    'AF-PAR' => array(
      'country' => 'AF',
      'title' => /*t(*/'Parwan province'/*)*/,
      'tag' => '0'
    ),
    'AF-PIA' => array(
      'country' => 'AF',
      'title' => /*t(*/'Paktia province'/*)*/,
      'tag' => '0'
    ),
    'AF-PKA' => array(
      'country' => 'AF',
      'title' => /*t(*/'Paktika province'/*)*/,
      'tag' => '0'
    ),
    'AF-SAM' => array(
      'country' => 'AF',
      'title' => /*t(*/'Samangan province'/*)*/,
      'tag' => '0'
    ),
    'AF-SAR' => array(
      'country' => 'AF',
      'title' => /*t(*/'Sar-e Pol province'/*)*/,
      'tag' => '0'
    ),
    'AF-TAK' => array(
      'country' => 'AF',
      'title' => /*t(*/'Takhar province'/*)*/,
      'tag' => '0'
    ),
    'AF-WAR' => array(
      'country' => 'AF',
      'title' => /*t(*/'Wardak province'/*)*/,
      'tag' => '0'
    ),
    'AF-ZAB' => array(
      'country' => 'AF',
      'title' => /*t(*/'Zabol province'/*)*/,
      'tag' => '0'
    ),
    'AG-ASG' => array(
      'country' => 'AG',
      'title' => /*t(*/'Saint George'/*)*/,
      'tag' => '0'
    ),
    'AG-ASH' => array(
      'country' => 'AG',
      'title' => /*t(*/'Saint Philip'/*)*/,
      'tag' => '0'
    ),
    'AG-ASJ' => array('country' => 'AG', 'title' => /*t(*/'Saint John'/*)*/, 'tag' => '0'),
    'AG-ASL' => array('country' => 'AG', 'title' => /*t(*/'Saint Paul'/*)*/, 'tag' => '0'),
    'AG-ASM' => array('country' => 'AG', 'title' => /*t(*/'Saint Mary'/*)*/, 'tag' => '0'),
    'AG-ASR' => array(
      'country' => 'AG',
      'title' => /*t(*/'Saint Peter'/*)*/,
      'tag' => '0'
    ),
    'AG-BAR' => array('country' => 'AG', 'title' => /*t(*/'Barbuda'/*)*/, 'tag' => '0'),
    'AG-RED' => array('country' => 'AG', 'title' => /*t(*/'Redonda'/*)*/, 'tag' => '0'),
    'AL-BR' => array('country' => 'AL', 'title' => /*t(*/'Berat'/*)*/, 'tag' => '0'),
    'AL-BU' => array('country' => 'AL', 'title' => /*t(*/'Bulqize'/*)*/, 'tag' => '0'),
    'AL-DI' => array('country' => 'AL', 'title' => /*t(*/'Diber'/*)*/, 'tag' => '0'),
    'AL-DL' => array('country' => 'AL', 'title' => /*t(*/'Delvine'/*)*/, 'tag' => '0'),
    'AL-DR' => array('country' => 'AL', 'title' => /*t(*/'Durres'/*)*/, 'tag' => '0'),
    'AL-DV' => array('country' => 'AL', 'title' => /*t(*/'Devoll'/*)*/, 'tag' => '0'),
    'AL-EL' => array('country' => 'AL', 'title' => /*t(*/'Elbasan'/*)*/, 'tag' => '0'),
    'AL-ER' => array('country' => 'AL', 'title' => /*t(*/'Kolonje'/*)*/, 'tag' => '0'),
    'AL-FR' => array('country' => 'AL', 'title' => /*t(*/'Fier'/*)*/, 'tag' => '0'),
    'AL-GJ' => array('country' => 'AL', 'title' => /*t(*/'Gjirokaster'/*)*/, 'tag' => '0'),
    'AL-GR' => array('country' => 'AL', 'title' => /*t(*/'Gramsh'/*)*/, 'tag' => '0'),
    'AL-HA' => array('country' => 'AL', 'title' => /*t(*/'Has'/*)*/, 'tag' => '0'),
    'AL-KA' => array('country' => 'AL', 'title' => /*t(*/'Kavaje'/*)*/, 'tag' => '0'),
    'AL-KB' => array('country' => 'AL', 'title' => /*t(*/'Kurbin'/*)*/, 'tag' => '0'),
    'AL-KC' => array('country' => 'AL', 'title' => /*t(*/'Kucove'/*)*/, 'tag' => '0'),
    'AL-KO' => array('country' => 'AL', 'title' => /*t(*/'Korce'/*)*/, 'tag' => '0'),
    'AL-KR' => array('country' => 'AL', 'title' => /*t(*/'Kruje'/*)*/, 'tag' => '0'),
    'AL-KU' => array('country' => 'AL', 'title' => /*t(*/'Kukes'/*)*/, 'tag' => '0'),
    'AL-LB' => array('country' => 'AL', 'title' => /*t(*/'Librazhd'/*)*/, 'tag' => '0'),
    'AL-LE' => array('country' => 'AL', 'title' => /*t(*/'Lezhe'/*)*/, 'tag' => '0'),
    'AL-LU' => array('country' => 'AL', 'title' => /*t(*/'Lushnje'/*)*/, 'tag' => '0'),
    'AL-MK' => array('country' => 'AL', 'title' => /*t(*/'Mallakaster'/*)*/, 'tag' => '0'),
    'AL-MM' => array(
      'country' => 'AL',
      'title' => /*t(*/'Malesi e Madhe'/*)*/,
      'tag' => '0'
    ),
    'AL-MR' => array('country' => 'AL', 'title' => /*t(*/'Mirdite'/*)*/, 'tag' => '0'),
    'AL-MT' => array('country' => 'AL', 'title' => /*t(*/'Mat'/*)*/, 'tag' => '0'),
    'AL-PG' => array('country' => 'AL', 'title' => /*t(*/'Pogradec'/*)*/, 'tag' => '0'),
    'AL-PQ' => array('country' => 'AL', 'title' => /*t(*/'Peqin'/*)*/, 'tag' => '0'),
    'AL-PR' => array('country' => 'AL', 'title' => /*t(*/'Permet'/*)*/, 'tag' => '0'),
    'AL-PU' => array('country' => 'AL', 'title' => /*t(*/'Puke'/*)*/, 'tag' => '0'),
    'AL-SH' => array('country' => 'AL', 'title' => /*t(*/'Shkoder'/*)*/, 'tag' => '0'),
    'AL-SK' => array('country' => 'AL', 'title' => /*t(*/'Skrapar'/*)*/, 'tag' => '0'),
    'AL-SR' => array('country' => 'AL', 'title' => /*t(*/'Sarande'/*)*/, 'tag' => '0'),
    'AL-TE' => array('country' => 'AL', 'title' => /*t(*/'Tepelene'/*)*/, 'tag' => '0'),
    'AL-TP' => array('country' => 'AL', 'title' => /*t(*/'Tropoje'/*)*/, 'tag' => '0'),
    'AL-TR' => array('country' => 'AL', 'title' => /*t(*/'Tirane'/*)*/, 'tag' => '0'),
    'AL-VL' => array('country' => 'AL', 'title' => /*t(*/'Vlore'/*)*/, 'tag' => '0'),
    'AM-AGT' => array('country' => 'AM', 'title' => /*t(*/'Aragatsotn'/*)*/, 'tag' => '0'),
    'AM-ARM' => array('country' => 'AM', 'title' => /*t(*/'Armavir'/*)*/, 'tag' => '0'),
    'AM-ARR' => array('country' => 'AM', 'title' => /*t(*/'Ararat'/*)*/, 'tag' => '0'),
    'AM-GEG' => array(
      'country' => 'AM',
      'title' => /*t(*/"Geghark'unik'"/*)*/,
      'tag' => '0'
    ),
    'AM-KOT' => array('country' => 'AM', 'title' => /*t(*/"Kotayk'"/*)*/, 'tag' => '0'),
    'AM-LOR' => array('country' => 'AM', 'title' => /*t(*/'Lorri'/*)*/, 'tag' => '0'),
    'AM-SHI' => array('country' => 'AM', 'title' => /*t(*/'Shirak'/*)*/, 'tag' => '0'),
    'AM-SYU' => array('country' => 'AM', 'title' => /*t(*/"Syunik'"/*)*/, 'tag' => '0'),
    'AM-TAV' => array('country' => 'AM', 'title' => /*t(*/'Tavush'/*)*/, 'tag' => '0'),
    'AM-VAY' => array(
      'country' => 'AM',
      'title' => /*t(*/"Vayots' Dzor"/*)*/,
      'tag' => '0'
    ),
    'AM-YER' => array('country' => 'AM', 'title' => /*t(*/'Yerevan'/*)*/, 'tag' => '0'),
    'AO-BGO' => array('country' => 'AO', 'title' => /*t(*/'Bengo'/*)*/, 'tag' => '0'),
    'AO-BGU' => array(
      'country' => 'AO',
      'title' => /*t(*/'Benguela province'/*)*/,
      'tag' => '0'
    ),
    'AO-BIE' => array('country' => 'AO', 'title' => /*t(*/'Bie'/*)*/, 'tag' => '0'),
    'AO-CAB' => array('country' => 'AO', 'title' => /*t(*/'Cabinda'/*)*/, 'tag' => '0'),
    'AO-CCU' => array(
      'country' => 'AO',
      'title' => /*t(*/'Cuando-Cubango'/*)*/,
      'tag' => '0'
    ),
    'AO-CNN' => array('country' => 'AO', 'title' => /*t(*/'Cunene'/*)*/, 'tag' => '0'),
    'AO-CNO' => array(
      'country' => 'AO',
      'title' => /*t(*/'Cuanza Norte'/*)*/,
      'tag' => '0'
    ),
    'AO-CUS' => array('country' => 'AO', 'title' => /*t(*/'Cuanza Sul'/*)*/, 'tag' => '0'),
    'AO-HUA' => array(
      'country' => 'AO',
      'title' => /*t(*/'Huambo province'/*)*/,
      'tag' => '0'
    ),
    'AO-HUI' => array(
      'country' => 'AO',
      'title' => /*t(*/'Huila province'/*)*/,
      'tag' => '0'
    ),
    'AO-LNO' => array(
      'country' => 'AO',
      'title' => /*t(*/'Lunda Norte'/*)*/,
      'tag' => '0'
    ),
    'AO-LSU' => array('country' => 'AO', 'title' => /*t(*/'Lunda Sul'/*)*/, 'tag' => '0'),
    'AO-LUA' => array('country' => 'AO', 'title' => /*t(*/'Luanda'/*)*/, 'tag' => '0'),
    'AO-MAL' => array('country' => 'AO', 'title' => /*t(*/'Malange'/*)*/, 'tag' => '0'),
    'AO-MOX' => array('country' => 'AO', 'title' => /*t(*/'Moxico'/*)*/, 'tag' => '0'),
    'AO-NAM' => array('country' => 'AO', 'title' => /*t(*/'Namibe'/*)*/, 'tag' => '0'),
    'AO-UIG' => array('country' => 'AO', 'title' => /*t(*/'Uige'/*)*/, 'tag' => '0'),
    'AO-ZAI' => array('country' => 'AO', 'title' => /*t(*/'Zaire'/*)*/, 'tag' => '0'),
    'AR-A' => array('country' => 'AR', 'title' => /*t(*/'Salta'/*)*/, 'tag' => '0'),
    'AR-B' => array(
      'country' => 'AR',
      'title' => /*t(*/'Buenos Aires Province'/*)*/,
      'tag' => '0'
    ),
    'AR-C' => array(
      'country' => 'AR',
      'title' => /*t(*/'Distrito Federal'/*)*/,
      'tag' => '0'
    ),
    'AR-D' => array('country' => 'AR', 'title' => /*t(*/'San Luis'/*)*/, 'tag' => '0'),
    'AR-E' => array('country' => 'AR', 'title' => /*t(*/'Entre Rios'/*)*/, 'tag' => '0'),
    'AR-F' => array('country' => 'AR', 'title' => /*t(*/'La Rioja'/*)*/, 'tag' => '0'),
    'AR-G' => array(
      'country' => 'AR',
      'title' => /*t(*/'Santiago del Estero'/*)*/,
      'tag' => '0'
    ),
    'AR-H' => array('country' => 'AR', 'title' => /*t(*/'Chaco'/*)*/, 'tag' => '0'),
    'AR-J' => array('country' => 'AR', 'title' => /*t(*/'San Juan'/*)*/, 'tag' => '0'),
    'AR-K' => array('country' => 'AR', 'title' => /*t(*/'Catamarca'/*)*/, 'tag' => '0'),
    'AR-L' => array('country' => 'AR', 'title' => /*t(*/'La Pampa'/*)*/, 'tag' => '0'),
    'AR-M' => array('country' => 'AR', 'title' => /*t(*/'Mendoza'/*)*/, 'tag' => '0'),
    'AR-N' => array('country' => 'AR', 'title' => /*t(*/'Misiones'/*)*/, 'tag' => '0'),
    'AR-P' => array('country' => 'AR', 'title' => /*t(*/'Formosa'/*)*/, 'tag' => '0'),
    'AR-Q' => array('country' => 'AR', 'title' => /*t(*/'Neuquen'/*)*/, 'tag' => '0'),
    'AR-R' => array('country' => 'AR', 'title' => /*t(*/'Rio Negro'/*)*/, 'tag' => '0'),
    'AR-S' => array('country' => 'AR', 'title' => /*t(*/'Santa Fe'/*)*/, 'tag' => '0'),
    'AR-T' => array('country' => 'AR', 'title' => /*t(*/'Tucuman'/*)*/, 'tag' => '0'),
    'AR-U' => array('country' => 'AR', 'title' => /*t(*/'Chubut'/*)*/, 'tag' => '0'),
    'AR-V' => array(
      'country' => 'AR',
      'title' => /*t(*/'Tierra del Fuego'/*)*/,
      'tag' => '0'
    ),
    'AR-W' => array('country' => 'AR', 'title' => /*t(*/'Corrientes'/*)*/, 'tag' => '0'),
    'AR-X' => array('country' => 'AR', 'title' => /*t(*/'Cordoba'/*)*/, 'tag' => '0'),
    'AR-Y' => array('country' => 'AR', 'title' => /*t(*/'Jujuy'/*)*/, 'tag' => '0'),
    'AR-Z' => array('country' => 'AR', 'title' => /*t(*/'Santa Cruz'/*)*/, 'tag' => '0'),
    'AS-E' => array('country' => 'AS', 'title' => /*t(*/'Eastern'/*)*/, 'tag' => '0'),
    'AS-M' => array('country' => 'AS', 'title' => /*t(*/"Manu'a"/*)*/, 'tag' => '0'),
    'AS-R' => array('country' => 'AS', 'title' => /*t(*/'Rose Island'/*)*/, 'tag' => '0'),
    'AS-S' => array(
      'country' => 'AS',
      'title' => /*t(*/'Swains Island'/*)*/,
      'tag' => '0'
    ),
    'AS-W' => array('country' => 'AS', 'title' => /*t(*/'Western'/*)*/, 'tag' => '0'),
    'AT-BUR' => array('country' => 'AT', 'title' => /*t(*/'Burgenland'/*)*/, 'tag' => '0'),
    'AT-KAR' => array('country' => 'AT', 'title' => /*t(*/'Karnten'/*)*/, 'tag' => '0'),
    'AT-NOS' => array(
      'country' => 'AT',
      'title' => /*t(*/'Niederosterreich'/*)*/,
      'tag' => '0'
    ),
    'AT-OOS' => array(
      'country' => 'AT',
      'title' => /*t(*/'Oberosterreich'/*)*/,
      'tag' => '0'
    ),
    'AT-SAL' => array('country' => 'AT', 'title' => /*t(*/'Salzburg'/*)*/, 'tag' => '0'),
    'AT-STE' => array('country' => 'AT', 'title' => /*t(*/'Steiermark'/*)*/, 'tag' => '0'),
    'AT-TIR' => array('country' => 'AT', 'title' => /*t(*/'Tirol'/*)*/, 'tag' => '0'),
    'AT-VOR' => array('country' => 'AT', 'title' => /*t(*/'Vorarlberg'/*)*/, 'tag' => '0'),
    'AT-WIE' => array('country' => 'AT', 'title' => /*t(*/'Wien'/*)*/, 'tag' => '0'),
    'AU-ACT' => array(
      'country' => 'AU',
      'title' => /*t(*/'Australian Capital Territory'/*)*/,
      'tag' => '0'
    ),
    'AU-NSW' => array(
      'country' => 'AU',
      'title' => /*t(*/'New South Wales'/*)*/,
      'tag' => '0'
    ),
    'AU-NT' => array(
      'country' => 'AU',
      'title' => /*t(*/'Northern Territory'/*)*/,
      'tag' => '0'
    ),
    'AU-QLD' => array('country' => 'AU', 'title' => /*t(*/'Queensland'/*)*/, 'tag' => '0'),
    'AU-SA' => array(
      'country' => 'AU',
      'title' => /*t(*/'South Australia'/*)*/,
      'tag' => '0'
    ),
    'AU-TAS' => array('country' => 'AU', 'title' => /*t(*/'Tasmania'/*)*/, 'tag' => '0'),
    'AU-VIC' => array('country' => 'AU', 'title' => /*t(*/'Victoria'/*)*/, 'tag' => '0'),
    'AU-WA' => array(
      'country' => 'AU',
      'title' => /*t(*/'Western Australia'/*)*/,
      'tag' => '0'
    ),
    'AZ-AB' => array(
      'country' => 'AZ',
      'title' => /*t(*/'Ali Bayramli'/*)*/,
      'tag' => '0'
    ),
    'AZ-ABS' => array('country' => 'AZ', 'title' => /*t(*/'Abseron'/*)*/, 'tag' => '0'),
    'AZ-AGA' => array('country' => 'AZ', 'title' => /*t(*/'Agstafa'/*)*/, 'tag' => '0'),
    'AZ-AGC' => array('country' => 'AZ', 'title' => /*t(*/'AgcabAdi'/*)*/, 'tag' => '0'),
    'AZ-AGM' => array('country' => 'AZ', 'title' => /*t(*/'Agdam'/*)*/, 'tag' => '0'),
    'AZ-AGS' => array('country' => 'AZ', 'title' => /*t(*/'Agdas'/*)*/, 'tag' => '0'),
    'AZ-AGU' => array('country' => 'AZ', 'title' => /*t(*/'Agsu'/*)*/, 'tag' => '0'),
    'AZ-AST' => array('country' => 'AZ', 'title' => /*t(*/'Astara'/*)*/, 'tag' => '0'),
    'AZ-BA' => array('country' => 'AZ', 'title' => /*t(*/'Baki'/*)*/, 'tag' => '0'),
    'AZ-BAB' => array('country' => 'AZ', 'title' => /*t(*/'Babak'/*)*/, 'tag' => '0'),
    'AZ-BAL' => array('country' => 'AZ', 'title' => /*t(*/'Balakan'/*)*/, 'tag' => '0'),
    'AZ-BAR' => array('country' => 'AZ', 'title' => /*t(*/'Barda'/*)*/, 'tag' => '0'),
    'AZ-BEY' => array('country' => 'AZ', 'title' => /*t(*/'Beylaqan'/*)*/, 'tag' => '0'),
    'AZ-BIL' => array('country' => 'AZ', 'title' => /*t(*/'Bilasuvar'/*)*/, 'tag' => '0'),
    'AZ-CAB' => array('country' => 'AZ', 'title' => /*t(*/'Cabrayil'/*)*/, 'tag' => '0'),
    'AZ-CAL' => array('country' => 'AZ', 'title' => /*t(*/'Calilabab'/*)*/, 'tag' => '0'),
    'AZ-CUL' => array('country' => 'AZ', 'title' => /*t(*/'Culfa'/*)*/, 'tag' => '0'),
    'AZ-DAS' => array('country' => 'AZ', 'title' => /*t(*/'Daskasan'/*)*/, 'tag' => '0'),
    'AZ-DAV' => array('country' => 'AZ', 'title' => /*t(*/'Davaci'/*)*/, 'tag' => '0'),
    'AZ-FUZ' => array('country' => 'AZ', 'title' => /*t(*/'Fuzuli'/*)*/, 'tag' => '0'),
    'AZ-GA' => array('country' => 'AZ', 'title' => /*t(*/'Ganca'/*)*/, 'tag' => '0'),
    'AZ-GAD' => array('country' => 'AZ', 'title' => /*t(*/'Gadabay'/*)*/, 'tag' => '0'),
    'AZ-GOR' => array('country' => 'AZ', 'title' => /*t(*/'Goranboy'/*)*/, 'tag' => '0'),
    'AZ-GOY' => array('country' => 'AZ', 'title' => /*t(*/'Goycay'/*)*/, 'tag' => '0'),
    'AZ-HAC' => array('country' => 'AZ', 'title' => /*t(*/'Haciqabul'/*)*/, 'tag' => '0'),
    'AZ-IMI' => array('country' => 'AZ', 'title' => /*t(*/'Imisli'/*)*/, 'tag' => '0'),
    'AZ-ISM' => array('country' => 'AZ', 'title' => /*t(*/'Ismayilli'/*)*/, 'tag' => '0'),
    'AZ-KAL' => array('country' => 'AZ', 'title' => /*t(*/'Kalbacar'/*)*/, 'tag' => '0'),
    'AZ-KUR' => array('country' => 'AZ', 'title' => /*t(*/'Kurdamir'/*)*/, 'tag' => '0'),
    'AZ-LAC' => array('country' => 'AZ', 'title' => /*t(*/'Lacin'/*)*/, 'tag' => '0'),
    'AZ-LAN' => array('country' => 'AZ', 'title' => /*t(*/'Lankaran'/*)*/, 'tag' => '0'),
    'AZ-LER' => array('country' => 'AZ', 'title' => /*t(*/'Lerik'/*)*/, 'tag' => '0'),
    'AZ-MAS' => array('country' => 'AZ', 'title' => /*t(*/'Masalli'/*)*/, 'tag' => '0'),
    'AZ-MI' => array('country' => 'AZ', 'title' => /*t(*/'Mingacevir'/*)*/, 'tag' => '0'),
    'AZ-NA' => array('country' => 'AZ', 'title' => /*t(*/'Naftalan'/*)*/, 'tag' => '0'),
    'AZ-NEF' => array('country' => 'AZ', 'title' => /*t(*/'Neftcala'/*)*/, 'tag' => '0'),
    'AZ-NX' => array('country' => 'AZ', 'title' => /*t(*/'Naxcivan'/*)*/, 'tag' => '0'),
    'AZ-OGU' => array('country' => 'AZ', 'title' => /*t(*/'Oguz'/*)*/, 'tag' => '0'),
    'AZ-ORD' => array('country' => 'AZ', 'title' => /*t(*/'Ordubad'/*)*/, 'tag' => '0'),
    'AZ-QAB' => array('country' => 'AZ', 'title' => /*t(*/'Qabala'/*)*/, 'tag' => '0'),
    'AZ-QAX' => array('country' => 'AZ', 'title' => /*t(*/'Qax'/*)*/, 'tag' => '0'),
    'AZ-QAZ' => array('country' => 'AZ', 'title' => /*t(*/'Qazax'/*)*/, 'tag' => '0'),
    'AZ-QBA' => array('country' => 'AZ', 'title' => /*t(*/'Quba'/*)*/, 'tag' => '0'),
    'AZ-QBI' => array('country' => 'AZ', 'title' => /*t(*/'Qubadli'/*)*/, 'tag' => '0'),
    'AZ-QOB' => array('country' => 'AZ', 'title' => /*t(*/'Qobustan'/*)*/, 'tag' => '0'),
    'AZ-QUS' => array('country' => 'AZ', 'title' => /*t(*/'Qusar'/*)*/, 'tag' => '0'),
    'AZ-SA' => array('country' => 'AZ', 'title' => /*t(*/'Saki'/*)*/, 'tag' => '0'),
    'AZ-SAB' => array('country' => 'AZ', 'title' => /*t(*/'Sabirabad'/*)*/, 'tag' => '0'),
    'AZ-SAD' => array('country' => 'AZ', 'title' => /*t(*/'Sadarak'/*)*/, 'tag' => '0'),
    'AZ-SAH' => array('country' => 'AZ', 'title' => /*t(*/'Sahbuz'/*)*/, 'tag' => '0'),
    'AZ-SAK' => array('country' => 'AZ', 'title' => /*t(*/'Saki'/*)*/, 'tag' => '0'),
    'AZ-SAL' => array('country' => 'AZ', 'title' => /*t(*/'Salyan'/*)*/, 'tag' => '0'),
    'AZ-SAR' => array('country' => 'AZ', 'title' => /*t(*/'Sarur'/*)*/, 'tag' => '0'),
    'AZ-SAT' => array('country' => 'AZ', 'title' => /*t(*/'Saatli'/*)*/, 'tag' => '0'),
    'AZ-SIY' => array('country' => 'AZ', 'title' => /*t(*/'Siyazan'/*)*/, 'tag' => '0'),
    'AZ-SKR' => array('country' => 'AZ', 'title' => /*t(*/'Samkir'/*)*/, 'tag' => '0'),
    'AZ-SM' => array('country' => 'AZ', 'title' => /*t(*/'Sumqayit'/*)*/, 'tag' => '0'),
    'AZ-SMI' => array('country' => 'AZ', 'title' => /*t(*/'Samaxi'/*)*/, 'tag' => '0'),
    'AZ-SMX' => array('country' => 'AZ', 'title' => /*t(*/'Samux'/*)*/, 'tag' => '0'),
    'AZ-SS' => array('country' => 'AZ', 'title' => /*t(*/'Susa'/*)*/, 'tag' => '0'),
    'AZ-SUS' => array('country' => 'AZ', 'title' => /*t(*/'Susa'/*)*/, 'tag' => '0'),
    'AZ-TAR' => array('country' => 'AZ', 'title' => /*t(*/'Tartar'/*)*/, 'tag' => '0'),
    'AZ-TOV' => array('country' => 'AZ', 'title' => /*t(*/'Tovuz'/*)*/, 'tag' => '0'),
    'AZ-UCA' => array('country' => 'AZ', 'title' => /*t(*/'Ucar'/*)*/, 'tag' => '0'),
    'AZ-XA' => array('country' => 'AZ', 'title' => /*t(*/'Xankandi'/*)*/, 'tag' => '0'),
    'AZ-XAC' => array('country' => 'AZ', 'title' => /*t(*/'Xacmaz'/*)*/, 'tag' => '0'),
    'AZ-XAN' => array('country' => 'AZ', 'title' => /*t(*/'Xanlar'/*)*/, 'tag' => '0'),
    'AZ-XCI' => array('country' => 'AZ', 'title' => /*t(*/'Xocali'/*)*/, 'tag' => '0'),
    'AZ-XIZ' => array('country' => 'AZ', 'title' => /*t(*/'Xizi'/*)*/, 'tag' => '0'),
    'AZ-XVD' => array('country' => 'AZ', 'title' => /*t(*/'Xocavand'/*)*/, 'tag' => '0'),
    'AZ-YAR' => array('country' => 'AZ', 'title' => /*t(*/'Yardimli'/*)*/, 'tag' => '0'),
    'AZ-YEV' => array('country' => 'AZ', 'title' => /*t(*/'Yevlax'/*)*/, 'tag' => '0'),
    'AZ-ZAN' => array('country' => 'AZ', 'title' => /*t(*/'Zangilan'/*)*/, 'tag' => '0'),
    'AZ-ZAQ' => array('country' => 'AZ', 'title' => /*t(*/'Zaqatala'/*)*/, 'tag' => '0'),
    'AZ-ZAR' => array('country' => 'AZ', 'title' => /*t(*/'Zardab'/*)*/, 'tag' => '0'),
    'BA-BRO' => array(
      'country' => 'BA',
      'title' => /*t(*/'Brcko district'/*)*/,
      'tag' => '0'
    ),
    'BA-FBP' => array(
      'country' => 'BA',
      'title' => /*t(*/'Bosanskopodrinjski Kanton'/*)*/,
      'tag' => '0'
    ),
    'BA-FHN' => array(
      'country' => 'BA',
      'title' => /*t(*/'Hercegovacko-neretvanski Kanton'/*)*/,
      'tag' => '0'
    ),
    'BA-FPO' => array(
      'country' => 'BA',
      'title' => /*t(*/'Posavski Kanton'/*)*/,
      'tag' => '0'
    ),
    'BA-FSA' => array(
      'country' => 'BA',
      'title' => /*t(*/'Kanton Sarajevo'/*)*/,
      'tag' => '0'
    ),
    'BA-FSB' => array(
      'country' => 'BA',
      'title' => /*t(*/'Srednjebosanski Kanton'/*)*/,
      'tag' => '0'
    ),
    'BA-FTU' => array(
      'country' => 'BA',
      'title' => /*t(*/'Tuzlanski Kanton'/*)*/,
      'tag' => '0'
    ),
    'BA-FUS' => array(
      'country' => 'BA',
      'title' => /*t(*/'Unsko-Sanski Kanton'/*)*/,
      'tag' => '0'
    ),
    'BA-FZA' => array(
      'country' => 'BA',
      'title' => /*t(*/'Zapadnobosanska'/*)*/,
      'tag' => '0'
    ),
    'BA-FZE' => array(
      'country' => 'BA',
      'title' => /*t(*/'Zenicko-Dobojski Kanton'/*)*/,
      'tag' => '0'
    ),
    'BA-FZH' => array(
      'country' => 'BA',
      'title' => /*t(*/'Zapadnohercegovacka Zupanija'/*)*/,
      'tag' => '0'
    ),
    'BA-SBI' => array('country' => 'BA', 'title' => /*t(*/'Bijeljina'/*)*/, 'tag' => '0'),
    'BA-SBL' => array('country' => 'BA', 'title' => /*t(*/'Banja Luka'/*)*/, 'tag' => '0'),
    'BA-SDO' => array('country' => 'BA', 'title' => /*t(*/'Doboj'/*)*/, 'tag' => '0'),
    'BA-SFO' => array('country' => 'BA', 'title' => /*t(*/'Foca'/*)*/, 'tag' => '0'),
    'BA-SSR' => array(
      'country' => 'BA',
      'title' => /*t(*/'Sarajevo-Romanija or Sokolac'/*)*/,
      'tag' => '0'
    ),
    'BA-STR' => array('country' => 'BA', 'title' => /*t(*/'Trebinje'/*)*/, 'tag' => '0'),
    'BA-SVL' => array('country' => 'BA', 'title' => /*t(*/'Vlasenica'/*)*/, 'tag' => '0'),
    'BB-AND' => array(
      'country' => 'BB',
      'title' => /*t(*/'Saint Andrew'/*)*/,
      'tag' => '0'
    ),
    'BB-CC' => array(
      'country' => 'BB',
      'title' => /*t(*/'Christ Church'/*)*/,
      'tag' => '0'
    ),
    'BB-GEO' => array(
      'country' => 'BB',
      'title' => /*t(*/'Saint George'/*)*/,
      'tag' => '0'
    ),
    'BB-JAM' => array(
      'country' => 'BB',
      'title' => /*t(*/'Saint James'/*)*/,
      'tag' => '0'
    ),
    'BB-JOH' => array('country' => 'BB', 'title' => /*t(*/'Saint John'/*)*/, 'tag' => '0'),
    'BB-JOS' => array(
      'country' => 'BB',
      'title' => /*t(*/'Saint Joseph'/*)*/,
      'tag' => '0'
    ),
    'BB-LUC' => array('country' => 'BB', 'title' => /*t(*/'Saint Lucy'/*)*/, 'tag' => '0'),
    'BB-MIC' => array(
      'country' => 'BB',
      'title' => /*t(*/'Saint Michael'/*)*/,
      'tag' => '0'
    ),
    'BB-PET' => array(
      'country' => 'BB',
      'title' => /*t(*/'Saint Peter'/*)*/,
      'tag' => '0'
    ),
    'BB-PHI' => array(
      'country' => 'BB',
      'title' => /*t(*/'Saint Philip'/*)*/,
      'tag' => '0'
    ),
    'BB-THO' => array(
      'country' => 'BB',
      'title' => /*t(*/'Saint Thomas'/*)*/,
      'tag' => '0'
    ),
    'BD-BAR' => array('country' => 'BD', 'title' => /*t(*/'Barisal'/*)*/, 'tag' => '0'),
    'BD-CHI' => array('country' => 'BD', 'title' => /*t(*/'Chittagong'/*)*/, 'tag' => '0'),
    'BD-DHA' => array('country' => 'BD', 'title' => /*t(*/'Dhaka'/*)*/, 'tag' => '0'),
    'BD-KHU' => array('country' => 'BD', 'title' => /*t(*/'Khulna'/*)*/, 'tag' => '0'),
    'BD-RAJ' => array('country' => 'BD', 'title' => /*t(*/'Rajshahi'/*)*/, 'tag' => '0'),
    'BD-SYL' => array('country' => 'BD', 'title' => /*t(*/'Sylhet'/*)*/, 'tag' => '0'),
    'BE-VAN' => array('country' => 'BE', 'title' => /*t(*/'Antwerpen'/*)*/, 'tag' => '0'),
    'BE-VBR' => array(
      'country' => 'BE',
      'title' => /*t(*/'Vlaams Brabant'/*)*/,
      'tag' => '0'
    ),
    'BE-VLI' => array('country' => 'BE', 'title' => /*t(*/'Limburg'/*)*/, 'tag' => '0'),
    'BE-VOV' => array(
      'country' => 'BE',
      'title' => /*t(*/'Oost-Vlaanderen'/*)*/,
      'tag' => '0'
    ),
    'BE-VWV' => array(
      'country' => 'BE',
      'title' => /*t(*/'West-Vlaanderen'/*)*/,
      'tag' => '0'
    ),
    'BE-WBR' => array(
      'country' => 'BE',
      'title' => /*t(*/'Brabant Wallon'/*)*/,
      'tag' => '0'
    ),
    'BE-WHT' => array('country' => 'BE', 'title' => /*t(*/'Hainaut'/*)*/, 'tag' => '0'),
    'BE-WLG' => array('country' => 'BE', 'title' => /*t(*/'Liege'/*)*/, 'tag' => '0'),
    'BE-WLX' => array('country' => 'BE', 'title' => /*t(*/'Luxembourg'/*)*/, 'tag' => '0'),
    'BE-WNA' => array('country' => 'BE', 'title' => /*t(*/'Namur'/*)*/, 'tag' => '0'),
    'BF-BAL' => array('country' => 'BF', 'title' => /*t(*/'Bale'/*)*/, 'tag' => '0'),
    'BF-BAM' => array('country' => 'BF', 'title' => /*t(*/'Bam'/*)*/, 'tag' => '0'),
    'BF-BAN' => array('country' => 'BF', 'title' => /*t(*/'Banwa'/*)*/, 'tag' => '0'),
    'BF-BAZ' => array('country' => 'BF', 'title' => /*t(*/'Bazega'/*)*/, 'tag' => '0'),
    'BF-BLG' => array('country' => 'BF', 'title' => /*t(*/'Boulgou'/*)*/, 'tag' => '0'),
    'BF-BOK' => array('country' => 'BF', 'title' => /*t(*/'Boulkiemde'/*)*/, 'tag' => '0'),
    'BF-BOR' => array('country' => 'BF', 'title' => /*t(*/'Bougouriba'/*)*/, 'tag' => '0'),
    'BF-COM' => array('country' => 'BF', 'title' => /*t(*/'Comoe'/*)*/, 'tag' => '0'),
    'BF-GAN' => array('country' => 'BF', 'title' => /*t(*/'Ganzourgou'/*)*/, 'tag' => '0'),
    'BF-GNA' => array('country' => 'BF', 'title' => /*t(*/'Gnagna'/*)*/, 'tag' => '0'),
    'BF-GOU' => array('country' => 'BF', 'title' => /*t(*/'Gourma'/*)*/, 'tag' => '0'),
    'BF-HOU' => array('country' => 'BF', 'title' => /*t(*/'Houet'/*)*/, 'tag' => '0'),
    'BF-IOA' => array('country' => 'BF', 'title' => /*t(*/'Ioba'/*)*/, 'tag' => '0'),
    'BF-KAD' => array('country' => 'BF', 'title' => /*t(*/'Kadiogo'/*)*/, 'tag' => '0'),
    'BF-KEN' => array('country' => 'BF', 'title' => /*t(*/'Kenedougou'/*)*/, 'tag' => '0'),
    'BF-KOD' => array('country' => 'BF', 'title' => /*t(*/'Komondjari'/*)*/, 'tag' => '0'),
    'BF-KOL' => array('country' => 'BF', 'title' => /*t(*/'Koulpelogo'/*)*/, 'tag' => '0'),
    'BF-KOP' => array('country' => 'BF', 'title' => /*t(*/'Kompienga'/*)*/, 'tag' => '0'),
    'BF-KOS' => array('country' => 'BF', 'title' => /*t(*/'Kossi'/*)*/, 'tag' => '0'),
    'BF-KOT' => array('country' => 'BF', 'title' => /*t(*/'Kouritenga'/*)*/, 'tag' => '0'),
    'BF-KOW' => array('country' => 'BF', 'title' => /*t(*/'Kourweogo'/*)*/, 'tag' => '0'),
    'BF-LER' => array('country' => 'BF', 'title' => /*t(*/'Leraba'/*)*/, 'tag' => '0'),
    'BF-LOR' => array('country' => 'BF', 'title' => /*t(*/'Loroum'/*)*/, 'tag' => '0'),
    'BF-MOU' => array('country' => 'BF', 'title' => /*t(*/'Mouhoun'/*)*/, 'tag' => '0'),
    'BF-NAH' => array('country' => 'BF', 'title' => /*t(*/'Nahouri'/*)*/, 'tag' => '0'),
    'BF-NAM' => array('country' => 'BF', 'title' => /*t(*/'Namentenga'/*)*/, 'tag' => '0'),
    'BF-NAY' => array('country' => 'BF', 'title' => /*t(*/'Nayala'/*)*/, 'tag' => '0'),
    'BF-NOU' => array('country' => 'BF', 'title' => /*t(*/'Noumbiel'/*)*/, 'tag' => '0'),
    'BF-OUB' => array('country' => 'BF', 'title' => /*t(*/'Oubritenga'/*)*/, 'tag' => '0'),
    'BF-OUD' => array('country' => 'BF', 'title' => /*t(*/'Oudalan'/*)*/, 'tag' => '0'),
    'BF-PAS' => array('country' => 'BF', 'title' => /*t(*/'Passore'/*)*/, 'tag' => '0'),
    'BF-PON' => array('country' => 'BF', 'title' => /*t(*/'Poni'/*)*/, 'tag' => '0'),
    'BF-SAG' => array('country' => 'BF', 'title' => /*t(*/'Sanguie'/*)*/, 'tag' => '0'),
    'BF-SAM' => array('country' => 'BF', 'title' => /*t(*/'Sanmatenga'/*)*/, 'tag' => '0'),
    'BF-SEN' => array('country' => 'BF', 'title' => /*t(*/'Seno'/*)*/, 'tag' => '0'),
    'BF-SIS' => array('country' => 'BF', 'title' => /*t(*/'Sissili'/*)*/, 'tag' => '0'),
    'BF-SOM' => array('country' => 'BF', 'title' => /*t(*/'Soum'/*)*/, 'tag' => '0'),
    'BF-SOR' => array('country' => 'BF', 'title' => /*t(*/'Sourou'/*)*/, 'tag' => '0'),
    'BF-TAP' => array('country' => 'BF', 'title' => /*t(*/'Tapoa'/*)*/, 'tag' => '0'),
    'BF-TUY' => array('country' => 'BF', 'title' => /*t(*/'Tuy'/*)*/, 'tag' => '0'),
    'BF-YAG' => array('country' => 'BF', 'title' => /*t(*/'Yagha'/*)*/, 'tag' => '0'),
    'BF-YAT' => array('country' => 'BF', 'title' => /*t(*/'Yatenga'/*)*/, 'tag' => '0'),
    'BF-ZIR' => array('country' => 'BF', 'title' => /*t(*/'Ziro'/*)*/, 'tag' => '0'),
    'BF-ZOD' => array('country' => 'BF', 'title' => /*t(*/'Zondoma'/*)*/, 'tag' => '0'),
    'BF-ZOW' => array('country' => 'BF', 'title' => /*t(*/'Zoundweogo'/*)*/, 'tag' => '0'),
    'BG-01' => array('country' => 'BG', 'title' => /*t(*/'Blagoevgrad'/*)*/, 'tag' => '0'),
    'BG-02' => array('country' => 'BG', 'title' => /*t(*/'Burgas'/*)*/, 'tag' => '0'),
    'BG-03' => array('country' => 'BG', 'title' => /*t(*/'Varna'/*)*/, 'tag' => '0'),
    'BG-04' => array(
      'country' => 'BG',
      'title' => /*t(*/'Veliko Turnovo'/*)*/,
      'tag' => '0'
    ),
    'BG-05' => array('country' => 'BG', 'title' => /*t(*/'Vidin'/*)*/, 'tag' => '0'),
    'BG-06' => array('country' => 'BG', 'title' => /*t(*/'Vratsa'/*)*/, 'tag' => '0'),
    'BG-07' => array('country' => 'BG', 'title' => /*t(*/'Gabrovo'/*)*/, 'tag' => '0'),
    'BG-08' => array('country' => 'BG', 'title' => /*t(*/'Dobrich'/*)*/, 'tag' => '0'),
    'BG-09' => array('country' => 'BG', 'title' => /*t(*/'Kurdzhali'/*)*/, 'tag' => '0'),
    'BG-10' => array('country' => 'BG', 'title' => /*t(*/'Kyustendil'/*)*/, 'tag' => '0'),
    'BG-11' => array('country' => 'BG', 'title' => /*t(*/'Lovech'/*)*/, 'tag' => '0'),
    'BG-12' => array('country' => 'BG', 'title' => /*t(*/'Montana'/*)*/, 'tag' => '0'),
    'BG-13' => array('country' => 'BG', 'title' => /*t(*/'Pazardzhik'/*)*/, 'tag' => '0'),
    'BG-14' => array('country' => 'BG', 'title' => /*t(*/'Pernik'/*)*/, 'tag' => '0'),
    'BG-15' => array('country' => 'BG', 'title' => /*t(*/'Pleven'/*)*/, 'tag' => '0'),
    'BG-16' => array('country' => 'BG', 'title' => /*t(*/'Plovdiv'/*)*/, 'tag' => '0'),
    'BG-17' => array('country' => 'BG', 'title' => /*t(*/'Razgrad'/*)*/, 'tag' => '0'),
    'BG-18' => array('country' => 'BG', 'title' => /*t(*/'Ruse'/*)*/, 'tag' => '0'),
    'BG-19' => array('country' => 'BG', 'title' => /*t(*/'Silistra'/*)*/, 'tag' => '0'),
    'BG-20' => array('country' => 'BG', 'title' => /*t(*/'Sliven'/*)*/, 'tag' => '0'),
    'BG-21' => array('country' => 'BG', 'title' => /*t(*/'Smolyan'/*)*/, 'tag' => '0'),
    'BG-22' => array(
      'country' => 'BG',
      'title' => /*t(*/'Sofia Region'/*)*/,
      'tag' => '0'
    ),
    'BG-23' => array('country' => 'BG', 'title' => /*t(*/'Sofia'/*)*/, 'tag' => '0'),
    'BG-24' => array(
      'country' => 'BG',
      'title' => /*t(*/'Stara Zagora'/*)*/,
      'tag' => '0'
    ),
    'BG-25' => array('country' => 'BG', 'title' => /*t(*/'Turgovishte'/*)*/, 'tag' => '0'),
    'BG-26' => array('country' => 'BG', 'title' => /*t(*/'Khaskovo'/*)*/, 'tag' => '0'),
    'BG-27' => array('country' => 'BG', 'title' => /*t(*/'Shumen'/*)*/, 'tag' => '0'),
    'BG-28' => array('country' => 'BG', 'title' => /*t(*/'Yambol'/*)*/, 'tag' => '0'),
    'BH-CAP' => array('country' => 'BH', 'title' => /*t(*/'Capital'/*)*/, 'tag' => '0'),
    'BH-CEN' => array('country' => 'BH', 'title' => /*t(*/'Central'/*)*/, 'tag' => '0'),
    'BH-MUH' => array('country' => 'BH', 'title' => /*t(*/'Muharraq'/*)*/, 'tag' => '0'),
    'BH-NOR' => array('country' => 'BH', 'title' => /*t(*/'Northern'/*)*/, 'tag' => '0'),
    'BH-SOU' => array('country' => 'BH', 'title' => /*t(*/'Southern'/*)*/, 'tag' => '0'),
    'BI-BB' => array('country' => 'BI', 'title' => /*t(*/'Bubanza'/*)*/, 'tag' => '0'),
    'BI-BJ' => array('country' => 'BI', 'title' => /*t(*/'Bujumbura'/*)*/, 'tag' => '0'),
    'BI-BR' => array('country' => 'BI', 'title' => /*t(*/'Bururi'/*)*/, 'tag' => '0'),
    'BI-CA' => array('country' => 'BI', 'title' => /*t(*/'Cankuzo'/*)*/, 'tag' => '0'),
    'BI-CI' => array('country' => 'BI', 'title' => /*t(*/'Cibitoke'/*)*/, 'tag' => '0'),
    'BI-GI' => array('country' => 'BI', 'title' => /*t(*/'Gitega'/*)*/, 'tag' => '0'),
    'BI-KI' => array('country' => 'BI', 'title' => /*t(*/'Kirundo'/*)*/, 'tag' => '0'),
    'BI-KR' => array('country' => 'BI', 'title' => /*t(*/'Karuzi'/*)*/, 'tag' => '0'),
    'BI-KY' => array('country' => 'BI', 'title' => /*t(*/'Kayanza'/*)*/, 'tag' => '0'),
    'BI-MA' => array('country' => 'BI', 'title' => /*t(*/'Makamba'/*)*/, 'tag' => '0'),
    'BI-MU' => array('country' => 'BI', 'title' => /*t(*/'Muramvya'/*)*/, 'tag' => '0'),
    'BI-MW' => array('country' => 'BI', 'title' => /*t(*/'Mwaro'/*)*/, 'tag' => '0'),
    'BI-MY' => array('country' => 'BI', 'title' => /*t(*/'Muyinga'/*)*/, 'tag' => '0'),
    'BI-NG' => array('country' => 'BI', 'title' => /*t(*/'Ngozi'/*)*/, 'tag' => '0'),
    'BI-RT' => array('country' => 'BI', 'title' => /*t(*/'Rutana'/*)*/, 'tag' => '0'),
    'BI-RY' => array('country' => 'BI', 'title' => /*t(*/'Ruyigi'/*)*/, 'tag' => '0'),
    'BJ-AK' => array('country' => 'BJ', 'title' => /*t(*/'Atakora'/*)*/, 'tag' => '0'),
    'BJ-AL' => array('country' => 'BJ', 'title' => /*t(*/'Alibori'/*)*/, 'tag' => '0'),
    'BJ-AQ' => array('country' => 'BJ', 'title' => /*t(*/'Atlantique'/*)*/, 'tag' => '0'),
    'BJ-BO' => array('country' => 'BJ', 'title' => /*t(*/'Borgou'/*)*/, 'tag' => '0'),
    'BJ-CO' => array('country' => 'BJ', 'title' => /*t(*/'Collines'/*)*/, 'tag' => '0'),
    'BJ-DO' => array('country' => 'BJ', 'title' => /*t(*/'Donga'/*)*/, 'tag' => '0'),
    'BJ-KO' => array('country' => 'BJ', 'title' => /*t(*/'Kouffo'/*)*/, 'tag' => '0'),
    'BJ-LI' => array('country' => 'BJ', 'title' => /*t(*/'Littoral'/*)*/, 'tag' => '0'),
    'BJ-MO' => array('country' => 'BJ', 'title' => /*t(*/'Mono'/*)*/, 'tag' => '0'),
    'BJ-OU' => array('country' => 'BJ', 'title' => /*t(*/'Oueme'/*)*/, 'tag' => '0'),
    'BJ-PL' => array('country' => 'BJ', 'title' => /*t(*/'Plateau'/*)*/, 'tag' => '0'),
    'BJ-ZO' => array('country' => 'BJ', 'title' => /*t(*/'Zou'/*)*/, 'tag' => '0'),
    'BM-DS' => array('country' => 'BM', 'title' => /*t(*/'Devonshire'/*)*/, 'tag' => '0'),
    'BM-GC' => array(
      'country' => 'BM',
      'title' => /*t(*/'Saint George City'/*)*/,
      'tag' => '0'
    ),
    'BM-HA' => array('country' => 'BM', 'title' => /*t(*/'Hamilton'/*)*/, 'tag' => '0'),
    'BM-HC' => array(
      'country' => 'BM',
      'title' => /*t(*/'Hamilton City'/*)*/,
      'tag' => '0'
    ),
    'BM-PB' => array('country' => 'BM', 'title' => /*t(*/'Pembroke'/*)*/, 'tag' => '0'),
    'BM-PG' => array('country' => 'BM', 'title' => /*t(*/'Paget'/*)*/, 'tag' => '0'),
    'BM-SA' => array('country' => 'BM', 'title' => /*t(*/'Sandys'/*)*/, 'tag' => '0'),
    'BM-SG' => array(
      'country' => 'BM',
      'title' => /*t(*/"Saint George's"/*)*/,
      'tag' => '0'
    ),
    'BM-SH' => array('country' => 'BM', 'title' => /*t(*/'Southampton'/*)*/, 'tag' => '0'),
    'BM-SM' => array('country' => 'BM', 'title' => /*t(*/"Smith's"/*)*/, 'tag' => '0'),
    'BM-WA' => array('country' => 'BM', 'title' => /*t(*/'Warwick'/*)*/, 'tag' => '0'),
    'BN-BEL' => array('country' => 'BN', 'title' => /*t(*/'Belait'/*)*/, 'tag' => '0'),
    'BN-BRM' => array(
      'country' => 'BN',
      'title' => /*t(*/'Brunei and Muara'/*)*/,
      'tag' => '0'
    ),
    'BN-TEM' => array('country' => 'BN', 'title' => /*t(*/'Temburong'/*)*/, 'tag' => '0'),
    'BN-TUT' => array('country' => 'BN', 'title' => /*t(*/'Tutong'/*)*/, 'tag' => '0'),
    'BO-BEN' => array(
      'country' => 'BO',
      'title' => /*t(*/'Departmento Beni'/*)*/,
      'tag' => '0'
    ),
    'BO-CHU' => array(
      'country' => 'BO',
      'title' => /*t(*/'Departmento Chuquisaca'/*)*/,
      'tag' => '0'
    ),
    'BO-COC' => array(
      'country' => 'BO',
      'title' => /*t(*/'Departmento Cochabamba'/*)*/,
      'tag' => '0'
    ),
    'BO-LPZ' => array(
      'country' => 'BO',
      'title' => /*t(*/'Departmento La Paz'/*)*/,
      'tag' => '0'
    ),
    'BO-ORU' => array(
      'country' => 'BO',
      'title' => /*t(*/'Departmento Oruro'/*)*/,
      'tag' => '0'
    ),
    'BO-PAN' => array(
      'country' => 'BO',
      'title' => /*t(*/'Departmento Pando'/*)*/,
      'tag' => '0'
    ),
    'BO-POT' => array(
      'country' => 'BO',
      'title' => /*t(*/'Departmento Potosi'/*)*/,
      'tag' => '0'
    ),
    'BO-SCZ' => array(
      'country' => 'BO',
      'title' => /*t(*/'Departmento Santa Cruz'/*)*/,
      'tag' => '0'
    ),
    'BO-TAR' => array(
      'country' => 'BO',
      'title' => /*t(*/'Departmento Tarija'/*)*/,
      'tag' => '0'
    ),
    'BR-AC' => array('country' => 'BR', 'title' => /*t(*/'Acre'/*)*/, 'tag' => '0'),
    'BR-AL' => array('country' => 'BR', 'title' => /*t(*/'Alagoas'/*)*/, 'tag' => '0'),
    'BR-AM' => array('country' => 'BR', 'title' => /*t(*/'Amazonas'/*)*/, 'tag' => '0'),
    'BR-AP' => array('country' => 'BR', 'title' => /*t(*/'Amapa'/*)*/, 'tag' => '0'),
    'BR-BA' => array('country' => 'BR', 'title' => /*t(*/'Bahia'/*)*/, 'tag' => '0'),
    'BR-CE' => array('country' => 'BR', 'title' => /*t(*/'Ceara'/*)*/, 'tag' => '0'),
    'BR-DF' => array(
      'country' => 'BR',
      'title' => /*t(*/'Distrito Federal'/*)*/,
      'tag' => '0'
    ),
    'BR-ES' => array(
      'country' => 'BR',
      'title' => /*t(*/'Espirito Santo'/*)*/,
      'tag' => '0'
    ),
    'BR-GO' => array('country' => 'BR', 'title' => /*t(*/'Goias'/*)*/, 'tag' => '0'),
    'BR-MA' => array('country' => 'BR', 'title' => /*t(*/'Maranhao'/*)*/, 'tag' => '0'),
    'BR-MG' => array(
      'country' => 'BR',
      'title' => /*t(*/'Minas Gerais'/*)*/,
      'tag' => '0'
    ),
    'BR-MS' => array(
      'country' => 'BR',
      'title' => /*t(*/'Mato Grosso do Sul'/*)*/,
      'tag' => '0'
    ),
    'BR-MT' => array('country' => 'BR', 'title' => /*t(*/'Mato Grosso'/*)*/, 'tag' => '0'),
    'BR-PA' => array('country' => 'BR', 'title' => /*t(*/'Para'/*)*/, 'tag' => '0'),
    'BR-PB' => array('country' => 'BR', 'title' => /*t(*/'Paraiba'/*)*/, 'tag' => '0'),
    'BR-PE' => array('country' => 'BR', 'title' => /*t(*/'Pernambuco'/*)*/, 'tag' => '0'),
    'BR-PI' => array('country' => 'BR', 'title' => /*t(*/'Piaui'/*)*/, 'tag' => '0'),
    'BR-PR' => array('country' => 'BR', 'title' => /*t(*/'Parana'/*)*/, 'tag' => '0'),
    'BR-RJ' => array(
      'country' => 'BR',
      'title' => /*t(*/'Rio de Janeiro'/*)*/,
      'tag' => '0'
    ),
    'BR-RN' => array(
      'country' => 'BR',
      'title' => /*t(*/'Rio Grande do Norte'/*)*/,
      'tag' => '0'
    ),
    'BR-RO' => array('country' => 'BR', 'title' => /*t(*/'Rondonia'/*)*/, 'tag' => '0'),
    'BR-RR' => array('country' => 'BR', 'title' => /*t(*/'Roraima'/*)*/, 'tag' => '0'),
    'BR-RS' => array(
      'country' => 'BR',
      'title' => /*t(*/'Rio Grande do Sul'/*)*/,
      'tag' => '0'
    ),
    'BR-SC' => array(
      'country' => 'BR',
      'title' => /*t(*/'Santa Catarina'/*)*/,
      'tag' => '0'
    ),
    'BR-SE' => array('country' => 'BR', 'title' => /*t(*/'Sergipe'/*)*/, 'tag' => '0'),
    'BR-SP' => array('country' => 'BR', 'title' => /*t(*/'Sao Paulo'/*)*/, 'tag' => '0'),
    'BR-TO' => array('country' => 'BR', 'title' => /*t(*/'Tocantins'/*)*/, 'tag' => '0'),
    'BS-ACK' => array('country' => 'BS', 'title' => /*t(*/'Acklins'/*)*/, 'tag' => '0'),
    'BS-BER' => array(
      'country' => 'BS',
      'title' => /*t(*/'Berry Islands'/*)*/,
      'tag' => '0'
    ),
    'BS-BIM' => array('country' => 'BS', 'title' => /*t(*/'Bimini'/*)*/, 'tag' => '0'),
    'BS-BLK' => array(
      'country' => 'BS',
      'title' => /*t(*/'Black Point'/*)*/,
      'tag' => '0'
    ),
    'BS-CAB' => array(
      'country' => 'BS',
      'title' => /*t(*/'Central Abaco'/*)*/,
      'tag' => '0'
    ),
    'BS-CAN' => array(
      'country' => 'BS',
      'title' => /*t(*/'Central Andros'/*)*/,
      'tag' => '0'
    ),
    'BS-CAT' => array('country' => 'BS', 'title' => /*t(*/'Cat Island'/*)*/, 'tag' => '0'),
    'BS-CEL' => array(
      'country' => 'BS',
      'title' => /*t(*/'Central Eleuthera'/*)*/,
      'tag' => '0'
    ),
    'BS-CRO' => array(
      'country' => 'BS',
      'title' => /*t(*/'Crooked Island'/*)*/,
      'tag' => '0'
    ),
    'BS-EGB' => array(
      'country' => 'BS',
      'title' => /*t(*/'East Grand Bahama'/*)*/,
      'tag' => '0'
    ),
    'BS-EXU' => array('country' => 'BS', 'title' => /*t(*/'Exuma'/*)*/, 'tag' => '0'),
    'BS-FRE' => array(
      'country' => 'BS',
      'title' => /*t(*/'City of Freeport'/*)*/,
      'tag' => '0'
    ),
    'BS-GRD' => array('country' => 'BS', 'title' => /*t(*/'Grand Cay'/*)*/, 'tag' => '0'),
    'BS-HAR' => array(
      'country' => 'BS',
      'title' => /*t(*/'Harbour Island'/*)*/,
      'tag' => '0'
    ),
    'BS-HOP' => array('country' => 'BS', 'title' => /*t(*/'Hope Town'/*)*/, 'tag' => '0'),
    'BS-INA' => array('country' => 'BS', 'title' => /*t(*/'Inagua'/*)*/, 'tag' => '0'),
    'BS-LNG' => array(
      'country' => 'BS',
      'title' => /*t(*/'Long Island'/*)*/,
      'tag' => '0'
    ),
    'BS-MAN' => array(
      'country' => 'BS',
      'title' => /*t(*/'Mangrove Cay'/*)*/,
      'tag' => '0'
    ),
    'BS-MAY' => array('country' => 'BS', 'title' => /*t(*/'Mayaguana'/*)*/, 'tag' => '0'),
    'BS-MOO' => array(
      'country' => 'BS',
      'title' => /*t(*/"Moore's Island"/*)*/,
      'tag' => '0'
    ),
    'BS-NAB' => array(
      'country' => 'BS',
      'title' => /*t(*/'North Abaco'/*)*/,
      'tag' => '0'
    ),
    'BS-NAN' => array(
      'country' => 'BS',
      'title' => /*t(*/'North Andros'/*)*/,
      'tag' => '0'
    ),
    'BS-NEL' => array(
      'country' => 'BS',
      'title' => /*t(*/'North Eleuthera'/*)*/,
      'tag' => '0'
    ),
    'BS-RAG' => array(
      'country' => 'BS',
      'title' => /*t(*/'Ragged Island'/*)*/,
      'tag' => '0'
    ),
    'BS-RUM' => array('country' => 'BS', 'title' => /*t(*/'Rum Cay'/*)*/, 'tag' => '0'),
    'BS-SAB' => array(
      'country' => 'BS',
      'title' => /*t(*/'South Abaco'/*)*/,
      'tag' => '0'
    ),
    'BS-SAL' => array(
      'country' => 'BS',
      'title' => /*t(*/'San Salvador'/*)*/,
      'tag' => '0'
    ),
    'BS-SAN' => array(
      'country' => 'BS',
      'title' => /*t(*/'South Andros'/*)*/,
      'tag' => '0'
    ),
    'BS-SEL' => array(
      'country' => 'BS',
      'title' => /*t(*/'South Eleuthera'/*)*/,
      'tag' => '0'
    ),
    'BS-SWE' => array(
      'country' => 'BS',
      'title' => /*t(*/'Spanish Wells'/*)*/,
      'tag' => '0'
    ),
    'BS-WGB' => array(
      'country' => 'BS',
      'title' => /*t(*/'West Grand Bahama'/*)*/,
      'tag' => '0'
    ),
    'BT-BUM' => array('country' => 'BT', 'title' => /*t(*/'Bumthang'/*)*/, 'tag' => '0'),
    'BT-CHU' => array('country' => 'BT', 'title' => /*t(*/'Chukha'/*)*/, 'tag' => '0'),
    'BT-DAG' => array('country' => 'BT', 'title' => /*t(*/'Dagana'/*)*/, 'tag' => '0'),
    'BT-GAS' => array('country' => 'BT', 'title' => /*t(*/'Gasa'/*)*/, 'tag' => '0'),
    'BT-HAA' => array('country' => 'BT', 'title' => /*t(*/'Haa'/*)*/, 'tag' => '0'),
    'BT-LHU' => array('country' => 'BT', 'title' => /*t(*/'Lhuntse'/*)*/, 'tag' => '0'),
    'BT-MON' => array('country' => 'BT', 'title' => /*t(*/'Mongar'/*)*/, 'tag' => '0'),
    'BT-PAR' => array('country' => 'BT', 'title' => /*t(*/'Paro'/*)*/, 'tag' => '0'),
    'BT-PEM' => array(
      'country' => 'BT',
      'title' => /*t(*/'Pemagatshel'/*)*/,
      'tag' => '0'
    ),
    'BT-PUN' => array('country' => 'BT', 'title' => /*t(*/'Punakha'/*)*/, 'tag' => '0'),
    'BT-SAR' => array('country' => 'BT', 'title' => /*t(*/'Sarpang'/*)*/, 'tag' => '0'),
    'BT-SAT' => array('country' => 'BT', 'title' => /*t(*/'Samtse'/*)*/, 'tag' => '0'),
    'BT-SJO' => array(
      'country' => 'BT',
      'title' => /*t(*/'Samdrup Jongkhar'/*)*/,
      'tag' => '0'
    ),
    'BT-THI' => array('country' => 'BT', 'title' => /*t(*/'Thimphu'/*)*/, 'tag' => '0'),
    'BT-TRG' => array('country' => 'BT', 'title' => /*t(*/'Trashigang'/*)*/, 'tag' => '0'),
    'BT-TRO' => array('country' => 'BT', 'title' => /*t(*/'Trongsa'/*)*/, 'tag' => '0'),
    'BT-TRY' => array(
      'country' => 'BT',
      'title' => /*t(*/'Trashiyangste'/*)*/,
      'tag' => '0'
    ),
    'BT-TSI' => array('country' => 'BT', 'title' => /*t(*/'Tsirang'/*)*/, 'tag' => '0'),
    'BT-WPH' => array(
      'country' => 'BT',
      'title' => /*t(*/'Wangdue Phodrang'/*)*/,
      'tag' => '0'
    ),
    'BT-ZHE' => array('country' => 'BT', 'title' => /*t(*/'Zhemgang'/*)*/, 'tag' => '0'),
    'BW-CE' => array('country' => 'BW', 'title' => /*t(*/'Central'/*)*/, 'tag' => '0'),
    'BW-GH' => array('country' => 'BW', 'title' => /*t(*/'Ghanzi'/*)*/, 'tag' => '0'),
    'BW-KD' => array('country' => 'BW', 'title' => /*t(*/'Kgalagadi'/*)*/, 'tag' => '0'),
    'BW-KT' => array('country' => 'BW', 'title' => /*t(*/'Kgatleng'/*)*/, 'tag' => '0'),
    'BW-KW' => array('country' => 'BW', 'title' => /*t(*/'Kweneng'/*)*/, 'tag' => '0'),
    'BW-NE' => array('country' => 'BW', 'title' => /*t(*/'North East'/*)*/, 'tag' => '0'),
    'BW-NG' => array('country' => 'BW', 'title' => /*t(*/'Ngamiland'/*)*/, 'tag' => '0'),
    'BW-NW' => array('country' => 'BW', 'title' => /*t(*/'North West'/*)*/, 'tag' => '0'),
    'BW-SE' => array('country' => 'BW', 'title' => /*t(*/'South East'/*)*/, 'tag' => '0'),
    'BW-SO' => array('country' => 'BW', 'title' => /*t(*/'Southern'/*)*/, 'tag' => '0'),
    'BY-BR' => array(
      'country' => 'BY',
      'title' => /*t(*/'Brest voblast'/*)*/,
      'tag' => '0'
    ),
    'BY-HO' => array(
      'country' => 'BY',
      'title' => /*t(*/'Homyel voblast'/*)*/,
      'tag' => '0'
    ),
    'BY-HR' => array(
      'country' => 'BY',
      'title' => /*t(*/'Hrodna voblast'/*)*/,
      'tag' => '0'
    ),
    'BY-MA' => array(
      'country' => 'BY',
      'title' => /*t(*/'Mahilyow voblast'/*)*/,
      'tag' => '0'
    ),
    'BY-MI' => array(
      'country' => 'BY',
      'title' => /*t(*/'Minsk voblast'/*)*/,
      'tag' => '0'
    ),
    'BY-VI' => array(
      'country' => 'BY',
      'title' => /*t(*/'Vitsebsk voblast'/*)*/,
      'tag' => '0'
    ),
    'BZ-BZ' => array(
      'country' => 'BZ',
      'title' => /*t(*/'Belize District'/*)*/,
      'tag' => '0'
    ),
    'BZ-CR' => array(
      'country' => 'BZ',
      'title' => /*t(*/'Corozal District'/*)*/,
      'tag' => '0'
    ),
    'BZ-CY' => array(
      'country' => 'BZ',
      'title' => /*t(*/'Cayo District'/*)*/,
      'tag' => '0'
    ),
    'BZ-OW' => array(
      'country' => 'BZ',
      'title' => /*t(*/'Orange Walk District'/*)*/,
      'tag' => '0'
    ),
    'BZ-SC' => array(
      'country' => 'BZ',
      'title' => /*t(*/'Stann Creek District'/*)*/,
      'tag' => '0'
    ),
    'BZ-TO' => array(
      'country' => 'BZ',
      'title' => /*t(*/'Toledo District'/*)*/,
      'tag' => '0'
    ),
    'CA-AB' => array('country' => 'CA', 'title' => /*t(*/'Alberta'/*)*/, 'tag' => '0'),
    'CA-BC' => array(
      'country' => 'CA',
      'title' => /*t(*/'British Columbia'/*)*/,
      'tag' => '0'
    ),
    'CA-MB' => array('country' => 'CA', 'title' => /*t(*/'Manitoba'/*)*/, 'tag' => '0'),
    'CA-NB' => array(
      'country' => 'CA',
      'title' => /*t(*/'New Brunswick'/*)*/,
      'tag' => '0'
    ),
    'CA-NL' => array(
      'country' => 'CA',
      'title' => /*t(*/'Newfoundland and Labrador'/*)*/,
      'tag' => '0'
    ),
    'CA-NS' => array('country' => 'CA', 'title' => /*t(*/'Nova Scotia'/*)*/, 'tag' => '0'),
    'CA-NT' => array(
      'country' => 'CA',
      'title' => /*t(*/'Northwest Territories'/*)*/,
      'tag' => '0'
    ),
    'CA-NU' => array('country' => 'CA', 'title' => /*t(*/'Nunavut'/*)*/, 'tag' => '0'),
    'CA-ON' => array('country' => 'CA', 'title' => /*t(*/'Ontario'/*)*/, 'tag' => '0'),
    'CA-PE' => array(
      'country' => 'CA',
      'title' => /*t(*/'Prince Edward Island'/*)*/,
      'tag' => '0'
    ),
    'CA-QC' => array('country' => 'CA', 'title' => /*t(*/'Quebec'/*)*/, 'tag' => '0'),
    'CA-SK' => array(
      'country' => 'CA',
      'title' => /*t(*/'Saskatchewan'/*)*/,
      'tag' => '0'
    ),
    'CA-YT' => array(
      'country' => 'CA',
      'title' => /*t(*/'Yukon Territory'/*)*/,
      'tag' => '0'
    ),
    'CC-D' => array(
      'country' => 'CC',
      'title' => /*t(*/'Direction Island'/*)*/,
      'tag' => '0'
    ),
    'CC-H' => array('country' => 'CC', 'title' => /*t(*/'Home Island'/*)*/, 'tag' => '0'),
    'CC-O' => array(
      'country' => 'CC',
      'title' => /*t(*/'Horsburgh Island'/*)*/,
      'tag' => '0'
    ),
    'CC-S' => array('country' => 'CC', 'title' => /*t(*/'South Island'/*)*/, 'tag' => '0'),
    'CC-W' => array('country' => 'CC', 'title' => /*t(*/'West Island'/*)*/, 'tag' => '0'),
    'CD-BC' => array('country' => 'CD', 'title' => /*t(*/'Bas-Congo'/*)*/, 'tag' => '0'),
    'CD-BN' => array('country' => 'CD', 'title' => /*t(*/'Bandundu'/*)*/, 'tag' => '0'),
    'CD-EQ' => array('country' => 'CD', 'title' => /*t(*/'Equateur'/*)*/, 'tag' => '0'),
    'CD-KA' => array('country' => 'CD', 'title' => /*t(*/'Katanga'/*)*/, 'tag' => '0'),
    'CD-KE' => array(
      'country' => 'CD',
      'title' => /*t(*/'Kasai-Oriental'/*)*/,
      'tag' => '0'
    ),
    'CD-KN' => array('country' => 'CD', 'title' => /*t(*/'Kinshasa'/*)*/, 'tag' => '0'),
    'CD-KW' => array(
      'country' => 'CD',
      'title' => /*t(*/'Kasai-Occidental'/*)*/,
      'tag' => '0'
    ),
    'CD-MA' => array('country' => 'CD', 'title' => /*t(*/'Maniema'/*)*/, 'tag' => '0'),
    'CD-NK' => array('country' => 'CD', 'title' => /*t(*/'Nord-Kivu'/*)*/, 'tag' => '0'),
    'CD-OR' => array('country' => 'CD', 'title' => /*t(*/'Orientale'/*)*/, 'tag' => '0'),
    'CD-SK' => array('country' => 'CD', 'title' => /*t(*/'Sud-Kivu'/*)*/, 'tag' => '0'),
    'CF-BAN' => array('country' => 'CF', 'title' => /*t(*/'Bangui'/*)*/, 'tag' => '0'),
    'CF-BBA' => array(
      'country' => 'CF',
      'title' => /*t(*/'Bamingui-Bangoran'/*)*/,
      'tag' => '0'
    ),
    'CF-BKO' => array(
      'country' => 'CF',
      'title' => /*t(*/'Basse-Kotto'/*)*/,
      'tag' => '0'
    ),
    'CF-HKO' => array(
      'country' => 'CF',
      'title' => /*t(*/'Haute-Kotto'/*)*/,
      'tag' => '0'
    ),
    'CF-HMB' => array(
      'country' => 'CF',
      'title' => /*t(*/'Haut-Mbomou'/*)*/,
      'tag' => '0'
    ),
    'CF-KEM' => array('country' => 'CF', 'title' => /*t(*/'Kemo'/*)*/, 'tag' => '0'),
    'CF-LOB' => array('country' => 'CF', 'title' => /*t(*/'Lobaye'/*)*/, 'tag' => '0'),
    'CF-MBO' => array('country' => 'CF', 'title' => /*t(*/'Mbomou'/*)*/, 'tag' => '0'),
    'CF-MKD' => array(
      'country' => 'CF',
      'title' => /*t(*/'Mambere-Kadei'/*)*/,
      'tag' => '0'
    ),
    'CF-NGR' => array(
      'country' => 'CF',
      'title' => /*t(*/'Nana-Grebizi'/*)*/,
      'tag' => '0'
    ),
    'CF-NMM' => array(
      'country' => 'CF',
      'title' => /*t(*/'Nana-Mambere'/*)*/,
      'tag' => '0'
    ),
    'CF-OMP' => array(
      'country' => 'CF',
      'title' => /*t(*/"Ombella-M'Poko"/*)*/,
      'tag' => '0'
    ),
    'CF-OPE' => array(
      'country' => 'CF',
      'title' => /*t(*/'Ouham-Pende'/*)*/,
      'tag' => '0'
    ),
    'CF-OUH' => array('country' => 'CF', 'title' => /*t(*/'Ouham'/*)*/, 'tag' => '0'),
    'CF-OUK' => array('country' => 'CF', 'title' => /*t(*/'Ouaka'/*)*/, 'tag' => '0'),
    'CF-SMB' => array(
      'country' => 'CF',
      'title' => /*t(*/'Sangha-Mbaere'/*)*/,
      'tag' => '0'
    ),
    'CF-VAK' => array('country' => 'CF', 'title' => /*t(*/'Vakaga'/*)*/, 'tag' => '0'),
    'CG-BO' => array('country' => 'CG', 'title' => /*t(*/'Bouenza'/*)*/, 'tag' => '0'),
    'CG-BR' => array('country' => 'CG', 'title' => /*t(*/'Brazzaville'/*)*/, 'tag' => '0'),
    'CG-CO' => array(
      'country' => 'CG',
      'title' => /*t(*/'Cuvette-Ouest'/*)*/,
      'tag' => '0'
    ),
    'CG-CU' => array('country' => 'CG', 'title' => /*t(*/'Cuvette'/*)*/, 'tag' => '0'),
    'CG-KO' => array('country' => 'CG', 'title' => /*t(*/'Kouilou'/*)*/, 'tag' => '0'),
    'CG-LE' => array('country' => 'CG', 'title' => /*t(*/'Lekoumou'/*)*/, 'tag' => '0'),
    'CG-LI' => array('country' => 'CG', 'title' => /*t(*/'Likouala'/*)*/, 'tag' => '0'),
    'CG-NI' => array('country' => 'CG', 'title' => /*t(*/'Niari'/*)*/, 'tag' => '0'),
    'CG-PL' => array('country' => 'CG', 'title' => /*t(*/'Plateaux'/*)*/, 'tag' => '0'),
    'CG-PO' => array('country' => 'CG', 'title' => /*t(*/'Pool'/*)*/, 'tag' => '0'),
    'CG-SA' => array('country' => 'CG', 'title' => /*t(*/'Sangha'/*)*/, 'tag' => '0'),
    'CH-AG' => array('country' => 'CH', 'title' => /*t(*/'Aargau'/*)*/, 'tag' => '0'),
    'CH-AI' => array(
      'country' => 'CH',
      'title' => /*t(*/'Appenzell Innerhoden'/*)*/,
      'tag' => '0'
    ),
    'CH-AR' => array(
      'country' => 'CH',
      'title' => /*t(*/'Appenzell Ausserrhoden'/*)*/,
      'tag' => '0'
    ),
    'CH-BE' => array('country' => 'CH', 'title' => /*t(*/'Bern'/*)*/, 'tag' => '0'),
    'CH-BL' => array(
      'country' => 'CH',
      'title' => /*t(*/'Basel-Landschaft'/*)*/,
      'tag' => '0'
    ),
    'CH-BS' => array('country' => 'CH', 'title' => /*t(*/'Basel-Stadt'/*)*/, 'tag' => '0'),
    'CH-FR' => array('country' => 'CH', 'title' => /*t(*/'Fribourg'/*)*/, 'tag' => '0'),
    'CH-GE' => array('country' => 'CH', 'title' => /*t(*/'Geneva'/*)*/, 'tag' => '0'),
    'CH-GL' => array('country' => 'CH', 'title' => /*t(*/'Glarus'/*)*/, 'tag' => '0'),
    'CH-GR' => array('country' => 'CH', 'title' => /*t(*/'Graubunden'/*)*/, 'tag' => '0'),
    'CH-JU' => array('country' => 'CH', 'title' => /*t(*/'Jura'/*)*/, 'tag' => '0'),
    'CH-LU' => array('country' => 'CH', 'title' => /*t(*/'Lucerne'/*)*/, 'tag' => '0'),
    'CH-NE' => array(
      'country' => 'CH',
      'title' => /*t(*/'Neuchatel'/*)*/,
      'tag' => '0'
    ),
    'CH-NW' => array('country' => 'CH', 'title' => /*t(*/'Nidwalden'/*)*/, 'tag' => '0'),
    'CH-OW' => array('country' => 'CH', 'title' => /*t(*/'Obwalden'/*)*/, 'tag' => '0'),
    'CH-SG' => array('country' => 'CH', 'title' => /*t(*/'St. Gallen'/*)*/, 'tag' => '0'),
    'CH-SH' => array(
      'country' => 'CH',
      'title' => /*t(*/'Schaffhausen'/*)*/,
      'tag' => '0'
    ),
    'CH-SO' => array('country' => 'CH', 'title' => /*t(*/'Solothurn'/*)*/, 'tag' => '0'),
    'CH-SZ' => array('country' => 'CH', 'title' => /*t(*/'Schwyz'/*)*/, 'tag' => '0'),
    'CH-TG' => array('country' => 'CH', 'title' => /*t(*/'Thurgau'/*)*/, 'tag' => '0'),
    'CH-TI' => array('country' => 'CH', 'title' => /*t(*/'Ticino'/*)*/, 'tag' => '0'),
    'CH-UR' => array('country' => 'CH', 'title' => /*t(*/'Uri'/*)*/, 'tag' => '0'),
    'CH-VD' => array('country' => 'CH', 'title' => /*t(*/'Vaud'/*)*/, 'tag' => '0'),
    'CH-VS' => array('country' => 'CH', 'title' => /*t(*/'Valais'/*)*/, 'tag' => '0'),
    'CH-ZG' => array('country' => 'CH', 'title' => /*t(*/'Zug'/*)*/, 'tag' => '0'),
    'CH-ZH' => array('country' => 'CH', 'title' => /*t(*/'Zurich'/*)*/, 'tag' => '0'),
    'CI-ABE' => array('country' => 'CI', 'title' => /*t(*/'Abengourou'/*)*/, 'tag' => '0'),
    'CI-ABI' => array('country' => 'CI', 'title' => /*t(*/'Abidjan'/*)*/, 'tag' => '0'),
    'CI-ABO' => array('country' => 'CI', 'title' => /*t(*/'Aboisso'/*)*/, 'tag' => '0'),
    'CI-ADI' => array('country' => 'CI', 'title' => /*t(*/'Adiake'/*)*/, 'tag' => '0'),
    'CI-ADZ' => array('country' => 'CI', 'title' => /*t(*/'Adzope'/*)*/, 'tag' => '0'),
    'CI-AGB' => array('country' => 'CI', 'title' => /*t(*/'Agboville'/*)*/, 'tag' => '0'),
    'CI-AGN' => array(
      'country' => 'CI',
      'title' => /*t(*/'Agnibilekrou'/*)*/,
      'tag' => '0'
    ),
    'CI-ALE' => array('country' => 'CI', 'title' => /*t(*/'Alepe'/*)*/, 'tag' => '0'),
    'CI-BAN' => array('country' => 'CI', 'title' => /*t(*/'Bangolo'/*)*/, 'tag' => '0'),
    'CI-BDK' => array('country' => 'CI', 'title' => /*t(*/'Bondoukou'/*)*/, 'tag' => '0'),
    'CI-BDL' => array('country' => 'CI', 'title' => /*t(*/'Boundiali'/*)*/, 'tag' => '0'),
    'CI-BEO' => array('country' => 'CI', 'title' => /*t(*/'Beoumi'/*)*/, 'tag' => '0'),
    'CI-BFL' => array('country' => 'CI', 'title' => /*t(*/'Bouafle'/*)*/, 'tag' => '0'),
    'CI-BGN' => array('country' => 'CI', 'title' => /*t(*/'Bongouanou'/*)*/, 'tag' => '0'),
    'CI-BIA' => array('country' => 'CI', 'title' => /*t(*/'Biankouma'/*)*/, 'tag' => '0'),
    'CI-BKE' => array('country' => 'CI', 'title' => /*t(*/'Bouake'/*)*/, 'tag' => '0'),
    'CI-BNA' => array('country' => 'CI', 'title' => /*t(*/'Bouna'/*)*/, 'tag' => '0'),
    'CI-BOC' => array('country' => 'CI', 'title' => /*t(*/'Bocanda'/*)*/, 'tag' => '0'),
    'CI-DAL' => array('country' => 'CI', 'title' => /*t(*/'Daloa'/*)*/, 'tag' => '0'),
    'CI-DAN' => array('country' => 'CI', 'title' => /*t(*/'Danane'/*)*/, 'tag' => '0'),
    'CI-DAO' => array('country' => 'CI', 'title' => /*t(*/'Daoukro'/*)*/, 'tag' => '0'),
    'CI-DBU' => array('country' => 'CI', 'title' => /*t(*/'Dabou'/*)*/, 'tag' => '0'),
    'CI-DIM' => array('country' => 'CI', 'title' => /*t(*/'Dimbokro'/*)*/, 'tag' => '0'),
    'CI-DIV' => array('country' => 'CI', 'title' => /*t(*/'Divo'/*)*/, 'tag' => '0'),
    'CI-DKL' => array('country' => 'CI', 'title' => /*t(*/'Dabakala'/*)*/, 'tag' => '0'),
    'CI-DUE' => array('country' => 'CI', 'title' => /*t(*/'Duekoue'/*)*/, 'tag' => '0'),
    'CI-FER' => array(
      'country' => 'CI',
      'title' => /*t(*/'Ferkessedougou'/*)*/,
      'tag' => '0'
    ),
    'CI-GAG' => array('country' => 'CI', 'title' => /*t(*/'Gagnoa'/*)*/, 'tag' => '0'),
    'CI-GBA' => array(
      'country' => 'CI',
      'title' => /*t(*/'Grand-Bassam'/*)*/,
      'tag' => '0'
    ),
    'CI-GLA' => array(
      'country' => 'CI',
      'title' => /*t(*/'Grand-Lahou'/*)*/,
      'tag' => '0'
    ),
    'CI-GUI' => array('country' => 'CI', 'title' => /*t(*/'Guiglo'/*)*/, 'tag' => '0'),
    'CI-ISS' => array('country' => 'CI', 'title' => /*t(*/'Issia'/*)*/, 'tag' => '0'),
    'CI-JAC' => array(
      'country' => 'CI',
      'title' => /*t(*/'Jacqueville'/*)*/,
      'tag' => '0'
    ),
    'CI-KAT' => array('country' => 'CI', 'title' => /*t(*/'Katiola'/*)*/, 'tag' => '0'),
    'CI-KOR' => array('country' => 'CI', 'title' => /*t(*/'Korhogo'/*)*/, 'tag' => '0'),
    'CI-LAK' => array('country' => 'CI', 'title' => /*t(*/'Lakota'/*)*/, 'tag' => '0'),
    'CI-MAN' => array('country' => 'CI', 'title' => /*t(*/'Man'/*)*/, 'tag' => '0'),
    'CI-MBA' => array('country' => 'CI', 'title' => /*t(*/'Mbahiakro'/*)*/, 'tag' => '0'),
    'CI-MKN' => array('country' => 'CI', 'title' => /*t(*/'Mankono'/*)*/, 'tag' => '0'),
    'CI-ODI' => array('country' => 'CI', 'title' => /*t(*/'Odienne'/*)*/, 'tag' => '0'),
    'CI-OUM' => array('country' => 'CI', 'title' => /*t(*/'Oume'/*)*/, 'tag' => '0'),
    'CI-SAK' => array('country' => 'CI', 'title' => /*t(*/'Sakassou'/*)*/, 'tag' => '0'),
    'CI-SAS' => array('country' => 'CI', 'title' => /*t(*/'Sassandra'/*)*/, 'tag' => '0'),
    'CI-SEG' => array('country' => 'CI', 'title' => /*t(*/'Seguela'/*)*/, 'tag' => '0'),
    'CI-SIN' => array('country' => 'CI', 'title' => /*t(*/'Sinfra'/*)*/, 'tag' => '0'),
    'CI-SOU' => array('country' => 'CI', 'title' => /*t(*/'Soubre'/*)*/, 'tag' => '0'),
    'CI-SPE' => array('country' => 'CI', 'title' => /*t(*/'San-Pedro'/*)*/, 'tag' => '0'),
    'CI-TAB' => array('country' => 'CI', 'title' => /*t(*/'Tabou'/*)*/, 'tag' => '0'),
    'CI-TAN' => array('country' => 'CI', 'title' => /*t(*/'Tanda'/*)*/, 'tag' => '0'),
    'CI-TBA' => array('country' => 'CI', 'title' => /*t(*/'Touba'/*)*/, 'tag' => '0'),
    'CI-TIA' => array('country' => 'CI', 'title' => /*t(*/'Tiassale'/*)*/, 'tag' => '0'),
    'CI-TIE' => array('country' => 'CI', 'title' => /*t(*/'Tiebissou'/*)*/, 'tag' => '0'),
    'CI-TIN' => array('country' => 'CI', 'title' => /*t(*/'Tingrela'/*)*/, 'tag' => '0'),
    'CI-TLP' => array('country' => 'CI', 'title' => /*t(*/'Toulepleu'/*)*/, 'tag' => '0'),
    'CI-TMD' => array('country' => 'CI', 'title' => /*t(*/'Toumodi'/*)*/, 'tag' => '0'),
    'CI-VAV' => array('country' => 'CI', 'title' => /*t(*/'Vavoua'/*)*/, 'tag' => '0'),
    'CI-YAM' => array(
      'country' => 'CI',
      'title' => /*t(*/'Yamoussoukro'/*)*/,
      'tag' => '0'
    ),
    'CI-ZUE' => array('country' => 'CI', 'title' => /*t(*/'Zuenoula'/*)*/, 'tag' => '0'),
    'CK-AI' => array('country' => 'CK', 'title' => /*t(*/'Aitutaki'/*)*/, 'tag' => '0'),
    'CK-AT' => array('country' => 'CK', 'title' => /*t(*/'Atiu'/*)*/, 'tag' => '0'),
    'CK-MA' => array('country' => 'CK', 'title' => /*t(*/'Manuae'/*)*/, 'tag' => '0'),
    'CK-MG' => array('country' => 'CK', 'title' => /*t(*/'Mangaia'/*)*/, 'tag' => '0'),
    'CK-MK' => array('country' => 'CK', 'title' => /*t(*/'Manihiki'/*)*/, 'tag' => '0'),
    'CK-MT' => array('country' => 'CK', 'title' => /*t(*/'Mitiaro'/*)*/, 'tag' => '0'),
    'CK-MU' => array('country' => 'CK', 'title' => /*t(*/'Mauke'/*)*/, 'tag' => '0'),
    'CK-NI' => array(
      'country' => 'CK',
      'title' => /*t(*/'Nassau Island'/*)*/,
      'tag' => '0'
    ),
    'CK-PA' => array('country' => 'CK', 'title' => /*t(*/'Palmerston'/*)*/, 'tag' => '0'),
    'CK-PE' => array('country' => 'CK', 'title' => /*t(*/'Penrhyn'/*)*/, 'tag' => '0'),
    'CK-PU' => array('country' => 'CK', 'title' => /*t(*/'Pukapuka'/*)*/, 'tag' => '0'),
    'CK-RK' => array('country' => 'CK', 'title' => /*t(*/'Rakahanga'/*)*/, 'tag' => '0'),
    'CK-RR' => array('country' => 'CK', 'title' => /*t(*/'Rarotonga'/*)*/, 'tag' => '0'),
    'CK-SU' => array('country' => 'CK', 'title' => /*t(*/'Surwarrow'/*)*/, 'tag' => '0'),
    'CK-TA' => array('country' => 'CK', 'title' => /*t(*/'Takutea'/*)*/, 'tag' => '0'),
    'CL-AI' => array(
      'country' => 'CL',
      'title' => /*t(*/'Aisen del General Carlos Ibanez del Campo (XI)'/*)*/,
      'tag' => '0'
    ),
    'CL-AN' => array(
      'country' => 'CL',
      'title' => /*t(*/'Antofagasta (II)'/*)*/,
      'tag' => '0'
    ),
    'CL-AR' => array(
      'country' => 'CL',
      'title' => /*t(*/'Araucania (IX)'/*)*/,
      'tag' => '0'
    ),
    'CL-AT' => array(
      'country' => 'CL',
      'title' => /*t(*/'Atacama (III)'/*)*/,
      'tag' => '0'
    ),
    'CL-BI' => array(
      'country' => 'CL',
      'title' => /*t(*/'Bio-Bio (VIII)'/*)*/,
      'tag' => '0'
    ),
    'CL-CO' => array(
      'country' => 'CL',
      'title' => /*t(*/'Coquimbo (IV)'/*)*/,
      'tag' => '0'
    ),
    'CL-LI' => array(
      'country' => 'CL',
      'title' => /*t(*/"Libertador General Bernardo O'Higgins (VI)"/*)*/,
      'tag' => '0'
    ),
    'CL-LL' => array(
      'country' => 'CL',
      'title' => /*t(*/'Los Lagos (X)'/*)*/,
      'tag' => '0'
    ),
    'CL-MA' => array(
      'country' => 'CL',
      'title' => /*t(*/'Magallanes (XII)'/*)*/,
      'tag' => '0'
    ),
    'CL-ML' => array('country' => 'CL', 'title' => /*t(*/'Maule (VII)'/*)*/, 'tag' => '0'),
    'CL-RM' => array(
      'country' => 'CL',
      'title' => /*t(*/'Region Metropolitana (RM)'/*)*/,
      'tag' => '0'
    ),
    'CL-TA' => array(
      'country' => 'CL',
      'title' => /*t(*/'Tarapaca (I)'/*)*/,
      'tag' => '0'
    ),
    'CL-VS' => array(
      'country' => 'CL',
      'title' => /*t(*/'Valparaiso (V)'/*)*/,
      'tag' => '0'
    ),
    'CM-ADA' => array(
      'country' => 'CM',
      'title' => /*t(*/'Adamawa Province (Adamaoua)'/*)*/,
      'tag' => '0'
    ),
    'CM-CEN' => array(
      'country' => 'CM',
      'title' => /*t(*/'Centre Province'/*)*/,
      'tag' => '0'
    ),
    'CM-EST' => array(
      'country' => 'CM',
      'title' => /*t(*/'East Province (Est)'/*)*/,
      'tag' => '0'
    ),
    'CM-EXN' => array(
      'country' => 'CM',
      'title' => /*t(*/'Extreme North Province (Extreme-Nord)'/*)*/,
      'tag' => '0'
    ),
    'CM-LIT' => array(
      'country' => 'CM',
      'title' => /*t(*/'Littoral Province'/*)*/,
      'tag' => '0'
    ),
    'CM-NOR' => array(
      'country' => 'CM',
      'title' => /*t(*/'North Province (Nord)'/*)*/,
      'tag' => '0'
    ),
    'CM-NOT' => array(
      'country' => 'CM',
      'title' => /*t(*/'Northwest Province (Nord-Ouest)'/*)*/,
      'tag' => '0'
    ),
    'CM-OUE' => array(
      'country' => 'CM',
      'title' => /*t(*/'West Province (Ouest)'/*)*/,
      'tag' => '0'
    ),
    'CM-SOU' => array(
      'country' => 'CM',
      'title' => /*t(*/'Southwest Province (Sud-Ouest).'/*)*/,
      'tag' => '0'
    ),
    'CM-SUD' => array(
      'country' => 'CM',
      'title' => /*t(*/'South Province (Sud)'/*)*/,
      'tag' => '0'
    ),
    'CN-11' => array('country' => 'CN', 'title' => /*t(*/'Beijing'/*)*/, 'tag' => '0'),
    'CN-12' => array('country' => 'CN', 'title' => /*t(*/'Tianjin'/*)*/, 'tag' => '0'),
    'CN-13' => array('country' => 'CN', 'title' => /*t(*/'Hebei'/*)*/, 'tag' => '0'),
    'CN-14' => array('country' => 'CN', 'title' => /*t(*/'Shanxi'/*)*/, 'tag' => '0'),
    'CN-15' => array('country' => 'CN', 'title' => /*t(*/'Nei Mongol'/*)*/, 'tag' => '0'),
    'CN-21' => array('country' => 'CN', 'title' => /*t(*/'Liaoning'/*)*/, 'tag' => '0'),
    'CN-22' => array('country' => 'CN', 'title' => /*t(*/'Jilin'/*)*/, 'tag' => '0'),
    'CN-23' => array(
      'country' => 'CN',
      'title' => /*t(*/'Heilongjiang'/*)*/,
      'tag' => '0'
    ),
    'CN-31' => array('country' => 'CN', 'title' => /*t(*/'Shanghai'/*)*/, 'tag' => '0'),
    'CN-32' => array('country' => 'CN', 'title' => /*t(*/'Jiangsu'/*)*/, 'tag' => '0'),
    'CN-33' => array('country' => 'CN', 'title' => /*t(*/'Zhejiang'/*)*/, 'tag' => '0'),
    'CN-34' => array('country' => 'CN', 'title' => /*t(*/'Anhui'/*)*/, 'tag' => '0'),
    'CN-35' => array('country' => 'CN', 'title' => /*t(*/'Fujian'/*)*/, 'tag' => '0'),
    'CN-36' => array('country' => 'CN', 'title' => /*t(*/'Jiangxi'/*)*/, 'tag' => '0'),
    'CN-37' => array('country' => 'CN', 'title' => /*t(*/'Shandong'/*)*/, 'tag' => '0'),
    'CN-41' => array('country' => 'CN', 'title' => /*t(*/'Henan'/*)*/, 'tag' => '0'),
    'CN-42' => array('country' => 'CN', 'title' => /*t(*/'Hubei'/*)*/, 'tag' => '0'),
    'CN-43' => array('country' => 'CN', 'title' => /*t(*/'Hunan'/*)*/, 'tag' => '0'),
    'CN-44' => array('country' => 'CN', 'title' => /*t(*/'Guangdong'/*)*/, 'tag' => '0'),
    'CN-45' => array('country' => 'CN', 'title' => /*t(*/'Guangxi'/*)*/, 'tag' => '0'),
    'CN-46' => array('country' => 'CN', 'title' => /*t(*/'Hainan'/*)*/, 'tag' => '0'),
    'CN-51' => array('country' => 'CN', 'title' => /*t(*/'Sichuan'/*)*/, 'tag' => '0'),
    'CN-52' => array('country' => 'CN', 'title' => /*t(*/'Guizhou'/*)*/, 'tag' => '0'),
    'CN-53' => array('country' => 'CN', 'title' => /*t(*/'Yunnan'/*)*/, 'tag' => '0'),
    'CN-54' => array(
      'country' => 'CN',
      'title' => /*t(*/'Xizang Zizhiqu (Tibet)'/*)*/,
      'tag' => '0'
    ),
    'CN-61' => array('country' => 'CN', 'title' => /*t(*/'Shaanxi'/*)*/, 'tag' => '0'),
    'CN-62' => array('country' => 'CN', 'title' => /*t(*/'Gansu'/*)*/, 'tag' => '0'),
    'CN-63' => array('country' => 'CN', 'title' => /*t(*/'Qinghai'/*)*/, 'tag' => '0'),
    'CN-64' => array('country' => 'CN', 'title' => /*t(*/'Ningxia'/*)*/, 'tag' => '0'),
    'CN-65' => array('country' => 'CN', 'title' => /*t(*/'Xinjiang'/*)*/, 'tag' => '0'),
    'CN-71' => array('country' => 'CN', 'title' => /*t(*/'Taiwan'/*)*/, 'tag' => '0'),
    'CN-91' => array('country' => 'CN', 'title' => /*t(*/'Xianggang'/*)*/, 'tag' => '0'),
    'CN-92' => array('country' => 'CN', 'title' => /*t(*/'Aomen'/*)*/, 'tag' => '0'),
    'CN-97' => array(
      'country' => 'CN',
      'title' => /*t(*/'Chongqing'/*)*/,
      'tag' => '0'
    ),
    'CN-98' => array('country' => 'CN', 'title' => /*t(*/'Gaoxiong'/*)*/, 'tag' => '0'),
    'CN-99' => array('country' => 'CN', 'title' => /*t(*/'Taibei'/*)*/, 'tag' => '0'),
    'CO-AMZ' => array('country' => 'CO', 'title' => /*t(*/'Amazonas'/*)*/, 'tag' => '0'),
    'CO-ANT' => array('country' => 'CO', 'title' => /*t(*/'Antioquia'/*)*/, 'tag' => '0'),
    'CO-ARA' => array('country' => 'CO', 'title' => /*t(*/'Arauca'/*)*/, 'tag' => '0'),
    'CO-ATL' => array('country' => 'CO', 'title' => /*t(*/'Atlantico'/*)*/, 'tag' => '0'),
    'CO-BDC' => array(
      'country' => 'CO',
      'title' => /*t(*/'Bogota D.C.'/*)*/,
      'tag' => '0'
    ),
    'CO-BOL' => array('country' => 'CO', 'title' => /*t(*/'Bolivar'/*)*/, 'tag' => '0'),
    'CO-BOY' => array('country' => 'CO', 'title' => /*t(*/'Boyaca'/*)*/, 'tag' => '0'),
    'CO-CAL' => array('country' => 'CO', 'title' => /*t(*/'Caldas'/*)*/, 'tag' => '0'),
    'CO-CAM' => array(
      'country' => 'CO',
      'title' => /*t(*/'Cundinamarca'/*)*/,
      'tag' => '0'
    ),
    'CO-CAQ' => array('country' => 'CO', 'title' => /*t(*/'Caqueta'/*)*/, 'tag' => '0'),
    'CO-CAS' => array('country' => 'CO', 'title' => /*t(*/'Casanare'/*)*/, 'tag' => '0'),
    'CO-CAU' => array('country' => 'CO', 'title' => /*t(*/'Cauca'/*)*/, 'tag' => '0'),
    'CO-CES' => array('country' => 'CO', 'title' => /*t(*/'Cesar'/*)*/, 'tag' => '0'),
    'CO-CHO' => array('country' => 'CO', 'title' => /*t(*/'Choco'/*)*/, 'tag' => '0'),
    'CO-COR' => array('country' => 'CO', 'title' => /*t(*/'Cordoba'/*)*/, 'tag' => '0'),
    'CO-GJR' => array('country' => 'CO', 'title' => /*t(*/'Guajira'/*)*/, 'tag' => '0'),
    'CO-GNA' => array('country' => 'CO', 'title' => /*t(*/'Guainia'/*)*/, 'tag' => '0'),
    'CO-GVR' => array('country' => 'CO', 'title' => /*t(*/'Guaviare'/*)*/, 'tag' => '0'),
    'CO-HUI' => array('country' => 'CO', 'title' => /*t(*/'Huila'/*)*/, 'tag' => '0'),
    'CO-MAG' => array('country' => 'CO', 'title' => /*t(*/'Magdalena'/*)*/, 'tag' => '0'),
    'CO-MET' => array('country' => 'CO', 'title' => /*t(*/'Meta'/*)*/, 'tag' => '0'),
    'CO-NAR' => array('country' => 'CO', 'title' => /*t(*/'Narino'/*)*/, 'tag' => '0'),
    'CO-NDS' => array(
      'country' => 'CO',
      'title' => /*t(*/'Norte de Santander'/*)*/,
      'tag' => '0'
    ),
    'CO-PUT' => array('country' => 'CO', 'title' => /*t(*/'Putumayo'/*)*/, 'tag' => '0'),
    'CO-QUI' => array('country' => 'CO', 'title' => /*t(*/'Quindio'/*)*/, 'tag' => '0'),
    'CO-RIS' => array('country' => 'CO', 'title' => /*t(*/'Risaralda'/*)*/, 'tag' => '0'),
    'CO-SAN' => array('country' => 'CO', 'title' => /*t(*/'Santander'/*)*/, 'tag' => '0'),
    'CO-SAP' => array(
      'country' => 'CO',
      'title' => /*t(*/'San Andres y Providencia'/*)*/,
      'tag' => '0'
    ),
    'CO-SUC' => array('country' => 'CO', 'title' => /*t(*/'Sucre'/*)*/, 'tag' => '0'),
    'CO-TOL' => array('country' => 'CO', 'title' => /*t(*/'Tolima'/*)*/, 'tag' => '0'),
    'CO-VAU' => array('country' => 'CO', 'title' => /*t(*/'Vaupes'/*)*/, 'tag' => '0'),
    'CO-VDC' => array(
      'country' => 'CO',
      'title' => /*t(*/'Valle del Cauca'/*)*/,
      'tag' => '0'
    ),
    'CO-VIC' => array('country' => 'CO', 'title' => /*t(*/'Vichada'/*)*/, 'tag' => '0'),
    'CR-AL' => array('country' => 'CR', 'title' => /*t(*/'Alajuela'/*)*/, 'tag' => '0'),
    'CR-CA' => array('country' => 'CR', 'title' => /*t(*/'Cartago'/*)*/, 'tag' => '0'),
    'CR-GU' => array('country' => 'CR', 'title' => /*t(*/'Guanacaste'/*)*/, 'tag' => '0'),
    'CR-HE' => array('country' => 'CR', 'title' => /*t(*/'Heredia'/*)*/, 'tag' => '0'),
    'CR-LI' => array('country' => 'CR', 'title' => /*t(*/'Limon'/*)*/, 'tag' => '0'),
    'CR-PU' => array('country' => 'CR', 'title' => /*t(*/'Puntarenas'/*)*/, 'tag' => '0'),
    'CR-SJ' => array('country' => 'CR', 'title' => /*t(*/'San Jose'/*)*/, 'tag' => '0'),
    'CS-KOS' => array('country' => 'CS', 'title' => /*t(*/'Kosovo'/*)*/, 'tag' => '0'),
    'CS-MON' => array('country' => 'CS', 'title' => /*t(*/'Montenegro'/*)*/, 'tag' => '0'),
    'CS-SER' => array('country' => 'CS', 'title' => /*t(*/'Serbia'/*)*/, 'tag' => '0'),
    'CS-VOJ' => array('country' => 'CS', 'title' => /*t(*/'Vojvodina'/*)*/, 'tag' => '0'),
    'CU-CAM' => array('country' => 'CU', 'title' => /*t(*/'Camaguey'/*)*/, 'tag' => '0'),
    'CU-CAV' => array(
      'country' => 'CU',
      'title' => /*t(*/'Ciego de Avila'/*)*/,
      'tag' => '0'
    ),
    'CU-CFU' => array('country' => 'CU', 'title' => /*t(*/'Cienfuegos'/*)*/, 'tag' => '0'),
    'CU-CLH' => array(
      'country' => 'CU',
      'title' => /*t(*/'Ciudad de La Habana'/*)*/,
      'tag' => '0'
    ),
    'CU-GRA' => array('country' => 'CU', 'title' => /*t(*/'Granma'/*)*/, 'tag' => '0'),
    'CU-GUA' => array('country' => 'CU', 'title' => /*t(*/'Guantanamo'/*)*/, 'tag' => '0'),
    'CU-HOL' => array('country' => 'CU', 'title' => /*t(*/'Holguin'/*)*/, 'tag' => '0'),
    'CU-IJU' => array(
      'country' => 'CU',
      'title' => /*t(*/'Isla de la Juventud'/*)*/,
      'tag' => '0'
    ),
    'CU-LHA' => array('country' => 'CU', 'title' => /*t(*/'La Habana'/*)*/, 'tag' => '0'),
    'CU-LTU' => array('country' => 'CU', 'title' => /*t(*/'Las Tunas'/*)*/, 'tag' => '0'),
    'CU-MAT' => array('country' => 'CU', 'title' => /*t(*/'Matanzas'/*)*/, 'tag' => '0'),
    'CU-PRI' => array(
      'country' => 'CU',
      'title' => /*t(*/'Pinar del Rio'/*)*/,
      'tag' => '0'
    ),
    'CU-SCU' => array(
      'country' => 'CU',
      'title' => /*t(*/'Santiago de Cuba'/*)*/,
      'tag' => '0'
    ),
    'CU-SSP' => array(
      'country' => 'CU',
      'title' => /*t(*/'Sancti Spiritus'/*)*/,
      'tag' => '0'
    ),
    'CU-VCL' => array(
      'country' => 'CU',
      'title' => /*t(*/'Villa Clara'/*)*/,
      'tag' => '0'
    ),
    'CV-BR' => array('country' => 'CV', 'title' => /*t(*/'Brava'/*)*/, 'tag' => '0'),
    'CV-BV' => array('country' => 'CV', 'title' => /*t(*/'Boa Vista'/*)*/, 'tag' => '0'),
    'CV-CA' => array(
      'country' => 'CV',
      'title' => /*t(*/'Santa Catarina'/*)*/,
      'tag' => '0'
    ),
    'CV-CR' => array('country' => 'CV', 'title' => /*t(*/'Santa Cruz'/*)*/, 'tag' => '0'),
    'CV-CS' => array(
      'country' => 'CV',
      'title' => /*t(*/'Calheta de Sao Miguel'/*)*/,
      'tag' => '0'
    ),
    'CV-MA' => array('country' => 'CV', 'title' => /*t(*/'Maio'/*)*/, 'tag' => '0'),
    'CV-MO' => array('country' => 'CV', 'title' => /*t(*/'Mosteiros'/*)*/, 'tag' => '0'),
    'CV-PA' => array('country' => 'CV', 'title' => /*t(*/'Paul'/*)*/, 'tag' => '0'),
    'CV-PN' => array('country' => 'CV', 'title' => /*t(*/'Porto Novo'/*)*/, 'tag' => '0'),
    'CV-PR' => array('country' => 'CV', 'title' => /*t(*/'Praia'/*)*/, 'tag' => '0'),
    'CV-RG' => array(
      'country' => 'CV',
      'title' => /*t(*/'Ribeira Grande'/*)*/,
      'tag' => '0'
    ),
    'CV-SD' => array(
      'country' => 'CV',
      'title' => /*t(*/'Sao Domingos'/*)*/,
      'tag' => '0'
    ),
    'CV-SF' => array('country' => 'CV', 'title' => /*t(*/'Sao Filipe'/*)*/, 'tag' => '0'),
    'CV-SL' => array('country' => 'CV', 'title' => /*t(*/'Sal'/*)*/, 'tag' => '0'),
    'CV-SN' => array('country' => 'CV', 'title' => /*t(*/'Sao Nicolau'/*)*/, 'tag' => '0'),
    'CV-SV' => array('country' => 'CV', 'title' => /*t(*/'Sao Vicente'/*)*/, 'tag' => '0'),
    'CV-TA' => array('country' => 'CV', 'title' => /*t(*/'Tarrafal'/*)*/, 'tag' => '0'),
    'CY-A' => array('country' => 'CY', 'title' => /*t(*/'Larnaca'/*)*/, 'tag' => '0'),
    'CY-F' => array('country' => 'CY', 'title' => /*t(*/'Famagusta'/*)*/, 'tag' => '0'),
    'CY-I' => array('country' => 'CY', 'title' => /*t(*/'Limassol'/*)*/, 'tag' => '0'),
    'CY-K' => array('country' => 'CY', 'title' => /*t(*/'Kyrenia'/*)*/, 'tag' => '0'),
    'CY-N' => array('country' => 'CY', 'title' => /*t(*/'Nicosia'/*)*/, 'tag' => '0'),
    'CY-P' => array('country' => 'CY', 'title' => /*t(*/'Paphos'/*)*/, 'tag' => '0'),
    'CZ-JC' => array(
      'country' => 'CZ',
      'title' => /*t(*/'South Bohemian Region (Jihocesky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-JM' => array(
      'country' => 'CZ',
      'title' => /*t(*/'South Moravian Region (Jihomoravsky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-KA' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Carlsbad Region  (Karlovarsky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-KR' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Hradec Kralove Region (Kralovehradecky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-LI' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Liberec Region (Liberecky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-MO' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Moravian-Silesian Region (Moravskoslezsky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-OL' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Olomouc Region (Olomoucky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-PA' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Pardubice Region (Pardubicky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-PL' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Plzen( Region Plzensky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-PR' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Prague - the Capital (Praha - hlavni mesto)'/*)*/,
      'tag' => '0'
    ),
    'CZ-ST' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Central Bohemian Region (Stredocesky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-US' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Usti nad Labem Region (Ustecky kraj)'/*)*/,
      'tag' => '0'
    ),
    'CZ-VY' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Vysoc(ina Region (kraj Vysoc(ina)'/*)*/,
      'tag' => '0'
    ),
    'CZ-ZL' => array(
      'country' => 'CZ',
      'title' => /*t(*/'Zlin Region (Zlinsky kraj)'/*)*/,
      'tag' => '0'
    ),
    'DE-BE' => array('country' => 'DE', 'title' => /*t(*/'Berlin'/*)*/, 'tag' => '0'),
    'DE-BR' => array('country' => 'DE', 'title' => /*t(*/'Brandenburg'/*)*/, 'tag' => '0'),
    'DE-BW' => array(
      'country' => 'DE',
      'title' => /*t(*/'Baden-Wurttemberg'/*)*/,
      'tag' => '0'
    ),
    'DE-BY' => array('country' => 'DE', 'title' => /*t(*/'Bayern'/*)*/, 'tag' => '0'),
    'DE-HB' => array('country' => 'DE', 'title' => /*t(*/'Bremen'/*)*/, 'tag' => '0'),
    'DE-HE' => array('country' => 'DE', 'title' => /*t(*/'Hessen'/*)*/, 'tag' => '0'),
    'DE-HH' => array('country' => 'DE', 'title' => /*t(*/'Hamburg'/*)*/, 'tag' => '0'),
    'DE-MV' => array(
      'country' => 'DE',
      'title' => /*t(*/'Mecklenburg-Vorpommern'/*)*/,
      'tag' => '0'
    ),
    'DE-NI' => array(
      'country' => 'DE',
      'title' => /*t(*/'Niedersachsen'/*)*/,
      'tag' => '0'
    ),
    'DE-NW' => array(
      'country' => 'DE',
      'title' => /*t(*/'Nordrhein-Westfalen'/*)*/,
      'tag' => '0'
    ),
    'DE-RP' => array(
      'country' => 'DE',
      'title' => /*t(*/'Rheinland-Pfalz'/*)*/,
      'tag' => '0'
    ),
    'DE-SH' => array(
      'country' => 'DE',
      'title' => /*t(*/'Schleswig-Holstein'/*)*/,
      'tag' => '0'
    ),
    'DE-SL' => array('country' => 'DE', 'title' => /*t(*/'Saarland'/*)*/, 'tag' => '0'),
    'DE-SN' => array('country' => 'DE', 'title' => /*t(*/'Sachsen'/*)*/, 'tag' => '0'),
    'DE-ST' => array(
      'country' => 'DE',
      'title' => /*t(*/'Sachsen-Anhalt'/*)*/,
      'tag' => '0'
    ),
    'DE-TH' => array('country' => 'DE', 'title' => /*t(*/'Thuringen'/*)*/, 'tag' => '0'),
    'DJ-J' => array('country' => 'DJ', 'title' => /*t(*/'Djibouti'/*)*/, 'tag' => '0'),
    'DJ-K' => array('country' => 'DJ', 'title' => /*t(*/'Dikhil'/*)*/, 'tag' => '0'),
    'DJ-O' => array('country' => 'DJ', 'title' => /*t(*/'Obock'/*)*/, 'tag' => '0'),
    'DJ-S' => array('country' => 'DJ', 'title' => /*t(*/"'Ali Sabih"/*)*/, 'tag' => '0'),
    'DJ-T' => array('country' => 'DJ', 'title' => /*t(*/'Tadjoura'/*)*/, 'tag' => '0'),
    'DK-AR' => array('country' => 'DK', 'title' => /*t(*/'Arhus'/*)*/, 'tag' => '0'),
    'DK-BH' => array('country' => 'DK', 'title' => /*t(*/'Bornholm'/*)*/, 'tag' => '0'),
    'DK-CC' => array(
      'country' => 'DK',
      'title' => /*t(*/'Copenhagen (municipality)'/*)*/,
      'tag' => '0'
    ),
    'DK-CO' => array('country' => 'DK', 'title' => /*t(*/'Copenhagen'/*)*/, 'tag' => '0'),
    'DK-FC' => array(
      'country' => 'DK',
      'title' => /*t(*/'Frederiksberg (municipality)'/*)*/,
      'tag' => '0'
    ),
    'DK-FO' => array(
      'country' => 'DK',
      'title' => /*t(*/'Faroe Islands'/*)*/,
      'tag' => '0'
    ),
    'DK-FR' => array(
      'country' => 'DK',
      'title' => /*t(*/'Frederiksborg'/*)*/,
      'tag' => '0'
    ),
    'DK-FU' => array('country' => 'DK', 'title' => /*t(*/'Funen'/*)*/, 'tag' => '0'),
    'DK-GL' => array('country' => 'DK', 'title' => /*t(*/'Greenland'/*)*/, 'tag' => '0'),
    'DK-NJ' => array(
      'country' => 'DK',
      'title' => /*t(*/'North Jutland'/*)*/,
      'tag' => '0'
    ),
    'DK-RB' => array('country' => 'DK', 'title' => /*t(*/'Ribe'/*)*/, 'tag' => '0'),
    'DK-RK' => array('country' => 'DK', 'title' => /*t(*/'Ringkjobing'/*)*/, 'tag' => '0'),
    'DK-RO' => array('country' => 'DK', 'title' => /*t(*/'Roskilde'/*)*/, 'tag' => '0'),
    'DK-SJ' => array(
      'country' => 'DK',
      'title' => /*t(*/'South Jutland'/*)*/,
      'tag' => '0'
    ),
    'DK-ST' => array('country' => 'DK', 'title' => /*t(*/'Storstrom'/*)*/, 'tag' => '0'),
    'DK-VB' => array('country' => 'DK', 'title' => /*t(*/'Viborg'/*)*/, 'tag' => '0'),
    'DK-VK' => array('country' => 'DK', 'title' => /*t(*/'Vejle'/*)*/, 'tag' => '0'),
    'DK-WZ' => array(
      'country' => 'DK',
      'title' => /*t(*/'West Zealand'/*)*/,
      'tag' => '0'
    ),
    'DM-AND' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint Andrew Parish'/*)*/,
      'tag' => '0'
    ),
    'DM-DAV' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint David Parish'/*)*/,
      'tag' => '0'
    ),
    'DM-GEO' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint George Parish'/*)*/,
      'tag' => '0'
    ),
    'DM-JOH' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint John Parish'/*)*/,
      'tag' => '0'
    ),
    'DM-JOS' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint Joseph Parish'/*)*/,
      'tag' => '0'
    ),
    'DM-LUK' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint Luke Parish'/*)*/,
      'tag' => '0'
    ),
    'DM-MAR' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint Mark Parish'/*)*/,
      'tag' => '0'
    ),
    'DM-PAT' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint Patrick Parish'/*)*/,
      'tag' => '0'
    ),
    'DM-PAU' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint Paul Parish'/*)*/,
      'tag' => '0'
    ),
    'DM-PET' => array(
      'country' => 'DM',
      'title' => /*t(*/'Saint Peter Parish'/*)*/,
      'tag' => '0'
    ),
    'DO-AL' => array(
      'country' => 'DO',
      'title' => /*t(*/'La Altagracia'/*)*/,
      'tag' => '0'
    ),
    'DO-AZ' => array('country' => 'DO', 'title' => /*t(*/'Azua'/*)*/, 'tag' => '0'),
    'DO-BC' => array('country' => 'DO', 'title' => /*t(*/'Baoruco'/*)*/, 'tag' => '0'),
    'DO-BH' => array('country' => 'DO', 'title' => /*t(*/'Barahona'/*)*/, 'tag' => '0'),
    'DO-DJ' => array('country' => 'DO', 'title' => /*t(*/'Dajabon'/*)*/, 'tag' => '0'),
    'DO-DN' => array(
      'country' => 'DO',
      'title' => /*t(*/'Distrito Nacional'/*)*/,
      'tag' => '0'
    ),
    'DO-DU' => array('country' => 'DO', 'title' => /*t(*/'Duarte'/*)*/, 'tag' => '0'),
    'DO-EL' => array('country' => 'DO', 'title' => /*t(*/'Elias Pina'/*)*/, 'tag' => '0'),
    'DO-ET' => array('country' => 'DO', 'title' => /*t(*/'Espaillat'/*)*/, 'tag' => '0'),
    'DO-HM' => array('country' => 'DO', 'title' => /*t(*/'Hato Mayor'/*)*/, 'tag' => '0'),
    'DO-IN' => array(
      'country' => 'DO',
      'title' => /*t(*/'Independencia'/*)*/,
      'tag' => '0'
    ),
    'DO-JO' => array(
      'country' => 'DO',
      'title' => /*t(*/'San Jose de Ocoa'/*)*/,
      'tag' => '0'
    ),
    'DO-MC' => array(
      'country' => 'DO',
      'title' => /*t(*/'Monte Cristi'/*)*/,
      'tag' => '0'
    ),
    'DO-MN' => array(
      'country' => 'DO',
      'title' => /*t(*/'Monsenor Nouel'/*)*/,
      'tag' => '0'
    ),
    'DO-MP' => array('country' => 'DO', 'title' => /*t(*/'Monte Plata'/*)*/, 'tag' => '0'),
    'DO-MT' => array(
      'country' => 'DO',
      'title' => /*t(*/'Maria Trinidad Sanchez'/*)*/,
      'tag' => '0'
    ),
    'DO-PD' => array('country' => 'DO', 'title' => /*t(*/'Pedernales'/*)*/, 'tag' => '0'),
    'DO-PM' => array(
      'country' => 'DO',
      'title' => /*t(*/'San Pedro de Macoris'/*)*/,
      'tag' => '0'
    ),
    'DO-PP' => array(
      'country' => 'DO',
      'title' => /*t(*/'Puerto Plata'/*)*/,
      'tag' => '0'
    ),
    'DO-PR' => array(
      'country' => 'DO',
      'title' => /*t(*/'Peravia (Bani)'/*)*/,
      'tag' => '0'
    ),
    'DO-RO' => array('country' => 'DO', 'title' => /*t(*/'La Romana'/*)*/, 'tag' => '0'),
    'DO-SA' => array('country' => 'DO', 'title' => /*t(*/'Santiago'/*)*/, 'tag' => '0'),
    'DO-SC' => array(
      'country' => 'DO',
      'title' => /*t(*/'San Cristobal'/*)*/,
      'tag' => '0'
    ),
    'DO-SD' => array(
      'country' => 'DO',
      'title' => /*t(*/'Santo Domingo'/*)*/,
      'tag' => '0'
    ),
    'DO-SH' => array(
      'country' => 'DO',
      'title' => /*t(*/'Sanchez Ramirez'/*)*/,
      'tag' => '0'
    ),
    'DO-SJ' => array('country' => 'DO', 'title' => /*t(*/'San Juan'/*)*/, 'tag' => '0'),
    'DO-SL' => array('country' => 'DO', 'title' => /*t(*/'Salcedo'/*)*/, 'tag' => '0'),
    'DO-SM' => array('country' => 'DO', 'title' => /*t(*/'Samana'/*)*/, 'tag' => '0'),
    'DO-ST' => array(
      'country' => 'DO',
      'title' => /*t(*/'Santiago Rodriguez'/*)*/,
      'tag' => '0'
    ),
    'DO-SY' => array('country' => 'DO', 'title' => /*t(*/'El Seybo'/*)*/, 'tag' => '0'),
    'DO-VA' => array('country' => 'DO', 'title' => /*t(*/'Valverde'/*)*/, 'tag' => '0'),
    'DO-VE' => array('country' => 'DO', 'title' => /*t(*/'La Vega'/*)*/, 'tag' => '0'),
    'DZ-ADE' => array('country' => 'DZ', 'title' => /*t(*/'Ain Defla'/*)*/, 'tag' => '0'),
    'DZ-ADR' => array('country' => 'DZ', 'title' => /*t(*/'Adrar'/*)*/, 'tag' => '0'),
    'DZ-ALG' => array('country' => 'DZ', 'title' => /*t(*/'Alger'/*)*/, 'tag' => '0'),
    'DZ-ANN' => array('country' => 'DZ', 'title' => /*t(*/'Annaba'/*)*/, 'tag' => '0'),
    'DZ-ATE' => array(
      'country' => 'DZ',
      'title' => /*t(*/'Ain Temouchent'/*)*/,
      'tag' => '0'
    ),
    'DZ-BAT' => array('country' => 'DZ', 'title' => /*t(*/'Batna'/*)*/, 'tag' => '0'),
    'DZ-BBA' => array(
      'country' => 'DZ',
      'title' => /*t(*/'Bordj Bou Arreridj'/*)*/,
      'tag' => '0'
    ),
    'DZ-BEC' => array('country' => 'DZ', 'title' => /*t(*/'Bechar'/*)*/, 'tag' => '0'),
    'DZ-BEJ' => array('country' => 'DZ', 'title' => /*t(*/'Bejaia'/*)*/, 'tag' => '0'),
    'DZ-BIS' => array('country' => 'DZ', 'title' => /*t(*/'Biskra'/*)*/, 'tag' => '0'),
    'DZ-BLI' => array('country' => 'DZ', 'title' => /*t(*/'Blida'/*)*/, 'tag' => '0'),
    'DZ-BMD' => array('country' => 'DZ', 'title' => /*t(*/'Boumerdes'/*)*/, 'tag' => '0'),
    'DZ-BOA' => array('country' => 'DZ', 'title' => /*t(*/'Bouira'/*)*/, 'tag' => '0'),
    'DZ-CHL' => array('country' => 'DZ', 'title' => /*t(*/'Chlef'/*)*/, 'tag' => '0'),
    'DZ-CON' => array(
      'country' => 'DZ',
      'title' => /*t(*/'Constantine'/*)*/,
      'tag' => '0'
    ),
    'DZ-DJE' => array('country' => 'DZ', 'title' => /*t(*/'Djelfa'/*)*/, 'tag' => '0'),
    'DZ-EBA' => array('country' => 'DZ', 'title' => /*t(*/'El Bayadh'/*)*/, 'tag' => '0'),
    'DZ-EOU' => array('country' => 'DZ', 'title' => /*t(*/'El Oued'/*)*/, 'tag' => '0'),
    'DZ-ETA' => array('country' => 'DZ', 'title' => /*t(*/'El Tarf'/*)*/, 'tag' => '0'),
    'DZ-GHA' => array('country' => 'DZ', 'title' => /*t(*/'Ghardaia'/*)*/, 'tag' => '0'),
    'DZ-GUE' => array('country' => 'DZ', 'title' => /*t(*/'Guelma'/*)*/, 'tag' => '0'),
    'DZ-ILL' => array('country' => 'DZ', 'title' => /*t(*/'Illizi'/*)*/, 'tag' => '0'),
    'DZ-JIJ' => array('country' => 'DZ', 'title' => /*t(*/'Jijel'/*)*/, 'tag' => '0'),
    'DZ-KHE' => array('country' => 'DZ', 'title' => /*t(*/'Khenchela'/*)*/, 'tag' => '0'),
    'DZ-LAG' => array('country' => 'DZ', 'title' => /*t(*/'Laghouat'/*)*/, 'tag' => '0'),
    'DZ-MED' => array('country' => 'DZ', 'title' => /*t(*/'Medea'/*)*/, 'tag' => '0'),
    'DZ-MIL' => array('country' => 'DZ', 'title' => /*t(*/'Mila'/*)*/, 'tag' => '0'),
    'DZ-MOS' => array('country' => 'DZ', 'title' => /*t(*/'Mostaganem'/*)*/, 'tag' => '0'),
    'DZ-MSI' => array('country' => 'DZ', 'title' => /*t(*/"M'Sila"/*)*/, 'tag' => '0'),
    'DZ-MUA' => array('country' => 'DZ', 'title' => /*t(*/'Muaskar'/*)*/, 'tag' => '0'),
    'DZ-NAA' => array('country' => 'DZ', 'title' => /*t(*/'Naama'/*)*/, 'tag' => '0'),
    'DZ-OEB' => array(
      'country' => 'DZ',
      'title' => /*t(*/'Oum el-Bouaghi'/*)*/,
      'tag' => '0'
    ),
    'DZ-ORA' => array('country' => 'DZ', 'title' => /*t(*/'Oran'/*)*/, 'tag' => '0'),
    'DZ-OUA' => array('country' => 'DZ', 'title' => /*t(*/'Ouargla'/*)*/, 'tag' => '0'),
    'DZ-REL' => array('country' => 'DZ', 'title' => /*t(*/'Relizane'/*)*/, 'tag' => '0'),
    'DZ-SAH' => array('country' => 'DZ', 'title' => /*t(*/'Souk Ahras'/*)*/, 'tag' => '0'),
    'DZ-SAI' => array('country' => 'DZ', 'title' => /*t(*/'Saida'/*)*/, 'tag' => '0'),
    'DZ-SBA' => array(
      'country' => 'DZ',
      'title' => /*t(*/'Sidi Bel Abbes'/*)*/,
      'tag' => '0'
    ),
    'DZ-SET' => array('country' => 'DZ', 'title' => /*t(*/'Setif'/*)*/, 'tag' => '0'),
    'DZ-SKI' => array('country' => 'DZ', 'title' => /*t(*/'Skikda'/*)*/, 'tag' => '0'),
    'DZ-TAM' => array(
      'country' => 'DZ',
      'title' => /*t(*/'Tamanghasset'/*)*/,
      'tag' => '0'
    ),
    'DZ-TEB' => array('country' => 'DZ', 'title' => /*t(*/'Tebessa'/*)*/, 'tag' => '0'),
    'DZ-TIA' => array('country' => 'DZ', 'title' => /*t(*/'Tiaret'/*)*/, 'tag' => '0'),
    'DZ-TIN' => array('country' => 'DZ', 'title' => /*t(*/'Tindouf'/*)*/, 'tag' => '0'),
    'DZ-TIP' => array('country' => 'DZ', 'title' => /*t(*/'Tipaza'/*)*/, 'tag' => '0'),
    'DZ-TIS' => array('country' => 'DZ', 'title' => /*t(*/'Tissemsilt'/*)*/, 'tag' => '0'),
    'DZ-TLE' => array('country' => 'DZ', 'title' => /*t(*/'Tlemcen'/*)*/, 'tag' => '0'),
    'DZ-TOU' => array('country' => 'DZ', 'title' => /*t(*/'Tizi Ouzou'/*)*/, 'tag' => '0'),
    'EC-A' => array('country' => 'EC', 'title' => /*t(*/'Azuay'/*)*/, 'tag' => '0'),
    'EC-B' => array('country' => 'EC', 'title' => /*t(*/'Bolivar'/*)*/, 'tag' => '0'),
    'EC-C' => array('country' => 'EC', 'title' => /*t(*/'Carchi'/*)*/, 'tag' => '0'),
    'EC-D' => array('country' => 'EC', 'title' => /*t(*/'Orellana'/*)*/, 'tag' => '0'),
    'EC-E' => array('country' => 'EC', 'title' => /*t(*/'Esmeraldas'/*)*/, 'tag' => '0'),
    'EC-F' => array('country' => 'EC', 'title' => /*t(*/'Canar'/*)*/, 'tag' => '0'),
    'EC-G' => array('country' => 'EC', 'title' => /*t(*/'Guayas'/*)*/, 'tag' => '0'),
    'EC-H' => array('country' => 'EC', 'title' => /*t(*/'Chimborazo'/*)*/, 'tag' => '0'),
    'EC-I' => array('country' => 'EC', 'title' => /*t(*/'Imbabura'/*)*/, 'tag' => '0'),
    'EC-L' => array('country' => 'EC', 'title' => /*t(*/'Loja'/*)*/, 'tag' => '0'),
    'EC-M' => array('country' => 'EC', 'title' => /*t(*/'Manabi'/*)*/, 'tag' => '0'),
    'EC-N' => array('country' => 'EC', 'title' => /*t(*/'Napo'/*)*/, 'tag' => '0'),
    'EC-O' => array('country' => 'EC', 'title' => /*t(*/'El Oro'/*)*/, 'tag' => '0'),
    'EC-P' => array('country' => 'EC', 'title' => /*t(*/'Pichincha'/*)*/, 'tag' => '0'),
    'EC-R' => array('country' => 'EC', 'title' => /*t(*/'Los Rios'/*)*/, 'tag' => '0'),
    'EC-S' => array(
      'country' => 'EC',
      'title' => /*t(*/'Morona-Santiago'/*)*/,
      'tag' => '0'
    ),
    'EC-T' => array('country' => 'EC', 'title' => /*t(*/'Tungurahua'/*)*/, 'tag' => '0'),
    'EC-U' => array('country' => 'EC', 'title' => /*t(*/'Sucumbios'/*)*/, 'tag' => '0'),
    'EC-W' => array('country' => 'EC', 'title' => /*t(*/'Galapagos'/*)*/, 'tag' => '0'),
    'EC-X' => array('country' => 'EC', 'title' => /*t(*/'Cotopaxi'/*)*/, 'tag' => '0'),
    'EC-Y' => array('country' => 'EC', 'title' => /*t(*/'Pastaza'/*)*/, 'tag' => '0'),
    'EC-Z' => array(
      'country' => 'EC',
      'title' => /*t(*/'Zamora-Chinchipe'/*)*/,
      'tag' => '0'
    ),
    'EE-37' => array(
      'country' => 'EE',
      'title' => /*t(*/'Harju County'/*)*/,
      'tag' => '0'
    ),
    'EE-39' => array('country' => 'EE', 'title' => /*t(*/'Hiiu County'/*)*/, 'tag' => '0'),
    'EE-44' => array(
      'country' => 'EE',
      'title' => /*t(*/'Ida-Viru County'/*)*/,
      'tag' => '0'
    ),
    'EE-49' => array('country' => 'EE', 'title' => /*t(*/'JOAa County'/*)*/, 'tag' => '0'),
    'EE-51' => array(
      'country' => 'EE',
      'title' => /*t(*/'JArva County'/*)*/,
      'tag' => '0'
    ),
    'EE-57' => array(
      'country' => 'EE',
      'title' => /*t(*/'LAAne County'/*)*/,
      'tag' => '0'
    ),
    'EE-59' => array(
      'country' => 'EE',
      'title' => /*t(*/'LAAne-Viru County'/*)*/,
      'tag' => '0'
    ),
    'EE-65' => array('country' => 'EE', 'title' => /*t(*/'POA County'/*)*/, 'tag' => '0'),
    'EE-67' => array(
      'country' => 'EE',
      'title' => /*t(*/'PArnu County'/*)*/,
      'tag' => '0'
    ),
    'EE-70' => array(
      'country' => 'EE',
      'title' => /*t(*/'Rapla County'/*)*/,
      'tag' => '0'
    ),
    'EE-74' => array(
      'country' => 'EE',
      'title' => /*t(*/'Saare County'/*)*/,
      'tag' => '0'
    ),
    'EE-78' => array(
      'country' => 'EE',
      'title' => /*t(*/'Tartu County'/*)*/,
      'tag' => '0'
    ),
    'EE-82' => array(
      'country' => 'EE',
      'title' => /*t(*/'Valga County'/*)*/,
      'tag' => '0'
    ),
    'EE-84' => array(
      'country' => 'EE',
      'title' => /*t(*/'Viljandi County'/*)*/,
      'tag' => '0'
    ),
    'EE-86' => array('country' => 'EE', 'title' => /*t(*/'VOACounty'/*)*/, 'tag' => '0'),
    'EG-ASW' => array('country' => 'EG', 'title' => /*t(*/'Aswan'/*)*/, 'tag' => '0'),
    'EG-ASY' => array('country' => 'EG', 'title' => /*t(*/'Asyut'/*)*/, 'tag' => '0'),
    'EG-BAM' => array(
      'country' => 'EG',
      'title' => /*t(*/'Al Bahr al Ahmar'/*)*/,
      'tag' => '0'
    ),
    'EG-BHY' => array(
      'country' => 'EG',
      'title' => /*t(*/'Al Buhayrah'/*)*/,
      'tag' => '0'
    ),
    'EG-BSD' => array('country' => 'EG', 'title' => /*t(*/"Bur Sa'id"/*)*/, 'tag' => '0'),
    'EG-BSW' => array(
      'country' => 'EG',
      'title' => /*t(*/'Bani Suwayf'/*)*/,
      'tag' => '0'
    ),
    'EG-DHY' => array(
      'country' => 'EG',
      'title' => /*t(*/'Ad Daqahliyah'/*)*/,
      'tag' => '0'
    ),
    'EG-DMY' => array('country' => 'EG', 'title' => /*t(*/'Dumyat'/*)*/, 'tag' => '0'),
    'EG-FYM' => array('country' => 'EG', 'title' => /*t(*/'Al Fayyum'/*)*/, 'tag' => '0'),
    'EG-GBY' => array(
      'country' => 'EG',
      'title' => /*t(*/'Al Gharbiyah'/*)*/,
      'tag' => '0'
    ),
    'EG-IDR' => array(
      'country' => 'EG',
      'title' => /*t(*/'Al Iskandariyah'/*)*/,
      'tag' => '0'
    ),
    'EG-IML' => array(
      'country' => 'EG',
      'title' => /*t(*/"Al Isma'iliyah"/*)*/,
      'tag' => '0'
    ),
    'EG-JNS' => array(
      'country' => 'EG',
      'title' => /*t(*/"Janub Sina'"/*)*/,
      'tag' => '0'
    ),
    'EG-JZH' => array('country' => 'EG', 'title' => /*t(*/'Al Jizah'/*)*/, 'tag' => '0'),
    'EG-KSH' => array(
      'country' => 'EG',
      'title' => /*t(*/'Kafr ash Shaykh'/*)*/,
      'tag' => '0'
    ),
    'EG-MAT' => array('country' => 'EG', 'title' => /*t(*/'Matruh'/*)*/, 'tag' => '0'),
    'EG-MFY' => array(
      'country' => 'EG',
      'title' => /*t(*/'Al Minufiyah'/*)*/,
      'tag' => '0'
    ),
    'EG-MNY' => array('country' => 'EG', 'title' => /*t(*/'Al Minya'/*)*/, 'tag' => '0'),
    'EG-QHR' => array('country' => 'EG', 'title' => /*t(*/'Al Qahirah'/*)*/, 'tag' => '0'),
    'EG-QIN' => array('country' => 'EG', 'title' => /*t(*/'Qina'/*)*/, 'tag' => '0'),
    'EG-QLY' => array(
      'country' => 'EG',
      'title' => /*t(*/'Al Qalyubiyah'/*)*/,
      'tag' => '0'
    ),
    'EG-SHQ' => array(
      'country' => 'EG',
      'title' => /*t(*/'Ash Sharqiyah'/*)*/,
      'tag' => '0'
    ),
    'EG-SHS' => array(
      'country' => 'EG',
      'title' => /*t(*/"Shamal Sina'"/*)*/,
      'tag' => '0'
    ),
    'EG-SUH' => array('country' => 'EG', 'title' => /*t(*/'Suhaj'/*)*/, 'tag' => '0'),
    'EG-SWY' => array('country' => 'EG', 'title' => /*t(*/'As Suways'/*)*/, 'tag' => '0'),
    'EG-WJD' => array(
      'country' => 'EG',
      'title' => /*t(*/'Al Wadi al Jadid'/*)*/,
      'tag' => '0'
    ),
    'ER-BR' => array(
      'country' => 'ER',
      'title' => /*t(*/'Gash-Barka (Barentu)'/*)*/,
      'tag' => '0'
    ),
    'ER-DE' => array(
      'country' => 'ER',
      'title' => /*t(*/'Southern (Debub)'/*)*/,
      'tag' => '0'
    ),
    'ER-DK' => array(
      'country' => 'ER',
      'title' => /*t(*/'Southern Red Sea (Debub-Keih-Bahri)'/*)*/,
      'tag' => '0'
    ),
    'ER-KE' => array(
      'country' => 'ER',
      'title' => /*t(*/'Anseba (Keren)'/*)*/,
      'tag' => '0'
    ),
    'ER-MA' => array(
      'country' => 'ER',
      'title' => /*t(*/'Central (Maekel)'/*)*/,
      'tag' => '0'
    ),
    'ER-SK' => array(
      'country' => 'ER',
      'title' => /*t(*/'Northern Red Sea (Semien-Keih-Bahri)'/*)*/,
      'tag' => '0'
    ),
    'ES-AB' => array('country' => 'ES', 'title' => /*t(*/'Albacete'/*)*/, 'tag' => '0'),
    'ES-AC' => array('country' => 'ES', 'title' => /*t(*/'Alicante'/*)*/, 'tag' => '0'),
    'ES-AL' => array('country' => 'ES', 'title' => /*t(*/'Alava'/*)*/, 'tag' => '0'),
    'ES-AM' => array('country' => 'ES', 'title' => /*t(*/'Almeria'/*)*/, 'tag' => '0'),
    'ES-AS' => array('country' => 'ES', 'title' => /*t(*/'Asturias'/*)*/, 'tag' => '0'),
    'ES-AV' => array('country' => 'ES', 'title' => /*t(*/'Avila'/*)*/, 'tag' => '0'),
    'ES-BA' => array('country' => 'ES', 'title' => /*t(*/'Barcelona'/*)*/, 'tag' => '0'),
    'ES-BJ' => array('country' => 'ES', 'title' => /*t(*/'Badajoz'/*)*/, 'tag' => '0'),
    'ES-BU' => array('country' => 'ES', 'title' => /*t(*/'Burgos'/*)*/, 'tag' => '0'),
    'ES-CA' => array('country' => 'ES', 'title' => /*t(*/'A Coruna'/*)*/, 'tag' => '0'),
    'ES-CC' => array('country' => 'ES', 'title' => /*t(*/'Caceres'/*)*/, 'tag' => '0'),
    'ES-CD' => array('country' => 'ES', 'title' => /*t(*/'Cordoba'/*)*/, 'tag' => '0'),
    'ES-CL' => array('country' => 'ES', 'title' => /*t(*/'Castellon'/*)*/, 'tag' => '0'),
    'ES-CR' => array('country' => 'ES', 'title' => /*t(*/'Ciudad Real'/*)*/, 'tag' => '0'),
    'ES-CT' => array('country' => 'ES', 'title' => /*t(*/'Cantabria'/*)*/, 'tag' => '0'),
    'ES-CU' => array('country' => 'ES', 'title' => /*t(*/'Cuenca'/*)*/, 'tag' => '0'),
    'ES-CZ' => array('country' => 'ES', 'title' => /*t(*/'Cadiz'/*)*/, 'tag' => '0'),
    'ES-GD' => array('country' => 'ES', 'title' => /*t(*/'Granada'/*)*/, 'tag' => '0'),
    'ES-GI' => array('country' => 'ES', 'title' => /*t(*/'Girona'/*)*/, 'tag' => '0'),
    'ES-GJ' => array('country' => 'ES', 'title' => /*t(*/'Guadalajara'/*)*/, 'tag' => '0'),
    'ES-GP' => array('country' => 'ES', 'title' => /*t(*/'Guipuzcoa'/*)*/, 'tag' => '0'),
    'ES-HL' => array('country' => 'ES', 'title' => /*t(*/'Huelva'/*)*/, 'tag' => '0'),
    'ES-HS' => array('country' => 'ES', 'title' => /*t(*/'Huesca'/*)*/, 'tag' => '0'),
    'ES-IB' => array(
      'country' => 'ES',
      'title' => /*t(*/'Illes Balears'/*)*/,
      'tag' => '0'
    ),
    'ES-JN' => array('country' => 'ES', 'title' => /*t(*/'Jaen'/*)*/, 'tag' => '0'),
    'ES-LE' => array('country' => 'ES', 'title' => /*t(*/'Leon'/*)*/, 'tag' => '0'),
    'ES-LG' => array('country' => 'ES', 'title' => /*t(*/'Lugo'/*)*/, 'tag' => '0'),
    'ES-LL' => array('country' => 'ES', 'title' => /*t(*/'Lleida'/*)*/, 'tag' => '0'),
    'ES-MD' => array('country' => 'ES', 'title' => /*t(*/'Madrid'/*)*/, 'tag' => '0'),
    'ES-ML' => array('country' => 'ES', 'title' => /*t(*/'Malaga'/*)*/, 'tag' => '0'),
    'ES-MU' => array('country' => 'ES', 'title' => /*t(*/'Mucria'/*)*/, 'tag' => '0'),
    'ES-NV' => array('country' => 'ES', 'title' => /*t(*/'Navarra'/*)*/, 'tag' => '0'),
    'ES-OU' => array('country' => 'ES', 'title' => /*t(*/'Ourense'/*)*/, 'tag' => '0'),
    'ES-PL' => array('country' => 'ES', 'title' => /*t(*/'Palencia'/*)*/, 'tag' => '0'),
    'ES-PM' => array('country' => 'ES', 'title' => /*t(*/'Las Palmas'/*)*/, 'tag' => '0'),
    'ES-PO' => array('country' => 'ES', 'title' => /*t(*/'Pontevedra'/*)*/, 'tag' => '0'),
    'ES-RJ' => array('country' => 'ES', 'title' => /*t(*/'La Rioja'/*)*/, 'tag' => '0'),
    'ES-SC' => array(
      'country' => 'ES',
      'title' => /*t(*/'Santa Cruz de Tererife'/*)*/,
      'tag' => '0'
    ),
    'ES-SG' => array('country' => 'ES', 'title' => /*t(*/'Segovia'/*)*/, 'tag' => '0'),
    'ES-SL' => array('country' => 'ES', 'title' => /*t(*/'Salamanca'/*)*/, 'tag' => '0'),
    'ES-SO' => array('country' => 'ES', 'title' => /*t(*/'Soria'/*)*/, 'tag' => '0'),
    'ES-SV' => array('country' => 'ES', 'title' => /*t(*/'Sevilla'/*)*/, 'tag' => '0'),
    'ES-TA' => array('country' => 'ES', 'title' => /*t(*/'Tarragona'/*)*/, 'tag' => '0'),
    'ES-TE' => array('country' => 'ES', 'title' => /*t(*/'Teruel'/*)*/, 'tag' => '0'),
    'ES-TO' => array('country' => 'ES', 'title' => /*t(*/'Toledo'/*)*/, 'tag' => '0'),
    'ES-VC' => array('country' => 'ES', 'title' => /*t(*/'Valencia'/*)*/, 'tag' => '0'),
    'ES-VD' => array('country' => 'ES', 'title' => /*t(*/'Valladolid'/*)*/, 'tag' => '0'),
    'ES-VZ' => array('country' => 'ES', 'title' => /*t(*/'Vizcaya'/*)*/, 'tag' => '0'),
    'ES-ZM' => array('country' => 'ES', 'title' => /*t(*/'Zamora'/*)*/, 'tag' => '0'),
    'ES-ZR' => array('country' => 'ES', 'title' => /*t(*/'Zaragoza'/*)*/, 'tag' => '0'),
    'ET-AA' => array('country' => 'ET', 'title' => /*t(*/'Addis Ababa'/*)*/, 'tag' => '0'),
    'ET-AF' => array('country' => 'ET', 'title' => /*t(*/'Afar'/*)*/, 'tag' => '0'),
    'ET-AH' => array('country' => 'ET', 'title' => /*t(*/'Amhara'/*)*/, 'tag' => '0'),
    'ET-BG' => array(
      'country' => 'ET',
      'title' => /*t(*/'Benishangul-Gumaz'/*)*/,
      'tag' => '0'
    ),
    'ET-DD' => array('country' => 'ET', 'title' => /*t(*/'Dire Dawa'/*)*/, 'tag' => '0'),
    'ET-GB' => array('country' => 'ET', 'title' => /*t(*/'Gambela'/*)*/, 'tag' => '0'),
    'ET-HR' => array('country' => 'ET', 'title' => /*t(*/'Hariai'/*)*/, 'tag' => '0'),
    'ET-OR' => array('country' => 'ET', 'title' => /*t(*/'Oromia'/*)*/, 'tag' => '0'),
    'ET-SM' => array('country' => 'ET', 'title' => /*t(*/'Somali'/*)*/, 'tag' => '0'),
    'ET-SN' => array(
      'country' => 'ET',
      'title' => /*t(*/'Southern Nations - Nationalities and Peoples Region'/*)*/,
      'tag' => '0'
    ),
    'ET-TG' => array('country' => 'ET', 'title' => /*t(*/'Tigray'/*)*/, 'tag' => '0'),
    'FI-AH' => array(
      'country' => 'FI',
      'title' => /*t(*/'Ahvenanmaan laani'/*)*/,
      'tag' => '0'
    ),
    'FI-ES' => array(
      'country' => 'FI',
      'title' => /*t(*/'Etela-Suomen laani'/*)*/,
      'tag' => '0'
    ),
    'FI-IS' => array(
      'country' => 'FI',
      'title' => /*t(*/'Ita-Suomen laani'/*)*/,
      'tag' => '0'
    ),
    'FI-LL' => array('country' => 'FI', 'title' => /*t(*/'Lapin laani'/*)*/, 'tag' => '0'),
    'FI-LS' => array(
      'country' => 'FI',
      'title' => /*t(*/'Lansi-Suomen laani'/*)*/,
      'tag' => '0'
    ),
    'FI-OU' => array('country' => 'FI', 'title' => /*t(*/'Oulun laani'/*)*/, 'tag' => '0'),
    'FJ-C' => array(
      'country' => 'FJ',
      'title' => /*t(*/'Central Division'/*)*/,
      'tag' => '0'
    ),
    'FJ-E' => array(
      'country' => 'FJ',
      'title' => /*t(*/'Eastern Division'/*)*/,
      'tag' => '0'
    ),
    'FJ-N' => array(
      'country' => 'FJ',
      'title' => /*t(*/'Northern Division'/*)*/,
      'tag' => '0'
    ),
    'FJ-R' => array('country' => 'FJ', 'title' => /*t(*/'Rotuma'/*)*/, 'tag' => '0'),
    'FJ-W' => array(
      'country' => 'FJ',
      'title' => /*t(*/'Western Division'/*)*/,
      'tag' => '0'
    ),
    'FM-C' => array('country' => 'FM', 'title' => /*t(*/'Chuuk'/*)*/, 'tag' => '0'),
    'FM-K' => array('country' => 'FM', 'title' => /*t(*/'Kosrae'/*)*/, 'tag' => '0'),
    'FM-P' => array('country' => 'FM', 'title' => /*t(*/'Pohnpei'/*)*/, 'tag' => '0'),
    'FM-Y' => array('country' => 'FM', 'title' => /*t(*/'Yap'/*)*/, 'tag' => '0'),
    'FR-A67' => array(
      'country' => 'FR',
      'title' => /*t(*/'Bas-Rhin - Alsace'/*)*/,
      'tag' => '0'
    ),
    'FR-A68' => array(
      'country' => 'FR',
      'title' => /*t(*/'Haut-Rhin - Alsace'/*)*/,
      'tag' => '0'
    ),
    'FR-B24' => array(
      'country' => 'FR',
      'title' => /*t(*/'Dordogne - Aquitaine'/*)*/,
      'tag' => '0'
    ),
    'FR-B33' => array(
      'country' => 'FR',
      'title' => /*t(*/'Gironde - Aquitaine'/*)*/,
      'tag' => '0'
    ),
    'FR-B40' => array(
      'country' => 'FR',
      'title' => /*t(*/'Landes - Aquitaine'/*)*/,
      'tag' => '0'
    ),
    'FR-B47' => array(
      'country' => 'FR',
      'title' => /*t(*/'Lot-et-Garonne - Aquitaine'/*)*/,
      'tag' => '0'
    ),
    'FR-B64' => array(
      'country' => 'FR',
      'title' => /*t(*/'Pyrenees-Atlantiques - Aquitaine'/*)*/,
      'tag' => '0'
    ),
    'FR-B79' => array(
      'country' => 'FR',
      'title' => /*t(*/'Deux-Sevres - Aquitaine'/*)*/,
      'tag' => '0'
    ),
    'FR-C03' => array(
      'country' => 'FR',
      'title' => /*t(*/'Allier - Auvergne'/*)*/,
      'tag' => '0'
    ),
    'FR-C15' => array(
      'country' => 'FR',
      'title' => /*t(*/'Cantal - Auvergne'/*)*/,
      'tag' => '0'
    ),
    'FR-C43' => array(
      'country' => 'FR',
      'title' => /*t(*/'Haute-Loire - Auvergne'/*)*/,
      'tag' => '0'
    ),
    'FR-C63' => array(
      'country' => 'FR',
      'title' => /*t(*/'Pu-de-Dme - Auvergne'/*)*/,
      'tag' => '0'
    ),
    'FR-D21' => array(
      'country' => 'FR',
      'title' => /*t(*/"Cote-d'Or - Bourgogne"/*)*/,
      'tag' => '0'
    ),
    'FR-D58' => array(
      'country' => 'FR',
      'title' => /*t(*/'Nievre - Bourgogne'/*)*/,
      'tag' => '0'
    ),
    'FR-D71' => array(
      'country' => 'FR',
      'title' => /*t(*/'Saone-et-Loire - Bourgogne'/*)*/,
      'tag' => '0'
    ),
    'FR-D89' => array(
      'country' => 'FR',
      'title' => /*t(*/'Yonne - Bourgogne'/*)*/,
      'tag' => '0'
    ),
    'FR-E22' => array(
      'country' => 'FR',
      'title' => /*t(*/"Cotes-d'Armor - Bretagne"/*)*/,
      'tag' => '0'
    ),
    'FR-E29' => array(
      'country' => 'FR',
      'title' => /*t(*/'Finistere - Bretagne'/*)*/,
      'tag' => '0'
    ),
    'FR-E35' => array(
      'country' => 'FR',
      'title' => /*t(*/'Ille-et-Vilaine - Bretagne'/*)*/,
      'tag' => '0'
    ),
    'FR-E56' => array(
      'country' => 'FR',
      'title' => /*t(*/'Morbihan - Bretagne'/*)*/,
      'tag' => '0'
    ),
    'FR-F18' => array(
      'country' => 'FR',
      'title' => /*t(*/'Cher - Centre'/*)*/,
      'tag' => '0'
    ),
    'FR-F28' => array(
      'country' => 'FR',
      'title' => /*t(*/'Eure-et-Loir - Centre'/*)*/,
      'tag' => '0'
    ),
    'FR-F36' => array(
      'country' => 'FR',
      'title' => /*t(*/'Indre - Centre'/*)*/,
      'tag' => '0'
    ),
    'FR-F37' => array(
      'country' => 'FR',
      'title' => /*t(*/'Indre-et-Loire - Centre'/*)*/,
      'tag' => '0'
    ),
    'FR-F41' => array(
      'country' => 'FR',
      'title' => /*t(*/'Loir-et-Cher - Centre'/*)*/,
      'tag' => '0'
    ),
    'FR-F45' => array(
      'country' => 'FR',
      'title' => /*t(*/'Loiret - Centre'/*)*/,
      'tag' => '0'
    ),
    'FR-G08' => array(
      'country' => 'FR',
      'title' => /*t(*/'Ardennes - Champagne-Ardenne'/*)*/,
      'tag' => '0'
    ),
    'FR-G10' => array(
      'country' => 'FR',
      'title' => /*t(*/'Aube - Champagne-Ardenne'/*)*/,
      'tag' => '0'
    ),
    'FR-G51' => array(
      'country' => 'FR',
      'title' => /*t(*/'Marne - Champagne-Ardenne'/*)*/,
      'tag' => '0'
    ),
    'FR-G52' => array(
      'country' => 'FR',
      'title' => /*t(*/'Haute-Marne - Champagne-Ardenne'/*)*/,
      'tag' => '0'
    ),
    'FR-H2A' => array(
      'country' => 'FR',
      'title' => /*t(*/'Corse-du-Sud - Corse'/*)*/,
      'tag' => '0'
    ),
    'FR-H2B' => array(
      'country' => 'FR',
      'title' => /*t(*/'Haute-Corse - Corse'/*)*/,
      'tag' => '0'
    ),
    'FR-I25' => array(
      'country' => 'FR',
      'title' => /*t(*/'Doubs - Franche-Comte'/*)*/,
      'tag' => '0'
    ),
    'FR-I39' => array(
      'country' => 'FR',
      'title' => /*t(*/'Jura - Franche-Comte'/*)*/,
      'tag' => '0'
    ),
    'FR-I70' => array(
      'country' => 'FR',
      'title' => /*t(*/'Haute-Saone - Franche-Comte'/*)*/,
      'tag' => '0'
    ),
    'FR-I90' => array(
      'country' => 'FR',
      'title' => /*t(*/'Haute-Saone - Territoire de Belfort'/*)*/,
      'tag' => '0'
    ),
    'FR-J75' => array(
      'country' => 'FR',
      'title' => /*t(*/'Paris - Ile-de-France'/*)*/,
      'tag' => '0'
    ),
    'FR-J77' => array(
      'country' => 'FR',
      'title' => /*t(*/'Seine-et-Marne - Ile-de-France'/*)*/,
      'tag' => '0'
    ),
    'FR-J78' => array(
      'country' => 'FR',
      'title' => /*t(*/'Yvelines - Ile-de-France'/*)*/,
      'tag' => '0'
    ),
    'FR-J91' => array(
      'country' => 'FR',
      'title' => /*t(*/'Essonne - Ile-de-France'/*)*/,
      'tag' => '0'
    ),
    'FR-J92' => array(
      'country' => 'FR',
      'title' => /*t(*/'Hauts-de-Seine - Ile-de-France'/*)*/,
      'tag' => '0'
    ),
    'FR-J93' => array(
      'country' => 'FR',
      'title' => /*t(*/'Seine-Saint-Denis - Ile-de-France'/*)*/,
      'tag' => '0'
    ),
    'FR-J94' => array(
      'country' => 'FR',
      'title' => /*t(*/'Val-de-Marne - Ile-de-France'/*)*/,
      'tag' => '0'
    ),
    'FR-J95' => array(
      'country' => 'FR',
      'title' => /*t(*/"Val-d'Oise - Ile-de-France"/*)*/,
      'tag' => '0'
    ),
    'FR-U04' => array(
      'country' => 'FR',
      'title' => /*t(*/'Alpes-de-Haute-Provence'/*)*/,
      'tag' => '0'
    ),
    'FR-U05' => array(
      'country' => 'FR',
      'title' => /*t(*/'Hautes-Alpes'/*)*/,
      'tag' => '0'
    ),
    'FR-U06' => array(
      'country' => 'FR',
      'title' => /*t(*/'Alpes-Maritimes'/*)*/,
      'tag' => '0'
    ),
    'FR-U13' => array(
      'country' => 'FR',
      'title' => /*t(*/'Bouches-du-Rhne'/*)*/,
      'tag' => '0'
    ),
    'FR-U83' => array('country' => 'FR', 'title' => /*t(*/'Var'/*)*/, 'tag' => '0'),
    'FR-U84' => array('country' => 'FR', 'title' => /*t(*/'Vaucluse'/*)*/, 'tag' => '0'),
    'GA-ES' => array('country' => 'GA', 'title' => /*t(*/'Estuaire'/*)*/, 'tag' => '0'),
    'GA-HO' => array('country' => 'GA', 'title' => /*t(*/'Haut-Ogooue'/*)*/, 'tag' => '0'),
    'GA-MO' => array(
      'country' => 'GA',
      'title' => /*t(*/'Moyen-Ogooue'/*)*/,
      'tag' => '0'
    ),
    'GA-NG' => array('country' => 'GA', 'title' => /*t(*/'Ngounie'/*)*/, 'tag' => '0'),
    'GA-NY' => array('country' => 'GA', 'title' => /*t(*/'Nyanga'/*)*/, 'tag' => '0'),
    'GA-OI' => array(
      'country' => 'GA',
      'title' => /*t(*/'Ogooue-Ivindo'/*)*/,
      'tag' => '0'
    ),
    'GA-OL' => array('country' => 'GA', 'title' => /*t(*/'Ogooue-Lolo'/*)*/, 'tag' => '0'),
    'GA-OM' => array(
      'country' => 'GA',
      'title' => /*t(*/'Ogooue-Maritime'/*)*/,
      'tag' => '0'
    ),
    'GA-WN' => array('country' => 'GA', 'title' => /*t(*/'Woleu-Ntem'/*)*/, 'tag' => '0'),
    'GB-ABD' => array(
      'country' => 'GB',
      'title' => /*t(*/'Aberdeenshire'/*)*/,
      'tag' => '0'
    ),
    'GB-ABE' => array('country' => 'GB', 'title' => /*t(*/'Aberdeen'/*)*/, 'tag' => '0'),
    'GB-AGB' => array(
      'country' => 'GB',
      'title' => /*t(*/'Argyll and Bute'/*)*/,
      'tag' => '0'
    ),
    'GB-AGY' => array(
      'country' => 'GB',
      'title' => /*t(*/'Isle of Anglesey'/*)*/,
      'tag' => '0'
    ),
    'GB-ANS' => array('country' => 'GB', 'title' => /*t(*/'Angus'/*)*/, 'tag' => '0'),
    'GB-ANT' => array('country' => 'GB', 'title' => /*t(*/'Antrim'/*)*/, 'tag' => '0'),
    'GB-ARD' => array('country' => 'GB', 'title' => /*t(*/'Ards'/*)*/, 'tag' => '0'),
    'GB-ARM' => array('country' => 'GB', 'title' => /*t(*/'Armagh'/*)*/, 'tag' => '0'),
    'GB-BAS' => array(
      'country' => 'GB',
      'title' => /*t(*/'Bath and North East Somerset'/*)*/,
      'tag' => '0'
    ),
    'GB-BBD' => array(
      'country' => 'GB',
      'title' => /*t(*/'Blackburn with Darwen'/*)*/,
      'tag' => '0'
    ),
    'GB-BDF' => array(
      'country' => 'GB',
      'title' => /*t(*/'Bedfordshire'/*)*/,
      'tag' => '0'
    ),
    'GB-BDG' => array(
      'country' => 'GB',
      'title' => /*t(*/'Barking and Dagenham'/*)*/,
      'tag' => '0'
    ),
    'GB-BEN' => array('country' => 'GB', 'title' => /*t(*/'Brent'/*)*/, 'tag' => '0'),
    'GB-BEX' => array('country' => 'GB', 'title' => /*t(*/'Bexley'/*)*/, 'tag' => '0'),
    'GB-BFS' => array('country' => 'GB', 'title' => /*t(*/'Belfast'/*)*/, 'tag' => '0'),
    'GB-BGE' => array('country' => 'GB', 'title' => /*t(*/'Bridgend'/*)*/, 'tag' => '0'),
    'GB-BGW' => array(
      'country' => 'GB',
      'title' => /*t(*/'Blaenau Gwent'/*)*/,
      'tag' => '0'
    ),
    'GB-BIR' => array('country' => 'GB', 'title' => /*t(*/'Birmingham'/*)*/, 'tag' => '0'),
    'GB-BKM' => array(
      'country' => 'GB',
      'title' => /*t(*/'Buckinghamshire'/*)*/,
      'tag' => '0'
    ),
    'GB-BLA' => array('country' => 'GB', 'title' => /*t(*/'Ballymena'/*)*/, 'tag' => '0'),
    'GB-BLY' => array('country' => 'GB', 'title' => /*t(*/'Ballymoney'/*)*/, 'tag' => '0'),
    'GB-BMH' => array(
      'country' => 'GB',
      'title' => /*t(*/'Bournemouth'/*)*/,
      'tag' => '0'
    ),
    'GB-BNB' => array('country' => 'GB', 'title' => /*t(*/'Banbridge'/*)*/, 'tag' => '0'),
    'GB-BNE' => array('country' => 'GB', 'title' => /*t(*/'Barnet'/*)*/, 'tag' => '0'),
    'GB-BNH' => array(
      'country' => 'GB',
      'title' => /*t(*/'Brighton and Hove'/*)*/,
      'tag' => '0'
    ),
    'GB-BNS' => array('country' => 'GB', 'title' => /*t(*/'Barnsley'/*)*/, 'tag' => '0'),
    'GB-BOL' => array('country' => 'GB', 'title' => /*t(*/'Bolton'/*)*/, 'tag' => '0'),
    'GB-BPL' => array('country' => 'GB', 'title' => /*t(*/'Blackpool'/*)*/, 'tag' => '0'),
    'GB-BRC' => array(
      'country' => 'GB',
      'title' => /*t(*/'Bracknell Forest'/*)*/,
      'tag' => '0'
    ),
    'GB-BRD' => array('country' => 'GB', 'title' => /*t(*/'Bradford'/*)*/, 'tag' => '0'),
    'GB-BRY' => array('country' => 'GB', 'title' => /*t(*/'Bromley'/*)*/, 'tag' => '0'),
    'GB-BST' => array(
      'country' => 'GB',
      'title' => /*t(*/'Bristol City of'/*)*/,
      'tag' => '0'
    ),
    'GB-BUR' => array('country' => 'GB', 'title' => /*t(*/'Bury'/*)*/, 'tag' => '0'),
    'GB-CAM' => array(
      'country' => 'GB',
      'title' => /*t(*/'Cambridgeshire'/*)*/,
      'tag' => '0'
    ),
    'GB-CAY' => array('country' => 'GB', 'title' => /*t(*/'Caerphilly'/*)*/, 'tag' => '0'),
    'GB-CGN' => array('country' => 'GB', 'title' => /*t(*/'Ceredigion'/*)*/, 'tag' => '0'),
    'GB-CGV' => array('country' => 'GB', 'title' => /*t(*/'Craigavon'/*)*/, 'tag' => '0'),
    'GB-CHS' => array('country' => 'GB', 'title' => /*t(*/'Cheshire'/*)*/, 'tag' => '0'),
    'GB-CKF' => array(
      'country' => 'GB',
      'title' => /*t(*/'Carrickfergus'/*)*/,
      'tag' => '0'
    ),
    'GB-CKT' => array('country' => 'GB', 'title' => /*t(*/'Cookstown'/*)*/, 'tag' => '0'),
    'GB-CLD' => array('country' => 'GB', 'title' => /*t(*/'Calderdale'/*)*/, 'tag' => '0'),
    'GB-CLK' => array(
      'country' => 'GB',
      'title' => /*t(*/'Clackmannanshire'/*)*/,
      'tag' => '0'
    ),
    'GB-CLR' => array('country' => 'GB', 'title' => /*t(*/'Coleraine'/*)*/, 'tag' => '0'),
    'GB-CMA' => array('country' => 'GB', 'title' => /*t(*/'Cumbria'/*)*/, 'tag' => '0'),
    'GB-CMD' => array('country' => 'GB', 'title' => /*t(*/'Camden'/*)*/, 'tag' => '0'),
    'GB-CMN' => array(
      'country' => 'GB',
      'title' => /*t(*/'Carmarthenshire'/*)*/,
      'tag' => '0'
    ),
    'GB-CON' => array('country' => 'GB', 'title' => /*t(*/'Cornwall'/*)*/, 'tag' => '0'),
    'GB-COV' => array(
      'country' => 'GB',
      'title' => /*t(*/'Coventry (West Midlands district)'/*)*/,
      'tag' => '0'
    ),
    'GB-CRF' => array('country' => 'GB', 'title' => /*t(*/'Cardiff'/*)*/, 'tag' => '0'),
    'GB-CRY' => array('country' => 'GB', 'title' => /*t(*/'Croydon'/*)*/, 'tag' => '0'),
    'GB-CSR' => array(
      'country' => 'GB',
      'title' => /*t(*/'Castlereagh'/*)*/,
      'tag' => '0'
    ),
    'GB-CWY' => array('country' => 'GB', 'title' => /*t(*/'Conwy'/*)*/, 'tag' => '0'),
    'GB-DAL' => array('country' => 'GB', 'title' => /*t(*/'Darlington'/*)*/, 'tag' => '0'),
    'GB-DBY' => array('country' => 'GB', 'title' => /*t(*/'Derbyshire'/*)*/, 'tag' => '0'),
    'GB-DEN' => array(
      'country' => 'GB',
      'title' => /*t(*/'Denbighshire'/*)*/,
      'tag' => '0'
    ),
    'GB-DER' => array('country' => 'GB', 'title' => /*t(*/'Derby'/*)*/, 'tag' => '0'),
    'GB-DEV' => array('country' => 'GB', 'title' => /*t(*/'Devon'/*)*/, 'tag' => '0'),
    'GB-DGN' => array(
      'country' => 'GB',
      'title' => /*t(*/'Dungannon and South Tyrone'/*)*/,
      'tag' => '0'
    ),
    'GB-DGY' => array(
      'country' => 'GB',
      'title' => /*t(*/'Dumfries and Galloway'/*)*/,
      'tag' => '0'
    ),
    'GB-DNC' => array('country' => 'GB', 'title' => /*t(*/'Doncaster'/*)*/, 'tag' => '0'),
    'GB-DND' => array('country' => 'GB', 'title' => /*t(*/'Dundee'/*)*/, 'tag' => '0'),
    'GB-DOR' => array('country' => 'GB', 'title' => /*t(*/'Dorset'/*)*/, 'tag' => '0'),
    'GB-DOW' => array('country' => 'GB', 'title' => /*t(*/'Down'/*)*/, 'tag' => '0'),
    'GB-DRY' => array('country' => 'GB', 'title' => /*t(*/'Derry'/*)*/, 'tag' => '0'),
    'GB-DUD' => array(
      'country' => 'GB',
      'title' => /*t(*/'Dudley (West Midlands district)'/*)*/,
      'tag' => '0'
    ),
    'GB-DUR' => array('country' => 'GB', 'title' => /*t(*/'Durham'/*)*/, 'tag' => '0'),
    'GB-EAL' => array('country' => 'GB', 'title' => /*t(*/'Ealing'/*)*/, 'tag' => '0'),
    'GB-EAY' => array(
      'country' => 'GB',
      'title' => /*t(*/'East Ayrshire'/*)*/,
      'tag' => '0'
    ),
    'GB-EDH' => array('country' => 'GB', 'title' => /*t(*/'Edinburgh'/*)*/, 'tag' => '0'),
    'GB-EDU' => array(
      'country' => 'GB',
      'title' => /*t(*/'East Dunbartonshire'/*)*/,
      'tag' => '0'
    ),
    'GB-ELN' => array(
      'country' => 'GB',
      'title' => /*t(*/'East Lothian'/*)*/,
      'tag' => '0'
    ),
    'GB-ELS' => array(
      'country' => 'GB',
      'title' => /*t(*/'Eilean Siar'/*)*/,
      'tag' => '0'
    ),
    'GB-ENF' => array('country' => 'GB', 'title' => /*t(*/'Enfield'/*)*/, 'tag' => '0'),
    'GB-ERW' => array(
      'country' => 'GB',
      'title' => /*t(*/'East Renfrewshire'/*)*/,
      'tag' => '0'
    ),
    'GB-ERY' => array(
      'country' => 'GB',
      'title' => /*t(*/'East Riding of Yorkshire'/*)*/,
      'tag' => '0'
    ),
    'GB-ESS' => array('country' => 'GB', 'title' => /*t(*/'Essex'/*)*/, 'tag' => '0'),
    'GB-ESX' => array(
      'country' => 'GB',
      'title' => /*t(*/'East Sussex'/*)*/,
      'tag' => '0'
    ),
    'GB-FAL' => array('country' => 'GB', 'title' => /*t(*/'Falkirk'/*)*/, 'tag' => '0'),
    'GB-FER' => array('country' => 'GB', 'title' => /*t(*/'Fermanagh'/*)*/, 'tag' => '0'),
    'GB-FIF' => array('country' => 'GB', 'title' => /*t(*/'Fife'/*)*/, 'tag' => '0'),
    'GB-FLN' => array('country' => 'GB', 'title' => /*t(*/'Flintshire'/*)*/, 'tag' => '0'),
    'GB-GAT' => array(
      'country' => 'GB',
      'title' => /*t(*/'Gateshead (Tyne & Wear district)'/*)*/,
      'tag' => '0'
    ),
    'GB-GLG' => array('country' => 'GB', 'title' => /*t(*/'Glasgow'/*)*/, 'tag' => '0'),
    'GB-GLS' => array(
      'country' => 'GB',
      'title' => /*t(*/'Gloucestershire'/*)*/,
      'tag' => '0'
    ),
    'GB-GRE' => array('country' => 'GB', 'title' => /*t(*/'Greenwich'/*)*/, 'tag' => '0'),
    'GB-GSY' => array('country' => 'GB', 'title' => /*t(*/'Guernsey'/*)*/, 'tag' => '0'),
    'GB-GWN' => array('country' => 'GB', 'title' => /*t(*/'Gwynedd'/*)*/, 'tag' => '0'),
    'GB-HAL' => array('country' => 'GB', 'title' => /*t(*/'Halton'/*)*/, 'tag' => '0'),
    'GB-HAM' => array('country' => 'GB', 'title' => /*t(*/'Hampshire'/*)*/, 'tag' => '0'),
    'GB-HAV' => array('country' => 'GB', 'title' => /*t(*/'Havering'/*)*/, 'tag' => '0'),
    'GB-HCK' => array('country' => 'GB', 'title' => /*t(*/'Hackney'/*)*/, 'tag' => '0'),
    'GB-HEF' => array(
      'country' => 'GB',
      'title' => /*t(*/'Herefordshire County of'/*)*/,
      'tag' => '0'
    ),
    'GB-HIL' => array('country' => 'GB', 'title' => /*t(*/'Hillingdon'/*)*/, 'tag' => '0'),
    'GB-HLD' => array('country' => 'GB', 'title' => /*t(*/'Highland'/*)*/, 'tag' => '0'),
    'GB-HMF' => array(
      'country' => 'GB',
      'title' => /*t(*/'Hammersmith and Fulham'/*)*/,
      'tag' => '0'
    ),
    'GB-HNS' => array('country' => 'GB', 'title' => /*t(*/'Hounslow'/*)*/, 'tag' => '0'),
    'GB-HPL' => array('country' => 'GB', 'title' => /*t(*/'Hartlepool'/*)*/, 'tag' => '0'),
    'GB-HRT' => array(
      'country' => 'GB',
      'title' => /*t(*/'Hertfordshire'/*)*/,
      'tag' => '0'
    ),
    'GB-HRW' => array('country' => 'GB', 'title' => /*t(*/'Harrow'/*)*/, 'tag' => '0'),
    'GB-HRY' => array('country' => 'GB', 'title' => /*t(*/'Haringey'/*)*/, 'tag' => '0'),
    'GB-IOS' => array(
      'country' => 'GB',
      'title' => /*t(*/'Isles of Scilly'/*)*/,
      'tag' => '0'
    ),
    'GB-IOW' => array(
      'country' => 'GB',
      'title' => /*t(*/'Isle of Wight'/*)*/,
      'tag' => '0'
    ),
    'GB-ISL' => array('country' => 'GB', 'title' => /*t(*/'Islington'/*)*/, 'tag' => '0'),
    'GB-IVC' => array('country' => 'GB', 'title' => /*t(*/'Inverclyde'/*)*/, 'tag' => '0'),
    'GB-JSY' => array('country' => 'GB', 'title' => /*t(*/'Jersey'/*)*/, 'tag' => '0'),
    'GB-KEC' => array(
      'country' => 'GB',
      'title' => /*t(*/'Kensington and Chelsea'/*)*/,
      'tag' => '0'
    ),
    'GB-KEN' => array('country' => 'GB', 'title' => /*t(*/'Kent'/*)*/, 'tag' => '0'),
    'GB-KHL' => array(
      'country' => 'GB',
      'title' => /*t(*/'Kingston upon Hull City of'/*)*/,
      'tag' => '0'
    ),
    'GB-KIR' => array('country' => 'GB', 'title' => /*t(*/'Kirklees'/*)*/, 'tag' => '0'),
    'GB-KTT' => array(
      'country' => 'GB',
      'title' => /*t(*/'Kingston upon Thames'/*)*/,
      'tag' => '0'
    ),
    'GB-KWL' => array('country' => 'GB', 'title' => /*t(*/'Knowsley'/*)*/, 'tag' => '0'),
    'GB-LAN' => array('country' => 'GB', 'title' => /*t(*/'Lancashire'/*)*/, 'tag' => '0'),
    'GB-LBH' => array('country' => 'GB', 'title' => /*t(*/'Lambeth'/*)*/, 'tag' => '0'),
    'GB-LCE' => array('country' => 'GB', 'title' => /*t(*/'Leicester'/*)*/, 'tag' => '0'),
    'GB-LDS' => array('country' => 'GB', 'title' => /*t(*/'Leeds'/*)*/, 'tag' => '0'),
    'GB-LEC' => array(
      'country' => 'GB',
      'title' => /*t(*/'Leicestershire'/*)*/,
      'tag' => '0'
    ),
    'GB-LEW' => array('country' => 'GB', 'title' => /*t(*/'Lewisham'/*)*/, 'tag' => '0'),
    'GB-LIN' => array(
      'country' => 'GB',
      'title' => /*t(*/'Lincolnshire'/*)*/,
      'tag' => '0'
    ),
    'GB-LIV' => array('country' => 'GB', 'title' => /*t(*/'Liverpool'/*)*/, 'tag' => '0'),
    'GB-LMV' => array('country' => 'GB', 'title' => /*t(*/'Limavady'/*)*/, 'tag' => '0'),
    'GB-LND' => array(
      'country' => 'GB',
      'title' => /*t(*/'London City of'/*)*/,
      'tag' => '0'
    ),
    'GB-LRN' => array('country' => 'GB', 'title' => /*t(*/'Larne'/*)*/, 'tag' => '0'),
    'GB-LSB' => array('country' => 'GB', 'title' => /*t(*/'Lisburn'/*)*/, 'tag' => '0'),
    'GB-LUT' => array('country' => 'GB', 'title' => /*t(*/'Luton'/*)*/, 'tag' => '0'),
    'GB-MAN' => array('country' => 'GB', 'title' => /*t(*/'Manchester'/*)*/, 'tag' => '0'),
    'GB-MDB' => array(
      'country' => 'GB',
      'title' => /*t(*/'Middlesbrough'/*)*/,
      'tag' => '0'
    ),
    'GB-MDW' => array('country' => 'GB', 'title' => /*t(*/'Medway'/*)*/, 'tag' => '0'),
    'GB-MFT' => array(
      'country' => 'GB',
      'title' => /*t(*/'Magherafelt'/*)*/,
      'tag' => '0'
    ),
    'GB-MIK' => array(
      'country' => 'GB',
      'title' => /*t(*/'Milton Keynes'/*)*/,
      'tag' => '0'
    ),
    'GB-MLN' => array('country' => 'GB', 'title' => /*t(*/'Midlothian'/*)*/, 'tag' => '0'),
    'GB-MON' => array(
      'country' => 'GB',
      'title' => /*t(*/'Monmouthshire'/*)*/,
      'tag' => '0'
    ),
    'GB-MRT' => array('country' => 'GB', 'title' => /*t(*/'Merton'/*)*/, 'tag' => '0'),
    'GB-MRY' => array('country' => 'GB', 'title' => /*t(*/'Moray'/*)*/, 'tag' => '0'),
    'GB-MTY' => array(
      'country' => 'GB',
      'title' => /*t(*/'Merthyr Tydfil'/*)*/,
      'tag' => '0'
    ),
    'GB-MYL' => array('country' => 'GB', 'title' => /*t(*/'Moyle'/*)*/, 'tag' => '0'),
    'GB-NAY' => array(
      'country' => 'GB',
      'title' => /*t(*/'North Ayrshire'/*)*/,
      'tag' => '0'
    ),
    'GB-NBL' => array(
      'country' => 'GB',
      'title' => /*t(*/'Northumberland'/*)*/,
      'tag' => '0'
    ),
    'GB-NDN' => array('country' => 'GB', 'title' => /*t(*/'North Down'/*)*/, 'tag' => '0'),
    'GB-NEL' => array(
      'country' => 'GB',
      'title' => /*t(*/'North East Lincolnshire'/*)*/,
      'tag' => '0'
    ),
    'GB-NET' => array(
      'country' => 'GB',
      'title' => /*t(*/'Newcastle upon Tyne'/*)*/,
      'tag' => '0'
    ),
    'GB-NFK' => array('country' => 'GB', 'title' => /*t(*/'Norfolk'/*)*/, 'tag' => '0'),
    'GB-NGM' => array('country' => 'GB', 'title' => /*t(*/'Nottingham'/*)*/, 'tag' => '0'),
    'GB-NLK' => array(
      'country' => 'GB',
      'title' => /*t(*/'North Lanarkshire'/*)*/,
      'tag' => '0'
    ),
    'GB-NLN' => array(
      'country' => 'GB',
      'title' => /*t(*/'North Lincolnshire'/*)*/,
      'tag' => '0'
    ),
    'GB-NSM' => array(
      'country' => 'GB',
      'title' => /*t(*/'North Somerset'/*)*/,
      'tag' => '0'
    ),
    'GB-NTA' => array(
      'country' => 'GB',
      'title' => /*t(*/'Newtownabbey'/*)*/,
      'tag' => '0'
    ),
    'GB-NTH' => array(
      'country' => 'GB',
      'title' => /*t(*/'Northamptonshire'/*)*/,
      'tag' => '0'
    ),
    'GB-NTL' => array(
      'country' => 'GB',
      'title' => /*t(*/'Neath Port Talbot'/*)*/,
      'tag' => '0'
    ),
    'GB-NTT' => array(
      'country' => 'GB',
      'title' => /*t(*/'Nottinghamshire'/*)*/,
      'tag' => '0'
    ),
    'GB-NTY' => array(
      'country' => 'GB',
      'title' => /*t(*/'North Tyneside'/*)*/,
      'tag' => '0'
    ),
    'GB-NWM' => array('country' => 'GB', 'title' => /*t(*/'Newham'/*)*/, 'tag' => '0'),
    'GB-NWP' => array('country' => 'GB', 'title' => /*t(*/'Newport'/*)*/, 'tag' => '0'),
    'GB-NYK' => array(
      'country' => 'GB',
      'title' => /*t(*/'North Yorkshire'/*)*/,
      'tag' => '0'
    ),
    'GB-NYM' => array(
      'country' => 'GB',
      'title' => /*t(*/'Newry and Mourne'/*)*/,
      'tag' => '0'
    ),
    'GB-OLD' => array('country' => 'GB', 'title' => /*t(*/'Oldham'/*)*/, 'tag' => '0'),
    'GB-OMH' => array('country' => 'GB', 'title' => /*t(*/'Omagh'/*)*/, 'tag' => '0'),
    'GB-ORK' => array(
      'country' => 'GB',
      'title' => /*t(*/'Orkney Islands'/*)*/,
      'tag' => '0'
    ),
    'GB-OXF' => array(
      'country' => 'GB',
      'title' => /*t(*/'Oxfordshire'/*)*/,
      'tag' => '0'
    ),
    'GB-PEM' => array(
      'country' => 'GB',
      'title' => /*t(*/'Pembrokeshire'/*)*/,
      'tag' => '0'
    ),
    'GB-PKN' => array(
      'country' => 'GB',
      'title' => /*t(*/'Perth and Kinross'/*)*/,
      'tag' => '0'
    ),
    'GB-PLY' => array('country' => 'GB', 'title' => /*t(*/'Plymouth'/*)*/, 'tag' => '0'),
    'GB-POL' => array('country' => 'GB', 'title' => /*t(*/'Poole'/*)*/, 'tag' => '0'),
    'GB-POR' => array('country' => 'GB', 'title' => /*t(*/'Portsmouth'/*)*/, 'tag' => '0'),
    'GB-POW' => array('country' => 'GB', 'title' => /*t(*/'Powys'/*)*/, 'tag' => '0'),
    'GB-PTE' => array(
      'country' => 'GB',
      'title' => /*t(*/'Peterborough'/*)*/,
      'tag' => '0'
    ),
    'GB-RCC' => array(
      'country' => 'GB',
      'title' => /*t(*/'Redcar and Cleveland'/*)*/,
      'tag' => '0'
    ),
    'GB-RCH' => array('country' => 'GB', 'title' => /*t(*/'Rochdale'/*)*/, 'tag' => '0'),
    'GB-RCT' => array(
      'country' => 'GB',
      'title' => /*t(*/'Rhondda Cynon Taf'/*)*/,
      'tag' => '0'
    ),
    'GB-RDB' => array('country' => 'GB', 'title' => /*t(*/'Redbridge'/*)*/, 'tag' => '0'),
    'GB-RDG' => array('country' => 'GB', 'title' => /*t(*/'Reading'/*)*/, 'tag' => '0'),
    'GB-RFW' => array(
      'country' => 'GB',
      'title' => /*t(*/'Renfrewshire'/*)*/,
      'tag' => '0'
    ),
    'GB-RIC' => array(
      'country' => 'GB',
      'title' => /*t(*/'Richmond upon Thames'/*)*/,
      'tag' => '0'
    ),
    'GB-ROT' => array('country' => 'GB', 'title' => /*t(*/'Rotherham'/*)*/, 'tag' => '0'),
    'GB-RUT' => array('country' => 'GB', 'title' => /*t(*/'Rutland'/*)*/, 'tag' => '0'),
    'GB-SAW' => array('country' => 'GB', 'title' => /*t(*/'Sandwell'/*)*/, 'tag' => '0'),
    'GB-SAY' => array(
      'country' => 'GB',
      'title' => /*t(*/'South Ayrshire'/*)*/,
      'tag' => '0'
    ),
    'GB-SCB' => array(
      'country' => 'GB',
      'title' => /*t(*/'Scottish Borders The'/*)*/,
      'tag' => '0'
    ),
    'GB-SFK' => array('country' => 'GB', 'title' => /*t(*/'Suffolk'/*)*/, 'tag' => '0'),
    'GB-SFT' => array('country' => 'GB', 'title' => /*t(*/'Sefton'/*)*/, 'tag' => '0'),
    'GB-SGC' => array(
      'country' => 'GB',
      'title' => /*t(*/'South Gloucestershire'/*)*/,
      'tag' => '0'
    ),
    'GB-SHF' => array('country' => 'GB', 'title' => /*t(*/'Sheffield'/*)*/, 'tag' => '0'),
    'GB-SHN' => array('country' => 'GB', 'title' => /*t(*/'St Helens'/*)*/, 'tag' => '0'),
    'GB-SHR' => array('country' => 'GB', 'title' => /*t(*/'Shropshire'/*)*/, 'tag' => '0'),
    'GB-SKP' => array('country' => 'GB', 'title' => /*t(*/'Stockport'/*)*/, 'tag' => '0'),
    'GB-SLF' => array('country' => 'GB', 'title' => /*t(*/'Salford'/*)*/, 'tag' => '0'),
    'GB-SLG' => array('country' => 'GB', 'title' => /*t(*/'Slough'/*)*/, 'tag' => '0'),
    'GB-SLK' => array(
      'country' => 'GB',
      'title' => /*t(*/'South Lanarkshire'/*)*/,
      'tag' => '0'
    ),
    'GB-SND' => array('country' => 'GB', 'title' => /*t(*/'Sunderland'/*)*/, 'tag' => '0'),
    'GB-SOL' => array('country' => 'GB', 'title' => /*t(*/'Solihull'/*)*/, 'tag' => '0'),
    'GB-SOM' => array('country' => 'GB', 'title' => /*t(*/'Somerset'/*)*/, 'tag' => '0'),
    'GB-SOS' => array(
      'country' => 'GB',
      'title' => /*t(*/'Southend-on-Sea'/*)*/,
      'tag' => '0'
    ),
    'GB-SRY' => array('country' => 'GB', 'title' => /*t(*/'Surrey'/*)*/, 'tag' => '0'),
    'GB-STB' => array('country' => 'GB', 'title' => /*t(*/'Strabane'/*)*/, 'tag' => '0'),
    'GB-STE' => array(
      'country' => 'GB',
      'title' => /*t(*/'Stoke-on-Trent'/*)*/,
      'tag' => '0'
    ),
    'GB-STG' => array('country' => 'GB', 'title' => /*t(*/'Stirling'/*)*/, 'tag' => '0'),
    'GB-STH' => array(
      'country' => 'GB',
      'title' => /*t(*/'Southampton'/*)*/,
      'tag' => '0'
    ),
    'GB-STN' => array('country' => 'GB', 'title' => /*t(*/'Sutton'/*)*/, 'tag' => '0'),
    'GB-STS' => array(
      'country' => 'GB',
      'title' => /*t(*/'Staffordshire'/*)*/,
      'tag' => '0'
    ),
    'GB-STT' => array(
      'country' => 'GB',
      'title' => /*t(*/'Stockton-on-Tees'/*)*/,
      'tag' => '0'
    ),
    'GB-STY' => array(
      'country' => 'GB',
      'title' => /*t(*/'South Tyneside'/*)*/,
      'tag' => '0'
    ),
    'GB-SWA' => array('country' => 'GB', 'title' => /*t(*/'Swansea'/*)*/, 'tag' => '0'),
    'GB-SWD' => array('country' => 'GB', 'title' => /*t(*/'Swindon'/*)*/, 'tag' => '0'),
    'GB-SWK' => array('country' => 'GB', 'title' => /*t(*/'Southwark'/*)*/, 'tag' => '0'),
    'GB-TAM' => array('country' => 'GB', 'title' => /*t(*/'Tameside'/*)*/, 'tag' => '0'),
    'GB-TFW' => array(
      'country' => 'GB',
      'title' => /*t(*/'Telford and Wrekin'/*)*/,
      'tag' => '0'
    ),
    'GB-THR' => array('country' => 'GB', 'title' => /*t(*/'Thurrock'/*)*/, 'tag' => '0'),
    'GB-TOB' => array('country' => 'GB', 'title' => /*t(*/'Torbay'/*)*/, 'tag' => '0'),
    'GB-TOF' => array('country' => 'GB', 'title' => /*t(*/'Torfaen'/*)*/, 'tag' => '0'),
    'GB-TRF' => array('country' => 'GB', 'title' => /*t(*/'Trafford'/*)*/, 'tag' => '0'),
    'GB-TWH' => array(
      'country' => 'GB',
      'title' => /*t(*/'Tower Hamlets'/*)*/,
      'tag' => '0'
    ),
    'GB-VGL' => array(
      'country' => 'GB',
      'title' => /*t(*/'Vale of Glamorgan'/*)*/,
      'tag' => '0'
    ),
    'GB-WAR' => array(
      'country' => 'GB',
      'title' => /*t(*/'Warwickshire'/*)*/,
      'tag' => '0'
    ),
    'GB-WBK' => array(
      'country' => 'GB',
      'title' => /*t(*/'West Berkshire'/*)*/,
      'tag' => '0'
    ),
    'GB-WDU' => array(
      'country' => 'GB',
      'title' => /*t(*/'West Dunbartonshire'/*)*/,
      'tag' => '0'
    ),
    'GB-WFT' => array(
      'country' => 'GB',
      'title' => /*t(*/'Waltham Forest'/*)*/,
      'tag' => '0'
    ),
    'GB-WGN' => array('country' => 'GB', 'title' => /*t(*/'Wigan'/*)*/, 'tag' => '0'),
    'GB-WIL' => array('country' => 'GB', 'title' => /*t(*/'Wiltshire'/*)*/, 'tag' => '0'),
    'GB-WKF' => array('country' => 'GB', 'title' => /*t(*/'Wakefield'/*)*/, 'tag' => '0'),
    'GB-WLL' => array('country' => 'GB', 'title' => /*t(*/'Walsall'/*)*/, 'tag' => '0'),
    'GB-WLN' => array(
      'country' => 'GB',
      'title' => /*t(*/'West Lothian'/*)*/,
      'tag' => '0'
    ),
    'GB-WLV' => array(
      'country' => 'GB',
      'title' => /*t(*/'Wolverhampton'/*)*/,
      'tag' => '0'
    ),
    'GB-WND' => array('country' => 'GB', 'title' => /*t(*/'Wandsworth'/*)*/, 'tag' => '0'),
    'GB-WNM' => array(
      'country' => 'GB',
      'title' => /*t(*/'Windsor and Maidenhead'/*)*/,
      'tag' => '0'
    ),
    'GB-WOK' => array('country' => 'GB', 'title' => /*t(*/'Wokingham'/*)*/, 'tag' => '0'),
    'GB-WOR' => array(
      'country' => 'GB',
      'title' => /*t(*/'Worcestershire'/*)*/,
      'tag' => '0'
    ),
    'GB-WRL' => array('country' => 'GB', 'title' => /*t(*/'Wirral'/*)*/, 'tag' => '0'),
    'GB-WRT' => array('country' => 'GB', 'title' => /*t(*/'Warrington'/*)*/, 'tag' => '0'),
    'GB-WRX' => array('country' => 'GB', 'title' => /*t(*/'Wrexham'/*)*/, 'tag' => '0'),
    'GB-WSM' => array(
      'country' => 'GB',
      'title' => /*t(*/'Westminster'/*)*/,
      'tag' => '0'
    ),
    'GB-WSX' => array(
      'country' => 'GB',
      'title' => /*t(*/'West Sussex'/*)*/,
      'tag' => '0'
    ),
    'GB-YOR' => array('country' => 'GB', 'title' => /*t(*/'York'/*)*/, 'tag' => '0'),
    'GB-ZET' => array(
      'country' => 'GB',
      'title' => /*t(*/'Shetland Islands'/*)*/,
      'tag' => '0'
    ),
    'GD-A' => array('country' => 'GD', 'title' => /*t(*/'Saint Andrew'/*)*/, 'tag' => '0'),
    'GD-C' => array('country' => 'GD', 'title' => /*t(*/'Carriacou'/*)*/, 'tag' => '0'),
    'GD-D' => array('country' => 'GD', 'title' => /*t(*/'Saint David'/*)*/, 'tag' => '0'),
    'GD-G' => array('country' => 'GD', 'title' => /*t(*/'Saint George'/*)*/, 'tag' => '0'),
    'GD-J' => array('country' => 'GD', 'title' => /*t(*/'Saint John'/*)*/, 'tag' => '0'),
    'GD-M' => array('country' => 'GD', 'title' => /*t(*/'Saint Mark'/*)*/, 'tag' => '0'),
    'GD-P' => array(
      'country' => 'GD',
      'title' => /*t(*/'Saint Patrick'/*)*/,
      'tag' => '0'
    ),
    'GD-Q' => array(
      'country' => 'GD',
      'title' => /*t(*/'Petit Martinique'/*)*/,
      'tag' => '0'
    ),
    'GE-AB' => array('country' => 'GE', 'title' => /*t(*/'Abkhazia'/*)*/, 'tag' => '0'),
    'GE-AJ' => array('country' => 'GE', 'title' => /*t(*/'Ajaria'/*)*/, 'tag' => '0'),
    'GE-GU' => array('country' => 'GE', 'title' => /*t(*/'Guria'/*)*/, 'tag' => '0'),
    'GE-IM' => array('country' => 'GE', 'title' => /*t(*/'Imereti'/*)*/, 'tag' => '0'),
    'GE-KA' => array('country' => 'GE', 'title' => /*t(*/'Kakheti'/*)*/, 'tag' => '0'),
    'GE-KK' => array(
      'country' => 'GE',
      'title' => /*t(*/'Kvemo Kartli'/*)*/,
      'tag' => '0'
    ),
    'GE-MM' => array(
      'country' => 'GE',
      'title' => /*t(*/'Mtskheta-Mtianeti'/*)*/,
      'tag' => '0'
    ),
    'GE-RL' => array(
      'country' => 'GE',
      'title' => /*t(*/'Racha Lechkhumi and Kvemo Svaneti'/*)*/,
      'tag' => '0'
    ),
    'GE-SJ' => array(
      'country' => 'GE',
      'title' => /*t(*/'Samtskhe-Javakheti'/*)*/,
      'tag' => '0'
    ),
    'GE-SK' => array(
      'country' => 'GE',
      'title' => /*t(*/'Shida Kartli'/*)*/,
      'tag' => '0'
    ),
    'GE-SZ' => array(
      'country' => 'GE',
      'title' => /*t(*/'Samegrelo-Zemo Svaneti'/*)*/,
      'tag' => '0'
    ),
    'GE-TB' => array('country' => 'GE', 'title' => /*t(*/'Tbilisi'/*)*/, 'tag' => '0'),
    'GH-AS' => array(
      'country' => 'GH',
      'title' => /*t(*/'Ashanti Region'/*)*/,
      'tag' => '0'
    ),
    'GH-BA' => array(
      'country' => 'GH',
      'title' => /*t(*/'Brong-Ahafo Region'/*)*/,
      'tag' => '0'
    ),
    'GH-CE' => array(
      'country' => 'GH',
      'title' => /*t(*/'Central Region'/*)*/,
      'tag' => '0'
    ),
    'GH-EA' => array(
      'country' => 'GH',
      'title' => /*t(*/'Eastern Region'/*)*/,
      'tag' => '0'
    ),
    'GH-GA' => array(
      'country' => 'GH',
      'title' => /*t(*/'Greater Accra Region'/*)*/,
      'tag' => '0'
    ),
    'GH-NO' => array(
      'country' => 'GH',
      'title' => /*t(*/'Northern Region'/*)*/,
      'tag' => '0'
    ),
    'GH-UE' => array(
      'country' => 'GH',
      'title' => /*t(*/'Upper East Region'/*)*/,
      'tag' => '0'
    ),
    'GH-UW' => array(
      'country' => 'GH',
      'title' => /*t(*/'Upper West Region'/*)*/,
      'tag' => '0'
    ),
    'GH-VO' => array(
      'country' => 'GH',
      'title' => /*t(*/'Volta Region'/*)*/,
      'tag' => '0'
    ),
    'GH-WE' => array(
      'country' => 'GH',
      'title' => /*t(*/'Western Region'/*)*/,
      'tag' => '0'
    ),
    'GL-A' => array('country' => 'GL', 'title' => /*t(*/'Avannaa'/*)*/, 'tag' => '0'),
    'GL-K' => array('country' => 'GL', 'title' => /*t(*/'Kitaa'/*)*/, 'tag' => '0'),
    'GL-T' => array('country' => 'GL', 'title' => /*t(*/'Tunu'/*)*/, 'tag' => '0'),
    'GM-BJ' => array('country' => 'GM', 'title' => /*t(*/'Banjul'/*)*/, 'tag' => '0'),
    'GM-BR' => array('country' => 'GM', 'title' => /*t(*/'Brikama'/*)*/, 'tag' => '0'),
    'GM-BS' => array('country' => 'GM', 'title' => /*t(*/'Basse'/*)*/, 'tag' => '0'),
    'GM-CR' => array(
      'country' => 'GM',
      'title' => /*t(*/'Central River'/*)*/,
      'tag' => '0'
    ),
    'GM-JA' => array('country' => 'GM', 'title' => /*t(*/'Janjangbure'/*)*/, 'tag' => '0'),
    'GM-KA' => array('country' => 'GM', 'title' => /*t(*/'Kanifeng'/*)*/, 'tag' => '0'),
    'GM-KE' => array('country' => 'GM', 'title' => /*t(*/'Kerewan'/*)*/, 'tag' => '0'),
    'GM-KU' => array('country' => 'GM', 'title' => /*t(*/'Kuntaur'/*)*/, 'tag' => '0'),
    'GM-LR' => array('country' => 'GM', 'title' => /*t(*/'Lower River'/*)*/, 'tag' => '0'),
    'GM-MA' => array('country' => 'GM', 'title' => /*t(*/'Mansakonko'/*)*/, 'tag' => '0'),
    'GM-NB' => array('country' => 'GM', 'title' => /*t(*/'North Bank'/*)*/, 'tag' => '0'),
    'GM-UR' => array('country' => 'GM', 'title' => /*t(*/'Upper River'/*)*/, 'tag' => '0'),
    'GM-WE' => array('country' => 'GM', 'title' => /*t(*/'Western'/*)*/, 'tag' => '0'),
    'GN-BFA' => array('country' => 'GN', 'title' => /*t(*/'Boffa'/*)*/, 'tag' => '0'),
    'GN-BOK' => array('country' => 'GN', 'title' => /*t(*/'Boke'/*)*/, 'tag' => '0'),
    'GN-BYL' => array('country' => 'GN', 'title' => /*t(*/'Beyla'/*)*/, 'tag' => '0'),
    'GN-CNK' => array('country' => 'GN', 'title' => /*t(*/'Conakry'/*)*/, 'tag' => '0'),
    'GN-COY' => array('country' => 'GN', 'title' => /*t(*/'Coyah'/*)*/, 'tag' => '0'),
    'GN-DBL' => array('country' => 'GN', 'title' => /*t(*/'Dabola'/*)*/, 'tag' => '0'),
    'GN-DBR' => array('country' => 'GN', 'title' => /*t(*/'Dubreka'/*)*/, 'tag' => '0'),
    'GN-DGR' => array('country' => 'GN', 'title' => /*t(*/'Dinguiraye'/*)*/, 'tag' => '0'),
    'GN-DLB' => array('country' => 'GN', 'title' => /*t(*/'Dalaba'/*)*/, 'tag' => '0'),
    'GN-FRC' => array('country' => 'GN', 'title' => /*t(*/'Forecariah'/*)*/, 'tag' => '0'),
    'GN-FRI' => array('country' => 'GN', 'title' => /*t(*/'Fria'/*)*/, 'tag' => '0'),
    'GN-FRN' => array('country' => 'GN', 'title' => /*t(*/'Faranah'/*)*/, 'tag' => '0'),
    'GN-GAO' => array('country' => 'GN', 'title' => /*t(*/'Gaoual'/*)*/, 'tag' => '0'),
    'GN-GCD' => array('country' => 'GN', 'title' => /*t(*/'Gueckedou'/*)*/, 'tag' => '0'),
    'GN-KBA' => array('country' => 'GN', 'title' => /*t(*/'Koubia'/*)*/, 'tag' => '0'),
    'GN-KDA' => array('country' => 'GN', 'title' => /*t(*/'Koundara'/*)*/, 'tag' => '0'),
    'GN-KND' => array('country' => 'GN', 'title' => /*t(*/'Kindia'/*)*/, 'tag' => '0'),
    'GN-KNK' => array('country' => 'GN', 'title' => /*t(*/'Kankan'/*)*/, 'tag' => '0'),
    'GN-KRA' => array('country' => 'GN', 'title' => /*t(*/'Kouroussa'/*)*/, 'tag' => '0'),
    'GN-KRN' => array('country' => 'GN', 'title' => /*t(*/'Kerouane'/*)*/, 'tag' => '0'),
    'GN-KSD' => array(
      'country' => 'GN',
      'title' => /*t(*/'Kissidougou'/*)*/,
      'tag' => '0'
    ),
    'GN-LAB' => array('country' => 'GN', 'title' => /*t(*/'Labe'/*)*/, 'tag' => '0'),
    'GN-LLM' => array('country' => 'GN', 'title' => /*t(*/'Lelouma'/*)*/, 'tag' => '0'),
    'GN-LOL' => array('country' => 'GN', 'title' => /*t(*/'Lola'/*)*/, 'tag' => '0'),
    'GN-MAL' => array('country' => 'GN', 'title' => /*t(*/'Mali'/*)*/, 'tag' => '0'),
    'GN-MAM' => array('country' => 'GN', 'title' => /*t(*/'Mamou'/*)*/, 'tag' => '0'),
    'GN-MAN' => array('country' => 'GN', 'title' => /*t(*/'Mandiana'/*)*/, 'tag' => '0'),
    'GN-MCT' => array('country' => 'GN', 'title' => /*t(*/'Macenta'/*)*/, 'tag' => '0'),
    'GN-NZR' => array('country' => 'GN', 'title' => /*t(*/'Nzerekore'/*)*/, 'tag' => '0'),
    'GN-PIT' => array('country' => 'GN', 'title' => /*t(*/'Pita'/*)*/, 'tag' => '0'),
    'GN-SIG' => array('country' => 'GN', 'title' => /*t(*/'Siguiri'/*)*/, 'tag' => '0'),
    'GN-TLM' => array('country' => 'GN', 'title' => /*t(*/'Telimele'/*)*/, 'tag' => '0'),
    'GN-TOG' => array('country' => 'GN', 'title' => /*t(*/'Tougue'/*)*/, 'tag' => '0'),
    'GN-YOM' => array('country' => 'GN', 'title' => /*t(*/'Yomou'/*)*/, 'tag' => '0'),
    'GQ-AN' => array(
      'country' => 'GQ',
      'title' => /*t(*/'Provincia Annobon'/*)*/,
      'tag' => '0'
    ),
    'GQ-BN' => array(
      'country' => 'GQ',
      'title' => /*t(*/'Provincia Bioko Norte'/*)*/,
      'tag' => '0'
    ),
    'GQ-BS' => array(
      'country' => 'GQ',
      'title' => /*t(*/'Provincia Bioko Sur'/*)*/,
      'tag' => '0'
    ),
    'GQ-CS' => array(
      'country' => 'GQ',
      'title' => /*t(*/'Provincia Centro Sur'/*)*/,
      'tag' => '0'
    ),
    'GQ-KN' => array(
      'country' => 'GQ',
      'title' => /*t(*/'Provincia Kie-Ntem'/*)*/,
      'tag' => '0'
    ),
    'GQ-LI' => array(
      'country' => 'GQ',
      'title' => /*t(*/'Provincia Litoral'/*)*/,
      'tag' => '0'
    ),
    'GQ-WN' => array(
      'country' => 'GQ',
      'title' => /*t(*/'Provincia Wele-Nzas'/*)*/,
      'tag' => '0'
    ),
    'GR-AT' => array('country' => 'GR', 'title' => /*t(*/'Attica'/*)*/, 'tag' => '0'),
    'GR-CM' => array(
      'country' => 'GR',
      'title' => /*t(*/'Central Macedonia'/*)*/,
      'tag' => '0'
    ),
    'GR-CN' => array(
      'country' => 'GR',
      'title' => /*t(*/'Central Greece'/*)*/,
      'tag' => '0'
    ),
    'GR-CR' => array('country' => 'GR', 'title' => /*t(*/'Crete'/*)*/, 'tag' => '0'),
    'GR-EM' => array(
      'country' => 'GR',
      'title' => /*t(*/'East Macedonia and Thrace'/*)*/,
      'tag' => '0'
    ),
    'GR-EP' => array('country' => 'GR', 'title' => /*t(*/'Epirus'/*)*/, 'tag' => '0'),
    'GR-II' => array(
      'country' => 'GR',
      'title' => /*t(*/'Ionian Islands'/*)*/,
      'tag' => '0'
    ),
    'GR-NA' => array(
      'country' => 'GR',
      'title' => /*t(*/'North Aegean'/*)*/,
      'tag' => '0'
    ),
    'GR-PP' => array(
      'country' => 'GR',
      'title' => /*t(*/'Peloponnesos'/*)*/,
      'tag' => '0'
    ),
    'GR-SA' => array(
      'country' => 'GR',
      'title' => /*t(*/'South Aegean'/*)*/,
      'tag' => '0'
    ),
    'GR-TH' => array('country' => 'GR', 'title' => /*t(*/'Thessaly'/*)*/, 'tag' => '0'),
    'GR-WG' => array('country' => 'GR', 'title' => /*t(*/'West Greece'/*)*/, 'tag' => '0'),
    'GR-WM' => array(
      'country' => 'GR',
      'title' => /*t(*/'West Macedonia'/*)*/,
      'tag' => '0'
    ),
    'GT-AV' => array(
      'country' => 'GT',
      'title' => /*t(*/'Alta Verapaz'/*)*/,
      'tag' => '0'
    ),
    'GT-BV' => array(
      'country' => 'GT',
      'title' => /*t(*/'Baja Verapaz'/*)*/,
      'tag' => '0'
    ),
    'GT-CM' => array(
      'country' => 'GT',
      'title' => /*t(*/'Chimaltenango'/*)*/,
      'tag' => '0'
    ),
    'GT-CQ' => array('country' => 'GT', 'title' => /*t(*/'Chiquimula'/*)*/, 'tag' => '0'),
    'GT-ES' => array('country' => 'GT', 'title' => /*t(*/'Escuintla'/*)*/, 'tag' => '0'),
    'GT-GU' => array('country' => 'GT', 'title' => /*t(*/'Guatemala'/*)*/, 'tag' => '0'),
    'GT-HU' => array(
      'country' => 'GT',
      'title' => /*t(*/'Huehuetenango'/*)*/,
      'tag' => '0'
    ),
    'GT-IZ' => array('country' => 'GT', 'title' => /*t(*/'Izabal'/*)*/, 'tag' => '0'),
    'GT-JA' => array('country' => 'GT', 'title' => /*t(*/'Jalapa'/*)*/, 'tag' => '0'),
    'GT-JU' => array('country' => 'GT', 'title' => /*t(*/'Jutiapa'/*)*/, 'tag' => '0'),
    'GT-PE' => array('country' => 'GT', 'title' => /*t(*/'El Peten'/*)*/, 'tag' => '0'),
    'GT-PR' => array('country' => 'GT', 'title' => /*t(*/'El Progreso'/*)*/, 'tag' => '0'),
    'GT-QC' => array('country' => 'GT', 'title' => /*t(*/'El Quiche'/*)*/, 'tag' => '0'),
    'GT-QZ' => array(
      'country' => 'GT',
      'title' => /*t(*/'Quetzaltenango'/*)*/,
      'tag' => '0'
    ),
    'GT-RE' => array('country' => 'GT', 'title' => /*t(*/'Retalhuleu'/*)*/, 'tag' => '0'),
    'GT-SM' => array('country' => 'GT', 'title' => /*t(*/'San Marcos'/*)*/, 'tag' => '0'),
    'GT-SO' => array('country' => 'GT', 'title' => /*t(*/'Solola'/*)*/, 'tag' => '0'),
    'GT-SR' => array('country' => 'GT', 'title' => /*t(*/'Santa Rosa'/*)*/, 'tag' => '0'),
    'GT-ST' => array(
      'country' => 'GT',
      'title' => /*t(*/'Sacatepequez'/*)*/,
      'tag' => '0'
    ),
    'GT-SU' => array(
      'country' => 'GT',
      'title' => /*t(*/'Suchitepequez'/*)*/,
      'tag' => '0'
    ),
    'GT-TO' => array('country' => 'GT', 'title' => /*t(*/'Totonicapan'/*)*/, 'tag' => '0'),
    'GT-ZA' => array('country' => 'GT', 'title' => /*t(*/'Zacapa'/*)*/, 'tag' => '0'),
    'GW-BB' => array(
      'country' => 'GW',
      'title' => /*t(*/'Biombo Region'/*)*/,
      'tag' => '0'
    ),
    'GW-BF' => array(
      'country' => 'GW',
      'title' => /*t(*/'Bafata Region'/*)*/,
      'tag' => '0'
    ),
    'GW-BL' => array(
      'country' => 'GW',
      'title' => /*t(*/'Bolama Region'/*)*/,
      'tag' => '0'
    ),
    'GW-BS' => array(
      'country' => 'GW',
      'title' => /*t(*/'Bissau Region'/*)*/,
      'tag' => '0'
    ),
    'GW-CA' => array(
      'country' => 'GW',
      'title' => /*t(*/'Cacheu Region'/*)*/,
      'tag' => '0'
    ),
    'GW-GA' => array('country' => 'GW', 'title' => /*t(*/'Gabu Region'/*)*/, 'tag' => '0'),
    'GW-OI' => array('country' => 'GW', 'title' => /*t(*/'Oio Region'/*)*/, 'tag' => '0'),
    'GW-QU' => array(
      'country' => 'GW',
      'title' => /*t(*/'Quinara Region'/*)*/,
      'tag' => '0'
    ),
    'GW-TO' => array(
      'country' => 'GW',
      'title' => /*t(*/'Tombali Region'/*)*/,
      'tag' => '0'
    ),
    'GY-BW' => array(
      'country' => 'GY',
      'title' => /*t(*/'Barima-Waini'/*)*/,
      'tag' => '0'
    ),
    'GY-CM' => array(
      'country' => 'GY',
      'title' => /*t(*/'Cuyuni-Mazaruni'/*)*/,
      'tag' => '0'
    ),
    'GY-DM' => array(
      'country' => 'GY',
      'title' => /*t(*/'Demerara-Mahaica'/*)*/,
      'tag' => '0'
    ),
    'GY-EC' => array(
      'country' => 'GY',
      'title' => /*t(*/'East Berbice-Corentyne'/*)*/,
      'tag' => '0'
    ),
    'GY-EW' => array(
      'country' => 'GY',
      'title' => /*t(*/'Essequibo Islands-West Demerara'/*)*/,
      'tag' => '0'
    ),
    'GY-MB' => array(
      'country' => 'GY',
      'title' => /*t(*/'Mahaica-Berbice'/*)*/,
      'tag' => '0'
    ),
    'GY-PI' => array(
      'country' => 'GY',
      'title' => /*t(*/'Potaro-Siparuni'/*)*/,
      'tag' => '0'
    ),
    'GY-PM' => array(
      'country' => 'GY',
      'title' => /*t(*/'Pomeroon-Supenaam'/*)*/,
      'tag' => '0'
    ),
    'GY-UD' => array(
      'country' => 'GY',
      'title' => /*t(*/'Upper Demerara-Berbice'/*)*/,
      'tag' => '0'
    ),
    'GY-UT' => array(
      'country' => 'GY',
      'title' => /*t(*/'Upper Takutu-Upper Essequibo'/*)*/,
      'tag' => '0'
    ),
    'HK-HCW' => array(
      'country' => 'HK',
      'title' => /*t(*/'Central and Western Hong Kong Island'/*)*/,
      'tag' => '0'
    ),
    'HK-HEA' => array(
      'country' => 'HK',
      'title' => /*t(*/'Eastern Hong Kong Island'/*)*/,
      'tag' => '0'
    ),
    'HK-HSO' => array(
      'country' => 'HK',
      'title' => /*t(*/'Southern Hong Kong Island'/*)*/,
      'tag' => '0'
    ),
    'HK-HWC' => array(
      'country' => 'HK',
      'title' => /*t(*/'Wan Chai Hong Kong Island'/*)*/,
      'tag' => '0'
    ),
    'HK-KKC' => array(
      'country' => 'HK',
      'title' => /*t(*/'Kowloon City Kowloon'/*)*/,
      'tag' => '0'
    ),
    'HK-KKT' => array(
      'country' => 'HK',
      'title' => /*t(*/'Kwun Tong Kowloon'/*)*/,
      'tag' => '0'
    ),
    'HK-KSS' => array(
      'country' => 'HK',
      'title' => /*t(*/'Sham Shui Po Kowloon'/*)*/,
      'tag' => '0'
    ),
    'HK-KWT' => array(
      'country' => 'HK',
      'title' => /*t(*/'Wong Tai Sin Kowloon'/*)*/,
      'tag' => '0'
    ),
    'HK-KYT' => array(
      'country' => 'HK',
      'title' => /*t(*/'Yau Tsim Mong Kowloon'/*)*/,
      'tag' => '0'
    ),
    'HK-NIS' => array(
      'country' => 'HK',
      'title' => /*t(*/'Islands New Territories'/*)*/,
      'tag' => '0'
    ),
    'HK-NKT' => array(
      'country' => 'HK',
      'title' => /*t(*/'Kwai Tsing New Territories'/*)*/,
      'tag' => '0'
    ),
    'HK-NNO' => array(
      'country' => 'HK',
      'title' => /*t(*/'North New Territories'/*)*/,
      'tag' => '0'
    ),
    'HK-NSK' => array(
      'country' => 'HK',
      'title' => /*t(*/'Sai Kung New Territories'/*)*/,
      'tag' => '0'
    ),
    'HK-NST' => array(
      'country' => 'HK',
      'title' => /*t(*/'Sha Tin New Territories'/*)*/,
      'tag' => '0'
    ),
    'HK-NTM' => array(
      'country' => 'HK',
      'title' => /*t(*/'Tuen Mun New Territories'/*)*/,
      'tag' => '0'
    ),
    'HK-NTP' => array(
      'country' => 'HK',
      'title' => /*t(*/'Tai Po New Territories'/*)*/,
      'tag' => '0'
    ),
    'HK-NTW' => array(
      'country' => 'HK',
      'title' => /*t(*/'Tsuen Wan New Territories'/*)*/,
      'tag' => '0'
    ),
    'HK-NYL' => array(
      'country' => 'HK',
      'title' => /*t(*/'Yuen Long New Territories'/*)*/,
      'tag' => '0'
    ),
    'HM-F' => array('country' => 'HM', 'title' => /*t(*/'Flat Island'/*)*/, 'tag' => '0'),
    'HM-H' => array('country' => 'HM', 'title' => /*t(*/'Heard Island'/*)*/, 'tag' => '0'),
    'HM-M' => array(
      'country' => 'HM',
      'title' => /*t(*/'McDonald Island'/*)*/,
      'tag' => '0'
    ),
    'HM-S' => array('country' => 'HM', 'title' => /*t(*/'Shag Island'/*)*/, 'tag' => '0'),
    'HN-AT' => array('country' => 'HN', 'title' => /*t(*/'Atlantida'/*)*/, 'tag' => '0'),
    'HN-CH' => array('country' => 'HN', 'title' => /*t(*/'Choluteca'/*)*/, 'tag' => '0'),
    'HN-CL' => array('country' => 'HN', 'title' => /*t(*/'Colon'/*)*/, 'tag' => '0'),
    'HN-CM' => array('country' => 'HN', 'title' => /*t(*/'Comayagua'/*)*/, 'tag' => '0'),
    'HN-CP' => array('country' => 'HN', 'title' => /*t(*/'Copan'/*)*/, 'tag' => '0'),
    'HN-CR' => array('country' => 'HN', 'title' => /*t(*/'Cortes'/*)*/, 'tag' => '0'),
    'HN-FM' => array(
      'country' => 'HN',
      'title' => /*t(*/'Francisco Morazan'/*)*/,
      'tag' => '0'
    ),
    'HN-GD' => array(
      'country' => 'HN',
      'title' => /*t(*/'Gracias a Dios'/*)*/,
      'tag' => '0'
    ),
    'HN-IB' => array(
      'country' => 'HN',
      'title' => /*t(*/'Islas de la Bahia (Bay Islands)'/*)*/,
      'tag' => '0'
    ),
    'HN-IN' => array('country' => 'HN', 'title' => /*t(*/'Intibuca'/*)*/, 'tag' => '0'),
    'HN-LE' => array('country' => 'HN', 'title' => /*t(*/'Lempira'/*)*/, 'tag' => '0'),
    'HN-OC' => array('country' => 'HN', 'title' => /*t(*/'Ocotepeque'/*)*/, 'tag' => '0'),
    'HN-OL' => array('country' => 'HN', 'title' => /*t(*/'Olancho'/*)*/, 'tag' => '0'),
    'HN-PA' => array('country' => 'HN', 'title' => /*t(*/'El Paraiso'/*)*/, 'tag' => '0'),
    'HN-PZ' => array('country' => 'HN', 'title' => /*t(*/'La Paz'/*)*/, 'tag' => '0'),
    'HN-SB' => array(
      'country' => 'HN',
      'title' => /*t(*/'Santa Barbara'/*)*/,
      'tag' => '0'
    ),
    'HN-VA' => array('country' => 'HN', 'title' => /*t(*/'Valle'/*)*/, 'tag' => '0'),
    'HN-YO' => array('country' => 'HN', 'title' => /*t(*/'Yoro'/*)*/, 'tag' => '0'),
    'HR-01' => array(
      'country' => 'HR',
      'title' => /*t(*/'Zagreb county'/*)*/,
      'tag' => '0'
    ),
    'HR-02' => array(
      'country' => 'HR',
      'title' => /*t(*/'Krapina-Zagorje county'/*)*/,
      'tag' => '0'
    ),
    'HR-03' => array(
      'country' => 'HR',
      'title' => /*t(*/'Sisak-Moslavina county'/*)*/,
      'tag' => '0'
    ),
    'HR-04' => array(
      'country' => 'HR',
      'title' => /*t(*/'Karlovac county'/*)*/,
      'tag' => '0'
    ),
    'HR-05' => array(
      'country' => 'HR',
      'title' => /*t(*/'Varazdin county'/*)*/,
      'tag' => '0'
    ),
    'HR-06' => array(
      'country' => 'HR',
      'title' => /*t(*/'Koprivnica-Krizevci county'/*)*/,
      'tag' => '0'
    ),
    'HR-07' => array(
      'country' => 'HR',
      'title' => /*t(*/'Bjelovar-Bilogora county'/*)*/,
      'tag' => '0'
    ),
    'HR-08' => array(
      'country' => 'HR',
      'title' => /*t(*/'Primorje-Gorski Kotar county'/*)*/,
      'tag' => '0'
    ),
    'HR-09' => array(
      'country' => 'HR',
      'title' => /*t(*/'Lika-Senj county'/*)*/,
      'tag' => '0'
    ),
    'HR-10' => array(
      'country' => 'HR',
      'title' => /*t(*/'Virovitica-Podravina county'/*)*/,
      'tag' => '0'
    ),
    'HR-11' => array(
      'country' => 'HR',
      'title' => /*t(*/'Pozega-Slavonia county'/*)*/,
      'tag' => '0'
    ),
    'HR-12' => array(
      'country' => 'HR',
      'title' => /*t(*/'Brod-Posavina county'/*)*/,
      'tag' => '0'
    ),
    'HR-13' => array(
      'country' => 'HR',
      'title' => /*t(*/'Zadar county'/*)*/,
      'tag' => '0'
    ),
    'HR-14' => array(
      'country' => 'HR',
      'title' => /*t(*/'Osijek-Baranja county'/*)*/,
      'tag' => '0'
    ),
    'HR-15' => array(
      'country' => 'HR',
      'title' => /*t(*/'Sibenik-Knin county'/*)*/,
      'tag' => '0'
    ),
    'HR-16' => array(
      'country' => 'HR',
      'title' => /*t(*/'Vukovar-Srijem county'/*)*/,
      'tag' => '0'
    ),
    'HR-17' => array(
      'country' => 'HR',
      'title' => /*t(*/'Split-Dalmatia county'/*)*/,
      'tag' => '0'
    ),
    'HR-18' => array(
      'country' => 'HR',
      'title' => /*t(*/'Istria county'/*)*/,
      'tag' => '0'
    ),
    'HR-19' => array(
      'country' => 'HR',
      'title' => /*t(*/'Dubrovnik-Neretva county'/*)*/,
      'tag' => '0'
    ),
    'HR-20' => array(
      'country' => 'HR',
      'title' => /*t(*/'Medjimurje county'/*)*/,
      'tag' => '0'
    ),
    'HR-21' => array(
      'country' => 'HR',
      'title' => /*t(*/'Zagreb (city)'/*)*/,
      'tag' => '0'
    ),
    'HT-AR' => array('country' => 'HT', 'title' => /*t(*/'Artibonite'/*)*/, 'tag' => '0'),
    'HT-CE' => array('country' => 'HT', 'title' => /*t(*/'Centre'/*)*/, 'tag' => '0'),
    'HT-GA' => array('country' => 'HT', 'title' => /*t(*/"Grand'Anse"/*)*/, 'tag' => '0'),
    'HT-ND' => array('country' => 'HT', 'title' => /*t(*/'Nord'/*)*/, 'tag' => '0'),
    'HT-NE' => array('country' => 'HT', 'title' => /*t(*/'Nord-Est'/*)*/, 'tag' => '0'),
    'HT-NO' => array('country' => 'HT', 'title' => /*t(*/'Nord-Ouest'/*)*/, 'tag' => '0'),
    'HT-OU' => array('country' => 'HT', 'title' => /*t(*/'Ouest'/*)*/, 'tag' => '0'),
    'HT-SD' => array('country' => 'HT', 'title' => /*t(*/'Sud'/*)*/, 'tag' => '0'),
    'HT-SE' => array('country' => 'HT', 'title' => /*t(*/'Sud-Est'/*)*/, 'tag' => '0'),
    'HU-BA' => array(
      'country' => 'HU',
      'title' => /*t(*/'Borsod-Abauj-Zemplen'/*)*/,
      'tag' => '0'
    ),
    'HU-BC' => array('country' => 'HU', 'title' => /*t(*/'Bekescsaba'/*)*/, 'tag' => '0'),
    'HU-BK' => array('country' => 'HU', 'title' => /*t(*/'Bacs-Kiskun'/*)*/, 'tag' => '0'),
    'HU-BR' => array('country' => 'HU', 'title' => /*t(*/'Baranya'/*)*/, 'tag' => '0'),
    'HU-BS' => array('country' => 'HU', 'title' => /*t(*/'Bekes'/*)*/, 'tag' => '0'),
    'HU-BU' => array('country' => 'HU', 'title' => /*t(*/'Budapest'/*)*/, 'tag' => '0'),
    'HU-CG' => array('country' => 'HU', 'title' => /*t(*/'Csongrad'/*)*/, 'tag' => '0'),
    'HU-DB' => array('country' => 'HU', 'title' => /*t(*/'Debrecen'/*)*/, 'tag' => '0'),
    'HU-DJ' => array('country' => 'HU', 'title' => /*t(*/'Dunaujvaros'/*)*/, 'tag' => '0'),
    'HU-EG' => array('country' => 'HU', 'title' => /*t(*/'Eger'/*)*/, 'tag' => '0'),
    'HU-FJ' => array('country' => 'HU', 'title' => /*t(*/'Fejer'/*)*/, 'tag' => '0'),
    'HU-GM' => array(
      'country' => 'HU',
      'title' => /*t(*/'Gyor-Moson-Sopron'/*)*/,
      'tag' => '0'
    ),
    'HU-GY' => array('country' => 'HU', 'title' => /*t(*/'Gyor'/*)*/, 'tag' => '0'),
    'HU-HB' => array('country' => 'HU', 'title' => /*t(*/'Hajdu-Bihar'/*)*/, 'tag' => '0'),
    'HU-HM' => array(
      'country' => 'HU',
      'title' => /*t(*/'Hodmezovasarhely'/*)*/,
      'tag' => '0'
    ),
    'HU-HV' => array('country' => 'HU', 'title' => /*t(*/'Heves'/*)*/, 'tag' => '0'),
    'HU-JN' => array(
      'country' => 'HU',
      'title' => /*t(*/'Jasz-Nagykun-Szolnok'/*)*/,
      'tag' => '0'
    ),
    'HU-KC' => array('country' => 'HU', 'title' => /*t(*/'Kecskemet'/*)*/, 'tag' => '0'),
    'HU-KE' => array(
      'country' => 'HU',
      'title' => /*t(*/'Komarom-Esztergom'/*)*/,
      'tag' => '0'
    ),
    'HU-KP' => array('country' => 'HU', 'title' => /*t(*/'Kaposvar'/*)*/, 'tag' => '0'),
    'HU-MK' => array('country' => 'HU', 'title' => /*t(*/'Miskolc'/*)*/, 'tag' => '0'),
    'HU-NG' => array('country' => 'HU', 'title' => /*t(*/'Nograd'/*)*/, 'tag' => '0'),
    'HU-NK' => array('country' => 'HU', 'title' => /*t(*/'Nagykanizsa'/*)*/, 'tag' => '0'),
    'HU-NY' => array('country' => 'HU', 'title' => /*t(*/'Nyiregyhaza'/*)*/, 'tag' => '0'),
    'HU-PC' => array('country' => 'HU', 'title' => /*t(*/'Pecs'/*)*/, 'tag' => '0'),
    'HU-PE' => array('country' => 'HU', 'title' => /*t(*/'Pest'/*)*/, 'tag' => '0'),
    'HU-SB' => array('country' => 'HU', 'title' => /*t(*/'Szombathely'/*)*/, 'tag' => '0'),
    'HU-SG' => array('country' => 'HU', 'title' => /*t(*/'Szeged'/*)*/, 'tag' => '0'),
    'HU-SK' => array(
      'country' => 'HU',
      'title' => /*t(*/'Szekesfehervar'/*)*/,
      'tag' => '0'
    ),
    'HU-SL' => array('country' => 'HU', 'title' => /*t(*/'Szolnok'/*)*/, 'tag' => '0'),
    'HU-SM' => array('country' => 'HU', 'title' => /*t(*/'Somogy'/*)*/, 'tag' => '0'),
    'HU-SO' => array('country' => 'HU', 'title' => /*t(*/'Sopron'/*)*/, 'tag' => '0'),
    'HU-SS' => array(
      'country' => 'HU',
      'title' => /*t(*/'Szabolcs-Szatmar-Bereg'/*)*/,
      'tag' => '0'
    ),
    'HU-TB' => array('country' => 'HU', 'title' => /*t(*/'Tatabanya'/*)*/, 'tag' => '0'),
    'HU-TO' => array('country' => 'HU', 'title' => /*t(*/'Tolna'/*)*/, 'tag' => '0'),
    'HU-VA' => array('country' => 'HU', 'title' => /*t(*/'Vas'/*)*/, 'tag' => '0'),
    'HU-VZ' => array('country' => 'HU', 'title' => /*t(*/'Veszprem'/*)*/, 'tag' => '0'),
    'HU-ZA' => array('country' => 'HU', 'title' => /*t(*/'Zala'/*)*/, 'tag' => '0'),
    'HU-ZG' => array(
      'country' => 'HU',
      'title' => /*t(*/'Zalaegerszeg'/*)*/,
      'tag' => '0'
    ),
    'ID-AC' => array('country' => 'ID', 'title' => /*t(*/'Aceh'/*)*/, 'tag' => '0'),
    'ID-BA' => array('country' => 'ID', 'title' => /*t(*/'Bali'/*)*/, 'tag' => '0'),
    'ID-BB' => array(
      'country' => 'ID',
      'title' => /*t(*/'Bangka-Belitung'/*)*/,
      'tag' => '0'
    ),
    'ID-BE' => array('country' => 'ID', 'title' => /*t(*/'Bengkulu'/*)*/, 'tag' => '0'),
    'ID-BT' => array('country' => 'ID', 'title' => /*t(*/'Banten'/*)*/, 'tag' => '0'),
    'ID-GO' => array('country' => 'ID', 'title' => /*t(*/'Gorontalo'/*)*/, 'tag' => '0'),
    'ID-IJ' => array('country' => 'ID', 'title' => /*t(*/'Papua'/*)*/, 'tag' => '0'),
    'ID-JA' => array('country' => 'ID', 'title' => /*t(*/'Jambi'/*)*/, 'tag' => '0'),
    'ID-JI' => array('country' => 'ID', 'title' => /*t(*/'Jawa Timur'/*)*/, 'tag' => '0'),
    'ID-JK' => array(
      'country' => 'ID',
      'title' => /*t(*/'Jakarta Raya'/*)*/,
      'tag' => '0'
    ),
    'ID-JR' => array('country' => 'ID', 'title' => /*t(*/'Jawa Barat'/*)*/, 'tag' => '0'),
    'ID-JT' => array('country' => 'ID', 'title' => /*t(*/'Jawa Tengah'/*)*/, 'tag' => '0'),
    'ID-KB' => array(
      'country' => 'ID',
      'title' => /*t(*/'Kalimantan Barat'/*)*/,
      'tag' => '0'
    ),
    'ID-KI' => array(
      'country' => 'ID',
      'title' => /*t(*/'Kalimantan Timur'/*)*/,
      'tag' => '0'
    ),
    'ID-KS' => array(
      'country' => 'ID',
      'title' => /*t(*/'Kalimantan Selatan'/*)*/,
      'tag' => '0'
    ),
    'ID-KT' => array(
      'country' => 'ID',
      'title' => /*t(*/'Kalimantan Tengah'/*)*/,
      'tag' => '0'
    ),
    'ID-LA' => array('country' => 'ID', 'title' => /*t(*/'Lampung'/*)*/, 'tag' => '0'),
    'ID-MA' => array('country' => 'ID', 'title' => /*t(*/'Maluku'/*)*/, 'tag' => '0'),
    'ID-MU' => array(
      'country' => 'ID',
      'title' => /*t(*/'Maluku Utara'/*)*/,
      'tag' => '0'
    ),
    'ID-NB' => array(
      'country' => 'ID',
      'title' => /*t(*/'Nusa Tenggara Barat'/*)*/,
      'tag' => '0'
    ),
    'ID-NT' => array(
      'country' => 'ID',
      'title' => /*t(*/'Nusa Tenggara Timur'/*)*/,
      'tag' => '0'
    ),
    'ID-RI' => array('country' => 'ID', 'title' => /*t(*/'Riau'/*)*/, 'tag' => '0'),
    'ID-SB' => array(
      'country' => 'ID',
      'title' => /*t(*/'Sumatera Barat'/*)*/,
      'tag' => '0'
    ),
    'ID-SG' => array(
      'country' => 'ID',
      'title' => /*t(*/'Sulawesi Tenggara'/*)*/,
      'tag' => '0'
    ),
    'ID-SL' => array(
      'country' => 'ID',
      'title' => /*t(*/'Sumatera Selatan'/*)*/,
      'tag' => '0'
    ),
    'ID-SN' => array(
      'country' => 'ID',
      'title' => /*t(*/'Sulawesi Selatan'/*)*/,
      'tag' => '0'
    ),
    'ID-ST' => array(
      'country' => 'ID',
      'title' => /*t(*/'Sulawesi Tengah'/*)*/,
      'tag' => '0'
    ),
    'ID-SU' => array(
      'country' => 'ID',
      'title' => /*t(*/'Sumatera Utara'/*)*/,
      'tag' => '0'
    ),
    'ID-SW' => array(
      'country' => 'ID',
      'title' => /*t(*/'Sulawesi Utara'/*)*/,
      'tag' => '0'
    ),
    'ID-YO' => array('country' => 'ID', 'title' => /*t(*/'Yogyakarta'/*)*/, 'tag' => '0'),
    'IE-CK' => array('country' => 'IE', 'title' => /*t(*/'Cork'/*)*/, 'tag' => '0'),
    'IE-CL' => array('country' => 'IE', 'title' => /*t(*/'Clare'/*)*/, 'tag' => '0'),
    'IE-CV' => array('country' => 'IE', 'title' => /*t(*/'Cavan'/*)*/, 'tag' => '0'),
    'IE-CW' => array('country' => 'IE', 'title' => /*t(*/'Carlow'/*)*/, 'tag' => '0'),
    'IE-DB' => array('country' => 'IE', 'title' => /*t(*/'Dublin'/*)*/, 'tag' => '0'),
    'IE-DG' => array('country' => 'IE', 'title' => /*t(*/'Donegal'/*)*/, 'tag' => '0'),
    'IE-GW' => array('country' => 'IE', 'title' => /*t(*/'Galway'/*)*/, 'tag' => '0'),
    'IE-KD' => array('country' => 'IE', 'title' => /*t(*/'Kildare'/*)*/, 'tag' => '0'),
    'IE-KK' => array('country' => 'IE', 'title' => /*t(*/'Kilkenny'/*)*/, 'tag' => '0'),
    'IE-KR' => array('country' => 'IE', 'title' => /*t(*/'Kerry'/*)*/, 'tag' => '0'),
    'IE-LA' => array('country' => 'IE', 'title' => /*t(*/'Laois'/*)*/, 'tag' => '0'),
    'IE-LF' => array('country' => 'IE', 'title' => /*t(*/'Longford'/*)*/, 'tag' => '0'),
    'IE-LM' => array('country' => 'IE', 'title' => /*t(*/'Limerick'/*)*/, 'tag' => '0'),
    'IE-LR' => array('country' => 'IE', 'title' => /*t(*/'Leitrim'/*)*/, 'tag' => '0'),
    'IE-LT' => array('country' => 'IE', 'title' => /*t(*/'Louth'/*)*/, 'tag' => '0'),
    'IE-MG' => array('country' => 'IE', 'title' => /*t(*/'Monaghan'/*)*/, 'tag' => '0'),
    'IE-MT' => array('country' => 'IE', 'title' => /*t(*/'Meath'/*)*/, 'tag' => '0'),
    'IE-MY' => array('country' => 'IE', 'title' => /*t(*/'Mayo'/*)*/, 'tag' => '0'),
    'IE-OF' => array('country' => 'IE', 'title' => /*t(*/'Offaly'/*)*/, 'tag' => '0'),
    'IE-RC' => array('country' => 'IE', 'title' => /*t(*/'Roscommon'/*)*/, 'tag' => '0'),
    'IE-SL' => array('country' => 'IE', 'title' => /*t(*/'Sligo'/*)*/, 'tag' => '0'),
    'IE-TP' => array('country' => 'IE', 'title' => /*t(*/'Tipperary'/*)*/, 'tag' => '0'),
    'IE-WF' => array('country' => 'IE', 'title' => /*t(*/'Waterford'/*)*/, 'tag' => '0'),
    'IE-WK' => array('country' => 'IE', 'title' => /*t(*/'Wicklow'/*)*/, 'tag' => '0'),
    'IE-WM' => array('country' => 'IE', 'title' => /*t(*/'Westmeath'/*)*/, 'tag' => '0'),
    'IE-WX' => array('country' => 'IE', 'title' => /*t(*/'Wexford'/*)*/, 'tag' => '0'),
    'IL-C' => array('country' => 'IL', 'title' => /*t(*/'Central'/*)*/, 'tag' => '0'),
    'IL-H' => array('country' => 'IL', 'title' => /*t(*/'Haifa'/*)*/, 'tag' => '0'),
    'IL-J' => array('country' => 'IL', 'title' => /*t(*/'Jerusalem'/*)*/, 'tag' => '0'),
    'IL-N' => array('country' => 'IL', 'title' => /*t(*/'Northern'/*)*/, 'tag' => '0'),
    'IL-S' => array('country' => 'IL', 'title' => /*t(*/'Southern'/*)*/, 'tag' => '0'),
    'IL-T' => array('country' => 'IL', 'title' => /*t(*/'Tel Aviv'/*)*/, 'tag' => '0'),
    'IN-AN' => array(
      'country' => 'IN',
      'title' => /*t(*/'Andaman and Nicobar Islands'/*)*/,
      'tag' => '0'
    ),
    'IN-AP' => array(
      'country' => 'IN',
      'title' => /*t(*/'Andhra Pradesh'/*)*/,
      'tag' => '0'
    ),
    'IN-AR' => array(
      'country' => 'IN',
      'title' => /*t(*/'Arunachal Pradesh'/*)*/,
      'tag' => '0'
    ),
    'IN-AS' => array('country' => 'IN', 'title' => /*t(*/'Assam'/*)*/, 'tag' => '0'),
    'IN-BR' => array('country' => 'IN', 'title' => /*t(*/'Bihar'/*)*/, 'tag' => '0'),
    'IN-CH' => array('country' => 'IN', 'title' => /*t(*/'Chandigarh'/*)*/, 'tag' => '0'),
    'IN-CT' => array(
      'country' => 'IN',
      'title' => /*t(*/'Chhattisgarh'/*)*/,
      'tag' => '0'
    ),
    'IN-DD' => array(
      'country' => 'IN',
      'title' => /*t(*/'Daman and Diu'/*)*/,
      'tag' => '0'
    ),
    'IN-DL' => array('country' => 'IN', 'title' => /*t(*/'Delhi'/*)*/, 'tag' => '0'),
    'IN-DN' => array(
      'country' => 'IN',
      'title' => /*t(*/'Dadra and Nagar Haveli'/*)*/,
      'tag' => '0'
    ),
    'IN-GA' => array('country' => 'IN', 'title' => /*t(*/'Goa'/*)*/, 'tag' => '0'),
    'IN-GJ' => array('country' => 'IN', 'title' => /*t(*/'Gujarat'/*)*/, 'tag' => '0'),
    'IN-HP' => array(
      'country' => 'IN',
      'title' => /*t(*/'Himachal Pradesh'/*)*/,
      'tag' => '0'
    ),
    'IN-HR' => array('country' => 'IN', 'title' => /*t(*/'Haryana'/*)*/, 'tag' => '0'),
    'IN-JH' => array('country' => 'IN', 'title' => /*t(*/'Jharkhand'/*)*/, 'tag' => '0'),
    'IN-JK' => array(
      'country' => 'IN',
      'title' => /*t(*/'Jammu and Kashmir'/*)*/,
      'tag' => '0'
    ),
    'IN-KA' => array('country' => 'IN', 'title' => /*t(*/'Karnataka'/*)*/, 'tag' => '0'),
    'IN-KL' => array('country' => 'IN', 'title' => /*t(*/'Kerala'/*)*/, 'tag' => '0'),
    'IN-LD' => array('country' => 'IN', 'title' => /*t(*/'Lakshadweep'/*)*/, 'tag' => '0'),
    'IN-ML' => array('country' => 'IN', 'title' => /*t(*/'Meghalaya'/*)*/, 'tag' => '0'),
    'IN-MM' => array('country' => 'IN', 'title' => /*t(*/'Maharashtra'/*)*/, 'tag' => '0'),
    'IN-MN' => array('country' => 'IN', 'title' => /*t(*/'Manipur'/*)*/, 'tag' => '0'),
    'IN-MP' => array(
      'country' => 'IN',
      'title' => /*t(*/'Madhya Pradesh'/*)*/,
      'tag' => '0'
    ),
    'IN-MZ' => array('country' => 'IN', 'title' => /*t(*/'Mizoram'/*)*/, 'tag' => '0'),
    'IN-NL' => array('country' => 'IN', 'title' => /*t(*/'Nagaland'/*)*/, 'tag' => '0'),
    'IN-OR' => array('country' => 'IN', 'title' => /*t(*/'Orissa'/*)*/, 'tag' => '0'),
    'IN-PB' => array('country' => 'IN', 'title' => /*t(*/'Punjab'/*)*/, 'tag' => '0'),
    'IN-PY' => array('country' => 'IN', 'title' => /*t(*/'Pondicherry'/*)*/, 'tag' => '0'),
    'IN-RJ' => array('country' => 'IN', 'title' => /*t(*/'Rajasthan'/*)*/, 'tag' => '0'),
    'IN-SK' => array('country' => 'IN', 'title' => /*t(*/'Sikkim'/*)*/, 'tag' => '0'),
    'IN-TN' => array('country' => 'IN', 'title' => /*t(*/'Tamil Nadu'/*)*/, 'tag' => '0'),
    'IN-TR' => array('country' => 'IN', 'title' => /*t(*/'Tripura'/*)*/, 'tag' => '0'),
    'IN-UL' => array('country' => 'IN', 'title' => /*t(*/'Uttaranchal'/*)*/, 'tag' => '0'),
    'IN-UP' => array(
      'country' => 'IN',
      'title' => /*t(*/'Uttar Pradesh'/*)*/,
      'tag' => '0'
    ),
    'IN-WB' => array('country' => 'IN', 'title' => /*t(*/'West Bengal'/*)*/, 'tag' => '0'),
    'IO-DG' => array(
      'country' => 'IO',
      'title' => /*t(*/'Diego Garcia'/*)*/,
      'tag' => '0'
    ),
    'IO-DI' => array(
      'country' => 'IO',
      'title' => /*t(*/'Danger Island'/*)*/,
      'tag' => '0'
    ),
    'IO-EA' => array(
      'country' => 'IO',
      'title' => /*t(*/'Eagle Islands'/*)*/,
      'tag' => '0'
    ),
    'IO-EG' => array(
      'country' => 'IO',
      'title' => /*t(*/'Egmont Islands'/*)*/,
      'tag' => '0'
    ),
    'IO-NI' => array(
      'country' => 'IO',
      'title' => /*t(*/'Nelsons Island'/*)*/,
      'tag' => '0'
    ),
    'IO-PB' => array(
      'country' => 'IO',
      'title' => /*t(*/'Peros Banhos'/*)*/,
      'tag' => '0'
    ),
    'IO-SI' => array(
      'country' => 'IO',
      'title' => /*t(*/'Salomon Islands'/*)*/,
      'tag' => '0'
    ),
    'IO-TB' => array(
      'country' => 'IO',
      'title' => /*t(*/'Three Brothers'/*)*/,
      'tag' => '0'
    ),
    'IQ-AB' => array('country' => 'IQ', 'title' => /*t(*/'Al Anbar'/*)*/, 'tag' => '0'),
    'IQ-AL' => array('country' => 'IQ', 'title' => /*t(*/'Arbil'/*)*/, 'tag' => '0'),
    'IQ-BA' => array('country' => 'IQ', 'title' => /*t(*/'Al Basrah'/*)*/, 'tag' => '0'),
    'IQ-BB' => array('country' => 'IQ', 'title' => /*t(*/'Babil'/*)*/, 'tag' => '0'),
    'IQ-BD' => array('country' => 'IQ', 'title' => /*t(*/'Baghdad'/*)*/, 'tag' => '0'),
    'IQ-DH' => array('country' => 'IQ', 'title' => /*t(*/'Dahuk'/*)*/, 'tag' => '0'),
    'IQ-DQ' => array('country' => 'IQ', 'title' => /*t(*/'Dhi Qar'/*)*/, 'tag' => '0'),
    'IQ-DY' => array('country' => 'IQ', 'title' => /*t(*/'Diyala'/*)*/, 'tag' => '0'),
    'IQ-KB' => array('country' => 'IQ', 'title' => /*t(*/'Al Karbala'/*)*/, 'tag' => '0'),
    'IQ-MU' => array('country' => 'IQ', 'title' => /*t(*/'Al Muthanna'/*)*/, 'tag' => '0'),
    'IQ-MY' => array('country' => 'IQ', 'title' => /*t(*/'Maysan'/*)*/, 'tag' => '0'),
    'IQ-NJ' => array('country' => 'IQ', 'title' => /*t(*/'An Najaf'/*)*/, 'tag' => '0'),
    'IQ-NN' => array('country' => 'IQ', 'title' => /*t(*/'Ninawa'/*)*/, 'tag' => '0'),
    'IQ-QA' => array('country' => 'IQ', 'title' => /*t(*/'Al Qadisyah'/*)*/, 'tag' => '0'),
    'IQ-SD' => array(
      'country' => 'IQ',
      'title' => /*t(*/'Salah ad Din'/*)*/,
      'tag' => '0'
    ),
    'IQ-SL' => array(
      'country' => 'IQ',
      'title' => /*t(*/'As Sulaymaniyah'/*)*/,
      'tag' => '0'
    ),
    'IQ-TM' => array('country' => 'IQ', 'title' => /*t(*/"At Ta'mim"/*)*/, 'tag' => '0'),
    'IQ-WS' => array('country' => 'IQ', 'title' => /*t(*/'Wasit'/*)*/, 'tag' => '0'),
    'IR-ARD' => array('country' => 'IR', 'title' => /*t(*/'Ardabil'/*)*/, 'tag' => '0'),
    'IR-BSH' => array('country' => 'IR', 'title' => /*t(*/'Bushehr'/*)*/, 'tag' => '0'),
    'IR-CMB' => array(
      'country' => 'IR',
      'title' => /*t(*/'Chahar Mahaal and Bakhtiari'/*)*/,
      'tag' => '0'
    ),
    'IR-EAZ' => array(
      'country' => 'IR',
      'title' => /*t(*/'East Azarbaijan'/*)*/,
      'tag' => '0'
    ),
    'IR-EFH' => array('country' => 'IR', 'title' => /*t(*/'Esfahan'/*)*/, 'tag' => '0'),
    'IR-FAR' => array('country' => 'IR', 'title' => /*t(*/'Fars'/*)*/, 'tag' => '0'),
    'IR-GIL' => array('country' => 'IR', 'title' => /*t(*/'Gilan'/*)*/, 'tag' => '0'),
    'IR-GLS' => array('country' => 'IR', 'title' => /*t(*/'Golestan'/*)*/, 'tag' => '0'),
    'IR-HMD' => array('country' => 'IR', 'title' => /*t(*/'Hamadan'/*)*/, 'tag' => '0'),
    'IR-HRM' => array('country' => 'IR', 'title' => /*t(*/'Hormozgan'/*)*/, 'tag' => '0'),
    'IR-ILM' => array('country' => 'IR', 'title' => /*t(*/'Ilam'/*)*/, 'tag' => '0'),
    'IR-KBA' => array(
      'country' => 'IR',
      'title' => /*t(*/'Kohkiluyeh and Buyer Ahmad'/*)*/,
      'tag' => '0'
    ),
    'IR-KRB' => array('country' => 'IR', 'title' => /*t(*/'Kerman'/*)*/, 'tag' => '0'),
    'IR-KRD' => array('country' => 'IR', 'title' => /*t(*/'Kurdistan'/*)*/, 'tag' => '0'),
    'IR-KRM' => array('country' => 'IR', 'title' => /*t(*/'Kermanshah'/*)*/, 'tag' => '0'),
    'IR-KZT' => array('country' => 'IR', 'title' => /*t(*/'Khuzestan'/*)*/, 'tag' => '0'),
    'IR-LRS' => array('country' => 'IR', 'title' => /*t(*/'Lorestan'/*)*/, 'tag' => '0'),
    'IR-MKZ' => array('country' => 'IR', 'title' => /*t(*/'Markazi'/*)*/, 'tag' => '0'),
    'IR-MZD' => array('country' => 'IR', 'title' => /*t(*/'Mazandaran'/*)*/, 'tag' => '0'),
    'IR-NKH' => array(
      'country' => 'IR',
      'title' => /*t(*/'North Khorasan'/*)*/,
      'tag' => '0'
    ),
    'IR-QAZ' => array('country' => 'IR', 'title' => /*t(*/'Qazvin'/*)*/, 'tag' => '0'),
    'IR-QOM' => array('country' => 'IR', 'title' => /*t(*/'Qom'/*)*/, 'tag' => '0'),
    'IR-RKH' => array(
      'country' => 'IR',
      'title' => /*t(*/'Razavi Khorasan'/*)*/,
      'tag' => '0'
    ),
    'IR-SBL' => array(
      'country' => 'IR',
      'title' => /*t(*/'Sistan and Baluchistan'/*)*/,
      'tag' => '0'
    ),
    'IR-SKH' => array(
      'country' => 'IR',
      'title' => /*t(*/'South Khorasan'/*)*/,
      'tag' => '0'
    ),
    'IR-SMN' => array('country' => 'IR', 'title' => /*t(*/'Semnan'/*)*/, 'tag' => '0'),
    'IR-TEH' => array('country' => 'IR', 'title' => /*t(*/'Tehran'/*)*/, 'tag' => '0'),
    'IR-WEZ' => array(
      'country' => 'IR',
      'title' => /*t(*/'West Azarbaijan'/*)*/,
      'tag' => '0'
    ),
    'IR-YZD' => array('country' => 'IR', 'title' => /*t(*/'Yazd'/*)*/, 'tag' => '0'),
    'IR-ZAN' => array('country' => 'IR', 'title' => /*t(*/'Zanjan'/*)*/, 'tag' => '0'),
    'IS-AL' => array('country' => 'IS', 'title' => /*t(*/'Austurland'/*)*/, 'tag' => '0'),
    'IS-HF' => array(
      'country' => 'IS',
      'title' => /*t(*/'Hofuoborgarsvaeoi'/*)*/,
      'tag' => '0'
    ),
    'IS-NE' => array(
      'country' => 'IS',
      'title' => /*t(*/'Norourland eystra'/*)*/,
      'tag' => '0'
    ),
    'IS-NV' => array(
      'country' => 'IS',
      'title' => /*t(*/'Norourland vestra'/*)*/,
      'tag' => '0'
    ),
    'IS-SL' => array('country' => 'IS', 'title' => /*t(*/'Suourland'/*)*/, 'tag' => '0'),
    'IS-SN' => array('country' => 'IS', 'title' => /*t(*/'Suournes'/*)*/, 'tag' => '0'),
    'IS-VF' => array('country' => 'IS', 'title' => /*t(*/'Vestfiroir'/*)*/, 'tag' => '0'),
    'IS-VL' => array('country' => 'IS', 'title' => /*t(*/'Vesturland'/*)*/, 'tag' => '0'),
    'IT-AG' => array('country' => 'IT', 'title' => /*t(*/'Agrigento'/*)*/, 'tag' => '0'),
    'IT-AL' => array('country' => 'IT', 'title' => /*t(*/'Alessandria'/*)*/, 'tag' => '0'),
    'IT-AN' => array('country' => 'IT', 'title' => /*t(*/'Ancona'/*)*/, 'tag' => '0'),
    'IT-AO' => array('country' => 'IT', 'title' => /*t(*/'Aosta'/*)*/, 'tag' => '0'),
    'IT-AP' => array(
      'country' => 'IT',
      'title' => /*t(*/'Ascoli Piceno'/*)*/,
      'tag' => '0'
    ),
    'IT-AQ' => array('country' => 'IT', 'title' => /*t(*/"L'Aquila"/*)*/, 'tag' => '0'),
    'IT-AR' => array('country' => 'IT', 'title' => /*t(*/'Arezzo'/*)*/, 'tag' => '0'),
    'IT-AT' => array('country' => 'IT', 'title' => /*t(*/'Asti'/*)*/, 'tag' => '0'),
    'IT-AV' => array('country' => 'IT', 'title' => /*t(*/'Avellino'/*)*/, 'tag' => '0'),
    'IT-BA' => array('country' => 'IT', 'title' => /*t(*/'Bari'/*)*/, 'tag' => '0'),
    'IT-BG' => array('country' => 'IT', 'title' => /*t(*/'Bergamo'/*)*/, 'tag' => '0'),
    'IT-BI' => array('country' => 'IT', 'title' => /*t(*/'Biella'/*)*/, 'tag' => '0'),
    'IT-BL' => array('country' => 'IT', 'title' => /*t(*/'Belluno'/*)*/, 'tag' => '0'),
    'IT-BN' => array('country' => 'IT', 'title' => /*t(*/'Benevento'/*)*/, 'tag' => '0'),
    'IT-BO' => array('country' => 'IT', 'title' => /*t(*/'Bologna'/*)*/, 'tag' => '0'),
    'IT-BR' => array('country' => 'IT', 'title' => /*t(*/'Brindisi'/*)*/, 'tag' => '0'),
    'IT-BS' => array('country' => 'IT', 'title' => /*t(*/'Brescia'/*)*/, 'tag' => '0'),
    'IT-BZ' => array('country' => 'IT', 'title' => /*t(*/'Bolzano'/*)*/, 'tag' => '0'),
    'IT-CA' => array('country' => 'IT', 'title' => /*t(*/'Cagliari'/*)*/, 'tag' => '0'),
    'IT-CB' => array('country' => 'IT', 'title' => /*t(*/'Campobasso'/*)*/, 'tag' => '0'),
    'IT-CE' => array('country' => 'IT', 'title' => /*t(*/'Caserta'/*)*/, 'tag' => '0'),
    'IT-CH' => array('country' => 'IT', 'title' => /*t(*/'Chieti'/*)*/, 'tag' => '0'),
    'IT-CL' => array(
      'country' => 'IT',
      'title' => /*t(*/'Caltanissetta'/*)*/,
      'tag' => '0'
    ),
    'IT-CN' => array('country' => 'IT', 'title' => /*t(*/'Cuneo'/*)*/, 'tag' => '0'),
    'IT-CO' => array('country' => 'IT', 'title' => /*t(*/'Como'/*)*/, 'tag' => '0'),
    'IT-CR' => array('country' => 'IT', 'title' => /*t(*/'Cremona'/*)*/, 'tag' => '0'),
    'IT-CS' => array('country' => 'IT', 'title' => /*t(*/'Cosenza'/*)*/, 'tag' => '0'),
    'IT-CT' => array('country' => 'IT', 'title' => /*t(*/'Catania'/*)*/, 'tag' => '0'),
    'IT-CZ' => array('country' => 'IT', 'title' => /*t(*/'Catanzaro'/*)*/, 'tag' => '0'),
    'IT-EN' => array('country' => 'IT', 'title' => /*t(*/'Enna'/*)*/, 'tag' => '0'),
    'IT-FE' => array('country' => 'IT', 'title' => /*t(*/'Ferrara'/*)*/, 'tag' => '0'),
    'IT-FG' => array('country' => 'IT', 'title' => /*t(*/'Foggia'/*)*/, 'tag' => '0'),
    'IT-FI' => array('country' => 'IT', 'title' => /*t(*/'Firenze'/*)*/, 'tag' => '0'),
    'IT-FO' => array('country' => 'IT', 'title' => /*t(*/'Forli-cesena'/*)*/, 'tag' => '0'),
    'IT-FR' => array('country' => 'IT', 'title' => /*t(*/'Frosinone'/*)*/, 'tag' => '0'),
    'IT-GE' => array('country' => 'IT', 'title' => /*t(*/'Genova'/*)*/, 'tag' => '0'),
    'IT-GO' => array('country' => 'IT', 'title' => /*t(*/'Gorizia'/*)*/, 'tag' => '0'),
    'IT-GR' => array('country' => 'IT', 'title' => /*t(*/'Grosseto'/*)*/, 'tag' => '0'),
    'IT-IM' => array('country' => 'IT', 'title' => /*t(*/'Imperia'/*)*/, 'tag' => '0'),
    'IT-IS' => array('country' => 'IT', 'title' => /*t(*/'Isernia'/*)*/, 'tag' => '0'),
    'IT-KR' => array('country' => 'IT', 'title' => /*t(*/'Crotone'/*)*/, 'tag' => '0'),
    'IT-LC' => array('country' => 'IT', 'title' => /*t(*/'Lecco'/*)*/, 'tag' => '0'),
    'IT-LE' => array('country' => 'IT', 'title' => /*t(*/'Lecce'/*)*/, 'tag' => '0'),
    'IT-LI' => array('country' => 'IT', 'title' => /*t(*/'Livorno'/*)*/, 'tag' => '0'),
    'IT-LO' => array('country' => 'IT', 'title' => /*t(*/'Lodi'/*)*/, 'tag' => '0'),
    'IT-LT' => array('country' => 'IT', 'title' => /*t(*/'Latina'/*)*/, 'tag' => '0'),
    'IT-LU' => array('country' => 'IT', 'title' => /*t(*/'Lucca'/*)*/, 'tag' => '0'),
    'IT-MC' => array('country' => 'IT', 'title' => /*t(*/'Macerata'/*)*/, 'tag' => '0'),
    'IT-ME' => array('country' => 'IT', 'title' => /*t(*/'Messina'/*)*/, 'tag' => '0'),
    'IT-MI' => array('country' => 'IT', 'title' => /*t(*/'Milano'/*)*/, 'tag' => '0'),
    'IT-MN' => array('country' => 'IT', 'title' => /*t(*/'Mantova'/*)*/, 'tag' => '0'),
    'IT-MO' => array('country' => 'IT', 'title' => /*t(*/'Modena'/*)*/, 'tag' => '0'),
    'IT-MS' => array(
      'country' => 'IT',
      'title' => /*t(*/'Massa-Carrara'/*)*/,
      'tag' => '0'
    ),
    'IT-MT' => array('country' => 'IT', 'title' => /*t(*/'Matera'/*)*/, 'tag' => '0'),
    'IT-NA' => array('country' => 'IT', 'title' => /*t(*/'Naploli'/*)*/, 'tag' => '0'),
    'IT-NO' => array('country' => 'IT', 'title' => /*t(*/'Novara'/*)*/, 'tag' => '0'),
    'IT-NU' => array('country' => 'IT', 'title' => /*t(*/'Nuoro'/*)*/, 'tag' => '0'),
    'IT-OR' => array('country' => 'IT', 'title' => /*t(*/'Oristano'/*)*/, 'tag' => '0'),
    'IT-PA' => array('country' => 'IT', 'title' => /*t(*/'Palermo'/*)*/, 'tag' => '0'),
    'IT-PC' => array('country' => 'IT', 'title' => /*t(*/'Piacenza'/*)*/, 'tag' => '0'),
    'IT-PD' => array('country' => 'IT', 'title' => /*t(*/'Padova'/*)*/, 'tag' => '0'),
    'IT-PE' => array('country' => 'IT', 'title' => /*t(*/'Pescara'/*)*/, 'tag' => '0'),
    'IT-PG' => array('country' => 'IT', 'title' => /*t(*/'Perugia'/*)*/, 'tag' => '0'),
    'IT-PI' => array('country' => 'IT', 'title' => /*t(*/'Pisa'/*)*/, 'tag' => '0'),
    'IT-PN' => array('country' => 'IT', 'title' => /*t(*/'Pordenone'/*)*/, 'tag' => '0'),
    'IT-PO' => array('country' => 'IT', 'title' => /*t(*/'Prato'/*)*/, 'tag' => '0'),
    'IT-PR' => array('country' => 'IT', 'title' => /*t(*/'Parma'/*)*/, 'tag' => '0'),
    'IT-PS' => array(
      'country' => 'IT',
      'title' => /*t(*/'Pesaro e Urbino'/*)*/,
      'tag' => '0'
    ),
    'IT-PT' => array('country' => 'IT', 'title' => /*t(*/'Pistoia'/*)*/, 'tag' => '0'),
    'IT-PV' => array('country' => 'IT', 'title' => /*t(*/'Pavia'/*)*/, 'tag' => '0'),
    'IT-PZ' => array('country' => 'IT', 'title' => /*t(*/'Potenza'/*)*/, 'tag' => '0'),
    'IT-RA' => array('country' => 'IT', 'title' => /*t(*/'Ravenna'/*)*/, 'tag' => '0'),
    'IT-RC' => array(
      'country' => 'IT',
      'title' => /*t(*/'Reggio Calabria'/*)*/,
      'tag' => '0'
    ),
    'IT-RE' => array(
      'country' => 'IT',
      'title' => /*t(*/'Reggio Emilia'/*)*/,
      'tag' => '0'
    ),
    'IT-RG' => array('country' => 'IT', 'title' => /*t(*/'Ragusa'/*)*/, 'tag' => '0'),
    'IT-RI' => array('country' => 'IT', 'title' => /*t(*/'Rieti'/*)*/, 'tag' => '0'),
    'IT-RM' => array('country' => 'IT', 'title' => /*t(*/'Roma'/*)*/, 'tag' => '0'),
    'IT-RN' => array('country' => 'IT', 'title' => /*t(*/'Rimini'/*)*/, 'tag' => '0'),
    'IT-RO' => array('country' => 'IT', 'title' => /*t(*/'Rovigo'/*)*/, 'tag' => '0'),
    'IT-SA' => array('country' => 'IT', 'title' => /*t(*/'Salerno'/*)*/, 'tag' => '0'),
    'IT-SI' => array('country' => 'IT', 'title' => /*t(*/'Siena'/*)*/, 'tag' => '0'),
    'IT-SO' => array('country' => 'IT', 'title' => /*t(*/'Sondrio'/*)*/, 'tag' => '0'),
    'IT-SP' => array('country' => 'IT', 'title' => /*t(*/'La Spezia'/*)*/, 'tag' => '0'),
    'IT-SR' => array('country' => 'IT', 'title' => /*t(*/'Siracusa'/*)*/, 'tag' => '0'),
    'IT-SS' => array('country' => 'IT', 'title' => /*t(*/'Sassari'/*)*/, 'tag' => '0'),
    'IT-SV' => array('country' => 'IT', 'title' => /*t(*/'Savona'/*)*/, 'tag' => '0'),
    'IT-TA' => array('country' => 'IT', 'title' => /*t(*/'Taranto'/*)*/, 'tag' => '0'),
    'IT-TE' => array('country' => 'IT', 'title' => /*t(*/'Teramo'/*)*/, 'tag' => '0'),
    'IT-TN' => array('country' => 'IT', 'title' => /*t(*/'Trento'/*)*/, 'tag' => '0'),
    'IT-TO' => array('country' => 'IT', 'title' => /*t(*/'Torino'/*)*/, 'tag' => '0'),
    'IT-TP' => array('country' => 'IT', 'title' => /*t(*/'Trapani'/*)*/, 'tag' => '0'),
    'IT-TR' => array('country' => 'IT', 'title' => /*t(*/'Terni'/*)*/, 'tag' => '0'),
    'IT-TS' => array('country' => 'IT', 'title' => /*t(*/'Trieste'/*)*/, 'tag' => '0'),
    'IT-TV' => array('country' => 'IT', 'title' => /*t(*/'Treviso'/*)*/, 'tag' => '0'),
    'IT-UD' => array('country' => 'IT', 'title' => /*t(*/'Udine'/*)*/, 'tag' => '0'),
    'IT-VA' => array('country' => 'IT', 'title' => /*t(*/'Varese'/*)*/, 'tag' => '0'),
    'IT-VB' => array(
      'country' => 'IT',
      'title' => /*t(*/'Verbano-Cusio-Ossola'/*)*/,
      'tag' => '0'
    ),
    'IT-VC' => array('country' => 'IT', 'title' => /*t(*/'Vercelli'/*)*/, 'tag' => '0'),
    'IT-VE' => array('country' => 'IT', 'title' => /*t(*/'Venezia'/*)*/, 'tag' => '0'),
    'IT-VI' => array('country' => 'IT', 'title' => /*t(*/'Vicenza'/*)*/, 'tag' => '0'),
    'IT-VR' => array('country' => 'IT', 'title' => /*t(*/'Verona'/*)*/, 'tag' => '0'),
    'IT-VT' => array('country' => 'IT', 'title' => /*t(*/'Viterbo'/*)*/, 'tag' => '0'),
    'IT-VV' => array(
      'country' => 'IT',
      'title' => /*t(*/'Vibo Valentia'/*)*/,
      'tag' => '0'
    ),
    'JM-AND' => array(
      'country' => 'JM',
      'title' => /*t(*/'Saint Andrew Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-ANN' => array(
      'country' => 'JM',
      'title' => /*t(*/'Saint Ann Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-CAT' => array(
      'country' => 'JM',
      'title' => /*t(*/'Saint Catherine Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-CLA' => array(
      'country' => 'JM',
      'title' => /*t(*/'Clarendon Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-ELI' => array(
      'country' => 'JM',
      'title' => /*t(*/'Saint Elizabeth Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-HAN' => array(
      'country' => 'JM',
      'title' => /*t(*/'Hanover Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-JAM' => array(
      'country' => 'JM',
      'title' => /*t(*/'Saint James Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-KIN' => array(
      'country' => 'JM',
      'title' => /*t(*/'Kingston Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-MAN' => array(
      'country' => 'JM',
      'title' => /*t(*/'Manchester Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-MAR' => array(
      'country' => 'JM',
      'title' => /*t(*/'Saint Mary Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-POR' => array(
      'country' => 'JM',
      'title' => /*t(*/'Portland Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-THO' => array(
      'country' => 'JM',
      'title' => /*t(*/'Saint Thomas Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-TRL' => array(
      'country' => 'JM',
      'title' => /*t(*/'Trelawny Parish'/*)*/,
      'tag' => '0'
    ),
    'JM-WML' => array(
      'country' => 'JM',
      'title' => /*t(*/'Westmoreland Parish'/*)*/,
      'tag' => '0'
    ),
    'JO-AJ' => array('country' => 'JO', 'title' => /*t(*/'Ajlun'/*)*/, 'tag' => '0'),
    'JO-AM' => array('country' => 'JO', 'title' => /*t(*/"'Amman"/*)*/, 'tag' => '0'),
    'JO-AQ' => array('country' => 'JO', 'title' => /*t(*/"Al 'Aqabah"/*)*/, 'tag' => '0'),
    'JO-BA' => array('country' => 'JO', 'title' => /*t(*/"Al Balqa'"/*)*/, 'tag' => '0'),
    'JO-IR' => array('country' => 'JO', 'title' => /*t(*/'Irbid'/*)*/, 'tag' => '0'),
    'JO-JA' => array('country' => 'JO', 'title' => /*t(*/'Jarash'/*)*/, 'tag' => '0'),
    'JO-KA' => array('country' => 'JO', 'title' => /*t(*/'Al Karak'/*)*/, 'tag' => '0'),
    'JO-MD' => array('country' => 'JO', 'title' => /*t(*/'Madaba'/*)*/, 'tag' => '0'),
    'JO-MF' => array('country' => 'JO', 'title' => /*t(*/'Al Mafraq'/*)*/, 'tag' => '0'),
    'JO-MN' => array('country' => 'JO', 'title' => /*t(*/"Ma'an"/*)*/, 'tag' => '0'),
    'JO-TA' => array('country' => 'JO', 'title' => /*t(*/'At Tafilah'/*)*/, 'tag' => '0'),
    'JO-ZA' => array('country' => 'JO', 'title' => /*t(*/"Az Zarqa'"/*)*/, 'tag' => '0'),
    'JP-01' => array('country' => 'JP', 'title' => /*t(*/'Hokkaido'/*)*/, 'tag' => '0'),
    'JP-02' => array('country' => 'JP', 'title' => /*t(*/'Aomori'/*)*/, 'tag' => '0'),
    'JP-03' => array('country' => 'JP', 'title' => /*t(*/'Iwate'/*)*/, 'tag' => '0'),
    'JP-04' => array('country' => 'JP', 'title' => /*t(*/'Miyagi'/*)*/, 'tag' => '0'),
    'JP-05' => array('country' => 'JP', 'title' => /*t(*/'Akita'/*)*/, 'tag' => '0'),
    'JP-06' => array('country' => 'JP', 'title' => /*t(*/'Yamagata'/*)*/, 'tag' => '0'),
    'JP-07' => array(
      'country' => 'JP',
      'title' => /*t(*/'Hukusima (Fukushima)'/*)*/,
      'tag' => '0'
    ),
    'JP-08' => array('country' => 'JP', 'title' => /*t(*/'Ibaraki'/*)*/, 'tag' => '0'),
    'JP-09' => array(
      'country' => 'JP',
      'title' => /*t(*/'Totigi (Tochigi)'/*)*/,
      'tag' => '0'
    ),
    'JP-10' => array('country' => 'JP', 'title' => /*t(*/'Gunma'/*)*/, 'tag' => '0'),
    'JP-11' => array('country' => 'JP', 'title' => /*t(*/'Saitama'/*)*/, 'tag' => '0'),
    'JP-12' => array(
      'country' => 'JP',
      'title' => /*t(*/'Tiba (Chiba)'/*)*/,
      'tag' => '0'
    ),
    'JP-13' => array('country' => 'JP', 'title' => /*t(*/'Tokyo'/*)*/, 'tag' => '0'),
    'JP-14' => array('country' => 'JP', 'title' => /*t(*/'Kanagawa'/*)*/, 'tag' => '0'),
    'JP-15' => array('country' => 'JP', 'title' => /*t(*/'Niigata'/*)*/, 'tag' => '0'),
    'JP-16' => array('country' => 'JP', 'title' => /*t(*/'Toyama'/*)*/, 'tag' => '0'),
    'JP-17' => array(
      'country' => 'JP',
      'title' => /*t(*/'Isikawa (Ishikawa)'/*)*/,
      'tag' => '0'
    ),
    'JP-18' => array(
      'country' => 'JP',
      'title' => /*t(*/'Hukui (Fukui)'/*)*/,
      'tag' => '0'
    ),
    'JP-19' => array(
      'country' => 'JP',
      'title' => /*t(*/'Yamanasi (Yamanashi)'/*)*/,
      'tag' => '0'
    ),
    'JP-20' => array('country' => 'JP', 'title' => /*t(*/'Nagano'/*)*/, 'tag' => '0'),
    'JP-21' => array(
      'country' => 'JP',
      'title' => /*t(*/'Gihu  (Gifu)'/*)*/,
      'tag' => '0'
    ),
    'JP-22' => array(
      'country' => 'JP',
      'title' => /*t(*/'Sizuoka (Shizuoka)'/*)*/,
      'tag' => '0'
    ),
    'JP-23' => array(
      'country' => 'JP',
      'title' => /*t(*/'Aiti (Aichi)'/*)*/,
      'tag' => '0'
    ),
    'JP-24' => array('country' => 'JP', 'title' => /*t(*/'Mie'/*)*/, 'tag' => '0'),
    'JP-25' => array(
      'country' => 'JP',
      'title' => /*t(*/'Siga (Shiga)'/*)*/,
      'tag' => '0'
    ),
    'JP-26' => array('country' => 'JP', 'title' => /*t(*/'Kyoto'/*)*/, 'tag' => '0'),
    'JP-27' => array('country' => 'JP', 'title' => /*t(*/'Osaka'/*)*/, 'tag' => '0'),
    'JP-28' => array('country' => 'JP', 'title' => /*t(*/'Hyogo'/*)*/, 'tag' => '0'),
    'JP-29' => array('country' => 'JP', 'title' => /*t(*/'Nara'/*)*/, 'tag' => '0'),
    'JP-30' => array('country' => 'JP', 'title' => /*t(*/'Wakayama'/*)*/, 'tag' => '0'),
    'JP-31' => array('country' => 'JP', 'title' => /*t(*/'Tottori'/*)*/, 'tag' => '0'),
    'JP-32' => array(
      'country' => 'JP',
      'title' => /*t(*/'Simane (Shimane)'/*)*/,
      'tag' => '0'
    ),
    'JP-33' => array('country' => 'JP', 'title' => /*t(*/'Okayama'/*)*/, 'tag' => '0'),
    'JP-34' => array(
      'country' => 'JP',
      'title' => /*t(*/'Hirosima (Hiroshima)'/*)*/,
      'tag' => '0'
    ),
    'JP-35' => array(
      'country' => 'JP',
      'title' => /*t(*/'Yamaguti (Yamaguchi)'/*)*/,
      'tag' => '0'
    ),
    'JP-36' => array(
      'country' => 'JP',
      'title' => /*t(*/'Tokusima (Tokushima)'/*)*/,
      'tag' => '0'
    ),
    'JP-37' => array('country' => 'JP', 'title' => /*t(*/'Kagawa'/*)*/, 'tag' => '0'),
    'JP-38' => array('country' => 'JP', 'title' => /*t(*/'Ehime'/*)*/, 'tag' => '0'),
    'JP-39' => array(
      'country' => 'JP',
      'title' => /*t(*/'Koti (Kochi)'/*)*/,
      'tag' => '0'
    ),
    'JP-40' => array(
      'country' => 'JP',
      'title' => /*t(*/'Hukuoka (Fukuoka)'/*)*/,
      'tag' => '0'
    ),
    'JP-41' => array('country' => 'JP', 'title' => /*t(*/'Saga'/*)*/, 'tag' => '0'),
    'JP-42' => array('country' => 'JP', 'title' => /*t(*/'Nagasaki'/*)*/, 'tag' => '0'),
    'JP-43' => array('country' => 'JP', 'title' => /*t(*/'Kumamoto'/*)*/, 'tag' => '0'),
    'JP-44' => array('country' => 'JP', 'title' => /*t(*/'Oita'/*)*/, 'tag' => '0'),
    'JP-45' => array('country' => 'JP', 'title' => /*t(*/'Miyazaki'/*)*/, 'tag' => '0'),
    'JP-46' => array(
      'country' => 'JP',
      'title' => /*t(*/'Kagosima (Kagoshima)'/*)*/,
      'tag' => '0'
    ),
    'JP-47' => array('country' => 'JP', 'title' => /*t(*/'Okinawa'/*)*/, 'tag' => '0'),
    'KE-CE' => array('country' => 'KE', 'title' => /*t(*/'Central'/*)*/, 'tag' => '0'),
    'KE-CO' => array('country' => 'KE', 'title' => /*t(*/'Coast'/*)*/, 'tag' => '0'),
    'KE-EA' => array('country' => 'KE', 'title' => /*t(*/'Eastern'/*)*/, 'tag' => '0'),
    'KE-NA' => array(
      'country' => 'KE',
      'title' => /*t(*/'Nairobi Area'/*)*/,
      'tag' => '0'
    ),
    'KE-NE' => array(
      'country' => 'KE',
      'title' => /*t(*/'North Eastern'/*)*/,
      'tag' => '0'
    ),
    'KE-NY' => array('country' => 'KE', 'title' => /*t(*/'Nyanza'/*)*/, 'tag' => '0'),
    'KE-RV' => array('country' => 'KE', 'title' => /*t(*/'Rift Valley'/*)*/, 'tag' => '0'),
    'KE-WE' => array('country' => 'KE', 'title' => /*t(*/'Western'/*)*/, 'tag' => '0'),
    'KG-B' => array('country' => 'KG', 'title' => /*t(*/'Batken'/*)*/, 'tag' => '0'),
    'KG-C' => array('country' => 'KG', 'title' => /*t(*/'Chu'/*)*/, 'tag' => '0'),
    'KG-GB' => array('country' => 'KG', 'title' => /*t(*/'Bishkek'/*)*/, 'tag' => '0'),
    'KG-J' => array('country' => 'KG', 'title' => /*t(*/'Jalal-Abad'/*)*/, 'tag' => '0'),
    'KG-N' => array('country' => 'KG', 'title' => /*t(*/'Naryn'/*)*/, 'tag' => '0'),
    'KG-O' => array('country' => 'KG', 'title' => /*t(*/'Osh'/*)*/, 'tag' => '0'),
    'KG-T' => array('country' => 'KG', 'title' => /*t(*/'Talas'/*)*/, 'tag' => '0'),
    'KG-Y' => array('country' => 'KG', 'title' => /*t(*/'Ysyk-Kol'/*)*/, 'tag' => '0'),
    'KH-BA' => array('country' => 'KH', 'title' => /*t(*/'Battambang'/*)*/, 'tag' => '0'),
    'KH-BM' => array(
      'country' => 'KH',
      'title' => /*t(*/'Banteay Meanchey'/*)*/,
      'tag' => '0'
    ),
    'KH-KB' => array('country' => 'KH', 'title' => /*t(*/'Keb'/*)*/, 'tag' => '0'),
    'KH-KK' => array('country' => 'KH', 'title' => /*t(*/'Kaoh Kong'/*)*/, 'tag' => '0'),
    'KH-KL' => array('country' => 'KH', 'title' => /*t(*/'Kandal'/*)*/, 'tag' => '0'),
    'KH-KM' => array(
      'country' => 'KH',
      'title' => /*t(*/'Kampong Cham'/*)*/,
      'tag' => '0'
    ),
    'KH-KN' => array(
      'country' => 'KH',
      'title' => /*t(*/'Kampong Chhnang'/*)*/,
      'tag' => '0'
    ),
    'KH-KO' => array('country' => 'KH', 'title' => /*t(*/'Kampong Som'/*)*/, 'tag' => '0'),
    'KH-KP' => array('country' => 'KH', 'title' => /*t(*/'Kampot'/*)*/, 'tag' => '0'),
    'KH-KR' => array('country' => 'KH', 'title' => /*t(*/'Kratie'/*)*/, 'tag' => '0'),
    'KH-KT' => array(
      'country' => 'KH',
      'title' => /*t(*/'Kampong Thom'/*)*/,
      'tag' => '0'
    ),
    'KH-KU' => array(
      'country' => 'KH',
      'title' => /*t(*/'Kampong Speu'/*)*/,
      'tag' => '0'
    ),
    'KH-MK' => array('country' => 'KH', 'title' => /*t(*/'Mondul Kiri'/*)*/, 'tag' => '0'),
    'KH-OM' => array(
      'country' => 'KH',
      'title' => /*t(*/'Oddar Meancheay'/*)*/,
      'tag' => '0'
    ),
    'KH-PA' => array('country' => 'KH', 'title' => /*t(*/'Pailin'/*)*/, 'tag' => '0'),
    'KH-PG' => array('country' => 'KH', 'title' => /*t(*/'Prey Veng'/*)*/, 'tag' => '0'),
    'KH-PP' => array('country' => 'KH', 'title' => /*t(*/'Phnom Penh'/*)*/, 'tag' => '0'),
    'KH-PR' => array(
      'country' => 'KH',
      'title' => /*t(*/'Preah Vihear'/*)*/,
      'tag' => '0'
    ),
    'KH-PS' => array(
      'country' => 'KH',
      'title' => /*t(*/'Preah Seihanu (Kompong Som or Sihanoukville)'/*)*/,
      'tag' => '0'
    ),
    'KH-PU' => array('country' => 'KH', 'title' => /*t(*/'Pursat'/*)*/, 'tag' => '0'),
    'KH-RK' => array(
      'country' => 'KH',
      'title' => /*t(*/'Ratanak Kiri'/*)*/,
      'tag' => '0'
    ),
    'KH-SI' => array('country' => 'KH', 'title' => /*t(*/'Siemreap'/*)*/, 'tag' => '0'),
    'KH-SR' => array('country' => 'KH', 'title' => /*t(*/'Svay Rieng'/*)*/, 'tag' => '0'),
    'KH-ST' => array('country' => 'KH', 'title' => /*t(*/'Stung Treng'/*)*/, 'tag' => '0'),
    'KH-TK' => array('country' => 'KH', 'title' => /*t(*/'Takeo'/*)*/, 'tag' => '0'),
    'KI-AG' => array('country' => 'KI', 'title' => /*t(*/'Abaiang'/*)*/, 'tag' => '0'),
    'KI-AK' => array('country' => 'KI', 'title' => /*t(*/'Aranuka'/*)*/, 'tag' => '0'),
    'KI-AM' => array('country' => 'KI', 'title' => /*t(*/'Abemama'/*)*/, 'tag' => '0'),
    'KI-AO' => array('country' => 'KI', 'title' => /*t(*/'Arorae'/*)*/, 'tag' => '0'),
    'KI-BA' => array('country' => 'KI', 'title' => /*t(*/'Banaba'/*)*/, 'tag' => '0'),
    'KI-BE' => array('country' => 'KI', 'title' => /*t(*/'Beru'/*)*/, 'tag' => '0'),
    'KI-bT' => array('country' => 'KI', 'title' => /*t(*/'Butaritari'/*)*/, 'tag' => '0'),
    'KI-KA' => array('country' => 'KI', 'title' => /*t(*/'Kanton'/*)*/, 'tag' => '0'),
    'KI-KR' => array('country' => 'KI', 'title' => /*t(*/'Kiritimati'/*)*/, 'tag' => '0'),
    'KI-KU' => array('country' => 'KI', 'title' => /*t(*/'Kuria'/*)*/, 'tag' => '0'),
    'KI-ME' => array('country' => 'KI', 'title' => /*t(*/'Marakei'/*)*/, 'tag' => '0'),
    'KI-MI' => array('country' => 'KI', 'title' => /*t(*/'Maiana'/*)*/, 'tag' => '0'),
    'KI-MN' => array('country' => 'KI', 'title' => /*t(*/'Makin'/*)*/, 'tag' => '0'),
    'KI-NI' => array('country' => 'KI', 'title' => /*t(*/'Nikunau'/*)*/, 'tag' => '0'),
    'KI-NO' => array('country' => 'KI', 'title' => /*t(*/'Nonouti'/*)*/, 'tag' => '0'),
    'KI-ON' => array('country' => 'KI', 'title' => /*t(*/'Onotoa'/*)*/, 'tag' => '0'),
    'KI-TE' => array('country' => 'KI', 'title' => /*t(*/'Teraina'/*)*/, 'tag' => '0'),
    'KI-TM' => array('country' => 'KI', 'title' => /*t(*/'Tamana'/*)*/, 'tag' => '0'),
    'KI-TR' => array('country' => 'KI', 'title' => /*t(*/'Tabuaeran'/*)*/, 'tag' => '0'),
    'KI-TT' => array('country' => 'KI', 'title' => /*t(*/'Tabiteuea'/*)*/, 'tag' => '0'),
    'KI-TW' => array('country' => 'KI', 'title' => /*t(*/'Tarawa'/*)*/, 'tag' => '0'),
    'KM-A' => array('country' => 'KM', 'title' => /*t(*/'Anjouan'/*)*/, 'tag' => '0'),
    'KM-G' => array(
      'country' => 'KM',
      'title' => /*t(*/'Grande Comore'/*)*/,
      'tag' => '0'
    ),
    'KM-M' => array('country' => 'KM', 'title' => /*t(*/'Moheli'/*)*/, 'tag' => '0'),
    'KN-CAP' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint Paul Capesterre'/*)*/,
      'tag' => '0'
    ),
    'KN-CCN' => array(
      'country' => 'KN',
      'title' => /*t(*/'Christ Church Nichola Town'/*)*/,
      'tag' => '0'
    ),
    'KN-CHA' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint Paul Charlestown'/*)*/,
      'tag' => '0'
    ),
    'KN-SAS' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint Anne Sandy Point'/*)*/,
      'tag' => '0'
    ),
    'KN-SGB' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint George Basseterre'/*)*/,
      'tag' => '0'
    ),
    'KN-SGG' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint George Gingerland'/*)*/,
      'tag' => '0'
    ),
    'KN-SJC' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint John Capesterre'/*)*/,
      'tag' => '0'
    ),
    'KN-SJF' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint John Figtree'/*)*/,
      'tag' => '0'
    ),
    'KN-SJW' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint James Windward'/*)*/,
      'tag' => '0'
    ),
    'KN-SMC' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint Mary Cayon'/*)*/,
      'tag' => '0'
    ),
    'KN-SPB' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint Peter Basseterre'/*)*/,
      'tag' => '0'
    ),
    'KN-STL' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint Thomas Lowland'/*)*/,
      'tag' => '0'
    ),
    'KN-STM' => array(
      'country' => 'KN',
      'title' => /*t(*/'Saint Thomas Middle Island'/*)*/,
      'tag' => '0'
    ),
    'KN-TPP' => array(
      'country' => 'KN',
      'title' => /*t(*/'Trinity Palmetto Point'/*)*/,
      'tag' => '0'
    ),
    'KP-CHA' => array('country' => 'KP', 'title' => /*t(*/'Chagang-do'/*)*/, 'tag' => '0'),
    'KP-HAB' => array(
      'country' => 'KP',
      'title' => /*t(*/'Hamgyong-bukto'/*)*/,
      'tag' => '0'
    ),
    'KP-HAN' => array(
      'country' => 'KP',
      'title' => /*t(*/'Hamgyong-namdo'/*)*/,
      'tag' => '0'
    ),
    'KP-HWB' => array(
      'country' => 'KP',
      'title' => /*t(*/'Hwanghae-bukto'/*)*/,
      'tag' => '0'
    ),
    'KP-HWN' => array(
      'country' => 'KP',
      'title' => /*t(*/'Hwanghae-namdo'/*)*/,
      'tag' => '0'
    ),
    'KP-KAN' => array('country' => 'KP', 'title' => /*t(*/'Kangwon-do'/*)*/, 'tag' => '0'),
    'KP-NAJ' => array(
      'country' => 'KP',
      'title' => /*t(*/'Rason Directly Governed City'/*)*/,
      'tag' => '0'
    ),
    'KP-PYB' => array(
      'country' => 'KP',
      'title' => /*t(*/"P'yongan-bukto"/*)*/,
      'tag' => '0'
    ),
    'KP-PYN' => array(
      'country' => 'KP',
      'title' => /*t(*/"P'yongan-namdo"/*)*/,
      'tag' => '0'
    ),
    'KP-PYO' => array(
      'country' => 'KP',
      'title' => /*t(*/"P'yongyang Special City"/*)*/,
      'tag' => '0'
    ),
    'KP-YAN' => array(
      'country' => 'KP',
      'title' => /*t(*/'Ryanggang-do (Yanggang-do)'/*)*/,
      'tag' => '0'
    ),
    'KR-11' => array(
      'country' => 'KR',
      'title' => /*t(*/'Seoul Special City'/*)*/,
      'tag' => '0'
    ),
    'KR-26' => array(
      'country' => 'KR',
      'title' => /*t(*/'Busan Metropolitan City'/*)*/,
      'tag' => '0'
    ),
    'KR-27' => array(
      'country' => 'KR',
      'title' => /*t(*/'Daegu Metropolitan City'/*)*/,
      'tag' => '0'
    ),
    'KR-28' => array(
      'country' => 'KR',
      'title' => /*t(*/'Incheon Metropolitan City'/*)*/,
      'tag' => '0'
    ),
    'KR-29' => array(
      'country' => 'KR',
      'title' => /*t(*/'Gwangju Metropolitan City'/*)*/,
      'tag' => '0'
    ),
    'KR-30' => array(
      'country' => 'KR',
      'title' => /*t(*/'Daejeon Metropolitan City'/*)*/,
      'tag' => '0'
    ),
    'KR-31' => array(
      'country' => 'KR',
      'title' => /*t(*/'Ulsan Metropolitan City'/*)*/,
      'tag' => '0'
    ),
    'KR-41' => array('country' => 'KR', 'title' => /*t(*/'Gyeonggi-do'/*)*/, 'tag' => '0'),
    'KR-42' => array('country' => 'KR', 'title' => /*t(*/'Gangwon-do'/*)*/, 'tag' => '0'),
    'KR-43' => array(
      'country' => 'KR',
      'title' => /*t(*/'Chungcheongbuk-do'/*)*/,
      'tag' => '0'
    ),
    'KR-44' => array(
      'country' => 'KR',
      'title' => /*t(*/'Chungcheongnam-do'/*)*/,
      'tag' => '0'
    ),
    'KR-45' => array(
      'country' => 'KR',
      'title' => /*t(*/'Jeollabuk-do'/*)*/,
      'tag' => '0'
    ),
    'KR-46' => array(
      'country' => 'KR',
      'title' => /*t(*/'Jeollanam-do'/*)*/,
      'tag' => '0'
    ),
    'KR-47' => array(
      'country' => 'KR',
      'title' => /*t(*/'Gyeongsangbuk-do'/*)*/,
      'tag' => '0'
    ),
    'KR-48' => array(
      'country' => 'KR',
      'title' => /*t(*/'Gyeongsangnam-do'/*)*/,
      'tag' => '0'
    ),
    'KR-49' => array('country' => 'KR', 'title' => /*t(*/'Jeju-do'/*)*/, 'tag' => '0'),
    'KW-D' => array('country' => 'KW', 'title' => /*t(*/'Al Ahmadi'/*)*/, 'tag' => '0'),
    'KW-F' => array(
      'country' => 'KW',
      'title' => /*t(*/'Al Farwaniyah'/*)*/,
      'tag' => '0'
    ),
    'KW-H' => array('country' => 'KW', 'title' => /*t(*/'Hawalli'/*)*/, 'tag' => '0'),
    'KW-J' => array('country' => 'KW', 'title' => /*t(*/'Al Jahra'/*)*/, 'tag' => '0'),
    'KW-S' => array('country' => 'KW', 'title' => /*t(*/'Al Asimah'/*)*/, 'tag' => '0'),
    'KY-CR' => array('country' => 'KY', 'title' => /*t(*/'Creek'/*)*/, 'tag' => '0'),
    'KY-EA' => array('country' => 'KY', 'title' => /*t(*/'Eastern'/*)*/, 'tag' => '0'),
    'KY-ML' => array('country' => 'KY', 'title' => /*t(*/'Midland'/*)*/, 'tag' => '0'),
    'KY-SK' => array('country' => 'KY', 'title' => /*t(*/'Stake Bay'/*)*/, 'tag' => '0'),
    'KY-SP' => array('country' => 'KY', 'title' => /*t(*/'Spot Bay'/*)*/, 'tag' => '0'),
    'KY-ST' => array('country' => 'KY', 'title' => /*t(*/'South Town'/*)*/, 'tag' => '0'),
    'KY-WD' => array('country' => 'KY', 'title' => /*t(*/'West End'/*)*/, 'tag' => '0'),
    'KY-WN' => array('country' => 'KY', 'title' => /*t(*/'Western'/*)*/, 'tag' => '0'),
    'KZ-AKM' => array('country' => 'KZ', 'title' => /*t(*/'Aqmola'/*)*/, 'tag' => '0'),
    'KZ-AKT' => array('country' => 'KZ', 'title' => /*t(*/'Aqtobe'/*)*/, 'tag' => '0'),
    'KZ-ALA' => array('country' => 'KZ', 'title' => /*t(*/'Almaty'/*)*/, 'tag' => '0'),
    'KZ-ALM' => array('country' => 'KZ', 'title' => /*t(*/'Almaty'/*)*/, 'tag' => '0'),
    'KZ-AST' => array('country' => 'KZ', 'title' => /*t(*/'Astana'/*)*/, 'tag' => '0'),
    'KZ-ATY' => array('country' => 'KZ', 'title' => /*t(*/'Atyrau'/*)*/, 'tag' => '0'),
    'KZ-KAR' => array('country' => 'KZ', 'title' => /*t(*/'Qaraghandy'/*)*/, 'tag' => '0'),
    'KZ-KUS' => array('country' => 'KZ', 'title' => /*t(*/'Qustanay'/*)*/, 'tag' => '0'),
    'KZ-KZY' => array('country' => 'KZ', 'title' => /*t(*/'Qyzylorda'/*)*/, 'tag' => '0'),
    'KZ-MAN' => array(
      'country' => 'KZ',
      'title' => /*t(*/'Mangghystau'/*)*/,
      'tag' => '0'
    ),
    'KZ-PAV' => array('country' => 'KZ', 'title' => /*t(*/'Paylodar'/*)*/, 'tag' => '0'),
    'KZ-SEV' => array(
      'country' => 'KZ',
      'title' => /*t(*/'Soltustik Qazaqstan'/*)*/,
      'tag' => '0'
    ),
    'KZ-VOS' => array(
      'country' => 'KZ',
      'title' => /*t(*/'Shyghys Qazaqstan'/*)*/,
      'tag' => '0'
    ),
    'KZ-YUZ' => array(
      'country' => 'KZ',
      'title' => /*t(*/'Ongtustik Qazaqstan'/*)*/,
      'tag' => '0'
    ),
    'KZ-ZAP' => array(
      'country' => 'KZ',
      'title' => /*t(*/'Baty Qazaqstan'/*)*/,
      'tag' => '0'
    ),
    'KZ-ZHA' => array('country' => 'KZ', 'title' => /*t(*/'Zhambyl'/*)*/, 'tag' => '0'),
    'LA-AT' => array('country' => 'LA', 'title' => /*t(*/'Attapu'/*)*/, 'tag' => '0'),
    'LA-BK' => array('country' => 'LA', 'title' => /*t(*/'Bokeo'/*)*/, 'tag' => '0'),
    'LA-BL' => array('country' => 'LA', 'title' => /*t(*/'Bolikhamxai'/*)*/, 'tag' => '0'),
    'LA-CH' => array('country' => 'LA', 'title' => /*t(*/'Champasak'/*)*/, 'tag' => '0'),
    'LA-HO' => array('country' => 'LA', 'title' => /*t(*/'Houaphan'/*)*/, 'tag' => '0'),
    'LA-KH' => array('country' => 'LA', 'title' => /*t(*/'Khammouan'/*)*/, 'tag' => '0'),
    'LA-LM' => array(
      'country' => 'LA',
      'title' => /*t(*/'Louang Namtha'/*)*/,
      'tag' => '0'
    ),
    'LA-LP' => array(
      'country' => 'LA',
      'title' => /*t(*/'Louangphabang'/*)*/,
      'tag' => '0'
    ),
    'LA-OU' => array('country' => 'LA', 'title' => /*t(*/'Oudomxai'/*)*/, 'tag' => '0'),
    'LA-PH' => array('country' => 'LA', 'title' => /*t(*/'Phongsali'/*)*/, 'tag' => '0'),
    'LA-SL' => array('country' => 'LA', 'title' => /*t(*/'Salavan'/*)*/, 'tag' => '0'),
    'LA-SV' => array('country' => 'LA', 'title' => /*t(*/'Savannakhet'/*)*/, 'tag' => '0'),
    'LA-VI' => array('country' => 'LA', 'title' => /*t(*/'Vientiane'/*)*/, 'tag' => '0'),
    'LA-VT' => array('country' => 'LA', 'title' => /*t(*/'Vientiane'/*)*/, 'tag' => '0'),
    'LA-XA' => array('country' => 'LA', 'title' => /*t(*/'Xaignabouli'/*)*/, 'tag' => '0'),
    'LA-XE' => array('country' => 'LA', 'title' => /*t(*/'Xekong'/*)*/, 'tag' => '0'),
    'LA-XI' => array('country' => 'LA', 'title' => /*t(*/'Xiangkhoang'/*)*/, 'tag' => '0'),
    'LA-XN' => array('country' => 'LA', 'title' => /*t(*/'Xaisomboun'/*)*/, 'tag' => '0'),
    'LC-AR' => array(
      'country' => 'LC',
      'title' => /*t(*/'Anse-la-Raye'/*)*/,
      'tag' => '0'
    ),
    'LC-CA' => array('country' => 'LC', 'title' => /*t(*/'Castries'/*)*/, 'tag' => '0'),
    'LC-CH' => array('country' => 'LC', 'title' => /*t(*/'Choiseul'/*)*/, 'tag' => '0'),
    'LC-DA' => array('country' => 'LC', 'title' => /*t(*/'Dauphin'/*)*/, 'tag' => '0'),
    'LC-DE' => array('country' => 'LC', 'title' => /*t(*/'Dennery'/*)*/, 'tag' => '0'),
    'LC-GI' => array('country' => 'LC', 'title' => /*t(*/'Gros-Islet'/*)*/, 'tag' => '0'),
    'LC-LA' => array('country' => 'LC', 'title' => /*t(*/'Laborie'/*)*/, 'tag' => '0'),
    'LC-MI' => array('country' => 'LC', 'title' => /*t(*/'Micoud'/*)*/, 'tag' => '0'),
    'LC-PR' => array('country' => 'LC', 'title' => /*t(*/'Praslin'/*)*/, 'tag' => '0'),
    'LC-SO' => array('country' => 'LC', 'title' => /*t(*/'Soufriere'/*)*/, 'tag' => '0'),
    'LC-VF' => array('country' => 'LC', 'title' => /*t(*/'Vieux-Fort'/*)*/, 'tag' => '0'),
    'LI-A' => array('country' => 'LI', 'title' => /*t(*/'Schaan'/*)*/, 'tag' => '0'),
    'LI-B' => array('country' => 'LI', 'title' => /*t(*/'Balzers'/*)*/, 'tag' => '0'),
    'LI-E' => array('country' => 'LI', 'title' => /*t(*/'Eschen'/*)*/, 'tag' => '0'),
    'LI-G' => array('country' => 'LI', 'title' => /*t(*/'Gamprin'/*)*/, 'tag' => '0'),
    'Li-L' => array('country' => 'LI', 'title' => /*t(*/'Schellenberg'/*)*/, 'tag' => '0'),
    'LI-M' => array('country' => 'LI', 'title' => /*t(*/'Mauren'/*)*/, 'tag' => '0'),
    'LI-N' => array('country' => 'LI', 'title' => /*t(*/'Triesen'/*)*/, 'tag' => '0'),
    'LI-P' => array('country' => 'LI', 'title' => /*t(*/'Planken'/*)*/, 'tag' => '0'),
    'LI-R' => array('country' => 'LI', 'title' => /*t(*/'Ruggell'/*)*/, 'tag' => '0'),
    'LI-T' => array('country' => 'LI', 'title' => /*t(*/'Triesenberg'/*)*/, 'tag' => '0'),
    'LI-V' => array('country' => 'LI', 'title' => /*t(*/'Vaduz'/*)*/, 'tag' => '0'),
    'LK-CE' => array('country' => 'LK', 'title' => /*t(*/'Central'/*)*/, 'tag' => '0'),
    'LK-EA' => array('country' => 'LK', 'title' => /*t(*/'Eastern'/*)*/, 'tag' => '0'),
    'LK-NC' => array(
      'country' => 'LK',
      'title' => /*t(*/'North Central'/*)*/,
      'tag' => '0'
    ),
    'LK-NO' => array('country' => 'LK', 'title' => /*t(*/'Northern'/*)*/, 'tag' => '0'),
    'LK-NW' => array(
      'country' => 'LK',
      'title' => /*t(*/'North Western'/*)*/,
      'tag' => '0'
    ),
    'LK-SA' => array(
      'country' => 'LK',
      'title' => /*t(*/'Sabaragamuwa'/*)*/,
      'tag' => '0'
    ),
    'LK-SO' => array('country' => 'LK', 'title' => /*t(*/'Southern'/*)*/, 'tag' => '0'),
    'LK-UV' => array('country' => 'LK', 'title' => /*t(*/'Uva'/*)*/, 'tag' => '0'),
    'LK-WE' => array('country' => 'LK', 'title' => /*t(*/'Western'/*)*/, 'tag' => '0'),
    'LR-BG' => array('country' => 'LR', 'title' => /*t(*/'Bong'/*)*/, 'tag' => '0'),
    'LR-BI' => array('country' => 'LR', 'title' => /*t(*/'Bomi'/*)*/, 'tag' => '0'),
    'LR-CM' => array(
      'country' => 'LR',
      'title' => /*t(*/'Grand Cape Mount'/*)*/,
      'tag' => '0'
    ),
    'LR-GB' => array('country' => 'LR', 'title' => /*t(*/'Grand Bassa'/*)*/, 'tag' => '0'),
    'LR-GG' => array('country' => 'LR', 'title' => /*t(*/'Grand Gedeh'/*)*/, 'tag' => '0'),
    'LR-GK' => array('country' => 'LR', 'title' => /*t(*/'Grand Kru'/*)*/, 'tag' => '0'),
    'LR-LO' => array('country' => 'LR', 'title' => /*t(*/'Lofa'/*)*/, 'tag' => '0'),
    'LR-MG' => array('country' => 'LR', 'title' => /*t(*/'Margibi'/*)*/, 'tag' => '0'),
    'LR-ML' => array('country' => 'LR', 'title' => /*t(*/'Maryland'/*)*/, 'tag' => '0'),
    'LR-MS' => array('country' => 'LR', 'title' => /*t(*/'Montserrado'/*)*/, 'tag' => '0'),
    'LR-NB' => array('country' => 'LR', 'title' => /*t(*/'Nimba'/*)*/, 'tag' => '0'),
    'LR-RC' => array('country' => 'LR', 'title' => /*t(*/'River Cess'/*)*/, 'tag' => '0'),
    'LR-SN' => array('country' => 'LR', 'title' => /*t(*/'Sinoe'/*)*/, 'tag' => '0'),
    'LS-BB' => array('country' => 'LS', 'title' => /*t(*/'Butha-Buthe'/*)*/, 'tag' => '0'),
    'LS-BE' => array('country' => 'LS', 'title' => /*t(*/'Berea'/*)*/, 'tag' => '0'),
    'LS-LE' => array('country' => 'LS', 'title' => /*t(*/'Leribe'/*)*/, 'tag' => '0'),
    'LS-MF' => array('country' => 'LS', 'title' => /*t(*/'Mafeteng'/*)*/, 'tag' => '0'),
    'LS-MH' => array(
      'country' => 'LS',
      'title' => /*t(*/"Mohale's Hoek"/*)*/,
      'tag' => '0'
    ),
    'LS-MK' => array('country' => 'LS', 'title' => /*t(*/'Mokhotlong'/*)*/, 'tag' => '0'),
    'LS-MS' => array('country' => 'LS', 'title' => /*t(*/'Maseru'/*)*/, 'tag' => '0'),
    'LS-QN' => array(
      'country' => 'LS',
      'title' => /*t(*/"Qacha's Nek"/*)*/,
      'tag' => '0'
    ),
    'LS-QT' => array('country' => 'LS', 'title' => /*t(*/'Quthing'/*)*/, 'tag' => '0'),
    'LS-TT' => array('country' => 'LS', 'title' => /*t(*/'Thaba-Tseka'/*)*/, 'tag' => '0'),
    'LT-AL' => array('country' => 'LT', 'title' => /*t(*/'Alytus'/*)*/, 'tag' => '0'),
    'LT-KA' => array('country' => 'LT', 'title' => /*t(*/'Kaunas'/*)*/, 'tag' => '0'),
    'LT-KL' => array('country' => 'LT', 'title' => /*t(*/'Klaipeda'/*)*/, 'tag' => '0'),
    'LT-MA' => array('country' => 'LT', 'title' => /*t(*/'Marijampole'/*)*/, 'tag' => '0'),
    'LT-PA' => array('country' => 'LT', 'title' => /*t(*/'Panevezys'/*)*/, 'tag' => '0'),
    'LT-SI' => array('country' => 'LT', 'title' => /*t(*/'Siauliai'/*)*/, 'tag' => '0'),
    'LT-TA' => array('country' => 'LT', 'title' => /*t(*/'Taurage'/*)*/, 'tag' => '0'),
    'LT-TE' => array('country' => 'LT', 'title' => /*t(*/'Telsiai'/*)*/, 'tag' => '0'),
    'LT-UT' => array('country' => 'LT', 'title' => /*t(*/'Utena'/*)*/, 'tag' => '0'),
    'LT-VI' => array('country' => 'LT', 'title' => /*t(*/'Vilnius'/*)*/, 'tag' => '0'),
    'LU-DC' => array('country' => 'LU', 'title' => /*t(*/'Clervaux'/*)*/, 'tag' => '0'),
    'LU-DD' => array('country' => 'LU', 'title' => /*t(*/'Diekirch'/*)*/, 'tag' => '0'),
    'LU-DR' => array('country' => 'LU', 'title' => /*t(*/'Redange'/*)*/, 'tag' => '0'),
    'LU-DV' => array('country' => 'LU', 'title' => /*t(*/'Vianden'/*)*/, 'tag' => '0'),
    'LU-DW' => array('country' => 'LU', 'title' => /*t(*/'Wiltz'/*)*/, 'tag' => '0'),
    'LU-GE' => array('country' => 'LU', 'title' => /*t(*/'Echternach'/*)*/, 'tag' => '0'),
    'LU-GG' => array(
      'country' => 'LU',
      'title' => /*t(*/'Grevenmacher'/*)*/,
      'tag' => '0'
    ),
    'LU-GR' => array('country' => 'LU', 'title' => /*t(*/'Remich'/*)*/, 'tag' => '0'),
    'LU-LC' => array('country' => 'LU', 'title' => /*t(*/'Capellen'/*)*/, 'tag' => '0'),
    'LU-LE' => array(
      'country' => 'LU',
      'title' => /*t(*/'Esch-sur-Alzette'/*)*/,
      'tag' => '0'
    ),
    'LU-LL' => array('country' => 'LU', 'title' => /*t(*/'Luxembourg'/*)*/, 'tag' => '0'),
    'LU-LM' => array('country' => 'LU', 'title' => /*t(*/'Mersch'/*)*/, 'tag' => '0'),
    'LV-AIZ' => array(
      'country' => 'LV',
      'title' => /*t(*/'Aizkraukles Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-ALU' => array(
      'country' => 'LV',
      'title' => /*t(*/'Aluksnes Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-BAL' => array(
      'country' => 'LV',
      'title' => /*t(*/'Balvu Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-BAU' => array(
      'country' => 'LV',
      'title' => /*t(*/'Bauskas Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-CES' => array(
      'country' => 'LV',
      'title' => /*t(*/'Cesu Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-DGR' => array(
      'country' => 'LV',
      'title' => /*t(*/'Daugavpils Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-DGV' => array('country' => 'LV', 'title' => /*t(*/'Daugavpils'/*)*/, 'tag' => '0'),
    'LV-DOB' => array(
      'country' => 'LV',
      'title' => /*t(*/'Dobeles Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-GUL' => array(
      'country' => 'LV',
      'title' => /*t(*/'Gulbenes Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-JEK' => array(
      'country' => 'LV',
      'title' => /*t(*/'Jekabpils Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-JGR' => array(
      'country' => 'LV',
      'title' => /*t(*/'Jelgavas Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-JGV' => array('country' => 'LV', 'title' => /*t(*/'Jelgava'/*)*/, 'tag' => '0'),
    'LV-JUR' => array('country' => 'LV', 'title' => /*t(*/'Jurmala'/*)*/, 'tag' => '0'),
    'LV-KRA' => array(
      'country' => 'LV',
      'title' => /*t(*/'Kraslavas Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-KUL' => array(
      'country' => 'LV',
      'title' => /*t(*/'Kuldigas Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-LIM' => array(
      'country' => 'LV',
      'title' => /*t(*/'Limbazu Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-LPK' => array('country' => 'LV', 'title' => /*t(*/'Liepaja'/*)*/, 'tag' => '0'),
    'LV-LPR' => array(
      'country' => 'LV',
      'title' => /*t(*/'Liepajas Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-LUD' => array(
      'country' => 'LV',
      'title' => /*t(*/'Ludzas Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-MAD' => array(
      'country' => 'LV',
      'title' => /*t(*/'Madonas Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-OGR' => array(
      'country' => 'LV',
      'title' => /*t(*/'Ogres Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-PRE' => array(
      'country' => 'LV',
      'title' => /*t(*/'Preilu Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-RGA' => array('country' => 'LV', 'title' => /*t(*/'Riga'/*)*/, 'tag' => '0'),
    'LV-RGR' => array(
      'country' => 'LV',
      'title' => /*t(*/'Rigas Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-RZK' => array('country' => 'LV', 'title' => /*t(*/'Rezekne'/*)*/, 'tag' => '0'),
    'LV-RZR' => array(
      'country' => 'LV',
      'title' => /*t(*/'Rezeknes Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-SAL' => array(
      'country' => 'LV',
      'title' => /*t(*/'Saldus Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-TAL' => array(
      'country' => 'LV',
      'title' => /*t(*/'Talsu Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-TUK' => array(
      'country' => 'LV',
      'title' => /*t(*/'Tukuma Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-VLK' => array(
      'country' => 'LV',
      'title' => /*t(*/'Valkas Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-VLM' => array(
      'country' => 'LV',
      'title' => /*t(*/'Valmieras Rajons'/*)*/,
      'tag' => '0'
    ),
    'LV-VSL' => array('country' => 'LV', 'title' => /*t(*/'Ventspils'/*)*/, 'tag' => '0'),
    'LV-VSR' => array(
      'country' => 'LV',
      'title' => /*t(*/'Ventspils Rajons'/*)*/,
      'tag' => '0'
    ),
    'LY-AJ' => array('country' => 'LY', 'title' => /*t(*/'Ajdabiya'/*)*/, 'tag' => '0'),
    'LY-AS' => array('country' => 'LY', 'title' => /*t(*/"Ash Shati'"/*)*/, 'tag' => '0'),
    'LY-AW' => array('country' => 'LY', 'title' => /*t(*/'Awbari'/*)*/, 'tag' => '0'),
    'LY-AZ' => array(
      'country' => 'LY',
      'title' => /*t(*/"Al 'Aziziyah"/*)*/,
      'tag' => '0'
    ),
    'LY-BA' => array('country' => 'LY', 'title' => /*t(*/'Banghazi'/*)*/, 'tag' => '0'),
    'LY-DA' => array('country' => 'LY', 'title' => /*t(*/'Darnah'/*)*/, 'tag' => '0'),
    'LY-FA' => array('country' => 'LY', 'title' => /*t(*/'Al Fatih'/*)*/, 'tag' => '0'),
    'LY-GD' => array('country' => 'LY', 'title' => /*t(*/'Ghadamis'/*)*/, 'tag' => '0'),
    'LY-GY' => array('country' => 'LY', 'title' => /*t(*/'Gharyan'/*)*/, 'tag' => '0'),
    'LY-JA' => array(
      'country' => 'LY',
      'title' => /*t(*/'Al Jabal al Akhdar'/*)*/,
      'tag' => '0'
    ),
    'LY-JU' => array('country' => 'LY', 'title' => /*t(*/'Al Jufrah'/*)*/, 'tag' => '0'),
    'LY-KH' => array('country' => 'LY', 'title' => /*t(*/'Al Khums'/*)*/, 'tag' => '0'),
    'LY-KU' => array('country' => 'LY', 'title' => /*t(*/'Al Kufrah'/*)*/, 'tag' => '0'),
    'LY-MI' => array('country' => 'LY', 'title' => /*t(*/'Misratah'/*)*/, 'tag' => '0'),
    'LY-MZ' => array('country' => 'LY', 'title' => /*t(*/'Murzuq'/*)*/, 'tag' => '0'),
    'LY-NK' => array(
      'country' => 'LY',
      'title' => /*t(*/'An Nuqat al Khams'/*)*/,
      'tag' => '0'
    ),
    'LY-SB' => array('country' => 'LY', 'title' => /*t(*/'Sabha'/*)*/, 'tag' => '0'),
    'LY-SU' => array('country' => 'LY', 'title' => /*t(*/'Surt'/*)*/, 'tag' => '0'),
    'LY-SW' => array('country' => 'LY', 'title' => /*t(*/'Sawfajjin'/*)*/, 'tag' => '0'),
    'LY-TH' => array('country' => 'LY', 'title' => /*t(*/'Tarhunah'/*)*/, 'tag' => '0'),
    'LY-TL' => array(
      'country' => 'LY',
      'title' => /*t(*/'Tarabulus (Tripoli)'/*)*/,
      'tag' => '0'
    ),
    'LY-TU' => array('country' => 'LY', 'title' => /*t(*/'Tubruq'/*)*/, 'tag' => '0'),
    'LY-YA' => array('country' => 'LY', 'title' => /*t(*/'Yafran'/*)*/, 'tag' => '0'),
    'LY-ZA' => array('country' => 'LY', 'title' => /*t(*/'Az Zawiyah'/*)*/, 'tag' => '0'),
    'LY-ZL' => array('country' => 'LY', 'title' => /*t(*/'Zlitan'/*)*/, 'tag' => '0'),
    'MA-ADK' => array('country' => 'MA', 'title' => /*t(*/'Ad Dakhla'/*)*/, 'tag' => '0'),
    'MA-AGD' => array('country' => 'MA', 'title' => /*t(*/'Agadir'/*)*/, 'tag' => '0'),
    'MA-AZI' => array('country' => 'MA', 'title' => /*t(*/'Azilal'/*)*/, 'tag' => '0'),
    'MA-BJD' => array('country' => 'MA', 'title' => /*t(*/'Boujdour'/*)*/, 'tag' => '0'),
    'MA-BLM' => array('country' => 'MA', 'title' => /*t(*/'Boulemane'/*)*/, 'tag' => '0'),
    'MA-BME' => array(
      'country' => 'MA',
      'title' => /*t(*/'Beni Mellal'/*)*/,
      'tag' => '0'
    ),
    'MA-BSL' => array(
      'country' => 'MA',
      'title' => /*t(*/'Ben Slimane'/*)*/,
      'tag' => '0'
    ),
    'MA-CBL' => array('country' => 'MA', 'title' => /*t(*/'Casablanca'/*)*/, 'tag' => '0'),
    'MA-CHA' => array('country' => 'MA', 'title' => /*t(*/'Chaouen'/*)*/, 'tag' => '0'),
    'MA-EJA' => array('country' => 'MA', 'title' => /*t(*/'El Jadida'/*)*/, 'tag' => '0'),
    'MA-EKS' => array(
      'country' => 'MA',
      'title' => /*t(*/'El Kelaa des Sraghna'/*)*/,
      'tag' => '0'
    ),
    'MA-ERA' => array(
      'country' => 'MA',
      'title' => /*t(*/'Er Rachidia'/*)*/,
      'tag' => '0'
    ),
    'MA-ESM' => array('country' => 'MA', 'title' => /*t(*/'Es Smara'/*)*/, 'tag' => '0'),
    'MA-ESS' => array('country' => 'MA', 'title' => /*t(*/'Essaouira'/*)*/, 'tag' => '0'),
    'MA-FES' => array('country' => 'MA', 'title' => /*t(*/'Fes'/*)*/, 'tag' => '0'),
    'MA-FIG' => array('country' => 'MA', 'title' => /*t(*/'Figuig'/*)*/, 'tag' => '0'),
    'MA-GLM' => array('country' => 'MA', 'title' => /*t(*/'Guelmim'/*)*/, 'tag' => '0'),
    'MA-HOC' => array('country' => 'MA', 'title' => /*t(*/'Al Hoceima'/*)*/, 'tag' => '0'),
    'MA-IFR' => array('country' => 'MA', 'title' => /*t(*/'Ifrane'/*)*/, 'tag' => '0'),
    'MA-KEN' => array('country' => 'MA', 'title' => /*t(*/'Kenitra'/*)*/, 'tag' => '0'),
    'MA-KHM' => array('country' => 'MA', 'title' => /*t(*/'Khemisset'/*)*/, 'tag' => '0'),
    'MA-KHN' => array('country' => 'MA', 'title' => /*t(*/'Khenifra'/*)*/, 'tag' => '0'),
    'MA-KHO' => array('country' => 'MA', 'title' => /*t(*/'Khouribga'/*)*/, 'tag' => '0'),
    'MA-LAR' => array('country' => 'MA', 'title' => /*t(*/'Larache'/*)*/, 'tag' => '0'),
    'MA-LYN' => array('country' => 'MA', 'title' => /*t(*/'Laayoune'/*)*/, 'tag' => '0'),
    'MA-MKN' => array('country' => 'MA', 'title' => /*t(*/'Meknes'/*)*/, 'tag' => '0'),
    'MA-MRK' => array('country' => 'MA', 'title' => /*t(*/'Marrakech'/*)*/, 'tag' => '0'),
    'MA-NAD' => array('country' => 'MA', 'title' => /*t(*/'Nador'/*)*/, 'tag' => '0'),
    'MA-ORZ' => array('country' => 'MA', 'title' => /*t(*/'Ouarzazate'/*)*/, 'tag' => '0'),
    'MA-OUJ' => array('country' => 'MA', 'title' => /*t(*/'Oujda'/*)*/, 'tag' => '0'),
    'MA-RSA' => array('country' => 'MA', 'title' => /*t(*/'Rabat-Sale'/*)*/, 'tag' => '0'),
    'MA-SAF' => array('country' => 'MA', 'title' => /*t(*/'Safi'/*)*/, 'tag' => '0'),
    'MA-SET' => array('country' => 'MA', 'title' => /*t(*/'Settat'/*)*/, 'tag' => '0'),
    'MA-SKA' => array('country' => 'MA', 'title' => /*t(*/'Sidi Kacem'/*)*/, 'tag' => '0'),
    'MA-TAN' => array('country' => 'MA', 'title' => /*t(*/'Tan-Tan'/*)*/, 'tag' => '0'),
    'MA-TAO' => array('country' => 'MA', 'title' => /*t(*/'Taounate'/*)*/, 'tag' => '0'),
    'MA-TAT' => array('country' => 'MA', 'title' => /*t(*/'Tata'/*)*/, 'tag' => '0'),
    'MA-TAZ' => array('country' => 'MA', 'title' => /*t(*/'Taza'/*)*/, 'tag' => '0'),
    'MA-TET' => array('country' => 'MA', 'title' => /*t(*/'Tetouan'/*)*/, 'tag' => '0'),
    'MA-TGR' => array('country' => 'MA', 'title' => /*t(*/'Tangier'/*)*/, 'tag' => '0'),
    'MA-TIZ' => array('country' => 'MA', 'title' => /*t(*/'Tiznit'/*)*/, 'tag' => '0'),
    'MA-TRD' => array('country' => 'MA', 'title' => /*t(*/'Taroudannt'/*)*/, 'tag' => '0'),
    'MC-FV' => array('country' => 'MC', 'title' => /*t(*/'Fontvieille'/*)*/, 'tag' => '0'),
    'MC-LC' => array(
      'country' => 'MC',
      'title' => /*t(*/'La Condamine'/*)*/,
      'tag' => '0'
    ),
    'MC-MC' => array('country' => 'MC', 'title' => /*t(*/'Monte-Carlo'/*)*/, 'tag' => '0'),
    'MC-MV' => array(
      'country' => 'MC',
      'title' => /*t(*/'Monaco-Ville'/*)*/,
      'tag' => '0'
    ),
    'MD-BA' => array('country' => 'MD', 'title' => /*t(*/'Balti'/*)*/, 'tag' => '0'),
    'MD-CA' => array('country' => 'MD', 'title' => /*t(*/'Cahul'/*)*/, 'tag' => '0'),
    'MD-CU' => array('country' => 'MD', 'title' => /*t(*/'Chisinau'/*)*/, 'tag' => '0'),
    'MD-ED' => array('country' => 'MD', 'title' => /*t(*/'Edinet'/*)*/, 'tag' => '0'),
    'MD-GA' => array('country' => 'MD', 'title' => /*t(*/'Gagauzia'/*)*/, 'tag' => '0'),
    'MD-LA' => array('country' => 'MD', 'title' => /*t(*/'Lapusna'/*)*/, 'tag' => '0'),
    'MD-OR' => array('country' => 'MD', 'title' => /*t(*/'Orhei'/*)*/, 'tag' => '0'),
    'MD-SN' => array(
      'country' => 'MD',
      'title' => /*t(*/'Stinga Nistrului'/*)*/,
      'tag' => '0'
    ),
    'MD-SO' => array('country' => 'MD', 'title' => /*t(*/'Soroca'/*)*/, 'tag' => '0'),
    'MD-TI' => array('country' => 'MD', 'title' => /*t(*/'Tighina'/*)*/, 'tag' => '0'),
    'MD-UN' => array('country' => 'MD', 'title' => /*t(*/'Ungheni'/*)*/, 'tag' => '0'),
    'MG-AN' => array(
      'country' => 'MG',
      'title' => /*t(*/'Antananarivo province'/*)*/,
      'tag' => '0'
    ),
    'MG-AS' => array(
      'country' => 'MG',
      'title' => /*t(*/'Antsiranana province'/*)*/,
      'tag' => '0'
    ),
    'MG-FN' => array(
      'country' => 'MG',
      'title' => /*t(*/'Fianarantsoa province'/*)*/,
      'tag' => '0'
    ),
    'MG-MJ' => array(
      'country' => 'MG',
      'title' => /*t(*/'Mahajanga province'/*)*/,
      'tag' => '0'
    ),
    'MG-TL' => array(
      'country' => 'MG',
      'title' => /*t(*/'Toliara province'/*)*/,
      'tag' => '0'
    ),
    'MG-TM' => array(
      'country' => 'MG',
      'title' => /*t(*/'Toamasina province'/*)*/,
      'tag' => '0'
    ),
    'MH-ALG' => array('country' => 'MH', 'title' => /*t(*/'Ailinginae'/*)*/, 'tag' => '0'),
    'MH-ALK' => array('country' => 'MH', 'title' => /*t(*/'Ailuk'/*)*/, 'tag' => '0'),
    'MH-ALL' => array(
      'country' => 'MH',
      'title' => /*t(*/'Ailinglaplap'/*)*/,
      'tag' => '0'
    ),
    'MH-ARN' => array('country' => 'MH', 'title' => /*t(*/'Arno'/*)*/, 'tag' => '0'),
    'MH-AUR' => array('country' => 'MH', 'title' => /*t(*/'Aur'/*)*/, 'tag' => '0'),
    'MH-BKK' => array('country' => 'MH', 'title' => /*t(*/'Bokak'/*)*/, 'tag' => '0'),
    'MH-BKN' => array('country' => 'MH', 'title' => /*t(*/'Bikini'/*)*/, 'tag' => '0'),
    'MH-BKR' => array('country' => 'MH', 'title' => /*t(*/'Bikar'/*)*/, 'tag' => '0'),
    'MH-EBN' => array('country' => 'MH', 'title' => /*t(*/'Ebon'/*)*/, 'tag' => '0'),
    'MH-EKB' => array('country' => 'MH', 'title' => /*t(*/'Erikub'/*)*/, 'tag' => '0'),
    'MH-ENT' => array('country' => 'MH', 'title' => /*t(*/'Enewetak'/*)*/, 'tag' => '0'),
    'MH-JBT' => array('country' => 'MH', 'title' => /*t(*/'Jabat'/*)*/, 'tag' => '0'),
    'MH-JEM' => array('country' => 'MH', 'title' => /*t(*/'Jemo'/*)*/, 'tag' => '0'),
    'MH-JLT' => array('country' => 'MH', 'title' => /*t(*/'Jaluit'/*)*/, 'tag' => '0'),
    'MH-KIL' => array('country' => 'MH', 'title' => /*t(*/'Kili'/*)*/, 'tag' => '0'),
    'MH-KWJ' => array('country' => 'MH', 'title' => /*t(*/'Kwajalein'/*)*/, 'tag' => '0'),
    'MH-LAE' => array('country' => 'MH', 'title' => /*t(*/'Lae'/*)*/, 'tag' => '0'),
    'MH-LIB' => array('country' => 'MH', 'title' => /*t(*/'Lib'/*)*/, 'tag' => '0'),
    'MH-LKP' => array('country' => 'MH', 'title' => /*t(*/'Likiep'/*)*/, 'tag' => '0'),
    'MH-MIL' => array('country' => 'MH', 'title' => /*t(*/'Mili'/*)*/, 'tag' => '0'),
    'MH-MJR' => array('country' => 'MH', 'title' => /*t(*/'Majuro'/*)*/, 'tag' => '0'),
    'MH-MJT' => array('country' => 'MH', 'title' => /*t(*/'Mejit'/*)*/, 'tag' => '0'),
    'MH-MLP' => array('country' => 'MH', 'title' => /*t(*/'Maloelap'/*)*/, 'tag' => '0'),
    'MH-NAM' => array('country' => 'MH', 'title' => /*t(*/'Namu'/*)*/, 'tag' => '0'),
    'MH-NMK' => array('country' => 'MH', 'title' => /*t(*/'Namorik'/*)*/, 'tag' => '0'),
    'MH-RGK' => array('country' => 'MH', 'title' => /*t(*/'Rongrik'/*)*/, 'tag' => '0'),
    'MH-RGL' => array('country' => 'MH', 'title' => /*t(*/'Rongelap'/*)*/, 'tag' => '0'),
    'MH-TOK' => array('country' => 'MH', 'title' => /*t(*/'Toke'/*)*/, 'tag' => '0'),
    'MH-UJA' => array('country' => 'MH', 'title' => /*t(*/'Ujae'/*)*/, 'tag' => '0'),
    'MH-UJL' => array('country' => 'MH', 'title' => /*t(*/'Ujelang'/*)*/, 'tag' => '0'),
    'MH-UTK' => array('country' => 'MH', 'title' => /*t(*/'Utirik'/*)*/, 'tag' => '0'),
    'MH-WTH' => array('country' => 'MH', 'title' => /*t(*/'Wotho'/*)*/, 'tag' => '0'),
    'MH-WTJ' => array('country' => 'MH', 'title' => /*t(*/'Wotje'/*)*/, 'tag' => '0'),
    'ML-CD' => array(
      'country' => 'ML',
      'title' => /*t(*/'Bamako Capital District'/*)*/,
      'tag' => '0'
    ),
    'ML-GA' => array('country' => 'ML', 'title' => /*t(*/'Gao'/*)*/, 'tag' => '0'),
    'ML-KD' => array('country' => 'ML', 'title' => /*t(*/'Kidal'/*)*/, 'tag' => '0'),
    'ML-KL' => array('country' => 'ML', 'title' => /*t(*/'Koulikoro'/*)*/, 'tag' => '0'),
    'ML-KY' => array('country' => 'ML', 'title' => /*t(*/'Kayes'/*)*/, 'tag' => '0'),
    'ML-MP' => array('country' => 'ML', 'title' => /*t(*/'Mopti'/*)*/, 'tag' => '0'),
    'ML-SG' => array('country' => 'ML', 'title' => /*t(*/'Segou'/*)*/, 'tag' => '0'),
    'ML-SK' => array('country' => 'ML', 'title' => /*t(*/'Sikasso'/*)*/, 'tag' => '0'),
    'ML-TB' => array('country' => 'ML', 'title' => /*t(*/'Tombouctou'/*)*/, 'tag' => '0'),
    'MM-AY' => array('country' => 'MM', 'title' => /*t(*/'Ayeyarwady'/*)*/, 'tag' => '0'),
    'MM-BG' => array('country' => 'MM', 'title' => /*t(*/'Bago'/*)*/, 'tag' => '0'),
    'MM-CH' => array('country' => 'MM', 'title' => /*t(*/'Chin State'/*)*/, 'tag' => '0'),
    'MM-KC' => array(
      'country' => 'MM',
      'title' => /*t(*/'Kachin State'/*)*/,
      'tag' => '0'
    ),
    'MM-KH' => array('country' => 'MM', 'title' => /*t(*/'Kayah State'/*)*/, 'tag' => '0'),
    'MM-KN' => array('country' => 'MM', 'title' => /*t(*/'Kayin State'/*)*/, 'tag' => '0'),
    'MM-MD' => array('country' => 'MM', 'title' => /*t(*/'Mandalay'/*)*/, 'tag' => '0'),
    'MM-MG' => array('country' => 'MM', 'title' => /*t(*/'Magway'/*)*/, 'tag' => '0'),
    'MM-MN' => array('country' => 'MM', 'title' => /*t(*/'Mon State'/*)*/, 'tag' => '0'),
    'MM-RK' => array(
      'country' => 'MM',
      'title' => /*t(*/'Rakhine State'/*)*/,
      'tag' => '0'
    ),
    'MM-SG' => array('country' => 'MM', 'title' => /*t(*/'Sagaing'/*)*/, 'tag' => '0'),
    'MM-SH' => array('country' => 'MM', 'title' => /*t(*/'Shan State'/*)*/, 'tag' => '0'),
    'MM-TN' => array('country' => 'MM', 'title' => /*t(*/'Tanintharyi'/*)*/, 'tag' => '0'),
    'MM-YG' => array('country' => 'MM', 'title' => /*t(*/'Yangon'/*)*/, 'tag' => '0'),
    'MN-035' => array('country' => 'MN', 'title' => /*t(*/'Orhon'/*)*/, 'tag' => '0'),
    'MN-037' => array('country' => 'MN', 'title' => /*t(*/'Darhan uul'/*)*/, 'tag' => '0'),
    'MN-039' => array('country' => 'MN', 'title' => /*t(*/'Hentiy'/*)*/, 'tag' => '0'),
    'MN-041' => array('country' => 'MN', 'title' => /*t(*/'Hovsgol'/*)*/, 'tag' => '0'),
    'MN-043' => array('country' => 'MN', 'title' => /*t(*/'Hovd'/*)*/, 'tag' => '0'),
    'MN-046' => array('country' => 'MN', 'title' => /*t(*/'Uvs'/*)*/, 'tag' => '0'),
    'MN-047' => array('country' => 'MN', 'title' => /*t(*/'Tov'/*)*/, 'tag' => '0'),
    'MN-049' => array('country' => 'MN', 'title' => /*t(*/'Selenge'/*)*/, 'tag' => '0'),
    'MN-051' => array('country' => 'MN', 'title' => /*t(*/'Suhbaatar'/*)*/, 'tag' => '0'),
    'MN-053' => array('country' => 'MN', 'title' => /*t(*/'Omnogovi'/*)*/, 'tag' => '0'),
    'MN-055' => array('country' => 'MN', 'title' => /*t(*/'Ovorhangay'/*)*/, 'tag' => '0'),
    'MN-057' => array('country' => 'MN', 'title' => /*t(*/'Dzavhan'/*)*/, 'tag' => '0'),
    'MN-059' => array('country' => 'MN', 'title' => /*t(*/'DundgovL'/*)*/, 'tag' => '0'),
    'MN-061' => array('country' => 'MN', 'title' => /*t(*/'Dornod'/*)*/, 'tag' => '0'),
    'MN-063' => array('country' => 'MN', 'title' => /*t(*/'Dornogov'/*)*/, 'tag' => '0'),
    'MN-064' => array(
      'country' => 'MN',
      'title' => /*t(*/'Govi-Sumber'/*)*/,
      'tag' => '0'
    ),
    'MN-065' => array('country' => 'MN', 'title' => /*t(*/'Govi-Altay'/*)*/, 'tag' => '0'),
    'MN-067' => array('country' => 'MN', 'title' => /*t(*/'Bulgan'/*)*/, 'tag' => '0'),
    'MN-069' => array(
      'country' => 'MN',
      'title' => /*t(*/'Bayanhongor'/*)*/,
      'tag' => '0'
    ),
    'MN-071' => array(
      'country' => 'MN',
      'title' => /*t(*/'Bayan-Olgiy'/*)*/,
      'tag' => '0'
    ),
    'MN-073' => array('country' => 'MN', 'title' => /*t(*/'Arhangay'/*)*/, 'tag' => '0'),
    'MN-1' => array('country' => 'MN', 'title' => /*t(*/'Ulanbaatar'/*)*/, 'tag' => '0'),
    'MO-ANT' => array(
      'country' => 'MO',
      'title' => /*t(*/'St. Anthony Parish'/*)*/,
      'tag' => '0'
    ),
    'MO-CAT' => array(
      'country' => 'MO',
      'title' => /*t(*/'Cathedral Parish'/*)*/,
      'tag' => '0'
    ),
    'MO-LAW' => array(
      'country' => 'MO',
      'title' => /*t(*/'St. Lawrence Parish'/*)*/,
      'tag' => '0'
    ),
    'MO-LAZ' => array(
      'country' => 'MO',
      'title' => /*t(*/'St. Lazarus Parish'/*)*/,
      'tag' => '0'
    ),
    'MO-OLF' => array(
      'country' => 'MO',
      'title' => /*t(*/'Our Lady Fatima Parish'/*)*/,
      'tag' => '0'
    ),
    'MP-N' => array(
      'country' => 'MP',
      'title' => /*t(*/'Northern Islands'/*)*/,
      'tag' => '0'
    ),
    'MP-R' => array('country' => 'MP', 'title' => /*t(*/'Rota'/*)*/, 'tag' => '0'),
    'MP-S' => array('country' => 'MP', 'title' => /*t(*/'Saipan'/*)*/, 'tag' => '0'),
    'MP-T' => array('country' => 'MP', 'title' => /*t(*/'Tinian'/*)*/, 'tag' => '0'),
    'MR-AD' => array('country' => 'MR', 'title' => /*t(*/'Adrar'/*)*/, 'tag' => '0'),
    'MR-AS' => array('country' => 'MR', 'title' => /*t(*/'Assaba'/*)*/, 'tag' => '0'),
    'MR-BR' => array('country' => 'MR', 'title' => /*t(*/'Brakna'/*)*/, 'tag' => '0'),
    'MR-DN' => array(
      'country' => 'MR',
      'title' => /*t(*/'Dakhlet Nouadhibou'/*)*/,
      'tag' => '0'
    ),
    'MR-GM' => array('country' => 'MR', 'title' => /*t(*/'Guidimaka'/*)*/, 'tag' => '0'),
    'MR-GO' => array('country' => 'MR', 'title' => /*t(*/'Gorgol'/*)*/, 'tag' => '0'),
    'MR-HC' => array(
      'country' => 'MR',
      'title' => /*t(*/'Hodh Ech Chargui'/*)*/,
      'tag' => '0'
    ),
    'MR-HG' => array(
      'country' => 'MR',
      'title' => /*t(*/'Hodh El Gharbi'/*)*/,
      'tag' => '0'
    ),
    'MR-IN' => array('country' => 'MR', 'title' => /*t(*/'Inchiri'/*)*/, 'tag' => '0'),
    'MR-NO' => array('country' => 'MR', 'title' => /*t(*/'Nouakchott'/*)*/, 'tag' => '0'),
    'MR-TA' => array('country' => 'MR', 'title' => /*t(*/'Tagant'/*)*/, 'tag' => '0'),
    'MR-TR' => array('country' => 'MR', 'title' => /*t(*/'Trarza'/*)*/, 'tag' => '0'),
    'MR-TZ' => array(
      'country' => 'MR',
      'title' => /*t(*/'Tiris Zemmour'/*)*/,
      'tag' => '0'
    ),
    'MS-A' => array(
      'country' => 'MS',
      'title' => /*t(*/'Saint Anthony'/*)*/,
      'tag' => '0'
    ),
    'MS-G' => array(
      'country' => 'MS',
      'title' => /*t(*/'Saint Georges'/*)*/,
      'tag' => '0'
    ),
    'MS-P' => array('country' => 'MS', 'title' => /*t(*/'Saint Peter'/*)*/, 'tag' => '0'),
    'MT-ATT' => array('country' => 'MT', 'title' => /*t(*/'Attard'/*)*/, 'tag' => '0'),
    'MT-BAL' => array('country' => 'MT', 'title' => /*t(*/'Balzan'/*)*/, 'tag' => '0'),
    'MT-BGU' => array('country' => 'MT', 'title' => /*t(*/'Birgu'/*)*/, 'tag' => '0'),
    'MT-BKK' => array('country' => 'MT', 'title' => /*t(*/'Birkirkara'/*)*/, 'tag' => '0'),
    'MT-BOR' => array('country' => 'MT', 'title' => /*t(*/'Bormla'/*)*/, 'tag' => '0'),
    'MT-BRZ' => array('country' => 'MT', 'title' => /*t(*/'Birzebbuga'/*)*/, 'tag' => '0'),
    'MT-DIN' => array('country' => 'MT', 'title' => /*t(*/'Dingli'/*)*/, 'tag' => '0'),
    'MT-FGU' => array('country' => 'MT', 'title' => /*t(*/'Fgura'/*)*/, 'tag' => '0'),
    'MT-FLO' => array('country' => 'MT', 'title' => /*t(*/'Floriana'/*)*/, 'tag' => '0'),
    'MT-FNT' => array('country' => 'MT', 'title' => /*t(*/'Fontana'/*)*/, 'tag' => '0'),
    'MT-GDJ' => array('country' => 'MT', 'title' => /*t(*/'Gudja'/*)*/, 'tag' => '0'),
    'MT-GHJ' => array(
      'country' => 'MT',
      'title' => /*t(*/'Ghajnsielem'/*)*/,
      'tag' => '0'
    ),
    'MT-GHR' => array('country' => 'MT', 'title' => /*t(*/'Gharb'/*)*/, 'tag' => '0'),
    'MT-GHS' => array('country' => 'MT', 'title' => /*t(*/'Ghasri'/*)*/, 'tag' => '0'),
    'MT-GRG' => array('country' => 'MT', 'title' => /*t(*/'Gargur'/*)*/, 'tag' => '0'),
    'MT-GXQ' => array('country' => 'MT', 'title' => /*t(*/'Gaxaq'/*)*/, 'tag' => '0'),
    'MT-GZR' => array('country' => 'MT', 'title' => /*t(*/'Gzira'/*)*/, 'tag' => '0'),
    'MT-HMR' => array('country' => 'MT', 'title' => /*t(*/'Hamrun'/*)*/, 'tag' => '0'),
    'MT-IKL' => array('country' => 'MT', 'title' => /*t(*/'Iklin'/*)*/, 'tag' => '0'),
    'MT-ISL' => array('country' => 'MT', 'title' => /*t(*/'Isla'/*)*/, 'tag' => '0'),
    'MT-KLK' => array('country' => 'MT', 'title' => /*t(*/'Kalkara'/*)*/, 'tag' => '0'),
    'MT-KRC' => array('country' => 'MT', 'title' => /*t(*/'Kercem'/*)*/, 'tag' => '0'),
    'MT-KRK' => array('country' => 'MT', 'title' => /*t(*/'Kirkop'/*)*/, 'tag' => '0'),
    'MT-LIJ' => array('country' => 'MT', 'title' => /*t(*/'Lija'/*)*/, 'tag' => '0'),
    'MT-LUQ' => array('country' => 'MT', 'title' => /*t(*/'Luqa'/*)*/, 'tag' => '0'),
    'MT-MDN' => array('country' => 'MT', 'title' => /*t(*/'Mdina'/*)*/, 'tag' => '0'),
    'MT-MEL' => array('country' => 'MT', 'title' => /*t(*/'Melliea'/*)*/, 'tag' => '0'),
    'MT-MGR' => array('country' => 'MT', 'title' => /*t(*/'Mgarr'/*)*/, 'tag' => '0'),
    'MT-MKL' => array('country' => 'MT', 'title' => /*t(*/'Marsaskala'/*)*/, 'tag' => '0'),
    'MT-MQA' => array('country' => 'MT', 'title' => /*t(*/'Mqabba'/*)*/, 'tag' => '0'),
    'MT-MRS' => array('country' => 'MT', 'title' => /*t(*/'Marsa'/*)*/, 'tag' => '0'),
    'MT-MSI' => array('country' => 'MT', 'title' => /*t(*/'Msida'/*)*/, 'tag' => '0'),
    'MT-MST' => array('country' => 'MT', 'title' => /*t(*/'Mosta'/*)*/, 'tag' => '0'),
    'MT-MTF' => array('country' => 'MT', 'title' => /*t(*/'Mtarfa'/*)*/, 'tag' => '0'),
    'MT-MUN' => array('country' => 'MT', 'title' => /*t(*/'Munxar'/*)*/, 'tag' => '0'),
    'MT-MXL' => array('country' => 'MT', 'title' => /*t(*/'Marsaxlokk'/*)*/, 'tag' => '0'),
    'MT-NAD' => array('country' => 'MT', 'title' => /*t(*/'Nadur'/*)*/, 'tag' => '0'),
    'MT-NAX' => array('country' => 'MT', 'title' => /*t(*/'Naxxar'/*)*/, 'tag' => '0'),
    'MT-PAO' => array('country' => 'MT', 'title' => /*t(*/'Paola'/*)*/, 'tag' => '0'),
    'MT-PEM' => array('country' => 'MT', 'title' => /*t(*/'Pembroke'/*)*/, 'tag' => '0'),
    'MT-PIE' => array('country' => 'MT', 'title' => /*t(*/'Pieta'/*)*/, 'tag' => '0'),
    'MT-QAL' => array('country' => 'MT', 'title' => /*t(*/'Qala'/*)*/, 'tag' => '0'),
    'MT-QOR' => array('country' => 'MT', 'title' => /*t(*/'Qormi'/*)*/, 'tag' => '0'),
    'MT-QRE' => array('country' => 'MT', 'title' => /*t(*/'Qrendi'/*)*/, 'tag' => '0'),
    'MT-RAB' => array('country' => 'MT', 'title' => /*t(*/'Rabat'/*)*/, 'tag' => '0'),
    'MT-SAF' => array('country' => 'MT', 'title' => /*t(*/'Safi'/*)*/, 'tag' => '0'),
    'MT-SGI' => array('country' => 'MT', 'title' => /*t(*/'San Giljan'/*)*/, 'tag' => '0'),
    'MT-SGW' => array('country' => 'MT', 'title' => /*t(*/'San Gwann'/*)*/, 'tag' => '0'),
    'MT-SIG' => array('country' => 'MT', 'title' => /*t(*/'Siggiewi'/*)*/, 'tag' => '0'),
    'MT-SLA' => array(
      'country' => 'MT',
      'title' => /*t(*/'San Lawrenz'/*)*/,
      'tag' => '0'
    ),
    'MT-SLM' => array('country' => 'MT', 'title' => /*t(*/'Sliema'/*)*/, 'tag' => '0'),
    'MT-SLU' => array(
      'country' => 'MT',
      'title' => /*t(*/'Santa Lucija'/*)*/,
      'tag' => '0'
    ),
    'MT-SNT' => array('country' => 'MT', 'title' => /*t(*/'Sannat'/*)*/, 'tag' => '0'),
    'MT-SPB' => array(
      'country' => 'MT',
      'title' => /*t(*/'San Pawl il-Bahar'/*)*/,
      'tag' => '0'
    ),
    'MT-SVE' => array(
      'country' => 'MT',
      'title' => /*t(*/'Santa Venera'/*)*/,
      'tag' => '0'
    ),
    'MT-SWQ' => array('country' => 'MT', 'title' => /*t(*/'Swieqi'/*)*/, 'tag' => '0'),
    'MT-TRX' => array('country' => 'MT', 'title' => /*t(*/'Tarxien'/*)*/, 'tag' => '0'),
    'MT-TXB' => array('country' => 'MT', 'title' => /*t(*/'Ta Xbiex'/*)*/, 'tag' => '0'),
    'MT-VIC' => array('country' => 'MT', 'title' => /*t(*/'Victoria'/*)*/, 'tag' => '0'),
    'MT-VLT' => array('country' => 'MT', 'title' => /*t(*/'Valletta'/*)*/, 'tag' => '0'),
    'MT-XEW' => array('country' => 'MT', 'title' => /*t(*/'Xewkija'/*)*/, 'tag' => '0'),
    'MT-XGJ' => array('country' => 'MT', 'title' => /*t(*/'Xgajra'/*)*/, 'tag' => '0'),
    'MT-ZAG' => array('country' => 'MT', 'title' => /*t(*/'Xagra'/*)*/, 'tag' => '0'),
    'MT-ZBG' => array('country' => 'MT', 'title' => /*t(*/'Zebbug'/*)*/, 'tag' => '0'),
    'MT-ZBR' => array('country' => 'MT', 'title' => /*t(*/'Zabbar'/*)*/, 'tag' => '0'),
    'MT-ZEB' => array('country' => 'MT', 'title' => /*t(*/'Zebbug'/*)*/, 'tag' => '0'),
    'MT-ZJT' => array('country' => 'MT', 'title' => /*t(*/'Zejtun'/*)*/, 'tag' => '0'),
    'MT-ZRQ' => array('country' => 'MT', 'title' => /*t(*/'Zurrieq'/*)*/, 'tag' => '0'),
    'MU-AG' => array(
      'country' => 'MU',
      'title' => /*t(*/'Agalega Islands'/*)*/,
      'tag' => '0'
    ),
    'MU-BL' => array('country' => 'MU', 'title' => /*t(*/'Black River'/*)*/, 'tag' => '0'),
    'MU-BR' => array(
      'country' => 'MU',
      'title' => /*t(*/'Beau Bassin-Rose Hill'/*)*/,
      'tag' => '0'
    ),
    'MU-CC' => array(
      'country' => 'MU',
      'title' => /*t(*/'Cargados Carajos Shoals (Saint Brandon Islands)'/*)*/,
      'tag' => '0'
    ),
    'MU-CU' => array('country' => 'MU', 'title' => /*t(*/'Curepipe'/*)*/, 'tag' => '0'),
    'MU-FL' => array('country' => 'MU', 'title' => /*t(*/'Flacq'/*)*/, 'tag' => '0'),
    'MU-GP' => array('country' => 'MU', 'title' => /*t(*/'Grand Port'/*)*/, 'tag' => '0'),
    'MU-MO' => array('country' => 'MU', 'title' => /*t(*/'Moka'/*)*/, 'tag' => '0'),
    'MU-PA' => array(
      'country' => 'MU',
      'title' => /*t(*/'Pamplemousses'/*)*/,
      'tag' => '0'
    ),
    'MU-PL' => array('country' => 'MU', 'title' => /*t(*/'Port Louis'/*)*/, 'tag' => '0'),
    'MU-PU' => array('country' => 'MU', 'title' => /*t(*/'Port Louis'/*)*/, 'tag' => '0'),
    'MU-PW' => array(
      'country' => 'MU',
      'title' => /*t(*/'Plaines Wilhems'/*)*/,
      'tag' => '0'
    ),
    'MU-QB' => array(
      'country' => 'MU',
      'title' => /*t(*/'Quatre Bornes'/*)*/,
      'tag' => '0'
    ),
    'MU-RO' => array('country' => 'MU', 'title' => /*t(*/'Rodrigues'/*)*/, 'tag' => '0'),
    'MU-RR' => array(
      'country' => 'MU',
      'title' => /*t(*/'Riviere du Rempart'/*)*/,
      'tag' => '0'
    ),
    'MU-SA' => array('country' => 'MU', 'title' => /*t(*/'Savanne'/*)*/, 'tag' => '0'),
    'MU-VP' => array(
      'country' => 'MU',
      'title' => /*t(*/'Vacoas-Phoenix'/*)*/,
      'tag' => '0'
    ),
    'MV-AAD' => array(
      'country' => 'MV',
      'title' => /*t(*/'Ari Atoll Dheknu'/*)*/,
      'tag' => '0'
    ),
    'MV-AAU' => array(
      'country' => 'MV',
      'title' => /*t(*/'Ari Atoll Uthuru'/*)*/,
      'tag' => '0'
    ),
    'MV-ADD' => array('country' => 'MV', 'title' => /*t(*/'Addu'/*)*/, 'tag' => '0'),
    'MV-FAA' => array(
      'country' => 'MV',
      'title' => /*t(*/'Faadhippolhu'/*)*/,
      'tag' => '0'
    ),
    'MV-FEA' => array(
      'country' => 'MV',
      'title' => /*t(*/'Felidhe Atoll'/*)*/,
      'tag' => '0'
    ),
    'MV-FMU' => array('country' => 'MV', 'title' => /*t(*/'Fua Mulaku'/*)*/, 'tag' => '0'),
    'MV-HAD' => array(
      'country' => 'MV',
      'title' => /*t(*/'Huvadhu Atoll Dhekunu'/*)*/,
      'tag' => '0'
    ),
    'MV-HAU' => array(
      'country' => 'MV',
      'title' => /*t(*/'Huvadhu Atoll Uthuru'/*)*/,
      'tag' => '0'
    ),
    'MV-HDH' => array(
      'country' => 'MV',
      'title' => /*t(*/'Hadhdhunmathi'/*)*/,
      'tag' => '0'
    ),
    'MV-KLH' => array(
      'country' => 'MV',
      'title' => /*t(*/'Kolhumadulu'/*)*/,
      'tag' => '0'
    ),
    'MV-MAA' => array('country' => 'MV', 'title' => /*t(*/'Male Atoll'/*)*/, 'tag' => '0'),
    'MV-MAD' => array(
      'country' => 'MV',
      'title' => /*t(*/'Maalhosmadulu Dhekunu'/*)*/,
      'tag' => '0'
    ),
    'MV-MAU' => array(
      'country' => 'MV',
      'title' => /*t(*/'Maalhosmadulu Uthuru'/*)*/,
      'tag' => '0'
    ),
    'MV-MLD' => array(
      'country' => 'MV',
      'title' => /*t(*/'Miladhunmadulu Dhekunu'/*)*/,
      'tag' => '0'
    ),
    'MV-MLU' => array(
      'country' => 'MV',
      'title' => /*t(*/'Miladhunmadulu Uthuru'/*)*/,
      'tag' => '0'
    ),
    'MV-MUA' => array(
      'country' => 'MV',
      'title' => /*t(*/'Mulaku Atoll'/*)*/,
      'tag' => '0'
    ),
    'MV-NAD' => array(
      'country' => 'MV',
      'title' => /*t(*/'Nilandhe Atoll Dhekunu'/*)*/,
      'tag' => '0'
    ),
    'MV-NAU' => array(
      'country' => 'MV',
      'title' => /*t(*/'Nilandhe Atoll Uthuru'/*)*/,
      'tag' => '0'
    ),
    'MV-THD' => array(
      'country' => 'MV',
      'title' => /*t(*/'Thiladhunmathi Dhekunu'/*)*/,
      'tag' => '0'
    ),
    'MV-THU' => array(
      'country' => 'MV',
      'title' => /*t(*/'Thiladhunmathi Uthuru'/*)*/,
      'tag' => '0'
    ),
    'MW-BLK' => array('country' => 'MW', 'title' => /*t(*/'Balaka'/*)*/, 'tag' => '0'),
    'MW-BLT' => array('country' => 'MW', 'title' => /*t(*/'Blantyre'/*)*/, 'tag' => '0'),
    'MW-CKW' => array('country' => 'MW', 'title' => /*t(*/'Chikwawa'/*)*/, 'tag' => '0'),
    'MW-CRD' => array('country' => 'MW', 'title' => /*t(*/'Chiradzulu'/*)*/, 'tag' => '0'),
    'MW-CTP' => array('country' => 'MW', 'title' => /*t(*/'Chitipa'/*)*/, 'tag' => '0'),
    'MW-DDZ' => array('country' => 'MW', 'title' => /*t(*/'Dedza'/*)*/, 'tag' => '0'),
    'MW-DWA' => array('country' => 'MW', 'title' => /*t(*/'Dowa'/*)*/, 'tag' => '0'),
    'MW-KRG' => array('country' => 'MW', 'title' => /*t(*/'Karonga'/*)*/, 'tag' => '0'),
    'MW-KSG' => array('country' => 'MW', 'title' => /*t(*/'Kasungu'/*)*/, 'tag' => '0'),
    'MW-LKM' => array('country' => 'MW', 'title' => /*t(*/'Likoma'/*)*/, 'tag' => '0'),
    'MW-LLG' => array('country' => 'MW', 'title' => /*t(*/'Lilongwe'/*)*/, 'tag' => '0'),
    'MW-MCG' => array('country' => 'MW', 'title' => /*t(*/'Machinga'/*)*/, 'tag' => '0'),
    'MW-MCH' => array('country' => 'MW', 'title' => /*t(*/'Mchinji'/*)*/, 'tag' => '0'),
    'MW-MGC' => array('country' => 'MW', 'title' => /*t(*/'Mangochi'/*)*/, 'tag' => '0'),
    'MW-MLJ' => array('country' => 'MW', 'title' => /*t(*/'Mulanje'/*)*/, 'tag' => '0'),
    'MW-MWZ' => array('country' => 'MW', 'title' => /*t(*/'Mwanza'/*)*/, 'tag' => '0'),
    'MW-MZM' => array('country' => 'MW', 'title' => /*t(*/'Mzimba'/*)*/, 'tag' => '0'),
    'MW-NKB' => array('country' => 'MW', 'title' => /*t(*/'Nkhata Bay'/*)*/, 'tag' => '0'),
    'MW-NKH' => array('country' => 'MW', 'title' => /*t(*/'Nkhotakota'/*)*/, 'tag' => '0'),
    'MW-NSJ' => array('country' => 'MW', 'title' => /*t(*/'Nsanje'/*)*/, 'tag' => '0'),
    'MW-NTI' => array('country' => 'MW', 'title' => /*t(*/'Ntchisi'/*)*/, 'tag' => '0'),
    'MW-NTU' => array('country' => 'MW', 'title' => /*t(*/'Ntcheu'/*)*/, 'tag' => '0'),
    'MW-PHL' => array('country' => 'MW', 'title' => /*t(*/'Phalombe'/*)*/, 'tag' => '0'),
    'MW-RMP' => array('country' => 'MW', 'title' => /*t(*/'Rumphi'/*)*/, 'tag' => '0'),
    'MW-SLM' => array('country' => 'MW', 'title' => /*t(*/'Salima'/*)*/, 'tag' => '0'),
    'MW-THY' => array('country' => 'MW', 'title' => /*t(*/'Thyolo'/*)*/, 'tag' => '0'),
    'MW-ZBA' => array('country' => 'MW', 'title' => /*t(*/'Zomba'/*)*/, 'tag' => '0'),
    'MX-AGU' => array(
      'country' => 'MX',
      'title' => /*t(*/'Aguascalientes'/*)*/,
      'tag' => '0'
    ),
    'MX-BCN' => array(
      'country' => 'MX',
      'title' => /*t(*/'Baja California'/*)*/,
      'tag' => '0'
    ),
    'MX-BCS' => array(
      'country' => 'MX',
      'title' => /*t(*/'Baja California Sur'/*)*/,
      'tag' => '0'
    ),
    'MX-CAM' => array('country' => 'MX', 'title' => /*t(*/'Campeche'/*)*/, 'tag' => '0'),
    'MX-CHH' => array('country' => 'MX', 'title' => /*t(*/'Chihuahua'/*)*/, 'tag' => '0'),
    'MX-CHP' => array('country' => 'MX', 'title' => /*t(*/'Chiapas'/*)*/, 'tag' => '0'),
    'MX-COA' => array('country' => 'MX', 'title' => /*t(*/'Coahuila'/*)*/, 'tag' => '0'),
    'MX-COL' => array('country' => 'MX', 'title' => /*t(*/'Colima'/*)*/, 'tag' => '0'),
    'MX-DIF' => array(
      'country' => 'MX',
      'title' => /*t(*/'Distrito Federal'/*)*/,
      'tag' => '0'
    ),
    'MX-DUR' => array('country' => 'MX', 'title' => /*t(*/'Durango'/*)*/, 'tag' => '0'),
    'MX-GRO' => array('country' => 'MX', 'title' => /*t(*/'Guerrero'/*)*/, 'tag' => '0'),
    'MX-GUA' => array('country' => 'MX', 'title' => /*t(*/'Guanajuato'/*)*/, 'tag' => '0'),
    'MX-HID' => array('country' => 'MX', 'title' => /*t(*/'Hidalgo'/*)*/, 'tag' => '0'),
    'MX-JAL' => array('country' => 'MX', 'title' => /*t(*/'Jalisco'/*)*/, 'tag' => '0'),
    'MX-MEX' => array('country' => 'MX', 'title' => /*t(*/'Mexico'/*)*/, 'tag' => '0'),
    'MX-MIC' => array('country' => 'MX', 'title' => /*t(*/'Michoacan'/*)*/, 'tag' => '0'),
    'MX-MOR' => array('country' => 'MX', 'title' => /*t(*/'Morelos'/*)*/, 'tag' => '0'),
    'MX-NAY' => array('country' => 'MX', 'title' => /*t(*/'Nayarit'/*)*/, 'tag' => '0'),
    'MX-NLE' => array('country' => 'MX', 'title' => /*t(*/'Nuevo Leon'/*)*/, 'tag' => '0'),
    'MX-OAX' => array('country' => 'MX', 'title' => /*t(*/'Oaxaca'/*)*/, 'tag' => '0'),
    'MX-PUE' => array('country' => 'MX', 'title' => /*t(*/'Puebla'/*)*/, 'tag' => '0'),
    'MX-QUE' => array('country' => 'MX', 'title' => /*t(*/'Queretaro'/*)*/, 'tag' => '0'),
    'MX-ROO' => array(
      'country' => 'MX',
      'title' => /*t(*/'Quintana Roo'/*)*/,
      'tag' => '0'
    ),
    'MX-SIN' => array('country' => 'MX', 'title' => /*t(*/'Sinaloa'/*)*/, 'tag' => '0'),
    'MX-SLP' => array(
      'country' => 'MX',
      'title' => /*t(*/'San Luis Potosi'/*)*/,
      'tag' => '0'
    ),
    'MX-SON' => array('country' => 'MX', 'title' => /*t(*/'Sonora'/*)*/, 'tag' => '0'),
    'MX-TAB' => array('country' => 'MX', 'title' => /*t(*/'Tabasco'/*)*/, 'tag' => '0'),
    'MX-TAM' => array('country' => 'MX', 'title' => /*t(*/'Tamaulipas'/*)*/, 'tag' => '0'),
    'MX-TLA' => array('country' => 'MX', 'title' => /*t(*/'Tlaxcala'/*)*/, 'tag' => '0'),
    'MX-VER' => array('country' => 'MX', 'title' => /*t(*/'Veracruz'/*)*/, 'tag' => '0'),
    'MX-YUC' => array('country' => 'MX', 'title' => /*t(*/'Yucatan'/*)*/, 'tag' => '0'),
    'MX-ZAC' => array('country' => 'MX', 'title' => /*t(*/'Zacatecas'/*)*/, 'tag' => '0'),
    'MY-JH' => array('country' => 'MY', 'title' => /*t(*/'Johor'/*)*/, 'tag' => '0'),
    'MY-KD' => array('country' => 'MY', 'title' => /*t(*/'Kedah'/*)*/, 'tag' => '0'),
    'MY-KL' => array(
      'country' => 'MY',
      'title' => /*t(*/'Kuala Lumpur'/*)*/,
      'tag' => '0'
    ),
    'MY-KN' => array('country' => 'MY', 'title' => /*t(*/'Kelantan'/*)*/, 'tag' => '0'),
    'MY-LB' => array('country' => 'MY', 'title' => /*t(*/'Labuan'/*)*/, 'tag' => '0'),
    'MY-ML' => array('country' => 'MY', 'title' => /*t(*/'Malacca'/*)*/, 'tag' => '0'),
    'MY-NS' => array(
      'country' => 'MY',
      'title' => /*t(*/'Negeri Sembilan'/*)*/,
      'tag' => '0'
    ),
    'MY-PG' => array('country' => 'MY', 'title' => /*t(*/'Penang'/*)*/, 'tag' => '0'),
    'MY-PH' => array('country' => 'MY', 'title' => /*t(*/'Pahang'/*)*/, 'tag' => '0'),
    'MY-PK' => array('country' => 'MY', 'title' => /*t(*/'Perak'/*)*/, 'tag' => '0'),
    'MY-PS' => array('country' => 'MY', 'title' => /*t(*/'Perlis'/*)*/, 'tag' => '0'),
    'MY-SB' => array('country' => 'MY', 'title' => /*t(*/'Sabah'/*)*/, 'tag' => '0'),
    'MY-SL' => array('country' => 'MY', 'title' => /*t(*/'Selangor'/*)*/, 'tag' => '0'),
    'MY-SR' => array('country' => 'MY', 'title' => /*t(*/'Sarawak'/*)*/, 'tag' => '0'),
    'MY-TR' => array('country' => 'MY', 'title' => /*t(*/'Terengganu'/*)*/, 'tag' => '0'),
    'MY-WP' => array(
      'country' => 'MY',
      'title' => /*t(*/'Wilayah Persekutuan'/*)*/,
      'tag' => '0'
    ),
    'MZ-CD' => array(
      'country' => 'MZ',
      'title' => /*t(*/'Cabo Delgado'/*)*/,
      'tag' => '0'
    ),
    'MZ-GZ' => array('country' => 'MZ', 'title' => /*t(*/'Gaza'/*)*/, 'tag' => '0'),
    'MZ-IN' => array('country' => 'MZ', 'title' => /*t(*/'Inhambane'/*)*/, 'tag' => '0'),
    'MZ-MC' => array(
      'country' => 'MZ',
      'title' => /*t(*/'Maputo (city)'/*)*/,
      'tag' => '0'
    ),
    'MZ-MN' => array('country' => 'MZ', 'title' => /*t(*/'Manica'/*)*/, 'tag' => '0'),
    'MZ-MP' => array('country' => 'MZ', 'title' => /*t(*/'Maputo'/*)*/, 'tag' => '0'),
    'MZ-NA' => array('country' => 'MZ', 'title' => /*t(*/'Nampula'/*)*/, 'tag' => '0'),
    'MZ-NI' => array('country' => 'MZ', 'title' => /*t(*/'Niassa'/*)*/, 'tag' => '0'),
    'MZ-SO' => array('country' => 'MZ', 'title' => /*t(*/'Sofala'/*)*/, 'tag' => '0'),
    'MZ-TE' => array('country' => 'MZ', 'title' => /*t(*/'Tete'/*)*/, 'tag' => '0'),
    'MZ-ZA' => array('country' => 'MZ', 'title' => /*t(*/'Zambezia'/*)*/, 'tag' => '0'),
    'NA-CA' => array('country' => 'NA', 'title' => /*t(*/'Caprivi'/*)*/, 'tag' => '0'),
    'NA-ER' => array('country' => 'NA', 'title' => /*t(*/'Erongo'/*)*/, 'tag' => '0'),
    'NA-HA' => array('country' => 'NA', 'title' => /*t(*/'Hardap'/*)*/, 'tag' => '0'),
    'NA-KH' => array('country' => 'NA', 'title' => /*t(*/'Khomas'/*)*/, 'tag' => '0'),
    'NA-KR' => array('country' => 'NA', 'title' => /*t(*/'Karas'/*)*/, 'tag' => '0'),
    'NA-KU' => array('country' => 'NA', 'title' => /*t(*/'Kunene'/*)*/, 'tag' => '0'),
    'NA-KV' => array('country' => 'NA', 'title' => /*t(*/'Kavango'/*)*/, 'tag' => '0'),
    'NA-OJ' => array(
      'country' => 'NA',
      'title' => /*t(*/'Otjozondjupa'/*)*/,
      'tag' => '0'
    ),
    'NA-OK' => array('country' => 'NA', 'title' => /*t(*/'Omaheke'/*)*/, 'tag' => '0'),
    'NA-ON' => array('country' => 'NA', 'title' => /*t(*/'Oshana'/*)*/, 'tag' => '0'),
    'NA-OO' => array('country' => 'NA', 'title' => /*t(*/'Oshikoto'/*)*/, 'tag' => '0'),
    'NA-OT' => array('country' => 'NA', 'title' => /*t(*/'Omusati'/*)*/, 'tag' => '0'),
    'NA-OW' => array('country' => 'NA', 'title' => /*t(*/'Ohangwena'/*)*/, 'tag' => '0'),
    'NC-L' => array('country' => 'NC', 'title' => /*t(*/'Iles Loyaute'/*)*/, 'tag' => '0'),
    'NC-N' => array('country' => 'NC', 'title' => /*t(*/'Nord'/*)*/, 'tag' => '0'),
    'NC-S' => array('country' => 'NC', 'title' => /*t(*/'Sud'/*)*/, 'tag' => '0'),
    'NG-AB' => array('country' => 'NG', 'title' => /*t(*/'Abia'/*)*/, 'tag' => '0'),
    'NG-AD' => array('country' => 'NG', 'title' => /*t(*/'Adamawa'/*)*/, 'tag' => '0'),
    'NG-AG' => array('country' => 'NG', 'title' => /*t(*/'Agadez'/*)*/, 'tag' => '0'),
    'NG-AK' => array('country' => 'NG', 'title' => /*t(*/'Akwa Ibom'/*)*/, 'tag' => '0'),
    'NG-AN' => array('country' => 'NG', 'title' => /*t(*/'Anambra'/*)*/, 'tag' => '0'),
    'NG-BC' => array('country' => 'NG', 'title' => /*t(*/'Bauchi'/*)*/, 'tag' => '0'),
    'NG-BN' => array('country' => 'NG', 'title' => /*t(*/'Benue'/*)*/, 'tag' => '0'),
    'NG-BO' => array('country' => 'NG', 'title' => /*t(*/'Borno'/*)*/, 'tag' => '0'),
    'NG-BY' => array('country' => 'NG', 'title' => /*t(*/'Bayelsa'/*)*/, 'tag' => '0'),
    'NG-CR' => array('country' => 'NG', 'title' => /*t(*/'Cross River'/*)*/, 'tag' => '0'),
    'NG-CT' => array(
      'country' => 'NG',
      'title' => /*t(*/'Federal Capital Territory'/*)*/,
      'tag' => '0'
    ),
    'NG-DE' => array('country' => 'NG', 'title' => /*t(*/'Delta'/*)*/, 'tag' => '0'),
    'NG-DF' => array('country' => 'NG', 'title' => /*t(*/'Diffa'/*)*/, 'tag' => '0'),
    'NG-DS' => array('country' => 'NG', 'title' => /*t(*/'Dosso'/*)*/, 'tag' => '0'),
    'NG-EB' => array('country' => 'NG', 'title' => /*t(*/'Ebonyi'/*)*/, 'tag' => '0'),
    'NG-ED' => array('country' => 'NG', 'title' => /*t(*/'Edo'/*)*/, 'tag' => '0'),
    'NG-EK' => array('country' => 'NG', 'title' => /*t(*/'Ekiti'/*)*/, 'tag' => '0'),
    'NG-EN' => array('country' => 'NG', 'title' => /*t(*/'Enugu'/*)*/, 'tag' => '0'),
    'NG-GO' => array('country' => 'NG', 'title' => /*t(*/'Gombe'/*)*/, 'tag' => '0'),
    'NG-IM' => array('country' => 'NG', 'title' => /*t(*/'Imo'/*)*/, 'tag' => '0'),
    'NG-JI' => array('country' => 'NG', 'title' => /*t(*/'Jigawa'/*)*/, 'tag' => '0'),
    'NG-KD' => array('country' => 'NG', 'title' => /*t(*/'Kaduna'/*)*/, 'tag' => '0'),
    'NG-KE' => array('country' => 'NG', 'title' => /*t(*/'Kebbi'/*)*/, 'tag' => '0'),
    'NG-KN' => array('country' => 'NG', 'title' => /*t(*/'Kano'/*)*/, 'tag' => '0'),
    'NG-KO' => array('country' => 'NG', 'title' => /*t(*/'Kogi'/*)*/, 'tag' => '0'),
    'NG-KT' => array('country' => 'NG', 'title' => /*t(*/'Katsina'/*)*/, 'tag' => '0'),
    'NG-KW' => array('country' => 'NG', 'title' => /*t(*/'Kwara'/*)*/, 'tag' => '0'),
    'NG-LA' => array('country' => 'NG', 'title' => /*t(*/'Lagos'/*)*/, 'tag' => '0'),
    'NG-MA' => array('country' => 'NG', 'title' => /*t(*/'Maradi'/*)*/, 'tag' => '0'),
    'NG-NA' => array('country' => 'NG', 'title' => /*t(*/'Nassarawa'/*)*/, 'tag' => '0'),
    'NG-NI' => array('country' => 'NG', 'title' => /*t(*/'Niger'/*)*/, 'tag' => '0'),
    'NG-NM' => array('country' => 'NG', 'title' => /*t(*/'Niamey'/*)*/, 'tag' => '0'),
    'NG-OG' => array('country' => 'NG', 'title' => /*t(*/'Ogun'/*)*/, 'tag' => '0'),
    'NG-ONG' => array('country' => 'NG', 'title' => /*t(*/'Ondo'/*)*/, 'tag' => '0'),
    'NG-OS' => array('country' => 'NG', 'title' => /*t(*/'Osun'/*)*/, 'tag' => '0'),
    'NG-OY' => array('country' => 'NG', 'title' => /*t(*/'Oyo'/*)*/, 'tag' => '0'),
    'NG-PL' => array('country' => 'NG', 'title' => /*t(*/'Plateau'/*)*/, 'tag' => '0'),
    'NG-RI' => array('country' => 'NG', 'title' => /*t(*/'Rivers'/*)*/, 'tag' => '0'),
    'NG-SO' => array('country' => 'NG', 'title' => /*t(*/'Sokoto'/*)*/, 'tag' => '0'),
    'NG-TA' => array('country' => 'NG', 'title' => /*t(*/'Taraba'/*)*/, 'tag' => '0'),
    'NG-TH' => array('country' => 'NG', 'title' => /*t(*/'Tahoua'/*)*/, 'tag' => '0'),
    'NG-TL' => array('country' => 'NG', 'title' => /*t(*/'Tillaberi'/*)*/, 'tag' => '0'),
    'NG-YO' => array('country' => 'NG', 'title' => /*t(*/'Yobe'/*)*/, 'tag' => '0'),
    'NG-ZA' => array('country' => 'NG', 'title' => /*t(*/'Zamfara'/*)*/, 'tag' => '0'),
    'NG-ZD' => array('country' => 'NG', 'title' => /*t(*/'Zinder'/*)*/, 'tag' => '0'),
    'NI-AN' => array(
      'country' => 'NI',
      'title' => /*t(*/'Region Autonoma del Atlantico Norte'/*)*/,
      'tag' => '0'
    ),
    'NI-AS' => array(
      'country' => 'NI',
      'title' => /*t(*/'Region Autonoma del Atlantico Sur'/*)*/,
      'tag' => '0'
    ),
    'NI-BO' => array('country' => 'NI', 'title' => /*t(*/'Boaco'/*)*/, 'tag' => '0'),
    'NI-CA' => array('country' => 'NI', 'title' => /*t(*/'Carazo'/*)*/, 'tag' => '0'),
    'NI-CD' => array('country' => 'NI', 'title' => /*t(*/'Chinandega'/*)*/, 'tag' => '0'),
    'NI-CT' => array('country' => 'NI', 'title' => /*t(*/'Chontales'/*)*/, 'tag' => '0'),
    'NI-ES' => array('country' => 'NI', 'title' => /*t(*/'Esteli'/*)*/, 'tag' => '0'),
    'NI-GR' => array('country' => 'NI', 'title' => /*t(*/'Granada'/*)*/, 'tag' => '0'),
    'NI-JI' => array('country' => 'NI', 'title' => /*t(*/'Jinotega'/*)*/, 'tag' => '0'),
    'NI-LE' => array('country' => 'NI', 'title' => /*t(*/'Leon'/*)*/, 'tag' => '0'),
    'NI-MD' => array('country' => 'NI', 'title' => /*t(*/'Madriz'/*)*/, 'tag' => '0'),
    'NI-MN' => array('country' => 'NI', 'title' => /*t(*/'Managua'/*)*/, 'tag' => '0'),
    'NI-MS' => array('country' => 'NI', 'title' => /*t(*/'Masaya'/*)*/, 'tag' => '0'),
    'NI-MT' => array('country' => 'NI', 'title' => /*t(*/'Matagalpa'/*)*/, 'tag' => '0'),
    'NI-NS' => array(
      'country' => 'NI',
      'title' => /*t(*/'Nueva Segovia'/*)*/,
      'tag' => '0'
    ),
    'NI-RV' => array('country' => 'NI', 'title' => /*t(*/'Rivas'/*)*/, 'tag' => '0'),
    'NI-SJ' => array(
      'country' => 'NI',
      'title' => /*t(*/'Rio San Juan'/*)*/,
      'tag' => '0'
    ),
    'NL-DR' => array('country' => 'NL', 'title' => /*t(*/'Drenthe'/*)*/, 'tag' => '0'),
    'NL-FL' => array('country' => 'NL', 'title' => /*t(*/'Flevoland'/*)*/, 'tag' => '0'),
    'NL-FR' => array('country' => 'NL', 'title' => /*t(*/'Friesland'/*)*/, 'tag' => '0'),
    'NL-GE' => array('country' => 'NL', 'title' => /*t(*/'Gelderland'/*)*/, 'tag' => '0'),
    'NL-GR' => array('country' => 'NL', 'title' => /*t(*/'Groningen'/*)*/, 'tag' => '0'),
    'NL-LI' => array('country' => 'NL', 'title' => /*t(*/'Limburg'/*)*/, 'tag' => '0'),
    'NL-NB' => array(
      'country' => 'NL',
      'title' => /*t(*/'Noord Brabant'/*)*/,
      'tag' => '0'
    ),
    'NL-NH' => array(
      'country' => 'NL',
      'title' => /*t(*/'Noord Holland'/*)*/,
      'tag' => '0'
    ),
    'NL-OV' => array('country' => 'NL', 'title' => /*t(*/'Overijssel'/*)*/, 'tag' => '0'),
    'NL-UT' => array('country' => 'NL', 'title' => /*t(*/'Utrecht'/*)*/, 'tag' => '0'),
    'NL-ZE' => array('country' => 'NL', 'title' => /*t(*/'Zeeland'/*)*/, 'tag' => '0'),
    'NL-ZH' => array(
      'country' => 'NL',
      'title' => /*t(*/'Zuid Holland'/*)*/,
      'tag' => '0'
    ),
    'NO-AA' => array('country' => 'NO', 'title' => /*t(*/'Aust-Agder'/*)*/, 'tag' => '0'),
    'NO-AK' => array('country' => 'NO', 'title' => /*t(*/'Akershus'/*)*/, 'tag' => '0'),
    'NO-BU' => array('country' => 'NO', 'title' => /*t(*/'Buskerud'/*)*/, 'tag' => '0'),
    'NO-FM' => array('country' => 'NO', 'title' => /*t(*/'Finnmark'/*)*/, 'tag' => '0'),
    'NO-HL' => array('country' => 'NO', 'title' => /*t(*/'Hordaland'/*)*/, 'tag' => '0'),
    'NO-HM' => array('country' => 'NO', 'title' => /*t(*/'Hedmark'/*)*/, 'tag' => '0'),
    'NO-MR' => array(
      'country' => 'NO',
      'title' => /*t(*/'More og Romdal'/*)*/,
      'tag' => '0'
    ),
    'NO-NL' => array('country' => 'NO', 'title' => /*t(*/'Nordland'/*)*/, 'tag' => '0'),
    'NO-NT' => array(
      'country' => 'NO',
      'title' => /*t(*/'Nord-Trondelag'/*)*/,
      'tag' => '0'
    ),
    'NO-OF' => array('country' => 'NO', 'title' => /*t(*/'Ostfold'/*)*/, 'tag' => '0'),
    'NO-OL' => array('country' => 'NO', 'title' => /*t(*/'Oslo'/*)*/, 'tag' => '0'),
    'NO-OP' => array('country' => 'NO', 'title' => /*t(*/'Oppland'/*)*/, 'tag' => '0'),
    'NO-RL' => array('country' => 'NO', 'title' => /*t(*/'Rogaland'/*)*/, 'tag' => '0'),
    'NO-SJ' => array(
      'country' => 'NO',
      'title' => /*t(*/'Sogn og Fjordane'/*)*/,
      'tag' => '0'
    ),
    'NO-ST' => array(
      'country' => 'NO',
      'title' => /*t(*/'Sor-Trondelag'/*)*/,
      'tag' => '0'
    ),
    'NO-TM' => array('country' => 'NO', 'title' => /*t(*/'Telemark'/*)*/, 'tag' => '0'),
    'NO-TR' => array('country' => 'NO', 'title' => /*t(*/'Troms'/*)*/, 'tag' => '0'),
    'NO-VA' => array('country' => 'NO', 'title' => /*t(*/'Vest-Agder'/*)*/, 'tag' => '0'),
    'NO-VF' => array('country' => 'NO', 'title' => /*t(*/'Vestfold'/*)*/, 'tag' => '0'),
    'NP-BA' => array('country' => 'NP', 'title' => /*t(*/'Bagmati'/*)*/, 'tag' => '0'),
    'NP-BH' => array('country' => 'NP', 'title' => /*t(*/'Bheri'/*)*/, 'tag' => '0'),
    'NP-DH' => array('country' => 'NP', 'title' => /*t(*/'Dhawalagiri'/*)*/, 'tag' => '0'),
    'NP-GA' => array('country' => 'NP', 'title' => /*t(*/'Gandaki'/*)*/, 'tag' => '0'),
    'NP-JA' => array('country' => 'NP', 'title' => /*t(*/'Janakpur'/*)*/, 'tag' => '0'),
    'NP-KA' => array('country' => 'NP', 'title' => /*t(*/'Karnali'/*)*/, 'tag' => '0'),
    'NP-KO' => array('country' => 'NP', 'title' => /*t(*/'Kosi'/*)*/, 'tag' => '0'),
    'NP-LU' => array('country' => 'NP', 'title' => /*t(*/'Lumbini'/*)*/, 'tag' => '0'),
    'NP-MA' => array('country' => 'NP', 'title' => /*t(*/'Mahakali'/*)*/, 'tag' => '0'),
    'NP-ME' => array('country' => 'NP', 'title' => /*t(*/'Mechi'/*)*/, 'tag' => '0'),
    'NP-NA' => array('country' => 'NP', 'title' => /*t(*/'Narayani'/*)*/, 'tag' => '0'),
    'NP-RA' => array('country' => 'NP', 'title' => /*t(*/'Rapti'/*)*/, 'tag' => '0'),
    'NP-SA' => array('country' => 'NP', 'title' => /*t(*/'Sagarmatha'/*)*/, 'tag' => '0'),
    'NP-SE' => array('country' => 'NP', 'title' => /*t(*/'Seti'/*)*/, 'tag' => '0'),
    'NR-AA' => array('country' => 'NR', 'title' => /*t(*/'Anabar'/*)*/, 'tag' => '0'),
    'NR-AI' => array('country' => 'NR', 'title' => /*t(*/'Anibare'/*)*/, 'tag' => '0'),
    'NR-AO' => array('country' => 'NR', 'title' => /*t(*/'Aiwo'/*)*/, 'tag' => '0'),
    'NR-AT' => array('country' => 'NR', 'title' => /*t(*/'Anetan'/*)*/, 'tag' => '0'),
    'NR-BA' => array('country' => 'NR', 'title' => /*t(*/'Baiti'/*)*/, 'tag' => '0'),
    'NR-BO' => array('country' => 'NR', 'title' => /*t(*/'Boe'/*)*/, 'tag' => '0'),
    'NR-BU' => array('country' => 'NR', 'title' => /*t(*/'Buada'/*)*/, 'tag' => '0'),
    'NR-DE' => array('country' => 'NR', 'title' => /*t(*/'Denigomodu'/*)*/, 'tag' => '0'),
    'NR-EW' => array('country' => 'NR', 'title' => /*t(*/'Ewa'/*)*/, 'tag' => '0'),
    'NR-IJ' => array('country' => 'NR', 'title' => /*t(*/'Ijuw'/*)*/, 'tag' => '0'),
    'NR-ME' => array('country' => 'NR', 'title' => /*t(*/'Meneng'/*)*/, 'tag' => '0'),
    'NR-NI' => array('country' => 'NR', 'title' => /*t(*/'Nibok'/*)*/, 'tag' => '0'),
    'NR-UA' => array('country' => 'NR', 'title' => /*t(*/'Uaboe'/*)*/, 'tag' => '0'),
    'NR-YA' => array('country' => 'NR', 'title' => /*t(*/'Yaren'/*)*/, 'tag' => '0'),
    'NZ-AUK' => array('country' => 'NZ', 'title' => /*t(*/'Auckland'/*)*/, 'tag' => '0'),
    'NZ-BOP' => array(
      'country' => 'NZ',
      'title' => /*t(*/'Bay of Plenty'/*)*/,
      'tag' => '0'
    ),
    'NZ-CAN' => array('country' => 'NZ', 'title' => /*t(*/'Canterbury'/*)*/, 'tag' => '0'),
    'NZ-GIS' => array('country' => 'NZ', 'title' => /*t(*/'Gisborne'/*)*/, 'tag' => '0'),
    'NZ-HKB' => array(
      'country' => 'NZ',
      'title' => /*t(*/"Hawke's Bay"/*)*/,
      'tag' => '0'
    ),
    'NZ-MBH' => array(
      'country' => 'NZ',
      'title' => /*t(*/'Marlborough'/*)*/,
      'tag' => '0'
    ),
    'NZ-MWT' => array(
      'country' => 'NZ',
      'title' => /*t(*/'Manawatu-Wanganui'/*)*/,
      'tag' => '0'
    ),
    'NZ-NSN' => array('country' => 'NZ', 'title' => /*t(*/'Nelson'/*)*/, 'tag' => '0'),
    'NZ-NTL' => array('country' => 'NZ', 'title' => /*t(*/'Northland'/*)*/, 'tag' => '0'),
    'NZ-OTA' => array('country' => 'NZ', 'title' => /*t(*/'Otago'/*)*/, 'tag' => '0'),
    'NZ-STL' => array('country' => 'NZ', 'title' => /*t(*/'Southland'/*)*/, 'tag' => '0'),
    'NZ-TAS' => array('country' => 'NZ', 'title' => /*t(*/'Tasman'/*)*/, 'tag' => '0'),
    'NZ-TKI' => array('country' => 'NZ', 'title' => /*t(*/'Taranaki'/*)*/, 'tag' => '0'),
    'NZ-WGN' => array('country' => 'NZ', 'title' => /*t(*/'Wellington'/*)*/, 'tag' => '0'),
    'NZ-WKO' => array('country' => 'NZ', 'title' => /*t(*/'Waikato'/*)*/, 'tag' => '0'),
    'NZ-WTC' => array('country' => 'NZ', 'title' => /*t(*/'West Coast'/*)*/, 'tag' => '0'),
    'OM-BA' => array('country' => 'OM', 'title' => /*t(*/'Al Batinah'/*)*/, 'tag' => '0'),
    'OM-DA' => array(
      'country' => 'OM',
      'title' => /*t(*/'Ad Dakhiliyah'/*)*/,
      'tag' => '0'
    ),
    'OM-MA' => array('country' => 'OM', 'title' => /*t(*/'Masqat'/*)*/, 'tag' => '0'),
    'OM-MU' => array('country' => 'OM', 'title' => /*t(*/'Musandam'/*)*/, 'tag' => '0'),
    'OM-SH' => array(
      'country' => 'OM',
      'title' => /*t(*/'Ash Sharqiyah'/*)*/,
      'tag' => '0'
    ),
    'OM-WU' => array('country' => 'OM', 'title' => /*t(*/'Al Wusta'/*)*/, 'tag' => '0'),
    'OM-ZA' => array('country' => 'OM', 'title' => /*t(*/'Az Zahirah'/*)*/, 'tag' => '0'),
    'OM-ZU' => array('country' => 'OM', 'title' => /*t(*/'Zufar'/*)*/, 'tag' => '0'),
    'PA-BT' => array(
      'country' => 'PA',
      'title' => /*t(*/'Bocas del Toro'/*)*/,
      'tag' => '0'
    ),
    'PA-CC' => array('country' => 'PA', 'title' => /*t(*/'Cocle'/*)*/, 'tag' => '0'),
    'PA-CH' => array('country' => 'PA', 'title' => /*t(*/'Chiriqui'/*)*/, 'tag' => '0'),
    'PA-CL' => array('country' => 'PA', 'title' => /*t(*/'Colon'/*)*/, 'tag' => '0'),
    'PA-DA' => array('country' => 'PA', 'title' => /*t(*/'Darien'/*)*/, 'tag' => '0'),
    'PA-HE' => array('country' => 'PA', 'title' => /*t(*/'Herrera'/*)*/, 'tag' => '0'),
    'PA-LS' => array('country' => 'PA', 'title' => /*t(*/'Los Santos'/*)*/, 'tag' => '0'),
    'PA-PA' => array('country' => 'PA', 'title' => /*t(*/'Panama'/*)*/, 'tag' => '0'),
    'PA-SB' => array('country' => 'PA', 'title' => /*t(*/'San Blas'/*)*/, 'tag' => '0'),
    'PA-VG' => array('country' => 'PA', 'title' => /*t(*/'Veraguas'/*)*/, 'tag' => '0'),
    'PE-AM' => array('country' => 'PE', 'title' => /*t(*/'Amazonas'/*)*/, 'tag' => '0'),
    'PE-AN' => array('country' => 'PE', 'title' => /*t(*/'Ancash'/*)*/, 'tag' => '0'),
    'PE-AP' => array('country' => 'PE', 'title' => /*t(*/'Apurimac'/*)*/, 'tag' => '0'),
    'PE-AR' => array('country' => 'PE', 'title' => /*t(*/'Arequipa'/*)*/, 'tag' => '0'),
    'PE-AY' => array('country' => 'PE', 'title' => /*t(*/'Ayacucho'/*)*/, 'tag' => '0'),
    'PE-CJ' => array('country' => 'PE', 'title' => /*t(*/'Cajamarca'/*)*/, 'tag' => '0'),
    'PE-CL' => array('country' => 'PE', 'title' => /*t(*/'Callao'/*)*/, 'tag' => '0'),
    'PE-CU' => array('country' => 'PE', 'title' => /*t(*/'Cusco'/*)*/, 'tag' => '0'),
    'PE-HO' => array('country' => 'PE', 'title' => /*t(*/'Huanuco'/*)*/, 'tag' => '0'),
    'PE-HV' => array(
      'country' => 'PE',
      'title' => /*t(*/'Huancavelica'/*)*/,
      'tag' => '0'
    ),
    'PE-IC' => array('country' => 'PE', 'title' => /*t(*/'Ica'/*)*/, 'tag' => '0'),
    'PE-JU' => array('country' => 'PE', 'title' => /*t(*/'Junin'/*)*/, 'tag' => '0'),
    'PE-LD' => array('country' => 'PE', 'title' => /*t(*/'La Libertad'/*)*/, 'tag' => '0'),
    'PE-LI' => array('country' => 'PE', 'title' => /*t(*/'Lima'/*)*/, 'tag' => '0'),
    'PE-LO' => array('country' => 'PE', 'title' => /*t(*/'Loreto'/*)*/, 'tag' => '0'),
    'PE-LY' => array('country' => 'PE', 'title' => /*t(*/'Lambayeque'/*)*/, 'tag' => '0'),
    'PE-MD' => array(
      'country' => 'PE',
      'title' => /*t(*/'Madre de Dios'/*)*/,
      'tag' => '0'
    ),
    'PE-MO' => array('country' => 'PE', 'title' => /*t(*/'Moquegua'/*)*/, 'tag' => '0'),
    'PE-PA' => array('country' => 'PE', 'title' => /*t(*/'Pasco'/*)*/, 'tag' => '0'),
    'PE-PI' => array('country' => 'PE', 'title' => /*t(*/'Piura'/*)*/, 'tag' => '0'),
    'PE-PU' => array('country' => 'PE', 'title' => /*t(*/'Puno'/*)*/, 'tag' => '0'),
    'PE-SM' => array('country' => 'PE', 'title' => /*t(*/'San Martin'/*)*/, 'tag' => '0'),
    'PE-TA' => array('country' => 'PE', 'title' => /*t(*/'Tacna'/*)*/, 'tag' => '0'),
    'PE-TU' => array('country' => 'PE', 'title' => /*t(*/'Tumbes'/*)*/, 'tag' => '0'),
    'PE-UC' => array('country' => 'PE', 'title' => /*t(*/'Ucayali'/*)*/, 'tag' => '0'),
    'PF-I' => array(
      'country' => 'PF',
      'title' => /*t(*/'Archipel des Tubuai'/*)*/,
      'tag' => '0'
    ),
    'PF-M' => array(
      'country' => 'PF',
      'title' => /*t(*/'Archipel des Marquises'/*)*/,
      'tag' => '0'
    ),
    'PF-S' => array(
      'country' => 'PF',
      'title' => /*t(*/'Iles Sous-le-Vent'/*)*/,
      'tag' => '0'
    ),
    'PF-T' => array(
      'country' => 'PF',
      'title' => /*t(*/'Archipel des Tuamotu'/*)*/,
      'tag' => '0'
    ),
    'PF-V' => array('country' => 'PF', 'title' => /*t(*/'Iles du Vent'/*)*/, 'tag' => '0'),
    'PG-BV' => array(
      'country' => 'PG',
      'title' => /*t(*/'Bougainville'/*)*/,
      'tag' => '0'
    ),
    'PG-CE' => array('country' => 'PG', 'title' => /*t(*/'Central'/*)*/, 'tag' => '0'),
    'PG-CH' => array('country' => 'PG', 'title' => /*t(*/'Chimbu'/*)*/, 'tag' => '0'),
    'PG-EB' => array(
      'country' => 'PG',
      'title' => /*t(*/'East New Britain'/*)*/,
      'tag' => '0'
    ),
    'PG-EH' => array(
      'country' => 'PG',
      'title' => /*t(*/'Eastern Highlands'/*)*/,
      'tag' => '0'
    ),
    'PG-EN' => array('country' => 'PG', 'title' => /*t(*/'Enga'/*)*/, 'tag' => '0'),
    'PG-ES' => array('country' => 'PG', 'title' => /*t(*/'East Sepik'/*)*/, 'tag' => '0'),
    'PG-GU' => array('country' => 'PG', 'title' => /*t(*/'Gulf'/*)*/, 'tag' => '0'),
    'PG-MB' => array('country' => 'PG', 'title' => /*t(*/'Milne Bay'/*)*/, 'tag' => '0'),
    'PG-MD' => array('country' => 'PG', 'title' => /*t(*/'Madang'/*)*/, 'tag' => '0'),
    'PG-MN' => array('country' => 'PG', 'title' => /*t(*/'Manus'/*)*/, 'tag' => '0'),
    'PG-MR' => array('country' => 'PG', 'title' => /*t(*/'Morobe'/*)*/, 'tag' => '0'),
    'PG-NC' => array(
      'country' => 'PG',
      'title' => /*t(*/'National Capital'/*)*/,
      'tag' => '0'
    ),
    'PG-NI' => array('country' => 'PG', 'title' => /*t(*/'New Ireland'/*)*/, 'tag' => '0'),
    'PG-NO' => array('country' => 'PG', 'title' => /*t(*/'Northern'/*)*/, 'tag' => '0'),
    'PG-SA' => array('country' => 'PG', 'title' => /*t(*/'Sandaun'/*)*/, 'tag' => '0'),
    'PG-SH' => array(
      'country' => 'PG',
      'title' => /*t(*/'Southern Highlands'/*)*/,
      'tag' => '0'
    ),
    'PG-WB' => array(
      'country' => 'PG',
      'title' => /*t(*/'West New Britain'/*)*/,
      'tag' => '0'
    ),
    'PG-WE' => array('country' => 'PG', 'title' => /*t(*/'Western'/*)*/, 'tag' => '0'),
    'PG-WH' => array(
      'country' => 'PG',
      'title' => /*t(*/'Western Highlands'/*)*/,
      'tag' => '0'
    ),
    'PH-ABR' => array('country' => 'PH', 'title' => /*t(*/'Abra'/*)*/, 'tag' => '0'),
    'PH-AKL' => array('country' => 'PH', 'title' => /*t(*/'Aklan'/*)*/, 'tag' => '0'),
    'PH-ALB' => array('country' => 'PH', 'title' => /*t(*/'Albay'/*)*/, 'tag' => '0'),
    'PH-ANO' => array(
      'country' => 'PH',
      'title' => /*t(*/'Agusan del Norte'/*)*/,
      'tag' => '0'
    ),
    'PH-ANT' => array('country' => 'PH', 'title' => /*t(*/'Antique'/*)*/, 'tag' => '0'),
    'PH-APY' => array('country' => 'PH', 'title' => /*t(*/'Apayao'/*)*/, 'tag' => '0'),
    'PH-ASU' => array(
      'country' => 'PH',
      'title' => /*t(*/'Agusan del Sur'/*)*/,
      'tag' => '0'
    ),
    'PH-AUR' => array('country' => 'PH', 'title' => /*t(*/'Aurora'/*)*/, 'tag' => '0'),
    'PH-BAS' => array('country' => 'PH', 'title' => /*t(*/'Basilan'/*)*/, 'tag' => '0'),
    'PH-BEN' => array('country' => 'PH', 'title' => /*t(*/'Benguet'/*)*/, 'tag' => '0'),
    'PH-BLR' => array('country' => 'PH', 'title' => /*t(*/'Biliran'/*)*/, 'tag' => '0'),
    'PH-BOL' => array('country' => 'PH', 'title' => /*t(*/'Bohol'/*)*/, 'tag' => '0'),
    'PH-BTA' => array('country' => 'PH', 'title' => /*t(*/'Bataan'/*)*/, 'tag' => '0'),
    'PH-BTE' => array('country' => 'PH', 'title' => /*t(*/'Batanes'/*)*/, 'tag' => '0'),
    'PH-BTG' => array('country' => 'PH', 'title' => /*t(*/'Batangas'/*)*/, 'tag' => '0'),
    'PH-BUK' => array('country' => 'PH', 'title' => /*t(*/'Bukidnon'/*)*/, 'tag' => '0'),
    'PH-BUL' => array('country' => 'PH', 'title' => /*t(*/'Bulacan'/*)*/, 'tag' => '0'),
    'PH-CAG' => array('country' => 'PH', 'title' => /*t(*/'Cagayan'/*)*/, 'tag' => '0'),
    'PH-CAM' => array('country' => 'PH', 'title' => /*t(*/'Camiguin'/*)*/, 'tag' => '0'),
    'PH-CAP' => array('country' => 'PH', 'title' => /*t(*/'Capiz'/*)*/, 'tag' => '0'),
    'PH-CAT' => array(
      'country' => 'PH',
      'title' => /*t(*/'Catanduanes'/*)*/,
      'tag' => '0'
    ),
    'PH-CAV' => array('country' => 'PH', 'title' => /*t(*/'Cavite'/*)*/, 'tag' => '0'),
    'PH-CEB' => array('country' => 'PH', 'title' => /*t(*/'Cebu'/*)*/, 'tag' => '0'),
    'PH-CMP' => array('country' => 'PH', 'title' => /*t(*/'Compostela'/*)*/, 'tag' => '0'),
    'PH-CNO' => array(
      'country' => 'PH',
      'title' => /*t(*/'Camarines Norte'/*)*/,
      'tag' => '0'
    ),
    'PH-CSU' => array(
      'country' => 'PH',
      'title' => /*t(*/'Camarines Sur'/*)*/,
      'tag' => '0'
    ),
    'PH-DNO' => array(
      'country' => 'PH',
      'title' => /*t(*/'Davao del Norte'/*)*/,
      'tag' => '0'
    ),
    'PH-DOR' => array(
      'country' => 'PH',
      'title' => /*t(*/'Davao Oriental'/*)*/,
      'tag' => '0'
    ),
    'PH-DSU' => array(
      'country' => 'PH',
      'title' => /*t(*/'Davao del Sur'/*)*/,
      'tag' => '0'
    ),
    'PH-ESA' => array(
      'country' => 'PH',
      'title' => /*t(*/'Eastern Samar'/*)*/,
      'tag' => '0'
    ),
    'PH-GUI' => array('country' => 'PH', 'title' => /*t(*/'Guimaras'/*)*/, 'tag' => '0'),
    'PH-IFU' => array('country' => 'PH', 'title' => /*t(*/'Ifugao'/*)*/, 'tag' => '0'),
    'PH-ILO' => array('country' => 'PH', 'title' => /*t(*/'Iloilo'/*)*/, 'tag' => '0'),
    'PH-INO' => array(
      'country' => 'PH',
      'title' => /*t(*/'Ilocos Norte'/*)*/,
      'tag' => '0'
    ),
    'PH-ISA' => array('country' => 'PH', 'title' => /*t(*/'Isabela'/*)*/, 'tag' => '0'),
    'PH-ISU' => array('country' => 'PH', 'title' => /*t(*/'Ilocos Sur'/*)*/, 'tag' => '0'),
    'PH-KAL' => array('country' => 'PH', 'title' => /*t(*/'Kalinga'/*)*/, 'tag' => '0'),
    'PH-LAG' => array('country' => 'PH', 'title' => /*t(*/'Laguna'/*)*/, 'tag' => '0'),
    'PH-LEY' => array('country' => 'PH', 'title' => /*t(*/'Leyte'/*)*/, 'tag' => '0'),
    'PH-LNO' => array(
      'country' => 'PH',
      'title' => /*t(*/'Lanao del Norte'/*)*/,
      'tag' => '0'
    ),
    'PH-LSU' => array(
      'country' => 'PH',
      'title' => /*t(*/'Lanao del Sur'/*)*/,
      'tag' => '0'
    ),
    'PH-MAG' => array(
      'country' => 'PH',
      'title' => /*t(*/'Maguindanao'/*)*/,
      'tag' => '0'
    ),
    'PH-MIC' => array(
      'country' => 'PH',
      'title' => /*t(*/'Mindoro Occidental'/*)*/,
      'tag' => '0'
    ),
    'PH-MIR' => array(
      'country' => 'PH',
      'title' => /*t(*/'Mindoro Oriental'/*)*/,
      'tag' => '0'
    ),
    'PH-MOP' => array(
      'country' => 'PH',
      'title' => /*t(*/'Mountain Province'/*)*/,
      'tag' => '0'
    ),
    'PH-MOR' => array(
      'country' => 'PH',
      'title' => /*t(*/'Misamis Oriental'/*)*/,
      'tag' => '0'
    ),
    'PH-MRN' => array('country' => 'PH', 'title' => /*t(*/'Marinduque'/*)*/, 'tag' => '0'),
    'PH-MSB' => array('country' => 'PH', 'title' => /*t(*/'Masbate'/*)*/, 'tag' => '0'),
    'PH-MSC' => array(
      'country' => 'PH',
      'title' => /*t(*/'Misamis Occidental'/*)*/,
      'tag' => '0'
    ),
    'PH-NCT' => array(
      'country' => 'PH',
      'title' => /*t(*/'North Cotabato'/*)*/,
      'tag' => '0'
    ),
    'PH-NEC' => array(
      'country' => 'PH',
      'title' => /*t(*/'Nueva Ecija'/*)*/,
      'tag' => '0'
    ),
    'PH-NOC' => array(
      'country' => 'PH',
      'title' => /*t(*/'Negros Occidental'/*)*/,
      'tag' => '0'
    ),
    'PH-NOR' => array(
      'country' => 'PH',
      'title' => /*t(*/'Negros Oriental'/*)*/,
      'tag' => '0'
    ),
    'PH-NSM' => array(
      'country' => 'PH',
      'title' => /*t(*/'Northern Samar'/*)*/,
      'tag' => '0'
    ),
    'PH-NVZ' => array(
      'country' => 'PH',
      'title' => /*t(*/'Nueva Vizcaya'/*)*/,
      'tag' => '0'
    ),
    'PH-PLW' => array('country' => 'PH', 'title' => /*t(*/'Palawan'/*)*/, 'tag' => '0'),
    'PH-PMP' => array('country' => 'PH', 'title' => /*t(*/'Pampanga'/*)*/, 'tag' => '0'),
    'PH-PNG' => array('country' => 'PH', 'title' => /*t(*/'Pangasinan'/*)*/, 'tag' => '0'),
    'PH-QRN' => array('country' => 'PH', 'title' => /*t(*/'Quirino'/*)*/, 'tag' => '0'),
    'PH-QZN' => array('country' => 'PH', 'title' => /*t(*/'Quezon'/*)*/, 'tag' => '0'),
    'PH-RIZ' => array('country' => 'PH', 'title' => /*t(*/'Rizal'/*)*/, 'tag' => '0'),
    'PH-ROM' => array('country' => 'PH', 'title' => /*t(*/'Romblon'/*)*/, 'tag' => '0'),
    'PH-SCO' => array(
      'country' => 'PH',
      'title' => /*t(*/'South Cotabato'/*)*/,
      'tag' => '0'
    ),
    'PH-SKU' => array(
      'country' => 'PH',
      'title' => /*t(*/'Sultan Kudarat'/*)*/,
      'tag' => '0'
    ),
    'PH-SLE' => array(
      'country' => 'PH',
      'title' => /*t(*/'Southern Leyte'/*)*/,
      'tag' => '0'
    ),
    'PH-SLU' => array('country' => 'PH', 'title' => /*t(*/'Sulu'/*)*/, 'tag' => '0'),
    'PH-SMR' => array('country' => 'PH', 'title' => /*t(*/'Samar'/*)*/, 'tag' => '0'),
    'PH-SNO' => array(
      'country' => 'PH',
      'title' => /*t(*/'Surigao del Norte'/*)*/,
      'tag' => '0'
    ),
    'PH-SQJ' => array('country' => 'PH', 'title' => /*t(*/'Siquijor'/*)*/, 'tag' => '0'),
    'PH-SRG' => array('country' => 'PH', 'title' => /*t(*/'Sarangani'/*)*/, 'tag' => '0'),
    'PH-SRS' => array('country' => 'PH', 'title' => /*t(*/'Sorsogon'/*)*/, 'tag' => '0'),
    'PH-SSU' => array(
      'country' => 'PH',
      'title' => /*t(*/'Surigao del Sur'/*)*/,
      'tag' => '0'
    ),
    'PH-TAR' => array('country' => 'PH', 'title' => /*t(*/'Tarlac'/*)*/, 'tag' => '0'),
    'PH-TAW' => array('country' => 'PH', 'title' => /*t(*/'Tawi-Tawi'/*)*/, 'tag' => '0'),
    'PH-UNI' => array('country' => 'PH', 'title' => /*t(*/'La Union'/*)*/, 'tag' => '0'),
    'PH-ZBL' => array('country' => 'PH', 'title' => /*t(*/'Zambales'/*)*/, 'tag' => '0'),
    'PH-ZNO' => array(
      'country' => 'PH',
      'title' => /*t(*/'Zamboanga del Norte'/*)*/,
      'tag' => '0'
    ),
    'PH-ZSI' => array(
      'country' => 'PH',
      'title' => /*t(*/'Zamboanga Sibugay'/*)*/,
      'tag' => '0'
    ),
    'PH-ZSU' => array(
      'country' => 'PH',
      'title' => /*t(*/'Zamboanga del Sur'/*)*/,
      'tag' => '0'
    ),
    'PK-B' => array('country' => 'PK', 'title' => /*t(*/'Balochistan'/*)*/, 'tag' => '0'),
    'PK-I' => array(
      'country' => 'PK',
      'title' => /*t(*/'Islamabad Capital Territory'/*)*/,
      'tag' => '0'
    ),
    'PK-N' => array(
      'country' => 'PK',
      'title' => /*t(*/'North-West Frontier Province'/*)*/,
      'tag' => '0'
    ),
    'PK-P' => array('country' => 'PK', 'title' => /*t(*/'Punjab'/*)*/, 'tag' => '0'),
    'PK-S' => array('country' => 'PK', 'title' => /*t(*/'Sindh'/*)*/, 'tag' => '0'),
    'PK-T' => array(
      'country' => 'PK',
      'title' => /*t(*/'Federally Administered Tribal Areas'/*)*/,
      'tag' => '0'
    ),
    'PL-DO' => array(
      'country' => 'PL',
      'title' => /*t(*/'Dolnoslaskie'/*)*/,
      'tag' => '0'
    ),
    'PL-KP' => array(
      'country' => 'PL',
      'title' => /*t(*/'Kujawsko-Pomorskie'/*)*/,
      'tag' => '0'
    ),
    'PL-LL' => array('country' => 'PL', 'title' => /*t(*/'Lubelskie'/*)*/, 'tag' => '0'),
    'PL-LO' => array('country' => 'PL', 'title' => /*t(*/'Lodzkie'/*)*/, 'tag' => '0'),
    'PL-LU' => array('country' => 'PL', 'title' => /*t(*/'Lubuskie'/*)*/, 'tag' => '0'),
    'PL-ML' => array('country' => 'PL', 'title' => /*t(*/'Malopolskie'/*)*/, 'tag' => '0'),
    'PL-MZ' => array('country' => 'PL', 'title' => /*t(*/'Mazowieckie'/*)*/, 'tag' => '0'),
    'PL-OP' => array('country' => 'PL', 'title' => /*t(*/'Opolskie'/*)*/, 'tag' => '0'),
    'PL-PL' => array('country' => 'PL', 'title' => /*t(*/'Podlaskie'/*)*/, 'tag' => '0'),
    'PL-PM' => array('country' => 'PL', 'title' => /*t(*/'Pomorskie'/*)*/, 'tag' => '0'),
    'PL-PP' => array(
      'country' => 'PL',
      'title' => /*t(*/'Podkarpackie'/*)*/,
      'tag' => '0'
    ),
    'PL-SL' => array('country' => 'PL', 'title' => /*t(*/'Slaskie'/*)*/, 'tag' => '0'),
    'PL-SW' => array(
      'country' => 'PL',
      'title' => /*t(*/'Swietokrzyskie'/*)*/,
      'tag' => '0'
    ),
    'PL-WM' => array(
      'country' => 'PL',
      'title' => /*t(*/'Warminsko-Mazurskie'/*)*/,
      'tag' => '0'
    ),
    'PL-WP' => array(
      'country' => 'PL',
      'title' => /*t(*/'Wielkopolskie'/*)*/,
      'tag' => '0'
    ),
    'PL-ZA' => array(
      'country' => 'PL',
      'title' => /*t(*/'Zachodniopomorskie'/*)*/,
      'tag' => '0'
    ),
    'PM-M' => array('country' => 'PM', 'title' => /*t(*/'Miquelon'/*)*/, 'tag' => '0'),
    'PM-P' => array('country' => 'PM', 'title' => /*t(*/'Saint Pierre'/*)*/, 'tag' => '0'),
    'PT-AR' => array(
      'country' => 'PT',
      'title' => /*t(*/'Acores (Azores)'/*)*/,
      'tag' => '0'
    ),
    'PT-AV' => array('country' => 'PT', 'title' => /*t(*/'Aveiro'/*)*/, 'tag' => '0'),
    'PT-BA' => array('country' => 'PT', 'title' => /*t(*/'Braga'/*)*/, 'tag' => '0'),
    'PT-BJ' => array('country' => 'PT', 'title' => /*t(*/'Beja'/*)*/, 'tag' => '0'),
    'PT-BN' => array('country' => 'PT', 'title' => /*t(*/'Braganca'/*)*/, 'tag' => '0'),
    'PT-CB' => array(
      'country' => 'PT',
      'title' => /*t(*/'Castelo Branco'/*)*/,
      'tag' => '0'
    ),
    'PT-CO' => array('country' => 'PT', 'title' => /*t(*/'Coimbra'/*)*/, 'tag' => '0'),
    'PT-EV' => array('country' => 'PT', 'title' => /*t(*/'Evora'/*)*/, 'tag' => '0'),
    'PT-FA' => array('country' => 'PT', 'title' => /*t(*/'Faro'/*)*/, 'tag' => '0'),
    'PT-GU' => array('country' => 'PT', 'title' => /*t(*/'Guarda'/*)*/, 'tag' => '0'),
    'PT-LE' => array('country' => 'PT', 'title' => /*t(*/'Leiria'/*)*/, 'tag' => '0'),
    'PT-LI' => array('country' => 'PT', 'title' => /*t(*/'Lisboa'/*)*/, 'tag' => '0'),
    'PT-MA' => array('country' => 'PT', 'title' => /*t(*/'Madeira'/*)*/, 'tag' => '0'),
    'PT-PG' => array('country' => 'PT', 'title' => /*t(*/'Portalegre'/*)*/, 'tag' => '0'),
    'PT-PO' => array('country' => 'PT', 'title' => /*t(*/'Porto'/*)*/, 'tag' => '0'),
    'PT-SA' => array('country' => 'PT', 'title' => /*t(*/'Santarem'/*)*/, 'tag' => '0'),
    'PT-SE' => array('country' => 'PT', 'title' => /*t(*/'Setubal'/*)*/, 'tag' => '0'),
    'PT-VC' => array(
      'country' => 'PT',
      'title' => /*t(*/'Viana do Castelo'/*)*/,
      'tag' => '0'
    ),
    'PT-VR' => array('country' => 'PT', 'title' => /*t(*/'Vila Real'/*)*/, 'tag' => '0'),
    'PT-VS' => array('country' => 'PT', 'title' => /*t(*/'Viseu'/*)*/, 'tag' => '0'),
    'PW-AM' => array('country' => 'PW', 'title' => /*t(*/'Aimeliik'/*)*/, 'tag' => '0'),
    'PW-AN' => array('country' => 'PW', 'title' => /*t(*/'Angaur'/*)*/, 'tag' => '0'),
    'PW-AR' => array('country' => 'PW', 'title' => /*t(*/'Airai'/*)*/, 'tag' => '0'),
    'PW-HA' => array('country' => 'PW', 'title' => /*t(*/'Hatohobei'/*)*/, 'tag' => '0'),
    'PW-KA' => array('country' => 'PW', 'title' => /*t(*/'Kayangel'/*)*/, 'tag' => '0'),
    'PW-KO' => array('country' => 'PW', 'title' => /*t(*/'Koror'/*)*/, 'tag' => '0'),
    'PW-ME' => array('country' => 'PW', 'title' => /*t(*/'Melekeok'/*)*/, 'tag' => '0'),
    'PW-NA' => array('country' => 'PW', 'title' => /*t(*/'Ngaraard'/*)*/, 'tag' => '0'),
    'PW-NC' => array('country' => 'PW', 'title' => /*t(*/'Ngchesar'/*)*/, 'tag' => '0'),
    'PW-ND' => array('country' => 'PW', 'title' => /*t(*/'Ngardmau'/*)*/, 'tag' => '0'),
    'PW-NG' => array('country' => 'PW', 'title' => /*t(*/'Ngarchelong'/*)*/, 'tag' => '0'),
    'PW-NR' => array(
      'country' => 'PW',
      'title' => /*t(*/'Ngeremlengui'/*)*/,
      'tag' => '0'
    ),
    'PW-NT' => array('country' => 'PW', 'title' => /*t(*/'Ngatpang'/*)*/, 'tag' => '0'),
    'PW-NW' => array('country' => 'PW', 'title' => /*t(*/'Ngiwal'/*)*/, 'tag' => '0'),
    'PW-PE' => array('country' => 'PW', 'title' => /*t(*/'Peleliu'/*)*/, 'tag' => '0'),
    'PW-SO' => array('country' => 'PW', 'title' => /*t(*/'Sonsorol'/*)*/, 'tag' => '0'),
    'PY-AG' => array(
      'country' => 'PY',
      'title' => /*t(*/'Alto Paraguay'/*)*/,
      'tag' => '0'
    ),
    'PY-AM' => array('country' => 'PY', 'title' => /*t(*/'Amambay'/*)*/, 'tag' => '0'),
    'PY-AN' => array('country' => 'PY', 'title' => /*t(*/'Alto Parana'/*)*/, 'tag' => '0'),
    'PY-AS' => array('country' => 'PY', 'title' => /*t(*/'Asuncion'/*)*/, 'tag' => '0'),
    'PY-BO' => array('country' => 'PY', 'title' => /*t(*/'Boqueron'/*)*/, 'tag' => '0'),
    'PY-CC' => array('country' => 'PY', 'title' => /*t(*/'Concepcion'/*)*/, 'tag' => '0'),
    'PY-CD' => array('country' => 'PY', 'title' => /*t(*/'Cordillera'/*)*/, 'tag' => '0'),
    'PY-CE' => array('country' => 'PY', 'title' => /*t(*/'Central'/*)*/, 'tag' => '0'),
    'PY-CG' => array('country' => 'PY', 'title' => /*t(*/'Caaguazu'/*)*/, 'tag' => '0'),
    'PY-CN' => array('country' => 'PY', 'title' => /*t(*/'Canindeyu'/*)*/, 'tag' => '0'),
    'PY-CZ' => array('country' => 'PY', 'title' => /*t(*/'Caazapa'/*)*/, 'tag' => '0'),
    'PY-GU' => array('country' => 'PY', 'title' => /*t(*/'Guaira'/*)*/, 'tag' => '0'),
    'PY-IT' => array('country' => 'PY', 'title' => /*t(*/'Itapua'/*)*/, 'tag' => '0'),
    'PY-MI' => array('country' => 'PY', 'title' => /*t(*/'Misiones'/*)*/, 'tag' => '0'),
    'PY-NE' => array('country' => 'PY', 'title' => /*t(*/'Neembucu'/*)*/, 'tag' => '0'),
    'PY-PA' => array('country' => 'PY', 'title' => /*t(*/'Paraguari'/*)*/, 'tag' => '0'),
    'PY-PH' => array(
      'country' => 'PY',
      'title' => /*t(*/'Presidente Hayes'/*)*/,
      'tag' => '0'
    ),
    'PY-SP' => array('country' => 'PY', 'title' => /*t(*/'San Pedro'/*)*/, 'tag' => '0'),
    'QA-DW' => array('country' => 'QA', 'title' => /*t(*/'Ad Dawhah'/*)*/, 'tag' => '0'),
    'QA-GW' => array(
      'country' => 'QA',
      'title' => /*t(*/'Al Ghuwayriyah'/*)*/,
      'tag' => '0'
    ),
    'QA-JB' => array(
      'country' => 'QA',
      'title' => /*t(*/'Jarayan al Batinah'/*)*/,
      'tag' => '0'
    ),
    'QA-JM' => array(
      'country' => 'QA',
      'title' => /*t(*/'Al Jumayliyah'/*)*/,
      'tag' => '0'
    ),
    'QA-KR' => array('country' => 'QA', 'title' => /*t(*/'Al Khawr'/*)*/, 'tag' => '0'),
    'QA-MS' => array(
      'country' => 'QA',
      'title' => /*t(*/'Madinat ash Shamal'/*)*/,
      'tag' => '0'
    ),
    'QA-RN' => array('country' => 'QA', 'title' => /*t(*/'Ar Rayyan'/*)*/, 'tag' => '0'),
    'QA-UD' => array('country' => 'QA', 'title' => /*t(*/"Umm Sa'id"/*)*/, 'tag' => '0'),
    'QA-UL' => array('country' => 'QA', 'title' => /*t(*/'Umm Salal'/*)*/, 'tag' => '0'),
    'QA-WK' => array('country' => 'QA', 'title' => /*t(*/'Al Wakrah'/*)*/, 'tag' => '0'),
    'RO-AD' => array('country' => 'RO', 'title' => /*t(*/'Arad'/*)*/, 'tag' => '0'),
    'RO-AG' => array('country' => 'RO', 'title' => /*t(*/'Arges'/*)*/, 'tag' => '0'),
    'RO-AL' => array('country' => 'RO', 'title' => /*t(*/'Alba'/*)*/, 'tag' => '0'),
    'RO-BA' => array('country' => 'RO', 'title' => /*t(*/'Bacau'/*)*/, 'tag' => '0'),
    'RO-BC' => array('country' => 'RO', 'title' => /*t(*/'Bucuresti'/*)*/, 'tag' => '0'),
    'RO-BH' => array('country' => 'RO', 'title' => /*t(*/'Bihor'/*)*/, 'tag' => '0'),
    'RO-BL' => array('country' => 'RO', 'title' => /*t(*/'Braila'/*)*/, 'tag' => '0'),
    'RO-BN' => array(
      'country' => 'RO',
      'title' => /*t(*/'Bistrita-Nasaud'/*)*/,
      'tag' => '0'
    ),
    'RO-BO' => array('country' => 'RO', 'title' => /*t(*/'Botosani'/*)*/, 'tag' => '0'),
    'RO-BS' => array('country' => 'RO', 'title' => /*t(*/'Brasov'/*)*/, 'tag' => '0'),
    'RO-BZ' => array('country' => 'RO', 'title' => /*t(*/'Buzau'/*)*/, 'tag' => '0'),
    'RO-CJ' => array('country' => 'RO', 'title' => /*t(*/'Cluj'/*)*/, 'tag' => '0'),
    'RO-CR' => array('country' => 'RO', 'title' => /*t(*/'Calarasi'/*)*/, 'tag' => '0'),
    'RO-CS' => array(
      'country' => 'RO',
      'title' => /*t(*/'Caras-Severin'/*)*/,
      'tag' => '0'
    ),
    'RO-CT' => array('country' => 'RO', 'title' => /*t(*/'Constanta'/*)*/, 'tag' => '0'),
    'RO-CV' => array('country' => 'RO', 'title' => /*t(*/'Covasna'/*)*/, 'tag' => '0'),
    'RO-DI' => array('country' => 'RO', 'title' => /*t(*/'Dimbovita'/*)*/, 'tag' => '0'),
    'RO-DO' => array('country' => 'RO', 'title' => /*t(*/'Dolj'/*)*/, 'tag' => '0'),
    'RO-GG' => array('country' => 'RO', 'title' => /*t(*/'Giurgiu'/*)*/, 'tag' => '0'),
    'RO-GJ' => array('country' => 'RO', 'title' => /*t(*/'Gorj'/*)*/, 'tag' => '0'),
    'RO-GL' => array('country' => 'RO', 'title' => /*t(*/'Galati'/*)*/, 'tag' => '0'),
    'RO-HA' => array('country' => 'RO', 'title' => /*t(*/'Harghita'/*)*/, 'tag' => '0'),
    'RO-HU' => array('country' => 'RO', 'title' => /*t(*/'Hunedoara'/*)*/, 'tag' => '0'),
    'RO-IF' => array('country' => 'RO', 'title' => /*t(*/'Ilfov'/*)*/, 'tag' => '0'),
    'RO-IM' => array('country' => 'RO', 'title' => /*t(*/'Ialomita'/*)*/, 'tag' => '0'),
    'RO-IS' => array('country' => 'RO', 'title' => /*t(*/'Iasi'/*)*/, 'tag' => '0'),
    'RO-MA' => array('country' => 'RO', 'title' => /*t(*/'Maramures'/*)*/, 'tag' => '0'),
    'RO-ME' => array('country' => 'RO', 'title' => /*t(*/'Mehedinti'/*)*/, 'tag' => '0'),
    'RO-MU' => array('country' => 'RO', 'title' => /*t(*/'Mures'/*)*/, 'tag' => '0'),
    'RO-NE' => array('country' => 'RO', 'title' => /*t(*/'Neamt'/*)*/, 'tag' => '0'),
    'RO-OL' => array('country' => 'RO', 'title' => /*t(*/'Olt'/*)*/, 'tag' => '0'),
    'RO-PR' => array('country' => 'RO', 'title' => /*t(*/'Prahova'/*)*/, 'tag' => '0'),
    'RO-SI' => array('country' => 'RO', 'title' => /*t(*/'Timis'/*)*/, 'tag' => '0'),
    'RO-SJ' => array('country' => 'RO', 'title' => /*t(*/'Salaj'/*)*/, 'tag' => '0'),
    'RO-SM' => array('country' => 'RO', 'title' => /*t(*/'Satu Mare'/*)*/, 'tag' => '0'),
    'RO-SO' => array('country' => 'RO', 'title' => /*t(*/'Sibiu'/*)*/, 'tag' => '0'),
    'RO-SU' => array('country' => 'RO', 'title' => /*t(*/'Suceava'/*)*/, 'tag' => '0'),
    'RO-TE' => array('country' => 'RO', 'title' => /*t(*/'Teleorman'/*)*/, 'tag' => '0'),
    'RO-TU' => array('country' => 'RO', 'title' => /*t(*/'Tulcea'/*)*/, 'tag' => '0'),
    'RO-VA' => array('country' => 'RO', 'title' => /*t(*/'Vaslui'/*)*/, 'tag' => '0'),
    'RO-VI' => array('country' => 'RO', 'title' => /*t(*/'Vilcea'/*)*/, 'tag' => '0'),
    'RO-VR' => array('country' => 'RO', 'title' => /*t(*/'Vrancea'/*)*/, 'tag' => '0'),
    'RU-AD' => array('country' => 'RU', 'title' => /*t(*/'Adygeya'/*)*/, 'tag' => '0'),
    'RU-AGB' => array(
      'country' => 'RU',
      'title' => /*t(*/'Aga Buryatia'/*)*/,
      'tag' => '0'
    ),
    'RU-AL' => array(
      'country' => 'RU',
      'title' => /*t(*/'Altai Republic'/*)*/,
      'tag' => '0'
    ),
    'RU-ALT' => array('country' => 'RU', 'title' => /*t(*/'Altai Krai'/*)*/, 'tag' => '0'),
    'RU-AMU' => array('country' => 'RU', 'title' => /*t(*/'Amur'/*)*/, 'tag' => '0'),
    'RU-ARK' => array(
      'country' => 'RU',
      'title' => /*t(*/'Arkhangelsk'/*)*/,
      'tag' => '0'
    ),
    'RU-AST' => array('country' => 'RU', 'title' => /*t(*/'Astrakhan'/*)*/, 'tag' => '0'),
    'RU-BA' => array(
      'country' => 'RU',
      'title' => /*t(*/'Bashkortostan'/*)*/,
      'tag' => '0'
    ),
    'RU-BEL' => array('country' => 'RU', 'title' => /*t(*/'Belgorod'/*)*/, 'tag' => '0'),
    'RU-BRY' => array('country' => 'RU', 'title' => /*t(*/'Bryansk'/*)*/, 'tag' => '0'),
    'RU-BU' => array('country' => 'RU', 'title' => /*t(*/'Buryatia'/*)*/, 'tag' => '0'),
    'RU-CE' => array('country' => 'RU', 'title' => /*t(*/'Chechnya'/*)*/, 'tag' => '0'),
    'RU-CHE' => array(
      'country' => 'RU',
      'title' => /*t(*/'Chelyabinsk'/*)*/,
      'tag' => '0'
    ),
    'RU-CHI' => array('country' => 'RU', 'title' => /*t(*/'Chita'/*)*/, 'tag' => '0'),
    'RU-CHU' => array('country' => 'RU', 'title' => /*t(*/'Chukotka'/*)*/, 'tag' => '0'),
    'RU-CU' => array('country' => 'RU', 'title' => /*t(*/'Chuvashia'/*)*/, 'tag' => '0'),
    'RU-DA' => array('country' => 'RU', 'title' => /*t(*/'Dagestan'/*)*/, 'tag' => '0'),
    'RU-EVE' => array('country' => 'RU', 'title' => /*t(*/'Evenkia'/*)*/, 'tag' => '0'),
    'RU-IN' => array('country' => 'RU', 'title' => /*t(*/'Ingushetia'/*)*/, 'tag' => '0'),
    'RU-IRK' => array('country' => 'RU', 'title' => /*t(*/'Irkutsk'/*)*/, 'tag' => '0'),
    'RU-IVA' => array('country' => 'RU', 'title' => /*t(*/'Ivanovo'/*)*/, 'tag' => '0'),
    'RU-KAM' => array('country' => 'RU', 'title' => /*t(*/'Kamchatka'/*)*/, 'tag' => '0'),
    'RU-KB' => array(
      'country' => 'RU',
      'title' => /*t(*/'Kabardino-Balkaria'/*)*/,
      'tag' => '0'
    ),
    'RU-KC' => array(
      'country' => 'RU',
      'title' => /*t(*/'Karachay-Cherkessia'/*)*/,
      'tag' => '0'
    ),
    'RU-KDA' => array('country' => 'RU', 'title' => /*t(*/'Krasnodar'/*)*/, 'tag' => '0'),
    'RU-KEM' => array('country' => 'RU', 'title' => /*t(*/'Kemerovo'/*)*/, 'tag' => '0'),
    'RU-KGD' => array(
      'country' => 'RU',
      'title' => /*t(*/'Kaliningrad'/*)*/,
      'tag' => '0'
    ),
    'RU-KGN' => array('country' => 'RU', 'title' => /*t(*/'Kurgan'/*)*/, 'tag' => '0'),
    'RU-KHA' => array('country' => 'RU', 'title' => /*t(*/'Khabarovsk'/*)*/, 'tag' => '0'),
    'RU-KHM' => array(
      'country' => 'RU',
      'title' => /*t(*/'Khantia-Mansia'/*)*/,
      'tag' => '0'
    ),
    'RU-KIR' => array('country' => 'RU', 'title' => /*t(*/'Kirov'/*)*/, 'tag' => '0'),
    'RU-KK' => array('country' => 'RU', 'title' => /*t(*/'Khakassia'/*)*/, 'tag' => '0'),
    'RU-KL' => array('country' => 'RU', 'title' => /*t(*/'Kalmykia'/*)*/, 'tag' => '0'),
    'RU-KLU' => array('country' => 'RU', 'title' => /*t(*/'Kaluga'/*)*/, 'tag' => '0'),
    'RU-KO' => array('country' => 'RU', 'title' => /*t(*/'Komi'/*)*/, 'tag' => '0'),
    'RU-KOP' => array('country' => 'RU', 'title' => /*t(*/'Permyakia'/*)*/, 'tag' => '0'),
    'RU-KOR' => array('country' => 'RU', 'title' => /*t(*/'Koryakia'/*)*/, 'tag' => '0'),
    'RU-KOS' => array('country' => 'RU', 'title' => /*t(*/'Kostroma'/*)*/, 'tag' => '0'),
    'RU-KR' => array('country' => 'RU', 'title' => /*t(*/'Karelia'/*)*/, 'tag' => '0'),
    'RU-KRS' => array('country' => 'RU', 'title' => /*t(*/'Kursk'/*)*/, 'tag' => '0'),
    'RU-KYA' => array(
      'country' => 'RU',
      'title' => /*t(*/'Krasnoyarsk'/*)*/,
      'tag' => '0'
    ),
    'RU-LEN' => array('country' => 'RU', 'title' => /*t(*/'Leningrad'/*)*/, 'tag' => '0'),
    'RU-LIP' => array('country' => 'RU', 'title' => /*t(*/'Lipetsk'/*)*/, 'tag' => '0'),
    'RU-MAG' => array('country' => 'RU', 'title' => /*t(*/'Magadan'/*)*/, 'tag' => '0'),
    'RU-ME' => array('country' => 'RU', 'title' => /*t(*/'Mari El'/*)*/, 'tag' => '0'),
    'RU-MO' => array('country' => 'RU', 'title' => /*t(*/'Mordovia'/*)*/, 'tag' => '0'),
    'RU-MOS' => array(
      'country' => 'RU',
      'title' => /*t(*/'Moscow (Province)'/*)*/,
      'tag' => '0'
    ),
    'RU-MOW' => array(
      'country' => 'RU',
      'title' => /*t(*/'Moscow (City)'/*)*/,
      'tag' => '0'
    ),
    'RU-MUR' => array('country' => 'RU', 'title' => /*t(*/'Murmansk'/*)*/, 'tag' => '0'),
    'RU-NEN' => array('country' => 'RU', 'title' => /*t(*/'Nenetsia'/*)*/, 'tag' => '0'),
    'RU-NGR' => array('country' => 'RU', 'title' => /*t(*/'Novgorod'/*)*/, 'tag' => '0'),
    'RU-NIZ' => array(
      'country' => 'RU',
      'title' => /*t(*/'Nizhny Novgorod'/*)*/,
      'tag' => '0'
    ),
    'RU-NVS' => array(
      'country' => 'RU',
      'title' => /*t(*/'Novosibirsk'/*)*/,
      'tag' => '0'
    ),
    'RU-OMS' => array('country' => 'RU', 'title' => /*t(*/'Omsk'/*)*/, 'tag' => '0'),
    'RU-ORE' => array('country' => 'RU', 'title' => /*t(*/'Orenburg'/*)*/, 'tag' => '0'),
    'RU-ORL' => array('country' => 'RU', 'title' => /*t(*/'Oryol'/*)*/, 'tag' => '0'),
    'RU-PER' => array('country' => 'RU', 'title' => /*t(*/'Perm'/*)*/, 'tag' => '0'),
    'RU-PNZ' => array('country' => 'RU', 'title' => /*t(*/'Penza'/*)*/, 'tag' => '0'),
    'RU-PRI' => array('country' => 'RU', 'title' => /*t(*/'Primorsky'/*)*/, 'tag' => '0'),
    'RU-PSK' => array('country' => 'RU', 'title' => /*t(*/'Pskov'/*)*/, 'tag' => '0'),
    'RU-ROS' => array('country' => 'RU', 'title' => /*t(*/'Rostov'/*)*/, 'tag' => '0'),
    'RU-RYA' => array('country' => 'RU', 'title' => /*t(*/'Ryazan'/*)*/, 'tag' => '0'),
    'RU-SA' => array('country' => 'RU', 'title' => /*t(*/'Sakha'/*)*/, 'tag' => '0'),
    'RU-SAK' => array('country' => 'RU', 'title' => /*t(*/'Sakhalin'/*)*/, 'tag' => '0'),
    'RU-SAM' => array('country' => 'RU', 'title' => /*t(*/'Samara'/*)*/, 'tag' => '0'),
    'RU-SAR' => array('country' => 'RU', 'title' => /*t(*/'Saratov'/*)*/, 'tag' => '0'),
    'RU-SE' => array(
      'country' => 'RU',
      'title' => /*t(*/'North Ossetia'/*)*/,
      'tag' => '0'
    ),
    'RU-SMO' => array('country' => 'RU', 'title' => /*t(*/'Smolensk'/*)*/, 'tag' => '0'),
    'RU-SPE' => array(
      'country' => 'RU',
      'title' => /*t(*/'St. Petersburg'/*)*/,
      'tag' => '0'
    ),
    'RU-STA' => array('country' => 'RU', 'title' => /*t(*/'Stavropol'/*)*/, 'tag' => '0'),
    'RU-SVE' => array('country' => 'RU', 'title' => /*t(*/'Sverdlovsk'/*)*/, 'tag' => '0'),
    'RU-TA' => array('country' => 'RU', 'title' => /*t(*/'Tatarstan'/*)*/, 'tag' => '0'),
    'RU-TAM' => array('country' => 'RU', 'title' => /*t(*/'Tambov'/*)*/, 'tag' => '0'),
    'RU-TAY' => array('country' => 'RU', 'title' => /*t(*/'Taymyria'/*)*/, 'tag' => '0'),
    'RU-TOM' => array('country' => 'RU', 'title' => /*t(*/'Tomsk'/*)*/, 'tag' => '0'),
    'RU-TUL' => array('country' => 'RU', 'title' => /*t(*/'Tula'/*)*/, 'tag' => '0'),
    'RU-TVE' => array('country' => 'RU', 'title' => /*t(*/'Tver'/*)*/, 'tag' => '0'),
    'RU-TY' => array('country' => 'RU', 'title' => /*t(*/'Tuva'/*)*/, 'tag' => '0'),
    'RU-TYU' => array('country' => 'RU', 'title' => /*t(*/'Tyumen'/*)*/, 'tag' => '0'),
    'RU-UD' => array('country' => 'RU', 'title' => /*t(*/'Udmurtia'/*)*/, 'tag' => '0'),
    'RU-ULY' => array('country' => 'RU', 'title' => /*t(*/'Ulynovsk'/*)*/, 'tag' => '0'),
    'RU-UOB' => array(
      'country' => 'RU',
      'title' => /*t(*/'Ust-Orda Buryatia'/*)*/,
      'tag' => '0'
    ),
    'RU-VGG' => array('country' => 'RU', 'title' => /*t(*/'Volgograd'/*)*/, 'tag' => '0'),
    'RU-VLA' => array('country' => 'RU', 'title' => /*t(*/'Vladimir'/*)*/, 'tag' => '0'),
    'RU-VLG' => array('country' => 'RU', 'title' => /*t(*/'Vologda'/*)*/, 'tag' => '0'),
    'RU-VOR' => array('country' => 'RU', 'title' => /*t(*/'Voronezh'/*)*/, 'tag' => '0'),
    'RU-YAN' => array('country' => 'RU', 'title' => /*t(*/'Yamalia'/*)*/, 'tag' => '0'),
    'RU-YAR' => array('country' => 'RU', 'title' => /*t(*/'Yaroslavl'/*)*/, 'tag' => '0'),
    'RU-YEV' => array(
      'country' => 'RU',
      'title' => /*t(*/'Jewish Oblast'/*)*/,
      'tag' => '0'
    ),
    'RW-BU' => array('country' => 'RW', 'title' => /*t(*/'Butare'/*)*/, 'tag' => '0'),
    'RW-BY' => array('country' => 'RW', 'title' => /*t(*/'Byumba'/*)*/, 'tag' => '0'),
    'RW-CY' => array('country' => 'RW', 'title' => /*t(*/'Cyangugu'/*)*/, 'tag' => '0'),
    'RW-GK' => array('country' => 'RW', 'title' => /*t(*/'Gikongoro'/*)*/, 'tag' => '0'),
    'RW-GS' => array('country' => 'RW', 'title' => /*t(*/'Gisenyi'/*)*/, 'tag' => '0'),
    'RW-GT' => array('country' => 'RW', 'title' => /*t(*/'Gitarama'/*)*/, 'tag' => '0'),
    'RW-KG' => array('country' => 'RW', 'title' => /*t(*/'Kibungo'/*)*/, 'tag' => '0'),
    'RW-KR' => array(
      'country' => 'RW',
      'title' => /*t(*/'Kigali Rurale'/*)*/,
      'tag' => '0'
    ),
    'RW-KV' => array(
      'country' => 'RW',
      'title' => /*t(*/'Kigali-ville'/*)*/,
      'tag' => '0'
    ),
    'RW-KY' => array('country' => 'RW', 'title' => /*t(*/'Kibuye'/*)*/, 'tag' => '0'),
    'RW-RU' => array('country' => 'RW', 'title' => /*t(*/'Ruhengeri'/*)*/, 'tag' => '0'),
    'RW-UM' => array('country' => 'RW', 'title' => /*t(*/'Umutara'/*)*/, 'tag' => '0'),
    'SA-AQ' => array(
      'country' => 'SA',
      'title' => /*t(*/'Ash Sharqiyah (Eastern Province)'/*)*/,
      'tag' => '0'
    ),
    'SA-AS' => array('country' => 'SA', 'title' => /*t(*/"'Asir"/*)*/, 'tag' => '0'),
    'SA-BH' => array('country' => 'SA', 'title' => /*t(*/'Al Bahah'/*)*/, 'tag' => '0'),
    'SA-HL' => array('country' => 'SA', 'title' => /*t(*/"Ha'il"/*)*/, 'tag' => '0'),
    'SA-HS' => array(
      'country' => 'SA',
      'title' => /*t(*/'Al Hudud ash Shamaliyah'/*)*/,
      'tag' => '0'
    ),
    'SA-JF' => array('country' => 'SA', 'title' => /*t(*/'Al Jawf'/*)*/, 'tag' => '0'),
    'SA-JZ' => array('country' => 'SA', 'title' => /*t(*/'Jizan'/*)*/, 'tag' => '0'),
    'SA-MD' => array('country' => 'SA', 'title' => /*t(*/'Al Madinah'/*)*/, 'tag' => '0'),
    'SA-ML' => array('country' => 'SA', 'title' => /*t(*/'Makkah'/*)*/, 'tag' => '0'),
    'SA-NR' => array('country' => 'SA', 'title' => /*t(*/'Najran'/*)*/, 'tag' => '0'),
    'SA-QS' => array('country' => 'SA', 'title' => /*t(*/'Al Qasim'/*)*/, 'tag' => '0'),
    'SA-RD' => array('country' => 'SA', 'title' => /*t(*/'Ar Riyad'/*)*/, 'tag' => '0'),
    'SA-TB' => array('country' => 'SA', 'title' => /*t(*/'Tabuk'/*)*/, 'tag' => '0'),
    'SB-CE' => array('country' => 'SB', 'title' => /*t(*/'Central'/*)*/, 'tag' => '0'),
    'SB-CH' => array('country' => 'SB', 'title' => /*t(*/'Choiseul'/*)*/, 'tag' => '0'),
    'SB-GC' => array('country' => 'SB', 'title' => /*t(*/'Guadalcanal'/*)*/, 'tag' => '0'),
    'SB-HO' => array('country' => 'SB', 'title' => /*t(*/'Honiara'/*)*/, 'tag' => '0'),
    'SB-IS' => array('country' => 'SB', 'title' => /*t(*/'Isabel'/*)*/, 'tag' => '0'),
    'SB-MK' => array('country' => 'SB', 'title' => /*t(*/'Makira'/*)*/, 'tag' => '0'),
    'SB-ML' => array('country' => 'SB', 'title' => /*t(*/'Malaita'/*)*/, 'tag' => '0'),
    'SB-RB' => array(
      'country' => 'SB',
      'title' => /*t(*/'Rennell and Bellona'/*)*/,
      'tag' => '0'
    ),
    'SB-TM' => array('country' => 'SB', 'title' => /*t(*/'Temotu'/*)*/, 'tag' => '0'),
    'SB-WE' => array('country' => 'SB', 'title' => /*t(*/'Western'/*)*/, 'tag' => '0'),
    'SC-AB' => array(
      'country' => 'SC',
      'title' => /*t(*/'Anse Boileau'/*)*/,
      'tag' => '0'
    ),
    'SC-AE' => array('country' => 'SC', 'title' => /*t(*/'Anse Etoile'/*)*/, 'tag' => '0'),
    'SC-AL' => array('country' => 'SC', 'title' => /*t(*/'Anse Louis'/*)*/, 'tag' => '0'),
    'SC-AP' => array(
      'country' => 'SC',
      'title' => /*t(*/'Anse aux Pins'/*)*/,
      'tag' => '0'
    ),
    'SC-AR' => array('country' => 'SC', 'title' => /*t(*/'Anse Royale'/*)*/, 'tag' => '0'),
    'SC-BA' => array('country' => 'SC', 'title' => /*t(*/'Bel Air'/*)*/, 'tag' => '0'),
    'SC-BL' => array('country' => 'SC', 'title' => /*t(*/'Baie Lazare'/*)*/, 'tag' => '0'),
    'SC-BO' => array('country' => 'SC', 'title' => /*t(*/'Bel Ombre'/*)*/, 'tag' => '0'),
    'SC-BS' => array(
      'country' => 'SC',
      'title' => /*t(*/'Baie Sainte Anne'/*)*/,
      'tag' => '0'
    ),
    'SC-BV' => array('country' => 'SC', 'title' => /*t(*/'Beau Vallon'/*)*/, 'tag' => '0'),
    'SC-CA' => array('country' => 'SC', 'title' => /*t(*/'Cascade'/*)*/, 'tag' => '0'),
    'SC-DG' => array('country' => 'SC', 'title' => /*t(*/'La Digue'/*)*/, 'tag' => '0'),
    'SC-GL' => array('country' => 'SC', 'title' => /*t(*/'Glacis'/*)*/, 'tag' => '0'),
    'SC-GM' => array(
      'country' => 'SC',
      'title' => /*t(*/"Grand' Anse (on Mahe)"/*)*/,
      'tag' => '0'
    ),
    'SC-GP' => array(
      'country' => 'SC',
      'title' => /*t(*/"Grand' Anse (on Praslin)"/*)*/,
      'tag' => '0'
    ),
    'SC-MB' => array('country' => 'SC', 'title' => /*t(*/'Mont Buxton'/*)*/, 'tag' => '0'),
    'SC-MF' => array('country' => 'SC', 'title' => /*t(*/'Mont Fleuri'/*)*/, 'tag' => '0'),
    'SC-PG' => array('country' => 'SC', 'title' => /*t(*/'Port Glaud'/*)*/, 'tag' => '0'),
    'SC-PL' => array('country' => 'SC', 'title' => /*t(*/'Plaisance'/*)*/, 'tag' => '0'),
    'SC-PR' => array(
      'country' => 'SC',
      'title' => /*t(*/'Pointe La Rue'/*)*/,
      'tag' => '0'
    ),
    'SC-RA' => array(
      'country' => 'SC',
      'title' => /*t(*/'La Riviere Anglaise'/*)*/,
      'tag' => '0'
    ),
    'SC-SL' => array('country' => 'SC', 'title' => /*t(*/'Saint Louis'/*)*/, 'tag' => '0'),
    'SC-TA' => array('country' => 'SC', 'title' => /*t(*/'Takamaka'/*)*/, 'tag' => '0'),
    'SD-ANB' => array(
      'country' => 'SD',
      'title' => /*t(*/'An Nil al Abyad'/*)*/,
      'tag' => '0'
    ),
    'SD-ANL' => array(
      'country' => 'SD',
      'title' => /*t(*/"A'ali an Nil"/*)*/,
      'tag' => '0'
    ),
    'SD-ANZ' => array(
      'country' => 'SD',
      'title' => /*t(*/'An Nil al Azraq'/*)*/,
      'tag' => '0'
    ),
    'SD-ASH' => array(
      'country' => 'SD',
      'title' => /*t(*/'Ash Shamaliyah'/*)*/,
      'tag' => '0'
    ),
    'SD-BAM' => array(
      'country' => 'SD',
      'title' => /*t(*/'Al Bahr al Ahmar'/*)*/,
      'tag' => '0'
    ),
    'SD-BJA' => array(
      'country' => 'SD',
      'title' => /*t(*/'Bahr al Jabal'/*)*/,
      'tag' => '0'
    ),
    'SD-BRT' => array(
      'country' => 'SD',
      'title' => /*t(*/'Al Buhayrat'/*)*/,
      'tag' => '0'
    ),
    'SD-GBG' => array(
      'country' => 'SD',
      'title' => /*t(*/'Gharb Bahr al Ghazal'/*)*/,
      'tag' => '0'
    ),
    'SD-GDA' => array(
      'country' => 'SD',
      'title' => /*t(*/'Gharb Darfur'/*)*/,
      'tag' => '0'
    ),
    'SD-GIS' => array(
      'country' => 'SD',
      'title' => /*t(*/"Gharb al Istiwa'iyah"/*)*/,
      'tag' => '0'
    ),
    'SD-GKU' => array(
      'country' => 'SD',
      'title' => /*t(*/'Gharb Kurdufan'/*)*/,
      'tag' => '0'
    ),
    'SD-JDA' => array(
      'country' => 'SD',
      'title' => /*t(*/'Janub Darfur'/*)*/,
      'tag' => '0'
    ),
    'SD-JKU' => array(
      'country' => 'SD',
      'title' => /*t(*/'Janub Kurdufan'/*)*/,
      'tag' => '0'
    ),
    'SD-JQL' => array('country' => 'SD', 'title' => /*t(*/'Junqali'/*)*/, 'tag' => '0'),
    'SD-JZR' => array('country' => 'SD', 'title' => /*t(*/'Al Jazirah'/*)*/, 'tag' => '0'),
    'SD-KRT' => array('country' => 'SD', 'title' => /*t(*/'Al Khartum'/*)*/, 'tag' => '0'),
    'SD-KSL' => array('country' => 'SD', 'title' => /*t(*/'Kassala'/*)*/, 'tag' => '0'),
    'SD-NNL' => array(
      'country' => 'SD',
      'title' => /*t(*/'Nahr an Nil'/*)*/,
      'tag' => '0'
    ),
    'SD-QDR' => array('country' => 'SD', 'title' => /*t(*/'Al Qadarif'/*)*/, 'tag' => '0'),
    'SD-SBG' => array(
      'country' => 'SD',
      'title' => /*t(*/'Shamal Bahr al Ghazal'/*)*/,
      'tag' => '0'
    ),
    'SD-SDA' => array(
      'country' => 'SD',
      'title' => /*t(*/'Shamal Darfur'/*)*/,
      'tag' => '0'
    ),
    'SD-SIS' => array(
      'country' => 'SD',
      'title' => /*t(*/"Sharq al Istiwa'iyah"/*)*/,
      'tag' => '0'
    ),
    'SD-SKU' => array(
      'country' => 'SD',
      'title' => /*t(*/'Shamal Kurdufan'/*)*/,
      'tag' => '0'
    ),
    'SD-SNR' => array('country' => 'SD', 'title' => /*t(*/'Sinnar'/*)*/, 'tag' => '0'),
    'SD-WDH' => array('country' => 'SD', 'title' => /*t(*/'Al Wahdah'/*)*/, 'tag' => '0'),
    'SD-WRB' => array('country' => 'SD', 'title' => /*t(*/'Warab'/*)*/, 'tag' => '0'),
    'SH-A' => array('country' => 'SH', 'title' => /*t(*/'Ascension'/*)*/, 'tag' => '0'),
    'SH-S' => array('country' => 'SH', 'title' => /*t(*/'Saint Helena'/*)*/, 'tag' => '0'),
    'SH-T' => array(
      'country' => 'SH',
      'title' => /*t(*/'Tristan da Cunha'/*)*/,
      'tag' => '0'
    ),
    'SK-BA' => array(
      'country' => 'SK',
      'title' => /*t(*/'Banskobystricky'/*)*/,
      'tag' => '0'
    ),
    'SK-BR' => array(
      'country' => 'SK',
      'title' => /*t(*/'Bratislavsky'/*)*/,
      'tag' => '0'
    ),
    'SK-KO' => array('country' => 'SK', 'title' => /*t(*/'Kosicky'/*)*/, 'tag' => '0'),
    'SK-NI' => array('country' => 'SK', 'title' => /*t(*/'Nitriansky'/*)*/, 'tag' => '0'),
    'SK-PR' => array('country' => 'SK', 'title' => /*t(*/'Presovsky'/*)*/, 'tag' => '0'),
    'SK-TC' => array('country' => 'SK', 'title' => /*t(*/'Trenciansky'/*)*/, 'tag' => '0'),
    'SK-TV' => array('country' => 'SK', 'title' => /*t(*/'Trnavsky'/*)*/, 'tag' => '0'),
    'SK-ZI' => array('country' => 'SK', 'title' => /*t(*/'Zilinsky'/*)*/, 'tag' => '0'),
    'SL-E' => array('country' => 'SL', 'title' => /*t(*/'Eastern'/*)*/, 'tag' => '0'),
    'SL-N' => array('country' => 'SL', 'title' => /*t(*/'Northern'/*)*/, 'tag' => '0'),
    'SL-S' => array('country' => 'SL', 'title' => /*t(*/'Southern'/*)*/, 'tag' => '0'),
    'SL-W' => array('country' => 'SL', 'title' => /*t(*/'Western'/*)*/, 'tag' => '0'),
    'SM-AC' => array('country' => 'SM', 'title' => /*t(*/'Acquaviva'/*)*/, 'tag' => '0'),
    'SM-BM' => array(
      'country' => 'SM',
      'title' => /*t(*/'Borgo Maggiore'/*)*/,
      'tag' => '0'
    ),
    'SM-CH' => array('country' => 'SM', 'title' => /*t(*/'Chiesanuova'/*)*/, 'tag' => '0'),
    'SM-DO' => array('country' => 'SM', 'title' => /*t(*/'Domagnano'/*)*/, 'tag' => '0'),
    'SM-FA' => array('country' => 'SM', 'title' => /*t(*/'Faetano'/*)*/, 'tag' => '0'),
    'SM-FI' => array('country' => 'SM', 'title' => /*t(*/'Fiorentino'/*)*/, 'tag' => '0'),
    'SM-MO' => array(
      'country' => 'SM',
      'title' => /*t(*/'Montegiardino'/*)*/,
      'tag' => '0'
    ),
    'SM-SE' => array('country' => 'SM', 'title' => /*t(*/'Serravalle'/*)*/, 'tag' => '0'),
    'SM-SM' => array(
      'country' => 'SM',
      'title' => /*t(*/'Citta di San Marino'/*)*/,
      'tag' => '0'
    ),
    'SN-DA' => array('country' => 'SN', 'title' => /*t(*/'Dakar'/*)*/, 'tag' => '0'),
    'SN-DI' => array('country' => 'SN', 'title' => /*t(*/'Diourbel'/*)*/, 'tag' => '0'),
    'SN-FA' => array('country' => 'SN', 'title' => /*t(*/'Fatick'/*)*/, 'tag' => '0'),
    'SN-KA' => array('country' => 'SN', 'title' => /*t(*/'Kaolack'/*)*/, 'tag' => '0'),
    'SN-KO' => array('country' => 'SN', 'title' => /*t(*/'Kolda'/*)*/, 'tag' => '0'),
    'SN-LO' => array('country' => 'SN', 'title' => /*t(*/'Louga'/*)*/, 'tag' => '0'),
    'SN-MA' => array('country' => 'SN', 'title' => /*t(*/'Matam'/*)*/, 'tag' => '0'),
    'SN-SL' => array('country' => 'SN', 'title' => /*t(*/'Saint-Louis'/*)*/, 'tag' => '0'),
    'SN-TA' => array('country' => 'SN', 'title' => /*t(*/'Tambacounda'/*)*/, 'tag' => '0'),
    'SN-TH' => array('country' => 'SN', 'title' => /*t(*/'Thies'/*)*/, 'tag' => '0'),
    'SN-ZI' => array('country' => 'SN', 'title' => /*t(*/'Ziguinchor'/*)*/, 'tag' => '0'),
    'SO-AW' => array('country' => 'SO', 'title' => /*t(*/'Awdal'/*)*/, 'tag' => '0'),
    'SO-BK' => array('country' => 'SO', 'title' => /*t(*/'Bakool'/*)*/, 'tag' => '0'),
    'SO-BN' => array('country' => 'SO', 'title' => /*t(*/'Banaadir'/*)*/, 'tag' => '0'),
    'SO-BR' => array('country' => 'SO', 'title' => /*t(*/'Bari'/*)*/, 'tag' => '0'),
    'SO-BY' => array('country' => 'SO', 'title' => /*t(*/'Bay'/*)*/, 'tag' => '0'),
    'SO-GA' => array('country' => 'SO', 'title' => /*t(*/'Galguduud'/*)*/, 'tag' => '0'),
    'SO-GE' => array('country' => 'SO', 'title' => /*t(*/'Gedo'/*)*/, 'tag' => '0'),
    'SO-HI' => array('country' => 'SO', 'title' => /*t(*/'Hiiraan'/*)*/, 'tag' => '0'),
    'SO-JD' => array(
      'country' => 'SO',
      'title' => /*t(*/'Jubbada Dhexe'/*)*/,
      'tag' => '0'
    ),
    'SO-JH' => array(
      'country' => 'SO',
      'title' => /*t(*/'Jubbada Hoose'/*)*/,
      'tag' => '0'
    ),
    'SO-MU' => array('country' => 'SO', 'title' => /*t(*/'Mudug'/*)*/, 'tag' => '0'),
    'SO-NU' => array('country' => 'SO', 'title' => /*t(*/'Nugaal'/*)*/, 'tag' => '0'),
    'SO-SA' => array('country' => 'SO', 'title' => /*t(*/'Sanaag'/*)*/, 'tag' => '0'),
    'SO-SD' => array(
      'country' => 'SO',
      'title' => /*t(*/'Shabeellaha Dhexe'/*)*/,
      'tag' => '0'
    ),
    'SO-SH' => array(
      'country' => 'SO',
      'title' => /*t(*/'Shabeellaha Hoose'/*)*/,
      'tag' => '0'
    ),
    'SO-SL' => array('country' => 'SO', 'title' => /*t(*/'Sool'/*)*/, 'tag' => '0'),
    'SO-TO' => array('country' => 'SO', 'title' => /*t(*/'Togdheer'/*)*/, 'tag' => '0'),
    'SO-WG' => array(
      'country' => 'SO',
      'title' => /*t(*/'Woqooyi Galbeed'/*)*/,
      'tag' => '0'
    ),
    'SR-BR' => array('country' => 'SR', 'title' => /*t(*/'Brokopondo'/*)*/, 'tag' => '0'),
    'SR-CM' => array('country' => 'SR', 'title' => /*t(*/'Commewijne'/*)*/, 'tag' => '0'),
    'SR-CR' => array('country' => 'SR', 'title' => /*t(*/'Coronie'/*)*/, 'tag' => '0'),
    'SR-MA' => array('country' => 'SR', 'title' => /*t(*/'Marowijne'/*)*/, 'tag' => '0'),
    'SR-NI' => array('country' => 'SR', 'title' => /*t(*/'Nickerie'/*)*/, 'tag' => '0'),
    'SR-PA' => array('country' => 'SR', 'title' => /*t(*/'Para'/*)*/, 'tag' => '0'),
    'SR-PM' => array('country' => 'SR', 'title' => /*t(*/'Paramaribo'/*)*/, 'tag' => '0'),
    'SR-SA' => array('country' => 'SR', 'title' => /*t(*/'Saramacca'/*)*/, 'tag' => '0'),
    'SR-SI' => array('country' => 'SR', 'title' => /*t(*/'Sipaliwini'/*)*/, 'tag' => '0'),
    'SR-WA' => array('country' => 'SR', 'title' => /*t(*/'Wanica'/*)*/, 'tag' => '0'),
    'ST-P' => array('country' => 'ST', 'title' => /*t(*/'Principe'/*)*/, 'tag' => '0'),
    'ST-S' => array('country' => 'ST', 'title' => /*t(*/'Sao Tome'/*)*/, 'tag' => '0'),
    'SV-AH' => array('country' => 'SV', 'title' => /*t(*/'Ahuachapan'/*)*/, 'tag' => '0'),
    'SV-CA' => array('country' => 'SV', 'title' => /*t(*/'Cabanas'/*)*/, 'tag' => '0'),
    'SV-CH' => array(
      'country' => 'SV',
      'title' => /*t(*/'Chalatenango'/*)*/,
      'tag' => '0'
    ),
    'SV-CU' => array('country' => 'SV', 'title' => /*t(*/'Cuscatlan'/*)*/, 'tag' => '0'),
    'SV-LB' => array('country' => 'SV', 'title' => /*t(*/'La Libertad'/*)*/, 'tag' => '0'),
    'SV-MO' => array('country' => 'SV', 'title' => /*t(*/'Morazan'/*)*/, 'tag' => '0'),
    'SV-PZ' => array('country' => 'SV', 'title' => /*t(*/'La Paz'/*)*/, 'tag' => '0'),
    'SV-SA' => array('country' => 'SV', 'title' => /*t(*/'Santa Ana'/*)*/, 'tag' => '0'),
    'SV-SM' => array('country' => 'SV', 'title' => /*t(*/'San Miguel'/*)*/, 'tag' => '0'),
    'SV-SO' => array('country' => 'SV', 'title' => /*t(*/'Sonsonate'/*)*/, 'tag' => '0'),
    'SV-SS' => array(
      'country' => 'SV',
      'title' => /*t(*/'San Salvador'/*)*/,
      'tag' => '0'
    ),
    'SV-SV' => array('country' => 'SV', 'title' => /*t(*/'San Vicente'/*)*/, 'tag' => '0'),
    'SV-UN' => array('country' => 'SV', 'title' => /*t(*/'La Union'/*)*/, 'tag' => '0'),
    'SV-US' => array('country' => 'SV', 'title' => /*t(*/'Usulutan'/*)*/, 'tag' => '0'),
    'SW-BL' => array('country' => 'SW', 'title' => /*t(*/'Blekinge'/*)*/, 'tag' => '0'),
    'SW-DA' => array('country' => 'SW', 'title' => /*t(*/'Dalama'/*)*/, 'tag' => '0'),
    'SW-GA' => array('country' => 'SW', 'title' => /*t(*/'Gavleborg'/*)*/, 'tag' => '0'),
    'SW-GO' => array('country' => 'SW', 'title' => /*t(*/'Gotland'/*)*/, 'tag' => '0'),
    'SW-HA' => array('country' => 'SW', 'title' => /*t(*/'Halland'/*)*/, 'tag' => '0'),
    'SW-JA' => array('country' => 'SW', 'title' => /*t(*/'Jamtland'/*)*/, 'tag' => '0'),
    'SW-JO' => array('country' => 'SW', 'title' => /*t(*/'Jonkping'/*)*/, 'tag' => '0'),
    'SW-KA' => array('country' => 'SW', 'title' => /*t(*/'Kalmar'/*)*/, 'tag' => '0'),
    'SW-KR' => array('country' => 'SW', 'title' => /*t(*/'Kronoberg'/*)*/, 'tag' => '0'),
    'SW-NO' => array('country' => 'SW', 'title' => /*t(*/'Norrbotten'/*)*/, 'tag' => '0'),
    'SW-OG' => array(
      'country' => 'SW',
      'title' => /*t(*/'Ostergotland'/*)*/,
      'tag' => '0'
    ),
    'SW-OR' => array('country' => 'SW', 'title' => /*t(*/'Orebro'/*)*/, 'tag' => '0'),
    'SW-SK' => array('country' => 'SW', 'title' => /*t(*/'Skane'/*)*/, 'tag' => '0'),
    'SW-SO' => array(
      'country' => 'SW',
      'title' => /*t(*/'Sodermanland'/*)*/,
      'tag' => '0'
    ),
    'SW-ST' => array('country' => 'SW', 'title' => /*t(*/'Stockholm'/*)*/, 'tag' => '0'),
    'SW-UP' => array('country' => 'SW', 'title' => /*t(*/'Uppdala'/*)*/, 'tag' => '0'),
    'SW-VB' => array(
      'country' => 'SW',
      'title' => /*t(*/'Vasterbotten'/*)*/,
      'tag' => '0'
    ),
    'SW-VG' => array(
      'country' => 'SW',
      'title' => /*t(*/'Vastra Gotaland'/*)*/,
      'tag' => '0'
    ),
    'SW-VL' => array('country' => 'SW', 'title' => /*t(*/'Varmland'/*)*/, 'tag' => '0'),
    'SW-VM' => array('country' => 'SW', 'title' => /*t(*/'Vastmanland'/*)*/, 'tag' => '0'),
    'SW-VN' => array(
      'country' => 'SW',
      'title' => /*t(*/'Vasternorrland'/*)*/,
      'tag' => '0'
    ),
    'SY-DA' => array('country' => 'SY', 'title' => /*t(*/'Dara'/*)*/, 'tag' => '0'),
    'SY-DI' => array('country' => 'SY', 'title' => /*t(*/'Dimashq'/*)*/, 'tag' => '0'),
    'SY-DZ' => array(
      'country' => 'SY',
      'title' => /*t(*/'Dayr az Zawr'/*)*/,
      'tag' => '0'
    ),
    'SY-HA' => array('country' => 'SY', 'title' => /*t(*/'Al Hasakah'/*)*/, 'tag' => '0'),
    'SY-HI' => array('country' => 'SY', 'title' => /*t(*/'Hims'/*)*/, 'tag' => '0'),
    'SY-HL' => array('country' => 'SY', 'title' => /*t(*/'Halab'/*)*/, 'tag' => '0'),
    'SY-HM' => array('country' => 'SY', 'title' => /*t(*/'Hamah'/*)*/, 'tag' => '0'),
    'SY-ID' => array('country' => 'SY', 'title' => /*t(*/'Idlib'/*)*/, 'tag' => '0'),
    'SY-LA' => array(
      'country' => 'SY',
      'title' => /*t(*/'Al Ladhiqiyah'/*)*/,
      'tag' => '0'
    ),
    'SY-QU' => array(
      'country' => 'SY',
      'title' => /*t(*/'Al Qunaytirah'/*)*/,
      'tag' => '0'
    ),
    'SY-RD' => array('country' => 'SY', 'title' => /*t(*/'Rif Dimashq'/*)*/, 'tag' => '0'),
    'SY-RQ' => array('country' => 'SY', 'title' => /*t(*/'Ar Raqqah'/*)*/, 'tag' => '0'),
    'SY-SU' => array('country' => 'SY', 'title' => /*t(*/'As Suwayda'/*)*/, 'tag' => '0'),
    'SY-TA' => array('country' => 'SY', 'title' => /*t(*/'Tartus'/*)*/, 'tag' => '0'),
    'SZ-H' => array('country' => 'SZ', 'title' => /*t(*/'Hhohho'/*)*/, 'tag' => '0'),
    'SZ-L' => array('country' => 'SZ', 'title' => /*t(*/'Lubombo'/*)*/, 'tag' => '0'),
    'SZ-M' => array('country' => 'SZ', 'title' => /*t(*/'Manzini'/*)*/, 'tag' => '0'),
    'SZ-S' => array('country' => 'SZ', 'title' => /*t(*/'Shishelweni'/*)*/, 'tag' => '0'),
    'TC-AC' => array(
      'country' => 'TC',
      'title' => /*t(*/'Ambergris Cays'/*)*/,
      'tag' => '0'
    ),
    'TC-DC' => array('country' => 'TC', 'title' => /*t(*/'Dellis Cay'/*)*/, 'tag' => '0'),
    'TC-EC' => array('country' => 'TC', 'title' => /*t(*/'East Caicos'/*)*/, 'tag' => '0'),
    'TC-FC' => array('country' => 'TC', 'title' => /*t(*/'French Cay'/*)*/, 'tag' => '0'),
    'TC-GT' => array('country' => 'TC', 'title' => /*t(*/'Grand Turk'/*)*/, 'tag' => '0'),
    'TC-LW' => array(
      'country' => 'TC',
      'title' => /*t(*/'Little Water Cay'/*)*/,
      'tag' => '0'
    ),
    'TC-MC' => array(
      'country' => 'TC',
      'title' => /*t(*/'Middle Caicos'/*)*/,
      'tag' => '0'
    ),
    'TC-NC' => array(
      'country' => 'TC',
      'title' => /*t(*/'North Caicos'/*)*/,
      'tag' => '0'
    ),
    'TC-PN' => array('country' => 'TC', 'title' => /*t(*/'Pine Cay'/*)*/, 'tag' => '0'),
    'TC-PR' => array(
      'country' => 'TC',
      'title' => /*t(*/'Providenciales'/*)*/,
      'tag' => '0'
    ),
    'TC-RC' => array('country' => 'TC', 'title' => /*t(*/'Parrot Cay'/*)*/, 'tag' => '0'),
    'TC-SC' => array(
      'country' => 'TC',
      'title' => /*t(*/'South Caicos'/*)*/,
      'tag' => '0'
    ),
    'TC-SL' => array('country' => 'TC', 'title' => /*t(*/'Salt Cay'/*)*/, 'tag' => '0'),
    'TC-WC' => array('country' => 'TC', 'title' => /*t(*/'West Caicos'/*)*/, 'tag' => '0'),
    'TD-BA' => array('country' => 'TD', 'title' => /*t(*/'Batha'/*)*/, 'tag' => '0'),
    'TD-BE' => array(
      'country' => 'TD',
      'title' => /*t(*/'Borkou-Ennedi-Tibesti'/*)*/,
      'tag' => '0'
    ),
    'TD-BI' => array('country' => 'TD', 'title' => /*t(*/'Biltine'/*)*/, 'tag' => '0'),
    'TD-CB' => array(
      'country' => 'TD',
      'title' => /*t(*/'Chari-Baguirmi'/*)*/,
      'tag' => '0'
    ),
    'TD-GU' => array('country' => 'TD', 'title' => /*t(*/'Guera'/*)*/, 'tag' => '0'),
    'TD-KA' => array('country' => 'TD', 'title' => /*t(*/'Kanem'/*)*/, 'tag' => '0'),
    'TD-LA' => array('country' => 'TD', 'title' => /*t(*/'Lac'/*)*/, 'tag' => '0'),
    'TD-LC' => array(
      'country' => 'TD',
      'title' => /*t(*/'Logone Occidental'/*)*/,
      'tag' => '0'
    ),
    'TD-LR' => array(
      'country' => 'TD',
      'title' => /*t(*/'Logone Oriental'/*)*/,
      'tag' => '0'
    ),
    'TD-MC' => array('country' => 'TD', 'title' => /*t(*/'Moyen-Chari'/*)*/, 'tag' => '0'),
    'TD-MK' => array('country' => 'TD', 'title' => /*t(*/'Mayo-Kebbi'/*)*/, 'tag' => '0'),
    'TD-OU' => array('country' => 'TD', 'title' => /*t(*/'Ouaddai'/*)*/, 'tag' => '0'),
    'TD-SA' => array('country' => 'TD', 'title' => /*t(*/'Salamat'/*)*/, 'tag' => '0'),
    'TD-TA' => array('country' => 'TD', 'title' => /*t(*/'Tandjile'/*)*/, 'tag' => '0'),
    'TF-A' => array(
      'country' => 'TF',
      'title' => /*t(*/'Ile Amsterdam'/*)*/,
      'tag' => '0'
    ),
    'TF-C' => array('country' => 'TF', 'title' => /*t(*/'Iles Crozet'/*)*/, 'tag' => '0'),
    'TF-D' => array('country' => 'TF', 'title' => /*t(*/'Adelie Land'/*)*/, 'tag' => '0'),
    'TF-K' => array(
      'country' => 'TF',
      'title' => /*t(*/'Iles Kerguelen'/*)*/,
      'tag' => '0'
    ),
    'TF-P' => array(
      'country' => 'TF',
      'title' => /*t(*/'Ile Saint-Paul'/*)*/,
      'tag' => '0'
    ),
    'TG-C' => array('country' => 'TG', 'title' => /*t(*/'Centrale'/*)*/, 'tag' => '0'),
    'TG-K' => array('country' => 'TG', 'title' => /*t(*/'Kara'/*)*/, 'tag' => '0'),
    'TG-M' => array('country' => 'TG', 'title' => /*t(*/'Maritime'/*)*/, 'tag' => '0'),
    'TG-P' => array('country' => 'TG', 'title' => /*t(*/'Plateaux'/*)*/, 'tag' => '0'),
    'TG-S' => array('country' => 'TG', 'title' => /*t(*/'Savanes'/*)*/, 'tag' => '0'),
    'TH-10' => array('country' => 'TH', 'title' => /*t(*/'Bangkok'/*)*/, 'tag' => '0'),
    'TH-11' => array(
      'country' => 'TH',
      'title' => /*t(*/'Samut Prakan'/*)*/,
      'tag' => '0'
    ),
    'TH-12' => array('country' => 'TH', 'title' => /*t(*/'Nonthaburi'/*)*/, 'tag' => '0'),
    'TH-13' => array(
      'country' => 'TH',
      'title' => /*t(*/'Pathum Thani'/*)*/,
      'tag' => '0'
    ),
    'TH-14' => array(
      'country' => 'TH',
      'title' => /*t(*/'Phra Nakhon Si Ayutthaya'/*)*/,
      'tag' => '0'
    ),
    'TH-15' => array('country' => 'TH', 'title' => /*t(*/'Ang Thong'/*)*/, 'tag' => '0'),
    'TH-16' => array('country' => 'TH', 'title' => /*t(*/'Lop Buri'/*)*/, 'tag' => '0'),
    'TH-17' => array('country' => 'TH', 'title' => /*t(*/'Sing Buri'/*)*/, 'tag' => '0'),
    'TH-18' => array('country' => 'TH', 'title' => /*t(*/'Chai Nat'/*)*/, 'tag' => '0'),
    'TH-19' => array('country' => 'TH', 'title' => /*t(*/'Saraburi'/*)*/, 'tag' => '0'),
    'TH-20' => array('country' => 'TH', 'title' => /*t(*/'Chon Buri'/*)*/, 'tag' => '0'),
    'TH-21' => array('country' => 'TH', 'title' => /*t(*/'Rayong'/*)*/, 'tag' => '0'),
    'TH-22' => array('country' => 'TH', 'title' => /*t(*/'Chanthaburi'/*)*/, 'tag' => '0'),
    'TH-23' => array('country' => 'TH', 'title' => /*t(*/'Trat'/*)*/, 'tag' => '0'),
    'TH-24' => array(
      'country' => 'TH',
      'title' => /*t(*/'Chachoengsao'/*)*/,
      'tag' => '0'
    ),
    'TH-25' => array(
      'country' => 'TH',
      'title' => /*t(*/'Prachin Buri'/*)*/,
      'tag' => '0'
    ),
    'TH-26' => array(
      'country' => 'TH',
      'title' => /*t(*/'Nakhon Nayok'/*)*/,
      'tag' => '0'
    ),
    'TH-27' => array('country' => 'TH', 'title' => /*t(*/'Sa Kaeo'/*)*/, 'tag' => '0'),
    'TH-30' => array(
      'country' => 'TH',
      'title' => /*t(*/'Nakhon Ratchasima'/*)*/,
      'tag' => '0'
    ),
    'TH-31' => array('country' => 'TH', 'title' => /*t(*/'Buri Ram'/*)*/, 'tag' => '0'),
    'TH-32' => array('country' => 'TH', 'title' => /*t(*/'Surin'/*)*/, 'tag' => '0'),
    'TH-33' => array('country' => 'TH', 'title' => /*t(*/'Si Sa Ket'/*)*/, 'tag' => '0'),
    'TH-34' => array(
      'country' => 'TH',
      'title' => /*t(*/'Ubon Ratchathani'/*)*/,
      'tag' => '0'
    ),
    'TH-35' => array('country' => 'TH', 'title' => /*t(*/'Yasothon'/*)*/, 'tag' => '0'),
    'TH-36' => array('country' => 'TH', 'title' => /*t(*/'Chaiyaphum'/*)*/, 'tag' => '0'),
    'TH-37' => array(
      'country' => 'TH',
      'title' => /*t(*/'Amnat Charoen'/*)*/,
      'tag' => '0'
    ),
    'TH-39' => array(
      'country' => 'TH',
      'title' => /*t(*/'Nong Bua Lam Phu'/*)*/,
      'tag' => '0'
    ),
    'TH-40' => array('country' => 'TH', 'title' => /*t(*/'Khon Kaen'/*)*/, 'tag' => '0'),
    'TH-41' => array('country' => 'TH', 'title' => /*t(*/'Udon Thani'/*)*/, 'tag' => '0'),
    'TH-42' => array('country' => 'TH', 'title' => /*t(*/'Loei'/*)*/, 'tag' => '0'),
    'TH-43' => array('country' => 'TH', 'title' => /*t(*/'Nong Khai'/*)*/, 'tag' => '0'),
    'TH-44' => array(
      'country' => 'TH',
      'title' => /*t(*/'Maha Sarakham'/*)*/,
      'tag' => '0'
    ),
    'TH-45' => array('country' => 'TH', 'title' => /*t(*/'Roi Et'/*)*/, 'tag' => '0'),
    'TH-46' => array('country' => 'TH', 'title' => /*t(*/'Kalasin'/*)*/, 'tag' => '0'),
    'TH-47' => array(
      'country' => 'TH',
      'title' => /*t(*/'Sakon Nakhon'/*)*/,
      'tag' => '0'
    ),
    'TH-48' => array(
      'country' => 'TH',
      'title' => /*t(*/'Nakhon Phanom'/*)*/,
      'tag' => '0'
    ),
    'TH-49' => array('country' => 'TH', 'title' => /*t(*/'Mukdahan'/*)*/, 'tag' => '0'),
    'TH-50' => array('country' => 'TH', 'title' => /*t(*/'Chiang Mai'/*)*/, 'tag' => '0'),
    'TH-51' => array('country' => 'TH', 'title' => /*t(*/'Lamphun'/*)*/, 'tag' => '0'),
    'TH-52' => array('country' => 'TH', 'title' => /*t(*/'Lampang'/*)*/, 'tag' => '0'),
    'TH-53' => array('country' => 'TH', 'title' => /*t(*/'Uttaradit'/*)*/, 'tag' => '0'),
    'TH-54' => array('country' => 'TH', 'title' => /*t(*/'Phrae'/*)*/, 'tag' => '0'),
    'TH-55' => array('country' => 'TH', 'title' => /*t(*/'Nan'/*)*/, 'tag' => '0'),
    'TH-56' => array('country' => 'TH', 'title' => /*t(*/'Phayao'/*)*/, 'tag' => '0'),
    'TH-57' => array('country' => 'TH', 'title' => /*t(*/'Chiang Rai'/*)*/, 'tag' => '0'),
    'TH-58' => array(
      'country' => 'TH',
      'title' => /*t(*/'Mae Hong Son'/*)*/,
      'tag' => '0'
    ),
    'TH-60' => array(
      'country' => 'TH',
      'title' => /*t(*/'Nakhon Sawan'/*)*/,
      'tag' => '0'
    ),
    'TH-61' => array('country' => 'TH', 'title' => /*t(*/'Uthai Thani'/*)*/, 'tag' => '0'),
    'TH-62' => array(
      'country' => 'TH',
      'title' => /*t(*/'Kamphaeng Phet'/*)*/,
      'tag' => '0'
    ),
    'TH-63' => array('country' => 'TH', 'title' => /*t(*/'Tak'/*)*/, 'tag' => '0'),
    'TH-64' => array('country' => 'TH', 'title' => /*t(*/'Sukhothai'/*)*/, 'tag' => '0'),
    'TH-65' => array('country' => 'TH', 'title' => /*t(*/'Phitsanulok'/*)*/, 'tag' => '0'),
    'TH-66' => array('country' => 'TH', 'title' => /*t(*/'Phichit'/*)*/, 'tag' => '0'),
    'TH-70' => array('country' => 'TH', 'title' => /*t(*/'Ratchaburi'/*)*/, 'tag' => '0'),
    'TH-71' => array(
      'country' => 'TH',
      'title' => /*t(*/'Kanchanaburi'/*)*/,
      'tag' => '0'
    ),
    'TH-72' => array('country' => 'TH', 'title' => /*t(*/'Suphanburi'/*)*/, 'tag' => '0'),
    'TH-73' => array(
      'country' => 'TH',
      'title' => /*t(*/'Nakhon Pathom'/*)*/,
      'tag' => '0'
    ),
    'TH-74' => array(
      'country' => 'TH',
      'title' => /*t(*/'Samut Sakhon'/*)*/,
      'tag' => '0'
    ),
    'TH-75' => array(
      'country' => 'TH',
      'title' => /*t(*/'Samut Songkhram'/*)*/,
      'tag' => '0'
    ),
    'TH-76' => array('country' => 'TH', 'title' => /*t(*/'Phetchabun'/*)*/, 'tag' => '0'),
    'TH-77' => array(
      'country' => 'TH',
      'title' => /*t(*/'Prachuap Khiri Khan'/*)*/,
      'tag' => '0'
    ),
    'TH-78' => array('country' => 'TH', 'title' => /*t(*/'Phetchaburi'/*)*/, 'tag' => '0'),
    'TH-80' => array(
      'country' => 'TH',
      'title' => /*t(*/'Nakhon Si Thammarat'/*)*/,
      'tag' => '0'
    ),
    'TH-81' => array('country' => 'TH', 'title' => /*t(*/'Krabi'/*)*/, 'tag' => '0'),
    'TH-82' => array('country' => 'TH', 'title' => /*t(*/'Phang Nga'/*)*/, 'tag' => '0'),
    'TH-83' => array('country' => 'TH', 'title' => /*t(*/'Phuket'/*)*/, 'tag' => '0'),
    'TH-84' => array('country' => 'TH', 'title' => /*t(*/'Surat Thani'/*)*/, 'tag' => '0'),
    'TH-85' => array('country' => 'TH', 'title' => /*t(*/'Ranong'/*)*/, 'tag' => '0'),
    'TH-86' => array('country' => 'TH', 'title' => /*t(*/'Chumpon'/*)*/, 'tag' => '0'),
    'TH-90' => array('country' => 'TH', 'title' => /*t(*/'Songkhla'/*)*/, 'tag' => '0'),
    'TH-91' => array('country' => 'TH', 'title' => /*t(*/'Satun'/*)*/, 'tag' => '0'),
    'TH-92' => array('country' => 'TH', 'title' => /*t(*/'Trang'/*)*/, 'tag' => '0'),
    'TH-93' => array('country' => 'TH', 'title' => /*t(*/'Phattalung'/*)*/, 'tag' => '0'),
    'TH-94' => array('country' => 'TH', 'title' => /*t(*/'Pattani'/*)*/, 'tag' => '0'),
    'TH-95' => array('country' => 'TH', 'title' => /*t(*/'Yala'/*)*/, 'tag' => '0'),
    'TH-96' => array('country' => 'TH', 'title' => /*t(*/'Narathiwat'/*)*/, 'tag' => '0'),
    'TH-S' => array('country' => 'TH', 'title' => /*t(*/'Pattaya'/*)*/, 'tag' => '0'),
    'TJ-GB' => array(
      'country' => 'TJ',
      'title' => /*t(*/'Gorno-Badakhstan'/*)*/,
      'tag' => '0'
    ),
    'TJ-KT' => array('country' => 'TJ', 'title' => /*t(*/'Khatlon'/*)*/, 'tag' => '0'),
    'TJ-SU' => array('country' => 'TJ', 'title' => /*t(*/'Sughd'/*)*/, 'tag' => '0'),
    'TK-A' => array('country' => 'TK', 'title' => /*t(*/'Atafu'/*)*/, 'tag' => '0'),
    'TK-F' => array('country' => 'TK', 'title' => /*t(*/'Fakaofo'/*)*/, 'tag' => '0'),
    'TK-N' => array('country' => 'TK', 'title' => /*t(*/'Nukunonu'/*)*/, 'tag' => '0'),
    'TL-AL' => array('country' => 'TL', 'title' => /*t(*/'Aileu'/*)*/, 'tag' => '0'),
    'TL-AN' => array('country' => 'TL', 'title' => /*t(*/'Ainaro'/*)*/, 'tag' => '0'),
    'TL-BA' => array('country' => 'TL', 'title' => /*t(*/'Baucau'/*)*/, 'tag' => '0'),
    'TL-BO' => array('country' => 'TL', 'title' => /*t(*/'Bobonaro'/*)*/, 'tag' => '0'),
    'TL-CO' => array('country' => 'TL', 'title' => /*t(*/'Cova Lima'/*)*/, 'tag' => '0'),
    'TL-DI' => array('country' => 'TL', 'title' => /*t(*/'Dili'/*)*/, 'tag' => '0'),
    'TL-ER' => array('country' => 'TL', 'title' => /*t(*/'Ermera'/*)*/, 'tag' => '0'),
    'TL-LA' => array('country' => 'TL', 'title' => /*t(*/'Lautem'/*)*/, 'tag' => '0'),
    'TL-LI' => array('country' => 'TL', 'title' => /*t(*/'Liquica'/*)*/, 'tag' => '0'),
    'TL-MF' => array('country' => 'TL', 'title' => /*t(*/'Manufahi'/*)*/, 'tag' => '0'),
    'TL-MT' => array('country' => 'TL', 'title' => /*t(*/'Manatuto'/*)*/, 'tag' => '0'),
    'TL-OE' => array('country' => 'TL', 'title' => /*t(*/'Oecussi'/*)*/, 'tag' => '0'),
    'TL-VI' => array('country' => 'TL', 'title' => /*t(*/'Viqueque'/*)*/, 'tag' => '0'),
    'TM-A' => array(
      'country' => 'TM',
      'title' => /*t(*/'Ahal Welayaty'/*)*/,
      'tag' => '0'
    ),
    'TM-B' => array(
      'country' => 'TM',
      'title' => /*t(*/'Balkan Welayaty'/*)*/,
      'tag' => '0'
    ),
    'TM-D' => array(
      'country' => 'TM',
      'title' => /*t(*/'Dashhowuz Welayaty'/*)*/,
      'tag' => '0'
    ),
    'TM-L' => array(
      'country' => 'TM',
      'title' => /*t(*/'Lebap Welayaty'/*)*/,
      'tag' => '0'
    ),
    'TM-M' => array(
      'country' => 'TM',
      'title' => /*t(*/'Mary Welayaty'/*)*/,
      'tag' => '0'
    ),
    'TN-AR' => array('country' => 'TN', 'title' => /*t(*/'Ariana'/*)*/, 'tag' => '0'),
    'TN-BA' => array('country' => 'TN', 'title' => /*t(*/'Ben Arous'/*)*/, 'tag' => '0'),
    'TN-BI' => array('country' => 'TN', 'title' => /*t(*/'Bizerte'/*)*/, 'tag' => '0'),
    'TN-BJ' => array('country' => 'TN', 'title' => /*t(*/'Beja'/*)*/, 'tag' => '0'),
    'TN-GB' => array('country' => 'TN', 'title' => /*t(*/'Gabes'/*)*/, 'tag' => '0'),
    'TN-GF' => array('country' => 'TN', 'title' => /*t(*/'Gafsa'/*)*/, 'tag' => '0'),
    'TN-JE' => array('country' => 'TN', 'title' => /*t(*/'Jendouba'/*)*/, 'tag' => '0'),
    'TN-KB' => array('country' => 'TN', 'title' => /*t(*/'Kebili'/*)*/, 'tag' => '0'),
    'TN-KF' => array('country' => 'TN', 'title' => /*t(*/'Kef'/*)*/, 'tag' => '0'),
    'TN-KR' => array('country' => 'TN', 'title' => /*t(*/'Kairouan'/*)*/, 'tag' => '0'),
    'TN-KS' => array('country' => 'TN', 'title' => /*t(*/'Kasserine'/*)*/, 'tag' => '0'),
    'TN-ME' => array('country' => 'TN', 'title' => /*t(*/'Medenine'/*)*/, 'tag' => '0'),
    'TN-MH' => array('country' => 'TN', 'title' => /*t(*/'Mahdia'/*)*/, 'tag' => '0'),
    'TN-MN' => array('country' => 'TN', 'title' => /*t(*/'Manouba'/*)*/, 'tag' => '0'),
    'TN-MO' => array('country' => 'TN', 'title' => /*t(*/'Monastir'/*)*/, 'tag' => '0'),
    'TN-NA' => array('country' => 'TN', 'title' => /*t(*/'Nabeul'/*)*/, 'tag' => '0'),
    'TN-SD' => array('country' => 'TN', 'title' => /*t(*/'Sidi'/*)*/, 'tag' => '0'),
    'TN-SF' => array('country' => 'TN', 'title' => /*t(*/'Sfax'/*)*/, 'tag' => '0'),
    'TN-SL' => array('country' => 'TN', 'title' => /*t(*/'Siliana'/*)*/, 'tag' => '0'),
    'TN-SO' => array('country' => 'TN', 'title' => /*t(*/'Sousse'/*)*/, 'tag' => '0'),
    'TN-TA' => array('country' => 'TN', 'title' => /*t(*/'Tataouine'/*)*/, 'tag' => '0'),
    'TN-TO' => array('country' => 'TN', 'title' => /*t(*/'Tozeur'/*)*/, 'tag' => '0'),
    'TN-TU' => array('country' => 'TN', 'title' => /*t(*/'Tunis'/*)*/, 'tag' => '0'),
    'TN-ZA' => array('country' => 'TN', 'title' => /*t(*/'Zaghouan'/*)*/, 'tag' => '0'),
    'TO-H' => array('country' => 'TO', 'title' => /*t(*/"Ha'apai"/*)*/, 'tag' => '0'),
    'TO-T' => array('country' => 'TO', 'title' => /*t(*/'Tongatapu'/*)*/, 'tag' => '0'),
    'TO-V' => array('country' => 'TO', 'title' => /*t(*/"Vava'u"/*)*/, 'tag' => '0'),
    'TR-ADA' => array('country' => 'TR', 'title' => /*t(*/'Adana'/*)*/, 'tag' => '0'),
    'TR-ADI' => array('country' => 'TR', 'title' => /*t(*/'Adiyaman'/*)*/, 'tag' => '0'),
    'TR-AFY' => array(
      'country' => 'TR',
      'title' => /*t(*/'Afyonkarahisar'/*)*/,
      'tag' => '0'
    ),
    'TR-AGR' => array('country' => 'TR', 'title' => /*t(*/'Agri'/*)*/, 'tag' => '0'),
    'TR-AKS' => array('country' => 'TR', 'title' => /*t(*/'Aksaray'/*)*/, 'tag' => '0'),
    'TR-AMA' => array('country' => 'TR', 'title' => /*t(*/'Amasya'/*)*/, 'tag' => '0'),
    'TR-ANK' => array('country' => 'TR', 'title' => /*t(*/'Ankara'/*)*/, 'tag' => '0'),
    'TR-ANT' => array('country' => 'TR', 'title' => /*t(*/'Antalya'/*)*/, 'tag' => '0'),
    'TR-ARD' => array('country' => 'TR', 'title' => /*t(*/'Ardahan'/*)*/, 'tag' => '0'),
    'TR-ART' => array('country' => 'TR', 'title' => /*t(*/'Artvin'/*)*/, 'tag' => '0'),
    'TR-AYI' => array('country' => 'TR', 'title' => /*t(*/'Aydin'/*)*/, 'tag' => '0'),
    'TR-BAL' => array('country' => 'TR', 'title' => /*t(*/'Balikesir'/*)*/, 'tag' => '0'),
    'TR-BAR' => array('country' => 'TR', 'title' => /*t(*/'Bartin'/*)*/, 'tag' => '0'),
    'TR-BAT' => array('country' => 'TR', 'title' => /*t(*/'Batman'/*)*/, 'tag' => '0'),
    'TR-BAY' => array('country' => 'TR', 'title' => /*t(*/'Bayburt'/*)*/, 'tag' => '0'),
    'TR-BIL' => array('country' => 'TR', 'title' => /*t(*/'Bilecik'/*)*/, 'tag' => '0'),
    'TR-BIN' => array('country' => 'TR', 'title' => /*t(*/'Bingol'/*)*/, 'tag' => '0'),
    'TR-BIT' => array('country' => 'TR', 'title' => /*t(*/'Bitlis'/*)*/, 'tag' => '0'),
    'TR-BOL' => array('country' => 'TR', 'title' => /*t(*/'Bolu'/*)*/, 'tag' => '0'),
    'TR-BRD' => array('country' => 'TR', 'title' => /*t(*/'Burdur'/*)*/, 'tag' => '0'),
    'TR-BRS' => array('country' => 'TR', 'title' => /*t(*/'Bursa'/*)*/, 'tag' => '0'),
    'TR-CKL' => array('country' => 'TR', 'title' => /*t(*/'Canakkale'/*)*/, 'tag' => '0'),
    'TR-CKR' => array('country' => 'TR', 'title' => /*t(*/'Cankiri'/*)*/, 'tag' => '0'),
    'TR-COR' => array('country' => 'TR', 'title' => /*t(*/'Corum'/*)*/, 'tag' => '0'),
    'TR-DEN' => array('country' => 'TR', 'title' => /*t(*/'Denizli'/*)*/, 'tag' => '0'),
    'TR-DIY' => array('country' => 'TR', 'title' => /*t(*/'Diyarbakir'/*)*/, 'tag' => '0'),
    'TR-DUZ' => array('country' => 'TR', 'title' => /*t(*/'Duzce'/*)*/, 'tag' => '0'),
    'TR-EDI' => array('country' => 'TR', 'title' => /*t(*/'Edirne'/*)*/, 'tag' => '0'),
    'TR-ELA' => array('country' => 'TR', 'title' => /*t(*/'Elazig'/*)*/, 'tag' => '0'),
    'TR-ESK' => array('country' => 'TR', 'title' => /*t(*/'Eskisehir'/*)*/, 'tag' => '0'),
    'TR-EZC' => array('country' => 'TR', 'title' => /*t(*/'Erzincan'/*)*/, 'tag' => '0'),
    'TR-EZR' => array('country' => 'TR', 'title' => /*t(*/'Erzurum'/*)*/, 'tag' => '0'),
    'TR-GAZ' => array('country' => 'TR', 'title' => /*t(*/'Gaziantep'/*)*/, 'tag' => '0'),
    'TR-GIR' => array('country' => 'TR', 'title' => /*t(*/'Giresun'/*)*/, 'tag' => '0'),
    'TR-GMS' => array('country' => 'TR', 'title' => /*t(*/'Gumushane'/*)*/, 'tag' => '0'),
    'TR-HKR' => array('country' => 'TR', 'title' => /*t(*/'Hakkari'/*)*/, 'tag' => '0'),
    'TR-HTY' => array('country' => 'TR', 'title' => /*t(*/'Hatay'/*)*/, 'tag' => '0'),
    'TR-IGD' => array('country' => 'TR', 'title' => /*t(*/'Igdir'/*)*/, 'tag' => '0'),
    'TR-ISP' => array('country' => 'TR', 'title' => /*t(*/'Isparta'/*)*/, 'tag' => '0'),
    'TR-IST' => array('country' => 'TR', 'title' => /*t(*/'Istanbul'/*)*/, 'tag' => '0'),
    'TR-IZM' => array('country' => 'TR', 'title' => /*t(*/'Izmir'/*)*/, 'tag' => '0'),
    'TR-KAH' => array(
      'country' => 'TR',
      'title' => /*t(*/'Kahramanmaras'/*)*/,
      'tag' => '0'
    ),
    'TR-KAS' => array('country' => 'TR', 'title' => /*t(*/'Kastamonu'/*)*/, 'tag' => '0'),
    'TR-KAY' => array('country' => 'TR', 'title' => /*t(*/'Kayseri'/*)*/, 'tag' => '0'),
    'TR-KLR' => array('country' => 'TR', 'title' => /*t(*/'Kirklareli'/*)*/, 'tag' => '0'),
    'TR-KLS' => array('country' => 'TR', 'title' => /*t(*/'Kilis'/*)*/, 'tag' => '0'),
    'TR-KOC' => array('country' => 'TR', 'title' => /*t(*/'Kocaeli'/*)*/, 'tag' => '0'),
    'TR-KON' => array('country' => 'TR', 'title' => /*t(*/'Konya'/*)*/, 'tag' => '0'),
    'TR-KRB' => array('country' => 'TR', 'title' => /*t(*/'Karabuk'/*)*/, 'tag' => '0'),
    'TR-KRH' => array('country' => 'TR', 'title' => /*t(*/'Kirsehir'/*)*/, 'tag' => '0'),
    'TR-KRK' => array('country' => 'TR', 'title' => /*t(*/'Kirikkale'/*)*/, 'tag' => '0'),
    'TR-KRM' => array('country' => 'TR', 'title' => /*t(*/'Karaman'/*)*/, 'tag' => '0'),
    'TR-KRS' => array('country' => 'TR', 'title' => /*t(*/'Kars'/*)*/, 'tag' => '0'),
    'TR-KUT' => array('country' => 'TR', 'title' => /*t(*/'Kutahya'/*)*/, 'tag' => '0'),
    'TR-MAL' => array('country' => 'TR', 'title' => /*t(*/'Malatya'/*)*/, 'tag' => '0'),
    'TR-MAN' => array('country' => 'TR', 'title' => /*t(*/'Manisa'/*)*/, 'tag' => '0'),
    'TR-MAR' => array('country' => 'TR', 'title' => /*t(*/'Mardin'/*)*/, 'tag' => '0'),
    'TR-MER' => array('country' => 'TR', 'title' => /*t(*/'Mersin'/*)*/, 'tag' => '0'),
    'TR-MUG' => array('country' => 'TR', 'title' => /*t(*/'Mugla'/*)*/, 'tag' => '0'),
    'TR-MUS' => array('country' => 'TR', 'title' => /*t(*/'Mus'/*)*/, 'tag' => '0'),
    'TR-NEV' => array('country' => 'TR', 'title' => /*t(*/'Nevsehir'/*)*/, 'tag' => '0'),
    'TR-NIG' => array('country' => 'TR', 'title' => /*t(*/'Nigde'/*)*/, 'tag' => '0'),
    'TR-ORD' => array('country' => 'TR', 'title' => /*t(*/'Ordu'/*)*/, 'tag' => '0'),
    'TR-OSM' => array('country' => 'TR', 'title' => /*t(*/'Osmaniye'/*)*/, 'tag' => '0'),
    'TR-RIZ' => array('country' => 'TR', 'title' => /*t(*/'Rize'/*)*/, 'tag' => '0'),
    'TR-SAK' => array('country' => 'TR', 'title' => /*t(*/'Sakarya'/*)*/, 'tag' => '0'),
    'TR-SAM' => array('country' => 'TR', 'title' => /*t(*/'Samsun'/*)*/, 'tag' => '0'),
    'TR-SAN' => array('country' => 'TR', 'title' => /*t(*/'Sanliurfa'/*)*/, 'tag' => '0'),
    'TR-SII' => array('country' => 'TR', 'title' => /*t(*/'Siirt'/*)*/, 'tag' => '0'),
    'TR-SIN' => array('country' => 'TR', 'title' => /*t(*/'Sinop'/*)*/, 'tag' => '0'),
    'TR-SIR' => array('country' => 'TR', 'title' => /*t(*/'Sirnak'/*)*/, 'tag' => '0'),
    'TR-SIV' => array('country' => 'TR', 'title' => /*t(*/'Sivas'/*)*/, 'tag' => '0'),
    'TR-TEL' => array('country' => 'TR', 'title' => /*t(*/'Tekirdag'/*)*/, 'tag' => '0'),
    'TR-TOK' => array('country' => 'TR', 'title' => /*t(*/'Tokat'/*)*/, 'tag' => '0'),
    'TR-TRA' => array('country' => 'TR', 'title' => /*t(*/'Trabzon'/*)*/, 'tag' => '0'),
    'TR-TUN' => array('country' => 'TR', 'title' => /*t(*/'Tunceli'/*)*/, 'tag' => '0'),
    'TR-USK' => array('country' => 'TR', 'title' => /*t(*/'Usak'/*)*/, 'tag' => '0'),
    'TR-VAN' => array('country' => 'TR', 'title' => /*t(*/'Van'/*)*/, 'tag' => '0'),
    'TR-YAL' => array('country' => 'TR', 'title' => /*t(*/'Yalova'/*)*/, 'tag' => '0'),
    'TR-YOZ' => array('country' => 'TR', 'title' => /*t(*/'Yozgat'/*)*/, 'tag' => '0'),
    'TR-ZON' => array('country' => 'TR', 'title' => /*t(*/'Zonguldak'/*)*/, 'tag' => '0'),
    'TT-AR' => array('country' => 'TT', 'title' => /*t(*/'Arima'/*)*/, 'tag' => '0'),
    'TT-CH' => array('country' => 'TT', 'title' => /*t(*/'Chaguanas'/*)*/, 'tag' => '0'),
    'TT-CT' => array(
      'country' => 'TT',
      'title' => /*t(*/'Couva/Tabaquite/Talparo'/*)*/,
      'tag' => '0'
    ),
    'TT-DM' => array(
      'country' => 'TT',
      'title' => /*t(*/'Diego Martin'/*)*/,
      'tag' => '0'
    ),
    'TT-MR' => array(
      'country' => 'TT',
      'title' => /*t(*/'Mayaro/Rio Claro'/*)*/,
      'tag' => '0'
    ),
    'TT-PD' => array('country' => 'TT', 'title' => /*t(*/'Penal/Debe'/*)*/, 'tag' => '0'),
    'TT-PF' => array(
      'country' => 'TT',
      'title' => /*t(*/'Point Fortin'/*)*/,
      'tag' => '0'
    ),
    'TT-PS' => array(
      'country' => 'TT',
      'title' => /*t(*/'Port of Spain'/*)*/,
      'tag' => '0'
    ),
    'TT-PT' => array(
      'country' => 'TT',
      'title' => /*t(*/'Princes Town'/*)*/,
      'tag' => '0'
    ),
    'TT-SF' => array(
      'country' => 'TT',
      'title' => /*t(*/'San Fernando'/*)*/,
      'tag' => '0'
    ),
    'TT-SG' => array(
      'country' => 'TT',
      'title' => /*t(*/'Sangre Grande'/*)*/,
      'tag' => '0'
    ),
    'TT-SI' => array('country' => 'TT', 'title' => /*t(*/'Siparia'/*)*/, 'tag' => '0'),
    'TT-SL' => array(
      'country' => 'TT',
      'title' => /*t(*/'San Juan/Laventille'/*)*/,
      'tag' => '0'
    ),
    'TT-TO' => array('country' => 'TT', 'title' => /*t(*/'Tobago'/*)*/, 'tag' => '0'),
    'TT-TP' => array(
      'country' => 'TT',
      'title' => /*t(*/'Tunapuna/Piarco'/*)*/,
      'tag' => '0'
    ),
    'TV-FUN' => array('country' => 'TV', 'title' => /*t(*/'Funafuti'/*)*/, 'tag' => '0'),
    'TV-NFT' => array('country' => 'TV', 'title' => /*t(*/'Nukufetau'/*)*/, 'tag' => '0'),
    'TV-NLK' => array('country' => 'TV', 'title' => /*t(*/'Niulakita'/*)*/, 'tag' => '0'),
    'TV-NLL' => array('country' => 'TV', 'title' => /*t(*/'Nukulaelae'/*)*/, 'tag' => '0'),
    'TV-NME' => array('country' => 'TV', 'title' => /*t(*/'Nanumea'/*)*/, 'tag' => '0'),
    'TV-NMG' => array('country' => 'TV', 'title' => /*t(*/'Nanumanga'/*)*/, 'tag' => '0'),
    'TV-NTO' => array('country' => 'TV', 'title' => /*t(*/'Niutao'/*)*/, 'tag' => '0'),
    'TV-NUI' => array('country' => 'TV', 'title' => /*t(*/'Nui'/*)*/, 'tag' => '0'),
    'TV-VAI' => array('country' => 'TV', 'title' => /*t(*/'Vaitupu'/*)*/, 'tag' => '0'),
    'TW-CC' => array('country' => 'TW', 'title' => /*t(*/'Chia-i city'/*)*/, 'tag' => '0'),
    'TW-CH' => array('country' => 'TW', 'title' => /*t(*/'Chang-hua'/*)*/, 'tag' => '0'),
    'TW-CI' => array('country' => 'TW', 'title' => /*t(*/'Chia-i'/*)*/, 'tag' => '0'),
    'TW-CL' => array('country' => 'TW', 'title' => /*t(*/'Chi-lung'/*)*/, 'tag' => '0'),
    'TW-HC' => array('country' => 'TW', 'title' => /*t(*/'Hsin-chu'/*)*/, 'tag' => '0'),
    'TW-HL' => array('country' => 'TW', 'title' => /*t(*/'Hua-lien'/*)*/, 'tag' => '0'),
    'TW-HS' => array('country' => 'TW', 'title' => /*t(*/'Hsin-chu'/*)*/, 'tag' => '0'),
    'TW-IL' => array('country' => 'TW', 'title' => /*t(*/'I-lan'/*)*/, 'tag' => '0'),
    'TW-KC' => array(
      'country' => 'TW',
      'title' => /*t(*/'Kao-hsiung city'/*)*/,
      'tag' => '0'
    ),
    'TW-KH' => array(
      'country' => 'TW',
      'title' => /*t(*/'Kao-hsiung county'/*)*/,
      'tag' => '0'
    ),
    'TW-KM' => array('country' => 'TW', 'title' => /*t(*/'Kin-men'/*)*/, 'tag' => '0'),
    'TW-LC' => array('country' => 'TW', 'title' => /*t(*/'Lien-chiang'/*)*/, 'tag' => '0'),
    'TW-ML' => array('country' => 'TW', 'title' => /*t(*/'Miao-li'/*)*/, 'tag' => '0'),
    'TW-NT' => array('country' => 'TW', 'title' => /*t(*/"Nan-t'ou"/*)*/, 'tag' => '0'),
    'TW-PH' => array('country' => 'TW', 'title' => /*t(*/"P'eng-hu"/*)*/, 'tag' => '0'),
    'TW-PT' => array('country' => 'TW', 'title' => /*t(*/"P'ing-tung"/*)*/, 'tag' => '0'),
    'TW-TA' => array('country' => 'TW', 'title' => /*t(*/"T'ai-nan"/*)*/, 'tag' => '0'),
    'TW-TC' => array(
      'country' => 'TW',
      'title' => /*t(*/"T'ai-pei city"/*)*/,
      'tag' => '0'
    ),
    'TW-TG' => array('country' => 'TW', 'title' => /*t(*/"T'ai-chung"/*)*/, 'tag' => '0'),
    'TW-TH' => array('country' => 'TW', 'title' => /*t(*/"T'ai-chung"/*)*/, 'tag' => '0'),
    'TW-TN' => array('country' => 'TW', 'title' => /*t(*/"T'ai-nan"/*)*/, 'tag' => '0'),
    'TW-TP' => array(
      'country' => 'TW',
      'title' => /*t(*/"T'ai-pei county"/*)*/,
      'tag' => '0'
    ),
    'TW-TT' => array('country' => 'TW', 'title' => /*t(*/"T'ai-tung"/*)*/, 'tag' => '0'),
    'TW-TY' => array('country' => 'TW', 'title' => /*t(*/"T'ao-yuan"/*)*/, 'tag' => '0'),
    'TW-YL' => array('country' => 'TW', 'title' => /*t(*/'Yun-lin'/*)*/, 'tag' => '0'),
    'TZ-AR' => array('country' => 'TZ', 'title' => /*t(*/'Arusha'/*)*/, 'tag' => '0'),
    'TZ-DO' => array('country' => 'TZ', 'title' => /*t(*/'Dodoma'/*)*/, 'tag' => '0'),
    'TZ-DS' => array(
      'country' => 'TZ',
      'title' => /*t(*/'Dar es Salaam'/*)*/,
      'tag' => '0'
    ),
    'TZ-IR' => array('country' => 'TZ', 'title' => /*t(*/'Iringa'/*)*/, 'tag' => '0'),
    'TZ-KA' => array('country' => 'TZ', 'title' => /*t(*/'Kagera'/*)*/, 'tag' => '0'),
    'TZ-KI' => array('country' => 'TZ', 'title' => /*t(*/'Kigoma'/*)*/, 'tag' => '0'),
    'TZ-KJ' => array('country' => 'TZ', 'title' => /*t(*/'Kilimanjaro'/*)*/, 'tag' => '0'),
    'TZ-LN' => array('country' => 'TZ', 'title' => /*t(*/'Lindi'/*)*/, 'tag' => '0'),
    'TZ-MB' => array('country' => 'TZ', 'title' => /*t(*/'Mbeya'/*)*/, 'tag' => '0'),
    'TZ-MO' => array('country' => 'TZ', 'title' => /*t(*/'Morogoro'/*)*/, 'tag' => '0'),
    'TZ-MR' => array('country' => 'TZ', 'title' => /*t(*/'Mara'/*)*/, 'tag' => '0'),
    'TZ-MT' => array('country' => 'TZ', 'title' => /*t(*/'Mtwara'/*)*/, 'tag' => '0'),
    'TZ-MW' => array('country' => 'TZ', 'title' => /*t(*/'Mwanza'/*)*/, 'tag' => '0'),
    'TZ-MY' => array('country' => 'TZ', 'title' => /*t(*/'Manyara'/*)*/, 'tag' => '0'),
    'TZ-PN' => array('country' => 'TZ', 'title' => /*t(*/'Pemba North'/*)*/, 'tag' => '0'),
    'TZ-PS' => array('country' => 'TZ', 'title' => /*t(*/'Pemba South'/*)*/, 'tag' => '0'),
    'TZ-PW' => array('country' => 'TZ', 'title' => /*t(*/'Pwani'/*)*/, 'tag' => '0'),
    'TZ-RK' => array('country' => 'TZ', 'title' => /*t(*/'Rukwa'/*)*/, 'tag' => '0'),
    'TZ-RV' => array('country' => 'TZ', 'title' => /*t(*/'Ruvuma'/*)*/, 'tag' => '0'),
    'TZ-SH' => array('country' => 'TZ', 'title' => /*t(*/'Shinyanga'/*)*/, 'tag' => '0'),
    'TZ-SI' => array('country' => 'TZ', 'title' => /*t(*/'Singida'/*)*/, 'tag' => '0'),
    'TZ-TB' => array('country' => 'TZ', 'title' => /*t(*/'Tabora'/*)*/, 'tag' => '0'),
    'TZ-TN' => array('country' => 'TZ', 'title' => /*t(*/'Tanga'/*)*/, 'tag' => '0'),
    'TZ-ZC' => array(
      'country' => 'TZ',
      'title' => /*t(*/'Zanzibar Central/South'/*)*/,
      'tag' => '0'
    ),
    'TZ-ZN' => array(
      'country' => 'TZ',
      'title' => /*t(*/'Zanzibar North'/*)*/,
      'tag' => '0'
    ),
    'TZ-ZU' => array(
      'country' => 'TZ',
      'title' => /*t(*/'Zanzibar Urban/West'/*)*/,
      'tag' => '0'
    ),
    'UA-CH' => array('country' => 'UA', 'title' => /*t(*/'Chernihiv'/*)*/, 'tag' => '0'),
    'UA-CK' => array('country' => 'UA', 'title' => /*t(*/'Cherkasy'/*)*/, 'tag' => '0'),
    'UA-CR' => array('country' => 'UA', 'title' => /*t(*/'Crimea'/*)*/, 'tag' => '0'),
    'UA-CV' => array('country' => 'UA', 'title' => /*t(*/'Chernivtsi'/*)*/, 'tag' => '0'),
    'UA-DN' => array(
      'country' => 'UA',
      'title' => /*t(*/"Dnipropetrovs'k"/*)*/,
      'tag' => '0'
    ),
    'UA-DO' => array('country' => 'UA', 'title' => /*t(*/"Donets'k"/*)*/, 'tag' => '0'),
    'UA-IV' => array(
      'country' => 'UA',
      'title' => /*t(*/"Ivano-Frankivs'k"/*)*/,
      'tag' => '0'
    ),
    'UA-KL' => array(
      'country' => 'UA',
      'title' => /*t(*/'Kharkiv Kherson'/*)*/,
      'tag' => '0'
    ),
    'UA-KM' => array(
      'country' => 'UA',
      'title' => /*t(*/"Khmel'nyts'kyy"/*)*/,
      'tag' => '0'
    ),
    'UA-KR' => array('country' => 'UA', 'title' => /*t(*/'Kirovohrad'/*)*/, 'tag' => '0'),
    'UA-KV' => array('country' => 'UA', 'title' => /*t(*/'Kiev'/*)*/, 'tag' => '0'),
    'UA-KY' => array('country' => 'UA', 'title' => /*t(*/'Kyyiv'/*)*/, 'tag' => '0'),
    'UA-LU' => array('country' => 'UA', 'title' => /*t(*/"Luhans'k"/*)*/, 'tag' => '0'),
    'UA-LV' => array('country' => 'UA', 'title' => /*t(*/"L'viv"/*)*/, 'tag' => '0'),
    'UA-MY' => array('country' => 'UA', 'title' => /*t(*/'Mykolayiv'/*)*/, 'tag' => '0'),
    'UA-OD' => array('country' => 'UA', 'title' => /*t(*/'Odesa'/*)*/, 'tag' => '0'),
    'UA-PO' => array('country' => 'UA', 'title' => /*t(*/'Poltava'/*)*/, 'tag' => '0'),
    'UA-RI' => array('country' => 'UA', 'title' => /*t(*/'Rivne'/*)*/, 'tag' => '0'),
    'UA-SE' => array('country' => 'UA', 'title' => /*t(*/'Sevastopol'/*)*/, 'tag' => '0'),
    'UA-SU' => array('country' => 'UA', 'title' => /*t(*/'Sumy'/*)*/, 'tag' => '0'),
    'UA-TE' => array('country' => 'UA', 'title' => /*t(*/"Ternopil'"/*)*/, 'tag' => '0'),
    'UA-VI' => array('country' => 'UA', 'title' => /*t(*/'Vinnytsya'/*)*/, 'tag' => '0'),
    'UA-VO' => array('country' => 'UA', 'title' => /*t(*/"Volyn'"/*)*/, 'tag' => '0'),
    'UA-ZA' => array(
      'country' => 'UA',
      'title' => /*t(*/'Zaporizhzhya'/*)*/,
      'tag' => '0'
    ),
    'UA-ZH' => array('country' => 'UA', 'title' => /*t(*/'Zhytomyr'/*)*/, 'tag' => '0'),
    'UA-ZK' => array('country' => 'UA', 'title' => /*t(*/'Zakarpattya'/*)*/, 'tag' => '0'),
    'UG-ADJ' => array('country' => 'UG', 'title' => /*t(*/'Adjumani'/*)*/, 'tag' => '0'),
    'UG-APC' => array('country' => 'UG', 'title' => /*t(*/'Apac'/*)*/, 'tag' => '0'),
    'UG-ARU' => array('country' => 'UG', 'title' => /*t(*/'Arua'/*)*/, 'tag' => '0'),
    'UG-BSH' => array('country' => 'UG', 'title' => /*t(*/'Bushenyi'/*)*/, 'tag' => '0'),
    'UG-BUG' => array('country' => 'UG', 'title' => /*t(*/'Bugiri'/*)*/, 'tag' => '0'),
    'UG-BUN' => array('country' => 'UG', 'title' => /*t(*/'Bundibugyo'/*)*/, 'tag' => '0'),
    'UG-BUS' => array('country' => 'UG', 'title' => /*t(*/'Busia'/*)*/, 'tag' => '0'),
    'UG-GUL' => array('country' => 'UG', 'title' => /*t(*/'Gulu'/*)*/, 'tag' => '0'),
    'UG-HOI' => array('country' => 'UG', 'title' => /*t(*/'Hoima'/*)*/, 'tag' => '0'),
    'UG-IGA' => array('country' => 'UG', 'title' => /*t(*/'Iganga'/*)*/, 'tag' => '0'),
    'UG-JIN' => array('country' => 'UG', 'title' => /*t(*/'Jinja'/*)*/, 'tag' => '0'),
    'UG-KAB' => array(
      'country' => 'UG',
      'title' => /*t(*/'Kaberamaido'/*)*/,
      'tag' => '0'
    ),
    'UG-KAL' => array('country' => 'UG', 'title' => /*t(*/'Kalangala'/*)*/, 'tag' => '0'),
    'UG-KAM' => array('country' => 'UG', 'title' => /*t(*/'Kamwenge'/*)*/, 'tag' => '0'),
    'UG-KAN' => array('country' => 'UG', 'title' => /*t(*/'Kanungu'/*)*/, 'tag' => '0'),
    'UG-KAR' => array('country' => 'UG', 'title' => /*t(*/'Kabarole'/*)*/, 'tag' => '0'),
    'UG-KAS' => array('country' => 'UG', 'title' => /*t(*/'Kasese'/*)*/, 'tag' => '0'),
    'UG-KAY' => array('country' => 'UG', 'title' => /*t(*/'Kayunga'/*)*/, 'tag' => '0'),
    'UG-KBA' => array('country' => 'UG', 'title' => /*t(*/'Kibaale'/*)*/, 'tag' => '0'),
    'UG-KBL' => array('country' => 'UG', 'title' => /*t(*/'Kabale'/*)*/, 'tag' => '0'),
    'UG-KIB' => array('country' => 'UG', 'title' => /*t(*/'Kiboga'/*)*/, 'tag' => '0'),
    'UG-KIS' => array('country' => 'UG', 'title' => /*t(*/'Kisoro'/*)*/, 'tag' => '0'),
    'UG-KIT' => array('country' => 'UG', 'title' => /*t(*/'Kitgum'/*)*/, 'tag' => '0'),
    'UG-KML' => array('country' => 'UG', 'title' => /*t(*/'Kamuli'/*)*/, 'tag' => '0'),
    'UG-KMP' => array('country' => 'UG', 'title' => /*t(*/'Kampala'/*)*/, 'tag' => '0'),
    'UG-KOT' => array('country' => 'UG', 'title' => /*t(*/'Kotido'/*)*/, 'tag' => '0'),
    'UG-KPC' => array('country' => 'UG', 'title' => /*t(*/'Kapchorwa'/*)*/, 'tag' => '0'),
    'UG-KTK' => array('country' => 'UG', 'title' => /*t(*/'Katakwi'/*)*/, 'tag' => '0'),
    'UG-KUM' => array('country' => 'UG', 'title' => /*t(*/'Kumi'/*)*/, 'tag' => '0'),
    'UG-KYE' => array('country' => 'UG', 'title' => /*t(*/'Kyenjojo'/*)*/, 'tag' => '0'),
    'UG-LIR' => array('country' => 'UG', 'title' => /*t(*/'Lira'/*)*/, 'tag' => '0'),
    'UG-LUW' => array('country' => 'UG', 'title' => /*t(*/'Luwero'/*)*/, 'tag' => '0'),
    'UG-MAS' => array('country' => 'UG', 'title' => /*t(*/'Masaka'/*)*/, 'tag' => '0'),
    'UG-MAY' => array('country' => 'UG', 'title' => /*t(*/'Mayuge'/*)*/, 'tag' => '0'),
    'UG-MBA' => array('country' => 'UG', 'title' => /*t(*/'Mbale'/*)*/, 'tag' => '0'),
    'UG-MBR' => array('country' => 'UG', 'title' => /*t(*/'Mbarara'/*)*/, 'tag' => '0'),
    'UG-MOY' => array('country' => 'UG', 'title' => /*t(*/'Moyo'/*)*/, 'tag' => '0'),
    'UG-MPI' => array('country' => 'UG', 'title' => /*t(*/'Mpigi'/*)*/, 'tag' => '0'),
    'UG-MRT' => array('country' => 'UG', 'title' => /*t(*/'Moroto'/*)*/, 'tag' => '0'),
    'UG-MSN' => array('country' => 'UG', 'title' => /*t(*/'Masindi'/*)*/, 'tag' => '0'),
    'UG-MUB' => array('country' => 'UG', 'title' => /*t(*/'Mubende'/*)*/, 'tag' => '0'),
    'UG-MUK' => array('country' => 'UG', 'title' => /*t(*/'Mukono'/*)*/, 'tag' => '0'),
    'UG-NAK' => array(
      'country' => 'UG',
      'title' => /*t(*/'Nakapiripirit'/*)*/,
      'tag' => '0'
    ),
    'UG-NEB' => array('country' => 'UG', 'title' => /*t(*/'Nebbi'/*)*/, 'tag' => '0'),
    'UG-NKS' => array(
      'country' => 'UG',
      'title' => /*t(*/'Nakasongola'/*)*/,
      'tag' => '0'
    ),
    'UG-NTU' => array('country' => 'UG', 'title' => /*t(*/'Ntungamo'/*)*/, 'tag' => '0'),
    'UG-PAD' => array('country' => 'UG', 'title' => /*t(*/'Pader'/*)*/, 'tag' => '0'),
    'UG-PAL' => array('country' => 'UG', 'title' => /*t(*/'Pallisa'/*)*/, 'tag' => '0'),
    'UG-RAK' => array('country' => 'UG', 'title' => /*t(*/'Rakai'/*)*/, 'tag' => '0'),
    'UG-RUK' => array('country' => 'UG', 'title' => /*t(*/'Rukungiri'/*)*/, 'tag' => '0'),
    'UG-SEM' => array('country' => 'UG', 'title' => /*t(*/'Sembabule'/*)*/, 'tag' => '0'),
    'UG-SIR' => array('country' => 'UG', 'title' => /*t(*/'Sironko'/*)*/, 'tag' => '0'),
    'UG-SOR' => array('country' => 'UG', 'title' => /*t(*/'Soroti'/*)*/, 'tag' => '0'),
    'UG-TOR' => array('country' => 'UG', 'title' => /*t(*/'Tororo'/*)*/, 'tag' => '0'),
    'UG-WAK' => array('country' => 'UG', 'title' => /*t(*/'Wakiso'/*)*/, 'tag' => '0'),
    'UG-YUM' => array('country' => 'UG', 'title' => /*t(*/'Yumbe'/*)*/, 'tag' => '0'),
    'UM-BI' => array(
      'country' => 'UM',
      'title' => /*t(*/'Baker Island'/*)*/,
      'tag' => '0'
    ),
    'UM-HI' => array(
      'country' => 'UM',
      'title' => /*t(*/'Howland Island'/*)*/,
      'tag' => '0'
    ),
    'UM-JA' => array(
      'country' => 'UM',
      'title' => /*t(*/'Johnston Atoll'/*)*/,
      'tag' => '0'
    ),
    'UM-JI' => array(
      'country' => 'UM',
      'title' => /*t(*/'Jarvis Island'/*)*/,
      'tag' => '0'
    ),
    'UM-KR' => array(
      'country' => 'UM',
      'title' => /*t(*/'Kingman Reef'/*)*/,
      'tag' => '0'
    ),
    'UM-MA' => array(
      'country' => 'UM',
      'title' => /*t(*/'Midway Atoll'/*)*/,
      'tag' => '0'
    ),
    'UM-NI' => array(
      'country' => 'UM',
      'title' => /*t(*/'Navassa Island'/*)*/,
      'tag' => '0'
    ),
    'UM-PA' => array(
      'country' => 'UM',
      'title' => /*t(*/'Palmyra Atoll'/*)*/,
      'tag' => '0'
    ),
    'UM-WI' => array('country' => 'UM', 'title' => /*t(*/'Wake Island'/*)*/, 'tag' => '0'),
    'US-AK' => array('country' => 'US', 'title' => /*t(*/'Alaska'/*)*/, 'tag' => '0'),
    'US-AL' => array('country' => 'US', 'title' => /*t(*/'Alabama'/*)*/, 'tag' => '0'),
    'US-AR' => array('country' => 'US', 'title' => /*t(*/'Arkansas'/*)*/, 'tag' => '0'),
    'US-AS' => array(
      'country' => 'US',
      'title' => /*t(*/'American Samoa'/*)*/,
      'tag' => '0'
    ),
    'US-AZ' => array('country' => 'US', 'title' => /*t(*/'Arizona'/*)*/, 'tag' => '0'),
    'US-CA' => array('country' => 'US', 'title' => /*t(*/'California'/*)*/, 'tag' => '0'),
    'US-CO' => array('country' => 'US', 'title' => /*t(*/'Colorado'/*)*/, 'tag' => '0'),
    'US-CT' => array('country' => 'US', 'title' => /*t(*/'Connecticut'/*)*/, 'tag' => '0'),
    'US-DC' => array(
      'country' => 'US',
      'title' => /*t(*/'District of Columbia'/*)*/,
      'tag' => '0'
    ),
    'US-DE' => array('country' => 'US', 'title' => /*t(*/'Delaware'/*)*/, 'tag' => '0'),
    'US-FL' => array('country' => 'US', 'title' => /*t(*/'Florida'/*)*/, 'tag' => '0'),
    'US-GA' => array('country' => 'US', 'title' => /*t(*/'Georgia'/*)*/, 'tag' => '0'),
    'US-GU' => array('country' => 'US', 'title' => /*t(*/'Guam'/*)*/, 'tag' => '0'),
    'US-HI' => array('country' => 'US', 'title' => /*t(*/'Hawaii'/*)*/, 'tag' => '0'),
    'US-IA' => array('country' => 'US', 'title' => /*t(*/'Iowa'/*)*/, 'tag' => '0'),
    'US-ID' => array('country' => 'US', 'title' => /*t(*/'Idaho'/*)*/, 'tag' => '0'),
    'US-IL' => array('country' => 'US', 'title' => /*t(*/'Illinois'/*)*/, 'tag' => '0'),
    'US-IN' => array('country' => 'US', 'title' => /*t(*/'Indiana'/*)*/, 'tag' => '0'),
    'US-KS' => array('country' => 'US', 'title' => /*t(*/'Kansas'/*)*/, 'tag' => '0'),
    'US-KY' => array('country' => 'US', 'title' => /*t(*/'Kentucky'/*)*/, 'tag' => '0'),
    'US-LA' => array('country' => 'US', 'title' => /*t(*/'Louisiana'/*)*/, 'tag' => '0'),
    'US-MA' => array(
      'country' => 'US',
      'title' => /*t(*/'Massachusetts'/*)*/,
      'tag' => '0'
    ),
    'US-MD' => array('country' => 'US', 'title' => /*t(*/'Maryland'/*)*/, 'tag' => '0'),
    'US-ME' => array('country' => 'US', 'title' => /*t(*/'Maine'/*)*/, 'tag' => '0'),
    'US-MI' => array('country' => 'US', 'title' => /*t(*/'Michigan'/*)*/, 'tag' => '0'),
    'US-MN' => array('country' => 'US', 'title' => /*t(*/'Minnesota'/*)*/, 'tag' => '0'),
    'US-MO' => array('country' => 'US', 'title' => /*t(*/'Missouri'/*)*/, 'tag' => '0'),
    'US-MP' => array(
      'country' => 'US',
      'title' => /*t(*/'Northern Mariana Islands'/*)*/,
      'tag' => '0'
    ),
    'US-MS' => array('country' => 'US', 'title' => /*t(*/'Mississippi'/*)*/, 'tag' => '0'),
    'US-MT' => array('country' => 'US', 'title' => /*t(*/'Montana'/*)*/, 'tag' => '0'),
    'US-NC' => array(
      'country' => 'US',
      'title' => /*t(*/'North Carolina'/*)*/,
      'tag' => '0'
    ),
    'US-ND' => array(
      'country' => 'US',
      'title' => /*t(*/'North Dakota'/*)*/,
      'tag' => '0'
    ),
    'US-NE' => array('country' => 'US', 'title' => /*t(*/'Nebraska'/*)*/, 'tag' => '0'),
    'US-NH' => array(
      'country' => 'US',
      'title' => /*t(*/'New Hampshire'/*)*/,
      'tag' => '0'
    ),
    'US-NJ' => array('country' => 'US', 'title' => /*t(*/'New Jersey'/*)*/, 'tag' => '0'),
    'US-NM' => array('country' => 'US', 'title' => /*t(*/'New Mexico'/*)*/, 'tag' => '0'),
    'US-NV' => array('country' => 'US', 'title' => /*t(*/'Nevada'/*)*/, 'tag' => '0'),
    'US-NY' => array('country' => 'US', 'title' => /*t(*/'New York'/*)*/, 'tag' => '0'),
    'US-OH' => array('country' => 'US', 'title' => /*t(*/'Ohio'/*)*/, 'tag' => '0'),
    'US-OK' => array('country' => 'US', 'title' => /*t(*/'Oklahoma'/*)*/, 'tag' => '0'),
    'US-OR' => array('country' => 'US', 'title' => /*t(*/'Oregon'/*)*/, 'tag' => '0'),
    'US-PA' => array(
      'country' => 'US',
      'title' => /*t(*/'Pennsylvania'/*)*/,
      'tag' => '0'
    ),
    'US-PR' => array('country' => 'US', 'title' => /*t(*/'Puerto Rico'/*)*/, 'tag' => '0'),
    'US-RI' => array(
      'country' => 'US',
      'title' => /*t(*/'Rhode Island'/*)*/,
      'tag' => '0'
    ),
    'US-SC' => array(
      'country' => 'US',
      'title' => /*t(*/'South Carolina'/*)*/,
      'tag' => '0'
    ),
    'US-SD' => array(
      'country' => 'US',
      'title' => /*t(*/'South Dakota'/*)*/,
      'tag' => '0'
    ),
    'US-TN' => array('country' => 'US', 'title' => /*t(*/'Tennessee'/*)*/, 'tag' => '0'),
    'US-TX' => array('country' => 'US', 'title' => /*t(*/'Texas'/*)*/, 'tag' => '0'),
    'US-UM' => array(
      'country' => 'US',
      'title' => /*t(*/'U.S. Minor Outlying Islands'/*)*/,
      'tag' => '0'
    ),
    'US-UT' => array('country' => 'US', 'title' => /*t(*/'Utah'/*)*/, 'tag' => '0'),
    'US-VA' => array('country' => 'US', 'title' => /*t(*/'Virginia'/*)*/, 'tag' => '0'),
    'US-VI' => array(
      'country' => 'US',
      'title' => /*t(*/'Virgin Islands of the U.S.'/*)*/,
      'tag' => '0'
    ),
    'US-VT' => array('country' => 'US', 'title' => /*t(*/'Vermont'/*)*/, 'tag' => '0'),
    'US-WA' => array('country' => 'US', 'title' => /*t(*/'Washington'/*)*/, 'tag' => '0'),
    'US-WI' => array('country' => 'US', 'title' => /*t(*/'Wisconsin'/*)*/, 'tag' => '0'),
    'US-WV' => array(
      'country' => 'US',
      'title' => /*t(*/'West Virginia'/*)*/,
      'tag' => '0'
    ),
    'US-WY' => array('country' => 'US', 'title' => /*t(*/'Wyoming'/*)*/, 'tag' => '0'),
    'UY-AR' => array('country' => 'UY', 'title' => /*t(*/'Artigas'/*)*/, 'tag' => '0'),
    'UY-CA' => array('country' => 'UY', 'title' => /*t(*/'Canelones'/*)*/, 'tag' => '0'),
    'UY-CL' => array('country' => 'UY', 'title' => /*t(*/'Cerro Largo'/*)*/, 'tag' => '0'),
    'UY-CO' => array('country' => 'UY', 'title' => /*t(*/'Colonia'/*)*/, 'tag' => '0'),
    'UY-DU' => array('country' => 'UY', 'title' => /*t(*/'Durazno'/*)*/, 'tag' => '0'),
    'UY-FA' => array('country' => 'UY', 'title' => /*t(*/'Florida'/*)*/, 'tag' => '0'),
    'UY-FS' => array('country' => 'UY', 'title' => /*t(*/'Flores'/*)*/, 'tag' => '0'),
    'UY-LA' => array('country' => 'UY', 'title' => /*t(*/'Lavalleja'/*)*/, 'tag' => '0'),
    'UY-MA' => array('country' => 'UY', 'title' => /*t(*/'Maldonado'/*)*/, 'tag' => '0'),
    'UY-MO' => array('country' => 'UY', 'title' => /*t(*/'Montevideo'/*)*/, 'tag' => '0'),
    'UY-PA' => array('country' => 'UY', 'title' => /*t(*/'Paysandu'/*)*/, 'tag' => '0'),
    'UY-RN' => array('country' => 'UY', 'title' => /*t(*/'Rio Negro'/*)*/, 'tag' => '0'),
    'UY-RO' => array('country' => 'UY', 'title' => /*t(*/'Rocha'/*)*/, 'tag' => '0'),
    'UY-RV' => array('country' => 'UY', 'title' => /*t(*/'Rivera'/*)*/, 'tag' => '0'),
    'UY-SJ' => array('country' => 'UY', 'title' => /*t(*/'San Jose'/*)*/, 'tag' => '0'),
    'UY-SL' => array('country' => 'UY', 'title' => /*t(*/'Salto'/*)*/, 'tag' => '0'),
    'UY-SO' => array('country' => 'UY', 'title' => /*t(*/'Soriano'/*)*/, 'tag' => '0'),
    'UY-TT' => array(
      'country' => 'UY',
      'title' => /*t(*/'Treinta y Tres'/*)*/,
      'tag' => '0'
    ),
    'UZ-AN' => array('country' => 'UZ', 'title' => /*t(*/'Andijon'/*)*/, 'tag' => '0'),
    'UZ-BU' => array('country' => 'UZ', 'title' => /*t(*/'Buxoro'/*)*/, 'tag' => '0'),
    'UZ-FA' => array('country' => 'UZ', 'title' => /*t(*/"Farg'ona"/*)*/, 'tag' => '0'),
    'UZ-JI' => array('country' => 'UZ', 'title' => /*t(*/'Jizzax'/*)*/, 'tag' => '0'),
    'UZ-NG' => array('country' => 'UZ', 'title' => /*t(*/'Namangan'/*)*/, 'tag' => '0'),
    'UZ-NW' => array('country' => 'UZ', 'title' => /*t(*/'Navoiy'/*)*/, 'tag' => '0'),
    'UZ-QA' => array('country' => 'UZ', 'title' => /*t(*/'Qashqadaryo'/*)*/, 'tag' => '0'),
    'UZ-QR' => array(
      'country' => 'UZ',
      'title' => /*t(*/"Qoraqalpog'iston Republikasi"/*)*/,
      'tag' => '0'
    ),
    'UZ-SA' => array('country' => 'UZ', 'title' => /*t(*/'Samarqand'/*)*/, 'tag' => '0'),
    'UZ-SI' => array('country' => 'UZ', 'title' => /*t(*/'Sirdaryo'/*)*/, 'tag' => '0'),
    'UZ-SU' => array('country' => 'UZ', 'title' => /*t(*/'Surxondaryo'/*)*/, 'tag' => '0'),
    'UZ-TK' => array(
      'country' => 'UZ',
      'title' => /*t(*/'Toshkent city'/*)*/,
      'tag' => '0'
    ),
    'UZ-TO' => array(
      'country' => 'UZ',
      'title' => /*t(*/'Toshkent region'/*)*/,
      'tag' => '0'
    ),
    'UZ-XO' => array('country' => 'UZ', 'title' => /*t(*/'Xorazm'/*)*/, 'tag' => '0'),
    'VC-A' => array('country' => 'VC', 'title' => /*t(*/'Saint Andrew'/*)*/, 'tag' => '0'),
    'VC-C' => array('country' => 'VC', 'title' => /*t(*/'Charlotte'/*)*/, 'tag' => '0'),
    'VC-D' => array('country' => 'VC', 'title' => /*t(*/'Saint David'/*)*/, 'tag' => '0'),
    'VC-G' => array('country' => 'VC', 'title' => /*t(*/'Saint George'/*)*/, 'tag' => '0'),
    'VC-P' => array(
      'country' => 'VC',
      'title' => /*t(*/'Saint Patrick'/*)*/,
      'tag' => '0'
    ),
    'VC-R' => array('country' => 'VC', 'title' => /*t(*/'Grenadines'/*)*/, 'tag' => '0'),
    'VE-A' => array(
      'country' => 'VE',
      'title' => /*t(*/'Federal District'/*)*/,
      'tag' => '0'
    ),
    'VE-B' => array('country' => 'VE', 'title' => /*t(*/'Anzoategui'/*)*/, 'tag' => '0'),
    'VE-C' => array('country' => 'VE', 'title' => /*t(*/'Apure'/*)*/, 'tag' => '0'),
    'VE-D' => array('country' => 'VE', 'title' => /*t(*/'Aragua'/*)*/, 'tag' => '0'),
    'VE-E' => array('country' => 'VE', 'title' => /*t(*/'Barinas'/*)*/, 'tag' => '0'),
    'VE-F' => array('country' => 'VE', 'title' => /*t(*/'Bolivar'/*)*/, 'tag' => '0'),
    'VE-G' => array('country' => 'VE', 'title' => /*t(*/'Carabobo'/*)*/, 'tag' => '0'),
    'VE-H' => array('country' => 'VE', 'title' => /*t(*/'Cojedes'/*)*/, 'tag' => '0'),
    'VE-I' => array('country' => 'VE', 'title' => /*t(*/'Falcon'/*)*/, 'tag' => '0'),
    'VE-J' => array('country' => 'VE', 'title' => /*t(*/'Guarico'/*)*/, 'tag' => '0'),
    'VE-K' => array('country' => 'VE', 'title' => /*t(*/'Lara'/*)*/, 'tag' => '0'),
    'VE-L' => array('country' => 'VE', 'title' => /*t(*/'Merida'/*)*/, 'tag' => '0'),
    'VE-M' => array('country' => 'VE', 'title' => /*t(*/'Miranda'/*)*/, 'tag' => '0'),
    'VE-N' => array('country' => 'VE', 'title' => /*t(*/'Monagas'/*)*/, 'tag' => '0'),
    'VE-O' => array(
      'country' => 'VE',
      'title' => /*t(*/'Nueva Esparta'/*)*/,
      'tag' => '0'
    ),
    'VE-P' => array('country' => 'VE', 'title' => /*t(*/'Portuguesa'/*)*/, 'tag' => '0'),
    'VE-R' => array('country' => 'VE', 'title' => /*t(*/'Sucre'/*)*/, 'tag' => '0'),
    'VE-S' => array('country' => 'VE', 'title' => /*t(*/'Tachira'/*)*/, 'tag' => '0'),
    'VE-T' => array('country' => 'VE', 'title' => /*t(*/'Trujillo'/*)*/, 'tag' => '0'),
    'VE-U' => array('country' => 'VE', 'title' => /*t(*/'Yaracuy'/*)*/, 'tag' => '0'),
    'VE-V' => array('country' => 'VE', 'title' => /*t(*/'Zulia'/*)*/, 'tag' => '0'),
    'VE-W' => array(
      'country' => 'VE',
      'title' => /*t(*/'Federal Dependency'/*)*/,
      'tag' => '0'
    ),
    'VE-X' => array('country' => 'VE', 'title' => /*t(*/'Vargas'/*)*/, 'tag' => '0'),
    'VE-Y' => array(
      'country' => 'VE',
      'title' => /*t(*/'Delta Amacuro'/*)*/,
      'tag' => '0'
    ),
    'VE-Z' => array('country' => 'VE', 'title' => /*t(*/'Amazonas'/*)*/, 'tag' => '0'),
    'VI-C' => array('country' => 'VI', 'title' => /*t(*/'Saint Croix'/*)*/, 'tag' => '0'),
    'VI-J' => array('country' => 'VI', 'title' => /*t(*/'Saint John'/*)*/, 'tag' => '0'),
    'VI-T' => array('country' => 'VI', 'title' => /*t(*/'Saint Thomas'/*)*/, 'tag' => '0'),
    'VN-AG' => array('country' => 'VN', 'title' => /*t(*/'An Giang'/*)*/, 'tag' => '0'),
    'VN-BC' => array('country' => 'VN', 'title' => /*t(*/'Bac Ninh'/*)*/, 'tag' => '0'),
    'VN-BG' => array('country' => 'VN', 'title' => /*t(*/'Bac Giang'/*)*/, 'tag' => '0'),
    'VN-BH' => array('country' => 'VN', 'title' => /*t(*/'Binh Dinh'/*)*/, 'tag' => '0'),
    'VN-BK' => array('country' => 'VN', 'title' => /*t(*/'Bac Kan'/*)*/, 'tag' => '0'),
    'VN-BL' => array('country' => 'VN', 'title' => /*t(*/'Bac Lieu'/*)*/, 'tag' => '0'),
    'VN-BN' => array('country' => 'VN', 'title' => /*t(*/'Ben Tre'/*)*/, 'tag' => '0'),
    'VN-BP' => array('country' => 'VN', 'title' => /*t(*/'Binh Phuoc'/*)*/, 'tag' => '0'),
    'VN-BR' => array(
      'country' => 'VN',
      'title' => /*t(*/'Ba Ria-Vung Tau'/*)*/,
      'tag' => '0'
    ),
    'VN-BT' => array('country' => 'VN', 'title' => /*t(*/'Binh Thuan'/*)*/, 'tag' => '0'),
    'VN-BU' => array('country' => 'VN', 'title' => /*t(*/'Binh Duong'/*)*/, 'tag' => '0'),
    'VN-CB' => array('country' => 'VN', 'title' => /*t(*/'Cao Bang'/*)*/, 'tag' => '0'),
    'VN-CM' => array('country' => 'VN', 'title' => /*t(*/'Ca Mau'/*)*/, 'tag' => '0'),
    'VN-CT' => array('country' => 'VN', 'title' => /*t(*/'Can Tho'/*)*/, 'tag' => '0'),
    'VN-DB' => array('country' => 'VN', 'title' => /*t(*/'Dien Bien'/*)*/, 'tag' => '0'),
    'VN-DG' => array('country' => 'VN', 'title' => /*t(*/'Dak Nong'/*)*/, 'tag' => '0'),
    'VN-DI' => array('country' => 'VN', 'title' => /*t(*/'Dong Nai'/*)*/, 'tag' => '0'),
    'VN-DL' => array('country' => 'VN', 'title' => /*t(*/'Dak Lak'/*)*/, 'tag' => '0'),
    'VN-DN' => array('country' => 'VN', 'title' => /*t(*/'Da Nang'/*)*/, 'tag' => '0'),
    'VN-DT' => array('country' => 'VN', 'title' => /*t(*/'Dong Thap'/*)*/, 'tag' => '0'),
    'VN-GL' => array('country' => 'VN', 'title' => /*t(*/'Gia Lai'/*)*/, 'tag' => '0'),
    'VN-HB' => array('country' => 'VN', 'title' => /*t(*/'Hoa Binh'/*)*/, 'tag' => '0'),
    'VN-HC' => array(
      'country' => 'VN',
      'title' => /*t(*/'Ho Chin Minh'/*)*/,
      'tag' => '0'
    ),
    'VN-HD' => array('country' => 'VN', 'title' => /*t(*/'Hai Duong'/*)*/, 'tag' => '0'),
    'VN-HG' => array('country' => 'VN', 'title' => /*t(*/'Ha Giang'/*)*/, 'tag' => '0'),
    'VN-HH' => array('country' => 'VN', 'title' => /*t(*/'Ha Tinh'/*)*/, 'tag' => '0'),
    'VN-HI' => array('country' => 'VN', 'title' => /*t(*/'Ha Noi'/*)*/, 'tag' => '0'),
    'VN-HM' => array('country' => 'VN', 'title' => /*t(*/'Ha Nam'/*)*/, 'tag' => '0'),
    'VN-HP' => array('country' => 'VN', 'title' => /*t(*/'Hai Phong'/*)*/, 'tag' => '0'),
    'VN-HT' => array('country' => 'VN', 'title' => /*t(*/'Ha Tay'/*)*/, 'tag' => '0'),
    'VN-HU' => array('country' => 'VN', 'title' => /*t(*/'Hau Giang'/*)*/, 'tag' => '0'),
    'VN-HY' => array('country' => 'VN', 'title' => /*t(*/'Hung Yen'/*)*/, 'tag' => '0'),
    'VU-MA' => array('country' => 'VU', 'title' => /*t(*/'Malampa'/*)*/, 'tag' => '0'),
    'VU-PE' => array('country' => 'VU', 'title' => /*t(*/'Penama'/*)*/, 'tag' => '0'),
    'VU-SA' => array('country' => 'VU', 'title' => /*t(*/'Sanma'/*)*/, 'tag' => '0'),
    'VU-SH' => array('country' => 'VU', 'title' => /*t(*/'Shefa'/*)*/, 'tag' => '0'),
    'VU-TA' => array('country' => 'VU', 'title' => /*t(*/'Tafea'/*)*/, 'tag' => '0'),
    'VU-TO' => array('country' => 'VU', 'title' => /*t(*/'Torba'/*)*/, 'tag' => '0'),
    'WF-A' => array('country' => 'WF', 'title' => /*t(*/'Alo'/*)*/, 'tag' => '0'),
    'WF-S' => array('country' => 'WF', 'title' => /*t(*/'Sigave'/*)*/, 'tag' => '0'),
    'WF-W' => array('country' => 'WF', 'title' => /*t(*/'Wallis'/*)*/, 'tag' => '0'),
    'WS-AI' => array(
      'country' => 'WS',
      'title' => /*t(*/'Aiga-i-le-Tai'/*)*/,
      'tag' => '0'
    ),
    'WS-AN' => array('country' => 'WS', 'title' => /*t(*/"A'ana"/*)*/, 'tag' => '0'),
    'WS-AT' => array('country' => 'WS', 'title' => /*t(*/'Atua'/*)*/, 'tag' => '0'),
    'WS-FA' => array(
      'country' => 'WS',
      'title' => /*t(*/"Fa'asaleleaga"/*)*/,
      'tag' => '0'
    ),
    'WS-GE' => array(
      'country' => 'WS',
      'title' => /*t(*/"Gaga'emauga"/*)*/,
      'tag' => '0'
    ),
    'WS-GF' => array(
      'country' => 'WS',
      'title' => /*t(*/'Gagaifomauga'/*)*/,
      'tag' => '0'
    ),
    'WS-PA' => array('country' => 'WS', 'title' => /*t(*/'Palauli'/*)*/, 'tag' => '0'),
    'WS-SA' => array(
      'country' => 'WS',
      'title' => /*t(*/"Satupa'itea"/*)*/,
      'tag' => '0'
    ),
    'WS-TU' => array('country' => 'WS', 'title' => /*t(*/'Tuamasaga'/*)*/, 'tag' => '0'),
    'WS-VF' => array(
      'country' => 'WS',
      'title' => /*t(*/"Va'a-o-Fonoti"/*)*/,
      'tag' => '0'
    ),
    'WS-VS' => array('country' => 'WS', 'title' => /*t(*/'Vaisigano'/*)*/, 'tag' => '0'),
    'YE-AB' => array('country' => 'YE', 'title' => /*t(*/'Abyan'/*)*/, 'tag' => '0'),
    'YE-AD' => array('country' => 'YE', 'title' => /*t(*/'Adan'/*)*/, 'tag' => '0'),
    'YE-AM' => array('country' => 'YE', 'title' => /*t(*/'Amran'/*)*/, 'tag' => '0'),
    'YE-BA' => array('country' => 'YE', 'title' => /*t(*/'Al Bayda'/*)*/, 'tag' => '0'),
    'YE-DA' => array('country' => 'YE', 'title' => /*t(*/'Ad Dali'/*)*/, 'tag' => '0'),
    'YE-DH' => array('country' => 'YE', 'title' => /*t(*/'Dhamar'/*)*/, 'tag' => '0'),
    'YE-HD' => array('country' => 'YE', 'title' => /*t(*/'Hadramawt'/*)*/, 'tag' => '0'),
    'YE-HJ' => array('country' => 'YE', 'title' => /*t(*/'Hajjah'/*)*/, 'tag' => '0'),
    'YE-HU' => array('country' => 'YE', 'title' => /*t(*/'Al Hudaydah'/*)*/, 'tag' => '0'),
    'YE-IB' => array('country' => 'YE', 'title' => /*t(*/'Ibb'/*)*/, 'tag' => '0'),
    'YE-JA' => array('country' => 'YE', 'title' => /*t(*/'Al Jawf'/*)*/, 'tag' => '0'),
    'YE-LA' => array('country' => 'YE', 'title' => /*t(*/'Lahij'/*)*/, 'tag' => '0'),
    'YE-MA' => array('country' => 'YE', 'title' => /*t(*/"Ma'rib"/*)*/, 'tag' => '0'),
    'YE-MR' => array('country' => 'YE', 'title' => /*t(*/'Al Mahrah'/*)*/, 'tag' => '0'),
    'YE-MW' => array('country' => 'YE', 'title' => /*t(*/'Al Mahwit'/*)*/, 'tag' => '0'),
    'YE-SD' => array('country' => 'YE', 'title' => /*t(*/"Sa'dah"/*)*/, 'tag' => '0'),
    'YE-SH' => array('country' => 'YE', 'title' => /*t(*/'Shabwah'/*)*/, 'tag' => '0'),
    'YE-SN' => array('country' => 'YE', 'title' => /*t(*/"San'a"/*)*/, 'tag' => '0'),
    'YE-TA' => array('country' => 'YE', 'title' => /*t(*/"Ta'izz"/*)*/, 'tag' => '0'),
    'ZA-EC' => array(
      'country' => 'ZA',
      'title' => /*t(*/'Eastern Cape'/*)*/,
      'tag' => '0'
    ),
    'ZA-FS' => array('country' => 'ZA', 'title' => /*t(*/'Free State'/*)*/, 'tag' => '0'),
    'ZA-GT' => array('country' => 'ZA', 'title' => /*t(*/'Gauteng'/*)*/, 'tag' => '0'),
    'ZA-KN' => array(
      'country' => 'ZA',
      'title' => /*t(*/'KwaZulu-Natal'/*)*/,
      'tag' => '0'
    ),
    'ZA-LP' => array('country' => 'ZA', 'title' => /*t(*/'Limpopo'/*)*/, 'tag' => '0'),
    'ZA-MP' => array('country' => 'ZA', 'title' => /*t(*/'Mpumalanga'/*)*/, 'tag' => '0'),
    'ZA-NC' => array(
      'country' => 'ZA',
      'title' => /*t(*/'Northern Cape'/*)*/,
      'tag' => '0'
    ),
    'ZA-NW' => array('country' => 'ZA', 'title' => /*t(*/'North West'/*)*/, 'tag' => '0'),
    'ZA-WC' => array(
      'country' => 'ZA',
      'title' => /*t(*/'Western Cape'/*)*/,
      'tag' => '0'
    ),
    'ZM-CB' => array(
      'country' => 'ZM',
      'title' => /*t(*/'Copperbelt Province'/*)*/,
      'tag' => '0'
    ),
    'ZM-CE' => array(
      'country' => 'ZM',
      'title' => /*t(*/'Central Province'/*)*/,
      'tag' => '0'
    ),
    'ZM-EA' => array(
      'country' => 'ZM',
      'title' => /*t(*/'Eastern Province'/*)*/,
      'tag' => '0'
    ),
    'ZM-LK' => array(
      'country' => 'ZM',
      'title' => /*t(*/'Lusaka Province'/*)*/,
      'tag' => '0'
    ),
    'ZM-LP' => array(
      'country' => 'ZM',
      'title' => /*t(*/'Luapula Province'/*)*/,
      'tag' => '0'
    ),
    'ZM-NO' => array(
      'country' => 'ZM',
      'title' => /*t(*/'Northern Province'/*)*/,
      'tag' => '0'
    ),
    'ZM-NW' => array(
      'country' => 'ZM',
      'title' => /*t(*/'North-Western Province'/*)*/,
      'tag' => '0'
    ),
    'ZM-SO' => array(
      'country' => 'ZM',
      'title' => /*t(*/'Southern Province'/*)*/,
      'tag' => '0'
    ),
    'ZM-WE' => array(
      'country' => 'ZM',
      'title' => /*t(*/'Western Province'/*)*/,
      'tag' => '0'
    ),
    'ZW-BU' => array(
      'country' => 'ZW',
      'title' => /*t(*/'Bulawayo (city)'/*)*/,
      'tag' => '0'
    ),
    'ZW-HA' => array(
      'country' => 'ZW',
      'title' => /*t(*/'Harare (city)'/*)*/,
      'tag' => '0'
    ),
    'ZW-MC' => array(
      'country' => 'ZW',
      'title' => /*t(*/'Mashonaland Central'/*)*/,
      'tag' => '0'
    ),
    'ZW-MD' => array('country' => 'ZW', 'title' => /*t(*/'Midlands'/*)*/, 'tag' => '0'),
    'ZW-ME' => array(
      'country' => 'ZW',
      'title' => /*t(*/'Mashonaland East'/*)*/,
      'tag' => '0'
    ),
    'ZW-ML' => array('country' => 'ZW', 'title' => /*t(*/'Manicaland'/*)*/, 'tag' => '0'),
    'ZW-MN' => array(
      'country' => 'ZW',
      'title' => /*t(*/'Matabeleland North'/*)*/,
      'tag' => '0'
    ),
    'ZW-MS' => array(
      'country' => 'ZW',
      'title' => /*t(*/'Matabeleland South'/*)*/,
      'tag' => '0'
    ),
    'ZW-MV' => array('country' => 'ZW', 'title' => /*t(*/'Masvingo'/*)*/, 'tag' => '0'),
    'ZW-MW' => array(
      'country' => 'ZW',
      'title' => /*t(*/'Mashonaland West'/*)*/,
      'tag' => '0'
    )
  );

  if (!empty($forcountry)) {
    $filtered = array();
    foreach ($amember_states as $state => $item) {
      if ($item['country'] == $forcountry) {
        $filtered[$state] = $item;
      }
    }
    $amember_states = $filtered;
  }

  //TODO allow to modify by baseconfig (e.g for klickmail)
  // define ('STATES_OVVERIDE', array('DE' => array('title' => 'Germany', 'weight' => '0)));
  // $amember_countries = array_merge($amember_countries, STATES_OVVERIDE);

  // sort by 'weight' DESC
  //TODO no need to sort at the very moment (all weights 0)
  //klicktipp_uasort($amember_states, array('weight' => 'DESC'));

  return $amember_states;
}



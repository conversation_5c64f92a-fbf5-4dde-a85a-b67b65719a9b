<?php

use App\Klicktipp\BlacklistHandler;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\Dates;
use App\Klicktipp\Errors;
use App\Klicktipp\Lists;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\Settings;
use App\Klicktipp\Statistics;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\ToolCountdown;

function klicktipp_tools_countdown_create_form($form, $form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Create Countdown');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Countdowns'), "tools/$UserID/countdowns")), $page_title);

  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $weight = 1;

  $form = array();

  // --- check if the count tagging pixel limit has been reached ---
  $MaxTools = ToolCountdown::LimitReached($UserID);
  if ($MaxTools !== FALSE) {

    $content = _klicktipp_get_content_include('klicktipp_content_include_countdown_limit_upsell');

    $form['LimitExeeded'] = array(
      '#type' => 'markup',
      '#value' => ($content) ? $content : t("You have reached the maximum number of %count Countdown(s). If you need more Countdown, please upgrade your membership.", array('%count' => $MaxTools)),
    );

    return $form;

  }

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => '',
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? [] : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
      'container_class' => 'klicktipp-magicselect-short',
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Create Countdown'),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "tools/$UserID/countdowns",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_tools_countdown_create_form_validate($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];

  if (ToolCountdown::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Countdown with the name %name already exists.', array(
      '%name' => $Name,
    )));
  }

}

function klicktipp_tools_countdown_create_form_submit($form, &$form_state) {

  $Name = $form_state['values']['Name'];
  $UserID = $form_state['values']['uid'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $NewToolID = ToolCountdown::InsertDB(array(
    'Name' => $Name,
    'RelOwnerUserID' => $UserID,
    DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
  ));

  if (!empty($NewToolID)) {

    drupal_set_message(t("Countdown %name successfully created.", array('%name' => $Name)));

    // redirect to countdown edit
    klicktipp_set_redirect($form_state, "tools/$UserID/countdown/$NewToolID/edit");

  }
  else {

    $error = array(
      '!uid' => $UserID,
      '!name' => $Name,
    );

    //notify user
    Errors::unexpected("Countdown: Create for UserID: !uid with name !name", $error, TRUE);

  }

}

function klicktipp_tools_countdown_edit_form ($form, &$form_state, $account, $ToolID ) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Edit Countdown');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Marketing Tools'), "tools/$UserID")), $page_title);

  $weight = 1;

  $form = array();

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  /** @var ToolCountdown $Countdown */
  $Countdown = ToolCountdown::FromID($UserID, $ToolID);

  if ( empty($Countdown) ) {

    drupal_set_message(t("Countdown not found."), 'error');
    drupal_goto("tools/$UserID");
    return NULL;

  }

  $form['#pre_render'][] = 'klicktipp_tools_countdown_edit_form_pre_render';

  $form['Countdown'] = array(
    '#type' => 'value',
    '#value' => $Countdown,
  );

  $ArrayCountdown = $Countdown->GetData();

  $Labels = $Countdown->GetMetaLabels();
  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => $ArrayCountdown['Name'],
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? $Labels : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  //t('round_orange')
  //t('round_orange')
  //t('round_purple')
  //t('round_blue')
  //t('round_green')
  //t('round_lightgreen')
  //t('round_turquoise')
  //t('round_grey')
  $template_options = array();
  foreach (Settings::get('marketing_tools_emailcountdown_templates') as $template ) {
    $template_options[$template] = t(/*ignore*/$template);
  }

  $form['Template'] = array(
    '#type' => 'select',
    '#title' => t('Template'),
    '#default_value' => $ArrayCountdown['Template'],
    '#options' => $template_options,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_show_preview_countdown',
      'data-event-change' => 'js_show_preview_countdown',
    ),
  );

  $preview = Settings::get('marketing_tools_emailcountdown_s3url') . "/{$ArrayCountdown['Template']}/1/{$ArrayCountdown['Template']}_120.gif";
  $form['TemplatePreview'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_info',
    '#value' => "<img src='$preview' class='templatepreview-image' />",
    '#weight' => $weight++,
  );

  $form['TerminationType'] = array(
    '#type' => 'select',
    '#title' => t('Termination datetime'),
    '#default_value' => (empty($ArrayCountdown['TerminationType'])) ? ToolCountdown::TERMINATION_TYPE_FIXED : $ArrayCountdown['TerminationType'],
    '#options' => array(
      ToolCountdown::TERMINATION_TYPE_FIXED => t(/*ignore*/ToolCountdown::$DisplayTerminationType[ToolCountdown::TERMINATION_TYPE_FIXED]),
      ToolCountdown::TERMINATION_TYPE_FROM_CUSTOMFIELD => t(/*ignore*/ToolCountdown::$DisplayTerminationType[ToolCountdown::TERMINATION_TYPE_FROM_CUSTOMFIELD]),
      ToolCountdown::TERMINATION_TYPE_FROM_TAGGING => t(/*ignore*/ToolCountdown::$DisplayTerminationType[ToolCountdown::TERMINATION_TYPE_FROM_TAGGING]),
    ),
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_change_termination_date',
      'data-event-change' => 'js_change_termination_date',
    ),
  );

  $form['TerminationDateTime'] = array(
    '#type' => 'markup',
    '#prefix' => '<div class="edit-TerminationDateTime klicktipp-widget-datetime">',
    '#suffix' => '</div>',
    '#weight' => $weight++,
    '#tree' => TRUE,
  );

  $date = (empty($ArrayCountdown['TerminationDateTime'])) ? "" : Dates::formatDate(Dates::FORMAT_DMY_DATEPICKER_PHP, (int) $ArrayCountdown['TerminationDateTime']);
  $form['TerminationDateTime']['Date'] = array(
    '#type' => 'textfield',
    '#default_value' => $date,
    '#weight' => $weight++,
    '#datepicker' => TRUE,
  );

  $form['TerminationDateTime']['at'] = array(
    '#type' => 'markup',
    '#markup' => '<span class="datetime">&nbsp;' . t('at(time)') . '&nbsp;</span>',
    '#weight' => $weight++,
  );

  $Hours = empty($ArrayCountdown['TerminationDateTime']) ? '00' : date('H', $ArrayCountdown['TerminationDateTime']);
  $HourOptions = array();
  for ($i = 0; $i < 24; $i++) {
    $s = sprintf('%02d', $i);
    $HourOptions[$s] = $s;
  }

  $form['TerminationDateTime']['Hours'] = array(
    '#type' => 'select',
    '#default_value' => $Hours,
    '#options' => $HourOptions,
    '#attributes' => array('class' => array('datetime-hours')),
    '#suffix' => '<span class="datetime"> : </span>',
    '#weight' => $weight++,
  );

  $Minutes = empty($ArrayCountdown['TerminationDateTime']) ? '00' : date('i', $ArrayCountdown['TerminationDateTime']);
  $MinuteOptions = array();
  for ($i = 0; $i < 60; $i++) {
    $s = sprintf('%02d', $i);
    $MinuteOptions[$s] = $s;
  }
  $form['TerminationDateTime']['Minutes'] = array(
    '#type' => 'select',
    '#default_value' => $Minutes,
    '#options' => $MinuteOptions,
    '#attributes' => array('class' => array('datetime-minutes')),
    '#weight' => $weight++,
  );

  //retrieve datetime custom fields of user
  $query = "SELECT CustomFieldID, FieldName FROM {custom_fields} WHERE RelOwnerUserID = :RelOwnerUserID AND FieldTypeEnum = :FieldTypeEnum";
  $params = array(':RelOwnerUserID' => $UserID, ':FieldTypeEnum' => CustomFields::TYPE_DATETIME);
  $result = db_query($query, $params);
  $CustomFieldOptions = array('' => 'Please select');
  while ( $cf = kt_fetch_array($result)) {
    $CustomFieldOptions[$cf['CustomFieldID']] = $cf['FieldName'];
  }

  if ( count($CustomFieldOptions) > 1 ) {

    $form['TerminationCustomFieldID'] = array(
      '#type' => 'select',
      '#default_value' => $ArrayCountdown['TerminationCustomFieldID'],
      '#options' => $CustomFieldOptions,
      '#weight' => $weight++,
    );

  }
  else {

    $form['TerminationCustomFieldID'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t('Please create a DateTime custom field to use this feature.'),
      '#weight' => $weight++,
    );

  }

  $form['TerminationTaggingWrapper'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
  );

  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID);
  $TagFilter = Tag::GetTagFilter($UserID);
  $TagFilter[t('Filter: All tags')] = $TagOptions;

  $form['TerminationTaggingWrapper']['TerminationTagIDs'] = array(
    '#type' => 'textfield',
    '#default_value' => $ArrayCountdown['TerminationTagIDs'],
    '#title' => t("Tag"),
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['TerminationTagIDs'],
      'filter' => $TagFilter,
      //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
  );

  $form['TerminationTaggingWrapper']['TerminationTagDurationWrapper'] = array(
    '#type' => 'klicktipp_block',
    '#title' => t('Duration after tagging'),
    '#attributes' => array('class' => array('select-time'), 'style' => 'display: block;'),
    '#weight' => $weight++,
  );

  $Days = empty($ArrayCountdown['TerminationTagDuration']) ? '00' : sprintf('%02d', intval($ArrayCountdown['TerminationTagDuration']/86400));
  $DayOptions = array();
  for ($i = 0; $i < 30; $i++) {
    $s = sprintf('%02d', $i);
    $DayOptions[$s] = $s;
  }

  $form['TerminationTaggingWrapper']['TerminationTagDurationWrapper']['TerminationTagDurationDays'] = array(
    '#type' => 'select',
    '#default_value' => $Days,
    '#options' => $DayOptions,
    '#suffix' => '<span class="time">' . t('countdown: days and') . '</span>',
    '#weight' => $weight++,
  );

  $Hours = empty($ArrayCountdown['TerminationTagDuration']) ? '01' : sprintf('%02d', intval(($ArrayCountdown['TerminationTagDuration'] % 86400) / 3600));
  $form['TerminationTaggingWrapper']['TerminationTagDurationWrapper']['TerminationTagDurationHours'] = array(
    '#type' => 'select',
    '#default_value' => $Hours,
    '#options' => $HourOptions,
    '#attributes' => array('class' => array('datetime-hours')),
    '#suffix' => '<span class="time">' . t('countdown: hours') . '</span>',
    '#weight' => $weight++,
  );

  $form['UseCustomExpiredImage'] = array(
    '#type' => 'checkbox',
    '#title' => t('Use a custom expired image'),
    '#default_value' => $ArrayCountdown['UseCustomExpiredImage'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_custom_expired_image',
      'data-event-change' => 'js_custom_expired_image',
    ),
  );

  $ckfinder_button = array(
    '#title' => t('Select image'),
    '#value' => '#',
    '#attributes' => array(
      'data-event-click' => 'js_select_expired_image',
    ),
  );

  $form['CustomExpiredImage'] = array(
    '#type' => 'textfield',
    '#title' => t('Custom expired image'),
    '#default_value' => (empty($ArrayCountdown['CustomExpiredImage'])) ? Settings::get('marketing_tools_emailcountdown_default_expired_image') : $ArrayCountdown['CustomExpiredImage'],
    '#weight' => $weight++,
    '#prefix' => '<div class="ckfinder-select">',
    '#suffix' => theme('klicktipp_edit_button', array('element' => $ckfinder_button)) . '</div>',
    '#element_validate' => array('klicktipp_element_textfield_validate_url'), //allow & for urls
    '#maxlength' => 800,
    '#quickhelp' => 'countdown-field-customexpiredimage',
  );

  $quickhelp = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'countdown-target-url',
      '#title' => t("Use a custom target url"),
    )
  ));
  $QuickhelpSubscriberParameter = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'countdown-use-subscriber-parameter',
      '#title' => t("Display subscriber id parameter"),
    )
  ));
  $QuickhelpEmailParameter = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'countdown-use-email-parameter',
      '#title' => t("Display email parameter"),
    )
  ));

  $form['UseTargetURL'] = array(
    '#type' => 'checkbox',
    '#title' => t("Use a custom target url") . $quickhelp,
    '#default_value' => !isset($ArrayCountdown['UseTargetURL']) ? 1 : $ArrayCountdown['UseTargetURL'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#target-parameters',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#target-parameters',
    ),
  );

  $form['TargetParameters'] = array(
    '#type' => 'fieldset',
    '#title' => t('Target URL Parameters'),
    '#collapsible' => FALSE,
    '#weight' => $weight++,
    '#attributes' => array(
      'id' => 'target-parameters',
    ),
  );

  $form['TargetParameters']['TargetURL'] = array(
    '#type' => 'textfield',
    '#title' => t('Target URL'),
    '#default_value' => $ArrayCountdown['TargetURL'],
    '#weight' => $weight++,
    '#element_validate' => array('klicktipp_element_textfield_validate_url'), //allow & for urls
    '#description' => t('The contact will be redirected to this URL after clicking on the countdown.'),
    '#maxlength' => 800,
    '#quickhelp' => 'countdown-field-targeturl',
  );

  $form['TargetParameters']['TargetURLExpired'] = array(
    '#type' => 'textfield',
    '#title' => t('Expired target URL'),
    '#default_value' => $ArrayCountdown['TargetURLExpired'],
    '#weight' => $weight++,
    '#element_validate' => array('klicktipp_element_textfield_validate_url'), //allow & for urls
    '#description' => t('The contact will be redirected to this URL after clicking on the expired countdown.<br />Leave empty if the expired target URL is equal to the target URL.'),
    '#maxlength' => 800,
    '#quickhelp' => 'countdown-field-targeturlexpired',
  );

  $form['TargetParameters']['UseSubscriberParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display subscriber id parameter") . $QuickhelpSubscriberParameter,
    '#default_value' => !isset($ArrayCountdown['UseSubscriberParameter']) ? 1 : $ArrayCountdown['UseSubscriberParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-subscriberparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-subscriberparametername-wrapper',
    ),
  );

  $form['TargetParameters']['SubscriberParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($ArrayCountdown['SubscriberParameterName']) ? 'SubscriberID' : $ArrayCountdown['SubscriberParameterName'],
    '#weight' => $weight++,
  );

  $form['TargetParameters']['UseEmailParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display email parameter") . $QuickhelpEmailParameter,
    '#default_value' => !isset($ArrayCountdown['UseEmailParameter']) ? 1 : $ArrayCountdown['UseEmailParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-emailparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-emailparametername-wrapper',
    ),
  );

  $form['TargetParameters']['EmailParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($ArrayCountdown['EmailParameterName']) ? 'email' : $ArrayCountdown['EmailParameterName'],
    '#weight' => $weight++,
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => 10000,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['Save'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Save Countdown'),
    '#weight' => $weight++,
  );

  $form['buttons']['ModalDeleteConfirmTrigger'] = array(
    '#theme' => 'klicktipp_delete_modal_button',
    '#title' => t('Delete Countdown'),
    '#value' => 'klicktipp_tools_countdown_delete_confirm_form',
    '#weight' => $weight++,
    '#external' => FALSE,
    '#modal_confirm' => drupal_get_form('klicktipp_tools_countdown_delete_confirm_form', $account, $ToolID ),
  );

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "tools/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_tools_countdown_edit_form_pre_render ( $form ) {

  $preview_image = Settings::get('marketing_tools_emailcountdown_s3url') ."/TEMPLATE/1/TEMPLATE_120.gif";

  //ckfinder path and script
  $path_to_CKF = APP_URL . "ckfinder";

  $script = "

    window['js_change_termination_date'] = function(element, args) {

      $('.edit-TerminationDateTime').hide();
      $('#edit-terminationcustomfieldid').hide();
      $('#edit-terminationtaggingwrapper-wrapper').hide();

      var value = element.val();
      if ( value == '" . ToolCountdown::TERMINATION_TYPE_FROM_CUSTOMFIELD . "' ) {
        $('#edit-terminationcustomfieldid').show();
      }
      else if ( value == '" . ToolCountdown::TERMINATION_TYPE_FROM_TAGGING . "' ) {
        $('#edit-terminationtaggingwrapper-wrapper').show();
      }
      else {
        $('.edit-TerminationDateTime').show();

      }

      window['update_view']();

    }

    window['js_custom_expired_image'] = function(element, args) {

      if ( element.prop('checked') ) {
        $('.ckfinder-select').show();
      }
      else {
        $('.ckfinder-select').hide();
      }

      window['update_view']();

    }

    window['js_show_preview_countdown'] = function(element, args) {

      var template = element.val();
      var preview_image = '$preview_image';

      var img_src = preview_image.replace(/TEMPLATE/g, template);

      $('.templatepreview-image').attr('src', img_src);

      window['update_view']();

    }

    window['js_select_expired_image'] = function(element, args) {

      var href = '$path_to_CKF/ckfinder.html?action=js&func=js_set_expired_image';
      window.open(href, 'CKFinder', 'resizeable=yes');

      element.attr('href', 'JavaScript:void(0)');
      return false;
    }

    window['js_set_expired_image'] = function(url) {
      if (url != '') {
        $('.edit-CustomExpiredImage').val(url);
      }
    }

  ";

  drupal_add_js($script, array('type' => 'inline', 'scope' => 'footer'));

  return $form;

}

function klicktipp_tools_countdown_edit_form_validate ($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $ArrayCountdown = $form_state['values']['Countdown']->GetData();

  $Name = $form_state['values']['Name'];

  //check if updated name already exists
  if ( $Name != $ArrayCountdown['Name'] && ToolCountdown::CheckDuplicateName($UserID, $Name) ) {
    form_set_error('Name', t('A Countdown with the name %name already exists.', array(
      '%name' => $Name,
    )));
  }

  if ( $form_state['values']['TerminationType'] == ToolCountdown::TERMINATION_TYPE_FIXED ) {

    $TerminationDateTime = $form_state['values']['TerminationDateTime'];

    if ( empty($TerminationDateTime['Date']) ) {
      form_set_error('TerminationDateTime][Date', t('Please specify a termination date.'));
    }
    elseif (!Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $TerminationDateTime['Date']) ) {
      form_set_error('TerminationDateTime][Date', t('Invalid termination date.'));
    }
    else {

      $datetime = Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $form_state['values']['TerminationDateTime']['Date']);
      $datetime = $datetime + ($form_state['values']['TerminationDateTime']['Hours'] * 3600) + ($form_state['values']['TerminationDateTime']['Minutes'] * 60);

      if ( $datetime < time() ) {
        form_set_error('TerminationDateTime', t('Please specify a termination date in the future.'));
      }
      elseif ( $datetime > strtotime("+3 month") ) {
        form_set_error('TerminationDateTime', t('The termination date cannot be further away than 3 month from now.'));
      }

    }

  }
  elseif ( $form_state['values']['TerminationType'] == ToolCountdown::TERMINATION_TYPE_FROM_TAGGING ) {

    if ( empty($form_state['values']['TerminationTagIDs']) ) {
      form_set_error('TerminationTagIDs', t('Please specify a tag.'));
    }

    $delay = intval($form_state['values']['TerminationTagDurationDays']) * 86400;
    $delay += intval($form_state['values']['TerminationTagDurationHours']) * 3600;

    if ( $delay < 3600 ) {
      form_set_error('TerminationTagDurationWrapper][', t('The duration of the countdown must be at least 1 hour.'));
    }
    elseif ( $delay > (30 * 86400) ) {
      //unlikely since we are using dropdowns
      form_set_error('TerminationTagDurationWrapper][', t('The duration of the countdown cannot be greater than 30 days.'));
    }

  }
  else {

    if ( empty($form_state['values']['TerminationCustomFieldID']) ) {
      form_set_error('TerminationCustomFieldID', t('Please specify a custom field containing the termination datetime.'));
    }

  }

  if ( $form_state['values']['UseCustomExpiredImage'] ) {

    if ( empty($form_state['values']['CustomExpiredImage']) ) {
      form_set_error('CustomExpiredImage', t("Please specify an URL to the custom expired image."));
    }
    elseif (!valid_url($form_state['values']['CustomExpiredImage'], TRUE)) {
      form_set_error('CustomExpiredImage', t("Invalid custom expired image URL."));
    }

  }

  if (!empty($form_state['values']['TargetURL'])) {
    if (!valid_url($form_state['values']['TargetURL'], TRUE)) {
      form_set_error('TargetURL', t("Invalid target URL."));
    }
    elseif (BlacklistHandler::isUrlBlacklisted($form_state['values']['TargetURL'])[0]) {
      form_set_error('TargetURL', t('The entered URL is blacklisted.'));
    }
  }

  if (!empty($form_state['values']['TargetURLExpired'])) {
    if (!valid_url($form_state['values']['TargetURLExpired'], TRUE)) {
      form_set_error('TargetURLExpired', t("Invalid expired target URL."));
    }
    elseif (BlacklistHandler::isUrlBlacklisted($form_state['values']['TargetURLExpired'])[0]) {
      form_set_error('TargetURLExpired', t('The entered URL is blacklisted.'));
    }
  }

  if ( empty($form_state['values']['TargetURL']) && !empty($form_state['values']['TargetURLExpired']) ) {
    form_set_error('TargetURLExpired', t("When using an expired target URL, a target URL is required."));
  }

}

function klicktipp_tools_countdown_edit_form_submit ($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $Countdown = $form_state['values']['Countdown'];
  $Name = $form_state['values']['Name'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $ArrayCountdown = $Countdown->GetData();
  $ArrayCountdown['Name'] = $Name;
  $ArrayCountdown[DatabaseTableLabeled::LABEL_DATA_KEY] = $MetaLabels;

  $ArrayCountdown['Template'] = $form_state['values']['Template'];
  $ArrayCountdown['TerminationType'] = $form_state['values']['TerminationType'];

  if ($form_state['values']['TerminationType'] == ToolCountdown::TERMINATION_TYPE_FIXED) {

    $datetime = Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $form_state['values']['TerminationDateTime']['Date']);
    $datetime = $datetime + ($form_state['values']['TerminationDateTime']['Hours'] * 3600) + ($form_state['values']['TerminationDateTime']['Minutes'] * 60);

    $ArrayCountdown['TerminationDateTime'] = $datetime;
    $ArrayCountdown['TerminationCustomFieldID'] = '';
    $ArrayCountdown['TerminationTagIDs'] = array();

  }
  elseif ( $form_state['values']['TerminationType'] == ToolCountdown::TERMINATION_TYPE_FROM_TAGGING ) {

    $TerminationTagIDs = Tag::CreateManualTagByTagName($UserID, $form_state['values']['TerminationTagIDs'], TRUE);
    if (!$TerminationTagIDs) {
      //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!data' => $form_state['values']['TerminationTagIDs'],
        '!UserID' => $UserID,
      );
      Errors::unexpected("Error: CreateManualTagByTagName in Countdown edit. UserID: !UserID", $error, TRUE); //notify user
      return;
    }

    $ArrayCountdown['TerminationDateTime'] = 0;
    $ArrayCountdown['TerminationCustomFieldID'] = '';
    $ArrayCountdown['TerminationTagIDs'] = $TerminationTagIDs;

    $duration = intval($form_state['values']['TerminationTagDurationDays']) * 86400;
    $duration += intval($form_state['values']['TerminationTagDurationHours']) * 3600;

    $ArrayCountdown['TerminationTagDuration'] = $duration;

  }
  else {
    $ArrayCountdown['TerminationDateTime'] = 0;
    $ArrayCountdown['TerminationCustomFieldID'] = $form_state['values']['TerminationCustomFieldID'];
    $ArrayCountdown['TerminationTagIDs'] = array();
  }

  $ArrayCountdown['UseCustomExpiredImage'] = $form_state['values']['UseCustomExpiredImage'];

  if ( $form_state['values']['UseCustomExpiredImage'] ) {
    $ArrayCountdown['CustomExpiredImage'] = $form_state['values']['CustomExpiredImage'];
  }
  else {
    $ArrayCountdown['CustomExpiredImage'] = Settings::get('marketing_tools_emailcountdown_default_expired_image');
  }

  $ArrayCountdown['TargetURL'] = (empty($form_state['values']['UseTargetURL'])) ? '' : $form_state['values']['TargetURL'];
  $ArrayCountdown['TargetURLExpired'] = (empty($form_state['values']['UseTargetURL'])) ? '' : $form_state['values']['TargetURLExpired'];
  $ArrayCountdown['UseTargetURL'] = (empty($ArrayCountdown['TargetURL']) && empty($ArrayCountdown['TargetURLExpired'])) ? 0 : 1;

  if (empty($ArrayCountdown['UseTargetURL'])) {
    // user does not want to use a custom target url, so do not use any url parameters
    $ArrayCountdown['UseSubscriberParameter'] = 0;
    $ArrayCountdown['UseEmailParameter'] = 0;
    $ArrayCountdown['SubscriberParameterName'] = '';
    $ArrayCountdown['EmailParameterName'] = '';
  }
  else {
    // user uses at least one of the target urls, so set parameters
    $ArrayCountdown['UseSubscriberParameter'] = (empty($form_state['values']['UseSubscriberParameter'])) ? 0 : 1;
    $ArrayCountdown['UseEmailParameter'] = (empty($form_state['values']['UseEmailParameter'])) ? 0 : 1;
    $ArrayCountdown['SubscriberParameterName'] = (empty($ArrayCountdown['UseSubscriberParameter'])) ? '' : $form_state['values']['SubscriberParameterName'];
    $ArrayCountdown['EmailParameterName'] = (empty($ArrayCountdown['UseEmailParameter'])) ? '' : $form_state['values']['EmailParameterName'];
  }

  if ( ToolCountdown::UpdateDB($ArrayCountdown) ) {

    drupal_set_message(t("Countdown %name successfully updated.", array('%name' => $Name)));

  }
  else {
    $error = array(
      '!uid' => $UserID,
      '!ToolID' => $ArrayCountdown['ToolID'],
      '!name' => $Name,
      '!CountDown' => $ArrayCountdown,
    );

    //notify user
    Errors::unexpected("Countdown: Update for UserID: !uid with name !name (ToolID: !ToolID)", $error, TRUE);

  }

}

function klicktipp_tools_countdown_delete_confirm_form($form, $form_state, $account, $ToolID) {

  $UserID = $account->uid;

  $ArrayCountdown = ToolCountdown::FromID($UserID, $ToolID)->GetDBArray();

  $EntityName = $ArrayCountdown['Name'];
  $Title = t('Delete Countdown');

  $form = _klicktipp_confirm_form_modal('klicktipp_tools_countdown_delete_confirm_form', $EntityName, $Title);

  $form['Entity'] = array(
    '#type' => 'value',
    '#value' => $ArrayCountdown,
  );

  return $form;

}

function klicktipp_tools_countdown_delete_confirm_form_submit($form, &$form_state) {

  $ArrayCountdown = $form_state['values']['Entity'];
  $ToolID = $ArrayCountdown['ToolID'];
  $UserID = $ArrayCountdown['RelOwnerUserID'];
  $Name = $ArrayCountdown['Name'];

  //set deleted flag but do not actually delete since there could be still links out there
  if ( ToolCountdown::DeleteDB($ArrayCountdown) ) {
    drupal_set_message(t("Countdown %name successfully deleted.", array('%name' => $Name)));
  }
  else {

    $error = array(
      '!UserID' => $UserID,
      '!ToolID' => $ToolID,
      '!Name' => $Name,
    );

    //notify user
    Errors::unexpected("Countdown: Delete Countdown (ID: !ToolID) for user: !UserID with name !Name", $error, TRUE);

  }

  klicktipp_set_redirect($form_state, "tools/$UserID/countdowns");

}

function klicktipp_tools_countdown_image( $Query ) {

  $ArrayParameters = Core::DecryptURL($Query);
  $UserID = $ArrayParameters['UserID'];
  $ToolID = $ArrayParameters['ToolID'];
  $SubscriberID = $ArrayParameters['SubscriberID'];
  $ReferenceID = $ArrayParameters['ReferenceID'];

  //retrieve deleted also
  /** @var ToolCountdown $ObjectCountdown */
  $ObjectCountdown = ToolCountdown::FromID_IncludeDeleted($UserID, $ToolID);

  if ( $ObjectCountdown ) {

    $ArrayCountdown = $ObjectCountdown->GetData();

    $starttime = $ObjectCountdown->GetTime($SubscriberID, $ReferenceID);

    if ( $starttime <= 0 ) {
      //countdown has expired

      if ( $ArrayCountdown['UseCustomExpiredImage'] ) {
        $RedirectURL = $ArrayCountdown['CustomExpiredImage'];
      }
      else {
        //"This offer has ended..."
        $RedirectURL = Settings::get('marketing_tools_emailcountdown_default_expired_image');
      }

    }
    elseif ( $starttime > 30 * 24 * 60 * 60 ) {
      //start time is greater than 30 days
      //possible if datetime from custom field

      //"Button: Please click here" just as the one at the end of every countdown
      $RedirectURL = Settings::get('marketing_tools_emailcountdown_calltoaction_image');
    }
    else {
      //active countdown

      if ( $starttime <= 86400 ) {
        //the countdown expires in less than a day

        if ( $starttime <= 6 * 60 * 60) {
          //the countdown expires in less than 6 hours -> there is an image for every second
          $subfolder = 1;
        }
        else {
          //the countdown expires in less than 24 hours but more than 6 hours
          //-> there is an image for every 5 second
          $subfolder = 5;
          $starttime = $starttime - ($starttime % 5);
        }

      }
      else {
        //the countdown expires in more than 1 days -> there is an image for every 20 seconds

        $subfolder = "days/" . (int)($starttime / 86400);
        $starttime = $starttime - ($starttime % 60);

      }

      $RedirectURL = Settings::get('marketing_tools_emailcountdown_s3url') . "/{$ArrayCountdown['Template']}/$subfolder/{$ArrayCountdown['Template']}_{$starttime}.gif";

    }

  }
  else {
    //countdown not found
    // -> "This offer has ended..."
    $RedirectURL = Settings::get('marketing_tools_emailcountdown_default_expired_image');
  }

  if ( empty($RedirectURL) ) {
    //the countdown doe not exist
    //"Button: Please click here" just as the one at the end of every countdown
    $RedirectURL = Settings::get('marketing_tools_emailcountdown_calltoaction_image');
  }

  header("location: $RedirectURL");
  exit;

}

function klicktipp_tools_countdown_redirect( $Query ) {

  $ArrayParameters = Core::DecryptURL($Query);

  $UserID = $ArrayParameters['UserID'];
  $ToolID = $ArrayParameters['ToolID'];
  $SubscriberID = $ArrayParameters['SubscriberID'];
  $CampaignID = $ArrayParameters['CampaignID'];
  $EmailID = $ArrayParameters['EmailID'];
  $ReferenceID = $ArrayParameters['ReferenceID'];

  //retrieve deleted also
  /** @var ToolCountdown $ObjectCountdown */
  $ObjectCountdown = ToolCountdown::FromID_IncludeDeleted($UserID, $ToolID);

  if ( !$ObjectCountdown ) {

    //display a white label message: 'Sorry, this link is no longer valid.'
    klicktipp_invalid_link($UserID);
    // this wont return

  }

  $ArrayCountdown = $ObjectCountdown->GetData();

  $resttime = $ObjectCountdown->GetTime($SubscriberID, $ReferenceID);

  if ( $resttime > 0 || empty($ArrayCountdown['TargetURLExpired']) ) {

    $RedirectURL = $ArrayCountdown['TargetURL'];
    $LinkTitle = t("Countdown: !name", array('!name' => $ArrayCountdown['Name']));

  }
  else {

    $RedirectURL = $ArrayCountdown['TargetURLExpired'];
    $LinkTitle = t("Countdown: !name (expired)", array('!name' => $ArrayCountdown['Name']));

  }

  $LinkURL = $RedirectURL; //for link tracking without subscriber information

  if ( !empty($RedirectURL) && !empty($SubscriberID) ) {
    //add subscriber information to redirect url

    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    if ( !empty($FullSubscriber) ) {
      $Parameters = ToolCountdown::GetRedirectParameters($ArrayCountdown, $FullSubscriber);
      $RedirectURL = Lists::CreateCustomSubscriptionRedirect($RedirectURL, $Parameters);
    }

  }

  if ( empty($RedirectURL) ) {
    //the redirect url was removed from the countdown after the email was sent
    //display a white label message: 'Sorry, this link is no longer valid.'
    klicktipp_invalid_link($UserID);
    // this wont return
  }

  if ( empty($ArrayParameters['Preview']) ) {

    // Update click statistics - Start
    Statistics::RegisterLinkClickTrack($SubscriberID, $ReferenceID, $UserID, $CampaignID, $EmailID, $LinkURL, $LinkURL, $LinkTitle);
    // Update click statistics - End

  }

  klicktipp_goto_with_blacklist_check($RedirectURL, $ArrayParameters);
}


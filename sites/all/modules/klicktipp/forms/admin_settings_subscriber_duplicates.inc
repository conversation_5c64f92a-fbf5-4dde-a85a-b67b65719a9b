<?php

use App\Klicktipp\Core;
use App\Klicktipp\Errors;
use App\Klicktipp\ProcessLog;
use App\Klicktipp\SubscriberDuplicateDetectionCookie;
use App\Klicktipp\SubscriberDuplicateDetectionEmailSimilarity;
use App\Klicktipp\SubscriberDuplicateDetectionIpAddress;

function klicktipp_duplicates_detection_form($form, &$form_state) {
  $form['active_event_types'] = array(
    '#type' => 'checkboxes',
    '#title' => 'Active event types',
    '#default_value' => variable_get('klicktipp_duplicate_detection_active_event_types', []),
    '#options' => [
      SubscriberDuplicateDetectionCookie::TYPE => 'Cookie',
      SubscriberDuplicateDetectionIpAddress::TYPE => 'IP Address',
      SubscriberDuplicateDetectionEmailSimilarity::TYPE => 'Email Similarity'
    ]
  );

  $form['ip'] = [
    '#type' => 'fieldset',
    '#title' => 'Detection by IP',
  ];

  $form['ip']['max_subscribers_per_ip'] = array(
    '#type' => 'textfield',
    '#title' => 'Maximum subscribers per IP',
    '#description' => 'Duplicate detection by ip address ignores an ip address, if the number of subscribers this ip is assigned to already, hits this limit',
    '#element_validate' => array('element_validate_integer_positive'),
    '#default_value' => variable_get('klicktipp_duplicate_detection_max_subscribers_per_ip', 10),
  );

  $form['email-similarity'] = [
    '#type' => 'fieldset',
    '#title' => 'Detection by email-similarity',
  ];

  $configURL = '/admin/config/elasticsearch-connector/clusters';
  $configLink = l($configURL, $configURL, ['attributes' => ['target' => '#blank']]);
  $form['email-similarity']['es-cluster'] = array(
    '#type' => 'textfield',
    '#title' => 'Elastic/Opensearch cluster to use',
    '#description' => 'NOTE: specified cluster must be configured under ' . $configLink,
    '#default_value' => variable_get('klicktipp_duplicate_detection_es_cluster'),
  );

  $form['email-similarity']['es-index'] = array(
    '#type' => 'textfield',
    '#title' => 'Elastic/Opensearch index to use',
    '#description' => 'NOTE: index muste be present in specified cluster (see textfield above)',
    '#default_value' => variable_get('klicktipp_duplicate_detection_es_index'),
  );


  $form['email-similarity']['asynchronous'] = array(
    '#type' => 'checkbox',
    '#title' => 'Process asynchronously',
    '#description' => 'If checked, detection of similar email is processed via low prio queue, otherwise immediately',
    '#default_value' => variable_get('klicktipp_duplicate_asynchronous_email_similarity_detection', TRUE),
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_unsubscribe_submit',
    '#value' => t('Submit')
  );

  $form['buttons']['cancel'] = array(
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "", //frontpage
    '#theme' => 'klicktipp_cancel_button',
  );

  return $form;
}

function klicktipp_duplicates_detection_form_validate($form, &$form_state) {
  $values = $form_state['values'];
  $client = null;
  if (!empty($values['es-cluster'])) {
    $client = elasticsearch_connector_get_client_by_id($values['es-cluster']);
    if (!$client) {
      form_set_error('es-cluster', 'Cluster not configured in elasticsearch connector');
      return;
    }
  }
  if (!empty($values['es-index'])) {
    if ($client && empty($client->getIndicesStats()['indices'][$values['es-index']])) {
      form_set_error('es-index', 'Index does not exist in cluster');
    }
  }
}

function klicktipp_duplicates_detection_form_submit($form, &$form_state) {
  variable_set('klicktipp_duplicate_detection_active_event_types', $form_state['values']['active_event_types']);
  variable_set('klicktipp_duplicate_detection_max_subscribers_per_ip', (int)$form_state['values']['max_subscribers_per_ip']);
  variable_set('klicktipp_duplicate_detection_es_cluster', $form_state['values']['es-cluster']);
  variable_set('klicktipp_duplicate_detection_es_index', $form_state['values']['es-index']);
  variable_set('klicktipp_duplicate_asynchronous_email_similarity_detection', (bool)$form_state['values']['asynchronous']);
}

function klicktipp_duplicates_export_form($form, &$form_state) {
  global $user;

  $lastJobInfo = klicktipp_get_subscriber_duplicate_export_job_info($user->uid);
  $disableForm = FALSE;

  if ($lastJobInfo['status'] == 'finished') {
    // filePath looks like "private://1/duplicates-export/duplicates-20210212102617.csv"
    $parse = parse_url($lastJobInfo['filePath']);
    $QueryParameters = array(
      'UserID' => $parse['host'],
      'URI' => ltrim($parse['path'], '/' ),
    );
    $link = Core::EncryptURL($QueryParameters, 'download');
    drupal_set_message(sprintf('An export file is avalable. Klick to download <a href="%s">here</a>', $link));
  } elseif($lastJobInfo['status'] == 'running') {
    $disableForm = true;
    drupal_set_message(
      sprintf('An export ist currently in progress. %d slices are not processed yet', $lastJobInfo['unprocessed_slices']),
      'warning'
    );
  }

  $form['event_types'] = array(
    '#disabled' => $disableForm,
    '#required' => true,
    '#type' => 'checkboxes',
    '#title' => 'Event types to be included',
    '#options' => array(SubscriberDuplicateDetectionCookie::TYPE => 'Cookie', SubscriberDuplicateDetectionIpAddress::TYPE => 'IP Address')
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_unsubscribe_submit',
    '#value' => t('Start'),
    '#disabled' => $disableForm,
  );

  $form['buttons']['cancel'] = array(
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => '', //frontpage
    '#theme' => 'klicktipp_cancel_button',
  );

  if ($disableForm) {
    $form['#attached']['js'][] = array(
      'type' => 'inline',
      'data' => 'setTimeout(function () { location.reload(true); }, 4000)',
    );
  }
  return $form;
}

function klicktipp_get_subscriber_duplicate_export_job_info($userID) {
  if (isset($_SESSION['duplicateSubscriberExportLogID'])) {
    $query = 'SELECT * FROM {processlog} WHERE LogID = :logID';
    $args = [':logID' => $_SESSION['duplicateSubscriberExportLogID']];
  }
  // this case can should only occur, if session was lost. we try to check last export job of current user
  else {
    $query = "SELECT * FROM {processlog} WHERE EntityID = :userID AND QueueName = 'export_subscriber_duplicates' ORDER BY LogID DESC LIMIT 1";
    $args = [':userID' => $userID];
  }

  $result = db_query($query, $args)->fetchAssoc();
  if (!$result) {
    return ['status' => 'not_existing'];
  }
  if ($result['finished'] > 0) {
    $data = unserialize($result['Data']);
    if (!is_array($data)) {
      return FALSE;
    }
    return ['status' => 'finished', 'filePath' => $data['jobdata']['filePath']];
  } else {
    return ['status' => 'running', 'unprocessed_slices' => $result['JobsUnprocessed']];
  }
}

function klicktipp_duplicates_export_form_submit($form, &$form_state) {
  global $user;
  $logID = ProcessLog::Create('export_subscriber_duplicates', $user->uid);
  $_SESSION['duplicateSubscriberExportLogID'] = $logID;
  $itemSize = variable_get('klicktipp_duplicates_export_queue_item_size', 1000);

  $lastEventID = 0;
  $sql = 'SELECT EventID FROM {subscriber_duplicate_events} '
    . 'WHERE  Type IN (:types) AND EventID > :lastEventID '
    . 'ORDER BY  EventID';

  $eventTypes = array_keys(array_filter($form_state['values']['event_types']));
  $args = [':lastEventID' => &$lastEventID,  ':types' => $eventTypes ];

  $filePath = klicktipp_settings_duplicates_export_filepath(time());
  $file = fopen($filePath, 'a');
  $header = [
    'Date', 'Type', 'Status', 'SubscriberID', 'DuplicateID',
    'SubscriberFirstName', 'DuplicateFirstName', 'SubscriberLastName', 'DuplicateLastName',
    'SubscriberCity', 'DuplicateCity', 'SubscriberZip', 'DuplicateZip',
    'SubscriberStreet1', 'DuplicateStreet1', 'SubscriberStreet2', 'DuplicateStreet2'
  ];
  fputcsv($file, $header);
  fclose($file);

  while ($eventIDs = db_query_range($sql, 0, $itemSize, $args)->fetchCol()) {
    $lastEventID = end($eventIDs);
    ProcessLog::AddQueueItem($logID, ['eventIDs' => $eventIDs, 'filePath' => $filePath]);
  }
}

function klicktipp_settings_duplicates_export_filepath($starttime) {

  global $user;

  $filename = "duplicates-" . date('YmdHis', $starttime) . ".csv";
  $path = $user->uid . '/' . KLICKTIPP_DUPLICATES_DOWNLOAD_DIR;

  $dir = "private://$path";
  $uri = "$dir/$filename";

  //create the user directory for exports and/or check if it is writable
  if (!file_prepare_directory($dir, FILE_CREATE_DIRECTORY || FILE_MODIFY_PERMISSIONS)) {

    $error = array(
      '!uid' => $user->uid,
      '!uri' => $uri,
    );

    Errors::unexpected("Error: Create/Modify private user directory for user !uid. URI = '!uri'", $error, TRUE); //notify user
  }

  return $uri;
}

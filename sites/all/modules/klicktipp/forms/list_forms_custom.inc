<?php

use App\Klicktipp\AccountManager;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\Errors;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\Subscribers;
use App\Klicktipp\SubscriptionForms;
use App\Klicktipp\SubscriptionFormsColors;
use App\Klicktipp\SubscriptionFormsCustom;
use App\Klicktipp\SubscriptionFormsInline;
use App\Klicktipp\Tag;

function klicktipp_list_forms_custom_create_form ($form, $form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Create Subscription form (@type)', array(
    '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_CUSTOM]),
  ));
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Listbuilding'), "listbuilding/$UserID")), $page_title);

  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $weight = 1;

  $form = array();

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => '',
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? [] : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
      'container_class' => 'klicktipp-magicselect-short',
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Create Subscription form'),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "listbuilding/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_list_forms_custom_create_form_validate ($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];

  if (SubscriptionFormsCustom::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Subscription form (@type) with the name %name already exists.', array(
      '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_CUSTOM]),
      '%name' => $Name,
    )));
  }

}

function klicktipp_list_forms_custom_create_form_submit ($form, &$form_state) {

  $Name = $form_state['values']['Name'];
  $UserID = $form_state['values']['uid'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $BuildID = SubscriptionFormsCustom::InsertDB(array(
    'Name' => $Name,
    'RelOwnerUserID' => $UserID,
    'Settings' => [
      'UseSpamProtection' => 1,
    ],
    DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
  ));

  if (!empty($BuildID)) {

    drupal_set_message(t("Subscription form %name successfully created.", array('%name' => $Name)));

    // redirect to countdown edit
    klicktipp_set_redirect($form_state, "listbuilding/$UserID/form-custom/$BuildID/edit");

  }
  else {

    $error = array(
      '!uid' => $UserID,
      '!name' => $Name,
    );

    //notify user
    Errors::unexpected("SubscriptionFormCustom: Create for UserID: !uid with name !name", $error, TRUE);

  }

}

function klicktipp_list_forms_custom_edit_form ($form, &$form_state, $account, $BuildID) {

  $UserID = $account->uid;

  /** @var SubscriptionFormsCustom $ObjectForm */
  if ( !empty($form_state['storage']['ObjectForm']) ) {
    //a custom field has been added to the form and there may be other changes, do not load the form from the database
    //@see: submit and includes/list_forms.inc -> klicktipp_list_forms_add_customfield()
    $ObjectForm = $form_state['storage']['ObjectForm'];
  }
  else {
    $ObjectForm = SubscriptionFormsCustom::FromID($UserID, $BuildID);
  }

  if ( empty($ObjectForm) ) {

    drupal_set_message(t("Subscription form not found."), 'error');
    drupal_goto("listbuilding/$UserID");
    return NULL;

  }

  $page_title = t('Edit Subscription form');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(
    l(t('Listbuilding'), "listbuilding/$UserID"),
  ), $page_title);

  $ArrayForm = $ObjectForm->GetData();

  $form = array();

  $form['#pre_render'][] = 'klicktipp_list_forms_custom_edit_form_pre_render';

  $Labels = $ObjectForm->GetMetaLabels();
  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $weight = 1;

  //retrieve lists of user
  $ListsOptions = Lists::RetrieveListsAsOptionsArray($UserID);

  //retrieve tags of user
  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['ArrayForm'] = array(
    '#type' => 'value',
    '#value' => $ArrayForm,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => $ArrayForm['Name'],
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? $Labels : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  $linkText = t('Listbuilding::form::Edit Double-Opt-In');
  $form['RelListID'] = array(
    '#type' => 'select',
    '#default_value' => $ArrayForm['RelListID'],
    '#title' => t("Subscription process"),
    '#weight' => $weight++,
    '#options' => $ListsOptions,
    '#suffix' => sprintf('<a href="#" id="doi-link">%s</a>', $linkText),
    '#attributes' => array('class' => array('doi-select')),
  );

  $form['OptInSubscribeTo'] = array(
    '#type' => 'textfield',
    '#default_value' => $ArrayForm['AssignTagID'],
    '#title' => t('Additional tagging (optional)'),
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => FALSE,
      'free_entries' => TRUE,
      'free_entries_message' => t('The tag will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['OptInSubscribeTo'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'additional-tagging',
  );

  $ajax = array(
    'callback' => 'klicktipp_form_ajax_callback',
    'wrapper' => 'formbuilder-ajax',
    'method' => 'replace',
  );

  Libraries::include('list_forms.inc');

  $form['CustomFields'] = klicktipp_list_forms_customfield_settings_table($ArrayForm, $weight++, $ajax, array(CustomFields::TYPE_HTML));

  $form['Preview'] = array(
    '#type' => 'klicktipp_block',
    '#title' => t('Preview'),
    '#weight' => $weight++,
    '#attributes' => array('class' => array('preview-box', 'preview-box-center')),
  );

  $form['Preview']['Content'] = array(
    '#type' => 'markup',
    '#markup' => $ObjectForm->GetHTML(TRUE),
    '#weight' => $weight++,
  );

  $form['Preview']['Resize'] = array(
    '#type' => 'markup',
    '#markup' => '<div id="klicktipp-resize" class="has-tooltip" data-tooltip-title="' . t("Drag to change width.") . '"></div>',
    '#weight' => $weight++,
  );

  $form['FormStyle'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('preview-box', 'append')),
  );

  $form['FormStyle']['Style'] = array(
    '#type' => 'radios',
    '#title' => t('Style'),
    '#default_value' => $ArrayForm['Settings']['Style'],
    '#options' => array(
      SubscriptionForms::STYLE_FLAT => t('style: Flat'),
      SubscriptionForms::STYLE_GRADIENT => t('style: Gradient'),
    ),
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'Style'),
    '#attributes' => array(
      'class' => array('form-radios-inline'),
      'data-event-load' => 'js-select-style',
      'data-event-click' => 'js-select-style',
    ),
  );

  $form['FormStyle']['ColorTemplates'] = array(
    '#type' => 'markup',
    '#title' => t('Color templates'),
    '#markup' => "<label>" . t('Color templates') . "</label>" . SubscriptionFormsColors::DisplayColorTemplates(),
    '#weight' => $weight++,
  );

  $form['Settings'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('settings-box')),
  );

  $form['Settings']['ColorsBright'] = array(
    '#type' => 'hidden',
    '#default_value' => $ArrayForm['Settings']['Colors']['bright'],
    '#parents' => array('Settings', 'Colors', 'bright'),
  );

  $form['Settings']['ColorsNormal'] = array(
    '#type' => 'hidden',
    '#default_value' => $ArrayForm['Settings']['Colors']['normal'],
    '#parents' => array('Settings', 'Colors', 'normal'),
  );

  $form['Settings']['ColorsDark'] = array(
    '#type' => 'hidden',
    '#default_value' => $ArrayForm['Settings']['Colors']['dark'],
    '#parents' => array('Settings', 'Colors', 'dark'),
  );

  $form['Settings']['Responsive'] = array(
    '#type' => 'klicktipp_grid_row',
    '#weight' => $weight++,
  );

  $form['Settings']['Responsive']['Left'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('col-md-6')),
  );

  $form['Settings']['Responsive']['Left']['ColorsBase'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Background color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['base'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'base'),
  );

  $form['Settings']['Responsive']['Left']['DisplayChicklet'] = array(
    '#type' => 'checkbox',
    '#title' => t('Display chicklet'),
    '#default_value' => $ArrayForm['Settings']['DisplayChicklet'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'DisplayChicklet'),
    '#attributes' => array(
      'data-event-load' => 'js_formbuilder_display_chicklet',
      'data-event-change' => 'js_formbuilder_display_chicklet',
    ),
  );

  $form['Settings']['Responsive']['Left']['ColorsChicklet'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Chicklet color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['chicklet'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'chicklet'),
  );

  $form['Settings']['Responsive']['Left']['ColorsChickletGradient'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Chicklet color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['chicklet_gradient'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'chicklet_gradient'),
  );

  $form['Settings']['Responsive']['Left']['ChickletText'] = array(
    '#type' => 'textfield',
    '#title' => t('Chicklet text'),
    '#default_value' => (empty($ArrayForm['Settings']['ChickletText'])) ? t('%chicklet% Entries') : $ArrayForm['Settings']['ChickletText'],
    '#weight' => $weight++,
    '#description' => t('Enter the chicklet text. Use %chicklet% to display the count.'),
    '#attributes' => array(
      'class' => array('input-medium-large'),
    ),
    '#parents' => array('Settings', 'ChickletText'),
  );

  $form['Settings']['Responsive']['Left']['AffiliateID'] = klicktipp_form_digistore_affiliate_checkbox($account, $ArrayForm['Settings']['AffiliateID'], $weight++, $ajax);
  $form['Settings']['Responsive']['Left']['AffiliateID']['#parents'] = array('Settings', 'AffiliateID');

  if ( variable_get('klicktipp_settings_digistore_enable_multi_device_tracking', 0) ) {

    $multidevicetracking = TRUE;
    if (!klicktipp_feature_access($account, 'access digistore multi device tracking')) {
      $multidevicetracking = FALSE;
      //quickhelp shows an upsell
      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-upsell',
          '#title' => t("Use Digistore24 Multi-Device-Tracking"),
        ),
      ));
    }
    elseif (empty($account->UserSettings['DigiStore24ApiKeys'])) {
      $multidevicetracking = FALSE;
      //quickhelp shows how to connect DigiStore24 accounts
      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-no-accounts',
          '#title' => t("Use Digistore24 Multi-Device-Tracking"),
        ),
      ));
    }
    else {
      //quickhelp shows info about multi-device-tracking (the user does not have to do anything, unlike OptimizePress etc.)
      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-generell',
          '#title' => t("Use Digistore24 Multi-Device-Tracking"),
        ),
      ));
    }

    $form['Settings']['Responsive']['Left']['UseDigistoreMultiDeviceTracking'] = array(
      '#type' => 'checkbox',
      '#title' => t("Use Digistore24 Multi-Device-Tracking") . $quickhelp,
      '#default_value' => empty($ArrayForm['Settings']['UseDigistoreMultiDeviceTracking']) ? 0 : 1,
      '#return_value' => 1,
      '#parents' => array('Settings', 'UseDigistoreMultiDeviceTracking'),
      '#disabled' => !$multidevicetracking,
      '#weight' => $weight++,
    );
  }

  $form['Settings']['Responsive']['Left']['Width'] = array(
    '#type' => 'textfield',
    '#title' => t('Form width'),
    '#default_value' => $ArrayForm['Settings']['Width'],
    '#weight' => $weight++,
    '#attributes' => array('class' => array('input-short')),
    '#parents' => array('Settings', 'Width'),
  );

  $form['Settings']['Responsive']['Left']['Height'] = array(
    '#type' => 'hidden',
    '#default_value' => $ArrayForm['Settings']['Height'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Height'),
  );

  $form['Settings']['Responsive']['Right'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('col-md-6')),
  );

  $form['Settings']['Responsive']['Right']['ButtonType'] = array(
    '#type' => 'select',
    '#title' => t('Button type'),
    '#default_value' => $ArrayForm['Settings']['Button']['button'],
    '#options' => SubscriptionForms::GetButtonsAsOptionsArray(SubscriptionForms::BUTTON_CATEGORY_FORM),
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#attributes' => array(
      'data-event-load' => 'js_formbuilder_button_type',
      'data-event-change' => 'js_formbuilder_button_type',
    ),
    '#parents' => array('Settings', 'Button', 'button'),
  );

  $form['Settings']['Responsive']['Right']['ButtonColorImage'] = array(
    '#type' => 'select',
    '#title' => t('Button color'),
    '#default_value' => $ArrayForm['Settings']['Button']['color'],
    '#options' => array(
      SubscriptionForms::COLOR_ORANGE => t('color: orange'),
      SubscriptionForms::COLOR_BLUE => t('color: blue'),
      SubscriptionForms::COLOR_VIOLET => t('color: violet'),
      SubscriptionForms::COLOR_LIGHTGREEN => t('color: light green'),
      SubscriptionForms::COLOR_GREEN => t('color: green'),
      SubscriptionForms::COLOR_TURQUOISE => t('color: turquoise'),
      SubscriptionForms::COLOR_RED => t('color: red'),
      SubscriptionForms::COLOR_GREY => t('color: grey'),
    ),
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'Button', 'color'),
  );

  $form['Settings']['Responsive']['Right']['ButtonColor'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Button color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['button'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'button'),
  );

  $form['Settings']['Responsive']['Right']['ColorsButtonText'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Button text color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['button_text'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'button_text'),
  );

  $form['Settings']['Responsive']['Right']['ButtonSwoosh'] = array(
    '#type' => 'checkbox',
    '#title' => t('Button Swoosh'),
    '#default_value' => $ArrayForm['Settings']['Button']['swoosh'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'Button', 'swoosh'),
  );

  $form['Settings']['Responsive']['Right']['ButtonText'] = array(
    '#type' => 'textfield',
    '#title' => t('Button text'),
    '#default_value' => (empty($ArrayForm['Settings']['Button']['text'])) ? t('Your button text') : $ArrayForm['Settings']['Button']['text'],
    '#weight' => $weight++,
    '#attributes' => array(
      'class' => array('input-medium'),
    ),
    '#parents' => array('Settings', 'Button', 'text'),
  );

  $form['Settings']['Responsive']['Right']['ButtonSize'] = array(
    '#type' => 'select',
    '#title' => t('Button size'),
    '#default_value' => $ArrayForm['Settings']['Button']['size'],
    '#options' => array(
      SubscriptionForms::BUTTON_SIZE_SMALL => t('size: small'),
      SubscriptionForms::BUTTON_SIZE_MEDIUM => t('size: medium'),
      SubscriptionForms::BUTTON_SIZE_LARGE => t('size: large'),
    ),
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'Button', 'size'),
  );

  $form['Settings']['DisplayDSGVOCheckbox'] = array(
    '#type' => 'checkbox',
    '#title' => t('Display DSGVO checkbox'),
    '#default_value' => $ArrayForm['Settings']['DisplayDSGVOCheckbox'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'DisplayDSGVOCheckbox'),
    '#attributes' => array(
      'data-event-load' => 'js_formbuilder_display_dsgvocheckbox',
      'data-event-change' => 'js_formbuilder_display_dsgvocheckbox',
    ),
  );

  $form['Settings']['DSGVOText'] = array(
    '#type' => 'textarea',
    '#default_value' => (empty($ArrayForm['Settings']['DSGVOText'])) ? variable_get('listbuilding_htmldsgvotext_default', '') : $ArrayForm['Settings']['DSGVOText'],
    '#weight' => $weight++,
    '#resizable' => FALSE,
    '#parents' => array('Settings', 'DSGVOText'),
    '#attributes' => array(
      'class' => array('cke-textarea personalized jquery_ckeditor content-html'),
      'data-event-load' => 'js_init_ckeditor_personalization',
    ),
  );

    if (AccountManager::canDeactivateCaptcha($account)) {
        $form['Settings']['DeactivateCaptcha'] = array(
            '#type' => 'checkbox',
            '#title' => t("Captcha::FormEdit::Checkbox::I want to deactivate the captcha for this form"),
            '#default_value' => empty($ArrayForm['DeactivateCaptcha']) ? 0 : 1,
            '#return_value' => 1,
            '#weight' => $weight++,
            '#attributes' => array(
                'data-event-load' => 'js_checkbox_toggle_element_display',
                'data-event-load-args' => '#captcha-warning',
                'data-event-change' => 'js_checkbox_toggle_element_display',
                'data-event-change-args' => '#captcha-warning',
            )
        );

        $captchaWarning = t('Captcha::FormEdit::Warning::It is not recommended to deactivate the captcha protection.');
        $form['Settings']['DeactivateCaptchaWarning'] = array(
            '#type' => 'markup',
            '#markup' => '<div id="captcha-warning" class="alert alert-warning">' . $captchaWarning . '</div>',
            '#weight' => $weight++,
        );
    }

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => 10000,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['Save'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Save Subscription form'),
    '#weight' => $weight++,
    '#attributes' => array(
      'onmousedown' => 'JavaScript:window["js_calculate_height_on_submit"]();'
    )
  );

  Libraries::include('list_forms.inc');
  $form['buttons']['EmbedCode'] = klicktipp_list_forms_embed_code_modal("embed-code-$BuildID", $ObjectForm->GetEmbedCode(), $weight++);

  $ModalID = 'modalDeleteConfirmAjax';
  $Title = t('Delete Subscription form');

  $modalFormParameters = [
    "modalTitle" => $Title,
    "modalId" => $ModalID,
    "dependencyMessage" => t('This subscription form is used in the following objects and cannot be deleted:'),
    "entityId" => $BuildID,
    "entityClass" => SubscriptionFormsInline::class,
    "entityName" => $ArrayForm['Name'],
    "entity" => $ArrayForm,
    "submitFunction" => "klicktipp_list_forms_custom_delete_confirm_form_submit",
  ];

  // will include the generic modal form "klicktipp_delete_modal_form"
  Libraries::include("form_delete_modal.inc");

  $form['buttons']['ModalDeleteConfirmTrigger'] = [
    '#theme' => 'klicktipp_delete_modal_ajax_button',
    '#title' => $Title,
    '#value' => $ModalID,
    '#weight' => $weight++,
    '#modal_confirm' => drupal_get_form("klicktipp_delete_modal_form", $account, $modalFormParameters),
  ];

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "listbuilding/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_list_forms_custom_edit_form_pre_render ($form) {
  global $user;
  global $base_url;
  global $language;
  $path_to_CKF = "$base_url/ckfinder/";
  $lang = substr($language->language, 0, 2);

  drupal_add_js('

  window["content-html"] = 0;

  window["cke_persofocus"] = function(element, args) {
    $(".persofocus").removeClass("persofocus");
    $(args).addClass("persofocus");
    window["js_insert_placeholder"](element, args);
  }

  window["js_calculate_height_on_submit"] = function() {

    if (window["content-html"]) {
      $("label[for=DSGVOCheckbox]").html(window["content-html"].getData());
    }

    window["js_formbuilder_update_height"](true);
  }

  $(document).ready(function () {

    // Initialize the editor.
    var config = {
      toolbar:
      [
          ["Cut","Copy","Paste","PasteText","PasteFromWord"],
          ["Undo","Redo","-","SelectAll","RemoveFormat"],
          "/",
          ["Bold","Italic","Underline","Strike","-","Subscript","Superscript"],
          ["NumberedList","BulletedList","-","Outdent","Indent","Blockquote","CreateDiv"],
          ["JustifyLeft","JustifyCenter","JustifyRight","JustifyBlock"],
          ["Link","Unlink"],
          "/",
          ["Image","Table","HorizontalRule","SpecialChar"],
          ["Styles","Format","Font","FontSize"],
          ["TextColor","BGColor"],
          ["Maximize", "ShowBlocks"]
      ],
      enterMode : CKEDITOR.ENTER_BR,
      language: "' . $lang . '",
      height: "400px",
      resize_enabled : false,
      allowedContent: true,
      forceSimpleAmpersand: true,
      filebrowserUploadUrl : null, //disable upload tab
      filebrowserImageUploadUrl : null, //disable upload tab
      filebrowserFlashUploadUrl : null, //disable upload tab
      filebrowserBrowseUrl : "' . $path_to_CKF . 'ckfinder.html",
      filebrowserImageBrowseUrl : "' . $path_to_CKF . 'ckfinder.html",
      filebrowserFlashBrowseUrl: "' . $path_to_CKF . 'ckfinder.html",
    };

    try {
      $(".edit-Settings-DSGVOText").ckeditor(config);
      window["content-html"] = $(".edit-Settings-DSGVOText").ckeditorGet();

      //DataProcessor

      CKEDITOR.on( "instanceReady", function( ev ) {
        ckeditor = ev.editor;
        ckeditor.on("blur", function() {
          $("label[for=DSGVOCheckbox]").html(ckeditor.getData());
          window["js_formbuilder_update_height"](true);
        });
        ckeditor.on("change", function() {
          $("label[for=DSGVOCheckbox]").html(ckeditor.getData());
          window["js_formbuilder_update_height"](true);
        });
        ckeditor.dataProcessor.htmlFilter.addRules({
          elements : {
            p : function(element) {
            //remove p-Tags containing just a &nbsp;
            var content = element.children[0].value;
            if (content && content.charCodeAt(0) == 160) {
              return false;
            }
          }
        }
      });

      });

    } catch (e) {
    alert(e);
    };

  });
  ', array('type' => 'inline', 'scope' => 'footer'));

  $UserID = $form['uid']['#value'];
  $ArrayForm = $form['ArrayForm']['#value'];

  $ChickletCount =  Subscribers::GetActiveTotal($UserID);
  $StoredWidth = $ArrayForm['Settings']['Width'];

  $script = "

    window['ChickletCount'] = $ChickletCount;
    window['StoredWidth'] = $StoredWidth;
    window['UserWidth'] = 0;
    window['LastWidth'] = 0;
    window['MinWidth'] = 200;
    window['MinResize'] = 200;
    window['MaxResize'] = 800;
    window['IsResizing'] = 0;

    $(document).ready(function () {

      window['ResizeCenter'] = $('.preview-box').offset().left + Math.round($('.preview-box').outerWidth() / 2);

      window['js_formbuilder_update']();

      $('#klicktipp-resize').mousedown(function (ev) {
          window['OnResizeStart']();
      });

      $('.preview-box').mousemove(function (ev) {
          window['OnResizing'](ev);
      });

      $(document).mouseup(function (ev) {
          window['OnResizeEnd'](ev);
      });

      $('#edit-settings-width').blur(function () {

          var width = $(this).val().replace(/\D/g, ''); //strip non-numeric chars
          if ( !isNaN(width) ) {
              window['ResizeForm'](width,1); //userinput
          }

      });

      $('.edit-Settings-ChickletText').keyup(function() {

        var text = $(this).val();
        text = text.replace(/%chicklet%/g, '<span class=\"ktchicklet\">' + window['ChickletCount'] + '</span>');
        $('.ktv2-form-chicklet').html(text);

      });

      $('#DSGVOCheckbox').on('change', function(e) {
        if ($(e.target).prop('checked')) {
          $('.ktv2-submit-element-button').attr('disabled', false);
        }
        else {
          $('.ktv2-submit-element-button').attr('disabled', true);
        }
      });

      $('.edit-Settings-Button-text').keyup(function() {
        if ( $(this).val() == '' ) {
          $('.ktv2-submit-element-button.button-text').val('" . t('Your button text') . "');
        }
        else {
          $('.ktv2-submit-element-button.button-text').val($(this).val());
        }
      });

    });

    // --- color picker ---

    window['ColorpickerMove'] = function (button, textfield, color) {

      if ( textfield.attr('id') == 'edit-settings-colors-base' ) {

        var base_hex = color.toHexString();

        var colors = window['ColorPickerMonochromatic'](color);

        var bright_hex = colors[0];
        var normal_hex = colors[1];
        var dark_hex = colors[2];

        $('.style-gradient .ktv2-form-body').css({
          'background': 'transparent -moz-linear-gradient(top, ' + normal_hex + ' 0%, ' + dark_hex + ' 100%)'
        }).css({
          'background': 'transparent -webkit-linear-gradient(top, ' + normal_hex + ' 0%, ' + dark_hex + ' 100%)'
        }).css({
          'background': 'transparent linear-gradient(to bottom, ' + normal_hex + ' 0%, ' + dark_hex + ' 100%)'
        });

        $('.style-gradient .ktv2-form-element-textfield, .style-gradient .ktv2-form-element-textarea').css({'border-color': dark_hex});

        $('.style-gradient .ktv2-form-body-bg').css({
          'background': 'transparent -moz-linear-gradient(top, ' + bright_hex + ' 0%, ' + normal_hex + ' 100%)'
        }).css({
          'background': 'transparent -webkit-linear-gradient(top, ' + bright_hex + ' 0%, ' + normal_hex + ' 100%)'
        }).css({
          'background': 'transparent linear-gradient(to bottom, ' + bright_hex + ' 0%, ' + normal_hex + ' 100%)'
        });

        $('.style-flat .ktv2-form-body').css({'background-color': base_hex});
        $('.style-flat .ktv2-form-body-bg').css({'background': 'none'});
        $('.style-flat .ktv2-form-element-textfield, .style-flat .ktv2-form-element-textarea').css({'border-color': '1px solid rgba(0, 0, 0, 0.1)'});

        $('.edit-Settings-Colors-base').val(base_hex);
        $('.edit-Settings-Colors-bright').val(bright_hex);
        $('.edit-Settings-Colors-normal').val(normal_hex);
        $('.edit-Settings-Colors-dark').val(dark_hex);

      }
      else if ( textfield.attr('id') == 'edit-settings-colors-button' ) {
        var hex = color.toHexString();
        $('.ktv2-submit-element-bg.button-text').css({'background-color': hex});
        $('.style-flat .ktv2-submit-element-button.button-text').css({'border-color': color.darken().toHexString()});
        $('.style-gradient .ktv2-submit-element-button.button-text').css({'border-color': 'rgba(0,0,0,0.2)'});
        $('.edit-Settings-Colors-button').val(hex);
      }
      else if ( textfield.attr('id') == 'edit-settings-colors-button-text' ) {
        $('.ktv2-submit-element-button.button-text').css({'color': color.toHexString()});
        $('.edit-Settings-Colors-button-text').val(color.toHexString());
      }
      else if ( textfield.attr('id') == 'edit-settings-colors-chicklet' ) {
        var hex = color.toHexString();
        $('.style-flat .ktv2-form-chicklet').css({'color': hex});
        $('.style-flat .ktv2-form-element-label').css({'color': hex});
        $('.edit-Settings-Colors-chicklet').val(hex);
      }
      else if ( textfield.attr('id') == 'edit-settings-colors-chicklet-gradient' ) {
        var hex = color.toHexString();
        $('.style-gradient .ktv2-form-chicklet').css({'color': hex});
        $('.style-gradient .ktv2-form-element-label').css({'color': hex});
        $('.edit-Settings-Colors-chicklet-gradient').val(hex);
      }

    }

    window['js_formbuilder_update_height'] = function (hasChanges) {

        var preview = $('#formbuilder-preview');

        //adjust for 4px error per textarea in height calculation
        var adjust = preview.find('textarea').length || 0;
        if (adjust > 0) adjust *= 4;

        $('.edit-Settings-Height').val(preview.outerHeight() + adjust);

        if ( hasChanges == 1 ) {

          var tooltip = $('<div></div>').css('display', 'inline-block');

          //deactivate embed code button after change the checkbox state
          $('#edit-embedcode').attr({
            'data-toggle': '',
            'data-modal-id': '',
            'disabled': 'disabled'
          }).wrap(tooltip);

          $('#edit-embedcode').parent().tooltip({
            'title': '" . t('Please save the Subscription form first.') . "',
            'container': 'body'
          });

        }
    }

    window['js_formbuilder_update'] = function (hasChanges) {

      var preview = $('#formbuilder-preview');

      if ( preview.length > 0 ) {

        //hide the dsgvo checkbox + text so it will not be considered when calculating the with
        $('#formbuilder-preview .ktv2-form-dsgvocheckbox').hide()

        var width = preview.outerWidth(true);

        //show the dsgvo checkbox again
        $('#formbuilder-preview .ktv2-form-dsgvocheckbox').show()

        if ( hasChanges ) {
          //minimum resize width is the css auto width, the absolute min width is 200px
          window['MinResize'] = (width > window['MinWidth']) ? width : window['MinWidth'];
        }

        if ( window['UserWidth'] == 0 && window['StoredWidth'] != 0 && width < window['StoredWidth'] ) {
          //the user did not change the width manually yet
          //if the auto width is smaller than the stored width, resize the form to the stored width
          width = window['StoredWidth'];

          //else: a form element is greater than the stored width, use auto width

        }
        else if ( window['UserWidth'] > 0 && width < window['UserWidth'] ) {
          //the user already adjusted the width manually
          //if the auto width is smaller than the user width, resize the form to the user width
          width = window['UserWidth'];

          //else: a form element is greater than the user width, use auto width

        }

        //adjust for 4px error per textarea in height calculation
        var adjust = preview.find('textarea').length || 0;
        if (adjust > 0) adjust *= 4;

        $('.edit-Settings-Width').val(width);
        $('.edit-Settings-Height').val(preview.outerHeight() + adjust);

        window['ResizeForm'](width);

      }

      if ( hasChanges == 1 ) {

        var tooltip = $('<div></div>').css('display', 'inline-block');

        //deactivate embed code button after change the checkbox state
        $('#edit-embedcode').attr({
          'data-toggle': '',
          'data-modal-id': '',
          'disabled': 'disabled'
        }).wrap(tooltip);

        $('#edit-embedcode').parent().tooltip({
          'title': '" . t('Please save the Subscription form first.') . "',
          'container': 'body'
        });

      }

      window['update_view']();

    }

    window['js_formbuilder_button_type'] = function (element, args) {

      var value = element.val();

      if ( value == 'text' ) {
        $('.edit-Settings-Button-swoosh-wrapper').hide();
        $('.edit-Settings-Button-color-wrapper').hide();
        $('.edit-Settings-Button-text-wrapper').show();
        $('.edit-Settings-Colors-button-wrapper').show();
        $('.edit-Settings-Colors-button-text-wrapper').show();

      }
      else {
        $('.edit-Settings-Button-swoosh-wrapper').show();
        $('.edit-Settings-Button-color-wrapper').show();
        $('.edit-Settings-Button-text-wrapper').hide();
        $('.edit-Settings-Colors-button-wrapper').hide();
        $('.edit-Settings-Colors-button-text-wrapper').hide();
      }

      window['update_view']();

    }

    window['js_formbuilder_display_chicklet'] = function (element, args) {

      if ( element.prop('checked') ) {
        $('.edit-Settings-ChickletText-wrapper').show();
        window['js-select-style']($('#edit-settings-style-flat'));
        window['js-select-style']($('#edit-settings-style-gradient'));
      }
      else {
        $('.edit-Settings-Colors-chicklet-wrapper, .edit-Settings-ChickletText-wrapper, .edit-Settings-Colors-chicklet-gradient-wrapper').hide();
      }

      window['update_view']();

    }

    window['js_formbuilder_display_dsgvocheckbox'] = function (element, args) {
      if (element.prop('checked')) {
        $('#edit-settings-dsgvotext-wrapper').show();
        $('.ktv2-submit-element-button').attr('disabled', true);
      }
      else {
        $('#edit-settings-dsgvotext-wrapper').hide();
      }
      window['update_view']();
    }

    window['OnResizeStart'] = function () {
      window['IsResizing'] = 1;
    }

    window['OnResizing'] = function (ev) {

      if (window['IsResizing'] == 1 ) {
          window['ResizeForm'](Math.round(ev.clientX - window['ResizeCenter']) * 2, 1); //userinput
          $('#klicktipp-resize').tooltip('destroy');
      }

    }

    window['OnResizeEnd'] = function () {
        window['IsResizing'] = 0;
        $('#klicktipp-resize').tooltip({'container': 'body', 'title': $('#klicktipp-resize').attr('data-tooltip-title')});
    }

    window['ResizeForm'] = function (width, userinput) {

      if ( width < window['MinResize']) {
        width = window['MinResize'];
      }

      if ( width > window['MaxResize']) {
        width = window['MaxResize'];
      }

      $('#edit-settings-width').val(width);
      $('.ktv2-form, .ktv2-box').width(width);
      $('#klicktipp-resize').css('margin-left', Math.round(width/2) - 13); //resize button width = 27

      if ( userinput ) {
        window['UserWidth'] = width;
      }

    }

    window['js-select-color-template'] = function ( element, args ) {

      var colors = args.split(',');

      $('.edit-Settings-Colors-base').setColor(colors[0]);
      $('.edit-Settings-Colors-button').setColor(colors[1]);
      $('.edit-Settings-Colors-button-text').setColor(colors[2]);
      $('.edit-Settings-Colors-chicklet').setColor(colors[3]);
      $('.edit-Settings-Colors-chicklet-gradient').setColor(colors[4]);

    }

    window['js-select-style'] = function ( element, args ) {

      if ( element.prop('checked') && element.val() == 'flat' ) {
        $('.edit-Settings-Colors-chicklet-wrapper').show();
        $('.edit-Settings-Colors-chicklet-gradient-wrapper').hide();
      }
      else if ( element.prop('checked') && element.val() == 'gradient' ) {
        $('.edit-Settings-Colors-chicklet-wrapper').hide();
        $('.edit-Settings-Colors-chicklet-gradient-wrapper').show();
      }

      window['update_view']();

    }

    $(document).ready(function() {
      function updateDoiLink(id) {
          const linkElement = document.getElementById('doi-link');
          linkElement.innerHTML = '" . t('Listbuilding::form::Edit Double-Opt-In') . "';
          linkElement.href = '/app/double-opt-in/settings/$user->uid/' + id +'/edit';
      }

      $('.doi-select').on('change', function(e) {
        updateDoiLink($(this).val());
      });

      const select = $('#edit-rellistid-wrapper select');

      // Fix drupal styles to allow link next to dropdown.
      $('#edit-rellistid-wrapper').css('display', 'inline-block');
      select.css('max-width', 'unset');

      updateDoiLink(select.val())

    });

  ";

  $script .= SubscriptionFormsColors::GetColorSchemeJavaScript();

  Libraries::include('list_forms.inc');
  $script .= klicktipp_list_forms_customfield_settings_javascript();

  drupal_add_js($script, array('type' => 'inline', 'scope' => 'header'));

  return $form;

}

function klicktipp_list_forms_custom_edit_form_validate ($form, &$form_state ) {

  $UserID = $form_state['values']['uid'];
  $ArrayForm = $form_state['values']['ArrayForm'];
  $Name = $form_state['values']['Name'];
  $Settings = $form_state['values']['Settings'];

  if ($Name != $ArrayForm['Name'] && SubscriptionFormsCustom::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Subscription form (@type) with the name %name already exists.', array(
      '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_CUSTOM]),
      '%name' => $Name,
    )));
  }

  if (!empty($Settings['DisplayDSGVOCheckbox']) && empty($Settings['DSGVOText'])) {
    form_set_error('DSGVOText', t('The DSGVO text cannot be empty.'));
  }

  if ( $Settings['DisplayChicklet'] && strpos($Settings['ChickletText'], '%chicklet%') === FALSE ) {
    form_set_error('Settings][ChickletText', t('Please use the placeholder %chicklet% in your Chicklet text to display your active contact count.'));
  }

  $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['base']));
  if ( strlen($color) != 7 ) {
    form_set_error('Settings][Colors][base', t('Invalid hex color code.'));
  }

  if ( $Settings['Button']['button'] == 'text' ) {

    $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['button']));
    if ( strlen($color) != 7 ) {
      form_set_error('Settings][Colors][button', t('Invalid hex color code.'));
    }

    $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['button_text']));
    if ( strlen($color) != 7 ) {
      form_set_error('Settings][Colors][button_text', t('Invalid hex color code.'));
    }

  }

}

function klicktipp_list_forms_custom_edit_form_submit ($form, &$form_state ) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];
  $ArrayForm = $form_state['values']['ArrayForm'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $OptInSubscribeTo = array();
  if (!empty($form_state['values']['OptInSubscribeTo'])) {
    //in case user entered a free value, create new tag, it's 1 tag but pass as array, return value: $OptInSubscribeTo[0] = new/existing TagID
    $OptInSubscribeTo = Tag::CreateManualTagByTagName($UserID, array($form_state['values']['OptInSubscribeTo']));
    if (!$OptInSubscribeTo) {
      //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!UserID' => $UserID,
        '!function' => __FUNCTION__,
        '!BuildID' => $ArrayForm['BuildID'],
        '!OptInSubscribeTo' => $form_state['values']['OptInSubscribeTo'],
      );
      Errors::unexpected("Error: CreateManualTagByTagName with '!OptInSubscribeTo' in !function for User !UserID with BuildID !BuildID", $error, TRUE);
      return;
    }

  }

  $ArrayForm['Name'] = $Name;
  $ArrayForm['AssignTagID'] = (empty($OptInSubscribeTo)) ? 0 : $OptInSubscribeTo[0];
  $ArrayForm['RelListID'] = $form_state['values']['RelListID'];
  $ArrayForm['Settings'] = $form_state['values']['Settings'];
  $ArrayForm['Settings']['UseSpamProtection'] = 1;
  $ArrayForm[DatabaseTableLabeled::LABEL_DATA_KEY] = $MetaLabels;
    if (isset($form_state['values']['DeactivateCaptcha'])) {
        $ArrayForm['DeactivateCaptcha'] = !empty($form_state['values']['DeactivateCaptcha']);
    }

  // --- custom field settings

  //filters out every custom field that is not select (value == 0)
  $selected = array_filter($form_state['values']['CustomFields']['FormFieldsSelected']);

  $ArrayForm['FormFields'] = array();
  foreach ( $selected as $CustomFieldID => $value ) {

    $settings = $form_state['values']['CustomFields']['FormFields'][$CustomFieldID];

    if ( $CustomFieldID == 'EmailAddress' ) {

      // email address label
      $ArrayForm['EmailAddress'] = array(
        'label' => $form_state['values']['CustomFields']['FormFields']['EmailAddress']['label'],
        'weight' => $form_state['values']['CustomFields']['FormFieldWeights']['EmailAddress'],
      );

    }
    elseif ( $CustomFieldID == 'PhoneNumber' ) {

      $ArrayForm['PhoneNumber'] = array(
        'label' => $settings['label'],
        'required' => (empty($settings['required'])) ? 0 : $settings['required'],
        'hidden' => (empty($settings['hidden'])) ? 0 : $settings['hidden'],
        'weight' => $form_state['values']['CustomFields']['FormFieldWeights'][$CustomFieldID],
        'selected' => 1,
      );

    }
    else {

      $ArrayForm['FormFields'][$CustomFieldID] = array(
        'label' => $settings['label'],
        'type' => $settings['type'],
        'required' => (empty($settings['required'])) ? 0 : $settings['required'],
        'hidden' => (empty($settings['hidden'])) ? 0 : $settings['hidden'],
        'rows' => (empty($settings['rows'])) ? 0 : $settings['rows'],
        'default_value' => (empty($settings['default_value'])) ? '' : $settings['default_value'],
        'widget' => (empty($settings['widget'])) ? CustomFields::WIDGET_NONE : $settings['widget'],
        'weight' => $form_state['values']['CustomFields']['FormFieldWeights'][$CustomFieldID],
        'max_length' => $settings['max_length'] ?? '',
      );

      if (array_key_exists('allow_urls', $settings)) {
        $ArrayForm['FormFields'][$CustomFieldID]['allow_urls'] = intval($settings['allow_urls']);
      }
    }

  }

  if ( empty($selected['PhoneNumber']) ) {
    $ArrayForm['PhoneNumber']['selected'] = 0;
  }

  if ( $form_state['values']['AjaxPreview'] ) {

    $preview = SubscriptionFormsCustom::FromArray($ArrayForm);
    $preview->SetData($ArrayForm);

    $form_state['values']['AjaxReturn'] = $preview->GetHTML(TRUE);

    return;

  }

  if ( !empty($form_state['values']['CustomFields']['AddCustomFieldID']) ) {

    //a custom field has been selected to be added to the form
    //Note: the custom field id is written to the hidden field by javascript @see: list_forms.inc -> klicktipp_list_forms_customfield_settings_javascript()
    Libraries::include('list_forms.inc');
    klicktipp_list_forms_add_customfield($form_state, $ArrayForm);

    //the field has been added but we don't save the form, the user can still click on cancel
    return;

  }

  // update subscription form
  if (!SubscriptionFormsCustom::UpdateDB($ArrayForm)) {

    $error = array(
      '!UserID' => $UserID,
      '!BuildID' => $ArrayForm['BuildID'],
      '!Name' => $Name,
      '!ArrayForm' => $ArrayForm,
    );
    Errors::unexpected("SubscriptionFormCustom: Error while updating the Subscription form !Name for User !UserID with BuildID !BuildID.", $error, TRUE);

    return;

  }
  else {

    $ObjectForm = SubscriptionFormsCustom::FromID($UserID, $ArrayForm['BuildID']);

    if ( !$ObjectForm || !$ObjectForm->Publish() ) {

      $error = array(
        '!UserID' => $UserID,
        '!BuildID' => $ArrayForm['BuildID'],
        '!Name' => $Name,
        '!ArrayForm' => $ArrayForm,
      );
      Errors::unexpected("SubscriptionFormCustom: Error while publishing the Subscription form !Name for User !UserID with BuildID !BuildID.", $error, TRUE);

      return;

    }

  }

  drupal_set_message(t("Subscription form %name successfully updated.", array('%name' => $Name)));

}

function klicktipp_list_forms_custom_delete_confirm_form_submit($form, &$form_state) {

  $ArrayForm = $form_state['values']['Entity'];
  $BuildID = $ArrayForm['BuildID'];
  $UserID = $ArrayForm['RelOwnerUserID'];
  $Name = $ArrayForm['Name'];

  if ( SubscriptionFormsCustom::DeleteDB($ArrayForm) ) {
    drupal_set_message(t("Subscription form %name successfully deleted.", array('%name' => $Name)));
  }
  else {

    $error = array(
      '!UserID' => $UserID,
      '!BuildID' => $BuildID,
      '!Name' => $Name,
    );

    //notify user
    Errors::unexpected("SubscriptionFormCustom: Delete Subscription form (ID: !BuildID) for user: !UserID with name !Name", $error, TRUE);

  }

  klicktipp_set_redirect($form_state, "listbuilding/$UserID");

}

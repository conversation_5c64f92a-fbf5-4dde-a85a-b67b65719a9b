<?php

use App\Klicktipp\AccountManager;
use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\Errors;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\SubscriptionForms;
use App\Klicktipp\SubscriptionFormsColors;
use App\Klicktipp\SubscriptionFormsInline;
use App\Klicktipp\Tag;

function klicktipp_list_forms_inline_create_form ($form, $form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Create Subscription form (@type)', array(
    '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_INLINE]),
  ));
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Listbuilding'), "listbuilding/$UserID")), $page_title);

  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $weight = 1;

  $form = array();

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => '',
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? [] : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
      'container_class' => 'klicktipp-magicselect-short',
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Create Subscription form'),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "listbuilding/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_list_forms_inline_create_form_validate ($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];

  if (SubscriptionFormsInline::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Subscription form (@type) with the name %name already exists.', array(
      '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_INLINE]),
      '%name' => $Name,
    )));
  }

}

function klicktipp_list_forms_inline_create_form_submit ($form, &$form_state) {

  $Name = $form_state['values']['Name'];
  $UserID = $form_state['values']['uid'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $BuildID = SubscriptionFormsInline::InsertDB(array(
    'Name' => $Name,
    'RelOwnerUserID' => $UserID,
    'Settings' => [
      'UseSpamProtection' => 1,
    ],
    DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
  ));

  if (!empty($BuildID)) {

    drupal_set_message(t("Subscription form %name successfully created.", array('%name' => $Name)));

    // redirect to countdown edit
    klicktipp_set_redirect($form_state, "listbuilding/$UserID/form-inline/$BuildID/edit");

  }
  else {

    $error = array(
      '!uid' => $UserID,
      '!name' => $Name,
    );

    //notify user
    Errors::unexpected("SubscriptionFormInline: Create for UserID: !uid with name !name", $error, TRUE);

  }

}

function klicktipp_list_forms_inline_edit_form ($form, &$form_state, $account, $BuildID) {

  $UserID = $account->uid;

  /** @var SubscriptionFormsInline $ObjectForm */
  $ObjectForm = SubscriptionFormsInline::FromID($UserID, $BuildID);

  if ( empty($ObjectForm) ) {

    drupal_set_message(t("Subscription form not found."), 'error');
    drupal_goto("listbuilding/$UserID");
    return NULL;

  }

  $page_title = t('Edit Subscription form');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(
    l(t('Listbuilding'), "listbuilding/$UserID"),
  ), $page_title);

  $ArrayForm = $ObjectForm->GetData();

  $form = array();

  $form['#pre_render'][] = 'klicktipp_list_forms_inline_edit_form_pre_render';

  $Labels = $ObjectForm->GetMetaLabels();
  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $weight = 1;

  //retrieve lists of user
  $ListsOptions = Lists::RetrieveListsAsOptionsArray($UserID);

  //retrieve tags of user
  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['ArrayForm'] = array(
    '#type' => 'value',
    '#value' => $ArrayForm,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => $ArrayForm['Name'],
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? $Labels : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  $linkText = t('Listbuilding::form::Edit Double-Opt-In');
  $form['RelListID'] = array(
    '#type' => 'select',
    '#default_value' => $ArrayForm['RelListID'],
    '#title' => t("Subscription process"),
    '#weight' => $weight++,
    '#options' => $ListsOptions,
    '#suffix' => sprintf('<a href="#" id="doi-link">%s</a>', $linkText),
    '#attributes' => array('class' => array('doi-select')),
  );

  $form['OptInSubscribeTo'] = array(
    '#type' => 'textfield',
    '#default_value' => $ArrayForm['AssignTagID'],
    '#title' => t('Additional tagging (optional)'),
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => FALSE,
      'free_entries' => TRUE,
      'free_entries_message' => t('The tag will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['OptInSubscribeTo'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'additional-tagging',
  );

  $ajax = array(
    'callback' => 'klicktipp_form_ajax_callback',
    'wrapper' => 'formbuilder-ajax',
    'method' => 'replace',
  );

  $form['Preview'] = array(
    '#type' => 'klicktipp_block',
    '#title' => t('Preview'),
    '#weight' => $weight++,
    '#attributes' => array('class' => array('preview-box', 'preview-box-center')),
  );

  $form['Preview']['Content'] = array(
    '#type' => 'markup',
    '#markup' => $ObjectForm->GetHTML(TRUE),
    '#weight' => $weight++,
  );

  $form['FormStyle'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('preview-box', 'append')),
  );

  $form['FormStyle']['ColorTemplates'] = array(
    '#type' => 'markup',
    '#title' => t('Color templates'),
    '#markup' => "<label>" . t('Color templates') . "</label>" . SubscriptionFormsColors::DisplayButtonColorTemplates(),
    '#weight' => $weight++,
  );

  $form['Settings'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('settings-box')),
  );

  $form['Settings']['Responsive'] = array(
    '#type' => 'klicktipp_grid_row',
    '#weight' => $weight++,
  );

  $form['Settings']['Responsive']['Left'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('col-md-12')),
  );

  $form['Settings']['Responsive']['Left']['Width'] = array(
    '#type' => 'hidden', //make accessible by javascript
    '#default_value' => $ArrayForm['Settings']['Width'],
    '#parents' => array('Settings', 'Width'),
    '#attributes' => array(
      'data-event-load' => 'js_formbuilder_update',
    ),
  );

  $form['Settings']['Responsive']['Left']['Height'] = array(
    '#type' => 'hidden', //make accessible by javascript
    '#default_value' => $ArrayForm['Settings']['Height'],
    '#parents' => array('Settings', 'Height'),
  );

  $form['Settings']['Responsive']['Left']['Button'] = array(
    '#weight' => $weight++,
    '#tree' => TRUE,
    '#parents' => array('Settings', 'Button'),
  );

  $form['Settings']['Responsive']['Left']['Button']['color'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Button color'),
    '#default_value' => $ArrayForm['Settings']['Button']['color'],
    '#weight' => $weight++,
  );

  $form['Settings']['Responsive']['Left']['Button']['icon'] = array(
    '#type' => 'select',
    '#title' => t('Button icon color'),
    '#options' => array(
      '0' => t('color: white'),
      '150' => t('color: black'),
      '300' => t('color: grey'),
      '450' => t('color: purple'),
      '600' => t('color: violet'),
      '750' => t('color: dark blue'),
      '900' => t('color: blue'),
      '1050' => t('color: red'),
      '1200' => t('color: light orange'),
      '1350' => t('color: orange'),
      '1500' => t('color: green'),
      '1650' => t('color: light green'),
    ),
    '#default_value' => $ArrayForm['Settings']['Button']['icon'],
    '#ajax' => $ajax,
    '#weight' => $weight++,
  );

  $form['Settings']['Responsive']['Left']['Button']['size'] = array(
    '#type' => 'select',
    '#title' => t('Button size'),
    '#default_value' => $ArrayForm['Settings']['Button']['size'],
    '#options' => array(
      SubscriptionForms::BUTTON_SIZE_SMALL => t('size: small'),
      SubscriptionForms::BUTTON_SIZE_MEDIUM => t('size: medium'),
      SubscriptionForms::BUTTON_SIZE_LARGE => t('size: large'),
    ),
    '#weight' => $weight++,
    '#ajax' => $ajax,
  );

  $form['Settings']['Responsive']['Left']['AffiliateID'] = klicktipp_form_digistore_affiliate_checkbox($account, $ArrayForm['Settings']['AffiliateID'], $weight++, $ajax);
  $form['Settings']['Responsive']['Left']['AffiliateID']['#parents'] = array('Settings', 'AffiliateID');

  if ( variable_get('klicktipp_settings_digistore_enable_multi_device_tracking', 0) ) {

    $multidevicetracking = TRUE;
    if (!klicktipp_feature_access($account, 'access digistore multi device tracking')) {
      $multidevicetracking = FALSE;
      //quickhelp shows an upsell
      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-upsell',
          '#title' => t("Use Digistore24 Multi-Device-Tracking"),
        ),
      ));
    }
    elseif (empty($account->UserSettings['DigiStore24ApiKeys'])) {
      $multidevicetracking = FALSE;
      //quickhelp shows how to connect DigiStore24 accounts
      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-no-accounts',
          '#title' => t("Use Digistore24 Multi-Device-Tracking"),
        ),
      ));
    }
    else {
      //quickhelp shows info about multi-device-tracking (the user does not have to do anything, unlike OptimizePress etc.)
      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-generell',
          '#title' => t("Use Digistore24 Multi-Device-Tracking"),
        ),
      ));
    }

    $form['Settings']['Responsive']['Left']['UseDigistoreMultiDeviceTracking'] = array(
      '#type' => 'checkbox',
      '#title' => t("Use Digistore24 Multi-Device-Tracking") . $quickhelp,
      '#default_value' => empty($ArrayForm['Settings']['UseDigistoreMultiDeviceTracking']) ? 0 : 1,
      '#return_value' => 1,
      '#parents' => array('Settings', 'UseDigistoreMultiDeviceTracking'),
      '#disabled' => !$multidevicetracking,
      '#weight' => $weight++,
    );

  }

  $form['Settings']['Responsive']['Left']['EmailAddressLabel'] = array(
    '#type' => 'textfield',
    '#title' => t('Email address label'),
    '#default_value' => (empty($ArrayForm['EmailAddress']['label'])) ? t('Your email address (polite)') : $ArrayForm['EmailAddress']['label'],
    '#weight' => $weight++,
    '#description' => t('Enter the label for the email address field.'),
    '#required' => TRUE,
    '#attributes' => array(
      'class' => array('input-medium'),
    )
  );

  $form['Settings']['DisplayDSGVOCheckbox'] = array(
    '#type' => 'checkbox',
    '#title' => t('Display DSGVO checkbox'),
    '#default_value' => $ArrayForm['Settings']['DisplayDSGVOCheckbox'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'DisplayDSGVOCheckbox'),
    '#attributes' => array(
      'data-event-load' => 'js_formbuilder_display_dsgvocheckbox',
      'data-event-change' => 'js_formbuilder_display_dsgvocheckbox',
    ),
  );

  $form['Settings']['DSGVOText'] = array(
    '#type' => 'textarea',
    '#default_value' => (empty($ArrayForm['Settings']['DSGVOText'])) ? variable_get('listbuilding_htmldsgvotext_default', '') : $ArrayForm['Settings']['DSGVOText'],
    '#weight' => $weight++,
    '#resizable' => FALSE,
    '#parents' => array('Settings', 'DSGVOText'),
    '#attributes' => array(
      'class' => array('cke-textarea personalized jquery_ckeditor content-html'),
      'data-event-load' => 'js_init_ckeditor_personalization',
    ),
  );

    if (AccountManager::canDeactivateCaptcha($account)) {
        $form['Settings']['DeactivateCaptcha'] = array(
            '#type' => 'checkbox',
            '#title' => t("Captcha::FormEdit::Checkbox::I want to deactivate the captcha for this form"),
            '#default_value' => empty($ArrayForm['DeactivateCaptcha']) ? 0 : 1,
            '#return_value' => 1,
            '#weight' => $weight++,
            '#attributes' => array(
                'data-event-load' => 'js_checkbox_toggle_element_display',
                'data-event-load-args' => '#captcha-warning',
                'data-event-change' => 'js_checkbox_toggle_element_display',
                'data-event-change-args' => '#captcha-warning',
            )
        );

        $captchaWarning = t('Captcha::FormEdit::Warning::It is not recommended to deactivate the captcha protection.');
        $form['Settings']['DeactivateCaptchaWarning'] = array(
            '#type' => 'markup',
            '#markup' => '<div id="captcha-warning" class="alert alert-warning">' . $captchaWarning . '</div>',
            '#weight' => $weight++,
        );
    }

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => 10000,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['Save'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Save Subscription form'),
    '#weight' => $weight++,
  );

  Libraries::include('list_forms.inc');
  $form['buttons']['EmbedCode'] = klicktipp_list_forms_embed_code_modal("embed-code-$BuildID", $ObjectForm->GetEmbedCode(), $weight++);

  $ModalID = 'modalDeleteConfirmAjax';
  $Title = t('Delete Subscription form');

  $modalFormParameters = [
    "modalTitle" => $Title,
    "modalId" => $ModalID,
    "dependencyMessage" => t('This subscription form is used in the following objects and cannot be deleted:'),
    "entityId" => $BuildID,
    "entityClass" => SubscriptionFormsInline::class,
    "entityName" => $ArrayForm['Name'],
    "entity" => $ArrayForm,
    "submitFunction" => "klicktipp_list_forms_inline_delete_confirm_form_submit",
  ];

  // will include the generic modal form "klicktipp_delete_modal_form"
  Libraries::include("form_delete_modal.inc");

  $form['buttons']['ModalDeleteConfirmTrigger'] = [
    '#theme' => 'klicktipp_delete_modal_ajax_button',
    '#title' => $Title,
    '#value' => $ModalID,
    '#weight' => $weight++,
    '#modal_confirm' => drupal_get_form("klicktipp_delete_modal_form", $account, $modalFormParameters),
  ];

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "listbuilding/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_list_forms_inline_edit_form_pre_render ($form) {

  global $base_url;
  global $language;
  global $user;
  $path_to_CKF = "$base_url/ckfinder/";
  $lang = substr($language->language, 0, 2);

  drupal_add_js('

  window["content-html"] = 0;

  window["cke_persofocus"] = function(element, args) {
    $(".persofocus").removeClass("persofocus");
    $(args).addClass("persofocus");
    window["js_insert_placeholder"](element, args);
  }

  $(document).ready(function () {

    // Initialize the editor.
    var config = {
      toolbar:
      [
          ["Cut","Copy","Paste","PasteText","PasteFromWord"],
          ["Undo","Redo","-","SelectAll","RemoveFormat"],
          "/",
          ["Bold","Italic","Underline","Strike","-","Subscript","Superscript"],
          ["NumberedList","BulletedList","-","Outdent","Indent","Blockquote","CreateDiv"],
          ["JustifyLeft","JustifyCenter","JustifyRight","JustifyBlock"],
          ["Link","Unlink"],
          "/",
          ["Image","Table","HorizontalRule","SpecialChar"],
          ["Styles","Format","Font","FontSize"],
          ["TextColor","BGColor"],
          ["Maximize", "ShowBlocks"]
      ],
      enterMode : CKEDITOR.ENTER_BR,
      language: "' . $lang . '",
      height: "400px",
      resize_enabled : false,
      allowedContent: true,
      forceSimpleAmpersand: true,
      filebrowserUploadUrl : null, //disable upload tab
      filebrowserImageUploadUrl : null, //disable upload tab
      filebrowserFlashUploadUrl : null, //disable upload tab
      filebrowserBrowseUrl : "' . $path_to_CKF . 'ckfinder.html",
      filebrowserImageBrowseUrl : "' . $path_to_CKF . 'ckfinder.html",
      filebrowserFlashBrowseUrl: "' . $path_to_CKF . 'ckfinder.html",
    };

    try {
      $(".edit-Settings-DSGVOText").ckeditor(config);
      window["content-html"] = $(".edit-Settings-DSGVOText").ckeditorGet();

      //DataProcessor

      CKEDITOR.on( "instanceReady", function( ev ) {
        ckeditor = ev.editor;
        ckeditor.on("blur", function() {
          $("label[for=DSGVOCheckbox]").html(ckeditor.getData());
        });
        ckeditor.dataProcessor.htmlFilter.addRules({
          elements : {
            p : function(element) {
            //remove p-Tags containing just a &nbsp;
            var content = element.children[0].value;
            if (content && content.charCodeAt(0) == 160) {
              return false;
            }
          }
        }
      });

      });

    } catch (e) {
    alert(e);
    };

  });
  ', array('type' => 'inline', 'scope' => 'footer'));

  $script = "

    window['js_formbuilder_update'] = function (hasChanges) {

      var preview = $('#formbuilder-preview');

      if ( preview.length > 0 ) {

        $('.edit-Settings-Width').val(preview.outerWidth());
        $('.edit-Settings-Height').val(preview.outerHeight());

      }

      if ( hasChanges == 1 ) {
        var tooltip = $('<div></div>').css('display', 'inline-block');

        //deactivate embed code button
        $('#edit-embedcode').attr({
          'data-toggle': '',
          'data-modal-id': '',
          'disabled': 'disabled'
        }).wrap(tooltip);

        $('#edit-embedcode').parent().tooltip({
          'title': '" . t('Please save the Subscription form first.') . "',
          'container': 'body'
        });
      }

      window['update_view']();

    }

    // --- color picker ---

    window['ColorpickerMove'] = function (button, textfield, color) {

      if ( textfield.attr('id') == 'edit-settings-button-color' ) {

        $('.ktv2-form-inline .ktv2-submit-element-bg').css({
          'background-color': color.toHexString(),
        });

      }

    }

    window['js-select-color-template'] = function ( element, args ) {

      var colors = args.split(',');

      $('.edit-Settings-Button-color').setColor(colors[0]);
      $('.edit-Settings-Button-icon').val(colors[2]).trigger('change');

    }

    window['js_formbuilder_display_dsgvocheckbox'] = function (element, args) {
      if (element.prop('checked')) {
        $('#edit-settings-dsgvotext-wrapper').show();
        $('.ktv2-submit-element-button').attr('disabled', true);
      }
      else {
        $('#edit-settings-dsgvotext-wrapper').hide();
      }
      window['update_view']();
    }

    $(document).ready(function () {
      function updateDoiLink(id) {
          const linkElement = document.getElementById('doi-link');
          linkElement.innerHTML = '" . t('Listbuilding::form::Edit Double-Opt-In') . "';
          linkElement.href = '/app/double-opt-in/settings/$user->uid/' + id +'/edit';
      }

      $('.edit-EmailAddressLabel').keyup(function() {
        if ( $(this).val() == '' ) {
          $('#formbuilder-preview #FormField_EmailAddress').attr('placeholder', '" . t('Your email address (polite)') . "');
        }
        else {
          $('#formbuilder-preview #FormField_EmailAddress').attr('placeholder', $(this).val());
        }
      });

      $('#DSGVOCheckbox').on('change', function(e) {
        if ($(e.target).prop('checked')) {
          $('.ktv2-submit-element-button').attr('disabled', false);
        }
        else {
          $('.ktv2-submit-element-button').attr('disabled', true);
        }
      });

      $('.doi-select').on('change', function(e) {
        updateDoiLink($(this).val());
      });
      
      const select = $('#edit-rellistid-wrapper select');
      
      // Fix drupal styles to allow link next to dropdown.
      $('#edit-rellistid-wrapper').css('display', 'inline-block');
      select.css('max-width', 'unset');

      updateDoiLink(select.val())
    });

  ";

  drupal_add_js($script, array('type' => 'inline', 'scope' => 'header'));
  return $form;

}

function klicktipp_list_forms_inline_edit_form_validate ($form, &$form_state ) {

  $UserID = $form_state['values']['uid'];
  $ArrayForm = $form_state['values']['ArrayForm'];
  $Name = $form_state['values']['Name'];
  $Settings = $form_state['values']['Settings'];

  if ($Name != $ArrayForm['Name'] && SubscriptionFormsInline::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Subscription form (@type) with the name %name already exists.', array(
      '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_INLINE]),
      '%name' => $Name,
    )));
  }

  if (!empty($Settings['DisplayDSGVOCheckbox']) && empty($Settings['DSGVOText'])) {
    form_set_error('DSGVOText', t('The DSGVO text cannot be empty.'));
  }

  // validate colors

  if ( !SubscriptionFormsColors::FormatColorHexCode($form_state['values']['Settings']['Button']['color']) ) {
    form_set_error('Settings][Button][color', t('Invalid hex color code.'));
}

}

function klicktipp_list_forms_inline_edit_form_submit ($form, &$form_state ) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];
  $ArrayForm = $form_state['values']['ArrayForm'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $OptInSubscribeTo = array();
  if (!empty($form_state['values']['OptInSubscribeTo'])) {
    //in case user entered a free value, create new tag, it's 1 tag but pass as array, return value: $OptInSubscribeTo[0] = new/existing TagID
    $OptInSubscribeTo = Tag::CreateManualTagByTagName($UserID, array($form_state['values']['OptInSubscribeTo']));
    if (!$OptInSubscribeTo) {
      //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!UserID' => $UserID,
        '!function' => __FUNCTION__,
        '!BuildID' => $ArrayForm['BuildID'],
        '!OptInSubscribeTo' => $form_state['values']['OptInSubscribeTo'],
      );
      Errors::unexpected("Error: CreateManualTagByTagName with '!OptInSubscribeTo' in !function for User !UserID with BuildID !BuildID", $error, TRUE);
      return;
    }

  }

  $ArrayForm['Name'] = $Name;
  $ArrayForm['AssignTagID'] = (empty($OptInSubscribeTo)) ? 0 : $OptInSubscribeTo[0];
  $ArrayForm['RelListID'] = $form_state['values']['RelListID'];
  $ArrayForm['EmailAddress']['label'] = $form_state['values']['EmailAddressLabel'];
  $ArrayForm['Settings'] = $form_state['values']['Settings'];
  $ArrayForm['Settings']['UseSpamProtection'] = 1;
  $ArrayForm[DatabaseTableLabeled::LABEL_DATA_KEY] = $MetaLabels;
    if (isset($form_state['values']['DeactivateCaptcha'])) {
        $ArrayForm['DeactivateCaptcha'] = !empty($form_state['values']['DeactivateCaptcha']);
    }

  if ( $form_state['values']['AjaxPreview'] ) {

    $preview = SubscriptionFormsInline::FromArray($ArrayForm);
    $preview->SetData($ArrayForm);

    $form_state['values']['AjaxReturn'] = $preview->GetHTML(TRUE);

    return;

  }

  // update subscription form
  if (!SubscriptionFormsInline::UpdateDB($ArrayForm)) {

    $error = array(
      '!UserID' => $UserID,
      '!BuildID' => $ArrayForm['BuildID'],
      '!Name' => $Name,
      '!ArrayForm' => $ArrayForm,
    );
    Errors::unexpected("SubscriptionFormInline: Error while updating the Subscription form !Name for User !UserID with BuildID !BuildID.", $error, TRUE);

    return;

  }
  else {

    $ObjectForm = SubscriptionFormsInline::FromID($UserID, $ArrayForm['BuildID']);

    if ( !$ObjectForm || !$ObjectForm->Publish() ) {

      $error = array(
        '!UserID' => $UserID,
        '!BuildID' => $ArrayForm['BuildID'],
        '!Name' => $Name,
        '!ArrayForm' => $ArrayForm,
      );
      Errors::unexpected("SubscriptionFormInline: Error while publishing the Subscription form !Name for User !UserID with BuildID !BuildID.", $error, TRUE);

      return;

    }

  }

  drupal_set_message(t("Subscription form %name successfully updated.", array('%name' => $Name)));

}

function klicktipp_list_forms_inline_delete_confirm_form_submit($form, &$form_state) {

  $ArrayForm = $form_state['values']['Entity'];
  $BuildID = $ArrayForm['BuildID'];
  $UserID = $ArrayForm['RelOwnerUserID'];
  $Name = $ArrayForm['Name'];

  if ( SubscriptionFormsInline::DeleteDB($ArrayForm) ) {
    drupal_set_message(t("Subscription fom %name successfully deleted.", array('%name' => $Name)));
  }
  else {

    $error = array(
      '!UserID' => $UserID,
      '!BuildID' => $BuildID,
      '!Name' => $Name,
    );

    //notify user
    Errors::unexpected("SubscriptionFormInline: Delete Subscription form (ID: !BuildID) for user: !UserID with name !Name", $error, TRUE);

  }

  klicktipp_set_redirect($form_state, "listbuilding/$UserID");

}

<?php

use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\Errors;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\Tag;
use App\Klicktipp\ToolOutboundKajabi;

function klicktipp_tools_kajabi_create_form($form, $form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Create Kajabi Membership Activation');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Kajabi Overview'), "tools/$UserID/outbound")), $page_title);

  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $weight = 1;

  $form = array();

  // --- check if the count outbound limit has been reached ---
  $MaxTools = ToolOutboundKajabi::LimitReached($UserID);
  if ($MaxTools !== FALSE) {

    $content = _klicktipp_get_content_include('klicktipp_outbound_kajabi_limit_upsell');

    $form['LimitExeeded'] = array(
      '#type' => 'markup',
      '#value' => ($content) ? $content : t("You have reached the maximum number of %count Outbound Activations. If you need more activations, please upgrade your membership.", array('%count' => $MaxTools)),
    );

    return $form;

  }

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => '',
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? [] : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
      'container_class' => 'klicktipp-magicselect-short',
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));
  $form['ActivationTagID'] = array(
    '#type' => 'textfield',
    '#title' => t("Activation tag"),
    '#default_value' => '',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('The tag will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['ActivationTagID'],
      //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'edit-kajabi-activation-tagid',
  );

  $form['ActivationURL'] = array(
    '#type' => 'textfield',
    '#title' => t("Activation URL"),
    '#default_value' => '',
    '#required' => TRUE,
    '#weight' => $weight++,
    //allow & for urls
    '#element_validate' => array('klicktipp_element_textfield_validate_url'),
    '#quickhelp' => 'edit-kajabi-activation-url',
    '#maxlength' => 800,
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Create Kajabi Activation'),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "tools/$UserID/outbound",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_tools_kajabi_create_form_validate($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];

  if (ToolOutboundKajabi::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Kajabi activation with the name %name already exists.', array(
      '%name' => $Name,
    )));
  }

}

function klicktipp_tools_kajabi_create_form_submit($form, &$form_state) {

  $Name = $form_state['values']['Name'];
  $UserID = $form_state['values']['uid'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $ArrayTool = array(
    'Name' => $Name,
    'RelOwnerUserID' => $UserID,
    'ActivationURL' => $form_state['values']['ActivationURL'],
    DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
  );

  if (!empty($form_state['values']['ActivationTagID'])) {
    $ActivationTagID = Tag::CreateManualTagByTagName($UserID, $form_state['values']['ActivationTagID']);
    if (!$ActivationTagID) {
      //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!data' => $form_state['values']['ActivationTagID'],
        '!UserID' => $UserID,
      );
      Errors::unexpected("Error: CreateManualTagByTagName in Kajabi edit. UserID: !UserID", $error, TRUE); //notify user
      return;
    }
    else {
      $ArrayTool['ActivationTagID'] = $ActivationTagID;
    }
  }

  $NewToolID = ToolOutboundKajabi::InsertDB($ArrayTool);

  if (!empty($NewToolID)) {

    drupal_set_message(t("Kajabi activation %name successfully created.", array('%name' => $Name)));

    // redirect to tagging pixel edit
    klicktipp_set_redirect($form_state, "tools/$UserID/kajabi/$NewToolID/edit");

  }
  else {

    $error = array(
      '!uid' => $UserID,
      '!name' => $Name,
    );

    //notify user
    Errors::unexpected("Kajabi Create for UserID: !uid with name !name", $error, TRUE);

  }

}

function klicktipp_tools_kajabi_edit_form($form, &$form_state, $account, $ToolID) {

  $UserID = $account->uid;

  /** @var ToolOutboundKajabi $ObjectTool */
  $ObjectTool = ToolOutboundKajabi::FromID($UserID, $ToolID);
  if (empty($ObjectTool)) {
    drupal_set_message(t("Kajabi activation not found."), 'error');
    drupal_goto("tools/$UserID/outbound");
    return NULL;
  }

  //set breadcrumb and page title
  $page_title = t('Edit Kajabi Membership Activation');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Kajabi Overview'), "tools/$UserID/outbound")), $page_title);

  $ArrayTool = $ObjectTool->GetData();

  $Labels = $ObjectTool->GetMetaLabels();
  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $weight = 1;

  $form = array();

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['ObjectTool'] = array(
    '#type' => 'value',
    '#value' => $ObjectTool,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => $ArrayTool['Name'],
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? $Labels : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));
  $form['ActivationTagID'] = array(
    '#type' => 'textfield',
    '#title' => t("Activation tag"),
    '#default_value' => $ArrayTool['ActivationTagID'],
    '#required' => TRUE,
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['ActivationTagID'],
      //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'edit-kajabi-activation-tagid',
  );

  $form['ActivationURL'] = array(
    '#type' => 'textfield',
    '#title' => t("Activation URL"),
    '#default_value' => $ArrayTool['ActivationURL'],
    '#required' => TRUE,
    '#weight' => $weight++,
    //allow & for urls
    '#element_validate' => array('klicktipp_element_textfield_validate_url'),
    '#quickhelp' => 'edit-kajabi-activation-url',
    '#maxlength' => 800,
  );

  $form['DeactivationTagID'] = array(
    '#type' => 'textfield',
    '#title' => t("Deactivation tag"),
    '#default_value' => $ArrayTool['DeactivationTagID'],
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('The tag will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['DeactivationTagID'],
      //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'edit-kajabi-deactivation-tagid',
  );

  $form['DeactivationURL'] = array(
    '#type' => 'textfield',
    '#title' => t("Deactivation URL"),
    '#default_value' => $ArrayTool['DeactivationURL'],
    '#weight' => $weight++,
    //allow & for urls
    '#element_validate' => array('klicktipp_element_textfield_validate_url'),
    '#quickhelp' => 'edit-kajabi-deactivation-url',
    '#maxlength' => 800,
  );

  $form['SendFirstnameOnly'] = array(
    '#type' => 'checkbox',
    '#title' => t('Send firstname only') . theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'edit-kajabi-firstname-only',
          '#title' => t('Send firstname only to Kajabi'),
        )
      )),
    '#default_value' => isset($ArrayTool['SendFirstnameOnly']) ? $ArrayTool['SendFirstnameOnly'] : 0,
    '#weight' => $weight++,
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => 10000,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['Save'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Save Kajabi Activation'),
    '#weight' => $weight++,
  );

  $form['buttons']['ModalDeleteConfirmTrigger'] = array(
    '#theme' => 'klicktipp_delete_modal_button',
    '#title' => t('Delete Kajabi Activation'),
    '#value' => 'klicktipp_tools_kajabi_delete_confirm_form',
    '#weight' => $weight++,
    '#external' => FALSE,
    '#modal_confirm' => drupal_get_form('klicktipp_tools_kajabi_delete_confirm_form', $account, $ToolID),
  );

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "tools/$UserID/outbound",
    '#weight' => $weight++,
  );

  return $form;

}


function klicktipp_tools_kajabi_edit_form_validate($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $ArrayTool = $form_state['values']['ObjectTool']->GetData();

  $Name = $form_state['values']['Name'];

  //check if updated name already exists
  if ($Name != $ArrayTool['Name'] && ToolOutboundKajabi::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Kajabi activation with the name %name already exists.', array(
      '%name' => $Name,
    )));
  }

  // activation and deactivation tags must be disjoint (otherwise we get race conditions)
  $ArrayTags = Tag::RetrieveManualTags($UserID);

  $intersection = array_values(array_intersect($form_state['values']['ActivationTagID'], $form_state['values']['DeactivationTagID']));
  if (count($intersection) == 1) {
    //if empty($ArrayTags[$intersection[0]]['TagName']) the user tried to AutoCreate the same tag in OptInSubscribeTo and OptInUnsubscribeFrom
    $tag_name = (empty($ArrayTags[$intersection[0]]['TagName'])) ? $intersection[0] : $ArrayTags[$intersection[0]]['TagName'];
    form_set_error('DeactivationTagID', t("Tag %name cannot be used in activation and deactivation at the same time.", array('%name' => $tag_name)));
  }
  else {
    if (count($intersection) > 1) {
      $tag_names = array();
      foreach ($intersection as $id) {
        //if empty($ArrayTags[$id]['TagName']) the user tried to AutoCreate the same tags in OptInSubscribeTo and OptInUnsubscribeFrom
        $tag_names[] = (empty($ArrayTags[$id]['TagName'])) ? $id : $ArrayTags[$id]['TagName'];
      }

      form_set_error('DeactivationTagID', t("The tags %names cannot be used in activation and deactivation at the same time.", array('%names' => implode(', ', $tag_names))));
    }
  }

  // URL must be present if tags are given
  if (!empty($form_state['values']['DeactivationTagID']) && empty($form_state['values']['DeactivationURL'])) {
    form_set_error('DeactivationURL', t("The deactivation URL must be filled, if deactivation tags are selected."));
  }

  if (strcasecmp(trim($form_state['values']['ActivationURL']), trim($form_state['values']['DeactivationURL'])) == 0) {
    form_set_error('DeactivationURL', t("The deactivation URL must be different from the activation URL."));
  }
}

function klicktipp_tools_kajabi_edit_form_submit($form, &$form_state) {

  $Name = $form_state['values']['Name'];
  $UserID = $form_state['values']['uid'];
  /** @var ToolOutboundKajabi $ObjectTool */
  $ObjectTool = $form_state['values']['ObjectTool'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $ArrayTool = $ObjectTool->GetData();
  $ArrayTool['Name'] = $Name;
  $ArrayTool['ActivationURL'] = trim($form_state['values']['ActivationURL']);
  $ArrayTool['DeactivationURL'] = trim($form_state['values']['DeactivationURL']);
  $ArrayTool['SendFirstnameOnly'] = $form_state['values']['SendFirstnameOnly'];
  $ArrayTool[DatabaseTableLabeled::LABEL_DATA_KEY] = $MetaLabels;

  if (!empty($form_state['values']['ActivationTagID'])) {
    $ActivationTagID = Tag::CreateManualTagByTagName($UserID, $form_state['values']['ActivationTagID']);
    if (!$ActivationTagID) {
      //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!data' => $form_state['values']['ActivationTagID'],
        '!UserID' => $UserID,
      );
      Errors::unexpected("Error: CreateManualTagByTagName in Kajabi edit. UserID: !UserID", $error, TRUE); //notify user
      return;
    }
    else {
      $ArrayTool['ActivationTagID'] = $ActivationTagID;
    }
  }

  if (!empty($form_state['values']['DeactivationTagID'])) {
    $DeactivationTagID = Tag::CreateManualTagByTagName($UserID, $form_state['values']['DeactivationTagID']);
    if (!$DeactivationTagID) {
      //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!data' => $form_state['values']['DeactivationTagID'],
        '!UserID' => $UserID,
      );
      Errors::unexpected("Error: CreateManualTagByTagName in Kajabi edit. UserID: !UserID", $error, TRUE); //notify user
      return;
    }
    else {
      $ArrayTool['DeactivationTagID'] = $DeactivationTagID;
    }
  }

  if (ToolOutboundKajabi::UpdateDB($ArrayTool)) {

    drupal_set_message(t("Kajabi activation %name successfully updated.", array('%name' => $Name)));

  }
  else {
    $error = array(
      '!uid' => $UserID,
      '!ToolID' => $ArrayTool['ToolID'],
      '!name' => $Name,
      '!Kajabi' => $ArrayTool,
    );

    //notify user
    Errors::unexpected("Kajabi: Update for UserID: !uid with name !name (ToolID: !ToolID)", $error, TRUE);

  }

}

function klicktipp_tools_kajabi_delete_confirm_form($form, $form_state, $account, $ToolID) {

  $UserID = $account->uid;

  $ArrayTool = ToolOutboundKajabi::FromID($UserID, $ToolID)->GetDBArray();

  $EntityName = $ArrayTool['Name'];
  $Title = t('Delete Kajabi Membership Activation');

  $form = _klicktipp_confirm_form_modal('klicktipp_tools_kajabi_delete_confirm_form', $EntityName, $Title);

  $form['Entity'] = array(
    '#type' => 'value',
    '#value' => $ArrayTool,
  );

  return $form;

}

function klicktipp_tools_kajabi_delete_confirm_form_submit($form, &$form_state) {

  $ArrayTool = $form_state['values']['Entity'];
  $UserID = $ArrayTool['RelOwnerUserID'];
  $Name = $ArrayTool['Name'];

  //set deleted flag but do not actually delete since there could be still links out there
  if (ToolOutboundKajabi::DeleteDB($ArrayTool)) {
    drupal_set_message(t("Kajabi activation %name successfully deleted.", array('%name' => $Name)));
  }
  else {

    $error = array(
      '!data' => $ArrayTool,
      '!function' => __FUNCTION__,
    );
    //notify user
    Errors::unexpected("DeleteDB !function", $error, TRUE);

  }

  klicktipp_set_redirect($form_state, "tools/$UserID/outbound");

}

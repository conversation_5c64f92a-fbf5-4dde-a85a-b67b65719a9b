<?php

use App\Klicktipp\BlacklistHandler;
use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\Dates;
use App\Klicktipp\Emails;
use App\Klicktipp\EmailsConfirmationEmail;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\ToolPluginGeneral;

define("CONTROLLER_LIST_SAVE_DOI_PROCESS", /*t(*/'Save Subscription process'/*)*/);
define('CONTROLLER_COPY_DOI_PROCESS', /*t(*/'Copy'/*)*/);

/**
 * list create form
 */
function klicktipp_list_create_form($form, $form_state, $account, $ListIdToCopy = '') {

  if (!user_access('klicktipp admin role')) {
    // dialog is migrated to angular, only admins can access the old dialog for debugging
    drupal_access_denied();
    drupal_exit();
  }

  $form = array();
  $weight = 1;

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = empty($ListIdToCopy) ? t('Create Subscription process') : t('Copy Subscription process');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Subscription processes'), "lists/$UserID")), $page_title);

  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  if (!empty($form_state['values']['Name'])) {
    $ListName = $form_state['values']['Name'];
  } else {
    $ListName = '';
    if (!empty($ListIdToCopy)) {
      $UserID = $account->uid;
      /** @var Lists $ObjectList */
      $ObjectList = Lists::FromID($UserID, $ListIdToCopy);
      $List = $ObjectList->GetData();
      $ListName = t('Duplicate of: ') . $List['Name'];
    }
  }

  $form['Name'] = array(
    '#type' => 'textfield',
    '#title' => t('Name'),
    '#default_value' => $ListName,
    '#weight' => $weight++,
    '#required' => TRUE,
  );

  $form['ListIdToCopy'] = array(
    '#type' => 'value',
    '#value' => $ListIdToCopy,
  );

  if (!empty($form_state['input']['MetaLabels'])) {
    $Labels = $form_state['input']['MetaLabels'];
  } else {
    $Labels = empty($ObjectList) ? [] : $ObjectList->GetMetaLabels();
  }

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => $Labels,
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
      'container_class' => 'klicktipp-magicselect-short',
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Create Subscription process'),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "lists/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

/**
 * list create form validate
 */
function klicktipp_list_create_form_validate($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];

  //existing and not deleted lists
  $ExistingList = Lists::RetrieveList(array('ListID'), array(
    'RelOwnerUserID' => $UserID,
    'Name' => $Name,
    'Deleted' => 0,
  ));
  if ($ExistingList) {
    form_set_error('Name', t('A subscription process with the name %name already exists', array('%name' => $Name)));
  }

}

/**
 * list create form submit
 */
function klicktipp_list_create_form_submit($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);
  $ListIdToCopy = $form_state['values']['ListIdToCopy'];

  if (empty($ListIdToCopy)) {
    $ArrayFieldAndValues = [
      'RelOwnerUserID' => $UserID,
      'Name' => $Name,
      'SubscriptionConfirmationPendingPageURL' => '',
      'SubscriptionConfirmedPageURL' => '',
      DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
    ];
    $NewListID = Lists::InsertDB($ArrayFieldAndValues);
  } else {
    // copy the given list ...
    $ArrayFieldAndValues = Lists::RetrieveListByID($UserID, $ListIdToCopy);
    $OldName = $ArrayFieldAndValues['Name'];
    $ArrayConfirmEmail = EmailsConfirmationEmail::FromID($UserID, $ArrayFieldAndValues['RelOptInConfirmationEmailID'])->GetData();
    $ArrayFieldAndValues['Name'] = $Name;
    $ArrayFieldAndValues[DatabaseTableLabeled::LABEL_DATA_KEY] = $MetaLabels;
    $NewListID = Lists::Import($UserID, $ArrayFieldAndValues, t('Copy of'));

    // fix names, because import adds name prefix and empty prefix adds leading SPACE
    if (!empty($NewListID)) {
      $ArrayFieldAndValues = Lists::RetrieveListByID($UserID, $NewListID);
      $ArrayFieldAndValues['Name'] = $Name;
      Lists::UpdateDB($ArrayFieldAndValues);
      // rename confirm email too, because the name is displayed in the breadcrumb when the email is edited
      $ImportedArrayEmail = EmailsConfirmationEmail::FromID($UserID, $ArrayFieldAndValues['RelOptInConfirmationEmailID'])->GetData();
      EmailsConfirmationEmail::SetNameInDBArray($ImportedArrayEmail, t('Confirmation Email: @name', ['@name' => $Name]));
      // import assumes that another user imports an email, where it makes sense
      // to overwrite FromName/FromEmail with the importer's info
      // but here a real copy is needed, so overwrite From* with the old values
      $ImportedArrayEmail['FromName'] = $ArrayConfirmEmail['FromName'];
      $ImportedArrayEmail['FromEmail'] = $ArrayConfirmEmail['FromEmail'];
      EmailsConfirmationEmail::UpdateDB($ImportedArrayEmail);
    }
  }

  if ($NewListID) {

    if (empty($ListIdToCopy)) {
      drupal_set_message(t("Subscription process %name successfully created.", ['%name' => $Name]));
    } else {
      drupal_set_message(t("Created new subscription process @newName based on @oldName.", [
        '@newName' => $Name,
        '@oldName' => $OldName,
      ]));
    }

    //redirect to edit dialog -> on create the user cannot enter his own pending and thankyou page or the confirmation email
    klicktipp_set_redirect($form_state, "lists/$UserID/$NewListID/edit");

  }
  else {

    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');

  }


}

/**
 * Implements hook_form().
 */
function klicktipp_list_edit_form($form, $form_state, $account, $ListID) {

  if (!user_access('klicktipp admin role')) {
    // dialog is migrated to angular, only admins can access the old dialog for debugging
    drupal_access_denied();
    drupal_exit();
  }

  $UserID = $account->uid;

  /** @var Lists $ObjectList */
  $ObjectList = Lists::FromID($UserID, $ListID);
  $List = $ObjectList->GetData();

  if (empty($List)) {

    drupal_set_message(t("Subscription process not found."), "error");
    drupal_goto("lists/$UserID");
    return NULL;

  }

  $isDefaultList = (empty($List['Name']));

  //set breadcrumb and page title
  $page_title = t('Edit Subscription process');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Subscription processes'), "lists/$UserID")), $page_title);

  $Labels = $ObjectList->GetMetaLabels();
  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $form = array();

  $weight = 1;

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['List'] = array(
    '#type' => 'value',
    '#value' => $List,
  );

  $form['isDefaultList'] = array(
    '#type' => 'value',
    '#value' => $isDefaultList,
  );

  if ($isDefaultList) {
    $form['DefaultName'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_item',
      '#value' => t("Default subscription process"),
      '#weight' => $weight++,
      '#title' => t('Name'),
    );
  }
  else {
    $form['Name'] = array(
      '#type' => 'textfield',
      '#default_value' => $List['Name'],
      '#weight' => $weight++,
      '#title' => t('Name'),
      '#required' => TRUE,
    );
  }

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? $Labels : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  if (!empty($account->UserPrivileges['AllowSingleOptinLists'])) {
    //the user has the privilege to create single optin lists

    $quickhelp = theme('klicktipp_quickhelp', array(
      'element' => array(
        '#quickhelp' => 'subscription-process-use-single-optin',
        '#title' => t("Use Single Optin"),
      )
    ));

    $form['OptInModeEnum'] = array(
      '#type' => 'checkbox',
      '#title' => t("Use Single Optin") . $quickhelp,
      '#default_value' => $List['OptInModeEnum'],
      '#return_value' => Lists::OPTIN_MODE_SINGLE,
      '#weight' => $weight++,
      '#attributes' => array(
        //do not display the re-send checkbox if single optin is activated @see script.js
        'data-event-load' => 'js_checkbox_toggle_element_display_invert',
        'data-event-load-args' => '#edit-confirmationemail, #edit-useownpendingpage-wrapper, #edit-resendconfirmationemail-wrapper, #edit-pendingsubscribersdelay-wrapper',
        'data-event-click' => 'js_checkbox_toggle_element_display_invert',
        'data-event-click-args' => '#edit-confirmationemail, #edit-useownpendingpage-wrapper, #edit-resendconfirmationemail-wrapper, #edit-pendingsubscribersdelay-wrapper',
      ),
    );
  }
  else {
    //the user does not have the privilege to create single optin lists
    //display a disabled checkbox with upsell quickhelp
    //NOTE: if the upsell quickhelp 'subscription-process-use-single-optin-upsell' is not set (no help text),
    //do not show the checkbox

    $UpsellQuickhelp = klicktipp_quickhelp('subscription-process-use-single-optin-upsell');

    if (!empty($UpsellQuickhelp['help_text'])) {

      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'subscription-process-use-single-optin-upsell',
          '#title' => t("Use Single Optin"),
        )
      ));

      $form['EnterpriseUpsell'] = array(
        '#type' => 'checkbox',
        '#title' => t("Use Single Optin") . $quickhelp,
        '#value' => 0,
        '#disabled' => TRUE,
        '#weight' => $weight++,
      );
    }
  }

  $form['ConfirmationEmail'] = array(
    '#title' => t("Edit confirmation email"),
    '#value' => "email/$UserID/edit/confirmation/$ListID/" . $List['RelOptInConfirmationEmailID'],
    '#theme' => 'klicktipp_edit_button',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );


  $quickhelp = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'subscription-process-pending-page',
      '#title' => t("Display my own confirmation pending page"),
    )
  ));
  $QuickhelpSubscriberParameter = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'subscription-process-use-subscriber-parameter',
      '#title' => t("Display subscriber id parameter"),
    )
  ));
  $QuickhelpListParameter = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'subscription-process-use-list-parameter',
      '#title' => t("Display list id parameter"),
    )
  ));
  $QuickhelpEmailParameter = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'subscription-process-use-email-parameter',
      '#title' => t("Display email parameter"),
    )
  ));
  $QuickhelpSubscriberKeyParameter = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'subscription-process-use-subscriberkey-parameter',
      '#title' => t("Display contact-key parameter"),
    )
  ));
  $QuickhelpReferralLinkParameter = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'subscription-process-use-referral-link-parameter',
      '#title' => t("list-confirmed-parameters-referral:Add referral link"),
    )
  ));

  $form['UseOwnPendingPage'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display my own confirmation pending page") . $quickhelp,
    '#default_value' => empty($List['SubscriptionConfirmationPendingPageURL']) ? 0 : 1,
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#pending-parameters',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#pending-parameters',
    ),
  );

  $form['PendingParameters'] = array(
    '#type' => 'fieldset',
    '#title' => t('Pending Page Parameters'),
    '#collapsible' => FALSE,
    '#weight' => $weight++,
    '#attributes' => array(
      'id' => 'pending-parameters',
    ),
    '#tree' => TRUE,
  );

  $form['PendingParameters']['SubscriptionConfirmationPendingPageURL'] = array(
    '#type' => 'textfield',
    '#title' => t('confirmation pending page'),
    '#default_value' => empty($List['SubscriptionConfirmationPendingPageURL']) ? 'https://' : $List['SubscriptionConfirmationPendingPageURL'],
    '#weight' => $weight++,
    '#element_validate' => array('klicktipp_element_textfield_validate_url'),
    //allow & for urls
    '#maxlength' => 800,
    '#quickhelp' => 'doi-subscriptionconfirmationpendingpageurl',
  );

  $form['PendingParameters']['UseSubscriberParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display subscriber id parameter") . $QuickhelpSubscriberParameter,
    '#default_value' => !isset($List['Pending']['UseSubscriberParameter']) ? 1 : $List['Pending']['UseSubscriberParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-pendingparameters-subscriberparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-pendingparameters-subscriberparametername-wrapper',
    ),
  );

  $form['PendingParameters']['SubscriberParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($List['Pending']['SubscriberParameterName']) ? 'SubscriberID' : $List['Pending']['SubscriberParameterName'],
    '#weight' => $weight++,
  );

  $form['PendingParameters']['UseListParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display list id parameter") . $QuickhelpListParameter,
    '#default_value' => !isset($List['Pending']['UseListParameter']) ? 1 : $List['Pending']['UseListParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-pendingparameters-listparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-pendingparameters-listparametername-wrapper',
    ),
  );

  $form['PendingParameters']['ListParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($List['Pending']['ListParameterName']) ? 'listid' : $List['Pending']['ListParameterName'],
    '#weight' => $weight++,
  );

  $form['PendingParameters']['UseEmailParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display email parameter") . $QuickhelpEmailParameter,
    '#default_value' => !isset($List['Pending']['UseEmailParameter']) ? 1 : $List['Pending']['UseEmailParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-pendingparameters-emailparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-pendingparameters-emailparametername-wrapper',
    ),
  );

  $form['PendingParameters']['EmailParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($List['Pending']['EmailParameterName']) ? 'email' : $List['Pending']['EmailParameterName'],
    '#weight' => $weight++,
  );

  $form['PendingParameters']['UseSubscriberKeyParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display contact-key parameter") . $QuickhelpSubscriberKeyParameter,
    '#default_value' => !isset($List['Pending']['UseSubscriberKeyParameter']) ? 1 : $List['Pending']['UseSubscriberKeyParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-pendingparameters-subscriberkeyparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-pendingparameters-subscriberkeyparametername-wrapper',
    ),
  );

  $form['PendingParameters']['SubscriberKeyParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($List['Pending']['SubscriberKeyParameterName']) ? 'ContactKey' : $List['Pending']['SubscriberKeyParameterName'],
    '#weight' => $weight++,
  );

  $quickhelp = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'subscription-process-confirmation-page',
      '#title' => t("Display my own subscription confirmed page"),
    )
  ));

  $form['UseOwnConfirmedPage'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display my own subscription confirmed page") . $quickhelp,
    '#default_value' => empty($List['SubscriptionConfirmedPageURL']) ? 0 : 1,
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#subscription-confirmed-parameters',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#subscription-confirmed-parameters',
    ),
  );

  $form['SubscriptionConfirmedParameters'] = array(
    '#type' => 'fieldset',
    '#title' => t('Subscription Confirmed Page Parameters'),
    '#collapsible' => FALSE,
    '#weight' => $weight++,
    '#attributes' => array(
      'id' => 'subscription-confirmed-parameters',
    ),
    '#tree' => TRUE,
  );

  $form['SubscriptionConfirmedParameters']['SubscriptionConfirmedPageURL'] = array(
    '#type' => 'textfield',
    '#title' => t('subscription confirmed page'),
    '#default_value' => empty($List['SubscriptionConfirmedPageURL']) ? 'https://' : $List['SubscriptionConfirmedPageURL'],
    '#weight' => $weight++,
    '#element_validate' => array('klicktipp_element_textfield_validate_url'),
    //allow & for urls
    '#maxlength' => 800,
    '#quickhelp' => 'doi-subscriptionconfirmedpageurl',
  );

  $form['SubscriptionConfirmedParameters']['UseSubscriberParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display subscriber id parameter") . $QuickhelpSubscriberParameter,
    '#default_value' => !isset($List['Confirmed']['UseSubscriberParameter']) ? 1 : $List['Confirmed']['UseSubscriberParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-subscriptionconfirmedparameters-subscriberparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-subscriptionconfirmedparameters-subscriberparametername-wrapper',
    ),
  );

  $form['SubscriptionConfirmedParameters']['SubscriberParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($List['Confirmed']['SubscriberParameterName']) ? 'SubscriberID' : $List['Confirmed']['SubscriberParameterName'],
    '#weight' => $weight++,
  );

  $form['SubscriptionConfirmedParameters']['UseListParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display list id parameter") . $QuickhelpListParameter,
    '#default_value' => !isset($List['Confirmed']['UseListParameter']) ? 1 : $List['Confirmed']['UseListParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-subscriptionconfirmedparameters-listparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-subscriptionconfirmedparameters-listparametername-wrapper',
    ),
  );

  $form['SubscriptionConfirmedParameters']['ListParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($List['Confirmed']['ListParameterName']) ? 'listid' : $List['Confirmed']['ListParameterName'],
    '#weight' => $weight++,
  );

  $form['SubscriptionConfirmedParameters']['UseEmailParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display email parameter") . $QuickhelpEmailParameter,
    '#default_value' => !isset($List['Confirmed']['UseEmailParameter']) ? 1 : $List['Confirmed']['UseEmailParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-subscriptionconfirmedparameters-emailparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-subscriptionconfirmedparameters-emailparametername-wrapper',
    ),
  );

  $form['SubscriptionConfirmedParameters']['EmailParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($List['Confirmed']['EmailParameterName']) ? 'email' : $List['Confirmed']['EmailParameterName'],
    '#weight' => $weight++,
  );

  $form['SubscriptionConfirmedParameters']['UseSubscriberKeyParameter'] = array(
    '#type' => 'checkbox',
    '#title' => t("Display contact-key parameter") . $QuickhelpSubscriberKeyParameter,
    '#default_value' => !isset($List['Confirmed']['UseSubscriberKeyParameter']) ? 1 : $List['Confirmed']['UseSubscriberKeyParameter'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_checkbox_toggle_element_display',
      'data-event-load-args' => '#edit-subscriptionconfirmedparameters-subscriberkeyparametername-wrapper',
      'data-event-change' => 'js_checkbox_toggle_element_display',
      'data-event-change-args' => '#edit-subscriptionconfirmedparameters-subscriberkeyparametername-wrapper',
    ),
  );

  $form['SubscriptionConfirmedParameters']['SubscriberKeyParameterName'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($List['Confirmed']['SubscriberKeyParameterName']) ? 'ContactKey' : $List['Confirmed']['SubscriberKeyParameterName'],
    '#weight' => $weight++,
  );



  // Dropdown for Referral Link
  $options = []; // ToolID -> Referral Name
  foreach (ToolPluginGeneral::GetEntitiesAsOptionsArray($UserID) as $toolId => $name) {
    $toolObject = ToolPluginGeneral::FromID($UserID, $toolId);
    if ($toolObject->GetData('VarcharIndexed') == 'referral') {
      $options[$toolId] = $name;
    }
  }

  if (!empty($options)) {
    $form['SubscriptionConfirmedParameters']['UseReferralLinkParameter'] = [
      '#type' => 'checkbox',
      '#title' => t(
          "list-confirmed-parameters-referral:Add referral link"
        ) . $QuickhelpReferralLinkParameter,
      '#default_value' => !isset($List['Confirmed']['UseReferralLinkParameter']) ? 1 : $List['Confirmed']['UseReferralLinkParameter'],
      '#return_value' => 1,
      '#weight' => $weight++,
      '#attributes' => [
        'data-event-load' => 'js_checkbox_toggle_element_display',
        'data-event-load-args' => '#edit-subscriptionconfirmedparameters-usereferrallinkparameter-wrapper',
        'data-event-change' => 'js_checkbox_toggle_element_display',
        'data-event-change-args' => '#edit-subscriptionconfirmedparameters-usereferrallinkparameter-wrapper',
      ],
    ];

    $form['SubscriptionConfirmedParameters']['ReferralLinkName'] = array(
      '#type' => 'textfield',
      '#default_value' => empty($List['Confirmed']['ReferralLinkName']) ? 'referral' : $List['Confirmed']['ReferralLinkName'],
      '#weight' => $weight++,
    );

    $form['SubscriptionConfirmedParameters']['ReferralLinkToolID'] = [
      '#type' => 'select',
      '#options' => $options,
      '#default_value' => empty($List['Confirmed']['ReferralLinkToolID']) ? 0 : $List['Confirmed']['ReferralLinkToolID'],
      '#weight' => $weight++,
    ];
  }

  $quickhelp = theme('klicktipp_quickhelp', array(
    'element' => array(
      '#quickhelp' => 'subscription-process-use-as-change-email-list',
      '#title' => t("Use as change email list"),
    )
  ));

  $form['UseAsChangeEmailList'] = array(
    '#type' => 'checkbox',
    '#title' => t("Use as change email list") . $quickhelp,
    '#default_value' => empty($List['UseAsChangeEmailList']) ? 0 : 1,
    '#return_value' => 1,
    '#weight' => $weight++,
  );

  if ( klicktipp_feature_access($account, 'allow re-send confirmation email') ) {
    //enterprise customers can send the confirmation email on every subscribe via this DOI process

    $quickhelp = theme('klicktipp_quickhelp', array(
      'element' => array(
        '#quickhelp' => 'subscription-process-allow-resend-confirmation-email',
        '#title' => t("Should Klick-Tipp send the confirmation email on every subscription?"),
      )
    ));

    $form['ReSendConfirmationEmail'] = array(
      '#type' => 'checkbox',
      '#title' => t("Should Klick-Tipp send the confirmation email on every subscription?") . $quickhelp,
      '#default_value' => (empty($List['ReSendConfirmationEmail'])) ? 0 : 1,
      '#return_value' => Lists::OPTIN_MODE_SINGLE,
      '#weight' => $weight++,
    );

  }
  else {
    //no enterprise customer
    //display a disabled checkbox with upsell quickhelp
    //NOTE: if the upsell quickhelp 'subscription-process-allow-resend-confirmation-email-upsell' is not set (no help text),
    //do not show the checkbox

    $UpsellQuickhelp = klicktipp_quickhelp('subscription-process-allow-resend-confirmation-email-upsell');

    if (!empty($UpsellQuickhelp['help_text'])) {

      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'subscription-process-allow-resend-confirmation-email-upsell',
          '#title' => t("Should Klick-Tipp send the confirmation email on every subscription?"),
        )
      ));

      $form['ReSendConfirmationEmail'] = array(
        '#type' => 'checkbox',
        '#title' => t("Should Klick-Tipp send the confirmation email on every subscription?") . $quickhelp,
        '#value' => 0,
        '#disabled' => TRUE,
        '#weight' => $weight++,
      );

    }

  }

  $form['PendingSubscribersDelay'] = array(
    '#type' => 'select',
    '#title' => t('Delete Pending Subscribers'),
    '#options' => array(
      0 => t('Do not delete pending subscribers'),
      1 => t('Delete pending subscribers after !x day', array('!x' => 1)),
      3 => t('Delete pending subscribers after !x days', array('!x' => 3)),
      7 => t('Delete pending subscribers after !x days', array('!x' => 7)),
      14 => t('Delete pending subscribers after !x days', array('!x' => 14)),
    ),
    '#default_value' => (empty($List['PendingSubscribersDelay'])) ? 0 : $List['PendingSubscribersDelay'],
    '#weight' => $weight++,
    '#quickhelp' => 'subscription-process-pending-subscribers-delay',
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t(/*ignore*/CONTROLLER_LIST_SAVE_DOI_PROCESS),
    '#weight' => $weight++,
  );

  if (!$isDefaultList) {

    $ModalID = 'modalDeleteConfirmAjax';
    $Title = t('Delete Subscription process');

    $modalFormParameters = [
      "modalTitle" => $Title,
      "modalId" => $ModalID,
      "dependencyMessage" => t("This DOI process is used in the following objects and cannot be deleted:"),
      "entityId" => $ListID,
      "entityClass" => Lists::class,
      "entityName" => $List['Name'],
      "entity" => $List,
      "submitFunction" => "klicktipp_list_delete_confirm_form_submit",
    ];

    // will include the generic modal form "klicktipp_delete_modal_form"
    Libraries::include("form_delete_modal.inc");

    $form['buttons']['ModalDeleteConfirmTrigger'] = [
      '#theme' => 'klicktipp_delete_modal_ajax_button',
      '#title' => $Title,
      '#value' => $ModalID,
      '#weight' => $weight++,
      '#modal_confirm' => drupal_get_form("klicktipp_delete_modal_form", $account, $modalFormParameters),
    ];

  }

  if (user_access('access simple copy form/signature/opt-in', $account)) {
    $form['buttons']['copy'] = [
      '#type' => 'submit',
      '#theme' => 'klicktipp_duplicate_submit',
      '#value' => t(/*ignore*/CONTROLLER_COPY_DOI_PROCESS),
      '#weight' => $weight++,
    ];
  }

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "lists/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

/**
 * Implementation of form_validate().
 */
function klicktipp_list_edit_form_validate($form, &$form_state) {

  // don't validate at all on copy action
  if ($form_state['values']['op'] === t(/*ignore*/CONTROLLER_COPY_DOI_PROCESS)) {
    return;
  }

  $UserID = $form_state['values']['uid'];
  $List = $form_state['values']['List'];
  $Name = $form_state['values']['Name'];
  $isDefaultList = $form_state['values']['isDefaultList'];

  if (!$isDefaultList && $Name != $List['Name']) {

    $ExistingList = Lists::RetrieveList(array('ListID'), array(
      'RelOwnerUserID' => $UserID,
      'Name' => $Name,
      'Deleted' => 0,
    ));
    if ($ExistingList) {
      form_set_error('Name', t('A subscription process with the name %name already exists', array('%name' => $Name)));
    }

  }

  if (!empty($form_state['values']['UseOwnPendingPage'])) {
    if ((!empty($form_state['values']['PendingParameters']['SubscriptionConfirmationPendingPageURL']) && $form_state['values']['PendingParameters']['SubscriptionConfirmationPendingPageURL'] != 'https://')) {
      if (!valid_url($form_state['values']['PendingParameters']['SubscriptionConfirmationPendingPageURL'], TRUE)) {
        form_set_error('SubscriptionConfirmationPendingPageURL', t("Invalid URL for confirmation pending page"));
      }
      elseif (BlacklistHandler::isUrlBlacklisted($form_state['values']['PendingParameters']['SubscriptionConfirmationPendingPageURL'])[0]) {
        form_set_error('SubscriptionConfirmationPendingPageURL', t("The entered URL is blacklisted."));
      }
    }
  }

  if (!empty($form_state['values']['UseOwnConfirmedPage'])) {
    if ((!empty($form_state['values']['SubscriptionConfirmedParameters']['SubscriptionConfirmedPageURL']) && $form_state['values']['SubscriptionConfirmedParameters']['SubscriptionConfirmedPageURL'] != 'https://')) {
      if (!valid_url($form_state['values']['SubscriptionConfirmedParameters']['SubscriptionConfirmedPageURL'], TRUE)) {
        form_set_error('SubscriptionConfirmedPageURL', t("Invalid URL for subscription confirmed page"));
      }
      elseif (BlacklistHandler::isUrlBlacklisted($form_state['values']['SubscriptionConfirmedParameters']['SubscriptionConfirmedPageURL'])[0]) {
        form_set_error('SubscriptionConfirmedPageURL', t("The entered URL is blacklisted."));
      }
    }
  }

}

/**
 * override submit redirection (there is no hook_submit)
 */
function klicktipp_list_edit_form_submit($form, &$form_state) {

  $values = $form_state['values'];
  $UserID = $values['uid'];
  $List = $values['List'];
  $ListID = $List['ListID'];

  if ($values['op'] === t(/*ignore*/CONTROLLER_COPY_DOI_PROCESS)) {
    klicktipp_set_redirect($form_state, "lists/$UserID/$ListID/copy");
    return;
  }

  $Name = $values['Name'];
  $isDefaultList = $values['isDefaultList'];
  $MetaLabels = (empty($values['MetaLabels'])) ?  array() : array_keys($values['MetaLabels']);

  //update subscription process

  $useAsChangeEmailList = $values['UseAsChangeEmailList'];

  if ($useAsChangeEmailList && !$List['UseAsChangeEmailList']) {

    $UserLists = Lists::RetrieveLists($UserID);
    if (!empty($UserLists)) {
      foreach ($UserLists as $ArrayList) {
        if (!empty($ArrayList['UseAsChangeEmailList'])) {
          $ArrayList['UseAsChangeEmailList'] = 0;
          Lists::UpdateDB($ArrayList);
        }
      }
    }
  }

  $pendingFormParameters = $values['PendingParameters'];
  $subscriptionConfirmedFormParameters = $values['SubscriptionConfirmedParameters'];

  $ArrayFieldsAndValues = array(
    'ListID' => $ListID,
    'Name' => ($isDefaultList) ? '' : $Name,
    'RelOwnerUserID' => $UserID,
    'SubscriptionConfirmationPendingPageURL' => (empty($values['UseOwnPendingPage']) || $pendingFormParameters['SubscriptionConfirmationPendingPageURL'] == 'https://') ? '' : $pendingFormParameters['SubscriptionConfirmationPendingPageURL'],
    'SubscriptionConfirmedPageURL' => (empty($values['UseOwnConfirmedPage']) || $subscriptionConfirmedFormParameters['SubscriptionConfirmedPageURL'] == 'https://') ? '' : $subscriptionConfirmedFormParameters['SubscriptionConfirmedPageURL'],
    'UseAsChangeEmailList' => $useAsChangeEmailList,
    DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
  );

  //if optin mode is single, do not re-send confirmation email
  $ArrayFieldsAndValues['ReSendConfirmationEmail'] = (empty($values['ReSendConfirmationEmail']) || $values['OptInModeEnum'] == Lists::OPTIN_MODE_SINGLE) ? 0 : 1;
  // parameter on custom thankyou url
  $ArrayFieldsAndValues['UseSubscriberParameter'] = (empty($values['UseSubscriberParameter'])) ? 0 : 1;
  $ArrayFieldsAndValues['UseListParameter'] = (empty($values['UseListParameter'])) ? 0 : 1;
  $ArrayFieldsAndValues['UseEmailParameter'] = (empty($values['UseEmailParameter'])) ? 0 : 1;
  $ArrayFieldsAndValues['SubscriberParameterName'] = (empty($ArrayFieldsAndValues['UseSubscriberParameter'])) ? '' : $values['SubscriberParameterName'];
  $ArrayFieldsAndValues['ListParameterName'] = (empty($ArrayFieldsAndValues['UseListParameter'])) ? '' : $values['ListParameterName'];
  $ArrayFieldsAndValues['EmailParameterName'] = (empty($ArrayFieldsAndValues['UseEmailParameter'])) ? '' : $values['EmailParameterName'];
  // delay after which pending subscribers are automatically deleted
  $ArrayFieldsAndValues['PendingSubscribersDelay'] = (empty($values['PendingSubscribersDelay'])) ? 0 : $values['PendingSubscribersDelay'];


  $pending = [];
  if (empty($ArrayFieldsAndValues['SubscriptionConfirmationPendingPageURL'])) {
    // user has deactivated or never set pending page, so do not use any url parameters
    $pending['UseSubscriberParameter'] = 0;
    $pending['UseListParameter'] = 0;
    $pending['UseEmailParameter'] = 0;
    $pending['UseSubscriberKeyParameter'] = 0;
    $pending['SubscriberParameterName'] = '';
    $pending['ListParameterName'] = '';
    $pending['EmailParameterName'] = '';
    $pending['SubscriberKeyParameterName'] = '';
  }
  else {
    // user has own pending page for this list, update parameters
    $pending['UseSubscriberParameter'] = (empty($pendingFormParameters['UseSubscriberParameter'])) ? 0 : 1;
    $pending['UseListParameter'] = (empty($pendingFormParameters['UseListParameter'])) ? 0 : 1;
    $pending['UseEmailParameter'] = (empty($pendingFormParameters['UseEmailParameter'])) ? 0 : 1;
    $pending['UseSubscriberKeyParameter'] = (empty($pendingFormParameters['UseSubscriberKeyParameter'])) ? 0 : 1;
    $pending['SubscriberParameterName'] = (empty($pending['UseSubscriberParameter'])) ? '' : $pendingFormParameters['SubscriberParameterName'];
    $pending['ListParameterName'] = (empty($pending['UseListParameter'])) ? '' : $pendingFormParameters['ListParameterName'];
    $pending['EmailParameterName'] = (empty($pending['UseEmailParameter'])) ? '' : $pendingFormParameters['EmailParameterName'];
    $pending['SubscriberKeyParameterName'] = (empty($pending['UseSubscriberKeyParameter'])) ? '' : $pendingFormParameters['SubscriberKeyParameterName'];
  }
  $ArrayFieldsAndValues['Pending'] = $pending;

  $confirmed = [];
  if (empty($ArrayFieldsAndValues['SubscriptionConfirmedPageURL'])) {
    // user has deactivated or never set confirmed page, so do not use any url parameters
    $confirmed['UseSubscriberParameter'] = 0;
    $confirmed['UseListParameter'] = 0;
    $confirmed['UseEmailParameter'] = 0;
    $confirmed['UseSubscriberKeyParameter'] = 0;
    $confirmed['SubscriberParameterName'] = '';
    $confirmed['ListParameterName'] = '';
    $confirmed['EmailParameterName'] = '';
    $confirmed['ReferralLinkName'] = '';
    $confirmed['ReferralLinkToolID'] = '';
    $confirmed['SubscriberKeyParameterName'] = '';
  }
  else {
    // user has own confirmed page for this list, update parameters
    $confirmed['UseSubscriberParameter'] = (empty($subscriptionConfirmedFormParameters['UseSubscriberParameter'])) ? 0 : 1;
    $confirmed['UseListParameter'] = (empty($subscriptionConfirmedFormParameters['UseListParameter'])) ? 0 : 1;
    $confirmed['UseEmailParameter'] = (empty($subscriptionConfirmedFormParameters['UseEmailParameter'])) ? 0 : 1;
    $confirmed['UseSubscriberKeyParameter'] = (empty($subscriptionConfirmedFormParameters['UseSubscriberKeyParameter'])) ? 0 : 1;
    $confirmed['UseReferralLinkParameter'] = (empty($subscriptionConfirmedFormParameters['UseReferralLinkParameter'])) ? 0 : 1;
    $confirmed['SubscriberParameterName'] = (empty($confirmed['UseSubscriberParameter'])) ? '' : $subscriptionConfirmedFormParameters['SubscriberParameterName'];
    $confirmed['ListParameterName'] = (empty($confirmed['UseListParameter'])) ? '' : $subscriptionConfirmedFormParameters['ListParameterName'];
    $confirmed['EmailParameterName'] = (empty($confirmed['UseEmailParameter'])) ? '' : $subscriptionConfirmedFormParameters['EmailParameterName'];
    $confirmed['ReferralLinkName'] = (empty($confirmed['UseReferralLinkParameter'])) ? '' : $subscriptionConfirmedFormParameters['ReferralLinkName'];
    $confirmed['ReferralLinkToolID'] = (empty($confirmed['UseReferralLinkParameter'])) ? '' : $subscriptionConfirmedFormParameters['ReferralLinkToolID'];
    $confirmed['SubscriberKeyParameterName'] = (empty($confirmed['UseSubscriberKeyParameter'])) ? '' : $subscriptionConfirmedFormParameters['SubscriberKeyParameterName'];
  }
  $ArrayFieldsAndValues['Confirmed'] = $confirmed;

  //if the customer has single optin privileges (@see user account edit) store the optin mode
  if (isset($values['OptInModeEnum'])) {
    $ArrayFieldsAndValues['OptInModeEnum'] = $values['OptInModeEnum'];
  }

  Lists::UpdateDB($ArrayFieldsAndValues);
  if (!$isDefaultList && $Name != $List['Name']) {

    // update email name (if not default subscription process)

    //TODO confirmation email name should be calculated when its needed and not stored in db
    /** @var Emails $ObjectEmail */
    $ObjectEmail = Emails::FromID($ArrayFieldsAndValues['RelOwnerUserID'], $List['RelOptInConfirmationEmailID']);
    if ($ObjectEmail) {
      $ArrayEmail = $ObjectEmail->GetData();
        $ArrayEmail['EmailName'] = t('Confirmation Email: @name', array('@name' => $ArrayFieldsAndValues['Name']));
      $ObjectEmail->UpdateDB($ArrayEmail);
    }
  }

  drupal_set_message(t('Subscription process %name successfully updated.', array('%name' => $Name)));

  //redirect to DOI overview
  klicktipp_set_redirect($form_state, "lists/$UserID");

}

/**
 * Callback for lists overview page
 */
function klicktipp_list_overview_form($form, $form_state, $account, $PagerPage = 1, $PagerSize = 0) {

  if (!user_access('klicktipp admin role')) {
    // dialog is migrated to angular, only admins can access the old dialog for debugging
    drupal_access_denied();
    drupal_exit();
  }

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Subscription processes');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $showStats = !empty($account->UserPrivileges['ShowConfirmationEmailStats']);

  // display data

  $Lists = Lists::RetrieveLists($UserID);
  // calc confirmation stats
  if ($showStats) {
    $sql = "SELECT COUNT(*) FROM {subscription} WHERE RelListID = %d AND RelOwnerUserID = %d AND SubscriptionStatus = %d AND SubscriptionType = ". Subscription::SUBSCRIPTIONTYPE_EMAIL;
    foreach ($Lists as $key => $list) {
      $result = kt_query($sql, $list['ListID'], $UserID, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
      $list['Confirmed'] = $result->fetchField();
      $list['Emails'] = $list['Confirmed'] + kt_query($sql, $list['ListID'], $UserID, Subscribers::SUBSCRIPTIONSTATUS_OPTIN)->fetchField();
      $list['Rate'] = $list['Emails'] > 0 ? $list['Confirmed'] * 100 / $list['Emails'] : 0;
      $Lists[$key] = $list;
    }
  }

  //sortable header
  $header = array(
    array(
      'data' => t('ID'),
      'field' => 'ListID',
      'class' => array('table-col-fit table-col-right'),
    ),
    array(
      'data' => t('Name'),
      'field' => 'Name',
      'sort' => 'asc',
    ),
    array(
      'data' => t('Created on'),
      'field' => 'CreatedOn',
      'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
    ),
  );
  if ($showStats) {
    $header[] = array(
      'data' => t('Confirmation E-Mails'),
      'field' => 'Emails',
      'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
    );
    $header[] = array(
      'data' => t('Confirmed'),
      'field' => 'Confirmed',
      'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
    );
    $header[] = array(
      'data' => t('Rate'),
      'field' => 'Rate',
      'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
    );
  }

  //tablesort
  $ArraySort = klicktipp_get_tablesort($header, array('Name' => 'ASC'));
  if (isset($ArraySort['Name'])) {
    klicktipp_uasort($Lists, $ArraySort, 'strcasecmp');
  }
  else {
    //use strcasecmp as compare function
    klicktipp_uasort($Lists, $ArraySort);
  }

  $rows = array();
  foreach ($Lists as $list) {
    $ListID = $list['ListID'];
    if (empty($list['Name'])) {
      $ListName = t("Default subscription process");
      $highlight_default = ' warning'; //bootstrap to color the row with yellow
    }
    else {
      $ListName = $list['Name'];
      $highlight_default = '';
    }

    $SingleOptinLabel = ($list['OptInModeEnum'] == Lists::OPTIN_MODE_SINGLE) ? '<span class="label label-info splittestlabel">' . t('Single Optin') . '</span>' : '';
    $ReSendLabel = (!empty($list['ReSendConfirmationEmail']) && empty($SingleOptinLabel)) ? '<span class="label label-info splittestlabel">' . t('Resend confirmation email') . '</span>' : '';

    $url = "/lists/$UserID/$ListID/edit";
    $DisplayName = array(
     'data' => '<a href="' . $url . '" class="cut-text display-email has-tooltip"
       title="' . $ListName . '">' . $ListName . '</a><span>'. $SingleOptinLabel . $ReSendLabel .'</span>',
     );

    $row = array(
      array(
        'data' => $ListID,
        'class' => array('table-col-fit table-col-right'),
      ),
      $DisplayName,
      array(
        'data' => Dates::formatDate(Dates::FORMAT_DMY, (int) $list['CreatedOn']),
        'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
      ),
    );
    if ($showStats) {
      $row[] = array(
        'data' => $list['Emails'],
        'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
      );
      $row[] = array(
        'data' => $list['Confirmed'],
        'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
      );
      $row[] = array(
        'data' => $list['Rate'] > 0 ? klicktipp_number_format($list['Rate'], 2) : '',
        'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
      );
    }

    $rows[] = array(
      'data' => $row,
      'class' => array($highlight_default),
    );
  }

  $ListCount = count($rows);

  //Pager
  $PagerLink = "lists/$UserID";
  [$PagerPage, $PagerSize, $Offset] = klicktipp_validate_pager($PagerPage, $PagerSize, $ListCount, $PagerLink);

  $Lists = array_slice($rows, $Offset, $PagerSize, TRUE);

  $weight = 1;

  $form = array();

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $createButtons = [
    [
      '#title' => t('Action::Add::Subscription process'),
      '#value' => "lists/$UserID/add",
    ],
  ];

  $form['create_buttons'] = [
    '#theme' => 'klicktipp_create_buttons',
    '#buttons' => $createButtons,
    '#weight' => $weight++,
    '#prefix' => '<div class="create-buttons"><div class="clearfix"><div class="right">',
    '#suffix' => '</div></div></div>',
  ];

  $form['overview'] = array(
    '#type' => 'markup',
    '#value' => theme('table', array(
      'header' => $header,
      'rows' => $Lists,
      'dataRow' => TRUE,
      'attributes' => ['data-e2e-id' => 'table-lists-overview']
    )),
    '#weight' => $weight++,
    '#suffix' => theme('klicktipp_table_pager', array(
      'element' => array(
        'pager_page' => $PagerPage,
        //current page
        'pager_size' => $PagerSize,
        //current page size
        'pager_hide_sizes' => FALSE,
        //hide page size badges
        'pager_total' => $ListCount,
        //total entries in the table
        'pager_max_items' => KLICKTIPP_PAGER_MAX_PAGINATION_ITEM,
        //max pager items
        'pager_link' => $PagerLink,
        //link of pager badges and items, "/<CURRENT-PAGE>/<PAGE-SIZE>" will be added
        'pager_link_query' => (empty($_GET['order'])) ? array() : array(
          'order' => $_GET['order'],
          'sort' => $_GET['sort'],
        ),
        //build an additional link query string, possibly for table sort @see l()/url()
      )
    )),
  );

  return $form;
}

/**
 * list delete form submit
 */
function klicktipp_list_delete_confirm_form_submit($form, &$form_state) {

  $List = $form_state['values']['Entity'];

  $UserID = $List['RelOwnerUserID'];

  Lists::DeleteDB($List);

  drupal_set_message(t("Subscription process %name successfully deleted.", array('%name' => $List['Name'])));

  klicktipp_set_redirect($form_state, "lists/$UserID");

}

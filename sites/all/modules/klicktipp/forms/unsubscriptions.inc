<?php

use App\Klicktipp\BlacklistHandler;
use App\Klicktipp\Errors;
use App\Klicktipp\Tag;
use App\Klicktipp\UserGroups;

define('UNSUBSCRIPTION_DIALOG_MAX_MESSAGE_LENGTH', 150);

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function theme_unsubscriptions_dialogs($variables) {
  $form = $variables['form'];

  $header = array(
    array(
      'data' => t('Priority'),
      'class' => array('table-col-fit'),
    ),
    array(
      'data' => t('ID'),
      'class' => array('table-col-fit table-col-right'),
    ),
    array('data' => t('Name')),
    array('data' => t('Weight')),
    array('data' => t('Tags')),
  );

  drupal_add_tabledrag('unsubscriptions_dialogs', 'order', 'sibling', 'dialog-weight');

  foreach (element_children($form) as $DialogID) {
    // Add class to group weight fields for drag and drop.

    $row = array(array(
        'data' => '',
        'class' => array('table-col-fit'),
      ));
    $row[] = array(
      'data' => drupal_render($form[$DialogID]['DialogID']),
      'class' => array('table-col-fit table-col-right'),
    );
    $row[] = array('data' => drupal_render($form[$DialogID]['DialogName']));
    $row[] = array('data' => drupal_render($form[$DialogID]['DialogWeight']));
    $row[] = array('data' => drupal_render($form[$DialogID]['DialogTags']));
    $rows[] = array(
      'data' => $row,
      'class' => array('draggable'),
    );
  }

  $form['Table'] = array(
    '#type' => 'klicktipp_block',
    '#value' => theme('table', array(
      'header' => $header,
      'rows' => $rows,
      'attributes' => array(
        'id' => 'unsubscriptions_dialogs',
        'sticky-disabled' => TRUE,
        'data-e2e-id' => 'table-unsubscription-messages',
      ),
      'dragdrop' => TRUE
    )),
    '#weight' => 10,
    '#attributes' => array('class' => array('form-control klicktipp-tabledrag')),
  );

  return drupal_render_children($form);

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function theme_unsubscriptions_urls($variables) {
  $form = $variables['form'];

  $header = array(
    array(
      'data' => t('Priority'),
      'class' => array('table-col-fit'),
    ),
    array(
      'data' => t('ID'),
      'class' => array('table-col-fit table-col-right'),
    ),
    array('data' => t('Name')),
    array('data' => t('Weight')),
    array('data' => t('URL')),
    array('data' => t('Tags')),
  );

  drupal_add_tabledrag('unsubscriptions_urls', 'order', 'sibling', 'url-weight');

  foreach (element_children($form) as $URL_ID) {
    // Add class to group weight fields for drag and drop.

    $row = array(array(
        'data' => '',
        'class' => array('table-col-fit'),
      ));
    $row[] = array(
      'data' => drupal_render($form[$URL_ID]['URL_ID']),
      'class' => array('table-col-fit table-col-right'),
    );
    $row[] = drupal_render($form[$URL_ID]['URL_Name']);
    $row[] = drupal_render($form[$URL_ID]['URL_Weight']);
    $row[] = drupal_render($form[$URL_ID]['URL_href']);
    $row[] = drupal_render($form[$URL_ID]['URL_Tags']);
    $rows[] = array(
      'data' => $row,
      'class' => array('draggable'),
    );
  }

  $form['Table'] = array(
    '#type' => 'klicktipp_block',
    '#value' => theme('table', array(
      'header' => $header,
      'rows' => $rows,
      'attributes' => array(
        'id' => 'unsubscriptions_urls',
        'sticky-disabled' => TRUE,
        'data-e2e-id' => 'table-unsubscription-urls',
      ),
      'dragdrop' => TRUE
    )),
    '#weight' => 10,
    '#attributes' => array('class' => array('form-control klicktipp-tabledrag')),
  );

  return drupal_render_children($form);

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_dialog_browse_form($form, $form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Unsubscription Messages');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $UserGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID, TRUE);
  $MaxMessages = empty($UserGroup['Data']['LimitUnsubscriptionMessages']) ? 0 : $UserGroup['Data']['LimitUnsubscriptionMessages'];

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);
  $Dialogs = $Unsubscriptions['Dialogs'];

  $Tags = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));

  $weight = 0;

  $form = array();

  $form['#pre_render'][] = 'klicktipp_unsubscriptions_dialog_browse_form_pre_render';

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  $createButtons = [
    [
      '#title' => t('Action::Add::Unsubscription Message'),
      '#value' => "unsubscriptions/$UserID/messages/add",
    ],
  ];

  $form['create_buttons'] = [
    '#theme' => 'klicktipp_create_buttons',
    '#buttons' => $createButtons,
    '#weight' => $weight++,
    '#prefix' => '<div class="create-buttons"><div class="clearfix"><div class="right">',
    '#suffix' => '</div></div></div>',
  ];

  if (empty($Dialogs)) {

    $form['empty'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("There are no unsubscription messages yet."),
      '#weight' => $weight++,
    );

  }
  else {

    if ($MaxMessages != UserGroups::LIMIT_UNLIMITED && count($Unsubscriptions['Dialogs']) >= $MaxMessages) {
      unset($form['create_buttons']);
      //user reached the unsubscription messages limit
      if (!empty($UserGroup['Data']['LimitUnsubscriptionMessagesUpgrade'])) {
        drupal_set_message($UserGroup['Data']['LimitUnsubscriptionMessagesUpgrade'], 'warning');
      }
      else {
        drupal_set_message(t("You have reached the maximum number of %count messages(s) for your current %member membership. If you need more messages, please upgrade your membership.", array(
          '%member' => $UserGroup['GroupName'],
          '%count' => $MaxMessages,
        )), 'warning');
      }
    }

    if ($MaxMessages == UserGroups::LIMIT_UNLIMITED) {
      $intro = t("In the following table you find your unsubscription messages.");
    }
    else {
      $intro = t("In the following table you find your unsubscription messages. For your current %member membership you can create up to %count different messages.", array(
        '%count' => $MaxMessages,
        '%member' => $UserGroup['GroupName'],
      ));
    }

    $form['intro'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => $intro,
      '#weight' => $weight++,
    );

    $quickhelp = theme('klicktipp_quickhelp',
      [
        'element' => [
          '#quickhelp' => 'unsubscription-messages-overview-priority',
          '#title' => t('Priority'),
        ]
      ]
    );

    $form['helpintro'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("You can change the priorities of your unsubscription messages by dragging the cross.") . $quickhelp,
      '#weight' => $weight++,
    );

    $form['UnsubscriptionsDialogs'] = array(
      '#theme' => 'unsubscriptions_dialogs',
      '#tree' => TRUE,
      '#weight' => $weight++,
    );

    foreach ($Dialogs as $DialogID => $dialog) {

      $DialogTags = array();
      foreach ($dialog['DialogTags'] as $tag) {
        $DialogTags[] = $Tags[$tag];
      } //get the TagNames

      $form['UnsubscriptionsDialogs'][$DialogID]['DialogID'] = array(
        '#type' => 'markup',
        '#value' => $DialogID,
      );

      $form['UnsubscriptionsDialogs'][$DialogID]['DialogName'] = array(
        '#type' => 'markup',
        '#value' => l($dialog['DialogName'], "unsubscriptions/$UserID/messages/$DialogID/edit"),
      );

      $form['UnsubscriptionsDialogs'][$DialogID]['DialogWeight'] = array(
        '#type' => 'weight',
        '#delta' => 5,
        '#default_value' => $dialog['#weight'],
        '#attributes' => array('class' => array('dialog-weight')),
      );

      $form['UnsubscriptionsDialogs'][$DialogID]['DialogTags'] = array(
        '#type' => 'markup',
        '#value' => implode(', ', $DialogTags),
      );

    }
  }

  // buttons
  //hide the buttons via javascript on page load (by class 'table-drag-buttons')
  //the buttons will be shown after the user dragged som rows @see Drupal.theme.prototype.tableDragChangedWarning in script.js
  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => 10000,
    '#prefix' => '<div class="button-row table-drag-buttons">',
    '#suffix' => '</div>',
  );

  if (!empty($Dialogs)) {

    $form['buttons']['SubmitSaveWeights'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Save Unsubscription message order'),
      '#weight' => $weight++,
    );

    $form['buttons']['cancel'] = array(
      '#theme' => 'klicktipp_cancel_button',
      '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
      '#value' => "unsubscriptions/$UserID",
      '#weight' => $weight++,
    );

  }

  return $form;

}

//TODO Drupal7: remove form_pre_render in D7, title and breadcrumb are set in form function
/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_dialog_browse_form_pre_render($form) {

  $page_title = t('Unsubscription Messages');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  return $form;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_dialog_browse_form_submit($form, &$form_state) {

  $account = $form_state['values']['account'];
  $UserID = $account->uid;

  if (klicktipp_unsubscriptions_update_dialog_weights($account, $form_state['values'])) {
    drupal_set_message(t('Unsubscription message order successfully updated.'));
  }
  else {
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_dialog_form($form, $form_state, $account, $DialogID = 0) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = ($DialogID) ? t('Edit Unsubscription Message') : t('Create Unsubscription Message');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Unsubscription Messages'), "unsubscriptions/$UserID/messages")), $page_title);

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);

  $Dialog = (empty($DialogID)) ? array() : $Unsubscriptions['Dialogs'][$DialogID];

  $used_tags = array();
  foreach ($Unsubscriptions['Dialogs'] as $did => $dialog) {
    if ($did == $DialogID) {
      continue;
    }
    foreach ($dialog['DialogTags'] as $tag) {
      $used_tags[] = $tag;
    }
  }

  $available_tags = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL), 0, $used_tags);

  $weight = 1;

  $form['#pre_render'][] = 'klicktipp_unsubscriptions_dialog_form_pre_render';

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['UsedTags'] = array(
    '#type' => 'value',
    '#value' => $used_tags,
  );

  $form['DialogID'] = array(
    '#type' => 'value',
    '#value' => $DialogID,
  );

  $form['DialogWeight'] = array(
    '#type' => 'value',
    '#value' => empty($Dialog['#weight']) ? 0 : $Dialog['#weight'],
  );

  $form['CurrentDialogName'] = array(
    '#type' => 'value',
    '#value' => $Dialog['DialogName'],
  );

  $form['DialogName'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#size' => 60,
    '#title' => t('Name'),
    '#default_value' => $Dialog['DialogName'],
  );

  $form['DialogTags'] = array(
    '#type' => 'textfield',
    '#default_value' => $Dialog['DialogTags'],
    '#title' => t("Tags"),
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $available_tags,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['DialogTags'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#required' => TRUE,
  );

  $form['DialogText'] = array(
    '#type' => 'textfield',
    '#default_value' => $Dialog['DialogText'],
    '#title' => t('Message'),
    '#weight' => $weight++,
    '#maxlength' => UNSUBSCRIPTION_DIALOG_MAX_MESSAGE_LENGTH,
    '#required' => TRUE,
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  if ($DialogID) {

    $form['buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Save Unsubscription message'),
      '#weight' => $weight++,
    );

    $form['buttons']['ModalDeleteConfirmTrigger'] = array(
      '#theme' => 'klicktipp_delete_modal_button',
      '#title' => t('Delete Unsubscription message'),
      '#value' => 'klicktipp_unsubscriptions_dialog_delete_confirm_form',
      '#weight' => $weight++,
      '#external' => FALSE,
      '#modal_confirm' => drupal_get_form('klicktipp_unsubscriptions_dialog_delete_confirm_form', $account, $DialogID),
    );

  }
  else {

    $form['buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Create Unsubscription message'),
      '#weight' => $weight++,
    );

  }

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "unsubscriptions/$UserID/messages",
    '#weight' => $weight++,
  );

  return $form;

}

//TODO Drupal7: remove form_pre_render in D7, title and breadcrumb are set in form function
/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_dialog_form_pre_render($form) {

  $UserID = $form['uid']['#value'];
  $DialogID = $form['DialogID']['#value'];

  $page_title = ($DialogID) ? t('Edit Unsubscription Message') : t('Create Unsubscription Message');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Unsubscription Messages'), "unsubscriptions/$UserID/messages")), $page_title);

  return $form;
}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_dialog_form_validate($form, &$form_state) {

  $UserID = $form['uid']['#value'];
  $UsedTags = $form['UsedTags']['#value'];
  $ManualTags = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));
  $DialogTags = $form_state['values']['DialogTags'];
  $DialogID = $form_state['values']['DialogID'];
  $DialogName = $form_state['values']['DialogName'];
  $CurrentDialogName = $form_state['values']['CurrentDialogName'];

  // --- check for existing names ---

  if ($DialogName != $CurrentDialogName) {
    //get the unsubscription dialogs
    //Note: load the account in case the user has created/updated a dialog in another tab
    // TODO Convert "user_load" to "user_load_multiple" if "$UserID" is other than a uid.
    // To return a single user object, wrap "user_load_multiple" with "array_shift" or equivalent.
    // Example: array_shift(user_load_multiple(array(), $UserID))
    $account = user_load($UserID);
    $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);


    foreach ($Unsubscriptions['Dialogs'] as $Dialog) {

      if ($Dialog['DialogID'] != $DialogID && $Dialog['DialogName'] == $DialogName) {

        form_set_error('DialogName', t('An Unsubscription dialog with the name %name already exists.', array(
          '%name' => $DialogName,
        )));

        break;

      }
    }

  }

  // --- check for used tags ---

  //Note: form_set_error only displays on message per form field -> collect used tags first
  $UsedAutoCreateTags = array();
  foreach ($UsedTags as $TagID) {
    if (in_array($ManualTags[$TagID], $DialogTags)) {
      $UsedAutoCreateTags[] = $ManualTags[$TagID];
    }
  }

  if (count($UsedAutoCreateTags) == 1) {
    form_set_error('DialogTags', t('The tag "%name" is already been used in another Unsubscription Message.', array(
      '%name' => $UsedAutoCreateTags[0],
    )));
  }
  else {
    if (count($UsedAutoCreateTags) > 1) {
      form_set_error('DialogTags', t('The tags "%names" are already been used in other Unsubscription Messages.', array(
        '%names' => implode('", "', $UsedAutoCreateTags),
      )));
    }
  }

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_dialog_form_submit($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $account = user_load($UserID);
  $DialogID = $form_state['values']['DialogID'];

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);
  $Dialogs = $Unsubscriptions['Dialogs'] ?? [];

  if (empty($DialogID)) {
    //create message
    $ids = array_keys($Dialogs);
    $DialogID = (empty($ids)) ? 1 : max($ids) + 1;

    $success_msg = t("Unsubscription message %name successfully created.", array('%name' => $form_state['values']['DialogName']));

  }
  else {
    // update message
    $success_msg = t('Unsubscription message %name successfully updated.', array('%name' => $form_state['values']['DialogName']));

  }

  $DialogTags = array();
  if (!empty($form_state['values']['DialogTags'])) {
    //in case user entered free values, create tags
    $DialogTags = Tag::CreateManualTagByTagName($UserID, $form_state['values']['DialogTags']);
    if (!$DialogTags) {
      //error creating at least 1 tags, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!UserID' => $UserID,
        '!function' => __FUNCTION__,
        '!DialogID' => $DialogID,
        '!DialogTags' => implode("', '", $form_state['values']['DialogTags']),
      );
      Errors::unexpected("Error: CreateManualTagByTagName with '!DialogTags' in !function for User !UserID with DialogID !DialogID", $error, TRUE);
      return;
    }

  }

  $Dialogs[$DialogID] = array(
    'DialogID' => $DialogID,
    'DialogName' => $form_state['values']['DialogName'],
    'DialogText' => $form_state['values']['DialogText'],
    'DialogTags' => $DialogTags,
    '#weight' => $form_state['values']['DialogWeight'],
  );

  uasort($Dialogs, 'element_sort');

  $Unsubscriptions['Dialogs'] = $Dialogs;

  if (user_save($account, array('Unsubscriptions' => $Unsubscriptions))) {
    drupal_set_message($success_msg);
    klicktipp_set_redirect($form_state, "unsubscriptions/$UserID/messages");
  }
  else {
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_dialog_delete_confirm_form($form, $form_state, $account, $DialogID) {

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);

  //Dialog exists, checked by menu_access-function
  $Dialog = $Unsubscriptions['Dialogs'][$DialogID];

  $Entity = array(
    'account' => $account,
    'DialogID' => $DialogID,
    'Unsubscriptions' => $Unsubscriptions,
  );

  $EntityName = $Dialog['DialogName'];
  $Title = t('Delete Unsubscription message');

  $form = _klicktipp_confirm_form_modal('klicktipp_unsubscriptions_dialog_delete_confirm_form', $EntityName, $Title);

  $form['Entity'] = array(
    '#type' => 'value',
    '#value' => $Entity,
  );

  return $form;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_dialog_delete_confirm_form_submit($form, &$form_state) {

  $account = $form_state['values']['Entity']['account'];

  $UserID = $account->uid;

  $Unsubscriptions = $form_state['values']['Entity']['Unsubscriptions'];

  $DialogID = $form_state['values']['Entity']['DialogID'];

  $DialogName = $Unsubscriptions['Dialogs'][$DialogID]['DialogName'];

  unset($Unsubscriptions['Dialogs'][$DialogID]);

  if (user_save($account, array("Unsubscriptions" => $Unsubscriptions))) {

    drupal_set_message(t("Unsubscription message %name successfully deleted.", array('%name' => $DialogName)));

    klicktipp_set_redirect($form_state, "unsubscriptions/$UserID/messages");

  }
  else {
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_url_browse_form($form, $form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Unsubscription URLs');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);
  $URLs = $Unsubscriptions['URLs'];

  $Tags = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));

  $weight = 0;

  $form = array();

  $form['#pre_render'][] = 'klicktipp_unsubscriptions_url_browse_form_pre_render';

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  $createButtons = [
    [
      '#title' => t('Action::Add::Unsubscription URL'),
      '#value' => "unsubscriptions/$UserID/url/add",
    ],
  ];

  $form['create_buttons'] = [
    '#theme' => 'klicktipp_create_buttons',
    '#buttons' => $createButtons,
    '#weight' => $weight++,
    '#prefix' => '<div class="create-buttons"><div class="clearfix"><div class="right">',
    '#suffix' => '</div></div></div>',
  ];

  $form['intro'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_info',
    '#value' => t("In the following table you find your unsubscription URLs."),
    '#weight' => $weight++,
  );

  if (empty($URLs)) {

    $form['empty'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("There are no unsubscription URLs yet."),
      '#weight' => $weight++,
    );

  }
  else {

    $quickhelp = theme('klicktipp_quickhelp', array('element' => array(
  '#quickhelp' => 'unsubscription-urls-overview-priority',
  '#title' => t('Priority'),
)));
    $form['helpintro'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("You can change the priorities of your unsubscription URLs by dragging the cross.") . $quickhelp,
      '#weight' => $weight++,
    );

    $form['UnsubscriptionsURLs'] = array(
      '#theme' => 'unsubscriptions_urls',
      '#tree' => TRUE,
      '#weight' => $weight++,
    );

    foreach ($URLs as $URL_ID => $url) {

      $URL_Tags = array();
      foreach ($url['URL_Tags'] as $tag) {
        $URL_Tags[] = $Tags[$tag];
      } //get the TagNames

      $form['UnsubscriptionsURLs'][$URL_ID]['URL_ID'] = array(
        '#type' => 'markup',
        '#value' => $URL_ID,
      );

      $form['UnsubscriptionsURLs'][$URL_ID]['URL_Name'] = array(
        '#type' => 'markup',
        '#value' => l($url['URL_Name'], "unsubscriptions/$UserID/url/$URL_ID/edit"),
      );

      $form['UnsubscriptionsURLs'][$URL_ID]['URL_Weight'] = array(
        '#type' => 'weight',
        '#delta' => 5,
        '#default_value' => $url['#weight'],
        '#attributes' => array('class' => array('url-weight')),
      );

      $form['UnsubscriptionsURLs'][$URL_ID]['URL_href'] = array(
        '#type' => 'markup',
        '#value' => l($url['URL_href'], $url['URL_href'], array('attributes' => array('target' => '_blank'))),
      );

      $form['UnsubscriptionsURLs'][$URL_ID]['URL_Tags'] = array(
        '#type' => 'markup',
        '#value' => implode(', ', $URL_Tags),
      );

    }

  }

  // buttons
  //hide the buttons via javascript on page load (by class 'table-drag-buttons')
  //the buttons will be shown after the user dragged som rows @see Drupal.theme.prototype.tableDragChangedWarning in script.js
  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => 10000,
    '#prefix' => '<div class="button-row table-drag-buttons">',
    '#suffix' => '</div>',
  );

  if (!empty($URLs)) {

    $form['buttons']['SubmitSaveWeights'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Save Unsubscription URL order'),
      '#weight' => $weight++,
    );

    $form['buttons']['cancel'] = array(
      '#theme' => 'klicktipp_cancel_button',
      '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
      '#value' => "unsubscriptions/$UserID/url",
      '#weight' => $weight++,
    );

  }

  return $form;

}

//TODO Drupal7: remove form_pre_render in D7, title and breadcrumb are set in form function
/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_url_browse_form_pre_render($form) {

  $page_title = t('Unsubscription URLs');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  return $form;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_url_browse_form_submit($form, &$form_state) {

  $account = $form_state['values']['account'];

  if (klicktipp_unsubscriptions_update_url_weights($account, $form_state['values'])) {
    drupal_set_message(t('Unsubscription URL order successfully updated.'));
  }
  else {
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_url_form($form, $form_state, $account, $URL_ID = 0) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = ($URL_ID) ? t('Edit Unsubscription URL') : t('Create Unsubscription URL');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Unsubscription URLs'), "unsubscriptions/$UserID/url")), $page_title);

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);

  $URL = (empty($URL_ID)) ? array() : $Unsubscriptions['URLs'][$URL_ID];

  $used_tags = array();
  foreach ($Unsubscriptions['URLs'] as $urlid => $dialog) {
    if ($urlid == $URL_ID) {
      continue;
    }
    foreach ($dialog['URL_Tags'] as $tag) {
      $used_tags[] = $tag;
    }
  }

  $available_tags = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL), 0, $used_tags);

  $weight = 1;

  $form['#pre_render'][] = 'klicktipp_unsubscriptions_url_form_pre_render';

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['UsedTags'] = array(
    '#type' => 'value',
    '#value' => $used_tags,
  );

  $form['URL_ID'] = array(
    '#type' => 'value',
    '#value' => $URL_ID,
  );

  $form['URL_Weight'] = array(
    '#type' => 'value',
    '#value' => empty($URL['#weight']) ? 0 : $URL['#weight'],
  );

  $form['CurrentURL_Name'] = array(
    '#type' => 'value',
    '#value' => $URL['URL_Name'],
  );

  $form['URL_Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#size' => 60,
    '#title' => t('Name'),
    '#default_value' => $URL['URL_Name'],
  );

  $form['URL_Tags'] = array(
    '#type' => 'textfield',
    '#default_value' => $URL['URL_Tags'],
    '#title' => t("Tags"),
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $available_tags,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['URL_Tags'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#required' => TRUE,
  );

  $form['URL_href'] = array(
    '#type' => 'textfield',
    '#default_value' => empty($URL['URL_href']) ? 'https://' : $URL['URL_href'],
    '#title' => t('URL'),
    '#weight' => $weight++,
    '#required' => TRUE,
    '#element_validate' => array('klicktipp_element_textfield_validate_url'), //allow & for urls
    '#maxlength' => 800,
    '#quickhelp' => 'unsubscriptions-field-urlhref',
  );

  // buttons
  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  if ($URL_ID) {

    $form['buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Save Unsubscription URL'),
      '#weight' => $weight++,
    );

    $form['buttons']['ModalDeleteConfirmTrigger'] = array(
      '#theme' => 'klicktipp_delete_modal_button',
      '#title' => t('Delete Unsubscription URL'),
      '#value' => 'klicktipp_unsubscriptions_url_delete_confirm_form',
      '#weight' => $weight++,
      '#external' => FALSE,
      '#modal_confirm' => drupal_get_form('klicktipp_unsubscriptions_url_delete_confirm_form', $account, $URL_ID),
    );

  }
  else {

    $form['buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Create Unsubscription URL'),
      '#weight' => $weight++,
    );

  }

  $form['buttons']['cancel'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "unsubscriptions/$UserID/url",
    '#weight' => $weight++,
  );

  return $form;

}

//TODO Drupal7: remove form_pre_render in D7, title and breadcrumb are set in form function
/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_url_form_pre_render($form) {

  $UserID = $form['uid']['#value'];
  $URL_ID = $form['URL_ID']['#value'];

  $page_title = ($URL_ID) ? t('Edit Unsubscription URL') : t('Create Unsubscription URL');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Unsubscription URLs'), "unsubscriptions/$UserID/url")), $page_title);

  return $form;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_url_form_validate($form, &$form_state) {

  $UserID = $form['uid']['#value'];
  $UsedTags = $form['UsedTags']['#value'];
  $ManualTags = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));
  $UrlTags = $form_state['values']['URL_Tags'];
  $URL_ID = $form_state['values']['URL_ID'];
  $URL_Name = $form_state['values']['URL_Name'];
  $CurrentURL_Name = $form_state['values']['CurrentURL_Name'];

  // --- check for existing names ---

  if ($URL_Name != $CurrentURL_Name) {
    //get the unsubscription URLs
    //Note: load the account in case the user has created/updated an URL in another tab
    // TODO Convert "user_load" to "user_load_multiple" if "$UserID" is other than a uid.
    // To return a single user object, wrap "user_load_multiple" with "array_shift" or equivalent.
    // Example: array_shift(user_load_multiple(array(), $UserID))
    $account = user_load($UserID);
    $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);


    foreach ($Unsubscriptions['URLs'] as $URLs) {

      if ($URLs['URL_ID'] != $URL_ID && $URLs['URL_Name'] == $URL_Name) {

        form_set_error('URL_Name', t('An Unsubscription URL with the name %name already exists.', array(
          '%name' => $URL_Name,
        )));

        break;

      }
    }

  }

  // --- check for used tags ---

  //Note: form_set_error only displays on message per form field -> collect used tags first
  $UsedAutoCreateTags = array();
  foreach ($UsedTags as $TagID) {
    if (in_array($ManualTags[$TagID], $UrlTags)) {
      $UsedAutoCreateTags[] = $ManualTags[$TagID];
    }
  }

  if (count($UsedAutoCreateTags) == 1) {
    form_set_error('DialogTags', t('The tag "%name" is already been used in another Unsubscription URL.', array(
      '%name' => $UsedAutoCreateTags[0],
    )));
  }
  else {
    if (count($UsedAutoCreateTags) > 1) {
      form_set_error('DialogTags', t('The tags "%names" are already been used in other Unsubscription URLs.', array(
        '%names' => implode('", "', $UsedAutoCreateTags),
      )));
    }
  }

  if (!valid_url($form_state['values']['URL_href'], TRUE)) {
    form_set_error('URL_href', t("The entered URL is not valid."));
  }
  elseif (BlacklistHandler::isUrlBlacklisted($form_state['values']['URL_href'])[0]) {
    form_set_error('URL_href', t('The entered URL is blacklisted.'));
  }

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_url_form_submit($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $account = user_load($UserID);
  $URL_ID = $form_state['values']['URL_ID'];

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);
  $URLs = $Unsubscriptions['URLs'] ?? [];

  if (empty($URL_ID)) {
    //create URL

    $ids = array_keys($URLs);
    $URL_ID = (empty($ids)) ? 1 : max($ids) + 1;

    $success_msg = t("Unsubscription URL %name successfully created.", array('%name' => $form_state['values']['URL_Name']));

  }
  else {
    // update URL
    $success_msg = t('Unsubscription URL %name successfully updated.', array('%name' => $form_state['values']['URL_Name']));

  }

  $URL_Tags = array();
  if (!empty($form_state['values']['URL_Tags'])) {
    //in case user entered free values, create tags
    $URL_Tags = Tag::CreateManualTagByTagName($UserID, $form_state['values']['URL_Tags']);
    if (!$URL_Tags) {
      //error creating at least 1 tags, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!UserID' => $UserID,
        '!function' => __FUNCTION__,
        '!URLID' => $URL_ID,
        '!URLTags' => implode("', '", $form_state['values']['URL_Tags']),
      );
      Errors::unexpected("Error: CreateManualTagByTagName with '!URLTags' in !function for User !UserID with URLID !URLID", $error, TRUE);
      return;
    }

  }

  $URLs[$URL_ID] = array(
    'URL_ID' => $URL_ID,
    'URL_Name' => $form_state['values']['URL_Name'],
    'URL_href' => $form_state['values']['URL_href'],
    'URL_Tags' => $URL_Tags,
    '#weight' => $form_state['values']['URL_Weight'],
  );

  uasort($URLs, 'element_sort');

  $Unsubscriptions['URLs'] = $URLs;

  if (user_save($account, array('Unsubscriptions' => $Unsubscriptions))) {
    drupal_set_message($success_msg);
    klicktipp_set_redirect($form_state, "unsubscriptions/$UserID/url");
  }
  else {
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }


}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_url_delete_confirm_form($form, $form_state, $account, $URL_ID) {

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);

  //Dialog exists, checked by menu_access-function
  $URL = $Unsubscriptions['URLs'][$URL_ID];

  $Entity = array(
    'account' => $account,
    'URL_ID' => $URL_ID,
    'Unsubscriptions' => $Unsubscriptions,
  );

  $EntityName = $URL['URL_Name'];
  $Title = t('Delete Unsubscription URL');

  $form = _klicktipp_confirm_form_modal('klicktipp_unsubscriptions_url_delete_confirm_form', $EntityName, $Title);

  $form['Entity'] = array(
    '#type' => 'value',
    '#value' => $Entity,
  );

  return $form;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_url_delete_confirm_form_submit($form, &$form_state) {

  $account = $form_state['values']['Entity']['account'];

  $UserID = $account->uid;

  $Unsubscriptions = $form_state['values']['Entity']['Unsubscriptions'];

  $URL_ID = $form_state['values']['Entity']['URL_ID'];

  $URL_Name = $Unsubscriptions['URLs'][$URL_ID]['URL_Name'];

  unset($Unsubscriptions['URLs'][$URL_ID]);

  if (user_save($account, array("Unsubscriptions" => $Unsubscriptions))) {

    drupal_set_message(t("Unsubscription URL %name successfully deleted.", array('%name' => $URL_Name)));

    klicktipp_set_redirect($form_state, "unsubscriptions/$UserID/url");

  }
  else {
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_update_dialog_weights($account, $form_values) {

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);
  $Dialogs = $Unsubscriptions['Dialogs'];

  if (!empty($form_values['UnsubscriptionsDialogs'])) {
    foreach ($form_values['UnsubscriptionsDialogs'] as $DialogID => $dialog) {
      $Dialogs[$DialogID]['#weight'] = $dialog['DialogWeight'];
    }
  }

  uasort($Dialogs, 'element_sort');

  $Unsubscriptions['Dialogs'] = $Dialogs;

  return user_save($account, array('Unsubscriptions' => $Unsubscriptions));

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_update_url_weights($account, $form_values) {

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);
  $URLs = $Unsubscriptions['URLs'];

  if (!empty($form_values['UnsubscriptionsURLs'])) {
    foreach ($form_values['UnsubscriptionsURLs'] as $URL_ID => $url) {
      $URLs[$URL_ID]['#weight'] = $url['URL_Weight'];
    }
  }

  uasort($URLs, 'element_sort');

  $Unsubscriptions['URLs'] = $URLs;

  return user_save($account, array('Unsubscriptions' => $Unsubscriptions));

}


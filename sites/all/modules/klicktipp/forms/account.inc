<?php

use App\Klicktipp\ChatGPTYoutubeSummery;
use App\Klicktipp\Core;
use App\Klicktipp\CSAClient;
use App\Klicktipp\Dates;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Errors;
use App\Klicktipp\FullContactQueueWorker;
use App\Klicktipp\Libraries;
use App\Klicktipp\Subaccount;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\UserGroups;
use App\Klicktipp\UserSpamHelper;
use App\Klicktipp\VarAgedSubscribers;
use App\Klicktipp\VarAppSettings;
use App\Klicktipp\VarCleverbridge;
use App\Klicktipp\VarEmailPreviewSubscriber;
use App\Klicktipp\VarFullContact;
use App\Klicktipp\VarSpamActivity;

/**
 * order page callback
 */
function klicktipp_account_order_page() {

  //the content of the order page is determined by blocks in the regions content_top and content_bottom
  //@see page-order.tpl.php
  return '';

}

/**
 * Render the DPO PFD from template file with user data
 * Template file: @see klicktipp-data-processing-order.tpl.php and klicktipp_data_processing_order()
 * the PDF will be created via the dompdf library
 * @param stdClass $account
 */
function klicktipp_account_data_processing_order_download($account) {

  //get the content for the PDF with user data inserted
  $html = theme('klicktipp_data_processing_order', array('account' => $account));

  $filename = $account->mail . ".pdf";

  //create, render and stream the PDF
  _klicktipp_stream_pdf($html, $filename);
  exit;

}

/**
 * Prepare vCard (iCal, Outlook, etc.) for download
 */
function klicktipp_account_get_user_vcard($param_encoded) {

  $param_decoded = json_decode(base64_decode($param_encoded), TRUE);

  if (!MinihashCheck($param_decoded['hash'], $param_decoded['uid'], $param_decoded['email'])) {

    //set header to deliver a vcard
    header('Content-Type: text/x-vCard; charset=utf-8');
    header('Content-Disposition: attachment; filename="error.vcf"');

    echo "BEGIN:VCARD\n";
    echo "VERSION:3.0\n";
    echo "END:VCARD\n";
  }
  else {

    $account = user_load($param_decoded['uid']);

    $FirstName = addslashes($account->FirstName);
    $LastName = addslashes($account->LastName);
    $Street = addslashes($account->Street);
    $City = addslashes($account->City);
    $Zip = addslashes($account->Zip);
    $Country = addslashes($account->Country);

    //the email prefix has a valid filename format
    $FileName = $account->mail . ".vcf";

    //set header to deliver a vcard
    header('Content-Type: text/x-vCard');
    header('Content-Disposition: attachment; filename="' . $FileName . '"');

    echo "BEGIN:VCARD\n";
    echo "VERSION:3.0\n";
    echo "N:$LastName;$FirstName\n";
    echo "EMAIL;TYPE=INTERNET:" . $param_decoded['email'] . "\n";
    echo "ADR;TYPE=intl,work,postal,parcel:;;$Street;$City;;$Zip;$Country\n";
    echo "END:VCARD\n";

  }

  exit;

}

/**
 * Menu callback; process sender phone number deletion
 */
function klicktipp_account_sender_phonenumber_delete($account, $index) {

  // rearrange senders
  $senders = array();
  foreach ($account->AdditionalSenderPhoneNumber as $key => $sender) {
    if ($key != $index) {
      $senders[] = $sender;
    }
  }

  user_save($account, array('AdditionalSenderPhoneNumber' => $senders));
  drupal_set_message(t('Your sender phone number has been removed.'));
  drupal_goto('user/me/edit');
}

/**
 * AJAX endpoint for JavaScript:js_ajax_get(); @see /misc/script.js
 * @param stdClass $account
 * @param string $op : execute specified operation
 * @param string $value : operation value
 * @return string json
 */
function klicktipp_account_process_ajax($account, $op, $value) {

  $op = check_plain($op);
  $value = check_plain($value);

  $output = array();

  switch ($op) {
    case KLICKTIPP_ACCOUNT_DATA_PROCESSING_ORDER_ID:
      //user that have no data processing order will be reminded by a drupal message
      //canceling that message will set a flag to not show this message again until the session expires
      $_SESSION[KLICKTIPP_SESSION_USER_FLAGS] = (isset($_SESSION[KLICKTIPP_SESSION_USER_FLAGS])) ? $_SESSION[KLICKTIPP_SESSION_USER_FLAGS] : array();
      $_SESSION[KLICKTIPP_SESSION_USER_FLAGS][KLICKTIPP_ACCOUNT_DATA_PROCESSING_ORDER_ID] = (!empty($value)) ? TRUE : FALSE;
      $output['success'] = TRUE; //the response won't be evaluated

      break;

    default:
      $output['error'] = TRUE;
      $output['message'] = "Invalid operation";
  }

  //return $output['success'] = TRUE or $output['error'] = TRUE to invoke possible handlers

  drupal_json_output($output);
  exit;

}

/**
 * implementation of hook_form_alter "user-profile-form"
 */
function _klicktipp_user_profile_form(&$form, &$form_state, $form_id) {

  $account = $form['#user'];
  $edit = (array) $account;

  //set breadcrumb and page title
  $page_title = t('My Account');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $form['PersInfo'] = array(
    '#title' => t('Personal information'),
    '#type' => 'fieldset',
    '#collapsible' => !user_access('access klicktipp', $account),
    '#collapsed' => !user_access('access klicktipp', $account),
    '#weight' => -15,
  );

  $weight = 1;
  $persInfoEditPath = "user/{$account->uid}/personal-information";
  $form['PersInfo']['changePersInfo'] = array(
    '#theme' => 'klicktipp_edit_button',
    '#title' => t('Change personal information'),
    '#value' => $persInfoEditPath,
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
    '#access' => menu_get_item($persInfoEditPath)['access'],
  );

  global $language;
  if ($language->language == 'ja') {
    // different sequence for japan
    $PersInfoWeights['Username'] = $weight++;
    $PersInfoWeights['CompanyName'] = $weight++;
    $PersInfoWeights['LastName'] = $weight++;
    $PersInfoWeights['FirstName'] = $weight++;
    $PersInfoWeights['Country'] = $weight++;
    $PersInfoWeights['Zip'] = $weight++;
    $PersInfoWeights['State'] = $weight++;
    $PersInfoWeights['City'] = $weight++;
    $PersInfoWeights['Street'] = $weight++;
    $PersInfoWeights['Phone'] = $weight++;
    $PersInfoWeights['Fax'] = $weight++;
    $PersInfoWeights['Website'] = $weight++;
    $PersInfoWeights['dst'] = $weight++;
  }
  else {
    $PersInfoWeights['Username'] = $weight++;
    $PersInfoWeights['CompanyName'] = $weight++;
    $PersInfoWeights['FirstName'] = $weight++;
    $PersInfoWeights['LastName'] = $weight++;
    $PersInfoWeights['Street'] = $weight++;
    $PersInfoWeights['Zip'] = $weight++;
    $PersInfoWeights['City'] = $weight++;
    $PersInfoWeights['Country'] = $weight++;
    $PersInfoWeights['State'] = $weight++;
    $PersInfoWeights['Phone'] = $weight++;
    $PersInfoWeights['Fax'] = $weight++;
    $PersInfoWeights['Website'] = $weight++;
    $PersInfoWeights['dst'] = $weight++;
  }

  $form['PersInfo']['Username'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['name'],
    '#weight' => $PersInfoWeights['Username'],
    '#title' => t('Username'),
  );
  $form['PersInfo']['CompanyName'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['CompanyName'],
    '#weight' => $PersInfoWeights['CompanyName'],
    '#title' => t('Company Name'),
  );
  $form['PersInfo']['FirstName'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['FirstName'],
    '#weight' => $PersInfoWeights['FirstName'],
    '#title' => t('First Name'),
  );
  $form['PersInfo']['LastName'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['LastName'],
    '#weight' => $PersInfoWeights['LastName'],
    '#title' => t('Last Name'),
  );
  $form['PersInfo']['Street'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['Street'],
    '#weight' => $PersInfoWeights['Street'],
    '#title' => t('Street'),
  );
  $form['PersInfo']['Zip'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['Zip'],
    '#weight' => $PersInfoWeights['Zip'],
    '#title' => t('Zip Code'),
    '#maxlength' => 50,
  );
  $form['PersInfo']['City'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['City'],
    '#weight' => $PersInfoWeights['City'],
    '#title' => t('City'),
    '#maxlength' => 50,
  );
  $form['PersInfo']['Country'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['Country'],
    '#weight' => $PersInfoWeights['Country'],
    '#title' => t('Country'),
  );
  $form['PersInfo']['State'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['State'],
    '#weight' => $PersInfoWeights['State'],
    '#title' => t('State'),
  );
  $form['PersInfo']['Phone'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['Phone'],
    '#weight' => $PersInfoWeights['Phone'],
    '#title' => t('Phone'),
  );
  $form['PersInfo']['Fax'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['Fax'],
    '#weight' => $PersInfoWeights['Fax'],
    '#title' => t('Fax'),
  );
  $form['PersInfo']['Website'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#value' => $edit['Website'],
    '#weight' => $PersInfoWeights['Website'],
    '#title' => t('Website'),
  );
  $tzoptions = array();
  foreach (DateTimeZone::listIdentifiers() as $TZ) {
    $tzoptions[$TZ] = t(/*ignore*/$TZ);
  }

  $timezone = empty($edit['dst']) ? variable_get(Dates::TIMEZONE_VARIABLE, 'Europe/Berlin') : $edit['dst'];
  $tmzTime = new DateTime(date('Y-m-d H:i:s', time()), new DateTimeZone($timezone));
  $form['PersInfo']['dst'] = array(
    '#type' => 'hidden', // ToDo: Set back to 'select' when Timezone usage is implemented
    '#default_value' => empty($edit['dst']) ? variable_get(Dates::TIMEZONE_VARIABLE, 'Europe/Berlin') : $edit['dst'],
    '#options' => $tzoptions,
    '#weight' => $PersInfoWeights['dst'],
    '#title' => t('Timezone'),
    '#description' => t('Time for timezone !timezone is: !date (Save your changes to update).', array(
      '!timezone' => $timezone,
      '!date' => Dates::formatDate(Dates::FORMAT_DMYHI, $tmzTime->getTimestamp()),
    )),
  );

  if (klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_ADMINISTRATOR)) {

    $form['AdditionalSenderEmailAddressGroup'] = array(
      '#type' => 'fieldset',
      '#title' => t('Sender Email Addresses'),
      '#weight' => $weight++,
    );

    $form['AdditionalSenderEmailAddressGroup']['intro'] = array(
      '#type' => 'markup',
      '#value' => "<p>" . t('AdditionalSenderEmailAddress::Separate dialog notification.') . "</p>",
    );

    $form['AdditionalSenderEmailAddressGroup']['buttons'] = array(
      '#type' => 'markup',
      '#weight' => 10000,
      '#prefix' => '<div class="button-row">',
      '#suffix' => '</div>',
    );

    $form['AdditionalSenderEmailAddressGroup']['buttons']['edit'] = array(
      '#theme' => 'klicktipp_edit_button',
      '#title' => t('Edit sender addresses'),
      '#value' => "user/{$account->uid}/sender",
      '#weight' => $weight++,
    );

  }

  // --- User SMS settings start ---
  if (user_access('access sms marketing')) {

    $form['EditSMSSettings'] = array(
      '#type' => 'fieldset',
      '#title' => t('SMS settings'),
      '#weight' => $weight++,
      '#description' => t('Manage you settings for sending SMS'),
    );

    $form['EditSMSSettings']['NexmoApiKey'] = array(
      '#type' => 'textfield',
      '#title' => t('Nexmo API key'),
      '#default_value' => empty($edit['UserSettings']['SMSSettings']['NexmoApiKey']) ? '' : $edit['UserSettings']['SMSSettings']['NexmoApiKey'],
      '#weight' => $weight++,
      '#description' => t('Enter your Nexmo API key'),
      '#parents' => array('EditSMSSettings', 'NexmoApiKey'),
    );

    $form['EditSMSSettings']['NexmoApiSecret'] = array(
      '#type' => 'textfield',
      '#title' => t('Nexmo API secret'),
      '#default_value' => empty($edit['UserSettings']['SMSSettings']['NexmoApiSecret']) ? '' : $edit['UserSettings']['SMSSettings']['NexmoApiSecret'],
      '#weight' => $weight++,
      '#description' => t('Enter your Nexmo API secret'),
      '#parents' => array('EditSMSSettings', 'NexmoApiSecret'),
    );

    $form['EditSMSSettings']['TwilioAccountSid'] = array(
      '#type' => 'textfield',
      '#title' => t('Twilio AccountSid'),
      '#default_value' => empty($edit['UserSettings']['SMSSettings']['TwilioAccountSid']) ? '' : $edit['UserSettings']['SMSSettings']['TwilioAccountSid'],
      '#weight' => $weight++,
      '#description' => t('Enter your Twilio AccountSid'),
      '#parents' => array('EditSMSSettings', 'TwilioAccountSid'),
    );

    $form['EditSMSSettings']['TwilioAuthToken'] = array(
      '#type' => 'textfield',
      '#title' => t('Twilio AuthToken'),
      '#default_value' => empty($edit['UserSettings']['SMSSettings']['TwilioAuthToken']) ? '' : $edit['UserSettings']['SMSSettings']['TwilioAuthToken'],
      '#weight' => $weight++,
      '#description' => t('Enter your Twilio AuthToken'),
      '#parents' => array('EditSMSSettings', 'TwilioAuthToken'),
    );

    $form['EditSMSSettings']['NotificationEmailVerified'] = array(
      '#type' => 'value',
      '#value' => empty($edit['UserSettings']['SMSSettings']['NotificationEmailVerified']) ? 0 : $edit['UserSettings']['SMSSettings']['NotificationEmailVerified'],
      '#parents' => array('EditSMSSettings', 'NotificationEmailVerified'),
    );

    $status = "";
    if (!empty($edit['UserSettings']['SMSSettings']['NotificationEmailAddress'])) {
      $status = " (" . ((!empty($edit['UserSettings']['SMSSettings']['NotificationEmailVerified'])) ? ('confirmed') : t('not verified')) . ")";
    }
    $form['EditSMSSettings']['NotificationEmailAddress'] = array(
      '#type' => 'textfield',
      '#title' => t('SMS notification email address') . $status,
      '#default_value' => empty($edit['UserSettings']['SMSSettings']['NotificationEmailAddress']) ? '' : Subscribers::DepunycodeEmailAddress($edit['UserSettings']['SMSSettings']['NotificationEmailAddress']),
      '#weight' => $weight++,
      '#description' => t('Enter an email address to which every incoming SMS should be forwarded.'),
      '#parents' => array('EditSMSSettings', 'NotificationEmailAddress'),
    );

    if (!empty($edit['UserSettings']['SMSSettings']['NexmoApiKey']) || !empty($edit['UserSettings']['SMSSettings']['TwilioAuthToken'])) {

      $form['AdditionalSenderPhoneNumberGroup'] = array(
        '#type' => 'fieldset',
        '#title' => t('SMS Sender Phone Numbers'),
        '#weight' => $weight++,
      );

      $form['AdditionalSenderPhoneNumberGroup']['intro'] = array(
        '#type' => 'markup',
        '#value' => "<p>" . t('Add phone numbers to be used as SMS senders in Klick-Tipp. Recipients are able to reply your SMS to these numbers. After you add and verify a phone number, you will be able to select this number as a sender in each SMS configuration. <b>Note:</b> You may always enter your name as a sender, so this is optional.') . "</p>",
      );

      $senderweight = 1;

      $header = array(
        t('Sender phone number'),
        t('Status'),
        t('Action'),
      );

      $toverify = array();
      $rows = array();
      foreach ($edit['AdditionalSenderPhoneNumber'] as $index => $sender) {
        $row = array(
          $sender['phonenumber'],
          array('data' => $sender['verified'] > 0 ? t('confirmed') : t('not verified')),
          array(
            'data' => l(t('delete'), 'user/' . $account->uid . '/smssenderdelete/' . ($index == 0 ? '0' : $index)),
            'class' => array('tdlast'),
          ),
        );
        $rows[] = $row;

        // remember this to verify
        if (empty($sender['verified'])) {
          $toverify = $sender;
        }
      }

      $form['AdditionalSenderPhoneNumberGroup']['NewAdditionalSenderPhoneNumber'] = array(
        '#type' => 'textfield',
        '#default_value' => empty($toverify) ? $form_state['input']['NewAdditionalSenderPhoneNumber'] : $toverify['phonenumber'],
        '#weight' => $senderweight++,
        '#title' => t('Additional Sender Phone Number'),
        '#quickhelp' => 'user-me-edit-additional-sender-phone-number',
      );
      if (!empty($toverify)) {
        $form['AdditionalSenderPhoneNumberGroup']['NewAdditionalSenderPhoneNumberConfirmationCode'] = array(
          '#type' => 'textfield',
          '#title' => t('Confirmation code'),
          '#default_value' => '',
          '#weight' => $weight++,
          '#description' => t('Enter the code sent to you via SMS.'),
        );
      }

      if (!empty($rows)) {
        $form['AdditionalSenderPhoneNumberGroup']['SenderTable'] = array(
          '#type' => 'markup',
          '#value' => theme('table', array(
            'header' => $header,
            'rows' => $rows,
            'attributes' => ['data-e2e-id' => 'table-account-additional-sms-number']
          )),
          '#weight' => $senderweight++,
        );
      }
    }
  }

  // --- User OpenAI settings ---

  //TODO launch darkly flag?
  if (user_access('use whitelabel domain', $account)) {
    $form['EditOpenAISettings'] = array(
      '#type' => 'fieldset',
      '#title' => t('account:settings:openai:ChatGPT Settings'),
      '#weight' => $weight++,
    );

    $form['EditOpenAISettings']['OpenAIApiKey'] = array(
      '#type' => 'textfield',
      '#title' => t('account:settings:openai:ChatGPT API Key'),
      '#default_value' => empty($edit['UserSettings']['EditOpenAISettings']['OpenAIApiKey']) ? '' : $edit['UserSettings']['EditOpenAISettings']['OpenAIApiKey'],
      '#weight' => $weight++,
      '#parents' => array('EditOpenAISettings', 'OpenAIApiKey'),
      '#maxlength' => 256,
    );
  }

  // --- Digistore Accounts ---

  if (!empty($edit['UserSettings']['DigiStore24ApiKeys'])) {

    $form['Digistore24Accounts'] = array(
      '#type' => 'fieldset',
      '#title' => t('Digistore24 accounts'),
      '#weight' => $weight++,
    );

    $header = array(
      t('Merchant name'),
      array('data' => t('Action'), 'class' => array('table-col-fit')),
    );

    $rows = array();
    $form_included = FALSE;
    foreach ($edit['UserSettings']['DigiStore24ApiKeys'] as $merchant => $apikey) {

      if (!$form_included) {

        $form['Digistore24Accounts'][$merchant] = array(
          '#theme' => 'klicktipp_delete_modal_button',
          '#title' => t('Disconnect'),
          '#value' => "klicktipp_account_disconnect_digistore_modal_form",
          '#external' => FALSE,
          '#modal_confirm' => drupal_get_form("klicktipp_account_disconnect_digistore_modal_form", $account, $merchant),
          '#attributes' => array(
            'data-event-click' => 'js_digistore_disconnect_set_merchant_name',
            'data-event-click-args' => $merchant,
          ),
          '#suffix' => '
            <script type="text/javascript">

              function js_digistore_disconnect_set_modal_href(element, args) {
                element.attr("href", "#" + element.attr("data-modal-id"));
              }

              function js_digistore_disconnect_set_merchant_name(element, args) {
                $("#klicktipp-account-disconnect-digistore-modal-form .modal-message em").html(args);
                $(".edit-Entity").val(args);
              }

            </script>
          ',
        );

        $form_included = TRUE;

      }
      else {

        $form['Digistore24Accounts'][$merchant] = array(
          '#theme' => 'klicktipp_delete_button',
          '#title' => t('Disconnect'),
          '#value' => '',
          '#attributes' => array(
            'data-toggle' => 'modal',
            'data-modal-id' => "klicktipp_account_disconnect_digistore_modal_form",
            'data-event-load' => 'js_digistore_disconnect_set_modal_href',
            'data-event-click' => 'js_digistore_disconnect_set_merchant_name',
            'data-event-click-args' => $merchant,
          ),
        );

      }

      $rows[] = array(
        $merchant,
        array('data' => render($form['Digistore24Accounts'][$merchant]))
      );

    }

    $form['Digistore24Accounts']['Table'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array(
        'header' => $header,
        'rows' => $rows,
        'attributes' => ['data-e2e-id' => 'table-account-digistore-accounts']
      )),
    );

  }

  // --- Cleverbridge settings ---

  $CleverbridgeCredentials = VarCleverbridge::GetVariable($account->uid, []);
  if (
    !empty($CleverbridgeCredentials) &&
    klicktipp_listinterfaces_IPN_access($account, 'access IPN Cleverbridge')
  ) {

    $form['CleverbridgeCredentials'] = array(
      '#type' => 'fieldset',
      '#title' => t('Cleverbridge IPN Credentials'),
      '#weight' => $weight++,
    );

    $form['CleverbridgeCredentials']['IPNUsername'] = array(
      '#type' => 'textfield',
      '#title' => t('Cleverbridge IPN Username'),
      '#default_value' => (empty($CleverbridgeCredentials['user'])) ? '' : $CleverbridgeCredentials['user'],
      '#weight' => $weight++,
      '#parents' => array('EditCleverbridge', 'IPNUsername'),
    );

    $form['CleverbridgeCredentials']['IPNPassword'] = array(
      '#type' => 'textfield',
      '#title' => t('Cleverbridge IPN Password'),
      '#default_value' => (empty($CleverbridgeCredentials['pw'])) ? '' : $CleverbridgeCredentials['pw'],
      '#weight' => $weight++,
      '#parents' => array('EditCleverbridge', 'IPNPassword'),
    );

  }

  // --- User SMS settings end ---

  // --- Subaccount settings ---

  if (user_access('use developer key', $account)) {

    $form['DeveloperKeySettings'] = array(
      '#type' => 'fieldset',
      '#title' => t('Developer Key'),
      '#weight' => $weight++,
    );

    $developer_key = Subaccount::CreateDeveloperKey($account->uid);
    $form['DeveloperKeySettings']['DeveloperKey'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_item',
      '#title' => t('Developer Key'),
      '#value' => $developer_key,
      '#weight' => $weight++,
      '#quickhelp' => 'user-me-edit-developer-key',
      '#attributes' => array(
        'data-event-click' => 'js_select_text',
      ),
    );

    $customer_key = Subaccount::CreateCustomerKey($account->uid, $account->uid);
    $form['DeveloperKeySettings']['CustomerKey'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_item',
      '#title' => t('Your Own Customer Key'),
      '#value' => $customer_key,
      '#weight' => $weight++,
      '#quickhelp' => 'user-me-edit-own-customer-key',
      '#attributes' => array(
        'data-event-click' => 'js_select_text',
        'style' => 'width: 100%;',
      ),
    );

    $partner_name = $account->FirstName . ' ' . $account->LastName;
    if (!empty($account->CompanyName)) {
      $partner_name .= ', ' . $account->CompanyName;
    }
    $form['DeveloperKeySettings']['PartnerAccessGrantName'] = array(
      '#type' => 'textfield',
      '#title' => t('Your name in "Grant access dialog"'),
      '#default_value' => empty($edit['UserSettings']['PartnerAccessGrantName']) ? '' : $edit['UserSettings']['PartnerAccessGrantName'],
      '#weight' => $weight++,
      '#description' => t('Enter, how your name shows up in the "Grant access dialog" to your customer. Leave empty for default: %name', array('%name' => $partner_name)),
      '#quickhelp' => 'user-me-edit-partner-access-grant-name',
    );

    $partner_id = Core::CryptNumber($account->uid);
    $partner_link = url("grantapiaccess/$partner_id", array('absolute' => TRUE)) . "?url=";
    $form['DeveloperKeySettings']['PartnerAccessGrantLink'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_item',
      '#title' => t('Link to "Grant access dialog"'),
      '#value' => $partner_link,
      '#weight' => $weight++,
      '#description' => t('For your customer to grant api access to you, they have to follow the link above and confirm. Add an URL, where we shall redirect your customer to. We will add the customer key to your URL.'),
      '#quickhelp' => 'user-me-edit-partner-access-grant-link',
      '#attributes' => array(
        'data-event-click' => 'js_select_text',
      ),
    );
  }

  // --- Subaccount settings ---

  $useSubaccounts = user_access('use subaccounts', $account);
  $useAgencyAccess = user_access('use agency access', $account);

  $form['SubaccountSettings'] = array(
    '#type' => 'fieldset',
    '#title' => t('Subaccount settings'),
    '#weight' => $weight++,
    '#access' => $useSubaccounts || $useAgencyAccess
  );

  $form['SubaccountSettings']['actions'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['SubaccountSettings']['actions']['create'] = array(
    '#theme' => 'klicktipp_edit_button',
    '#title' => t('Create subaccount'),
    '#value' => "user/{$account->uid}/subaccount/add",
    '#weight' => $weight++,
    '#access' => $useSubaccounts
  );

  $form['SubaccountSettings']['actions']['createAgencyAccess'] = array(
    '#theme' => 'klicktipp_edit_button',
    '#title' => t('Add Agency Access'),
    '#value' => "user/{$account->uid}/subaccount/addagency",
    '#weight' => $weight++,
  );

  $form['SubaccountSettings']['actions']['manageSalesEmployees'] = array(
    '#theme' => 'klicktipp_edit_button',
    '#title' => t("BCREvent::Employee::Manage Sales Employees"),
    '#value' => "sales-employees/{$account->uid}",
    '#weight' => $weight++,
    '#access' => $useSubaccounts
  );

  $subaccount_options = Subaccount::GetTypeOptions();

  $header = array(
    t('Subaccount Name'),
    t('Subaccount Type'),
    t('Agency Access'),
    '',
  );

  $rows = array();

  $superaccounts = Subaccount::GetSuperAccounts($account->uid);
  $hasOwnApprovedAccount = FALSE;
  foreach ($superaccounts as $sa) {
    if ($sa['IsOwnAccount'] == Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_APPROVED) {
      $hasOwnApprovedAccount = TRUE;
    }
  }
  foreach ($superaccounts as $said => $sa) {
    $a = user_load($said);
    if (empty($sa['AgencyAccess'])) {
      $row = array(
        l($a->name, "user/{$account->uid}/subaccount/{$said}/edit"),
        array('data' => $subaccount_options[$sa['SubaccountType']]),
        array('data' => ''),
      );
    }
    else {
      $row = array(
        l(Subscribers::DepunycodeEmailAddress($a->mail), "user/{$account->uid}/subaccount/{$said}/view"),
        array('data' => $subaccount_options[$sa['SubaccountType']]),
        array('data' => 'x'),
      );
    };
    if ($hasOwnApprovedAccount && $sa['IsOwnAccount'] != Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_APPROVED) {
      // this is not the approved account, show empty row entry
      $data = '';
    }
    else {
      switch ($sa['IsOwnAccount']) {
        case Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_PENDING:
          $form[$said] = array(
            '#name' => $said,
            '#type' => 'submit',
            '#theme' => 'klicktipp_edit_submit',
            '#submit' => array('klicktipp_account_approve_account_submit'),
            '#value' => t('Approve as own account'),
          );
          $data = drupal_render($form[$said]);
          break;
        case Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_APPROVED:
          $form['RemoveApproval'] = array(
            '#name' => $said,
            '#type' => 'submit',
            '#theme' => 'klicktipp_delete_submit',
            '#submit' => array('klicktipp_account_remove_approved_account_submit'),
            '#value' => t('Remove approval as own account'),
          );
          $data = drupal_render($form['RemoveApproval']);
          break;
        case Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_NONE:
        default:
          $data = '';
          break;
      }
    }
    $row[] = array('data' => $data, 'class' => array('table-col-fit'));
    $rows[] = $row;
  }

  if (empty($rows)) {
    $SubaccountTable = '<em>' . t("No subaccount defined.") . '</em>';
  }
  else {
    $SubaccountTable = theme('table', array(
      'header' => $header,
      'rows' => $rows,
      'attributes' => ['data-e2e-id' => 'table-account-subaccounts']
    ));
  }

  $form['SubaccountSettings']['SubaccountTable'] = array(
    '#type' => 'markup',
    '#value' => $SubaccountTable,
    '#weight' => $weight++,
  );

  // --- FullContact settings ---

  if (user_access('access fullcontact', $account)) {
    $VarFullContact = VarFullContact::GetVariable($account->uid, []);

    $form['FullContactSettings'] = array(
      '#type' => 'fieldset',
      '#title' => t('FullContact settings'),
      '#weight' => $weight++,
    );

    $form['FullContactSettings']['ApiKey'] = array(
      '#type' => 'textfield',
      '#title' => t('FullContact API key'),
      '#default_value' => empty($VarFullContact['apiKey']) ? '' : $VarFullContact['apiKey'],
      '#weight' => $weight++,
      '#description' => t('Enter your FullContact API key'),
      '#parents' => array('EditFullContact', 'ApiKey'),
    );

    $form['FullContactSettings']['Likelihood'] = array(
      '#type' => 'textfield',
      '#title' => t('FullContact Likelihood'),
      '#default_value' => empty($VarFullContact['likelihood']) ? '85' : $VarFullContact['likelihood'],
      '#weight' => $weight++,
      '#maxlength' => 3,
      '#description' => t('Enter a number between 1 and 100 as your desired FullContact likelihood.'),
      '#parents' => array('EditFullContact', 'Likelihood'),
    );

  }

  // --- User privileges ---

  if (user_access('administer klicktipp')) {
    // user privileges can only be edited by admins or support users

    $privileges_weight = 1;

    $form['UserPrivileges'] = array(
      '#type' => 'fieldset',
      '#title' => t('User privileges'),
      '#weight' => $weight++,
    );

    //all (future) privileges will be stored in $account->UserPrivileges
    //this flag helps to store privileges more comfortable @see case 'update'
    $form['EditUserPrivileges'] = array(
      '#type' => 'value',
      '#value' => 1,
    );

    $UserGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID);

    // startup has only limited access, other products have full access
    $hasFullAccess = $UserGroup['Data']['GroupCategory'] === UserGroups::ACCOUNT_CATEGORY_STARTUP ? false : true;

    $form['UserPrivileges']['UnlimitedEmailsPerDay'] = array(
      '#type' => 'checkbox',
      '#default_value' => empty($edit['UserPrivileges']['UnlimitedEmailsPerDay']) ? 0 : $edit['UserPrivileges']['UnlimitedEmailsPerDay'],
      '#return_value' => 1,
      '#weight' => $privileges_weight++,
      '#title' => t('Send unlimited emails per contact per day'),
      '#description' => t('Without this privilege the maximum of emails sent per contact per day is limited to 4.'),
      '#access' => $hasFullAccess,
    );

    $form['UserPrivileges']['AllowSingleOptinLists'] = array(
      '#type' => 'checkbox',
      '#default_value' => empty($edit['UserPrivileges']['AllowSingleOptinLists']) ? 0 : $edit['UserPrivileges']['AllowSingleOptinLists'],
      '#return_value' => 1,
      '#weight' => $privileges_weight++,
      '#title' => t('Allow SingleOptin Lists'),
      '#description' => t('Allow this customer to create single optin lists. (Should be for Enterprise customers only)'),
      '#access' => $hasFullAccess,
    );

    $form['UserPrivileges']['AllowSignaturesWithoutParameters'] = array(
      '#type' => 'checkbox',
      '#default_value' => empty($edit['UserPrivileges']['AllowSignaturesWithoutParameters']) ? 0 : $edit['UserPrivileges']['AllowSignaturesWithoutParameters'],
      '#return_value' => 1,
      '#weight' => $privileges_weight++,
      '#title' => t('Allow Signatures without Parameters'),
      '#description' => t('Disable check for required %User:*% signature parameters. (Should be for Enterprise customers only)'),
      '#access' => $hasFullAccess,
    );

    $form['UserPrivileges']['AllowKlicktippSenderAddress'] = array(
      '#type' => 'checkbox',
      '#default_value' => empty($edit['UserPrivileges']['AllowKlicktippSenderAddress']) ? 0 : $edit['UserPrivileges']['AllowKlicktippSenderAddress'],
      '#return_value' => 1,
      '#weight' => $privileges_weight++,
      '#title' => t('Allow Klicktipp Domain Addresses as Senders'),
      '#description' => t('For Klicktipp Marketing only, e.g. <EMAIL>'),
      '#access' => $hasFullAccess,
    );

    $form['UserPrivileges']['ShowConfirmationEmailStats'] = array(
      '#type' => 'checkbox',
      '#default_value' => empty($edit['UserPrivileges']['ShowConfirmationEmailStats']) ? 0 : $edit['UserPrivileges']['ShowConfirmationEmailStats'],
      '#return_value' => 1,
      '#weight' => $privileges_weight++,
      '#title' => t('Show confirmation email stats'),
      '#access' => true,
    );

    $form['UserPrivileges']['DisplayEnglishLanguage'] = array(
      '#type' => 'checkbox',
      '#default_value' => empty($edit['UserPrivileges']['DisplayEnglishLanguage']) ? 0 : $edit['UserPrivileges']['DisplayEnglishLanguage'],
      '#return_value' => 1,
      '#weight' => $privileges_weight++,
      '#title' => t('Display english language'),
      '#description' => t('Display some specific dialogues (e.g. unsubscriptions) in English.'),
      '#access' => true,
    );

    $form['UserPrivileges']['KnownForSpamActivity'] = array(
      '#type' => 'checkbox',
      '#default_value' => empty($edit['UserPrivileges']['KnownForSpamActivity']) ? 0 : 1,
      '#return_value' => 1,
      '#weight' => $privileges_weight++,
      '#title' => t('Mark this user as known for spam acitity'),
      '#description' => t('If checked, we show 404 for all tracking-, countdown-, splittest- and opt-confirm links of this user'),
      '#access' => true,
    );
  }

  // --- User settings ---

  if (user_access('access klicktipp', $account)) {

    $form['UserSettings'] = array(
      '#type' => 'fieldset',
      '#title' => t('User settings'),
      '#weight' => $weight++,
    );

    $UserGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID);

    // startup has only limited access, other products have full access
    $hasFullAccess = $UserGroup['Data']['GroupCategory'] === UserGroups::ACCOUNT_CATEGORY_STARTUP ? false : true;

    $form['UserSettings']['DeactivateGlobalBounceManagement'] = array(
      '#type' => 'checkbox',
      '#title' => t('Deactivate Global Bounce Management'),
      '#weight' => $weight++,
      '#default_value' => empty($edit['UserSettings']['DeactivateGlobalBounceManagement']) ? 0 : $edit['UserSettings']['DeactivateGlobalBounceManagement'],
      '#return_value' => 1,
      '#disabled' => !user_access('deactivate gbm', $account),
      '#description' => t('Check to deactivate global bounce management. Enterprise level with own reputation service only.'),
      '#access' => $hasFullAccess,
    );

    $form['UserSettings']['PlainTextContentByUser'] = array(
      '#type' => 'checkbox',
      '#title' => t('Create alternative plain text email content yourself'),
      '#weight' => $weight++,
      '#default_value' => empty($edit['UserSettings']['PlainTextContentByUser']) ? 0 : $edit['UserSettings']['PlainTextContentByUser'],
      '#return_value' => 1,
      '#description' => t('Check to edit both html and plain text versions of a multipart email yourself. Otherwise the plain text is automatically created from the html version.'),
      '#access' => true,
    );

    $form['UserSettings']['DeactivateUserNotificationEmails'] = array(
      '#type' => 'checkbox',
      '#title' => t('Deactivate notification emails'),
      '#weight' => $weight++,
      '#default_value' => empty($edit['UserSettings']['DeactivateUserNotificationEmails']) ? 0 : $edit['UserSettings']['DeactivateUserNotificationEmails'],
      '#return_value' => 1,
      '#description' => t('Check to deactivate notification emails that inform you about misconfigurations in your account.'),
      '#access' => true,
    );

    // only single value tags can be used to tag aged subscribers
    $userSingleValueTags = Tag::RetrieveTagsByCategory($account->uid, [Tag::CATEGORY_MANUAL]);
    $userSingleValueTags = array_filter($userSingleValueTags, function($tag) {
      return empty($tag['MultiValue']);
    });
    $tagOptions = [];
    foreach ($userSingleValueTags as $tag) {
      $tagOptions[$tag['TagID']] = $tag['TagName'];
    }

    $currentTag = VarAgedSubscribers::GetVariable($account->uid, 0);
    $form['UserSettings']['TagAgedSubscribersCurrent'] = [
      '#type' => 'value',
      '#value' => $currentTag
    ];

    $tagAgedSubscribersAfterXMonths =
      empty($edit['UserSettings']['TagAgedSubscribersAfterXMonths']) ? 6
        : $edit['UserSettings']['TagAgedSubscribersAfterXMonths'];

    $form['UserSettings']['TagAgedSubscribers'] = array(
      '#type' => 'textfield',
      '#default_value' => $currentTag,
      '#title' => t("Tag aged subscribers"),
      '#weight' => $weight++,
      '#magic_select' => array(
        'autocomplete' => '',
        'options' => $tagOptions,
        'multiple' => FALSE,
        'free_entries' => TRUE,
        'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
        'allow_numeric_only' => FALSE,
        'numeric_only_message' => t('Tags cannot contain only numbers.'),
        'form_post' => $form_state['input']['TagAgedSubscribers'], //catch free entries on form validation error
      ),
      '#theme' => 'klicktipp_magic_select',
      '#quickhelp' => 'tag-aged-subscribers',
      '#description' => t(
        'Tag subscribers, that did not open any email in the last %TagAgedSubscribersAfterXMonths months.',
        ['%TagAgedSubscribersAfterXMonths' => $tagAgedSubscribersAfterXMonths]),
      '#access' => true,
    );

    $form['UserSettings']['TagAgedSubscribersAfterXMonths'] = [
      '#type' => 'textfield',
      '#default_value' => $tagAgedSubscribersAfterXMonths,
      '#title' => t("Tag aged subscribers after X months"),
      '#weight' => $weight++,
      '#maxlength' => 1,
      '#description' => t('Enter after how many months the Subscriber will receive the Tag. Maximum is 6 months.'),
      '#access' => true,
    ];

    $TagEmailPreviewSubscriber = VarEmailPreviewSubscriber::GetVariable($account->uid, 0);
    $form['UserSettings']['TagEmailPreviewSubscriber'] = array(
      '#type' => 'textfield',
      '#default_value' => $TagEmailPreviewSubscriber ?: NULL,
      '#title' => t("AccountSettings::Tag email preview subscriber"),
      '#weight' => $weight++,
      '#magic_select' => array(
        'autocomplete' => '',
        'options' => Tag::RetrieveTagsAsOptionsArray($account->uid, array(Tag::CATEGORY_MANUAL)),
        'multiple' => FALSE,
        'free_entries' => TRUE,
        'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
        'allow_numeric_only' => FALSE,
        'numeric_only_message' => t('Tags cannot contain only numbers.'),
        'form_post' => $form_state['input']['TagEmailPreviewSubscriber'], //catch free entries on form validation error
      ),
      '#theme' => 'klicktipp_magic_select',
      '#quickhelp' => 'tag-email-preview-subscriber',
      '#description' => t('AccountSettings::Tag subscribers to use for preview emails.'),
      '#access' => true,
    );

    $form['UserSettings']['EE_MANUALSAVEBUTTON'] = [
      '#type' => 'checkbox',
      '#default_value' => VarAppSettings::GetAppSetting($account->uid, VarAppSettings::EE_MANUALSAVEBUTTON, 0),
      '#return_value' => 1,
      '#weight' => $weight++,
      '#title' => t('Email Editor Manual Save Button'),
      '#description' => t('Display manual save button in email editor.'),
      '#access' => true,
    ];

    $form['#attached']['css'][] = array(
      'data' => drupal_get_path('theme', 'bootstrapklicktipp') . '/css/magicsuggest.css',
      'type' => 'file'
    );

    $form['#attached']['js'][] = array(
      'data' => 'misc/magicsuggest-class.js',
      'type' => 'file',
    );
    $form['#attached']['js'][] = array(
      'data' => 'misc/magicsuggest.js',
      'type' => 'file',
    );
  }

  $form_state['#redirect'] = "user/{$account->uid}/edit";
  // give klicktipp's validator a chance to punycode user's email address
  // otherwise drupal's email validation will fail
  array_unshift($form['#validate'] , '_klicktipp_user_profile_form_validate');

  //Note: call klicktipp's submit first so we can react on changed data (compared to $account)
  array_unshift($form['#submit'], '_klicktipp_user_profile_form_submit');

  # remove all settings that reside now in Keycloak
  $form['account']['mail']['#disabled'] = true;
  $form['account']['mail']['#description'] = t(
    'Your e-mail address is now part of the <a href="!url">account security</a> settings.',
    [
      '!url' => '/auth/account-console',
    ]
  );
  $form['account']['name']['#disabled'] = true;
  $form['account']['name']['#description'] = 'Usernames are changed in Keycloak now!';
  unset($form['account']['pass']);
  unset($form['account']['current_pass']);
  unset($form['account']['current_pass_required_values']);
}

function _klicktipp_user_profile_form_validate(&$form, &$form_state) {

  if (!empty($form['AdditionalSenderEmailAddressGroup']['NewAdditionalSenderEmailAddress'])) {
    form_set_value($form['AdditionalSenderEmailAddressGroup']['NewAdditionalSenderEmailAddress'], Subscribers::PunycodeEmailAddress(check_plain($form_state['values']['NewAdditionalSenderEmailAddress'])), $form_state);
  }
  if (!empty($form['EditSMSSettings']['NotificationEmailAddress'])) {
    form_set_value($form['EditSMSSettings']['NotificationEmailAddress'], Subscribers::PunycodeEmailAddress(check_plain($form_state['values']['EditSMSSettings']['NotificationEmailAddress'])), $form_state);
  }

  $edit = $form_state['values'];
  $account = $form['#user'];

  // validate new sender phone number
  if (!empty($edit['NewAdditionalSenderPhoneNumber'])) {
    // fail, if not a valid email address
    $addphonenumber = Subscribers::FormatSMSNumber($edit['NewAdditionalSenderPhoneNumber']);
    if (empty($addphonenumber)) {
      form_set_error('NewAdditionalSenderPhoneNumber', t('This is not a valid phone number.'));
    }

    // add this to data
    //Note: we need to add the data here for klicktipp_user_presave() but it will be ignored if the form validation fails
    if (isset($account->AdditionalSenderPhoneNumber)) {
      $form_state['values']['AdditionalSenderPhoneNumber'] = $account->AdditionalSenderPhoneNumber;
    }
    else {
      $form_state['values']['AdditionalSenderPhoneNumber'] = array();
    }
    $found = FALSE;
    foreach ($form_state['values']['AdditionalSenderPhoneNumber'] as $index => $sender) {
      if ($sender['phonenumber'] == $addphonenumber) {
        if (!empty($edit['NewAdditionalSenderPhoneNumberConfirmationCode'])) {
          if ($sender['code'] == strtolower(trim($edit['NewAdditionalSenderPhoneNumberConfirmationCode']))) {
            unset ($sender['code']);
            $sender['verified'] = 1;
            $form_state['values']['AdditionalSenderPhoneNumber'][$index] = $sender;
          }
          else {
            form_set_error('NewAdditionalSenderPhoneNumber', t('Sender phone number verification failed.'));
          }
        }
        $found = TRUE;
        break;
      }
    }
    if (!$found) {
      $form_state['values']['AdditionalSenderPhoneNumber'][] = array(
        'phonenumber' => $addphonenumber,
        'verified' => 0,
        'code' => klicktipp_generate_tan_code(4),
      );
    }
  }

  if (isset($form_state['values']['EditUserPrivileges'])) {
    //the flag EditUserPrivileges is only present with user_access('administer klicktipp')
    //Note: we need to add the data here for klicktipp_user_presave() but it will be ignored if the form validation fails

    // if array UserPrivileges doesn't exist, create it
    if (isset($account->UserPrivileges)) {
      $form_state['values']['UserPrivileges'] = $account->UserPrivileges;
    }
    else {
      $form_state['values']['UserPrivileges'] = array();
    }

    //store privilege UnlimitedEmailsPerDay
    $form_state['values']['UserPrivileges']['UnlimitedEmailsPerDay'] = $form_state['values']['UnlimitedEmailsPerDay'];
    unset($form_state['values']['UnlimitedEmailsPerDay']);

    //store privilege AllowSingleOptinLists
    $form_state['values']['UserPrivileges']['AllowSingleOptinLists'] = $form_state['values']['AllowSingleOptinLists'];
    unset($form_state['values']['AllowSingleOptinLists']);

    //store privilege AllowSignaturesWithoutParameters
    $form_state['values']['UserPrivileges']['AllowSignaturesWithoutParameters'] = $form_state['values']['AllowSignaturesWithoutParameters'];
    unset($form_state['values']['AllowSignaturesWithoutParameters']);

    //store privilege AllowKlicktippSenderAddress
    $form_state['values']['UserPrivileges']['AllowKlicktippSenderAddress'] = $form_state['values']['AllowKlicktippSenderAddress'];
    unset($form_state['values']['AllowKlicktippSenderAddress']);

    //store privilege ShowConfirmationEmailStats
    $form_state['values']['UserPrivileges']['ShowConfirmationEmailStats'] = $form_state['values']['ShowConfirmationEmailStats'];
    unset($form_state['values']['ShowConfirmationEmailStats']);

    //store privilege DisplayEnglishLanguage
    $form_state['values']['UserPrivileges']['DisplayEnglishLanguage'] = $form_state['values']['DisplayEnglishLanguage'];
    unset($form_state['values']['DisplayEnglishLanguage']);

    $form_state['values']['UserPrivileges']['KnownForSpamActivity'] = $form_state['values']['KnownForSpamActivity'];
    unset($form_state['values']['KnownForSpamActivity']);


    //store additional privileges here

    unset($form_state['values']['EditUserPrivileges']); //do not store the flag

  }

  // --- save user settings start ---
  //Note: we need to add the data here for klicktipp_user_presave() but it will be ignored if the form validation fails

  // if array UserSettings doesn't exist, create it
  if (isset($account->UserSettings)) {
    $form_state['values']['UserSettings'] = $account->UserSettings;
  }
  else {
    $form_state['values']['UserSettings'] = array();
  }

  // store DeactivateGlobalBounceManagement
  // the user needs a special permission (enterprise level) to do so
  // this deactivates the suppression list
  if (user_access('deactivate gbm', $account)) {
    $form_state['values']['UserSettings']['DeactivateGlobalBounceManagement'] = $form_state['values']['DeactivateGlobalBounceManagement'];
  }
  else {
    unset($form_state['values']['UserSettings']['DeactivateGlobalBounceManagement']);
  }
  unset($form_state['values']['DeactivateGlobalBounceManagement']);

  //store setting PlainTextContentByUser
  //if checked, the plain text editor is visible to user on multipart emails
  //@see: klicktipp_email_edit_form
  $form_state['values']['UserSettings']['PlainTextContentByUser'] = $form_state['values']['PlainTextContentByUser'];
  unset($form_state['values']['PlainTextContentByUser']);

  //store setting DeactivateUserNotificationEmails
  //if checked, no notification emails via \App\Klicktipp\Mail\KlicktippMail::sendUserNotification will be sent
  //@see: \App\Klicktipp\Mail\KlicktippMail::sendUserNotification
  $form_state['values']['UserSettings']['DeactivateUserNotificationEmails'] = $form_state['values']['DeactivateUserNotificationEmails'];
  unset($form_state['values']['DeactivateUserNotificationEmails']);

  //store setting PartnerAccessGrantName
  //shows up in 'Grant partner access' dialog
  //@see: klicktipp_register_partner_form
  $form_state['values']['UserSettings']['PartnerAccessGrantName'] = $form_state['values']['PartnerAccessGrantName'];
  unset($form_state['values']['PartnerAccessGrantName']);

  // --- validate SMS notification email address

  if (isset($form_state['values']['EditSMSSettings'])) {

    $SMSSettings = $form_state['values']['EditSMSSettings']; //values from form

    if (!empty($SMSSettings['NexmoApiKey']) && $SMSSettings['NexmoApiKey'] != $account->UserSettings['SMSSettings']['NexmoApiKey']) {
      // set callback urls and validate credentials
      Libraries::include('list_forms.inc');
      if (!klicktipp_list_forms_validate_nexmo_credentials($account, $SMSSettings['NexmoApiKey'], $SMSSettings['NexmoApiSecret'])) {
        form_set_error('EditSMSSettings][NexmoApiKey', t('Invalid NEXMO API credentials.'));
      };
    }

    if (!empty($SMSSettings['TwilioAccountSid']) && $SMSSettings['TwilioAccountSid'] != $account->UserSettings['SMSSettings']['TwilioAccountSid']) {
      // set callback urls and validate credentials
      Libraries::include('list_forms.inc');
      if (!klicktipp_list_forms_validate_twilio_credentials($account, $SMSSettings['TwilioAccountSid'], $SMSSettings['TwilioAuthToken'])) {
        form_set_error('EditSMSSettings][TwilioAccountSid', t('Invalid Twilio API credentials.'));
      };
    }

    if (!empty($SMSSettings['NotificationEmailAddress']) && !Subscribers::IsSameEmailAddress($SMSSettings['NotificationEmailAddress'], $account->UserSettings['SMSSettings']['NotificationEmailAddress'])) {
      //@see: klicktipp_account_sender_email_verification()

      // fail, if not a valid email address
      if (!Subscribers::ValidateEmailAddress($SMSSettings['NotificationEmailAddress'])) {
        form_set_error('EditSMSSettings][NotificationEmailAddress', t('This is not a valid e-mail address.'));
      }
      else {
        if (is_klicktipp_email_address($SMSSettings['NotificationEmailAddress'])) {
          form_set_error('EditSMSSettings][NotificationEmailAddress', t('This SMS notification email address is not allowed here.'));
        }
        else {
          $SMSSettings['NotificationEmailVerified'] = 0; //set status to "not verified"
        }
      }

    }

    //store sms settings in UserSettings
    $form_state['values']['UserSettings']['SMSSettings'] = $SMSSettings;

  }
  unset($form_state['values']['EditSMSSettings']);

  // --- validate openai settings

  if (isset($form_state['values']['EditOpenAISettings'])) {

    $OpenAISettings = $form_state['values']['EditOpenAISettings']; //values from form

    if (!empty($OpenAISettings['OpenAIApiKey']) && $OpenAISettings['OpenAIApiKey'] != $account->UserSettings['OpenAISettings']['OpenAIApiKey']) {
      // check key
      $chatGPT = new ChatGPTYoutubeSummery();
      $chatGPT->setApiKey($OpenAISettings['OpenAIApiKey']);
      if (empty($chatGPT->getModels())) {
        form_set_error('EditOpenAISettings][OpenAIApiKey', t('account:settings:openai:Invalid OpenAI key'));
      }
    }
    //store sms settings in UserSettings
    $form_state['values']['UserSettings']['EditOpenAISettings'] = $OpenAISettings;
  }
  unset($form_state['values']['EditOpenAISettings']);

  // --- save user settings end ---

  // --- validate FullContact credentials

  if (isset($form_state['values']['EditFullContact'])) {

    if (!empty($form_state['values']['EditFullContact']['ApiKey'])) {
      // only validate if an api key has been provided

      $FullContactSettings = $form_state['values']['EditFullContact'];
      $VarFullContact = VarFullContact::GetVariable($account->uid, []);

      if ($FullContactSettings['ApiKey'] != $VarFullContact['apiKey']) {
        // validate credentials
        if (!FullContactQueueWorker::validateCredentials($FullContactSettings['ApiKey'])) {
          form_set_error('EditFullContact][ApiKey', t('Invalid FullContact API credentials.'));
        }
      }

      // round likelihood to integer value
      $FullContactSettings['Likelihood'] = round($FullContactSettings['Likelihood']);

      if (empty($FullContactSettings['Likelihood']) || ($FullContactSettings['Likelihood'] < 1 || $FullContactSettings['Likelihood'] > 100) ) {
        form_set_error('EditFullContact][Likelihood', t('Invalid FullContact Likelihood. Must be between 1 and 100.'));
      }

    }

  }
  else {
    // unset fullcontact settings so they won't be stored
    unset($form_state['values']['EditFullContact']);
  }

  // --- validate aged subscribers tag

  if ($form_state['values']['TagAgedSubscribers'] !== $form_state['values']['TagAgedSubscribersCurrent']) {
    $existsID = Tag::RetrieveTagIdByName($account->uid, $form_state['values']['TagAgedSubscribers']);

    if ($existsID) {
      $existingTag = Tag::FromID($account->uid, $existsID);
      if ($existingTag && $existingTag->GetData('MultiValue')) {
        // the user entered the name of an existing MULTI VALUE tag
        // only SINGLE VALUE tags can be used / will be created
        form_set_error('TagAgedSubscribers', t('AgedSubscribersTag::The tag cannot be created since a multi value tag with the same name already exists.'));
      }
    }


  }

  // --- validate aged subscribers month setting

  if (
    !
    (
      empty($form_state['values']['TagAgedSubscribersAfterXMonths'])
      && !is_int($form_state['values']['TagAgedSubscribersAfterXMonths'])
    )
    &&
    (
      (int)$form_state['values']['TagAgedSubscribersAfterXMonths'] < 1
      || (int)$form_state['values']['TagAgedSubscribersAfterXMonths'] > 6
    )
  ) {
    form_set_error(
      'TagAgedSubscribers][TagAgedSubscribersAfterXMonths',
      t(
        'Invalid months value for Tag Aged Subscribers. Must be between 1 and 6.'
      )
    );
  } else {
    $form_state['values']['UserSettings']['TagAgedSubscribersAfterXMonths'] = $form_state['values']['TagAgedSubscribersAfterXMonths'];
    unset($form_state['values']['TagAgedSubscribersAfterXMonths']);
  }

}

function _klicktipp_user_profile_form_submit($form, &$form_state) {
  $account = $form['#user'];
  // get amember data that we wont change
  $member = _klicktipp_account_get_member($account->name);
  $fields_to_update = $member;
  unset($fields_to_update['data']); // unserialize it, if needed

  // do it
  // be aware of endless loops: this will call amember -> klicktipp-plugin -> coreapi -> hook_user
  amember_rpc_update_user($member['member_id'], $fields_to_update);

  // dont write dst, if default:
  if ($form_state['values']['dst'] == variable_get(Dates::TIMEZONE_VARIABLE, 'Europe/Berlin')) {
    $form_state['values']['dst'] = NULL;
  }

  if (!empty($form_state['values']['NewAdditionalSenderPhoneNumber']) && empty($form_state['values']['NewAdditionalSenderPhoneNumberConfirmationCode'])) {

    $PhoneNumber = Subscribers::FormatSMSNumber($form_state['values']['NewAdditionalSenderPhoneNumber']);
    if (!empty($PhoneNumber)) {
      // send verification sms
      foreach ($form_state['values']['AdditionalSenderPhoneNumber'] as $index => $sender) {
        if ($sender['phonenumber'] == $PhoneNumber && !empty($sender['code'])) {
          //call Nexmo API
          $result = klicktipp_nexmo_send_sms(
            $PhoneNumber,
            t('Confirmation Code: !code', array('!code' => $sender['code'])),
            $account->UserSettings['SMSSettings']['NexmoApiKey'],
            $account->UserSettings['SMSSettings']['NexmoApiSecret']
          );
          if ($result) {
            drupal_set_message(t('An SMS has been sent to %number with your confirmation code.', array(
              '%number' => $PhoneNumber,
            )));
          }
          break;
        }
      }
    }
  }
  unset($form_state['values']['NewAdditionalSenderPhoneNumber']);

  // --- save SMS settings start ---

  if (isset($form_state['values']['UserSettings']['SMSSettings'])) {

    $SMSSettings = $form_state['values']['UserSettings']['SMSSettings'];

    $SMSSettings['NotificationEmailAddress'] = Subscribers::PunycodeEmailAddress($SMSSettings['NotificationEmailAddress']);

    if (!empty($SMSSettings['NotificationEmailAddress']) && !Subscribers::IsSameEmailAddress($SMSSettings['NotificationEmailAddress'], $account->UserSettings['SMSSettings']['NotificationEmailAddress'])) {
      //the user changed the notification email address, send confirmation email
      //@see: klicktipp_account_sender_email_verification()

      // send verification email
      $QueryParameters = 'smsnotification/' . $account->uid . '/' . $SMSSettings['NotificationEmailAddress'] . '/' . substr(md5($SMSSettings['NotificationEmailAddress'] . KLICKTIPP_SENDER_HASHSECRET), 1, 5);
      $senderhash = rawurlencode(base64_encode($QueryParameters));

      $params = array();
      $params['account'] = $account;
      $params['context']['url'] = url('user/senderverify/' . $senderhash, array('absolute' => TRUE));
      $params['context']['subject'] = variable_get('klicktipp_smsnotification_subject', '');
      $params['context']['body'] = variable_get('klicktipp_smsnotification_body', '');

      if (drupal_mail('klicktipp', 'sender_verify', $SMSSettings['NotificationEmailAddress'], user_preferred_language($account), $params, variable_get('site_mail', '<EMAIL>'))) {
        drupal_set_message(t('A verification email has been sent to your SMS notification email address. You must follow the link provided in that email in order to verify your SMS notification email address.'));
      }
      else {
        drupal_set_message(t('A verification email could not be sent to your SMS notification email address.'), 'error');
      }

    }

  }

  // --- save SMS settings end ---

  // --- save user variables start ---

  if ($form_state['values']['TagAgedSubscribers'] !== $form_state['values']['TagAgedSubscribersCurrent']) {
    // the tag aged subscribers tag has been changed
    // create SINGLE VALUE tags from free entered values, returns FALSE if at least one tag could not be created, existing tags stay untouched
    $TagAgedSubscribers = Tag::CreateManualTagByTagName($account->uid, array($form_state['values']['TagAgedSubscribers']), FALSE, NULL, 0);
    if (!$TagAgedSubscribers) {
      //at least on tag could not be created, no redirect => stay in the form
      $error = array(
        '%tag' => $form_state['values']['TagAgedSubscribers'],
        '!UserID' => $account->uid,
      );
      Errors::unexpected("Error: CreateManualTagByTagName with free entry %tag in add subscriber. UserID: !UserID", $error, TRUE); //notify user
      return;
    }
    else {
      $TagAgedSubscribers = $TagAgedSubscribers[0];
      VarAgedSubscribers::SetTag($account->uid, $TagAgedSubscribers);
    }

  }
  unset($form_state['values']['TagAgedSubscribers']);
  unset($form_state['values']['TagAgedSubscribersCurrent']);


  // save email preview subscriber tag
  if (!empty($form_state['values']['TagEmailPreviewSubscriber'])) {

    // create tags from free entered values, returns FALSE if at least one tag could not be created, existing tags stay untouched
    $TagEmailPreviewSubscriber = Tag::CreateManualTagByTagName($account->uid, [$form_state['values']['TagEmailPreviewSubscriber']]);
    if (!$TagEmailPreviewSubscriber) {
      //at least on tag could not be created, no redirect => stay in the form
      $error = array(
        '%tag' => $form_state['values']['TagAgedSubscribers'],
        '!UserID' => $account->uid,
      );
      Errors::unexpected("Error: CreateManualTagByTagName with free entry %tag in add subscriber. UserID: !UserID", $error, TRUE); //notify user
      return;
    }
    else {
      VarEmailPreviewSubscriber::SetVariable($account->uid, $TagEmailPreviewSubscriber[0]);
    }

  }
  else {
    VarEmailPreviewSubscriber::DeleteVariable($account->uid);
  }
  unset($form_state['values']['TagEmailPreviewSubscriber']);

  if (isset($form_state['values']['EditFullContact'])) {
    $VarFullContact = VarFullContact::GetVariable($account->uid, []);
    $VarFullContact['apiKey'] = $form_state['values']['EditFullContact']['ApiKey'];
    $VarFullContact['likelihood'] = $form_state['values']['EditFullContact']['Likelihood'];
    VarFullContact::SetVariable($account->uid, $VarFullContact);
  }
  unset($form_state['values']['EditFullContact']);

  if (isset($form_state['values']['EditCleverbridge'])) {
    $CleverbridgeCredentials = VarCleverbridge::GetVariable($account->uid, []);
    $IPNUsername = $form_state['values']['EditCleverbridge']['IPNUsername'];
    $IPNPassword = $form_state['values']['EditCleverbridge']['IPNPassword'];
    if (
      $CleverbridgeCredentials['user'] != $IPNUsername ||
      $CleverbridgeCredentials['pw'] != $IPNPassword
    ) {
      VarCleverbridge::SetVariable($account->uid, array(
        'user' => $IPNUsername,
        'pw' => $IPNPassword,
      ));
    }
  }
  unset($form_state['values']['EditCleverbridge']);

  if (isset($form_state['values']['EE_MANUALSAVEBUTTON'])) {
    VarAppSettings::SetAppSetting(
      $account->uid,
      VarAppSettings::EE_MANUALSAVEBUTTON,
      $form_state['values']['EE_MANUALSAVEBUTTON']
    );
  }
  // --- save user variables end ---

  $isUserSpammerOriginally = (bool) $account->UserPrivileges['KnownForSpamActivity'];
  $isUserSpammerAfterUpdate = (bool) $form_state['values']['UserPrivileges']['KnownForSpamActivity'];

  if ($isUserSpammerOriginally !== $isUserSpammerAfterUpdate) {
    if ($isUserSpammerAfterUpdate) {
      UserSpamHelper::afterUserHasMarkedAsSpammer($account);
    } else {
      UserSpamHelper::afterUserSpammerMarkHasBeenRemoved($account);
    }
  }
}

function klicktipp_account_approve_account_submit($form, $form_state) {

  $account = $form['#user'];
  $said = $account->uid;
  $UserID = $form_state['clicked_button']['#name'];

  $ObjectSubAccount = Subaccount::FromID($UserID, $said);
  if (empty($ObjectSubAccount)) {
    return;
  }
  $sa = $ObjectSubAccount->GetData();
  if (empty($sa) || $sa['IsOwnAccount'] != Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_PENDING) {
    return;
  }
  // set is own account status to approved
  $sa['IsOwnAccount'] = Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_APPROVED;
  if (!Subaccount::UpdateDB($sa)) {
    return;
  }

  $subaccounts = Subaccount::GetSubAccounts($UserID);
  foreach ($subaccounts as $subaccount) {
    if ($subaccount['SubAccountID'] == $said) {
      // dismiss subaccount that needs to be updated
      continue;
    }
    if ($subaccount['IsOwnAccount'] != Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_NONE) {
      // reset pending or approved subaccounts
      $subaccount['IsOwnAccount'] = Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_NONE;
      Subaccount::UpdateDB($subaccount);
    }
    // subscribe subaccount user to just approved consultant account
    Subaccount::SubscribeSubaccountUserToConsultantAccount($UserID, $subaccount['SubAccountID']);
  }

  drupal_set_message(t("Agency Access approved as own account."));

}

function klicktipp_account_remove_approved_account_submit($form, $form_state) {

  $account = $form['#user'];
  $said = $account->uid;
  $UserID = $form_state['clicked_button']['#name'];

  $ObjectSubAccount = Subaccount::FromID($UserID, $said);
  if (empty($ObjectSubAccount)) {
    return;
  }
  $sa = $ObjectSubAccount->GetData();
  if (empty($sa) || $sa['IsOwnAccount'] != Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_APPROVED) {
    return;
  }
  // remove bam assignments and linked DrupalUserID
  $subaccounts = Subaccount::GetSubAccounts($UserID);
  foreach ($subaccounts as $subaccount) {
    if ($subaccount['SubAccountID'] != $said) {
      Subaccount::RemoveSubaccountUserFromConsultantAccount($said, $subaccount['SubAccountID']);
    }
  }
  // set is own account status to none
  $sa['IsOwnAccount'] = Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_NONE;
  Subaccount::UpdateDB($sa);
  drupal_set_message(t("Approval as own account removed."));

}

function _klicktipp_account_get_member($username) {
  // get amember data that we dont have in drupal (shareitid)
  return amember_get_member_by_username($username);
}

function klicktipp_account_get_countries() {
  Libraries::include('countries.inc');
  return klicktipp_account_get_countries_inner();
}

function _klicktipp_account_get_states($forcountry = '') {
  Libraries::include('countries.inc');
  return _klicktipp_account_get_states_inner($forcountry);
}

/**
 * Menu callback; Retrieve a JSON object containing states for country
 */
function _klicktipp_account_get_states_json($forcountry = '') {
  $states = _klicktipp_account_get_states($forcountry);

  if (count($states) == 1) {
    // the only entry would be "Select state"
    print "<option value=\"\">" . t("No states available") . "</option>";
    exit;
  }

  $s = '';
  foreach ($states as $key => $value) {
    $s .= "<option value=\"$key\">$value</option>";
  }

  print $s;
  exit;
}

/**
 * Overrides user_pass_validate() found in user.pages.inc.
 * From module: username_enumeration_prevention
 */
function klicktipp_pass_validate($form, &$form_state) {
  $name = trim($form_state['values']['name']);
  // Try to load by email.
  $users = user_load_multiple(array(), array('mail' => Subscribers::PunycodeEmailAddress($name), 'status' => '1'));
  $account = reset($users);
  if (!$account) {
    // No success, try to load by name.
    $users = user_load_multiple(array(), array(
      'name' => $name,
      'status' => '1'
    ));
    $account = reset($users);
  }

  if (isset($account->uid)) {
    form_set_value(array('#parents' => array('account')), $account, $form_state);
  }
  else {
    // Reuse the 'send email' message if a value was entered in form.
    if ($name != '') {
      drupal_set_message(t('Further instructions have been sent to your e-mail address.'));
    }
  }
}

/**
 * Overrides the user_pass_submit() found in user.pages.inc.
 * From module: username_enumeration_prevention
 */
function klicktipp_pass_submit($form, &$form_state) {
  if (isset($form_state['values']['account'])) {
    global $language;
    $account = $form_state['values']['account'];

    // Mail one time login URL and instructions using current language.
    _user_mail_notify('password_reset', $account, $language);
    watchdog('user', 'Password reset instructions mailed to %name at %email.', array(
      '%name' => $account->name,
      '%email' => Subscribers::DepunycodeEmailAddress($account->mail)
    ));
    drupal_set_message(t('Further instructions have been sent to your e-mail address.'));
  }
  $form_state['redirect'] = 'user';
}

/*
* Delete confirm form for digistore connections
* @see klicktipp_forms()
*/
function klicktipp_account_disconnect_digistore_modal_form($form, $form_state, $account, $MerchantName) {

  $EntityName = $MerchantName;
  $Title = t('Disconnect Digistore24 account');
  $Message = t('Are you sure to delete the connection to the Digistore24 account %account?', array('%account' => $MerchantName));
  $Warning = t('Note: Connected products of this account are not affected.');

  $form = _klicktipp_confirm_form_modal("klicktipp_account_disconnect_digistore_modal_form", $EntityName, $Title, TRUE, $Message, $Warning);

  $form['Entity'] = array(
    '#type' => 'hidden',
    '#value' => $MerchantName,
  );

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  return $form;

}

/**
 * Delete confirm submit
 * @see klicktipp_forms()
 */
function klicktipp_account_disconnect_digistore_modal_form_submit($form, &$form_state) {

  Libraries::include('digistore.inc');

  $MerchantName = check_plain($form_state['input']['Entity']);

  $account = $form_state['values']['account'];
  $UserSettings = $account->UserSettings;

  //remove api key in digistore account
  _klicktipp_digistore_api_http_request($account, $UserSettings['DigiStore24ApiKeys'][$MerchantName], 'unregister');

  //remove from account data
  unset($UserSettings['DigiStore24ApiKeys'][$MerchantName]);

  //remove from accoutn data
  user_save($account, array('UserSettings' => $UserSettings));

  klicktipp_set_redirect($form_state, "user/{$account->uid}/edit");

}

<?php

use App\Klicktipp\Core;
use App\Klicktipp\ProcessLog;
use App\Klicktipp\SubscriberDuplicateDetectionCookie;
use App\Klicktipp\SubscriberDuplicateDetectionEmailSimilarity;
use App\Klicktipp\SubscriberDuplicateDetectionIpAddress;

function klicktipp_kubernetes_form($form, &$form_state) {
  $queueNames = array_keys(module_invoke_all('cron_queue_info'));
  sort($queueNames);
  $form['queues'] = array(
    '#type' => 'checkboxes',
    '#title' => 'Queues in Kubernetes',
    '#default_value' => variable_get('klicktipp_queues_in_kubernetes', []),
    '#options' => array_combine($queueNames, $queueNames),
  );


  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_unsubscribe_submit',
    '#value' => t('Submit')
  );

  $form['buttons']['cancel'] = array(
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "", //frontpage
    '#theme' => 'klicktipp_cancel_button',
  );

  return $form;
}

function klicktipp_kubernetes_form_submit($form, &$form_state) {
  $queues = $form_state['values']['queues'];
  variable_set('klicktipp_queues_in_kubernetes', array_filter($queues));
}
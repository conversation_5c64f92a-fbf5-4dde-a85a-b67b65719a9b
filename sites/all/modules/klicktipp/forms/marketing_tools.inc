<?php

use App\Klicktipp\Dates;
use App\Klicktipp\Libraries;
use App\Klicktipp\MarketingTools;
use App\Klicktipp\Plugin;
use App\Klicktipp\TaggingPixel\TaggingPixelsLinkResolver;
use App\Klicktipp\ToolPluginGeneral;
use App\Klicktipp\ToolWebsiteExitLightbox;
use App\Klicktipp\ToolWebsiteFeedback;
use App\Klicktipp\ToolWebsiteOneTimeOffer;
use App\Klicktipp\ToolWebsiteSocialproof;
use App\Klicktipp\ToolWebsiteSplittest;

function klicktipp_marketing_tools_browse_form($form, $form_state, $account, $Filter = '', $PagerPage = 1, $PagerSize = 0) {

  Libraries::include('tools_countdown.inc', '/forms');

  $UserID = $account->uid;

  $form = array();

  $weight = 1;

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  // default query: all marketing tools
  $query = "SELECT * FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID AND Deleted = :NotDeleted ";
  $typequery = " AND ToolType NOT IN (:ToolType) ";
  $params = array(
    ':RelOwnerUserID' => $UserID,
    ':NotDeleted' => MarketingTools::TOOL_DELETE_STATUS_NOTDELETED,
    ':ToolType' => array(MarketingTools::TOOL_TYPE_TAGGINGPIXEL, MarketingTools::TOOL_TYPE_TEMPLATE),
  );

  //add filter for status (array key => form element (#type = select) id to used in submit, values => options of select element
  $ArrayFilters = array(
    'FilterType' => array(
      'all' => t('Show all'),
    ),
  );

  //get the last filter setting from the user session
  if (empty($Filter)) {
    $Filter = (empty($_SESSION['UserDefaults']['MarketingToolsFilter'])) ? KLICKTIPP_DEFAULTS_MARKETINGTOOLS_FILTER : $_SESSION['UserDefaults']['MarketingToolsFilter'];
  }

  $allCreateButtons = [
    'taggingpixel' => [
      '#title' => t('Action::Add::Tagging-Pixel'),
      '#value' => (new TaggingPixelsLinkResolver())->linkCreate($UserID),
    ],
    'template' => [
      '#title' => t('Action::Add::Automation Template'),
      '#value' => "tools/$UserID/template/add",
    ],
    'outbound' => [
      '#title' => t('Action::Add::Outbound'),
      '#value' => "tools/$UserID/outbound/add",
    ],
    'kajabi' => [
      '#title' => t('Action::Add::Kajabi'),
      '#value' => "tools/$UserID/kajabi/add",
    ],
    'zapier' => [
      '#title' => t('Action::Add::Zapier'),
      '#value' => "tools/$UserID/zapier/add",
    ],
    'statistic' => [
      '#title' => t('Action::Add::Report'),
      '#value' => "tools/$UserID/statistics/add",
    ],
    'countdown' => [
      '#title' => t('Action::Add::Countdown'),
      '#value' => "tools/$UserID/countdown/add",
    ],
  ];

  switch($Filter) {
    case 'taggingpixels':

      $page_title = t('Tagging Pixels');
      $nosets_message = t("You don't have any Tagging Pixels created yet.");

      $typequery = " AND ToolType = :ToolType ";
      $params[':ToolType'] = MarketingTools::TOOL_TYPE_TAGGINGPIXEL;

      $createButtons = [$allCreateButtons['taggingpixel']];

      break;
    case 'template':

      $page_title = t('Automation Templates');
      $nosets_message = t("You don't have any Automation Templates created yet.");

      $typequery = " AND ToolType = :ToolType ";
      $params[':ToolType'] = MarketingTools::TOOL_TYPE_TEMPLATE;

      $createButtons = [$allCreateButtons['template']];

      break;
    case 'outbound':

      $page_title = t('Outbound Events');
      $nosets_message = t("You don't have any outbound events created yet.");

      $typequery = " AND ToolType IN (:ToolType) ";
      $params[':ToolType'] = array(
        MarketingTools::TOOL_TYPE_KAJABI,
        MarketingTools::TOOL_TYPE_OUTBOUND,
        MarketingTools::TOOL_TYPE_ZAPIER,
        );

      $createButtons = [
        $allCreateButtons['outbound'],
        $allCreateButtons['kajabi'],
        $allCreateButtons['zapier'],
      ];

      break;
    case 'statistics':
      $page_title = t('Statistics');
      $nosets_message = t("You don't have any statistics created yet.");

      $typequery = " AND ToolType = :ToolType ";
      $params[':ToolType'] = MarketingTools::TOOL_TYPE_STATISTICS;

      $createButtons = [$allCreateButtons['statistic']];

      break;
    case 'countdowns':
      $page_title = t('Countdowns');
      $nosets_message = t("You don't have any countdowns created yet.");

      $typequery = " AND ToolType = :ToolType ";
      $params[':ToolType'] = MarketingTools::TOOL_TYPE_COUNTDOWN;

      $createButtons = [$allCreateButtons['countdown']];

      break;
    default: // all marketing tools

      $page_title = t('Tools');
      $nosets_message = t("You don't have any Tools created yet.");

      $typequery = " AND ToolType IN (:ToolType) ";

      //do not show the splittest-club tools
      $params[':ToolType'] = array(
        MarketingTools::TOOL_TYPE_TAGGINGPIXEL,
        MarketingTools::TOOL_TYPE_TEMPLATE,
        MarketingTools::TOOL_TYPE_KAJABI,
        MarketingTools::TOOL_TYPE_OUTBOUND,
        MarketingTools::TOOL_TYPE_ZAPIER,
        MarketingTools::TOOL_TYPE_STATISTICS,
        MarketingTools::TOOL_TYPE_COUNTDOWN,
        MarketingTools::TOOL_TYPE_WEBINAR,
      );

      $createButtons = array_values($allCreateButtons);

      break;
  }

  //set breadcrumb and page title
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $form['create_buttons'] = [
    '#theme' => 'klicktipp_create_buttons',
    '#buttons' => $createButtons,
    '#weight' => $weight++,
    '#prefix' => '<div class="create-buttons"><div class="clearfix"><div class="right">',
    '#suffix' => '</div></div></div>',
  ];

  //table header with sort
  $header = array(
    array(
      'data' => t('ID'),
      'field' => 'ToolID',
      'class' => array('table-col-fit table-col-right hidden-xs'),
    ),
    array(
      'data' => t('Name'),
      'field' => 'Name',
      'sort' => 'asc',
    ),
    array(
      'data' => t('Type'),
      'class' => array('table-col-fit hidden-xs'),
    ),
    array(
      'data' => t('Operations'),
      'class' => array('table-col-fit'),
    ),
  );

  $ArraySort = klicktipp_get_tablesort($header, array('Name' => 'ASC'));

  $query .= $typequery;

  //order by
  if (!empty($ArraySort)) {
    foreach ($ArraySort as $field => $dir) {
      $query .= "ORDER BY $field $dir,";
    }

    $query = trim($query, ',');
  }

  //execute query
  $result = db_query($query, $params);

  $rows = array();
  while ($tool = kt_fetch_array($result)) {

    $ObjectTool = MarketingTools::FromArray($tool);
    $edit_url = $ObjectTool->GetEditURL();

    $btn_edit = theme('klicktipp_settings_table_button', array(
      'element' => array(
        '#title' => t('Settings'),
        '#value' => $edit_url,
      )
    ));


    $btn_builder = '';
    if ( $ObjectTool->GetData('ToolType') == MarketingTools::TOOL_TYPE_STATISTICS ) {
      $btn_builder = theme('klicktipp_preview_table_button', array(
        'element' => array(
          '#title' => t('Edit'),
          '#value' => preg_replace('|\/edit$|', '/builder', $edit_url),
        )
      ));
    }

    $DisplayName = array(
      'data'=> '<a href="' . $edit_url . '"><span class="cut-text display-email has-tooltip" title="' . $tool['Name'] . '">' .
      $tool['Name'] . '</span></a>');

    $row = array(
      array(
        'data' => $tool['ToolID'],
        'class' => array('table-col-fit table-col-right hidden-xs'),
      ),
      $DisplayName,
      array(
        'data' => t(/*ignore*/MarketingTools::$DisplayToolType[$tool['ToolType']]),
        'class' => array('table-col-fit hidden-xs hidden-sm'),
      ),
      array(
        'data' => $btn_edit . $btn_builder,
        'class' => array('table-col-fit'),
      ),
    );

    $rows[] = $row;

  }

  $ToolCount = count($rows);

  //Pager
  $PagerLink = "tools/$UserID/$Filter";
  [$PagerPage, $PagerSize, $Offset] = klicktipp_validate_pager($PagerPage, $PagerSize, $ToolCount, $PagerLink);

  $Tools = array_slice($rows, $Offset, $PagerSize, TRUE);

  // --- Overview filter: @see klicktipp_overview_filter

  if (count($ArrayFilters['FilterType']) > 2) {
    //Note: the condition can be removed if more than 1 Marketing Tool Type is available

    // default value of the filter (use same key as in $ArrayFilters)
    $ArrayFilterDefaults = array(
      'FilterType' => $Filter,
    );

    //create the filter: one select element for each key in $ArrayFilters
    //adds a hidden Apply-Filter-Button (#type = submit)
    $form['Filter'] = klicktipp_overview_filter($ArrayFilters, $ArrayFilterDefaults, $weight, t('Marketing Tool type'));

  }

  // --- end overview filter

  if (empty($Tools)) {

    $form['empty'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => $nosets_message,
      '#weight' => $weight++,
    );

  }
  else {

    $form['Overview'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array(
        'header' => $header,
        'rows' => $Tools,
        'attributes' => array(
          'class' => array('table-buttons'),
          'data-e2e-id' => 'table-marketing-tools-browse'
        )
      )),
      '#weight' => $weight++,
      '#suffix' => theme('klicktipp_table_pager', array(
        'element' => array(
          'pager_page' => $PagerPage,
          //current page
          'pager_size' => $PagerSize,
          //current page size
          'pager_hide_sizes' => FALSE,
          //hide page size badges
          'pager_total' => $ToolCount,
          //total entries in the table
          'pager_max_items' => KLICKTIPP_PAGER_MAX_PAGINATION_ITEM,
          //max pager items
          'pager_link' => $PagerLink,
          //link of pager badges and items, "/<CURRENT-PAGE>/<PAGE-SIZE>" will be added
          'pager_link_query' => (empty($_GET['order'])) ? array() : array(
            'order' => $_GET['order'],
            'sort' => $_GET['sort'],
          ),
          //build an additional link query string, possibly for table sort @see l()/url()
        )
      )),
    );

  }

  return $form;
}

function klicktipp_marketing_tools_browse_form_submit($form, &$form_state) {

  if ($form_state['values']['op'] == t(/*ignore*/KLICKTIPP_BUTTON_TEXT_APPLY_FILTER)) {

    $UserID = $form_state['values']['uid'];
    $FilterData = $form_state['values']['FilterType'];

    $_SESSION['UserDefaults']['MarketingToolsFilter'] = $FilterData;

    klicktipp_set_redirect($form_state, "tools/$UserID/$FilterData");

  }

}

function klicktipp_marketing_tools_overview_form($form, $form_state, $account, $PagerPage = 1, $PagerSize = 0) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Marketing Tools');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $MarketingTools = array(
    MarketingTools::TOOL_TYPE_WEBSITE_EXITLIGHTBOX,
    MarketingTools::TOOL_TYPE_WEBSITE_FEEDBACK,
    MarketingTools::TOOL_TYPE_WEBSITE_ONETIMEOFFER,
    MarketingTools::TOOL_TYPE_WEBSITE_SOCIALPROOF,
    MarketingTools::TOOL_TYPE_WEBSITE_SPLITTEST,
    MarketingTools::TOOL_TYPE_PLUGIN,
  );

  $tool_count = db_query("SELECT COUNT(*) FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID AND ToolType IN (:ToolTypes) AND Deleted = :NotDeleted", array(
    ':RelOwnerUserID' => $UserID,
    ':ToolTypes' => $MarketingTools,
    ':NotDeleted' => MarketingTools::TOOL_DELETE_STATUS_NOTDELETED,
  ))->fetchField();

  $createButtons = [
    [
      '#title' => t('Action::Add::Marketing Tool'),
      '#value' => "marketingtools/$UserID/create",
    ],
  ];

  if ( empty($tool_count) ) {

    $form['create_buttons'] = [
      '#theme' => 'klicktipp_create_buttons',
      '#buttons' => $createButtons,
      '#prefix' => '<div class="create-buttons"><div class="clearfix"><div class="right">',
      '#suffix' => '</div></div></div>',
    ];

    $form['empty'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("You don't have any marketing tools created yet."),
    );

    return $form;

  }

  $ToolTypes = klicktipp_get_marketing_tools_by_permission($account);

  //retrieve marketing tools by filter

  $EditLinks = array();
  $OverviewLinks = array();

  //get the filter setting from the user session
  //$Filter contains either a single ToolType or comma-separated ToolTypes (if a group is selected)
  $Filter = $_SESSION['UserDefaults']['FilterMarketingToolsAndPlugins'];

  if ( empty($Filter) ) {
    $Filter = implode(',', array_keys($ToolTypes));
  }

  $Types = [];
  $PluginIDs = [];
  foreach(explode(',', $Filter) as $key) {
    if (is_numeric($key)) {
      // splittest club tools have numeric keys
      $Types[] = $key;
      if (!in_array('', $PluginIDs)) {
        $PluginIDs[] = '';
      }
    }
    else {
      // plugins have string keys
      $PluginIDs[] = $key;
      if (!in_array(MarketingTools::TOOL_TYPE_PLUGIN, $Types)) {
        $Types[] = MarketingTools::TOOL_TYPE_PLUGIN;
      }
    }
  }

  $query = "SELECT * FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID " .
    " AND ToolType IN (:ToolTypes) AND VarcharIndexed IN (:PluginIDs) AND Deleted = :NotDeleted";

  $result = db_query($query, array(
    ':RelOwnerUserID' => $UserID,
    ':ToolTypes' => $Types,
    ':PluginIDs' => $PluginIDs,
    ':NotDeleted' => MarketingTools::TOOL_DELETE_STATUS_NOTDELETED,
  ));

  $ArrayMarketingTools = [];
  while ( $DBArray = kt_fetch_array($result) ) {

    $tool = MarketingTools::FromArray($DBArray);

    if ( $tool ) {

      $ToolData = $tool->GetData();

      //add additional (temporary) fields for table sort
      if ( $Filter == MarketingTools::TOOL_TYPE_WEBSITE_SPLITTEST ) {

        $visitors = 0;
        $actions = 0;
        $conversion = 0;

        foreach ( $ToolData['variants'] as $variant ) {
          $visitors += $variant['uniques'];
          $actions += $variant['actions'];
        }

        if ( $visitors > 0 ) {
          $conversion = klicktipp_number_format($actions / $visitors * 100, 2);
        }

        //support table sort for this value
        //NOTE: $tools is a reference @see foreach
        $ToolData['visitors'] = $visitors;
        $ToolData['actions'] = $actions;
        $ToolData['conversion'] = $conversion;

      }
      elseif ( $Filter == MarketingTools::TOOL_TYPE_WEBSITE_FEEDBACK ) {

        $fb = $tool->ReadFeedbackLog();
        $ToolData['feedbacks'] = count($fb);

      }

      $ArrayMarketingTools[] = $ToolData;
      $EditLinks[$DBArray['ToolID']] = $tool->GetEditURL();
      $OverviewLinks[$DBArray['ToolID']] = $tool->GetOverviewURL();

    }

  }

  // --- END include splittest club conversion tools from {marketing_tools}

  $header = array(
    array(
      'data' => t('ID'),
      'field' => 'ToolID',
      'class' => array('table-col-fit table-col-right hidden-xs'),
      'sort' => 'desc',
    ),
    array(
      'data' => t('Name'),
      'field' => 'Name',
    ),
    array(
      'data' => t('Type'),
      'field' => 'ToolType',
      'class' => array('table-col-fit hidden-xs'),
    ),

  );

  if ( $Filter === (string) MarketingTools::TOOL_TYPE_WEBSITE_SPLITTEST ) {

    $header[] = array(
      'data' => t('Visitors'),
      'field' => 'visitors', //temporary field in this form
      'class' => array('table-col-fit table-col-right'),
    );

    $header[] = array(
      'data' => t('Actions'),
      'field' => 'actions', //temporary field in this form
      'class' => array('table-col-fit table-col-right'),
    );

    $header[] = array(
      'data' => t('Conversion'),
      'field' => 'conversion', //temporary field in this form
      'class' => array('table-col-fit table-col-right'),
    );

  }
  elseif ( $Filter === (string) MarketingTools::TOOL_TYPE_WEBSITE_FEEDBACK ) {

    $header[] = array(
      'data' => t('Feedbacks'),
      'field' => 'feedbacks', //temporary field in this form
      'class' => array('table-col-fit table-col-right'),
    );

  }
  elseif ( $Filter === (string) MarketingTools::TOOL_TYPE_WEBSITE_SOCIALPROOF ) {

    $header[] = array(
      'data' => t('Counter value'),
      'field' => 'socialproofcounter', //temporary field in this form
      'class' => array('table-col-fit table-col-right'),
    );

  }

  $header[] = array(
    'data' => t('Created on'),
    'field' => 'created',
    'class' => array('table-col-fit table-col-right'),
  );

  $header[] = array(
    'data' => t('Operations'),
    'class' => array('table-col-fit'),
  );

  // table sort

  $ArraySort = klicktipp_get_tablesort($header, array());

  if ( !empty($ArraySort['Name']) ) {
    klicktipp_uasort($ArrayMarketingTools, $ArraySort, 'strnatcasecmp');
  }
  else {
    klicktipp_uasort($ArrayMarketingTools, $ArraySort);
  }

  // --- Form

  $form = array();

  $form['#pre_render'][] = 'klicktipp_marketing_tools_overview_pre_render';

  $weight = 1;

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  $form['create_buttons'] = [
    '#theme' => 'klicktipp_create_buttons',
    '#buttons' => $createButtons,
    '#weight' => $weight++,
    '#prefix' => '<div class="create-buttons"><div class="clearfix"><div class="right">',
    '#suffix' => '</div></div></div>',
  ];

  $rows = array();
  foreach ($ArrayMarketingTools as $key => $tools ) {

    $ToolID = $tools['ToolID'];
    $type = $tools['ToolType'];
    $edit = $EditLinks[$tools['ToolID']];
    $name = $tools['Name'];
    $created = Dates::formatDate(Dates::FORMAT_DMY, (int) $tools['created']);
    if ($type == MarketingTools::TOOL_TYPE_PLUGIN) {
      $display_type = Plugin::get_plugin_var_byid($tools['VarcharIndexed'], 'PluginName');
      $overview = $EditLinks[$tools['ToolID']];
      $nameLink = $tools['terminated'] ? $edit : $overview;
    }
    else {
      $display_type = t(/*ignore*/MarketingTools::$DisplayToolType[$type]);
      $nameLink = $OverviewLinks[$tools['ToolID']];
    }

    // table buttons
    $btn_edit = theme('klicktipp_edit_table_button', array('element' => array(
      '#title' => t('Edit'),
      '#value' => $edit,
    )));

    $DisplayName = array(
    'data'=> '<a href="' . $nameLink . '"><span class="cut-text display-email has-tooltip" title="' . $name . '">' .
      $name . '</span></a>');

    $row = array(
      array(
        'data' => $ToolID,
        'class' => array('table-col-fit table-col-right hidden-xs'),
      ),
      $DisplayName,
      array(
        'data' => $display_type,
        'class' => array('table-col-fit hidden-xs hidden-sm'),
      ),

    );

    if ( $Filter === (string) MarketingTools::TOOL_TYPE_WEBSITE_SPLITTEST ) {

      $row[] = array(
        'data' => $tools['visitors'],
        'class' => array('table-col-fit table-col-right'),
      );

      $row[] = array(
        'data' => $tools['actions'],
        'class' => array('table-col-fit table-col-right'),
      );

      $row[] = array(
        'data' => ($tools['visitors'] > 0) ? $tools['conversion'] . '&nbsp;%' : t('N/A'),
        'class' => array('table-col-fit table-col-right'),
      );

    }
    elseif ( $Filter === (string) MarketingTools::TOOL_TYPE_WEBSITE_FEEDBACK ) {

      $row[] = array(
        'data' => $tools['feedbacks'],
        'class' => array('table-col-fit table-col-right'),
      );

    }
    elseif ( $Filter === (string) MarketingTools::TOOL_TYPE_WEBSITE_SOCIALPROOF ) {

      $row[] = array(
        'data' => $tools['socialproofcounter'] ? number_format($tools['socialproofcounter'], 0, ',', '.') : '0',
        'class' => array('table-col-fit table-col-right'),
      );

    }

    $row[] = array(
      'data' => $created,
      'class' => array('table-col-fit table-col-right'),
    );

    $row[] = array(
      'data' => $btn_edit,
      'class' => array('table-col-fit table-col-right'),
    );

    $rows[] = $row;

  }

  $MarketingToolCount = count($rows);

  //Pager
  $PagerLink = "marketingtools/$UserID";
  [$PagerPage, $PagerSize, $Offset] = klicktipp_validate_pager($PagerPage, $PagerSize, $MarketingToolCount, $PagerLink);

  $Tools = array_slice($rows, $Offset, $PagerSize, TRUE);

  //group accessible listbuildings @see klicktipp_get_listbuildings_by_permission()
  $groups = array();
  foreach ( $ToolTypes as $type => $lb ) {

    $group = $lb['group'];
    if ( !isset($groups[$group]) ) {
      $groups[$group] = array();
    }

    $groups[$group][$type] = $lb['title'];

  }

  $all = implode(',', array_keys($ToolTypes));
  $ToolTypeOptions = array( $all => t('Show all'));
  $ToolTypeOptGroups = array($all);
  foreach ( $groups as $group => $members ) {

    $ids = implode(',', array_keys($members)); //get the ids of the members and use them as group key
    $ToolTypeOptions[$ids] = $group; //add optgroup
    $ToolTypeOptions += $members; //append members
    $ToolTypeOptGroups[] = $ids; //mark as option group @see theme_klicktipp_select_selectable_optgroup()

  }

  $form['Filter'] = array(
    '#type' => 'select',
    '#theme' => 'klicktipp_select_selectable_optgroup',
    '#title' => t('Marketing Tool type'),
    '#default_value' => $Filter,
    '#options' => $ToolTypeOptions,
    '#optgroups' => $ToolTypeOptGroups,
    '#weight' => $weight++,
    '#parents' => array('Filter'),
    '#attributes' => array(
      'data-event-change' => 'js_filter_onchange'
    ),
  );

  $form['FilterSubmit'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['FilterSubmit']['Submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_APPLY_FILTER),
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_filter_onload'
    ),
  );

  // --- end overview filter

  if ( empty($rows) ) {

    $form['empty'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("No Marketing Tools found with current filter settings."),
      '#weight' => $weight++,
    );

  }
  else {

    $form['Overview'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array(
        'header' => $header,
        'rows' => $Tools,
        'attributes' => array(
          'class' => array('table-buttons'),
          'data-e2e-id' => 'table-marketing-tool-overview'
        )
      )),
      '#weight' => $weight++,
      '#suffix' => theme('klicktipp_table_pager', array(
        'element' => array(
          'pager_page' => $PagerPage,
          //current page
          'pager_size' => $PagerSize,
          //current page size
          'pager_hide_sizes' => FALSE,
          //hide page size badges
          'pager_total' => $MarketingToolCount,
          //total entries in the table
          'pager_max_items' => KLICKTIPP_PAGER_MAX_PAGINATION_ITEM,
          //max pager items
          'pager_link' => $PagerLink,
          //link of pager badges and items, "/<CURRENT-PAGE>/<PAGE-SIZE>" will be added
          'pager_link_query' => (empty($_GET['order'])) ? array() : array(
            'order' => $_GET['order'],
            'sort' => $_GET['sort'],
          ),
          //build an additional link query string, possibly for table sort @see l()/url()
        )
      )),
    );

  }

  return $form;


}

function klicktipp_marketing_tools_overview_pre_render($form) {

  $script = "

    js_filter_onload = function(element, args) {
      //if javascript is available, the filter submit button will be hidden
      element.hide();
    }

    js_filter_onchange = function(element, args) {
      //reload the page if the filter changed
      element.parents('form')[0].submit();
    }

  ";

  drupal_add_js($script, array('type' => 'inline', 'scope' => 'header'));

  return $form;

}

function klicktipp_marketing_tools_overview_form_submit($form, &$form_state) {

  $UserID = $form_state['values']['account']->uid;

  $Filter = (empty($form_state['values']['Filter'])) ? 0 : $form_state['values']['Filter'];

  $_SESSION['UserDefaults']['FilterMarketingToolsAndPlugins'] = $Filter;

  klicktipp_set_redirect($form_state, "marketingtools/$UserID");

}

function klicktipp_marketing_tools_create_form($form, &$form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Create Marketing Tool');
  klicktipp_set_title('');
  klicktipp_set_breadcrumb(array(l(t('Marketing Tools'), "marketingtools/$UserID")), $page_title);

  $ToolTypes = klicktipp_get_marketing_tools_by_permission($account, TRUE);

  $form = array();

  $weight = 1;

  foreach ( $ToolTypes as $ToolType => $lb ) {

    $group = $lb['group'];

    if ( empty($lb['create']) ) {
      continue; // disabled or deprecated plugins or splittest-club conversion tools
    }

    if ( !isset($form[$group]) ) {

      $form[$group] = array(
        '#theme' => 'klicktipp_icon_grid',
        '#title' => $group,
        '#description' => '',
      );

    }

    $form[$group][$ToolType] = array(
      '#theme' => $lb['theme'],
      '#value' => $lb['create'],
      '#icon' => $lb['icon'],
      '#image' => empty($lb['image']) ? '' : $lb['image'],
      '#title' => $lb['title'],
      '#quickhelp' => $lb['quickhelp'],
      '#weight' => $weight++,
      '#attributes' => $lb['attributes'],
    );
    if (!empty($lb['prefix'])) {
      $form[$group][$ToolType]['#prefix'] = $lb['prefix'];
    }
    if (!empty($lb['br'])) {
      $form[$group][$ToolType]['#suffix'] = '<br />';
    }

  }

  return $form;

}

function klicktipp_get_marketing_tools_by_permission ($account, $forcreate = FALSE) {

  $UserID = $account->uid;

  $MarketingTools = [];

  if (klicktipp_feature_access($account, 'access splittest-club')) {
    $MarketingTools = [
      MarketingTools::TOOL_TYPE_WEBSITE_SPLITTEST => [
        'theme' => 'klicktipp_icon',
        'title' => t('Splittest'),
        'group' => t('Splittest-Club Conversion Tools'),
        'icon' => 'stc-icon-splittest',
        'create' => ToolWebsiteSplittest::GetAddURL($UserID),
      ],
      MarketingTools::TOOL_TYPE_WEBSITE_EXITLIGHTBOX => [
        'theme' => 'klicktipp_icon',
        'title' => t('Exit-Lightbox'),
        'group' => t('Splittest-Club Conversion Tools'),
        'icon' => 'stc-icon-exitlightbox',
        'create' => ToolWebsiteExitLightbox::GetAddURL($UserID),
      ],
      MarketingTools::TOOL_TYPE_WEBSITE_FEEDBACK => [
        'theme' => 'klicktipp_icon',
        'title' => t('Feedback Form'),
        'group' => t('Splittest-Club Conversion Tools'),
        'icon' => 'stc-icon-feedback',
        'create' => ToolWebsiteFeedback::GetAddURL($UserID),
      ],
      MarketingTools::TOOL_TYPE_WEBSITE_ONETIMEOFFER => [
        'theme' => 'klicktipp_icon',
        'title' => t('One Time Offer'),
        'group' => t('Splittest-Club Conversion Tools'),
        'icon' => 'stc-icon-onetimeoffer',
        'create' => ToolWebsiteOneTimeOffer::GetAddURL($UserID),
      ],
      MarketingTools::TOOL_TYPE_WEBSITE_SOCIALPROOF => [
        'theme' => 'klicktipp_icon',
        'title' => t('Social Proof Counter'),
        'group' => t('Splittest-Club Conversion Tools'),
        'icon' => 'stc-icon-socialproof',
        'create' => ToolWebsiteSocialproof::GetAddURL($UserID),
      ],
    ];
  }

  //TODO PLUGIN
  $first = TRUE;
  $plugins = Plugin::get_plugins_by_permission($account, $forcreate);
  foreach (array_keys($plugins) as $pluginid) {
    $plugin = Plugin::get_plugin($pluginid);

    //TODO only once? or individual?
    $icon_class = 'kt-icon-plugin-default';

    $style = <<<EOL
<style>
.$icon_class {
  background-color: transparent
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-position: 0px 0px;
}

@media (min-width: 1200px) {

.$icon_class {
  background-image: url("/misc/plugins/background.png");
  width: 204px;
  height: 204px;
}

.$icon_class:hover {
  background-position: 0px -204px;
}

.$icon_class:active {
  background-position: 0px -408px;
}

}

@media (min-width: 993px) and (max-width: 1199px) {

.$icon_class {
  background-image: url("/misc/plugins/background_medium.png");
  width: 164px;
  height: 164px;
}

.$icon_class:hover {
  background-position: 0px -164px;
}

.$icon_class:active {
  background-position: 0px -328px;
}

}

@media (max-width: 992px) {

.$icon_class {
  background-image: url("/misc/plugins/background_small.png");
  width: 94px;
  height: 94px;
}

.$icon_class:hover {
  background-position: 0px -94px;
}

.$icon_class:active {
  background-position: 0px -188px;
}

}

.kt-icon-plugin-image {
  display: table-cell;
  vertical-align: middle;
  padding: 15px;
}

</style>
EOL;

    $create = $plugin['Create'];
    $MarketingTools[$pluginid] = [
      'theme' => 'klicktipp_plugin_icon',
      'title' => $plugin['PluginName'],
      'group' => empty($create['group']) ? t('Plugins') : $create['group'],
      'icon' => 'kt-icon-plugin-default',
      'create' => ToolPluginGeneral::GetAddURL($UserID, $pluginid),
      'image' => empty($create['image']) ? '' : $create['image'],
      'quickhelp' => 'plugin-create-'.$plugin['PluginID'],
      'br' => $create['br'],
    ];
    if ($first) {
      //TODO add this only once unless we know that we do individual styles
      $first = FALSE;
      $MarketingTools[$pluginid]['prefix'] = $style;
    }
  }

  return $MarketingTools;

}

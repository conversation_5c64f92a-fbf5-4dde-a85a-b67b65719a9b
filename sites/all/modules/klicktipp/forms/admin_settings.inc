<?php

use App\Klicktipp\ApiMillionVerifier;
use App\Klicktipp\ApiMillionVerifierDrupalForm;
use App\Klicktipp\AutoresponderQueue;
use App\Klicktipp\Beamer;
use App\Klicktipp\BounceEngine;
use App\Klicktipp\Cache;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsProcessFlow;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DatabaseTableCRUD;
use App\Klicktipp\Dates;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\Errors;
use App\Klicktipp\GoogleWebRiskBlacklist;
use App\Klicktipp\LandingPage\LandingPage;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\NewsletterQueue;
use App\Klicktipp\Personalization;
use App\Klicktipp\PreGeneration\PreGenerationHandler;
use App\Klicktipp\ProcessLog;
use App\Klicktipp\Reference;
use App\Klicktipp\SendEngine;
use App\Klicktipp\Settings;
use App\Klicktipp\ShortenerBlacklist;
use App\Klicktipp\Signatures;
use App\Klicktipp\SpamhausBlacklist;
use App\Klicktipp\Subaccount;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\TransactionalQueue;
use App\Klicktipp\UriblBlacklist;
use App\Klicktipp\UserGroups;
use App\Klicktipp\VarAdditionalAddresses;
use App\Klicktipp\VarCustomerNotes;
use App\Klicktipp\VarMillionVerifierUsage;
use App\Klicktipp\VarMillionVerifierUsageDrupalForm;
use App\Klicktipp\VarSegment;
use App\Klicktipp\Watchdog;
use Google\Cloud\WebRisk\V1\WebRiskServiceClient;

define('KLICKTIPP_GROUPS_DELETE_SELECTED_BUTTON_TEXT', 'Delete seleted groups');
define('KLICKTIPP_GROUPS_DELETE_BUTTON_TEXT', 'Delete');
define('KLICKTIPP_GROUPS_SAVE_BUTTON_TEXT', 'Save');
define('KLICKTIPP_GROUPS_SAVE_CATEGORY_NAMES_BUTTON_TEXT', 'Save category names');

define('KLICKTIPP_HATER_DELETE_SELECTED_BUTTON_TEXT', 'Delete seleted ids');
define('KLICKTIPP_HATER_SEARCH_BUTTON_TEXT', 'Search E-Mail');
define('KLICKTIPP_HATER_SEARCH_RESET_BUTTON_TEXT', 'Reset search');

define('KLICKTIPP_QUICKHELP_SAVE_BUTTON_TEXT', 'Save');
define('KLICKTIPP_QUICKHELP_CANCEL_BUTTON_TEXT', 'Cancel');

define('KLICKTIPP_SENDERSCORE_SAVE_BUTTON_TEXT', 'Save');
define('KLICKTIPP_SENDERSCORE_CANCEL_BUTTON_TEXT', 'Cancel');

define('KLICKTIPP_RESET_COCKPIT_COOKIE', 'Clear Cockpit Cookie');

define('KLICKTIPP_SAVE_NOTES', 'Save');

define('KLICKTIPP_HELPBLOCK_BUTTON_TEXT_IMPORT_TAGS', 'Import');
define('KLICKTIPP_HELPBLOCK_BUTTON_TEXT_EXPORT_TAGS', 'Export');

/**
 * Configure klicktipp handling.
 */
function klicktipp_settings($form, &$form_state) {
  Libraries::include('s3_filemanager.inc'); //S3 settings
  Libraries::include('contact.inc', '/forms'); //S3 settings


  $form['klicktipp_supportmail'] = array(
    '#type' => 'fieldset',
    '#title' => 'Support email',
  );
  $form['klicktipp_supportmail']['klicktipp_support_email_address'] = array(
    '#type' => 'textfield',
    '#title' => 'Support address in helpscout',
    '#default_value' => variable_get('klicktipp_support_email_address', ''),
  );

  $form['klicktipp_theme'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp theme',
  );

  $form['klicktipp_theme'][KLICKTIPP_THEME_BOOTSTRAP_CDN_JAVASCRIPT] = array(
    '#type' => 'textfield',
    '#title' => 'Bootstrap CDN for JavaScript',
    '#default_value' => variable_get(KLICKTIPP_THEME_BOOTSTRAP_CDN_JAVASCRIPT, 'https://netdna.bootstrapcdn.com/bootstrap/3.0.3/js/bootstrap.min.js'),
    '#description' => 'URL to the Bootstrap CDN for the JavaScript file.<br />' .
      '<strong>Also upload new versions to S3 with the same path as a fallback.</strong>'.
      '<br />Example: https://netdna.bootstrapcdn.com/bootstrap/3.0.3/js/bootstrap.min.js<br />'.
      'https://assets.klicktipp.com/bootstrap/3.0.3/js/bootstrap.min.js',
  );

  $form['klicktipp_theme'][KLICKTIPP_THEME_BOOTSTRAP_CDN_CSS] = array(
    '#type' => 'textfield',
    '#title' => 'Bootstrap CDN for CSS',
    '#default_value' => variable_get(KLICKTIPP_THEME_BOOTSTRAP_CDN_CSS, 'https://netdna.bootstrapcdn.com/bootstrap/3.0.3/css/bootstrap.min.css'),
    '#description' => 'URL to the Bootstrap CDN for the CSS file.<br />'.
      '<strong>Also upload new versions to S3 with the same path as a fallback.</strong>'.
      '<br />Example: https://netdna.bootstrapcdn.com/bootstrap/3.0.3/css/bootstrap.min.css<br />'.
      'https://assets.klicktipp.com/bootstrap/3.0.3/css/bootstrap.min.css',
  );

  $form['klicktipp_theme'][KLICKTIPP_THEME_CKEDITOR_CDN_EDITOR] = array(
    '#type' => 'textfield',
    '#title' => 'CKEditor CDN',
    '#default_value' => variable_get(KLICKTIPP_THEME_CKEDITOR_CDN_EDITOR, 'https://cdn.ckeditor.com/4.4.2/full/ckeditor.js'),
    '#description' => 'URL to the CKEditor CDN.<br />'.
      '<strong>Also upload new versions to S3 with the same path as a fallback.</strong>'.
      '<br />Example: https://cdn.ckeditor.com/4.4.2/full/ckeditor.js<br />'.
      'https://assets.klicktipp.com/ckeditor442-full/ckeditor.js',
  );

  $form['klicktipp_theme'][KLICKTIPP_THEME_CKEDITOR_CDN_JQUERY_ADAPTER] = array(
    '#type' => 'textfield',
    '#title' => 'CKEditor jQuery adapter CDN',
    '#default_value' => variable_get(KLICKTIPP_THEME_CKEDITOR_CDN_JQUERY_ADAPTER, 'https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.4.2/adapters/jquery.js'),
    '#description' => 'URL to the CKEditor jQuery adapter CDN.<br />'.
      '<strong>Also upload new versions to S3 with the same path as a fallback.</strong>'.
      '<br />Example: https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.4.2/adapters/jquery.js<br />'.
      'https://klicktipp.s3.amazonaws.com/ckeditor/4.4.2/adapters/jquery.js',
  );

  $form['klicktipp_theme']['klicktipp_theme_spectrum_colorpicker_cdn'] = array(
    '#type' => 'textfield',
    '#title' => 'Spectrum colorpicker CDN',
    '#default_value' => Settings::get('klicktipp_theme_spectrum_colorpicker_cdn'),
    '#description' => 'URL to the Spectrum colorpicker CDN '.
      '(without filename, "/spectrum.min.css" and "/spectrum.min.js" will be appended).<br />'.
      '<strong>Also upload new versions to S3 with the same path as a fallback.</strong>'.
      '<br />Example: https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.7.1<br />'.
      'https://assets.klicktipp.com/spectrum/1.7.1',
  );

  $form['klicktipp_theme']['klicktipp_groups_category_display_info'] = array(
    '#type' => 'checkbox',
    '#title' => 'Show account info',
    '#default_value' => variable_get('klicktipp_groups_category_display_info', 1),
    '#description' => 'Display account info (Stars + upsell link) next to the menu.',
  );

  $form['klicktipp_theme'][KLICKTIPP_THEME_COLORS] = array(
    '#type' => 'textarea',
    '#title' => 'Klicktipp colors',
    '#default_value' => implode(',', Settings::get(KLICKTIPP_THEME_COLORS)),
    '#rows' => 3,
    '#description' => 'Enter a comma separated list of hex color codes (with leading #). '.
      'Example: "#efc224,#ee994d".<br />'.
      'The colors are mainly used in charts. The first chart item gets color[0] and so on.',
  );

  $form['klicktipp_theme']['klicktipp_theme_google_header_user_script'] = array(
    '#type' => 'textarea',
    '#title' => 'Google Tag Manager Script Header',
    '#default_value' => Settings::get('klicktipp_theme_google_header_user_script'),
    '#rows' => 1,
    '#description' => 'Google Tag Manager script that identifies the logged in user. The placeholder %UserID% will be replaced.',
  );

  $form['klicktipp_theme']['klicktipp_theme_google_header_script'] = array(
    '#type' => 'textarea',
    '#title' => 'Google Tag Manager Script Header',
    '#default_value' => Settings::get('klicktipp_theme_google_header_script'),
    '#rows' => 3,
    '#description' => 'Google Tag Manager script that needs to be included inside the header.',
  );

  $form['klicktipp_theme'][KLICKTIPP_THEME_GOOGLE_SCRIPTS] = array(
    '#type' => 'textarea',
    '#title' => 'Google Tag Manager Script Body',
    '#default_value' => Settings::get(KLICKTIPP_THEME_GOOGLE_SCRIPTS),
    '#rows' => 3,
    '#description' => 'Some Google scripts like the Tag Manager need to be included right after the body tag not wrapped in any elements.',
  );

  $menues = menu_get_menus();

  $menuname = isset($menues[KT_MENU_MAIN]) ? $menues[KT_MENU_MAIN] : KT_MENU_MAIN . ' missing in router table';
  $form['klicktipp_theme']['klicktipp_theme_main_menu_authenticated'] = array(
    '#type' => 'select',
    '#title' => 'Klicktipp authenticated menu',
    '#options' => $menues,
    '#default_value' => variable_get('klicktipp_theme_main_menu_authenticated', 'menu-main2'),
    '#description' => "Select the menu to display for authenticated users. Latest is $menuname."
  );

  $form['klicktipp_theme'][KLICKTIPP_THEME_FOOTER_MENU] = array(
    '#type' => 'select',
    '#title' => 'Klicktipp Footer menu',
    '#options' => menu_get_menus(),
    '#default_value' => variable_get(KLICKTIPP_THEME_FOOTER_MENU, 'menu-impressum'),
    '#description' => 'Select the menu to display as the footer menu.',
  );

  $form['klicktipp_theme'][KLICKTIPP_THEME_WHITELABEL_MENU] = array(
    '#type' => 'select',
    '#title' => 'Klicktipp Whitelabel menu',
    '#options' => menu_get_menus(),
    '#default_value' => variable_get(KLICKTIPP_THEME_WHITELABEL_MENU, 'menu-impressum'),
    '#description' => 'Select the menu to display in the Whitelabel footer.',
  );

  $Vocabularies = taxonomy_get_vocabularies();
  $VocOptions = array(0 => 'Please select');
  foreach ($Vocabularies as $voc) {
    $VocOptions[$voc->vid] = $voc->name;
  }

  $form['klicktipp_theme'][KLICKTIPP_HELP_VOCABULARY_ID] = array(
    '#type' => 'select',
    '#title' => 'Klicktipp help tags',
    '#options' => $VocOptions,
    '#default_value' => variable_get(KLICKTIPP_HELP_VOCABULARY_ID, 0),
    '#description' => 'Select the taxonomy vocabulary containing the Klicktipp help tags.',
  );

  $form['klicktipp_redirects'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp content',
  );

  $form['klicktipp_redirects']['site_frontpage_authenticated'] = array(
    '#type' => 'textfield',
    '#title' => 'Frontpage authenticated users',
    '#default_value' => variable_get('site_frontpage_authenticated', 'user/me'),
  );

  $form['klicktipp_redirects']['site_frontpage_unauthenticated'] = array(
    '#type' => 'textfield',
    '#title' => 'Frontpage unauthenticated users',
    '#default_value' => variable_get('site_frontpage_unauthenticated', ''),
  );

  $form['klicktipp_redirects'][KLICKTIPP_CONTENT_INCLUDE_FRONTPAGE_WELCOME] = array(
    '#type' => 'textfield',
    '#title' => 'New customers frontpage welcome',
    '#default_value' => variable_get(KLICKTIPP_CONTENT_INCLUDE_FRONTPAGE_WELCOME, ''),
    '#description' => 'Show a welcome message/video to new customers that have no contacts or no sent campaigns yet. (Insert path to the content_include file)',
  );

  $form['klicktipp_redirects'][KLICKTIPP_CONTENT_INCLUDE_PARTNERINFO] = array(
    '#type' => 'textfield',
    '#title' => 'Partner information',
    '#default_value' => variable_get(KLICKTIPP_CONTENT_INCLUDE_PARTNERINFO, ''),
    '#description' => 'OLD: AVOID TO USE: Show the partner information. (Insert path to the content_include file)',
  );

  $form['klicktipp_redirects']['partner-program-marketing-page-redirect'] = array(
    '#type' => 'textfield',
    '#title' => 'Partner programm redirect',
    '#default_value' => Settings::get('partner-program-marketing-page-redirect'),
    '#description' => 'Redirect to the partnerprogramm marketing page. This already handles the access check.',
  );

  $form['klicktipp_redirects'][KLICKTIPP_CONTENT_INCLUDE_ADD_CONTACT_PERMISSION_PAGE] = array(
    '#type' => 'textfield',
    '#title' => 'Add contacts (permission page when no access)',
    '#default_value' => variable_get(KLICKTIPP_CONTENT_INCLUDE_ADD_CONTACT_PERMISSION_PAGE, ''),
    '#description' => 'The permission to add contacts requires a review by the support. Show instructions. (Insert path to the content_include file)',
  );

  //KLICKTIPP_CONTENT_INCLUDE_DATA_PROCESSING_ORDER
  $form['klicktipp_redirects'][KLICKTIPP_CONTENT_INCLUDE_DATA_PROCESSING_ORDER] = array(
    '#type' => 'textfield',
    '#title' => 'Data processing order',
    '#default_value' => variable_get(KLICKTIPP_CONTENT_INCLUDE_DATA_PROCESSING_ORDER, ''),
    '#description' => 'Show the order page for the Klick-Tipp data processing order. (Insert path to the content_include file)',
  );

  $form['klicktipp_redirects']['klicktipp_content_include_automation_template_import'] = array(
    '#type' => 'textfield',
    '#title' => 'Automation Template Import Landingpage',
    '#default_value' => variable_get('klicktipp_content_include_automation_template_import', ''),
    '#description' => 'Show the landing page for the Klick-Tipp automation import. (Insert path to the content_include file)',
  );

  $form['klicktipp_redirects']['klicktipp_content_include_change_password'] = array(
    '#type' => 'textfield',
    '#title' => 'Change Password Landingpage',
    '#default_value' => variable_get('klicktipp_content_include_change_password', ''),
    '#description' => 'Show the landing page for the Klick-Tipp password change. (Insert path to the content_include file)',
  );

  $form['klicktipp_resources'] = array(
    '#type' => 'fieldset',
    '#title' => 'Resources',
  );

  $form['klicktipp_resources'][S3_SETTINGS_KEY] = array(
    '#type' => 'textfield',
    '#title' => 'S3 Key',
    '#default_value' => _s3_get_setting(S3_SETTINGS_KEY),
  );

  $form['klicktipp_resources'][S3_SETTINGS_SECRET] = array(
    '#type' => 'textfield',
    '#title' => 'S3 Secret',
    '#default_value' => _s3_get_setting(S3_SETTINGS_SECRET),
  );

  // CKFinder
  $form['klicktipp_resources'][KLICKTIPP_RESOURCE_CKFINDER_LICENSENAME] = array(
    '#type' => 'textfield',
    '#title' => 'CKFinder License Name',
    '#description' => 'Leave empty for fully functional demo version',
    '#default_value' => variable_get(KLICKTIPP_RESOURCE_CKFINDER_LICENSENAME, ''),
  );
  $form['klicktipp_resources'][KLICKTIPP_RESOURCE_CKFINDER_LICENSEKEY] = array(
    '#type' => 'textfield',
    '#title' => 'CKFinder License Key',
    '#description' => 'Leave empty for fully functional demo version',
    '#default_value' => variable_get(KLICKTIPP_RESOURCE_CKFINDER_LICENSEKEY, ''),
  );
  $form['klicktipp_resources'][KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_IMAGE_TYPES] = array(
    '#type' => 'textfield',
    '#title' => 'CKFinder allowed image types',
    '#default_value' => variable_get(KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_IMAGE_TYPES, 'gif,jpeg,jpg,png'),
    '#description' => 'Enter a comma separated list of image file extentions, the user is allowed to upload.<br />Example: "gif,jpeg,jpg,png"',
  );
  $form['klicktipp_resources'][KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_IMAGE_FILESIZE] = array(
    '#type' => 'textfield',
    '#title' => 'CKFinder allowed image file size',
    '#default_value' => variable_get(KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_IMAGE_FILESIZE, '2M'),
    '#description' => 'Enter the maximum image file size.<br />"1G" = 1 Gigabyte, "1M" = 1 Megabyte, "1K" = 1 Kilobyte, "512" = 512 Byte',
  );
  $form['klicktipp_resources'][KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_FILE_TYPES] = array(
    '#type' => 'textfield',
    '#title' => 'CKFinder allowed file types',
    '#default_value' => variable_get(KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_FILE_TYPES, 'zip,7z,gz,gzip,rar,tar,tgz,csv,doc,docx,ods,odt,pdf,ppt,pptx,pxd,rtf,sxc,sxw,txt,vsd,xls,xlsx,bmp,gif,jpeg,jpg,png,tif,tiff'),
    '#description' => 'Enter a comma separated list of file extentions, the user is allowed to upload.<br />Example: "pdf,rar,zip"',
  );
  $form['klicktipp_resources'][KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_FILE_FILESIZE] = array(
    '#type' => 'textfield',
    '#title' => 'CKFinder allowed file size',
    '#default_value' => variable_get(KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_FILE_FILESIZE, '5M'),
    '#description' => 'Enter the maximum file size.<br />"1G" = 1 Gigabyte, "1M" = 1 Megabyte, "1K" = 1 Kilobyte, "512" = 512 Byte',
  );

  //DigiStore24 API
  $form['klicktipp_resources']['klicktipp_settings_digistore_api_key'] = array(
    '#type' => 'textfield',
    '#title' => 'DigiStore24 API Key',
    '#description' => 'Enter the DigiStore24 API Key',
    '#default_value' => variable_get('klicktipp_settings_digistore_api_key', ''),
  );
  $form['klicktipp_resources']['klicktipp_settings_digistore_developer_key'] = array(
    '#type' => 'textfield',
    '#title' => 'DigiStore24 Developer Key',
    '#description' => 'Enter the DigiStore24 Developer Key',
    '#default_value' => variable_get('klicktipp_settings_digistore_developer_key', ''),
  );
  $form['klicktipp_resources']['klicktipp_settings_digistore_hash_password'] = array(
    '#type' => 'textfield',
    '#title' => 'DigiStore24 Hash Password',
    '#description' => 'Enter the DigiStore24 Hash Password (used for multi-device-tracking)',
    '#default_value' => variable_get('klicktipp_settings_digistore_hash_password', ''),
  );
  $form['klicktipp_resources']['klicktipp_settings_digistore_enable_multi_device_tracking'] = array(
    '#type' => 'checkbox',
    '#title' => 'DigiStore24 Enable Multi-Device-Tracking',
    '#return_value' => 1,
    '#description' => 'If enabled, all Klick-tipp track links will be redirected through Digistore24 to set Affiliate Cookie (if subscriber has affiliates)',
    '#default_value' => variable_get('klicktipp_settings_digistore_enable_multi_device_tracking', 0),
  );
  $form['klicktipp_resources']['klicktipp_settings_digistore_redirect_resolution_timeout'] = array(
    '#type' => 'textfield',
    '#title' => 'Redirect resolution request timeout for DigiStore24 tracking links',
    '#default_value' => variable_get('klicktipp_settings_digistore_redirect_resolution_timeout', 1),
    '#element_validate' => array('element_validate_integer_positive'),
    '#description' => 'Max execution time to fetch target url of a DigiStore24 tracking link.' . ' ' .
      'If this time exceedes, we redirect to url defined by user instead of DigiStore24 tracking url.'
  );

  $form['klicktipp_resources']['klicktipp_gmail_preview_email_address'] = array(
    '#type' => 'textfield',
    '#title' => 'Google Gmail Preview Email Address',
    '#default_value' => variable_get('klicktipp_gmail_preview_email_address', ''),
    '#description' => 'Enter the email address used for the Gmail Inspector.',
  );

  $form['klicktipp_resources']['klicktipp_gmail_preview_google_oauth_clientid'] = array(
    '#type' => 'textfield',
    '#title' => 'Google Gmail Preview OAuth2 ClientID',
    '#default_value' => variable_get('klicktipp_gmail_preview_google_oauth_clientid', ''),
    '#description' => 'Enter the Google OAuth2 ClientID used for the Gmail Inspector.',
  );

  $form['klicktipp_resources']['klicktipp_gmail_preview_google_oauth_clientsecret'] = array(
    '#type' => 'textfield',
    '#title' => 'Google Gmail Preview OAuth2 ClientSecret',
    '#default_value' => variable_get('klicktipp_gmail_preview_google_oauth_clientsecret', ''),
    '#description' => 'Enter the Google OAuth2 ClientSecret used for the Gmail Inspector.',
  );

  $form['klicktipp_resources']['klicktipp_gmail_preview_google_oauth_refresh_token'] = array(
    '#type' => 'textfield',
    '#title' => 'Google Gmail Preview OAuth2 Refresh Token',
    '#default_value' => variable_get('klicktipp_gmail_preview_google_oauth_refresh_token', ''),
    '#description' => 'Enter the Google OAuth2 Refresh Token used for the Gmail Inspector.',
  );

  // YouTube Data API
  $form['klicktipp_resources']['klicktipp_settings_youtube_api_key'] = array(
    '#type' => 'textfield',
    '#title' => 'RapidAPI API Key for YouTube Content Analysis',
    '#description' => 'Enter the RapidAPI API Key for the YouTubeAPI',
    '#default_value' => variable_get('klicktipp_settings_youtube_api_key', ''),
  );

  // Settings for use with "date" and "App\Klicktipp\Dates::formatDate" functions.
  // Note: Drupals "format_date" does not work with php5 timezones and DST (daylight saving times).
  $form['klicktipp_datesettings'] = array(
    '#type' => 'fieldset',
    '#title' => 'Date and time',
  );
  $tzoptions = array();
  foreach (DateTimeZone::listIdentifiers() as $TZ) {
    $tzoptions[$TZ] = t(/*ignore*/$TZ);
  }
  $wikipedia = l('Overview about UTC offsets and DST',
    'http://en.wikipedia.org/wiki/List_of_tz_database_time_zones',
    array('attributes' => array('target' => '_blank'))
  );
  $form['klicktipp_datesettings'][Dates::TIMEZONE_VARIABLE] = array(
    '#type' => 'select',
    '#title' => 'Default timezone',
    '#default_value' => variable_get(Dates::TIMEZONE_VARIABLE, 'Europe/Berlin'),
    '#options' => $tzoptions,
    '#description' => "This timezone is used, when user timezone is not given. $wikipedia"
  );
  $form['klicktipp_datesettings'][Dates::FORMAT_DMY] = array(
    '#type' => 'textfield',
    '#title' => 'Date',
    '#default_value' => variable_get(Dates::FORMAT_DMY, 'd.m.Y'),
    '#description' => 'Date only: ' . Dates::formatDate(Dates::FORMAT_DMY, time()),
  );

  //@see klicktipp_settings_form_pre_render
  $datepicker_link = l('Datepicker', 'http://api.jqueryui.com/datepicker/', array(
    'external' => TRUE,
    'fragment' => 'utility-formatDate',
    'attributes' => array('target' => '_blank'),
  ));
  $form['klicktipp_datesettings'][Dates::FORMAT_DMY_DATEPICKER] = array(
    '#type' => 'textfield',
    '#title' => 'Date for datepicker component',
    '#default_value' => variable_get(Dates::FORMAT_DMY_DATEPICKER, 'dd.mm.yy'),
    '#description' => "Date format to write into the textfield by the datepicker component. ".
      "The date formats differ between PHP and the Datepicker!<br />".
      "Please see the manuals for $datepicker_link <br />".
      "<strong>The format must be equivalent to the PHP format below!!!</strong>",
  );

  $form['klicktipp_datesettings'][Dates::FORMAT_DMY_DATEPICKER_PHP] = array(
    '#type' => 'textfield',
    '#title' => 'Date for datepicker (PHP)',
    '#default_value' => variable_get(Dates::FORMAT_DMY_DATEPICKER_PHP, 'd.m.Y'),
    '#description' => 'Date format for datepicker (set/get by PHP). '.
      'The date formats differ between PHP and the Datepicker!<br />'.
      '<strong>The format must be equivalent to the component date above !!!</strong>',
  );

  $form['klicktipp_datesettings'][Dates::FORMAT_DMYHI] = array(
    '#type' => 'textfield',
    '#title' => 'Date with minutes',
    '#default_value' => variable_get(Dates::FORMAT_DMYHI, 'd.m.Y H:i'),
    '#description' => 'Date with minutes: ' . Dates::formatDate(Dates::FORMAT_DMYHI, time()),
  );
  $form['klicktipp_datesettings'][Dates::FORMAT_DMYHIS] = array(
    '#type' => 'textfield',
    '#title' => 'Date with seconds',
    '#default_value' => variable_get(Dates::FORMAT_DMYHIS, 'd.m.Y H:i:s'),
    '#description' => 'Date with seconds: ' . Dates::formatDate(Dates::FORMAT_DMYHIS, time()),
  );
  $form['klicktipp_datesettings'][Dates::FORMAT_HIS] = array(
    '#type' => 'textfield',
    '#title' => 'Date with seconds',
    '#default_value' => variable_get(Dates::FORMAT_HIS, 'H:i:s \U\h\r'),
    '#description' => 'Time with suffix: ' . Dates::formatDate(Dates::FORMAT_HIS, time()),
  );
  $form['klicktipp_datesettings'][Dates::FORMAT_DM_CHARTS] = array(
    '#type' => 'textfield',
    '#title' => 'Date for chart tooltips',
    '#default_value' => variable_get(Dates::FORMAT_DM_CHARTS, 'd.m.'),
    '#description' => 'Date for chart tooltips: ' . Dates::formatDate(Dates::FORMAT_DM_CHARTS, time()),
  );
  $form['klicktipp_datesettings'][Dates::FORMAT_HI] = array(
    '#type' => 'textfield',
    '#title' => 'Time with hours and minutes',
    '#default_value' => variable_get(Dates::FORMAT_HI, 'H:i'),
    '#description' => 'Time with hours and minutes: ' . klicktipp_date(Dates::FORMAT_HI, time()),
  );

  $form['klicktipp_number_formats'] = array(
    '#type' => 'fieldset',
    '#title' => 'Number formats',
  );

  $form['klicktipp_number_formats'][KLICKTIPP_NUMBER_FORMAT_DECIMAL_POINT] = array(
    '#type' => 'textfield',
    '#title' => 'Decimal point',
    '#default_value' => variable_get(KLICKTIPP_NUMBER_FORMAT_DECIMAL_POINT, ','),
    '#description' => 'Format numbers with klicktipp_number_format() using this decimal point. Default is "," (3,45)',
  );

  $form['klicktipp_number_formats'][KLICKTIPP_NUMBER_FORMAT_THOUSANDS_SEPARATOR] = array(
    '#type' => 'textfield',
    '#title' => 'Thousands separator',
    '#default_value' => variable_get(KLICKTIPP_NUMBER_FORMAT_THOUSANDS_SEPARATOR, ''),
    '#description' => 'Format numbers with klicktipp_number_format() using this thousands separator. Leave empty for no separator. Example for ".": 1.234',
  );

  $google_link = l('Google chart date format', 'http://icu-project.org/apiref/icu4c/classSimpleDateFormat.html', array(
    'external' => TRUE,
    'fragment' => 'details',
    'attributes' => array('target' => '_blank'),
  ));
  $form['klicktipp_datesettings'][Dates::FORMAT_DM_CHARTS_Y_AXIS] = array(
    '#type' => 'textfield',
    '#title' => 'Date for chart y-axis',
    '#default_value' => variable_get(Dates::FORMAT_DM_CHARTS_Y_AXIS, 'd.MM.'),
    '#description' => "Date for chart y-axis, @see $google_link",
  );

  $form['klicktipp_datesettings'][Dates::FORMAT_SUBSCRIPTION_FORM_CREATED_ON] = array(
    '#type' => 'textfield',
    '#title' => 'Date for subscription form created on',
    '#default_value' => variable_get(Dates::FORMAT_SUBSCRIPTION_FORM_CREATED_ON, 'd.m.Y'),
    '#description' => 'Date for the subscription form created on: ' . Dates::formatDate(Dates::FORMAT_SUBSCRIPTION_FORM_CREATED_ON, time()),
  );

  $form[KLICKTIPP_AGED_MAXFACTOR] = array(
    '#type' => 'textfield',
    '#title' => 'Aged Subscribers: factor for unsubscribes',
    '#default_value' => variable_get(KLICKTIPP_AGED_MAXFACTOR, 1),
    '#description' => 'This factor adjusts the maximum count of unsubscribes per newsletter. 1=default, 2=doubles unsubscribe rate, 0=no unsubscribes',
  );

  $form[KLICKTIPP_ACCOUNT_DATA_PROCESSING_ORDER_PRODUCT_ID] = array(
    '#type' => 'textfield',
    '#title' => 'Amember product ID for the Data Processing Order',
    '#default_value' => variable_get(KLICKTIPP_ACCOUNT_DATA_PROCESSING_ORDER_PRODUCT_ID, 100),
    '#description' => 'Enter the Amember product id.',
  );

  $form['klicktipp_account_dpo_digistore_product_id'] = array(
    '#type' => 'textfield',
    '#title' => 'Digistore product ID for the Data Processing Order',
    '#default_value' => variable_get('klicktipp_account_dpo_digistore_product_id', 37123),
    '#description' => 'Enter the Digistore24 product id for prefill cart urls.',
  );

  $form['klicktipp_htmlfilter_enabled'] = array(
    '#type' => 'checkbox',
    '#title' => 'Enable html validation for emails',
    '#default_value' => variable_get('klicktipp_htmlfilter_enabled', 1),
    '#description' => 'Check email content and signatures for valid and script-free html.',
  );

  $form['klicktipp_contact'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp Contact form',
  );

  klicktipp_include('contact.inc', '/forms');
  $form['klicktipp_contact'][KLICKTIPP_CONTACT_AMEMBER_ADMIN_LINK] = array(
    '#type' => 'textfield',
    '#title' => 'aMember admin link',
    '#default_value' => variable_get(KLICKTIPP_CONTACT_AMEMBER_ADMIN_LINK, 'https://www.klick-tipp.com/crm/admin'),
    '#description' => 'Enter the link to the aMember admin area to create support email links to customer edit.',
  );

  $form['klicktipp_contact'][KLICKTIPP_CONTACT_TAGGING_ACCOUNT] = array(
    '#type' => 'textfield',
    '#title' => 'Subscriber Tagging Account',
    '#default_value' => variable_get(KLICKTIPP_CONTACT_TAGGING_ACCOUNT, ''),
    '#description' => 'Enter the UserID of the Klick-Tipp account the customer should be tagged in after submitting a ticket.<br />Leave empty for no tagging.',
  );

  $form['klicktipp_contact'][KLICKTIPP_CONTACT_TAGGING_TAGID] = array(
    '#type' => 'textfield',
    '#title' => 'Subscriber Tagging Tag',
    '#default_value' => variable_get(KLICKTIPP_CONTACT_TAGGING_TAGID, ''),
    '#description' => 'Enter the ID of the tag the customer should be tagged with in the above account after submitting a ticket.',
  );

  $form['klicktipp_cache_settings'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp Cache Settings',
  );
  $form['klicktipp_cache_settings']['locale_cache_length'] = array(
    '#type' => 'textfield',
    '#title' => 'Cache length for translation strings',
    '#default_value' => variable_get('locale_cache_length', '500'),
    '#description' => '',
  );
  $form['klicktipp_cache_settings']['klicktipp_default_cache_lifetime'] = array(
    '#type' => 'textfield',
    '#title' => 'Default cache lifetime',
    '#default_value' => Cache::getDefaultLifetime(),
    '#description' => 'Default lifetime of klicktipp cache entries in seconds',
    '#element_validate' => ['element_validate_integer_positive'],
  );

  $form['#validate'][] = 'klicktipp_settings_form_validate';
  $form['#submit'][] = 'klicktipp_settings_form_submit';
  $form['#pre_render'][] = 'klicktipp_settings_form_pre_render';

  return system_settings_form($form);
}

function klicktipp_settings_form_pre_render($form) {
  //check via JavaScript on page load if both formats have the same output
  $TestDate = "2014-02-01";
  $form['klicktipp_datesettings']['DatepickerFormatCheck'] = array(
    '#type' => 'markup',
    '#value' => '<input id="datepicker-check-field" class="klicktipp-datepicker" type="hidden" value="' . Dates::formatDate(Dates::FORMAT_DMY_DATEPICKER_PHP, strtotime($TestDate)) . '" />',
  );

  $check_script = '
    $(document).ready(function () {
      var php_val = $("#datepicker-check-field").val();
      $("#datepicker-check-field").datepicker( "setDate", new Date("' . $TestDate . '") );
      var js_val = $("#datepicker-check-field").val();
      if ( php_val != js_val )
        $("#datepicker-check-field").parents("form").prepend("<div class=\"messages error\">The output of the datepicker date format differs from the standard date format!</div>");
    });
  ';

  drupal_add_js($check_script, array('type' => 'inline', 'scope' => 'footer'));

  return $form;
}

function klicktipp_settings_form_validate($form, &$form_state) {
  //check if the entered colors are in the right format
  $KlickTippColors = array_map('trim', explode(',', strtolower($form_state['values'][KLICKTIPP_THEME_COLORS])));
  if (!empty($KlickTippColors)) {
    foreach ($KlickTippColors as $color) {
      if (!preg_match("!#([0-9a-f]{3}|[0-9a-f]{6})+$!is", $color)) {
        form_set_error(KLICKTIPP_THEME_COLORS, "Invalid css color format: $color.");
      }

    }
  }
}

function klicktipp_settings_form_submit($form, &$form_state) {
  //format the comma separated color list into an array
  if (empty($form_state['values'][KLICKTIPP_THEME_COLORS])) {
    $form_state['values'][KLICKTIPP_THEME_COLORS] = array();
  }
  else {
    $form_state['values'][KLICKTIPP_THEME_COLORS] = array_map('trim', explode(',', strtolower($form_state['values'][KLICKTIPP_THEME_COLORS])));
  }

}

/*
 * Marketing settings
 */

function klicktipp_settings_marketing($form, &$form_state) {

  $weight = 1;

  $form['klicktipp_marketing'] = array(
    '#type' => 'fieldset',
    '#title' => 'Marketing Account',
    '#weight' => $weight++,
  );

  $form['klicktipp_marketing']['klicktipp_marketing_account_id'] = array(
    '#type' => 'textfield',
    '#title' => 'KlickTipp marketing account',
    '#default_value' => variable_get('klicktipp_marketing_account_id', variable_get('klicktipp_helpscout_nps_marketing_account_id', '')),
    '#description' => 'Enter the UserID of the KlickTipp account to use for marketing.',
    '#element_validate' => array('element_validate_integer_positive'),
    '#weight' => $weight++,
  );

  $marketingAccountID = variable_get('klicktipp_marketing_account_id', 0);
  if (!empty($marketingAccountID)) {
    $marketingAccount = user_load($marketingAccountID);
    //retrieve available whitelabel domains of marketing account with DMARC enabled
    $whitelabelDomains = Domainset::RetrieveSenderDomainNames($marketingAccount, FALSE);
    $domainOptions = array();
    foreach($whitelabelDomains as $domain) {
        $domainOptions[$domain] = $domain;
    }

    if (!empty($domainOptions)) {

      $form['klicktipp_marketing']['klicktipp_marketing_account_drupal_mail_domain'] = [
        '#type' => 'select',
        '#title' => 'Sender Domain for drupal_mail()',
        '#default_value' => variable_get('klicktipp_marketing_account_drupal_mail_domain', ''),
        '#options' => array_merge(['' => 'Please select'], $domainOptions),
        '#description' => 'Choose a whitelabel domain in the KlickTipp marketing account (see above) which should be used for (all) emails sent via drupal_mail().',
        '#weight' => $weight++,
      ];

    }
    else {
      $form['klicktipp_marketing']['klicktipp_marketing_account_drupal_mail_domain'] = array(
        '#type' => 'item',
        '#title' => 'Sender Domain for drupal_mail()',
        '#value' => 'The KlickTipp marketing account id (above) does not have a whitelabel domain configured, that can be used for (all) emails sent via drupal_mail().',
        '#weight' => $weight++,
      );
    }

  }
  else {

    $form['klicktipp_marketing']['klicktipp_marketing_account_drupal_mail_domain'] = array(
      '#type' => 'item',
      '#title' => 'Sender Domain for drupal_mail()',
      '#value' => 'Enter a KlickTipp marketing account id (above) to use a whitelabel domain for (all) emails sent via drupal_mail()',
      '#weight' => $weight++,
    );

  }

  $form['klicktipp_marketing']['klicktipp_data_processing_order_check'] = array(
    '#type' => 'checkbox',
    '#title' => 'Activate Data Processing Order Check',
    '#return_value' => 1,
    '#default_value' => variable_get('klicktipp_data_processing_order_check', 0),
    '#description' => 'Activate if users have to confirm the data processing order to access their account',
    '#weight' => $weight++,
  );

  $form['klicktipp_marketing']['klicktipp_data_processing_order_tom'] = array(
    '#type' => 'textfield',
    '#title' => 'Technical and organizational measures',
    '#default_value' => variable_get('klicktipp_data_processing_order_tom', ''),
    '#description' => 'Enter the absolute link to the TOM PDF',
    '#weight' => $weight++,
  );

  $form['klicktipp_marketing']['klicktipp_marketing_account_customer_tagid'] = array(
    '#type' => 'textfield',
    '#title' => 'KlickTipp marketing account customer tag id',
    '#default_value' => variable_get('klicktipp_marketing_account_customer_tagid', 0),
    '#description' => 'Enter the TagID of the KlickTipp accounts customer tag.',
    '#weight' => $weight++,
  );

  $form['klicktipp_marketing_custom_fields'] = array(
    '#type' => 'fieldset',
    '#title' => 'Marketing Account Custom Fields',
    '#weight' => $weight++,
  );

  $form['klicktipp_marketing_custom_fields']['klicktipp_marketing_klicktipp_login_custom_field_id'] = array(
    '#type' => 'textfield',
    '#title' => 'KlickTipp login link (custom field id)',
    '#default_value' => variable_get('klicktipp_marketing_klicktipp_login_custom_field_id', ''),
    '#description' => 'Enter the ID of the custom field (in klicktipp marketing account) that contains the klicktipp login link (generated drupal reset password link).',
    '#weight' => $weight++,
  );

  $form['klicktipp_marketing_custom_fields']['klicktipp_marketing_subscriber_limit_custom_field_id'] = array(
    '#type' => 'textfield',
    '#title' => 'KlickTipp subscriber limit reach (custom field id)',
    '#default_value' => variable_get('klicktipp_marketing_subscriber_limit_custom_field_id', ''),
    '#description' => 'Enter the ID of the custom field (in klicktipp marketing account) that contains the subscriber limit reach.',
    '#weight' => $weight++,
  );

  $form['klicktipp_marketing_custom_fields']['klicktipp_marketing_subscriber_username_custom_field_id'] = array(
    '#type' => 'textfield',
    '#title' => 'KlickTipp subscriber username (custom field id)',
    '#default_value' => variable_get('klicktipp_marketing_subscriber_username_custom_field_id', ''),
    '#description' => 'Enter the ID of the custom field (in klicktipp marketing account) that contains the klicktipp username.',
    '#weight' => $weight++,
  );

  $form['klicktipp_marketing_custom_fields']['klicktipp_marketing_subscriber_amemberid_custom_field_id'] = array(
    '#type' => 'textfield',
    '#title' => 'KlickTipp Amember userid (custom field id)',
    '#default_value' => variable_get('klicktipp_marketing_subscriber_amemberid_custom_field_id'),
    '#description' => 'Enter the ID of the custom field (in klicktipp marketing account) that contains the Users Amember Id.',
    '#weight' => $weight++,
    '#required' => TRUE,
  );

  $form['klicktipp_marketing_custom_fields']['klicktipp_marketing_subscriber_affiliated_by_custom_field_id'] = array(
    '#type' => 'textfield',
    '#title' => 'KlickTipp Affiliated by AffiliateName (custom field id)',
    '#default_value' => variable_get('klicktipp_marketing_subscriber_affiliated_by_custom_field_id', ''),
    '#description' => 'Enter the ID of the custom field (in klicktipp marketing account) that contains the Digistore affiliate name of the affiliate who referred this contact.',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp Upsell Includes',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell'][KLICKTIPP_CONTENT_INCLUDE_IPN_PRODUCT_LIMIT_UPSELL] = array(
    '#type' => 'textfield',
    '#title' => 'IPN product upsell (Limit reached)',
    '#default_value' => variable_get(KLICKTIPP_CONTENT_INCLUDE_IPN_PRODUCT_LIMIT_UPSELL, ''),
    '#description' => 'Show an upsell when the IPN Product Limit is reached. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell'][KLICKTIPP_CONTENT_INCLUDE_WUFOO_LIMIT_UPSELL] = array(
    '#type' => 'textfield',
    '#title' => 'Wufoo subscription forms (Limit reached)',
    '#default_value' => variable_get(KLICKTIPP_CONTENT_INCLUDE_WUFOO_LIMIT_UPSELL, ''),
    '#description' => 'Show an upsell when the Wufoo Subscription forms Limit is reached. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell'][KLICKTIPP_CONTENT_INCLUDE_APIKEY_UPSELL] = array(
    '#type' => 'textfield',
    '#title' => 'Create API key (permission page when no access)',
    '#default_value' => variable_get(KLICKTIPP_CONTENT_INCLUDE_APIKEY_UPSELL, ''),
    '#description' => 'Show an upsell for the API key feature. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_content_include_sms_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'SMS marketing (permission page/fieldset when no access)',
    '#default_value' => variable_get('klicktipp_content_include_sms_upsell', ''),
    '#description' => 'Show an upsell for the SMS marketing features. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_content_include_marketing_tools_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'Marketing Tools (permission page/dialog items when no access)',
    '#default_value' => variable_get('klicktipp_content_include_marketing_tools_upsell', ''),
    '#description' => 'Show an upsell for the marketing tool Countdown. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_content_include_tagging_pixel_limit_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'Tagging pixel (limit reached)',
    '#default_value' => variable_get('klicktipp_content_include_tagging_pixel_limit_upsell', ''),
    '#description' => 'Show an upsell when the Tagging Pixel limit is reached. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_content_include_countdown_limit_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'Countdown (limit reached)',
    '#default_value' => variable_get('klicktipp_content_include_countdown_limit_upsell', ''),
    '#description' => 'Show an upsell when the Countdown limit is reached. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_outbound_limit_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'Outbound upsell (Limit reached)',
    '#default_value' => variable_get('klicktipp_outbound_limit_upsell', ''),
    '#description' => 'Show an upsell when the Outbound Limit is reached. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_outbound_kajabi_limit_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'Kajabi activation upsell (Limit reached)',
    '#default_value' => variable_get('klicktipp_outbound_kajabi_limit_upsell', ''),
    '#description' => 'Show an upsell when the Outbound Limit is reached. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_content_include_whitelabel_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'Whitelabel (permission page when no access)',
    '#default_value' => variable_get('klicktipp_content_include_whitelabel_upsell', ''),
    '#description' => 'Show an upsell for Whitelabel. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_content_include_automations_limit_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'Automations (limit reached)',
    '#default_value' => variable_get('klicktipp_content_include_automations_limit_upsell', ''),
    '#description' => 'Show an upsell when the Automation limit is reached. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_content_include_splittest_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'Splittest-Club',
    '#default_value' => variable_get('klicktipp_content_include_splittest_upsell', ''),
    '#description' => 'Show an upsell for Splittest-Club Tools. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_upsell']['klicktipp_content_include_youtube_analysis_upsell'] = array(
    '#type' => 'textfield',
    '#title' => 'YouTube Content Analysis (permission page/fieldset when no access)',
    '#default_value' => variable_get('klicktipp_content_include_youtube_analysis_upsell', ''),
    '#description' => 'Show an upsell for the YouTube Content Analysis Tool. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp Consultant Settings',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['klicktipp_consultant_roleid'] = array(
    '#type' => 'textfield',
    '#title' => 'Role Consultant',
    '#default_value' => variable_get('klicktipp_consultant_roleid', ''),
    '#description' => 'Enter the id of the consultant role.',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['amember_consultant_productids'] = array(
    '#type' => 'textfield',
    '#title' => 'Amember Consultant Product IDs',
    '#default_value' => variable_get('amember_consultant_productids', '147,148'),
    '#description' => 'Enter a comma separated list of amember product IDs.',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['amember_email_marketing_formula_productids'] = array(
    '#type' => 'textfield',
    '#title' => 'email marketing formula amember product ids',
    '#default_value' => variable_get('amember_email_marketing_formula_productids', ''),
    '#description' => 'Enter a comma separated list of amember product IDs for the email marketing formula',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['consultant_webinar_amember_id'] = array(
    '#type' => 'textfield',
    '#title' => 'Consultant webinar amember id',
    '#default_value' => variable_get('consultant_webinar_amember_id', ''),
    '#description' => 'Enter the id of the custom field that stores the amemberid for the consultant webinar',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['klicktipp_consultant_manager_email_address'] = array(
    '#type' => 'textfield',
    '#title' => 'Consultant manager address',
    '#default_value' => variable_get('klicktipp_consultant_manager_email_address', ''),
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['klicktipp_content_include_consultant_marketplace'] = array(
    '#type' => 'textfield',
    '#title' => 'Consultant Marketplace Landingpage',
    '#default_value' => variable_get('klicktipp_content_include_consultant_marketplace', ''),
    '#description' => 'Show the landing page for the Klick-Tipp consultant marketplace. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['klicktipp_content_include_consultant_marketplace_entry'] = array(
    '#type' => 'textfield',
    '#title' => 'Consultant Marketplace Entry',
    '#default_value' => variable_get('klicktipp_content_include_consultant_marketplace_entry', ''),
    '#description' => 'Show the landing page for the Klick-Tipp consultant marketplace entry. (Insert path to the content_include file)',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['klicktipp_consultant_skills'] = array(
    '#type' => 'textarea',
    '#title' => 'Skills',
    '#rows' => 15,
    '#default_value' => implode("\n", (array)variable_get('klicktipp_consultant_skills')),
    '#description' => 'Enter the skills list for consultants (1 per line).',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['klicktipp_consultant_tools'] = array(
    '#type' => 'textarea',
    '#title' => 'Tools',
    '#rows' => 15,
    '#default_value' => implode("\n", (array)variable_get('klicktipp_consultant_tools')),
    '#description' => 'Enter the tools list for consultants (1 per line).',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['klicktipp_consultant_services'] = array(
    '#type' => 'textarea',
    '#title' => 'Services',
    '#rows' => 10,
    '#default_value' => implode("\n", (array)variable_get('klicktipp_consultant_services')),
    '#description' => 'Enter the service list for consultants (1 per line).',
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['klicktipp_consulting_request_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_consulting_request_subject', 'New consulting request'),
    '#size' => 60,
    '#maxlength' => 256,
    '#required' => TRUE,
    '#weight' => $weight++,
  );

  $form['klicktipp_consultants']['klicktipp_consulting_request_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#description' => "The placeholders !cname, !name, !company, !email, !phone and !message will be replaced.",
    '#default_value' => variable_get('klicktipp_consulting_request_body', ''),
    '#cols' => 80,
    '#rows' => 10,
    '#required' => TRUE,
    '#weight' => $weight++,
  );

  $form['klicktipp_convention'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp Convention Settings',
    '#weight' => $weight++,
  );

  $form['klicktipp_convention']['klicktipp_convention_roleid'] = array(
    '#type' => 'textfield',
    '#title' => 'Role Convention',
    '#default_value' => variable_get('klicktipp_convention_roleid', ''),
    '#description' => 'Enter the id of the convention role.',
    '#weight' => $weight++,
  );

  $form['klicktipp_convention']['amember_convention_productids'] = array(
    '#type' => 'textfield',
    '#title' => 'Amember Convention Product IDs',
    '#default_value' => variable_get('amember_convention_productids', ''),
    '#description' => 'Enter a comma separated list of amember product IDs.',
    '#weight' => $weight++,
  );

  $form['klicktipp_upgrade'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp Order Upgrade Settings',
    '#weight' => $weight++,
  );

  $form['klicktipp_upgrade']['amember_upgrade_excluded_productids'] = array(
    '#type' => 'textfield',
    '#title' => 'Amember Product IDs that are no klicktipp subscriptions',
    '#default_value' => variable_get('amember_upgrade_excluded_productids', ''),
    '#description' => 'Enter a comma separated list of amember product IDs.',
    '#weight' => $weight++,
  );

  $form['klicktipp_digistore_settings'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp Digistore Affiliate Settings',
    '#weight' => $weight++,
  );

  $form['klicktipp_digistore_settings']['amember_goto_default_redirect_url'] = array(
    '#type' => 'textfield',
    '#title' => 'goto.php default affiliate redirect',
    '#default_value' => variable_get('amember_goto_default_redirect_url', APP_URL),
    '#description' => 'Where should a customer be redirected if the target url is not valid/allowed. This usually points to the webinar registration.',
    '#weight' => $weight++,
  );

  // former amamber $config['payment']['digistore']['afflink_affiliate_product'];
  $form['klicktipp_digistore_settings']['digistore_afflink_affiliate_product'] = array(
    '#type' => 'textfield',
    '#title' => 'goto.php default affiliate product',
    '#default_value' => variable_get('digistore_afflink_affiliate_product', 0),
    '#description' => 'Enter the DigiStore24 ID of the KlickTipp affiliate product used for creating affiliate links.',
    '#weight' => $weight++,
  );

  // former amamber $config['payment']['digistore']['afflink_redirect_password'];
  $form['klicktipp_digistore_settings']['digistore_afflink_redirect_password'] = array(
    '#type' => 'textfield',
    '#title' => 'goto.php affiliate hash key',
    '#default_value' => variable_get('digistore_afflink_redirect_password', ''),
    '#description' => 'Enter the DigiStore24 ThankyouPage key of the Klick-Tipp account. Used to create a hash for affiliate link target urls.',
    '#weight' => $weight++,
  );

  $form['klicktipp_domain_mx_check'] = [
    '#type' => 'fieldset',
    '#title' => 'Setting for check of domains without mx records',
    '#description' => 'We use this setting to determine DKIM-Domains (and their subdomains used in sender addresses) without MX record',
    '#tree' => TRUE,
    '#weight' => $weight++,
  ];

  $domainCheckSettings =  variable_get('klicktipp_domain_mx_check', []);
  $form['klicktipp_domain_mx_check']['active'] = [
    '#type' => 'checkbox',
    '#title' => 'Active ?',
    '#weight' => $weight++,
    '#default_value' => $domainCheckSettings['active'] ?? FALSE,
    '#states' => [
      'checked' => [
        ':input[name="klicktipp_marketing_account_id"]' => ['filled' => TRUE],
      ],
      'unchecked' => [
        ':input[name="klicktipp_domain_mx_check[active]"]' => ['checked' => FALSE],
      ],
      'enabled' => [
        ':input[name="klicktipp_marketing_account_id"]' => ['filled' => TRUE],
      ],
    ],
  ];

  $requiredState = [
    'required' => [
      ':input[name="klicktipp_domain_mx_check[active]"]' => ['checked' => TRUE],
    ],
  ];

  $form['klicktipp_domain_mx_check']['tag_id'] = [
    '#type' => 'textfield',
    '#title' => 'Tag-ID',
    '#default_value' => $domainCheckSettings['tag_id'] ?? '',
    '#description' => 'ID of Tag in marketing account to tag subscriber with, if corresponding user has some domains without mx record',
    '#element_validate' => array('element_validate_integer_positive'),
    '#weight' => $weight++,
    '#states' => $requiredState,
  ];

  $form['klicktipp_domain_mx_check']['custom_field_id'] = [
    '#type' => 'textfield',
    '#title' => 'CustomField-ID',
    '#default_value' => $domainCheckSettings['custom_field_id'] ?? '',
    '#description' => 'ID of custom field in marketing account to put domains without mx record into',
    '#element_validate' => array('element_validate_integer_positive'),
    '#weight' => $weight++,
    '#states' => $requiredState,
  ];


  $form['#submit'][] = 'klicktipp_settings_marketing_submit';

  return system_settings_form($form);
}

function klicktipp_settings_marketing_validate(&$form, &$form_state) {
  $domainCheckSettings = $form_state['values']['klicktipp_domain_mx_check'];
  $tagId = $domainCheckSettings['tag_id'];
  $customFieldId = $domainCheckSettings['custom_field_id'];
  if ($domainCheckSettings['active']) {
    $accountId = $form_state['values']['klicktipp_marketing_account_id'];
    if (empty($tagId)) {
      form_set_error('klicktipp_domain_mx_check][tag_id', 'Tag-ID is required');
    } elseif (!Tag::FromID($accountId, $tagId)) {
      form_set_error('klicktipp_domain_mx_check][tag_id', 'Tag does not exists in marketing account');
    }
    if (empty($customFieldId)) {
      form_set_error('klicktipp_domain_mx_check][custom_field_id', 'CustomField-ID is required');
    } elseif(!CustomFields::FromID($accountId, $customFieldId)) {
      form_set_error('klicktipp_domain_mx_check][custom_field_id', 'CustomField does not exists in marketing account');
    }
  }
}

function klicktipp_settings_marketing_submit($form, &$form_state) {

  $skills = array_filter(array_map('trim', explode("\n", trim($form_state['values']['klicktipp_consultant_skills']))));
  if (empty($skills)) {
    $form_state['values']['klicktipp_consultant_skills'] = variable_get('klicktipp_consultant_skills', '');
  }
  else {
    $form_state['values']['klicktipp_consultant_skills'] = $skills;
  }
  $tools = array_filter(array_map('trim', explode("\n", trim($form_state['values']['klicktipp_consultant_tools']))));
  if (empty($tools)) {
    $form_state['values']['klicktipp_consultant_tools'] = variable_get('klicktipp_consultant_tools', '');
  }
  else {
    $form_state['values']['klicktipp_consultant_tools'] = $tools;
  }
  $services = array_filter(array_map('trim', explode("\n", trim($form_state['values']['klicktipp_consultant_services']))));
  if (empty($services)) {
    $form_state['values']['klicktipp_consultant_services'] = variable_get('klicktipp_consultant_services', '');
  }
  else {
    $form_state['values']['klicktipp_consultant_services'] = $services;
  }

}

function klicktipp_settings_facebook($form, &$form_state) {
  Libraries::include("list_formbuilder.inc");

  $form['klicktipp_facebook'] = array(
    '#type' => 'fieldset',
    '#title' => 'Facebook parameters',
  );
  $form['klicktipp_facebook']['klicktipp_facebook_appkey'] = array(
    '#type' => 'textfield',
    '#title' => 'Application key',
    '#default_value' => variable_get('klicktipp_facebook_appkey', '244591058892778'),
  );
  $form['klicktipp_facebook']['klicktipp_facebook_appsecret'] = array(
    '#type' => 'textfield',
    '#title' => 'Application secret',
    '#default_value' => variable_get('klicktipp_facebook_appsecret', '********************************'),
  );
  $form['klicktipp_facebook']['klicktipp_facebook_appdf'] = array(
    '#type' => 'textfield',
    '#title' => 'Application tutorial',
    '#default_value' => variable_get('klicktipp_facebook_appdf', 'https://assets.klicktipp.com/EOA_Facebook_App_erstellen.pdf'),
  );

  $form['klicktipp_facebook_audience'] = array(
    '#type' => 'fieldset',
    '#title' => 'Facebook Audience App',
  );
  $form['klicktipp_facebook_audience']['klicktipp_facebook_audience_appkey'] = array(
    '#type' => 'textfield',
    '#title' => 'Application key',
    '#default_value' => variable_get('klicktipp_facebook_audience_appkey', ''),
  );
  $form['klicktipp_facebook_audience']['klicktipp_facebook_audience_appsecret'] = array(
    '#type' => 'textfield',
    '#title' => 'Application secret',
    '#default_value' => variable_get('klicktipp_facebook_audience_appsecret', ''),
  );
  $form['klicktipp_facebook_audience']['klicktipp_facebook_audience_api_version'] = array(
    '#type' => 'textfield',
    '#title' => 'Facebook API version',
    '#default_value' => Settings::get('klicktipp_facebook_audience_api_version'),
    '#description' => 'Enter the current Facebook Graph API version. Format: v2.9',
  );

  return system_settings_form($form);
}

function klicktipp_settings_confirmation($form, &$form_state) {
  $form['klicktipp_confirmation'] = array(
    '#type' => 'fieldset',
    '#title' => 'Confirmation email template',
  );
  $form['klicktipp_confirmation']['klicktipp_confirmation_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_confirmation_subject', ''),
  );
  $form['klicktipp_confirmation']['klicktipp_confirmation_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#rows' => 50,
    '#cols' => 120,
    '#default_value' => variable_get('klicktipp_confirmation_body', ''),
    '#attributes' => array('style' => 'font-family: monospace;width:700px;'),
  );
  $form['klicktipp_confirmation']['klicktipp_confirmation_free_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body (Privilege: Signatures w/o Parameters)',
    '#rows' => 50,
    '#cols' => 120,
    '#default_value' => variable_get('klicktipp_confirmation_free_body', variable_get('klicktipp_confirmation_body', '')),
    '#attributes' => array('style' => 'font-family: monospace;width:700px;'),
  );

  $form['klicktipp_senderaddress'] = array(
    '#type' => 'fieldset',
    '#title' => 'Sender address verification email template',
    '#description' => 'This email is send to the customer, when he adds a new sender email address to his account.',
  );
  $form['klicktipp_senderaddress']['klicktipp_senderaddress_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_senderaddress_subject', 'New sender email address verification request for !username at !site'),
    '#description' => "The placeholders !username and !site will be replaced by the username and the site name.",
    '#size' => 60,
    '#maxlength' => 256,
    '#required' => TRUE,
  );
  $form['klicktipp_senderaddress']['klicktipp_senderaddress_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#description' => "The placeholders !username, !site and !email_url will be replaced by the username and the verification link.",
    '#default_value' => variable_get('klicktipp_senderaddress_body',
      'Hello !username,

A request to add a new sender email address has been made at !site.
You need to verify the address by clicking on the link below or by
copying and pasting it in your browser:

!email_url

If you do not click the link to confirm, your email address
at !site will not be usable as a sender.

Kind regards
Your team at !site
'),
    '#cols' => 80,
    '#rows' => 10,
    '#required' => TRUE,
  );

  $form['klicktipp_replyaddress'] = array(
    '#type' => 'fieldset',
    '#title' => 'Replay address verification email template',
    '#description' => 'This email is send to the customer, when he adds a new reply email address to his account.',
  );
  $form['klicktipp_replyaddress']['klicktipp_replyaddress_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_replyaddress_subject', 'New reply email address verification request for !username at !site'),
    '#description' => "The placeholders !username and !site will be replaced by the username and the site name.",
    '#size' => 60,
    '#maxlength' => 256,
    '#required' => TRUE,
  );
  $form['klicktipp_replyaddress']['klicktipp_replyaddress_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#description' => "The placeholders !username, !site and !email_url will be replaced by the username and the verification link.",
    '#default_value' => variable_get('klicktipp_replyaddress_body',
      'Hello !username,

A request to add a new reply email address has been made at !site.
You need to verify the address by clicking on the link below or by
copying and pasting it in your browser:

!email_url

If you do not click the link to confirm, your email address
at !site will not be usable as a reply address.

Kind regards
Your team at !site
'),
    '#cols' => 80,
    '#rows' => 10,
    '#required' => TRUE,
  );


  // --- SMS notification email address verification email template START

  $form['klicktipp_smsnotification'] = array(
    '#type' => 'fieldset',
    '#title' => 'SMS notification email address verification email template',
    '#description' => 'This email is send to the customer, when he adds an SMS notification email address to his account.',
  );
  $form['klicktipp_smsnotification']['klicktipp_smsnotification_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_smsnotification_subject', 'SMS notification email address verification request for !username at !site'),
    '#description' => "The placeholders !username and !site will be replaced by the username and the site name.",
    '#size' => 60,
    '#maxlength' => 256,
    '#required' => TRUE,
  );
  $form['klicktipp_smsnotification']['klicktipp_smsnotification_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#description' => "The placeholders !username, !site and !email_url will be replaced by the username and the verification link.",
    '#default_value' => variable_get('klicktipp_smsnotification_body',
      'Hello !username,

A request to add an SMS notification email address has been made at !site.
You need to verify the address by clicking on the link below or by
copying and pasting it in your browser:

!email_url

If you do not click the link to confirm, your email address
at !site will not be usable for SMS forwarding.

Kind regards
Your team at !site
'),
    '#cols' => 80,
    '#rows' => 10,
    '#required' => TRUE,
  );

  // --- SMS notification email address verification email template END

  $form['klicktipp_email_provider'] = array(
    '#type' => 'fieldset',
    '#title' => 'Optin confirmation email provider',
    '#description' => 'Provide the subscriber with a link to his email provider on the optin confirmation page.',
  );

  $ArrayProvider = variable_get(KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER, array());
  $TextProvider = "";
  if (!empty($ArrayProvider)) {
    foreach ($ArrayProvider as $domain => $link) {
      $TextProvider .= "$domain|{$link['link_title']}|{$link['link_url']}\n";
    }
  }
  $form['klicktipp_email_provider'][KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER] = array(
    '#type' => 'textarea',
    '#title' => 'Email provider',
    '#default_value' => trim($TextProvider),
    '#cols' => 80,
    '#rows' => 10,
    '#description' => 'Enter one pattern per line.<br />Format: provider domain without top-level | link title | link to inbox<br />Example: gmx|GMX|http://www.gmx.com',
  );

  // --- Subaccount agency access notification email template START

  $form['klicktipp_subaccount'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp subaccount agency access notification email template',
    '#description' => 'This email will be send to the agency, as soon as a customer has granted (created) an access to his account.',
  );

  $form['klicktipp_subaccount']['klicktipp_agency_access_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_agency_access_subject', '!firstname !lastname (!company) has granted access for !username at !site'),
    '#description' => "placeholders: !firstname !lastname !company (of the customer) !username (of the agency) !switchlink !site !date",
    '#size' => 60,
    '#maxlength' => 256,
    '#required' => TRUE,
  );

  $form['klicktipp_subaccount']['klicktipp_agency_access_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#description' => "placeholders: !firstname !lastname !company (of the customer) !username (of the agency) !switchlink !site !date",
    '#default_value' => variable_get('klicktipp_agency_access_body',
      'Hello !username,<br/>
<br/>
Your customer !firstname !lastname (!company) has granted access to !site.<br/>
You may switch to this account by using the following link:<br/>
<br/>
!switchlink<br/>
<br/>
Important: You need to be logged in to !site to access the link!<br/>
<br/>
Kind regards<br/>
Your team at !site<br/>
'),
    '#cols' => 80,
    '#rows' => 10,
    '#required' => TRUE,
  );

  // --- Subaccount agency access notification email template END

  // --- Privacy information email template START

  $form['klicktipp_privacy_information'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp Privacy information email template',
  );

  $form['klicktipp_privacy_information']['klicktipp_privacy_information_email_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_privacy_information_email_subject', 'Answer to your information request'),
    '#size' => 60,
    '#required' => TRUE,
  );

  $form['klicktipp_privacy_information']['klicktipp_privacy_information_email_header'] = array(
    '#type' => 'textarea',
    '#title' => 'Header',
    '#default_value' => variable_get('klicktipp_privacy_information_email_header', ''),
    '#cols' => 80,
    '#rows' => 10,
    '#required' => TRUE,
  );

  $form['klicktipp_privacy_information']['klicktipp_privacy_information_email_footer'] = array(
    '#type' => 'textarea',
    '#title' => 'Footer',
    '#default_value' => variable_get('klicktipp_privacy_information_email_footer', ''),
    '#cols' => 80,
    '#rows' => 10,
    '#required' => TRUE,
  );

  // --- Privacy information email template END

  $form['#validate'][] = 'klicktipp_settings_confirmation_validate';
  $form['#submit'][] = 'klicktipp_settings_confirmation_submit';

  return system_settings_form($form);
}

function klicktipp_settings_confirmation_validate($form, &$form_state) {
  Libraries::include('htmlfilter.inc');
  if (!empty($form_state['values']['klicktipp_confirmation_body'])) {
    $body = $form_state['values']['klicktipp_confirmation_body'];
    $pos = _klicktipp_html_validate($body);
    if ($pos >= 0) {
      form_set_error('klicktipp_confirmation_body', "Confirmation E-mail body. Invalid html found at position $pos:" . mb_strcut($body, $pos, 30));
    }
  }
  if (!empty($form_state['values']['klicktipp_confirmation_free_body'])) {
    $body = $form_state['values']['klicktipp_confirmation_free_body'];
    $pos = _klicktipp_html_validate($body);
    if ($pos >= 0) {
      form_set_error('klicktipp_confirmation_free_body', "Confirmation E-mail body (Signatures w/o Parameters). Invalid html found at position $pos: " . mb_strcut($body, $pos, 30));
    }
  }

  if (!empty($form_state['values'][KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER])) {

    $provider_data = explode("\n", $form_state['values'][KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER]);

    $line = 1;
    foreach ($provider_data as $data) {

      if (empty($data)) {
        continue;
      }

      $p = explode('|', trim($data));

      if (empty($p[0]) || empty($p[1]) || empty($p[2])) {
        form_set_error(KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER, "Invalid format for email provider in line $line ($data).");
      }
      else {
        if (!valid_url($p[2], TRUE)) {
          form_set_error(KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER, "Invalid email provider URL in line $line.");
        }
      }

      $line++;

    }
  }

}

function klicktipp_settings_confirmation_submit($form, &$form_state) {
  $ArrayProvider = array();

  if (!empty($form_state['values'][KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER])) {

    $provider_data = explode("\n", $form_state['values'][KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER]);

    foreach ($provider_data as $data) {

      $p = explode('|', trim($data));

      $ArrayProvider[$p[0]] = array(
        'link_title' => $p[1],
        'link_url' => $p[2],
      );

    }

  }

  $form_state['values'][KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER] = $ArrayProvider;
}

function klicktipp_settings_signatures($form, &$form_state) {
  $form['klicktipp_signatures'] = array(
    '#type' => 'fieldset',
    '#title' => 'Default Signatures',
  );
  $form['klicktipp_signatures']['klicktipp_plainsignature_default'] = array(
    '#type' => 'textarea',
    '#title' => 'Plain',
    '#rows' => 20,
    '#cols' => 68,
    '#default_value' => variable_get('klicktipp_plainsignature_default', ''),
    '#attributes' => array('style' => 'font-family: monospace;width:500px;'),
  );
  $form['klicktipp_signatures']['klicktipp_htmlsignature_default'] = array(
    '#type' => 'textarea',
    '#title' => 'HTML',
    '#rows' => 20,
    '#cols' => 68,
    '#default_value' => variable_get('klicktipp_htmlsignature_default', ''),
    '#attributes' => array('style' => 'width:500px;'),
  );
  $form['klicktipp_signatures']['klicktipp_htmlsignature_transactional'] = array(
    '#type' => 'textarea',
    '#title' => 'Transactional HTML',
    '#rows' => 20,
    '#cols' => 68,
    '#default_value' => variable_get('klicktipp_htmlsignature_transactional', ''),
    '#attributes' => array('style' => 'width:500px;'),
  );


  $form['#validate'][] = 'klicktipp_settings_signatures_validate';

  return system_settings_form($form);
}

function klicktipp_settings_signatures_validate($form, &$form_state) {
  $checkHtmlSignature = function ($element) use ($form, $form_state) {
    if (empty($form_state['values'][$element])) {
      return;
    }
    Libraries::include('htmlfilter.inc');

    $body = $form_state['values'][$element];
    $pos = _klicktipp_html_validate($body);
    if ($pos >= 0) {
      form_set_error($element, "HTML Signature. Invalid html found at position $pos: " . mb_strcut($body, $pos, 30));
    }

    //check spam score
    $MaxScore = variable_get(KLICKTIPP_SPAMSCORE_SIGNATURE, '');
    // check spam score
    $SpamReport = Emails::GetSPAMRating('signature', $form_state['values']['uid'],
      $form_state['values']['klicktipp_plainsignature_default'], $form_state['values'][$element]);
    // show score
    $Scores = '';
    foreach ($SpamReport['Scores'] as $Each) {
      $Scores .= "<li>{$Each['Description']} ({$Each['Point']} points)</li>";
    }
    $Message = "The signature spam score is {$SpamReport['Hits']} (max $MaxScore). <ul>$Scores</ul>";

    if ($SpamReport['Hits'] > $MaxScore) {
      // validation failed, signature wont be save
      form_set_error($element, $Message);
    }
    else {
      drupal_set_message($Message);
    }
  };
  $checkHtmlSignature('klicktipp_htmlsignature_default');
  $checkHtmlSignature('klicktipp_htmlsignature_transactional');

}

function klicktipp_settings_trash($form, &$form_state) {
  // Captcha
    $form['captcha'] = array(
        '#type' => 'fieldset',
        '#title' => 'Captcha',
    );
    $form['captcha']['activate_captcha_for_subscription_forms'] = array(
        '#type' => 'checkbox',
        '#title' => 'Activate captcha for legacy subscription forms',
        '#default_value' => Settings::get('activate_captcha_for_subscription_forms'),
        '#return_value' => 1,
        '#description' => 'Uncheck if problems occur with the CAPTCHA in the subscription forms',
    );

  // Mail system
  $form['mail_system_settings'] = array(
    '#type' => 'fieldset',
    '#title' => 'Mail system',
  );
  $form['mail_system_settings']['subscriber_mails_per_day_maximum'] = array(
    '#title' => 'Maximum amount of mails per day',
    '#type' => 'select',
    '#options' => [3 => 3, 4 => 4, 5 => 5, 6 => 6, 7 => 7, 8 => 8, 9 => 9 , 10 => 10],
    '#default_value' => variable_get('subscriber_mails_per_day_maximum', 3),
    '#description' => 'Maximum amount of recurring mails per day sent to a Subscriber.',
  );
  $mail_modes = variable_get('mail_system', array('default-system' => 'DefaultMailSystem'));
  $form['mail_system_settings']['klicktipp_mail_system'] = array(
    '#title' => 'Mail system used (for drupal mails)',
    '#type' => 'select',
    '#options' => array(
      'DefaultMailSystem' => 'DefaultMailSystem (php mail)',
      'KlicktippMailSystem' => 'Modified for HTML emails (php mail)',
      'KlicktippQueueMailSystem' => 'Klicktipp Mail System (send queue)'
    ),
    '#default_value' => $mail_modes['default-system'],
    '#description' => 'Choose system to send system emails.',
  );

  // Bounce settings
  $form['bounce_settings'] = array(
    '#type' => 'fieldset',
    '#title' => 'Bounce settings',
  );
  $form['bounce_settings']['klicktipp_bouncecheck_limit'] = array(
    '#type' => 'textfield',
    '#title' => 'Count of hard bounces that are checked per minute',
    '#default_value' => Settings::get('klicktipp_bouncecheck_limit'),
    '#description' => 'Count of hard bounces that are automatically checked per minute (1-99).',
  );
  $form['bounce_settings']['bouncelog_bouncecheck'] = array(
    '#type' => 'checkbox',
    '#title' => 'Log bounce check result',
    '#default_value' => Settings::get('bouncelog_bouncecheck'),
    '#description' => 'Enable bounce check log. Results of a deliverable/undeliverable request header. ' .
      'We need to check these bounces from time to time to improve filters.',
  );
  $form['bounce_settings']['klicktipp_softbouncecheck_limit'] = array(
    '#type' => 'textfield',
    '#title' => 'Count of soft bounces that are checked per minute',
    '#default_value' => Settings::get('klicktipp_softbouncecheck_limit'),
    '#description' => 'Count of soft bounces that are automatically checked per minute (1-99).',
  );
  $form['bounce_settings']['klicktipp_softbounce_blocking'] = array(
    '#type' => 'textfield',
    '#title' => 'Time to block address after SOFT bounce (days)',
    '#default_value' => Settings::get('klicktipp_softbounce_blocking'),
    '#description' => 'Give number of days, a SOFT bounce Subscriber E-Mail-Address will be blocked.',
  );
  $form['bounce_settings']['klicktipp_spambounce_blocking'] = array(
    '#type' => 'textfield',
    '#title' => 'Time to block address after SPAM bounce (days)',
    '#default_value' => Settings::get('klicktipp_spambounce_blocking'),
    '#description' => 'Give number of days, a SPAM bounce Subscriber E-Mail-Address will be blocked.',
  );
  $form['bounce_settings']['bouncelog_bouncetypeunknown'] = array(
    '#type' => 'checkbox',
    '#title' => 'Log bounces, we cannot classify',
    '#default_value' => Settings::get('bouncelog_bouncetypeunknown'),
    '#description' => 'Enable bounce log for bounces that we can\'t classify as HARD, SOFT or SPAM. ' .
      'These bounces become soft bounces. ' .
      'We need to check these bounces from time to time.',
  );
  $form['bounce_settings']['bouncelog_notabounce'] = array(
    '#type' => 'checkbox',
    '#title' => 'Log bounces, that do not have a bounce address',
    '#default_value' => Settings::get('bouncelog_notabounce'),
    '#description' => 'Enable bounce log for bounces that are not sent to a bounce-address, ' .
      'e.g. a non-existing klicktipp system or request address. ' .
      'As we cannot assign these bounces to a customer, these bounces are ignored. ' .
      'We need to check these bounces from time to time.',
  );

  // X-Abuse-Info SMTP header
  $varsemail = "abuse@" . variable_get('klicktipp_shared_sender_domain', KLICKTIPP_DOMAIN);
  $varsurl = variable_get('klicktipp_shared_alias', KLICKTIPP_DOMAIN) . '/abuse/' . 'X-MessageID';
  $form['klicktipp_abuseheader'] = array(
    '#type' => 'fieldset',
    '#title' => 'Email abuse header',
    '#description' => "Configure X-Abuse-Info in email headers. Variables @abuseemail = $varsemail, @abuseurl = $varsurl ",
  );
  $form['klicktipp_abuseheader']['klicktipp_abuseheader_line1'] = array(
    '#type' => 'textfield',
    '#title' => 'X-Abuse-Info - Line 1',
    '#size' => 130,
    '#default_value' => variable_get('klicktipp_abuseheader_line1', 'Please forward a copy of this message, including all headers, to @abuseemail'),
    '#description' => 'ONLY ASCII CHARACTERS ALLOWED.',
  );
  $form['klicktipp_abuseheader']['klicktipp_abuseheader_line2'] = array(
    '#type' => 'textfield',
    '#title' => 'X-Abuse-Info - Line 2',
    '#size' => 130,
    '#default_value' => variable_get('klicktipp_abuseheader_line2', 'You can also report abuse here: @abuseurl'),
    '#description' => 'ONLY ASCII CHARACTERS ALLOWED.',
  );

  // "report spam" links at top of email
  $form['klicktipp_spamreport'] = array(
    '#type' => 'fieldset',
    '#title' => 'Spam report',
    '#description' => 'Configure the spam report links at top of an email.',
  );
  $form['klicktipp_spamreport']['klicktipp_spamreport_text'] = array(
    '#type' => 'textfield',
    '#title' => 'Text for spam reporting',
    '#default_value' => variable_get('klicktipp_spamreport_text', 'Report Spam'),
  );
  $form['klicktipp_spamreport']['klicktipp_spamreport_unsubscribe'] = array(
    '#type' => 'textfield',
    '#title' => 'Text for unsubscribe link',
    '#default_value' => variable_get('klicktipp_spamreport_unsubscribe', 'Unsubscribe'),
  );
  $form['klicktipp_spamreport']['klicktipp_spamreport_browserview'] = array(
    '#type' => 'textfield',
    '#title' => 'Text for browser view link',
    '#default_value' => variable_get('klicktipp_spamreport_browserview', 'View E-Mail in Web-Browser'),
  );
  $form['klicktipp_spamreport']['klicktipp_spamreport_separator'] = array(
    '#type' => 'textfield',
    '#title' => 'Separator',
    '#maxlength' => 255,
    '#default_value' => Settings::get('klicktipp_spamreport_separator'),
    '#element_validate' => array(), //allow <> for <img>-Tag
  );
  $form['klicktipp_spamreport']['klicktipp_spamreport_helplink'] = array(
    '#type' => 'textfield',
    '#title' => 'Link to handbook page',
    '#default_value' => variable_get('klicktipp_spamreport_helplink', ''),
  );


  $form['klicktipp_spam'] = array(
    '#type' => 'fieldset',
    '#title' => 'Spam settings',
  );
  $form['klicktipp_spam'][KLICKTIPP_SPAMSCORE_EMAIL] = array(
    '#type' => 'textfield',
    '#title' => 'Maximum Spam Score in an email content',
    '#default_value' => variable_get(KLICKTIPP_SPAMSCORE_EMAIL, ''),
  );
  $form['klicktipp_spam'][KLICKTIPP_SPAMSCORE_SIGNATURE] = array(
    '#type' => 'textfield',
    '#title' => 'Maximum Spam Score in a signature',
    '#default_value' => variable_get(KLICKTIPP_SPAMSCORE_SIGNATURE, ''),
  );
  $form['klicktipp_spam'][KLICKTIPP_SPAMASSASSIN] = array(
    '#type' => 'textfield',
    '#title' => 'Spamassassin Parameters',
    '#default_value' => variable_get(KLICKTIPP_SPAMASSASSIN, '-Lt'),
  );
  $form['klicktipp_spam']['klicktipp_spam_external_check_url'] = array(
    '#type' => 'textfield',
    '#title' => 'External check url',
    '#description' => 'if set, klicktipp sends contant of saved emails to specified url',
    '#default_value' => variable_get('klicktipp_spam_external_check_url', ''),
  );

  $form['klicktipp_spamtrap'] = array(
    '#type' => 'fieldset',
    '#title' => 'Spam trap settings',
  );
  $form['klicktipp_spamtrap']['klicktipp_zy0de_enabled'] = array(
    '#type' => 'checkbox',
    '#title' => 'Enable zy0.de spam trap fetch job',
    '#default_value' => variable_get('klicktipp_zy0de_enabled', 0),
  );

  $form['klicktipp_import_restrictions'] = [
    '#type' => 'fieldset',
    '#title' => 'Import restrictions',
  ];

  $form['klicktipp_import_restrictions']['klicktipp_min_age_of_standard_account'] = [
    '#type' => 'textfield',
    '#title' => 'Minimum age of standard accounts for import (in days)',
    '#default_value' => variable_get('klicktipp_min_age_of_standard_account', 45),
    '#element_validate' => array('element_validate_integer_positive'),
    '#description' => 'Younger accounts are not able to use import dialog',
  ];

  $form['klicktipp_spam_detection'] = [
    '#type' => 'fieldset',
    '#title' => 'Spam account detection settings',
  ];

  $form['klicktipp_spam_detection']['klicktipp_spam_detection_account_age'] = [
    '#type' => 'textfield',
    '#title' => 'Maximum age of considered accounts (in days)',
    '#default_value' => variable_get('klicktipp_spam_detection_account_age', 31),
    '#element_validate' => array('element_validate_integer_positive'),
  ];

  $form['klicktipp_spam_detection']['klicktipp_spam_detection_number_of_unconfirmed_subscriptions'] = [
    '#type' => 'textfield',
    '#title' => 'Minimum number of unconfirmed subscription in account',
    '#default_value' => variable_get('klicktipp_spam_detection_number_of_unconfirmed_subscriptions', 1000),
    '#element_validate' => array('element_validate_integer_positive'),
  ];

  $form['klicktipp_spam_detection']['klicktipp_spam_detection_number_of_deleted_unconfirmed_subscriptions'] = [
    '#type' => 'textfield',
    '#title' => 'Minimum number of deleted unconfirmed subscription in account',
    '#default_value' => variable_get('klicktipp_spam_detection_number_of_deleted_unconfirmed_subscriptions', 100),
    '#element_validate' => array('element_validate_integer_positive'),
  ];

  $form['klicktipp_spam_detection']['klicktipp_spam_detection_age_of_deleted_unconfirmed_subscriptions'] = [
    '#type' => 'textfield',
    '#title' => 'Maximum age of deleted unconfirmed subscription in account',
    '#default_value' => variable_get('klicktipp_spam_detection_age_of_deleted_unconfirmed_subscriptions', 7),
    '#element_validate' => array('element_validate_integer_positive'),
  ];

  $form['klicktipp_user_blacklist'] = array(
    '#type' => 'fieldset',
    '#title' => 'User blacklist',
  );

  $form['klicktipp_user_blacklist']['klicktipp_nonblockable_emails'] = array(
    '#type' => 'textarea',
    '#title' => 'E-mails that should not be blocked by user blacklist',
    '#rows' => 20,
    '#cols' => 68,
    '#default_value' => Settings::get('klicktipp_nonblockable_emails'),
    '#description' => 'We check user blacklist agains this e-mail adresses. We reject user blacklist as to too dangerous, if one of the them would be blocked.',
  );

  $form['#submit'][] = 'klicktipp_settings_trash_submit';

  $form['klicktipp_app emails'] = array(
    '#type' => 'fieldset',
    '#title' => 'List-Help header settings',
    '#description' => strtr('List-Help header settings (for emails without unsubscription link). Header based on current settings (domain of url might be an example): "!value"',
      ['!value' => htmlentities(strtr(variable_get('klicktipp_list_help_header', ''), ['!domain' => 'klick.example-host.com']))]
    )
  );

  $form['klicktipp_app emails']['klicktipp_list_help_email'] = array(
    '#type' => 'textfield',
    '#title' => 'Email for "List-Help" header',
    '#description' => 'Email address to get to get explanations why application mails are sent and why receivers can not unsubscribe.',
    '#default_value' => variable_get('klicktipp_list_help_email', '')
  );

  $form['klicktipp_app emails']['klicktipp_list_help_url'] = array(
    '#type' => 'textfield',
    '#title' => 'Url for "List-Help" header',
    '#description' => "Path or url to a page explaining why application mails are sent and why receivers can not unsubscribe.<br /><br />Examples for a path: csa-compliance or /csa-compliance.<br />If a path is provided the users sender domain will be used as domain. So /my-path will be adjusted to something like  https://klick.my-domain.de/my-path<br /><br />Examples for an url: https://my-domain.de/csa-compliance<br />If an url is provided there will be no adjustments and it will be set into the header as is.",
    '#default_value' => variable_get('klicktipp_list_help_url', '')
  );

  $form['klicktipp_forbidden_as_sender'] = array(
    '#type' => 'fieldset',
    '#title' => 'Sender address',
  );

  $form['klicktipp_forbidden_as_sender']['klicktipp_forbidden_as_sender_patterns'] = array(
    '#type' => 'textarea',
    '#title' => 'Reject as sender address',
    '#rows' => 20,
    '#cols' => 68,
    '#default_value' => variable_get('klicktipp_forbidden_as_sender_patterns', ''),
    '#description' => 'Enter one pattern per line, use wildcards: * = any char, ? = one char, ^ = start, $ = end of string.',
  );
  $form['klicktipp_forbidden_as_sender']['klicktipp_spf_domain'] = array(
    '#type' => 'textfield',
    '#title' => 'SPF domain',
    '#required' => TRUE,
    '#default_value' => Settings::get('klicktipp_spf_domain'),
    '#description' => 'Enter a domain to be included to the SPF of the domains of additional sender addresses.',
  );

  $form['klicktipp_email_blacklisting'] = array(
    '#type' => 'fieldset',
    '#title' => 'Email Blacklisting',
  );

  $blacklistImplementations =  [
    SpamhausBlacklist::TYPE => 'Spamhaus',
    UriblBlacklist::TYPE =>  'URIBL',
    ShortenerBlacklist::TYPE => 'Shortener',
    GoogleWebRiskBlacklist::TYPE => 'Google Web Risk'
  ];

  $form['klicktipp_email_blacklisting']['klicktipp_active_blacklists_editor'] = array(
    '#type' => 'checkboxes',
    '#title' => 'Blacklists to check against in editor',
    '#default_value' => Settings::get('klicktipp_active_blacklists_editor'),
    '#options' => $blacklistImplementations
  );

  $form['klicktipp_email_blacklisting']['klicktipp_log_in_editor'] = array(
    '#type' => 'checkbox',
    '#title' => 'Log blacklisted entries in editor',
    '#default_value' => variable_get('klicktipp_log_in_editor', FALSE),
    '#description' => 'If set klicktipp will log occurrences of blacklisted url in editor'
  );

  $form['klicktipp_email_blacklisting']['klicktipp_blacklist_shortener_block_save_email'] = array(
    '#type' => 'checkbox',
    '#title' => 'Block saving email if it contains shortener (editor)',
    '#default_value' => Settings::get('klicktipp_blacklist_shortener_block_save_email'),
    '#description' => 'This option only applies if shorteners are activated above.'
  );

  $form['klicktipp_email_blacklisting']['klicktipp_blacklist_shortener_spam_points'] = array(
    '#type' => 'textfield',
    '#title' => 'Spam points added to SpamScore for shortener',
    '#default_value' => Settings::get('klicktipp_blacklist_shortener_spam_points'),
    '#description' => 'This option only applies if shorteners are not blocked when saving the email (deactivated option above)'
  );

  $form['klicktipp_email_blacklisting']['klicktipp_active_blacklists_redirect'] = array(
    '#type' => 'checkboxes',
    '#title' => 'Blacklists to check against before redirects',
    '#default_value' => Settings::get('klicktipp_active_blacklists_redirect'),
    '#options' => $blacklistImplementations
  );

  $form['klicktipp_email_blacklisting']['klicktipp_blacklist_check_whitelist'] = array(
    '#type' => 'textarea',
    '#title' => 'Whitelisted domains (patterns)',
    '#rows' => 10,
    '#default_value' => variable_get('klicktipp_blacklist_check_whitelist', ''),
    '#description' => 'If a domain matches one of this patter, we skip all blacklist checks. ' .
      'Enter one pattern per line, use wildcards: * = any char, ? = one char, ^ = start, $ = end of string.',
  );

  $form['klicktipp_email_blacklisting']['klicktipp_dnsbl_cache_ttl'] = array(
    '#type' => 'textfield',
    '#title' => 'Cache TTL for blacklist check results (in seconds)',
    '#default_value' => variable_get('klicktipp_dnsbl_cache_ttl', '1'),
    '#element_validate' => array('element_validate_integer_positive'),
    '#description' => 'Lifetime of blacklist check results in cache. Enter a number. If number is less than 1, timeout will be 1. ',
  );

  $form['klicktipp_email_blacklisting']['klicktipp_blacklist_shortener'] = array(
    '#type' => 'textarea',
    '#title' => 'Blacklisted shortener domains',
    '#rows' => 10,
    '#default_value' => variable_get('klicktipp_blacklist_shortener', ''),
    '#description' => 'Enter one domain per line.',
  );

  $form['klicktipp_email_blacklisting']['klicktipp_max_number_of_redirects_to_follow'] = array(
    '#type' => 'textfield',
    '#title' => 'Max number of redirects to follow',
    '#default_value' => variable_get('klicktipp_max_number_of_redirects_to_follow', 0),
    '#element_validate' => array('element_validate_integer_positive'),
    '#description' => 'Max number of redirects to follow. To prevent following redirects, let this value empty'
  );

  $form['klicktipp_email_blacklisting']['klicktipp_log_exceeding_of_redirect_limit'] = array(
    '#type' => 'checkbox',
    '#title' => 'Write a log entry if max number of redirects was exceeded? ',
    '#default_value' => variable_get('klicktipp_log_exceeding_of_redirect_limit', 1),
    '#description' => 'E.g. If a redirect chain has 4 redirects and "Max number of redirects to follow", should we log it?',
  );

  $form['klicktipp_email_blacklisting']['klicktipp_dnsbl_timeout'] = array(
    '#type' => 'textfield',
    '#title' => 'DNSBL request timeout',
    '#default_value' => variable_get('klicktipp_dnsbl_timeout', '300'),
    '#element_validate' => array('element_validate_integer_positive'),
    '#description' => 'Max execution time for DNSBL request (takes effect for Spamhaus and URIBL). ' .
      'If this time exceedes, we stop request and consider domain as not blacklisted. ' .
      'Enter a number. NOTE: 0 means infinite (until cache gets cleared) caching'
  );

  $form['klicktipp_email_blacklisting']['klicktipp_redirect_resolution_timeout'] = array(
    '#type' => 'textfield',
    '#title' => 'Redirect resolution request timeout',
    '#default_value' => variable_get('klicktipp_redirect_resolution_timeout', 1),
    '#element_validate' => array('element_validate_integer_positive'),
    '#description' => 'Max execution time to fetch target url of a redirect. ' .
      'If this time exceedes, we stop request and continue without following the redirect. ' .
      'Enter a number. NOTE: empty values means no timeout'
  );

  $form['klicktipp_email_blacklisting']['klicktipp_spamhaus_datafeed'] = array(
    '#type' => 'textfield',
    '#title' => 'Spamhaus DNSBL Datafeed',
    '#default_value' => variable_get('klicktipp_spamhaus_datafeed', '')
  );

  $form['klicktipp_email_blacklisting']['uribl'] = array(
    '#type' => 'fieldset',
    '#title' => 'URIBL',
  );

  $form['klicktipp_email_blacklisting']['uribl']['klicktipp_uribl_datafeed'] = array(
    '#type' => 'textfield',
    '#title' => 'URIBL DNSBL Datafeed',
    '#default_value' => variable_get('klicktipp_uribl_datafeed', '')
  );

  $form['klicktipp_email_blacklisting']['uribl']['klicktipp_uribl_accepted_lists'] = array(
    '#type' => 'select',
    '#multiple' => TRUE,
    '#title' => 'URIBL accepted list types',
    // Keys are supposed to appear in TXT line of uribl. e.g. if only 'Blacklisted' and 'Redlisted' are selected, we consider only records starting with 'Blacklisted' or 'Redlisted'
    '#options' => array_combine(UriblBlacklist::URLBL_LISTING_TYPES, UriblBlacklist::URLBL_LISTING_TYPES),
    '#default_value' => Settings::get('klicktipp_uribl_accepted_lists'),
    '#description' => 'NOTE: this property only take affect for URIBL (if it is active). ' .
      'For more Details about list types see <a target="_blank" href="http://uribl.com/about.shtml#info">http://uribl.com/about.shtml#info</a>. ' .
      'Check also links for information about <a target="_blank" href="https://help.infusionsoft.com/help/uribl-greylisting">Greylisting</a> and <a target="_blank" href="http://uribl.com/gold.shtml">Goldlisting</a>. ' .
      'We ignore hits related to list types which are not selected here. Hist not matching. If a hit can not be assigned to any  known list, we log it.'
  );

  $form['klicktipp_email_blacklisting']['webrisk'] = array(
    '#type' => 'fieldset',
    '#title' => 'Google Webrisk',
  );

  $form['klicktipp_email_blacklisting']['webrisk']['klicktipp_blacklist_webrisk_check_url'] = array(
    '#type' => 'textfield',
    '#title' => 'Website status check url',
    '#default_value' => Settings::get('klicktipp_blacklist_webrisk_check_url'),
    '#description' => 'Url to use by customers to check website status. Is displayed in editor error messages (if webrisk is configured to be used in editor).',
  );

  $form['klicktipp_email_blacklisting']['webrisk']['klicktipp_blacklist_webrisk_credentials'] = array(
    '#type' => 'textarea',
    '#title' => 'Webrisk credentials (keyfile content)',
    '#rows' => 10,
    '#default_value' => variable_get('klicktipp_blacklist_webrisk_credentials', ''),
    '#element_validate' => 'klicktipp_element_textfield_validate_url',
    '#description' => 'JSON-Content of keyfile generated in google webrisk projekt',
  );

  $form['klicktipp_email_blacklisting']['mock'] = array(
    '#type' => 'fieldset',
    '#title' => 'Mock mode for e2e tests',
    '#description' => 'In mock mode we do not check agains blacklist (excepted shorteners). Instead domains in a static list below are considered as blacklisted, other domains not. Please note that whitelist is considered in mock mode.'
  );

  $form['klicktipp_email_blacklisting']['mock']['klicktipp_blacklist_mock_mode_is_active'] = array(
    '#type' => 'checkbox',
    '#title' => 'Is block mode active ?',
    '#default_value' => variable_get('klicktipp_blacklist_mock_mode_is_active', 0),
    '#description' => 'IMPORTANT: do not activate mock mode in productive einvironments! It is supposed to be used for e2e test.',
  );


  $form['klicktipp_email_blacklisting']['mock']['klicktipp_blacklist_mock_blacklisted_domains'] = array(
    '#type' => 'textarea',
    '#title' => 'Blacklisted domains',
    '#rows' => 10,
    '#default_value' => variable_get('klicktipp_blacklist_mock_blacklisted_domains', ''),
    '#description' => 'Domain considered as blacklisted in mock mode. Enter one domain per line.',
  );

  $form['klicktipp_subdomain'] = array(
    '#type' => 'fieldset',
    '#title' => 'Subdomain Blacklist',
  );
  $form['klicktipp_subdomain']['klicktipp_subdomain_blacklist'] = array(
    '#type' => 'textarea',
    '#title' => 'Reject the following Subdomains',
    '#rows' => 20,
    '#cols' => 68,
    '#default_value' => variable_get('klicktipp_subdomain_blacklist', ''),
    '#description' => 'Enter one subdomain per line, wildcards are NOT supported.',
  );

  return system_settings_form($form);
}

function klicktipp_settings_trash_validate($form, &$form_state) {
  $nonblockable_emails = $form_state['values']['klicktipp_nonblockable_emails'];

  if (!empty($nonblockable_emails)) {
    foreach (explode("\n", $nonblockable_emails) as $email) {
      if (!filter_var(trim($email), FILTER_VALIDATE_EMAIL)) {
        form_set_error('klicktipp_nonblockable_emails', 'User blacklist: this field should contain only valid e-mail addresses.');
        break;
      }
    }
  }
  $externalCheckField = 'klicktipp_spam_external_check_url';
  if (!empty($form_state['values'][$externalCheckField]) && !valid_url($form_state['values'][$externalCheckField], TRUE)) {
    form_set_error($externalCheckField, $form['klicktipp_spam'][$externalCheckField]['#title'] . ': Invalid URL');
  }
  if (!empty($form_state['values']['klicktipp_list_help_url']) && !valid_url($form_state['values']['klicktipp_list_help_url'])) {
    form_set_error('klicktipp_list_help_url', $form['klicktipp_app emails']['klicktipp_list_help_url']['#title'] . ': Invalid URL');
  }
  if (!empty($form_state['values']['klicktipp_list_help_email']) && !valid_email_address($form_state['values']['klicktipp_list_help_email'])) {
    form_set_error('klicktipp_list_help_email', $form['klicktipp_app emails']['klicktipp_list_help_email']['#title'] . ': Invalid email address');
  }
  if (!filter_var('https://' . $form_state['values']['klicktipp_spf_domain'], FILTER_VALIDATE_URL)) {
    form_set_error('klicktipp_spf_domain', $form['klicktipp_forbidden_as_sender']['klicktipp_spf_domain']['#title'] . ': Invalid domain');
  }

  if (!is_numeric($form_state['values']['klicktipp_blacklist_shortener_spam_points'])) {
    form_set_error('klicktipp_blacklist_shortener_spam_points', 'Please enter a valid [float] number for the shortener spam points.');
  }

  if ($form_state['values']['klicktipp_active_blacklists_redirect']['webrisk'] ||
    $form_state['values']['klicktipp_active_blacklists_editor']['webrisk'] ||
    !empty($form_state['values']['klicktipp_blacklist_webrisk_credentials'])
  ) {
    $credentials = json_decode($form_state['values']['klicktipp_blacklist_webrisk_credentials'], TRUE);
    if (!is_array($credentials)) {
      form_set_error('klicktipp_blacklist_webrisk_credentials', 'Field content must be valid JSON');
    } else {
      try {
        new WebRiskServiceClient(['credentials' => $credentials]);
      } catch (Exception $e) {
        form_set_error('klicktipp_blacklist_webrisk_credentials', 'Webrisk credentials invalid: ' . $e->getMessage());
      }
    }
  }
}

function klicktipp_settings_trash_submit($form, &$form_state) {
  // set drupal mail system
  $mode = $form_state['values']['klicktipp_mail_system'];

  $mail_modes = variable_get('mail_system', array('default-system' => 'DefaultMailSystem'));
  $mail_modes['default-system'] = $mode;

  variable_set('mail_system', $mail_modes);
  variable_set('klicktipp_list_help_header', klicktipp_build_list_help_header($form_state['values']));
}

/**
 * Builds "List-Help" header.
 *
 * @param array $message
 *
 * @return string
 */
function klicktipp_build_list_help_header($values) {
    $listHelpParts = [];

    $listHelpUrl = $values['klicktipp_list_help_url'];
    if (!empty($listHelpUrl)) {
      // absolute url
      if (strpos($listHelpUrl, '://')) {
        $listHelpParts[] = '<' . $listHelpUrl . '>';
        // relative url
      } else {
        $listHelpParts[] = '<https://!domain/' . ltrim($listHelpUrl, '/') . '>';
      }
    }

    $listHelpEmail = $values['klicktipp_list_help_email'];
    if (!empty($listHelpEmail)) {
      $listHelpParts[] = '<mailto:' .  $listHelpEmail. '>';
    }

    return implode(', ',  $listHelpParts);
}

function klicktipp_settings_aliases($form, &$form_state) {

  $form['klicktipp_shared'] = array(
    '#type' => 'fieldset',
    '#title' => 'Shared Sender Domains',
  );
  $userwithwhitelabelconfig = Settings::get('shared_senderdomain_user');
  $link = l('whitelabel configuration', "user/$userwithwhitelabelconfig/domains", ['attributes' => ['target' => '_blank']]);
  $form['klicktipp_shared']['shared_senderdomain_user'] = array(
    '#type' => 'textfield',
    '#title' => 'User with whitelabel configuration',
    '#default_value' => $userwithwhitelabelconfig,
    '#description' => "User with $link for shared sender domains."
  );
  $form['klicktipp_shared']['shared_senderdomain_domains'] = array(
    '#type' => 'textfield',
    '#title' => 'Shared Domains',
    '#default_value' => Settings::get('shared_senderdomain_domains'),
    '#description' => 'Enter a json array of domains, e.g. ["alpha.com","beta.com","omega.com"]. Never add or delete a domain, just replace it, so the index of existing domains does not change. '.
      'To take one of the domains off, repeat another one at that place. e.g. ["alpha.com","alpha.com","omega.com"].',
    '#element_validate' => array(), // do not check for & < > " '
  );

  $form['klicktipp_shared']['new_shared_senderdomain_domains'] = array(
    '#type' => 'textfield',
    '#title' => 'New Shared Domains (tmp, until migration is complete)',
    '#default_value' => Settings::get('new_shared_senderdomain_domains'),
    '#description' => 'Enter a json array of domains, e.g. ["alpha.com","beta.com","omega.com"]. Never add or delete a domain, just replace it, so the index of existing domains does not change. '.
      'To take one of the domains off, repeat another one at that place. e.g. ["alpha.com","alpha.com","omega.com"].',
    '#element_validate' => array(), // do not check for & < > " '
  );

  $form['klicktipp_shared']['last_user_without_subdomains'] = array(
    '#type' => 'textfield',
    '#title' => 'Last without individual subdomains',
    '#default_value' => variable_get('last_user_without_subdomains', PHP_INT_MAX),
    '#description' => 'If not set at all, all users do not use individual shared subdomains. If specified, all users with higher uid use individual subdomains',
    '#element_validate' => array('element_validate_integer'),
  );

  $form['klicktipp_shared_failover'] = array(
    '#type' => 'fieldset',
    '#title' => 'Shared Sender Domains Failover',
  );
  $form['klicktipp_shared_failover']['klicktipp_shared_sender_domain'] = array(
    '#type' => 'textfield',
    '#title' => 'Domain',
    '#default_value' => variable_get('klicktipp_shared_sender_domain', KLICKTIPP_DOMAIN),
  );
  $form['klicktipp_shared_failover']['klicktipp_shared_return_path'] = array(
    '#type' => 'textfield',
    '#title' => 'Bounce host (return path)',
    '#default_value' => variable_get('klicktipp_shared_return_path', BOUNCE_CATCHALL_DOMAIN),
  );
  $form['klicktipp_shared_failover']['klicktipp_shared_alias'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias (CNAME)',
    '#default_value' => variable_get('klicktipp_shared_alias', KLICKTIPP_DOMAIN),
  );

  $form['klicktipp_appdomain'] = array(
    '#type' => 'fieldset',
    '#title' => 'Whitelabel CNAME'
  );

  $form['klicktipp_appdomain']['klicktipp_whitelabel_appdomain'] = array(
    '#type' => 'textfield',
    '#title' => 'CNAME',
    '#default_value' => variable_get('klicktipp_whitelabel_appdomain', 'wl.strold.io'),
    '#description' => 'CNAME used for common klick-host (ip that points to website).',
  );

  $form['klicktipp_appdomain']['klicktipp_shared_dkimdomain'] = array(
    '#type' => 'textfield',
    '#title' => 'CNAME for sender domains',
    '#default_value' => variable_get('klicktipp_shared_dkimdomain', 'strold.io'),
    '#description' => 'CNAME used for klick-hosts of sender-domains (shared users)'
  );

  $form['klicktipp_domaincheck_options'] = array(
    '#type' => 'fieldset',
    '#title' => 'Whitelabel CNAME'
  );

  $form['klicktipp_domaincheck_options']['klicktipp_invalidate_domains_without_dmarc'] = array(
    '#type' => 'checkbox',
    '#title' => 'Invalidate domains without dmarc record ?',
    '#default_value' => variable_get('klicktipp_invalidate_domains_without_dmarc', false),
  );

  $form['klicktipp_affdomain'] = array(
    '#type' => 'fieldset',
    '#title' => 'Affiliate link',
  );
  $form['klicktipp_affdomain']['amember_affiliate_url'] = array(
    '#type' => 'textfield',
    '#title' => 'URL (prefix) for affiliate links',
    '#default_value' => variable_get('amember_affiliate_url', APP_URL),
    '#description' => 'With trailing slash, e.g. https://www.example.com/',
  );

  $form['klicktipp_forwarders'] = array(
    '#type' => 'fieldset',
    '#title' => 'PMTA forwarders',
    '#description' => 'Static parts of the PMTA forwarders configuration in file ' .  PMTA_FORWARDERS_FILE,
  );
  $form['klicktipp_forwarders']['klicktipp_pmta_forwarders_support_address'] = array(
    '#type' => 'textfield',
    '#title' => 'Postmaster support address',
    '#default_value' => variable_get('klicktipp_pmta_forwarders_support_address', ''),
  );
  $form['klicktipp_forwarders']['klicktipp_pmta_forwarders_top'] = array(
    '#type' => 'textarea',
    '#title' => 'Top',
    '#default_value' => variable_get('klicktipp_pmta_forwarders_top', ''),
  );
  $form['klicktipp_forwarders']['klicktipp_pmta_forwarders_aliases'] = array(
    '#type' => 'textarea',
    '#title' => 'Aliases',
    '#default_value' => variable_get('klicktipp_pmta_forwarders_aliases', ''),
  );

  $form['klicktipp_shared_vmtas'] = array(
    '#type' => 'fieldset',
    '#title' => 'VMTA Pools',
    '#description' => 'Static part of the VMTA pools configuration in file ' . PMTA_VMTA_POOLS_FILE,
  );
  $form['klicktipp_shared_vmtas']['klicktipp_pmta_shared_vmta_pools'] = array(
    '#type' => 'textarea',
    '#title' => 'Shared VMTA Pools',
    '#default_value' => variable_get('klicktipp_pmta_shared_vmta_pools', ''),
  );

  $form['klicktipp_ip_mapping'] = array(
    '#type' => 'fieldset',
    '#title' => 'PMTA IP Mapping',
    '#description' => 'Mapping of Anexia Load Balancer IPs (external) to PMTA Sender IPs (internal)',
  );
  $form['klicktipp_ip_mapping']['klicktipp_pmta_ip_mapping'] = array(
    '#type' => 'textarea',
    '#title' => 'Mapping',
    '#default_value' => variable_get('klicktipp_pmta_ip_mapping', '213.227.171=172.17.171'),
    '#description' => 'One byte triple external => internal per line, e.g. 213.227.171=172.17.171',
  );

  $form['klicktipp_aliases'] = array(
    '#type' => 'fieldset',
    '#title' => 'Aliases',
    '#description' => '<span style="color:red;font-weight:bold;">NOTE: Do not forget to change .htaccess in root directory, as well!</span>',
  );
  $form['klicktipp_aliases']['klicktipp_aliases_opt_confirm'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for opt_confirm.php',
    '#default_value' => variable_get('klicktipp_aliases_opt_confirm', 'bestaetigen'),
  );
  $form['klicktipp_aliases']['klicktipp_aliases_track_link'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for link tracking',
    '#default_value' => variable_get('klicktipp_aliases_track_link', 'info'),
  );
  $form['klicktipp_aliases']['klicktipp_aliases_track_open'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for track_open.php',
    '#default_value' => variable_get('klicktipp_aliases_track_open', 'bilder'),
  );
  $form['klicktipp_aliases']['klicktipp_aliases_unsubscribe'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for unsubscribe.php',
    '#default_value' => variable_get('klicktipp_aliases_unsubscribe', 'abmelden'),
    '#description' => '<span style="color:red;font-weight:bold;">NOTE: Do not forget to change .htaccess in root directory AND forwarder from &lt;unsubscribe-alias&gt;@&lt;klicktipp-domain&gt; to "unsubscribe.php"!</span>',
  );
  $form['klicktipp_aliases']['klicktipp_aliases_web_browser'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for web_browser.php',
    '#default_value' => variable_get('klicktipp_aliases_web_browser', 'web'),
  );
  $form['klicktipp_aliases']['klicktipp_aliases_spam_report'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for spam_report.php',
    '#default_value' => variable_get('klicktipp_aliases_spam_report', 'spam'),
  );
  $form['klicktipp_aliases']['klicktipp_aliases_change_email'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for "change-email" link',
    '#default_value' => variable_get('klicktipp_aliases_change_email', 'change-email'),
  );
  $form['klicktipp_aliases']['klicktipp_aliases_unsubscription_feedback'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for "unsubscription feedback" link',
    '#default_value' => variable_get('klicktipp_aliases_unsubscription_feedback', 'ifeedback'),
  );
  $form['klicktipp_aliases']['klicktipp_aliases_request_data'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for "subscriber request data" link',
    '#default_value' => variable_get('klicktipp_aliases_request_data', 'my-data'),
  );
  $form['klicktipp_aliases']['klicktipp_aliases_update_data'] = array(
    '#type' => 'textfield',
    '#title' => 'Alias for "subscriber update data" link',
    '#default_value' => variable_get('klicktipp_aliases_update_data', 'my-data-update'),
  );

  //specify system email addresses that can't be used by the customers
  $form['klicktipp_systememail'] = array(
    '#type' => 'fieldset',
    '#title' => 'System email addresses',
  );
  $form['klicktipp_systememail']['klicktipp_reserved_system_email_addresses'] = array(
    '#type' => 'textarea',
    '#title' => 'Reserved system email addresses (local parts)',
    '#description' => strtr("Enter one email addess (without @!domain) per line that will be blocked to customers.", array('!domain' => KLICKTIPP_DOMAIN)),
    '#default_value' => variable_get('klicktipp_reserved_system_email_addresses', ''),
    '#rows' => 20,
    '#cols' => 68,
  );

  $form = system_settings_form($form);

  // run own sumbit handler AFTER default, so values are already saved
  $form['#submit'][] = 'klicktipp_settings_aliases_submit';
  return $form;
}

function klicktipp_settings_aliases_validate($form, &$form_state) {
  $shared_senderdomain_user = $form_state['values']['shared_senderdomain_user'];
  $shared_senderdomain_domains = $form_state['values']['shared_senderdomain_domains'];
  $new_shared_senderdomain_domains = $form_state['values']['new_shared_senderdomain_domains'];
  $klicktipp_pmta_ip_mapping = trim($form_state['values']['klicktipp_pmta_ip_mapping']);

  if (!empty($shared_senderdomain_user) || !empty(json_decode($shared_senderdomain_domains))) {
    $SharedDomains = DomainSet::RetrieveWhitelabelDomains($shared_senderdomain_user);
    $divisor = count($SharedDomains);

    if (empty($SharedDomains)) {
      form_set_error('shared_senderdomain_user', 'No whitelabel domains defined for this user.');
    }
    else {
      $domain = DomainSet::GetSharedSenderDomains($divisor-1, TRUE, $shared_senderdomain_user, $shared_senderdomain_domains, $new_shared_senderdomain_domains);
      if (empty($domain)) {
        form_set_error('shared_senderdomain_domains', 'Whitelabel domains do not match.');
      }
    }
  }

  if (!empty($klicktipp_pmta_ip_mapping)) {
    // split the textarea to simplify regex
    $lines = explode("\n", $klicktipp_pmta_ip_mapping);
    foreach ($lines as $no => $line) {
      $nop=$no+1;
      if (!preg_match("/^\d{1,3}\.\d{1,3}\.\d{1,3}=\d{1,3}\.\d{1,3}\.\d{1,3}$/", trim($line))) {
        form_set_error('klicktipp_pmta_ip_mapping', 'Wrong format of ip mapping in line ' . $nop);
      }
      else {
        [$external, $internal] = explode("=", trim($line));
        // normalize
        if (ip2long($external . '.1') === FALSE) {
          form_set_error('klicktipp_pmta_ip_mapping', 'Wrong format of external ip in line ' . $nop);
        }
        if (ip2long($internal . '.1') === FALSE) {
          form_set_error('klicktipp_pmta_ip_mapping', 'Wrong format of internal ip in line ' . $nop);
        }
      }
    }
  }

  //check if a reserved system email address is already in use by a customer
  $reserved_addresses = array_map('trim', explode("\n", $form_state['values']['klicktipp_reserved_system_email_addresses']));
  $reserved_addresses = array_diff($reserved_addresses, ['']);

  foreach ($reserved_addresses as $address) {

    //check if used as a request email address
    $array_list = kt_fetch_array(db_query("SELECT RelOwnerUserID, RelListID FROM {listbuilding} WHERE BuildType = :BuildType AND VarcharIndexed = :EmailAddress", array(
      ':BuildType' => Listbuildings::TYPE_REQUESTS,
      ':EmailAddress' => $address,
    )));

    if (!empty($array_list)) {

      $list_user = user_load($array_list['RelOwnerUserID']);
      form_set_error('klicktipp_reserved_system_email_addresses', strtr("@address is already used as request email address in the list @list of user @user", array(
        '@address' => $address,
        '@list' => $array_list['RelListID'],
        '@user' => $list_user->name,
      )));

    }

  }

}

function klicktipp_settings_aliases_submit($form, &$form_state) {
  // update forwarders
  ProcessLog::CreateUniqueQueueItem('request_forwarders_queue');

  // update vmta pools
  DomainSet::GenerateVirtualMTAConfig();
}

/*
 * Callback for groups overview page
 */

function klicktipp_groups_overview_form($form, $form_state) {

  $array_view_data = UserGroups::RetrieveUserGroups(array('*'), array());

  // display data

  $weight = 1;

  //translate account category names
  $GroupCategoryNames = array_map('t', UserGroups::$DisplayAccountCategory);

  $header = array(
    array('data' => 'ID'),
    array('data' => 'Name of group'),
    array('data' => 'Category'),
    array('data' => 'Subscriber Limit'),
    array('data' => 'Import Limit'),
    array('data' => 'Bounce Resets'),
    array('data' => 'Unsubs. Limit'),
    array('data' => 'Marketing Tools Limit'),
    array('data' => 'Automations Limit'),
    array('data' => 'Sender domains limit'),
    array('data' => 'Weight'),
    array('data' => 'Users'),
  );

  $form['spacebefore'] = array(
    '#type' => 'markup',
    '#value' => '<br />',
    '#weight' => $weight++,
  );

  $rows = array();
  foreach ($array_view_data as $group) {
    $row = array(
      array('data' => $group['UserGroupID']),
      array('data' => l($group['GroupName'], 'admin/config/klicktipp/groups/edit/' . $group['UserGroupID'])),
      array('data' => (empty($group['Data']['GroupCategory']) ? 'unassigned' : $GroupCategoryNames[$group['Data']['GroupCategory']])),
      array('data' => ($group['LimitSubscribers'] ? $group['LimitSubscribers'] : '0')),
      array('data' => ($group['Data']['LimitImports'] ? $group['Data']['LimitImports'] : '0')),
      array('data' => ($group['Data']['LimitBounceResets'] ? $group['Data']['LimitBounceResets'] : '0')),
      array('data' => ($group['Data']['LimitUnsubscriptionMessages'] ? $group['Data']['LimitUnsubscriptionMessages'] : '0')),
      array('data' => ($group['Data']['LimitMarketingTools'] ? $group['Data']['LimitMarketingTools'] : '0')),
      array('data' => ($group['Data']['LimitAutomations'] ? $group['Data']['LimitAutomations'] : '0')),
      array('data' => ($group['Data']['LimitDomains'] ?: '0')),
      array('data' => ($group['GroupWeight'] ? $group['GroupWeight'] : '0')),
      array('data' => ($group['TotalUsers'] ? $group['TotalUsers'] : '0')),
    );
    $rows[] = $row;
  }

  if (empty($rows)) {
    $form['empty'] = array(
      '#type' => 'markup',
      '#value' => '<p>There are no groups yet</p>',
      '#weight' => $weight++,
    );
  }
  else {

    $form['OverviewTable'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array('header' => $header, 'rows' => $rows)),
      '#weight' => $weight++,
    );

  }

  $form['submitcreate'] = array(
    '#type' => 'submit',
    '#value' => 'Create new group',
    '#weight' => 10000,
    '#prefix' => '<br />',
  );

  return $form;
}

function klicktipp_groups_overview_form_submit($form, &$form_state) {

  // create list_form
  klicktipp_set_redirect($form_state, 'admin/config/klicktipp/groups/create');
}

function klicktipp_groups_overview_delete_confirm_form($form, $form_state, $groupid) {

  $form['groupid'] = array(
    '#type' => 'value',
    '#value' => $groupid,
  );

  $group = UserGroups::RetrieveUserGroup($groupid, TRUE);
  $title = check_plain($group['GroupName']);

  $question = "Are you sure to delete the group $title?";

  klicktipp_set_title("Delete group $title");

  $form['assureblock'] = array(
    '#type' => 'markup',
    '#value' => '<p>Note: This action can not be undone!</p><p>' . $question . '</p>',
  );

  // Confirm form fails duplication check, as the form values rarely change -- so skip it.
  $form['#skip_duplicate_check'] = TRUE;

  $form['submit'] = array(
    '#type' => 'submit',
    '#value' => 'Delete',
  );
  $form['cancel'] = array(
    '#title' => 'Cancel',
    '#value' => isset($_GET['destination']) ? $_GET['destination'] : 'admin/config/klicktipp/groups/browse',
    '#theme' => 'klicktipp_cancel_button',
  );

  return $form;
}

function klicktipp_groups_overview_delete_confirm_form_submit($form, &$form_state) {

  $groupid = $form_state['values']['groupid'];

  UserGroups::Delete($groupid);

  drupal_set_message('The group was successfully deleted.');

  klicktipp_set_redirect($form_state, 'admin/config/klicktipp/groups/browse');
}

/*
 * Callback for list form edit page
 */

function klicktipp_groups_create_form($form, $form_state) {

  klicktipp_set_title('Create group');

  $weight = 1;

  $form['GroupNameFrame'] = array(
    '#type' => 'fieldset',
    '#title' => 'Name of group',
  );

  $form['GroupNameFrame']['GroupName'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#size' => 60,
    '#description' => 'You can change the name of your group anytime.',
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:90px;"><label style="font-weight:bold;">Name*:</label></td><td>',
    '#suffix' => '</td></tr></table>',
  );

  // buttons

  $form['submit'] = array(
    '#type' => 'submit',
    '#value' => KLICKTIPP_GROUPS_SAVE_BUTTON_TEXT,
    '#weight' => $weight++,
    '#prefix' => '<br />',
  );

  return $form;
}

function klicktipp_groups_create_form_submit($form, &$form_state) {

  $Group = array(
    'GroupName' => $form_state['values']['GroupName'],
    'LimitSubscribers' => 0,
  );

  $groupid = UserGroups::CreateUserGroup($Group);

  if ($groupid) {
    klicktipp_set_redirect($form_state, 'admin/config/klicktipp/groups/edit/' . $groupid);
  }
  else {
    drupal_set_message('An error occured creating the group.');
    klicktipp_set_redirect($form_state, 'admin/config/klicktipp/groups/browse');
  }

}

function klicktipp_groups_form($form, $form_state, $groupid) {

  // get group
  $group = UserGroups::RetrieveUserGroup($groupid, TRUE);

  $form['groupid'] = array(
    '#type' => 'value',
    '#value' => $groupid,
  );

  $form['OldData'] = array(
    '#type' => 'value',
    '#value' => empty($group['Data']) ? array() : $group['Data'],
  );

  klicktipp_set_title(check_plain($group['GroupName']));

  $weight = 1;

  $form['GroupNameFrame'] = array(
    '#type' => 'fieldset',
    '#title' => 'Name of group',
    '#weight' => $weight++,
  );

  $form['GroupNameFrame']['GroupName'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#default_value' => $group['GroupName'],
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Name*:</label></td><td>',
    '#suffix' => '</td></tr></table>',
  );

  $form['GroupFields'] = array(
    '#type' => 'fieldset',
    '#weight' => $weight++,
  );

  $form['GroupFields']['LimitSubscribers'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['LimitSubscribers'],
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Subscriber Limit:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => 'Startup, Business and First Class have tier dependent subscriber limits. For those cases leave this field at 0.'
  );

  $form['GroupFields']['Data'] = array(
    '#tree' => TRUE,
    '#weight' => $weight++,
  );

  $GroupCategoryNames = array_map('t', UserGroups::$DisplayAccountCategory);

  $form['GroupFields']['Data']['GroupCategory'] = array(
    '#type' => 'select',
    '#default_value' => $group['Data']['GroupCategory'],
    '#weight' => $weight++,
    '#options' => array(0 => 'Please select') + $GroupCategoryNames,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Group category:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => 'Translate "Account Beginner", "Account Standard", "Account Premium", "Account Platinum", "Account Enterprise", "Account Free", "Account Startup", "Account Business", "Account First Class" via Drupal translation.',
  );

  $roleOptions = db_select('role')
    ->fields('role', ['rid', 'name'])
    ->orderBy('weight')
    ->execute()
    ->fetchAllKeyed();

  $form['GroupFields']['Data']['Role'] = [
    '#type' => 'select',
    '#default_value' => $group['Data']['Role'],
    '#weight' => $weight++,
    '#options' => $roleOptions,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Role:</label></td><td>',
    '#suffix' => '</td></tr></table>',
  ];

  $form['GroupFields']['Data']['TierInAccount'] = [
    '#type' => 'checkbox',
    '#default_value' => $group['Data']['TierInAccount'] ?? 0,
    '#weight' => $weight++,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Tier in Account ?</label></td><td>',
    '#suffix' => '</td></tr></table>',
  ];

  $form['GroupFields']['Data']['LimitImports'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['LimitImports'],
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Import Limit:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => 'Startup, Business and First Class have tier dependent import limits. For those cases leave this field at 0.'
  );

  $form['GroupFields']['Data']['LimitBounceResets'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['LimitBounceResets'],
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Bounce Resets Limit:</label></td><td>',
    '#suffix' => '</td></tr></table>',
  );

  $form['GroupFields']['Data']['LimitUnsubscriptionMessages'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['LimitUnsubscriptionMessages'],
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Unsubscription Message Limit:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => "Enter '" . UserGroups::LIMIT_UNLIMITED . "' for no limit",
  );

  $form['GroupFields']['Data']['LimitUnsubscriptionMessagesUpgrade'] = array(
    '#type' => 'textarea',
    '#default_value' => $group['Data']['LimitUnsubscriptionMessagesUpgrade'],
    '#weight' => $weight++,
    '#attributes' => array('style' => 'width:95%; height:50px;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Unsubscription Message Upsell:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => 'Enter the message to show to the customer when the limit is exeeded.',
  );

  $form['GroupFields']['Data']['LimitMarketingTools'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['LimitMarketingTools'],
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Marketing Tools Limit:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => "Enter '" . UserGroups::LIMIT_UNLIMITED . "' for no limit",
  );

  $form['GroupFields']['Data']['LimitAutomations'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['LimitAutomations'],
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Automations Limit:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => "Enter '" . UserGroups::LIMIT_UNLIMITED . "' for no limit",
  );

  $form['GroupFields']['Data']['LimitDomains'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['LimitDomains'] ?:0,
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Sender Domains Limit:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => "Enter '" . UserGroups::LIMIT_UNLIMITED . "' for no limit",
  );

  $form['GroupFields']['Data']['LimitSenderIPs'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['LimitSenderIPs'],
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Count of allowed IPs:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => 'Enter the maximum count of whitelabel sender ips.',
  );

  $form['GroupFields']['Data']['LimitLandingPages'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['LimitLandingPages'] ?? 0,
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Landing pages limit:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => 'Enter the maximum number of landing pages. Use ' . UserGroups::LIMIT_UNLIMITED . ' for no limit.',
  );

  $form['GroupFields']['Data']['CustomLandingPageProducts'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['CustomLandingPageProducts'] ?? '[]',
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Product for LandingPage with custom domains:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => 'JSON-Array. Keys are product ids, values contingents',
    '#weight' => $weight++,
    '#element_validate' => [], // allow json content
  );

  $form['GroupFields']['Data']['CloseCRMProducts'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['Data']['CloseCRMProducts'] ?? '[]',
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Product for CloseCRM Plugin:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => 'JSON-Array. Keys are product ids, values 1',
    '#weight' => $weight++,
    '#element_validate' => [], // allow json content
  );

  $form['GroupFields']['GroupWeight'] = array(
    '#type' => 'textfield',
    '#default_value' => $group['GroupWeight'],
    '#weight' => $weight++,
    '#size' => 60,
    '#attributes' => array('style' => 'width:auto;display:inline;'),
    '#prefix' => '<table><tr><td style="width:150px;"><label style="font-weight:bold;">Weight:</label></td><td>',
    '#suffix' => '</td></tr></table>',
    '#description' => 'Group weight for product subscriptions. The product with the highest weight will be taken to assign permissions. So an upgrade will grant higher permissions, even if both subscriptions are active. NOTE: the Affiliate Group needs to have weight 0, as this defines a non customer product. All other products need higher values.',
  );

  $form['submitcreate'] = array(
    '#type' => 'submit',
    '#value' => KLICKTIPP_GROUPS_SAVE_BUTTON_TEXT,
    '#weight' => $weight++,
    '#prefix' => '<br />',
  );

  $form['delete'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => KLICKTIPP_GROUPS_DELETE_BUTTON_TEXT,
    '#value' => "admin/config/klicktipp/groups/delete/$groupid",
    '#weight' => $weight++,
  );

  return $form;
}

function  klicktipp_groups_form_validate($form, &$form_state) {
  $landingPageProductConfig = json_decode($form_state['values']['Data']['CustomLandingPageProducts'], TRUE);
  if (!is_array($landingPageProductConfig)) {
    form_set_error('Data][CustomLandingPageProducts', 'valid json array expected');
  }
  $closeCrmProductConfig = json_decode($form_state['values']['Data']['CloseCRMProducts'], TRUE);
  if (!is_array($closeCrmProductConfig)) {
    form_set_error('Data][CloseCRMProducts', 'valid json array expected');
  }
}

function klicktipp_groups_form_submit($form, &$form_state) {

  $groupid = $form_state['values']['groupid'];

  if ($form_state['values']['op'] == KLICKTIPP_GROUPS_DELETE_BUTTON_TEXT) {
    // redirect to confirmation
    klicktipp_set_redirect($form_state, 'admin/config/klicktipp/groups/delete/' . $groupid);
  }
  else {
    // save

    $ArrayAPIData = array();
    $ArrayAPIData['GroupName'] = $form_state['values']['GroupName'];
    $ArrayAPIData['LimitSubscribers'] = $form_state['values']['LimitSubscribers'];
    $ArrayAPIData['GroupWeight'] = $form_state['values']['GroupWeight'];

    $ArrayAPIData['Data'] = array_merge($form_state['values']['OldData'], $form_state['values']['Data']);

    UserGroups::Update($ArrayAPIData, array('UserGroupID' => $groupid));

    // redirect to source (next step)
    klicktipp_set_redirect($form_state, 'admin/config/klicktipp/groups/browse');
  }
}

/*
 * Hater
 */
function klicktipp_hater_overview_form($form, &$form_state) {

  // display data

  klicktipp_set_title('Haters');

  $weight = 1;

  $candidates = array();
  $checkboxes = array();
  $limit = 50;
  $start = isset($_REQUEST['page']) ? $_REQUEST['page'] * $limit : 0;
  $placeholders = [
    ':SuppressionSourceEnum' => [
      BounceEngine::SUPPRESSION_LIST_SOURCE_SPAMTRAP,
      BounceEngine::SUPPRESSION_LIST_SOURCE_ACCOUNT
    ]
  ];

  $searchEmail = $form_state['storage']['searchEmail'] ?? '';
  $searchUser = $form_state['storage']['searchUser'] ?? '';

  $query = "SELECT * FROM {suppression_list} WHERE SuppressionSourceEnum IN (:SuppressionSourceEnum)";
  $countQuery = "SELECT count(SuppressionID) FROM {suppression_list} WHERE SuppressionSourceEnum IN (:SuppressionSourceEnum)";

  if (!empty($searchEmail)) {
    $query .= " AND EmailAddress LIKE :EmailAddress";
    $countQuery .= " AND EmailAddress LIKE :EmailAddress";
    $placeholders[':EmailAddress'] = '%' . db_like($searchEmail) . '%';
  }

  if (!empty($searchUser)) {
    $query .= " AND UserID = :UserID";
    $countQuery .= " AND UserID = :UserID";
    $placeholders[':UserID'] = $searchUser;
  }

  // get total query count for pager
  $countResult = db_query($countQuery, $placeholders)->fetchField();
  // actual paginated query
  $result = db_query_range($query, $start, $limit, $placeholders);
  pager_default_initialize($countResult, $limit);

  // loop over result set
  while($entry = $result->fetchAssoc()){
    $index = $entry['SuppressionID'];
    $candidates[$index] = $entry['EmailAddress'];

    $checkboxes[$index] = '';
    $form['index'][$index] = array(
      '#type' => 'markup',
      '#value' => $index,
    );
    $form['EmailAddress'][$index] = array(
      '#type' => 'markup',
      '#value' => Subscribers::DepunycodeEmailAddress($entry['EmailAddress']),
    );
    $form['Type'][$index] = array(
      '#type' => 'markup',
      '#value' => BounceEngine::DISPLAY_SUPPRESSION_LIST_SOURCE[$entry['SuppressionSourceEnum']] ?? 'Unknown',
    );
    $form['Username'][$index] = array(
      '#type' => 'markup',
      '#value' => empty($entry['UserID']) ? 'global' : "UserID: {$entry['UserID']}",
    );
    $form['Listname'][$index] = array(
      '#type' => 'markup',
      '#value' => '',
    );
    $form['SubscriptionStatus'][$index] = array(
      '#type' => 'markup',
      '#value' => '',
    );
    $form['SubscriptionIP'][$index] = array(
      '#type' => 'markup',
      '#value' => '',
    );
  }

  $form['candidates'] = array(
    '#type' => 'value',
    '#value' => $candidates,
  );

  // message for empty result set
  if (empty($index) && empty($_REQUEST['page'])) {
    $form['empty'] = array(
      '#type' => 'markup',
      '#value' => '<p>No haters found</p>',
      '#weight' => $weight++,
    );
  }
  // message in case of invalid pagination
  elseif (empty($index) && $_REQUEST['page']) {
    $form['empty'] = array(
      '#type' => 'markup',
      '#value' => '<p>This page doesn\'t exist</p><a href="/admin/config/klicktipp/haters/browse">Go to first page</a><br>',
    );
  }
  else {
    $form['checkboxes'] = array(
      '#type' => 'checkboxes',
      '#options' => $checkboxes,
    );
    // delete button
    $form['submitdelete'] = array(
      '#type' => 'submit',
      '#value' => KLICKTIPP_HATER_DELETE_SELECTED_BUTTON_TEXT,
      '#weight' => 10001,
    );
    $form['pager'] = [
      '#markup' => theme('pager')
    ];
  }

  $form['total'] = [
    '#markup' => "<span style='position: absolute; right: 0;'>Total: {$countResult}</span>",
    '#weight' => 997,
  ];
  // search field
  $form['searchemail'] = array(
    '#type' => 'textfield',
    '#title' => t('E-Mail'),
    '#size' => 40,
    '#weight' => 998,
  );
  $form['searchuser'] = array(
    '#type' => 'textfield',
    '#title' => t('UserID'),
    '#size' => 10,
    '#weight' => 999,
  );
  // search button
  $form['submitsearch'] = array(
    '#type' => 'submit',
    '#value' => KLICKTIPP_HATER_SEARCH_BUTTON_TEXT,
    '#weight' => 999,
  );
  // reset search button
  if ($_REQUEST['search']) {
    $form['submitreset'] = array(
      '#type' => 'submit',
      '#value' => KLICKTIPP_HATER_SEARCH_RESET_BUTTON_TEXT,
      '#weight' => 999,
    );
  }

  $form['#theme'] = 'klicktipp_hater_create_form';

  return $form;
}

function klicktipp_hater_overview_form_submit($form, &$form_state) {
  // form was submitted by delete button
  if ($form_state['values']['op'] == KLICKTIPP_HATER_DELETE_SELECTED_BUTTON_TEXT) {
    // delete selection
    $candidates = $form_state['values']['candidates'];
    $deleteIDs = array();
    $emails = [];
    foreach ($form_state['values']['checkboxes'] as $id) {
      if ($id) {
        $deleteIDs[] = $id;
        $emails[] = Subscribers::DepunycodeEmailAddress($candidates[$id]);
      }
    }

    if (empty($deleteIDs)) {
      drupal_set_message('Please select the haters you want to delete.');
    }
    else {
      db_delete('suppression_list')
        ->condition('SuppressionID', $deleteIDs, 'IN')
        ->execute();

      $msg = 'The following haters have been removed from the suppression list:<br />';
      drupal_set_message($msg . '<ul><li>' . implode('</li><li>', $emails) . '</li></ul>');
    }
  }
  // form was submitted by search button with search value
  elseif ($form_state['values']['op'] === KLICKTIPP_HATER_SEARCH_BUTTON_TEXT) {
    $form_state['storage']['searchEmail'] = $form_state['values']['searchemail'];
    $form_state['storage']['searchUser'] = $form_state['values']['searchuser'];
    $form_state['rebuild'] = TRUE;
  }
  // form was submitted by search reset button
  elseif ($form_state['values']['op'] === KLICKTIPP_HATER_SEARCH_RESET_BUTTON_TEXT) {
    klicktipp_set_redirect($form_state,'/admin/config/klicktipp/haters/browse');
  }
  else {
    klicktipp_set_redirect($form_state, 'admin/config/klicktipp/haters/create');
  }
}

function theme_klicktipp_hater_create_form($variables) {
  $form = $variables['form'];
  $output = empty($form['HaterAddressFrame']) ? '' : drupal_render($form['HaterAddressFrame']);

  $header = array(
    array(
      'data' => '',
      'class' => array('table-col-fit'),
    ),
    array(
      'data' => 'ID',
      'class' => array('tdid tdheadercenter'),
    ),
    'Email address',
    'Type',
    array('data' => empty($form['HaterAddressFrame']) ? '' : 'User'),
    array('data' => empty($form['HaterAddressFrame']) ? '' : 'Subscription Status'),
    array(
      'data' => empty($form['HaterAddressFrame']) ? '' : 'IP address',
      'class' => array('tdr'),
    ),
  );
  if (!empty($form['checkboxes']['#options'])) {

    $rows = array();
    $customerListHint = FALSE;
    foreach (element_children($form['index']) as $key) {

      $style = [];
      if ($form['Type'][$key]['#value'] == BounceEngine::DISPLAY_SUPPRESSION_LIST_SOURCE[BounceEngine::SUPPRESSION_LIST_SOURCE_ACCOUNT]) {
        $customerListHint = TRUE;
        $style = ['background-color: lightcoral'];
      }

      $rows[] = [
        'data' => [
          drupal_render($form['checkboxes'][$key]),
          drupal_render($form['index'][$key]),
          drupal_render($form['EmailAddress'][$key]),
          drupal_render($form['Type'][$key]),
          drupal_render($form['Username'][$key]),
          drupal_render($form['SubscriptionStatus'][$key]),
          drupal_render($form['SubscriptionIP'][$key]),
        ],
        'style' => $style,
      ];
    }
    $output .= theme('table', array('header' => $header, 'rows' => $rows));
  }

  if ($customerListHint) {
    $hint = "<p style='color: lightcoral'>IMPORTANT: Customer List. Please double-check before removing them!!!</p>";
    $output = $hint . $output;
  }

  $output .= drupal_render_children($form);

  return $output;
}

/**
 *
 * Show all email notification types with a link to edit email settings
 *
 * to add a new type:
 * - define the type in klicktipp.module
 * - add a new
 *     $rows[] = array(
 *       "COREAPI_NOTIFY_EMAIL_<NEW TYPE>", //type (identifier)
 *       'Insert description here', //description
 *       l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_<NEW TYPE> . "/edit"), //edit link
 * - set the function to send the email with in Core::SendNotificationEmail() in klicktipp.module
 * );
 * @param array $form_state
 */
function klicktipp_notification_email_overview_form($form, $form_state) {

  $form = array();

  $weight = 0;

  $form['title_support_notifcations'] = array(
    '#type' => 'markup',
    '#value' => "<h3>Support notifications</h3>",
    '#weight' => $weight++,
  );

  $form['description_support_notifcations'] = array(
    '#type' => 'markup',
    '#value' => "<p>The following table shows email notification send to the support or the administration.</p>",
    '#weight' => $weight++,
  );

  $header = array(
    'Type',
    'Description',
    'Operation',
  );

  $rows = array();

  //COREAPI_NOTIFY_EMAIL_UNSUBSCRIPTION_FEEDBACK: Unsubscription Feedback
  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_UNSUBSCRIPTION_FEEDBACK",
    //type (identifier)
    'Send an email to the support system with the feedback from the Unsubscriber using his email address',
    //description
    l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_UNSUBSCRIPTION_FEEDBACK . "/edit"),
    //edit link
  );

  //COREAPI_NOTIFY_EMAIL_SIGNATURE_REVISION: Signature revision
  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_SIGNATURE_REVISION",
    //type (identifier)
    'Send a signature to the support for revision',
    //description
    l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_SIGNATURE_REVISION . "/edit"),
    //edit link
  );

  //COREAPI_NOTIFY_EMAIL_BCC_EMAIL_REVISION: email revision
  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_BCC_EMAIL_REVISION",
    //type (identifier)
    'A copy of every customer email is sent to the support for revision',
    //description
    l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_BCC_EMAIL_REVISION . "/edit"),
    //edit link
  );

  //COREAPI_NOTIFY_EMAIL_CONTACT_FORM: contact form for auth users
  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_CONTACT_FORM",
    //type (identifier)
    'The contents of the contact form plus support information/links is sent to the support',
    //description
    l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_CONTACT_FORM . "/edit"),
    //edit link
  );

  //COREAPI_NOTIFY_EMAIL_BLACKLISTING: blacklisting filter in bounce-script
  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_BLACKLISTING",
    //type (identifier)
    'The contents of the bouncing email is sent to the support',
    //description
    l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_BLACKLISTING . "/edit"),
    //edit link
  );

  //COREAPI_NOTIFY_EMAIL_CONSULTANT_REVIEW: consultant marketplace entry review
  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_CONSULTANT_REVIEW",
    //type (identifier)
    'Send an email to consultant manager, if new consultant marketplace entry needs to be reviewed',
    //description
    l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_CONSULTANT_REVIEW . "/edit"),
    //edit link
  );

  //COREAPI_NOTIFY_EMAIL_SENDERIP_CHECK: whitelabel sender ip check
  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_SENDERIP_CHECK",
    //type (identifier)
    'Send an email to the account manager, if there are users that exceed their maximum count of whitelabel sender IPs',
    //description
    l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_SENDERIP_CHECK . "/edit"),
    //edit link
  );

  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_INVALID_SENDER_ADDRESS",
    //type (identifier)
    'Notify support about invalid addresses',
    //description
    l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_INVALID_SENDER_ADDRESS . "/edit"),
    //edit link
  );

  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_MAILSERVER_DELETION_REQUEST",
    'Notification about a mailserver deletion request.',
    l('edit', "admin/config/klicktipp/notifications/" . COREAPI_NOTIFY_EMAIL_MAILSERVER_DELETION_REQUEST . "/edit"),
  );

    $rows[] = array(
        "PROCESSFLOW_NOTIFY_CAMPAIGN_RESTART_RATE_LIMIT_REACHED",
        'Notification about too many campaign restarts for a subscriber within defined time.',
        l('edit', "admin/config/klicktipp/notifications/" . PROCESSFLOW_NOTIFY_CAMPAIGN_RESTART_RATE_LIMIT_REACHED . "/edit"),
    );

  //klicktipp_notify_monitor: klicktipp monitor mail
  $rows[] = array(
    "Klicktipp monitor",
    'The contents of the klicktipp monitor report is sent to the application admins',
    l('edit', "admin/config/klicktipp/notifications/klicktipp_notify_monitor/edit"),
  );

  $form['support_notifications'] = array(
    '#type' => 'markup',
    '#value' => theme('table', array('header' => $header, 'rows' => $rows)),
    '#weight' => $weight++,
  );


  $form['separator'] = array(
    '#type' => 'markup',
    '#value' => "<br />",
    '#weight' => $weight++,
  );

  /*--------------------- USER NOTIFICATIONS ----------------*/


  $form['title_user_notifications'] = array(
    '#type' => 'markup',
    '#value' => "<h3>User notifications</h3>",
    '#weight' => $weight++,
  );

  $form['description_user_notifications'] = array(
    '#type' => 'markup',
    '#value' => "<p>The following table shows all notification send to the user.</p>",
    '#weight' => $weight++,
  );

  $rows = [];

  // spf check for additional sender addresses
  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_USER_WHITELABELDOMAIN_CHECK",
    'The contents of the the invalid whitelabel domains from the nightly whitelabel check for additional sender addresses, send to the user.',
    l('edit', "admin/config/klicktipp/notifications/user/" . COREAPI_NOTIFY_EMAIL_USER_WHITELABELDOMAIN_CHECK . "/edit"),
  );

  $rows[] = array(
    "COREAPI_NOTIFY_EMAIL_USER_SENDERDOMAIN_CHECK",
    'The contents of the the invalid sender domains from the nightly whitelabel check for additional sender addresses, send to the user.',
    l('edit', "admin/config/klicktipp/notifications/user/" . COREAPI_NOTIFY_EMAIL_USER_SENDERDOMAIN_CHECK . "/edit"),
  );

  $rows[] = array(
    'COREAPI_NOTIFY_EMAIL_USER_DOMAIN_INSTRUCTIONS',
    'The contents of email containing domain instructions for a partner of user',
    l('edit', "admin/config/klicktipp/notifications/user/" . COREAPI_NOTIFY_EMAIL_USER_DOMAIN_INSTRUCTIONS . "/edit"),
  );

  $form['user_notifications'] = array(
    '#type' => 'markup',
    '#value' => theme('table', array('header' => $header, 'rows' => $rows)),
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_notification_email_edit_form($form, $form_state, $Type) {

  $form = array();

  $weight = 0;

  $Data = variable_get($Type, array());

  $form['Type'] = array(
    '#type' => 'value',
    '#value' => $Type,
  );

  $form['ID'] = array(
    '#type' => 'item',
    '#title' => 'Type',
    '#value' => strtoupper($Type),
    '#weight' => $weight++,
  );

  $form['ToEmail'] = array(
    '#type' => 'textfield',
    '#title' => 'Recipient email address',
    '#default_value' => (empty($form_state['values']['ToEmail'])) ? $Data['ToEmail'] : $form_state['values']['ToEmail'],
    '#description' => 'Enter the (list of comma separated) recipient email address(es), to which this type of notification should be sent to. ' .
       'Leave empty for default ' . variable_get('site_mail', '')
  );

  $form['Subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#required' => TRUE,
    // leave this field required, so we get something useful the support can identify
    '#default_value' => (empty($form_state['values']['Subject'])) ? $Data['Subject'] : $form_state['values']['Subject'],
    '#description' => 'Enter the default subject for this notification type.',
  );

  $form['MessagePrefix'] = array(
    '#type' => 'textarea',
    '#title' => 'Message prefix',
    '#default_value' => (empty($form_state['values']['MessagePrefix'])) ? $Data['MessagePrefix'] : $form_state['values']['MessagePrefix'],
    '#description' => 'Enter the text that should be inserted before the notification message',
  );

  $form['MessageSuffix'] = array(
    '#type' => 'textarea',
    '#title' => 'Message suffix',
    '#default_value' => (empty($form_state['values']['MessageSuffix'])) ? $Data['MessageSuffix'] : $form_state['values']['MessageSuffix'],
    '#description' => 'Enter the text that should be inserted after the notification message',
  );

  $form['separator'] = array(
    '#type' => 'markup',
    '#value' => "<br />",
  );

  $form['submit'] = array(
    '#type' => 'submit',
    '#value' => 'Save',
    '#weight' => $weight++,
  );

  $form['cancel'] = array(
    '#type' => 'markup',
    '#value' => l('Cancel', 'admin/config/klicktipp/notifications'),
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_notification_email_edit_form_submit($form, &$form_state) {

  $values = $form_state['values'];

  $Data = array(
    'ToEmail' => $values['ToEmail'],
    'FromName' => $values['FromName'],
    'FromEmail' => $values['FromEmail'],
    'ReplyToName' => $values['ReplyToName'],
    'ReplyToEmail' => $values['ReplyToEmail'],
    'Subject' => $values['Subject'],
    'MessagePrefix' => $values['MessagePrefix'],
    'MessageSuffix' => $values['MessageSuffix'],
  );

  variable_set($values['Type'], $Data);

  drupal_set_message('Notification email settings saved');

  klicktipp_set_redirect($form_state, "admin/config/klicktipp/notifications");

}

function klicktipp_notification_email_user_edit_form($form, $form_state, $Type) {
  $form = array();

  $weight = 0;

  $Data = variable_get($Type, array());

  $form['Type'] = array(
    '#type' => 'value',
    '#value' => $Type,
  );

  $form['ID'] = array(
    '#type' => 'item',
    '#title' => 'Type',
    '#value' => $Type,
    '#weight' => $weight++,
  );

  $form['SenderName'] = array(
    '#type' => 'textfield',
    '#title' => 'Sender name',
    '#default_value' => (empty($form_state['values']['SenderName'])) ? $Data['SenderName'] : $form_state['values']['SenderName'],
    '#description' => "Enter a sender name like 'KlickTipp | Customer Happiness'.",
    '#weight' => $weight++,
  );

  $form['SenderAddress'] = array(
    '#type' => 'textfield',
    '#title' => 'Sender email address',
    '#required' => TRUE,
    '#default_value' => (empty($form_state['values']['SenderAddress'])) ? $Data['SenderAddress'] : $form_state['values']['SenderAddress'],
    '#description' => "Enter a sender email address like '<EMAIL>'.",
    '#weight' => $weight++,
  );

  $form['Subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#required' => TRUE,
    '#default_value' => (empty($form_state['values']['Subject'])) ? $Data['Subject'] : $form_state['values']['Subject'],
    '#description' => 'Enter subject for this notification. Possible placeholders:<br>!firstName (first name of the user)'
      . '<br>!domain (disabled domain)<br>!url (url of dns check tool)',
    '#weight' => $weight++,
  );

  $form['Body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#required' => TRUE,
    '#default_value' => (empty($form_state['values']['Body'])) ? $Data['Body'] : $form_state['values']['Body'],
    '#description' => 'Enter the email body. Placeholders:<br>!firstName (first name of the user)'
      . '<br>!domain (disabled domain)<br>!url (url of dns check tool)',
    '#weight' => $weight++,
  );

  $form['separator'] = array(
    '#type' => 'markup',
    '#value' => "<br />",
    '#weight' => $weight++,
  );

  $form['submit'] = array(
    '#type' => 'submit',
    '#value' => 'Save',
    '#weight' => $weight++,
  );

  $form['cancel'] = array(
    '#type' => 'markup',
    '#value' => l('Cancel', 'admin/config/klicktipp/notifications'),
    '#weight' => $weight++,
  );

  return $form;
}

function klicktipp_notification_email_user_edit_form_validate($form, &$form_state) {
  $senderArray = explode(" ", $form_state['values']['SenderAddress']);
  $sender = array_pop( $senderArray);
  if (!filter_var(trim($sender), FILTER_VALIDATE_EMAIL)) {
    form_set_error('SenderAddress', 'Sender address not a valid email address.');
  }
}


function klicktipp_notification_email_user_edit_form_submit($form, &$form_state) {

  $values = $form_state['values'];

  $Data = array(
    'SenderName' => trim($values['SenderName']),
    'SenderAddress' => trim($values['SenderAddress']),
    'Subject' => $values['Subject'],
    'Body' => $values['Body']
  );

  variable_set($values['Type'], $Data);

  drupal_set_message('User notification email settings saved');

  klicktipp_set_redirect($form_state, "admin/config/klicktipp/notifications");

}

function klicktipp_quickhelp_overview_form($form, $form_state) {

  $quickhelps = variable_get(KLICKTIPP_QUICKHELP_REFERENCE, array());

  $weight = 0;

  $form = array();

  if (empty($quickhelps)) {
    $form['nohelp'] = array(
      '#type' => 'markup',
      '#value' => "<p>There are no quick helps yet.</p>",
      '#weight' => $weight++,
    );
  }
  else {

    $header = array(
      'ID',
      'Dialog',
      'Status',
      'Operations',
    );

    $rows = array();
    foreach ($quickhelps as $id => $help) {

      $rows[] = array(
        array('data' => $id),
        l(check_plain($help['dialog_url']), $help['dialog_url'], array('attributes' => array('target' => '_blank'))),
        (empty($help['help_text'])) ? 'inactive' : 'active',
        l('edit', "admin/structure/translate/quickhelp/$id/edit"),
      );

    }

    $form['intro'] = array(
      '#type' => 'markup',
      '#value' => 'The following table shows all available quick helps.',
      '#weight' => $weight++,
    );

    $form['quickhelps_table'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array('header' => $header, 'rows' => $rows)),
      '#weight' => $weight++,
    );

    $SpamassassinQuickhelps = variable_get(KLICKTIPP_SPAMASSASSIN_QUICKHELPS, array());
    $SpamassassinMatches = "";
    if (!empty($SpamassassinQuickhelps)) {
      foreach ($SpamassassinQuickhelps as $quickhelp_id => $message_match) {
        $SpamassassinMatches .= "$quickhelp_id|$message_match\n";
      }
    }

    $form['SpamassassinMatches'] = array(
      '#type' => 'textarea',
      '#title' => 'Spamassassin quick helps',
      '#default_value' => trim($SpamassassinMatches),
      '#weight' => $weight++,
      '#description' => 'Assign quick helps to Spamassassin messages. Enter one match per line. strpos() is used to match messages.<br />Format: "QUICK_HELP_ID|SPAMASSASSIN_MESSAGE_MATCH"<br />Example: "spamassassin-bayes-test|Spamwahrscheinlichkeit nach Bayes-Test"<br />',
    );

    $form['SpamassassinSubmit'] = array(
      '#type' => 'submit',
      '#value' => 'Save Spamassassin matches',
      '#weight' => $weight++,
    );

  }

  return $form;
}

function klicktipp_quickhelp_overview_form_submit($form, &$form_state) {

  if ($form_state['values']['op'] == 'Save Spamassassin matches') {

    $SpamassassinQuickhelps = array();
    if (!empty($form_state['values']['SpamassassinMatches'])) {
      $matches = explode("\n", $form_state['values']['SpamassassinMatches']);
      foreach ($matches as $match) {
        $split = explode('|', $match);
        $quickhelp_id = trim($split[0]);
        $message_match = trim($split[1]);
        if (!empty($quickhelp_id) && !empty($message_match)) {
          $SpamassassinQuickhelps[$quickhelp_id] = $message_match;
        }
      }
    }

    variable_set(KLICKTIPP_SPAMASSASSIN_QUICKHELPS, $SpamassassinQuickhelps);

  }

}

function klicktipp_quickhelp_edit_form($form, $form_state, $HelpID) {

  $weight = 0;

  $form = array();

  $quickhelps = variable_get(KLICKTIPP_QUICKHELP_REFERENCE, array());

  if (empty($HelpID) || !isset($quickhelps[$HelpID])) {

    $form['NotFound'] = array(
      '#type' => 'markup',
      '#value' => "<p>Quickhelp not found</p>",
      '#weight' => $weight++,
    );

    $form['Cancel'] = array(
      '#type' => 'submit',
      '#value' => KLICKTIPP_QUICKHELP_CANCEL_BUTTON_TEXT,
      '#weight' => $weight++,
    );

    return $form;
  }

  $help = $quickhelps[$HelpID];

  $form['HelpID'] = array(
    '#type' => 'value',
    '#value' => $HelpID,
  );

  $form['ID'] = array(
    '#type' => 'item',
    '#title' => 'ID',
    '#value' => $HelpID,
    '#weight' => $weight++,
  );

  $form['DialogURL'] = array(
    '#type' => 'item',
    '#title' => 'Dialog',
    '#value' => l(check_plain($help['dialog_url']), $help['dialog_url'], array('attributes' => array('target' => '_blank'))),
    '#weight' => $weight++,
  );

  $form['HelpText'] = array(
    '#type' => 'textarea',
    '#title' => 'Help text',
    '#default_value' => (empty($form_state['values']['HelpText'])) ? $help['help_text'] : $form_state['values']['HelpText'],
    '#weight' => $weight++,
    '#description' => 'Enter the text to display in the help popup. HTML for links etc allowed.',
  );

  $form['Submit'] = array(
    '#type' => 'submit',
    '#value' => KLICKTIPP_QUICKHELP_SAVE_BUTTON_TEXT,
    '#weight' => $weight++,
  );

  $form['Cancel'] = array(
    '#type' => 'submit',
    '#value' => KLICKTIPP_QUICKHELP_CANCEL_BUTTON_TEXT,
    '#weight' => $weight++,
  );

  return $form;
}

function klicktipp_quickhelp_edit_form_submit($form, &$form_state) {

  if ($form_state['values']['op'] == KLICKTIPP_QUICKHELP_SAVE_BUTTON_TEXT) {

    $quickhelps = variable_get(KLICKTIPP_QUICKHELP_REFERENCE, array());
    $HelpID = $form_state['values']['HelpID'];

    $quickhelps[$HelpID]['help_text'] = $form_state['values']['HelpText'];

    variable_set(KLICKTIPP_QUICKHELP_REFERENCE, $quickhelps);

    drupal_set_message('Quickhelp successfully edited.');

  }

  klicktipp_set_redirect($form_state, 'admin/structure/translate/quickhelp');

}

/*
 * Merge Accounts
 */

function klicktipp_merge_accounts_form($form, $form_state, $step = 0, $from_account = 0, $to_account = 0) {
  klicktipp_set_title('Merge Accounts');

  $weight = 1;

  $form['step'] = array(
    '#type' => 'value',
    '#value' => $step,
  );
  $form['fromaccount'] = array(
    '#type' => 'value',
    '#value' => $from_account,
  );
  $form['toaccount'] = array(
    '#type' => 'value',
    '#value' => $to_account,
  );

  $form['selection'] = array(
    '#type' => 'fieldset',
    '#title' => 'Select accounts',
    '#weight' => $weight++,
    '#description' => 'This will move all data FROM the source account TO the destination account.',
  );

  if ($step == 0) {
    // select source account
    $form['selection']['source'] = array(
      '#type' => 'textfield',
      '#title' => 'FROM account',
      '#maxlength' => 60,
      '#autocomplete_path' => 'user/autocomplete',
      '#weight' => $weight++,
      '#description' => 'This is the user account to move all data from. IT WILL DELETE ALL DATA IN THIS ACCOUNT!',
    );
    // select source account
    $form['selection']['dest'] = array(
      '#type' => 'textfield',
      '#title' => 'TO account',
      '#maxlength' => 60,
      '#autocomplete_path' => 'user/autocomplete',
      '#weight' => $weight++,
      '#description' => 'This is the user account to move the data to.',
    );
  }
  else {

    // select source account
    $account = user_load($from_account);
    $form['selection']['source'] = array(
      '#type' => 'item',
      '#title' => 'FROM account',
      '#value' => "{$account->name} (id: {$account->uid})",
      '#weight' => $weight++,
      '#description' => 'This is the user account to move all data from. IT WILL DELETE ALL DATA IN THIS ACCOUNT!',
    );
    // select source account
    $account = user_load($to_account);
    $form['selection']['dest'] = array(
      '#type' => 'item',
      '#title' => 'TO account',
      '#value' => "{$account->name} (id: {$account->uid})",
      '#weight' => $weight++,
      '#description' => 'This is the user account to move the data to.',
    );

    $form['confirm'] = array(
      '#type' => 'markup',
      '#value' => '<p>Please confirm to merge these accounts. THIS ACTION CANNOT BE UNDONE!</p>',
      '#weight' => $weight++,
    );

    $form['cancel'] = array(
      '#type' => 'markup',
      '#value' => l('Cancel', 'admin/config/klicktipp/mergeaccounts'),
      '#weight' => 10001,
    );

  }

  $form['submit'] = array(
    '#type' => 'submit',
    '#value' => 'Merge accounts',
    '#weight' => 10000,
  );

  return $form;
}

function klicktipp_merge_accounts_form_submit($form, &$form_state) {
  // default target
  klicktipp_set_redirect($form_state, "admin/config/klicktipp/mergeaccounts");

  if ($form_state['values']['step'] == 0) {

    // validate (no hook_form_validation needed, as we will ask for confirmation => step = 1)

    // source not found
    $from_account = user_load_by_name($form_state['values']['source']);
    if (empty($from_account->uid)) {
      drupal_set_message('Invalid selection', 'error');
      return;
    }

    // destination not found
    $to_account = user_load_by_name($form_state['values']['dest']);
    if (empty($to_account->uid)) {
      drupal_set_message('Invalid selection', 'error');
      return;
    }

    // no listbuilding sources allowed (we cant support old confirmation links, fanpages, ...)
    $count = db_query("SELECT 1 FROM {listbuilding} WHERE RelOwnerUserID = :RelOwnerUserID LIMIT 0,1", array(':RelOwnerUserID' => $from_account->uid))->fetchField();
    $count += db_query("SELECT 1 FROM {listforms} WHERE RelOwnerUserID = :RelOwnerUserID LIMIT 0,1", array(':RelOwnerUserID' => $from_account->uid))->fetchField();
    $count += db_query("SELECT 1 FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID LIMIT 0,1", array(':RelOwnerUserID' => $from_account->uid))->fetchField();
    if ($count > 0) {
      drupal_set_message('Please remove all listbuilding and marketing tool configurations from the source account.', 'error');
      return;
    }

    // ask for confirmation
    klicktipp_set_redirect($form_state, "admin/config/klicktipp/mergeaccounts/1/{$from_account->uid}/{$to_account->uid}");

  }
  else {
    // do it

    $from_account = user_load($form_state['values']['fromaccount']);
    $from_account_edit = array();
    $from_name = " ({$from_account->name})";

    $to_account = user_load($form_state['values']['toaccount']);
    $to_account_edit = array();

    // copy senders (sms)
    $to_senders = $to_account->AdditionalSenderPhoneNumber;
    foreach ($from_account->AdditionalSenderPhoneNumber as $from_sender) {
      $found = FALSE;
      foreach ($to_account->AdditionalSenderPhoneNumber as $to_index => $to_sender) {
        if (strcasecmp($from_sender['phonenumber'], $to_sender['phonenumber']) == 0) {
          $found = TRUE;
          if ($from_sender['verified'] == 1) {
            $to_senders[$to_index]['verified'] = 1;
          }
          break;
        }
      }
      if (!$found) {
        $to_senders[] = $from_sender;
      }
    }
    $to_account_edit['AdditionalSenderPhoneNumber'] = $to_senders;
    $from_account_edit['AdditionalSenderPhoneNumber'] = array();

    // copy unsubscription dialogs and urls
    $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($to_account);
    $Dialogs = $Unsubscriptions['Dialogs'];
    $URLs = $Unsubscriptions['URLs'];

    $FromUnsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($from_account);
    foreach ($FromUnsubscriptions['Dialogs'] as $Dialog) {
      $ids = array_keys($Dialogs);
      $DialogID = (empty($ids)) ? 1 : max($ids) + 1;
      $Dialog['DialogID'] = $DialogID;
      $Dialog['DialogText'] .= $from_name;
      $Dialogs[$DialogID] = $Dialog;
    }
    uasort($Dialogs, 'element_sort');
    $Unsubscriptions['Dialogs'] = $Dialogs;

    foreach ($FromUnsubscriptions['URLs'] as $URL) {
      $ids = array_keys($URLs);
      $URL_ID = (empty($ids)) ? 1 : max($ids) + 1;
      $URL['URL_ID'] = $URL_ID;
      $URL['URL_Name'] .= $from_name;
      $URLs[$URL_ID] = $URLs;
    }
    uasort($URLs, 'element_sort');
    $Unsubscriptions['URLs'] = $URLs;

    $to_account_edit['Unsubscriptions'] = $Unsubscriptions;
    $from_account_edit['Unsubscriptions'] = array();

    // update destination user data
    user_save($to_account, $to_account_edit);
    // clear source user data
    user_save($from_account, $from_account_edit);

    VarAdditionalAddresses::merge($from_account->uid, $to_account->uid);

    // reload to account
    $to_account = user_load($to_account->uid);

    // reset custom field categories
    $AllCustomFields = CustomFields::RetrieveCustomFields($from_account->uid, TRUE);
    foreach ($AllCustomFields as $field) {
      if (!empty($field['FieldParameters']['FieldCategory'])) {
        $field['FieldParameters']['FieldCategory'] = '';
        CustomFields::Update($field, $field['CustomFieldID']);
      }
    }

    // start transaction
    kt_begin_transaction();

    // rename references so we can move them later
    // we need to get a free range of ReferenceIDs in "to" that are new in "from" as well
    $nextReferenceID = db_query('SELECT MAX(ReferenceID) FROM {reference} WHERE RelOwnerUserID IN (:UserID)', [':UserID' => [$from_account->uid, $to_account->uid]])->fetchField() + 1;
    // move all references that need to be moved, but leave them in the "from" account (necessary to check subscription references later on)
    $result = db_query(
      'SELECT * FROM {reference} WHERE RelOwnerUserID = :RelOwnerUserID AND ReferenceID > 0 ORDER BY ReferenceID ASC',
      [':RelOwnerUserID' => $from_account->uid]
    );
    while ($ref = $result->fetchAssoc()) {
      if ($existingReferenceID = db_query(
        'SELECT ReferenceID FROM {reference} WHERE RelOwnerUserID = :RelOwnerUserID AND NumberRange = :NumberRange AND ExtReferenceID = :ExtReferenceID',
        [':RelOwnerUserID' => $to_account->uid, ':NumberRange' => $ref['NumberRange'], ':ExtReferenceID' => $ref['ExtReferenceID']])->fetchField()) {
        // if there is an existing external reference we need to match it
        $newReferenceID = $existingReferenceID;
      }
      else {
        // if there is no existing external reference we need to create one
        $newReferenceID = Reference::InsertDB([
          'RelOwnerUserID' => $to_account->uid,
          'ReferenceID' => $nextReferenceID++,
          'NumberRange' => $ref['NumberRange'],
          'ExtReferenceID' => $ref['ExtReferenceID'],
          'DisplayName' => $ref['DisplayName']
        ]);
      }
      // change references in from account (so we can easily move them)
      db_query("UPDATE {subscription_reference} SET ReferenceID = :newReferenceID " .
        " WHERE ReferenceID = :ReferenceID AND RelOwnerUserID = :RelOwnerUserID", array(
        ':newReferenceID' => $newReferenceID,
        ':ReferenceID' => $ref['ReferenceID'],
        ':RelOwnerUserID' => $ref['RelOwnerUserID'],
      ));
      db_query("UPDATE {tagging} SET ReferenceID = :newReferenceID " .
        " WHERE ReferenceID = :ReferenceID AND RelOwnerUserID = :RelOwnerUserID", array(
        ':newReferenceID' => $newReferenceID,
        ':ReferenceID' => $ref['ReferenceID'],
        ':RelOwnerUserID' => $ref['RelOwnerUserID'],
      ));
      db_query("UPDATE {custom_field_values} SET ReferenceID = :newReferenceID " .
        " WHERE ReferenceID = :ReferenceID AND RelOwnerUserID = :RelOwnerUserID", array(
        ':newReferenceID' => $newReferenceID,
        ':ReferenceID' => $ref['ReferenceID'],
        ':RelOwnerUserID' => $ref['RelOwnerUserID'],
      ));
    }
    // remove reference entities
    db_delete('reference')
      ->condition('RelOwnerUserID', $from_account->uid)
      ->execute();

    // userid only
    kt_query("UPDATE {campaigns} SET RelOwnerUserID = %d, CampaignName = CONCAT(CampaignName,'%s') WHERE RelOwnerUserID = %d", $to_account->uid, $from_name, $from_account->uid);
    kt_query("UPDATE {custom_fields} SET RelOwnerUserID = %d, FieldName = CONCAT(FieldName,'%s') WHERE RelOwnerUserID = %d", $to_account->uid, $from_name, $from_account->uid);
    kt_query("UPDATE {smsnumbers} SET RelOwnerUserID = %d, Label = CONCAT(Label,'%s') WHERE RelOwnerUserID = %d", $to_account->uid, $from_name, $from_account->uid);

    db_update('split_tests')
      ->fields(array(
        'RelOwnerUserID' => $to_account->uid,
      ))
      ->condition('RelOwnerUserID', $from_account->uid)
      ->execute();

    db_update('stats_activity')
      ->fields(array(
        'RelOwnerUserID' => $to_account->uid,
      ))
      ->condition('RelOwnerUserID', $from_account->uid)
      ->execute();

    // special case: email
    kt_query("UPDATE {emails} SET RelUserID = %d, EmailName = CONCAT(EmailName,'%s') WHERE RelUserID = %d", $to_account->uid, $from_name, $from_account->uid);

    db_update('emails_revision')
      ->fields(array(
        'RelUserID' => $to_account->uid,
      ))
      ->condition('RelUserID', $from_account->uid)
      ->execute();

    // special case: tags
    // first: add from account name
    $ArrayTags = Tag::RetrieveManualTags($from_account->uid);
    foreach ($ArrayTags as $tag) {
      $tag['TagName'] .= $from_name;
      Tag::UpdateTag($tag);
    }
    // then: move all
    db_update('tag')
      ->fields(array(
        'RelOwnerUserID' => $to_account->uid,
      ))
      ->condition('RelOwnerUserID', $from_account->uid)
      ->execute();
    Tag::ResetTagsCache($from_account->uid);
    Tag::ResetTagsCache($to_account->uid);

    // special case: lists
    // first: rename default subscription process
    db_update('subscriber_lists')
      ->fields(array(
        'Name' => t('Default subscription process'),
      ))
      ->condition('RelOwnerUserID', $from_account->uid)
      ->condition('Name', '')
      ->execute();
    // then: move all
    kt_query("UPDATE {subscriber_lists} SET RelOwnerUserID = %d, Name = CONCAT(Name,'%s') WHERE RelOwnerUserID = %d", $to_account->uid, $from_name, $from_account->uid);

    // special case: signatures
    // first: remove default signature as this already exists in destination
    db_delete('signatures')
      ->condition('RelOwnerUserID', $from_account->uid)
      ->condition('SignatureName', '')
      ->execute();
    // then: move signatures
    kt_query("UPDATE {signatures} SET RelOwnerUserID = %d, SignatureName = CONCAT(SignatureName,'%s') WHERE RelOwnerUserID = %d", $to_account->uid, $from_name, $from_account->uid);
    kt_query("UPDATE {signatures_revision} SET RelOwnerUserID = %d WHERE RelOwnerUserID = %d", $to_account->uid, $from_account->uid);

    // userid + subscriberid
    $query = "SELECT s.* FROM {subscribers} s WHERE s.RelOwnerUserID = :RelOwnerUserID";
    $result = db_query($query, array(':RelOwnerUserID' => $from_account->uid));
    while ($from = kt_fetch_array($result)) {

      $newid = 0;

      // check if subscriber already exists by contact info
      $result2 = db_query("SELECT ContactInfo, SubscriptionType FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID ORDER BY SubscriptionType ASC", array(
        ':RelOwnerUserID' => $from_account->uid,
        ':RelSubscriberID' => $from['SubscriberID'],
      ));
      while ($from_subscription = kt_fetch_array($result2)) {

        $result3 = db_query("SELECT RelSubscriberID FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND ContactInfo = :ContactInfo AND SubscriptionType = :SubscriptionType", array(
          ':RelOwnerUserID' => $to_account->uid,
          ':ContactInfo' => $from_subscription['ContactInfo'],
          ':SubscriptionType' => $from_subscription['SubscriptionType'],
        ));
        $newid = $result3->fetchField();
        if (!empty($newid)) {
          // we found a matching contact info
          break;
        }
      }

      if (empty($newid)) {
        // NO: just move subscribe to new account

        db_update('fbl_reports')
          ->fields(array(
            'UserID' => $to_account->uid,
          ))
          ->condition('UserID', $from_account->uid)
          ->condition('SubscriberID', $from['SubscriberID'])
          ->execute();

        db_update('subscriber_history')
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_update('tagging')
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_update(TransactionalQueue::TABLE_NAME)
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_update(AutoresponderQueue::TABLE_NAME)
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_update(NewsletterQueue::TABLE_NAME)
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_update('subscribers')
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('SubscriberID', $from['SubscriberID'])
          ->execute();

        db_update('subscription')
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_update('subscription_reference')
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_update('custom_field_values')
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

      }
      else {
        // YES: keep or drop subscriber data

        // 1. keep all data, that depends on a copied email

        db_update('fbl_reports')
          ->fields(array(
            'UserID' => $to_account->uid,
            'SubscriberID' => $newid,
          ))
          ->condition('UserID', $from_account->uid)
          ->condition('SubscriberID', $from['SubscriberID'])
          ->execute();

        db_update('subscriber_history')
          ->fields(array(
            'RelOwnerUserID' => $to_account->uid,
            'RelSubscriberID' => $newid,
          ))
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        // move values only, if they dont exist in destination (happens if two subscribers are copied into one)
        db_query("UPDATE IGNORE {tagging} SET RelOwnerUserID = :ToRelOwnerUserID, RelSubscriberID = :ToRelSubscriberID WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID", array(
          ':ToRelOwnerUserID' => $to_account->uid,
          ':ToRelSubscriberID' => $newid,
          ':RelOwnerUserID' => $from_account->uid,
          ':RelSubscriberID' => $from['SubscriberID']
        ));

        // move values only, if they dont exist in destination (happens if two subscribers are copied into one)
        db_query("UPDATE IGNORE {".TransactionalQueue::TABLE_NAME."} SET RelOwnerUserID = :ToRelOwnerUserID, RelSubscriberID = :ToRelSubscriberID WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID", array(
          ':ToRelOwnerUserID' => $to_account->uid,
          ':ToRelSubscriberID' => $newid,
          ':RelOwnerUserID' => $from_account->uid,
          ':RelSubscriberID' => $from['SubscriberID']
        ));

        db_query("UPDATE IGNORE {".AutoresponderQueue::TABLE_NAME."} SET RelOwnerUserID = :ToRelOwnerUserID, RelSubscriberID = :ToRelSubscriberID WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID", array(
          ':ToRelOwnerUserID' => $to_account->uid,
          ':ToRelSubscriberID' => $newid,
          ':RelOwnerUserID' => $from_account->uid,
          ':RelSubscriberID' => $from['SubscriberID']
        ));

        db_query("UPDATE IGNORE {".NewsletterQueue::TABLE_NAME."} SET RelOwnerUserID = :ToRelOwnerUserID, RelSubscriberID = :ToRelSubscriberID WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID", array(
          ':ToRelOwnerUserID' => $to_account->uid,
          ':ToRelSubscriberID' => $newid,
          ':RelOwnerUserID' => $from_account->uid,
          ':RelSubscriberID' => $from['SubscriberID']
        ));

        // 2. override/add data

        // check if subscription already exists by contact info (start with email address)
        $result2 = db_query("SELECT * FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID ORDER BY SubscriptionType ASC", array(
          ':RelOwnerUserID' => $from_account->uid,
          ':RelSubscriberID' => $from['SubscriberID'],
        ));
        while ($from_subscription = kt_fetch_array($result2)) {

          $result3 = db_query("SELECT RelSubscriberID FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND ContactInfo = :ContactInfo AND SubscriptionType = :SubscriptionType", array(
            ':RelOwnerUserID' => $to_account->uid,
            ':ContactInfo' => $from_subscription['ContactInfo'],
            ':SubscriptionType' => $from_subscription['SubscriptionType'],
          ));
          $anotherid = $result3->fetchField();

          if (empty($anotherid)) {
            // no other subscriber with matching contact info -> move
            db_update('subscription')
              ->fields(array(
                'RelOwnerUserID' => $to_account->uid,
                'RelSubscriberID' => $newid,
              ))
              ->condition('RelOwnerUserID', $from_account->uid)
              ->condition('ContactInfo', $from_subscription['ContactInfo'])
              ->condition('SubscriptionType', $from_subscription['SubscriptionType'])
              ->execute();

            db_update('subscription_reference')
              ->fields(array(
                'RelOwnerUserID' => $to_account->uid,
                'RelSubscriberID' => $newid,
              ))
              ->condition('RelOwnerUserID', $from_account->uid)
              ->condition('ContactInfo', $from_subscription['ContactInfo'])
              ->condition('SubscriptionType', $from_subscription['SubscriptionType'])
              ->execute();
          }
          elseif ($newid == $anotherid) {
            // matching contact info -> update SubscriptionStatus if OptIn
            db_update('subscription')
              ->fields(array(
                'SubscriptionStatus' => $from_subscription['SubscriptionStatus'],
                'SubscriptionDate' => $from_subscription['SubscriptionDate'],
              ))
              ->condition('RelOwnerUserID', $to_account->uid)
              ->condition('ContactInfo', $from_subscription['ContactInfo'])
              ->condition('SubscriptionType', $from_subscription['SubscriptionType'])
              ->condition('SubscriptionStatus', Subscribers::SUBSCRIPTIONSTATUS_OPTIN)
              ->execute();

            // override with LastOpen if newer
            db_update('subscription')
              ->fields(array(
                'LastOpenDate' => $from_subscription['LastOpenDate'],
              ))
              ->condition('RelOwnerUserID', $to_account->uid)
              ->condition('ContactInfo', $from_subscription['ContactInfo'])
              ->condition('SubscriptionType', $from_subscription['SubscriptionType'])
              ->condition('LastOpenDate', $from_subscription['LastOpenDate'], '<')
              ->execute();

            // copy all non existent references (all ReferenceID in from_account are already converted to those of to_account)
            $references = db_query("SELECT * FROM {subscription_reference} WHERE ".
              " RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND SubscriptionType = :SubscriptionType AND ContactInfo = :ContactInfo ",
            [
              ':RelOwnerUserID' =>  $from_account->uid,
              ':RelSubscriberID' => $from_subscription['RelSubscriberID'],
              ':SubscriptionType' => $from_subscription['SubscriptionType'],
              ':ContactInfo' => $from_subscription['ContactInfo'],
            ]);
            while ($reference = $references->fetchAssoc()) {
              db_query("INSERT IGNORE INTO {subscription_reference} " .
                " (RelOwnerUserID, ReferenceID, RelSubscriberID, SubscriptionType, ContactInfo, Optional, SubscriptionDate) " .
                " VALUES " .
                " (:RelOwnerUserID, :ReferenceID, :RelSubscriberID, :SubscriptionType, :ContactInfo, :Optional, :SubscriptionDate) ",
                [
                  ':RelOwnerUserID' => $to_account->uid,
                  ':RelSubscriberID' => $newid,
                  ':SubscriptionType' => $reference['SubscriptionType'],
                  ':ContactInfo' => $reference['ContactInfo'],
                  ':ReferenceID' => $reference['ReferenceID'],
                  ':Optional' => $reference['Optional'],
                  ':SubscriptionDate' => $reference['SubscriptionDate'],
                ]);
            }
          }
          // (else) same contact info in another subscriber -> ignore (drop)

        }

        // add custom field value from source if not exists

        $addressexists = db_query("SELECT 1 FROM {custom_field_values} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID IN ('Street1', 'Street2', 'City', 'State', 'Zip', 'Country')", array(
          ':RelOwnerUserID' => $to_account->uid,
          ':RelSubscriberID' => $newid
        ))->fetchField();
        if (empty($addressexists)) {
          // move address fields together
          db_query("UPDATE IGNORE {custom_field_values} SET RelOwnerUserID = :ToRelOwnerUserID, RelSubscriberID = :ToRelSubscriberID " .
            " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID IN ('Street1', 'Street2', 'City', 'State', 'Zip', 'Country')", [
            ':ToRelOwnerUserID' => $to_account->uid,
            ':ToRelSubscriberID' => $newid,
            ':RelOwnerUserID' => $from_account->uid,
            ':RelSubscriberID' => $from['SubscriberID']
          ]);
        }
        // move non address fields
        db_query("UPDATE IGNORE {custom_field_values} SET RelOwnerUserID = :ToRelOwnerUserID, RelSubscriberID = :ToRelSubscriberID ".
          " WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelFieldID NOT IN ('Street1', 'Street2', 'City', 'State', 'Zip', 'Country')", array(
          ':ToRelOwnerUserID' => $to_account->uid,
          ':ToRelSubscriberID' => $newid,
          ':RelOwnerUserID' => $from_account->uid,
          ':RelSubscriberID' => $from['SubscriberID']
        ));

        // 3. drop other data of source subscriber

        db_delete('tagging')
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_delete(TransactionalQueue::TABLE_NAME)
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_delete(AutoresponderQueue::TABLE_NAME)
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_delete(NewsletterQueue::TABLE_NAME)
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_delete('custom_field_values')
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_delete('subscribers')
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('SubscriberID', $from['SubscriberID'])
          ->execute();

        db_delete('subscription')
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();

        db_delete('subscription_reference')
          ->condition('RelOwnerUserID', $from_account->uid)
          ->condition('RelSubscriberID', $from['SubscriberID'])
          ->execute();
      }

    }

    // the old account is empty now, but we should keep it functional, see: klicktipp_user('insert'), what needs to be re-created
    // create default signature
    $SignatureID = Signatures::CreateDefaultSignature($from_account->uid);
    Signatures::CreateRevision($from_account->uid, $SignatureID);
    // create default subscription process
    $list_id = Lists::InsertDB(array(
      'Name' => '',
      'RelOwnerUserID' => $from_account->uid
    ));

    // commit transaction
    kt_commit_transaction();

    drupal_set_message("ALL DATA MOVED FROM {$form_state['values']['fromaccount']} TO {$form_state['values']['toaccount']}");
  }
}

/**
 * Show blocking campaign stats
 */
function klicktipp_blocking_campaigns_form($form, &$form_state) {

  Libraries::include('prod_check.inc');

  $form['desc1'] = array(
    '#type' => 'markup',
    '#markup' => '<p>This table shows all campaigns, that have more pending subscribers, than can be processed with one queue run. This may block the queue for other campaigns. '.
        'Data is taken once an hour and aggregated for the last 24 hours (see periods). Rows in red indicate a constant high pending rate over a long period.</p>'.
      '<p><strong>To analyze complaints: divide the user id of the complaining customer by 1024 and check the corresponding slice for other campaigns with high pending counts.</strong></p>',
  );

  $form['stats'] = array(
    '#type' => 'markup',
    '#markup' => _klicktipp_blocking_campaigns(),
  );

  return $form;
}

/**
 * Configure klicktipp profiler.
 */
function klicktipp_profiler_form($form, &$form_state) {

  $form['cron'] = array(
    '#type' => 'fieldset',
    '#title' => 'Cron Settings',
  );

  $form['cron']['klicktipp_mcp_enabled'] = array(
    '#type' => 'checkbox',
    '#title' => 'Enable morning cron profiling',
    '#default_value' => variable_get('klicktipp_mcp_enabled', 0),
    '#description' => 'If enabled the profiler will analyse every morning cron job',
  );

  $form['cron']['klicktipp_nl_deletion_cron_campaign_to_delete'] = array(
    '#type' => 'textfield',
    '#title' => 'newsletter_queue: Amount of newsletter to be considered during newsletter deletion run for newsletter_queue',
    '#default_value' => Settings::get('klicktipp_nl_deletion_cron_campaign_to_delete'),
    '#description' => 'This number is the amount of newsletters, which will be considered to check if they have entries in the newsletter_queue table, which should be deleted.',
  );

  $form['cron']['klicktipp_nl_tags_deletion_cron_campaign_to_delete'] = array(
    '#type' => 'textfield',
    '#title' => 'tagging: Amount of newsletters to be considered during newsletter tag deletion run',
    '#default_value' => Settings::get('klicktipp_nl_tags_deletion_cron_campaign_to_delete'),
    '#description' => 'This number is the amount of newsletters, which will be considered to check if they have entries in the tagging table, which should be deleted.',
  );

  $form['cron']['klicktipp_transactional_queue_cron_campaigns_to_delete'] = array(
    '#type' => 'textfield',
    '#title' => 'Transactional queue: number of non-transactional campaigns, which items can be delete by one cron run ',
    '#default_value' => variable_get('klicktipp_transactional_queue_cron_campaigns_to_delete', 0),
    '#description' => 'After autoresponder exclusion there are a lot of autoresponder items in transactional queue. There is a cron to delete them. ' .
      'This config property defines the number of non-processflow campaigns, which items can be deleted by a cron run.',
    '#element_validate' => ['element_validate_integer'],
  );

  $form['cron']['klicktipp_transactional_queue_cron_entries_to_delete'] = array(
    '#type' => 'textfield',
    '#title' => 'Transactional queue: number of non-transactional campaigns items to be deleted by single query ',
    '#default_value' => variable_get('klicktipp_transactional_queue_cron_entries_to_delete', 0),
    '#description' => 'After autoresponder exclusion there are a lot of autoresponder items in transactional queue. There is a cron to delete them. ' .
      'This config property defines the number of non-processflow campaigns items, which can be deleted by single delete query.',
    '#element_validate' => ['element_validate_integer'],
  );

  $form['cron']['klicktipp_autoresponder_queue_cron_campaigns_to_delete'] = array(
    '#type' => 'textfield',
    '#title' => 'Autoresponder queue: number of non-autoresponder campaigns, which items can be delete by one cron run ',
    '#default_value' => variable_get('klicktipp_autoresponder_queue_cron_campaigns_to_delete', 0),
    '#description' => 'After autoresponder exclusion there are a lot of transactional items in autoresponder queue. There is a cron to delete them. ' .
      'This config property defines the number of non-autoresponder campaigns, which items can be deleted by a cron run.',
    '#element_validate' => ['element_validate_integer'],
  );

  $form['cron']['klicktipp_autoresponder_queue_cron_entries_to_delete'] = array(
    '#type' => 'textfield',
    '#title' => 'Autoresponder queue: number of non-autoresponder campaigns items to be deleted by single query ',
    '#default_value' => variable_get('klicktipp_autoresponder_queue_cron_entries_to_delete', 0),
    '#description' => 'After autoresponder exclusion there are a lot of transactional items in autoresponder queue. There is a cron to delete them. ' .
      'This config property defines the number of non-autoresponder campaigns items, which can be deleted by single delete query.',
    '#element_validate' => ['element_validate_integer'],
  );

  $form['delete_newsletter_queue'] = array(
    '#type' => 'fieldset',
    '#title' => 'Delete Newsletter Queue',
  );

  $form['delete_newsletter_queue']['klicktipp_newsletter_entries_to_delete'] = array(
    '#type' => 'textfield',
    '#title' => 'Amount of entries to delete for newsletter campaign per run in delete_newsletter_queue',
    '#default_value' => Settings::get('klicktipp_newsletter_entries_to_delete'),
    '#description' => 'This number is the amount of entries for a newsletters campaign, which will be deleted from the db within one job in delete_newsletter_queue.',
  );

  $form['delete_taggings'] = array(
    '#type' => 'fieldset',
    '#title' => 'Delete Taggings',
  );

  $form['delete_taggings']['klicktipp_newsletter_tag_entries_to_delete'] = array(
    '#type' => 'textfield',
    '#title' => 'Amount of entries to delete for newsletter campaign per run in tagging table',
    '#default_value' => Settings::get('klicktipp_newsletter_tag_entries_to_delete'),
    '#description' => 'This number is the amount of entries for a newsletters campaign, which will be deleted from the db within one job in tagging table.',
  );

  $form['send_queue'] = array(
    '#type' => 'fieldset',
    '#title' => 'Send Queue',
  );

  $form['send_queue']['klicktipp_sq_slice_min'] = array(
    '#type' => 'textfield',
    '#title' => 'Minimum queue job size (create_queue)',
    '#default_value' => variable_get('klicktipp_sq_slice_min', 1000),
    '#description' => 'The minimum amount of emails to create a job at all. Smaller newsletter will be processed by the app directly, but need to be executed in a web request, so this needs to be processable in 15 sec.',
  );

  $form['send_queue']['klicktipp_wts_queue_slice'] = array(
    '#type' => 'textfield',
    '#title' => 'Send queue job size (create_queue)',
    '#default_value' => variable_get('klicktipp_wts_queue_slice', 10000),
    '#description' => 'How many emails should be put into one queue job. This should be processable before the queue timeout.',
  );

  $beanstalk_send_queue = variable_get("beanstalk_queue_send_queue", 0);
  $form['send_queue']['klicktipp_sq_timeout'] = array(
    '#type' => 'textfield',
    '#title' => 'Send queue job timeout (send_queue)',
    '#default_value' => variable_get('klicktipp_sq_timeout', intval(floor($beanstalk_send_queue['ttr'] / 2))),
    '#description' => "Send queue timeout. After that time the job will be re-queued. Note: This value MUST be much smaller than the Beanstalk TTR (= {$beanstalk_send_queue['ttr']}).",
  );

  $form['send_queue']['klicktipp_sqp_enabled'] = array(
    '#type' => 'checkbox',
    '#title' => 'Profiler ON',
    '#default_value' => variable_get('klicktipp_sqp_enabled', 0),
    '#description' => 'If enabled the profiler will analyse every send queue job',
  );

  $form['send_queue']['klicktipp_sqp_sps'] = array(
    '#type' => 'textfield',
    '#title' => 'Subscribers per second',
    '#default_value' => variable_get('klicktipp_sqp_sps', 100),
    '#description' => 'If the queue falls below this limit the profiler will write the results to disc.',
  );

  $form['web_transactional_send'] = array(
    '#type' => 'fieldset',
    '#title' => 'Web Transactional Send',
  );

  $form['web_transactional_send']['klicktipp_wts_limit'] = array(
    '#type' => 'textfield',
    '#title' => 'Queue limit',
    '#default_value' => variable_get('klicktipp_wts_limit', 3000),
    '#description' => 'How many emails should be processed per transactional send (this limits the maximum output).',
  );

  $options = array(
    0 => 'No slices (ok for test instances)',
    2 => '2 slices',
    4 => '4 slices',
    8 => '8 slices',
    16 => '16 slices',
    32 => '32 slices',
    64 => '64 slices',
    128 => '128 slices',
    256 => '256 slices',
    512 => '512 slices',
    1024 => '1024 slices',
  );
  $form['web_transactional_send']['klicktipp_wts_slices'] = array(
    '#type' => 'select',
    '#title' => 'Queue slices',
    '#options' => $options,
    '#default_value' => variable_get('klicktipp_wts_slices', 0),
    '#description' => 'No of queue slices. All slices should be processed within one minute. '.
      '80% of jobs will be processed in 5 seconds, 20% in one minute. '.
      'So you need 40% of slices as beanstalk runners, e.g. 25 runners for 64 slices.',
  );

  $form['web_transactional_send']['klicktipp_wts_min_multisenddelay'] = array(
    '#type' => 'textfield',
    '#title' => 'Minimum delay of multiple sends',
    '#default_value' => variable_get('klicktipp_wts_min_multisenddelay', 0),
    '#description' => 'Minimum Delay in MINUTES of next accepted trigger (from finished tagging).',
    '#element_validate' => array('element_validate_integer_positive'),
    '#required' => TRUE,
  );

  $form['web_transactional_send']['klicktipp_wts_max_multisenddelay'] = array(
    '#type' => 'textfield',
    '#title' => 'Maximum delay of multiple sends',
    '#default_value' => variable_get('klicktipp_wts_max_multisenddelay', 0),
    '#description' => 'Minimum Delay in MINUTES of next accepted trigger (from finished tagging).',
    '#element_validate' => array('element_validate_integer_positive'),
    '#required' => TRUE,
  );

  $form['web_transactional_send']['klicktipp_wts_enabled'] = array(
    '#type' => 'checkbox',
    '#title' => 'Profiler ON',
    '#default_value' => variable_get('klicktipp_wts_enabled', 0),
    '#description' => 'If enabled the profiler will analyse every transactional queue run',
  );

  $form['web_transactional_send']['klicktipp_wts_eps'] = array(
    '#type' => 'textfield',
    '#title' => 'Emails per second',
    '#default_value' => variable_get('klicktipp_wts_eps', 100),
    '#description' => 'If the queue falls below this limit the profiler will write the results to disc.',
  );

  $form['subscriber_queue'] = array(
    '#type' => 'fieldset',
    '#title' => 'Subscriber Queue',
  );

  $form['subscriber_queue']['klicktipp_subscriber_queue_profiler_enabled'] = array(
    '#type' => 'checkbox',
    '#title' => 'Profiler ON',
    '#default_value' => variable_get('klicktipp_subscriber_queue_profiler_enabled', 0),
    '#description' => 'If enabled the profiler will analyse every subscriber queue run',
  );

  $form['subscriber_queue']['klicktipp_subscriber_queue_limit'] = array(
    '#type' => 'textfield',
    '#title' => 'Subscriber Queue limit',
    '#default_value' => variable_get('klicktipp_subscriber_queue_limit', 1000),
    '#description' => 'How many subscribers should be processed per queue job (this limits the maximum output).',
  );

  $form['subscriber_queue']['klicktipp_subscriber_queue_profiler_tps'] = array(
    '#type' => 'textfield',
    '#title' => 'Subscribers per second',
    '#default_value' => variable_get('klicktipp_subscriber_queue_profiler_tps', 10),
    '#description' => 'If the queue falls below this limit the profiler will write the results to disc.',
  );

  $form['active_payments_queue'] = array(
    '#type' => 'fieldset',
    '#title' => 'Active Payments Queue',
  );

  $form['active_payments_queue']['klicktipp_active_payments_queue_limit'] = array(
    '#type' => 'textfield',
    '#title' => t('Active Payments Queue limit'),
    '#default_value' => variable_get('klicktipp_active_payments_queue_limit', 100),
    '#description' => 'How many subscribers should be processed per queue job.',
  );

  $form['active_payments_queue']['klicktipp_active_payments_rebuild_smart_tags'] = array(
    '#type' => 'checkbox',
    '#title' => t('Active Payments Rebuild'),
    '#default_value' => variable_get('klicktipp_active_payments_rebuild_smart_tags', 0),
    '#description' => 'Check to rebuild all smart tags for active payments (will be reset by job).',
  );

  $form['delete_pending_subscribers_queue'] = array(
    '#type' => 'fieldset',
    '#title' => 'Delete Pending Subscribers Queue',
  );

  $form['delete_pending_subscribers_queue']['klicktipp_delete_pending_subscribers_queue_limit'] = array(
    '#type' => 'textfield',
    '#title' => 'Delete Pending Subscribers Queue limit',
    '#default_value' => variable_get('klicktipp_delete_pending_subscribers_queue_limit', 100),
    '#description' => 'How many lists should be processed per queue job (this limits the maximum output).',
  );

  $form['outbound_queue'] = array(
    '#type' => 'fieldset',
    '#title' => t('Outbound Queue'),
  );

  $form['outbound_queue']['klicktipp_outbound_trigger_delay'] = array(
    '#type' => 'textfield',
    '#title' => t('Multiple Outbound Triggers Delay'),
    '#default_value' => variable_get('klicktipp_outbound_trigger_delay', ""),
    '#description' => 'Allow multiple Outbound triggers by tag (not in automations) after this period. Use notations allowed in strtotime, e.g. "1 day", "6 hours", "15 minutes". Leave empty to process all triggers.',
  );

  $form['datadog'] = array(
    '#type' => 'fieldset',
    '#title' => 'Datadog',
    '#description' => 'If enabled the app will send monitoring data to datadog',
  );
  $form['datadog']['klicktipp_dd_enabled'] = array(
    '#type' => 'checkbox',
    '#title' => 'Enable Datadog metrics',
    '#default_value' => variable_get('klicktipp_dd_enabled', 0),
  );
  $form['datadog']['klicktipp_dd_apikey'] = array(
    '#type' => 'textfield',
    '#title' => 'Datadog API key',
    '#default_value' => variable_get('klicktipp_dd_apikey', ''),
  );
  $form['datadog']['klicktipp_dd_host'] = array(
    '#type' => 'textfield',
    '#title' => 'App host in Datadog',
    '#default_value' => variable_get('klicktipp_dd_host', ''),
  );

  $form['recipient-refresh'] = array(
    '#type' => 'fieldset',
    '#title' => 'Automations/Newsletters Recipient Calculation Refresh',
    '#description' => 'Sets the slice size and max refresh time.',
  );
  $form['recipient-refresh']['klicktipp_automation_start_recipient_calculation_batch_size'] = array(
    '#type' => 'textfield',
    '#title' => "Automation's Slice Size",
    '#default_value' => variable_get('klicktipp_automation_start_recipient_calculation_batch_size', '50'),
    '#element_validate' => array('element_validate_integer_positive'),
  );
  $form['recipient-refresh']['klicktipp_automation_start_recipient_calculation_max_time'] = array(
    '#type' => 'textfield',
    '#title' => 'Automations: Max seconds a user has to wait until the count is refreshed in the UI',
    '#default_value' => variable_get('klicktipp_automation_start_recipient_calculation_max_time', '10'),
    '#element_validate' => array('element_validate_integer_positive'),
  );

  $form['recipient-refresh']['klicktipp_newsletter_start_recipient_calculation_batch_size'] = array(
    '#type' => 'textfield',
    '#title' => "Newsletter's Slice Size",
    '#default_value' => variable_get('klicktipp_newsletter_start_recipient_calculation_batch_size', '50'),
    '#element_validate' => array('element_validate_integer_positive'),
  );
  $form['recipient-refresh']['klicktipp_newsletter_start_recipient_calculation_max_time'] = array(
    '#type' => 'textfield',
    '#title' => 'Newsletters: Max seconds a user has to wait until the count is refreshed in the UI',
    '#default_value' => variable_get('klicktipp_newsletter_start_recipient_calculation_max_time', '10'),
    '#element_validate' => array('element_validate_integer_positive'),
  );

  $form['customer-slices'] = array(
    '#type' => 'fieldset',
    '#title' => 'Customer Slices',
    '#description' => 'Selected customers are processed in their own slices, so other slices do not stack up because of them. <br/>'.
      '<span style="color:red;font-weight:bold;">NOTE: Changing this will start a complex and performance intense process. <br/>'.
      '1. Go to Beanstalk console, stop tubes transactional_queue, autoresponder_queue, newsletter_queue, subscriber_queue and subscriber_lowprio_queue. <br/>'.
      '2. Wait for the workers in "current-jobs-reserved" to finish.<br/>'.
      '3. Submit changes.</span>',
  );
  $form['customer-slices']['klicktipp_customers_in_separate_slices_field'] = array(
    '#type' => 'textarea',
    '#title' => "User IDs",
    '#default_value' => implode(',', Settings::get("klicktipp_customers_in_separate_slices")),
    '#description' => 'Comma separated list of user ids to be processed in own slices.',
  );

  // --- KlickTipp Watchdog Profile

  $watchdogProfile = Settings::get('klicktipp_watchdog_profile');

  $form['klicktipp_watchdog_profile'] = array(
    '#type' => 'fieldset',
    '#title' => 'Watchdog Profile',
    '#tree' => TRUE
  );

  $form['klicktipp_watchdog_profile']['dblog'] = array(
    '#type' => 'fieldset',
    '#title' => 'DBLog',
    '#tree' => TRUE,
    '#description' => 'Define which log types/messages WILL NOT be written to the database.<br />Enter 1 type/message per line.'
  );

  $form['klicktipp_watchdog_profile']['dblog']['type'] = array(
    '#type' => 'textarea',
    '#title' => 'Filter by type',
    '#default_value' => implode("\n", $watchdogProfile['dblog']['type']),
    '#description' => 'watchdog(TYPE, $message, $variables, $severity)'
  );

  $form['klicktipp_watchdog_profile']['dblog']['message'] = array(
    '#type' => 'textarea',
    '#title' => 'Filter by message',
    '#default_value' => implode("\n", $watchdogProfile['dblog']['message']),
    '#description' => 'watchdog($type, MESSAGE, $variables, $severity)'
  );

  $form['klicktipp_watchdog_profile']['dblog']['severity'] = array(
    '#type' => 'select',
    '#title' => 'Minumum severity',
    '#description' => 'Entries with lower severity will be ignored',
    '#default_value' => $watchdogProfile['dblog']['severity'] ?? WATCHDOG_DEBUG,
    '#options' => Watchdog::SEVERITY_MAP,
  );

  $form['klicktipp_watchdog_profile']['syslog'] = array(
    '#type' => 'fieldset',
    '#title' => 'SysLog -> DataDog',
    '#tree' => TRUE,
    '#description' => 'Define which log types/messages WILL NOT be sent to the DataDog.<br />Enter 1 type/message per line.'
  );

  $form['klicktipp_watchdog_profile']['syslog']['type'] = array(
    '#type' => 'textarea',
    '#title' => 'Filter by type',
    '#default_value' => implode("\n", $watchdogProfile['syslog']['type']),
    '#description' => 'watchdog(TYPE, $message, $variables, $severity)'
  );

  $form['klicktipp_watchdog_profile']['syslog']['message'] = array(
    '#type' => 'textarea',
    '#title' => 'Filter by message',
    '#default_value' => implode("\n", $watchdogProfile['syslog']['message']),
    '#description' => 'watchdog($type, MESSAGE, $variables, $severity)'
  );

  $form['klicktipp_watchdog_profile']['syslog']['severity'] = array(
    '#type' => 'select',
    '#title' => 'Minumum severity',
    '#description' => 'Entries with lower severity will be ignored',
    '#default_value' => $watchdogProfile['syslog']['severity'] ?? WATCHDOG_DEBUG,
    '#options' => Watchdog::SEVERITY_MAP,
  );

  $form['klicktipp_watchdog_profile']['stdout'] = array(
    '#type' => 'fieldset',
    '#title' => 'Stdout',
    '#tree' => TRUE,
    '#description' => 'Define which log types/messages WILL NOT be sent to the DataDog.<br />Enter 1 type/message per line.'
  );

  $form['klicktipp_watchdog_profile']['stdout']['type'] = array(
    '#type' => 'textarea',
    '#title' => 'Filter by type',
    '#default_value' => implode("\n", $watchdogProfile['stdout']['type']),
    '#description' => 'watchdog(TYPE, $message, $variables, $severity)'
  );

  $form['klicktipp_watchdog_profile']['stdout']['message'] = array(
    '#type' => 'textarea',
    '#title' => 'Filter by message',
    '#default_value' => implode("\n", $watchdogProfile['stdout']['message']),
    '#description' => 'watchdog($type, MESSAGE, $variables, $severity)'
  );

  $form['klicktipp_watchdog_profile']['stdout']['severity'] = array(
    '#type' => 'select',
    '#title' => 'Minimum severity',
    '#description' => 'Entries with lower severity will be ignored',
    '#default_value' => $watchdogProfile['stdout']['severity'] ?? WATCHDOG_DEBUG,
    '#options' => Watchdog::SEVERITY_MAP,
  );

  $form['klicktipp_newsletter_pregeneration'] = array(
    '#type' => 'fieldset',
    '#title' => 'Newsletter pre-generation',
  );

  $form['klicktipp_newsletter_pregeneration']['klicktipp_assumed_email_throughput'] = array(
    '#type' => 'textfield',
    '#title' => 'Assumed e-mail throughput',
    '#default_value' => PreGenerationHandler::getThroughputPerMinute(),
    '#description' => 'Assumed number of e-mail we can generate per MINUTE.',
    '#required' => TRUE,
    '#element_validate' => array('element_validate_integer_positive'),
  );

  $form['klicktipp_newsletter_pregeneration']['klicktipp_min_time_for_cancellation'] = array(
    '#type' => 'textfield',
    '#title' => 'Min remaining time to cancel dispatching of pre-generated e-mails',
    '#default_value' => PreGenerationHandler::getMinTimeForCancellation(),
    '#description' => 'Time in SECONDS until send datetime. If the remaining time fails below this value, it is not possible to stop campaign anymore',
    '#required' => TRUE,
    '#element_validate' => array('element_validate_integer_positive'),
  );

  $form['klicktipp_newsletter_pregeneration']['klicktipp_pregeneration_min_basetime'] = array(
    '#type' => 'textfield',
    '#title' => 'Minimum base time to add to calculated time',
    '#default_value' => PreGenerationHandler::getMinBasetime(),
    '#description' => 'Time in SECONDS.',
    '#required' => TRUE,
    '#element_validate' => array('element_validate_integer_positive'),
  );

  $form['klicktipp_newsletter_pregeneration']['klicktipp_pregeneration_max_basetime'] = array(
    '#type' => 'textfield',
    '#title' => 'Maximum base time to add to calculated time',
    '#default_value' => PreGenerationHandler::getMaxBasetime(),
    '#description' => 'Time in SECONDS.',
    '#required' => TRUE,
    '#element_validate' => array('element_validate_integer_positive'),
  );


  $form['klicktipp_instant_web_trigger'] = array(
    '#type' => 'fieldset',
    '#title' => 'Instant Web Triggers',
  );

  $form['klicktipp_instant_web_trigger']['klicktipp_instant_web_trigger_on'] = array(
    '#type' => 'checkbox',
    '#title' => 'Execute Web-Requests Instantly',
    '#default_value' => variable_get('klicktipp_instant_web_trigger_on', false),
  );

  $form['#submit'][] = 'klicktipp_profiler_form_submit';

  return system_settings_form($form);
}

function klicktipp_profiler_form_validate($form, &$form_state) {
  $beanstalk_send_queue = variable_get("beanstalk_queue_send_queue", 0);
  if ($form_state['values']['klicktipp_sq_timeout'] >= $beanstalk_send_queue['ttr']) {
    form_set_error('klicktipp_sq_timeout', 'Send queue job timeout MUST be smaller than the Beanstalk TTR.');
  }

  if ($form_state['values']['klicktipp_pregeneration_min_basetime'] >= $form_state['values']['klicktipp_pregeneration_max_basetime']) {
    form_set_error('klicktipp_pregeneration_min_basetime', 'Min base time must be lower then max base time');
  }


  $existing_customers_in_separate_slices = Settings::get("klicktipp_customers_in_separate_slices");
  $new_customers_in_separate_slices = array_filter(array_unique(array_map('trim', explode(',', $form_state['values']['klicktipp_customers_in_separate_slices_field']))));
  foreach($new_customers_in_separate_slices as $uid) {
    if (!in_array($uid, $existing_customers_in_separate_slices)) {
      // added customer
      $account = user_load($uid);
      if (empty($account)) {
        form_set_error('klicktipp_customers_in_separate_slices_field', t('Added user id !uid is not a valid user.', ['!uid' => $uid]));
      }
    }
  }
}

function klicktipp_profiler_form_submit($form, &$form_state) {

  $existing_customers_in_separate_slices = Settings::get("klicktipp_customers_in_separate_slices");
  $new_customers_in_separate_slices = array_filter(array_unique(array_map('trim', explode(',', $form_state['values']['klicktipp_customers_in_separate_slices_field']))));

  $data = [];

  // check added customers
  foreach($new_customers_in_separate_slices as $uid) {
    if (!in_array($uid, $existing_customers_in_separate_slices)) {
      $account = user_load($uid);
      if (empty($account)) {
        continue;
      }
      drupal_set_message(t('User !uid !name !mail has been ADDED to customer slices.',
        ['!uid' => $uid, '!name' => $account->name, '!mail' => $account->mail]));
      $data[] = [
        'add' => TRUE,
        'uid' => $uid,
      ];
    }
  }
  // check removed customers
  foreach($existing_customers_in_separate_slices as $uid) {
    if (!in_array($uid, $new_customers_in_separate_slices)) {
      $account = user_load($uid);
      if (empty($account)) {
        continue;
      }
      drupal_set_message(t('User !uid !name !mail has been REMOVED from customer slices.',
        ['!uid' => $uid, '!name' => $account->name, '!mail' => $account->mail]));
      $data[] = [
        'add' => FALSE,
        'uid' => $uid,
      ];
    }
  }

  if (!empty($data)) {
    _klicktipp_batch_set('move_customers_in_separate_slices', $data);
    variable_set('klicktipp_customers_in_separate_slices', $new_customers_in_separate_slices);
  }

  //format watchdog profile
  foreach ($form_state['values']['klicktipp_watchdog_profile'] as &$values) {
    $values['type'] = array_filter(array_map('trim', explode("\n", $values['type'])));
    $values['message'] = array_filter(array_map('trim', explode("\n", $values['message'])));
  }
}

/*
 * senderscore settings
 */
function klicktipp_senderscore_settings_overview_form($form, $form_state) {

  $hosts = variable_get(KLICKTIPP_SENDERSCORE_HOSTS, array());

  $weight = 0;

  $form = array();

  $header = array(
    '',
    'IP',
    'Hostname',
    'Failover',
    'Whitelabel',
    'zy0.de data',
  );

  // sort by TLD, SLD, host (reverse by point)
  uasort($hosts, function ($a, $b) {
    $ar = implode('.', array_reverse(explode('.', $a['hostname'])));
    $br = implode('.', array_reverse(explode('.', $b['hostname'])));
    return strcmp($ar, $br);
  });

  $rows = array();
  foreach ($hosts as $id => $host) {

    $zy0de = l(
      empty($host['zy0de']['ts']) ? (isset($host['zy0de']['ts']) ? 'no data' : '') : Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $host['zy0de']['ts']),
      "http://zy0.de/q/{$host['IP']}",
      array('attributes' => array('target' => '_blank'))
    );

    $rows[] = array(
      array('data' => $id),
      l($host['IP'], "admin/config/klicktipp/senderscore/$id/edit"),
      l($host['hostname'], "admin/config/klicktipp/senderscore/$id/edit"),
      empty($host['failover']) ? '' : 'X',
      empty($host['whitelabel']) ? '' : 'X',
      $zy0de,
    );

  }
  // add "add" link
  $rows[] = array(
    '',
    l('Add host', "admin/config/klicktipp/senderscore/new/edit"),
    '',
    '',
    '',
    '',
  );

  $form['senderscore_settings'] = array(
    '#type' => 'fieldset',
    '#title' => 'Senderscore settings',
    '#description' => 'The following table shows all defined senderscore hosts. ' . l('View scores.', 'senderscore'),
    '#weight' => $weight++,
  );

  $form['senderscore_settings']['senderscore_table'] = array(
    '#type' => 'markup',
    '#value' => theme('table', array('header' => $header, 'rows' => $rows)),
    '#weight' => $weight++,
  );

  return system_settings_form($form);
}

function klicktipp_senderscore_settings_edit_form($form, $form_state, $HostID = 'new') {

  $weight = 0;

  $form = array();

  $hosts = variable_get(KLICKTIPP_SENDERSCORE_HOSTS, array());

  $host = empty($hosts[$HostID]) ? array() : $hosts[$HostID];

  $form['HostID'] = array(
    '#type' => 'value',
    '#value' => empty($host) ? 'new' : $HostID,
  );

  $form['IP'] = array(
    '#type' => 'textfield',
    '#title' => 'IP',
    '#default_value' => empty($host['IP']) ? '' : $host['IP'],
    '#weight' => $weight++,
    '#description' => 'IP address of klicktipp host (e.g. "************"). Make empty to remove this host.',
  );

  $form['hostname'] = array(
    '#type' => 'textfield',
    '#title' => 'Hostname',
    '#default_value' => $host['hostname'],
    '#weight' => $weight++,
    '#description' => 'Hostname to be displayed in customer view (e.g. "eta.klick-tipp.com").',
  );

  $form['failover'] = array(
    '#type' => 'checkbox',
    '#title' => 'Failover',
    '#default_value' => $host['failover'],
    '#weight' => $weight++,
  );

  $form['whitelabel'] = array(
    '#type' => 'checkbox',
    '#title' => 'Whitelabel',
    '#default_value' => $host['whitelabel'],
    '#weight' => $weight++,
  );

  $form['Submit'] = array(
    '#type' => 'submit',
    '#value' => KLICKTIPP_SENDERSCORE_SAVE_BUTTON_TEXT,
    '#weight' => $weight++,
  );

  $form['Cancel'] = array(
    '#type' => 'submit',
    '#value' => KLICKTIPP_SENDERSCORE_CANCEL_BUTTON_TEXT,
    '#weight' => $weight++,
  );

  return $form;
}

function klicktipp_senderscore_settings_edit_form_validate($form, &$form_state) {
  if ($form_state['values']['op'] == KLICKTIPP_SENDERSCORE_SAVE_BUTTON_TEXT) {

    if (!empty($form_state['values']['IP'])) {
      $IP = inet_pton($form_state['values']['IP']);
      if (empty($IP)) {
        form_set_error('IP', 'Invalid IP address');
      }
    }
  }
}

function klicktipp_senderscore_settings_edit_form_submit($form, &$form_state) {

  if ($form_state['values']['op'] == KLICKTIPP_SENDERSCORE_SAVE_BUTTON_TEXT) {

    Libraries::include('senderscore.inc');
    $message = klicktipp_senderscore_set_host($form_state['values']['hostname'], $form_state['values']['IP'], empty($form_state['values']['whitelabel']) ? 0 : 1, empty($form_state['values']['failover']) ? 0 : 1);

    if ($message ){
      drupal_set_message($message);
    }
  }

  klicktipp_set_redirect($form_state, 'admin/config/klicktipp/senderscore');

}

function klicktipp_customer_menu_form($form, $form_state, $CustomerUserID = 0) {

  $form = array();

  $CustomerUserID = (empty($CustomerUserID)) ? $_COOKIE[DatabaseTableCRUD::COOKIENAME_CUSTOMER] : $CustomerUserID;

  if (empty($CustomerUserID)) {

    $form['CustomerUserID'] = array(
      '#type' => 'textfield',
      '#title' => "[encrypted] UserID, Username or Email address",
      '#default_value' => '',
      '#attributes' => array(
        'autofocus' => 'autofocus'
      ),
    );

    $form['Submit'] = array(
      '#type' => 'submit',
      '#value' => 'Continue',
    );

  }
  else {

    $subAccounts = Subaccount::GetSubAccounts($CustomerUserID);

    if (!empty($subAccounts)) {

      $mainAccount = array_shift($subAccounts);
      $mainAccountID = $mainAccount['SubAccountID'];
      $mainAccount = user_load($mainAccountID);

      if ($mainAccount->uid) {

        $form['subaccount'] = [
          '#type' => 'fieldset',
          '#title' => "This user is a subaccount for '{$mainAccount->name}' ({$mainAccountID})!",
          '#description' => "Do you want to show the links of the main account '{$mainAccount->name}'? "
        ];

        $form['subaccount']['CustomerUserID'] = [
          '#type' => 'value',
          '#value' => $mainAccountID,
        ];

        $form['subaccount']['Submit'] = [
          '#type' => 'submit',
          '#value' => 'Continue',
        ];

      }

    }

    //get the klicktipp main menu with "me" replaced by the customer's ID
    $menu = klicktipp_menu_main($CustomerUserID);
    $account = user_load($CustomerUserID);

    if (!$account->uid) {
      // value in the cookie no longer valid, reset cookie and reload page
      setcookie(DatabaseTableCRUD::COOKIENAME_CUSTOMER, NULL, -1, "/", "." . KLICKTIPP_DOMAIN);
      drupal_goto('/admin/user/customermenu');
      return $form;
    }

    $info = UserGroups::GetSubscriptionInfo($mainAccount ?? $account);

    $frontpage = l('Home', "user/$CustomerUserID", array('attributes' => array('target' => '_blank')));

    $email = Subscribers::DepunycodeEmailAddress($account->mail);
    $title = "Customer menu for {$account->name}" .
      "(UserID: {$account->uid}, Email: $email, Product: " .t(/*ignore*/$info['category']) . ", " .
      "ProductID: {$info['productID']}, Tier: {$info['tier']}, Term: {$info['term']}, " .
      "GroupID: {$account->RelUserGroupID}, AmemberID: {$account->amemberid})";
    klicktipp_set_title($title);

    $form['CustomerUserID'] = array(
      '#type' => 'value',
      '#value' => $CustomerUserID,
    );

    $form['ResetTop'] = array(
      '#type' => 'submit',
      '#value' => KLICKTIPP_RESET_COCKPIT_COOKIE,
    );

    // customer notes
    $notes = VarCustomerNotes::GetNotes($CustomerUserID);
    $form['CustomerNotes'] = array(
      '#type' => 'textarea',
      '#title' => 'Notes',
      '#default_value' => $notes,
    );
    $form['SaveCustomerNotes'] = array(
      '#type' => 'submit',
      '#value' => KLICKTIPP_SAVE_NOTES,
    );

    $form['CustomerMenu'] = array(
      '#type' => 'markup',
      '#value' => "<p>$frontpage</p><p>$menu</p>",
    );

    //create links to amember admin
    klicktipp_include('contact.inc', '/forms');
    $AmemberAdminURL = variable_get(KLICKTIPP_CONTACT_AMEMBER_ADMIN_LINK, 'https://www.klick-tipp.com/crm/admin');
    $AmemberEditLink = l('Amember Edit', "$AmemberAdminURL/users.php?action=edit&member_id={$account->amemberid}", array(
      'attributes' => array('target' => '_blank'),
    ));
    $AmemberPaymentsLink = l('Amember Payments', "$AmemberAdminURL/users.php?action=payments&member_id={$account->amemberid}", array(
      'attributes' => array('target' => '_blank'),
    ));

    $form['CustomerAmember'] = array(
      '#type' => 'markup',
      '#value' => "<h3>Amember-Links</h3><p>$AmemberEditLink</p><p>$AmemberPaymentsLink</p>",
    );

    // create test mail link
    $TestmailLink = l("Send Testmail", "admin/{$account->uid}/testmail", array(
      'attributes' => array('target' => '_blank'),
    ));
    $form['CustomerTestmail'] = array(
      '#type' => 'markup',
      '#value' => "<h3>Send Testmail</h3><p>$TestmailLink</p>",
    );

    // create developer campaign export link
    $DeveloperExportLink = l("Export Campaigns", "admin/user/customermenu/campaignexport/$CustomerUserID", array(
      'attributes' => array('target' => '_blank'),
    ));
    $form['CampaignExport'] = array(
      '#type' => 'markup',
      '#value' => "<h3>Campaign Developer Export</h3><p>$DeveloperExportLink</p>",
    );

    $form['Cancel'] = array(
      '#type' => 'klicktipp_back_button',
      '#title' => 'Change customer',
      '#value' => 'admin/user/customermenu',
    );

    $form['MillionVerifierUsage'] = [
      '#type' => 'fieldset',
      '#title' => 'MillionVerifier Api Usage',
      '#collapsible' => TRUE,
      '#collapsed' => TRUE
    ];

    $mvAccountUsage = new VarMillionVerifierUsage(['RelOwnerUserID' => $CustomerUserID]);
    $form['MillionVerifierUsage']['limit'] = [
      '#type' => 'item',
      '#theme' => 'klicktipp_item',
      '#title' => 'Limit for this account',
      '#value' => $mvAccountUsage->GetAccountLimit(),
    ];

    $form['MillionVerifierUsage']['mvAccountUsage'] = [
      '#type' => 'textfield',
      '#title' => 'Usage',
      '#default_value' => $mvAccountUsage->GetUsage(),
      '#description' => 'Adjust to any value if needed.'
    ];

    $form['MillionVerifierUsage']['updateUsage'] = [
      '#type' => 'submit',
      '#value' => 'Update usage',
    ];

    if ($form_state['storage']['showSegments']) {

      $userSegments = VarSegment::getUserSegments($account, variable_get('klicktipp_marketing_account_id', 0), FALSE);
      $form['segments'] = array(
        '#type' => 'fieldset',
        '#title' => 'Segments',
      );

      foreach($userSegments as $selector => $value) {
        [$segGroup, $segSelector] = explode('::', $selector);
        $form['segments'][$selector] = [
          '#type' => 'markup',
          '#markup' => '<p>' . l(
            $selector,
            "/admin/config/klicktipp/segments/$segGroup/$segSelector"
            ) . "&nbsp;: " . ($value['value'] ? 'YES' : 'NO') . '</p>'
        ];
      }

      $form['featureFlags'] = array(
        '#type' => 'fieldset',
        '#title' => 'Feature Flags',
      );

      $featureToggle = new \App\Klicktipp\FeatureToggle($account);
      $featureFlags = $featureToggle->allFeatureFlags();

      foreach($featureFlags as $name => $value) {
        $form['featureFlags'][$name] = [
          '#type' => 'markup',
          '#markup' => "<p>$name:&nbsp;" . ($value ? 'YES' : 'NO') . '</p>'
        ];
      }
    }

    $form['showSegments'] = array(
      '#type' => 'submit',
      '#value' => 'Show segments',
    );


  }

  $cookie = $_COOKIE[DatabaseTableCRUD::COOKIENAME_CUSTOMER];
  if ($cookie) {
    $form['Reset'] = array(
      '#type' => 'submit',
      '#value' => KLICKTIPP_RESET_COCKPIT_COOKIE,
    );
    $form['CookieInfo'] = array(
      '#type' => 'markup',
      '#value' => "Cockpit Cookie for customer #$cookie set.",
    );
  }

  return $form;

}

function klicktipp_customer_menu_form_submit($form, &$form_state) {

  if ($form_state['values']['op'] == KLICKTIPP_RESET_COCKPIT_COOKIE) {
    setcookie(DatabaseTableCRUD::COOKIENAME_CUSTOMER, NULL, -1, "/", "." . KLICKTIPP_DOMAIN);
    drupal_set_message('Cockpit Cookie cleared.');
    // reload the page to remove a possible user id from the url e.g /admin/user/customermenu/<userid>
    klicktipp_set_redirect($form_state, '/admin/user/customermenu');
  }
  elseif ($form_state['values']['op'] == 'Update usage') {
    $CustomerUserID = $form_state['values']['CustomerUserID'];
    $mvAccountUsage = new VarMillionVerifierUsage(
      ['RelOwnerUserID' => $CustomerUserID]
    );
    $newUsage = $form_state['values']['mvAccountUsage'];
    if (is_numeric(
        $newUsage
      ) && $newUsage >= 0 && $newUsage != $mvAccountUsage->GetUsage()) {
      $mvAccountUsage->SetUsage($newUsage);
    }
    $form_state['rebuild'] = TRUE;
  }
  elseif ($form_state['values']['op'] == KLICKTIPP_SAVE_NOTES) {
    $notes = trim($form_state['values']['CustomerNotes']);
    $customerUserID = $form_state['values']['CustomerUserID'];
    VarCustomerNotes::SetNotes($customerUserID, $notes);
    drupal_set_message('Notes saved.');
  }
  else {

    $CustomerUserID = $form_state['values']['CustomerUserID'];

    if (!empty($CustomerUserID)) {

      if (is_numeric($CustomerUserID)) {
        $Customer = user_load($CustomerUserID);
      }
      elseif (strpos($CustomerUserID, Beamer::ENCRYPTED_USERID_PREFIX) === 0) {
        $beamer = new Beamer();
        $uid = $beamer->decryptUserId($CustomerUserID);
        if ($uid) {
          $Customer = user_load($uid);
        }
      }
      else {
        $CustomerUserIdAsEmailAddress = Subscribers::PunycodeEmailAddress($CustomerUserID);
        if (valid_email_address($CustomerUserIdAsEmailAddress)) {
          // be aware that utf8_unicode_ci of the database field mail causes not
          // only the equality of lower and upper case chars, but also e.g. "A" and "Ä";
          // so currently it is not possible to add the following two users:
          //    <EMAIL> and hä*************
          // Note: other collations use other equality definitions
          $Customer = user_load_by_mail($CustomerUserIdAsEmailAddress);
        }
        else {
          $Customer = user_load_by_name($CustomerUserID);
        }

      }

    }

    if (!empty($Customer)) {
      $form_state['rebuild'] = TRUE;
      $form_state['storage']['CustomerUserID'] = $Customer->uid;
      $form_state['storage']['account'] = $Customer;

      if ($form_state['values']['op'] == 'Show segments' || $form_state['storage']['showSegments']) {
        $form_state['storage']['showSegments'] = TRUE;
      }

      // set customer cookie for cockpit
      $cookie = $Customer->uid;
      setcookie(DatabaseTableCRUD::COOKIENAME_CUSTOMER, $cookie, strtotime('+1 days'), "/", "." . KLICKTIPP_DOMAIN);
      $_COOKIE[DatabaseTableCRUD::COOKIENAME_CUSTOMER] = $cookie;
    }
    else {
      $form_state['rebuild'] = FALSE;
      drupal_set_message('User not found.', 'error');
    }

  }
}

function klicktipp_account_sync_user() {

  // sync all email addresses from drupal to amember
  $output = '<div>List of synced users:<br/></div>';
  /*	not used at the moment
   $result = db_query('SELECT name, mail FROM {users} WHERE login > 0');
   while ($account = kt_fetch_array($result)) {
   if ($member = klicktipp_account_get_member($account['name'])) {
   if (!empty($member['login']) && $member['email'] != $account['mail']) {
   $output .= '<div>'.	$member['login'].' '.$member['email'] .' -&gt; ' . $account['mail'].'</div>';

   $fields_to_update = $member;
   unset($fields_to_update['data']);
   $fields_to_update['email'] = $account['mail'];
   amember_rpc_update_user($member['member_id'], $fields_to_update);

   };
   }
   };
   */
  $output .= '<div><br/>finished</div>';
  return $output;
}

/*
 * SMS/Nexmo settings
 */

function klicktipp_settings_sms($form, &$form_state) {

  Libraries::include('twofactorauth.inc', '/forms');

  $form['klicktipp_twofactorauth'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp SMS Marketing and Two-Factor-Authorization',
  );

  $form['klicktipp_twofactorauth'][KLICKTIPP_TWOFACTORAUTH_SETTINGS_NEXMO_KEY] = array(
    '#type' => 'textfield',
    '#title' => 'Nexmo API key',
    '#default_value' => variable_get(KLICKTIPP_TWOFACTORAUTH_SETTINGS_NEXMO_KEY, ''),
    '#description' => 'Enter the API key for the Nexmo account to activate Two-Factor-Authorization.',
  );

  $form['klicktipp_twofactorauth'][KLICKTIPP_TWOFACTORAUTH_SETTINGS_NEXMO_SECRET] = array(
    '#type' => 'textfield',
    '#title' => 'Nexmo API secret',
    '#default_value' => variable_get(KLICKTIPP_TWOFACTORAUTH_SETTINGS_NEXMO_SECRET, ''),
    '#description' => 'Enter the API secret for the Nexmo account to activate Two-Factor-Authorization.',
  );
  $form['klicktipp_twofactorauth']['klicktipp_twofactorauth_settings_nexmo_api'] = array(
    '#type' => 'textfield',
    '#title' => 'Nexmo API endpoint',
    '#default_value' => variable_get('klicktipp_twofactorauth_settings_nexmo_api', 'https://rest.nexmo.com/sms/json'),
    '#description' => 'Enter the Nexmo API enpoint.',
  );

  $form['klicktipp_twofactorauth'][KLICKTIPP_TWOFACTORAUTH_SETTINGS_SMS_FROM] = array(
    '#type' => 'textfield',
    '#title' => 'Two-Factor-Authorization SMS sender name',
    '#default_value' => variable_get(KLICKTIPP_TWOFACTORAUTH_SETTINGS_SMS_FROM, ''),
    '#description' => 'Enter the sender name of the SMS that is displayed instead of the sender phone number.',
  );

  //TODO same as klicktipp_marketing_account_id <- use this
  $form['klicktipp_twofactorauth'][KLICKTIPP_TWOFACTORAUTH_MARKETING_ACCOUNT_ID] = array(
    '#type' => 'textfield',
    '#title' => 'KlickTipp marketing account.',
    '#default_value' => variable_get(KLICKTIPP_TWOFACTORAUTH_MARKETING_ACCOUNT_ID, ''),
    '#description' => 'Enter the UserID of the KlickTipp account to use for marketing.',
  );

  $form['klicktipp_twofactorauth'][KLICKTIPP_TWOFACTORAUTH_MARKETING_CUSTOMFIELD_ID] = array(
    '#type' => 'textfield',
    '#title' => 'KlickTipp marketing mobile number CustomField.',
    '#default_value' => variable_get(KLICKTIPP_TWOFACTORAUTH_MARKETING_CUSTOMFIELD_ID, ''),
    '#description' => 'Enter the ID of the CustomField in which to store authenticated phone numbers.',
  );

  $form['klicktipp_twofactorauth'][KLICKTIPP_TWOFACTORAUTH_DEFAULT_PHONENUMBER_PREFIX] = array(
    '#type' => 'textfield',
    '#title' => 'Default phone number prefix',
    '#default_value' => variable_get(KLICKTIPP_TWOFACTORAUTH_DEFAULT_PHONENUMBER_PREFIX, '0049'),
    '#description' => 'Enter the default prefix for phone numbers.',
  );

  $PhoneNumberPrefixes = variable_get('klicktipp_settings_sms_phonenumber_prefixes', array());
  $PhoneNumberPrefixes_string = "";
  if (!empty($PhoneNumberPrefixes)) {
    foreach ($PhoneNumberPrefixes as $country_code => $country_prefixes) {
      $PhoneNumberPrefixes_string .= "$country_code:" . implode(',', $country_prefixes) . "\n";
    }
  }
  $form['klicktipp_twofactorauth']['klicktipp_settings_sms_phonenumber_prefixes'] = array(
    '#type' => 'textarea',
    '#title' => 'Default mobile phone number prefixes',
    '#rows' => 10,
    '#default_value' => trim($PhoneNumberPrefixes_string),
    '#description' => 'Enter mobile phone number prefixes without leading 0 by country to help format sms phone numbers.<br />' .
      'Format: [CountryCode]:[Prefix],[Prefix][Linebreak] ' .
      'Example:<br />0049:152,163,177<br />0043:77,70',
  );

  // --- Short links

  $form['klicktipp_short_link'] = array(
    '#type' => 'fieldset',
    '#title' => 'Short links',
  );
  $form['klicktipp_short_link']['klicktipp_short_link_domain'] = array(
    '#type' => 'textfield',
    '#title' => 'Short link domain (for SMS)',
    '#default_value' => variable_get('klicktipp_short_link_domain', APP_URL . 's'),
    '#description' => 'The CNAME domain for short links (e.g. http://simse.me). Should link to ' . APP_URL . 's. Note: NO / at end.'
  );

  // --- SMS sender host

  $form['klicktipp_sms_sender'] = array(
    '#type' => 'fieldset',
    '#title' => 'SMS sender',
  );

  $form['klicktipp_sms_sender']['sms_queue_on'] = array(
    '#type' => 'checkbox',
    '#title' => 'Activate SMS dispatch via queue',
    '#default_value' => variable_get('sms_queue_on', 0),
    '#return_value' => 1,
  );

  $form['klicktipp_sms_sender']['klicktipp_sms_sender_host'] = array(
    '#type' => 'textfield',
    '#title' => 'Domain of SMS sender host',
    '#default_value' => variable_get('klicktipp_sms_sender_host', 'nexmosms.' . KLICKTIPP_DOMAIN),
    '#description' => 'E.g. ' . 'nexmosms.' . KLICKTIPP_DOMAIN,
  );

  // --- SMS forwarding email template START

  $form['klicktipp_sms_forwarding'] = array(
    '#type' => 'fieldset',
    '#title' => 'SMS forwarding email template',
    '#description' => 'This email is send to the customer, when he receives an SMS and added a forwarding email address.',
  );
  $form['klicktipp_sms_forwarding']['klicktipp_sms_forwarding_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_sms_forwarding_subject', 'Klick-Tipp SMS notification'),
    '#size' => 60,
    '#maxlength' => 256,
    '#required' => TRUE,
  );
  $form['klicktipp_sms_forwarding']['klicktipp_sms_forwarding_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#description' => "The placeholders will be replaced in listbuildings_sms.inc.",
    '#default_value' => variable_get('klicktipp_sms_forwarding_body',
      'Hi @firstname,

you received a new SMS to your Nexmo Inbound number @inbound.

Email address: @email
Phone number: @phonenumbe
Text: @text

Contact link: !link

Kind regards
Your team at !site'),
    '#cols' => 80,
    '#rows' => 10,
    '#required' => TRUE,
  );

  // --- SMS forwarding email template END

  $form['#submit'][] = 'klicktipp_settings_sms_form_submit';

  return system_settings_form($form);

}

function klicktipp_settings_sms_form_submit($form, &$form_state) {

  $new_prefixes = trim($form_state['values']['klicktipp_settings_sms_phonenumber_prefixes']);

  $lines = explode("\n", strtolower($new_prefixes));
  $prefixes = array();
  foreach ($lines as $line) {
    if (!empty($line)) {
      $bycountrycode = explode(':', trim($line));
      if (!empty($bycountrycode[0]) && !empty($bycountrycode[1])) {
        $prefixes[$bycountrycode[0]] = array_map('trim', explode(',', $bycountrycode[1]));
      }
    }
  }

  if (!empty($prefixes)) {
    $msg = 'The following sms number prefixes have been saved:<br />';
    foreach ($prefixes as $cc => $p) {
      foreach ($p as $pref) {
        $msg .= "$cc$pref,";
      }
      $msg .= "<br />";
    }

    drupal_set_message($msg);
  }

  $form_state['values']['klicktipp_settings_sms_phonenumber_prefixes'] = $prefixes;

}

function klicktipp_smsnumbers_overview_form($form, &$form_state) {

  //retrieve all SMS numbers
  $result = db_query("SELECT * FROM {smsnumbers} WHERE 1 ORDER BY RelOwnerUserID ASC");
  while ($number = kt_fetch_array($result)) {
    //NOTE: unserialize Data if needed
    $Numbers[$number['NumberID']] = $number;
  }

  $weight = 1;

  $form = array();

  if (empty($Numbers)) {

    $form['NoNumbers'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#weight' => $weight++,
      '#value' => 'There are no SMS numbers yet.',
    );

  }
  else {

    $header = array(
      array(
        'data' => 'ID',
        'class' => array('table-col-fit table-col-right hidden-xs'),
      ),
      array('data' => 'Label'),
      array('data' => 'Number', 'class' => array('table-col-fit')),
      array('data' => 'UserID', 'class' => array('table-col-fit')),
      array(
        'data' => 'Operations',
        'class' => array('table-col-fit'),
      ),
    );

    $rows = array();

    foreach ($Numbers as $NumberID => $Number) {

      $EditButton = array(
        '#title' => 'Edit',
        '#value' => "admin/config/klicktipp/smsnumbers/$NumberID/edit",
      );

      $url = "/admin/config/klicktipp/smsnumbers/$NumberID/edit";
      $LabelNumber = array(
      'data' => '<a href="' . $url . '" class="cut-text display-email" title="' . $Number['Label'] . '">' .
        $Number['Label'] . '</a>',
      );

      $row = array(
        array(
          'data' => $NumberID,
          'class' => array('table-col-fit table-col-right hidden-xs'),
        ),
        $LabelNumber,
        array(
          'data' => (empty($Number['Number'])) ? 0 : $Number['Number'],
          'class' => array('table-col-fit'),
        ),
        array(
          'data' => l($Number['RelOwnerUserID'], 'admin/user/customermenu/' . $Number['RelOwnerUserID'], array('attributes' => array('target' => '_blank'))),
          'class' => array('table-col-fit hidden-xs hidden-sm'),
        ),
        array(
          'data' => theme('klicktipp_edit_table_button', array('element' => $EditButton)),
          'class' => array('table-col-fit'),
        ),
      );

      $rows[] = $row;

    }

    $form['Overview'] = array(
      '#type' => 'markup',
      '#markup' => theme('table', array(
        'header' => $header,
        'rows' => $rows,
        'attributes' => array('class' => array('table-buttons'))
      )),
      '#weight' => $weight++,
    );

  }

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => 10000,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['Add'] = array(
    '#theme' => 'klicktipp_edit_button',
    '#title' => 'Add SMS number',
    '#value' => 'admin/config/klicktipp/smsnumbers/add',
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_smsnumbers_create_form($form, &$form_state) {

  $weight = 1;

  $form = array();

  $form['Label'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#size' => 60,
    '#maxlength' => 60,
    '#title' => 'Label',
    '#default_value' => '',
  );

  $form['Number'] = array(
    '#type' => 'textfield',
    '#weight' => $weight++,
    '#required' => TRUE,
    '#size' => 60,
    '#maxlength' => 40,
    '#title' => 'SMS Number',
    '#default_value' => empty($form_state['values']['Number']) ? '' : $form_state['values']['Number'],
  );

  $form['Customer'] = array(
    '#type' => 'textfield',
    '#weight' => $weight++,
    '#size' => 60,
    '#title' => 'Customer',
    '#default_value' => '',
    '#description' => "Enter UserID, Username, Email address or leave empty for a global SMS number",
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => 'Create SMS Number',
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_cancel_button',
    '#title' => KLICKTIPP_BUTTON_TEXT_CANCEL,
    '#value' => "admin/config/klicktipp/smsnumbers",
    '#weight' => $weight++,
  );


  return $form;

}

function klicktipp_smsnumbers_create_form_validate($form, &$form_state) {

  $CustomerUserID = $form_state['values']['Customer'];
  $Number = $form_state['values']['Number'];
  $Label = $form_state['values']['Label'];

  if (!empty($CustomerUserID)) {

    if (!empty($CustomerUserID)) { //global number

      if (is_numeric($CustomerUserID)) {
        $Customer = user_load($CustomerUserID);
      }
      else {
        $CustomerUserIdAsEmailAddress = Subscribers::PunycodeEmailAddress($CustomerUserID);
        if (valid_email_address($CustomerUserIdAsEmailAddress)) {
          // be aware that utf8_unicode_ci of the database field mail causes not
          // only the equality of lower and upper case chars, but also e.g. "A" and "Ä";
          // so currently it is not possible to add the following two users:
          //    <EMAIL> and hä*************
          // Note: other collations use other equality definitions
          $Customer = user_load_by_mail($CustomerUserIdAsEmailAddress);
        }
        else {
          $Customer = user_load_by_name($CustomerUserID);
        }
      }

      if (empty($Customer)) {
        form_set_error('Customer', "User $CustomerUserID not found.");
      }
      else {
        $form_state['values']['Account'] = $Customer;
      }

    }

  }

  //format/sanitize user input
  $NumberFormatted = Subscribers::FormatSMSNumber($Number);

  if (empty($NumberFormatted)) {
    form_set_error('Number', "Invalid number $Number.");
  }
  else {

    if ($NumberFormatted != $Number) {
      drupal_set_message("The format of the number has been adjusted. ($Number -&gt; $NumberFormatted)", 'warning');

      $form_state['values']['Number'] = $NumberFormatted;

    }

    //check if the number already exists
    $ExistingNumber = db_query("SELECT NumberID FROM {smsnumbers} WHERE Number = :Number", array(':Number' => $NumberFormatted))->fetchField();

    if (!empty($ExistingNumber)) {
      form_set_error('Number', "The number $NumberFormatted already exists.");
    }

    //check if the label already exists
    $ExistingLabel = db_query("SELECT NumberID FROM {smsnumbers} WHERE Label = :Label", array(':Label' => $Label))->fetchField();

    if (!empty($ExistingLabel)) {
      form_set_error('Label', "A number with the label $Label already exists.");
    }

  }

}

function klicktipp_smsnumbers_create_form_submit($form, &$form_state) {

  $account = $form_state['values']['Account'];
  $RelOwnerUserID = (empty($account)) ? 0 : $account->uid;

  $ArrayFieldAndValues = array(
    'RelOwnerUserID' => (empty($account)) ? 0 : $account->uid,
    'Number' => $form_state['values']['Number'],
    'Label' => $form_state['values']['Label'],
    'Data' => '',
  );

  $NumberID = kt_insert_row($ArrayFieldAndValues, '{smsnumbers}');

  if (!empty($NumberID)) {
    drupal_set_message("SMS Number {$form_state['values']['Number']} for User {$RelOwnerUserID} successfully created.");

    klicktipp_set_redirect($form_state, "admin/config/klicktipp/smsnumbers");

  }
  else {
    $error = array(
      '!uid' => $RelOwnerUserID,
      '!number' => $form_state['values']['Number'],
      '!label' => $form_state['values']['Label'],
    );

    //notify user
    Errors::unexpected("SMSNumbers: CreateSMSNumber for User !uid with number !number (!label)", $error, TRUE);

  }

}

function klicktipp_smsnumbers_edit_form($form, &$form_state, $NumberID) {

  //retrieve number
  //Note: unserialize Data if needed
  $result = db_query("SELECT * FROM {smsnumbers} WHERE NumberID = :NumberID", array(':NumberID' => $NumberID));
  $ArrayNumber = kt_fetch_array($result);

  $weight = 1;

  $form = array();

  $form['ArrayNumber'] = array(
    '#type' => 'value',
    '#value' => $ArrayNumber,
  );

  $form['Label'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#size' => 60,
    '#maxlength' => 60,
    '#title' => 'Label',
    '#default_value' => $ArrayNumber['Label'],
  );

  $form['Number'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#size' => 60,
    '#maxlength' => 40,
    '#title' => 'SMS Number',
    '#default_value' => $ArrayNumber['Number'],
  );

  $form['Customer'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#title' => 'Customer',
    '#weight' => $weight++,
    '#value' => $ArrayNumber['RelOwnerUserID'],
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => 'Save SMS Number',
    '#weight' => $weight++,
  );

  $form['buttons']['ModalDeleteConfirmTrigger'] = array(
    '#theme' => 'klicktipp_delete_modal_button',
    '#title' => 'Delete SMS Number',
    '#value' => 'klicktipp_smsnumbers_delete_confirm_form',
    '#weight' => $weight++,
    '#external' => FALSE,
    '#modal_confirm' => drupal_get_form('klicktipp_smsnumbers_delete_confirm_form', $NumberID),
  );

  $form['buttons']['cancel'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_cancel_button',
    '#title' => KLICKTIPP_BUTTON_TEXT_CANCEL,
    '#value' => "admin/config/klicktipp/smsnumbers",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_smsnumbers_edit_form_validate($form, &$form_state) {

  $ArrayNumber = $form_state['values']['ArrayNumber'];
  $Number = $form_state['values']['Number'];
  $Label = $form_state['values']['Label'];

  //format/sanitize user input
  $NumberFormatted = Subscribers::FormatSMSNumber($Number);

  if (!is_numeric($NumberFormatted)) {
    form_set_error('Number', "Invalid number $Number.");
  }
  else {

    if ($NumberFormatted != $Number) {
      drupal_set_message("The format of the number has been adjusted. ($Number -&gt; $NumberFormatted)", 'warning');

      $form_state['values']['Number'] = $NumberFormatted;

    }

    //check if the number already exists
    $ExistingNumber = db_query("SELECT NumberID FROM {smsnumbers} WHERE Number = :Number", array(':Number' => $NumberFormatted))->fetchField();

    if (!empty($ExistingNumber) && $ExistingNumber['NumberID'] != $ArrayNumber['NumberID']) {
      form_set_error('Number', "The number $NumberFormatted already exists.");
    }

    //check if the label already exists
    $ExistingLabel = db_query("SELECT NumberID FROM {smsnumbers} WHERE Label = :Label", array(':Label' => $Label))->fetchField();

    if (!empty($ExistingLabel) && $ExistingLabel['NumberID'] != $ArrayNumber['NumberID']) {
      form_set_error('Label', "A number with the label $Label already exists.");
    }

  }

}

function klicktipp_smsnumbers_edit_form_submit($form, &$form_state) {

  $ArrayNumber = $form_state['values']['ArrayNumber'];

  $ArrayFieldAndValues = array(
    'Number' => $form_state['values']['Number'],
    'Label' => $form_state['values']['Label'],
  );
  $ArrayCriterias = array(
    'NumberID' => $ArrayNumber['NumberID'],
    'RelOwnerUserID' => $ArrayNumber['RelOwnerUserID'],
  );

  kt_update_rows($ArrayFieldAndValues, $ArrayCriterias, '{smsnumbers}');

  drupal_set_message('SMS Number successfully updated.');

  klicktipp_set_redirect($form_state, "admin/config/klicktipp/smsnumbers");

}

function klicktipp_smsnumbers_delete_confirm_form($form, $form_state, $NumberID) {

  //retrieve number
  //Note: unserialize Data if needed
  $result = db_query("SELECT * FROM {smsnumbers} WHERE NumberID = :NumberID", array(':NumberID' => $NumberID));
  $ArrayNumber = kt_fetch_array($result);

  $EntityName = $ArrayNumber['Label'];
  $Title = 'Delete SMS Number';

  $form = _klicktipp_confirm_form_modal('klicktipp_smsnumbers_delete_confirm_form', $EntityName, $Title);

  $form['Entity'] = array(
    '#type' => 'value',
    '#value' => $ArrayNumber,
  );

  return $form;

}

function klicktipp_smsnumbers_delete_confirm_form_submit($form, &$form_state) {

  $ArrayNumber = $form_state['values']['Entity'];
  $NumberID = $ArrayNumber['NumberID'];

  //delete number from database
  if (kt_delete_rows(array('NumberID' => $NumberID), '{smsnumbers}')) {

    drupal_set_message("SMS Number successfully deleted.");
    klicktipp_set_redirect($form_state, "admin/config/klicktipp/smsnumbers");

  }
  else {
    //unlikely database error
    $error = array(
      '!number' => $ArrayNumber['Number'],
      '!username' => $ArrayNumber['Data']['Username'],
      '!uid' => $ArrayNumber['RelOwnerUserID'],
      '!NumberID' => $NumberID,
      '!ArrayNumber' => $ArrayNumber,
    );

    Errors::unexpected("SMSNumber: DeleteSMSNumber !number for User !username (ID: !uid) and NumberID !NumberID", $error);

  }

}

/*
 * KlickTipp Marketing Tools settings
 */

function klicktipp_settings_marketing_tools($form, &$form_state) {

  $form = array();

  $weight = 1;

  $form['tool_countdown'] = array(
    '#type' => 'fieldset',
    '#title' => 'Email Countdown',
    '#weight' => $weight++,
  );

  $form['tool_countdown']['marketing_tools_emailcountdown_s3url'] = array(
    '#type' => 'textfield',
    '#title' => 'S3 source URL',
    '#default_value' => Settings::get('marketing_tools_emailcountdown_s3url'),
    '#description' => 'Enter the S3 URL (https://bucket.endpoint/...) containing all countdowns. Note: For VMs use the klicktipp bucket (read only).',
    '#weight' => $weight++,
  );

  $form['tool_countdown']['marketing_tools_emailcountdown_default_expired_image'] = array(
    '#type' => 'textfield',
    '#title' => 'Default expired image URL',
    '#default_value' => Settings::get('marketing_tools_emailcountdown_default_expired_image'),
    '#description' => 'Enter the URL to the default expired image (full URL).',
    '#weight' => $weight++,
  );

  $form['tool_countdown']['marketing_tools_emailcountdown_ckeditor_preview_image'] = array(
    '#type' => 'textfield',
    '#title' => 'CKEditor preview image',
    '#default_value' => Settings::get('marketing_tools_emailcountdown_ckeditor_preview_image'),
    '#description' => 'Enter the URL to the preview image displayed in the CKEditor (full URL).',
    '#weight' => $weight++,
  );

  $form['tool_countdown']['marketing_tools_emailcountdown_calltoaction_image'] = array(
    '#type' => 'textfield',
    '#title' => 'Call to action',
    '#default_value' => Settings::get('marketing_tools_emailcountdown_calltoaction_image'),
    '#description' => 'Enter the URL to the call to action image displayed when the countdown does not exist or a subscriber has no termination datetime.',
    '#weight' => $weight++,
  );

  $templates = Settings::get('marketing_tools_emailcountdown_templates');
  $templates = (empty($templates)) ? '' : implode("\n", $templates);

  $form['tool_countdown']['marketing_tools_emailcountdown_templates'] = array(
    '#type' => 'textarea',
    '#title' => 'Email Countdown templates',
    '#default_value' => $templates,
    '#rows' => 10,
    '#description' => 'Enter 1 template name per line. It must match the file and folder name on S3.<br >' .
      'Example: S3URI/TEMPLATENAME/1/TEMPLATENAME_3600.gif<br />' .
      'Translate the template name for display in the template dropdown in the Countdown edit form.',
    '#weight' => $weight++,
  );


  $form['#submit'][] = 'klicktipp_settings_marketing_tools_submit';

  return system_settings_form($form);

}

function klicktipp_settings_marketing_tools_submit($form, &$form_state) {

  $form_state['values']['marketing_tools_emailcountdown_s3url'] = trim($form_state['values']['marketing_tools_emailcountdown_s3url'], " /");
  $form_state['values']['marketing_tools_emailcountdown_templates'] = array_map('trim', explode("\n", $form_state['values']['marketing_tools_emailcountdown_templates']));

}

/*
 * KlickTipp Listbuildings settings
 */

function klicktipp_settings_listbuildings($form, &$form_state) {

  $form = array();

  $weight = 1;

  $form['general'] = array(
    '#type' => 'fieldset',
    '#title' => 'General',
    '#weight' => $weight++,
  );

  $form['general']['listbuilding_affiliate_link_keywords'] = array(
    '#type' => 'textarea',
    '#title' => 'Affiliate link keywords',
    '#rows' => 15,
    '#default_value' => implode("\n", Settings::get('listbuilding_affiliate_link_keywords')),
    '#description' => 'Enter search engine keywords for affiliate links (1 per line).<br />Keywords will be used as link description and title and will be randomly selected.',
    '#weight' => $weight++,
  );

  $form['general']['listbuilding_htmldsgvotext_default'] = array(
    '#type' => 'textarea',
    '#title' => 'DSGVO Text Default HTML',
    '#rows' => 20,
    '#cols' => 68,
    '#default_value' => variable_get('listbuilding_htmldsgvotext_default', ''),
    '#weight' => $weight++,
  );

  $form['PaymentIPNS'] = array(
    '#type' => 'fieldset',
    '#title' => 'Payment IPNs',
    '#weight' => $weight++,
  );

  $form['PaymentIPNS']['PayPal'] = array(
    '#type' => 'fieldset',
    '#title' => 'PayPal',
    '#weight' => $weight++,
  );

  $form['PaymentIPNS']['PayPal']['listbuilding_paymentipns_paypal_validation_url'] = array(
    '#type' => 'textfield',
    '#title' => 'IPN Validation Host',
    '#default_value' => Settings::get('listbuilding_paymentipns_paypal_validation_url'),
    '#description' => 'Enter the hostname a PayPal IPN is sent back to for validation.<br />Sandbox: www.sandbox.paypal.com',
    '#weight' => $weight++,
  );

  $form['PaymentIPNS']['Payments'] = array(
    '#type' => 'fieldset',
    '#title' => 'Payments',
    '#weight' => $weight++,
  );

  $form['PaymentIPNS']['Payments']['listbuilding_payments_base_currency'] = array(
    '#type' => 'textfield',
    '#title' => 'Payments base currency',
    '#default_value' => Settings::get('listbuilding_payments_base_currency'),
    '#description' => 'The base currency is used to calculate a unified lead value with mixed currencies.',
    '#size' => 3,
    '#maxlength' => 3,
    '#weight' => $weight++,
  );

  $form['PaymentIPNS']['Payments']['listbuilding_payments_elopage'] = array(
    '#type' => 'textfield',
    '#title' => 'elopage',
    '#default_value' => variable_get('listbuilding_payments_elopage', ''),
    '#description' => 'Enter the URL to the elopage integration manual. Leave empty to not display an icon in the listbuilding create dialog.',
    '#weight' => $weight++,
  );

  $form['Integrations'] = array(
    '#type' => 'fieldset',
    '#title' => 'Online Shops',
    '#weight' => $weight++,
  );

  $form['Integrations']['listbuilding_online_shops_magento'] = array(
    '#type' => 'textfield',
    '#title' => 'Magento',
    '#default_value' => variable_get('listbuilding_online_shops_magento', ''),
    '#description' => 'Enter the URL to the Magento integration manual. Leave empty to not display an icon in the listbuilding create dialog.',
    '#weight' => $weight++,
  );

  $form['Integrations']['listbuilding_online_shops_woocommerce'] = array(
    '#type' => 'textfield',
    '#title' => 'Woo Commerce',
    '#default_value' => variable_get('listbuilding_online_shops_woocommerce', ''),
    '#description' => 'Enter the URL to the Woo Commerce integration manual. Leave empty to not display an icon in the listbuilding create dialog.',
    '#weight' => $weight++,
  );

  $form['Integrations']['listbuilding_online_shops_shopware'] = array(
    '#type' => 'textfield',
    '#title' => 'Shopware',
    '#default_value' => variable_get('listbuilding_online_shops_shopware', ''),
    '#description' => 'Enter the URL to the Shopware integration manual. Leave empty to not display an icon in the listbuilding create dialog.',
    '#weight' => $weight++,
  );

  $form['Integrations']['listbuilding_online_shops_gambio'] = array(
    '#type' => 'textfield',
    '#title' => 'Gambio',
    '#default_value' => variable_get('listbuilding_online_shops_gambio', ''),
    '#description' => 'Enter the URL to the Gambio integration manual. Leave empty to not display an icon in the listbuilding create dialog.',
    '#weight' => $weight++,
  );

  $form['Integrations']['listbuilding_online_shops_hubspot'] = array(
    '#type' => 'textfield',
    '#title' => 'Hubspot',
    '#default_value' => variable_get('listbuilding_online_shops_hubspot', ''),
    '#description' => 'Enter the URL to the hubspot integration manual. Leave empty to not display an icon in the listbuilding create dialog.',
    '#weight' => $weight++,
  );

  $form['Integrations']['listbuilding_integration_etermin'] = array(
    '#type' => 'textfield',
    '#title' => 'eTermin',
    '#default_value' => variable_get('listbuilding_integration_etermin', ''),
    '#description' => 'Enter the URL to the etermin integration manual. Leave empty to not display an icon in the listbuilding create dialog.',
    '#weight' => $weight++,
  );

  $form['Integrations']['listbuilding_integration_terminpilot'] = array(
    '#type' => 'textfield',
    '#title' => 'Terminpilot',
    '#default_value' => variable_get('listbuilding_integration_terminpilot', ''),
    '#description' => 'Enter the URL to the Terminpilot integration manual. Leave empty to not display an icon in the listbuilding create dialog.',
    '#weight' => $weight++,
  );

  $form['BCRApp'] = array(
    '#type' => 'fieldset',
    '#title' => 'BCR App',
    '#weight' => $weight++,
  );

  $form['BCRApp']['klicktipp_sales_employee_roleid'] = array(
    '#type' => 'textfield',
    '#title' => 'Role Sales Employee',
    '#default_value' => variable_get('klicktipp_sales_employee_roleid', ''),
    '#description' => 'Enter the id of the sales employee role.',
    '#weight' => $weight++,
  );

  $form['BCRApp']['listbuilding_bcrapp_bcrkey_account_id'] = array(
    '#type' => 'textfield',
    '#title' => 'Pseudo account id for BCR key',
    '#default_value' => Settings::get('listbuilding_bcrapp_bcrkey_account_id'),
    '#description' => 'This user id is the base for the generated bcr key below.<br>This user id should not conflict with a user id of an existing account.<br>If you have to change it, set the variable "listbuilding_bcrapp_bcrkey_account_id" to the needed id.' ,
    '#attributes' => ["disabled" => "disabled"],
    '#weight' => $weight++,
  );

  $bcr_key = (empty(Settings::get('listbuilding_bcrapp_bcrkey_account_id'))) ? '' : Subaccount::CreateDeveloperKey(
    Settings::get('listbuilding_bcrapp_bcrkey_account_id'));
  $form['BCRApp']['listbuilding_bcrapp_bcrkey'] = array(
    '#type' => 'item',
    '#title' => 'BCR key',
    '#description' => 'If this key changes, the app must be changed and a new version of it has to be put into the app stores.<br>Do not alter unless you know what you are doing.',
    '#theme' => 'klicktipp_item',
    '#value' => $bcr_key,
    '#weight' => $weight++,
  );

  $form['BCRApp']['listbuilding_bcrapp_login_timeout'] = array(
    '#type' => 'textfield',
    '#title' => 'Timeout for BCR deep link timeout',
    '#default_value' => variable_get('listbuilding_bcrapp_login_timeout', 86400),
    '#description' => 'Enter a timeout value for the BCR deep link',
    '#weight' => $weight++,
  );

  $form['BCRApp']['listbuilding_bcrapp_deeplink_url'] = array(
    '#type' => 'textfield',
    '#title' => 'URL for BCR deep link',
    '#default_value' => variable_get('listbuilding_bcrapp_deeplink_url', APP_URL . 'bcr'),
    '#description' => 'Enter the URL for the BCR deep link',
    '#weight' => $weight++,
  );

  $form['BCRApp']['listbuilding_bcrapp_hide_subscribers'] = array(
    '#type' => 'checkbox',
    '#title' => 'Hide subscribers inside the bcr app after subscription',
    '#return_value' => 1,
    '#default_value' => variable_get('listbuilding_bcrapp_hide_subscribers', 0),
    '#weight' => $weight++,
  );

  $form['BCRApp']['listbuilding_bcrapp_exclude_global_fields'] = array(
    '#type' => 'textfield',
    '#title' => 'Exclude Global Fields',
    '#default_value' => implode(',', (array)variable_get('listbuilding_bcrapp_exclude_global_fields')),
    '#description' => 'Enter a comma separated list of global field ids that will not be part of the global field default.',
    '#weight' => $weight++,
  );

  $form['BCRApp']['listbuilding_bcrapp_regex'] = array(
    '#type' => 'textarea',
    '#title' => 'Regular expressions used inside the app',
    '#rows' => 20,
    '#default_value' => variable_get('listbuilding_bcrapp_regex', ''),
    '#description' => 'Enter one key value regex per line, valid json is required.',
    '#weight' => $weight++,
  );

  // --- Listbuilding event deep link email template START

  $form['BCRApp']['klicktipp_deep_link_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_deep_link_subject', ''),
    '#description' => "placeholders: !name !deeplink !site !date",
    '#size' => 60,
    '#maxlength' => 256,
    '#weight' => $weight++,
  );

  $form['BCRApp']['klicktipp_deep_link_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#description' => "placeholders: !name !deeplink !site !date",
    '#default_value' => variable_get('klicktipp_deep_link_body', ''),
    '#cols' => 80,
    '#rows' => 10,
    '#weight' => $weight++,
  );

  // --- Listbuilding event deep link email template END

  $form['#submit'][] = 'klicktipp_settings_listbuildings_submit';

  return system_settings_form($form);

}

function klicktipp_settings_listbuildings_validate($form, &$form_state) {

  $bcrKeyUserID = $form_state['values']['listbuilding_bcrapp_bcrkey_account_id'];
  if (!empty($bcrKeyUserID)) {
    $account = user_load($bcrKeyUserID);
    if (!empty($account)) {
      form_set_error('listbuilding_bcrapp_bcrkey_account_id', 'The user id cannot be related to an existing account.');
    }
  }

  if (!empty($form_state['values']['listbuilding_bcrapp_regex'])) {
    $decodedRegex = drupal_json_decode($form_state['values']['listbuilding_bcrapp_regex']);
    if ($decodedRegex === NULL) {
      form_set_error('listbuilding_bcrapp_regex', 'This field must contain valid json.');
    }
  }

  if (!empty($form_state['values']['listbuilding_bcrapp_deeplink_url']) && !valid_url($form_state['values']['listbuilding_bcrapp_deeplink_url'], TRUE)) {
    form_set_error('listbuilding_bcrapp_deeplink_url', 'Invalid URL');
  }

  if (!empty($form_state['values']['listbuilding_htmldsgvotext_default'])) {
    Libraries::include('htmlfilter.inc');
    $body = $form_state['values']['listbuilding_htmldsgvotext_default'];
    $pos = _klicktipp_html_validate($body);
    if ($pos >= 0) {
      form_set_error('listbuilding_htmldsgvotext_default', "HTML Signature. Invalid html found at position $pos: " . mb_strcut($body, $pos, 30));
    }
  }

}

function klicktipp_settings_listbuildings_submit($form, &$form_state) {

  $keywords = array_filter(array_map('trim', explode("\n", trim($form_state['values']['listbuilding_affiliate_link_keywords']))));
  if (empty($keywords)) {
    $form_state['values']['listbuilding_affiliate_link_keywords'] = Settings::get('listbuilding_affiliate_link_keywords');
  }
  else {
    $form_state['values']['listbuilding_affiliate_link_keywords'] = $keywords;
  }
  if (empty($form_state['values']['listbuilding_bcrapp_exclude_global_fields'])) {
    $form_state['values']['listbuilding_bcrapp_exclude_global_fields'] = [];
  }
  else {
    $form_state['values']['listbuilding_bcrapp_exclude_global_fields'] = array_map('trim', explode(',', $form_state['values']['listbuilding_bcrapp_exclude_global_fields']));
  }

}

/**
 * Callback for admin testmail form
 */
function klicktipp_admin_testmail_form($form, &$form_state, $account) {

  $Subject = "Testemail for {$account->name}";
  $Content = <<<EOD
Customer Profile:<br />
<br />
%User:CompanyName%<br />
%User:FirstName% %User:LastName%<br />
%User:Street%<br />
%User:Zip% %User:City%<br />
%User:Country%<br />
%User:EmailAddress%
EOD;

  $email = Subscribers::DepunycodeEmailAddress($account->mail);
  klicktipp_set_title("Send Testmail for {$account->name} (UserID: {$account->uid}, Email: $email)");

  $weight = 1;

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  $form['PreviewEmailAddress'] = array(
    '#type' => 'textfield',
    '#title' => 'Send to (email address)',
    '#required' => TRUE,
    '#default_value' => '',
    '#weight' => $weight++,
  );

  $SenderDomains = DomainSet::GetValidSenderDomains((array) $account, TRUE);
  // show select box only, if there is something to choose from
  if (is_array($SenderDomains)) {
    if (count($SenderDomains) > 1) {


      $SenderOptions = array();
      foreach ($SenderDomains as $domain => $domainset) {
        $SenderOptions[$domain] = $domain;
      }

      $form['SenderDomain'] = array(
        '#type' => 'select',
        '#title' => 'Sender domain',
        '#options' => $SenderOptions,
        '#default_value' => '',
        '#weight' => $weight++,
      );
    }

    $form['AllHosts'] = array(
      '#type' => 'checkbox',
      '#title' => 'Send email from all hosts (multiple emails)',
      '#default_value' => 0,
      '#weight' => $weight++,
    );
  }

  $form['FromGroup'] = array(
    '#type' => 'fieldset',
    '#title' => 'From + Subject',
    '#weight' => $weight++,
    '#description' => 'Parameters: Use !domain for the sender domain and !bounces for the return-path domain.'
  );

  $form['FromGroup']['Sender1'] = array(
    '#type' => 'textfield',
    '#title' => 'From (domain address)',
    '#required' => TRUE,
    '#default_value' => 'someone@!domain',
    '#weight' => $weight++,
  );
  $form['FromGroup']['Subject1'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject (domain address)',
    '#required' => TRUE,
    '#default_value' => "Testemail (domain address) for {$account->name}",
    '#weight' => $weight++,
    '#element_validate' => array(),
    //allow &<>"' for this field @see klicktipp_element_textfield_validate()
  );

  $form['FromGroup']['Sender2'] = array(
    '#type' => 'textfield',
    '#title' => 'From (bounce address)',
    '#required' => TRUE,
    '#default_value' => 'someone@!bounces',
    '#weight' => $weight++,
  );
  $form['FromGroup']['Subject2'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject (bounce address)',
    '#required' => TRUE,
    '#default_value' => "Testemail (bounce address) for {$account->name}",
    '#weight' => $weight++,
    '#element_validate' => array(),
    //allow &<>"' for this field @see klicktipp_element_textfield_validate()
  );

  $form['FromGroup']['Sender3'] = array(
    '#type' => 'textfield',
    '#title' => 'From (klick-tipp)',
    '#required' => TRUE,
    '#default_value' => '<EMAIL>',
    '#weight' => $weight++,
  );
  $form['FromGroup']['Subject3'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject (klick-tipp)',
    '#required' => TRUE,
    '#default_value' => "Testemail (klick-tipp) for {$account->name}",
    '#weight' => $weight++,
    '#element_validate' => array(),
    //allow &<>"' for this field @see klicktipp_element_textfield_validate()
  );

  $form['BodyContent'] = array(
    '#type' => 'textarea',
    '#title' => 'Text',
    '#default_value' => $Content,
    '#weight' => $weight++,
    '#resizable' => FALSE,
    '#attributes' => array(
      'class' => array('textarea'),
      'style' => 'height:400px;',
    ),
  );

  $form['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_email_submit',
    '#value' => 'Send testmail',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  return $form;
}

/**
 * testmail form submit
 */
function testmail_replace_pmta_domain_by_host($EmailContent, $SenderDomain) {
  if (!empty($SenderDomain['domain']) && !empty($SenderDomain['host'])) {
    // replace domain by host (so PMTA will use this host for sending)
    $EmailContent = str_replace("X-Virtual-MTA: " . $SenderDomain['domain'], "X-Virtual-MTA: " . $SenderDomain['host'], $EmailContent);
  }
  return $EmailContent;
}

function klicktipp_admin_testmail_form_submit($form, &$form_state) {

  Libraries::include('htmlfilter.inc');

  $account = (array) $form_state['values']['account'];
  $PreviewEmailAddress = Subscribers::PunycodeEmailAddress($form_state['values']['PreviewEmailAddress']);
  $Domain = $form_state['values']['SenderDomain'];
  $AllHosts = $form_state['values']['AllHosts'];
  $From1 = $form_state['values']['Sender1'];
  $Subject1 = $form_state['values']['Subject1'];
  $From2 = $form_state['values']['Sender2'];
  $Subject2 = $form_state['values']['Subject2'];
  $From3 = $form_state['values']['Sender3'];
  $Subject3 = $form_state['values']['Subject3'];
  $BodyContent = $form_state['values']['BodyContent'];

  $ArrayEmail = array(
    'RelUserID' => $account['uid'],
    'Subject' => 'pseudo subject',
    'ContentTypeEnum' => Emails::CONTENT_TYPE_BOTH,
    'PlainContent' => _klicktipp_html_to_text($BodyContent),
    'HTMLContent' => $BodyContent,
  );

  $ArraySubscription = Subscribers::SelectDummySubscriber($account['uid']);
  $ReferenceID = 0;

  $SenderDomains = DomainSet::GetValidSenderDomains($account, TRUE);

  $domainset = DomainSet::SelectDomainSet($account['uid'], $SenderDomains, $Domain);
  if (empty($domainset)) {
    $hosts = array('' => '');
  }
  else {
    if (empty($AllHosts)) {
      // get first entry
      $hosts = array_slice($domainset['Data']['hosts'], 0, 1, TRUE);
    }
    else {
      $hosts = $domainset['Data']['hosts'];
    }
  }

  foreach ($hosts as $host => $ip) {
    $SenderDomain = DomainSet::FillSenderParameters($domainset, $account['uid'], $host, $account['mail']);

    [$TMPSubject, $TMPPlainBody, $TMPHTMLBody, $ContentType] = Personalization::PersonalizeEmail(
      $account, 0, $ArrayEmail, [], $ArraySubscription, $ReferenceID, $SenderDomain, [], FALSE, TRUE, TRUE, 0);

    $AbuseMessageID = Core::GenerateAbuseMessageID($ArrayEmail['EmailID'], $ArraySubscription['SubscriberID'], $ReferenceID,
      $ArraySubscription['EmailAddress'], $account['uid'], 0);

    $HostForSubject = (empty($SenderDomain['host']) ? '' : $SenderDomain['host'] . ': ');

    $replace = array(
      '!domain' => $SenderDomain['domain'],
      '!bounces' => $SenderDomain['bounces'],
    );

    // domain address

    // maybe a white label domain
    $FromEmail = Subscribers::PunycodeEmailAddress(strtr($From1, $replace));

    $EmailContent = SendEngine::SetEmailProperties(
      $SenderDomain,
      $ArrayEmail['FromName'], // $FromName
      $FromEmail, // $FromEmail
      $PreviewEmailAddress, // $ToEmail
      $FromEmail, // $ReplyToEmail
      array(), // $ArrayHeaders
      $ContentType, // $ContentType
      $HostForSubject . $Subject1, // $Subject
      $TMPHTMLBody, // $HTMLContent
      $TMPPlainBody, // $PlainContent
      $AbuseMessageID, // $AbuseMessageID
      0, // $CampaignID
      0, // $EmailID
      $account['uid'], // $UserID
      $ArraySubscription['SubscriberID'], // $SubscriberID
      $ReferenceID,
      $ArraySubscription['RelListID'] // $ListID
    );

    $EmailContent = testmail_replace_pmta_domain_by_host($EmailContent, $SenderDomain);

    $ArrayResult = SendEngine::SendEmail($EmailContent, 'con', $SenderDomain['host']);
    if ($ArrayResult == FALSE) {
      drupal_set_message('Error Occurred', 'error', FALSE);
    }
    else {
      $address = Subscribers::DepunycodeEmailAddress($PreviewEmailAddress);
      drupal_set_message("A preview email is sent to given email addresses ($address) via host {$SenderDomain['host']} with sender $FromEmail.");
    }

    // bounces address

    $FromEmail = strtr($From2, $replace);

    $EmailContent = SendEngine::SetEmailProperties(
      $SenderDomain,
      $ArrayEmail['FromName'], // $FromName
      $FromEmail, // $FromEmail
      $PreviewEmailAddress, // $ToEmail
      $FromEmail, // $ReplyToEmail
      array(), // $ArrayHeaders
      $ContentType, // $ContentType
      $HostForSubject . $Subject2, // $Subject
      $TMPHTMLBody, // $HTMLContent
      $TMPPlainBody, // $PlainContent
      $AbuseMessageID, // $AbuseMessageID
      0, // $CampaignID
      0, // $EmailID
      $account['uid'], // $UserID
      $ArraySubscription['SubscriberID'], // $SubscriberID
      $ReferenceID,
      $ArraySubscription['RelListID'] // $ListID
    );

    $EmailContent = testmail_replace_pmta_domain_by_host($EmailContent, $SenderDomain);

    $ArrayResult = SendEngine::SendEmail($EmailContent, 'con', $SenderDomain['host']);
    if ($ArrayResult == FALSE) {
      drupal_set_message('Error Occurred', 'error', FALSE);
    }
    else {
      $email = Subscribers::DepunycodeEmailAddress($PreviewEmailAddress);
      drupal_set_message("A preview email is sent to given email addresses ($email) via host {$SenderDomain['host']} with sender $FromEmail.");
    }

    // klick-tipp

    $FromEmail = strtr($From3, $replace);

    $EmailContent = SendEngine::SetEmailProperties(
      $SenderDomain,
      $ArrayEmail['FromName'], // $FromName
      $FromEmail, // $FromEmail
      $PreviewEmailAddress, // $ToEmail
      $FromEmail, // $ReplyToEmail
      array(), // $ArrayHeaders
      $ContentType, // $ContentType
      $HostForSubject . $Subject3, // $Subject
      $TMPHTMLBody, // $HTMLContent
      $TMPPlainBody, // $PlainContent
      $AbuseMessageID, // $AbuseMessageID
      0, // $CampaignID
      0, // $EmailID
      $account['uid'], // $UserID
      $ArraySubscription['SubscriberID'], // $SubscriberID
      $ReferenceID,
      $ArraySubscription['RelListID'] // $ListID
    );

    $EmailContent = testmail_replace_pmta_domain_by_host($EmailContent, $SenderDomain);

    $ArrayResult = SendEngine::SendEmail($EmailContent, 'con', $SenderDomain['host']);
    if ($ArrayResult == FALSE) {
      drupal_set_message('Error Occurred', 'error', FALSE);
    }
    else {
      $email = Subscribers::DepunycodeEmailAddress($PreviewEmailAddress);
      drupal_set_message("A preview email is sent to given email addresses ($email) via host {$SenderDomain['host']} with sender $FromEmail.");
    }
  }

}

/**
 * Callback for admin testmail form
 */
function klicktipp_admin_subscriber_export_form($form, &$form_state) {

  $form['klicktipp_export_queue_item_size'] = array(
    '#type' => 'textfield',
    '#title' => 'Cron queue size',
    '#default_value' => variable_get('klicktipp_export_queue_item_size', 1000),
    '#description' => "Enter the number of subscribers to be exported in 1 queue item.",
    '#required' => TRUE,
  );

  // --- Export email template START

  $form['klicktipp_export'] = array(
    '#type' => 'fieldset',
    '#title' => 'Klick-Tipp export email template',
    '#description' => 'This email with a download link for the export file is send to the customer, when the export batch process is finished.',
  );

  $form['klicktipp_export']['klicktipp_export_subject'] = array(
    '#type' => 'textfield',
    '#title' => 'Subject',
    '#default_value' => variable_get('klicktipp_export_subject', 'Contact export for !username at !site'),
    '#description' => "The placeholder !username and !site will be replaced by the username and the site name.",
    '#size' => 60,
    '#maxlength' => 256,
    '#required' => TRUE,
  );

  $form['klicktipp_export']['klicktipp_export_body'] = array(
    '#type' => 'textarea',
    '#title' => 'Body',
    '#description' => "The placeholders !username, !site, !date and !download_link will be replaced.",
    '#default_value' => variable_get('klicktipp_export_body',
      'Hello !username,

The contact export initiated on !date at !site has completed.
You can download the export file by clicking the following link:

!download_link

Important: You need to be logged in to !site to access the file!

Kind regards
Your team at !site
'),
    '#cols' => 80,
    '#rows' => 10,
    '#required' => TRUE,
  );

  // --- Export email template END

  return system_settings_form($form);

}

/*
 * HelpScout NPS Settings
 * @see: used in callbacks/helpscout.inc
 */

function klicktipp_settings_helpscout($form, &$form_state) {

  $weight = 1;

  $form['klicktipp_helpscout_nps'] = array(
    '#type' => 'fieldset',
    '#title' => 'HelpScout NPS Settings',
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_nps']['credentials']['klicktipp_helpscout_api_app_id'] = array(
    '#type' => 'textfield',
    '#title' => 'HelpScout API App ID',
    '#default_value' => variable_get('klicktipp_helpscout_api_app_id', ''),
    '#description' => 'Enter the HelpScout API App ID.',
    '#required' => TRUE,
    '#parents' => array('credentials', 'klicktipp_helpscout_api_app_id'),
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_nps']['credentials']['klicktipp_helpscout_api_app_secret'] = array(
    '#type' => 'textfield',
    '#title' => 'HelpScout API App Secret',
    '#default_value' => variable_get('klicktipp_helpscout_api_app_secret', ''),
    '#description' => 'Enter the HelpScout API App Secret.',
    '#required' => TRUE,
    '#parents' => array('credentials', 'klicktipp_helpscout_api_app_secret'),
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_nps']['klicktipp_helpscout_nps_webhook_secret_key'] = array(
    '#type' => 'textfield',
    '#title' => 'HelpScout Webhook Secret Key',
    '#default_value' => variable_get('klicktipp_helpscout_nps_webhook_secret_key', ''),
    '#description' => 'Enter the Webhook Secret Key that was entered inside HelpScout.',
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_nps']['klicktipp_helpscout_nps_helpscout_mailboxes'] = array(
    '#type' => 'textfield',
    '#title' => 'HelpScout Mailboxes',
    '#default_value' => implode(',', (array)variable_get('klicktipp_helpscout_nps_helpscout_mailboxes')),
    '#description' => 'Enter a comma separated list of HelpScout mailbox IDs.',
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_nps']['klicktipp_nps_google_oauth_refresh_token'] = array(
    '#type' => 'textfield',
    '#title' => 'Google NPS Spreadsheet OAuth2 Refresh Token',
    '#default_value' => variable_get('klicktipp_nps_google_oauth_refresh_token', ''),
    '#description' => 'Enter the Google OAuth2 Refresh Token used for NPS.',
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_nps']['klicktipp_nps_google_oauth_clientid'] = array(
    '#type' => 'textfield',
    '#title' => 'Google NPS Spreadsheet OAuth2 ClientID',
    '#default_value' => variable_get('klicktipp_nps_google_oauth_clientid', ''),
    '#description' => 'Enter the Google OAuth2 ClientID used for NPS.',
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_nps']['klicktipp_nps_google_oauth_clientsecret'] = array(
    '#type' => 'textfield',
    '#title' => 'Google NPS Spreadsheet OAuth2 ClientSecret',
    '#default_value' => variable_get('klicktipp_nps_google_oauth_clientsecret', ''),
    '#description' => 'Enter the Google OAuth2 ClientSecret used for NPS.',
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_nps']['klicktipp_nps_google_sheets_spreadsheetid'] = array(
    '#type' => 'textfield',
    '#title' => 'Google NPS SpreadsheetID',
    '#default_value' => variable_get('klicktipp_nps_google_sheets_spreadsheetid', ''),
    '#description' => 'Enter the Google OAuth2 Spreadsheet ID used for NPS.',
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_scripts_beacon'] = array(
    '#type' => 'textarea',
    '#rows' => 3,
    '#title' => t('Helpscout Beacon Scripts'),
    '#default_value' => variable_get('klicktipp_helpscout_scripts_beacon', ''),
    '#description' => 'JavaScript snippet to include the Beacon widget<br />' .
      'Replacements: "%name%", "%username%", "%email%", "%accountType%"<br />' .
      'FeatureToggle: "show-beacon"',
    '#weight' => $weight++,
  );

  $form['klicktipp_helpscout_scripts_user_like'] = array(
    '#type' => 'textarea',
    '#rows' => 3,
    '#title' => t('Helpscout UserLike Scripts'),
    '#default_value' => variable_get('klicktipp_helpscout_scripts_user_like', ''),
    '#description' => 'JavaScript snippet to include the Userlike widegt<br />' .
      'Replacements: "%name%", "%username%", "%email%", "%accountType%"<br />' .
      'FeatureToggle: "show-user-like"',
    '#weight' => $weight++,
  );

  $form['#submit'][] = 'klicktipp_settings_helpscout_submit';

  return system_settings_form($form);

}

function klicktipp_settings_helpscout_validate($form, $form_state) {

  Libraries::include('helpscout.inc');

  $applicationId = $form_state['values']['credentials']['klicktipp_helpscout_api_app_id'];
  $applicationSecret = $form_state['values']['credentials']['klicktipp_helpscout_api_app_secret'];

  if (!klicktipp_helpscout_api_is_valid_credentials($applicationId, $applicationSecret)){
    form_set_error('credentials', "Invalid App ID or App Secret");
  }

}

function klicktipp_settings_helpscout_submit($form, &$form_state) {

  // to have a red error frame on both input fields but save the credentials separately
  // we have to store values into a flatter array structure and unset the parent so we get not
  // a additional variable called "credentials"
  $form_state['values']['klicktipp_helpscout_api_app_id'] = $form_state['values']['credentials']['klicktipp_helpscout_api_app_id'];
  $form_state['values']['klicktipp_helpscout_api_app_secret'] = $form_state['values']['credentials']['klicktipp_helpscout_api_app_secret'];
  unset($form_state['values']['credentials']);

  // we have to delete an existing token if we provide new valid API credentials
  // because a existing token could belong to annother account
  Libraries::include('helpscout.inc');
  variable_set(HELPSCOUT_API_TOKEN_VARIABLE_KEY, NULL);

  if (empty($form_state['values']['klicktipp_helpscout_nps_helpscout_mailboxes'])) {
    $form_state['values']['klicktipp_helpscout_nps_helpscout_mailboxes'] = array();
  }
  else {
    $form_state['values']['klicktipp_helpscout_nps_helpscout_mailboxes'] = array_map('trim', explode(',', $form_state['values']['klicktipp_helpscout_nps_helpscout_mailboxes']));
  }
}

function klicktipp_settings_product_fruits($form, &$form_state) {

  $weight = 1;

  $form['klicktipp_product_fruits_settings'] = array(
    '#type' => 'fieldset',
    '#title' => 'Product Fruits Settings',
    '#weight' => $weight++,
  );

  $form['klicktipp_product_fruits_settings']['klicktipp_product_fruits_workspace_code'] = array(
    '#type' => 'textfield',
    '#title' => 'Product Fruits Workspace Code',
    '#default_value' => variable_get('klicktipp_product_fruits_workspace_code', ''),
    '#description' => 'Enter the Product Fruits Workspace Code.',
    '#weight' => $weight++,
  );

  $form['klicktipp_product_fruits_scripts'] = array(
    '#type' => 'textarea',
    '#rows' => 3,
    '#title' => t('Product Fruits Scripts'),
    '#default_value' => variable_get('klicktipp_product_fruits_scripts', ''),
    '#description' => 'JavaScript snippet to include the Product Fruits widget<br />' .
      'FeatureToggle: "show-product-fruits"',
    '#weight' => $weight++,
  );

  $dashboard = klicktipp_get_setting('klicktipp_product_fruits_dashboard');

  $form['klicktipp_product_fruits_dashboard'] = [
    '#type' => 'fieldset',
    '#title' => 'Customer Dashboard Settings',
    '#tree' => true,
    '#weight' => $weight++,
  ];

  $form['klicktipp_product_fruits_dashboard']['onboardingChecklistActivationDate'] = array(
    '#type' => 'textfield',
    '#title' => 'Onboarding Checklists Start Date',
    '#default_value' => $dashboard['onboardingChecklistActivationDate'],
    '#description' => 'Users with a first login date after start date will see the Onboarding checklists<br />' .
      'Format: yyyy-mm-dd',
    '#weight' => $weight++,
  );

  $form['klicktipp_product_fruits_dashboard']['onboardingChecklistIdentifier'] = array(
    '#type' => 'textfield',
    '#title' => 'Onboarding Checklist Identifier',
    '#default_value' => $dashboard['onboardingChecklistIdentifier'],
    '#description' => 'All checklists with a name that starts with [identifier] will be included on the dashboard',
    '#weight' => $weight++,
  );

  $form['klicktipp_product_fruits_dashboard']['additionalChecklistIdentifiers'] = array(
    '#type' => 'textarea',
    '#rows' => 10,
    '#title' => t('Additional Checklist Identifiers'),
    '#default_value' => $dashboard['additionalChecklistIdentifiers'],
    '#description' => 'Add additional identifiers (Checklists with names that start with [identifier] '.
      'will be included on the dashboard.)<br />' .
      'Add 1 identifier per line.',
    '#weight' => $weight++,
  );

  $form['klicktipp_product_fruits_dashboard']['sidebarShadowDomUrl'] = array(
    '#type' => 'textfield',
    '#title' => 'Sidebar Content',
    '#default_value' => $dashboard['sidebarShadowDomUrl'],
    '#description' => 'The sidebar content will be loaded from the Marketing Page via the set url.',
    '#weight' => $weight++,
  );

  return system_settings_form($form);

}

/**
 * Import a CSV file (Google Docs spreadsheet) with a path/HelpTag matrix
 *
 * Important: An import will delete all previous taggings, the file MUST contain all dialogs and helptags/categories
 *
 * New helptags and categories (taxonomies in the help vocabulary) can be created by adding the tag names to the header of the CSV file
 * All helptags and categories (taxonomies in the help vocabulary) that are not in the header will be deleted from the vocabulary
 *
 * CSV file format:
 * 1: Index,NodeID,Dialog/Title,Total Visits,Recent Visits,Include,URL,TagName[,TagName[,TagName[...]]]
 * 2: Category,,,,,,CategoryOfTagAbove[,CategoryOfTagAbove[,...]]
 *
 * Example (simplified):
 * NodeID,Dialog/Title,TagA,TagB
 * 4711  ,            ,  x ,     -> Node 4711 will be tagged with TagA
 *       , emails/%   ,  x ,  v  -> dialog emails/% will be tagged with TagA and TagB
 *
 * TODO: the 'v' indicates to also show an icon to display videos specific to that dialog (contained in the nodes tagged with TagB)
 */

function klicktipp_import_help_tags_form($form, $form_state) {

  $step = (empty($form_state['values']['step'])) ? 1 : $form_state['values']['step'];

  $form = array();

  $weight = 1;

  $form['step'] = array(
    '#type' => 'value',
    '#value' => $step,
  );

  if ($step == 1) {

    $form['Export'] = array(
      '#type' => 'fieldset',
      '#title' => 'Export',
      '#description' => 'Export current manual page and dialog taggings as well as the page view count.',
    );

    $form['Export']['buttons'] = array(
      '#type' => 'markup',
      '#weight' => $weight++,
      '#prefix' => '<div class="button-row">',
      '#suffix' => '</div>',
    );

    $form['Export']['buttons']['export'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_download_submit',
      '#value' => KLICKTIPP_HELPBLOCK_BUTTON_TEXT_EXPORT_TAGS,
      '#weight' => $weight++,
    );

    $form['Import'] = array(
      '#type' => 'fieldset',
      '#title' => 'Import',
      '#description' => 'Import manual page and dialog taggings.',
    );

    $form['Import']['csv_file'] = array(
      '#type' => 'file',
      '#size' => 48,
      '#title' => "CSV file",
      '#weight' => $weight++,
    );

    $form['Import']['buttons'] = array(
      '#type' => 'markup',
      '#weight' => $weight++,
      '#prefix' => '<div class="button-row">',
      '#suffix' => '</div>',
    );

    $form['Import']['buttons']['import'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_upload_submit',
      '#value' => KLICKTIPP_HELPBLOCK_BUTTON_TEXT_IMPORT_TAGS,
      '#weight' => $weight++,
    );

    $form['Images'] = array(
      '#type' => 'fieldset',
      '#title' => 'Images',
      '#description' => "<div id='image-url' style='border:1px solid #333333; line-height: 20px; padding: 5px; min-width: 50%;'>&nbsp;</div>",
    );

    $form['Images']['CKFinder'] = array(
      '#type' => 'item',
      '#title' => 'Manage',
      '#theme' => 'klicktipp_edit_button',
      '#value' => '#',
      '#attributes' => array(
        'data-event-click' => 'js_open_ckfinder',
      ),
    );

    //ckfinder path and script
    $path_to_CKF = APP_URL . "ckfinder";
    $ckf_type = t("Content-include-images");

    $script = "

    window['js_open_ckfinder'] = function(element, args) {

      var href = '$path_to_CKF/ckfinder.html?action=js&func=js_set_image_url&type=$ckf_type';
      window.open(href, 'CKFinder', 'resizeable=yes');

      element.attr('href', 'JavaScript:void(0)');
      return false;
    }

    window['js_set_image_url'] = function(url) {
        if (url != '') {
          $('#image-url').text(url);
        }
      }

  ";

    drupal_add_js($script, array('type' => 'inline', 'scope' => 'footer'));

  }
  else {

    if (!empty($form_state['storage']['AddTags'])) {

      $tags = '<span style="border:1px solid #555;padding:10px;"><span style="background-color:#5cb85c;color:#ffffff;padding:5px;">' .
        implode('</span> <span style="background-color:#5cb85c;color:#ffffff;padding:5px;">', $form_state['storage']['AddTags']) .
        '</span></span>';

      $form['AddTags'] = array(
        '#type' => 'markup',
        '#markup' => "<h3>The following help tags will be created:</h3>$tags",
      );

    }
    else {

      $form['no_tag_create'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_info',
        '#weight' => $weight++,
        '#value' => 'No new help tags will be created.',
      );

    }

    if (!empty($form_state['storage']['DeleteTags'])) {

      $tags = '<span style="border:1px solid #555;padding:10px;"><span style="background-color:#d9534f;color:#ffffff;padding:5px;">' .
        implode('</span> <span style="background-color:#d9534f;color:#ffffff;padding:5px;">', $form_state['storage']['DeleteTags']) .
        '</span></span>';

      $form['DeleteTags'] = array(
        '#type' => 'markup',
        '#markup' => "<h3>The following help tags will be deleted:</h3>$tags",
      );

    }
    else {

      $form['no_tag_delete'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_info',
        '#weight' => $weight++,
        '#value' => 'No help tags will be deleted.',
      );

    }

    if (!empty($form_state['storage']['AddCategories'])) {

      $categories = '<span style="border:1px solid #555;padding:10px;"><span style="background-color:#5cb85c;color:#ffffff;padding:5px;">' .
        implode('</span> <span style="background-color:#5cb85c;color:#ffffff;padding:5px;">', $form_state['storage']['AddCategories']) .
        '</span></span>';

      $form['AddCategories'] = array(
        '#type' => 'markup',
        '#markup' => "<h3>The following help tag categories will be created:</h3>$categories",
      );

    }
    else {

      $form['no_category_create'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_info',
        '#weight' => $weight++,
        '#value' => 'No new help tag categories will created.',
      );

    }

    if (!empty($form_state['storage']['DeleteCategories'])) {

      $categories = '<span style="border:1px solid #555;padding:10px;"><span style="background-color:#d9534f;color:#ffffff;padding:5px;">' .
        implode('</span> <span style="background-color:#d9534f;color:#ffffff;padding:5px;">', $form_state['storage']['DeleteCategories']) .
        '</span></span>';

      $form['DeleteCategories'] = array(
        '#type' => 'markup',
        '#markup' => "<h3>The following help tag categories will be deleted:</h3>$categories",
      );

    }
    else {

      $form['no_category_delete'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_info',
        '#weight' => $weight++,
        '#value' => 'No help tag categories will be deleted.',
      );

    }

    $form['confirm'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#weight' => $weight++,
      '#value' => 'Continue import?',
    );

    $form['buttons'] = array(
      '#type' => 'markup',
      '#weight' => $weight++,
      '#prefix' => '<br /><div class="button-row">',
      '#suffix' => '</div>',
    );

    $form['buttons']['import_tags'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_upload_submit',
      '#value' => KLICKTIPP_HELPBLOCK_BUTTON_TEXT_IMPORT_TAGS,
      '#weight' => $weight++,
    );

    $form['buttons']['cancel'] = array(
      '#theme' => 'klicktipp_cancel_button',
      '#title' => KLICKTIPP_BUTTON_TEXT_CANCEL,
      '#value' => "admin/config/klicktipp/happiness",
      '#weight' => $weight++,
    );

  }

  return $form;

}

function klicktipp_import_help_tags_form_submit($form, &$form_state) {

  Libraries::include('help_block.inc');

  $HelpVocabularyID = variable_get(KLICKTIPP_HELP_VOCABULARY_ID, 0);

  if ($form_state['values']['op'] == KLICKTIPP_HELPBLOCK_BUTTON_TEXT_EXPORT_TAGS) {

    klicktipp_export_help_tags();
    return;

  }

  if ($form_state['values']['op'] == KLICKTIPP_HELPBLOCK_BUTTON_TEXT_IMPORT_TAGS) {

    $step = $form_state['values']['step'];

    $validators = array(
      'file_validate_extensions' => array('csv'), //just allow csv files
    );
    $file_info = file_save_upload('csv_file', $validators);

    if ($step == 1) {

      $FileError = '';
      if ($file_info != 0) {

        if ($file_info->filesize > 0) {

          if (($handle = fopen($file_info->destination, "r")) !== FALSE) {

            //csv file contains a header
            $Header = fgets($handle);
            $Header = str_getcsv($Header, ',', '"');

            //csv file contains a second header
            $Categories = fgets($handle);
            $Categories = str_getcsv($Categories, ',', '"');

            $Header = array_map('trim', array_map('check_plain', $Header));

            $Categories = array_map('trim', array_map('check_plain', $Categories));

            if (empty($Header) || $Header[0] != 'Index' || $Header[1] != 'NodeID' || $Header[2] != 'Dialog/Title' || $Header[3] != 'Total Visits' || $Header[4] != 'Recent Visits' || $Header[5] != 'Include' || $Header[6] != 'URL') {
              $FileError = "The uploaded file contains an invalid CSV format. The file must have a header: Index,NodeID,Dialog/Title,Total Visits, Recent Visits,Include,URL followed by help tag names.";
            }
            elseif (empty($Categories) || $Categories[0] != 'Category') {
              $FileError = "The uploaded file contains an invalid CSV format. The file must have a second header: Category',,,,,, followed by the category names of tags above.";
            }
            elseif (count($Header) != count($Categories)) {
              $FileError = "The uploaded file contains an invalid CSV format. Column count of tag names and categories do not match.";
            }

            if (empty($FileError)) { //no error so far

              $unique = array_unique($Header);
              if (count($Header) != count($unique)) {
                $tags = implode(', ', array_diff_assoc($Header, $unique));
                $FileError = "Duplicate tag names found: $tags";
              }

            }

            if (empty($FileError)) { //no error so far

              foreach ($Header as $key => $value) {

                if ($key <= 6) {
                  continue;
                }

                if (empty($value)) {
                  $FileError = "Column with no tag name found.";
                  break;
                }
                elseif (empty($Categories[$key])) {
                  $FileError = "Tag $value has no category.";
                  break;
                }
                else {
                  $intersect = array_intersect($Header, $Categories);
                  if (count($intersect) > 0) {
                    $cats = implode(', ', $intersect);
                    $FileError = "Categories cannot have the same name as tags: $cats";
                  }
                }

              }

              $existingCategories = array();
              $existingTags = array();
              if (empty($FileError)) { //no error so far

                $taxonomies = taxonomy_get_tree($HelpVocabularyID);

                foreach ($taxonomies as $tax) {
                  $parents = array_filter($tax->parents); //no parents == array(0 => 0);
                  if (empty($parents)) {
                    $existingCategories[$tax->tid] = $tax->name;
                  }
                  else {
                    $existingTags[$tax->tid] = $tax->name;
                  }
                }

                $importTags = $Header;
                unset($importTags[0]); //'Index'
                unset($importTags[1]); //'NodeID'
                unset($importTags[2]); //'Dialog/Title'
                unset($importTags[3]); //'Total Visits'
                unset($importTags[4]); //'Recent Visits'
                unset($importTags[5]); //'Include'
                unset($importTags[6]); //'URL'
                $interTags = array_intersect($existingTags, $importTags);
                $deleteTags = array_diff($existingTags, $interTags);
                $addTags = array_diff($importTags, $interTags);

                $importCategories = array_unique($Categories);
                unset($importCategories[0]); //'Category'
                unset($importCategories[1]); //empty
                unset($importCategories[2]); //empty
                unset($importCategories[3]); //empty
                unset($importCategories[4]); //empty
                unset($importCategories[5]); //empty
                unset($importCategories[6]); //empty
                $interCategories = array_intersect($existingCategories, $importCategories);
                $deleteCategories = array_diff($existingCategories, $interCategories);
                $addCategories = array_diff($importCategories, $interCategories);

              }

              if (empty($FileError)) { //no error so far

                $form_state['storage']['file_info'] = $file_info;

                $Taggings = array();

                while (($line = fgets($handle)) !== FALSE) {

                  $data = str_getcsv($line, ',', '"');
                  $nid = 0;
                  $path = '';

                  //$key: 0 => Index, 1 => NodeID, 2 => Dialog/Title, 3 => Total Visits, 4 => Recent Visits, 5 => Include, 6 => URL
                  foreach ($data as $key => $value) {

                    if ($key == 1) {
                      //the value is the node id or empty if dialog
                      if (!empty($value)) {
                        $nid = $value;
                        $path = "node/$nid";
                        $Taggings[$path] = array();
                      }
                    }
                    elseif ($key == 2) {
                      //the value is the dialog path (or the node title)
                      if (empty($nid)) {
                        $path = $value;
                        $Taggings[$path] = array();
                      }
                    }
                    elseif ($key <= 6) {
                      //ignore
                    }
                    else {

                      $tagged = strtolower(trim($value));

                      if (!empty($tagged)) {

                        $type = ($tagged == 'v') ? HELPTAG_TYPE_DIALOG_VIDEO : HELPTAG_TYPE_DEFAULT;

                        $Taggings[$path][] = array(
                          'name' => $Header[$key],
                          'category' => $Categories[$key],
                          'type' => $type,
                        );

                      }

                    }

                  }

                }

                $form_state['storage']['ExistingTags'] = $existingTags;
                $form_state['storage']['AddTags'] = $addTags;
                $form_state['storage']['DeleteTags'] = $deleteTags;
                $form_state['storage']['AddCategories'] = $addCategories;
                $form_state['storage']['DeleteCategories'] = $deleteCategories;

                $form_state['storage']['Taggings'] = $Taggings;

                $form_state['values']['step'] = 2;

              }

            }

            fclose($handle);

          }
          else {
            $FileError = "The uploaded file could not be processed.";
          }

        }
        else {
          $FileError = "The uploaded file is empty.";
        }

      }
      else {
        $FileError = "The file could not be uploaded.";
      }

      if (!empty($FileError)) {
        drupal_set_message($FileError, 'error');
      }

      $form_state['rebuild'] = TRUE;

    }
    elseif ($step = 2) {

      $TagsByName = array_flip($form_state['storage']['ExistingTags']);

      if (!empty($form_state['storage']['DeleteTags'])) {

        foreach ($form_state['storage']['DeleteTags'] as $TagName) {
          if (!empty($TagsByName[$TagName])) {
            taxonomy_term_delete($TagsByName[$TagName]);
          }
        }

      }

      if (!empty($form_state['storage']['DeleteCategories'])) {

        foreach ($form_state['storage']['DeleteCategories'] as $tid => $TagName) {
          taxonomy_term_delete($tid);
        }

      }

      if (!empty($form_state['storage']['Taggings'])) {

        //remove all previous taggings
        db_query("TRUNCATE {helptagging}");

        //add taggings
        foreach ($form_state['storage']['Taggings'] as $path => $Tagging) {

          foreach ($Tagging as $tag) {

            //create help tag &&|| create category &&|| update category of tag
            $term = klicktipp_help_tag_get($tag['name'], $tag['category']);

            if ($term) {

              db_query("INSERT INTO {helptagging} (tid, path, type) VALUES (:tid, :path, :type)", array(
                ':tid' => $term->tid,
                ':path' => $path,
                ':type' => $tag['type'],
              ));

            }

          }

        }

      }

      //clear caches to update the help block
      cache_clear_all();

    }

  }

}

/**
 * Display the contents of the {campaigs} data field in a textarea to be copied and inserted into a dev database
 * All entity ids (emailID, campaignID,...) will be set to 0
 * Note: this form won't alter the selected campaign
 * @param $form
 * @param $form_state
 * @param stdClass $CustomerAccount : drupal account object
 * @return array
 */
function klicktipp_automation_developer_export_form($form, $form_state, $CustomerAccount) {

  $form = array();
  $weight = 1;

  klicktipp_set_title("Developer campaign export for user {$CustomerAccount->username} (UserID: {$CustomerAccount->uid})");

  //get all processflows and rules
  $result = db_query("SELECT * FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID AND AutoResponderTriggerTypeEnum IN (:Types)", array(
    ':RelOwnerUserID' => $CustomerAccount->uid,
    ':Types' => array(
      Campaigns::TRIGGER_TYPE_PROCESSFLOW
    ),
  ));

  $ArrayCampaigns = array('0' => 'Please select');
  $ArrayProcessflows = array();
  while ($DBArray = kt_fetch_array($result)) {

    $Data = unserialize($DBArray['Data']);

    $id = $DBArray['CampaignID'];
    $name = $DBArray['CampaignName'] . ' (' . Campaigns::$DisplayCampaignType[$DBArray['AutoResponderTriggerTypeEnum']] . ')';
    $ArrayCampaigns[$id] = $name;

    //these fields will be set to 0 in every state that contains them
    $remove = array(
      'outboundID',
      'tagID',
      'customFieldID',
      'audienceID',
      'campaignID',
      'emailID',
      'splittestID',
    );

    foreach ($Data['ProcessFlow']['states'] as $stateid => $state) {
      foreach ($remove as $key) {
        if (isset($state[$key])) {
          $Data['ProcessFlow']['states'][$stateid][$key] = 0;
        }
      }
    }

    $ArrayProcessflows[$id] = serialize($Data);

  }

  //store the serialized Data field in the Druapl.settings to make them available in the form
  drupal_add_js(array('Processflows' => $ArrayProcessflows), 'setting');

  //dropdown with all processflows and rules
  //onchange will display the Data field in the textarea
  $form['CampaignOptions'] = array(
    '#type' => 'select',
    '#title' => 'Automations',
    '#options' => $ArrayCampaigns,
    '#weight' => $weight++,
    '#attributes' => array(
      'onchange' => 'js_select_campaign(this)',
    ),
    '#suffix' => "
       <script type='text/javascript'>
        function js_select_campaign(element) {

          var id = $(element).val();
          $('#edit-processflow').val(Drupal.settings.Processflows[id]);
        }
      </script>
    "
  );

  $form['Processflow'] = array(
    '#type' => 'textarea',
    '#title' => 'Campaign Data',
    '#value' => '',
    '#weight' => $weight++,
  );


  return $form;

}

function klicktipp_firstnames_create_form($form, &$form_state) {

  $weight = 1;

  $form = array();

  $form['FirstName'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#size' => 60,
    '#maxlength' => 60,
    '#title' => 'FirstName',
    '#default_value' => '',
  );

  $genderOptions = array(
    CustomFields::GENDER_MALE => 'Male',
    CustomFields::GENDER_FEMALE => 'Female',
    CustomFields::GENDER_UNISEX => 'Unisex',
  );
  $form['Gender'] = array(
    '#type' => 'select',
    '#weight' => $weight++,
    '#title' => "Gender",
    '#options' => $genderOptions,
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => 'Create Firstname',
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_cancel_button',
    '#title' => KLICKTIPP_BUTTON_TEXT_CANCEL,
    '#value' => "admin/config/",
    '#weight' => $weight++,
  );


  return $form;

}

function klicktipp_firstnames_create_form_validate($form, &$form_state) {
  if (preg_match('/\\d/', $form_state['values']['FirstName'])) {
    // firstname contains numbers
    form_set_error('FirstName', 'Firstname cannot contain numbers.');
  }
}

function klicktipp_firstnames_create_form_submit($form, &$form_state) {
  CustomFields::CreateFirstname($form_state['values']['FirstName'], $form_state['values']['Gender']);
  drupal_set_message("Firstname {$form_state['values']['FirstName']} successfully created.");
}

function klicktipp_settings_millionverifier_form($form, &$form_state) {

  $objectMV = new ApiMillionVerifierDrupalForm();

  $config = $objectMV->GetSettings();

  $form['apiKey'] = [
    '#type' => 'textfield',
    '#title' => 'MillionVerifier Api Key',
    '#default_value' => $config['apiKey'],
  ];

  $form['excludeISPs'] = [
    '#type' => 'textarea',
    '#title' => 'Exclude email addresses with ISPs from verification',
    '#default_value' => $config['excludeISPs'],
    '#description' => 'Email addresses with the specified domains will not be checked<br />' .
      'against the MillionVerifier API and will automatically receive the status OK (valid)<br />' .
      'Insert 1 domain per line without the @'
  ];

  $form['cacheDuration'] = [
    '#type' => 'textfield',
    '#title' => 'Cache duration (days)',
    '#default_value' => $config['cacheDuration'],
    '#description' => 'The status of an email address retrieved from the MV Api<br />' .
      'will be cached for this duration'
  ];

  $form['denyThreshold'] = [
    '#type' => 'textfield',
    '#title' => 'Deny threshold',
    '#default_value' => $config['denyThreshold'],
    '#description' => 'If [threshold] percent of email addresses are denied, we deny the whole import.<br >' .
      'Enter a float between 0 and 1 (percent representation)'
  ];

  $form['denyThresholdMinDenies'] = [
    '#type' => 'textfield',
    '#title' => 'Deny threshold minimum denies',
    '#default_value' => $config['denyThresholdMinDenies'],
    '#description' => 'If [threshold] percent of email addresses are denied, we deny the whole import.<br >' .
      'But there must be a minimum of [denies].'
  ];

  $blockMsg = '';
  $blockReason = $objectMV->GetApiBlockedReason();
  if (!empty($blockReason)) {
    $blockMsg = "<p class='error'>The MillionVerifier Api is currently blocked due to '$blockReason'<br />" .
      "Saving this dialog will unblock the API.</p>";
  }
  elseif (!empty($config['apiKey'])) {

    $form['checkEmail'] = [
      '#type' => 'fieldset',
      '#title' => 'Check email address',
      '#description' => 'Enter an email address to retrieve the MillionVerifier status.'
    ];

    $form['checkEmail']['email'] = [
      '#type' => 'textfield',
      '#title' => 'Email address',
      '#default_value' => ''
    ];

    $form['checkEmail']['button'] = [
      '#type' => 'submit',
      '#value' => 'Check',
    ];

  }

  // ---

  $AccountUsage = new VarMillionVerifierUsageDrupalForm();
  $usageConfig = $AccountUsage->GetSettings();

  $form['limits'] = [
    '#type' => 'fieldset',
    '#title' => 'MillionVerifier Api Usage Limits by Account Tier',
    '#description' => 'Specify how many MV Api Calls an account is allowed to perform.',
    '#tree' => TRUE
  ];

  foreach($usageConfig['limits'] as $tier => $limit) {

    $form['limits']["tier$tier"] = [
      '#type' => 'textfield',
      '#title' => "Tier: $tier",
      '#default_value' => $limit,
    ];

  }

  $form['submit'] = [
    '#type' => 'submit',
    '#value' => 'Save configuration',
    '#prefix' => $blockMsg
  ];

  return $form;

}

function klicktipp_settings_millionverifier_form_validate($form, &$form_state) {

  $objectMV = new ApiMillionVerifierDrupalForm();

  $errors = $objectMV->ValidateSettings($form_state['values']);

  if (!empty($errors)) {
    foreach($errors as $error) {
      form_set_error($error[0], $error[1]);
    }
  }

  // ---

  $AccountUsage = new VarMillionVerifierUsageDrupalForm();
  $errors = $AccountUsage->ValidateSettings($form_state['values']);

  if (!empty($errors)) {
    foreach($errors as $error) {
      form_set_error($error[0], $error[1]);
    }
  }

}

function klicktipp_settings_millionverifier_form_submit($form, &$form_state) {

  if ($form_state['values']['op'] == 'Check') {
    $objectMV = new ApiMillionVerifier();
    $result = $objectMV->ValidateEmailAddress($form_state['values']['email']);
    drupal_set_message("Status for {$form_state['values']['email']}: $result" .
      '<p><pre>' . print_r($objectMV->GetLastResponse(), 1) . '</pre></p>');
  }
  else {
    $objectMV = new ApiMillionVerifierDrupalForm();
    $objectMV->UpdateSettings($form_state['values']);

    $AccountUsage = new VarMillionVerifierUsageDrupalForm();
    $AccountUsage->UpdateSettings($form_state['values']);

  }

}


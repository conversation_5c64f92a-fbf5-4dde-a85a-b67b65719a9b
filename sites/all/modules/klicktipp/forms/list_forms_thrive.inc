<?php

use App\Klicktipp\AccountManager;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\Errors;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\SubscriptionFormsThrive;
use App\Klicktipp\Tag;

function klicktipp_list_forms_thrive_create_form ($form, $form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Create Subscription form (@type)', array(
    '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_THRIVE]),
  ));
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Listbuilding'), "listbuilding/$UserID")), $page_title);

  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $weight = 1;

  $form = array();

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => '',
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? [] : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
      'container_class' => 'klicktipp-magicselect-short',
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Create Subscription form'),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "listbuilding/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_list_forms_thrive_create_form_validate ($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];

  if (SubscriptionFormsThrive::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Subscription form (@type) with the name %name already exists.', array(
      '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_THRIVE]),
      '%name' => $Name,
    )));
  }

}

function klicktipp_list_forms_thrive_create_form_submit ($form, &$form_state) {

  $Name = $form_state['values']['Name'];
  $UserID = $form_state['values']['uid'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $BuildID = SubscriptionFormsThrive::InsertDB(array(
    'Name' => $Name,
    'RelOwnerUserID' => $UserID,
    'Settings' => [
      'UseSpamProtection' => 1,
    ],
    DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
  ));

  if (!empty($BuildID)) {

    drupal_set_message(t("Please note that you need to include the spam protection code separately."), 'warning');
    drupal_set_message(t("Subscription form %name successfully created.", array('%name' => $Name)));

    // redirect to countdown edit
    klicktipp_set_redirect($form_state, "listbuilding/$UserID/form-thrive/$BuildID/edit");

  }
  else {

    $error = array(
      '!uid' => $UserID,
      '!name' => $Name,
    );

    //notify user
    Errors::unexpected("SubscriptionFormsThrive: Create for UserID: !uid with name !name", $error, TRUE);

  }

}

function klicktipp_list_forms_thrive_edit_form ($form, &$form_state, $account, $BuildID) {

  $UserID = $account->uid;

  /** @var SubscriptionFormsThrive $ObjectForm */
  if ( !empty($form_state['storage']['ObjectForm']) ) {
    //a custom field has been added to the form and there may be other changes, do not load the form from the database
    //@see: submit and includes/list_forms.inc -> klicktipp_list_forms_add_customfield()
    $ObjectForm = $form_state['storage']['ObjectForm'];
  }
  else {
    $ObjectForm = SubscriptionFormsThrive::FromID($UserID, $BuildID);
  }

  if ( empty($ObjectForm) ) {

    drupal_set_message(t("Subscription form not found."), 'error');
    drupal_goto("listbuilding/$UserID");
    return NULL;

  }

  $page_title = t('Edit Subscription form');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(
    l(t('Listbuilding'), "listbuilding/$UserID"),
  ), $page_title);

  $ArrayForm = $ObjectForm->GetData();

  $Labels = $ObjectForm->GetMetaLabels();
  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $form = array();

  $form['#pre_render'][] = 'klicktipp_list_forms_thrive_edit_form_pre_render';

  $weight = 1;

  //retrieve lists of user
  $ListsOptions = Lists::RetrieveListsAsOptionsArray($UserID);

  //retrieve tags of user
  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['ArrayForm'] = array(
    '#type' => 'value',
    '#value' => $ArrayForm,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => $ArrayForm['Name'],
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? $Labels : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  $linkText = t('Listbuilding::form::Edit Double-Opt-In');
  $form['RelListID'] = array(
    '#type' => 'select',
    '#default_value' => $ArrayForm['RelListID'],
    '#title' => t("Subscription process"),
    '#weight' => $weight++,
    '#options' => $ListsOptions,
    '#suffix' => sprintf('<a href="#" id="doi-link">%s</a>', $linkText),
    '#attributes' => array('class' => array('doi-select')),
  );

  $form['OptInSubscribeTo'] = array(
    '#type' => 'textfield',
    '#default_value' => $ArrayForm['AssignTagID'],
    '#title' => t('Additional tagging (optional)'),
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => FALSE,
      'free_entries' => TRUE,
      'free_entries_message' => t('The tag will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['OptInSubscribeTo'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'additional-tagging',
  );

  $ajax = array();

  Libraries::include('list_forms.inc');

  $form['CustomFields'] = klicktipp_list_forms_customfield_settings_table($ArrayForm, $weight++, $ajax, array(
    //exclude date and time fields
    CustomFields::TYPE_DATE,
    CustomFields::TYPE_DATETIME,
    CustomFields::TYPE_TIME,
    CustomFields::TYPE_HTML,
  ), array('widget')); //no custom field settings

  $form['Settings'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
  );

  if ( variable_get('klicktipp_settings_digistore_enable_multi_device_tracking', 0) ) {

    $multidevicetracking = TRUE;
    if (!klicktipp_feature_access($account, 'access digistore multi device tracking')) {
      $multidevicetracking = FALSE;
      //quickhelp shows an upsell
      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-upsell',
          '#title' => t("Use Digistore24 Multi-Device-Tracking"),
        ),
      ));
    }
    elseif (empty($account->UserSettings['DigiStore24ApiKeys'])) {
      $multidevicetracking = FALSE;
      //quickhelp shows how to connect DigiStore24 accounts
      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-no-accounts',
          '#title' => t("Use Digistore24 Multi-Device-Tracking"),
        ),
      ));
    }
    else {
      //quickhelp shows info about multi-device-tracking (the user does not have to do anything, unlike OptimizePress etc.)
      $quickhelp = theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-thrive',
          '#title' => t("Use Digistore24 Multi-Device-Tracking"),
        ),
      ));
    }

    $form['Settings']['UseDigistoreMultiDeviceTracking'] = array(
      '#type' => 'checkbox',
      '#title' => t("Use Digistore24 Multi-Device-Tracking") . $quickhelp,
      '#default_value' => empty($ArrayForm['Settings']['UseDigistoreMultiDeviceTracking']) ? 0 : 1,
      '#return_value' => 1,
      '#parents' => array('Settings', 'UseDigistoreMultiDeviceTracking'),
      '#disabled' => !$multidevicetracking,
      '#weight' => $weight++,
    );

  }

    if (AccountManager::canDeactivateCaptcha($account)) {
        $form['Settings']['DeactivateCaptcha'] = array(
            '#type' => 'checkbox',
            '#title' => t("Captcha::FormEdit::Checkbox::I want to deactivate the captcha for this form"),
            '#default_value' => empty($ArrayForm['DeactivateCaptcha']) ? 0 : 1,
            '#return_value' => 1,
            '#weight' => $weight++,
            '#attributes' => array(
                'data-event-load' => 'js_checkbox_toggle_element_display',
                'data-event-load-args' => '#captcha-warning',
                'data-event-change' => 'js_checkbox_toggle_element_display',
                'data-event-change-args' => '#captcha-warning',
            )
        );

        $captchaWarning = t('Captcha::FormEdit::Warning::It is not recommended to deactivate the captcha protection.');
        $form['Settings']['DeactivateCaptchaWarning'] = array(
            '#type' => 'markup',
            '#markup' => '<div id="captcha-warning" class="alert alert-warning">' . $captchaWarning . '</div>',
            '#weight' => $weight++,
        );
    }

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => 10000,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['Save'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Save Subscription form'),
    '#weight' => $weight++,
  );

  Libraries::include('list_forms.inc');
  $form['buttons']['EmbedCode'] = klicktipp_list_forms_embed_code_modal("embed-code-$BuildID", $ObjectForm->GetEmbedCode(), $weight++);

  $ModalID = 'modalDeleteConfirmAjax';
  $Title = t('Delete Subscription form');

  $modalFormParameters = [
    "modalTitle" => $Title,
    "modalId" => $ModalID,
    "dependencyMessage" => t('This subscription form is used in the following objects and cannot be deleted:'),
    "entityId" => $BuildID,
    "entityClass" => SubscriptionFormsThrive::class,
    "entityName" => $ArrayForm['Name'],
    "entity" => $ArrayForm,
    "submitFunction" => "klicktipp_list_forms_thrive_delete_confirm_form_submit",
  ];

  // will include the generic modal form "klicktipp_delete_modal_form"
  Libraries::include("form_delete_modal.inc");

  $form['buttons']['ModalDeleteConfirmTrigger'] = [
    '#theme' => 'klicktipp_delete_modal_ajax_button',
    '#title' => $Title,
    '#value' => $ModalID,
    '#weight' => $weight++,
    '#modal_confirm' => drupal_get_form("klicktipp_delete_modal_form", $account, $modalFormParameters),
  ];

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "listbuilding/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_list_forms_thrive_edit_form_pre_render ($form) {
  global $user;
  Libraries::include('list_forms.inc');

  $script = "

    window['js_on_add_custom_field'] = function () {

      var tooltip = $('<div></div>').css('display', 'inline-block');

      //deactivate embed code button
      $('#edit-embedcode').attr({
        'data-toggle': '',
        'data-modal-id': '',
        'disabled': 'disabled'
      }).wrap(tooltip);

      $('#edit-embedcode').parent().tooltip({
        'title': '" . t('Please save the Subscription form first.') . "',
        'container': 'body'
      });

      window['update_view']();

    }

    $(document).ready(function() {
      function updateDoiLink(id) {
          const linkElement = document.getElementById('doi-link');
          linkElement.innerHTML = '" . t('Listbuilding::form::Edit Double-Opt-In') . "';
          linkElement.href = '/app/double-opt-in/settings/$user->uid/' + id +'/edit';
      }

      $('input.cf-select').click(function() {
        //also deactivate embed code button after removing custom fields
        window['js_on_add_custom_field']();
      });

      $('.doi-select').on('change', function(e) {
        updateDoiLink($(this).val());
      });

      const select = $('#edit-rellistid-wrapper select');

      // Fix drupal styles to allow link next to dropdown.
      $('#edit-rellistid-wrapper').css('display', 'inline-block');
      select.css('max-width', 'unset');

      updateDoiLink(select.val())

    });

  ";


  $script .= klicktipp_list_forms_customfield_settings_javascript();

  drupal_add_js($script, array('type' => 'inline', 'scope' => 'header'));

  return $form;

}

function klicktipp_list_forms_thrive_edit_form_validate ($form, &$form_state ) {

  $UserID = $form_state['values']['uid'];
  $ArrayForm = $form_state['values']['ArrayForm'];
  $Name = $form_state['values']['Name'];

  if ($Name != $ArrayForm['Name'] && SubscriptionFormsThrive::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Subscription form (@type) with the name %name already exists.', array(
      '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_THRIVE]),
      '%name' => $Name,
    )));
  }
}

function klicktipp_list_forms_thrive_edit_form_submit ($form, &$form_state ) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];
  $ArrayForm = $form_state['values']['ArrayForm'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $OptInSubscribeTo = array();
  if (!empty($form_state['values']['OptInSubscribeTo'])) {
    //in case user entered a free value, create new tag, it's 1 tag but pass as array, return value: $OptInSubscribeTo[0] = new/existing TagID
    $OptInSubscribeTo = Tag::CreateManualTagByTagName($UserID, array($form_state['values']['OptInSubscribeTo']));
    if (!$OptInSubscribeTo) {
      //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!UserID' => $UserID,
        '!function' => __FUNCTION__,
        '!BuildID' => $ArrayForm['BuildID'],
        '!OptInSubscribeTo' => $form_state['values']['OptInSubscribeTo'],
      );
      Errors::unexpected("Error: CreateManualTagByTagName with '!OptInSubscribeTo' in !function for User !UserID with BuildID !BuildID", $error, TRUE);
      return;
    }

  }

  $ArrayForm['Name'] = $Name;
  $ArrayForm['AssignTagID'] = (empty($OptInSubscribeTo)) ? 0 : $OptInSubscribeTo[0];
  $ArrayForm['RelListID'] = $form_state['values']['RelListID'];
  $ArrayForm['Settings'] = $form_state['values']['Settings'];
  $ArrayForm['Settings']['UseSpamProtection'] = 1;
  $ArrayForm[DatabaseTableLabeled::LABEL_DATA_KEY] = $MetaLabels;
    if (isset($form_state['values']['DeactivateCaptcha'])) {
        $ArrayForm['DeactivateCaptcha'] = !empty($form_state['values']['DeactivateCaptcha']);
    }

  // --- custom field settings

  //filters out every custom field that is not select (value == 0)
  $selected = array_filter($form_state['values']['CustomFields']['FormFieldsSelected']);

  $ArrayForm['FormFields'] = array();
  foreach ( $selected as $CustomFieldID => $value ) {

    $settings = $form_state['values']['CustomFields']['FormFields'][$CustomFieldID];

    if ( $CustomFieldID == 'EmailAddress' ) {

      // email address label
      $ArrayForm['EmailAddress'] = array(
        'label' => '',
        'weight' => $form_state['values']['CustomFields']['FormFieldWeights']['EmailAddress'],
      );

    }
    elseif ( $CustomFieldID == 'PhoneNumber' ) {

      $ArrayForm['PhoneNumber'] = array(
        'label' => '',
        'required' => (empty($settings['required'])) ? 0 : $settings['required'],
        'hidden' => (empty($settings['hidden'])) ? 0 : $settings['hidden'],
        'weight' => $form_state['values']['CustomFields']['FormFieldWeights'][$CustomFieldID],
        'selected' => 1,
      );

    }
    else {

      $ArrayForm['FormFields'][$CustomFieldID] = array(
        'label' => '',
        'type' => $settings['type'],
        'required' => (empty($settings['required'])) ? 0 : $settings['required'],
        'hidden' => (empty($settings['hidden'])) ? 0 : $settings['hidden'],
        'rows' => 0,
        'default_value' => '',
        'widget' => (empty($settings['widget'])) ? CustomFields::WIDGET_NONE : $settings['widget'],
        'weight' => $form_state['values']['CustomFields']['FormFieldWeights'][$CustomFieldID],
      );

    }

  }

  if ( empty($selected['PhoneNumber']) ) {
    $ArrayForm['PhoneNumber']['selected'] = 0;
  }

  if ( !empty($form_state['values']['CustomFields']['AddCustomFieldID']) ) {

    //a custom field has been selected to be added to the form
    //Note: the custom field id is written to the hidden field by javascript @see: list_forms.inc -> klicktipp_list_forms_customfield_settings_javascript()
    Libraries::include('list_forms.inc');
    klicktipp_list_forms_add_customfield($form_state, $ArrayForm);

    //the field has been added but we don't save the form, the user can still click on cancel
    return;

  }

  // update subscription form
  if (!SubscriptionFormsThrive::UpdateDB($ArrayForm)) {

    $error = array(
      '!UserID' => $UserID,
      '!BuildID' => $ArrayForm['BuildID'],
      '!Name' => $Name,
      '!ArrayForm' => $ArrayForm,
    );
    Errors::unexpected("SubscriptionFormsThrive: Error while updating the Subscription form !Name for User !UserID with BuildID !BuildID.", $error, TRUE);

    return;

  }

  drupal_set_message(t("Please note that you need to include the spam protection code separately."), 'warning');
  drupal_set_message(t("Subscription form %name successfully updated.", array('%name' => $Name)));

}

function klicktipp_list_forms_thrive_delete_confirm_form_submit($form, &$form_state) {

  $ArrayForm = $form_state['values']['Entity'];
  $BuildID = $ArrayForm['BuildID'];
  $UserID = $ArrayForm['RelOwnerUserID'];
  $Name = $ArrayForm['Name'];

  if ( SubscriptionFormsThrive::DeleteDB($ArrayForm) ) {
    drupal_set_message(t("Subscription form %name successfully deleted.", array('%name' => $Name)));
  }
  else {

    $error = array(
      '!UserID' => $UserID,
      '!BuildID' => $BuildID,
      '!Name' => $Name,
    );

    //notify user
    Errors::unexpected("SubscriptionFormsThrive: Delete Subscription form (ID: !BuildID) for user: !UserID with name !Name", $error, TRUE);

  }

  klicktipp_set_redirect($form_state, "listbuilding/$UserID");

}

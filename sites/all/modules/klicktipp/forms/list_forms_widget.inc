<?php

use App\Klicktipp\AccountManager;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\Errors;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\Subscribers;
use App\Klicktipp\SubscriptionForms;
use App\Klicktipp\SubscriptionFormsColors;
use App\Klicktipp\SubscriptionFormsWidget;
use App\Klicktipp\Tag;

function klicktipp_list_forms_widget_create_form ($form, $form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Create Subscription form (@type)', array(
    '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_WIDGET]),
  ));
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Listbuilding'), "listbuilding/$UserID")), $page_title);

  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $weight = 1;

  $form = array();

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#size' => 60,
    '#default_value' => '',
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? [] : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
      'container_class' => 'klicktipp-magicselect-short',
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  // buttons

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Create Subscription form'),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "listbuilding/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_list_forms_widget_create_form_validate ($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];

  if (SubscriptionFormsWidget::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Subscription form (@type) with the name %name already exists.', array(
      '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_WIDGET]),
      '%name' => $Name,
    )));
  }

}

function klicktipp_list_forms_widget_create_form_submit ($form, &$form_state) {

  $Name = $form_state['values']['Name'];
  $UserID = $form_state['values']['uid'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $BuildID = SubscriptionFormsWidget::InsertDB(array(
    'Name' => $Name,
    'RelOwnerUserID' => $UserID,
    'Settings' => [
      'UseSpamProtection' => 1,
    ],
    DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
  ));

  if (!empty($BuildID)) {

    drupal_set_message(t("Subscription form %name successfully created.", array('%name' => $Name)));

    // redirect to countdown edit
    klicktipp_set_redirect($form_state, "listbuilding/$UserID/form-widget/$BuildID/edit");

  }
  else {

    $error = array(
      '!uid' => $UserID,
      '!name' => $Name,
    );

    //notify user
    Errors::unexpected("SubscriptionFormWidget: Create for UserID: !uid with name !name", $error, TRUE);

  }

}

function klicktipp_list_forms_widget_edit_form ($form, &$form_state, $account, $BuildID) {

  $UserID = $account->uid;

  /** @var SubscriptionFormsWidget $ObjectForm */
  if ( !empty($form_state['storage']['ObjectForm']) ) {
    //a custom field has been added to the form and there may be other changes, do not load the form from the database
    //@see: submit and includes/list_forms.inc -> klicktipp_list_forms_add_customfield()
    $ObjectForm = $form_state['storage']['ObjectForm'];
  }
  else {
    $ObjectForm = SubscriptionFormsWidget::FromID($UserID, $BuildID);
  }

  if ( empty($ObjectForm) ) {

    drupal_set_message(t("Subscription form not found."), 'error');
    drupal_goto("listbuilding/$UserID");
    return NULL;

  }

  $page_title = t('Edit Subscription form');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(
    l(t('Listbuilding'), "listbuilding/$UserID"),
  ), $page_title);

  $ArrayForm = $ObjectForm->GetData();

  $Labels = $ObjectForm->GetMetaLabels();
  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $form = array();

  $form['#pre_render'][] = 'klicktipp_list_forms_widget_edit_form_pre_render';

  $weight = 1;

  //retrieve lists of user
  $ListsOptions = Lists::RetrieveListsAsOptionsArray($UserID);

  //retrieve tags of user
  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['ArrayForm'] = array(
    '#type' => 'value',
    '#value' => $ArrayForm,
  );

  $form['Name'] = array(
    '#type' => 'textfield',
    '#required' => TRUE,
    '#weight' => $weight++,
    '#title' => t('Name'),
    '#default_value' => $ArrayForm['Name'],
  );

  $form['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? $Labels : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  $linkText = t('Listbuilding::form::Edit Double-Opt-In');
  $form['RelListID'] = array(
    '#type' => 'select',
    '#default_value' => $ArrayForm['RelListID'],
    '#title' => t("Subscription process"),
    '#weight' => $weight++,
    '#options' => $ListsOptions,
    '#suffix' => sprintf('<a href="#" id="doi-link">%s</a>', $linkText),
    '#attributes' => array('class' => array('doi-select')),
  );

  $form['OptInSubscribeTo'] = array(
    '#type' => 'textfield',
    '#default_value' => $ArrayForm['AssignTagID'],
    '#title' => t('Additional tagging (optional)'),
    '#weight' => $weight++,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => FALSE,
      'free_entries' => TRUE,
      'free_entries_message' => t('The tag will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => $form_state['input']['OptInSubscribeTo'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'additional-tagging',
  );

  $ajax = array(
    'callback' => 'klicktipp_form_ajax_callback',
    'wrapper' => 'formbuilder-ajax',
    'method' => 'replace',
  );

  Libraries::include('list_forms.inc');

  $form['CustomFields'] = klicktipp_list_forms_customfield_settings_table($ArrayForm, $weight++, $ajax, array(CustomFields::TYPE_HTML));

  $form['Preview'] = array(
    '#type' => 'klicktipp_block',
    '#title' => t('Preview'),
    '#weight' => $weight++,
    '#attributes' => array(
      'class' => array('preview-box', 'preview-box-center', 'preview-desktop'),
    ),
  );

  $form['Preview']['Content'] = array(
    '#type' => 'markup',
    '#markup' => $ObjectForm->GetWidget(TRUE),
    '#weight' => $weight++,
  );

  $form['Settings'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('settings-box')),
  );

  $form['Settings']['Responsive'] = array(
    '#type' => 'klicktipp_grid_row',
    '#weight' => $weight++,
  );

  $form['Settings']['Responsive']['Left'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('col-md-6')),
  );

  $form['Settings']['Responsive']['Left']['Position'] = array(
    '#type' => 'radios',
    '#title' => t('Widget position'),
    '#default_value' => (empty($ArrayForm['Settings']['Position'])) ? SubscriptionFormsWidget::POSITION_BOTTOM_RIGHT : $ArrayForm['Settings']['Position'],
    '#options' => array(
      SubscriptionFormsWidget::POSITION_BOTTOM_RIGHT => '<i class="icon-widget-bottom-right has-tooltip" data-tooltip-title="' . t('Bottom right') . '"></i>',
      SubscriptionFormsWidget::POSITION_BOTTOM_LEFT => '<i class="icon-widget-bottom-left has-tooltip" data-tooltip-title="' . t('Bottom left') . '"></i>',
      SubscriptionFormsWidget::POSITION_RIGHT => '<i class="icon-widget-right has-tooltip" data-tooltip-title="' . t('Right') . '"></i>',
      SubscriptionFormsWidget::POSITION_LEFT => '<i class="icon-widget-left has-tooltip" data-tooltip-title="' . t('Left') . '"></i>',
    ),
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Position'),
  );

  $form['Settings']['Responsive']['Left']['Delay'] = array(
    '#type' => 'select',
    '#title' => t('Open widget automatically'),
    '#options' => array(
      0 => t('Do not open widget automatically'),
      1 => t('Open widget immediately'),
      5 => t('Open widget after !x seconds', array('!x' => 5)),
      10 => t('Open widget after !x seconds', array('!x' => 10)),
      15 => t('Open widget after !x seconds', array('!x' => 15)),
      30 => t('Open widget after !x seconds', array('!x' => 30)),
      45 => t('Open widget after !x seconds', array('!x' => 45)),
      60 => t('Open widget after !x seconds', array('!x' => 60)),
      90 => t('Open widget after !x seconds', array('!x' => 90)),
      120 => t('Open widget after !x seconds', array('!x' => 120)),
    ),
    '#default_value' => (empty($ArrayForm['Settings']['Delay'])) ? 0 : $ArrayForm['Settings']['Delay'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Delay'),
  );

  $form['Settings']['Responsive']['Left']['Inline'] = array(
    '#type' => 'klicktipp_grid_row',
    '#weight' => $weight++,
  );

  $form['Settings']['Responsive']['Left']['Inline']['Left'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('col-md-5')),
  );

  $form['Settings']['Responsive']['Left']['Inline']['Left']['ColorsWidget'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Widget color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['normal'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'normal'),
  );

  $form['Settings']['Responsive']['Left']['Inline']['Right'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('col-md-5')),
  );

  $form['Settings']['Responsive']['Left']['Inline']['Right']['ColorsWidgetText'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Widget text color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['bright'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'bright'),
  );

  $form['Settings']['Responsive']['Left']['Headline'] = array(
    '#type' => 'textfield',
    '#title' => t('Headline'),
    '#default_value' => (empty($ArrayForm['Settings']['Headline'])) ? t('My headline') : $ArrayForm['Settings']['Headline'],
    '#weight' => $weight++,
    '#maxlength' => 512,
    '#required' => TRUE,
    '#attributes' => array(
      'class' => array('input-medium-large'),
    ),
    '#parents' => array('Settings', 'Headline'),
  );

  $form['Settings']['Responsive']['Left']['ColorsHeadline'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Headline color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['dark'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'dark'),
  );

  $form['Settings']['Responsive']['Left']['Intro'] = array(
    '#type' => 'textfield',
    '#title' => t('Intro'),
    '#default_value' => (empty($ArrayForm['Settings']['Intro'])) ? '' : $ArrayForm['Settings']['Intro'],
    '#weight' => $weight++,
    '#maxlength' => 512,
    '#attributes' => array(
      'class' => array('input-medium-large'),
      'placeholder' => t('My intro'),
    ),
    '#parents' => array('Settings', 'Intro'),
  );

  $form['Settings']['Responsive']['Left']['DataProtectionLinkURL'] = array(
    '#type' => 'textfield',
    '#title' => t('Data Protection Link URL'),
    '#default_value' => (empty($ArrayForm['Settings']['DataProtectionLinkURL'])) ? '' : $ArrayForm['Settings']['DataProtectionLinkURL'],
    '#weight' => $weight++,
    '#maxlength' => 800,
    '#quickhelp' => 'subscription-forms-dataprotectionlinkurl',
    '#attributes' => array(
      'class' => array('input-medium-large'),
      'placeholder' => t('https://'),
    ),
    '#parents' => array('Settings', 'DataProtectionLinkURL'),
    '#element_validate' => array('klicktipp_element_textfield_validate_url'),
  );

  $form['Settings']['Responsive']['Left']['DataProtectionLinkText'] = array(
    '#type' => 'textfield',
    '#title' => t('Data Protection Link Text'),
    '#default_value' => (empty($ArrayForm['Settings']['DataProtectionLinkText'])) ? '' : $ArrayForm['Settings']['DataProtectionLinkText'],
    '#weight' => $weight++,
    '#maxlength' => 512,
    '#attributes' => array(
      'class' => array('input-medium-large'),
      'placeholder' => t("Data Protection"),
    ),
    '#parents' => array('Settings', 'DataProtectionLinkText'),
  );

  $form['Settings']['Responsive']['Left']['Inline2'] = array(
    '#type' => 'klicktipp_grid_row',
    '#weight' => $weight++,
  );

  $form['Settings']['Responsive']['Left']['Inline2']['Left'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('col-md-5')),
  );

  $form['Settings']['Responsive']['Left']['Inline2']['Left']['ColorsBase'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Background color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['base'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'base'),
  );

  $form['Settings']['Responsive']['Left']['Inline2']['Right'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('col-md-5')),
  );

  //text color for chicklet and intro
  $form['Settings']['Responsive']['Left']['Inline2']['Right']['ColorsText'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Text color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['chicklet'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'chicklet'),
  );

  $form['Settings']['Responsive']['Left']['DisplayChicklet'] = array(
    '#type' => 'checkbox',
    '#title' => t('Display chicklet'),
    '#default_value' => $ArrayForm['Settings']['DisplayChicklet'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'DisplayChicklet'),
    '#attributes' => array(
      'data-event-load' => 'js_formbuilder_display_chicklet',
      'data-event-change' => 'js_formbuilder_display_chicklet',
    ),
  );

  $form['Settings']['Responsive']['Left']['ChickletText'] = array(
    '#type' => 'textfield',
    '#title' => t('Chicklet text'),
    '#default_value' => (empty($ArrayForm['Settings']['ChickletText'])) ? t('%chicklet% Entries') : $ArrayForm['Settings']['ChickletText'],
    '#weight' => $weight++,
    '#description' => t('Enter the chicklet text. Use %chicklet% to display the count.'),
    '#attributes' => array(
      'class' => array('input-medium-large'),
    ),
    '#parents' => array('Settings', 'ChickletText'),
  );

  $form['Settings']['Responsive']['Left']['AffiliateID'] = klicktipp_form_digistore_affiliate_checkbox($account, $ArrayForm['Settings']['AffiliateID'], $weight++, $ajax);
  $form['Settings']['Responsive']['Left']['AffiliateID']['#parents'] = array('Settings', 'AffiliateID');

  $multidevicetracking = TRUE;
  if ( !klicktipp_feature_access($account, 'access digistore multi device tracking') ) {
    $multidevicetracking = FALSE;
    //quickhelp shows an upsell
    $quickhelp = theme('klicktipp_quickhelp', array(
      'element' => array(
        '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-upsell',
        '#title' => t("Use Digistore24 Multi-Device-Tracking"),
      ),
    ));
  }
  elseif( empty($account->UserSettings['DigiStore24ApiKeys']) ) {
    $multidevicetracking = FALSE;
    //quickhelp shows how to connect DigiStore24 accounts
    $quickhelp = theme('klicktipp_quickhelp', array(
      'element' => array(
        '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-no-accounts',
        '#title' => t("Use Digistore24 Multi-Device-Tracking"),
      ),
    ));
  }
  else {
    //quickhelp shows info about multi-device-tracking (the user does not have to do anything, unlike OptimizePress etc.)
    $quickhelp = theme('klicktipp_quickhelp', array(
      'element' => array(
        '#quickhelp' => 'listbuilding-edit-form-use-digistore-multidevicetracking-generell',
        '#title' => t("Use Digistore24 Multi-Device-Tracking"),
      ),
    ));
  }

  $form['Settings']['Responsive']['Left']['UseDigistoreMultiDeviceTracking'] = array(
    '#type' => 'checkbox',
    '#title' => t("Use Digistore24 Multi-Device-Tracking") . $quickhelp,
    '#default_value' => empty($ArrayForm['Settings']['UseDigistoreMultiDeviceTracking']) ? 0 : 1,
    '#return_value' => 1,
    '#parents' => array('Settings', 'UseDigistoreMultiDeviceTracking'),
    '#disabled' => !$multidevicetracking,
    '#weight' => $weight++,
  );

  $form['Settings']['Responsive']['Left']['Width'] = array(
    '#type' => 'value',
    '#value' => $ArrayForm['Settings']['Width'],
    '#parents' => array('Settings', 'Width'),
  );

  $form['Settings']['Responsive']['Left']['Height'] = array(
    '#type' => 'hidden',
    '#default_value' => $ArrayForm['Settings']['Height'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Height'),
  );

  $form['Settings']['Responsive']['Left']['WidgetHeight'] = array(
    '#type' => 'hidden',
    '#default_value' => $ArrayForm['Settings']['WidgetHeight'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'WidgetHeight'),
  );

  $form['Settings']['Responsive']['Right'] = array(
    '#type' => 'klicktipp_block',
    '#weight' => $weight++,
    '#attributes' => array('class' => array('col-md-6')),
  );

  $form['Settings']['Responsive']['Right']['ButtonType'] = array(
    '#type' => 'select',
    '#title' => t('Button type'),
    '#default_value' => $ArrayForm['Settings']['Button']['button'],
    '#options' => SubscriptionForms::GetButtonsAsOptionsArray(SubscriptionForms::BUTTON_CATEGORY_FORM),
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#attributes' => array(
      'data-event-load' => 'js_formbuilder_button_type',
      'data-event-change' => 'js_formbuilder_button_type',
    ),
    '#parents' => array('Settings', 'Button', 'button'),
  );

  $form['Settings']['Responsive']['Right']['ButtonColorImage'] = array(
    '#type' => 'select',
    '#title' => t('Button color'),
    '#default_value' => $ArrayForm['Settings']['Button']['color'],
    '#options' => array(
      SubscriptionForms::COLOR_ORANGE => t('color: orange'),
      SubscriptionForms::COLOR_BLUE => t('color: blue'),
      SubscriptionForms::COLOR_VIOLET => t('color: violet'),
      SubscriptionForms::COLOR_LIGHTGREEN => t('color: light green'),
      SubscriptionForms::COLOR_GREEN => t('color: green'),
      SubscriptionForms::COLOR_TURQUOISE => t('color: turquoise'),
      SubscriptionForms::COLOR_RED => t('color: red'),
      SubscriptionForms::COLOR_GREY => t('color: grey'),
    ),
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'Button', 'color'),
  );

  $form['Settings']['Responsive']['Right']['ButtonColor'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Button color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['button'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'button'),
  );

  $form['Settings']['Responsive']['Right']['ColorsButtonText'] = array(
    '#type' => 'textfield',
    '#theme' => 'klicktipp_colorpicker',
    '#title' => t('Button text color'),
    '#default_value' => $ArrayForm['Settings']['Colors']['button_text'],
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Colors', 'button_text'),
  );

  $form['Settings']['Responsive']['Right']['ButtonSwoosh'] = array(
    '#type' => 'checkbox',
    '#title' => t('Button Swoosh'),
    '#default_value' => $ArrayForm['Settings']['Button']['swoosh'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'Button', 'swoosh'),
  );

  $form['Settings']['Responsive']['Right']['ButtonAlign'] = array(
    '#type' => 'radios',
    '#title' => t('Button align'),
    '#default_value' => $ArrayForm['Settings']['Button']['align'],
    '#options' => array(
      SubscriptionForms::ALIGN_CENTER => t('Align center'),
      SubscriptionForms::ALIGN_LEFT => t('Align left'),
    ),
    '#weight' => $weight++,
    '#parents' => array('Settings', 'Button', 'align'),
  );

  $form['Settings']['Responsive']['Right']['ButtonText'] = array(
    '#type' => 'textfield',
    '#title' => t('Button text'),
    '#default_value' => (empty($ArrayForm['Settings']['Button']['text'])) ? t('Your button text') : $ArrayForm['Settings']['Button']['text'],
    '#weight' => $weight++,
    '#attributes' => array(
      'class' => array('input-medium'),
    ),
    '#parents' => array('Settings', 'Button', 'text'),
  );

  $form['Settings']['DisplayDSGVOCheckbox'] = array(
    '#type' => 'checkbox',
    '#title' => t('Display DSGVO checkbox'),
    '#default_value' => $ArrayForm['Settings']['DisplayDSGVOCheckbox'],
    '#return_value' => 1,
    '#weight' => $weight++,
    '#ajax' => $ajax,
    '#parents' => array('Settings', 'DisplayDSGVOCheckbox'),
    '#attributes' => array(
      'data-event-load' => 'js_formbuilder_display_dsgvocheckbox',
      'data-event-change' => 'js_formbuilder_display_dsgvocheckbox',
    ),
  );

  $form['Settings']['DSGVOText'] = array(
    '#type' => 'textarea',
    '#default_value' => (empty($ArrayForm['Settings']['DSGVOText'])) ? variable_get('listbuilding_htmldsgvotext_default', '') : $ArrayForm['Settings']['DSGVOText'],
    '#weight' => $weight++,
    '#resizable' => FALSE,
    '#parents' => array('Settings', 'DSGVOText'),
    '#attributes' => array(
      'class' => array('cke-textarea personalized jquery_ckeditor content-html'),
      'data-event-load' => 'js_init_ckeditor_personalization',
    ),
  );

    if (AccountManager::canDeactivateCaptcha($account)) {
        $form['Settings']['DeactivateCaptcha'] = array(
            '#type' => 'checkbox',
            '#title' => t("Captcha::FormEdit::Checkbox::I want to deactivate the captcha for this form"),
            '#default_value' => empty($ArrayForm['DeactivateCaptcha']) ? 0 : 1,
            '#return_value' => 1,
            '#weight' => $weight++,
            '#attributes' => array(
                'data-event-load' => 'js_checkbox_toggle_element_display',
                'data-event-load-args' => '#captcha-warning',
                'data-event-change' => 'js_checkbox_toggle_element_display',
                'data-event-change-args' => '#captcha-warning',
            )
        );

        $captchaWarning = t('Captcha::FormEdit::Warning::It is not recommended to deactivate the captcha protection.');
        $form['Settings']['DeactivateCaptchaWarning'] = array(
            '#type' => 'markup',
            '#markup' => '<div id="captcha-warning" class="alert alert-warning">' . $captchaWarning . '</div>',
            '#weight' => $weight++,
        );
    }

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => 10000,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['Save'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Save Subscription form'),
    '#weight' => $weight++,
  );

  $EmbedCode = $ObjectForm->GetEmbedCode();
  unset($EmbedCode['iframe']);

  Libraries::include('list_forms.inc');
  $form['buttons']['EmbedCode'] = klicktipp_list_forms_embed_code_modal("embed-code-$BuildID", $EmbedCode, $weight++);

  $ModalID = 'modalDeleteConfirmAjax';
  $Title = t('Delete Subscription form');

  $modalFormParameters = [
    "modalTitle" => $Title,
    "modalId" => $ModalID,
    "dependencyMessage" => t('This subscription form is used in the following objects and cannot be deleted:'),
    "entityId" => $BuildID,
    "entityClass" => SubscriptionFormsWidget::class,
    "entityName" => $ArrayForm['Name'],
    "entity" => $ArrayForm,
    "submitFunction" => "klicktipp_list_forms_widget_delete_confirm_form_submit",
  ];

  // will include the generic modal form "klicktipp_delete_modal_form"
  Libraries::include("form_delete_modal.inc");

  $form['buttons']['ModalDeleteConfirmTrigger'] = [
    '#theme' => 'klicktipp_delete_modal_ajax_button',
    '#title' => $Title,
    '#value' => $ModalID,
    '#weight' => $weight++,
    '#modal_confirm' => drupal_get_form("klicktipp_delete_modal_form", $account, $modalFormParameters),
  ];

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "listbuilding/$UserID",
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_list_forms_widget_edit_form_pre_render ($form) {
  global $user;
  global $base_url;
  global $language;
  $path_to_CKF = "$base_url/ckfinder/";
  $lang = substr($language->language, 0, 2);

  drupal_add_js('

  window["content-html"] = 0;

  window["cke_persofocus"] = function(element, args) {
    $(".persofocus").removeClass("persofocus");
    $(args).addClass("persofocus");
    window["js_insert_placeholder"](element, args);
  }

  $(document).ready(function () {

    // Initialize the editor.
    var config = {
      toolbar:
      [
          ["Cut","Copy","Paste","PasteText","PasteFromWord"],
          ["Undo","Redo","-","SelectAll","RemoveFormat"],
          "/",
          ["Bold","Italic","Underline","Strike","-","Subscript","Superscript"],
          ["NumberedList","BulletedList","-","Outdent","Indent","Blockquote","CreateDiv"],
          ["JustifyLeft","JustifyCenter","JustifyRight","JustifyBlock"],
          ["Link","Unlink"],
          "/",
          ["Image","Table","HorizontalRule","SpecialChar"],
          ["Styles","Format","Font","FontSize"],
          ["TextColor","BGColor"],
          ["Maximize", "ShowBlocks"]
      ],
      enterMode : CKEDITOR.ENTER_BR,
      language: "' . $lang . '",
      height: "400px",
      resize_enabled : false,
      allowedContent: true,
      forceSimpleAmpersand: true,
      filebrowserUploadUrl : null, //disable upload tab
      filebrowserImageUploadUrl : null, //disable upload tab
      filebrowserFlashUploadUrl : null, //disable upload tab
      filebrowserBrowseUrl : "' . $path_to_CKF . 'ckfinder.html",
      filebrowserImageBrowseUrl : "' . $path_to_CKF . 'ckfinder.html",
      filebrowserFlashBrowseUrl: "' . $path_to_CKF . 'ckfinder.html",
    };

    try {
      $(".edit-Settings-DSGVOText").ckeditor(config);
      window["content-html"] = $(".edit-Settings-DSGVOText").ckeditorGet();

      //DataProcessor

      CKEDITOR.on( "instanceReady", function( ev ) {
        ckeditor = ev.editor;
        ckeditor.on("blur", function() {
          $("label[for=DSGVOCheckbox]").html(ckeditor.getData());
        });
        ckeditor.dataProcessor.htmlFilter.addRules({
          elements : {
            p : function(element) {
            //remove p-Tags containing just a &nbsp;
            var content = element.children[0].value;
            if (content && content.charCodeAt(0) == 160) {
              return false;
            }
          }
        }
      });

      });

    } catch (e) {
    alert(e);
    };

  });
  ', array('type' => 'inline', 'scope' => 'footer'));

  $UserID = $form['uid']['#value'];
  $ArrayForm = $form['ArrayForm']['#value'];

  $ChickletCount =  Subscribers::GetActiveTotal($UserID);

  $defaultDpoText = t('Data Protection');

  $script = "

    window['ChickletCount'] = $ChickletCount;
    window['PreviewWidgetTimer'] = 0;

    $(document).ready(function () {

      function updateDoiLink(id) {
          const linkElement = document.getElementById('doi-link');
          linkElement.innerHTML = '" . t('Listbuilding::form::Edit Double-Opt-In') . "';
          linkElement.href = '/app/double-opt-in/settings/$user->uid/' + id +'/edit';
      }

      window['js_formbuilder_update']();

      $('.edit-Settings-ChickletText').keyup(function() {

        var text = $(this).val();
        text = text.replace(/%chicklet%/g, '<span class=\"ktchicklet\">' + window['ChickletCount'] + '</span>');
        $('.ktv2-form-chicklet').html(text);

        window['js_formbuilder_update'](1);

      });

      $('#edit-settings-dataprotectionlinktext, #edit-settings-dataprotectionlinkurl').keyup(function() {
        window['js_formbuilder_update'](1);
      });

      $('.edit-Settings-Headline').keyup(function() {

        var text = $(this).val();
        $('.ktv2-widget-headline').text(text);
        $('.ktv2-widget-button-text').text(text);

        //the text of the widget button is changed, make sure the widget button is visible
        $('.ktv2-widget').addClass('preview-widget');

        //hide the widget after 5 seconds of the last key stroke
        window.clearTimeout(window['PreviewWidgetTimer']);
        window['PreviewWidgetTimer'] = window.setTimeout('ColorpickerChange()', 5000);

        window['js_formbuilder_update'](1);

      });

      $('.edit-Settings-Intro').keyup(function() {

        var text = $(this).val();
        $('.ktv2-widget-intro').text(text);

        window['js_formbuilder_update'](1);

      });

      $('.edit-Settings-Button-text').keyup(function() {
        if ( $(this).val() == '' ) {
          $('.ktv2-submit-element-button.button-text').val('" . t('Your button text') . "');
        }
        else {
          $('.ktv2-submit-element-button.button-text').val($(this).val());
        }
        window['js_formbuilder_update'](1);
      });

      $('.edit-Settings-Position input').click(function () {
        $('.ktv2-widget').removeClass('widget-left widget-right widget-bottom-right widget-bottom-left');
        $('.ktv2-widget').addClass($(this).val());
        window['js_formbuilder_update'](1);
      });

      $('.edit-Settings-Button-align input').click(function () {
        $('.ktv2-submit-element').css('text-align', $(this).val());
        $('.ktv2-form-chicklet').css('text-align', $(this).val());
      });

      $('#DSGVOCheckbox').on('change', function(e) {
        if ($(e.target).prop('checked')) {
          $('.ktv2-submit-element-button').attr('disabled', false);
        }
        else {
          $('.ktv2-submit-element-button').attr('disabled', true);
        }
      });

      $('.doi-select').on('change', function(e) {
        updateDoiLink($(this).val());
      });

      const select = $('#edit-rellistid-wrapper select');

      // Fix drupal styles to allow link next to dropdown.
      $('#edit-rellistid-wrapper').css('display', 'inline-block');
      select.css('max-width', 'unset');

      updateDoiLink(select.val())

    });

    // --- color picker ---

    window['ColorpickerMove'] = function (button, textfield, color) {

      if ( textfield.attr('id') == 'edit-settings-colors-base' ) {

        var base_hex = color.toHexString();

        $('.ktv2-form').css({'background-color': base_hex});

      }
      else if ( textfield.attr('id') == 'edit-settings-colors-normal' ) {

        //the color of the widget button is changed, make sure the widget button is visible
        $('.ktv2-widget').addClass('preview-widget');

        var base_hex = color.toHexString();

        $('.ktv2-widget-body, .ktv2-widget-button').css({'background-color': base_hex});

      }
      else if ( textfield.attr('id') == 'edit-settings-colors-button' ) {
        var hex = color.toHexString();
        $('.ktv2-submit-element-bg.button-text').css({'background-color': hex});
        $('.style-flat .ktv2-submit-element-button.button-text').css({'border-color': color.darken().toHexString()});
        $('.style-gradient .ktv2-submit-element-button.button-text').css({'border-color': 'rgba(0,0,0,0.2)'});
        $('.edit-Settings-Colors-button').val(hex);
      }
      else if ( textfield.attr('id') == 'edit-settings-colors-bright') {
        //widget button text color, affiliate link text color

        //the text color of the widget button is changed, make sure the widget button is visible
        $('.ktv2-widget').addClass('preview-widget');

        var hex = color.toHexString();
        $('.ktv2-widget-button-text, .ktv2-widget-afflink').css({'color': hex });
        $('.edit-Settings-Colors-bright').val(hex);
      }
      else if ( textfield.attr('id') == 'edit-settings-colors-dark') {
        //widget headline color

        var hex = color.toHexString();
        $('.ktv2-widget-headline').css({'color': hex});
        $('.edit-Settings-Colors-dark').val(hex);
      }
      else if ( textfield.attr('id') == 'edit-settings-colors-button-text' ) {
        $('.ktv2-submit-element-button.button-text').css({'color': color.toHexString()});
        $('.edit-Settings-Colors-button-text').val(color.toHexString());
      }
      else if ( textfield.attr('id') == 'edit-settings-colors-chicklet' ) {
        var hex = color.toHexString();
        $('.ktv2-form-chicklet, .ktv2-widget-closer, .ktv2-widget-intro').css({'color': hex});
        $('.edit-Settings-Colors-chicklet').val(hex);
      }

    }

    window['ColorpickerChange'] = function (button, textfield, color) {
      //the text color of the widget button was set, hide the widget button
      $('.ktv2-widget').removeClass('preview-widget');
    }

    window['js_formbuilder_update'] = function (hasChanges) {

      var preview = $('#formbuilder-preview');
      var widget = $('.ktv2-widget-body');

      if ( preview.length > 0 ) {

        //check if we have a dpo link
        var dpo_link = $('#edit-settings-dataprotectionlinkurl').val();
        var dpo_text = $('#edit-settings-dataprotectionlinktext').val();

        if (dpo_link == '' && dpo_text == '') {
          //hide dpo in subscription form
          $('.ktv2-widget-dpo').hide();
        }
        else {
          dpo_text = (dpo_text == '') ? '$defaultDpoText' : dpo_text;
          $('.ktv2-widget-dpo a').attr('href', dpo_link);
          $('.ktv2-widget-dpo a').html(dpo_text);
          $('.ktv2-widget-dpo').show();
        }

        var height = preview.outerHeight();
        $('.edit-Settings-Height').val(height);

        var widgetheight = widget.outerHeight();
        $('.edit-Settings-WidgetHeight').val(widgetheight);
        $('.ktv2-widget.widget-left, .ktv2-widget.widget-right').css('margin-top', Math.floor(widgetheight/2) * -1);
        $('.ktv2-widget.widget-left .ktv2-widget-button, .ktv2-widget.widget-right .ktv2-widget-button').css('height', widgetheight);
        $('.ktv2-widget.widget-bottom-left .ktv2-widget-button, .ktv2-widget.widget-bottom-right .ktv2-widget-button').css('height', '');

        var css = '@keyframes slide-out {' +
        '  0%   {bottom: -' + widgetheight + 'px;}' +
        '  100% {bottom: 0px;}' +
        '} ' +
        '@keyframes slide-in {' +
        '  0%   {bottom: 0px;}' +
        '  100% {bottom: -' + widgetheight + 'px;}' +
        '} ' +
        ' .ktv2-widget.widget-bottom-left.ktv2-open-form.ktv2-close-form, .ktv2-widget.widget-bottom-right.ktv2-open-form.ktv2-close-form {bottom: -' + widgetheight + 'px}';

        $('.preview-box').height(widgetheight+50);

        var animation = $('<style></style>').text(css);

        preview.append(animation);

        ktv2OpenForm('{$ArrayForm["BuildID"]}');

      }

      if ( hasChanges == 1 ) {

        var tooltip = $('<div></div>').css('display', 'inline-block');

        //deactivate embed code button after change the checkbox state
        $('#edit-embedcode').attr({
          'data-toggle': '',
          'data-modal-id': '',
          'disabled': 'disabled'
        }).wrap(tooltip);

        $('#edit-embedcode').parent().tooltip({
          'title': '" . t('Please save the Subscription form first.') . "',
          'container': 'body'
        });

      }

      window['update_view']();

    }

    window['js_formbuilder_button_type'] = function (element, args) {

      var value = element.val();

      if ( value == 'text' ) {
        $('.edit-Settings-Button-swoosh-wrapper').hide();
        $('.edit-Settings-Button-color-wrapper').hide();
        $('.edit-Settings-Button-text-wrapper').show();
        $('.edit-Settings-Colors-button-wrapper').show();
        $('.edit-Settings-Colors-button-text-wrapper').show();

      }
      else {
        $('.edit-Settings-Button-swoosh-wrapper').show();
        $('.edit-Settings-Button-color-wrapper').show();
        $('.edit-Settings-Button-text-wrapper').hide();
        $('.edit-Settings-Colors-button-wrapper').hide();
        $('.edit-Settings-Colors-button-text-wrapper').hide();
      }

      window['update_view']();

    }

    window['js_formbuilder_display_chicklet'] = function (element, args) {

      if ( element.prop('checked') ) {
        $('.edit-Settings-ChickletText-wrapper').show();
      }
      else {
        $('.edit-Settings-ChickletText-wrapper').hide();
      }

      window['update_view']();

    }

    window['js_formbuilder_display_dsgvocheckbox'] = function (element, args) {
      if (element.prop('checked')) {
        $('#edit-settings-dsgvotext-wrapper').show();
        $('.ktv2-submit-element-button').attr('disabled', true);
      }
      else {
        $('#edit-settings-dsgvotext-wrapper').hide();
      }
      window['update_view']();
    }

  ";

  $script .= SubscriptionFormsColors::GetColorSchemeJavaScript();

  Libraries::include('list_forms.inc');
  $script .= klicktipp_list_forms_customfield_settings_javascript();

  drupal_add_js($script, array('type' => 'inline', 'scope' => 'header'));

  return $form;

}

function klicktipp_list_forms_widget_edit_form_validate ($form, &$form_state ) {

  $UserID = $form_state['values']['uid'];
  $ArrayForm = $form_state['values']['ArrayForm'];
  $Name = $form_state['values']['Name'];
  $Settings = $form_state['values']['Settings'];

  if ($Name != $ArrayForm['Name'] && SubscriptionFormsWidget::CheckDuplicateName($UserID, $Name)) {
    form_set_error('Name', t('A Subscription form (@type) with the name %name already exists.', array(
      '@type' => t(/*ignore*/Listbuildings::$DisplayListbuildingType[Listbuildings::TYPE_FORM_WIDGET]),
      '%name' => $Name,
    )));
  }

  if (!empty($Settings['DisplayDSGVOCheckbox']) && empty($Settings['DSGVOText'])) {
    form_set_error('DSGVOText', t('The DSGVO text cannot be empty.'));
  }

  if ( $Settings['DisplayChicklet'] && strpos($Settings['ChickletText'], '%chicklet%') === FALSE ) {
    form_set_error('ChickletText', t('Please use the placeholder %chicklet% in your Chicklet text to display your active contact count.'));
  }

  if ( !empty($Settings['DataProtectionLinkText']) && empty($Settings['DataProtectionLinkURL']) ) {
    form_set_error('DataProtectionLinkURL', t('You have specified a Data Protection Link Text but no Data Protection Link URL.'));
  }

  $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['base']));
  if ( strlen($color) != 7 ) {
    form_set_error('Settings][Colors][base', t('Invalid hex color code.'));
  }

  $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['normal']));
  if ( strlen($color) != 7 ) {
    form_set_error('Settings][Colors][normal', t('Invalid hex color code.'));
  }

  $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['dark']));
  if ( strlen($color) != 7 ) {
    form_set_error('Settings][Colors][dark', t('Invalid hex color code.'));
  }

  $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['bright']));
  if ( strlen($color) != 7 ) {
    form_set_error('Settings][Colors][bright', t('Invalid hex color code.'));
  }

  $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['chicklet']));
  if ( strlen($color) != 7 ) {
    form_set_error('Settings][Colors][chicklet', t('Invalid hex color code.'));
  }

  if ( $Settings['Button']['button'] == 'text' ) {

    $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['button']));
    if ( strlen($color) != 7 ) {
      form_set_error('Settings][Colors][button', t('Invalid hex color code.'));
    }

    $color = '#' . preg_replace('/[^0-9a-f]/', '', strtolower($Settings['Colors']['button_text']));
    if ( strlen($color) != 7 ) {
      form_set_error('Settings][Colors][button_text', t('Invalid hex color code.'));
    }

  }

}

function klicktipp_list_forms_widget_edit_form_submit ($form, &$form_state ) {

  $UserID = $form_state['values']['uid'];
  $Name = $form_state['values']['Name'];
  $ArrayForm = $form_state['values']['ArrayForm'];
  $MetaLabels = (empty($form_state['values']['MetaLabels'])) ?  array() : array_keys($form_state['values']['MetaLabels']);

  $OptInSubscribeTo = array();
  if (!empty($form_state['values']['OptInSubscribeTo'])) {
    //in case user entered a free value, create new tag, it's 1 tag but pass as array, return value: $OptInSubscribeTo[0] = new/existing TagID
    $OptInSubscribeTo = Tag::CreateManualTagByTagName($UserID, array($form_state['values']['OptInSubscribeTo']));
    if (!$OptInSubscribeTo) {
      //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!UserID' => $UserID,
        '!function' => __FUNCTION__,
        '!BuildID' => $ArrayForm['BuildID'],
        '!OptInSubscribeTo' => $form_state['values']['OptInSubscribeTo'],
      );
      Errors::unexpected("Error: CreateManualTagByTagName with '!OptInSubscribeTo' in !function for User !UserID with BuildID !BuildID", $error, TRUE);
      return;
    }

  }

  $ArrayForm['Name'] = $Name;
  $ArrayForm['AssignTagID'] = (empty($OptInSubscribeTo)) ? 0 : $OptInSubscribeTo[0];
  $ArrayForm['RelListID'] = $form_state['values']['RelListID'];
  $ArrayForm['Settings'] = $form_state['values']['Settings'];
  $ArrayForm['Settings']['UseSpamProtection'] = 1;
  $ArrayForm[DatabaseTableLabeled::LABEL_DATA_KEY] = $MetaLabels;
    if (isset($form_state['values']['DeactivateCaptcha'])) {
        $ArrayForm['DeactivateCaptcha'] = !empty($form_state['values']['DeactivateCaptcha']);
    }

  // --- custom field settings

  //filters out every custom field that is not select (value == 0)
  $selected = array_filter($form_state['values']['CustomFields']['FormFieldsSelected']);

  $ArrayForm['FormFields'] = array();
  foreach ( $selected as $CustomFieldID => $value ) {

    $settings = $form_state['values']['CustomFields']['FormFields'][$CustomFieldID];

    if ( $CustomFieldID == 'EmailAddress' ) {

      // email address label
      $ArrayForm['EmailAddress'] = array(
        'label' => $form_state['values']['CustomFields']['FormFields']['EmailAddress']['label'],
        'weight' => $form_state['values']['CustomFields']['FormFieldWeights']['EmailAddress'],
      );

    }
    elseif ( $CustomFieldID == 'PhoneNumber' ) {

      $ArrayForm['PhoneNumber'] = array(
        'label' => $settings['label'],
        'required' => (empty($settings['required'])) ? 0 : $settings['required'],
        'hidden' => (empty($settings['hidden'])) ? 0 : $settings['hidden'],
        'weight' => $form_state['values']['CustomFields']['FormFieldWeights'][$CustomFieldID],
        'selected' => 1,
      );

    }
    else {

      $ArrayForm['FormFields'][$CustomFieldID] = array(
        'label' => $settings['label'],
        'type' => $settings['type'],
        'required' => (empty($settings['required'])) ? 0 : $settings['required'],
        'hidden' => (empty($settings['hidden'])) ? 0 : $settings['hidden'],
        'rows' => (empty($settings['rows'])) ? 0 : $settings['rows'],
        'default_value' => (empty($settings['default_value'])) ? '' : $settings['default_value'],
        'widget' => (empty($settings['widget'])) ? CustomFields::WIDGET_NONE : $settings['widget'],
        'weight' => $form_state['values']['CustomFields']['FormFieldWeights'][$CustomFieldID],
        'max_length' => $settings['max_length'] ?? '',
      );

      if (array_key_exists('allow_urls', $settings)) {
        $ArrayForm['FormFields'][$CustomFieldID]['allow_urls'] = intval($settings['allow_urls']);
      }
    }

  }

  if ( empty($selected['PhoneNumber']) ) {
    $ArrayForm['PhoneNumber']['selected'] = 0;
  }

  if ( $form_state['values']['AjaxPreview'] ) {

    $preview = SubscriptionFormsWidget::FromArray($ArrayForm);
    $preview->SetData($ArrayForm);

    $form_state['values']['AjaxReturn'] = $preview->GetHTML(TRUE);

    return;

  }

  if ( !empty($form_state['values']['CustomFields']['AddCustomFieldID']) ) {

    //a custom field has been selected to be added to the form
    //Note: the custom field id is written to the hidden field by javascript @see: list_forms.inc -> klicktipp_list_forms_customfield_settings_javascript()
    Libraries::include('list_forms.inc');
    klicktipp_list_forms_add_customfield($form_state, $ArrayForm);

    //the field has been added but we don't save the form, the user can still click on cancel
    return;

  }

  // update subscription form
  if (!SubscriptionFormsWidget::UpdateDB($ArrayForm)) {

    $error = array(
      '!UserID' => $UserID,
      '!BuildID' => $ArrayForm['BuildID'],
      '!Name' => $Name,
      '!ArrayForm' => $ArrayForm,
    );
    Errors::unexpected("SubscriptionFormWidget: Error while updating the Subscription form !Name for User !UserID with BuildID !BuildID.", $error, TRUE);

    return;

  }
  else {

    $ObjectForm = SubscriptionFormsWidget::FromID($UserID, $ArrayForm['BuildID']);

    if ( !$ObjectForm || !$ObjectForm->Publish() ) {

      $error = array(
        '!UserID' => $UserID,
        '!BuildID' => $ArrayForm['BuildID'],
        '!Name' => $Name,
        '!ArrayForm' => $ArrayForm,
      );
      Errors::unexpected("SubscriptionFormWidget: Error while publishing the Subscription form !Name for User !UserID with BuildID !BuildID.", $error, TRUE);

      return;

    }

  }

  drupal_set_message(t("Subscription form %name successfully updated.", array('%name' => $Name)));

}

function klicktipp_list_forms_widget_delete_confirm_form_submit($form, &$form_state) {

  $ArrayForm = $form_state['values']['Entity'];
  $BuildID = $ArrayForm['BuildID'];
  $UserID = $ArrayForm['RelOwnerUserID'];
  $Name = $ArrayForm['Name'];

  if ( SubscriptionFormsWidget::DeleteDB($ArrayForm) ) {
    drupal_set_message(t("Subscription form %name successfully deleted.", array('%name' => $Name)));
  }
  else {

    $error = array(
      '!UserID' => $UserID,
      '!BuildID' => $BuildID,
      '!Name' => $Name,
    );

    //notify user
    Errors::unexpected("SubscriptionFormWidget: Delete Subscription form (ID: !BuildID) for user: !UserID with name !Name", $error, TRUE);

  }

  klicktipp_set_redirect($form_state, "listbuilding/$UserID");

}

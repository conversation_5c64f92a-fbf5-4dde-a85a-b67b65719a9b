<?php

// We need to include the email.inc file here. If the form 'klicktipp_email_edit_form'
// is called later in the campaigns.inc file it will make use of the drupal ajax
// process. But the ajax call will only include the campaign.inc file, not the email.inc
// file by itself. So we have to include it here. Then the ajax call can find the
// needed function in that file, like the validate function.
// We want to be as specific when to include the file to prevent unexpected side effects.
use App\Klicktipp\AutoResponders;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DatabaseTableCRUD;
use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\Dates;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\EmailsNewsletterEmail;
use App\Klicktipp\EmailsNewsletterSMS;
use App\Klicktipp\Errors;
use App\Klicktipp\Libraries;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\SplitTests;
use App\Klicktipp\Statistics;
use App\Klicktipp\Subaccount;
use App\Klicktipp\Subscribers;
use App\Klicktipp\SubscribersSearch;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;
use App\Klicktipp\VarAdditionalAddresses;
use App\Klicktipp\VarSubscriberAudience;

if (current_path() === 'system/ajax' && $form_state['build_info']['form_id'] == 'klicktipp_email_edit_form') {
  Libraries::include('email.inc', '/forms');
}

define("CONTROLLER_CAMPAIGN_SPAM_TRESHOLD", 0.001); //0.1 percent

/**
 * Callback for campaigns overview page
 */
function klicktipp_campaign_overview_form($form, $form_state, $account, $FilterData = '', $PagerPage = 1, $PagerSize = 0) {

  //get the last filter setting from the user session
  if (empty($FilterData)) {
    $FilterData = (empty($_SESSION['UserDefaults']['NewsletterFilter'])) ? KLICKTIPP_DEFAULTS_NEWSLETTER_FILTER : $_SESSION['UserDefaults']['NewsletterFilter'];
  }

  //set breadcrumb and page title
  $page_title = t('Newsletter Overview - @status', array('@status' => t("$FilterData(EmailStatus)")));
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $header = array(
    array(
      'data' => t('ID'),
      'field' => 'CampaignID',
      'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
    ),
  );
  if ($FilterData == 'Sent') {
    $header[] = array(
      'data' => t('Name'),
      'field' => 'CampaignName',
    );
    $header[] = array(
      'data' => t('Date scheduled'),
      'field' => 'SendProcessFinishedOn',
      'sort' => 'desc',
      'class' => array('table-col-fit'),
    ); // SendProcessFinishedOn
    $header[] = array(
      'data' => t('Sent'),
      //no longer in db table, so deactivated until cockpit EK 2017-06-15
      //'field' => 'TotalSent',
      'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
    ); // TotalSent
    $header[] = array(
      'data' => t('Opened'),
      //no longer in db table, so deactivated until cockpit EK 2017-06-15
      //'field' => 'TotalOpens',
      'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
    ); // TotalOpens
    $header[] = array(
      'data' => t('Clicked'),
      //no longer in db table, so deactivated until cockpit EK 2017-06-15
      //'field' => 'TotalClicks',
      'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
    ); // TotalClicks
  }
  else {
    if ($FilterData == 'Scheduled') {
      $header[] = array(
        'data' => t('Name'),
        'field' => 'CampaignName',
      );
      $header[] = array(
        'data' => t('Date scheduled'),
        'field' => 'SendDatetime',
        'sort' => 'desc',
        'class' => array('table-col-fit table-col-right'),
      ); // SendDate
    }
    else {
      $header[] = array(
        'data' => t('Name'),
        'field' => 'CampaignName',
        'sort' => 'asc',
      );
    }
  }

  $ArraySort = klicktipp_get_tablesort($header, array('CampaignName' => 'ASC'));

  // Get campaign data -- Start
  static $data;
  if (!isset($data)) {
    $data = array(
      'Campaigns' => array(),
      'TotalCampaigns' => 0,
      'TotalSentCampaigns' => 0,
      'TotalOutboxCampaigns' => 0,
      'TotalDraftCampaigns' => 0,
      'TotalScheduledCampaigns' => 0,
    );

    // row order
    $SortField = array_keys($ArraySort);
    $SortField = $SortField[0];
    $Order = $ArraySort[$SortField];
    $AllFilteredCampaigns = array();

    $result = db_query("SELECT CampaignID, CampaignStatusEnum, ScheduleTypeEnum FROM {campaigns} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID " .
      " AND (AutoResponderTriggerTypeEnum = :TriggerTypeEnum1 OR AutoResponderTriggerTypeEnum = :TriggerTypeEnum2) " .
      " ORDER BY $SortField $Order",
      array(
        ':RelOwnerUserID' => $account->uid,
        ':TriggerTypeEnum1' => Campaigns::TRIGGER_TYPE_CAMPAIGN,
        ':TriggerTypeEnum2' => Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN,
      ));
    while ($Campaign = $result->fetchAssoc()) {
      switch ($Campaign['CampaignStatusEnum']) {
        case Campaigns::STATUS_DRAFT:
          // 'Draft'
          if ($FilterData == 'Draft') {
            $AllFilteredCampaigns[] = $Campaign['CampaignID'];
          }
          $data['TotalDraftCampaigns']++;
          break;
        case Campaigns::STATUS_READY:
          if ($Campaign['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED) {
            // 'Draft'
            if ($FilterData == 'Draft') {
              $AllFilteredCampaigns[] = $Campaign['CampaignID'];
            }
            $data['TotalDraftCampaigns']++;
          }
          else {
            // 'Scheduled'
            if ($FilterData == 'Scheduled') {
              $AllFilteredCampaigns[] = $Campaign['CampaignID'];
            }
            $data['TotalScheduledCampaigns']++;
          }
          break;
        case Campaigns::STATUS_CREATE_QUEUE:
        case Campaigns::STATUS_CREATE_SPLITTEST_QUEUE:
        case Campaigns::STATUS_WAITING_FOR_WINNER:
        case Campaigns::STATUS_CREATE_WINNER_QUEUE:
        case Campaigns::STATUS_SENDING:
          // 'Outbox'
          if ($FilterData == 'Outbox') {
            $AllFilteredCampaigns[] = $Campaign['CampaignID'];
          }
          $data['TotalOutboxCampaigns']++;
          break;
        case Campaigns::STATUS_SENT:
          // 'Sent'
          if ($FilterData == 'Sent') {
            $AllFilteredCampaigns[] = $Campaign['CampaignID'];
          }
          $data['TotalSentCampaigns']++;
          break;
      }
    }
    $data['AllFilteredCampaigns'] = $AllFilteredCampaigns;
    $data['TotalCampaigns'] = count($AllFilteredCampaigns);
  }
  // Get campaign data -- End

  // split for pager
  // NOTE: MigrationLinks -> will no longer exist
  $PagerLink = "campaigns/{$account->uid}/$FilterData";
  [$PagerPage, $PagerSize, $Offset] = klicktipp_validate_pager($PagerPage, $PagerSize, count($data['AllFilteredCampaigns']), $PagerLink);

  $data['Campaigns'] = array_slice($data['AllFilteredCampaigns'], $Offset, $PagerSize, TRUE);

  // display data

  $weight = 1;

  $form = array();

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $account->uid,
    '#weight' => $weight++,
  );

  // NOTE: MigrationLinks -> will no longer exist
  $createButtons = [
    [
      '#title' => t('Action::Add::Newsletter'),
      '#value' => "emails/{$account->uid}/add",
    ],
    [
      '#title' => t('Action::Add::SMS Newsletter'),
      '#value' => "emails/{$account->uid}/addsms",
    ],
  ];

  $form['create_buttons'] = [
    '#theme' => 'klicktipp_create_buttons',
    '#buttons' => $createButtons,
    '#weight' => $weight++,
    '#prefix' => '<div class="create-buttons"><div class="clearfix"><div class="right">',
    '#suffix' => '</div></div></div>',
  ];

  // --- Overview filter: @see klicktipp_overview_filter

  //add filter for status (array key => form element (#type = select) id to used in submit, values => options of select element
  $ArrayFilters = array(
    'FilterStatus' => array(
      'Sent' => t('Sent (@count)', array('@count' => $data['TotalSentCampaigns'])),
      'Outbox' => t('Outbox (@count)', array('@count' => $data['TotalOutboxCampaigns'])),
      'Draft' => t('Draft (@count)', array('@count' => $data['TotalDraftCampaigns'])),
      'Scheduled' => t('Scheduled (@count)', array('@count' => $data['TotalScheduledCampaigns'])),
    ),
  );

  // default value of the filter (use same key as in $ArrayFilters)
  $ArrayFilterDefaults = array(
    'FilterStatus' => $FilterData,
  );

  //create the filter: one select element for each key in $ArrayFilters
  //adds a hidden Apply-Filter-Button (#type = submit)
  $form['Filter'] = klicktipp_overview_filter($ArrayFilters, $ArrayFilterDefaults, $weight, t('Newsletter status'));
  // --- end overview filter

  $edit_link = ($FilterData == 'Draft') ? "/edit" : (($FilterData == 'Scheduled') ? "/senddate" : "");

  $rows = array();
  if (isset($data['Campaigns'])) {
    foreach ($data['Campaigns'] as $CampaignID) {
      $campaign = Campaigns::RetrieveCampaignByID($CampaignID, $account->uid);

      $SMSLabel = '';
      if (Campaigns::IsSMSCampaign($campaign['AutoResponderTriggerTypeEnum'])) {
        $SMSLabel = '<span class="label label-info splittestlabel">' . t('SMS') . '</span>';
      };
      $DisplayName = '<span class="cut-text display-email has-tooltip" title="' . $campaign['CampaignName'] . '">' . $campaign['CampaignName'] . '</span>';
      $SplittestLabel = ($campaign['SplittestID']) ? '<span class="label label-info splittestlabel">' . t('Splittest') . '</span>' : '';

      $CampaignName = ($campaign['SplittestID']) ? $DisplayName : $DisplayName;

      $row = array(
        array(
          'data' => $campaign['CampaignID'],
          'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
        ),
        array(
          // NOTE: MigrationLinks -> will no longer exist
          'data' => l($CampaignName, "emails/{$account->uid}/{$campaign['CampaignID']}" . $edit_link, array('html' => TRUE)) . $SMSLabel . $SplittestLabel
        ),
      );

      if ($FilterData == 'Sent') {
        // sent at
        $row[] = array(
          'data' => Dates::formatDate(Dates::FORMAT_DMYHI, (int) $campaign['SendProcessFinishedOn']),
          'class' => array('table-col-fit'),
        );
        $row[] = array(
          'data' => $campaign['TotalSent'],
          'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
        ); //total sent
        $row[] = array(
          'data' => Campaigns::IsSMSCampaign($campaign['AutoResponderTriggerTypeEnum']) ? '' : $campaign['TotalOpens'],
          'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
        ); //unique opens
        $row[] = array(
          'data' => $campaign['TotalClicks'],
          'class' => array('table-col-fit table-col-right hidden-xs hidden-sm'),
        ); //unique clicks
      }
      else {
        if ($FilterData == 'Scheduled') {
          // send at
          $SendDate = (empty($campaign['SendDatetime'])) ? t('Now') : Dates::formatDate(Dates::FORMAT_DMYHI, (int) $campaign['SendDatetime']);
          $row[] = array(
            'data' => $SendDate,
            'class' => array('table-col-fit table-col-right'),
          );

        }
      }

      $rows[] = $row;
    }
  }

  if (empty($rows)) {
    $form['empty'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t('There are no campaigns within this category yet.'),
      '#weight' => $weight++,
    );
  }
  else {

    $form['OverviewTable'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array(
        'header' => $header,
        'rows' => $rows,
        'attributes' => ['data-e2e-id' => 'table-newsletter-overview']
      )),
      '#weight' => $weight++,
      '#suffix' => theme('klicktipp_table_pager', array(
        'element' => array(
          'pager_page' => $PagerPage,
          //current page

          'pager_size' => $PagerSize,
          //current page size

          'pager_hide_sizes' => FALSE,
          //hide page size badges

          'pager_total' => $data['TotalCampaigns'],
          //total entries in the table

          'pager_max_items' => KLICKTIPP_PAGER_MAX_PAGINATION_ITEM,
          //max pager items

          'pager_link' => $PagerLink,
          //link of pager badges and items, "/<CURRENT-PAGE>/<PAGE-SIZE>" will be added

          'pager_link_query' => (empty($_GET['order'])) ? array() : array(
            'order' => $_GET['order'],
            'sort' => $_GET['sort'],
          ),
          //build an additional link query string, possibly for table sort @see l()/url()
        )
      )),
    );

  }

  return $form;
}

/**
 * campaign overview submit
 */
function klicktipp_campaign_overview_form_submit($form, &$form_state) {

  if ($form_state['values']['op'] == t(/*ignore*/KLICKTIPP_BUTTON_TEXT_APPLY_FILTER)) {

    $UserID = $form_state['values']['uid'];
    $FilterData = $form_state['values']['FilterStatus'];

    $_SESSION['UserDefaults']['NewsletterFilter'] = $FilterData;

    // NOTE: MigrationLinks -> will no longer exist
    klicktipp_set_redirect($form_state, "campaigns/$UserID/$FilterData");

  }

}

/**
 * the campaign edit form
 */
function klicktipp_campaign_form($form, $form_state, $account, $CampaignID = 'add', $Wizard = 0) {

  $UserID = $account->uid;

  //insert TRUE -> create new email: the user is allowed to edit everything (email type, trigger time, tags, create splittest, edit splittest)
  $insert = TRUE;
  $insert_type = Campaigns::TRIGGER_TYPE_CAMPAIGN;
  switch ($CampaignID) {
    case 'add': // Newsletter
      $insert_type = Campaigns::TRIGGER_TYPE_CAMPAIGN;
      break;
    case 'addar': // Autoresponder
      $insert_type = Campaigns::TRIGGER_TYPE_SUBSCRIPTION;
      break;
    case 'addbd': // Birthday AR
      $insert_type = Campaigns::TRIGGER_TYPE_BIRTHDAY;
      break;
    case 'addsms': // SMS Newsletter
      $insert_type = Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN;
      break;
    case 'addsmsar': // SMS Autoresponder
      $insert_type = Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION;
      break;
    case 'addsmsbd': // SMS Birthday AR
      $insert_type = Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY;
      break;
    default:
      $insert = empty($CampaignID);
      break;
  }

  if ($insert) {

    $CampaignInformation = array(
      'AutoResponderTriggerTypeEnum' => $insert_type,
    );
    $CampaignRecipients = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array(),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );

    $SplittestObject = FALSE;
  }
  else {
    $CampaignObject = Campaigns::FromID($UserID, $CampaignID);

    if (empty($CampaignObject)) {
      drupal_not_found();
      drupal_exit();
    }

    $CampaignInformation = $CampaignObject->GetData();

    $CampaignRecipients = $CampaignInformation['RecipientLists'];

    klicktipp_campaign_datetime_to_subscription($CampaignInformation);

    $SplittestObject = CampaignsNewsletter::GetSplittestObject($CampaignInformation);

  }

  //set breadcrumb and page title
  switch ($CampaignInformation['AutoResponderTriggerTypeEnum']) {
    case Campaigns::TRIGGER_TYPE_SUBSCRIPTION:
      $IsSMS = FALSE;
      $IsAutoResponder = TRUE;
      $IsBirthdayAR = FALSE;
      $page_title = $insert ? t('Create Autoresponder') : t('Autoresponder Settings');
      $breadcrumb = array(
        // NOTE: MigrationLinks -> will no longer exist
        l(t('Autoresponder Overview'), "autoresponders/$UserID")
      );
      break;
    case Campaigns::TRIGGER_TYPE_BIRTHDAY:
      $IsSMS = FALSE;
      $IsAutoResponder = TRUE;
      $IsBirthdayAR = TRUE;
      $page_title = $insert ? t('Create Birthday (autoresponder)') : t('Birthday (autoresponder) Settings');
      $breadcrumb = array(
        // NOTE: MigrationLinks -> will no longer exist
        l(t('Autoresponder Overview'), "autoresponders/$UserID"),
      );
      break;
    case Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN:
      $IsSMS = TRUE;
      $IsAutoResponder = FALSE;
      $IsBirthdayAR = FALSE;
      $page_title = $insert ? t('Create SMS Newsletter') : t('SMS Newsletter Settings');
      $breadcrumb = array(
        // NOTE: MigrationLinks -> will no longer exist
        l(t('Newsletter Overview'), "campaigns/$UserID"),
      );
      break;
    case Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION:
      $IsSMS = TRUE;
      $IsAutoResponder = TRUE;
      $IsBirthdayAR = FALSE;
      $page_title = $insert ? t('Create SMS Autoresponder') : t('SMS Autoresponder Settings');
      $breadcrumb = array(
        // NOTE: MigrationLinks -> will no longer exist
        l(t('Autoresponder Overview'), "autoresponders/$UserID"),
      );
      break;
    case Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY:
      $IsSMS = TRUE;
      $IsAutoResponder = TRUE;
      $IsBirthdayAR = TRUE;
      $page_title = $insert ? t('Create Birthday (SMS autoresponder)') : t('Birthday (SMS autoresponder) Settings');
      $breadcrumb = array(
        // NOTE: MigrationLinks -> will no longer exist
        l(t('Autoresponder Overview'), "autoresponders/$UserID"),
      );
      break;
    default: // Campaigns::TRIGGER_TYPE_CAMPAIGN:
      $IsSMS = FALSE;
      $IsAutoResponder = FALSE;
      $IsBirthdayAR = FALSE;
      $page_title = $insert ? t('Create Newsletter') : t('Newsletter Settings');
      $breadcrumb = array(
        // NOTE: MigrationLinks -> will no longer exist
        l(t('Newsletter Overview'), "campaigns/$UserID"),
      );
      break;
  }

  if ( !$insert ) {
    // NOTE: MigrationLinks -> will no longer exist
    $breadcrumb[] = l($CampaignInformation['CampaignName'], "emails/$UserID/$CampaignID");
  }

  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb($breadcrumb, $page_title);

  if ($IsSMS && !klicktipp_feature_access($account, 'access sms marketing')) {
    //show sms marketing upsell

    $form['NoAccess'] = array(
      '#type' => 'markup',
      '#value' => _klicktipp_get_content_include('klicktipp_content_include_sms_upsell'),
    );

    return $form;

  }

  //edit_campaign TRUE -> existing campaign (status draft or ready && not scheduled): the user is allowed to edit trigger time and tags
  $edit_campaign = ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_DRAFT) ||
    ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_READY && $CampaignInformation['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED);

  //edit_splittest TRUE -> access splittest & new email or existing splittest campaign (status draft or ready && not scheduled): the user is allowed to edit the splittest settings
  $edit_splittest = (user_access('klicktipp premium', $account) && ($insert || ($edit_campaign && !empty($CampaignInformation['SplittestID']))));

  //$edit_receiver_email TRUE -> autoresponder && access 'use receiver email' (ar dont have a finished state, so we may edit it anytime)
  $edit_receiver_email = ($IsAutoResponder && user_access('use receiver email', $account));

  //$edit_multiple_send TRUE -> autoresponder && dont have a finished state, so we may edit it anytime
  $edit_multiple_send = $IsAutoResponder && !$IsBirthdayAR;

  //if this dialog is called via "Create e-mail" or "Create e-mail" -> Submit -> Back-Button "Edit recipients" (in klicktipp_email_edit_form ) the user is still in the Wizard
  //when this dialog is called via LocalTask or EditLinks the Wizard is not active
  //the Wizard determines the Save button text and redirect
  $form['Wizard'] = array(
    '#type' => 'value',
    '#value' => $Wizard,
  );

  $form['insert'] = array(
    '#type' => 'value',
    '#value' => $insert,
  );

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  $form['CampaignInformation'] = array(
    '#type' => 'value',
    '#value' => $CampaignInformation,
  );

  $form['IsAutoResponder'] = array(
    '#type' => 'value',
    '#value' => $IsAutoResponder,
  );

  $form['IsSMS'] = array(
    '#type' => 'value',
    '#value' => $IsSMS,
  );

  $form['EditCampaign'] = array(
    '#type' => 'value',
    '#value' => $edit_campaign,
  );

  $form['EditSplittest'] = array(
    '#type' => 'value',
    '#value' => $edit_splittest,
  );

  $form['EditReceiverEmail'] = array(
    '#type' => 'value',
    '#value' => $edit_receiver_email,
  );

  $form['EditMultipleSend'] = array(
    '#type' => 'value',
    '#value' => $edit_multiple_send,
  );

  //this value will be changed by javascript to 1 if the new email editor is chosen
  $form['Version'] = array(
    '#type' => 'hidden',
    '#default_value' => 0,
  );

  $weight = 0;

  $helptags = ($IsAutoResponder) ? array(
    "follow-up-emails",
    "tagging",
    "smarttags",
  ) : array(
    "newsletter",
    "tagging",
    "smarttags",
  );

  if ($IsSMS) {
    $helptags[] = 'sms';
  }

  $chooseEditor = ($insert && !$IsSMS);
  //Note: on form error (e.g. duplicate name) the version has already been selected
  //      do not show the popup again
  $chooseEditor = $chooseEditor && (!isset($form_state['input']['Version']));

  if ($chooseEditor) {
    //the user is allowed to us the new email editor
    //display an overlay over the form where he has to choose between
    //the old or new editor
    //after the user selected an editor, the overlay will be hidden,
    //the hidden field version will be set to 0 or 1 and the create form will be displayed
    Libraries::include('campaigns.inc');
    $form['choose_editor'] = klicktipp_form_element_choose_email_editor($weight);
  }

  //wrap the form with a container and hide the form if the user is allowed
  //to choose editors
  $displayForm = ($chooseEditor) ? 'display: none;' : '';
  $form['form_wrapper'] = array(
    '#type' => 'markup',
    '#prefix' => '<div class="email-form-wrapper" style="' . $displayForm . '">',
    '#suffix' => '</div>',
    '#weight' => $weight++,
  );

  //name of campaign, always editable
  $form['form_wrapper']['title'] = array(
    '#type' => 'textfield',
    '#title' => t('Name'),
    '#required' => TRUE,
    '#default_value' => $CampaignInformation['CampaignName'],
    '#size' => 60,
    '#weight' => $weight++,
    '#quickhelp' => ($IsSMS) ? 'create-edit-sms-name' : 'create-edit-email-name',
  );

  if ($insert) {
    $Labels = [];
  } else {
    $Labels = $CampaignObject->GetMetaLabels();
  }
  $LabelOptions = MetaLabels::GetEntitiesAsOptionsArray($UserID);

  $form['form_wrapper']['MetaLabels'] = array(
    '#type' => 'textfield',
    '#default_value' => (empty($form_state['input']['MetaLabels'])) ? $Labels : $form_state['input']['MetaLabels'],
    '#required' => FALSE,
    '#title' => t("Labels"),
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $LabelOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing labels will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Labels cannot contain only numbers.'),
      'form_post' => $form_state['input']['MetaLabels'], //catch free entries on form validation error
    ),
    '#theme' => 'klicktipp_magic_select',
    '#weight' => $weight++,
    '#quickhelp' => 'edit-tag-labels',
  );

  // --- Edit Campaign Type (newsletter or autoresponder) ---

  $form['form_wrapper']['AutoResponderTriggerTypeEnum'] = array(
    '#type' => 'hidden', //must be type hidden for JavaScript
    '#value' => $CampaignInformation['AutoResponderTriggerTypeEnum'],
  );

  // --- Edit Autoresponder trigger time (Delay) ---

  $TriggerTimeTypeOptions = array(
    Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY => t('Immediately'),
    Campaigns::TRIGGER_TIME_TYPE_SECONDS => t('Seconds later'),
    Campaigns::TRIGGER_TIME_TYPE_MINUTES => t('Minutes later'),
    Campaigns::TRIGGER_TIME_TYPE_HOURS => t('Hours later'),
    Campaigns::TRIGGER_TIME_TYPE_DAYS => t('Days later'),
    Campaigns::TRIGGER_TIME_TYPE_WEEKS => t('Weeks later'),
    Campaigns::TRIGGER_TIME_TYPE_MONTHS => t('Months later'),
    Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD => t('Start time from contact field'),
  );

  // ISO-8601 day of week is 1 (monday) bis 7 (sunday)
  $TriggerTimeDayOfWeekOptions = array(
    1 => t('Monday (short)'),
    2 => t('Tuesday (short)'),
    3 => t('Wednesday (short)'),
    4 => t('Thursday (short)'),
    5 => t('Friday (short)'),
    6 => t('Saturday (short)'),
    7 => t('Sunday (short)'),
  );

  for ($i = 0; $i < 24; $i++) {
    $s = sprintf('%02d', $i);
    $DelayTimeOptionsHours[$s] = $s;
  }
  for ($i = 0; $i < 56; $i += 5) {
    $s = sprintf('%02d', $i);
    $DelayTimeOptionsMinutes[$s] = $s;
  }

  // get data for "delay defined by custom field"
  $CFDatetimeFields = array('' => t('Please select'));
  $CFDateFields = array('' => t('Please select'));

  $AllCustomFields = CustomFields::RetrieveCustomFields($UserID);
  foreach ($AllCustomFields as $field) {
    $fieldname = CustomFields::GetFieldName($field);
    if ($field['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
      $CFDatetimeFields[$field['CustomFieldID']] = t("Date and time from Custom field '@fieldname'", array('@fieldname' => $fieldname));
    }
    if ($field['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
      $CFDateFields[$field['CustomFieldID']] = t("Date from Custom field '@fieldname'", array('@fieldname' => $fieldname));
    }
  }

  if (count($CFDatetimeFields) < 2) { //count 1 == t('Please select')
    // disable custom field option if no datetime field available
    unset($TriggerTimeTypeOptions[Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD]);
  }

  $SplittestWinnerOptions = array();
  if (!$IsSMS) {
    $SplittestWinnerOptions[SplitTests::WINNER_OPENS] = t('The version with the highest open rate');
  }
  $SplittestWinnerOptions[SplitTests::WINNER_CLICKS] = t('The version with the most unique click');
  if (!($edit_campaign || $insert) || user_access('access conversion pixel', $account)) {
    $SplittestWinnerOptions[SplitTests::WINNER_CONVERSION] = t('The version with the most unique conversions');
    $SplittestWinnerOptions[SplitTests::WINNER_AMOUNT] = t('The version with the highest revenue');
  }

  if ($IsAutoResponder && ($edit_campaign || $insert)) {
    //trigger time is only editable for new emails or draft and stopped autoresponders

    if ($IsBirthdayAR) {

      // BirthdayTimeDelay
      $form['form_wrapper']['BirthdayCustomFieldID'] = array(
        '#type' => 'select',
        '#title' => t('Delay'),
        '#default_value' => empty($CampaignInformation['BirthdayCustomFieldID']) ?
          CustomFields::$GlobalCustomFieldDefs['Birthday']['CustomFieldID'] : $CampaignInformation['BirthdayCustomFieldID'],
        '#options' => $CFDateFields,
        '#weight' => $weight++,
        '#quickhelp' => 'create-edit-email-trigger-birthday',
      );
      $form['form_wrapper']['BirthdayTimeDelay'] = array(
        '#type' => 'markup',
        '#prefix' => '<div id="BirthdayTimeDelayTable" class="select-time" style="display:block;"><label for="edit-SendTimeHour">' . t('Time') . ':</label>',
        '#suffix' => '</div>',
        '#weight' => $weight++,
      );
      $form['form_wrapper']['BirthdayTimeDelay']['SendTimeHour'] = array(
        '#type' => 'select',
        '#default_value' => empty($CampaignInformation['BirthdayTimeDelay']) ? '00' :
          sprintf('%02d', intval($CampaignInformation['BirthdayTimeDelay'] / 3600)),
        '#options' => $DelayTimeOptionsHours,
        '#suffix' => '<span id="SendTimeMinuteSpan">:</span>',
      );
      $form['form_wrapper']['BirthdayTimeDelay']['SendTimeMinute'] = array(
        '#type' => 'select',
        '#default_value' => empty($CampaignInformation['BirthdayTimeDelay']) ? '00' :
          date('i', $CampaignInformation['BirthdayTimeDelay']),
        '#options' => $DelayTimeOptionsMinutes,
      );

    }
    else {

      $form['form_wrapper']['TriggerTimeTable'] = array(
        '#weight' => $weight++,
        '#prefix' => '<div id="TriggerTimeTable">',
        '#suffix' => '</div>',
      );

      $form['form_wrapper']['TriggerTimeTable']['TriggerTime'] = array(
        '#type' => 'textfield',
        '#title' => t('Delay'),
        '#default_value' => $CampaignInformation['TriggerTime'],
        '#weight' => $weight++,
        '#size' => 6,
        '#maxlength' => 3,
        '#prefix' => '<div class="row row-element"><div id="TriggerTimeValue" class="col-md-1 col-sm-2 col-xs-12 row-element" style="z-index: 3;">',
        '#suffix' => '</div>',
        '#quickhelp' => ($IsSMS) ? 'create-edit-sms-trigger-time' : 'create-edit-email-trigger-time',
      );

      $form['form_wrapper']['TriggerTimeTable']['TriggerTimeTypeEnum'] = array(
        '#type' => 'select',
        '#title' => t('Delay'),
        '#default_value' => $CampaignInformation['TriggerTimeTypeEnum'],
        '#options' => $TriggerTimeTypeOptions,
        '#weight' => $weight++,
        '#prefix' => '<div class="col-md-11 col-sm-10 col-xs-12 row-element" style="z-index: 2;">',
        '#suffix' => '</div></div>',
        '#quickhelp' => ($IsSMS) ? 'create-edit-sms-trigger-time' : 'create-edit-email-trigger-time',
      );

      $form['form_wrapper']['DelayByCustomFieldDatetimeFieldID'] = array(
        '#type' => 'select',
        '#default_value' => $CampaignInformation['DelayByCustomFieldDatetimeFieldID'],
        '#options' => $CFDatetimeFields,
        '#weight' => $weight++,
        '#quickhelp' => 'create-edit-email-trigger-datetime',
      );

      $form['form_wrapper']['UseTriggerDayOfWeek'] = array(
        '#type' => 'checkbox',
        '#title' => t('Send on the following weekdays.'),
        '#default_value' => (empty($CampaignInformation['UseTriggerDayOfWeek'])) ? 0 : 1,
        '#return_value' => 1,
        '#weight' => $weight++,
        '#attributes' => array(
          //@see script.js
          'data-event-load' => 'js_checkbox_toggle_element_display',
          'data-event-load-args' => '#TriggerTimeDayOfWeekTable',
          'data-event-click' => 'js_checkbox_toggle_element_display',
          'data-event-click-args' => '#TriggerTimeDayOfWeekTable',
        ),
        '#quickhelp' => 'create-edit-email-trigger-dayofweek',
      );

      $form['form_wrapper']['TriggerDayOfWeek'] = array(
        '#type' => 'checkboxes',
        '#default_value' => (empty($CampaignInformation['TriggerDayOfWeek'])) ? array(1,2,3,4,5,6,7) : $CampaignInformation['TriggerDayOfWeek'],
        '#return_value' => 1,
        '#options' => $TriggerTimeDayOfWeekOptions,
        '#weight' => $weight++,
        '#prefix' => '<div id="TriggerTimeDayOfWeekTable">',
        '#suffix' => '<div style="clear:both;"></div></div>',
        '#attributes' => array(
          'data-event-load' => 'js_select_weekday',
        ),
      );

      $form['form_wrapper']['UseDayOfWeekTimeDelay'] = array(
        '#type' => 'checkbox',
        '#title' => t('Send at a specific time.'),
        '#default_value' => (empty($CampaignInformation['UseDayOfWeekTimeDelay'])) ? 0 : 1,
        '#return_value' => 1,
        '#weight' => $weight++,
        '#attributes' => array(
          //@see script.js
          'data-event-load' => 'js_checkbox_toggle_element_display',
          'data-event-load-args' => '#DayOfWeekTimeDelayTable',
          'data-event-click' => 'js_checkbox_toggle_element_display',
          'data-event-click-args' => '#DayOfWeekTimeDelayTable',
        ),
        '#quickhelp' => 'create-edit-email-trigger-dayofweek-timedelay',
      );

      $form['form_wrapper']['DayOfWeekTimeDelay'] = array(
        '#type' => 'markup',
        '#prefix' => '<div id="DayOfWeekTimeDelayTable" class="select-time" style="display:block;">',
        '#suffix' => '</div>',
        '#weight' => $weight++,
      );
      $form['form_wrapper']['DayOfWeekTimeDelay']['SendTimeHour'] = array(
        '#type' => 'select',
        '#default_value' => empty($CampaignInformation['DayOfWeekTimeDelay']) ? '00' :
          sprintf('%02d', intval($CampaignInformation['DayOfWeekTimeDelay'] / 3600)),
        '#options' => $DelayTimeOptionsHours,
        '#suffix' => '<span class="time">:</span>',
      );
      $form['form_wrapper']['DayOfWeekTimeDelay']['SendTimeMinute'] = array(
        '#type' => 'select',
        '#default_value' => empty($CampaignInformation['DayOfWeekTimeDelay']) ? '00' :
          date('i', $CampaignInformation['DayOfWeekTimeDelay']),
        '#options' => $DelayTimeOptionsMinutes,
      );

    }

  }
  else {
    if ($IsAutoResponder) {
      //active autoresponder, display the trigger time

      if ($IsBirthdayAR) {
        //trigger datetime from custom fields

        $form['form_wrapper']['DisplayTriggerTimeDateField'] = array(
          '#type' => 'item',
          '#theme' => 'klicktipp_item',
          '#title' => t('Delay'),
          '#value' => t('Day in Custom field %field at %time', array(
            '%field' => $CampaignInformation['BirthdayCustomFieldID'],
            '%time' => sprintf('%02d', intval($CampaignInformation['BirthdayTimeDelay'] / 3600)) . ':' . date('i', $CampaignInformation['BirthdayTimeDelay']),
          )),
          '#weight' => $weight++,
          '#quickhelp' => 'create-edit-email-trigger-time',
        );

      }
      else {
        if ($CampaignInformation['TriggerTimeTypeEnum'] == Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD) {
          //trigger date from custom fields

          $form['form_wrapper']['DelayByCustomFieldDatetimeFieldID'] = array(
            '#type' => 'item',
            '#theme' => 'klicktipp_item',
            '#title' => t('Delay'),
            '#value' => $CFDatetimeFields[$CampaignInformation['DelayByCustomFieldDatetimeFieldID']],
            '#weight' => $weight++,
            '#quickhelp' => 'create-edit-email-trigger-time',
          );

        }
        else {
          //display trigger time, if not immediate, concat time and type

          if ($CampaignInformation['TriggerTimeTypeEnum'] == Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY) {
            $DisplayTime = $TriggerTimeTypeOptions[$CampaignInformation['TriggerTimeTypeEnum']];
          }
          else {
            $DisplayTime = $CampaignInformation['TriggerTime'] . " " . $TriggerTimeTypeOptions[$CampaignInformation['TriggerTimeTypeEnum']];
          }

          $form['form_wrapper']['DisplayTriggerTime'] = array(
            '#type' => 'item',
            '#theme' => 'klicktipp_item',
            '#title' => t('Delay'),
            '#value' => $DisplayTime,
            '#weight' => $weight++,
            '#quickhelp' => 'create-edit-email-trigger-time',
          );

          if ( !empty($CampaignInformation['UseTriggerDayOfWeek']) ) {

            $displayoptions = array();
            foreach($CampaignInformation['TriggerDayOfWeek'] as $key) {
              $displayoptions[] = $TriggerTimeDayOfWeekOptions[$key];
            }
            $form['form_wrapper']['TriggerDayOfWeek'] = array(
              '#type' => 'item',
              '#theme' => 'klicktipp_item',
              '#title' => t('Send on the following days'),
              '#value' => implode(', ', $displayoptions),
              '#weight' => $weight++,
              '#quickhelp' => 'create-edit-email-trigger-dayofweek',
            );

          }

          if ( !empty($CampaignInformation['UseDayOfWeekTimeDelay']) ) {

            $form['form_wrapper']['DayOfWeekTimeDelay'] = array(
              '#type' => 'item',
              '#theme' => 'klicktipp_item',
              '#title' => t('Send time'),
              '#value' => t('sendtime: !hours:!minutes', array(
                '!hours' => sprintf('%02d', intval($CampaignInformation['DayOfWeekTimeDelay'] / 3600)),
                '!minutes' =>  date('i', $CampaignInformation['DayOfWeekTimeDelay']),
              )),
              '#weight' => $weight++,
              '#quickhelp' => 'create-edit-email-trigger-dayofweek-timedelay',
            );

          }

        }
      }

    }
  }

  // --- Edit recipients (tags) ---

  // get all manual tags
  $RecipientsOptions = Tag::RetrieveTagsAsOptionsArray($UserID, array(), $insert ? 0 : $CampaignID);

  // recipients
  if ($insert || $edit_campaign) {

    $TagFilter = Tag::GetTagFilter($UserID, $insert ? 0 : $CampaignID);
    $TagFilter[t('Filter: All tags')] = $RecipientsOptions;

    if (count($RecipientsOptions) > 1) {

      $form['form_wrapper']['Tagging'] = array(
        '#type' => 'fieldset',
        '#title' => t("Tag conditions"),
        '#weight' => $weight++,
        '#collapsible' => TRUE,
        '#collapsed' => (!empty($CampaignInformation['SubscriberAudience'])),
        '#attributes' => array(
          'data-event-show-collapsed' => 'js_toggle_recipient_mode',
          'data-event-show-collapsed-args' => '.recipient-mode-tagging',
          'class' => array('recipient-mode-tagging'),
        ),
      );

      $RecipientsOpDefault = (empty($form_state['input']['OpTaggedWith'])) ? $CampaignRecipients['OpTaggedWith'] : $form_state['input']['OpTaggedWith'];
      $form['form_wrapper']['Tagging']['OpTaggedWith'] = array(
        '#type' => 'select',
        '#title' => t("Tagged with"),
        '#default_value' => (empty($RecipientsOpDefault)) ? Campaigns::TAG_HAS_ANY : $RecipientsOpDefault,
        '#options' => array(
          Campaigns::TAG_HAS_ANY => t("Contact has ANY of these tags"),
          Campaigns::TAG_HAS_ALL => t("Contact has ALL of these tags"),
        ),
        '#weight' => $weight++,
        '#attributes' => array(
          'data-event-change' => 'ToggleTaggingQuickhelp',
          'data-event-change-args' => 'positive',
          'data-event-load' => 'ToggleTaggingQuickhelp',
          'data-event-load-args' => 'positive',
        ),
        '#prefix' => '<div class="tagging-dropdown-quickhelp">',
        '#suffix' => '<div class="quickhelps">' . theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => 'contact-tagged-with-any',
              '#title' => t("Quickhelp: Tag link"),
            )
          )) .
          theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => 'contact-tagged-with-all',
              '#title' => t("Quickhelp: Tag link"),
            )
          )) . '</div></div>',
      );

      $form['form_wrapper']['Tagging']['TaggedWith'] = array(
        '#type' => 'textfield',
        '#default_value' => $CampaignRecipients['TaggedWith'],
        '#weight' => $weight++,
        '#magic_select' => array(
          'autocomplete' => '',
          'options' => $RecipientsOptions,
          'multiple' => TRUE,
          'free_entries' => TRUE,
          'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
          'allow_numeric_only' => FALSE,
          'numeric_only_message' => t('Tags cannot contain only numbers.'),
          'form_post' => $form_state['input']['TaggedWith'],
          //catch free entries on form validation error
          'filter' => $TagFilter,
          'container_class' => 'ms-tag-color-positive',
          'maxSelection' => 50,
          'maxSelection_message' => t('The maximum number of positive and negative tags is !max.', array('!max' => 50)),
          'groupID' => 'campaign-recipients',
        ),
        '#theme' => 'klicktipp_magic_select',
      );

      $RecipientsOpNotDefault = (empty($form_state['input']['OpNotTaggedWith'])) ? $CampaignRecipients['OpNotTaggedWith'] : $form_state['input']['OpNotTaggedWith'];
      $form['form_wrapper']['Tagging']['OpNotTaggedWith'] = array(
        '#type' => 'select',
        '#title' => t("Not tagged with"),
        '#default_value' => (empty($RecipientsOpNotDefault)) ? Campaigns::TAG_HAS_NOT_ANY : $RecipientsOpNotDefault,
        '#options' => array(
          Campaigns::TAG_HAS_NOT_ANY => t("Contact doesn't have ANY of these tags"),
          //(!A AND !B AND !C)
          Campaigns::TAG_HAS_NOT_ALL => t("Contact doesn't have ALL of these tags"),
          //at least one tag is missing
        ),
        '#weight' => $weight++,
        '#attributes' => array(
          'data-event-change' => 'ToggleTaggingQuickhelp',
          'data-event-change-args' => 'negative',
          'data-event-load' => 'ToggleTaggingQuickhelp',
          'data-event-load-args' => 'negative',
        ),
        '#prefix' => '<div class="tagging-dropdown-quickhelp">',
        '#suffix' => '<div class="quickhelps">' . theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => 'contact-not-tagged-with-all',
              '#title' => t("Quickhelp: Tag link"),
            )
          )) .
          theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => 'contact-not-tagged-with-any',
              '#title' => t("Quickhelp: Tag link"),
            )
          )) . '</div></div>',
      );

      $form['form_wrapper']['Tagging']['NotTaggedWith'] = array(
        '#type' => 'textfield',
        '#default_value' => $CampaignRecipients['NotTaggedWith'],
        '#weight' => $weight++,
        '#magic_select' => array(
          'autocomplete' => '',
          'options' => $RecipientsOptions,
          'multiple' => TRUE,
          'free_entries' => TRUE,
          'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
          'allow_numeric_only' => FALSE,
          'numeric_only_message' => t('Tags cannot contain only numbers.'),
          'form_post' => $form_state['input']['NotTaggedWith'],
          //catch free entries on form validation error
          'filter' => $TagFilter,
          'container_class' => 'ms-tag-color-negative',
          'maxSelection' => 50,
          'maxSelection_message' => t('The maximum number of positive and negative tags is !max.', array('!max' => 50)),
          'groupID' => 'campaign-recipients',
        ),
        '#theme' => 'klicktipp_magic_select',
      );

      if (!$IsAutoResponder) {
        // audiences are only eligible for campaigns
        $audienceOptions = VarSubscriberAudience::RetrieveAudiencesAsOptionsArray($UserID);
        if (!empty($audienceOptions)) {
          $form['form_wrapper']['SubscriberAudience'] = array(
            '#type' => 'fieldset',
            '#title' => t("Subscriber Audience"),
            '#weight' => $weight++,
            '#collapsible' => TRUE,
            '#collapsed' => (empty($CampaignInformation['SubscriberAudience'])),
            '#attributes' => array(
              'data-event-show-collapsed' => 'js_toggle_recipient_mode',
              'data-event-show-collapsed-args' => '.recipient-mode-audience',
              'class' => array('recipient-mode-audience'),
            ),
          );

          $defaultOption = array(0 => t('Please select'));
          // use + operator to not rearrange the keys
          $audienceOptions = $defaultOption + $audienceOptions;
          $form['form_wrapper']['SubscriberAudience']['Audience'] = array(
            '#type' => 'select',
            '#default_value' => (empty($CampaignInformation['SubscriberAudience'])) ? '' : $CampaignInformation['SubscriberAudience'],
            '#options' => $audienceOptions,
            '#title' => t("Subscriber Audience"),
            '#weight' => $weight++,
          );

        }
      }

    }

  }
  else {
    if (!$IsAutoResponder && empty($CampaignRecipients['TaggedWith']) && empty($CampaignRecipients['NotTaggedWith']) && empty($CampaignInformation['SubscriberAudience'])) {
      //sent or ready newsletter without tags

      $message = ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_READY) ? t('The newsletter will be sent to every active contact in your account.') : t('The newsletter was sent to every active contact in your account.');
      $form['form_wrapper']['TaggedWith'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_info',
        '#value' => $message,
        '#weight' => $weight++,
      );


    }
    elseif (!empty($CampaignInformation['SubscriberAudience'])) {
      $Audience = VarSubscriberAudience::RetrieveAudience($UserID, $CampaignInformation['SubscriberAudience']);
      if (empty($Audience)) {
        $message = t('The newsletter was sent to every active contact in your selected audience.');
      }
      else {
        $message = t('The newsletter will be sent to every active contact in your selected audience !name.', array(
          '!name' => $Audience['VarcharIndexed'],
        ));
      }
      $form['form_wrapper']['Audience'] = array(
        '#type' => 'item',
        '#value' => "<p>$message</p>",
        '#title' => t("Subscriber Audience"),
        '#weight' => $weight++,
      );
    }
    else {

      // already sent with perhaps removed tags

      function tagHtml($carry, $tagInfo) {
        [$tagName, $tagID, $extraClass] = $tagInfo;
        if (empty($tagName)) {
          $extraClass = '';
          $tagName = t('Removed tag with id #@id', ['@id' => $tagID]);
        }
        return $carry . '<span class="label label-default tag-as-label ' . $extraClass . '">' . $tagName . '</span>';
      }

      if (!empty($CampaignRecipients['TaggedWith'])) {

        $therecepients = array();
        foreach ($CampaignRecipients['TaggedWith'] as $EachList) {
          $therecepients[] = [$RecipientsOptions[$EachList], $EachList, "positive"];
        }

        $DisplayTags = '<span class="tagged-labels form-control">' . array_reduce($therecepients, "tagHtml", '') . '</span>';

        $form['form_wrapper']['TaggedWith'] = array(
          '#type' => 'item',
          '#title' => t("Tagged with"),
          '#value' => "<p>" . (($CampaignRecipients['OpTaggedWith'] == Campaigns::TAG_HAS_ANY) ? t("Contact has ANY of these tags") : t("Contact has ALL of these tags")) . "<br />$DisplayTags</p>",
          '#weight' => $weight++,
          '#quickhelp' => ($CampaignRecipients['OpTaggedWith'] == Campaigns::TAG_HAS_ANY) ? 'contact-tagged-with-any' : 'contact-tagged-with-all',
        );

      }

      if (!empty($CampaignRecipients['NotTaggedWith'])) {

        $therecepients = array();
        foreach ($CampaignRecipients['NotTaggedWith'] as $EachList) {
          $therecepients[] = [$RecipientsOptions[$EachList], $EachList, "negative"];
        }

        $DisplayTags = '<span class="tagged-labels form-control">' . array_reduce($therecepients, "tagHtml", '') . '</span>';

        $form['form_wrapper']['NotTaggedWith'] = array(
          '#type' => 'item',
          '#title' => t("Not tagged with"),
          '#value' => "<p>" . (($CampaignRecipients['OpNotTaggedWith'] == Campaigns::TAG_HAS_NOT_ANY) ? t("Contact doesn't have ANY of these tags") : t("Contact doesn't have ALL of these tags")) . "<br />$DisplayTags</p>",
          '#weight' => $weight++,
          '#quickhelp' => ($CampaignRecipients['OpNotTaggedWith'] == Campaigns::TAG_HAS_NOT_ANY) ? 'contact-not-tagged-with-any' : 'contact-not-tagged-with-all',
        );

      }

    }
  }

  // --- Edit receiver email address ---

  if ($IsAutoResponder && (!$IsSMS || user_access('administer klicktipp')) ) {

    $form['form_wrapper']['ReceiverEmailFlag'] = array(
      '#type' => 'checkbox',
      '#title' => t('Send all emails to same receiver') . theme('klicktipp_quickhelp', array(
          'element' => array(
            '#quickhelp' => ($IsSMS) ? 'create-edit-sms-receiver-email' : 'create-edit-email-receiver-email',
            '#title' => t('Send all emails to same receiver'),
          )
        )),
      '#disabled' => !user_access('use receiver email', $account),
      '#default_value' => !empty($CampaignInformation['ReceiverEmail']),
      '#weight' => $weight++,
    );

    if ($edit_receiver_email) {
      $EmailAddresses = VarAdditionalAddresses::GetSenderReplyOptions($account);
      $receivers = ['' => t('Please select')] + $EmailAddresses['reply'];

      $form['form_wrapper']['ReceiverEmail'] = array(
        '#type' => 'select',
        '#options' => $receivers,
        '#title' => t('Receiver email address'),
        '#default_value' => $CampaignInformation['ReceiverEmail'],
        '#weight' => $weight++,
        '#quickhelp' => ($IsSMS) ? 'create-edit-sms-receiver-email' : 'create-edit-email-receiver-email',
        '#description' => t('Send all emails to this receiver (e.g. a ticket system) instead of the subscriber.'),
        '#prefix' => '<div id="ReceiverEmailTable">',
        '#suffix' => '</div>',
      );
    }

  }

  // --- Edit MultipleSend ---

  if ($edit_multiple_send) {

    if ($insert) {

      $MultipleSendFlagTitle = $IsSMS ? t('Send SMS multiple times') : t('Send email multiple times');
      $form['form_wrapper']['MultipleSendFlag'] = array(
        '#type' => 'checkbox',
        '#title' => $MultipleSendFlagTitle . theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => 'create-edit-email-multiple-send',
              '#title' => $MultipleSendFlagTitle,
            )
          )),
        '#default_value' => !empty($CampaignInformation['MultipleSendFlag']),
        '#weight' => $weight++,
      );

      $form['form_wrapper']['MultipleSendTags'] = array(
        '#type' => 'textfield',
        '#title' => t("Untag after send"),
        '#default_value' => $CampaignInformation['MultipleSendTags'],
        '#weight' => $weight++,
        '#magic_select' => array(
          'autocomplete' => '',
          'options' => $RecipientsOptions,
          'multiple' => TRUE,
          'free_entries' => TRUE,
          'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
          'allow_numeric_only' => FALSE,
          'numeric_only_message' => t('Tags cannot contain only numbers.'),
          'form_post' => $form_state['input']['MultipleSendTags'],
          //catch free entries on form validation error
          'filter' => $TagFilter,
          'container_class' => 'ms-tag-color-positive',
        ),
        '#theme' => 'klicktipp_magic_select',
        '#prefix' => '<div id="MultipleSendTagsTable">',
        '#suffix' => '</div>',
      );

    }
    elseif (!empty($CampaignInformation['MultipleSendFlag'])) {

      $multiplesendtags = array();
      foreach ($CampaignInformation['MultipleSendTags'] as $EachList) {
        $multiplesendtags[] = $RecipientsOptions[$EachList];
      }

      $DisplayTags = '<span class="tagged-labels form-control"><span class="label label-default tag-as-label positive">' . implode('</span><span class="label label-default tag-as-label positive">', $multiplesendtags) . '</span></span>';

      $form['form_wrapper']['MultipleSendTags'] = array(
        '#type' => 'item',
        '#title' => t("Send email multiple times"),
        '#value' => "<p>" . t("Untag after send") . "<br />$DisplayTags</p>",
        '#weight' => $weight++,
        '#quickhelp' => 'email-multiple-send',
      );

    }
  }

  // --- Edit Splittesting ---

  if ($edit_splittest && !$IsAutoResponder) {
    // edit splittest settings
    // newsletter only

    if ($insert) {
      //allow the user to activate splittesting only for new campaign

      $splittestString = ($IsSMS) ? t('Splittest this SMS') : t('Splittest this email');
      $form['form_wrapper']['SplitTesting'] = array(
        '#type' => 'checkbox',
        '#title' => $splittestString . theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => ($IsSMS) ? 'create-edit-sms-splittest' : 'create-edit-email-splittest',
              '#title' => $splittestString,
            )
          )),
        '#default_value' => FALSE,
        '#weight' => $weight++,
      );
    }

    //--- splittest size

    $TestSizeDefault = $insert ? 20 : $SplittestObject->GetData('TestSize');
    $form['form_wrapper']['TestSize'] = array(
      '#type' => 'textfield',
      '#title' => t('Test size'),
      '#quickhelp' => ($IsSMS) ? 'create-edit-sms-splittest-size' : 'create-edit-email-splittest-size',
      '#default_value' => $TestSizeDefault,
      '#attributes' => array(
        'data-slider-min' => 2,
        'data-slider-max' => 98,
        'data-slider-step' => 1,
        'data-slider-value' => $TestSizeDefault,
        'data-slider-orientation' => 'horizontal',
        'data-slider-selection' => 'none',
        'data-slider-tooltip' => 'show',
        'style' => 'width: 300px;',
        'class' => array('is-slider'),
      ),
      '#prefix' => '<div id="TestSizeTable">',
      '#suffix' => '</div>',
      '#weight' => $weight++,
    );

    // --- splittest duration

    if ($insert) {
      //default values for new campaigns
      $TestDurationBaseSecondsDefault = 3600;
      $TestDurationMultiplierDefault = 1;
    }
    else {
      //calculate stored value (seconds) back to hours, days and month
      $TestDurationBaseSecondsDefault = 3600;
      $TestDurationMultiplierDefault = $SplittestObject->GetData('TestDuration') / $TestDurationBaseSecondsDefault;

      if ($TestDurationMultiplierDefault > 23 && ($SplittestObject->GetData('TestDuration') % 86400 == 0)) {
        $TestDurationBaseSecondsDefault = 86400;
        $TestDurationMultiplierDefault = $SplittestObject->GetData('TestDuration') / $TestDurationBaseSecondsDefault;
        if ($TestDurationMultiplierDefault > 59 && ($SplittestObject->GetData('TestDuration') % 2592000 == 0)) {
          $TestDurationBaseSecondsDefault = 2592000;
          $TestDurationMultiplierDefault = $SplittestObject->GetData('TestDuration') / $TestDurationBaseSecondsDefault;
        }
      }

    }

    $TestDurationBaseSecondsOptions = array(
      3600 => t('Hours'),
      86400 => t('Days'),
      2592000 => t('Months'),
    );

    $form['form_wrapper']['TestDurationMultiplier'] = array(
      '#type' => 'textfield',
      '#title' => t('Test duration'),
      '#default_value' => $TestDurationMultiplierDefault,
      '#size' => 6,
      '#prefix' => '<div id="TestDurationMultiplierTable" class="row row-element"><div class="col-md-1 col-sm-2 col-xs-3 row-element" style="z-index: 2;">',
      '#suffix' => '</div>',
      '#weight' => $weight++,
      '#quickhelp' => 'create-edit-email-splittest-duration',
    );

    $form['form_wrapper']['TestDurationBaseSeconds'] = array(
      '#type' => 'select',
      '#title' => ' ',
      '#default_value' => $TestDurationBaseSecondsDefault,
      '#options' => $TestDurationBaseSecondsOptions,
      '#prefix' => '<div class="col-md-11 col-sm-10 col-xs-9 row-element hide-label">',
      '#suffix' => '</div></div>',
      '#weight' => $weight++,
    );

    // --- splittest winner

    if (count($SplittestWinnerOptions) == 1) {
      // dont show dropdown if only one value to select (SMS in standard accounts)
      $form['form_wrapper']['DisplayWinner'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_item',
        '#title' => t('Winner is'),
        '#quickhelp' => 'create-edit-sms-splittest-winner',
        '#value' => reset($SplittestWinnerOptions),
        '#prefix' => '<div id="TestWinnerTable">',
        '#suffix' => '</div>',
        '#weight' => $weight++,
      );
      $keys = array_keys($SplittestWinnerOptions);
      $form['form_wrapper']['WinnerEnum'] = array(
        '#type' => 'value',
        '#value' => reset($keys),
      );
    }
    else {
      $form['form_wrapper']['WinnerEnum'] = array(
        '#type' => 'select',
        '#title' => t('Winner is'),
        '#quickhelp' => 'create-edit-email-splittest-winner',
        '#default_value' => $SplittestObject ? $SplittestObject->GetData('WinnerEnum') : SplitTests::WINNER_OPENS,
        '#options' => $SplittestWinnerOptions,
        '#prefix' => '<div id="TestWinnerTable">',
        '#suffix' => '</div>',
        '#weight' => $weight++,
      );
    }

  }
  else {
    if (!empty($CampaignInformation['SplittestID'])) {
      //already sent splittest campaign, display splittest settings

      if (!$IsAutoResponder) {
        //display test size only for newsletters

        $form['form_wrapper']['DisplayTestSize'] = array(
          '#type' => 'item',
          '#theme' => 'klicktipp_item',
          '#title' => t('Test size'),
          '#value' => $SplittestObject->GetData('TestSize') . '%',
          '#weight' => $weight++,
          '#size' => 6,
          '#attributes' => array('class' => array('input-short')),
          '#quickhelp' => ($IsSMS) ? 'create-edit-sms-splittest-size' : 'create-edit-email-splittest-size',
        );

      }

      //calculate stored value (seconds) back to hours, days and month
      $TestDurationBaseSecondsDefault = 3600;
      $TestDurationMultiplierDefault = $SplittestObject->GetData('TestDuration') / $TestDurationBaseSecondsDefault;

      if ($TestDurationMultiplierDefault > 23 && ($SplittestObject->GetData('TestDuration') % 86400 == 0)) {
        $TestDurationBaseSecondsDefault = 86400;
        $TestDurationMultiplierDefault = $SplittestObject->GetData('TestDuration') / $TestDurationBaseSecondsDefault;
        if ($TestDurationMultiplierDefault > 59 && ($SplittestObject->GetData('TestDuration') % 2592000 == 0)) {
          $TestDurationBaseSecondsDefault = 2592000;
          $TestDurationMultiplierDefault = $SplittestObject->GetData('TestDuration') / $TestDurationBaseSecondsDefault;
        }
      }

      $DisplayDuration = $TestDurationMultiplierDefault . ' ';
      if ($TestDurationBaseSecondsDefault == 3600) {
        $DisplayDuration .= ($TestDurationMultiplierDefault > 1) ? t('Hours') : t('Hour');
      }
      else {
        if ($TestDurationBaseSecondsDefault == 86400) {
          $DisplayDuration .= ($TestDurationMultiplierDefault > 1) ? t('Days') : t('Day');
        }
        else {
          if ($TestDurationBaseSecondsDefault == 2592000) {
            $DisplayDuration .= ($TestDurationMultiplierDefault > 1) ? t('Months') : t('Month');
          }
        }
      }

      $form['form_wrapper']['DisplayTestDuration'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_item',
        '#title' => t('Test duration'),
        '#value' => $DisplayDuration,
        '#weight' => $weight++,
        '#quickhelp' => 'create-edit-email-splittest-duration',
      );

      $form['form_wrapper']['DisplayWinner'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_item',
        '#title' => t('Winner is'),
        '#value' => $SplittestWinnerOptions[$SplittestObject->GetData('WinnerEnum')],
        '#weight' => $weight++,
        '#quickhelp' => ($IsSMS) ? 'create-edit-sms-splittest-winner' : 'create-edit-email-splittest-winner',
      );

    }
  }

  if (!$insert && user_access('access conversion pixel', $account)) {

    $ArrayUrls = DomainSet::GetPixelUrls($account);

    if (!$IsSMS) { // for emails

      $EmailIDs = array();
      if ($edit_splittest) { // split test
        foreach ($SplittestObject->GetData('Variants') as $Variant) {
          $EmailIDs[] = $Variant['EntityID'];
        }
      } else { // no split test
        $EmailIDs[] = $CampaignInformation['RelEmailID'];
      }

      $ArrayUrls = klicktipp_campaign_filter_pixel_urls_by_email_sender_domains($UserID, $ArrayUrls, $EmailIDs);

    }

    if (!empty($ArrayUrls)) {

      $CP = array(
        'UserID' => $CampaignInformation['RelOwnerUserID'],
        'CampaignID' => $CampaignInformation['CampaignID'],
      );

      $form['form_wrapper']['PixelSnippets'] = array(
        '#type' => 'markup',
        '#weight' => $weight++,
        '#prefix' => '<div class="pixel-snippets">',
        '#suffix' => '</div>',
      );

      if (count($ArrayUrls) > 1) {

        $form['form_wrapper']['PixelSnippets']['Info'] = array(
          '#type' => 'item',
          '#theme' => 'klicktipp_info',
          '#weight' => $weight++,
          '#title' => t('Conversion Pixel'),
          '#quickhelp' => 'edit-conversion-pixel',
          '#description' => t('CONVERSION_PIXEL::Please use the conversion pixel snippet corresponding to your sending domain.'),
        );

        foreach ($ArrayUrls as $WihtelabelDomain => $Url) {
          $snippet = "<img src='".Core::EncryptURL($CP, 'conversion', $Url)."' height='1' width='1' />";
          $form['form_wrapper']['PixelSnippets']['WhiteLabelDomain_'.uniqid()] = array(
            '#type' => 'item',
            '#theme' => 'klicktipp_item',
            '#weight' => $weight++,
            '#value' => htmlspecialchars($snippet),
            '#description' => t('CONVERSION_PIXEL::Snippet for sender domain: !domain', array('!domain' => $WihtelabelDomain)),
            '#attributes' => array(
              'data-event-click' => 'js_select_text',
              'class' => array('conversion-pixel-snippet')
            )
          );
        }

      } else {
        $snippet = "<img src='".Core::EncryptURL($CP, 'conversion', current($ArrayUrls))."' height='1' width='1' />";
        $form['form_wrapper']['PixelSnippets']['Default'] = array(
          '#type' => 'item',
          '#theme' => 'klicktipp_item',
          '#title' => t('Conversion Pixel'),
          '#quickhelp' => 'edit-conversion-pixel',
          '#weight' => $weight++,
          '#value' => htmlspecialchars($snippet),
          '#description' => t('CONVERSION_PIXEL::Snippet for sender domain: !domain', array('!domain' => key($ArrayUrls))),
          '#attributes' => array(
            'data-event-click' => 'js_select_text',
            'class' => array('conversion-pixel-snippet')
          )
        );
      }

    }

  };

  if ($insert && $insert_type == Campaigns::TRIGGER_TYPE_CAMPAIGN) {
    $form['form_wrapper']['SendingMode'] = [
      '#type' => 'radios',
      '#title' => t('#campaign-sending-mode') . theme('klicktipp_quickhelp', array(
          'element' => array(
            '#quickhelp' => 'campaign-sending-mode',
            '#title' => t('#campaign-sending-mode-quick-help-title'),
          )
        )),
      '#options' => [
        'only-one' => t('#send-to-only-one-subscription'),
        'all' => t('#send-to-all-subscriptions')
      ],
      '#default_value' => 'only-one',
      '#weight' => $weight++,
    ];
  }

  $form['form_wrapper']['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['form_wrapper']['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => ($insert) ? $page_title : t('Save'),
    '#weight' => $weight++,
  );

  if (!$insert && klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER)) {

    $ModalID = 'klicktipp_campaign_delete_confirm_form';

    if ($IsAutoResponder) {
      $Title = t('Delete Autoresponder');
      $DependencyMessage = t('This Autoresponder is used in the following objects and cannot be deleted:');
    } else {
      $Title = t('Delete Newsletter');
      $DependencyMessage = t('This Newsletter is is used in the following objects and cannot be deleted:');
    }

    $modalFormParameters = [
      "modalTitle" => $Title,
      "modalId" => $ModalID,
      "dependencyMessage" => $DependencyMessage,
      "entityId" => $CampaignID,
      "entityClass" => CampaignsNewsletter::class,
      "entityName" => $CampaignInformation['CampaignName'],
      "entity" => $CampaignInformation,
      "submitFunction" => "klicktipp_campaign_delete_confirm_form_submit",
    ];

    // will include the generic modal form "klicktipp_delete_modal_form"
    Libraries::include("form_delete_modal.inc");

    $form['form_wrapper']['buttons']['ModalDeleteConfirmTrigger'] = [
      '#theme' => 'klicktipp_delete_modal_ajax_button',
      '#title' => $Title,
      '#value' => $ModalID,
      '#weight' => $weight++,
      '#modal_confirm' => drupal_get_form("klicktipp_delete_modal_form", $account, $modalFormParameters),
    ];

  }

  $form['form_wrapper']['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    // NOTE: MigrationLinks -> will no longer exist
    '#value' => ($IsAutoResponder) ? "autoresponders/$UserID" : "campaigns/$UserID",
    '#weight' => $weight++,
  );

  $form['#pre_render'][] = 'klicktipp_campaign_form_pre_render';
  return $form;
}

/**
 * campaign edit form pre render
 * this prevents the script from beeing cached on submit (espacially errors)
 */
function klicktipp_campaign_form_pre_render($form) {

  if (empty($form['insert']['#value'])) {
    // edit
    $CampaignInformation = $form['CampaignInformation']['#value'];
    $SplittestCheck = $CampaignInformation['SplittestID'] ? ' return true ' : ' return false ';
  }
  else {
    // insert
    $SplittestCheck = ' return $(".edit-SplitTesting").length && $(".edit-SplitTesting").get(0).checked';
  }


  drupal_add_js('

  function with_splittest() {' . $SplittestCheck . '}

  function klicktipp_slider_callback(e, ui) {
    $(".edit-TestSize.is-slider").slider("setValue", ui.value);
  }

  function on_receiveremail_change() {

    if ($(".edit-ReceiverEmailFlag").length && $(".edit-ReceiverEmailFlag").get(0).checked) {
      $("#ReceiverEmailTable").show();
    }
    else {
      $("#ReceiverEmailTable").hide();
    }

    if (window["update_view"])
        window["update_view"]();
  }

  function on_multiplesend_change() {

    if ($(".edit-MultipleSendFlag").length && $(".edit-MultipleSendFlag").get(0).checked) {
      $("#MultipleSendTagsTable").show();
      $(".edit-SplitTesting").prop("disabled", true);
      if (window["MagicSelects"]) {
        if (window["MagicSelects"]["#edit-multiplesendtags"]) {
          var untagwith = window["MagicSelects"]["#edit-multiplesendtags"].getValue();
          if (!untagwith.length && window["MagicSelects"]["#edit-taggedwith"]) {
            var taggedwith = window["MagicSelects"]["#edit-taggedwith"].getValue();
            if (taggedwith.length)
              window["MagicSelects"]["#edit-multiplesendtags"].setValue(taggedwith);
          }
        }
      }
    }
    else {
      $("#MultipleSendTagsTable").hide();
      $(".edit-SplitTesting").prop("disabled", false);
    }

    if (window["update_view"])
        window["update_view"]();
  }

  function on_splittest_change() {

    if (with_splittest()) {
      $("#TestSizeTable").show();
      $("#TestDurationMultiplierTable").show();
      $("#TestWinnerTable").show();
      $(".edit-MultipleSendFlag").prop("disabled", true);
    }
    else {
      $("#TestSizeTable").hide();
      $("#TestDurationMultiplierTable").hide();
      $("#TestWinnerTable").hide();
      $(".edit-MultipleSendFlag").prop("disabled", false);
    }

    if (window["update_view"])
        window["update_view"]();
  }

  function on_trigger_time_type_change() {

    value = $(".edit-TriggerTimeTypeEnum").val();

    $("#TriggerTimeDayOfWeekTable").hide();
    $("#DayOfWeekTimeDelayTable").hide();
    $(".edit-UseTriggerDayOfWeek-wrapper").hide();
    $(".edit-UseDayOfWeekTimeDelay-wrapper").hide();

    if (value == "' . Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY . '") {
      $("#TriggerTimeValue").hide();
      $(".edit-TriggerTimeTypeEnum").siblings("label").css("visibility", "visible");
      $(".edit-DelayByCustomFieldDatetimeFieldID-wrapper").hide();
    }
    else if (value == "' . Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD . '") {
      $("#TriggerTimeValue").hide();
      $(".edit-TriggerTimeTypeEnum").siblings("label").css("visibility", "visible");
      $(".edit-DelayByCustomFieldDatetimeFieldID-wrapper").show();
    }
    else {

      $("#TriggerTimeValue").show();
      $(".edit-TriggerTimeTypeEnum").siblings("label").css("visibility", "hidden");
      $(".edit-DelayByCustomFieldDatetimeFieldID-wrapper").hide();

      if (value == "' . Campaigns::TRIGGER_TIME_TYPE_DAYS . '" ||
          value == "' . Campaigns::TRIGGER_TIME_TYPE_WEEKS . '" ||
          value == "' . Campaigns::TRIGGER_TIME_TYPE_MONTHS . '" ) {

        $(".edit-UseTriggerDayOfWeek-wrapper").show();
        $(".edit-UseDayOfWeekTimeDelay-wrapper").show();
        window["js_checkbox_toggle_element_display"]($(".edit-UseTriggerDayOfWeek"), "#TriggerTimeDayOfWeekTable");
        window["js_checkbox_toggle_element_display"]($(".edit-UseDayOfWeekTimeDelay"), "#DayOfWeekTimeDelayTable");

      }

    }

    if (window["update_view"])
      window["update_view"]();

  }

  window["js_select_weekday"] = function (element, args) {

    if ( args == "click" ) {

      //click on checkbox wrapper -> toggle checked status
      element.prop("checked", !element.prop("checked"));

    }
    else {

      //document load, add click event to checkbox wrapper
      element.parent("div.form-item.form-type-checkbox").click(function () {
        window["js_select_weekday"](element, "click");
      });

    }

    //set selected styles for checkbox wrapper depending on check status
    if ( element.prop("checked") ) {
      element.parent("div.form-item.form-type-checkbox").addClass("weekday-selected");
    }
    else {
      element.parent("div.form-item.form-type-checkbox").removeClass("weekday-selected");
    }

  }

  window["ToggleTaggingQuickhelp"] = function (element, args) {

    if ( args == "positive" ) {

        if ( element.val() == ' . Campaigns::TAG_HAS_ANY . ') {
          $("#quickhelp-contact-tagged-with-any").show();
          $("#quickhelp-contact-tagged-with-all").hide();
        }
        else {
          $("#quickhelp-contact-tagged-with-any").hide();
          $("#quickhelp-contact-tagged-with-all").show();
        }

    }
    else {

        if ( element.val() == ' . Campaigns::TAG_HAS_NOT_ALL . ') {
            $("#quickhelp-contact-not-tagged-with-all").show();
            $("#quickhelp-contact-not-tagged-with-any").hide();
        }
        else {
            $("#quickhelp-contact-not-tagged-with-all").hide();
            $("#quickhelp-contact-not-tagged-with-any").show();
        }

    }

  }

  window["js_toggle_recipient_mode"] = function ( element, args ) {

    var tagging_fieldset = $(".recipient-mode-tagging a.accordion-toggle");
    var audience_fieldset = $(".recipient-mode-audience a.accordion-toggle");
    var clear_inputs = false;

    if ( tagging_fieldset.length != 1 || audience_fieldset.length != 1 )
      return;

    if ( args == ".recipient-mode-tagging" && !audience_fieldset.hasClass("collapsed") ) {
        audience_fieldset.trigger("click");
        $("#edit-audience").find("option:first").prop("selected", "selected");
        clear_inputs = true;
    }
    else if ( args == ".recipient-mode-audience" && !tagging_fieldset.hasClass("collapsed") ) {
        tagging_fieldset.trigger("click");
        clear_inputs = true;
    }

    if (clear_inputs) {
      $(args).find("input").val("");
      $(args).find("select").find("option:first").prop("selected", "selected");
      $(args).find(".klicktipp-magicselect").each(function () {
        var msid = $(this).attr("data-magicselect");
        if (MagicSelects[msid])
          MagicSelects[msid].clear();
      });
    }

  };

  $(document).ready(function() {


    if ( $(".edit-SplitTesting").length ) {

      $(".edit-SplitTesting").click(function () {

        on_splittest_change();

        if ( $(".edit-TestSize.is-slider").length )
          $(".edit-TestSize.is-slider").slider("update");

      });

    }

    if ( $(".edit-TriggerTimeTypeEnum").length ) {
      $(".edit-TriggerTimeTypeEnum").change(on_trigger_time_type_change);
    }

    if ( $(".edit-TestSize.is-slider").length ) {
      $(".edit-TestSize.is-slider").slider(
       {
         "formater" : SliderFormatter_To_Percent
       }
      ).on("slide", function(ev) {
        klicktipp_slider_callback(0, ev);
      }).on("slideStop", function (ev) {
        klicktipp_slider_callback(0, ev);
      });

    }

    if ( $(".edit-ReceiverEmailFlag").length ) {
      $(".edit-ReceiverEmailFlag").click(on_receiveremail_change);
    }

    if ( $(".edit-MultipleSendFlag").length ) {
      $(".edit-MultipleSendFlag").click(on_multiplesend_change);
    }

    on_splittest_change();
    on_trigger_time_type_change();
    on_receiveremail_change();
    on_multiplesend_change();

    $(".edit-TestSize.is-slider").slider("update");

    if (window["update_view"])
      window["update_view"]();

  });

  ', array('type' => 'inline', 'scope' => 'footer'));

  return $form;
}

/**
 * Implements hook_form_validate().
 * ().
 */
function klicktipp_campaign_form_validate($form, &$form_state) {

  $account = $form_state['values']['account'];
  $CampaignInformation = $form_state['values']['CampaignInformation'];
  $CampaignID = $CampaignInformation['CampaignID'];
  $IsAutoresponder = $form_state['values']['IsAutoResponder'];
  $insert = $form_state['values']['insert']; //is a new campaign being created
  $edit_campaign = $form_state['values']['EditCampaign']; //is the campaign editable with the current status
  $edit_splittest = $form_state['values']['EditSplittest']; // is the campaign a splittest campaign and is it editable (Status and user_access)
  $IsSplittest = (!empty($form_state['values']['SplitTesting']) || $edit_splittest); //new splittest campaign or existing one
  $IsMultipleSend = !empty($form_state['values']['MultipleSendFlag']) || !empty($CampaignInformation['MultipleSendFlag']);

  //validate name (not empty and doesn't exist)
  if (empty($form_state['values']['title'])) {
    form_set_error('title', t('Name is required.'));
  }
  else {
    $form_state['values']['title'] = kt_strip_utf8mb4($form_state['values']['title']);

    if ($insert || ($form_state['values']['title'] != $CampaignInformation['CampaignName'])) {
      if (CampaignsNewsletter::CheckDuplicateName($account->uid, $form_state['values']['title'])) {
        form_set_error('title', t('An email with this name already exists.'));
      }
    }
  }

  if ($insert || $edit_campaign) {

    //validate autoresponder trigger time (delay) and autorespnder start tags

    if ($form_state['values']['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SUBSCRIPTION ||
      $form_state['values']['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION
    ) {

      if ( in_array($form_state['values']['TriggerTimeTypeEnum'], array(
        Campaigns::TRIGGER_TIME_TYPE_DAYS, Campaigns::TRIGGER_TIME_TYPE_WEEKS, Campaigns::TRIGGER_TIME_TYPE_MONTHS) )) {

        if ( !empty($form_state['values']['UseTriggerDayOfWeek']) ) {

          $selected = array_values(array_filter($form_state['values']['TriggerDayOfWeek']));

          if ( empty($selected) ) {
            form_set_error('TriggerDayOfWeek', t("At least 1 weekday is required."));
          }

        }

      }
      elseif (!in_array($form_state['values']['TriggerTimeTypeEnum'],
        array(
          Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY,
          Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD,
        ))
      ) {
        if (empty($form_state['values']['TriggerTime'])) {
          form_set_error('TriggerTime', t("Please specify the time delay or choose 'Immediately'"));
        }
        elseif (intval($form_state['values']['TriggerTime']) < 1) {
          form_set_error('TriggerTime', t("Invalid time delay"));
        }
        elseif (intval($form_state['values']['TriggerTime']) > 1000) {
          form_set_error('TriggerTime', t("Invalid time delay"));
        }
        else {
          //check if the time delay is greater than 2 years
          $CheckCampaign = array(
            'TriggerTimeTypeEnum' => $form_state['values']['TriggerTimeTypeEnum'],
            'TriggerTime' => $form_state['values']['TriggerTime'],
          );
          $delaytime = AutoResponders::calculateSendTimeFromTriggerType(time(), $CheckCampaign); //from now
          $delaylimit = strtotime("+2 years"); //from now
          if ($delaytime > $delaylimit) {
            form_set_error('TriggerTime', t('The time delay cannot be greater than 2 years.'));
          }
        }
      }
    }

    if ($form_state['values']['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SUBSCRIPTION ||
      $form_state['values']['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION
    ) {
      if ($form_state['values']['TriggerTimeTypeEnum'] == Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD) {
        if (empty($form_state['values']['DelayByCustomFieldDatetimeFieldID'])) {
          form_set_error('DelayByCustomFieldDatetimeFieldID', t("Please specify a custom field that contains the start date and time."));
        }
      }
    }

    if ($form_state['values']['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_BIRTHDAY ||
      $form_state['values']['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY
    ) {
      if (empty($form_state['values']['BirthdayCustomFieldID'])) {
        form_set_error('BirthdayCustomFieldID', t("Please specify a custom field that contains the birthday date."));
      }
    }

    //validate tags
    if (array_intersect($form_state['values']['TaggedWith'], $form_state['values']['NotTaggedWith'])) {
      form_set_error('TaggedWith', t('There are tags selected both within "tagged with" and "not tagged with".'));
    }

    if (count($form_state['values']['TaggedWith']) + count($form_state['values']['NotTaggedWith']) > 50) {
      form_set_error('TaggedWith', t('The maximum number of positive and negative tags is !max.', array('!max' => 50)));
    }

    if ($IsSplittest && !$IsAutoresponder) {

      if (empty($form_state['values']['TestDurationMultiplier'])) {
        form_set_error('TestDurationMultiplier', t('Please specify a test duration.'));
      }
      else {
        if ($form_state['values']['TestDurationMultiplier'] * $form_state['values']['TestDurationBaseSeconds'] > 99999999) {
          // maximium is limited to 8 diggits, which is 1157 days
          form_set_error('TestDurationMultiplier', t('Test duration exceeds limit.'));

        }
      }
    }

    if ($IsMultipleSend) {

      if (empty($form_state['values']['TaggedWith'])) {
        form_set_error('TaggedWith', t('Please specify a precondition for this multiple send autoresponder.'));
      }

      if ($insert) {
        // MultipleSend is not changeable, so we need these checks on insert only
        // Note: as MultipleSend is a minor feature, all errors point to this field

        if (!empty($form_state['values']['SplitTesting'])) {
          // this shouldnt be possible, if javascript is working
          form_set_error('MultipleSendFlag', t('You can not splittest multiple send autoresponders.'));
        }

        if (empty($form_state['values']['MultipleSendTags'])) {
          form_set_error('MultipleSendTags', t('Please specify tags to reset the precondition after sending.'));
        }

        // matching preconditions with tags to reset
        $matching = array_intersect($form_state['values']['TaggedWith'], $form_state['values']['MultipleSendTags']);

        if (count($form_state['values']['MultipleSendTags']) != count($matching)) {
          // all MultipleSendTags must be contained in TaggedWith
          form_set_error('MultipleSendTags', t('The tags to reset must match the precondition.'));
        }

        if ($form_state['values']['OpTaggedWith'] == Campaigns::TAG_HAS_ANY) {
          if (count($form_state['values']['TaggedWith']) != count($matching)) {
            // HAS ANY: MultipleSendTags must reset all in TaggedWith
            form_set_error('MultipleSendTags', t('Please specify all precondition tags, so the precondition becomes reseted after sending.'));
          }
        }
      }
      else {
        // on update we just need to check the preconditions
        // Note: as only TaggedWith can be changed, all errors point to this field

        // matching preconditions with tags to reset
        $matching = array_intersect($form_state['values']['TaggedWith'], $CampaignInformation['MultipleSendTags']);

        if (count($CampaignInformation['MultipleSendTags']) != count($matching)) {
          // all MultipleSendTags must be contained in TaggedWith
          form_set_error('TaggedWith', t('The precondition must match the tags to reset the multiple send autoresponder.'));
        }

        if ($form_state['values']['OpTaggedWith'] == Campaigns::TAG_HAS_ANY) {
          if (count($form_state['values']['TaggedWith']) != count($matching)) {
            // HAS ANY: MultipleSendTags must reset all in TaggedWith
            form_set_error('TaggedWith', t('The precondition must match the tags to reset the multiple send autoresponder.'));
          }
        }
      }

    }
  }
}

/**
 * override submit redirection (there is no hook_submit)
 */
function klicktipp_campaign_form_submit($form, &$form_state) {

  $account = $form_state['values']['account'];
  $CampaignInformation = $form_state['values']['CampaignInformation'];
  $CampaignID = $CampaignInformation['CampaignID'];
  $CampaignName = $form_state['values']['title'];
  $IsAutoresponder = $form_state['values']['IsAutoResponder'];

  $message = "";
  if (empty($form_state['values']['insert'])) {
    if (klicktipp_campaign_event_save($form_state['values'])) {
      $message = ($IsAutoresponder) ? t('Autoresponder %name successfully updated.', array('%name' => $CampaignName)) : t('Newsletter %name successfully updated.', array('%name' => $CampaignName));
    }
  }
  else {
    $CampaignID = klicktipp_campaign_event_create($form_state['values']);
    if ($CampaignID) {
      $message = ($IsAutoresponder) ? t('Autoresponder %name successfully created.', array('%name' => $CampaignName)) : t('Newsletter %name successfully created.', array('%name' => $CampaignName));
    }
  }

  if (!empty($message)) {
    drupal_set_message($message);
  }
  else {
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }

  // redirect to edit emails when the Wizard is active and tell the dialog the wizard is active, otherwise stay in the dialog
  if ($form_state['values']['Wizard'] == KLICKTIPP_WIZARD_CREATE_EMAIL) {
    // NOTE: MigrationLinks -> will no longer exist
    klicktipp_set_redirect($form_state, "emails/{$account->uid}/$CampaignID/emails/" . $form_state['values']['Wizard']);
  }

}

/**
 * campaign view
 */
function klicktipp_campaign_view($account, $CampaignID) {

  $output = "";

  $UserID = $account->uid;
  $CampaignInformation = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);

  klicktipp_include_google_chart_api();

  $page_title = t('Overview');
  klicktipp_set_title($page_title);
  $breadcrumb_name = $CampaignInformation['CampaignName'];
  // NOTE: MigrationLinks -> will no longer exist
  $Overview = Campaigns::IsAutoresponder($CampaignInformation['AutoResponderTriggerTypeEnum']) ?
    l(t('Autoresponder Overview'), "autoresponders/$UserID") : l(t('Newsletter Overview'), "campaigns/$UserID");
  klicktipp_set_breadcrumb(array(
    $Overview,
    // NOTE: MigrationLinks -> will no longer exist
    l($breadcrumb_name, "emails/$UserID/$CampaignID")
  ), $page_title);

  $MostClickedLinks = Statistics::RetrieveCampaignLinksClicked($CampaignID, $UserID);

  //--- spam alert ---
  if ($CampaignInformation['TotalSent'] > 0) {

    $TotalSpamReports = $CampaignInformation['TotalSpamComplaints'];
    $SpamRate = $TotalSpamReports / $CampaignInformation['TotalSent'];

    if ($SpamRate >= CONTROLLER_CAMPAIGN_SPAM_TRESHOLD) {
      $output .= "
          <div id='ctrl-campaign-spam-alert' class='alert alert-danger'>
            <div class='spam-alert-content'>
                <h3>" . t('Attention: Your spam complaint rate is too high!') . "</h3>
                <p>" . /*DO not break this string*/t('This email has been sent to !sent subscribers. !spam subscribers have complained that this email is spam. This results in a spam complaint rate of !percent percent. In the future, please make sure that your spam complaint rate does not exceed 0.1 percent (i.e. maximum one out of 1000 subscribers complains that your email is spam).',
          array(
            '!sent' => $CampaignInformation['TotalSent'],
            '!spam' => $TotalSpamReports > 0 ? $TotalSpamReports : '0',
            '!percent' => klicktipp_number_format($SpamRate * 100, 2),
          )) . "</p>
            </div>
          </div>
        ";

    }
  }

  // --- Status bar ---
  $output .= klicktipp_campaign_status_bar($account, $CampaignID, $CampaignInformation);

  //Campaigns in status DRAFT do not have statistic data -> do not show charts
  if ($CampaignInformation['CampaignStatusEnum'] != Campaigns::STATUS_DRAFT) {

    $TotalSent = $CampaignInformation['TotalSent'];
    $TotalBounces = $CampaignInformation['TotalHardBounces'] + $CampaignInformation['TotalSoftBounces'];
    $Delivered = max($TotalSent - $TotalBounces, 0);
    $DeliveredRatio = klicktipp_number_format(($Delivered / $TotalSent * 100), 2) . "%";

    // opens
    $Opens = $CampaignInformation['UniqueOpens'];
    $Unopens = $TotalSent - $Opens;

    // clicks
    if (Campaigns::IsSMSCampaign($CampaignInformation['AutoResponderTriggerTypeEnum'])) {
      $DataTotal = $TotalSent;
      $DataTotalLabel = t('<strong>!sent</strong> sent', array('!sent' => $DataTotal));
    }
    else {
      $DataTotal = $Opens;
      $DataTotalLabel = t('<strong>!opens</strong> opens', array('!opens' => $DataTotal));
    }
    $Clicks = $CampaignInformation['UniqueClicks'];
    $NotClicked = $DataTotal - $Clicks;
    $Conversions = empty($CampaignInformation['UniqueConversions']) ? 0 : $CampaignInformation['UniqueConversions'];
    $NotConverted = $DataTotal - $Conversions;

    $ShowDeliveredStats = (empty($account->UserSettings['ShowAdvancedEmailStats'])) ? 0 : 1;
    $chart_name = 'campaign_stats_hbar';
    $chart_info = array(
      'uid' => $UserID,
      'entity' => $CampaignInformation['CampaignID'],
      'name' => $chart_name,
      'advanced' => $ShowDeliveredStats,
    );
    // calc no of bars (see ktcharts_campaign_stats_hbar)
    // minimum is sent, clicked, bounces + footer
    $bars = 4;
    if (!Campaigns::IsSMSCampaign($CampaignInformation['AutoResponderTriggerTypeEnum'])) {
      // opened, unsubscribes, spam complaints
      $bars += 3;
      if ($ShowDeliveredStats) {
        // delivered
        $bars++;
      };
    }
    if (user_access('access conversion pixel', $account)) {
      // conversions
      $bars++;
    }
    $output .= '
             <div id="ctrl-campaign-statistics-chart" class="chart-bars-' . $bars . '"><div id="' . $chart_name . '" style="width:100%;">
                 <script type="text/javascript">
                   google.setOnLoadCallback(function () { ktcharts_load_stacked_hbar_chart(' . json_encode($chart_info) . '); });
                 </script>
             </div></div>
           ';


    //create links to subscriber search to show subscribers that opened, not opened, clicked, not clicked or bounced this campaign
    $ArraySentTag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_SENT, $CampaignID);
    $ArrayOpenTag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_OPENED, $CampaignID);
    $ArrayClickedTag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_CLICKED, $CampaignID);
    $ArrayConvertedTag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_CONVERTED, $CampaignID);
    // NOTE: MigrationLinks -> will no longer exist
    $opened_search_link = url("emails/$UserID/search/$CampaignID/{$ArrayOpenTag['TagID']}");
    $not_opened_search_link = url("emails/$UserID/search/$CampaignID/{$ArraySentTag['TagID']}/{$ArrayOpenTag['TagID']}");
    $clicked_search_link = url("emails/$UserID/search/$CampaignID/{$ArrayClickedTag['TagID']}");
    $not_clicked_search_link = url("emails/$UserID/search/$CampaignID/{$ArrayOpenTag['TagID']}/{$ArrayClickedTag['TagID']}");
    $converted_search_link = url("emails/$UserID/search/$CampaignID/{$ArrayConvertedTag['TagID']}");
    $not_converted_search_link = url("emails/$UserID/search/$CampaignID/{$ArrayOpenTag['TagID']}/{$ArrayConvertedTag['TagID']}");
    $bounced_link = l(t('Bounces'), "emails/$UserID/search/$CampaignID/{$ArraySentTag['TagID']}/0/1");

    if (!empty($account->UserSettings['ShowAdvancedEmailStats'])) {
      //only show the stats if the user checked the ShowAdvancedEmailStats checkbox in user edit
      //@see: klicktipp_account_user

      $output .= "
              <div id='ctrl-campaign-send-score'>
                <div class='send-score-envelopes'>
                  <div class='envelope send-score-sent'>$TotalSent</div>
                  <div class='envelope send-score-bounced'>$TotalBounces</div>
                  <div class='envelope send-score-delivered'>$Delivered</div>
                </div>
                <div class='send-score-labels'>
                  <span class='send-score-label send-score-sent'>" . t('Sent') . "</span>
                  <span class='send-score-label send-score-bounced'>" . t('Bounced') . "</span>
                  <span class='send-score-label send-score-delivered'>" . t('!percent delivered', array('!percent' => $DeliveredRatio)) . "</span>
                  <div class='send-score-bounced-descr'>" . t('!bounce_link occure when email address are temporarily not reachable or do not exist (anymore).', array('!bounce_link' => $bounced_link)) . "</div>
                </div>

              </div>
            ";

    }

    // --- opens donut chart ---

    if ($TotalSent > 0 && !Campaigns::IsSMSCampaign($CampaignInformation['AutoResponderTriggerTypeEnum'])) {
      //without sent emails there are no opens/unopens, google charts would not render the chart

      $chart_name = 'campaign_opens_donut';
      $output .= "
              <div class='splittest-separator'></div>
              <div id='ctrl-campaign-opens'>
                  <div id='$chart_name' class='donut-chart'>
                    <script type='text/javascript'>google.setOnLoadCallback(function () { ktcharts_load_donut_chart({'uid': '$UserID', 'entity': '{$CampaignInformation['CampaignID']}', 'name': '$chart_name'}); });</script>
                  </div>
                  <div class='donut-chart-info'>
                    <div class='row'>
                      <div class='col-lg-6 col-md-6 col-sm-6 col-xs-6 col-lg-offset-1 col-md-offset-1 col-sm-offset-1 col-xs-offset-1'>
                        <div class='chart-title'>" . t('Opens') . "</div>
                        <div class='data-positive'>
                          <span class='data-box'>$Opens</span>
                          <span class='data-label'><a class='btn btn-default' href='$opened_search_link'><i class='icon-email-opens'></i>" . t('Opened') . "</a></span>
                        </div>
                        <div class='data-negative'>
                          <span class='data-box'>$Unopens</span>
                          <span class='data-label'><a class='btn btn-default' href='$not_opened_search_link'><i class='icon-email-unopens'></i>" . t('Not opened') . "</a></span>
                        </div>
                      </div>
                      <div class='col-lg-5 col-md-5 col-sm-5 col-xs-5'>
                        <div class='data-total'>" . t('<strong>!delivered</strong> delivered', array('!delivered' => $Delivered)) . "</div>
                      </div>
                    </div>
                  </div>
              </div>
            ";

    }

    // --- clicks donut chart ---

    if ($DataTotal > 0) {
      //without opens there are no clicks/no-clicks, google charts would not render the chart

      $chart_name = 'campaign_clicks_donut';
      $output .= "
              <div class='splittest-separator'></div>
              <div id='ctrl-campaign-clicks'>
                  <div id='$chart_name' class='donut-chart'>
                    <script type='text/javascript'>google.setOnLoadCallback(function () { ktcharts_load_donut_chart({'uid': '$UserID', 'entity': '{$CampaignInformation['CampaignID']}', 'name': '$chart_name'}); });</script>
                  </div>
                  <div class='donut-chart-info'>
                    <div class='row'>
                      <div class='col-lg-6 col-md-6 col-sm-6 col-xs-6 col-lg-offset-1 col-md-offset-1 col-sm-offset-1 col-xs-offset-1'>
                        <div class='chart-title'>" . t('Clicks') . "</div>
                        <div class='data-positive'>
                          <span class='data-box'>$Clicks</span>
                          <span class='data-label'><a class='btn btn-default' href='$clicked_search_link'><i class='icon-email-clicked'></i>" . t('Clicked') . "</a></span>
                        </div>
                        <div class='data-negative'>
                          <span class='data-box'>$NotClicked</span>
                          <span class='data-label'><a class='btn btn-default' href='$not_clicked_search_link'><i class='icon-email-notclicked'></i>" . t('Not clicked') . "</a></span>
                        </div>
                      </div>
                      <div class='col-lg-5 col-md-5 col-sm-5 col-xs-5'>
                        <div class='data-total'>" . $DataTotalLabel . "</div>
                      </div>
                    </div>
                  </div>
              </div>
            ";

    }

    // --- conversions donut chart ---

    if ($DataTotal > 0 && user_access('access conversion pixel', $account)) {

      $chart_name = 'campaign_conversions_donut';
      $output .= "
              <div class='splittest-separator'></div>
              <div id='ctrl-campaign-conversions'>
                  <div id='$chart_name' class='donut-chart'>
                    <script type='text/javascript'>google.setOnLoadCallback(function () { ktcharts_load_donut_chart({'uid': '$UserID', 'entity': '{$CampaignInformation['CampaignID']}', 'name': '$chart_name'}); });</script>
                  </div>
                  <div class='donut-chart-info'>
                    <div class='row'>
                      <div class='col-lg-6 col-md-6 col-sm-6 col-xs-6 col-lg-offset-1 col-md-offset-1 col-sm-offset-1 col-xs-offset-1'>
                        <div class='chart-title'>" . t('Conversions') . "</div>
                        <div class='data-positive'>
                          <span class='data-box'>$Conversions</span>
                          <span class='data-label'><a class='btn btn-default' href='$converted_search_link'><i class='icon-email-clicked'></i>" . t('Converted') . "</a></span>
                        </div>
                        <div class='data-negative'>
                          <span class='data-box'>$NotConverted</span>
                          <span class='data-label'><a class='btn btn-default' href='$not_converted_search_link'><i class='icon-email-notclicked'></i>" . t('Not converted') . "</a></span>
                        </div>
                      </div>
                      <div class='col-lg-5 col-md-5 col-sm-5 col-xs-5'>
                        <div class='data-total'>" . $DataTotalLabel . "</div>
                      </div>
                    </div>
                  </div>
              </div>
            ";

    }

    // --- clicked links table ---

    $link_header = array(
      array('data' => t('Link')),
      array(
        'data' => t('Unique'),
        'class' => array('table-col-fit'),
      ),
      array(
        'data' => t('Total'),
        'class' => array('table-col-fit'),
      ),
    );

    $link_rows = array();
    if (count($MostClickedLinks) > 0) {

      foreach ($MostClickedLinks as $LinkURL => $EachLink) {
        $linktitle = empty($EachLink['LinkTitle']) ? $LinkURL : $EachLink['LinkTitle'];

        if (strlen($linktitle) > 80) {
          $linktitle = substr($linktitle, 0, 80) . '...';
        }

        $row = array(
          array('data' => l($linktitle, $LinkURL, array('attributes' => array('target' => '_blank')))),
          array(
            'data' => klicktipp_number_format($EachLink['UniqueClicks']),
            'class' => array('table-col-fit table-col-right'),
          ),
          array(
            'data' => klicktipp_number_format($EachLink['TotalClicks']),
            'class' => array('table-col-fit table-col-right'),
          ),
        );

        $link_rows[] = $row;

      }

      $output .= "<div id='ctrl-campaign-most-clicked-links'>" . theme('table', array(
          'header' => $link_header,
          'rows' => $link_rows,
          'attributes' => ['data-e2e-id' => 'table-newsletter-most-clicked']
        )) . "</div>";

    }

  }

  return $output;
}

/**
 * form edit: emails
 */
function klicktipp_campaign_emails($account, $CampaignID, $Wizard = 0) {

  Libraries::include('email.inc', '/forms');

  $UserID = $account->uid;
  $CampaignInformation = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
  $IsSMS = Campaigns::IsSMSCampaign($CampaignInformation['AutoResponderTriggerTypeEnum']);
  $IsAutoresponder = Campaigns::IsAutoresponder($CampaignInformation['AutoResponderTriggerTypeEnum']);

  if (empty($CampaignInformation['SplittestID'])) {
    $page_title = $IsSMS ? t('Edit SMS') : t('Edit email');
  }
  else {
    if (in_array($CampaignInformation['CampaignStatusEnum'], array(
      Campaigns::STATUS_DRAFT,
      Campaigns::STATUS_READY,
    ))
    ) {
      $page_title = $IsSMS ? t('Edit Splittest SMS') : t('Edit Splittest emails');
    }
    else {
      $page_title = t('Splittest results');
    }
  }

  klicktipp_set_title($page_title);
  // NOTE: MigrationLinks -> will no longer exist
  $Overview = $IsAutoresponder ? l(t('Autoresponder Overview'), "autoresponders/$UserID") : l(t('Newsletter Overview'), "campaigns/$UserID");
  $breadcrumb_name = $CampaignInformation['CampaignName'];
  klicktipp_set_breadcrumb(array(
    $Overview,
    // NOTE: MigrationLinks -> will no longer exist
    l($breadcrumb_name, "emails/$UserID/$CampaignID")
  ), $page_title);

  // NOTE: MigrationLinks -> will no longer exist
  $CancelLink = array(
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => $IsAutoresponder ? "autoresponders/$UserID" : "campaigns/$UserID",
  );

  $SplittestObject = CampaignsNewsletter::GetSplittestObject($CampaignInformation);
  if (!empty($SplittestObject)) {

    $SplittestEmails = $SplittestObject->GetData('Variants');

    if (count($SplittestEmails) == 1 && $Wizard == KLICKTIPP_WIZARD_CREATE_EMAIL) {

      $EachEmail = current($SplittestEmails);
      $e = drupal_get_form('klicktipp_email_edit_form', $account, 'campaign', $CampaignInformation['CampaignID'], $EachEmail['EntityID'], 1, $Wizard);
      return drupal_render($e);

    }

    $output = '';
    // NOTE: MigrationLinks -> will no longer exist
    $campaignpath = "emails/$UserID/$CampaignID/";

    if (count($SplittestEmails) > 0) {
      //show the splittest email overview of the newsletter/autoresponder

      if ($CampaignInformation['CampaignStatusEnum'] != Campaigns::STATUS_DRAFT && !empty($SplittestEmails)) {
        //we only have splittest data of sent, sending, paused or canceled campaigns

        klicktipp_include_google_chart_api();

        $ChartColors = variable_get(KLICKTIPP_THEME_COLORS, array());

        // --- display splittest info ---

        $SplittestStartDate = Dates::formatDate(Dates::FORMAT_DMY, (int) $SplittestObject->GetData('started'));
        $SplittestStartTime = Dates::formatDate(Dates::FORMAT_HIS, (int) $SplittestObject->GetData('started'));

        if (empty($SplittestObject->GetData('started'))) {
          //campaign is scheduled for a future date, the splittest has not started yet
          $SplittestStartDate = Dates::formatDate(Dates::FORMAT_DMY, (int) $CampaignInformation['SendDatetime']);
          $SplittestStartTime = Dates::formatDate(Dates::FORMAT_HIS, (int) $CampaignInformation['SendDatetime']);
          $SplittestEndDate = Dates::formatDate(Dates::FORMAT_DMY, $CampaignInformation['SendDatetime'] + $SplittestObject->GetData('TestDuration'));
          $SplittestEndTime = Dates::formatDate(Dates::FORMAT_HIS, $CampaignInformation['SendDatetime'] + $SplittestObject->GetData('TestDuration'));
        }
        elseif (empty($SplittestObject->GetData('finished') || !empty($SplittestObject->GetData('calculating')))) {
          //the splittest is running and ends after the set duration
          $SplittestEndDate = Dates::formatDate(Dates::FORMAT_DMY, $SplittestObject->GetData('started') + $SplittestObject->GetData('TestDuration'));
          $SplittestEndTime = Dates::formatDate(Dates::FORMAT_HIS, $SplittestObject->GetData('started') + $SplittestObject->GetData('TestDuration'));
        }
        else {
          //the splittest ended, we have a termination date
          $SplittestEndDate = Dates::formatDate(Dates::FORMAT_DMY, (int) $SplittestObject->GetData('finished'));
          $SplittestEndTime = Dates::formatDate(Dates::FORMAT_HIS, (int) $SplittestObject->GetData('finished'));
        }

        //autoresponder splittests do not have a test size.
        $SplittestTestSize = $IsAutoresponder ? "100%" : $SplittestObject->GetData('TestSize') . "%";

        $output .= "
          <div id='ctrl-campaign-splittest-status' class='row'>
            <div class='col-lg-2 col-md-2 col-sm-2 col-xs-2 col-lg-offset-1 col-md-offset-1'>
                <div id='splittest-status-testsize' class='panel panel-default'>
                    <div class='panel-heading'>" . t('Test size') . "</div>
                    <div class='panel-body text-center'>
                        <div class='splittest-status-testsize-value'>$SplittestTestSize</div>
                    </div>
                </div>
            </div>
            <div class='col-lg-2 col-md-2 col-sm-2 col-xs-2'>
                <div class='splittest-arrow'></div>
            </div>
            <div class='col-lg-2 col-md-2 col-sm-2 col-xs-2'>
                <div id='splittest-status-start'>
                    <div class='splittest-start-watch'>
                        <p class='splittest-watch-title green'>" . t('Start') . ":</p>
                        <p class='splittest-watch-date'>$SplittestStartDate</p>
                        <p class='splittest-watch-time'>$SplittestStartTime</p>
                    </div>
                </div>
            </div>
            <div class='col-lg-2 col-md-2 col-sm-2 col-xs-2'>
                <div class='splittest-arrow'></div>
            </div>
            <div class='col-lg-2 col-md-2 col-sm-2 col-xs-2'>
                <div id='splittest-status-stop'>
                    <div class='splittest-stop-watch'>
                        <p class='splittest-watch-title red'>" . t('End') . ":</p>
                        <p class='splittest-watch-date'>$SplittestEndDate</p>
                        <p class='splittest-watch-time'>$SplittestEndTime</p>
                    </div>
                </div>
            </div>

          </div>";

        // --- display winner of finished splittest ---

        if (!empty($SplittestObject->GetData('finished')) && empty($SplittestObject->GetData('calculating'))) {

          //the splittest is finished -> show the winner box

          //use the color of the winner version -> same array index in $ChartColors and $SplittestEmails
          $SplittestWinnerIndex = $SplittestObject->GetData('WinnerID');
          $SplittestWinnerColor = $ChartColors[$SplittestWinnerIndex];

          /** @var DatabaseTableCRUD $EmailObject */
          $EmailObject = $SplittestObject->GetVariantEntity($SplittestObject->GetData('WinnerID'));
          $email_name = $EmailObject ? $EmailObject->GetName() : '';

          switch($SplittestObject->GetData('WinnerEnum')) {
            case SplitTests::WINNER_OPENS:
              $WinnerHeadline = t('Winner by most opens: Version !version', array('!version' => chr(65 + $SplittestWinnerIndex)));
              break;
            case SplitTests::WINNER_CLICKS:
              $WinnerHeadline = t('Winner by most clicks: Version !version', array('!version' => chr(65 + $SplittestWinnerIndex)));
              break;
            case SplitTests::WINNER_CONVERSION:
              $WinnerHeadline = t('Winner by most conversions: Version !version', array('!version' => chr(65 + $SplittestWinnerIndex)));
              break;
            default: // case SplitTests::WINNER_AMOUNT:
              $WinnerHeadline = t('Winner by highest revenue: Version !version', array('!version' => chr(65 + $SplittestWinnerIndex)));
              break;
          }

          $output .= "
                <div class='splittest-separator'></div>
                <div id='ctrl-campaign-splittest-winner' class='row'>
                  <div class='col-lg-2 col-md-2 col-sm-2 col-xs-2 col-lg-offset-1 col-md-offset-1 col-sm-offset-1 col-xs-offset-1'>
                    <div class='splittest-winner-trophy'></div>
                  </div>
                  <div class='col-lg-8 col-md-8 col-sm-8 col-xs-8'>
                    <div class='splittest-winner-headline' style='color:$SplittestWinnerColor;'>$WinnerHeadline</div>
                    <div class='splittest-winner-email' style='border-color:$SplittestWinnerColor;'>" . $email_name . "</div>
                  </div>
                </div>";


        }

        // --- display the opens/clicks charts ---

        //unique opens chart
        if ($IsSMS) {
          $opens_chart = '';
        }
        else {
          $chart_name = 'campaign_splittest_opens_hbar';
          $opens_chart = '<h3>' . t('Unique opens') . '</h3>
              <div id="ctrl-campaign-splittest-opens-chart"><div id="' . $chart_name . '" style="width:100%;">
                  <script type="text/javascript">
                    google.setOnLoadCallback(function () { ktcharts_load_hbar_chart({"uid": "' . $UserID . '", "entity": "' . $CampaignInformation['CampaignID'] . '", "name": "' . $chart_name . '"}); });
                  </script>
              </div></div>
            ';
        }

        //unique clicks chart
        $chart_name = 'campaign_splittest_clicks_hbar';
        $clicks_chart = '<h3>' . t('Unique clicks') . '</h3>
              <div id="ctrl-campaign-splittest-clicks-chart"><div id="' . $chart_name . '" style="width:100%;">
                  <script type="text/javascript">
                    google.setOnLoadCallback(function () { ktcharts_load_hbar_chart({"uid": "' . $UserID . '", "entity": "' . $CampaignInformation['CampaignID'] . '", "name": "' . $chart_name . '"}); });
                  </script>
              </div></div>
            ';

        if (user_access('access conversion pixel', $account)) {

          //unique conversions chart
          $chart_name = 'campaign_splittest_conversions_hbar';
          $conversions_chart = '<h3>' . t('Unique conversions') . '</h3>
              <div id="ctrl-campaign-splittest-conversions-chart"><div id="' . $chart_name . '" style="width:100%;">
                  <script type="text/javascript">
                    google.setOnLoadCallback(function () { ktcharts_load_hbar_chart({"uid": "' . $UserID . '", "entity": "' . $CampaignInformation['CampaignID'] . '", "name": "' . $chart_name . '"}); });
                  </script>
              </div></div>
            ';

          //unique revenue chart
          $chart_name = 'campaign_splittest_revenue_hbar';
          $revenue_chart = '<h3>' . t('Highest revenue') . '</h3>
              <div id="ctrl-campaign-splittest-revenue-chart"><div id="' . $chart_name . '" style="width:100%;">
                  <script type="text/javascript">
                    google.setOnLoadCallback(function () { ktcharts_load_hbar_chart({"uid": "' . $UserID . '", "entity": "' . $CampaignInformation['CampaignID'] . '", "name": "' . $chart_name . '"}); });
                  </script>
              </div></div>
            ';
        }
        else {
          $conversions_chart = '';
          $revenue_chart = '';
        }

        $output .= "
            <div class='splittest-separator'></div>
            $opens_chart
            $clicks_chart
            $conversions_chart
            $revenue_chart
          ";

        // --- display splittest email table ---

        $header = array(
          array(
            'data' => t('Splittest version'),
            'class' => array('table-col-fit'),
          ),
          array('data' => t('Email')),
        );
        if (!$IsSMS) {
          $header[] =
            array(
              'data' => t('Opens'),
              'class' => array('table-col-fit'),
            );
        }
        $header[] =
          array(
            'data' => t('Clicks'),
            'class' => array('table-col-fit'),
          );
        if (user_access('access conversion pixel', $account)) {
          $header[] =
            array(
              'data' => t('Conversions'),
              'class' => array('table-col-fit'),
            );
          $header[] =
            array(
              'data' => t('Revenue'),
              'class' => array('table-col-fit'),
            );
        }
        $header[] =
          array(
            'data' => t('Operations'),
            'class' => array('table-col-fit'),
          );

        $version_ascii = 65; //ascii code for "A"
        $version_color = 0; //index of $ChartColors
        $rows = array();
        foreach ($SplittestEmails as $index => $Variant) {

          $buttons = "";

          $Stats = $SplittestObject->GetTaggingStatsForNewsletter($index);
          $Entity = $SplittestObject->GetVariantEntity($index);

          if ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_READY && $CampaignInformation['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED) {
            //canceled campaign, we have statistics but the emails can be edited

            $buttons .= theme('klicktipp_duplicate_table_button', array(
              'element' => array(
                '#title' => t('Duplicate'),
                '#value' => $campaignpath . 'duplicatetestemail/' . $CampaignInformation['SplittestID'] . '/' . $Variant['EntityID'],
              )
            ));
            $buttons .= theme('klicktipp_edit_table_button', array(
              'element' => array(
                '#title' => t('Edit'),
                '#value' => $campaignpath . 'campaign/' . $Variant['EntityID'],
              )
            ));
            $buttons .= theme('klicktipp_delete_table_button', array(
              'element' => array(
                '#title' => t('Delete'),
                '#value' => $campaignpath . 'removetestemail/' . $CampaignInformation['SplittestID'] . '/' . $Variant['EntityID'],
              )
            ));

            $buttons .= theme('klicktipp_preview_table_button', array(
              'element' => array(
                '#title' => t('Preview'),
                '#value' => 'email/' . $UserID . '/preview/' . $Variant['EntityID'] . '/campaign/' . $CampaignInformation['CampaignID'],
              )
            ));

          }
          else {
            $buttons = theme('klicktipp_preview_table_button', array(
              'element' => array(
                '#title' => t('Preview'),
                '#value' => 'email/' . $UserID . '/preview/' . $Variant['EntityID'] . '/campaign/' . $CampaignInformation['CampaignID'],
              )
            ));
          }

          $DisplayName = '<span class="cut-text display-email has-tooltip"
              title="' . $Entity->GetData('EmailName') . '">'
              . $Entity->GetData('EmailName') . '</span>';

          $version = t('Version !version', array('!version' => chr($version_ascii)));
          $row = array(
            array(
              'data' => $version,
              'class' => array('table-col-fit'),
            ),
            array(
            'data' => $DisplayName,
            'style' => "color: {$ChartColors[$version_color]};",
            ),
          );
          if (!$IsSMS) {
            $row[] =
              array(
                'data' => $Stats['opened'],
                'class' => array('table-col-fit table-col-right'),
              );
          }
          $row[] =
            array(
              'data' => $Stats['clicked'],
              'class' => array('table-col-fit table-col-right'),
            );
          if (user_access('access conversion pixel', $account)) {
            $row[] =
              array(
                'data' => $Stats['converted'],
                'class' => array('table-col-fit table-col-right'),
              );
            $row[] =
              array(
                'data' => klicktipp_number_format($Stats['amount']/100, 2),
                'class' => array('table-col-fit table-col-right'),
              );
          }
          $row[] =
            array(
              'data' => $buttons,
              'class' => array('table-col-fit'),
            );

          $rows[] = $row;

          $version_ascii++; //next letter in alphabet
          $version_color++; //next color

        }

        $output .= "<div id='ctrl-campaign-splittest-links'>" . theme('table', array(
            'header' => $header,
            'rows' => $rows,
            'attributes' => array(
              'class' => array('table-buttons'),
              'data-e2e-id' => 'table-newsletter-splittest-links'
            )
          )) . "</div>";

      }
      else {
        //status draft with at least 1 email

        $header = array(
          array('data' => t('Email')),
          array(
            'data' => t('Operations'),
            'class' => array('table-col-fit'),
          ),
        );

        $rows = array();

        foreach ($SplittestEmails as $index => $Variant) {

          $Entity = $SplittestObject->GetVariantEntity($index);

          $link = '';

          $link .= theme('klicktipp_duplicate_table_button', array(
            'element' => array(
              '#title' => t('Duplicate'),
              '#value' => $campaignpath . 'duplicatetestemail/' . $CampaignInformation['SplittestID'] . '/' . $Variant['EntityID'],
            )
          ));
          $link .= theme('klicktipp_edit_table_button', array(
            'element' => array(
              '#title' => t('Edit'),
              '#value' => $campaignpath . 'campaign/' . $Variant['EntityID'],
            )
          ));
          $link .= theme('klicktipp_delete_table_button', array(
            'element' => array(
              '#title' => t('Delete'),
              '#value' => $campaignpath . 'removetestemail/' . $CampaignInformation['SplittestID'] . '/' . $Variant['EntityID'],
            )
          ));

          $link .= theme('klicktipp_preview_table_button', array(
            'element' => array(
              '#title' => t('Preview'),
              '#value' => 'email/' . $UserID . '/preview/' . $Variant['EntityID'] . '/campaign/' . $CampaignInformation['CampaignID'],
            )
          ));

          $DisplayName = array(
            'data' => '<span class="cut-text display-email has-tooltip" title="' . $Entity->GetData('EmailName') . '">' . $Entity->GetData('EmailName') . '</span>',);
          $row = array(
            $DisplayName,
            array(
              'data' => $link,
              'class' => array('table-col-fit'),
            ),
          );

          $rows[] = $row;

        }

        $output .= theme('table', array(
          'header' => $header,
          'rows' => $rows,
          'attributes' => array(
            'class' => array('table-buttons'),
            'data-e2e-id' => 'table-newsletter-splittest-emails'
          )
        ));

      }


    }
    else {
      //status draft with no emails
      $output .= "<p>" . t('There are no Splittest emails yet.') . "</p>";

    }

    if (in_array($CampaignInformation['CampaignStatusEnum'], array(
      Campaigns::STATUS_DRAFT,
      Campaigns::STATUS_READY,
    ))
    ) {
      $SendDateButton = '';

      if (klicktipp_campaign_senddate_menu_access($account, $CampaignInformation['CampaignID'])) {

        $SendDateLink = array(
          '#title' => t('Edit send date'),
          // NOTE: MigrationLinks -> will no longer exist
          '#value' => 'emails/' . $UserID . '/' . $CampaignInformation['CampaignID'] . '/senddate',
        );

        $SendDateButton = theme('klicktipp_arrow_button', array('element' => $SendDateLink));
      };

      if ($IsSMS) {
        $CreateLink = array(
          '#title' => t('Add new test sms'),
          '#value' => 'email/' . $UserID . '/edit/campaign/' . $CampaignInformation['CampaignID'],
        );
      }
      else {
        $CreateLink = array(
          '#title' => t('Add new test email'),
          // NOTE: MigrationLinks -> will no longer exist
          '#value' => "emails/$UserID/splittest/add/" . $CampaignInformation['CampaignID'],
        );
      }

      $output .= '<div class="button-row">' .
        $SendDateButton .
        theme('klicktipp_email_button', array('element' => $CreateLink)) .
        theme('klicktipp_cancel_button', array('element' => $CancelLink)) . '</div>';

    }

    return $output;

  }
  else { // not splittest

    //get the email of the campaign
    $EachEmail = Emails::RetrieveEmailByID($UserID, $CampaignInformation['RelEmailID']);

    if (in_array($CampaignInformation['CampaignStatusEnum'], array(
      Campaigns::STATUS_DRAFT,
      Campaigns::STATUS_READY,
    ))
    ) {

      if (empty($EachEmail['EmailID'])) { //TODO this should be impossible now - what do we do, if an email is not created? create it here?
        $e = drupal_get_form('klicktipp_email_edit_form', $account, 'campaign', $CampaignInformation['CampaignID']);
        return drupal_render($e);
      }
      else {
        $e = drupal_get_form('klicktipp_email_edit_form', $account, 'campaign', $CampaignInformation['CampaignID'], $EachEmail['EmailID'], 1, $Wizard);
        return drupal_render($e);
      }

      //TODO add preview button to email form

    }
    else {

      if (!empty($EachEmail['EmailID'])) {
        $PreviewLink = array(
          '#title' => t('Preview'),
          '#value' => 'email/' . $UserID . '/preview/' . $EachEmail['EmailID'] . '/campaign/' . $CampaignInformation['CampaignID'],
          '#attributes' => array('target' => '_blank'),
        );
        return '<div class="button-row">' . theme('klicktipp_preview_button', array('element' => $PreviewLink)) . theme('klicktipp_cancel_button', array('element' => $CancelLink)) . '</div>';
      }

    }

  }

}

/**
 * form edit: senddate
 */
function klicktipp_campaign_senddate($form, $form_state, $account, $CampaignID) {

  $UserID = $account->uid;
  $CampaignInformation = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
  $IsAutoresponder = Campaigns::IsAutoresponder($CampaignInformation['AutoResponderTriggerTypeEnum']);

  //set breadcrumb and page title
  $page_title = t('Edit Send date');
  klicktipp_set_title($page_title);
  // NOTE: MigrationLinks -> will no longer exist
  $Overview = $IsAutoresponder ? l(t('Autoresponder Overview'), "autoresponders/$UserID") : l(t('Newsletter Overview'), "campaigns/$UserID");
  klicktipp_set_breadcrumb(array(
    $Overview,
    // NOTE: MigrationLinks -> will no longer exist
    l($CampaignInformation['CampaignName'], "emails/$UserID/$CampaignID")
  ), $page_title);

  $form = array();

  $weight = 1;

  $form['#pre_render'][] = 'klicktipp_campaign_senddate_pre_render';

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['Campaign'] = array(
    '#type' => 'value',
    '#value' => $CampaignInformation,
  );

  $form['campaign_id'] = array(
    '#type' => 'value',
    '#value' => $CampaignInformation['CampaignID'],
  );

  $SplittestObject = CampaignsNewsletter::GetSplittestObject($CampaignInformation);
  if (!empty($SplittestObject) && (count($SplittestObject->GetData('Variants')) < 2)) {

    $form['content_first'] = array(
      '#type' => 'markup',
      '#value' => "<p>" . t('Before you can schedule your splittest campain, you have to create at least 2 splittest emails.') . "</p>",
      '#weight' => $weight++,
    );

    $form['buttons'] = array(
      '#type' => 'markup',
      '#prefix' => '<div class="button-row">',
      '#suffix' => '</div>',
      '#weight' => $weight++,
    );

    $form['buttons']['back'] = array(
      '#theme' => 'klicktipp_wizard_back_button',
      '#title' => t('Create Splittest emails'),
      // NOTE: MigrationLinks -> will no longer exist
      '#value' => "emails/$UserID/$CampaignID/emails",
      '#weight' => $weight++,
    );

    $form['buttons']['cancel'] = array(
      '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
      // NOTE: MigrationLinks -> will no longer exist
      '#value' => $IsAutoresponder ? "autoresponders/$UserID" : "campaigns/$UserID",
      '#theme' => 'klicktipp_cancel_button',
      '#weight' => $weight++,
    );

  }
  else {
    if (in_array($CampaignInformation['CampaignStatusEnum'], Campaigns::$ArrayCampaignStatiSending) ||
      ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
        $CampaignInformation['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_IMMEDIATE)
    ) {

      $form['statusbar'] = array(
        '#type' => 'markup',
        '#value' => klicktipp_campaign_status_bar($account, $CampaignID, $CampaignInformation),
      );

    }
    else {

      if (!empty($SplittestObject) && $IsAutoresponder) {
        // is splittest autoresponder

        $form['ScheduleSplittest'] = array(
          '#type' => 'item',
          '#theme' => 'klicktipp_info',
          '#value' => t('Your splittest campaign can not be scheduled. Please use an automation to create your splittest.'),
        );

        return $form;

      }

      $form['uid'] = array(
        '#type' => 'value',
        '#value' => $UserID,
      );

      $form['ScheduleTypeEnum'] = array(
        '#type' => 'select',
        '#title' => t('Send date'),
        '#default_value' => $CampaignInformation['ScheduleTypeEnum'],
        '#options' => array(
          Campaigns::SCHEDULE_TYPE_IMMEDIATE => t('Now'),
          Campaigns::SCHEDULE_TYPE_FUTURE => t('On given date and time'),
          Campaigns::SCHEDULE_TYPE_NOTSCHEDULED => t('Pause'),
        ),
        '#quickhelp' => $IsAutoresponder ? 'autoresponder-send-date' : 'newsletter-send-date',
        '#attributes' => array(
          'data-event-load' => 'on_scheduletype_change',
          'data-event-change' => 'on_scheduletype_change',
        ),
      );

      $dateTime = CustomFields::ConvertCustomFieldDataToWidget($CampaignInformation['SendDatetime'], CustomFields::WIDGET_DATETIME);

      $form['SendDate'] = array(
        '#type' => 'textfield',
        '#default_value' => $CampaignInformation['SendDatetime'] == 0 ? Dates::formatDate(Dates::FORMAT_DMY_DATEPICKER_PHP, time()) : $dateTime['date'],
        '#title' => t('Date'),
        '#attributes' => array(
          'data-datepicker-alt-field' => '.edit-SendDate',
        ),
        '#datepicker' => TRUE,
        '#quickhelp' => 'send-date-date',
      );

      $SendTimeOptions = array();
      for ($i = 0; $i < 24; $i++) {
        $s = sprintf('%02d', $i);
        $SendTimeOptions[$s] = $s;
      }

      $form['SendTime'] = array(
        '#type' => 'markup',
        '#prefix' => '<div id="SendTimeHourTable" class="select-time"><label>' . t('Time') . theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => 'send-date-time',
              '#title' => t('Time'),
            )
          )) . ':</label>',
        '#suffix' => '</div>',
      );

      $form['SendTime']['SendTimeHour'] = array(
        '#type' => 'select',
        '#default_value' => $dateTime['hours'],
        '#options' => $SendTimeOptions,
        '#suffix' => '<span id="SendTimeMinuteSpan">:</span>',
      );

      $SendMinuteOptions = array();
      for ($i = 0; $i < 60; $i++) {
        $s = sprintf('%02d', $i);
        $SendMinuteOptions[$s] = $s;
      }
      $form['SendTime']['SendTimeMinute'] = array(
        '#type' => 'select',
        '#default_value' => $dateTime['minutes'],
        '#options' => $SendMinuteOptions,
      );

      $form['buttons'] = array(
        '#type' => 'markup',
        '#prefix' => '<div class="button-row">',
        '#suffix' => '</div>',
      );

      $form['buttons']['submit'] = array(
        '#type' => 'submit',
        '#value' => t('Save Send date'),
        '#weight' => $weight++,
        '#theme' => 'klicktipp_submit',
        '#attributes' => array('class' => array('edit-submit')),
        '#id' => 'save-SendDate',
      );

      // Note: this function is called BEFORE the modal is displayed
      $MinEstimatedRecipients = 0;
      // compare with '$limitquery' in Subscribers::RetrieveTaggedSubscribers():
      // assumption: $TriggerTypEnum >= 0
      // 'all active email subscribers with offset'
      $limitquery = "SELECT COUNT(DISTINCT(RelSubscriberID)) FROM {subscription} " .
        " WHERE RelOwnerUserID = :RelOwnerUserID AND SubscriptionStatus = :SubscriptionStatus AND BounceType != :BounceType AND SubscriptionType = :SubscriptionType";
      $params = array(
        ':RelOwnerUserID' => $UserID,
        ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ':BounceType' => Subscribers::BOUNCETYPE_HARD,
      );
      if (Campaigns::IsSMSCampaign($CampaignInformation['AutoResponderTriggerTypeEnum'])) {
        $params[':SubscriptionType'] = Subscription::SUBSCRIPTIONTYPE_SMS;
      }
      else {
        $params[':SubscriptionType'] = Subscription::SUBSCRIPTIONTYPE_EMAIL;
      }
      $MaxEstimatedRecipients = db_query($limitquery, $params)->fetchField();
      // works as a countdown
      $RefreshOffset = $MaxEstimatedRecipients;

      // these fields are used by the batch recipients timer in JS
      $form['RefreshOffset'] = array(
        '#type' => 'hidden',
        '#default_value' => $RefreshOffset,
      );
      $form['MinRecipients'] = array(
        '#type' => 'hidden',
        '#default_value' => $MinEstimatedRecipients,
      );
      $form['MaxRecipients'] = array(
        '#type' => 'hidden',
        '#default_value' => $MaxEstimatedRecipients,
      );

      $ModalID = 'klicktipp_newsletter_senddate_confirm_form';

      $form['buttons']['ModalSendConfirmTrigger'] = array(
        '#theme' => 'klicktipp_submit_modal_button',
        '#title' => t('Save Send date'),
        '#value' => $ModalID,
        '#weight' => $weight++,
        '#id' => 'edit-ModalSendConfirmTrigger',
      );

      Libraries::include('campaigns.inc');

      $form['DropdownOptionModal'] = array(
        '#type' => 'markup',
        '#markup' => klicktipp_refresh_subscriber_count_confirm_modal_markup($ModalID)
          . '<script type="text/javascript">'
          . klicktipp_refresh_subscriber_count_javascript($UserID, $CampaignID, t(/*ignore*/Campaigns::$DisplayCampaignType[$CampaignInformation['AutoResponderTriggerTypeEnum']]))
          . '$("#' . $ModalID . '").on("show.bs.modal", function (event) {
            updateRecipientBatchStepperMessage(true, ' . $MinEstimatedRecipients. ', ' . $MaxEstimatedRecipients .', ' . $RefreshOffset .');
            startRecipientBatchStepper("newsletter");
          });

          function js_modal_confirm_senddate_receivers_save() {
            cancelRecipientBatchStepper();
            $("#save-SendDate").click();
          }
        </script>'
      );

      $form['buttons']['cancel'] = array(
        '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
        // NOTE: MigrationLinks -> will no longer exist
        '#value' => $IsAutoresponder ? "autoresponders/$UserID" : "campaigns/$UserID",
        '#weight' => $weight++,
        '#theme' => 'klicktipp_cancel_button',
      );

    }
  }

  return $form;

}

/**
 * send date prerender
 */
function klicktipp_campaign_senddate_pre_render($form) {

  drupal_add_js('
    function on_scheduletype_change( element, args ) {
      var option = $(".edit-ScheduleTypeEnum option:selected").val();
      if (option == "' . Campaigns::SCHEDULE_TYPE_FUTURE . '") {
        $(".edit-SendDate-wrapper").show();
        $("#SendTimeHourTable").show();
      } else {
        $(".edit-SendDate-wrapper").hide();
        $("#SendTimeHourTable").hide();
      };
      if (option == "' . Campaigns::SCHEDULE_TYPE_NOTSCHEDULED . '") {
        $(".edit-submit").show();
        $("#edit-ModalSendConfirmTrigger").hide();
      } else {
        $("#edit-ModalSendConfirmTrigger").show();
        $(".edit-submit").hide();
      };
      window["update_view"]();
    };
    ', array('type' => 'inline', 'scope' => 'footer'));

  return $form;
}

/**
 * senddate hook_form_validate
 */
function klicktipp_campaign_senddate_validate($form, &$form_state) {

  $UserID = $form_state['values']['uid'];
  $CampaignID = $form_state['values']['campaign_id'];

  //get the campaign from the database (no caching) to check the trigger time, (multiple browser tabs)
  $CheckCampaign = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);

  if (empty($CheckCampaign)) {
    drupal_set_message(t('The autoresponder/newsletter does not exist anymore.'), 'error');
    drupal_goto("user/$UserID");
    return;
  }

  $SendDateTime = 0; //paused
  if ($form_state['values']['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_FUTURE) {
    if (empty($form_state['values']['SendDate'])) {
      form_set_error('SendDate', t('Please specify a date.'));
    }
    else {
      $TimeStampDate = Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $form_state['values']['SendDate']);
      $TimeStampTime = ($form_state['values']['SendTimeHour'] * 3600) + ($form_state['values']['SendTimeMinute'] * 60);
      $SendDateTime = $TimeStampDate + $TimeStampTime;

      if ( $SendDateTime < time() ) {
        form_set_error('SendDate', t('The send date is in the past.'));
      }

    }

  }
  else {
    if ($form_state['values']['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_IMMEDIATE) {
      $SendDateTime = time();
    }
  }

  if (!empty($SendDateTime)) {

    if (Campaigns::IsAutoresponder($CheckCampaign['AutoResponderTriggerTypeEnum'])) {

      $delaytime = AutoResponders::calculateSendTimeFromTriggerType(time(), $CheckCampaign); //from now
      $delaylimit = strtotime("+2 years"); //from now

      if ($delaytime > $delaylimit) {
        // NOTE: MigrationLinks -> will no longer exist
        $link = l(t('Edit time delay.'), "emails/$UserID/$CampaignID/edit");
        form_set_error('', t('The time delay cannot be greater than 2 years. !link', array('!link' => $link)));
      }

      $SendDateTime = AutoResponders::calculateSendTimeFromTriggerType($SendDateTime, $CheckCampaign); //from SendDate

    }

    $sendlimit = strtotime("2038-01-01"); //http://de.wikipedia.org/wiki/Jahr-2038-Problem (32bit int, DB only)


    if ($SendDateTime > $sendlimit) {
      form_set_error('SendDate', t('The send date is too far in the future.'));
    }

  }

}

/**
 * senddate hook_form_submit
 */
function klicktipp_campaign_senddate_submit($form, &$form_state) {

  $values = $form_state['values'];

  $UserID = $values['uid'];
  $CampaignID = $values['campaign_id'];

  //get the campaign from the database (no caching) to check the edit status, in case the campaign has been sent already or and old browser tab was used
  $CheckStatusCampaign = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
  //$edit_senddate TRUE -> campaign has not been sent yet
  $edit_senddate = (in_array($CheckStatusCampaign['CampaignStatusEnum'], array(
    Campaigns::STATUS_DRAFT,
    Campaigns::STATUS_READY,
  )));

  if (!$edit_senddate) {
    if (Campaigns::IsAutoresponder($CheckStatusCampaign['AutoResponderTriggerTypeEnum'])) {
      drupal_set_message(t('Unable to update the send date. The Autoresponder is already active. Please stop the Autoresponder first.'), 'error');
    }
    else {
      drupal_set_message(t('Unable to update the send date. The Newsletter has already been sent.'), 'error');
    }

    return;
  }

  // Campaign update - Start {
  $ArrayFieldAndValues = array();

  $ArrayFieldAndValues['ScheduleTypeEnum'] = $values['ScheduleTypeEnum'];

  if ($values['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED) {
    $ArrayFieldAndValues['CampaignStatusEnum'] = Campaigns::STATUS_DRAFT;
  }
  else {
    $ArrayFieldAndValues['CampaignStatusEnum'] = Campaigns::STATUS_READY;
  }

  if ($values['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_FUTURE) {
    //convert the date provided by the datepicker and the time fields
    $value['date'] = $values['SendDate'];
    $value['hours'] = $values['SendTimeHour'];
    $value['minutes'] = $values['SendTimeMinute'];
    $ArrayFieldAndValues['SendDatetime'] = CustomFields::ConvertCustomFieldDataFromWidget($value, CustomFields::WIDGET_DATETIME);
  }

  if (isset($values['MaxRecipients'])) {
    $ArrayFieldAndValues['EstimatedRecipients'] = $values['MaxRecipients'];
  }

  if (Campaigns::UpdateCampaign($UserID, $CampaignID, $ArrayFieldAndValues)) {

    drupal_set_message(t('Send date successfully updated.'));

    if ($ArrayFieldAndValues['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
      $ArrayFieldAndValues['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_IMMEDIATE) {

      if (Campaigns::ProcessCampaign($UserID, $CampaignID)) {
        drupal_set_message(t('Campaign successfully started.'));
      }
    }

    //redirect to campaign view
    // NOTE: MigrationLinks -> will no longer exist
    klicktipp_set_redirect($form_state, "emails/$UserID/$CampaignID");

  }
  else {
    $error = array(
      '!function' => __FUNCTION__,
      '!values' => $ArrayFieldAndValues,
    );
    Errors::unexpected("Error: Update campaign send date", $error, TRUE);
  }
  // Campaign update - End }

}

/**
 * Delete confirm submit
 */
function klicktipp_campaign_delete_confirm_form_submit($form, &$form_state) {

  $campaign = $form_state['values']['Entity'];
  $UserID = $campaign['RelOwnerUserID'];

  //messages
  if (Campaigns::IsAutoresponder($campaign['AutoResponderTriggerTypeEnum'])) {
    $success_msg = t('Autoresponder %name successfully deleted.', array('%name' => $campaign['CampaignName']));
    // NOTE: MigrationLinks -> will no longer exist
    $redirect = "autoresponders/$UserID";
  }
  else {
    $success_msg = t('Newsletter %name successfully deleted.', array('%name' => $campaign['CampaignName']));
    // NOTE: MigrationLinks -> will no longer exist
    $redirect = "campaigns/$UserID";
  }

  if (CampaignsNewsletter::DeleteDB($campaign)) {

    drupal_set_message($success_msg);

    klicktipp_set_redirect($form_state, $redirect);
  }
  else {
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }

}

/**
 * direct link from stats to subscriber search
 */
function klicktipp_campaign_search_subscribers($account, $CampaignID, $PositiveTagID, $NegativeTagID = 0, $ShowBounced = 0) {
  //redirect to contact search with status = 'subscribed' + Tag

  $_SESSION['subscriber_search_filter'] = array(
    'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
    'TaggedWith' => array($PositiveTagID),
    'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
    'NotTaggedWith' => (empty($NegativeTagID)) ? array() : array($NegativeTagID),
  );

  $campaign = Campaigns::RetrieveCampaignByID($CampaignID, $account->uid);
  if (Campaigns::IsSMSCampaign($campaign['AutoResponderTriggerTypeEnum'])) {
    $_SESSION['subscriber_search_filter']['SearchMode'] = SubscribersSearch::SEARCH_BY_PHONE_NUMBER;
    if ($ShowBounced) {
      $_SESSION['subscriber_search_filter']['SMSSearch']['SMSStatus'] = array();
      $_SESSION['subscriber_search_filter']['SMSSearch']['SMSBounceStatus'] = array(
        Subscribers::BOUNCETYPE_SOFT,
        Subscribers::BOUNCETYPE_HARD,
      );
    }
  }
  else {
    $_SESSION['subscriber_search_filter']['SearchMode'] = SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS;
    if ($ShowBounced) {
      $_SESSION['subscriber_search_filter']['EmailSearch']['Status'] = array();
      $_SESSION['subscriber_search_filter']['EmailSearch']['BounceStatus'] = array(
        Subscribers::BOUNCETYPE_SOFT,
        Subscribers::BOUNCETYPE_HARD,
      );
    }
  }

  drupal_goto("subscribers/{$account->uid}");
}


/**
 * Duplicates test email for a campaign
 *
 * @return void
 *
 **/
function klicktipp_campaign_duplicatetestemail($account, $CampaignID, $TestID, $EmailID) {

  $ArrayEmail = Emails::RetrieveEmailByID($account->uid, $EmailID);
  if (!empty($ArrayEmail)) {

    // adjust id and name for copy
    unset($ArrayEmail['EmailID']);
    $ArrayEmail['EmailName'] = t('Duplicate of: ') . $ArrayEmail['EmailName'];

    // create duplicate
    if ($ArrayEmail['EmailType'] == Emails::TYPE_NEWSLETTEREMAIL) {
      $NewEmailID = EmailsNewsletterEmail::InsertDB($ArrayEmail);
    } else {
      $NewEmailID = EmailsNewsletterSMS::InsertDB($ArrayEmail);
    };

    // add to campaign
    SplitTests::AddVariantForNewsletter($account->uid, $CampaignID, $NewEmailID);

    drupal_set_message(t('Test email is duplicated.'));
  }

  // NOTE: MigrationLinks -> will no longer exist
  drupal_goto("emails/{$account->uid}/$CampaignID/emails");
}

/**
 * Removes test email
 *
 * @return void
 *
 **/
function klicktipp_campaign_removetestemail($account, $CampaignID, $TestID, $EmailID) {

  SplitTests::RemoveVariantForNewsletter($account->uid, $CampaignID, $EmailID);

  drupal_set_message(t('Test email is removed.'));

  // NOTE: MigrationLinks -> will no longer exist
  drupal_goto("emails/{$account->uid}/$CampaignID/emails");
}

/**
 * Cancel sending controller
 */
function klicktipp_campaign_cancelsending($account, $CampaignID) {

  if (Campaigns::StopCampaign($account->uid, $CampaignID)) {
    drupal_set_message(t("Email dispatching successfully canceled."));
  }
  else {
    drupal_set_message(t('Error while canceling the email dispatching.'), 'error');
  }
  // NOTE: MigrationLinks -> will no longer exist
  drupal_goto("emails/{$account->uid}/$CampaignID");
}

/**
 * Helpers
 */

/**
 * create PAUSE/RESUME/CANCEL buttons based on campaign status
 */
function klicktipp_campaign_status_bar($account, $CampaignID, $CampaignInformation) {
  $isAR = Campaigns::IsAutoresponder($CampaignInformation['AutoResponderTriggerTypeEnum']);

  // Cancel sending
  $button_cancel = '';
  if (klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER)) {

    if (in_array($CampaignInformation['CampaignStatusEnum'], Campaigns::$ArrayCampaignStatiSending) ||
      ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
        $CampaignInformation['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_IMMEDIATE)
    ) {
      $button_cancel = theme('klicktipp_cancel_button', array(
        'element' => array(
          '#title' => t('Cancel sending'),
          // NOTE: MigrationLinks -> will no longer exist
          '#value' => "emails/{$account->uid}/$CampaignID/cancelsending",
        )
      ));

      $button_cancel .= theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'campaign-view-cancel-campaign',
          '#title' => t('Cancel sending'),
        )
      ));

    }
  }

  // approx recipient count
  $EstimatedRecipients = '';
  if (!empty($CampaignInformation['EstimatedRecipients'])) {
    $EstimatedRecipients = ' ' . t('Recipients ~ !count', array('!count' => $CampaignInformation['EstimatedRecipients']));
  }

  // SendProcessFinishedOn
  $SendProcessFinishedOn = '';
  if ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_DRAFT) {
    $SendProcessFinishedOn = ($isAR) ? t('Status: This autoresponder has not been started yet.') : t('Status: This newsletter has not been started yet.');
  }
  elseif ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
    $CampaignInformation['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_IMMEDIATE
  ) {
    $SendProcessFinishedOn = ($isAR ? t('Status: This autoresponder is scheduled for now.') : t('Status: This newsletter is scheduled for now.')) . $EstimatedRecipients;
  }
  elseif ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
    $CampaignInformation['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_FUTURE
  ) {
    if ($isAR) {
      $SendProcessFinishedOn = t('Status: This autoresponder is scheduled for !date.', array('!date' => Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $CampaignInformation['SendDatetime']))) . $EstimatedRecipients;
    }
    else {
      $SendProcessFinishedOn = t('Status: This newsletter is scheduled for !date.', array('!date' => Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $CampaignInformation['SendDatetime']))) . $EstimatedRecipients;
    }
  }
  elseif ($CampaignInformation['CampaignStatusEnum'] == Campaigns::STATUS_READY &&
    $CampaignInformation['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED
  ) {
    $SendProcessFinishedOn = ($isAR) ? t('Status: This autoresponder has been canceled.') : t('Status: This newsletter has been canceled.');
  }
  elseif (in_array($CampaignInformation['CampaignStatusEnum'], Campaigns::$ArrayCampaignStatiSending)) {
    $SendProcessFinishedOn = ($isAR) ? t('Status: This autoresponder is active.') : t('Status: This newsletter is being sent right now.') . $EstimatedRecipients;
    if (in_array($CampaignInformation['CampaignStatusEnum'], array(Campaigns::STATUS_CREATE_SPLITTEST_QUEUE, Campaigns::STATUS_WAITING_FOR_WINNER))) {
      // Splittest in progress. Show time when it will send winner mail
      $SplittestObject = CampaignsNewsletter::GetSplittestObject($CampaignInformation);
      $TimeToSendWinner = $SplittestObject->GetData('started') + $SplittestObject->GetData('TestDuration');
      $SendProcessFinishedOn = t('Status: Splittest in progress until !date.', array('!date' => Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $TimeToSendWinner)));
    }
  }
  else {
    $SendProcessFinishedOn = t('Status: Sent on !date', array('!date' => Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $CampaignInformation['SendProcessFinishedOn'])));
  }

  // --- Status bar ---

  $output = "
    <div id='ctrl-campaign-status' class='row'>
      <div class='col-lg-1 col-md-1 col-sm-1 col-xs-1'>
        <div class='traffic-light'></div>
      </div>
      <div class='col-lg-8 col-md-7 col-sm-7 col-xs-7'>
        <div class='status-text'>
            <div>$SendProcessFinishedOn</div>
        </div>
      </div>
      <div class='col-lg-3 col-md-4 col-sm-4 col-xs-4'>
        <div class='buttons quickhelp-inline'>$button_cancel</div>
      </div>
    </div>
    ";

  return $output;
}

/**
 * A Datetime from custom fields autoresponder is a special case of an AR.
 * Detect and switch trigger type, so the dialog works like for a standard AR.
 */
function klicktipp_campaign_datetime_to_subscription(&$CampaignInformation) {
  if ($CampaignInformation['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_DATETIME) {
    $CampaignInformation['AutoResponderTriggerTypeEnum'] = Campaigns::TRIGGER_TYPE_SUBSCRIPTION;
    $CampaignInformation['TriggerTimeTypeEnum'] = Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD;
  }
  if ($CampaignInformation['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_DATETIME) {
    $CampaignInformation['AutoResponderTriggerTypeEnum'] = Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION;
    $CampaignInformation['TriggerTimeTypeEnum'] = Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD;
  }
}

/**
 * Revert klicktipp_campaign_datetime_to_subscription
 */
function klicktipp_campaign_subscription_to_datetime(&$CampaignInformation) {
  if ($CampaignInformation['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SUBSCRIPTION) {
    if ($CampaignInformation['TriggerTimeTypeEnum'] == Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD) {
      $CampaignInformation['AutoResponderTriggerTypeEnum'] = Campaigns::TRIGGER_TYPE_DATETIME;
    }
  }
  if ($CampaignInformation['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION) {
    if ($CampaignInformation['TriggerTimeTypeEnum'] == Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD) {
      $CampaignInformation['AutoResponderTriggerTypeEnum'] = Campaigns::TRIGGER_TYPE_SMS_DATETIME;
    }
  }
}

/**
 * Campaign create function
 **/
function klicktipp_campaign_event_create($values = array()) {

  $UserID = $values['account']->uid;

  klicktipp_campaign_subscription_to_datetime($values);

  $TaggedWith = array();
  if (!empty($values['TaggedWith'])) {
    //in case user entered free values, create new tags
    //Note: last param: TRUE -> do not create manual tags from smart tag id @see: Tag::CreateManualTagByTagName
    $TaggedWith = Tag::CreateManualTagByTagName($UserID, $values['TaggedWith'], TRUE);
    if (!$TaggedWith) {
      //error creating at least 1 tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!UserID' => $UserID,
        '!function' => __FUNCTION__,
        '!OpTaggedWith' => $values['TaggedWith'],
      );
      Errors::unexpected("Error: CreateManualTagByTagName with '!OpTaggedWith' in !function for User !UserID with CampaignID !CampaignID", $error, TRUE);
      return;
    }

  }

  $NotTaggedWith = array();
  if (!empty($values['NotTaggedWith'])) {
    //in case user entered free values, create new tags
    //Note: last param: TRUE -> do not create manual tags from smart tag id @see: Tag::CreateManualTagByTagName
    $NotTaggedWith = Tag::CreateManualTagByTagName($UserID, $values['NotTaggedWith'], TRUE);
    if (!$NotTaggedWith) {
      //error creating at least 1 tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!UserID' => $UserID,
        '!function' => __FUNCTION__,
        '!OpNotTaggedWith' => $values['NotTaggedWith'],
      );
      Errors::unexpected("Error: CreateManualTagByTagName with '!OpNotTaggedWith' in !function for User !UserID", $error, TRUE);
      return;
    }

  }

  $MetaLabels = (empty($values['MetaLabels'])) ?  array() : array_keys($values['MetaLabels']);

  $Version = (empty($values['Version'])) ? 0 : 1;

  $ArrayFieldAndValues = array(
    'CampaignName' => check_plain($values['title']),
    'RelOwnerUserID' => $UserID,
    'CampaignStatusEnum' => Campaigns::STATUS_DRAFT,
    'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_NOTSCHEDULED,
    'AutoResponderTriggerTypeEnum' => $values['AutoResponderTriggerTypeEnum'],
    // not in {campaigns}
    'OpTaggedWith' => $values['OpTaggedWith'],
    'TaggedWith' => $TaggedWith,
    'OpNotTaggedWith' => $values['OpNotTaggedWith'],
    'NotTaggedWith' => $NotTaggedWith,
    'SplitTesting' => $values['SplitTesting'],
    'SubscriberAudience' => (empty($values['Audience'])) ? 0 : $values['Audience'],
    DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
    'Version' => $Version
  );

  if ($values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_CAMPAIGN) {
    //only email newsletter have the option to send it to only one subscription
    //Note: the default value (not set) for all other types is FALSE (0)
    $ArrayFieldAndValues['SendToOnlyOneSubscription'] = ($values['SendingMode'] == 'only-one');
  }

  if ($values['EditReceiverEmail'] && $values['ReceiverEmailFlag'] && !empty($values['ReceiverEmail'])) {
    $ArrayFieldAndValues['ReceiverEmail'] = $values['ReceiverEmail'];
  }

  if ($values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SUBSCRIPTION ||
    $values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION
  ) {
    $ArrayFieldAndValues['TriggerTimeTypeEnum'] = $values['TriggerTimeTypeEnum'];

    if ($values['TriggerTimeTypeEnum'] == Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY) { //send immetiately
      $ArrayFieldAndValues['TriggerTime'] = 0;
    }
    else {
      $ArrayFieldAndValues['TriggerTime'] = intval($values['TriggerTime']);

      if ( in_array($values['TriggerTimeTypeEnum'], array(
        Campaigns::TRIGGER_TIME_TYPE_DAYS, Campaigns::TRIGGER_TIME_TYPE_WEEKS, Campaigns::TRIGGER_TIME_TYPE_MONTHS)) ) {

        $ArrayFieldAndValues['UseTriggerDayOfWeek'] = $values['UseTriggerDayOfWeek'];

        if ( !empty($values['UseTriggerDayOfWeek']) ) {
          $ArrayFieldAndValues['TriggerDayOfWeek'] = array_values(array_filter($values['TriggerDayOfWeek']));
        }

        $ArrayFieldAndValues['UseDayOfWeekTimeDelay'] = $values['UseDayOfWeekTimeDelay'];

        if ( !empty($values['UseDayOfWeekTimeDelay']) ) {
          $ArrayFieldAndValues['DayOfWeekTimeDelay'] = ($values['SendTimeHour'] * 3600) + ($values['SendTimeMinute'] * 60);
        }

      }

    }

  }

  if ($values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_DATETIME ||
    $values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_DATETIME
  ) {

    $ArrayFieldAndValues['TriggerTimeTypeEnum'] = Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY;
    $ArrayFieldAndValues['TriggerTime'] = 0;
    $ArrayFieldAndValues['DelayByCustomFieldDatetimeFieldID'] = $values['DelayByCustomFieldDatetimeFieldID'];

  }

  if ($values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_BIRTHDAY ||
    $values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY
  ) {

    $ArrayFieldAndValues['TriggerTimeTypeEnum'] = Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY;
    $ArrayFieldAndValues['TriggerTime'] = 0;
    $ArrayFieldAndValues['BirthdayCustomFieldID'] = $values['BirthdayCustomFieldID'];
    $ArrayFieldAndValues['BirthdayTimeDelay'] = ($values['SendTimeHour'] * 3600) + ($values['SendTimeMinute'] * 60);

  }

  if (!empty($values['SplitTesting'])) {

    if (!Campaigns::IsAutoresponder($values['AutoResponderTriggerTypeEnum'])) {
      $ArrayFieldAndValues['TestSize'] = $values['TestSize'];
    }
    $ArrayFieldAndValues['TestDuration'] = $values['TestDurationMultiplier'] * $values['TestDurationBaseSeconds'];
    $ArrayFieldAndValues['WinnerEnum'] = $values['WinnerEnum'];

  }

  // NOTE: multiple send is not changeable, so we need this in create only
  if (!empty($values['MultipleSendFlag'])) {
    $ArrayFieldAndValues['MultipleSendFlag'] = 1;
    //in case user entered free values in TaggedWith, we need the tag ids from the copied names
    //so this wont create tags, but fetch those that were created before
    $ArrayFieldAndValues['MultipleSendTags'] = Tag::CreateManualTagByTagName($UserID, $values['MultipleSendTags'], TRUE);
    if (!$ArrayFieldAndValues['MultipleSendTags']) {
      //error creating at least 1 tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
      $error = array(
        '!UserID' => $UserID,
        '!function' => __FUNCTION__,
        '!MultipleSendTags' => $values['MultipleSendTags'],
      );
      Errors::unexpected("Error: CreateManualTagByTagName with MultipleSendTags '!MultipleSendTags' in !function for User !UserID with CampaignID !CampaignID", $error, TRUE);
      return;
    }
  }

  $CampaignID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);

  // reset cache
  Tag::ResetTagsCache($UserID);

  return $CampaignID;
}

/**
 * Campaign save information function
 *
 **/
function klicktipp_campaign_event_save($values = array()) {

  $CampaignInformation = $values['CampaignInformation'];
  $CampaignID = $CampaignInformation['CampaignID'];
  $UserID = $CampaignInformation['RelOwnerUserID'];
  $IsAutoresponder = $values['IsAutoResponder'];
  $DialogEditStatus = $values['EditCampaign']; //TRUE: the campaign was editable when the dialog was called
  $MetaLabels = (empty($values['MetaLabels'])) ?  array() : array_keys($values['MetaLabels']);

  //get the campaign from the database (no caching) to check the edit status, in case the campaign has been sent already or and old browser tab was used
  $CheckStatusCampaign = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
  //edit_campaign TRUE -> camapign was editable in the dialog and still has the status draft or ready && not scheduled): the user is allowed to edit trigger time and tags
  $edit_campaign = ($DialogEditStatus && (($CheckStatusCampaign['CampaignStatusEnum'] == Campaigns::STATUS_DRAFT) ||
      ($CheckStatusCampaign['CampaignStatusEnum'] == Campaigns::STATUS_READY && $CheckStatusCampaign['ScheduleTypeEnum'] == Campaigns::SCHEDULE_TYPE_NOTSCHEDULED)));

  $edit_splittest = $values['EditSplittest']; // is the campaign a splittest campaign and is it editable (Status and user_access)
  $IsSplittest = (!empty($values['SplitTesting']) || $edit_splittest); //new splittest campaign or existing one

  klicktipp_campaign_subscription_to_datetime($values);

  // Campaign update - Start {
  $ArrayFieldAndValues = array(
    'CampaignName' => check_plain($values['title']),
    DatabaseTableLabeled::LABEL_DATA_KEY => $MetaLabels,
  );

  if ($values['EditReceiverEmail'] && $values['ReceiverEmailFlag'] && !empty($values['ReceiverEmail'])) {
    $ArrayFieldAndValues['ReceiverEmail'] = $values['ReceiverEmail'];
  }
  else {
    $ArrayFieldAndValues['ReceiverEmail'] = '';
  }

  if ($edit_campaign) {

    if ($IsAutoresponder) {

      // defaults for autoresponder
      $ArrayFieldAndValues['TriggerTimeTypeEnum'] = Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY;
      $ArrayFieldAndValues['TriggerTime'] = 0;
      $ArrayFieldAndValues['BirthdayCustomFieldID'] = '';
      $ArrayFieldAndValues['BirthdayTimeDelay'] = '';
      $ArrayFieldAndValues['DelayByCustomFieldDatetimeFieldID'] = '';
      $ArrayFieldAndValues['DayOfWeekTimeDelay'] = '';

      if ($values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SUBSCRIPTION ||
        $values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION
      ) {

        // we cna change types SUBSCRIPTION <--> DATETIME
        $ArrayFieldAndValues['AutoResponderTriggerTypeEnum'] = $values['AutoResponderTriggerTypeEnum'];
        $ArrayFieldAndValues['TriggerTimeTypeEnum'] = $values['TriggerTimeTypeEnum'];
        if ($values['TriggerTimeTypeEnum'] == Campaigns::TRIGGER_TIME_TYPE_IMMEDIATELY) {
          //send immediately
          $ArrayFieldAndValues['TriggerTime'] = 0;
        }
        else {
          $ArrayFieldAndValues['TriggerTime'] = intval($values['TriggerTime']);

          if ( in_array($values['TriggerTimeTypeEnum'], array(
            Campaigns::TRIGGER_TIME_TYPE_DAYS, Campaigns::TRIGGER_TIME_TYPE_WEEKS, Campaigns::TRIGGER_TIME_TYPE_MONTHS)) ) {

            $ArrayFieldAndValues['UseTriggerDayOfWeek'] = $values['UseTriggerDayOfWeek'];

            if ( !empty($values['UseTriggerDayOfWeek']) ) {
              $ArrayFieldAndValues['TriggerDayOfWeek'] = array_values(array_filter($values['TriggerDayOfWeek']));
            }

            $ArrayFieldAndValues['UseDayOfWeekTimeDelay'] = $values['UseDayOfWeekTimeDelay'];

            if ( !empty($values['UseDayOfWeekTimeDelay']) ) {
              $ArrayFieldAndValues['DayOfWeekTimeDelay'] = ($values['SendTimeHour'] * 3600) + ($values['SendTimeMinute'] * 60);
            }

          }

        }

      }

      if ($values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_DATETIME ||
        $values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_DATETIME
      ) {
        // we can change types SUBSCRIPTION <--> DATETIME
        $ArrayFieldAndValues['AutoResponderTriggerTypeEnum'] = $values['AutoResponderTriggerTypeEnum'];
        $ArrayFieldAndValues['DelayByCustomFieldDatetimeFieldID'] = $values['DelayByCustomFieldDatetimeFieldID'];
      }

      if ($values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_BIRTHDAY ||
        $values['AutoResponderTriggerTypeEnum'] == Campaigns::TRIGGER_TYPE_SMS_BIRTHDAY
      ) {
        $ArrayFieldAndValues['BirthdayCustomFieldID'] = $values['BirthdayCustomFieldID'];
        $ArrayFieldAndValues['BirthdayTimeDelay'] = ($values['SendTimeHour'] * 3600) + ($values['SendTimeMinute'] * 60);
      }

    }
    else {
      // update subscriber audience for campaigns
      $ArrayFieldAndValues['SubscriberAudience'] = (empty($values['Audience'])) ? 0 : $values['Audience'];
    }

    //update the recipent tags

    $TaggedWith = array();
    if (!empty($values['TaggedWith'])) {
      //in case user entered free values, create new tags
      //Note: last param: TRUE -> do not create manual tags from smart tag id @see: Tag::CreateManualTagByTagName
      $TaggedWith = Tag::CreateManualTagByTagName($UserID, $values['TaggedWith'], TRUE);
      if (!$TaggedWith) {
        //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
        $error = array(
          '!UserID' => $UserID,
          '!function' => __FUNCTION__,
          '!CampaignID' => $CampaignID,
          '!OpTaggedWith' => implode("', '", $values['TaggedWith']),
        );
        Errors::unexpected("Error: CreateManualTagByTagName with '!OpTaggedWith' in !function for User !UserID with CampaignID !CampaignID", $error, TRUE);
        return;
      }

    }

    $NotTaggedWith = array();
    if (!empty($values['NotTaggedWith'])) {
      //in case user entered free values, create new tags
      //Note: last param: TRUE -> do not create manual tags from smart tag id @see: Tag::CreateManualTagByTagName
      $NotTaggedWith = Tag::CreateManualTagByTagName($UserID, $values['NotTaggedWith'], TRUE);
      if (!$NotTaggedWith) {
        //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
        $error = array(
          '!UserID' => $UserID,
          '!function' => __FUNCTION__,
          '!CampaignID' => $CampaignID,
          '!OpNotTaggedWith' => implode("', '", $values['NotTaggedWith']),
        );
        Errors::unexpected("Error: CreateManualTagByTagName with '!OpNotTaggedWith' in !function for User !UserID with CampaignID !CampaignID", $error, TRUE);
        return;
      }

    }

    $ArrayFieldAndValues['OpTaggedWith'] = $values['OpTaggedWith'];
    $ArrayFieldAndValues['TaggedWith'] = $TaggedWith;
    $ArrayFieldAndValues['OpNotTaggedWith'] = $values['OpNotTaggedWith'];
    $ArrayFieldAndValues['NotTaggedWith'] = $NotTaggedWith;

    if ($IsSplittest) {

      $ArrayFieldAndValues['TestSize'] = ($IsAutoresponder) ? 0 : $values['TestSize'];
      $ArrayFieldAndValues['TestDuration'] = $values['TestDurationMultiplier'] * $values['TestDurationBaseSeconds'];
      $ArrayFieldAndValues['WinnerEnum'] = $values['WinnerEnum'];

    }

  }

  $result = Campaigns::UpdateCampaign($UserID, $CampaignID, $ArrayFieldAndValues);

  // reset cache
  if ($CampaignInformation['CampaignName'] != $ArrayFieldAndValues['CampaignName']) {
  Tag::ResetTagsCache($UserID);
  }

  return $result;


}

/**
 * Retrieves a list of URLs for various domains to be shown within a pixel snippet
 *
 * Response should have following format: array(domain => https://alias.domain/)
 *
 * @param $UserID int ID of the user
 * @param $Urls array of domain and pixel urls
 * @param $EmailIDs array of email IDs
 *
 * @return array
 */
function klicktipp_campaign_filter_pixel_urls_by_email_sender_domains($UserID, $Urls, $EmailIDs) {
  return Campaigns::filterConversionPixelUrlsByEmailSenderDomains($UserID, $Urls, $EmailIDs);
}

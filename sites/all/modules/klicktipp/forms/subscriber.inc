<?php

use App\Klicktipp\BounceEngine;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsProcessFlow;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Dates;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\Errors;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\PaymentIPNs;
use App\Klicktipp\ProcessLog;
use App\Klicktipp\Reference;
use App\Klicktipp\Signatures;
use App\Klicktipp\SmartLink;
use App\Klicktipp\Statistics;
use App\Klicktipp\Subaccount;
use App\Klicktipp\Subscriber;
use App\Klicktipp\SubscriberDuplicates;
use App\Klicktipp\Subscribers;
use App\Klicktipp\SubscribersSearch;
use App\Klicktipp\SubscribersSearchDialog;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;
use App\Klicktipp\ToolFacebookAudience;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\UserCache;
use App\Klicktipp\UserGroups;
use App\Klicktipp\VarImport;
use App\Klicktipp\VarPrivacy;
use App\Klicktipp\VarSubscriberAudience;
use App\Klicktipp\Wufoo;

define('CONTROLLER_SUBSCRIBER_SAVE_BUTTON_TEXT', /*t(*/'Save Contact'/*)*/);
define('CONTROLLER_SUBSCRIBER_SEARCH_BUTTON_TEXT', /*t(*/'Search'/*)*/);
define('CONTROLLER_SUBSCRIBER_RESET_BUTTON_TEXT', /*t(*/'Reset'/*)*/);
define('CONTROLLER_SUBSCRIBER_EXPORT_BUTTON_TEXT', /*t(*/'Export contacts'/*)*/);

//Tablesort Headers
define('TABLESORT_OPTIN_DATE', /*t(*/'Opt-In Date'/*)*/);
define('TABLESORT_CONFIRMATION_DATE', /*t(*/'Confirmation Date'/*)*/);
define('TABLESORT_OPTOUT_DATE', /*t(*/'Opt-Out Date'/*)*/);
define('TABLESORT_FIRSTNAME', /*t(*/'FirstName'/*)*/);
define('TABLESORT_LASTNAME', /*t(*/'LastName'/*)*/);

define('CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED', 'all');

define('CONTROLLER_SUBSCRIBER_HISTORY_FILTER_SUBSCRIPTION', 'subscription');
define('CONTROLLER_SUBSCRIBER_HISTORY_FILTER_TAGGING', 'tagging');
define('CONTROLLER_SUBSCRIBER_HISTORY_FILTER_INFO', 'info');

function klicktipp_show_subscriber_duplicate_hint($UserID, $SubscriberID) {
  // hint for duplicate subscribers
  $filter = ['RelSubscriberID' => (int)$SubscriberID, 'Status' => SubscriberDuplicates::STATUS_OPEN];
  $duplicates = SubscriberDuplicates::getDuplicates($UserID, [], $filter, 0, 10);

  if ($duplicates->rowCount()) {

    $duplicateSubscriberLinks = [];

    foreach ($duplicates as $duplicate) {
      $DuplicateSubscriberID = $duplicate["DuplicateSubscriberID"];
      $duplicateSubscriberLinks[] = l(
        $DuplicateSubscriberID,
        "/subscriber/$UserID/edit/$DuplicateSubscriberID",
        [
          "attributes" => [
            "target" => "_blank"
          ]
        ]
      );
    }

    $linkToDuplicatesDashboard = l(t('Show all duplicate contacts.'), "/subscribers/$UserID/$SubscriberID/duplicates");

    $message = t(
      "KlickTipp has detected potential duplicates for this contact: !events.<br><br>!linkToDuplicatesDashboard",
      [
        "!events" => implode(", ", $duplicateSubscriberLinks),
        "!linkToDuplicatesDashboard" => $linkToDuplicatesDashboard
      ]
    );

    drupal_set_message($message, "info", FALSE);

  }
}

/**
 * Callback for contact search
 */
function klicktipp_subscriber_search_form($form, &$form_state, $account, $PagerPage = 1, $PagerSize = 0) {

  //Note: check the logged in user so support users can test with big customer accounts
  $UseAjaxSearch = user_access('access ajax subscriber search');

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Search Contacts');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $CustomFields = CustomFields::RetrieveCustomFieldsCategories($UserID);

  // sort field by name desc
  $sortByName = function($fieldA, $fieldB) {
    $a = CustomFields::GetFieldName($fieldA);
    $b = CustomFields::GetFieldName($fieldB);
    return $a <=> $b;
  };

  //IMPORTANT: use uasort to keep the keys
  uasort($CustomFields['AllCustomFields'], $sortByName);

  $weight = 1;

  //set Status = subscribed and BounceType = not bounced as default search settings
  $_SESSION['subscriber_search_filter'] = is_array($_SESSION['subscriber_search_filter']) ?
    $_SESSION['subscriber_search_filter'] :
    array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS,
      'EmailSearch' => array(
        'Status' => array(SubscribersSearch::STATUS_SUBSCRIBED),
        'BounceStatus' => array(
          Subscribers::BOUNCETYPE_NOTBOUNCED,
          Subscribers::BOUNCETYPE_SOFT,
        ),
      ),
      'CustomFields' => array(
        'FirstName' => 5,
        'LastName' => 5,
        'CompanyName' => 5,
        'City' => 5,
      ),
      'FormValue_Fields' => array(
        'CustomFieldFirstName' => '',
        'CustomFieldLastName' => '',
        'CustomFieldCompanyName' => '',
        'CustomFieldCity' => '',
      ),
    );

  if (empty($_SESSION['subscriber_search_filter']['Session'])) {
    //generate search session
    $_SESSION['subscriber_search_filter']['Session'] = time();
  }

  $SearchMode = $_SESSION['subscriber_search_filter']['SearchMode'];
  $EmailSearch = $_SESSION['subscriber_search_filter']['EmailSearch'];
  $SMSSearch = $_SESSION['subscriber_search_filter']['SMSSearch'];
  $OpTaggedWith = $_SESSION['subscriber_search_filter']['OpTaggedWith'];
  $TaggedWith = $_SESSION['subscriber_search_filter']['TaggedWith'];
  $OpNotTaggedWith = $_SESSION['subscriber_search_filter']['OpNotTaggedWith'];
  $NotTaggedWith = $_SESSION['subscriber_search_filter']['NotTaggedWith'];

  //the search form is loaded/reloaded, unset previous subscriber selection for delete, assign tag, unsubscribe
  unset($_SESSION['subscriber_search_selection']);

  $form = array();

  //title, breadcrumb, javascript
  $form['#pre_render'][] = 'klicktipp_subscriber_search_form_pre_render';

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  //when the search form is displayed it contains the search result for the
  //current search session (a search session is generated when the search button is clicked)
  //the javascript of the modal dialogs will read this value to make sure that the search mask
  //has not been changed in another tab
  $form['Session'] = array(
    '#type' => 'hidden',
    '#default_value' => $_SESSION['subscriber_search_filter']['Session'],
    '#attributes' => array('id' => 'search-session'),
    //Note: some kind of form caching doesn't update the hidden field value after submit
    '#suffix' => '
      <script type="text/javascript">
        $("#search-session").val("' . $_SESSION['subscriber_search_filter']['Session'] . '");
      </script>
    '
  );

  $createButtons = [
    [
      '#title' => t('Action::Add::Contact'),
      '#value' => "contacts/$UserID/add",
      '#attributes' => [
        'data-e2e-id' => 'search-button-add-contact'
      ]
    ],
  ];

  if (klicktipp_subscriber_import_access($account)) {
    $createButtons[] = [
      '#title' => t('Action::Import::Contact'),
      '#value' => "app/import/$UserID",
      '#attributes' => [
        'data-e2e-id' => 'search-button-import-contacts'
      ]
    ];
  }

  $form['create_buttons'] = [
    '#theme' => 'klicktipp_create_buttons',
    '#buttons' => $createButtons,
    '#weight' => $weight++,
    '#prefix' => '<div class="create-buttons"><div class="clearfix"><div class="right">',
    '#suffix' => '</div></div></div>',
  ];

  // --- search mode
  $form['SearchMode'] = array(
    '#type' => 'hidden',
    '#default_value' => (!empty($SearchMode)) ? $SearchMode : SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS,
    '#attributes' => array(
      'data-event-load' => 'js_init_search_mode'
    )
  );

  $audienceID = $_SESSION['subscriber_search_filter']['SubscriberAudiences'];
  $audienceOptions = VarSubscriberAudience::RetrieveAudiencesAsOptionsArray($UserID);
  if (!empty($audienceOptions)) {
    $form['SubscriberAudience'] = array(
      '#type' => 'fieldset',
      '#title' => t("Subscriber Audience"),
      '#weight' => $weight++,
      '#collapsible' => TRUE,
      '#collapsed' => (empty($audienceID)),
    );

    $defaultOption = array(0 => t('Please select'));
    // use + operator to not rearrange the keys
    $audienceOptions = $defaultOption + $audienceOptions;
    $form['SubscriberAudience']['Audiences'] = klicktipp_overview_filter(
      array('SubscriberAudiences' => $audienceOptions),
      array('SubscriberAudiences' => (empty($audienceID)) ? 0 : $audienceID),
      $weight,
      t('Subscriber Audience')
    );

    if (!empty($audienceID)) {
      $form['SubscriberAudience']['buttons'] = array(
        '#type' => 'markup',
        '#weight' => $weight++,
        '#prefix' => '<div class="button-row">',
        '#suffix' => '</div>',
      );
      $form['SubscriberAudience']['buttons']['AudienceEdit'] = array(
        '#theme' => 'klicktipp_edit_modal_button',
        '#title' => t('Edit'),
        '#value' => 'klicktipp_subscriber_audience_edit_form',
        '#weight' => $weight++,
        '#modal_confirm' => drupal_get_form('klicktipp_subscriber_audience_edit_form', $account, $audienceID, $audienceOptions),
      );
      $form['SubscriberAudience']['buttons']['AudienceDelete'] = array(
        '#theme' => 'klicktipp_delete_modal_button',
        '#title' => t('Delete'),
        '#value' => 'klicktipp_subscriber_audience_delete_form',
        '#weight' => $weight++,
        '#modal_confirm' => drupal_get_form('klicktipp_subscriber_audience_delete_form', $account, $audienceID),
      );
    }

  }

  // --- search by email subscription

  $form['EmailSearch'] = array(
    '#type' => 'fieldset',
    '#title' => t('Email marketing information'),
    '#collapsible' => TRUE,
    '#collapsed' => !in_array($SearchMode, [SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS, SubscribersSearch::SEARCH_BY_EMAIL_AND_PHONE_NUMBER]),
    '#weight' => $weight++,
    '#tree' => TRUE,
    '#attributes' => array(
      'data-event-show-collapsed' => 'js_toggle_search_mode',
      'data-event-show-collapsed-args' => '.search-mode-email',
      'data-event-hidden-collapsed' => 'js_toggle_search_mode',
      'data-event-hidden-collapsed-args' => 'collapse',
      'class' => array('search-mode-email'),
    ),
  );

  //TODO UI:  on SearchMode = IS_EMPTY, we should hide the status, bounce, date filter
  $EmailPhoneNumberOptions = array(
    0 => t("Please select"),
    SubscribersSearch::IS => t('is'),
    SubscribersSearch::IS_NOT => t('is not'),
    SubscribersSearch::CONTAINS => t('contains'),
    SubscribersSearch::CONTAINS_NOT => t('does not contain'),
    SubscribersSearch::STARTS_WITH => t('starts with'),
    SubscribersSearch::STARTS_NOT_WITH => t('does not start with'),
    SubscribersSearch::ENDS_WITH => t('ends with'),
    SubscribersSearch::ENDS_NOT_WITH => t('does not end with'),
    SubscribersSearch::IS_EMPTY => t('is empty'),
    SubscribersSearch::IS_NOT_EMPTY => t('is not empty'),
  );

  $form['EmailSearch']['EmailAddressOp'] = array(
    '#type' => 'select',
    '#title' => t('Email Address'),
    '#default_value' => (!empty($EmailSearch['EmailAddressOp'])) ? $EmailSearch['EmailAddressOp'] : SubscribersSearch::CONTAINS,
    '#options' => $EmailPhoneNumberOptions,
    '#weight' => $weight++,
    '#attributes' => array(
      'class' => array('search-op'),
      'data-event-load' => 'js-toggle-contactinfo-search-mask',
      'data-event-load-args' => '#email-address-search-mask',
      'data-event-change' => 'js-toggle-contactinfo-search-mask',
      'data-event-change-args' => '#email-address-search-mask',
    ),
  );

  $form['EmailSearch']['EmailAddress'] = array(
    '#type' => 'textfield',
    '#weight' => $weight++,
    '#default_value' => $EmailSearch['EmailAddress'],
    '#quickhelp' => 'contact-search-email-address',
    '#prefix' => '<div id="email-address-search-mask">',
    '#attributes' => array(
      'class' => array('contactinfo-field'),
    )
  );

  //subscription status
  $StatusOptions = array(
    '' => '',
    SubscribersSearch::STATUS_SUBSCRIBED => t('Subscribed'),
    SubscribersSearch::STATUS_UNSUBSCRIBED => t('Unsubscribed'),
    SubscribersSearch::STATUS_PENDING => t('Pending'),
    SubscribersSearch::STATUS_PENDING_7DAYS => t('Pending more than 7 days'),
    SubscribersSearch::STATUS_IMPORT => t('Imported'),
  );

  $BounceOptions = array(
    '' => '',
    Subscribers::BOUNCETYPE_NOTBOUNCED => t('Not Bounced'),
    Subscribers::BOUNCETYPE_SOFT => t('Soft bounced'),
    Subscribers::BOUNCETYPE_SPAM => t('Spam bounced'),
    Subscribers::BOUNCETYPE_HARD => t('Hard bounced'),
  );

  $DateOptions = array(
    0 => t("Please select"),
    SubscribersSearch::DATE_ON => t("on(date)"),
    SubscribersSearch::DATE_NOT_ON => t("not on(date)"),
    SubscribersSearch::DATE_BEFORE => t("before(date)"),
    SubscribersSearch::DATE_AFTER => t("after(date)"),
    SubscribersSearch::DATE_BETWEEN => t("between(date)"),
    SubscribersSearch::DATE_NOT_BETWEEN => t("not between(date)"),
  );

  $form['EmailSearch']['Status'] = array(
    '#type' => 'select',
    '#default_value' => (empty($EmailSearch['Status'])) ? array() : $EmailSearch['Status'],
    '#title' => t("Subscription status"),
    '#weight' => $weight++,
    '#options' => $StatusOptions,
    '#multiple' => TRUE,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => '#options',
      'multiple' => TRUE,
      'free_entries' => FALSE,
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'contact-search-email-status',
  );

  $form['EmailSearch']['BounceStatus'] = array(
    '#type' => 'select',
    '#default_value' => (empty($EmailSearch['BounceStatus'])) ? array() : $EmailSearch['BounceStatus'],
    '#title' => t("Bounce status"),
    '#weight' => $weight++,
    '#options' => $BounceOptions,
    '#multiple' => TRUE,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => '#options',
      'multiple' => TRUE,
      'free_entries' => FALSE,
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'contact-search-bounce-status',
  );

  $form['EmailSearch']['Subscription'] = array(
    '#weight' => $weight++,
    '#prefix' => '<div class="enhanced-date">',
    '#suffix' => '</div>',
  );

  $form['EmailSearch']['Subscription']['OpSubscriptionDate'] = array(
    '#type' => 'select',
    '#title' => t("Opt-In Date"),
    '#default_value' => (empty($EmailSearch['Subscription']['OpSubscriptionDate'])) ? 0 : $EmailSearch['Subscription']['OpSubscriptionDate'],
    '#options' => $DateOptions,
    '#weight' => $weight++,
    //'#parents' => array('Subscription', 'OpSubscriptionDate'),
    '#attributes' => array(
      'data-event-load' => 'js_widget_date_enhanced',
      //show/hide SubscriptionDateUntil on page load
      'data-event-load-args' => 'DateEnhancedSubscription',
      //array key in Drupal.settings @see pre_render
      'data-event-change' => 'js_widget_date_enhanced',
      //show/hide SubscriptionDateUntil on changed
      'data-event-change-args' => 'DateEnhancedSubscription',
      //array key in Drupal.settings @see pre_render
    ),
    '#quickhelp' => 'contact-search-subscription-status',
  );

  $form['EmailSearch']['Subscription']['SubscriptionDate'] = array(
    '#type' => 'textfield',
    '#default_value' => $EmailSearch['Subscription']['SubscriptionDate'],
    '#weight' => $weight++,
    //'#parents' => array('Subscription', 'SubscriptionDate'),
    '#attributes' => array('class' => array('subscription-date')),
    '#datepicker' => TRUE,
  );

  $form['EmailSearch']['Subscription']['And'] = array(
    '#type' => 'markup',
    '#value' => "<div class='subscription-date-until'>" . t('and') . "</div>",
    '#weight' => $weight++,
  );

  $form['EmailSearch']['Subscription']['SubscriptionDateUntil'] = array(
    '#type' => 'textfield',
    '#default_value' => $EmailSearch['Subscription']['SubscriptionDateUntil'],
    '#weight' => $weight++,
    //'#parents' => array('Subscription', 'SubscriptionDateUntil'),
    '#attributes' => array('class' => array('subscription-date-until')),
    '#datepicker' => TRUE,
  );

  $form['EmailSearch']['Confirmation'] = array(
    '#weight' => $weight++,
    '#prefix' => '<div class="enhanced-date">',
    '#suffix' => '</div>',
  );

  $form['EmailSearch']['Confirmation']['OpConfirmationDate'] = array(
    '#type' => 'select',
    '#title' => t("Confirmation Date"),
    '#default_value' => (empty($EmailSearch['Subscription']['OpConfirmationDate'])) ? 0 : $EmailSearch['Subscription']['OpConfirmationDate'],
    '#options' => $DateOptions,
    '#weight' => $weight++,
    '#parents' => array('EmailSearch', 'Subscription', 'OpConfirmationDate'),
    '#attributes' => array(
      'data-event-load' => 'js_widget_date_enhanced',
      //show/hide ConfirmationDateUntil on page load
      'data-event-load-args' => 'DateEnhancedConfirmation',
      //array key in Drupal.settings @see pre_render
      'data-event-change' => 'js_widget_date_enhanced',
      //show/hide ConfirmationDateUntil on change
      'data-event-change-args' => 'DateEnhancedConfirmation',
      //array key in Drupal.settings @see pre_render
    ),
    '#quickhelp' => 'contact-search-confirmation-status',
  );

  $form['EmailSearch']['Confirmation']['ConfirmationDate'] = array(
    '#type' => 'textfield',
    '#default_value' => $EmailSearch['Subscription']['ConfirmationDate'],
    '#weight' => $weight++,
    '#parents' => array('EmailSearch', 'Subscription', 'ConfirmationDate'),
    '#attributes' => array('class' => array('confirmation-date')),
    '#datepicker' => TRUE,
  );

  $form['EmailSearch']['Confirmation']['And'] = array(
    '#type' => 'markup',
    '#value' => "<div class='confirmation-date-until'>" . t('and') . "</div>",
    '#weight' => $weight++,
  );

  $form['EmailSearch']['Confirmation']['ConfirmationDateUntil'] = array(
    '#type' => 'textfield',
    '#default_value' => $EmailSearch['Subscription']['ConfirmationDateUntil'],
    '#weight' => $weight++,
    '#parents' => array('EmailSearch', 'Subscription', 'ConfirmationDateUntil'),
    '#attributes' => array('class' => array('confirmation-date-until')),
    '#datepicker' => TRUE,
    '#suffix' => '</div>', //email-address-search-mask
  );

  // --- search by sms marketing information

  $form['SMSSearch'] = array(
    '#type' => 'fieldset',
    '#title' => t('SMS marketing information'),
    '#collapsible' => TRUE,
    '#collapsed' => !in_array($SearchMode, [SubscribersSearch::SEARCH_BY_PHONE_NUMBER, SubscribersSearch::SEARCH_BY_EMAIL_AND_PHONE_NUMBER]),
    '#weight' => $weight++,
    '#tree' => TRUE,
    '#attributes' => array(
      'data-event-show-collapsed' => 'js_toggle_search_mode',
      'data-event-show-collapsed-args' => '.search-mode-phonenumber',
      'data-event-hidden-collapsed' => 'js_toggle_search_mode',
      'data-event-hidden-collapsed-args' => 'collapse',
      'class' => array('search-mode-phonenumber'),
    ),
  );

  $form['SMSSearch']['SMSPhoneNumberOp'] = array(
    '#type' => 'select',
    '#title' => t('SMS Phone number'),
    '#default_value' => (!empty($SMSSearch['SMSPhoneNumberOp'])) ? $SMSSearch['SMSPhoneNumberOp'] : SubscribersSearch::CONTAINS,
    '#options' => $EmailPhoneNumberOptions,
    '#weight' => $weight++,
    '#attributes' => array(
      'class' => array('search-op'),
      'data-event-load' => 'js-toggle-contactinfo-search-mask',
      'data-event-load-args' => '#phonenumber-search-mask',
      'data-event-change' => 'js-toggle-contactinfo-search-mask',
      'data-event-change-args' => '#phonenumber-search-mask'
    ),
  );

  $form['SMSSearch']['SMSPhoneNumber'] = array(
    '#type' => 'textfield',
    '#weight' => $weight++,
    '#default_value' => $SMSSearch['SMSPhoneNumber'],
    '#quickhelp' => 'contact-search-sms-phonenumber',
    '#prefix' => '<div id="phonenumber-search-mask">',
    '#attributes' => array(
      'class' => array('contactinfo-field'),
    )
  );

  //subscription status
  $SMSStatusOptions = array(
    '' => '',
    Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED => t('Subscribed'),
    Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED => t('Unsubscribed'),
  );

  $form['SMSSearch']['SMSStatus'] = array(
    '#type' => 'select',
    '#default_value' => (!empty($SMSSearch['SMSStatus'])) ? $SMSSearch['SMSStatus'] : array(),
    '#title' => t("SMS Subscription status"),
    '#weight' => $weight++,
    '#options' => $SMSStatusOptions,
    '#multiple' => TRUE,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => '#options',
      'multiple' => TRUE,
      'free_entries' => FALSE,
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'contact-search-sms-status',
  );

  $form['SMSSearch']['SMSBounceStatus'] = array(
    '#type' => 'select',
    '#default_value' => (!empty($SMSSearch['SMSBounceStatus'])) ? $SMSSearch['SMSBounceStatus'] : array(),
    '#title' => t("Bounce status"),
    '#weight' => $weight++,
    '#options' => $BounceOptions,
    '#multiple' => TRUE,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => '#options',
      'multiple' => TRUE,
      'free_entries' => FALSE,
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'contact-search-sms-bounce-status',
  );

  $form['SMSSearch']['SMSSubscriptionDate'] = array(
    '#weight' => $weight++,
    '#prefix' => '<div class="enhanced-date">',
    '#suffix' => '</div>',
  );

  $form['SMSSearch']['SMSSubscriptionDate']['OpDate'] = array(
    '#type' => 'select',
    '#title' => t("Opt-In Date"),
    '#default_value' => $SMSSearch['SMSSubscriptionDate']['OpDate'],
    '#options' => $DateOptions,
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-load' => 'js_widget_date_enhanced',
      //show/hide SubscriptionDateUntil on page load
      'data-event-load-args' => 'DateEnhancedSMSSubscription',
      //array key in Drupal.settings @see pre_render
      'data-event-change' => 'js_widget_date_enhanced',
      //show/hide SubscriptionDateUntil on changed
      'data-event-change-args' => 'DateEnhancedSMSSubscription',
      //array key in Drupal.settings @see pre_render
    ),
    '#quickhelp' => 'contact-search-sms-subscription-date',
  );

  $form['SMSSearch']['SMSSubscriptionDate']['Date'] = array(
    '#type' => 'textfield',
    '#default_value' => $SMSSearch['SMSSubscriptionDate']['Date'],
    '#weight' => $weight++,
    '#attributes' => array('class' => array('sms-subscription-date')),
    '#datepicker' => TRUE,
  );

  $form['SMSSearch']['SMSSubscriptionDate']['And'] = array(
    '#type' => 'markup',
    '#markup' => "<div class='sms-subscription-date-until'>" . t('and') . "</div>",
    '#weight' => $weight++,
  );

  $form['SMSSearch']['SMSSubscriptionDate']['DateUntil'] = array(
    '#type' => 'textfield',
    '#default_value' => $SMSSearch['SMSSubscriptionDateUntil']['DateUntil'],
    '#weight' => $weight++,
    '#attributes' => array('class' => array('sms-subscription-date-until')),
    '#datepicker' => TRUE,
    '#suffix' => '</div>'
  );

  // --- custom fields

  $collapsed = FALSE;
  foreach ($_SESSION['subscriber_search_filter']['CustomFields'] as $cfID => $op) {
    $value = $_SESSION['subscriber_search_filter']['FormValue_Fields']["CustomField$cfID"];
    if (!empty($op) && !empty($value)) {
      $collapsed = FALSE;
      break;
    }
  }

  $form['CustomFields'] = array(
    '#type' => 'fieldset',
    '#title' => t("Custom fields"),
    '#weight' => $weight++,
    '#collapsible' => TRUE,
    '#collapsed' => $collapsed,
  );

  $form['CustomFields']['UseCustomField'] = array(
    '#type' => 'select',
    '#title' => t("Search for a value in custom field"),
    '#options' => array(0 => t("Please select")),
    '#weight' => $weight++,
    '#attributes' => array(
      'data-event-change' => 'js_select_show_option_selector',
    ),
    '#quickhelp' => 'contact-search-select-custom-field',
    '#suffix' => '<hr />'
  );

  //make sure the pre-selected fields are displayed on top
  $allFields = $CustomFields['AllCustomFields'];
  $displayFields = [
    'FirstName' => $allFields['FirstName'],
    'LastName' => $allFields['LastName'],
    'CompanyName' => $allFields['CompanyName'],
    'City' => $allFields['City'],
  ];
  unset($allFields['FirstName']);
  unset($allFields['LastName']);
  unset($allFields['CompanyName']);
  unset($allFields['City']);
  $displayFields += $allFields;

  foreach ($displayFields as $CustomFieldID => $EachField) {

    $CustomFieldName = CustomFields::GetFieldName($EachField);
    $between = "";

    $operations = array(
      0 => t("Please select"),
      -1 => t("Dismiss"),
    );

    if ($EachField['FieldTypeEnum'] != CustomFields::TYPE_CHECKBOX) {
      $operations[SubscribersSearch::IS_EMPTY] = t('is empty');
      $operations[SubscribersSearch::IS_NOT_EMPTY] = t('is not empty');
      if (!in_array($EachField['FieldTypeEnum'], array(
        CustomFields::TYPE_DATE,
        CustomFields::TYPE_TIME,
        CustomFields::TYPE_DATETIME,
        CustomFields::TYPE_HTML
      ))
      ) {
        $operations[SubscribersSearch::IS] = t('is');
        $operations[SubscribersSearch::IS_NOT] = t('is not');
      }
    }
    else {
      $operations[SubscribersSearch::CHECKED] = t('checked');
    }

    if (in_array($EachField['FieldTypeEnum'], array(
      CustomFields::TYPE_SINGLE,
      CustomFields::TYPE_PARAGRAPH,
      CustomFields::TYPE_EMAIL,
      CustomFields::TYPE_URL,
    ))
    ) {
      $operations[SubscribersSearch::CONTAINS] = t('contains');
      $operations[SubscribersSearch::CONTAINS_NOT] = t('does not contain');
      $operations[SubscribersSearch::STARTS_WITH] = t('starts with');
      $operations[SubscribersSearch::STARTS_NOT_WITH] = t('does not start with');
      $operations[SubscribersSearch::ENDS_WITH] = t('ends with');
      $operations[SubscribersSearch::ENDS_NOT_WITH] = t('does not end with');
    }

    if (in_array($EachField['FieldTypeEnum'], array(
      CustomFields::TYPE_NUMBER,
      CustomFields::TYPE_DECIMAL,
    ))
    ) {
      $operations[SubscribersSearch::LESS_THAN] = t('less than');
      $operations[SubscribersSearch::GREATER_THAN] = t('greater than');
    }
    else {
      if (in_array($EachField['FieldTypeEnum'], array(
        CustomFields::TYPE_DATE,
        CustomFields::TYPE_TIME,
        CustomFields::TYPE_DATETIME,
      ))
      ) {

        $css = "jstoggle-date-until-$CustomFieldID";

        if ($EachField['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
          $operations[SubscribersSearch::ON] = t('on(date)');
          $operations[SubscribersSearch::NOT_ON] = t('not on(date)');
          $between = '
          <div class="' . $css . '">' . t("and") . '</div><div class="' . $css . '"><input type="text" class="form-control klicktipp-datepicker ' . $css . '" name="FormValue_Fields[CustomFieldBetween' . $CustomFieldID . ']"
          value="' . $_SESSION['subscriber_search_filter']['FormValue_Fields']["CustomFieldBetween$CustomFieldID"] . '" id="FormValue_CustomFieldBetween' . $CustomFieldID . '" /></div>';
        }
        elseif ($EachField['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
          //datetime custom field: generate the widget for between/not between search operation

          $operations[SubscribersSearch::ON] = t('on(date)');
          $operations[SubscribersSearch::NOT_ON] = t('not on(date)');

          $hours_options_html = '';
          $minutes_options_html = '';
          for ($h = 0; $h < 24; $h++) {
            $formatted = sprintf("%02d", $h); //leading 0
            $selected = ($formatted == $_SESSION['subscriber_search_filter']['FormValue_Fields']["CustomFieldBetween$CustomFieldID"]["hours"]) ? 'selected="selected"' : '';
            $hours_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
          }
          for ($m = 0; $m < 60; $m++) {
            $formatted = sprintf("%02d", $m); //leading 0
            $selected = ($formatted == $_SESSION['subscriber_search_filter']['FormValue_Fields']["CustomFieldBetween$CustomFieldID"]["minutes"]) ? 'selected="selected"' : '';
            $minutes_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
          }

          $between = '<span class="search-and ' . $css . '">' . t("and") . '</span><div class="klicktipp-widget-datetime ' . $css . '">' .
            '<input type="text" size="12" class="form-control klicktipp-datepicker" name="FormValue_Fields[CustomFieldBetween' . $CustomFieldID . '][date]" ' .
            'value="' . $_SESSION['subscriber_search_filter']['FormValue_Fields']["CustomFieldBetween$CustomFieldID"]["date"] . '" id="FormValue_CustomFieldBetween' . $CustomFieldID . '" />' .
            '<span class="datetime">&nbsp;' . t('at(time)') . '&nbsp;</span>' .
            '<select class="form-control datetime-hours" name="FormValue_Fields[CustomFieldBetween' . $CustomFieldID . '][hours]">' . $hours_options_html . '</select><span class="datetime"> : </span>' .
            '<select class="form-control datetime-minutes" name="FormValue_Fields[CustomFieldBetween' . $CustomFieldID . '][minutes]">' . $minutes_options_html . '</select></div>';

        }
        else {
          $operations[SubscribersSearch::AT] = t('at(time)');
          $operations[SubscribersSearch::NOT_AT] = t('not at(time)');
          $hours_options_html = '';
          $minutes_options_html = '';
          for ($h = 0; $h < 24; $h++) {
            $formatted = sprintf("%02d", $h); //leading 0
            $selected = ($formatted == $_SESSION['subscriber_search_filter']['FormValue_Fields']["CustomFieldBetween$CustomFieldID"]["hours"]) ? 'selected="selected"' : '';
            $hours_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
          }
          for ($m = 0; $m < 60; $m++) {
            $formatted = sprintf("%02d", $m); //leading 0
            $selected = ($formatted == $_SESSION['subscriber_search_filter']['FormValue_Fields']["CustomFieldBetween$CustomFieldID"]["minutes"]) ? 'selected="selected"' : '';
            $minutes_options_html .= '<option value="' . $formatted . '" ' . $selected . '>' . $formatted . '</option>';
          }
          $between = '<div class="' . $css . '">' . t("and") . '</div><div class="cf-time ' . $css . '"><select class="form-control" name="FormValue_Fields[CustomFieldBetween' . $CustomFieldID . '][hours]">';
          $between .= $hours_options_html . '</select><span> : </span>';
          $between .= '<select class="form-control" name="FormValue_Fields[CustomFieldBetween' . $CustomFieldID . '][minutes]">' . $minutes_options_html . '</select></div>';

        }
        $operations[SubscribersSearch::BEFORE] = t('before(date)');
        $operations[SubscribersSearch::AFTER] = t('after(date)');
        $operations[SubscribersSearch::BETWEEN] = t('between(date)');
        $operations[SubscribersSearch::NOT_BETWEEN] = t('not between(date)');
      }
    }

    if (in_array($EachField['FieldTypeEnum'], array(
      CustomFields::TYPE_DATE,
      CustomFields::TYPE_TIME,
      CustomFields::TYPE_DATETIME,
    ))
    ) {

      if ($EachField['FieldTypeEnum'] == CustomFields::TYPE_TIME) {
        $html = '<div class="cf-time jstoggle-date-normal-' . $CustomFieldID . '">';
      }
      else {
        $html = '<div class="jstoggle-date-normal-' . $CustomFieldID . '">';
      }

      $html .= CustomFields::GenerateCustomFieldHTMLCode($EachField, $_SESSION['subscriber_search_filter']['FormValue_Fields']["CustomField$CustomFieldID"], '', FALSE, TRUE) . "</div>$between";

      $datetime_class = ($EachField['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) ? 'klicktipp-widget-datetime-search ' : '';
      //Javascript: HideEmptyCustomFields hides all CustomFields that are empty
      $form['CustomFields']["CustomFieldDateEnhanced_$CustomFieldID"] = array(
        '#type' => 'markup',
        '#weight' => $weight++,
        '#prefix' => '<div class="form-group enhanced-date ' . $datetime_class . 'jstoggle-cf-use-' . $CustomFieldID . '"
                      data-event-load="HideEmptyCustomFields" data-event-load-args="' . ((empty($_SESSION['subscriber_search_filter']['CustomFields'][$CustomFieldID])) ? 0 : 1) . '">',
        '#suffix' => '</div>',
      );

      //add custom field to selection box, the option value determines the class to toggle the display
      $form['CustomFields']['UseCustomField']['#options'][".jstoggle-cf-use-$CustomFieldID"] = $CustomFieldName;

      $form['CustomFields']["CustomFieldDateEnhanced_$CustomFieldID"][$CustomFieldID] = array(
        '#type' => 'select',
        '#title' => $CustomFieldName,
        '#default_value' => $_SESSION['subscriber_search_filter']['CustomFields'][$CustomFieldID],
        '#options' => $operations,
        '#weight' => $weight++,
        '#parents' => array('CustomFields', $CustomFieldID),
        '#attributes' => array(
          'data-event-load' => 'ShowCustomFieldValue',
          'data-event-load-args' => 'CustomFieldDateTimeOptions',
          'data-event-change' => 'ShowCustomFieldValue',
          'data-event-change-args' => 'CustomFieldDateTimeOptions',
          'data-toggle-key-append' => $CustomFieldID,
        ),
      );

      $form['CustomFields']["CustomFieldDateEnhanced_$CustomFieldID"]["CF_Value_$CustomFieldID"] = array(
        '#type' => 'markup',
        '#value' => $html,
        '#weight' => $weight++,
      );

    }
    else {

      $html = CustomFields::GenerateCustomFieldHTMLCode($EachField, $_SESSION['subscriber_search_filter']['FormValue_Fields']["CustomField$CustomFieldID"], '', FALSE, TRUE);

      //place all child-element in a row
      //Javascript: HideEmptyCustomFields hides all CustomFields that are empty
      $form['CustomFields']["CustomFieldInline_$CustomFieldID"] = array(
        '#type' => 'markup',
        '#weight' => $weight++,
        '#prefix' => '<div class="form-group element-row jstoggle-cf-use-' . $CustomFieldID . '"
                      data-event-load="HideEmptyCustomFields" data-event-load-args="' . ((empty($_SESSION['subscriber_search_filter']['CustomFields'][$CustomFieldID])) ? 0 : 1) . '">
                      <label>' . $CustomFieldName . '</label>',
        '#suffix' => '</div>',
      );

      //add custom field to selection box, the option value determines the class to toggle the display
      $form['CustomFields']['UseCustomField']['#options'][".jstoggle-cf-use-$CustomFieldID"] = $CustomFieldName;

      $form['CustomFields']["CustomFieldInline_$CustomFieldID"][$CustomFieldID] = array(
        '#type' => 'select',
        '#default_value' => $_SESSION['subscriber_search_filter']['CustomFields'][$CustomFieldID],
        '#options' => $operations,
        '#weight' => $weight++,
        '#parents' => array('CustomFields', $CustomFieldID),
        '#attributes' => array(
          'data-event-load' => 'ShowCustomFieldValue',
          'data-event-load-args' => 'CustomFieldOptions',
          'data-event-change' => 'ShowCustomFieldValue',
          'data-event-change-args' => 'CustomFieldOptions',
          'data-toggle-key-append' => $CustomFieldID,
        ),
      );

      $form['CustomFields']["CustomFieldInline_$CustomFieldID"]["CF_Value_$CustomFieldID"] = array(
        '#type' => 'markup',
        '#value' => '<div class="jstoggle-cf-' . $CustomFieldID . '">' . $html . '</div>',
        '#weight' => $weight++,
      );

    }

  }

  // --- tags

  $form['TaggedWithFrame'] = array(
    '#type' => 'fieldset',
    '#title' => t("Tagged with"),
    '#collapsible' => TRUE,
    '#collapsed' => FALSE,
    '#weight' => $weight++,
  );

  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID);
  $TagFilter = Tag::GetTagFilter($UserID);
  $TagFilter[t('Filter: All tags')] = $TagOptions;

  $form['TaggedWithFrame']['OpTaggedWith'] = array(
    '#type' => 'select',
    '#default_value' => $OpTaggedWith,
    '#options' => array(
      Campaigns::TAG_HAS_ANY => t("Contact has ANY of these tags"),
      Campaigns::TAG_HAS_ALL => t("Contact has ALL of these tags"),
    ),
    '#weight' => $weight++,
    '#title' => t("Tag link"),
    '#attributes' => array(
      'data-event-change' => 'ToggleTaggingQuickhelp',
      'data-event-change-args' => 'positive',
      'data-event-load' => 'ToggleTaggingQuickhelp',
      'data-event-load-args' => 'positive',
    ),
    '#prefix' => '<div class="tagging-dropdown-quickhelp">',
    '#suffix' => '<div class="quickhelps">' . theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'contact-tagged-with-any',
          '#title' => t("Quickhelp: Tag link"),
        )
      )) .
      theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'contact-tagged-with-all',
          '#title' => t("Quickhelp: Tag link"),
        )
      )) . '</div></div>',
  );

  $form['TaggedWithFrame']['TaggedWith'] = array(
    '#type' => 'select',
    '#default_value' => $TaggedWith,
    '#title' => t("Tags"),
    '#weight' => $weight++,
    '#options' => $TagOptions,
    '#multiple' => TRUE,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => '#options',
      'multiple' => TRUE,
      'free_entries' => FALSE,
      'filter' => $TagFilter,
      'container_class' => 'ms-tag-color-positive',
      'maxSelection' => 50,
      'maxSelection_message' => t('The maximum number of positive and negative tags is !max.', array('!max' => 50)),
      'groupID' => 'subscriber-search',
      //the maximumSelection is shared between all magicselects with the same groupIDs
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'contact-search-has-tag-tags',
  );

  $form['NotTaggedWithFrame'] = array(
    '#type' => 'fieldset',
    '#title' => t("Not tagged with"),
    '#collapsible' => TRUE,
    '#collapsed' => empty($NotTaggedWith),
    '#weight' => $weight++,
  );

  $form['NotTaggedWithFrame']['OpNotTaggedWith'] = array(
    '#type' => 'select',
    '#default_value' => $OpNotTaggedWith,
    '#options' => array(
      Campaigns::TAG_HAS_NOT_ALL => t("Contact doesn't have ALL of these tags"),
      //at least one tag is missing
      Campaigns::TAG_HAS_NOT_ANY => t("Contact doesn't have ANY of these tags"),
      //(!A AND !B AND !C)
    ),
    '#weight' => $weight++,
    '#title' => t("Tag link"),
    '#attributes' => array(
      'data-event-change' => 'ToggleTaggingQuickhelp',
      'data-event-change-args' => 'negative',
      'data-event-load' => 'ToggleTaggingQuickhelp',
      'data-event-load-args' => 'negative',
    ),
    '#prefix' => '<div class="tagging-dropdown-quickhelp">',
    '#suffix' => '<div class="quickhelps">' . theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'contact-not-tagged-with-all',
          '#title' => t("Quickhelp: Tag link"),
        )
      )) .
      theme('klicktipp_quickhelp', array(
        'element' => array(
          '#quickhelp' => 'contact-not-tagged-with-any',
          '#title' => t("Quickhelp: Tag link"),
        )
      )) . '</div></div>',
  );

  $form['NotTaggedWithFrame']['NotTaggedWith'] = array(
    '#type' => 'select',
    '#default_value' => $NotTaggedWith,
    '#title' => t("Tags"),
    '#weight' => $weight++,
    '#options' => $TagOptions,
    '#multiple' => TRUE,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => '#options',
      'multiple' => TRUE,
      'free_entries' => FALSE,
      'filter' => $TagFilter,
      'container_class' => 'ms-tag-color-negative',
      'maxSelection' => 50,
      'maxSelection_message' => t('The maximum number of positive and negative tags is !max.', array('!max' => 50)),
      'groupID' => 'subscriber-search',
      //the maximumSelection is shared between all magicselects with the same groupIDs
    ),
    '#theme' => 'klicktipp_magic_select',
    '#quickhelp' => 'contact-search-has-not-tag-tags',
  );

  //after clicking Search, we want to jump to the search result
  //because of the fixed top menu we have an offset, so place the anchor here
  $form['SearchResultAnchor'] = array(
    '#type' => 'markup',
    '#value' => '<a id="SearchResult" name="SearchResult"></a>',
    '#weight' => $weight++,
  );

  $form['search_buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  if ($UseAjaxSearch) {

    $form['search_buttons']['ajax_submit'] = array(
      '#theme' => 'klicktipp_search_button',
      '#title' => t(/*ignore*/CONTROLLER_SUBSCRIBER_SEARCH_BUTTON_TEXT),
      '#value' => '',
      '#attributes' => array(
        'data-event-click' => 'js_submit_search',
        'class' => array('submit-search'),
      ),
      '#weight' => $weight++,
    );

  }
  else {

    $form['search_buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_search_submit',
      '#value' => t(/*ignore*/CONTROLLER_SUBSCRIBER_SEARCH_BUTTON_TEXT),
      '#weight' => $weight++,
      '#attributes' => array(
        'class' => array('submit-search')
      ),
    );

  }

  $form['search_buttons']['reset'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_reload_submit',
    '#value' => t(/*ignore*/CONTROLLER_SUBSCRIBER_RESET_BUTTON_TEXT),
    '#weight' => $weight++,
  );

  $form['header_result'] = array(
    '#type' => 'markup',
    '#value' => "<h1 class='content-divider'>" . t("Search result") . "</h1>",
    '#weight' => $weight++,
  );

  if (!$UseAjaxSearch) {

    //Pager
    [$PagerPage, $PagerSize, $Offset] = klicktipp_validate_pager($PagerPage, $PagerSize);

    $form['_PagerParameters'] = [
      '#type' => 'value',
      '#value' => [
        "page" => $PagerPage,
        "size" => $PagerSize,
        "order" => check_plain($_GET['order']),
        "sort" => check_plain($_GET['sort'])
      ]
    ];

    $form = _klicktipp_subscriber_search_result($form, $form_state, $account, $PagerSize, $PagerPage, $weight, $UseAjaxSearch);

    if ($form_state['values']['hasSearchResult']) {
      //let pre_render know to add a script to scroll to the search result
      $form['scrollTo'] = array(
        '#type' => 'value',
        '#value' => TRUE
      );
    }

  }
  else {

    $form['_PagerParameters'] = [
      '#type' => 'value',
      '#value' => [
        "order" => check_plain($_GET['order']),
        "sort" => check_plain($_GET['sort'])
      ]
    ];

    $form['Result'] = [
      '#type' => 'markup',
      '#value' => "<p id='search-result-table'></p>",
      '#weight' => $weight++,
    ];

  }

  // the action buttons need to be rendered for the form to work
  // if the ajax search is used, hide the buttons when performing the search and
  // show them once we have an result
  $showActions = ($UseAjaxSearch || empty($form['ResultSubscribers']['#value'])) ? 'display:none;' : '';

  $form['header_action'] = array(
    '#type' => 'markup',
    '#value' => "<div id='search-result-actions' style='$showActions'><h1 class='content-divider'>" . t("Actions") . "</h1>",
    '#weight' => $weight++,
  );

  $form['button_info'] = array(
    '#type' => 'markup',
    '#value' => "<p id='ApplyTo' data-event-load='js_klicktipp_subscriber_search_selection'>" . t('To apply the following actions you need to select at least 1 contact.') . "</p>",
    '#weight' => $weight++,
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['ModalAssignTagTrigger'] = array(
    '#theme' => 'klicktipp_tag_modal_button',
    '#title' => t('Assign tags'),
    '#value' => 'klicktipp_subscriber_tag_subscribers_form',
    '#weight' => $weight++,
    '#modal_confirm' => drupal_get_form('klicktipp_subscriber_tag_subscribers_form', $account),
  );

  $form['buttons']['ModalRemoveTagTrigger'] = array(
    '#theme' => 'klicktipp_tag_modal_button',
    '#title' => t('Remove tags'),
    '#value' => 'klicktipp_subscriber_untag_subscribers_form',
    '#weight' => $weight++,
    '#modal_confirm' => drupal_get_form('klicktipp_subscriber_untag_subscribers_form', $account),
  );

  $form['buttons']['SaveSubscriberAudience'] = array(
    '#theme' => 'klicktipp_check_modal_button',
    '#title' => t('Save Subscriber Audience'),
    '#value' => 'klicktipp_subscriber_audience_create_form',
    '#weight' => $weight++,
    '#modal_confirm' => drupal_get_form('klicktipp_subscriber_audience_create_form', $account),
  );

  // check for global user that might be a support user
  if (user_access('access bounce check')) {

    $form['buttons']['ModalBounceCheckConfirmTrigger'] = array(
      '#theme' => 'klicktipp_check_modal_button',
      '#title' => t('Check Bounce Status'),
      '#value' => 'klicktipp_subscriber_bouncecheck_form',
      '#weight' => $weight++,
      '#modal_confirm' => drupal_get_form('klicktipp_subscriber_bouncecheck_form', $account),
    );

  }

  $form['buttons']['export_selection'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_export_submit',
    '#value' => t(/*ignore*/CONTROLLER_SUBSCRIBER_EXPORT_BUTTON_TEXT),
    '#weight' => $weight++,
  );

  $form['buttons']['ModalMergeContactsTrigger'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_merge_dialog_submit',
    '#value' =>  t('Merge contacts'),
    '#weight' => $weight++,
  );

  $form['buttons']['ModalUnsubscribeConfirmTrigger'] = array(
    '#theme' => 'klicktipp_unsubscribe_modal_button',
    '#title' => t('Unsubscribe'),
    '#value' => 'klicktipp_subscriber_unsubscribe_confirm_form',
    '#weight' => $weight++,
    '#modal_confirm' => drupal_get_form('klicktipp_subscriber_unsubscribe_confirm_form', $account),
  );

  if (klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_ADMINISTRATOR)) {

    $form['buttons']['ModalDeleteConfirmTrigger'] = array(
      '#theme' => 'klicktipp_delete_modal_button',
      '#title' => t('Delete Contacts'),
      '#value' => 'klicktipp_subscriber_delete_confirm_form',
      '#weight' => $weight++,
      '#modal_confirm' => drupal_get_form('klicktipp_subscriber_delete_confirm_form', $account),
    );

    $form['buttons']['ModalDeleteContactInfoConfirmTrigger'] = array(
      '#theme' => 'klicktipp_delete_modal_button',
      '#title' => t('Delete ContactInfos'),
      '#value' => 'klicktipp_subscriber_delete_contactinfo_confirm_form',
      '#weight' => $weight++,
      '#modal_confirm' => drupal_get_form('klicktipp_subscriber_delete_contactinfo_confirm_form', $account),
    );

  }

  $form['header_action_end'] = array(
    '#type' => 'markup',
    '#markup' => '</div>'
  );

  return $form;
}



//$UseAjaxSearch == TRUE
// $form = array()


//$UseAjaxSearch == FALSE
// $form = contains search form fields


function _klicktipp_subscriber_search_result($form, $form_state, $account, $PagerSize, $PagerPage, &$weight, $UseAjaxSearch) {

  $UserID = $account->uid;

  //only if the user has more than 1 reference, display a column with reference names
  $userReferences = Reference::getReferenceIDsOfUser($UserID);
  $displayReferences = count($userReferences) > 1;

  $MasterSelect = '<input type="checkbox" class="form-checkbox subscriber-master-select has-tooltip" value="1" name="MasterSelection" data-title="' . t('Select the entire search result') . '"/>';

  // tablesort is available, initial sort criteria is ID DESC
  $header = array(
    array(
      'data' => $MasterSelect,
      'class' => array('table-col-fit table-col-quickhelp'),
    ),
    //hide on phone
    array(
      'data' => t('ID'),
      'field' => 'main.RelSubscriberID',
      'sort' => 'desc',
      'class' => array('hidden-xs hidden-sm table-col-right'),
    ),
    array(
      'data' => t('ContactInfo'),
      'field' => 'main.ContactInfo',
    ),
  );

  if ($displayReferences) {

    $header[] = array(
      'data' => t('Context'),
      'field' => 'ref.DisplayName',
      //hide on phone
      'class' => array('hidden-xs hidden-sm'),
    );

  }

  $header = array_merge($header, array(
    //hide on tablet and phone
    array(
      'data' => t(/*ignore*/TABLESORT_FIRSTNAME),
      'field' => 'firstname.ValueText',
      'class' => array('hidden-xs hidden-sm hidden-md'),
    ),
    //hide on desktop, tablet and phone
    array(
      'data' => t(/*ignore*/TABLESORT_LASTNAME),
      'field' => 'lastname.ValueText',
      'class' => array('hidden-xs hidden-sm hidden-md'),
    ),
    //hide on desktop, tablet and phone
    array(
      'data' => t(/*ignore*/TABLESORT_OPTIN_DATE),
      'field' => 'sub.OptInDate',
      'class' => array('hidden-xs hidden-sm'),
    ),
    //hide on tablet and phone
    array(
      'data' => t(/*ignore*/TABLESORT_CONFIRMATION_DATE),
      'field' => 'sub.SubscriptionDate',
      'class' => array('hidden-xs'),
    ),
    //hide on tablet and phone
    array(
      'data' => t(/*ignore*/TABLESORT_OPTOUT_DATE),
      'field' => 'sub.UnsubscriptionDate',
      'class' => array('hidden-xs'),
    ),
  ));

  $ResultSubscribers = [];
  $SearchDialog = new SubscribersSearchDialog($UserID, $_SESSION['subscriber_search_filter']);
  $tablesort = explode(' ', trim(str_replace('ORDER BY', '', tablesort_sql($header))));

  $Offset = ($PagerPage-1) * $PagerSize;
  if ($UseAjaxSearch) {

    $ResultSubscribers = $SearchDialog->Search($Offset, $PagerSize, $tablesort[0], $tablesort[1]);

  }
  else {

    if (empty($_POST) || !empty($form_state['submitted']) || $form_state['input']['op'] != t(CONTROLLER_SUBSCRIBER_SEARCH_BUTTON_TEXT)) {
      //$ResultSubscribers = klicktipp_subscriber_search_subscribers($UserID, $_SESSION['subscriber_search_filter'], $header, $PagerPage, $PagerSize, FALSE);
      $ResultSubscribers = $SearchDialog->Search($Offset, $PagerSize, $tablesort[0], $tablesort[1]);
    }

    if (count($ResultSubscribers) == 0 && $PagerPage > 1) {
      //if the current pager page is empty, redirect to pager page 1

      $PagerPage = 1;
      drupal_goto("subscribers/$UserID/$PagerPage/$PagerSize");
      //this won't return

    }

  }

  $form['ResultSubscribers'] = array(
    '#type' => 'value',
    '#value' => $ResultSubscribers,
  );

  $Icons = array(
    'status' => array(
      Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED => 'icon-status-active',
      Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED => 'icon-status-unsubscribed',
      Subscribers::SUBSCRIPTIONSTATUS_OPTIN => 'icon-status-pending',
    ),
    'bounce' => array(
      Subscribers::BOUNCETYPE_NOTBOUNCED => 'icon-status-notbounced',
      Subscribers::BOUNCETYPE_SOFT => 'icon-status-softbounce',
      Subscribers::BOUNCETYPE_SPAM => 'icon-status-spambounce',
      Subscribers::BOUNCETYPE_HARD => 'icon-status-hardbounce',
    ),
  );

  $lastSubscriberID = 0;
  $highlight = 0;
  $alternate = array(
    0 => 1,
    1 => 0
  );
  $rows = array();
  foreach ($ResultSubscribers as $s) {

    $SubscriptionID = SubscribersSearch::EncodeSubscriptionKey($s['RelSubscriberID'], $s['SubscriptionType'], $s['ContactInfo'], $s['ReferenceID']);

    if ($tablesort[0] == 'main.RelSubscriberID') {
      //color-group subscriptions by subsriber if tablesort is subscriber id
      if ($s['RelSubscriberID'] != $lastSubscriberID) {
        $highlight = $alternate[$highlight];
        $lastSubscriberID = $s['RelSubscriberID'];
      }
    }

    $status = $s['SubscriptionStatus'];
    $tooltip_status = t(/*ignore*/Subscribers::$DisplaySubscriptionStatus[$s['SubscriptionStatus']]);
    $tooltip_bounce = t(/*ignore*/Subscribers::$TranslationBounceTypes[$s['BounceType']]);
    $tooltip_active = t('Active');
    $tooltip_paused = t('Inactive');

    $s['ContactInfo'] = (empty($s['ContactInfo']) || $s['ContactInfo'] == $s['RelSubscriberID']) ? t('No ContactInfo') : Subscribers::DepunycodeEmailAddress($s['ContactInfo']);

    $EmailAddress = '<i class="' . $Icons['status'][$status] . '" title="' . $tooltip_status . '"></i>';
    $EmailAddress .= '<i class="' . $Icons['bounce'][$s['BounceType']] . '" title="' . $tooltip_bounce . '"></i>';

    if (!empty($s['Optional'])) {
      //this is not the default subscription, mark it with a special icon
      $EmailAddress .= '<i class="icon-status-paused" title="' . $tooltip_paused . '"></i>';
    }
    else {
      //no icon, but reserve the space
      $EmailAddress .= '<i class="icon-status-play" title="' . $tooltip_active . '"></i>';
    }

    $url = "/subscriber/$UserID/edit/{$s['RelSubscriberID']}";
    $EmailAddress = array(
        'data' => $EmailAddress . '<a
          href="' . $url . '"><span class="cut-text display-email" title="' . $s['ContactInfo'] . '">' . $s['ContactInfo'] . '</span></a>',
      );

    $data = array(
      array(
        'data' => '<input type="checkbox" class="form-checkbox subscriber-select" value="' . $SubscriptionID . '" name="Selection[]">',
        'class' => array('table-col-fit'),
      ),
      array(
        'data' => $s['RelSubscriberID'],
        'class' => array('hidden-xs hidden-sm table-col-right'),
      ),
      $EmailAddress,
    );

    if ($displayReferences) {

      $data[] = [
        'data' => Reference::getTranslatedDisplayName($s),
        'class' => ['hidden-xs hidden-sm'],
      ];

    }

    $data = array_merge($data, array(
      array(
        'data' => $s['CustomFieldFirstName'],
        'class' => array('hidden-xs hidden-sm hidden-md'),
      ),
      array(
        'data' => $s['CustomFieldLastName'],
        'class' => array('hidden-xs hidden-sm hidden-md'),
      ),
      array(
        'data' => $s['OptInDate'] == 0 ? t('Transfer') : Dates::formatDate(Dates::FORMAT_DMY, (int) $s['OptInDate']),
        'class' => array('hidden-xs hidden-sm'),
      ),
      array(
        'data' => $s['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN || $s['SubscriptionDate'] == 0 ? t('Not confirmed') : Dates::formatDate(Dates::FORMAT_DMY, (int) $s['SubscriptionDate']),
        'class' => array('hidden-xs'),
      ),
      array(
        'data' => $s['UnsubscriptionDate'] == 0 ? '&mdash;' : Dates::formatDate(Dates::FORMAT_DMY, (int) $s['UnsubscriptionDate']),
        'class' => array('hidden-xs hidden-sm'),
      ),
    ));

    $rows[] = array(
      'style' => ($highlight) ? 'background-color:#f5f5f5;' : '',
      'data' => $data
    );

  }

  if (!empty($rows)) {

    $resultCountHtml = t("The search result contains !scount subscriptions of !ccount contacts of your !total total contacts.", [
      '!scount' => '<span id="SubscriptionCountSearch" class="subscriber-search-throbber-counts"></span>',
      '!ccount' => '<span id="SubscriberCountSearch" class="subscriber-search-throbber-counts"></span>',
      '!total' => '<span id="SubscriberCountTotal" class="subscriber-search-throbber-counts"></span>',
    ]);

    $form['ResultCount'] = [
      '#type' => 'markup',
      '#value' => "<span id='ResultCount'>$resultCountHtml</span>",
      '#weight' => $weight++,
    ];

    //Note: for the tablesort header links, $_GET['q'] is used, since this is called via an
    //      ajax request, $_GET['q'] contains the ajax callback path
    //      set it to subscriber search
    $_GET['q'] = "subscribers/$UserID";

    $form['ResultTable'] = [
      '#type' => 'markup',
      '#value' => theme('table', [
        'header' => $header,
        'rows' => $rows,
        'dataRow' => TRUE,
        'attributes' => [
          'class' => ['searchresulttable'],
          'data-e2e-id' => 'table-subscriber-search-result'
        ]
      ]),
      '#weight' => $weight++,
    ];

    $form['PagerBar'] = [
      '#type' => 'markup',
      '#value' => '<div id="PagerBar" class="subscriber-search-throbber-pager"></div>',
      '#weight' => $weight++,
    ];

  }
  else {

    $form['EmptyResult'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("No contact matches your search criterias."),
      '#weight' => $weight++,
    );

  }

  if ($UseAjaxSearch) {

    //<!--kt-valid-result--> is just a marker for the ajax callback that we have a valid result and not display
    //an unknown error as the search result

    //add the event to the select checkboxes in the table

    $form['validResult'] = array(
      '#type' => 'markup',
      '#markup' => "<!--kt-valid-result-->
        <script type='text/javascript'>

          $('input.form-checkbox.subscriber-select').each(function() {
            window['js_klicktipp_subscriber_search_selection']($(this));
          });


        </script>
      "
    );

  }


  return $form;

}

function _klicktipp_subscriber_search_ajax_callback ($account, $mode = 'pager') {

  //Note: check the logged in user so support users can test with big customer accounts
  $UseAjaxSearch = user_access('access ajax subscriber search');

  $input = json_decode(file_get_contents("php://input"), true);
  $pagerParams = (empty($input['PagerParameters'])) ? array() : $input['PagerParameters'];

  [$pagerParams['page'], $pagerParams['size'], $Offset] = klicktipp_validate_pager($pagerParams['page'], $pagerParams['size']);

  if ($mode == 'result') {

    if (!$UseAjaxSearch) {
      drupal_exit();
    }

    $updateSessionValueScript = '';

    if (!empty($input['searchMask'])) {
      //the search mask has been updated
      //save the mask in the session to be used in _klicktipp_subscriber_search_result()

      $searchMask = array();
      foreach($input['searchMask'] as $field) {

        $multikey = explode('[', $field['name']);

        $value = &$searchMask;
        while(count($multikey)) {

          $key = trim(array_shift($multikey), '[]');

          if (count($multikey)) {
            $value[$key] = (isset($value[$key])) ? $value[$key] : array();
            $value = &$value[$key];
          }
          else {
            $value[$key] = $field['value'];
          }

        }

      }

      //generate search session
      $searchSession = time();
      $searchMask['Session'] = $searchSession;
      //add a javascript to the search result to update the session value in the form
      $updateSessionValueScript = '<script type="text/javascript">$("#search-session").val("' . $searchSession . '")';

      $_SESSION['subscriber_search_filter'] = $searchMask;

    }


    $weight = 0;
    $form = _klicktipp_subscriber_search_result([], [], $account, $pagerParams['size'], $pagerParams['page'], $weight, TRUE);
    echo drupal_render($form) . $updateSessionValueScript;

    drupal_exit();
  }

  if (!isset($pagerParams['subscriberCount'])) {

    $UserID = $account->uid;

    $SearchDialog = new SubscribersSearchDialog($UserID, $_SESSION['subscriber_search_filter']);

    $pagerParams['subscriberCount'] = $SearchDialog->CountResultSubscribers();
    $pagerParams['subscriptionCount'] = $SearchDialog->CountResultSubscriptions();
    $pagerParams['totalCount'] = $SearchDialog->CountTotalSubscribers();

  }

  $pagerParams['pager'] = _klicktipp_subscriber_create_pager_html($UserID, $pagerParams, $UseAjaxSearch);

  echo json_encode($pagerParams);

  drupal_exit();

}

function _klicktipp_subscriber_create_pager_html($UserID, $pagerParams, $UseAjaxSearch) {

  $ResultCount = $pagerParams["subscriptionCount"];
  $PagerPage = $pagerParams["page"];
  $PagerSize = $pagerParams["size"];
  $order = $pagerParams["order"];
  $sort = $pagerParams["sort"];

  $pager_link_builder = '';

  if ($UseAjaxSearch) {

    //when ajax search is active, do not reload the page when using the pager
    //build javascript links

    $pager_link_builder = function($source, $text, $PagerLink, $PagerPage, $PagerSize, $pager_link_attr) {
      $attr = (empty($pager_link_attr['attributes'])) ? array() : $pager_link_attr['attributes'];
      $href = "JavaScript:window['js_select_page']($PagerSize,$PagerPage);";
      return '<a href="' . $href . '" ' . drupal_attributes($attr) . '>' . $text . '</a>';
    };

  }

  return theme('klicktipp_table_pager', [
    'element' => [
      //current page
      'pager_page' => $PagerPage,
      //current page size
      'pager_size' => $PagerSize,
      //hide page size badges
      'pager_hide_sizes' => FALSE,
      //total entries in the table
      'pager_total' => $ResultCount,
      //max pager items
      'pager_max_items' => KLICKTIPP_PAGER_MAX_PAGINATION_ITEM,
      //link of pager badges and items, "/<CURRENT-PAGE>/<PAGE-SIZE>" will be added
      'pager_link' => "subscribers/$UserID",
      //build an additional link query string, possibly for table sort @see l()/url()
      'pager_link_query' => (empty($order)) ? [] : [
        'order' => $order,
        'sort' => $sort,
      ],
      //HTML anchor (possibly the table) to jump to after clicking a pager badge or item
      'pager_anchor' => 'SearchResult',
      'pager_link_builder' => $pager_link_builder
    ]
  ]);

}

/**
 * subscriber search prerender
 */
function klicktipp_subscriber_search_form_pre_render($form) {

  $account = $form['account']['#value'];
  //add some constants needed for JavaScript
  $constants = array(
    'klicktipp_subscriber_search_form' => array(
      'CONTROLLER_SUBSCRIBER_CUSTOMFIELDS_BETWEEN' => SubscribersSearch::BETWEEN,
      'CONTROLLER_SUBSCRIBER_CUSTOMFIELDS_NOT_BETWEEN' => SubscribersSearch::NOT_BETWEEN,
      'CONTROLLER_SUBSCRIBER_DATE_BETWEEN' => SubscribersSearch::DATE_BETWEEN,
      'CONTROLLER_SUBSCRIBER_DATE_NOT_BETWEEN' => SubscribersSearch::DATE_NOT_BETWEEN,
    ),
    /*
 * tell the javascript which elements (by class => array key) to hide,
 * when a certian option (array value) is selected through the select field (on, not on, between, ...)
 * @see js_widget_date_enhanced in script.js
 */
    'DateEnhancedConfirmation' => array(
      '.confirmation-date' => array(0), //"Please select", hide first date field
      '.confirmation-date-until' => array(//hide second date field when between or not-between is not selected
        0,
        SubscribersSearch::DATE_ON,
        SubscribersSearch::DATE_NOT_ON,
        SubscribersSearch::DATE_BEFORE,
        SubscribersSearch::DATE_AFTER,
      ),
    ),
    'DateEnhancedSubscription' => array(
      '.subscription-date' => array(0), //"Please select", hide first date field
      '.subscription-date-until' => array(//hide second date field when between or not-between is not selected
        0,
        SubscribersSearch::DATE_ON,
        SubscribersSearch::DATE_NOT_ON,
        SubscribersSearch::DATE_BEFORE,
        SubscribersSearch::DATE_AFTER,
      ),
    ),
    'DateEnhancedSMSSubscription' => array(
      '.sms-subscription-date' => array(0),
      //"Please select", hide first date field
      '.sms-subscription-date-until' => array(//hide second date field when between or not-between is not selected
        0,
        SubscribersSearch::DATE_ON,
        SubscribersSearch::DATE_NOT_ON,
        SubscribersSearch::DATE_BEFORE,
        SubscribersSearch::DATE_AFTER,
      ),
    ),
    'CustomFieldOptions' => array(
      //hide ..jstoggle-cf-$CustomFieldID if "0" (Please select),
      //SubscribersSearch::IS_EMPTY or SubscribersSearch::IS_NOT_EMPTY is selected
      ".jstoggle-cf-" => array(
        0,
        SubscribersSearch::IS_EMPTY,
        SubscribersSearch::IS_NOT_EMPTY,
      ),
    ),
    'CustomFieldDateTimeOptions' => array(
      //hide .jstoggle-date-normal-$CustomFieldID if "0" (Please select),
      //SubscribersSearch::IS_EMPTY or SubscribersSearch::IS_NOT_EMPTY is selected
      ".jstoggle-date-normal-" => array(
        0,
        SubscribersSearch::IS_EMPTY,
        SubscribersSearch::IS_NOT_EMPTY,
      ),
      ".jstoggle-date-until-" => array(
        0,
        SubscribersSearch::IS_EMPTY,
        SubscribersSearch::IS_NOT_EMPTY,
        SubscribersSearch::ON,
        SubscribersSearch::NOT_ON,
        SubscribersSearch::AT,
        SubscribersSearch::NOT_AT,
        SubscribersSearch::BEFORE,
        SubscribersSearch::AFTER,
      ),
    ),
  );


  drupal_add_js($constants, array('type' => 'setting', 'scope' => 'header', 'group' => JS_DEFAULT));

  //encode to proper encoding for javascript
  $action = json_encode(t('The following actions will be applied to !sub subscriptions of !count contacts:'));
  $please = json_encode(t('To apply the following actions you need to select at least 1 subscription.'));

  $script = "

    window['js_klicktipp_subscriber_search_selection'] = function ( element, args) {

      //delegate this event to the parent table (element) to make sure, it will be executed after other click events
      //input.klicktipptablemasterselect has to select/unselect all checkboxes first
      $('.subscriber-select').click(function () {
        $('.subscriber-master-select').prop('checked', false);
        window['CountSelection']();
      });

      $('.subscriber-master-select').click(function () {

        window['js_checkbox_multiselect']($(this), '.subscriber-select');

        window['CountSelection']();

      });

      window['CountSelection']();

    };

    window['CountSelection'] = function () {

      if ( !$('#ApplyTo').length || !$('#SubscriptionCountSearch').length || !$('#SubscriberCountSearch').length)
        return;

      var hint = $action;

      var selected = $('.subscriber-select:checked').length;

      if ( $('.subscriber-master-select:checked').length == 1 ) {
        var subcount = parseInt($('#SubscriptionCountSearch').text());
        var count = parseInt($('#SubscriberCountSearch').text());
        hint = hint.replace('!sub', subcount).replace('!count', count);
      }
      else if ( selected > 0 ) {
        var count = 0;
        var values = [];
        $('.subscriber-select:checked').each(function() {
          var sid = parseInt($(this).val().split(':')[0]);
          if (values.indexOf(sid) === -1) {
            values.push(sid);
          }
        });
        hint = hint.replace('!sub', selected).replace('!count', values.length);
      }
      else {
        hint = $please;
      }

      $('#ApplyTo').html(hint);

    };

    window['HideEmptyCustomFields'] = function (element, args) {
     if ( parseInt(args) ) {
        element.show();
      }
      else {
        element.hide();
      }

      window['update_view']();
    }

    window['ShowCustomFieldValue'] = function (element, args) {

      var isDateEnhanced = element.parents('.enhanced-date').length;
      var value = element.val();

      if (value == -1 ) {
        //dismiss is selected, hide whole custom field
        element.parents('[class*=jstoggle-cf-use-]').hide();

        //set the value back to 'Please select'
        element.val(0).trigger('change');
      }
      else {

        if (isDateEnhanced)
          window['js_widget_date_enhanced'](element, args);
        else
          window['js_select_toggle_element_display'](element, args);

     }

  }

  window['ToggleTaggingQuickhelp'] = function (element, args) {

    if ( args == 'positive' ) {

     if ( element.val() == " . Campaigns::TAG_HAS_ANY . ") {
        $('#quickhelp-contact-tagged-with-any').show();
        $('#quickhelp-contact-tagged-with-all').hide();
     }
     else {
        $('#quickhelp-contact-tagged-with-any').hide();
        $('#quickhelp-contact-tagged-with-all').show();
     }

    }
    else {

     if ( element.val() == " . Campaigns::TAG_HAS_NOT_ALL . ") {
       $('#quickhelp-contact-not-tagged-with-all').show();
       $('#quickhelp-contact-not-tagged-with-any').hide();
     }
     else {
       $('#quickhelp-contact-not-tagged-with-all').hide();
       $('#quickhelp-contact-not-tagged-with-any').show();
     }

    }

  }

  window['js-toggle-contactinfo-search-mask'] = function(element, args) {

    if (element.val() === '" . SubscribersSearch::IS_EMPTY . "') {
      $(args).hide();
    }
    else {
      $(args).show();

      if (element.val() === '" . SubscribersSearch::IS_NOT_EMPTY . "') {
        $(args + ' .contactinfo-field').hide();
      }
      else {
        $(args + ' .contactinfo-field').show();
      }

    }

    window['update_view']();

  }

  ";

  //allow user to change the search mode (email or phone number)

  $script .= "

  window['js_init_search_mode'] = function ( element, args ) {
    var email_fieldset = $('.search-mode-email a.accordion-toggle');
    var phonenumber_fieldset = $('.search-mode-phonenumber a.accordion-toggle');

    var mode = element.val();
    if (mode == " . SubscribersSearch::SEARCH_BY_NONE . ") {
      email_fieldset.addClass('collapsed');
      phonenumber_fieldset.addClass('collapsed');
    }
    else if (mode == " . SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS . ") {
      phonenumber_fieldset.addClass('collapsed');
    }
    else if (mode == " . SubscribersSearch::SEARCH_BY_PHONE_NUMBER . ") {
      email_fieldset.addClass('collapsed');
    }

  }

  window['js_toggle_search_mode'] = function ( element, args ) {

    var email_fieldset = $('.search-mode-email a.accordion-toggle');
    var phonenumber_fieldset = $('.search-mode-phonenumber a.accordion-toggle');
    var clear_inputs = false;

    if ( email_fieldset.length != 1 || phonenumber_fieldset.length != 1 ) {
      return;
    }

    var mode = '';
    if ( args == '.search-mode-email' ) {

      mode = '" . SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS . "';

      if (!phonenumber_fieldset.hasClass('collapsed')) {
        phonenumber_fieldset.trigger('click');
        clear_inputs = true;
      }

    }
    else if ( args == '.search-mode-phonenumber') {

      mode = '" . SubscribersSearch::SEARCH_BY_PHONE_NUMBER . "';

      if (!email_fieldset.hasClass('collapsed')) {
        email_fieldset.trigger('click');
        clear_inputs = true;
      }

    }
    else if (email_fieldset.hasClass('collapsed') && phonenumber_fieldset.hasClass('collapsed')) {
      mode = '" . SubscribersSearch::SEARCH_BY_NONE . "';
    }
    else {
      return;
    }

    if ($('.edit-SearchMode').length > 0 ) {
      $('.edit-SearchMode').val(mode);
    }


    if ( clear_inputs ) {
      $(args).find('input').val('');
      $(args).find('select').val(0).trigger('change');
      $(args).find('select.search-op').val(" . SubscribersSearch::IS_NOT_EMPTY . ").trigger('change');
      $(args).find('.klicktipp-magicselect').each(function () {
        var msid = $(this).attr('data-magicselect');
        if ( MagicSelects[msid] )
          MagicSelects[msid].clear();
      });
    }

  }";

  //Note: for ajax search, this contains the result table order, since we need to reload the page
  //      for none ajax search, this also contains the current pager page and size (always reload)
  $pagerParams = json_encode($form['_PagerParameters']['#value']);
  $sort = (empty($form['_PagerParameters']['#value']['sort'])) ? '' : $form['_PagerParameters']['#value']['sort'];
  $order = (empty($form['_PagerParameters']['#value']['order'])) ? '' : urlencode($form['_PagerParameters']['#value']['order']);

  $searchCallback = APP_URL . "subscribers/{$account->uid}/ajax";
  $tablesort = (empty($order) || empty($sort)) ? '' : "sort=$sort&order=$order";

  //encode to proper encoding for javascript
  $errMsg = json_encode('<p>' . t('An error occurred retrieving the search result. Please reload the page.') . '</p>');
  $searchMsg = json_encode('<p>' . t('Searching...') . '<div class="subscriber-search-throbber-counts"></div></p>');

  //Note: check the logged in user so support users can test with big customer accounts
  $UseAjaxSearch = user_access('access ajax subscriber search');

  $initSearch = "window['js_get_pager']();";

  if ($UseAjaxSearch) {

    $initSearch = "window['js_perform_search']();\n";

    $script .= "\n\n

    window['doc-ready'] = false;
    $(document).ready(function() {

      if (!window['doc-ready']) {
        window['doc-ready'] = true;

        $('#klicktipp-subscriber-search-form input[type=text]').keypress(ev => {

          if (ev && ev.charCode === 13) {

            $('#edit-ajax-submit').trigger('click');
            ev.preventDefault();
            return false;
          }

        });
      }

    });

    window['js_submit_search'] = function(element, args) {

      element.attr('href', 'JavaScript: void(0)');

      $('#search-result-actions').hide();
      $('#search-result-table').html($searchMsg);
      window['js_update_pager'](true);

      var searchMask = $('#klicktipp-subscriber-search-form').serializeArray();
      window['js_perform_search'](searchMask);

      window['js_scroll_to']('#SearchResult', 1000);

      return false;

    }

    window['js_select_page'] = function(size, page) {

      window['js_search_pager']['size'] = size;
      window['js_search_pager']['page'] = page;
      window['js_search_pager']['pager'] = null;

      window['js_update_pager']();
      window['js_perform_search']();

    }

    window['js_perform_search'] = function(searchMask) {

      $.ajax({
        type: 'POST',
        url: '$searchCallback/result?$tablesort',
        data: JSON.stringify({
          searchMask: searchMask || {},
          PagerParameters: window['js_search_pager']
        }),
        contentType : 'application/json',
        success: function(response) {

          if (response.indexOf('<!--kt-valid-result-->') === -1) {
            $('#search-result-table').html($errMsg);
          }
          else {
            $('#search-result-table').html(response);
            $('#search-result-actions').show();
            window['js_get_pager']();
          }
        }
      });

    }

    ";

  }


  //js_update_pager(reset=true) we remove pager, the subscriptionCount, subscriberCount and the totalCount which triggers a db query
  //Note: the pager might be updated but the result count might not

  $script .= "\n\n

    window['js_search_pager'] = $pagerParams;

    window['js_update_pager'] = function(reset) {

      if (reset) {

        window['js_search_pager'] = {
          page: 1,
          size:  window['js_search_pager']['size'],
          order: window['js_search_pager']['order'],
          sort: window['js_search_pager']['sort'],
        };

      }

      if (!window['js_search_pager'].hasOwnProperty('subscriptionCount')) {
        $('#SubscriptionCountSearch').text('').addClass('subscriber-search-throbber-counts');
      }
      else {
        $('#SubscriptionCountSearch').text(window['js_search_pager']['subscriptionCount']).removeClass('subscriber-search-throbber-counts');
      }

      if (!window['js_search_pager'].hasOwnProperty('subscriberCount')) {
        $('#SubscriberCountSearch').text('').addClass('subscriber-search-throbber-counts');
      }
      else {
        $('#SubscriberCountSearch').text(window['js_search_pager']['subscriberCount']).removeClass('subscriber-search-throbber-counts');
      }

      if (!window['js_search_pager'].hasOwnProperty('totalCount')) {
        $('#SubscriberCountTotal').text('').addClass('subscriber-search-throbber-counts');
      }
      else {
        $('#SubscriberCountTotal').text(window['js_search_pager']['totalCount']).removeClass('subscriber-search-throbber-counts');
      }

      if (!window['js_search_pager'].hasOwnProperty('pager')) {
        $('#PagerBar').html('').addClass('subscriber-search-throbber-pager');
      }
      else {
        $('#PagerBar').html(window['js_search_pager']['pager']).removeClass('subscriber-search-throbber-pager');
      }

    }

    window['js_get_pager'] = function() {

      $.ajax({
        type: 'POST',
        url: '$searchCallback/pager',
        data: JSON.stringify({PagerParameters: window['js_search_pager']}),
        contentType : 'application/json',
        success: function(response){
            var data = JSON.parse(response);
            window['js_search_pager'] = data;
            window['js_update_pager']();
        }
      });

    }

  ";

  //TODO: init is called twice, why?

  $scrollTo = (empty($form['scrollTo']['#value'])) ? '' : "window['js_scroll_to']('#SearchResult', 1000);";

  $script .= "\n\n

    window['js_initial_search'] = true;

    $(document).ready(function() {

      if (window['js_initial_search']) {

         $scrollTo

         window['js_initial_search'] = false;
         window['js_update_pager']();
         $initSearch
      }

    });

  ";

  drupal_add_js($script, array('type' => 'inline', 'scope' => 'header', 'group' => JS_DEFAULT));

  return $form;

}

/**
 * subscriber search form submit
 */
function klicktipp_subscriber_search_form_submit($form, &$form_state) {

  // keep values
  $_SESSION['subscriber_search_filter'] = $form_state['values'];
  $_SESSION['subscriber_search_filter']['FormValue_Fields'] = $form_state['input']['FormValue_Fields'];
  //generate search session
  $_SESSION['subscriber_search_filter']['Session'] = time();

  $account = $form_state['values']['account'];
  $UserID = $account->uid;

  if ($form_state['values']['op'] == t(/*ignore*/CONTROLLER_SUBSCRIBER_RESET_BUTTON_TEXT)) {

    unset($_SESSION['subscriber_search_filter']);

  }
  elseif ($form_state['values']['op'] == t(/*ignore*/CONTROLLER_SUBSCRIBER_EXPORT_BUTTON_TEXT)) {

    $Subscribers = (empty($_POST['Selection'])) ? array() : $_POST['Selection'];

    // subscriber selection -> prepare confirmation
    $Selection = array();
    if (empty($_POST['MasterSelection']) && empty($Subscribers)) {
      //the user did not select anything
      drupal_set_message(t('To apply this action you need to select at least 1 contact.'), 'warning');

      $_SESSION['subscriber_search_selection'] = array();

      return; //stay in the search dialog

    }
    elseif (empty($_POST['MasterSelection']) && !empty($Subscribers)) {
      // user selected single contacts from the table

      foreach ($Subscribers as $selection) {

        $Subscription = SubscribersSearch::DecodeSubscriptionKey($selection);

        $Selection[] = array(
          'SubscriberID' => $Subscription[0],
          'SubscriptionType' => $Subscription[1],
          'ContactInfo' => $Subscription[2],
          'ReferenceID' => $Subscription[3]
        );

      }

    }
    else {
      //user checked the master select to export the entire search result
      $Selection = CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED;
    }


    //store the current selection in the session for the export dialog
    $_SESSION['subscriber_search_selection'] = $Selection;

    klicktipp_set_redirect($form_state, "subscribers/$UserID/export/");

  }
  elseif ($form_state['values']['op'] == t('Merge contacts')) {

    $Subscribers = (empty($_POST['Selection'])) ? array() : $_POST['Selection'];

    $Selection = array();
    if (empty($_POST['MasterSelection']) && empty($Subscribers) || count($Subscribers) !== 2) {
      drupal_set_message(t('Only exactly 2 contacts can be merged. Please select exactly two contacts and try again.'), 'error');

      $_SESSION['subscriber_merge_selection'] = array();

      return;
    }

    foreach ($Subscribers as $selection) {
      $Subscription = SubscribersSearch::DecodeSubscriptionKey($selection);

      $Selection[] = array(
        'SubscriberID' => $Subscription[0],
        'SubscriptionType' => $Subscription[1],
        'ContactInfo' => $Subscription[2],
        'ReferenceID' => $Subscription[3]
      );
    }

    $subscriberId = $Selection[0]['SubscriberID'];
    $mergedSubscriberId = $Selection[1]['SubscriberID'];

    if ($subscriberId === $mergedSubscriberId) {
      drupal_set_message(t('Selected subscriptions already reference the same contact.'), 'error');

      return;
    }

    //store the current selection in the session for the export dialog
    $_SESSION['subscriber_merge_selection'] = $Selection;

    klicktipp_set_redirect($form_state, "subscribers/$UserID/duplicate/$subscriberId/$mergedSubscriberId/merge");

  }
  elseif (!empty($form_state['values']['SubscriberAudiences'])) {
    // user selected an audience
    $SubscriberAudience = VarSubscriberAudience::RetrieveAudience($UserID, $form_state['values']['SubscriberAudiences']);
    if (!empty($SubscriberAudience)) {
      // overwrite search filter session with audience settings
      $_SESSION['subscriber_search_filter'] = $SubscriberAudience['Data'];
      $_SESSION['subscriber_search_filter']['SubscriberAudiences'] = $form_state['values']['SubscriberAudiences'];
    }
  }
  else {
    //when Search was clicked, jump to the result (#SearchResult anchor)
    $form_state['values']['hasSearchResult'] = TRUE;
    $form_state['rebuild'] = TRUE;
    // prevent double loading of page
    $form_state['redirect'] = FALSE;
  }
}

function klicktipp_subscriber_get_subscriptions($UserID, $search) {

  $GetSubscriptions = new SubscribersSearch($UserID, $search, array(
    'main.RelSubscriberID AS SubscriberID',
    'main.SubscriptionType',
    'main.ReferenceID',
    'main.ContactInfo'
  ));

  $ArraySubscriptions = $GetSubscriptions->Search();

  return $ArraySubscriptions;
}


/**
 * search query builder: subscription
 */
//TODO: remove after re-do of simpletests
function klicktipp_get_subscribers_search_email_clauses($EmailSearch, &$FinalQuery, &$QueryData) {

  $EmailAddress = Subscribers::PunycodeEmailAddress($EmailSearch['EmailAddress']);
  $EmailAddressOp = $EmailSearch['EmailAddressOp'];
  $SubscriptionStatus = $EmailSearch['Status'];
  $BounceStatus = $EmailSearch['BounceStatus'];
  $OpSubscriptionDate = $EmailSearch['Subscription']['OpSubscriptionDate'];
  $OpConfirmationDate = $EmailSearch['Subscription']['OpConfirmationDate'];

  // --- search by email address

  $SearchByEmailAddress = ($EmailAddress !== '' || $EmailAddressOp == SubscribersSearch::IS_EMPTY ||
    $EmailAddressOp == SubscribersSearch::IS_NOT_EMPTY);

  if ($SearchByEmailAddress) {

    switch ($EmailAddressOp) {
      case SubscribersSearch::IS:
        $FinalQuery[] = "(cinfo.ContactInfo = '%s' AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = $EmailAddress;
        break;
      case SubscribersSearch::IS_NOT:
        $FinalQuery[] = "(cinfo.ContactInfo != '%s' AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = $EmailAddress;
        break;
      case SubscribersSearch::CONTAINS:
        $FinalQuery[] = "(cinfo.ContactInfo LIKE '%s' AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = "%$EmailAddress%";
        break;
      case SubscribersSearch::CONTAINS_NOT:
        $FinalQuery[] = "(cinfo.ContactInfo NOT LIKE '%s' AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = "%$EmailAddress%";
        break;
      case SubscribersSearch::STARTS_WITH:
        $FinalQuery[] = "(cinfo.ContactInfo LIKE '%s' AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = "$EmailAddress%";
        break;
      case SubscribersSearch::STARTS_NOT_WITH:
        $FinalQuery[] = "(cinfo.ContactInfo NOT LIKE '%s' AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = "$EmailAddress%";
        break;
      case SubscribersSearch::ENDS_WITH:
        $FinalQuery[] = "(cinfo.ContactInfo LIKE '%s' AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = "%$EmailAddress";
        break;
      case SubscribersSearch::ENDS_NOT_WITH:
        $FinalQuery[] = "(cinfo.ContactInfo NOT LIKE '%s' AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = "%$EmailAddress";
        break;
      case SubscribersSearch::IS_EMPTY:
        $FinalQuery[] = "(cinfo.ContactInfo IS NULL)";
        break;
      case SubscribersSearch::IS_NOT_EMPTY:
        $FinalQuery[] = "(cinfo.ContactInfo IS NOT NULL)";
        break;
    }

  }

  // --- email address subscription status

  // ----- START STATUS QUERY -----
  if (!empty($SubscriptionStatus)) {

    $status_in_query = array();
    $status_in_values = array();
    foreach ($SubscriptionStatus as $selected) {
      switch ($selected) {
        case SubscribersSearch::STATUS_SUBSCRIBED:
          $status_in_values[] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
          $status_in_query[] = '%d';
          break;
        case SubscribersSearch::STATUS_UNSUBSCRIBED:
          $status_in_values[] = Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED;
          $status_in_query[] = '%d';
          break;
        case SubscribersSearch::STATUS_PENDING:
          $status_in_values[] = Subscribers::SUBSCRIPTIONSTATUS_OPTIN;
          $status_in_query[] = '%d';
          break;
        case SubscribersSearch::STATUS_PENDING_7DAYS:
          if (!in_array(SubscribersSearch::STATUS_PENDING, $SubscriptionStatus)) {
            //only if normal pending status  is not selected

            //normal pending status
            $status_in_values[] = Subscribers::SUBSCRIPTIONSTATUS_OPTIN;
            $status_in_query[] = '%d';

            //longer than 7 days
            $one_week_back = time() - (7 * 24 * 60 * 60);
            $FinalQuery[] = "OptInDate < $one_week_back";
          }
          break;
        case SubscribersSearch::STATUS_IMPORT:
          $FinalQuery[] = "cinfo.RelListID = 0";
          break;
      }
    }

    if (!empty($status_in_query)) {
      $status_query = '';
      if (count($status_in_query) > 1) {
        $status_query = "(cinfo.SubscriptionStatus IN (" . implode(', ', $status_in_query) . ") " .
          "AND cinfo.ContactInfo IS NOT NULL)";
        foreach ($status_in_values as $v) {
          $QueryData[] = $v;
        }
      }
      else {
        $status_query = "(cinfo.SubscriptionStatus = %d AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = $status_in_values[0];
      }

      $FinalQuery[] = $status_query;
    }

  }

  // ----- END STATUS QUERY -----

  // ----- START BOUNCE STATUS QUERY -----
  if (!empty($BounceStatus)) {
    $bounce_in_query = array();
    $bounce_in_values = array();
    foreach ($BounceStatus as $selected) {
      $bounce_in_values[] = $selected;
      $bounce_in_query[] = '%d';
    }

    if (!empty($bounce_in_query)) {
      $bounce_query = "";
      if (count($bounce_in_query) > 1) {
        $bounce_query = "(cinfo.BounceType IN (" . implode(', ', $bounce_in_query) . ") AND cinfo.ContactInfo IS NOT NULL)";
        foreach ($bounce_in_values as $v) {
          $QueryData[] = $v;
        }
      }
      else {
        $bounce_query = "(cinfo.BounceType = %d AND cinfo.ContactInfo IS NOT NULL)";
        $QueryData[] = $bounce_in_values[0];
      }

      $FinalQuery[] = $bounce_query;
    }
  }

  // --- email address optin/subscription date

  if (!empty($OpSubscriptionDate) || !empty($OpConfirmationDate)) {

    $fields = array(
      'OpSubscriptionDate' => 'SubscriptionDate',
      'OpConfirmationDate' => 'ConfirmationDate',
    );

    foreach ($fields as $option => $data) {

      if (!empty($EmailSearch['Subscription'][$option])) {
        $col = ($data == 'SubscriptionDate') ? "OptInDate " : "SubscriptionDate "; // $data == 'SubscriptionDate' || 'ConfirmationDate'

        //convert the date string provided by the datepicker to a timestamp
        $midnight = Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $EmailSearch['Subscription'][$data]);

        switch ($EmailSearch['Subscription'][$option]) {
          case SubscribersSearch::DATE_ON:
            $FinalQuery[] = "((cinfo.$col BETWEEN %d AND %d) AND cinfo.ContactInfo IS NOT NULL)";
            $QueryData[] = $midnight;
            $QueryData[] = $midnight + (24 * 60 * 60) - 1;
            break;
          case SubscribersSearch::DATE_NOT_ON:
            $FinalQuery[] = "((cinfo.$col NOT BETWEEN %d AND %d) AND cinfo.ContactInfo IS NOT NULL)";
            $QueryData[] = $midnight;
            $QueryData[] = $midnight + (24 * 60 * 60) - 1;
            break;
          case SubscribersSearch::DATE_BEFORE:
            $FinalQuery[] = "(cinfo.$col < %d AND cinfo.ContactInfo IS NOT NULL)";
            $QueryData[] = $midnight;
            break;
          case SubscribersSearch::DATE_AFTER:
            $FinalQuery[] = "(cinfo.$col > %d AND cinfo.ContactInfo IS NOT NULL)";
            $QueryData[] = $midnight + (24 * 60 * 60) + 1;
            break;
          case SubscribersSearch::DATE_BETWEEN:
            $FinalQuery[] = "((cinfo.$col BETWEEN %d AND %d) AND cinfo.ContactInfo IS NOT NULL)";
            $QueryData[] = $midnight;
            //convert the date string provided by the datepicker to a timestamp
            $until = Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $EmailSearch['Subscription'][$data . "Until"]);
            $QueryData[] = $until + (24 * 60 * 60) - 1;
            break;
          case SubscribersSearch::DATE_NOT_BETWEEN:
            $FinalQuery[] = "((cinfo.$col NOT BETWEEN %d AND %d) AND cinfo.ContactInfo IS NOT NULL)";
            $QueryData[] = $midnight;
            //convert the date string provided by the datepicker to a timestamp
            $until = Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $EmailSearch['Subscription'][$data . "Until"]);
            $QueryData[] = $until + (24 * 60 * 60) - 1;
            break;
        }
      }
    }
  }

}

/**
 * tag subscriber form
 */
function klicktipp_subscriber_tag_subscribers_form($form, $form_state, $account) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $UserID = $account->uid;

  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));

  $Title = t('Assign tags');

  $Message = t("A JavaScript could not be executed.");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscriber_tag_subscribers_form';
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message, '', 'modal-info');

  //alter the standard delete button to show the number of subscribers to be deleted
  $form['ModalButtons']['Submit']['#theme'] = 'klicktipp_tag_submit';
  $form['ModalButtons']['Submit']['#value'] = t('Assign tags');

  $form['ModalContent']['TagIDs'] = array(
    '#type' => 'textfield',
    '#name' => 'TagIDs',
    '#id' => 'edit-tagids',
    '#default_value' => '',
    '#title' => t("Tags"),
    '#weight' => 3.5,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => TRUE,
      'free_entries' => TRUE,
      'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => array(),
      //catch free entries on form validation error => no validation error possible
    ),
    '#theme' => 'klicktipp_magic_select',
    '#description' => t("Select the tags you want to assign to the following contacts."),
  );


  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $account->uid,
  );

  // write search into form to prevent problems when user has multiple tabs open
  // and starts new searches while a modal dialog is still open
  $form['SearchSettings'] = array(
    '#type' => 'value',
    '#value' => $_SESSION['subscriber_search_filter'],
  );

  //SubscriberIDs of the selected checkboxes in the search result table
  //ID of this element is needed in the JavaScript
  $form['SelectedCheckboxesAssignTag'] = array(
    '#type' => 'hidden',
    '#default_value' => '', //default: user did not select anything
  );

  //when the search form is displayed it contains the search result for the
  //current search session (a search session is generated when the search button is clicked)
  //the javascript of this modal will set its session value to this value of the search session
  //onSubmit (PHP) this session must match $_SESSION['subscriber_search_filter']['Session']
  $form['SearchSession'] = array(
    '#type' => 'hidden',
    '#default_value' => '',
    '#attributes' => array('id' => 'search-session-assign-tag')
  );

  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";
  $form['#attributes']['data-event-load-args'] = 'SubscriberSearchModalAssignTag';

  //implement the onBeforeShow JavaScript callback
  //the form #suffix already contains HTML -> append the script
  $form['#suffix'] .= "
    <script type='text/javascript'>

      window['SubscriberSearchModalAssignTag'] = function ( element ) {

        //get the modal container ID from the button
        var dialog = $('#$ModalID');

        //set the search session
        dialog.find('#search-session-assign-tag').val($('#search-session').val());

        var DisplayCount = 0;

        //make sure the magicselect and the submit button are visible
        dialog.find('input[name=op], .edit-tagids-wrapper').show();

        var SelectedCheckboxes = $('.edit-SelectedCheckboxesAssignTag');

        var Selection = $('.subscriber-select:checked');
        var MasterSelect = $('.subscriber-master-select:checked').length;

        if ( MasterSelect == 1 ) {
          //apply to complete search result

          SelectedCheckboxes.val('" . CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED . "');

          DisplayCount = parseInt($('#SubscriberCountSearch').text());
        }
        else if ( Selection.length ) {
          //apply to single selections

          var selected = [];
          Selection.each(function () {
            selected.push($(this).val());
          });
          SelectedCheckboxes.val(selected.join(','));

          DisplayCount = Selection.length;
        }
        else {
          // no selection, hide magicselect and submit button, only show message

          SelectedCheckboxes.val('');

          var Message = dialog.find('p.modal-message');
          Message.html( '" . t('To apply this action you need to select at least 1 contact.') . "');

          //hide the magicselect and the submit button
          dialog.find('input[name=op], .edit-tagids-wrapper').hide();

          return;
        }

        //the modal dialog contains a magic select, it will not be rendered correctly since it is hidden on page load
        //call each magic selects resize function to render them again, when the dialog is displayed
        dialog.on('shown.bs.modal', function () {
          for ( var ms in window['MagicSelects'] )
            window['MagicSelects'][ms].resize();
        });

        var Message = dialog.find('p.modal-message');
        Message.html( (DisplayCount > 1 ) ? '" . t('The selected tags will be assigned to %count contacts.') . "'.replace('%count', DisplayCount) : '" . t('The selected tags will be assigned to 1 contact.') . "' );

      }

    </script>
  ";

  return $form;
}

/**
 * tag subscriber form submit
 */
function klicktipp_subscriber_tag_subscribers_form_submit($form, &$form_state) {
  $UserID = $form_state['values']['UserID'];
  $TagIDs = $form_state['values']['TagIDs'];
  $session = $form_state['values']['SearchSession'];
  $searchMaskSession = $_SESSION['subscriber_search_filter']['Session'];

  klicktipp_set_redirect($form_state, "subscribers/$UserID");

  if (empty($TagIDs)) {
    drupal_set_message(t('Please select at least 1 tag.'), 'error');
    return;
  }

  if (empty($session) || empty($searchMaskSession) || $session != $searchMaskSession) {
    drupal_set_message(t('The current search session has expired. Please perform your search again.'), 'error');
    return;
  }

  $ArraySubscriptions = array();
  if (empty($form_state['values']['SelectedCheckboxesAssignTag'])) {
    //user did not select anything

    //TODO: in none Bootstrap themes we get here because this confirm form is under the search form and has no access to the search result checkboxes
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }
  else {
    if ($form_state['values']['SelectedCheckboxesAssignTag'] == CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED) {
      // user selected the masterselect, tag the entire search result
      $ArraySubscriptions = klicktipp_subscriber_get_subscriptions($UserID, $form_state['values']['SearchSettings']);
    }
    else {
      // user selected individual subscribers
      $selection = explode(',', $form_state['values']['SelectedCheckboxesAssignTag']);
      foreach ($selection as $subscription) {
        $s = SubscribersSearch::DecodeSubscriptionKey($subscription);
        $ArraySubscriptions[] = [
          'SubscriberID' => $s[0],
          'SubscriptionType' => $s[1],
          'ContactInfo' => $s[2],
          'ReferenceID' => $s[3],
        ];
      }
    }

    if (!empty($ArraySubscriptions)) {
      klicktipp_subscriber_tag_subscribers($UserID, $ArraySubscriptions, $TagIDs);
    }
    else {
      //could only occur when explode(',', $form_state['values']['SelectedCheckboxesAssignTag']); fails, probably JavaScript error
      drupal_set_message(t('Error, there are no contacts to tag.'));
      watchdog("kt-controller", "Error: Modal tagging without SubscriberIDs (UserID: !userID)", ['!userID' => $UserID], WATCHDOG_ERROR);
    }
  }

  $_SESSION['subscriber_search_filter'] = $form_state['values']['SearchSettings'];

}

/**
 * tag subscribers execute
 */
function klicktipp_subscriber_tag_subscribers($UserID, $ArraySubscriptions, $Tags) {

  //in case user entered a free values, create the tags, return value: array(new/existing TagIDs)
  $Tagging = Tag::CreateManualTagByTagName($UserID, $Tags);

  if (!$Tagging) {
    //error creating the tag, no changes in the form have been applied yet, so show error and stay in the settings dialog
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
    watchdog("kt-controller", "Error: CreateManualTagByTagName with (!tags) UserID: !userID", ['!tags' => implode(', ', $Tags), '!userID' => $UserID], WATCHDOG_ERROR);
    return;
  }

  $QueueData = array();

  //prepare data for batch queue
  //each queue entry will tag a subscriber with one tag
  foreach ($Tagging as $TagID) {
    foreach ($ArraySubscriptions as $Subscription) {
      $QueueData[] = array(
        'UserID' => $UserID,
        'SubscriberID' => $Subscription['SubscriberID'],
        'ReferenceID' => $Subscription['ReferenceID'],
        'TagID' => $TagID,
        'Mode' => Subscribers::HISTORY_TAGGED,
      );
    }
  }

  // current selection will be checked/marked as checks, so selection gets obsolete
  unset($_SESSION['subscriber_search_selection']);

  // get worker definition
  $queueinfo = module_invoke_all('cron_queue_info');

  $SubscriberCount = count($ArraySubscriptions);
  if (count($QueueData) > $queueinfo['tag_subscribers']['klicktipp_batchlimit']) { // in background
    $message = t("%count Contacts will be tagged in the background.", array('%count' => $SubscriberCount));
  }

  else {
    // with progress bar

    if ($SubscriberCount == 1) {
      $message = t("Selected Contact successfully tagged.");
    }
    else {
      $message = t("%count Contacts successfully tagged.", array('%count' => $SubscriberCount));
    }
  }

  drupal_set_message($message);

  // tag subscriber batch
  _klicktipp_batch_set('tag_subscribers', $QueueData);

}

/**
 * tag subscriber form
 */
function klicktipp_subscriber_untag_subscribers_form($form, $form_state, $account) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $UserID = $account->uid;

  $TagOptions = Tag::RetrieveTagsAsOptionsArray($UserID, array(Tag::CATEGORY_MANUAL));

  $Title = t('Remove tags');

  $Message = t("A JavaScript could not be executed.");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscriber_untag_subscribers_form';
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message, '', 'modal-info');

  //alter the standard delete button to show the number of subscribers to be deleted
  $form['ModalButtons']['Submit']['#theme'] = 'klicktipp_tag_submit';
  $form['ModalButtons']['Submit']['#value'] = t('Remove tags');

  $form['ModalContent']['TagIDs'] = array(
    '#type' => 'textfield',
    '#name' => 'TagIDs',
    '#id' => 'edit-untagids',
    '#default_value' => '',
    '#title' => t("Tags"),
    '#weight' => 3.5,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $TagOptions,
      'multiple' => TRUE,
      'free_entries' => FALSE,
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('Tags cannot contain only numbers.'),
      'form_post' => array(),
      //catch free entries on form validation error => no validation error possible
    ),
    '#theme' => 'klicktipp_magic_select',
    '#description' => t("Select the tags you want to unassign from the following contacts."),
  );


  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $account->uid,
  );

  // write search into form to prevent problems when user has multiple tabs open
  // and starts new searches while a modal dialog is still open
  $form['SearchSettings'] = array(
    '#type' => 'value',
    '#value' => $_SESSION['subscriber_search_filter'],
  );

  //SubscriberIDs of the selected checkboxes in the search result table
  //ID of this element is needed in the JavaScript
  $form['SelectedCheckboxesRemoveTag'] = array(
    '#type' => 'hidden',
    '#default_value' => '', //default: user did not select anything
  );

  //when the search form is displayed it contains the search result for the
  //current search session (a search session is generated when the search button is clicked)
  //the javascript of this modal will set its session value to this value of the search session
  //onSubmit (PHP) this session must match $_SESSION['subscriber_search_filter']['Session']
  $form['SearchSession'] = array(
    '#type' => 'hidden',
    '#default_value' => '',
    '#attributes' => array('id' => 'search-session-unassign-tag')
  );

  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";
  $form['#attributes']['data-event-load-args'] = 'SubscriberSearchModalRemoveTag';

  //implement the onBeforeShow JavaScript callback
  //the form #suffix already contains HTML -> append the script
  $form['#suffix'] .= "
    <script type='text/javascript'>

      window['SubscriberSearchModalRemoveTag'] = function ( element ) {

        //get the modal container ID from the button
        var dialog = $('#$ModalID');

        var DisplayCount = 0;

        //make sure the magicselect and the submit button are visible
        dialog.find('input[name=op], .edit-tagids-wrapper').show();

        //set the search session
        dialog.find('#search-session-unassign-tag').val($('#search-session').val());

        var SelectedCheckboxes = $('.edit-SelectedCheckboxesRemoveTag');

        var Selection = $('.subscriber-select:checked');
        var MasterSelect = $('.subscriber-master-select:checked').length;

        if ( MasterSelect == 1 ) {
          //apply to complete search result

          SelectedCheckboxes.val('" . CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED . "');

          DisplayCount = parseInt($('#SubscriberCountSearch').text());
        }
        else if ( Selection.length ) {
          //apply to single selections

          var selected = [];
          Selection.each(function () {
            selected.push($(this).val());
          });
          SelectedCheckboxes.val(selected.join(','));

          DisplayCount = Selection.length;
        }
        else {
          // no selection, hide magicselect and submit button, only show message

          SelectedCheckboxes.val('');

          var Message = dialog.find('p.modal-message');
          Message.html( '" . t('To apply this action you need to select at least 1 contact.') . "');

          //hide the magicselect and the submit button
          dialog.find('input[name=op], .edit-tagids-wrapper').hide();

          return;
        }

        //the modal dialog contains a magic select, it will not be rendered correctly since it is hidden on page load
        //call each magic selects resize function to render them again, when the dialog is displayed
        dialog.on('shown.bs.modal', function () {
          for ( var ms in window['MagicSelects'] )
            window['MagicSelects'][ms].resize();
        });

        var Message = dialog.find('p.modal-message');
        Message.html( (DisplayCount > 1 ) ? '" . t('The selected tags will be unassigned from %count contacts.') . "'.replace('%count', DisplayCount) : '" . t('The selected tags will be unassigned from 1 contact.') . "');

      }

    </script>
  ";

  return $form;
}

/**
 * tag subscriber form submit
 */
function klicktipp_subscriber_untag_subscribers_form_submit($form, &$form_state) {
  $UserID = $form_state['values']['UserID'];
  $TagIDs = $form_state['values']['TagIDs'];
  $session = $form_state['values']['SearchSession'];
  $searchMaskSession = $_SESSION['subscriber_search_filter']['Session'];

  klicktipp_set_redirect($form_state, "subscribers/$UserID");

  if (empty($TagIDs)) {
    drupal_set_message(t('Please select at least 1 tag.'), 'error');
    return;
  }

  if (empty($session) || empty($searchMaskSession) || $session != $searchMaskSession) {
    drupal_set_message(t('The current search session has expired. Please perform your search again.'), 'error');
    return;
  }

  $ArraySubscriptions = array();
  if (empty($form_state['values']['SelectedCheckboxesRemoveTag'])) {
    //user did not select anything

    //TODO: in none Bootstrap themes we get here because this confirm form is under the search form and has no access to the search result checkboxes
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }
  else {
    if ($form_state['values']['SelectedCheckboxesRemoveTag'] == CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED) {
      // user selected the masterselect, tag the entire search result
      $ArraySubscriptions = klicktipp_subscriber_get_subscriptions($UserID, $form_state['values']['SearchSettings']);
    }
    else {
      // user selected individual subscribers
      $selection = explode(',', $form_state['values']['SelectedCheckboxesRemoveTag']);
      foreach ($selection as $subscription) {
        $s = SubscribersSearch::DecodeSubscriptionKey($subscription);
        $ArraySubscriptions[] = [
          'SubscriberID' => $s[0],
          'SubscriptionType' => $s[1],
          'ContactInfo' => $s[2],
          'ReferenceID' => $s[3],
        ];
      }
    }

    if (!empty($ArraySubscriptions)) {
      klicktipp_subscriber_untag_subscribers($UserID, $ArraySubscriptions, $TagIDs);
    }
    else {
      //could only occur when explode(',', $form_state['values']['SelectedCheckboxesRemoveTag']); fails, probably JavaScript error
      drupal_set_message(t('Error, there are no contacts to untag.'));
      watchdog("kt-controller", "Error: Modal untagging without SubscriberIDs (UserID: !userID)", ['!userID' => $UserID], WATCHDOG_ERROR);
    }
  }

  $_SESSION['subscriber_search_filter'] = $form_state['values']['SearchSettings'];

}

/**
 * tag subscribers execute
 */
function klicktipp_subscriber_untag_subscribers($UserID, $ArraySubscriptions, $Tags) {

  $QueueData = array();

  //prepare data for batch queue
  //each queue entry will untag a subscriber with one tag
  foreach ($Tags as $TagID) {
    foreach ($ArraySubscriptions as $Subscription) {
      $QueueData[] = array(
        'UserID' => $UserID,
        'SubscriberID' => $Subscription['SubscriberID'],
        'ReferenceID' => $Subscription['ReferenceID'],
        'TagID' => $TagID,
        'Mode' => Subscribers::HISTORY_UNTAGGED,
      );
    }
  }

  // current selection will be checked/marked as checks, so selection gets obsolete
  unset($_SESSION['subscriber_search_selection']);

  // get worker definition
  $queueinfo = module_invoke_all('cron_queue_info');

  $SubscriberCount = count($ArraySubscriptions);
  if (count($QueueData) > $queueinfo['untag_subscribers']['klicktipp_batchlimit']) { // in background
    $message = t("%count Contacts will be untagged in the background.", array('%count' => $SubscriberCount));
  }

  else {
    // with progress bar

    if ($SubscriberCount == 1) {
      $message = t("Selected Contact successfully untagged.");
    }
    else {
      $message = t("%count Contacts successfully untagged.", array('%count' => $SubscriberCount));
    }
  }

  drupal_set_message($message);

  // untag subscribers batch
  _klicktipp_batch_set('untag_subscribers', $QueueData);

}

/**
 * delete subscriber confirm form
 */
function klicktipp_subscriber_delete_confirm_form($form, $form_state, $account) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $UserID = $account->uid;

  $Title = t('Delete Contacts');

  $Message = t("A JavaScript could not be executed.");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscriber_delete_confirm_form';
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message);

  //alter the standard delete button to show the number of subscribers to be deleted
  $SubmitButtonText = t("Delete");
  $form['ModalButtons']['Submit']['#value'] = $SubmitButtonText;

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  // write search into form to prevent problems when user has multiple tabs open
  // and starts new searches while a modal dialog is still open
  $form['SearchSettings'] = array(
    '#type' => 'value',
    '#value' => $_SESSION['subscriber_search_filter'],
  );

  //SubscriberIDs of the selected checkboxes in the search result table
  //ID of this element is needed in the JavaScript
  $form['SelectedCheckboxesDelete'] = array(
    '#type' => 'hidden',
    '#default_value' => '', //default: user did not select anything
  );

  //when the search form is displayed it contains the search result for the
  //current search session (a search session is generated when the search button is clicked)
  //the javascript of this modal will set its session value to this value of the search session
  //onSubmit (PHP) this session must match $_SESSION['subscriber_search_filter']['Session']
  $form['SearchSession'] = array(
    '#type' => 'hidden',
    '#default_value' => '',
    '#attributes' => array('id' => 'search-session-delete')
  );

  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";
  $form['#attributes']['data-event-load-args'] = 'SubscriberSearchModalDelete';

  $hint = json_encode(t('To apply this action you need to select at least 1 contact.'));
  $singularMsg = json_encode(t('Are you sure to delete the selected Contact?'));
  $singularButton = json_encode(t('I would like to delete the selected Contact'));
  $pluralMsg = json_encode(t('Are you sure to delete %count Contacts?'));
  $pluralButton = json_encode(t('I would like to delete @count Contacts'));
  $allMsg = json_encode(t('Contacts::DeleteAll::ConfirmationModal::You are about to delete a large number (%count) of your contacts. This deletion operation cannot be undone. To continue, please confirm the deletion operation by entering the total number of contacts to be deleted in the field below:'));
  $allButton = $pluralButton; // as of now: $allButton var only exists to make it more consistent

  //implement the onBeforeShow JavaScript callback
  //the form #suffix already contains HTML -> append the script
  $form['#suffix'] .= "
    <script type='text/javascript'>

      window['SubscriberSearchModalDelete'] = function ( element ) {

        //get the modal container ID from the button
        var dialog = $('#$ModalID');

        //set the search session
        dialog.find('#search-session-delete').val($('#search-session').val());

        var DisplayCount = 0;

        var SelectedCheckboxes = $('.edit-SelectedCheckboxesDelete');

        //make sure the submit button and warning are visible
        $('#klicktipp_subscriber_delete_confirm_form input[name=op], .modal-warning').show();

        var Selection = $('.subscriber-select:checked');

        var MasterSelect = $('.subscriber-master-select:checked').length;

        if ( MasterSelect == 1 ) {
          //apply to complete search result

          SelectedCheckboxes.val('" . CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED . "');

          DisplayCount = parseInt($('#SubscriberCountSearch').text());
        }
        else if ( Selection.length ) {
          var selected = [];
          Selection.each(function () {
            var sid = $(this).val();
            if (selected.indexOf(sid) === -1) {
              //unique subscriber ids
              selected.push(sid);
            }
          });
          SelectedCheckboxes.val(selected.join(','));

          DisplayCount = selected.length;

        }
        else {
          // no selection, hide magicselect and submit button, only show message

          SelectedCheckboxes.val('');

          var Message = dialog.find('p.modal-message');
          Message.html($hint);

          //hide the submit button and warning
          $('#klicktipp_subscriber_delete_confirm_form input[name=op], .modal-warning').hide();

          return;

        }

        var Message = dialog.find('p.modal-message');
        var Button = dialog.find('input[name=op]');

        if (MasterSelect == 1 && DisplayCount > Selection.length) {

          $('#klicktipp_subscriber_delete_confirm_form input[name=op], .modal-warning').hide();
          var msg = $allMsg + '<br/><input id=\"confirm-amount-deleted\"\>';
          Message.html(msg.replace('%count', DisplayCount));
          //check if input in input field matches total contacts to be deleted, if yes: enable confirm button
          $('#confirm-amount-deleted').on('keyup', function () { if ( $(this).val() == DisplayCount ) { $('#klicktipp_subscriber_delete_confirm_form input[name=op], .modal-warning').show(); } else { $('#klicktipp_subscriber_delete_confirm_form input[id=edit-submit--6], .modal-warning').hide(); }} );
          var button = $allButton;
          Button.val(button.replace('@count', DisplayCount));
        } else if (DisplayCount > 1) {
          var msg = $pluralMsg;
          Message.html(msg.replace('%count', DisplayCount));
          var button = $pluralButton;
          Button.val(button.replace('@count', DisplayCount));
        }
        else {
          Message.html($singularMsg);
          Button.val($singularButton);
        }

        //after clicking (before submitting the form) change the button text back to the original text, so we pass Drupals validation
        Button.one('click', function () { $(this).val('$SubmitButtonText')});

      }

    </script>
  ";

  return $form;

}

/**
 * Form submit handler for delete_confirm_form.
 * @param $form
 * @param $form_state
 */
function klicktipp_subscriber_delete_confirm_form_submit($form, &$form_state) {
  $UserID = $form_state['values']['UserID'];
  $session = $form_state['values']['SearchSession'];
  $searchMaskSession = $_SESSION['subscriber_search_filter']['Session'];

  if (empty($session) || empty($searchMaskSession) || $session != $searchMaskSession) {
    drupal_set_message(t('The current search session has expired. Please perform your search again.'), 'error');
    return;
  }

  $ArraySubscriptions = array();
  if (empty($form_state['values']['SelectedCheckboxesDelete'])) {
    //user did not select anything

    //TODO: in none Bootstrap themes we get here because this confirm form is under the search form and has no access to the search result checkboxes
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }
  else {
    if ($form_state['values']['SelectedCheckboxesDelete'] == CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED) {
      // user selected the masterselect, tag the entire search result
      $ArraySubscriptions = klicktipp_subscriber_get_subscriptions($UserID, $form_state['values']['SearchSettings']);
    }
    else {
      //user selected individual subscribers
      $selection = explode(',', $form_state['values']['SelectedCheckboxesDelete']);

      foreach ($selection as $subscription) {
        $s = SubscribersSearch::DecodeSubscriptionKey($subscription);
        $ArraySubscriptions[] = [
          'SubscriberID' => $s[0],
          'SubscriptionType' => $s[1],
          'ContactInfo' => $s[2],
          'ReferenceID' => $s[3],
        ];
      }
    }

    if (!empty($ArraySubscriptions)) {
      klicktipp_subscriber_event_delete_subscribers($UserID, $ArraySubscriptions);
    }
    else {
      //could only occur when explode(',', $form_state['values']['SelectedCheckboxesDelete']); fails, probably JavaScript error
      drupal_set_message(t('Error, there are no contacts to delete.'));
      watchdog("kt-controller", "Error: Modal delete without SubscriberIDs (UserID: !userID)", ['!userID' => $UserID], WATCHDOG_ERROR);
    }
  }

  $_SESSION['subscriber_search_filter'] = $form_state['values']['SearchSettings'];
  klicktipp_set_redirect($form_state, "subscribers/$UserID");
}




/**
 * delete subscriber confirm form
 */
function klicktipp_subscriber_delete_contactinfo_confirm_form($form, $form_state, $account) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $UserID = $account->uid;

  $Title = t('Delete ContactInfos');

  $Message = t("A JavaScript could not be executed.");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscriber_delete_contactinfo_confirm_form';
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message);

  //alter the standard delete button to show the number of subscribers to be deleted
  $SubmitButtonText = t("Delete");
  $form['ModalButtons']['Submit']['#value'] = $SubmitButtonText;

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  // write search into form to prevent problems when user has multiple tabs open
  // and starts new searches while a modal dialog is still open
  $form['SearchSettings'] = array(
    '#type' => 'value',
    '#value' => $_SESSION['subscriber_search_filter'],
  );

  //SubscriberIDs of the selected checkboxes in the search result table
  //ID of this element is needed in the JavaScript
  $form['SelectedCheckboxesDeleteContactInfos'] = array(
    '#type' => 'hidden',
    '#default_value' => '', //default: user did not select anything
  );

  //when the search form is displayed it contains the search result for the
  //current search session (a search session is generated when the search button is clicked)
  //the javascript of this modal will set its session value to this value of the search session
  //onSubmit (PHP) this session must match $_SESSION['subscriber_search_filter']['Session']
  $form['SearchSession'] = array(
    '#type' => 'hidden',
    '#default_value' => '',
    '#attributes' => array('id' => 'search-session-delete-contactinfo')
  );

  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";
  $form['#attributes']['data-event-load-args'] = 'SubscriberSearchModalDeleteContactInfos';

  $hint = json_encode(t('To apply this action you need to select at least 1 ContactInfo.'));
  $singularMsg = json_encode(t('Are you sure to delete the selected ContactInfo?'));
  $singularButton = json_encode(t('I would like to delete the selected ContactInfo'));
  $pluralMsg = json_encode(t('Are you sure to delete %count ContactInfos?'));
  $pluralButton = json_encode(t('I would like to delete @count ContactInfos'));

  //implement the onBeforeShow JavaScript callback
  //the form #suffix already contains HTML -> append the script
  $form['#suffix'] .= "
    <script type='text/javascript'>

      window['SubscriberSearchModalDeleteContactInfos'] = function ( element ) {

        //get the modal container ID from the button
        var dialog = $('#$ModalID');

        //set the search session
        dialog.find('#search-session-delete-contactinfo').val($('#search-session').val());

        var DisplayCount = 0;

        var SelectedCheckboxes = $('.edit-SelectedCheckboxesDeleteContactInfos');

        //make sure the submit button and warning are visible
        $('input[name=op], .modal-warning').show();

        var Selection = $('.subscriber-select:checked');
        var MasterSelect = $('.subscriber-master-select:checked').length;

        if ( MasterSelect == 1 ) {
          //apply to complete search result

          SelectedCheckboxes.val('" . CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED . "');

          DisplayCount = parseInt($('#SubscriptionCountSearch').text());
        }
        else if ( Selection.length ) {
          var selected = [];
          Selection.each(function () {
            selected.push($(this).val());
          });
          SelectedCheckboxes.val(selected.join(','));

          DisplayCount = Selection.length;

        }
        else {
          // no selection, hide magicselect and submit button, only show message

          SelectedCheckboxes.val('');

          var Message = dialog.find('p.modal-message');
          Message.html($hint);

          //hide the submit button and warning
          $('input[name=op], .modal-warning').hide();

          return;

        }

        var Message = dialog.find('p.modal-message');
        var Button = dialog.find('input[name=op]');

        if (DisplayCount > 1) {
          var msg = $pluralMsg;
          Message.html(msg.replace('%count', DisplayCount));
          var button = $pluralButton;
          Button.val(button.replace('@count', DisplayCount));
        }
        else {
          Message.html($singularMsg);
          Button.val($singularButton);
        }

        //after clicking (before submitting the form) change the button text back to the original text, so we pass Drupals validation
        Button.one('click', function () { $(this).val('$SubmitButtonText')});

      }

    </script>
  ";

  return $form;

}

/**
 * Form submit handler for delete_confirm_form.
 * @param $form
 * @param $form_state
 */
function klicktipp_subscriber_delete_contactinfo_confirm_form_submit($form, &$form_state) {
  $UserID = $form_state['values']['UserID'];
  $session = $form_state['values']['SearchSession'];
  $searchMaskSession = $_SESSION['subscriber_search_filter']['Session'];

  if (empty($session) || empty($searchMaskSession) || $session != $searchMaskSession) {
    drupal_set_message(t('The current search session has expired. Please perform your search again.'), 'error');
    return;
  }

  $ArraySubscriptions = array();
  if (empty($form_state['values']['SelectedCheckboxesDeleteContactInfos'])) {
    //user did not select anything

    //TODO: in none Bootstrap themes we get here because this confirm form is under the search form and has no access to the search result checkboxes
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
  }
  else {
    if ($form_state['values']['SelectedCheckboxesDeleteContactInfos'] == CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED) {
      // user selected the masterselect, tag the entire search result
      $ArraySubscriptions = klicktipp_subscriber_get_subscriptions($UserID, $form_state['values']['SearchSettings']);
    }
    else {
      //user selected individual subscribers
      $selection = explode(',', $form_state['values']['SelectedCheckboxesDeleteContactInfos']);
      foreach ($selection as $subscription) {
        $s = SubscribersSearch::DecodeSubscriptionKey($subscription);
        $ArraySubscriptions[] = [
          'SubscriberID' => $s[0],
          'SubscriptionType' => $s[1],
          'ContactInfo' => $s[2],
          'ReferenceID' => $s[3],
        ];
      }
    }

    if (!empty($ArraySubscriptions)) {
      klicktipp_subscriber_event_delete_subscription($UserID, $ArraySubscriptions);
    }
    else {
      //could only occur when explode(',', $form_state['values']['SelectedCheckboxesDelete']); fails, probably JavaScript error
      drupal_set_message(t('Error, there are no ContactInfos to delete.'));
      watchdog("kt-controller", "Error: Modal delete without Subscriptions (UserID: !userID)", ['!userID' => $UserID], WATCHDOG_ERROR);
    }
  }

  $_SESSION['subscriber_search_filter'] = $form_state['values']['SearchSettings'];
  klicktipp_set_redirect($form_state, "subscribers/$UserID");

}

/**
 * Delete subscribers event
 *
 * @param $UserID
 * @param array $ArraySubscriptions
 */
function klicktipp_subscriber_event_delete_subscribers($UserID, $ArraySubscriptions = array()) {

  $QueueData = array();

  //prepare data for batch queue
  foreach ($ArraySubscriptions as $Subscription) {
    //unique entries
    if (empty($QueueData[$Subscription['SubscriberID']])) {
      $QueueData[$Subscription['SubscriberID']] = array(
        'UserID' => $UserID,
        'SubscriberID' => $Subscription['SubscriberID'],
      );
    }
  }

  $QueueData = array_values($QueueData);

  // get worker definition
  $queueinfo = module_invoke_all('cron_queue_info');

  $SubscriberCount = count($QueueData);
  if (count($QueueData) > $queueinfo['delete_subscribers']['klicktipp_batchlimit']) {
    // in background (queue)
    $message = t("%count Contacts will be deleted in the background.", array('%count' => $SubscriberCount));
  }

  else {
    // with progress bar (drupal batch)

    if ($SubscriberCount == 1) {
      $message = t('Selected contact successfully deleted.');
    }
    else {
      $message = t("Selected contacts successfully deleted.");
    }
  }

  //current selection will be deleted, we don't need it anymore
  unset($_SESSION['subscriber_search_selection']);

  drupal_set_message($message);

  //delete subscribers
  _klicktipp_batch_set('delete_subscribers', $QueueData);

}

/**
 * Delete contact infos event
 *
 * @param $UserID
 * @param array $Selection
 */
function klicktipp_subscriber_event_delete_subscription($UserID, $Selection = array()) {

  //prepare data for batch queue
  $ArraySubscriptions = array();
  foreach ($Selection as $s) {

    //the selection could contain the same subscriptions with different reference ids
    $key = SubscribersSearch::EncodeSubscriptionKey($s['SubscriberID'], $s['SubscriptionType'], $s['ContactInfo'], 0);

    //unique entries
    $ArraySubscriptions[$key] = array(
      'UserID' => $UserID,
      'SubscriberID' => $s['SubscriberID'],
      'SubscriptionType' => $s['SubscriptionType'],
      'ContactInfo' => $s['ContactInfo'],
    );
  }

  $QueueData = array_values($ArraySubscriptions);

  // get worker definition
  $queueinfo = module_invoke_all('cron_queue_info');

  $SubscriberCount = count($ArraySubscriptions);
  if (count($QueueData) > $queueinfo['delete_subscriptions']['klicktipp_batchlimit']) {
    // in background (queue)
    $message = t("%count ContactInfos will be deleted in the background.", array('%count' => $SubscriberCount));
  }

  else {
    // with progress bar (drupal batch)

    if ($SubscriberCount == 1) {
      $message = t('Selected ContactInfo successfully deleted.');
    }
    else {
      $message = t("Selected ContactInfos successfully deleted.");
    }
  }

  //current selection will be deleted, we don't need it anymore
  unset($_SESSION['subscriber_search_selection']);

  drupal_set_message($message);

  //delete subscriptions
  _klicktipp_batch_set('delete_subscriptions', $QueueData);

}

/**
 * unsubscribe subscriber confirm form
 */
function klicktipp_subscriber_unsubscribe_confirm_form($form, $form_state, $account) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $UserID = $account->uid;

  $Title = t('Unsubscribe Contacts');

  $Message = t("A JavaScript could not be executed.");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscriber_unsubscribe_confirm_form';
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message);

  //alter the standard delete button to show the number of subscribers to be deleted
  $SubmitButtonText = t("Unsubscribe");
  $form['ModalButtons']['Submit']['#value'] = $SubmitButtonText;
  $form['ModalButtons']['Submit']['#theme'] = 'klicktipp_unsubscribe_submit';

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  // write search into form to prevent problems when user has multiple tabs open
  // and starts new searches while a modal dialog is still open
  $form['SearchSettings'] = array(
    '#type' => 'value',
    '#value' => $_SESSION['subscriber_search_filter'],
  );

  //SubscriberIDs of the selected checkboxes in the search result table
  //ID of this element is needed in the JavaScript
  $form['SelectedCheckboxesUnsubscribe'] = array(
    '#type' => 'hidden',
    '#default_value' => '', //default: user did not select anything
  );

  //when the search form is displayed it contains the search result for the
  //current search session (a search session is generated when the search button is clicked)
  //the javascript of this modal will set its session value to this value of the search session
  //onSubmit (PHP) this session must match $_SESSION['subscriber_search_filter']['Session']
  $form['SearchSession'] = array(
    '#type' => 'hidden',
    '#default_value' => '',
    '#attributes' => array('id' => 'search-session-unsubscribe')
  );

  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";
  $form['#attributes']['data-event-load-args'] = 'SubscriberSearchModalUnsubscribe';

  $hint = json_encode(t('To apply this action you need to select at least 1 ContactInfo.'));
  $singularMsg = json_encode(t('Are you sure to unsubscribe the selected ContactInfo?'));
  $singularButton = json_encode(t('I would like to unsubscribe the selected ContactInfo'));
  $pluralMsg = json_encode(t('Are you sure to unsubscribe %count ContactInfos?'));
  $pluralButton = json_encode(t('I would like to unsubscribe @count ContactInfos'));

  //implement the onBeforeShow JavaScript callback
  //the form #suffix already contains HTML -> append the script
  $form['#suffix'] .= "
    <script type='text/javascript'>

      window['SubscriberSearchModalUnsubscribe'] = function ( element ) {

        //get the modal container ID from the button
        var dialog = $('#$ModalID');

        //set the search session
        dialog.find('#search-session-unsubscribe').val($('#search-session').val());

        var DisplayCount = 0;

        //make sure the submit button and warning are visible
        $('input[name=op], .modal-warning').show();

        var SelectedCheckboxes = $('.edit-SelectedCheckboxesUnsubscribe');

        var Selection = $('.subscriber-select:checked');
        var MasterSelect = $('.subscriber-master-select:checked').length;

        if ( MasterSelect == 1 ) {
          //apply to complete search result

          SelectedCheckboxes.val('" . CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED . "');

          DisplayCount = parseInt($('#SubscriberCountSearch').text());

        }
        else if ( Selection.length ) {
          var selected = [];
          Selection.each(function () {
            selected.push($(this).val());
          });
          SelectedCheckboxes.val(selected.join(','));

          DisplayCount = Selection.length;

        }
        else {

          // no selection, hide magicselect and submit button, only show message

          SelectedCheckboxes.val('');

          var Message = dialog.find('p.modal-message');
          Message.html($hint);

          //hide the submit button and warning
          $('input[name=op], .modal-warning').hide();

          return;

        }

        var Message = dialog.find('p.modal-message');
        var Button = dialog.find('input[name=op]');

         if (DisplayCount > 1) {
          var msg = $pluralMsg;
          Message.html(msg.replace('%count', DisplayCount));
          var button = $pluralButton;
          Button.val(button.replace('@count', DisplayCount));
        }
        else {
          Message.html($singularMsg);
          Button.val($singularButton);
        }

        //after clicking (before submitting the form) change the button text back to the original text, so we pass Drupals validation
        Button.one('click', function () { $(this).val('$SubmitButtonText')});

      }

    </script>
  ";

  return $form;

}

/**
 * unsubscribe subscriber confirm form submit
 */
function klicktipp_subscriber_unsubscribe_confirm_form_submit($form, &$form_state) {
  $UserID = $form_state['values']['UserID'];
  $session = $form_state['values']['SearchSession'];
  $searchMaskSession = $_SESSION['subscriber_search_filter']['Session'];

  if (empty($session) || empty($searchMaskSession) || $session != $searchMaskSession) {
    drupal_set_message(t('The current search session has expired. Please perform your search again.'), 'error');
    return;
  }

  $ArraySubscriptions = array();
  if (empty($form_state['values']['SelectedCheckboxesUnsubscribe'])) {
    //user did not select anything

    //TODO: in none Bootstrap themes we get here because this confirm form is under the search form and has no access to the search result checkboxes
    drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');

  }
  else {

    if ($form_state['values']['SelectedCheckboxesUnsubscribe'] == CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED) {
      // user selected the masterselect, tag the entire search result
      $ArraySubscriptions = klicktipp_subscriber_get_subscriptions($UserID, $form_state['values']['SearchSettings']);
    }
    else {
      // user selected individual subscribers
      $selection = explode(',', $form_state['values']['SelectedCheckboxesUnsubscribe']);
      foreach ($selection as $subscription) {
        $s = SubscribersSearch::DecodeSubscriptionKey($subscription);
        $ArraySubscriptions[] = [
          'SubscriberID' => $s[0],
          'SubscriptionType' => $s[1],
          'ContactInfo' => $s[2],
          'ReferenceID' => $s[3],
        ];
      }
    }

    if (!empty($ArraySubscriptions)) {
      klicktipp_subscriber_event_unsubscribe_subscribers($UserID, $ArraySubscriptions);
    }
    else {
      //could only occur when explode(',', $form_state['values']['SelectedCheckboxesUnsubscribe']); fails, probably JavaScript error
      drupal_set_message(t('Error, there are no contacts to unsubscribe.'));
      watchdog("kt-controller", "Error: Modal unsubscribe without SubscriberIDs (UserID: !userID)", ['!userID' => $UserID], WATCHDOG_ERROR);
    }
  }

  $_SESSION['subscriber_search_filter'] = $form_state['values']['SearchSettings'];
  klicktipp_set_redirect($form_state, "subscribers/$UserID");
}

/**
 * Unsubscribe subscribers event
 *
 * @param $UserID
 * @param array $Selection
 */
function klicktipp_subscriber_event_unsubscribe_subscribers($UserID, $Selection = array()) {

  $ArraySubscriptions = array();
  foreach ($Selection as $s) {

    //the selection could contain the same subscriptions with different reference ids
    $key = SubscribersSearch::EncodeSubscriptionKey($s['SubscriberID'], $s['SubscriptionType'], $s['ContactInfo'], 0);

    //unique entries
    if (empty($ArraySubscriptions[$key])) {

      $ArraySubscriptions[$key] = array(
        'UserID' => $UserID,
        'SubscriberID' => $s['SubscriberID'],
        'SubscriptionType' => $s['SubscriptionType'],
        'ContactInfo' => $s['ContactInfo'],
      );

      Subscription::UnsubscribeSubscription($UserID, $s['ContactInfo'], $s['SubscriptionType']);

    }

  }

  //current selection will be unsubscribed, we don't need it anymore
  unset($_SESSION['subscriber_search_selection']);

  //unsubscribe subscribers
  if (!empty($ArraySubscriptions)) {
    if (count($ArraySubscriptions) == 1) {
      $sub = reset($ArraySubscriptions);
      $message = t('Contact %name successfully unsubscribed.', array(
        '%name' => $sub['ContactInfo']
      ));
    }
    else {
      $message = t('Selected Contacts successfully unsubscribed.');

    }
    drupal_set_message($message);
  }

  // make sure all list/subscriber displays are current
  UserCache::clear($UserID);

}

/**
 * export subscriber form
 */
function klicktipp_subscriber_export_subscribers_form($form, $form_state, $account) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Export Contacts');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(l(t('Search Contacts'), "subscribers/$UserID")), $page_title);

  $Subscriptions = $_SESSION['subscriber_search_selection']; //selected subscribers from search result table

  $CustomFields = CustomFields::RetrieveCustomFieldsCategories($UserID);
  $CategoryFields = $CustomFields['CategoryFields'];

  if ($Subscriptions == CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED || empty($Subscriptions)) {
    // user checked the master select, apply to entire result
    // if empty($Subscriptions) the user must have called the export dialog via URL, show him his last or default search result -> just an export, no harm done
    $Subscriptions = klicktipp_subscriber_get_subscriptions($UserID, $_SESSION['subscriber_search_filter']);
  }

  $weight = 1;
  $form = array();

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  $form['Subscriptions'] = array(
    '#type' => 'value',
    '#value' => $Subscriptions,
  );

  $SubscriberCount = count($Subscriptions);
  $form['intro'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_info',
    '#weight' => $weight++,
    '#value' => ($SubscriberCount == 1) ? t("Please choose the data you want to export for the selected contact.") : t("Please choose the data you want to export for the !count selected contacts.", array('!count' => "<strong>$SubscriberCount</strong>")),
  );

  // --- email marketing information

  $subscription_options = array(
    'SubscriberID' => t("Contact ID"),
    'SubscriptionStatus' => t("Subscription Status"),
    'BounceType' => t("Bounce status"),
    'OptInDate' => t("Opt-In Date"),
    'OptInIP' => t("Opt-In IP"),
    'SubscriptionDate' => t("Confirmation Date"),
    'SubscriptionIP' => t("Confirmation IP"),
    'UnsubscriptionDate' => t("Unsubscription Date"),
    'UnsubscriptionIP' => t("Unsubscription IP"),
    'SubscriptionReferrer' => t("Referrer"),
  );

  $form['ContactData'] = array(
    '#type' => 'fieldset',
    '#title' => t('Contact data'),
    '#collapsible' => TRUE,
    '#collapsed' => FALSE,
    '#weight' => $weight++,
  );

  $form['ContactData']['Subscription'] = array(
    '#type' => 'select',
    '#title' => t('Subscription information'),
    '#default_value' => array_keys($subscription_options),
    '#options' => $subscription_options,
    '#weight' => $weight++,
    '#multiple' => TRUE,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => '#options',
      'multiple' => TRUE,
      'free_entries' => FALSE,
      'form_post' => $form_state['input']['Subscription'],
    ),
    '#theme' => 'klicktipp_magic_select',
  );

  // --- sms marketing information

  $global_field_options = array();
  $global_field_defaults = array();
  $field_options = array();
  $field_defaults = array();
  foreach ($CategoryFields as $category => $fields) {
    foreach ($fields as $f) {
      if ($f['IsGlobal']) {
        $global_field_options[$f['CustomFieldID']] = $f['FieldName'];
        $global_field_defaults[] = $f['CustomFieldID']; //select all
      }
      else {
        $field_options[$f['CustomFieldID']] = $f['FieldName'];
        $field_defaults[] = $f['CustomFieldID']; //select all
      }
    }
  }

  $form['ContactData']['GlobalCustomFields'] = array(
    '#type' => 'select',
    '#title' => t('Global custom fields'),
    '#default_value' => $global_field_defaults,
    '#options' => $global_field_options,
    '#weight' => $weight++,
    '#multiple' => TRUE,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => '#options',
      'multiple' => TRUE,
      'free_entries' => FALSE,
      'form_post' => $form_state['input']['GlobalCustomFields'],
    ),
    '#theme' => 'klicktipp_magic_select',
  );

  $form['ContactData']['CustomFields'] = array(
    '#type' => 'select',
    '#title' => t('Custom fields'),
    '#default_value' => $field_defaults,
    '#options' => $field_options,
    '#weight' => $weight++,
    '#multiple' => TRUE,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => '#options',
      'multiple' => TRUE,
      'free_entries' => FALSE,
      'form_post' => $form_state['input']['CustomFields'],
    ),
    '#theme' => 'klicktipp_magic_select',
  );

  $form['SettingsFrame'] = array(
    '#type' => 'fieldset',
    '#title' => t('Settings'),
    '#collapsible' => TRUE,
    '#collapsed' => FALSE,
    '#weight' => $weight++,
  );

  $form['SettingsFrame']['Tags'] = array(
    '#type' => 'checkbox',
    '#default_value' => 1,
    '#return_value' => 1,
    '#title' => t("Export the assigned manual tags as a comma separated list"),
    '#weight' => $weight++,
  );

  $form['SettingsFrame']['Delimeter'] = array(
    '#type' => 'select',
    '#default_value' => 59,
    '#options' => array(
      9 => t('Tab'),
      59 => t('Semicolon'),
      44 => t('Comma'),
      32 => t('Space'),
    ),
    '#title' => t('CSV delimeter'),
    '#weight' => $weight++,
  );

  $form['SettingsFrame']['Encloser'] = array(
    '#type' => 'select',
    '#default_value' => 34,
    '#options' => array(
      34 => t('Double quotes'),
      39 => t('Single quotes'),
    ),
    '#title' => t('CSV string encloser'),
    '#weight' => $weight++,
  );

  global $user;
  if (user_access('administer klicktipp')) {

    $form['SettingsFrame']['EmailAddress'] = array(
      '#type' => 'textfield',
      '#title' => t('Email address'),
      '#default_value' => Subscribers::DepunycodeEmailAddress($user->mail),
      '#weight' => $weight++,
      '#description' => t('Enter an email address to which the download link will be sent.'),
    );

  }
  else {

    $form['EmailAddress'] = array(
      '#type' => 'value',
      '#value' => Subscribers::DepunycodeEmailAddress($user->mail),
    );

  }

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_download_submit',
    '#value' => t(/*ignore*/CONTROLLER_SUBSCRIBER_EXPORT_BUTTON_TEXT),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "subscribers/$UserID",
    '#theme' => 'klicktipp_cancel_button',
    '#weight' => $weight++,
  );

  return $form;

}

function klicktipp_subscriber_export_subscribers_form_validate($form, &$form_state) {

  if (empty($form_state['values']['EmailAddress'])) {
    form_set_error('EmailAddress', t('Please enter an email address to which the download link will be sent.'));
  }

}

/**
 * export subscriber form submit
 */
function klicktipp_subscriber_export_subscribers_form_submit($form, &$form_state) {

  $account = $form_state['values']['account'];

  $UserID = $account->uid;
  $SubscriberIDs = $form_state['values']['Subscriptions'];
  $CustomFields = (empty($form_state['values']['CustomFields'])) ? array() : $form_state['values']['CustomFields'];
  $GlobalCustomFields = (empty($form_state['values']['GlobalCustomFields'])) ? array() : $form_state['values']['GlobalCustomFields'];
  $ExportSubscription = (empty($form_state['values']['Subscription'])) ? array() : $form_state['values']['Subscription'];
  $Tags = $form_state['values']['Tags'];
  $Delimiter = chr($form_state['values']['Delimeter']);
  $Encloser = chr($form_state['values']['Encloser']);
  $EmailAddress = $form_state['values']['EmailAddress'];

  $ExportCustomFields = $GlobalCustomFields + $CustomFields;

  //current selection will be exported, we don't need it anymore
  unset($_SESSION['subscriber_search_selection']);

  // --- export subscribers

  // Add global custom field values to subscribers - Start {
  $ArrayCustomFields = CustomFields::RetrieveCustomFieldsCategories($UserID);

  $subscription_labels = $form['ContactData']['Subscription']['#options'];

  // create CSV-Header and
  // Header2 will contain hashes to identify fields for re-import
  // Note: not all fields can be re-imported into their source field
  //       example: SubscriptionType
  //       those field won't have a hash

  $ImportFields = VarImport::GetImportFields($UserID);
  //prepare for easier access
  $Hashes = array();
  foreach($ImportFields as $field) {
    $Hashes[$field['id']] = $field['csvHash'];
  }

  $Header = array(
    'Hash' => t('KlickTipp ID'),
    'ContactInfo' => t('ContactInfo'),
    'SubscriptionType' => t('SubscriptionType'),
    'ReferenceDisplay' => t('Context')
  );
  $Header2 = array(
    'Hash' => $Hashes['Hash'],
    'ContactInfo' => '',
    'SubscriptionType' => '',
    'ReferenceDisplay' => ''
  );
  foreach ($ExportSubscription as $value) {
    $Header[$value] = $subscription_labels[$value];
    $Header2[$value] = $Hashes[$value];
  }

  if (!empty($ExportCustomFields)) {

    foreach ($ExportCustomFields as $FieldID) {
      $Header["CustomField$FieldID"] = CustomFields::GetFieldName($ArrayCustomFields['AllCustomFields'][$FieldID]);
      $Header2["CustomField$FieldID"] = $Hashes[$FieldID];
    }

  }

  // --- get tags to export ---

  if ($Tags) {

    $ManualTags = Tag::RetrieveManualTags($UserID);

    foreach ($ManualTags as $TagID => $Tag) {
      $Header["Tagging$TagID"] = $Tag['TagName'];
      $Header2["Tagging$TagID"] = $Hashes[$TagID];
    }

  }

  // --- export filename, path ---

  $DownloadTime = time();
  $filename = date("Y_m_d_H_i_s", $DownloadTime) . ".csv";
  $dir = "private://$UserID/export";
  $uri = "$dir/$filename";

  //create the user directory for exports and/or check if it is writable
  if (!file_prepare_directory($dir, FILE_CREATE_DIRECTORY || FILE_MODIFY_PERMISSIONS)) {

    $error = array(
      '!uid' => $UserID,
      '!uri' => $uri,
    );

    Errors::unexpected("Error: Create/Modify private user directory for user !uid. URI = '!uri'", $error, TRUE); //notify user

    return;

  }

  //  --- create queue ---

  $LogID = ProcessLog::Create('export_subscribers');

  $item_size = variable_get('klicktipp_export_queue_item_size', 1000);

  $TotalSubscribers = count($SubscriberIDs);
  $TotalParts = ceil($TotalSubscribers / $item_size);
  $Part = 1;

  while (!empty($SubscriberIDs)) {

    if (count($SubscriberIDs) > $item_size) {
      $ExportIDs = array_splice($SubscriberIDs, 0, $item_size);
    }
    else {
      $ExportIDs = $SubscriberIDs;
      $SubscriberIDs = array(); //will break the loop
    }

    $item = array(
      'UserID' => $UserID,
      'SubscriptionIDs' => $ExportIDs,
      'TotalSubscribers' => $TotalSubscribers,
      'Filename' => $filename,
      'URI' => $uri,
      'Header' => $Header,
      'Header2' => $Header2,
      'Delimiter' => $Delimiter,
      'Encloser' => $Encloser,
      'EmailAddress' => Subscribers::PunycodeEmailAddress($EmailAddress),
      'Time' => $DownloadTime,
      'TotalParts' => $TotalParts,
      'Part' => $Part++,
      'ExportStart' => $DownloadTime,
    );

    ProcessLog::AddQueueItem($LogID, $item);

  }

  drupal_set_message(t('Your export is being processed in the background. After completion a download link will be sent to your email address @email.', array(
    '@email' => $EmailAddress,
  )));

  klicktipp_set_redirect($form_state, "subscribers/$UserID");

  return;

}

/**
 * subscriber audience create form
 */
function klicktipp_subscriber_audience_create_form($form, $form_state, $account) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $UserID = $account->uid;

  $Title = t('Create subscriber audience');

  $Message = t("Define a name for your subscriber audience.");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscriber_audience_create_form';
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message, '', 'modal-info');

  $audienceOptions = VarSubscriberAudience::RetrieveAudiencesAsOptionsArray($UserID);

  $form['ModalContent']['Name'] = array(
    '#type' => 'textfield',
    '#default_value' => '',
    '#id' => 'edit-audience',
    '#name' => 'Name',
    '#title' => t("Subscriber audience name"),
    '#weight' => 2.5,
    '#magic_select' => array(
      'autocomplete' => '',
      'options' => $audienceOptions,
      'multiple' => FALSE,
      'free_entries' => TRUE,
      'free_entries_message' => t('SubscriberAudience::This will create a new audience.'),
      'allow_numeric_only' => FALSE,
      'numeric_only_message' => t('SubscriberAudience::The audience name cannot contain only numbers.'),
      'form_post' => array(),
      //catch free entries on form validation error => no validation error possible
    ),
    '#theme' => 'klicktipp_magic_select',
  );

  //alter the standard delete button to show the number of subscribers to be deleted
  $SubmitButtonText = t("SubscriberAudience::Save");
  $form['ModalButtons']['Submit']['#value'] = $SubmitButtonText;
  $form['ModalButtons']['Submit']['#theme'] = 'klicktipp_check_submit';

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  // write search into form to prevent problems when user has multiple tabs open
  // and starts new searches while a modal dialog is still open
  $form['SearchSettings'] = array(
    '#type' => 'value',
    '#value' => $_SESSION['subscriber_search_filter'],
  );

  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";
  $form['#attributes']['data-event-load-args'] = 'SubscriberSearchModalAudienceSave';

  $form['#suffix'] .= "
  
        <script type='text/javascript'>
          window['SubscriberSearchModalAudienceSave'] = function ( element ) {
    
            //get the modal container ID from the button
            const dialog = $('#$ModalID');
            
            
            //the modal dialog contains a magic select, it will not be rendered correctly since it is hidden on page load
            //call each magic selects resize function to render them again, when the dialog is displayed
            dialog.on('shown.bs.modal', function () {
              for ( var ms in window['MagicSelects'] )
                window['MagicSelects'][ms].resize();
            });
            
          }
        </script>
  ";

  return $form;

}

/**
 * subscriber audience create form submit
 */
function klicktipp_subscriber_audience_create_form_submit($form, &$form_state) {

  $UserID = $form_state['values']['UserID'];

  // if a free value (new name) is entered, the magic select returns the entered value
  // if an existing name is selected, the magic select returns the SeqNo from the options
  $nameOrSeqNo = $form_state['values']['Name'];

  $existingAudiences = VarSubscriberAudience::RetrieveAudiencesAsOptionsArray($UserID);

  $data = [
    'SearchMode' => $form_state['values']['SearchSettings']['SearchMode'],
    'EmailSearch' => $form_state['values']['SearchSettings']['EmailSearch'],
    'SMSSearch' => $form_state['values']['SearchSettings']['SMSSearch'],
    'OpTaggedWith' => $form_state['values']['SearchSettings']['OpTaggedWith'],
    'TaggedWith' => $form_state['values']['SearchSettings']['TaggedWith'],
    'OpNotTaggedWith' => $form_state['values']['SearchSettings']['OpNotTaggedWith'],
    'NotTaggedWith' => $form_state['values']['SearchSettings']['NotTaggedWith'],
    'UseCustomField' => $form_state['values']['SearchSettings']['UseCustomField'],
    'CustomFields' => $form_state['values']['SearchSettings']['CustomFields'],
    'FormValue_Fields' => $form_state['values']['SearchSettings']['FormValue_Fields'],
  ];

  if (!empty($existingAudiences[$nameOrSeqNo])) {
    $seqNo = VarSubscriberAudience::UpdateAudience($UserID, $nameOrSeqNo, $data);
    if ($seqNo) {
      drupal_set_message(t("SubscriberAudience::The subscriber audience was successfully updated."));
      $form_state['values']['SearchSettings']['SubscriberAudiences'] = $seqNo;
    }
    else {
      drupal_set_message(t("SubscriberAudience::The subscriber audience could not be updated."), 'error');
    }
  }
  else {
    $seqNo = VarSubscriberAudience::CreateAudience($UserID, $form_state['values']['Name'], $data);
    if ($seqNo) {
      drupal_set_message(t("SubscriberAudience::The subscriber audience was successfully created."));
      $form_state['values']['SearchSettings']['SubscriberAudiences'] = $seqNo;
    }
    else {
      drupal_set_message(t("SubscriberAudience::The subscriber audience could not be created."), 'error');
    }
  }

  $_SESSION['subscriber_search_filter'] = $form_state['values']['SearchSettings'];
  klicktipp_set_redirect($form_state, "subscribers/$UserID");
}

/**
 * subscriber audience edit form
 */
function klicktipp_subscriber_audience_edit_form($form, $form_state, $account, $audienceID, $audiences) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $UserID = $account->uid;

  $Title = t('Update subscriber audience');

  $Message = t("Update the name of your audience.");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscriber_audience_edit_form';
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message, '', 'modal-info');

  $form['ModalContent']['Name'] = array(
    '#type' => 'textfield',
    '#title' => t('Subscriber audience name'),
    '#default_value' => (empty($audienceID)) ? "" : $audiences[$audienceID],
    '#weight' => 2.5,
    '#required' => TRUE,
  );

  //alter the standard delete button to show the number of subscribers to be deleted
  $SubmitButtonText = t("Save");
  $form['ModalButtons']['Submit']['#value'] = $SubmitButtonText;
  $form['ModalButtons']['Submit']['#theme'] = 'klicktipp_check_submit';

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );
  $form['AudienceID'] = array(
    '#type' => 'value',
    '#value' => $audienceID,
  );

  // write search into form to prevent problems when user has multiple tabs open
  // and starts new searches while a modal dialog is still open
  $form['SearchSettings'] = array(
    '#type' => 'value',
    '#value' => $_SESSION['subscriber_search_filter'],
  );

  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";

  return $form;

}

/**
 * subscriber audience edit form submit
 */
function klicktipp_subscriber_audience_edit_form_submit($form, &$form_state) {

  $UserID = $form_state['values']['UserID'];
  $AudienceID = $form_state['values']['AudienceID'];
  if (VarSubscriberAudience::UpdateAudienceName($UserID, $AudienceID, $form_state['values']['Name'])) {
    drupal_set_message(t("The subscriber audience was successfully updated."));
  }

  $_SESSION['subscriber_search_filter'] = $form_state['values']['SearchSettings'];
  klicktipp_set_redirect($form_state, "subscribers/$UserID");
}

/**
 * subscriber audience delete form
 */
function klicktipp_subscriber_audience_delete_form($form, $form_state, $account, $audienceID) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $UserID = $account->uid;

  $Title = t('Delete subscriber audience');

  $Message = t("Are you sure to delete the selected audience?");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscriber_audience_delete_form';
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message);

  //alter the standard delete button to show the number of subscribers to be deleted
  $SubmitButtonText = t("Delete");
  $form['ModalButtons']['Submit']['#value'] = $SubmitButtonText;

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );
  $form['AudienceID'] = array(
    '#type' => 'value',
    '#value' => $audienceID,
  );

  // write search into form to prevent problems when user has multiple tabs open
  // and starts new searches while a modal dialog is still open
  $form['SearchSettings'] = array(
    '#type' => 'value',
    '#value' => $_SESSION['subscriber_search_filter'],
  );

  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";

  return $form;

}

/**
 * subscriber audience delete form submit
 */
function klicktipp_subscriber_audience_delete_form_submit($form, &$form_state) {

  $UserID = $form_state['values']['UserID'];
  $AudienceID = $form_state['values']['AudienceID'];

  if (VarSubscriberAudience::DeleteAudience($UserID, $AudienceID)) {
    drupal_set_message(t("The subscriber audience was successfully deleted."));
  }

  // reset search
  unset($_SESSION['subscriber_search_filter']);
  klicktipp_set_redirect($form_state, "subscribers/$UserID");
}

function klicktipp_subscriber_fullcontact_social_profile($UserID, $SubscriberID) {
  //TODO DEV-2431 R2 - ReferenceID shall come from dialog
  $ReferenceID = 0;

  $form = array();
  $weight = 1;

  $FullContactField = CustomFields::GetCustomFieldData($UserID, $SubscriberID, CustomFields::$GlobalCustomFieldDefs['FullContact'], $ReferenceID);
  $FullContactField = drupal_json_decode($FullContactField);

  if (empty($FullContactField)) {
    return $form;
  }

  // convert the old person v2 api to person enrich api structure
  $FullContactField = CustomFields::ConvertFullContactData($FullContactField);

  $FullContactData = $FullContactField['data'];

  if (isset($FullContactData['details']['photos'][0]['value'])) {

    $image_url = $FullContactData['details']['photos'][0]['value'];

    $label = t('Profile image');

    $markup = "
      <label for='edit-subscriberimage'>$label</label>
      <div>
        <img id='edit-subscriberimage' class='subscriber-profile-image' src='$image_url' />
      </div>
    ";

    $form['Emailframe']['Responsive']['Right']['SubscriberImage'] = array(
      '#type' => 'markup',
      '#markup' => $markup,
      '#weight' => $weight++,
    );

  }

  if (isset($FullContactData['details']['profiles']) && !empty($FullContactData['details']['profiles'])) {

    $label = t('More profiles');

    $markup = "<label for='edit-socialicons'>$label</label><div id='edit-socialicons'>";

    $S3Path = klicktipp_get_assets_url("socialmedia/icons");

    foreach ($FullContactData['details']['profiles'] as $profile) {
      $markup .= "<a href='{$profile['url']}' target='_blank'><img class='social-media-icon' src='{$S3Path}/{$profile['service']}.png' /></a>";
    }
    $markup .= "</div>";

    $form['Emailframe']['Responsive']['Right']['SocialIcons'] = array(
      '#type' => 'markup',
      '#markup' => $markup,
      '#weight' => $weight++,
    );

  }

  return $form;

}

/**
 * Show the unsupscription feedback
 */
function klicktipp_subscriber_unsubscription_feedback_form($form, $form_state, $account, $PagerPage = 1, $PagerSize = 0) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Unsubscription Feedback');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $header = array(
    array(
      'data' => t("Date"),
      'field' => 'HistoryDate',
      'sort' => 'desc',
    ),
    array('data' => t("Subscriber")),
    array('data' => t("Feedback")),
  );

  //Pager
  $PagerLink = "contacts/$UserID/feedback";
  [$PagerPage, $PagerSize, $Offset] = klicktipp_validate_pager($PagerPage, $PagerSize);

  $ArraySort = klicktipp_get_tablesort($header, array('HistoryDate' => 'DESC'));
  $Feedbacks = Subscribers::ReadHistory($UserID, 0, $ArraySort, $Offset, $PagerSize,
    array(
      Subscribers::HISTORY_UNSUBSCRIPTION_FEEDBACK,
      Subscribers::HISTORY_SUBSCRIBER_FEEDBACK
    ));

  if (empty($Feedbacks) && $PagerPage > 1) {
    //if there are no feedbacks and we are not on pager page 1, redirect to page 1

    $PagerPage = 1;
    drupal_goto("$PagerLink/$PagerPage/$PagerSize");
    //this won't return

  }

  $weight = 0;
  $form = array();

  if (empty($Feedbacks)) {

    $form['NoFeedbacks'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("There is no feedback yet."),
    );

    return $form;
  }

  $form['Intro'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_info',
    '#value' => t("The following table shows all feedback given by your unsubscribed contacts."),
    '#weight' => $weight++,
  );

  $rows = array();

  foreach ($Feedbacks as $feedback) {

    $row = array(
      array('data' => Dates::formatDate(Dates::FORMAT_DMY, (int) $feedback['HistoryDate'])),
      l(Subscribers::DepunycodeEmailAddress($feedback['EmailAddress']), "subscriber/$UserID/edit/" . $feedback['RelSubscriberID']),
      str_replace("\n", "<br />", $feedback['Text']),
    );
    $rows[] = $row;

  }

  $FeedbackCount = kt_query("SELECT COUNT(*) FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND HistoryType IN (:type1, :type2)", array(
    ':RelOwnerUserID' => $UserID,
    ':type1' => Subscribers::HISTORY_UNSUBSCRIPTION_FEEDBACK,
    ':type2' => Subscribers::HISTORY_SUBSCRIBER_FEEDBACK,
  ))->fetchField();

  if (empty($Feedbacks)) {

    $form['empty'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("There is no feedback yet."),
      '#weight' => $weight++,
    );

  }
  else {

    $form['Table'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array(
        'header' => $header,
        'rows' => $rows,
        'attributes' => ['data-e2e-id' => 'table-subscriber-feedback']
      )),
      '#weight' => $weight++,
      '#suffix' => theme('klicktipp_table_pager', array(
        'element' => array(
          'pager_page' => $PagerPage,
          //current page
          'pager_size' => $PagerSize,
          //current page size
          'pager_hide_sizes' => FALSE,
          //hide page size badges
          'pager_total' => $FeedbackCount,
          //total entries in the table
          'pager_max_items' => KLICKTIPP_PAGER_MAX_PAGINATION_ITEM,
          //max pager items
          'pager_link' => $PagerLink,
          //link of pager badges and items, "/<CURRENT-PAGE>/<PAGE-SIZE>" will be added
          'pager_link_query' => (empty($_GET['order'])) ? array() : array(
            'order' => $_GET['order'],
            'sort' => $_GET['sort'],
          ),
          //build an additional link query string, possibly for table sort @see l()/url()
        )
      )),
    );

  }

  return $form;

}

/**
 * Show inbound sms
 */
function klicktipp_subscriber_inbound_sms_form($form, $form_state, $account, $PagerPage = 1, $PagerSize = 0) {

  $UserID = $account->uid;

  //set breadcrumb and page title
  $page_title = t('Contact SMS');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(), $page_title);

  $weight = 0;
  $form = array();

  if (!klicktipp_feature_access($account, 'access sms marketing')) {
    //show sms marketing upsell

    $form['NoAccess'] = array(
      '#type' => 'markup',
      '#value' => _klicktipp_get_content_include('klicktipp_content_include_sms_upsell'),
    );

    return $form;

  }

  $header = array(
    array(
      'data' => t("Date"),
      'field' => 'HistoryDate',
      'sort' => 'desc',
    ),
    array('data' => t("Subscriber")),
    array('data' => t("Feedback")),
  );

  //Pager
  $PagerLink = "contacts/$UserID/sms";
  [$PagerPage, $PagerSize, $Offset] = klicktipp_validate_pager($PagerPage, $PagerSize);

  $ArraySort = klicktipp_get_tablesort($header, array('HistoryDate' => 'DESC'));
  $InboundSMS = Subscribers::ReadHistory($UserID, 0, $ArraySort, $Offset, $PagerSize,
    array(Subscribers::HISTORY_SMS_INBOUND));

  if (empty($InboundSMS) && $PagerPage > 1) {
    //if there is no sms feedback and we are not on pager page 1, redirect to page 1

    $PagerPage = 1;
    drupal_goto("$PagerLink/$PagerPage/$PagerSize");
    //this won't return

  }

  if (empty($InboundSMS)) {

    $form['NoSMS'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("There are no SMS yet."),
    );

    return $form;
  }

  $form['Intro'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_info',
    '#value' => t("The following table shows all inbound SMS from your contacts."),
    '#weight' => $weight++,
  );

  $rows = array();

  foreach ($InboundSMS as $sms) {

    $row = array(
      array('data' => Dates::formatDate(Dates::FORMAT_DMY, (int) $sms['HistoryDate'])),
      l($sms['EmailAddress'], "subscriber/$UserID/edit/" . $sms['SubscriberID']),
      str_replace("\n", "<br />", $sms['Text']),
    );
    $rows[] = $row;

  }

  $SMSCount = kt_query("SELECT COUNT(*) FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND HistoryType = :type", array(
    ':RelOwnerUserID' => $UserID,
    ':type' => Subscribers::HISTORY_SMS_INBOUND,
  ))->fetchField();

  if (empty($SMSCount)) {

    $form['empty'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("There are no SMS yet."),
      '#weight' => $weight++,
    );

  }
  else {

    $form['Table'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array(
        'header' => $header,
        'rows' => $rows,
        'attributes' => ['data-e2e-id' => 'table-sms-feedback']
      )),
      '#weight' => $weight++,
      '#suffix' => theme('klicktipp_table_pager', array(
        'element' => array(
          'pager_page' => $PagerPage,
          //current page
          'pager_size' => $PagerSize,
          //current page size
          'pager_hide_sizes' => FALSE,
          //hide page size badges
          'pager_total' => $SMSCount,
          //total entries in the table
          'pager_max_items' => KLICKTIPP_PAGER_MAX_PAGINATION_ITEM,
          //max pager items
          'pager_link' => $PagerLink,
          //link of pager badges and items, "/<CURRENT-PAGE>/<PAGE-SIZE>" will be added
          'pager_link_query' => (empty($_GET['order'])) ? array() : array(
            'order' => $_GET['order'],
            'sort' => $_GET['sort'],
          ),
          //build an additional link query string, possibly for table sort @see l()/url()
        )
      )),
    );

  }

  return $form;

}

/**
 * bouncecheck form
 */
function klicktipp_subscriber_bouncecheck_form($form, $form_state, $account) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $UserID = $account->uid;

  $Title = t('Check bounce status');

  // check for the global user which may be a support user
  $hasAccess = user_access('access bounce check');
  if (!$hasAccess) {
    $Message = t("You do not have permission to access the bounce check");
  }
  else {
    $Message = t("A JavaScript could not be executed.");
  }

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscriber_bouncecheck_form';
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, $hasAccess, $Message, '', 'modal-info');

  if ($hasAccess) {
    //alter the standard delete button to show the number of subscribers to be deleted
    $form['ModalButtons']['Submit']['#theme'] = 'klicktipp_check_submit';
    $form['ModalButtons']['Submit']['#value'] = t('Check bounce status');
  }

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  // write search into form to prevent problems when user has multiple tabs open
  // and starts new searches while a modal dialog is still open
  $form['SearchSettings'] = array(
    '#type' => 'value',
    '#value' => $_SESSION['subscriber_search_filter'],
  );

  //SubscriberIDs of the selected checkboxes in the search result table
  //ID of this element is needed in the JavaScript
  $form['SelectedCheckboxesBounceCheck'] = array(
    '#type' => 'hidden',
    '#default_value' => '', //default: user did not select anything
  );

  //when the search form is displayed it contains the search result for the
  //current search session (a search session is generated when the search button is clicked)
  //the javascript of this modal will set its session value to this value of the search session
  //onSubmit (PHP) this session must match $_SESSION['subscriber_search_filter']['Session']
  $form['SearchSession'] = array(
    '#type' => 'hidden',
    '#default_value' => '',
    '#attributes' => array('id' => 'search-session-check-bounce')
  );

  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";
  $form['#attributes']['data-event-load-args'] = 'SubscriberSearchModalBounceCheck';

  //implement the onBeforeShow JavaScript callback
  //the form #suffix already contains HTML -> append the script
  $form['#suffix'] .= "
    <script type='text/javascript'>

      window['SubscriberSearchModalBounceCheck'] = function ( element ) {

        //get the modal container ID from the button
        var dialog = $('#$ModalID');

        //set the search session
        dialog.find('#search-session-check-bounce').val($('#search-session').val());

        var DisplayCount = 0;

        var SelectedCheckboxes = $('.edit-SelectedCheckboxesBounceCheck');

        //make sure the submit button is visible
        dialog.find('input[name=op]').show();

        var Selection = $('.subscriber-select:checked');
        var MasterSelect = $('.subscriber-master-select:checked').length;

        if ( MasterSelect == 1 ) {
          //apply to complete search result

          SelectedCheckboxes.val('" . CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED . "');

          DisplayCount = parseInt($('#SubscriberCountSearch').text());

        }
        else if ( Selection.length ) {
          var selected = [];
          Selection.each(function () {
            selected.push($(this).val());
          });
          SelectedCheckboxes.val(selected.join(','));

          DisplayCount = Selection.length;

        }
        else {
          // no selection, hide submit button, only show message

          SelectedCheckboxes.val('');

          var Message = dialog.find('p.modal-message');
          Message.html( '" . t('To apply this action you need to select at least 1 contact.') . "');

          //hide the submit button
          dialog.find('input[name=op]').hide();

          return;

        }


        if ( " . (($hasAccess) ? 1 : 0) . " ) {
          var singular = '" . t('Do you want to check the bounce status of 1 contact?') . "';
          var plural = '" . t('Do you want to check the bounce status of %count contacts?') . "'.replace('%count', DisplayCount);
          var Message = dialog.find('p.modal-message');
          Message.html( (DisplayCount > 1 ) ? plural : singular );
        }
      }

    </script>
  ";

  return $form;
}

/**
 * bouncecheck form submit
 */
function klicktipp_subscriber_bouncecheck_form_submit($form, &$form_state) {

  $UserID = $form_state['values']['UserID'];
  $session = $form_state['values']['SearchSession'];
  $searchMaskSession = $_SESSION['subscriber_search_filter']['Session'];

  if (empty($session) || empty($searchMaskSession) || $session != $searchMaskSession) {
    drupal_set_message(t('The current search session has expired. Please perform your search again.'), 'error');
    return;
  }

  $ArraySubscriptions = array();
  if (empty($form_state['values']['SelectedCheckboxesBounceCheck'])) {
    // no search results

    //TODO: in none Bootstrap themes we get here because this confirm form is under the search form and has no access to the search result checkboxes

    Errors::unexpected("Error: Modal bounce check without search results (UserID: !uid)", array('!uid' => $UserID), TRUE);

  }
  else {

    if ($form_state['values']['SelectedCheckboxesBounceCheck'] == CONTROLLER_SUBSCRIBER_SEARCH_MODAL_ALL_SELECTED) {
      // user selected the masterselect, tag the entire search result
      $ArraySubscriptions = klicktipp_subscriber_get_subscriptions($UserID, $form_state['values']['SearchSettings']);
    }
    else {
      //user selected individual subscribers
      $selection = explode(',', $form_state['values']['SelectedCheckboxesBounceCheck']);
      foreach ($selection as $subscription) {
        $s = SubscribersSearch::DecodeSubscriptionKey($subscription);
        $ArraySubscriptions[] = [
          'SubscriberID' => $s[0],
          'SubscriptionType' => $s[1],
          'ContactInfo' => $s[2],
          'ReferenceID' => $s[3],
        ];
      }
    }

    if (!empty($ArraySubscriptions)) {

      // for support: if the executing user (global $user) has the "omit" permission, the bounce check is omitted
      $OmitBounceCheck = user_access('omit bounce check');

      // collect data
      $CheckedSubscriptions = array();
      $BouncedSubscriptions = 0;
      $DataArray = array();

      foreach ($ArraySubscriptions as $s) {

        if ($s['SubscriptionType'] != Subscription::SUBSCRIPTIONTYPE_EMAIL) {
          continue;
        }

        if (in_array($s['ContactInfo'], $CheckedSubscriptions)) {
          //the selection could contain the same subscription with multiple reference ids
          continue;
        }

        $Subscription = Subscription::RetrieveSubscriptionByContactInfo($UserID, $s['ContactInfo'], Subscription::SUBSCRIPTIONTYPE_EMAIL);

        if (!$Subscription) {
          continue;
        }

        if (!in_array($Subscription['BounceType'], array(
          Subscribers::BOUNCETYPE_HARD,
          Subscribers::BOUNCETYPE_SOFT,
          Subscribers::BOUNCETYPE_SPAM,
        ))) {
          continue;
        }

          // prepare data for display
        $BouncedSubscriptions++;
        $CheckedSubscriptions[] = $s['ContactInfo'];

        // prepare data for batch queue
        $DataArray[] = array(
          'UserID' => $UserID,
          'SubscriberID' => $Subscription['RelSubscriberID'],
          'EmailAddress' => $s['ContactInfo'],
          'OmitBounceCheck' => $OmitBounceCheck,
          'NotifyUser' => TRUE,
        );

      }

      if (!$BouncedSubscriptions) {
        // none
        $message = t("There are no contacts to check.");

      }
      else {
        // in background
        $message = t("The bounce status for %count contacts will be checked in the background.", array('%count' => $BouncedSubscriptions));
        ProcessLog::CreateSlicedQueueItems('bouncecheck', $DataArray, 100);
      }

      drupal_set_message($message);

    }
    else {
      //could only occur when explode(',', $form_state['values']['SelectedCheckboxesBounceCheck']); fails, probably JavaScript error
      Errors::unexpected("Error: Modal bounce check without SubscriberIDs (UserID: !uid)", array('!uid' => $UserID), TRUE);
    }

  }

  $_SESSION['subscriber_search_filter'] = $form_state['values']['SearchSettings'];
  klicktipp_set_redirect($form_state, "subscribers/$UserID");

}

function klicktipp_subscriber_overview_form($form, $form_state, $account, $SubscriberID) {
  $ReferenceID = 0;
  $SubscriberID = (int)$SubscriberID;
  $UserID = $account->uid;

  $pageTitle = t('Overview of contact !subscriberID', ['!subscriberID' =>  $SubscriberID]);
  klicktipp_set_title($pageTitle);
  klicktipp_set_breadcrumb(array(
    l(t('Search Contacts'), "subscribers/$UserID"),
  ), $pageTitle);

  $weight = 0;

  if (user_access('access duplicates management')) {
    klicktipp_show_subscriber_duplicate_hint($UserID, $SubscriberID);
  }

  $form['#validate'][] = 'klicktipp_subscriber_edit_form_validate';
  $form['#submit'][] = 'klicktipp_subscriber_edit_form_submit';

  $customFields = CustomFields::RetrieveCustomFields($UserID);

  $form['ContactRecord'] = array(
    '#type' => 'fieldset',
    '#title' => t("Contact record"),
    '#weight' => $weight++,
    '#attributes' => ['id' => 'new-subscription-form']
  );

  $placeCustomField = function ($fieldId, &$form) use ($customFields, $UserID, $SubscriberID, $ReferenceID, &$weight) {
    $customField =  $customFields[$fieldId];
    $value = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $customField, $ReferenceID, $fieldId);

    $form['CustomField' . $fieldId . '_' . $ReferenceID] = array(
      '#type' => 'item',
      '#title' => CustomFields::GetFieldName($customField),
      '#value' => CustomFields::GenerateCustomFieldHTMLCode($customField, $value, '', FALSE, TRUE, $ReferenceID),
      '#weight' => $weight++,
    );
  };

  $responsiveBlock = array(
    '#type' => 'klicktipp_block',
    '#attributes' => array('class' => array('col-md-6')),
  );

  // responsive blocks allows to play fields (Last name + FirstName, CompanyName + City) side by side
  $form['ContactRecord']['Responsive1'] = $form['ContactRecord']['Responsive2'] = array(
    '#type' => 'klicktipp_grid_row',
    'Left' => $responsiveBlock,
    'Right' => $responsiveBlock,
    '#weight' => $weight++,
  );

  $placeCustomField('FirstName', $form['ContactRecord']['Responsive1']['Left']);
  $placeCustomField('LastName', $form['ContactRecord']['Responsive1']['Right']);

  // this provisional implementation should be replace by activity score algorithm as far as one is implemented
  $retrieveMostActiveSubscription = function ($subscriptionType) use ($UserID, $SubscriberID) {
    $defaultSubscriptions = Subscription::RetrieveDefaultSubscriptions($UserID, $SubscriberID, $subscriptionType);
    // prefer default reference (0)
    ksort($defaultSubscriptions);
    return reset($defaultSubscriptions);
  };

  $subscription = $retrieveMostActiveSubscription(Subscription::SUBSCRIPTIONTYPE_EMAIL) ?:
      $retrieveMostActiveSubscription(Subscription::SUBSCRIPTIONTYPE_SMS);

  switch($subscription['SubscriptionType'] ?? Subscription::SUBSCRIPTIONTYPE_NONE) {
    case Subscription::SUBSCRIPTIONTYPE_EMAIL:
      $title = t('E-mail address');
      $contactInfo = Subscribers::DepunycodeEmailAddress($subscription['ContactInfo']);
      break;
    case Subscription::SUBSCRIPTIONTYPE_SMS:
      $title = t('SMS Phone number');
      $contactInfo = $subscription['ContactInfo'];
      break;
    default:
      $title = '';
      $contactInfo = t('No ContactInfo');
  }

  // Most active
  $form['ContactRecord']['MostActiveSubscription'] = array(
    '#type' => 'textfield',
    '#title' => $title,
    '#value' => $contactInfo,
    '#weight' => $weight++,
    '#disabled' => true,
    '#attributes' => [
      'style' => 'width: 100%'
    ]
  );

  $form['ContactRecord']['Responsive2']['#weight'] = $weight++;

  $placeCustomField('CompanyName', $form['ContactRecord']['Responsive2']['Left']);
  $placeCustomField('City', $form['ContactRecord']['Responsive2']['Right']);

  // --- Tagging ---

  $ArrayManualTags = Tag::RetrieveManualTags($UserID);
  $SingleValueTagNames = [];
  $MultiValueTagNames = [];
  foreach ($ArrayManualTags as $id => $Tag) {
    if ($Tag['Category'] == Tag::CATEGORY_SMARTLINK) {
      $Tag['TagName'] = SmartLink::decorateName($Tag['TagName']);
    }
    if (empty($Tag['MultiValue'])) {
      $SingleValueTagNames[$id] = $Tag['TagName'];
    } else {
      $MultiValueTagNames[$id] = $Tag['TagName'];
    }
  }

  $referenceDisplayNames = Reference::getSubscriberRelatedDisplayNames($UserID, $SubscriberID);
  $firstReferenceID = array_key_first($referenceDisplayNames);
  $hasReferences = count($referenceDisplayNames) > 1 || $firstReferenceID != 0;

  $form['hasReferences'] = [
    '#type' => 'value',
    '#value' => $hasReferences
  ];

  $form['Tags'] = array(
    '#type' => 'fieldset',
    '#title' => t('Tags'),
    '#weight' => $weight++,
    '#attributes' => ['id' => 'new-subscription-form']
  );

  $form['Tags']['AssignTags'] = [
    '#tree' => TRUE,
  ];

  if (!$hasReferences) {
    //the subscriber has no references
    //display his single and multi tags in 1 magic select
    $existingTaggings = array_intersect_key(Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, 0), $ArrayManualTags);
    $existingTaggings = array_keys($existingTaggings);

    $form['ExistingTaggings'] = [
      '#type' => 'value',
      '#value' => ['single' => $existingTaggings],
    ];

    $form['Tags']['AssignTags']['single'] = [
      '#type' => 'textfield',
      '#default_value' => $existingTaggings,
      // '#title' => t("Tags"),
      '#weight' => $weight++,
      '#magic_select' => array(
        'autocomplete' => '',
        'options' => $SingleValueTagNames + $MultiValueTagNames,
        'multiple' => TRUE,
        'free_entries' => TRUE,
        'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
        'allow_numeric_only' => FALSE,
        'numeric_only_message' => t('Tags cannot contain only numbers.'),
        'form_post' => $form_state['input']['AssignTags']['single'],
        //catch free entries on form validation error
      ),
      '#theme' => 'klicktipp_magic_select',
    ];

  }
  else {

    $emailSubscriptions = Subscription::RetrieveDefaultSubscriptions($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL);
    $smsSubscriptions = Subscription::RetrieveDefaultSubscriptions($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_SMS);

    $taggingsByRef = [
      'single' => []
    ];

    $ContactInfoRefIDMap = [];
    $showReferences = FALSE;

    if (!empty($emailSubscriptions)) {

      foreach($emailSubscriptions as $sub) {

        $refID = $sub['ReferenceID'];

        if (!isset($taggingsByRef[$refID])) {

          $taggingsByRef[$refID] = [];

          //since we retrieved the default subscriptions, we can use this email address for display
          if (empty($ContactInfoRefIDMap[$sub['ContactInfo']])) {
            $ContactInfoRefIDMap[$sub['ContactInfo']] = [$refID];
          }
          else {
            $ContactInfoRefIDMap[$sub['ContactInfo']][] = $refID;
            //we have at least 1 email address with multiple references, show detailed table
            $showReferences = TRUE;
          }

          $refTaggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $refID);
          if (!empty($refTaggings)) {
            foreach($refTaggings as $t) {
              $tid = $t['RelTagID'];
              $ArrayTag = $ArrayManualTags[$tid];
              if ($ArrayTag) {
                //tagging is manual tag
                $key = ($ArrayTag['MultiValue']) ? $refID : 'single';
                $taggingsByRef[$key][$tid] = $tid;
              }
            }
          }
        }

      }
    }

    if (!empty($smsSubscriptions)) {

      foreach($smsSubscriptions as $sub) {

        $refID = $sub['ReferenceID'];

        if (!isset($taggingsByRef[$refID])) {
          //Note: if this reference also has an email address, we don't get here

          $taggingsByRef[$refID] = [];

          //since we retrieved the default subscriptions, we can use this sms number for display
          if (empty($ContactInfoRefIDMap[$sub['ContactInfo']])) {
            $ContactInfoRefIDMap[$sub['ContactInfo']] = [$refID];
          }
          else {
            $ContactInfoRefIDMap[$sub['ContactInfo']][] = $refID;
            //we have at least 1 sms number with multiple references, show detailed table
            $showReferences = TRUE;
          }

          $refTaggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $refID);
          if (!empty($refTaggings)) {
            foreach($refTaggings as $t) {
              $tid = $t['RelTagID'];
              $ArrayTag = $ArrayManualTags[$tid];
              if ($ArrayTag) {
                //tagging is manual tag
                $key = ($ArrayTag['MultiValue']) ? $refID : 'single';
                $taggingsByRef[$key][$tid] = $tid;
              }
            }
          }
        }

      }
    }

    $form['ExistingTaggings'] = [
      '#type' => 'value',
      '#value' => $taggingsByRef,
    ];

    if ($showReferences) {
      $header = [
        ['data' => t('contact::overview::tagtable::Valid for')],
        ['data' => t('contact::overview::tagtable::Reference')],
        ['data' => t('contact::overview::tagtable::Tags')],
      ];
    }
    else {
      $header = [
        ['data' => t('contact::overview::tagtable::Valid for')],
        ['data' => t('contact::overview::tagtable::Tags')],
      ];
    }

    $rows = [];

    // add first row with magic select for the single value tags

    $elementID = "ms-single";

    $form['Tags']['AssignTags'][$elementID] = array(
      '#id' => $elementID, //needs to be set for drupal_render() to render magic select
      '#name' => "AssignTags[$elementID]", //needed to get form values due to drupal_render()
      '#type' => 'textfield',
      '#default_value' => $taggingsByRef['single'],
      '#weight' => $weight++,
      '#magic_select' => array(
        'autocomplete' => '',
        'options' => $SingleValueTagNames,
        'multiple' => TRUE,
        'free_entries' => TRUE,
        'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
        'allow_numeric_only' => FALSE,
        'numeric_only_message' => t('Tags cannot contain only numbers.'),
        'form_post' => $form_state['input']['AssignTags'][$elementID],
        //catch free entries on form validation error
      ),
      '#attributes' => [

      ],
      '#theme' => 'klicktipp_magic_select',
    );

    $row = [
      ['data' => t('contact::overview::tagtable::Valid for all')]
    ];

    if ($showReferences) {
      $row[] = ['data' => t('contact::overview::tagtable::All references')];
    }

    $row[] = ['data' => drupal_render($form['Tags']['AssignTags'][$elementID])];
    $rows[] = $row;


    $msCount = 1;
    foreach($ContactInfoRefIDMap as $validFor => $refIDs) {

      $row = [
        ['data' => $validFor, 'rowspan' => count($refIDs)]
      ];

      if (count($refIDs) > 1) {
        //at least 1 default subscription has more than 1 reference
        //show all references and their taggings

        foreach ($refIDs as $refID) {

          $row[] = ['data' => $referenceDisplayNames[$refID]];

          $elementID = "ms$msCount-$refID";
          $msCount++;

          $form['Tags']['AssignTags'][$elementID] = array(
            '#id' => $elementID, //needs to be set for drupal_render() to render magic select
            '#name' => "AssignTags[$elementID]", //needed to get form values due to drupal_render()
            '#type' => 'textfield',
            '#default_value' => $taggingsByRef[$refID],
            '#weight' => $weight++,
            '#magic_select' => array(
              'autocomplete' => '',
              'options' => $MultiValueTagNames,
              'multiple' => TRUE,
              'free_entries' => TRUE,
              'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
              'allow_numeric_only' => FALSE,
              'numeric_only_message' => t('Tags cannot contain only numbers.'),
              'form_post' => $form_state['input']['AssignTags'][$elementID],
              //catch free entries on form validation error
            ),
            '#theme' => 'klicktipp_magic_select',
          );

          $row[] = ['data' => drupal_render($form['Tags']['AssignTags'][$elementID])];
          $rows[] = $row;

          //new row for next refID
          //Note: there is no value (['data' => ...]) because we set rowspan above
          $row = [];

        }

      }
      else {
        //all default subscriptions have only 1 reference
        //no need to show the reference column

        $refID = $refIDs[0];

        $elementID = "ms1-$refID";

        $form['Tags']['AssignTags'][$elementID] = array(
          '#id' => $elementID, //needs to be set for drupal_render() to render magic select
          '#name' => "AssignTags[$elementID]", //needed to get form values due to drupal_render()
          '#type' => 'textfield',
          '#default_value' => $taggingsByRef[$refID],
          '#weight' => $weight++,
          '#magic_select' => array(
            'autocomplete' => '',
            'options' => $MultiValueTagNames,
            'multiple' => TRUE,
            'free_entries' => TRUE,
            'free_entries_message' => t('Non-existing tags will automatically be created when sending the form.'),
            'allow_numeric_only' => FALSE,
            'numeric_only_message' => t('Tags cannot contain only numbers.'),
            'form_post' => $form_state['input']['AssignTags'][$elementID],
            //catch free entries on form validation error
          ),
          '#theme' => 'klicktipp_magic_select',
        );

        $row[] = ['data' => drupal_render($form['Tags']['AssignTags'][$elementID])];
        $rows[] = $row;

      }

    }

    $form['Tags']['Overview'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array(
        'header' => $header,
        'rows' => $rows,
        'attributes' => ['data-e2e-id' => 'table-subscriber-edit-tags']
      )),
      '#weight' => $weight++,
    );

  }

  //Note: at least UserID is used in submit
  $form['data'] = array(
    '#type' => 'value',
    '#value' => array(
      'UserID' => $UserID,
      'SubscriberID' => $SubscriberID,
      'SubscriberTags' => [],
      'EmailDefaults' => [],
      'SmsDefaults' => []
    ),
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t(/*ignore*/CONTROLLER_SUBSCRIBER_SAVE_BUTTON_TEXT),
    '#weight' => $weight++,
  );

  //download vCard
  $form['buttons']['vcard'] = array(
    '#theme' => 'klicktipp_preview_button',
    '#title' => t('Download vCard'),
    '#value' => "subscriber/$UserID/vcard/$SubscriberID/$ReferenceID",
    '#weight' => $weight++,
  );

  $form['buttons']['merge'] = array(
    '#theme' => 'klicktipp_merge_dialog',
    '#title' => t('SubscriberOverview::Merge with another contact'),
    '#value' => "subscriber/$UserID/merge/$SubscriberID",
    '#weight' => $weight++,
  );

  //store this subscriber in the session for the delete/unsubscribe confirm dialog
  $_SESSION['subscriber_search_selection'] = array($SubscriberID);

  if (klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_ADMINISTRATOR)) {
    $form['buttons']['ModalDeleteConfirmTrigger'] = array(
      '#theme' => 'klicktipp_delete_modal_button',
      '#title' => t('Delete Contact'),
      '#value' => 'klicktipp_subscriber_edit_delete_confirm_form',
      '#weight' => $weight++,
      '#external' => FALSE,
      '#modal_confirm' => drupal_get_form('klicktipp_subscriber_edit_delete_confirm_form', $account, $SubscriberID),
    );
  }

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "subscribers/$UserID",
    '#weight' => $weight++,
  );

  return $form;
}

/**
 * subscriber edit form
 */
function klicktipp_subscriber_edit_form($form, $form_state, $account, $SubscriberID, $fieldCategory = 'all') {
  $UserID = $account->uid;
  $baseUrl = "/subscriber/$UserID/edit/$SubscriberID/fields/";

  $customFieldCategories = CustomFields::RetrieveCustomFieldsCategories($UserID);
  $categoryNames = $customFieldCategories['CategoryNames'];
  $fieldCategories = [
      'all' => t('- All -'),
      'global' => t('Global fields'),
      'custom' => t('Custom fields'),
    ]
    + $categoryNames
    +
    [
      'contact-infos' => t('Contact infos'),
      'contact-key' => t('filter-category::Contact-Key')
    ];

  asort($fieldCategories, SORT_LOCALE_STRING);

  if (!isset($fieldCategories[$fieldCategory])) {
    $fieldCategory = 'all';
  }

  $referenceDisplayNames = Reference::getSubscriberRelatedDisplayNames($UserID, $SubscriberID);
  $firstReferenceID = array_key_first($referenceDisplayNames);
  $displayReferences = count($referenceDisplayNames) > 1 || $firstReferenceID != 0;

  $globalFields = [];
  $customFields = [];

  $allFields = $customFieldCategories['CategoryFields'][$fieldCategory] ?? $customFieldCategories['AllCustomFields'];

  foreach ($allFields as $key => $field) {
    if (empty($field['IsGlobal'])) {
      $customFields[$key] = $field;
    } else {
      $globalFields[$key] = $field;
    }
  }

  //set breadcrumb and page title
  $page_title = t('Fields of contact !subscriberID', array('!subscriberID' => $SubscriberID));
  klicktipp_set_title($page_title);

  klicktipp_set_breadcrumb(array(
    l(t('Search Contacts'), "subscribers/$UserID"),
    l($SubscriberID, "subscriber/$UserID/edit/$SubscriberID"),
  ), $page_title);

  $weight = 1;

  $form = array();

  $form['FieldCategory'] = array(
    '#type' => 'select',
    '#default_value' => $fieldCategory,
    '#options' => $fieldCategories,
    '#title' => t("Field category"),
    '#attributes' => ['onchange' => 'window.location.href="' . $baseUrl . '" + encodeURIComponent(this.value);']
  );

  $form['StartFieldSection'] = array(
    '#type' => 'markup',
    '#value' => '<table class="table table-hover sticky-enabled tableheader-processed sticky-table">' .
      '<thead><tr><th>' . t('Field') .'</th><th>' . ($displayReferences ? t('context') : '') . '</th><th>' . t('Value') .'</th></tr></thead>' .
      '<tbody>'
    ,
    '#weight' => $weight++,
  );

  // assignment of default email to references

  $emailDefaults = $smsDefaults = [];

  $hideDSaveButton = $fieldCategory == 'contact-infos'; // note: can be change in block below
  if (in_array($fieldCategory, ['all', 'contact-infos'])) {

    $emailSubscriptions = Subscription::RetrieveSubscriptionsByType($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL);
    $smsSubscriptions = Subscription::RetrieveSubscriptionsByType($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_SMS);
    $emailDefaults = Subscription::RetrievePrimaryContactsPerReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL);
    $smsDefaults = Subscription::RetrievePrimaryContactsPerReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_SMS);

    foreach($referenceDisplayNames as $referenceID => $displayName) {
      $prefix = '<tr>';
      if ($referenceID == $firstReferenceID) {
        $prefix .= '<td rowspan="' . count($referenceDisplayNames) . '">' . t('Email Address') . '</td>';
      }
      $prefix .= '<td>' . ($displayReferences ? $displayName : '') . '</td>';
      $prefix .= '<td>';
      $form['DefaultEmail' . $referenceID] = array(
        '#prefix' => $prefix,
        '#suffix' => '</td></tr>',
        '#type' => 'textfield',
        '#default_value' => Subscribers::DepunycodeEmailAddress(key($emailSubscriptions)),
        '#weight' => $weight++,
        '#disabled' => true
      );
      if (count($emailSubscriptions) < 1) {
        $form['DefaultEmail' . $referenceID]['#type'] = 'markup';
        $form['DefaultEmail' . $referenceID]['#value'] = t('No subscription');
      } elseif (count($emailSubscriptions) > 1 || !isset($emailDefaults[$referenceID])) {
        $options = [];
        if (isset($emailDefaults[$referenceID])) {
          $defaultValue = $emailDefaults[$referenceID];
        } else {
          $options[''] = '';
          $defaultValue = '';
        }
        $options += array_combine(array_keys($emailSubscriptions), array_keys($emailSubscriptions));
        $options = array_map([Subscribers::class, 'DepunycodeEmailAddress'], $options);
        $form['DefaultEmail' . $referenceID]['#type'] = 'select';
        $form['DefaultEmail' . $referenceID]['#options'] = $options;
        $form['DefaultEmail' . $referenceID]['#default_value'] = $defaultValue;
        $form['DefaultEmail' . $referenceID]['#disabled'] = $hideDSaveButton = false;
      }
    }

    // assignment of default phone numbers to references
    foreach($referenceDisplayNames as $referenceID => $displayName) {
      $prefix = '<tr>';
      if ($referenceID == $firstReferenceID) {
        $prefix .= '<td rowspan="' . count($referenceDisplayNames) . '">' . t('Phone Number') . '</td>';
      }
      $prefix .= '<td>' . ($displayReferences ? $displayName : '') . '</td>';
      $prefix .= '<td>';
      $form['DefaultSms' . $referenceID] = array(
        '#prefix' => $prefix,
        '#suffix' => '</td></tr>',
        '#type' => 'textfield',
        '#default_value' => key($smsSubscriptions),
        '#weight' => $weight++,
        '#disabled' => true
      );
      if (count($smsSubscriptions) < 1) {
        $form['DefaultSms' . $referenceID]['#type'] = 'markup';
        $form['DefaultSms' . $referenceID]['#value'] = t('No subscription');
      } elseif (count($smsSubscriptions) > 1 || !isset($smsDefaults[$referenceID])) {
        $options = [];
        if (isset($smsDefaults[$referenceID])) {
          $defaultValue = $smsDefaults[$referenceID];
        } else {
          $options[''] = '';
          $defaultValue = '';
        }
        $options += array_combine(array_keys($smsSubscriptions), array_keys($smsSubscriptions));

        $form['DefaultSms' . $referenceID]['#type'] = 'select';
        $form['DefaultSms' . $referenceID]['#options'] = $options;
        $form['DefaultSms' . $referenceID]['#default_value'] = $defaultValue;
        $form['DefaultSms' . $referenceID]['#disabled'] = $hideDSaveButton = false;
      }
    }
  }

  if (in_array($fieldCategory, ['all', 'contact-infos', 'contact-key'])) {
    // assignment of default phone numbers to references
    foreach ($referenceDisplayNames as $referenceID => $displayName) {

      $prefix = '';
      if ($referenceID == $firstReferenceID) {
        $quickhelp = theme(
          'klicktipp_quickhelp', ['element' => ['#quickhelp' => 'contact-key', '#title' => t('filter-category::Contact-Key')]]
        );
        $prefix .= '<td class="quickhelp-inline" rowspan="' . count($referenceDisplayNames) . '">' . t('filter-category::Contact-Key') . $quickhelp . '</td>';
      }
      $prefix .= '<td>' . ($displayReferences ? $displayName : '') . '</td>';

      $form['SubscriberKey' . $referenceID] = [
        '#prefix' => '<tr>'. $prefix . '<td>',
        '#suffix' => '</td></tr>',
        '#type' => 'item',
        '#value' => Core::EncryptURL([
          'UserID' => $UserID,
          'SubscriberID' => $SubscriberID,
          'ReferenceID' => $referenceID
        ], 'subscriber_key'),
        '#weight' => $weight++,
      ];
    }
  }

  if (in_array($fieldCategory, ['all', 'global'])) {
    Libraries::include('subscriber.inc');
    $form = _klicktipp_subscriber_edit_customfields_form_elements($UserID, $SubscriberID, $referenceDisplayNames, $globalFields, $form, $weight);
  }
  if (!empty($customFields) && (in_array($fieldCategory, ['all', 'custom']) || isset($customFieldCategories['CategoryFields'][$fieldCategory]))) {
    Libraries::include('subscriber.inc');
    $form = _klicktipp_subscriber_edit_customfields_form_elements($UserID, $SubscriberID, $referenceDisplayNames, $customFields, $form, $weight);
  }

  $form['EndFieldSection'] = array(
    '#type' => 'markup',
    '#value' => '</tbody></table>',
    '#weight' => $weight++,
  );

  $form['data'] = array(
    '#type' => 'value',
    '#value' => array(
      'UserID' => $UserID,
      'SubscriberID' => $SubscriberID,
      'SubscriberTags' => [],
      'EmailDefaults' => $emailDefaults,
      'SmsDefaults' => $smsDefaults
    ),
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  if (!$hideDSaveButton) {
    $form['buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t(/*ignore*/CONTROLLER_SUBSCRIBER_SAVE_BUTTON_TEXT),
      '#weight' => $weight++,
    );
  }

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => ($fieldCategory == 'all') ? "subscribers/$UserID" : "subscriber/$UserID/edit/$SubscriberID/fields",
    '#weight' => $weight++,
  );

  return $form;
}

/**
 * subscriber edit form validate
 */
function klicktipp_subscriber_edit_form_validate($form, &$form_state) {
  if ($form_state['values']['op'] == t(/*ignore*/CONTROLLER_SUBSCRIBER_SAVE_BUTTON_TEXT)) {

    $UserID = $form_state['values']['data']['UserID'];

    foreach ($_POST['FormValue_Fields'] as $FormCustomFieldID => $Value) {

      $CustomFieldID = substr($FormCustomFieldID, 11, strlen($FormCustomFieldID));
      $CustomFieldID = preg_replace('/_refid_[0-9]{1,}$/', '', $CustomFieldID);

      $ArrayCustomField = CustomFields::RetrieveCustomField($CustomFieldID, $UserID);

      if ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_TIME) {
        $Value = implode(':', $Value);
      }
      elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
        // ValidateCustomFieldValue will check Y-m-d format
        $Value = date('Y-m-d', strtotime($Value));
      }
      elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
        // ValidateCustomFieldValue will check "Y-m-d H:i" format
        $Value = date('Y-m-d H:i', strtotime($Value[0] . ' ' . $Value[1] . ':' .  $Value[2]));
      }
      $Value = check_plain($Value);
      // values for fields of type date, time, and datetime must be valid as argument for strtotime()
      $Check = CustomFields::ValidateCustomFieldValue($ArrayCustomField, $Value, TRUE);

      if (!$Check[0]) {
        form_set_error($FormCustomFieldID, $ArrayCustomField['FieldName'] . ': ' . $Check[1]);
      }
    }
  }
}

/**
 * subscriber edit form submit
 */
function klicktipp_subscriber_edit_form_submit($form, &$form_state) {
  $referencesToTriggerAutomation = [0 => 0];

  Libraries::include('subscriber.inc');

  $UserID = $form_state['values']['data']['UserID'];
  $SubscriberID = $form_state['values']['data']['SubscriberID'];

  if ($form_state['values']['op'] == t(/*ignore*/CONTROLLER_SUBSCRIBER_SAVE_BUTTON_TEXT)) {

    // --------------------------------- TAGS ----------------------------------------------------------------

    if (isset($form_state['values']['AssignTags'])) {
      //the AssignTags form fields are present, so we are in the overview dialog
      //where the subscriber can be tagged

      $existingTagsByRef = $form_state['values']['ExistingTaggings'];
      //TRUE if user has more than just the default reference (0)
      $hasReferences = $form_state['values']['hasReferences'];

      foreach($form_state['values']['AssignTags'] as $msID => $selectedTags) {

        //make sure it is an array for array_diff()
        //if all tags are removed in the form, it's an empty string
        $selectedTags = $selectedTags ?: [];

        //get the referenceID from the MagicSelect ID 'ms{counter}-{refID}'
        $parts = explode('-', $msID);
        $refID = array_pop($parts);

        $existingTagIDs = $existingTagsByRef[$refID] ?? [];

        $assignTags = array_diff($selectedTags, $existingTagIDs);
        $removeTags = array_diff($existingTagIDs, $selectedTags);

        $tagForReference = $refID == 'single' ? 0 : $refID;

        if (!empty($assignTags)) {

          // if the subscriber has no additional references,
          // both multi and single value tags are in the magic select with refID = single
          // in this case, we create multi value tags
          // if he has additional references,
          // we create single value tags for refID single and multi value for all additional references
          $createMultiValue = !($hasReferences && $refID == 'single');

          // once tag is created as single value, we don't allow user to change it to multi value
          $TagData = [
            'AllowedToChange' => $createMultiValue ? 1 : 0
          ];

          //create tags from free entered values, returns FALSE if at least one tag could not be created, existing tags stay untouched
          $assignTagIDs = Tag::CreateManualTagByTagName($UserID, $assignTags, FALSE, NULL, $createMultiValue, $TagData);
          if (!$assignTagIDs) {
            //at least on tag could not be created, no redirect => stay in the form
            drupal_set_message(t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR), 'error');
            watchdog("kt-controller", "Error: CreateManualTagByTagName with (!tags) UserID: !userID", ['!tags' => implode(', ', $assignTagIDs), '!userID' => $UserID], WATCHDOG_ERROR);
            return;
          }

          foreach($assignTagIDs as $TagID) {
              Subscribers::TagSubscriber($UserID, $SubscriberID, $TagID, $tagForReference, TRUE);
              $referencesToTriggerAutomation[$tagForReference] = $tagForReference;
          }

        }

        if (!empty($removeTags)) {
          foreach ($removeTags as $TagID) {
            //untag subscriber
            Subscribers::UntagSubscriber($UserID, $SubscriberID, $tagForReference, $TagID);
            $referencesToTriggerAutomation[$tagForReference] = $tagForReference;
          }
        }

      }

    }

    // --------------------------------- EMAILS ----------------------------------------------------------------------
    $SpecifiedEmailDefaults =  klicktipp_group_form_values_by_reference_id('DefaultEmail', $form_state['values']);
    $StoredEmailDefaults = $form_state['values']['data']['EmailDefaults'];
    foreach ($SpecifiedEmailDefaults as $referenceID => $email) {
      if ((empty($StoredEmailDefaults[$referenceID]) && !empty($email)) || ($StoredEmailDefaults[$referenceID] != $email)) {
        Subscriber::setPrimarySubscription($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $email, $referenceID);
        $referencesToTriggerAutomation[$referenceID] = $referenceID;
      }
    }

    // ------------------------------------ SMS ---------------------------------------------------------------------
    $SpecifiedSmsDefaults =  klicktipp_group_form_values_by_reference_id('DefaultSms', $form_state['values']);
    $StoredSmsDefaults = $form_state['values']['data']['SmsDefaults'];
    foreach ($SpecifiedSmsDefaults as $referenceID => $sms) {
      if ((empty($StoredSmsDefaults[$referenceID]) && !empty($sms))  || ($StoredSmsDefaults[$referenceID] != $sms)) {
        Subscriber::setPrimarySubscription($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_SMS, $sms, $referenceID);
        $referencesToTriggerAutomation[$referenceID] = $referenceID;
      }
    }

    // ---------------------------------------- CUSTOM FIELD VALUES ---------------------------------------------------

    $parameters = _klicktipp_subscriber_edit_customfields_form_values($UserID);
    $parameters = klicktipp_group_custom_field_values_by_reference_id($parameters);
    $referencedValues = $parameters['referenced_values'];
    unset($parameters['referenced_values']);
    $firstReferenceID = key($referencedValues);

    $success = TRUE;
    foreach ($referencedValues as $referenceID => $valuesPerReference) {
      $params = array_merge(['ReferenceID' => $referenceID], $valuesPerReference);
      // make sure that context-independent parameters are update once
      if ($referenceID == $firstReferenceID) {
        $params = array_merge($parameters, $params);
      }
      $params['InstantTrigger'] = true;
      $array_return = Subscribers::UpdateSubscription($UserID, $SubscriberID, $params);
      if ($array_return['Success'] == FALSE) {
        $success = FALSE;
        drupal_set_message(t('Subscriber information is not valid for update.'), 'error');
        break;
      }
    }

    if ($success) {
      //Note: dummy email addresses contain the SubscriberID, display phone number if we have one
      drupal_set_message(t('Contact successfully updated.'));

    }

    foreach ($referencesToTriggerAutomation as $referenceID) {
      TransactionEmails::RegisterAutomations($UserID, $SubscriberID, $referenceID, TRUE);
    }
    // trigger automations as last action
  }
}

function klicktipp_group_form_values_by_reference_id($prefix, array $values) {
  $prefixLength = strlen($prefix);
  $filterTagValues = function ($fieldName) use ($prefix, $prefixLength) {
    return substr($fieldName, 0, $prefixLength) == $prefix;
  };
  $tagValues = array_filter($values, $filterTagValues, ARRAY_FILTER_USE_KEY);
  $groupedValues = [];
  foreach ($tagValues as $fieldName => $values) {
    $referenceID = str_replace($prefix, '', $fieldName);
    $groupedValues[$referenceID] = $values;
  }
  return $groupedValues;
}

function klicktipp_group_custom_field_values_by_reference_id(array $parameters) {
  $result = ['referenced_values' => []];
  foreach($parameters as $fieldId => $value) {
    if (preg_match('/^(.*)_refid_([0-9]{1,})/', $fieldId, $matches)) {
      $referenceID = $matches[2];
      $customField = $matches[1];
      if(!isset($result['referenced_values'][$referenceID])) {
        $result['referenced_values'][$referenceID] = [];
      }
      $result['referenced_values'][$referenceID][$customField] = $value;
    } else {
      $result[$fieldId] = $value;
    }
  }
  return $result;
}

/**
 * subscriber edit form delete confirm
 */
function klicktipp_subscriber_edit_delete_confirm_form($form, $form_state, $account, $SubscriberID) {
  // the complete subscriber will be deleted, so ReferenceID does not apply
  $ReferenceID = 0;

  $FullSubscriber = Subscribers::RetrieveFullSubscriber($account->uid, $SubscriberID, $ReferenceID);

  //Note: dummy email addresses contain the SubscriberID, display phone number if we have one
  if (empty($FullSubscriber['EmailAddress'])) {
    $EntityName = (empty($FullSubscriber['PhoneNumber'])) ? $FullSubscriber['SubscriberID'] : $FullSubscriber['PhoneNumber'];
  }
  else {
    $EntityName = Subscribers::DepunycodeEmailAddress($FullSubscriber['EmailAddress']);
  }

  $Title = t('Delete Contact');

  $form = _klicktipp_confirm_form_modal('klicktipp_subscriber_edit_delete_confirm_form', $EntityName, $Title);

  $form['Entity'] = array(
    '#type' => 'value',
    '#value' => $FullSubscriber,
  );

  return $form;
}

/**
 * subscriber edit form delete confirm submit
 */
function klicktipp_subscriber_edit_delete_confirm_form_submit($form, &$form_state) {

  $FullSubscriber = $form_state['values']['Entity'];
  $UserID = $FullSubscriber['RelOwnerUserID'];

  klicktipp_subscriber_event_delete_subscribers($UserID, array(
    array('SubscriberID' => $FullSubscriber['SubscriberID'])
  ));

  drupal_get_messages('info', TRUE);
  klicktipp_set_redirect($form_state, "subscribers/$UserID");
}


/**
 * Note: not currently used (May 2021)
 * subscriber edit - add to facebook audience modal form
 */
function klicktipp_subscriber_edit_facebook_audience_form($form, $form_state, $account, $SubscriberID) {

  //TODO  if reactivated, the ReferenceID is not important, but the subscriber could
  //      have multiple email addresses
  $ReferenceID = 0;

  $FullSubscriber = Subscribers::RetrieveFullSubscriber($account->uid, $SubscriberID, $ReferenceID);

  $EntityName = Subscribers::DepunycodeEmailAddress($FullSubscriber['EmailAddress']);

  $AudienceOptions = ToolFacebookAudience::GetEntitiesAsOptionsArray($account->uid);


  if ( empty($AudienceOptions) ) {
    $AudienceOptions = array(
      '' => t("No Facebook Audiences are connected to your account."),
    );
  }
  else {
    $AudienceOptions =  array('' => t('Please select')) + $AudienceOptions;
  }

  $Title = t('Add contact to Facebook Audience');
  $Message = t('Select a Facebook Audience to which the contact %email should be added.', array(
    '%email' => $EntityName,
  ));

  $form = _klicktipp_confirm_form_modal('klicktipp_subscriber_edit_facebook_audience_form', $EntityName, $Title, TRUE, $Message, '', 'modal-info');

  //alter the standard delete button to show the number of subscribers to be deleted
  $form['ModalButtons']['Submit']['#theme'] = 'klicktipp_tag_submit';
  $form['ModalButtons']['Submit']['#value'] = t('Add contact');

  $form['ModalContent']['ToolID'] = array(
    '#type' => 'select',
    '#default_value' => '',
    '#options' => $AudienceOptions,
    '#title' => t("Facebook Audiences"),
    '#weight' => 3.5,
  );

  $form['Entity'] = array(
    '#type' => 'value',
    '#value' => $FullSubscriber,
  );

  return $form;

}

/**
 * Note: not currently used (May 2021)
 * subscriber edit - add to facebook audience modal form submit
 */
function klicktipp_subscriber_edit_facebook_audience_form_submit($form, &$form_state) {

  $FullSubscriber = $form_state['values']['Entity'];
  $UserID = $FullSubscriber['RelOwnerUserID'];
  $ToolID = $form_state['values']['ToolID'];

  //TODO  if reactivated, the ReferenceID is not important, but the subscriber could
  //      have multiple email addresses
  $ReferenceID = 0;

  if ( empty($ToolID) ) {
    drupal_set_message(t('Please select a Facebook Audience.'), 'error');
    return;
  }

  /** @var ToolFacebookAudience $ObjectAudience */
  $ObjectAudience = ToolFacebookAudience::FromID($UserID, $ToolID);

  if ( !$ObjectAudience ) {
    Errors::unexpected("FacebookAudience: Tool !ToolID not found for user !UserID", array(
      '!ToolID' => $ToolID,
      '!UserID' => $UserID,
    ));
    return;
  }

  $result = $ObjectAudience->AddSubscriber($FullSubscriber['SubscriberID'], $ReferenceID);

  if ( $result[0] ) {

    drupal_set_message(t('Contact successfully added to Facebook Audience %audience.', array(
      '%audience' => $ObjectAudience->GetData('Name')
    )));

  }
  else {

    drupal_set_message(t('Contact could not be added to Facebook Audience %audience.<br />Error: !error', array(
      '%audience' => $ObjectAudience->GetData('Name'),
      '!error' => t(/*ignore*/$result[1]),
    )), 'error');

  }

}




/**
 * subscriber edit form unsubscribe confirm
 */
function klicktipp_subscriber_edit_unsubscribe_confirm_form($form, $form_state, $account, $SubscriberID) {
  //TODO DEV-2421 ReferenceID shall come from dialog
  $ReferenceID = 0;

  $FullSubscriber = Subscribers::RetrieveFullSubscriber($account->uid, $SubscriberID, $ReferenceID);

  $EntityName = $FullSubscriber['EmailAddress'];
  $Title = t('Unsubscribe Contact');
  $Message = t('Are you sure to unsubscribe %name?', array('%name' => $EntityName));

  $form = _klicktipp_confirm_form_modal('klicktipp_subscriber_edit_unsubscribe_confirm_form', $EntityName, $Title, TRUE, $Message);

  //alter the standard delete button
  $form['ModalButtons']['Submit']['#theme'] = 'klicktipp_unsubscribe_submit';
  $form['ModalButtons']['Submit']['#value'] = $Title;

  $form['Entity'] = array(
    '#type' => 'value',
    '#value' => $FullSubscriber,
  );

  return $form;
}

/**
 * subscriber edit form unsubscribe confirm submit
 */
function klicktipp_subscriber_edit_unsubscribe_confirm_form_submit($form, &$form_state) {

  $FullSubscriber = $form_state['values']['Entity'];
  $UserID = $FullSubscriber['RelOwnerUserID'];

  klicktipp_subscriber_event_unsubscribe_subscribers($UserID, array($FullSubscriber['SubscriberID']));

  drupal_get_messages('info', TRUE);
  klicktipp_set_redirect($form_state, "subscribers/$UserID");
}

/**
 * Note: currently not used (bounces can be checked via subscriber search)
 *       the modal button should be placed in the subscriptions dialog
 * subscriber edit form reset bounce confirm
 */
function klicktipp_subscription_resetbounce_confirm_form($form, $form_state, $account, $subscription, $index) {
  $EntityName = Subscribers::DepunycodeEmailAddress($subscription['ContactInfo']);
  $Title = t('Reset bounce');
  $Message = t('Are you sure the e-mail %name exists?', array('%name' => $EntityName));

  $modalID = 'klicktipp_subscription_resetbounce_confirm_form_' . $index;
  $form = _klicktipp_confirm_form_modal($modalID, $EntityName, $Title, TRUE, $Message, '');

  //alter the standard delete button
  $form['ModalButtons']['Submit']['#theme'] = 'klicktipp_unsubscribe_submit';
  $form['ModalButtons']['Submit']['#value'] = $Title;

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $account->uid,
  );

  $form['Subscription'] = array(
    '#type' => 'value',
    '#value' => $subscription,
  );

  return $form;
}

/**
 * Note: currently not used (bounces can be checked via subscriber search)
 *       the modal button should be placed in the subscriptions dialog
 * subscriber edit form reset bounce confirm submit
 */
function klicktipp_subscription_resetbounce_confirm_form_submit($form, &$form_state) {

  $subscription = $form_state['values']['Subscription'];
  $UserID = $form_state['values']['UserID'];

  BounceEngine::ProcessBouncecheck($UserID, $subscription['ContactInfo'], TRUE, TRUE);

  // count today resets of user
  Statistics::UpdateActivityStatistics($UserID, array('TotalBounceResets' => 1));

  klicktipp_set_redirect($form_state, "/subscriber/$UserID/edit/{$subscription['SubscriberID']}/subscriptions");
}

/*
 * Subscriber Edit for Wufoo Subscription form Custom fields
 */

/**
 * subscriber edit form wufoo
 */
function klicktipp_subscriber_wufoo_customfields_edit_form($form, $form_state, $account, $SubscriberID, $BuildID = 0) {
  //TODO DEV-2414 R2+ ReferenceID shall come from dialog and/or listbuilding
  $ReferenceID = 0;

  $UserID = $account->uid;

  $result = db_query("SELECT * FROM {listbuilding} WHERE RelOwnerUserID = :RelOwnerUserID AND BuildType = :BuildType ORDER BY BuildID DESC", array(
    ':RelOwnerUserID' => $UserID,
    ':BuildType' => Listbuildings::TYPE_WUFOO,
  ));

  $UserWufoos = array();
  while ($DBArray = kt_fetch_array($result)) {
    $ObjectWufoo = Wufoo::FromArray($DBArray);
    if (!empty($ObjectWufoo)) {
      $UserWufoos[] = $ObjectWufoo->GetData();
    }
  }

  $form = array();
  $weight = 1;

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  // --- Retrieve subscription with custom fields

  $FullSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);

  if (empty($FullSubscriber)) {
    drupal_set_message(t("Contact not found."), 'error');
    drupal_goto("subscribers/$UserID"); //search form
    return NULL;
  }

  //set breadcrumb and page title
  $page_title = t('Edit Contact - Wufoo Subscription forms');
  klicktipp_set_title($page_title);

  //Note: dummy email addresses contain the SubscriberID, display phone number if we have one
  if (empty($FullSubscriber['EmailAddress'])) {
    if (empty($FullSubscriber['PhoneNumber'])) {
      $DisplaySubscriber = t('No email address');
    }
    else {
      $DisplaySubscriber = $FullSubscriber['PhoneNumber'];
    }
  }
  else {
    $DisplaySubscriber = Subscribers::DepunycodeEmailAddress($FullSubscriber['EmailAddress']);
  }

  klicktipp_set_breadcrumb(array(
    l(t('Search Contacts'), "subscribers/$UserID"),
    l($DisplaySubscriber, "subscriber/$UserID/edit/$SubscriberID"),
  ), $page_title);

  $form['Subscription'] = array(
    '#type' => 'value',
    '#value' => $FullSubscriber,
  );

  //for breadcrumb
  $form['#pre_render'][] = 'klicktipp_subscriber_wufoo_customfields_edit_form_pre_render';

  if (empty($UserWufoos)) {

    $link = l(t('here'), "listbuilding/$UserID/wufoo/add");
    $form['NoWufooForms'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("There are no Wufoo Subscription form. Create one !here.", array('!here' => $link)),
      '#weight' => $weight++,
    );

  }
  else {

    //display the email address
    $form['EmailAddress'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_item',
      '#title' => t('Email Address'),
      '#value' => ($FullSubscriber['EmailAddress'] == $FullSubscriber['SubscriberID']) ? t('No email address') : Subscribers::DepunycodeEmailAddress($FullSubscriber['EmailAddress']),
      '#weight' => $weight++,
    );

    if (!empty($FullSubscriber['PhoneNumber'])) {
      //display the phone number
      $form['PhoneNumber'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_item',
        '#title' => t('SMS Phone number'),
        '#value' => $FullSubscriber['PhoneNumber'],
        '#weight' => $weight++,
      );
    }

    $form['SubscriberID'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_item',
      '#title' => t('Contact ID'),
      '#value' => $SubscriberID,
      '#weight' => $weight++,
      '#attributes' => array('class' => array('input-short')),
    );

    // --- Wufoo Subscription form filter: @see klicktipp_overview_filter

    //add "filter" for WufooForm
    $WufooFormFilter = array(
      'BuildID' => array(), //only 1 filter => only 1 select (dropdown)
    );
    foreach ($UserWufoos as $id => $WufooForm) {
      $WufooFormFilter['BuildID'][$id] = $WufooForm['Name'];
    }


    // Display default Wufoo Subscription form
    // Priority: $BuildID -> SESSION -> latest Wufoo Subscription form
    $SessionFilterValue = $_SESSION['UserDefaults']['SubscriberEditWufooFilter']; //contains the last selected BuildID (by the user)
    if (!empty($BuildID) && array_key_exists($BuildID, $UserWufoos)) {
      $ArrayWufoo = $UserWufoos[$BuildID];
    }
    elseif (!empty($SessionFilterValue) && array_key_exists($SessionFilterValue, $UserWufoos)) {
      $ArrayWufoo = $UserWufoos[$SessionFilterValue];
    }
    else {
      $ArrayWufoo = array_shift($UserWufoos); //default: get the latest WufooForm (first in array, ordered desc)
    }

    // default value of the filter (use same key as in $ArrayFilters)
    $WufooFilterDefault = array(
      'BuildID' => $ArrayWufoo['BuildID'],
    );

    //create the filter: a select element with ID BuildID
    //adds a hidden Apply-Filter-Button (#type = submit)
    $form['WufooFormFilter'] = klicktipp_overview_filter($WufooFormFilter, $WufooFilterDefault, $weight, t('Wufoo Subscription forms'));

    // --- Get Wufoo Subscription form custom field

    $CustomFields = array();

    if (!empty($ArrayWufoo['Fields'])) {
      foreach ($ArrayWufoo['Fields'] as $field) {
        if (!empty($field['CustomFieldID'])) {
          $CustomFields[] = CustomFields::RetrieveCustomField($field['CustomFieldID'], $UserID);
        }
      }
    }

    // --- Display Custom field form element ---
    // --- @see: klicktipp_subscriber_edit_form() //TODO: create a function to use for both dialogs

    if (!empty($CustomFields)) {

      foreach ($CustomFields as $EachField) {

        if (empty($_POST['FormValue_Fields']['CustomField' . $EachField['CustomFieldID']])) {

          $CustomFieldValue = $FullSubscriber['CustomField' . $EachField['CustomFieldID']];
          if ($EachField['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
            $CustomFieldValue = CustomFields::ConvertCustomFieldDataToWidget($CustomFieldValue, CustomFields::WIDGET_DATE_DATEPICKER); //convert for datepicker
          }
          elseif ($EachField['FieldTypeEnum'] == CustomFields::TYPE_TIME) {
            $CustomFieldValue = CustomFields::ConvertCustomFieldDataToWidget($CustomFieldValue, CustomFields::WIDGET_TIME_2SELECTS); //convert for 2 select fields
          }
          elseif ($EachField['FieldTypeEnum'] == CustomFields::TYPE_CHECKBOX) { //checkboxes
            $CustomFieldValue = CustomFields::ConvertCustomFieldDataToWidget($CustomFieldValue, CustomFields::WIDGET_CHECKBOXES); //convert to array
          }
          elseif ($EachField['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
            $CustomFieldValue = CustomFields::ConvertCustomFieldDataToWidget($CustomFieldValue, CustomFields::WIDGET_DATETIME);
          }

        }
        else {
          $CustomFieldValue = $_POST['FormValue_Fields']['CustomField' . $EachField['CustomFieldID']];
        }

        if ($EachField['FieldTypeEnum'] == CustomFields::TYPE_DATE || $EachField['FieldTypeEnum'] == CustomFields::TYPE_TIME) {

          $CustomFieldID = $EachField['CustomFieldID'];
          $html = CustomFields::GenerateCustomFieldHTMLCode($EachField, $CustomFieldValue, '', FALSE, TRUE);

          $form["CustomFieldDateEnhanced_$CustomFieldID"] = array(
            '#type' => 'markup',
            '#weight' => $weight++,
            '#prefix' => '<div class="enhanced-date">',
            '#suffix' => '</div>',
          );

          $form["CustomFieldDateEnhanced_$CustomFieldID"]['CustomField' . $EachField['CustomFieldID']] = array(
            '#type' => 'item',
            '#title' => $EachField['FieldName'],
            '#value' => ($EachField['FieldTypeEnum'] == CustomFields::TYPE_TIME) ? "<div class='cf-time'>$html</div>" : $html,
            '#weight' => $weight++,
          );
        }
        else {

          $form['CustomField' . $EachField['CustomFieldID']] = array(
            '#type' => 'item',
            '#value' => CustomFields::GenerateCustomFieldHTMLCode($EachField, $CustomFieldValue, '', FALSE, TRUE),
            '#weight' => $weight++,
            '#title' => $EachField['FieldName'],
          );
        }

      }

    }
    else {
      $link = l(t('here'), "listbuilding/$UserID/wufoo/$BuildID/edit");
      $form['NoWufooFormFieldss'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_info',
        '#value' => t("There are no Custom fields assigned to this Wufoo Subscription form.<br />Edit the Wufoo Subscription form !here.", array('!here' => $link)),
        '#weight' => $weight++,
      );
    }

  }


  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  if (!empty($CustomFields)) {

    $form['buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Save Contact'),
      '#weight' => $weight++,
    );

    $form['buttons']['cancel'] = array(
      '#theme' => 'klicktipp_cancel_button',
      '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
      '#value' => "subscriber/$UserID/edit/$SubscriberID",
      '#weight' => $weight++,
    );

  }
  else {

    $form['buttons']['back'] = array(
      '#theme' => 'klicktipp_back_button',
      '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_BACK),
      '#value' => "subscriber/$UserID/edit/$SubscriberID",
      '#weight' => $weight++,
    );

  }

  return $form;

}

//TODO Drupal7: remove form_pre_render in D7, title and breadcrumb are set in form function
/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_wufoo_customfields_edit_form_pre_render($form) {

  $UserID = $form['account']['#value']->uid;
  $SubscriberID = $form['Subscription']['#value']['SubscriberID'];
  $EmailAddress = $form['Subscription']['#value']['EmailAddress'];

  $page_title = t('Edit Contact - Wufoo Subscription forms');
  klicktipp_set_title($page_title);
  klicktipp_set_breadcrumb(array(
    l(t('Search Contacts'), "subscribers/$UserID"),
    l(Subscribers::DepunycodeEmailAddress($EmailAddress), "subscriber/$UserID/edit/$SubscriberID"),
  ), $page_title);

  return $form;
}

/**
 * subscriber edit form wufoo validate
 */
function klicktipp_subscriber_wufoo_customfields_edit_form_validate($form, &$form_state) {

  if ($form_state['values']['op'] == t(/*ignore*/CONTROLLER_SUBSCRIBER_SAVE_BUTTON_TEXT)) {

    $UserID = $form_state['values']['account']->uid;

    foreach ($_POST['FormValue_Fields'] as $FormCustomFieldID => $Value) {

      $CustomFieldID = substr($FormCustomFieldID, 11, strlen($FormCustomFieldID));

      $ArrayCustomField = CustomFields::RetrieveCustomField($CustomFieldID, $UserID);

      if ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_TIME) {
        $Value = implode(':', $Value);
      }
      elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
        // ValidateCustomFieldValue will check Y-m-d format
        $Value = date('Y-m-d', strtotime($Value));
      }
      elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
        // ValidateCustomFieldValue will check "Y-m-d H:i" format
        $Value = date('Y-m-d H:i', strtotime($Value[0] . ' ' . $Value[1] . ':' .  $Value[2]));
      }

      $Value = check_plain($Value);
      // values for fields of type date, time, and datetime must be valid as argument for strtotime()
      $Check = CustomFields::ValidateCustomFieldValue($ArrayCustomField, $Value, TRUE);

      if (!$Check[0]) {
        form_set_error($FormCustomFieldID, $ArrayCustomField['FieldName'] . ': ' . $Check[1]);
      }

    }
  }
}

/**
 * subscriber edit form wufoo submit
 */
function klicktipp_subscriber_wufoo_customfields_edit_form_submit($form, &$form_state) {

  $UserID = $form_state['values']['account']->uid;
  $SubscriberID = $form_state['values']['Subscription']['SubscriberID'];
  $op = $form_state['values']['op'];

  if ($op == t(/*ignore*/KLICKTIPP_BUTTON_TEXT_APPLY_FILTER)) {

    $BuildID = $form_state['values']['BuildID'];

    $_SESSION['UserDefaults']['SubscriberEditWufooFilter'] = $BuildID;

    klicktipp_set_redirect($form_state, "subscriber/$UserID/edit/$SubscriberID/wufoo/$BuildID");

  }
  else {
    //save contact

    $parameters = array();
    foreach ($_POST['FormValue_Fields'] as $FormCustomFieldID => $Value) {

      $CustomFieldID = substr($FormCustomFieldID, 11, strlen($FormCustomFieldID));

      $ArrayCustomField = CustomFields::RetrieveCustomField($CustomFieldID, $UserID);

      //convert widget values
      if ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATE) {
        //the date widget used is the datepicker
        $Value = CustomFields::ConvertCustomFieldDataFromWidget($Value, CustomFields::WIDGET_DATE_DATEPICKER);
      }
      elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_TIME) {
        //the time widget used has 2 selects
        $Value = CustomFields::ConvertCustomFieldDataFromWidget($Value, CustomFields::WIDGET_TIME_2SELECTS);
      }
      elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_CHECKBOX) {
        //not a widget actually, but implode the array for the database
        $Value = CustomFields::ConvertCustomFieldDataFromWidget($Value, CustomFields::WIDGET_CHECKBOXES);
      }
      elseif ($ArrayCustomField['FieldTypeEnum'] == CustomFields::TYPE_DATETIME) {
        //the datetime widget used has 1 textfield for the date (datepicker) and 2 selects for hours and minutes
        if (empty($Value['date'])) {
          $Value = ""; //reset the datetime field by leaving the date field empty
        }
        else {
          $Value = CustomFields::ConvertCustomFieldDataFromWidget($Value, CustomFields::WIDGET_DATETIME);
        }
      }

      $parameters[$FormCustomFieldID] = check_plain($Value);
    }

    $parameters['InstantTrigger'] = true;
    $array_return = Subscribers::UpdateSubscription($UserID, $SubscriberID, $parameters);
    if ($array_return['Success'] == FALSE) {
      drupal_set_message(t('Subscriber information is not valid for update.'), 'error');
    }
    else {
      drupal_set_message(t('Contact %name successfully updated.', array('%name' => Subscribers::DepunycodeEmailAddress($form_state['values']['data']['Subscription']['EmailAddress']))));

    }

  }

}

/**
 * Subscriber Edit History
 */
function klicktipp_subscriber_history_form($form, $form_state, $account, $SubscriberID, $Filter = '', $PagerPage = 1, $PagerSize = 0) {

  $UserID = $account->uid;

  //Filter
  if (empty($Filter)) {
    $Filter = $_SESSION['UserDefaults'][$SubscriberID]['HistoryFilter'] ?? KLICKTIPP_DEFAULTS_HISTORY_FILTER;
  }

  $ReferenceID = $_SESSION['UserDefaults'][$SubscriberID]['ReferenceID'] ?? '';
  // Key from ContactInfo-Select: <SUBSCRIPTION_TYPE>_<CONTACT_INFO>
  $Subscription = $_SESSION['UserDefaults'][$SubscriberID]['Subscription'] ?? '';
  $ReferenceIDs = [];
  if ($ReferenceID != '') {
    $ReferenceIDs = [$ReferenceID];
  }

  if ($Subscription != '') {
    [$SubscriptionType, $ContactInfo] = explode('_', $Subscription, 2);
    if (empty($ReferenceIDs)) {
      $ReferenceIDs = array_keys(Subscription::getAssociatedSubscriptionReferences($UserID, $SubscriptionType, $ContactInfo));
    }
  } else {
    $SubscriptionType = null;
    $ContactInfo = '';
  }

  $_SESSION['UserDefaults'][$SubscriberID]['HistoryFilter'] = $Filter;

  $header = array(
    array(
      'data' => t('Date'),
      'field' => 'HistoryDate',
      'sort' => 'desc',
    ),
    array('data' => t('Type')),
    array('data' => t('Action')),
  );

  $ArraySort = klicktipp_get_tablesort($header, array('HistoryDate' => 'DESC'));

  $History = Subscribers::ReadHistory($UserID, $SubscriberID, $ArraySort, 0, 10, [], $ReferenceIDs, $ContactInfo, $SubscriptionType);
  $PendingHistory = Subscribers::ReadPendingHistory($UserID, $SubscriberID, $ArraySort, $ReferenceIDs);
  $History = ($ArraySort['HistoryDate'] == 'DESC') ? array_merge($PendingHistory, $History) : array_merge($History, $PendingHistory);

  //set breadcrumb and page title
  $page_title = t('Contact History');
  klicktipp_set_title($page_title);

  klicktipp_set_breadcrumb(array(
    l(t('Search Contacts'), "subscribers/$UserID"),
    l($SubscriberID, "subscriber/$UserID/edit/$SubscriberID"),
  ), $page_title);

  $weight = 1;

  $form = array();

  $form['#pre_render'][] = 'klicktipp_subscriber_history_form_pre_render';

  $form['uid'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['SubscriberID'] = array(
    '#type' => 'value',
    '#value' => $SubscriberID,
  );

  $action_labels = array(
    Subscribers::HISTORY_TEXT => t("history-text"),
    Subscribers::HISTORY_CREATED => t("history-created"),
    Subscribers::HISTORY_UPDATED => t("history-updated"),
    Subscribers::HISTORY_OPTIN => t("history-optin"),
    Subscribers::HISTORY_SUBSCRIBED => t("history-subscribed"),
    Subscribers::HISTORY_UNSUBSCRIBED => t("history-unsubscribed"),
    Subscribers::HISTORY_TAGGED => t("history-tagged"),
    Subscribers::HISTORY_TRANSACTIONAL => t("history-transactional"),
    Subscribers::HISTORY_UNTAGGED => t("history-untagged"),
    Subscribers::HISTORY_CUSTOMFIELDS => t("history-customfields-updated"),
    Subscribers::HISTORY_IMPORT => t("history-import"),
    Subscribers::HISTORY_UNSUBSCRIPTION_FEEDBACK => t("history-unsubscription-feedback"),
    Subscribers::HISTORY_SUBSCRIBER_FEEDBACK => t("history-subscriber-feedback"),
    Subscribers::HISTORY_EMAIL_ADDRESS_CHANGED => t("history-subscriber-update"),
    Subscribers::HISTORY_BOUNCED => t("history-bounce"),
    Subscribers::HISTORY_BOUNCECHECK => t("history-bounce-check"),
    Subscribers::HISTORY_BOUNCERESET => t('history-bounce-reset'),
    Subscribers::HISTORY_SOFTBOUNCERESET => t('history-bounce-reset'),
    Subscribers::HISTORY_SPAMBOUNCERESET => t('history-bounce-reset'),
    Subscribers::HISTORY_SOFTBOUNCE_RESOLVED => t('history-bounce'),
    Subscribers::HISTORY_HARDBOUNCE_RESOLVED => t('history-bounce'),
    Subscribers::HISTORY_SPAMBOUNCE_RESOLVED => t('history-bounce'),
    Subscribers::HISTORY_HATER => t("history-bounce"),
    Subscribers::HISTORY_SENDFAILED => t("history-failed"),
    Subscribers::HISTORY_SMS_INBOUND => t("history-sms-inbound"),
    Subscribers::HISTORY_SMSNUMBER_SUBSCRIBED => t("history-sms-subscribed"),
    Subscribers::HISTORY_SMSNUMBER_UNSUBSCRIBED => t("history-sms-unsubscribed"),
    Subscribers::HISTORY_SMSNUMBER_CHANGED => t("history-sms-changed"),
    Subscribers::HISTORY_SMSNUMBER_CONFLICT => t("history-sms-conflict"),
    Subscribers::HISTORY_SMSNUMBER_DELETED => t("history-sms-deleted"),
    Subscribers::HISTORY_SMSNUMBER_BOUNCED => t("history-sms-bounced"),
    Subscribers::HISTORY_SMSNUMBER_SOFTBOUNCE_RESOLVED => t("history-sms-bounced"),
    Subscribers::HISTORY_SMSNUMBER_HARDBOUNCE_RESOLVED => t("history-sms-bounced"),
    Subscribers::HISTORY_OUTBOUND_CALL_FAILED => t("history-outbound-call-failed"),
    Subscribers::HISTORY_FULLCONTACT_CALL_FAILED => t("history-fullcontact-call-failed"),
    Subscribers::HISTORY_FACEBOOK_AUDIENCE_EXPIRED_ACCESS => t("history-facebook-audience-expired"),
    Subscribers::HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED => t("history-facebook-audience-not-added"),
    Subscribers::HISTORY_PENDING => t("history-pending"),
    Subscribers::HISTORY_PAUSED => t("history-paused"),
    Subscribers::HISTORY_PAUSED_RESTART => t("history-paused-restart"),
    Subscribers::HISTORY_PROCESSING => t("history-processing"),
    Subscribers::HISTORY_GOAL_REACHED => t("history-goal-reached"),
    Subscribers::HISTORY_GOAL_PASSED => t("history-goal-passed"),
    Subscribers::HISTORY_NOTIFICATION_INVALID_TO_EMAIL => t("history-notification-invalid-to-email"),
    Subscribers::HISTORY_REQUEST_INFORMATION => t("history-request-information"),
    Subscribers::HISTORY_REQUEST_INFORMATION_FINISHED => t("history-request-information-finished"),
    Subscribers::HISTORY_REQUEST_UPDATE => t("history-request-update"),
    Subscribers::HISTORY_REQUEST_UPDATE_FINISHED => t("history-request-update-finished"),
    Subscribers::HISTORY_REQUEST_DELETE => t("history-request-delete"),
    Subscribers::HISTORY_PAYMENT => t("history-payment"),
    Subscribers::HISTORY_PAYMENT_INVOICE => t("history-payment-invoice"),
    Subscribers::HISTORY_STATE_MOVED => t("history-state-moved"),
    Subscribers::HISTORY_WAITING_FOR_TEMPORAL_CONDITION => t("history-waiting-for-temporal-condition"),
  );

  $action_filter = array(
    KLICKTIPP_DEFAULTS_HISTORY_FILTER => t("All"),
    CONTROLLER_SUBSCRIBER_HISTORY_FILTER_SUBSCRIPTION => t("Subscription status"),
    CONTROLLER_SUBSCRIBER_HISTORY_FILTER_TAGGING => t('Tagging'),
    CONTROLLER_SUBSCRIBER_HISTORY_FILTER_INFO => t('Miscellaneous'),
  );

  $action_groups = array(
    CONTROLLER_SUBSCRIBER_HISTORY_FILTER_SUBSCRIPTION => array(
      Subscribers::HISTORY_CREATED,
      Subscribers::HISTORY_UPDATED,
      Subscribers::HISTORY_OPTIN,
      Subscribers::HISTORY_SUBSCRIBED,
      Subscribers::HISTORY_UNSUBSCRIBED,
      Subscribers::HISTORY_IMPORT,
      Subscribers::HISTORY_EMAIL_ADDRESS_CHANGED,
      Subscribers::HISTORY_BOUNCED,
      Subscribers::HISTORY_BOUNCECHECK,
      Subscribers::HISTORY_BOUNCERESET,
      Subscribers::HISTORY_SOFTBOUNCE_RESOLVED,
      Subscribers::HISTORY_HARDBOUNCE_RESOLVED,
      Subscribers::HISTORY_SPAMBOUNCE_RESOLVED,
      Subscribers::HISTORY_SOFTBOUNCERESET,
      Subscribers::HISTORY_SPAMBOUNCERESET,
      Subscribers::HISTORY_HATER,
      Subscribers::HISTORY_SMSNUMBER_SUBSCRIBED,
      Subscribers::HISTORY_SMSNUMBER_UNSUBSCRIBED,
      Subscribers::HISTORY_SMSNUMBER_CHANGED,
      Subscribers::HISTORY_SMSNUMBER_CONFLICT,
      Subscribers::HISTORY_SMSNUMBER_DELETED,
      Subscribers::HISTORY_SMSNUMBER_BOUNCED,
      Subscribers::HISTORY_SMSNUMBER_SOFTBOUNCE_RESOLVED,
      Subscribers::HISTORY_SMSNUMBER_HARDBOUNCE_RESOLVED,
      Subscribers::HISTORY_NOTIFICATION_INVALID_TO_EMAIL,
      Subscribers::HISTORY_REQUEST_INFORMATION,
      Subscribers::HISTORY_REQUEST_INFORMATION_FINISHED,
      Subscribers::HISTORY_REQUEST_UPDATE,
      Subscribers::HISTORY_REQUEST_UPDATE_FINISHED,
      Subscribers::HISTORY_REQUEST_DELETE,
      Subscribers::HISTORY_PAYMENT,
      Subscribers::HISTORY_PAYMENT_INVOICE,
    ),
    CONTROLLER_SUBSCRIBER_HISTORY_FILTER_TAGGING => array(
      Subscribers::HISTORY_TAGGED,
      Subscribers::HISTORY_UNTAGGED,
    ),
    CONTROLLER_SUBSCRIBER_HISTORY_FILTER_INFO => array(
      Subscribers::HISTORY_TEXT,
      Subscribers::HISTORY_CUSTOMFIELDS,
      Subscribers::HISTORY_UNSUBSCRIPTION_FEEDBACK,
      Subscribers::HISTORY_SUBSCRIBER_FEEDBACK,
      Subscribers::HISTORY_SMS_INBOUND,
      Subscribers::HISTORY_OUTBOUND_CALL_FAILED,
      Subscribers::HISTORY_FULLCONTACT_CALL_FAILED,
      Subscribers::HISTORY_FACEBOOK_AUDIENCE_EXPIRED_ACCESS,
      Subscribers::HISTORY_FACEBOOK_AUDIENCE_NOT_ADDED,
      Subscribers::HISTORY_PENDING,
      Subscribers::HISTORY_PAUSED,
      Subscribers::HISTORY_PAUSED_RESTART,
      Subscribers::HISTORY_PROCESSING,
      Subscribers::HISTORY_GOAL_REACHED,
      Subscribers::HISTORY_GOAL_PASSED
    ),
  );

  $BounceLabels = Subscribers::$TranslationBounceTypes;

  $rows = array();
  foreach ($History as $history) {
    $referenceID = empty($history['ReferenceID']) ? 0 : $history['ReferenceID'];
    if ($Filter != KLICKTIPP_DEFAULTS_HISTORY_FILTER && !in_array($history['HistoryType'], $action_groups[$Filter])) {
      continue;
    }

    $highlight = "";
    $AdminButton = "";
    $BounceQuickhelp = "";
    $InvoiceButton = "";

    switch ($history['HistoryType']) {
      case Subscribers::HISTORY_PENDING:
      case Subscribers::HISTORY_WAITING_FOR_TEMPORAL_CONDITION:
        $highlight = "info";
        if (klicktipp_user_edit_access($account)) {
          $ObjectCampaign = Campaigns::FromID($account->uid, $history['HistoryData']['CampaignID']);
          $triggerType = $ObjectCampaign->GetData('AutoResponderTriggerTypeEnum');
          if (!Campaigns::IsNewsletter($triggerType)) {
            // allow users with SUBACCOUNT_ADMINISTRATOR to immediately launch pending queue entries
            // @see: klicktipp_subscriber_launch_pending_queue_entry() in callbacks/subscriber.inc
            $AdminButton = theme("klicktipp_edit_table_button", array(
              'element' => array(
                '#title' => t('Launch now'),
                '#value' => "subscriber/{$account->uid}/pending-queue-entry/{$history['HistoryData']['SubscriberID']}/{$history['HistoryData']['CampaignID']}/$referenceID",
              ),
            ));
          }
          if (Campaigns::IsProcessflow($triggerType) && $history['HistoryType'] == Subscribers::HISTORY_WAITING_FOR_TEMPORAL_CONDITION) {
            $AdminButton = ' ' .  theme("klicktipp_edit_table_button", array(
              'element' => array(
                '#title' => t('Skip temporal condition'),
                '#value' => "subscriber/{$account->uid}/skip-temporal-condition/{$history['HistoryData']['SubscriberID']}/{$history['HistoryData']['CampaignID']}/$referenceID",
              )
            ));
          }
        }
        break;
      case Subscribers::HISTORY_PAUSED_RESTART:
        $highlight = "info";
        break;
      case Subscribers::HISTORY_PAUSED:
        $highlight = "warning";
        break;
      case Subscribers::HISTORY_PROCESSING:
        $highlight = "success";
        break;
      case Subscribers::HISTORY_BOUNCED:
        if ($history['HistoryID'] == 'softbounce') {
          $BounceQuickhelp = theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => 'bounce-type-soft',
              '#title' => t(/*ignore*/$BounceLabels[Subscribers::BOUNCETYPE_SOFT]),
            )
          ));
        }
        elseif ($history['HistoryID'] == 'spambounce') {
          $BounceQuickhelp = theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => 'bounce-type-spam',
              '#title' => t(/*ignore*/$BounceLabels[Subscribers::BOUNCETYPE_SPAM]),
            )
          ));
        }
        elseif ($history['HistoryID'] == 'hardbounce') {
          $BounceQuickhelp = theme('klicktipp_quickhelp', array(
            'element' => array(
              '#quickhelp' => 'bounce-type-hard',
              '#title' => t(/*ignore*/$BounceLabels[Subscribers::BOUNCETYPE_HARD]),
            )
          ));
        }
        break;
      case Subscribers::HISTORY_BOUNCECHECK:
      case Subscribers::HISTORY_BOUNCERESET:
      case Subscribers::HISTORY_HARDBOUNCE_RESOLVED:
        $BounceQuickhelp = theme('klicktipp_quickhelp', array(
          'element' => array(
            '#quickhelp' => 'bounce-type-hard',
            '#title' => t(/*ignore*/$BounceLabels[Subscribers::BOUNCETYPE_HARD]),
          )
        ));
        break;
      case Subscribers::HISTORY_SOFTBOUNCE_RESOLVED:
      case Subscribers::HISTORY_SOFTBOUNCERESET:
        $BounceQuickhelp = theme('klicktipp_quickhelp', array(
          'element' => array(
            '#quickhelp' => 'bounce-type-soft',
            '#title' => t(/*ignore*/$BounceLabels[Subscribers::BOUNCETYPE_SOFT]),
          )
        ));
        break;
      case Subscribers::HISTORY_SPAMBOUNCE_RESOLVED:
      case Subscribers::HISTORY_SPAMBOUNCERESET:
        $BounceQuickhelp = theme('klicktipp_quickhelp', array(
          'element' => array(
            '#quickhelp' => 'bounce-type-spam',
            '#title' => t(/*ignore*/$BounceLabels[Subscribers::BOUNCETYPE_SPAM]),
          )
        ));
        break;
      case Subscribers::HISTORY_TAGGED:
        $tag = Tag::RetrieveTagByNameCached($account->uid, $history['Text']);
        if ($tag['Category'] == Tag::CATEGORY_SMARTLINK) {
          $history['Text'] = SmartLink::decorateName($history['Text']);
        }
        break;
      case Subscribers::HISTORY_PAYMENT:
        // do we have an email template defined?
        /** @var PaymentIPNs $ObjectListbuilding */
        $ObjectListbuilding = PaymentIPNs::FromID($account->uid, $history['HistoryData']['BuildID']);
        if ($ObjectListbuilding) {
          if ($ObjectListbuilding->GetData('InvoiceEmailID') > 0) {
            // add "Send Invoice" button (to re-send the invoice
            $InvoiceButton = ' '.theme("klicktipp_email_table_button", [
                'element' => [
                  '#title' => t('Send Invoice'),
                  '#value' => "subscriber/{$account->uid}/edit/$SubscriberID/sendinvoice/{$history['HistoryID']}",
                ],
              ]);
          }
        }
        break;
      default:
        break;
    }

    $row = array(
      array('data' => Dates::formatDate(Dates::FORMAT_DMYHIS, (int) $history['HistoryDate'])),
      array('data' => $action_labels[$history['HistoryType']]),
      array(
        'data' => $history['Text'] . $BounceQuickhelp . $AdminButton . $InvoiceButton,
        'class' => array('table-col-quickhelp'),
      ),
    );

    $rows[] = array(
      'data' => $row,
      'class' => array($highlight),
    );

  }

  $form['History'] = array(
    '#type' => 'fieldset',
    '#title' => t("Create new history entry"),
    '#collapsible' => TRUE,
    '#collapsed' => TRUE,
    '#weight' => $weight++,
  );

  $form['History']['HistoryText'] = array(
    '#type' => 'textarea',
    '#default_value' => '',
    '#resizable' => FALSE,
    '#rows' => 3,
    '#title' => t('History text'),
    '#weight' => $weight++,
  );

  $form['History']['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['History']['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Save History text'),
    '#weight' => $weight++,
  );

  $form['History']['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "subscriber/$UserID/edit/$SubscriberID/history",
    '#weight' => $weight++,
  );

  //after changing the filter or the pager, we want to jump to the history table including the filer select
  //because of the fixed top menu we have an offset, so place the anchor here
  $form['HistoryTableAnchor'] = array(
    '#type' => 'markup',
    '#value' => '<a id="HistoryTable" name="HistoryTable" style="position: absolute; margin-top: -80px;"></a>',
    '#weight' => $weight++,
  );

  $form['FilterHistory'] = array(
    '#type' => 'hidden',
    '#default_value' => '',
  );

  $referenceDisplayNames = Reference::getSubscriberRelatedDisplayNames($UserID, $SubscriberID);
  $displayReferences = count($referenceDisplayNames) > 1 || key($referenceDisplayNames) != 0;
  if ($displayReferences) {
    $filtersTitle = t('Filter by Type, Reference and Subscription');
  } else {
    $filtersTitle = t('Filter by Type and Subscription');
  }

  $collapseFilters = empty($Filter) && empty($ContactInfo) && $ReferenceID === '';
  $form['Filters'] = array(
    '#type' => 'fieldset',
    '#title' => $filtersTitle,
    '#collapsible' => TRUE,
    '#collapsed' => $collapseFilters,
    '#weight' => $weight++,
  );

  $form['Filters']['Filter'] = array(
    '#type' => 'select',
    '#title' => t("History type"),
    '#weight' => $weight++,
    '#options' => $action_filter,
    '#default_value' => $Filter,
    '#attributes' => array(
      'data-event-change' => 'FilterHistory',
    ),
  );

  if ($displayReferences) {
    $form['Filters']['ReferenceID'] = array(
      '#type' => 'select',
      '#title' => t("Reference"),
      '#weight' => $weight++,
      '#options' => ['' => t('All')] + $referenceDisplayNames,
      '#default_value' => $ReferenceID,
      '#attributes' => array(
        'data-event-change' => 'FilterHistory',
      ),
    );
  }

  $subscriptionTypeLabels = [
    Subscription::SUBSCRIPTIONTYPE_EMAIL => t('Email Address'),
    Subscription::SUBSCRIPTIONTYPE_SMS => t('SMS Phone number'),
  ];

  $subscriptionOptions = ['' => t('All')];
  $subscriptions = Subscriber::getAllSubscriptions($UserID, $SubscriberID);
  foreach ($subscriptions as $subscription) {
    $contactType = $subscription['SubscriptionType'];
    if ($contactType == Subscription::SUBSCRIPTIONTYPE_NONE) {
      continue;
    }
    $subscriptionTypeLabel = $subscriptionTypeLabels[$contactType];
    $subscriptionKey = sprintf('%d_%s', $contactType, $subscription['ContactInfo']);
    if (!isset($subscriptionOptions[$subscriptionTypeLabel])) {
      $subscriptionOptions[$subscriptionTypeLabel] = [];
    }
    $subscriptionOptions[$subscriptionTypeLabel][$subscriptionKey] = $subscription['ContactInfo'];
  }


  $form['Filters']['Subscription'] = array(
    '#type' => 'select',
    '#title' => t("Contact info"),
    '#weight' => $weight++,
    '#options' => $subscriptionOptions,
    '#default_value' => $Subscription,
    '#attributes' => array(
      'data-event-change' => 'FilterHistory',
    ),
  );

  if (empty($rows)) {
    $form['HistoryTable'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t("There is no history for this contact yet."),
      '#weight' => $weight++,
    );
  }
  else {

    $HistoryCount = count($rows);

    //Pager
    $PagerLink = "subscriber/$UserID/edit/$SubscriberID/history/$Filter";
    [$PagerPage, $PagerSize, $Offset] = klicktipp_validate_pager($PagerPage, $PagerSize, $HistoryCount, $PagerLink);

    $rows = array_slice($rows, $Offset, $PagerSize, TRUE);

    $form['HistoryTable'] = array(
      '#type' => 'markup',
      '#value' => theme('table', array(
        'header' => $header,
        'rows' => $rows,
        'dataRow' => TRUE,
        'attributes' => ['data-e2e-id' => 'table-subscriber-history']
      )),
      '#weight' => $weight++,
      '#suffix' => theme('klicktipp_table_pager', array(
        'element' => array(
          'pager_page' => $PagerPage,
          //current page
          'pager_size' => $PagerSize,
          //current page size
          'pager_hide_sizes' => FALSE,
          //hide page size badges
          'pager_total' => $HistoryCount,
          //total entries in the table
          'pager_max_items' => KLICKTIPP_PAGER_MAX_PAGINATION_ITEM,
          //max pager items
          'pager_link' => $PagerLink,
          //link of pager badges and items, "/<CURRENT-PAGE>/<PAGE-SIZE>" will be added
          'pager_link_query' => (empty($_GET['order'])) ? array() : array(
            'order' => $_GET['order'],
            'sort' => $_GET['sort'],
          ),
          //build an additional link query string, possibly for table sort @see l()/url()
          'pager_anchor' => 'HistoryTable',
          //HTML anchor (possibly the table) to jump to after clicking a pager badge or item
        )
      )),
    );

  }

  return $form;

}

/**
 * Subscriber Edit History: pre render
 */
function klicktipp_subscriber_history_form_pre_render($form) {

  $script = "
    window['FilterHistory'] = function (element, args) {

      $('.edit-FilterHistory').val(1);
      element.parents('form').submit();
    };
  ";

  drupal_add_js($script, array('type' => 'inline', 'scope' => 'header', 'group' => JS_DEFAULT));

  return $form;

}

/**
 * Subscriber Edit History: validate
 */
function klicktipp_subscriber_history_form_validate($form, &$form_state) {

  if (empty($form_state['values']['FilterHistory']) && empty($form_state['values']['HistoryText'])) {
    form_set_error('HistoryText', t("Please insert a history text."));
  }

}

/**
 * Subscriber Edit History: submit
 */
function klicktipp_subscriber_history_form_submit($form, &$form_state) {
  $UserID = $form_state['values']['uid'];
  $SubscriberID = $form_state['values']['SubscriberID'];

  if (empty($form_state['values']['FilterHistory'])) {

    $HistoryText = $form_state['values']['HistoryText'];

    Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_TEXT, $HistoryText);

    drupal_set_message(t('History text successfully updated.'));

    klicktipp_set_redirect($form_state, "subscriber/$UserID/edit/$SubscriberID/history");

  }
  else {
    $PagerSize = $_SESSION['UserDefaults']['PagerSize'];
    $Filter = $form_state['values']['Filter'];
    if (count($_SESSION['UserDefaults'] ?? []) >= 10) {
      array_shift($_SESSION['UserDefaults']);
    }
    $_SESSION['UserDefaults'][$SubscriberID]['HistoryFilter'] = $Filter;
    $_SESSION['UserDefaults'][$SubscriberID]['ReferenceID'] = $form_state['values']['ReferenceID'];
    $_SESSION['UserDefaults'][$SubscriberID]['Subscription'] = $form_state['values']['Subscription'];
    klicktipp_set_redirect($form_state, "subscriber/$UserID/edit/$SubscriberID/history/$Filter/1/$PagerSize");
  }

}

/**
 * subscriber change email dialog
 */
function klicktipp_subscriber_change_email_whitelabel($ChangeEmailHash) {
  //theme this dialog as a white label

  $e = drupal_get_form('klicktipp_subscriber_change_email_form', $ChangeEmailHash);
  $form = drupal_render($e);

  $params_decoded = Core::DecryptURL($ChangeEmailHash);
  klicktipp_display_whitelabel_message(t('Change email address'), $form, '', $params_decoded['UserID']);
  //this won't return

}

/**
 * subscriber change email dialog
 */
function klicktipp_subscriber_change_email_form($form, $form_state, $ChangeEmailHash) {

  $form = array();
  $weight = 1;

  $params_decoded = Core::DecryptURL($ChangeEmailHash);

  $UserID = $params_decoded['UserID'];
  $OldEmailAddress = $params_decoded['EmailAddress']; // punycoded

  if (empty($UserID) || empty($OldEmailAddress)) {
    $error = array(
      '!UserID' => $UserID,
      '!OldEmailAddress' => $params_decoded['OldEmailAddress'],
      '!hash' => $ChangeEmailHash,
      'decoded' => $params_decoded,
    );

    //temporary logging of invalid hashes
    //probably URL manipulation or old link after email has been changed
    Errors::typedUnexpected('kt-change-email-form', "Invalid hash: !hash (User: !UserID, EmailAddress: !OldEmailAddress)", $error, FALSE);

    //display a white label message: 'Sorry, this link is no longer valid.'
    klicktipp_invalid_link($UserID);
    return $form;
  }

  $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $OldEmailAddress);

  if (empty($SubscriberID)) {
    //subscriber has already changed the email address (old link)
    //display a white label message: 'Sorry, this link is no longer valid.'
    klicktipp_invalid_link($UserID);
    return $form;
  }

  $form['ChangeEmailHash'] = array(
    '#type' => 'value',
    '#value' => $ChangeEmailHash,
  );

  $form['OldEmailAddress'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#title' => t('Existing email address'),
    '#value' => Subscribers::DepunycodeEmailAddress($OldEmailAddress),
    '#weight' => $weight++,
  );

  $form['NewEmailAddress'] = array(
    '#type' => 'textfield',
    '#title' => t('New email address'),
    '#default_value' => '',
    '#weight' => $weight++,
    '#description' => t('A confirmation email will be sent to this address.'),
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  $form['buttons']['submit'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t('Change email address'),
    '#weight' => $weight++,
  );

  $form['buttons']['cancel'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_cancel_submit',
    '#value' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#weight' => $weight++,
  );

  return $form;

}

/**
 * Check if an email address is entered or if the new email address is the old one
 * Note: Email address validation is done in Subscribers::UpdateSubscription() but it doesn't check for an empty value
 * Note: we use _form_validate() instead of #required due to the cancel submit button
 */
function klicktipp_subscriber_change_email_form_validate($form, &$form_state) {

  if ($form_state['values']['op'] != t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL)) {

    if (empty($form_state['values']['NewEmailAddress'])) {
      form_set_error('NewEmailAddress', t("Please enter your new email address."));
    }
    elseif ($form_state['values']['NewEmailAddress'] == $form_state['values']['ArraySubscriber']['EmailAddress']) {
      form_set_error('NewEmailAddress', t("Please enter an email address different from your current email address."));
    }

  }

}

/**
 * subscriber change email dialog submit
 */
function klicktipp_subscriber_change_email_form_submit($form, &$form_state) {

  //check hash again
  $params_decoded = Core::DecryptURL($form_state['values']['ChangeEmailHash']);
  $UserID = $params_decoded['UserID'];

  if ($form_state['values']['op'] == t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL)) {

    $message = t('Your email addess has not been changed.');
    klicktipp_display_whitelabel_message(t('Change email address'), $message, '', $UserID);
    //this won't return
  }

  $OldEmailAddress = $params_decoded['EmailAddress'];
  $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $OldEmailAddress);

  if (empty($SubscriberID)) {
    //form manipulation or same change-email-form in multiple tabs and the email has already been changed

    $error = array(
      '!UserID' => $UserID,
      '@old' => $OldEmailAddress,
      '@new' => $form_state['values']['NewEmailAddress'],
      '!subscriber' => $SubscriberID,
      '!hash' => $form_state['values']['ChangeEmailHash'],
      '!function' => __FUNCTION__,
    );

    //no need to notify the user, the form hash is invalid so returning to the form will show the invalid link message
    Errors::unexpected('Error while changing the email address from "@old" to "@new" by the subscriber in !function (UserID: !UserID).', $error);
    return;
  }

  $NewEmailAddress = Subscribers::PunycodeEmailAddress($form_state['values']['NewEmailAddress']);

  //params: (UserID, SubscriberID, NewEmailAddress, use change email DOI for confirmation email)
  $parameters = array(
    'EmailAddress' => $NewEmailAddress,
    'InstantTrigger' => true,
  );
  $array_return = Subscribers::UpdateSubscription($UserID, $SubscriberID, $parameters, Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST);

  if ($array_return['Success'] == FALSE) {

    if (in_array($array_return['ErrorCode'], array(
      KLICKTIPPAPI_ERROR_EMAILADDRESS_VALIDATION_FAILED,
      KLICKTIPPAPI_ERROR_EMAILADDRESS_CHANGE_NOT_ALLOWED,
      KLICKTIPPAPI_ERROR_SUBSCRIBER_EMAIL_NOT_FOUND,
    ))
    ) {
      drupal_set_message(t('New contact email address is not valid for update.'), 'error');

    }
    else {

      $error = array(
        '!UserID' => $UserID,
        '@old' => Subscribers::DepunycodeEmailAddress($OldEmailAddress),
        '@new' => $NewEmailAddress,
        '@originalOld' => $OldEmailAddress,
        '@originalNew' => $form_state['values']['NewEmailAddress'],
        '!subscriber' => $SubscriberID,
        '!return' => $array_return,
      );

      Errors::unexpected('Error while changing the email address from "@old" to "@new" by the subscriber (UserID: !UserID).', $error, TRUE); //notify user

    }

  }
  else {
    $FullSubscription = Subscription::RetrieveSubscriptionAndReferenceByContactInfo($UserID, $NewEmailAddress, Subscription::SUBSCRIPTIONTYPE_EMAIL, TRUE);
    if (empty($FullSubscription)) {
      $error = array(
        '!UserID' => $UserID,
        '@old' => Subscribers::DepunycodeEmailAddress($OldEmailAddress),
        '@new' => $form_state['values']['NewEmailAddress'],
        '!subscriber' => $SubscriberID,
        '!hash' => $form_state['values']['ChangeEmailHash'],
        '!function' => __FUNCTION__,
      );

      //no need to notify the user, the form hash is invalid so returning to the form will show the invalid link message
      Errors::unexpected('Error, subscriber not found after changing the email address from "@old" to "@new" by the subscriber in !function. (UserID: !UserID)', $error);
      return;
    }

    $ChangeEmailList = Lists::RetrieveChangeEmailList($UserID);

    $RedirectURL = Lists::GetSubscriptionRedirect($UserID, $FullSubscription, $ChangeEmailList, $FullSubscription['ReferenceID'], ['oldemail' => $OldEmailAddress]);

    header('Location: ' . $RedirectURL, TRUE, 301);
    exit;


  }


}

/**
 * subscriber update information dialog
 * A subscriber can update his information via this dialog
 */
function klicktipp_subscriber_update_form($form, $form_state, $account, $Parameters) {


  $UserID = $account->uid;
  $form = array();
  $weight = 1;

  $Subscriber = Subscribers::RetrieveFullSubscriberWithMergeCheck($UserID, $Parameters['SubscriberID'], $Parameters['ReferenceID']);
  $Parameters['SubscriberID'] = $Subscriber['SubscriberID'];
  $ReferenceID = $Parameters['ReferenceID'] = $Subscriber['ReferenceID'];

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $UserID,
  );

  $form['Parameters'] = array(
    '#type' => 'value',
    '#value' => $Parameters,
  );

  if (empty($Subscriber)) {

    $form['notfound'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t('The email address %email is unknown.', array(
        '%email' => Subscribers::DepunycodeEmailAddress($Parameters['EmailAddress'])
      ))
    );

  }
  else {

    $form['intro'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t('Here you can update the information for the email address %email.', array(
        '%email' => Subscribers::DepunycodeEmailAddress($Parameters['EmailAddress'])
      ))
    );

    //customfields form fields
    $Template = VarPrivacy::GetTemplate($UserID);
    $CustomFields = CustomFields::RetrieveCustomFields($UserID);
    $UpdateFields = array();
    foreach ($CustomFields as $field) {
      $CustomFieldID = $field['CustomFieldID'];
      if (isset($Template['fields'][$CustomFieldID])) {
        $field['FieldName'] = (empty($Template['fields'][$CustomFieldID])) ? $field['FieldName'] : $Template['fields'][$CustomFieldID];
        $UpdateFields[$CustomFieldID] = $field;
      }
    }

    if (!empty($UpdateFields)) {
      Libraries::include('subscriber.inc');
      $form = _klicktipp_subscriber_edit_customfields_form_fields($UserID, $Parameters['SubscriberID'], $ReferenceID, $UpdateFields, $form, $weight);
    }

    if (!empty($form['GlobalFields'])) {
      $form['GlobalFields']['#title'] = t('Privacy:Personal data');
    }

    if (!empty($form['CustomFields'])) {
      $form['CustomFields']['#title'] = t('Privacy:Additional data');
    }

    $form['buttons'] = array(
      '#type' => 'markup',
      '#weight' => $weight++,
      '#prefix' => '<div class="button-row">',
      '#suffix' => '</div>',
    );

    $form['buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Save'),
      '#weight' => $weight++,
    );

    $form['buttons']['cancel'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t('If you do not want to update, just close the browser/tab.'),
      '#weight' => $weight++,
    );

  }

  return $form;

}

function klicktipp_subscriber_update_form_submit($form, &$form_state) {

  Libraries::include('subscriber.inc');

  $UserID = $form_state['values']['UserID'];
  $Parameters = $form_state['values']['Parameters'];

  $SubscriberID = $Parameters['SubscriberID'];

  $ReferenceID = $Parameters['ReferenceID'];

  $FormValues = _klicktipp_subscriber_edit_customfields_form_values($UserID);

  $UpdateFields = array();
  foreach($FormValues as $FormCustomFieldID => $value) {
    $CustomFieldID = substr($FormCustomFieldID, 11, strlen($FormCustomFieldID));
    $UpdateFields[$CustomFieldID] = $value;
  }

  $UpdateFields['ReferenceID'] = $ReferenceID;
  Subscribers::SubscriberRequestRegister($UserID, $SubscriberID, Subscribers::HISTORY_REQUEST_UPDATE, $UpdateFields);

  $redirectURL = VarPrivacy::GetCustomRedirect($Parameters['UserID'], $Parameters['SubscriberID'], $Parameters['EmailAddress'], Subscribers::HISTORY_REQUEST_UPDATE);

  if (empty($redirectURL)) {
    klicktipp_display_whitelabel_message(t('Privacy:Subscriber Update'), t('Privacy:Your personal information have been submitted and will be updated soon.'), '', $Parameters['UserID']);
  }
  else {
    header("Location: {$redirectURL}", TRUE, 301);
    drupal_exit();
  }

}


/**
 * subscriber send email form
 */
function klicktipp_subscriber_sendmail_form($form, $form_state, $account, $SubscriberID) {
  //TODO DEV-2430 R2 - refID should be selected in the dialog (dropdown with all email addresses if subscriber)
  $ReferenceID = 0;

  $UserID = $account->uid;
  $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
  $CanReceiveMails = !empty($FullSubscriberWithFields['EmailAddress']);

  //set breadcrumb and page title
  $page_title = t('Send Email to Contact');
  klicktipp_set_title($page_title);

  //Note: dummy email addresses contain the SubscriberID, display phone number if we have one
  if (empty($FullSubscriberWithFields['EmailAddress'])) {
    if (empty($FullSubscriberWithFields['PhoneNumber'])) {
      $DisplaySubscriber = t('No email address');
    }
    else {
      $DisplaySubscriber = $FullSubscriberWithFields['PhoneNumber'];
    }
    $DisplayEmailAddress = t('No email address');

    $CanReceiveMails = FALSE;
    drupal_set_message (t('This subscriber cannot receive emails.'), 'warning');
  }
  else {
    $DisplaySubscriber = Subscribers::DepunycodeEmailAddress($FullSubscriberWithFields['EmailAddress']);
    $DisplayEmailAddress = $DisplaySubscriber;

    // subscribed?
    if ($FullSubscriberWithFields['SubscriptionStatus'] != Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED) {
      $CanReceiveMails = FALSE;
      drupal_set_message (t('This subscriber cannot receive emails.'), 'warning');
    }
  }

  klicktipp_set_breadcrumb(array(
    l(t('Search Contacts'), "subscribers/$UserID"),
    l($DisplaySubscriber, "subscriber/$UserID/edit/$SubscriberID"),
  ), $page_title);

  $weight = 1;

  $form = array();

  $form['#pre_render'][] = 'klicktipp_subscriber_sendmail_form_pre_render';

  $form['data'] = array(
    '#type' => 'value',
    '#value' => array(
      'Subscription' => $FullSubscriberWithFields,
      'account' => $account,
    ),
  );


  $form['EmailAddress'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_item',
    '#title' => t('Email Address'),
    '#value' => $DisplayEmailAddress,
    '#weight' => $weight++,
  );

  // get sendable email templates
  $options = array(
    0 => t('Please select'),
  );
  $result = db_query("SELECT * FROM {emails} WHERE RelUserID = :RelUserID AND EmailType = :EmailType ORDER BY EmailName", array(
    ':RelUserID' => $UserID,
    ':EmailType' => Emails::TYPE_AUTOMATIONEMAIL,
  ));
  while ($email = kt_fetch_array($result)) {
    $EmailObject = Emails::FromArray($email);
    if ($EmailObject) {
      if ($EmailObject->GetData('UseAsCRMTemplate')) {
        $options[$EmailObject->GetData('EmailID')] = $EmailObject->GetData('EmailName');
      }
    }
  }

  $form['EmailID'] = array(
    '#type' => 'select',
    '#title' => t("Email to send"),
    '#default_value' => 0,
    '#options' => $options,
    '#required' => TRUE,
    '#weight' => $weight++,
    '#quickhelp' => 'contact-sendmail-select-email',
    '#attributes' => array(
      'data-event-load' => 'on_emailid_change',
      'data-event-change' => 'on_emailid_change',
    ),
  );

  $form['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );

  if ($CanReceiveMails) {
    $form['buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Send Email'),
      '#weight' => $weight++,
    );

    $form['buttons']['preview'] = array(
      '#theme' => 'klicktipp_preview_button',
      '#title' => t('Preview'),
      // the link will be replaced in 'on_emailid_change'
      '#value' => "subscribers/$UserID",
      '#weight' => $weight++,
      '#attributes' => array('target' => '_blank'),
    );
  };

  $form['buttons']['cancel'] = array(
    '#theme' => 'klicktipp_cancel_button',
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "subscribers/$UserID",
    '#weight' => $weight++,
  );

  return $form;
}

/**
 * send email prerender
 */
function klicktipp_subscriber_sendmail_form_pre_render($form) {

  $data = $form['data']['#value'];
  $SubscriberID = $data['Subscription']['SubscriberID'];
  $account = $data['account'];
  $UserID = $account->uid;

  $form['insert']['#value'];
  // Note: it is ok, that the preview link still points to drupal, not migrated yet
  drupal_add_js('
    function on_emailid_change( element, args ) {
      var option = $("#edit-emailid option:selected").val();
      if (option == "0") {
        $("#edit-preview").hide();
      } else {
        $("#edit-preview").attr("href", "/email/'.$UserID.'/preview/" + option + "/contact/'.$SubscriberID.'");
        $("#edit-preview").show();
      };
    };
    ', array('type' => 'inline', 'scope' => 'footer'));

  return $form;
}

/**
 * subscriber edit form validate
 */
function klicktipp_subscriber_sendmail_form_validate($form, &$form_state) {

  $EmailID = $form_state['values']['EmailID'];
  if (empty($EmailID)) {
    form_set_error('EmailID', t('Please select an email to send.'));
  }

}

/**
 * subscriber edit form submit
 */
function klicktipp_subscriber_sendmail_form_submit($form, &$form_state) {

  $FullSubscriberWithFields = $form_state['values']['data']['Subscription'];
  $EmailID = $form_state['values']['EmailID'];
  $account = $form_state['values']['data']['account'];

  //TODO DEV-2430 R2 - see klicktipp_subscriber_sendmail_form
  $ReferenceID = 0;

  // prepare debug data in case of error
  $error = array(
    'values' => $form_state['values'],
    'reason' => 0,
  );

  $error = _klicktipp_subscriber_sendmail($account, $EmailID, $FullSubscriberWithFields, $ReferenceID, $error);
  if (!empty($error['reason'])) {
    Errors::unexpected('Error in send email from subscriber edit', $error, TRUE);
  }
  else {
    drupal_set_message(t('An email is sent to contact.'));
    klicktipp_set_redirect($form_state, "subscribers/{$account->uid}");
  }
}

/**
 * send email (testable)
 *
 * @param $account
 * @param $EmailID
 * @param $FullSubscriberWithFields
 * @param $ReferenceID
 * @param $error
 * @param bool $ReturnEmail
 *
 * @return mixed
 */
function _klicktipp_subscriber_sendmail($account, $EmailID, $FullSubscriberWithFields, $ReferenceID, $error, $ReturnEmail = FALSE) {

  $SubscriberID = $FullSubscriberWithFields['SubscriberID'];

  // User sender hosts check - Start
  $SenderDomains = DomainSet::GetValidSenderDomains((array) $account);
  if (empty($SenderDomains)) {
    // display error and stay in dialog
    $error['reason'] = TRANSACTION_REASON_NO_SENDER_DOMAIN;
    return $error;
  }
  // User sender hosts check - End

  // Retrieve email information - Start
  $ArrayEmail = Emails::RetrieveEmailByID($account->uid, $EmailID);
  if (empty($ArrayEmail)) {
    // display error and stay in dialog
    $error['reason'] = TRANSACTION_REASON_EMAIL_NOT_FOUND;
    return $error;
  }
  // Retrieve email information - End

  // check email subscriber - Start
  if (empty($FullSubscriberWithFields['EmailAddress'])) {
    // display error and stay in dialog
    $error['reason'] = TRANSACTION_REASON_NO_EMAIL_SUBSCRIBER;
    return $error;
  }
  // check email subscriber - End

  // check subscription - Start
  if ($FullSubscriberWithFields['SubscriptionStatus'] != Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED) {
    // display error and stay in dialog
    $error['reason'] = TRANSACTION_REASON_SUBSCRIBER_NOT_SUBSCRIBED;
    return $error;
  }
  // check subscription - End

  // find right signature for subscriber - Start
  $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($account->uid, $SubscriberID, $ReferenceID, TRUE);
  $ArraySignature = Signatures::FindSignatureByTag($ArrayEmail, $Taggings);
  if (empty($ArraySignature)) {
    // display error and stay in dialog
    $error['reason'] = TRANSACTION_REASON_SIGNATURE_NOT_FOUND;
    $error['taggings'] = $Taggings;
    return $error;
  }
  // find right signature for subscriber - End

  // send the email
  $ArrayResult = Emails::SendEmail($FullSubscriberWithFields['EmailAddress'], $account, 0, $FullSubscriberWithFields, $ReferenceID, $ArrayEmail, $ArraySignature, 'con', !empty($ArrayEmail['ReportSpamEnabled']), FALSE, $SenderDomains, 0, $ReturnEmail);

  // Report send status and errors - Start
  if ($ReturnEmail) {
    // used in test: return email content
    $error['result'] = $ArrayResult;
  }
  elseif ($ArrayResult[0] == FALSE) {
    // display error and stay in dialog
    $error['reason'] = $ArrayResult[2];
    return $error;
  }

  // get email tag
  $tag = Tag::RetrieveTagOfEntity($account->uid, Tag::CATEGORY_EMAIL_SENT, $EmailID);
  if ($tag) {
    Subscribers::TagSubscriber($account->uid, $SubscriberID, $tag['TagID'], $ReferenceID, TRUE, TRUE);
    TransactionEmails::RegisterAutomations($account->uid, $SubscriberID, $ReferenceID, TRUE);
  }

  // no error
  return $error;
}


function klicktipp_subscriber_subscriptions_form($form, &$form_state, $account, $subscriberID) {
  $userID = $account->uid;

  klicktipp_set_breadcrumb(array(
    l(t('Search Contacts'), "subscribers/$userID"),
    l($subscriberID, "subscriber/$userID/edit/$subscriberID"),
  ), drupal_get_title());

  $userGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID, TRUE);
  $referenceDisplayNames = Reference::getSubscriberRelatedDisplayNames($userID, $subscriberID);
  $displayReferences = count($referenceDisplayNames) > 1 || array_key_first($referenceDisplayNames) != 0;
  $subscriptions = array_merge(
    Subscription::RetrieveSubscriptionsByType($userID, $subscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL),
    Subscription::RetrieveSubscriptionsByType($userID, $subscriberID, Subscription::SUBSCRIPTIONTYPE_SMS)
  );

  $header = [
    [
      'data' => t('ContactInfo'),
    ],
    [
      'data' => t('Subscription'),
    ],
    [
      'data' => t('Subscription::Confirmation'),
    ],
    [
      'data' => t('Unsubscription'),
    ],
    [
      'data' => t('Activity index'),
    ],
    [
      'data' => t('Operations'),
      'class' => ['table-col-fit'],
    ]
  ];

  $icons = array(
    'status' => array(
      Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED => 'icon-status-active',
      Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED => 'icon-status-unsubscribed',
      Subscribers::SUBSCRIPTIONSTATUS_OPTIN => 'icon-status-pending',
    ),
    'bounce' => array(
      Subscribers::BOUNCETYPE_NOTBOUNCED => 'icon-status-notbounced',
      Subscribers::BOUNCETYPE_SOFT => 'icon-status-softbounce',
      Subscribers::BOUNCETYPE_SPAM => 'icon-status-spambounce',
      Subscribers::BOUNCETYPE_HARD => 'icon-status-hardbounce',
    ),
    'optional' => array(
      1 => 'icon-status-paused',
      0 => 'icon-status-play',
    )
  );

  $weight = 0;

  // we need a numeric index for, since ContactInfo can cause invalid DOM-IDs for modal boxes
  foreach (array_values($subscriptions) as $index => $subscription) {
    $referenceRelations = Subscription::getAssociatedSubscriptionReferences($userID, $subscription['SubscriptionType'], $subscription['ContactInfo']);
    $associatedDisplayNames = array_intersect_key($referenceDisplayNames, $referenceRelations);

    $activeIcon = '<i class="icon-status-paused" title="' . t('Inactive') . '"></i>';
    foreach ($associatedDisplayNames as $referenceID => $displayName) {
      if (!$referenceRelations[$referenceID]['Optional']) {
        $activeIcon = '<i class="icon-status-play" title="' . t('Active') . '"></i>';
      }
    }

    $buttons = [];
    if (klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_ADMINISTRATOR)) {
      $modalID = 'klicktipp_subscription_delete_confirm_form_' . $index;
      $buttons[] = theme('klicktipp_delete_modal_button', [
        'element' => [
          '#title' => t(KLICKTIPP_BUTTON_TEXT_DELETE),
          '#table' => TRUE,
          '#value' => $modalID,
          '#weight' => $weight++,
          '#external' => FALSE,
        ]
      ]);

      // since we render button with theme method (so in fact we skip render logic of form) we need an extra element rendering modal confirm
      $form['klicktipp_delete_modal_dialog_' . $index] = [
        '#type' => 'value',
        '#value' => '',
        '#modal_confirm' => drupal_get_form($modalID, $account, $subscription, $index)
      ];
    }

    if ($subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED) {
      $modalID = 'klicktipp_subscription_unsubscribe_confirm_form_' . $index;
      $buttons[] = theme('klicktipp_unsubscribe_modal_button', [
        'element' => [
          '#title' => t('Unsubscribe'),
          '#table' => TRUE,
          '#value' => $modalID,
          '#weight' => $weight++,
          '#external' => FALSE,
        ]
      ]);
      // since we render button with theme method (so in fact we skip render logic of form) we need an extra element rendering modal confirm
      $form['klicktipp_unsubscribe_modal_dialog_' . $index] = [
        '#type' => 'value',
        '#value' => '',
        '#modal_confirm' => drupal_get_form($modalID, $account, $subscription, $index),
      ];
    }

    if ($subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL && in_array(
        $subscription['BounceType'],
        [
          Subscribers::BOUNCETYPE_HARD,
          Subscribers::BOUNCETYPE_SOFT,
          Subscribers::BOUNCETYPE_SPAM,
        ]
      )
    ) {

      $maxBounceResetsPerDay = $userGroup['Data']['LimitBounceResets'];
      $totalBounceResets = Statistics::RetrieveActivityCountPerDayOfUser($userID, 'TotalBounceResets'); //for the current day
      if ($totalBounceResets < $maxBounceResetsPerDay) {
        $modalID = 'klicktipp_subscription_resetbounce_confirm_form_' . $index;
        $buttons[] = theme('klicktipp_unsubscribe_modal_button', [
          'element' => [
            '#title' => t('Reset bounce'),
            '#table' => TRUE,
            '#value' => $modalID,
            '#weight' => $weight++,
            '#external' => FALSE,
          ]
        ]);

        // since we render button with theme method (so in fact we skip render logic of form) we need an extra element rendering modal confirm
        $form['klicktipp_resetbounce_modal_dialog_' . $index] = [
          '#type' => 'value',
          '#value' => '',
          '#modal_confirm' => drupal_get_form($modalID, $account, $subscription, $index),
        ];
      }
    }

    $changeFeature = $subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL ?
      'change subscriber email address' :
      'access sms marketing';

    if ($subscription['SubscriptionStatus'] != Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED &&
      klicktipp_feature_access($account, $changeFeature)
    ) {
      $buttons[] = theme('klicktipp_edit_table_button', [
        'element' => [
          '#title' => t('Edit'),
          '#table' => TRUE,
          '#value' => "contacts/{$account->uid}/edit/{$subscription['SubscriptionType']}/" . $subscription['ContactInfo'],
          '#external' => FALSE,
          '#attributes' => [
            'target' => '_blank',
          ],
        ]
      ]);
    }

    $tooltipStatus = t(/*ignore*/Subscribers::$DisplaySubscriptionStatus[$subscription['SubscriptionStatus']]);
    $tooltipBounce = t(/*ignore*/Subscribers::$TranslationBounceTypes[$subscription['BounceType']]);

    $contactInfoData  = '<i class="' . $icons['status'][$subscription['SubscriptionStatus']] . '" title="' . $tooltipStatus . '"></i>'
      . '<i class="' . $icons['bounce'][$subscription['BounceType']] . '" title="' . $tooltipBounce . '"></i>'
      . "$activeIcon"
      . Subscribers::DepunycodeEmailAddress($subscription['ContactInfo']);
    $activityIndex = '&mdash;';
    if ($subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
      $activityIndex = round(Subscription::calculateActivityIndex($subscription));
    }

    $row = [
      [
        'data' => $contactInfoData,
      ],
      [
        'data' => !empty($subscription['OptInDate']) ? Dates::formatDate(Dates::FORMAT_DMY, (int) $subscription['OptInDate']) : '&mdash;',
      ],
      [
        'data' => ($subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL && !empty($subscription['SubscriptionDate'])) ? Dates::formatDate(Dates::FORMAT_DMY, (int) $subscription['SubscriptionDate']) : '&mdash;',
      ],
      [
        'data' => !empty($subscription['UnsubscriptionDate']) ? Dates::formatDate(Dates::FORMAT_DMY, (int) $subscription['UnsubscriptionDate']) : '&mdash;'
      ],
      [
        'data' => $activityIndex,
        'class' => ['activity-index']
      ],
      [
        'data' => implode(' ', $buttons),
        'class' => ['table-col-fit'],
      ]
    ];
    $rows[] = $row;
  }

  $form['UserID'] = [
    '#type' => 'value',
    '#value' => $userID
  ];

  $form['SubscriberID'] = [
    '#type' => 'value',
    '#value' => $subscriberID
  ];

  $createButtons = [
    [
      '#title' => t('Action::Add::Subscriptions'),
      '#attributes' => ['onclick' => "document.getElementById('new-subscription-form').scrollIntoView({ behavior: 'smooth' });$('#new-subscription-form .accordion-toggle').click();return false;"]
    ],
  ];

  $form['create_buttons'] = [
    '#theme' => 'klicktipp_create_buttons',
    '#buttons' => $createButtons,
    '#weight' => $weight++,
    '#prefix' => '<div class="create-buttons"><div class="clearfix"><div class="right">',
    '#suffix' => '</div></div></div>',
  ];

  if (empty($rows)) {
    $form['empty'] = [
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => t('There are currently no subscriptions.'),
    ];
  } else {
    $form['Subscription'] = [
      '#type' => 'markup',
      '#weight' => $weight++,
      '#value' => theme('table', [
        'header' => $header,
        'rows' => $rows,
        'attributes' => [
          'class' => ['table-buttons'],
          'data-e2e-id' => 'table-subscriber-subscriptions-overview'
        ]
      ]),
    ];
  }
  $form['NewSubscription'] = array(
    '#type' => 'fieldset',
    '#title' => t("New subscription"),
    '#weight' => $weight++,
    '#collapsible' => TRUE,
    '#collapsed' => TRUE,
    '#attributes' => ['id' => 'new-subscription-form']
  );

  $subscriptionTypeOptions = [
    Subscription::SUBSCRIPTIONTYPE_EMAIL => t('Email Address'),
    Subscription::SUBSCRIPTIONTYPE_SMS => t('SMS Phone number'),
  ];
  $form['NewSubscription']['SubscriptionType'] = array(
    '#type' => 'select',
    '#title' => t('Type of contact'),
    '#options' => $subscriptionTypeOptions,
    '#attributes' =>  ['onchange' => '$("#edit-contactinfo-wrapper label[for=edit-contactinfo]").contents()[0].textContent = $("option:selected", this).text() + " "'],
    '#weight' => $weight++,
  );

  $subscriptionTypeTitle = empty($form_state['input']['SubscriptionType']) ? reset($subscriptionTypeOptions) : $subscriptionTypeOptions[$form_state['input']['SubscriptionType']];
  $form['NewSubscription']['ContactInfo'] = array(
    '#type' => 'textfield',
    '#title' => $subscriptionTypeTitle,
    '#weight' => $weight++,
    '#required' => TRUE,
  );

  $form['NewSubscription']['buttons'] = array(
    '#type' => 'markup',
    '#weight' => $weight++,
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
  );
  $form['NewSubscription']['buttons']['CreateSubscription'] = array(
    '#theme' => 'klicktipp_submit',
    '#value' => t('Add Contact'),
    '#type' => 'submit',
    '#weight' => $weight++,
  );
  return $form;
}

function klicktipp_subscriber_subscriptions_form_validate(&$form, &$form_state) {
  $userID = $form_state['values']['UserID'];
  $contactInfo = trim($form_state['values']['ContactInfo']);
  $subscriptionType = $form_state['values']['SubscriptionType'];
  if (empty($contactInfo)) {
    // field is required, expand fieldset to show red highlighted field
    $form['NewSubscription']['#collapsed'] = false;
    return;
  }
  if ($subscriptionType == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
    if (!Subscribers::ValidateEmailAddress($contactInfo, $form_state['values']['UserID'])) {
      form_set_error('ContactInfo', t('Invalid email address'));
    }
  } elseif (empty(Subscribers::FormatSMSNumber($contactInfo))) {
    form_set_error('ContactInfo', t('Invalid number %number.', ['%number' => $contactInfo]));
  }
  if(Subscription::RetrieveSubscriptionByContactInfo($userID, $contactInfo, $subscriptionType)) {
    form_set_error('ContactInfo', t('This subscription already exists for this user.'));
  }
  if (form_get_errors()) {
    // field is required, expand fieldset to show red highlighted field
    $form['NewSubscription']['#collapsed'] = false;
  }
}

function klicktipp_subscriber_subscriptions_form_submit($form, &$form_state) {
  $userID = $form_state['values']['UserID'];
  $subscriberID = $form_state['values']['SubscriberID'];
  $contactInfo = trim($form_state['values']['ContactInfo']);
  $subscriptionType = $form_state['values']['SubscriptionType'];


  //import list settings
  $ArraySubscriberList = array(
    'ListID' => 0,
    'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
  );

  $Parameters = array(
    'UserID' => $userID,
    'SubscriberID' => $subscriberID,
    'ListInformation' => $ArraySubscriberList,
    'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    'TriggerAutoResponders' => TRUE,
    'InstantTrigger' => TRUE,
  );

  if ($subscriptionType == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
    $Parameters['EmailAddress'] = $contactInfo;
  } else {
    $Parameters['PhoneNumber'] = $contactInfo;
  }

  $ArrayReturn = Subscribers::Subscribe($Parameters);

  if (!$ArrayReturn[0]) {
    //error

    switch ($ArrayReturn[1]) {
      case Subscribers::SUBSCRIBE_ERROR_INVALID_EMAIL_ADDRESS:
        $error_message = t('Unable to add contact due to an invalid email address (%email).', array('%email' => Subscribers::DepunycodeEmailAddress($contactInfo)));
        break;
      case Subscribers::SUBSCRIBE_ERROR_UNSUBSCRIBED_EMAIL_ADDRESS:
        $error_message = t('The contact %email is already unsubscribed from your contact cloud and cannot be added again.', array('%email' => Subscribers::DepunycodeEmailAddress($contactInfo)));
        break;
      default:
        //Subscribers::SUBSCRIBE_ERROR_INVALID_USER_INFORMATION
        //Subscribers::SUBSCRIBE_ERROR_INVALID_LIST_INFORMATION
        //Subscribers::SUBSCRIBE_ERROR_CUSTOMFIELD_DATA
        // => out of users control, show "unexpected error" message
        $error_message = t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR);
        watchdog("kt-controller", "Error: Error while adding contact %email (original: %origEmail) for user %uid. Subscribers::Subscribe() error code: %code", array(
          '%email' => Subscribers::DepunycodeEmailAddress($contactInfo),
          '%origEmail' => $contactInfo,
          '%uid' => $userID,
          '%code' => $ArrayReturn[1],
        ), WATCHDOG_ERROR);
    }

    drupal_set_message($error_message, 'error');

  }
  else {
    Statistics::UpdateActivityStatistics($userID, array('TotalImport' => 1)); //increase by 1
    drupal_set_message(t('Contact successfully added.'));
  }
}

function klicktipp_subscription_delete_confirm_form($form, $form_state, $account, $subscription, $index) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $Title = t('Delete Contact');

  $Message = t("A JavaScript could not be executed.");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscription_delete_confirm_form_' . $index;
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message);

  //alter the standard delete button to show the number of subscribers to be deleted
  $SubmitButtonText = t("Delete");
  $form['ModalButtons']['Submit']['#value'] = $SubmitButtonText;

  $form['account'] = array(
    '#type' => 'value',
    '#value' => $account,
  );

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $account->uid,
  );

  $form['Subscription'] = array(
    '#type' => 'value',
    '#value' => $subscription,
  );



  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";
  $form['#attributes']['data-event-load-args'] = 'SubscriberSearchModalDelete' . $index;

  //implement the onBeforeShow JavaScript callback
  //the form #suffix already contains HTML -> append the script
  $form['#suffix'] .= "
    <script type='text/javascript'>

      window['SubscriberSearchModalDelete$index'] = function ( element ) {

        //get the modal container ID from the button
        var dialog = $('#$ModalID');

        var contactInfo = '{$subscription['ContactInfo']}';
        //make sure the submit button and warning are visible
        $('input[name=op], .modal-warning').show();



        var Message = dialog.find('p.modal-message');
        var Button = dialog.find('input[name=op]');

        Message.html('" . t('Are you sure to delete @contactInfo?') . "'.replace('@contactInfo', contactInfo));
        Button.val('" . t('I would like to delete @contactInfo') . "'.replace('@contactInfo', contactInfo));

        //after clicking (before submitting the form) change the button text back to the original text, so we pass Drupals validation
        Button.one('click', function () { $(this).val('$SubmitButtonText')});

      }

    </script>
  ";

  return $form;
}

function klicktipp_subscription_delete_confirm_form_submit($form, &$form_state) {
  $account = $form_state['values']['account'];
  $subscriptionData = $form_state['values']['Subscription'];
  $subscription = new Subscription($subscriptionData);
  $subscription->deleteWithSpamCheck($account);

  drupal_set_message(t('Subscription successfully deleted'));


  klicktipp_set_redirect($form_state, "/subscriber/{$account->uid}/edit/{$subscriptionData['SubscriberID']}/subscriptions");
}

function klicktipp_subscription_unsubscribe_confirm_form($form, $form_state, $account, $subscription, $index) {
  // prevent that SearchSettings get overwritten by form rebuild
  $form_state['cache'] = TRUE;

  $Title = t('Unsubscribe Contact');

  $Message = t("A JavaScript could not be executed.");

  //generate the modal dialog + form
  $ModalID = 'klicktipp_subscription_unsubscribe_confirm_form_' . $index;
  $form = _klicktipp_confirm_form_modal($ModalID, '', $Title, TRUE, $Message);

  //alter the standard delete button to show the number of subscribers to be deleted
  $SubmitButtonText = t("Delete");
  $form['ModalButtons']['Submit']['#value'] = $SubmitButtonText;

  $form['UserID'] = array(
    '#type' => 'value',
    '#value' => $account->uid,
  );

  $form['Subscription'] = array(
    '#type' => 'value',
    '#value' => $subscription,
  );



  //add javascript to initialize the modal dialog
  //on-document-load-function to call (@see the themes scripts.js)
  //js_modal_init() adds the onBeforeShow-Event if data-event-load-args contains a JavaScript callback
  $form['#attributes']['data-event-load'] = "js_modal_init";
  $form['#attributes']['data-event-load-args'] = 'SubscriberSearchModalUnsubscribe' . $index;

  //implement the onBeforeShow JavaScript callback
  //the form #suffix already contains HTML -> append the script
  $form['#suffix'] .= "
    <script type='text/javascript'>

      window['SubscriberSearchModalUnsubscribe$index'] = function ( element ) {

        //get the modal container ID from the button
        var dialog = $('#$ModalID');

        var contactInfo = '{$subscription['ContactInfo']}';
        //make sure the submit button and warning are visible
        $('input[name=op], .modal-warning').show();



        var Message = dialog.find('p.modal-message');
        var Button = dialog.find('input[name=op]');

        Message.html('" . t('Are you sure to unsubscribe @contactInfo?') . "'.replace('@contactInfo', contactInfo));
        Button.val('" . t('I would like to unsubscribe @contactInfo') . "'.replace('@contactInfo', contactInfo));

        //after clicking (before submitting the form) change the button text back to the original text, so we pass Drupals validation
        Button.one('click', function () { $(this).val('$SubmitButtonText')});

      }

    </script>
  ";

  return $form;

}

function klicktipp_subscription_unsubscribe_confirm_form_submit($form, &$form_state) {
  $UserID = $form_state['values']['UserID'];

  $subscription = $form_state['values']['Subscription'];

  $result = Subscribers::Unsubscribe($UserID, $subscription['ContactInfo'], 0, '0.0.0.0', $subscription['SubscriptionType'], true);
  if ($result[0]) {
    drupal_set_message(t('Contact successfully unsubscribed'));
  } else {
    drupal_set_message(t('An error occured while unsubscribing. Please try again or contact our support.'), 'error');
  }

  klicktipp_set_redirect($form_state, "/subscriber/$UserID/edit/{$subscription['SubscriberID']}/subscriptions");
}

function klicktipp_subscriber_edit_contactinfo_form($form, $form_state, $account, $subscriptionType, $contactInfo) {
  $userID = $account->uid;

  $subscription = Subscription::RetrieveSubscriptionByContactInfo($userID, $contactInfo, $subscriptionType);
  if (!$subscription) {
    drupal_not_found();
    drupal_exit();
  }

  klicktipp_set_breadcrumb(array(l(t('Search Contacts'), "subscribers/$userID")), drupal_get_title());

  $form = [];

  $form['subscription'] = [
    '#type' => 'value',
    '#value' => $subscription,
  ];

  $form['buttons'] = array(
    '#type' => 'markup',
    '#prefix' => '<div class="button-row">',
    '#suffix' => '</div>',
    '#weight' => 2,
  );

  $form['buttons']['cancel'] = array(
    '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
    '#value' => "subscriber/$userID/edit/{$subscription['RelSubscriberID']}/subscriptions",
    '#theme' => 'klicktipp_cancel_button',
    '#weight' => 4, // let space for submit button
  );

  if ($subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL && !klicktipp_feature_access($account, 'change subscriber email address')) {
    $form['AccessError'] = array(
      '#type' => 'markup',
      '#value' => '<p>' .
        t('You are not allowed to change email addresses. Please contact our support team.') .
        '</p>',
    );

    return $form;
  } elseif ($subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_SMS && !klicktipp_feature_access($account, 'access sms marketing')) {
    $form['AccessError'] = array(
      '#type' => 'markup',
      '#value' => '<p>' .
        t('You are not allowed to change sms phone numbers. Please contact our support team.') .
        '</p>',
    );

    return $form;
  }

  $subscriptionStatus = $subscription['SubscriptionStatus'];
  if ($subscriptionStatus == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED) {
    $form['SubscriptionError'] = array(
      '#type' => 'markup',
      '#value' => '<p>' .
        t('It is not allowed to change contact info of an unsubscribed contact.') .
        '</p>',
    );

    return $form;
  }

  if (klicktipp_subscriber_edit_contactinfo_form_check_import_limit($form, $subscription, $account)) {
    $contactInfoTitle = $subscriptionType == Subscription::SUBSCRIPTIONTYPE_EMAIL ? t('Email Address') : t('SMS Phone number');

    if ($subscriptionType == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
      $contactInfo = Subscribers::DepunycodeEmailAddress($contactInfo);
    }

    $form['NewContactInfo'] = array(
      '#type' => 'textfield',
      '#default_value' => $form_state['input']['NewContactInfo'] ?: $contactInfo,
      '#title' => $contactInfoTitle,
      '#required' => TRUE,
    );

    $form['buttons']['submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_submit',
      '#value' => t('Save'),
      '#weight' => 3,
    );
  }

  return $form;
}

function klicktipp_subscriber_edit_contactinfo_form_validate(&$form, &$form_state) {
  $subscription = $form_state['values']['subscription'];
  $oldContactInfo = trim($subscription['ContactInfo']);
  $contactInfo = trim($form_state['values']['NewContactInfo']);

  $subscriptionType = $subscription['SubscriptionType'];

  if ($subscriptionType == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
    $form_state['values']['NewContactInfo'] = $formattedContactInfo = Subscribers::PunycodeEmailAddress($contactInfo);
  } else {
    $form_state['values']['NewContactInfo'] = $formattedContactInfo = Subscribers::FormatSMSNumber($contactInfo);
  }

  // contact info unchanged. we handle it like click on cancel button
  if ($oldContactInfo == $formattedContactInfo) {
    drupal_goto($form['buttons']['cancel']['#value']);
  }

  $userID = $subscription['RelOwnerUserID'];

  $contactInfoValid = TRUE;
  if ($subscriptionType == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
    if (!Subscribers::ValidateEmailAddress($formattedContactInfo, $userID)) {
      $contactInfoValid = FALSE;
      form_set_error('ContactInfo', t('Invalid email address'));
    }
  } elseif (empty($formattedContactInfo)) {
    // $contactInfo empty => Subscribers::FormatSMSNumber detected invalid sms number
    $contactInfoValid = FALSE;
    form_set_error('ContactInfo', t('Invalid number %number.', ['%number' => $contactInfo]));
  }
  if($contactInfoValid && Subscription::RetrieveSubscriptionByContactInfo($userID, $formattedContactInfo, $subscriptionType)) {
    form_set_error('ContactInfo', t('This subscription already exists for this user.'));
  }
}

function klicktipp_subscriber_edit_contactinfo_form_submit($form, &$form_state) {
  $subscription = $form_state['values']['subscription'];

  $oldContactInfo = trim($subscription['ContactInfo']);
  $contactInfo = trim($form_state['values']['NewContactInfo']);

  $userID = $subscription['RelOwnerUserID'];
  $subscriberID = $subscription['RelSubscriberID'];
  $subscriptionType = $subscription['SubscriptionType'];

  if ($subscriptionType == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
    $contactInfoField = 'EmailAddress';
  } else {
    $contactInfoField = 'PhoneNumber';
  }

  $parameters = [
    $contactInfoField => $contactInfo,
    'Old' . $contactInfoField => $oldContactInfo,
    'InstantTrigger' => true,
  ];

  $UseDoubleOptinProcess = Subscribers::DOI_PROCESS_USE_SINGLE_OPTIN;
  $ArrayReturn = Subscribers::UpdateSubscription($userID, $subscriberID, $parameters, $UseDoubleOptinProcess);

  if (!$ArrayReturn['Success']) {
    //error

    switch ($ArrayReturn['ErrorCode']) {
      case Subscribers::SUBSCRIBE_ERROR_INVALID_EMAIL_ADDRESS:
        $error_message = t('Unable to add contact due to an invalid email address (%email).', array('%email' => $contactInfo));
        break;
      case Subscribers::SUBSCRIBE_ERROR_INVALID_PHONE_NUMBER:
        $error_message = t('Unable to add contact due to an invalid phonenumber (%number).', array('%number' => $contactInfo));
        break;
      case Subscribers::SUBSCRIBE_ERROR_UNSUBSCRIBED_EMAIL_ADDRESS:
      case Subscribers::SUBSCRIBE_ERROR_UNSUBSCRIBED_PHONE_NUMBER:
        $error_message = t('The contact %contact is already unsubscribed from your contact cloud and cannot be added again.', ['%contact' => $contactInfo]);
        break;
      case KLICKTIPPAPI_ERROR_EMAILADDRESS_CHANGE_NOT_ALLOWED:
        $error_message = t('The contact %email is not acceptable for subscription.', array('%email' => $contactInfo));
        break;
      default:
        // => out of users control, show "unexpected error" message
        $error_message = t(/*ignore*/KLICKTIPP_UNEXPECTED_ERROR);
        watchdog("kt-controller", "Error: Error while adding contact %contactInfo (original: %origEmail) for user %uid. Subscribers::Subscribe() error code: %code", array(
          '%contactInfo' => $contactInfo,
          '%origContactInfo' => $oldContactInfo,
          '%uid' => $userID,
          '%code' => $ArrayReturn[1],
        ), WATCHDOG_ERROR);
    }

    drupal_set_message($error_message, 'error');

  }
  else {
    if ($subscription['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_EMAIL) {
      Statistics::UpdateActivityStatistics($userID, array('TotalImport' => 1)); //increase by 1
    }
    drupal_set_message(t('The Digital ID has successfully been updated.'));
    drupal_goto($form['buttons']['cancel']['#value']);
  }
}

function klicktipp_subscriber_edit_contactinfo_form_check_import_limit(&$form, $subscription, $account) {
  if ($subscription['SubscriptionType'] != Subscription::SUBSCRIPTIONTYPE_EMAIL) {
    return TRUE;
  }
  $userGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID, TRUE);
  $totalImport = Statistics::RetrieveTotalImportPerDayOfUser($account->uid); //for the current day

  // max import for new products is tier
  if (!empty($userGroup['Data']['TierInAccount'])) {

    $maxImportsPerDay = UserGroups::GetContactLimit($account, $userGroup);

    if (empty($maxImportsPerDay)) {

      $form['TierError'] = array(
        '#type' => 'markup',
        '#value' => '<p>' .
          t("There was an error with you import limit. Please contact our support team.") .
          '</p>',
      );

      return FALSE;
    }

  } else {

    // max import for old products is in LimitImports of the group
    $maxImportsPerDay = $userGroup['Data']['LimitImports'];
  }

  if ($totalImport >= $maxImportsPerDay) {

    $form['MaxImportsReached'] = array(
      '#type' => 'markup',
      '#value' => '<p>' .
        t('You have reached your import limit of %MaxImport contacts per today. Tomorrow you can add another %MaxImport contacts.', array('%MaxImport' => $maxImportsPerDay)) .
        '</p>',
    );
    return FALSE;
  }
  return TRUE;
}

<?php

use App\Klicktipp\VarDataProcessingOrder;
use App\Security\Keycloak\KeycloakApi;

/**
 * /tm4j/me/account-login
 * For a given account
 * - reset to first login status
 * - reset to default password
 * - generate password reset links
 * @param $form
 * @param $form_state
 *
 * @return mixed
 */
function klicktipp_tm4j_account_reset_form($form, &$form_state) {

  $resetLink = function($account, $mode = '') {

    $pass = $account->pass;
    $timestamp = time();
    $hashTimestamp = $timestamp;

    if ($mode === 'expired') {
      $timestamp = strtotime('-1 week', REQUEST_TIME);
    }
    elseif ($mode === 'invalid') {
      $pass = 'invalid';
    }

    return url("user/reset/$account->uid/$timestamp/" . user_pass_rehash($pass, $hashTimestamp, $account->login, $account->uid), array('absolute' => TRUE));

  };

  $tm4jSettingsLink = l('TM4j Account Login Settings', 'admin/config/klicktipp/marketing/account-login', array(
    'attributes' => array('target' => '_blank')
  ));

  $form['intro'] = array(
    '#type' => 'item',
    '#theme' => 'klicktipp_info',
    '#value' => 'This dialog provides reset links and helps to reset accounts for the Account-Login TM4J Testcases.<br />' .
      "You can find the settings for this dialog here: $tm4jSettingsLink"
  );

  $loginSettingsKey = 'klicktipp_marketing_account_login_settings';
  $loginSettings = variable_get($loginSettingsKey, array());

  $resetPassword = $loginSettings['tm4j']['account_password'];

  if (empty($resetPassword)) {
    $form['password_hint'] = array(
      '#type' => 'item',
      '#theme' => 'klicktipp_info',
      '#value' => '<strong>The account reset functionality is not available since no default password has been set.<br />' .
        "You can set a default password here: $tm4jSettingsLink</strong>"
    );
  }
  else {

    $tm4jAccountID = $loginSettings['tm4j']['account'];

    $account = user_load($tm4jAccountID);

    if ($account) {

      $url = $resetLink($account);
      $markup = "<p>Reset Password (valid): <a href='$url'>$url</a></p>";

      $url = $resetLink($account, 'expired');
      $markup .= "<p>Reset Password (expired): <a href='$url'>$url</a></p>";

      $url = $resetLink($account, 'invalid');
      $markup .= "<p>Reset Password (invalid): <a href='$url'>$url</a></p>";

      try {
        $keycloakApi = ServiceContainer::getInstance()->get(KeycloakApi::class);
        $keycloakId = $keycloakApi->getUserIdByName($account->name);
        $keycloakApi->updateUser($keycloakId, ['requiredActions' => ['UPDATE_PASSWORD']]);
        $url = $keycloakApi->generateMagicLink($account->mail);
        $markup .= "<p>Reset Password (KeyCloak): <a href='$url'>$url</a></p>";
      } catch (Throwable $e) {
        $markup .= "<p>Reset Password (KeyCloak): Error: " . $e->getMessage() . "</p>";
      }

      $form['ResetLinks'] = array(
        '#type' => 'markup',
        '#markup' => $markup
      );

      if (!empty($resetPassword)) {

        $form["first_login"] = array(
          '#type' => 'submit',
          '#theme' => 'klicktipp_submit',
          '#value' => "Reset to first login",
          '#prefix' => '<div">',
          '#suffix' => '</div>',
        );

      }

    }
    else {
      $form['#description'] = 'The account does not exist.';
    }

  }


  return $form;

}

function klicktipp_tm4j_account_reset_form_submit($form, &$form_state) {

  $loginSettingsKey = 'klicktipp_marketing_account_login_settings';
  $loginSettings = variable_get($loginSettingsKey, array());

  $UserID = $loginSettings['tm4j']['account'];
  $account = user_load($UserID);

  if (!$account) {
    drupal_set_message("TM4J account $UserID not found!", 'error');
    return;
  }

  $updateAccount = array(
    'login' => 0,
    'created' => time(),
    'changed' => 0,
    'access' => 0,
    'firstLogin' => []
  );

  user_save($account, $updateAccount);

  //delete DPO variable
  VarDataProcessingOrder::DeleteVariable($account->uid);

  klicktipp_user_cache_clear($account->uid);

  drupal_set_message('The account has been successfully reset.');

}

<?php

$schema['addon_entities'] = array(
  'description' => 'Table containing information about addon entities',
  'fields' => array(
    'RelOwnerUserID' => array(
      'description' => 'The user, who owns the entity',
      'type' => 'int',
      'not null' => TRUE,
      'unsigned' => TRUE,
    ),
    'AddonType' => array(
      'description' => 'Type of an addon (e.g. Landing page plus)',
      'type' => 'int',
      'not null' => TRUE,
      'unsigned' => TRUE,
    ),
    'EntityType' => array(
      'description' => 'Type of entity (e.g. Email, LandingPage, Campaign)',
      'type' => 'int',
      'not null' => TRUE,
      'unsigned' => TRUE,
    ),
    'RelEntityID' => array(
      'description' => 'Identifier of entity',
      'type' => 'int',
      'not null' => TRUE,
      'unsigned' => TRUE,
    ),
    'UpsellStatus' => array(
      'description' => 'Status (e.g. paid, pending)',
      'type' => 'int',
      'size' => 'tiny',
      'not null' => TRUE,
      'unsigned' => TRUE,
    ),
    'CompletedAt' => array(
      'description' => 'Datetime of payment completion',
      'type' => 'int',
      'default' => 0,
      'not null' => TRUE,
      'unsigned' => TRUE,
    ),
    'CanceledAt' => array(
      'description' => 'Datetime of cancellation',
      'type' => 'int',
      'default' => 0,
      'not null' => TRUE,
      'unsigned' => TRUE,
    ),
  ),
  'unique keys' => array(
    'unique keys' => array('RelOwnerUserID', 'AddonType', 'EntityType', 'RelEntityID'),
  ),
  'mysql_engine' => 'InnoDB',
);
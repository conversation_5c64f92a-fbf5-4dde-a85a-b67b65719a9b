<?php

use App\Security\Oidc\OidcClient;

// Klicktipp Bootstrap theme
define('KLICKTIPP_THEME_BOOTSTRAP_CDN_JAVASCRIPT', 'klicktipp_theme_bootstrap_javascript');
define('KL<PERSON>KTIPP_THEME_BOOTSTRAP_CDN_CSS', 'klicktipp_theme_bootstrap_css');
define('KLICKTIPP_THEME_CKEDITOR_CDN_EDITOR', 'klicktipp_theme_ckeditor_cdn_editor');
define('KLICKTIPP_THEME_CKEDITOR_CDN_JQUERY_ADAPTER', 'klicktipp_theme_ckeditor_cdn_jquery_adapter');
define('KLICKTIPP_THEME_COLORS', 'klicktipp_theme_colors');
define('KLICKTIPP_THEME_GOOGLE_SCRIPTS', 'klicktipp_theme_google_scripts');
define('KLICKTIPP_THEME_WHITELABEL_MENU', 'klicktipp_theme_whitelabel_menu');
define('KL<PERSON><PERSON><PERSON><PERSON>_THEME_FOOTER_MENU', 'klicktipp_theme_footer_menu');

// Klicktipp filter
define('KLICKTIPP_SPAMASSASSIN', 'klicktipp_spamassassin_params');
define('KLICKTIPP_SPAMSCORE_EMAIL', 'klicktipp_spamscore_email');
define('KLICKTIPP_SPAMSCORE_SIGNATURE', 'klicktipp_spamscore_signature');
define('KLICKTIPP_AGED_MAXFACTOR', 'klicktipp_aged_maxfactor');

// verify email
define('KLICKTIPP_VERIFYEMAIL_URL', 'klicktipp_verifyemail_url');

//CKFinder
define('KLICKTIPP_RESOURCE_CKFINDER_LICENSENAME', 'klicktipp_resource_ckfinder_licensename');
define('KLICKTIPP_RESOURCE_CKFINDER_LICENSEKEY', 'klicktipp_resource_ckfinder_licensekey');
define('KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_IMAGE_TYPES', 'klicktipp_resource_ckfinder_allowed_image_types');
define('KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_IMAGE_FILESIZE', 'klicktipp_resource_ckfinder_allowed_image_filesize');
define('KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_FILE_TYPES', 'klicktipp_resource_ckfinder_allowed_file_types');
define('KLICKTIPP_RESOURCE_CKFINDER_ALLOWED_FILE_FILESIZE', 'klicktipp_resource_ckfinder_allowed_file_filesize');

//Pagination
define('KLICKTIPP_PAGER_MAX_PAGINATION_ITEM', 10);
define('KLICKTIPP_PAGER_PAGE_SIZES', "10,25,50,100");
define('KLICKTIPP_PAGER_DEFAULT_PAGE_SIZE', 10);

define('KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER', 'klicktipp_settings_optin_confirmation_email_provider');

define('KLICKTIPP_QUICKHELP_REFERENCE', 'klicktipp_quickhelp'); //variable_get ID
define('KLICKTIPP_SPAMASSASSIN_QUICKHELPS', 'klicktipp_spamassassin_quickhelps'); //variable_get ID
define('KLICKTIPP_HELP_REFERRER', 'klicktipp_help_referrer'); //variable_get ID
define('KLICKTIPP_HELP_REFERRER_COOKIE', 'KTHELPREF'); //cookie ID
define('KLICKTIPP_HELP_VOCABULARY_ID', 'klicktipp_help_vocabulary_id');

define('KLICKTIPP_SENDERSCORE_HOSTS', 'klicktipp_senderscore_hosts'); //variable_get ID

define('KLICKTIPP_WIZARD_CREATE_EMAIL', '1');

define('KLICKTIPP_NUMBER_FORMAT_DECIMAL_POINT', 'klicktipp_number_format_decimal_point');
define('KLICKTIPP_NUMBER_FORMAT_THOUSANDS_SEPARATOR', 'klicktipp_number_format_thousands_separator');

// Klick-Tipp Modal Delete Confirm Dialog messages
define("KLICKTIPP_DELETE_CONFIRM_MESSAGE_WARNING", /*t(*/"Note: This action can not be undone!"/*)*/);
define("KLICKTIPP_DELETE_CONFIRM_MESSAGE_ASSURE", /*t(*/"Are you sure to delete %name?"/*)*/);
define("KLICKTIPP_DELETE_CONFIRM_BUTTON_DELETE", /*t(*/"Delete"/*)*/);
define("KLICKTIPP_DELETE_CONFIRM_BUTTON_CANCEL", /*t(*/"Cancel"/*)*/);

define("KLICKTIPP_BUTTON_TEXT_DELETE", /*t(*/'Delete'/*)*/);
define("KLICKTIPP_BUTTON_TEXT_CANCEL", /*t(*/'Cancel'/*)*/);
define("KLICKTIPP_BUTTON_TEXT_BACK", /*t(*/'Back'/*)*/);
define("KLICKTIPP_BUTTON_TEXT_RESET", /*t(*/'Reset'/*)*/);
define("KLICKTIPP_BUTTON_TEXT_APPLY_FILTER", /*t(*/'Apply filter'/*)*/);

// dialogs with cached user data has this info on the bottom
define("KLICKTIPP_CLEAR_USER_CACHE_INFO", /*t(*/'To provide fastest system response all data is delayed for some minutes. You may get the latest data by clicking on this !link.'/*)*/);

//error messag for unexpected error
define("KLICKTIPP_UNEXPECTED_ERROR", /*t(*/'An unexpected error occurred. A notification has been sent to our tech support.'/*)*/);

//klicktipp default values
define("KLICKTIPP_DEFAULTS_EMAIL_LINEBREAKS", 65);
define("KLICKTIPP_DEFAULTS_NEWSLETTER_FILTER", 'Draft');
define("KLICKTIPP_DEFAULTS_HISTORY_FILTER", 'all');
define("KLICKTIPP_DEFAULTS_MARKETINGTOOLS_FILTER", 'all');

//klicktipp user session  flags
define("KLICKTIPP_SESSION_USER_FLAGS", 'klicktipp_session_user_flags');
define("KLICKTIPP_ACCOUNT_TWOFACTORAUTH_REMINDER", "tfa_reminder");
define('KLICKTIPP_USER_COOKIE', 'has_ktp_user_id');
/**
 * pantheon filters cookies without SESS-prefix
 * remaining text must be lowercase
 * @see https://pantheon.io/docs/caching-advanced-topics#using-your-own-session-style-cookies
 */
define('KLICKTIPP_MAIN_ACCOUNT_ID_COOKIE', 'SESSktsmai');

//Klick-Tipp Data Processing Order
define('KLICKTIPP_ACCOUNT_DATA_PROCESSING_ORDER_PRODUCT_ID', 'klicktipp_account_dpo_product_id'); // the amember produkt id for DPO
define('KLICKTIPP_ACCOUNT_DATA_PROCESSING_ORDER_ID', 'dpo-reminder'); // id for SESSION flag, Ajax operation and drupal_set_message type
define('KLICKTIPP_ACCOUNT_DATA_PROCESSING_ORDER_SUBSCRIBER_MINIMUM', 200); // do not show reminder if user has less subscribers


//klicktipp content_includes @see _klicktipp_get_content_include
define("KLICKTIPP_CONTENT_INCLUDE_FRONTPAGE_WELCOME", 'klicktipp_content_include_frontpage_welcome');
define("KLICKTIPP_CONTENT_INCLUDE_PARTNERINFO", 'klicktipp_content_include_partnerinfo');
define("KLICKTIPP_CONTENT_INCLUDE_ADD_CONTACT_PERMISSION_PAGE", 'klicktipp_add_contact_permission_page');
define("KLICKTIPP_CONTENT_INCLUDE_APIKEY_UPSELL", 'klicktipp_api_permission_page');
define("KLICKTIPP_CONTENT_INCLUDE_FANPAGE_UPSELL", 'klicktipp_add_fanpage_permission_page');
define("KLICKTIPP_CONTENT_INCLUDE_IPN_PRODUCT_LIMIT_UPSELL", 'klicktipp_ipn_product_limit_upsell');
define("KLICKTIPP_CONTENT_INCLUDE_DATA_PROCESSING_ORDER", 'klicktipp_data_processing_order');
define("KLICKTIPP_CONTENT_INCLUDE_WUFOO_LIMIT_UPSELL", 'klicktipp_content_include_wufoo_limit_upsell');

//external libraries @see klicktipp_library() (unused)
define("KLICKTIPP_LIBRARY_DOMPDF", 'klicktipp_library_dompdf');
define("KLICKTIPP_LIBRARY_S3", 'klicktipp_library_s3');

define('KLICKTIPP_SENDER_HASHSECRET', '***************');

use App\Klicktipp\AccountManager;
use App\Klicktipp\AddOn\AddOnManager;
use App\Klicktipp\AddOn\Exception\AddOnException;
use App\Klicktipp\AddonEntity\AddonEntity;
use App\Klicktipp\BlacklistHandler;
use App\Klicktipp\Cache;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsNewsletterAutoresponder;
use App\Klicktipp\CampaignsProcessFlow;
use App\Klicktipp\Core;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\Dates;
use App\Klicktipp\Errors;
use App\Klicktipp\Libraries;
use App\Klicktipp\Mail\KlicktippMail;
use App\Klicktipp\PluginHookConnectRedirect;
use App\Klicktipp\ProductFruitsWidget;
use App\Klicktipp\Includes\Xhprof;
use App\Klicktipp\QueueCreator;
use App\Klicktipp\CustomFields;
use App\Klicktipp\CustomFieldsPublicAPI;
use App\Klicktipp\CustomFieldsTypeCheckbox;
use App\Klicktipp\CustomFieldsTypeDate;
use App\Klicktipp\CustomFieldsTypeDatetime;
use App\Klicktipp\CustomFieldsTypeDecimal;
use App\Klicktipp\CustomFieldsTypeDropdown;
use App\Klicktipp\CustomFieldsTypeEmail;
use App\Klicktipp\CustomFieldsTypeHtml;
use App\Klicktipp\CustomFieldsTypeNumber;
use App\Klicktipp\CustomFieldsTypeParagraph;
use App\Klicktipp\CustomFieldsTypeSingle;
use App\Klicktipp\CustomFieldsTypeTime;
use App\Klicktipp\CustomFieldsTypeURL;
use App\Klicktipp\DatabaseTableCRUD;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\EmailsAutomationEmail;
use App\Klicktipp\EmailsAutomationSMS;
use App\Klicktipp\EmailsNotificationEmail;
use App\Klicktipp\EmailsNotificationSMS;
use App\Klicktipp\GoogleWebRiskBlacklist;
use App\Klicktipp\KlicktippServicesFormatterHTML;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\MarketingTools;
use App\Klicktipp\MetaLabels;
use App\Klicktipp\Plugin;
use App\Klicktipp\ProcessLog;
use App\Klicktipp\Reference;
use App\Klicktipp\Settings;
use App\Klicktipp\Signatures;
use App\Klicktipp\SplitTests;
use App\Klicktipp\Statistics;
use App\Klicktipp\Subaccount;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;
use App\Klicktipp\TagCategoryAPIKey;
use App\Klicktipp\TagCategoryAutonmationFinished;
use App\Klicktipp\TagCategoryAutonmationStarted;
use App\Klicktipp\TagCategoryCampaignClicked;
use App\Klicktipp\TagCategoryCampaignConverted;
use App\Klicktipp\TagCategoryCampaignOpened;
use App\Klicktipp\TagCategoryCampaignSent;
use App\Klicktipp\TagCategoryCampaignViewed;
use App\Klicktipp\TagCategoryChargedback;
use App\Klicktipp\TagCategoryDeferredPayment;
use App\Klicktipp\TagCategoryDigistoreAffiliation;
use App\Klicktipp\TagCategoryEmailClicked;
use App\Klicktipp\TagCategoryEmailOpened;
use App\Klicktipp\TagCategoryEmailSent;
use App\Klicktipp\TagCategoryEmailViewed;
use App\Klicktipp\TagCategoryFacebook;
use App\Klicktipp\TagCategoryFacebookAudience;
use App\Klicktipp\TagCategoryForms;
use App\Klicktipp\TagCategoryOutboundActivated;
use App\Klicktipp\TagCategoryOutboundKajabiActivated;
use App\Klicktipp\TagCategoryOutboundKajabiDeactivated;
use App\Klicktipp\TagCategoryPayment;
use App\Klicktipp\TagCategoryPaymentCompleted;
use App\Klicktipp\TagCategoryPaymentExpired;
use App\Klicktipp\TagCategoryPluginInboundFinished;
use App\Klicktipp\TagCategoryPluginInboundInProgress;
use App\Klicktipp\TagCategoryPluginInboundReady;
use App\Klicktipp\TagCategoryPluginInboundStarted;
use App\Klicktipp\TagCategoryRebillCanceled;
use App\Klicktipp\TagCategoryRebillResumed;
use App\Klicktipp\TagCategoryRefunded;
use App\Klicktipp\TagCategoryRequest;
use App\Klicktipp\TagCategorySmartLink;
use App\Klicktipp\TagCategorySMSClicked;
use App\Klicktipp\TagCategorySMSListbuilding;
use App\Klicktipp\TagCategorySMSSent;
use App\Klicktipp\TagCategorySubsequentPayment;
use App\Klicktipp\TagCategoryTaggingPixel;
use App\Klicktipp\TemplatesEmail;
use App\Klicktipp\TemplatesEmailGlobal;
use App\Klicktipp\ToolCalendar;
use App\Klicktipp\ToolOutboundGeneral;
use App\Klicktipp\ToolPluginGeneralPublicAPI;
use App\Klicktipp\ToolStatistics;
use App\Klicktipp\ToolWebsiteExitLightboxPublicAPI;
use App\Klicktipp\ToolWebsiteFeedbackPublicAPI;
use App\Klicktipp\ToolWebsiteOneTimeOfferPublicAPI;
use App\Klicktipp\ToolWebsiteSocialproofPublicAPI;
use App\Klicktipp\ToolWebsiteSplittest;
use App\Klicktipp\ToolWebsiteSplittestPublicAPI;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\UserCache;
use App\Klicktipp\UserGroups;
use App\Klicktipp\UserHelper;
use App\Klicktipp\UserVariables;
use App\Klicktipp\VarAdditionalAddresses;
use App\Klicktipp\VarConsultant;
use App\Klicktipp\VarConvention;
use App\Klicktipp\VarDataProcessingOrder;
use App\Klicktipp\VarFeatureMessages;
use App\Klicktipp\VarImport;
use App\Klicktipp\VarProcessflow;
use App\Klicktipp\VarTier;
use App\Klicktipp\VarAmemberProductID;
use App\Klicktipp\Beamer;
use App\Klicktipp\Amember;
use App\Security\Keycloak\KeycloakApi;

/**
 * Implements hook_boot().
 */
function klicktipp_boot() {
  // this is called in DRUPAL_BOOTSTRAP_PAGE_CACHE
  // there is no database access here

  error_reporting(E_ALL ^ E_NOTICE ^ E_WARNING ^ E_DEPRECATED ^ E_USER_DEPRECATED);

  set_time_limit(240);

  Libraries::include('consts.inc', '/core');
  Libraries::include('utils.inc', '/core');
  Libraries::include('database.inc', '/core');

  klicktipp_prepare_test_mocks();
}

/**
 * Implements hook_init().
 */
function klicktipp_init() {

  global $language;
  global $conf;
  global $user;

  $headers = getallheaders();
  Xhprof::startByRequest(
    $headers[Xhprof::HEADER_ENABLED] ?? null,
    $headers[Xhprof::HEADER_PROFILER_NAMESPACE] ?? null,
  );

  // if password of current user was changed on another device after login on current device,
  // logout user on current device
  if ($user->uid && $user->ktdata) {
    // hook_user_load is not called for global user, so ktdata is not unserialized
    $lastPasswordChange = unserialize($user->ktdata)['last_password_change'] ?? 0;
    $loginTime = $_SESSION['last_authentication'] ?? 0;
    if ($lastPasswordChange > $loginTime) {
      module_load_include('pages.inc', 'user');
      user_logout();
    }
  }

  //set global language to user language as specified in account settings
  if ($user->language != $language->language) {
    //user language is different than site language, change to user language

    //get the list of availabel languages
    $langs = language_list();
    //set global language to user language if enabled
    $UserLanguage = $langs[$user->language];
    if (!empty($UserLanguage) && $UserLanguage->enabled) {
      $language = $UserLanguage;
    }
  }

  //make the klicktipp scripts available in all themes (file, type, scope, defer, cache, aggregate id turned on )
  drupal_add_js('misc/script.js', array(
    'type' => 'file',
    'scope' => 'header',
    'group' => JS_DEFAULT,
    'every_page' => TRUE,
    'weight' => 1,
    'defer' => FALSE,
    'cache' => TRUE,
    'preprocess' => TRUE,
  ));

  if (file_exists('content_includes/css/custom_styles.css')) {
    drupal_add_css('content_includes/css/custom_styles.css', array(
      'type' => 'file',
      'scope' => 'header',
      'group' => CSS_THEME,
      'every_page' => TRUE,
      'weight' => 0,
      'media' => 'all',
      'preprocess' => TRUE,
    ));
  }

  //add datepicker
  drupal_add_library('system', 'ui.datepicker');

  //add jquery.cookie
  drupal_add_library('system', 'jquery.cookie');

  //include language for datepicker
  //Note: jquery_update module must exist
  $lang = ($language->language == 'pt-br') ? 'pt-BR' : $language->language;
  if ($language->language != 'en') {

    $path = drupal_get_path('module', 'jquery_update') . '/replace/ui/ui/minified/i18n/jquery.ui.datepicker-' . $lang . '.min.js';
    drupal_add_js($path, array(
      'type' => 'file',
      'scope' => 'header',
      'group' => JS_DEFAULT,
      'every_page' => TRUE,
      'weight' => 2,
      'defer' => FALSE,
      'cache' => TRUE,
      'preprocess' => TRUE,
    ));

  }

  //set the return value of the Datepicker according to the klicktipp settings
  drupal_add_js(array(
    'DatePicker' => array(
      'format' => variable_get(Dates::FORMAT_DMY_DATEPICKER, 'dd.mm.yy'),
      'language' => $lang,
    ),
  ), array('type' => 'setting', 'scope' => 'header', 'group' => JS_DEFAULT));

  // set default timezone
  Dates::setTimezone();

  // show user messages from cron etc.
  klicktipp_get_messages();

  // lookup individual front page for the users roles
  if (drupal_is_front_page()) {
    // if fpm-fcgi and not wget/curl then it should be save to do a redirect
    if ((php_sapi_name() == 'fpm-fcgi') && !preg_match('/^(curl|wget)/i', $_SERVER['HTTP_USER_AGENT'])) {
      // a call from the browser
      global $user;
      if ($user->uid) {
        $newfront = "user/me";
        // set new front page
        $_GET['q'] = drupal_get_normal_path($newfront);
        // trick drupal_is_front_page
        $conf['site_frontpage'] = $newfront;
      }
      elseif ($_SERVER['SCRIPT_NAME'] == "/index.php") {
        // not logged in: redirect to klicktipp.com
        $newfront = variable_get('site_frontpage_unauthenticated', '');
        // first match is served
        if (!empty($newfront)) {
          // set new front page (301 moved permanently, shifts seo link power to klicktipp.com)
          klicktipp_goto($newfront);
        }
      }
    }
  }

  if (strpos(DRUPAL_ROOT, 'mailandbeyond.com') !== FALSE) {
    //the application is running on a VM, check if a cloned database is used and secure it
    Libraries::include('prod_check.inc');
    _klicktipp_secure_database_clone();
  }
}

/**
 * Implements hook_exit()
 */
function klicktipp_exit() {
  Xhprof::stopStartedByRequest();
}

/**
 * Implements hook_menu().
 * Load menu callback definitions from includes
 */
function klicktipp_menu() {
  Libraries::include('menu.inc', '/core');
  return klicktipp_menu_items();
}

/**
 * Implements hook_menu_alter().
 */
function klicktipp_menu_alter(&$items) {
  $items['node/%node/view']['title'] = 'Overview';
  $items['node/%node/edit']['title'] = 'Settings';

  //redirect to frontpage on node/add
  $items['node/add']['page callback'] = 'drupal_goto';
  $items['node/add']['page arguments'] = ['<front>'];

  //restrict users to view taxonomies
  $items['taxonomy/term/%']['access arguments'] = array('administer taxonomy');
  $items['taxonomy/autocomplete']['access arguments'] = array('administer taxonomy');

  // change those items to MENU_CALLBACKS so they won't appear in the affix
  $items['user/%user/view']['type'] = MENU_CALLBACK;
  $items['user/%user/contact']['type'] = MENU_CALLBACK; //admin only
  $items['user/%user/track/navigation']['type'] = MENU_CALLBACK; //admin only
  $items['user/%user/devel']['type'] = MENU_CALLBACK; //admin only

  //the user information has been move to user/%user/personal-information and is accessible without confirming the dpo
  //to access user/%user/edit the user has to confirm the dpo first
  $items['user/%user/edit']['access callback'] = 'klicktipp_user_edit_access';
  $items['user/%user/edit']['access arguments'] = array(1, (string)Subaccount::SUBACCOUNT_ADMINISTRATOR, FALSE);

  //do not show overview tab (frontpage) for plain affiliates (only authenticated user), klicktipp_account_user() redirects to tab "partner info"
  $items['user/%user/view']['access callback'] = 'klicktipp_user_edit_access';
  $items['user/%user/view']['access arguments'] = array(1);

  // if no user id is specified ("/user"), show user page of main account
  $items['user']['page callback'] = 'klicktipp_user_page';

  // grant subaccount access to user/me (authenticated front page of main account)
  $items['user/%user']['access callback'] = 'klicktipp_user_view_access';
  $items['user/%user']['access arguments'] = array(1);

  // lift up role changes from 'administer permissions' to 'administer users', so we can grant access to assign roles to support users
  $items['admin/people/permissions']['access arguments'] = array('administer users');
  $items['admin/people/permissions/roles']['access arguments'] = array('administer users');

  // display more details for watchdog entries
  $items['admin/reports/event/%']['page callback'] = 'drupal_get_form';
  $items['admin/reports/event/%']['page arguments'] = array('klicktipp_prod_check_watchdog', 3);
  $items['admin/reports/event/%']['file'] = '../../sites/all/modules/klicktipp/includes/prod_check.inc';

  //set the page callback for the one-time login to our custom function which redirects to the wizard
  $items['user/reset/%/%/%']['page callback'] = 'klicktipp_account_user_pass_reset';
  $items['user/reset/%/%/%']['page arguments'] = array(2, 3, 4);
  $items['user/reset/%/%/%']['file'] = '../../sites/all/modules/klicktipp/forms/account_registration.inc';
}

/**
 * Klick-Tipp main menu
 * Get the main menu as a <UL> list
 * $CustomerUserID: if > 0, "me" in menu links will be replaced with $CustomerUserID
 */
function klicktipp_menu_main(int $CustomerUserID = 0, bool $forceLoggedOutMenu = false) {

  Libraries::include('api_angular.inc', '/api');

  global $user;

  $output = "<ul class='nav navbar-nav'>";

  $login_icon = "";

  if ($forceLoggedOutMenu) {
    // Note: create menu and change menu id in case we have a special menu for the first login pages
    $menumain = 'menu-main-unauth';
  }
  elseif ($user->uid) {
    $menumain = variable_get('klicktipp_theme_main_menu_authenticated', 'menu-main2');
  }
  else {
    $menumain = 'menu-main-unauth';
    $login_icon = '<li><a class="top-menu-login" href="' . url(variable_get('amember_register_login_url', 'user')) . '"><i class="icon-top-menu-login"></i>' . t('Login') . '</a></li>';
  }

  $search_icon = '<li id="top-menu-beacon-search" style="display: none;"><a class="top-menu-search" href="Javascript:void(0);"><i class="icon-top-menu-search"></i></a></li>';
  $beamer_icon = '<li id="top-menu-beamer" class="display-beamer" style="display: none;"><a class="kt-beamer" href="Javascript:void(0);"><div class="icon-beamer"></div></a></li>';

  //open customer menu link in new tab
  $link_target = (empty($CustomerUserID)) ? array() : array('attributes' => array('target' => '_blank')); //open menu links in the same tab

  $items = menu_tree_page_data($menumain);
  $index = 0;

  foreach ($items as $item) {

    $link = $item['link'];

    $e2eMain = getE2eIdFromPath("main-$index", '');
    $index++;

    if ($link['hidden']) {
      continue;
    }

    $html = "";

    if ($link['has_children'] && !empty($item['below'])) {

      $caret = "<b class='caret'></b>";
      $html .= "<li class='dropdown'><a href='#' class='dropdown-toggle' data-toggle='dropdown' data-e2e-id='$e2eMain'>" . $link['link_title'] . "$caret</a>";
      $html .= '<ul class="dropdown-menu">';

      foreach ($item['below'] as $drop) {

        if (substr($drop['link']['link_title'], 0, 1) == '-') {
          $html .= '<li class="divider"></li>';
          $drop['link']['link_title'] = substr($drop['link']['link_title'], 1);
        }

        // check if third level menu should be created
        if ($drop['link']['has_children'] && !empty($drop['below'])) {

          //replace "me" placeholder with CustomerUserID in link_path (if set)
          $path = klicktipp_menu_customer_path($drop['link']['link_path'], $CustomerUserID);
          $drop['link']['link_path'] = $path;

          $e2eSub = getE2eIdFromPath($drop['link']['link_path'], $e2eMain);
          $drop['attributes']['data-e2e-id'] = $e2eSub;

          // add css class and data attribute
          $drop['attributes']['class'][] = 'dropdown-submenu-toggle';
          $drop['attributes']['data-toggle'][] = 'dropdown-submenu-toggle';
          if ($CustomerUserID) {
            $drop['attributes']['target'][] = '_blank';
            if (empty($path)) {
              //the link is not accessible for the support user (sub account links) -> deactivate
              $drop['attributes']['style'][] = 'text-decoration: line-through;cursor: not-allowed;pointer-events: none;';
            }
          }

          $html .= "<li class='dropdown-submenu'>" . l($drop['link']['link_title'], $drop['link']['link_path'], $drop);
          $html .= '<ul class="dropdown-menu">';

          // loop over drop children to make third level
          foreach ($drop['below'] as $thirdLevel) {
            // check for divider
            if (substr($thirdLevel['link']['link_title'], 0, 1) == '-') {
              $html .= '<li class="divider"></li>';
              $thirdLevel['link']['link_title'] = substr($thirdLevel['link']['link_title'], 1);
            }
            //replace "me" placeholder with CustomerUserID in link_path (if set)
            $path = klicktipp_menu_customer_path($thirdLevel['link']['link_path'], $CustomerUserID);
            $thirdLevel['link']['link_path'] = $path;

            $linkAttr = $link_target;
            if ($CustomerUserID && empty($path)) {
              //the link is not accessible for the support user (sub account links) -> deactivate
              $linkAttr['attributes']['style'][] = 'text-decoration: line-through;cursor: not-allowed;pointer-events: none;';
            }

            $e2eThird = getE2eIdFromPath($thirdLevel['link']['link_path'], $e2eSub);
            $linkAttr['attributes']['data-e2e-id'] = $e2eThird;
            $linkAttr['attributes']['data-e2e-special'] = 'trigger-js-click';

            $html .= "<li>" . l($thirdLevel['link']['link_title'], $thirdLevel['link']['link_path'], $linkAttr) . "</li>";
          }

          $html .= "</ul>";
          $html .= "</li>";
        } else {
          //replace "me" placeholder with CustomerUserID in link_path (if set)
          $path = klicktipp_menu_customer_path($drop['link']['link_path'], $CustomerUserID);
          $drop['link']['link_path'] = $path;

          $linkAttr = $link_target;
          if ($CustomerUserID && empty($path)) {
            //the link is not accessible for the support user (sub account links) -> deactivate
            $linkAttr['attributes']['style'][] = 'text-decoration: line-through;cursor: not-allowed;pointer-events: none;';
          }

          $html .= "<li>" . l($drop['link']['link_title'], $drop['link']['link_path'], $linkAttr) . "</li>";
        }
      }

      $html .= "</ul>";
      $html .= "</li>";

    }
    else {

      //replace "me" placeholder with CustomerUserID in link_path (if set)
      $link['link_path'] = klicktipp_menu_customer_path($link['link_path'], $CustomerUserID);

      $html .= "<li>" . l($link['link_title'], $link['link_path'], $link_target) . "</li>";

    }

    $output .= $html;

  }

  $output .= (empty($CustomerUserID)) ? $search_icon . $beamer_icon . $login_icon : '';

  $output .= "</ul>";

  return $output;

}

/**
 * footer menu
 */
function klicktipp_menu_footer() {

  $MenuID = variable_get(KLICKTIPP_THEME_FOOTER_MENU, 'menu-impressum');

  if (empty($MenuID)) {
    return '';
  }

  $items = menu_tree_page_data($MenuID);

  if (empty($items)) {
    return '';
  }

  $output = "<ul class='nav navbar-nav pull-right'>";


  foreach ($items as $item) {

    $link = $item['link'];

    if ($link['hidden']) {
      continue;
    }

    $output .= "<li>" . l($link['link_title'], $link['link_path']) . "</li>";

  }

  $output .= "</ul>";

  return $output;

}

/**
 * whitelabel footer menu
 */
function klicktipp_menu_whitelabel_footer() {

  $MenuID = variable_get(KLICKTIPP_THEME_WHITELABEL_MENU, 'menu-impressum');

  if (empty($MenuID)) {
    return '';
  }

  $items = menu_tree_page_data($MenuID);

  if (empty($items)) {
    return '';
  }

  $output = '<div id="whitelabel-footer"><span>' . t("Klick-Tipp !copy @year. All rights reserved", array(
      '!copy' => '&copy;',
      '@year' => date('Y')
    )) . '</span>';

  foreach ($items as $item) {

    $link = $item['link'];

    if ($link['hidden']) {
      continue;
    }

    $output .= '<span class="footer-separator">|</span>' . l($link['link_title'], $link['link_path']);

  }

  $output .= "</div>";

  return $output;

}

/**
 * Support users can access a menu for a customer
 * replace the placeholder "me" with the CustomerUserID in menu paths
 */
function klicktipp_menu_customer_path($path, $CustomerUserID = 0) {

  if (!empty($CustomerUserID)) {

    $path_elements = explode('/', $path);

    foreach ($path_elements as &$element) {
      if ($element == "me") {
        $element = $CustomerUserID;
      }
    }

    $path = implode('/', $path_elements);

    $item = menu_get_item($path);
    if (!$item['access']) {
      //Support account can't access path for $CustomerUserID (subaccount)
      return '';
    }

  }

  return $path;
}

/**
 * get the title for the newsletter splittest email tab
 * @see menu.inc emails/%user/%/emails
 */
function klicktipp_menu_splittest_email_title($account, $CampaignID) {

  $Campaign = Campaigns::RetrieveCampaignByID($CampaignID, $account->uid);
  if (Campaigns::IsSMSCampaign($Campaign['AutoResponderTriggerTypeEnum'])) {
    return empty($Campaign['SplittestID']) ? t('SMS') : t('Splittest-SMS');
  }
  else {
    return empty($Campaign['SplittestID']) ? t('Email') : t('Splittest-Emails');
  }
}

function klicktipp_menu_email_editor($type, $account, $mode, $ambiguous = '', $CampaignEmailID = 0) {

  if ($mode === 'email' || $mode === 'notifyemail') {
    //automation email || notification email
    $EmailID = $ambiguous;
  }
  elseif (is_numeric($mode)) {
    //newsletter, autoresponder

    if ($type === 'splittest' && ($ambiguous === 'campaign' || $ambiguous === 'splittesteditor')) {
      $EmailID = $CampaignEmailID;
    }
    elseif ($type === 'email') {
      //the email id is not given in the path, retrieve the campaign

      $CampaignID = $mode;
      // @var CampaignsNewsletter $ObjectCampaign
      $ObjectCampaign = CampaignsNewsletter::FromID($account->uid, $CampaignID);
      if ($ObjectCampaign instanceof CampaignsNewsletter) {
        $EmailID = $ObjectCampaign->GetData('RelEmailID');
      }

    }

  }

  if (empty($EmailID)) {
    //unlikely, but the editor will catch this case
    $EmailID = 0;
  }

  $email = Emails::FromID($account->uid, $EmailID);

  if (!$email instanceof Emails) {
    drupal_not_found();
    drupal_exit();
  }

  drupal_goto($email->getEntityUrlEditor());

}

function klicktipp_menu_access_email_editor($type, $account, $mode, $ambiguous = '', $CampaignEmailID = 0) {

  //this function is called twice per page request
  //save some db queries
  static $CachedCampaign;
  static $CachedEmail;

  if (empty($account->uid)) {
    return FALSE;
  }

  if ($mode == 'email' || $mode == 'notifyemail') {
    //automation email || notification email

    if ($type == 'splittest') {
      return FALSE;
    }

    $EmailID = $ambiguous;
  }
  elseif (is_numeric($mode)) {
    //newsletter, autoresponder

    if ($ambiguous == 'campaign' || $ambiguous == 'splittesteditor') {

      if ($type == 'email') {
        return;
      }
      //type == splittest
      $EmailID = $CampaignEmailID;
    }
    elseif ($type == 'email') {
      //the email id is not given in the path, retrieve the campaign

      if (isset($CachedCampaign)) {
        $ObjectCampaign = $CachedCampaign;
      }
      else {
        $CampaignID = $mode;
        // @var CampaignsNewsletter $ObjectCampaign
        $ObjectCampaign = CampaignsNewsletter::FromID($account->uid, $CampaignID);
        $CachedCampaign = $ObjectCampaign;
      }

      if (!empty($ObjectCampaign)) {
        $EmailID = $ObjectCampaign->GetData('RelEmailID');
      }

    }

  }
  else {
    //sms, notifysms, add, addar, ...
    return FALSE;
  }


  if (empty($EmailID)) {
    return FALSE;
  }

  if (isset($CachedEmail)) {
    $ObjectEmail = $CachedEmail;
  }
  else {
    // @var Emails $EmailObject
    $ObjectEmail = Emails::FromID($account->uid, $EmailID);
    $CachedEmail = $ObjectEmail;
  }

  if (!$ObjectEmail) {
    return FALSE;
  }

  if (!$ObjectEmail->GetData('Version')) {
    return FALSE;
  }

  return TRUE;

}

function klicktipp_user_digistore_invoice_menu_access($account) {
  if (user_access('administer klicktipp', $account)) {
    // make the menu item always accessible for admins, so it can be added to the menu
    return TRUE;
  }

  if (!klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_ADMINISTRATOR)) {
    return FALSE;
  }

  return (!empty($account->digistore_receipt) && valid_url($account->digistore_receipt));
}

function klicktipp_staging_sync_menu_access()
{
  if (KLICKTIPP_ENVIRONMENT !== 'local') {
    return false;
  }

  return user_access('administer site configuration');
}

/**
 * Implementation of hook_forms($formId, $args)
 *
 * @param string $formId id of the form (usually name of form function, but can be different in some cases
 * @param array $args form arguments
 *
 * @return array|string[][] list of form definitions
 */
function klicktipp_forms($formId, $args)
{
  // On some pages (e.g. subscription list) we have multiple forms using same form function (with different arguments).
  // This case causes problems, since name of form function is used as formId by default and formId
  // is used internally as cache key for form state, so all forms use form state of first one.
  // The logic below allows us to resolve the problem by mapping different form ids to same form function
  $dynamicFormCallbacks = [
    'klicktipp_subscription_delete_confirm_form',
    'klicktipp_subscription_unsubscribe_confirm_form',
    'klicktipp_subscription_resetbounce_confirm_form',
    'klicktipp_domain_delete_confirm_form',
    'klicktipp_domain_instructions_form'
  ];
  foreach ($dynamicFormCallbacks as $dynamicFormCallback) {
    if (strpos($formId, $dynamicFormCallback) === 0) {
      return [$formId => ['callback' => $dynamicFormCallback]];
    }
  }

  return [];
}

/**
 * implementation of hook_form_alter
 * Alter some drupla forms:
 * - user_profile_form: theme bootstrap styles
 * - user_pass:         theme bootstrap styles
 * - user_login:        TwoFactorAuth + theme bootstrap styles
 * - contact_mail_page: contact form theme bootstrap styles
 * - filter_admin_format_form: remove element validation for HTML fields
 */
function klicktipp_form_alter(&$form, &$form_state, $form_id) {

  global $user;

  if ($form_id == 'user_profile_form') {

    // add klicktipp customer profile
    Libraries::include('account.inc', '/forms');
    _klicktipp_user_profile_form($form, $form_state, $form_id);

    // change name field
    if (empty($form['account']['name'])) {
      //if not user_access('change_own_username') the form element does $form['account']['name'] not exist
      // using global $user in this case is OK, display only (the logged in user sees his own username)
      $form['account']['name'] = array(
        '#type' => 'item',
        '#theme' => 'klicktipp_item',
        '#value' => $user->name,
        '#title' => t('Username'),
        '#weight' => -1,
      );
    }

    // change pass confirm
    $form['account']['pass']['#weight'] = 2;
    // theme submit
    $form['actions']['submit']['#theme'] = 'klicktipp_submit';
    $form['actions']['submit']['#weight'] = 10000;
    $form['actions']['submit']['#prefix'] = '<div class="button-row">';

    // theme delete
    if (isset($form['delete'])) {
      $form['actions']['delete']['#theme'] = 'klicktipp_delete_submit';
      $form['actions']['delete']['#weight'] = 10001;
    }

    $form['actions']['cancel'] = array(
      '#title' => t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL),
      '#value' => "", //frontpage
      '#theme' => 'klicktipp_cancel_button',
      '#weight' => 10002,
      '#suffix' => '</div>',
    );

    // Remove the current password field from the user_profile_form form
    Libraries::include('change_password.inc', '/forms');
    $form['account']['pass_value']['#access'] = FALSE;
    $form['account']['pass']['#access'] = FALSE;

    unset($form['contact']);
    unset($form['locale']);

    // decode umlaut email address
    $account = $form['#user'];
    $register = ($form['#user']->uid > 0 ? FALSE : TRUE);
    $form['account']['mail']['#default_value'] = (!$register ? Subscribers::DepunycodeEmailAddress($account->mail) : '');
  }
  elseif ($form_id == 'user_pass') {

    Libraries::include('account.inc', '/forms');

    $weight = 1;

    $form['body'] = array(
      '#type' => 'markup',
      '#prefix' => '<div class="modal-body">',
      '#suffix' => '</div>',
    );

    if ($user->uid) {
      //Note: if the user is logged in, no input field for the email address will be displayed
      //      the account email address will be used $form['name']['#value']
      //      a markup with an info will be shown ($form['mail'])
      //      remove the markup and show our description
      //      after submit the message t('Further instructions have been sent to your e-mail address.')
      //      will be shown on the logged in frontpage

      unset($form['mail']);

      $description = t('Password reset instructions will be mailed to %email.', array(
        '%email' => $form['name']['#value'])
      );

    }
    else {
      $description = t("To reset your password, enter your email address or username here. You will receive an email with instructions to set a new password.");
    }

    $form['body']['description'] = array(
      '#type' => 'markup',
      '#value' => "<p class='modal-description'>$description</p>",
      '#weight' => $weight++,
    );

    $form['name']['#weight'] = $weight++;
    $form['body']['name'] = $form['name']; // add name element to body
    unset($form['name']);

    $Login = l(t('(Login)'), 'user');
    $form['actions']['#prefix'] = '<div class="modal-footer">';
    $form['actions']['#suffix'] = '<div class="forgot-credentials">' . $Login . '</div></div>'; // closes the modal-footer and the forgot-password container
    $form['actions']['submit']['#theme'] = 'klicktipp_email_submit';
    $form['actions']['submit']['#attributes']['class'] = array('btn-orange');
    $form['actions']['submit']['#weight'] = $weight++;

    klicktipp_set_title(t('Reset password'));

    // From module: username_enumeration_prevention
    // Override core validate and submit actions.
    $form['#validate'][0] = 'klicktipp_pass_validate';
    $form['#submit'][0] = 'klicktipp_pass_submit';
  }
  elseif ($form_id == 'user_login' || $form_id == 'user_login_block') {

    $oidcClient = ServiceContainer::getInstance()->get(OidcClient::class);
    header('Location: ' . $oidcClient->getAuthorizeUrl($oidcClient->getRequestUri()));
    exit();

  }
  elseif ($form_id == 'contact_mail_page') {

    drupal_set_breadcrumb(array());

    $form['contact_information']['#theme'] = 'klicktipp_info';

    $form['message']['#resizable'] = FALSE;
    $form['message']['#rows'] = 10;
    $form['message']['#title'] = t('Your contact message');

    $form['actions']['submit']['#prefix'] = '<div class="button-row">';
    $form['actions']['submit']['#suffix'] = '</div>';
    $form['actions']['submit']['#theme'] = 'klicktipp_email_submit';
  }
  elseif ($form_id == 'filter_admin_format_form') {
    //admin/config/content/formats/6
    //remove the textfield validator -> html entities must be entered into this textfield
    //@see klicktipp_element_info_alter()
    $form['filters']['settings']['filter_html']['allowed_html']['#element_validate'] = array();
  }
  elseif ($form_id == 'locale_translate_edit_form') {
    //deactivate the locale_string_is_safe() check for translations (does not allow <>&)
    $key = array_search('locale_translate_edit_form_validate', $form['#validate']);
    if ($key !== FALSE) {
      unset($form['#validate'][$key]);
    }
  }
  elseif ($form_id == 'services_edit_form_endpoint_resources') {
    drupal_set_message('Note: If a new endpoint has been created, please update the install script.<br />' .
      '@see: /profiles/klicktipp_app/klicktipp_app.install _klicktipp_app_profile_load_api() and _klicktipp_app_profile_load_cockpit()', 'warning');
  } elseif($form_id == 'system_performance_settings') {
    klicktipp_system_performance_settings($form);
  }

  _klicktipp_form_preserve_maxlength_for_magic_selects($form);
}

function klicktipp_system_performance_settings(&$form)
{
  $form['transactional_emails_runqueue_db_transactions'] = array(
    '#type' => 'fieldset',
    '#title' => 'TrancactionEmails DB Transactions',
    '#description' => ''
  );

  $form['transactional_emails_runqueue_db_transactions']['klicktipp_transaction_emails_db_transaction_runstarted'] = array(
    '#type' => 'checkbox',
    '#title' => 'TransactionEmails::RunStarted DB Transactions',
    '#default_value' => variable_get('klicktipp_transaction_emails_db_transaction_runstarted', TRUE),
  );

  $form['transactional_emails_runqueue_db_transactions']['klicktipp_transaction_emails_db_transaction'] = array(
    '#type' => 'checkbox',
    '#title' => 'TransactionEmails DB Transactions, all other than RunStarted',
    '#default_value' => variable_get('klicktipp_transaction_emails_db_transaction', TRUE),
  );
}

/**
 * Drupal produces an error if a textfield has a '#maxlength' attrubute and it's value isn't scalar. This causes
 * problems with #magic_select, because the values is an array in this case. '#maxlength' is set to 128 by default.
 * This function sets '#maxlength' to null in order to prevent the default value.
 *
 * @param $form
 */
function _klicktipp_form_preserve_maxlength_for_magic_selects(&$form) {
  foreach (element_children($form) as $key) {
    if (!empty($form[$key]['#magic_select'])) {
      $form[$key]['#maxlength'] = null;
    }
    _klicktipp_form_preserve_maxlength_for_magic_selects($form[$key]);
  }
}

/**
 * submit function for the login form that substitutes a valid account email address with the accounts' username
 * @param $form
 * @param $form_state
 */
function _klicktipp_user_login_form_submit($form, &$form_state) {
  $emailAddress = Subscribers::PunycodeEmailAddress($form_state['values']['name']);
  if (Subscribers::ValidateEmailAddress($emailAddress)) {
    // user wants to login with a valid email address
    // be aware that utf8_unicode_ci of the database field mail causes not
    // only the equality of lower and upper case chars, but also e.g. "A" and "Ä";
    // so currently it is not possible to add the following two users:
    //    <EMAIL> and hä*************
    // Note: other collations use other equality definitions
    $account = user_load_by_mail($emailAddress);
    if (!empty($account)) {
      // account with provided email address was found
      $form_state['values']['name'] = $account->name;
    }
  }
}

/**
 * Validator for the login form that substitutes a valid account email address with the account's username
 * @param $form
 * @param $form_state
 */
function _klicktipp_user_login_authenticate_validate(&$form, &$form_state) {
  $emailAddress = Subscribers::PunycodeEmailAddress($form_state['values']['name']);
  if (Subscribers::ValidateEmailAddress($emailAddress)) {
    // user wants to login with a valid email address
    // be aware that utf8_unicode_ci of the database field mail causes not
    // only the equality of lower and upper case chars, but also e.g. "A" and "Ä";
    // so currently it is not possible to add the following two users:
    //    <EMAIL> and hä*************
    // Note: other collations use other equality definitions
    $account = user_load_by_mail($emailAddress);
    if (!empty($account)) {
      // account with provided email address was found
      form_set_value($form['body']['name'], $account->name, $form_state);
    }
  }
}

/**
 * Additional submit function to change login form redirect to authenticated front page
 * Note: user_login_submit() in user.module redirects to user/<uid>
 *       subaccounts do not have access to their user/me or to user/me of the main account
 *       => redirect to front page and let klicktipp_user_view() handle the rest
 * @param $form
 * @param $form_state
 */
function _klicktipp_redirect_user_login_form_submit($form, &$form_state) {
  global $user;
  if ($user->uid > 0) {
    //only redirect if the login was successful and we have a user
    $form_state['redirect'] = '/';
  }
}

/**
 * Implements hook_filter_info
 */
function klicktipp_filter_info() {

  $filters = array();

  //klicktipp filter to execute PHP in a content_include file (*.inc)
  $filters['klicktipp_content_include'] = array(
    'title' => t('Klicktipp Content Include Filter'),
    'description' => t('Executes a piece of PHP code from content includes.'),
    'cache' => FALSE, // No caching for the PHP evaluator
    'process callback' => 'klicktipp_filter_content_include',
    'weight' => -10, //in case more filters are enabled, make it the first one
  );

  return $filters;

}

/**
 * get settings, allows calculated defaults
 *
 * @deprecated use \App\Klicktipp\Settings::get() instead
 */
function klicktipp_get_setting($key) {
  return Settings::get($key);
}

/**
 * Implements hook_permission().
 */
function klicktipp_permission() {
  return array(
    'access klicktipp' => array(
      'title' => t('access klicktipp'),
      'description' => t('Klicktipp application access'),
    ),
    'klicktipp admin role' => array(
      'title' => t('klicktipp admin role'),
      'description' => t('Klicktipp application full admin access (for now only used to identify admin users for user segments)'),
    ),
    'klicktipp premium' => array(
      'title' => t('klicktipp premium'),
      'description' => t('Klicktipp application access premium'),
    ),
    'klicktipp deluxe' => array(
      'title' => t('klicktipp deluxe'),
      'description' => t('Klicktipp application access deluxe'),
    ),
    'klicktipp api reset autoresponder' => array(
      'title' => t('klicktipp api reset autoresponder'),
      'description' => t('Allows to re-terminate and sent an autoresponder by api'),
    ),
    'access other accounts via api subscriber key' => array(
      'title' => t('access other accounts via api subscriber key'),
      'description' => t('Allows to retrieve subscriber data (api) from other accounts via subscriber-key'),
    ),
    'administer klicktipp' => array(
      'title' => t('administer klicktipp'),
      'description' => t('Klicktipp application administration access'),
    ),
    'use whitelabel domain' => array(
      'title' => t('use whitelabel domain'),
      'description' => t('Allows whitelabel domains'),
    ),
    'administer haters' => array(
      'title' => t('administer haters'),
      'description' => t('Administer haters'),
    ),
    'administer senderscore' => array(
      'title' => t('administer senderscore'),
      'description' => t('Administer senderscore'),
    ),
    'access customer stats' => array(
      'title' => t('access customer stats'),
      'description' => t('Access customer stats'),
    ),
    'access partnerprogramm' => array(
      'title' => t('access partnerprogramm'),
      'description' => t('Access klicktipp partnerprogramm'),
    ),
    'access consultant' => array(
      'title' => t('access consultant'),
      'description' => t('Access klicktipp consultant'),
    ),
    'access bounce check' => array(
      'title' => t('access bounce check'),
      'description' => t('Allows to check bounces'),
    ),
    'omit bounce check' => array(
      'title' => t('omit bounce check'),
      'description' => t('For support: release bounces without check'),
    ),
    'deactivate gbm' => array(
      'title' => t('deactivate gbm'),
      'description' => t('Deactivation of global bounce management allowed'),
    ),
    'omit html validation' => array(
      'title' => t('omit html validation'),
      'description' => t('Allows to submit invalid html in emails'),
    ),
    'import for customers' => array(
      'title' => t('import for customers'),
      'description' => t('Import access for special klicktipp customers'),
    ),
    'import for support' => array(
      'title' => t('import for support'),
      'description' => t('For support: access to import contacts for klicktipp customers'),
    ),
    'angular subscriber import' => array(
      'title' => t('angular subscriber import'),
      'description' => t('Access to import contacts (angular)'),
    ),
    'import without domain' => array(
      'title' => t('Import without domain'),
      'description' => t('Allows to import contacts without having a DKIM/Enterprise domain'),
    ),
    'omit MillionVerifier validation' => array(
      'title' => t('omit MillionVerifier validation'),
      'description' => t('Allows to skip email address validation via MillionVerifier for subscriber imports '),
    ),
    'access unsubscription urls' => array(
      'title' => t('access unsubscription urls'),
      'description' => t('Add, create, edit unsubscription urls'),
    ),
    'access add contacts' => array(
      'title' => t('access add contacts'),
      'description' => t('Add contacts manually'),
    ),
    'access data processing order' => array(
      'title' => t('access data processing order'),
      'description' => t('Klick-Tipp data processing order'),
    ),
    'access fullcontact' => array(
      'title' => t('access fullcontact'),
      'description' => t('Allow users to use FullContact API'),
    ),
    'access user blacklist' => array(
      'title' => t('access user blacklist'),
      'description' => t('Allow users to create their own email address blacklist'),
    ),

    // ---- Marketing Cockpit ---

    'access campaign builder' => array(
      'title' => t('access campaign builder'),
      'description' => t('Marketing Cockpit'),
    ),

    // ---- Listbuildings ---

    'klicktipp api' => array(
      'title' => t('klicktipp api'),
      'description' => t('Allow users to create an ApiKey'),
    ),
    'access wufoo' => array(
      'title' => t('access wufoo'),
      'description' => t('Create/edit Wufoo Subscription forms'),
    ),
    'access digistore multi device tracking' => array(
      'title' => t('access digistore multi device tracking'),
      'description' => t('Activate Digistore Multi-Device-Tracking for Subscription forms'),
    ),
    'access bcr event' => array(
      'title' => t('access event'),
      'description' => t('Allow users to create an event'),
    ),

    // ---- Listbuilding IPNs ---

    'access IPN DigiStore24' => array(
      'title' => t('access IPN DigiStore24'),
      'description' => t('Allow users to create a DigiStore24 product'),
    ),
    'access IPN AffiliCon' => array(
      'title' => t('access IPN AffiliCon'),
      'description' => t('Allow users to create an AffiliCon product'),
    ),
    'access IPN Clickbank' => array(
      'title' => t('access IPN Clickbank'),
      'description' => t('Allow users to create a Clickbank product'),
    ),
    'access IPN PayPal' => array(
      'title' => t('access IPN PayPal'),
      'description' => t('Allow users to create a PayPal product'),
    ),
    'access IPN Cleverbridge' => array(
      'title' => t('access IPN Cleverbridge'),
      'description' => t('Allow users to create a Cleverbridge product'),
    ),

    // --- Marketing Tools ---

    'access sms marketing' => array(
      'title' => t('access sms marketing'),
      'description' => t('Allow customers to create SMS Listbuildings and Campaigns'),
    ),
    'access tool webinar' => array(
      'title' => t('access tool webinar'),
      'description' => t('Allow customers to access the KlickTipp Marketing Tool Webinar'),
    ),
    'access splittest-club' => array(
      'title' => t('access splittest-club tools'),
      'description' => t('Allow customers to access the Splittest-Club Marketing Tools'),
    ),
    'access facebook audience' => array(
      'title' => t('access facebook audience'),
      'description' => t('Allow customers to access their Facebook Custom Audience'),
    ),
    'access youtube content analysis' => array(
      'title' => t('access youtube content analysis'),
      'description' => t('Allow customers to access the YouTube Analysis Tool'),
    ),
    'access gif maker' => array(
      'title' => t('access gif maker'),
      'description' => t('Allow customers to access gif maker tool.'),
    ),

    // ---

    'change subscriber email address' => array(
      'title' => t('change subscriber email address'),
      'description' => t("Allow the customer to change a contact's email address in subscriber edit"),
    ),
    'use receiver email' => array(
      'title' => t('use receiver email'),
      'description' => t('Use sender addresses to redirect all emails to a single address'),
    ),
    'allow re-send confirmation email' => array(
      'title' => t('allow re-send confirmation email'),
      'description' => t('If activated in DIO process, re-sends the confirmation email on every subscribe'),
    ),
    'use subaccounts' => array(
      'title' => t('use subaccounts'),
      'description' => t('Use subaccounts'),
    ),
    'use agency access' => array(
      'title' => t('use agency access'),
      'description' => t('Use agency access'),
    ),
    'use developer key' => array(
      'title' => t('use developer key'),
      'description' => t('Use developer key'),
    ),
    'access conversion pixel' => array(
      'title' => t('access conversion pixel'),
      'description' => t('Access conversion pixel in campaigns'),
    ),
    'ckfinder add S3 content include images' => array(
      'title' => t('ckfinder add S3 content include images'),
      'description' => t('Add images and subfolders to the content_inlcude folder on S3 (support and external designers)'),
    ),
    'ckfinder edit S3 content include images' => array(
      'title' => t('ckfinder edit S3 content include images'),
      'description' => t('Edit, delete images and subfolders in the content_inlcude folder on S3 (support)'),
    ),

    // --- Translation ---

    'access translation translater' => array(
      'title' => t('Klick-Tipp Translater'),
      'description' => t('Edit translations and submit to ledger.'),
    ),

    'access translation admin' => array(
      'title' => t('Klick-Tipp Translatation Admin'),
      'description' => t('Edit translations and submit to ledger and publish.'),
    ),

    // --- Features 2019 ---

    'access smart template import' => array(
      'title' => t('access smart template import'),
      'description' => 'Enables smart template import',
    ),

    'access simple copy form/signature/opt-in' => array(
      'title' => t('access simple copy form/signature/opt-in'),
      'description' => 'Adds new button to simply copy forms, signatures and opt-in processes',
    ),

    // Cockpit

    //TODO: remove if the Limiter is available for everybody
    //@see api_resource.inc -> _klicktippapi_resource_account_index()
    //@see stae-edit-wait.component.ts -> ngOnInit()
    'access feature limiter' => array(
      'title' => t('Klick-Tipp Automation Limiter'),
      'description' => t('This feature is not published yet, activate for special accounts only.'),
    ),

    // TM4J Test Pages

    'access tm4j test pages' => array(
      'title' => 'Klick Tipp TM4J Test Pages',
      'description' => 'Allow access to the tm4j test pages',
    ),

    // subscriber duplicates

    'access duplicates management' => array(
      'title' => t('Access duplicates management'),
      'description' => t('Allow customers to access pages related to subscriber duplicates (e.g. dashboard'),
    ),

    // ajax search

    'access ajax subscriber search' => array(
      'title' => t('Access AJAX subscriber search'),
      'description' => t('Activate the AJAX subscriber search'),
    ),

    // Beamer notifications

    'access beamer' => array(
      'title' => 'Access Beamer notifications',
      'description' =>'Activate to show the beamer icon in the menu',
    ),

    'suppress beamer for automated testing' => array(
      'title' => 'Suppress beamer for automated testing',
      'description' =>'Activate to not show beamer alerts or popups for automated test users (would disrupt tests, e2e)',
    ),

    'suppress product fruits for automated testing' => array(
      'title' => 'Suppress Product Fruits for automated testing',
      'description' => 'Activate to not show Product Fruits alerts or popups for automated test users (would disrupt tests, e2e)',
    ),

    'suppress captcha for automated testing' => array(
      'title' => 'Suppress Captcha for automated testing',
      'description' => 'Activate to deactivate captcha for automated test users (would disrupt tests, e2e)',
    ),

    // transactional emails
    'access send transactional' => array(
      'title' => 'access send transactional',
      'description' => 'Activates checkbox to send first email without unsubscribe link',
      )
  );
}

/**
 * Page callback: displays the user page of main account
 *
 * @return array a render array for either a user profile or a login form.
 *
 * @see user_page()
 */
function klicktipp_user_page() {
  global $user;
  if ($user->uid) {
    menu_set_active_item('user/' . Subaccount::FindMainAccount());
    return menu_execute_active_handler(NULL, FALSE);
  }
  return user_page();
}

/**
 * grant access for user/me (each user its own, subaccounts their main account, admins all)
 * @param stdClass $account
 */
function klicktipp_user_view_access($account) {

  if (user_view_access($account)) {
    // this is a main account
    return TRUE;
  }

  // grant access to subaccount
  return (Subaccount::FindMainAccount() == $account->uid);
}

/**
 * check access for email marketing functions (Roles Standard, Premium, Deluxe)
 * plain Affiliates just have the role "authenticated user" => access for partner program and edit account
 * @param stdClass $account
 * @param mixed $subaccounttype : grant access for subaccount types (use SUBACCOUNT_NONE to deny for all)
 * @param $needsKlickTippAccess : TRUE if user needs access to email marketing functions
 */
function klicktipp_user_edit_access($account, $subaccounttype = Subaccount::SUBACCOUNT_ADMINISTRATOR, $needsKlickTippAccess = TRUE) {

  // true if user has klicktipp access or klicktipp access is not needed
  $accessKlickTipp = (!$needsKlickTippAccess || user_access('access klicktipp', $account));

  if (function_exists('_administerusersbyrole_check_access')) {
    if (_administerusersbyrole_check_access($account, 'edit') && $accessKlickTipp) {
      // this is a main account or support user
      return TRUE;
    }
  }

  if (!VarDataProcessingOrder::HasAccess($account->uid)) {
    //Note: after finishing the new first login process, the user will be redirected
    //      to the front page and since he has not confirmed the DPO, the contract will
    //      be shown to him, which is not ideal
    //      => disable DPO check for the front page
    //      The DPO process will be revised with DEV-1844
    if (!drupal_is_front_page()) {
      return FALSE;
    }
  }

  if (user_edit_access($account) && $accessKlickTipp) {
    // this is a main account with access to klicktipp email marketing functions
    return TRUE;
  }

  // make sure, we have a subaccount access
  if (Subaccount::FindMainAccount() != $account->uid) {
    // not a subaccount of accessed user
    return FALSE;
  }

  // this is a subaccount access, which might be denied at all
  // note: use klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_NONE) to deny subaccount access
  $subaccounttype = intval($subaccounttype);
  if (empty($subaccounttype)) {
    return FALSE;
  }

  // this is a subaccount access, so check access type
  global $user;
  
  return Subaccount::checkSubAccountAccess($user, $account, intval($subaccounttype));

}

function klicktipp_stc_user_edit_access($account, $subaccounttype = Subaccount::SUBACCOUNT_ADMINISTRATOR) {

  if (function_exists('_administerusersbyrole_check_access')) {
    if (_administerusersbyrole_check_access($account, 'edit') && user_access('access splittest-club', $account)) {
      // this is a main account or support user
      return TRUE;
    }
  }

  if (!VarDataProcessingOrder::HasAccess($account->uid)) {
    return FALSE;
  }

  if (user_edit_access($account) && user_access('access splittest-club', $account)) {
    // this is a main account
    return TRUE;
  }

  // make sure, we have a subaccount access
  if (Subaccount::FindMainAccount() != $account->uid) {
    // not a subaccount of accessed user
    return FALSE;
  }

  // this is a subaccount access, which might be denied at all
  // note: use klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_NONE) to deny subaccount access
  $subaccounttype = intval($subaccounttype);
  if (empty($subaccounttype)) {
    return FALSE;
  }

  // this is a subaccount access, so check access type
  global $user;
  $info = Subaccount::FromID($user->uid, $account->uid);
  if (empty($info)) {
    // impossible, as FindMainAccount already returned that set
    return FALSE;
  }
  switch ($info->GetData('SubaccountType')) {
    case Subaccount::SUBACCOUNT_ADMINISTRATOR:
      // all
      return TRUE;
      break;
    case Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER:
      // may create and schedule emails
      return $subaccounttype == Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER || $subaccounttype == Subaccount::SUBACCOUNT_TEXTER
      || $subaccounttype == Subaccount::SUBACCOUNT_BASICS;
      break;
    case Subaccount::SUBACCOUNT_TEXTER:
      // may create emails, but cannot schedule
      return $subaccounttype == Subaccount::SUBACCOUNT_TEXTER || $subaccounttype == Subaccount::SUBACCOUNT_BASICS;
      break;
    case Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT:
      // search contacts and change single contacts
      return $subaccounttype == Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT || $subaccounttype == Subaccount::SUBACCOUNT_BASICS;
      break;
    case Subaccount::SUBACCOUNT_WEBSITE_BUILDER:
      // all configuration, like forms, lists, tags, etc.
      return $subaccounttype == Subaccount::SUBACCOUNT_WEBSITE_BUILDER || $subaccounttype == Subaccount::SUBACCOUNT_BASICS;
      break;
    case Subaccount::SUBACCOUNT_API_USER:
      // api only
      return $subaccounttype == Subaccount::SUBACCOUNT_API_USER;
      break;
  }

  return FALSE;
}

/**
 * Some features are only accessable with certain roles (premium, deluxe, ...)
 * if no access, check if an upsell is configured, then show the menu link anyways
 * @param string $perm : permisssion string of the feature
 * @param string $include : variable_get key that stores the include path
 * @param int $subaccounttype : grant access for subaccount types (use SUBACCOUNT_NONE to deny for all)
 * @return bool
 */
function klicktipp_upsell_access($account, $perm, $include, $subaccounttype = Subaccount::SUBACCOUNT_ADMINISTRATOR) {

  if (!klicktipp_user_edit_access($account, $subaccounttype)) {
    //user has no access to this account
    return FALSE;
  }

  if (user_access($perm)) {
    // user or the support has access to the feature, show menu link
    return TRUE;
  }

  global $user;
  if (($user->uid != $account->uid) && (Subaccount::FindMainAccount() == $account->uid)) {
    // this is a subaccount access, so check if main account has access (dont show upsell)
    return user_access($perm, $account);
  }

  //get upsell include filename
  $file = variable_get($include, '');
  if (empty($file) || !file_exists($file)) {
    return FALSE;
  } //an upsell is not configured, do not show the menu link

  return TRUE; //show menu link for the upsell

}

function klicktipp_stc_upsell_access($account, $perm, $include, $subaccounttype = Subaccount::SUBACCOUNT_ADMINISTRATOR) {
  if (!klicktipp_stc_user_edit_access($account, $subaccounttype)) {
    //user has no access to this account
    return FALSE;
  }

  if (user_access($perm)) {
    // user or the support has access to the feature, show menu link
    return TRUE;
  }

  global $user;
  if (($user->uid != $account->uid) && (Subaccount::FindMainAccount() == $account->uid)) {
    // this is a subaccount access, so check if main account has access (dont show upsell)
    return user_access($perm, $account);
  }

  //get upsell include filename
  $file = variable_get($include, '');
  if (empty($file) || !file_exists($file)) {
    return FALSE;
  } //an upsell is not configured, do not show the menu link

  return TRUE; //show menu link for the upsell

}

/**
 * for use in afix of website splittest
 */

function klicktipp_stc_splittest_cache($account, $ToolID) {
  // menue callbacks are called multiple times in row for the same splittest, so lets cache it
  static $SplittestObject;

  /** @var ToolWebsiteSplittest $SplittestObject */
  if (empty($SplittestObject) || $SplittestObject->GetData('ToolID') != $ToolID) {
    $SplittestObject = ToolWebsiteSplittest::FromID($account->uid, $ToolID);
    if (empty($SplittestObject)) {
      return FALSE;
    }
  }

  return $SplittestObject;
}

function klicktipp_stc_splittest_access_callback($account, $ToolID, $target, $perm, $include, $subaccounttype = Subaccount::SUBACCOUNT_ADMINISTRATOR) {
  if (!klicktipp_stc_upsell_access($account, $perm, $include, $subaccounttype)) {
    return FALSE;
  }

  if (empty($target)) {
    return FALSE;
  }
  $target = intval($target);
  if ($target < 1 || $target > ToolWebsiteSplittest::SPLITTEST_MAX_TARGETS) {
    return FALSE;
  }

  /** @var ToolWebsiteSplittest $SplittestObject */
  $SplittestObject = klicktipp_stc_splittest_cache($account, $ToolID);
  if (empty($SplittestObject)) {
    return FALSE;
  }

  // return TRUE if target is defined
  return !empty($SplittestObject->GetData('targets')[$target-1]);
}

/**
 * @deprecated use AccountManager::hasFeatureDrupalAccess() instead
 * for use inside functions. Grant access to single features.
 * @param $account
 * @param $perm
 * @return bool
 */
function klicktipp_feature_access($account, $perm) {

  if (user_access($perm)) {
    // user or the support has access to the feature
    return TRUE;
  }
  if (Subaccount::FindMainAccount() == $account->uid) {
    // this is a subaccount access, so check if main account has access
    if (user_access($perm, $account)) {
      return TRUE;
    }
  }

  // show upsell
  return FALSE;
}

/**
 * Menu access function to show Wufoo Affix tab in subscriber edit
 * @param $account
 * @param $subaccounttype
 * @param $needsKlickTippAccess
 *
 * @return bool
 * @throws \Doctrine\DBAL\Exception
 */
function klicktipp_subscriber_wufoo_tab_access($account, $subaccounttype = Subaccount::SUBACCOUNT_ADMINISTRATOR, $needsKlickTippAccess = TRUE): bool
{

  if (!klicktipp_user_edit_access($account, $subaccounttype, $needsKlickTippAccess)) {
    // generally no access
    return FALSE;
  }

  // only show tab if the user has at least 1 wufoo form
  $sql = "SELECT COUNT(*) FROM {listbuilding} WHERE RelOwnerUserID = :user AND BuildType = :buildType";
  $result = db_query($sql, [
    ':user' => $account->uid,
    ':buildType' => Listbuildings::TYPE_WUFOO
  ])->fetchField();

  return !empty($result);
}

/**
 * klicktipp 'order' and 'partnerprogram' page: grant access to everybody except subaccount users
 */
function klicktipp_order_access() {

  global $user;
  if (Subaccount::FindMainAccount() != $user->uid) {
    // this is a subaccount access, so dont allow to upgrade
    return FALSE;
  }

  // to everybody else (incl anonymous)
  return TRUE;
}

/**
 * Access function for menu callback and content display
 */
function klicktipp_account_data_processing_order_access($account) {

  $activated = variable_get('klicktipp_data_processing_order_check', 0);

  if (!$activated) {
    return FALSE;
  }

  return user_edit_access($account);

}

/**
 * access callback for campaign senddate
 */
function klicktipp_campaign_senddate_menu_access($account, $CampaignID) {

  /** @var Campaigns $ObjectCampaign */
  $ObjectCampaign = Campaigns::FromID($account->uid, $CampaignID);
  if (empty($ObjectCampaign)) {
    return FALSE;
  }

  return $ObjectCampaign->canEditSendDate();
}

/**
 * access callback for campaign senddate
 */
function klicktipp_website_splittest_embed_menu_access($account, $ToolID) {

  if (!klicktipp_stc_upsell_access($account, 'access splittest-club', 'klicktipp_content_include_splittest_upsell', Subaccount::SUBACCOUNT_WEBSITE_BUILDER)) {
    return FALSE;
  }

  /** @var ToolWebsiteSplittest $ToolSplittest */
  $ToolSplittest = ToolWebsiteSplittest::FromID($account->uid, $ToolID);
  if (empty($ToolSplittest)) {
    return FALSE;
  }

  $SplittestData = $ToolSplittest->GetData();

  if ($SplittestData['sptype'] == ToolWebsiteSplittest::SPLITTEST_TYPE_URL && $SplittestData['variants'][0]['spurl'] == "http://") {
    // do not show the affix tab if an URL splittest has no url
    return FALSE;
  }

  if ($SplittestData['sptype'] == ToolWebsiteSplittest::SPLITTEST_TYPE_CONTENT && $SplittestData['variants'][0]['content'] == "") {
    // do not show the affix tab if a content splittest has no content
    return FALSE;
  }

  return TRUE;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_listinterfaces_IPN_access($account, $ipn) {

  return klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_WEBSITE_BUILDER) &&
  (user_access($ipn, $account) /* main account */ || user_access($ipn) /* or support user */);

}

/**
 * grants access to subaccount switch if there are subaccounts assignedklicktipp_subaccount_switch_access
 */
function klicktipp_subaccount_switch_access() {
  global $user;

  // grant access to admins for changing the user menu
  if (user_access('administer menu')) {
    return TRUE;
  }

  // check access to the account
  if (!user_edit_access($user)) {
    return FALSE;
  }

  // show menu item only, if subaccounts are assigned
  return count(Subaccount::GetSubAccounts($user->uid)) > 0;
}

/**
 * grants access to view subaccount
 */
function klicktipp_subaccount_view_access($account, $SubaccountID) {
  global $user;

  // grant access to admins for changing the user menu
  if (user_access('administer menu')) {
    return TRUE;
  }

  // check access to the account
  if (!user_edit_access($user)) {
    return FALSE;
  }

  // allow, if the logged in user is the customer or the agency
  return $user->uid == $account->uid || $user->uid == $SubaccountID;
}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_custom_field_tab_access($account, $CategoryID) {

  $TabTitle = $account->CustomFieldsCategoryTabs[$CategoryID];

  return (klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT) && !empty($TabTitle));

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_custom_field_tab_title($account, $CategoryID) {

  $TabTitle = $account->CustomFieldsCategoryTabs[$CategoryID];

  return $TabTitle;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_import_access($account) {

  return (klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT) &&
    (user_access('angular subscriber import', $account) || user_access('import for support', $account)));

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_bounce_check_access($account) {

  return (klicktipp_user_edit_access($account) && user_access('access bounce check', $account));

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_add_message_access($account) {

  if (!klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_WEBSITE_BUILDER)) {
    return FALSE;
  }

  //check if unsubscription messages limit is reached
  $UserGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID, TRUE);
  $MaxMessages = empty($UserGroup['Data']['LimitUnsubscriptionMessages']) ? 0 : $UserGroup['Data']['LimitUnsubscriptionMessages'];
  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);

  return ($MaxMessages == UserGroups::LIMIT_UNLIMITED || count($Unsubscriptions['Dialogs'] ?? []) < $MaxMessages);

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_unsubscriptions_edit_message_access($account, $EntityID) {

  if (!klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_WEBSITE_BUILDER) || empty($EntityID)) {
    return FALSE;
  }

  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);

  return !empty($Unsubscriptions['Dialogs'][$EntityID]);

}

/**
 * only customers with this access can use and create unsubscription urls
 */
function klicktipp_unsubscriptions_url_access($account) {

  return (klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_WEBSITE_BUILDER) && user_access('access unsubscription urls', $account));

}

/**
 * only customers with this access can use and edit unsubscription urls
 */
function klicktipp_unsubscriptions_url_edit_access($account, $EntityID) {

  if (!klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_WEBSITE_BUILDER) || !user_access('access unsubscription urls', $account)) {
    return FALSE;
  }

  //Edit URL: check if URL exists
  $Unsubscriptions = klicktipp_unsubscriptions_get_user_unsubscriptions($account);

  return !empty($Unsubscriptions['URLs'][$EntityID]);

}

/**
 * plugin access by permission or tagging in marketing account
    * who has access? (see https://klicktipp.atlassian.net/wiki/spaces/DEV/pages/edit-v2/**********#Permissions)
    * 1. all admins 'administer klicktipp'
    * 2. plugin is "published" + permission as requested in "permission" attribute
    * 3. plugin is not "published" + "permission" + email address of user in marketing account (380) has tag configured in plugin
 *
 * @param $account
 * @param array $plugin
 * @param bool $forCreate
 * @param bool $grantAdmin
 *
 * @return bool
 */
function klicktipp_plugin_access($account, $plugin = [], bool $forCreate = false, bool $grantAdmin = true) {
  // All subscribers in our marketing account have ReferenceID 0 at the moment
  $ReferenceID = 0;

  // grant admin access ($grant_admin = false for tests)
  if ($grantAdmin && user_access('administer klicktipp')) {
    return true;
  }

  // check if (main) account has general plugin access
  if (!klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_WEBSITE_BUILDER)) {
    return false;
  }
  if (!user_access('access klicktipp', $account)) {
    return false;
  }

  // if no plugin is given, we ask for general plugin access
  if (empty($plugin)) {
    return true;
  }

  // --- published with regular access

  // missing permissions?
  $permission = empty($plugin['permission']) ? 'access klicktipp' : $plugin['permission'];
  if (!user_access($permission, $account)) {
    return false;
  }

  // check dor Digistore payment
  if ($plugin['Create']['method'] == Plugin::PLUGIN_CREATE_METHOD_ADDON) {
    $addOnManager = AddOnManager::noInjectionFactory();
    if (!$addOnManager->isAvailable($account)) {
      // AddOn for Digistore customers only
      return false;
    }
    if (!$forCreate) {
      // check payment of AddOn
      if (!$addOnManager->getAddOnStatusForEntity(
          $plugin['PluginID'],
          $account->uid) == AddonEntity::STATUS_COMPLETE)
      {
        return false;
      }
    }
  }
  
  // published? access greanted by permission
  if (!empty($plugin['published'])) {
    return true;
  }

  // --- right permission, not published, with beta tester access

  // "beta access" by tagging of user in marketing account
  $marketing_uid = variable_get('klicktipp_marketing_account_id', 0);
  if (empty($marketing_uid)) {
    return false;
  }

  $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($marketing_uid, $account->mail);
  if (empty($SubscriberID)) {
    return false;
  }

  // check if account has plugin access by tagging
  $Tagging = Subscribers::RetrieveTagging($marketing_uid, $plugin['AccessTagID'], $SubscriberID, $ReferenceID);
  if (empty($Tagging)) {
    return false;
  }

  return true;
}

/**
 * load a plugin by id - allows %klicktipp_plugin in menu items
 * @param string $pluginid
 * @return array Plugin
 */
function klicktipp_plugin_load($pluginid) {
  return Plugin::get_plugin($pluginid);
}

/**
 * find the user for a cockpit redirect (that has no user by design)
 * considers the logged in user, a subaccount user and a potential support user
 */
function klicktipp_find_mainaccount() {

  global $user;

  // support access with special cookie (set in customer menu)
  $cookie = $_COOKIE[DatabaseTableCRUD::COOKIENAME_CUSTOMER];
  if (!empty($cookie)) {
    if (!user_access('administer klicktipp')) {
      // user logged in is not a support user
      return FALSE;
    }
    // get the main account
    $account = user_load($cookie);
  }
  else {
    // get uid by cookies (session or subaccount-cookie)
    $uid = Subaccount::FindMainAccount();
    // get account
    if ($uid == $user->uid) {
      // the main account is the user logged in
      $account = $user;
    }
    else {
      // get the main account
      $account = user_load($uid);
    }
  }

  return $account;
}

/**
 * Access function for private file downloads "private://..."
 * Implements hook_file_download()
 */
function klicktipp_file_download($uri) {

  $scheme = file_uri_scheme($uri);

  if ($scheme != 'private') {
    //not handled by the klicktipp module
    return NULL;
  }

  $args = explode('/', str_replace("$scheme://", '', $uri));

  if ($args[0] == VarImport::ENV_DATA_USERS_DIRECTORY) {
    //import reports have a different uri format
    //$scheme://$op=import/<UserID-Slice>/$UserID/$ImportID/Filename
    $UserID = $args[2];
    $op = VarImport::ENV_DATA_USERS_DIRECTORY;
  }
  else {
    //default uri format
    //$scheme://$UserID/$op/Filename
    $UserID = $args[0];
    $op = $args[1];
  }

  $account = user_load($UserID);

  if (empty($UserID) || empty($account) || !file_exists($uri)) {
    klicktipp_display_whitelabel_message(t('Error Occurred'), t('The requested file was not found.'));
    //this won't return
  }

  //do not check for klicktipp access, possible affiliate/consultant account
  if (!klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT, FALSE)) {
    klicktipp_display_whitelabel_message(t('Access denied'), t('You do not have access to download this file. Please log in as the user who requested the export.'), '', $UserID);
    //this won't return
  }

  switch ($op) {

    case 'custstats':

      $date = explode('custstats/', $uri);
      $filename = $date[1];

      return array(
        'Content-Type' => 'text/csv; charset=utf-8',
        'Content-Disposition' => "attachment; filename=\"$filename\"",
      );

    case 'export':

      $date = explode('export/', $uri);
      $filename = strtr('Klick_Tipp_Export_!date', array('!date' => $date[1]));

      return array(
        'Content-Type' => 'text/csv; charset=utf-8',
        'Content-Disposition' => "attachment; filename=\"$filename\"",
      );

    case 'duplicates-export':

      $pathParts = explode(KLICKTIPP_DUPLICATES_DOWNLOAD_DIR . '/', $uri);
      $filename = $pathParts[1];

      return array(
        'Content-Type' => 'text/csv; charset=utf-8',
        'Content-Disposition' => "attachment; filename=\"$filename\"",
      );

    case 'multisend-smarttag-report.csv':

      $filename = $op;

      return array(
        'Content-Type' => 'text/csv; charset=utf-8',
        'Content-Disposition' => "attachment; filename=\"$filename\"",
      );


    case 'fullcontact':

      $date = explode('fullcontact/', $uri);
      $filename = strtr('Klick_Tipp_FullContact_!date', array('!date' => $date[1]));

      return array(
        'Content-Type' => 'text/csv; charset=utf-8',
        'Content-Disposition' => "attachment; filename=\"$filename\"",
      );

    case 'dpo':

      return array(
        'Content-Type' => 'application/pdf; charset=utf-8',
        'Content-Disposition' => "attachment; filename=\"{$args[2]}\"",
      );

    case VarImport::ENV_DATA_USERS_DIRECTORY:

      $filename = array_pop($args);

      return array(
        'Content-Type' => 'text/csv; charset=utf-8',
        'Content-Disposition' => "attachment; filename=\"$filename\"",
      );

  }

  return NULL; //not handled by the klicktipp module

}

/**
 * Implements hook_cronapi(). See ultimate_cron.api.php
 */
function klicktipp_cronapi() {
  $items = array();

  /*
   * once a day
   */
  $items['klicktipp_nightly_cron'] = array(
    'title' => 'Nightly klicktipp cron',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // daily at 1
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('0 1 * * *'),
      ),
    ),
  );

  $items['klicktipp_newsletter_deletion_cron'] = array(
    'title' => 'Klicktipp maintenance cron',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('*/20 * * * *'),
      ),
    ),
  );

  $items['klicktipp_newsletter_tags_deletion_cron'] = array(
    'title' => 'Klicktipp maintenance cron',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('*/20+10 * * * *'),
      ),
    ),
  );

  $items['klicktipp_tmp_transaction_queue_obsolete_items_deletion_cron'] = array(
    'title' => 'Klicktipp maintenance cron',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('*/20+15 * * * *'),
      ),
    ),
  );

  $items['klicktipp_tmp_autoresponder_queue_obsolete_items_deletion_cron'] = array(
    'title' => 'Klicktipp maintenance cron',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('*/20+5 * * * *'),
      ),
    ),
  );

  $items['klicktipp_morning_cron'] = array(
    'title' => 'Daily klicktipp cron',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // daily at 6
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('0 6 * * *'),
      ),
    ),
  );

  /*
   * every hour
   */
  $items['klicktipp_hourly_cron'] = array(
    'title' => 'Hourly klicktipp cron',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Once an hour
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('17 * * * *'),
      ),
    ),
  );

  /*
   * every 10 mnutes
   */
  $items['klicktipp_every10minutes_cron'] = array(
    'title' => 'Every 10 minutes klicktipp cron',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every 10 minutes
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('*/10 * * * *'),
      ),
    ),
  );

  /*
   * every minute
   */
  $items['klicktipp_transactional_cron'] = array(
    'title' => 'Send transactional emails',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every minute
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('* * * * *'),
      ),
    ),
  );

  $items['klicktipp_send_cron'] = array(
    'title' => 'Send campaigns',
    'file' => 'cron_send.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every minute
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('* * * * *'),
      ),
    ),
  );

  $items['klicktipp_process_winner_cron'] = array(
    'title' => 'Process splittest campaigns',
    'file' => 'cron_send.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every minute
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('* * * * *'),
      ),
    ),
  );

  $items['klicktipp_process_finished_cron'] = array(
    'title' => 'Process finished campaigns',
    'file' => 'cron_send.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every minute
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('* * * * *'),
      ),
    ),
  );

  $items['klicktipp_deliver_pregenerated_emails_cron'] = array(
    'title' => 'Start delivery of pregenerated emails',
    'file' => 'cron_send.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every minute
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('* * * * *'),
      ),
    ),
  );

  $items['klicktipp_minutely_cron'] = array(
    'title' => 'Minutely klicktipp cron',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every minute
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('* * * * *'),
      ),
    ),
  );

  $items['klicktipp_aged_cron'] = array(
    'title' => 'Check aged subscribers',
    'file' => 'cron_aged.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every minute
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('* * * * *'),
      ),
    ),
  );

  $items['klicktipp_datadog_cron'] = array(
    'title' => 'Submit datadog metrics',
    'file' => 'cron_datadog.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every minute
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('* * * * *'),
      ),
    ),
  );

  $items['klicktipp_gmail_preview_cron'] = array(
    'title' => 'Process gmail previews',
    'file' => 'cron_send.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every minute
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('* * * * *'),
      ),
    ),
  );

  $items['klicktipp_bee_token_update_cron'] = array(
    'title' => 'Update BEE access tokens',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    // Every fourth minute (since the token is valid for 5 minutes)
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('*/4 * * * *'),
      ),
    ),
  );

  $items['klicktipp_cron_subscriber_stats'] = array(
    'title' => 'Calculate subscribers statistic',
    'file' => 'cron_subscriber_stats.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('0 3 * * 1'), // Every monday 03:00.
      ),
    ),
  );

  $items['klicktipp_sync_digistore_addon_status_cron'] = array(
    'title' => 'Sync status of user addons with digistore',
    'file' => 'cron_klicktipp.inc',
    'file path' => KLICKTIPP_PATH . '/cli',
    'scheduler' => array(
      'name' => 'crontab',
      'crontab' => array(
        'rules' => array('0 5 * * *'),
      ),
    )
  );

  return $items;
}

/**
 * Implementation of hook_cron_queue_info
 *
 */
function klicktipp_cron_queue_info() {
  return QueueCreator::queueInfo();
}

/**
 * move customers to/from separate slices
 */
function move_customers_in_separate_slices_queue($data) {
  Libraries::include("cron_klicktipp.inc", '/cli');
  move_customers_in_separate_slices_worker($data);
}

/**
 * bouncecheck queue
 */
function _klicktipp_bouncecheck_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_bouncecheck_queue_worker($data);
}

/**
 * soft bouncecheck queue
 */
function _klicktipp_softbouncecheck_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_softbouncecheck_queue_worker($data);
}

/**
 * delete newsletter queue
 */
function _klicktipp_delete_newsletter_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_delete_newsletter_queue_worker($data);
}

/**
 * delete taggings queue
 */
function _klicktipp_delete_taggings($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_delete_taggings_worker($data);
}

/**
 * subscriber tagging queue
 */
function klicktipp_subscriber_tagging_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  klicktipp_subscriber_tagging_queue_worker($data);
}

/**
 * switch to multivalue
 */
function switch_multivalue_taggings_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  switch_multivalue_taggings_queue_worker($data);
}

function switch_multivalue_custom_fields_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  switch_multivalue_custom_fields_queue_worker($data);
}

/**
 * switch from multivalue to singlevalue
 */
function switch_multivalue_to_singlevalue_taggings_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  switch_multivalue_to_singlevalue_taggings_queue_worker($data);
}

/**
 * custom field value processing after switching a multi value custom field to single value
 */
function switch_multivalue_to_singlevalue_custom_fields_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  switch_multivalue_to_singlevalue_custom_fields_queue_worker($data);
}

/**
 * subscriber deletion queue
 */
function klicktipp_subscriber_deletion_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  klicktipp_subscriber_deletion_queue_worker($data);
}

/**
 * subscription deletion queue
 */
function klicktipp_subscription_deletion_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  klicktipp_subscription_deletion_queue_worker($data);
}

/**
 * subscriber import queue
 */
function klicktipp_subscriber_import_queue($data) {
  Libraries::include("import.inc", "/forms");
  return klicktipp_subscriber_import_queue_worker($data);
}

function _klicktipp_subscriber_import_batch_finished_wrapper($success, $results, $operations) {
  Libraries::include("import.inc", "/forms");
  _klicktipp_subscriber_import_batch_finished($success, $results, $operations);
}

/**
 * senderscore queue worker
 */
function _klicktipp_senderscore_queue($data) {
  Libraries::include("senderscore.inc");
  _klicktipp_senderscore_queue_worker($data);
}

/**
 * zy0.de spam traps queue worker
 */
function _klicktipp_zy0de_queue($data) {
  Libraries::include("senderscore.inc");
  _klicktipp_zy0de_queue_worker();
}

/**
 * request_forwarders queue
 */
function _klicktipp_request_forwarders_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_request_forwarders_queue_worker($data);
}

/**
 * subscriber_export queue
 */
function _klicktipp_subscriber_export_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_subscriber_export_queue_worker($data);
}

function _klicktipp_subscriber_duplicates_export_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_subscriber_duplicates_export_queue_worker($data);
}

/**
 * subscriber limit queue worker:
 * check subscriber count for each customer and notify them if he is about to reach his subscriber limit
 */
function _klicktipp_subscriber_limit_queue($data) {
  Libraries::include("marketing.inc");
  _klicktipp_subscriber_limit_queue_worker($data);
}

/**
 * npscore queue worker
 * push ratings to google sheet and helpscout mailbox
 */
function _klicktipp_npscore_queue($data) {
  Libraries::include("npscore.inc");
  _klicktipp_npscore_queue_worker($data);
}

/**
 * subscriber limit queue worker:
 * check subscriber count for each customer and notify them if he is about to reach his subscriber limit
 */
function _klicktipp_delete_pending_subscribers_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_delete_pending_subscribers_queue_worker($data);
}

/**
 * stop inactive users queue worker:
 * stop campaigns from inactive users
 */
function _klicktipp_stop_inactive_users_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_stop_inactive_users_queue_worker($data);
}

/**
 * check if subscriptions are active (have a valid and not expired payment)
 */
function _klicktipp_active_payments_queue($data) {
  Libraries::include("cron_klicktipp.inc", "/cli");
  _klicktipp_active_payments_queue_worker($data, time());
}

/**
 * sum up "LeadValue" from payments and add to global custom field "LeadValue"
 * and create subscriber history
 *
 * $data = [
 *   'pid' => ... // payment id (offset)
 *   'size' => ... // limit of query
 * ]
 *
 * tables used:
 * listbuilding, payments, subscriber_history, custom_field_values
 *
 * Note: this is used twice: intro of feature LeadValue and amember migration (so remove afterwards)
 */
function _klicktipp_aggregate_lead_value_from_payments_queue($data) {

  //TODO processlog (throws hardening messages)

  $FieldInformation = CustomFields::$GlobalCustomFieldDefs['LeadValue'];

  $pid = $data['pid'];
  $size = $data['size'];

  $result = db_query("SELECT p.*, l.BuildType, l.Name FROM {payments} p ".
    " INNER JOIN {listbuilding} l ON p.RelOwnerUserID = l.RelOwnerUserID AND p.RelBuildID = l.BuildID ".
    " WHERE PaymentID >= $pid AND PaymentID < $pid + $size ORDER BY PaymentID ASC");
  while ($ArrayPayment = kt_fetch_array($result)) {

    $result2 = db_query("SELECT 1 FROM {subscriber_history} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND HistoryType = :HistoryType AND HistoryDate = :HistoryDate", [
      ':RelOwnerUserID' => $ArrayPayment['RelOwnerUserID'],
      ':RelSubscriberID' => $ArrayPayment['RelSubscriberID'],
      ':HistoryType' => Subscribers::HISTORY_PAYMENT,
      ':HistoryDate' => $ArrayPayment['CreatedOn']
    ])->fetchField();
    if (empty($result2)) {

      $Name = $ArrayPayment['Name'];
      $EditURL = '';
      switch ($ArrayPayment['BuildType']) {
        case Listbuildings::TYPE_DIGISTORE:
          $EditURL = APP_URL . "listbuilding/{$ArrayPayment['RelOwnerUserID']}/digistore/{$ArrayPayment['RelBuildID']}/edit";
          break;
        case Listbuildings::TYPE_AFFILICON:
          $EditURL = APP_URL . "listbuilding/{$ArrayPayment['RelOwnerUserID']}/affilicon/{$ArrayPayment['RelBuildID']}/edit";
          break;
        case Listbuildings::TYPE_CLICKBANK:
          $EditURL = APP_URL . "listbuilding/{$ArrayPayment['RelOwnerUserID']}/clickbank/{$ArrayPayment['RelBuildID']}/edit";
          break;
        case Listbuildings::TYPE_PAYPAL:
          $EditURL = APP_URL . "listbuilding/{$ArrayPayment['RelOwnerUserID']}/paypal/{$ArrayPayment['RelBuildID']}/edit";
          break;
        default:
          break;
      }

      // remove listbuilding values from payment
      unset($ArrayPayment['Name']);
      unset($ArrayPayment['BuildType']);

      // write subscriber history
      Subscribers::WriteHistory($ArrayPayment['RelOwnerUserID'], $ArrayPayment['RelSubscriberID'], Subscribers::HISTORY_PAYMENT,
        [
          'PaymentID' => $ArrayPayment['PaymentID'],
          'BuildID' => $ArrayPayment['RelBuildID'],
          'Payment' => $ArrayPayment,
          'Amount' => $ArrayPayment['Amount'],
          'Currency' => $ArrayPayment['Currency'],
          'ProductName' => $Name,
          'ProductEditURL' => $EditURL,
          'IPN' => [],
        ],
        $ArrayPayment['CreatedOn']);

      // sum up to global custom field "LeadValue"
      $ReferenceID = 0; // LeadValue is not a multivalue field
      $LeadValue = CustomFields::GetCustomFieldData($ArrayPayment['RelOwnerUserID'], $ArrayPayment['RelSubscriberID'], $FieldInformation, $ReferenceID);
      $LeadValue = empty($LeadValue) ? $ArrayPayment['LeadValue'] : $LeadValue + $ArrayPayment['LeadValue'];
      CustomFields::UpdateCustomFieldData($ArrayPayment['RelOwnerUserID'], $ArrayPayment['RelSubscriberID'], $ReferenceID, $FieldInformation, $LeadValue);
    }
  }

  //TODO processlog

}

/**
 * @param int [] $userIDs
 *
 * @return void
 */
function _klicktipp_addon_status_sync_queue(array $userIDs)
{
  $addOnManager = ServiceContainer::getInstance()->get(AddOnManager::class);
  foreach ($userIDs as $userID) {
    try {
      $user = user_load($userID);
      if (!$user) {
        watchdog(
          'addons',
          'User !userID not found', ['!userID' => $userID],
          WATCHDOG_WARNING
        );
      }

      $addOnManager->syncEntitiesStatus($user);
    } catch (Throwable $exception) {
      watchdog_exception('addons', $exception, $exception->getMessage());
    }
  }
}

/**
 * batch (use batch or queue)
 */
function _klicktipp_batch_set($QueueName, $DataArray, $ReturnPath = '') {

  // get worker definition
  $queues = module_invoke_all('cron_queue_info');
  drupal_alter('cron_queue_info', $queues);
  $Info = $queues[$QueueName];
  if (empty($Info)) {
    return FALSE;
  }

  // Merge in defaults.
  $Info += array(
    'klicktipp_finished' => '_klicktipp_batch_finished',
    'klicktipp_batchlimit' => 0,
    'klicktipp_queue_slice' => 0,
    'klicktipp_notify' => TRUE,
    'klicktipp_title' => t('Processing'),
    'klicktipp_init_message' => t('Initializing.'),
    'klicktipp_progress_message' => t('Completed @current of @total.'),
    'klicktipp_error_message' => t('An error has occurred.'),
  );

  // use batch or queue?
  if (count($DataArray) < $Info['klicktipp_batchlimit']) {

    // BATCH (use drupal batch)

    $batch = array(
      'operations' => array(
        array('_klicktipp_batch_process', array($Info, $DataArray)),
      ),
      'finished' => $Info['klicktipp_finished'],
      'title' => $Info['klicktipp_title'],
      'init_message' => $Info['klicktipp_init_message'],
      'progress_message' => $Info['klicktipp_progress_message'],
      'error_message' => $Info['klicktipp_error_message'],
    );

    batch_set($batch);

    // Only needed if not inside a form _submit handler.
    // Setting redirect in batch_process.
    if (!empty($ReturnPath)) {
      batch_process($ReturnPath);
    }

    return FALSE;
  }
  else {

    // QUEUE (use beanstalk queue)

    $LogID = ProcessLog::CreateSlicedQueueItems($QueueName, $DataArray, $Info['klicktipp_queue_slice'], $Info);

    drupal_set_message($Info['klicktipp_title']);

    return $LogID;
  }

}

function _klicktipp_batch_process($Info, $DataArray, &$context) {
  if (!isset($context['sandbox']['progress'])) {
    $context['sandbox']['progress'] = 0;
    $context['sandbox']['data'] = $DataArray;
    $context['sandbox']['max'] = count($DataArray);
  }

  // process
  $data = array_shift($context['sandbox']['data']);
  $function = $Info['worker callback'];
  $context['results'][] = $function($data);

  // Update our progress information.
  $context['sandbox']['progress']++;

  // Inform the batch engine that we are not finished,
  // and provide an estimation of the completion level we reached.
  if ($context['sandbox']['progress'] < $context['sandbox']['max']) {
    $context['finished'] = $context['sandbox']['progress'] / $context['sandbox']['max'];
  }
}

function _klicktipp_batch_finished($success, $results, $operations) {
  klicktipp_get_messages();

  if (!$success) {
    // An error occurred.
    drupal_set_message(t('An error occurred while processing a batch process'), 'error');
  }
}

/**
 * Implements hook_user_login().
 * This is the final action in "user_login_submit", so we can do anything we do in a submit.
 */
function klicktipp_user_login(&$form_state, $account)
{
  // remember login time ON DEVICE,
  // login field in users table is not usable for this purpose
  // since it would be overwritten after login on another device
  $_SESSION['last_authentication'] = REQUEST_TIME;

  // unset support cookie (probably we are no longer in the same support account)
  setcookie(DatabaseTableCRUD::COOKIENAME_CUSTOMER, NULL, -1, "/", "." . KLICKTIPP_DOMAIN);

  // set a cookie on login that allows wordpress/pantheon site to identify customers
  setcookie(KLICKTIPP_USER_COOKIE, 1, strtotime('+1 years'), "/", "." . KLICKTIPP_DOMAIN);
  // set user id of current main account to cookie. This is used in Klicktipp marketing site
  $MainAccount = klicktipp_find_mainaccount();
  setcookie(KLICKTIPP_MAIN_ACCOUNT_ID_COOKIE, $MainAccount->uid, strtotime('+1 years'), "/", "." . KLICKTIPP_DOMAIN);

  // redirect to 'grantapiaccess' after login from there
  if (stripos($_GET['q'], 'grantapiaccess/', 0) === 0) {
    // redirect to where we came from, e.g. "grantapiaccess/452?url=http://www.google.de/?q="
    $options = array('query' => array('url' => $_GET['url']));
    drupal_goto($_GET['q'], $options);
  }

  // redirect to 'template' after login from there
  if (stripos($_GET['q'], 'template/', 0) === 0) {
    // redirect to where we came from, e.g. "template/15z1zfz86ed"
    drupal_goto($_GET['q']);
  }

  // redirect to 'plugins/4711/connect' after login from there
  // TODO: confirm that this part is deprecated since Keycloak
  /*
  if (stripos($_GET['q'], 'plugins/', 0) === 0) {

    $options = [];

    // redirect to where we came from with the original request
    $pluginid = str_replace(['plugins/', '/connect'], '', $_GET['q']);
    $plugin = Plugin::get_plugin($pluginid);
    if (!empty($plugin)) {
      // copy only the data we need (drop e.g. login credentials)
      $fields = PluginHookConnectRedirect::fetchInboundsFromRequest($plugin, $_REQUEST);
      foreach($fields as $key) {
        $options['query'][$key] = $_REQUEST[$key];
      }
    }

    drupal_goto($_GET['q'], $options);
  }
  */
}

/**
 * Implements hook_user_logout().
 *
 * Is executed, if user logouts actively (logout link), not in case of session
 * expiration
 */
function klicktipp_user_logout($account)
{
  drupal_goto('/auth/logout');
  drupal_exit();
}

/**
 * Implements hook_user_insert().
 */
function klicktipp_user_insert(&$edit, $account, $category) {
  // called after user is created, so we need to save additional values our own

  // update country, language
  if (empty($edit['Country'])) {
    $edit['Country'] = KLICKTIPP_DEFAULT_COUNTRY;
  }
  else {
    $edit['Country'] = check_plain($edit['Country']);
  }

  $edit['language'] = KLICKTIPP_DEFAULT_LANGUAGE;

  // update user
  db_update('users')
    ->fields(array(
      'Country' => $edit['Country'],
      'language' => $edit['language'],
    ))
    ->condition('uid', $account->uid)
    ->execute();

  // create default signature
  $SignatureID = Signatures::CreateDefaultSignature($account->uid);
  Signatures::CreateRevision($account->uid, $SignatureID);

  // create default subscription process
  $list_id = Lists::InsertDB(array(
    'Name' => '',
    'RelOwnerUserID' => $account->uid
  ));

  // set processflow default values
  VarProcessflow::SetDefaults($account->uid);

  // assign attendee roles to user if they attended a seminar
  VarConsultant::InsertAttendee($account->uid);
  VarConvention::InsertAttendee($account->uid);

  // update subscriber fields in marketing account with customer data
  Libraries::include("marketing.inc");
  klicktipp_marketing_update_account($account);

  // save tier in user var
  if (isset($edit['tier'])) {
      VarTier::SetTier($account->uid, $edit['tier']);
      unset($edit['tier']);
  }

  // save current amember product id in user var
  if (isset($edit['productID'])) {
    VarAmemberProductID::SetVariable($account->uid, $edit['productID']);
    unset($edit['productID']);
  }

  Reference::createDefaultReference($account->uid);

  // mail is nullable for some reason. prevent errors in case mail is not set
  if (!empty($account->mail)) {
    VarAdditionalAddresses::SetAddresses($account->uid, [['email' => $account->mail, 'verified' => 1, 'valid' => 1]]);
  }

  if (!empty($edit['keycloakUserId'])) {
    AccountManager::updateKeycloakUser(
      ServiceContainer::getInstance()->get(KeycloakApi::class),
      $edit['keycloakUserId'],
      $account,
      $edit['subaccountid'] ?? null
    );
  }
}

/**
 * Implements hook_user_load().
 */
function klicktipp_user_load($users) {
  // make data fields available
  foreach (array_keys($users) as $uid) {

    // load users tier, might be 0 if old account without tier limits
    $users[$uid]->tier = VarTier::GetTier($uid);

    // we cannot use $users[$uid] = drupal_unpack($user); as $users is not a reference parameter
    if ($users[$uid]->ktdata && $data = unserialize($users[$uid]->ktdata)) {
      foreach ($data as $key => $value) {
        if (!empty($key) && !isset($users[$uid]->$key)) {
          $users[$uid]->$key = $value;
        }
      }
    }
  }
}

/**
 * Implements hook_user_view().
 */
function klicktipp_user_view($account, $view_mode) {

  if ( empty($account->uid) || !klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_BASICS)) {
    //perform a redirect for guests/affiliates-only, especially search engine robots
    //the check if the user is an affiliate is handled there
    drupal_goto(Settings::get('partner-program-marketing-page-redirect'), ['external' => true]);
    drupal_exit();
  }

  if (!VarDataProcessingOrder::HasAccess($account->uid)) {
    //front page, user has not confirmed the data processing order

    //Note: after finishing the new first login process, the user will be redirected
    //      to the front page and since he has not confirmed the DPO, the contract will
    //      be shown to him, which is not ideal
    //      => disable DPO check for the front page
    //      The DPO process will be revised with DEV-1844
    // if (!drupal_is_front_page()) {
      drupal_access_denied();
      drupal_exit();
      //will not return
    // }

  }

  drupal_goto("application/$account->uid/dashboard/$account->uid");
  drupal_exit();

}

/**
 * Implements hook_user_view_alter().
 */
function klicktipp_user_view_alter(&$build) {
  unset($build['summary']);
}

/**
 * Implements hook_user_presave().
 */
function klicktipp_user_presave(&$edit, $account, $category) {
  // this is called from the amember module to cache amember data
  if ($category == 'amember') {
    // be aware: dont do anything amember related here
  }

  // called before user is updated, so we can set additional values here

  if (empty($edit['Country']) && empty($account->Country) && !empty($edit['City'])) {
    // if country is not given, but address is (indicated by city), then set country default
    $edit['Country'] = KLICKTIPP_DEFAULT_COUNTRY;
  }

  if (isset($edit['RelUserGroupID'])) {
    $edit['roles'] = _klicktipp_roles_by_group($account->uid, $edit['RelUserGroupID']);
  }

  // reset amember cache
  // if this value is already set, cache is going to be set
  if (empty($edit['profile_lastaccess'])) {
    $edit['profile_lastaccess'] = 0;
  }

  // move all our special data fields into $edit['ktdata']

  if (!empty($account->ktdata)) {
    $ktdata = unserialize($account->ktdata);
  }
  else {
    $ktdata = array();
  }

  // remember timestamp of password change
  if (!empty($edit['pass'])) {
    // Note: we set last_password_change "hardly", because it shouldn't be
    // injected/manipulated from outside
    $ktdata['last_password_change'] = REQUEST_TIME;

    // is password change related to logged in user?
    global $user;
    if (!$account->is_new && $account->uid == $user->uid) {
      $_SESSION['last_authentication'] = REQUEST_TIME;
    }
  }

  // amember cache flag
  $ktdata['profile_lastaccess'] = empty($edit['profile_lastaccess']) ? 0 : $edit['profile_lastaccess'];
  unset($edit['profile_lastaccess']);

  // clear old cache data
  $ktdata['profile_payments'] = '';

  $fields = array(
    'Unsubscriptions',
    'CustomFieldsCategoryTabs',
    'AdditionalSenderPhoneNumber',
    'UserPrivileges',
    'UserSettings',
    'TwoFactorAuthorization',
    'SubscriberLimitStatus',
    'cleverbridge_id',
    'cleverbridge_cancellation',
    'cleverbridge_update',
    'digistore_affiliate_name',
    'digistore_cancellation',
    'digistore_update',
    'digistore_receipt',
    'digistore_affiliation',
    'profile_userdata',
    'profile_memberid',
    'profile_lastaccess',
    'firstLogin'
  );
  foreach ($fields as $field) {
    if (isset($edit[$field])) {
      $ktdata[$field] = $edit[$field];
      unset($edit[$field]);
    }
  }

  $edit['ktdata'] = serialize($ktdata);

  //check if user data regarding data processing order has changed
  $invalidateDataProcessingOrder = VarDataProcessingOrder::CheckUpdate($account, $edit);
  if ($invalidateDataProcessingOrder) {
    //invalidate dpo, user has 2 weeks to confirm the new one
    VarDataProcessingOrder::Invalidate($account->uid);
  }
  $purgeOverviewCache = $invalidateDataProcessingOrder ||
    (isset($edit['RelUserGroupID']) && !$account->is_new  && $account->RelUserGroupID != $edit['RelUserGroupID']);
  if ($purgeOverviewCache) {
    UserCache::remove($account->uid, UserCache::CACHE_OVERVIEW);
  }

  if ($account->uid) {
    UserCache::remove($account->uid, UserCache::CACHE_SEGMENTS);
    UserCache::remove($account->uid, UserCache::CACHE_FEATURE_TOGGLES);
  } elseif (
    // in some test cases mail is not set. mail field is nullable in drupal db,
    // so tests work, but it makes no sense in klicktipp and in keycloak
    !empty($edit['mail']) &&
    // as long as keycloak does not work in test pipeline,
    // we prevent creating keycloak users by profile installation
    !drupal_installation_attempted()
  ) {
    $keycloakApi = ServiceContainer::getInstance()->get(KeycloakApi::class);
    $subaccountId = $edit['subaccountid'] ?? null;
    $keycloakUserId = AccountManager::createKeycloakUser(
      $keycloakApi,
      // note $account always exists here, but the needed fields are not always set
      // opposite to $edit
      (object)[
        'mail' => $edit['mail'],
        'name' => $edit['name'],
        'FirstName' => $edit['FirstName'] ?? '',
        'LastName' => $edit['LastName'] ?? '',
        !$subaccountId
      ]
    );
    if ($subaccountId) {
      try {
        AccountManager::triggerKeycloakEmailVerification($keycloakApi, $edit['mail']);
      } catch (Exception $e) {
        // we don't want to block the user creation process, if verification email could not be sent
        watchdog('keycloak', 'could not trigger e-mail verification for subaccount %email', [
          '%email' => $edit['mail'],
          '%exception' => $e->__toString(),
        ], WATCHDOG_ERROR);
      }
    }
    $edit['keycloakUserId'] = $keycloakUserId;
  }
}

/**
 * Implements hook_user_update().
 */
function klicktipp_user_update(&$edit, $account, $category) {
  $originalActive = (bool) $edit['original']->status;
  $updatedActive = (bool) ($edit['status'] ?? $originalActive);
  if (!$updatedActive && $originalActive) {
    UserHelper::deactivateAllLandingPages($account->uid);
  }

  if ($updatedActive !== $originalActive) {
    Subaccount::setStatusOfSubaccounts($account->uid, $updatedActive);
  }

  // assign attendee roles to user if they attended a seminar
  VarConsultant::UpdateAttendee($account->uid);
  VarConvention::UpdateAttendee($account->uid);

  // update subscriber fields in marketing account with customer data
  Libraries::include("marketing.inc");
  klicktipp_marketing_update_account($account);

  // save tier in user var
  if (isset($edit['tier'])) {
    VarTier::SetTier($account->uid, $edit['tier']);
    unset($edit['tier']);
    UserCache::remove($account->uid, UserCache::CACHE_OVERVIEW);
  }

  // save current amember product id in user var
  if (isset($edit['productID'])) {
    VarAmemberProductID::SetVariable($account->uid, $edit['productID']);
    unset($edit['productID']);
  }
}

/**
 * Implements hook_user_cancel().
 */
function klicktipp_user_cancel($edit, $account, $method) {

  // delete coreapi data

  $UserID = $account->uid;

  // delete all listbuildings
  Listbuildings::DeleteOfUser($UserID);
  MarketingTools::DeleteOfUser($UserID);

  Tag::DeleteTagsOfUser($UserID);
  Subscribers::RemoveSubscribersOfUser($UserID);
  Reference::DeleteOfUser($UserID);


  Emails::DeleteOfUser($UserID);
  Signatures::DeleteOfUser($UserID);
  DomainSet::DeleteWhitelabelDomains($UserID, '');

  // delete transaction data first, as all other functions are less efficient in doing so
  TransactionEmails::DeleteTransactionEmailsOfUser($UserID);
  Campaigns::DeleteOfUser($UserID);
  SplitTests::DeleteOfUser($UserID);

  Lists::DeleteOfUser($UserID);
  CustomFields::DeleteOfUser($UserID);

  // stats last
  Statistics::DeleteStatisticsOfUser($UserID);

  kt_delete_rows(array('UserID' => $UserID), '{fbl_reports}');

  //delete user inbound sms numbers
  kt_delete_rows(array('RelOwnerUserID' => $UserID), '{smsnumbers}');

  MetaLabels::DeleteOfUser($UserID);

  UserVariables::DeleteOfUser($UserID);

  Subaccount::DeleteOfUser($UserID);

}

/**
 * Implements hook_user_delete(). Hook is called before execution delete query
 */
function klicktipp_user_delete($account) {
    try {
        AccountManager::deleteKeycloakUser(ServiceContainer::getInstance()->get(KeycloakApi::class), $account);
    } catch (Exception $e) {
        // we don't want to block the user deletion process in drupal, if keycloak user could not be deleted
        watchdog(
            'keycloak',
            'could not delete keycloak user %email',
            ['%email' => $account->mail, '%exception' => $e->__toString()],
            WATCHDOG_ERROR
        );
    }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function klicktipp_form_user_profile_form_alter(&$form, &$form_state, $form_id) {
  /*
   * Support users have 'administer permissions' but no 'administer users' perm, so they can assign roles to users.
   * Deny to assign roles with 'administer users' permission to anyone (incl themselves).
   */
  if (!user_access('administer users') && user_access('administer permissions')) {
    foreach ($form['account']['roles']['#options'] as $rid => $name) {
      $permissions = user_role_permissions(array($rid => $name));
      if (!empty($permissions)) {
        // $permissions is array($rid => array('permission' => 1))
        $parray = array_keys(reset($permissions));
        if (in_array('administer users', $parray)) {
          // remove role if it has 'administer users' permission
          unset($form['account']['roles']['#options'][$rid]);
        }
      }
    }
  }
}

function _klicktipp_roles_by_group($uid, $group = 0) {

  // get roles of user
  $roles = db_query('SELECT rid FROM {users_roles} WHERE uid = :uid', [':uid' => $uid])
    ->fetchAllKeyed(0, 0);

  // get group related role id
  $groupRoles = UserGroups::retrieveGroupRelatedRoleIDs();
  $roles = array_diff_key($roles, $groupRoles);

  // ensure group role is assigned to customer
  $roleID = UserGroups::RetrieveUserGroup($group)['Data']['Role'] ?? 0;
  if ($roleID > 0) {
    $roles[$roleID] = $roleID;
  }

  return $roles;
}

/**
 * Implements hook_mail().
 *
 * NOTE: this applies only to emails sent by klicktipp module. There is another hook (see klicktipp_mail_alter)
 * applying to all emails
 *
 * @see klicktipp_mail_alter
 */
function klicktipp_mail($key, &$message, $params) {
  KlicktippMail::prepareMail($key, $message, $params);
}

/**
 * Implements hook_url_inbound_alter().
 */
function klicktipp_url_inbound_alter(&$path, $original_path, $path_language) {

  $unsubscribeAlias = variable_get('klicktipp_aliases_unsubscribe', 'abmelden');
  $spamAlias = variable_get('klicktipp_aliases_spam_report', 'spam');
  $unsubscriptionFeedbackAlias = variable_get('klicktipp_aliases_unsubscription_feedback', 'ifeedback');
  $changeEmailAlias = variable_get('klicktipp_aliases_change_email', 'change-email');
  $requestDataAlias = variable_get('klicktipp_aliases_request_data', 'my-data');
  $updateDataAlias = variable_get('klicktipp_aliases_update_data', 'my-data-update');
  $url_path = explode("/", $path);

  if (!empty($url_path[1])) {
    switch ($url_path[0]) {
      case $unsubscribeAlias:
      case $spamAlias:
      case $unsubscriptionFeedbackAlias:
        $ArrayParameters = Core::DecryptURL($url_path[1]);
        $EmailID = $ArrayParameters['EmailID'];
        if (!empty($EmailID)) {
          // find the user of the given email
          $UserID = db_query("SELECT RelUserID FROM {emails} WHERE EmailID = :EmailID", array(':EmailID' => $EmailID))->fetchField();
        }
        break;
      case $changeEmailAlias:
      case $requestDataAlias:
      case $updateDataAlias:
        $ArrayParameters = Core::DecryptURL($url_path[1]);
        $UserID = $ArrayParameters['UserID'];
        break;
    }
    if (!empty($UserID)) {
      $account = user_load($UserID);
      if ($account && !empty($account->UserPrivileges['DisplayEnglishLanguage'])) {
        // change display language to english
        $languages = language_list();
        // overwrite the global language object
        global $language;
        $language = $languages['en-gb'];
      }
    }
  }

}

/**
 * write changed email to amember
 * (implementation of module hook hook_email_confirm)
 */
function klicktipp_email_confirm($op, $uid, $old_mail, $new_mail) {
  if ($op == 'email confirmation') {
    // get amember data that we wont change
    $account = user_load($uid);
    Libraries::include('account.inc', '/forms');
    $member = _klicktipp_account_get_member($account->name);
    $fields_to_update = $member;
    unset($fields_to_update['data']); // unserialize it, if needed

    // new email
    $fields_to_update['email'] = $new_mail;
    // do it
    amember_rpc_update_user($member['member_id'], $fields_to_update);
  }
}

/**
 * Implements hook_prod_check_alter().
 * @param array reference to an associative array of all available checks
 */
function klicktipp_prod_check_alter(&$checks) {
  Libraries::include("prod_check.inc");

  _klicktipp_prod_check($checks);
}

/*
 * THEMING
 */

/**
 * This function together with klicktipp_element_info() replaces Drupal 6 klicktipp_element()
 */
function klicktipp_element_info_alter(&$type) {

  //set default validation function for every textfield: check for invalid characters: & < > " ' @see: check_plain()
  $type['textfield']['#element_validate'] = array('klicktipp_element_textfield_validate');

  //set default value_callback to allow value type array for texfields with property #magic_select
  //@see: klicktipp_form_type_textfield_value() and form_type_textfield_value() includes/form.inc
  $type['textfield']['#value_callback'] = 'klicktipp_form_type_textfield_value';

  //the default #theme_wrapper of #type submit is theme_button() which overwrites all change through #theme
  //-> remove the default #theme_wrapper, theme_button() will be called in theme_klicktipp_submit()
  //-> set theme_klicktipp_submit() as default #theme so buttons in other themes will be rendered
  $type['submit']['#theme_wrappers'] = array();
  $type['submit']['#theme'] = array('klicktipp_submit');

  //check every form if it contains a modal button and append the modal dialog form
  //the original #theme_wrapper theme_form() will be called in klicktipp_form_wrapper
  $type['form']['#theme_wrappers'] = array('klicktipp_form_wrapper');

  //migrate Drupal 6 markups and items to Drupal 7
  //both elements use #markup instead of #value
  //@see: klicktipp_pre_render_markup() and drupal_pre_render_markup()
  //Note: #type = 'markup' or 'item' must be set in the form element, otherwise the function is not called
  $type['markup']['#pre_render'] = array('klicktipp_pre_render_markup');
  $type['item']['#pre_render'] = array('klicktipp_pre_render_markup');

  //add old Drupal 6 element id as css class for JavaScripts @see: klicktipp_form_clean_id
  $type['textfield']['#pre_render'][] = 'klicktipp_form_clean_id';
  $type['password']['#pre_render'][] = 'klicktipp_form_clean_id';
  $type['select']['#pre_render'][] = 'klicktipp_form_clean_id';
  $type['radio']['#pre_render'][] = 'klicktipp_form_clean_id';
  $type['radios']['#pre_render'][] = 'klicktipp_form_clean_id';
  $type['checkbox']['#pre_render'][] = 'klicktipp_form_clean_id';
  $type['checkboxes']['#pre_render'][] = 'klicktipp_form_clean_id';
  $type['textarea']['#pre_render'][] = 'klicktipp_form_clean_id';
  $type['button']['#pre_render'][] = 'klicktipp_form_clean_id';
  $type['hidden']['#pre_render'][] = 'klicktipp_form_clean_id';

}

/**
 * This function migrates Drupal 6 markups and items to Drupal 7
 * both elements use #markup instead of #value
 * Note: #type = 'markup' or 'item' must be set in the form element, otherwise the function is not called
 */
function klicktipp_pre_render_markup($elements) {

  //Note: #markup is set, we can only check for empty()
  if (empty($elements['#markup']) && isset($elements['#value'])) {
    $elements['#markup'] = $elements['#value'];
    unset($elements['#value']);
  }

  return drupal_pre_render_markup($elements);

}

/**
 * Adds the old Drupal 6 element id as a css class for the JavaScripts @see: klicktipp_element_info_alter()
 * Also prepare the css class for the element wrapper @see: bootstrapklicktipp_form_element()
 * Note: original D6 core function: form_clean_id()
 */
function klicktipp_form_clean_id($element) {

  static $seen_ids = array();

  $id = str_replace(array('[', '_', ' '), '-', $element['#name']);
  $id = str_replace(']', '', $id);

  // Ensure IDs are unique. The first occurrence is held but left alone.
  // Subsequent occurrences get a number appended to them. This incrementing
  // will almost certainly break code that relies on explicit HTML IDs in
  // forms that appear more than once on the page, but the alternative is
  // outputting duplicate IDs, which would break JS code and XHTML
  // validity anyways. For now, it's an acceptable stopgap solution.
  if (isset($seen_ids[$id])) {
    $id = $id . '-' . $seen_ids[$id]++;
  }
  else {
    $seen_ids[$id] = 1;
  }

  $id_class = "edit-$id";

  _form_set_class($element, array($id_class));

  //class name for the element wrapper @see: bootstrapklicktipp_form_element()
  $element['#wrapper_class'] = "$id_class-wrapper";

  return $element;

}

/*
 * Custom value callback for textfields
 * Drupal does not allow for scalar values in textfields
 * the value type of magic selects is array()
 * if a textfield has the property '#magic_select' do not check for is_scalar
 * otherwise use Drupal's value callback
 * klicktipp_form_type_textfield_value() is set as the default value_callback for all textfields
 * @see: klicktipp_element_info_alter()
 */
function klicktipp_form_type_textfield_value($element, $input = FALSE) {

  if (empty($element['#magic_select'])) {
    return form_type_textfield_value($element, $input);
  }

  $valueCallback = fn($element) => str_replace(array("\r", "\n"), '', (string) $element);
  if (is_array($input)) {
    return array_map($valueCallback, $input);
  } if ($input !== FALSE && $input !== NULL) {
    return $valueCallback($input);
  }
}


/**
 * KlickTipps own element types
 */
function klicktipp_element_info() {

  $type = array();

  //basically #type => markup @see system_elements()
  $type['klicktipp_block'] = array(
    '#input' => FALSE,
    '#theme' => 'klicktipp_block',
    '#pre_render' => array('klicktipp_pre_render_markup'),
  );

  //basically #type => markup @see system_elements()
  $type['klicktipp_grid_row'] = array(
    '#input' => FALSE,
    '#theme' => 'klicktipp_grid_row',
    '#pre_render' => array('klicktipp_pre_render_markup'),
  );

  return $type;

}

/**
 * Default validation function for every textfield
 * check for invalid characters: & < > " ' @see: check_plain()
 * unset or overwrite with '#element_validate' => array() or '#element_validate' => array('validate_function', ...)
 */
function klicktipp_element_textfield_validate($element, &$form_state) {

  //for magic selects mit multiple and free value, $element['#value'] is an array
  $value = (is_array($element['#value'])) ? implode('', $element['#value']) : $element['#value'];

  //if check_plain() changed $value, $value contains the forbidden chars & < > " '
  if ($value != check_plain($value)) {
    form_set_error($element['#name'], t("The characters !chars are not allowed.", array(
      '!chars' => '<strong>' . check_plain("& < > \" '") . '</strong>',
    )));
  }

}

/**
 * Validate textfields where the user can enter & (e.g. email "From:")
 * allow & but disallow < > " '
 */
function klicktipp_element_textfield_validate_allow_ampersand($element, &$form_state) {

  //for magic selects mit multiple and free value, $element['#value'] is an array
  $value = (is_array($element['#value'])) ? implode('', $element['#value']) : $element['#value'];

  //if check_plain() changed (modified) $value, $value contains the forbidden chars < > " '
  $teststring = str_replace('&', '', $value);
  if ($teststring != check_plain($teststring)) {
    form_set_error($element['#name'], t("The characters !chars are not allowed.", array(
      '!chars' => '<strong>' . check_plain("< > \" '") . '</strong>',
    )));
  }

}

/**
 * Validate textfields where the user can enter URLs
 * allow & but disallow < > " '
 */
function klicktipp_element_textfield_validate_url($element, &$form_state) {

  if (!empty($element['#value']) && $element['#value'] != 'http://' && $element['#value'] != 'https://') {

    if (!empty($element['#value']) && filter_var($element['#value'], FILTER_VALIDATE_URL) === FALSE) {

      $name = empty($element['#parents']) ?
        $element['#name'] :
        // the line makes elements in fieldsets addressable
        implode('][', $element['#parents']);

      $urlParts = parse_url($element['#value']);
      // missing protocol
      if ($urlParts && !$urlParts['scheme']) {
        $message = t('validation:url:missing-scheme');
      } elseif (($sanitizedUrl = filter_var($element['#value'], FILTER_SANITIZE_URL)) != $element['#value']) {
        // not allowed chars in url
        $invalidChars = array_diff(mb_str_split($element['#value']), mb_str_split($sanitizedUrl));
        $invalidChars = array_map(fn($el) => $el == ' ' ? t('validation:url:space'): $el, $invalidChars);
        $message = t('validation:url:invalid-chars: @chars', ['@chars' => implode(' ', array_unique($invalidChars))]);
      } else {
        $message = t('validation:url:invalid @url', ['@url' => $element['#value']]);
      }

      form_set_error($name, $message);
    }

  }
}

/**
 * Check that entered (or selected) e-mail address is NOT blacklisted
 *
 * @param array $element
 * @param array $form_state
 */
function klicktipp_element_email_blacklist_validate($element, &$form_state) {

  $email = $element['#value'];

  $result = BlacklistHandler::isEmailBlacklisted($email);

  if (empty($result[0])) {
    return;
  }

  $info = '';
  if (!empty($result[3])) {
    $linkText = t('blacklist:provider-link-text @link', ['@link' => $result[3]]);
    $info = l($linkText, $result[3], [
      'attributes' => [
        'external' => TRUE,
        'target' => '_blank'
      ]
    ]);
  }
  $message = t('!fieldName: The email address !email contains a blacklisted domain and cannot be used. !info', array(
    '!fieldName' => $element['#title'],
    '!email' => $email,
    '!info' => $info
  ));

  form_set_error($element['#name'], $message);

}

/**
 * Implements hook_block_info().
 */
function klicktipp_block_info() {

  $blocks = array();

  //KlickTipp help block
  //show links to relevant manual pages for the current dialog or manual page
  $blocks['helpblock'] = array(
    'info' => t('KlickTipp Help Block'),
    'weight' => 0,
    'region' => '',
    'visibility' => 0, //Show on all pages except listed pages
    'pages' => 'handbuch', //show on all pages except the manual overview
    'cache' => DRUPAL_NO_CACHE,
  );

  //KlickTipp affiliate blocker cookie block
  //add a cookie for logged in user to prevent affiliation misuse
  $blocks['affiliate-blocker-cookie'] = array(
    'info' => 'KlickTipp Affiliate Blocker Cookie Block (authenticated user)',
    'weight' => 0,
    'cache' => DRUPAL_NO_CACHE,
  );

  //KlickTipp Feature Message block
  //show user a modal with info about a new feature
  $blocks['feature-message'] = array(
    'info' => 'KlickTipp Feature Message Block (authenticated user)',
    'weight' => 0,
    'cache' => DRUPAL_NO_CACHE,
  );

  //KlickTipp Beamer block
  //use this block to control on what pages Beamer should be displayed
  $blocks['beamer'] = array(
    'info' => 'KlickTipp Beamer Block',
    'weight' => 0,
    'cache' => DRUPAL_NO_CACHE,
  );

  //KlickTipp Product Fruits block
  //use this block to control on what pages Product Fruits should be displayed
  $blocks['product-fruits'] = array(
    'info' => 'KlickTipp Product Fruits Block',
    'weight' => 0,
    'cache' => DRUPAL_NO_CACHE,
  );

  return $blocks;
}

/**
 * Implements hook_block_view().
 */
function klicktipp_block_view($delta) {

  if ($delta == 'helpblock') {
    //get content for KlickTipp help block

    Libraries::include('help_block.inc');

    $block = array(
      'subject' => '',
      'content' => klicktipp_get_help(),
    );

    return $block;

  }
  elseif ($delta == 'affiliate-blocker-cookie') {

    Libraries::include('api_content_includes.inc', '/api');
    ciapi_digistore_set_affiliate_blocker_cookie();

    //just have some invisible content to output
    $block = array(
      'subject' => '',
      'content' => '<span></span>',
    );

    return $block;

  } elseif ($delta == 'feature-message') {

    global $user;
    $account = user_load($user->uid);

    // check if block should be displayed for current user
    if (VarFeatureMessages::CanDisplayFeatureMessage($account)) {
      $block = array(
        'subject' => NULL,
        // retrieve complete modal html from user variable class
        'content' => VarFeatureMessages::GetModal($account),
      );
    } else {
      $block = array(
        'subject' => NULL,
        // output nothing as block
        'content' => '',
      );
    }

    return $block;

  }
  elseif ($delta == 'beamer') {

    global $user;
    $account = user_load($user->uid);

    $beamer = new Beamer($account);

    // check if block should be displayed for current user
    if ($beamer->isActivated()) {

      $displayIcon = '<script type="text/javascript">$(document).ready(() => {$(".display-beamer").show();});</script>';

      $block = array(
        'subject' => NULL,
        // retrieve complete modal html from user variable class
        'content' => $beamer->getEmbedCode() . $displayIcon,
      );
    } else {
      $block = array(
        'subject' => NULL,
        // output nothing as block
        'content' => '',
      );
    }

    return $block;

  } elseif ($delta == 'product-fruits') {
    global $user;
    $account = user_load($user->uid);

    $productFruitsWidget = new ProductFruitsWidget($account);

    // check if block should be displayed for current user
    if ($productFruitsWidget->isActive()) {
      $block = array(
        'subject' => NULL,
        // retrieve complete modal html from user variable class
        'content' => $productFruitsWidget->getHeaderWidget(),
      );
    } else {
      $block = array(
        'subject' => NULL,
        // output nothing as block
        'content' => '',
      );
    }

    return $block;
  }

}

/**
 * Implements hook_custom_theme
 */
function klicktipp_custom_theme() {
  if (current_path() == 'admin/config/klicktipp/custstats') {
    // to get "datepicker"
    // "system_custom_theme" comes later, so we need to inject
    global $conf;
    $conf['admin_theme'] = 'bootstrapklicktipp';
  }
}


/**
 * implementation of hook_theme
 */
function klicktipp_theme($existing, $type, $theme, $path) {
  return array(
    // whitelabel pages
    'klicktipp_whitelabel' => array(
      'variables' => array('message' => NULL, 'content' => NULL),
      'template' => 'klicktipp-whitelabel',
    ),
    'klicktipp_optin_pending' => array(
      'variables' => array(
        'UserID' => NULL,
        'ListID' => NULL,
        'SubscriberID' => NULL
      ),
      'template' => 'klicktipp-optin-pending',
    ),
    'klicktipp_optin_thankyou' => array(
      'variables' => array(
        'UserID' => NULL,
        'ListID' => NULL,
        'SubscriberID' => NULL
      ),
      'template' => 'klicktipp-optin-thankyou',
    ),
    //Klick-Tipp data processing order pdf template
    'klicktipp_data_processing_order' => array(
      'variables' => array('account' => NULL),
      'template' => 'klicktipp-data-processing-order',
    ),
    //campaign conversion statistics pdf
    'klicktipp_campaign_statistics_pdf' => array(
      'variables' => array('account' => NULL),
      'template' => 'klicktipp-campaign-statistics-pdf',
    ),
    /*
     * forms
     */
    // config whitelabel domain form
    'klicktipp_account_whitelabel_domain_form' => array(
      'render element' => 'form',
      'file' => 'forms/account_whitelabel.inc',
    ),
    // customfields
    'subscription_fields' => array(
      'render element' => 'form',
      'file' => 'forms/customfields.inc',
    ),
    // outbound fields
    'klicktipp_outbound_fields' => array(
      'render element' => 'form',
      'file' => 'forms/tools_outbound.inc',
    ),
    // signatures
    'klicktipp_signatures_overview_form' => array(
      'render element' => 'form',
      'file' => 'forms/signatures.inc',
    ),
    // unsubscriptions
    'unsubscriptions_dialogs' => array(
      'render element' => 'form',
      'file' => 'forms/unsubscriptions.inc',
    ),
    'unsubscriptions_urls' => array(
      'render element' => 'form',
      'file' => 'forms/unsubscriptions.inc',
    ),
    'klicktipp_plugin_variables' => array(
      'render element' => 'form',
      'file' => 'forms/admin_settings_plugin.inc',
    ),
    'klicktipp_link' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    /*
     * buttons
     */
    // link buttons
    'klicktipp_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_create_buttons' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_email_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_cancel_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_delete_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_arrow_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_download_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_pause_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_resume_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_edit_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_back_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_wizard_back_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_preview_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_expand_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_fullscreen_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_previous_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_contents_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_search_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_next_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_add_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_duplicate_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_edit_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_download_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_merge_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_reject_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_change_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_delete_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_preview_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_settings_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_email_table_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_modal_link' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_modal_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_edit_modal_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_delete_modal_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_delete_modal_ajax_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_unsubscribe_modal_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_reset_modal_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_tag_modal_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_check_modal_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_submit_modal_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_modal_cancel_button' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    // submit buttons
    'klicktipp_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_delete_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_merge_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_merge_dialog' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_edit_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_reject_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_email_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_reload_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_reset_confirm_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_download_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_unsubscribe_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_export_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_import_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_merge_dialog_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_search_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_tag_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_check_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_preview_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_upload_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_add_secondary_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_cancel_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_wizard_back_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_login_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_duplicate_submit' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_magic_select' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_select_selectable_optgroup' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    // autoresponders planetarium
    'klicktipp_autoresponders_planetarium' => array(
      'variables' => array('data' => NULL),
      'template' => 'klicktipp-autoresponders-planetarium',
    ),
    'klicktipp_compact_string' => array(
      'variables' => array(
        'string' => NULL,
        'max_length' => NULL,
        'max_length_mobile' => NULL
      ),
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_table_pager' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_quickhelp' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_grid_row' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_block' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_item' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_info' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_help_block' => array(
      'variables' => array('Links' => NULL, 'ContactFormLink' => NULL),
      'file' => 'includes/help_block.inc',
    ),
    'klicktipp_form_wrapper' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_icon' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_plugin_icon' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_icon_grid' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_colorpicker' => array(
      'render element' => 'element',
      'file' => 'includes/theme.inc',
    ),
    'klicktipp_safe_redirect' => array(
      'variables' => array('original_uri' => NULL),
      'template' => 'klicktipp-safe-redirect',
      'path' => drupal_get_path('theme', 'bootstrapklicktipp'),
    ),
  );
}

/*
 * preprocess klicktipp whitelabel page
 *
 * this is derived from theme.inc/template_preprocess_page
 * reduced to what is usable in a "whitelabel" page
 */
function template_preprocess_klicktipp_whitelabel(&$variables) {
  $variables['head_title'] = drupal_get_title() ? strip_tags(drupal_get_title()) : '';
  $variables['base_path'] = base_path();
  $variables['front_page'] = url();
  //$variables['head']              = drupal_get_html_head();
  $variables['language'] = $GLOBALS['language'];
  $variables['language']->dir = $GLOBALS['language']->direction ? 'rtl' : 'ltr';
  // TODO Please change this theme call to use an associative array for the $variables parameter.
  $variables['messages'] = theme('status_messages');
  $variables['title'] = drupal_get_title();

  //include the external bootstrap styles before every drupal_add_css() styles

  //Bootstrap CSS via CDN
  $bootstrap_cdn_css = variable_get(KLICKTIPP_THEME_BOOTSTRAP_CDN_CSS, 'https://netdna.bootstrapcdn.com/bootstrap/3.0.3/css/bootstrap.min.css');
  $variables['styles'] = '<link rel="stylesheet" type="text/css" href="' . $bootstrap_cdn_css . '" />';

  //add all styles by drupal_add_css
  $variables['styles'] .= drupal_get_css();

  //Bootstrap JavaScript via CDN
  $bootstrap_cdn_js = variable_get(KLICKTIPP_THEME_BOOTSTRAP_CDN_JAVASCRIPT, 'https://netdna.bootstrapcdn.com/bootstrap/3.0.3/js/bootstrap.min.js');
  $variables['scripts'] = '<script type="text/javascript" src="' . $bootstrap_cdn_js . '"></script>';

  //add all scripts by drupal_add_js
  $variables['scripts'] = drupal_get_js() . $variables['scripts'];

  //add a body class to identify the language used to change styles that depend on the language (example: images with text)
  //example:
  //button.order-now {background-image: url(order_button_sprite.png);} //default (language: de)
  //body.lang-pt-br button.order-now {background-image: url(order_button_sprite_br.png);} //language: pt-br
  $variables['body_class'] = "lang-" . strtolower($variables['language']->language);

  //Google scripts
  $variables['GoogleScripts'] = Settings::get(KLICKTIPP_THEME_GOOGLE_SCRIPTS);

  // the logo MUST point to the main domain (instead of shared klick-host), so the page won't be seen as a phishing website.
  $variables['brand'] = '<a class="navbar-brand" href="'.APP_URL.'" rel="nofollow"><img src="' . theme_get_setting('logo') . '" height="30" /></a>';
  $variables['navbar_style'] = 'navbar-inverse';

  if (!empty($variables['uid'])) {
    $account = user_load($variables['uid']);
    if (!empty($account->UserSettings['CustomerLogo'])) {
      //display white navigation bar with whitelabel logo
      //make sure to not display a menu
      $variables['navbar_style'] = 'navbar-custom';
      $variables['MainMenu'] = '';
      if (empty($account->UserSettings['CustomerLogoLink'])) {
        $variables['brand'] = '<div class="navbar-brand"><img src="' . $account->UserSettings['CustomerLogo'] . '" height="30" /></div>';
      }
      else {
        $variables['brand'] = '<a class="navbar-brand" href="' . $account->UserSettings['CustomerLogoLink'] .'" rel="nofollow"><img src="' . $account->UserSettings['CustomerLogo'] . '" height="30" /></a>';
      }
    }
  }

  $variables['FooterMenu'] = klicktipp_menu_whitelabel_footer();

}

function template_preprocess_klicktipp_optin_pending(&$variables) {

  $UserID = $variables['UserID'];
  $ListID = $variables['ListID'];
  $SubscriberID = $variables['SubscriberID'];
  $ReferenceID = $variables['ReferenceID'];
  $account = user_load($UserID);

  //retrieve the DOI process, return default process if ListID doesn't exist
  $ArraySubscriberList = Lists::RetrieveListByIDOrDefault($UserID, $ListID);

  //retrieve subscriber
  $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);

  if (empty($ArraySubscriberList)) {
    // unexpected error: default DOI process not found

    $vars = array(
      '!function' => __FUNCTION__,
      '!file' => __FILE__,
      '!message' => 'Subscription pending page: SubscriberList not found.',
      '!SubscriberID' => (empty($SubscriberID)) ? '[empty]' : $SubscriberID,
      '!UserID' => (empty($UserID)) ? '[empty]' : $UserID,
      '!ListID' => (empty($ListID)) ? '[empty]' : $ListID,
    );

    watchdog("kt-subscription", "!function in !file: !message (UserID: !UserID, SubscriberID: !SubscriberID, ListID: !ListID)", $vars, WATCHDOG_ERROR);

  }

  if (empty($ArraySubscriberList) || empty($FullSubscriber)) {
    //display a white label message: 'Sorry, this link is no longer valid.'
    klicktipp_invalid_link($UserID);
    return;
  }

  //get thesubscribers email provider to provide a link to his inbox
  $SubscriberEmailAddress = $FullSubscriber['EmailAddress'];
  $EmailDomain = explode('@', $SubscriberEmailAddress);
  $EmailDomain = explode('.', $EmailDomain[1]);
  $EmailDomain = $EmailDomain[0];

  $provider_data = variable_get(KLICKTIPP_SETTINGS_OPTIN_CONFIRMATION_EMAIL_PROVIDER, array());
  $variables['ProviderLink'] = t("inbox");
  $variables['ProviderImageLinkAttributes'] = "";
  if (!empty($provider_data[$EmailDomain])) {
    $variables['ProviderLink'] = l($provider_data[$EmailDomain]['link_title'], $provider_data[$EmailDomain]['link_url'], array(
      'external' => TRUE,
      'attributes' => array('target' => '_blank', 'rel' => 'nofollow'),
    ));
    $variables['ProviderImageLinkAttributes'] = 'href="' . url($provider_data[$EmailDomain]['link_url'], array(
        'external' => TRUE,
        'attributes' => array('target' => '_blank', 'rel' => 'nofollow'),
      )) . '" target="_blank"';
  }

  $variables['SenderName'] = $account->FirstName . ' ' . $account->LastName;

  template_preprocess_klicktipp_whitelabel($variables);
}

function template_preprocess_klicktipp_optin_thankyou(&$variables) {

  $UserID = $variables['UserID'];
  $ListID = $variables['ListID'];
  $SubscriberID = $variables['SubscriberID'];
  $account = user_load($UserID);

  //retrieve the DIO process, return default process if ListID doesn't exist
  $ArraySubscriberList = Lists::RetrieveListByIDOrDefault($UserID, $ListID);

  if (empty($ArraySubscriberList)) {
    // unexpected error: default DOI process not found

    $vars = array(
      '!function' => __FUNCTION__,
      '!file' => __FILE__,
      '!message' => 'Subscription thankyou page: SubscriberList not found.',
      '!SubscriberID' => (empty($SubscriberID)) ? '[empty]' : $SubscriberID,
      '!UserID' => (empty($UserID)) ? '[empty]' : $UserID,
      '!ListID' => (empty($ListID)) ? '[empty]' : $ListID,
    );
    watchdog("kt-subscription", "!function in !file: !message (UserID: !UserID, SubscriberID: !SubscriberID, ListID: !ListID)", $vars, WATCHDOG_ERROR);

    //display a white label message: 'Sorry, this link is no longer valid.'
    klicktipp_invalid_link($UserID);
    return;
  }

  //retrieve the confirmation email to provide the used sender email address for the pending/whitelisting page
  $ConfirmationEmail = Emails::RetrieveEmailByID($UserID, $ArraySubscriberList['RelOptInConfirmationEmailID']);
  $EmailAddress = empty($ConfirmationEmail['FromEmail']) ? $account->mail : $ConfirmationEmail['FromEmail'];

  $variables['FirstName'] = $account->FirstName;
  $variables['LastName'] = $account->LastName;
  $variables['Street'] = $account->Street;
  $variables['City'] = $account->City;
  $variables['Zip'] = $account->Zip;
  $variables['Country'] = $account->Country;
  $variables['SenderEmail'] = $EmailAddress;

  $vcard_param = array(
    'uid' => $UserID,
    'email' => $EmailAddress,
    'hash' => MinihashCreate($UserID, $EmailAddress),
  );

  $vcard_param_encoded = trim(base64_encode(json_encode($vcard_param)), '=');

  //get sender domain of confirmation email
  $SenderDomain = DomainSet::SelectValidSenderDomainById($account->uid, $ArraySubscriberList['RelOptInConfirmationEmailID']);

  $variables['vCardURL'] = $SenderDomain['appurl'] . "vcard/$vcard_param_encoded";

  if (!empty($variables['simpletest'])) {
    //@see coreapi_lists.test
    //we have to stop here since template_preprocess_klicktipp_whitelabel does not return
    return;
  }

  template_preprocess_klicktipp_whitelabel($variables);
}

/*
 * preprocess klicktipp autoresponders planetarium
 */
function template_preprocess_klicktipp_autoresponders_planetarium(&$variables) {

  $variables['head_title'] = drupal_get_title() ? strip_tags(drupal_get_title()) : '';
  $variables['base_path'] = base_path();
  $variables['front_page'] = url();
  //$variables['head']              = drupal_get_js('header'). drupal_get_css() .drupal_get_html_head();
  $variables['language'] = $GLOBALS['language'];
  $variables['language']->dir = $GLOBALS['language']->direction ? 'rtl' : 'ltr';
  // TODO Please change this theme call to use an associative array for the $variables parameter.
  $variables['messages'] = theme('status_messages');
  $variables['title'] = drupal_get_title();
  //$variables['closure']             = drupal_get_js('footer');

  //include the external bootstrap styles before every drupal_add_css() styles

  //Bootstrap CSS via CDN
  $bootstrap_cdn_css = variable_get(KLICKTIPP_THEME_BOOTSTRAP_CDN_CSS, 'https://netdna.bootstrapcdn.com/bootstrap/3.0.3/css/bootstrap.min.css');
  $variables['styles'] = '<link rel="stylesheet" type="text/css" href="' . $bootstrap_cdn_css . '" />';

  //add all styles by drupal_add_css
  $variables['styles'] .= drupal_get_css();

  //Bootstrap JavaScript via CDN
  $bootstrap_cdn_js = variable_get(KLICKTIPP_THEME_BOOTSTRAP_CDN_JAVASCRIPT, 'https://netdna.bootstrapcdn.com/bootstrap/3.0.3/js/bootstrap.min.js');
  $variables['scripts'] = '<script type="text/javascript" src="' . $bootstrap_cdn_js . '"></script>';

  drupal_add_js("misc/d3.v2.min.js", array(
    'type' => 'file',
    'weight' => JS_THEME,
    'preprocess' => FALSE
  ));

  //add all scripts by drupal_add_js
  $variables['scripts'] = drupal_get_js() . $variables['scripts'];

}

/**
 * Preprocess the Klick-Tipp data processing order template for the PDF
 */
function template_preprocess_klicktipp_data_processing_order(&$variables) {

  Libraries::include('countries.inc');
  $countries = klicktipp_account_get_countries_inner();

  $account = $variables['account'];

  $variables['CompanyName'] = $account->CompanyName;
  $variables['FirstName'] = $account->FirstName;
  $variables['LastName'] = $account->LastName;
  $variables['Street'] = $account->Street;
  $variables['Zip'] = $account->Zip;
  $variables['City'] = $account->City;
  $variables['Country'] = t(/*ignore*/$countries[$account->Country]);
  $variables['UserName'] = $account->name;
  $variables['OrderID'] = "ADV-" . Core::CryptNumber($account->amemberid) . "-" . $account->amemberid;

}

/**
 * set the page title and breadcrumb for book pages
 */
function klicktipp_preprocess_book_navigation(&$variables) {

  $active_trail = menu_get_active_trail($variables['book_link']);
  unset($active_trail[0]); //front page is set by klicktipp_set_breadcrumb
  $current_trail = array_pop($active_trail); //the last one is the current page and not a link
  $breadcrumb = array();
  foreach ($active_trail as $trail) {
    $breadcrumb[] = l($trail['title'], $trail['href']);
  }

  // show manual feedback form only if user is logged in
  $variables['showFeedbackForm'] = !empty($variables['user']->uid);

  if ( drupal_get_title() !== '' ) {
    //the title has been set to '' via ciapi_styleguide_set_title() -> do not overwrite
    klicktipp_set_title($current_trail['title']);
  }

  klicktipp_set_breadcrumb($breadcrumb, $current_trail['title']);

}

/**
 * Theme the maintenance page as a whitelabel page
 * @see: maintenance-page.tpl.php
 */
function klicktipp_preprocess_maintenance_page(&$variables) {
  template_preprocess_klicktipp_whitelabel($variables);
}

/**
 * Output optin content (confirmation/thankyou) via a template file
 * params are encoded for protection
 * param_encoded contains uid => UserID, sid => SubScriberID and hash => MiniHash(), encoded: trim(base64_encode(json_encoded()), '=')
 */
function _klicktipp_get_optin_template($template, $param_encoded) {
  // try to decode "old" encoding (as of 2014-12 we are not sure, where these come from ...)
  $param_decoded = json_decode(base64_decode($param_encoded), TRUE);

  if (empty($param_decoded['hash']) || !MinihashCheck($param_decoded['hash'], $param_decoded['lid'], $param_decoded['sid'])) {

    // new encoding
    $param_decoded = Core::DecryptURL($param_encoded);
    if (empty($param_decoded['uid'])) {

      // unexpected error: invalid params
      $vars = array(
        '!function' => __FUNCTION__,
        '!file' => __FILE__,
        '!template' => $template,
        '!message' => 'Optin confirmation/thankyou: Invalid parameters for ',
        '!param_hash' => (empty($param_encoded)) ? '[empty]' : $param_encoded,
        '!param_decoded' => (empty($param_decoded)) ? '[empty]' : $param_decoded,
      );
      watchdog("kt-subscription", "!function in !file: !message !template (ParamHash: !param_hash, ParamDecoded: !param_decoded)", $vars, WATCHDOG_ERROR);
      klicktipp_display_whitelabel_message(t('Error Occurred'), t('Please return back and try again. If problem continues, contact service provider.'), t('Subscription Error'));
      exit;
    }
  }

  if (!empty($param_decoded['sid'])) {
    // set subscriber cookie
    Statistics::SetSubscriberCookie($param_decoded['uid'], $param_decoded['sid']);
  }

  print theme($template, array(
    'UserID' => $param_decoded['uid'],
    'ListID' => $param_decoded['lid'],
    'SubscriberID' => $param_decoded['sid'],
    'ReferenceID' => empty($param_decoded['rid']) ? 0 : $param_decoded['rid'],
  ));

  exit;
}


/**
 * Format a form.
 *
 * @param $element
 *   An associative array containing the properties of the element.
 *   Properties used: action, method, attributes, children
 * @return
 *   A themed HTML string representing the form.
 *
 * @ingroup themeable
 */
function phptemplate_form($element) {
  //TODO Drupal7: remove this function @see theme_klicktipp_form_wrapper()

  //add the rendered confirm form(s) under the edit form in none bootstrap themes (if given)
  // in Bootstrap-Theme te confirm form is hidden as a modal dialog
  $modal_confirm = _klicktipp_get_modal_confirm($element);

  return theme_form($element) . $modal_confirm;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_set_breadcrumb($trail = array(), $title = '') {
  // insert trail into
  $breadcrumb = $trail;
  array_unshift($breadcrumb, l(t('Home'), '<front>'));
  if (empty($title)) {
    $title = drupal_get_title();
  }
  $breadcrumb[] = check_plain($title);

  // Set Breadcrumbs
  drupal_set_breadcrumb($breadcrumb);

}


/**
 * set page title
 * historical function that had more logic, for now it's just a wrapper for drupal_set_title() but plain checks the title
 */
function klicktipp_set_title($title = NULL) {

  if (isset($title)) {
    drupal_set_title($title);
  }

}

/**
 * Redirect a form after submit
 *
 * Drupal will not redirect when $form_state['rebuild'] is set to TRUE
 * When we call this function, we definitely want to redirect => clear rebuild
 * @param: $redirect can be a string (path) or an array (path, array_options )
 * @see drupal_redirect_form() -> drupal_goto() -> url()
 */
function klicktipp_set_redirect(&$form_state, $redirect) {

  unset($form_state['rebuild']);

  $form_state['redirect'] = $redirect;

}

/*
 * HELPERS
 */

/**
 * set language for translation (t-function) by account
 * NOTE: use only in system processes, where no customer is logged in
 * OR re-init with klicktipp_set_language() afterwards
 * NOTE: if you just need one string to be translated, use
 * t ($string, $args, $account->language);
 */
function klicktipp_set_language($account = array()) {
  // reset to system settings
  if (empty($account)) {
    drupal_language_initialize();
    return;
  }

  global $language;

  // Get a list of enabled languages.
  $languages = language_list('enabled');
  $languages = $languages[1];

  if (!is_array($account)) {
    $account = (array) $account;
  }

  if ($account['uid'] && isset($languages[$account['language']])) {
    // User language.
    $language = $languages[$account['language']];
  }
  else {
    // Fall back on the default if everything else fails.
    $language = language_default();
  }
}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_set_timezone() {
  return Dates::setTimezone();
}

/**
 * user date in klicktipp format (using user timezone)
 */
function klicktipp_date($format, $time) {
  return Dates::formatDate((string) $format, (int) $time);
}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_display_whitelabel_message($message_title, $message, $page_title = '', $UserID = 0) {

  klicktipp_set_title(empty($page_title) ? $message_title : $page_title);
  print theme('klicktipp_whitelabel', array(
    'message' => check_plain($message_title),
    'content' => $message,
    'uid' => $UserID,
  ));
  exit; // just display the message and leave script
}

/**
 * klicktipp t function deprecated and not used anymore
 */
function kt($LanguageGroup, $LanguageCode, $SubLanguageCode = '', $ArrayReplaceList = array(), $untranslated = FALSE) {
  static $ArrayLanguageStrings;

  if (!isset($ArrayLanguageStrings)) {
    // always load english texts from coreapi -> translation is made by drupal
    $ArrayLanguageStrings = array();
    include DRUPAL_ROOT . '/data/en.inc.php';
  }

  $LanguageString = $ArrayLanguageStrings[$LanguageGroup][$LanguageCode];
  if (!empty($SubLanguageCode)) {
    $LanguageString = $LanguageString[$SubLanguageCode];
  }
  if ($untranslated) {
    return $LanguageString;
  }
  else {
    return t(/*ignore*/$LanguageString, $ArrayReplaceList);
  }
}

/////////////////////////////////////////////////////////////////// Application

function _klicktipp_get_amemberid($name, $account = '') {
  return Amember::getAmemberId($name, $account);
}


function _klicktipp_is_system_email($email) {

  if (empty($email)) {
    //it's actually an error, but an empty email is still not a system email
    return FALSE;
  }

  //reserved system emails are enter without @DOMAIN in the settings, so strip it
  if (strpos($email, '@') !== FALSE) {
    $email_short = explode('@', $email);
    $email = $email_short[0];
  }

  //check if address is the alias for unsubscribe.php:
  if (Subscribers::IsSameEmailAddress($email, variable_get('klicktipp_aliases_unsubscribe', 'abmelden'))) {
    return TRUE;
  }

  //get all reseved system emails and compare them with $email
  $reserved_addresses = array_map('trim', explode("\n", variable_get('klicktipp_reserved_system_email_addresses', '')));
  if (in_array($email, $reserved_addresses)) {
    //email is a reserved system email
    return TRUE;
  }

  //$email is not a reserved system email
  return FALSE;

}

/**
 * get an include of the klicktipp module
 */
function klicktipp_include($file, $subdir = '/includes') {
  Libraries::include($file, $subdir);
}

/**
 * Include external libraries located in sites/all/libraries
 * Libraries:
 * - DOMPDF: create PDF files from HTML
 * @param string $library
 */
function klicktipp_library($library) {

  switch ($library) {
    case KLICKTIPP_LIBRARY_DOMPDF:
      Libraries::loadDOMPDF();
      return true;
    case KLICKTIPP_LIBRARY_S3:
      Libraries::loadS3();
      return true;
    default:
      return false;
  }
}

/**
 * Implements hook_services_resources().
 */
function klicktipp_services_resources() {
  Libraries::include("api_resource.inc", '/api');

  $resources = array();

  // public (customer) api
  $resources += _klicktippapi_resource_definition();
  $resources += CustomFieldsPublicAPI::klicktippapi_resource_definition();
  $resources += ToolWebsiteExitLightboxPublicAPI::klicktippapi_resource_definition();
  $resources += ToolWebsiteFeedbackPublicAPI::klicktippapi_resource_definition();
  $resources += ToolWebsiteOneTimeOfferPublicAPI::klicktippapi_resource_definition();
  $resources += ToolWebsiteSocialproofPublicAPI::klicktippapi_resource_definition();
  $resources += ToolWebsiteSplittestPublicAPI::klicktippapi_resource_definition();
  $resources += ToolPluginGeneralPublicAPI::klicktippapi_resource_definition();
//  $resources += TagPublicAPI::klicktippapi_resource_definition(); //TODO implement and replace api_tags.inc

  // angular api (kt-*)
  $resources += CampaignsProcessFlow::klicktippapi_resource_definition();
  $resources += CampaignsNewsletter::klicktippapi_resource_definition();
  $resources += CampaignsNewsletterAutoresponder::klicktippapi_resource_definition();
  $resources += CustomFields::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeSingle::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeParagraph::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeCheckbox::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeDropdown::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeEmail::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeNumber::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeURL::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeDate::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeTime::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeDatetime::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeHtml::klicktippapi_resource_definition();
  $resources += CustomFieldsTypeDecimal::klicktippapi_resource_definition();
  $resources += Emails::klicktippapi_resource_definition();
  $resources += EmailsAutomationEmail::klicktippapi_resource_definition();
  $resources += EmailsAutomationSMS::klicktippapi_resource_definition();
  $resources += EmailsNotificationEmail::klicktippapi_resource_definition();
  $resources += EmailsNotificationSMS::klicktippapi_resource_definition();
  $resources += Listbuildings::klicktippapi_resource_definition();
  $resources += MarketingTools::klicktippapi_resource_definition();
  $resources += SplitTests::klicktippapi_resource_definition();
  $resources += Tag::klicktippapi_resource_definition();
  $resources += TagCategorySmartLink::klicktippapi_resource_definition();
  $resources += TagCategoryCampaignSent::klicktippapi_resource_definition();
  $resources += TagCategoryCampaignOpened::klicktippapi_resource_definition();
  $resources += TagCategoryCampaignClicked::klicktippapi_resource_definition();
  $resources += TagCategoryCampaignViewed::klicktippapi_resource_definition();
  $resources += TagCategoryCampaignConverted::klicktippapi_resource_definition();
  $resources += TagCategoryAutonmationStarted::klicktippapi_resource_definition();
  $resources += TagCategoryAutonmationFinished::klicktippapi_resource_definition();
  $resources += TagCategoryOutboundKajabiActivated::klicktippapi_resource_definition();
  $resources += TagCategoryOutboundKajabiDeactivated::klicktippapi_resource_definition();
  $resources += TagCategoryOutboundActivated::klicktippapi_resource_definition();
  $resources += TagCategoryTaggingPixel::klicktippapi_resource_definition();
  $resources += TagCategoryEmailSent::klicktippapi_resource_definition();
  $resources += TagCategoryEmailOpened::klicktippapi_resource_definition();
  $resources += TagCategoryEmailClicked::klicktippapi_resource_definition();
  $resources += TagCategoryEmailViewed::klicktippapi_resource_definition();
  $resources += TagCategorySMSSent::klicktippapi_resource_definition();
  $resources += TagCategorySMSClicked::klicktippapi_resource_definition();
  $resources += TagCategoryAPIKey::klicktippapi_resource_definition();
  $resources += TagCategoryRequest::klicktippapi_resource_definition();
  $resources += TagCategorySMSListbuilding::klicktippapi_resource_definition();
  $resources += TagCategoryForms::klicktippapi_resource_definition();
  $resources += TagCategoryPayment::klicktippapi_resource_definition();
  $resources += TagCategoryRefunded::klicktippapi_resource_definition();
  $resources += TagCategoryChargedback::klicktippapi_resource_definition();
  $resources += TagCategorySubsequentPayment::klicktippapi_resource_definition();
  $resources += TagCategoryDeferredPayment::klicktippapi_resource_definition();
  $resources += TagCategoryRebillCanceled::klicktippapi_resource_definition();
  $resources += TagCategoryRebillResumed::klicktippapi_resource_definition();
  $resources += TagCategoryPaymentCompleted::klicktippapi_resource_definition();
  $resources += TagCategoryPaymentExpired::klicktippapi_resource_definition();
  $resources += TagCategoryDigistoreAffiliation::klicktippapi_resource_definition();
  $resources += TagCategoryFacebookAudience::klicktippapi_resource_definition();
  $resources += TagCategoryPluginInboundReady::klicktippapi_resource_definition();
  $resources += TagCategoryPluginInboundStarted::klicktippapi_resource_definition();
  $resources += TagCategoryPluginInboundInProgress::klicktippapi_resource_definition();
  $resources += TagCategoryPluginInboundFinished::klicktippapi_resource_definition();
  $resources += TemplatesEmail::klicktippapi_resource_definition();
  $resources += TemplatesEmailGlobal::klicktippapi_resource_definition();
  $resources += ToolOutboundGeneral::klicktippapi_resource_definition();
  $resources += ToolCalendar::klicktippapi_resource_definition();
  $resources += ToolStatistics::klicktippapi_resource_definition();
  $resources += UserVariables::klicktippapi_resource_definition();
  $resources += Signatures::klicktippapi_resource_definition();
  $resources += MetaLabels::klicktippapi_resource_definition();
  $resources += Lists::klicktippapi_resource_definition();

  return $resources;
}

/**
 * Implements hook_rest_server_response_formatters_alter().
 * - adds text/html response formatter
 */
function klicktipp_rest_server_response_formatters_alter(&$formatters) {

  $formatters['html'] = array(
    'mime types' => array('text/html'),
    'formatter class' => KlicktippServicesFormatterHTML::class,
  );
}

/**
 * Return the language-country code for the current language if supported, otherwise 'en-US'
 * @param bool $underscore : TRUE => format 'de_DE', FALSE => 'de-DE'
 */
function klicktipp_get_language_country_code($underscore = FALSE) {

  global $language;

  $supported_langs = array(
    'en' => 'en-US',
    'de' => 'de-DE',
    'pt-br' => 'pt-BR',
    'fr' => 'fr-FR',
    'ja' => 'ja-JP',
  );

  if ($underscore) {
    return ($supported_langs[$language->language]) ? str_replace('-', '_', $supported_langs[$language->language]) : "en_US";
  }

  return ($supported_langs[$language->language]) ? $supported_langs[$language->language] : "en-US";

}

/**
 * Return the full country name from the country code
 * @param string country code 'DE', 'en'
 */
function klicktipp_get_country_name_from_country_code($CountryCode) {

  //to get the country name from the country code we use drupal translation
  //by translating "iso3166_<COUNTRYCODE>" into the full country name

  $TranslateString = 'iso3166_' . strtoupper($CountryCode);
  $CountryName = t(/*ignore*/$TranslateString);

  if ($CountryName == $TranslateString) {
    return strtoupper($CountryCode);
  } //translation not found, return country code

  return $CountryName;

}

/**
 * klicktipp user specific application cache management
 * caches data with key "klicktipp:<uid>:<applicationkey>" until page cache is emptied
 */

/**
 * klicktipp cache get
 */
function klicktipp_user_cache_get($uid, $location) {
  return UserCache::get($uid, $location);
}

function klicktipp_cache_get($location) {
  return Cache::get($location);
}

/**
 * klicktipp cache set
 */
function klicktipp_user_cache_set($uid, $location, $data, $expire = CACHE_TEMPORARY) {
  UserCache::set($uid, $location, $data, $expire);
}

function klicktipp_cache_set($location, $data, $expire = CACHE_TEMPORARY) {
  Cache::set($location, $data, $expire);
}

/**
 * klicktipp cache clear
 */
function klicktipp_user_cache_clear($uid, $location = '') {
  if (!empty($location)) {
    UserCache::remove($uid, $location);

    return;
  }

  UserCache::clear($uid);
}

function klicktipp_cache_clear($location) {
  Cache::remove($location);
}

/**
 * clear current users cache
 */
function klicktipp_clearcache() {
  UserCache::clearCurrentUserCache();
}

/**
 * reditrect to user/<uid>/edit
 * replaces user/me/edit, as "me" might not be the logged in user
 */
function klicktipp_goto_user_edit($uid) {
  if (user_access('administer klicktipp')) {
    // support staff should be able to use "me" replacement in customer menu
    drupal_goto("user/$uid/edit");
  }
  else {
    // redirect to "own" account
    global $user;
    drupal_goto("user/{$user->uid}/edit");
  }
}

/**
 * Check access to sender domains dialog
 *
 * @param \stdClass $account
 *
 * @return bool
 */
function klicktipp_domains_access(stdClass $account) {
  if (!klicktipp_user_edit_access($account)) {
    return FALSE;
  }
  $userGroup = UserGroups::RetrieveUserGroup($account->RelUserGroupID);
  $limitDomains = $userGroup['Data']['LimitDomains'] ?? 0;
  return $limitDomains === UserGroups::LIMIT_UNLIMITED || $limitDomains > 0;
}

/**
 * charts
 */
function klicktipp_include_google_chart_api() {

  static $google_api;
  if (!empty($google_api)) {
    return;
  }

  Libraries::include("charts.inc");
  ktcharts_include_google_chart_api();

  $google_api = TRUE;

}

/**
 * store message (for later display message to a specific user e.g. from cron)
 */
function klicktipp_set_message($uid, $message, $type = 'status') {
  if ($cached_xml = cache_get("klicktipp:message:$uid", 'cache')) {
    $messages = unserialize($cached_xml->data);
  }
  else {
    $messages = array();
  }

  $messages[] = array($message, $type);

  cache_set("klicktipp:message:$uid", serialize($messages), 'cache');
}


/**
 * display stored message (e.g. from cron) to the current user
 */
function klicktipp_get_messages() {
  global $user;
  $uid = $user->uid;
  if ($uid && $cached_xml = cache_get("klicktipp:message:$uid", 'cache')) {
    $messages = unserialize($cached_xml->data);
    cache_clear_all("klicktipp:message:$uid", 'cache');

    foreach ($messages as $m) {
      [$message, $type] = $m;
      drupal_set_message($message, $type);
    }
  }
}

/*
 * Errors logged via this function will have the right format to be caught with the prod_check
 * $notify_user == TRUE: display a predefined user message (not $message) via drupal_set_error
 */
function _klicktipp_typed_unexpected_error($type, $message, $args = array(), $notify_user = FALSE, $link = NULL) {
  Errors::typedUnexpected($type, $message, $args, $notify_user, $link);
}

function _klicktipp_unexpected_error($message, $args = array(), $notify_user = FALSE, $link = NULL) {
  Errors::unexpected($message, $args, $notify_user, $link);
}

/*
 * usage logging
 */
function _klicktipp_usage($function) {
  watchdog('kt-usage', $function);
}

/**
 * Whitelable message for redirect
 */
function klicktipp_invalid_link($UserID = 0) {
  // display error as whitelabel page
  klicktipp_display_whitelabel_message(t('Redirect Error'), t('Sorry, this link is no longer valid.'), '', $UserID);
  // this wont return
}

/**
 * Add Drupal.t strings from inline JavaScripts to the Drupal.locale.strings object
 * normally only Drupal.t strings in JavaScript files will be added
 * _locale_rebuild_js() creates an extra js file to init the Drupal.locale.strings object
 * -> all inline strings will be stored in Drupal.settings.inlineLocaleStrings via drupal_add_js
 * => a merge script will add them to Drupal.locale.strings on page load
 */
function klicktipp_t($string, $params = '') {

  static $called;

  if (!isset($called)) {
    // add the merge script only once
    $called = TRUE;

    $script = "$(document).ready(function() {
      if (Drupal.settings.inlineLocaleStrings) {
        Drupal.locale.strings = Drupal.locale.strings || {};
        Drupal.locale.strings[''] = Drupal.locale.strings[''] || {};
        for ( var s in Drupal.settings.inlineLocaleStrings ) {
          if ( $.isArray(Drupal.settings.inlineLocaleStrings[s]) )
            Drupal.locale.strings[''][s] = Drupal.settings.inlineLocaleStrings[s][0];
          else
            Drupal.locale.strings[''][s] = Drupal.settings.inlineLocaleStrings[s];
        }
      }
    });";

    drupal_add_js($script, array('type' => 'inline'));

  }

  //drupal will append the string to Drupal.settings.inlineLocaleStrings array in case it already contains values
  //if the same string is added, drupal creates an array, the merge scripts handles it accordingly
  $translation = array('inlineLocaleStrings' => array($string => t(/*ignore*/$string)));
  drupal_add_js($translation, array(
    'type' => 'setting',
    'scope' => 'header', 'group' => JS_DEFAULT
  ));

  $wrapped = (strpos("'", $string) !== FALSE) ? '"' . $string . '"' : "'$string'";

  return (empty($params)) ? "Drupal.t(/*ignore*/$wrapped)" : "Drupal.t(/*ignore*/$wrapped, $params)";

}

/**
 * Get the sort criteria from Drupals tablesort
 * Convert to CORE API and klicktipp_uasort format: array(field_name => 'ASC' || 'DESC')
 * @param $header : header array with tablesort info used in theme('table,...)
 * @param $default : default sort criteria
 */
function klicktipp_get_tablesort($header, $default) {

  $ts = tablesort_init($header);
  if ($ts['sql']) {
    // Based on code from db_escape_table(), but this can also contain a dot.
    $field = preg_replace('/[^A-Za-z0-9_.]+/', '', $ts['sql']);

    // Sort order can only be ASC or DESC.
    $sort = drupal_strtoupper($ts['sort']);
    $sort = in_array($sort, array('ASC', 'DESC')) ? $sort : 'DESC';

  }

  if (empty($field) || empty($sort)) {
    return (empty($default)) ? array() : $default;
  }

  return array($field => $sort);

}

/**
 * Klicktipp customizable uasort function
 * Sort associative arrays by value for a certain key
 * @param $values : array to sort
 * @param $ArraySort : sort criteria array(key => 'ASC' || 'DESC')
 * @param $SortFunction : function name of sort function (example: 'strcmp') or leave empty for numeric values
 */
function klicktipp_uasort(&$values, $ArraySort, $SortFunction = '') {
  //for arrays only 1 search criteria is possible, take the first array entry
  $field = array_keys($ArraySort);
  $field = $field[0];
  $sort = strtoupper($ArraySort[$field]);

  if (function_exists($SortFunction)) {
    $sort_callback = function ($a, $b) use ($SortFunction, $field, $sort) {
      if ($sort == 'ASC') {
        return $SortFunction($a[$field], $b[$field]);
      }
      else {
        return $SortFunction($b[$field], $a[$field]);
      }
    };

  }
  else {
    //we expect numeric values
    $sort_callback = function ($a, $b) use ($field, $sort) {
      if ($sort == 'ASC') {
        return $a[$field] > $b[$field];
      }
      else {
        return $a[$field] < $b[$field];
      }
    };

  }

  uasort($values, $sort_callback);

  return TRUE;
}

/*
 * quickhelp
 */

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_quickhelp($id) {

  $quickhelps = variable_get(KLICKTIPP_QUICKHELP_REFERENCE, array());

  if (empty($id)) {
    return array();
  }

  if (!isset($quickhelps[$id])) {
    //register the quickhelp id so it will show up in the settings form
    $quickhelps[$id] = array(
      'id' => $id,
      'help_text' => '',
      'dialog_url' => trim(request_uri(), '/'), //for the quickhelp settings
    );

    variable_set(KLICKTIPP_QUICKHELP_REFERENCE, $quickhelps);
  }

  return $quickhelps[$id];

}

/**
 * return the body html of a drupal confirm modal
 *
 * @param $EntityName string
 * @param string $Message
 * @param string $Warning
 * @return string rendered html
 */
function _klicktipp_confirm_modal_html_ajax(
  $EntityName,
  $Message = KLICKTIPP_DELETE_CONFIRM_MESSAGE_ASSURE,
  $Warning = KLICKTIPP_DELETE_CONFIRM_MESSAGE_WARNING) {

  $html = '';

  if ($Message) {
    $Message = ($Message == KLICKTIPP_DELETE_CONFIRM_MESSAGE_ASSURE) ? t(/*ignore*/KLICKTIPP_DELETE_CONFIRM_MESSAGE_ASSURE, array('%name' => $EntityName)) : $Message;
    $html .= "<p class='modal-message'>$Message</p>";
  }

  if ($Warning) {
    $Warning = ($Warning == KLICKTIPP_DELETE_CONFIRM_MESSAGE_WARNING) ? t(/*ignore*/KLICKTIPP_DELETE_CONFIRM_MESSAGE_WARNING) : $Warning;
    $html .= "<p class='modal-warning'><strong>$Warning</strong></p>";
  }

  return $html;
}

/**
 * return a drupal form array for a modal-capable delete confirm dialog (bootstrap)
 * $ModalID => id of the madal container, must be passed by #value to the corresponding modal trigger button
 * $EntityName => Name of the object the action of the modal dialog is applied to (only for display)
 * $Title => Titel of the modal (header)
 * $ShowSubmit => show/hide the submit button
 * $Message => content of the modal dialog
 * $Warning => highlighted message just before the buttons
 * $Style => string of CSS classes to modify the modal theme
 */
function _klicktipp_confirm_form_modal(
  $ModalID,
  $EntityName,
  $Title,
  $ShowSubmit = TRUE,
  $Message = KLICKTIPP_DELETE_CONFIRM_MESSAGE_ASSURE,
  $Warning = KLICKTIPP_DELETE_CONFIRM_MESSAGE_WARNING,
  $Style = 'modal-confirm',
  $DisableSubmit = FALSE) {

  $weight = 1;

  $form = array(
    '#prefix' => '<div class="modal fade" id="' . $ModalID . '" tabindex="-1" role="dialog" aria-labelledby="' . $Title . '" aria-hidden="true">
      <div class="modal-dialog ' . $Style . '">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">
              <i class="modal-icon modal-icon-close" data-dismiss="modal" aria-hidden="true"></i>
              <i class="modal-icon modal-icon-confirm"></i>' . $Title . '
            </h4>
          </div>',
    '#suffix' => '</div></div></div>',
    '#skip_duplicate_check' => TRUE,
    // Confirm form fails duplication check, as the form values rarely change -- so skip it.
  );

  //store the modal id for the javascript
  $form['#attributes']['data-modal-id'] = $ModalID;

  //modal dialog markup
  $form['ModalContent'] = array(
    '#prefix' => '<div class="modal-body">',
    '#suffix' => '</div>',
    '#weight' => $weight++,
  );

  if (!empty($Message)) {
    $Message = ($Message == KLICKTIPP_DELETE_CONFIRM_MESSAGE_ASSURE) ? t(/*ignore*/KLICKTIPP_DELETE_CONFIRM_MESSAGE_ASSURE, array('%name' => $EntityName)) : $Message;
    $form['ModalContent']['Message'] = array(
      '#type' => 'markup',
      '#value' => "<p class='modal-message'>$Message</p>",
      '#weight' => $weight++,
    );
  }

  //show a warning only if submit button is shown
  if (!empty($Warning) && $ShowSubmit) {
    $Warning = ($Warning == KLICKTIPP_DELETE_CONFIRM_MESSAGE_WARNING) ? t(/*ignore*/KLICKTIPP_DELETE_CONFIRM_MESSAGE_WARNING) : $Warning;
    $form['ModalContent']['Warning'] = array(
      '#type' => 'markup',
      '#value' => "<p class='modal-warning'><strong>$Warning</strong></p>",
      '#weight' => $weight++,
    );
  }

  //modal dialog button row
  $form['ModalButtons'] = array(
    '#prefix' => '<div class="modal-footer">',
    '#suffix' => '</div>',
    '#weight' => $weight++,
  );

  if ($ShowSubmit) {
    //on some occasions, the entity cannot be delete (has dependencies) -> don't show the submit button
    $form['ModalButtons']['Submit'] = array(
      '#type' => 'submit',
      '#theme' => 'klicktipp_delete_submit',
      '#value' => t(/*ignore*/KLICKTIPP_DELETE_CONFIRM_BUTTON_DELETE),
      '#weight' => $weight++,
      '#attributes' => [
        'data-e2e-id' => $ModalID . '-submit'
      ]
    );

    if ($DisableSubmit) {
      // we can not use drupals '#disabled' here because it will
      // force the value to be ignored, even if we change the disabled
      // status with jquery or alike
      $form['ModalButtons']['Submit']['#attributes']['disabled'] = 'disabled';
    }

  }

  //modal dialog close button
  $form['ModalButtons']['Cancel'] = array(
    '#title' => t(/*ignore*/KLICKTIPP_DELETE_CONFIRM_BUTTON_CANCEL),
    '#value' => '#',
    '#theme' => 'klicktipp_modal_cancel_button',
    '#weight' => $weight++,
    '#attributes' => [
      'data-e2e-id' => $ModalID . '-cancel'
    ]
  );

  return $form;

}


function _klicktipp_confirm_transactional_form_modal(
  $ModalID,
  $EntityName,
  $Title,
  $actionText,
  $action,
  $ShowSubmit = TRUE,
  $Warning = null,
  $Style = 'modal-info'
  ) {

  $weight = 1;

  $form = array(
    '#prefix' => '<div class="modal fade" id="' . $ModalID . '" tabindex="-1" role="dialog" aria-labelledby="' . $Title . '" aria-hidden="true">
      <div class="modal-dialog ' . $Style . '">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title">
              <i class="modal-icon modal-icon-close" data-dismiss="modal" aria-hidden="true"></i>
              <i class="modal-icon modal-icon-confirm"></i>' . $Title . '
            </h4>
          </div>',
    '#suffix' => '</div></div></div>',
    '#skip_duplicate_check' => TRUE,
    // Confirm form fails duplication check, as the form values rarely change -- so skip it.
  );

  //store the modal id for the javascript
  $form['#attributes']['data-modal-id'] = $ModalID;

  //modal dialog markup
  $form['ModalContent'] = array(
    '#prefix' => '<div class="modal-body">',
    '#suffix' => '</div>',
    '#weight' => $weight++,
  );

  $form['ModalContent']['Warning'] = array(
    '#type' => 'markup',
    '#value' => "<p class='modal-warning'><strong>$Warning</strong></p>",
    '#weight' => $weight++,
  );


  //modal dialog button row
  $form['ModalButtons'] = array(
    '#prefix' => '<div class="modal-footer">',
    '#suffix' => '</div>',
    '#weight' => $weight++,
  );

  if ($ShowSubmit) {

    $form['ModalButtons']['Submit'] = array(
      '#theme' => 'klicktipp_submit_modal_button',
      '#title' => $actionText,
      '#weight' => $weight++,
      '#attributes' => array (
        'onClick' => '$(".' . $action . ' ").click();'
      )
    );
  }

  //modal dialog close button
  $form['ModalButtons']['Cancel'] = array(
    '#title' => t(/*ignore*/KLICKTIPP_DELETE_CONFIRM_BUTTON_CANCEL),
    '#value' => '#',
    '#theme' => 'klicktipp_modal_cancel_button',
    '#weight' => $weight++,
  );

  return $form;

}

/**
 * Recursive function to get all #modal_confirm values of $element and its children
 * #modal_confirm contains a rendered delete_confirm form (html)
 * Initialy called by theme function "form"
 */

function _klicktipp_get_modal_confirm($element) {

  $modal_confirm = '';

  $children = element_children($element);

  foreach ($children as $key) {
    $modal_confirm .= _klicktipp_get_modal_confirm($element[$key]);
  }

  if (!empty($element['#modal_confirm'])) {
    return $modal_confirm . drupal_render($element['#modal_confirm']);
  }

  return $modal_confirm;

}

/**
 *
 * Create form elements for an overview filter (multiple criterias supported)
 * each filter criteria is represented by a select element containing the criteria options
 * a hidden submit button is added in case the javascript fails
 * @param $ArrayFilters : key = criteria, values = array of criteria options
 * @param $ArrayDefaults : default values for each criteria ( keys == keys of $ArrayFilters)
 * @param $weight : weight of the filter in the form
 * @param $title : title of the filter. Each criteria should have its label in the options (example: "Filter status: Draft", "Filter status: Sent", etc)
 *
 * The filter settings from the user can be read in form_submit ($form_state['values'][$ArrayFilters keys])
 */
function klicktipp_overview_filter($ArrayFilters, $ArrayDefaults, &$weight, $title = '') {

  //create a unique ID, just in case there are multiple forms with filters on one page
  //the onChange event will be added to every select with the class filter-<FilterID>
  $FilterID = uniqid('filter-');

  //wrap the filter elements for grouping and styling
  $form = array(
    '#weight' => $weight++,
    '#prefix' => '<div class="overview-filter">' . ((empty($title)) ? '' : "<label>$title</label>"),
    '#suffix' => '</div>',
  );

  //create a select element for each filter criteria
  $filter_weight = 1;
  foreach ($ArrayFilters as $Filter => $FilterOptions) {

    $form[$Filter] = array(
      '#type' => 'select',
      '#options' => $FilterOptions,
      '#default_value' => $ArrayDefaults[$Filter],
      '#weight' => $filter_weight++,
      '#attributes' => array('class' => array($FilterID)),
      '#prefix' => '<div class="overview-filter-item">',
      '#suffix' => '</div>',
    );

  }

  //submit button for the filter
  //it will be hidden by the javascript and only be visible if the javascript fails
  //a change of any of the criteria select elements will trigger a click on this button to submit the form
  $form['FilterButton'] = array(
    '#type' => 'submit',
    '#theme' => 'klicktipp_submit',
    '#value' => t(/*ignore*/ KLICKTIPP_BUTTON_TEXT_APPLY_FILTER),
    '#weight' => $filter_weight++,
    '#prefix' => '<div class="overview-filter-item">',
    '#suffix' => '</div>',
    '#attributes' => array('class' => array($FilterID)),
  );

  //javascript to hide the submit button and trigger the submit on change of any select element
  $form['FilterScript'] = array(
    '#type' => 'markup',
    '#value' => "<script type='text/javascript'>

      $(document).ready(function () {

        try {
          $('input.$FilterID').hide();
          $('select.$FilterID').change(function () {
            try {
              $('input.$FilterID').trigger('click');
            }
            catch (e) {
              $('input.$FilterID').show();
            }
          });
        }
        catch (e) {
          $('input.$FilterID').show();
        }

      });

      </script>
    ",
  );

  return $form;

}

use App\Klicktipp\Watchdog;
use Dompdf\Dompdf;

/**
 * Convert HTML to a PDF and stream it to the browser
 * Config: @see /site/all/libraries/dompdf/dompdf_config.custom.inc.php
 * @param string $html : HTML content to render the PDF
 * @param string $PDFName : filename of the streamed PDF
 */
function _klicktipp_stream_pdf($html, $PDFName) {

  Libraries::loadDOMPDF();

  $PDFName = (empty($PDFName)) ? "klicktipp.pdf" : $PDFName;
  $html = (empty($html)) ? "<html><head></head><body></body></html>" : $html;

  //create PDF with DOMPDF
  $dompdf = dompdf_render_html($html);

  //direct download
  $dompdf->stream($PDFName);
  exit;

}

/**
 * klicktipp_unsubscriptions
 *
 * DEPRECATED
 * rewrite all calls to use $account->Unsubscriptions directly
 * be aware: this value can be NULL
 */
function klicktipp_unsubscriptions_get_user_unsubscriptions($account) {
  return empty($account->Unsubscriptions) ? array() : $account->Unsubscriptions;
}

/**
 * Implements hook_html_head_alter().
 *
 * Prevent the output of meta generator Drupal 7 (security, obfuscation)
 */
function klicktipp_html_head_alter(&$head_elements) {
  if (isset($head_elements['system_meta_generator'])) {
    unset($head_elements['system_meta_generator']);
  }
}

/**
 * Implements hook_admin_menu_cache_info()
 */
function klicktipp_admin_menu_cache_info() {
  // menu entries for beans defined by klicktipp module
  return [
    GoogleWebRiskBlacklist::class => [
      'title' => 'Google Webrisk auth',
      'callback' => 'klicktipp_admin_menu_flush_cache'
    ]
  ];
}

/**
 * Removes all cache entries related to specified bin
 *
 * @param string $bin
 */
function klicktipp_admin_menu_flush_cache($bin) {
  cache_clear_all(NULL, $bin);
}

function klicktipp_helpscout_beacon() {

  global $user;
  $MainAccountID = Subaccount::FindMainAccount();
  $account = user_load($MainAccountID);

  return AccountManager::getHelpScoutWidget($account, $user);

}

function klicktipp_watchdog(array $logEntry) {
  if (class_exists(Watchdog::class)) {
    Watchdog::logToStdout($logEntry);
  }
}

// Redirect to login page on 403 (drupal only) - execution callback
// note: this won't be migrated to symfony
function _klicktipp_redirect_to_login_on_403($value = NULL) {
  $default_callback =& drupal_static('_klicktipp_redirect_to_login_on_403_default_callback', FALSE);

  global $user;
  if ($value == MENU_ACCESS_DENIED && $user->uid == 0) {
    // on 403 - redirect to login page if user is not logged
    drupal_goto('user');
  }
  else {
    return $default_callback($value);
  }
}
// Redirect to login page on 403 (drupal only) - alter hook
// note: this won't be migrated to symfony
function klicktipp_page_delivery_callback_alter(&$callback) {
  $default_callback =& drupal_static('_klicktipp_redirect_to_login_on_403_default_callback', FALSE);
  $default_callback = $callback;
  $callback = '_klicktipp_redirect_to_login_on_403';
}

/**
 * Mocks some services for testing. Only in test environment.
 *
 * @return void
 */
function klicktipp_prepare_test_mocks()
{

  if (empty($GLOBALS['drupal_test_info']['test_run_id'])) {
    // only mock in test environment
    return;
  }

  // as long as keycloak is not working in simpletets pipeline, we mock it
  $keycloakApiMock = Mockery::mock(KeycloakApi::class);
  // create user just returns username as keycloak identifier
  $keycloakApiMock->allows('createUser')->andReturnArg(0);
  $keycloakApiMock->allows('updateUser')->andReturnNull();
  ServiceContainer::getInstance()->set(KeycloakApi::class, $keycloakApiMock);
}

function klicktipp_redirect_account_security()
{
    drupal_goto(
      ServiceContainer::getInstance()->get(KeycloakApi::class)->getAccountConsoleUrl(),
      ['external' => true],
    );
}

/**
 * Show Menu items only for sub account users
 * @param stdClass $account
 * @return bool
 */
function klicktipp_menu_subaccount_only_access(stdClass $account): bool
{
    global $user;
    if (AccountManager::isAdmin($user)) {
        // needed to add the menu point
        return true;
    }

    if ($user->uid === $account->uid || AccountManager::isSupport($user)) {
        return false;
    }

    $subaccountRelation = Subaccount::FromID($user->uid, $account->uid);
    if ($subaccountRelation && $subaccountRelation->GetData('AgencyAccess')) {
        return false;
    }

    return true;
}

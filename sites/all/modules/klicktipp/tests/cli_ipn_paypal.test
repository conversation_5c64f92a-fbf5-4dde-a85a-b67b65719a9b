<?php

use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\PaymentIPNs;
use App\Klicktipp\PayPal;
use App\Klicktipp\Settings;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;

class cliPayPalTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'cli IPN PayPal',
      'description' => 'IPN PayPal',
      'group' => 'Klicktipp',
    );

  }

  function testPayPal() {

    Libraries::include('amember.inc', '/tests/includes');

    $UserID = 1;

    $ReferenceID = 0;

    $this->assertEqual(PayPal::GetProductCount($UserID), 0, 'Product Count');

    $MerchantName = $UserID;
    $PayPalProductID = 123;
    $ipn_passphrase = PayPal::IPN_PASSPHRASE;
    $StandardCurrency = Settings::get('listbuilding_payments_base_currency');

    // --- Create Test data ---
    $message = "Test init: ";

    //create 1 subscribed and 1 unsubscribed subscriber
    $ArraySubscriberList = array(
      'ListID' => 0,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
    );

    $subscribed_email = '<EMAIL>';
    $subscription = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $subscribed_email,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    );

    $result = Subscribers::Subscribe($subscription);
    $SubscribedID = $result[1];
    $subscription_subscribed = Subscribers::RetrieveFullSubscriber($UserID, $SubscribedID, $ReferenceID);
    $this->assertTrue($subscription_subscribed['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message $subscribed_email subscribed");


    $unsubscribed_email = '<EMAIL>';
    $subscription['EmailAddress'] = $unsubscribed_email;
    $result = Subscribers::Subscribe($subscription);
    $UnSubscribedID = $result[1];
    //unsubscribe
    Subscribers::Unsubscribe($UserID, $unsubscribed_email);
    $subscription_unsubscribed = Subscribers::RetrieveFullSubscriber($UserID, $UnSubscribedID, $ReferenceID);
    $this->assertTrue($subscription_unsubscribed['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message $unsubscribed_email unsubscribed");

    // create payment subscription process
    $ListFieldAndValues = array(
      'Name' => 'Payment List',
      'RelOwnerUserID' => $UserID,
    );

    $PaymentListID = Lists::InsertDB($ListFieldAndValues);
    $this->assertTrue($PaymentListID > 0, "$message Payment List created");


    //create Tags for a Product
    $OptInSubscribeTo = array(
      PayPal::EVENT_PAYMENT => Tag::CreateManualTag($UserID, 'PayPal on_payment', 'on_payment', ''),
      PayPal::EVENT_REFUND => Tag::CreateManualTag($UserID, 'PayPal on_refund', 'on_refund', ''),
      PayPal::EVENT_CHARGEBACK => Tag::CreateManualTag($UserID, 'PayPal on_chargeback', 'on_chargeback', ''),
      PayPal::EVENT_SUB_CANCELED => Tag::CreateManualTag($UserID, 'PayPal on_rebill_canceled', 'on_rebill_canceled', ''),
      PayPal::EVENT_SUB_PAYMENT => Tag::CreateManualTag($UserID, 'PayPal on_rebill_resumed', 'on_rebill_resumed', ''),
    );

    foreach ($OptInSubscribeTo as $event => $TagID) {
      $this->assertTrue($TagID > 0, "$message Tag created for event $event");
    }

    //create Tags for CatchAllProduct
    $OptInSubscribeToCatchAll = array(
      PayPal::EVENT_PAYMENT => Tag::CreateManualTag($UserID, 'PayPal on_payment CatchAll', 'on_payment', ''),
      PayPal::EVENT_REFUND => Tag::CreateManualTag($UserID, 'PayPal on_refund CatchAll', 'on_refund', ''),
      PayPal::EVENT_CHARGEBACK => Tag::CreateManualTag($UserID, 'PayPal on_chargeback CatchAll', 'on_chargeback', ''),
      PayPal::EVENT_SUB_CANCELED => Tag::CreateManualTag($UserID, 'PayPal on_rebill_canceled CatchAll', 'on_rebill_canceled', ''),
      PayPal::EVENT_SUB_PAYMENT => Tag::CreateManualTag($UserID, 'PayPal on_rebill_resumed CatchAll', 'on_rebill_resumed', ''),
    );

    foreach ($OptInSubscribeToCatchAll as $event => $TagID) {
      $this->assertTrue($TagID > 0, "$message Tag created for CatchAll event $event");
    }

    //Create CustomField for Reference
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'PayPal ReferenceCustomField',
      'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
    );
    $ReferenceCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
    $this->assertTrue($ReferenceCustomFieldID > 0, "$message Reference CustomField created");

    //create PayPal product
    $ProductFieldAndValues = array(
      'Name' => 'Test Product 1',
      'RelOwnerUserID' => $UserID,
      'VarcharIndexed' => $MerchantName,
      'RelIndexed' => $PayPalProductID,
      'OptInSubscribeTo' => $OptInSubscribeTo,
      'RelListID' => $PaymentListID,
      'ReferenceCustomFieldID' => $ReferenceCustomFieldID,
    );

    $ProductID = PayPal::InsertDB($ProductFieldAndValues);
    $this->assertTrue($ProductID > 0, "$message Product created");
    $this->assertEqual(PayPal::GetProductCount($UserID), 1, "$message Product Count");
    /** @var PayPal $ObjectListbuildingProduct */
    $ObjectListbuildingProduct = Listbuildings::FromID($UserID, $ProductID);
    $this->assertTrue(!empty($ObjectListbuildingProduct), "$message Product object");
    $this->assertTrue($ObjectListbuildingProduct->GetData('SubscriptionLength') == PaymentIPNs::SUBSCRIPTION_LIFETIME, $message . ' check SubscriptionLength');
    $this->assertTrue($ObjectListbuildingProduct->GetData('RequirePaidBillingStatus') == 1, $message . ' check RequirePaidBillingStatus');

    //create PayPal CachAll product
    $ProductFieldAndValues['Name'] = 'Catch all';
    $ProductFieldAndValues['RelIndexed'] = 0;
    $ProductFieldAndValues['OptInSubscribeTo'] = $OptInSubscribeToCatchAll;
    $CatchAllID = PayPal::InsertDB($ProductFieldAndValues);
    $this->assertTrue($CatchAllID > 0, "$message CatchAllProduct created");
    $this->assertEqual(PayPal::GetProductCount($UserID), 2, "$message Product Count");
    $ObjectListbuildingCatchAll = Listbuildings::FromID($UserID, $CatchAllID);
    $this->assertTrue(!empty($ObjectListbuildingCatchAll), "$message Catchall object");

    // --- End Create Test data ---

    // build user data hash so that the callback knows which product (BuildID)
    $ArrayQueryParameters = array(
        'UserID' => $UserID,
        'BuildID' => PayPal::IPN_PASSPHRASE,
    );
    $UserKey = Core::EncryptURL($ArrayQueryParameters, 'api_key');

    //TESTCASE: IPN connection_test
    $message = "IPN connection_test:";
    $curl_test = array(
      'post1' => 'IPN Test', //some data
      'post2' => 'OK', //some date
      'event' => PayPal::EVENT_RECURR_PAYMENT,
    );

    $Curl = $this->paypal_send_ipn($curl_test, $UserKey, $message);
    $this->assertTrue($Curl == "OK", "$message expected answer");


    // data from PayPal IPN test

    //TESTCASE: live on_payment
    $message = "IPN Payment received (live): ";
    $paypalLiveEmailAddress = '<EMAIL>';
    $TestOrder = array(
      "txn_type" => PayPal::EVENT_PAYMENT,
      "item_number" => $PayPalProductID,
      "mc_gross" => 9.99,
      "protection_eligibility" => 'Eligible',
      "address_status" => 'unconfirmed',
      "payer_id" => '393BXDYKHR5SJ',
      "tax" => 1.60,
      "address_street" => 'Musterstraße 12',
      "payment_date" => date('H:i:s M d, Y') . ' PDT', //HH:MM:SS Mmm DD, YYYY PDT
      "payment_status" => 'Completed',
      "charset" => 'windows-1252',
      "address_zip" => '10707',
      "first_name" => 'Peter',
      "mc_fee" => 0.59,
      "address_country_code" => 'DE',
      "address_name" => 'Max Mustermann',
      "notify_version" => 3.8,
      "custom" => 'shopify',
      "payer_status" => 'verified',
      "business" => '<EMAIL>',
      "address_country" => 'Germany',
      "address_city" => 'Berlin',
      "quantity" => '1',
      "verify_sign" => 'ADqcHejCRn5jCXTcnoXpnSuO2K8.AvoAsvlozOlbSa-3kTB-I9F.QvCH',
      "payer_email" => $paypalLiveEmailAddress,
      "txn_id" => '87403901AX973640V',
      "payment_type" => 'instant',
      "btn_id" => '3250365',
      "last_name" => 'Mueller',
      "address_state" => 'Berlin',
      "receiver_email" => '<EMAIL>',
      "payment_fee" => 0.59,
      "shipping_discount" => 0.00,
      "insurance_amount" => 0.00,
      "receiver_id" => 'KEZVCZ3XS9RSQ',
      "item_name" => 'Sample Buy Now Button',
      "discount" => '0.00',
      "mc_currency" => $StandardCurrency,
      "residence_country" => 'US',
      "test_ipn" => '',
      "reason_code" => '',
      "shipping_method" => 'Default',
      "handling_amount" => 0.00,
      "transaction_subject" => '',
      "payment_gross" => 9.99,
      "shipping" => 0.00,
      "ipn_track_id" => 'd3b445cd7a8ea',
    );

    //TESTCASE: dateformat
    $strtotime = strtotime("2015-03-16 16:11:23 PDT");
    $paypal = strtotime('16:11:23 Mar 16, 2015 PDT');
    $this->assertTrue($strtotime == $paypal, "Order date: strtotime(HH:MM:SS Mmm DD, YYYY PDT)");


    //TESTCASE: write payment
    $message = "Write payment: ";
    $TestOrder['payer_email'] = $paypalLiveEmailAddress;
    $TestOrder['txn_type'] = PayPal::EVENT_PAYMENT;
    $TestOrder['txn_id'] = '87403901AX973640V';

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $PaymentSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, Subscribers::PunycodeEmailAddress($TestOrder['payer_email']));
    $this->assertTrue(!empty($PaymentSubscriber), "$message contact subscribed");

    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue(!empty($payment), "$message Payment with hash found");
    $this->assertTrue($UserID == $payment['RelOwnerUserID'], "$message Payment UserID");
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_ACTIVE, "$message Payment is active");
    $this->assertTrue($PaymentSubscriber['SubscriberID'] == $payment['RelSubscriberID'], "$message Payment SubscriberID " . $payment['RelSubscriberID'] . " (" . $PaymentSubscriber['SubscriberID'] . ")");
    $this->assertTrue($ProductID == $payment['RelBuildID'], "$message Payment BuildID");
    $this->assertTrue(empty($payment['SubscriptionID']), "$message Payment has no subscription id");

    //TESTCASE: payment chargeback
    //
    $message = "Set payment ChargeBack: ";
    $TestOrder['txn_type'] = "";
    $TestOrder['reason_code'] = PayPal::EVENT_CHARGEBACK;

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Chargeback received");

    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue(!empty($payment), "$message Payment with hash found");
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_CHARGEBACK, "$message Payment has a chargeback");

    $TestOrder['txn_type'] = PayPal::EVENT_PAYMENT;
    $TestOrder['test_ipn'] = 1; // test
    $TestOrder['reason_code'] = '';
    $TestOrder['txn_id'] = '87403901AX973640W';

    $message = "Write payment Test IPN: ";

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Test IPN received");

    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue(!empty($payment), "$message payment written");

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $ExpectedValues = $TestOrder;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $ExpectedValues['RelListID'] = $PaymentListID;
    $ExpectedValues['Taggings'] = array(
      $OptInSubscribeTo[PayPal::EVENT_PAYMENT],
      $ObjectListbuildingProduct->GetData('SmartTagID'),
    );
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, TRUE);

    $CheckCustomFields = Subscribers::RetrieveFullSubscriberWithFields($IPNSubscriber['RelOwnerUserID'], $IPNSubscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($CheckCustomFields["CustomField$ReferenceCustomFieldID"] == $TestOrder['custom'], "$message Reference stored");

    //TESTCASE: test on_payment (test)
    $message = "IPN Payment received (test): ";
    $TestOrder['payer_email'] = '<EMAIL>';
    $TestOrder['test_ipn'] = 1; // test
    $TestOrder['txn_type'] = PayPal::EVENT_PAYMENT;
    $TestOrder['txn_id'] = '87403901AX973640X';

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $ExpectedValues = $TestOrder;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $ExpectedValues['RelListID'] = $PaymentListID;
    $ExpectedValues['Taggings'] = array(
      $OptInSubscribeTo[PayPal::EVENT_PAYMENT],
      $ObjectListbuildingProduct->GetData('SmartTagID'),
    );
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, TRUE);

    $CheckCustomFields = Subscribers::RetrieveFullSubscriberWithFields($IPNSubscriber['RelOwnerUserID'], $IPNSubscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($CheckCustomFields["CustomField$ReferenceCustomFieldID"] == $TestOrder['custom'], "$message Reference stored");

    // payments
    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue(!empty($payment), "$message payment written");

    //TESTCASE: live on_refund
    $message = "IPN refund (live): ";
    $TestOrder['payer_email'] = $paypalLiveEmailAddress;
    $TestOrder['reason_code'] = PayPal::EVENT_REFUND;
    $TestOrder['test_ipn'] = ""; // live
    $TestOrder['txn_type'] = "";
    // keep Receipt ID of last payment, so we can refer to this

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message refund");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, Subscribers::PunycodeEmailAddress($TestOrder['payer_email']));
    $ExpectedValues = $TestOrder;
    $ExpectedValues['SubscriptionStatus'] = 0;
    $ExpectedValues['Taggings'] = array(
      $OptInSubscribeTo[PayPal::EVENT_REFUND],
      $ObjectListbuildingProduct->GetData('SmartTagID'),
      $ObjectListbuildingProduct->GetData('RefundSmartTagID'),
    );
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, FALSE);

    $CheckCustomFields = Subscribers::RetrieveFullSubscriberWithFields($IPNSubscriber['RelOwnerUserID'], $IPNSubscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($CheckCustomFields["CustomField$ReferenceCustomFieldID"] == $TestOrder['custom'], "$message Reference stored");

    //TESTCASE: live on_chargeback
    $TestOrder['reason_code'] = 'chargeback'; // update
    $message = "IPN chargeback (live): ";
    $TestOrder['payer_email'] = $paypalLiveEmailAddress;
    $TestOrder['reason_code'] = PayPal::EVENT_CHARGEBACK;

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message chargeback");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, Subscribers::PunycodeEmailAddress($TestOrder['payer_email']));
    $ExpectedValues = $TestOrder;
    $ExpectedValues['SubscriptionStatus'] = 0;
    $ExpectedValues['Taggings'] = array(
      $OptInSubscribeTo[PayPal::EVENT_CHARGEBACK],
      $ObjectListbuildingProduct->GetData('SmartTagID'),
      $ObjectListbuildingProduct->GetData('ChargebackSmartTagID'),
    );
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, FALSE);

    $CheckCustomFields = Subscribers::RetrieveFullSubscriberWithFields($IPNSubscriber['RelOwnerUserID'], $IPNSubscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($CheckCustomFields["CustomField$ReferenceCustomFieldID"] == $TestOrder['custom'], "$message Reference stored");


    //TESTCASE: test on_payment (amount < 1)
    $message = "IPN Payment received (amount < 1, DOI): ";
    $TestOrder['payer_email'] = '<EMAIL>';
    $TestOrder['txn_type'] = PayPal::EVENT_PAYMENT;
    $TestOrder['mc_gross'] = 0.5;

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment recieved");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $ExpectedValues = $TestOrder;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_OPTIN;
    $ExpectedValues['RelListID'] = $PaymentListID;
    $ExpectedValues['Taggings'] = array(
      $OptInSubscribeTo[PayPal::EVENT_PAYMENT],
      $ObjectListbuildingProduct->GetData('SmartTagID'),
    );
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, TRUE);

    //TESTCASE: test on_payment (CatchAll)
    $message = "IPN Payment received (CatchAll): ";
    $TestOrder['payer_email'] = '<EMAIL>';
    $TestOrder['txn_type'] = PayPal::EVENT_PAYMENT;
    $TestOrder['item_number'] = 4711; //does not exist
    $TestOrder['mc_gross'] = 5;

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $ExpectedValues = $TestOrder;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $ExpectedValues['RelListID'] = $PaymentListID;
    $ExpectedValues['Taggings'] = array(
      $OptInSubscribeToCatchAll[PayPal::EVENT_PAYMENT],
      $ObjectListbuildingCatchAll->GetData('SmartTagID'),
    );
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, TRUE);

    //TESTCASE: test on_payment already subscribed
    $message = "IPN Payment received (already subscribed): ";
    $TestOrder['payer_email'] = $subscribed_email;
    //$TestOrder['test_ipn'] = ""; // live
    $TestOrder['txn_type'] = PayPal::EVENT_PAYMENT;

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $ExpectedValues = $TestOrder;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $ExpectedValues['RelListID'] = 0; // imported
    $ExpectedValues['Taggings'] = array(
      $OptInSubscribeToCatchAll[PayPal::EVENT_PAYMENT],
      $ObjectListbuildingCatchAll->GetData('SmartTagID'),
    );
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, TRUE);

    //TESTCASE: test on_payment on unsubscribed
    $message = "IPN Payment received (on unsubscribed): ";
    $TestOrder['payer_email'] = $unsubscribed_email;
    $TestOrder['txn_type'] = PayPal::EVENT_PAYMENT;
    $TestOrder['mc_gross'] = 23.23;

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $ExpectedValues = $TestOrder;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $ExpectedValues['RelListID'] = $PaymentListID;
    $ExpectedValues['Taggings'] = array(
      $OptInSubscribeToCatchAll[PayPal::EVENT_PAYMENT],
      $ObjectListbuildingCatchAll->GetData('SmartTagID'),
    );
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, TRUE);

    //////////////////// LeadValue

    $LeadValueFieldInformation = CustomFields::RetrieveCustomField('LeadValue');

    $TestOrder['payer_email'] = '<EMAIL>';
    $TestOrder["item_number"] = $PayPalProductID;

    //TESTCASE: lead value from gross amount (default)
    $message = "IPN on_payment LeadValue gross amount (live): ";
    $TestOrder['txn_id'] = '87403901AX97364V2'; // update

    $Data = $ObjectListbuildingProduct->GetData();
    $Data['LeadValueFrom'] = PaymentIPNs::LEADVALUE_FROM_GROSS;
    $ObjectListbuildingProduct->UpdateDB($Data);

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $IPNSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $IPNSubscriber['SubscriberID'], $ReferenceID);
    $ExpectedValues = $TestOrder;
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, TRUE);
    $LeadValue = CustomFields::GetCustomFieldData($UserID, $IPNSubscriber['SubscriberID'], $LeadValueFieldInformation, $ReferenceID);
    $ExpectedLeadValue = (int)($ExpectedValues['mc_gross'] * 100);
    $this->assertTrue($LeadValue == $ExpectedLeadValue, "$message LeadValue $LeadValue");

    // payments
    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue($UserID == $payment['RelOwnerUserID'], "$message Payment UserID".print_r($payment,1));
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_ACTIVE, "$message Payment is active");
    $this->assertTrue($payment['RelBuildID'] == $ProductID, "$message Payment BuildID");
    $this->assertTrue($payment['Amount'] == (int)($ExpectedValues['mc_gross'] * 100), "$message Amount");
    $this->assertTrue($payment['Currency'] == Settings::get('listbuilding_payments_base_currency'), "$message Currency");
    $this->assertTrue($payment['LeadValue'] == (int)($ExpectedValues['mc_gross'] * 100), "$message LeadValue");

    //TESTCASE: lead value from net amount
    $message = "IPN on_payment LeadValue net amount (live): ";
    $TestOrder['txn_id'] = '87403901AX97364V3'; // update

    $Data = $ObjectListbuildingProduct->GetData();
    $Data['LeadValueFrom'] = PaymentIPNs::LEADVALUE_FROM_NET;
    $ObjectListbuildingProduct->UpdateDB($Data);

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $IPNSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $IPNSubscriber['SubscriberID'], $ReferenceID);
    $ExpectedValues = $TestOrder;
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, TRUE);
    $LeadValue = CustomFields::GetCustomFieldData($UserID, $IPNSubscriber['SubscriberID'], $LeadValueFieldInformation, $ReferenceID);
    $netamount = (int)($ExpectedValues['mc_gross'] * 100) - (int) ($ExpectedValues['tax'] * 100);
    $ExpectedLeadValue = $ExpectedLeadValue + $netamount;
    $this->assertTrue($LeadValue == $ExpectedLeadValue, "$message LeadValue $LeadValue");

    // payments
    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue($UserID == $payment['RelOwnerUserID'], "$message Payment UserID".print_r($payment,1));
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_ACTIVE, "$message Payment is active");
    $this->assertTrue($payment['RelBuildID'] == $ProductID, "$message Payment BuildID");
    $this->assertTrue($payment['Amount'] == $netamount, "$message Amount");
    $this->assertTrue($payment['Currency'] == Settings::get('listbuilding_payments_base_currency'), "$message Currency");
    $this->assertTrue($payment['LeadValue'] == $netamount, "$message LeadValue");

    //TESTCASE: lead value from payout amount
    $message = "IPN on_payment LeadValue payout amount (live): ";
    $TestOrder['txn_id'] = '87403901AX97364V4'; // update

    $Data = $ObjectListbuildingProduct->GetData();
    $Data['LeadValueFrom'] = PaymentIPNs::LEADVALUE_FROM_PAYOUT;
    $ObjectListbuildingProduct->UpdateDB($Data);

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $IPNSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $IPNSubscriber['SubscriberID'], $ReferenceID);
    $ExpectedValues = $TestOrder;
    $this->paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, TRUE);
    $LeadValue = CustomFields::GetCustomFieldData($UserID, $IPNSubscriber['SubscriberID'], $LeadValueFieldInformation, $ReferenceID);
    $payout = (int) ($ExpectedValues['mc_gross'] * 100) - (int) ($ExpectedValues['mc_fee'] * 100);
    $ExpectedLeadValue = $ExpectedLeadValue + $payout;
    $this->assertTrue($LeadValue == $ExpectedLeadValue, "$message LeadValue $LeadValue");

    // payments
    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue($UserID == $payment['RelOwnerUserID'], "$message Payment UserID".print_r($payment,1));
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_ACTIVE, "$message Payment is active");
    $this->assertTrue($payment['RelBuildID'] == $ProductID, "$message Payment BuildID");
    $this->assertTrue($payment['Amount'] == $payout, "$message Amount");
    $this->assertTrue($payment['Currency'] == Settings::get('listbuilding_payments_base_currency'), "$message Currency");
    $this->assertTrue($payment['LeadValue'] == $payout, "$message LeadValue");

    //////////////////// SubscriptionLength

    $next_payment_at = strtotime("+31 days");
    $now = time();

    $TestOrder['payer_email'] = '<EMAIL>';
    $TestOrder["item_number"] = $PayPalProductID;
    $TestOrder['txn_type'] = PayPal::EVENT_PAYMENT;

    //TESTCASE: no subscription
    $message = "no subscription: ";

    // create subscriber w/o payments
    Subscribers::Subscribe([
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $TestOrder['payer_email'],
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    ]);

    $result = Subscribers::Subscribe($subscription);
    $this->assertTrue($result[0], "$message Affiliate");

    $IPNSubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $TestOrder['payer_email']);
    $payment = [
      'RelOwnerUserID' => $UserID,
      'RelBuildID' => $ProductID,
      'RelSubscriberID' => $IPNSubscriber['SubscriberID'],
    ];

    test_check_active_payment($this, $message, $payment, FALSE, FALSE, FALSE, FALSE);

    //TESTCASE: subscription length = SUBSCRIPTION_LIFETIME (default)
    $message = "IPN on_payment SubscriptionLength = SUBSCRIPTION_LIFETIME (live): ";
    $TestOrder['txn_id'] = '99778866X998877A';

    $Data = $ObjectListbuildingProduct->GetData();
    $this->assertTrue($Data['SubscriptionLength'] == PaymentIPNs::SUBSCRIPTION_LIFETIME, "$message SubscriptionLength");

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue($UserID == $payment['RelOwnerUserID'], "$message Payment UserID".print_r($payment,1));
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_ACTIVE, "$message Payment is active");
    $this->assertTrue($payment['RelBuildID'] == $ProductID, "$message Payment BuildID");
    $this->assertTrue($payment['BeginDate'] >= $now && $payment['BeginDate'] <= time(), "$message BeginDate");
    $this->assertTrue($payment['ExpireDate'] == PaymentIPNs::SUBSCRIPTION_LIFETIME_EXPIRE_DATE, "$message ExpireDate");

    test_check_active_payment($this, $message, $payment, FALSE, FALSE, TRUE, FALSE);

    //TESTCASE: subscription length = 98 days
    $message = "SubscriptionLength changed from lifetime to 98 days: ";

    $Data = $ObjectListbuildingProduct->GetData();
    $Data['SubscriptionLength'] = 98;

    // simulate the change has been done through the web interface (see klicktipp_list_digistore_edit_form_submit)
    $ObjectListbuildingProduct->AdjustExpireDateFromSubscriptionLength($Data['SubscriptionLength']);
    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue($UserID == $payment['RelOwnerUserID'], "$message Payment UserID".print_r($payment,1));
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_ACTIVE, "$message Payment is active");
    $this->assertTrue($payment['RelBuildID'] == $ProductID, "$message Payment BuildID");
    $this->assertTrue($payment['BeginDate'] >= $now && $payment['BeginDate'] <= time(), "$message BeginDate");

    $ExpirationDate = $Data['SubscriptionLength'] * 24 * 60 * 60 + $payment['BeginDate'];
    $this->assertTrue( $ExpirationDate == $payment['ExpireDate'], "$message ExpireDate");

    $ObjectListbuildingProduct->UpdateDB($Data);

    //TESTCASE: subscription length = 1 year
    $message = "IPN on_payment SubscriptionLength = 1 year (live): ";
    $TestOrder['txn_id'] = '99778866X998877B';

    $Data = $ObjectListbuildingProduct->GetData();
    $Data['SubscriptionLength'] = 365; // 1 year
    $ObjectListbuildingProduct->UpdateDB($Data);

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue($UserID == $payment['RelOwnerUserID'], "$message Payment UserID".print_r($payment,1));
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_ACTIVE, "$message Payment is active");
    $this->assertTrue($payment['RelBuildID'] == $ProductID, "$message Payment BuildID");
    $this->assertTrue($payment['BeginDate'] >= $now && $payment['BeginDate'] <= time(), "$message BeginDate");
    $this->assertTrue(abs($payment['ExpireDate'] - strtotime('+' . ($Data['SubscriptionLength']+1) . ' days', $payment['BeginDate'])) <= 86400, "$message ExpireDate");

    //TESTCASE: subscription length = SUBSCRIPTION_FROM_IPN
    $message = "IPN on_payment SubscriptionLength = SUBSCRIPTION_FROM_IPN (live): ";
    $TestOrder['txn_id'] = '99778866X998877C';

    $Data = $ObjectListbuildingProduct->GetData();
    $Data['SubscriptionLength'] = PaymentIPNs::SUBSCRIPTION_FROM_IPN;
    $ObjectListbuildingProduct->UpdateDB($Data);

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue($UserID == $payment['RelOwnerUserID'], "$message Payment UserID".print_r($payment,1));
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_ACTIVE, "$message Payment is active");
    $this->assertTrue($payment['RelBuildID'] == $ProductID, "$message Payment BuildID");
    $this->assertTrue($payment['BeginDate'] >= $now && $payment['BeginDate'] <= time(), "$message BeginDate");
    // there is no expire date in the paypal ipn, so the fallback is 35 days (see PaymentIPNs::GetExpireDate)
    $this->assertTrue($payment['ExpireDate'] == strtotime('+35 days', $payment['BeginDate']), "$message ExpireDate");

    //////////////////// RequirePaidBillingStatus

    $TestOrder['payer_email'] = '<EMAIL>';
    $TestOrder["item_number"] = $PayPalProductID;
    $TestOrder['txn_type'] = PayPal::EVENT_PAYMENT;

    //TESTCASE: unpaid + required yes
    $message = "IPN on_payment w/ unpaid + required yes (live): ";
    $TestOrder['payment_status'] = 'Pending';
    $TestOrder['txn_id'] = '99887766X123456A';

    $Data = $ObjectListbuildingProduct->GetData();
    $Data['RequirePaidBillingStatus'] = 1;
    $ObjectListbuildingProduct->UpdateDB($Data);

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue(empty($payment), "$message Payment ignored");

    //TESTCASE: unpaid + required no
    $message = "IPN on_payment w/ unpaid + required no (live): ";
    $TestOrder['payment_status'] = 'Pending';
    $TestOrder['txn_id'] = '99887766X123456B';

    $Data = $ObjectListbuildingProduct->GetData();
    $Data['RequirePaidBillingStatus'] = 0;
    $ObjectListbuildingProduct->UpdateDB($Data);

    $Curl = $this->paypal_send_ipn($TestOrder, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Payment received");

    $payment = PaymentIPNs::GetPaymentByReceiptID($UserID, $ProductID, $TestOrder['txn_id']);
    $this->assertTrue($UserID == $payment['RelOwnerUserID'], "$message Payment UserID".print_r($payment,1));
    $this->assertTrue($payment['PaymentStatus'] == PaymentIPNs::PAYMENT_STATUS_UNPAID, "$message Payment is active");



    // clean up products and payments
    $productCount = PayPal::GetProductCount($UserID);
    $productPaymentCount = $this->GetPaymentCount($UserID, $ProductID);
    $catchAllPaymentCount = $this->GetPaymentCount($UserID, $CatchAllID);

    $this->assertEqual($productCount, 2, 'Product Count: 2 == ' . $productCount);
    $this->assertEqual($productPaymentCount, 10, 'Product Payment Count: 10 == ' . $productPaymentCount);
    $this->assertEqual($catchAllPaymentCount, 1, 'Catch All Product Payment Count: 1 == ' . $catchAllPaymentCount);

    PayPal::DeleteDB([PayPal::$DBUserField => $UserID, PayPal::$DBSerialField => $ProductID]);
    $productCount = PayPal::GetProductCount($UserID);
    $productPaymentCount = $this->GetPaymentCount($UserID, $ProductID);
    $catchAllPaymentCount = $this->GetPaymentCount($UserID, $CatchAllID);

    $this->assertEqual($productCount, 1, 'Delete succeeded? Product Count: 1 == ' . $productCount);
    $this->assertEqual($productPaymentCount, 0, 'Delete succeeded? Product Payment Count: 0 == ' . $productPaymentCount);
    $this->assertEqual($catchAllPaymentCount, 1, 'Catch All Product Payment Count still: 1 == ' . $catchAllPaymentCount);

    PayPal::DeleteDB([PayPal::$DBUserField => $UserID, PayPal::$DBSerialField => $CatchAllID]);
    $productCount = PayPal::GetProductCount($UserID);
    $catchAllPaymentCount = $this->GetPaymentCount($UserID, $CatchAllID);

    $this->assertEqual($productCount, 0, 'Delete succeeded? Product Count: 0 == ' . $productCount);
    $this->assertEqual($catchAllPaymentCount, 0, 'Delete succeeded? Catch All Product Payment Count: 0 == ' . $catchAllPaymentCount);

  }

  function paypal_send_ipn($post, $user_data, $message = '') {

    Libraries::include('ipn_paypal.inc', '/callbacks');

    /* test callback function */

    global $callbackresult;

    if (!function_exists('test_form_redirect')) {
      function test_form_redirect($error = array()) {
        global $callbackresult;

        // simulate curl result
        if (empty($error)) {
          $callbackresult = array(TRUE, 'OK');
        }
        else {
          $callbackresult = array(FALSE, $error);
        }
      }
    }

    $decrypted_user_data = Core::DecryptURL($user_data);
    $UserID = $decrypted_user_data['UserID'];

    // caching breaks tests so we reset the cache for each call
    drupal_static_reset('paypal_posted_value');

    klicktipp_ipn_receiver_paypal($UserID, $post, 'test_form_redirect');

    $this->assertTrue($callbackresult[0], "$message IPN call successfull");

    return $callbackresult[1];
  }


  function paypal_check_subscription($IPNSubscriber, $ExpectedValues, $message, $CheckCustomFields = FALSE) {
    $ReferenceID = 0;

    $this->assertTrue($IPNSubscriber, "$message Buyer subscribed");
    $ArraySubscriber = Subscribers::RetrieveFullSubscriberWithFields($IPNSubscriber['RelOwnerUserID'], $IPNSubscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue(Subscribers::IsSameEmailAddress($ArraySubscriber['EmailAddress'], $ExpectedValues['payer_email']), "$message EmailAddress " . $ExpectedValues['payer_email']);

    if (!empty($ExpectedValues['SubscriptionStatus'])) {
      $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == $ExpectedValues['SubscriptionStatus'], "$message Status " . $ExpectedValues['SubscriptionStatus'] . " [" . $ArraySubscriber['SubscriptionStatus'] . "]");
      $this->assertTrue($ArraySubscriber['RelListID'] == $ExpectedValues['RelListID'], "$message List " . $ExpectedValues['RelListID']);
    }

    //check if subscriber was tagged
    foreach ($ExpectedValues['Taggings'] as $tagid) {
      $this->assertTrue(!empty(Subscribers::RetrieveTagging($IPNSubscriber['RelOwnerUserID'], $tagid, $IPNSubscriber['SubscriberID'], $ReferenceID)), $message . ' Subscriber tagged with '.$tagid);
    }

    //check custom fields
    if ($CheckCustomFields) {
      $CustomFields = array(
        'FirstName' => "first_name",
        'LastName' => "last_name",
        'Street1' => "address_street",
        'City' => "address_city",
        'Zip' => "address_zip",
        'State' => "address_state",
        'Country' => "address_country_code",
      );

      foreach ($CustomFields as $CustomFieldID => $PostID) {
        if ($CustomFieldID == "Country") {
          $this->assertTrue($ArraySubscriber["CustomField$CustomFieldID"] == $ExpectedValues[$PostID], "$message CustomField $CustomFieldID = iso3166_" . $ExpectedValues[$PostID]);
        }
        else {
          $this->assertTrue($ArraySubscriber["CustomField$CustomFieldID"] == $ExpectedValues[$PostID], "$message CustomField $CustomFieldID = " . $ExpectedValues[$PostID] . " [" . $ArraySubscriber["CustomField$CustomFieldID"] . "]");
        }

      }

    }


  }

  private function GetPaymentCount($UserID, $BuildID) {
    return db_query("SELECT COUNT(*) FROM {payments} WHERE RelOwnerUserID = :RelOwnerUserID AND RelBuildID = :RelBuildID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelBuildID' => $BuildID,
    ))->fetchField();
  }

}

<?php

use App\Klicktipp\BounceEngine;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsProcessFlow;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Emails;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\Redis\RedisFactory;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\ToolFacebookAudience;
use App\Klicktipp\ToolOutboundGeneral;
use App\Klicktipp\TransactionalQueue;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\UserCache;
use App\Klicktipp\VarFacebookAudience;
use App\Tests\Integration\Shared\AmqpSubscriberQueueTestHelper;

class coreapiTransactionalProcessflowTestCase extends DrupalWebTestCase {

  public function setUp(): void
  {
    parent::setUp();
    AmqpSubscriberQueueTestHelper::initialize();

    $client = (new RedisFactory())->getRedis(KLICKTIPP_REDIS_RATE_LIMIT_DSN);
    $existingRedisKeys = $client->keys('user:subscriber:actions:email:sent:*');

    foreach ($existingRedisKeys as $key) {
      $client->del($key);
    }
  }
  
  public static function getInfo() {
    return array(
      'name' => 'coreapi TransactionalEmails (Automations)',
      'description' => 'Test classes/transaction_emails.inc',
      'group' => 'Klicktipp',
    );

  }

  function testCoreapiTransactionalProcessflow() {

    // Load other modules - Start
    Libraries::include('cron_send.inc', '/cli');
    // Load other modules - End

    Libraries::include('full_user.inc', '/tests/includes');
    Libraries::include('campaigns.inc', '/tests/includes');
    Libraries::include('queue.inc', '/tests/includes');
    Libraries::include('transactional.inc', '/tests/includes');

    /*
     * prepare
     * -> create a tag, a list and a tagged subscriber, so we can do all primary tests to this subscriber
     */
    $UserID = 1;

    $ReferenceID = 0;

    $HttptestURL = 'https://dev:ovi8eTei@'.KLICKTIPP_DOMAIN.'/kthttptest';

    // manipulate user cache
    $NexmoApiKey = 'nexmoapikey';
    $NexmoApiSecret = 'nexmoapisecret';
    $account = user_load($UserID);
    $edit = array();
    $edit['UserSettings']['SMSSettings']['NexmoApiKey'] = $NexmoApiKey;
    $edit['UserSettings']['SMSSettings']['NexmoApiSecret'] = $NexmoApiSecret;
    $edit['UserPrivileges'] = array('UnlimitedEmailsPerDay' => 1);
    user_save($account, $edit);
    $account = user_load($UserID);
    $this->assertTrue($account->UserSettings['SMSSettings']['NexmoApiSecret'] == $NexmoApiSecret, ' NexmoApiSecret');
    $this->assertTrue(user_access('access sms marketing', $account), 'sms access');

    // create tag
    $message = 'create tag';
    $ManualTagID = Tag::CreateManualTag($UserID, 'manual test tag 1', '');
    $this->assertTrue($ManualTagID > 0, "$message #$ManualTagID");

    $ManualTagID2 = Tag::CreateManualTag($UserID, 'manual test tag 2', '');
    $this->assertTrue($ManualTagID2 > 0, "$message #$ManualTagID2");

    $ManualTagID3 = Tag::CreateManualTag($UserID, 'manual test tag 3', '');
    $this->assertTrue($ManualTagID3 > 0, "$message #$ManualTagID3");

    $ManualTagID4 = Tag::CreateManualTag($UserID, 'manual test tag 4', '');
    $this->assertTrue($ManualTagID4 > 0, "$message #$ManualTagID4");

    $ManualTagID5 = Tag::CreateManualTag($UserID, 'manual test tag 5', '');
    $this->assertTrue($ManualTagID5 > 0, "$message #$ManualTagID5");

    $ManualTagID6 = Tag::CreateManualTag($UserID, 'manual test tag 6', '');
    $this->assertTrue($ManualTagID6 > 0, "$message #$ManualTagID6");

    // custom field
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'datetime',
      'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
    );
    $DateTimeCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
    $DateTimeCustomField = CustomFields::RetrieveCustomField($DateTimeCustomFieldID, $UserID);
    $this->assertTrue(is_array($DateTimeCustomField), $message . ' datetime field exists');

    // custom field
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'time',
      'FieldTypeEnum' => CustomFields::TYPE_TIME,
    );
    $TimeCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
    $TimeCustomField = CustomFields::RetrieveCustomField($TimeCustomFieldID, $UserID);
    $this->assertTrue(is_array($TimeCustomField), $message . ' time field exists');

    // custom field
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'number',
      'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
    );
    $NumberCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
    $NumberCustomField = CustomFields::RetrieveCustomField($NumberCustomFieldID, $UserID);
    $this->assertTrue(is_array($NumberCustomField), $message . ' number field exists');

    // create list
    $message = 'create subscriber list';
    $ArrayFieldAndValues = array(
      'Name' => 'test list',
      'RelOwnerUserID' => $UserID,
    );
    $SubscriberListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($SubscriberListID > 0, $message);
    $ArraySubscriberList = Lists::RetrieveListByID($UserID, $SubscriberListID);
    $this->assertTrue($ArraySubscriberList['OptInModeEnum'] == Lists::OPTIN_MODE_DOUBLE, $message . ' optin mode');

    $fieldValues1 = array(
      'CustomFieldFirstName' => 'start-startmiddle-middle-endmiddle-end',
      'CustomFieldBirthday' => strtotime('1981-09-12 08:02:00'),
      "CustomField$DateTimeCustomFieldID" => time(),
      "CustomField$TimeCustomFieldID" => 8 * 3600 + 15 * 60,
      "CustomField$NumberCustomFieldID" => 4711,
    );

    // create subscriber 1 : email subscribed, tagged 1
    $message = 'create subscriber 1';
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
      'OtherFields' => $fieldValues1,
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID = $Result[1];

    // create subscriber 1b : email subscribed, not tagged
    $message = 'create subscriber 1b';
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID1b = $Result[1];

    $fieldValues2 = array(
      'CustomFieldBirthday' => strtotime('2001-02-23 20:15:00'),
      "CustomField$DateTimeCustomFieldID" => strtotime('2020-02-20 20:20:00'),
    );

    // create subscriber 2 : sms subscribed, tagged 2
    $message = 'create subscriber 2';
    $PhoneNumber = '0049123456789';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'PhoneNumber' => $PhoneNumber,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID2,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
      'OtherFields' => $fieldValues2,
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID2 = $Result[1];

    // age tagging 2
    db_query("UPDATE {tagging} SET SubscriptionDate = SubscriptionDate - 3600 WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID  = :RelSubscriberID AND RelTagID = :RelTagID",
      array(':RelOwnerUserID' => $UserID, ':RelSubscriberID' => $SubscriberID2, ':RelTagID' => $ManualTagID2));
    $Tagging = Subscribers::RetrieveTagging($UserID, $ManualTagID2, $SubscriberID2, $ReferenceID);
    $this->assertTrue($Tagging['SubscriptionDate'] < time() - 1000, $message . ' tagging aged');
    $TaggingsCached = TransactionEmails::GetCachedTagging($UserID, $SubscriberID2, $ReferenceID, FALSE, TRUE);
    $this->assertTrue($TaggingsCached[0]['SubscriptionDate'] < time() - 1000, $message . ' tagging aged');

    /*
     * campaign with external start condition
     */
    $message = 'external start';

    // create campaign
    $ArrayFieldAndValues = array(
      'CampaignName' => $message,
      'RelOwnerUserID' => $UserID,
    );
    $CampaignID = CampaignsProcessFlow::InsertDB($ArrayFieldAndValues);
    $BounceCampaignID = $CampaignID;
    /** @var CampaignsProcessFlow $ObjectCamnpaign */
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(!empty($ObjectCamnpaign), $message . ' object');

    // a new campaign is always ready for external start
    $result = CampaignsProcessFlow::CheckForEmptyStart($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' CheckForEmptyStart');

    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID);
    $this->assertTrue(!$result, $message . ' CheckStartState no action');
    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID, TRUE);
    $this->assertTrue(!$result, $message . ' CheckStartState no action forced');

    // add ACTION state 'send email' (and dont ignore subscriber status
    $state = simpletest_transactional_create_processflow_state_action(2, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 1);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' created');

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);

    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID);
    $this->assertTrue(!$result, $message . ' CheckStartState w/ action');
    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID, TRUE);
    $this->assertTrue($result, $message . ' CheckStartState w/ action forced');

    // create a condition and update start state
    $condition = simpletest_transactional_create_processflow_state_decision_condition(CampaignsProcessFlow::PROCESSFLOW_CONDITION_IS_TAGGED, $ManualTagID2);
    $segment = simpletest_transactional_create_processflow_state_decision_segment($conditionsOpAND = TRUE, array($condition));
    $dbarray = $ObjectCamnpaign->GetData();
    $dbarray['ProcessFlow']['states'][0]['segmentsOpAND'] = TRUE;
    $dbarray['ProcessFlow']['states'][0]['segments'] = array($segment);
    CampaignsProcessFlow::UpdateDB($dbarray);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $result = CampaignsProcessFlow::CheckForEmptyStart($ObjectCamnpaign->GetData());
    $this->assertTrue(!$result, $message . ' CheckForEmptyStart w/ condition'.print_r($ObjectCamnpaign,1));
    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID);
    $this->assertTrue(!$result, $message . ' CheckStartState w/ condition');
    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID, TRUE);
    $this->assertTrue(!$result, $message . ' CheckStartState w/ condition forced');

    // change condition and update start state
    $condition = simpletest_transactional_create_processflow_state_decision_condition(CampaignsProcessFlow::PROCESSFLOW_CONDITION_IS_TAGGED, $ManualTagID);
    $segment = simpletest_transactional_create_processflow_state_decision_segment($conditionsOpAND = TRUE, array($condition));
    $dbarray = $ObjectCamnpaign->GetData();
    $dbarray['ProcessFlow']['states'][0]['segmentsOpAND'] = TRUE;
    $dbarray['ProcessFlow']['states'][0]['segments'] = array($segment);
    CampaignsProcessFlow::UpdateDB($dbarray);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $result = CampaignsProcessFlow::CheckForEmptyStart($ObjectCamnpaign->GetData());
    $this->assertTrue(!$result, $message . ' CheckForEmptyStart w/ condition'.print_r($ObjectCamnpaign,1));
    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID);
    $this->assertTrue($result, $message . ' CheckStartState w/ condition');
    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID, TRUE);
    $this->assertTrue($result, $message . ' CheckStartState w/ condition forced');

    // create an empty condition and update start state
    $condition = simpletest_transactional_create_processflow_state_decision_condition('');
    $segment = simpletest_transactional_create_processflow_state_decision_segment($conditionsOpAND = TRUE, array($condition));
    $dbarray = $ObjectCamnpaign->GetData();
    $dbarray['ProcessFlow']['states'][0]['segmentsOpAND'] = TRUE;
    $dbarray['ProcessFlow']['states'][0]['segments'] = array($segment);
    CampaignsProcessFlow::UpdateDB($dbarray);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $result = CampaignsProcessFlow::CheckForEmptyStart($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' CheckForEmptyStart w/o condition'.print_r($ObjectCamnpaign,1));
    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID);
    $this->assertTrue(!$result, $message . ' CheckStartState w/o condition');
    [$result, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID, TRUE);
    $this->assertTrue($result, $message . ' CheckStartState w/o condition forced');

    // check the start condition was re-initialized
    $dbarray = $ObjectCamnpaign->GetData();
    $this->assertTrue(!isset($dbarray['ProcessFlow']['states'][0]['segments']), $message . ' re-initialized');

    /*
     * simple automation start
     */
    $message = 'create automation';

    // create campaign with START state 'is tagged with' $ManualTagID
    $CampaignID = simpletest_transactional_create_campaign($this, $message, $UserID, $ManualTagID);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    // add ACTION state 'send email' (and dont ignore subscriber status
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action_email($ObjectCamnpaign->GetData(), 2, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL, FALSE);
    $EmailID = $state['emailID'];
    $BounceEmailID = $EmailID;
    /** @var Emails $ObjectEmail */
    $ObjectEmail = Emails::FromID($UserID, $EmailID);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 1);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' created');

    // validate
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $TodoList = CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData());
    $this->assertTrue(empty($TodoList), $message . ' validate'. print_r($TodoList,1) . print_r($ObjectCamnpaign->GetData('ProcessFlow'),1));

    // check start condition for subscribers
    [$ActionState, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID, $ReferenceID);
    $this->assertTrue($ActionState == 2, $message . ' subscriber 1 action state '.$ActionState);
    [$ActionState, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID2, $ReferenceID);
    $this->assertTrue($ActionState == 0, $message . ' subscriber 2 action state '.$ActionState);

    simpletest_transactional_call_send();

    // check results
    $result = db_query("SELECT COUNT(*) FROM {".TransactionalQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelAutoResponderID = :RelAutoResponderID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelSubscriberID' => $SubscriberID,
      ':RelAutoResponderID' => $CampaignID
    ))->fetchField();
    $this->assertTrue($result == 0, $message . ' queue');
    $result = db_query("SELECT CampaignStatusEnum FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID AND CampaignID = :CampaignID", array(
      ':RelOwnerUserID' => $UserID,
      ':CampaignID' => $CampaignID
    ))->fetchField();
    $this->assertTrue($result == Campaigns::STATUS_READY, $message . ' status');

    // check with FetchSenddateSubscribers
    $tofetch = $ObjectCamnpaign->FetchSenddateSubscribers($UserID, 0, 10000);
    $this->assertTrue($tofetch == 1, $message . ' FetchSenddateSubscribers:'.$tofetch);
    $unprocessed = $ObjectCamnpaign->SubscriberNotProcessedYetInCampaign($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($unprocessed, $message . ' SubscriberNotProcessedYetInCampaign');

    // start campaign
    $message = 'start automation';
    Campaigns::UpdateCampaign($UserID, $CampaignID, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));

    simpletest_transactional_call_send();
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);

    // check results
    $result = db_query("SELECT COUNT(*) FROM {".TransactionalQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelAutoResponderID = :RelAutoResponderID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelSubscriberID' => $SubscriberID,
      ':RelAutoResponderID' => $CampaignID
    ))->fetchField();
    $this->assertTrue($result == 1, $message . ' queue');
    $result = db_query("SELECT CampaignStatusEnum FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID AND CampaignID = :CampaignID", array(
      ':RelOwnerUserID' => $UserID,
      ':CampaignID' => $CampaignID
    ))->fetchField();
    $this->assertTrue($result == Campaigns::STATUS_SENDING, $message . ' status'.$result);
    $result = db_query("SELECT TotalRecipients FROM {campaign_stats} WHERE RelOwnerUserID = :RelOwnerUserID AND RelCampaignID = :CampaignID", array(
      ':RelOwnerUserID' => $UserID,
      ':CampaignID' => $CampaignID
    ))->fetchField();
    $this->assertTrue($result == 1, $message . ' TotalRecipients');

    // send campaign
    $message = 'send automation';

    // check email stats before
    UserCache::clear($UserID);
    $stats = $ObjectEmail->GetTaggingStats();
    $this->assertTrue($stats['sent'] == 0, $message . ' stats sent'.print_r($stats,1));
    $this->assertTrue($stats['opened'] == 0, $message . ' stats opened');
    $this->assertTrue($stats['clicked'] == 0, $message . ' stats clicked');

    // check with FetchSenddateSubscribers
    $tofetch = $ObjectCamnpaign->FetchSenddateSubscribers($UserID, 0, 10000);
    $this->assertTrue($tofetch == 0, $message . ' FetchSenddateSubscribers:'.$tofetch);
    $unprocessed = $ObjectCamnpaign->SubscriberNotProcessedYetInCampaign($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!$unprocessed, $message . ' SubscriberNotProcessedYetInCampaign');

    // send email
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PAUSED_AFTER);
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);

    // check sent tag
    $tag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_EMAIL_SENT, $EmailID);
    $tagging = Subscribers::RetrieveTagging($UserID, $tag['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), $message);

    // check stats
    $Campaign = $ObjectCamnpaign->GetData();
    $this->assertTrue($Campaign['CampaignStatusEnum'] == Campaigns::STATUS_SENDING, $message . ' status' . print_r($Campaign, 1));
    $this->assertTrue($Campaign['TotalSent'] == 0, $message . ' TotalSent');
    $this->assertTrue($Campaign['TotalRecipients'] == 1, $message . ' TotalRecipients');

    // check email stats after
    UserCache::clear($UserID);
    $stats = $ObjectEmail->GetTaggingStats();
    $this->assertTrue($stats['sent'] == 1, $message . ' stats sent'.print_r($stats,1));
    $this->assertTrue($stats['opened'] == 0, $message . ' stats opened');
    $this->assertTrue($stats['clicked'] == 0, $message . ' stats clicked');

    /*
     * stop and restart (to see it does not change state)
     */
    $message = 'stop and restart';
    $result = Campaigns::StopCampaign($UserID, $CampaignID);
    $this->assertTrue($result, $message . ' stopped');

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue($ObjectCamnpaign->GetData('CampaignStatusEnum') == Campaigns::STATUS_READY, $message . ' status');

    // check state
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PAUSED_AFTER, 2, TransactionEmails::STATUS_PAUSED_AFTER);

    Campaigns::UpdateCampaign($UserID, $CampaignID, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));
    CampaignsProcessFlow::ProcessCampaign($UserID, $CampaignID);

    // go through wait
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PAUSED_AFTER, 2, TransactionEmails::STATUS_PAUSED_AFTER);

    /*
     * stop and change states
     */
    $message = 'stop autoresponder';
    $result = Campaigns::StopCampaign($UserID, $CampaignID);
    $this->assertTrue($result, $message . ' stopped');

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue($ObjectCamnpaign->GetData('CampaignStatusEnum') == Campaigns::STATUS_READY, $message . ' status');

    // add ACTION state 'wait'
    $state = simpletest_transactional_create_processflow_state_action(3, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 2);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' created');

    // check state
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PAUSED_AFTER, 2, TransactionEmails::STATUS_PAUSED_AFTER);

    $message = 'restart autoresponder';
    Campaigns::UpdateCampaign($UserID, $CampaignID, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));
    CampaignsProcessFlow::ProcessCampaign($UserID, $CampaignID);
    _klicktipp_test_single_queue_cron_run('create_queue');

    // disable queue entries
    TransactionEmails::DisableProcessflowInQueue($UserID, $CampaignID);
    simpletest_transactional_check_transactional_state($this, 'entry stopped', $SubscriberID, $ReferenceID, $UserID, $CampaignID, 3, TransactionEmails::STATUS_STOPPED);
    $ArrayCampaign = TransactionEmails::GetCachedCampaign($UserID, $CampaignID);
    // restart queue entries
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ArrayCampaign);
    simpletest_transactional_check_transactional_state($this, 'entry pending', $SubscriberID, $ReferenceID, $UserID, $CampaignID, 3, TransactionEmails::STATUS_PENDING);

    // go through wait
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, 3, TransactionEmails::STATUS_PENDING, 3, TransactionEmails::STATUS_PENDING_AFTER);

    // disable queue entries
    TransactionEmails::DisableProcessflowInQueue($UserID, $CampaignID);
    simpletest_transactional_check_transactional_state($this, 'entry stopped after', $SubscriberID, $ReferenceID, $UserID, $CampaignID, 3, TransactionEmails::STATUS_STOPPED_AFTER);
    $ArrayCampaign = TransactionEmails::GetCachedCampaign($UserID, $CampaignID);
    // restart queue entries
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ArrayCampaign);
    simpletest_transactional_check_transactional_state($this, 'entry pending after', $SubscriberID, $ReferenceID, $UserID, $CampaignID, 3, TransactionEmails::STATUS_PENDING_AFTER);


    /*
     * run SMS subscriber
     */
    $message = 'run SMS subscriber';

    $result = Subscribers::TagSubscriber($UserID, $SubscriberID2, $ManualTagID, $ReferenceID);
    $this->assertTrue($result, $message . ' tagged');
    TransactionEmails::RegisterAutomations($UserID, $SubscriberID2, $ReferenceID);
    simpletest_transactional_call_trigger();

    // halt on active email subscriber
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PAUSED);

    // change state 2 to ignore subscriber status
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $dbarray = $ObjectCamnpaign->GetData();
    foreach($dbarray['ProcessFlow']['states'] as $key => $value) {
      if ($value['id'] == 2) {
        $dbarray['ProcessFlow']['states'][$key]['ignoreSubscriberStatus'] = 1;
      }
    }
    CampaignsProcessFlow::UpdateDB($dbarray);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // the entry goes from PAUSED to PENDING with a delay of 300 seconds, so age that to prepare the next transition
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 300 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID2,
    ));

    // go through wait
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 3, TransactionEmails::STATUS_PENDING_AFTER);

    /*
     * check actions
     */

    $laststate = 3;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_WAIT for custom field

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT . ' (with custom field)';

    // add state
    $stateOptions = [
      'delayType' => CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_DAYS,
      'delayTime' => 10,
      'delayField' => $DateTimeCustomFieldID
    ];
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT, 0, $stateOptions);
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // go through the actual wait action
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignID, $laststate, TransactionEmails::STATUS_PENDING_AFTER, $laststate + 1, TransactionEmails::STATUS_PENDING_AFTER);

    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email([
      'RelSubscriberID' => $SubscriberID2,
      'RelAutoResponderID' => $CampaignID,
      'RelOwnerUserID' => $UserID,
    ], TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] ==  TransactionEmails::STATUS_PENDING_AFTER, $message . ' status after');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == $laststate + 1, $message . ' state after');
    $this->assertTrue($ArrayTransactionalEmail['TimeToSend'] == strtotime('+' . $stateOptions['delayTime'] . ' days', $fieldValues2["CustomField$DateTimeCustomFieldID"]), $message . ' time to send');

    // change custom datetime field
    $newDateTime = strtotime('2020-01-01 10:10:00');
    $DateTimeCustomField = CustomFields::RetrieveCustomField($DateTimeCustomFieldID, $UserID);
    CustomFields::UpdateCustomFieldData($UserID, $SubscriberID2, $ReferenceID, $DateTimeCustomField, $newDateTime);

    // simulate the form being saved
    TransactionEmails::RegisterAutomations($UserID, $SubscriberID2, $ReferenceID);

    // trigger subscriber queue
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData());

    // check changed time to send within the transaction queue
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID2,
      'RelAutoResponderID' => $CampaignID,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] ==  TransactionEmails::STATUS_PENDING_AFTER, $message . ' status after custom field change');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == $laststate + 1, $message . ' state after custom field change');
    $this->assertTrue($ArrayTransactionalEmail['TimeToSend'] == strtotime('+' . $stateOptions['delayTime'] . ' days', $newDateTime), $message . ' time to send after custom field change');

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_WAIT for custom field with exceeded waiting time

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT . ' (with custom field - send time limit exceeded)';

    // add state
    $stateOptions = [
      'delayType' => CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_CUSTOMFIELD_YEARS,
      'delayTime' => 50,
      'delayField' => $DateTimeCustomFieldID
    ];
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT, 0, $stateOptions);
    $ObjectCampaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    simpletest_transactional_create_processflow_add_state($ObjectCampaign, $state, $laststate);
    $result = $ObjectCampaign->UpdateDB($ObjectCampaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // go through the actual wait action
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignID, $laststate, TransactionEmails::STATUS_PENDING_AFTER, $laststate + 1, TransactionEmails::STATUS_PENDING_AFTER);

    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email([
      'RelSubscriberID' => $SubscriberID2,
      'RelAutoResponderID' => $CampaignID,
      'RelOwnerUserID' => $UserID,
    ], TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] ==  TransactionEmails::STATUS_PENDING_AFTER, $message . ' status after');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == $laststate + 1, $message . ' state after');
    $this->assertTrue($ArrayTransactionalEmail['TimeToSend'] != 2147483647, $message . ' time to send must not be limited to 2147483647');

    // reset time to send to go on
    $now = time();
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = $now " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID2,
    ));

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_WAIT for birthday

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT . ' (with birthday)';

    // add state
    $stateOptions = [
      'delayType' => CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_BIRTHDAY,
      'delayField' => 'Birthday',
      "delaySecondsOfDay" => 54000
    ];
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT, 0, $stateOptions);
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // go through the actual wait action
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignID, $laststate, TransactionEmails::STATUS_PENDING_AFTER, $laststate + 1, TransactionEmails::STATUS_PENDING_AFTER);

    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID2,
      'RelAutoResponderID' => $CampaignID,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] ==  TransactionEmails::STATUS_PENDING_AFTER, $message . ' status after');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == $laststate + 1, $message . ' state after');

    // we are going to calculate the new time with the same method as the campaign would
    // because this is tested on another place. Here we just want to be sure that the process
    // has gone through the correct steps
    $currentTimeToSend = TransactionEmails::CalcTransactionalWait($state, time(), $SubscriberID2, $ReferenceID, $UserID);
    $this->assertTrue($ArrayTransactionalEmail['TimeToSend'] == $currentTimeToSend, $message . ' time to send');

    // change custom datetime field
    $newDateTime = strtotime('2000-11-01 06:06:06');
    $BirthdayCustomField = CustomFields::RetrieveCustomField('Birthday', $UserID);
    CustomFields::UpdateCustomFieldData($UserID, $SubscriberID2, $ReferenceID, $BirthdayCustomField, $newDateTime);

    // simulate the form being saved
    TransactionEmails::RegisterAutomations($UserID, $SubscriberID2, $ReferenceID);

    // trigger subscriber queue
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData());

    // check changed time to send within the transaction queue
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID2,
      'RelAutoResponderID' => $CampaignID,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] ==  TransactionEmails::STATUS_PENDING_AFTER, $message . ' status after custom field change');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == $laststate + 1, $message . ' state after custom field change');

    // we are going to calculate the new time with the same method as the campaign would
    // because this is tested on another place. Here we just want to be sure that the process
    // has gone through the correct steps
    $alteredTimeToSend = TransactionEmails::CalcTransactionalWait($state, time(), $SubscriberID2, $ReferenceID, $UserID);
    $this->assertTrue($ArrayTransactionalEmail['TimeToSend'] == $alteredTimeToSend, $message . ' time to send after custom field change');

    // move email subscriber to be in the latest state with its current status to be able to go on
    $currentState = $laststate + 1;
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET StateID = $currentState " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ));

    $now = time();
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = $now " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID2,
    ));

    // set state to a normal test wait state with no wait condition
    // to not have the birthday condition, as this will
    // always reset the TimeToSend when triggering the jobs
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT, 0);
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    simpletest_transactional_create_processflow_update_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' set to default wait state');

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_SENDEMAIL

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action_email($ObjectCamnpaign->GetData(), $laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    // check
    $tag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_EMAIL_SENT, $state['emailID']);
    $tagging = Subscribers::RetrieveTagging($UserID, $tag['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber");
    $tagging = Subscribers::RetrieveTagging($UserID, $tag['TagID'], $SubscriberID2, $ReferenceID);
    $this->assertTrue(empty($tagging), "$message sms subscriber");

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_SENDSMS

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDSMS;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action_email($ObjectCamnpaign->GetData(), $laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDSMS);
    $EmailID = $state['emailID'];
    $BounceSMS = $EmailID;
    /** @var Emails $ObjectEmail */
    $ObjectEmail = Emails::FromID($UserID, $EmailID);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    // check
    $tag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_SMS_SENT, $EmailID);
    $tagging = Subscribers::RetrieveTagging($UserID, $tag['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($tagging), "$message email subscriber");
    $tagging = Subscribers::RetrieveTagging($UserID, $tag['TagID'], $SubscriberID2, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message sms subscriber".print_r([$tag, $tagging],1));

    // check email stats after
    UserCache::clear($UserID);
    $stats = $ObjectEmail->GetTaggingStats();
    $this->assertTrue($stats['sent'] == 1, $message . ' stats sent'.print_r($stats,1));
    $this->assertTrue(!isset($stats['opened']), $message . ' stats -opened');
    $this->assertTrue($stats['clicked'] == 0, $message . ' stats clicked');

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action_email($ObjectCamnpaign->GetData(), $laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYEMAIL);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_NOTIFYSMS

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action_email($ObjectCamnpaign->GetData(), $laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_NOTIFYSMS);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_OUTBOUND

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_OUTBOUND;

    // create outbound
    $ToolID = ToolOutboundGeneral::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'Name' => 'outboundname',
      'ActivationURL' => url($HttptestURL, array('query' => array('secret' => KLICKTIPP_HTTPTEST_SECRET))),
    ));
    $this->assertTrue($ToolID > 0, $message . ' id');

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_OUTBOUND);
    $state['outboundID'] = $ToolID;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    // check
    $ObjectTool = ToolOutboundGeneral::FromID($UserID, $ToolID);
    $tagID = $ObjectTool->GetData('ActivationSmartTagID');
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber");
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID2, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message sms subscriber");

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_TAGGING

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_TAGGING;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_TAGGING);
    $state['tagID'] = $ManualTagID3;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    // check
    $tagID = $ManualTagID3;
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber");
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID2, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message sms subscriber");

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_UNTAGGING

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNTAGGING;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNTAGGING);
    $state['tagID'] = $ManualTagID3;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    // check
    $tagID = $ManualTagID3;
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($tagging), "$message email subscriber");
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID2, $ReferenceID);
    $this->assertTrue(empty($tagging), "$message sms subscriber");

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_SETFIELD

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SETFIELD;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SETFIELD);
    $state['customFieldOp'] = CustomFields::CALCULATE_OP_VALUE;
    $state['customFieldID'] = 'FirstName';
    $state['customFieldValue'] = 'Max';
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    // check
    $FieldInfo = CustomFields::RetrieveCustomField($state['customFieldID'], $UserID);
    $Value = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInfo, $ReferenceID);
    $this->assertTrue($Value == $state['customFieldValue'], "$message email subscriber");
    $Value = CustomFields::GetCustomFieldData($UserID, $SubscriberID2, $FieldInfo, $ReferenceID);
    $this->assertTrue($Value == $state['customFieldValue'], "$message sms subscriber");

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_DECISION / PROCESSFLOW_STATE_TYPE_GOTO

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_DECISION .'+'. CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_GOTO;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);

    $condition = simpletest_transactional_create_processflow_state_decision_condition(CampaignsProcessFlow::PROCESSFLOW_CONDITION_IS_ACTIVE_EMAIL);
    $segment = simpletest_transactional_create_processflow_state_decision_segment($conditionsOpAND = TRUE, array($condition));
    $state = simpletest_transactional_create_processflow_state_decision($laststate + 1, array($segment));
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $conditionstate = $laststate + 1;
    // condition NO -> goto decision
    $state = simpletest_transactional_create_processflow_state_action($laststate + 2, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_GOTO, $conditionstate);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $conditionstate, TRUE);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $conditionstate, TransactionEmails::STATUS_PAUSED_AFTER);
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID2,
      'RelAutoResponderID' => $CampaignID,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] ==  TransactionEmails::STATUS_PAUSED, $message . ' after');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == $laststate + 1, $message . ' state after');

    $laststate++;
    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_UNSUBSCRIBE

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNSUBSCRIBE;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNSUBSCRIBE);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $conditionstate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // check before
    $ArraySubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message email subscriber");
    $ArraySubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID2, $ReferenceID);
    $this->assertTrue($ArraySubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message sms subscriber sms status");

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate'.print_r($ObjectCamnpaign->GetData(),1));
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    // check
    $ArraySubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message email subscriber".print_r($ArraySubscriber,1));
    $ArraySubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID2, $ReferenceID);
    $this->assertTrue($ArraySubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message sms subscriber sms status".print_r($ArraySubscriber,1));

    $laststate++;

    /////////////////////// run PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD


    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD;

    //--- create dummy access token (valid)
    $AccessToken = array(
      'FBAppID' => 12345,
      'FBAppSecret' => 'secret',
      'Token' => 'abcdefg',
      'ExpiresAt' => time() + 3600,
      'FacebookAccountID' => '0815',
      'FacebookAccountName' => 'FB Simpletest',
      'ThrottleUntil' => 0,
    );
    VarFacebookAudience::SetVariable($UserID, $AccessToken);
    $ResultAccessTokens = VarFacebookAudience::GetVariable($UserID, array());
    $this->assertTrue($ResultAccessTokens == $AccessToken, "$message access token created");

    //--- create 1 audience for each facebook account

    $ToolValidAudienceID = ToolFacebookAudience::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'Name' => 'Valid Audience',
      'AudienceID' => 4711000,
      'FacebookAccountID' => 4711,
    ));
    $ObjectToolValidAudience = ToolFacebookAudience::FromID($UserID, $ToolValidAudienceID);
    $this->assertTrue($ObjectToolValidAudience, "$message valid audience created");
    $token = $ObjectToolValidAudience->GetValidToken();
    $this->assertTrue($token[0], "$message valid audience has valid token");


    $ToolInvalidAudienceID = ToolFacebookAudience::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'Name' => 'Invalid Audience',
      'AudienceID' => 815000,
      'FacebookAccountID' => 815,
    ));
    $ObjectToolInvalidAudience = ToolFacebookAudience::FromID($UserID, $ToolInvalidAudienceID);
    $this->assertTrue($ObjectToolInvalidAudience, "$message invalid audience created");
    $token = $ObjectToolInvalidAudience->GetValidToken();
    $this->assertTrue($token[0], "$message invalid audience has valid token OK");

    // --- add state for successfull api call

    $fbaud_valid_subscriber_add = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'num_received' => 1,
        'num_invalid_entries' => 0,
        'invalid_entry_samples' => array(),
      )
    ));
    variable_set('simpletest-facebook-graph-api', $fbaud_valid_subscriber_add);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD);
    $state['audienceID'] = $ToolValidAudienceID;
    $state['ignoreExpiredAccess'] = 1;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid action added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    // check
    $tagID = $ObjectToolValidAudience->GetData('SmartTagID');
    $this->assertTrue(!empty($tagID), "$message tag");
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($tagging), "$message email subscriber");

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    $this->drupalQueueCronRun();

    // the entry goes from STATUS_PAUSED_QUEUE_JOB to PENDING with a delay of 60 seconds, so age that to prepare the next transition
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 60 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ));

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber");

    $laststate++;

    // --- add state for expired access with ignoreExpiredAccess

    $fbaud_expired = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => '400',
        'error' => array(
          'code' => 190,
        ),
      )
    ));
    variable_set('simpletest-facebook-graph-api', $fbaud_expired);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD);
    $state['audienceID'] = $ToolInvalidAudienceID;
    $state['ignoreExpiredAccess'] = 1;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' invalid action ignore added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    $token = $ObjectToolInvalidAudience->GetValidToken();
    $this->assertTrue($token[0], $message . ' token valid before api call');

    $this->drupalQueueCronRun();

    // the entry goes from PAUSED to PENDING with a delay of 60 seconds, so age that to prepare the next transition
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 60 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ));

    //since we ignore the subscriber status, we reach the end again
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);
    $token = $ObjectToolInvalidAudience->GetValidToken();
    $this->assertTrue(!$token[0], $message . ' token invalidated');

    $laststate++;

    // --- add state for expired access without ignoreExpiredAccess

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD);
    $state['audienceID'] = $ToolInvalidAudienceID;
    $state['ignoreExpiredAccess'] = 0;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' invalid action no ignore added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    $token = $ObjectToolInvalidAudience->GetValidToken();
    $this->assertTrue(!$token[0], $message . ' token invalid before web send');

    //since we do not ignore the subscriber status, we are still pausing
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PENDING);

    $Delay1Day = db_query("SELECT TimeToSend FROM {".TransactionalQueue::TABLE_NAME."} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ))->fetchField();

    $this->assertTrue($Delay1Day >= strtotime('+86398 seconds'), $message . ' access expired wait 1 day: ' . ($Delay1Day - time()));

    //reactive the token

    $AccessToken = array(
      'FBAppID' => 12345,
      'FBAppSecret' => 'secret',
      'Token' => 'abcdefg',
      'ExpiresAt' => time() + 3600,
      'FacebookAccountID' => '0815',
      'FacebookAccountName' => 'FB Simpletest',
      'ThrottleUntil' => 0,
    );

    VarFacebookAudience::SetVariable($UserID, $AccessToken);
    $ResultAccessTokens = VarFacebookAudience::GetVariable($UserID, array());
    $this->assertTrue($ResultAccessTokens == $AccessToken, "$message access token reactivated");

    //run the campaign again with a valid token but let Facebook return a deprecated API version

    $fbaud_deprecated_version = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => '400',
        'error' => array(
          'message' =>  '(#2635) You are calling a deprecated version of the Ads API. Please update to the latest version: v2.9.',
          'type' =>  'OAuthException',
          'code' => 2635,
          'fbtrace_id' => 'GDUfXTAhJCK'
        ),
      )
    ));
    variable_set('simpletest-facebook-graph-api', $fbaud_deprecated_version);

    //the access token was expired so the entry was set to pending +1 day, so age that to prepare the next transition
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 86400 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ));

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    $this->drupalQueueCronRun();

    //the status is pending since we wait till the API version has been updated
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PENDING);

    //now let Facebook return a valid result

    $fbaud_valid_subscriber_add = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'num_received' => 1,
        'num_invalid_entries' => 0,
        'invalid_entry_samples' => array(),
      )
    ));
    variable_set('simpletest-facebook-graph-api', $fbaud_valid_subscriber_add);

    //the access token was deprecated API so the entry was set to pending +1 day, so age that to prepare the next transition
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 86400 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ));

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //pause the campaign before the cron job runs -> status will be set to pending
    $ArrayCampaign = $ObjectCamnpaign->GetData();
    $ArrayCampaign['CampaignStatusEnum'] = Campaigns::STATUS_DRAFT;
    CampaignsProcessFlow::UpdateDB($ArrayCampaign);

    $this->drupalQueueCronRun();

    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PENDING);

    $ArrayCampaign = $ObjectCamnpaign->GetData();
    $ArrayCampaign['CampaignStatusEnum'] = Campaigns::STATUS_SENDING;
    CampaignsProcessFlow::UpdateDB($ArrayCampaign);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //now let Facebook return a rate limit reached -> status will be set to pending + 5 to 30 minutes

    $fbaud_valid_subscriber_add = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => '400',
        'error' => array(
          'message' =>  '(#17) Rate Limit reached',
          'code' => 17,
        ),
      )
    ));
    variable_set('simpletest-facebook-graph-api', $fbaud_valid_subscriber_add);

    // check
    $tagID = $ObjectToolInvalidAudience->GetData('SmartTagID');
    $this->assertTrue(!empty($tagID), "$message tag");
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($tagging), "$message email subscriber");

    $this->drupalQueueCronRun();

    //check if the status is pending and TimeToSend is between 5 and 30 minutes in the future
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PENDING);

    $Delay5_30 = db_query("SELECT TimeToSend FROM {".TransactionalQueue::TABLE_NAME."} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ))->fetchField();

    //the rate limit was caught
    $this->assertTrue($Delay5_30 >= strtotime('+5 minutes') && $Delay5_30 <= strtotime('+30 minutes'), $message . ' rate limit reached, wait 5 to 30 minutes: ' . ($Delay5_30 - time()));

    //TimeToSend is between 5 and 30 minutes in the future, set to - 30 minutes so we can continue
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 1800 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ));

    ///now let Facebook return a valid result

    $fbaud_valid_subscriber_add = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'num_received' => 1,
        'num_invalid_entries' => 0,
        'invalid_entry_samples' => array(),
      )
    ));
    variable_set('simpletest-facebook-graph-api', $fbaud_valid_subscriber_add);

    //due to the reached rate limit, all calls within the next 5 minutes are also delayed 5 to 30 minutes

    //run state
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);
    $this->drupalQueueCronRun();

    //check if the status is pending and TimeToSend is between 5 and 30 minutes in the future
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PENDING);

    $Delay5_30 = db_query("SELECT TimeToSend FROM {".TransactionalQueue::TABLE_NAME."} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ))->fetchField();

    //the rate limit delay is active
    $this->assertTrue($Delay5_30 >= strtotime('+5 minutes') && $Delay5_30 <= strtotime('+30 minutes'), $message . ' rate limit reached, wait 5 to 30 minutes: ' . ($Delay5_30 - time()));

    //reset the rate limit delay (empty(ThrottleUntil))
    $AccessToken = array(
      'FBAppID' => 12345,
      'FBAppSecret' => 'secret',
      'Token' => 'abcdefg',
      'ExpiresAt' => time() + 3600,
      'FacebookAccountID' => '0815',
      'FacebookAccountName' => 'FB Simpletest',
      'ThrottleUntil' => 0,
    );

    VarFacebookAudience::SetVariable($UserID, $AccessToken);
    $ResultAccessTokens = VarFacebookAudience::GetVariable($UserID, array());
    $this->assertTrue($ResultAccessTokens == $AccessToken, "$message rate limit reset");

    //TimeToSend is between 5 and 30 minutes in the future, set to - 30 minutes so we can continue
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 1800 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ));

    //run state
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    $this->drupalQueueCronRun();

    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber");

    //check if we reached the end again
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

    /////////////////////// recover TransactionEmails::STATUS_PAUSED

    $message = 'STATUS_PAUSED';

    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'SubscriberID' => $SubscriberID2,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' subscribe');

    // check before
    $ArraySubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID2, $ReferenceID);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message sms subscriber");
    $this->assertTrue($ArraySubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message sms subscriber sms status");

    // the entry goes from PAUSED to PENDING with a delay of 300 seconds, so age that to prepare the next transition
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 300 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID2,
    ));

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignID, $conditionstate, TransactionEmails::STATUS_PENDING, $laststate - 2, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //restore facebook audience: first facebook audience add
    $this->drupalQueueCronRun();

    // the job finished succesfully so the entry was set to pending +1 minute, so age that to prepare the next transition
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 60 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID2,
    ));

    $tagID = $ObjectToolValidAudience->GetData('SmartTagID');
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID2, $ReferenceID);
    $this->assertTrue(!empty($tagging), "facebook audience add restore sms subscriber");

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignID, $laststate - 1, TransactionEmails::STATUS_PENDING, $laststate - 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //restore facebook audience: second facebook audience add, now that the token is valid
    $this->drupalQueueCronRun();

    // the job finished succesfully so the entry was set to pending +1 minute, so age that to prepare the next transition
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 60 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID2,
    ));

    $tagID = $ObjectToolValidAudience->GetData('SmartTagID');
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID2, $ReferenceID);
    $this->assertTrue(!empty($tagging), "facebook audience add restore sms subscriber");

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignID, $laststate, TransactionEmails::STATUS_PENDING, $laststate, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //restore facebook audience: third facebook audience add, same as before, token is valid
    $this->drupalQueueCronRun();

    // the job finished succesfully so the entry was set to pending +1 minute, so age that to prepare the next transition
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = TimeToSend - 60 " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID2,
    ));

    // run to check if we reached the end again

    // check
    $ArraySubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message email subscriber".print_r($ArraySubscriber,1));
    $ArraySubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID2, $ReferenceID);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message sms subscriber".print_r($ArraySubscriber,1));
    $this->assertTrue($ArraySubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message sms subscriber sms status".print_r($ArraySubscriber,1));

    // now remove facebook audience subscription (via campaign step)

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_REMOVE;
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_REMOVE);
    $state['audienceID'] = $ToolValidAudienceID;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' state added');

//    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
//    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //restore facebook audience: first facebook audience add
    $this->drupalQueueCronRun();
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID2, $ReferenceID);
    $this->assertTrue(empty($tagging), "facebook audience remove sms subscriber");

    $laststate++;


    /////////////////////// run PROCESSFLOW_STATE_TYPE_STARTAUTOMATION

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STARTAUTOMATION;

    // create a second campaign with "empty" START state (startable from other campaigns)
    $ArrayFieldAndValues = array(
      'CampaignName' => $message,
      'RelOwnerUserID' => $UserID,
    );
    $CampaignID2 = CampaignsProcessFlow::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($CampaignID2 > 0, $message . ' id');
    // add 2 x ACTION state 'wait'
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID2);
    $state = simpletest_transactional_create_processflow_state_action(2, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 1);
    $state = simpletest_transactional_create_processflow_state_action(3, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 2);
    $state = simpletest_transactional_create_processflow_state_action(4, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 3);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' created');
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID2);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');

    // start campaign
    Campaigns::UpdateCampaign($UserID, $CampaignID2, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));

    simpletest_transactional_call_send();

    // check results
    $result = db_query("SELECT COUNT(*) FROM {".TransactionalQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID2
    ))->fetchField();
    $this->assertTrue($result == 0, $message . ' queue');
    $result = db_query("SELECT CampaignStatusEnum FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID AND CampaignID = :CampaignID", array(
      ':RelOwnerUserID' => $UserID,
      ':CampaignID' => $CampaignID2
    ))->fetchField();
    $this->assertTrue($result == Campaigns::STATUS_SENDING, $message . ' status'.$result);

    // tag subscribers
    Subscribers::TagSubscriber($UserID, $SubscriberID, $ManualTagID3, $ReferenceID);
    Subscribers::TagSubscriber($UserID, $SubscriberID2, $ManualTagID3, $ReferenceID);

    // check tagging
    $tagID = $ManualTagID3;
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber");
    $tagging = Subscribers::RetrieveTagging($UserID, $tagID, $SubscriberID2, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message sms subscriber");

    // add state PROCESSFLOW_STATE_TYPE_STARTAUTOMATION to campaign 1
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STARTAUTOMATION);
    $state['campaignID'] = $CampaignID2;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation (campaign 1)
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // check campaign 2 not started
    $result = db_query("SELECT COUNT(*) FROM {".TransactionalQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID2
    ))->fetchField();
    $this->assertTrue($result == 0, $message . ' queue');

    // run (campaign 1)
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID2);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 0, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    $laststate++;

    // check campaign 2 started
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID,
      'RelAutoResponderID' => $CampaignID2,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] == TransactionEmails::STATUS_PENDING, $message . ' started'.print_r($ArrayTransactionalEmail,1));
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == 2, $message . ' state started');
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID2,
      'RelAutoResponderID' => $CampaignID2,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] == TransactionEmails::STATUS_PENDING, $message . ' started'.print_r($ArrayTransactionalEmail,1));
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == 2, $message . ' state started');

    // check CATEGORY_AUTOMATION_STARTED + CATEGORY_AUTOMATION_FINISHED
    $tag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_AUTOMATION_STARTED, $CampaignID2);
    $tagging = Subscribers::RetrieveTagging($UserID, $tag['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber");
    $tag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_AUTOMATION_FINISHED, $CampaignID2);
    $tagging = Subscribers::RetrieveTagging($UserID, $tag['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($tagging), "$message email subscriber");

    /////////////////////// run PROCESSFLOW_STATE_TYPE_STOPAUTOMATION

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STOPAUTOMATION;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_STOPAUTOMATION);
    $state['campaignID'] = $CampaignID2;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID2);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 0, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    $laststate++;

    // check campaign 2 stopped
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID,
      'RelAutoResponderID' => $CampaignID2,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] == TransactionEmails::STATUS_SENT, $message . ' started'.print_r($ArrayTransactionalEmail,1));
    // the previous transactional send first processed campaign 2 (smaller transactional id) and then stopped it, so we are in state 2 as the first state is a wait state
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == 2, $message . ' state started');

    // check CATEGORY_AUTOMATION_STARTED + CATEGORY_AUTOMATION_FINISHED
    $tag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_AUTOMATION_STARTED, $CampaignID2);
    $tagging = Subscribers::RetrieveTagging($UserID, $tag['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber");
    $tag = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_AUTOMATION_FINISHED, $CampaignID2);
    $tagging = Subscribers::RetrieveTagging($UserID, $tag['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber");

    /////////////////////// run PROCESSFLOW_STATE_TYPE_EXIT

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT;

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    TransactionEmails::RunStarted($UserID, $SubscriberID2, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // check CATEGORY_AUTOMATION_STARTED + CATEGORY_AUTOMATION_FINISHED
    $started = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_AUTOMATION_STARTED, $CampaignID);
    $finished = Tag::RetrieveTagOfEntity($UserID, Tag::CATEGORY_AUTOMATION_FINISHED, $CampaignID);
    $tagging = Subscribers::RetrieveTagging($UserID, $started['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber started");
    $tagging = Subscribers::RetrieveTagging($UserID, $finished['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($tagging), "$message email subscriber finished");

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_SENT);

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID2);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 0, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    // check CATEGORY_AUTOMATION_STARTED + CATEGORY_AUTOMATION_FINISHED
    $tagging = Subscribers::RetrieveTagging($UserID, $started['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber started");
    $tagging = Subscribers::RetrieveTagging($UserID, $finished['TagID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($tagging), "$message email subscriber finished");

    $laststate++;

    /////////////////////// change running campaign

    $message = "delete node with STATUS_PAUSED_AFTER";

    // create a second campaign with START state 'is tagged with' $ManualTagID
    $CampaignID4 = simpletest_transactional_create_campaign($this, $message, $UserID, $ManualTagID3);
    $this->assertTrue($CampaignID4 > 0, $message . ' id');
    // add 2 x ACTION state 'wait'
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $state = simpletest_transactional_create_processflow_state_action(2, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 1);
    $state = simpletest_transactional_create_processflow_state_action(3, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 2);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' created');
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');

    // start campaign
    Campaigns::UpdateCampaign($UserID, $CampaignID4, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));

    simpletest_transactional_call_send();

    // check results
    $result = db_query("SELECT COUNT(*) FROM {".TransactionalQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID4
    ))->fetchField();
    $this->assertTrue($result == 2, $message . ' queue '.$result);
    $result = db_query("SELECT CampaignStatusEnum FROM {campaigns} WHERE RelOwnerUserID = :RelOwnerUserID AND CampaignID = :CampaignID", array(
      ':RelOwnerUserID' => $UserID,
      ':CampaignID' => $CampaignID4
    ))->fetchField();
    $this->assertTrue($result == Campaigns::STATUS_SENDING, $message . ' status '.$result);

    // create subscriber 3 : email subscribed, tagged 3
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID3,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID3 = $Result[1];

    // run
    simpletest_transactional_call_trigger();
    simpletest_transactional_call_transactional_send($this, $SubscriberID3, $ReferenceID, $UserID, $CampaignID4, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PENDING_AFTER);
    simpletest_transactional_call_transactional_send($this, $SubscriberID3, $ReferenceID, $UserID, $CampaignID4, 2, TransactionEmails::STATUS_PENDING_AFTER, 3, TransactionEmails::STATUS_PENDING_AFTER);
    simpletest_transactional_call_transactional_send($this, $SubscriberID3, $ReferenceID, $UserID, $CampaignID4, 3, TransactionEmails::STATUS_PENDING_AFTER, 3, TransactionEmails::STATUS_PAUSED_AFTER);

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 0, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    // remove state 3
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $dbarray = $ObjectCamnpaign->GetData();
    foreach($dbarray['ProcessFlow']['states'] as $key => $value) {
      if ($value['next'] == 3) {
        $dbarray['ProcessFlow']['states'][$key]['next'] = 0;
      }
      if ($value['id'] == 3) {
        unset($dbarray['ProcessFlow']['states'][$key]);
      }
    }
    CampaignsProcessFlow::UpdateDB($dbarray);

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate'.print_r($ObjectCamnpaign->GetData(),1));
    TransactionEmails::RunStarted($UserID, $SubscriberID3, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // check failed
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID3,
      'RelAutoResponderID' => $CampaignID4,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] == TransactionEmails::STATUS_FAILED, $message . ' failed'.print_r($ArrayTransactionalEmail,1));
    $this->assertTrue($ArrayTransactionalEmail['StatusReason'] == TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE, $message . ' failed reason');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == 3, $message . ' failed id');

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 1, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    // repeat with PENDING

    $message = "delete node with STATUS_PENDING";

    // add 2 x ACTION state 'wait'
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $waitstate = 4;
    $state = simpletest_transactional_create_processflow_state_action($waitstate, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 2);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' created');
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');

    // create subscriber 4 : email subscribed, tagged 3
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID3,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID4 = $Result[1];

    // run
    simpletest_transactional_call_trigger();
    simpletest_transactional_call_transactional_send($this, $SubscriberID4, $ReferenceID, $UserID, $CampaignID4, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PENDING_AFTER);
    simpletest_transactional_call_transactional_send($this, $SubscriberID4, $ReferenceID, $UserID, $CampaignID4, 2, TransactionEmails::STATUS_PENDING_AFTER, $waitstate, TransactionEmails::STATUS_PENDING_AFTER);

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 1, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    // remove state 2
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $dbarray = $ObjectCamnpaign->GetData();
    foreach($dbarray['ProcessFlow']['states'] as $key => $value) {
      if ($value['next'] == $waitstate) {
        $dbarray['ProcessFlow']['states'][$key]['next'] = 0;
      }
      if ($value['id'] == $waitstate) {
        unset($dbarray['ProcessFlow']['states'][$key]);
      }
    }
    CampaignsProcessFlow::UpdateDB($dbarray);

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate'.print_r($ObjectCamnpaign->GetData(),1));
    TransactionEmails::RunStarted($UserID, $SubscriberID3, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID4, $ReferenceID, $UserID, $CampaignID4, $waitstate, TransactionEmails::STATUS_PENDING_AFTER, $waitstate, TransactionEmails::STATUS_FAILED);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 2, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    /////////////////////// TransactionEmails::STATUS_PAUSED_AFTER_NO

    $message = "STATUS_PAUSED_AFTER_NO";

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);

    $condition = simpletest_transactional_create_processflow_state_decision_condition(CampaignsProcessFlow::PROCESSFLOW_CONDITION_IS_ACTIVE_SMS);
    $segment = simpletest_transactional_create_processflow_state_decision_segment($conditionsOpAND = TRUE, array($condition));
    $conditionstate = 5;
    $state = simpletest_transactional_create_processflow_state_decision($conditionstate, array($segment));
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 2);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' created');
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');

    // create subscriber 5 : email subscribed, tagged 3
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID3,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID5 = $Result[1];

    // run
    simpletest_transactional_call_trigger();
    simpletest_transactional_call_transactional_send($this, $SubscriberID5, $ReferenceID, $UserID, $CampaignID4, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PENDING_AFTER);
    simpletest_transactional_call_transactional_send($this, $SubscriberID5, $ReferenceID, $UserID, $CampaignID4, 2, TransactionEmails::STATUS_PENDING_AFTER, $conditionstate, TransactionEmails::STATUS_PAUSED_AFTER_NO);

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 2, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    // add state
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $waitstate2 = $conditionstate + 1;
    $state = simpletest_transactional_create_processflow_state_action($waitstate2, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $conditionstate, TRUE);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate'.print_r($ObjectCamnpaign->GetData(),1));
    TransactionEmails::RunStarted($UserID, $SubscriberID5, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID5, $ReferenceID, $UserID, $CampaignID4, $waitstate2, TransactionEmails::STATUS_PENDING, $waitstate2, TransactionEmails::STATUS_PENDING_AFTER);
    simpletest_transactional_call_transactional_send($this, $SubscriberID5, $ReferenceID, $UserID, $CampaignID4, $waitstate2, TransactionEmails::STATUS_PENDING_AFTER, $waitstate2, TransactionEmails::STATUS_PAUSED_AFTER);

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 2, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    // remove state 6
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $dbarray = $ObjectCamnpaign->GetData();
    foreach($dbarray['ProcessFlow']['states'] as $key => $value) {
      if ($value['next'] == $waitstate2) {
        $dbarray['ProcessFlow']['states'][$key]['next'] = 0;
      }
      if ($value['nextNo'] == $waitstate2) {
        $dbarray['ProcessFlow']['states'][$key]['nextNo'] = 0;
      }
      if ($value['id'] == $waitstate2) {
        unset($dbarray['ProcessFlow']['states'][$key]);
      }
    }
    CampaignsProcessFlow::UpdateDB($dbarray);
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');

    // create subscriber 6 : email subscribed, tagged 3
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID3,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID6 = $Result[1];

    // run
    simpletest_transactional_call_trigger();
    simpletest_transactional_call_transactional_send($this, $SubscriberID6, $ReferenceID, $UserID, $CampaignID4, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PENDING_AFTER);
    simpletest_transactional_call_transactional_send($this, $SubscriberID6, $ReferenceID, $UserID, $CampaignID4, 2, TransactionEmails::STATUS_PENDING_AFTER, $conditionstate, TransactionEmails::STATUS_PAUSED_AFTER_NO);

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 2, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    // remove state 5
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $dbarray = $ObjectCamnpaign->GetData();
    foreach($dbarray['ProcessFlow']['states'] as $key => $value) {
      if ($value['next'] == $conditionstate) {
        $dbarray['ProcessFlow']['states'][$key]['next'] = 0;
      }
      if ($value['id'] == $conditionstate) {
        unset($dbarray['ProcessFlow']['states'][$key]);
      }
    }
    CampaignsProcessFlow::UpdateDB($dbarray);

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate'.print_r($ObjectCamnpaign->GetData(),1));
    TransactionEmails::RunStarted($UserID, $SubscriberID6, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // check failed
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID6,
      'RelAutoResponderID' => $CampaignID4,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] == TransactionEmails::STATUS_FAILED, $message . ' failed'.print_r($ArrayTransactionalEmail,1));
    $this->assertTrue($ArrayTransactionalEmail['StatusReason'] == TRANSACTION_REASON_AUTOMATION_UNKNOWN_STATE, $message . ' failed reason');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == $conditionstate, $message . ' failed id');

    // check failed
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID4);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalFailed') == 3, $message . ' failed '.$ObjectCamnpaign->GetData('TotalFailed'));

    // check stats
    /* 1->STATUS_SENT
     * 2->STATUS_SENT
     * 3->STATUS_FAILED
     * 4->STATUS_FAILED
     * 5->STATUS_PAUSED_AFTER
     * 6->STATUS_FAILED
     */
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 6, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 3, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 0, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 3, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 3, $message . ' stats onopenend');

    /////////////////////// self constructing condition
    // a condition (e.g. has tag) which leads to a path that negates the condition
    // (tag the missing tag) and returns (goto condition)
    // -> should continue, even the condition node has already been visited
    $message = 'self negating condition';

    // create a third campaign with START state 'is tagged with' $ManualTagID4
    $CampaignID3 = simpletest_transactional_create_campaign($this, $message, $UserID, $ManualTagID4);
    $this->assertTrue($CampaignID3 > 0, $message . ' id');
    $laststate = 1;

    // add state
    /** @var CampaignsProcessFlow $ObjectCamnpaign */
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID3);
    $condition = simpletest_transactional_create_processflow_state_decision_condition(CampaignsProcessFlow::PROCESSFLOW_CONDITION_IS_TAGGED, $ManualTagID4);
    $segment = simpletest_transactional_create_processflow_state_decision_segment($conditionsOpAND = TRUE, array($condition));
    $state = simpletest_transactional_create_processflow_state_decision($laststate + 1, array($segment));
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $conditionstate = $laststate + 1;
    $laststate++;
    // condition YES -> untag + goto decision
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_UNTAGGING);
    $state['tagID'] = $ManualTagID4;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $conditionstate);
    $laststate++;
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_GOTO, $conditionstate);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $laststate++;
    // condition NO -> EXIT
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $conditionstate, TRUE);
    $laststate++;
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID3);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    Campaigns::UpdateCampaign($UserID, $CampaignID3, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));

    simpletest_transactional_call_send();

    // create subscriber 7 : email subscribed, tagged 4
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID4,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID7 = $Result[1];

    // check stats
    simpletest_transactional_call_trigger();
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 1, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 0, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 1, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID7, $ReferenceID, $UserID, $CampaignID3, 2, TransactionEmails::STATUS_PENDING, $laststate, TransactionEmails::STATUS_SENT);

    // check stats
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 1, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 1, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 0, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');

    //////////////////////////////////////////////////////// MultipleSendFlag

    $message = 'MultipleSendFlag';

    // create a third campaign with START state 'is tagged with' $ManualTagID5
    $CampaignIDMS = simpletest_transactional_create_campaign($this, $message, $UserID, $ManualTagID5, array('MultipleSendFlag' => 1));
    $this->assertTrue($CampaignIDMS > 0, $message . ' id');
    $laststate = 1;

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDMS);
    $StartedTagID = $ObjectCamnpaign->GetData('AutomationStartedSmartTagID');
    $FinishedTagID = $ObjectCamnpaign->GetData('AutomationFinishedSmartTagID');

    // add ACTION state 'send email' (and dont ignore subscriber status
    $state = simpletest_transactional_create_processflow_state_action_email($ObjectCamnpaign->GetData(), $laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SENDEMAIL, FALSE);
    $EmailID = $state['emailID'];
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $laststate++;
    // add EXIT
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $laststate++;
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    /** @var Emails $ObjectEmail */
    $ObjectEmail = Emails::FromID($UserID, $EmailID);
    $SentTagID = $ObjectEmail->GetData('EmailSentSmartTagID');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDMS);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate '.print_r($ObjectCamnpaign,1));
    Campaigns::UpdateCampaign($UserID, $CampaignIDMS, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));

    simpletest_transactional_call_send();

    // create subscriber 8 : email subscribed, tagged 5
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID5,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID8 = $Result[1];

    // check stats
    simpletest_transactional_call_trigger();
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 1, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 0, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 1, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDMS);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalSent') == 0, $message . ' stats TotalSent');

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID8, $ReferenceID, $UserID, $CampaignIDMS, 2, TransactionEmails::STATUS_PENDING);

    // check stats
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 1, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 1, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 0, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDMS);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalSent') == 1, $message . ' stats TotalSent');

    $stats = $ObjectEmail->GetTaggingStats();
    $this->assertTrue($stats['sent'] == 1, $message . ' stats sent'.print_r($stats,1));

    // check smart taggings
    $SentTagging = Subscribers::RetrieveTagging($UserID, $SentTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(!empty($SentTagging), $message);
    $StartedTagging = Subscribers::RetrieveTagging($UserID, $StartedTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(empty($StartedTagging), $message);
    $FinishedTagging = Subscribers::RetrieveTagging($UserID, $FinishedTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(!empty($FinishedTagging), $message);

    //////// second run

    // delay is 24h, set maximum to 1 so AlreadySentTest acts like old 1 max logic
    $delay = 1440;
    variable_set('subscriber_mails_per_day_maximum', 1);

    // make the finished tagging older, so the campaign will start (but not the email)
    db_query("UPDATE {tagging} SET SubscriptionDate = :SubscriptionDate WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelTagID = :TagID ", array(
      ':TagID' => $FinishedTagID,
      ':RelOwnerUserID' => $UserID,
      ':RelSubscriberID' => $SubscriberID8,
      ':SubscriptionDate' => strtotime("-$delay minutes") - 1
    ));

    // tag and trigger campaign
    $result = Subscribers::TagSubscriber($UserID, $SubscriberID8, $ManualTagID5, $ReferenceID);
    $this->assertTrue($result, $message . ' tagged');

    TransactionEmails::RegisterAutomations($UserID, $SubscriberID8, $ReferenceID);
    simpletest_transactional_call_trigger();

    // check stats
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 2, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 1, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 1, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID8, $ReferenceID, $UserID, $CampaignIDMS, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PENDING);

    // now make the email sent tagging older, so the campaign can proceed
    db_query("UPDATE {tagging} SET SubscriptionDate = :SubscriptionDate WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID AND RelTagID = :TagID ", array(
      ':TagID' => $SentTagID,
      ':RelOwnerUserID' => $UserID,
      ':RelSubscriberID' => $SubscriberID8,
      ':SubscriptionDate' => strtotime("-$delay minutes") - 1
    ));

    // the campaign has been delayed by AlreadySentTest -> test it
    $TimeToSend = db_query("SELECT TimeToSend FROM {".TransactionalQueue::TABLE_NAME."} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID AND ReferenceID = :ReferenceID", [
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignIDMS,
      ':RelSubscriberID' => $SubscriberID8,
      ':ReferenceID' => $ReferenceID
    ])->fetchField();

    // check if TimeToSend is roughly about $delay minutes into the future (substract 5 minutes due delay being based on oldest entry)
    $this->assertTrue(
      $TimeToSend > (strtotime("+" . ($delay - 5) .  " minutes") ),
      $message . "email delayed (by AlreadySentTest). Now=" .time().", TimeToSend=$TimeToSend, Delay(expected)=$delay, Delay(real)=".(($TimeToSend-time())/60)
    );

    $alreadyBeenSendToSubscriber = db_query("SELECT t.SubscriptionDate FROM {tagging} t " .
                                         " STRAIGHT_JOIN {tag} tt ON tt.TagID = t.RelTagID AND tt.RelOwnerUserID = t.RelOwnerUserID AND tt.Category = :Category AND tt.EntityID = :EntityID " .
                                         " WHERE t.RelOwnerUserID = :RelOwnerUserID AND t.RelSubscriberID = :RelSubscriberID AND t.SubscriptionDate > :SubscriptionDate AND (tt.MultiValue = 0 OR t.ReferenceID = :ReferenceID)", [
                                         ':Category' => Tag::CATEGORY_EMAIL_SENT,
                                         ':EntityID' => $EmailID,
                                         ':RelOwnerUserID' => $UserID,
                                         ':RelSubscriberID' => $SubscriberID8,
                                         ':ReferenceID' => $ReferenceID,
                                         ':SubscriptionDate' => strtotime("-$delay minutes"),
                                       ])->fetchField();

    $this->assertTrue(empty($alreadyBeenSendToSubscriber), $message);

    // make it runable again
    variable_set('subscriber_mails_per_day_maximum', 6);

    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = :TimeToSend " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
        ':TimeToSend' => time() - 1,
        ':RelOwnerUserID' => $UserID,
        ':RelAutoResponderID' => $CampaignIDMS,
        ':RelSubscriberID' => $SubscriberID8,
    ));

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID8, $ReferenceID, $UserID, $CampaignIDMS, 2, TransactionEmails::STATUS_PENDING);

    // check stats
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 1, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 1, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 0, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');

    $stats = $ObjectEmail->GetTaggingStats();
    $this->assertTrue($stats['sent'] == 1, $message . ' stats sent'.print_r($stats,1));

    // check smart taggings
    $SentTagging = Subscribers::RetrieveTagging($UserID, $SentTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(!empty($SentTagging), $message);
    $StartedTagging = Subscribers::RetrieveTagging($UserID, $StartedTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(empty($StartedTagging), $message);
    $FinishedTagging = Subscribers::RetrieveTagging($UserID, $FinishedTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(!empty($FinishedTagging), $message);

    //////////////////////////////////////////////////////// MultipleSendFlag AND

    $message = 'MultipleSendFlag AND';

    // create a third campaign with START state 'is tagged with' $ManualTagID5 AND $ManualTagID6
    $condition1 = simpletest_transactional_create_processflow_state_decision_condition(CampaignsProcessFlow::PROCESSFLOW_CONDITION_IS_TAGGED, $ManualTagID5);
    $condition2 = simpletest_transactional_create_processflow_state_decision_condition(CampaignsProcessFlow::PROCESSFLOW_CONDITION_IS_TAGGED, $ManualTagID6);
    $segment = simpletest_transactional_create_processflow_state_decision_segment($conditionsOpAND = TRUE, array($condition1, $condition2));
    $processflow = array(
      'start' => 1,
      'states' => array(simpletest_transactional_create_processflow_state_start(TRUE, array($segment))),
    );

    // create campaign
    $ArrayFieldAndValues = array(
      'CampaignName' => $message,
      'RelOwnerUserID' => $UserID,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_PROCESSFLOW,
      'ProcessFlow' => $processflow,
    );
    $ArrayFieldAndValues = array_merge($ArrayFieldAndValues, array('MultipleSendFlag' => 1));
    $CampaignIDMSAND = CampaignsProcessFlow::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($CampaignIDMSAND > 0, $message . ' id');

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDMSAND);
    $StartedTagID = $ObjectCamnpaign->GetData('AutomationStartedSmartTagID');
    $FinishedTagID = $ObjectCamnpaign->GetData('AutomationFinishedSmartTagID');

    $laststate = 1;
    // add EXIT
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_EXIT);
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $laststate++;
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDMSAND);
    $validate = CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData());
    $this->assertTrue(empty($validate), $message . ' validate '.print_r(array($validate, $ObjectCamnpaign->GetData()),1));
    Campaigns::UpdateCampaign($UserID, $CampaignIDMSAND, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));

    simpletest_transactional_call_send();

    // create subscriber 8 : email subscribed, tagged 5
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID5,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID8 = $Result[1];
    $result = Subscribers::TagSubscriber($UserID, $SubscriberID8, $ManualTagID6, $ReferenceID);
    $this->assertTrue($result, $message . ' tagged');

    // check stats
    simpletest_transactional_call_trigger();
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 1, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 0, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 1, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDMSAND);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalSent') == 0, $message . ' stats TotalSent');

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID8, $ReferenceID, $UserID, $CampaignIDMSAND, 2, TransactionEmails::STATUS_PENDING);

    // check stats
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 1, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 1, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 0, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDMSAND);
    $this->assertTrue($ObjectCamnpaign->GetData('TotalSent') == 1, $message . ' stats TotalSent');

    $stats = $ObjectEmail->GetTaggingStats();
    $this->assertTrue($stats['sent'] == 1, $message . ' stats sent'.print_r($stats,1));

    // check smart taggings
    $StartedTagging = Subscribers::RetrieveTagging($UserID, $StartedTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(empty($StartedTagging), $message);
    $FinishedTagging = Subscribers::RetrieveTagging($UserID, $FinishedTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(!empty($FinishedTagging), $message);

    //////// second run

    // delay trigger, so "finished" tagging becomes older than "trigger" tagging
    sleep(2);

    // tag and trigger campaign
    $result = Subscribers::TagSubscriber($UserID, $SubscriberID8, $ManualTagID5, $ReferenceID);
    $this->assertTrue($result, $message . ' tagged');

    TransactionEmails::RegisterAutomations($UserID, $SubscriberID8, $ReferenceID);
    simpletest_transactional_call_trigger();

    // check stats
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 2, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 1, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 1, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID8, $ReferenceID, $UserID, $CampaignIDMSAND, 2, TransactionEmails::STATUS_PENDING);

    // check stats
    UserCache::clear($UserID);
    $stats = $ObjectCamnpaign->GetTaggingStats();
    $this->assertTrue($stats['started'] == 1, $message . ' stats started'.print_r($stats,1));
    $this->assertTrue($stats['finished'] == 1, $message . ' stats finished');
    $this->assertTrue($stats['active'] == 0, $message . ' stats active');
    $this->assertTrue($stats['failed'] == 0, $message . ' stats failed');
    $this->assertTrue($stats['onopenend'] == 0, $message . ' stats onopenend');

    // check smart taggings
    $StartedTagging = Subscribers::RetrieveTagging($UserID, $StartedTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(empty($StartedTagging), $message);
    $FinishedTagging = Subscribers::RetrieveTagging($UserID, $FinishedTagID, $SubscriberID8, $ReferenceID);
    $this->assertTrue(!empty($FinishedTagging), $message);

    /*
    * campaign for failed queue worker jobs
    */

    $message = 'queue worker job:';

    // create tag
    $QueueTagID = Tag::CreateManualTag($UserID, 'manual queue tag', '');
    $this->assertTrue($QueueTagID > 0, "$message create tag #$QueueTagID");

    // create campaign with START state 'is tagged with' $QueueTagID
    $CampaignIDqueue = simpletest_transactional_create_campaign($this, $message, $UserID, $QueueTagID);
    $this->assertTrue($CampaignIDqueue > 0, $message . ' id');

    // add ACTION state 'facebook audience'
    $StateID = 2;
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDqueue);
    $CampaignQueueStartedID = $ObjectCamnpaign->GetData('AutomationStartedSmartTagID');
    $CampaignQueueFinishedID = $ObjectCamnpaign->GetData('AutomationFinishedSmartTagID');

    //remember the processflow without the facebook audience state for later
    $ProcessFlowFBAudienceRemoved = $ObjectCamnpaign->GetData('ProcessFlow');

    $state = simpletest_transactional_create_processflow_state_action($StateID, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FACEBOOK_AUDIENCE_ADD);
    $state['audienceID'] = $ToolValidAudienceID;
    $state['ignoreExpiredAccess'] = 1;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, 1);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid facebook audience action added');

    // validate
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDqueue);
    $TodoList = CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData());
    $this->assertTrue(empty($TodoList), $message . ' validate'. print_r($TodoList,1) . print_r($ObjectCamnpaign->GetData('ProcessFlow'),1));

    $AccessToken = array(
      'FBAppID' => 12345,
      'FBAppSecret' => 'secret',
      'Token' => 'abcdefg',
      'ExpiresAt' => time() + 3600,
      'FacebookAccountID' => '0815',
      'FacebookAccountName' => 'FB Simpletest',
      'ThrottleUntil' => 0,
    );


    VarFacebookAudience::SetVariable($UserID, $AccessToken);
    $ResultAccessTokens = VarFacebookAudience::GetVariable($UserID, array());
    $this->assertTrue($ResultAccessTokens == $AccessToken, "$message access token reactivated");

    //Remove smart tag from subscribers
    $SmartTagID = $ObjectToolValidAudience->GetData('SmartTagID');
    Subscribers::UntagSubscriber($UserID, $SubscriberID2, $ReferenceID, $SmartTagID);
    Subscribers::UntagSubscriber($UserID, $SubscriberID3, $ReferenceID, $SmartTagID);
    Subscribers::UntagSubscriber($UserID, $SubscriberID4, $ReferenceID, $SmartTagID);

    //run the campaign again with a valid token

    $fbaud_valid_subscriber_add = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'num_received' => 1,
        'num_invalid_entries' => 0,
        'invalid_entry_samples' => array(),
      )
    ));
    variable_set('simpletest-facebook-graph-api', $fbaud_valid_subscriber_add);

    // start campaign
    Campaigns::UpdateCampaign($UserID, $CampaignIDqueue, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDqueue);

    //--- TestCase: delete transactional queue entry

    //trigger campaign for subscriber

    //tag subscriber for start condition
    Subscribers::TagSubscriber($UserID, $SubscriberID2, $QueueTagID, $ReferenceID);

    // check start condition for subscribers
    [$ActionState, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID2, $ReferenceID);
    $this->assertTrue($ActionState == $StateID, $message . ' subscriber 1 action state '.$ActionState);

    simpletest_transactional_call_send();
    // run and check if the subscriber is waiting for the cron job
    simpletest_transactional_call_transactional_send($this, $SubscriberID2, $ReferenceID, $UserID, $CampaignIDqueue, $StateID, TransactionEmails::STATUS_PENDING, $StateID, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    $DeleteTransaction = TransactionEmails::RetrieveTransactionByID($UserID, $SubscriberID2, $ReferenceID, $CampaignIDqueue);
    $this->assertTrue(!empty($DeleteTransaction), "$message transactional queue entry ready for delete");

    //delete transactional queue entry
    TransactionalQueue::RemoveFromQueue($DeleteTransaction);
    $DeleteTransaction = TransactionEmails::RetrieveTransactionByID($UserID, $SubscriberID2, $ReferenceID, $CampaignIDqueue);
    $this->assertTrue(empty($DeleteTransaction), "$message transactional queue entry deleted");

    //run the queue job
    $this->drupalQueueCronRun();

    $hasSmartTag = Subscribers::RetrieveTagging($UserID, $SmartTagID, $SubscriberID2, $ReferenceID);
    $hasStarted = Subscribers::RetrieveTagging($UserID, $CampaignQueueStartedID, $SubscriberID2, $ReferenceID);
    $hasFinsihed = Subscribers::RetrieveTagging($UserID, $CampaignQueueFinishedID, $SubscriberID2, $ReferenceID);

    $this->assertTrue(empty($hasSmartTag), "$message transactional queue entry deleted -> no smart tag");
    $this->assertTrue(!empty($hasStarted), "$message transactional queue entry deleted -> automation started");
    $this->assertTrue(empty($hasFinsihed), "$message transactional queue entry deleted -> automation not finished");

    //--- TestCase: delete facebook audience state

    Subscribers::TagSubscriber($UserID, $SubscriberID3, $QueueTagID, $ReferenceID); //test case: delete state

    // check start condition for subscribers
    [$ActionState, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID3, $ReferenceID);
    $this->assertTrue($ActionState == $StateID, $message . ' subscriber 2 action state '.$ActionState);

    simpletest_transactional_call_send();
    TransactionEmails::RunStarted($UserID, $SubscriberID3, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    // run and check if the subscriber is waiting for the cron job
    simpletest_transactional_call_transactional_send($this, $SubscriberID3, $ReferenceID, $UserID, $CampaignIDqueue, $StateID, TransactionEmails::STATUS_PENDING, $StateID, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //remove the facebook audience state
    $DBArray = $ObjectCamnpaign->GetData();
    $ProccessFlowFBAudience = $DBArray['ProcessFlow']; //remember for next test case
    $DBArray['ProcessFlow'] = $ProcessFlowFBAudienceRemoved;
    CampaignsProcessFlow::UpdateDB($DBArray);
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDqueue);

    //run the queue job
    $this->drupalQueueCronRun();

    $hasSmartTag = Subscribers::RetrieveTagging($UserID, $SmartTagID, $SubscriberID3, $ReferenceID);
    $hasStarted = Subscribers::RetrieveTagging($UserID, $CampaignQueueStartedID, $SubscriberID3, $ReferenceID);
    $hasFinsihed = Subscribers::RetrieveTagging($UserID, $CampaignQueueFinishedID, $SubscriberID3, $ReferenceID);

    $this->assertTrue(empty($hasSmartTag), "$message facebook audience state deleted -> no smart tag");
    $this->assertTrue(!empty($hasStarted), "$message facebook audience state deleted -> automation started");
    $this->assertTrue(!empty($hasFinsihed), "$message facebook audience state deleted -> automation not finished");

    //--- TestCase: delete campaign

    //restore the facebook audience state
    $DBArray = $ObjectCamnpaign->GetData();
    $DBArray['ProcessFlow'] = $ProccessFlowFBAudience;
    CampaignsProcessFlow::UpdateDB($DBArray);
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignIDqueue);

    Subscribers::TagSubscriber($UserID, $SubscriberID4, $QueueTagID, $ReferenceID); //test case: delete campaign

    // check start condition for subscribers
    [$ActionState, $TimeToSend] = CampaignsProcessFlow::CheckStartState($ObjectCamnpaign->GetData(), $SubscriberID4, $ReferenceID);
    $this->assertTrue($ActionState == $StateID, $message . ' subscriber 3 action state '.$ActionState);

    simpletest_transactional_call_send();
    TransactionEmails::RunStarted($UserID, $SubscriberID4, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    // run and check if the subscriber is waiting for the cron job
    simpletest_transactional_call_transactional_send($this, $SubscriberID4, $ReferenceID, $UserID, $CampaignIDqueue, $StateID, TransactionEmails::STATUS_PENDING, $StateID, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //delete campaign
    $DBArray = $ObjectCamnpaign->GetData();
    CampaignsProcessFlow::DeleteDB($DBArray);

    //run the queue job
    $this->drupalQueueCronRun();

    $hasSmartTag = Subscribers::RetrieveTagging($UserID, $SmartTagID, $SubscriberID4, $ReferenceID);
    $hasStarted = Subscribers::RetrieveTagging($UserID, $CampaignQueueStartedID, $SubscriberID4, $ReferenceID);
    $hasFinsihed = Subscribers::RetrieveTagging($UserID, $CampaignQueueFinishedID, $SubscriberID4, $ReferenceID);

    $this->assertTrue(empty($hasSmartTag), "$message campaign deleted -> no smart tag");
    $this->assertTrue(empty($hasStarted), "$message campaign deleted -> automation started");
    $this->assertTrue(empty($hasFinsihed), "$message campaign deleted -> automation not finished");

    //check if all queue jobs have been finished

    $result = db_query("SELECT started, finished FROM {processlog} WHERE QueueName = :QueueName", array(
      ':QueueName' => 'facebook audience'
    ));
    while( $log = kt_fetch_array($result) ) {
      if ( $log['finished'] < $log['started'] ) {
        $this->assertTrue(FALSE, "$message all facebook audience queue jobs have been finished");
      }
    }

    //check if all status STATUS_PAUSED_QUEUE_JOB have been resolved

    $count = db_query("SELECT COUNT(*) FROM {".TransactionalQueue::TABLE_NAME."} WHERE StatusEnum = :StatusEnum", array(
      ':StatusEnum' => TransactionEmails::STATUS_PAUSED_QUEUE_JOB
    ))->fetchField();
    $this->assertTrue(empty($count), "$message all STATUS_PAUSED_QUEUE_JOB have been finished $count");

    /*
     * bounce subscriber
     */

     Campaigns::UpdateCampaign($UserID, $BounceCampaignID, array(
           'CampaignStatusEnum' => Campaigns::STATUS_SENDING,
           'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
         ));

    BounceEngine::RegisterHardBounce($UserID, $SubscriberID, $EmailAddress, $BounceEmailID, $BounceCampaignID, 'dummy');

    $ObjectEmail = Emails::FromID($UserID, $BounceEmailID);
    $this->assertTrue($ObjectEmail->GetData('TotalHardBounces') == 1, "Email HardBounce ".print_r($ObjectEmail->GetData(),1));

    BounceEngine::RegisterSMSHardBounce($UserID, $SubscriberID2, $PhoneNumber, $BounceSMS, $BounceCampaignID, 'dummy');

    $ObjectEmail = Emails::FromID($UserID, $BounceSMS);
    $this->assertTrue($ObjectEmail->GetData('TotalHardBounces') == 1, "SMS HardBounce ".print_r($ObjectEmail->GetData(),1));

  }
  private function drupalQueueCronRun() {
    drupal_queue_cron_run();
  }

}

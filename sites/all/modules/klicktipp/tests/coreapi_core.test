<?php

use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\Core;
use App\Klicktipp\DatabaseTableWithData;
use App\Klicktipp\Emails;
use App\Klicktipp\Lists;
use App\Klicktipp\Signatures;
use App\Klicktipp\Subscribers;

class coreapiCoreTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'coreapi Core',
      'description' => 'Test Core.',
      'group' => 'Klicktipp',
    );

  }

  /**
   * this method represents the "new old" encryption (ignoring reference id in parametrs
   */
  function EncryptURLWithoutReference($ArrayQueryParameters, $Skript, $APP_URL = APP_URL)
  {
    // try to shorten url to a minimum by coding of position and needed values only
    switch ($Skript) {
      case 'thankyou':
      case 'pending':
        $QueryParameters =
          Core::EncryptURLNumber($ArrayQueryParameters['uid']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['lid']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['sid']) . 'z' .
          EncryptURLSignature_thankyou_v1 . 'z' .
          MinihashCreate($ArrayQueryParameters['lid'], $ArrayQueryParameters['sid']);
        return $APP_URL . "$Skript/$QueryParameters";
      case 'track_link':
        $QueryParameters =
          Core::EncryptURLNumber($ArrayQueryParameters['RevisionID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['Linknumber']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['CampaignID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['SubscriberID']) . 'z' .
          $ArrayQueryParameters['HTML'] . 'z' .
          $ArrayQueryParameters['Preview'] . 'z' .
          EncryptURLSignature_track_link_v1 . 'z' . EncryptURLHash_track_link;
        $Query = $APP_URL . variable_get('klicktipp_aliases_track_link', 'info') . '/' . $QueryParameters;
        if (!empty($ArrayQueryParameters['ShortLink'])) {
          $Query = Core::CreateShortLink($Query);
        }
        return $Query;
      case 'unsubscribe':
      case 'spam_report':
        $QueryParameters =
          Core::EncryptURLNumber($ArrayQueryParameters['ListID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['CampaignID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['SubscriberID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['EmailID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['ReferenceID']) . 'z' .
          $ArrayQueryParameters['Preview'] . 'z' .
          Core::EncryptURLNumber(Core::HashEmail($ArrayQueryParameters['EmailAddress'])) . 'z' .
          EncryptURLSignature_unsubscribe_v2 . 'z' .
          MinihashCreate($ArrayQueryParameters['EmailID'], $ArrayQueryParameters['SubscriberID']);
        if ($Skript == 'spam_report') {
          // same as unsubscribe, but different url
          $Query = $APP_URL . variable_get('klicktipp_aliases_spam_report', 'spam') . '/' . $QueryParameters;
        }
        else {
          if (empty($APP_URL)) {
            // for email header, see SendEngine::SetEmailProperties
            $Query = $QueryParameters;
          }
          else {
            $Query = $APP_URL . variable_get('klicktipp_aliases_unsubscribe', 'abmelden') . '/' . $QueryParameters;
          }
        }
        return $Query;
      case 'track_open':
        $QueryParameters =
          Core::EncryptURLNumber($ArrayQueryParameters['CampaignID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['SubscriberID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['EmailID']) . 'z' .
          $ArrayQueryParameters['Preview'] . 'z' .
          EncryptURLSignature_track_open_v2 . 'z' . EncryptURLHash_track_open;
        return $APP_URL . variable_get('klicktipp_aliases_track_open', 'bilder') . '/' . $QueryParameters;
      case 'web_browser':
        $QueryParameters =
          Core::EncryptURLNumber($ArrayQueryParameters['CampaignID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['SubscriberID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['EmailID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['HistoryID']) . 'z' .
          $ArrayQueryParameters['Preview'] . 'z' .
          EncryptURLSignature_web_browser_v2 . 'z' . EncryptURLHash_web_browser;
        return $APP_URL . variable_get('klicktipp_aliases_web_browser', 'web') . '/' . $QueryParameters;
      case 'opt_confirm':
        $QueryParameters =
          Core::EncryptURLNumber($ArrayQueryParameters['ListID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['SubscriberID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['ListFormID']) . 'z' .
          EncryptURLSignature_opt_confirm_v1 . 'z' .
          MinihashCreate($ArrayQueryParameters['ListID'], $ArrayQueryParameters['SubscriberID']);
        return $APP_URL . variable_get('klicktipp_aliases_opt_confirm', 'bestaetigen') . '/' . $QueryParameters;
      case 'bounce':
        return
          Core::EncryptURLNumber($ArrayQueryParameters['EmailID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['UserID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['SubscriberID']) . 'z' .
          Core::EncryptURLNumber($ArrayQueryParameters['CampaignID']) . 'z' .
          EncryptURLSignature_bounce_v1 . 'z' . EncryptURLHash_bounce;
    }
  }

  // this is the original encrypt using "base64_encode", which is not case insensitive
  // with this, we can test "old tracking links" that are still contained in emails
  function OldEncryptURL($ArrayQueryParameters, $Skript, $APP_URL = APP_URL) {
    $Query = '';
    // try to shorten url to a minimum by coding of position and needed values only
    switch ($Skript) {
      case 'opt_confirm':
        // given parameters may be: SubscriberID, ListID, FormID
        $QueryParameters = 'otcf-' .
          (empty($ArrayQueryParameters['ListID']) ? '' : base_convert($ArrayQueryParameters['ListID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['SubscriberID']) ? '' : base_convert($ArrayQueryParameters['SubscriberID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['ListFormID']) ? '' : $ArrayQueryParameters['ListFormID']) . '-' .
          MinihashCreate($ArrayQueryParameters['ListID'], $ArrayQueryParameters['SubscriberID']);
        $Query = $APP_URL . variable_get('klicktipp_aliases_opt_confirm', 'bestaetigen') . '/' . rawurlencode(base64_encode($QueryParameters));
        break;
      case 'track_link':
        // given parameters may be: RevisionID, Linknumber, CampaignID, SubscriberID, Preview
        $QueryParameters = 'tklk-' .
          (empty($ArrayQueryParameters['RevisionID']) ? '' : base_convert($ArrayQueryParameters['RevisionID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['Linknumber']) ? '' : base_convert($ArrayQueryParameters['Linknumber'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['CampaignID']) ? '' : base_convert($ArrayQueryParameters['CampaignID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['SubscriberID']) ? '' : base_convert($ArrayQueryParameters['SubscriberID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['EmailID']) ? '' : base_convert($ArrayQueryParameters['EmailID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['HTML']) ? '' : $ArrayQueryParameters['HTML']) . '-' .
          (empty($ArrayQueryParameters['Preview']) ? '' : $ArrayQueryParameters['Preview']);
        $Query = $APP_URL . variable_get('klicktipp_aliases_track_link', 'info') . '/' . rawurlencode(base64_encode($QueryParameters));
        break;
      case 'track_sign_link':
        // same as track_link, but based on list signature
        $QueryParameters = 'tksl-' .
          (empty($ArrayQueryParameters['RevisionID']) ? '' : base_convert($ArrayQueryParameters['RevisionID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['Linknumber']) ? '' : base_convert($ArrayQueryParameters['Linknumber'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['CampaignID']) ? '' : base_convert($ArrayQueryParameters['CampaignID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['SubscriberID']) ? '' : base_convert($ArrayQueryParameters['SubscriberID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['EmailID']) ? '' : base_convert($ArrayQueryParameters['EmailID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['HTML']) ? '' : $ArrayQueryParameters['HTML']) . '-' .
          (empty($ArrayQueryParameters['Preview']) ? '' : $ArrayQueryParameters['Preview']);
        $Query = $APP_URL . variable_get('klicktipp_aliases_track_link', 'info') . '/' . rawurlencode(base64_encode($QueryParameters));
        break;
      case 'track_open':
        // given parameters may be: CampaignID, EmailID, SubscriberID, Preview
        $QueryParameters = 'tkko-' .
          (empty($ArrayQueryParameters['CampaignID']) ? '' : base_convert($ArrayQueryParameters['CampaignID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['EmailID']) ? '' : base_convert($ArrayQueryParameters['EmailID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['SubscriberID']) ? '' : base_convert($ArrayQueryParameters['SubscriberID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['Preview']) ? '' : $ArrayQueryParameters['Preview']);
        $Query = $APP_URL . variable_get('klicktipp_aliases_track_open', 'bilder') . '/' . rawurlencode(base64_encode($QueryParameters));
        break;
      case 'unsubscribe':
      case 'spam_report':
        // given parameters may be: CampaignID, SubscriberID, ListID, Preview, EmailID
        $QueryParameters = 'unsb-' .
          (empty($ArrayQueryParameters['ListID']) ? ($ArrayQueryParameters['ListID'] === 0 ? '0' : '') : base_convert($ArrayQueryParameters['ListID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['SubscriberID']) ? '' : base_convert($ArrayQueryParameters['SubscriberID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['CampaignID']) ? '' : base_convert($ArrayQueryParameters['CampaignID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['EmailID']) ? '' : base_convert($ArrayQueryParameters['EmailID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['Preview']) ? '' : $ArrayQueryParameters['Preview']) . '-' .
          MinihashCreate($ArrayQueryParameters['EmailID'], $ArrayQueryParameters['SubscriberID'])
          . '-' . '1'; // suppress trailing = (which will become lengthy %3D with rawurlencode) TODO: use trim('=') instead

        $code = rawurlencode(base64_encode($QueryParameters));

        if ($Skript == 'spam_report') {
          $Query = $APP_URL . variable_get('klicktipp_aliases_spam_report', 'spam') . '/' . $code;
        }
        else {
          if (empty($APP_URL)) {
            $Query = $code;
          }
          else {
            $Query = $APP_URL . variable_get('klicktipp_aliases_unsubscribe', 'abmelden') . '/' . $code;
          }
        }
        break;
      case 'web_browser':
        // given parameters may be: CampaignID, EmailID, SubscriberID, Preview
        $QueryParameters = 'webr-' .
          (empty($ArrayQueryParameters['CampaignID']) ? '' : base_convert($ArrayQueryParameters['CampaignID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['EmailID']) ? '' : base_convert($ArrayQueryParameters['EmailID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['SubscriberID']) ? '' : base_convert($ArrayQueryParameters['SubscriberID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['Preview']) ? '' : $ArrayQueryParameters['Preview']);
        $Query = $APP_URL . variable_get('klicktipp_aliases_web_browser', 'web') . '/' . rawurlencode(base64_encode($QueryParameters));
        break;
      case 'api_key':
        // given parameters may be: UserID, BuildID
        $QueryParameters = 'apik-' .
          (empty($ArrayQueryParameters['BuildID']) ? '' : base_convert($ArrayQueryParameters['BuildID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['UserID']) ? '' : base_convert($ArrayQueryParameters['UserID'], 10, 36)) . '-' .
          SimplehashCreate($ArrayQueryParameters['BuildID'], $ArrayQueryParameters['UserID']);
        $Query = rawurlencode(trim(base64_encode($QueryParameters), '='));
        break;
      case 'subscriber_secret':
        // given parameters may be: UserID, SubscriberID
        $QueryParameters = 'usid-' .
          (empty($ArrayQueryParameters['UserID']) ? '' : base_convert($ArrayQueryParameters['UserID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['SubscriberID']) ? '' : base_convert($ArrayQueryParameters['SubscriberID'], 10, 36)) . '-' .
          MinihashCreate($ArrayQueryParameters['UserID'], $ArrayQueryParameters['SubscriberID']);
        $Query = rawurlencode(trim(base64_encode($QueryParameters), '='));
        break;
      case 'change_email':
        // given parameters may be: UserID, EmailAddress
        $QueryParameters = 'mail-' .
          (empty($ArrayQueryParameters['UserID']) ? '' : base_convert($ArrayQueryParameters['UserID'], 10, 36)) . '-' .
          (empty($ArrayQueryParameters['EmailAddress']) ? '' : str_replace('-', '%2D', $ArrayQueryParameters['EmailAddress']) . '-' .
            sha1($ArrayQueryParameters['UserID'] . MINIHASHSECRET . $ArrayQueryParameters['EmailAddress']));
        $Query = $APP_URL . variable_get('klicktipp_aliases_change_email', 'change-email') . '/' . rawurlencode(trim(base64_encode($QueryParameters), '='));
        break;
      default: // ignore
        $Query = '';
        break;
    }
    return $Query;
  }

  function testCore() {

    /*
     * Minihash
     */
    $message = 'Minihash';
    // loop to check some random values
    for ($i = 0; $i < 1000; $i++) {
      $ListID = $i;
      $Minihash = MinihashCreate($ListID);
      if (!MinihashCheck($Minihash, $ListID)) {
        $this->assertTrue(FALSE, "$message $ListID $Minihash");
      }
    }
    // show last
    $this->assertTrue(MinihashCheck($Minihash, $ListID), "$message $ListID $Minihash");

    // empty fails
    $this->assertFalse(MinihashCheck('', $ListID), "$message empty hash");

    /*
     * Simplehash
     */
    $message = 'Simplehash';
    $ListID = 4711;
    $Simplehash = SimplehashCreate($ListID);
    $this->assertTrue(SimplehashCheck($Simplehash, $ListID), "$message $ListID $Simplehash");

    $message = "is_klicktipp_email_address";
    foreach (KLICKTIPP_EMAIL_DOMAINS as $klicktippEmailDomain) {
      $email = 'test@' . $klicktippEmailDomain;
      $this->assertTrue(is_klicktipp_email_address($email), "$message $email true");
    }
    foreach (['<EMAIL>', 'invalid-email'] as $email) {
      $this->assertFalse(is_klicktipp_email_address($email), "$message $email false");
    }


    /*
     * create some test data
     */
    // create subscriber list
    $message = 'create test data';
    $UserID = 1;
    $ArrayFieldAndValues = array(
      'Name' => 'The List',
      'RelOwnerUserID' => $UserID,
    );
    $ListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($ListID > 0, $message . ' list');
    $ArraySubscriberList = Lists::RetrieveListByID($UserID, $ListID);
    $SomeFantasyTagID = 1147;
    // create subscriber
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID = $Result[1];
    $AwesomeHistoryID = 998877;

    // create campaign
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'CampaignName' => 'Test campaign 1',
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_CAMPAIGN,
    );
    $CampaignID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($CampaignID > 0, $message . ' campaign');
    $ArrayCampaign = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);
    // email
    $EmailID = $ArrayCampaign['RelEmailID'];
    $this->assertTrue($EmailID > 0, $message . ' email');
    $LinkText = 'some link text';
    $LinkTitle = 'some link text';
    $Link = APP_URL;
    /** @var Emails $ObjectEmail */
    $ObjectEmail = Emails::FromID($UserID, $EmailID);
    $ArrayEmail = $ObjectEmail->GetData();
    $ArrayEmail['HTMLContent'] = 'test with <a href="http://www.example.com">two</a> links to check URL ' . l($LinkText, $Link, array('attributes' => array('title' => $LinkTitle))) . ' with some more text';
    $ObjectEmail->UpdateDB($ArrayEmail);
    $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
    $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);
    $this->assertTrue(!empty($ArrayRevision), $message . ' email revision');
    // get link number
    $ArrayContent = unserialize($ArrayRevision['HTMLContent']);
    $Linknumber = 1;
    $this->assertTrue($ArrayContent[$Linknumber]['Link'] == $Link, $message . ' link number' . print_r($ArrayContent, 1));
    // signature
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'SignatureName' => 'SomeSignature',
      'HTMLSignatureText' => 'test with <a href="http://www.example.com">three</a> links to <a href="http://www.example.com">check</a> URL ' . l($LinkText, $Link, array('attributes' => array('title' => $LinkTitle))) . ' with some more text',
      'RelTagIDs' => array($ListID),
    );
    $NewSignatureID = Signatures::InsertDB($ArrayFieldAndValues);
    $NewRevisionID = Signatures::CreateRevision($UserID, $NewSignatureID);
    $Signature = Signatures::FindSignatureByTag($ArrayEmail, array($ListID));
    $this->assertTrue($Signature['SignatureID'] == $NewSignatureID, $message . ' signature');
    $SignatureRevision = Signatures::RetrieveLatestRevision($UserID, $NewSignatureID);
    $this->assertTrue($SignatureRevision['RevisionID'] == $NewRevisionID, $message . ' revision');
    // get link number
    $ArrayContent = unserialize($SignatureRevision['HTMLSignature']);
    $SignatureLinknumber = 2;
    $this->assertTrue($ArrayContent[$SignatureLinknumber]['Link'] == $Link, $message . ' signature link number' . print_r($ArrayContent, 1));

    /*
     $p = "53n5zz2nlbz1ybsfz1zz3z3";
     $Encrypted = explode('z', strtolower($p));
     $Parameters = array(
     'p1' => Core::DecryptURLNumber($Encrypted[0], TRUE),
     'p2' => Core::DecryptURLNumber($Encrypted[1], TRUE),
     'p3' => Core::DecryptURLNumber($Encrypted[2], TRUE),
     'p4' => Core::DecryptURLNumber($Encrypted[3], TRUE),
     'p5' => Core::DecryptURLNumber($Encrypted[4], TRUE),
     );
     $this->assertTrue(FALSE, print_r($Encrypted,1));
     $this->assertTrue(FALSE, print_r($Parameters,1));
     return;
     */
    /*
     *  Enycrpt/Decrypt thankyou
     */
    $message = 'Enycrpt/Decrypt thankyou without reference';

    $ArrayQueryParameters = array(
      'uid' => 123456,
      'lid' => 98765,
      'sid' => 9876543210,
    );
    $hash = $this->EncryptURLWithoutReference($ArrayQueryParameters, 'thankyou');
    $hash = str_replace(APP_URL . 'thankyou/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['uid'] == $ArrayQueryParameters['uid'], $message . ' decrypted UserID ' . $hash);
    $this->assertTrue($values['lid'] == $ArrayQueryParameters['lid'], $message . ' decrypted ListID');
    $this->assertTrue($values['sid'] == $ArrayQueryParameters['sid'], $message . ' decrypted SubscriberID');
    $this->assertTrue($values['rid'] === 0, $message . ' default (0) reference id');

    $message = 'Enycrpt/Decrypt thankyou lid = 0';
    $ArrayQueryParameters = array(
      'uid' => 123456,
      'lid' => 0, // imported
      'sid' => 9876543210,
      'rid' => 123
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'thankyou');
    $hash = str_replace(APP_URL . 'thankyou/', '', $hash);
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['sid'] == $ArrayQueryParameters['sid'], $message . ' decrypted sid');
    $this->assertTrue($values['lid'] == $ArrayQueryParameters['lid'], $message . ' decrypted lid');
    $this->assertTrue($values['rid'] == $ArrayQueryParameters['rid'], $message . ' decrypted rid');

    $message = 'Enycrpt/Decrypt thankyou sid = 0';
    $ArrayQueryParameters = array(
      'uid' => 123456,
      'lid' => 9876,
      'sid' => 0,
      'rid' => 3333
      // there is code like "show thankyou even if subscription failed"
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'thankyou');
    $hash = str_replace(APP_URL . 'thankyou/', '', $hash);
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['sid'] == $ArrayQueryParameters['sid'], $message . ' decrypted sid');
    $this->assertTrue($values['lid'] == $ArrayQueryParameters['lid'], $message . ' decrypted lid');
    $this->assertTrue($values['rid'] == $ArrayQueryParameters['rid'], $message . ' decrypted rid');

    $message = 'Enycrpt/Decrypt pending';

    $hash = Core::EncryptURL($ArrayQueryParameters, 'pending');
    $hash = str_replace(APP_URL . 'pending/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['uid'] == $ArrayQueryParameters['uid'], $message . ' decrypted UserID ' . $hash);
    $this->assertTrue($values['lid'] == $ArrayQueryParameters['lid'], $message . ' decrypted ListID');
    $this->assertTrue($values['sid'] == $ArrayQueryParameters['sid'], $message . ' decrypted SubscriberID');
    $this->assertTrue($values['rid'] == $ArrayQueryParameters['rid'], $message . ' decrypted rid');

    $message = 'Enycrpt/Decrypt pending (without reference id)';

    $hash = $this->EncryptURLWithoutReference($ArrayQueryParameters, 'pending');
    $hash = str_replace(APP_URL . 'pending/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['uid'] == $ArrayQueryParameters['uid'], $message . ' decrypted UserID ' . $hash);
    $this->assertTrue($values['lid'] == $ArrayQueryParameters['lid'], $message . ' decrypted ListID');
    $this->assertTrue($values['sid'] == $ArrayQueryParameters['sid'], $message . ' decrypted SubscriberID');
    $this->assertTrue($values['rid'] === 0, $message . ' default (0) rid');

    /*
     *  Enycrpt/Decrypt opt_confirm
     */

    // new link
    $message = 'Enycrpt/Decrypt opt_confirm (new)';
    $ListIDA = 4711;
    $SubscriberIDA = 987654321;
    $ArrayQueryParameters = array(
      'ListID' => $ListIDA,
      'SubscriberID' => $SubscriberIDA,
    );
    $hash = $this->EncryptURLWithoutReference($ArrayQueryParameters, 'opt_confirm');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_opt_confirm', 'bestaetigen') . '/', '', $hash);
    $this->assertTrue(($nl = strlen($hash)) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['ListID'] == $ListIDA, $message . ' decrypted ListID' . " length new $nl " . $hash);
    $this->assertTrue($values['SubscriberID'] == $SubscriberIDA, $message . ' decrypted SubscriberID');
    $this->assertTrue(isset($values['ListFormID']) && $values['ListFormID'] == '', $message . ' decrypted ListFormID');
    $this->assertTrue($values['ReferenceID'] === 0, $message . ' default (0) ReferenceID');

    // new link with ListFormID
    $message = 'Enycrpt/Decrypt opt_confirm (new with ListFormID and ReferenceID)';
    $SomeFantasyListFormID = 10;
    $ArrayQueryParameters = array(
      'ListID' => $ListID,
      'SubscriberID' => $SubscriberID,
      'ListFormID' => $SomeFantasyListFormID,
      'ReferenceID' => rand(1, 100)
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'opt_confirm');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_opt_confirm', 'bestaetigen') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['ListID'] == $ListID, $message . ' decrypted ListID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ListFormID'] == $SomeFantasyListFormID, $message . ' decrypted Mode');
    $this->assertTrue($values['ReferenceID'] == $ArrayQueryParameters['ReferenceID'], $message . ' decrypted ReferenceID');

    /*
     *  Enycrpt/Decrypt track_link
     */

    // new link
    $ArrayQueryParameters = array(
      'RevisionID' => $ArrayRevision['RevisionID'],
      'Linknumber' => $Linknumber,
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'EmailID' => $EmailID,
      'HTML' => '1',
      'Preview' => '',
    );
    $hash = $this->EncryptURLWithoutReference($ArrayQueryParameters, 'track_link');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_track_link', 'info') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');
    $this->assertTrue($values['LinkURL'] == $Link, $message . ' decrypted LinkURL');
    $this->assertTrue($values['LinkTitle'] == $LinkTitle, $message . ' decrypted LinkTitle');
    $this->assertTrue($values['ReferenceID'] == 0, $message . ' default (0) reference id');

    // new link with reference id
    $message = 'Enycrpt/Decrypt track_link with reference_id';
    $ArrayQueryParameters = array(
      'RevisionID' => $ArrayRevision['RevisionID'],
      'Linknumber' => $Linknumber,
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'EmailID' => $EmailID,
      'HTML' => '1',
      'Preview' => '',
      'ReferenceID' => rand(1, 100),
    );

    $hash = Core::EncryptURL($ArrayQueryParameters, 'track_link');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_track_link', 'info') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');
    $this->assertTrue($values['LinkURL'] == $Link, $message . ' decrypted LinkURL');
    $this->assertTrue($values['LinkTitle'] == $LinkTitle, $message . ' decrypted LinkTitle');
    $this->assertTrue($values['ReferenceID'] == $ArrayQueryParameters['ReferenceID'], $message . ' decrypted reference id');
    /*
     *  Enycrpt/Decrypt short link (track_link)
     */

    // new link
    $ArrayQueryParameters = array(
      'RevisionID' => $ArrayRevision['RevisionID'],
      'Linknumber' => $Linknumber,
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'EmailID' => $EmailID,
      'HTML' => '1',
      'Preview' => '',
    );
    $LongLink = Core::EncryptURL($ArrayQueryParameters, 'track_link');

    $ArrayQueryParameters['ShortLink'] = 1;
    $ShortLink = Core::EncryptURL($ArrayQueryParameters, 'track_link');

    $this->assertTrue(strpos($ShortLink, variable_get('klicktipp_short_link_domain', APP_URL . 's')) === 0, $message . ' short domain');
    $hash = str_replace(variable_get('klicktipp_short_link_domain', APP_URL . 's') . '/', '', $ShortLink);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $thelink = Core::DecryptShortLink($hash);
    $this->assertTrue($thelink = $LongLink, $message . ' decrypted link');

    /*
     *  Enycrpt/Decrypt track_sign_link
     */

    // new link
    $ArrayQueryParameters = array(
      'RevisionID' => $SignatureRevision['RevisionID'],
      'Linknumber' => $SignatureLinknumber,
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'EmailID' => $EmailID,
      'HTML' => '1',
      'Preview' => '',
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'track_sign_link');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_track_link', 'info') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');
    $this->assertTrue($values['LinkURL'] == $Link, $message . ' decrypted LinkURL');
    $this->assertTrue($values['LinkTitle'] == $LinkTitle, $message . ' decrypted LinkTitle');

    /*
     *  Enycrpt/Decrypt track_open
     */

    // new
    $message = 'Enycrpt/Decrypt track_open new (without reference id)';
    $ArrayQueryParameters = array(
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'Preview' => '',
    );
    $hash = $this->EncryptURLWithoutReference($ArrayQueryParameters, 'track_open');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_track_open', 'bilder') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] === 0, $message . ' default (0) ReferenceID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');

    $message = 'Enycrpt/Decrypt track_open new (with reference id)';
    $ArrayQueryParameters['ReferenceID'] = rand(1, 100);
    $hash = Core::EncryptURL($ArrayQueryParameters, 'track_open');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_track_open', 'bilder') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] == $ArrayQueryParameters['ReferenceID'], $message . ' decrypted ReferenceID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');

    /*
     *  Enycrpt/Decrypt web_browser
     */

    $message = 'Enycrpt/Decrypt web_browser new (without reference id) ';
    $ArrayQueryParameters = array(
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'EmailID' => $EmailID,
      'HistoryID' => $AwesomeHistoryID,
      'ReferenceID' => rand(1, 100),
      'Preview' => '',
    );
    $hash = $this->EncryptURLWithoutReference($ArrayQueryParameters, 'web_browser');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_web_browser', 'web') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['HistoryID'] == $AwesomeHistoryID, $message . ' decrypted HistoryID');
    $this->assertTrue($values['ReferenceID'] === 0, $message . ' default (0) ReferenceID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');

    $message = 'Enycrpt/Decrypt web_browser new (with reference id) ';
    $hash = Core::EncryptURL($ArrayQueryParameters, 'web_browser');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_web_browser', 'web') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['HistoryID'] == $AwesomeHistoryID, $message . ' decrypted HistoryID');
    $this->assertTrue($values['ReferenceID'] == $ArrayQueryParameters['ReferenceID'], $message . ' default (0) ReferenceID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');

    /*
     *  Enycrpt/Decrypt unsubscribe
     */

    // new
    $message = 'Enycrpt/Decrypt unsubscribe (new without reference and email address)';
    $ArrayQueryParameters = array(
      'ListID' => $ListID,
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'EmailID' => $EmailID,
      'Preview' => '',
    );
    $hash = $this->EncryptURLWithoutReference($ArrayQueryParameters, 'unsubscribe');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_unsubscribe', 'abmelden') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['ListID'] == $ListID, $message . ' decrypted ListID');
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] === 0, $message . ' default (0) ReferenceID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');
    $this->assertTrue($values['EmailAddressHash'] == 0, $message . ' decrypted email address hash');

    // new with listid 0 (imported subscriber)
    $message = 'Enycrpt/Decrypt unsubscribe (new) listid===0';
    $ArrayQueryParameters = array(
      'ListID' => 0,
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'ReferenceID' => rand(1, 100),
      'EmailID' => $EmailID,
      'Preview' => '',
      'EmailAddress' => $EmailAddress
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'unsubscribe');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_unsubscribe', 'abmelden') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue(empty($values['ListID']), $message . ' decrypted ListID');
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] == $ArrayQueryParameters['ReferenceID'], $message . ' decrypted ReferenceID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');
    $this->assertTrue($values['EmailAddressHash'] == Core::HashEmail($EmailAddress), $message . ' decrypted email address hash');

    /*
     *  Enycrpt/Decrypt spam_report
     */

    $message = 'Enycrpt/Decrypt spam_report (without reference)';
    $ArrayQueryParameters = array(
      'ListID' => $ListID,
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'EmailID' => $EmailID,
      'Preview' => '',
    );
    $hash = $this->EncryptURLWithoutReference($ArrayQueryParameters, 'spam_report');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_spam_report', 'spam') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['ListID'] == $ListID, $message . ' decrypted ListID');
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] === 0, $message . ' default (0) ReferenceID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');

    // with listid 0 (imported subscriber)
    $message = 'Enycrpt/Decrypt spam_report listid===0';
    $ArrayQueryParameters = array(
      'ListID' => 0,
      'CampaignID' => $CampaignID,
      'SubscriberID' => $SubscriberID,
      'ReferenceID'  => rand(1, 100),
      'EmailID' => $EmailID,
      'Preview' => '',
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'spam_report');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_spam_report', 'spam') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue(empty($values['ListID']), $message . ' decrypted ListID');
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] == $ArrayQueryParameters['ReferenceID'], $message . ' decrypted ReferenceID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');

    /*
     *  Enycrpt/Decrypt api_key
     */

    $message = 'Enycrpt/Decrypt api_key (old)';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'BuildID' => 999888777666555444,
    );
    $hash = self::OldEncryptURL($ArrayQueryParameters, 'api_key');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['BuildID'] == $ArrayQueryParameters['BuildID'], $message . ' decrypted BuildID');

    $message = 'Enycrpt/Decrypt api_key';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'BuildID' => 999888777666555444,
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'api_key');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['BuildID'] == $ArrayQueryParameters['BuildID'], $message . ' decrypted BuildID');

    /*
     *  Enycrpt/Decrypt subscriber_secret
     */

    $message = 'Enycrpt/Decrypt subscriber_secret';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'SubscriberID' => 1174,
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'subscriber_secret');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['SubscriberID'] == $ArrayQueryParameters['SubscriberID'], $message . ' decrypted SubscriberID');

    /*
     *  Enycrpt/Decrypt subscriber_secret
     */

    $message = 'Enycrpt/Decrypt subscriber_key ReferenceID = 0';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'SubscriberID' => 1174,
      'ReferenceID' => 0
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'subscriber_key');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['SubscriberID'] == $ArrayQueryParameters['SubscriberID'], $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] == $ArrayQueryParameters['ReferenceID'], $message . ' decrypted ReferenceID');

    $message = 'Enycrpt/Decrypt subscriber_key ReferenceID = 1';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'SubscriberID' => 1174,
      'ReferenceID' => 1
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'subscriber_key');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['SubscriberID'] == $ArrayQueryParameters['SubscriberID'], $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] == $ArrayQueryParameters['ReferenceID'], $message . ' decrypted ReferenceID');

    /*
     *  Enycrpt/Decrypt change_email
     */

    $message = 'Enycrpt/Decrypt change_email';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'EmailAddress' => '<EMAIL>',
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'change_email');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_change_email', 'change-email') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['EmailAddress'] == $ArrayQueryParameters['EmailAddress'], $message . ' decrypted EmailAddress');

    $message = 'Enycrpt/Decrypt change_email with email address containing "-"';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'EmailAddress' => '<EMAIL>',
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'change_email');
    $hash = str_replace(APP_URL . variable_get('klicktipp_aliases_change_email', 'change-email') . '/', '', $hash);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['EmailAddress'] == $ArrayQueryParameters['EmailAddress'], $message . ' decrypted EmailAddress');

    /*
     *  Enycrpt/Decrypt bounce
     */

    $message = 'Enycrpt/Decrypt bounce (without reference)';
    $ArrayQueryParameters = array(
      'EmailID' => 1234567890,
      'UserID' => 4711,
      'SubscriberID' => 8888,
      'CampaignID' => 9999,
    );
    $hash = $this->EncryptURLWithoutReference($ArrayQueryParameters, 'bounce');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['EmailID'] == $ArrayQueryParameters['EmailID'], $message . ' decrypted EmailID');
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['SubscriberID'] == $ArrayQueryParameters['SubscriberID'], $message . ' decrypted SubscriberID');
    $this->assertTrue($values['CampaignID'] == $ArrayQueryParameters['CampaignID'], $message . ' decrypted CampaignID');
    $this->assertTrue($values['ReferenceID'] === 0, $message . ' default (0) ReferenceID');
    $this->assertTrue($values['PhoneNumberHash'] === 0, $message . ' default (0) PhoneNumberHash');

    $message = 'Enycrpt/Decrypt bounce (with reference and phone number)';
    $ArrayQueryParameters = array(
      'EmailID' => 1234567890,
      'UserID' => 4711,
      'SubscriberID' => 8888,
      'CampaignID' => 9999,
      'ReferenceID' => rand(0, 100),
      'PhoneNumberHash' => Core::HashPhoneNumber('001762317458')
    );
    $hash = Core::EncryptURL($ArrayQueryParameters, 'bounce');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['EmailID'] == $ArrayQueryParameters['EmailID'], $message . ' decrypted EmailID');
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['SubscriberID'] == $ArrayQueryParameters['SubscriberID'], $message . ' decrypted SubscriberID');
    $this->assertTrue($values['CampaignID'] == $ArrayQueryParameters['CampaignID'], $message . ' decrypted CampaignID');
    $this->assertTrue($values['ReferenceID'] == $ArrayQueryParameters['ReferenceID'], $message . ' decrypted ReferenceID');
    $this->assertTrue($values['PhoneNumberHash'] == $ArrayQueryParameters['PhoneNumberHash'], $message . ' decrypted PhoneNumberHash');

    /*
     *  Enycrpt/Decrypt download
     */

    $message = 'Enycrpt/Decrypt download';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'URI' => 'export/test.csv',
    );
    $url = Core::EncryptURL($ArrayQueryParameters, 'download');
    $hash = str_replace(APP_URL . 'download/', '', $url);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == 4711, $message . ' decrypted UserID');
    $this->assertTrue($values['URI'] == 'export/test.csv', $message . ' decrypted URI');

    /*
     *  Enycrpt/Decrypt conversion
     */

    $message = 'Enycrpt/Decrypt conversion';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'CampaignID' => 4712,
    );
    $url = Core::EncryptURL($ArrayQueryParameters, 'conversion');
    $hash = str_replace(APP_URL . 'pic/', '', $url);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['CampaignID'] == $ArrayQueryParameters['CampaignID'], $message . ' decrypted CampaignID');

    $message = 'Enycrpt/Decrypt conversion w/ amount';
    $values = Core::DecryptURL($hash . '/1');
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID int');
    $this->assertTrue($values['CampaignID'] == $ArrayQueryParameters['CampaignID'], $message . ' decrypted CampaignID int');
    $values = Core::DecryptURL($hash . '/47,11');
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID comma');
    $this->assertTrue($values['CampaignID'] == $ArrayQueryParameters['CampaignID'], $message . ' decrypted CampaignID comma');
    $values = Core::DecryptURL($hash . '/47.11');
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID point');
    $this->assertTrue($values['CampaignID'] == $ArrayQueryParameters['CampaignID'], $message . ' decrypted CampaignID point');

    /*
     *  Enycrpt/Decrypt taggingpixel
     */

    $message = 'Enycrpt/Decrypt taggingpixel';
    $ArrayQueryParameters = array(
      'UserID' => 4711,
      'ToolID' => 4712,
    );
    $url = Core::EncryptURL($ArrayQueryParameters, 'taggingpixel');
    $hash = str_replace(APP_URL . 'pix/', '', $url);
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == $ArrayQueryParameters['UserID'], $message . ' decrypted UserID');
    $this->assertTrue($values['ToolID'] == $ArrayQueryParameters['ToolID'], $message . ' decrypted ToolID');

    /*
     *  Enycrpt/Decrypt pixelcookie
     */

    $message = 'Enycrpt/Decrypt pixelcookie';
    $ArrayQueryParameters = array(4711, 4712, 4713);
    $hash = Core::EncryptURL($ArrayQueryParameters, 'pixelcookie');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue(count($values) == count($ArrayQueryParameters), $message . ' decrypted count');
    $this->assertTrue($values[1] == $ArrayQueryParameters[1], $message . ' decrypted values');

    $message = 'Enycrpt/Decrypt pixelcookie empty';
    $ArrayQueryParameters = array();
    $hash = Core::EncryptURL($ArrayQueryParameters, 'pixelcookie');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue(empty($values), $message . ' decrypted count');
    $this->assertTrue(is_array($values), $message . ' decrypted values');

    /*
     *  Enycrpt/Decrypt timed_user_hash
     */

    $message = 'Enycrpt/Decrypt timed_user_hash';
    $time = time();
    $ArrayQueryParameters = ['UserID' => 4711, 'time' => $time];
    $hash = Core::EncryptURL($ArrayQueryParameters, 'timed_user_hash');
    $this->assertTrue(strlen($hash) > 0, $message . ' encrypted');
    $values = Core::DecryptURL($hash);
    $this->assertTrue($values['UserID'] == 4711, $message . ' decrypted UserID');
    $this->assertTrue($values['time'] == $time, $message . ' decrypted time');
    $this->assertTrue($values['intime'], $message . ' decrypted intime');

    /*
     * Decrypt bad input
     */

    // no slash in input
    $badInput = "kjhkuhuhu";
    Core::DecryptURL($badInput);
    $records = db_query("SELECT * FROM {watchdog} ORDER BY wid DESC LIMIT 1")->fetchAll();
    $variables = $records[0]->variables;
    $pos = strpos($variables, ':"args";a:1:{');
    $this->assertTrue(strpos($variables, ':"args";a:1:{i:0;s:' . strlen($badInput) . ':"' . $badInput . '";}') !== FALSE,
      $message . " Core::DecryptURL() with bad input preserves original query param (1): " . substr($variables, $pos, 255) . "...");

    // with slash in input
    $badInput = "kjhkuhuhu/?q=2";
    Core::DecryptURL($badInput);
    $records = db_query("SELECT * FROM {watchdog} ORDER BY wid DESC LIMIT 1")->fetchAll();
    $variables = $records[0]->variables;
    $pos = strpos($variables, ':"args";a:1:{');
    $this->assertTrue(strpos($variables, ':"args";a:1:{i:0;s:' . strlen($badInput) . ':"' . $badInput . '";}') !== FALSE,
      $message . " Core::DecryptURL() with bad input preserves original query param (2): " . substr($variables, $pos, 255) . "...");

    /*
     *  GenerateSMTPMessageID ExtractSMTPMessageID
     */
    $message = 'GenerateSMTPMessageID/ExtractSMTPMessageID';
    $ReferenceID = 42;
    $MessageID = Core::GenerateSMTPMessageID(time(), $EmailID, $SubscriberID, $ReferenceID, $UserID, $CampaignID);
    $this->assertTrue(strlen($MessageID) > 0, $message . ' encrypted');

    $values = Core::ExtractSMTPMessageID($MessageID);
    $this->assertTrue($values['UserID'] == $UserID, $message . ' decrypted UserID');
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] == $ReferenceID, $message . ' decrypted ReferenceID');

    // without padding
    $values1 = Core::ExtractSMTPMessageID("MTM5NTMxNDY0NTUzMmFjZmQ1MGI0NzQsNDcxMSw0NzE0LDQ3MTMsNDcxMg==");
    $values2 = Core::ExtractSMTPMessageID("MTM5NTMxNDY0NTUzMmFjZmQ1MGI0NzQsNDcxMSw0NzE0LDQ3MTMsNDcxMg");
    $this->assertTrue($values1['UserID'] == $values2['UserID'], $message . ' decrypted UserID padding');
    $this->assertTrue($values1['CampaignID'] == $values2['CampaignID'], $message . ' decrypted CampaignID padding');
    $this->assertTrue($values1['EmailID'] == $values2['EmailID'], $message . ' decrypted EmailID padding');
    $this->assertTrue($values1['SubscriberID'] == $values2['SubscriberID'], $message . ' decrypted SubscriberID padding');

    /*
     *  GenerateAbuseMessageID ExtractAbuseMessageID
     */
    $message = 'GenerateAbuseMessageID/ExtractAbuseMessageID';
    $EmailAddress = '<EMAIL>';
    $MessageID = Core::GenerateAbuseMessageID($EmailID, $SubscriberID, $ReferenceID, $EmailAddress, $UserID, $CampaignID);
    $this->assertTrue(strlen($MessageID) > 0, $message . ' encrypted');
    $values = Core::ExtractAbuseMessageID($MessageID);
    $this->assertTrue($values['UserID'] == $UserID, $message . ' decrypted UserID');
    $this->assertTrue($values['EmailAddress'] == $EmailAddress, $message . ' decrypted EmailAddress');
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] == $ReferenceID, $message . ' decrypted ReferenceID');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');

    /*
     *  GenerateReturnPathCode ExtractReturnPathCode
     */
    $message = 'GenerateReturnPathCode/ExtractReturnPathCode';
    $ReturnPathCode = Core::GenerateReturnPathCode($EmailID, $EmailAddress, $SubscriberID, $ReferenceID, $UserID, $CampaignID);
    $this->assertTrue(strlen($ReturnPathCode) > 0, $message . ' encrypted');
    $ReturnEmailAddress = 'bounce-' . $ReturnPathCode . '@' . variable_get('klicktipp_shared_return_path', BOUNCE_CATCHALL_DOMAIN);
    $this->assertTrue(strlen($ReturnEmailAddress) > 0, $message . ' address is ' . $ReturnEmailAddress);
    $values = Core::ExtractReturnPathCode($ReturnEmailAddress);
    $this->assertTrue($values['UserID'] == $UserID, $message . ' decrypted UserID');
    $this->assertTrue($values['CampaignID'] == $CampaignID, $message . ' decrypted CampaignID');
    $this->assertTrue($values['EmailID'] == $EmailID, $message . ' decrypted EmailID');
    $this->assertTrue($values['SubscriberID'] == $SubscriberID, $message . ' decrypted SubscriberID');
    $this->assertTrue($values['ReferenceID'] == $ReferenceID, $message . ' decrypted ReferenceIDp');
    $this->assertTrue($values['EmailAddressHash'] == Core::HashEmail($EmailAddress), $message . ' decrypted EmailAddressHash');
    $this->assertTrue($values['Preview'] == '', $message . ' decrypted Preview');

    /*
     * DatabaseTableWithData
     */
    $message = 'DatabaseTableWithData::DataUnserialize';

    $DBArray = array(
      'Field1Level0' => '1-0-fromdb',
      'Data' => serialize(array(
        'Field1Level1' => '1-1-fromdb',
        'Field2Level1' => array(
          'Field2Level2' => array(
            'Field1Level3' => '1-3-fromdb',
          ),
        )
      )),
    );
    $flat = TestDatabaseTableWithData::DataUnserialize($DBArray);
    $this->assertTrue($flat['Field1Level0'] == '1-0-fromdb', $message . ' Field1Level0'.print_r($flat,1));
    $this->assertTrue($flat['Field1Level1'] == '1-1-fromdb', $message . ' Field1Level1');
    $this->assertTrue($flat['Field2Level1']['Field1Level2'] == '1-2', $message . ' Field1Level2');
    $this->assertTrue($flat['Field2Level1']['Field2Level2']['Field1Level3'] == '1-3-fromdb', $message . ' Field1Level3');
    $this->assertTrue($flat['Field2Level1']['Field2Level2']['Field2Level3'] == '2-3', $message . ' Field2Level3');
    $this->assertTrue($flat['Field3Level1'] == '3-1', $message . ' Field3Level1');

  }
}

class TestDatabaseTableWithData extends DatabaseTableWithData {

  public static $DefaultDataFields = array(
    'Field1Level1' => '1-1',
    'Field2Level1' => array(
      'Field1Level2' => '1-2',
      'Field2Level2' => array(
        'Field1Level3' => '1-3',
        'Field2Level3' => '2-3',
      ),
    ),
    'Field3Level1' => '3-1',
  );

  public static function FromArray($DBArray) {

    $tool = new TestDatabaseTableWithData($DBArray);

    if (empty($tool->DataFlat)) {
      return FALSE;
    }

    return $tool;
  }

}

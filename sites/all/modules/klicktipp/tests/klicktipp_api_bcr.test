<?php

use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DatabaseTableLabeled;
use App\Klicktipp\Event;
use App\Klicktipp\KlickTippAPIUtils;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\Signatures;
use App\Klicktipp\Subaccount;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\VarSalesEmployee;
use App\Security\Keycloak\KeycloakApi;
use App\Security\Oidc\OidcConfigurationProvider;
use App\Security\Oidc\OidcInternalAccessTokenProvider;

class klicktippAPIBCRTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'klicktipp API BCR',
      'description' => 'Test all API BCR functions.',
      'group' => 'Klicktipp',
    );

  }

  function testBCRTags() {

    /* load API functions */
    function services_error($message, $code = 0, $data = NULL) {
      return FALSE;
    }

    Libraries::include('api_bcr.inc', '/api');
    Libraries::include('full_user.inc', '/tests/includes');

    $ReferenceID = 0;

    // the subaccount needs the permission 'use developer key' so assign a role that reflects that
    $salesemployee_role = user_role_load_by_name(ROLENAME_SALESEMPLOYEE);

    // create a user with api access
    $superaccountPass = 'U5nATY2f';
    $superaccount = user_save(NULL, array(
      'name' => 'apibcruser',
      'FirstName' => 'FirstnameAPI',
      'LastName' => 'LastnameAPI',
      'mail' => '<EMAIL>',
      'pass' => $superaccountPass,
      'roles' => array(
        $salesemployee_role->rid => $salesemployee_role->rid,
      ),
    ));

    global $user;
    $user = $superaccount;
    $SubAccountID = $superaccount->uid; // api_resource works on global user, so we should too

    $account = user_load_by_name(USERNAME_ENTERPRISE);
    $MainAccountID = $account->uid;

    $message = 'prepare data';

    $bcrKeyUserID = 4092;
    $bcrKey = Subaccount::CreateDeveloperKey($bcrKeyUserID);

    variable_set('listbuilding_bcrapp_login_timeout', 86400);
    variable_set('listbuilding_bcrapp_bcrkey_account_id', $bcrKeyUserID);

    // create subaccount
    Subaccount::InsertDB(array(
      'RelOwnerUserID' => $SubAccountID,
      'SubAccountID' => $MainAccountID,
      'AgencyAccess' => 0,
      'SubaccountType' => Subaccount::SUBACCOUNT_API_USER,
      'IsOwnAccount' => Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_NONE,
    ));

    $subaccount = Subaccount::FromID($SubAccountID, $MainAccountID)->GetData();
    $this->assertTrue(!empty($subaccount), $message.' add subaccount');
    $this->assertTrue($subaccount['RelOwnerUserID'] == $SubAccountID, $message . ' UserID set');
    $this->assertTrue($subaccount['SubAccountID'] == $MainAccountID, $message . ' SubAccountID set');
    $this->assertTrue(empty($subaccount['AgencyAccess']), $message . ' AgencyAccess set');
    $this->assertTrue($subaccount['SubaccountType'] == Subaccount::SUBACCOUNT_API_USER, $message . ' SubaccountType set');
    $this->assertTrue($subaccount['IsOwnAccount'] == Subaccount::SUBACCOUNT_ISOWNACCOUNT_STATUS_NONE, $message . ' IsOwnAccount set');

    // create deep link parameters
    $deepLinkParameters = Event::CreateDeepLinkParameters($MainAccountID, $SubAccountID);
    $decoded = drupal_json_decode(base64_decode($deepLinkParameters));
    $customerKey = $decoded['customer_key'];

    // generate ciphertext1st
    $hmac = hash_hmac('sha256', $decoded['customer_key'], pack('H*', $bcrKey), TRUE);
    $ciphertext1st = base64_encode($hmac . $customerKey);

    $TagName1 = 'some tag name';
    $TagID1 = Tag::CreateManualTag($MainAccountID, $TagName1, '');
    $this->assertTrue($TagID1 > 0, $message . ' tag 1');

    $FirstNameFieldInformation = CustomFields::RetrieveCustomField('FirstName');
    $DefaultList = Lists::RetrieveDefaultList($MainAccountID);
    // create event
    $EventFieldAndValues = array(
      'Name' => 'Test Event 1',
      'RelOwnerUserID' => $MainAccountID,
      DatabaseTableLabeled::LABEL_DATA_KEY => [],
      'Datetime' => time(),
      'EndDatetime' => time(),
      'FollowUpTime' => 604800,
      'RelListID' => $DefaultList['ListID'],
      'HideSubscribers' => FALSE,
      'GlobalCustomFields' => [
        $FirstNameFieldInformation['CustomFieldID'] => $FirstNameFieldInformation['CustomFieldID'],
      ],
      'CustomFields' => [],
      'Tags' => [
        $TagID1 => $TagID1,
      ],
      'SalesEmployees' => [],
    );
    $BuildID = Event::InsertDB($EventFieldAndValues);
    $this->assertTrue($BuildID > 0, "$message Event created");
    $ObjectListbuildingEvent = Event::FromID($MainAccountID, $BuildID);
    $this->assertTrue(!empty($ObjectListbuildingEvent), "$message Event object");

    // create 2nd event (w/o Tags)
    $EventFieldAndValues2 = array(
      'Name' => 'Test Event 2',
      'RelOwnerUserID' => $MainAccountID,
      DatabaseTableLabeled::LABEL_DATA_KEY => [],
      'Datetime' => time(),
      'EndDatetime' => time(),
      'FollowUpTime' => 604800,
      'RelListID' => $DefaultList['ListID'],
      'HideSubscribers' => FALSE,
      'GlobalCustomFields' => [
        $FirstNameFieldInformation['CustomFieldID'] => $FirstNameFieldInformation['CustomFieldID'],
      ],
      'CustomFields' => [],
      'Tags' => [],
      'SalesEmployees' => [],
    );
    $BuildID2 = Event::InsertDB($EventFieldAndValues2);
    $this->assertTrue($BuildID2 > 0, "$message Event created");

    /*
     * _klicktippapi_resource_bcr_deep_link_login
     */
    $message = '_klicktippapi_resource_bcr_deep_link_login';

    $this->setRequestHeader("X-UN", $superaccount->name);
    $this->setRequestHeader("X-CI", $ciphertext1st);

    $result = _klicktippapi_resource_bcr_deep_link_login($decoded['timestamp'], $decoded['hash'], TRUE);
    $this->assertTrue($result['developer_key'] == Subaccount::CreateDeveloperKey($SubAccountID), $message . ' developer key');

    // generate ciphertext
    $hmac = hash_hmac('sha256', $customerKey, pack('H*', $result['developer_key']), TRUE);
    $ciphertext = base64_encode($hmac . $customerKey);

    $this->setRequestHeader("X-UN", $superaccount->name);
    $this->setRequestHeader("X-CI", $ciphertext);

    /*
     * _klicktippapi_resource_event_index
     */
    $message = '_klicktippapi_resource_event_index';

    $result = (array) _klicktippapi_resource_event_index();
    $this->assertTrue(empty($result), $message . ' no events');

    // update event with subaccount id
    $ObjectEvent = Event::FromID($MainAccountID, $BuildID);
    $this->assertTrue(!empty($ObjectEvent), $message . ' event retrieved');
    $Data = $ObjectEvent->GetData();
    $Data['SalesEmployees'] = [
      $SubAccountID => $SubAccountID,
    ];
    $ObjectEvent->UpdateDB($Data);

    $result = (array) _klicktippapi_resource_event_index();
    $this->assertTrue(count($result) == 1, $message . ' event returned');
    $this->assertTrue($result[$BuildID]['id'] == $BuildID, $message . ' event id');
    $this->assertTrue($result[$BuildID]['name'] == $EventFieldAndValues['Name'], $message . ' event name');
    $this->assertTrue($result[$BuildID]['listid'] == $EventFieldAndValues['RelListID'], $message . ' event listid');
    $this->assertTrue($result[$BuildID]['date'] == $EventFieldAndValues['Datetime'], $message . ' event date');
    $this->assertTrue($result[$BuildID]['enddate'] == $EventFieldAndValues['EndDatetime'], $message . ' event end date');
    $this->assertTrue($result[$BuildID]['followuptime'] == $EventFieldAndValues['FollowUpTime'], $message . ' event follow up time');

    // update event with enddate in the past
    $ObjectEvent = Event::FromID($MainAccountID, $BuildID);
    $this->assertTrue(!empty($ObjectEvent), $message . ' event retrieved');
    $Data = $ObjectEvent->GetData();
    $Data['Datetime'] = time() - 604980;
    $Data['EndDatetime'] = time() - 604860;
    $ObjectEvent->UpdateDB($Data);

    $result = (array) _klicktippapi_resource_event_index();
    $this->assertTrue(empty($result), $message . ' no event returned');

    /*
     * _klicktippapi_resource_event_retrieve
     */
    $message = '_klicktippapi_resource_event_retrieve';

    $result = _klicktippapi_resource_event_retrieve($BuildID);
    $this->assertTrue(empty($result), $message . ' no event found as no sales employee exists');

    // create sales employee
    VarSalesEmployee::InsertEmployee($MainAccountID, $SubAccountID, array(
      'Name' => 'Sales Employee',
      'EmailAddress' => '<EMAIL>',
      'MobilePhone' => '',
      'AssignTagID' => $TagID1,
    ));

    $result = _klicktippapi_resource_event_retrieve($BuildID);
    $this->assertTrue(!empty($result), $message . ' event returned');
    $this->assertTrue($result->id == $BuildID, $message . ' event id');
    $this->assertTrue($result->name == $EventFieldAndValues['Name'], $message . ' event name');
    $this->assertTrue($result->listid == $EventFieldAndValues['RelListID'], $message . ' event listid');
    $this->assertTrue($result->date == $Data['Datetime'], $message . ' event date');
    $this->assertTrue($result->enddate == $Data['EndDatetime'], $message . ' event end date');
    $this->assertTrue($result->followuptime == $Data['FollowUpTime'], $message . ' event follow up time');
    $this->assertTrue($result->hide_subscribers == $EventFieldAndValues['HideSubscribers'], $message . ' event hide_subscribers');
    $this->assertTrue($result->fields == array(
      "field{$FirstNameFieldInformation['CustomFieldID']}" => array(
        "id" => $FirstNameFieldInformation['CustomFieldID'],
        "name" => t($FirstNameFieldInformation['FieldName']),
        "type" => CustomFields::$APIIndexFilterTypes[$FirstNameFieldInformation['FieldTypeEnum']],
      ),
    ), $message . ' event fields');
    $this->assertTrue($result->personal_tagid == $TagID1, $message . ' event personal_tagid');
    $result->tags = (array) $result->tags;
    $this->assertTrue($result->tags == array(
      $TagID1 => $TagName1,
    ), $message . ' event tags');

    // set regex
    $expectedRegex = array(
      "email" => "\b[\w.!#$%&’*+\/=?^`{|}~-]+@[\w-]+(?:\.[\w-]+)*\b",
      "sms_phone" => "^(?:[0-9]●?){6,14}[0-9]$",
      "fieldFirstName" => "/\P{L}/u",
      "fieldLastName" => "/\P{L}/u",
      "fieldCompanyName" => "^.*(gbr|e.k.|ohg|kg|gmbh|ag|mbh|limited|ltd)$.*$",
      "fieldStreet1" => "^.*(straße|strasse|weg|allee|chaussee|gasse|platz).*$",
      "fieldStreet2" => "^.*(straße|strasse|weg|allee|chaussee|gasse|platz).*$",
      "fieldCity" => "/\P{L}/u",
      "fieldState" => "/\P{L}/u",
      "fieldZip" => "^[0-9]{5}(?:-[0-9]{4})?$",
      "fieldCountry" => "/\P{L}/u",
      "fieldPrivatePhone" => "^(?:[0-9]●?){6,14}[0-9]$",
      "fieldMobilePhone" => "^(?:[0-9]●?){6,14}[0-9]$",
      "fieldPhone" => "^(?:[0-9]●?){6,14}[0-9]$",
      "fieldFax" => "^(?:[0-9]●?){6,14}[0-9]$",
      "fieldWebsite" => "^(((https?):\/\/)?([\w\-\.])+(\.)([\w]){2,4}([\w\/+=%&_\.~?\-]*))*$",
    );
    variable_set('listbuilding_bcrapp_regex', drupal_json_encode($expectedRegex));

    /*
     * _klicktippapi_resource_bcr_regex
     */
    $message = '_klicktippapi_resource_bcr_regex';

    $regex = _klicktippapi_resource_bcr_regex();
    $this->assertTrue($regex == $expectedRegex, $message . ' regex are returned');

    /*
     * KlickTippAPIUtils::GetSubscriptionValidationErrors
     */
    $message = 'KlickTippAPIUtils::GetSubscriptionValidationErrors';

    // prepare data
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $MainAccountID,
      'FieldName' => 'email field',
      'FieldTypeEnum' => CustomFields::TYPE_EMAIL,
    );
    $EmailCustomFieldID = CustomFields::Create($ArrayFieldAndValues);

    $ArraySubscriberList = Lists::RetrieveDefaultList($MainAccountID);

    $Parameters = array(
      'UserID' => $MainAccountID,
      'ListInformation' => $ArraySubscriberList,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    );

    // Double Optin - Subscribed
    $EmailAddress = '<EMAIL>';
    $Parameters['EmailAddress'] = $EmailAddress;
    $Parameters['OptInSubscribeTo'] = $TagID1;
    $Parameters['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $subscriberid = $Result[1];

    // SMS
    $EmailAddressSMS = '<EMAIL>';
    $SMSPhonenumber = '*************';
    $Parameters['PhoneNumber'] = $SMSPhonenumber;
    $Parameters['EmailAddress'] = $EmailAddressSMS;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber SMS');
    $SubscriberIDSMS = $Result[1];

    // this is the user defined field
    $EmailCustomFieldInformation = CustomFields::RetrieveCustomField($EmailCustomFieldID, $MainAccountID);
    $this->assertTrue(is_array($EmailCustomFieldInformation), $message . ' email field exists');

    // test _klicktippapi_resource_bcr_subscriber_create
    $before_count = get_confirmation_email_count();
    $result = _klicktippapi_resource_bcr_subscriber_create('<EMAIL>', '****************', 0, 0, []);
    $this->assertTrue(!empty($result), $message . ' created'.print_r([$result, $ArraySubscriberList], 1));
    $this->assertTrue($result->status == Subscribers::$DisplaySubscriptionStatus[Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED], $message . ' subscribed');
    check_confirmation_email_count($this, $message, $before_count);

    $data = [
      'email' => 'noemailaddress.com',
      'smsnumber' => '*************', // not a sms number,
      'tagid' => 12345, // tag does not exist in this account
    ];
    $fields = [
      'fieldBirthday' => '01.01.1980', // not a timestamp
      'fieldLeadValue' => 'leadValue', // not numeric
      'fieldWebsite' => 'www.klick-tipp', // not a well-defined url
      'field' . $EmailCustomFieldID => $data['email'],
    ];

    $ArrayOtherFields = array();
    foreach ($fields as $key => $f) {
      if (strpos($key, 'field') === 0) {
        $newkey = str_replace('field', 'CustomField', $key);
        $ArrayOtherFields[$newkey] = $f;
      }
    }
    // Case: malformed data
    $Parameters = array(
      'UserID' => $MainAccountID,
      'ListInformation' => $ArraySubscriberList,
      'OptInSubscribeTo' => $data['tagid'],
      'EmailAddress' => $data['email'],
      'PhoneNumber' => $data['smsnumber'],
      'IPAddress' => '0.0.0.0 - By API Request',
      'SubscriptionReferrer' => '',
      'OtherFields' => $ArrayOtherFields,
      'UpdateIfUnsubscribed' => TRUE,
      'SendConfirmationEmail' => TRUE,
      'UpdateStatistics' => TRUE,
      'TriggerAutoResponders' => TRUE,
    );
    $result = _klicktippapi_resource_bcr_subscriber_create($data['email'], $data['smsnumber'], $ArraySubscriberList['ListID'], $data['tagid'], $fields);
    $this->assertTrue(empty($result), $message . ' not subscribed, because tag does not exist');
    $result = _klicktippapi_resource_bcr_subscriber_update($subscriberid, $fields, $data['email'], $data['smsnumber']);
    $this->assertTrue(empty($result), $message . ' not updated');

    $subscribeError = KlickTippAPIUtils::GetSubscriptionValidationErrors($Parameters, KlickTippAPIUtils::VALIDATION_ON_SUBSCRIBE);
    $this->assertTrue(!empty($subscribeError['errors']), $message . ' errors were provided');
    $this->assertTrue(count($subscribeError['errors']) == count($data) + count($fields), $message . ' ' . count($subscribeError['errors']).  ' errors detected');

    $Parameters['SubscriberID'] = $subscriberid;
    $updateError = KlickTippAPIUtils::GetSubscriptionValidationErrors($Parameters, KlickTippAPIUtils::VALIDATION_ON_UPDATE);
    $this->assertTrue(!empty($updateError['errors']), $message . ' errors were provided');
    $this->assertTrue(count($updateError['errors']) == count($data) + count($fields), $message . ' ' . count($updateError['errors']).  ' errors detected');

    // Case: Event has expired
    $SubscriptionInformation = Subscribers::RetrieveFullSubscriber($MainAccountID, $SubscriberIDSMS, $ReferenceID);
    $fields = [
      'fieldBCRAPIKey' => Core::EncryptURL([
        'UserID' => $MainAccountID,
        'BuildID' => $BuildID,
      ], 'api_key'),
    ];
    $ArrayOtherFields = array();
    foreach ($fields as $key => $f) {
      if (strpos($key, 'field') === 0) {
        $newkey = str_replace('field', 'CustomField', $key);
        $ArrayOtherFields[$newkey] = $f;
      }
    }
    $Parameters = array(
      'UserID' => $MainAccountID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $SubscriptionInformation['EmailAddress'],
      'PhoneNumber' => $SubscriptionInformation['PhoneNumber'],
      'IPAddress' => '0.0.0.0 - By API Request',
      'SubscriptionReferrer' => '',
      'OtherFields' => $ArrayOtherFields,
      'UpdateIfUnsubscribed' => TRUE,
      'SendConfirmationEmail' => TRUE,
      'UpdateStatistics' => TRUE,
      'TriggerAutoResponders' => TRUE,
    );
    $result = _klicktippapi_resource_bcr_subscriber_create($SubscriptionInformation['EmailAddress'], $SubscriptionInformation['PhoneNumber'], $ArraySubscriberList['ListID'], 0, $fields);
    $this->assertTrue(empty($result), $message . ' not subscribed (event has expired)');
    $result = _klicktippapi_resource_bcr_subscriber_update($subscriberid, $fields, $SubscriptionInformation['EmailAddress'], $SubscriptionInformation['PhoneNumber']);
    $this->assertTrue(empty($result), $message . ' not updated');

    // update event with enddate in the future
    $ObjectEvent = Event::FromID($MainAccountID, $BuildID);
    $this->assertTrue(!empty($ObjectEvent), $message . ' event retrieved');
    $Data = $ObjectEvent->GetData();
    $Data['Datetime'] = time();
    $Data['EndDatetime'] = time() + 180;
    $ObjectEvent->UpdateDB($Data);

    //to check: no confirmation email sent
    $before_count = get_confirmation_email_count();

    // Case: Already existing email address and phone number
    $result = _klicktippapi_resource_bcr_subscriber_create($SubscriptionInformation['EmailAddress'], $SubscriptionInformation['PhoneNumber'], $ArraySubscriberList['ListID'], 0, $fields);
    $this->assertTrue(!empty($result), $message . ' subscribed');
    $this->assertTrue(!empty(Subscribers::RetrieveTagging($MainAccountID, $ObjectEvent->GetData('SmartTagID'), $SubscriptionInformation['SubscriberID'], $ReferenceID)), $message . ' smart tag');
    check_confirmation_email_count($this, $message, $before_count);
    $result = _klicktippapi_resource_bcr_subscriber_update($subscriberid, $fields, $SubscriptionInformation['EmailAddress'], $SubscriptionInformation['PhoneNumber']);
    $this->assertTrue(empty($result), $message . ' not updated');

    $subscribeError = KlickTippAPIUtils::GetSubscriptionValidationErrors($Parameters, KlickTippAPIUtils::VALIDATION_ON_SUBSCRIBE);
    $this->assertTrue(empty($subscribeError['errors']), $message . ' no error');

    $Parameters['SubscriberID'] = $subscriberid;
    $updateError = KlickTippAPIUtils::GetSubscriptionValidationErrors($Parameters, KlickTippAPIUtils::VALIDATION_ON_UPDATE);
    $expectedError = [
      0 => [
        'name' => 'newemail',
        'reason' => t('subscriber with this email address already exists'),
      ],
      1 => [
        'name' => 'newsmsnumber',
        'reason' => t('subscriber with this smsnumber already exists'),
      ],
    ];
    $this->assertTrue($updateError['errors'] == $expectedError, $message . ' error matches');

    // Case: Unsubscribed email address and phone number and parameters set like api create/update does
    $result = Subscribers::Unsubscribe($MainAccountID, $SubscriptionInformation['EmailAddress']);
    $this->assertTrue($result[0], $message . ' email unsubscribed');
    $result = Subscribers::UnsubscribeSMSNumber($MainAccountID, $SubscriptionInformation['PhoneNumber']);
    $this->assertTrue($result[0], $message . ' sms unsubscribed');
    Subscribers::UntagSubscriber($MainAccountID, $SubscriptionInformation['SubscriberID'], $ReferenceID, $ObjectEvent->GetData('SmartTagID'));
    $this->assertTrue(empty(Subscribers::RetrieveTagging($MainAccountID, $ObjectEvent->GetData('SmartTagID'), $SubscriptionInformation['SubscriberID'], $ReferenceID)), $message . ' smart tag not exists');

    $Parameters = array(
      'UserID' => $MainAccountID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $SubscriptionInformation['EmailAddress'],
      'PhoneNumber' => $SubscriptionInformation['PhoneNumber'],
      'IPAddress' => '0.0.0.0 - By API Request',
      'SubscriptionReferrer' => '',
      'OtherFields' => $ArrayOtherFields,
      'UpdateIfUnsubscribed' => TRUE,
      'UpdatePhoneNumberIfUnsubscribed' => FALSE,
      'SendConfirmationEmail' => TRUE,
      'UpdateStatistics' => TRUE,
      'TriggerAutoResponders' => TRUE,
    );
    $result = _klicktippapi_resource_bcr_subscriber_create($SubscriptionInformation['EmailAddress'], $SubscriptionInformation['PhoneNumber'], $ArraySubscriberList['ListID'], 0, $fields);
    $this->assertTrue(empty($result), $message . ' not subscribed (subscriber is unsubscribed from sms)');
    $result = _klicktippapi_resource_bcr_subscriber_update($SubscriptionInformation['SubscriberID'], $fields, $SubscriptionInformation['EmailAddress'], $SubscriptionInformation['PhoneNumber']);
    $this->assertTrue(!empty($result), $message . ' updated');
    $this->assertTrue(!empty(Subscribers::RetrieveTagging($MainAccountID, $ObjectEvent->GetData('SmartTagID'), $SubscriptionInformation['SubscriberID'], $ReferenceID)), $message . ' smart tag');

    $subscribeError = KlickTippAPIUtils::GetSubscriptionValidationErrors($Parameters, KlickTippAPIUtils::VALIDATION_ON_SUBSCRIBE);
    $expectedError = [
      0 => [
        'name' => 'smsnumber',
        'reason' => t('subscriber is unsubscribed from sms'),
      ],
    ];
    $this->assertTrue($subscribeError['errors'] == $expectedError, $message . ' error matches');

    $Parameters['SubscriberID'] = $SubscriptionInformation['SubscriberID'];
    $Parameters['UpdatePhoneNumberIfUnsubscribed'] = TRUE;
    $updateError = KlickTippAPIUtils::GetSubscriptionValidationErrors($Parameters, KlickTippAPIUtils::VALIDATION_ON_UPDATE);
    $this->assertTrue(empty($updateError['errors']), $message . ' no error');

    // Case: Unsubscribed email address and phone number (with parameters that are not the behaviour of api create/update)
    $Parameters['SubscriberID'] = 0;
    $Parameters['UpdateIfUnsubscribed'] = FALSE;
    $Parameters['UpdatePhoneNumberIfUnsubscribed'] = FALSE;

    $subscribeError = KlickTippAPIUtils::GetSubscriptionValidationErrors($Parameters, KlickTippAPIUtils::VALIDATION_ON_SUBSCRIBE);
    $expectedError = [
      0 => [
        'name' => 'smsnumber',
        'reason' => t('subscriber is unsubscribed from sms'),
      ],
    ];
    $this->assertTrue($subscribeError['errors'] == $expectedError, $message . ' error matches');

    $Parameters['SubscriberID'] = $SubscriptionInformation['SubscriberID'];
    $updateError = KlickTippAPIUtils::GetSubscriptionValidationErrors($Parameters, KlickTippAPIUtils::VALIDATION_ON_UPDATE);
    $expectedError = [
      0 => [
        'name' => 'newsmsnumber',
        'reason' => t('subscriber is unsubscribed from sms'),
      ],
    ];
    $this->assertTrue($updateError['errors'] == $expectedError, $message . ' error matches');

    /*
     * _klicktippapi_resource_subscriber_search
     */
    $message = '_klicktippapi_resource_bcr_subscriber_search';

    $result = _klicktippapi_resource_bcr_subscriber_search($EmailAddress);
    $this->assertTrue($result == $subscriberid, $message . ' id');

    $result = _klicktippapi_resource_bcr_subscriber_search('', $SMSPhonenumber);
    $this->assertTrue($result == $SubscriberIDSMS, $message . ' id');

    $result = _klicktippapi_resource_bcr_subscriber_search($EmailAddress, $SMSPhonenumber);
    $this->assertTrue($result == $subscriberid, $message . ' id');

    $result = _klicktippapi_resource_bcr_subscriber_search("<EMAIL>", $SMSPhonenumber);
    $this->assertTrue($result == $SubscriberIDSMS, $message . ' id');

    /*
     * _klicktippapi_resource_bcr_subscriber_retrieve
     */
    $message = '_klicktippapi_resource_bcr_subscriber_retrieve';

    $result = _klicktippapi_resource_bcr_subscriber_retrieve($subscriberid, "does-not-exists");
    $this->assertTrue(empty($result), $message . ' not found (wrong api key)');

    // W/ Manual Tags

    $apikey = Core::EncryptURL([
      'UserID' => $MainAccountID,
      'BuildID' => $BuildID,
    ], 'api_key');

    $result = _klicktippapi_resource_bcr_subscriber_retrieve(PHP_INT_MAX, $apikey);
    $this->assertTrue(empty($result), $message . ' not found (subscriber id does not exits)');

    // retrieve subscriber that is tagged with the tagid assigned to the event
    $Subscriber = Subscribers::RetrieveFullSubscriberWithFields($MainAccountID, $subscriberid, $ReferenceID);

    $result = _klicktippapi_resource_bcr_subscriber_retrieve($subscriberid, $apikey);
    $this->assertTrue(!empty($result), $message . ' SMS result' . print_r($result, 1));
    $this->assertTrue($result->id == $Subscriber['SubscriberID'], $message . ' SMS id');
    $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $Subscriber['EmailAddress']), $message . ' SMS email');
    $this->assertTrue($result->bounce == 'Not Bounced', $message . ' SMS bounce');
    $this->assertTrue($result->status == 'Subscribed', $message . ' SMS status');
    $this->assertTrue($result->{$FirstNameFieldInformation['CustomFieldID']} == $Subscriber["CustomField{$FirstNameFieldInformation['CustomFieldID']}"], $message . ' firstname field');
    $this->assertTrue($result->tags == array(
        0 => $TagID1,
      ), $message . ' event tags');
    $result->manual_tags = (array) $result->manual_tags;
    $this->assertTrue(array_keys($result->manual_tags) == array($TagID1), $message . ' event manual tags');

    // W/O Manual Tags

    $apikey = Core::EncryptURL([
      'UserID' => $MainAccountID,
      'BuildID' => $BuildID2
    ], 'api_key');

    $result = _klicktippapi_resource_bcr_subscriber_retrieve($subscriberid, $apikey);
    $this->assertTrue(!empty($result), $message . ' SMS result' . print_r($result, 1));
    $this->assertTrue($result->id == $Subscriber['SubscriberID'], $message . ' SMS id');
    $this->assertTrue(Subscribers::IsSameEmailAddress($result->email, $Subscriber['EmailAddress']), $message . ' SMS email');
    $this->assertTrue($result->bounce == 'Not Bounced', $message . ' SMS bounce');
    $this->assertTrue($result->status == 'Subscribed', $message . ' SMS status');
    $this->assertTrue($result->{$FirstNameFieldInformation['CustomFieldID']} == $Subscriber["CustomField{$FirstNameFieldInformation['CustomFieldID']}"], $message . ' firstname field');
    $this->assertTrue(empty($result->tags), $message . ' event tags');
    $this->assertTrue(empty((array)$result->manual_tags), $message . ' event manual tags');

    /*
     * _klicktippapi_resource_bcr_login
     */
    $message = '_klicktippapi_resource_bcr_login';

    $container = ServiceContainer::getInstance();
    $container->set(
      KeycloakApi::class,
      new class(
        $container->get(OidcConfigurationProvider::class),
        $container->get(OidcInternalAccessTokenProvider::class),
      ) extends KeycloakApi {
        public function __construct(
          OidcConfigurationProvider $configurationProvider,
          OidcInternalAccessTokenProvider $accessTokenProvider
        ) {
          parent::__construct(
            $configurationProvider,
            $accessTokenProvider,
            '',
            '',
            '',
          );
        }
        public function createUser(
          string $username,
          string $email,
          string $firstName,
          string $lastName,
          bool $emailVerified = TRUE
        ): string {
          return '';
        }
        public function updateUser(
          string $userId,
          array $data
        ): void {
        }
      },
    );

    $account = user_load_by_name('User1');

    $result = _klicktippapi_resource_bcr_login($account->name, 'klicktipp');
    $this->assertTrue(!empty($result['deep_link_parameters']), $message . ' deep link parameters');
    $this->assertTrue($result['deep_link_parameters']['username'] == $account->name, $message . ' username');
    $this->assertTrue(!empty($result['deep_link_parameters']['customer_key']), $message . ' customer key');
    $this->assertTrue(!empty($result['deep_link_parameters']['timestamp']), $message . ' timestamp');
    $this->assertTrue(!empty($result['deep_link_parameters']['hash']), $message . ' hash');

    // generate ciphertext
    $developer_key = Subaccount::CreateDeveloperKey($account->uid);
    $hmac = hash_hmac('sha256', $result['deep_link_parameters']['customer_key'], pack('H*', $developer_key), TRUE);
    $ciphertext = base64_encode($hmac . $result['deep_link_parameters']['customer_key']);
    // check that generated customer_key belongs to right user
    [$partnerid, $customerid] = Subaccount::DecodeCiphertext($account->uid, $ciphertext, KLICKTIPP_SALT);
    $this->assertTrue($partnerid == $account->uid, $message . ' partnerid');
    $this->assertTrue($customerid == $account->uid, $message . ' customerid');

    VarSalesEmployee::InsertEmployee($MainAccountID, $SubAccountID, array(
      'Name' => 'Sales Employee',
      'EmailAddress' => '<EMAIL>',
      'MobilePhone' => '',
      'AssignTagID' => $TagID1,
    ));

    $SignatureData = array(
      'RelOwnerUserID' => $MainAccountID,
      'SignatureName'=> 'Some signature',
      'PlainSignatureText' => 'What Ever',
      'HTMLSignatureText' => '<p>What Ever</p>',
      'RelTagIDs' => [$TagID1 => $TagID1],
      'vCard' => [
        'FirstName' => 'FirstName',
        'LastName' => 'LastName',
        'CompanyName' => 'Some Company',
        'EmailAddress' => '<EMAIL>',
        'Phone' => '**********',
        'CellPhone' => '',
        'Street' => 'Some street 123',
        'Zip' => '12345',
        'City' => 'Some City',
        'State' => 'Some State',
        'Country' => 'Deutschland',
        'Website' => 'https://www.klick-tipp.com/',
      ],
    );
    Signatures::InsertDB($SignatureData);

    /*
     * _klicktippapi_resource_bcr_account_index
     */

    // set user back to the original subaccount user for $MainAccountID
    $user = $superaccount;

    $result = _klicktippapi_resource_bcr_account_index();
    $vCardData = (object) $SignatureData['vCard'];
    KlickTippAPIUtils::FilterObject('vCard', $vCardData);
    $this->assertEqual(
      $result,
      $vCardData,
      ' vCardData (found vCard matching dispatch profile)'
    );

  }

  private function setRequestHeader($name, $value) {
    $_SERVER["HTTP_$name"] = $value;
  }

}

<?php

use App\Klicktipp\ToolSplittestClub;
use App\Klicktipp\ToolWebsiteSplittest;

class coreapiToolsWebsiteSplittestTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'coreapi Website Splittest',
      'description' => 'Test classes/tools_website_splittest.inc',
      'group' => 'Klicktipp',
    );

  }

  function testToolsWebsiteSplittest() {

    $UserID = 1;

    // --- Create Test data ---
    $name = 'content test 2 variants';
    $message = $name;

    $ToolID = ToolWebsiteSplittest::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'Name' => $name,
      'sptype' => ToolWebsiteSplittest::SPLITTEST_TYPE_CONTENT,
      'selectionMethod' => ToolWebsiteSplittest::SPLITTEST_SELECTION_METHOD_ROUND_ROBIN,
      'variants' => array(
        0 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'A',
          'weight' => 50,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
        1 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'B',
          'weight' => 50,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
      ),
    ));
    $this->assertTrue($ToolID > 0, $message);

    /** @var ToolWebsiteSplittest $ToolObject */
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), $message . ' object');
    $cookieName = $ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_SPLIT, $ToolID);
    $cookieAction = $ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_RESPONSE, $ToolID);

    // reset and get first click
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    unset($_COOKIE[$cookieName]);
    $this->assertTrue($ToolObject->GetSplittestVariant('1') == 0, $message . ' 1');

    // reset and get second click
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    unset($_COOKIE[$cookieName]);
    $this->assertTrue($ToolObject->GetSplittestVariant('2') == 1, $message . ' 2');

    // reset and get third click
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    unset($_COOKIE[$cookieName]);
    $this->assertTrue($ToolObject->GetSplittestVariant('3') == 0, $message . ' 3');

    // get by cookie
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $this->assertTrue($ToolObject->GetSplittestVariant('2') == 0, $message . ' 2, but cookie == 3');
    // get by ip
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    unset($_COOKIE[$cookieName]);
    $this->assertTrue($ToolObject->GetSplittestVariant('2') == 0, $message . ' 2, by ip, but overridden');

    // track 2
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    unset($_COOKIE[$cookieName]);
    unset($_COOKIE[$cookieAction]);
    $ToolObject->TrackSplittestResponse('2');

    // check data
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $ArrayTool = $ToolObject->GetData();
    $this->assertTrue($ArrayTool['clicks'] === 5, $message . ' clicks'.print_r($ArrayTool,1));
    $this->assertTrue($ArrayTool['unassigned'] === 0, $message . ' unassigned');
    $this->assertTrue($ArrayTool['variants'][0]['uniques'] === 2, $message . ' 0: uniques');
    $this->assertTrue($ArrayTool['variants'][0]['actions'] === 1, $message . ' 0: actions');
    $this->assertTrue($ArrayTool['variants'][1]['uniques'] === 1, $message . ' 1: uniques');
    $this->assertTrue($ArrayTool['variants'][1]['actions'] === 0, $message . ' 1: actions');

    // track 4 (unassigned)
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    unset($_COOKIE[$cookieName]);
    unset($_COOKIE[$cookieAction]);
    $ToolObject->TrackSplittestResponse('4');

    // reset and get fourth click
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    unset($_COOKIE[$cookieName]);
    $this->assertTrue($ToolObject->GetSplittestVariant('4') == 1, $message . ' 4');

    // check data
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $ArrayTool = $ToolObject->GetData();
    $this->assertTrue($ArrayTool['clicks'] === 6, $message . ' clicks'.print_r($ArrayTool,1));
    $this->assertTrue($ArrayTool['unassigned'] === 1, $message . ' unassigned');
    $this->assertTrue($ArrayTool['variants'][0]['uniques'] === 2, $message . ' 0: uniques');
    $this->assertTrue($ArrayTool['variants'][0]['actions'] === 1, $message . ' 0: actions');
    $this->assertTrue($ArrayTool['variants'][1]['uniques'] === 2, $message . ' 1: uniques');
    $this->assertTrue($ArrayTool['variants'][1]['actions'] === 0, $message . ' 1: actions');

    // track 4 after click
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    unset($_COOKIE[$cookieName]);
    unset($_COOKIE[$cookieAction]);
    $ToolObject->TrackSplittestResponse('4');

    // check data (unchanged)
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $ArrayToolAfter = $ToolObject->GetData();
    $this->assertTrue($ArrayTool['clicks'] === $ArrayToolAfter['clicks'], $message . ' clicks'.print_r($ArrayToolAfter,1));
    $this->assertTrue($ArrayTool['unassigned'] === $ArrayToolAfter['unassigned'], $message . ' unassigned');
    $this->assertTrue($ArrayTool['variants'][0]['uniques'] === $ArrayToolAfter['variants'][0]['uniques'], $message . ' 0: uniques');
    $this->assertTrue($ArrayTool['variants'][0]['actions'] === $ArrayToolAfter['variants'][0]['actions'], $message . ' 0: actions');
    $this->assertTrue($ArrayTool['variants'][1]['uniques'] === $ArrayToolAfter['variants'][1]['uniques'], $message . ' 1: uniques');
    $this->assertTrue($ArrayTool['variants'][1]['actions'] === $ArrayToolAfter['variants'][1]['actions'], $message . ' 1: actions');

    // --- Create Test data ---
    $name = 'content test more variants';
    $message = $name;

    $ToolID = ToolWebsiteSplittest::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'Name' => $name,
      'sptype' => ToolWebsiteSplittest::SPLITTEST_TYPE_CONTENT,
      'selectionMethod' => ToolWebsiteSplittest::SPLITTEST_SELECTION_METHOD_ROUND_ROBIN,
      'variants' => array(
        0 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'A',
          'weight' => 40,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
        1 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'B',
          'weight' => 10,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
        2 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'C',
          'weight' => 12,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
        3 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'D',
          'weight' => 28,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
        4 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'E',
          'weight' => 1,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
        5 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'F',
          'weight' => 9,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
      ),
    ));
    $this->assertTrue($ToolID > 0, $message);

    /** @var ToolWebsiteSplittest $ToolObject */
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), $message . ' object');
    $cookieName = $ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_SPLIT, $ToolID);
    $cookieNameResponse = $ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_RESPONSE, $ToolID);

    // reset and get click
    $seen = array();
    for ($i = 0 ; $i < 100; $i++) {
      // reset and get click
      $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
      unset($_COOKIE[$cookieName]);
      $seen[$i] = $ToolObject->GetSplittestVariant('1.'.$i);
      // reset and get response
      $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
      unset($_COOKIE[$cookieNameResponse]);
      $ToolObject->TrackSplittestResponse('1.'.$i);
    }

    // check data
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $ArrayTool = $ToolObject->GetData();
    $this->assertTrue($ArrayTool['clicks'] === 100, $message . ' clicks'.print_r($ArrayTool,1));
    $this->assertTrue($ArrayTool['unassigned'] === 0, $message . ' unassigned');
    for ($v = 0; $v < count($ArrayTool['variants']); $v++) {
      $this->assertTrue($ArrayTool['variants'][$v]['uniques'] == $ArrayTool['variants'][$v]['weight'], "$message $v uniques");
      $this->assertTrue($ArrayTool['variants'][$v]['actions'] == $ArrayTool['variants'][$v]['weight'], "$message $v actions");
    }

    // --- Terminate ---
    $message = 'terminate';

    ToolWebsiteSplittest::TerminateSplittest($UserID, $ToolID);

    for ($i = 0 ; $i < 100; $i++) {
      // reset and get click
      $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
      unset($_COOKIE[$cookieName]);
      $s = $ToolObject->GetSplittestVariant('1.'.$i);
      $this->assertTrue($seen[$i] == $s, "$message $i: $s");
    }
    for ($i = 100 ; $i < 110; $i++) {
      // reset and get click
      $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
      unset($_COOKIE[$cookieName]);
      $s = $ToolObject->GetSplittestVariant('1.'.$i);
      $this->assertTrue($seen[$i] == $ToolObject->GetData('winner'), "$message $i: $s");
      // reset and get response
      $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
      unset($_COOKIE[$cookieNameResponse]);
      $ToolObject->TrackSplittestResponse('1.'.$i);
    }

    // check data = no changes
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $ArrayTool = $ToolObject->GetData();
    $this->assertTrue($ArrayTool['clicks'] === 100, $message . ' clicks'.print_r($ArrayTool,1));
    $this->assertTrue($ArrayTool['unassigned'] === 0, $message . ' unassigned');
    for ($v = 0; $v < count($ArrayTool['variants']); $v++) {
      $this->assertTrue($ArrayTool['variants'][$v]['uniques'] == $ArrayTool['variants'][$v]['weight'], "$message $v uniques");
      $this->assertTrue($ArrayTool['variants'][$v]['actions'] == $ArrayTool['variants'][$v]['weight'], "$message $v actions");
    }

    // --- Create Test data ---
    $name = 'content test random';
    $message = $name;

    $ToolID = ToolWebsiteSplittest::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'Name' => $name,
      'sptype' => ToolWebsiteSplittest::SPLITTEST_TYPE_CONTENT,
      'selectionMethod' => ToolWebsiteSplittest::SPLITTEST_SELECTION_METHOD_RANDOM,
      'variants' => array(
        0 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'A',
          'weight' => 25,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
        1 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'B',
          'weight' => 25,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
        2 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'C',
          'weight' => 50,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
      ),
    ));
    $this->assertTrue($ToolID > 0, $message);

    /** @var ToolWebsiteSplittest $ToolObject */
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), $message . ' object');
    $this->assertEqual(ToolWebsiteSplittest::SPLITTEST_SELECTION_METHOD_RANDOM, $ToolObject->GetData('selectionMethod'), $message . " random selection set");
    $cookieName = $ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_SPLIT, $ToolID);
    $cookieAction = $ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_RESPONSE, $ToolID);

    $counts[0] = 0;
    $counts[1] = 0;
    $counts[2] = 0;

    // reset and click
    for ($i = 1; $i <= 250; $i += 4) {
      $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
      unset($_COOKIE[$cookieName]);
      $newVariantIndex = $ToolObject->GetSplittestVariant("$i");
      $counts[$newVariantIndex]++;

      $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
      unset($_COOKIE[$cookieName]);
      $newVariantIndex = $ToolObject->GetSplittestVariant("" . ($i + 1));
      $counts[$newVariantIndex]++;

      $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
      unset($_COOKIE[$cookieName]);
      $newVariantIndex = $ToolObject->GetSplittestVariant("" . ($i + 2));
      $counts[$newVariantIndex]++;

      $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
      unset($_COOKIE[$cookieName]);
      $newVariantIndex = $ToolObject->GetSplittestVariant("" . ($i + 3));
      $counts[$newVariantIndex]++;
    }

    $this->assertTrue($counts[0] < $counts[2], $message . " 1st variant less used than 3rd: " . htmlspecialchars(print_r($counts, TRUE)));
    $this->assertTrue($counts[1] < $counts[2], $message . " 2nd variant less used than 3rd: " . htmlspecialchars(print_r($counts, TRUE)));

    // --- Create Test data ---
    $name = 'multistep targets';
    $message = $name;

    $ToolID = ToolWebsiteSplittest::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'Name' => $name,
      'sptype' => ToolWebsiteSplittest::SPLITTEST_TYPE_CONTENT,
      'selectionMethod' => ToolWebsiteSplittest::SPLITTEST_SELECTION_METHOD_ROUND_ROBIN,
      'variants' => array(
        0 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'A',
          'weight' => 50,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
        1 => array(
          'descr' => '',
          'spurl' => 'https://',
          'content' => 'B',
          'weight' => 50,
          'price' => 0,
          'uniques' => 0,
          'actions' => 0,
        ),
      ),
      'targets' => [
        0 => 'order',
        1 => 'sold',
        2 => 'upsell',
        3 => 'target4',
        4 => 'target5',
      ]
    ));
    $this->assertTrue($ToolID > 0, $message);

    /** @var ToolWebsiteSplittest $ToolObject */
    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), $message . ' object');
    $ipAddress = '*******';
    unset($_COOKIE[$ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_SPLIT, $ToolID)]);
    unset($_COOKIE[$ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_RESPONSE, $ToolID)]);
    unset($_COOKIE[$ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_TARGET2, $ToolID)]);
    unset($_COOKIE[$ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_TARGET3, $ToolID)]);
    unset($_COOKIE[$ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_TARGET4, $ToolID)]);
    unset($_COOKIE[$ToolObject->GetVisitorCookieName(ToolSplittestClub::SPLITTEST_ACTION_TARGET5, $ToolID)]);

    $versionSelected = $ToolObject->GetSplittestVariant($ipAddress);
    $this->assertTrue($versionSelected == 0, "$message versionSelected");

    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $this->assertTrue($ToolObject->GetData('variants')[0]['uniques'] == 1, "$message 0 uniques".print_r($ToolObject->GetData('variants'),1));
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions'] == 0, "$message 0 actions");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions2'] == 0, "$message 0 actions2");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions3'] == 0, "$message 0 actions3");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions4'] == 0, "$message 0 actions4");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions5'] == 0, "$message 0 actions5");
    $this->assertTrue($ToolObject->GetData('variants')[1]['uniques'] == 0, "$message 1 uniques");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions'] == 0, "$message 1 actions");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions2'] == 0, "$message 1 actions2");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions3'] == 0, "$message 1 actions3");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions4'] == 0, "$message 1 actions4");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions5'] == 0, "$message 1 actions5");

    $ToolObject->TrackSplittestResponse($ipAddress, '');

    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $this->assertTrue($ToolObject->GetData('variants')[0]['uniques'] == 1, "$message 0 uniques".print_r($ToolObject->GetData('variants'),1));
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions'] == 1, "$message 0 actions");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions2'] == 0, "$message 0 actions2");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions3'] == 0, "$message 0 actions3");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions4'] == 0, "$message 0 actions4");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions5'] == 0, "$message 0 actions5");
    $this->assertTrue($ToolObject->GetData('variants')[1]['uniques'] == 0, "$message 1 uniques");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions'] == 0, "$message 1 actions");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions2'] == 0, "$message 1 actions2");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions3'] == 0, "$message 1 actions3");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions4'] == 0, "$message 1 actions4");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions5'] == 0, "$message 1 actions5");

    $ToolObject->TrackSplittestResponse($ipAddress, 'order');

    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $this->assertTrue($ToolObject->GetData('variants')[0]['uniques'] == 1, "$message 0 uniques".print_r($ToolObject->GetData('variants'),1));
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions'] == 1, "$message 0 actions");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions2'] == 0, "$message 0 actions2");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions3'] == 0, "$message 0 actions3");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions4'] == 0, "$message 0 actions4");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions5'] == 0, "$message 0 actions5");
    $this->assertTrue($ToolObject->GetData('variants')[1]['uniques'] == 0, "$message 1 uniques");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions'] == 0, "$message 1 actions");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions2'] == 0, "$message 1 actions2");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions3'] == 0, "$message 1 actions3");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions4'] == 0, "$message 1 actions4");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions5'] == 0, "$message 1 actions5");

    $ToolObject->TrackSplittestResponse($ipAddress, 'target5');

    $ToolObject = ToolWebsiteSplittest::FromID($UserID, $ToolID);
    $this->assertTrue($ToolObject->GetData('variants')[0]['uniques'] == 1, "$message 0 uniques".print_r($ToolObject->GetData('variants'),1));
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions'] == 1, "$message 0 actions");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions2'] == 0, "$message 0 actions2");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions3'] == 0, "$message 0 actions3");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions4'] == 0, "$message 0 actions4");
    $this->assertTrue($ToolObject->GetData('variants')[0]['actions5'] == 1, "$message 0 actions5");
    $this->assertTrue($ToolObject->GetData('variants')[1]['uniques'] == 0, "$message 1 uniques");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions'] == 0, "$message 1 actions");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions2'] == 0, "$message 1 actions2");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions3'] == 0, "$message 1 actions3");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions4'] == 0, "$message 1 actions4");
    $this->assertTrue($ToolObject->GetData('variants')[1]['actions5'] == 0, "$message 1 actions5");
  }

}

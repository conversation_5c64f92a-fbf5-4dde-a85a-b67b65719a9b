<?php

use App\Klicktipp\BusinessCardReader;
use App\Klicktipp\Core;
use App\Klicktipp\Libraries;
use App\Klicktipp\Listbuildings;
use App\Klicktipp\Lists;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;

class cliBusinessCardProTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'cli Webhook BusinessCardPro',
      'description' => 'Webhook BusinessCardPro',
      'group' => 'Klicktipp',
    );

  }

  function testBusinessCardPro() {

    $UserID = 1;

    $ReferenceID = 0;

    // --- Create Test data ---
    $message = "Test init: ";

    // create double optin subscription process
    $ListFieldAndValues = array(
      'Name' => 'List',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE,
    );

    $DOIListID = Lists::InsertDB($ListFieldAndValues);
    $this->assertTrue($DOIListID > 0, "$message List created ($DOIListID)");

    // create single optin subscription process
    $ListFieldAndValues = array(
      'Name' => 'List2',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
    );

    $SOIListID = Lists::InsertDB($ListFieldAndValues);
    $this->assertTrue($SOIListID > 0, "$message List created ($SOIListID)");

    // create manual tag
    $TagID = Tag::CreateManualTag($UserID, 'Tag', '');
    $this->assertTrue($TagID > 0, "$message Tag created ($TagID)");

    // create manual tags for additional tagging
    $TagID2 = Tag::CreateManualTag($UserID, 'Tag2', '');
    $this->assertTrue($TagID2 > 0, "$message Tag created ($TagID2)");

    $TagID3 = Tag::CreateManualTag($UserID, 'Tag3', '');
    $this->assertTrue($TagID3 > 0, "$message Tag created ($TagID3)");

    //create Business Card with DOI
    $ProductFieldAndValues = array(
      'Name' => 'Test BusinessCard 1',
      'RelOwnerUserID' => $UserID,
      'RelListID' => $DOIListID,
      'AssignTagID' => $TagID,
    );

    $BuildIDDOI = BusinessCardReader::InsertDB($ProductFieldAndValues);
    $this->assertTrue($BuildIDDOI > 0, "$message BusinessCard created");
    $ObjectListbuildingCardDOI = Listbuildings::FromID($UserID, $BuildIDDOI);
    $this->assertTrue(!empty($ObjectListbuildingCardDOI), "$message Card object");

    //create Business Card with SOI
    $ProductFieldAndValues = array(
      'Name' => 'Test BusinessCard 1',
      'RelOwnerUserID' => $UserID,
      'RelListID' => $SOIListID,
      'AssignTagID' => $TagID,
    );

    $BuildIDSOI = BusinessCardReader::InsertDB($ProductFieldAndValues);
    $this->assertTrue($BuildIDSOI > 0, "$message BusinessCard created");
    $ObjectListbuildingCardSOI = Listbuildings::FromID($UserID, $BuildIDSOI);
    $this->assertTrue(!empty($ObjectListbuildingCardSOI), "$message Card object");

    // --- End Create Test data ---


    //TESTCASE: IPN connection_test
    $message = "Webhook connection_test:";
    $curl_test = array(
      'post1' => 'Webhook Test', //some data
      'post2' => 'OK', //some date
    );

    $ArrayQueryParameters = array(
      'UserID' => $UserID,
      'BuildID' => $BuildIDDOI,
    );

    $UserKey = Core::EncryptURL($ArrayQueryParameters, 'api_key');

    $Curl = $this->businesscard_send_webhook($curl_test, $UserKey, $message);
    $this->assertTrue($Curl == "OK", "$message expected answer");

    $emailAddress = "hosbach@rtüs.de";

    $TestCard = array(
      "phones" => array(
        "work" => array(
          0 => "023158449111",
        ),
      ),
      "emails" => array(
        "work" => array(
          0 => $emailAddress,
        ),
      ),
      "urls" => array(
        "work" => array(
          0 => "www.example.com",
        ),
      ),
      "addresses" => array(
        0 => array(
          "countryCode" => "DE",
          "country" => "Germany",
          "street" => "Dudenstraße 2-4",
          "zip" => "44137",
          "city" => "Dortmund",
        ),
      ),
      "companyName" => "RTS Media Reisen GmbH",
      "job" => "Geschäftsführer",
      "personFirstName" => "Max",
      "personLastName" => "Mustermann",
      "command" => "add",
    );

    //TESTCASE: live business card (Double Optin)
    $message = "Webhook BusinessCard received: (Double Optin)";
    $Curl = $this->businesscard_send_webhook($TestCard, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Card received");

    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, Subscribers::PunycodeEmailAddress($TestCard['emails']['work'][0]));
    $ExpectedValues = $TestCard;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_OPTIN;
    $ExpectedValues['RelListID'] = $DOIListID;
    $ExpectedValues['Taggings'] = array(
      $ObjectListbuildingCardDOI->GetData('AssignTagID'),
      $ObjectListbuildingCardDOI->GetData('SmartTagID'),
    );
    $ExpectedValues['urls']['work'][0] = "http://" . $ExpectedValues['urls']['work'][0];
    $this->businesscard_check_subscription($Subscriber, $ExpectedValues, $message, TRUE);

    //TESTCASE: live business card (Single Optin)
    $message = "Webhook BusinessCard received: (Single Optin)";
    $ArrayQueryParameters = array(
      'UserID' => $UserID,
      'BuildID' => $BuildIDSOI,
    );
    $UserKey = Core::EncryptURL($ArrayQueryParameters, 'api_key');

    $Curl = $this->businesscard_send_webhook($TestCard, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Card received");

    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, Subscribers::PunycodeEmailAddress($TestCard['emails']['work'][0]));
    $ExpectedValues = $TestCard;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $ExpectedValues['RelListID'] = $SOIListID;
    $ExpectedValues['Taggings'] = array(
      $ObjectListbuildingCardSOI->GetData('AssignTagID'),
      $ObjectListbuildingCardSOI->GetData('SmartTagID'),
    );
    $ExpectedValues['urls']['work'][0] = "http://" . $ExpectedValues['urls']['work'][0];
    $this->businesscard_check_subscription($Subscriber, $ExpectedValues, $message, TRUE);

    //TESTCASE: live business card additional tags (Single Optin)
    $message = "Webhook BusinessCard Additional Tags: (Single Optin)";
    $Tag2 = Tag::RetrieveTag($UserID, $TagID2);
    $Tag3 = Tag::RetrieveTag($UserID, $TagID3);
    $TestCard['notes'] = strtoupper(Tag::GetTagName($Tag2)) . ',' . strtolower(Tag::GetTagName($Tag3));
    $Curl = $this->businesscard_send_webhook($TestCard, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Card received");

    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, Subscribers::PunycodeEmailAddress($TestCard['emails']['work'][0]));
    $ExpectedValues = $TestCard;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $ExpectedValues['RelListID'] = $SOIListID;
    $ExpectedValues['Taggings'] = array(
      $TagID2,
      $TagID3,
      $ObjectListbuildingCardSOI->GetData('AssignTagID'),
      $ObjectListbuildingCardSOI->GetData('SmartTagID'),
    );
    $ExpectedValues['urls']['work'][0] = "http://" . $ExpectedValues['urls']['work'][0];
    $this->businesscard_check_subscription($Subscriber, $ExpectedValues, $message, TRUE);

    //TESTCASE: live business card with sms number transfer (Single Optin)
    $message = "Webhook BusinessCard with SMS Number Transfer: (Single Optin)";
    $TestCard['phones']['work'][0] = '00491631737743'; // update: http://www.frankgehtran.de/

    $Data = $ObjectListbuildingCardSOI->GetData();
    $Data['UseSMSNumber'] = 1; // activate sms number transfer
    $ObjectListbuildingCardSOI->UpdateDB($Data);

    $Curl = $this->businesscard_send_webhook($TestCard, $UserKey, $message);
    $this->assertTrue($Curl == 'OK', "$message Card received");

    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, Subscribers::PunycodeEmailAddress($TestCard['emails']['work'][0]));
    $ExpectedValues = $TestCard;
    $ExpectedValues['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $ExpectedValues['RelListID'] = $SOIListID;
    $ExpectedValues['Taggings'] = array(
      $ObjectListbuildingCardSOI->GetData('AssignTagID'),
      $ObjectListbuildingCardSOI->GetData('SmartTagID'),
    );
    $ExpectedValues['urls']['work'][0] = "http://" . $ExpectedValues['urls']['work'][0];
    $this->businesscard_check_subscription($Subscriber, $ExpectedValues, $message, TRUE);

    $Subscriber = Subscribers::RetrieveFullSubscriber($Subscriber['RelOwnerUserID'], $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($Subscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");
    Subscribers::UnsubscribeSMSNumber($Subscriber['RelOwnerUserID'], $Subscriber['PhoneNumber']);
    $Subscriber = Subscribers::RetrieveFullSubscriber($Subscriber['RelOwnerUserID'], $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($Subscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message SMS SubscriptionStatus unsubscribed");

  }

  function businesscard_send_webhook($post, $user_data, $message = '') {

    Libraries::include('businesscardreader.inc', '/callbacks');

    /* test callback function */

    global $callbackresult;

    if (!function_exists('test_form_redirect')) {
      function test_form_redirect($error = array()) {
        global $callbackresult;

        // simulate curl result
        if (empty($error)) {
          $callbackresult = array(TRUE, 'OK');
        }
        else {
          $callbackresult = array(FALSE, $error);
        }
      }
    }

    $decrypted_user_data = Core::DecryptURL($user_data);
    $UserID = $decrypted_user_data['UserID'];
    $BuildID = $decrypted_user_data['BuildID'];

    // caching breaks tests so we reset the cache for each call
    drupal_static_reset('business_card_posted_value');

    klicktipp_receiver_business_card($UserID, $BuildID, $post, 'test_form_redirect');

    $this->assertTrue($callbackresult[0], "$message IPN call successfull");

    return $callbackresult[1];
  }

  function businesscard_check_subscription($Subscriber, $ExpectedValues, $message, $CheckCustomFields = FALSE) {
    $ReferenceID = 0;

    $this->assertTrue($Subscriber, "$message Business Card subscribed");
    $ArraySubscriber = Subscribers::RetrieveFullSubscriberWithFields($Subscriber['RelOwnerUserID'], $Subscriber['SubscriberID'], $ReferenceID);
    $ExpectedEmailAddress = Subscribers::PunycodeEmailAddress($ExpectedValues['emails']['work'][0]);
    $this->assertTrue(Subscribers::IsSameEmailAddress($ArraySubscriber['EmailAddress'], $ExpectedEmailAddress), "$message EmailAddress " . $ExpectedEmailAddress);

    if (!empty($ExpectedValues['SubscriptionStatus'])) {
      $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == $ExpectedValues['SubscriptionStatus'], "$message Status " . $ExpectedValues['SubscriptionStatus']);
      $this->assertTrue($ArraySubscriber['RelListID'] == $ExpectedValues['RelListID'], "$message List " . $ExpectedValues['RelListID']);
    }

    //check if subscriber was tagged
    foreach ($ExpectedValues['Taggings'] as $tagid) {
      $this->assertTrue(!empty(Subscribers::RetrieveTagging($Subscriber['RelOwnerUserID'], $tagid, $Subscriber['SubscriberID'], $ReferenceID)), $message . ' Subscriber tagged with '.$tagid);
    }

    //check custom fields
    if ($CheckCustomFields) {
      $CustomFields = array(
        'FirstName' => "personFirstName",
        'LastName' => "personLastName",
        'CompanyName' => "companyName",
        'Street1' => "street",
        'City' => "city",
        'Zip' => "zip",
        'Country' => "countryCode",
        'Phone' => "phones",
        'Website' => "urls",
      );

      if (!empty(Subscribers::FormatSMSNumber($ExpectedValues['phones']['work'][0]))) {
        $this->assertTrue($ArraySubscriber['PhoneNumber'] == $ExpectedValues['phones']['work'][0], "$message SMS PhoneNumber " . $ExpectedValues['phones']['work'][0]);
      }

      foreach ($CustomFields as $CustomFieldID => $PostID) {

        switch ($CustomFieldID) {

          case "FirstName":
          case "LastName":
          case "CompanyName":
            $this->assertTrue($ArraySubscriber["CustomField$CustomFieldID"] == $ExpectedValues[$PostID], "$message CustomField $CustomFieldID = " . $ExpectedValues[$PostID] . ' [' . $ArraySubscriber["CustomField$CustomFieldID"] . ']');
            break;
          case "Country":
            $this->assertTrue($ArraySubscriber["CustomField$CustomFieldID"] == $ExpectedValues['addresses'][0][$PostID], "$message CustomField $CustomFieldID = iso3166_" . $ExpectedValues['addresses'][0][$PostID]);
            break;
          case "Phone":
            $this->assertTrue($ArraySubscriber["CustomField$CustomFieldID"] == $ExpectedValues[$PostID]['work'][0], "$message CustomField $CustomFieldID = " . $ExpectedValues[$PostID]['work'][0] . ' [' . $ArraySubscriber["CustomField$CustomFieldID"] . ']');
            break;
          case "Website":
            $this->assertTrue($ArraySubscriber["CustomField$CustomFieldID"] == $ExpectedValues[$PostID]['work'][0], "$message CustomField $CustomFieldID = " . $ExpectedValues[$PostID]['work'][0] . ' [' . $ArraySubscriber["CustomField$CustomFieldID"] . ']');
            break;
          default:
            $this->assertTrue($ArraySubscriber["CustomField$CustomFieldID"] == $ExpectedValues['addresses'][0][$PostID], "$message CustomField $CustomFieldID = " . $ExpectedValues['addresses'][0][$PostID] . ' [' . $ArraySubscriber["CustomField$CustomFieldID"] . ']');
            break;

        }

      }

    }

  }

}

<?php

use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\EmailsNewsletterEmail;
use App\Klicktipp\Lists;
use App\Klicktipp\MarketingTools;
use App\Klicktipp\Personalization;
use App\Klicktipp\Plugin;
use App\Klicktipp\PluginHook;
use App\Klicktipp\PluginHookBeforeOutbound;
use App\Klicktipp\PluginHookConnectRedirect;
use App\Klicktipp\PluginHookInbound;
use App\Klicktipp\PluginHookOutbound;
use App\Klicktipp\PluginParameters;
use App\Klicktipp\Subaccount;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;
use App\Klicktipp\ToolPluginGeneral;
use App\Klicktipp\ToolPluginSimpletest;
use App\Klicktipp\ToolPluginRPC;
use App\Klicktipp\UserVariables;
use App\Klicktipp\VarPluginData;

class coreapiToolsPluginsTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'coreapi Plugin',
      'description' => 'Test classes/tools_plugin.inc',
      'group' => 'Klicktipp',
    );

  }

  function testPlugin() {

    $message = "prepare";

    $account = user_load_by_name(USERNAME_ENTERPRISE);
    $UserID = $account->uid;

    $ReferenceID = 0;

    $HttptestURL = 'https://'.KLICKTIPP_DOMAIN.'/kthttptest?secret=' . KLICKTIPP_HTTPTEST_SECRET;

    $ResultFieldID = CustomFields::Create(array(
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'Result',
      'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
      'FieldOptions' => ''
    ));
    $this->assertTrue($ResultFieldID > 0, $message . ' ResultFieldID');

    $OptInSubscribeTo = Tag::CreateManualTag($UserID, 'autosubscribe tag', '');
    $this->assertTrue($OptInSubscribeTo > 0, $message . 'tag');

    $firstname = 'Max';
    $lastname = 'Mustermann';
    $EmailAddress1 = "<EMAIL>";
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => array('ListID' => 0),
      'EmailAddress' => $EmailAddress1,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OtherFields' => array(
        'CustomFieldFirstName' => $firstname,
        'CustomFieldLastName' => $lastname,
      )
    );
    $result = Subscribers::Subscribe($Parameters);
    $SubscriberID1 = $result[1];
    $this->assertTrue($SubscriberID1 > 0, $message . ' Subscriber' . $SubscriberID1);
    $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID1, $ReferenceID);

    /**
     * Plugin
     */
    $message = "Plugin";

    $pluginid = 'myplugin';
    $plugindata = [
      'PluginName' => 'MyPlugin',
    ];

    // check not defined
    $index = Plugin::get_plugins();
    $this->assertTrue(empty($index[$pluginid]), "$message $pluginid undefined");

    // get plugin (undefined, with raw defaults)
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check PluginID set".print_r($plugin,1));
    $this->assertTrue($plugin['PluginName'] == "Plugin Name", "$message check PluginName set");
    $this->assertTrue($plugin['Class'] == "App\Klicktipp\ToolPluginGeneral", "$message check Class set");

    $subst = yaml_parse(variable_get("klicktipp_plugins_{$pluginid}_substitutions", ""));
    $this->assertTrue(empty($subst), "$message check subst".print_r($subst,1));

    // insert plugin
    Plugin::update_plugin($pluginid, $plugindata);
    $index = Plugin::get_plugins();
    $this->assertTrue(count($index) == 1, "$message 1 defined");
    $this->assertTrue($index[$pluginid], "$message $pluginid defined");

    // insert 2nd plugin
    Plugin::update_plugin("{$pluginid}2", $plugindata);
    $index = Plugin::get_plugins();
    $this->assertTrue(count($index) == 2, "$message 2 defined".print_r($index,1));
    $this->assertTrue($index[$pluginid], "$message $pluginid defined");

    // get plugin (with defaults)
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check PluginID set".print_r($plugin,1));
    $this->assertTrue($plugin['PluginName'] == $plugindata['PluginName'], "$message check PluginName set");
    $this->assertTrue($plugin['Class'] == "App\Klicktipp\ToolPluginGeneral", "$message check Class set");
    $subst = yaml_parse(variable_get("klicktipp_plugins_{$pluginid}_substitutions", ""));
    $this->assertTrue(count($subst) == count($plugindata) + 1, "$message check subst".print_r($subst,1));
    $plugin = variable_get("klicktipp_plugins_$pluginid", []);
    $this->assertTrue(count($plugin) == 4, "$message check plugin".print_r($plugin,1));

    // get plugin (reset = recombination of subst with yaml file)
    $recombined = Plugin::get_plugin($pluginid, TRUE);
    $this->assertTrue($plugin == $recombined, "$message check recombined".print_r($recombined,1));

    // delete 2nd plugin
    Plugin::delete_plugin("{$pluginid}2");
    $index = Plugin::get_plugins();
    $this->assertTrue(count($index) == 1, "$message 2 defined");
    $this->assertTrue($index[$pluginid], "$message $pluginid still defined");

    //TODO get_plugins_by_permission
    //TODO get_plugin_var
    //TODO get_plugin_var_byid

    ////// unwrap_key
    $key = Plugin::unwrap_key('Tool:', '%Tool:ToolID%');
    $this->assertTrue($key == 'ToolID', "$message ToolID $key");
    $key = Plugin::unwrap_key('Subscriber:CustomFieldPlugin:', '%Subscriber:CustomFieldPlugin:SomeField-%Tool:ToolID%%');
    $this->assertTrue($key == 'SomeField-%Tool:ToolID%', "$message CustomFieldPlugin $key");

    ////// PluginHook::prepare

    $pluginid = 'pluginhookprepare';
    $ToolID = 9876;
    $ReferenceName = 'a 3rd party tool name';
    $RelIndexed = 4711;
    $TagID = 1234;
    $time = time();
    $plugindata = [
      'PluginName' => 'Test PluginHook::prepare',
      'Hooks' => [
        'connect' => [
          'Types' => [
            Plugin::PLUGIN_HOOK_TYPE_CONNECT,
          ],
          'Variables' => [
            '%Tool:RelIndexed%' => [
              'type' => Plugin::PLUGIN_VARIABLE_TYPE_STATIC,
              'default' => $RelIndexed + 10,
            ],
            '%Tool:ReferenceName%' => [
              'type' => Plugin::PLUGIN_VARIABLE_TYPE_STATIC,
              'default' => $ReferenceName,
            ],
          ],
        ],
      ],
    ];
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $ArrayTool = [
      'ToolID' => $ToolID,
      'RelOwnerUserID' => $UserID,
      'Name' => 'My Tool',
      'VarcharIndexed' => $pluginid,
      'RelIndexed' => $RelIndexed,
      'PluginReadySmartTagID' => $TagID,
    ];

    $Taggings = [
      $TagID => [
        'RelTagID' => $TagID,
        'SubscriptionDate' => $time,
      ]
    ];

    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];
    $keys = Plugin::getKeys($variables);
    $this->assertTrue(in_array('%User:UserID%', array_keys($keys)), "$message get_default_keys".print_r($keys,1));
    $this->assertTrue(in_array('%Tool:RelIndexed%', array_keys($keys)), "$message get_default_keys RelIndexed");
    $this->assertTrue(in_array('%Tool:ReferenceName%', array_keys($keys)), "$message get_default_keys RelIReferenceNamendexed");

    $parameters = new PluginParameters($plugin);
    PluginHookConnectRedirect::prepare(
      $parameters,
      $plugin['Hooks']['connect'],
      [
        'User' => (array) $account,
        'Tool' => $ArrayTool,
        'Subscription' => $FullSubscriberWithFields,
        'Taggings' => $Taggings
      ]
    );

    $this->assertTrue($parameters['%Plugin:PluginID%'] == $pluginid, "$message PluginID".print_r($parameters,1));
    $this->assertTrue($parameters['%App:AppUrl%'] == APP_URL, "$message AppUrl");
    $this->assertTrue($parameters['%App:Salt%'] == KLICKTIPP_SALT, "$message Salt");
    $this->assertTrue($parameters['%User:UserID%'] == $UserID, "$message UserID");
    $this->assertTrue($parameters['%Tool:ToolID%'] == $ToolID, "$message ToolID");
    $this->assertTrue($parameters['%Tool:RelIndexed%'] == $RelIndexed, "$message RelIndexed");
    $this->assertTrue($parameters['%Tool:ReferenceName%'] == $ReferenceName, "$message ReferenceName");
    $this->assertTrue(!isset($parameters['%Tool:PluginReadySmartTagID%']), "$message PluginReadySmartTagID");
    $this->assertTrue($parameters['%Subscriber:SubscriberID%'] == $SubscriberID1, "$message SubscriberID");
    $this->assertTrue(!isset($parameters['%Subscriber:CustomFieldFirstName%']), "$message CustomFieldFirstName");
    $this->assertTrue(!isset($parameters['%Subscriber:CustomFieldLastName%']), "$message CustomFieldLastName");
    $this->assertTrue(!isset($parameters['%Subscriber:FullName%']), "$message FullName");
    $this->assertTrue(!isset($parameters['%Tagging:%Tool:PluginReadySmartTagID%%']), "$message Tagging");

    $keys = array_merge($keys, [
      '%Tool:RelIndexed%' => '%Tool:RelIndexed%',
      '%Tool:ReferenceName%' => '%Tool:ReferenceName%',
      '%Tool:PluginReadySmartTagID%' => '%Tool:PluginReadySmartTagID%',
      '%Subscriber:CustomFieldFirstName%' => '%Subscriber:CustomFieldFirstName%',
      '%Subscriber:CustomFieldLastName%' => '%Subscriber:CustomFieldLastName%',
      '%Subscriber:FullName%' => '%Subscriber:FullName%',
      '%Tagging:%Tool:PluginReadySmartTagID%%' => '%Tagging:%Tool:PluginReadySmartTagID%%',
    ]);

    /**
     * klicktipp_plugin_access
     *
     * who has access?
     * 1. all admins 'administer klicktipp'
     * 2. plugin is published + 'access plugins'
     * 3. plugin is not published + 'access plugins' + email address of user in marketing account (380) has tag configured in plugin
     */
    $message = "klicktipp_plugin_access";

    // prepare plugin

    $marketing_uid = variable_get('klicktipp_marketing_account_id', 0);
    $this->assertTrue($marketing_uid > 0, $message . ' marketing uid');

    $AccessTagID = Tag::CreateManualTag($marketing_uid, 'access tag', '');
    $this->assertTrue($AccessTagID > 0, $message . ' tag');

    $pluginid = 'accessplugin';
    $plugindata = [
      'PluginName' => 'Test klicktipp_plugin_access',
      'AccessTagID' => $AccessTagID,
    ];
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    // prepare user in marketing account

    $Parameters = array(
      'UserID' => $marketing_uid,
      'ListInformation' => array('ListID' => 0),
      'EmailAddress' => $account->mail,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    );
    $result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($result[0], $message . ' subscribe user in marketing account');

    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($marketing_uid, $account->mail);
    $this->assertTrue(!empty($Subscriber), $message . ' subscriber in marketing account exists');

    // check general access

    $result = klicktipp_user_edit_access($account, Subaccount::SUBACCOUNT_WEBSITE_BUILDER);
    $this->assertTrue($result, $message . ' user edit access');

    $result = user_access('access klicktipp', $account);
    $this->assertTrue($result, $message . ' general plugin access');

    // check general plugin access

    // any plugin
    $result = klicktipp_plugin_access($account, '', TRUE, FALSE);
    $this->assertTrue($result, $message . ' any plugin');

    // not published
    $result = klicktipp_plugin_access($account, $plugin, TRUE, FALSE);
    $this->assertTrue(!$result, $message . ' plugin not published');

    // published
    $plugin['published'] = 1;

    $account_standard = user_load_by_name(USERNAME_BASIC); // standard
    $account_premium = user_load_by_name(USERNAME_PLUS); // premium
    $account_deluxe = user_load_by_name(USERNAME_ADVANCED); // deluxe

    // $plugin['permission'] = 'access klicktipp'; # default
    $result = klicktipp_plugin_access($account_standard, $plugin, TRUE, FALSE);
    $this->assertTrue($result, $message . ' plugin published standard w/ access klicktipp');
    $result = klicktipp_plugin_access($account_premium, $plugin, TRUE, FALSE);
    $this->assertTrue($result, $message . ' plugin published premium w/ access klicktipp');
    $result = klicktipp_plugin_access($account_deluxe, $plugin, TRUE, FALSE);
    $this->assertTrue($result, $message . ' plugin published deluxe w/ access klicktipp');
    $result = klicktipp_plugin_access($account, $plugin, TRUE, FALSE);
    $this->assertTrue($result, $message . ' plugin published enterprise w/ access klicktipp');

    $plugin['permission'] = 'klicktipp deluxe';
    $result = klicktipp_plugin_access($account_standard, $plugin, TRUE, FALSE);
    $this->assertTrue(!$result, $message . ' plugin published stnadard w/ klicktipp deluxe');
    $result = klicktipp_plugin_access($account_premium, $plugin, TRUE, FALSE);
    $this->assertTrue(!$result, $message . ' plugin published premium w/ klicktipp deluxe');
    $result = klicktipp_plugin_access($account_deluxe, $plugin, TRUE, FALSE);
    $this->assertTrue($result, $message . ' plugin published deluxe w/ klicktipp deluxe');
    $result = klicktipp_plugin_access($account, $plugin, TRUE, FALSE);
    $this->assertTrue($result, $message . ' plugin published enterprise w/ klicktipp deluxe');

    // check plugin access by tag
    $plugin['published'] = 0;

    $Taggings = Subscribers::RetrieveTagging($marketing_uid, $AccessTagID, $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue(empty($Taggings), $message . ' no access tagging');

    // check create access (w/o tagging)
    $result = klicktipp_plugin_access($account, $plugin, TRUE, FALSE);
    $this->assertTrue(!$result, $message . ' plugin create access');

    // check access (w/o tagging)
    $result = klicktipp_plugin_access($account, $plugin, FALSE, FALSE);
    $this->assertTrue(!$result, $message . ' no plugin access');

    // this is what will be done enabling beta testing (tag base access)
    $result = Subscribers::TagSubscriber($marketing_uid, $Subscriber['SubscriberID'], $AccessTagID, $ReferenceID);
    $this->assertTrue($result, $message . ' tag access');

    $Taggings = Subscribers::RetrieveTagging($marketing_uid, $AccessTagID, $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue(!empty($Taggings), $message . ' check access tagging');

    // check create access (w/ tagging)
    $result = klicktipp_plugin_access($account, $plugin, TRUE, FALSE);
    $this->assertTrue($result, $message . ' plugin create access');

    // check access (w/ tagging)
    $result = klicktipp_plugin_access($account, $plugin, FALSE, FALSE);
    $this->assertTrue($result, $message . ' now has plugin access');

    /**
     * VarPluginData
     */
    $message = "VarPluginData";

    $pluginid1 = 'mypluginwithvars1';
    $pluginid2 = 'mypluginwithvars2';
    $pluginid3 = 'mypluginwithvars3';
    $plugindata = [
      'PluginName' => 'MyPlugin',
      'Hooks' => [
        'connect' => [
          'Types' => [
            Plugin::PLUGIN_HOOK_TYPE_CONNECT,
          ],
          'Variables' => [
            '%Tool:Ignore%' => [
              'default' => '',
            ],
            '%UserVariable:SomeString%' => [
              'default' => '',
              'varfield' => 'VarcharIndexed',
            ],
            '%UserVariable:SomeInt%' => [
              'default' => '',
              'varfield' => 'RelIndexed',
            ],
            '%UserVariable:SomeHash%' => [
              'default' => '',
              'varfield' => 'HashIndexed',
            ],
            '%UserVariable:SomeOtherVar%' => [
              'default' => '',
            ],
          ],
        ],
      ],
    ];
    Plugin::update_plugin($pluginid1, $plugindata);
    $plugin1 = Plugin::get_plugin($pluginid1);
    $this->assertTrue($plugin1['PluginID'] == $pluginid1, "$message check plugin $pluginid1");
    $this->assertTrue(empty(VarPluginData::GetPluginDataByPluginID($pluginid1, $UserID)), "$message check no data for plugin $pluginid1");
    Plugin::update_plugin($pluginid2, $plugindata);
    $plugin2 = Plugin::get_plugin($pluginid2);
    $this->assertTrue($plugin2['PluginID'] == $pluginid2, "$message check plugin $pluginid2");
    $this->assertTrue(empty(VarPluginData::GetPluginDataByPluginID($pluginid2, $UserID)), "$message check no data for plugin $pluginid2");
    Plugin::update_plugin($pluginid3, $plugindata);
    $plugin3 = Plugin::get_plugin($pluginid3);
    $this->assertTrue($plugin3['PluginID'] == $pluginid3, "$message check plugin $pluginid3");
    $this->assertTrue(empty(VarPluginData::GetPluginDataByPluginID($pluginid3, $UserID)), "$message check no data for plugin $pluginid3");

    // VarcharIndexed: plugindata 1
    $VarcharIndexed = 'indexed';
    $parameters = [
      '%User:UserID%' => $UserID,
      '%Tool:Ignore%' => 4711,
      '%UserVariable:SomeString%' => $VarcharIndexed,
      '%UserVariable:SomeOtherVar%' => 'some other value',
    ];
    $variables = $plugin1['Hooks']['connect']['Variables'] ?? [];
    VarPluginData::SetPluginDataByParameter($pluginid1, $variables, $parameters);

    // get by parameter
    $result = VarPluginData::GetPluginDataByParameter($pluginid1, $variables, $parameters, []);
    $this->assertTrue(empty($result['%Tool:Ignore%']), "$message check %Tool ".print_r($result,1));
    $this->assertTrue($result['%UserVariable:SomeString%'] == $VarcharIndexed, "$message check SomeString");
    $this->assertTrue($result['%UserVariable:SomeOtherVar%'] == $parameters['%UserVariable:SomeOtherVar%'], "$message check SomeOtherVar");
    $this->assertTrue($result['%User:UserID%'] == $UserID, "$message check UserID");

    // get by VarcharIndexed
    $result = VarPluginData::GetPluginDataByVarcharIndexed($pluginid1, $VarcharIndexed, []);
    $this->assertTrue($result['%UserVariable:SomeString%'] == $VarcharIndexed, "$message check SomeString");
    $this->assertTrue($result['%UserVariable:SomeOtherVar%'] == $parameters['%UserVariable:SomeOtherVar%'], "$message check SomeOtherVar");

    // RelIndexed: plugindata 2
    $RelIndexed = 1234;
    $parameters = [
      '%User:UserID%' => $UserID,
      '%Tool:Ignore%' => 4711,
      '%UserVariable:SomeString%' => $VarcharIndexed,
      '%UserVariable:SomeInt%' => $RelIndexed,
      '%UserVariable:SomeOtherVar%' => 'some other value',
    ];
    VarPluginData::SetPluginDataByParameter($pluginid2, $variables, $parameters);

    // get by parameter
    $result = VarPluginData::GetPluginDataByParameter($pluginid2, $variables, $parameters, []);
    $this->assertTrue(empty($result['%Tool:Ignore%']), "$message check %Tool ".print_r($result,1));
    $this->assertTrue($result['%UserVariable:SomeInt%'] == $RelIndexed, "$message check SomeInt");
    $this->assertTrue($result['%UserVariable:SomeString%'] == $VarcharIndexed, "$message check SomeString");
    $this->assertTrue($result['%UserVariable:SomeOtherVar%'] == $parameters['%UserVariable:SomeOtherVar%'], "$message check SomeOtherVar");

    // get by RelIndexed
    $result = VarPluginData::GetPluginDataByRelIndexed($pluginid2, $RelIndexed, []);
    $this->assertTrue($result['%UserVariable:SomeInt%'] == $RelIndexed, "$message check SomeInt");
    $this->assertTrue($result['%UserVariable:SomeString%'] == $VarcharIndexed, "$message check SomeString");
    $this->assertTrue($result['%UserVariable:SomeOtherVar%'] == $parameters['%UserVariable:SomeOtherVar%'], "$message check SomeOtherVar");

    // HashIndexed: hash_from_string
    // MySQL max value for int(11) is 2^31 - make sure our int hash is smaller
    $HashIndexed = "obBEe8ewaL_KdyNjniT4KPd8ffDWt9fGB";
    $this->assertTrue(crc32($HashIndexed) > 2**31, "$message crc32 exceeds 32bit maxint");
    $this->assertTrue(Plugin::hash_from_string($HashIndexed) < 2**31, "$message hash not exceeds 32bit maxint");

    // HashIndexed: plugindata 3
    $parameters = [
      '%User:UserID%' => $UserID,
      '%Tool:Ignore%' => 4711,
      '%UserVariable:SomeString%' => $VarcharIndexed,
      '%UserVariable:SomeOtherVar%' => 'some other value',
      '%UserVariable:SomeHash%' => $HashIndexed,
    ];
    VarPluginData::SetPluginDataByParameter($pluginid3, $variables, $parameters);

    // get by parameter
    $result = VarPluginData::GetPluginDataByParameter($pluginid3, $variables, $parameters, []);
    $this->assertTrue(empty($result['%Tool:Ignore%']), "$message check %Tool ".print_r($result,1));
    $this->assertTrue($result['%UserVariable:SomeHash%'] == $HashIndexed, "$message check SomeHash");
    $this->assertTrue($result['%UserVariable:SomeString%'] == $VarcharIndexed, "$message check SomeString");
    $this->assertTrue($result['%UserVariable:SomeOtherVar%'] == $parameters['%UserVariable:SomeOtherVar%'], "$message check SomeOtherVar");

    // get by HashIndexed
    $result = VarPluginData::GetPluginDataByHashIndexed($pluginid3, '%UserVariable:SomeHash%', $HashIndexed, []);
    $this->assertTrue($result['%UserVariable:SomeHash%'] == $HashIndexed, "$message check SomeHash");
    $this->assertTrue($result['%UserVariable:SomeString%'] == $VarcharIndexed, "$message check SomeString");
    $this->assertTrue($result['%UserVariable:SomeOtherVar%'] == $parameters['%UserVariable:SomeOtherVar%'], "$message check SomeOtherVar");

    // delete
    VarPluginData::DeletePluginData($pluginid2, $UserID);

    $result = VarPluginData::GetPluginDataByPluginID($pluginid1, $UserID);
    $this->assertTrue(!empty($result), "$message check not null plugin 1");
    $result = VarPluginData::GetPluginDataByPluginID($pluginid2, $UserID);
    $this->assertTrue(empty($result), "$message check null plugin 2");

    VarPluginData::DeletePluginData($pluginid1, $UserID);

    $result = VarPluginData::GetPluginDataByPluginID($pluginid1, $UserID);
    $this->assertTrue(empty($result), "$message check null plugin 1");
    $result = VarPluginData::GetPluginDataByPluginID($pluginid2, $UserID);
    $this->assertTrue(empty($result), "$message check null plugin 2");

    /**
     * Plugin::getInboundField
     */
    $message = "Plugin getInboundField";

    $pluginid = 'plugininboundfields';
    $plugindata = <<<EOF
---
PluginName: PluginInboundFields
Hooks:
  whatever:
    Variables:
      '%Temp:WithPattern%':
        pattern: '%Const:WithPattern%'
        regex: /(.*)/
      '%Temp:Empty%':
        pattern: '%Const:Empty%'
        regex: /^$/
      '%Temp:NotEmpty%':
        pattern: '%Const:WithPattern%'
        regex: /^$/
      '%Temp:Occurrence%':
        type: Pop
        field: payload|object|occurrences
      '%Temp:WebinarStart%':
        type: Inbound
        pattern: '%Temp:Occurrence%'
        regex: /\"start_time\":\"([^\"]*)\"/
      '%UserVariable:Whatever%':
        type: ReadIfExists
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $occurrence_id = "occ1";
    $occurrence_start_encoded = "2020-01-01T12:00:00Z"; // ISO 8601 as UTC (Z)

    $data = [
      "payload" => [
        "object" => [
          "occurrences" => [
            [
              "occurrence_id" => $occurrence_id,
              "start_time" => $occurrence_start_encoded,
            ]
          ],
        ],
      ]
    ];

    $pattern = 'pattern';

    $parameters = [
      '%Const:WithPattern%' => 'pattern',
    ];

    // simple pattern (.*) from other field value
    $value = Plugin::getInboundField($plugin['Hooks']['whatever']['Variables']['%Temp:WithPattern%'], $data, $parameters);
    $this->assertTrue($value == $pattern, "$message WithPattern $value");

    // empty pattern (^$) - get is always empty
    $value = Plugin::getInboundField($plugin['Hooks']['whatever']['Variables']['%Temp:Empty%'], $data, $parameters);
    $this->assertTrue($value === '', "$message Empty $value");
    $value = Plugin::getInboundField($plugin['Hooks']['whatever']['Variables']['%Temp:NotEmpty%'], $data, $parameters);
    $this->assertTrue($value === null, "$message NotEmpty $value");

    // validate empty pattern (^$)
    $result = '';
    $value = Plugin::validateInboundField('%Temp:Empty%', $plugin['Hooks']['whatever']['Variables']['%Temp:Empty%'], $data, $parameters, $result);
    $this->assertTrue($value, "$message validate Empty $value");
    $value = Plugin::validateInboundField('%Temp:NotEmpty%', $plugin['Hooks']['whatever']['Variables']['%Temp:NotEmpty%'], $data, $parameters, $result);
    $this->assertFalse($value, "$message validate NotEmpty $value");

    // pop and get from structure
    $value = Plugin::popInboundField($plugin['Hooks']['whatever']['Variables']['%Temp:Occurrence%'], $data, $parameters);
    $this->assertTrue(is_string($value), "$message Pop structure $value");
    $parameters['%Temp:Occurrence%'] = $value;
    $value = Plugin::getInboundField($plugin['Hooks']['whatever']['Variables']['%Temp:WebinarStart%'], $data, $parameters);
    $this->assertTrue($value == $occurrence_start_encoded, "$message get from Pop structure $value");

    // unspecified get
    $value = Plugin::getInboundField($plugin['Hooks']['whatever']['Variables']['%UserVariable:Whatever%'], [], $parameters);
    $this->assertTrue($value === null, "$message unspecified get $value");

    //TODO more cases of Variables for getInboundField

    /**
     * parse connect request
     */

    $message = "Plugin perse request";

    $pluginid = 'myplugin';
    $plugindata = <<<EOF
---
PluginName: MyPlugin
Hooks:
  connect:
    Types:
      - hook_connect # PLUGIN_HOOK_TYPE_CONNECT
    Variables:
      '%Tool:RelIndexed%':
        default: ""
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: automation_id
        regex: /(.*)/
      '%UserVariable:Token%':
        default: ""
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: token
        regex: /(.*)/
        varfield: VarcharIndexed
      '%Tool:ConnectRedirectURL%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: redirect_url
        regex: /(.*)/
        sanitize: url
      '%Temp:ConnectHash%':
        default: ""
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: hash
        regex: /(.*)/
      '%Temp:CalcHash%':
        default: ""
        type: Calc # PLUGIN_VARIABLE_TYPE_CALC
        method: hash # callable
        parameter:
        - sha512
        - '%Tool:RelIndexed%,%UserVariable:Token%,123stagingsecret'
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $request = [
      'automation_id' => 4711,
      'token' => 'a1b2c3d4',
      'redirect_url' => 'https://www.zauberlist.com?a=b&c=d',
    ];
    $request['hash'] = hash('sha512', implode(',', [$request['automation_id'], $request['token'], "123stagingsecret"]));
    $parameters = new PluginParameters($plugin);
    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];
    $result = $parameters->process($variables, $request);

    $this->assertTrue($parameters['%Tool:RelIndexed%'] == 4711, "$message check RelIndexed ".print_r($parameters->getParameters(),1));
    $this->assertTrue($parameters['%Tool:ConnectRedirectURL%'] == $request['redirect_url'], "$message check ConnectRedirectURL");
    $this->assertTrue($parameters['%Temp:ConnectHash%'] == $request['hash'], "$message check ConnectHash=".$request['hash']);
    $this->assertTrue($parameters['%Temp:CalcHash%'] == $request['hash'], "$message check CalcHash");

    /**
     * Plugin::validate_inbound_parameters
     */

    $message = "Plugin validate_inbound_parameters";

    $pluginid = 'validateplugin';
    $plugindata = <<<EOF
---
PluginName: TestInboundValidation
Hooks:
  connect:
    Types:
      - hook_connect # PLUGIN_HOOK_TYPE_CONNECT
    Variables:
      '%Tool:RelIndexed%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: automation_id
        regex: /(.*)/
  ready:
    Types:
      - hook_inbound
    validate:
      '%Temp:Inbound%':
        type: Validate # PLUGIN_VARIABLE_TYPE_VALIDATE
        field: event
        regex: /^(contact\.notification_due)$/
    process:
      '%Tool:SomeField%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: payload
        regex: /\"somethingnumeric\"\:([\d]*)[^\d]/
      '%Subscriber:CustomFieldWebsite%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: payload
        regex: /\"landingpage\"\:\"([^\"].*)\"/
        default: '#'
      '%Tool:RelIndexed%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: automation_id
        regex: /(.*)/
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $data = [
      "event" => "contact.notification_due",
      "automation_id" => 4711,
      "payload" => [
        "landing_page_url" => "https://app.wowing.io/api/notifications/430c21f3-123/redirect",
        "files" => [
          [
            "mime_type" => "audio/mpeg",
            "path" => "https://cdn-staging.wowing.io/media/123.mp3",
            "somethingnumeric" => 555,
          ],
        ]
      ]
    ];

    $value = Plugin::getInboundField($plugin['Hooks']['ready']['validate']['%Temp:Inbound%'], $data);
    $this->assertTrue($value == 'contact.notification_due', "$message prepare_inbound_value event=".$value);

    $value = Plugin::getInboundField($plugin['Hooks']['ready']['process']['%Tool:SomeField%'], $data);
    $this->assertTrue($value == 555, "$message prepare_inbound_value payload=".$value);

    $parameters = new PluginParameters($plugin);
    $result = PluginHook::validateEvent($parameters, 'ready', $data);
    $this->assertTrue($result, "$message valid ".print_r($parameters->getParameters(),1));
    $this->assertTrue($parameters['%Temp:Inbound%'] == 'contact.notification_due', "$message check Inbound");
    $this->assertTrue(!isset($parameters['%Tool:RelIndexed%']), "$message check RelIndexed");

    $invalid = $data;
    $invalid['event'] = "some other";

    $parameters->init();
    $result = PluginHook::validateEvent($parameters, 'ready', $invalid);
    $this->assertTrue(!$result, "$message invalid".print_r($parameters->getParameters(),1));
    $this->assertTrue(!isset($parameters['%Temp:Inbound%']), "$message check Inbound");
    $this->assertTrue(!isset($parameters['%Tool:RelIndexed%']), "$message check RelIndexed");

    /**
     * PluginHookInbound::processEvent
     */

    $message = "PluginHookInbound::processEvent";

    $pluginid = 'parseinboundplugin';
    $plugindata = <<<EOF
---
PluginName: TestParseInbound
Hooks:
  connect:
    Types:
      - hook_connect # PLUGIN_HOOK_TYPE_CONNECT
    Variables:
      '%Tool:RelIndexed%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: automation_id
        regex: /(.*)/
  ready:
    Types:
      - hook_inbound
    validate:
      '%Temp:Inbound%':
        type: Validate # PLUGIN_VARIABLE_TYPE_VALIDATE
        field: event
        regex: /^contact.notification_due$/
    process:
      '%Tool:RelIndexed%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: automation_id
        regex: /(.*)/
      '%Tool:SomeField%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: payload
        # json is: "somethingnumeric":555
        regex: /\"somethingnumeric\"\:([\d]*)[^\d]/
      '%Subscriber:Landingpage%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: payload|landing_page_url
        regex: /(.*)/
        default: '#'
        sanitize: url
      '%Subscriber:PreviewImage%':
        type: Inbound # PLUGIN_VARIABLE_TYPE_INBOUND
        field: payload|preview_file_path|0|path
        regex: /(.*)/
        default: /misc/plugins/videothumnail.jpg
      '%Tool:PluginReadySmartTagID%':
        type: SmartTag # PLUGIN_VARIABLE_TYPE_SMARTTAG
        category: 81 # Tag::CATEGORY_PLUGIN_INBOUND_READY
        name: Wowing %Tool:Name% versandbereit
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $landingpage = "https://app.wowing.io/api/notifications/430c21f3-123/redirect";
    $preview_file_path = "https://cdn-staging.wowing.io/assets/audio-preview.png";
    $data = [
      "event" => "contact.notification_due",
      "automation_id" => 4711,
      "payload" => [
        "landing_page_url" => $landingpage,
        "preview_file_path" => [
          [
            "mime_type" => "image/png",
            "path" => $preview_file_path,
          ]
        ],
        "files" => [
          [
            "mime_type" => "audio/mpeg",
            "path" => "https://cdn-staging.wowing.io/media/123.mp3",
            "somethingnumeric" => 555,
          ],
        ]
      ]
    ];

    $parameters = new PluginParameters($plugin);
    PluginHookInbound::processEvent($parameters, 'ready', $data);
    $this->assertTrue(!isset($parameters['%Tool:Inbound%']), "$message check Inbound".print_r($parameters->getParameters(),1));
    $this->assertTrue($parameters['%Tool:RelIndexed%'] == 4711, "$message check RelIndexed");
    $this->assertTrue($parameters['%Tool:SomeField%'] == 555, "$message check SomeField");
    $this->assertTrue($parameters['%Subscriber:Landingpage%'] == $landingpage, "$message check LandingPage");
    $this->assertTrue($parameters['%Subscriber:PreviewImage%'] == $preview_file_path, "$message check PreviewImage");

    /**
     * PluginHookConnectRedirect::validateEvent
     */

    $message = "PluginHookConnectRedirect::validateEvent";

    $plugin['Connect'] = [
      'method' => Plugin::PLUGIN_CONNECT_METHOD_LOGIN_CREATE_REDIRECT,
      'validation' => [
        'pattern' => '%Temp:CalcHash%|%Temp:ConnectHash%',
        'regex' => '/^([^\|]+)\|\1$/',
      ],
    ];
    Plugin::update_plugin($pluginid, $plugindata);

    $parameters = new PluginParameters($plugin);
    $parameters['%Temp:CalcHash%'] = 'abc';
    $parameters['%Temp:ConnectHash%'] = 'abc';

    $result = PluginHookConnectRedirect::validateEvent($parameters, 'connect');
    $this->assertTrue($result, "$message success ".strtr('%Temp:CalcHash%|%Temp:ConnectHash%',$parameters->getParameters()));

    $parameters['%Temp:CalcHash%'] = '0123456789';

    $result = PluginHookConnectRedirect::validateEvent($parameters, 'connect');
    $this->assertTrue(!$result, "$message fail ".strtr('%Temp:CalcHash%|%Temp:ConnectHash%',$parameters->getParameters()));

    /**
     * Plugin::create_guzzle_request
     */

    $message = "Plugin create_guzzle_request";

    $RequestConf = [
      'method' => Plugin::PLUGIN_CONNECT_METHOD_LOGIN_CREATE_REDIRECT,
      'url' => 'https://www.example.com/api/automations/%Tool:RelIndexed%',
      'request' => [
        // parameters for guzzle_http_request
        'method' => 'PUT',
        'timeout' => 10.0,
        'data' => [
          'outbound_webhook_url' => APP_URL.'api/plugin/inbound',
          'integration_data' => [
            "klicktipp_entity_url" => APP_URL.'plugins/%User:UserID%/%Tool:ToolID%/edit',
            "klicktipp_entity_name" => "%Tool:Name%",
          ],
        ],
        'headers' => [
          'Authorization' => 'Bearer %UserVariable:Token%',
        ]
      ]

    ];

    $parameters = [
      '%Tool:Name%' => 'My App',
      '%User:UserID%' => $UserID,
      '%Tool:ToolID%' => 4711,
      '%Tool:RelIndexed%' => 815,
      '%UserVariable:Token%' => 'bearertoken',
    ];
    $rebound = Plugin::create_guzzle_request($RequestConf, new PluginParameters([], $parameters));

    $this->assertTrue(!empty($rebound), "$message success".print_r($rebound,1));
    [$ReboundURL, $options] = $rebound;
    $this->assertTrue($ReboundURL == 'https://www.example.com/api/automations/815', "$message ReboundURL");
    $this->assertTrue($options['timeout'] == 10, "$message timeout".print_r($options,1));
    $this->assertTrue($options['data']['outbound_webhook_url'] == APP_URL."api/plugin/inbound", "$message outbound_webhook_url");
    $this->assertTrue($options['data']['integration_data']['klicktipp_entity_url'] == APP_URL."plugins/$UserID/4711/edit", "$message klicktipp_entity_url");
    $this->assertTrue($options['data']['integration_data']['klicktipp_entity_name'] == 'My App', "$message klicktipp_entity_name");
    $this->assertTrue($options['headers']['Authorization'] == "Bearer bearertoken", "$message headers");

    /**
     * ToolPluginGeneral::InsertDB
     **/
    $message = 'InsertDB';

    $pluginid = 'insertdbplugin';
    $plugindata = [
      'PluginName' => 'TestInsertDB',
    ];
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    // see ToolPluginGeneral::$RequiredFieldsOnInsert
    $InsertData = [
      'RelOwnerUserID' => $UserID,
      'Name' => 'My Tool',
      'VarcharIndexed' => $pluginid,
      'SomeAdditionalField' => 'abc',
    ];

    // check changed serialization
    $serialized = ToolPluginGeneral::DataSerialize($InsertData);
    $this->assertTrue(empty($serialized['SomeAdditionalField']), "$message DataSerialize".print_r($serialized,1));
    $unserialized = ToolPluginGeneral::DataUnserialize($serialized);
    $this->assertTrue($unserialized['SomeAdditionalField'] == 'abc', "$message DataUnserialize".print_r($unserialized,1));

    // check Presave
    $presave = ToolPluginGeneral::Presave($serialized);
    $this->assertTrue(count($presave) == count($InsertData), "$message Presave".print_r($presave,1));

    $ToolID = ToolPluginGeneral::InsertDB($InsertData);
    $this->assertTrue(!empty($ToolID), "$message check");

    /**
     * ToolPluginGeneral::FromRelIndexed
     **/
    $message = 'FromRelIndexed';

    $OldToolID = $ToolID; // last insert

    $InsertData = [
      'RelOwnerUserID' => $UserID,
      'Name' => 'My Tool',
      'VarcharIndexed' => $pluginid,
    ];

    // first insert
    $InsertData['RelIndexed'] = 4711;
    $ToolID1 = ToolPluginGeneral::InsertDB($InsertData);
    $this->assertTrue($ToolID1 > $OldToolID, "$message check");

    // second insert
    $InsertData['RelIndexed'] = 4712;
    $ToolID2 = ToolPluginGeneral::InsertDB($InsertData);
    $this->assertTrue($ToolID2 > $ToolID1, "$message check");

    // get first
    $ToolObject1 = ToolPluginGeneral::FromIndex($pluginid, 4711);
    $this->assertTrue(!empty($ToolObject1), "$message FromRelIndexed 4711");
    $this->assertTrue($ToolObject1->GetData('ToolID') == $ToolID1, "$message FromRelIndexed ToolID 1");

    // get second
    $ToolObject2 = ToolPluginGeneral::FromIndex($pluginid, 4712);
    $this->assertTrue(!empty($ToolObject2), "$message FromRelIndexed 4712");
    $this->assertTrue($ToolObject2->GetData('ToolID') == $ToolID2, "$message FromRelIndexed ToolID 2");

    /**
     * ToolPluginGeneral::GetSmartTags
     **/
    $message = 'GetSmartTags';

    $pluginid = 'smarttagsplugin';
    $plugindata = [
      'PluginName' => 'TestGetSmartTags',
      'Hooks' => [
        'connect' => [
          'Types' => [
            Plugin::PLUGIN_HOOK_TYPE_CONNECT,
          ],
          'Variables' => [
            '%Tool:ActivationSmartTagID%' => [
              'type' => Plugin::PLUGIN_VARIABLE_TYPE_SMARTTAG,
              'category' => Tag::CATEGORY_OUTBOUND,
            ],
          ],
        ],
      ],
    ];
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $InsertData = [
      'RelOwnerUserID' => $UserID,
      'Name' => 'My Tool',
      'VarcharIndexed' => $pluginid,
    ];
    $ToolID = ToolPluginGeneral::InsertDB($InsertData);
    $this->assertTrue(!empty($ToolID), "$message InsertDB");

    $ToolObject = ToolPluginGeneral::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), "$message FromID".print_r($ToolObject,1));

    $SmartTags = $ToolObject->GetSmartTags();
    $this->assertTrue(!empty($SmartTags['ActivationSmartTagID']), "$message GetSmartTags".print_r($SmartTags,1));

    $result = Subscribers::TagSubscriber($UserID, $SubscriberID1, $SmartTags['ActivationSmartTagID'], $ReferenceID);
    $this->assertTrue($result, $message . ' tag SmartTag');

    /**
     * ToolPluginGeneral::CreateUnqiueName
     **/
    $message = 'CreateUnqiueName';

    $pluginid = 'uniquenameplugin';
    $ReferenceName = '3rd party tool name';
    $plugindata = [
      'PluginName' => 'TestCreateUnqiueName',
      'Hooks' => [
        'connect' => [
          'Types' => [
            Plugin::PLUGIN_HOOK_TYPE_CONNECT,
          ],
          'Variables' => [
            '%Tool:ReferenceName%' => [
              'type' => Plugin::PLUGIN_VARIABLE_TYPE_STATIC,
              'default' => $ReferenceName,
            ],
          ],
        ],
      ],
    ];
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");
    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];
    $keys = Plugin::getKeys($variables);

    $InsertData = [
      'RelOwnerUserID' => $UserID,
      'Name' => $ReferenceName,
      'VarcharIndexed' => $pluginid,
    ];
    $ToolID = ToolPluginGeneral::InsertDB($InsertData);
    $this->assertTrue(!empty($ToolID), "$message InsertDB");

    $ToolObject = ToolPluginGeneral::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), "$message FromID");

    $parameters = new PluginParameters($plugin);
    PluginHookConnectRedirect::prepare(
      $parameters,
      $plugin['Hooks']['connect'],
      [
        'User' => (array) $account,
        'Tool' => $ArrayTool,
      ]
    );

    // no name pattern given

    $ExpectedName = "{$plugindata['PluginName']} {$ReferenceName}";
    $ToolName = ToolPluginGeneral::CreateUnqiueName($plugin, $account, $parameters->getParameters());
    $this->assertTrue($ToolName == $ExpectedName, $message . print_r([$ToolName, $ExpectedName, $parameters->getParameters()],1));

    // insert and repeat
    $InsertData['Name'] = $ToolName;
    $ToolID = ToolPluginGeneral::InsertDB($InsertData);
    $this->assertTrue(!empty($ToolID), "$message InsertDB");
    $ExpectedName = "{$ExpectedName}1";
    $ToolName = ToolPluginGeneral::CreateUnqiueName($plugin, $account, $parameters->getParameters());
    $this->assertTrue($ToolName == $ExpectedName, $message . print_r([$ToolName, $ExpectedName, $parameters->getParameters()],1));

    // name pattern w/o %Temp:Count% given

    $plugindata = [
      'PluginName' => 'TestCreateUnqiueName',
      'Connect' => [
        'name_pattern' => "Test1: %Tool:ReferenceName%",
      ],
      'Hooks' => [
        'connect' => [
          'Types' => [
            Plugin::PLUGIN_HOOK_TYPE_CONNECT,
          ],
          'Variables' => [
            '%Tool:ReferenceName%' => [
              'type' => Plugin::PLUGIN_VARIABLE_TYPE_STATIC,
              'default' => $ReferenceName,
            ],
          ],
        ],
      ],
    ];
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check".print_r($plugin,1));
    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];
    $keys = Plugin::getKeys($variables);
    $parameters = new PluginParameters($plugin);
    PluginHookConnectRedirect::prepare(
      $parameters,
      $plugin['Hooks']['connect'],
      [
        'User' => (array) $account,
        'Tool' => $ToolObject->GetData(),
      ]
    );

    $ExpectedName = "Test1: {$ReferenceName}";
    $ToolName = ToolPluginGeneral::CreateUnqiueName($plugin, $account, $parameters->getParameters());
    $this->assertTrue($ToolName == $ExpectedName, $message . print_r([$ToolName, $ExpectedName, $parameters->getParameters()],1));

    // insert and repeat
    $InsertData['Name'] = $ToolName;
    $ToolID = ToolPluginGeneral::InsertDB($InsertData);
    $this->assertTrue(!empty($ToolID), "$message InsertDB");
    $ExpectedName = "{$ExpectedName}1";
    $ToolName = ToolPluginGeneral::CreateUnqiueName($plugin, $account, $parameters->getParameters());
    $this->assertTrue($ToolName == $ExpectedName, $message . print_r([$ToolName, $ExpectedName, $parameters->getParameters()],1));

    // name pattern w/ %Temp:Count% given

    $plugindata = [
      'PluginName' => 'TestCreateUnqiueName',
      'Connect' => [
        'name_pattern' => "Test2: %Tool:ReferenceName%%Temp:Count%",
      ],
      'Hooks' => [
        'connect' => [
          'Types' => [
            Plugin::PLUGIN_HOOK_TYPE_CONNECT,
          ],
          'Variables' => [
            '%Tool:ReferenceName%' => [
              'type' => Plugin::PLUGIN_VARIABLE_TYPE_STATIC,
              'default' => $ReferenceName,
            ],
          ],
        ],
      ],
    ];
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");
    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];
    $keys = Plugin::getKeys($variables);
    $parameters = new PluginParameters($plugin);
    PluginHookConnectRedirect::prepare(
      $parameters,
      $plugin['Hooks']['connect'],
      [
        'User' => (array) $account,
        'Tool' => $ArrayTool,
      ]
    );

    $ExpectedName = "Test2: {$ReferenceName}";
    $ToolName = ToolPluginGeneral::CreateUnqiueName($plugin, $account, $parameters->getParameters());
    $this->assertTrue($ToolName == $ExpectedName, $message . print_r([$ToolName, $ExpectedName, $parameters->getParameters()],1));

    // insert and repeat
    $InsertData['Name'] = $ToolName;
    $ToolID = ToolPluginGeneral::InsertDB($InsertData);
    $this->assertTrue(!empty($ToolID), "$message InsertDB");
    $ExpectedName = "{$ExpectedName}1";
    $ToolName = ToolPluginGeneral::CreateUnqiueName($plugin, $account, $parameters->getParameters());
    $this->assertTrue($ToolName == $ExpectedName, $message . print_r([$ToolName, $ExpectedName, $parameters->getParameters()],1));

    /**
     * ToolPluginGeneral::InsertPlugin
     **/
    $message = 'InsertPlugin';

    $pluginid = 'insertplugin';
    $plugindata = <<<EOF
---
PluginName: TestInsertPlugin
Hooks:
  connect:
    Types:
      - hook_connect # HOOK_TYPE_CONNECT
    Variables:
      '%Tool:ReferenceName%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: automation_name
        regex: /(.*)/
      '%Tool:ActivationURL%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: webhook_url
        regex: /(.*)/
  out:
    Types:
      - hook_outbound
    validate:
      '%Subscriber:EmailAddress%':
        type: Key # VARIABLE_TYPE_KEY
      '%User:PluginAccess%':
        type: Validate
        regex: '/^T$/'
    Request:
      url: '%Tool:ActivationURL%'
      request:
        method: POST
        timeout: 10
        data:
          email: '%Subscriber:EmailAddress%'
    process:
      '%Tool:ActivationSmartTagID%':
        type: SmartTag # VARIABLE_TYPE_SMARTTAG
        category: 22 # Tag::CATEGORY_OUTBOUND
    on_error:
      error: Error
      message: Some error message.
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $parameters = new PluginParameters($plugin);

    // get 'Variables' of hook_connect
    $hookname = PluginHookConnectRedirect::getConnectHookname($plugin);
    $hook = $parameters->getHook($hookname);
    $variables = $hook['Variables'] ?? [];

    $keys = Plugin::getKeys($variables);
    $parameters->resetScope($keys);
    $parameters->getParametersFromUser($keys, (array) $account);

    $request = [
      'automation_name' => 'My tool',
      'webhook_url' => $HttptestURL, //TODO .'&email=%Outbound:EmailAddress%',
    ];
    PluginHookConnectRedirect::validateEvent($parameters, $hookname, $request);
    $this->assertTrue($parameters['%Tool:ReferenceName%'] == $request['automation_name'], "$message check ReferenceName ".print_r($parameters->getParameters(),1));
    $this->assertTrue($parameters['%Tool:ActivationURL%'] == $HttptestURL, "$message check ActivationURL");
    $this->assertTrue(empty($parameters['%Tool:RequestMethod%']), "$message check RequestMethod");

    $ToolID = ToolPluginGeneral::InsertPlugin($plugin, $variables, $parameters);
    $this->assertTrue($ToolID > 0, $message . ' id');

    $ToolObject = ToolPluginGeneral::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), $message . ' FromID'.print_r($ToolObject,1));

    $ArrayTool = $ToolObject->GetData();
    $this->assertTrue(!empty($ArrayTool['ActivationSmartTagID']), $message . ' ActivationSmartTagID');
    $this->assertTrue($ArrayTool['ActivationURL'] == $request['webhook_url'], $message . ' ActivationURL');

    // check plugin access

    $hook = $parameters->getHook('out');
    $variables = $hook['validate'] ?? [];

    $keys = Plugin::getKeys($variables);
    $parameters->resetScope($keys);
    $parameters->getParametersFromUser($keys, (array) $account);
    // this checks if it works at all. to be valid it has to be a digistore user with a payed subscription ...
    $this->assertTrue($parameters['%User:PluginAccess%'] === 'F', "$message check plugin access ".print_r($parameters->getParameters(),1));
    $this->assertTrue(!PluginHook::validateEvent($parameters, 'out'), "$message validate");

    /**
     * ToolPluginRPC::writeTool
     * same as InsertPlugin for RPC
     **/
    $message = 'writeTool';

    $toolData = [
      'RelOwnerUserID' => $UserID,
      'ReferenceName' => 'Name of the foreign object',
      'ActivationURL' => $HttptestURL,
    ];
    $relIndexed = 4711; // gets overridden by hash
    $hashIndexed = 'hash';
    $hashKey = 'somekey';

    $newToolData = ToolPluginRPC::writeTool($pluginid, $relIndexed, $hashKey, $hashIndexed, $toolData);
    $this->assertTrue(!empty($newToolData), $message . ' writeTool '.print_r($newToolData,1));
    $this->assertTrue(!empty($newToolData['ActivationSmartTagID']), $message . ' ActivationSmartTagID');
    $this->assertTrue($newToolData['RelOwnerUserID'] == $UserID, $message . ' RelOwnerUserID');
    $this->assertTrue($newToolData['ActivationURL'] == $toolData['ActivationURL'], $message . ' ActivationURL');
    $this->assertTrue($newToolData['ReferenceName'] == $toolData['ReferenceName'], $message . ' ReferenceName');
    $this->assertTrue($newToolData['VarcharIndexed'] == $pluginid, $message . ' VarcharIndexed');
    $this->assertTrue($newToolData['RelIndexed'] == Plugin::hash_from_string($hashIndexed), $message . ' RelIndexed');
    $this->assertTrue($newToolData[$hashKey] == $hashIndexed, $message . ' key');
    $newToolID = $newToolData['ToolID'];
    $this->assertTrue($newToolID > $ToolID, $message . ' ToolID');

    // read by index (not toolid)
    $message = 'readTool';
    $toolArray = ToolPluginRPC::readTool($pluginid, $UserID, 0, $relIndexed, $hashKey, $hashIndexed);
    $this->assertTrue(!empty($toolArray), $message . ' readTool'.print_r($toolArray,1));
    $this->assertTrue($newToolID == $toolArray['ToolID'], $message . ' ToolID');
    $ToolObject = ToolPluginRPC::FromHashIndexed($pluginid, $hashKey, $hashIndexed);
    $this->assertTrue(!empty($toolArray), $message . ' FromHashIndexed'.print_r($ToolObject,1));
    if ($ToolObject) {
      $this->assertTrue($newToolID == $ToolObject->GetData('ToolID'), $message . ' ToolID');
    }
    $ToolObject = ToolPluginRPC::FromIndex($pluginid, Plugin::hash_from_string($hashIndexed));
    $this->assertTrue(!empty($toolArray), $message . ' FromIndex'.print_r($ToolObject,1));
    if ($ToolObject) {
      $this->assertTrue($newToolID == $ToolObject->GetData('ToolID'), $message . ' ToolID');
    }

    // update without toolid
    $message = 'writeTool update';
    $toolData['ActivationURL'] = 'http://www.example.com'; // the only change

    $newToolData = ToolPluginRPC::writeTool($pluginid, $relIndexed, $hashKey, $hashIndexed, $toolData);
    $this->assertTrue(!empty($newToolData), $message . ' writeTool '.print_r($newToolData,1));
    $this->assertTrue(!empty($newToolData['ActivationSmartTagID']), $message . ' ActivationSmartTagID');
    $this->assertTrue($newToolData['RelOwnerUserID'] == $UserID, $message . ' RelOwnerUserID');
    $this->assertTrue($newToolData['ActivationURL'] == $toolData['ActivationURL'], $message . ' ActivationURL');
    $this->assertTrue($newToolData['ReferenceName'] == $toolData['ReferenceName'], $message . ' ReferenceName');
    $this->assertTrue($newToolData['VarcharIndexed'] == $pluginid, $message . ' VarcharIndexed');
    $this->assertTrue($newToolData['RelIndexed'] == Plugin::hash_from_string($hashIndexed), $message . ' RelIndexed');
    $this->assertTrue($newToolData[$hashKey] == $hashIndexed, $message . ' key');
    $this->assertTrue($newToolID == $newToolData['ToolID'], $message . ' ToolID');

    /**
     * ToolPluginGeneral::InsertPlugin multiple Outbounds
     **/
    $message = 'InsertPlugin Multi-Outbounds';

    $pluginid = 'insertplugin2';
    $plugindata = <<<EOF
---
PluginName: TestInsertPlugin2
Hooks:
  connect:
    Types:
      - hook_connect # HOOK_TYPE_CONNECT
    Variables:
      '%Tool:ReferenceName%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: automation_name
        regex: /(.*)/
  out:
    Types:
      - hook_outbound
    validate:
      '%Subscriber:EmailAddress%':
        type: Key # VARIABLE_TYPE_KEY
    Request:
      url: $HttptestURL
      request:
        method: POST
        timeout: 10
        data:
          email: '%Subscriber:EmailAddress%'
    process:
      '%Tool:ActivationSmartTagID%':
        type: SmartTag # VARIABLE_TYPE_SMARTTAG
        category: 22 # Tag::CATEGORY_OUTBOUND
    on_error:
      error: Error
      message: Some error message.
  out2:
    Types:
      - hook_outbound
    validate:
      '%Subscriber:EmailAddress%':
        type: Key # VARIABLE_TYPE_KEY
    Request:
      url: $HttptestURL
      request:
        method: POST
        timeout: 10
        data:
          email: '%Subscriber:EmailAddress%'
    process:
      '%Tool:AnotherSmartTagID%':
        type: SmartTag # VARIABLE_TYPE_SMARTTAG
        category: 22 # Tag::CATEGORY_OUTBOUND
    on_error:
      error: Error
      message: Some error message.
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $parameters = new PluginParameters($plugin);
    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];
    $keys = Plugin::getKeys($variables);
    $parameters->resetScope($keys);
    $parameters->getParametersFromUser($keys, (array) $account);

    $request = [
      'automation_name' => 'My multi outbound tool',
    ];
    PluginHookConnectRedirect::validateEvent($parameters, $hookname, $request);
    $this->assertTrue($parameters['%Tool:ReferenceName%'] == $request['automation_name'], "$message check ReferenceName ".print_r($parameters->getParameters(),1));

    $ToolID = ToolPluginGeneral::InsertPlugin($plugin, $variables, $parameters);
    $this->assertTrue($ToolID > 0, $message . ' id');

    $ToolObject = ToolPluginGeneral::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), $message . ' FromID');

    $SmartTags = $ToolObject->GetSmartTags();
    $this->assertTrue(!empty($SmartTags['ActivationSmartTagID']), "$message GetSmartTags out".print_r($SmartTags,1));
    $this->assertTrue(!empty($SmartTags['AnotherSmartTagID']), "$message GetSmartTags out2");

    /**
     * ToolPluginGeneral::process_inbound_data
     */

    $message = "Plugin process_inbound_data";

    $pluginid = 'processinbounddataplugin';
    $plugindata = <<<EOF
---
PluginName: TestProcessInboundData
Hooks:
  connect:
    Types:
      - hook_connect # HOOK_TYPE_CONNECT
    Variables:
      '%Tool:ResultField%':
        type: CustomField
        widget: 0
        required: true
        default: ""
  in:
    Types:
      - hook_inbound
    process:
      '%Tool:RelIndexed%':
        type: Read # VARIABLE_TYPE_READ
        field: automation_id
        regex: /(.*)/
      '%Subscriber:EmailAddress%':
        type: Read # VARIABLE_TYPE_READ
        field: contact_email
        regex: /(.*)/
      '%Temp:RefID%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: automation_id
        regex: /(.*)/
      '%Tool:InboundTagID%':
        type: SmartTag # VARIABLE_TYPE_SMARTTAG
        category: 82
        name: An inbound tagging
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];

    /** @var ToolPluginGeneral $ToolObject */
    $reference = 4711;
    $Parameters = [
      '%User:UserID%' => $UserID,
      '%Tool:Name%' => "Tool $pluginid",
      '%Tool:ResultField%' => $ResultFieldID,
      '%Tool:RelIndexed%' => $reference,
    ];
    $parameters = new PluginParameters($plugin, $Parameters);

    $ToolID = ToolPluginGeneral::InsertPlugin($plugin, $variables, $parameters);
    $ToolObject = ToolPluginGeneral::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), $message . ' FromID'.print_r($ToolObject->GetData(),1));
    $this->assertTrue($ToolObject->GetData('ResultField') == $ResultFieldID, "$message InsertPlugin ResultField");
    $this->assertTrue($ToolObject->GetData('InboundTagID') > 0, "$message InboundTagID ");

    // INBOUND
    $inbound_message = "$message INBOUND";

    $variables = $plugin['Hooks']['in']['process'];

    $parameters = new PluginParameters($plugin);

    $data = [
      "toolid" => $ToolID,
      "automation_id" => $reference,
      "contact_email" => $EmailAddress1,
    ];

    $result = $parameters->process($variables, $data);
    $this->assertTrue(empty($result), $inbound_message . ' result'.print_r($result,1));
    $this->assertTrue($parameters['%Tool:ToolID%'] == $ToolID, "$inbound_message ToolID ".print_r($parameters->getParameters(),1));
    $this->assertTrue($parameters['%Subscriber:EmailAddress%'] == $EmailAddress1, "$inbound_message EmailAddress ");
    $this->assertTrue($parameters['%Temp:RefID%'] == $reference, "$inbound_message RefID ");
    $this->assertTrue($parameters['%Tool:InboundTagID%'] == $ToolObject->GetData('InboundTagID'), "$inbound_message InboundTagID ");
    $this->assertTrue(!empty(Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('InboundTagID'), $SubscriberID1, $ReferenceID)), $inbound_message . ' Subscriber tagged');

    /**
     * ToolPluginGeneralPublicAPI::klicktippapi_plugin_inbound
     **/
    $message = 'klicktippapi_plugin_inbound';

    $pluginid = 'inboundplugin';
    $plugindata = <<<EOF
---
PluginName: TestInboundPlugin
Hooks:
  connect:
    Types:
      - hook_connect # HOOK_TYPE_CONNECT
    Variables:
      '%Tool:RelIndexed%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: automation_id
        regex: /(.*)/
      '%UserVariable:Token%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: token
        regex: /(.*)/
        varfield: VarcharIndexed
  out:
    Types:
      - hook_outbound
    validate:
      '%Tool:PluginReadySmartTagID%':
        type: Key # VARIABLE_TYPE_KEY
      '%Tagging:%Tool:PluginReadySmartTagID%%':
        type: Key # VARIABLE_TYPE_KEY
      '%Temp:Tagging%':
        type: Validate # VARIABLE_TYPE_VALIDATE
        pattern: '%Tagging:%Tool:PluginReadySmartTagID%%'
        regex: /^$/ # empty(PluginReadySmartTagID)
    Request:
      url: $HttptestURL
      request:
        method: POST
        timeout: 10
        data: []
        headers:
          User-Agent: KlickTipp\/1.0
          Accept: application\/json
    process:
      '%Tool:ActivationSmartTagID%':
        type: SmartTag # VARIABLE_TYPE_SMARTTAG
        category: 22 # Tag::CATEGORY_OUTBOUND
        name: Activation
    on_error:
      error: Error
      message: Some error message.
  ready:
    Types:
      - hook_inbound
    validate:
      '%Temp:Inbound%':
        type: Validate # VARIABLE_TYPE_VALIDATE
        field: event
        regex: /^contact.notification_due$/
    process:        
      '%Tool:RelIndexed%':
        type: Read # VARIABLE_TYPE_READ
        field: automation_id
        regex: /(.*)/
      '%Subscriber:EmailAddress%':
        type: Read # VARIABLE_TYPE_READ
        field: contact_email
        regex: /(.*)/
      '%Tool:SomeField%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: payload
        regex: /\"somethingnumeric\"\:([\d].*),/
      '%Tool:PluginReadySmartTagID%':
        type: SmartTag # VARIABLE_TYPE_SMARTTAG
        category: 81 # Tag::CATEGORY_PLUGIN_INBOUND_READY
        name: Wowing %Tool:Name% versandbereit
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $parameters = new PluginParameters($plugin);
    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];
    $keys = Plugin::getKeys($variables);
    $parameters->resetScope($keys);
    $parameters->getParametersFromUser($keys, (array) $account);

    $reference = 4711;
    $request = [
      'automation_id' => $reference,
      'token' => "token$UserID",
    ];
    $parameters->process($variables, $request);

    $this->assertTrue($parameters['%Tool:RelIndexed%'] == $reference, "$message check RelIndexed ".print_r($parameters->getParameters(),1));

    $ToolID = ToolPluginGeneral::InsertPlugin($plugin, $variables, $parameters);
    $this->assertTrue($ToolID > 0, $message . ' id');

    $ToolObject = ToolPluginGeneral::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), $message . ' FromID');

    $ToolObject = ToolPluginGeneral::FromIndex($pluginid, $reference);
    $this->assertTrue(!empty($ToolObject), $message . ' FromRelIndexed');
    $this->assertTrue($ToolObject->GetData('ToolID') == $ToolID, "$message FromRelIndexed ToolID");
    $this->assertTrue($ToolObject->GetData('RelIndexed') == $reference, "$message FromRelIndexed RelIndexed");

    // simulate confirmation
    VarPluginData::SetPluginDataByParameter($pluginid, $variables, $parameters->getParameters());
    $result = VarPluginData::GetPluginDataByParameter($pluginid, $variables, $parameters->getParameters(), []);
    $this->assertTrue($result['%UserVariable:Token%'] == $request['token'], "$message check UserVariable");

    $data = [
      "event" => "contact.notification_due",
      "automation_id" => $reference,
      "contact_email" => $EmailAddress1,
      "payload" => [
        "landing_page_url" => "https://app.wowing.io/api/notifications/430c21f3-123/redirect",
        "files" => [
          [
            "mime_type" => "audio/mpeg",
            "path" => "https://cdn-staging.wowing.io/media/123.mp3",
            "somethingnumeric" => 555,
          ],
        ]
      ]
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    $this->assertTrue(empty($result), "$message inbound".print_r($result,1));

    // test Subscriber tagging
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginReadySmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginReadySmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('ActivationSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' ActivationSmartTagID');

    /**
     * TriggerOutboundEvent
     **/
    $message = 'TriggerOutboundEvent';

    // tagged with PluginReadySmartTagID - the validation checks an empty tagging, so this will fail

    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID1, $ReferenceID);
    $parameters = new PluginParameters($plugin);
    PluginHookConnectRedirect::prepare(
      $parameters,
      $plugin['Hooks']['out'],
      [
        'Tool' => $ToolObject->GetData(),
        'Taggings' => $Taggings
      ]
    );
    $this->assertTrue($parameters['%Tool:PluginReadySmartTagID%'] == $ToolObject->GetData('PluginReadySmartTagID'), "$message check PluginReadySmartTagID ".print_r($parameters->getParameters(),1));
    $this->assertTrue($parameters['%Tagging:%Tool:PluginReadySmartTagID%%'] == $Taggings[$ToolObject->GetData('PluginReadySmartTagID')]['SubscriptionDate'], "$message check Tagging".print_r($Taggings,1));

    $result = PluginHook::validateEvent($parameters, 'out');
    $this->assertTrue(!$result, "$message valid ".print_r($parameters->getParameters(),1));

    $ToolObject->TriggerOutboundEvent($SubscriberID1, $ReferenceID);
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('ActivationSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' ActivationSmartTagID');

    // untagged - so this is valid

    Subscribers::UntagSubscriber($UserID, $SubscriberID1, $ReferenceID, $ToolObject->GetData('PluginReadySmartTagID'));

    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID1, $ReferenceID);
    $parameters = new PluginParameters($plugin);
    PluginHookConnectRedirect::prepare(
      $parameters,
      $plugin['Hooks']['out'],
      [
        'Tool' => $ToolObject->GetData(),
        'Taggings' => $Taggings
      ]
    );
    $this->assertTrue($parameters['%Tool:PluginReadySmartTagID%'] == $ToolObject->GetData('PluginReadySmartTagID'), "$message check PluginReadySmartTagID ");
    $this->assertTrue(empty($parameters['%Tagging:%Tool:PluginReadySmartTagID%%']), "$message check Tagging");

    $result = PluginHook::validateEvent($parameters, 'out');
    $this->assertTrue($result, "$message valid ".print_r($parameters->getParameters(),1));

    $ToolObject->TriggerOutboundEvent($SubscriberID1, $ReferenceID);
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('ActivationSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' ActivationSmartTagID');

    /**
     * ToolPluginGeneralPublicAPI::auto_subscribe
     **/
    $message = 'auto_subscribe';

    // Single-Optin
    $ArrayFieldAndValues = array(
      'Name' => 'Some SOI List',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
    );
    $ListSOI = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($ListSOI > 0, $message . ' list');

    $EmailAddressDOI = "<EMAIL>";
    $EmailAddressSOI = "<EMAIL>";

    // missing email address
    $parameters = [
      '%Tool:RelListID%' => null,
      '%User:UserID%' => $UserID,
      '%Subscriber:EmailAddress%' => null,
      '%Subscriber:PhoneNumber%' => null,
    ];
    [$success, $data] = ToolPluginSimpletest::auto_subscribe($plugin, $parameters);
    $this->assertTrue(!$success, "$message success".print_r($data,1));
    $this->assertTrue($data['error'] == KLICKTIPPAPI_ERROR_EMAILADDRESS_SMSNUMBER_REQUIRED, "$message error");

    // DOI
    $parameters = [
      '%Tool:RelListID%' => null,
      '%User:UserID%' => $UserID,
      '%Subscriber:EmailAddress%' => $EmailAddressDOI,
      '%Subscriber:PhoneNumber%' => null,
    ];
    [$success, $data] = ToolPluginSimpletest::auto_subscribe($plugin, $parameters);
    $this->assertTrue($success, "$message success".print_r($data,1));
    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddressDOI);
    $this->assertTrue(!empty($Subscriber), "$message subscribed".print_r($Subscriber,1));
    $this->assertTrue($Subscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message SubscriptionStatus");

    // SOI
    $parameters = [
      '%Tool:RelListID%' => $ListSOI,
      '%User:UserID%' => $UserID,
      '%Subscriber:EmailAddress%' => $EmailAddressSOI,
      '%Subscriber:PhoneNumber%' => null,
      '%Tool:AssignTagID%' => $OptInSubscribeTo,
    ];
    [$success, $data] = ToolPluginSimpletest::auto_subscribe($plugin, $parameters);
    $this->assertTrue($success, "$message success".print_r($data,1));
    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddressSOI);
    $this->assertTrue(!empty($Subscriber), "$message subscribed".print_r($Subscriber,1));
    $this->assertTrue($Subscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SubscriptionStatus");
    $Taggings = Subscribers::RetrieveTagging($UserID, $OptInSubscribeTo, $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue(!empty($Taggings), $message . ' PluginReadySmartTagID');

    // CustomFields
    $parameters = [
      '%Tool:RelListID%' => $ListSOI,
      '%User:UserID%' => $UserID,
      '%Subscriber:EmailAddress%' => $EmailAddressSOI,
      '%Subscriber:CustomFieldFirstName%' => 'Max',
      '%Subscriber:CustomFieldLastName%' => 'Moritz',
    ];
    [$success, $data] = ToolPluginSimpletest::auto_subscribe($plugin, $parameters);
    $this->assertTrue($success, "$message success".print_r($data,1));
    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddressSOI);
    $this->assertTrue(!empty($Subscriber), "$message subscribed".print_r($Subscriber,1));
    $this->assertTrue($Subscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SubscriptionStatus");
    $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($FullSubscriberWithFields['CustomFieldFirstName'] == 'Max', "$message CustomFieldFirstName".print_r($FullSubscriberWithFields,1));
    $this->assertTrue($FullSubscriberWithFields['CustomFieldLastName'] == 'Moritz', "$message CustomFieldLastName");

    // CustomFieldPlugin
    $parameters = [
      '%Tool:RelListID%' => $ListSOI,
      '%User:UserID%' => $UserID,
      '%Subscriber:EmailAddress%' => $EmailAddressSOI,
      '%Subscriber:CustomFieldFirstName%' => 'Max',
      '%Subscriber:CustomFieldLastName%' => 'Moritz',
      "%Subscriber:CustomFieldPlugin:Link-$ToolID%" => 'landing page',
      "%Subscriber:CustomFieldPlugin:Image-$ToolID%" => 'preview image',
    ];
    [$success, $data] = ToolPluginSimpletest::auto_subscribe($plugin, $parameters);
    $this->assertTrue($success, "$message success".print_r($data,1));
    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddressSOI);
    $this->assertTrue(!empty($Subscriber), "$message subscribed".print_r($Subscriber,1));
    $this->assertTrue($Subscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SubscriptionStatus");
    $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($FullSubscriberWithFields['CustomFieldFirstName'] == 'Max', "$message CustomFieldFirstName".print_r($FullSubscriberWithFields,1));
    $this->assertTrue($FullSubscriberWithFields['CustomFieldLastName'] == 'Moritz', "$message CustomFieldLastName");
    //check WowingLandingPage
    $value = CustomFields::GetCustomFieldData($UserID, $Subscriber['SubscriberID'], CustomFields::$GlobalCustomFieldDefs['Plugin'], $ReferenceID, "Link-$ToolID");
    $this->assertTrue($value == 'landing page', "$message WowLP");
    // check WowPI
    $value = CustomFields::GetCustomFieldData($UserID, $Subscriber['SubscriberID'], CustomFields::$GlobalCustomFieldDefs['Plugin'], $ReferenceID, "Image-$ToolID");
    $this->assertTrue($value == 'preview image', "$message WowPI");

    /**
     * Outbound on_error
     **/
    $message = 'on_error';

    $HttptestCodeURL = 'https://'.KLICKTIPP_DOMAIN.'/kthttptest/httpcode?secret=' . KLICKTIPP_HTTPTEST_SECRET;

    $pluginid = 'onerrorplugin';
    $plugindata = <<<EOF
---
PluginName: TestOnErrorPlugin
Hooks:
  out:
    Types:
      - hook_outbound
    validate:
      '%Subscriber:EmailAddress%':
        type: Key # VARIABLE_TYPE_KEY
    Request:
      url: '$HttptestCodeURL'
      request:
        method: POST
        timeout: 10
        data:
          email: '%Subscriber:EmailAddress%'
          # send result data for test (see klicktipp_httptest_callback)
          httpcode: 400
          error: Bad Request
          code: "3027"
          message: "Webinar host can not register for the webinar."
    on_error:
      error: '(%Temp:Code%) %Temp:Error%'
      message: 'Zoom Registration Outbound "@name" fehlgeschlagen: "@error"'
      Variables:
        '%Temp:Error%':
          type: Inbound # VARIABLE_TYPE_INBOUND
          field: data|message
          regex: /(.*)/
        '%Temp:Code%':
          type: Inbound # VARIABLE_TYPE_INBOUND
          field: data|code
          regex: /(.*)/
...
EOF;

    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    /** @var ToolPluginGeneral $ToolObject */
    $reference = 4711;
    $Parameters = [
      '%User:UserID%' => $UserID,
      '%Tool:Name%' => "Tool $pluginid",
      '%Tool:ResultField%' => $ResultFieldID,
      '%Tool:RelIndexed%' => $reference,
    ];
    $parameters = new PluginParameters($plugin, $Parameters);
    $ToolID = ToolPluginGeneral::InsertPlugin($plugin, [], $parameters);
    $ToolObject = ToolPluginGeneral::FromID($UserID, $ToolID);
    $this->assertTrue(!empty($ToolObject), $message . ' FromID'.print_r($ToolObject->GetData(),1));

    // simulate failed outbound
    $ToolObject->TriggerOutboundEvent($SubscriberID1, $ReferenceID);

    $history = Subscribers::ReadHistory($UserID, $SubscriberID1, array('HistoryDate' => 'DESC'), 0, 10, [Subscribers::HISTORY_OUTBOUND_CALL_FAILED]);
    $found = FALSE;
    foreach ($history as $entry) {
      if ($entry['HistoryType'] == Subscribers::HISTORY_OUTBOUND_CALL_FAILED) {
        $this->assertTrue(strpos($entry['Text'], $plugin['Hooks']['out']['Request']['request']['data']['code']), "$message code");
        $this->assertTrue(strpos($entry['Text'], $plugin['Hooks']['out']['Request']['request']['data']['message']), "$message message");
        $found = TRUE;
        break;
      }
    }
    $this->assertTrue($found, "$message outbound found".print_r($history,1));

    /**
     *  Placeholders
     **/
    $message = 'placeholders';

    $pluginid = 'placeholderplugin';
    $thumbnail = APP_URL.'misc/plugins/videothumnail.jpg';
    $plugindata = <<<EOF
---
PluginName: TestPlaceholderPlugin
ListBuilding:
  auto_subscribe: 1
Placeholders:
  index1:
    types:
      - 1 # ToolPluginGeneral::PLACEHOLDER_TYPE_STANDARD
    email_types:
      - 10 # Emails::TYPE_AUTOMATIONEMAIL
    dropdown: '%Tool:Name%'
    html: <a href="%Subscriber:CustomFieldPlugin:Link-%Tool:ToolID%%"><img src="%Subscriber:CustomFieldPlugin:Image-%Tool:ToolID%%"/></a></p>
    plain: 'Vollständige Nachricht anschauen: %Subscriber:CustomFieldPlugin:Link-%Tool:ToolID%%'
    sms: '%Subscriber:CustomFieldPlugin:Link-%Tool:ToolID%%'
    preview_placeholder: '%Subscriber:CustomFieldPlugin:Image-%Tool:ToolID%%'
    preview_image: $thumbnail
Hooks:
  connect:
    Types:
      - hook_connect # HOOK_TYPE_CONNECT
    Variables:
      '%Tool:RelIndexed%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: automation_id
        regex: /(.*)/
      '%UserVariable:Token%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: token
        regex: /(.*)/
        varfield: VarcharIndexed
  ready:
    Types:
      - hook_inbound
    validate:
      '%Temp:Inbound%':
        type: Validate # VARIABLE_TYPE_VALIDATE
        field: event
        regex: /^contact.notification_due$/
    process:
      '%Tool:RelIndexed%':
        type: Read # VARIABLE_TYPE_READ
        field: automation_id
        regex: /(.*)/
      '%Subscriber:CustomFieldPlugin:Link-%Tool:ToolID%%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: payload
        # json is: "landing_page_url":"https:\/\/app.wowing.io\/api\/notifications\/430c21f3-123\/redirect"
        regex: /\"landing_page_url\"\:\"([^\"]*)\"/
        default: '#'
      '%Subscriber:CustomFieldPlugin:Image-%Tool:ToolID%%':
        type: Inbound # VARIABLE_TYPE_INBOUND
        field: payload
        # json is: "preview_file_path":[{"mime_type":"image\/png","path":"https:\/\/cdn-staging.wowing.io\/assets\/audio-preview.png"}]
        regex: /\"preview_file_path\".*?\"path\"\:\"([^\"]*)\"/
        default: /misc/plugins/videothumnail.jpg
      '%Subscriber:EmailAddress%':
        type: Write # VARIABLE_TYPE_WRITE
        field: contact_email
        regex: /(.*)/
      '%Tool:PluginReadySmartTagID%':
        type: SmartTag # VARIABLE_TYPE_SMARTTAG
        category: 81 # Tag::CATEGORY_PLUGIN_INBOUND_READY
        name: Wowing %Tool:Name% versandbereit
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];

    $reference = 3344;
    $token = "anothertoken$UserID";
    $Parameters = [
      '%User:UserID%' => $account->uid,
      '%Tool:ReferenceName%' => 'The Tool',
      '%Tool:RelIndexed%' => $reference,
      '%UserVariable:Token%' => $token,
    ];
    $parameters = new PluginParameters($plugin, $Parameters);
    $ToolID = ToolPluginGeneral::InsertPlugin($plugin, $variables, $parameters);
    $this->assertTrue($ToolID > 0, $message . ' id');

    $ToolObject = ToolPluginGeneral::FromIndex($pluginid, $reference);
    $this->assertTrue(!empty($ToolObject), $message . ' FromRelIndexed');
    $this->assertTrue($ToolObject->GetData('ToolID') == $ToolID, "$message FromRelIndexed ToolID");
    $this->assertTrue($ToolObject->GetData('RelIndexed') == $reference, "$message FromRelIndexed RelIndexed");

    // simulate confirmation
    VarPluginData::SetPluginDataByParameter($pluginid, $variables, $parameters->getParameters());
    $result = VarPluginData::GetPluginDataByParameter($pluginid, $variables, $parameters->getParameters(), []);
    $this->assertTrue($result['%UserVariable:Token%'] == $token, "$message check UserVariable");

    // --- check email editor

    $Placeholders = [];
    $ToolObject->GetPlaceholders($Placeholders, 'dropdown', Emails::TYPE_NEWSLETTEREMAIL);
    $this->assertTrue(empty($Placeholders), "$message check TYPE_NEWSLETTEREMAIL");
    $ToolObject->GetPlaceholders($Placeholders, 'dropdown', Emails::TYPE_AUTOMATIONEMAIL);
    $this->assertTrue(count($Placeholders) == 1, "$message check TYPE_AUTOMATIONEMAIL".print_r($Placeholders,1));
    // check dropdown display name (default pattern defined in CreateUnqiueName)
    $this->assertTrue(current($Placeholders) == 'TestPlaceholderPlugin The Tool', "$message check placeholder name in dialog");

    // --- create subscriber with custom fields set by inbound

    $landingpage = "https://app.wowing.io/api/notifications/430c21f3-123/redirect";
    $previewimage = "https://cdn-staging.wowing.io/assets/audio-preview.png";
    $data = [
      "event" => "contact.notification_due",
      "automation_id" => $reference,
      "contact_email" => $EmailAddress1,
      "payload" => [
        "landing_page_url" => $landingpage,
        "preview_file_path" => [
          [
            "mime_type" => "image/png",
            "path" => $previewimage
          ]
        ],
        "files" => [
          [
            "mime_type" => "audio/mpeg",
            "path" => "https://cdn-staging.wowing.io/media/123.mp3",
          ],
        ],
      ]
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    $this->assertTrue(empty($result), "$message inbound".print_r($result,1));

    // check subscriber
    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddress1);
    $this->assertTrue(!empty($Subscriber), "$message subscribed".print_r($Subscriber,1));
    $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($FullSubscriberWithFields['EmailAddress'] == $EmailAddress1, "$message EmailAddress");

    //check WowingLandingPage
    $value = CustomFields::GetCustomFieldData($UserID, $Subscriber['SubscriberID'], CustomFields::$GlobalCustomFieldDefs['Plugin'], $ReferenceID, "Link-$ToolID");
    $this->assertTrue($value == $data['payload']['landing_page_url'], "$message WowLP ".$value);
    $this->assertTrue($value == $landingpage, "$message WowLP ".$landingpage);
    // check WowPI
    $value = CustomFields::GetCustomFieldData($UserID, $Subscriber['SubscriberID'], CustomFields::$GlobalCustomFieldDefs['Plugin'], $ReferenceID, "Image-$ToolID");
    $this->assertTrue($value == $data['payload']['preview_file_path'][0]['path'], "$message WowPI ".$value);

    // --- check email generation

    $ArrayUser = (array) $account;

    $LandingPageParam = '%Subscriber:CustomFieldPlugin:Link-%Tool:ToolID%%';
    $PreviewImageParam = '%Subscriber:CustomFieldPlugin:Image-%Tool:ToolID%%';

    $paramtool = ['%Tool:ToolID%' => $ToolID];

    //email dialog placeholder dropdown content
    $LandingPagePlaceholder = strtr($LandingPageParam, $paramtool);
    $PreviewImagePlaceholder = strtr($PreviewImageParam, $paramtool);

    // email template content
    $ContentPlain = strtr($plugin['Placeholders']['index1']['plain'], $paramtool);
    $ContentHTML = strtr($plugin['Placeholders']['index1']['html'], $paramtool);

    // final email content
    $paramfinal = [$LandingPageParam => $landingpage, $PreviewImageParam => $previewimage];
    $ExpectedPlain = strtr($plugin['Placeholders']['index1']['plain'], $paramfinal);
    $ExpectedHTML = strtr($plugin['Placeholders']['index1']['html'], $paramfinal);

    // create a fresh email
    $ArrayFieldAndValues = array(
      'RelUserID' => $UserID,
      'EmailName' => 'Plugin',
      'ReportSpamEnabled' => 1,
      'Subject' => 'Plugin',
      'PlainContent' => $ContentPlain,
      'HTMLContent' => $ContentHTML,
    );
    $EmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($EmailID > 0, $message . ' create email');

    $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);
    $ArrayRevision = Emails::RetrieveLastestEmailRevision($ArrayEmail);
    $ArraySignature = [];

    $SenderDomain = DomainSet::SelectValidSenderDomainById($UserID, $EmailID);

    $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['PlainContent']);
    $this->assertTrue(in_array($LandingPagePlaceholder, $Params), "$message $LandingPagePlaceholder ".print_r($Params,1));
    $this->assertTrue(!in_array($PreviewImagePlaceholder, $Params), "$message $PreviewImagePlaceholder");

    $Params = Personalization::PersonalizeExtractSingle($ArrayEmail['HTMLContent'], $Params);
    $this->assertTrue(in_array($LandingPagePlaceholder, $Params), "$message $LandingPagePlaceholder ".print_r($Params,1));
    $this->assertTrue(in_array($PreviewImagePlaceholder, $Params), "$message $PreviewImagePlaceholder");

    $PlainContent = Personalization::PersonalizePrepareContent($ArrayEmail['PlainContent'], $Params, $ArrayRevision, $ArraySignature);
    $HTMLContent = Personalization::PersonalizePrepareContent($ArrayEmail['HTMLContent'], $Params, $ArrayRevision, $ArraySignature);

    // preview
    $Replacements = Personalization::PersonalizeAssign($Params, $ArrayUser, $ArrayEmail, 7777, $FullSubscriberWithFields, $ReferenceID, TRUE, $SenderDomain);
    $this->assertTrue(empty($Replacements[$LandingPagePlaceholder]), $message . ' replacements PREVIEW landingpage '.print_r($Replacements,1));
    $this->assertTrue($Replacements[$PreviewImagePlaceholder] == $thumbnail, $message . ' replacements PREVIEW preview image');

    // not preview
    $Replacements = Personalization::PersonalizeAssign($Params, $ArrayUser, $ArrayEmail, 7777, $FullSubscriberWithFields, $ReferenceID, FALSE, $SenderDomain);
    $this->assertTrue($Replacements[$LandingPagePlaceholder] == $landingpage, $message . ' replacements landingpage '.print_r($Replacements,1));
    $this->assertTrue($Replacements[$PreviewImagePlaceholder] == $previewimage, $message . ' replacements preview image');

    $PlainContent = Personalization::PersonalizeReplace($PlainContent, $Replacements);
    $HTMLContent = Personalization::PersonalizeReplace($HTMLContent, $Replacements);

    // check plain content
    $this->assertTrue(strpos($PlainContent, $landingpage) !== FALSE, $message . ' plain: landingpage '.$PlainContent);
    $this->assertTrue(strpos($PlainContent, $previewimage) === FALSE, $message . ' plain: preview image');
    $this->assertTrue($PlainContent == $ExpectedPlain, $message . ' plain '.$ExpectedPlain);

    // check HTML content
    $this->assertTrue(strpos($HTMLContent, $landingpage) !== FALSE, $message . ' HTML: landingpage '.htmlspecialchars($HTMLContent));
    $this->assertTrue(strpos($HTMLContent, $previewimage) !== FALSE, $message . ' HTML: preview image');
    $this->assertTrue($HTMLContent == $ExpectedHTML, $message . ' HTML '.htmlspecialchars($ExpectedHTML));

    /**
     *  Delete
     **/
    $message = 'Delete';

    // fetch all created plugins
    $DBData = db_query("SELECT Seqno FROM {user_variables} WHERE RelOwnerUserID = :RelOwnerUserID AND VarType = :VarType", [
      ':RelOwnerUserID' => $UserID,
      ':VarType' => UserVariables::VARTYPE_PLUGINDATA,
    ])->fetchCol();
    $old_count = count($DBData);
    $this->assertTrue(!empty($DBData), $message . ' plugins with user data '.print_r($DBData,1));

    $pluginid = 'placeholderplugin';
    $pluginno = Plugin::get_pluginno($pluginid);
    $this->assertTrue(in_array($pluginno, $DBData), $message . ' ' . $pluginid);

    // fetch all tools from plugin
    $tools = db_query("SELECT ToolID FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID ".
      " AND ToolType = :ToolType AND VarcharIndexed = :VarcharIndexed", array(
      ':RelOwnerUserID' => $UserID,
      ':ToolType' => MarketingTools::TOOL_TYPE_PLUGIN,
      ':VarcharIndexed' => $pluginid,
    ))->fetchCol();
    $this->assertTrue(!empty($tools), $message . ' tools '.print_r($tools,1));

    // delete tools
    foreach($tools as $toolid) {
      $ToolObject = ToolPluginGeneral::FromID($UserID, $toolid);
      ToolPluginGeneral::DeleteDB($ToolObject->GetData());
    }

    // check tools deleted
    $tools = db_query("SELECT 1 FROM {marketing_tools} WHERE RelOwnerUserID = :RelOwnerUserID ".
      " AND ToolType = :ToolType AND VarcharIndexed = :VarcharIndexed", array(
      ':RelOwnerUserID' => $UserID,
      ':ToolType' => ToolPluginGeneral::$ToolType,
      ':VarcharIndexed' => $pluginid,
    ))->fetchField();
    $this->assertTrue(empty($tools), $message . ' tools deleted');

    // check user var deleted
    $DBData = db_query("SELECT Seqno FROM {user_variables} WHERE RelOwnerUserID = :RelOwnerUserID AND VarType = :VarType", [
      ':RelOwnerUserID' => $UserID,
      ':VarType' => UserVariables::VARTYPE_PLUGINDATA,
    ])->fetchCol();
    $this->assertTrue(count($DBData) == $old_count - 1, $message . ' plugins with user data '.print_r($DBData,1));
    $this->assertTrue(!in_array($pluginid, $DBData), $message . ' removed ' . $pluginid);

    //TODO test CampaignsProcessFlow::ValidateOutbound($ArrayOutbound, $state, &$TodoList, $SplittestVariant = -1)


    /**
     * test helper functions for type "Calc"
     */

    // use the plugin field "IntID" to test the function pmUpdateCustomFieldData()
    $PCF_IntID = 'IntID-'.$ToolID;
    $new_value = (string)rand(1, 100);
    Plugin::pmUpdateCustomFieldData($UserID, $SubscriberID1, $ReferenceID, "%Subscriber:CustomFieldPlugin:$PCF_IntID%", $new_value);
    $CF_info = ToolPluginGeneral::GetPluginCustomFieldInformation($PCF_IntID, '', []);
    $CF_value = CustomFields::GetCustomFieldData($UserID, $SubscriberID1, $CF_info, $ReferenceID);
    $this->assertTrue($CF_value === $new_value, "pmUpdateCustomFieldData() | write and read plugin custom field data");

    // -------------------------

    // tests for pmCalculate()
    // structure: [ 'calculation' => 'expected result' ]
    $rnd_1 = rand(1, 100);
    $rnd_2 = rand(1, 100);
    $sum = $rnd_1 + $rnd_2;
    $tests_calc = [
      '0 + 1' => 1,
      '5 + 5' => 10,
      '4 - 2' => 2,
      '3 * 3' => 9,
      '8 / 2' => 4,
      '4 ^ 2' => 16,
      '2 + 4 * 5 - 3' => 19,
      '3 + 3;DROP DATABASE klicktipp' => 0, // pmCalculate must not allow characters since it opens up attack vectors
      'add something to 7' => 0,
      $rnd_1 . ' + ' . $rnd_2 => $sum,
      '0 && 1' => 0,
      '5 && 2' => 1,
      '4 || 0' => 1,
      '0 || 0' => 0,
      'true || false' => 0, // TODO: make pmCalculate throw an exception instead of returning zero
    ];

    $success = true;
    $failed_calculation = '';
    foreach ($tests_calc as $calculation => $expected) {
      $result = Plugin::pmCalculate($calculation);
      if ($result !== $expected) {
        $success = false;
        $failed_calculation = ' | '.$calculation.' returned '.$result.' (expected '.$expected.')';
        break;
      }
    }

    $this->assertTrue($success, "pmCalculate() | arithmetic and boolean calculations".$failed_calculation);

    // -------------------------

    $url = Core::EncryptURL([
      'ToolID' => $ToolID,
      'SubscriberID' => $SubscriberID1,
      'ReferenceID' => $ReferenceID,
    ], 'plugin_redirect_params');
    $exploded = explode('/', $url);
    $codedParams = end($exploded);
    $decodedParams = Core::DecryptURL($codedParams);

    $decodedToolID = $decodedParams['%Tool:ToolID%'] ?? 0;
    $decodedSubscriberID = $decodedParams['%Subscriber:SubscriberID%'] ?? 0;
    $decodedReferenceID = $decodedParams['%Subscriber:ReferenceID%'] ?? 0;

    $this->assertTrue($decodedToolID == $ToolID, "encrypt and decrypt codedParams | ToolID: $decodedToolID (expected: $ToolID)");
    $this->assertTrue($decodedSubscriberID == $SubscriberID1, "encrypt and decrypt codedParams | SubscriberID: $decodedSubscriberID (expected: $SubscriberID1)");
    $this->assertTrue($decodedReferenceID == $ReferenceID, "encrypt and decrypt codedParams | ReferenceID: $decodedReferenceID (expected: $ReferenceID)");

    // -------------------------


    // check if pmGetCookie() returns the correct value

    $testCookieValue = rand(1, 1000);
    $_COOKIE['KTREFERRER'.$ToolID] = strval($testCookieValue);
    $this->assertTrue(Plugin::pmGetCookie('KTREFERRER'.$ToolID) == $testCookieValue, "pmGetCookie()");



    /**
     *  Repeat, Pop
     **/
    $message = 'Repeat';

    $pluginid = 'repeatplugin';
    $plugindata = <<<EOF
---
PluginName: TestRepeatPlugin
Hooks:
  complexrepeat:
    Types:
      - hook_inbound
    validate:
      '%Temp:Inbound%':
        type: Validate
        field: event
        regex: /^repeat$/
    process:
      '%Temp:PreviousChoice%':
        # loop through previous list: take and remove the last choice
        type: Pop
        field: previous_data|list
        regex: /(.*)/
      '%Temp:Continue%':
        # skip if this choice is still in list
        type: Repeat
        field: "data|list"
        regex: "/(%Temp:PreviousChoice%)/"
      '%Temp:Repeat%':
        # repeat as long as values in previous list
        type: Repeat
        field: "previous_data|list"
        regex: /\[(.)+\]/
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check");

    $choice1 = "choice1";
    $choice2 = "choice2";

    $event = array(
      'event' => 'repeat',
      'data' => array(
        'date_created' => '2024-01-24T15:53:02.029000+00:00',
        'list' => array(
          $choice1,
        ),
      ),
      'previous_data' => array(
        'list' => array(
          $choice1,
          $choice2
        ),
        'date_updated' => '2024-01-24T15:53:52.565000+00:00',
      ),
    );
    $_GET['q'] = "api/plugin/inbound/$pluginid";

    // execute it step by step

    $temp = $event;
    $parameters = [];

    // 1st run = pop "choice2" in ["choice1"] ?
    $message = '1st Repeat';
    // %Temp:PreviousChoice% POP
    $value = Plugin::getInboundField($plugin['Hooks']['complexrepeat']['process']['%Temp:PreviousChoice%'], $temp, $parameters);
    $this->assertTrue($value == htmlspecialchars(json_encode($temp['previous_data']['list'])), "$message inbound %Temp:PreviousChoice%=".$value);
    $value = Plugin::popInboundField($plugin['Hooks']['complexrepeat']['process']['%Temp:PreviousChoice%'], $temp, $parameters);
    $this->assertTrue($value == $choice2, "$message pop %Temp:PreviousChoice%=".$value." ".print_r($temp,1));
    $parameters['%Temp:PreviousChoice%'] = $value;
    // %Temp:Continue% CONTINUE
    $value = Plugin::getInboundField($plugin['Hooks']['complexrepeat']['process']['%Temp:Continue%'], $temp, $parameters);
    $this->assertTrue(!$value, "$message Continue CONTINUE");
    // %Temp:Repeat% REPEAT
    $value = Plugin::getInboundField($plugin['Hooks']['complexrepeat']['process']['%Temp:Repeat%'], $temp, $parameters);
    $this->assertTrue($value == '&quot;', "$message Repeat REPEAT");

    // 2nd run = pop "choice1" in ["choice1"] ?
    $message = '2nd Repeat';
    // %Temp:PreviousChoice% POP
    $value = Plugin::getInboundField($plugin['Hooks']['complexrepeat']['process']['%Temp:PreviousChoice%'], $temp, $parameters);
    $this->assertTrue($value == htmlspecialchars(json_encode($temp['previous_data']['list'])), "$message inbound %Temp:PreviousChoice%=".$value);
    $value = Plugin::popInboundField($plugin['Hooks']['complexrepeat']['process']['%Temp:PreviousChoice%'], $temp, $parameters);
    $this->assertTrue($value == $choice1, "$message pop %Temp:PreviousChoice%=".$value." ".print_r([$temp, $parameters],1));
    $parameters['%Temp:PreviousChoice%'] = $value;
    // %Temp:Continue% REPEAT
    $value = Plugin::getInboundField($plugin['Hooks']['complexrepeat']['process']['%Temp:Continue%'], $temp, $parameters);
    $this->assertTrue($value, "$message Continue REPEAT (SKIP)".print_r([$value, bin2hex($value), $temp, $parameters],1));

    // 3rd run = pop (empty) in ["choice1"] ?
    $message = '3rd Repeat';
    // %Temp:PreviousChoice% POP
    $value = Plugin::getInboundField($plugin['Hooks']['complexrepeat']['process']['%Temp:PreviousChoice%'], $temp, $parameters);
    $this->assertTrue($value == json_encode($temp['previous_data']['list']), "$message inbound %Temp:PreviousChoice%=".$value);
    $value = Plugin::popInboundField($plugin['Hooks']['complexrepeat']['process']['%Temp:PreviousChoice%'], $temp, $parameters);
    $this->assertTrue(empty($value), "$message pop %Temp:PreviousChoice%=".$value." ".print_r($temp,1));

    // run it all

    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $event);
    $this->assertTrue(empty($result), "$message no errors".print_r($result,1));

  }


}

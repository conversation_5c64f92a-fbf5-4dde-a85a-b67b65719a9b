<?php

use App\Klicktipp\AutoresponderQueue;
use App\Klicktipp\AutoResponders;
use App\Klicktipp\BounceEngine;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\NewsletterQueue;
use App\Klicktipp\SplitTests;
use App\Klicktipp\Subscribers;
use App\Klicktipp\SubscribersSearch;
use App\Klicktipp\SubscribersSearchDialog;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;
use App\Klicktipp\ToolOutboundGeneral;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\VarSubscriberAudience;

class coreapiTransactionalEmailsMVTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'coreapi TransactionalEmails Multivalue (NL, AR, Outbounds)',
      'description' => 'Test classes/transaction_emails.inc',
      'group' => 'Klicktipp',
    );

  }

  function testTransactionalMVEmails() {

    unset($_SESSION['subscriber_search_data']);

    // Load other modules - Start
    Libraries::include('cron_send.inc', '/cli');
    Libraries::include("subscriber.inc", "/forms");
    // Load other modules - End

    Libraries::include('full_user.inc', '/tests/includes');
    Libraries::include('campaigns.inc', '/tests/includes');
    Libraries::include('queue.inc', '/tests/includes');
    Libraries::include('api.inc', '/tests/includes');
    //\App\Klicktipp\Libraries::include('transactional.inc', '/tests/includes');

    /*
     * prepare
     */
    $UserID = 1;

    $ReferenceID0 = 0;
    $ReferenceID4711 = 4711;

    // manipulate user cache
    $NexmoApiKey = 'nexmoapikey';
    $NexmoApiSecret = 'nexmoapisecret';
    $account = user_load($UserID);
    $edit = array();
    $edit['UserSettings']['SMSSettings']['NexmoApiKey'] = $NexmoApiKey;
    $edit['UserSettings']['SMSSettings']['NexmoApiSecret'] = $NexmoApiSecret;
    $edit['UserPrivileges'] = array('UnlimitedEmailsPerDay' => 1);
    user_save($account, $edit);
    $account = user_load($UserID);
    $this->assertTrue($account->UserSettings['SMSSettings']['NexmoApiSecret'] == $NexmoApiSecret, ' NexmoApiSecret');
    $this->assertTrue(user_access('access sms marketing', $account), 'sms access');

    // create tag
    $message = 'create tag';
    $MVManualTagID = Tag::CreateManualTag($UserID, 'manual test tag 1', '', '', null, 1);
    $this->assertTrue($MVManualTagID > 0, "$message #$MVManualTagID");

    $ManualTagID2 = Tag::CreateManualTag($UserID, 'manual test tag 2', '', '', null, 0);
    $this->assertTrue($ManualTagID2 > 0, "$message #$ManualTagID2");

    // custom field
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'datetime',
      'FieldTypeEnum' => CustomFields::TYPE_DATETIME,
      'MultiValue' => 1,
    );
    $DateTimeCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
    $DateTimeCustomField = CustomFields::RetrieveCustomField($DateTimeCustomFieldID, $UserID);
    $this->assertTrue(is_array($DateTimeCustomField), $message . ' datetime field exists');

    // custom field
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'time',
      'FieldTypeEnum' => CustomFields::TYPE_TIME,
    );
    $TimeCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
    $TimeCustomField = CustomFields::RetrieveCustomField($TimeCustomFieldID, $UserID);
    $this->assertTrue(is_array($TimeCustomField), $message . ' time field exists');

    // custom field
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'number',
      'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
    );
    $NumberCustomFieldID = CustomFields::Create($ArrayFieldAndValues);
    $NumberCustomField = CustomFields::RetrieveCustomField($NumberCustomFieldID, $UserID);
    $this->assertTrue(is_array($NumberCustomField), $message . ' number field exists');

    // create list
    $message = 'create subscriber list';
    $ArrayFieldAndValues = array(
      'Name' => 'test list',
      'RelOwnerUserID' => $UserID,
    );
    $SubscriberListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($SubscriberListID > 0, $message);
    $ArraySubscriberList = Lists::RetrieveListByID($UserID, $SubscriberListID);
    $this->assertTrue($ArraySubscriberList['OptInModeEnum'] == Lists::OPTIN_MODE_DOUBLE, $message . ' optin mode');

    // create outbound (so first tagging will trigger outbounds)
    $ToolID = ToolOutboundGeneral::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'Name' => 'outboundname',
      'ActivationURL' => url('https://'.KLICKTIPP_DOMAIN.'/kthttptest', array('query' => array('secret' => KLICKTIPP_HTTPTEST_SECRET))),
      'ActivationTagID' => [$MVManualTagID],
    ));
    $this->assertTrue($ToolID > 0, $message . ' id');
    $Outbound = ToolOutboundGeneral::FromID($UserID, $ToolID);
    $OutboundSmartTag = $Outbound->GetData('ActivationSmartTagID');
    $this->assertTrue(!empty($OutboundSmartTag), $message . ' ActivationSmartTagID');

    // future (used for AR with start time from CF
    $future = time() + 3600;

    $fieldValues1 = array(
      'CustomFieldFirstName' => 'start-startmiddle-middle-endmiddle-end',
      'CustomFieldBirthday' => strtotime('1981-09-12 08:02:00'),
      "CustomField$DateTimeCustomFieldID" => $future,
      "CustomField$TimeCustomFieldID" => 8 * 3600 + 15 * 60,
      "CustomField$NumberCustomFieldID" => 4711,
    );

    //empty search mask returns all subscriptions
    $SearchMsg = "no subscribers yet";
    $SearchMask = array();
    $this->SearchTest($UserID, $SearchMask, 0, 0, 0, $SearchMsg);

    // create subscriber 1 : email subscribed, tagged 1
    $message = 'create subscriber 1';
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'ReferenceID' => $ReferenceID4711,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $MVManualTagID,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
      'OtherFields' => $fieldValues1,
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID = $Result[1];
    // check multivalue fields
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID4711, TRUE);
    $this->assertTrue(strlen($Subscription["CustomFieldFirstName"]) > 0, "$message CustomFieldFirstName $ReferenceID4711". print_r($Subscription,1));
    $this->assertTrue($Subscription["CustomField$DateTimeCustomFieldID"] == $future, "$message CustomField$DateTimeCustomFieldID $ReferenceID4711");
    $this->assertTrue($Subscription["CustomField$NumberCustomFieldID"] > 0, "$message CustomField$NumberCustomFieldID $ReferenceID4711");
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID0, TRUE);
    $this->assertTrue(empty($Subscription), "$message CustomFieldFirstName $ReferenceID0". print_r($Subscription,1));
    // check multivalue tagging
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID4711, TRUE);
    $this->assertTrue(in_array($MVManualTagID, $Taggings), "$message tagging $ReferenceID4711 $SubscriberID");
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID0, TRUE);
    $this->assertTrue(empty($Taggings), "$message tagging $ReferenceID0");

    //search mask is still empty to return all subscriptions
    //there should be now 1 subscriber with 1 email subscription
    $SearchMsg = "1 subscriber with email only added, MV tagged, Ref: $ReferenceID4711";
    $SearchMask = array();
    $this->SearchTest($UserID, $SearchMask, 1, 1, 1, $SearchMsg);

    // create subscriber 1b : email subscribed, not tagged
    $message = 'create subscriber 1b';
    $EmailAddress1b = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'ReferenceID' => $ReferenceID0,
      'EmailAddress' => $EmailAddress1b,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID1b = $Result[1];
    // check multivalue tagging
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID1b, $ReferenceID4711, TRUE);
    $this->assertTrue(empty($Taggings), "$message tagging $ReferenceID4711");
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID1b, $ReferenceID0, TRUE);
    $this->assertTrue(empty($Taggings), "$message tagging $ReferenceID0");

    //search mask is still empty to return all subscriptions
    //there should be now 2 subscriber with 1 email subscription each
    $SearchMsg = "2. subscriber with only email added, not tagged, Ref: $ReferenceID0";
    $SearchMask = array();
    $this->SearchTest($UserID, $SearchMask, 2, 2, 2, $SearchMsg);

    // create subscriber 1c : email subscribed, tagged 1 all references
    $message = 'create subscriber 1c';
    $EmailAddress1c = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'ReferenceID' => $ReferenceID0,
      'EmailAddress' => $EmailAddress1c,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $MVManualTagID,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID1c = $Result[1];
    // update CF
    CustomFields::UpdateCustomFieldData($UserID, $SubscriberID1c, $ReferenceID0, $DateTimeCustomField, $future);
    // tag $ReferenceID
    Subscribers::TagSubscriber($UserID, $SubscriberID1c, $MVManualTagID, $ReferenceID4711, TRUE);
    // check multivalue tagging
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID1c, $ReferenceID4711, TRUE);
    $this->assertTrue(in_array($MVManualTagID, $Taggings), "$message tagging $ReferenceID4711 $SubscriberID1c ".print_r($Taggings,1));
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID1c, $ReferenceID0, TRUE);
    $this->assertTrue(in_array($MVManualTagID, $Taggings), "$message tagging $ReferenceID0");

    //search mask is empty to return all subscriptions
    //there should be now 3 subscriber with 3 email subscription each
    //the subscriber is tagged with 2 references, which added an empty subscription for the new reference
    //all subscriptions are shown in the search result
    $SearchMsg = "3. subscriber with only email added, MV tagged all references, Ref: $ReferenceID0";
    $SearchMask = array();
    $this->SearchTest($UserID, $SearchMask, 3, 4, 4, $SearchMsg);

    // create subscriber 1d : email subscribed, subscribed 2 all references
    $message = 'create subscriber 1d';
    $EmailAddress1d = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'ReferenceID' => $ReferenceID0,
      'EmailAddress' => $EmailAddress1d,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID2,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID1d = $Result[1];
    // subscribe $ReferenceID
    $Parameters['ReferenceID'] = $ReferenceID4711;
    Subscribers::Subscribe($Parameters);
    // update CF
    CustomFields::UpdateCustomFieldData($UserID, $SubscriberID1d, $ReferenceID4711, $DateTimeCustomField, $future);
    // check multivalue fields
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID1d, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID4711, TRUE);
    $this->assertTrue($Subscription["CustomField$DateTimeCustomFieldID"] == $future, "$message CustomField$DateTimeCustomFieldID $ReferenceID4711");
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID0, TRUE);
    $this->assertTrue(empty($Subscription["CustomField$DateTimeCustomFieldID"]), "$message CustomField$DateTimeCustomFieldID $ReferenceID0");
    // check multivalue tagging
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID1d, $ReferenceID4711, TRUE);
    $this->assertTrue(in_array($ManualTagID2, $Taggings), "$message tagging $ReferenceID4711 $SubscriberID1d ".print_r($Taggings,1));
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID1d, $ReferenceID0, TRUE);
    $this->assertTrue(in_array($ManualTagID2, $Taggings), "$message tagging $ReferenceID0");

    //search mask is empty to return all subscriptions
    //there should be now 4 subscribers with 5 subscriptions, since the same email address doesn't add another subscription
    //but both references for the email address is shown in the result
    $SearchMsg = "4. subscriber with only email for 2 references added, manual tagged both references, Ref: $ReferenceID0, $ReferenceID4711";
    $SearchMask = array();
    $this->SearchTest($UserID, $SearchMask, 4, 5, 6, $SearchMsg);

    // create subscriber 1e : different emails subscribed in ref 0 and 4711 (like after merge)
    $message = 'create subscriber 1e';
    $EmailAddress1e = '<EMAIL>';
    $EmailAddress1e2 = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'ReferenceID' => $ReferenceID0,
      'EmailAddress' => $EmailAddress1e,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID2,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID1e = $Result[1];
    // subscribe $ReferenceID
    $Parameters['SubscriberID'] = $SubscriberID1e;
    $Parameters['EmailAddress'] = $EmailAddress1e2;
    $Parameters['ReferenceID'] = $ReferenceID4711;

    // enforce higher LastOpenDate, so we can suppose $EmailAddress1e2 to be taken because of higher activity index
    sleep(1);

    $Result = Subscribers::Subscribe($Parameters);
    // check multivalue emails
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID1e, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID0);
    $this->assertTrue($Subscription["ContactInfo"] == $EmailAddress1e, "$message EmailAddress $ReferenceID0". print_r($Subscription,1));
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID1e, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID4711);
    $this->assertTrue($Subscription["ContactInfo"] == $EmailAddress1e2, "$message EmailAddress $ReferenceID4711". print_r($Subscription,1));
    $Subscription = Subscription::GetMostActiveReferenceForCampaign($UserID, $SubscriberID1e, Campaigns::TRIGGER_TYPE_CAMPAIGN, []);
    $this->assertTrue($Subscription["ReferenceID"] == $ReferenceID4711, "$message GetMostActiveReferenceForCampaign". print_r($Subscription,1));

    //search mask is empty to return all subscriptions
    //there should be now 5 subscribers with 7 subscriptions, since the 2 email addresses add a subscription each
    $SearchMsg = "5. subscriber with 2 email addresses with a different reference each added, Ref: $ReferenceID0, $ReferenceID4711";
    $SearchMask = array();
    $this->SearchTest($UserID, $SearchMask, 5, 7, 8, $SearchMsg);

    // create subscriber 2 : sms subscribed, tagged 2
    $message = 'create subscriber 2';
    $PhoneNumber = '0049123456789';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'PhoneNumber' => $PhoneNumber,
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $ManualTagID2,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
      'OtherFields' => $fieldValues1,
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID2 = $Result[1];
    // check multivalue fields
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID2, Subscription::SUBSCRIPTIONTYPE_SMS, $ReferenceID0, TRUE);
    $this->assertTrue(strlen($Subscription["CustomFieldFirstName"]) > 0, "$message CustomFieldFirstName $ReferenceID0". print_r($Subscription,1));
    $this->assertTrue($Subscription["CustomField$DateTimeCustomFieldID"] == $future, "$message CustomField$DateTimeCustomFieldID $ReferenceID0");
    $this->assertTrue($Subscription["CustomField$NumberCustomFieldID"] > 0, "$message CustomField$NumberCustomFieldID $ReferenceID0");
    // check multivalue tagging
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID2, $ReferenceID4711, TRUE);
    $this->assertTrue(in_array($ManualTagID2, $Taggings), "$message tagging $ReferenceID4711 $SubscriberID1d ".print_r($Taggings,1));
    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID2, $ReferenceID0, TRUE);
    $this->assertTrue(in_array($ManualTagID2, $Taggings), "$message tagging $ReferenceID0");

    //search mask is empty to return all subscriptions
    //there should be now 5 subscribers with 7 subscriptions, since the 2 email addresses add a subscription each
    $SearchMsg = "6. subscriber with phonenumber added, Ref: $ReferenceID0";
    $SearchMask = array();
    $this->SearchTest($UserID, $SearchMask, 6, 8, 9, $SearchMsg);


    // subscribers
    // $SubscriberID   (1) Ref0: -              | Ref4711: EMAIL1 Tag1 CF
    // $SubscriberID1b (2) Ref0: EMAIL2          | Ref4711: -
    // $SubscriberID1c (3) Ref0: EMAIL3 Tag1 CF  | Ref4711: Tag1
    // $SubscriberID1d (4) Ref0: EMAIL4 Tag2     | Ref4711: EMAIL4 Tag2 CF
    // $SubscriberID1e (5) Ref0: EMAIL5 Tag2    | Ref4711: EMAIL6 Tag2
    // $SubscriberID2  (6) Ref0: SMS1 Tag2 CF    | Ref4711: -

    // --- perform different searches on the current subscribers

    $TotalSubscribers = 6;

    $SearchMsg = "find all email subscriptions";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS,
    );
    //we have 6 different email addresses -> 6 email subscriptions
    //since EMAIL4 has 2 different references it has 2 search results
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 6, 7, $SearchMsg);


    $SearchMsg = "find all email subscriptions with MV tag";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS,
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($MVManualTagID => $MVManualTagID),
    );
    //EMAIL1 and EMAIL3 are tagged with Tag1
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 2, 2, $SearchMsg);

    $SearchMsg = "find all email subscriptions with manual tag";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS,
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($ManualTagID2 => $ManualTagID2),
    );
    //EMAIL4 (2x), EMAIL5 and EMAIL6 are tagged with Tag2
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 3, 4, $SearchMsg);

    $SearchMsg = "find all email subscriptions with MV OR manual tag";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS,
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($MVManualTagID => $MVManualTagID, $ManualTagID2 => $ManualTagID2),
    );
    //EMAIL1, EMAIL3, EMAIL4 (2x), EMAIL5 and EMAIL6 are tagged with Tag2
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 5, 6, $SearchMsg);

    $SearchMsg = "find all email subscriptions with MV AND manual tag";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS,
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($MVManualTagID => $MVManualTagID, $ManualTagID2 => $ManualTagID2),
    );
    //NONE
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 0, 0, $SearchMsg);


    $SearchMsg = "find all sms subscriptions";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_PHONE_NUMBER,
    );
    //we have 1 phonenumber -> 1 sms subscriptions
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 1, 1, $SearchMsg);


    $SearchMsg = "find all subscriptions tagged with manual tag";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_NONE,
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($ManualTagID2 => $ManualTagID2),
    );
    //we have 1 phonenumber -> 1 sms subscriptions +
    //EMAIL4 (2x), EMAIL5 and EMAIL6 are tagged with Tag2
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 4, 5, $SearchMsg);

    $SearchMsg = "find all subscriptions where firstname contains '-startmiddle-'";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_NONE,
      'FormValue_Fields' => array("CustomFieldFirstName" => '-startmiddle-'),
      'CustomFields' => array('FirstName' => SubscribersSearch::CONTAINS),
    );
    //only 2 subscriptions have the field filled
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 2, 2, $SearchMsg);


    $SearchMsg = "find all subscriptions that have no email address'";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS,
      'EmailSearch' => array(
        'EmailAddressOp' => SubscribersSearch::IS_EMPTY
      )
    );
    //we have 1 sms subscription
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 1, 1, $SearchMsg);

    $SearchMsg = "find all subscriptions that have no sms number'";
    $SearchMask = array(
      'SearchMode' => SubscribersSearch::SEARCH_BY_PHONE_NUMBER,
      'SMSSearch' => array(
        'SMSPhoneNumberOp' => SubscribersSearch::IS_EMPTY
      )
    );
    //we have 8 total subscriptions minus 1 sms subscription
    //Note: Subscriber3 has 2 refences for 1 email address
    $this->SearchTest($UserID, $SearchMask, $TotalSubscribers, 7, 8, $SearchMsg);

    /*
     * TRIGGER_TYPE_CAMPAIGN
     */
    $message = 'TRIGGER_TYPE_CAMPAIGN';

    $CampaignID = simpletest_create_campaign($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, [$MVManualTagID, $ManualTagID2], ['SendToOnlyOneSubscription' => 1]);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 4);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(NewsletterQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENT, -1, 4, 4);

    /*
     * TRIGGER_TYPE_CAMPAIGN all subscriptions
     */
    $message = 'TRIGGER_TYPE_CAMPAIGN all';

    $CampaignID = simpletest_create_campaign($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, [$MVManualTagID, $ManualTagID2], ['SendToOnlyOneSubscription' => 0]);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 4);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(NewsletterQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENT, -1, 6, 6);

    /*
     * TRIGGER_TYPE_SMS_CAMPAIGN
     */
    $message = 'TRIGGER_TYPE_SMS_CAMPAIGN';

    $CampaignID = simpletest_create_campaign($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, [$MVManualTagID, $ManualTagID2]);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 1);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(NewsletterQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENT, -1, 1, 1);

    /*
     * TRIGGER_TYPE_SUBSCRIPTION
     */
    $message = 'TRIGGER_TYPE_SUBSCRIPTION';

    $CampaignID = simpletest_create_campaign($UserID, Campaigns::TRIGGER_TYPE_SUBSCRIPTION, [$MVManualTagID]);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 2);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(AutoresponderQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENDING, -1, 2, 2);

    $message = 'TRIGGER_TYPE_SUBSCRIPTION restart';

    // stop autoresponder, change recipients an restart
    Campaigns::StopCampaign($UserID, $CampaignID);
    Campaigns::UpdateCampaign($UserID, $CampaignID, [
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => [$MVManualTagID, $ManualTagID2],
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ]);

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 2, 2, 2);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(AutoresponderQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENDING, -1, 6, 4);

    /*
     * TRIGGER_TYPE_SMS_SUBSCRIPTION
     */
    $message = 'TRIGGER_TYPE_SMS_SUBSCRIPTION';

    $CampaignID = simpletest_create_campaign($UserID, Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION, [$MVManualTagID, $ManualTagID2]);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 1);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(AutoresponderQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENDING, -1, 1, 1);

    /*
     * TRIGGER_TIME_TYPE_CUSTOMFIELD
     */
    $message = 'TRIGGER_TIME_TYPE_CUSTOMFIELD';

    $Additional = [
      'TriggerTimeTypeEnum' => Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD,
      'TriggerTime' => 0,
      'DelayByCustomFieldDatetimeFieldID' => $DateTimeCustomFieldID,
    ];
    $CampaignID = simpletest_create_campaign($UserID, Campaigns::TRIGGER_TYPE_DATETIME, [$MVManualTagID, $ManualTagID2], $Additional);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 4);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(AutoresponderQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_PENDING);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_PENDING);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_PENDING);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENDING, -1, 0, 3);

    /*
     * AUDIENCE
     */
    $message = 'AUDIENCE';
    $AudienceData = array(
      'Data' => array(
        'SearchMode' => SubscribersSearch::SEARCH_BY_EMAIL_ADDRESS,
        'EmailSearch' => array(
          'EmailAddressOp' => SubscribersSearch::CONTAINS,
          'EmailAddress' => '',
          'Status' => array(
            SubscribersSearch::STATUS_SUBSCRIBED => SubscribersSearch::STATUS_SUBSCRIBED,
          ),
          'BounceStatus' => array(
            Subscribers::BOUNCETYPE_NOTBOUNCED => Subscribers::BOUNCETYPE_NOTBOUNCED,
            Subscribers::BOUNCETYPE_SOFT => Subscribers::BOUNCETYPE_SOFT,
          ),
        ),
        'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
        'TaggedWith' => [$MVManualTagID, $ManualTagID2],
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ALL,
        'NotTaggedWith' => array(),
        'UseCustomField' => '0',
      ),
    );
    $audiences = VarSubscriberAudience::RetrieveAudiences($UserID);
    $this->assertTrue(empty($audiences), $message . ' no subscriber audiences yet');
    $result = VarSubscriberAudience::CreateAudience($UserID, "Test Audience", $AudienceData['Data']);
    $this->assertTrue($result, $message . ' audience created');
    $audiences = VarSubscriberAudience::RetrieveAudiences($UserID);
    $Audience = reset($audiences);
    $this->assertTrue(!empty($Audience), $message . ' audience retrieved');

    $Additional = [
      'SubscriberAudience' => $Audience['SeqNo'],
      'SendToOnlyOneSubscription' => 1,
    ];
    $CampaignID = simpletest_create_campaign($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, [], $Additional);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 6);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(NewsletterQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENT, -1, 4, 4);

    /*
     * TRIGGER_TYPE_BIRTHDAY
     */
    $message = 'TRIGGER_TYPE_BIRTHDAY';

    $Additional = array(
      'TriggerTimeTypeEnum' => Campaigns::TRIGGER_TIME_TYPE_CUSTOMFIELD,
      'TriggerTime' => 0,
      'BirthdayCustomFieldID' => $DateTimeCustomFieldID,
      'BirthdayTimeDelay' => (intval(date("H")) + 1) * 60 * 60, // in one hour
      'SendToOnlyOneSubscription' => 0,
    );
    $CampaignID = simpletest_create_campaign($UserID, Campaigns::TRIGGER_TYPE_BIRTHDAY, [$MVManualTagID, $ManualTagID2], $Additional);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 4);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(AutoresponderQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_PENDING);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_PENDING);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_PENDING);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // check TimeToSend
    $q = TransactionEmails::RetrieveTransactionByID($UserID, $SubscriberID, $ReferenceID4711, $CampaignID);
    $this->assertTrue($q['TimeToSend'] == strtotime(date("Y-m-d H:00:00"))+3600, "message TimeToSent".print_r($q,1));

    // make it sent last year
    TransactionEmails::Update(
      array('StatusEnum' => TransactionEmails::STATUS_SENT, 'TimeToSend' => strtotime("-1 years")),
      array('RelOwnerUserID' => $UserID, 'RelAutoResponderID' => $CampaignID),
      AutoresponderQueue::TABLE_NAME
    );

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_SENT);

    // reterminate
    AutoResponders::ReterminateBirthdayAutoResponders();

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_PENDING);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_PENDING);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_PENDING);

    /*
     * ToolOutboundGeneral
     */
    $message = 'ToolOutboundGeneral';

    // outbounds were already triggered and executed by action above
    $this->assertTrue(empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID, $ReferenceID0)), __LINE__." $message $SubscriberID/$ReferenceID0");
    $this->assertTrue(!empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID, $ReferenceID4711)), __LINE__." $message $SubscriberID/$ReferenceID4711");
    $this->assertTrue(empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID1b, $ReferenceID0)), __LINE__." $message $SubscriberID1b/$ReferenceID0");
    $this->assertTrue(empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID1b, $ReferenceID4711)), __LINE__." $message $SubscriberID1b/$ReferenceID4711");
    $this->assertTrue(!empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID1c, $ReferenceID0)), __LINE__." $message $SubscriberID1c/$ReferenceID0");
    $this->assertTrue(!empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID1c, $ReferenceID4711)), __LINE__." $message $SubscriberID1c/$ReferenceID4711");
    $this->assertTrue(empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID1d, $ReferenceID0)), __LINE__." $message $SubscriberID1d/$ReferenceID0");
    $this->assertTrue(empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID1d, $ReferenceID4711)), __LINE__." $message $SubscriberID1d/$ReferenceID4711");
    $this->assertTrue(empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID1e, $ReferenceID0)), __LINE__." $message $SubscriberID1e/$ReferenceID0");
    $this->assertTrue(empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID1e, $ReferenceID4711)), __LINE__." $message $SubscriberID1e/$ReferenceID4711");
    $this->assertTrue(empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID2, $ReferenceID0)), __LINE__." $message $SubscriberID2/$ReferenceID0");
    $this->assertTrue(empty(Subscribers::RetrieveTagging($UserID, $OutboundSmartTag, $SubscriberID2, $ReferenceID4711)), __LINE__." $message $SubscriberID2/$ReferenceID4711");

    /*
     * Splittest TRIGGER_TYPE_CAMPAIGN
     */
    $message = 'Splittest CAMPAIGN';

    $CampaignID = simpletest_create_splittest_campaign($UserID, $message, FALSE, [$MVManualTagID, $ManualTagID2], SplitTests::WINNER_OPENS, ['SendToOnlyOneSubscription' => 1], TRUE);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 4);

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(NewsletterQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711 2ND", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0 2ND", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish 1st run
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_WAITING_FOR_WINNER, -1, 2, 4);

    // simulate time for "waiting for winner" is over
    $ObjectCampaign = Campaigns::FromID($UserID, $CampaignID);
    db_query("UPDATE {split_tests} SET started = :started WHERE TestID = :TestID AND RelOwnerUserID = :RelOwnerUserID",
      array(
        ':started' => strtotime("-1 day"),
        ':TestID' => $ObjectCampaign->GetData('SplittestID'),
        ':RelOwnerUserID' => $UserID
      ));

    // 2nd run
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(NewsletterQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENT, -1, 4, 4);

    /*
     * Splittest TRIGGER_TYPE_CAMPAIGN all subscriptions
     */
    $message = 'Splittest CAMPAIGN all';

    $CampaignID = simpletest_create_splittest_campaign($UserID, $message, FALSE, [$MVManualTagID, $ManualTagID2], SplitTests::WINNER_OPENS, ['SendToOnlyOneSubscription' => 0], TRUE);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 4);

    // get email ids of variants
    $ObjectCampaign = Campaigns::FromID($UserID, $CampaignID);
    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCampaign->GetData());
    $EmailIDVariant0 = $SplittestObject->GetVariantEntity(0)->GetData('EmailID');
    $EmailIDVariant1 = $SplittestObject->GetVariantEntity(1)->GetData('EmailID');
    $this->assertTrue($EmailIDVariant0 != $EmailIDVariant1, "$message variants differ $EmailIDVariant0 $EmailIDVariant1");

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(NewsletterQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711 2ND", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    // the splittest gets one of the references but not both
    $ArrayTransactionalEmail = TransactionEmails::RetrieveTransactionByID($UserID, $SubscriberID1d, $ReferenceID4711, $CampaignID);
    if (!empty($ArrayTransactionalEmail)) {
      simpletest_expected_status($this, __LINE__ . " $SubscriberID1d/$ReferenceID0 2ND", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0);
      simpletest_expected_status($this, __LINE__ . " $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_SENT, $EmailIDVariant1);
    }
    else {
      simpletest_expected_status($this, __LINE__ . " $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT, $EmailIDVariant1);
      simpletest_expected_status($this, __LINE__ . " $SubscriberID1d/$ReferenceID4711 2ND", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711);
    }
    // the splittest gets one of the references but not both
    $ArrayTransactionalEmail = TransactionEmails::RetrieveTransactionByID($UserID, $SubscriberID1e, $ReferenceID0, $CampaignID);
    if (!empty($ArrayTransactionalEmail)) {
      simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
      simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711 2ND", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711);
    }
    else {
      simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0 2ND", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0);
      simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
    }
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish 1st run
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_WAITING_FOR_WINNER, -1, 3, 6);

    // simulate time for "waiting for winner" is over
    $ObjectCampaign = Campaigns::FromID($UserID, $CampaignID);
    db_query("UPDATE {split_tests} SET started = :started WHERE TestID = :TestID AND RelOwnerUserID = :RelOwnerUserID",
      array(
        ':started' => strtotime("-1 day"),
        ':TestID' => $ObjectCampaign->GetData('SplittestID'),
        ':RelOwnerUserID' => $UserID
      ));

    // 2nd run (sends all to winner)
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(NewsletterQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT, $EmailIDVariant1);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_SENT, $EmailIDVariant1);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENT, -1, 6, 6);

    /*
     * Splittest TRIGGER_TYPE_SUBSCRIPTION
     */
    $message = 'Splittest SUBSCRIPTION';

    $CampaignID = simpletest_create_splittest_campaign($UserID, $message, TRUE, [$MVManualTagID, $ManualTagID2], SplitTests::WINNER_OPENS, ['SendToOnlyOneSubscription' => 1], TRUE);

    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 4);

    // get email ids of variants
    $ObjectCampaign = Campaigns::FromID($UserID, $CampaignID);
    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCampaign->GetData());
    $EmailIDVariant0 = $SplittestObject->GetVariantEntity(0)->GetData('EmailID');
    $EmailIDVariant1 = $SplittestObject->GetVariantEntity(1)->GetData('EmailID');
    $this->assertTrue($EmailIDVariant0 != $EmailIDVariant1, "$message variants differ $EmailIDVariant0 $EmailIDVariant1");

    // start campaign
    simpletest_call_web_send();
    simpletest_call_transactional_send_simple(AutoresponderQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT, $EmailIDVariant1);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_SENT, $EmailIDVariant0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT, $EmailIDVariant1);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711, TransactionEmails::STATUS_SENT, $EmailIDVariant1);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);

    // finish 1st run
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_WAITING_FOR_WINNER, -1, 6, 6);

    // simulate time for "waiting for winner" is over
    $ObjectCampaign = Campaigns::FromID($UserID, $CampaignID);
    db_query("UPDATE {split_tests} SET started = :started WHERE TestID = :TestID AND RelOwnerUserID = :RelOwnerUserID",
      array(
        ':started' => strtotime("-1 day"),
        ':TestID' => $ObjectCampaign->GetData('SplittestID'),
        ':RelOwnerUserID' => $UserID
      ));

    // finish splittest
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENDING, -1, 6, 6);

    // get winner
    $ObjectCampaign = Campaigns::FromID($UserID, $CampaignID);
    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCampaign->GetData());
    $this->assertTrue(!empty($SplittestObject->GetData('finished')), "message finished ".print_r($SplittestObject,1));
    $this->assertTrue($SplittestObject->GetData('WinnerID') == 0, "message winner");
    $OldWinnerEmailID = $SplittestObject->GetNextSplittestVariant()->GetData('EmailID');
    // manipulate winner (otherwise we get variant 0 always)
    db_query("UPDATE {split_tests} SET WinnerID = :WinnerID WHERE TestID = :TestID",
      array(
        ':WinnerID' => 1,
        ':TestID' => $ObjectCampaign->GetData('SplittestID'),
      ));
    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCampaign->GetData());
    $WinnerEmailID = $SplittestObject->GetNextSplittestVariant()->GetData('EmailID');
    $this->assertTrue($OldWinnerEmailID != $WinnerEmailID, "$message winner changed");

    // send another email to $SubscriberID1d with different referenceid
    $ReferenceID4712 = 4712;
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'ReferenceID' => $ReferenceID4712,
      'EmailAddress' => $EmailAddress1d,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $MVManualTagID,
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add reference');

    AutoResponders::RegisterAutoResponders($UserID, $ReferenceID4712, $SubscriberID1d, $MVManualTagID);
    simpletest_call_transactional_send_simple(AutoresponderQueue::class);
    // $SubscriberID1d does not get winner, because he got $EmailIDVariant0 before
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4712", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4712, TransactionEmails::STATUS_SENT, $EmailIDVariant0);

    // send another email to a new subscriber
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'ReferenceID' => $ReferenceID4712,
      'EmailAddress' => '<EMAIL>',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => $MVManualTagID,
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID4712 = $Result[1];

    AutoResponders::RegisterAutoResponders($UserID, $ReferenceID4712, $SubscriberID4712, $MVManualTagID);
    simpletest_call_transactional_send_simple(AutoresponderQueue::class);
    // new subscriber gets winner
    simpletest_expected_status($this, __LINE__." $SubscriberID4712/$ReferenceID4712", $UserID, $CampaignID, $SubscriberID4712, $ReferenceID4712, TransactionEmails::STATUS_SENT, $WinnerEmailID);

    /*
     * Bounces
     */
    $message = 'Bounces';

    $CampaignID = simpletest_create_campaign($UserID, Campaigns::TRIGGER_TYPE_SUBSCRIPTION, [$MVManualTagID, $ManualTagID2]);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_READY, 5);

    // start campaign
    simpletest_call_web_send();

    // bounce (differently)
    BounceEngine::RegisterSpamBounce($UserID, $SubscriberID, $EmailAddress, 0, 0, '');
    BounceEngine::RegisterSoftBounce($UserID, $SubscriberID1c, $EmailAddress1c, 0, 0);
    BounceEngine::RegisterHardBounce($UserID, $SubscriberID1e, $EmailAddress1e2, 0, 0, '');

    simpletest_call_transactional_send_simple(AutoresponderQueue::class);

    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_FAILED);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_FAILED);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4712", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4712, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711, TransactionEmails::STATUS_FAILED);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID4712/$ReferenceID4712", $UserID, $CampaignID, $SubscriberID4712, $ReferenceID4712, TransactionEmails::STATUS_SENT);

    // finish campaign
    simpletest_call_web_send();
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENDING, -1, 5, 8);

    // unbounce all
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID4711);
    BounceEngine::ResolveSpamBounce($Subscription, TRUE);
    BounceEngine::RemoveFromSuppressionList($EmailAddress1c, Subscribers::BOUNCETYPE_SOFT);
    BounceEngine::RemoveFromSuppressionList($EmailAddress1e2, Subscribers::BOUNCETYPE_HARD);

    simpletest_call_transactional_send_simple(AutoresponderQueue::class);

    // final state = all delivered
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID0", $UserID, $CampaignID, $SubscriberID, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID1b/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1b, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1c/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1c, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1d/$ReferenceID4712", $UserID, $CampaignID, $SubscriberID1d, $ReferenceID4712, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID0", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID0, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID1e/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID1e, $ReferenceID4711, TransactionEmails::STATUS_SENT);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID0", $UserID, $CampaignID, $SubscriberID2, $ReferenceID0);
    simpletest_expected_status($this, __LINE__." $SubscriberID2/$ReferenceID4711", $UserID, $CampaignID, $SubscriberID2, $ReferenceID4711);
    simpletest_expected_status($this, __LINE__." $SubscriberID4712/$ReferenceID4712", $UserID, $CampaignID, $SubscriberID4712, $ReferenceID4712, TransactionEmails::STATUS_SENT);

    // finish campaign
    simpletest_call_web_send();
    // note: TotalRecipients counts AR runs twice if we reset a fail. Right or wrong ... maybe wrong, but historically.
    simpletest_check_campaign_status($this, $message, $UserID, $CampaignID, Campaigns::STATUS_SENDING, -1, 8, 11);

  }

  private function SearchTest($UserID, $SearchMask, $TotalSubscribers, $cSubscriptions, $cResults, $message) {

    $TestSearch = new SubscribersSearchDialog($UserID, $SearchMask);
    $countTotal = $TestSearch->CountTotalSubscribers();
    $countResultSubscriptions = $TestSearch->CountResultSubscriptions();
    $Result = $TestSearch->Search(0, 1000);
    $ResultCount = count($Result);

    if ($countTotal != $TotalSubscribers) {
      $this->assertTrue(FALSE, "SearchTest ($message): Total Subscriber: $countTotal ($TotalSubscribers)");
    }
    elseif ($cSubscriptions != $countResultSubscriptions) {
      $this->assertTrue(FALSE, "SearchTest ($message): Result Subscription $countResultSubscriptions ($cSubscriptions) - " . print_r($Result, 1));
    }
    elseif ($ResultCount != $cResults) {
      $this->assertTrue(FALSE, "SearchTest ($message): ResultCount $ResultCount ($cResults) - " . print_r($Result, 1));
    }
    else {
      $this->assertTrue(TRUE, "SearchTest ($message): OK");
    }

    return $Result;

  }

}

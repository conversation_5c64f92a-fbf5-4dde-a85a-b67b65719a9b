<?php

use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsProcessFlow;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\TransactionalQueue;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\Translator;
use App\Klicktipp\VarFullContact;
use App\Tests\Integration\Shared\AmqpSubscriberQueueTestHelper;

class coreapiFullContactProcessflowTestCase extends DrupalWebTestCase {
  public function setUp(): void
  {
    parent::setUp();
    AmqpSubscriberQueueTestHelper::initialize();
  }

  public static function getInfo() {
    return array(
      'name' => 'coreapi TransactionalEmails (FullContact)',
      'description' => 'Test classes/transaction_emails.inc',
      'group' => 'Klicktipp',
    );
  }

  function testCoreapiFullContactProcessflow() {

    // Load other modules - Start
    Libraries::include('cron_send.inc', '/cli');
    // Load other modules - End

    Libraries::include('full_user.inc', '/tests/includes');
    Libraries::include('campaigns.inc', '/tests/includes');
    Libraries::include('queue.inc', '/tests/includes');
    Libraries::include('transactional.inc', '/tests/includes');

    $UserID = 1;
    $HttptestURL = 'https://dev:ovi8eTei@'.KLICKTIPP_DOMAIN.'/kthttptest';
    $ReferenceID = 0;

    $message = 'create tag';
    $ManualTagID = Tag::CreateManualTag($UserID, 'manual test tag fullcontact', '');
    $this->assertTrue($ManualTagID > 0, "$message #$ManualTagID");

    // create subscriber
    $message = 'create subscriber';
    $DefaultList = Lists::RetrieveList(array('*'), array(
      'RelOwnerUserID' => $UserID,
      'ListID' => 'default',
    ), TRUE);
    $EmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'EmailAddress' => $EmailAddress,
      'ListInformation' => $DefaultList,
      'IPAddress' => '127.0.0.1',
      'OptInSubscribeTo' => $ManualTagID,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID = $Result[1];

    $message = 'create automation';

    // create campaign with START state 'is tagged with' $ManualTagID
    $CampaignID = simpletest_transactional_create_campaign($this, $message, $UserID, $ManualTagID);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    /////////////////////// run PROCESSFLOW_STATE_TYPE_FULLCONTACT

    $message = CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $APIKey = "abcdefghi";
    $Likelihood = 90;
    $VarFullContact = VarFullContact::GetVariable($UserID, []);
    $VarFullContact['apiKey'] = $APIKey;
    $VarFullContact['likelihood'] = $Likelihood;
    VarFullContact::SetVariable($UserID, $VarFullContact);

    // check update
    $VarFullContact = VarFullContact::GetVariable($UserID, []);
    $this->assertTrue($VarFullContact['apiKey'] == $APIKey, $message . ' ApiKey set');
    $this->assertTrue($VarFullContact['likelihood'] == $Likelihood, $message . ' Likelihood set');

    $FullContactData = array(
      "fullName" => "Bill Gates",
      "ageRange" => "57-67",
      "gender" => "Male",
      "location" => "Seattle, Wa, United States Of America",
      "title" => "Co-chair",
      "organization" => "Bill & Melinda Gates Foundation",
      "twitter" => "https://twitter.com/BillGates",
      "linkedin" => "https://www.linkedin.com/in/williamhgates",
      "facebook" => "https://www.facebook.com/BillGates",
      "avatar" => "https://img.fullcontact.com/static/b763bd967fc5a8f0d914b3cd049e1581_99b54d444695ec334501ebbed0e25c7dc61110a2b5f5b095f35278012d3ae176",
      "website" => "http://www.thegatesnotes.com/",
      "details" => array(
        "name" => array(
          "given" => "Bill",
          "family" => "Gates",
          "full" => "Bill Gates",
        ),
        "age" => array(
          "range" => "55-64",
          "value" => 62,
        ),
        "gender" => "Male",
        "demographics" => array(
          "gender" => "Male",
          "age" => array(
            "range" => "55-64",
            "value" => 62,
          ),
        ),
        "profiles" => array(
          "twitter" => array(
            "url" => "https://twitter.com/BillGates",
            "service" => "twitter",
          ),
          "facebook" => array(
            "url" => "https://www.facebook.com/BillGates",
            "service" => "facebook",
          ),
          "linkedin" => array(
            "url" => "https://www.linkedin.com/in/williamhgates",
            "service" => "linkedin",
          ),
        ),
        "locations" => array(
          0 => array(
            "city" => "Seattle",
            "region" => "Washington",
            "regionCode" => "WA",
            "country" => "United States",
            "countryCode" => "US",
            "formatted" => "Seattle, Wa, United States Of America",
          ),
        ),
        "employment" => array(
          0 => array(
            "name" => "Bill & Melinda Gates Foundation",
            "current" => 1,
            "title" => "Co-chair",
          ),
        ),
        "photos" => array(
          0 => array(
            "label" => "avatar",
            "value" => "https://img.fullcontact.com/static/b763bd967fc5a8f0d914b3cd049e1581_99b54d444695ec334501ebbed0e25c7dc61110a2b5f5b095f35278012d3ae176",
          ),
        ),
        "education" => array(
          0 => array(
            "name" => "Harvard University",
            "end" => array(
              "year" => 1975,
            ),
          ),
          1 => array(
            "name" => "Lakeside School, Seattle",
          ),
        ),
        "urls" => array(
          0 => array(
            "value" => "http://www.thegatesnotes.com/",
          ),
        ),
      ),
      "updated" => "2019-09-20",
    );
    $urlQuery = array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 200,
      ),
    );
    foreach ($FullContactData as $key => $value) {
      $urlQuery['query'][$key] = $value;
    }

    //--------------------------------------------------------------------------
    // CASE 1: successfull api call
    //--------------------------------------------------------------------------

    $message = 'CASE 1: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $fullContactUrl = url($HttptestURL, $urlQuery);
    variable_set('simpletest-fullcontact-api', $fullContactUrl);

    // last state is start state
    $laststate = 1;

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT);
    $state['ignoreRateLimit'] = 0;
    $state['overwriteData'] = 0;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid action added');

    // starting the automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    //TransactionEmails::RunStarted($UserID, $SubscriberID, $ObjectCamnpaign->GetData(), TRUE);
    Campaigns::UpdateCampaign($UserID, $CampaignID, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));
    simpletest_transactional_call_send();

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    // check that nothing was changed yet
    $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($Subscription['CustomFieldFirstName']), $message . ' empty firstname');
    $this->assertTrue(empty($Subscription['CustomFieldLastName']), $message . ' empty lastname');
    $this->assertTrue(empty($Subscription['CustomFieldCity']), $message . ' empty city');
    $this->assertTrue(empty($Subscription['CustomFieldState']), $message . ' empty state');
    $this->assertTrue(empty($Subscription['CustomFieldCountry']), $message . ' empty country');
    $this->assertTrue(empty($Subscription['CustomFieldWebsite']), $message . ' empty website');
    $this->assertTrue(empty($Subscription['CustomFieldCompanyName']), $message . ' empty company name');

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    // check that fields were updated
    $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($Subscription['CustomFieldFirstName'] == $FullContactData['details']['name']['given'], $message . ' set firstname');
    $this->assertTrue($Subscription['CustomFieldLastName'] == $FullContactData['details']['name']['family'], $message . ' set lastname');
    $this->assertTrue($Subscription['CustomFieldCity'] == $FullContactData['details']['locations'][0]['city'], $message . ' set city');
    $this->assertTrue($Subscription['CustomFieldState'] == $FullContactData['details']['locations'][0]['region'], $message . ' set state');
    $this->assertTrue($Subscription['CustomFieldCountry'] == Translator::getCountryNameFromCountryCode($FullContactData['details']['locations'][0]['countryCode']), $message . ' set country');
    $this->assertTrue($Subscription['CustomFieldWebsite'] == $FullContactData['details']['urls'][0]['value'], $message . ' set website');
    $this->assertTrue($Subscription['CustomFieldCompanyName'] == check_plain($FullContactData['details']['employment'][0]['name']), $message . ' set company name');
    // check hidden fullcontact field
    $FullContactField = CustomFields::GetCustomFieldData($UserID, $SubscriberID, CustomFields::$GlobalCustomFieldDefs['FullContact'], $ReferenceID);
    $FullContactField = (empty($FullContactField)) ? [] : drupal_json_decode($FullContactField);
    // remove secret, code and callback path to compare fullcontact data
    unset($FullContactField['data']['secret']);
    unset($FullContactField['data']['code']);
    unset($FullContactField['data']['q']);
    $this->assertTrue($FullContactField['data'] == $FullContactData, $message . ' all values stored');
    $this->assertTrue($FullContactField['timestamp'] > 0, $message . ' set timestamp');

    // run
    // we are at an open end
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

    //--------------------------------------------------------------------------
    // CASE 2: successfull api call but subscriber was already fetched in the last 24 hours
    //--------------------------------------------------------------------------

    $message = 'CASE 2: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $fullContactUrl = url($HttptestURL, $urlQuery);
    variable_set('simpletest-fullcontact-api', $fullContactUrl);
    $expectedMessage = t("The subscriber was already fetched within the last 24 hours.");

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT);
    $state['ignoreRateLimit'] = 0;
    $state['overwriteData'] = 0;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid action added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    $result = db_query("SELECT HistoryData FROM {subscriber_history} ".
      " WHERE RelOwnerUserID = :UserID AND RelSubscriberID = :SubscriberID AND HistoryType = :HistoryType", array(
      ':UserID' => $UserID,
      ':SubscriberID' => $SubscriberID,
      ':HistoryType' => Subscribers::HISTORY_FULLCONTACT_CALL_FAILED
    ));
    $entries = [];
    while ($entry = kt_fetch_array($result)) {
      // simply overwrite because we want to have the last entry
      $entries[] = (empty($entry['HistoryData']) ? [] : unserialize($entry['HistoryData']));
    }
    $this->assertTrue(count($entries) == 1, $message . ' fullcontact history entry');
    $this->assertTrue(array_search($expectedMessage, array_column($entries, 'Message')) !== FALSE, $message . ' subscriber fetched in last 24 hours');

    // run
    // we are at an open end
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

    //--------------------------------------------------------------------------
    // CASE 3: successfull api call with overwrite
    //--------------------------------------------------------------------------

    $message = 'CASE 3: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    // overwrite first and last name
    $expectedFirstName = "Max";
    $expectedLastName = "Mustermann";
    $urlQuery['query']['details']['name']['given'] = $expectedFirstName;
    $urlQuery['query']['details']['name']['family'] = $expectedLastName;
    $fullContactUrl = url($HttptestURL, $urlQuery);
    variable_set('simpletest-fullcontact-api', $fullContactUrl);

    // set timestamp 24 hours in the past and update field
    $FullContactField['timestamp'] = strtotime("-1 day");
    Subscribers::UpdateSubscription($UserID, $SubscriberID, array(
      'CustomFieldFullContact' => drupal_json_encode($FullContactField),
    ));

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT);
    $state['ignoreRateLimit'] = 0;
    $state['overwriteData'] = 1;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid action added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    // check that nothing was changed yet
    $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($Subscription['CustomFieldFirstName'] == $FullContactData['details']['name']['given'], $message . ' firstname remained');
    $this->assertTrue($Subscription['CustomFieldLastName'] == $FullContactData['details']['name']['family'], $message . ' lastname remained');

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($Subscription['CustomFieldFirstName'] == $expectedFirstName, $message . ' firstname overwritten');
    $this->assertTrue($Subscription['CustomFieldLastName'] == $expectedLastName, $message . ' lastname overwritten');

    // run
    // we are at an open end
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

    //--------------------------------------------------------------------------
    // CASE 5: fullcontact needs to process data first (returns 202 status code)
    //--------------------------------------------------------------------------

    $message = 'CASE 5: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $fullContactUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 202,
      )
    ));
    variable_set('simpletest-fullcontact-api', $fullContactUrl);

    // set timestamp 24 hours in the past and update field
    $FullContactField['timestamp'] = strtotime("-1 day");
    Subscribers::UpdateSubscription($UserID, $SubscriberID, array(
      'CustomFieldFullContact' => drupal_json_encode($FullContactField),
    ));

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT);
    $state['ignoreRateLimit'] = 0;
    $state['overwriteData'] = 0;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid action added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    // we wait 2 minutes in this case
    $Delay2Minutes = db_query("SELECT TimeToSend FROM {".TransactionalQueue::TABLE_NAME."} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ))->fetchField();

    $this->assertTrue($Delay2Minutes >= strtotime('+116 seconds') && $Delay2Minutes <= strtotime("+2 minutes"), $message . ' processing delay wait 2 minutes: ' . ($Delay2Minutes - time()));

    // run
    // we requeued, therefore job is in STATUS_PENDING
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PENDING);

    //--------------------------------------------------------------------------
    // CASE 6: fullcontact needs to process data first (returns 202 status code) but we ignore the delay
    //--------------------------------------------------------------------------

    $message = 'CASE 6: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $expectedMessage = t("Failed to fetch the subscriber due to a delay.");

    // set TimeToSend to -2 minutes so we can continue
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = :TimeToSend " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
      ':TimeToSend' => strtotime("-2 minutes"),
    ));

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $ArrayCampaign = $ObjectCamnpaign->GetData();
    $state = CampaignsProcessFlow::FindState($ArrayCampaign, $state['id']);
    $this->assertTrue(!empty($state), $message . ' state found');
    // update state to ignore the delay
    $state['ignoreRateLimit'] = 1;
    simpletest_transactional_create_processflow_update_state($ObjectCamnpaign, $state);
    // update campaign with updated state
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' action updated');

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    $result = db_query("SELECT HistoryData FROM {subscriber_history} ".
      " WHERE RelOwnerUserID = :UserID AND RelSubscriberID = :SubscriberID AND HistoryType = :HistoryType", array(
      ':UserID' => $UserID,
      ':SubscriberID' => $SubscriberID,
      ':HistoryType' => Subscribers::HISTORY_FULLCONTACT_CALL_FAILED
    ));
    $entries = [];
    while ($entry = kt_fetch_array($result)) {
      // simply overwrite because we want to have the last entry
      $entries[] = (empty($entry['HistoryData']) ? [] : unserialize($entry['HistoryData']));
    }
    $this->assertTrue(count($entries) == 2, $message . ' fullcontact history entry');
    $this->assertTrue(array_search($expectedMessage, array_column($entries, 'Message')) !== FALSE, $message . ' subscriber not fetched due to a delay');

    // run
    // we are at an open end
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

    //--------------------------------------------------------------------------
    // CASE 7: rate limit reached
    //--------------------------------------------------------------------------

    $message = 'CASE 7: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $resetDelay = 86400;
    $fullContactUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 403,
        'headers' => array(
          'x-rate-limit-limit' => 60,
          'x-rate-limit-remaining' => 0,
          'x-rate-limit-reset' => $resetDelay,
        )
      )
    ));
    variable_set('simpletest-fullcontact-api', $fullContactUrl);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT);
    $state['ignoreRateLimit'] = 0;
    $state['overwriteData'] = 0;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid action added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    // we wait the delay specified in the x-rate-limit-reset header
    $Delay = db_query("SELECT TimeToSend FROM {".TransactionalQueue::TABLE_NAME."} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ))->fetchField();

    $DelayDeviance = $resetDelay - 4;
    $this->assertTrue($Delay >= strtotime("+$DelayDeviance seconds") && $Delay <= strtotime("+$resetDelay seconds"), $message . " rate limit wait $resetDelay seconds: " . ($Delay - time()));

    // run
    // we requeued, therefore job is in STATUS_PENDING
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PENDING);

    //--------------------------------------------------------------------------
    // CASE 8: rate limit reached but we ignore it
    //--------------------------------------------------------------------------

    $message = 'CASE 8: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $expectedMessage = t("Failed to fetch the subscriber due to a rate limit.");

    // set TimeToSend to -$resetDelay seconds so we can continue
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = :TimeToSend " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
      ':TimeToSend' => strtotime("-$resetDelay seconds"),
    ));

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $ArrayCampaign = $ObjectCamnpaign->GetData();
    $state = CampaignsProcessFlow::FindState($ArrayCampaign, $state['id']);
    $this->assertTrue(!empty($state), $message . ' state found');
    // update state to ignore the delay
    $state['ignoreRateLimit'] = 1;
    simpletest_transactional_create_processflow_update_state($ObjectCamnpaign, $state);
    // update campaign with updated state
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' action updated');

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    $result = db_query("SELECT HistoryData FROM {subscriber_history} ".
      " WHERE RelOwnerUserID = :UserID AND RelSubscriberID = :SubscriberID AND HistoryType = :HistoryType", array(
      ':UserID' => $UserID,
      ':SubscriberID' => $SubscriberID,
      ':HistoryType' => Subscribers::HISTORY_FULLCONTACT_CALL_FAILED
    ));
    $entries = [];
    while ($entry = kt_fetch_array($result)) {
      // simply overwrite because we want to have the last entry
      $entries[] = (empty($entry['HistoryData'])) ? [] : unserialize($entry['HistoryData']);
    }
    $this->assertTrue(count($entries) == 3, $message . ' fullcontact history entry');
    $this->assertTrue(array_search($expectedMessage, array_column($entries, 'Message')) !== FALSE, $message . ' subscriber not fetched due to a rate limit');

    // run
    // we are at an open end
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

    //--------------------------------------------------------------------------
    // CASE 9: 403 error but no rate limit reached, check for monthly limit
    //--------------------------------------------------------------------------

    $message = 'CASE 9: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $fullContactUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 403,
        'headers' => array(
          'x-rate-limit-limit' => 60,
          'x-rate-limit-remaining' => 5,
          'x-rate-limit-reset' => 60,
        )
      )
    ));
    variable_set('simpletest-fullcontact-api', $fullContactUrl);
    $resetDelay = strtotime("+1 day");
    $periodEnd = date("Y-m-d", strtotime("last day of this month"));
    $fullContactStatsUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 200,
        'period' => array(
          'end' => $periodEnd,
        ),
        'stats' => array(
          'person' => array(
            'enrichment' => array(
              'matches' => array(
                'count' => 500,
                'quota' => 500,
                'overQuota' => FALSE,
              ),
            ),
          ),
        ),
      ),
    ));
    variable_set('simpletest-fullcontact-stats-api', $fullContactStatsUrl);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT);
    $state['ignoreRateLimit'] = 0;
    $state['overwriteData'] = 0;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid action added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    // we wait one day and not for the delay specified in the x-rate-limit-reset header as the user may have upgraded his plan
    $Delay = db_query("SELECT TimeToSend FROM {".TransactionalQueue::TABLE_NAME."} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ))->fetchField();

    // check set delay
    $DelayDeviance = $resetDelay + 8;
    $this->assertTrue($Delay <= $DelayDeviance && $Delay >= $resetDelay, $message . " monthly limit wait seconds: " . ($Delay - time()));
    $VarFullContact = VarFullContact::GetVariable($UserID, []);
    $this->assertTrue($VarFullContact['expiresAt'] <= $DelayDeviance && $VarFullContact['expiresAt'] >= $resetDelay, $message . " monthly limit wait seconds: " . ($VarFullContact['expiresAt'] - time()));

    // run
    // we requeued, therefore job is in STATUS_PENDING
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PENDING);

    //--------------------------------------------------------------------------
    // CASE 10: we reached the montly rate limit and thus do not call the fullcontact api for 24 hours
    //--------------------------------------------------------------------------

    $message = 'CASE 10: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $expectedMessage = t("Failed to fetch the subscriber due to monthly limit of api key.");

    // set TimeToSend to current time so we can continue
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = :TimeToSend " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
      ':TimeToSend' => time(),
    ));

    // we do not need this URL but to be sure to set the
    // variable 'simpletest-fullcontact-api' we do
    // set just a bae minimum. There will be no call to
    // FullContact if everything goes asa expected
    $fullContactUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 403,
      )
    ));
    variable_set('simpletest-fullcontact-api', $fullContactUrl);


    // we do not need this URL but to be sure to set the
    // variable 'simpletest-fullcontact-stats-api' we do
    // set just a bae minimum. There will be no call to
    // FullContact if everything goes asa expected
    $fullContactStatsUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 200,
      )
    ));
    variable_set('simpletest-fullcontact-stats-api', $fullContactStatsUrl);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $ArrayCampaign = $ObjectCamnpaign->GetData();
    $state = CampaignsProcessFlow::FindState($ArrayCampaign, $state['id']);
    $this->assertTrue(!empty($state), $message . ' state found');
    $state['ignoreRateLimit'] = 1;
    simpletest_transactional_create_processflow_update_state($ObjectCamnpaign, $state);
    // update campaign with updated state
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' action updated');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    $result = db_query("SELECT HistoryData FROM {subscriber_history} ".
      " WHERE RelOwnerUserID = :UserID AND RelSubscriberID = :SubscriberID AND HistoryType = :HistoryType", array(
      ':UserID' => $UserID,
      ':SubscriberID' => $SubscriberID,
      ':HistoryType' => Subscribers::HISTORY_FULLCONTACT_CALL_FAILED
    ));
    $entries = [];
    while ($entry = kt_fetch_array($result)) {
      // simply overwrite because we want to have the last entry
      $entries[] = (empty($entry['HistoryData'])) ? [] : unserialize($entry['HistoryData']);
    }
    $this->assertTrue(count($entries) == 4, $message . ' fullcontact history entry');
    $this->assertTrue(array_search($expectedMessage, array_column($entries, 'Message')) !== FALSE, $message . ' subscriber not fetched due to monthly limit of api key');

    // run
    // we are at an open end (because we ignored the rate limit in this case)
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);
    $laststate++;

    //--------------------------------------------------------------------------
    // CASE 11: 403 error but no rate limit reached, check for monthly limit, rate limit for stat request reached
    //--------------------------------------------------------------------------

    $message = 'CASE 11: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    // set TimeToSend to current time so we can continue
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = :TimeToSend " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
      ':TimeToSend' => time(),
    ));

    // set expiresAt flag to yesterday so monthly limit will be reset
    $VarFullContact['expiresAt'] = time();
    VarFullContact::SetVariable($UserID, $VarFullContact);

    $fullContactUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 403,
        'headers' => array(
          'x-rate-limit-limit' => 60,
          'x-rate-limit-remaining' => 5,
          'x-rate-limit-reset' => 60,
        )
      )
    ));
    variable_set('simpletest-fullcontact-api', $fullContactUrl);

    $resetDelay = 45; // seconds
    $fullContactStatsUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 403,
        'headers' => array(
          'x-rate-limit-limit' => 10,
          'x-rate-limit-remaining' => 0,
          'x-rate-limit-reset' => $resetDelay,
        ),
      ),
    ));
    variable_set('simpletest-fullcontact-stats-api', $fullContactStatsUrl);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT);
    $state['ignoreRateLimit'] = 0;
    $state['overwriteData'] = 0;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid action added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    // we wait one day and not for the delay specified in the x-rate-limit-reset header as the user may have upgraded his plan
    $Delay = db_query("SELECT TimeToSend FROM {".TransactionalQueue::TABLE_NAME."} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
    ))->fetchField();

    $DelayDeviance = $resetDelay - 4;
    $this->assertTrue($Delay >= strtotime("+$DelayDeviance seconds") && $Delay <= strtotime("+$resetDelay seconds"), $message . " rate limit wait $resetDelay seconds: " . ($Delay - time()));

    // run
    // we requeued, therefore job is in STATUS_PENDING
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PENDING);

    //--------------------------------------------------------------------------
    // CASE 12: 500 error
    //--------------------------------------------------------------------------

    $message = 'CASE 12: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    // set TimeToSend to current time so we can continue
    db_query("UPDATE {".TransactionalQueue::TABLE_NAME."} SET TimeToSend = :TimeToSend " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND RelAutoResponderID = :RelAutoResponderID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelAutoResponderID' => $CampaignID,
      ':RelSubscriberID' => $SubscriberID,
      ':TimeToSend' => time(),
    ));

    $fullContactUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 500,
        'headers' => array(
          'x-rate-limit-limit' => 60,
          'x-rate-limit-remaining' => 5,
          'x-rate-limit-reset' => 60,
        )
      )
    ));
    variable_set('simpletest-fullcontact-api', $fullContactUrl);

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    // this is an unexpected error and therefore we simply run through this action
    // we are at an open end
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

    //--------------------------------------------------------------------------
    // CASE 13: 404 error
    //--------------------------------------------------------------------------

    $message = 'CASE 13: ' . CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT;

    $expectedMessage = t("No subscriber data available.");
    $fullContactUrl = url($HttptestURL, array(
      'query' => array(
        'secret' => KLICKTIPP_HTTPTEST_SECRET,
        'code' => 404,
        'headers' => array(
          'x-rate-limit-limit' => 60,
          'x-rate-limit-remaining' => 5,
          'x-rate-limit-reset' => 60,
        )
      )
    ));
    variable_set('simpletest-fullcontact-api', $fullContactUrl);

    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_FULLCONTACT);
    $state['ignoreRateLimit'] = 0;
    $state['overwriteData'] = 0;
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' valid action added');

    // simulate restarting automation
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(empty(CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData())), $message . ' validate');
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);

    // run
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PENDING, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    //trigger the automation again before the cron job
    TransactionEmails::RunStarted($UserID, $SubscriberID, $ReferenceID, $ObjectCamnpaign->GetData(), TRUE);
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB, $laststate + 1, TransactionEmails::STATUS_PAUSED_QUEUE_JOB);

    drupal_queue_cron_run();

    $result = db_query("SELECT HistoryData FROM {subscriber_history} ".
      " WHERE RelOwnerUserID = :UserID AND RelSubscriberID = :SubscriberID AND HistoryType = :HistoryType", array(
      ':UserID' => $UserID,
      ':SubscriberID' => $SubscriberID,
      ':HistoryType' => Subscribers::HISTORY_FULLCONTACT_CALL_FAILED
    ));
    $entries = [];
    while ($entry = kt_fetch_array($result)) {
      // simply overwrite because we want to have the last entry
      $entries[] = (empty($entry['HistoryData']) ? [] : unserialize($entry['HistoryData']));
    }
    $this->assertTrue(count($entries) == 5, $message . ' fullcontact history entry');
    $this->assertTrue(array_search($expectedMessage, array_column($entries, 'Message')) !== FALSE, $message . ' subscriber not fetched due to a rate limit');

    // no data available so run through this action
    // we are at an open end
    simpletest_transactional_call_transactional_send($this, $SubscriberID, $ReferenceID, $UserID, $CampaignID, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER, $laststate + 1, TransactionEmails::STATUS_PAUSED_AFTER);

    $laststate++;

  }
}

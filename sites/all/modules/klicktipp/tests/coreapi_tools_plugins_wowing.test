<?php

use App\Klicktipp\CampaignsProcessFlow;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DomainSet;
use App\Klicktipp\Emails;
use App\Klicktipp\EmailsNewsletterEmail;
use App\Klicktipp\Libraries;
use App\Klicktipp\Personalization;
use App\Klicktipp\Plugin;
use App\Klicktipp\PluginHook;
use App\Klicktipp\PluginHookConnectRedirect;
use App\Klicktipp\PluginParameters;
use App\Klicktipp\PluginValidator;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;
use App\Klicktipp\ToolPluginGeneral;
use App\Klicktipp\ToolPluginSimpletest;
use App\Klicktipp\VarPluginData;

class coreapiToolsPluginsWowingTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'coreapi Plugin Wowing',
      'description' => 'Test classes/tools_plugin.inc Wowing',
      'group' => 'Klicktipp',
    );
  }

  function testPluginWowing() {

    Libraries::include('plugins.inc', '/forms');
    Libraries::include('transactional.inc', '/tests/includes');
    Libraries::include('api.inc', '/tests/includes');

    $ReferenceID = 0;

    /**
     * prepare test data
     **/

    $message = 'prepare test data';

    // misc

    $HttptestURL = 'https://'.KLICKTIPP_DOMAIN.'/kthttptest?secret=' . KLICKTIPP_HTTPTEST_SECRET;
    $AppURL = APP_URL;

    // plugin

    $pluginid = "wowing";

    // test original yaml file

    $filename = "plugin_includes/Wowing/wowing.yml";
    $data = file_get_contents($filename);
    $plugindata = yaml_parse($data);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid, true);

    $validator = PluginValidator::create();
    $validator->validate($plugin);
    $errors = $validator->getErrors();
    $this->assertTrue(count($errors) == 0, "$message original yaml ".print_r($errors,1));

    // prepare wowing plugin test config
    $plugindata = <<<EOF
---
PluginID: $pluginid
PluginName: Wowing
YamlFile: $filename
Class: App\Klicktipp\ToolPluginSimpletest
Hooks:
  rebound:
    Types:
    - hook_rebound
    Request:
      url: $HttptestURL&refid=%Tool:RelIndexed%
      request:
        method: PUT
        timeout: 10
        # our http test callback does not work with json, so use url encoded body
        headers: ~
        json: ~
        data:
          outbound_webhook_url: {$AppURL}api/plugin/inbound
          integration_data:
            klicktipp_entity_url: {$AppURL}plugins/%User:UserID%/%Tool:ToolID%/edit
            klicktipp_entity_name: '%Tool:Name%'
...
EOF;
    $plugindata = yaml_parse($plugindata);
    Plugin::update_plugin($pluginid, $plugindata);
    $plugin = Plugin::get_plugin($pluginid, true);
    $this->assertTrue($plugin['PluginID'] == $pluginid, "$message check 1".print_r($plugin,1));
    $this->assertTrue($plugin['Class'] == ToolPluginSimpletest::class, "$message check 2");

    // in user

    $account = user_load_by_name(USERNAME_ENTERPRISE);
    $UserID = $account->uid;

    // create subscriber

    $firstname = 'Max';
    $lastname = 'Mustermann';
    $EmailAddress1 = "<EMAIL>";
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => array('ListID' => 0),
      'EmailAddress' => $EmailAddress1,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OtherFields' => array(
        'CustomFieldFirstName' => $firstname,
        'CustomFieldLastName' => $lastname,
      )
    );
    $result = Subscribers::Subscribe($Parameters);
    $SubscriberID1 = $result[1];
    $this->assertTrue($SubscriberID1 > 0, $message . ' Subscriber' . $SubscriberID1);

    $EmailAddress3 = "<EMAIL>";
    $Parameters['EmailAddress'] = $EmailAddress3;
    $result = Subscribers::Subscribe($Parameters);
    $SubscriberID3 = $result[1];
    $this->assertTrue($SubscriberID3 > 0, $message . ' Subscriber' . $SubscriberID3);

    $EmailAddress4 = "<EMAIL>";
    $Parameters['EmailAddress'] = $EmailAddress4;
    $result = Subscribers::Subscribe($Parameters);
    $SubscriberID4 = $result[1];
    $this->assertTrue($SubscriberID4 > 0, $message . ' Subscriber' . $SubscriberID4);

    // unknwon subscriber (for auto subscribe by inbound)
    $EmailAddress2 = "<EMAIL>";

    // wowing

    $automation_id = 4711;
    $token = 'wow_api_lp8YaqRNNq9JPwO1O9FuYpx7dLOPzvHr';

    /**
     * Connect
     **/
    $message = 'Connect';

    // reference (foreign entity id and name)
    $_REQUEST['automation_id'] = $automation_id;
    $_REQUEST['automation_name'] = "LightBlue Automation";
    $_REQUEST['token'] = $token;
    // 'webhook_url' will be called
    $_REQUEST['webhook_url'] = 'https://'.KLICKTIPP_DOMAIN.'/kthttptest?secret=' . KLICKTIPP_HTTPTEST_SECRET;
    // 'redirect_url' won't be called here - just for checking the value, but use & so we see the sanitizer in action
    $_REQUEST['redirect_url'] = 'https://'.KLICKTIPP_DOMAIN.'/kthttptest?a=b&c=d';
    $_REQUEST['timestamp'] = time();
    $_REQUEST['hash'] = hash('sha512', implode(',', [$_REQUEST['automation_id'], $_REQUEST['token'], $_REQUEST['timestamp'], "123stagingsecret"]));
    $_REQUEST['dummy'] = 4711;

    // check if "keep form data" works
    $plugin = Plugin::get_plugin($pluginid);
    $fields = PluginHookConnectRedirect::fetchInboundsFromRequest($plugin, $_REQUEST);
    $this->assertTrue($fields['automation_id'] == 'automation_id', "$message automation_id".print_r($fields,1));
    $this->assertTrue($fields['automation_name'] == 'automation_name', "$message automation_name");
    $this->assertTrue($fields['token'] == 'token', "$message token");
    $this->assertTrue($fields['webhook_url'] == 'webhook_url', "$message webhook_url");
    $this->assertTrue($fields['redirect_url'] == 'redirect_url', "$message redirect_url");
    $this->assertTrue($fields['timestamp'] == 'timestamp', "$message timestamp");
    $this->assertTrue($fields['hash'] == 'hash', "$message hash");
    $this->assertTrue(empty($fields['dummy']), "$message dummy");

    // 1st connect
    $_REQUEST['connectdata'] = Core::encryptArray([
      'automation_id' => $automation_id,
      'automation_name' => $_REQUEST['automation_name'],
      'token' => $token,
      'webhook_url' => $_REQUEST['webhook_url'],
      'redirect_url' => $_REQUEST['redirect_url'],
      'timestamp' => $_REQUEST['timestamp'],
      'hash' => $_REQUEST['hash']
    ]);

    $form = klicktipp_plugin_connect_inner($pluginid, $UserID);

    // result of first connect is confirm form
    $this->assertTrue($form['#form_id'] == 'klicktipp_plugin_connect_confirm_form', "$message check form result".htmlspecialchars(substr(print_r($form,1), 0, 200)));
    $connectData = Core::decryptArray($form['request']['connectdata']['#value']);
    $this->assertTrue($connectData['automation_id'] == $automation_id, "$message automation_id ".print_r($connectData,1));
    $this->assertTrue($connectData['automation_name'] == $_REQUEST['automation_name'], "$message automation_name");
    $this->assertTrue($connectData['token'] == $token, "$message token");
    $this->assertTrue($connectData['webhook_url'] == $_REQUEST['webhook_url'], "$message webhook_url");
    $this->assertTrue($connectData['redirect_url'] == $_REQUEST['redirect_url'], "$message redirect_url");
    $this->assertTrue($connectData['timestamp'] == $_REQUEST['timestamp'], "$message timestamp");
    $this->assertTrue($connectData['hash'] == $_REQUEST['hash'], "$message hash");

    // simulate submit
    $variables = $plugin['Hooks']['connect']['Variables'] ?? [];
    VarPluginData::SetPluginDataByParameter($pluginid, $variables, $form['Parameters']['#value']);

    $userdata = VarPluginData::GetPluginDataByPluginID($pluginid, $UserID);
    $this->assertTrue($userdata['%User:UserID%'] == $UserID, "$message check token".print_r($userdata,1));

    // 2nd connect
    $result = klicktipp_plugin_connect_inner($pluginid, $UserID);

    // result of second connect is redirection to wowing (redirect_url)
    $this->assertTrue($result[0] == 'drupal_goto', "$message check goto".print_r($result,1));
    $this->assertTrue($result[1] == $_REQUEST['redirect_url'], "$message check redirect_url");

    /** @var ToolPluginGeneral $ToolObject */
    $ToolObject = ToolPluginGeneral::FromIndex($pluginid, $automation_id);
    $this->assertTrue(!empty($ToolObject), $message . ' FromID'.print_r($ToolObject,1));
    $ToolID = $ToolObject->GetData('ToolID');
    $ToolArray = $ToolObject->GetData();

    // check rebound from "kthttptest"
    $request = $this->fetch_callback_watchdog();
    $this->assertTrue(!empty($request), "$message rebound".print_r($request,1));
    $this->assertTrue($request['request']['refid'] == $automation_id, "$message check ref in rebound_url");
    $this->assertTrue($request['request']['outbound_webhook_url'] == APP_URL.'api/plugin/inbound', "$message check rebound outbound_webhook_url");
    $regex = '/'.preg_quote(APP_URL."plugins/$UserID/",'/')."(\d+)".preg_quote("/edit", '/').'/';
    $result = preg_match($regex, $request['request']['integration_data']['klicktipp_entity_url'], $matches);
    $this->assertTrue($result, "$message check rebound klicktipp_entity_url".print_r($matches,1));
    $this->assertTrue($ToolID == $matches[1], "$message check tool id");

    // check all variables
    $this->assertTrue($ToolArray['Name'] == $request['request']['integration_data']['klicktipp_entity_name'], "$message klicktipp_entity_name");
    $this->assertTrue($ToolArray['ConnectRedirectURL'] == $_REQUEST['redirect_url'], "$message check ConnectRedirectURL");
    $this->assertTrue($ToolArray['RelIndexed'] == $_REQUEST['automation_id'], "$message check RelIndexed");
    $this->assertTrue($ToolArray['ReferenceName'] == $_REQUEST['automation_name'], "$message check ReferenceName");
    $this->assertTrue($ToolArray['ActivationURL'] == $_REQUEST['webhook_url'], "$message check ActivationURL");
    $this->assertTrue($ToolArray['ActivationSmartTagID'] > 0, "$message check ActivationSmartTagID");
    $this->assertTrue($ToolArray['PluginReadySmartTagID'] > 0, "$message check PluginReadySmartTagID");
    $this->assertTrue($ToolArray['PluginStartedSmartTagID'] > 0, "$message check PluginStartedSmartTagID");
    $this->assertTrue($ToolArray['PluginInProgressSmartTagID'] > 0, "$message check PluginInProgressSmartTagID");
    $this->assertTrue($ToolArray['PluginFinishedSmartTagID'] > 0, "$message check PluginFinishedSmartTagID");

    /**
     * CallOutbound
     **/
    $message = 'TriggerOutboundEvent';

    // check outbound is not suppressed
    $ToolObject->TriggerOutboundEvent($SubscriberID1, $ReferenceID);

    // check subscriber tagged
    $this->assertTrue(!empty(Subscribers::RetrieveTagging($UserID, $ToolArray['ActivationSmartTagID'], $SubscriberID1, $ReferenceID)), $message . ' Subscriber tagged');

    // check outbound results
    $request = $this->fetch_callback_watchdog();
    $this->assertTrue(!empty($request), "$message outbound".print_r($request,1));
    $this->assertTrue($request['request']['email'] == $EmailAddress1, "$message check email");
    $this->assertTrue($request['request']['first_name'] == $firstname, "$message check first_name");
    $this->assertTrue($request['request']['last_name'] == $lastname, "$message check last_name");

    /**
     * Inbound connection_test
     **/
    $message = 'inbound connection_test';

    $data = [
      "connection_test" => 1,
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    // the connection test does nothing and has no side effects (as if we get an unknown request)
    // so at least it should not return an error
    $this->assertTrue(empty($result), "$message inbound".print_r($result,1));

    /**
     * Inbound PluginReadySmartTagID
     **/
    $message = 'inbound PluginReadySmartTagID';

    $data = [
      "event" => "contact.notification_due",
      "automation_id" => $automation_id,
      "contact_email" => $EmailAddress1,
      "payload" => [
        "landing_page_url" => "https://app.wowing.io/api/notifications/430c21f3-123/redirect",
        "files" => [
          [
            "mime_type" => "audio/mpeg",
            "path" => "https://cdn-staging.wowing.io/media/123.mp3",
            "somethingnumeric" => 555,
          ],
        ]
      ]
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    $this->assertTrue(empty($result), "$message inbound".print_r($result,1));

    // test Subscriber tagging
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginReadySmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginReadySmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginStartedSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginStartedSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginInProgressSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginInProgressSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginFinishedSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginFinishedSmartTagID');

    /**
     * Inbound PluginStartedSmartTagID
     **/
    $message = 'inbound PluginStartedSmartTagID';

    $data = [
      "event" => "notification.file_viewed",
      "automation_id" => $automation_id,
      "contact_email" => $EmailAddress1,
      "payload" => [
        "percentage_watched" => 0.03
      ]
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    $this->assertTrue(empty($result), "$message inbound".print_r([$result, json_encode($data)],1));

    // test Subscriber tagging
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginReadySmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginReadySmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginStartedSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginStartedSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginInProgressSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginInProgressSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginFinishedSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginFinishedSmartTagID');

    /**
     * Inbound PluginInProgressSmartTagID
     **/
    $message = 'inbound PluginInProgressSmartTagID';

    $data = [
      "event" => "notification.file_viewed",
      "automation_id" => $automation_id,
      "contact_email" => $EmailAddress1,
      "payload" => [
        "percentage_watched" => 0.63
      ]
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    $this->assertTrue(empty($result), "$message inbound".print_r([$result, json_encode($data)],1));

    // test Subscriber tagging
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginReadySmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginReadySmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginStartedSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginStartedSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginInProgressSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginInProgressSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginFinishedSmartTagID'), $SubscriberID1, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginFinishedSmartTagID');

    /**
     * Inbound PluginFinishedSmartTagID
     **/
    $message = 'inbound PluginFinishedSmartTagID';

    $data = [
      "event" => "notification.file_viewed",
      "automation_id" => $automation_id,
      "contact_email" => $EmailAddress4,
      "payload" => [
        "percentage_watched" => 0.9
      ]
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    $this->assertTrue(empty($result), "$message inbound".print_r([$result, json_encode($data)],1));

    // test Subscriber tagging
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginReadySmartTagID'), $SubscriberID4, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginReadySmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginStartedSmartTagID'), $SubscriberID4, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginStartedSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginInProgressSmartTagID'), $SubscriberID4, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginInProgressSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginFinishedSmartTagID'), $SubscriberID4, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginFinishedSmartTagID');

    $data = [
      "event" => "notification.file_viewed",
      "automation_id" => $automation_id,
      "contact_email" => $EmailAddress3,
      "payload" => [
        "percentage_watched" => 1
      ]
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    $this->assertTrue(empty($result), "$message inbound".print_r([$result, json_encode($data)],1));

    // test Subscriber tagging
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginReadySmartTagID'), $SubscriberID3, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginReadySmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginStartedSmartTagID'), $SubscriberID3, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginStartedSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginInProgressSmartTagID'), $SubscriberID3, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginInProgressSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginFinishedSmartTagID'), $SubscriberID3, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginFinishedSmartTagID');

    /**
     * auto subscribe by inbound w/ unknown subscriber
     **/
    $message = 'auto subscribe by inbound';

    $data = [
      "event" => "contact.notification_due",
      "automation_id" => $automation_id,
      "contact_email" => $EmailAddress2,
      "contact_first_name" => 'Alice',
      "contact_last_name" => 'Wonder',
      "payload" => [
        "landing_page_url" => "https://app.wowing.io/api/notifications/430c21f3-123/redirect",
        "files" => [
          [
            "mime_type" => "audio/mpeg",
            "path" => "https://cdn-staging.wowing.io/media/123.mp3",
            "somethingnumeric" => 555,
          ],
        ]
      ]
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    $this->assertTrue(empty($result), "$message inbound".print_r($result,1));

    // test subscription
    $Subscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddress2);
    $this->assertTrue(!empty($Subscriber), "$message subscribed".print_r($Subscriber,1));
    $this->assertTrue($Subscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message SubscriptionStatus");
    $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($FullSubscriberWithFields['CustomFieldFirstName'] == 'Alice', "$message CustomFieldFirstName".print_r($FullSubscriberWithFields,1));
    $this->assertTrue($FullSubscriberWithFields['CustomFieldLastName'] == 'Wonder', "$message CustomFieldLastName");

    // test Subscriber tagging
    $SubscriberID2 = $FullSubscriberWithFields['SubscriberID'];
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginReadySmartTagID'), $SubscriberID2, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' PluginReadySmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginStartedSmartTagID'), $SubscriberID2, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginStartedSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginInProgressSmartTagID'), $SubscriberID2, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginInProgressSmartTagID');
    $Tagging = Subscribers::RetrieveTagging($UserID, $ToolObject->GetData('PluginFinishedSmartTagID'), $SubscriberID2, $ReferenceID);
    $this->assertTrue(empty($Tagging), $message . ' PluginFinishedSmartTagID');

    // check outbound is suppressed
    $result = $ToolObject->TriggerOutboundEvent($SubscriberID2, $ReferenceID);
    $this->assertTrue(empty($result), $message . ' TriggerOutboundEvent did nothing');

    /**
     *  --- Placeholders ---
     **/
    $message = 'placeholders';

    $landingpage = "https://app.wowing.io/api/notifications/430c21f3-123/redirect";
    $previewimage = "https://cdn-staging.wowing.io/assets/audio-preview.png";
    $data = [
      "event" => "contact.notification_due",
      "automation_id" => $automation_id,
      "contact_email" => $EmailAddress1,
      "payload" => [
        "landing_page_url" => $landingpage,
        "files" => [
          [
            "mime_type" => "audio/mpeg",
            "path" => "https://cdn-staging.wowing.io/media/123.mp3",
          ],
        ],
        "preview_file_path" => [
          [
            "mime_type" => "image/png",
            "path" => $previewimage
          ]
        ],
      ]
    ];
    $_GET['q'] = "api/plugin/inbound/$pluginid";
    $result = ToolPluginSimpletest::klicktippapi_plugin_inbound($pluginid, $data);
    $this->assertTrue(empty($result), "$message inbound".print_r($result,1));

    // check subscriber
    $Subscriber = Subscription::RetrieveSubscriptionByContactInfoAndReference($UserID, $EmailAddress1, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID, TRUE);
    $this->assertTrue(!empty($Subscriber), "$message subscribed".print_r($Subscriber,1));
    $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $Subscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($FullSubscriberWithFields['EmailAddress'] == $EmailAddress1, "$message EmailAddress");

    //check WowingLandingPage
    $value = CustomFields::GetCustomFieldData($UserID, $Subscriber['SubscriberID'], CustomFields::$GlobalCustomFieldDefs['Plugin'], $ReferenceID, "Link-$ToolID");
    $this->assertTrue($value == $data['payload']['landing_page_url'], "$message WowLP");
    // check WowPI
    $value = CustomFields::GetCustomFieldData($UserID, $Subscriber['SubscriberID'], CustomFields::$GlobalCustomFieldDefs['Plugin'], $ReferenceID, "Image-$ToolID");
    $this->assertTrue($value == $data['payload']['preview_file_path'][0]['path'], "$message WowPI");

    // --- check email generation

    $LandingPageParam = '%Subscriber:CustomFieldPlugin:Link-%Tool:ToolID%%';
    $PreviewImageParam = '%Subscriber:CustomFieldPlugin:Image-%Tool:ToolID%%';

    // email template content
    $paramtool = ['%Tool:ToolID%' => $ToolID];
    $ContentPlain = strtr($plugin['Placeholders']['preview']['plain'], $paramtool);
    $ContentHTML = strtr($plugin['Placeholders']['preview']['html'], $paramtool);

    // final email content
    $paramfinal = [$LandingPageParam => $landingpage, $PreviewImageParam => $previewimage];
    $parameters = new PluginParameters($plugin, $paramfinal);
    PluginHook::prepare($parameters, [], []);
    $ExpectedPlain = strtr($plugin['Placeholders']['preview']['plain'], $parameters->getParameters());
    $ExpectedHTML = strtr($plugin['Placeholders']['preview']['html'], $parameters->getParameters());
    $ExpectedForPreviewEmail = strtr($plugin['Placeholders']['preview']['preview_image'], $parameters->getParameters());

    // create a fresh email
    $ArrayFieldAndValues = array(
      'RelUserID' => $UserID,
      'EmailName' => 'Plugin',
      'ReportSpamEnabled' => 1,
      'TurnOffTrackLink' => 1,
      'Subject' => 'Plugin',
      'PlainContent' => $ContentPlain,
      'HTMLContent' => $ContentHTML,
    );
    $EmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($EmailID > 0, $message . ' create email');

    $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);

    $SenderDomain = DomainSet::SelectValidSenderDomainById($UserID, $EmailID);

    // PREVIEW

    [$TMPSubject, $PlainContent, $HTMLContent, $ContentType] = Personalization::PersonalizeEmail($account, 7777, $ArrayEmail, [], $FullSubscriberWithFields, $ReferenceID, $SenderDomain, [], FALSE, TRUE);

    // check plain content
    $this->assertTrue(strpos($PlainContent, $landingpage) === FALSE, $message . ' plain: preview landingpage '.$PlainContent);
    $this->assertTrue(strpos($PlainContent, $previewimage) === FALSE, $message . ' plain: preview image');

    // check HTML content
    $this->assertTrue(strpos($HTMLContent, $landingpage) === FALSE, $message . ' HTML: preview landingpage '.htmlspecialchars($HTMLContent));
    $this->assertTrue(strpos($HTMLContent, $ExpectedForPreviewEmail) !== FALSE, $message . ' HTML: preview image');

    // NOT PREVIEW

    [$TMPSubject, $PlainContent, $HTMLContent, $ContentType] = Personalization::PersonalizeEmail($account, 7777, $ArrayEmail, [], $FullSubscriberWithFields, $ReferenceID, $SenderDomain, [], FALSE, FALSE);

    // check plain content
    $this->assertTrue(strpos($PlainContent, $landingpage) !== FALSE, $message . ' plain: landingpage '.$PlainContent);
    $this->assertTrue(strpos($PlainContent, $previewimage) === FALSE, $message . ' plain: preview image');
    $this->assertTrue($PlainContent == $ExpectedPlain, $message . ' plain '.$ExpectedPlain);

    // check HTML content
    $this->assertTrue(strpos($HTMLContent, $landingpage) > 0, $message . ' HTML: landingpage '.htmlspecialchars($HTMLContent));
    $this->assertTrue(strpos($HTMLContent, $previewimage) > 0, $message . ' HTML: preview image');

    // D+D EDITOR EMAIL

    // d+d editor tags
    $EditorTags = Personalization::GetEditorTags($account, Emails::TYPE_AUTOMATIONEMAIL);
    $this->assertTrue(!empty($EditorTags['tags']), "$message GetEditorTags");
    $ddeditortag = array_filter($EditorTags['links'], function($t) use ($ToolID) { return $t['id'] == $ToolID; });
    $placeholder = "%Subscriber:CustomFieldPlugin:Link-$ToolID%";
    $this->assertTrue(!empty($ddeditortag), "$message links");
    $ddeditortag = reset($ddeditortag);
    $this->assertTrue($ddeditortag['href'] == $placeholder, "$message href ".htmlspecialchars(print_r($ddeditortag,1)));
    $this->assertTrue(!empty($ddeditortag['title']), "$message title ");
    $ddeditortag = array_filter($EditorTags['tags'], function($t) use ($ToolID) { return $t['id'] == $ToolID; });

    $this->assertTrue(!empty($ddeditortag), "$message tags");
    $ddeditortag = reset($ddeditortag);
    $this->assertTrue(strpos($ddeditortag['ckeditor']['html'], $placeholder) !== false, "$message ckeditor placeholder ");

    $ddeditortag = array_filter($EditorTags['customaddons'], function($t) use ($ToolID) { return $t['id'] == $ToolID; });
    $this->assertTrue(!empty($ddeditortag), "$message customaddons");
    $ddeditortag = reset($ddeditortag);
    $this->assertTrue(strpos($ddeditortag['html'], $placeholder) !== false, "$message bee addOn html ");
    $this->assertTrue($ddeditortag['button_link'] == $placeholder, "$message bee addOn button_link ");

    $ContentHTML = $ddeditortag['html'];

    // create a fresh email
    $ArrayFieldAndValues = array(
      'RelUserID' => $UserID,
      'EmailName' => 'Plugin',
      'ReportSpamEnabled' => 1,
      'TurnOffTrackLink' => 1,
      'Subject' => 'Plugin',
      'PlainContent' => $ContentPlain,
      'HTMLContent' => $ContentHTML,
    );
    $EmailID = EmailsNewsletterEmail::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($EmailID > 0, $message . ' create email');

    $ArrayEmail = Emails::RetrieveEmailByID($UserID, $EmailID);

    // check preview HTML content
    [$TMPSubject, $PlainContent, $HTMLContent, $ContentType] = Personalization::PersonalizeEmail($account, 7777, $ArrayEmail, [], $FullSubscriberWithFields, $ReferenceID, $SenderDomain, [], FALSE, TRUE);
    $this->assertTrue(strpos($HTMLContent, $landingpage) === FALSE, $message . ' HTML: preview landingpage '.htmlspecialchars($HTMLContent));
    //TODO component fields
    //$this->assertTrue(strpos($HTMLContent, $ExpectedForPreviewEmail) !== FALSE, $message . ' HTML: preview image');

    // check non preview HTML content
    //TODO component fields
    /*
    list($TMPSubject, $PlainContent, $HTMLContent, $ContentType) = Personalization::PersonalizeEmail($account, 7777, $ArrayEmail, [], $FullSubscriberWithFields, $ReferenceID, $SenderDomain, [], FALSE, FALSE);
    $this->assertTrue(strpos($HTMLContent, $landingpage) > 0, $message . ' HTML: landingpage '.htmlspecialchars($HTMLContent));
    $this->assertTrue(strpos($HTMLContent, $previewimage) > 0, $message . ' HTML: preview image');
    $this->assertTrue(strpos($HTMLContent, trim($plugin['ThumbnailS'])) !== FALSE, $message . ' HTML: Thumbnail start code');
    */

    /**
     *  --- Conditions ---
     **/
    $message = 'conditions';

    $ExpectedConditions = [
      'label' => "Wowing Video",
      'createType' => Plugin::ENTITY_SERVICE_TYPE_PLUGIN,
      'actions' => [
        'ready' => "versandbereit",
        'started' => "gestartet",
        'inprogress' => "zu mehr als 50% angesehen",
        'finished' => "vollständig angesehen",
      ],
      'operations' => [
        [
          'value' => 'has',
          'label' => "Das Video ist für Kontakt",
          'actions' => [
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_READY,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_STARTED,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_IN_PROGRESS,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_FINISHED,
          ],
        ],
        [
          'value' => 'has not',
          'label' => "Das Video ist für Kontakt nicht",
          'actions' => [
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_READY,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_STARTED,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_IN_PROGRESS,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_FINISHED,
          ],
        ],
        [
          'value' => 'any',
          'label' => "Ein Video ist für Kontakt",
          'actions' => [
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_READY,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_STARTED,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_IN_PROGRESS,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_FINISHED,
          ],
        ],
        [
          'value' => 'not any',
          'label' => "Kein Video ist für Kontakt",
          'actions' => [
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_READY,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_STARTED,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_IN_PROGRESS,
            $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_FINISHED,
          ],
        ],
      ],
    ];
    $ExpectedConditionOps = [
      $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_READY => [
        'type' => 'wowing',
        'source' => Plugin::ENTITY_SERVICE_TYPE_PLUGIN,
        'subtype' => 'plugin-wowing',
        'field' => 'PluginReadySmartTagID',
        'action' => 'ready',
        'condition' => [
          'withEntity' => 'has',
          'withoutEntity' => 'any',
        ],
      ],
      $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_READY => [
        'type' => 'wowing',
        'source' => Plugin::ENTITY_SERVICE_TYPE_PLUGIN,
        'subtype' => 'plugin-wowing',
        'field' => 'PluginReadySmartTagID',
        'action' => 'ready',
        'condition' => [
          'withEntity' => 'has not',
          'withoutEntity' => 'not any',
        ],
      ],
      $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_STARTED => [
        'type' => 'wowing',
        'source' => Plugin::ENTITY_SERVICE_TYPE_PLUGIN,
        'subtype' => 'plugin-wowing',
        'field' => 'PluginStartedSmartTagID',
        'action' => 'started',
        'condition' => [
          'withEntity' => 'has',
          'withoutEntity' => 'any',
        ],
      ],
      $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_STARTED => [
        'type' => 'wowing',
        'source' => Plugin::ENTITY_SERVICE_TYPE_PLUGIN,
        'subtype' => 'plugin-wowing',
        'field' => 'PluginStartedSmartTagID',
        'action' => 'started',
        'condition' => [
          'withEntity' => 'has not',
          'withoutEntity' => 'not any',
        ],
      ],
      $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_IN_PROGRESS => [
        'type' => 'wowing',
        'source' => Plugin::ENTITY_SERVICE_TYPE_PLUGIN,
        'subtype' => 'plugin-wowing',
        'field' => 'PluginInProgressSmartTagID',
        'action' => 'inprogress',
        'condition' => [
          'withEntity' => 'has',
          'withoutEntity' => 'any',
        ],
      ],
      $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_IN_PROGRESS => [
        'type' => 'wowing',
        'source' => Plugin::ENTITY_SERVICE_TYPE_PLUGIN,
        'subtype' => 'plugin-wowing',
        'field' => 'PluginInProgressSmartTagID',
        'action' => 'inprogress',
        'condition' => [
          'withEntity' => 'has not',
          'withoutEntity' => 'not any',
        ],
      ],
      $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_FINISHED => [
        'type' => 'wowing',
        'source' => Plugin::ENTITY_SERVICE_TYPE_PLUGIN,
        'subtype' => 'plugin-wowing',
        'field' => 'PluginFinishedSmartTagID',
        'action' => 'finished',
        'condition' => [
          'withEntity' => 'has',
          'withoutEntity' => 'any',
        ],
      ],
      $pluginid.CampaignsProcessFlow::PROCESSFLOW_CONDITION_PLUGIN_SEPARATOR.Plugin::CONDITION_PLUGIN_NOT_FINISHED => [
        'type' => 'wowing',
        'source' => Plugin::ENTITY_SERVICE_TYPE_PLUGIN,
        'subtype' => 'plugin-wowing',
        'field' => 'PluginFinishedSmartTagID',
        'action' => 'finished',
        'condition' => [
          'withEntity' => 'has not',
          'withoutEntity' => 'not any',
        ],
      ],
    ];

    [$Conditions, $ConditionOps] = Plugin::conditions_for_plugin($plugin);
    $this->assertTrue($Conditions == $ExpectedConditions, "$message conditions:".print_r($Conditions,1));
    $this->assertTrue($ConditionOps == $ExpectedConditionOps, "$message condition ops:".print_r($ConditionOps,1));

    /**
     *  --- Campoigns ---
     **/
    $message = 'Campaign';


    $ManualTagID = Tag::CreateManualTag($UserID, 'manual test tag 1', '');
    $this->assertTrue($ManualTagID > 0, "$message #$ManualTagID");

    // create wowing app

    $automation_id = 1234567890;
    $_REQUEST['automation_id'] = $automation_id;
    $_REQUEST['automation_name'] = "They call it Kampagne now";
    $_REQUEST['token'] = $token;
    $_REQUEST['webhook_url'] = 'https://'.KLICKTIPP_DOMAIN.'/kthttptest?secret=' . KLICKTIPP_HTTPTEST_SECRET;
    $_REQUEST['redirect_url'] = 'https://'.KLICKTIPP_DOMAIN.'/kthttptest?a=b&c=d';
    $_REQUEST['timestamp'] = time();
    $_REQUEST['hash'] = hash('sha512', implode(',', [$_REQUEST['automation_id'], $_REQUEST['token'], $_REQUEST['timestamp'], "123stagingsecret"]));

    $_REQUEST['connectdata'] = Core::encryptArray([
      'automation_id' => $_REQUEST['automation_id'],
      'automation_name' => $_REQUEST['automation_name'],
      'token' => $_REQUEST['token'],
      'webhook_url' => $_REQUEST['webhook_url'],
      'redirect_url' => $_REQUEST['redirect_url'],
      'timestamp' => $_REQUEST['timestamp'],
      'hash' => $_REQUEST['hash']
    ]);

    $result = klicktipp_plugin_connect_inner($pluginid, $UserID);
    $this->assertTrue($result[0] == 'drupal_goto', "$message check goto ".print_r($result,1));
    /** @var ToolPluginGeneral $ToolObject */
    $ToolObject = ToolPluginGeneral::FromIndex($pluginid, $automation_id);
    $this->assertTrue(!empty($ToolObject), $message . ' FromIndex '.print_r($ToolObject,1));

    // create campaign

    // create campaign with START state 'is tagged with' $ManualTagID
    $CampaignID = simpletest_transactional_create_campaign($this, $message, $UserID, $ManualTagID);
    $this->assertTrue($CampaignID > 0, $message . ' id');

    /** @var CampaignsProcessFlow $ObjectCamnpaign */
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(!empty($ObjectCamnpaign), $message . ' object');

    $laststate = 1;

    // add ACTION state 'outbound' with plugin
    $state = simpletest_transactional_create_processflow_state_action($laststate + 1, CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_OUTBOUND);
    $state['outboundID'] = $ToolObject->GetData('ToolID');
    simpletest_transactional_create_processflow_add_state($ObjectCamnpaign, $state, $laststate);
    $laststate = $laststate + 1;

    // save
    $result = $ObjectCamnpaign->UpdateDB($ObjectCamnpaign->GetData());
    $this->assertTrue($result, $message . ' created');

    // validate
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $TodoList = CampaignsProcessFlow::ValidateAutomation($ObjectCamnpaign->GetData());
    $this->assertTrue(empty($TodoList), $message . ' validate'. print_r($TodoList,1) . print_r($ObjectCamnpaign->GetData('ProcessFlow'),1));

  }

  function fetch_callback_watchdog() {
    // fetches entry from klicktipp_httptest_callback
    $vars = db_query("SELECT variables FROM {watchdog} WHERE type = 'kt-test' ORDER BY wid DESC")->fetchField();
    return (!empty($vars)) ? unserialize($vars) : FALSE;
  }

}

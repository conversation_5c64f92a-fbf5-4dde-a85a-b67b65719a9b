<?php

use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\CampaignsProcessFlow;
use App\Klicktipp\Emails;
use App\Klicktipp\EmailsAutomationEmail;
use App\Klicktipp\EmailsAutomationSMS;
use App\Klicktipp\EmailsNewsletterEmail;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\MarketingTools;
use App\Klicktipp\NewsletterQueue;
use App\Klicktipp\SplitTests;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Tag;
use App\Klicktipp\ToolOutboundGeneral;
use App\Klicktipp\TransactionalQueue;
use App\Klicktipp\TransactionEmails;

class coreapiSplitTestsTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'coreapi SplitTests',
      'description' => 'Test SplitTests.',
      'group' => 'Klicktipp',
    );

  }

  function testSplitTests() {

    Libraries::include('queue.inc', '/tests/includes');
    Libraries::include('full_user.inc', '/tests/includes');
    Libraries::include('transactional.inc', '/tests/includes');

    /**
     * prepare test data
     **/
    $message = 'prepare test data';

    $UserID = 1;
    $ReferenceID = 0;

    $HttptestURL = 'https://dev:ovi8eTei@'.KLICKTIPP_DOMAIN.'/kthttptest';

    // manipulate user cache
    $NexmoApiKey = 'nexmoapikey';
    $NexmoApiSecret = 'nexmoapisecret';
    $account = user_load($UserID);
    $edit = array();
    $edit['UserSettings']['SMSSettings']['NexmoApiKey'] = $NexmoApiKey;
    $edit['UserSettings']['SMSSettings']['NexmoApiSecret'] = $NexmoApiSecret;
    user_save($account, $edit);
    $account = user_load($UserID);
    $this->assertTrue($account->UserSettings['SMSSettings']['NexmoApiSecret'] == $NexmoApiSecret, ' NexmoApiSecret');
    $this->assertTrue(user_access('access sms marketing', $account), 'sms access');

    // create campaign
    $ArrayFieldAndValues = array(
      'CampaignName' => $message,
      'RelOwnerUserID' => $UserID,
    );
    $CampaignID = CampaignsProcessFlow::InsertDB($ArrayFieldAndValues);
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue(!empty($ObjectCamnpaign), $message . ' campaign');

    // create automation email
    $ArrayEmail = array(
      'RelUserID' => $UserID,
      'EmailName' => t('Email: @name', array('@name' => $message)),
      'ContentTypeEnum' => Emails::CONTENT_TYPE_PLAIN,
      'Subject' => "$message subject",
      'PlainContent' => "$message plain",
      'HTMLContent' => "$message <a href='http://www.example.com'>and a link</a>",
    );
    $AutomationEmailID = EmailsAutomationEmail::InsertDB($ArrayEmail);
    $ObjectAutomationEmail = Emails::FromID($UserID, $AutomationEmailID);
    $this->assertTrue(!empty($ObjectAutomationEmail), $message . ' ObjectAutomationEmail');

    $ArrayEmail['EmailName'] .= '2';
    $AutomationSMSID = EmailsAutomationSMS::InsertDB($ArrayEmail);
    $ObjectAutomationSMS = Emails::FromID($UserID, $AutomationSMSID);
    $this->assertTrue(!empty($ObjectAutomationSMS), $message . ' ObjectAutomationSMS');

    $TagID = Tag::CreateManualTag($UserID, $message, '');
    $ObjectTag = Tag::FromID($UserID, $TagID);
    $this->assertTrue(!empty($ObjectTag), $message . ' ObjectTag');

    /**
     * InsertDB
     **/
    $message = 'InsertDB';
    $NewID = SplitTests::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'RelCampaignID' => $CampaignID,
      'TestDuration' => 3600,
    ));
    $this->assertTrue($NewID > 0, $message . ' id');

    /**
     * FromID
     **/
    $message = 'FromID';
    /** @var SplitTests $SplittestObject */
    $SplittestObject = SplitTests::FromID($UserID, $NewID);
    $this->assertTrue(!empty($SplittestObject), $message . ' object');

    /**
     * GetData
     **/
    $message = 'GetData';
    $this->assertTrue($SplittestObject->GetData('TestDuration') == 3600, $message . ' TestDuration');

    /**
     * AddVariant
     **/
    $message = 'AddVariant';
    $SplittestObject->AddVariant(get_class($ObjectAutomationEmail), $AutomationEmailID, $ObjectAutomationEmail->GetData('EmailOpenedSmartTagID'));
    $Variants = $SplittestObject->GetData('Variants');
    $this->assertTrue($Variants[0]['Weight'] == 100, $message . ' one'.print_r($Variants,1));
    $SplittestObject->AddVariant(get_class($ObjectAutomationEmail), 4712);
    $SplittestObject->AddVariant(EmailsAutomationSMS::class, $AutomationSMSID, $ObjectAutomationSMS->GetData('SMSClickedSmartTagID'));
    $Variants = $SplittestObject->GetData('Variants');
    $this->assertTrue($Variants[0]['Weight'] == 34, $message . ' 0'.print_r($Variants,1));
    $this->assertTrue($Variants[1]['Weight'] == 33, $message . ' 1');
    $this->assertTrue($Variants[2]['Weight'] == 33, $message . ' 2');
    $SplittestObject->AddVariant(Tag::class, $TagID, $TagID);
    $Variants = $SplittestObject->GetData('Variants');
    $this->assertTrue($Variants[0]['Weight'] == 25, $message . ' four 0'.print_r($Variants,1));
    $this->assertTrue($Variants[1]['Weight'] == 25, $message . ' four 1');
    $this->assertTrue($Variants[2]['Weight'] == 25, $message . ' four 2');
    $this->assertTrue($Variants[3]['Weight'] == 25, $message . ' four 3');

    /**
     * RemoveVariant
     **/
    $message = 'RemoveVariant';
    $SplittestObject->RemoveVariant(get_class($ObjectAutomationEmail), 4712);
    $Variants = $SplittestObject->GetData('Variants');
    $this->assertTrue($Variants[0]['Weight'] == 33, $message . ' 0'.print_r($Variants,1));
    $this->assertTrue($Variants[1]['Weight'] == 33, $message . ' 1');
    $this->assertTrue($Variants[2]['Weight'] == 34, $message . ' 2');

    /**
     * GetVariantEntity
     **/
    $message = 'GetVariantEntity';
    $VariantEntity = $SplittestObject->GetVariantEntity(0);
    $this->assertTrue(get_class($VariantEntity) == get_class($ObjectAutomationEmail), $message . ' class'.print_r($VariantEntity,1));
    $this->assertTrue($VariantEntity->GetData('EmailID') == $AutomationEmailID, $message . ' EmailID');
    $VariantEntity = $SplittestObject->GetVariantEntity(99);
    $this->assertTrue(empty($VariantEntity), $message . ' empty '.print_r($VariantEntity,1));
    $SplittestObject->UpdateDB($SplittestObject->GetData());

    /**
     * StartSplittests
     **/
    $message = 'StartSplittests';
    SplitTests::StartSplittests($UserID, $CampaignID);
    $SplittestObject = SplitTests::FromID($UserID, $NewID);
    $this->assertTrue($SplittestObject->GetData('started') > 0, $message . ' started'.print_r($SplittestObject,1));

    /**
     * GetNextSplittestVariant
     **/
    $message = 'GetNextSplittestVariant';

    // we have 3 variants:
    // 1. EmailsAutomationEmail = 25%
    // 2. EmailsAutomationSMS = 50%
    // 3. Tag = 25%
    $Data = $SplittestObject->GetData();
    $Data['Variants'][0]['Weight'] = 25;
    $Data['Variants'][1]['Weight'] = 50;
    $Data['Variants'][2]['Weight'] = 25;
    $SplittestObject->SetData($Data);

    // now check if we get them in the right order
    $expected_class = array(EmailsAutomationEmail::class, EmailsAutomationSMS::class, EmailsAutomationSMS::class, Tag::class);
    for ($sent = 0; $sent < 16; $sent++) {
      $VariantEntity = $SplittestObject->GetNextSplittestVariant();
      $this->assertTrue($SplittestObject->GetData('TotalSent') == $sent + 1, "$message $sent total sent");
      $mod4 = $sent % 4;
      $this->assertTrue(get_class($VariantEntity) == $expected_class[$mod4], "$message $sent class" . get_class($VariantEntity));
    }
    $this->assertTrue($SplittestObject->GetData('Variants')[0]['Sent'] == 4, $message . ' 0 sent');
    $this->assertTrue($SplittestObject->GetData('Variants')[1]['Sent'] == 8, $message . ' 1 sent');
    $this->assertTrue($SplittestObject->GetData('Variants')[2]['Sent'] == 4, $message . ' 2 sent');

    /**
     * GetVariantTaggingCount
     **/
    $message = 'GetVariantTaggingCount';

    // simulate sending and conversion - variant 0
    $variant = 0;

    $SubscriberID = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => 0,
        'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
      ),
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => 0,
      'SendConfirmationEmail' => false,
      'TriggerAutoResponders' => true,
      'ReferenceID' => $ReferenceID,
      'EmailAddress' => '<EMAIL>',
    ))[1];

    kt_insert_row(array(
      'RelOwnerUserID' => $UserID,
      'RelSubscriberID' => $SubscriberID,
      'TimeToSend' => time(),
      'RelAutoResponderID' => $CampaignID,
      'StatusEnum' => TransactionEmails::STATUS_SENT,
      'StatusReason' => 0,
      'StateID' => 1,
    ), '{'.TransactionalQueue::TABLE_NAME.'}');
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 0, "$message count conversions variant $variant before action");
    Subscribers::TagSubscriber($UserID, $SubscriberID, $ObjectAutomationEmail->GetData('EmailSentSmartTagID'), $ReferenceID, FALSE);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 0, "$message count conversions variant $variant after action");
    Subscribers::TagSubscriber($UserID, $SubscriberID, $ObjectAutomationEmail->GetData('EmailOpenedSmartTagID'), $ReferenceID, FALSE);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 1, "$message count conversions variant $variant after conversion");

    // simulate sending and conversion - variant 1
    $variant = 1;
    $SubscriberID = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => 0,
        'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
      ),
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => 0,
      'SendConfirmationEmail' => false,
      'TriggerAutoResponders' => true,
      'ReferenceID' => $ReferenceID,
      'EmailAddress' => '<EMAIL>',
    ))[1];
    kt_insert_row(array(
      'RelOwnerUserID' => $UserID,
      'RelSubscriberID' => $SubscriberID,
      'TimeToSend' => time(),
      'RelAutoResponderID' => $CampaignID,
      'StatusEnum' => TransactionEmails::STATUS_SENT,
      'StatusReason' => 0,
      'StateID' => 1,
    ), '{'.TransactionalQueue::TABLE_NAME.'}');
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 0, "$message count conversions variant $variant before action");
    Subscribers::TagSubscriber($UserID, $SubscriberID, $ObjectAutomationSMS->GetData('SMSSentSmartTagID'), $ReferenceID, FALSE);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 0, "$message count conversions variant $variant after action");
    Subscribers::TagSubscriber($UserID, $SubscriberID, $ObjectAutomationSMS->GetData('SMSClickedSmartTagID'), $ReferenceID, FALSE);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 1, "$message count conversions variant $variant after conversion");

    // simulate sending and conversion - variant 2
    $variant = 2;
    $SubscriberID = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => 0,
        'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
      ),
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => 0,
      'SendConfirmationEmail' => false,
      'TriggerAutoResponders' => true,
      'ReferenceID' => $ReferenceID,
      'EmailAddress' => '<EMAIL>',
    ))[1];
    kt_insert_row(array(
      'RelOwnerUserID' => $UserID,
      'RelSubscriberID' => $SubscriberID,
      'TimeToSend' => time(),
      'RelAutoResponderID' => $CampaignID,
      'StatusEnum' => TransactionEmails::STATUS_SENT,
      'StatusReason' => 0,
      'StateID' => 1,
    ), '{'.TransactionalQueue::TABLE_NAME.'}');
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 0, "$message count conversions variant $variant before action");
    Subscribers::TagSubscriber($UserID, $SubscriberID, $TagID, $ReferenceID, FALSE);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 1, "$message count conversions variant $variant after conversion");

    /**
     * FinishSplittest
     **/
    $message = 'FinishSplittest';

    // add another conversion  to variant 2, so it becomes winner
    $SubscriberID = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => 0,
        'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
      ),
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => 0,
      'SendConfirmationEmail' => false,
      'TriggerAutoResponders' => true,
      'ReferenceID' => $ReferenceID,
      'EmailAddress' => '<EMAIL>',
    ))[1];
    kt_insert_row(array(
      'RelOwnerUserID' => $UserID,
      'RelSubscriberID' => $SubscriberID,
      'TimeToSend' => time(),
      'RelAutoResponderID' => $CampaignID,
      'StatusEnum' => TransactionEmails::STATUS_SENT,
      'StatusReason' => 0,
      'StateID' => 1,
    ), '{'.TransactionalQueue::TABLE_NAME.'}');
    Subscribers::TagSubscriber($UserID, $SubscriberID, $TagID, $ReferenceID, FALSE);

    // finish
    $VariantEntity = $SplittestObject->FinishSplittest();
    $this->assertTrue(get_class($VariantEntity) == Tag::class, $message . ' winner entity '.print_r($SplittestObject,1));
    $this->assertTrue($SplittestObject->GetData('finished') >= $SplittestObject->GetData('started'), $message . ' finished');
    $this->assertTrue($SplittestObject->GetData('WinnerID') == 2, $message . ' WinnerID');

    /**
     * CreateSplittestForNewsletter
     */
    $message = 'CreateSplittestForNewsletter';

    // create splittest campaign
    $ArrayFieldAndValues = array(
      'CampaignName' => $message,
      'RelOwnerUserID' => $UserID,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_CAMPAIGN,
      // this forces a call to CreateSplittestForNewsletter in InsertDB
      'SplitTesting' => TRUE,
      'TestSize' => 50,
      'TestDuration' => 3600,
      'WinnerEnum' => SplitTests::WINNER_OPENS,
    );
    $CampaignNLID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $ObjectCamnpaign = Campaigns::FromID($UserID, $CampaignNLID);
    $this->assertTrue(!empty($ObjectCamnpaign), $message . ' campaign');

    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCamnpaign->GetData());
    $this->assertTrue(!empty($SplittestObject), $message . ' splittest');

    /**
     * AddVariantForNewsletter
     */
    $message = 'AddVariantForNewsletter';

    // see klicktipp_campaign_duplicatetestemail
    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCamnpaign->GetData());
    $ObjectEmail = $SplittestObject->GetVariantEntity(0);
    $this->assertTrue(!empty($ObjectEmail), $message . ' email');

    $ArrayEmail = $ObjectEmail->GetData();
    $EmailID = $ArrayEmail['EmailID'];
    unset($ArrayEmail['EmailID']);
    $ArrayEmail['EmailName'] = t('Duplicate of: ') . $ArrayEmail['EmailName'];
    $NewEmailID = EmailsNewsletterEmail::InsertDB($ArrayEmail);

    // add to campaign
    SplitTests::AddVariantForNewsletter($UserID, $CampaignNLID, $NewEmailID);

    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCamnpaign->GetData());
    $ObjectEmail = $SplittestObject->GetVariantEntity(1);
    $this->assertTrue(!empty($ObjectEmail), $message . ' email');
    $this->assertTrue($ObjectEmail->GetData('EmailID') == $NewEmailID, $message . ' email 2');

    /**
     * GetNextSplittestVariantForNewsletter
     */
    $message = 'GetNextSplittestVariantForNewsletter';

    $VariantEmailID = SplitTests::GetNextSplittestVariantForNewsletter($ObjectCamnpaign->GetData());
    $this->assertTrue($VariantEmailID == $EmailID, "$message $EmailID");
    $VariantEmailID = SplitTests::GetNextSplittestVariantForNewsletter($ObjectCamnpaign->GetData());
    $this->assertTrue($VariantEmailID == $NewEmailID, "$message $NewEmailID");
    $VariantEmailID = SplitTests::GetNextSplittestVariantForNewsletter($ObjectCamnpaign->GetData());
    $this->assertTrue($VariantEmailID == $EmailID, "$message $EmailID");

    /**
     * GetTaggingStatsForNewsletter
     **/
    $message = 'GetTaggingStatsForNewsletter';

    //** test by usage of FinishSplittest

    // simulate sending and conversion
    $SubscriberID1 = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => 0,
        'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
      ),
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => 0,
      'SendConfirmationEmail' => false,
      'TriggerAutoResponders' => true,
      'ReferenceID' => $ReferenceID,
      'EmailAddress' => '<EMAIL>',
    ))[1];
    kt_insert_row(array(
      'RelOwnerUserID' => $UserID,
      'RelSubscriberID' => $SubscriberID1,
      'TimeToSend' => time(),
      'RelAutoResponderID' => $CampaignNLID,
      'StatusEnum' => TransactionEmails::STATUS_SENT,
      'StatusReason' => 0,
      'RelEmailID' => $EmailID,
      'ConversionAmount' => 90,
    ), '{'.NewsletterQueue::TABLE_NAME.'}');
    $SubscriberID2 = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => 0,
        'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
      ),
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => 0,
      'SendConfirmationEmail' => false,
      'TriggerAutoResponders' => true,
      'ReferenceID' => $ReferenceID,
      'EmailAddress' => '<EMAIL>',
    ))[1];
    kt_insert_row(array(
      'RelOwnerUserID' => $UserID,
      'RelSubscriberID' => $SubscriberID2,
      'TimeToSend' => time(),
      'RelAutoResponderID' => $CampaignNLID,
      'StatusEnum' => TransactionEmails::STATUS_SENT,
      'StatusReason' => 0,
      'RelEmailID' => $NewEmailID,
      'ConversionAmount' => 89,
    ), '{'.NewsletterQueue::TABLE_NAME.'}');

    $Data = $SplittestObject->GetData();
    $Data['Variants'][0]['Sent'] = 1000;
    $Data['Variants'][1]['Sent'] = 1000;

    // opens in variant 1
    $Data['WinnerEnum'] = SplitTests::WINNER_OPENS;
    $SmartTagID = $ObjectCamnpaign->GetData('OpenedSmartTagID');
    $SubscriberID = $SubscriberID2;
    $Winner = 1;

    $SplittestObject->SetData($Data);
    Subscribers::TagSubscriber($UserID, $SubscriberID, $SmartTagID, $ReferenceID, FALSE);
    $SplittestObject->FinishSplittest();
    $this->assertTrue($SplittestObject->GetData('WinnerID') == $Winner, $message . ' WinnerID '.$Data['WinnerEnum']);
    Subscribers::UntagSubscriber($UserID, $SubscriberID, $ReferenceID, $SmartTagID);

    // clicks in variant 0
    $Data['WinnerEnum'] = SplitTests::WINNER_CLICKS;
    $SmartTagID = $ObjectCamnpaign->GetData('ClickedSmartTagID');
    $SubscriberID = $SubscriberID1;
    $Winner = 0;

    $SplittestObject->SetData($Data);
    Subscribers::TagSubscriber($UserID, $SubscriberID, $SmartTagID, $ReferenceID, FALSE);
    $SplittestObject->FinishSplittest();
    $this->assertTrue($SplittestObject->GetData('WinnerID') == $Winner, $message . ' WinnerID '.$Data['WinnerEnum']);
    Subscribers::UntagSubscriber($UserID, $SubscriberID, $ReferenceID, $SmartTagID);

    // conversions in variant 1
    $Data['WinnerEnum'] = SplitTests::WINNER_CONVERSION;
    $SmartTagID = $ObjectCamnpaign->GetData('ConvertedSmartTagID');
    $SubscriberID = $SubscriberID2;
    $Winner = 1;

    $SplittestObject->SetData($Data);
    Subscribers::TagSubscriber($UserID, $SubscriberID, $SmartTagID, $ReferenceID, FALSE);
    $SplittestObject->FinishSplittest();
    $this->assertTrue($SplittestObject->GetData('WinnerID') == $Winner, $message . ' WinnerID '.$Data['WinnerEnum']);
    Subscribers::UntagSubscriber($UserID, $SubscriberID, $ReferenceID, $SmartTagID);

    // amount in variant 0
    $Data['WinnerEnum'] = SplitTests::WINNER_CONVERSION;
    $SmartTagID = $ObjectCamnpaign->GetData('ConvertedSmartTagID');
    $Winner = 0;

    $SplittestObject->SetData($Data);
    Subscribers::TagSubscriber($UserID, $SubscriberID1, $SmartTagID, $ReferenceID, FALSE);
    Subscribers::TagSubscriber($UserID, $SubscriberID2, $SmartTagID, $ReferenceID, FALSE);
    $SplittestObject->FinishSplittest();
    $this->assertTrue($SplittestObject->GetData('WinnerID') == $Winner, $message . ' WinnerID '.$Data['WinnerEnum']);

    //** test directly

    $Stats = $SplittestObject->GetTaggingStatsForNewsletter(0);
    $this->assertTrue($Stats['opened'] == 0, $message . ' stats 0 opened'.print_r($Stats,1));
    $this->assertTrue($Stats['clicked'] == 0, $message . ' stats 0 clicked');
    $this->assertTrue($Stats['converted'] == 1, $message . ' stats 0 converted');
    $this->assertTrue($Stats['amount'] == 90, $message . ' stats 0 amount');

    $Stats = $SplittestObject->GetTaggingStatsForNewsletter(1);
    $this->assertTrue($Stats['opened'] == 0, $message . ' stats 1 opened'.print_r($Stats,1));
    $this->assertTrue($Stats['clicked'] == 0, $message . ' stats 1 clicked');
    $this->assertTrue($Stats['converted'] == 1, $message . ' stats 1 converted');
    $this->assertTrue($Stats['amount'] == 89, $message . ' stats 1 amount');

    $SplittestObject->UpdateDB($Data);

    /**
     * RemoveVariantForNewsletter
     */
    $message = 'RemoveVariantForNewsletter';

    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCamnpaign->GetData());
    $ObjectEmail = $SplittestObject->GetVariantEntity(0);
    $this->assertTrue(!empty($ObjectEmail), $message . ' email');
    $this->assertTrue($ObjectEmail->GetData('EmailID') == $EmailID, $message . " email in variant 0 - before = $EmailID");

    $result = SplitTests::RemoveVariantForNewsletter($UserID, $CampaignNLID, $EmailID);
    $this->assertTrue($result, $message . ' result');

    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCamnpaign->GetData());
    $ObjectEmail = $SplittestObject->GetVariantEntity(0);
    $this->assertTrue(!empty($ObjectEmail), $message . ' email');
    $this->assertTrue($ObjectEmail->GetData('EmailID') == $NewEmailID, $message . " email in variant 0 - after = $NewEmailID");

    /**
     * RemoveSplittests
     */
    $message = 'RemoveSplittests';

    SplitTests::RemoveSplittests($ObjectCamnpaign->GetData());

    // splittest deleted
    $SplittestObject = CampaignsNewsletter::GetSplittestObject($ObjectCamnpaign->GetData());
    $this->assertTrue(empty($SplittestObject), $message . ' splittest');

    /**
     * Automation splittest
     */
    $message = 'Automation splittest';

    // create campaign
    $ArrayFieldAndValues = array(
      'CampaignName' => $message,
      'RelOwnerUserID' => $UserID,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_PROCESSFLOW,
    );
    $CampaignID = CampaignsProcessFlow::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($CampaignID > 0, $message . ' campaign id');
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $ArrayCampaign = $ObjectCamnpaign->GetData();

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_NO_ACTION_FOUND, print_r($ArrayCampaign['ProcessFlow'],1));

    $ArrayCampaign['ProcessFlow']['states'][1] = array(
        'id' => 2,
        'name' => '',
        'type' => CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_SPLITTEST,
        'next' => 0,
        'nextNo' => 0,
    );
    $ArrayCampaign['ProcessFlow']['states'][0]['next'] = 2;
    $ObjectCamnpaign->UpdateDB($ArrayCampaign);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_SPLITTEST, print_r($ArrayCampaign['ProcessFlow'],1));

    $SplittestID = SplitTests::InsertDB(array(
      'RelOwnerUserID' => $UserID,
      'RelCampaignID' => $CampaignID,
      'TestDuration' => 0,
    ));
    $this->assertTrue($SplittestID > 0, $message . ' splittest id');
    $ArrayCampaign['ProcessFlow']['states'][1]['splittestID'] = $SplittestID;
    $ObjectCamnpaign->UpdateDB($ArrayCampaign);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_INVALID_TESTDURATION, print_r($ArrayCampaign['ProcessFlow'],1));

    $ObjectSplittest = SplitTests::FromID($UserID, $SplittestID);
    $ArraySplittest = $ObjectSplittest->GetData();
    $ArraySplittest['TestDuration'] = 3600;
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANTS_MISSING, print_r($ArraySplittest,1));

    $ArraySplittest['Variants'] = array(array(), array());
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_NO_VARIANT_ENTITY, print_r($ArraySplittest,1));

    // start automation variant

    $ArraySplittest['Variants'][0] = array(
      'EntityType' => SplitTests::getEntityTypeOfClass(Campaigns::class),
      'EntityID' => $CampaignID,
      'Weight' => 0,
      'TagID' => -1, // Tagging to count
    );
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_AUTOMATION_CANT_START_ITSELF, print_r($ArraySplittest,1));

    $ArrayFieldAndValues = array(
      'CampaignName' => $message,
      'RelOwnerUserID' => $UserID,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_SUBSCRIPTION,
    );
    $AutomationToStartID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $ArraySplittest['Variants'][0]['EntityID'] = $AutomationToStartID;
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_NO_VARIANT_ENTITY, print_r($ArraySplittest,1));

    $ArrayFieldAndValues = array(
      'CampaignName' => $message,
      'RelOwnerUserID' => $UserID,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_PROCESSFLOW,
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
      'ProcessFlow' => array(
        'start' => 1,
        'states' => array(
          array(
            'id' => 1,
            'name' => '',
            'type' => CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_START,
            'next' => 2,
            'nextNo' => 0,
          ),
          array(
            'id' => 2,
            'name' => '',
            'type' => CampaignsProcessFlow::PROCESSFLOW_STATE_TYPE_WAIT,
            'delayType' => CampaignsProcessFlow::PROCESSFLOW_WAIT_TYPE_IMMEDIATELY,
            'next' => 0,
            'nextNo' => 0,
          ),
        ),
      ),
    );
    $AutomationToStartID = CampaignsProcessFlow::InsertDB($ArrayFieldAndValues);
    $result = Campaigns::ProcessCampaign($UserID, $AutomationToStartID);
    $this->assertTrue($result, $message . ' started campaign created');
    $ArraySplittest['Variants'][0]['EntityType'] = SplitTests::getEntityTypeOfClass(CampaignsProcessFlow::class);
    $ArraySplittest['Variants'][0]['EntityID'] = $AutomationToStartID;
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_INVALID_WEIGHT, print_r($ArraySplittest,1));

    $ArraySplittest['Variants'][0]['Weight'] = 20;
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_INVALID_TAG, print_r($ArraySplittest,1));

    $AutomationToStartObject = Campaigns::FromID($UserID, $AutomationToStartID);
    $ArraySplittest['Variants'][0]['TagID'] = $AutomationToStartObject->GetData('AutomationStartedSmartTagID');
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_NO_VARIANT_ENTITY, print_r($ArraySplittest,1));

    // send automation email variant

    $ArrayFieldAndValues = array(
      'EmailName' => $message,
      'RelUserID' => $UserID,
    );
    $EmailToSendID = EmailsAutomationEmail::InsertDB($ArrayFieldAndValues);
    $EmailToSendObject = Emails::FromID($UserID, $EmailToSendID);

    $ArraySplittest['Variants'][1] = array(
      'EntityType' => SplitTests::getEntityTypeOfClass(EmailsAutomationEmail::class),
      'EntityID' => $EmailToSendID,
      'Weight' => 20,
      'TagID' => $EmailToSendObject->GetData('EmailSentSmartTagID'),
    );
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_SUBJECT, print_r($ArraySplittest,1));

    $ArrayEmail = $EmailToSendObject->GetData();
    $ArrayEmail['Subject'] = $message;
    $EmailToSendObject->UpdateDB($ArrayEmail);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_EMAIL_NO_CONTENT, print_r($ArraySplittest,1));

    $ArrayEmail['PlainContent'] = $message;
    $ArrayEmail['HTMLContent'] = $message;
    $EmailToSendObject->UpdateDB($ArrayEmail);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_WEIGHTS_NOT_100, print_r($ArraySplittest,1));

    // send automation sms variant

    $ArrayFieldAndValues = array(
      'EmailName' => $message,
      'RelUserID' => $UserID,
    );
    $SMSToSendID = EmailsAutomationSMS::InsertDB($ArrayFieldAndValues);
    $SMSToSendObject = Emails::FromID($UserID, $SMSToSendID);

    $ArraySplittest['Variants'][2] = array(
      'EntityType' => SplitTests::getEntityTypeOfClass(EmailsAutomationSMS::class),
      'EntityID' => $SMSToSendID,
      'Weight' => 20,
      'TagID' => $SMSToSendObject->GetData('SMSSentSmartTagID'),
    );
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SMS_NO_CONTENT, print_r($ArraySplittest,1));

    $ArrayEmail = $SMSToSendObject->GetData();
    $ArrayEmail['PlainContent'] = $message;
    $SMSToSendObject->UpdateDB($ArrayEmail);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_WEIGHTS_NOT_100, print_r($ArraySplittest,1));

    // activate outbound variant

    $ArrayFieldAndValues = array(
      'Name' => $message,
      'RelOwnerUserID' => $UserID,
    );
    $OutboundID = ToolOutboundGeneral::InsertDB($ArrayFieldAndValues);
    $OutboundObject = MarketingTools::FromID($UserID, $OutboundID);

    $ArraySplittest['Variants'][3] = array(
      'EntityType' => SplitTests::getEntityTypeOfClass(ToolOutboundGeneral::class),
      'EntityID' => $OutboundID,
      'Weight' => 20,
      'TagID' => $OutboundObject->GetData('ActivationSmartTagID'),
    );
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_INVALID_ACTIVATION_URL, print_r($ArraySplittest,1));

    $ArrayOutbound = $OutboundObject->GetData();
    $ArrayOutbound['RequestMethod'] = 'POST';
    $ArrayOutbound['ActivationURL'] = url($HttptestURL,
      array('query' => array('secret' => KLICKTIPP_HTTPTEST_SECRET, 'myparam' => '__email__')));
    $OutboundObject->UpdateDB($ArrayOutbound);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_WEIGHTS_NOT_100, print_r($ArraySplittest,1));

    // tagging variant

    $ArrayFieldAndValues = array(
      'TagName' => $message,
      'RelOwnerUserID' => $UserID,
    );
    $TaggingID = Tag::InsertDB($ArrayFieldAndValues);
    $TaggingObject = Tag::FromID($UserID, $TaggingID);

    $ArraySplittest['Variants'][4] = array(
      'EntityType' => SplitTests::getEntityTypeOfClass(Tag::class),
      'EntityID' => $TaggingID,
      'Weight' => 10,
      'TagID' => $TaggingID,
    );
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, CampaignsProcessFlow::PROCESSFLOW_VALIDATION_ERROR_ACTION_SPLITTEST_VARIANT_WEIGHTS_NOT_100, print_r($ArraySplittest,1));

    $ArraySplittest['Variants'][4]['Weight'] = 20;
    $ObjectSplittest->UpdateDB($ArraySplittest);

    $this->checkValidationError($ArrayCampaign, '', print_r($ArraySplittest,1));

    // process this splittest

    // start campaign and create queue jobs (same as klicktipp_automation_senddate_form_submit)
    $result = Campaigns::UpdateCampaign($UserID, $CampaignID, array(
      'CampaignStatusEnum' => Campaigns::STATUS_READY,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    ));
    $this->assertTrue($result, $message . ' start');
    $result = Campaigns::ProcessCampaign($UserID, $CampaignID);
    $ObjectCamnpaign = CampaignsProcessFlow::FromID($UserID, $CampaignID);
    $this->assertTrue($result, $message . ' process'.print_r($ObjectCamnpaign,1));
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $this->assertTrue($SplittestObject->GetData('started') > 0, $message . ' started'.print_r($SplittestObject,1));

    // create and process subscriber 1
    $message = 'subscriber 1';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => 0,
        'RelOwnerUserID' => $UserID,
      ),
      'EmailAddress' => '<EMAIL>',
      'PhoneNumber' => '00491234567891',
      'IPAddress' => '127.0.0.1',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE, // register into queue
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID1 = $Result[1];

    TransactionEmails::StartAutomation($UserID, $SubscriberID1, $ReferenceID, $CampaignID, TRUE);
    $this->processSubscriber($SubscriberID1, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PAUSED_AFTER, $AutomationToStartObject->GetData('AutomationStartedSmartTagID'));

    $variant = 0;
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 1, "$message count conversions variant $variant");

    // create and process subscriber 2
    $message = 'subscriber 2';
    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '00491234567892';
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID2 = $Result[1];

    // start automation
    TransactionEmails::StartAutomation($UserID, $SubscriberID2, $ReferenceID, $CampaignID, TRUE);
    $this->processSubscriber($SubscriberID2, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PAUSED_AFTER, $EmailToSendObject->GetData('EmailSentSmartTagID'));

    $variant = 1;
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 1, "$message count conversions variant $variant");

    // create and process subscriber 3
    $message = 'subscriber 3';
    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '00491234567893';
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID3 = $Result[1];

    // start automation
    TransactionEmails::StartAutomation($UserID, $SubscriberID3, $ReferenceID, $CampaignID, TRUE);
    $this->processSubscriber($SubscriberID3, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PAUSED_AFTER, $SMSToSendObject->GetData('SMSSentSmartTagID'));

    $variant = 2;
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 1, "$message count conversions variant $variant");

    // create and process subscriber 4
    $message = 'subscriber 4';
    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '00491234567894';
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID4 = $Result[1];

    // start automation
    TransactionEmails::StartAutomation($UserID, $SubscriberID4, $ReferenceID, $CampaignID, TRUE);
    $this->processSubscriber($SubscriberID4, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PAUSED_AFTER, $OutboundObject->GetData('ActivationSmartTagID'));

    $variant = 3;
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 1, "$message count conversions variant $variant");

    // create and process subscriber 5
    $message = 'subscriber 5';
    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '00491234567895';
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID5 = $Result[1];

    // start automation
    TransactionEmails::StartAutomation($UserID, $SubscriberID5, $ReferenceID, $CampaignID, TRUE);
    $this->processSubscriber($SubscriberID5, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PAUSED_AFTER, $TaggingID);

    $variant = 4;
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 1, "$message count conversions variant $variant");

    // create and process subscriber 6
    $message = 'subscriber 6';
    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '00491234567896';
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID6 = $Result[1];

    // start automation
    TransactionEmails::StartAutomation($UserID, $SubscriberID6, $ReferenceID, $CampaignID, TRUE);
    $this->processSubscriber($SubscriberID6, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PAUSED_AFTER, $AutomationToStartObject->GetData('AutomationStartedSmartTagID'));

    $variant = 0;
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 2, "$message count conversions variant $variant");

    // make the last variant a winner
    Subscribers::TagSubscriber($UserID, $SubscriberID1, $TaggingID, $ReferenceID);
    Subscribers::TagSubscriber($UserID, $SubscriberID2, $TaggingID, $ReferenceID);
    Subscribers::TagSubscriber($UserID, $SubscriberID3, $TaggingID, $ReferenceID);

    $variant = 4;
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $this->assertTrue(SplitTests::GetVariantTaggingCount($SplittestObject->GetData(), $variant) == 4, "$message count conversions variant $variant");

    // terminate splittest
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $Data = $SplittestObject->GetData();
    $Data['started'] -= 10000;
    $SplittestObject->UpdateDB($Data);
    _klicktipp_test_process_winner();

    // check results
    $SplittestObject = SplitTests::FromID($UserID, $SplittestID);
    $Data = $SplittestObject->GetData();
    $this->assertTrue($Data['finished'] >= $Data['started'], $message . ' finished'.print_r($SplittestObject,1));
    $this->assertTrue($Data['WinnerID'] == 4, $message . ' WinnerID');
    $Variants = $SplittestObject->GetData('Variants');
    $this->assertTrue($Variants[0]['Count'] == 2, $message . ' Count 0');
    $this->assertTrue($Variants[1]['Count'] == 1, $message . ' Count 1');
    $this->assertTrue($Variants[2]['Count'] == 1, $message . ' Count 2');
    $this->assertTrue($Variants[3]['Count'] == 1, $message . ' Count 3');
    $this->assertTrue($Variants[4]['Count'] == 4, $message . ' Count 4');

    // create and process subscriber 7 -> should get winner
    $message = 'subscriber 7';
    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '00491234567897';
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID7 = $Result[1];

    // start automation
    TransactionEmails::StartAutomation($UserID, $SubscriberID7, $ReferenceID, $CampaignID, TRUE);
    $this->processSubscriber($SubscriberID7, $UserID, $CampaignID, 2, TransactionEmails::STATUS_PENDING, 2, TransactionEmails::STATUS_PAUSED_AFTER, $TaggingID);
  }

  function checkValidationError($ArrayCampaign, $expected_error, $message) {
    $TodoList = CampaignsProcessFlow::ValidateAutomation($ArrayCampaign);
    if (empty($expected_error)) {
      $this->assertTrue(empty($TodoList), 'got '.$TodoList[0][1].' expected: none # '.$message);
    }
    else {
      $this->assertTrue($TodoList[0][1] == $expected_error, 'got '.$TodoList[0][1].' expected:'.$expected_error.' # '.$message);
    }
  }

  function processSubscriber($SubscriberID, $UserID, $CampaignID, $StateBefore, $ExpectedBefore, $StateAfter, $ExpectedAfter, $ExpectedTagging) {
    $ReferenceID = 0;

    // check queue entry before
    $message = 'check queue entry before';
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email([
      'RelSubscriberID' => $SubscriberID,
      'RelAutoResponderID' => $CampaignID,
      'RelOwnerUserID' => $UserID,
    ], TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] == $ExpectedBefore, $message . ' status before');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == $StateBefore, $message . ' state before');
    if ($ArrayTransactionalEmail['StatusEnum'] != $ExpectedBefore || $ArrayTransactionalEmail['StateID'] != $StateBefore) {
      $this->assertTrue(0, print_r($ArrayTransactionalEmail, 1));
    }

    $data = ['remainder' => 0, 'divisor' => 0];
    TransactionalQueue::Send($data);
    drupal_queue_cron_run();

    // check queue entry before
    $message = 'check queue entry after';
    $ArrayTransactionalEmail = simpletest_transactional_retrieve_email(array(
      'RelSubscriberID' => $SubscriberID,
      'RelAutoResponderID' => $CampaignID,
      'RelOwnerUserID' => $UserID,
    ), TransactionalQueue::TABLE_NAME);
    $this->assertTrue($ArrayTransactionalEmail['StatusEnum'] == $ExpectedAfter, $message . ' status after');
    $this->assertTrue($ArrayTransactionalEmail['StateID'] == $StateAfter, $message . ' state after');
    if ($ArrayTransactionalEmail['StatusEnum'] != $ExpectedAfter || $ArrayTransactionalEmail['StateID'] != $StateAfter) {
      $this->assertTrue(0, print_r($ArrayTransactionalEmail, 1));
    }

    $Taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID, TRUE);
    $this->assertTrue(in_array($ExpectedTagging, $Taggings), $message . ' tagging expected='.$ExpectedTagging.print_r($Taggings,1));

  }

}

<?php

use App\Klicktipp\DigiStore;
use App\Klicktipp\Libraries;

class cliAmemberDigistoreTestCase extends DrupalWebTestCase {

  public static function getInfo() {
    return array(
      'name' => 'cli Amember digistore',
      'description' => 'Test crm/plugins/payment/digistore/ipn.php',
      'group' => 'Klicktipp',
    );

  }

  function testAmemberDigistore() {

    ////////////////////////
    /// THIS TEST IS BROKEN
    /// It calls the amember server on local dev, which is no longer a part of the klicktipp app.
    /// As soon as we have amember in a docker container, we may bring this to work again.
    ////////////////////////
    return;

    Libraries::include('api.inc', '/tests/includes');
    Libraries::include('amember.inc', '/tests/includes');

    $message = 'prepare';

    // get useragent
    $useragent = useragent($this->databasePrefix);
    $this->assertTrue(!empty($useragent), $message . ' databasePrefix');
    if (!$useragent) {
      // dont proceed without proper user agent
      return;
    }

    $marketing = amember_test_prepare_data($this, $message);
    $this->assertTrue(!empty($marketing), "$message marketing" . print_r($marketing, 1));

    $paysys = 'digistore';

    /*
     * connection_test
     */
    $message = 'connection_test';

    $values = array(
      'event' => 'connection_test',
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    /*
     * on_affiliation
     */
    $message = 'on_affiliation';

    $name = 'aff'.time();
    $email = $name . '@zauberlist.com';

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => 137,
      ))->fetchAssoc();;
    $this->assertTrue(!empty($product), "$message product" . print_r($product, 1));
    $product['data'] = unserialize($product['data']);

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_AFFILIATION,
      'affiliate_name' => $name,
      'email' => $email,
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check member

    $member = db_query("SELECT * from {amember_members} WHERE email = :email",
      array(
        ':email' => $email,
      ))->fetchAssoc();
    amember_test_push_error_log($this, !empty($member), "$message member" . print_r($member, 1));
    $this->assertTrue($member['name_l'] == 'Mustermann', "$message member lastname");
    $this->assertTrue($member['state'] == 'DE-BY', "$message member state");
    $name = $member['login'];

    // check user

    $account = amember_test_check_user($this, $message, $name, $email, $product['data']['klicktipp_access'], 1, FALSE, FALSE);

    // check subscriber

    amember_test_check_subscriber($this, $message, $marketing, $email, $member, TRUE, FALSE, FALSE, FALSE, $member['digistore_affiliate_name']);

    /*
     * buy klicktipp basic
     */

    $message = 'on_payment basic';

    $email = 'basic' . time() . '@zauberlist.com';
    $subscription_id = 'SA' . substr(time(), -6);
    $purchase_id = 'PAYID-1-' . substr(time(), -7);
    $pid = 101;

    $pid_basic = $pid;

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => $pid,
      ))->fetchAssoc();;
    $this->assertTrue(!empty($product), "$message product");
    $product['data'] = unserialize($product['data']);

    $digistore_id = $product['data']['digistore_id'];
    $this->assertTrue(!empty($digistore_id), "$message digistore_id" . print_r($product, 1));

    $basic_groupid = $product['data']['klicktipp_access'];

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_PAYMENT,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+3 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check member

    $member = db_query("SELECT * from {amember_members} WHERE email = :email",
      array(
        ':email' => $email,
      ))->fetchAssoc();
    amember_test_push_error_log($this, !empty($member), "$message member" . print_r($member, 1));
    $name = $member['login'];

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-%",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 1, "$message payment completed");
    $this->assertTrue(strtotime($payment['expire_date']) > time(), "$message payment expire_date");
    $this->assertTrue(strtotime($payment['expire_date']) > strtotime('+1 days', strtotime($payment['begin_date'])), "$message payment begin_date");

    $basicexpires = strtotime($payment["expire_date"]);
    $receipt_basic1 = $payment["receipt_id"];

    // check user

    $account = amember_test_check_user($this, $message, $name, $email, $product['data']['klicktipp_access'], 1, TRUE, FALSE);

    // check user has product and access until expire date
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(strtotime($payment['expire_date']) == $access_timestamp, "$message payment access timestamp");

    // check subscriber

    amember_test_check_subscriber($this, $message, $marketing, $email, $member, FALSE, TRUE, FALSE, FALSE, FALSE);

    /*
     * buy klicktipp basic
     */

    $message = 'on_payment basicX2';

    $purchase_id = 'PAYID-2-' . substr(time(), -7);

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_PAYMENT,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+5 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-%",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 1, "$message payment completed");
    $this->assertTrue(strtotime($payment['expire_date']) > $basicexpires, "$message payment expire_date");

    $receipt_basic2 = $payment["receipt_id"];

    // check user

    $account = amember_test_check_user($this, $message, $name, $email, $product['data']['klicktipp_access'], 1, TRUE, FALSE);

    // check user has product and access until expire date
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(strtotime($payment['expire_date']) == $access_timestamp, "$message payment access timestamp");

    // check subscriber

    amember_test_check_subscriber($this, $message, $marketing, $email, $member, FALSE, TRUE, FALSE, FALSE, FALSE);

    /*
     * buy klicktipp advanced
     */

    $message = 'on_payment advanced';

    $subscription_id = 'SB' . substr(time(), -6);
    $purchase_id = 'PAYID-3-' . substr(time(), -7);
    $pid = 109;

    $pid_advanced = $pid;

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => $pid,
      ))->fetchAssoc();;
    $this->assertTrue(!empty($product), "$message product");
    $product['data'] = unserialize($product['data']);

    $digistore_id = $product['data']['digistore_id'];
    $this->assertTrue(!empty($digistore_id), "$message digistore_id" . print_r($product, 1));

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_PAYMENT,
      'email' => $email,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+35 days')),
      // in basket
      'item_count' => 3,
      'product_id' => 999999,
      'product_id_2' => $digistore_id,
      'product_id_3' => 999998,
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-%",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 1, "$message payment completed");
    $this->assertTrue(strtotime($payment['expire_date']) > time(), "$message payment expire_date");
    $this->assertTrue(strtotime($payment['expire_date']) > strtotime('+1 days', strtotime($payment['begin_date'])), "$message payment begin_date");

    $receipt_ultimate = $payment["receipt_id"];

    // check user

    $account = amember_test_check_user($this, $message, $name, $email, $product['data']['klicktipp_access'], 1, TRUE, TRUE);

    // check user has product and access until expire date
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(strtotime($payment['expire_date']) == $access_timestamp, "$message payment access timestamp");

    // check subscriber

    amember_test_check_subscriber($this, $message, $marketing, $email, $member, FALSE, TRUE, FALSE, FALSE, FALSE);

    /*
     * chargeback ultimate
     */

    $message = 'on_chargeback advanced';

    [$subscription_id, $purchase_id] = explode('-', $receipt_ultimate, 2);

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_CHARGEBACK,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+35 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-$purchase_id",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 0, "$message payment completed");

    // check user

    $account = amember_test_check_user($this, $message, $name, $email, $basic_groupid, 1, TRUE, FALSE);

    // check user has no valid access as we performed a chargeback
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(empty($access_timestamp), "$message payment no valid access");

    // check subscriber

    amember_test_check_subscriber($this, $message, $marketing, $email, $member, FALSE, TRUE, FALSE, FALSE, FALSE);

    /*
     * chargeback basic 1
     */

    $message = 'on_chargeback basic 1';

    [$subscription_id, $purchase_id] = explode('-', $receipt_basic1, 2);

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_CHARGEBACK,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+35 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-$purchase_id",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid_basic, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 0, "$message payment completed");

    /*
     * chargeback basic 2
     */

    $message = 'on_chargeback basic 2';

    [$subscription_id, $purchase_id] = explode('-', $receipt_basic2, 2);

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_CHARGEBACK,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+35 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-$purchase_id",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid_basic, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 0, "$message payment completed");

    // check user

    // no more subscriptions: still has 'klicktipp access", but account is disabled
    $account = amember_test_check_user($this, $message, $name, $email, $basic_groupid, 0, TRUE, FALSE);

    // check user has no valid access as we performed a chargeback
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(empty($access_timestamp), "$message payment no valid access");

    // check subscriber

    amember_test_check_subscriber($this, $message, $marketing, $email, $member, FALSE, FALSE, TRUE, FALSE, FALSE);

    /*
     * affiliation after unsubscribe
     */
    $message = 'on_affiliation former customer';

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => 137,
      ))->fetchAssoc();;
    $this->assertTrue(!empty($product), "$message product" . print_r($product, 1));
    $product['data'] = unserialize($product['data']);

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_AFFILIATION,
      'affiliate_name' => $name,
      'email' => $email,
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check member

    $member = db_query("SELECT * from {amember_members} WHERE email = :email",
      array(
        ':email' => $email,
      ))->fetchAssoc();
    amember_test_push_error_log($this, !empty($member), "$message member" . print_r($member, 1));

    // check user

    $account = amember_test_check_user($this, $message, $name, $email, $product['data']['klicktipp_access'], 1, FALSE, FALSE);

    // check subscriber

    amember_test_check_subscriber($this, $message, $marketing, $email, $member, TRUE, FALSE, TRUE, FALSE, $member['digistore_affiliate_name']);

    /*
     * buy klicktipp advanced
     */

    $message = 'on_payment advanced';

    $subscription_id = 'SC' . substr(time(), -6);
    $purchase_id = 'PAYID-4-' . substr(time(), -7);

    $pid = 109;

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => $pid,
      ))->fetchAssoc();;
    $this->assertTrue(!empty($product), "$message product");
    $product['data'] = unserialize($product['data']);

    $digistore_id = $product['data']['digistore_id'];
    $this->assertTrue(!empty($digistore_id), "$message digistore_id" . print_r($product, 1));

    $advanced_groupid = $product['data']['klicktipp_access'];

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_PAYMENT,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+35 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-%",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 1, "$message payment completed");
    $this->assertTrue(strtotime($payment['expire_date']) > time(), "$message payment expire_date");
    $this->assertTrue(strtotime($payment['expire_date']) > strtotime('+1 days', strtotime($payment['begin_date'])), "$message payment begin_date");

    // check user

    $account = amember_test_check_user($this, $message, $name, $email, $advanced_groupid, 1, TRUE, TRUE);

    // check user has product and access until expire date
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(strtotime($payment['expire_date']) == $access_timestamp, "$message payment access timestamp");

    // check subscriber

    amember_test_check_subscriber($this, $message, $marketing, $email, $member, TRUE, TRUE, FALSE, FALSE, $member['digistore_affiliate_name']);

    // save the current roles
    $user_roles = $account->roles;

    /*
     * buy klicktipp consultant seminar
     */

    $message = 'on_payment consultant';

    $subscription_id = 'SD' . substr(time(), -6);
    $purchase_id = 'PAYID-5-' . substr(time(), -7);

    $pid = 147;
    $consultant_pids = variable_get('amember_consultant_productids', '');
    if (!empty($consultant_pids)) {
      $consultant_pids = explode(",", $consultant_pids);
      $pid = $consultant_pids[0];
    }

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => $pid,
      ))->fetchAssoc();
    $this->assertTrue(!empty($product), "$message product");
    $product['data'] = unserialize($product['data']);

    $digistore_id = $product['data']['digistore_id'];
    $this->assertTrue(!empty($digistore_id), "$message digistore_id" . print_r($product, 1));

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_PAYMENT,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+35 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-%",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 1, "$message payment completed");
    $this->assertTrue(strtotime($payment['expire_date']) > time(), "$message payment expire_date");
    $this->assertTrue(strtotime($payment['expire_date']) > strtotime('+1 days', strtotime($payment['begin_date'])), "$message payment begin_date");

    // check user still has premium access
    $account = amember_test_check_user($this, $message, $name, $email, $advanced_groupid, 1, TRUE, TRUE);

    // check user still has all the previous rules
    foreach ($user_roles as $rid => $rname) {
      $this->assertTrue(user_has_role($rid, $account), "$message $rname role remained");
    }

    // check user has product and access until expire date
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(strtotime($payment['expire_date']) == $access_timestamp, "$message payment access timestamp");

    // get roleID of consultant role
    $consultant_rid = variable_get('klicktipp_consultant_roleid', 0);
    // check consultant role has been assigned
    $this->assertTrue(user_has_role($consultant_rid, $account), "$message consultant role assigned");

    /*
     * buy klicktipp convention seminar
     */

    $message = 'on_payment convention';

    $subscription_id = 'SE' . substr(time(), -6);
    $purchase_id = 'PAYID-6-' . substr(time(), -7);

    $pid = 151;
    $convention_pids = variable_get('amember_convention_productids', '');
    if (!empty($convention_pids)) {
      $convention_pids = explode(",", $convention_pids);
      $pid = $convention_pids[1];
    }

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => $pid,
      ))->fetchAssoc();
    $this->assertTrue(!empty($product), "$message product");
    $product['data'] = unserialize($product['data']);

    $digistore_id = $product['data']['digistore_id'];
    $this->assertTrue(!empty($digistore_id), "$message digistore_id" . print_r($product, 1));

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_PAYMENT,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+35 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-%",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 1, "$message payment completed");
    $this->assertTrue(strtotime($payment['expire_date']) > time(), "$message payment expire_date");
    $this->assertTrue(strtotime($payment['expire_date']) > strtotime('+1 days', strtotime($payment['begin_date'])), "$message payment begin_date");

    // check user still has premium access
    $account = amember_test_check_user($this, $message, $name, $email, $advanced_groupid, 1, TRUE, TRUE);

    // check user still has all the previous rules
    foreach ($user_roles as $rid => $rname) {
      $this->assertTrue(user_has_role($rid, $account), "$message $rname role remained");
    }

    // check user has product and access until expire date
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(strtotime($payment['expire_date']) == $access_timestamp, "$message payment access timestamp");

    // get roleID of convention role
    $convention_rid = variable_get('klicktipp_convention_roleid', 0);
    // check convention role has been assigned
    $this->assertTrue(user_has_role($convention_rid, $account), "$message convention role assigned");

    /*
     * buy klicktipp consultant seminar as initial product
     */

    $message = 'on_payment initial consultant';

    $name = 'consultant'.time();
    $email = $name . '@zauberlist.com';

    $subscription_id = 'SF' . substr(time(), -6);
    $purchase_id = 'PAYID-7-' . substr(time(), -7);

    $pid = 147;
    $consultant_pids = variable_get('amember_consultant_productids', '');
    if (!empty($consultant_pids)) {
      $consultant_pids = explode(",", $consultant_pids);
      $pid = $consultant_pids[0];
    }

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => $pid,
      ))->fetchAssoc();
    $this->assertTrue(!empty($product), "$message product");
    $product['data'] = unserialize($product['data']);

    $digistore_id = $product['data']['digistore_id'];
    $this->assertTrue(!empty($digistore_id), "$message digistore_id" . print_r($product, 1));

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_PAYMENT,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+35 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check member

    $member = db_query("SELECT * from {amember_members} WHERE email = :email",
      array(
        ':email' => $email,
      ))->fetchAssoc();
    amember_test_push_error_log($this, !empty($member), "$message member" . print_r($member, 1));
    $this->assertTrue($member['name_l'] == 'Mustermann', "$message member lastname");
    $this->assertTrue($member['state'] == 'DE-BY', "$message member state");
    $name = $member['login'];

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-%",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 1, "$message payment completed");
    $this->assertTrue(strtotime($payment['expire_date']) > time(), "$message payment expire_date");
    $this->assertTrue(strtotime($payment['expire_date']) > strtotime('+1 days', strtotime($payment['begin_date'])), "$message payment begin_date");

    // check user is active, has no klicktipp access and belongs to affiliate group
    $account = amember_test_check_user($this, $message, $name, $email, $product['data']['klicktipp_access'], 1, FALSE, FALSE);

    // check user has product and access until expire date
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(strtotime($payment['expire_date']) == $access_timestamp, "$message payment access timestamp");

    // get roleID of consultant role
    $consultant_rid = variable_get('klicktipp_consultant_roleid', 0);
    // check consultant role has been assigned
    $this->assertTrue(user_has_role($consultant_rid, $account), "$message consultant role assigned");

    /*
     * buy klicktipp convention seminar as initial product
     */

    $message = 'on_payment initial convention';

    $name = 'convention'.time();
    $email = $name . '@zauberlist.com';

    $subscription_id = 'SG' . substr(time(), -6);
    $purchase_id = 'PAYID-8-' . substr(time(), -7);

    $pid = 146;
    $convention_pids = variable_get('amember_convention_productids', '');
    if (!empty($convention_pids)) {
      $convention_pids = explode(",", $convention_pids);
      $pid = $convention_pids[0];
    }

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => $pid,
      ))->fetchAssoc();
    $this->assertTrue(!empty($product), "$message product");
    $product['data'] = unserialize($product['data']);

    $digistore_id = $product['data']['digistore_id'];
    $this->assertTrue(!empty($digistore_id), "$message digistore_id" . print_r($product, 1));

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_PAYMENT,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      "next_payment_at" => date('Y-m-d', strtotime('+35 days')),
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check member

    $member = db_query("SELECT * from {amember_members} WHERE email = :email",
      array(
        ':email' => $email,
      ))->fetchAssoc();
    amember_test_push_error_log($this, !empty($member), "$message member" . print_r($member, 1));
    $this->assertTrue($member['name_l'] == 'Mustermann', "$message member lastname");
    $this->assertTrue($member['state'] == 'DE-BY', "$message member state");
    $name = $member['login'];

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-%",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 1, "$message payment completed");
    $this->assertTrue(strtotime($payment['expire_date']) > time(), "$message payment expire_date");
    $this->assertTrue(strtotime($payment['expire_date']) > strtotime('+1 days', strtotime($payment['begin_date'])), "$message payment begin_date");

    // check user is active, has klicktipp access and belongs to enterprise group
    $account = amember_test_check_user($this, $message, $name, $email, $product['data']['klicktipp_access'], 1, TRUE, TRUE);

    // check user has product and access until expire date
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(strtotime($payment['expire_date']) == $access_timestamp, "$message payment access timestamp");

    // get roleID of convention role
    $convention_rid = variable_get('klicktipp_convention_roleid', 0);
    // check convention role has been assigned
    $this->assertTrue(user_has_role($convention_rid, $account), "$message convention role assigned");

    /*
     * buy klicktipp mentorship
     */

    $message = 'on_payment mentorship';

    $name = 'mentorship'.time();
    $email = $name . '@zauberlist.com';

    $subscription_id = 'SH' . substr(time(), -6);
    $purchase_id = 'PAYID-9-' . substr(time(), -7);

    $pid = 152;

    $product = db_query("SELECT * from {amember_products} WHERE product_id = :product_id",
      array(
        ':product_id' => $pid,
      ))->fetchAssoc();
    $this->assertTrue(!empty($product), "$message product");
    $product['data'] = unserialize($product['data']);

    $digistore_id = $product['data']['digistore_id'];
    $this->assertTrue(!empty($digistore_id), "$message digistore_id" . print_r($product, 1));

    // ipn call

    $values = array(
      'event' => DigiStore::EVENT_PAYMENT,
      'email' => $email,
      'product_id' => $digistore_id,
      "order_id" => $subscription_id,
      "payment_id" => $purchase_id,
      // this is a single payment without "next_payment_at"
    );

    $response = $this->digistore_call($useragent, $values);
    $this->assertTrue(empty($response->error), "$message {$response->error}".print_r($response,1));

    // check member

    $member = db_query("SELECT * from {amember_members} WHERE email = :email",
      array(
        ':email' => $email,
      ))->fetchAssoc();
    amember_test_push_error_log($this, !empty($member), "$message member" . print_r($member, 1));
    $this->assertTrue($member['name_l'] == 'Mustermann', "$message member lastname");
    $this->assertTrue($member['state'] == 'DE-BY', "$message member state");
    $name = $member['login'];

    // check payment

    $payment = kt_fetch_array(db_query("SELECT * FROM {amember_payments} WHERE paysys_id = :paysys_id AND receipt_id LIKE :receipt_id ORDER BY payment_id DESC", array(
      ':paysys_id' => $paysys,
      ':receipt_id' => "$subscription_id-%",
    )));
    $this->assertTrue(!empty($payment), "$message payment" . print_r($payment, 1));
    $this->assertTrue($payment['member_id'] == $member['member_id'], "$message payment member_id");
    $this->assertTrue($payment['product_id'] == $pid, "$message payment product_id");
    $this->assertTrue($payment['completed'] == 1, "$message payment completed");
    $this->assertTrue($payment['expire_date'] == "2037-12-31", "$message payment expire_date");

    // check user is active, has klicktipp access and belongs to enterprise group
    $account = amember_test_check_user($this, $message, $name, $email, $product['data']['klicktipp_access'], 1, FALSE, FALSE);

    // check user has product and access until expire date
    $access_timestamp = _amember_user_has_product($account->uid, array($pid), TRUE);
    $this->assertTrue(strtotime($payment['expire_date']) == $access_timestamp, "$message payment access timestamp");

    // check subscriber
    amember_test_check_subscriber($this, $message, $marketing, $email, $member, TRUE, FALSE, FALSE, FALSE, $member['digistore_affiliate_name']);

    // get roleID of consultant role
    $consultant_rid = variable_get('klicktipp_consultant_roleid', 0);
    // check consultant role has been assigned
    $this->assertTrue(user_has_role($consultant_rid, $account), "$message consultant role assigned");

  }

  function digistore_call($useragent, $values) {
    $service = 'https://' . KLICKTIPP_DOMAIN . '/crm/plugins/payment/digistore/ipn.php';

    $order = array(
// required:     "event" => DigiStore::EVENT_PAYMENT,
// required:     "email" => '<EMAIL>',
// required:     "product_id" => $DigiProductID,
      "api_mode" => 'live',
      "merchant_name" => 'Klicktipp',
      "address_city" => 'München',
      "address_country" => 'DE',
      "address_phone_no" => '089 1234 1234',
      "address_state" => 'Bayern',
      "address_street" => 'Bahnhofstr. 11',
      "address_zipcode" => '84783',
      "address_first_name" => 'Michael',
      "address_last_name" => 'Mustermann',
      "affiliate_commission" => 75.00,
      "affiliate_id" => 3,
      "affiliate_name" => 'myAffiliate',
      "amount" => 37.00,
      "billing_type" => 'single_payment',
      "billing_status" => 'completed',
      "buyer_id" => 17,
      "campaignkey" => 'abcdef',
      "country" => 'DE',
      "currency" => 'EUR',
      "custom" => 'custom param',
      "first_amount" => 0.00,
      "first_vat_amount" => 0.00,
      "language" => 'de',
      "merchant_id" => 2,
      "number_of_installments" => '',
      "order_date" => date('Y-m-d'), // today, eg. '2016-03-02'
      "order_id" => '2D83XKFT',
      "order_item_count" => 1,
      "order_item_id" => 280,
      "order_item_no" => 1,
      "order_time" => date("H:i:s"), // eg. '17:54:33'
      "order_date_time" => date("Y-m-d H:i:s"),
      "orderform_id" => 1,
      "pay_sequence_no" => 0,
      "payment_id" => 'PAYID-1-14524820',
      "product_delivery_type" => 'shipping',
      "product_language" => 'de',
      "product_name" => 'Instant Happinness',
      "quantity" => 1,
      "transaction_amount" => 37.00,
      "transaction_currency" => 'EUR',
      "transaction_id" => 271,
      "transaction_type" => 'payment',
      "vat_amount" => 5.91,
      "vat_rate" => 19.00,
      "function_call" => 'on_payment',
      "receipt_link" => "https://www.digistore24.com/receipt/UU4D52QJ/QE94E6QX",
      "renew_link" => "https://www.digistore24.com/renew/UU4D52QJ/QE94E6QX",
    );

    $values = array_merge($order, $values);

    //generate hash
    $values['sha_sign'] = $this->digistore_signature(DigiStore::IPN_PASSPHRASE, $values);

    $options = array(
      'method' => 'POST',
      'timeout' => 15,
      'headers' => array(
        'Content-Type' => 'application/x-www-form-urlencoded',
        'User-Agent' => $useragent,
        'Authorization' => 'Basic ' . base64_encode('dev:ovi8eTei'),
      ),
    );
    if (!empty($values)) {
      $options['data'] = http_build_query($values, '', '&');
    }

    $response = drupal_http_request($service, $options);
    return $response;
  }

  function digistore_signature($ipn_passphrase, $array) {

    unset($array['sha_sign']);

    $keys = array_keys($array);
    sort($keys);

    $sha_string = "";

    foreach ($keys as $key) {

      $value = $array[$key];

      $is_empty = !isset($value) || $value === "" || $value === FALSE;

      if ($is_empty) {
        continue;
      }

      $sha_string .= "$key=$value$ipn_passphrase";

    }

    $sha_sign = strtoupper(hash("sha512", $sha_string));

    return $sha_sign;

  }

}

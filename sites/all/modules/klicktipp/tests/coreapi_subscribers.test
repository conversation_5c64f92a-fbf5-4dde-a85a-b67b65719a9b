<?php

use App\Klicktipp\AutoresponderQueue;
use App\Klicktipp\BounceEngine;
use App\Klicktipp\Campaigns;
use App\Klicktipp\CampaignsNewsletter;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Core;
use App\Klicktipp\Emails;
use App\Klicktipp\Libraries;
use App\Klicktipp\Lists;
use App\Klicktipp\NewsletterQueue;
use App\Klicktipp\Reference;
use App\Klicktipp\Statistics;
use App\Klicktipp\SubscriberQueue;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;
use App\Klicktipp\TransactionalQueue;
use App\Klicktipp\TransactionEmails;
use App\Klicktipp\VarEmailBlacklist;
use App\Tests\Integration\Shared\AmqpSubscriberQueueTestHelper;

class coreapiSubscribersTestCase extends DrupalWebTestCase {

  public function setUp(): void
  {
    parent::setUp();
    AmqpSubscriberQueueTestHelper::initialize();
  }

  public static function getInfo() {
    return array(
      'name' => 'coreapi class Subscribers',
      'description' => 'Test classes/subscribers.inc',
      'group' => 'Klicktipp',
    );
  }

  function testSubscribers() {

    Libraries::include('full_user.inc', '/tests/includes');
    Libraries::include('subscribers.inc', '/tests/includes');
    Libraries::include('transactional.inc', '/tests/includes');

    /*
     * create some test data
     */

    $now = REQUEST_TIME;
    // this will guarantee all SubscriptionDates > $now
    sleep(1);

    $UserID = 1;

    $ReferenceID = 0;

    $account = user_load_by_name(USERNAME_BASIC);
    $BasicUserID = $account->uid;
    $this->assertTrue($BasicUserID > 0, 'get user ' . USERNAME_BASIC);

    $EmailAddress = '<EMAIL>';
    $Email2Address = '<EMAIL>';
    $Test2EmailAddress = '<EMAIL>';
    $Test3EmailAddress = '<EMAIL>';
    $SOEmailAddress = '<EMAIL>';

    $SMSEmailAddress = '<EMAIL>';
    $SMSPhonenumber = '************';
    $SMSEmailAddress2 = '<EMAIL>';
    $SMSPhonenumber2 = '***************';
    $SMSOnlyPhoneNumber = '***********';

    // create list with redirect parameters
    $message = 'create subscriber list';
    $Redirect = APP_URL;
    $ArrayFieldAndValues = array(
      'Name' => 'test confirm list',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE,
      'SubscriptionConfirmedPageURL' => $Redirect,
    );
    $ListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($ListID > 0, $message);
    $ArraySubscriberList = Lists::RetrieveListByID($UserID, $ListID);

    // create a single optin list
    $message = 'create single optin subscriber list';
    $Redirect = APP_URL;
    $ArrayFieldAndValues = array(
      'Name' => 'test single optin list',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
      'SubscriptionConfirmedPageURL' => $Redirect,
    );
    $ListSO = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($ListID > 0, $message);
    $ArraySOSubscriberList = Lists::RetrieveListByID($UserID, $ListSO);
    $this->assertTrue($ArraySOSubscriberList['OptInModeEnum'] == Lists::OPTIN_MODE_SINGLE, $message . ' mode ' . print_r($ArraySOSubscriberList, 1));

    // create a tag
    $message = 'create subscriber tags';
    $OptInSubscribeTo = Tag::CreateManualTag($UserID, 'some tag name', '');
    $this->assertTrue($OptInSubscribeTo > 0, $message);
    $OptInSubscribeTo2 = Tag::CreateManualTag($UserID, 'some tag name 2', '');
    $this->assertTrue($OptInSubscribeTo2 > 0, $message . ' 2');
    $OptInSubscribeTo3 = Tag::CreateManualTag($UserID, 'some tag name 3', '');
    $this->assertTrue($OptInSubscribeTo3 > 0, $message . ' 3');
    $OptInSubscribeTo4 = Tag::CreateManualTag($UserID, 'some tag name 4', '');
    $this->assertTrue($OptInSubscribeTo4 > 0, $message . ' 4');
    $OptInSubscribeTo5 = Tag::CreateManualTag($UserID, 'some tag name 5', '');
    $this->assertTrue($OptInSubscribeTo5 > 0, $message . ' 5');

    // same with second user
    $message = 'BasicUser create subscriber tags';
    $OptInSubscribeToBasicUser1 = Tag::CreateManualTag($BasicUserID, "$message 1", '');
    $this->assertTrue($OptInSubscribeToBasicUser1 > 0, $message);
    $OptInSubscribeToBasicUser2 = Tag::CreateManualTag($BasicUserID, "$message 2", '');
    $this->assertTrue($OptInSubscribeToBasicUser2 > 0, $message);

    // create custom field
    $UserCustomFieldID = CustomFields::Create([
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'Line',
      'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
    ]);
    $UserCustomFieldInformation = CustomFields::RetrieveCustomField($UserCustomFieldID, $UserID);
    $this->assertTrue(is_array($UserCustomFieldInformation), $message . ' user field exists');
    // create 2nd custom field
    $UserCustomFieldNumberID = CustomFields::Create([
      'RelOwnerUserID' => $UserID,
      'FieldName' => 'Number',
      'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
    ]);
    $UserCustomFieldNumberInformation = CustomFields::RetrieveCustomField($UserCustomFieldNumberID, $UserID);
    $this->assertTrue(is_array($UserCustomFieldNumberInformation), $message . ' user field exists');
    // this is the global field
    $GlobalCustomFieldInformation = CustomFields::RetrieveCustomField('FirstName');
    $this->assertTrue(is_array($GlobalCustomFieldInformation), $message . ' global field exists');

    /**
     * Validates email address format
     */
    $message = 'Subscribers::ValidateEmailAddress';

    $Result = Subscribers::ValidateEmailAddress('<EMAIL>');
    $this->assertTrue($Result === TRUE, $message . ' check syntax');
    $Result = Subscribers::ValidateEmailAddress('.@example.com');
    $this->assertTrue($Result === FALSE, $message . ' check wrong syntax');
    $Result = Subscribers::ValidateEmailAddress('<EMAIL>');
    $this->assertTrue($Result === TRUE, $message . ' no trash mail');

    /** some additional test cases for user blacklist **/

    VarEmailBlacklist::SetVariable($UserID, "*blacklist-pattern*\n\nblacklisted-domain.com$\n");

    $Result = Subscribers::ValidateEmailAddress('<EMAIL>');
    $this->assertTrue($Result === TRUE, $message . ' contains pattern defined by user, UserID is NOT provided');
    $Result = Subscribers::ValidateEmailAddress('<EMAIL>', $UserID);
    $this->assertTrue($Result === FALSE, $message . ' contains pattern defined by user, UserID is provided');

    $Result = Subscribers::ValidateEmailAddress('<EMAIL>');
    $this->assertTrue($Result === TRUE, $message . ' domain blacklisted by user, UserID is NOT provided');
    $Result = Subscribers::ValidateEmailAddress('<EMAIL>', $UserID);
    $this->assertTrue($Result === FALSE, $message . ' domain blacklisted, UserID is provided');

    /**
     * Checks if the provided email address already exists in the subscriber list or not
     */
    $message = 'Subscribers:RetrieveSubscriberIDByEmailAddress';
    $Result = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $EmailAddress);
    $this->assertTrue(empty($Result), $message);

    /**
     * Optin pending
     **/
    $message = 'Subscribers::Subscribe optin';
    // create a subscriber
    $UserFieldValue = 'Userfield-Value';
    $GlobalFieldValue = 'Globalfield-Value';
    $IPAddress = '***********';
    $Parameters = array(
      'UserID' => $UserID,
      'ReferenceID' => $ReferenceID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => $IPAddress,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      'UpdateIfUnsubscribed' => TRUE,
      // this will write a confirmation email, so check existence manually
      'SendConfirmationEmail' => TRUE,
      'TriggerAutoResponders' => FALSE,
      'OtherFields' => array(
        'CustomField' . $UserCustomFieldInformation['CustomFieldID'] => $UserFieldValue,
        'CustomField' . $GlobalCustomFieldInformation['CustomFieldID'] => $GlobalFieldValue,
        // get a validation error
        'CustomField' . $UserCustomFieldNumberInformation['CustomFieldID'] => 'this is not a number',
      ),
    );

    $before_count = get_confirmation_email_count();

    // validation error
    [$success, $errors] = Subscribers::SubscriptionParameterValidation($Parameters, FALSE);
    $this->assertTrue(!$success, $message . ' validate ' . print_r($errors, 1));
    $this->assertTrue(reset($errors)['ErrorCode'] == Subscribers::SUBSCRIBE_ERROR_CUSTOMFIELD_DATA, $message . ' error code');

    // validation success
    unset($Parameters['OtherFields']['CustomField' . $UserCustomFieldNumberInformation['CustomFieldID']]);
    [$success, $ValidationParameters] = Subscribers::SubscriptionParameterValidation($Parameters, FALSE);
    $this->assertTrue($success, $message . ' validate ' . print_r($ValidationParameters, 1));

    $timebefore = time();
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber ' . print_r($Result, 1));
    $SubscriberID = $Result[1];

    // check confirmation email
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriber['LastOpenDate'] == 0, "$message LastOpenDate".print_r($FullSubscriber,1));
    $this->assertTrue(empty($FullSubscriber['LastOpenIP']), "$message LastOpenIP");
    $this->assertTrue($FullSubscriber['ConfirmationEmailDate'] >= $timebefore, "$message ConfirmationEmailDate {$FullSubscriber['ConfirmationEmailDate']} >= $timebefore");
    check_confirmation_email_count($this, $message, $before_count + 1);

    // simulate confirmation email not sent today
    $lasttimesent = strtotime("-1 days"); // yesterday
    db_update('subscription')
      ->fields(array(
        'ConfirmationEmailDate' => $lasttimesent,
      ))
      ->condition('RelSubscriberID', $SubscriberID)
      ->condition('RelOwnerUserID', $UserID)
      ->condition('SubscriptionType', Subscription::SUBSCRIPTIONTYPE_EMAIL)
      ->condition('ContactInfo', $EmailAddress)
      ->execute();

    // send another confirmation email
    $timebefore = time();
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' subscribe 2 ' . print_r($Result, 1));
    $LastConfirmationEmailDate = db_query("SELECT ConfirmationEmailDate FROM {subscription} WHERE RelSubscriberID = :RelSubscriberID AND RelOwnerUserID = :RelOwnerUserID AND SubscriptionType = :SubscriptionType AND ContactInfo = :ContactInfo", array(
      ':RelSubscriberID' => $SubscriberID,
      ':RelOwnerUserID' => $UserID,
      ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_EMAIL,
      ':ContactInfo' => $EmailAddress,
    ))->fetchField();
    $this->assertTrue($LastConfirmationEmailDate >= $timebefore, "$message ConfirmationEmailDate 2nd $LastConfirmationEmailDate >= $timebefore");
    check_confirmation_email_count($this, $message, $before_count + 2);

    // simulate confirmation email sent more than one day before
    $lasttimesent = strtotime("00:00:01"); // today, but not now
    db_update('subscription')
      ->fields(array(
        'ConfirmationEmailDate' => $lasttimesent,
      ))
      ->condition('RelSubscriberID', $SubscriberID)
      ->condition('RelOwnerUserID', $UserID)
      ->condition('SubscriptionType', Subscription::SUBSCRIPTIONTYPE_EMAIL)
      ->condition('ContactInfo', $EmailAddress)
      ->execute();

    // no more confirmation email
    Subscribers::Subscribe($Parameters);
    $LastConfirmationEmailDate = db_query("SELECT ConfirmationEmailDate FROM {subscription} WHERE RelSubscriberID = :RelSubscriberID AND RelOwnerUserID = :RelOwnerUserID AND SubscriptionType = :SubscriptionType AND ContactInfo = :ContactInfo", array(
      ':RelSubscriberID' => $SubscriberID,
      ':RelOwnerUserID' => $UserID,
      ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_EMAIL,
      ':ContactInfo' => $EmailAddress,
    ))->fetchField();
    $this->assertTrue($LastConfirmationEmailDate == $lasttimesent, "$message ConfirmationEmailDate 3rd $LastConfirmationEmailDate == $lasttimesent");
    check_confirmation_email_count($this, $message, $before_count + 2);

    // Unsubscribe -> (Re)Subscribe : send confirmation email
    // 1. subscribe with SOI
    $Parameters2 = $Parameters;
    $Parameters2['ListInformation'] = array(
      'ListID' => 0,
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
    );
    $Parameters2['SubscriptionStatus'] = 0;
    Subscribers::Subscribe($Parameters2);
    $ArraySubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddress);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message now subscribed".print_r($ArraySubscriber,1));
    $this->assertTrue($ArraySubscriber['ConfirmationEmailDate'] == $lasttimesent, "$message ConfirmationEmailDate unchanged");
    check_confirmation_email_count($this, $message, $before_count + 2);
    // 2. unsubscribe
    Subscribers::Unsubscribe($UserID, $EmailAddress);
    $ArraySubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddress);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message now unsubscribed".print_r($ArraySubscriber,1));
    $this->assertTrue($ArraySubscriber['ConfirmationEmailDate'] == 0, "$message ConfirmationEmailDate reseted");
    check_confirmation_email_count($this, $message, $before_count + 2);
    // 2. subscribe with DOI
    Subscribers::Subscribe($Parameters);
    $ArraySubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddress);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message now pending".print_r($ArraySubscriber,1));
    $this->assertTrue($ArraySubscriber['ConfirmationEmailDate'] > $lasttimesent, "$message ConfirmationEmailDate set");
    check_confirmation_email_count($this, $message, $before_count + 3);
    $lasttimesent = $ArraySubscriber['ConfirmationEmailDate'];

    // subscribe again SOI (no confirmation email)
    Subscribers::Subscribe($Parameters2);
    $ArraySubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddress);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message now subscribed".print_r($ArraySubscriber,1));
    $this->assertTrue($ArraySubscriber['ConfirmationEmailDate'] == $lasttimesent, "$message ConfirmationEmailDate unchanged");
    check_confirmation_email_count($this, $message, $before_count + 3);

    // subscribe again DOI (no confirmation email)
    Subscribers::Subscribe($Parameters);
    $ArraySubscriber = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddress);
    $this->assertTrue($ArraySubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message now subscribed".print_r($ArraySubscriber,1));
    $this->assertTrue($ArraySubscriber['ConfirmationEmailDate'] == $lasttimesent, "$message ConfirmationEmailDate unchanged");
    check_confirmation_email_count($this, $message, $before_count + 3);

    /**
     * Returns a single subscriber matching given criterias
     **/
    $message = 'Subscriber::RetrieveSubscriptionByEmailAddress';
    $Result = Subscription::RetrieveSubscriptionByEmailAddress($UserID, $EmailAddress);
    $this->assertTrue($Result !== FALSE, $message . ' exists');

    $message = 'Subscribers::RetrieveSubscriber';
    $ArraySubscriberInformation = Subscribers::RetrieveSubscriber($UserID, $SubscriberID);
    $this->assertTrue($ArraySubscriberInformation['SubscriberID'] == $SubscriberID, $message . ' SubscriberID');

    $message = 'Subscribers::RetrieveFullSubscriberWithFields';
    $ArraySubscriberInformation = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($ArraySubscriberInformation['EmailAddress'] == $EmailAddress, $message . ' EmailAddress 2 '.print_r($ArraySubscriberInformation,1));
    $this->assertTrue($ArraySubscriberInformation['OptInIP'] == $IPAddress, $message . ' IPAddress');
    $this->assertTrue($ArraySubscriberInformation['CustomField' . $UserCustomFieldInformation['CustomFieldID']] == $UserFieldValue, $message . ' User CustomField');
    $this->assertTrue($ArraySubscriberInformation['CustomField' . $GlobalCustomFieldInformation['CustomFieldID']] == $GlobalFieldValue, $message . ' Global CustomField');

    $message = 'Subscribers::RetrieveTagging';
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $OptInSubscribeTo, $SubscriberID, $ReferenceID);
    $this->assertTrue($ArrayTagging == FALSE, $message . ' tagging');

    /**
     * SubscriptionParameterValidation simple checks
     */
    $this->subscribeValidationCheck('missing UserID', [], FALSE, KLICKTIPPAPI_ERROR_USER_NOT_FOUND);
    $this->subscribeValidationCheck('missing ListID', [
      'UserID' => 4711,
    ], FALSE, KLICKTIPPAPI_ERROR_LIST_NOT_FOUND);
    $this->subscribeValidationCheck('invalid SubscriberID', [
      'UserID' => 4711,
      'SubscriberID' => 10815,
    ], FALSE, Subscribers::SUBSCRIBE_ERROR_INVALID_SUBSCRIBER);
    $this->subscribeValidationCheck('invalid EmailAddress', [
      'UserID' => $UserID,
      'EmailAddress' => 'not a valid email address',
    ], FALSE, KLICKTIPPAPI_ERROR_EMAILADDRESS_VALIDATION_FAILED);
    $this->subscribeValidationCheck('invalid PhoneNumber', [
      'UserID' => $UserID,
      'PhoneNumber' => 'not a valid email phone number',
    ], FALSE, Subscribers::SUBSCRIBE_ERROR_INVALID_PHONE_NUMBER);
    $this->subscribeValidationCheck('missing OptInSubscribeTo', [
      'UserID' => $UserID,
      'SubscriberID' => $SubscriberID,
      'OptInSubscribeTo' => 4711
    ], FALSE, KLICKTIPPAPI_ERROR_TAG_NOT_FOUND);

    /**
     * Subscribe subscribed
     **/
    $message = 'Subscribers::Subscribe subscribe';
    // change to Subscribed
    $UserFieldValue = 'Userfield-Value-Updated-Twice'; // -> changed
    $GlobalFieldValue = 'Globalfield-Value-Updated-Twice'; // -> changed
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => $Email2Address,
      'OptInSubscribeTo' => $OptInSubscribeTo,
      // -> changed
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
      'UpdateIfDuplicate' => TRUE,
      'UpdateIfUnsubscribed' => TRUE,
      'IPAddress' => "*******",
      'OtherFields' => array(
        'CustomField' . $UserCustomFieldInformation['CustomFieldID'] => $UserFieldValue,
        'CustomField' . $GlobalCustomFieldInformation['CustomFieldID'] => $GlobalFieldValue,
      ),
    );
    $statsbefore = Statistics::RetrieveOverallListActivityOfUser($UserID);

    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber'.print_r($Result,1));
    $SubscriberIDS = $Result[1];

    $stats = Statistics::RetrieveOverallListActivityOfUser($UserID);
    $this->assertTrue($stats[date('Y-m-d')]['TotalSubscriptions'] == $statsbefore[date('Y-m-d')]['TotalSubscriptions'] + 1,
      $message . ' TotalSubscriptions' . print_r([$stats[date('Y-m-d')], $statsbefore[date('Y-m-d')]], 1));

    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberIDS, $ReferenceID);
    $this->assertTrue($FullSubscriber['LastOpenDate'] >= REQUEST_TIME, "$message LastOpenDate".print_r($FullSubscriber,1));
    $this->assertTrue($FullSubscriber['LastOpenIP'] == $Parameters['IPAddress'], "$message LastOpenIP");

    // subscribe some more
    $Parameters['EmailAddress'] = $Test2EmailAddress;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber 2');
    $SubscriberID2 = $Result[1];
    Subscribers::TagSubscriber($UserID, $SubscriberID2, $OptInSubscribeTo2, $ReferenceID);

    $Parameters['EmailAddress'] = $Test3EmailAddress;
    $Parameters['OptInSubscribeTo'] = $OptInSubscribeTo2;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber 3');
    $SubscriberID3 = $Result[1];

    // subscribe sms
    $Parameters['EmailAddress'] = $SMSEmailAddress;
    $Parameters['PhoneNumber'] = $SMSPhonenumber;
    $Parameters['OptInSubscribeTo'] = $OptInSubscribeTo;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber sms+email');
    $SubscriberIDSMS = $Result[1];

    $Parameters['EmailAddress'] = $SMSEmailAddress2;
    $Parameters['PhoneNumber'] = $SMSPhonenumber2;
    $Parameters['OptInSubscribeTo'] = $OptInSubscribeTo;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber sms+email');
    $SubscriberIDSMS2 = $Result[1];
    Subscribers::TagSubscriber($UserID, $SubscriberIDSMS2, $OptInSubscribeTo2, $ReferenceID);

    $Parameters['EmailAddress'] = '';
    $Parameters['PhoneNumber'] = $SMSOnlyPhoneNumber;
    $Parameters['OptInSubscribeTo'] = $OptInSubscribeTo2;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber sms only');
    $SubscriberIDSMSonly = $Result[1];

    // --- subscribe/update with SubscriberID no EmailAddress

    $Parameters['SubscriptionStatus'] = 0;

    $message = 'Subscribers::Subscribe update with SubscriberID no EmailAddress';
    $ExistingSubscriberID = $SubscriberID3;

    $ExistingSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ExistingSubscriberID, $ReferenceID);
    $isSubscribed = ($ExistingSubscriber && $ExistingSubscriber['EmailAddress'] == $Test3EmailAddress && $ExistingSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->assertTrue($isSubscribed, "$message: Subscriber $ExistingSubscriberID with $Test3EmailAddress is subscribed");

    //update custom fields and tags of subscriber by SubscriberID
    $Parameters['EmailAddress'] = "";
    $Parameters['PhoneNumber'] = "";
    $Parameters['SubscriberID'] = $ExistingSubscriberID;
    $Parameters['OptInSubscribeTo'] = $OptInSubscribeTo3;
    $Parameters['OtherFields'] = array('CustomFieldFirstName' => 'updated by SubscriberID');

    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'".print_r($Result,1));
    $UpdateSubscriberID = $Result[1];
    $this->assertTrue($UpdateSubscriberID == $ExistingSubscriberID, "$message: SubscriberID unchanged");
    $UpdatedSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $UpdateSubscriberID, $ReferenceID);
    $isSubscribed = ($UpdatedSubscriber && $UpdatedSubscriber['EmailAddress'] == $ExistingSubscriber['EmailAddress'] && $UpdatedSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    //check subscription
    $this->assertTrue($isSubscribed, "$message: Subscriber Status and EmailAddress unchanged");
    //check updated custom fields
    $this->assertTrue($UpdatedSubscriber['CustomFieldFirstName'] == $Parameters['OtherFields']['CustomFieldFirstName'], "$message: Subscriber Firstname updated to '{$UpdatedSubscriber['CustomFieldFirstName']}'");
    //check tagging
    $taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $UpdateSubscriberID, $ReferenceID, TRUE);
    $this->assertTrue(in_array($OptInSubscribeTo3, $taggings), "$message: Subscriber tagged with TagID '$OptInSubscribeTo3'".print_r($taggings,1));

    // --- subscribe/update with SubscriberID and (different) EmailAddress

    $message = 'Subscribers::Subscribe update with SubscriberID different EmailAddress';

    $ExistingSubscriber = $UpdatedSubscriber;

    //update custom fields and tags of subscriber by SubscriberID
    $Parameters['SubscriberID'] = $ExistingSubscriber['SubscriberID'];
    $Parameters['EmailAddress'] = "<EMAIL>"; //change email
    $Parameters['OptInSubscribeTo'] = $OptInSubscribeTo4;
    $Parameters['OtherFields'] = array('CustomFieldFirstName' => 'updated by SubscriberID different email address');

    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");
    $UpdateSubscriberID = $Result[1];
    $this->assertTrue($UpdateSubscriberID == $ExistingSubscriberID, "$message: SubscriberID unchanged");
    $UpdatedSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $UpdateSubscriberID, $ReferenceID);
    //check subscription
    $this->assertTrue($UpdatedSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message: subscriber status of default email subscription changed to optin");
    $this->assertTrue($UpdatedSubscriber['EmailAddress'] == $Parameters['EmailAddress'], "$message: defaut EmailAddress changed");
    //check updated custom fields
    $this->assertTrue($UpdatedSubscriber['CustomFieldFirstName'] == $Parameters['OtherFields']['CustomFieldFirstName'], "$message: Subscriber Firstname updated to '{$UpdatedSubscriber['CustomFieldFirstName']}'");
    //check tagging
    $taggings = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $UpdateSubscriberID, $ReferenceID, TRUE);
    $this->assertTrue(in_array($OptInSubscribeTo4, $taggings), "$message: Subscriber tagged with TagID '$OptInSubscribeTo4'");

    // --- subscribe/update with  existing EmailAddress without SubscriberID (SubscriberID is supposed to be detected)
    $message = 'Subscribers::Subscribe update with existing EmailAddress only';
    [, $UpdateSubscriberID] = Subscribers::Subscribe([
      'UserID' => $UserID,
      'EmailAddress' => $Test3EmailAddress
    ]);
    $this->assertEqual($UpdateSubscriberID, $ExistingSubscriber['SubscriberID'], $message . 'Subscriber properly detected by EmailAddress');
    $UpdatedSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $UpdateSubscriberID, $ReferenceID);
    $this->assertTrue($UpdatedSubscriber['EmailAddress'] == $Test3EmailAddress, "$message: found existing EmailAddress is now default");
    $this->assertTrue($UpdatedSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message: default email subscription is subscribed");

    // --- subscribe/update with invalid SubscriberID and existing EmailAddress

    $message = 'Subscribers::Subscribe update with SubscriberID and existing EmailAddress';

    $ExistingSubscriber = $UpdatedSubscriber;

    $Parameters['SubscriberID'] = $ExistingSubscriber['SubscriberID'];
    $Parameters['EmailAddress'] = $Test2EmailAddress; //change email to existing email address -> fail

    // trying to add an existing email address fails (a new one will be added as subscription)
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue(!$Result[0], "$message: failed, Result = '{$Result[1]}'".print_r($Result,1));

    // ------------------------

    $message = 'Subscribers::RetrieveSubscriber';
    $ArraySubscriberInformation = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberIDS, $ReferenceID);
    $this->assertTrue($ArraySubscriberInformation['EmailAddress'] == $Email2Address, $message . ' EmailAddress');

    $message = 'Subscribers::RetrieveFullSubscriberWithFields';
    $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberIDS, $ReferenceID);
    $this->assertTrue($FullSubscriberWithFields['EmailAddress'] == $Email2Address, $message . ' EmailAddress 2'.print_r($FullSubscriberWithFields,1));
    $this->assertTrue($FullSubscriberWithFields['OptInIP'] == $Parameters['IPAddress'], $message . ' IPAddress');
    $this->assertTrue($FullSubscriberWithFields['CustomField' . $UserCustomFieldInformation['CustomFieldID']] == $UserFieldValue, $message . ' User CustomField');
    $this->assertTrue($FullSubscriberWithFields['CustomField' . $GlobalCustomFieldInformation['CustomFieldID']] == $GlobalFieldValue, $message . ' Global CustomField');
    $this->assertTrue($FullSubscriberWithFields['SubscriptionDate'] > 0, $message . ' SubscriptionDate');

    $message = 'Subscribers::RetrieveTagging';
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $OptInSubscribeTo, $SubscriberIDS, $ReferenceID);
    $this->assertTrue(!empty($ArrayTagging), $message . ' tagging');

    //TODO test with imported

    /**
     * Returns all subscribers matching given tag
     **/

    /* taggings are:
     *  $SubscriberIDS = $OptInSubscribeTo
     *  $SubscriberID2 = $OptInSubscribeTo + $OptInSubscribeTo2
     *  $SubscriberID3 = $OptInSubscribeTo2
     *  $SubscriberIDSMS = $OptInSubscribeTo
     *  $SubscriberIDSMS2 = $OptInSubscribeTo + $OptInSubscribeTo2
     *  $SubscriberIDSMSonly = $OptInSubscribeTo2
     */

    $message = 'create a lot of subscribers';

    // same as  $SubscriberIDS = $OptInSubscribeTo
    $BasicUserIDEmail1_count = mt_rand(10, 200);
    $error = _testdata_subscribers($BasicUserID, $BasicUserIDEmail1_count, "BasicUserIDEmail1", $OptInSubscribeToBasicUser1);
    $this->assertFalse($error, "$message $error");
    // same as  $SubscriberID2 = $OptInSubscribeTo + $OptInSubscribeTo2
    $BasicUserIDEmail2_count = mt_rand(10, 200);
    $error = _testdata_subscribers($BasicUserID, $BasicUserIDEmail2_count, "BasicUserIDEmail2", $OptInSubscribeToBasicUser1,
      FALSE, FALSE, FALSE, array($OptInSubscribeToBasicUser2));
    $this->assertFalse($error, "$message $error");
    // same as  $SubscriberID3 = $OptInSubscribeTo2
    $BasicUserIDEmail3_count = mt_rand(10, 200);
    $error = _testdata_subscribers($BasicUserID, $BasicUserIDEmail3_count, "BasicUserIDEmail3", $OptInSubscribeToBasicUser2);
    $this->assertFalse($error, "$message $error");
    // same as  $SubscriberIDSMS = $OptInSubscribeTo
    $BasicUserIDSMS1_count = mt_rand(10, 200);
    $error = _testdata_subscribers($BasicUserID, $BasicUserIDSMS1_count, "BasicUserIDSMS1", $OptInSubscribeToBasicUser1,
      FALSE, FALSE, FALSE, array(), $phoneprefix = '00491111', TRUE);
    $this->assertFalse($error, "$message $error");
    // same as  $SubscriberIDSMS2 = $OptInSubscribeTo + $OptInSubscribeTo2
    $BasicUserIDEmail2SMS_count = mt_rand(10, 200);
    $error = _testdata_subscribers($BasicUserID, $BasicUserIDEmail2SMS_count, "BasicUserIDEmail2SMS", $OptInSubscribeToBasicUser1,
      FALSE, FALSE, FALSE, array($OptInSubscribeToBasicUser2), $phoneprefix = '00492222', TRUE);
    $this->assertFalse($error, "$message $error");
    // same as  $SubscriberIDSMSonly = $OptInSubscribeTo2
    $BasicUserIDSMSOnly_count = mt_rand(10, 200);
    $error = _testdata_subscribers($BasicUserID, $BasicUserIDSMSOnly_count, "BasicUserIDSMSOnly", $OptInSubscribeToBasicUser2,
      FALSE, FALSE, FALSE, array(), $phoneprefix = '00493333', TRUE, FALSE);
    $this->assertFalse($error, "$message $error");

    // make all subscription dates comparable (constant = $now)

    db_update('subscription')
      ->fields(array(
        'SubscriptionDate' => $now,
      ))
      ->condition('SubscriptionDate', 0, '>')
      ->condition('RelOwnerUserID', $UserID)
      ->execute();

    $message = 'RetrieveTaggedSubscribers all active';

    $AllSubscriptions = Subscribers::RetrieveTaggedSubscriberIDs($UserID, $ReferenceID);
    $this->assertTrue(count($AllSubscriptions) == 7, $message . ' count' . print_r($AllSubscriptions, 1));

    $AllSubscriptions = Subscribers::RetrieveTaggedSubscriberIDs($UserID, $ReferenceID, [], TRUE);
    $this->assertTrue(count($AllSubscriptions) == 7, $message . ' count' . print_r($AllSubscriptions, 1));
    $this->assertTrue(current($AllSubscriptions)['SubscriptionDate'] == $now, $message . ' SubscriptionDate');

    $message = 'RetrieveTaggedSubscribers Email all active';
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN);
    $this->assertTrue(count($AllSubscriptions) == 6, $message . ' count' . print_r($AllSubscriptions, 1));
    $this->assertTrue(current($AllSubscriptions) == $SubscriberID, $message . ' id '.$SubscriberID);
    $ArrayBasicUserSubscribers = self::GetTaggedSubscribersQueryCheck($BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDEmail1_count + $BasicUserIDEmail2_count + $BasicUserIDEmail3_count + $BasicUserIDSMS1_count + $BasicUserIDEmail2SMS_count, $message . ' basic user count');

    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, [], TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, [], TRUE);
    $this->assertTrue($TaggedSubscriberID == $SubscriberID, $message . ' id first only '.$TaggedSubscriberID);

    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, [], TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, [], TRUE, TRUE);
    $this->assertTrue($AllSubscriptions['SubscriberID'] == $SubscriberID, $message . ' with date' . print_r($AllSubscriptions, 1));
    $this->assertTrue($AllSubscriptions['SubscriptionDate'] == $now, $message . ' date');

    $message = 'RetrieveTaggedSubscribers SMS all active';
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN);
    $this->assertTrue(count($AllSubscriptions) == 3, $message . ' count');
    $this->assertTrue(current($AllSubscriptions) == $SubscriberIDSMS, $message . ' id' . print_r($AllSubscriptions, 1));
    $ArrayBasicUserSubscribers = self::GetTaggedSubscribersQueryCheck($BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDSMS1_count + $BasicUserIDEmail2SMS_count + $BasicUserIDSMSOnly_count, $message . ' basic user count');
    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, [], TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, [], TRUE);
    $this->assertTrue($TaggedSubscriberID == $SubscriberIDSMS, $message . ' id first only '.$TaggedSubscriberID.' '.$SubscriberIDSMS);
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, [], TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, [], TRUE, TRUE);
    $this->assertTrue($AllSubscriptions['SubscriberID'] == $SubscriberIDSMS, $message . ' with date' . print_r($AllSubscriptions, 1));
    $this->assertTrue($AllSubscriptions['SubscriptionDate'] == $now, $message . ' date');

    $message = 'RetrieveTaggedSubscribers with tagging any';
    $ArrayRecipients = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($OptInSubscribeTo2),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );
    $ArrayRecipientsBasicUser = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($OptInSubscribeToBasicUser2),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );

    // all active email+sms subscriptions
    $AllSubscriptions = Subscribers::RetrieveTaggedSubscriberIDs($UserID, $ReferenceID, $ArrayRecipients['TaggedWith']);
    $this->assertTrue(count($AllSubscriptions) == 4, $message . ' count all active tagged'.print_r($AllSubscriptions,1));
    $AllSubscriptions = Subscribers::RetrieveTaggedSubscriberIDs($UserID, $ReferenceID, $ArrayRecipients['TaggedWith'], TRUE);
    $this->assertTrue(count($AllSubscriptions) == 4, $message . ' count all active tagged'.print_r($AllSubscriptions,1));
    $this->assertTrue(current($AllSubscriptions)['SubscriptionDate'] > $now, $message . ' SubscriptionDate');
    $ArrayBasicUserSubscribers = Subscribers::RetrieveTaggedSubscriberIDs($BasicUserID, $ReferenceID, $ArrayRecipientsBasicUser['TaggedWith']);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDEmail2_count + $BasicUserIDEmail3_count + $BasicUserIDEmail2SMS_count + $BasicUserIDSMSOnly_count, $message . ' basic user count'.count($ArrayBasicUserSubscribers));

    // all active email subscriptions
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients);
    $this->assertTrue(count($AllSubscriptions) == 3, $message . ' count'.print_r($AllSubscriptions,1));
    $ArrayBasicUserSubscribers = self::GetTaggedSubscribersQueryCheck($BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDEmail2_count + $BasicUserIDEmail3_count + $BasicUserIDEmail2SMS_count, $message . ' count '.count($ArrayBasicUserSubscribers). ' = '.($BasicUserIDEmail2_count + $BasicUserIDEmail3_count + $BasicUserIDEmail2SMS_count));
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser);

    $this->assertTrue(current($AllSubscriptions) == $SubscriberID2, $message . ' id');
    $ArrayBasicUserSubscribers = self::GetTaggedSubscribersQueryCheck($BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDEmail2_count + $BasicUserIDEmail3_count + $BasicUserIDEmail2SMS_count, $message . ' basic user count');
    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE);
    $this->assertTrue($TaggedSubscriberID == $SubscriberID2, $message . ' id first only');
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE, TRUE);
    $this->assertTrue($AllSubscriptions['SubscriberID'] == $SubscriberID2, $message . ' with date' . print_r($AllSubscriptions, 1));
    $this->assertTrue($AllSubscriptions['SubscriptionDate'] > $now, $message . ' date');

    $message = 'RetrieveTaggedSubscribers SMS with tagging any';
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($AllSubscriptions) == 2, $message . ' count');
    $this->assertTrue(current($AllSubscriptions) == $SubscriberIDSMS2, $message . ' id');
    $ArrayBasicUserSubscribers = self::GetTaggedSubscribersQueryCheck($BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDEmail2SMS_count + $BasicUserIDSMSOnly_count, $message . ' basic user count');
    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE);
    $this->assertTrue($TaggedSubscriberID == $SubscriberIDSMS2, $message . ' id first only');
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients, TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE, TRUE);
    $this->assertTrue($AllSubscriptions['SubscriberID'] == $SubscriberIDSMS2, $message . ' with date' . print_r($AllSubscriptions, 1));
    $this->assertTrue($AllSubscriptions['SubscriptionDate'] > $now, $message . ' date');

    $message = 'RetrieveTaggedSubscribers with tagging any 2';
    $ArrayRecipients = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($OptInSubscribeTo, 4711),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );
    $ArrayRecipientsBasicUser = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($OptInSubscribeToBasicUser1, 4711),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );

    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($AllSubscriptions) == 4, $message . ' count' . print_r($AllSubscriptions, 1));
    $this->assertTrue(current($AllSubscriptions) == $SubscriberIDS, $message . ' id');
    $ArrayBasicUserSubscribers = self::GetTaggedSubscribersQueryCheck($BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDEmail1_count + $BasicUserIDEmail2_count + $BasicUserIDSMS1_count + $BasicUserIDEmail2SMS_count, $message . ' basic user count');
    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE);
    $this->assertTrue($TaggedSubscriberID == $SubscriberIDS, $message . ' id first only');
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE, TRUE);
    $this->assertTrue($AllSubscriptions['SubscriberID'] == $SubscriberIDS, $message . ' with date' . print_r($AllSubscriptions, 1));
    $this->assertTrue($AllSubscriptions['SubscriptionDate'] > $now, $message . ' date');

    $message = 'RetrieveTaggedSubscribers SMS with tagging any 2';
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($AllSubscriptions) == 2, $message . ' count');
    $this->assertTrue(current($AllSubscriptions) == $SubscriberIDSMS, $message . ' id');
    $ArrayBasicUserSubscribers = self::GetTaggedSubscribersQueryCheck($BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDSMS1_count + $BasicUserIDEmail2SMS_count, $message . ' basic user count');
    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE);
    $this->assertTrue($TaggedSubscriberID == $SubscriberIDSMS, $message . ' id first only');
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients, TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE, TRUE);
    $this->assertTrue($AllSubscriptions['SubscriberID'] == $SubscriberIDSMS, $message . ' with date');
    $this->assertTrue($AllSubscriptions['SubscriptionDate'] > $now, $message . ' date');

    $message = 'RetrieveTaggedSubscribers with tagging all';
    $ArrayRecipients = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($OptInSubscribeTo, $OptInSubscribeTo2),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );
    $ArrayRecipientsBasicUser = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($OptInSubscribeToBasicUser1, $OptInSubscribeToBasicUser2),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );

    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($AllSubscriptions) == 2, $message . ' count');
    $this->assertTrue(current($AllSubscriptions) == $SubscriberID2, $message . ' id');
    $ArrayBasicUserSubscribers = self::GetTaggedSubscribersQueryCheck($BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDEmail2_count + $BasicUserIDEmail2SMS_count, $message . ' basic user count');
    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE);
    $this->assertTrue($TaggedSubscriberID == $SubscriberID2, $message . ' id first only');
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE, TRUE);
    $this->assertTrue($AllSubscriptions['SubscriberID'] == $SubscriberID2, $message . ' with date');
    $this->assertTrue($AllSubscriptions['SubscriptionDate'] >= $now, $message . ' date');

    $message = 'RetrieveTaggedSubscribers SMS with tagging all';
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($AllSubscriptions) == 1, $message . ' count');
    $this->assertTrue(current($AllSubscriptions) == $SubscriberIDSMS2, $message . ' id');
    $ArrayBasicUserSubscribers = self::GetTaggedSubscribersQueryCheck($BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(count($ArrayBasicUserSubscribers) == $BasicUserIDEmail2SMS_count, $message . ' basic user count');
    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE);
    $this->assertTrue($TaggedSubscriberID == $SubscriberIDSMS2, $message . ' id first only');
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients, TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE, TRUE);
    $this->assertTrue($AllSubscriptions['SubscriberID'] == $SubscriberIDSMS2, $message . ' with date');
    $this->assertTrue($AllSubscriptions['SubscriptionDate'] >= $now, $message . ' date');

    $message = 'RetrieveTaggedSubscribers with tagging all 2';
    $ArrayRecipients = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($OptInSubscribeTo, 4711),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );
    $ArrayRecipientsBasicUser = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($OptInSubscribeToBasicUser1, 4711),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(empty($AllSubscriptions), $message . ' count' . print_r($AllSubscriptions, 1));
    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE);
    $this->assertTrue($TaggedSubscriberID == 0, $message . ' id first only');
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE, TRUE);
    $this->assertTrue(empty($AllSubscriptions), $message . ' with date');

    $message = 'RetrieveTaggedSubscribers SMS with tagging all 2';
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser);
    $this->assertTrue(empty($AllSubscriptions), $message . ' count' . print_r($AllSubscriptions, 1));
    $TaggedSubscriberID = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE);
    $this->assertTrue($TaggedSubscriberID == 0, $message . ' id first only');
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipients, TRUE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_SMS_CAMPAIGN, $ArrayRecipientsBasicUser, TRUE, TRUE);
    $this->assertTrue(empty($AllSubscriptions), $message . ' with date');

    // test MIN + MAX of SubscriptionDate
    $message = 'RetrieveTaggedSubscribers with SubscriptionDate';

    // do some date manipulations to get different values
    $first = strtotime('-1 days');
    $last = strtotime('-1 hours');
    db_update('tagging')
      ->fields(array(
        'SubscriptionDate' => $first,
      ))
      ->condition('RelSubscriberID', $SubscriberID2)
      ->condition('RelTagID', $OptInSubscribeTo)
      ->execute();
    db_update('tagging')
      ->fields(array(
        'SubscriptionDate' => $last,
      ))
      ->condition('RelSubscriberID', $SubscriberID2)
      ->condition('RelTagID', $OptInSubscribeTo2)
      ->execute();

    // with ALL its the LAST subscription that triggered
    $ArrayRecipients = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($OptInSubscribeTo, $OptInSubscribeTo2),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );
    $ArrayRecipientsBasicUser = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($OptInSubscribeToBasicUser1, $OptInSubscribeToBasicUser2),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, FALSE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, FALSE, TRUE);
    $this->assertTrue($AllSubscriptions[$SubscriberID2.'#'.$ReferenceID]['SubscriptionDate'] == $last, $message . ' with all');

    // with ANY its the FIRST subscription that triggered
    $ArrayRecipients = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($OptInSubscribeTo, $OptInSubscribeTo2),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );
    $ArrayRecipientsBasicUser = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($OptInSubscribeToBasicUser1, $OptInSubscribeToBasicUser2),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
      'NotTaggedWith' => array(),
    );
    $AllSubscriptions = self::GetTaggedSubscribersQueryCheck($UserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipients, FALSE, TRUE);
    self::RetrieveTaggedSubscribersCheck($message, $BasicUserID, Campaigns::TRIGGER_TYPE_CAMPAIGN, $ArrayRecipientsBasicUser, FALSE, TRUE);
    $this->assertTrue($AllSubscriptions[$SubscriberID2.'#'.$ReferenceID]['SubscriptionDate'] == $first, $message . ' with all');

    /**
     * Tag Subscriber
     **/
    $message = 'Subscribers::TagSubscriber';

    // prepare tags and AR
    $TagSubscribeTo = Tag::CreateManualTag($UserID, 'an auto subscribe tag', '');
    $TagID = Tag::CreateManualTag($UserID, 'a subscribe tag', '', array('OptInSubscribeTo' => array($TagSubscribeTo)));
    $MVTagID = Tag::CreateManualTag($UserID, 'a multivalue tag', '', '', null, 1);

    $ARSubscribeTo = $this->createAR('AR SubscribeTo', $UserID, $TagSubscribeTo);
    $ARTag = $this->createAR('AR Tag', $UserID, $TagID);

    // check tagging before
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($ArrayTagging), $message . ' tagging before');
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagSubscribeTo, $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($ArrayTagging), $message . ' auto tagging before');

    // check Autoresponder before
    $ArrayTransactionalEmailsBefore = simpletest_transactional_retrieve_email([
      'RelSubscriberID' => $SubscriberID,
      'RelOwnerUserID' => $UserID,
      'RelAutoResponderID' => $ARSubscribeTo
    ], AutoresponderQueue::TABLE_NAME);
    $this->assertTrue(empty($ArrayTransactionalEmailsBefore), $message . ' ar before');

    // tag
    $ReferenceID4711 = 4711;
    $result = Subscribers::TagSubscriber($UserID, $SubscriberID, 4711, $ReferenceID, TRUE);
    $this->assertTrue(!$result, $message . ' tag 4711');
    $result = Subscribers::TagSubscriber($UserID, $SubscriberID, $TagID, $ReferenceID, TRUE);
    $this->assertTrue($result, $message . ' tag');
    $result = Subscribers::TagSubscriber($UserID, $SubscriberID, $TagID, $ReferenceID4711, TRUE);
    $result = Subscribers::TagSubscriber($UserID, $SubscriberID, $MVTagID, $ReferenceID, TRUE);
    $result = Subscribers::TagSubscriber($UserID, $SubscriberID, $MVTagID, $ReferenceID4711, TRUE);
    //run queue to trigger ARs for tagging and autotagging
    $this->subscriberQueueQueueWorker();

    // check tagging
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($ArrayTagging), "$message tagging $TagID#$ReferenceID");
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagID, $SubscriberID, $ReferenceID4711);
    $this->assertTrue(!empty($ArrayTagging), "$message tagging $TagID#$ReferenceID4711");

    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $MVTagID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($ArrayTagging), "$message tagging $MVTagID#$ReferenceID");
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $MVTagID, $SubscriberID, $ReferenceID4711);
    $this->assertTrue(!empty($ArrayTagging), "$message tagging $MVTagID#$ReferenceID4711");

    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagSubscribeTo, $SubscriberID, $ReferenceID);
    $this->assertTrue($ArrayTagging['ReferenceID'] == $ReferenceID, "$message tagging ref $TagSubscribeTo#$ReferenceID" . print_r($ArrayTagging, 1));
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagSubscribeTo, $SubscriberID, $ReferenceID4711);
    $this->assertTrue($ArrayTagging['ReferenceID'] == $ReferenceID4711, "$message tagging ref $TagSubscribeTo#$ReferenceID4711" . print_r($ArrayTagging, 1));

    $Tagging = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID, TRUE);
    $this->assertTrue(count($Tagging) == 3, "$message tagging #$ReferenceID". print_r($Tagging,1));
    $this->assertTrue(in_array($TagID, $Tagging), "$message tagging $TagID#$ReferenceID");
    $Tagging = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID4711, TRUE);
    $this->assertTrue(count($Tagging) == 3, "$message tagging #$ReferenceID4711". print_r($Tagging,1));
    $this->assertTrue(in_array($TagID, $Tagging), "$message tagging $TagID#$ReferenceID4711");
    $Tagging = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(count($Tagging) == 3, "$message tagging #$ReferenceID". print_r($Tagging,1));
    $this->assertTrue(!empty($Tagging[$TagID]), "$message tagging $TagID#$ReferenceID");
    $this->assertTrue($Tagging[$TagID]['ReferenceID'] == $ReferenceID, "$message tagging ref $TagID#$ReferenceID");

    // check for empty subscriptions by tagging
    $message = 'addReference by Subscribers::TagSubscriber';

    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => '<EMAIL>',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID8 = $Result[1];

    // subscriptions: email/ref0
    $subsciptionsCount = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelSubscriberID = $SubscriberID8")->fetchField();
    $this->assertTrue($subsciptionsCount == 1, "$message subscriptions $subsciptionsCount");
    $subreferencesCount = db_query("SELECT COUNT(*) FROM {subscription_reference} WHERE RelSubscriberID = $SubscriberID8")->fetchField();
    $this->assertTrue($subreferencesCount == 1, "$message references $subreferencesCount");

    // add a tagging for an existing subscription/reference
    Subscribers::TagSubscriber($UserID, $SubscriberID8, $TagID, $ReferenceID);

    // subscriptions: email/ref0
    $subsciptionsCount = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelSubscriberID = $SubscriberID8")->fetchField();
    $this->assertTrue($subsciptionsCount == 1, "$message subscriptions $subsciptionsCount");
    $subreferencesCount = db_query("SELECT COUNT(*) FROM {subscription_reference} WHERE RelSubscriberID = $SubscriberID8")->fetchField();
    $this->assertTrue($subreferencesCount == 1, "$message references $subreferencesCount");

    // add a tagging for a new reference
    Subscribers::TagSubscriber($UserID, $SubscriberID8, $TagID, $ReferenceID4711);

    // subscriptions: email/ref0 + empty/ref4711
    $subsciptionsCount = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelSubscriberID = $SubscriberID8")->fetchField();
    $this->assertTrue($subsciptionsCount == 2, "$message subscriptions $subsciptionsCount");
    $subreferencesCount = db_query("SELECT COUNT(*) FROM {subscription_reference} WHERE RelSubscriberID = $SubscriberID8")->fetchField();
    $this->assertTrue($subreferencesCount == 2, "$message references $subreferencesCount");

    // add a tagging for a existing reference
    Subscribers::TagSubscriber($UserID, $SubscriberID8, $MVTagID, $ReferenceID4711);

    // subscriptions: email/ref0 + empty/ref4711
    $subsciptionsCount = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelSubscriberID = $SubscriberID8")->fetchField();
    $this->assertTrue($subsciptionsCount == 2, "$message subscriptions $subsciptionsCount");
    $subreferencesCount = db_query("SELECT COUNT(*) FROM {subscription_reference} WHERE RelSubscriberID = $SubscriberID8")->fetchField();
    $this->assertTrue($subreferencesCount == 2, "$message references $subreferencesCount");

    /**
     * Untag Subscriber
     **/
    $message = 'Subscribers::UntagSubscriber';

    $TagForUntag = Tag::CreateManualTag($UserID, $message, '');

    $error = _testdata_campaigns($UserID, 1, "ar $message", TRUE, $TagForUntag, FALSE, FALSE, TRUE /* this will start the AR */);
    $this->assertFalse($error, "$message $error");

    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => '<EMAIL>',
      'OptInSubscribeTo' => $TagForUntag,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'TriggerAutoResponders' => TRUE,
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID2 = $Result[1];

    //run queue to trigger ARs
    $this->subscriberQueueQueueWorker();

    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagForUntag, $SubscriberID2, $ReferenceID);
    $this->assertTrue(!empty($ArrayTagging), $message . ' tagging before');

    $result = db_query("SELECT COUNT(*) FROM {".AutoresponderQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelSubscriberID' => $SubscriberID2
    ))->fetchField();
    $this->assertTrue($result == 1, $message .' '. AutoresponderQueue::TABLE_NAME .' before' . $result);

    Subscribers::UntagSubscriber($UserID, $SubscriberID2, $ReferenceID, $TagForUntag);
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagForUntag, $SubscriberID2, $ReferenceID);
    $this->assertTrue(empty($ArrayTagging), $message . ' tagging after');

    $result = db_query("SELECT COUNT(*) FROM {".AutoresponderQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelSubscriberID' => $SubscriberID2
    ))->fetchField();
    $this->assertTrue($result == 0, $message .' '. AutoresponderQueue::TABLE_NAME .' after');

    $message = 'Subscribers::UntagSubscribersOfTag';

    $result = Subscribers::TagSubscriber($UserID, $SubscriberID2, $TagForUntag, $ReferenceID, TRUE);
    $this->assertTrue($result, $message . ' tag');
    $this->subscriberQueueQueueWorker();

    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagForUntag, $SubscriberID2, $ReferenceID);
    $this->assertTrue(!empty($ArrayTagging), $message . ' tagging before');

    $result = db_query("SELECT COUNT(*) FROM {".AutoresponderQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelSubscriberID' => $SubscriberID2
    ))->fetchField();
    $this->assertTrue($result == 1, $message .' '. AutoresponderQueue::TABLE_NAME . ' before '.$result);

    Subscribers::UntagSubscribersOfTag($UserID, $TagForUntag);
    $ArrayTagging = Subscribers::RetrieveTagging($UserID, $TagForUntag, $SubscriberID2, $ReferenceID);
    $this->assertTrue(empty($ArrayTagging), $message . ' tagging after');

    $result = db_query("SELECT COUNT(*) FROM {".AutoresponderQueue::TABLE_NAME."} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID", array(
      ':RelOwnerUserID' => $UserID,
      ':RelSubscriberID' => $SubscriberID2
    ))->fetchField();
    $this->assertTrue($result == 0, $message .' '. AutoresponderQueue::TABLE_NAME .' after');

    // auto assign and remove tag
    // create tags including auto assign and remove tags
    $message = "Subscribers::TagSubscriber Setting up auto assignment with multi/single values: ";

    $AAUser = user_save('', ['name' => 'aauser', 'pass' => 'aapass', 'mail' => '<EMAIL>', 'status' => 1]);
    $this->assertTrue($AAUser != FALSE, $message . ' created user ');
    $AAUserID = $AAUser->uid;

    $AAListID = Lists::InsertDB(['name' => 'AAList', 'RelOwnerUserID' => $AAUserID]);
    $this->assertTrue($AAListID > 0, $message . ' created List ' . $AAListID);
    $AAList = Lists::RetrieveListByID($AAUserID, $AAListID);

    [,$AASubscriberID] = Subscribers::Subscribe(['UserID' => $AAUserID, 'EmailAddress' => '<EMAIL>', 'ListInformation' => $AAList]);
    [,$AASubscriberID2] = Subscribers::Subscribe(['UserID' => $AAUserID, 'EmailAddress' => '<EMAIL>', 'ListInformation' => $AAList]);
    [,$AASubscriberID3] = Subscribers::Subscribe(['UserID' => $AAUserID, 'EmailAddress' => '<EMAIL>', 'ListInformation' => $AAList]);
    [,$AASubscriberID4] = Subscribers::Subscribe(['UserID' => $AAUserID, 'EmailAddress' => '<EMAIL>', 'ListInformation' => $AAList]);

    $SingleValueTagIDAutoAssignment_I = Tag::CreateManualTag($AAUserID, 'a single value tag for auto assignment and removal I', '', '', NULL, 0);
    $SingleValueTagAutoAssignment_I = Tag::RetrieveTag($AAUserID, $SingleValueTagIDAutoAssignment_I, TRUE);

    $SingleValueTagIDAutoAssignment_II = Tag::CreateManualTag($AAUserID, 'a single value tag for auto assignment and removal II', '', '', NULL, 0);
    $SingleValueTagAutoAssignment_II = Tag::RetrieveTag($AAUserID, $SingleValueTagIDAutoAssignment_II, TRUE);

    $SingleValueTagID = Tag::CreateManualTag($AAUserID,'a single value tag','', ["OptInSubscribeTo" => [$SingleValueTagIDAutoAssignment_I, $SingleValueTagIDAutoAssignment_II]], NULL, 0);
    $SingleValueTag = Tag::RetrieveTag($AAUserID, $SingleValueTagID, TRUE);

    $MultiValueTagIDAutoAssignment_I = Tag::CreateManualTag($AAUserID, 'a multi value tag for auto assignment and removal I', '','','', 1);
    $MultiValueTagAutoAssignment_I = Tag::RetrieveTag($AAUserID, $MultiValueTagIDAutoAssignment_I, TRUE);

    $MultiValueTagIDAutoAssignment_II = Tag::CreateManualTag($AAUserID, 'a multi value tag for auto assignment and removal II', '','','', 1);
    $MultiValueTagAutoAssignment_II = Tag::RetrieveTag($AAUserID, $MultiValueTagIDAutoAssignment_II, TRUE);

    $MultiValueTagID = Tag::CreateManualTag($AAUserID, 'a multi value tag', '', ["OptInSubscribeTo" => [$MultiValueTagIDAutoAssignment_I, $MultiValueTagIDAutoAssignment_II]],'', 1);
    $MultiValueTag = Tag::RetrieveTag($AAUserID, $MultiValueTagID, TRUE);

    $this->assertTrue(empty($SingleValueTagAutoAssignment_I['MultiValue']), $message . "first assignment tag is single value");
    $this->assertTrue(empty($SingleValueTagAutoAssignment_II['MultiValue']), $message . "second assignment tag is single value");
    $this->assertTrue($MultiValueTagAutoAssignment_I['MultiValue'], $message . "third assignment tag is multi value");
    $this->assertTrue($MultiValueTagAutoAssignment_II['MultiValue'], $message . "fourth assignment tag is multi value");

    $this->assertTrue(empty($SingleValueTag['MultiValue']), $message . "single value tag is single value");
    $this->assertTrue($MultiValueTag['MultiValue'], $message . "multi value tag is multi value");

    // single value tag --> single value tag
    $message = 'Subscribers::TagSubscriber [with auto assign: single value -> single value]: ';
    $ReferenceIDSV = 0;

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);

    $this->assertTrue(count($tags) === 0, $message .'no taggings before');

    Subscribers::TagSubscriber($AAUserID, $AASubscriberID, $SingleValueTagID, $ReferenceIDSV);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);

    $this->assertTrue(count($tags) === 3, $message .'3 tags after tagging the subscriber with source tag');
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) === 3, $message .'3 taggings after tagging the subscriber with source tag');
    $this->assertTrue(in_array($SingleValueTagID, array_unique(array_keys($tags))), $message .' subscriber tagged with source tag');
    $this->assertTrue(in_array($SingleValueTagIDAutoAssignment_I, array_unique(array_keys($tags))), $message .' auto assigned tag I is correct');
    $this->assertTrue(in_array($SingleValueTagIDAutoAssignment_II, array_unique(array_keys($tags))), $message .' auto assigned tag II is correct');
    $this->assertTrue(array_sum(array_map(function($item) {return $item[0];},array_values($tags))) === $ReferenceIDSV, $message .' all reference IDs are 0');

    Subscribers::UntagSubscriber($AAUserID, $AASubscriberID, $ReferenceIDSV, $SingleValueTagID);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) == 2, $message . 'untagged single value correctly');
    $this->assertTrue(in_array($SingleValueTagIDAutoAssignment_I, array_unique(array_keys($tags))), $message .' auto assigned tag I is correct');
    $this->assertTrue(in_array($SingleValueTagIDAutoAssignment_II, array_unique(array_keys($tags))), $message .' auto assigned tag II is correct');

    Tag::UpdateTag(array_merge($SingleValueTag, ["TagData" => ['OptInSubscribeTo' => [], 'OptInUnsubscribeFrom' => [$SingleValueTagIDAutoAssignment_I, $SingleValueTagIDAutoAssignment_II]]]));
    Subscribers::TagSubscriber($AAUserID, $AASubscriberID, $SingleValueTagID, $ReferenceIDSV);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) == 1, $message . 'untagged assigned single values correctly');
    $this->assertTrue(in_array($SingleValueTagID, array_unique(array_keys($tags))), $message .' subscriber tagged only with source tag');

    Subscribers::UntagSubscriber($AAUserID, $AASubscriberID, $ReferenceIDSV, $SingleValueTagID);

    // multi value tag --> multi value tag
    $message = 'Subscribers::TagSubscriber [with auto assign: multi value -> multi value]: ';
    $ReferenceIDMV = 42;

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(count($tags) === 0, $message . "no taggings on start");

    Subscribers::TagSubscriber($AAUserID, $AASubscriberID, $MultiValueTagID, $ReferenceIDMV);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(count($tags) === 3, $message . "3 tags after tagging with auto assignment");
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) === 3, $message . "3 taggings after tagging with auto assignment");
    $this->assertTrue(in_array($MultiValueTagID, array_unique(array_keys($tags))), $message .' subscriber tagged with source tag');
    $this->assertTrue(in_array($MultiValueTagIDAutoAssignment_I, array_unique(array_keys($tags))), $message .' auto assigned tag I is correct');
    $this->assertTrue(in_array($MultiValueTagIDAutoAssignment_II, array_unique(array_keys($tags))), $message .' auto assigned tag II is correct');
    $this->assertTrue($tags[$MultiValueTagID][0] == $ReferenceIDMV, $message .'source tagging has reference ID '.$ReferenceIDMV);
    $this->assertTrue($tags[$MultiValueTagIDAutoAssignment_I][0] == $ReferenceIDMV, $message .'first auto assigned tag has reference ID '.$ReferenceIDMV);
    $this->assertTrue($tags[$MultiValueTagIDAutoAssignment_II][0] == $ReferenceIDMV, $message .'second auto assigned tag has reference ID '.$ReferenceIDMV);

    Subscribers::UntagSubscriber($AAUserID, $AASubscriberID, $ReferenceIDMV, $MultiValueTagID);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) == 2, $message . 'untagged multi value correctly');
    $this->assertTrue(in_array($MultiValueTagIDAutoAssignment_I, array_unique(array_keys($tags))), $message .' auto assigned tag I is correct');
    $this->assertTrue(in_array($MultiValueTagIDAutoAssignment_II, array_unique(array_keys($tags))), $message .' auto assigned tag II is correct');

    Tag::UpdateTag(array_merge($MultiValueTag, ["TagData" => ['OptInSubscribeTo' => [], 'OptInUnsubscribeFrom' => [$MultiValueTagIDAutoAssignment_I, $MultiValueTagIDAutoAssignment_II]]]));
    Subscribers::TagSubscriber($AAUserID, $AASubscriberID, $MultiValueTagID, $ReferenceIDMV);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) == 1, $message . 'untagged assigned multi values correctly');
    $this->assertTrue(in_array($MultiValueTagID, array_unique(array_keys($tags))), $message .' subscriber tagged only with source tag');

    Subscribers::UntagSubscriber($AAUserID, $AASubscriberID, $ReferenceIDMV, $MultiValueTagID);

    // single value tag --> multi value tag
    $message = 'Subscribers::TagSubscriber [with auto assign: single value -> multi value]: ';
    $ReferenceIDMV_II = Reference::InsertDB(['RelOwnerUserID' => $AAUserID, 'NumberRange' => 'simpletests', 'ExtReferenceID' => 'simpletests_81']);

    // merge creates a new (merge) reference, related to $AASubscriberID
    Subscribers::merge($AAUserID, $AASubscriberID, $AASubscriberID2);
    $ReferenceIDMV_III = $ReferenceIDMV_II + 1;

    // merge creates a new (merge) reference, NOT related to $AASubscriberID
    Subscribers::merge($AAUserID, $AASubscriberID3, $AASubscriberID4);
    $ReferenceIDMV_IV = $ReferenceIDMV_III + 1;

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(count($tags) === 0, $message . "no taggings on start");

    Tag::UpdateTag(array_merge($SingleValueTag, ["TagData" => ['OptInSubscribeTo' => [$MultiValueTagIDAutoAssignment_I, $MultiValueTagIDAutoAssignment_II], 'OptInUnsubscribeFrom' => []]]));

    Subscribers::TagSubscriber($AAUserID, $AASubscriberID, $SingleValueTagID, $ReferenceIDSV);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(count($tags) === 3, $message . "3 tags after tagging with auto assignment");
    $this->assertTrue(array_sum(array_map('count', $tags)) === 7, $message . "7 taggingss after tagging with auto assignment");
    $this->assertTrue(isset($tags[$SingleValueTagID]), $message .' subscriber tagged with source tag');
    $this->assertTrue(isset($tags[$MultiValueTagIDAutoAssignment_I]), $message .' auto assigned tag I is correct');
    $this->assertTrue(isset($tags[$MultiValueTagIDAutoAssignment_II]), $message .' auto assigned tag II is correct');
    $this->assertTrue($tags[$SingleValueTagID][0] == $ReferenceIDSV, $message .'source tagging has reference ID '.$ReferenceIDSV);
    $this->assertTrue($tags[$MultiValueTagIDAutoAssignment_I][0] == 0, $message .'first auto assigned tag has default reference ID 0');
    $this->assertTrue($tags[$MultiValueTagIDAutoAssignment_I][1] == $ReferenceIDMV_II, $message .'first auto assigned tag has reference ID '.$ReferenceIDMV_II);
    $this->assertTrue($tags[$MultiValueTagIDAutoAssignment_I][2] == $ReferenceIDMV_III, $message .'first auto assigned tag has reference ID '.$ReferenceIDMV_III);
    $this->assertFalse(in_array($ReferenceIDMV_IV, $tags[$MultiValueTagIDAutoAssignment_I]), $message .'no tagging with first tag  for unrelated merge reference ID '.$ReferenceIDMV_IV);
    $this->assertTrue($tags[$MultiValueTagIDAutoAssignment_II][0] == 0, $message .'second auto assigned tag has default reference ID 0');
    $this->assertTrue($tags[$MultiValueTagIDAutoAssignment_II][1] == $ReferenceIDMV_II, $message .'second auto assigned tag has reference ID '.$ReferenceIDMV_II);
    $this->assertTrue($tags[$MultiValueTagIDAutoAssignment_II][2] == $ReferenceIDMV_III, $message .'second auto assigned tag has reference ID '.$ReferenceIDMV_III);
    $this->assertFalse(in_array($ReferenceIDMV_IV, $tags[$MultiValueTagIDAutoAssignment_II]), $message .'no tagging with second tag  for unrelated merge reference ID '.$ReferenceIDMV_IV);

    Subscribers::UntagSubscriber($AAUserID, $AASubscriberID, $ReferenceIDSV, $SingleValueTagID);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) == 6, $message . 'untagged multi value correctly');
    $this->assertTrue(in_array($MultiValueTagIDAutoAssignment_I, array_unique(array_keys($tags))), $message .' auto assigned tag I is correct');
    $this->assertTrue(in_array($MultiValueTagIDAutoAssignment_II, array_unique(array_keys($tags))), $message .' auto assigned tag II is correct');

    Tag::UpdateTag(array_merge($SingleValueTag, ["TagData" => ['OptInSubscribeTo' => [], 'OptInUnsubscribeFrom' => [$MultiValueTagIDAutoAssignment_I, $MultiValueTagIDAutoAssignment_II]]]));
    Subscribers::TagSubscriber($AAUserID, $AASubscriberID, $SingleValueTagID, $ReferenceIDSV);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) == 1, $message . 'untagged assigned multi values correctly');
    $this->assertTrue(in_array($SingleValueTagID, array_unique(array_keys($tags))), $message .' subscriber tagged only with source tag');

    Subscribers::UntagSubscriber($AAUserID, $AASubscriberID, $ReferenceIDSV, $SingleValueTagID);

    // multi value tag --> single value tag
    $message = 'Subscribers::TagSubscriber [with auto assign: multi value -> single value]: ';
    $ReferenceIDMV_V = 84;

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(count($tags) === 0, $message . "no taggings on start");

    Tag::UpdateTag(array_merge($MultiValueTag, ["TagData" => ['OptInSubscribeTo' => [$SingleValueTagIDAutoAssignment_I, $SingleValueTagIDAutoAssignment_II], 'OptInUnsubscribeFrom' => []]]));

    Subscribers::TagSubscriber($AAUserID, $AASubscriberID, $MultiValueTagID, $ReferenceIDMV_V);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(count($tags) === 3, $message . "3 tags after tagging with auto assignment");
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) === 3, $message . "3 taggings after tagging with auto assignment");
    $this->assertTrue(in_array($MultiValueTagID, array_unique(array_keys($tags))), $message .' subscriber tagged with source tag');
    $this->assertTrue(in_array($SingleValueTagIDAutoAssignment_I, array_unique(array_keys($tags))), $message .' auto assigned tag I is correct');
    $this->assertTrue(in_array($SingleValueTagIDAutoAssignment_II, array_unique(array_keys($tags))), $message .' auto assigned tag II is correct');
    $this->assertTrue($tags[$MultiValueTagID][0] == $ReferenceIDMV_V, $message .'source tagging has reference ID '.$ReferenceIDMV_V);
    $this->assertTrue($tags[$SingleValueTagIDAutoAssignment_I][0] == $ReferenceIDSV, $message .'first auto assigned tag has reference ID '.$ReferenceIDSV);
    $this->assertTrue($tags[$SingleValueTagIDAutoAssignment_II][0] == $ReferenceIDSV, $message .'second auto assigned tag has reference ID '.$ReferenceIDSV);

    Subscribers::UntagSubscriber($AAUserID, $AASubscriberID, $ReferenceIDMV_V, $MultiValueTagID);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) == 2, $message . 'untagged multi value correctly');
    $this->assertTrue(in_array($SingleValueTagIDAutoAssignment_I, array_unique(array_keys($tags))), $message .' auto assigned tag I is correct');
    $this->assertTrue(in_array($SingleValueTagIDAutoAssignment_II, array_unique(array_keys($tags))), $message .' auto assigned tag II is correct');

    Tag::UpdateTag(array_merge($MultiValueTag, ["TagData" => ['OptInSubscribeTo' => [], 'OptInUnsubscribeFrom' => [$SingleValueTagIDAutoAssignment_I, $SingleValueTagIDAutoAssignment_II]]]));
    Subscribers::TagSubscriber($AAUserID, $AASubscriberID, $MultiValueTagID, $ReferenceIDMV_V);

    $tags = $this->getTagsAndReferences($AAUserID, $AASubscriberID);
    $this->assertTrue(array_sum(array_map(function($item) {return count($item);},array_values($tags))) == 1, $message . 'untagged assigned multi values correctly');
    $this->assertTrue(in_array($MultiValueTagID, array_unique(array_keys($tags))), $message .' subscriber tagged only with source tag');

    Subscribers::UntagSubscriber($AAUserID, $AASubscriberID, $ReferenceIDMV_V, $MultiValueTagID);

    /**
     * Update subscription
     */
    $EmailAddress1 = '<EMAIL>';
    $EmailAddress2 = '<EMAIL>';
    $EmailAddress3 = '<EMAIL>';

    $message = 'Subscribers::UpdateSubscription single optin';
    $ImportList = array(
      'Name' => 'import',
      'ListID' => 0,
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
    );
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ImportList,
      'EmailAddress' => $EmailAddress1,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'IPAddress' => '0.0.0.0 import',
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID = $Result[1];

    $ArrayFieldAndValues = array(
      'EmailAddress' => $EmailAddress2,
    );
    $result = Subscribers::UpdateSubscription($UserID, $SubscriberID, $ArrayFieldAndValues);
    $this->assertTrue($result['Success'], $message . ' update subscriber'.print_r($result,1));
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID);
    $this->assertTrue($Subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, $message . ' status'.print_r($Subscription,1));
    $this->assertTrue($Subscription['ContactInfo'] == $EmailAddress2, $message . ' EmailAddress');

    $message = 'Subscribers::UpdateSubscription double optin';
    $FieldsForNoImportList = array(
      'Name' => 'no import',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE,
    );
    $NoImportListID = Lists::InsertDB($FieldsForNoImportList);
    $this->assertTrue($NoImportListID > 0, $message . ' create list');
    $NoImportList = Lists::RetrieveListByID($UserID, $NoImportListID);
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $NoImportList,
      'EmailAddress' => $EmailAddress1,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'IPAddress' => '127.0.0.1',
    );
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SubscriberID = $Result[1];
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID);
    $this->assertTrue($Subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $message . ' status');

    $ArrayFieldAndValues = array(
      'EmailAddress' => $EmailAddress2,
    );
    $result = Subscribers::UpdateSubscription($UserID, $SubscriberID, $ArrayFieldAndValues);
    $this->assertTrue(!$result['Success'], $message . ' update subscriber duplicate email');
    $this->assertTrue($result['ErrorCode'] == KLICKTIPPAPI_ERROR_EMAILADDRESS_EXISTS, $message . ' update subscriber duplicate email error');

    $ArrayFieldAndValues = array(
      'EmailAddress' => $EmailAddress3,
    );
    $result = Subscribers::UpdateSubscription($UserID, $SubscriberID, $ArrayFieldAndValues);
    $this->assertTrue($result['Success'], $message . ' update subscriber');
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID);
    $this->assertTrue($Subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, $message . ' status');
    $this->assertTrue($Subscription['ContactInfo'] == $EmailAddress3, $message . ' EmailAddress');

    $EmailOptional = '<EMAIL>';
    $EmailPrimary  = '<EMAIL>';
    $EmailOptionalChanged = '<EMAIL>';

    $SmsNumberOptional = '001789376111';
    $SmsNumberPrimary  = '001789376222';
    $SmsNumberOptionalChanged = '001789376333';


    $message = 'Subscribers::UpdateSubscription can update non primary subscription';

    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ImportList,
      'EmailAddress' => $EmailOptional,
      'PhoneNumber' => $SmsNumberOptional,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'IPAddress' => '0.0.0.0 import',
    );
    [, $subscriberID] = Subscribers::Subscribe($Parameters);
    $Parameters['SubscriberID'] = $subscriberID;
    $Parameters['EmailAddress'] = $EmailPrimary;
    $Parameters['PhoneNumber'] = $SmsNumberPrimary;
    Subscribers::Subscribe($Parameters);

    $emailSubscriptions = Subscription::getReferenceSubscriptionRelations($UserID, $subscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, 0);
    $emailSubscriptions = array_column($emailSubscriptions, 'Optional', 'ContactInfo');

    $smsSubscriptions = Subscription::getReferenceSubscriptionRelations($UserID, $subscriberID, Subscription::SUBSCRIPTIONTYPE_SMS, 0);
    $smsSubscriptions = array_column($smsSubscriptions, 'Optional', 'ContactInfo');

    $this->assertEqual($emailSubscriptions, [$EmailOptional => '1', $EmailPrimary => '0']);
    $this->assertEqual($smsSubscriptions, [$SmsNumberOptional => '1', $SmsNumberPrimary => '0']);

    Subscribers::UpdateSubscription(
      $UserID, $subscriberID,
      [
        'EmailAddress' => $EmailOptionalChanged, 'OldEmailAddress' => $EmailOptional,
        'PhoneNumber' => $SmsNumberOptionalChanged, 'OldPhoneNumber' => $SmsNumberOptional
      ]
    );

    $emailSubscriptions = Subscription::getReferenceSubscriptionRelations($UserID, $subscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, 0);
    $emailSubscriptions = array_column($emailSubscriptions, 'Optional', 'ContactInfo');

//    Subscribers::UpdateSubscription($UserID, $subscriberID, ['PhoneNumber' => $SmsNumberOptionalChanged, 'OldPhoneNumber' => $SmsNumberOptional]);

    $smsSubscriptions = Subscription::getReferenceSubscriptionRelations($UserID, $subscriberID, Subscription::SUBSCRIPTIONTYPE_SMS, 0);
    $smsSubscriptions = array_column($smsSubscriptions, 'Optional', 'ContactInfo');

    $this->assertEqual($emailSubscriptions, [$EmailOptionalChanged => '1', $EmailPrimary => '0'], 'non primary email (specified by "OldEmailAddress") was change ');
    $this->assertEqual($smsSubscriptions, [$SmsNumberOptionalChanged => '1', $SmsNumberPrimary => '0'], 'non primary sms number (specified by "OldEmailAddress") was change ');

    /**
     * Unsubscribe the subscriber from the subscriber list
     */

    // prepare data
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE,
      'UpdateIfDuplicate' => TRUE,
      'UpdateIfUnsubscribed' => TRUE,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    );

    // create campaign
    $ArrayFieldAndValues = array(
      'CampaignName' => 'Subscribers::UnsubscribeSMSNumber',
      'RelOwnerUserID' => $UserID,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_SUBSCRIPTION,
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($OptInSubscribeTo5),
      'CampaignStatusEnum' => Campaigns::STATUS_SENDING,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    );
    $CampaignID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($CampaignID > 0, $message . ' campaign');

    // unsubscribe non existing
    $message = 'Subscribers::Unsubscribe non existing';
    $EmailAddress = '<EMAIL>';
    $Result = Subscribers::Unsubscribe($UserID, $EmailAddress);
    $this->assertFalse($Result[0], $message . ' unsubscribe');
    $this->assertTrue($Result[1] == KLICKTIPPAPI_ERROR_SUBSCRIBER_EMAIL_NOT_FOUND, $message . ' unsubscribe reason');

    // unsubscribe optin
    $message = 'Subscribers::Unsubscribe optin';
    $EmailAddress = '<EMAIL>';
    $Parameters['EmailAddress'] = $EmailAddress;
    $Parameters['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_OPTIN;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $Result = Subscribers::Unsubscribe($UserID, $EmailAddress);
    $this->assertFalse($Result[0], $message . ' unsubscribe');
    $this->assertTrue($Result[1] == KLICKTIPPAPI_ERROR_SUBSCRIBER_NOT_SUBSCRIBED, $message . ' unsubscribe reason');

    // unsubscribe subscribed
    $message = 'Subscribers::Unsubscribe subscribed';
    $now = time();
    $EmailAddress = '<EMAIL>';
    $Parameters['EmailAddress'] = $EmailAddress;
    $Parameters['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $Parameters['OptInSubscribeTo'] = $OptInSubscribeTo5;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $UnSubscriberID = $Result[1];

    //run queue to trigger ARs
    $this->subscriberQueueQueueWorker();

    $before = db_query("SELECT COUNT(*) FROM {".AutoresponderQueue::TABLE_NAME."} WHERE RelSubscriberID = :SubscriberID",
      array(':SubscriberID' => $UnSubscriberID))->fetchField();
    $this->assertTrue($before == 1, $message . ' count before'.$before);

    $Subscription = Subscription::RetrieveSubscriptionAndReferenceByContactInfo($UserID, $EmailAddress, Subscription::SUBSCRIPTIONTYPE_EMAIL);
    $this->assertTrue(!empty($Subscription), $message . ' subscribed subscription');
    $this->assertTrue($Subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $message . ' subscribed SubscriptionStatus');
    $this->assertTrue($Subscription['UnsubscriptionDate'] == 0, $message . ' subscribed UnsubscriptionDate');

    // right listid
    $Result = Subscribers::Unsubscribe($UserID, $EmailAddress, $CampaignID, '*******');
    $this->assertTrue($Result[0], $message . ' unsubscribe' . print_r($Result, 1));

    $Subscription = Subscription::RetrieveSubscriptionAndReferenceByContactInfo($UserID, $EmailAddress, Subscription::SUBSCRIPTIONTYPE_EMAIL);
    $this->assertTrue(!empty($Subscription), $message . ' unsubscribed subscription');
    $this->assertTrue($Subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, $message . ' status');
    $this->assertTrue($Subscription['LastOpenDate'] >= $now, $message . ' LastOpenDate');
    $this->assertTrue($Subscription['UnsubscriptionDate'] >= $now, $message . ' UnsubscriptionDate');
    $this->assertTrue($Subscription['UnsubscriptionIP'] == '*******', $message . ' UnsubscriptionIP');

    $after = db_query("SELECT COUNT(*) FROM {".TransactionalQueue::TABLE_NAME."} WHERE RelSubscriberID = :SubscriberID",
      array(':SubscriberID' => $UnSubscriberID))->fetchField();
    $this->assertTrue($after == 0, $message . ' count after');

    // check stats
    $ObjectCampaign = Campaigns::FromID($UserID, $CampaignID);
    $this->assertTrue($ObjectCampaign->GetData('UniqueOpens') == 1, $message . ' UniqueOpens');
    $this->assertTrue($ObjectCampaign->GetData('TotalOpens') == 1, $message . ' TotalOpens');
    $this->assertTrue($ObjectCampaign->GetData('TotalUnsubscriptions') == 1, $message . ' TotalUnsubscriptions');

    //TODO
    // test UpdateListActivityStatistics

    /**
     * Re-subscribe the subscriber
     */

    $ReSubscriberID = $UnSubscriberID; // from before

    $before = db_query("SELECT COUNT(*) FROM {".TransactionalQueue::TABLE_NAME."} WHERE RelSubscriberID = :SubscriberID",
      array(':SubscriberID' => $ReSubscriberID))->fetchField();
    $this->assertTrue($before == 0, $message . ' count before'.$before);

    // re-subscribe unsubscribed
    $message = 'Subscribers::Subscribe re-subscribe';
    $Parameters['SubscriberID'] = $ReSubscriberID;
    $Parameters['SubscriptionStatus'] = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' re-subscribe');

    $afterre = db_query("SELECT COUNT(*) FROM {".AutoresponderQueue::TABLE_NAME."} WHERE RelSubscriberID = :SubscriberID",
      array(':SubscriberID' => $ReSubscriberID))->fetchField();
    $this->assertTrue($afterre == 1, $message . ' count after re-sub '.$afterre);

    /**
     * SMS Unsubscribe
     */

    // prepare data
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => TRUE,
      'UpdateIfDuplicate' => TRUE,
      'UpdateIfUnsubscribed' => TRUE,
      'OptInSubscribeTo' => $OptInSubscribeTo5,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    );

    // create campaign
    $ArrayFieldAndValues = array(
      'CampaignName' => 'Subscribers::UnsubscribeSMSNumber',
      'RelOwnerUserID' => $UserID,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_SMS_SUBSCRIPTION,
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($OptInSubscribeTo5),
      'CampaignStatusEnum' => Campaigns::STATUS_SENDING,
      'ScheduleTypeEnum' => Campaigns::SCHEDULE_TYPE_IMMEDIATE,
    );
    $CampaignID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($CampaignID > 0, $message . ' campaign');

    // unsubscribe non existing
    $message = 'Subscribers::UnsubscribeSMSNumber non existing';
    $PhoneNumber = '0041111101';
    $Result = Subscribers::UnsubscribeSMSNumber($UserID, $PhoneNumber);
    $this->assertFalse($Result[0], $message . ' unsubscribe');
    $this->assertTrue($Result[1] == Subscribers::SUBSCRIBE_ERROR_INVALID_PHONE_NUMBER, $message . ' unsubscribe reason');

    // unsubscribe not subscribed
    $message = 'Subscribers::UnsubscribeSMSNumber not subscribed';
    $PhoneNumber = '0041111102';
    $Parameters['PhoneNumber'] = $PhoneNumber;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    db_query("UPDATE {subscription} SET SubscriptionStatus = :SubscriptionStatus WHERE RelSubscriberID = :RelSubscriberID AND SubscriptionType = :SubscriptionType",
      array(
        ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED,
        ':RelSubscriberID' => $Result[1],
        ':SubscriptionType' => Subscription::SUBSCRIPTIONTYPE_SMS
      ));
    $Result = Subscribers::UnsubscribeSMSNumber($UserID, $PhoneNumber);
    $this->assertFalse($Result[0], $message . ' unsubscribe');
    $this->assertTrue($Result[1] == KLICKTIPPAPI_ERROR_SUBSCRIBER_NOT_SUBSCRIBED, $message . ' unsubscribe reason');

    // unsubscribe subscribed
    $message = 'Subscribers::UnsubscribeSMSNumber subscribed';
    $now = time();
    $PhoneNumber = '0041111103';
    $Parameters['PhoneNumber'] = $PhoneNumber;
    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber'.print_r($Result,1));
    $UnSubscriberID = $Result[1];

    //run queue to trigger ARs
    $this->subscriberQueueQueueWorker();

    $before = db_query("SELECT COUNT(*) FROM {".AutoresponderQueue::TABLE_NAME."} WHERE RelSubscriberID = :SubscriberID",
      array(':SubscriberID' => $UnSubscriberID))->fetchField();
    $this->assertTrue($before == 1, $message . ' count before'.$before);

    $Subscription = Subscription::RetrieveSubscriptionAndReferenceByContactInfo($UserID, $PhoneNumber, Subscription::SUBSCRIPTIONTYPE_SMS);
    $this->assertTrue(!empty($Subscription), $message . ' subscribed subscription');
    $this->assertTrue($Subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $message . ' subscribed SubscriptionStatus');
    $this->assertTrue($Subscription['UnsubscriptionDate'] == 0, $message . ' subscribed UnsubscriptionDate');

    $Result = Subscribers::UnsubscribeSMSNumber($UserID, $PhoneNumber);
    $this->assertTrue($Result[0], $message . ' unsubscribe');

    $Subscription = Subscription::RetrieveSubscriptionAndReferenceByContactInfo($UserID, $PhoneNumber, Subscription::SUBSCRIPTIONTYPE_SMS);
    $this->assertTrue(!empty($Subscription), $message . ' unsubscribed subscription');
    $this->assertTrue($Subscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, $message . ' unsubscribed SubscriptionStatus');
    $this->assertTrue($Subscription['UnsubscriptionDate'] >= $now, $message . ' unsubscribed UnsubscriptionDate');

    $after = db_query("SELECT COUNT(*) FROM {".TransactionalQueue::TABLE_NAME."} WHERE RelSubscriberID = :SubscriberID",
      array(':SubscriberID' => $UnSubscriberID))->fetchField();
    $this->assertTrue($after == 0, $message . ' count after');

    /**
     * Selects a random subscriber from the provided list. If there is no subscriber, returns a dummy subscriber information
     **/
    $message = 'Subscribers::SelectRandomSubscriber';
    // get random subscriber
    $ArrayRecipients = array(
      'OpTaggedWith' => Campaigns::TAG_HAS_ALL,
      'TaggedWith' => array($OptInSubscribeTo, $OptInSubscribeTo2),
      'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ALL,
      'NotTaggedWith' => array(),
    );
    $FullSubscriber = Subscription::SelectRandomSubscription(4711, $ArrayRecipients);
    $account = user_load(4711);
    $this->assertTrue($FullSubscriber['ContactInfo'] == $account->mail, $message . ' random from empty list');
    $FullSubscriber = Subscription::SelectRandomSubscription($UserID, $ArrayRecipients);
    $this->assertTrue(in_array($FullSubscriber['ContactInfo'], [$Test2EmailAddress, $SMSEmailAddress2]), $message . ' random');
    $this->assertTrue($FullSubscriber['CustomField' . $UserCustomFieldInformation['CustomFieldID']] == $UserFieldValue, $message . ' random firstname');
    // by email or phone
    $FullSubscriber = Subscription::SelectRandomSubscription($UserID, [], $SMSEmailAddress);
    $this->assertTrue($FullSubscriber['ContactInfo'] == $SMSEmailAddress, $message . ' by email');
    $FullSubscriber = Subscription::SelectRandomSubscription($UserID, [], $SMSPhonenumber, Subscription::SUBSCRIPTIONTYPE_SMS);
    $this->assertTrue($FullSubscriber['ContactInfo'] == $SMSPhonenumber, $message . ' by phone');
    // any email or phone with right tagging
    $FullSubscriber = Subscription::SelectRandomSubscription($UserID, $ArrayRecipients, '', Subscription::SUBSCRIPTIONTYPE_EMAIL);
    $this->assertTrue(in_array($FullSubscriber['ContactInfo'], [$Test2EmailAddress, $SMSEmailAddress2]), $message . ' random email');
    $FullSubscriber = Subscription::SelectRandomSubscription($UserID, $ArrayRecipients, '', Subscription::SUBSCRIPTIONTYPE_SMS);
    $this->assertTrue($FullSubscriber['ContactInfo'] == $SMSPhonenumber2, $message . ' random phone');
    // get dummy subscriber from non-empty list
    $FullSubscriber = Subscription::SelectRandomSubscription($UserID, [], '', 4711);
    $account = user_load($UserID);
    $this->assertTrue($FullSubscriber['ContactInfo'] == $account->mail, $message . ' dummy from non-empty list');
    $this->assertTrue($FullSubscriber['CustomField' . $UserCustomFieldInformation['CustomFieldID']] == 'This is a single-line string', $message . ' random firstname');


    /**
     * Removes subscribers with given email addresses
     **/
    $message = 'Subscribers::RemoveSubscribersByEmailAddresses';
    // this tests RemoveSubscribersByID + LogSubscriberDeletion

    // remember existing files. later there should be 1 more file
    $logDir = Core::GetSubscriberDeletionLogDir();
    $filesBefore = scandir($logDir);

    $FirstName = "Max";
    $LastName = "Mustermann";
    $EmailAddressRestore = "<EMAIL>";
    $ReferenceID42 = 42;
    $Parameters = array(
      'UserID' => $UserID,
      'SubscriberID' => $SubscriberID,
      'ReferenceID' => $ReferenceID42,
      'ListInformation' => array('ListID' => 0),
      'EmailAddress' => $EmailAddressRestore,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OtherFields' => array(
        'CustomFieldFirstName' => $FirstName,
        'CustomFieldLastName' => $LastName,
      )
    );

    // adding 2nd subscription and custom fields
    Subscribers::Subscribe($Parameters);

    $FieldInformationFirstName = CustomFields::RetrieveCustomField("FirstName");
    $FieldInformationLastName = CustomFields::RetrieveCustomField("LastName");

    // create campaign
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'CampaignName' => 'Test campaign 1',
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_CAMPAIGN,
    );
    $CampaignID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($CampaignID > 0, $message . ' campaign');
    $ArrayCampaign = Campaigns::RetrieveCampaignByID($CampaignID, $UserID);

    // create tag
    $TagID = Tag::CreateManualTag($UserID, 'yatag', '');
    $this->assertTrue($TagID > 0, $message . ' tag');

    // prepare subscriber
    $this->assertTrue($SubscriberID > 0, $message . ' subscriberid');
    $result = Subscribers::TagSubscriber($UserID, $SubscriberID, $TagID, $ReferenceID, TRUE);
    $this->assertTrue($result, $message . ' tag subscriber');
    $examplehistory = "example history with chars to escape '*\"{subscribers}'";
    Subscribers::WriteHistory($UserID, $SubscriberID, Subscribers::HISTORY_TEXT, $examplehistory);
    TransactionEmails::RegisterIntoQueue($UserID, $SubscriberID, time(), $ArrayCampaign, $ReferenceID);

    // check before - Reference 0
    $FullSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($FullSubscriber), $message . ' subscriber');
    $CustomFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationFirstName, $ReferenceID);
    $this->assertTrue($CustomFieldValue == $FirstName, $message .' first name');
    $CustomFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationLastName, $ReferenceID);
    $this->assertTrue($CustomFieldValue == $LastName, $message .' last name');
    $Tagging = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(!empty($Tagging), $message . ' tagging');
    $History = Subscribers::ReadHistory($UserID, $SubscriberID);
    $this->assertTrue(!empty($History), $message . ' history');
    $TransEmails = simpletest_transactional_retrieve_emails(
      ['*'],
      [
        'RelOwnerUserID' => $UserID,
        'RelSubscriberID' => $SubscriberID,
        'ReferenceID' => $ReferenceID,
        'RelAutoResponderID' => $CampaignID
      ],
      NewsletterQueue::TABLE_NAME
    );
    $this->assertTrue(!empty($TransEmails), $message . ' queue');

    // check before - Reference 42
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID42);
    $this->assertTrue($Subscription['ContactInfo'] == $EmailAddressRestore, $message .' additional subscription email address (Ref 42)');

    // remove
    $result = Subscribers::RemoveSubscribersByEmailAddresses($UserID, $FullSubscriber['EmailAddress']);
    $this->assertTrue($result, $message . ' result');

    // check log file
    $newFiles = array_diff(scandir($logDir), $filesBefore);
    $this->assertEqual(count($newFiles), 1,  $message . ' 1 log file created');
    $filename = $logDir . DIRECTORY_SEPARATOR . current($newFiles);

    // check after

    // reset cache
    CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationFirstName, $ReferenceID, '', TRUE);

    $SubscriberA = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($SubscriberA), $message . ' subscriber after');
    $CustomFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationFirstName, $ReferenceID);
    $this->assertTrue(empty($CustomFieldValue), $message .' first name after');
    $CustomFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationLastName, $ReferenceID);
    $this->assertTrue(empty($CustomFieldValue), $message .' last name after');
    $TaggingA = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($TaggingA), $message . ' tagging after');
    $HistoryA = Subscribers::ReadHistory($UserID, $SubscriberID);
    $this->assertTrue(strpos(print_r($HistoryA, TRUE), $examplehistory) === FALSE, $message . ' history after');
    $TransEmailsA = simpletest_transactional_retrieve_emails(
      ['*'],
      [
        'RelOwnerUserID' => $UserID,
        'RelSubscriberID' => $SubscriberID,
        'RelAutoResponderID' => $CampaignID
      ],
      NewsletterQueue::TABLE_NAME
    );
    $this->assertTrue(empty($TransEmailsA), $message . ' queue after');


    // check after - Reference 42

    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID42);
    $this->assertTrue(empty($Subscription), $message .' additional subscription email address after (Ref 42)');

    // restore
    $import = file_get_contents($filename);
    $this->assertTrue(strlen($import) > 0, $message . ' import' . $filename);
    foreach (explode(";\n", $import) as $query) {
      if (!empty($query)) { // the last one is an empty string
        $this->assertTrue(strlen($query) > 0, $message . ' query: ' . $query);
        klickitpp_coreapi_subscribers_simple_query_for_reimport($query);
      }
    }
    unlink($filename);

    // check after import

    // reset cache
    CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationFirstName, $ReferenceID, '', TRUE);

    $SubscriberA2 = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(strcmp(print_r($SubscriberA2, TRUE), print_r($FullSubscriber, TRUE)) == 0, $message . ' subscriber after import');
    $CustomFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationFirstName, $ReferenceID);
    $this->assertTrue($CustomFieldValue == $FirstName, $message .' first name after import');
    $CustomFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationLastName, $ReferenceID);
    $this->assertTrue($CustomFieldValue == $LastName, $message .' last name after import');
    $TaggingA2 = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(strcmp(print_r($TaggingA2, TRUE), print_r($Tagging, TRUE)) == 0, $message . ' tagging after import');
    $HistoryA2 = Subscribers::ReadHistory($UserID, $SubscriberID);
    $this->assertTrue(strpos(print_r($HistoryA2, TRUE), $examplehistory) > 0, $message . ' history after import' . print_r($HistoryA2, TRUE));
    $TransEmailsA2 = simpletest_transactional_retrieve_emails(
      ['*'],
      [
        'RelOwnerUserID' => $UserID,
        'RelSubscriberID' => $SubscriberID,
        'RelAutoResponderID' => $CampaignID
      ],
      NewsletterQueue::TABLE_NAME
    );
    $this->assertTrue(strcmp(print_r($TransEmailsA2, TRUE), print_r($TransEmails, TRUE)) == 0, $message . ' queue after import');

    // check after import - Reference 42
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID42);
    $this->assertTrue($Subscription['ContactInfo'] == $EmailAddressRestore, $message .' additional subscription email address after import (Ref 42)');

    // remove second time
    $result = Subscribers::RemoveSubscribersByEmailAddresses($UserID, $FullSubscriber['EmailAddress']);
    $this->assertTrue($result, $message . ' result');

    // remove campaign and tag
    Tag::DeleteTag($TagID, $UserID);
    CampaignsNewsletter::DeleteDB(array('RelOwnerUserID' => $UserID, 'CampaignID' => $CampaignID));

    // restore second time
    foreach (explode(";\n", $import) as $query) {
      if (!empty($query)) {
        klickitpp_coreapi_subscribers_simple_query_for_reimport($query);
      }
    }

    // check after import

    // reset cache
    CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationFirstName, $ReferenceID, '', TRUE);

    $SubscriberA2 = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(strcmp(print_r($SubscriberA2, TRUE), print_r($FullSubscriber, TRUE)) == 0, $message . ' subscriber after import 2');
    $CustomFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationFirstName, $ReferenceID);
    $this->assertTrue($CustomFieldValue == $FirstName, $message .' first name  after import 2');
    $CustomFieldValue = CustomFields::GetCustomFieldData($UserID, $SubscriberID, $FieldInformationLastName, $ReferenceID);
    $this->assertTrue($CustomFieldValue == $LastName, $message .' last name after import 2');
    $TaggingA2 = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue(count($TaggingA2) < count($Tagging), $message . ' tagging after import 2');
    $HistoryA2 = Subscribers::ReadHistory($UserID, $SubscriberID);
    $this->assertTrue(strpos(print_r($HistoryA2, TRUE), $examplehistory) > 0, $message . ' history after import 2');
    $TransEmailsA2 = simpletest_transactional_retrieve_emails(
      ['*'],
      [
        'RelOwnerUserID' => $UserID,
        'RelSubscriberID' => $SubscriberID
      ],
      NewsletterQueue::TABLE_NAME);
    $this->assertTrue($TransEmailsA2 == FALSE, $message . ' tagging after import 2' . print_r($TransEmailsA2, 1));

    // check after import - Reference 42
    $Subscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $SubscriberID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID42);
    $this->assertTrue($Subscription['ContactInfo'] == $EmailAddressRestore, $message .' additional subscription email address after import 2 (Ref 42)');

    //TEST CASES: Change email address by subscriber
    $message = "UpdateSubscription Prepare";
    $UserID = 1;

    $InitEmailAddressSubscribed = "<EMAIL>";
    $InitEmailAddressPending = "<EMAIL>";
    $InitEmailAddressUnsubscribed = "<EMAIL>";
    $InitEmailAddressImported = "<EMAIL>";
    $InitEmailAddressSingleoptin = "<EMAIL>";

    $ChangeEmailAddressSubscribed = "<EMAIL>";
    $ChangeEmailAddressPending = "<EMAIL>";
    $ChangeEmailAddressUnsubscribed = "<EMAIL>";
    $ChangeEmailAddressImported = "<EMAIL>";
    $ChangeEmailAddressSingleoptin = "<EMAIL>";

    $ApiEmailUpdateSubscribed = "<EMAIL>";
    $ApiEmailUpdatePending = "<EMAIL>";
    $ApiEmailUpdateUnsubscribed = "<EMAIL>";
    $ApiEmailUpdateImported = "<EMAIL>";
    $ApiEmailUpdateSingleoptin = "<EMAIL>";

    $ArrayFieldAndValues = array(
      'Name' => 'Subscribe List',
      'RelOwnerUserID' => $UserID,
    );
    $SubscribeListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($SubscribeListID > 0, "$message Subscriber list created");
    $SubscribeList = Lists::RetrieveListByID($UserID, $SubscribeListID);

    $ArrayFieldAndValues = array(
      'Name' => 'Change Email List',
      'RelOwnerUserID' => $UserID,
      'UseAsChangeEmailList' => 1,
    );
    $ChangeEmailListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($ChangeEmailListID > 0, "$message ChangeEmail list created");

    $ArrayFieldAndValues = array(
      'Name' => 'Single OptIn List',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
    );
    $SingleOptInListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($ChangeEmailListID > 0, "$message SingleOptIn list created");
    $SingleOptInList = Lists::RetrieveListByID($UserID, $SingleOptInListID);

    $ImportList = array(
      'ListID' => 0,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
    );

    // prepare subscriber status

    // subscribed step 1 -> pending
    $IPAddress = '***********';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $SubscribeList,
      'EmailAddress' => $InitEmailAddressSubscribed,
      'IPAddress' => $IPAddress,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      // this will write a confirmation email, so check existence manually
      'SendConfirmationEmail' => TRUE,
      'TriggerAutoResponders' => FALSE,
      'OtherFields' => array(),
    );

    $Result = Subscribers::Subscribe($Parameters);
    $Parameters['EmailAddress'] = $ApiEmailUpdateSubscribed;
    $Result2 = Subscribers::Subscribe($Parameters);

    // subscribed step 2 -> subscribed
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $SubscribeList,
      'EmailAddress' => $InitEmailAddressSubscribed,
      'IPAddress' => $IPAddress,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      // this will write a confirmation email, so check existence manually
      'SendConfirmationEmail' => TRUE,
      'TriggerAutoResponders' => FALSE,
      'OtherFields' => array(),
    );

    $Result = Subscribers::Subscribe($Parameters);
    $ChangeEmailSubscribedSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailSubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message $ChangeEmailAddressSubscribed subscribed");

    $Parameters['EmailAddress'] = $ApiEmailUpdateSubscribed;
    $Result = Subscribers::Subscribe($Parameters);
    $ApiEmailSubscribedSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailSubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message $ApiEmailUpdateSubscribed subscribed");

    // ---- pending ----
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $SubscribeList,
      'EmailAddress' => $InitEmailAddressPending,
      'IPAddress' => $IPAddress,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      // this will write a confirmation email, so check existence manually
      'SendConfirmationEmail' => TRUE,
      'TriggerAutoResponders' => FALSE,
      'OtherFields' => array(),
    );

    $Result = Subscribers::Subscribe($Parameters);
    $ChangeEmailPendingSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailPendingSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message $ChangeEmailAddressPending pending");

    $Parameters['EmailAddress'] = $ApiEmailUpdatePending;
    $Result = Subscribers::Subscribe($Parameters);
    $ApiEmailPendingSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailPendingSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message $ApiEmailUpdatePending pending");

    // ---- unsubscribed ----

    // unsubscribed step 1 -> pending
    $IPAddress = '***********';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $SubscribeList,
      'EmailAddress' => $InitEmailAddressUnsubscribed,
      'IPAddress' => $IPAddress,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      // this will write a confirmation email, so check existence manually
      'SendConfirmationEmail' => TRUE,
      'TriggerAutoResponders' => FALSE,
      'OtherFields' => array(),
    );

    $Result = Subscribers::Subscribe($Parameters);

    $Parameters['EmailAddress'] = $ApiEmailUpdateUnsubscribed;
    $Result2 = Subscribers::Subscribe($Parameters);

    // unsubscribed step 2 -> subscribed
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $SubscribeList,
      'EmailAddress' => $InitEmailAddressUnsubscribed,
      'IPAddress' => $IPAddress,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      // this will write a confirmation email, so check existence manually
      'SendConfirmationEmail' => TRUE,
      'TriggerAutoResponders' => FALSE,
      'OtherFields' => array(),
    );

    $Result = Subscribers::Subscribe($Parameters);
    $ChangeEmailUnsubscribedSID = $Result[1];

    $Result = Subscribers::Unsubscribe($UserID, $InitEmailAddressUnsubscribed, 0, $IPAddress);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailUnsubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message $ChangeEmailAddressUnsubscribed unsubscribed");

    $Parameters['EmailAddress'] = $ApiEmailUpdateUnsubscribed;

    $Result = Subscribers::Subscribe($Parameters);
    $ApiEmailUnsubscribedSID = $Result[1];

    $Result = Subscribers::Unsubscribe($UserID, $ApiEmailUpdateUnsubscribed, 0, $IPAddress);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailUnsubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message $ApiEmailUpdateUnsubscribed unsubscribed");


    // --- imported subscriber ---
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ImportList,
      'OptInSubscribeTo' => 0,
      'EmailAddress' => $InitEmailAddressImported,
      'IPAddress' => $IPAddress,
      'OtherFields' => array(),
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SendConfirmationEmail' => TRUE,
      'UpdateIfUnsubscribed' => FALSE,
      'TriggerAutoResponders' => FALSE,
      'ImportOptInDate' => 0,
    );

    $Result = Subscribers::Subscribe($Parameters);
    $ChangeEmailImportSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailImportSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message $ChangeEmailAddressImported imported");

    $Parameters['EmailAddress'] = $ApiEmailUpdateImported;

    $Result = Subscribers::Subscribe($Parameters);
    $ApiEmailImportedSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailImportedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message $ApiEmailUpdateImported imported");

    // --- single optin subscriber ---
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $SingleOptInList,
      'OptInSubscribeTo' => 0,
      'EmailAddress' => $InitEmailAddressSingleoptin,
      'IPAddress' => $IPAddress,
      'OtherFields' => array(),
      'SendConfirmationEmail' => TRUE,
      'UpdateIfUnsubscribed' => FALSE,
      'TriggerAutoResponders' => FALSE,
      'ImportOptInDate' => 0,
    );

    $Result = Subscribers::Subscribe($Parameters);
    $ChangeEmailSingleoptinSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailSingleoptinSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message $ChangeEmailAddressSingleoptin single opted in");

    $Parameters['EmailAddress'] = $ApiEmailUpdateSingleoptin;

    $Result = Subscribers::Subscribe($Parameters);
    $ApiEmailSingleoptinSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailSingleoptinSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message $ApiEmailUpdateSingleoptin single opted in");

    // ---- re-send confirmation email -----

    // create a single optin list
    $message = 'Re-send confirmation email: ';
    $ArrayFieldAndValues = array(
      'Name' => 'test single optin list',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE,
    );

    //create DOI list with ReSendConfirmationEmail
    $ResendListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($ResendListID > 0, "$message Double optin list created");
    $ArrayResendList = Lists::RetrieveListByID($UserID, $ResendListID);
    $ArrayResendList['ReSendConfirmationEmail'] = 1;
    $ArrayResendList['ListID'] = $ResendListID;
    Lists::UpdateDB($ArrayResendList);
    $ArrayResendList = Lists::RetrieveListByID($UserID, $ResendListID);
    $this->assertTrue($ArrayResendList['ReSendConfirmationEmail'] == 1, "$message Double optin set to re-send");

    // subscribe single optin
    $ResendEmailAddress = '<EMAIL>';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $SingleOptInList,
      'EmailAddress' => $ResendEmailAddress,
      'IPAddress' => $IPAddress,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SendConfirmationEmail' => TRUE,
      'TriggerAutoResponders' => FALSE,
      'OtherFields' => array(),
    );

    $Result = Subscribers::Subscribe($Parameters);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $Result[1], $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message step 1 subscribe with single optin");
    $this->assertTrue(empty($CheckSubscriber['ConfirmationEmailDate']), "$message step 1 no email sent (date == 0)");

    // optin again with double optin and re-send
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArrayResendList,
      'EmailAddress' => $ResendEmailAddress,
      'IPAddress' => $IPAddress,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      'SendConfirmationEmail' => TRUE,
      'TriggerAutoResponders' => FALSE,
      'OtherFields' => array(),
    );

    $before_count = get_confirmation_email_count();

    $Result = Subscribers::Subscribe($Parameters);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $Result[1], $ReferenceID);
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message step 2 re-optin with double optin and re-send");
    $after_count = get_confirmation_email_count();
    $this->assertTrue($after_count == $before_count + 1, "$message step 2 email sent");

    // TEST CASE: update subscriber firstname without changing the email address via API or Subscriber-Edit

    $message = "UpdateSubscription without changing email address:";

    $now = time();
    $lasttimesent = strtotime("-1 days"); // yesterday

    $NewFirstName = 'firstname changed via api';
    //subscribed
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailSubscribedSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailSubscribedSID, array('CustomFieldFirstName' => $NewFirstName), Subscribers::DOI_PROCESS_USE_FROM_SUBSCRIPTION );
    $CheckSubscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $ChangeEmailSubscribedSID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID, TRUE);
    $this->assertTrue($CheckSubscription['CustomFieldFirstName'] == $NewFirstName, "$message subscribed - firstname changed to '$NewFirstName''");
    $this->assertTrue($CheckSubscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message subscribed - status unchanged = subscribed ({$CheckSubscription['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscription['ConfirmationEmailDate'] == $lasttimesent, "$message ConfirmationEmailDate not sent for custom field change");

    //pending
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailPendingSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailPendingSID, array('CustomFieldFirstName' => $NewFirstName), Subscribers::DOI_PROCESS_USE_FROM_SUBSCRIPTION );
    $CheckSubscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $ChangeEmailPendingSID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID, TRUE);
    $this->assertTrue($CheckSubscription['CustomFieldFirstName'] == $NewFirstName, "$message pending - firstname changed to '$NewFirstName''");
    $this->assertTrue($CheckSubscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message pending - status unchanged = pending ({$CheckSubscription['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscription['ConfirmationEmailDate'] == $lasttimesent, "$message ConfirmationEmailDate not sent for custom field change");

    //unsubscribed
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailUnsubscribedSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailUnsubscribedSID, array('CustomFieldFirstName' => $NewFirstName), Subscribers::DOI_PROCESS_USE_FROM_SUBSCRIPTION );
    $CheckSubscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $ChangeEmailUnsubscribedSID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID, TRUE);
    $this->assertTrue($CheckSubscription['CustomFieldFirstName'] == $NewFirstName, "$message unsubscribed - firstname changed to '$NewFirstName''");
    $this->assertTrue($CheckSubscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message unsubscribed - status unchanged = unsubscribed ({$CheckSubscription['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscription['ConfirmationEmailDate'] == $lasttimesent, "$message ConfirmationEmailDate not sent for custom field change");

    //imported
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailImportSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailImportSID, array('CustomFieldFirstName' => $NewFirstName), Subscribers::DOI_PROCESS_USE_FROM_SUBSCRIPTION );
    $CheckSubscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $ChangeEmailImportSID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID, TRUE);
    $this->assertTrue($CheckSubscription['CustomFieldFirstName'] == $NewFirstName, "$message imported - firstname changed to '$NewFirstName''");
    $this->assertTrue($CheckSubscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message imported - status unchanged = subscribed ({$CheckSubscription['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscription['ConfirmationEmailDate'] == $lasttimesent, "$message ConfirmationEmailDate not sent for custom field change");

    //single optin
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailSingleoptinSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailSingleoptinSID, array('CustomFieldFirstName' => $NewFirstName), Subscribers::DOI_PROCESS_USE_FROM_SUBSCRIPTION );
    $CheckSubscription = Subscription::RetrieveSubscriptionByIDAndReference($UserID, $ChangeEmailSingleoptinSID, Subscription::SUBSCRIPTIONTYPE_EMAIL, $ReferenceID, TRUE);
    $this->assertTrue($CheckSubscription['CustomFieldFirstName'] == $NewFirstName, "$message subscribed single optin - firstname changed to '$NewFirstName''");
    $this->assertTrue($CheckSubscription['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message subscribed single optin - status unchanged = subscribed ({$CheckSubscription['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscription['ConfirmationEmailDate'] == $lasttimesent, "$message ConfirmationEmailDate not sent for custom field change");

    // --- change email addresses ---

    //TEST CASE: change email by customer in Subscriber-Edit
    $message = "UpdateSubscription change email address by customer:";

    //subscribed
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailSubscribedSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailSubscribedSID, array('EmailAddress' => $ChangeEmailAddressSubscribed), Subscribers::DOI_PROCESS_USE_SINGLE_OPTIN);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailSubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $ChangeEmailAddressSubscribed, "$message $InitEmailAddressSubscribed changed to $ChangeEmailAddressSubscribed");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message subscribed - status unchanged = subscribed ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] == 0, "$message ConfirmationEmailDate not sent for single optin".print_r($CheckSubscriber,1));

    //pending
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailPendingSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailPendingSID, array('EmailAddress' => $ChangeEmailAddressPending), Subscribers::DOI_PROCESS_USE_SINGLE_OPTIN);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailPendingSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $ChangeEmailAddressPending, "$message $InitEmailAddressPending changed to $ChangeEmailAddressPending");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message pending - status unchanged = pending ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] == 0, "$message ConfirmationEmailDate not sent for single optin");

    //unsubscribed
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailUnsubscribedSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailUnsubscribedSID, array('EmailAddress' => $ChangeEmailAddressUnsubscribed), Subscribers::DOI_PROCESS_USE_SINGLE_OPTIN);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailUnsubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $ChangeEmailAddressUnsubscribed, "$message $InitEmailAddressUnsubscribed changed to $ChangeEmailAddressUnsubscribed");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message unsubscribed - status unchanged = unsubscribed ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] == 0, "$message ConfirmationEmailDate not sent for single optin");

    //imported
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailImportSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailImportSID, array('EmailAddress' => $ChangeEmailAddressImported), Subscribers::DOI_PROCESS_USE_SINGLE_OPTIN);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailImportSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $ChangeEmailAddressImported, "$message $InitEmailAddressImported changed to $ChangeEmailAddressImported");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message imported - status unchanged = subscribed ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] == 0, "$message ConfirmationEmailDate not sent for single optin");

    //single optin
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailSingleoptinSID)->execute();
    Subscribers::UpdateSubscription($UserID, $ChangeEmailSingleoptinSID, array('EmailAddress' => $ChangeEmailAddressSingleoptin), Subscribers::DOI_PROCESS_USE_SINGLE_OPTIN);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailSingleoptinSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $ChangeEmailAddressSingleoptin, "$message $InitEmailAddressSingleoptin changed to $ChangeEmailAddressSingleoptin");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message subscribed single optin - status unchanged = subscribed ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] == 0, "$message ConfirmationEmailDate not sent for single optin");

    //TEST CASE: change email by subscriber via change email dialog

    $message = "UpdateSubscription change email address by subscriber:";

    //subscribed
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailSubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['RelListID'] != $ChangeEmailListID, "$message subscriber does not have change email subscription");
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailSubscribedSID)->execute();
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ChangeEmailSubscribedSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailSubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ChangeEmailAddressSubscribed changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message status changed to pending ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['RelListID'] == $ChangeEmailListID, "$message subscriber has change email subscription");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] >= $now, "$message ConfirmationEmailDate sent");

    //pending
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailPendingSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['RelListID'] != $ChangeEmailListID, "$message subscriber does not have change email subscription");
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailPendingSID)->execute();
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ChangeEmailPendingSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailPendingSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ChangeEmailAddressPending changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message status unchanged = pending ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['RelListID'] == $ChangeEmailListID, "$message subscriber has change email subscription");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] >= $now, "$message ConfirmationEmailDate sent");

    //unsubscribed
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailUnsubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['RelListID'] != $ChangeEmailListID, "$message subscriber does not have change email subscription");
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailUnsubscribedSID)->execute();
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ChangeEmailUnsubscribedSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailUnsubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ChangeEmailAddressUnsubscribed changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message status unchanged = unsubscribed ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['RelListID'] != $ChangeEmailListID, "$message subscriber list id unchanged (as this wont result in an optin)");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] == 0, "$message ConfirmationEmailDate sent");

    //imported
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailImportSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['RelListID'] != $ChangeEmailListID, "$message subscriber does not have change email subscription");
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailImportSID)->execute();
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ChangeEmailImportSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailImportSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ChangeEmailAddressImported changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message status changed to pending ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['RelListID'] == $ChangeEmailListID, "$message subscriber has change email subscription");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] >= $now, "$message ConfirmationEmailDate sent");

    //single optin list
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailSingleoptinSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['RelListID'] != $ChangeEmailListID, "$message subscriber does not have change email subscription");
    db_update('subscription')->fields(['ConfirmationEmailDate' => $lasttimesent])->condition('RelSubscriberID', $ChangeEmailSingleoptinSID)->execute();
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ChangeEmailSingleoptinSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST); // single optin (via api amember)
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ChangeEmailSingleoptinSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ChangeEmailAddressSingleoptin changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message status unchanged subscribed ({$CheckSubscriber['SubscriptionStatus']})");
    $this->assertTrue($CheckSubscriber['RelListID'] == $ChangeEmailListID, "$message subscriber has change email subscription");
    $this->assertTrue($CheckSubscriber['ConfirmationEmailDate'] >= $now, "$message ConfirmationEmailDate sent");

    //TEST CASE: delete email

    $message = "UpdateSubscription delete email";

    $newfirstname = 'newfirstname';

    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ImportList,
      'OptInSubscribeTo' => 0,
      'EmailAddress' => '<EMAIL>',
      'IPAddress' => $IPAddress,
      'OtherFields' => array(array('CustomFieldFirstName' => 'oldfirstname')),
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SendConfirmationEmail' => TRUE,
      'UpdateIfUnsubscribed' => FALSE,
      'TriggerAutoResponders' => FALSE,
      'ImportOptInDate' => 0,
    );

    // email only

    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '';
    $Result = Subscribers::Subscribe($Parameters);
    $DeleteSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID before".print_r([$Result, $CheckSubscriber],1));

    Subscribers::UpdateSubscription($UserID, $DeleteSID, array('EmailAddress' => '', 'CustomFieldFirstName' => $newfirstname));
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID email deleted".print_r($CheckSubscriber,1));
    $this->assertTrue(empty($CheckSubscriber['EmailAddress']), "$message $DeleteSID email deleted");
    $this->assertTrue(empty($CheckSubscriber['PhoneNumber']), "$message $DeleteSID phone deleted");
    $this->assertTrue(empty($CheckSubscriber['SubscriptionStatus']), "$message deleted SubscriptionStatus");
    $this->assertTrue(empty($CheckSubscriber['SMSSubscriptionStatus']), "$message deleted SMSSubscriptionStatus");
    $this->assertTrue($CheckSubscriber['CustomFieldFirstName'] == $newfirstname, "$message deleted CustomFieldFirstName");

    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '';
    $Result = Subscribers::Subscribe($Parameters);
    $DeleteSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID before".print_r([$Result, $CheckSubscriber],1));

    Subscribers::UpdateSubscription($UserID, $DeleteSID, array('EmailAddress' => '', 'PhoneNumber' => '', 'CustomFieldFirstName' => $newfirstname));
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID email deleted".print_r($CheckSubscriber,1));
    $this->assertTrue(empty($CheckSubscriber['EmailAddress']), "$message $DeleteSID email deleted");
    $this->assertTrue(empty($CheckSubscriber['PhoneNumber']), "$message $DeleteSID phone deleted");
    $this->assertTrue(empty($CheckSubscriber['SubscriptionStatus']), "$message deleted SubscriptionStatus");
    $this->assertTrue(empty($CheckSubscriber['SMSSubscriptionStatus']), "$message deleted SMSSubscriptionStatus");
    $this->assertTrue($CheckSubscriber['CustomFieldFirstName'] == $newfirstname, "$message deleted CustomFieldFirstName");

    // email + phone

    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '************991';
    $Result = Subscribers::Subscribe($Parameters);
    $DeleteSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID before".print_r([$Result, $CheckSubscriber],1));

    Subscribers::UpdateSubscription($UserID, $DeleteSID, array('EmailAddress' => '', 'CustomFieldFirstName' => $newfirstname));
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID email deleted".print_r($CheckSubscriber,1));
    $this->assertTrue(empty($CheckSubscriber['EmailAddress']), "$message $DeleteSID email deleted");
    $this->assertTrue(!empty($CheckSubscriber['PhoneNumber']), "$message $DeleteSID phone deleted");
    $this->assertTrue(empty($CheckSubscriber['SubscriptionStatus']), "$message deleted SubscriptionStatus");
    $this->assertTrue(!empty($CheckSubscriber['SMSSubscriptionStatus']), "$message deleted SMSSubscriptionStatus");
    $this->assertTrue($CheckSubscriber['CustomFieldFirstName'] == $newfirstname, "$message deleted CustomFieldFirstName");

    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '************992';
    $Result = Subscribers::Subscribe($Parameters);
    $DeleteSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID before".print_r([$Result, $CheckSubscriber],1));

    Subscribers::UpdateSubscription($UserID, $DeleteSID, array('EmailAddress' => '', 'PhoneNumber' => '', 'CustomFieldFirstName' => $newfirstname));
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID email deleted".print_r($CheckSubscriber,1));
    $this->assertTrue(empty($CheckSubscriber['EmailAddress']), "$message $DeleteSID email deleted");
    $this->assertTrue(empty($CheckSubscriber['PhoneNumber']), "$message $DeleteSID phone deleted");
    $this->assertTrue(empty($CheckSubscriber['SubscriptionStatus']), "$message deleted SubscriptionStatus");
    $this->assertTrue(empty($CheckSubscriber['SMSSubscriptionStatus']), "$message deleted SMSSubscriptionStatus");
    $this->assertTrue($CheckSubscriber['CustomFieldFirstName'] == $newfirstname, "$message deleted CustomFieldFirstName");

    // phone only

    $Parameters['EmailAddress'] = '';
    $Parameters['PhoneNumber'] = '************993';
    $Result = Subscribers::Subscribe($Parameters);
    $DeleteSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID before".print_r([$Result, $CheckSubscriber],1));

    Subscribers::UpdateSubscription($UserID, $DeleteSID, array('EmailAddress' => '', 'CustomFieldFirstName' => $newfirstname));
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID email deleted".print_r($CheckSubscriber,1));
    $this->assertTrue(empty($CheckSubscriber['EmailAddress']), "$message $DeleteSID email deleted");
    $this->assertTrue(!empty($CheckSubscriber['PhoneNumber']), "$message $DeleteSID phone deleted");
    $this->assertTrue(empty($CheckSubscriber['SubscriptionStatus']), "$message deleted SubscriptionStatus");
    $this->assertTrue(!empty($CheckSubscriber['SMSSubscriptionStatus']), "$message deleted SMSSubscriptionStatus");
    $this->assertTrue($CheckSubscriber['CustomFieldFirstName'] == $newfirstname, "$message deleted CustomFieldFirstName");

    $Parameters['EmailAddress'] = '';
    $Parameters['PhoneNumber'] = '************994';
    $Result = Subscribers::Subscribe($Parameters);
    $DeleteSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID before".print_r([$Result, $CheckSubscriber],1));

    Subscribers::UpdateSubscription($UserID, $DeleteSID, array('EmailAddress' => '', 'PhoneNumber' => '', 'CustomFieldFirstName' => $newfirstname));
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID email deleted".print_r($CheckSubscriber,1));
    $this->assertTrue(empty($CheckSubscriber['EmailAddress']), "$message $DeleteSID email deleted");
    $this->assertTrue(empty($CheckSubscriber['PhoneNumber']), "$message $DeleteSID phone deleted");
    $this->assertTrue(empty($CheckSubscriber['SubscriptionStatus']), "$message deleted SubscriptionStatus");
    $this->assertTrue(empty($CheckSubscriber['SMSSubscriptionStatus']), "$message deleted SMSSubscriptionStatus");
    $this->assertTrue($CheckSubscriber['CustomFieldFirstName'] == $newfirstname, "$message deleted CustomFieldFirstName");

    //TEST CASE: change email by api

    $message = "UpdateSubscription change email address by API:";

    //subscribed
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailSubscribedSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 0, "$message subscriber does not have change email subscription");
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ApiEmailSubscribedSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailSubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ApiEmailUpdateSubscribed changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message status changed to pending ({$CheckSubscriber['SubscriptionStatus']})");
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailSubscribedSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 1, "$message subscriber has change email subscription");

    //pending
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailPendingSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 0, "$message subscriber does not have change email subscription");
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ApiEmailPendingSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailPendingSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ApiEmailUpdatePending changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message status unchanged = pending ({$CheckSubscriber['SubscriptionStatus']})");
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailPendingSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 1, "$message subscriber has change email subscription");

    //unsubscribed
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailUnsubscribedSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 0, "$message subscriber does not have change email subscription");
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ApiEmailUnsubscribedSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailUnsubscribedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ApiEmailUpdateUnsubscribed changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message status unchanged unsubscribed ({$CheckSubscriber['SubscriptionStatus']})");
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailUnsubscribedSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 0, "$message subscriber still does not have change email subscription");

    //imported
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailImportedSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 0, "$message subscriber does not have change email subscription");
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ApiEmailImportedSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailImportedSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ApiEmailUpdateImported changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message status changed to pending ({$CheckSubscriber['SubscriptionStatus']})");
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailImportedSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 1, "$message subscriber has change email subscription");

    //single optin list
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailSingleoptinSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 0, "$message subscriber does not have change email subscription");
    $NewEmailAddress = '<EMAIL>';
    Subscribers::UpdateSubscription($UserID, $ApiEmailSingleoptinSID, array('EmailAddress' => $NewEmailAddress), Subscribers::DOI_PROCESS_USE_CHANGE_EMAIL_LIST); // single optin (via api amember)
    $CheckSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $ApiEmailSingleoptinSID, $ReferenceID);
    $this->assertTrue($CheckSubscriber['EmailAddress'] == $NewEmailAddress, "$message $ApiEmailUpdateSingleoptin changed to $NewEmailAddress");
    $this->assertTrue($CheckSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_OPTIN, "$message status unchanged subscribed ({$CheckSubscriber['SubscriptionStatus']})");
    $ChangeEmailSubscription = db_query("SELECT COUNT(*) FROM {subscription} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :SubscriberID AND RelListID = :RelListID", array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriberID' => $ApiEmailSingleoptinSID,
      ':RelListID' => $ChangeEmailListID
    ))->fetchField();
    $this->assertTrue($ChangeEmailSubscription == 1, "$message subscriber has change email subscription");

    // ----- TEST CASES SUBSCRIPTION WITH PHONE NUMBER - START -----

    $message = "PhoneNumber Subscription:";

    //create DOI list
    $ArrayFieldAndValues = array(
      'Name' => 'PhoneNumber subscription DOI',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE,
    );
    $SMSDoubleOptinListID = Lists::InsertDB($ArrayFieldAndValues);
    $SMSDoubleOptinList = Lists::RetrieveListByID($UserID, $SMSDoubleOptinListID);
    $this->assertTrue($SMSDoubleOptinList, "$message Create DOI list");

    //create SOI list
    $ArrayFieldAndValues = array(
      'Name' => 'PhoneNumber subscription SOI',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
    );
    $SMSSingleOptinListID = Lists::InsertDB($ArrayFieldAndValues);
    $SMSSingleOptinList = Lists::RetrieveListByID($UserID, $SMSSingleOptinListID);
    $this->assertTrue($SMSSingleOptinList, "$message Create SOI list");

    $TestData = array(
      __LINE__.' New Subscriber by email with no phone number, DOI' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'EmailAddress' => '<EMAIL>',
          'ListInformation' => $SMSDoubleOptinList,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '',
          'SMSSubscriptionStatus' => '',
        ),
      ),
      __LINE__.' New Subscriber (2) by email with no phone number, DOI' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'EmailAddress' => '<EMAIL>',
          'ListInformation' => $SMSDoubleOptinList,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '',
          'SMSSubscriptionStatus' => '',
        ),
      ),
      __LINE__.' New Subscriber by email with new phone number, DOI' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'EmailAddress' => '<EMAIL>',
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000003',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491520000003',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' New Subscriber by phone number without email address, DOI' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000004',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '',
          'SubscriptionStatus' => '',
          'PhoneNumber' => '00491520000004',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' New Subscriber (2) by phone number without email address, DOI' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000005',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '',
          'SubscriptionStatus' => '',
          'PhoneNumber' => '00491520000005',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' New Subscriber (3) by phone number without email address, DOI' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000006',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '',
          'SubscriptionStatus' => '',
          'PhoneNumber' => '00491520000006',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' New Subscriber without email address, phone number and SubscriberID - new empty subscriber' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '',
          'SubscriptionStatus' => '',
          'PhoneNumber' => '',
          'SMSSubscriptionStatus' => '',
        ),
      ),
      __LINE__.' Add phone number by email address and confirm, DOI' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'EmailAddress' => '<EMAIL>',
          'PhoneNumber' => '00491520000001',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
          'PhoneNumber' => '00491520000001',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Add phone number by SubscriberID and confirm, DOI' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'SubscriberID' => '<EMAIL>',
          //will be retrieved by email
          'PhoneNumber' => '00491520000002',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491520000002',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Replace dummy email address by SubscriberID' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'EmailAddress' => '<EMAIL>',
          'SubscriberID' => '00491520000004',
          //will be retrieved by phone number
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491520000004',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Replace dummy email address by phone number ' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'EmailAddress' => '<EMAIL>',
          'PhoneNumber' => '00491520000005', //will be retrieved by phone number
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491520000005',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Replace dummy email address by SubscriberID and existing PhoneNumber - error' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'EmailAddress' => '<EMAIL>',
          'PhoneNumber' => '00491520000005',
          //phone number of a different subscriber
          'SubscriberID' => '00491520000006',
          //will be retrieved by phone number
        ),
        'SubscriptionResult' => array(FALSE, Subscribers::SUBSCRIBE_ERROR_DUPLICATE_PHONE_NUMBER),
      ),
      __LINE__.' Replace dummy email address by SubscriberID and matching PhoneNumber' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'SubscriberID' => '00491520000006',
          //will be retrieved by phone number
          'EmailAddress' => '<EMAIL>',
          'PhoneNumber' => '00491520000006',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491520000006',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Replace real email address by SubscriberID with non-existing email address' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'SubscriberID' => '00491520000001',
          //will be retrieved by phone number
          'EmailAddress' => '<EMAIL>',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491520000001',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Replace real email address by SubscriberID with existing email address - error' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'SubscriberID' => '00491520000001',
          //will be retrieved by phone number
          'EmailAddress' => '<EMAIL>',
        ),
        'SubscriptionResult' => array(FALSE, KLICKTIPPAPI_ERROR_EMAILADDRESS_EXISTS),
      ),
      __LINE__.' Replace real email address by PhoneNumber with non-existing email address' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000001',
          'EmailAddress' => '<EMAIL>',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491520000001',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Replace real email address by PhoneNumber with another email address from same reference' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000001',
          'EmailAddress' => '<EMAIL>',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
          'PhoneNumber' => '00491520000001',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Replace real email address by PhoneNumber with existing email address - error' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000001',
          'EmailAddress' => '<EMAIL>',
        ),
        'SubscriptionResult' => array(FALSE, Subscribers::SUBSCRIBE_ERROR_DUPLICATE_PHONE_NUMBER),
      ),
      __LINE__.' Update phone number by SubscriberID with non-existing phone number' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520009001',
          'SubscriberID' => '<EMAIL>',
          //will be retrieved by email address
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
          'PhoneNumber' => '00491520009001',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Update phone number by SubscriberID with existing phone number - error' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000002',
          'SubscriberID' => '<EMAIL>',
          //will be retrieved by email address
        ),
        'SubscriptionResult' => array(FALSE, Subscribers::SUBSCRIBE_ERROR_DUPLICATE_PHONE_NUMBER),
      ),
      __LINE__.' Update phone number by EmailAddress with non-existing phone number - ignore' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520008001',
          'EmailAddress' => '<EMAIL>',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
          'PhoneNumber' => '00491520008001',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Update phone number by EmailAddress with existing phone number - error' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000002',
          'EmailAddress' => '<EMAIL>',
        ),
        'SubscriptionResult' => array(FALSE, Subscribers::SUBSCRIBE_ERROR_DUPLICATE_PHONE_NUMBER),
      ),
      __LINE__.' Delete phone number' => array(
        'DeletePhoneNumber' => '00491520000001', //delete phone number
      ),
      __LINE__.' Update deleted phone number by EmailAddress with same phone number' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000001', //add same phone number again
          'EmailAddress' => '<EMAIL>',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
          'PhoneNumber' => '00491520000001',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Update deleted phone number by EmailAddress with different phone number' => array(
        'DeletePhoneNumber' => '00491520000001',
        //delete this phone number first
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520007001', //add same phone number again
          'EmailAddress' => '<EMAIL>',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
          'PhoneNumber' => '00491520007001',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' New Subscriber by email with deleted phone number, DOI' => array(
        'DeletePhoneNumber' => '00491520007001',
        //delete this phone number first
        'Parameters' => array(
          'UserID' => $UserID,
          'EmailAddress' => '<EMAIL>',
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520007001',
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491520007001',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Unsubscribe subscribed phone number' => array(
        'UnsubscribePhoneNumber' => '00491520000002', //unsubscribe phone number
        'Expected' => array(
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED,
        ),
      ),
      __LINE__.' Re-subscribe unsubscribed phone number by EmailAddress with same phone number' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'ListInformation' => $SMSDoubleOptinList,
          'PhoneNumber' => '00491520000002',
          'SubscriberID' => '<EMAIL>',
          //will be retrieved by email address
          'UpdatePhoneNumberIfUnsubscribed' => TRUE,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491520000002',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Add Subscriber with email and phone number - prepare duplicate test' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'EmailAddress' => '<EMAIL>',
          'PhoneNumber' => '00491527777701',
          'ListInformation' => $SMSDoubleOptinList,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '<EMAIL>',
          'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
          'PhoneNumber' => '00491527777701',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Delete phone number - duplicate test' => array(
        'DeletePhoneNumber' => '00491527777701',
        //delete phone number to have a subscriber with dummy phone number
      ),
      __LINE__.' Add Subscriber with phone number and no email - prepare duplicate test' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'PhoneNumber' => '00491527777702',
          'ListInformation' => $SMSDoubleOptinList,
        ),
        'SubscriptionResult' => array(TRUE, 0),
        'Expected' => array(
          'EmailAddress' => '',
          'SubscriptionStatus' => '',
          'PhoneNumber' => '00491527777702',
          'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
        ),
      ),
      __LINE__.' Update Subscriber with phone number and email of 2 dummy subscribers - duplicate test - error' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'EmailAddress' => '<EMAIL>',
          'PhoneNumber' => '00491527777702',
          'ListInformation' => $SMSDoubleOptinList,
        ),
        'SubscriptionResult' => array(
          FALSE,
          Subscribers::SUBSCRIBE_ERROR_DUPLICATE_PHONE_NUMBER
        ),
      ),
      __LINE__.' Replace dummy email address with existing email address - error' => array(
        'Parameters' => array(
          'UserID' => $UserID,
          'EmailAddress' => '<EMAIL>',
          'SubscriberID' => '00491527777702',
          //will be retrieved by phone number, has dummy email
          'ListInformation' => $SMSDoubleOptinList,
        ),
        'SubscriptionResult' => array(
          FALSE,
          KLICKTIPPAPI_ERROR_EMAILADDRESS_EXISTS
        ),
      ),


    );

    foreach ($TestData as $msg => $data) {

      if (!empty($data['DeletePhoneNumber'])) {

        $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $data['DeletePhoneNumber']);
        $phoneNumberBeforeDeletion = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID)['PhoneNumber'];

        Subscribers::UpdateSubscription($UserID, $SubscriberID, array('PhoneNumber' => ''));

        $Subscription = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
        // after deletion another phone number can become default
        $this->assertTrue($Subscription['PhoneNumber'] != $phoneNumberBeforeDeletion, "$message $msg - phone number {$data['DeletePhoneNumber']} deleted");

      }

      if (!empty($data['UnsubscribePhoneNumber'])) {

        $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $data['UnsubscribePhoneNumber']);

        Subscribers::UnsubscribeSMSNumber($UserID, $data['UnsubscribePhoneNumber']);

        $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $SubscriberID, $ReferenceID);

        $m = "$message $msg - phone number {$data['UnsubscribePhoneNumber']} unsubscribed";
        $this->assertTrue($Subscription['PhoneNumber'] == $data['UnsubscribePhoneNumber'] && $Subscription['SMSSubscriptionStatus'] == $data['Expected']['SMSSubscriptionStatus'], $m);

      }

      if (!empty($data['Parameters'])) {

        if (!empty($data['Parameters']['SubscriberID'])) {
          if (valid_email_address($data['Parameters']['SubscriberID'])) {
            $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $data['Parameters']['SubscriberID']);
          }
          else {
            $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $data['Parameters']['SubscriberID']);
          }

          $data['Parameters']['SubscriberID'] = $SubscriberID;

        }

        $result = Subscribers::Subscribe($data['Parameters']);

        $this->assertTrue($result[0] == $data['SubscriptionResult'][0], "$message $msg");
        if (!$result[0]) {
          $this->assertTrue($result[1] == $data['SubscriptionResult'][1], "$message $msg - Error {$result[1]} ({$data['SubscriptionResult'][1]})");
        }

        if ($result[0]) {

          $CheckSubscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $result[1], $ReferenceID);

          foreach ($data['Expected'] as $param => $expected) {
            $value = $CheckSubscription[$param];

            $this->assertTrue($expected == $value, "$message $msg - Param $param = '$value' ('$expected')");

          }

        }

      }

    }

    // prepare data
    $Parameters = array(
      'ReferenceID' => $ReferenceID,
      'ListInformation' => $SMSDoubleOptinList,
      'OptInSubscribeTo' => 0,
      'IPAddress' => '0.0.0.0 by IPN',
      'SubscriptionReferrer' => 'http://www.example.com',
      'OtherFields' => array(),
      'SubscriptionStatus' => 0, //Double OptIn
      'SendConfirmationEmail' => TRUE,
      'UpdateIfUnsubscribed' => TRUE,
      'UpdatePhoneNumberIfUnsubscribed' => TRUE,
      'UpdateStatistics' => TRUE,
      'TriggerAutoResponders' => TRUE,
    );

    // --- Subscribers::SubscribeWithPhoneNumberCheck: subscribe with phone number check and (different) EmailAddress

    //TESTCASE: sms number subscribe
    $message = "Subscribers::SubscribeWithPhoneNumberCheck with valid phone number";
    $Parameters['EmailAddress'] = "<EMAIL>"; // update
    $Parameters['PhoneNumber'] = '00491631737743'; // update: http://www.frankgehtran.de/
    $Parameters['UserID'] = $UserID; // update

    $Result = Subscribers::SubscribeWithPhoneNumberCheck($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $Parameters['EmailAddress']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($FullSubscriber['RelOwnerUserID'], $SubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriber["PhoneNumber"] == $Parameters['PhoneNumber'], "$message SMS Phone Number subscribed");
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");
    Subscribers::UnsubscribeSMSNumber($FullSubscriber['RelOwnerUserID'], $FullSubscriber['PhoneNumber']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($FullSubscriber['RelOwnerUserID'], $FullSubscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message SMS SubscriptionStatus unsubscribed");

    //TESTCASE: unsubscribed sms number subscribe
    $message = "Subscribers::SubscribeWithPhoneNumberCheck SMS number (unsubscribed)";
    $Parameters['EmailAddress'] = "<EMAIL>";
    $Parameters['PhoneNumber'] = '00491631737743';

    $Result = Subscribers::SubscribeWithPhoneNumberCheck($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $Parameters['PhoneNumber']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriber["PhoneNumber"] == $Parameters['PhoneNumber'], "$message SMS Phone Number remained");
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");

    //TESTCASE: subscribed sms number no subscribe
    $message = "Subscribers::SubscribeWithPhoneNumberCheck SMS number (no sms subscribe)";
    $Parameters['EmailAddress'] = "<EMAIL>"; //update
    $Parameters['PhoneNumber'] = '00491631737743';

    $Result = Subscribers::SubscribeWithPhoneNumberCheck($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $Parameters['EmailAddress']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($FullSubscriber['RelOwnerUserID'], $SubscriberID, $ReferenceID);
    $this->assertTrue(empty($FullSubscriber['PhoneNumber']), "$message No SMS Phone Number");

    [$success, $error] = Subscribers::UnsubscribeSMSNumber($FullSubscriber['RelOwnerUserID'], $Parameters['PhoneNumber']);
    $this->assertTrue($success, "$message unsubscribe phone number $error");

    //TESTCASE: unsubscribed sms number subscribe
    $message = "Subscribers::SubscribeWithPhoneNumberCheck SMS number (subscribe on unsubscribed number)";
    $Parameters['EmailAddress'] = "<EMAIL>";
    $Parameters['PhoneNumber'] = '00491631737743';

    $Result = Subscribers::SubscribeWithPhoneNumberCheck($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $Parameters['EmailAddress']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($FullSubscriber['RelOwnerUserID'], $SubscriberID, $ReferenceID);

    $this->assertTrue($FullSubscriber["PhoneNumber"] == $Parameters['PhoneNumber'], "$message SMS Phone Number subscribed");
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed".print_r($FullSubscriber,1));
    [$success, $error] = Subscribers::UnsubscribeSMSNumber($FullSubscriber['RelOwnerUserID'], $FullSubscriber['PhoneNumber']);
    $this->assertTrue($success, "$message unsubscribe phone number $error");
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($FullSubscriber['RelOwnerUserID'], $FullSubscriber['SubscriberID'], $ReferenceID);
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message SMS SubscriptionStatus unsubscribed");

    //TESTCASE: unsubscribed sms number subscribe with new sms number
    $message = "Subscribers::SubscribeWithPhoneNumberCheck SMS number (subscribe new number on unsubscribed number)";
    $Parameters['EmailAddress'] = "<EMAIL>";
    $Parameters['PhoneNumber'] = '00491631737744'; // update

    $Result = Subscribers::SubscribeWithPhoneNumberCheck($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'".print_r($Result,1));

    $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $Parameters['EmailAddress']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);

    $this->assertTrue($FullSubscriber["PhoneNumber"] == $Parameters['PhoneNumber'], "$message SMS Phone Number subscribed".print_r($FullSubscriber,1));
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");

    // ----- TEST CASES SUBSCRIPTION WITH PHONE NUMBER - END -----

    // --- Subscribers::UpdateSubscription with mobile phone number

    $message = "UpdateSubscription change phone number";

    $newfirstname = 'newfirstname';

    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ImportList,
      'OtherFields' => array(array('CustomFieldFirstName' => 'oldfirstname')),
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    );

    // subscribed

    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '************111';
    $Result = Subscribers::Subscribe($Parameters);
    $DeleteSID = $Result[1];
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID before".print_r([$Result, $CheckSubscriber],1));
    $this->assertTrue($CheckSubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message before SMSSubscriptionStatus");

    $newphonenumber = '************112';
    Subscribers::UpdateSubscription($UserID, $DeleteSID, array('PhoneNumber' => $newphonenumber, 'CustomFieldFirstName' => $newfirstname));
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID phone updated".print_r($CheckSubscriber,1));
    $this->assertTrue(!empty($CheckSubscriber['EmailAddress']), "$message $DeleteSID email updated");
    $this->assertTrue($CheckSubscriber['PhoneNumber'] == $newphonenumber, "$message $DeleteSID phone updated");
    $this->assertTrue(!empty($CheckSubscriber['SubscriptionStatus']), "$message updated SubscriptionStatus");
    $this->assertTrue($CheckSubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message updated SMSSubscriptionStatus");
    $this->assertTrue($CheckSubscriber['CustomFieldFirstName'] == $newfirstname, "$message updated CustomFieldFirstName");

    // unsubscribed

    $Parameters['EmailAddress'] = '<EMAIL>';
    $Parameters['PhoneNumber'] = '************113';
    $Result = Subscribers::Subscribe($Parameters);
    $DeleteSID = $Result[1];
    Subscribers::Unsubscribe($UserID, $Parameters['PhoneNumber'], 0, '0.0.0.0', Subscription::SUBSCRIPTIONTYPE_SMS);
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID before".print_r([$Result, $CheckSubscriber],1));
    $this->assertTrue($CheckSubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED, "$message before SMSSubscriptionStatus");

    $newphonenumber = '************114';
    Subscribers::UpdateSubscription($UserID, $DeleteSID, array('PhoneNumber' => $newphonenumber, 'CustomFieldFirstName' => $newfirstname));
    $CheckSubscriber = Subscribers::RetrieveFullSubscriberWithFields($UserID, $DeleteSID, $ReferenceID);
    $this->assertTrue(!empty($CheckSubscriber), "$message $DeleteSID phone updated".print_r($CheckSubscriber,1));
    $this->assertTrue(!empty($CheckSubscriber['EmailAddress']), "$message $DeleteSID email updated");
    $this->assertTrue($CheckSubscriber['PhoneNumber'] == $newphonenumber, "$message $DeleteSID phone updated");
    $this->assertTrue(!empty($CheckSubscriber['SubscriptionStatus']), "$message updated SubscriptionStatus");
    $this->assertTrue($CheckSubscriber['SMSSubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message updated SMSSubscriptionStatus");
    $this->assertTrue($CheckSubscriber['CustomFieldFirstName'] == $newfirstname, "$message updated CustomFieldFirstName");

    // ----- TEST CASES UPDATE SUBSCRIPTION WITH PHONE NUMBER - END -----

    // --- Subscribers::SubscribeSMSInbound: subscribe with priorization of mobile phone number

    $message = "Subscribers::SubscribeSMSInbound with no email address but known phone number";
    $Parameters['EmailAddress'] = ""; // no email address could be found in the sms inbound
    $Parameters['PhoneNumber'] = '00491631737744'; // known number

    $Result = Subscribers::SubscribeSMSInbound($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $Parameters['PhoneNumber']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriber["PhoneNumber"] == $Parameters['PhoneNumber'], "$message SMS Phone Number remained");
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");
    $this->assertTrue($FullSubscriber["EmailAddress"] != $Parameters['EmailAddress'], "$message SMS EmailAddress remained");

    $message = "Subscribers::SubscribeSMSInbound with no email address and unknown phone number";
    $Parameters['EmailAddress'] = ""; // no email address could be found in the sms inbound
    $Parameters['PhoneNumber'] = '00491631737745'; // update

    $Result = Subscribers::SubscribeSMSInbound($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $Parameters['PhoneNumber']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");
    $this->assertTrue(empty($FullSubscriber["EmailAddress"]), "$message Dummy EmailAddress subscribed");

    $message = "Subscribers::SubscribeSMSInbound with known email address and known phone number";
    $Parameters['EmailAddress'] = "<EMAIL>"; // existing email address
    $Parameters['PhoneNumber'] = '00491631737745'; // existing number
    // note: the email subscriber and the phone subscriber differ

    $Result = Subscribers::SubscribeSMSInbound($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $Parameters['PhoneNumber']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriber["PhoneNumber"] == $Parameters['PhoneNumber'], "$message SMS Phone Number remained");
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");
    $this->assertTrue($FullSubscriber["EmailAddress"] != $Parameters['EmailAddress'], "$message SMS EmailAddress not changed");

    $message = "Subscribers::SubscribeSMSInbound with new email address and known phone number";
    $Parameters['EmailAddress'] = "<EMAIL>"; // new email address
    $Parameters['PhoneNumber'] = '00491631737745'; // existing number

    $ExistingSubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $Parameters['PhoneNumber']);

    $Result = Subscribers::SubscribeSMSInbound($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $Parameters['PhoneNumber']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriber["PhoneNumber"] == $Parameters['PhoneNumber'], "$message SMS Phone Number remained".print_r($FullSubscriber,1));
    $this->assertTrue($ExistingSubscriberID == $SubscriberID, "$message SMS Subscriber remained");
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");
    $this->assertTrue($FullSubscriber["EmailAddress"] == $Parameters['EmailAddress'], "$message SMS EmailAddress updated");

    $message = "Subscribers::SubscribeSMSInbound with known email address and new phone number";
    $Parameters['EmailAddress'] = "<EMAIL>"; // known email address
    $Parameters['PhoneNumber'] = '00491631737746'; // new number

    $ExistingSubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $Parameters['EmailAddress']);

    $Result = Subscribers::SubscribeSMSInbound($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $Parameters['PhoneNumber']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($ExistingSubscriberID == $SubscriberID, "$message Email Subscriber updated");
    $this->assertTrue($FullSubscriber["PhoneNumber"] == $Parameters['PhoneNumber'], "$message SMS Phone Number updated");
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");

    $message = "Subscribers::SubscribeSMSInbound with known email address of different subscriber and known phone number";
    $Parameters['EmailAddress'] = "<EMAIL>"; // known email address
    $Parameters['PhoneNumber'] = '00491631737746'; // known number, <NAME_EMAIL>

    $ExistingSubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $Parameters['EmailAddress']);

    $Result = Subscribers::SubscribeSMSInbound($Parameters);
    $this->assertTrue($Result[0], "$message: updated, Result = '{$Result[1]}'");

    $SubscriberID = Subscription::RetrieveSubscriberIDByPhoneNumber($UserID, $Parameters['PhoneNumber']);
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($ExistingSubscriberID != $SubscriberID, "$message SMS Subscriber unchanged");
    $this->assertTrue($FullSubscriber["PhoneNumber"] == $Parameters['PhoneNumber'], "$message SMS Phone Number remained");
    $this->assertTrue($FullSubscriber["EmailAddress"] != $Parameters['EmailAddress'], "$message SMS EmailAddress not updated");
    $this->assertTrue($FullSubscriber["SMSSubscriptionStatus"] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, "$message SMS SubscriptionStatus subscribed");

    // ----- TEST CASES SUBSCRIPTION FROM SMS INBOUND - END -----

    // --------------------

    //function RemoveSubscribersOfUser($UserID)
    //ASAP

    /**
     * Check subscriber limits
     */
    $message = "CheckSubscriberLimits";
    $ArrayUserInformation = (array) user_load($UserID);
    $this->assertTrue(Subscribers::CheckSubscriberLimits($ArrayUserInformation) == FALSE, "$message subscriber limit 10000");
    db_update('user_groups')
      ->fields(array(
        'LimitSubscribers' => 1,
      ))
      ->condition('UserGroupID', $ArrayUserInformation['RelUserGroupID'])
      ->execute();
    $this->assertTrue(Subscribers::CheckSubscriberLimits($ArrayUserInformation) == TRUE, "$message subscriber limit 1");

    /**
     * Returns total number of active subscribers of a subscriber tag
     **/
    //function GetActiveTotal($UserID, $TagID = 0)
    //ASAP with unsubscribed

    /**
     * Returns subscribed tag information of a given subscriber
     *
     * @return array
     *
     **/
    //public static function RetrieveTaggingsOfSubscriber($UserID, $SubscriberID)

    /**
     * Write History
     *
     **/
    //public static function WriteHistory($OwnerUserID, $SubscriberID, $HistoryType, $HistoryData)

    /**
     * Read History
     *
     **/
    //public static function ReadHistory($OwnerUserID, $SubscriberID)

    $message = "ReadHistory Feedback: ";

    $User2ID = 2;

    $AllEmailAddresses = [
      'feedback1@exämple.com',
      'feedback1@exämple.com',
      'feedback2@exämple.com',
      'feedback3@exämple.com'
    ];
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySubscriberList,
      'EmailAddress' => Subscribers::PunycodeEmailAddress($AllEmailAddresses[0]),
      'IPAddress' => '***********',
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    );
    $result = Subscribers::Subscribe($Parameters);
    $FeedbackSubscriber1 = Subscribers::RetrieveFullSubscriber($UserID, $result[1], $ReferenceID);

    $Parameters['EmailAddress'] = Subscribers::PunycodeEmailAddress($AllEmailAddresses[2]);
    $result = Subscribers::Subscribe($Parameters);
    $FeedbackSubscriber2 = Subscribers::RetrieveFullSubscriber($UserID, $result[1], $ReferenceID);

    $Parameters['EmailAddress'] = Subscribers::PunycodeEmailAddress($AllEmailAddresses[3]);
    $Parameters['UserID'] = $User2ID;
    $result = Subscribers::Subscribe($Parameters);
    $FeedbackSubscriber3 = Subscribers::RetrieveFullSubscriber($User2ID, $result[1], $ReferenceID);

    // create feedback campaign 1
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'CampaignName' => 'Feedback Campaign 1',
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_CAMPAIGN,
    );
    $Campaign1ID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $FeedbackCampaign1 = Campaigns::RetrieveCampaignByID($Campaign1ID, $UserID);
    $FeedbackEmail1 = Emails::RetrieveEmailByID($UserID, $FeedbackCampaign1['RelEmailID']);

    // create feedback campaign 2
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $User2ID,
      'CampaignName' => 'Feedback Campaign 2',
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_CAMPAIGN,
    );
    $Campaign2ID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $FeedbackCampaign2 = Campaigns::RetrieveCampaignByID($Campaign2ID, $User2ID);
    $FeedbackEmail2 = Emails::RetrieveEmailByID($User2ID, $FeedbackCampaign2['RelEmailID']);

    $this->assertTrue(!empty($FeedbackSubscriber1) && !empty($FeedbackSubscriber2) && !empty($FeedbackSubscriber3), $message . ' create feedback subscribers');
    $this->assertTrue(!empty($FeedbackCampaign1) && !empty($FeedbackCampaign2), $message . ' create feedback campaign');

    //add 2 feedbacks from subscriber 1
    Subscribers::AddSubscriberFeedback($UserID, $FeedbackSubscriber1['SubscriberID'], $FeedbackSubscriber1['EmailAddress'], $Campaign1ID, $FeedbackEmail1['EmailID'], 'Subscriber Feedback 1');
    sleep(2); //for a different HistoryDate
    Subscribers::AddSubscriberFeedback($UserID, $FeedbackSubscriber1['SubscriberID'], $FeedbackSubscriber1['EmailAddress'], $Campaign1ID, $FeedbackEmail1['EmailID'], 'Subscriber Feedback 2');
    sleep(2); //for a different HistoryDate

    //add 1 feedback from subscriber 2
    Subscribers::AddSubscriberFeedback($UserID, $FeedbackSubscriber2['SubscriberID'], $FeedbackSubscriber2['EmailAddress'], $Campaign1ID, $FeedbackEmail1['EmailID'], 'Subscriber Feedback 3');
    sleep(2); //for a different HistoryDate
    //add 1 feedback from subscriber 3 of user 2
    Subscribers::AddSubscriberFeedback($User2ID, $FeedbackSubscriber3['SubscriberID'], $FeedbackSubscriber3['EmailAddress'], $Campaign2ID, $FeedbackEmail2['EmailID'], 'Subscriber Feedback 4');

    //test if only feedbacks from User1 are retrieved
    $Feedbacks = Subscribers::ReadHistory($UserID, 0, array('HistoryDate' => 'DESC'), 0, 100,
      array(
        Subscribers::HISTORY_UNSUBSCRIPTION_FEEDBACK,
        Subscribers::HISTORY_SUBSCRIBER_FEEDBACK
      ));
    $this->assertTrue(count($Feedbacks) == 3, $message . ' 3 feedbacks read');
    // each message should contain the decoded email address ...
    $emails = variable_get('drupal_test_email_collector', []);
    foreach ($emails as $index => $m) {
      $this->assertTrue(strpos($m['body'], $AllEmailAddresses[$index]) !== FALSE,
        $message . " contains decoded email address " . $AllEmailAddresses[$index] . ": " . $m['body']);
    }

    //test sort
    $FeedbacksASC = Subscribers::ReadHistory($UserID, 0, array('HistoryDate' => 'ASC'), 0, 100,
      array(
        Subscribers::HISTORY_UNSUBSCRIPTION_FEEDBACK,
        Subscribers::HISTORY_SUBSCRIBER_FEEDBACK
      ));
    $FeedbacksASC = array_reverse($FeedbacksASC, FALSE);

    $sorted = 0;
    foreach ($FeedbacksASC as $index => $feedback) {
      if (!empty($feedback['HistoryData']['Feedback']) && $feedback['HistoryData']['Feedback'] == $Feedbacks[$index]['HistoryData']['Feedback']) {
        $sorted++;
      }
    }

    $this->assertTrue(count($Feedbacks) == $sorted, $message . ' 3 feedbacks sorted');

    //test pager (get the last 2 feedbacks)
    $FeedbackPager = Subscribers::ReadHistory($UserID, 0, array('HistoryDate' => 'DESC'), 1, 2,
      array(
        Subscribers::HISTORY_UNSUBSCRIPTION_FEEDBACK,
        Subscribers::HISTORY_SUBSCRIBER_FEEDBACK
      ));
    $p1 = ($FeedbackPager[0]['HistoryData']['Feedback'] == $Feedbacks[1]['HistoryData']['Feedback']);
    $p2 = ($FeedbackPager[1]['HistoryData']['Feedback'] == $Feedbacks[2]['HistoryData']['Feedback']);

    $this->assertTrue($p1 && $p2, $message . ' feedback pager');

    /**
     * Single Optin
     **/
    $message = 'Subscribers::Subscribe single optin';
    // create a subscriber
    $UserFieldValue = 'Userfield-Value';
    $GlobalFieldValue = 'Globalfield-Value';
    $IPAddress = '***********';
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArraySOSubscriberList,
      'EmailAddress' => $SOEmailAddress,
      'IPAddress' => $IPAddress,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
      'OtherFields' => array(
        'CustomField' . $UserCustomFieldInformation['CustomFieldID'] => $UserFieldValue,
        'CustomField' . $GlobalCustomFieldInformation['CustomFieldID'] => $GlobalFieldValue,
      ),
    );

    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0], $message . ' add subscriber');
    $SOSubscriberID = $Result[1];

    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SOSubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriber['EmailAddress'] == $SOEmailAddress, $message . ' EmailAddress');
    $this->assertTrue($FullSubscriber['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $message . ' Status');

    /*
     * some state checks
     */

    $ArrayFieldAndValues = array(
      'Name' => 'list 1',
      'RelOwnerUserID' => $UserID,
    );
    $QuickListID1 = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($QuickListID1 > 0, $message);
    $QuickList1 = Lists::RetrieveListByID($UserID, $QuickListID1);

    $ArrayFieldAndValues = array(
      'Name' => 'list 2',
      'RelOwnerUserID' => $UserID,
    );
    $QuickListID2 = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($QuickListID2 > 0, $message);
    $QuickList2 = Lists::RetrieveListByID($UserID, $QuickListID2);

    // subscribe twice same list
    $message = 'quick1';
    $EmailAddress = $message . '@example.com';
    $this->subscribeCheck($message . '-1', $UserID, $QuickList1, $EmailAddress, 0, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_OPTIN);
    $this->subscribeCheck($message . '-2', $UserID, $QuickList1, $EmailAddress, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->subscribeCheck($message . '-3', $UserID, $QuickList1, $EmailAddress, 0, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);

    // subscribe twice different lists
    $message = 'quick2';
    $EmailAddress = $message . '@example.com';
    $this->subscribeCheck($message . '-1', $UserID, $QuickList1, $EmailAddress, 0, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_OPTIN);
    $this->subscribeCheck($message . '-2', $UserID, $QuickList2, $EmailAddress, 0, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_OPTIN);
    $this->subscribeCheck($message . '-3', $UserID, $QuickList1, $EmailAddress, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->subscribeCheck($message . '-4', $UserID, $QuickList2, $EmailAddress, 0, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->subscribeCheck($message . '-5', $UserID, $QuickList2, $EmailAddress, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);

    // subscribe and re-subscribe in another list
    $message = 'quick3';
    $EmailAddress = $message . '@example.com';
    $this->subscribeCheck($message . '-1', $UserID, $QuickList1, $EmailAddress, 0, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_OPTIN);
    $this->subscribeCheck($message . '-2', $UserID, $QuickList1, $EmailAddress, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->subscribeCheck($message . '-3', $UserID, $QuickList2, $EmailAddress, 0, 0, array(), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);

    //Tests: re-subscribe with OptInSubscribeTo and AutoSubscribe, AutoUnsubscribe, CustomFields

    //create DOI list
    $ArrayFieldAndValues = array(
      'Name' => 'DOI',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE,
    );
    $DOI_ListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($DOI_ListID > 0, 'DOI list created');
    $DOI_List = Lists::RetrieveListByID($UserID, $DOI_ListID);

    //create different DOI list
    $ArrayFieldAndValues = array(
      'Name' => 'diffDOI',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE,
    );
    $diffDOI_ListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($diffDOI_ListID > 0, 'diffDOI list created');
    $diffDOI_List = Lists::RetrieveListByID($UserID, $diffDOI_ListID);

    //create different DOI list
    $ArrayFieldAndValues = array(
      'Name' => 'diffDOI',
      'RelOwnerUserID' => $UserID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE,
    );
    $diffDOI_ListID = Lists::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($diffDOI_ListID > 0, 'diffDOI list created');
    $diffDOI_List = Lists::RetrieveListByID($UserID, $diffDOI_ListID);

    //create 5 AutoSubscribeTo tags
    $OIST_TagID = array();
    for ($t = 1; $t <= 5; $t++) {
      $TagValues = array(
        'EntityID' => 100 + $t,
        'RelOwnerUserID' => $UserID,
        'Category' => Tag::CATEGORY_MANUAL,
      );
      $OIST_TagID[$t] = Tag::CreateTag($TagValues);
    }

    $message = "AutoSubscribe, CustomFields ";
    $Email = '<EMAIL>';
    $CustomFieldFirstName = CustomFields::RetrieveCustomField('FirstName', $UserID);
    $FirstName = 'new_subscriber';
    $SubscribeToTag = $OIST_TagID[1];
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, 0, $SubscribeToTag, array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_OPTIN);
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $SubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' new Subscriber status optin pending tagged');
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname = $FirstName");

    //optin confirm
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $SubscribeToTag, array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $SubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' new Subscriber optin confirmed AutoSubscribeTo tagged');
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname = $FirstName");

    //resubscribe, same list, different OptInSubscribeTo tag, custom field changed
    $FirstName = 'new_subscriber changed';
    $ExistingSubscribeToTag = $OIST_TagID[1];
    $SubscribeToTag = $OIST_TagID[2]; //change tag
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $SubscribeToTag, array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $ExistingSubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' new Subscriber resubscribed first tagged with Tag1');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $SubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' new Subscriber resubscribe tagged with Tag2');
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname = $FirstName");

    //resubscribe, different list, different OptInSubscribeTo tag with auto subscribe and auto unsubscribe, custom field change (umlaute)

    //add autosubscribe, autounsubscribe to tag3
    $Tag3 = Tag::RetrieveTag($UserID, $OIST_TagID[3]);
    $Tag3['TagData'] = array();
    $Tag3['TagData']['OptInSubscribeTo'] = array($OIST_TagID[4]); //auto subscribe to tag 4
    $Tag3['TagData']['OptInUnsubscribeFrom'] = array($OIST_TagID[1]); //auto unsubscribe from tag 1
    Tag::UpdateTag($Tag3);
    $Tag3 = Tag::RetrieveTag($UserID, $OIST_TagID[3]);
    $TagOptions = unserialize($Tag3['TagData']);
    $this->assertTrue(in_array($OIST_TagID[4], $TagOptions['OptInSubscribeTo']), $message . "OptInSubscribeTo set for Tag3 (Tag4)");
    $this->assertTrue(in_array($OIST_TagID[1], $TagOptions['OptInUnsubscribeFrom']), $message . "OptInUnsubscribeFrom set for Tag3 (Tag1)");

    $FirstName = 'new_subscriber Hänsel';
    $SubscribeToTag = $OIST_TagID[3]; //change tag
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $SubscribeToTag, array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $SubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' new Subscriber resubscribe Tag3 subscribed');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[4], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' new Subscriber resubscribe: Tag4 auto subscribed by Tag3');
    $this->assertTrue(!Subscribers::RetrieveTagging($UserID, $OIST_TagID[1], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' new Subscriber resubscribe: Tag1 unsubscribed');
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname (Umlaut) = $FirstName");

    //create Tag5 with autosubscribe Tag2
    $TagValues = array(
      'EntityID' => 106,
      'RelOwnerUserID' => $UserID,
      'Category' => Tag::CATEGORY_MANUAL,
      'TagData' => array(
        'OptInSubscribeTo' => array($OIST_TagID[5]), //auto subscribe Tag2
      ),
    );
    $OIST_TagID[6] = Tag::CreateTag($TagValues);
    $this->assertTrue($OIST_TagID[6] > 0, $message . ' Tag6 created');
    $Tag6 = Tag::RetrieveTag($UserID, $OIST_TagID[6]);
    $TagOptions = unserialize($Tag6['TagData']);
    $this->assertTrue(in_array($OIST_TagID[5], $TagOptions['OptInSubscribeTo']), $message . "OptInSubscribeTo set for Tag6 (Tag5)");

    //Test Case: resubscribe different list
    $message = "AutoSubscribe, CustomFields resubscribe different list ";
    $SubscribeToTag = $OIST_TagID[6]; //Tag6 with autosubscribe Tag5
    $FirstName = 'new_subscriber resubscribe diff list';
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $diffDOI_List, $Email, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $SubscribeToTag, array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $SubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' resubscribe diff list: Tag6 subscribed');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[5], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' resubscribe diff list: Tag5 auto subscribed by Tag6');
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname changed = $FirstName");

    //Test case: OptIn confirm different list
    //OptIn list1
    $message = "AutoSubscribe, CustomFields OptIn confirm different list ";
    $Email = '<EMAIL>';
    $FirstName = 'new_subscriber diff list (pending)';
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $diffDOI_List, $Email, 0, array(), array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_OPTIN);
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname = $FirstName");

    //OptIn confirm list2
    $FirstName = 'new_subscriber diff list OptIn confirm';
    $SubscribeToTag = $OIST_TagID[6]; //Tag6 with Auto-Subscribe Tag5
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $diffDOI_List, $Email, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $SubscribeToTag, array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $SubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' OptIn confirm diff list: Tag6 subscribed');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[5], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' OptIn confirm diff list:: Tag5 auto subscribed by Tag6');
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname changed = $FirstName");

    //create Tag7 with AutoSubscribe Tag1 and Tag6 (Tag6 has AutoSubscribe Tag5)
    $TagValues = array(
      'EntityID' => 107,
      'RelOwnerUserID' => $UserID,
      'Category' => Tag::CATEGORY_MANUAL,
      'TagData' => array(
        'OptInSubscribeTo' => array($OIST_TagID[1], $OIST_TagID[6]),
        //auto subscribe Tag2
      ),
    );
    $OIST_TagID[7] = Tag::CreateTag($TagValues);
    $this->assertTrue($OIST_TagID[7] > 0, $message . ' Tag7 created');
    $Tag7 = Tag::RetrieveTag($UserID, $OIST_TagID[7]);
    $TagOptions = unserialize($Tag7['TagData']);
    $this->assertTrue(in_array($OIST_TagID[1], $TagOptions['OptInSubscribeTo']), $message . "OptInSubscribeTo set for Tag7 (Tag1)");
    $this->assertTrue(in_array($OIST_TagID[6], $TagOptions['OptInSubscribeTo']), $message . "OptInSubscribeTo set for Tag7 (Tag6)");

    //Test case: Resubscribe without Status
    $message = "Resubscribe without Status ";
    $Email = '<EMAIL>';
    $FirstName = 'OptIn/Subscribe';
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, 0, array(), array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_OPTIN);
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname = $FirstName");
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, array(), array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);

    $FirstName = 'Resubscribe without Status';
    $SubscribeToTag = $OIST_TagID[7]; //Tag7 with Auto-Subscribe Tag1 and Tag6 (Tag6 has AutoSubscribe Tag5)
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, 0, $SubscribeToTag, array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $SubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag7 subscribed');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[1], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag1 subscribed');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[6], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag6 auto subscribed by Tag7');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[5], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag5 auto subscribed by Tag6');
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname changed = $FirstName");

    //Test case: resubscribe no ListID
    //OptIn with ListID
    $message = "Resubscribe without ListID ";
    $Email = '<EMAIL>';
    $FirstName = 'OptIn/Subscribe with ListID';
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, 0, array(), array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_OPTIN);
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname = $FirstName");
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, array(), array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);

    $FirstNameEmpty = ''; //existing value should be overwritten
    $SubscribeToTag = $OIST_TagID[7]; //Tag7 with Auto-Subscribe Tag1 and Tag6 (Tag6 has AutoSubscribe Tag5)
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, array('ListID' => 0), $Email, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $SubscribeToTag, array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstNameEmpty), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $SubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag7 subscribed');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[1], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag1 subscribed');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[6], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag6 auto subscribed by Tag7');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[5], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag5 auto subscribed by Tag6');
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstNameEmpty, "CustomField Firstname overwritten");

    //Test case: resubscribe without ListID and Status
    //OptIn with ListID
    $message = "Resubscribe without ListID and Status ";
    $Email = '<EMAIL>';
    $FirstName = 'OptIn/Subscribe with ListID';
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, 0, array(), array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_OPTIN);
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname = $FirstName");
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, $DOI_List, $Email, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, array(), array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);

    $FirstName = 'Resubscribe without Status';
    $SubscribeToTag = $OIST_TagID[7]; //Tag7 with Auto-Subscribe Tag1 and Tag6 (Tag6 has AutoSubscribe Tag5)
    $SubscriptionInfo = $this->subscribeCheck($message . $Email, $UserID, array('ListID' => 0), $Email, 0, $SubscribeToTag, array("CustomField" . $CustomFieldFirstName['CustomFieldID'] => $FirstName), TRUE, Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED);
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $SubscribeToTag, $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag7 subscribed');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[1], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag1 subscribed');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[6], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag6 auto subscribed by Tag7');
    $this->assertTrue(Subscribers::RetrieveTagging($UserID, $OIST_TagID[5], $SubscriptionInfo['SubscriberID'], $ReferenceID), $message . ' Tag5 auto subscribed by Tag6');
    $this->assertTrue(CustomFields::GetCustomFieldData($UserID, $SubscriptionInfo['SubscriberID'], $CustomFieldFirstName, $ReferenceID) == $FirstName, "CustomField Firstname changed = $FirstName");

    // --- import tests ---

    $message = 'Subscribers::Subscribe Import ';
    $ImportListInformation = array(
      'ListID' => 0,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE,
    );

    $EmailAddress = "<EMAIL>";

    $ImportOptInDate = strtotime("2013-01-01 12:34:56");
    $ImportConfirmationDate = strtotime("2013-01-01 12:35:56");
    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ImportListInformation,
      'EmailAddress' => $EmailAddress,
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'OptInSubscribeTo' => 0,
      'OtherFields' => array(),
      'IPAddress' => '***************',
      'SendConfirmationEmail' => FALSE,
      'UpdateIfUnsubscribed' => FALSE,
      'TriggerAutoResponders' => TRUE,
      'ImportOptInDate' => $ImportOptInDate,
      'ImportOptInIPAddress' => '0.0.0.0',
      'ImportConfirmationDate' => $ImportConfirmationDate,
      'SubscriptionReferrer' => str_pad("http://www.google.de/", 2000, 'x'),
    );

    $Import = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Import[0], $message . $Parameters['EmailAddress'] . ' imported');
    $SubscriberID = $Import[1];
    $FullSubscriberWithFields = Subscribers::RetrieveFullSubscriberWithFields($Parameters['UserID'], $SubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriberWithFields['OptInDate'] == $ImportOptInDate, $message . 'ImportOptInDate set');
    $this->assertTrue($FullSubscriberWithFields['SubscriptionDate'] == $ImportConfirmationDate, $message . 'SubscriptionDate = ImportOptInDate');
    $this->assertTrue($FullSubscriberWithFields['SubscriptionStatus'] == Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED, $message . 'SubscriptionStatus = Subscribed');
    $this->assertTrue($FullSubscriberWithFields['SubscriptionIP'] == $Parameters['IPAddress'], $message . 'SubscriptionIP');
    $this->assertTrue($FullSubscriberWithFields['OptInIP'] == $Parameters['ImportOptInIPAddress'], $message . 'OptInIP');
    $this->assertTrue($FullSubscriberWithFields['SubscriptionReferrer'] == substr($Parameters['SubscriptionReferrer'], 0, 1024), $message . 'SubscriptionReferrer set'.strlen($FullSubscriberWithFields['SubscriptionReferrer']).$Parameters['SubscriptionReferrer']);


    //check negative conditions
    //Subscribers::NegativeConditionsMatching
    $TestData = array(
      //Campaigns::TAG_HAS_NOT_ANY -> (!A AND !B AND !C)
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
        'Tagging' => array(1, 2, 3),
        'Expected' => TRUE,
        'message' => 'NegativeConditionsMatching (has not any): No match, contact has all tags',
      ),
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
        'Tagging' => array(1, 2, 4),
        'Expected' => TRUE,
        'message' => 'NegativeConditionsMatching (has not any): No match, contact has 2 of 3 tags',
      ),
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
        'Tagging' => array(1, 4, 5),
        'Expected' => TRUE,
        'message' => 'NegativeConditionsMatching (has not any): No match, contact has 1 of 3 tags',
      ),
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
        'Tagging' => array(5, 6, 7),
        'Expected' => FALSE,
        'message' => 'NegativeConditionsMatching (has not any): Match, contact has 0 of 3 tags',
      ),
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
        'Tagging' => array(),
        'Expected' => FALSE,
        'message' => 'NegativeConditionsMatching (has not any): Match, contact has no tags at all',
      ),
      array(
        'NotTaggedWith' => array(1),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
        'Tagging' => array(1, 2, 3),
        'Expected' => TRUE,
        'message' => 'NegativeConditionsMatching (has not any): No match, contact has the one tag',
      ),
      array(
        'NotTaggedWith' => array(4),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ANY,
        'Tagging' => array(1, 2, 3),
        'Expected' => FALSE,
        'message' => 'NegativeConditionsMatching (has not any): Match, contact does not have the one tag',
      ),
      //Campaigns::TAG_HAS_NOT_ALL -> at least one tag is missing
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ALL,
        'Tagging' => array(1, 2, 3),
        'Expected' => TRUE,
        'message' => 'NegativeConditionsMatching (has not all): No match, contact has all tags',
      ),
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ALL,
        'Tagging' => array(1, 2, 4),
        'Expected' => FALSE,
        'message' => 'NegativeConditionsMatching (has not all): Match, contact has only 2 of 3 tags',
      ),
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ALL,
        'Tagging' => array(1, 4, 5),
        'Expected' => FALSE,
        'message' => 'NegativeConditionsMatching (has not all): Match, contact has only 1 of 3 tags',
      ),
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ALL,
        'Tagging' => array(5, 6, 7),
        'Expected' => FALSE,
        'message' => 'NegativeConditionsMatching (has not all): Match, contact has 0 of 3 tags',
      ),
      array(
        'NotTaggedWith' => array(1, 2, 3),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ALL,
        'Tagging' => array(),
        'Expected' => FALSE,
        'message' => 'NegativeConditionsMatching (has not all): Match, contact has no tags at all',
      ),
      array(
        'NotTaggedWith' => array(1),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ALL,
        'Tagging' => array(1, 2, 3),
        'Expected' => TRUE,
        'message' => 'NegativeConditionsMatching (has not any): No match, contact has the one tag',
      ),
      array(
        'NotTaggedWith' => array(4),
        'OpNotTaggedWith' => Campaigns::TAG_HAS_NOT_ALL,
        'Tagging' => array(1, 2, 3),
        'Expected' => FALSE,
        'message' => 'NegativeConditionsMatching (has not any): Match, contact does not have the one tag',
      ),
    );

    foreach ($TestData as $data) {
      $NegativeCheck = Subscribers::NegativeConditionsMatching($UserID, 1, $ReferenceID, $data, $data['Tagging']);
      $this->assertTrue($NegativeCheck === $data['Expected'], $data['message']);
    }

    $message = 'Subscribers::SelectDummySubscriber ';

    $DummySubscriber = Subscribers::SelectDummySubscriber($UserID);

    $currentTime = time();
    $this->assertTrue(is_int($DummySubscriber['SubscriberID']), $message . 'SubscriberID is an integer');
    $this->assertEqual($DummySubscriber['RelOwnerUserID'], $UserID,  $message . 'RelOwnerUserID is uid of current user');
    $this->assertEqual($DummySubscriber['EmailAddress'], $account->mail, $message . 'EmailAddress is email of current user');
    $this->assertTrue(is_string($DummySubscriber['CustomFieldFirstName']) && !empty($DummySubscriber['CustomFieldFirstName']), $message . 'FirstName is an non-empty string');
    $this->assertTrue(is_string($DummySubscriber['CustomFieldLastName']) && !empty($DummySubscriber['CustomFieldLastName']), $message . 'lastName is an non-empty string');

    $this->assertEqual($DummySubscriber['BounceType'], Subscribers::BOUNCETYPE_NOTBOUNCED,  $message. 'BounceType notbounced');
    $this->assertTrue(($DummySubscriber['SubscriptionDate'] - $currentTime) <= 1, $message. 'SubscriptionDate is set to current timestamp');
    $this->assertEqual($DummySubscriber['SMSBounceType'], Subscribers::BOUNCETYPE_NOTBOUNCED,  $message. 'SMSBounceType notbounced');
    $this->assertTrue(($DummySubscriber['SMSSubscriptionStatus'] - $currentTime) <= 1, $message. 'SMSSubscriptionStatus is set to current timestamp');

    $this->assertTrue(($DummySubscriber['LastOpenDate'] - $currentTime) <= 1, $message. 'LastOpenDate is set to current timestamp');
    $this->assertTrue(($DummySubscriber['UnsubscriptionDate'] - $currentTime) <= 1, $message. 'SubscriptionDate is set to current timestamp');
    $this->assertTrue(($DummySubscriber['OptInDate'] - $currentTime) <= 1, $message. 'OptInDate is set to current timestamp');
    $this->assertTrue(($DummySubscriber['SMSSubscriptionDate'] - $currentTime) <= 1, $message. 'SMSSubscriptionDate is set to current timestamp');
    $this->assertTrue(($DummySubscriber['SMSUnsubscriptionDate'] - $currentTime) <= 1, $message. 'SMSUnsubscriptionDate is set to current timestamp');
    $this->assertTrue(($DummySubscriber['CustomFieldBirthday'] - $currentTime) <= 1, $message. 'CustomFieldBirthday is set to current timestamp');

    $this->assertTrue(filter_var($DummySubscriber['LastOpenIP'], FILTER_VALIDATE_IP),$message. 'LastOpenIP is a valid ip');
    $this->assertTrue(filter_var($DummySubscriber['SubscriptionIP'], FILTER_VALIDATE_IP), $message. 'SubscriptionIP is a valid ip');
    $this->assertTrue(filter_var($DummySubscriber['UnsubscriptionIP'], FILTER_VALIDATE_IP), $message. 'UnsubscriptionIP is a valid ip');
    $this->assertTrue(filter_var($DummySubscriber['OptInIP'], FILTER_VALIDATE_IP), $message. 'OptInIP is a valid ip');


    /* READ HISTORY TESTS */

    // ReadHistory

    $message = 'HISTORY: create transactional entries';

    $UserIDHistory = 42;
    $SubscriberIDHistory = 42;
    $ReferenceIDHistory = 42;

    // create NL
    $CampaignHistoryNameNL = 'History Campaign NL';
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserIDHistory,
      'CampaignName' => $CampaignHistoryNameNL,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_CAMPAIGN,
    );
    $CampaignIDHistoryNL = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($CampaignIDHistoryNL, $message . " -> newsletter created");

    db_query("INSERT INTO {".NewsletterQueue::TABLE_NAME."} (RelOwnerUserID, RelSubscriberID, ReferenceID, RelAutoResponderID, StatusEnum, StatusReason) VALUES (:RelOwnerUserID, :RelSubscriberID, :ReferenceID, :RelAutoResponderID, :StatusEnum, :StatusReason)", [
      ":RelOwnerUserID" => $UserIDHistory,
      ":RelSubscriberID" => $SubscriberIDHistory,
      ":ReferenceID" => $ReferenceIDHistory,
      ":RelAutoResponderID" => $CampaignIDHistoryNL,
      ":StatusEnum" => TransactionEmails::STATUS_FAILED,
      ":StatusReason" => TRANSACTION_REASON_SUBSCRIBER_MATCHED_NEGATIVES
    ]);


    // create AR
    $CampaignHistoryNameAR = 'History Campaign AR';
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserIDHistory,
      'CampaignName' => $CampaignHistoryNameAR,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_SUBSCRIPTION,
    );
    $CampaignIDHistoryAR = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($CampaignIDHistoryAR, $message . " -> autoresponder created");

    db_query("INSERT INTO {".TransactionalQueue::TABLE_NAME."} (RelOwnerUserID, RelSubscriberID, ReferenceID, RelAutoResponderID, StatusEnum, StatusReason) VALUES (:RelOwnerUserID, :RelSubscriberID, :ReferenceID, :RelAutoResponderID, :StatusEnum, :StatusReason)", [
      ":RelOwnerUserID" => $UserIDHistory,
      ":RelSubscriberID" => $SubscriberIDHistory,
      ":ReferenceID" => $ReferenceIDHistory,
      ":RelAutoResponderID" => $CampaignIDHistoryAR,
      ":StatusEnum" => TransactionEmails::STATUS_FAILED,
      ":StatusReason" => TRANSACTION_REASON_SUBSCRIBER_MATCHED_NEGATIVES,
    ]);

     $HistoryArray = Subscribers::ReadHistory($UserIDHistory, $SubscriberIDHistory, ['HistoryDate' => 'DESC'], 0, 10, [], [$ReferenceIDHistory]);

    $message = 'Subscribers::ReadHistory: ';

    $this->assertTrue(is_array($HistoryArray), $message . "history result is an array");
    $this->assertTrue(count($HistoryArray) == 2, $message . "history array holds 2 entries");

    // AR
    $this->assertTrue($HistoryArray[0]["HistoryType"] == Subscribers::HISTORY_SENDFAILED, $message . "type: " . $HistoryArray[0]["HistoryType"] . " (should be: ". Subscribers::HISTORY_SENDFAILED . ")");
    $this->assertTrue(strpos($HistoryArray[0]["Text"], $CampaignHistoryNameAR) !== FALSE, $message . "text: " . $HistoryArray[0]["Text"] . " (should be: ". $CampaignHistoryNameAR . ")");

    // NL
    $this->assertTrue($HistoryArray[1]["HistoryType"] == Subscribers::HISTORY_SENDFAILED, $message . "type: " . $HistoryArray[1]["HistoryType"] . " (should be: ". Subscribers::HISTORY_SENDFAILED . ")");
    $this->assertTrue(strpos($HistoryArray[1]["Text"], $CampaignHistoryNameNL) !== FALSE, $message . "text: " . $HistoryArray[1]["Text"] . " (should be: ". $CampaignHistoryNameNL . ")");

    // ReadPendingHistory

    $SubscriberIDHistory2 = 43;

    // create NL
    db_query("INSERT INTO {".NewsletterQueue::TABLE_NAME."} (RelOwnerUserID, RelSubscriberID, ReferenceID, RelAutoResponderID, StatusEnum) VALUES (:RelOwnerUserID, :RelSubscriberID, :ReferenceID, :RelAutoResponderID, :StatusEnum)", [
      ":RelOwnerUserID" => $UserIDHistory,
      ":RelSubscriberID" => $SubscriberIDHistory2,
      ":ReferenceID" => $ReferenceIDHistory,
      ":RelAutoResponderID" => $CampaignIDHistoryNL,
      ":StatusEnum" => TransactionEmails::STATUS_PENDING
    ]);

    // create AR
    $SubscriberIDHistory2 = 43;
    db_query("INSERT INTO {".TransactionalQueue::TABLE_NAME."} (RelOwnerUserID, RelSubscriberID, ReferenceID, RelAutoResponderID, StatusEnum) VALUES (:RelOwnerUserID, :RelSubscriberID, :ReferenceID, :RelAutoResponderID, :StatusEnum)", [
      ":RelOwnerUserID" => $UserIDHistory,
      ":RelSubscriberID" => $SubscriberIDHistory2,
      ":ReferenceID" => $ReferenceIDHistory,
      ":RelAutoResponderID" => $CampaignIDHistoryAR,
      ":StatusEnum" => TransactionEmails::STATUS_PENDING
    ]);


    $HistoryArray = Subscribers::ReadPendingHistory($UserIDHistory, $SubscriberIDHistory2, ['HistoryDate' => 'DESC'], [$ReferenceIDHistory]);

    $message = 'Subscribers::ReadPendingHistory: ';

    $this->assertTrue(is_array($HistoryArray), $message . "history result is an array");
    $this->assertTrue(count($HistoryArray) == 2, $message . "history array holds 2 entries");

    // AR
    $this->assertTrue($HistoryArray[0]["HistoryType"] == Subscribers::HISTORY_PROCESSING, $message . "type: " . $HistoryArray[0]["HistoryType"] . " (should be: ". Subscribers::HISTORY_PROCESSING . ")");
    $this->assertTrue($HistoryArray[0]["HistoryData"]["CampaignID"] == $CampaignIDHistoryAR, $message . "CampaignID: " . $HistoryArray[0]["HistoryData"]["CampaignID"] . " (should be: ". $CampaignIDHistoryAR . ")");
    $this->assertTrue($HistoryArray[0]["HistoryData"]["SubscriberID"] == $SubscriberIDHistory2, $message . "SubscriberID: " . $HistoryArray[0]["HistoryData"]["SubscriberID"] . " (should be: ". $SubscriberIDHistory2 . ")");
    $this->assertTrue(strpos($HistoryArray[0]["Text"], $CampaignHistoryNameAR) !== FALSE, $message . "text: " . $HistoryArray[0]["Text"] . "(should be: ". $CampaignHistoryNameAR . ")");

    // NL
    $this->assertTrue($HistoryArray[1]["HistoryType"] == Subscribers::HISTORY_PROCESSING, $message . "type: " . $HistoryArray[1]["HistoryType"] . " (should be: ". Subscribers::HISTORY_PROCESSING . ")");
    $this->assertTrue($HistoryArray[1]["HistoryData"]["CampaignID"] == $CampaignIDHistoryNL, $message . "CampaignID: " . $HistoryArray[1]["HistoryData"]["CampaignID"] . " (should be: ". $CampaignIDHistoryNL . ")");
    $this->assertTrue($HistoryArray[1]["HistoryData"]["SubscriberID"] == $SubscriberIDHistory2, $message . "SubscriberID: " . $HistoryArray[1]["HistoryData"]["SubscriberID"] . " (should be: ". $SubscriberIDHistory2 . ")");
    $this->assertTrue(strpos($HistoryArray[1]["Text"], $CampaignHistoryNameNL) !== FALSE, $message . "text: " . $HistoryArray[1]["Text"] . " (should be: ". $CampaignHistoryNameNL . ")");

  }

  ///////////// helpers

  function CalculateSenddateMinMaxRecipientsCheck($message, $UserID, $TriggerTypEnum, $RecipientLists, $RetrieveTaggedSubscribersCount) {
    $message = 'CSRC ' . $message;

    // don't use a timeout
    variable_set(CampaignsNewsletter::$RecipientCalculationMaxTimeVariableName, 1000);

    // create a newsletter with the given recipients
    $ArrayFieldAndValues = array(
      'RelOwnerUserID' => $UserID,
      'CampaignName' => $message,
      'AutoResponderTriggerTypeEnum' => $TriggerTypEnum,
    );
    if (!empty($RecipientLists['OpTaggedWith'])) {
      $ArrayFieldAndValues['OpTaggedWith'] = $RecipientLists['OpTaggedWith'];
    }
    if (!empty($RecipientLists['TaggedWith'])) {
      $ArrayFieldAndValues['TaggedWith'] = $RecipientLists['TaggedWith'];
    }
    if (!empty($RecipientLists['OpNotTaggedWith'])) {
      $ArrayFieldAndValues['OpNotTaggedWith'] = $RecipientLists['OpNotTaggedWith'];
    }
    if (!empty($RecipientLists['NotTaggedWith'])) {
      $ArrayFieldAndValues['NotTaggedWith'] = $RecipientLists['NotTaggedWith'];
    }
    $CampaignID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);

    $Campaign = Campaigns::FromID($UserID, $CampaignID);

    // calc max estimates (see forms/campaign.inc)
    // 'all active subscribers with offset'
    $limitquery = "SELECT COUNT(*) FROM {subscription} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND SubscriptionStatus = :SubscriptionStatus AND BounceType != :BounceType " .
      " AND SubscriptionType = :SubscriptionType";
    $params = array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      ':BounceType' => Subscribers::BOUNCETYPE_HARD,
      ':SubscriptionType' => Campaigns::IsSMSCampaign($TriggerTypEnum) ? Subscription::SUBSCRIPTIONTYPE_SMS : Subscription::SUBSCRIPTIONTYPE_EMAIL,
    );
    $AllActive = db_query($limitquery, $params)->fetchField();

    // make the slice size big
    $MinEstimatedRecipients = 0;
    $MaxEstimatedRecipients = $AllActive;
    $RefreshOffset = $MaxEstimatedRecipients;
    variable_get(CampaignsNewsletter::$RecipientCalculationBatchSizeVariableName, $MaxEstimatedRecipients + 1000);

    // get estimation
    [$MinEstimatedRecipients, $MaxEstimatedRecipients, $TimerShallContinue, $NextRefreshOffset] =
      $Campaign->CalculateSenddateMinMaxRecipients($UserID, intval($RefreshOffset), intval($MinEstimatedRecipients), intval($MaxEstimatedRecipients));

    $this->assertTrue($MinEstimatedRecipients == $MaxEstimatedRecipients, "$message min = max ONE ($MinEstimatedRecipients, $MaxEstimatedRecipients, $RetrieveTaggedSubscribersCount)");
    $this->assertTrue($MinEstimatedRecipients == $RetrieveTaggedSubscribersCount, "$message min = rts ONE ($MinEstimatedRecipients, $MaxEstimatedRecipients, $RetrieveTaggedSubscribersCount)");

    // make the slice size small
    $MinEstimatedRecipients = 0;
    $MaxEstimatedRecipients = $AllActive;
    $RefreshOffset = $MaxEstimatedRecipients;
    variable_get(CampaignsNewsletter::$RecipientCalculationBatchSizeVariableName, 3);

    // get estimation
    $TimerShallContinue = TRUE;
    while ($TimerShallContinue) {
      [$MinEstimatedRecipients, $MaxEstimatedRecipients, $TimerShallContinue, $NextRefreshOffset] =
        $Campaign->CalculateSenddateMinMaxRecipients($UserID, intval($RefreshOffset), intval($MinEstimatedRecipients), intval($MaxEstimatedRecipients));
      $RefreshOffset = $NextRefreshOffset;
    }

    $this->assertTrue($MinEstimatedRecipients == $MaxEstimatedRecipients, "$message min = max SLICES ($MinEstimatedRecipients, $MaxEstimatedRecipients, $RetrieveTaggedSubscribersCount)");
    $this->assertTrue($MinEstimatedRecipients == $RetrieveTaggedSubscribersCount, "$message min = rts SLICES ($MinEstimatedRecipients, $MaxEstimatedRecipients, $RetrieveTaggedSubscribersCount)");

  }

  /**
   * Returns all subscribers matching given tag conditions.
   */
  function GetTaggedSubscribersQueryCheck($UserID, $TriggerTypEnum, $RecipientLists = [], $FirstOnly = FALSE, $WithSubscriptionDate = FALSE, $offset = 0, $size = 0) {

    [$query, $args] = Subscribers::GetTaggedSubscribersQuery($UserID, $TriggerTypEnum, $RecipientLists, $offset, $FirstOnly ? 1 : $size);

    return Subscribers::RetrieveFilteredSubscribers($query, $args, $FirstOnly, $WithSubscriptionDate);
  }

  /*
   * check min/max recipients + slices of GetTaggedSubscribersQuery
   */
  function RetrieveTaggedSubscribersCheck($message, $UserID, $TriggerTypEnum = -1, $RecipientLists = [], $FirstOnly = FALSE, $WithSubscriptionDate = FALSE) {

    if ($TriggerTypEnum < 0) {
      // 'all active' -> limit query not supported -> nothing to check
      return;
    };
    if ($FirstOnly) {
      // "first only" ignores "size" -> nothing to check
      return;
    };

    $ArraySubscribersAll = self::GetTaggedSubscribersQueryCheck($UserID, $TriggerTypEnum, $RecipientLists, $FirstOnly, $WithSubscriptionDate);
    $all = count($ArraySubscribersAll);

    // test estimate
    self::CalculateSenddateMinMaxRecipientsCheck($message, $UserID, $TriggerTypEnum, $RecipientLists, $all);

    // compare with "sliced" calls (see _klicktipp_create_send_queue)

    $message = 'RTSC ' . $message;

    $limitquery = "SELECT COUNT(*) FROM {subscription} " .
      " WHERE RelOwnerUserID = :RelOwnerUserID AND SubscriptionStatus = :SubscriptionStatus AND BounceType != :BounceType " .
      " AND SubscriptionType = :SubscriptionType";
    $params = array(
      ':RelOwnerUserID' => $UserID,
      ':SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      ':BounceType' => Subscribers::BOUNCETYPE_HARD,
      ':SubscriptionType' => Campaigns::IsSMSCampaign($TriggerTypEnum) ? Subscription::SUBSCRIPTIONTYPE_SMS : Subscription::SUBSCRIPTIONTYPE_EMAIL,
    );
    $TotalActive = db_query($limitquery, $params)->fetchField();

    $size = 11; // very small slices

    for ($offset = 0; $offset <= $TotalActive; $offset += $size) {
      // get "slice"
      $slice = self::GetTaggedSubscribersQueryCheck($UserID, $TriggerTypEnum, $RecipientLists, $FirstOnly, $WithSubscriptionDate, $offset, $size);

      // check if every subscriber from "slice" is in "all"
      $diff = array_diff_key($ArraySubscribersAll, $slice);
      $this->assertTrue(count($ArraySubscribersAll) == count($diff) + count($slice), "$message count $offset ".print_r(array(count($ArraySubscribersAll), count($diff), count($slice)),1));

      // remove "slice" from "all"
      $ArraySubscribersAll = $diff;
    }
    // there should be none left
    $after = count($ArraySubscribersAll);
    $this->assertTrue($after == 0, "$message left $after from $all total active $TotalActive");
  }

  function subscribeCheck($message, $UserID, $ArrayList, $EmailAddress, $Status, $OptInSubscribeTo, $OtherFields, $ExpectedResult, $ExpectedStatus) {
    $ReferenceID = 0;

    // subscribe and check state

    $message = 'Subscribers::Subscribe ' . $message;

    $Parameters = array(
      'UserID' => $UserID,
      'ListInformation' => $ArrayList,
      'EmailAddress' => $EmailAddress,
      'SubscriptionStatus' => $Status,
      'OptInSubscribeTo' => (empty($OptInSubscribeTo)) ? 0 : $OptInSubscribeTo,
      'OtherFields' => (empty($OtherFields)) ? array() : $OtherFields,
      'IPAddress' => '***********',
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    );

    $Result = Subscribers::Subscribe($Parameters);
    $this->assertTrue($Result[0] == $ExpectedResult, $message . ' result');

    // if error is expected, return error code
    if (!$ExpectedResult) {
      return $Result[1];
    }

    $SubscriberID = $Result[1];
    $FullSubscriber = Subscribers::RetrieveFullSubscriber($UserID, $SubscriberID, $ReferenceID);
    $this->assertTrue($FullSubscriber['EmailAddress'] == $EmailAddress, $message . ' EmailAddress');
    $this->assertTrue($FullSubscriber['SubscriptionStatus'] == $ExpectedStatus, $message . ' Status');

    return $FullSubscriber;
  }

  function subscribeValidationCheck($message, $Parameters, $ExpectedResult = TRUE, $ExpectedErrorCode = 0, $ExpectedParameters = []) {
    $message = 'SubscriptionParameterValidation ' . $message;

    [$Result, $ResultParameters] = Subscribers::SubscriptionParameterValidation($Parameters, FALSE);
    $this->assertTrue($Result == $ExpectedResult, $message . ' result');

    if ($ExpectedResult) {
      foreach($ExpectedParameters as $key => $value) {
        $this->assertTrue($ResultParameters[$key] == $ExpectedParameters[$key], $message . ' '.$key);
      }
    }
    else {
      // if error is expected, check first error code
      $FirstError = reset($ResultParameters);
      $this->assertTrue($FirstError['ErrorCode'] == $ExpectedErrorCode, $message . ' ErrorCode');
    }
  }

  function createAR($Name, $UserID, $ManualTagID) {

    // create autoresponder
    $message = 'create autoresponder ' . $Name;
    $ArrayFieldAndValues = array(
      'CampaignName' => $Name,
      'RelOwnerUserID' => $UserID,
      'AutoResponderTriggerTypeEnum' => Campaigns::TRIGGER_TYPE_SUBSCRIPTION,
      'OpTaggedWith' => Campaigns::TAG_HAS_ANY,
      'TaggedWith' => array($ManualTagID),
    );
    $AutoResponderID = CampaignsNewsletter::InsertDB($ArrayFieldAndValues);
    $this->assertTrue($AutoResponderID > 0, $message . ' id');
    $ArrayAutoResponder = Campaigns::RetrieveCampaignByID($AutoResponderID);

    // fill email with content
    /** @var Emails $ObjectEmail */
    $ObjectEmail = Emails::FromID($UserID, $ArrayAutoResponder['RelEmailID']);
    $ArrayEmail = $ObjectEmail->GetData();
    $ArrayEmail['ContentTypeEnum'] = Emails::CONTENT_TYPE_PLAIN;
    $ArrayEmail['Subject'] = 'no email without subject';
    $ArrayEmail['PlainContent'] = 'some plain content, so email will be send';
    $ObjectEmail->UpdateDB($ArrayEmail);

    // start autoresponder ( thats what web_send.php does essentially)
    $message = 'start autoresponder';
    $ArrayFieldnValues = array(
      'CampaignStatusEnum' => Campaigns::STATUS_SENDING,
    );
    Campaigns::UpdateCampaign($UserID, $AutoResponderID, $ArrayFieldnValues);
    $ArrayAutoResponder = Campaigns::RetrieveCampaignByID($AutoResponderID);
    $this->assertTrue($ArrayAutoResponder['CampaignStatusEnum'] == Campaigns::STATUS_SENDING, $message . ' is sending');

    return $AutoResponderID;
  }

  // TODO: DEV-2279 get all references of all tags for a subscriber
  function getTagsAndReferences ($UserID, $SubscriberID) {
    $result = db_query(
      "SELECT RelTagID AS TagID, ReferenceID FROM {tagging} WHERE RelOwnerUserID = :RelOwnerUserID AND RelSubscriberID = :RelSubscriberID",
      [":RelOwnerUserID" => $UserID, ':RelSubscriberID' => $SubscriberID]
    );

    return $result->fetchAll(PDO::FETCH_GROUP|PDO::FETCH_COLUMN);

  }

  public function subscriberQueueQueueWorker(): void
  {
    AmqpSubscriberQueueTestHelper::executeWorker();
  }
}


<?php

use App\Klicktipp\Campaigns;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\Dates;
use App\Klicktipp\Lists;
use App\Klicktipp\Subscribers;
use App\Klicktipp\SubscribersSearch;
use App\Klicktipp\Subscription;
use App\Klicktipp\Tag;


/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_search_subscribers_alternative_test($UserID, $search, $tablesort_header = array()) {
  $ReferenceID = 0;

  $SearchResult = array();

  //Search criteria
  $SearchForEmail = !empty($search['EmailSearch']['EmailAddressOp']);
  $SearchForSMS = !empty($search['SMSSearch']['SMSPhoneNumberOp']);
  $SearchForEmailStatus = !empty($search['EmailSearch']['Status']);
  $SearchForBounceStatus = !empty($search['EmailSearch']['BounceStatus']);
  $SearchForOptInDate = !empty($search['EmailSearch']['Subscription']['OpSubscriptionDate']);
  $SearchForSubscriptionDate = !empty($search['EmailSearch']['Subscription']['OpConfirmationDate']);
  $SearchForTaggedWith = !empty($search['TaggedWith']);
  $SearchForNotTaggedWith = !empty($search['NotTaggedWith']);

  //get allSubscriberIDs of user
  $AllSubscribersIDsResult = db_query("SELECT SubscriberID FROM {subscribers} WHERE RelOwnerUserID = :RelOwnerUserID", array(':RelOwnerUserID' => $UserID));

  //get all Subscriptions of subscribers
  $AllSubscriptions = array();
  while ($s = kt_fetch_array($AllSubscribersIDsResult)) {

    $sid = $s['SubscriberID'];
    $Subscription = Subscribers::RetrieveFullSubscriberWithFields($UserID, $sid, $ReferenceID);
    $AllSubscriptions[$sid] = $Subscription; //store all subscriptions, for information, count() etc

    // filter non sms if sms is criteria
    if ($search['SearchMode'] == SubscribersSearch::SEARCH_BY_PHONE_NUMBER && empty($Subscription['PhoneNumber'])) {
      continue;
    }

    //check if email/phone is a search criteria
    if ($SearchForEmail) {
      $ContactInfoOp = $search['EmailSearch']['EmailAddressOp'];
      $SubscriptionField = 'EmailAddress';
      $SearchField = $search['EmailSearch']['EmailAddress'];
    }
    else {
      $ContactInfoOp = $search['SMSSearch']['SMSPhoneNumberOp'];
      $SubscriptionField = 'PhoneNumber';
      $SearchField = $search['SMSSearch']['SMSPhoneNumber'];
    }
    if ($SearchForEmail || $SearchForSMS) {
      $matches = FALSE;
      switch ($ContactInfoOp) {
        case SubscribersSearch::IS:
          $matches = $Subscription[$SubscriptionField] == $SearchField && !empty($Subscription[$SubscriptionField]);
          break;
        case SubscribersSearch::IS_NOT:
          $matches = $Subscription[$SubscriptionField] != $SearchField && !empty($Subscription[$SubscriptionField]);
          break;
        case SubscribersSearch::CONTAINS:
          $matches = strpos($Subscription[$SubscriptionField], $SearchField) !== FALSE && !empty($Subscription[$SubscriptionField]);
          break;
        case SubscribersSearch::CONTAINS_NOT:
          $matches = strpos($Subscription[$SubscriptionField], $SearchField) === FALSE && !empty($Subscription[$SubscriptionField]);
          break;
        case SubscribersSearch::STARTS_WITH:
          $matches = strpos($Subscription[$SubscriptionField], $SearchField) === 0 && !empty($Subscription[$SubscriptionField]);
          break;
        case SubscribersSearch::STARTS_NOT_WITH:
          $matches = strpos($Subscription[$SubscriptionField], $SearchField) !== 0 && !empty($Subscription[$SubscriptionField]);
          break;
        case SubscribersSearch::ENDS_WITH:
          $matches = substr($Subscription[$SubscriptionField], -strlen($SearchField)) === $SearchField && !empty($Subscription[$SubscriptionField]);
          break;
        case SubscribersSearch::ENDS_NOT_WITH:
          $matches = substr($Subscription[$SubscriptionField], -strlen($SearchField)) !== $SearchField && !empty($Subscription[$SubscriptionField]);
          break;
        case SubscribersSearch::IS_EMPTY:
          $matches = empty($Subscription[$SubscriptionField]);
          break;
        case SubscribersSearch::IS_NOT_EMPTY:
          $matches = !empty($Subscription[$SubscriptionField]);
          break;
      }
      if (!$matches) {
        continue;
      }
    }

    //check email status
    if ($SearchForEmailStatus) {
      if (empty($Subscription['EmailAddress'])) {
        // email status only
        continue;
      }

      $SubscriptionStatus = $Subscription['SubscriptionStatus'];

      $one_week_back = time() - (7 * 24 * 60 * 60);
      if (in_array(SubscribersSearch::STATUS_PENDING_7DAYS, $search['EmailSearch']['Status']) && !in_array(SubscribersSearch::STATUS_PENDING, $search['EmailSearch']['Status']) && $SubscriptionStatus == Subscribers::SUBSCRIPTIONSTATUS_OPTIN && $Subscription['OptInDate'] < $one_week_back) {
        $SubscriptionStatus = 'pending_for_7_days';
      }
      else {
        if (in_array(SubscribersSearch::STATUS_IMPORT, $search['EmailSearch']['Status']) && $Subscription['RelListID'] == 0) {
          $SubscriptionStatus = 'import';
        }
      }

      $SubscriptionStatusToSearchStatus = array(
        Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED => SubscribersSearch::STATUS_SUBSCRIBED,
        Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED => SubscribersSearch::STATUS_UNSUBSCRIBED,
        Subscribers::SUBSCRIPTIONSTATUS_OPTIN => SubscribersSearch::STATUS_PENDING,
        'pending_for_7_days' => SubscribersSearch::STATUS_PENDING_7DAYS,
        'import' => SubscribersSearch::STATUS_IMPORT,
      );

      if (!in_array($SubscriptionStatusToSearchStatus[$SubscriptionStatus], $search['EmailSearch']['Status'])) {
        continue;
      } //EmailStatus doesn't match

    }

    //check bounce status
    if ($SearchForBounceStatus) {
      if (empty($Subscription['EmailAddress'])) {
        // email status only
        continue;
      }

      $BounceType = $Subscription['BounceType'];

      $BounceTypeToSearchBounce = array(
        Subscribers::BOUNCETYPE_NOTBOUNCED => Subscribers::BOUNCETYPE_NOTBOUNCED,
        Subscribers::BOUNCETYPE_SOFT => Subscribers::BOUNCETYPE_SOFT,
        Subscribers::BOUNCETYPE_SPAM => Subscribers::BOUNCETYPE_SPAM,
        Subscribers::BOUNCETYPE_HARD => Subscribers::BOUNCETYPE_HARD,
      );

      if (!in_array($BounceTypeToSearchBounce[$BounceType], $search['EmailSearch']['BounceStatus'])) {
        continue;
      } //BounceType doesn't match

    }

    //check OptInDate
    if ($SearchForOptInDate) {
      if (empty($Subscription['EmailAddress'])) {
        // email status only
        continue;
      }
      if (!klicktipp_subscriber_get_subscribers_by_subscription_alternative_test('SubscriptionDate', $Subscription, $search)) {
        continue;
      } //OptInDate doesn't match
    }

    //check SubscriptionDate (ConfirmationDate)
    if ($SearchForSubscriptionDate) {
      if (empty($Subscription['EmailAddress'])) {
        // email status only
        continue;
      }
      if (!klicktipp_subscriber_get_subscribers_by_subscription_alternative_test('ConfirmationDate', $Subscription, $search)) {
        continue;
      } //SubscriptionDate doesn't match
    }

    //check tagged with
    if ($SearchForTaggedWith) {

      $SubscriberTagging = Subscribers::RetrieveTaggingsOfSubscriber($UserID, $Subscription['SubscriberID'], $ReferenceID, TRUE);

      if (empty($SubscriberTagging)) {
        continue;
      }

      if ($search['OpTaggedWith'] == Campaigns::TAG_HAS_ALL) {
        //subscriber must have all tags in TaggedWith
        $diff = array_diff($search['TaggedWith'], $SubscriberTagging);
        if (!empty($diff)) {
          continue;
        } //TaggedWith doesn't match, subscriber doesn't have at least one tag

      }
      else {
        //subscriber must have at least one tag in TaggedWith
        $intersect = array_intersect($search['TaggedWith'], $SubscriberTagging);
        if (empty($intersect)) {
          continue;
        } //TaggedWith doesn't match, subscriber doesn't have any of the tags
      }

    }

    //check not tagged with
    if ($SearchForNotTaggedWith) {
      if (Subscribers::NegativeConditionsMatching($UserID, $Subscription['SubscriberID'], $ReferenceID, $search)) {
        continue;
      } //NotTaggedWith doesn't match
    }

    //check custom fields
    foreach ($search['CustomFields'] as $CustomFieldID => $op) {
      if (!empty($op)) {
        if (!klicktipp_subscriber_get_subscribers_by_customfields_alternative_test($op, $CustomFieldID, $Subscription, $search)) {
          continue 2;
        } //continue the while loop and exclude the subscriber (one customfield doesn't match)
      }

    }


    //Subscriber matches search criteria, add to SearchResult
    $SearchResult[$sid] = $AllSubscriptions[$sid];

  }

  if (!empty($tablesort_header)) {
    if ($tablesort_header['order'] = 'OptInDate') {
      if ($tablesort_header['sort'] == 'desc') {
        uasort($SearchResult, 'klicktipp_subscriber_search_sort_by_OptInDate_desc');
      }
      else {
        uasort($SearchResult, 'klicktipp_subscriber_search_sort_by_OptInDate_asc');
      }
    }
    else {
      if ($tablesort_header['sort'] == 'desc') {
        uasort($SearchResult, 'klicktipp_subscriber_search_sort_by_SubscriptionDate_desc');
      }
      else {
        uasort($SearchResult, 'klicktipp_subscriber_search_sort_by_SubscriptionDate_asc');
      }

    }

  }

  return $SearchResult;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_get_subscribers_by_subscription_alternative_test($type, $Subscription, $search) {

  $match = FALSE;

  $Date = ($type == 'SubscriptionDate') ? $Subscription['OptInDate'] : $Subscription['SubscriptionDate']; //either SubscriptionDate (OptInDate) or ConfirmationDate
  $SearchDate = $search['EmailSearch']['Subscription'][$type];
  $DateOp = $search['EmailSearch']['Subscription']["Op$type"];
  //$DayStart = strtotime($SearchDate);
  $DayStart = Core::DateFormatToTimestamp(Dates::FORMAT_DMY_DATEPICKER_PHP, $SearchDate);
  $DayEnd = $DayStart + (24 * 60 * 60) - 1;

  switch ($DateOp) {
    case SubscribersSearch::DATE_ON:
      $match = ($Date >= $DayStart && $Date <= $DayEnd);
      break;
    case SubscribersSearch::DATE_NOT_ON:
      $match = !($Date >= $DayStart && $Date <= $DayEnd);
      break;
    case SubscribersSearch::DATE_BEFORE:
      $match = ($Date < $DayStart);
      break;
    case SubscribersSearch::DATE_AFTER:
      $match = ($Date > $DayEnd);
      break;
    case SubscribersSearch::DATE_BETWEEN:
      $match = ($Date >= $DayStart && $Date <= strtotime($search['EmailSearch']['Subscription']["{$type}Until"]) + (24 * 60 * 60) - 1);
      break;
    case SubscribersSearch::DATE_NOT_BETWEEN:
      $match = !($Date >= $DayStart && $Date <= strtotime($search['EmailSearch']['Subscription']["{$type}Until"]) + (24 * 60 * 60) - 1);
      break;
  }

  return $match;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_get_subscribers_by_customfields_alternative_test($op, $CustomFieldID, $Subscription, $search) {

  $SearchValue = $search['FormValue_Fields']["CustomField$CustomFieldID"];
  $SearchValueUntil = $search['FormValue_Fields']["CustomFieldBetween$CustomFieldID"];
  $SubscriptionValue = $Subscription["CustomField$CustomFieldID"];

  switch ($op) {
    case SubscribersSearch::IS_EMPTY:
      $match = empty($SubscriptionValue);
      break;
    case SubscribersSearch::IS_NOT_EMPTY:
      $match = !empty($SubscriptionValue);
      break;
    case SubscribersSearch::IS:
      $match = ($SubscriptionValue == $SearchValue) && !empty($SubscriptionValue);
      break;
    case SubscribersSearch::IS_NOT:
      $match = ($SubscriptionValue != $SearchValue) && !empty($SubscriptionValue);
      break;
    case SubscribersSearch::CONTAINS:
      $match = (strpos($SubscriptionValue, $SearchValue) !== FALSE) && !empty($SubscriptionValue);
      break;
    case SubscribersSearch::CONTAINS_NOT:
      $match = (strpos($SubscriptionValue, $SearchValue) === FALSE) && !empty($SubscriptionValue);
      break;
    case SubscribersSearch::STARTS_WITH:
      $match = (strpos($SubscriptionValue, $SearchValue) === 0) && !empty($SubscriptionValue);
      break;
    case SubscribersSearch::STARTS_NOT_WITH:
      $match = !(strpos($SubscriptionValue, $SearchValue) === 0) && !empty($SubscriptionValue);
      break;
    case SubscribersSearch::ENDS_WITH:
      if (strlen($SubscriptionValue) < strlen($SearchValue)) {
        $match = FALSE;
      }
      else {
        $match = ((substr($SubscriptionValue, -strlen($SearchValue))) == $SearchValue);
      }
      break;
    case SubscribersSearch::ENDS_NOT_WITH:
      if (strlen($SubscriptionValue) < strlen($SearchValue)) {
        $match = TRUE;
      }
      else {
        $match = ((substr($SubscriptionValue, -strlen($SearchValue))) != $SearchValue);
      }
      break;
    case SubscribersSearch::LESS_THAN:
      $match = ((int) $SubscriptionValue < (int) $SearchValue) && !empty($SubscriptionValue);
      break;
    case SubscribersSearch::GREATER_THAN:
      $match = ((int) $SubscriptionValue > (int) $SearchValue) && !empty($SubscriptionValue);
      break;
    case SubscribersSearch::CHECKED:
      //TODO check and test
      $SearchValue = (is_array($SearchValue)) ? implode('||||', $SearchValue) : $SearchValue;
      $match = ($SubscriptionValue == $SearchValue);
      break;
    case SubscribersSearch::ON:
    case SubscribersSearch::AT:
      if (is_array($SearchValue) && count($SearchValue) == 3) {
        //it's a datetime (1 textfield and 2 selects)
        $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATETIME);
        $SearchUntil = strtotime("+59 seconds", $SearchValue);
      }
      else {
        if (is_array($SearchValue)) {
          //it a time
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_TIME_2SELECTS);
          $SearchUntil = strtotime("+59 seconds", $SearchValue);
        }
        else {
          //it's a date
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATE_DATEPICKER);
          $SearchUntil = strtotime("+23 hours 59 minutes 59 seconds", $SearchValue);
        }
      }
      $match = !empty($SubscriptionValue) && ($SubscriptionValue >= $SearchValue && $SubscriptionValue <= $SearchUntil);
      break;
    case SubscribersSearch::NOT_ON:
    case SubscribersSearch::NOT_AT:
      if (is_array($SearchValue) && count($SearchValue) == 3) {
        //it's a datetime (1 textfield and 2 selects)
        $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATETIME);
        $SearchUntil = strtotime("+59 seconds", $SearchValue);
      }
      else {
        if (is_array($SearchValue)) {
          //it a time
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_TIME_2SELECTS);
          $SearchUntil = strtotime("+59 seconds", $SearchValue);
        }
        else {
          //it's a date
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATE_DATEPICKER);
          $SearchUntil = strtotime("+23 hours 59 minutes 59 seconds", $SearchValue);
        }
      }
      $match = !empty($SubscriptionValue) && ($SubscriptionValue < $SearchValue || $SubscriptionValue > $SearchUntil);
      break;
    case SubscribersSearch::BEFORE:
      if (is_array($SearchValue) && count($SearchValue) == 3) {
        //it's a datetime (1 textfield and 2 selects)
        $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATETIME);
      }
      else {
        if (is_array($SearchValue)) {
          //it a time
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_TIME_2SELECTS);
        }
        else {
          //it's a date
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATE_DATEPICKER);
        }
      }
      $match = !empty($SubscriptionValue) && ($SubscriptionValue < $SearchValue);
      break;
    case SubscribersSearch::AFTER:
      if (is_array($SearchValue) && count($SearchValue) == 3) {
        //it's a datetime (1 textfield and 2 selects)
        $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATETIME);
        $SearchValue = strtotime("+59 seconds", $SearchValue);
      }
      else {
        if (is_array($SearchValue)) {
          //it a time
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_TIME_2SELECTS);
          $SearchValue = strtotime("+59 seconds", $SearchValue);
        }
        else {
          //it's a date
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATE_DATEPICKER);
          $SearchValue = strtotime("+23 hours 59 minutes 59 seconds", $SearchValue);
        }
      }
      $match = !empty($SubscriptionValue) && ($SubscriptionValue > $SearchValue);
      break;
    case SubscribersSearch::BETWEEN:
      if (is_array($SearchValue) && count($SearchValue) == 3) {
        //it's a datetime (1 textfield and 2 selects)
        $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATETIME);
        $SearchValueUntil = CustomFields::ConvertCustomFieldDataFromWidget($SearchValueUntil, CustomFields::WIDGET_DATETIME);
        $SearchValueUntil = strtotime("+59 seconds", $SearchValueUntil);
      }
      else {
        if (is_array($SearchValue)) {
          //it a time
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_TIME_2SELECTS);
          $SearchValueUntil = CustomFields::ConvertCustomFieldDataFromWidget($SearchValueUntil, CustomFields::WIDGET_TIME_2SELECTS);
          $SearchValueUntil = strtotime("+59 seconds", $SearchValueUntil);
        }
        else {
          //it's a date
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATE_DATEPICKER);
          $SearchValueUntil = CustomFields::ConvertCustomFieldDataFromWidget($SearchValueUntil, CustomFields::WIDGET_DATE_DATEPICKER);
          $SearchValueUntil = strtotime("+23 hours 59 minutes 59 seconds", $SearchValueUntil);
        }
      }
      $match = !empty($SubscriptionValue) && ($SubscriptionValue >= $SearchValue && $SubscriptionValue <= $SearchValueUntil);
      break;
    case SubscribersSearch::NOT_BETWEEN:
      if (is_array($SearchValue) && count($SearchValue) == 3) {
        //it's a datetime (1 textfield and 2 selects)
        $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATETIME);
        $SearchValueUntil = CustomFields::ConvertCustomFieldDataFromWidget($SearchValueUntil, CustomFields::WIDGET_DATETIME);
        $SearchValueUntil = strtotime("+59 seconds", $SearchValueUntil);
      }
      else {
        if (is_array($SearchValue)) {
          //it a time
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_TIME_2SELECTS);
          $SearchValueUntil = CustomFields::ConvertCustomFieldDataFromWidget($SearchValueUntil, CustomFields::WIDGET_TIME_2SELECTS);
          $SearchValueUntil = strtotime("+59 seconds", $SearchValueUntil);
        }
        else {
          //it's a date
          $SearchValue = CustomFields::ConvertCustomFieldDataFromWidget($SearchValue, CustomFields::WIDGET_DATE_DATEPICKER);
          $SearchValueUntil = CustomFields::ConvertCustomFieldDataFromWidget($SearchValueUntil, CustomFields::WIDGET_DATE_DATEPICKER);
          $SearchValueUntil = strtotime("+23 hours 59 minutes 59 seconds", $SearchValueUntil);
        }
      }
      $match = !empty($SubscriptionValue) && ($SubscriptionValue < $SearchValue || $SubscriptionValue > $SearchValueUntil);
      break;

  }


  return $match;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_search_compare_results(&$Test, $LiveResult, $TestResult) {

  $message = "Compare Search Results: ";

  $ResultCount = count($LiveResult);
  if ($ResultCount != count($TestResult)) {
    $Test->assertTrue(FALSE, "$message Result count doesn't match (Live: " . $ResultCount . ")(Test: " . count($TestResult) . ")");
    return FALSE;
  }

  $success = TRUE;

  //for ( $i=0; $i < $ResultCount; $i++ ) {
  foreach ($LiveResult as $s => $live) {
    //$live = $LiveResult[$i];
    $test = $TestResult[$s];

    //check SubscriberID ($s) in results
    if (empty($test)) {
      $Test->assertTrue(FALSE, "$message Result doesn't match");
      $success = FALSE;
    }

    //check dates
    $isSMSSubscription = ($live['SubscriptionType'] == Subscription::SUBSCRIPTIONTYPE_SMS);
    if ($isSMSSubscription) {
      if ($live['SubscriptionDate'] != $test['SMSSubscriptionDate']) {
        $Test->assertTrue(FALSE, "$message SMSSubscriptionDate doesn't match for SubscriberID " . $live['SubscriberID'] . "(" . $live['SubscriptionDate'] . ", " . $test['SMSSubscriptionDate'] . ")");
        $success = FALSE;
      }
    }
    else {

      if ($live['OptInDate'] != $test['OptInDate']) {
        $Test->assertTrue(FALSE, "$message OptInDate doesn't match for SubscriberID " . $live['SubscriberID'] . "(" . $live['OptInDate'] . ", " . $test['OptInDate'] . ")");
        $success = FALSE;
      }

      if ($live['SubscriptionDate'] != $test['SubscriptionDate']) {
        $Test->assertTrue(FALSE, "$message SubscriptionDate doesn't match for SubscriberID " . $live['SubscriberID'] . "(" . $live['SubscriptionDate'] . ", " . $test['SubscriptionDate'] . ")");
        $success = FALSE;
      }
    }
  }

  return $success;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_search_init(&$Test, $UserID) {
  $ReferenceID = 0;

  // ######################
  // # create lists
  // ######################

  $ArrayFieldAndValues = array(
    'Name' => 'Test list 1',
    'RelOwnerUserID' => $UserID,
  );
  $List1ID = Lists::InsertDB($ArrayFieldAndValues);
  $Test->assertTrue($List1ID > 0, "List1 created: $List1ID");

  $ArrayFieldAndValues = array(
    'Name' => 'Test list 2',
    'RelOwnerUserID' => $UserID,
  );
  $List2ID = Lists::InsertDB($ArrayFieldAndValues);
  $Test->assertTrue($List2ID > 0, "List2 created: $List2ID");

  //get user lists
  $all_lists_of_user = Lists::RetrieveLists($UserID);
  $Test->assertTrue(count($all_lists_of_user) == 3, 'Setup: User has 3 lists, List1, List2, DefaultList');

  // ######################
  // # create tags
  // ######################

  $TagA = Tag::CreateManualTag($UserID, 'Tag A', '', '');
  $TagB = Tag::CreateManualTag($UserID, 'Tag B', '', '');
  $TagC = Tag::CreateManualTag($UserID, 'Tag C', '', '');
  $TagD = Tag::CreateManualTag($UserID, 'Tag D', '', '');
  $TagE = Tag::CreateManualTag($UserID, 'Tag E', '', '');

  $ProductA = Tag::CreateManualTag($UserID, 'Product A', '', '');
  $ProductB = Tag::CreateManualTag($UserID, 'Product B', '', '');

  $NL1Sent = Tag::CreateManualTag($UserID, 'NL1 Sent', '', ''); //simulating smart tag sent
  $NL1Opened = Tag::CreateManualTag($UserID, 'NL1 Opened', '', ''); //simulating smart tag opened
  $NL2Sent = Tag::CreateManualTag($UserID, 'NL2 Sent', '', ''); //simulating smart tag sent
  $NL2Opened = Tag::CreateManualTag($UserID, 'NL2 Opened', '', ''); //simulating smart tag opened
  $NL3Sent = Tag::CreateManualTag($UserID, 'NL3 Sent', '', ''); //simulating smart tag sent
  $NL3Opened = Tag::CreateManualTag($UserID, 'NL3 Opened', '', ''); //simulating smart tag opened

  $all_tags_of_user = Tag::RetrieveManualTags($UserID);
  $Test->assertTrue(count($all_tags_of_user) == 13, 'Setup: User has 11 Tags');

  // ######################
  // # create custom fields
  // ######################

  $ArrayFieldAndValues = array(
    'RelOwnerUserID' => $UserID,
    'FieldName' => 'Number',
    'FieldTypeEnum' => CustomFields::TYPE_NUMBER,
  );
  $CF_Number = CustomFields::Create($ArrayFieldAndValues);
  $Test->assertTrue($CF_Number > 0, 'Setup: User has custom field type number');

  $ArrayFieldAndValues = array(
    'RelOwnerUserID' => $UserID,
    'FieldName' => 'Decimal',
    'FieldTypeEnum' => CustomFields::TYPE_DECIMAL,
  );
  $CF_Decimal = CustomFields::Create($ArrayFieldAndValues);
  $Test->assertTrue($CF_Decimal > 0, 'Setup: User has custom field type decimal');

  $ArrayFieldAndValues = array(
    'RelOwnerUserID' => $UserID,
    'FieldName' => 'Date',
    'FieldParameters' => array(
      'RequestsName' => 'Date',
    ),
    'FieldTypeEnum' => CustomFields::TYPE_DATE,
  );
  $CF_Date = CustomFields::Create($ArrayFieldAndValues);
  $Test->assertTrue($CF_Date > 0, 'Setup: User has custom field type date');

  $ArrayFieldAndValues = array(
    'RelOwnerUserID' => $UserID,
    'FieldName' => 'Time',
    'FieldTypeEnum' => CustomFields::TYPE_TIME,
  );
  $CF_Time = CustomFields::Create($ArrayFieldAndValues);
  $Test->assertTrue($CF_Time > 0, 'Setup: User has custom field type time');

  // ######################
  // # create subscribers
  // ######################

  $TestResults = array(
    'SubscriberCount' => 0,
    'EmailSubscriberCount' => 0,
    'ImportedSubscribers' => 0,
    'SubscribedSubscribers' => 0,
    'PendingSubscribers' => 0,
    'Pending7Subscribers' => 0,
    'PendingSoftBouncedSubscribers' => 0,
    'UnsubscribedSubscribers' => 0,
    'hasTagA' => 0, //subscribers with tag A
    'hasTagB' => 0, //subscribers with tag B
    'hasTagC' => 0, //subscribers with tag C
    'hasTagD' => 0, //subscribers with tag D
    'hasTagE' => 0, //subscribers with tag E
    'Email_imported' => 0, //subscribers with 'imported' in the email address
    //custom field options (positive) based on the string 'FirstName'
    'CustomField IS FirstName' => 0,
    'CustomField STARTS FirstName' => 0,
    'CustomField ENDS FirstName' => 0,
    'CustomField CONTAINS FirstName' => 0,
    'CustomField IS NOT FirstName' => 0,
    'CustomField STARTS NOT FirstName' => 0,
    'CustomField ENDS NOT FirstName' => 0,
    'CustomField CONTAINS NOT FirstName' => 0,
    'CustomField EMPTY Date' => 0,
    'CustomField NOT EMPTY Date' => 0,
    //Dates
    'OptInDate Today' => 0,
    'OptInDate Not Today' => 0,
    'SubscriptionDate Today' => 0,
    'SubscriptionDate Not Today' => 0,
    'Tags' => array(
      'TagA' => $TagA,
      'TagB' => $TagB,
      'TagC' => $TagC,
      'TagD' => $TagD,
      'TagE' => $TagE,
      'ProductA' => $ProductA,
      'ProductB' => $ProductB,
      'NL1Sent' => $NL1Sent,
      'NL1Opened' => $NL1Opened,
      'NL2Sent' => $NL2Sent,
      'NL2Opened' => $NL2Opened,
      'NL3Sent' => $NL3Sent,
      'NL3Opened' => $NL3Opened,
    ),
    'Lists' => array(
      'List1ID' => $List1ID,
      'List2ID' => $List2ID,
    ),
    'CustomFields' => array(
      'Number' => $CF_Number,
      'Decimal' => $CF_Decimal,
      'Date' => $CF_Date,
      'Time' => $CF_Time,
    ),
    'All Customers' => 0, //bought at least one product
    'Unloyal Customers' => 0, //did not buy all products but at least one
    'Loyal Customers' => 0,//all contacts that bought all products
    'Unopened Newsletter' => 0, //all contacts that got all newsletters and did not open at least one
    'ProductAnotB' => 0, //contact has product A but not product B
  );

  // create 10 imported subscribers
  for ($i = 1; $i <= 10; $i++) {

    // SUBSCRIBE
    $SubscriptionResult = Subscribers::Subscribe([
      'UserID' => $UserID,
      'ListInformation' => [
        'ListID' => 0,
        'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE
      ], //ListID 0 for import
      'OptInSubscribeTo' => $TagA,
      'EmailAddress' => "imported_$<EMAIL>",
      'PhoneNumber' => "0049152000099" . sprintf("%02d", $i),
      'IPAddress' => $_SERVER['REMOTE_ADDR'],
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SubscriptionReferrer' => '',
      'OtherFields' => [
        'CustomFieldFirstName' => 'FirstName',
        "CustomField$CF_Number" => 10,
        "CustomField$CF_Decimal" => $i * 100,
        "CustomField$CF_Date" => strtotime("2013-01-01"),
        "CustomField$CF_Time" => (4 * 60) + 5, //04:05
      ],
      'UpdateIfUnsubscribed' => FALSE,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    ]);

    if (!$SubscriptionResult[0]) {
      $Test->assertTrue(FALSE, "Setup: Create imported subscriber $i");
    }

    //update Test data
    $TestResults['SubscriberCount']++;
    $TestResults['EmailSubscriberCount']++;
    $TestResults['SubscribedSubscribers']++;
    $TestResults['ImportedSubscribers']++;
    $TestResults['hasTagA']++;
    $TestResults['Email_imported']++;
    $TestResults['CustomField IS FirstName']++;
    $TestResults['CustomField STARTS FirstName']++;
    $TestResults['CustomField ENDS FirstName']++;
    $TestResults['CustomField CONTAINS FirstName']++;
    $TestResults['CustomField NOT EMPTY Date']++;
    $TestResults['OptInDate Today']++;
    $TestResults['SubscriptionDate Today']++;
    $TestResults['Unopened Newsletter']++;
    $TestResults['PhoneNumbers']++;

    if ($i == 5) {
      // change subscription date of one sms subscription
      db_update('subscription')
        ->fields([
          'SubscriptionDate' => time() - (10 * 24 * 60 * 60),
        ])
        ->condition('RelOwnerUserID', $UserID)
        ->condition('RelSubscriberID', $SubscriptionResult[1])
        ->execute();
      $TestResults['SubscriptionDate Today']--;
      $TestResults['SubscriptionDate Not Today']++;
    }
  }

  //create 10 pending subscribers
  for ($i = 11; $i <= 20; $i++) {

    // SUBSCRIBE
    $SubscriptionResult = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => $List1ID,
        'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
      ),
      'OptInSubscribeTo' => $TagB,
      'EmailAddress' => "pending_$<EMAIL>",
      'IPAddress' => $_SERVER['REMOTE_ADDR'],
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      'SubscriptionReferrer' => '',
      'OtherFields' => array(
        'CustomFieldFirstName' => 'pending FirstName',
        "CustomField$CF_Number" => 20,
        "CustomField$CF_Date" => strtotime("2012-01-06"),
        "CustomField$CF_Time" => (12 * 60) + 15, //12:15,
      ),
      'UpdateIfUnsubscribed' => FALSE,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    ));

    if (!$SubscriptionResult[0]) {
      $Test->assertTrue(FALSE, "Setup: Create pending subscriber $i");
    }

    //update Test data
    $TestResults['SubscriberCount']++;
    $TestResults['EmailSubscriberCount']++;
    $TestResults['PendingSubscribers']++;
    $TestResults['hasTagB']++;
    $TestResults['CustomField ENDS FirstName']++;
    $TestResults['CustomField CONTAINS FirstName']++;
    $TestResults['CustomField IS NOT FirstName']++;
    $TestResults['CustomField STARTS NOT FirstName']++;
    $TestResults['CustomField NOT EMPTY Date']++;
    $TestResults['OptInDate Today']++;
    $TestResults['SubscriptionDate Not Today']++;
    $TestResults['Unopened Newsletter']++;

  }

  //create 10 pending more than 7 days subscribers
  for ($i = 21; $i <= 30; $i++) {

    // SUBSCRIBE
    $SubscriptionResult = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => $List1ID,
        'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
      ),
      'OptInSubscribeTo' => $TagC,
      'EmailAddress' => "pending7_$<EMAIL>",
      'IPAddress' => $_SERVER['REMOTE_ADDR'],
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      'SubscriptionReferrer' => '',
      'OtherFields' => array(
        'CustomFieldFirstName' => 'pending7 FirstName',
        "CustomField$CF_Number" => 30,
        "CustomField$CF_Date" => strtotime("2013-01-09"),
        "CustomField$CF_Time" => (19 * 60) + 34, //19:34,
      ),
      'UpdateIfUnsubscribed' => FALSE,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    ));

    if (!$SubscriptionResult[0]) {
      $Test->assertTrue(FALSE, "Setup: Create pending7 subscriber $i");
    }

    $Subscribers[] = $SubscriptionResult[1];

    db_update('subscription')
      ->fields(array(
        'OptInDate' => time() - (10 * 24 * 60 * 60),
      ))
      ->condition('RelOwnerUserID', $UserID)
      ->condition('RelSubscriberID', $SubscriptionResult[1])
      ->execute();

    //update Test data
    $TestResults['SubscriberCount']++;
    $TestResults['EmailSubscriberCount']++;
    $TestResults['PendingSubscribers']++;
    $TestResults['Pending7Subscribers']++;
    $TestResults['hasTagC']++;
    $TestResults['CustomField ENDS FirstName']++;
    $TestResults['CustomField CONTAINS FirstName']++;
    $TestResults['CustomField IS NOT FirstName']++;
    $TestResults['CustomField STARTS NOT FirstName']++;
    $TestResults['CustomField NOT EMPTY Date']++;
    $TestResults['OptInDate Not Today']++;
    $TestResults['SubscriptionDate Not Today']++;
    $TestResults['Unopened Newsletter']++;

  }


  //create 10 soft bounced subscribers
  for ($i = 31; $i <= 40; $i++) {

    // SUBSCRIBE
    $EmailAddress = "pending_softbounced_$<EMAIL>";
    $SubscriptionResult = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => $List1ID,
        'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
      ),
      'OptInSubscribeTo' => $TagD,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => $_SERVER['REMOTE_ADDR'],
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      'SubscriptionReferrer' => '',
      'OtherFields' => array(
        'CustomFieldFirstName' => 'pending softbounced FirstName',
        "CustomField$CF_Number" => 40,
        "CustomField$CF_Date" => strtotime("2013-05-09"),
        "CustomField$CF_Time" => (19 * 60) + 35, //19:35,,
      ),
      'UpdateIfUnsubscribed' => FALSE,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    ));

    if (!$SubscriptionResult[0]) {
      $Test->assertTrue(FALSE, "Setup: Create soft bounced subscriber $i");
    }

    $Subscribers[] = $SubscriptionResult[1];

    //subscriber has baught product A
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $ProductA, $ReferenceID, FALSE);

    //subscriber received newsletter 1,2 and 3
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL1Sent, $ReferenceID, FALSE);
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL2Sent, $ReferenceID, FALSE);
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL3Sent, $ReferenceID, FALSE);

    Subscription::UpdateSubscription($UserID, $EmailAddress, Subscription::SUBSCRIPTIONTYPE_EMAIL, [
      'BounceType' => Subscribers::BOUNCETYPE_SOFT,
    ]);

    //update Test data
    $TestResults['SubscriberCount']++;
    $TestResults['EmailSubscriberCount']++;
    $TestResults['PendingSubscribers']++;
    $TestResults['PendingSoftBouncedSubscribers']++;
    $TestResults['hasTagD']++;
    $TestResults['CustomField ENDS FirstName']++;
    $TestResults['CustomField CONTAINS FirstName']++;
    $TestResults['CustomField IS NOT FirstName']++;
    $TestResults['CustomField STARTS NOT FirstName']++;
    $TestResults['CustomField NOT EMPTY Date']++;
    $TestResults['OptInDate Today']++;
    $TestResults['SubscriptionDate Not Today']++;
    $TestResults['All Customers']++;
    $TestResults['Unloyal Customers']++;
    $TestResults['Unopened Newsletter']++;
    $TestResults['ProductAnotB']++;

  }

  //create 10 hard bounced subscribers
  for ($i = 41; $i <= 50; $i++) {

    // SUBSCRIBE
    $EmailAddress = "pending_hardbounced_$<EMAIL>";
    $SubscriptionResult = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => $List1ID,
        'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
      ),
      'OptInSubscribeTo' => $TagE,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => $_SERVER['REMOTE_ADDR'],
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
      'SubscriptionReferrer' => '',
      'OtherFields' => array(
        'CustomFieldFirstName' => 'pending hardbounced FirstName',
      ),
      'UpdateIfUnsubscribed' => FALSE,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    ));

    if (!$SubscriptionResult[0]) {
      $Test->assertTrue(FALSE, "Setup: Create hard bounced subscriber $i");
    }

    $Subscribers[] = $SubscriptionResult[1];

    //subscriber has baught product B
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $ProductB, $ReferenceID, FALSE);

    //subscriber received and opened newsletter 1
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL1Sent, $ReferenceID, FALSE);
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL1Opened, $ReferenceID, FALSE);

    //subscriber received newsletter 2
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL2Sent, $ReferenceID, FALSE);

    Subscription::UpdateSubscription($UserID, $EmailAddress, Subscription::SUBSCRIPTIONTYPE_EMAIL, [
      'BounceType' => Subscribers::BOUNCETYPE_HARD,
    ]);

    //update Test data
    $TestResults['SubscriberCount']++;
    $TestResults['EmailSubscriberCount']++;
    $TestResults['PendingSubscribers']++;
    $TestResults['hasTagE']++;
    $TestResults['CustomField ENDS FirstName']++;
    $TestResults['CustomField CONTAINS FirstName']++;
    $TestResults['CustomField IS NOT FirstName']++;
    $TestResults['CustomField STARTS NOT FirstName']++;
    $TestResults['CustomField EMPTY Date']++;
    $TestResults['OptInDate Today']++;
    $TestResults['SubscriptionDate Not Today']++;
    $TestResults['All Customers']++;
    $TestResults['Unloyal Customers']++;
    $TestResults['Unopened Newsletter']++;

  }

  //create 10 subscribed subscribers
  $OptInDate = strtotime(date("Y-m-d"));
  for ($i = 51; $i <= 60; $i++) {

    // SUBSCRIBE
    $SubscriptionResult = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => $List1ID,
        'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE
      ),
      'OptInSubscribeTo' => $TagB,
      'EmailAddress' => "subscribed_$<EMAIL>",
      'IPAddress' => $_SERVER['REMOTE_ADDR'],
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
      'SubscriptionReferrer' => '',
      'OtherFields' => array(
        'CustomFieldFirstName' => 'subscribed FirstName',
      ),
      'UpdateIfUnsubscribed' => FALSE,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    ));

    if (!$SubscriptionResult[0]) {
      $Test->assertTrue(FALSE, "Setup: Create subscribed subscriber $i");
    }

    //subscriber has baught product A and B
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $ProductA, $ReferenceID, FALSE);
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $ProductB, $ReferenceID, FALSE);

    //subscriber received newsletter 3
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL3Sent, $ReferenceID, FALSE);

    db_update('subscription')
      ->fields(array(
        'OptInDate' => time() - ($i * 24 * 60 * 60),
      ))
      ->condition('RelOwnerUserID', $UserID)
      ->condition('RelSubscriberID', $SubscriptionResult[1])
      ->execute();

    //update Test data
    $TestResults['SubscriberCount']++;
    $TestResults['EmailSubscriberCount']++;
    $TestResults['SubscribedSubscribers']++;
    $TestResults['hasTagB']++;
    $TestResults['CustomField ENDS FirstName']++;
    $TestResults['CustomField CONTAINS FirstName']++;
    $TestResults['CustomField IS NOT FirstName']++;
    $TestResults['CustomField STARTS NOT FirstName']++;
    $TestResults['CustomField EMPTY Date']++;
    $TestResults['OptInDate Not Today']++;
    $TestResults['SubscriptionDate Today']++;
    $TestResults['All Customers']++;
    $TestResults['Loyal Customers']++;
    $TestResults['Unopened Newsletter']++;

  }

  //create 10 unsubscribed subscribers
  for ($i = 61; $i <= 70; $i++) {

    $EmailAddress = "unsubscribed_$<EMAIL>";
    // SUBSCRIBE
    $SubscriptionResult = Subscribers::Subscribe(array(
      'UserID' => $UserID,
      'ListInformation' => array(
        'ListID' => $List1ID,
        'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
      ),
      'OptInSubscribeTo' => 0,
      'EmailAddress' => $EmailAddress,
      'IPAddress' => $_SERVER['REMOTE_ADDR'],
      'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_UNSUBSCRIBED,
      'SubscriptionReferrer' => '',
      'OtherFields' => array(
        'CustomFieldFirstName' => 'unsubscribed FirstName',
      ),
      'UpdateIfUnsubscribed' => FALSE,
      'SendConfirmationEmail' => FALSE,
      'TriggerAutoResponders' => FALSE,
    ));

    if (!$SubscriptionResult[0]) {
      $Test->assertTrue(FALSE, "Setup: Create unsubscribed subscriber $i");
    }

    //subscriber received and opened newsletter 1
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL1Sent, $ReferenceID, FALSE);
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL1Opened, $ReferenceID, FALSE);

    //subscriber received and opened newsletter 2
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL2Sent, $ReferenceID, FALSE);
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL2Opened, $ReferenceID, FALSE);

    //subscriber received and opened newsletter 3
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL3Sent, $ReferenceID, FALSE);
    Subscribers::TagSubscriber($UserID, $SubscriptionResult[1], $NL3Opened, $ReferenceID, FALSE);

    //update Test data
    $TestResults['SubscriberCount']++;
    $TestResults['EmailSubscriberCount']++;
    $TestResults['UnsubscribedSubscribers']++;
    $TestResults['CustomField ENDS FirstName']++;
    $TestResults['CustomField CONTAINS FirstName']++;
    $TestResults['CustomField IS NOT FirstName']++;
    $TestResults['CustomField STARTS NOT FirstName']++;
    $TestResults['CustomField EMPTY Date']++;
    $TestResults['OptInDate Today']++;
    $TestResults['SubscriptionDate Today']++;


  }

  //every subscriber has 1 subscription -> ideal case
  //problems occured on multiple subscriptions -> create som test cases

  //special subscription 1: pending List1 -> pending List2 -> subscribe List2
  $SpecialCaseEmail1 = '<EMAIL>';

  //pending List1
  $SubscriptionResult = Subscribers::Subscribe(array(
    'UserID' => $UserID,
    'ListInformation' => array(
      'ListID' => $List1ID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
    ),
    'EmailAddress' => $SpecialCaseEmail1,
    'IPAddress' => $_SERVER['REMOTE_ADDR'],
    'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
    'SubscriptionReferrer' => '',
    'OtherFields' => array(
      'CustomFieldFirstName' => 'special1 FirstName',
    ),
    'UpdateIfUnsubscribed' => FALSE,
    'SendConfirmationEmail' => FALSE,
    'TriggerAutoResponders' => FALSE,
  ));

  if (!$SubscriptionResult[0]) {
    $Test->assertTrue(FALSE, "Setup: Special Case 1 - pending List1");
  }

  //pending List2
  $SubscriptionResult = Subscribers::Subscribe(array(
    'UserID' => $UserID,
    'ListInformation' => array(
      'ListID' => $List2ID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
    ),
    'EmailAddress' => $SpecialCaseEmail1,
    'IPAddress' => $_SERVER['REMOTE_ADDR'],
    'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
    'SubscriptionReferrer' => '',
    'OtherFields' => array(
      'CustomFieldFirstName' => 'special1 FirstName',
    ),
    'UpdateIfUnsubscribed' => FALSE,
    'SendConfirmationEmail' => FALSE,
    'TriggerAutoResponders' => FALSE,
  ));

  if (!$SubscriptionResult[0]) {
    $Test->assertTrue(FALSE, "Setup: Special Case 1 - pending List2");
  }

  //subscribe List2
  $SubscriptionResult = Subscribers::Subscribe(array(
    'UserID' => $UserID,
    'ListInformation' => array(
      'ListID' => $List2ID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
    ),
    'OptInSubscribeTo' => 0,
    'EmailAddress' => $SpecialCaseEmail1,
    'IPAddress' => $_SERVER['REMOTE_ADDR'],
    'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    'SubscriptionReferrer' => '',
    'OtherFields' => array(
      'CustomFieldFirstName' => 'special1 FirstName',
    ),
    'UpdateIfUnsubscribed' => FALSE,
    'SendConfirmationEmail' => FALSE,
    'TriggerAutoResponders' => FALSE,
  ));

  if (!$SubscriptionResult[0]) {
    $Test->assertTrue(FALSE, "Setup: Special Case 1 - subscribe List2");
  }

  //update Test data after special 1
  $TestResults['SubscriberCount']++;
  $TestResults['EmailSubscriberCount']++;
  $TestResults['SubscribedSubscribers']++;
  $TestResults['CustomField ENDS FirstName']++;
  $TestResults['CustomField CONTAINS FirstName']++;
  $TestResults['CustomField IS NOT FirstName']++;
  $TestResults['CustomField STARTS NOT FirstName']++;
  $TestResults['CustomField EMPTY Date']++;
  $TestResults['OptInDate Today']++;
  $TestResults['SubscriptionDate Today']++;

  //special subscription 2: pending List1 -> import -> subscribe List2
  $SpecialCaseEmail2 = '<EMAIL>';

  //pending List1
  $SubscriptionResult = Subscribers::Subscribe(array(
    'UserID' => $UserID,
    'ListInformation' => array(
      'ListID' => $List1ID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
    ),
    'EmailAddress' => $SpecialCaseEmail2,
    'IPAddress' => $_SERVER['REMOTE_ADDR'],
    'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_OPTIN,
    'SubscriptionReferrer' => '',
    'OtherFields' => array(
      'CustomFieldFirstName' => 'special2 FirstName',
    ),
    'UpdateIfUnsubscribed' => FALSE,
    'SendConfirmationEmail' => FALSE,
    'TriggerAutoResponders' => FALSE,
  ));

  if (!$SubscriptionResult[0]) {
    $Test->assertTrue(FALSE, "Setup: Special Case 2 - pending List1");
  }

  //subscribe List1
  $SubscriptionResult = Subscribers::Subscribe(array(
    'UserID' => $UserID,
    'ListInformation' => array(
      'ListID' => 0,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE
    ), //ListID 0 for import
    'OptInSubscribeTo' => 0,
    'EmailAddress' => $SpecialCaseEmail2,
    'IPAddress' => $_SERVER['REMOTE_ADDR'],
    'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    'SubscriptionReferrer' => '',
    'OtherFields' => array(
      'CustomFieldFirstName' => 'special2 FirstName',
    ),
    'UpdateIfUnsubscribed' => FALSE,
    'SendConfirmationEmail' => FALSE,
    'TriggerAutoResponders' => FALSE,
  ));

  if (!$SubscriptionResult[0]) {
    $Test->assertTrue(FALSE, "Setup: Special Case 2 - import");
  }


  //subscribe List2
  $SubscriptionResult = Subscribers::Subscribe(array(
    'UserID' => $UserID,
    'ListInformation' => array(
      'ListID' => $List2ID,
      'OptInModeEnum' => Lists::OPTIN_MODE_DOUBLE
    ),
    'OptInSubscribeTo' => 0,
    'EmailAddress' => $SpecialCaseEmail2,
    'IPAddress' => $_SERVER['REMOTE_ADDR'],
    'SubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    'SubscriptionReferrer' => '',
    'OtherFields' => array(
      'CustomFieldFirstName' => 'special2 FirstName',
    ),
    'UpdateIfUnsubscribed' => FALSE,
    'SendConfirmationEmail' => FALSE,
    'TriggerAutoResponders' => FALSE,
  ));

  if (!$SubscriptionResult[0]) {
    $Test->assertTrue(FALSE, "Setup: Special Case 2 - subscribe List2");
  }

  //update Test data after special 1
  $TestResults['SubscriberCount']++;
  $TestResults['EmailSubscriberCount']++;
  $TestResults['SubscribedSubscribers']++;
  $TestResults['CustomField ENDS FirstName']++;
  $TestResults['CustomField CONTAINS FirstName']++;
  $TestResults['CustomField IS NOT FirstName']++;
  $TestResults['CustomField STARTS NOT FirstName']++;
  $TestResults['CustomField EMPTY Date']++;
  $TestResults['OptInDate Today']++;
  $TestResults['SubscriptionDate Today']++;
  $TestResults['ImportedSubscribers']++;

  // subscriber without email address
  $SubscriptionResult = Subscribers::Subscribe([
    'UserID' => $UserID,
    'ListInformation' => [
      'ListID' => 0,
      'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE
    ], //ListID 0 for import
    'PhoneNumber' => "004915211119999",
    'IPAddress' => $_SERVER['REMOTE_ADDR'],
    'SMSSubscriptionStatus' => Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED,
    'SMSSubscriptionReferrer' => '',
    'OtherFields' => [
      'CustomFieldFirstName' => 'FirstNameWOEmail',
    ],
    'UpdateIfUnsubscribed' => FALSE,
    'SendConfirmationEmail' => FALSE,
    'TriggerAutoResponders' => FALSE,
  ]);

  if (!$SubscriptionResult[0]) {
    $Test->assertTrue(FALSE, "Setup: subscriber without email address");
  }

  //update Test data
  $TestResults['SubscriberCount']++;
  $TestResults['CustomField STARTS FirstName']++;
  $TestResults['CustomField CONTAINS FirstName']++;
  $TestResults['CustomField IS NOT FirstName']++;
  $TestResults['CustomField ENDS NOT FirstName']++;
  $TestResults['CustomField EMPTY Date']++;
  $TestResults['PhoneNumbers']++;

  return $TestResults;
}

//uasort($array_autoresponders, 'klicktipp_autoresponders_cmpBySimulationTime');
/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_search_sort_by_OptInDate_desc($a, $b) {

  if ($a['OptInDate'] == $b['OptInDate']) {
    return 0;
  }

  return ($a['OptInDate'] > $b['OptInDate']) ? -1 : 1;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_search_sort_by_OptInDate_asc($a, $b) {

  if ($a['OptInDate'] == $b['OptInDate']) {
    return 0;
  }

  return ($a['OptInDate'] < $b['OptInDate']) ? -1 : 1;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_search_sort_by_SubscriptionDate_desc($a, $b) {

  if ($a['SubscriptionDate'] == $b['SubscriptionDate']) {
    return 0;
  }

  return ($a['SubscriptionDate'] > $b['SubscriptionDate']) ? -1 : 1;

}

/**
 * @todo Please document this function.
 * @see http://drupal.org/node/1354
 */
function klicktipp_subscriber_search_sort_by_SubscriptionDate_asc($a, $b) {

  if ($a['SubscriptionDate'] == $b['SubscriptionDate']) {
    return 0;
  }

  return ($a['SubscriptionDate'] < $b['SubscriptionDate']) ? -1 : 1;

}

function klicktipp_subscriber_time_search
(
  $SearchTypeString,
  $Test,
  $message,
  $UserID,
  $customFieldID,
  $Controller,
  $Widget,
  $expectedSubscribers,
  $TestTimeString,
  $TestTimeStringUntil = null
){

  $SearchTimestamp = CustomFields::ConvertCustomFieldDataToWidget($TestTimeString, $Widget);
  $searchCustomField = [
    'FormValue_Fields' => [
      "CustomField$customFieldID" => $SearchTimestamp,
    ],
    'CustomFields' => [$customFieldID => $Controller],
  ];

  if ($TestTimeStringUntil) {
    $SearchTimestampUntil = CustomFields::ConvertCustomFieldDataToWidget($TestTimeStringUntil, $Widget);
    $searchCustomField["FormValue_Fields"]["CustomFieldBetween$customFieldID"] =  $SearchTimestampUntil;
  }

  $SearchResultCustomField = klicktipp_subscriber_search_subscribers_alternative_test($UserID, $searchCustomField);

  $time = "$SearchTypeString = $TestTimeString";
  if ($TestTimeStringUntil) {
    $time .= " and UNTIL $TestTimeStringUntil";
  }
  $message = "$message " . count($SearchResultCustomField) . " subscribers found (expected $expectedSubscribers) for $time";
  $Test->assertTrue(count($SearchResultCustomField) == $expectedSubscribers, $message);
  $LiveSearchResultCustomField = $Test->performSearch($UserID, $searchCustomField);

  $message = "Live test: " . count($LiveSearchResultCustomField) . " subscribers found for $SearchTypeString";
  $Test->assertTrue(klicktipp_subscriber_search_compare_results($Test, $LiveSearchResultCustomField, $SearchResultCustomField), $message, 1);

}

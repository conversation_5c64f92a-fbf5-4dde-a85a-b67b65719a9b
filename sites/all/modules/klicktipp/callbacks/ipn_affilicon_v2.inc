<?php

use App\Klicktipp\Affilicon;
use App\Klicktipp\Errors;
use App\Klicktipp\Lists;
use App\Klicktipp\PaymentIPNs;
use App\Klicktipp\Subscribers;
use App\Klicktipp\Subscription;
use App\Klicktipp\TransactionEmails;

/**
 * IPN receiver for Affilicon callback wrapper (secures function parameter in menu callbacks)
 */
function klicktipp_ipn_receiver_affilicon_wrapper() {
  if (variable_get('maintenance_mode', 0)) {
    exit;
  };

  // TODO: move $ipd_data = $_POST here - see end of (16 NEU) [https://www.pivotaltracker.com/story/show/109144896]
  klicktipp_ipn_receiver_affilicon();
}

//IPN receiver for Affilicon

function klicktipp_ipn_receiver_affilicon($redirect_callback = 'klicktipp_ipn_receiver_affilicon_redirect') {

  $ipn_data = $_REQUEST;

  // Signature check
  $received_signature = affilicon_posted_value($ipn_data, 'sha_sign');
  $expected_signature = affilicon_signature(Affilicon::IPN_PASSPHRASE, $ipn_data);
  $sha_sign_valid = $received_signature == $expected_signature;
  if (!$sha_sign_valid) {
    $error = array(
      '!ipn_data' => $ipn_data,
      '!signature' => $received_signature,
    );
    Errors::unexpected('AffiliCon IPN: Invalid sha signature. Check signature!', $error);
    call_user_func($redirect_callback, $error);
  }

  $event = affilicon_posted_value($ipn_data, 'event');
  $api_mode = affilicon_posted_value($ipn_data, 'api_mode'); // 'live' or 'test'

  $MerchantName = affilicon_posted_value($ipn_data, 'merchant_name');
  $ProductID = affilicon_posted_value($ipn_data, 'product_id');
  $EmailAddress = affilicon_posted_value($ipn_data, 'EmailAddress');

  switch ($event) {
    case Affilicon::EVENT_TEST:
      break; //returns 'OK' for DigiStore24 IPN connection test (they have a Button in their IPN form)
    case Affilicon::EVENT_PAYMENT:
      //retrieve the product information by ProductID AND MerchantName (Script is called anonymously), if not found, function returns CatchAll product of MerchantName (ProductID = 0, if exists)
      /** @var Affilicon $ObjectProduct */
      $ObjectProduct = Affilicon::FromMerchantAndProductID($MerchantName, $ProductID);
      if (empty($ObjectProduct)) {
        //returns "OK", customer misconfiguration
        Affilicon::SendConfigurationErrorMail(0, 'AffiliCon', $MerchantName, $ProductID, $EmailAddress, affilicon_posted_value($ipn_data, 'ReceiptID'));
        break;
      }

      $ProductInformation = $ObjectProduct->GetData();

      //TODO DEV-2414 ReferenceID may come from listbuilding (product)
      $ReferenceID = 0;

      // only perform actions on completed payments (if required)
      $BillingStatus = affilicon_posted_value($ipn_data, 'BillingStatus');
      $RequirePaidBillingStatus = $ProductInformation['RequirePaidBillingStatus'];
      if (empty($BillingStatus) && $RequirePaidBillingStatus) {
        break;
      }
      //successful payment of one or more products, subscribe customer with tagging

      // note: An order has one order_id and may consist of multiple order items.
      // For each order item, an ipn call is performed.

      $is_test_mode = $api_mode != 'live';

      // Note: not all orders have the complete address.
      // To make the complete address a requirement on the orderform,
      // edit the product settings in DigiStore24

      //the country is recieved as country code => @see klicktipp_get_country_name_from_country_code for translation
      $CustomFields = array();
      $LastName = affilicon_posted_value($ipn_data, 'LastName');
      if (!empty($LastName)) {

        $CustomFields['CustomFieldFirstName'] = affilicon_posted_value($ipn_data, 'FirstName');
        $CustomFields['CustomFieldLastName'] = $LastName;

      }

      $City = affilicon_posted_value($ipn_data, 'City');
      if (!empty($City)) {

        $CustomFields['CustomFieldStreet1'] = affilicon_posted_value($ipn_data, 'Street1');
        $CustomFields['CustomFieldCity'] = $City;
        $CustomFields['CustomFieldZip'] = affilicon_posted_value($ipn_data, 'Zip');
        $CustomFields['CustomFieldCountry'] = klicktipp_get_country_name_from_country_code(affilicon_posted_value($ipn_data, 'Country'));

      }

      $Phone = affilicon_posted_value($ipn_data, 'Phone');
      if (!empty($Phone)) {
        $CustomFields['CustomFieldPhone'] = $Phone;
      }

      $TransactionAmount = affilicon_posted_value($ipn_data, 'Amount'); //the actual amount paid
      $AmountSmaller95Cents = ($TransactionAmount < 0.95); //is the paid amount smaller 95 Cents, use DOI process

      $UserID = $ProductInformation['RelOwnerUserID'];
      $BuildID = $ProductInformation['BuildID'];

      $OptInSubscribeTo = $ProductInformation['OptInSubscribeTo'][Affilicon::EVENT_PAYMENT];

      $ArraySubscriberList = Lists::RetrieveListByIDOrDefault($UserID, $ProductInformation['RelListID']);

      if (!$ArraySubscriberList) {
        $error = array(
          '!ipn_data' => $ipn_data,
          '!uid' => $UserID,
          '!product' => $ProductInformation,
          '!list_id' => $ProductInformation['RelListID'],
          '!event' => $event,
        );
        Errors::unexpected('AffiliCon IPN: Default list (ID: !list_id) not found of User !uid. (!event)', $error);
        break; //the IPN call reached Klick-Tipp, so return "OK", but we should check
      }

      $NewSubscriptionStatus = Subscribers::SUBSCRIPTIONSTATUS_SUBSCRIBED; //under normal conditions, single optin
      if ($is_test_mode || $AmountSmaller95Cents) {
        $NewSubscriptionStatus = 0;
      } //TestMode or Product amount smaller 95 Cents, DOI process

      // SUBSCRIBE
      $ArrayReturn = Affilicon::Subscribe([
        'UserID' => $UserID,
        'ListInformation' => $ArraySubscriberList,
        'OptInSubscribeTo' => empty($OptInSubscribeTo) ? 0 : $OptInSubscribeTo,
        'EmailAddress' => $EmailAddress,
        'IPAddress' => '0.0.0.0 by IPN',
        'SubscriptionReferrer' => 'http://www.affilicon.net/',
        'OtherFields' => $CustomFields,
        'SubscriptionStatus' => $NewSubscriptionStatus,
        'SendConfirmationEmail' => TRUE,
        'UpdateIfUnsubscribed' => TRUE,
        'UpdateStatistics' => TRUE,
        'TriggerAutoResponders' => TRUE,
        'InstantTrigger' => TRUE,
      ]);

      if (!$ArrayReturn[0]) {
        break; // subscription fails, returns "OK"
      }

      $SubscriberID = $ArrayReturn[1];

      // smart tag
      Subscribers::TagSubscriber($UserID, $SubscriberID, $ProductInformation['SmartTagID'], $ReferenceID, TRUE);

      if (!$is_test_mode) {
        //do not write payments in test mode

        // write payments
        $PaymentData = [
          'GrossAmount' => affilicon_posted_value($ipn_data, 'GrossAmount'),
          'NetAmount' => affilicon_posted_value($ipn_data, 'NetAmount'),
          'TaxAmount' => affilicon_posted_value($ipn_data, 'TaxAmount'),
          'Currency' => affilicon_posted_value($ipn_data, 'Currency'),
          'Payout' => affilicon_posted_value($ipn_data, 'Payout'),
          'PayoutCurrency' => affilicon_posted_value($ipn_data, 'PayoutCurrency'),
          'ReceiptID' => affilicon_posted_value($ipn_data, 'ReceiptID'),
          'CreatedOn' => affilicon_posted_value($ipn_data, 'OrderDateTime'),
          'SubscriptionID' => affilicon_posted_value($ipn_data, 'SubscriptionID'),
          'BeginDate' => affilicon_posted_value($ipn_data, 'BeginDate'),
          'ExpireDate' => affilicon_posted_value($ipn_data, 'ExpireDate'),
          'PaymentStatus' => $BillingStatus ? PaymentIPNs::PAYMENT_STATUS_ACTIVE : PaymentIPNs::PAYMENT_STATUS_UNPAID,
        ];
        $ObjectProduct->InsertPayment($SubscriberID, $ReferenceID, $PaymentData, $ipn_data);
      }

      // register automations
      TransactionEmails::RegisterAutomations($UserID, $SubscriberID, $ReferenceID, true);

      // call the pending/thankyou url with all parameters
      $FullSubscriber = Subscribers::RetrieveFullSubscriber($ArraySubscriberList['RelOwnerUserID'], $SubscriberID, $ReferenceID);
      if ($FullSubscriber) {
        $url = Lists::GetSubscriptionRedirect($ArraySubscriberList['RelOwnerUserID'], $FullSubscriber, $ArraySubscriberList, $ReferenceID);
        drupal_http_request($url);
      }

      break;

    case Affilicon::EVENT_PAYMENT_MISSED:
    case Affilicon::EVENT_REFUND:
    case Affilicon::EVENT_CHARGEBACK:
      //(unsuccessful) payment events, tag customer accordingly (if subscriber exists)

      $is_test_mode = $api_mode != 'live'; //does not matter, tagging is OK in test mode

      //retrieve the product information by ProductID AND MerchantName (Script is called anonymously), if not found, function returns CatchAll product of MerchantName (ProductID = 0, if exists)
      $ObjectProduct = Affilicon::FromMerchantAndProductID($MerchantName, $ProductID);
      if (empty($ObjectProduct)) {
        //returns "OK", customer misconfiguration
        Affilicon::SendConfigurationErrorMail(0, 'AffiliCon', $MerchantName, $ProductID, $EmailAddress, affilicon_posted_value($ipn_data, 'ReceiptID'));
        break;
      }

      $ProductInformation = $ObjectProduct->GetData();

      //TODO DEV-2414 ReferenceID may come from listbuilding (product)
      $ReferenceID = 0;

      $UserID = $ProductInformation['RelOwnerUserID'];
      $BuildID = $ProductInformation['BuildID'];

      //only tag existing subscribers
      $SubscriberID = Subscription::RetrieveSubscriberIDByEmailAddress($UserID, $EmailAddress);
      if (empty($SubscriberID)) {
        //returns "OK", subscriber not found
        break;
      }

      $ArrayPayment = PaymentIPNs::GetPaymentByReceiptID($UserID, $BuildID, affilicon_posted_value($ipn_data, 'ReceiptID'));
      if (!empty($ArrayPayment)) {
        PaymentIPNs::SetPaymentStatus($ArrayPayment, PaymentIPNs::PAYMENT_STATUS_CHARGEBACK);
      }

      //tag subscriber
      switch ($event) {
        case Affilicon::EVENT_CHARGEBACK:
        case Affilicon::EVENT_PAYMENT_MISSED:
          //we treat 'on_payment_missed' as 'on_chargeback'
          $EventTag = Affilicon::EVENT_CHARGEBACK;
          $EventSmartTag = $ProductInformation['ChargebackSmartTagID'];
          break;
        case Affilicon::EVENT_REFUND:
        default:
          $EventTag = $event;
          $EventSmartTag = $ProductInformation['RefundSmartTagID'];
          break;
      }
      Subscribers::TagSubscriber($UserID, $SubscriberID, $EventSmartTag, $ReferenceID, TRUE);
      Subscribers::TagSubscriber($UserID, $SubscriberID, $ProductInformation['OptInSubscribeTo'][$EventTag], $ReferenceID, TRUE);
      TransactionEmails::RegisterAutomations($UserID, $SubscriberID, $ReferenceID, true);

      break;

    default:
      //Note: unknown event, AffiliCon probably will inform us about new features
      watchdog('kt-unknown-ipn', "AffiliCon: Unknown event (!Event).", array(
        '!Event' => $event,
        'IPN' => $ipn_data,
      ), WATCHDOG_INFO);
      break;
  }

  call_user_func($redirect_callback);
}

function klicktipp_ipn_receiver_affilicon_redirect($error = array()) {
  echo "OK"; //AffiliCon needs 'OK' or their IPN test will fail
  exit;
}

//original digistore code from here
function affilicon_signature($ipn_passphrase, $array) {
  unset($array['sha_sign']);

  $keys = array_keys($array);
  sort($keys);

  $sha_string = "";

  foreach ($keys as $key) {

    $value = $array[$key];

    $is_empty = !isset($value) || $value === "" || $value === FALSE;

    if ($is_empty) {
      continue;
    }

    $sha_string .= "$key=$value$ipn_passphrase";
  }

  $sha_sign = strtoupper(hash("sha512", $sha_string));

  return $sha_sign;
}


function affilicon_posted_value($ipn_data, $field) {
  $value = '';
  switch ($field) {
    case 'FirstName':
      $value = $ipn_data['custfirstname'];
      break;
    case 'LastName':
      $value = $ipn_data['custlastname'];
      break;
    case 'Street1':
      $value = $ipn_data['custaddr1'];
      break;
    case 'City':
      $value = $ipn_data['custcity'];
      break;
    case 'Zip':
      $value = $ipn_data['custzip'];
      break;
    case 'Country':
      $value = klicktipp_get_country_name_from_country_code(check_plain($ipn_data['custcc']));
      break;
    case 'State':
      $value = '';
      break;
    case 'Phone':
      $value = $ipn_data['custphone'];
      break;
    case 'OrderDateTime':
      $value = $ipn_data['transtime'];
      break;
    case 'ReceiptID':
      $value = $ipn_data['transreceipt'];
      break;
    case 'SubscriptionID':
      $value = $ipn_data['aboid'];;
      break;
    case 'BillingType':
      //Possible values: STANDARD, RECURRING
      $value = $ipn_data['prodtype'];
      break;
    case 'BillingStatus':
      // affilicon does not support the status
      $value = TRUE;
      break;
    case 'Amount':
      $value = intval($ipn_data['orderamount']) / 100;
      break;
    case 'GrossAmount':
      $value = intval($ipn_data['orderamount']);
      break;
    case 'NetAmount':
      $value = intval($ipn_data['orderamount']) - intval($ipn_data['taxamount']);
      break;
    case 'Payout':
      $value = intval($ipn_data['accountamount']);
      break;
    case 'Currency':
    case 'PayoutCurrency':
      $value = $ipn_data['currency'];
      break;
    case 'TaxAmount':
      $value = intval($ipn_data['taxamount']);
      break;
    case 'EmailAddress':
      $value = Subscribers::PunycodeEmailAddress(check_plain($ipn_data['email']));
      break;
    default:
      $value = $ipn_data[$field];
      break;
  }

  return empty($value) ? '' : check_plain($value);

}

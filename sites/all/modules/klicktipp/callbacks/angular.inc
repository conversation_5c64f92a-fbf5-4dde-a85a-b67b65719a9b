<?php

use App\Klicktipp\AccountManager;
use App\Klicktipp\Beamer;
use App\Klicktipp\ProductFruitsWidget;
use App\Klicktipp\Settings;
use App\Klicktipp\UserGroups;

function klicktipp_callbacks_angular_get_translation($lang = null) {

  //Note: global $language has already been changed to $user->language
  global $language;

  $langKey = $lang ?: $language->language;
  $path = "https://assets.klicktipp.com/translations/$langKey.js";

  if (module_exists('klicktipp_translation')) {
    $path = klicktipp_translation_get_s3_url($langKey);
  }

  if ( !empty($path) && file_get_contents($path) ) {
    header("Location: $path", TRUE, 302);
  }
  else {
    //language not found, just return a valid javascript
    drupal_add_http_header('Content-Type', 'text/javascript');
    echo "window['kt_translations'] = window['kt_translations'] || {};";
  }

  drupal_exit();

}

/**
 * Add google tracking scripts and helpscout beacon
 * @param $location
 */
function klicktipp_callbacks_angular_google_scripts($location) {

  $script = '';

  global $user;

  if ($location == 'header') {

    $GoogleTagManagerHeader = str_replace('%UserID%', $user->uid, Settings::get('klicktipp_theme_google_header_user_script'));
    $GoogleTagManagerHeader .= Settings::get('klicktipp_theme_google_header_script');

    if (!empty($GoogleTagManagerHeader)) {
      //encode this string to json to escape characters
      $script .= $GoogleTagManagerHeader;
    }

    //add helpscout beacon
    $helpscoutWidget = AccountManager::getHelpScoutWidget($user);
    if (!empty($helpscoutWidget)) {
      $script .= $helpscoutWidget;
    }

    $productFruitsWidget = new ProductFruitsWidget($user);
    if ($productFruitsWidget->isActive()) {
      $script .= $productFruitsWidget->getHeaderWidget();
    }

  }
  elseif ($location == 'body') {
    $GoogleTagManagerBody = Settings::get(KLICKTIPP_THEME_GOOGLE_SCRIPTS);

    if (!empty($GoogleTagManagerBody)) {
      //encode this string to json to escape characters
      $script .= $GoogleTagManagerBody;
    }

    $beamer = new Beamer($user);

    if ($beamer->isActivated()) {
      $script .= $beamer->getEmbedCode();
    }

  }

  if (empty($script)) {
    //no google scripts available, output valid javascript
    $documentWrite = '//OK';
  }
  else {
    $script = json_encode($script);
    $documentWrite = "document.write($script);";
  }

  drupal_add_http_header('Content-Type', 'text/javascript');

  echo $documentWrite;

  drupal_exit();

}

function klicktipp_callbacks_cockpit_header(): void {

 // Note: "build/campaign-builder/<CAMPAIGNID>" is not a trail in our menu -> no menu items
 //to get menu items, set active trail to "user/me"

  menu_tree_set_path(variable_get('klicktipp_theme_main_menu_authenticated', 'menu-main2'), 'user/me');
  $MainMenu = klicktipp_menu_main();

  $info = UserGroups::GetAccountAccessInfo();
  $logo = $info['logo'];
  $infoBar = $info['supportAccess'] || $info['otherAccountAccess'] ? 'has-info-bar' : '';

  // --- add css ---

  //clear all css added so far for the bootstrap theme and/or drupal modules
  drupal_static_reset('drupal_add_css');

  //TODO: trying to remove old styles
  drupal_add_css('build/css/old.css', array(
    'type' => 'file',
    'scope' => 'header',
    'group' => CSS_THEME,
    'every_page' => FALSE,
    'weight' => 1,
    'media' => 'all',
    'preprocess' => TRUE,
  ));

  //TODO: trying to remove old styles
  drupal_add_css('build/css/notifications.css', array(
    'type' => 'file',
    'scope' => 'header',
    'group' => CSS_THEME,
    'every_page' => FALSE,
    'weight' => 1,
    'media' => 'all',
    'preprocess' => TRUE,
  ));

  drupal_add_css("html, body { margin: 0; padding: 0;}", array(
    'type' => 'inline',
    'scope' => 'header',
    'group' => CSS_THEME,
    'every_page' => FALSE,
    'weight' => 1,
    'media' => 'all',
    'preprocess' => FALSE,
  ));

  drupal_add_css('build/css/main.css', array(
    'type' => 'file',
    'scope' => 'header',
    'group' => CSS_THEME,
    'every_page' => FALSE,
    'weight' => 3,
    'media' => 'all',
    'preprocess' => FALSE,
    'cache' => FALSE,
  ));

  // --- add javascript ---

  $ckeditor_cdn = variable_get(KLICKTIPP_THEME_CKEDITOR_CDN_EDITOR, 'https://cdn.ckeditor.com/4.4.2/full/ckeditor.js');
  //$ckeditor_jquery_adapter_cdn = variable_get(KLICKTIPP_THEME_CKEDITOR_CDN_JQUERY_ADAPTER, 'https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.4.2/adapters/jquery.js');

  drupal_add_js($ckeditor_cdn, array(
    'type' => 'external',
    'group' => JS_DEFAULT,
    'every_page' => FALSE,
    'weight' => 0,
    'scope' => 'header',
    'cache' => TRUE,
    'defer' => FALSE,
    'preprocess' => FALSE,
    'version' => NULL,
  ));

  $bundlejs = drupal_add_js();

  //remove unnecessary scripts for the drupal 'klicktippbootstrap' theme
  unset($bundlejs['sites/all/themes/bootstrapklicktipp/js/d3_autoresponders.js']);
  unset($bundlejs['sites/all/themes/bootstrapklicktipp/js/bootstrap_slider.js']);
  unset($bundlejs['misc/script.js']);

  //translate Drupal.t("...") strings
  locale_js_alter($bundlejs);

  global $user;
  /* TODO will be include a different way
  $GoogleTagManagerHeader = str_replace('%UserID%', $user->uid, \App\Klicktipp\Settings::get('klicktipp_theme_google_header_user_script'));
  $GoogleTagManagerHeader .= \App\Klicktipp\Settings::get('klicktipp_theme_google_header_script');
  $GoogleTagManagerBody = \App\Klicktipp\Settings::get(KLICKTIPP_THEME_GOOGLE_SCRIPTS);

  $helpscoutBeacon = klicktipp_helpscout_beacon();
  */

  $beamer = new Beamer($user);
  $showBeamerIcon = $beamer->isActivated() ? 1 : 0;

  $appUrl = APP_URL;
  $hello = t("Hello!");
  $loadingMessage = t('Klick-Tipp is loading...');
  $cancelButtonText = t(/*ignore*/KLICKTIPP_BUTTON_TEXT_CANCEL);
  $onlyGoogle = t('The Beta version of the Klick-Tipp Marketing Cockpit is only available for the Google Chrome Browser.');
  $downloadGoogle = t('Please download and install Google Chrome to continue.');
  $downloadButton = t('Download Google Chrome');

  // --- content javascript ---

  $contentJS = "
    <script type='text/javascript'>
      navigator.browserSpecs = (function () {
        var ua = navigator.userAgent,
          tem,
          M = ua.match(/(opera|chrome|safari|firefox|msie|trident(?=\/))\/?\s*(\d+)/i) || [];
        if (/trident/i.test(M[1])) {
          tem = /\brv[ :]+(\d+)/g.exec(ua) || [];
          return {
            name: 'IE',
            version: (tem[1] || '')
          };
        }
        if (M[1] === 'Chrome') {
          tem = ua.match(/\b(OPR|Edge)\/(\d+)/);
          if (tem != null) return {
            name: tem[1].replace('OPR', 'Opera'),
            version: tem[2]
          };
        }
        M = M[2] ? [M[1], M[2]] : [navigator.appName, navigator.appVersion, '-?'];
        if ((tem = ua.match(/version\/(\d+)/i)) != null)
          M.splice(1, 1, tem[1]);
        return {
          name: M[0],
          version: M[1]
        };
      })();

      // if (navigator.browserSpecs.name == 'Chrome') {
        jQuery('kt-app').show()
//      } else {
//        jQuery('#chrome-reqired').show();
//        jQuery('#loading').hide();
//      }

      //--- simulate bootstrap dropdown menu

      jQuery('#top-menu .dropdown a.dropdown-toggle').attr('href', 'JavaScript:void(0);');

      jQuery('#top-menu .dropdown a.dropdown-toggle').click(function () {
        jQuery('#top-menu .dropdown').removeClass('open');
        jQuery(this).parents('.dropdown').addClass('open');
      });

      // hide third level menus
      const thirdLevel = document.querySelectorAll('#top-menu li.dropdown-submenu > ul.dropdown-menu');
      for (let i = 0; i < thirdLevel.length; i++) {
        thirdLevel[i].style.display = 'none';
        thirdLevel[i].addEventListener('mouseover', function () {
          this.style.display = 'block';
        });
        thirdLevel[i].addEventListener('mouseleave', function () {
          this.style.display = 'none';
        });
      }

      // add hover event listener for second level menus
      const secondLevelToggels = document.querySelectorAll('#top-menu a.dropdown-submenu-toggle');
      for (let i = 0; i < secondLevelToggels.length; i++) {
        secondLevelToggels[i].addEventListener('mouseover', function () {
          this.nextElementSibling.style.display = 'block';
        });
        secondLevelToggels[i].addEventListener('mouseleave', function () {
          this.nextElementSibling.style.display = 'none';
        });
      }

      jQuery(document).click(function (event) {
        if (event && event.target && jQuery(event.target).hasClass('dropdown-toggle')) {
          return;
        }
        jQuery('#top-menu .dropdown').removeClass('open');
      });

      if ($showBeamerIcon) {
        document.getElementById('top-menu-beamer').style.display = 'inline-block';
      }
    </script>
  ";

  // --- content markup
  $contentMarkup = '<nav id="top-menu" class="navbar navbar-default navbar-fixed-top navbar-inverse navbar-no-electron '. $infoBar .'" role="navigation">
      <div class="row">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 navbar-col">
          <div class="navbar-logo-toggle">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-responsive-collapse">
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="/"><img src="' . $logo . '" style="height:30px;"/></a>
          </div>
          <div class="collapse navbar-collapse navbar-responsive-collapse">' . $MainMenu . '</div>
        </div>
      </div>
    </nav>
    <section id="loading" class="page-content system-page page-load">
      <header class="grid-6-12 centerblock">
        <figure class="klicky" style="width:300px"><img src="/build/images/klicky-run.svg">
          <div class="ui-spinner xlarge" style="bottom:-5%;margin-right:6rem;">
            <svg version="1.1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" viewBox="0 0 40 40"
              enable-background="new 0 0 40 40" xml:space="preserve">
              <path opacity="0.2" fill="#000"
                d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z">
              </path>
              <path fill="#000"
                d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                transform="rotate(306.644 20 20)">
                <animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 20 20"
                  to="360 20 20" dur="1s" repeatCount="indefinite"></animateTransform>
              </path>
            </svg>
          </div>
        </figure>
        <div class="air-top">
          <h1>' . $hello . '</h1>
          <h2 class="air-top">' . $loadingMessage . '</h2>
        </div>
      </header>
    </section>
    <div id="chrome-reqired" style="display:none;">
      <section class="page-content system-page">
        <header>
          <h1>Klick-Tipp Marketing Cockpit</h1>
        </header>
        <div class="system-message">
          <p class="message">' . $onlyGoogle . '</p>
          <p class="message">' . $downloadGoogle . '</p>
          <div class="align-center air-top-medium">
            <a class="button-action bevel google-logo"
              href="https://www.google.com/chrome/browser/desktop/index.html"><span
                class="google-icon"></span>' . $downloadButton . '</a>
            <a class="button-text glyph-cross-bold inline"
              href="' . $appUrl . '">' . $cancelButtonText . '</a>
          </div>
        </div>
      </section>
    </div>';

  $content = json_encode($contentMarkup . $contentJS);

  $header = drupal_get_js('header', $bundlejs, TRUE);

  $header .= "
    <script type='text/javascript'>
      function getContent() {
        return $content;
      }
    </script>
  ";

  $header .= drupal_get_css(NULL, TRUE);

  if (empty($header)) {
    //no google scripts available, output valid javascript
    $documentWrite = '//OK';
  }
  else {
    $header = json_encode($header);
    $documentWrite = "document.write($header);";
  }

  drupal_add_http_header('Content-Type', 'text/javascript');

  echo $documentWrite;

  drupal_exit();

}

<?php

use App\Klicktipp\Subaccount;
use App\Klicktipp\ToolPluginGeneralPublicAPI;
use App\Klicktipp\UserCache;

function klicktipp_menu_items() {

  $items = array();
  /**
   * Klick-Tipp menu callbacks with first level "admin"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp.module, includes/admin_settings
   */

// klicktipp settings
  $items['admin/config/klicktipp'] = array(
    'title' => 'Klicktipp',
    'description' => 'Klicktipp related configuration.',
    'position' => 'left',
    'weight' => -20,
    'page callback' => 'system_admin_menu_block_page',
    'access arguments' => array('administer klicktipp'),
    'file' => 'system.admin.inc',
    'file path' => drupal_get_path('module', 'system'),
  );
  $items['admin/config/klicktipp/general'] = array(
    'title' => 'General',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings'),
    'access arguments' => array('administer site configuration'),
    'weight' => 1,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/marketing'] = array(
    'title' => 'Marketing',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_marketing'),
    'access arguments' => array('administer site configuration'),
    'weight' => 1.5,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/facebook'] = array(
    'title' => 'Facebook',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_facebook'),
    'access arguments' => array('administer site configuration'),
    'weight' => 2,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/confirmation'] = array(
    'title' => 'Confirmation Email',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_confirmation'),
    'access arguments' => array('administer site configuration'),
    'weight' => 3,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/signatures'] = array(
    'title' => 'Signatures',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_signatures'),
    'access arguments' => array('administer site configuration'),
    'weight' => 4,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/trash'] = array(
    'title' => 'Email & Spam',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_trash'),
    'access arguments' => array('administer site configuration'),
    'weight' => 5,
    'file' => 'forms/admin_settings.inc',
  );

  $items['admin/config/klicktipp/trash/millionverifier'] = array(
    'title' => 'MillionVerifier',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_millionverifier_form'),
    'access arguments' => array('administer site configuration'),
    'file' => 'forms/admin_settings.inc',
  );

  $items['admin/config/klicktipp/aliases'] = array(
    'title' => 'Aliases',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_aliases'),
    'access arguments' => array('administer site configuration'),
    'weight' => 6,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/sms'] = array(
    'title' => 'SMS settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_sms'),
    'access arguments' => array('administer site configuration'),
    'weight' => 7,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/helpscout'] = array(
    'title' => 'HelpScout settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_helpscout'),
    'access arguments' => array('administer site configuration'),
    'weight' => 8,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/trans-queue-info'] = array(
    'title' => 'Transactional Queue Statistics',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_admin_settings_trans_queue_info_form'),
    'access arguments' => array('administer site configuration'),
    'weight' => 9,
    'file' => 'forms/admin_settings_trans_queue_info.inc',
  );
  $items['admin/config/klicktipp/productfruits'] = array(
    'title' => 'Product Fruits settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_product_fruits'),
    'access arguments' => array('administer site configuration'),
    'weight' => 8,
    'file' => 'forms/admin_settings.inc',
  );

  $items['admin/config/klicktipp/csa'] = array(
    'title' => 'CSA settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_csa_form'),
    'access arguments' => array('administer site configuration'),
    'weight' => 8,
    'file' => 'forms/admin_settings_csa.inc',
  );

  $items['admin/config/klicktipp/beamer'] = array(
    'title' => 'Beamer settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_beamer'),
    'access arguments' => array('administer site configuration'),
    'weight' => 8.5,
    'file' => 'forms/admin_settings_beamer.inc',
  );
  $items['admin/config/klicktipp/happiness'] = array(
    'title' => 'Customer Happiness settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_import_help_tags_form'),
    'access arguments' => array('administer site configuration'),
    'weight' => 9,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/angular'] = array(
    'title' => 'Angular settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_angular'),
    'access arguments' => array('administer site configuration'),
    'weight' => 10,
    'file' => 'forms/admin_settings_angular.inc',
  );
  $items['admin/config/klicktipp/custstats'] = array(
    'title' => 'Customer Statistics',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_customer_stats_form'),
    'access arguments' => array('access customer stats'),
    'weight' => 10.1,
    'file' => 'forms/admin_settings_customer_stats.inc',
  );

  $items['admin/config/klicktipp/segments'] = array(
    'title' => 'Customer Segments',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_segments_form'),
    'access arguments' => array('administer site configuration'),
    'weight' => 10.2,
    'file' => 'forms/admin_settings_segments.inc',
  );

// groups
  $items['admin/config/klicktipp/groups/browse'] = array(
    'title' => 'Groups',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_groups_overview_form'),
    'access arguments' => array('administer site configuration'),
    //  'type' => MENU_LOCAL_TASK,
    'weight' => 7,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/groups/delete/%'] = array(
    'title' => 'Delete groups',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_groups_overview_delete_confirm_form',
      5
    ),
    'access arguments' => array('administer site configuration'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/groups/create'] = array(
    'title' => 'Create group',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_groups_create_form'),
    'access arguments' => array('administer site configuration'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/groups/edit/%'] = array(
    'title' => 'Edit group',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_groups_form', 5),
    'access arguments' => array('administer site configuration'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings.inc',
  );

  // plugin
  $items['admin/config/klicktipp/plugin/browse'] = array(
    'title' => 'Plugins',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_plugin_overview_form'),
    'access arguments' => array('administer site configuration'),
    //  'type' => MENU_LOCAL_TASK,
    'weight' => 7.1,
    'file' => 'forms/admin_settings_plugin.inc',
  );
  $items['admin/config/klicktipp/plugin/delete/%'] = array(
    'title' => 'Delete plugin',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_settings_plugin_overview_delete_confirm_form',
      5
    ),
    'access arguments' => array('administer site configuration'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings_plugin.inc',
  );
  $items['admin/config/klicktipp/plugin/create'] = array(
    'title' => 'Create plugin',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_plugin_create_form'),
    'access arguments' => array('administer site configuration'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings_plugin.inc',
  );
  $items['admin/config/klicktipp/plugin/edit/%'] = array(
    'title' => 'Edit plugin',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_plugin_edit_form', 5),
    'access arguments' => array('administer site configuration'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings_plugin.inc',
  );

//klicktipp notification emails
  $items['admin/config/klicktipp/notifications'] = array(
    'title' => 'Notification emails',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_notification_email_overview_form'),
    'access arguments' => array('administer site configuration'),
    'weight' => 8,
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/admin_settings.inc',
  );

  $items['admin/config/klicktipp/notifications/%/edit'] = array(
    'title' => 'Klick-Tipp edit email notifications',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_notification_email_edit_form', 4),
    'access arguments' => array('administer site configuration'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings.inc',
  );

  $items['admin/config/klicktipp/notifications/user/%/edit'] = array(
    'title' => 'Klick-Tipp edit email notifications',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_notification_email_user_edit_form', 5),
    'access arguments' => array('administer site configuration'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings.inc',
  );

// merge accounts
  $items['admin/config/klicktipp/mergeaccounts'] = array(
    'title' => 'Merge accounts',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_merge_accounts_form'),
    'access arguments' => array('administer permissions'),
    'type' => MENU_NORMAL_ITEM,
    'weight' => 10,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/mergeaccounts/%/%/%'] = array(
    'title' => 'Merge accounts',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_merge_accounts_form', 4, 5, 6),
    'access arguments' => array('administer permissions'),
    'weight' => 10,
    'file' => 'forms/admin_settings.inc',
  );

// haters
  $items['admin/config/klicktipp/haters/browse'] = array(
    'title' => 'Haters',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_hater_overview_form'),
    'access arguments' => array('administer haters'),
    'type' => MENU_NORMAL_ITEM,
    'weight' => 11,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/haters/delete'] = array(
    'title' => 'Delete haters',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_hater_overview_delete_confirm_form'),
    'access arguments' => array('administer haters'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings.inc',
  );

// bulk abuse form
  $items['admin/config/klicktipp/bulk-report'] = array(
    'title' => 'Report Abuse (bulk)',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_bulk_report_abuse_form'),
    'access arguments' => array('administer haters'),
    'weight' => 11.1,
    'file' => 'forms/report_abuse.inc',
  );

  $items['admin/config/klicktipp/decodemsgid'] = array(
    'title' => 'Decode Message ID',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_decode_messageid_form'),
    'access arguments' => array('administer haters'),
    'weight' => 11.1,
    'file' => 'forms/report_abuse.inc',
  );

  $items['admin/config/klicktipp/bounces-simulation'] = array(
    'title' => 'Simulate a bounce',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_bounce_simulation_form'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 11.1,
    'file' => 'forms/admin_settings_bounce_simulation.inc',
  );


  $items['admin/config/klicktipp/blocking'] = array(
    'title' => 'Blocking Campaigns Statistics',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_blocking_campaigns_form'),
    'access arguments' => array('administer haters'),
    'weight' => 11.5,
    'file' => 'forms/admin_settings.inc',
  );

  // profiler
  $items['admin/config/klicktipp/profiler'] = array(
    'title' => 'Profiler',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_profiler_form'),
    'access arguments' => array('administer users'),
    'weight' => 12,
    'file' => 'forms/admin_settings.inc',
  );

  $items['admin/config/klicktipp/kubernetes'] = array(
    'title' => 'Settings for Kubernetes',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_kubernetes_form'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 13.1,
    'file' => 'forms/admin_settings_kubernetes.inc',
  );

    $items['admin/config/klicktipp/campaigns'] = array(
        'title' => 'Settings for Campaigns',
        'page callback' => 'drupal_get_form',
        'page arguments' => array('klicktipp_campaigns_form'),
        'access arguments' => array('administer klicktipp'),
        'weight' => 13,
        'file' => 'forms/admin_settings_campaigns.inc',
    );

// senderscore
  $items['admin/config/klicktipp/senderscore'] = array(
    'title' => 'Senderscore',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_senderscore_settings_overview_form'),
    'access arguments' => array('administer senderscore'),
    'weight' => 12.5,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/senderscore/%/edit'] = array(
    'title' => 'Senderscore edit',
    'type' => MENU_CALLBACK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_senderscore_settings_edit_form', 4),
    'access arguments' => array('administer senderscore'),
    'file' => 'forms/admin_settings.inc',
  );
//sms numbers
  $items['admin/config/klicktipp/smsnumbers'] = array(
    'title' => 'SMS numbers',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_smsnumbers_overview_form'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 12.5,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/smsnumbers/add'] = array(
    'title' => 'SMS number add',
    'type' => MENU_LOCAL_TASK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_smsnumbers_create_form'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 12.5,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/smsnumbers/%/edit'] = array(
    'title' => 'SMS number edit',
    'type' => MENU_CALLBACK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_smsnumbers_edit_form', 4),
    'access arguments' => array('administer klicktipp'),
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/tools'] = array(
    'title' => 'Marketing Tools',
    'type' => MENU_LOCAL_TASK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_marketing_tools'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 12.5,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/export'] = array(
    'title' => 'Subscriber export',
    'type' => MENU_LOCAL_TASK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_admin_subscriber_export_form'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 12.5,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/duplicates-detection'] = array(
    'title' => 'Detection of subscriber duplicates',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_duplicates_detection_form'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 13.1,
    'file' => 'forms/admin_settings_subscriber_duplicates.inc',
  );
  $items['admin/config/klicktipp/duplicates-export'] = array(
    'title' => 'Duplicate events export',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_duplicates_export_form'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 13.1,
    'file' => 'forms/admin_settings_subscriber_duplicates.inc',
  );
  $items['admin/config/klicktipp/smarttags-multisend'] = array(
    'title' => 'Evaluation of usage of multisend campaign smarttags',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_multisend_smarttag_report_form'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 13.1,
    'file' => 'forms/admin_settings_smarttags_of_multisend_campaigns.inc',
  );

  $items['admin/config/klicktipp/listbuilding'] = array(
    'title' => 'Listbuilding',
    'type' => MENU_LOCAL_TASK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_listbuildings'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 12.5,
    'file' => 'forms/admin_settings.inc',
  );
  $items['admin/config/klicktipp/firstnames/add'] = array(
    'title' => 'Firstnames',
    'type' => MENU_LOCAL_TASK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_firstnames_create_form'),
    'access arguments' => array('administer klicktipp'),
    'weight' => 12.5,
    'file' => 'forms/admin_settings.inc',
  );

  $items['admin/config/klicktipp/account-cancellation'] = array(
    'title' => 'Account Cancellation',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_admin_settings_account_cancellation_form'),
    'access arguments' => array('administer site configuration'),
    'weight' => 14,
    'file' => 'forms/admin_settings_cancellation.inc',
  );

//quick help settings
  $items['admin/structure/translate/quickhelp'] = array(
    'title' => 'Quick help',
    'weight' => 9,
    'type' => MENU_LOCAL_TASK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_quickhelp_overview_form'),
    'access arguments' => array('translate interface'),
    'file' => 'forms/admin_settings.inc',
  );

//quick help settings
  $items['admin/structure/translate/quickhelp/%/edit'] = array(
    'title' => 'Quick help',
    'type' => MENU_CALLBACK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_quickhelp_edit_form', 4),
    'access arguments' => array('translate interface'),
    'file' => 'forms/admin_settings.inc',
  );

// sync emails
  $items['admin/config/amember/sync'] = array(
    'title' => 'Sync amember mails',
    'page callback' => 'klicktipp_account_sync_user',
    'access arguments' => array('administer site configuration'),
    'file' => 'forms/admin_settings.inc',
  );

  $items['admin/user/customermenu'] = array(
    'title' => 'Customer menu',
    'type' => MENU_NORMAL_ITEM,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_customer_menu_form'),
    'access arguments' => array('administer permissions'),
    'file' => 'forms/admin_settings.inc',
  );

  //testmail
  $items['admin/%user/testmail'] = array(
    'title' => 'Send admin testmail',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_admin_testmail_form', 1),
    'access arguments' => array('administer klicktipp'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/admin_settings.inc',
  );

  $items['admin/user/customermenu/campaignexport/%user'] = array(
    'title' => 'Developer Campaign Export',
    'type' => MENU_CALLBACK,
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_automation_developer_export_form', 4),
    'access arguments' => array('administer klicktipp'),
    'file' => 'forms/admin_settings.inc',
  );

  /**
   * Klick-Tipp general menu callbacks
   * @see: klicktipp.module -> klicktipp_menu()
   */

// abuse form
  $items['abuse'] = array(
    'title' => 'Report Abuse',
    'page callback' => '_klicktipp_report_abuse_callback',
    'access arguments' => array('access content'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/report_abuse.inc',
  );

// charts ajax callback
  $items['charts/%/%/%'] = array(
    'title' => 'Klick-Tipp Charts',
    'page callback' => 'ktcharts_get_chart',
    'page arguments' => array(1, 2, 3),
    'access arguments' => array('access content'),
    // permissions are checked in 'ktcharts_get_chart'
    'type' => MENU_CALLBACK,
    'file' => 'includes/charts.inc',
  );

//optin pending "pending/<HASH>"
  $items['pending/%'] = array(
    'title' => 'Klick-Tipp Opt-in Confirmation',
    'page callback' => '_klicktipp_get_optin_template',
    'page arguments' => array('klicktipp_optin_pending', 1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
  );

//optin confirmation "thankyou/<HASH>"
  $items['thankyou/%'] = array(
    'title' => 'Klick-Tipp Opt-in Thank you',
    'page callback' => '_klicktipp_get_optin_template',
    'page arguments' => array('klicktipp_optin_thankyou', 1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
  );

// short links
  $items['drupal-s/%'] = array(
    'page callback' => 'klicktipp_redirect_short_link',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'includes/track_link.inc',
  );

// conversion pixel
  $items['drupal-pic/%/%'] = array(
    'page callback' => 'klicktipp_track_conversion',
    'page arguments' => array(1, 2),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'includes/track_link.inc',
  );

// ajax: states for country
  $items['states/%'] = array(
    'page callback' => '_klicktipp_account_get_states_json',
    'page arguments' => array(1),
    'access arguments' => array('access content'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account.inc',
  );

//order page
// everybody can access the page except subaccounts

// EK 2021-11-29 temporarily switch back to old order page (see BL-174)
// OLD version (order page in app.klicktipp.com)
// block restrictions determine the content for order (logged out) or upsell (logged in)
  $items['order'] = array(
    'page callback' => 'klicktipp_account_order_page',
    'access callback' => 'klicktipp_order_access',
    'type' => MENU_CALLBACK,
    'file' => 'forms/account.inc',
  );
// NEW version (order page in www.klicktipp.com = wordpress)
/*
  $items['order'] = array(
    'page callback' => 'klicktipp_goto',
    'page arguments' => array("https://www.klicktipp.com/pricing"),
    'access callback' => 'klicktipp_order_access',
    'type' => MENU_CALLBACK,
  );
  $items['order/enterprise'] = array(
    'page callback' => 'klicktipp_goto',
    'page arguments' => array("https://www.klicktipp.com/pricing/enterprise"),
    'access callback' => 'klicktipp_order_access',
    'type' => MENU_CALLBACK,
  );
*/

//partnerprogram
// everybody can access the page except subaccounts
  $items['partnerprogram'] = array(
    'page callback' => '_klicktipp_get_content_include',
    'page arguments' => array(KLICKTIPP_CONTENT_INCLUDE_PARTNERINFO),
    'access callback' => 'klicktipp_order_access',
    'type' => MENU_CALLBACK,
  );

  $items['partnerprogramm/tracking-pixel'] = array(
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_affiliate_tracking_pixels_form'),
    'access callback' => 'klicktipp_order_access',
    'type' => MENU_CALLBACK,
    'file' => 'forms/affiliate_tracking_pixels.inc',
  );

  $items['partnerprogramm/lead-cloning'] = array(
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_affiliate_promotions_form'),
    'access callback' => 'klicktipp_order_access',
    'type' => MENU_CALLBACK,
    'file' => 'forms/affiliate_tracking_pixels.inc',
  );

//download user vCard
  $items['drupal-vcard/%'] = array(
    'title' => 'Klick-Tipp vCard',
    'page callback' => 'klicktipp_account_get_user_vcard',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/account.inc',
  );

  //download signature vCard
  $items['drupal-signature_vcard/%'] = array(
    'title' => 'Klick-Tipp vCard',
    'page callback' => 'klicktipp_signature_vcard',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

//ifeedback
  $items['ifeedback/%'] = array(
    'title' => 'Unsubscription Completed',
    'page callback' => 'klicktipp_ifeedback_wrapper',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/ifeedback.inc',
  );

  //subscriber info
  $items['my-data/%'] = array(
    'title' => 'My data',
    'page callback' => 'klicktipp_subscriber_info',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

  //subscriber info update
  $items['my-data-update/%'] = array(
    'title' => 'My data update',
    'page callback' => 'klicktipp_subscriber_info_update',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

//senderscore stats page
  $items['senderscore'] = array(
    'title' => 'Klick-Tipp Sender Score',
    'page callback' => 'klicktipp_senderscore',
    'access arguments' => array('administer senderscore'),
    'type' => MENU_CALLBACK,
    'file' => 'includes/senderscore.inc',
  );

// watchdog details
  $items['wddetails/%'] = array(
    'title' => 'Watchdog details',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_prod_check_watchdog', 1),
    'access arguments' => array('administer users'),
    'file' => 'includes/prod_check.inc',
  );
// fbl details
  $items['fbldetails/%'] = array(
    'title' => 'FBL report',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_prod_check_fblreport', 1),
    'access arguments' => array('administer klicktipp'),
    'file' => 'includes/prod_check.inc',
  );

  $items['contact-us'] = array(
    'title' => 'Contact form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_contact_form'),
    'access callback' => 'user_is_logged_in',
    'type' => MENU_CALLBACK,
    'file' => 'forms/contact.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "subscribers" and "subscriber"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_subscriber
   */

  $items['subscribers/%user'] = array(
    'title' => 'Contacts',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_search_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber.inc',
  );

  // ajax callbacks for subscriber search
  $items['subscribers/%user/ajax'] = array(
    'title' => 'Contact counts and pager',
    'page callback' => '_klicktipp_subscriber_search_ajax_callback',
    'page arguments' => array(1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/subscriber.inc',
  );

  $items['subscribers/%user/export'] = array(
    'title' => 'Export contacts',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_subscriber_export_subscribers_form',
      1
    ),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/subscriber.inc',
  );

  $items['download/%'] = array(
    'title' => 'Download',
    'page callback' => 'klicktipp_subscriber_export_download',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

  $items['subscriber/%user/edit/%'] = array(
    'title' => 'Edit contact',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_subscriber_overview_form',
      1,
      3
    ),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/subscriber.inc',
    'weight' => -3,
  );

  $items['subscriber/%user/edit/%/overview'] = array(
    'title' => 'Overview',
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -3,
  );

  $items['subscriber/%user/merge/%'] = array(
    'title' => 'Merge contacts',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_merge_form', 1, 3, 4),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access duplicates management',
      '',
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber_merge.inc',
  );

  $items['subscriber/%user/merge/search/%/%'] = array(
    'title' => 'HTML',
    'page callback' => 'klicktipp_subscriber_merge_ajax',
    'page arguments' => array(1, 4, 5),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access duplicates management',
      '',
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/subscriber_merge.inc',
  );

  $items['subscriber/%user/edit/%/fields'] = array(
    'title' => 'Fields',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/subscriber.inc',
    'weight' => -2,
  );

  $items['subscriber/%user/edit/%/subscriptions'] = array(
    'title' => 'ContactInfo',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_subscriber_subscriptions_form',
      1,
      3
    ),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/subscriber.inc',
    'weight' => -1,
  );

  $items['subscriber/%user/edit/%/sendmail'] = array(
    'title' => 'Send email',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_subscriber_sendmail_form',
      1,
      3
    ),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/subscriber.inc',
  );

  $items['subscriber/%user/edit/%/sendinvoice/%'] = array(
    'title' => 'Send Invoice',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_subscriber_sendinvoice_form',
      1,
      3,
      5
    ),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/contact.inc',
  );

  $items['subscriber/%user/edit/%/wufoo'] = array(
    'title' => 'Wufoo Subscription forms',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_subscriber_wufoo_customfields_edit_form',
      1,
      3
    ),
    'access callback' => 'klicktipp_subscriber_wufoo_tab_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_ADMINISTRATOR),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/subscriber.inc',
  );

  $items['subscriber/%user/edit/%/history'] = array(
    'title' => 'Contact history',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_history_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/subscriber.inc',
  );

  //download vCard for a specific subscriber
  $items['subscriber/%user/vcard/%'] = array(
    'title' => 'Contact vCard',
    'page callback' => 'klicktipp_subscriber_vcard',
    'page arguments' => array(1, 3), // NOTE: there is fourth optional argument for reference id
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

  $items['change-email/%'] = array(
    'title' => 'Change email',
    'page callback' => 'klicktipp_subscriber_change_email_whitelabel',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/subscriber.inc',
  );

  // launch a pending queue entry for a specific subscriber and reference
  $items['subscriber/%user/pending-queue-entry/%/%/%'] = array(
    'title' => 'Launch Pending Queue Entry',
    'page callback' => 'klicktipp_subscriber_launch_pending_queue_entry',
    'page arguments' => array(1, 3, 4, 5),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

  //
  $items['subscriber/%user/skip-temporal-condition/%/%/%'] = array(
    'title' => 'Launch Pending Queue Entry',
    'page callback' => 'klicktipp_subscriber_skip_temporal_condition',
    'page arguments' => array(1, 3, 4, 5),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

  // redirect to a pre filled digistore cart
  $items['subscriber/digistorecart/%/%'] = array(
    'title' => 'Launch Pending Queue Entry',
    'page callback' => 'klicktipp_subscriber_create_digistore_cart_url_wrapper',
    'page arguments' => array(2, 3),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "contacts"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_subscriber
   */

  $items['contacts/%user/add'] = array(
    'title' => 'Add contact',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_add_contact_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access add contacts',
      KLICKTIPP_CONTENT_INCLUDE_ADD_CONTACT_PERMISSION_PAGE,
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/import.inc',
  );

  $items['contacts/%user/edit/%/%'] = array(
    'title' => 'Update Digital ID',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_edit_contactinfo_form', 1, 3, 4),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access add contacts',
      KLICKTIPP_CONTENT_INCLUDE_ADD_CONTACT_PERMISSION_PAGE,
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber.inc',
  );

  $items['contacts/%user/import'] = array(
    'title' => 'Import contacts',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_import_contacts_form', 1),
    'access callback' => 'klicktipp_subscriber_import_access',
    'access arguments' => array(1),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/import.inc',
  );

  $items['contacts/%user/blacklist'] = array(
    'title' => 'Blacklist',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_blacklist_contacts_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access user blacklist',
      '',
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/blacklist.inc',
  );

//unsubscription feedback
  $items['contacts/%user/feedback'] = array(
    'title' => 'Unsubscription feedback',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_subscriber_unsubscription_feedback_form',
      1
    ),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber.inc',
  );

  //inbound sms
  $items['contacts/%user/sms'] = array(
    'title' => 'Contact SMS',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_subscriber_inbound_sms_form',
      1
    ),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell'
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber.inc',
  );

  //facebook audience
  $items['contacts/%user/facebook-audience'] = array(
    'title' => 'Facebook Audience',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_facebook_audience_overview_form',
      1
    ),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access facebook audience',
      'klicktipp_content_include_facebook_audience_upsell'
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/tools_facebook_audience.inc',
  );

  $items['subscribers/%user/duplicates'] = array(
    'title' => 'Duplicates',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_duplicates_form', 1, '0'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access duplicates management',
      '',
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber_duplicates.inc',
  );

  $items['subscribers/%user/%/duplicates'] = array(
    'title' => 'Duplicates',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_duplicates_form', 1, 2),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access duplicates management',
      '',
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber_duplicates.inc',
  );

  $items['subscribers/%user/duplicate/%/%/delete'] = array(
    'title' => 'Duplicates',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_duplicates_delete_form', 1, 3, 4),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access duplicates management',
      '',
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber_duplicates.inc',
  );

  $items['subscribers/%user/duplicate/%/%/reject'] = array(
    'title' => 'Duplicates',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_duplicates_reject_form', 1, 3, 4),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access duplicates management',
      '',
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber_duplicates.inc',
  );

  $items['subscribers/%user/duplicate/%/%/merge'] = array(
    'title' => 'Merge duplicates',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subscriber_duplicates_merge_form', 1, 3, 4),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access duplicates management',
      '',
      (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/subscriber_duplicates.inc',
  );


  $items['facebook-audience/%user'] = array(
    'title' => 'KlickTipp Audience',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_facebook_synchronize_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access facebook audience',
      'klicktipp_content_include_facebook_audience_upsell'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_facebook_audience.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "customfields"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_customfields
   */

  $items['customfields/%user'] = array(
    'title' => 'Custom Fields',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_customfields_browse_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_BASICS),
    'type' => MENU_NORMAL_ITEM,
    'weight' => 2,
    'file' => 'forms/customfields.inc',
  );

  $items['customfields/%user/add'] = array(
    'title' => 'Create Field',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_customfields_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/customfields.inc',
  );

  $items['customfields/%user/edit/%'] = array(
    'title' => 'Edit Field',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_customfields_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/customfields.inc',
  );

  $items['customfields/%user/global/%'] = array(
    'title' => 'Edit Field',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_customfields_global_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_BASICS),
    'type' => MENU_CALLBACK,
    'file' => 'forms/customfields.inc',
  );

  $items["customfields/%user/merge/%"] = array(
    'title' => 'Categories autocomplete',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_customfields_merge_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/customfields.inc',
  );

  $items["customfields/%user/categories"] = array(
    'title' => 'Categories autocomplete',
    'page callback' => 'klicktipp_customfields_categories_ajax',
    'page arguments' => array(1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/customfields.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "listbuilding"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_listinterfaces
   */

// --- Overview ---
  $items['listbuilding/%user'] = array(
    'title' => 'Listbuilding',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_listbuilding_overview_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/listbuilding.inc',
  );

// --- Create Listbuilding ---
  $items['listbuilding/%user/create'] = array(
    'title' => 'Listbuilding',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_listbuilding_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/listbuilding.inc',
  );

// --- Subscription forms ---

//edid
//TODO: REMOVEOLDFORMS
  $items['listbuilding/%user/form/%/edit'] = array(
    'title' => 'Edit form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_form_builder', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'includes/list_formbuilder.inc',
  );

  // subscription form custom code

  //add
  $items['listbuilding/%user/form-custom/add'] = array(
    'title' => 'Create new subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_custom_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_custom.inc',
  );

  //edit
  $items['listbuilding/%user/form-custom/%/edit'] = array(
    'title' => 'Edit subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_custom_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_custom.inc',
  );

  // subscription form raw code

  //add
  $items['listbuilding/%user/form-raw/add'] = array(
    'title' => 'Create new subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_raw_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_raw.inc',
  );

  //edit
  $items['listbuilding/%user/form-raw/%/edit'] = array(
    'title' => 'Edit subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_raw_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_raw.inc',
  );

  // subscription form inline

  //add
  $items['listbuilding/%user/form-inline/add'] = array(
    'title' => 'Create new subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_inline_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_inline.inc',
  );

  //edit
  $items['listbuilding/%user/form-inline/%/edit'] = array(
    'title' => 'Edit subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_inline_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_inline.inc',
  );

  // subscription form widget

  //add
  $items['listbuilding/%user/form-widget/add'] = array(
    'title' => 'Create new subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_widget_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_widget.inc',
  );

  //edit
  $items['listbuilding/%user/form-widget/%/edit'] = array(
    'title' => 'Edit subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_widget_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_widget.inc',
  );

  // preview
  $items['listbuilding/%user/form-widget/%/preview'] = array(
    'title' => 'Preview subscription form',
    'page callback' => 'klicktipp_list_forms_widget_preview',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'includes/list_forms.inc',
  );

  // subscription leadpages

  //add
  $items['listbuilding/%user/form-leadpages/add'] = array(
    'title' => 'Create new Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_leadpages_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_leadpages.inc',
  );

  //edit
  $items['listbuilding/%user/form-leadpages/%/edit'] = array(
    'title' => 'Edit Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_leadpages_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_leadpages.inc',
  );

  // subscription wistia

  //add
  $items['listbuilding/%user/form-wistia/add'] = array(
    'title' => 'Create new Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_wistia_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_wistia.inc',
  );

  //edit
  $items['listbuilding/%user/form-wistia/%/edit'] = array(
    'title' => 'Edit Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_wistia_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_wistia.inc',
  );

  // subscription optimizepress

  //add
  $items['listbuilding/%user/form-optimizepress/add'] = array(
    'title' => 'Create new Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_optimizepress_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_optimizepress.inc',
  );

  //edit
  $items['listbuilding/%user/form-optimizepress/%/edit'] = array(
    'title' => 'Edit Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_optimizepress_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_optimizepress.inc',
  );

  // subscription optimizepress

  //add
  $items['listbuilding/%user/form-thrive/add'] = array(
    'title' => 'Create new Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_thrive_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_thrive.inc',
  );

  //edit
  $items['listbuilding/%user/form-thrive/%/edit'] = array(
    'title' => 'Edit Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_forms_thrive_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_forms_thrive.inc',
  );

// --- Request ---
//add
  $items['listbuilding/%user/request/add'] = array(
    'title' => 'Create new request',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_request_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_request.inc',
  );

//edit
  $items['listbuilding/%user/request/%/edit'] = array(
    'title' => 'Edit request',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_request_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_request.inc',
  );

// --- API ---

//add
  $items['listbuilding/%user/api/add'] = array(
    'title' => 'Create new api key',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_api_create_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'klicktipp api',
      KLICKTIPP_CONTENT_INCLUDE_APIKEY_UPSELL,
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_api.inc',
  );

//edit
  $items['listbuilding/%user/api/%/edit'] = array(
    'title' => 'Edit api key',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_api_edit_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'klicktipp api',
      KLICKTIPP_CONTENT_INCLUDE_APIKEY_UPSELL,
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_api.inc',
  );

// --- IPN DigiStore24 ---

//ipn
  $items['digistoreipn'] = array(
    'title' => 'Digistore24 IPN',
    'page callback' => 'klicktipp_ipn_receiver_digistore24_wrapper',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/ipn_digistore24.inc',
  );

//add
  $items['listbuilding/%user/digistore/add'] = array(
    'title' => 'Create new Digistore24 product',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_digistore_create_form', 1),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access IPN DigiStore24'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_digistore24.inc',
  );

//edit
  $items['listbuilding/%user/digistore/%/edit'] = array(
    'title' => 'Edit Digistore24 product',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_digistore_edit_form', 1, 3),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access IPN DigiStore24'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_digistore24.inc',
  );

  // Digistore affiliation (multiple device tracking)
  $items['drupal-digit/%'] = array(
    'title' => 'Digistore Affiliates',
    'page callback' => 'klicktipp_subscriber_digistore_affiliates',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

// --- IPN AffiliCon ---

//ipn
  $items['affiliconipn'] = array(
    'title' => 'Affilicon IPN',
    'page callback' => 'klicktipp_ipn_receiver_affilicon_wrapper',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/ipn_affilicon.inc',
  );

//add
  $items['listbuilding/%user/affilicon/add'] = array(
    'title' => 'Create new AffiliCon product',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_affilicon_create_form', 1),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access IPN AffiliCon'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_affilicon.inc',
  );

//edit
  $items['listbuilding/%user/affilicon/%/edit'] = array(
    'title' => 'Edit AffiliCon product',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_affilicon_edit_form', 1, 3),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access IPN AffiliCon'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_affilicon.inc',
  );

// --- IPN PayPal ---

//ipn
  $items['paypalipn/%'] = array(
    'title' => 'PayPal IPN',
    'page callback' => 'klicktipp_ipn_receiver_paypal_wrapper',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/ipn_paypal.inc',
  );

//add
  $items['listbuilding/%user/paypal/add'] = array(
    'title' => 'Create new PayPal product',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_paypal_create_form', 1),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access IPN PayPal'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_paypal.inc',
  );

//edit
  $items['listbuilding/%user/paypal/%/edit'] = array(
    'title' => 'Edit PayPal product',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_paypal_edit_form', 1, 3),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access IPN PayPal'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_paypal.inc',
  );


// --- IPN Wufoo ---

//add
  $items['listbuilding/%user/wufoo/add'] = array(
    'title' => 'Create new Wufoo Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_wufoo_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_wufoo.inc',
  );

//edit
  $items['listbuilding/%user/wufoo/%/edit'] = array(
    'title' => 'Edit Wufoo Subscription form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_wufoo_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_wufoo.inc',
  );

// --- IPN Clickbank ---

//ipn
  $items['clickbankipn/%'] = array(
    'title' => 'Clickbank IPN',
    'page callback' => 'klicktipp_ipn_receiver_clickbank_wrapper',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/ipn_clickbank.inc',
  );

//add
  $items['listbuilding/%user/clickbank/add'] = array(
    'title' => 'Create new Clickbank product',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_clickbank_create_form', 1),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access IPN Clickbank'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_clickbank.inc',
  );

//edit
  $items['listbuilding/%user/clickbank/%/edit'] = array(
    'title' => 'Edit Clickbank product',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_clickbank_edit_form', 1, 3),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access IPN Clickbank'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_clickbank.inc',
  );

  // --- YouTube content analysis tool ---
  $items['youtube-analysis/%user'] = array(
    'title' => 'Youtube Analysis',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_youtube_content_analysis_search_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access youtube content analysis',
      'klicktipp_content_include_youtube_analysis_upsell',
      (string) Subaccount::SUBACCOUNT_ADMINISTRATOR,
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/youtube_content_analysis.inc',
  );

  $items['youtube-analysis/%user/ajax'] = array(
    'title' => 'Youtube Analysis',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_youtube_content_analysis_ajax', 1),
    'access arguments' => array('access youtube content analysis', 1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/youtube_content_analysis.inc',
  );

  $items['youtube-analysis/%user/summerize/%/%'] = array(
    'title' => 'Youtube Analysis',
    'page callback' => 'klicktipp_youtube_content_analysis_summerize',
    'page arguments' => array(1, 3, 4),
    'access arguments' => array('access youtube content analysis', 1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/youtube_content_analysis.inc',
  );

  $items['youtube-analysis/%user/summary/%'] = array(
    'title' => 'Youtube Analysis',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('_klicktipp_summerize_youtube_video_results_form', 1, 3),
    'access arguments' => array('access youtube content analysis', 1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/youtube_content_analysis.inc',
  );

  // --- SMS Inbound ---

  // SMS Nexmo
  $items['drupal-nexmosms'] = array(
    'title' => 'Handle Nexmo SMS inbound events',
    'page callback' => 'klicktipp_sms_receiver_nexmo_wrapper',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/inbound_sms.inc',
  );

  // SMS Twilio
  $items['drupal-twiliosms'] = array(
    'title' => 'Handle Twilio SMS inbound events',
    'page callback' => 'klicktipp_sms_receiver_twilio_wrapper',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/inbound_sms.inc',
  );

  // SMS Bounce Nexmo
  $items['drupal-nexmobounce'] = array(
    'title' => 'Handle Nexmo SMS Bounces',
    'page callback' => 'klicktipp_sms_bounce_receiver_nexmo_wrapper',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/inbound_sms.inc',
  );

   $items['drupal-nexmo'] = array(
    'title' => 'Handle delivery receipts',
    'page callback' => 'klicktipp_sms_delivery_receipt_nexmo',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/inbound_sms.inc',
  );

  // SMS Bounce Twilio
  $items['drupal-twiliobounce/%'] = array(
    'title' => 'Handle Twilio SMS Bounces',
    'page callback' => 'klicktipp_sms_bounce_receiver_twilio_wrapper',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/inbound_sms.inc',
  );

// --- SMS Listbuilding ---

  //add
  $items['listbuilding/%user/sms/add'] = array(
      'title' => 'Create SMS Listbuilding',
      'page callback' => 'drupal_get_form',
      'page arguments' => array('klicktipp_list_sms_create_form', 1),
      'access callback' => 'klicktipp_upsell_access',
      'access arguments' => array(
        1,
        'access sms marketing',
        'klicktipp_content_include_sms_upsell'
      ),
      'type' => MENU_CALLBACK,
      'file' => 'forms/list_sms.inc',
  );

  //edit
  $items['listbuilding/%user/sms/%/edit'] = array(
      'title' => 'Edit SMS Listbuilding',
      'page callback' => 'drupal_get_form',
      'page arguments' => array('klicktipp_list_sms_edit_form', 1, 3),
      'access callback' => 'klicktipp_upsell_access',
      'access arguments' => array(
        1,
        'access sms marketing',
        'klicktipp_content_include_sms_upsell'
      ),
      'type' => MENU_CALLBACK,
      'file' => 'forms/list_sms.inc',
  );

  //add
  $items['listbuilding/%user/nexmo/add'] = array(
    'title' => 'Create Nexmo SMS Listbuilding',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_sms_nexmo_create_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_sms_nexmo.inc',
  );

  //edit
  $items['listbuilding/%user/nexmo/%/edit'] = array(
    'title' => 'Edit Nexmo SMS Listbuilding',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_sms_nexmo_edit_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_sms_nexmo.inc',
  );

  //add
  $items['listbuilding/%user/twilio/add'] = array(
    'title' => 'Create Twilio SMS Listbuilding',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_sms_twilio_create_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_sms_twilio.inc',
  );

  //edit
  $items['listbuilding/%user/twilio/%/edit'] = array(
    'title' => 'Edit Twilio SMS Listbuilding',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_sms_twilio_edit_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_sms_twilio.inc',
  );


//IPN Wufoo WebHook
  $items['drupal-wufooipn'] = array(
    'title' => 'KlickTipp Wufoo IPN',
    'page callback' => 'klicktipp_list_wufoo_signin',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/ipn_wufoo.inc',
  );

//Wufoo prefill link "wufoo"/<API_KEY>/< MINIHASH-USERID-SUBSCRIBERID == %Subscriber:Secret% >
  $items['drupal-wufoo/%/%'] = array(
    'title' => 'KlickTipp Wufoo Prefill',
    'page callback' => 'klicktipp_list_wufoo_prefill',
    'page arguments' => array(1, 2),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/ipn_wufoo.inc',
  );

//Wufoo thankyou callback link "wufoothankyou"/<API_KEY>/?email=<EMAIL-ADDRESS>&SubscriberID=<SUBSCRIBER-ID>
  $items['drupal-wufoothankyou/%'] = array(
    'title' => 'KlickTipp Wufoo Thankyou',
    'page callback' => 'klicktipp_list_wufoo_thankyou',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/ipn_wufoo.inc',
  );

  // --------- Business Card ----------

  // Business Card Reader Pro Webhook
  $items['drupal-businesscard/%'] = array(
    'title' => 'Handle Business Card Reader event',
    'page callback' => 'klicktipp_receiver_business_card_wrapper',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/businesscardreader.inc',
  );

  //edit
  $items['listbuilding/%user/businesscard/%/edit'] = array(
    'title' => 'Edit business card reader',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_businesscard_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_businesscardreader.inc',
  );

  // --------- Event ----------

  //add
  $items['listbuilding/%user/event/add'] = array(
    'title' => 'Create new event',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_event_create_form', 1),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access bcr event'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_event.inc',
  );

  //edit
  $items['listbuilding/%user/event/%/edit'] = array(
    'title' => 'Edit event',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_event_edit_form', 1, 3),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access bcr event'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_event.inc',
  );

  //deeplink
  $items['listbuilding/%user/event/%/deeplink/%/%'] = array(
    'title' => 'Edit event',
    'page callback' => 'klicktipp_list_event_deeplink_ajax',
    'page arguments' => array(1, 3, 5, 6),
    'access callback' => 'klicktipp_listinterfaces_IPN_access',
    'access arguments' => array(1, 'access bcr event'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/list_event.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "tags"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_tags
   */

  $items['tags/%user'] = array(
    'title' => 'Manual Tags',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tag_browse_form', 1, 'manual'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tag.inc',
  );

  $items['tags/%user/csv-export/%'] = array(
    'title' => 'Manual Tags',
    'page callback' => 'klicktipp_export_tags_as_csv',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tag.inc',
  );

  $items['tags/%user/smartlinks'] = array(
    'title' => 'SmartLinks',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tag_browse_form', 1, 'smartlink'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tag.inc',
  );

  $items['tags/%user/add'] = array(
    'title' => 'Create tag',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tag_edit_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tag.inc',
  );

  $items['tags/%user/smartlink/add'] = array(
    'title' => 'Create SmartLink',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tag_edit_form', 1, '', 'smartlink'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tag.inc',
  );

  $items['tags/%user/edit/%'] = array(
    'title' => 'Edit tag',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tag_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tag.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "lists"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_lists
   */

  $items['lists/%user'] = array(
    'title' => 'Subscription processes',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_overview_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/lists.inc',
  );

  $items['lists/%user/add'] = array(
    'title' => 'Create new subscription process',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/lists.inc',
  );

  $items['lists/%user/%/copy'] = array(
    'title' => 'Edit subscription process',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_create_form', 1, 2),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/lists.inc',
  );

  $items['lists/%user/%/edit'] = array(
    'title' => 'Edit subscription process',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_list_edit_form', 1, 2),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/lists.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "signatures"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_signatures
   */

  $items['signatures/%user'] = array(
    'title' => 'Signatures',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_signatures_overview_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/signatures.inc',
  );

  $items['signatures/%user/add'] = array(
    'title' => 'Create signatures',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_signatures_edit_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'weight' => 1,
    'file' => 'forms/signatures.inc',
  );

  $items['signatures/%user/edit/%'] = array(
    'title' => 'Edit signature',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_signatures_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'weight' => 1,
    'file' => 'forms/signatures.inc',
  );

  $items['signatures/%user/copy/%'] = array(
    'title' => 'Edit signature',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_signatures_edit_form', 1, 3, 'copy'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'weight' => 1,
    'file' => 'forms/signatures.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "unsubscriptions"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_unsubscriptions
   */

  $items['unsubscriptions/%user'] = array(
    'title' => 'Unsubscription messages',
    'page callback' => 'drupal_get_form',
    'page arguments' => array(
      'klicktipp_unsubscriptions_dialog_browse_form',
      1
    ),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'weight' => 2,
    'file' => 'forms/unsubscriptions.inc',
  );

  $items['unsubscriptions/%user/messages/add'] = array(
    'title' => 'Create unsupscription message',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_unsubscriptions_dialog_form', 1),
    'access callback' => 'klicktipp_unsubscriptions_add_message_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/unsubscriptions.inc',
  );

  $items['unsubscriptions/%user/messages/%/edit'] = array(
    'title' => 'Edit unsubscription message',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_unsubscriptions_dialog_form', 1, 3),
    'access callback' => 'klicktipp_unsubscriptions_edit_message_access',
    'access arguments' => array(1, 3),
    'type' => MENU_CALLBACK,
    'file' => 'forms/unsubscriptions.inc',
  );

  $items['unsubscriptions/%user/url'] = array(
    'title' => 'Unsubscription URLs',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_unsubscriptions_url_browse_form', 1),
    'access callback' => 'klicktipp_unsubscriptions_url_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/unsubscriptions.inc',
  );

  $items['unsubscriptions/%user/url/add'] = array(
    'title' => 'Create unsubscription URL',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_unsubscriptions_url_form', 1),
    'access callback' => 'klicktipp_unsubscriptions_url_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/unsubscriptions.inc',
  );

  $items['unsubscriptions/%user/url/%/edit'] = array(
    'title' => 'Edit unsubscription URL',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_unsubscriptions_url_form', 1, 3),
    'access callback' => 'klicktipp_unsubscriptions_url_edit_access',
    'access arguments' => array(1, 3),
    'type' => MENU_CALLBACK,
    'file' => 'forms/unsubscriptions.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "tools"
   */

  $items['tools/%user'] = array(
    'title' => 'Marketing Tools',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_marketing_tools_browse_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_BASICS),
    'type' => MENU_CALLBACK,
    'file' => 'forms/marketing_tools.inc',
  );

  //marketing tools - countdown

  //create
  $items['tools/%user/countdown/add'] = array(
    'title' => 'Create Countdown',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_countdown_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_countdown.inc',
  );

  //edit
  $items['tools/%user/countdown/%/edit'] = array(
    'title' => 'Edit Countdown',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_countdown_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_countdown.inc',
  );

  $items['drupal-clock/%'] = array(
    'title' => 'Countdown',
    'page callback' => 'klicktipp_tools_countdown_image',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_countdown.inc',
  );

  $items['drupal-infourl/%'] = array(
    'title' => 'Countdown Link',
    'page callback' => 'klicktipp_tools_countdown_redirect',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_countdown.inc',
  );

  //marketing tools - taggingpixel

  //create
  $items['tools/%user/taggingpixel/add'] = array(
    'title' => 'Create Tagging Pixel',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_taggingpixel_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_taggingpixel.inc',
  );

  //edit
  $items['tools/%user/taggingpixel/%/edit'] = array(
    'title' => 'Edit Tagging Pixel',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_taggingpixel_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_taggingpixel.inc',
  );

  $items['drupal-pix/%'] = array(
    'title' => 'Tagging Pixel',
    'page callback' => 'klicktipp_tools_taggingpixel_image',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_taggingpixel.inc',
  );

  //marketing tools - outbound

  //create
  $items['tools/%user/outbound/add'] = array(
    'title' => 'Create Outbound',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_outbound_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_outbound.inc',
  );

  //edit
  $items['tools/%user/outbound/%/edit'] = array(
    'title' => 'Edit Outbound',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_outbound_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_outbound.inc',
  );

  //marketing tools - kajabi

  //create
  $items['tools/%user/kajabi/add'] = array(
    'title' => 'Create Kajabi Membership Activation',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_kajabi_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_kajabi.inc',
  );

  //edit
  $items['tools/%user/kajabi/%/edit'] = array(
    'title' => 'Edit Kajabi Membership Activation',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_kajabi_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_kajabi.inc',
  );

  //marketing tools - zapier

  //create
  $items['tools/%user/zapier/add'] = array(
    'title' => 'Create Zapier Trigger',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_zapier_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_zapier.inc',
  );

  //edit
  $items['tools/%user/zapier/%/edit'] = array(
    'title' => 'Edit Zapier Trigger',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_zapier_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_zapier.inc',
  );

  //marketing tools - template

  //create
  $items['tools/%user/template/add'] = array(
    'title' => 'Create Automation Template',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_template_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_template.inc',
  );

  //edit
  $items['tools/%user/template/%/edit'] = array(
    'title' => 'Edit Automation Template',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_template_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_template.inc',
  );

  $items['template/%'] = array(
    'title' => 'Import Automation Template',
    'page callback' => 'klicktipp_tools_template_import',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_template.inc',
  );

  //marketing tools - statistics

  //create
  $items['tools/%user/statistics/add'] = array(
    'title' => 'Create Statistic',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_statistics_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_statistics.inc',
  );

  //edit
  $items['tools/%user/statistics/%/edit'] = array(
    'title' => 'Edit Statistic',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_statistics_edit_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_statistics.inc',
  );

  $items['tools/%user/statistics/%/builder'] = array(
    'title' => 'Klick-Tipp Analytics',
    'page callback' => 'klicktipp_tools_statistics_builder',
    'page arguments' => array(3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_statistics.inc',
  );

  // new Marketing Tools (Conversion tools Splittest-Club)

  $items['marketingtools/%user'] = array(
    'title' => 'Marketing Tools',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_marketing_tools_overview_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/marketing_tools.inc',
  );

  $items['marketingtools/%user/create'] = array(
    'title' => 'Create Marketing Tool',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_marketing_tools_create_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/marketing_tools.inc',
  );

  //marketing tools - website-exitlightbox

  //create
  $items['marketingtools/%user/exitlightbox/add'] = array(
    'title' => 'Create Exit Lightbox',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_exitlightbox_create_form', 1),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_website_exitlightbox.inc',
  );

  $items['marketingtools/%user/exitlightbox/%'] = array(
    'title' => 'View Exit Lightbox',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_exitlightbox_view_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/tools_website_exitlightbox.inc',
  );

  //affix
  $items['marketingtools/%user/exitlightbox/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => 1,
  );

  //affix
  $items['marketingtools/%user/exitlightbox/%/edit'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_exitlightbox_edit_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 2,
    'file' => 'forms/tools_website_exitlightbox.inc',
  );

  //affix
  $items['marketingtools/%user/exitlightbox/%/embed'] = array(
    'title' => 'Embed code',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_exitlightbox_embed_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 3,
    'file' => 'forms/tools_website_exitlightbox.inc',
  );

  // download wordpress plugin for exitlightbox
  $items['marketingtools/%user/exitlightbox/%/wpplugin'] = array(
    'page callback' => 'klicktipp_tools_website_exitlightbox_wpplugin',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_stc_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_website_exitlightbox.inc',
  );

  //marketing tools - website-feedback

  //create
  $items['marketingtools/%user/feedback/add'] = array(
    'title' => 'Create Feedback Form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_feedback_create_form', 1),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_website_feedback.inc',
  );

  $items['marketingtools/%user/feedback/%'] = array(
    'title' => 'View Feedback Form',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_feedback_view_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/tools_website_feedback.inc',
  );

  //affix
  $items['marketingtools/%user/feedback/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => 1,
  );

  //affix
  $items['marketingtools/%user/feedback/%/edit'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_feedback_edit_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 2,
    'file' => 'forms/tools_website_feedback.inc',
  );

  //affix
  $items['marketingtools/%user/feedback/%/embed'] = array(
    'title' => 'Embed code',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_feedback_embed_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 3,
    'file' => 'forms/tools_website_feedback.inc',
  );

  // delete one feedback
  $items['marketingtools/%user/feedback/%/deletefeedback/%'] = array(
    'page callback' => 'klicktipp_tools_website_feedback_view_delete_feedback',
    'page arguments' => array(1, 3, 5),
    'access callback' => 'klicktipp_stc_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_website_feedback.inc',
  );

  // export feedback
  $items['marketingtools/%user/feedback/%/export'] = array(
    'page callback' => 'klicktipp_tools_website_feedback_export',
    'page arguments' => array(1, 3, 5),
    'access callback' => 'klicktipp_stc_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_website_feedback.inc',
  );

  //marketing tools - website-onetimeoffer

  //create
  $items['marketingtools/%user/onetimeoffer/add'] = array(
    'title' => 'Create One Time Offer',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_onetimeoffer_create_form', 1),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_website_onetimeoffer.inc',
  );

  $items['marketingtools/%user/onetimeoffer/%'] = array(
    'title' => 'View One Time Offer',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_onetimeoffer_view_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/tools_website_onetimeoffer.inc',
  );

  //affix
  $items['marketingtools/%user/onetimeoffer/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => 1,
  );

  //affix
  $items['marketingtools/%user/onetimeoffer/%/edit'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_onetimeoffer_edit_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 2,
    'file' => 'forms/tools_website_onetimeoffer.inc',
  );

  //affix
  $items['marketingtools/%user/onetimeoffer/%/embed'] = array(
    'title' => 'Embed code',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_onetimeoffer_embed_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 3,
    'file' => 'forms/tools_website_onetimeoffer.inc',
  );

  //marketing tools - website-socialproof

  //create
  $items['marketingtools/%user/socialproof/add'] = array(
    'title' => 'Create Social Proof Counter',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_socialproof_create_form', 1),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_website_socialproof.inc',
  );

  $items['marketingtools/%user/socialproof/%'] = array(
    'title' => 'View Social Proof Counter',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_socialproof_view_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/tools_website_socialproof.inc',
  );

  //affix
  $items['marketingtools/%user/socialproof/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => 1,
  );

  //affix
  $items['marketingtools/%user/socialproof/%/edit'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_socialproof_edit_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 2,
    'file' => 'forms/tools_website_socialproof.inc',
  );

  //affix
  $items['marketingtools/%user/socialproof/%/embed'] = array(
    'title' => 'Embed code',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_socialproof_embed_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 3,
    'file' => 'forms/tools_website_socialproof.inc',
  );

  //marketing tools - website-splittest

  //create
  $items['marketingtools/%user/splittest/add'] = array(
    'title' => 'Create Website Splittest',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_splittest_create_form', 1),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_website_splittest.inc',
  );

  $items['marketingtools/%user/splittest/%'] = array(
    'title' => 'View Splittest',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_splittest_view_form', 1, 3, '1'),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/tools_website_splittest.inc',
  );

  //affix
  $items['marketingtools/%user/splittest/%/view'] = array(
    'title callback' => 'klicktipp_tools_website_splittest_title_callback',
    'title arguments' => array(1, 3, '1', t('View')),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => 1,
  );

  //affix
  $items['marketingtools/%user/splittest/%/2'] = array(
    'title callback' => 'klicktipp_tools_website_splittest_title_callback',
    'title arguments' => array(1, 3, '2', t('View')),
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_splittest_view_form', 1, 3, 4),
    'access callback' => 'klicktipp_stc_splittest_access_callback',
    'access arguments' => array(
      1, 3, '2',
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/tools_website_splittest.inc',
    'weight' => 2,
  );

  //affix
  $items['marketingtools/%user/splittest/%/3'] = array(
    'title callback' => 'klicktipp_tools_website_splittest_title_callback',
    'title arguments' => array(1, 3, '3', t('View')),
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_splittest_view_form', 1, 3, 4),
    'access callback' => 'klicktipp_stc_splittest_access_callback',
    'access arguments' => array(
      1, 3, '3',
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/tools_website_splittest.inc',
    'weight' => 3,
  );

  //affix
  $items['marketingtools/%user/splittest/%/4'] = array(
    'title callback' => 'klicktipp_tools_website_splittest_title_callback',
    'title arguments' => array(1, 3, '4', t('View')),
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_splittest_view_form', 1, 3, 4),
    'access callback' => 'klicktipp_stc_splittest_access_callback',
    'access arguments' => array(
      1, 3, '4',
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/tools_website_splittest.inc',
    'weight' => 4,
  );

  //affix
  $items['marketingtools/%user/splittest/%/5'] = array(
    'title callback' => 'klicktipp_tools_website_splittest_title_callback',
    'title arguments' => array(1, 3, '5', t('View')),
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_splittest_view_form', 1, 3, 4),
    'access callback' => 'klicktipp_stc_splittest_access_callback',
    'access arguments' => array(
      1, 3, '5',
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/tools_website_splittest.inc',
    'weight' => 5,
  );

  //affix
  $items['marketingtools/%user/splittest/%/edit'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_splittest_edit_form', 1, 3),
    'access callback' => 'klicktipp_stc_upsell_access',
    'access arguments' => array(
      1,
      'access splittest-club',
      'klicktipp_content_include_splittest_upsell',
      (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 6,
    'file' => 'forms/tools_website_splittest.inc',
  );

  //affix
  $items['marketingtools/%user/splittest/%/embed'] = array(
    'title' => 'Embed code',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_website_splittest_embed_form', 1, 3),
    'access callback' => 'klicktipp_website_splittest_embed_menu_access',
    'access arguments' => array(1,3),
    'type' => MENU_LOCAL_TASK,
    'weight' => 7,
    'file' => 'forms/tools_website_splittest.inc',
  );

  // download index.php for splittest
  $items['marketingtools/%user/splittest/%/indexphp'] = array(
    'page callback' => 'klicktipp_tools_website_splittest_indexphp',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_stc_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_WEBSITE_BUILDER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/tools_website_splittest.inc',
  );

  // "old" callbacks (redirects from splittest-club)
  $items['stc-countdown'] = array(
    'page callback' => 'klicktipp_tools_countdown_actionold',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/splittestclub.inc',
  );
  $items['stc-exitlightbox'] = array(
    'page callback' => 'klicktipp_tools_exitlightbox_actionold',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/splittestclub.inc',
  );
  $items['stc-feedback'] = array(
    'page callback' => 'klicktipp_tools_feedback_actionold',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/splittestclub.inc',
  );
  $items['stc-socialproof'] = array(
    'page callback' => 'klicktipp_tools_socialproof_actionold',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/splittestclub.inc',
  );
  $items['stc-splittest'] = array(
    'page callback' => 'klicktipp_tools_website_splittest_actionold',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/splittestclub.inc',
  );

  //plugins

  //create /plugins/%user/<pluginid>/add
  $items['plugins/%user/%klicktipp_plugin/add'] = array(
    'title' => 'Create Plugin',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_plugin_create_form', 1, 2),
    'access callback' => 'klicktipp_plugin_access',
    'access arguments' => array(1, 2, TRUE),
    'type' => MENU_CALLBACK,
    'file' => 'forms/plugins.inc',
  );

  //edit /plugins/%user/<toolid>/edit
  $items['plugins/%user/%/edit'] = array(
    'title' => 'Edit Plugin',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_plugin_edit_form', 1, 2),
    'access callback' => 'klicktipp_plugin_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/plugins.inc',
  );

  //inbound create /plugins/<pluginid>/connect
  $items['plugins/%/connect'] = array(
    'title' => 'Connect Plugin',
    'page callback' => 'klicktipp_plugin_connect',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/plugins.inc',
  );

  //inbound create /r/<codedparams> for the referrals plugin
  $items['r/%'] = array(
    'page callback' => ToolPluginGeneralPublicAPI::class . '::handleRedirect',
    'page arguments' => array(1),
    'access callback' => 'services_access_menu',
    'type' => MENU_CALLBACK
  );

  /**
   * Klick-Tipp menu callbacks with first level "campaigns", "autoresponders" or "emails"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_campaign
   */

// newsletter
  $items['campaigns/%user'] = array(
    'title' => 'Campaigns',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/newsletter/me'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'callbacks/callbacks.inc',
  );

  // min/max receiver calculation ajax callback
  // .../calculate-receiver-number/%CampaignID/%minReceivers/%maxReceivers/%Offset/get
  $items['newsletter/%user/calculate-receiver-number/%/%/%/%/get'] = array(
    'title' => 'Klick-Tipp Receiver Calculator',
    'page callback' => '_klicktipp_refresh_subscriber_count_ajax_callback',
    'page arguments' => array(1, 3, 4, 5, 6),
    'access arguments' => array('access content'),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/campaigns.inc',
  );

  // autoresponders
  $items['autoresponders/%user'] = array(
    'title' => 'Auto Responders',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/autoresponder/me'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

// autoresponder overview by StartTag
  $items['autoresponders/%user/%'] = array(
    'title' => 'Auto Responders',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_autoresponders_overview_form', 1, 2),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/autoresponders.inc',
  );

  // --- automation email, sms, notification email, notification sms ---

  $items['emails/%user'] = array(
    'title' => 'Notifications',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'email/overview/me'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'callbacks/callbacks.inc',
  );

  //email overview pager callback since emails/me/2/10 (page 2) calls 'emails/%user/%'
  $items['emails/%user/p'] = array(
    'title' => 'Notifications',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_emails_overview_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/email.inc',
  );

  $items['emails/%user/email/add'] = array(
    'title' => 'Create email',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'email/automation-email/me/create'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['emails/%user/email/%'] = array(
    'title' => 'View',
    'page callback' => 'drupal_get_form',
    // form_id, $account, $mode, $entity_id, $email_id
    'page arguments' => array('klicktipp_email_view_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/email.inc',
    'weight' => -10,
  );

  $items['emails/%user/email/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );

  // Redirect from Drupal to Angular - Do not remove!
  $items['emails/%user/%'] = array(
    'title' => 'Notifications',
    'page callback' => 'klicktipp_emails_angular_redirect',
    'page arguments' => array('klicktipp_email_edit_form', 1, 1, 2),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'file' => 'forms/email.inc',
  );

  $items['emails/%user/email/%/edit'] = array(
    'title' => 'E-Mail',
    'page callback' => 'drupal_get_form',
    // form_id, $account, $mode, $entity_id, $email_id
    'page arguments' => array('klicktipp_email_edit_form', 1, 'automation email', '0', 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/email.inc',
    'weight' => -9,
  );

  $items['emails/%user/email/%/editor'] = array(
    'title' => 'E-Mail Editor',
    'page callback' => 'klicktipp_menu_email_editor',
    'page arguments' => array('email', 1, 2, 3),
    'access callback' => 'klicktipp_menu_access_email_editor',
    'access arguments' => array('email', 1, 2, 3),
    'type' => MENU_LOCAL_TASK,
    'weight' => -8,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['emails/%user/sms/add'] = array(
    'title' => 'Create SMS',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'email/automation-sms/me/create'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['emails/%user/sms/%'] = array(
    'title' => 'View',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_email_view_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/email.inc',
  );

  $items['emails/%user/sms/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );

  $items['emails/%user/sms/%/edit'] = array(
    'title' => 'SMS',
    'page callback' => 'drupal_get_form',
    // form_id, $account, $mode, $entity_id, $email_id
    'page arguments' => array('klicktipp_email_edit_form', 1, 'automation sms', '0', 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/email.inc',
    'weight' => -9,
  );

  $items['emails/%user/notifyemail/add'] = array(
    'title' => 'Create email notification',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'email/notification-email/me/create'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['emails/%user/notifyemail/%'] = array(
    'title' => 'View',
    'page callback' => 'drupal_get_form',
    // form_id, $account, $mode, $entity_id, $email_id
    'page arguments' => array('klicktipp_email_view_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/email.inc',
    'weight' => -10,
  );

  $items['emails/%user/notifyemail/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );

  $items['emails/%user/notifyemail/%/edit'] = array(
    'title' => 'E-Mail',
    'page callback' => 'drupal_get_form',
    // form_id, $account, $mode, $entity_id, $email_id
    'page arguments' => array('klicktipp_email_edit_form', 1, 'notification email', '0', 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/email.inc',
    'weight' => -9,
  );

  $items['emails/%user/notifyemail/%/editor'] = array(
    'title' => 'E-Mail Editor',
    'page callback' => 'klicktipp_menu_email_editor',
    'page arguments' => array('email', 1, 2, 3),
    'access callback' => 'klicktipp_menu_access_email_editor',
    'access arguments' => array('email', 1, 2, 3),
    'type' => MENU_LOCAL_TASK,
    'weight' => -8,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['emails/%user/notifysms/add'] = array(
    'title' => 'Create SMS notification',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'email/automation-sms/me/create'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['emails/%user/notifysms/%'] = array(
    'title' => 'View',
    'page callback' => 'drupal_get_form',
    // form_id, $account, $mode, $entity_id, $email_id
    'page arguments' => array('klicktipp_email_view_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/email.inc',
    'weight' => -10,
  );

  $items['emails/%user/notifysms/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );

  $items['emails/%user/notifysms/%/edit'] = array(
    'title' => 'SMS',
    'page callback' => 'drupal_get_form',
    // form_id, $account, $mode, $entity_id, $email_id
    'page arguments' => array('klicktipp_email_edit_form', 1, 'notification sms', '0', 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/email.inc',
  );

  // --- splittest email ---

  $items['emails/%user/splittest/add/%'] = array(
    'title' => 'Create splittest email',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_email_create_form', 1, 2, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/email.inc',
  );

  $items['emails/%user/splittest-sms/add/%'] = array(
    'title' => 'Create splittest sms',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_email_create_form', 1, 2, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/email.inc',
  );

  // --- single email ---

  $items['emails/%user/add'] = array(
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/newsletter-email/me/create'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );
  $items['emails/%user/addar'] = array(
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/autoresponder-email/me/create'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );
  $items['emails/%user/addbd'] = array(
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/autoresponder-email-birthday/me/create'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );
  $items['emails/%user/addsms'] = array(
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/newsletter-sms/me/create'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );
  $items['emails/%user/addsmsar'] = array(
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/autoresponder-sms/me/create'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );
  $items['emails/%user/addsmsbd'] = array(
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/autoresponder-sms-birthday/me/create'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  // ####### Campaigns Drupal Links for Review - START
  // Only temporary for review on staging
  // only accessible via direkt urls in the browser

  $items['campaigns-drupal/%user'] = array(
    'title' => 'Campaigns',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_campaign_overview_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/campaign.inc',
  );

  $items['autoresponders-drupal/%user'] = array(
    'title' => 'Auto Responders',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_autoresponders_overview_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/autoresponders.inc',
  );

  $items['emails-drupal/%user/add'] = array(
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_campaign_form', 1, 2, KLICKTIPP_WIZARD_CREATE_EMAIL),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );
  $items['emails-drupal/%user/addar'] = array(
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_campaign_form', 1, 2, KLICKTIPP_WIZARD_CREATE_EMAIL),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );
  $items['emails-drupal/%user/addbd'] = array(
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_campaign_form', 1, 2, KLICKTIPP_WIZARD_CREATE_EMAIL),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );
  $items['emails-drupal/%user/addsms'] = array(
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_campaign_form', 1, 2, KLICKTIPP_WIZARD_CREATE_EMAIL),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );
  $items['emails-drupal/%user/addsmsar'] = array(
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_campaign_form', 1, 2, KLICKTIPP_WIZARD_CREATE_EMAIL),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );
  $items['emails-drupal/%user/addsmsbd'] = array(
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_campaign_form', 1, 2, KLICKTIPP_WIZARD_CREATE_EMAIL),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access sms marketing',
      'klicktipp_content_include_sms_upsell',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );

  $items['emails-drupal/%user/%'] = array(
    'page callback' => 'klicktipp_campaign_view',
    'page arguments' => array(1, 2),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/campaign.inc',
  );
  $items['emails-drupal/%user/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );
  $items['emails-drupal/%user/%/edit'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_campaign_form', 1, 2),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_LOCAL_TASK,
    'weight' => 1,
    'file' => 'forms/campaign.inc',
  );

  $items['emails-drupal/%user/%/senddate'] = array(
    'title' => 'Schedule',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_campaign_senddate', 1, 2),
    'access callback' => 'klicktipp_campaign_senddate_menu_access',
    'access arguments' => array(1, 2),
    'type' => MENU_LOCAL_TASK,
    'weight' => 5,
    'file' => 'forms/campaign.inc',
  );

  $items['automations-drupal/%user'] = array(
    'title' => 'Campaigns',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_automations_overview_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/automation.inc',
  );

  $items['automations-drupal/%user/add'] = array(
    'title' => 'Create automation',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_automation_create_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/automation.inc',
  );

  // ####### Campaigns Drupal Links for Review - END

  $items['emails/%user/%/campaign/%'] = array(
    //'title callback' => 'klicktipp_menu_splittest_email_title',
    //'title arguments' => array(1, 4),
    'page callback' => 'drupal_get_form',
    // form_id, $account, $mode, $entity_id, $email_id
    'page arguments' => array('klicktipp_email_edit_form', 1, 'campaign', 4, 2),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/email.inc',
  );

  $items['emails/%user/%/campaign/%/view'] = array(
    'title' => 'View',
    'page callback' => 'klicktipp_email_redirect_affix_to_angular',
    'page arguments' => array(1, 4, 'view'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_LOCAL_TASK,
    'weight' => 1,
    'file' => 'forms/email.inc',
  );

  $items['emails/%user/%/campaign/%/edit'] = array(
    'title' => 'Settings',
    'page callback' => 'klicktipp_email_redirect_affix_to_angular',
    'page arguments' => array(1, 4, 'settings'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_LOCAL_TASK,
    'weight' => 2,
    'file' => 'forms/email.inc',
  );

  $items['emails/%user/%/campaign/%/email'] = array(
    'title callback' => 'klicktipp_menu_splittest_email_title',
    'title arguments' => array(1, 4),
    'page callback' => 'klicktipp_email_redirect_affix_to_angular',
    'page arguments' => array(1, 4, 'email'),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'weight' => 3,
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/email.inc',
  );

  $items['emails/%user/%/campaign/%/editor'] = array(
    'title' => 'E-Mail Editor',
    'page callback' => 'klicktipp_menu_email_editor',
    'page arguments' => array('splittest', 1, 4, 'campaign', 2),
    'access callback' => 'klicktipp_menu_access_email_editor',
    //$type, $account, $mode, $ambiguous = '', $CampaignEmailID = 0
    'access arguments' => array('campaign-email', 1, 4, 'campaign', 2),
    'type' => MENU_LOCAL_TASK,
    'weight' => 5,
  );

  $items['emails/%user/%/campaign/%/schedule'] = array(
    'title' => 'Schedule',
    'page callback' => 'klicktipp_email_redirect_affix_to_angular',
    'page arguments' => array(1, 4, 'schedule'),
    'access callback' => 'klicktipp_campaign_senddate_menu_access',
    'access arguments' => array(1, 4),
    'type' => MENU_LOCAL_TASK,
    'weight' => 5,
    'file' => 'forms/email.inc',
  );

// campaign callbacks
  $items['emails/%user/%/cancelsending'] = array(
    'page callback' => 'klicktipp_campaign_cancelsending',
    'page arguments' => array(1, 2),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(
      1,
      (string)Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );
  $items['emails/%user/%/duplicatetestemail/%/%'] = array(
    'page callback' => 'klicktipp_campaign_duplicatetestemail',
    'page arguments' => array(1, 2, 4, 5),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );
  $items['emails/%user/%/removetestemail/%/%'] = array(
    'page callback' => 'klicktipp_campaign_removetestemail',
    'page arguments' => array(1, 2, 4, 5),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );
  $items['emails/%user/search/%/%'] = array(
    'page callback' => 'klicktipp_campaign_search_subscribers',
    'page arguments' => array(1, 3, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/campaign.inc',
  );

  /**
   * Klick-Tipp menu callbacks with first level "email"
   * @see: klicktipp.module -> klicktipp_menu()
   * @see: klicktipp_email and klicktipp_campaign
   */

  $items['email/%user/edit/%/%'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_email_edit_form', 1, 3, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/email.inc',
  );

  $items['email/%user/preview/%/%/%'] = array(
    'title' => 'HTML',
    'page callback' => 'klicktipp_email_preview',
    'page arguments' => array(1, 3, 4, 5),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_BASICS),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/email.inc',
  );

  $items['email/%user/copy/%/%'] = array(
    'title' => 'HTML',
    'page callback' => 'klicktipp_email_copy_ajax',
    'page arguments' => array(1, 3, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/email.inc',
  );

  $items['email/%user/content/%'] = array(
    'title' => 'HTML',
    'page callback' => 'klicktipp_get_email_content_ajax',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'forms/email.inc',
  );

  $items['email/%user/redirect/%/%'] = array(
    'title' => 'HTML',
    'page callback' => 'klicktipp_redirect_by_email_id',
    'page arguments' => array(1, 3, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_TEXTER),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  // --- automations ---

  $items['automations/%user'] = array(
    'title' => 'Campaigns',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/automation/me'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['automations/%user/add'] = array(
    'title' => 'Create automation',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'campaign/automation/me/create-automation'),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['automations/%user/edit/%'] = array(
    'title' => 'Edit automation',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_automation_view_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/automation.inc',
  );

  //affix
  $items['automations/%user/edit/%/view'] = array(
    'title' => 'View',
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER
    ),
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => 1,
  );

  //affix
  $items['automations/%user/edit/%/edit'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_automation_edit_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 2,
    'file' => 'forms/automation.inc',
  );

  //affix
  $items['automations/%user/edit/%/cockpit'] = array(
    'title' => 'Marketing Cockpit',
    'page callback' => 'klicktipp_automation_marketing_cockpit',
    'page arguments' => array(3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      '',
      (string)Subaccount::SUBACCOUNT_EMAIL_MARKETING_MANAGER
    ),
    'type' => MENU_LOCAL_TASK,
    'weight' => 3,
    'file' => 'forms/automation.inc',
  );

  //affix
  $items['automations/%user/edit/%/senddate'] = array(
    'title' => 'Schedule',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_automation_senddate_form', 1, 3),
    'access callback' => 'klicktipp_campaign_senddate_menu_access', //will check for SUBACCOUNT_EMAIL_MARKETING_MANAGER
    'access arguments' => array(1, 3),
    'type' => MENU_LOCAL_TASK,
    'weight' => 4,
    'file' => 'forms/automation.inc',
  );

  // overview of BAM campaign templates to import
  $items['automations/%user/bam'] = array(
    'title' => 'Overview BAM Automation Templates',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tools_template_create_bam_overview_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access campaign builder',
      'no_upsell_for_subaccounts_yet',
      (string)Subaccount::SUBACCOUNT_TEXTER
    ),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/tools_bam_templates.inc',
    'weight' => 5,
  );

  // min/max receiver calculation ajax callback
  // .../calculate-receiver-number/%CampaignID/%minReceivers/%maxReceivers/%Offset/get
  $items['automations/%user/calculate-receiver-number/%/%/%/%/get'] = array(
    'title' => 'Klick-Tipp Receiver Calculator',
    'page callback' => '_klicktipp_refresh_subscriber_count_ajax_callback',
    'page arguments' => array(1, 3, 4, 5, 6),
    'access arguments' => array('access content'),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/campaigns.inc',
  );

  $items['entity/add/%'] = array(
    'title' => 'Create',
    'page callback' => 'klicktipp_redirect_by_entityclass',
    'page arguments' => array(2),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['entity/ofsmarttag/%'] = array(
    'title' => 'Edit',
    'page callback' => 'klicktipp_redirect_by_smarttag',
    'page arguments' => array(2),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['entity/edit/%/%'] = array(
    'title' => 'Edit',
    'page callback' => 'klicktipp_redirect_by_entity',
    'page arguments' => array(2,3),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['entity/search/%/%'] = array(
    'page callback' => 'klicktipp_redirect_entity_search',
    'page arguments' => array(2,3),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  /*
   * additional sender
   */
  $items['user/%user/sender'] = array(
    'title' => 'Additional Sender Email Addresses',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_sender_addresses_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_ADMINISTRATOR),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_sender_addresses.inc',
  );

  $items['user/%user/senderdelete/%'] = array(
    'title' => 'Delete sender address',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_sender_email_delete_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_sender_addresses.inc',
  );


  $items['user/senderverify/%'] = array(
    'title' => 'E-mail addresss verification',
    'page callback' => 'klicktipp_account_additional_email_verification',
    'page arguments' => array(2),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_sender_addresses.inc',
  );


  $items['user/%user/smssenderdelete/%'] = array(
    'title' => 'Delete sender phone number',
    'page callback' => 'klicktipp_account_sender_phonenumber_delete',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account.inc',
  );

  /*
   * whitelabel domains
   */
  $items['user/%user/domains'] = array(
    'title' => 'Domains',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_domains_settings_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_domains.inc',
  );

  $items['domain/%/%'] = array(
    'title' => 'Domains',
    'page callback' => 'klicktipp_domain_readonly_form',
    'page arguments' => array(1, 2),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_domains.inc',
  );

  $items['user/%user/domainconfigure/%'] = array(
    'title' => 'Configure whitelabel domain',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_whitelabel_domain_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );

  $items['user/%user/domaindelete/%'] = array(
    'title' => 'Delete whitelabel domain',
    'page callback' => 'klicktipp_account_whitelabel_domain_delete',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );

  $items['user/%user/mailserverdelete/%'] = array(
    'title' => 'Delete whitelabel domain',
    'page callback' => 'klicktipp_account_whitelabel_mailserver_delete',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );

  $items['user/%user/domainverify'] = array(
    'title' => 'Block whitelabel domain',
    'page callback' => 'klicktipp_account_whitelabel_domain_verify',
    'page arguments' => array(1, 3),
    'access arguments' => array('administer klicktipp'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );
  $items['user/%user/domaindefault/%'] = array(
    'title' => 'Set as default',
    'page callback' => 'klicktipp_account_whitelabel_domain_default',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );

  $items['user/%user/domainblacklistcheck/%'] = array(
    'title' => 'Set as default',
    'page callback' => 'klicktipp_account_whitelabel_check_domain_against_blacklists',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );

  $items['user/%user/request-mailserver-deletion/%'] = array(
    'title' => 'Set as default',
    'page callback' => 'klicktipp_account_whitelabel_request_mailserver_deletion',
    'page arguments' => array(1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );

  $items['user/%user/hostconfigure/%'] = array(
    'title' => 'Configure whitelabel domain host',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_whitelabel_host_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );
  $items['user/%user/hostdelete/%/%'] = array(
    'title' => 'Delete whitelabel domain host',
    'page callback' => 'klicktipp_account_whitelabel_host_delete',
    'page arguments' => array(1, 3, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );
  $items['user/%user/hostconfirmation/%/%'] = array(
    'title' => 'Set whitelabel domain host as host for confirmation emails',
    'page callback' => 'klicktipp_account_whitelabel_host_confirmation',
    'page arguments' => array(1, 3, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );
  $items['user/%user/hostsubscriberarea/%/%'] = array(
    'title' => 'Set whitelabel domain host as host for subscriber area',
    'page callback' => 'klicktipp_account_whitelabel_host_subscriberarea',
    'page arguments' => array(1, 3, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_whitelabel.inc',
  );

  // digistore24 invoice callback
  // overview for the user of his current KlickTipp subscription
  // only accessible with a DigiStore24 subscription
  $items['user/%user/digistore-invoice'] = array(
    'title' => 'My Digistore Invoices',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, 'cancellation/me'),
    'access callback' => 'klicktipp_user_digistore_invoice_menu_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

    $items['service-center/%user'] = array(
        'title' => 'Account Security',
        'page callback' => 'klicktipp_redirect_account_security',
        'access callback' => 'klicktipp_menu_subaccount_only_access',
        'access arguments' => array(1),
        'type' => MENU_NORMAL_ITEM
    );

  /*
   * sales employee
   */

  $items['sales-employees/%user'] = array(
    'title' => 'Sales Employees',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_user_variables_sales_employee_overview_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access bcr event',
      'no_upsell_for_subaccounts_yet'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/user_variables_sales_employee.inc',
  );

  $items['sales-employees/%user/add'] = array(
    'title' => 'Create Sales Employee',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_user_variables_sales_employee_create_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access bcr event',
      'no_upsell_for_subaccounts_yet'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/user_variables_sales_employee.inc',
  );

  $items['sales-employees/%user/edit/%'] = array(
    'title' => 'Edit Subaccount',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_user_variables_sales_employee_edit_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'access bcr event',
      'no_upsell_for_subaccounts_yet'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/user_variables_sales_employee.inc',
  );

  /*
   * subaccount
   */
  // subaccount: create
  $items['user/%user/subaccount/add'] = array(
    'title' => 'Create Subaccount',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subaccount_create_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'use subaccounts',
      'no_upsell_for_subaccounts_yet'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/subaccount.inc',
  );

  // subaccount: add agency access
  $items['user/%user/subaccount/addagency'] = array(
    'title' => 'Add Agency Access',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subaccount_create_agency_access_form', 1),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'use agency access',
      'no_upsell_for_subaccounts_yet'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/subaccount.inc',
  );

  // subaccount: edit
  $items['user/%user/subaccount/%/edit'] = array(
    'title' => 'Edit Subaccount',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subaccount_edit_form', 1, 3),
    'access callback' => 'klicktipp_upsell_access',
    'access arguments' => array(
      1,
      'use subaccounts',
      'no_upsell_for_subaccounts_yet'
    ),
    'type' => MENU_CALLBACK,
    'file' => 'forms/subaccount.inc',
  );

  // subaccount: view
  $items['user/%user/subaccount/%/view'] = array(
    'title' => 'View Subaccount',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subaccount_view_form', 1, 3),
    'access callback' => 'klicktipp_subaccount_view_access',
    'access arguments' => array(1, 3),
    'type' => MENU_CALLBACK,
    'file' => 'forms/subaccount.inc',
  );

  // subaccount: switch
  $items['user/%/subaccount/switch'] = array(
    'title' => 'Switch Subaccount',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subaccount_switch_form'),
    'access callback' => 'klicktipp_subaccount_switch_access',
    'type' => MENU_CALLBACK,
    'file' => 'forms/subaccount.inc',
  );

  // subaccount: switch
  $items['user/%/subaccount/%/switch'] = array(
    'title' => 'Switch Subaccount',
    'page callback' => 'klicktipp_subaccount_switch_subaccount',
    'page arguments' => array(3),
    'access callback' => 'klicktipp_subaccount_switch_access',
    'type' => MENU_CALLBACK,
    'file' => 'forms/subaccount.inc',
  );

  // grant partner access to api
  $items['grantapiaccess/%'] = array(
    'title' => 'Grant partner api access',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_subaccount_grantpartneraccess_form', 1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/subaccount.inc',
  );

  // edit own user account
  $items['user/%/account'] = array(
    'title' => 'Edit',
    'page callback' => 'klicktipp_goto_user_edit',
    'page arguments' => array(1),
    'access callback' => 'user_is_logged_in',
    'type' => MENU_CALLBACK,
  );

  // clear klicktipp cache (access for all authenticated: needed for subaccounts)
  $items['user/clearcache'] = array(
    'title' => 'Clear Cache',
    'page callback' => UserCache::class . '::clearCurrentUserCache',
    'access callback' => 'user_is_logged_in',
    'type' => MENU_CALLBACK,
  );

// Klick-Tipp data processing order confirmation

  $items['user/%user/data_processing_order'] = array(
    'title' => 'Data Processing Order',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_dpo_form', 1),
    'access callback' => 'klicktipp_account_data_processing_order_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_dpo.inc',
  );

  // Klick-Tipp privacy dashboard
  $items['user/%user/privacy'] = array(
    'title' => 'Dashboard',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_privacy_dashboard_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_privacy.inc',
  );

  $items['user/%user/privacy/dashboard'] = array(
    'title' => 'Dashboard',
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -1,
  );

  $items['user/%user/privacy/settings'] = array(
    'title' => 'Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_privacy_settings_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/account_privacy.inc',
    'weight' => 0,
  );

  $items['user/%user/privacy/email'] = array(
    'title' => 'Email Template',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_privacy_email_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/account_privacy.inc',
    'weight' => 1,
  );

  //send information email
  $items['user/%user/privacy/%/send'] = array(
    'title' => 'Send Information Email',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_privacy_send_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_privacy.inc',
  );

  //update subscriber data
  $items['user/%user/privacy/%/update'] = array(
    'title' => 'Update Subscriber Data',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_privacy_update_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_privacy.inc',
  );

  //confirm delete
  $items['user/%user/privacy/%/delete'] = array(
    'title' => 'Confirm Delete',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_privacy_delete_form', 1, 3),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_privacy.inc',
  );

  //delete from facebook audiences errors
  $items['user/%user/privacy/facebook-audiences'] = array(
    'title' => 'Delete from Facebook Audiences',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_privacy_facebook_audience_errors_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_privacy.inc',
  );

  //delete from outbound errors
  $items['user/%user/privacy/outbounds'] = array(
    'title' => 'Outbound Failures',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_privacy_outbound_errors_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_CUSTOMER_SUPPORT),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_privacy.inc',
  );

// Klick-Tipp data processing order TODO: ??? remove
  $items['user/%user/ajax/%/%'] = array(
    'title' => 'AJAX Callback',
    'page callback' => 'klicktipp_account_process_ajax',
    'page arguments' => array(1, 3, 4),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account.inc',
  );

  $items['user/%user/personal-information'] = array(
    'title' => 'Personal information',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_personal_information_form', 1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/account_personal_information.inc',
  );

  // Klick-Tipp Consultant settings
  $items['user/%user/consultant'] = array(
    'title' => 'Consultant settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_consultant_settings_form', 1),
    // grant access to klicktipp and affiliate accounts
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_ADMINISTRATOR, FALSE),
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_consultant.inc',
  );

  // Processflow preferences
  $items['user/%user/marketingcockpit'] = array(
    'title' => 'Marketing Cockpit Preferences',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_processflow_preferences_form', 1),
    // grant access to klicktipp and affiliate accounts
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'forms/processflow.inc',
  );

  $items['login/%user/change-password'] = array(
    'title' => 'Change Password',
    'page callback' => 'klicktipp_account_password_change',
    'access callback' => 'user_is_logged_in',
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_registration.inc',
  );

  //Reset password
  $items['login/reset-password'] = array(
    'title' => 'Reset Password',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_wizard_form', 1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_registration.inc',
  );

  // First Login Wizard
  $items['login/wizard'] = array(
    'title' => 'First Login Wizard',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_wizard_form'),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/account_registration.inc',
  );

  //First login settings
  $items['admin/config/klicktipp/marketing/account-login'] = array(
    'title' => 'Login Settings',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_account_login_settings_form'),
    'access arguments' => array('administer site configuration'),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/admin_settings_registration.inc',
  );

  //Staging to Lacal Sync (payment addon)
  $items['admin/config/klicktipp/staging-synch'] = array(
    'title' => 'Staging Synch for Local',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_settings_staging_sync_form'),
    'access callback' => 'klicktipp_staging_sync_menu_access',
    'access arguments' => array(),
    'type' => MENU_LOCAL_TASK,
    'file' => 'forms/admin_settings_staging_sync.inc',
  );

  $items['consultants'] = array(
    'title' => 'Klick-Tipp Consultants',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_consultant_marketplace_form'),
    'access callback' => TRUE,
    'file' => 'forms/account_consultant.inc',
  );

  $items['consultants/%'] = array(
    'title' => 'Klick-Tipp Consultant',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_consultant_form', 1),
    'access callback' => TRUE,
    'file' => 'forms/account_consultant.inc',
  );

  $items['consultants/review/%'] = array(
    'title' => 'Klick-Tipp Consultant Review',
    'page callback' => 'klicktipp_consultant_marketplace_review',
    'page arguments' => array(2),
    'access arguments' => array('administer klicktipp'),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/subscriber.inc',
  );

  // From the module m4032404
  $items['kt4032404'] = array(
    'title' => '403 to 404 callback',
    'page callback' => 'klicktipp_4032404_callback',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  // From the module m4032404
  $items['kthttptest'] = array(
    'title' => 'test callback',
    'page callback' => 'klicktipp_httptest_callback',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  // Helpscout Webhook
  $items['helpscout'] = array(
    'title' => 'Handle HelpScout event',
    'page callback' => 'klicktipp_helpscout_ticket_wrapper',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/helpscout.inc',
  );

  //HelpScout ticket feedback
  $items['ticketfeedback/%'] = array(
    'title' => 'Klick-Tipp Feedback',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_npscore_form', 1),
    'access callback' => TRUE,
    'file' => 'forms/npscore.inc',
  );

  $items['npscore'] = array(
    'title' => 'NPS Dashboard',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_npscore_overview_form'),
    'access arguments' => array('administer klicktipp'),
    'file' => 'forms/npscore.inc',
  );

  $items['helplog'] = array(
    'title' => 'Helplog',
    'page callback' => 'klicktipp_help_log',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/helpscout.inc',
  );

  $items['edithelppath/%'] = array(
    'title' => 'Edit help path',
    'page callback' => 'klicktipp_help_edit_path',
    'page arguments' => array(1),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/helpscout.inc',
  );

  $items['quickhelp_retrieve'] = array(
    'title' => 'Quickhelp index',
    'page callback' => 'klicktipp_angular_quickhelp_retrieve',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/quickhelp.inc',
  );

  $items['quickhelp_update'] = array(
    'title' => 'Quickhelp update',
    'page callback' => 'klicktipp_angular_quickhelp_update',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/quickhelp.inc',
  );

  $items['redirect'] = array(
    'page callback' => 'klicktipp_safe_redirect',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/redirect.inc',
  );

  // Translations

  $items['admin/config/klicktipp/translation/deprecated'] = array(
    'title' => 'Translations (deprecated)',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_translation_settings_form'),
    'access arguments' => array('administer klicktipp'),
    'type' => MENU_NORMAL_ITEM,
    'file' => 'forms/translation.inc',
  );

  //javascript callback to return current ledger
  $items['translations/ledger'] = array(
    'title' => 'Translations',
    'page callback' => 'klicktipp_translation_ledger_get_js',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/translation.inc',
  );

  //javascript callback to update a translation
  $items['translations/update'] = array(
    'title' => 'Translations',
    'page callback' => 'klicktipp_translation_ledger_update',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/translation.inc',
  );

  //javascript callback to retrieve the ledger
  $items['translations/retrieve'] = array(
    'title' => 'Translations',
    'page callback' => 'klicktipp_translation_ledger_retrieve',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/translation.inc',
  );

  //javascript callback to clear the ledger
  $items['translations/clear'] = array(
    'title' => 'Translations',
    'page callback' => 'klicktipp_translation_ledger_clear',
    'page arguments' => array(),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'forms/translation.inc',
  );

  //javascript callback to publish translations
  $items['translations/publish'] = array(
    'title' => 'Translations',
    'page callback' => 'klicktipp_translation_publish',
    'page arguments' => array(),
    'access arguments' => array('access translation admin'),
    'type' => MENU_CALLBACK,
    'file' => 'forms/translation.inc',
  );

  //redirects from drupal menu to cockpit
  $items['cockpit/%user'] = array(
    'title' => 'Cockpit',
    'page callback' => 'klicktipp_cockpit_redirect',
    'page arguments' => array(),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  //redirects from drupal menu to angular app (new)
  $items['application/%user'] = array(
    'title' => 'Cockpit',
    'page callback' => 'klicktipp_application_redirect',
    'page arguments' => array(1, '*'),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  $items['angular/translation'] = array(
    'title' => 'Translations',
    'page callback' => 'klicktipp_callbacks_angular_get_translation',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/angular.inc',
  );

  $items['angular/google/%'] = array(
    'title' => 'Translations',
    'page callback' => 'klicktipp_callbacks_angular_google_scripts',
    'page arguments' => array(2),
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/angular.inc',
  );

  $items['angular/cockpit'] = array(
    'title' => 'Translations',
    'page callback' => 'klicktipp_callbacks_cockpit_header',
    'access callback' => TRUE,
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/angular.inc',
  );

  // --- TM4J help dialogs START ---

  // reset test accounts to test first login, pwd reset, etc...
  $items['tm4j/%user/account-login'] = array(
    'title' => 'TM4J Account Login',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tm4j_account_reset_form'),
    'access arguments' => array('access tm4j test pages'),
    'type' => MENU_CALLBACK,
    'file' => 'tm4j/account_registration.inc',
  );

  // list links to all available global email templates to check if sources exist on S3
  $items['tm4j/%user/email-templates'] = array(
    'title' => 'TM4J Email Templates',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tm4j_email_templates'),
    'access arguments' => array('access tm4j test pages'),
    'type' => MENU_CALLBACK,
    'file' => 'tm4j/email_templates.inc',
  );


  $items['tm4j/%user/subscription'] = array(
    'title' => 'TM4J Subscription Edit',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tm4j_subscription_form', 1),
    'access arguments' => array('access tm4j test pages'),
    'type' => MENU_CALLBACK,
    'file' => 'tm4j/subscription.inc',
  );

  $items['tm4j/%user/subscriber_key'] = array(
    'title' => 'TM4J Subscriber Key',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('klicktipp_tm4j_api_subscriber_key_form', 1),
    'access arguments' => array('access tm4j test pages'),
    'type' => MENU_CALLBACK,
    'file' => 'tm4j/api_subscriber_key.inc',
  );

  // --- TM4J help dialogs END ---

  // ajax: blacklisting a feature message in user variable
  // 'feature-messages/%userId%/%messageId%/blacklist'
  $items['feature-messages/%user/%/blacklist'] = array(
    'page callback' => '_klicktipp_user_variables_blacklist_feature_message',
    'page arguments' => array(1, 2),
    'access arguments' => array('access klicktipp', 1),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/user_variables_feature_messages.inc',
  );

  // --- dependency-check ---

  $items['dependency-check/%user/ajax'] = array(
    'title' => 'HTML',
    'page callback' => 'klicktipp_check_dependencies',
    'page arguments' => array(1),
    'access callback' => 'klicktipp_user_edit_access',
    'access arguments' => array(1, (string)Subaccount::SUBACCOUNT_BASICS),
    'type' => MENU_CALLBACK,
    'file' => 'callbacks/callbacks.inc',
  );

  return $items;

}

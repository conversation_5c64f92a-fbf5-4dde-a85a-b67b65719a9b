<?php

/**
 * @file
 * Automatd SimpleTest Case for using content access module with acl module
 */

require_once(drupal_get_path('module', 'content_access') .'/tests/content_access_test_help.php');

class ContentAccessACLTestCase extends ContentAccessTestCase {

  var $node;

  /**
   * Implementation of get_info() for information
   */
  public static function getInfo() {
    return array(
      'name' => t('Content Access Module with ACL Module Tests'),
      'description' => t('Various tests to check the combination of content access and ACL module.'),
      'group' => 'Content Access',
    );
  }

  /**
   * Setup configuration before each test
   */
  function setUp($module = '') {
    parent::setUp('acl');

    if (!module_exists('acl')) {
      $this->pass('No ACL module present, skipping test');
      return;
    }

    // Create test nodes
    $this->node = $this->drupalCreateNode(array('type' => $this->content_type->type));
  }

  /**
   * Test Viewing accessibility with permissions for single users
   */
  function testViewAccess() {
    // Exit test if ACL module could not be enabled
    if (!module_exists('acl')) {
      $this->pass('No ACL module present, skipping test');
      return;
    }

    // Restrict access to this content type (access is only allowed for the author)
    // Enable per node access control
    $access_permissions = array(
      'view[1]' => FALSE,
      'view[2]' => FALSE,
      'per_node' => TRUE,
    );
    $this->changeAccessContentType($access_permissions);

    // Allow access for test user
    $edit = array(
      'acl[view][add]' => $this->test_user->name,
    );
    $this->drupalPost('node/'. $this->node->nid .'/access', $edit, t('Add User'));
    $this->drupalPost(NULL, array(), t('Submit'));

    // Logout admin, try to access the node anonymously
    $this->drupalLogout();
    $this->drupalGet('node/'. $this->node->nid);
    $this->assertText(t('Access denied'), 'node is not viewable');

    // Login test user, view access should be allowed now
    $this->drupalLogin($this->test_user);
    $this->drupalGet('node/'. $this->node->nid);
    $this->assertNoText(t('Access denied'), 'node is viewable');

    // Login admin and disable per node access
    $this->drupalLogin($this->admin_user);
    $this->changeAccessPerNode(FALSE);

    // Logout admin, try to access the node anonymously
    $this->drupalLogout();
    $this->drupalGet('node/'. $this->node->nid);
    $this->assertText(t('Access denied'), 'node is not viewable');

    // Login test user, view access should be denied now
    $this->drupalLogin($this->test_user);
    $this->drupalGet('node/'. $this->node->nid);
    $this->assertText(t('Access denied'), 'node is not viewable');
  }

  /**
   * Test Editing accessibility with permissions for single users
   */
  function testEditAccess() {
    // Exit test if ACL module could not be enabled
    if (!module_exists('acl')) {
      $this->pass('No ACL module present, skipping test');
      return;
    }

    // Enable per node access control
    $this->changeAccessPerNode();

    // Allow edit access for test user
    $edit = array(
      'acl[update][add]' => $this->test_user->name,
    );
    $this->drupalPost('node/'. $this->node->nid .'/access', $edit, t('Add User'));
    $this->drupalPost(NULL, array(), t('Submit'));

    // Logout admin, try to edit the node anonymously
    $this->drupalLogout();
    $this->drupalGet('node/'. $this->node->nid .'/edit');
    $this->assertText(t('Access denied'), 'node is not editable');

    // Login test user, edit access should be allowed now
    $this->drupalLogin($this->test_user);
    $this->drupalGet('node/'. $this->node->nid .'/edit');
    $this->assertNoText(t('Access denied'), 'node is editable');

    // Login admin and disable per node access
    $this->drupalLogin($this->admin_user);
    $this->changeAccessPerNode(FALSE);

    // Logout admin, try to edit the node anonymously
    $this->drupalLogout();
    $this->drupalGet('node/'. $this->node->nid .'/edit');
    $this->assertText(t('Access denied'), 'node is not editable');

    // Login test user, edit access should be denied now
    $this->drupalLogin($this->test_user);
    $this->drupalGet('node/'. $this->node->nid .'/edit');
    $this->assertText(t('Access denied'), 'node is not editable');
  }

  /**
   * Test Deleting accessibility with permissions for single users
   */
  function testDeleteAccess() {
    // Exit test if ACL module could not be enabled
    if (!module_exists('acl')) {
      $this->pass('No ACL module present, skipping test');
      return;
    }

    // Enable per node access control
    $this->changeAccessPerNode();

    // Allow delete access for test user
    $edit = array(
      'acl[delete][add]' => $this->test_user->name,
    );
    $this->drupalPost('node/'. $this->node->nid .'/access', $edit, t('Add User'));
    $this->drupalPost(NULL, array(), t('Submit'));

    // Logout admin, try to delete the node anonymously
    $this->drupalLogout();
    $this->drupalGet('node/'. $this->node->nid .'/delete');
    $this->assertText(t('Access denied'), 'node is not deletable');

    // Login test user, delete access should be allowed now
    $this->drupalLogin($this->test_user);
    $this->drupalGet('node/'. $this->node->nid .'/delete');
    $this->assertNoText(t('Access denied'), 'node is deletable');

    // Login admin and disable per node access
    $this->drupalLogin($this->admin_user);
    $this->changeAccessPerNode(FALSE);

    // Logout admin, try to delete the node anonymously
    $this->drupalLogout();
    $this->drupalGet('node/'. $this->node->nid .'/delete');
    $this->assertText(t('Access denied'), 'node is not deletable');

    // Login test user, delete access should be denied now
    $this->drupalLogin($this->test_user);
    $this->drupalGet('node/'. $this->node->nid .'/delete');
    $this->assertText(t('Access denied'), 'node is not deletable');
  }
}
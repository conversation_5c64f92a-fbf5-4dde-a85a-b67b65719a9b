/*----------------------------------------\
|      Cross Browser Tree Widget 1.1      |
|-----------------------------------------|
| Created by <PERSON> (<EMAIL>) |
|    For WebFX (http://webfx.eae.net/)    |
|-----------------------------------------|
| This script is  provided as is  without |
| any warranty whatsoever. It may be used |
| free of charge for non commerical sites |
| For commerical use contact  the  author |
| of this script for further details.     |
|-----------------------------------------|
| Created 2000-12-11 | Updated 2001-09-06 |
\----------------------------------------*/

var webFXTreeConfig = {
  rootIcon        : 'media/images/empty.png',
  openRootIcon    : 'media/images/empty.png',
  folderIcon      : 'media/images/empty.png',
  openFolderIcon  : 'media/images/empty.png',
  fileIcon        : 'media/images/empty.png',
  iIcon           : 'media/images/I.png',
  lIcon           : 'media/images/L.png',
  lMinusIcon      : 'media/images/Lminus.png',
  lPlusIcon       : 'media/images/Lplus.png',
  tIcon           : 'media/images/T.png',
  tMinusIcon      : 'media/images/Tminus.png',
  tPlusIcon       : 'media/images/Tplus.png',
  blankIcon       : 'media/images/blank.png',
  defaultText     : 'Tree Item',
  defaultAction   : 'javascript:void(0);',
  defaultTarget   : 'right',
  defaultBehavior : 'classic'
};

var webFXTreeHandler = {
  idCounter : 0,
  idPrefix  : "webfx-tree-object-",
  all       : {},
  behavior  : null,
  selected  : null,
  getId     : function() { return this.idPrefix + this.idCounter++; },
  toggle    : function (oItem) { this.all[oItem.id.replace('-plus','')].toggle(); },
  select    : function (oItem) { this.all[oItem.id.replace('-icon','')].select(); },
  focus     : function (oItem) { this.all[oItem.id.replace('-anchor','')].focus(); },
  blur      : function (oItem) { this.all[oItem.id.replace('-anchor','')].blur(); },
  keydown   : function (oItem) { return this.all[oItem.id].keydown(window.event.keyCode); },
  cookies   : new WebFXCookie()
};

/*
 * WebFXCookie class
 */

function WebFXCookie() {
  if (document.cookie.length) { this.cookies = ' ' + document.cookie; }
}

WebFXCookie.prototype.setCookie = function (key, value) {
  document.cookie = key + "=" + escape(value);
}

WebFXCookie.prototype.getCookie = function (key) {
  if (this.cookies) {
    var start = this.cookies.indexOf(' ' + key + '=');
    if (start == -1) { return null; }
    var end = this.cookies.indexOf(";", start);
    if (end == -1) { end = this.cookies.length; }
    end -= start;
    var cookie = this.cookies.substr(start,end);
    return unescape(cookie.substr(cookie.indexOf('=') + 1, cookie.length - cookie.indexOf('=') + 1));
  }
  else { return null; }
}

/*
 * WebFXTreeAbstractNode class
 */

function WebFXTreeAbstractNode(sText, sAction, sTarget) {
  this.childNodes  = [];
  this.id     = webFXTreeHandler.getId();
  this.text   = sText || webFXTreeConfig.defaultText;
  this.action = sAction || webFXTreeConfig.defaultAction;
  this.targetWindow = sTarget || webFXTreeConfig.defaultTarget;
  this._last  = false;
  webFXTreeHandler.all[this.id] = this;
}

WebFXTreeAbstractNode.prototype.add = function (node) {
  node.parentNode = this;
  this.childNodes[this.childNodes.length] = node;
  var root = this;
  if (this.childNodes.length >=2) {
    this.childNodes[this.childNodes.length -2]._last = false;
  }
  while (root.parentNode) { root = root.parentNode; }
  if (root.rendered) {
    if (this.childNodes.length >= 2) {
      document.getElementById(this.childNodes[this.childNodes.length -2].id + '-plus').src = ((this.childNodes[this.childNodes.length -2].folder)?webFXTreeConfig.tMinusIcon:webFXTreeConfig.tIcon);
      if (this.childNodes[this.childNodes.length -2].folder) {
        this.childNodes[this.childNodes.length -2].plusIcon = webFXTreeConfig.tPlusIcon;
        this.childNodes[this.childNodes.length -2].minusIcon = webFXTreeConfig.tMinusIcon;
      }
      this.childNodes[this.childNodes.length -2]._last = false;
    }
    this._last = true;
    var foo = this;
    while (foo.parentNode) {
      for (var i = 0; i < foo.parentNode.childNodes.length; i++) {
        if (foo.id == foo.parentNode.childNodes[i].id) { break; }
      }
      if (++i == foo.parentNode.childNodes.length) { foo.parentNode._last = true; }
      else { foo.parentNode._last = false; }
      foo = foo.parentNode;
    }
    document.getElementById(this.id + '-cont').insertAdjacentHTML("beforeEnd", node.toString());
    if ((!this.folder) && (!this.openIcon)) {
      this.icon = webFXTreeConfig.folderIcon;
      this.openIcon = webFXTreeConfig.openFolderIcon;
    }
    this.folder = true;
    this.indent();
    this.expand();
  }
  return node;
}

WebFXTreeAbstractNode.prototype.toggle = function() {
  if (this.folder) {
    if (this.open) { this.collapse(); }
    else { this.expand(); }
  }
}

WebFXTreeAbstractNode.prototype.select = function() {
  document.getElementById(this.id + '-anchor').focus();
}

WebFXTreeAbstractNode.prototype.focus = function() {
  webFXTreeHandler.selected = this;
  if ((this.openIcon) && (webFXTreeHandler.behavior != 'classic')) { document.getElementById(this.id + '-icon').src = this.openIcon; }
  document.getElementById(this.id + '-anchor').style.backgroundColor = 'highlight';
  document.getElementById(this.id + '-anchor').style.color = 'highlighttext';
  document.getElementById(this.id + '-anchor').focus();
}

WebFXTreeAbstractNode.prototype.blur = function() {
  if ((this.openIcon) && (webFXTreeHandler.behavior != 'classic')) { document.getElementById(this.id + '-icon').src = this.icon; }
  document.getElementById(this.id + '-anchor').style.backgroundColor = 'transparent';
  document.getElementById(this.id + '-anchor').style.color = 'menutext';
}

WebFXTreeAbstractNode.prototype.doExpand = function() {
  if (webFXTreeHandler.behavior == 'classic') { document.getElementById(this.id + '-icon').src = this.openIcon; }
  if (this.childNodes.length) {  document.getElementById(this.id + '-cont').style.display = 'block'; }
  this.open = true;
  webFXTreeHandler.cookies.setCookie(this.id.substr(18,this.id.length - 18), '1');
}

WebFXTreeAbstractNode.prototype.doCollapse = function() {
  if (webFXTreeHandler.behavior == 'classic') { document.getElementById(this.id + '-icon').src = this.icon; }
  if (this.childNodes.length) { document.getElementById(this.id + '-cont').style.display = 'none'; }
  this.open = false;
  webFXTreeHandler.cookies.setCookie(this.id.substr(18,this.id.length - 18), '0');
}

WebFXTreeAbstractNode.prototype.expandAll = function() {
  this.expandChildren();
  if ((this.folder) && (!this.open)) { this.expand(); }
}

WebFXTreeAbstractNode.prototype.expandChildren = function() {
  for (var i = 0; i < this.childNodes.length; i++) {
    this.childNodes[i].expandAll();
} }

WebFXTreeAbstractNode.prototype.collapseAll = function() {
  if ((this.folder) && (this.open)) { this.collapse(); }
  this.collapseChildren();
}

WebFXTreeAbstractNode.prototype.collapseChildren = function() {
  for (var i = 0; i < this.childNodes.length; i++) {
    this.childNodes[i].collapseAll();
} }

WebFXTreeAbstractNode.prototype.indent = function(lvl, del, last, level) {
  /*
   * Since we only want to modify items one level below ourself,
   * and since the rightmost indentation position is occupied by
   * the plus icon we set this to -2
   */
  if (lvl == null) { lvl = -2; }
  var state = 0;
  for (var i = this.childNodes.length - 1; i >= 0 ; i--) {
    state = this.childNodes[i].indent(lvl + 1, del, last, level);
    if (state) { return; }
  }
  if (del) {
    if (level >= this._level) {
      if (this.folder) {
        document.getElementById(this.id + '-plus').src = (this.open)?webFXTreeConfig.lMinusIcon:webFXTreeConfig.lPlusIcon;
        this.plusIcon = webFXTreeConfig.lPlusIcon;
        this.minusIcon = webFXTreeConfig.lMinusIcon;
      }
      else { document.getElementById(this.id + '-plus').src = webFXTreeConfig.lIcon; }
      return 1;
    }
  }
  var foo = document.getElementById(this.id + '-indent-' + lvl);
  if (foo) {
    if ((del) && (last)) { foo._last = true; }
    if (foo._last) { foo.src =  webFXTreeConfig.blankIcon; }
    else { foo.src =  webFXTreeConfig.iIcon; }
  }
  return 0;
}

/*
 * WebFXTree class
 */

function WebFXTree(sText, sAction, sBehavior, sIcon, sOpenIcon) {
  this.base = WebFXTreeAbstractNode;
  this.base(sText, sAction);
  this.icon      = sIcon || webFXTreeConfig.rootIcon;
  this.openIcon  = sOpenIcon || webFXTreeConfig.openRootIcon;
  /* Defaults to open */
  this.open      = (webFXTreeHandler.cookies.getCookie(this.id.substr(18,this.id.length - 18)) == '0')?false:true;
  this.folder    = true;
  this.rendered  = false;
  if (!webFXTreeHandler.behavior) {  webFXTreeHandler.behavior = sBehavior || webFXTreeConfig.defaultBehavior; }
  this.targetWindow = 'right';
}

WebFXTree.prototype = new WebFXTreeAbstractNode;

WebFXTree.prototype.setBehavior = function (sBehavior) {
  webFXTreeHandler.behavior =  sBehavior;
};

WebFXTree.prototype.getBehavior = function (sBehavior) {
  return webFXTreeHandler.behavior;
};

WebFXTree.prototype.getSelected = function() {
  if (webFXTreeHandler.selected) { return webFXTreeHandler.selected; }
  else { return null; }
}

WebFXTree.prototype.remove = function() { }

WebFXTree.prototype.expand = function() {
  this.doExpand();
}

WebFXTree.prototype.collapse = function() {
  this.focus();
  this.doCollapse();
}

WebFXTree.prototype.getFirst = function() {
  return null;
}

WebFXTree.prototype.getLast = function() {
  return null;
}

WebFXTree.prototype.getNextSibling = function() {
  return null;
}

WebFXTree.prototype.getPreviousSibling = function() {
  return null;
}

WebFXTree.prototype.keydown = function(key) {
  if (key == 39) { this.expand(); return false; }
  if (key == 37) { this.collapse(); return false; }
  if ((key == 40) && (this.open)) { this.childNodes[0].select(); return false; }
  return true;
}

WebFXTree.prototype.toString = function() {
  var str = "<div id=\"" + this.id + "\" ondblclick=\"webFXTreeHandler.toggle(this);\" class=\"webfx-tree-item\" onkeydown=\"return webFXTreeHandler.keydown(this)\">";
  str += "<img id=\"" + this.id + "-icon\" class=\"webfx-tree-icon\" src=\"" + ((webFXTreeHandler.behavior == 'classic' && this.open)?this.openIcon:this.icon) + "\" onclick=\"webFXTreeHandler.select(this);\"><a href=\"" + this.action + "\" id=\"" + this.id + "-anchor\" target=\"" + this.targetWindow + "\" onfocus=\"webFXTreeHandler.focus(this);\" onblur=\"webFXTreeHandler.blur(this);\">" + this.text + "</a></div>";
  str += "<div id=\"" + this.id + "-cont\" class=\"webfx-tree-container\" style=\"display: " + ((this.open)?'block':'none') + ";\">";
  for (var i = 0; i < this.childNodes.length; i++) {
    str += this.childNodes[i].toString(i, this.childNodes.length);
  }
  str += "</div>";
  this.rendered = true;
  return str;
};

/*
 * WebFXTreeItem class
 */

function WebFXTreeItem(sText, sAction, eParent, sIcon, sOpenIcon) {
  this.base = WebFXTreeAbstractNode;
  this.base(sText, sAction);
  /* Defaults to close */
  this.open = (webFXTreeHandler.cookies.getCookie(this.id.substr(18,this.id.length - 18)) == '1')?true:false;
  if (eParent) { eParent.add(this); }
  if (sIcon) { this.icon = sIcon; }
  if (sOpenIcon) { this.openIcon = sOpenIcon; }
}

WebFXTreeItem.prototype = new WebFXTreeAbstractNode;

WebFXTreeItem.prototype.remove = function() {
  var parentNode = this.parentNode;
  var prevSibling = this.getPreviousSibling(true);
  var nextSibling = this.getNextSibling(true);
  var folder = this.parentNode.folder;
  var last = ((nextSibling) && (nextSibling.parentNode) && (nextSibling.parentNode.id == parentNode.id))?false:true;
  this.getPreviousSibling().focus();
  this._remove();
  if (parentNode.childNodes.length == 0) {
    parentNode.folder = false;
    parentNode.open = false;
  }
  if (last) {
    if (parentNode.id == prevSibling.id) {
      document.getElementById(parentNode.id + '-icon').src = webFXTreeConfig.fileIcon;
    }
    else { }
  }
  if ((!prevSibling.parentNode) || (prevSibling.parentNode != parentNode)) {
    parentNode.indent(null, true, last, this._level);
  }
  if (document.getElementById(prevSibling.id + '-plus')) {
    if (nextSibling) {
      if ((parentNode == prevSibling) && (parentNode.getNextSibling))  { document.getElementById(prevSibling.id + '-plus').src = webFXTreeConfig.tIcon; }
      else if (nextSibling.parentNode != prevSibling) { document.getElementById(prevSibling.id + '-plus').src = webFXTreeConfig.lIcon; }
    }
    else { document.getElementById(prevSibling.id + '-plus').src = webFXTreeConfig.lIcon; }
  }
}

WebFXTreeItem.prototype._remove = function() {
  for (var i = this.childNodes.length - 1; i >= 0; i--) {
    this.childNodes[i]._remove();
   }
  for (var i = 0; i < this.parentNode.childNodes.length; i++) {
    if (this.id == this.parentNode.childNodes[i].id) {
      for (var j = i; j < this.parentNode.childNodes.length; j++) {
        this.parentNode.childNodes[i] = this.parentNode.childNodes[i+1]
      }
      this.parentNode.childNodes.length = this.parentNode.childNodes.length - 1;
      if (i + 1 == this.parentNode.childNodes.length) { this.parentNode._last = true; }
    }
  }
  webFXTreeHandler.all[this.id] = null;
  if (document.getElementById(this.id)) {
    document.getElementById(this.id).innerHTML = "";
    document.getElementById(this.id).removeNode();
  }
}

WebFXTreeItem.prototype.expand = function() {
  this.doExpand();
  document.getElementById(this.id + '-plus').src = this.minusIcon;
}

WebFXTreeItem.prototype.collapse = function() {
  this.focus();
  this.doCollapse();
  document.getElementById(this.id + '-plus').src = this.plusIcon;
}

WebFXTreeItem.prototype.getFirst = function() {
  return this.childNodes[0];
}

WebFXTreeItem.prototype.getLast = function() {
  if (this.childNodes[this.childNodes.length - 1].open) { return this.childNodes[this.childNodes.length - 1].getLast(); }
  else { return this.childNodes[this.childNodes.length - 1]; }
}

WebFXTreeItem.prototype.getNextSibling = function() {
  for (var i = 0; i < this.parentNode.childNodes.length; i++) {
    if (this == this.parentNode.childNodes[i]) { break; }
  }
  if (++i == this.parentNode.childNodes.length) { return this.parentNode.getNextSibling(); }
  else { return this.parentNode.childNodes[i]; }
}

WebFXTreeItem.prototype.getPreviousSibling = function(b) {
  for (var i = 0; i < this.parentNode.childNodes.length; i++) {
    if (this == this.parentNode.childNodes[i]) { break; }
  }
  if (i == 0) { return this.parentNode; }
  else {
    if ((this.parentNode.childNodes[--i].open) || (b && this.parentNode.childNodes[i].folder)) { return this.parentNode.childNodes[i].getLast(); }
    else { return this.parentNode.childNodes[i]; }
} }

WebFXTreeItem.prototype.keydown = function(key) {
  if ((key == 39) && (this.folder)) {
    if (!this.open) { this.expand(); return false; }
    else { this.getFirst().select(); return false; }
  }
  else if (key == 37) {
    if (this.open) { this.collapse(); return false; }
    else { this.parentNode.select(); return false; }
  }
  else if (key == 40) {
    if (this.open) { this.getFirst().select(); return false; }
    else {
      var sib = this.getNextSibling();
      if (sib) { sib.select(); return false; }
  } }
  else if (key == 38) { this.getPreviousSibling().select(); return false; }
  return true;
}

WebFXTreeItem.prototype.toString = function (nItem, nItemCount) {
  var foo = this.parentNode;
  var indent = '';
  if (nItem + 1 == nItemCount) { this.parentNode._last = true; }
  var i = 0;
  while (foo.parentNode) {
    foo = foo.parentNode;
    indent = "<img id=\"" + this.id + "-indent-" + i + "\" src=\"" + ((foo._last)?webFXTreeConfig.blankIcon:webFXTreeConfig.iIcon) + "\">" + indent;
    i++;
  }
  this._level = i;
  if (this.childNodes.length) { this.folder = 1; }
  else { this.open = false; }
  if ((this.folder) || (webFXTreeHandler.behavior != 'classic')) {
    if (!this.icon) { this.icon = webFXTreeConfig.folderIcon; }
    if (!this.openIcon) { this.openIcon = webFXTreeConfig.openFolderIcon; }
  }
  else if (!this.icon) { this.icon = webFXTreeConfig.fileIcon; }
  var label = this.text;
  label = label.replace('<', '<');
  label = label.replace('>', '>');
  var str = "<div id=\"" + this.id + "\" ondblclick=\"webFXTreeHandler.toggle(this);\" class=\"webfx-tree-item\" onkeydown=\"return webFXTreeHandler.keydown(this)\">";
  str += indent;
  str += "<img id=\"" + this.id + "-plus\" src=\"" + ((this.folder)?((this.open)?((this.parentNode._last)?webFXTreeConfig.lMinusIcon:webFXTreeConfig.tMinusIcon):((this.parentNode._last)?webFXTreeConfig.lPlusIcon:webFXTreeConfig.tPlusIcon)):((this.parentNode._last)?webFXTreeConfig.lIcon:webFXTreeConfig.tIcon)) + "\" onclick=\"webFXTreeHandler.toggle(this);\">"
  str += "<img id=\"" + this.id + "-icon\" src=\"" + ((webFXTreeHandler.behavior == 'classic' && this.open)?this.openIcon:this.icon) + "\" onclick=\"webFXTreeHandler.select(this);\"><a href=\"" + this.action + "\" id=\"" + this.id + "-anchor\" target=\"" + this.targetWindow + "\" onfocus=\"webFXTreeHandler.focus(this);\" onblur=\"webFXTreeHandler.blur(this);\">" + label + "</a></div>";
  str += "<div id=\"" + this.id + "-cont\" class=\"webfx-tree-container\" style=\"display: " + ((this.open)?'block':'none') + ";\">";
  for (var i = 0; i < this.childNodes.length; i++) {
    str += this.childNodes[i].toString(i,this.childNodes.length);
  }
  str += "</div>";
  this.plusIcon = ((this.parentNode._last)?webFXTreeConfig.lPlusIcon:webFXTreeConfig.tPlusIcon);
  this.minusIcon = ((this.parentNode._last)?webFXTreeConfig.lMinusIcon:webFXTreeConfig.tMinusIcon);
  return str;
}

<?php

/**
 * @file
 * Tests for Libraries API.
 */

/**
 * Tests basic Libraries API functions.
 */
class LibrariesUnitTestCase extends DrupalUnitTestCase {
  public static function getInfo() {
    return array(
      'name' => 'Libraries API unit tests',
      'description' => 'Tests basic functions provided by Libraries API.',
      'group' => 'Libraries API',
    );
  }

  function setUp() {
    drupal_load('module', 'libraries');
    parent::setUp();
  }

  /**
   * Tests libraries_get_path().
   */
  function testLibrariesGetPath() {
    // Note that, even though libraries_get_path() doesn't find the 'example'
    // library, we are able to make it 'installed' by specifying the 'library
    // path' up-front. This is only used for testing purposed and is strongly
    // discouraged as it defeats the purpose of Libraries API in the first
    // place.
    $this->assertEqual(libraries_get_path('example'), FALSE, 'libraries_get_path() returns FALSE for a missing library.');
  }

  /**
   * Tests libraries_prepare_files().
   */
  function testLibrariesPrepareFiles() {
    $expected = array(
      'files' => array(
        'js' => array('example.js' => array()),
        'css' => array('example.css' => array()),
        'php' => array('example.php' => array()),
      ),
    );
    $library = array(
      'files' => array(
        'js' => array('example.js'),
        'css' => array('example.css'),
        'php' => array('example.php'),
      ),
    );
    libraries_prepare_files($library, NULL, NULL);
    $this->assertEqual($expected, $library, 'libraries_prepare_files() works correctly.');
  }
}

/**
 * Tests basic detection and loading of libraries.
 */
class LibrariesTestCase extends DrupalWebTestCase {
  protected $profile = 'testing';

  public static function getInfo() {
    return array(
      'name' => 'Libraries detection and loading',
      'description' => 'Tests detection and loading of libraries.',
      'group' => 'Libraries API',
    );
  }

  function setUp() {
    parent::setUp('libraries', 'libraries_test_module');
    theme_enable(array('libraries_test_theme'));
  }

  /**
   * Tests libraries_detect_dependencies().
   */
  function testLibrariesDetectDependencies() {
    $library = array(
      'name' => 'Example',
      'dependencies' => array('example_missing'),
    );
    libraries_detect_dependencies($library);
    $this->assertEqual($library['error'], 'missing dependency', 'libraries_detect_dependencies() detects missing dependency');
    $error_message = t('The %dependency library, which the %library library depends on, is not installed.', array(
      '%dependency' => 'Example missing',
      '%library' => $library['name'],
    ));
    $this->verbose("Expected:<br>$error_message");
    $this->verbose('Actual:<br>' . $library['error message']);
    $this->assertEqual($library['error message'], $error_message, 'Correct error message for a missing dependency');
    // Test versioned dependencies.
    $version = '1.1';
    $compatible = array(
      '1.1',
      '<=1.1',
      '>=1.1',
      '<1.2',
      '<2.0',
      '>1.0',
      '>1.0-rc1',
      '>1.0-beta2',
      '>1.0-alpha3',
      '>0.1',
      '<1.2, >1.0',
      '>0.1, <=1.1',
    );
    $incompatible = array(
      '1.2',
      '2.0',
      '<1.1',
      '>1.1',
      '<=1.0',
      '<=1.0-rc1',
      '<=1.0-beta2',
      '<=1.0-alpha3',
      '>=1.2',
      '<1.1, >0.9',
      '>=0.1, <1.1',
    );
    $library = array(
      'name' => 'Example',
    );
    foreach ($compatible as $version_string) {
      $library['dependencies'][0] = "example_dependency ($version_string)";
      // libraries_detect_dependencies() is a post-detect callback, so
      // 'installed' is already set, when it is called. It sets the value to
      // FALSE for missing or incompatible dependencies.
      $library['installed'] = TRUE;
      libraries_detect_dependencies($library);
      $this->verbose('Library:<pre>' . var_export($library, TRUE) . '</pre>');
      $this->assertTrue($library['installed'], "libraries_detect_dependencies() detects compatible version string: '$version_string' is compatible with '$version'");
    }
    foreach ($incompatible as $version_string) {
      $library['dependencies'][0] = "example_dependency ($version_string)";
      $library['installed'] = TRUE;
      unset($library['error'], $library['error message']);
      libraries_detect_dependencies($library);
      $this->verbose('Library:<pre>' . var_export($library, TRUE) . '</pre>');
      $this->assertEqual($library['error'], 'incompatible dependency', "libraries_detect_dependencies() detects incompatible version strings: '$version_string' is incompatible with '$version'");
    }
    // Instead of repeating this assertion for each version string, we just
    // re-use the $library variable from the foreach loop.
    $error_message = t('The version %dependency_version of the %dependency library is not compatible with the %library library.', array(
      '%dependency_version' => $version,
      '%dependency' => 'Example dependency',
      '%library' => $library['name'],
    ));
    $this->verbose("Expected:<br>$error_message");
    $this->verbose('Actual:<br>' . $library['error message']);
    $this->assertEqual($library['error message'], $error_message, 'Correct error message for an incompatible dependency');
  }

  /**
   * Tests libraries_scan_info_files().
   */
  function testLibrariesScanInfoFiles() {
    $expected = array('example_info_file' => (object) array(
      'uri' => drupal_get_path('module', 'libraries') . '/tests/libraries/example_info_file.libraries.info',
      'filename' => 'example_info_file.libraries.info',
      'name' => 'example_info_file.libraries',
    ));
    $this->assertEqual(libraries_scan_info_files(), $expected, 'libraries_scan_info_files() correctly finds the example info file.');
    $this->verbose('<pre>' . var_export(libraries_scan_info_files(), TRUE) . '</pre>');
  }

  /**
   * Tests libraries_info().
   */
  function testLibrariesInfo() {
    // Test that modules can provide and alter library information.
    $info = libraries_info();
    $this->assertTrue(isset($info['example_module']));
    $this->verbose('Library:<pre>' . var_export($info['example_module'], TRUE) . '</pre>');
    $this->assertEqual($info['example_module']['info type'], 'module');
    $this->assertEqual($info['example_module']['module'], 'libraries_test_module');
    $this->assertTrue($info['example_module']['module_altered']);

    // Test that themes can provide and alter library information.
    $this->assertTrue(isset($info['example_theme']));
    $this->verbose('Library:<pre>' . var_export($info['example_theme'], TRUE) . '</pre>');
    $this->assertEqual($info['example_theme']['info type'], 'theme');
    $this->assertEqual($info['example_theme']['theme'], 'libraries_test_theme');
    $this->assertTrue($info['example_theme']['theme_altered']);

    // Test that library information is found correctly.
    $expected = array(
      'name' => 'Example files',
      'library path' => drupal_get_path('module', 'libraries') . '/tests/libraries/example',
      'version' => '1',
      'files' => array(
        'js' => array('example_1.js' => array()),
        'css' => array('example_1.css' => array()),
        'php' => array('example_1.php' => array()),
      ),
      'info type' => 'module',
      'module' => 'libraries_test_module',
    );
    libraries_info_defaults($expected, 'example_files');
    $library = libraries_info('example_files');
    $this->verbose('Expected:<pre>' . var_export($expected, TRUE) . '</pre>');
    $this->verbose('Actual:<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library, $expected, 'Library information is correctly gathered.');

    // Test a library specified with an .info file gets detected.
    $expected = array(
      'name' => 'Example info file',
      'info type' => 'info file',
      'info file' => drupal_get_path('module', 'libraries') . '/tests/libraries/example_info_file.libraries.info',
    );
    libraries_info_defaults($expected, 'example_info_file');
    $library = libraries_info('example_info_file');
    // If this module was downloaded from Drupal.org, the Drupal.org packaging
    // system has corrupted the test info file.
    // @see http://drupal.org/node/1606606
    unset($library['core'], $library['datestamp'], $library['project'], $library['version']);
    $this->verbose('Expected:<pre>' . var_export($expected, TRUE) . '</pre>');
    $this->verbose('Actual:<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library, $expected, 'Library specified with an .info file found');
  }

  /**
   * Tests libraries_detect().
   */
  function testLibrariesDetect() {
    // Test missing library.
    $library = libraries_detect('example_missing');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['error'], 'not found', 'Missing library not found.');
    $error_message = t('The %library library could not be found.', array(
      '%library' => $library['name'],
    ));
    $this->assertEqual($library['error message'], $error_message, 'Correct error message for a missing library.');

    // Test unknown library version.
    $library = libraries_detect('example_undetected_version');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['error'], 'not detected', 'Undetected version detected as such.');
    $error_message = t('The version of the %library library could not be detected.', array(
      '%library' => $library['name'],
    ));
    $this->assertEqual($library['error message'], $error_message, 'Correct error message for a library with an undetected version.');

    // Test unsupported library version.
    $library = libraries_detect('example_unsupported_version');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['error'], 'not supported', 'Unsupported version detected as such.');
    $error_message = t('The installed version %version of the %library library is not supported.', array(
      '%version' => $library['version'],
      '%library' => $library['name'],
    ));
    $this->assertEqual($library['error message'], $error_message, 'Correct error message for a library with an unsupported version.');

    // Test supported library version.
    $library = libraries_detect('example_supported_version');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['installed'], TRUE, 'Supported library version found.');

    // Test libraries_get_version().
    $library = libraries_detect('example_default_version_callback');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['version'], '1', 'Expected version returned by default version callback.');

    // Test a multiple-parameter version callback.
    $library = libraries_detect('example_multiple_parameter_version_callback');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['version'], '1', 'Expected version returned by multiple parameter version callback.');

    // Test a top-level files property.
    $library = libraries_detect('example_files');
    $files = array(
      'js' => array('example_1.js' => array()),
      'css' => array('example_1.css' => array()),
      'php' => array('example_1.php' => array()),
    );
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['files'], $files, 'Top-level files property works.');

    // Test version-specific library files.
    $library = libraries_detect('example_versions');
    $files = array(
      'js' => array('example_2.js' => array()),
      'css' => array('example_2.css' => array()),
      'php' => array('example_2.php' => array()),
    );
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['files'], $files, 'Version-specific library files found.');

    // Test missing variant.
    $library = libraries_detect('example_variant_missing');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['variants']['example_variant']['error'], 'not found', 'Missing variant not found');
    $error_message = t('The %variant variant of the %library library could not be found.', array(
      '%variant' => 'example_variant',
      '%library' => 'Example variant missing',
    ));
    $this->assertEqual($library['variants']['example_variant']['error message'], $error_message, 'Correct error message for a missing variant.');

    // Test existing variant.
    $library = libraries_detect('example_variant');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['variants']['example_variant']['installed'], TRUE, 'Existing variant found.');
  }

  /**
   * Tests libraries_load().
   */
  function testLibrariesLoad() {
    // Test dependencies.
    $library = libraries_load('example_dependency_missing');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertFalse($library['loaded'], 'Library with missing dependency cannot be loaded');
    $library = libraries_load('example_dependency_incompatible');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertFalse($library['loaded'], 'Library with incompatible dependency cannot be loaded');
    $library = libraries_load('example_dependency_compatible');
    $this->verbose('<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library['loaded'], 1, 'Library with compatible dependency is loaded');
    $loaded = &drupal_static('libraries_load');
    $this->verbose('<pre>' . var_export($loaded, TRUE) . '</pre>');
    $this->assertEqual($loaded['example_dependency']['loaded'], 1, 'Dependency library is also loaded');

    // Test that PHP files that have a local $path variable do not break library
    // loading.
    // @see _libraries_require_once()
    $library = libraries_load('example_path_variable_override');
    $this->assertEqual($library['loaded'], 2, 'PHP files cannot break library loading.');
  }

  /**
   * Tests the applying of callbacks.
   */
  function testCallbacks() {
    $expected = array(
      'name' => 'Example callback',
      'library path' => drupal_get_path('module', 'libraries') . '/tests/libraries/example',
      'version' => '1',
      'versions' => array(
        '1' => array(
          'variants' => array(
            'example_variant' => array(
              'info callback' => 'not applied',
              'pre-detect callback' => 'not applied',
              'post-detect callback' => 'not applied',
              'pre-dependencies-load callback' => 'not applied',
              'pre-load callback' => 'not applied',
              'post-load callback' => 'not applied',
            ),
          ),
          'info callback' => 'not applied',
          'pre-detect callback' => 'not applied',
          'post-detect callback' => 'not applied',
          'pre-dependencies-load callback' => 'not applied',
          'pre-load callback' => 'not applied',
          'post-load callback' => 'not applied',
        ),
      ),
      'variants' => array(
        'example_variant' => array(
          'info callback' => 'not applied',
          'pre-detect callback' => 'not applied',
          'post-detect callback' => 'not applied',
          'pre-dependencies-load callback' => 'not applied',
          'pre-load callback' => 'not applied',
          'post-load callback' => 'not applied',
        ),
      ),
      'callbacks' => array(
        'info' => array('_libraries_test_module_info_callback'),
        'pre-detect' => array('_libraries_test_module_pre_detect_callback'),
        'post-detect' => array('_libraries_test_module_post_detect_callback'),
        'pre-dependencies-load' => array('_libraries_test_module_pre_dependencies_load_callback'),
        'pre-load' => array('_libraries_test_module_pre_load_callback'),
        'post-load' => array('_libraries_test_module_post_load_callback'),
      ),
      'info callback' => 'not applied',
      'pre-detect callback' => 'not applied',
      'post-detect callback' => 'not applied',
      'pre-dependencies-load callback' => 'not applied',
      'pre-load callback' => 'not applied',
      'post-load callback' => 'not applied',
      'info type' => 'module',
      'module' => 'libraries_test_module',
    );
    libraries_info_defaults($expected, 'example_callback');

    // Test a callback in the 'info' group.
    $expected['info callback'] = 'applied (top-level)';
    $expected['versions']['1']['info callback'] = 'applied (version 1)';
    $expected['versions']['1']['variants']['example_variant']['info callback'] = 'applied (version 1, variant example_variant)';
    $expected['variants']['example_variant']['info callback'] = 'applied (variant example_variant)';
    $library = libraries_info('example_callback');
    $this->verbose('Expected:<pre>' . var_export($expected, TRUE) . '</pre>');
    $this->verbose('Actual:<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library, $expected, 'Prepare callback was applied correctly.');

    // Test a callback in the 'pre-detect' and 'post-detect' phases.
    // Successfully detected libraries should only contain version information
    // for the detected version and thus, be marked as installed.
    unset($expected['versions']);
    $expected['installed'] = TRUE;
    // Additionally, version-specific properties of the detected version are
    // supposed to override the corresponding top-level properties.
    $expected['info callback'] = 'applied (version 1)';
    $expected['variants']['example_variant']['installed'] = TRUE;
    $expected['variants']['example_variant']['info callback'] = 'applied (version 1, variant example_variant)';
    // Version-overloading takes place after the 'pre-detect' callbacks have
    // been applied.
    $expected['pre-detect callback'] = 'applied (version 1)';
    $expected['post-detect callback'] = 'applied (top-level)';
    $expected['variants']['example_variant']['pre-detect callback'] = 'applied (version 1, variant example_variant)';
    $expected['variants']['example_variant']['post-detect callback'] = 'applied (variant example_variant)';
    $library = libraries_detect('example_callback');
    $this->verbose('Expected:<pre>' . var_export($expected, TRUE) . '</pre>');
    $this->verbose('Actual:<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library, $expected, 'Detect callback was applied correctly.');

    // Test a callback in the 'pre-dependencies-load', 'pre-load' and
    // 'post-load' phases.
    // Successfully loaded libraries should only contain information about the
    // already loaded variant.
    unset($expected['variants']);
    $expected['loaded'] = 0;
    $expected['pre-dependencies-load callback'] = 'applied (top-level)';
    $expected['pre-load callback'] = 'applied (top-level)';
    $expected['post-load callback'] = 'applied (top-level)';
    $library = libraries_load('example_callback');
    $this->verbose('Expected:<pre>' . var_export($expected, TRUE) . '</pre>');
    $this->verbose('Actual:<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library, $expected, 'Pre-load and post-load callbacks were applied correctly.');
    // This is not recommended usually and is only used for testing purposes.
    drupal_static_reset('libraries_load');
    // Successfully loaded library variants are supposed to contain the specific
    // variant information only.
    $expected['info callback'] = 'applied (version 1, variant example_variant)';
    $expected['pre-detect callback'] = 'applied (version 1, variant example_variant)';
    $expected['post-detect callback'] = 'applied (variant example_variant)';
    $library = libraries_load('example_callback', 'example_variant');
    $this->verbose('Expected:<pre>' . var_export($expected, TRUE) . '</pre>');
    $this->verbose('Actual:<pre>' . var_export($library, TRUE) . '</pre>');
    $this->assertEqual($library, $expected, 'Pre-detect and post-detect callbacks were applied correctly to a variant.');
  }

  /**
   * Tests that library files are properly added to the page output.
   *
   * We check for JavaScript and CSS files directly in the DOM and add a list of
   * included PHP files manually to the page output.
   *
   * @see _libraries_test_module_load()
   */
  function testLibrariesOutput() {
    // Test loading of a simple library with a top-level files property.
    $this->drupalGet('libraries-test-module/files');
    $this->assertLibraryFiles('example_1', 'File loading');

    // Test loading of integration files.
    $this->drupalGet('libraries-test-module/module-integration-files');
    $this->assertRaw('libraries_test_module.js', 'Integration file loading: libraries_test_module.js found');
    $this->assertRaw('libraries_test_module.css', 'Integration file loading: libraries_test_module.css found');
    $this->assertRaw('libraries_test_module.inc', 'Integration file loading: libraries_test_module.inc found');
    $this->drupalGet('libraries-test-module/theme-integration-files');
    $this->assertRaw('libraries_test_theme.js', 'Integration file loading: libraries_test_theme.js found');
    $this->assertRaw('libraries_test_theme.css', 'Integration file loading: libraries_test_theme.css found');
    $this->assertRaw('libraries_test_theme.inc', 'Integration file loading: libraries_test_theme.inc found');

    // Test loading of post-load integration files.
    $this->drupalGet('libraries-test-module/module-integration-files-post-load');
    // If the files were not loaded correctly, a fatal error occurs.
    $this->assertResponse(200, 'Post-load integration files are loaded correctly.');

    // Test version overloading.
    $this->drupalGet('libraries-test-module/versions');
    $this->assertLibraryFiles('example_2', 'Version overloading');

    // Test variant loading.
    $this->drupalGet('libraries-test-module/variant');
    $this->assertLibraryFiles('example_3', 'Variant loading');

    // Test version overloading and variant loading.
    $this->drupalGet('libraries-test-module/versions-and-variants');
    $this->assertLibraryFiles('example_4', 'Concurrent version and variant overloading');

    // Test caching.
    variable_set('libraries_test_module_cache', TRUE);
    cache_clear_all('example_callback', 'cache_libraries');
    // When the library information is not cached, all callback groups should be
    // invoked.
    $this->drupalGet('libraries-test-module/cache');
    $this->assertRaw('The <em>info</em> callback group was invoked.', 'Info callback invoked for uncached libraries.');
    $this->assertRaw('The <em>pre-detect</em> callback group was invoked.', 'Pre-detect callback invoked for uncached libraries.');
    $this->assertRaw('The <em>post-detect</em> callback group was invoked.', 'Post-detect callback invoked for uncached libraries.');
    $this->assertRaw('The <em>pre-load</em> callback group was invoked.', 'Pre-load callback invoked for uncached libraries.');
    $this->assertRaw('The <em>post-load</em> callback group was invoked.', 'Post-load callback invoked for uncached libraries.');
    // When the library information is cached only the 'pre-load' and
    // 'post-load' callback groups should be invoked.
    $this->drupalGet('libraries-test-module/cache');
    $this->assertNoRaw('The <em>info</em> callback group was not invoked.', 'Info callback not invoked for cached libraries.');
    $this->assertNoRaw('The <em>pre-detect</em> callback group was not invoked.', 'Pre-detect callback not invoked for cached libraries.');
    $this->assertNoRaw('The <em>post-detect</em> callback group was not invoked.', 'Post-detect callback not invoked for cached libraries.');
    $this->assertRaw('The <em>pre-load</em> callback group was invoked.', 'Pre-load callback invoked for cached libraries.');
    $this->assertRaw('The <em>post-load</em> callback group was invoked.', 'Post-load callback invoked for cached libraries.');
    variable_set('libraries_test_module_cache', FALSE);
  }

  /**
   * Helper function to assert that a library was correctly loaded.
   *
   * Asserts that all the correct files were loaded and all the incorrect ones
   * were not.
   *
   * @param $name
   *   The name of the files that should be loaded. The current testing system
   *   knows of 'example_1', 'example_2', 'example_3' and 'example_4'. Each name
   *   has an associated JavaScript, CSS and PHP file that will be asserted. All
   *   other files will be asserted to not be loaded. See
   *   tests/example/README.txt for more information on how the loading of the
   *   files is tested.
   * @param $label
   *   (optional) A label to prepend to the assertion messages, to make them
   *   less ambiguous.
   * @param $extensions
   *   (optional) The expected file extensions of $name. Defaults to
   *   array('js', 'css', 'php').
   */
  function assertLibraryFiles($name, $label = '', $extensions = array('js', 'css', 'php')) {
    $label = ($label !== '' ? "$label: " : '');

    // Test that the wrong files are not loaded...
    $names = array(
      'example_1' => FALSE,
      'example_2' => FALSE,
      'example_3' => FALSE,
      'example_4' => FALSE,
    );
    // ...and the correct ones are.
    $names[$name] = TRUE;

    // Test for the specific HTML that the different file types appear as in the
    // DOM.
    $html = array(
      'js' => array('<script type="text/javascript" src="', '"></script>'),
      'css' => array('@import url("', '");'),
      // PHP files do not get added to the DOM directly.
      // @see _libraries_test_load()
      'php' => array('<li>', '</li>'),
    );

    foreach ($names as $name => $expected) {
      foreach ($extensions as $extension) {
        $filepath = drupal_get_path('module', 'libraries') . "/tests/libraries/example/$name.$extension";
        // JavaScript and CSS files appear as full URLs and with an appended
        // query string.
        if (in_array($extension, array('js', 'css'))) {
          $filepath = url('', array('absolute' => TRUE)) . $filepath . '?' . variable_get('css_js_query_string');
        }
        $raw = $html[$extension][0] . $filepath . $html[$extension][1];
        if ($expected) {
          $this->assertRaw($raw, "$label$name.$extension found.");
        }
        else {
          $this->assertNoRaw($raw, "$label$name.$extension not found.");
        }
      }
    }
  }

}


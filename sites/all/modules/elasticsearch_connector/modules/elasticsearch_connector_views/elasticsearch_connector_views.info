name = Elasticsearch Connector Views (NOT STABLE)
description = "Not Stable yet.
Use only for development purposes or if you know what you are doing!
The module will integrate Elasticsearch Connector module with Views.
This modules will allow you to select any index and any document type from
selected cluster."
core = 7.x
package = Elasticsearch

dependencies[] = elasticsearch_connector
dependencies[] = views

files[] = elasticsearch_connector_views_query.inc
files[] = handlers/elasticsearch_connector_views_handler_field.inc
files[] = handlers/elasticsearch_connector_views_snippet_handler_field.inc
files[] = handlers/elasticsearch_connector_views_handler_sort.inc
files[] = handlers/elasticsearch_connector_views_handler_filter.inc
files[] = handlers/elasticsearch_connector_views_handler_filter_date.inc
files[] = handlers/elasticsearch_connector_views_handler_field_date.inc
files[] = handlers/elasticsearch_connector_views_handler_filter_string.inc
files[] = handlers/elasticsearch_connector_views_handler_filter_string_autocomplete.inc
files[] = handlers/elasticsearch_connector_views_keyword_handler_filter.inc
files[] = handlers/elasticsearch_connector_views_handler_filter_numeric.inc
files[] = handlers/elasticsearch_connector_views_handler_argument.inc
; Information added by Drupal.org packaging script on 2018-02-07
version = "7.x-5.0-alpha1"
core = "7.x"
project = "elasticsearch_connector"
datestamp = "1518040984"


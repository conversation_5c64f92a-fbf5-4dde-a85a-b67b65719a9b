<?php

// profile.install cannot have the same name as existing module.install,
// so the profile name ist klicktipp_app to prevent this

/**
 * @file
 * Install, update and uninstall functions for the klicktipp installation profile.
 */

use App\Klicktipp\APIKey;
use App\Klicktipp\Core;
use App\Klicktipp\CustomFields;
use App\Klicktipp\DigiStore;
use App\Klicktipp\Lists;
use App\Klicktipp\Tag;
use App\Klicktipp\UserGroups;
use App\Klicktipp\VarSegmentProductCategory;


/**
 * Implements hook_install().
 *
 * Performs actions to set up the site for this profile.
 *
 * @see system_install()
 */
function klicktipp_app_install() {
  // Was not called by drush during bootstrap. Required for site-install. Need to do that now.
  klicktipp_boot();

  // Enable some standard blocks.
  $default_theme = variable_get('theme_default', 'bartik');
  $values = array(
    array(
      'module' => 'system',
      'delta' => 'main',
      'theme' => $default_theme,
      'status' => 1,
      'weight' => 0,
      'region' => 'content',
      'pages' => '',
      'cache' => -1,
      'visibility' => 0,
    ),
    array(
      'module' => 'user',
      'delta' => 'login',
      'theme' => $default_theme,
      'status' => 1,
      'weight' => 0,
      'region' => 'content',
      'pages' => '<front>',
      'cache' => -1,
      'visibility' => 1,
    ),
    array(
      'module' => 'system',
      'delta' => 'help',
      'theme' => $default_theme,
      'status' => 0,
      'weight' => 0,
      'region' => 'header',
      'pages' => '',
      'cache' => -1,
      'visibility' => 0,
    ),
  );
  $query = db_insert('block')->fields(array(
    'module',
    'delta',
    'theme',
    'status',
    'weight',
    'region',
    'pages',
    'cache',
    'visibility',
  ));
  foreach ($values as $record) {
    $query->values($record);
  }
  $query->execute();

  // Enable default permissions for system roles.
  user_role_grant_permissions(DRUPAL_ANONYMOUS_RID, array('access content'));
  user_role_grant_permissions(DRUPAL_AUTHENTICATED_RID, array('access content'));

  // install languages
  locale_add_language('de', 'German', 'Deutsch', LANGUAGE_LTR, '', '', TRUE);
  locale_add_language('en-us', 'English US', 'English', LANGUAGE_LTR, '', '', TRUE);
  locale_add_language('pt-br', 'Portuguese, Brazil', 'Português', LANGUAGE_LTR, '', '', TRUE);
  locale_add_language('ru', 'Russian', 'Русский', LANGUAGE_LTR, '', '', TRUE);

  // only page and book and book is provided by the book module
  $page = array(
    'type' => 'page',
    'name' => st('Basic page'),
    'base' => 'node_content',
    'description' => st("Use <em>basic pages</em> for your static content, such as an 'About us' page."),
    'custom' => 1,
    'modified' => 1,
    'locked' => 0,
  );
  $page = node_type_set_defaults($page);
  node_type_save($page);
  node_add_body_field($page);

  // add url aliase to make menu work
  $path = ['alias' => 'bestellen', 'source' => 'order'];
  path_save($path);
  $path = ['alias' => 'bestellen/enterprise', 'source' => 'order/enterprise'];
  path_save($path);

  // Default page to not be promoted and have comments disabled.
  variable_set('node_options_page', array('status'));

  // Don't display date and author information for page nodes by default.
  $theme_settings = variable_get('theme_settings', array());
  $theme_settings['toggle_node_info_page'] = FALSE;
  variable_set('theme_settings', $theme_settings);

  // menu selection
  variable_set('menu_options_page', array(KT_MENU_MAIN, KT_MENU_MAIN_UNAUTH));
  variable_set('menu_options_book', array(KT_MENU_MAIN, KT_MENU_MAIN_UNAUTH));

  // Allow visitor account creation with administrative approval.
  variable_set('user_register', USER_REGISTER_VISITORS_ADMINISTRATIVE_APPROVAL);

  // default deletion method: Delete the account and make its content belong to the Anonymous user.
  // This default setting is defined under "admin/config/people/accounts".
  variable_set('user_cancel_method', 'user_cancel_reassign');

  // Update the menu router information.
  menu_rebuild();

  _klicktipp_app_set_variables();

  _klicktipp_app_create_vocabulary();

  $marketing = _klicktipp_app_create_roles_and_permissions();

  _klicktipp_app_select_themes();

  _klicktipp_app_create_menu();

  _klicktipp_app_formats();

  ///////////////////////////////////////////////////
  //S3 settings
  variable_set('s3_settings_key', KLICKTIPP_S3_SETTINGS_KEY);
  variable_set('s3_settings_secret', KLICKTIPP_S3_SETTINGS_SECRET);
  ///////////////////////////////////////////////////

  variable_set('klicktipp_wts_queue_slice', 1000);

  // jquery update module
  variable_set('jquery_update_jquery_version', '1.10');
  variable_set('jquery_update_jquery_admin_version', '');
  variable_set('jquery_update_compression_type', 'min');
  variable_set('jquery_update_jquery_cdn', 'none');

  // cachexclude default
  variable_set('cacheexclude_list', 'api/*');

  // ultimate cron
  // queue is driven by beanstalk in production, but we dont have that in test
  //variable_set('ultimate_cron_plugin_settings_queue_settings_enabled', 0);

  _klicktipp_app_profile_load_rest_service();

  // amember
  _klicktipp_app_profile_load_amember_config($marketing);
  _klicktipp_rename_amember_tables();

  _klicktipp_create_default_marketing_account_segments($marketing['account']);
}

function _klicktipp_app_create_vocabulary() {
  // manual tag taxonomy
  $vocab_name = 'klicktipp_tags';
  $new_vocab = (object) array(
    'name' => 'Klicktipp Tags',
    'description' => 'Klicktipp Tags',
    'machine_name' => $vocab_name,
  );
  taxonomy_vocabulary_save($new_vocab);
  $vocab = taxonomy_vocabulary_machine_name_load($vocab_name);
  variable_set('klicktipp_manual_tag_vocab', $vocab->vid);
}

function _klicktipp_app_set_variables() {
  ///////////////////////////////////////////////////
  // AMEMBER

  variable_set('amember_affiliate_url', APP_URL);

  ///////////////////////////////////////////////////
  // KLICKTIPP

  // klicktipp settings
  variable_set('klicktipp_spamscore_email', 12.0);
  variable_set('klicktipp_spamscore_signature', 2.0);

  variable_set('klicktipp_forbidden_as_sender_patterns', '*@gmail.com');

  variable_set('klicktipp_confirmation_subject', 'One click left ...');

  $body = 'Please click here: <a href="%Link:Confirm%">Confirm E-mail address</a><br />
<br />
Kind regards<br />
%User:FirstName% %User:LastName%<br />
<br />
Impressum:<br />
<br />
%User:CompanyName%<br />
%User:FirstName% %User:LastName%<br />
%User:Street%<br />
%User:Zip% %User:City%<br />
%User:Country%<br />
<br />
%Subscriber:SubscriptionIP%<br />
%Subscriber:OptInDate%<br />
%Subscriber:SubscriptionReferrer%<br />
%User:EmailAddress%
';
  variable_set('klicktipp_confirmation_body', $body);
  variable_set('klicktipp_confirmation_free_body', $body);

  variable_set('klicktipp_plainsignature_default', 'Impressum:

%User:CompanyName%
%User:FirstName% %User:LastName%
%User:Street%
%User:Zip% %User:City%
%User:Country%

%User:Website%

Sie möchten von uns keine E-Mails mehr erhalten? Dann können
Sie sich mit nur einem Klick auf den folgenden Link sicher aus
diesem Verteiler austragen:

%Link:Unsubscribe%

E-Mail-Marketing by Klick-Tipp
%User:AffiliateURL%');
  variable_set('klicktipp_htmlsignature_default', 'Impressum:<br />
<br />
%User:CompanyName%<br />
%User:FirstName% %User:LastName%<br />
%User:Street%<br />
%User:Zip%&nbsp;%User:City%<br />
%User:Country%<br />
<br />
%User:Website%<br />
<br />
Sie m&ouml;chten von uns keine E-Mails mehr erhalten? Dann k&ouml;nnen Sie sich mit nur einem Klick sicher <strong><a href="%Link:Unsubscribe%">aus diesem Verteiler austragen</a></strong>.<br />
<br />
E-Mail-Marketing by <a href="%User:AffiliateURL%">Klick-Tipp</a>');
    variable_set('klicktipp_htmlsignature_transactional', 'Impressum:<br />
<br />
%User:CompanyName%<br />
%User:FirstName% %User:LastName%<br />
%User:Street%<br />
%User:Zip%&nbsp;%User:City%<br />
%User:Country%<br />
<br />
%User:Website%<br />
<br />
E-Mail-Marketing by <a href="%User:AffiliateURL%">Klick-Tipp</a>');
  variable_set('klicktipp_aliases_archive', 'archive');
  variable_set('klicktipp_aliases_opt_confirm', 'confirm');
  variable_set('klicktipp_aliases_track_link', 'info');
  variable_set('klicktipp_aliases_track_open', 'images');
  variable_set('klicktipp_aliases_unsubscribe', 'unsubscribe');
  variable_set('klicktipp_aliases_web_browser', 'web');

  variable_set(KLICKTIPP_SPAMASSASSIN, '-Lt --siteconfigpath=/srv/www/klicktipp/spamassassin-config-de');

  //frontpage welcome
  variable_set('klicktipp_content_include_frontpage_welcome', 'content_includes/frontpage_welcome.inc');

  variable_set('beanstalkd_host', KLICKTIPP_BEANSTALKD_HOSTNAME);
}


function _klicktipp_app_create_roles_and_permissions() {
  // User Amember with test data
  define('USERNAME_AMEMBER', 'Amember');
  user_save(NULL, array(
    'RelUserGroupID' => 0,
    'name' => USERNAME_AMEMBER,
    'FirstName' => 'Amember',
    'LastName' => 'Amember',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));
  $account = user_load_by_name(USERNAME_AMEMBER);
  $user380 = $account->uid;

  variable_set('klicktipp_marketing_account_id', $user380);

  // collect data for marketing account
  $marketing = array(
    'account' => $account,
    'pass' => 'U4nATY2f',
  );

  $ListSOI = Lists::InsertDB(array(
    'Name' => 'SOI Klicktipp',
    'RelOwnerUserID' => $user380,
    'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE
  ));

  $TagIDKlicktippNewsletter = Tag::CreateManualTag($user380, 'Klicktipp Newsletter', '');

  $TagIDKlicktippEhemalig = Tag::CreateManualTag($user380, 'Klicktipp Kunde ehemalig', '');
  $TagIDKlicktippKunde = Tag::CreateManualTag($user380, 'Klicktipp Kunde', '', array(
    'OptInSubscribeTo' => array($TagIDKlicktippNewsletter),
    'OptInUnsubscribeFrom' => array($TagIDKlicktippEhemalig)
  ));
  $ArrayTag = Tag::RetrieveTag($user380, $TagIDKlicktippEhemalig);
  $ArrayTag['TagData'] = array('OptInUnsubscribeFrom' => array($TagIDKlicktippKunde));
  Tag::UpdateTag($ArrayTag);

  $marketing['Tag-Klicktipp-Kunde'] = $TagIDKlicktippKunde;
  $marketing['Tag-Klicktipp-Kunde-Ehemalig'] = $TagIDKlicktippEhemalig;
  $marketing['Tag-Klicktipp-Newsletter'] = $TagIDKlicktippNewsletter;

  $TagIDSplittestclubNewsletter = Tag::CreateManualTag($user380, 'Splittestclub Newsletter', '');
  $TagIDSplittestclubEhemalig = Tag::CreateManualTag($user380, 'Splittestclub Kunde ehemalig', '');
  $TagIDSplittestclubKunde = Tag::CreateManualTag($user380, 'Splittestclub Kunde', '', array(
    'OptInSubscribeTo' => array($TagIDSplittestclubNewsletter),
    'OptInUnsubscribeFrom' => array($TagIDSplittestclubEhemalig)
  ));
  $ArrayTag = Tag::RetrieveTag($user380, $TagIDSplittestclubEhemalig);
  $ArrayTag['TagData'] = array('OptInUnsubscribeFrom' => array($TagIDSplittestclubKunde));
  Tag::UpdateTag($ArrayTag);

  $marketing['Tag-Splitestclub-Kunde'] = $TagIDSplittestclubKunde;
  $marketing['Tag-Splitestclub-Kunde-Ehemalig'] = $TagIDSplittestclubEhemalig;
  $marketing['Tag-Splitestclub-Newsletter'] = $TagIDSplittestclubNewsletter;

  // create roles and permissions
  define('ROLENAME_BASIC', 'Standard');
  $basic_permissions = array(
    'access klicktipp',
    'access add contacts',
    'access campaign builder',
    'access wufoo',
    'access IPN DigiStore24',
    'access IPN AffiliCon',
    'access IPN Clickbank',
    'access IPN PayPal',
    'change subscriber email address',
    'use agency access',
  );

  // user_role_save does only work here - not in .profile...
  $role_basic = new stdClass();
  $role_basic->name = ROLENAME_BASIC;
  user_role_save($role_basic);
  user_role_grant_permissions($role_basic->rid, $basic_permissions);

  // create plus role
  define('ROLENAME_PLUS', 'Premium');
  $plus_permissions = array(
    'access klicktipp',
    'klicktipp premium',
    'access add contacts',
    'access campaign builder',
    'klicktipp api',
    'access wufoo',
    'access IPN DigiStore24',
    'access IPN AffiliCon',
    'access IPN Clickbank',
    'access IPN PayPal',
    'change subscriber email address',
    'use agency access',
  );
  $role_plus = new stdClass();
  $role_plus->name = ROLENAME_PLUS;
  user_role_save($role_plus);
  user_role_grant_permissions($role_plus->rid, $plus_permissions);

  // create advanced role
  define('ROLENAME_ADVANCED', 'Deluxe');
  $advanced_permissions = array(
    'access klicktipp',
    'klicktipp premium',
    'klicktipp deluxe',
    'access add contacts',
    'access campaign builder',
    'klicktipp api',
    'access wufoo',
    'access IPN DigiStore24',
    'access IPN AffiliCon',
    'access IPN Clickbank',
    'access IPN PayPal',
    'access splittest-club',
    'change subscriber email address',
    'use agency access',
  );
  $role_advanced = new stdClass();
  $role_advanced->name = ROLENAME_ADVANCED;
  user_role_save($role_advanced);
  user_role_grant_permissions($role_advanced->rid, $advanced_permissions);

  // create enterprise
  define('ROLENAME_ENTERPRISE', 'Enterprise');
  $enterprise_permissions = array(
    'access klicktipp',
    'klicktipp premium',
    'klicktipp deluxe',
    'klicktipp api reset autoresponder',
    'use whitelabel domain',
    'deactivate gbm',
    'access add contacts',
    'access fullcontact',
    'access campaign builder',
    'klicktipp api',
    'access wufoo',
    'access digistore multi device tracking',
    'access IPN DigiStore24',
    'access IPN AffiliCon',
    'access IPN Clickbank',
    'access IPN PayPal',
    'access sms marketing',
    'access splittest-club',
    'access facebook audience',
    'access youtube content analysis',
    'change subscriber email address',
    'use receiver email',
    'allow re-send confirmation email',
    'use subaccounts',
    'use agency access',
    'use developer key',
    'access conversion pixel',
    'import for customers',
  );
  $role_enterprise = new stdClass();
  $role_enterprise->name = ROLENAME_ENTERPRISE;
  user_role_save($role_enterprise);
  user_role_grant_permissions($role_enterprise->rid, $enterprise_permissions);

  // create free role
  define('ROLENAME_FREE', 'Free');
  $role_free = new stdClass();
  $role_free->name = ROLENAME_FREE;
  user_role_save($role_free);
  // same permissions as ROLENAME_ADVANCED
  user_role_grant_permissions($role_free->rid, $advanced_permissions);

  // create startup role
  define('ROLENAME_STARTUP', 'Startup');
  $role_startup = new stdClass();
  $role_startup->name = ROLENAME_STARTUP;
  user_role_save($role_startup);
  // TODO: once defined by PO change startup permissions
  $startup_permissions = array(
    'access klicktipp',
    'klicktipp premium',
    'access add contacts',
    'access campaign builder',
    'access wufoo',
    'access digistore multi device tracking',
    'access IPN DigiStore24',
    'access IPN AffiliCon',
    'access IPN Clickbank',
    'access IPN PayPal',
    'access sms marketing',
    'access splittest-club',
    'access facebook audience',
    'access youtube content analysis',
    'change subscriber email address',
    'use receiver email',
    'allow re-send confirmation email',
    'use agency access',
  );
  user_role_grant_permissions($role_startup->rid, $startup_permissions);

  // create business role
  define('ROLENAME_BUSINESS', 'Business');
  $role_business = new stdClass();
  $role_business->name = ROLENAME_BUSINESS;
  user_role_save($role_business);
  // same permissions as ROLENAME_ENTERPRISE
  user_role_grant_permissions($role_business->rid, $enterprise_permissions);

  // create first class role
  define('ROLENAME_FIRSTCLASS', 'FirstClass');
  $role_firstclass = new stdClass();
  $role_firstclass->name = ROLENAME_FIRSTCLASS;
  user_role_save($role_firstclass);
  // same permissions as ROLENAME_ENTERPRISE
  user_role_grant_permissions($role_firstclass->rid, $enterprise_permissions);

  // groups should be in sync with klick-tipp.com, so we can use product config from amember
  $groups = array(
    array(
      'UserGroupID' => 1,
      'GroupName' => 'Default User Group',
      'LimitSubscribers' => 0,
      'Data' => '',
      'GroupWeight' => 0,
    ),
    array(
      'UserGroupID' => 6,
      'GroupName' => 'Standard 10.000',
      'LimitSubscribers' => 10000,
      'Data' => serialize(array(
        "LimitImports" => "10",
        "LimitIPNProducts" => "1",
        "LimitWufooForms" => "1",
        "LimitUnsubscriptionMessages" => "3",
        "LimitOutbound" => "1",
        "LimitMarketingTools" => "1",
        "LimitAutomations" => "1",
        "LimitDomains" => 1,
        "LimitLandingPages" => 1,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_STANDARD,
        'TierInAccount' => 0,
        'Role' => $role_basic->rid
      )),
      'GroupWeight' => 10,
    ),
    array(
      'UserGroupID' => 9,
      'GroupName' => 'Standard 20.000',
      'LimitSubscribers' => 20000,
      'Data' => serialize(array(
        "LimitImports" => "20",
        "LimitIPNProducts" => "1",
        "LimitWufooForms" => "1",
        "LimitUnsubscriptionMessages" => "3",
        "LimitOutbound" => "1",
        "LimitMarketingTools" => "1",
        "LimitAutomations" => "1",
        "LimitDomains" => 1,
        "LimitLandingPages" => 1,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_STANDARD,
        'TierInAccount' => 0,
        'Role' => $role_basic->rid
      )),
      'GroupWeight' => 20,
    ),
    array(
      'UserGroupID' => 10,
      'GroupName' => 'Einsteiger 200',
      'LimitSubscribers' => 200,
      'Data' => serialize(array(
        "LimitImports" => "0",
        "LimitIPNProducts" => "0",
        "LimitWufooForms" => "0",
        "LimitUnsubscriptionMessages" => "1",
        "LimitLandingPages" => 0,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_BEGINNER,
        'TierInAccount' => 0,
        'Role' => $role_basic->rid
      )),
      'GroupWeight' => 2,
    ),
    array(
      'UserGroupID' => 11,
      'GroupName' => 'Premium 10.000',
      'LimitSubscribers' => 10000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "3",
        "LimitWufooForms" => "3",
        "LimitUnsubscriptionMessages" => "10",
        "LimitOutbound" => "3",
        "LimitMarketingTools" => "3",
        "LimitAutomations" => "3",
        "LimitDomains" => 2,
        "LimitLandingPages" => 2,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PREMIUM,
        'TierInAccount' => 0,
        'Role' => $role_plus->rid
      )),
      'GroupWeight' => 110,
    ),
    array(
      'UserGroupID' => 13,
      'GroupName' => 'Premium 20.000',
      'LimitSubscribers' => 20000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "3",
        "LimitWufooForms" => "3",
        "LimitUnsubscriptionMessages" => "10",
        "LimitOutbound" => "3",
        "LimitMarketingTools" => "3",
        "LimitAutomations" => "3",
        "LimitDomains" => 2,
        "LimitLandingPages" => 2,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PREMIUM,
        'TierInAccount' => 0,
        'Role' => $role_plus->rid
      )),
      'GroupWeight' => 120,
    ),
    array(
      'UserGroupID' => 16,
      'GroupName' => 'Deluxe 10.000',
      'LimitSubscribers' => 10000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid
      )),
      'GroupWeight' => 1010,
    ),
    array(
      'UserGroupID' => 17,
      'GroupName' => 'Deluxe 20.000',
      'LimitSubscribers' => 20000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1020,
    ),
    array(
      'UserGroupID' => 18,
      'GroupName' => 'Deluxe 30.000',
      'LimitSubscribers' => 30000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1030,
    ),
    array(
      'UserGroupID' => 19,
      'GroupName' => 'Deluxe 50.000',
      'LimitSubscribers' => 50000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1050,
    ),
    array(
      'UserGroupID' => 20,
      'GroupName' => 'Deluxe 100.000',
      'LimitSubscribers' => 100000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1090,
    ),
    array(
      'UserGroupID' => 21,
      'GroupName' => 'Premium 40.000',
      'LimitSubscribers' => 40000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PREMIUM,
        "LimitIPNProducts" => "3",
        "LimitWufooForms" => "3",
        "LimitUnsubscriptionMessages" => "10",
        "LimitOutbound" => "3",
        "LimitMarketingTools" => "3",
        "LimitAutomations" => "3",
        "LimitDomains" => 2,
        "LimitLandingPages" => 2,
        'TierInAccount' => 0,
        'Role' => $role_plus->rid
      )),
      'GroupWeight' => 140,
    ),
    array(
      'UserGroupID' => 22,
      'GroupName' => 'CleverBridge Affiliates',
      'LimitSubscribers' => 0,
      'Data' => serialize(array(
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_AFFILIATE,
      )),
      'GroupWeight' => 0,
    ),
    array(
      'UserGroupID' => 23,
      'GroupName' => 'CB Deluxe 10.000',
      'LimitSubscribers' => 10000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1011,
    ),
    array(
      'UserGroupID' => 24,
      'GroupName' => 'CB Deluxe 25.000',
      'LimitSubscribers' => 25000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1025,
    ),
    array(
      'UserGroupID' => 25,
      'GroupName' => 'CB Deluxe 50.000',
      'LimitSubscribers' => 50000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1050,
    ),
    array(
      'UserGroupID' => 26,
      'GroupName' => 'CB Deluxe 100.000',
      'LimitSubscribers' => 100000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1100,
    ),
    array(
      'UserGroupID' => 27,
      'GroupName' => 'CB Deluxe 250.000',
      'LimitSubscribers' => 250000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1250,
    ),
    array(
      'UserGroupID' => 28,
      'GroupName' => 'CB Deluxe 500.000',
      'LimitSubscribers' => 500000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 1500,
    ),
    array(
      'UserGroupID' => 29,
      'GroupName' => 'CB Deluxe 1.000.000',
      'LimitSubscribers' => 1000000,
      'Data' => serialize(array(
        "LimitImports" => "100",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => 3,
        "LimitLandingPages" => 3,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
        'TierInAccount' => 0,
        'Role' => $role_advanced->rid,
      )),
      'GroupWeight' => 2000,
    ),
    array(
      'UserGroupID' => 30,
      'GroupName' => 'Digistore Affiliates',
      'LimitSubscribers' => 0,
      'Data' => serialize(array(
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_AFFILIATE,
      )),
      'GroupWeight' => 0,
    ),
    array(
      'UserGroupID' => 31,
      'GroupName' => 'Enterpeise 10.000 1 Mailserver',
      'LimitSubscribers' => 10000,
      'Data' => serialize(array(
        "LimitImports" => "9999",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitDomains" => "unlimited",
        "LimitLandingPages" => 5,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_ENTERPRISE,
        'TierInAccount' => 0,
        'Role' => $role_enterprise->rid,
      )),
      'GroupWeight' => 2000,
    ),
    array(
      'UserGroupID' => 32,
      'GroupName' => 'Enterpeise 10.000 2 Mailserver',
      'LimitSubscribers' => 10000,
      'Data' => serialize(array(
        "LimitImports" => "9999",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => "unlimited",
        "LimitLandingPages" => 5,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_ENTERPRISE,
        'TierInAccount' => 0,
        'Role' => $role_enterprise->rid,
      )),
      'GroupWeight' => 2001,
    ),
    array(
      'UserGroupID' => 33,
      'GroupName' => 'Enterpeise 15.000',
      'LimitSubscribers' => 15000,
      'Data' => serialize(array(
        "LimitImports" => "9999",
        "LimitIPNProducts" => "unlimited",
        "LimitWufooForms" => "unlimited",
        "LimitUnsubscriptionMessages" => "unlimited",
        "LimitOutbound" => "unlimited",
        "LimitMarketingTools" => "unlimited",
        "LimitAutomations" => "unlimited",
        "LimitDomains" => "unlimited",
        "LimitLandingPages" => 5,
        "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_ENTERPRISE,
        'TierInAccount' => 0,
        'Role' => $role_enterprise->rid,
      )),
      'GroupWeight' => 2002,
    ),
  );
  $query = db_insert('user_groups')->fields(array(
    'UserGroupID',
    'GroupName',
    'LimitSubscribers',
    'Data',
    'GroupWeight'
  ));
  foreach ($groups as $group) {
    $query->values($group);
  }
  $query->execute();

  $AffiliateGroupid = '22';

  // user_role_save does only work here - not in .profile...
  // create standard group
  $TestBasicGroupid = UserGroups::CreateUserGroup(array(
    'GroupName' => 'Standard 99',
    'LimitSubscribers' => 99,
    'GroupWeight' => 100,
    'Data' => array(
      "LimitImports" => "10",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_STANDARD,
      "LimitIPNProducts" => "1",
      "LimitWufooForms" => "1",
      "LimitUnsubscriptionMessages" => "1",
      "LimitOutbound" => "1",
      "LimitMarketingTools" => "1",
      "LimitAutomations" => "1",
      "LimitDomains" => 1,
      "LimitLandingPages" => 1,
      'TierInAccount' => 0,
      'Role' => $role_basic->rid
    ),
  ));
  // standard group with configurable tier
  $TestNewBasicGroupID = UserGroups::CreateUserGroup([
    'GroupName' => 'Standard',
    'LimitSubscribers' => 0,
    'GroupWeight' => 105,
    'Data' => [
      "LimitImports" => "0",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_STANDARD,
      "LimitIPNProducts" => "1",
      "LimitWufooForms" => "1",
      "LimitUnsubscriptionMessages" => "3",
      "LimitOutbound" => "1",
      "LimitMarketingTools" => "1",
      "LimitAutomations" => "1",
      "LimitDomains" => 1,
      "LimitLandingPages" => 1,
      'TierInAccount' => 1,
      'Role' => $role_basic->rid
    ],
  ]);

  // create premium group
  $TestPlusGroupid = UserGroups::CreateUserGroup(array(
    'GroupName' => 'Premium 99',
    'LimitSubscribers' => 99,
    'GroupWeight' => 200,
    'Data' => array(
      "LimitImports" => "20",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PREMIUM,
      "LimitIPNProducts" => "3",
      "LimitWufooForms" => "3",
      "LimitUnsubscriptionMessages" => "3",
      "LimitOutbound" => "3",
      "LimitMarketingTools" => "3",
      "LimitAutomations" => "3",
      "LimitDomains" => 2,
      "LimitLandingPages" => 2,
      'TierInAccount' => 0,
      'Role' => $role_plus->rid
    ),
  ));
  // standard group with configurable tier
  $TestNewPlusGroupid = UserGroups::CreateUserGroup([
    'GroupName' => 'Premium',
    'LimitSubscribers' => 0,
    'GroupWeight' => 150,
    'Data' => [
      "LimitImports" => "0",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PREMIUM,
      "LimitIPNProducts" => "3",
      "LimitWufooForms" => "3",
      "LimitUnsubscriptionMessages" => "10",
      "LimitOutbound" => "3",
      "LimitMarketingTools" => "3",
      "LimitAutomations" => "3",
      "LimitDomains" => 2,
      "LimitLandingPages" => 2,
      'TierInAccount' => 1,
      'Role' => $role_plus->rid
    ],
  ]);

  // create deluxe group
  $TestAdvancedGroupid = UserGroups::CreateUserGroup(array(
    'GroupName' => 'Platinum 99',
    'LimitSubscribers' => 99,
    'GroupWeight' => 300,
    'Data' => array(
      "LimitImports" => "30",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
      "LimitIPNProducts" => "unlimited",
      "LimitWufooForms" => "unlimited",
      "LimitUnsubscriptionMessages" => "unlimited",
      "LimitOutbound" => "unlimited",
      "LimitMarketingTools" => "unlimited",
      "LimitAutomations" => "unlimited",
      "LimitDomains" => 3,
      "LimitLandingPages" => 3,
      'TierInAccount' => 0,
      'Role' => $role_advanced->rid,
    ),
  ));
  // deluxe group with configurable tier
  $TestNewAdvancedGroupid = UserGroups::CreateUserGroup([
    'GroupName' => 'Deluxe',
    'LimitSubscribers' => 0,
    'GroupWeight' => 1095,
    'Data' => [
      "LimitImports" => "0",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
      "LimitIPNProducts" => "unlimited",
      "LimitWufooForms" => "unlimited",
      "LimitUnsubscriptionMessages" => "unlimited",
      "LimitOutbound" => "unlimited",
      "LimitMarketingTools" => "unlimited",
      "LimitAutomations" => "unlimited",
      "LimitDomains" => 3,
      "LimitLandingPages" => 3,
      'TierInAccount' => 1,
      'Role' => $role_advanced->rid,
    ],
  ]);

  // create standard group
  $TestFreeGroupid = UserGroups::CreateUserGroup(array(
    'GroupName' => 'Free 99',
    'LimitSubscribers' => 99,
    'GroupWeight' => 5,
    'Data' => array(
      "LimitImports" => "30",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_FREE,
      "LimitIPNProducts" => "unlimited",
      "LimitWufooForms" => "unlimited",
      "LimitUnsubscriptionMessages" => "unlimited",
      "LimitOutbound" => "unlimited",
      "LimitMarketingTools" => "unlimited",
      "LimitAutomations" => "unlimited",
      "LimitDomains" => 1,
      "LimitLandingPages" => 1,
      'TierInAccount' => 1,
      'Role' => $role_free->rid
    ),
  ));

  // create free tag in marketing account
  $TagIDFree = Tag::CreateManualTag($user380, 'Klicktipp Free', '', array('OptInSubscribeTo' => array($TagIDKlicktippKunde)));
  $BuildID = APIKey::InsertDB(array(
    'Name' => 'SOI Klicktipp Free',
    'RelOwnerUserID' => $user380,
    'RelListID' => $ListSOI,
    'AssignTagID' => $TagIDFree
  ));
  $marketing['API-Klicktipp-Free'] = Core::EncryptURL(array('UserID' => $user380, 'BuildID' => $BuildID), 'api_key');

  // create free product
  $query = db_insert('amember_products')->fields(array(
    'product_id',
    'title',
    'description',
    'price',
    'data',
  ));
  $query->values([
    'product_id' => '201',
    'title' => 'Free KlickTipp',
    'description' => 'Free KlickTipp',
    'price' => '0',
    'data' => serialize([
      "expire_days" => "21d",
      "is_recurring" => "0",
      "klicktipp_access" => $TestFreeGroupid,
      "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Free'],
    ]),
  ]);
  $query->execute();

  // create startup group
  $TestStartupGroupid = UserGroups::CreateUserGroup(array(
    'GroupName' => 'Startup',
    'LimitSubscribers' => 0,
    'GroupWeight' => 1500,
    'Data' => array(
      "LimitImports" => "0",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_STARTUP,
      "LimitIPNProducts" => "unlimited",
      "LimitWufooForms" => "unlimited",
      "LimitUnsubscriptionMessages" => "unlimited",
      "LimitOutbound" => "unlimited",
      "LimitMarketingTools" => "unlimited",
      "LimitAutomations" => "unlimited",
      "LimitDomains" => 1,
      "LimitLandingPages" => 1,
      'TierInAccount' => 1,
      'Role' => $role_startup->rid

    ),
  ));

  // create enterprise group
  $TestEnterpriseGroupid = UserGroups::CreateUserGroup(array(
    'GroupName' => 'Enterprise 99',
    'LimitSubscribers' => 99,
    'GroupWeight' => 400,
    'Data' => array(
      "LimitImports" => "30",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_ENTERPRISE,
      "LimitIPNProducts" => "unlimited",
      "LimitWufooForms" => "unlimited",
      "LimitUnsubscriptionMessages" => "unlimited",
      "LimitOutbound" => "unlimited",
      "LimitMarketingTools" => "unlimited",
      "LimitAutomations" => "unlimited",
      "LimitDomains" => "unlimited",
      "LimitLandingPages" => 5,
      'TierInAccount' => 0,
      'Role' => $role_enterprise->rid,
    ),
  ));
  // create enterprise group with configurable tier
  $TestNewEnterpriseGroupid = UserGroups::CreateUserGroup([
    'GroupName' => 'Enterprise',
    'LimitSubscribers' => 0,
    'GroupWeight' => 2005,
    'Data' => [
      "LimitImports" => "0",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_ENTERPRISE,
      "LimitIPNProducts" => "unlimited",
      "LimitWufooForms" => "unlimited",
      "LimitUnsubscriptionMessages" => "unlimited",
      "LimitOutbound" => "unlimited",
      "LimitMarketingTools" => "unlimited",
      "LimitAutomations" => "unlimited",
      "LimitDomains" => "unlimited",
      "LimitLandingPages" => 5,
      'TierInAccount' => 1,
      'Role' => $role_enterprise->rid,
    ],
  ]);

  // create business group
  $TestBusinessGroupid = UserGroups::CreateUserGroup(array(
    'GroupName' => 'Business',
    'LimitSubscribers' => 0,
    'GroupWeight' => 3500,
    'Data' => array(
      "LimitImports" => "0",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_BUSINESS,
      "LimitIPNProducts" => "unlimited",
      "LimitWufooForms" => "unlimited",
      "LimitUnsubscriptionMessages" => "unlimited",
      "LimitOutbound" => "unlimited",
      "LimitMarketingTools" => "unlimited",
      "LimitAutomations" => "unlimited",
      "LimitDomains" => 2,
      "LimitLandingPages" => 2,
      'TierInAccount' => 1,
      'Role' => $role_business->rid,
    ),
  ));

  // create first class group
  $TestFirstClassGroupid = UserGroups::CreateUserGroup(array(
    'GroupName' => 'FirstClass',
    'LimitSubscribers' => 0,
    'GroupWeight' => 4500,
    'Data' => array(
      "LimitImports" => "0",
      "GroupCategory" => UserGroups::ACCOUNT_CATEGORY_FIRSTCLASS,
      "LimitIPNProducts" => "unlimited",
      "LimitWufooForms" => "unlimited",
      "LimitUnsubscriptionMessages" => "unlimited",
      "LimitOutbound" => "unlimited",
      "LimitMarketingTools" => "unlimited",
      "LimitAutomations" => "unlimited",
      "LimitDomains" => "unlimited",
      "LimitLandingPages" => 5,
      'TierInAccount' => 1,
      'Role' => $role_firstclass->rid,
    ),
  ));

  // create amember role = sync subscriber account
  define('ROLENAME_AMEMBER', 'Amember');
  $permissions = array(
    'access klicktipp',
    'klicktipp premium',
    'klicktipp api reset autoresponder',
    'use whitelabel domain',
    'access bounce check',
    'omit bounce check',
    'deactivate gbm',
    'omit html validation',
    'import for customers',
    'import for support',
    'access unsubscription urls',
    'access add contacts',
    'access fullcontact',
    'access campaign builder',
    'klicktipp api',
    'access wufoo',
    'access digistore multi device tracking',
    'access IPN DigiStore24',
    'access IPN AffiliCon',
    'access IPN Clickbank',
    'access IPN PayPal',
    'access sms marketing',
    'access splittest-club',
    'access facebook audience',
    'access youtube content analysis',
    'change subscriber email address',
    'use receiver email',
    'allow re-send confirmation email',
    'use subaccounts',
    'use agency access',
    'use developer key',
    'access conversion pixel',
    // admin features
    'administer klicktipp',
    'administer permissions',
    'administer users',
    'access user profiles',
  );
  $role = new stdClass();
  $role->name = ROLENAME_AMEMBER;
  user_role_save($role);
  $rid = $role->rid;
  user_role_grant_permissions($rid, $permissions);

  $AmemberRoleID = $rid;

  // create consultant role
  $role_consultant = new stdClass();
  $role_consultant->name = 'Consultant';
  user_role_save($role_consultant);
  $rid = $role_consultant->rid;
  user_role_grant_permissions($rid, array('access consultant'));
  $ConsultantRoleID = $rid;
  variable_set('klicktipp_consultant_roleid', $rid);
  variable_set('amember_consultant_productids', '147,152');

  // create convention role (no default permissions)
  $role_convention = new stdClass();
  $role_convention->name = 'Convention';
  user_role_save($role_convention);
  $rid = $role_convention->rid;
  variable_set('klicktipp_convention_roleid', $rid);
  variable_set('amember_convention_productids', '146,151');

  // create sales employee role
  define('ROLENAME_SALESEMPLOYEE', 'SalesEmployee');
  $role_salesemployee = new stdClass();
  $role_salesemployee->name = ROLENAME_SALESEMPLOYEE;
  user_role_save($role_salesemployee);
  $rid = $role_salesemployee->rid;
  user_role_grant_permissions($rid, array('use developer key'));

  // affiliates
  user_role_grant_permissions(DRUPAL_AUTHENTICATED_RID, array('access partnerprogramm'));
  user_role_grant_permissions(DRUPAL_AUTHENTICATED_RID, array('access data processing order'));
  user_role_grant_permissions(DRUPAL_AUTHENTICATED_RID, array('view own unpublished content'));

  // User 1
  $account = user_load(1);
  $edit = array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'User1',
    'FirstName' => 'FirstnameUser1',
    'LastName' => 'LastnameUser1',
    'mail' => '<EMAIL>',
  );
  klicktipp_user_insert($edit, $account, 'account');
  user_save($account, $edit);

  // User 2, 3, 4, 5
  define('USERNAME_ADVANCED', 'DeluxeUser');
  user_save(NULL, array(
    'RelUserGroupID' => $TestAdvancedGroupid,
    'name' => USERNAME_ADVANCED,
    'FirstName' => 'FirstnameDeluxe',
    'LastName' => 'LastnameDeluxe',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));
  define('USERNAME_PLUS', 'PremiumUser');
  user_save(NULL, array(
    'RelUserGroupID' => $TestPlusGroupid,
    'name' => USERNAME_PLUS,
    'FirstName' => 'FirstnamePremium',
    'LastName' => 'LastnamePremium',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));
  define('USERNAME_BASIC', 'StandardUser');
  user_save(NULL, array(
    'RelUserGroupID' => $TestBasicGroupid,
    'name' => USERNAME_BASIC,
    'FirstName' => 'FirstnameStandard',
    'LastName' => 'LastnameStandard',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));
  define('USERNAME_ENTERPRISE', 'EnterpriseUser');
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => USERNAME_ENTERPRISE,
    'FirstName' => 'FirstnameEnterprise',
    'LastName' => 'LastnameEnterprise',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  define('USERNAME_FREE', 'FreeUser');
  user_save(NULL, array(
    'RelUserGroupID' => $TestFreeGroupid,
    'name' => USERNAME_FREE,
    'FirstName' => 'FirstnameFree',
    'LastName' => 'LastnameFree',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));
  define('USERNAME_ECO', 'EcoUser');
  user_save(NULL, array(
    'RelUserGroupID' => $TestStartupGroupid,
    'name' => USERNAME_ECO,
    'FirstName' => 'FirstnameEco',
    'LastName' => 'LastnameEco',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));
  define('USERNAME_BUSINESS', 'BusinessUser');
  user_save(NULL, array(
    'RelUserGroupID' => $TestBusinessGroupid,
    'name' => USERNAME_BUSINESS,
    'FirstName' => 'FirstnameBusiness',
    'LastName' => 'LastnameBusiness',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));
  define('USERNAME_FIRSTCLASS', 'FirstClassUser');
  user_save(NULL, array(
    'RelUserGroupID' => $TestFirstClassGroupid,
    'name' => USERNAME_FIRSTCLASS,
    'FirstName' => 'FirstnameFirstClass',
    'LastName' => 'LastnameFirstClass',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // e2e standard user local and k8s
  // Note: UserGroup, Roles, permissions etc will be set by the test

  // e2e-test-dev user
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-dev',
    'FirstName' => 'e2e-test-dev firstname',
    'LastName' => 'e2e-test-dev lastname',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign e2e-test-dev user the enterprise role
  $account = user_load_by_name('e2e-test-dev');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // e2e recording users
  // Note: UserGroup, Roles, permissions etc will be set by the test

  // default user to record e2e test
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-recorder',
    'FirstName' => 'e2e Test Recorder',
    'LastName' => 'default',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign test user enterprise role
  $account = user_load_by_name('e2e-test-recorder');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // e2e recorder replay user for supported browsers (parallel execution)

  for ($u=0; $u<=20; $u++) {

      // one user for each e2e-rr gitlab job -> necessary for running with multiple jobs against same k8s instance
      user_save(NULL, array(
        'RelUserGroupID' => $TestEnterpriseGroupid,
        'name' => "e2e-test-replay-parallel-$u",
        'FirstName' => "e2e Test Replay parallel user $u",
        'LastName' => "parallelUser$u",
        'mail' => "e2e-test-replay-parallel-$<EMAIL>",
        'pass' => 'U4nATY2f'
      ));

      // assign test user enterprise role
      $account = user_load_by_name("e2e-test-replay-parallel-$u");
      $roles = $account->roles;
      $e2eEpRoleID = $role_enterprise->rid;
      $roles[$e2eEpRoleID] = $e2eEpRoleID;
      user_save($account, array('roles' => $roles));
  }

  // chrome
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-replay-chrome',
    'FirstName' => 'e2e Test Recorder',
    'LastName' => 'Chrome',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign test user enterprise role
  $account = user_load_by_name('e2e-test-replay-chrome');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // firefox
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-replay-firefox',
    'FirstName' => 'e2e Test Recorder',
    'LastName' => 'Firefox',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign test user enterprise role
  $account = user_load_by_name('e2e-test-replay-firefox');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // safari ipad
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-replay-safari_ipad',
    'FirstName' => 'e2e Test Recorder',
    'LastName' => 'Safari iPad',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign test user enterprise role
  $account = user_load_by_name('e2e-test-replay-safari_ipad');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // chrome macos
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-replay-chrome_macos',
    'FirstName' => 'e2e Test Recorder',
    'LastName' => 'Chrome MacOS',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign test user enterprise role
  $account = user_load_by_name('e2e-test-replay-chrome_macos');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // firefox windows
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-replay-firefox_win',
    'FirstName' => 'e2e Test Recorder',
    'LastName' => 'Firefox Windows',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign test user enterprise role
  $account = user_load_by_name('e2e-test-replay-firefox_win');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // safari macos
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-replay-safari_macos',
    'FirstName' => 'e2e Test Recorder',
    'LastName' => 'Safari MacOS',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign test user enterprise role
  $account = user_load_by_name('e2e-test-replay-safari_macos');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // edge
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-replay-edge',
    'FirstName' => 'e2e Test Recorder',
    'LastName' => 'Edge',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign test user enterprise role
  $account = user_load_by_name('e2e-test-replay-edge');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // keycloak e2e test user
  user_save(NULL, array(
    'RelUserGroupID' => $TestEnterpriseGroupid,
    'name' => 'e2e-test-keycloak-migration',
    'FirstName' => 'e2e Test keycloak',
    'LastName' => 'default',
    'mail' => '<EMAIL>',
    'pass' => 'U4nATY2f'
  ));

  // assign test user enterprise role
  $account = user_load_by_name('e2e-test-keycloak-migration');
  $roles = $account->roles;
  $e2eEpRoleID = $role_enterprise->rid;
  $roles[$e2eEpRoleID] = $e2eEpRoleID;
  user_save($account, array('roles' => $roles));

  // assign enterprise user consultant role
  $account = user_load_by_name(USERNAME_ENTERPRISE);
  $roles = $account->roles;
  $roles[$ConsultantRoleID] = $ConsultantRoleID;
  user_save($account, array('roles' => $roles));

  // assign marketing user amember role
  $account = user_load($user380);
  $roles = $account->roles;
  $roles[$AmemberRoleID] = $AmemberRoleID;
  user_save($account, array('roles' => $roles));

  // single optin lists and api keys

  $marketing['CF-AmemberID'] = CustomFields::Create(array(
    'RelOwnerUserID' => $user380,
    'FieldName' => 'AmemberID',
    'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
    'FieldOptions' => ''
  ));
  $marketing['CF-Username'] = CustomFields::Create(array(
    'RelOwnerUserID' => $user380,
    'FieldName' => 'Username',
    'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
    'FieldOptions' => ''
  ));
  $marketing['CF-Password'] = CustomFields::Create(array(
    'RelOwnerUserID' => $user380,
    'FieldName' => 'Password',
    'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
    'FieldOptions' => ''
  ));
  $marketing['CF-CleverbridgeID'] = CustomFields::Create(array(
    'RelOwnerUserID' => $user380,
    'FieldName' => 'CleverbridgeID',
    'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
    'FieldOptions' => ''
  ));
  $marketing['CF-Splittestclub-Login-Link'] = CustomFields::Create(array(
    'RelOwnerUserID' => $user380,
    'FieldName' => 'Splittest-Club login Link',
    'FieldTypeEnum' => CustomFields::TYPE_URL,
    'FieldOptions' => ''
  ));
  $marketing['CF-Digistore-Affiliate-Name'] = CustomFields::Create(array(
    'RelOwnerUserID' => $user380,
    'FieldName' => 'Digistore Affiliate Name',
    'FieldTypeEnum' => CustomFields::TYPE_SINGLE,
    'FieldOptions' => ''
  ));

  $TagIDSOI = Tag::CreateManualTag($user380, 'Klicktipp Basic', '', array('OptInSubscribeTo' => array($TagIDKlicktippKunde)));
  $BuildID = APIKey::InsertDB(array(
    'Name' => 'SOI Klicktipp Basic',
    'RelOwnerUserID' => $user380,
    'RelListID' => $ListSOI,
    'AssignTagID' => $TagIDSOI
  ));
  $marketing['API-Klicktipp-Basic'] = Core::EncryptURL(array('UserID' => $user380, 'BuildID' => $BuildID), 'api_key');
  $TagIDSOI = Tag::CreateManualTag($user380, 'Klicktipp Advanced', '', array('OptInSubscribeTo' => array($TagIDKlicktippKunde)));
  $BuildID = APIKey::InsertDB(array(
    'Name' => 'SOI Klicktipp Advanced',
    'RelOwnerUserID' => $user380,
    'RelListID' => $ListSOI,
    'AssignTagID' => $TagIDSOI
  ));
  $marketing['API-Klicktipp-Advanced'] = Core::EncryptURL(array('UserID' => $user380, 'BuildID' => $BuildID), 'api_key');


  $ListSOI = Lists::InsertDB(array(
    'Name' => 'SOI Splittestclub',
    'RelOwnerUserID' => $user380,
    'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE
  ));
  $TagIDSOI = Tag::CreateManualTag($user380, 'Splittestclub Product', '', array('OptInSubscribeTo' => array($TagIDSplittestclubKunde)));
  $BuildID = APIKey::InsertDB(array(
    'Name' => 'SOI Splittestclub',
    'RelOwnerUserID' => $user380,
    'RelListID' => $ListSOI,
    'AssignTagID' => $TagIDSOI
  ));
  $marketing['API-Splittestclub'] = Core::EncryptURL(array('UserID' => $user380, 'BuildID' => $BuildID), 'api_key');

  $ListSOI = Lists::InsertDB(array(
    'Name' => 'SOI Affiliates',
    'RelOwnerUserID' => $user380,
    'OptInModeEnum' => Lists::OPTIN_MODE_SINGLE
  ));
  $TagIDSOI = Tag::CreateManualTag($user380, 'Affiliates', '');
  $BuildID = APIKey::InsertDB(array(
    'Name' => 'SOI Klicktipp Affiliates',
    'RelOwnerUserID' => $user380,
    'RelListID' => $ListSOI,
    'AssignTagID' => $TagIDSOI
  ));
  $marketing['API-Affiliates'] = Core::EncryptURL(array('UserID' => $user380, 'BuildID' => $BuildID), 'api_key');


  return $marketing;
}


function _klicktipp_app_select_themes() {
  $themes = array(
    'theme_default' => 'bootstrapklicktipp',
    'admin_theme' => 'seven',
    'maintenance_theme' => 'seven',
  );
  theme_enable($themes);

  foreach ($themes as $var => $theme) {
    if (!is_numeric($var)) {
      variable_set($var, $theme);
    }
  }

  // Disable D7 default theme
  theme_disable(array('bartik'));

  // admin theme for node forms
  variable_set('node_admin_theme', 1);
}


function _klicktipp_app_formats() {
  // TODO standard formats needed? filtered, full?

  // Add text formats.
  $filtered_html_format = array(
    'format' => 'filtered_html',
    'name' => 'Filtered HTML',
    'weight' => 2,
    'filters' => array(
      // URL filter.
      'filter_url' => array(
        'weight' => 0,
        'status' => 1,
      ),
      // HTML filter.
      'filter_html' => array(
        'weight' => 1,
        'status' => 1,
      ),
      // Line break filter.
      'filter_autop' => array(
        'weight' => 2,
        'status' => 1,
      ),
      // HTML corrector filter.
      'filter_htmlcorrector' => array(
        'weight' => 10,
        'status' => 1,
      ),
    ),
  );
  $filtered_html_format = (object) $filtered_html_format;
  filter_format_save($filtered_html_format);

  $full_html_format = array(
    'format' => 'full_html',
    'name' => 'Full HTML',
    'weight' => 3,
    'filters' => array(
      // URL filter.
      'filter_url' => array(
        'weight' => 0,
        'status' => 1,
      ),
      // Line break filter.
      'filter_autop' => array(
        'weight' => 1,
        'status' => 1,
      ),
      // HTML corrector filter.
      'filter_htmlcorrector' => array(
        'weight' => 10,
        'status' => 1,
      ),
    ),
  );
  $full_html_format = (object) $full_html_format;
  filter_format_save($full_html_format);

  //klicktipp formats
  $klicktipp_format = array(
    'format' => 'klicktipp',
    'name' => 'Klicktipp',
    'weight' => 0,
    'filters' => array(
      'klicktipp' => array(
        'weight' => 11,
        'status' => 1,
      )
    ),
  );
  $klicktipp_format = (object) $klicktipp_format;
  filter_format_save($klicktipp_format);

  // filtered -> all
  $filtered_format_permission = filter_permission_name($filtered_html_format);
  user_role_grant_permissions(DRUPAL_AUTHENTICATED_RID, array($filtered_format_permission));
  user_role_grant_permissions(DRUPAL_ANONYMOUS_RID, array($filtered_format_permission));
}

/*
 * create amember data and configure with klicktipp marketing account 'Amember'
 * fields in $marketing

  'account'
  'pass'
  'CF-AmemberID'
  'CF-Username'
  'CF-Password'
  'CF-CleverbridgeID'
  'CF-Splittestclub-Login-Link'
  'CF-Digistore-Affiliate-Name'
  'Tag-Klicktipp-Kunde'
  'Tag-Klicktipp-Kunde-Ehemalig'
  'Tag-Klicktipp-Newsletter'
  'Tag-Splitestclub-Kunde'
  'Tag-Splitestclub-Kunde-Ehemalig'
  'Tag-Splitestclub-Newsletter'
  'API-Klicktipp-Basic'
  'API-Klicktipp-Advanced'
  'API-Splittestclub'
  'API-Affiliates'

 */
function _klicktipp_app_profile_load_amember_config($marketing) {


  // `amember_config`
  $amember_config = array(
    array(
      'config_id' => '2623',
      'name' => 'root_url',
      'type' => '0',
      'value' => 'http://' . KLICKTIPP_DOMAIN . '/crm',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2624',
      'name' => 'root_surl',
      'type' => '0',
      'value' => 'https://' . KLICKTIPP_DOMAIN . '/crm',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4015',
      'name' => 'admin_email',
      'type' => '0',
      'value' => '<EMAIL>',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2626',
      'name' => 'generate_login',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2627',
      'name' => 'login_min_length',
      'type' => '0',
      'value' => '6',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2628',
      'name' => 'login_max_length',
      'type' => '0',
      'value' => '10',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2631',
      'name' => 'generate_pass',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2632',
      'name' => 'pass_min_length',
      'type' => '0',
      'value' => '6',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2633',
      'name' => 'pass_max_length',
      'type' => '0',
      'value' => '10',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2352',
      'name' => 'clear_access_log',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2353',
      'name' => 'clear_access_log_days',
      'type' => '0',
      'value' => '7',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2355',
      'name' => 'max_ip_count',
      'type' => '0',
      'value' => '5',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2356',
      'name' => 'max_ip_period',
      'type' => '0',
      'value' => '24',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2359',
      'name' => 'select_multiple_products',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2361',
      'name' => 'multi_title',
      'type' => '0',
      'value' => 'Membership',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2638',
      'name' => 'use_coupons',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2367',
      'name' => 'date_format',
      'type' => '0',
      'value' => '%d.%m.%Y',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2368',
      'name' => 'time_format',
      'type' => '0',
      'value' => '%d.%m.%Y %H:%M:%S',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4028',
      'name' => 'send_signup_mail',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4034',
      'name' => 'mail_expire',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '53',
      'name' => 'mail_expire_days',
      'type' => '0',
      'value' => '1',
      'blob_value' => ''
    ),
    array(
      'config_id' => '2635',
      'name' => 'use_address_info',
      'type' => '0',
      'value' => '2',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2634',
      'name' => 'unique_email',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '56',
      'name' => 'curl',
      'type' => '0',
      'value' => '',
      'blob_value' => ''
    ),
    array(
      'config_id' => '2371',
      'name' => 'profile_fields',
      'type' => '1',
      'value' => '',
      'blob_value' => 'a:4:{i:0;s:5:"pass0";i:1;s:6:"name_f";i:2;s:6:"name_l";i:3;s:5:"email";}'
    ),
    /*
    array('config_id' => '983','name' => 'license','type' => '2','value' => '','blob_value' => '===== LICENSE (splittest-club.com, splittest-club.com, valid thru 2099-12-31) =====
  AAA2532B1F2225CA128F9CF468A977334DDFCA2E8E52DC1E93EA0E80D848336BF0FQ
  X9617329B5F67B972CE7F5E175E66BA0BDX323039392D31322D3331P
  C73706C6974746573742D636C75622E636F6DDAE87C01567F50D12D90D7EC512FA41936O
  AFE5599EA6F65FD2DEF87A1F383D2615773706C6974746573742D636C75622E636F6DQ
  ===== ENF OF LICENSE ====='),
    */
    array(
      'config_id' => '3863',
      'name' => 'plugins.payment',
      'type' => '1',
      'value' => '',
      'blob_value' => serialize(array(
        "digistore",
        "cleverbridge",
        "clickbank",
        "clickbank_k",
        "shareit",
        "ktfree",
        /*
        "SU_STMK_Kunden", "SU_STMK_Reseller",
        "free", "offline",
        "paypal_aws", "paypal_k", "paypal_r", "paypal_stc",
        "suebaws17", "suebaws27", "suebaws37", "suebaws47", "suebaws67", "suebplus",
        "vkaws17", "vkaws27", "vkaws37", "vkaws47", "vkaws67",
        */
      ))
    ),
    array(
      'config_id' => '3864',
      'name' => 'plugins.protect',
      'type' => '1',
      'value' => '',
      'blob_value' => serialize(array(
        "klicktipp",
        /*
        "jrox_affiliate",
        "php_include",
        */
      ))
    ),
    array(
      'config_id' => '1325',
      'name' => 'lang.list',
      'type' => '1',
      'value' => '',
      'blob_value' => 'a:1:{i:0;s:10:"de:Deutsch";}'
    ),
    array(
      'config_id' => '2377',
      'name' => 'auto_login_after_signup',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2564',
      'name' => 'db_version',
      'type' => '0',
      'value' => '320',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2375',
      'name' => 'keep_messages_online',
      'type' => '0',
      'value' => '12',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2622',
      'name' => 'site_title',
      'type' => '0',
      'value' => 'Splittest-Club',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2629',
      'name' => 'login_disallow_spaces',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2630',
      'name' => 'login_dont_lowercase',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2636',
      'name' => 'currency',
      'type' => '0',
      'value' => '€',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2639',
      'name' => 'use_affiliates',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2640',
      'name' => 'use_tax',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2350',
      'name' => 'use_cron',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2357',
      'name' => 'max_ip_actions',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2360',
      'name' => 'member_select_multiple_products',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2363',
      'name' => 'use_captcha_signup',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2364',
      'name' => 'manually_approve',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2365',
      'name' => 'product_paysystem',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2366',
      'name' => 'limit_renewals',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2369',
      'name' => 'bruteforce_count',
      'type' => '0',
      'value' => '5',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2370',
      'name' => 'bruteforce_delay',
      'type' => '0',
      'value' => '120',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2372',
      'name' => 'safe_send_pass',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2373',
      'name' => 'dont_check_updates',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2374',
      'name' => 'archive_for_browsing',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2376',
      'name' => 'dont_confirm_guests',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2378',
      'name' => 'hide_password_cp',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2379',
      'name' => 'terms_is_price',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),

    array(
      'config_id' => '2381',
      'name' => 'use_xmlrpc',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2382',
      'name' => 'xmlrpc_login',
      'type' => '0',
      'value' => 'mario',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2383',
      'name' => 'xmlrpc_password',
      'type' => '0',
      'value' => 'Oksana444',
      'blob_value' => NULL
    ),

    array(
      'config_id' => '2384',
      'name' => 'google_analytics',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4007',
      'name' => 'email_method',
      'type' => '0',
      'value' => 'mail',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4008',
      'name' => 'smtp_host',
      'type' => '0',
      'value' => '127.0.0.1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4013',
      'name' => 'sendmail_path',
      'type' => '0',
      'value' => '/usr/sbin/sendmail',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4014',
      'name' => 'test_email',
      'type' => '0',
      'value' => '<EMAIL>',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4016',
      'name' => 'admin_email_from',
      'type' => '0',
      'value' => '<EMAIL>',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4017',
      'name' => 'admin_email_name',
      'type' => '0',
      'value' => 'Klick-Tipp Hilfe',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4020',
      'name' => 'verify_email',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4023',
      'name' => 'send_pending_email',
      'type' => '1',
      'value' => '',
      'blob_value' => 'a:1:{i:0;s:0:"";}'
    ),
    array(
      'config_id' => '4024',
      'name' => 'send_pending_admin',
      'type' => '1',
      'value' => '',
      'blob_value' => 'a:1:{i:0;s:0:"";}'
    ),
    array(
      'config_id' => '4026',
      'name' => 'mail_not_completed',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '1726',
      'name' => 'mail_not_completed_days',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4029',
      'name' => 'send_payment_mail',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4030',
      'name' => 'send_pdf_invoice',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4031',
      'name' => 'invoice_contacts',
      'type' => '0',
      'value' => 'SPLITTEST-CLUB LTD
Office 504, 81 Oxford Street
W1D 2EU
London
United Kingdom',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4032',
      'name' => 'send_payment_admin',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4036',
      'name' => 'mail_cancel_member',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4037',
      'name' => 'mail_cancel_admin',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4038',
      'name' => 'send_pass',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4040',
      'name' => 'mail_autoresponder',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4041',
      'name' => 'autoresponder_renew',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4043',
      'name' => 'cc_rebill_failed',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4044',
      'name' => 'cc_rebill_failed_admin',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4045',
      'name' => 'cc_rebill_success',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4046',
      'name' => 'card_expires',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '1326',
      'name' => 'lang.default',
      'type' => '0',
      'value' => 'de:Deutsch',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '1327',
      'name' => 'lang.display_choice',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3902',
      'name' => 'member_fields',
      'type' => '1',
      'value' => '',
      'blob_value' => 'a:15:{i:0;a:6:{s:4:"name";s:7:"company";s:5:"title";s:11:"Unternehmen";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:11:{s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:2:"50";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:1;a:6:{s:4:"name";s:11:"clickbankid";s:5:"title";s:18:"Clickbank Nickname";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:11:{s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:2:"20";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"1";s:15:"display_profile";s:1:"1";s:24:"display_affiliate_signup";s:1:"1";s:25:"display_affiliate_profile";s:1:"1";}}i:2;a:6:{s:4:"name";s:5:"phone";s:5:"title";s:7:"Telefon";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:2:"20";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:3;a:6:{s:4:"name";s:3:"fax";s:5:"title";s:3:"Fax";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:2:"20";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:4;a:6:{s:4:"name";s:9:"shareitid";s:5:"title";s:21:"Share-It Affiliate ID";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:3:"INT";s:4:"size";s:2:"20";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:5;a:6:{s:4:"name";s:14:"shareitid_club";s:5:"title";s:32:"Shareit Affiliate ID Splittest-C";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:3:"INT";s:4:"size";s:2:"20";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:6;a:6:{s:4:"name";s:7:"website";s:5:"title";s:8:"Webseite";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:2:"60";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:7;a:6:{s:4:"name";s:15:"cleverbridge_id";s:5:"title";s:25:"Cleverbridge Affiliate ID";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:3:"INT";s:4:"size";s:2:"20";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:8;a:6:{s:4:"name";s:19:"cleverbridge_update";s:5:"title";s:26:"CB update subscription URL";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:3:"200";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:9;a:6:{s:4:"name";s:25:"cleverbridge_cancellation";s:5:"title";s:19:"CB cancellation URL";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:3:"200";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:10;a:6:{s:4:"name";s:24:"digistore_affiliate_name";s:5:"title";s:24:"Digistore Affiliate Name";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:2:"32";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:11;a:6:{s:4:"name";s:22:"digistore_cancellation";s:5:"title";s:26:"Digistore Cancellation URL";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:3:"128";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:13;a:6:{s:4:"name";s:17:"digistore_receipt";s:5:"title";s:21:"Digistore receipt URL";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:3:"128";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:14;a:6:{s:4:"name";s:21:"digistore_affiliation";s:5:"title";s:25:"Digistore Affiliation URL";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:3:"128";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}i:15;a:6:{s:4:"name";s:16:"digistore_update";s:5:"title";s:20:"Digistore Update URL";s:4:"type";s:4:"text";s:11:"description";s:0:"";s:13:"validate_func";s:0:"";s:17:"additional_fields";a:12:{s:11:"price_group";a:0:{}s:3:"sql";i:1;s:8:"sql_type";s:12:"VARCHAR(255)";s:4:"size";s:3:"128";s:7:"default";s:0:"";s:7:"options";a:0:{}s:4:"cols";s:2:"20";s:4:"rows";s:1:"5";s:14:"display_signup";s:1:"0";s:15:"display_profile";s:1:"0";s:24:"display_affiliate_signup";s:1:"0";s:25:"display_affiliate_profile";s:1:"0";}}}'
    ),
    array(
      'config_id' => '4009',
      'name' => 'smtp_user',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4010',
      'name' => 'smtp_pass',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4011',
      'name' => 'smtp_port',
      'type' => '0',
      'value' => '10025',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4012',
      'name' => 'smtp_security',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4048',
      'name' => 'disable_unsubscribe_link',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4049',
      'name' => 'copy_admin_email',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4021',
      'name' => 'verify_email_profile',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),

    array(
      'config_id' => '3921',
      'name' => 'aff.payout_methods',
      'type' => '1',
      'value' => '',
      'blob_value' => 'a:1:{i:0;s:6:"paypal";}'
    ),
    array(
      'config_id' => '3922',
      'name' => 'aff.aff_commission',
      'type' => '0',
      'value' => '50%',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3923',
      'name' => 'aff.aff_commission_rec',
      'type' => '0',
      'value' => '50%',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3924',
      'name' => 'aff.aff_commission2',
      'type' => '0',
      'value' => '0',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3925',
      'name' => 'aff.aff_commission_rec2',
      'type' => '0',
      'value' => '0',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3926',
      'name' => 'aff.cookie_lifetime',
      'type' => '0',
      'value' => '365',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3927',
      'name' => 'aff.only_first',
      'type' => '1',
      'value' => '',
      'blob_value' => 's:0:"";'
    ),
    array(
      'config_id' => '3928',
      'name' => 'aff.do_not_pay_for_free_subscriptions',
      'type' => '1',
      'value' => '',
      'blob_value' => 's:0:"";'
    ),
    array(
      'config_id' => '3929',
      'name' => 'aff.signup_type',
      'type' => '1',
      'value' => '',
      'blob_value' => 's:1:"1";'
    ),
    array(
      'config_id' => '3930',
      'name' => 'aff.mail_sale_admin',
      'type' => '1',
      'value' => '',
      'blob_value' => 's:0:"";'
    ),
    array(
      'config_id' => '3931',
      'name' => 'aff.mail_sale_user',
      'type' => '1',
      'value' => '',
      'blob_value' => 's:0:"";'
    ),
    array(
      'config_id' => '3856',
      'name' => 'aff.links',
      'type' => '1',
      'value' => '',
      'blob_value' => 'a:91:{i:0;a:4:{s:3:"url";s:35:"https://www.splittest-club.com/club";s:5:"title";s:25:"Splittest-Club Startseite";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:8;a:4:{s:3:"url";s:26:"https://www.klick-tipp.com";s:5:"title";s:21:"Klick-Tipp Startseite";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:14;a:4:{s:3:"url";s:65:"https://www.splittest-club.com/club/das-ultimative-splittest-tool";s:5:"title";s:29:"Splittest-Club Splittest-Tool";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:15;a:4:{s:3:"url";s:53:"https://www.splittest-club.com/club/was-kunden-denken";s:5:"title";s:28:"Splittest-Club Feedback-Tool";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:16;a:4:{s:3:"url";s:55:"https://www.splittest-club.com/club/exit-traffic-nutzen";s:5:"title";s:33:"Splittest-Club Exit-Lightbox-Tool";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:17;a:4:{s:3:"url";s:76:"https://www.splittest-club.com/club/kaufbarrieren-durch-social-proof-abbauen";s:5:"title";s:40:"Splittest-Club Social-Proof-Counter-Tool";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:18;a:4:{s:3:"url";s:55:"https://www.splittest-club.com/club/one-time-offer-tool";s:5:"title";s:34:"Splittest-Club One-Time-Offer-Tool";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:27;a:4:{s:3:"url";s:35:"https://www.splittest-club.com/club";s:5:"title";s:25:"Splittest-Club Startseite";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:46;a:4:{s:3:"url";s:60:"https://www.klick-tipp.com/rechtliches/datenschutzerklaerung";s:5:"title";s:32:"Klick-Tipp Datenschutzrichtlinie";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:48;a:4:{s:3:"url";s:55:"https://www.klick-tipp.com/rechtliches/anti-spam-policy";s:5:"title";s:27:"Klick-Tipp Anti-Spam-Policy";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:50;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=8g16KdSRGUw";s:5:"title";s:38:"Klick-Tipp YouTube 218.863 Euro Umsatz";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:53;a:4:{s:3:"url";s:36:"https://www.klick-tipp.com/bestellen";s:5:"title";s:31:"Klick-Tipp ist JETZT bestellbar";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:54;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:46:"Webinar // alt: Die 9.456-Euro-E-Mail-Kampagne";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:55;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:90:"Webinar // E-Mail-Marketing No-Gos: Was Sie als E-Mail-Versender *keinesfalls* tun sollten";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:56;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:63:"Webinar // alt: Wie Sie E-Mails schreiben, die geöffnet werden";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:57;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:47:"Webinar // alt: E-Mail-Marketing, aber richtig!";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:58;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:39:"Webinar // alt: Undercover Listbuilding";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:59;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:47:"Webinar // alt: Der Form Builder bei Klick-Tipp";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:60;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:88:"Webinar // alt: Social Proof: Eine *mächtige* Methode zur psychologischen Beeinflussung";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:63;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:69:"Webinar // alt: Matthias Brandmüller redet Tacheles über Klick-Tipp";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:64;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:50:"Webinar // alt: Ihre ersten Schritte in Klick-Tipp";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:65;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:53:"Webinar // alt: +58% Besucher durch User-Whitelisting";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:66;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:65:"Webinar // alt: Warum Sie E-Mails *nicht* personalisieren sollten";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:67;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:67:"Webinar // alt: Splittest-Ergebnisse: Minimalistische Squeeze Pages";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:68;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:76:"Webinar // alt: Die *perfekte* Squeeze Page (Interview mit Thomas Klußmann)";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:69;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:66:"Webinar // alt: Wichtige Verbesserungen im Double-Opt-in-Verfahren";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:70;a:4:{s:3:"url";s:73:"https://www.klick-tipp.com/anti-spam/kein-import-%C3%BCber-user-interface";s:5:"title";s:34:"Klick-Tipp Anti-Spam Import-Policy";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:71;a:4:{s:3:"url";s:60:"https://www.klick-tipp.com/anti-spam/double-opt-in-verfahren";s:5:"title";s:69:"Klick-Tipp Anti-Spam Vorteile im Double-opt-in-Prozess bei Klick-Tipp";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:72;a:4:{s:3:"url";s:75:"https://www.klick-tipp.com/anti-spam/eintragen-ist-einfach-austragen-ebenso";s:5:"title";s:63:"Klick-Tipp Anti-Spam Eintragen ist einfach – austragen ebenso";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:73;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:68:"Webinar // alt: Erfolgsfaktoren für profitables Affiliate-Marketing";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:74;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:90:"Webinar // alt: Live-Versand eines Newsletters (Affiliate-Promotion für Thomas Klußmann)";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:75;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:45:"Webinar // alt: Wie Sie Ihre Liste verdoppeln";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:76;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:73:"Webinar // alt: Wie Sie mit Klick-Tipp einen Splittest-Newsletter anlegen";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:77;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:45:"Webinar // alt: Newsletter an Unopens-Segment";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:78;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:50:"Webinar // alt: Newsletter-Performance vergleichen";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:79;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:66:"Webinar // alt: Auf einen Blick: Erfolgsstatistiken bei Klick-Tipp";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:80;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:92:"Webinar // alt: Splittest-Ergebnisse: Führen eingebettete Bilder in E-Mails zu mehr Klicks?";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:81;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:62:"Webinar // alt: +462% Besucher durch animierte GIFs in E-Mails";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:82;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:79:"Webinar // alt: Splittest-Ergebnisse: Führt „RE:" im Betreff zu mehr Klicks?";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:83;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:85:"Webinar // alt: Splittest-Ergebnisse: Ist ein Probemonat bei Abo-Produkten notwendig?";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:84;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:42:"Webinar // alt: Der T-Online-Benchmarktest";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:85;a:4:{s:3:"url";s:63:"https://www.klick-tipp.com/anti-spam/globales-bounce-management";s:5:"title";s:47:"Klick-Tipp Anti-Spam Globales Bounce-Management";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:86;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:66:"Webinar // alt: Wie Sie mit Klick-Tipp einen E-Mail-Kurs erstellen";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:87;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:39:"Webinar // alt: Follow-up-Autoresponder";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:88;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:50:"Webinar // alt: Kundenliste und Interessentenliste";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:89;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:78:"Webinar // alt: Wie Sie mit Klick-Tipp eine Globale Interessentenliste anlegen";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:90;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:65:"Webinar // alt: Wie Sie Klick-Tipp mit Ihren Skripten integrieren";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:91;a:4:{s:3:"url";s:61:"https://www.klick-tipp.com/anti-spam/e-mail-authentifizierung";s:5:"title";s:35:"Klick-Tipp E-Mail-Authentifizierung";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:92;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:62:"Webinar // alt: Listbuilding und Community-Building mit Drupal";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:93;a:4:{s:3:"url";s:60:"https://www.klick-tipp.com/rechtliches/datenschutzerklaerung";s:5:"title";s:32:"Klick-Tipp Datenschutzerklärung";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:94;a:4:{s:3:"url";s:55:"https://www.klick-tipp.com/rechtliches/anti-spam-policy";s:5:"title";s:27:"Klick-Tipp Anti-Spam-Policy";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:96;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:46:"Webinar // alt: Facebook-Listbuilding-Maschine";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:97;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:41:"Webinar // alt: Listbuilding mit Facebook";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:98;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:46:"Webinar // alt: Currywurst und Torte in Berlin";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:99;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:69:"Webinar // alt: Vor Wegwerf-E-Mail-Adressen und Spam-Fallen schützen";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:100;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:85:"Webinar // alt: Hard Bounces sowie unbestätigte und ausgetragene Empfänger löschen";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:101;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:111:"Webinar // alt: Exklusives Interview mit Matthias Brandmüller für die Mitglieder des Social Media Elite Clubs";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:102;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:58:"Webinar // alt: E-Mail-Liste mit Blog-Kommentaren aufbauen";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:103;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:78:"Webinar // alt: Wie Sie Klick-Tipp mit dem EvergreenBusinessSystem integrieren";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:104;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:94:"Webinar // alt: Wie Erico aus Brasilien seinen E-Mail-Verteiler kostenlos mit Facebook aufbaut";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:106;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:55:"Webinar // alt: Eintragungsraten und Kaufraten steigern";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:107;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:42:"Webinar // alt: Commitment and Consistency";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:108;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:80:"Webinar // alt: Die drei effektivsten Methoden der psychologischen Beeinflussung";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:109;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:54:"Webinar // alt: Marketing-Tools aus dem Splittest-Club";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:110;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:60:"Webinar // alt: Reseller-Rechte auf den Splittest-Masterkurs";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:111;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:62:"Webinar // alt: Mein erstes Internet-Marketing-Video ... EVER!";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:112;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:35:"Webinar // alt: Unopens reanimieren";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:113;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:55:"Webinar // alt: Japanisches Testimonial für Klick-Tipp";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:114;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:74:"Webinar // alt: 44.838 Euro verschenkt - machen Sie nicht denselben Fehler";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:115;a:4:{s:3:"url";s:26:"https://www.klick-tipp.com";s:5:"title";s:21:"Klick-Tipp Startseite";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:116;a:4:{s:3:"url";s:43:"https://www.splittest-club.com/club/webinar";s:5:"title";s:22:"Splittest-Club Webinar";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:117;a:4:{s:3:"url";s:34:"https://www.klick-tipp.com/webinar";s:5:"title";s:18:"Klick-Tipp Webinar";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:119;a:4:{s:3:"url";s:64:"https://www.klick-tipp.com/handbuch/facebook-listbuilding-button";s:5:"title";s:48:"Klick-Tipp Handbuch Facebook Listbuilding Button";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:120;a:4:{s:3:"url";s:59:"https://www.klick-tipp.com/handbuch/splittesting-smart-tags";s:5:"title";s:56:"Klick-Tipp Handbuch Wie Sie Ihre E-Mail-Liste verdoppeln";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:121;a:4:{s:3:"url";s:63:"https://www.klick-tipp.com/handbuch/whitelisting-zustellbarkeit";s:5:"title";s:32:"Klick-Tipp Handbuch Whitelisting";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:122;a:4:{s:3:"url";s:35:"https://www.klick-tipp.com/handbuch";s:5:"title";s:19:"Klick-Tipp Handbuch";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:123;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=g220ePcJoT4";s:5:"title";s:47:"Splittest-Club YouTube 44 838 Euro verloren …";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:124;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=uMz-DK7jtTo";s:5:"title";s:57:"Splittest-Club YouTube Höherer Preis – mehr Verkäufe?";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:125;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=ZB0YD7aTp2k";s:5:"title";s:51:"Splittest-Club YouTube Probemonat bei Abo-Produkten";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:126;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=v0WDpnSPijM";s:5:"title";s:51:"Splittest-Club YouTube Psychologische Beeinflussung";s:3:"pid";s:1:"1";s:6:"action";s:9:"edit_link";}i:127;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=8g16KdSRGUw";s:5:"title";s:61:"Klick-Tipp YouTube 218 865 Euro Umsatz durch E-Mail-Marketing";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:128;a:4:{s:3:"url";s:64:"https://www.klick-tipp.com/handbuch/facebook-listbuilding-button";s:5:"title";s:54:"Klick-Tipp Handbuch E-Mail-Liste mit Facebook aufbauen";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:129;a:4:{s:3:"url";s:59:"https://www.klick-tipp.com/handbuch/splittesting-smart-tags";s:5:"title";s:56:"Klick-Tipp Handbuch Wie Sie Ihre E-Mail-Liste verdoppeln";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:130;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=AB_5MLlFMhI";s:5:"title";s:51:"Klick-Tipp YouTube E-Mail Marketing im Weißen Haus";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:131;a:4:{s:3:"url";s:63:"https://www.klick-tipp.com/handbuch/whitelisting-zustellbarkeit";s:5:"title";s:66:"Klick-Tipp Handbuch 58 Prozent mehr Umsatz durch User-Whitelisting";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:132;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=dzs5S-ugz5E";s:5:"title";s:66:"Klick-Tipp YouTube 140 Prozent mehr Klicks durch Bilder in E-Mails";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:133;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=_fvgX4SKAv8";s:5:"title";s:65:"Klick-Tipp YouTube 462 Prozent mehr Besucher durch animierte GIFs";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:134;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=j18ZtRlfDMQ";s:5:"title";s:66:"Klick-Tipp YouTube Sechs Tipps für erfolgreiches E-Mail-Marketing";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:135;a:4:{s:3:"url";s:43:"https://www.youtube.com/watch?v=hT8m0ON0-ko";s:5:"title";s:66:"Klick-Tipp YouTube Warum Sie E-Mails nicht personalisieren sollten";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:136;a:4:{s:3:"url";s:56:"https://www.klick-tipp.com/handbuch/newsletter-erstellen";s:5:"title";s:40:"Klick-Tipp Handbuch Newsletter erstellen";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}i:137;a:4:{s:3:"url";s:40:"https://www.klick-tipp.com/kundenstimmen";s:5:"title";s:28:"Klick-Tipp Testimonial-Seite";s:3:"pid";s:1:"8";s:6:"action";s:9:"edit_link";}}'
    ),
    array(
      'config_id' => '3932',
      'name' => 'aff.mail_signup_user',
      'type' => '1',
      'value' => '',
      'blob_value' => 's:0:"";'
    ),

    //// protection plugins

    array(
      'config_id' => '4120',
      'name' => 'protect.klicktipp.api_url',
      'type' => '0',
      'value' => 'https://dev:ovi8eTei@' . KLICKTIPP_DOMAIN . '/api',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4121',
      'name' => 'protect.klicktipp.admin_username',
      'type' => '0',
      'value' => $marketing['account']->name,
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4122',
      'name' => 'protect.klicktipp.admin_pass',
      'type' => '0',
      'value' => $marketing['pass'],
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4130',
      'name' => 'protect.klicktipp.field_splittest_club_login_link',
      'type' => '0',
      'value' => $marketing['CF-Splittestclub-Login-Link'],
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4126',
      'name' => 'protect.klicktipp.field_password',
      'type' => '0',
      'value' => $marketing['CF-Password'],
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4127',
      'name' => 'protect.klicktipp.field_cleverbridge_id',
      'type' => '0',
      'value' => $marketing['CF-CleverbridgeID'],
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4129',
      'name' => 'protect.klicktipp.field_gotophp_default_redirect',
      'type' => '0',
      'value' => 'https://' . KLICKTIPP_DOMAIN . '/webinar',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4128',
      'name' => 'protect.klicktipp.field_digistore_affiliate_name',
      'type' => '0',
      'value' => $marketing['CF-Digistore-Affiliate-Name'],
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4123',
      'name' => 'protect.klicktipp.former_customer_tag',
      'type' => '0',
      'value' => $marketing['Tag-Klicktipp-Kunde-Ehemalig'],
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4124',
      'name' => 'protect.klicktipp.field_amember_id',
      'type' => '0',
      'value' => $marketing['CF-AmemberID'],
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4125',
      'name' => 'protect.klicktipp.field_username',
      'type' => '0',
      'value' => $marketing['CF-Username'],
      'blob_value' => NULL
    ),

    /* jrox
    array('config_id' => '501','name' => 'protect.jrox_affiliate.jam_url','type' => '0','value' => 'http://cashmaschine.com/aff','blob_value' => NULL),
    array('config_id' => '502','name' => 'protect.jrox_affiliate.refund_secret','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '503','name' => 'protect.jrox_affiliate.signup_secret','type' => '0','value' => 'secret','blob_value' => NULL),
  */

    //// payment plugins

    array(
      'config_id' => '3913',
      'name' => 'payment.digistore.allow_create',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3914',
      'name' => 'payment.digistore.title',
      'type' => '0',
      'value' => 'Credit Card',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3915',
      'name' => 'payment.digistore.description',
      'type' => '0',
      'value' => 'secure credit card payment',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3916',
      'name' => 'payment.digistore.disable_postback_log',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3917',
      'name' => 'payment.digistore.passphrase',
      'type' => '0',
      'value' => DigiStore::IPN_PASSPHRASE, //'SVuYmcMLHMlf2a5vYYeb',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3918',
      'name' => 'payment.digistore.affiliate_product',
      'type' => '0',
      'value' => '137',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3919',
      'name' => 'payment.digistore.afflink_affiliate_product',
      'type' => '0',
      'value' => '38219',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3920',
      'name' => 'payment.digistore.afflink_redirect_password',
      'type' => '0',
      'value' => 'eqqVtt8CFD32oXPLxBhI',
      'blob_value' => NULL
    ),

    // cleverbridge

    array(
      'config_id' => '3586',
      'name' => 'payment.cleverbridge.allow_create',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3587',
      'name' => 'payment.cleverbridge.title',
      'type' => '0',
      'value' => 'Cleverbridge',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3588',
      'name' => 'payment.cleverbridge.description',
      'type' => '0',
      'value' => 'Cleverbridge payments',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3589',
      'name' => 'payment.cleverbridge.disable_postback_log',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3590',
      'name' => 'payment.cleverbridge.account',
      'type' => '0',
      'value' => '1080',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3591',
      'name' => 'payment.cleverbridge.affiliate_product',
      'type' => '0',
      'value' => '67',
      'blob_value' => NULL
    ),

    // clickbank

    array(
      'config_id' => '2526',
      'name' => 'payment.clickbank.account',
      'type' => '0',
      'value' => 'stclub',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2527',
      'name' => 'payment.clickbank.secret',
      'type' => '0',
      'value' => '****************',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2528',
      'name' => 'payment.clickbank.developer_key',
      'type' => '0',
      'value' => 'DEV-163CB98EA579E8E00DC7329D680584022710',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2529',
      'name' => 'payment.clickbank.clerk_user_key',
      'type' => '0',
      'value' => 'API-E5B70253D6681695DE812AC162056EBCA8C7',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2530',
      'name' => 'payment.clickbank.allow_create',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2531',
      'name' => 'payment.clickbank.title',
      'type' => '0',
      'value' => 'Clickbank',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2532',
      'name' => 'payment.clickbank.description',
      'type' => '0',
      'value' => 'Credit Card Payment',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2552',
      'name' => 'payment.clickbank_k.account',
      'type' => '0',
      'value' => 'stmkurs',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2553',
      'name' => 'payment.clickbank_k.secret',
      'type' => '0',
      'value' => 'LKJL3K4JLWKEJOWI',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2554',
      'name' => 'payment.clickbank_k.allow_create',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2555',
      'name' => 'payment.clickbank_k.title',
      'type' => '0',
      'value' => 'Clickbank',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2556',
      'name' => 'payment.clickbank_k.description',
      'type' => '0',
      'value' => 'Vgl. %swww.clickbank.com%s f&uuml;r Beschreibung',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '2557',
      'name' => 'payment.clickbank_k.disable_postback_log',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),

    array(
      'config_id' => '3048',
      'name' => 'payment.shareit.allow_create',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3049',
      'name' => 'payment.shareit.title',
      'type' => '0',
      'value' => 'Share-It!',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3050',
      'name' => 'payment.shareit.description',
      'type' => '0',
      'value' => 'Share-It!',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '3051',
      'name' => 'payment.shareit.disable_postback_log',
      'type' => '0',
      'value' => '',
      'blob_value' => NULL
    ),

    array(
      'config_id' => '4113',
      'name' => 'payment.ktfree.allow_create',
      'type' => '0',
      'value' => '1',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4114',
      'name' => 'payment.ktfree.title',
      'type' => '0',
      'value' => 'KlickTipp Free',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4115',
      'name' => 'payment.ktfree.description',
      'type' => '0',
      'value' => 'KlickTipp Free',
      'blob_value' => NULL
    ),
    array(
      'config_id' => '4116',
      'name' => 'payment.ktfree.productid',
      'type' => '0',
      'value' => '201',
      'blob_value' => NULL
    ),

    /* paypal
      array('config_id' => '3064','name' => 'payment.paypal_r.business','type' => '0','value' => '<EMAIL>','blob_value' => NULL),
      array('config_id' => '3065','name' => 'payment.paypal_r.alt_business','type' => '0','value' => '','blob_value' => NULL),
      array('config_id' => '3066','name' => 'payment.paypal_r.testing','type' => '0','value' => '','blob_value' => NULL),
      array('config_id' => '3068','name' => 'payment.paypal_r.dont_verify','type' => '0','value' => '','blob_value' => NULL),
      array('config_id' => '3069','name' => 'payment.paypal_r.lc','type' => '0','value' => 'DE','blob_value' => NULL),
      array('config_id' => '3071','name' => 'payment.paypal_r.title','type' => '0','value' => 'PayPal','blob_value' => NULL),
      array('config_id' => '3072','name' => 'payment.paypal_r.description','type' => '0','value' => 'Sichere Kreditkartenzahlung','blob_value' => NULL),
      array('config_id' => '3073','name' => 'payment.paypal_r.disable_postback_log','type' => '0','value' => '','blob_value' => NULL),
      array('config_id' => '3074','name' => 'payment.paypal_r.rewrite_email','type' => '0','value' => '1','blob_value' => NULL),
      array('config_id' => '3075','name' => 'payment.paypal_r.resend_postback','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '3067','name' => 'payment.paypal_r.other_account','type' => '0','value' => '0','blob_value' => NULL),
    array('config_id' => '3070','name' => 'payment.paypal_r.allow_create','type' => '0','value' => '0','blob_value' => NULL),

    array('config_id' => '1429','name' => 'payment.paypal_aws.business','type' => '0','value' => '<EMAIL>','blob_value' => NULL),
    array('config_id' => '1430','name' => 'payment.paypal_aws.alt_business','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '1431','name' => 'payment.paypal_aws.testing','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '1432','name' => 'payment.paypal_aws.dont_verify','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '1433','name' => 'payment.paypal_aws.lc','type' => '0','value' => 'DE','blob_value' => NULL),
    array('config_id' => '1434','name' => 'payment.paypal_aws.title','type' => '0','value' => 'PayPal','blob_value' => NULL),
    array('config_id' => '1435','name' => 'payment.paypal_aws.description','type' => '0','value' => '(Auslieferung sofort)','blob_value' => NULL),
    array('config_id' => '1436','name' => 'payment.paypal_aws.disable_postback_log','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '1437','name' => 'payment.paypal_aws.rewrite_email','type' => '0','value' => '1','blob_value' => NULL),
    array('config_id' => '1438','name' => 'payment.paypal_aws.resend_postback','type' => '0','value' => '','blob_value' => NULL),

    array('config_id' => '2501','name' => 'payment.paypal_k.business','type' => '0','value' => '<EMAIL>','blob_value' => NULL),
    array('config_id' => '2502','name' => 'payment.paypal_k.alt_business','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '2503','name' => 'payment.paypal_k.testing','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '2504','name' => 'payment.paypal_k.dont_verify','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '2505','name' => 'payment.paypal_k.lc','type' => '0','value' => 'DE','blob_value' => NULL),
    array('config_id' => '2506','name' => 'payment.paypal_k.title','type' => '0','value' => 'PayPal','blob_value' => NULL),
    array('config_id' => '2507','name' => 'payment.paypal_k.description','type' => '0','value' => 'Sichere Kreditkartenzahlung','blob_value' => NULL),
    array('config_id' => '2508','name' => 'payment.paypal_k.disable_postback_log','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '2509','name' => 'payment.paypal_k.rewrite_email','type' => '0','value' => '1','blob_value' => NULL),
    array('config_id' => '2510','name' => 'payment.paypal_k.resend_postback','type' => '0','value' => '','blob_value' => NULL),

    array('config_id' => '3052','name' => 'payment.paypal_stc.business','type' => '0','value' => '<EMAIL>','blob_value' => NULL),
    array('config_id' => '3053','name' => 'payment.paypal_stc.alt_business','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '3054','name' => 'payment.paypal_stc.testing','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '3055','name' => 'payment.paypal_stc.other_account','type' => '0','value' => '0','blob_value' => NULL),
    array('config_id' => '3056','name' => 'payment.paypal_stc.dont_verify','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '3057','name' => 'payment.paypal_stc.lc','type' => '0','value' => 'DE','blob_value' => NULL),
    array('config_id' => '3058','name' => 'payment.paypal_stc.allow_create','type' => '0','value' => '0','blob_value' => NULL),
    array('config_id' => '3059','name' => 'payment.paypal_stc.title','type' => '0','value' => 'PayPal STC','blob_value' => NULL),
    array('config_id' => '3060','name' => 'payment.paypal_stc.description','type' => '0','value' => 'Sichere Kreditkartenzahlung','blob_value' => NULL),
    array('config_id' => '3061','name' => 'payment.paypal_stc.disable_postback_log','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '3062','name' => 'payment.paypal_stc.rewrite_email','type' => '0','value' => '1','blob_value' => NULL),
    array('config_id' => '3063','name' => 'payment.paypal_stc.resend_postback','type' => '0','value' => '','blob_value' => NULL),
    */

    /* sueb

    array('config_id' => '2413','name' => 'payment.SU_STMK_Reseller.title','type' => '0','value' => 'Sofortüberweisung Reseller Splittest-Masterkurs','blob_value' => NULL),
    array('config_id' => '2414','name' => 'payment.SU_STMK_Reseller.description','type' => '0','value' => 'Sofortüberweisung Reseller Splittest-Masterkurs','blob_value' => NULL),
    array('config_id' => '2415','name' => 'payment.SU_STMK_Kunden.title','type' => '0','value' => 'Sofortüberweisung Kunden Splittest-Masterkurs','blob_value' => NULL),
    array('config_id' => '2416','name' => 'payment.SU_STMK_Kunden.description','type' => '0','value' => 'Sofortüberweisung Kunden Splittest-Masterkurs','blob_value' => NULL),

    array('config_id' => '990','name' => 'payment.sueb.title','type' => '0','value' => 'Sofortueberweisung','blob_value' => NULL),
    array('config_id' => '991','name' => 'payment.sueb.description','type' => '0','value' => 'Sichere Banküberweisung','blob_value' => NULL),

    array('config_id' => '2791','name' => 'payment.suebplus.account','type' => '0','value' => '17689','blob_value' => NULL),
    array('config_id' => '2792','name' => 'payment.suebplus.project','type' => '0','value' => '79193','blob_value' => NULL),
    array('config_id' => '2793','name' => 'payment.suebplus.project_pass','type' => '0','value' => 'Oksana444','blob_value' => NULL),
    array('config_id' => '2794','name' => 'payment.suebplus.project_currency','type' => '0','value' => 'EUR','blob_value' => NULL),
    array('config_id' => '2795','name' => 'payment.suebplus.title','type' => '0','value' => 'Sofortueberweisung','blob_value' => NULL),
    array('config_id' => '2796','name' => 'payment.suebplus.description','type' => '0','value' => 'Sofortueberweisung','blob_value' => NULL),
    array('config_id' => '2797','name' => 'payment.suebplus.disable_postback_log','type' => '0','value' => '','blob_value' => NULL),

    array('config_id' => '1254','name' => 'payment.suebaws17.title','type' => '0','value' => 'Sofortüberweisung','blob_value' => NULL),
    array('config_id' => '1255','name' => 'payment.suebaws17.description','type' => '0','value' => '(Auslieferung sofort)','blob_value' => NULL),
    array('config_id' => '1256','name' => 'payment.suebaws27.title','type' => '0','value' => 'Sofortüberweisung','blob_value' => NULL),
    array('config_id' => '1257','name' => 'payment.suebaws27.description','type' => '0','value' => '(Auslieferung sofort)','blob_value' => NULL),
    array('config_id' => '1258','name' => 'payment.suebaws37.title','type' => '0','value' => 'Sofortüberweisung','blob_value' => NULL),
    array('config_id' => '1259','name' => 'payment.suebaws37.description','type' => '0','value' => '(Auslieferung sofort)','blob_value' => NULL),
    array('config_id' => '1260','name' => 'payment.suebaws47.title','type' => '0','value' => 'Sofortüberweisung','blob_value' => NULL),
    array('config_id' => '1261','name' => 'payment.suebaws47.description','type' => '0','value' => '(Auslieferung sofort)','blob_value' => NULL),
    array('config_id' => '1262','name' => 'payment.suebaws67.title','type' => '0','value' => 'Sofortüberweisung','blob_value' => NULL),
    array('config_id' => '1263','name' => 'payment.suebaws67.description','type' => '0','value' => '(Auslieferung sofort)','blob_value' => NULL),

    */

    /* vka
    array('config_id' => '1264','name' => 'payment.vkaws17.title','type' => '0','value' => 'Vorkasse','blob_value' => NULL),
    array('config_id' => '1265','name' => 'payment.vkaws17.description','type' => '0','value' => '(Auslieferung in 3-5 Tagen)','blob_value' => NULL),
    array('config_id' => '1266','name' => 'payment.vkaws27.title','type' => '0','value' => 'Vorkasse','blob_value' => NULL),
    array('config_id' => '1267','name' => 'payment.vkaws27.description','type' => '0','value' => '(Auslieferung in 3-5 Tagen)','blob_value' => NULL),
    array('config_id' => '1268','name' => 'payment.vkaws37.title','type' => '0','value' => 'Vorkasse','blob_value' => NULL),
    array('config_id' => '1269','name' => 'payment.vkaws37.description','type' => '0','value' => '(Auslieferung in 3-5 Tagen)','blob_value' => NULL),
    array('config_id' => '1270','name' => 'payment.vkaws47.title','type' => '0','value' => 'Vorkasse','blob_value' => NULL),
    array('config_id' => '1271','name' => 'payment.vkaws47.description','type' => '0','value' => '(Auslieferung in 3-5 Tagen)','blob_value' => NULL),
    array('config_id' => '1272','name' => 'payment.vkaws67.title','type' => '0','value' => 'Vorkasse','blob_value' => NULL),
    array('config_id' => '1273','name' => 'payment.vkaws67.description','type' => '0','value' => '(Auslieferung in 3-5 Tagen)','blob_value' => NULL),

    */

    /*
    array('config_id' => '573','name' => 'payment.offline.title','type' => '0','value' => 'Sofortabo','blob_value' => NULL),
    array('config_id' => '574','name' => 'payment.offline.description','type' => '0','value' => 'Sichere Bank&uuml;berweisung','blob_value' => NULL),

    array('config_id' => '1505','name' => 'payment.free.admin_approval','type' => '0','value' => '','blob_value' => NULL),
    array('config_id' => '1506','name' => 'payment.free.mail_admin','type' => '0','value' => '1','blob_value' => NULL),
  */

  );

  $query = db_insert('amember_config')->fields(array(
    'config_id',
    'name',
    'type',
    'value',
    'blob_value',
  ));
  foreach ($amember_config as $record) {
    $query->values($record);
  }
  $query->execute();

  // `amember_products`

  $products_default = array(
    "expire_days" => "",
    "trial1_price" => "",
    "trial1_days" => "",
    "is_recurring" => "",
    "start_date" => "",
    "terms" => "",
    "rebill_times" => "",
    "paypal_currency" => "EUR",
    "url" => "",
    "add_urls" => "",
    "thankyouurl" => "",
    "scope" => "",
    "order" => "",
    "price_group" => "",
    "renewal_group" => "",
    "need_agreement" => "",
    "require_other" => "",
    "prevent_if_other" => "",
    "autoresponder_renew" => "",
    "dont_mail_expire" => "",
    "drupal_access" => "",
    "klicktipp_access" => "",
    "klicktipp_sync_apikey" => "",
    "cleverbridge_id" => "",
    "clickbank_id" => "",
    "clickbank_cbskin" => "",
    "clickbank_k_id" => "",
    "clickbank_k_cbskin" => "",
    "shareit_account" => "",
    "shareit_id" => "",
    "shareit_publisher" => "",
    "additional_subscriptions" => "",
    "aff_commission" => "",
    "aff_commission_rec" => "",
    "aff_commission2" => "",
    "aff_commission_rec2" => "",
  );

  $amember_products = array(
    /*
    array(
      'product_id' => '1',
      'title' => 'Splittest-Club #21',
      'description' => '',
      'price' => '37.00',
      'data' => 'a:39:{s:11:"expire_days";s:2:"1m";s:12:"trial1_price";s:4:"1.00";s:11:"trial1_days";s:2:"1m";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:66:"In der einmonatigen Testphase kostenlos, danach 37 Euro pro Monat.";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"10";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncy1hay1iNmE5LTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:2:"21";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:13:"splittestclub";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:3:"40%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '6',
      'title' => 'Video-Lehrgang Ihre anonyme Website',
      'description' => '',
      'price' => '42.70',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:49:"einmalig 42,70 Euro, zeitlich unbegrenzter Zugang";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:6:"-18721";s:13:"renewal_group";s:1:"6";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"5";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '2',
      'title' => 'Video-Lehrgang Ihre anonyme Website',
      'description' => '',
      'price' => '37.00',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:46:"einmalig 37 Euro, zeitlich unbegrenzter Zugang";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:6:"-76121";s:13:"renewal_group";s:1:"3";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"5";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '3',
      'title' => 'Video-Lehrgang Ihre anonyme Website',
      'description' => '',
      'price' => '27.00',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:46:"einmalig 27 Euro, zeitlich unbegrenzter Zugang";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:6:"-29181";s:13:"renewal_group";s:1:"3";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"5";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '4',
      'title' => 'Video-Lehrgang Ihre anonyme Website',
      'description' => '',
      'price' => '22.70',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:49:"einmalig 22,70 Euro, zeitlich unbegrenzter Zugang";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:6:"-28371";s:13:"renewal_group";s:1:"4";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"5";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '5',
      'title' => 'Video-Lehrgang Ihre anonyme Website Share-It!',
      'description' => '',
      'price' => '32.70',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:49:"einmalig 32,70 Euro, zeitlich unbegrenzter Zugang";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:6:"-28972";s:13:"renewal_group";s:1:"5";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"5";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '7',
      'title' => 'Partnerprogramm',
      'description' => 'Mein Partnerprogramm für splittest-club.com sowie anonyme-website.info',
      'price' => '0.00',
      'data' => 'a:27:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:0:"";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:6:"-83726";s:13:"renewal_group";s:1:"7";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"6";s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '8',
      'title' => 'Klick-Tipp, Standard 10.000',
      'description' => 'Klick-Tipp, Standard 10.000',
      'price' => '27.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:42:"http://www.klick-tipp.com/vielen-dank.html";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:2:"-1";s:13:"renewal_group";s:10:"KTStandard";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"6";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncC1hay0yNTI3LTE%3D";s:15:"cleverbridge_id";s:6:"126043";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '9',
      'title' => 'Internet-Marketing-Tools aus dem Splittest-Club, Share-It!',
      'description' => '',
      'price' => '37.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:17:"37 Euro pro Monat";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"10";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncy1hay1iNmE5LTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:2:"22";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:13:"splittestclub";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:3:"40%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '10',
      'title' => 'Klick-Tipp, Standard 10.000, SOI',
      'description' => 'Klick-Tipp, Standard 10.000, SOI',
      'price' => '27.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:42:"http://www.klick-tipp.com/vielen-dank.html";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:10:"KTStandard";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '11',
      'title' => 'Reseller-Rechte auf den Splittest-Masterkurs',
      'description' => 'Reseller-Rechte für den Splittest-Masterkurs, inklusive Zugang zu Admin-Panel mit Website und Reseller-Tools',
      'price' => '81.51',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:16:"Einmalig 97 Euro";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:67:"https://www.splittest-club.com/club/ihre-bestellung-war-erfolgreich";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:6:"-82739";s:13:"renewal_group";s:2:"11";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"8";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:2:"31";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '12',
      'title' => 'Splittest-Masterkurs',
      'description' => 'Splittest-Masterkurs',
      'price' => '22.69',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"12";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:1:"9";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '13',
      'title' => 'Splittest-Club ohne Probezeit #23',
      'description' => '',
      'price' => '37.00',
      'data' => 'a:39:{s:11:"expire_days";s:2:"1m";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:17:"37 Euro pro Monat";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"10";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncy1hay1iNmE5LTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:2:"23";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:13:"splittestclub";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:3:"40%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '14',
      'title' => 'Splittest-Club ohne Probezeit #24',
      'description' => '',
      'price' => '37.00',
      'data' => 'a:39:{s:11:"expire_days";s:2:"1m";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:17:"37 Euro pro Monat";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"10";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncy1hay1iNmE5LTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:2:"24";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:13:"splittestclub";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:3:"40%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '15',
      'title' => 'Splittest-Masterkurs #6',
      'description' => 'Splittest-Masterkurs',
      'price' => '27.48',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"15";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:1:"6";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '31',
      'title' => 'Splittest-Club ohne Probezeit #3',
      'description' => '',
      'price' => '37.00',
      'data' => 'a:39:{s:11:"expire_days";s:2:"1m";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:17:"37 Euro pro Monat";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"10";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncy1hay1iNmE5LTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:1:"3";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:13:"splittestclub";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:3:"40%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '16',
      'title' => 'Reseller-Rechte auf den Splittest-Masterkurs',
      'description' => 'Reseller-Rechte für den Splittest-Masterkurs, inklusive Zugang zu Admin-Panel mit Website und Reseller-Tools',
      'price' => '123.53',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:29:"Einmalig 147 Euro inkl. MwSt.";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:67:"https://www.splittest-club.com/club/ihre-bestellung-war-erfolgreich";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"16";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"8";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:2:"32";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '20',
      'title' => 'Splittest-Masterkurs #7',
      'description' => 'Splittest-Masterkurs',
      'price' => '31.09',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:1:"7";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '30',
      'title' => 'Internet-Marketing-Tools aus dem Splittest-Club (mit Probemonat), Share-It!',
      'description' => '',
      'price' => '37.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:4:"1.00";s:11:"trial1_days";s:2:"1m";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:63:"In der einmonatigen Testphase 1 Euro, danach 37 Euro pro Monat.";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"10";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncy1hay1iNmE5LTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:1:"1";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:13:"splittestclub";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:3:"40%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '17',
      'title' => 'Reseller-Rechte auf den Splittest-Masterkurs',
      'description' => 'Reseller-Rechte für den Splittest-Masterkurs, inklusive Zugang zu Admin-Panel mit Website und Reseller-Tools',
      'price' => '165.55',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:29:"Einmalig 197 Euro inkl. MwSt.";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:67:"https://www.splittest-club.com/club/ihre-bestellung-war-erfolgreich";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"16";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"8";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:2:"33";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '18',
      'title' => 'Reseller-Rechte auf den Splittest-Masterkurs',
      'description' => 'Reseller-Rechte für den Splittest-Masterkurs, inklusive Zugang zu Admin-Panel mit Website und Reseller-Tools',
      'price' => '207.56',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:29:"Einmalig 247 Euro inkl. MwSt.";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:67:"https://www.splittest-club.com/club/ihre-bestellung-war-erfolgreich";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"16";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"8";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:2:"34";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '19',
      'title' => 'Reseller-Rechte auf den Splittest-Masterkurs',
      'description' => 'Reseller-Rechte für den Splittest-Masterkurs, inklusive Zugang zu Admin-Panel mit Website und Reseller-Tools',
      'price' => '249.58',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:29:"Einmalig 297 Euro inkl. MwSt.";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:67:"https://www.splittest-club.com/club/ihre-bestellung-war-erfolgreich";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"16";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"8";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:2:"35";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '21',
      'title' => 'Splittest-Masterkurs #10',
      'description' => 'Splittest-Masterkurs',
      'price' => '39.50',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:2:"10";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '22',
      'title' => 'Splittest-Masterkurs #11',
      'description' => 'Splittest-Masterkurs',
      'price' => '56.30',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:2:"11";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '23',
      'title' => 'Splittest-Masterkurs #12',
      'description' => 'Splittest-Masterkurs',
      'price' => '81.51',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:2:"12";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '24',
      'title' => 'Splittest-Masterkurs #14',
      'description' => 'Splittest-Masterkurs',
      'price' => '33.57',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:2:"14";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '25',
      'title' => 'Splittest-Masterkurs #8',
      'description' => 'Splittest-Masterkurs',
      'price' => '33.57',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:1:"8";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '26',
      'title' => 'Splittest-Masterkurs #13',
      'description' => 'Splittest-Masterkurs',
      'price' => '41.97',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:2:"13";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '27',
      'title' => 'Splittest-Masterkurs #15',
      'description' => 'Splittest-Masterkurs',
      'price' => '58.78',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:2:"15";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '28',
      'title' => 'Splittest-Masterkurs #16',
      'description' => 'Splittest-Masterkurs',
      'price' => '75.59',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:2:"16";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '29',
      'title' => 'Splittest-Masterkurs #17',
      'description' => 'Splittest-Masterkurs',
      'price' => '109.20',
      'data' => 'a:37:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:58:"https://www.splittest-club.com/club/haben-sie-vielen-dank/";s:4:"##12";N;s:5:"scope";s:8:"disabled";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"20";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:1:"7";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_listid";s:5:"19262";s:12:"clickbank_id";s:2:"17";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '33',
      'title' => 'Partnerprogramm-Masterkurs',
      'description' => 'Partnerprogramm-Masterkurs',
      'price' => '97.00',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:67:"https://www.splittest-club.com/club/ihre-bestellung-war-erfolgreich";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"32";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:2:"10";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '34',
      'title' => 'Klick-Tipp, Standard 10.000, Neukunden',
      'description' => 'Klick-Tipp, Standard 10.000, Neukunden',
      'price' => '27.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:4:"4.95";s:11:"trial1_days";s:3:"31d";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:42:"http://www.klick-tipp.com/vielen-dank.html";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:2:"-1";s:13:"renewal_group";s:10:"KTStandard";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"6";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncC1hay0yNTI3LTE%3D";s:15:"cleverbridge_id";s:6:"126043";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '35',
      'title' => 'Professionelle Verkaufsvideos erstellen',
      'description' => '',
      'price' => '47.00',
      'data' => 'a:39:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:16:"Einmalig 47 Euro";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:67:"https://www.splittest-club.com/club/ihre-bestellung-war-erfolgreich";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"35";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:2:"11";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"40%";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '36',
      'title' => 'Klick-Tipp, Standard 20.000',
      'description' => 'Klick-Tipp, Standard 20.000',
      'price' => '54.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:10:"KTStandard";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"9";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncC1hay0yNTI3LTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '37',
      'title' => 'Klick-Tipp, Einsteiger 200',
      'description' => 'Klick-Tipp, Einsteiger 200',
      'price' => '9.95',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:42:"http://www.klick-tipp.com/vielen-dank.html";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:5:"klick";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:2:"10";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '38',
      'title' => 'Klick-Tipp, 12 Monate, Reseller',
      'description' => 'Klick-Tipp, 12 Monate, Reseller',
      'price' => '204.12',
      'data' => 'a:39:{s:11:"expire_days";s:2:"1y";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:10:"KTStandard";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"6";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '39',
      'title' => 'Klick-Tipp, Premium 10.000, Neukunden',
      'description' => 'Klick-Tipp, Premium 10.000, Neukunden',
      'price' => '47.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:4:"9.95";s:11:"trial1_days";s:3:"31d";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:9:"KTPremium";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:2:"11";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncS1hay04NWQwLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '41',
      'title' => 'Klick-Tipp, Premium 10.000',
      'description' => 'Klick-Tipp, Premium 10.000',
      'price' => '47.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:9:"KTPremium";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:2:"11";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncS1hay04NWQwLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '43',
      'title' => 'Klick-Tipp, Premium 20.000',
      'description' => 'Klick-Tipp, Premium 20.000',
      'price' => '94.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:9:"KTPremium";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:2:"13";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncS1hay04NWQwLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '45',
      'title' => 'Deaktiviert: Klick-Tipp, Premium 30.000',
      'description' => 'Klick-Tipp, Premium 30.000',
      'price' => '141.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:9:"KTPremium";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '47',
      'title' => 'Klick-Tipp, 12 Monate, Social Media Elite Club',
      'description' => 'Klick-Tipp, 12 Monate, Social Media Elite Club',
      'price' => '259.20',
      'data' => 'a:39:{s:11:"expire_days";s:2:"1y";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:10:"KTStandard";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:1:"1";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"6";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '49',
      'title' => 'Jahresabo Klick-Tipp, Standard 10.000',
      'description' => 'Jahresabo Klick-Tipp, Standard 10.000',
      'price' => '270.00',
      'data' => 'a:39:{s:11:"expire_days";s:2:"1y";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"48";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"6";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncC1hay0yNTI3LTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '51',
      'title' => 'Jahresabo Klick-Tipp, Premium 10.000',
      'description' => 'Jahresabo Klick-Tipp, Premium 10.000',
      'price' => '470.00',
      'data' => 'a:39:{s:11:"expire_days";s:2:"1y";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"48";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:2:"11";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncS1hay04NWQwLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    */
    /*
    array(
      'product_id' => '64',
      'title' => 'Klick-Tipp, Premium 40.000',
      'description' => 'Klick-Tipp, Premium 40.000',
      'price' => '200.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:9:"KTPremium";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:2:"21";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncS1hay04NWQwLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '57',
      'title' => 'Klick-Tipp Deluxe 20.000 (Facebook Edition)',
      'description' => 'Klick-Tipp Deluxe 20.000 (Facebook Edition)',
      'price' => '140.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:8:"KTDeluxe";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"17";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1nci1hay04MzBmLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '58',
      'title' => 'Klick-Tipp Deluxe 30.000 (Facebook Edition)',
      'description' => 'Klick-Tipp Deluxe 30.000 (Facebook Edition)',
      'price' => '210.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:8:"KTDeluxe";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"18";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1nci1hay04MzBmLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '59',
      'title' => 'Klick-Tipp Deluxe 50.000 (Facebook Edition)',
      'description' => 'Klick-Tipp Deluxe 50.000 (Facebook Edition)',
      'price' => '350.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:8:"KTDeluxe";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"19";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1nci1hay04MzBmLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '60',
      'title' => 'Klick-Tipp Deluxe 100.000 (Facebook Edition)',
      'description' => 'Klick-Tipp Deluxe 100.000 (Facebook Edition)',
      'price' => '700.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:8:"KTDeluxe";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"20";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1nci1hay04MzBmLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '61',
      'title' => 'Klick-Tipp Deluxe 10.000 (Facebook Edition)',
      'description' => 'Klick-Tipp Deluxe 10.000 (Facebook Edition)',
      'price' => '67.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:1:"1";s:11:"trial1_days";s:3:"31d";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:8:"KTDeluxe";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"16";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1nci1hay04MzBmLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '62',
      'title' => 'Klick-Tipp Deluxe 10.000 (Facebook Edition) Upgrade',
      'description' => 'Klick-Tipp Deluxe 10.000 (Facebook Edition) Upgrade',
      'price' => '67.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:8:"KTDeluxe";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"16";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1nci1hay04MzBmLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '63',
      'title' => 'Klick-Tipp Deluxe 10.000 + Traffic-Bundle',
      'description' => 'Klick-Tipp Deluxe 10.000 + Traffic-Bundle',
      'price' => '500.00',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:8:"KTDeluxe";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"16";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1nci1hay04MzBmLTE%3D";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '65',
      'title' => 'Klick-Tipp, Standard 10.000, Mehr Geschäft',
      'description' => 'Klick-Tipp, Standard 10.000, Mehr Geschäft',
      'price' => '16.20',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:4:"1.00";s:11:"trial1_days";s:3:"31d";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:2:"-1";s:13:"renewal_group";s:10:"KTStandard";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"6";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncC1hay0yNTI3LTE%3D";s:15:"cleverbridge_id";s:6:"126043";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '66',
      'title' => 'Klick-Tipp, Standard 10.000, Mehr Geschäft',
      'description' => 'Klick-Tipp, Standard 10.000, Mehr Geschäft',
      'price' => '14.85',
      'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:4:"1.00";s:11:"trial1_days";s:3:"31d";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:2:"-1";s:13:"renewal_group";s:10:"KTStandard";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"6";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncC1hay0yNTI3LTE%3D";s:15:"cleverbridge_id";s:6:"126043";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:9:"*********";s:17:"shareit_publisher";s:9:"*********";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:3:"30%";s:18:"aff_commission_rec";s:3:"30%";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    */

    array(
      'product_id' => '67',
      'title' => 'CleverBridge Affiliates',
      'description' => 'CleverBridge Affiliates',
      'price' => '0.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2037-12-31",
        "renewal_group" => "67",
        "klicktipp_access" => "22",
        "klicktipp_sync_apikey" => $marketing['API-Affiliates'],
      ))),
    ),
    array(
      'product_id' => '68',
      'title' => 'CleverBridge, Klick-Tipp Standard 10.000',
      'description' => 'CleverBridge, Klick-Tipp Standard 10.000',
      'price' => '27.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "KTStandard",
        "klicktipp_access" => "6",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Basic'],
        "cleverbridge_id" => "126043",
      ))),
    ),
    array(
      'product_id' => '69',
      'title' => 'CleverBridge, Klick-Tipp Premium 10.000',
      'description' => 'CleverBridge, Klick-Tipp Premium 10.000',
      'price' => '47.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "KTPremium",
        "klicktipp_access" => "11",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "126044",
      ))),
    ),
    array(
      'product_id' => '71',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 1.000.000',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 1.000.000',
      'price' => '2499.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "29",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133927",
      ))),
    ),
    array(
      'product_id' => '72',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 500.000',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 500.000',
      'price' => '1499.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "28",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133926",
      ))),
    ),
    array(
      'product_id' => '73',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 250.000',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 250.000',
      'price' => '999.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "27",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133925",
      ))),
    ),

    array(
      'product_id' => '74',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 100.000',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 100.000',
      'price' => '499.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "26",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133924",
      ))),
    ),
    array(
      'product_id' => '75',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 50.000',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 50.000',
      'price' => '289.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "25",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133923",
      ))),
    ),
    array(
      'product_id' => '76',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 25.000',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 25.000',
      'price' => '179.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "24",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133922",
      ))),
    ),
    array(
      'product_id' => '77',
      'title' => 'CleverBridge, Klick-Tipp Deluxe 10.000',
      'description' => 'CleverBridge, Klick-Tipp Deluxe 10.000',
      'price' => '67.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "23",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133928",
      ))),
    ),
    array(
      'product_id' => '78',
      'title' => 'CleverBridge, Klick-Tipp Standard 10.000, Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Standard 10.000, Jahreszahlung',
      'price' => '289.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "78",
        "klicktipp_access" => "6",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Basic'],
        "cleverbridge_id" => "133941",
      ))),
    ),
    array(
      'product_id' => '79',
      'title' => 'CleverBridge, Klick-Tipp Premium 10.000, Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Premium 10.000, Jahreszahlung',
      'price' => '499.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "11",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133942",
      ))),
    ),
    array(
      'product_id' => '80',
      'title' => 'CleverBridge, Klick-Tipp Deluxe 10.000, Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Deluxe 10.000, Jahreszahlung',
      'price' => '719.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "23",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133943",
      ))),
    ),
    array(
      'product_id' => '91',
      'title' => 'CleverBridge, Klick-Tipp Premium 10.000, Zwei-Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Premium 10.000, Zwei-Jahreszahlung',
      'price' => '839.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "11",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "140342",
      ))),
    ),
    array(
      'product_id' => '82',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 25.000, Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 25.000, Jahreszahlung',
      'price' => '1929.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "24",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133945",
      ))),
    ),
    array(
      'product_id' => '83',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 50.000, Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 50.000, Jahreszahlung',
      'price' => '3119.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "25",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133946",
      ))),
    ),
    array(
      'product_id' => '84',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 100.000, Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 100.000, Jahreszahlung',
      'price' => '5389.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "26",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133947",
      ))),
    ),
    array(
      'product_id' => '85',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 250.000, Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 250.000, Jahreszahlung',
      'price' => '10789.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "27",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133948",
      ))),
    ),
    array(
      'product_id' => '86',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 500.000, Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 500.000, Jahreszahlung',
      'price' => '16189.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "28",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133949",
      ))),
    ),
    array(
      'product_id' => '87',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 1.000.000, Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 1.000.000, Jahreszahlung',
      'price' => '26989.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "29",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "133950",
      ))),
    ),
    array(
      'product_id' => '90',
      'title' => 'CleverBridge, Klick-Tipp Standard 10.000, Zwei-Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Standard 10.000, Zwei-Jahreszahlung',
      'price' => '479.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "6",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Basic'],
        "cleverbridge_id" => "140341",
      ))),
    ),
    array(
      'product_id' => '92',
      'title' => 'CleverBridge, Klick-Tipp Deluxe 10.000, Zwei-Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Deluxe 10.000, Zwei-Jahreszahlung',
      'price' => '1199.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "16",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "140343",
      ))),
    ),
    array(
      'product_id' => '93',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 25.000, Zwei-Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 25.000, Zwei-Jahreszahlung',
      'price' => '3219.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "24",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "140344",
      ))),
    ),
    array(
      'product_id' => '94',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 50.000, Zwei-Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 50.000, Zwei-Jahreszahlung',
      'price' => '5199.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "19",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "140345",
      ))),
    ),
    array(
      'product_id' => '95',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 100.000, Zwei-Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 100.000, Zwei-Jahreszahlung',
      'price' => '8979.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "26",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "140346",
      ))),
    ),
    array(
      'product_id' => '96',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 250.000, Zwei-Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 250.000, Zwei-Jahreszahlung',
      'price' => '17979.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "27",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "140347",
      ))),
    ),
    array(
      'product_id' => '97',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 500.000, Zwei-Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 500.000, Zwei-Jahreszahlung',
      'price' => '26979.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "28",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "140348",
      ))),
    ),
    array(
      'product_id' => '98',
      'title' => 'CleverBridge, Klick-Tipp Enterprise 1.000.000, Zwei-Jahreszahlung',
      'description' => 'CleverBridge, Klick-Tipp Enterprise 1.000.000, Zwei-Jahreszahlung',
      'price' => '44899.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "29",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "cleverbridge_id" => "140349",
      ))),
    ),

    /*
      array(
        'product_id' => '88',
        'title' => 'CleverBridge, Splittest-Club, Jahreszahlung',
        'description' => 'CleverBridge, Splittest-Club, Jahreszahlung',
        'price' => '399.00',
        'data' => 'a:39:{s:11:"expire_days";s:2:"1y";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"88";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncy1hay1iNmE5LTE%3D";s:15:"cleverbridge_id";s:6:"134033";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
      ),
      array(
        'product_id' => '89',
        'title' => 'CleverBridge, Splittest-Club',
        'description' => 'CleverBridge, Splittest-Club',
        'price' => '37.00',
        'data' => 'a:39:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"88";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncy1hay1iNmE5LTE%3D";s:15:"cleverbridge_id";s:6:"134032";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
      ),
      array(
        'product_id' => '99',
        'title' => 'CleverBridge, Splittest-Club, Zwei-Jahreszahlung',
        'description' => 'CleverBridge, Splittest-Club, Zwei-Jahreszahlung',
        'price' => '666.00',
        'data' => 'a:39:{s:11:"expire_days";s:2:"2y";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"90";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:26:"YXBpay1ncy1hay1iNmE5LTE%3D";s:15:"cleverbridge_id";s:6:"140340";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
      ),
  */

    /*
    array(
      'product_id' => '100',
      'title' => 'Auftragsdatenverarbeitung',
      'description' => 'Auftragsdatenverarbeitung',
      'price' => '49.95',
      'data' => 'a:40:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:3:"100";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"1";s:21:"klicktipp_sync_apikey";s:12:"wszauz8z076b";s:15:"cleverbridge_id";s:6:"141833";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"37123";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    */

    // digistore

    array(
      'product_id' => '137',
      'title' => 'DigiStore24 Affiliates',
      'description' => 'DigiStore24 Affiliates',
      'price' => '0.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2037-12-31",
        "renewal_group" => "137",
        "klicktipp_access" => "30",
        "klicktipp_sync_apikey" => $marketing['API-Affiliates'],
      ))),
    ),

    array(
      'product_id' => '101',
      'title' => 'DigiStore24, Klick-Tipp Standard 10.000',
      'description' => 'DigiStore24, Klick-Tipp Standard 10.000',
      'price' => '27.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "KTStandard",
        "klicktipp_access" => "6",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Basic'],
        "digistore_id" => "36467",
      ))),
    ),
    array(
      'product_id' => '102',
      'title' => 'DigiStore24, Klick-Tipp Standard 10.000, Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Standard 10.000, Jahreszahlung',
      'price' => '291.60',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "78",
        "klicktipp_access" => "6",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Basic'],
        "digistore_id" => "36469",
      ))),
    ),
    array(
      'product_id' => '103',
      'title' => 'DigiStore24, Klick-Tipp Standard 10.000, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Standard 10.000, Zwei-Jahreszahlung',
      'price' => '518.40',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "6",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Basic'],
        "digistore_id" => "140341",
      ))),
    ),
    array(
      'product_id' => '104',
      'title' => 'DigiStore24, Klick-Tipp Premium 10.000',
      'description' => 'DigiStore24, Klick-Tipp Premium 10.000',
      'price' => '47.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "KTPremium",
        "klicktipp_access" => "11",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36473",
      ))),
    ),
    array(
      'product_id' => '105',
      'title' => 'DigiStore24, Klick-Tipp Premium 10.000, Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Premium 10.000, Jahreszahlung',
      'price' => '507.60',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "11",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36475",
      ))),
    ),
    array(
      'product_id' => '106',
      'title' => 'DigiStore24, Klick-Tipp Premium 10.000, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Premium 10.000, Zwei-Jahreszahlung',
      'price' => '902.40',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "11",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36477",
      ))),
    ),
    array(
      'product_id' => '107',
      'title' => 'DigiStore24, Klick-Tipp Deluxe 10.000',
      'description' => 'DigiStore24, Klick-Tipp Deluxe 10.000',
      'price' => '67.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "16",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36479",
      ))),
    ),
    array(
      'product_id' => '108',
      'title' => 'DigiStore24, Klick-Tipp Deluxe 10.000, Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Deluxe 10.000, Jahreszahlung',
      'price' => '723.60',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "11",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36481",
      ))),
    ),
    array(
      'product_id' => '109',
      'title' => 'DigiStore24, Klick-Tipp Deluxe 10.000, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Deluxe 10.000, Zwei-Jahreszahlung',
      'price' => '1286.40',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "16",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36483",
      ))),
    ),
    array(
      'product_id' => '110',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 10.000, 1 Mailserver',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 10.000, 1 Mailserver',
      'price' => '149.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "110",
        "klicktipp_access" => "31",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36485",
      ))),
    ),
    array(
      'product_id' => '111',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 10.000, Jahreszahlung, 1 Mailserver',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 10.000, Jahreszahlung, 1 Mailserver',
      'price' => '1609.20',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "111",
        "klicktipp_access" => "31",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36487",
      ))),
    ),
    array(
      'product_id' => '112',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 10.000, Zwei-Jahreszahlung, 1 Mailserver',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 10.000, Zwei-Jahreszahlung, 1 Mailserver',
      'price' => '2860.80',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "112",
        "klicktipp_access" => "31",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36489",
      ))),
    ),
    array(
      'product_id' => '113',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 15.000, 2 Mailserver',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 15.000, 2 Mailserver',
      'price' => '199.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "113",
        "klicktipp_access" => "33",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "36491",
      ))),
    ),
    array(
      'product_id' => '114',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 15.000, Jahreszahlung, 2 Mailserver',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 15.000, Jahreszahlung, 2 Mailserver',
      'price' => '2149.20',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "114",
        "klicktipp_access" => "33",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37079",
      ))),
    ),
    array(
      'product_id' => '115',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 15.000, Zwei-Jahreszahlung, 2 Mailserver',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 15.000, Zwei-Jahreszahlung, 2 Mailserver',
      'price' => '3820.80',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "115",
        "klicktipp_access" => "33",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37081",
      ))),
    ),
    array(
      'product_id' => '116',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 25.000',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 25.000',
      'price' => '290.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "116",
        "klicktipp_access" => "24",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37083",
      ))),
    ),
    array(
      'product_id' => '117',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 25.000, Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 25.000, Jahreszahlung',
      'price' => '3132.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "24",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37085",
      ))),
    ),
    array(
      'product_id' => '118',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 25.000, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 25.000, Zwei-Jahreszahlung',
      'price' => '5568.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "24",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37087",
      ))),
    ),
    array(
      'product_id' => '119',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 50.000',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 50.000',
      'price' => '450.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "25",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37089",
      ))),
    ),
    array(
      'product_id' => '120',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 50.000, Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 50.000, Jahreszahlung',
      'price' => '4860.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "25",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37091",
      ))),
    ),
    array(
      'product_id' => '121',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 50.000, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 50.000, Zwei-Jahreszahlung',
      'price' => '8640.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "25",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37093",
      ))),
    ),
    array(
      'product_id' => '122',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 100.000',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 100.000',
      'price' => '750.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "26",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37095",
      ))),
    ),
    array(
      'product_id' => '123',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 100.000, Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 100.000, Jahreszahlung',
      'price' => '8100.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "26",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37097",
      ))),
    ),
    array(
      'product_id' => '124',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 100.000, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 100.000, Zwei-Jahreszahlung',
      'price' => '14400.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "26",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37099",
      ))),
    ),
    array(
      'product_id' => '125',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 250.000',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 250.000',
      'price' => '1500.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "27",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37101",
      ))),
    ),
    array(
      'product_id' => '126',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 250.000, Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 250.000, Jahreszahlung',
      'price' => '16200.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "27",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37103",
      ))),
    ),
    array(
      'product_id' => '127',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 250.000, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 250.000, Zwei-Jahreszahlung',
      'price' => '28800.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "27",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37105",
      ))),
    ),
    array(
      'product_id' => '128',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 500.000',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 500.000',
      'price' => '2300.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "28",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37107",
      ))),
    ),
    array(
      'product_id' => '129',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 500.000, Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 500.000, Jahreszahlung',
      'price' => '24840.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "28",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37109",
      ))),
    ),
    array(
      'product_id' => '130',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 500.000, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 500.000, Zwei-Jahreszahlung',
      'price' => '44160.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "28",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37113",
      ))),
    ),
    array(
      'product_id' => '131',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 1.000.000',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 1.000.000',
      'price' => '3700.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "71",
        "klicktipp_access" => "29",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37115",
      ))),
    ),
    array(
      'product_id' => '132',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 1.000.000, Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 1.000.000, Jahreszahlung',
      'price' => '39960.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "79",
        "klicktipp_access" => "29",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37119",
      ))),
    ),
    array(
      'product_id' => '133',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 1.000.000, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 1.000.000, Zwei-Jahreszahlung',
      'price' => '71040.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2y",
        "is_recurring" => "1",
        "renewal_group" => "90",
        "klicktipp_access" => "29",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "37121",
      ))),
    ),

    /*
    array(
      'product_id' => '134',
      'title' => 'DigiStore24, Splittest-Club',
      'description' => 'DigiStore24, Splittest-Club',
      'price' => '37.00',
      'data' => 'a:40:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"88";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:12:"h9zauz8zb6a9";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"37129";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '135',
      'title' => 'DigiStore24, Splittest-Club, Jahreszahlung',
      'description' => 'DigiStore24, Splittest-Club, Jahreszahlung',
      'price' => '399.60',
      'data' => 'a:40:{s:11:"expire_days";s:2:"1y";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"88";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:12:"h9zauz8zb6a9";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"37131";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '136',
      'title' => 'DigiStore24, Splittest-Club, Zwei-Jahreszahlung',
      'description' => 'DigiStore24, Splittest-Club, Zwei-Jahreszahlung',
      'price' => '666.00',
      'data' => 'a:40:{s:11:"expire_days";s:2:"2y";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"90";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:0:"";s:21:"klicktipp_sync_apikey";s:12:"h9zauz8zb6a9";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"37133";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '138',
      'title' => 'DigiStore24, Klick-Tipp Deluxe 10.000, Webinar-Sonderangebot',
      'description' => 'DigiStore24, Klick-Tipp Deluxe 10.000, Webinar-Sonderangebot',
      'price' => '67.00',
      'data' => 'a:40:{s:11:"expire_days";s:2:"3d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:3:"138";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"16";s:21:"klicktipp_sync_apikey";s:12:"h8zauz8z830f";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"38483";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '139',
      'title' => 'DigiStore24, Klick-Tipp Premium 10.000, Webinar-Sonderangebot',
      'description' => 'DigiStore24, Klick-Tipp Premium 10.000, Webinar-Sonderangebot',
      'price' => '47.00',
      'data' => 'a:40:{s:11:"expire_days";s:2:"3d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:3:"139";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:2:"11";s:21:"klicktipp_sync_apikey";s:12:"h7zauz8z85d0";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"38481";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '140',
      'title' => 'DigiStore24, Klick-Tipp Standard 10.000, Webinar-Sonderangebot',
      'description' => 'DigiStore24, Klick-Tipp Standard 10.000, Webinar-Sonderangebot',
      'price' => '27.00',
      'data' => 'a:40:{s:11:"expire_days";s:2:"3d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:3:"140";s:14:"need_agreement";s:0:"";s:13:"require_other";a:1:{i:0;s:0:"";}s:16:"prevent_if_other";a:1:{i:0;s:0:"";}s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"6";s:21:"klicktipp_sync_apikey";s:12:"h6zauz8z2527";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"38479";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '141',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 10.000, 1 Mailserver, Webinar-Sonderangebot',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 10.000, 1 Mailserver, Webinar-Sonderangebot',
      'price' => '99.00',
      'data' => 'a:40:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:3:"110";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"31";s:21:"klicktipp_sync_apikey";s:13:"47ozauz8z29ca";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"54847";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";a:1:{i:0;s:3:"100";}s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '142',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 25.000, Webinar-Sonderangebot',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 25.000, Webinar-Sonderangebot',
      'price' => '240.00',
      'data' => 'a:40:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:3:"116";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"24";s:21:"klicktipp_sync_apikey";s:13:"47ozauz8z29ca";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"54849";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";a:1:{i:0;s:3:"100";}s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '143',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 50.000, Webinar-Sonderangebot',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 50.000, Webinar-Sonderangebot',
      'price' => '400.00',
      'data' => 'a:40:{s:11:"expire_days";s:3:"35d";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:1:"1";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:2:"71";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:1:"4";s:16:"klicktipp_access";s:2:"25";s:21:"klicktipp_sync_apikey";s:13:"47ozauz8z29ca";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"54851";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";a:1:{i:0;s:3:"100";}s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    ),
    array(
      'product_id' => '145',
      'title' => 'Mailserver-Setup',
      'description' => 'Setupgebühren für das erneute Aufsetzen eines Mailservers',
      'price' => '200.00',
      'data' => 'a:40:{s:11:"expire_days";s:10:"2037-12-31";s:12:"trial1_price";s:0:"";s:11:"trial1_days";s:0:"";s:12:"is_recurring";s:0:"";s:10:"start_date";s:0:"";s:5:"terms";s:0:"";s:12:"rebill_times";s:0:"";s:15:"paypal_currency";s:3:"EUR";s:4:"##11";N;s:3:"url";s:0:"";s:8:"add_urls";s:0:"";s:11:"thankyouurl";s:0:"";s:4:"##12";N;s:5:"scope";s:0:"";s:5:"order";s:0:"";s:11:"price_group";s:0:"";s:13:"renewal_group";s:3:"145";s:14:"need_agreement";s:0:"";s:13:"require_other";N;s:16:"prevent_if_other";N;s:4:"##13";N;s:19:"autoresponder_renew";s:0:"";s:16:"dont_mail_expire";s:0:"";s:13:"drupal_access";s:0:"";s:16:"klicktipp_access";s:1:"1";s:21:"klicktipp_sync_apikey";s:0:"";s:15:"cleverbridge_id";s:0:"";s:12:"clickbank_id";s:0:"";s:16:"clickbank_cbskin";s:0:"";s:14:"clickbank_k_id";s:0:"";s:18:"clickbank_k_cbskin";s:0:"";s:12:"digistore_id";s:5:"61915";s:15:"shareit_account";s:9:"klicktipp";s:10:"shareit_id";s:0:"";s:17:"shareit_publisher";s:0:"";s:24:"additional_subscriptions";N;s:14:"aff_commission";s:0:"";s:18:"aff_commission_rec";s:0:"";s:15:"aff_commission2";s:0:"";s:19:"aff_commission_rec2";s:0:"";}'
    )
*/
    array(
      'product_id' => '146',
      'title' => 'DigiStore24, Klick-Tipp Enterprise 10.000, 1 Mailserver + Klick-Tipp Con',
      'description' => 'DigiStore24, Klick-Tipp Enterprise 10.000, 1 Mailserver + Klick-Tipp Con',
      'price' => '149.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "35d",
        "is_recurring" => "1",
        "renewal_group" => "146",
        "klicktipp_access" => "31",
        "klicktipp_sync_apikey" => $marketing['API-Klicktipp-Advanced'],
        "digistore_id" => "106179",
      ))),
    ),
    array(
      'product_id' => '147',
      'title' => 'Certified Consultant',
      'description' => 'Certified Consultant',
      'price' => '1995.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "147",
        "klicktipp_access" => "30",
        "klicktipp_sync_apikey" => $marketing['API-Affiliates'],
        "digistore_id" => "111167",
      ))),
    ),
    array(
      'product_id' => '151',
      'title' => 'Klick-Tipp Convention',
      'description' => 'Klick-Tipp Convention',
      'price' => '1449.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "1y",
        "is_recurring" => "1",
        "renewal_group" => "151",
        "klicktipp_access" => "30",
        "klicktipp_sync_apikey" => $marketing['API-Affiliates'],
        "digistore_id" => "104997",
      ))),
    ),
    array(
      'product_id' => '152',
      'title' => 'Mentorship',
      'description' => 'Mentorship',
      'price' => '5000.00',
      'data' => serialize(array_merge($products_default, array(
        "expire_days" => "2037-12-31",
        "is_recurring" => "",
        "renewal_group" => "152",
        "klicktipp_access" => "30",
        "klicktipp_sync_apikey" => $marketing['API-Affiliates'],
        "digistore_id" => "325008",
      ))),
    ),
  );
  $query = db_insert('amember_products')->fields(array(
    'product_id',
    'title',
    'description',
    'price',
    'data',
  ));
  foreach ($amember_products as $record) {
    $query->values($record);
  }
  $query->execute();

}

function _klicktipp_app_profile_load_rest_service() {

  //create public api endpoint /api
  $endpoint = _klicktipp_app_profile_load_api();
  services_endpoint_save($endpoint);

  //create cockpit api endpoint /ipa
  $cockpit = _klicktipp_app_profile_load_cockpit();
  services_endpoint_save($cockpit);

}

function _klicktipp_app_profile_load_api() {
  /*
   * Export service configuration at: https://www.klick-tipp.com/de/admin/build/services/list/klicktipp/export
   * then copy+paste it here
   */
  /////// START of export
  $endpoint = new stdClass();
  $endpoint->disabled = FALSE; /* Edit this to true to make a default endpoint disabled initially */
  $endpoint->api_version = 3;
  $endpoint->name = 'klicktipp';
  $endpoint->server = 'rest_server';
  $endpoint->path = 'api';
  $endpoint->authentication = array(
    'services' => 'services',
  );
  $endpoint->server_settings = array(
    'formatters' => array(
      'html' => TRUE,
      'json' => TRUE,
      'php' => TRUE,
      'xml' => TRUE,
      'bencode' => FALSE,
      'jsonp' => FALSE,
    ),
    'parsers' => array(
      'application/json' => TRUE,
      'application/x-www-form-urlencoded' => TRUE,
      'application/xml' => TRUE,
      'multipart/form-data' => TRUE,
      'text/xml' => TRUE,
      'application/vnd.php.serialized' => FALSE,
    ),
  );
  $endpoint->resources = array(
    'account' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'get-customer-data' => array(
          'enabled' => '1',
        ),
        'search' => array(
          'enabled' => '1',
        ),
        'login' => array(
          'enabled' => '1',
        ),
        'logout' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'bcraccount' => array(
      'operations' => array(
        'index' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'bcrevent' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'bcrlogin' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'bcrregex' => array(
      'operations' => array(
        'index' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'bcrsubscriber' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'search' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'exitlightbox' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
      ),
      'dependencies' => array(
        'enabled' => '1',
        ),
      ),
    ),
    'feedback' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
        'enabled' => '1',
        ),
        'response' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'field' => array(
      'operations' => array(
        'index' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'group' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'list' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'redirect' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'onetimeoffer' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'plugin' => array(
      'operations' => array(
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'inbound' => array(
          'enabled' => '1',
        ),
        'connect' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'socialproof' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'split' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'tunnel' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'subscriber' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'unsubscribe' => array(
          'enabled' => '1',
        ),
        'tag' => array(
          'enabled' => '1',
        ),
        'untag' => array(
          'enabled' => '1',
        ),
        'search' => array(
          'enabled' => '1',
        ),
        'tagged' => array(
          'enabled' => '1',
        ),
        'signin' => array(
          'enabled' => '1',
        ),
        'signout' => array(
          'enabled' => '1',
        ),
        'signoff' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'tag' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'blacklist' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-variable' => array(
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'zapier' => array(
      'operations' => array(
        'index' => array(
          'enabled' => '1',
        ),
      ),
    ),
  );
  $endpoint->debug = 0;


  /////// END of export

  return $endpoint;
}

function _klicktipp_app_profile_load_cockpit() {

  /*
   * Export service configuration at: https://www.klick-tipp.com/admin/structure/services/list/cockpit/export
   * then copy+paste it here
   */
  /////// START of export

  $endpoint = new stdClass();
  $endpoint->disabled = FALSE; /* Edit this to true to make a default endpoint disabled initially */
  $endpoint->api_version = 3;
  $endpoint->name = 'cockpit';
  $endpoint->server = 'rest_server';
  $endpoint->path = 'ipa';
  $endpoint->authentication = array(
    'services' => 'services',
  );
  $endpoint->server_settings = array(
    'formatters' => array(
      'json' => TRUE,
      'bencode' => FALSE,
      'html' => FALSE,
      'jsonp' => FALSE,
      'php' => FALSE,
      'xml' => FALSE,
    ),
    'parsers' => array(
      'application/json' => TRUE,
      'application/x-www-form-urlencoded' => TRUE,
      'application/xml' => TRUE,
      'multipart/form-data' => TRUE,
      'text/xml' => TRUE,
    ),
  );
  $endpoint->resources = array(
    'account' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'login' => array(
          'enabled' => '1',
        ),
        'logout' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-automation' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'validate' => array(
          'enabled' => '1',
        ),
        'subscribers-waiting' => array(
          'enabled' => '1',
        ),
        'conversion' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-automation-email' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'personalize' => array(
          'enabled' => '1',
        ),
        'signature_index' => array(
          'enabled' => '1',
        ),
        'editor_retrieve' => array(
          'enabled' => '1',
        ),
        'editor_save' => array(
          'enabled' => '1',
        ),
        'editor_publish' => array(
          'enabled' => '1',
        ),
        'editor_validate' => array(
          'enabled' => '1',
        ),
        'editor_templates' => array(
          'enabled' => '1',
        ),
        'editor_condition' => array(
          'enabled' => '1',
        ),
        'spamscore' => array(
          'enabled' => '1',
        ),
        'sendpreview' => array(
          'enabled' => '1',
        ),
        'gmailpreview' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-automation-sms' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'personalize' => array(
          'enabled' => '1',
        ),
        'signature_index' => array(
          'enabled' => '1',
        ),
        'editor_retrieve' => array(
          'enabled' => '1',
        ),
        'editor_save' => array(
          'enabled' => '1',
        ),
        'editor_publish' => array(
          'enabled' => '1',
        ),
        'editor_validate' => array(
          'enabled' => '1',
        ),
        'editor_templates' => array(
          'enabled' => '1',
        ),
        'editor_condition' => array(
          'enabled' => '1',
        ),
        'sendpreview' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-calendar' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'calendar' => array(
          'enabled' => '1',
        ),
      ),
    ),
  'kt-config' => array(
    'operations' => array(
      'index' => array(
        'enabled' => '1',
      ),
    ),
  ),
    'kt-email' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'personalize' => array(
          'enabled' => '1',
        ),
        'signature_index' => array(
          'enabled' => '1',
        ),
        'editor_retrieve' => array(
          'enabled' => '1',
        ),
        'editor_save' => array(
          'enabled' => '1',
        ),
        'editor_publish' => array(
          'enabled' => '1',
        ),
        'editor_validate' => array(
          'enabled' => '1',
        ),
        'editor_templates' => array(
          'enabled' => '1',
        ),
        'editor_import' => array(
          'enabled' => '1',
        ),
        'editor_condition' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-checkbox' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-date' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-datetime' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-decimal' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-dropdown' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-email' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-html' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-number' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-paragraph' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-single' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-time' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-field-url' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-import' => array(
      'actions' => array(
        'assign-fields' => array(
          'enabled' => '1',
        ),
        'cancel' => array(
          'enabled' => '1',
        ),
        'detect-format' => array(
          'enabled' => '1',
        ),
        'fileinfo' => array(
          'enabled' => '1',
        ),
        'import' => array(
          'enabled' => '1',
        ),
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'progress' => array(
          'enabled' => '1',
        ),
        'push' => array(
          'enabled' => '1',
        ),
        'validate-field' => array(
          'enabled' => '1',
        ),
        'validate-subscriber-ids' => array(
          'enabled' => '1',
        ),
      ),
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-listbuilding' => array(
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-marketingtool' => array(
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-metalabels' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'add' => array(
          'enabled' => '1',
        ),
        'remove' => array(
          'enabled' => '1',
        ),
        'search' => array(
          'enabled' => '1',
        ),
        'metatypes' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-newsletter-autoresponder' => array(
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-notification-email' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'personalize' => array(
          'enabled' => '1',
        ),
        'signature_index' => array(
          'enabled' => '1',
        ),
        'editor_retrieve' => array(
          'enabled' => '1',
        ),
        'editor_save' => array(
          'enabled' => '1',
        ),
        'editor_publish' => array(
          'enabled' => '1',
        ),
        'editor_validate' => array(
          'enabled' => '1',
        ),
        'editor_templates' => array(
          'enabled' => '1',
        ),
        'editor_condition' => array(
          'enabled' => '1',
        ),
        'spamscore' => array(
          'enabled' => '1',
        ),
        'sendpreview' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-notification-sms' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'personalize' => array(
          'enabled' => '1',
        ),
        'signature_index' => array(
          'enabled' => '1',
        ),
        'sendpreview' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-outbound' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-signature' => array(
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'signature_index' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-splittest' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-statistics' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'statistic' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-apikey' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-automation-finished' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-automation-started' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-chargedback' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-clicked' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-converted' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-digistore-affiliation' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-email-clicked' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-email-opened' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-email-sent' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-email-viewed' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-facebook' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-facebook-audience' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-forms' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-kajabi-activated' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-kajabi-deactivated' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-opened' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-outbound-activated' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-payment' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-payment-completed' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-payment-expired' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-plugin-inbound-finished' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-plugin-inbound-inprogress' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-plugin-inbound-ready' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-plugin-inbound-started' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-rebill-canceled' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-rebill-resumed' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-refunded' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-request' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-sent' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-smartlink' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-sms-clicked' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-sms-sent' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-smslistbuilding' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-tagging-pixel' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-tag-viewed' => array(
      'operations' => array(
        'retrieve' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-templates-email' => array(
      'operations' => array(
        'create' => array(
          'enabled' => '1',
        ),
        'retrieve' => array(
          'enabled' => '1',
        ),
        'update' => array(
          'enabled' => '1',
        ),
        'delete' => array(
          'enabled' => '1',
        ),
        'index' => array(
          'enabled' => '1',
        ),
      ),
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
        'dependencies' => array(
          'enabled' => '1',
        ),
        'editor_retrieve' => array(
          'enabled' => '1',
        ),
        'editor_save' => array(
          'enabled' => '1',
        ),
        'editor_publish' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-templates-email-global' => array(
      'operations' => array(
        'index' => array(
          'enabled' => '1',
        ),
      ),
    ),
    'kt-variable' => array(
      'actions' => array(
        'index_advanced' => array(
          'enabled' => '1',
        ),
      ),
    ),
  );
  $endpoint->debug = 0;


  /////// END of export

  return $endpoint;
}

/**
 * Removes drupal prefix from amember tables
 */
function _klicktipp_rename_amember_tables()
{
  $amember_tables = [
    'amember_config',
    'amember_email_templates',
    'amember_error_log',
    'amember_members',
    'amember_payments',
    'amember_products'
  ];

  $tables = db_query("SHOW TABLES")->fetchCol();

  foreach ($amember_tables as $amember_table) {
    $table = preg_grep("/.*" . $amember_table . "$/", $tables);
    if (!empty($table) && count($table) === 1) {
      $from = reset($table);
      if ($from != $amember_table) {
        // Bound parameters are not possible here
        db_query("RENAME TABLE " . $from . " TO " . $amember_table);
      }
    }
  }
}

function _klicktipp_create_default_marketing_account_segments($marketingAccount) {

  if (!$marketingAccount || !$marketingAccount->uid) {
    return;
  }

  $userId = $marketingAccount->uid;

  $defaultSegments = [
    [
      'name' => 'Account Standard',
      'selector' => 'account-standard',
      'description' => 'LaunchDarkly Segment: Account Standard',
      'product' => UserGroups::ACCOUNT_CATEGORY_STANDARD,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Account Beginner',
      'selector' => 'account-beginner',
      'description' => 'LaunchDarkly Segment: Account Beginner',
      'product' => UserGroups::ACCOUNT_CATEGORY_BEGINNER,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Account Premium',
      'selector' => 'account-premium',
      'description' => 'LaunchDarkly Segment: Account Premium',
      'product' => UserGroups::ACCOUNT_CATEGORY_PREMIUM,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Account Deluxe/Platinum',
      'selector' => 'account-deluxe',
      'description' => 'LaunchDarkly Segment: Account Deluxe/Platinum',
      'product' => UserGroups::ACCOUNT_CATEGORY_PLATINUM,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Account Enterprise',
      'selector' => 'account-enterprise',
      'description' => 'LaunchDarkly Segment: Account Enterprise',
      'product' => UserGroups::ACCOUNT_CATEGORY_ENTERPRISE,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Account Free',
      'selector' => 'account-free',
      'description' => 'LaunchDarkly Segment: Account Free',
      'product' => UserGroups::ACCOUNT_CATEGORY_FREE,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Account Startup',
      'selector' => 'account-startup',
      'description' => 'LaunchDarkly Segment: Account Startup',
      'product' => UserGroups::ACCOUNT_CATEGORY_STARTUP,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Account Business',
      'selector' => 'account-business',
      'description' => 'LaunchDarkly Segment: Account Business',
      'product' => UserGroups::ACCOUNT_CATEGORY_BUSINESS,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Account 1st Class',
      'selector' => 'account-1st-class',
      'description' => 'LaunchDarkly Segment: Account 1st Class',
      'product' => UserGroups::ACCOUNT_CATEGORY_FIRSTCLASS,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Account Affiliate',
      'selector' => 'account-affiliate',
      'description' => 'LaunchDarkly Segment: Account Affiliate',
      'product' => UserGroups::ACCOUNT_CATEGORY_AFFILIATE,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'Consultant',
      'selector' => 'account-consultant',
      'description' => 'LaunchDarkly Segment: Account Consultant',
      'product' => UserGroups::ACCOUNT_CATEGORY_CONSULTANT,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'KlickTipp Admin',
      'selector' => 'account-admin',
      'description' => 'LaunchDarkly Segment: KlickTipp Admin',
      'product' => VarSegmentProductCategory::KLICKTIPP_ADMIN,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
    [
      'name' => 'KlickTipp Support',
      'selector' => 'account-support',
      'description' => 'LaunchDarkly Segment: KlickTipp Support',
      'product' => VarSegmentProductCategory::KLICKTIPP_SUPPORT,
      'beamer' => TRUE,
      'launchDarkly' => TRUE,
    ],
  ];

  foreach($defaultSegments as $segment) {
    $dbArray = [
      'RelOwnerUserID' => $userId,
      'Data' => serialize($segment)
    ];
    $objSegment = VarSegmentProductCategory::FromArray($dbArray);
    if ($objSegment) {
      $objSegment->update();
    }
  }

}

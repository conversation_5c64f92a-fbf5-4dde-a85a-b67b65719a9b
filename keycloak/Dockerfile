# version taken from .env file
ARG KEYCLOAK_VERSION=local
# renovate: datasource=docker depName=curlimages/curl
ARG CURL_VERSION=8.11.1
# renovate: datasource=docker depName=eclipse-temurin
ARG JDK_VERSION=21-jdk


# download the plugins
# FROM --platform=linux/amd64 curlimages/curl:8.9.0 AS curl-loader
FROM --platform=$BUILDPLATFORM 035931995993.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/curlimages/curl:${CURL_VERSION} AS curl-loader

LABEL maintainer="<EMAIL>"
LABEL ManagedBy="Operations"

USER 1001

# renovate: datasource=github-releases depName=daniel-frak/keycloak-user-migration
ARG KEYCLOAK_REST_PROVIDER_VERSION=6.0.2
# renovate: datasource=github-releases depName=netzbegruenung/keycloak-mfa-plugins
ARG KEYCLOAK_SMS_AUTHENTICATOR_VERSION=26.1.6
# renovate: datasource=github-tags depName=p2-inc/keycloak-magic-link
ARG KEYCLOAK_MAGIC_LINK_VERSION=0.33
# renovate: datasource=github-releases depName=aznamier/keycloak-event-listener-rabbitmq
ARG KEYCLOAK_RABBITMQ_LISTENER_VERSION=3.0.5

RUN curl -L -o /tmp/rest-provider-${KEYCLOAK_REST_PROVIDER_VERSION}.jar \
        https://github.com/daniel-frak/keycloak-user-migration/releases/download/${KEYCLOAK_REST_PROVIDER_VERSION}/keycloak-rest-provider-${KEYCLOAK_REST_PROVIDER_VERSION}.jar && \
    curl -L -o /tmp/sms-authenticator-${KEYCLOAK_SMS_AUTHENTICATOR_VERSION}.jar \
        https://github.com/netzbegruenung/keycloak-mfa-plugins/releases/download/v${KEYCLOAK_SMS_AUTHENTICATOR_VERSION}/netzbegruenung.sms-authenticator-v${KEYCLOAK_SMS_AUTHENTICATOR_VERSION}.jar && \
    curl -L -o /tmp/magic-link-${KEYCLOAK_MAGIC_LINK_VERSION}.jar \
        https://repo1.maven.org/maven2/io/phasetwo/keycloak/keycloak-magic-link/${KEYCLOAK_MAGIC_LINK_VERSION}/keycloak-magic-link-${KEYCLOAK_MAGIC_LINK_VERSION}.jar && \
    curl -L -o /tmp/event-listener-rabbitmq-${KEYCLOAK_RABBITMQ_LISTENER_VERSION}.jar \
        https://github.com/aznamier/keycloak-event-listener-rabbitmq/releases/download/${KEYCLOAK_RABBITMQ_LISTENER_VERSION}/keycloak-to-rabbit-${KEYCLOAK_RABBITMQ_LISTENER_VERSION}.jar


# build a jar with the themes
# FROM --platform=linux/amd64 eclipse-temurin:21-jdk AS kt-builder
FROM --platform=$BUILDPLATFORM 035931995993.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/library/eclipse-temurin:${JDK_VERSION} AS kt-builder

USER 1001

COPY rootfs /

WORKDIR /src

RUN jar cf /tmp/klicktipp-themes.jar -C ./ .


# Build the final image
FROM --platform=$BUILDPLATFORM 035931995993.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/bitnami/keycloak:${KEYCLOAK_VERSION}

LABEL maintainer="<EMAIL>"
LABEL ManagedBy="Operations"

ARG KEYCLOAK_HOME="/opt/bitnami/keycloak"
ENV KEYCLOAK_HOME=${KEYCLOAK_HOME}

# copy content from curl-loader
COPY --from=curl-loader /tmp/*.jar ${KEYCLOAK_HOME}/providers/

# copy content from kt-builder
COPY --from=kt-builder /tmp/klicktipp-themes.jar ${KEYCLOAK_HOME}/providers/klicktipp-themes.jar

# copy the jq binary
COPY --from=ghcr.io/jqlang/jq:1.7.1 /jq /usr/bin/jq

COPY rootfs/usr /usr/

USER root
RUN mkdir -pv /home/<USER>
    chown -R keycloak:keycloak /home/<USER>
USER keycloak

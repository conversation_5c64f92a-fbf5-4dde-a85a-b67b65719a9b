<Directory "/srv/www/klicktipp/htdocs">
### anti DDoS rules - start

# Block HTTP/1.0 requests to /
# We should ideally block all HTTP/1.1 requests, but:
# - recent attacks (around 2019-07-12) connect to / with HTTP/1.1
# - we should first notify api users to upgrade to HTTP/1.1, as we'll be deprecating HTTP/1.0
RewriteEngine On
RewriteCond %{SERVER_PROTOCOL} ^HTTP/1\.0$
RewriteCond %{REQUEST_URI} ^/$
RewriteRule ^ - [F]

### anti DDos rules - end

#
# Apache/PHP/Drupal settings:
#

# Protect files and directories from prying eyes.
# Deny all requests from Apache 2.4+.
<IfModule mod_authz_core.c>
    <FilesMatch "\.(engine|inc|info|install|make|module|profile|test|po|py|sh|.*sql|theme|tpl(\.php)?|xtmpl)(~|\.sw[op]|\.bak|\.orig|\.save)?$|^(\.(?!well-known).*|Entries.*|Repository|Root|Tag|Template|composer\.(json|lock)|web\.config)$|^#.*#$|\.php(~|\.sw[op]|\.bak|\.orig|\.save)$">
        Require all denied
    </FilesMatch>
</IfModule>

# Don't show directory listings for URLs which map to a directory.
Options -Indexes

# Follow symbolic links in this directory.
Options +FollowSymLinks

# Make Drupal handle any 404 errors.
ErrorDocument 404 /index.php

# Force simple error message for requests for non-existent favicon.ico.
<Files favicon.ico>
  # There is no end quote below, for compatibility with Apache 1.3.
  ErrorDocument 404 "The requested file favicon.ico was not found."
</Files>

# Set the default handler.
DirectoryIndex index.php index.html index.htm

# Override PHP settings that cannot be changed at runtime. See
# sites/default/default.settings.php and drupal_environment_initialize() in
# includes/bootstrap.inc for settings that can be changed at runtime.

# Requires mod_expires to be enabled.
<IfModule mod_expires.c>
  # Enable expirations.
  ExpiresActive On

  # Cache all files for 2 weeks after access (A).
  ExpiresDefault ********

  <FilesMatch \.php$>
    # Do not allow PHP scripts to be cached unless they explicitly send cache
    # headers themselves. Otherwise all scripts would have to overwrite the
    # headers set by mod_expires if they want another caching behavior. This may
    # fail if an error occurs early in the bootstrap process, and it may cause
    # problems if a non-Drupal PHP file is installed in a subdirectory.
    ExpiresActive Off
  </FilesMatch>
</IfModule>

# Various rewrite rules.
<IfModule mod_rewrite.c>
  RewriteEngine on

  # Set "protossl" to "s" if we were accessed via https://.  This is used later
  # if you enable "www." stripping or enforcement, in order to ensure that
  # you don't bounce between http and https.
  RewriteRule ^ - [E=protossl]
  RewriteCond %{HTTPS} on
  RewriteRule ^ - [E=protossl:s]

  # Make sure Authorization HTTP header is available to PHP
  # even when running as CGI or FastCGI.
  RewriteRule ^ - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

  # Block access to "hidden" directories whose names begin with a period. This
  # includes directories used by version control systems such as Subversion or
  # Git to store control files. Files whose names begin with a period, as well
  # as the control files used by CVS, are protected by the FilesMatch directive
  # above.
  #
  # NOTE: This only works when mod_rewrite is loaded. Without mod_rewrite, it is
  # not possible to block access to entire directories from .htaccess, because
  # <DirectoryMatch> is not allowed here.
  #
  # If you do not have mod_rewrite installed, you should remove these
  # directories from your webroot or otherwise protect them from being
  # downloaded.
  RewriteRule "/\.|^\.(?!well-known/)" - [F]

  # If your site can be accessed both with and without the 'www.' prefix, you
  # can use one of the following settings to redirect users to your preferred
  # URL, either WITH or WITHOUT the 'www.' prefix. Choose ONLY one option:
  #
  # To redirect all users to access the site WITH the 'www.' prefix,
  # (http://example.com/... will be redirected to http://www.example.com/...)
  # uncomment the following:
  # RewriteCond %{HTTP_HOST} .
  # RewriteCond %{HTTP_HOST} !^www\. [NC]
  # RewriteRule ^ http%{ENV:protossl}://www.%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
  #
  # To redirect all users to access the site WITHOUT the 'www.' prefix,
  # (http://www.example.com/... will be redirected to http://example.com/...)
  # uncomment the following:
  # RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
  # RewriteRule ^ http%{ENV:protossl}://%1%{REQUEST_URI} [L,R=301]

  # Modify the RewriteBase if you are using Drupal in a subdirectory or in a
  # VirtualDocumentRoot and the rewrite rules are not working properly.
  # For example if your site is at http://example.com/drupal uncomment and
  # modify the following line:
  # RewriteBase /drupal
  #
  # If your site is running in a VirtualDocumentRoot at http://example.com/,
  # uncomment the following line:
  RewriteBase /

  RewriteRule ^docker$ - [R=404,L]
  RewriteRule ^docker/.*$ - [R=404,L]

  RewriteRule ^plugin_includes/.*$ - [R=404,L]

  # Block access to differnt sensitive folders.
  RewriteRule ^backend$ - [R=404,L]
  RewriteRule ^backend/.*$ - [R=404,L]

  RewriteRule ^scripts$ - [R=404,L]
  RewriteRule ^scripts/.*$ - [R=404,L]

  # Block access to /test and /test/anything if not from allowed domains
  # Exception for e2e report directories
  RewriteCond %{REQUEST_URI} !^/test/e2e/report-[^/]+/ [NC]
  RewriteCond %{HTTP_HOST} !^(sim|staging).zauberlist.com$ [NC]
  RewriteCond %{HTTP_HOST} !^(sim|app)(.+?)\.staging\.ktsys\.cloud$ [NC]
  RewriteCond %{HTTP_HOST} !^(sim|app)(.+?)\.dev\.ktsys\.cloud$ [NC]
  RewriteCond %{HTTP_HOST} !^app\.ktlocal\.com$ [NC]
  RewriteRule ^test(/|$) - [R=404,L]

  RewriteRule ^config - [R=404,L]
  RewriteRule ^config/.*$ - [R=404,L]

# Various rewrite rules - Start
# all affiliate links are defined here: https://docs.google.com/spreadsheets/d/1hbibx6SgW2bXPi02w7pDUK6yhImOOmek3adn6s-I0F4/edit#gid=0
  # Redirect Codes:
  # 301 = permanent
  # 302 = temporary (default)

  # Redirect affiliate ID links e.g. www.klick-tipp.com/380
  RewriteCond %{HTTP_HOST} ^www.klick-tipp-staging.com|^klick-tipp-staging.com|^app.klicktipp-staging.com
  RewriteRule ^(\d+)$ https://www.klicktipp-staging.com/de/?a=$1 [R=301,L]

  RewriteRule ^(\d+)$ https://www.klicktipp.com/de/?a=$1 [R=301,L]

  # Redirect goto.php to aMember Server
  RewriteCond %{HTTP_HOST} ^www.klick-tipp-staging.com|^klick-tipp-staging.com|^app.klicktipp-staging.com
  RewriteCond %{REQUEST_URI} ^/goto.php
  RewriteRule (.*) https://app.klicktipp-staging.com/crm%{REQUEST_URI} [R=301,L]

  RewriteCond %{REQUEST_URI} ^/goto.php
  RewriteRule (.*) https://app.klicktipp.com/crm%{REQUEST_URI} [R=301,L]

  # Redirect pricing
  RewriteRule ^pricing$                                       https://www.klicktipp.com/de/pricing/ [R=301,L]

  # Redirect contact
  RewriteRule ^contact$                                       https://www.klicktipp.com/de/kontakt/ [R=301,L]
  RewriteRule ^contact/$                                       https://www.klicktipp.com/de/kontakt/ [R=301,L]

  # special affiliate links
  RewriteRule ^bestellen/(\d+)$                               https://www.klicktipp.com/de/pricing/?a=$1 [R=301,L]
  RewriteRule ^bestellen/enterprise/(\d+)$                    https://www.klicktipp.com/de/pricing/enterprise/?a=$1 [R=301,L]
  RewriteRule ^consultants$                                   https://www.klicktipp.com/de/support/certified-consultants/ [R=301,L]
  RewriteRule ^consultants/.*$                                https://www.klicktipp.com/de/support/certified-consultants/ [R=301,L]
  RewriteRule ^certified-consultants/(\d+)$                   https://www.klicktipp.com/de/schulung/?a=$1 [R=301,L]

  RewriteRule ^kundenstimmen/(\d+)$                           https://app.klicktipp.com/crm/goto.php?goto_id=$1&goto_url=https://www.klicktipp.com/de/erfahrungen/ [R=301,L]

  RewriteRule ^mission$                            		      https://www.klicktipp.com/ [R=301,L]

  RewriteRule ^partnerprogramm/(\d+)$                         https://app.klicktipp.com/crm/goto.php?goto_id=$1&goto_url=https://www.klicktipp.com/de/partnerprogramm/ [R=301,L]
  RewriteRule ^partnerprogramm$                               https://www.klicktipp.com/de/partnerprogramm/ [R=301,L]
  RewriteRule ^partnerprogram$                                https://www.klicktipp.com/de/partnerprogramm/ [R=301,L]

  RewriteRule ^pna                                            https://www.klicktipp.com/de/schulung/?a=4015 [R=301,L]
  RewriteRule ^dawid                                          https://www.digistore24.com/redir/202951/klick-tipp/youtube [R=301,L]

  RewriteRule ^webinar/(\d+)/(.+)$                            https://www.klicktipp.com/de/schulung/?a=$1&email=$2 [R=301,L]
  RewriteRule ^webinar/(\d+)$                                 https://www.klicktipp.com/de/schulung/?a=$1 [R=301,L]

  RewriteRule ^webinar-marketing-automatisierung/(\d+)/(.+)$  https://www.klicktipp.com/de/schulung/?a=$1&email=$2 [R=301,L]
  RewriteRule ^webinar-marketing-automatisierung/(\d+)$       https://www.klicktipp.com/de/schulung/?a=$1 [R=301,L]

  RewriteRule ^academy-2019/(\d+)$                            https://www.klicktipp.com/de/schulung/?a=$1 [R=301,L]
  RewriteRule ^academy-2020/(\d+)$                            https://www.klicktipp.com/de/schulung/?a=$1 [R=301,L]
  RewriteRule ^academy/(\d+)$                                 https://www.klicktipp.com/de/schulung/?a=$1 [R=301,L]

  RewriteRule ^anti-spam/kein-import-.+ber-user-interface$    https://www.klicktipp.com/de/import-policy [R=301,L]

  RewriteRule ^vielen-dank-.{4,5}$ https://www.klicktipp.com/thank-you/ [R=301,L]

  RewriteRule ^partnerprogramm/joint-promotions$              partnerprogramm/lead-cloning [R=301,L]

  RewriteRule ^email-marketing-formel$                          https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^einstieg-in-einen-mega-markt-kurs$               https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^grundlagen-im-klick-tipp-konto-kurs$             https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^digitalisierung-von-visitenkarten-kurs$          https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^termine-automatisieren-kurs$                     https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^vertrieb-4-null-kurs$                            https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^online-reputation-kurs$                          https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^geburtstage-kurs$                                https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^Recruiting-4-null-kurs$                          https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^fachkraeftemangel-war-gestern-kurs$              https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^kundenonboarding-der-extraklasse-kurs$           https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^event-management-kurs$                           https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^leadgenerierung-live-on-stage-kurs$              https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^newsletteranmeldung-40-kurs$                     https://www.klicktipp.com/de/bam [R=301,L]
  RewriteRule ^automatisierung-mit-klicktipp-und-zapier-kurs$   https://www.klicktipp.com/de/bam [R=301,L]

# old root .htaccess -------------------------------------------------------

# i-phone ...
RewriteRule ^(apple-touch-icon.+)$ /misc/img/$1 [R,L]

# redirect old chicklets
RewriteCond %{REQUEST_URI} ^/user/website_integration_chicklet.php
RewriteRule ^user/website_integration_(.+)$ /$1 [R,L]

# POST must be rewritten - redirect with POST is not possible
RewriteCond %{REQUEST_URI} ^/de/form.php$
RewriteRule ^(.+)$ /form.php [L]

# we don't want to loose POST on subscribe.php
RewriteRule ^(de/)?subscribe\.php /backend.php [L,QSA]

#----------------------------------------------------------------

# umleitungen startseite
RewriteCond %{REQUEST_URI} ^/de/$
RewriteRule ^(.+)$ / [R,L]

RewriteCond %{REQUEST_URI} ^/de$
RewriteRule ^(.+)$ / [R,L]

# globale de -> / weiterleitung fuer rueckwaertskompatibilitaet
RewriteCond %{REQUEST_URI} ^/de/(.*)$
RewriteRule ^(.+)$ /%1 [R,L]

# prevent google indexing
<FilesMatch "^(form|opt_confirm|track_link|track_open|unsubscribe|web_browser|spam_report)\.php">
Header set X-Robots-Tag "noindex, nofollow"
</FilesMatch>

# opt_confirm.php <- /bestaetigen/
RewriteRule ^drupal-confirm/(.*)$ opt_confirm.php?p=$1 [L,QSA]
RewriteRule ^drupal-bestaetigen/(.*)$ opt_confirm.php?p=$1 [L,QSA]

RewriteRule ^(confirm|bestaetigen)/.+ backend.php [L]

# track_open.php <- /bilder/
RewriteRule ^images/.* backend.php [L,QSA]
RewriteRule ^bilder/.* backend.php [L,QSA]

# unsubscribe.php <- /abmelden/
RewriteRule ^unsubscribe/(.*)$ unsubscribe.php?p=$1 [L,QSA]
RewriteRule ^abmelden/(.*)$ unsubscribe.php?p=$1 [L,QSA]

# email browser view
RewriteRule ^web/.* backend.php [L,QSA]

# conversion pixel
RewriteRule ^pic/.*/.* backend.php [L,QSA]

# spam_report.php <- /spam/
RewriteRule ^spam/(.*)$ spam_report.php?p=$1 [L,QSA]

RewriteRule ^(info|s)/(.*) backend.php [L,QSA]

# SMS callback migration
RewriteRule ^(nexmosms|nexmobounce|twiliosms|twiliobounce) backend.php [L,QSA]

# IPN migration
RewriteRule ^(digistoreipn|subscriber/digistorecart) backend.php [L,QSA]

# We want to redirect all occurences from "/bcr", "/bcr/" and "/bcr/<FOO>"
# to the marketing page for the Android and iOS app
# Note: The mobile device will call /brc/* if the user has not yet installed our app
# The * is not important anymore since the mobile device already checked it
RewriteCond %{ENV:KLICKTIPP_ENVIRONMENT} =staging
RewriteRule ^bcr/(.*)$ https://www.klicktipp-staging.com/bcr/ [L,R=301]
RewriteCond %{ENV:KLICKTIPP_ENVIRONMENT} =staging
RewriteRule ^bcr$ https://www.klicktipp-staging.com/bcr/ [L,R=301]

RewriteRule ^bcr/(.*)$ https://www.klicktipp.com/bcr/ [L,R=301]
RewriteRule ^bcr$ https://www.klicktipp.com/bcr/ [L,R=301]

# apple is asking for this file without .json extension
# we need the extension though to provide the content-type application/json header
RewriteRule ^apple-app-site-association$ apple-app-site-association.json [L]

## forward angular1 traffic to dedicated angular application
# /build/angular/index.html <- /build/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^build/(.*)$ /build/angular/index.html [L,QSA]

## forward angular2 traffic to dedicated angular application
# /build/angular2/index.html <- /app/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^app/(.*)$ /build/angular2/index.html [L,QSA]

# Public API
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^public/api/v1/(.*)$ backend.php [L,QSA]

# Keycloak User Migration
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^internal/keycloak-user-migration backend.php [L,QSA]

# E2e API
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^e2e/api/(.*)$ backend.php [L,QSA]

# LandingPages
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^landing\-page/([^/]+)/?$ backend.php [L,QSA]

# OIDC Login
RewriteCond %{REQUEST_URI} ^/auth
RewriteRule ^(.+)$ backend.php [L]

# Dashboard redirect
RewriteCond %{REQUEST_URI} ^/dashboard
RewriteRule ^(.+)$ backend.php [L]

# Error pages
RewriteCond %{REQUEST_URI} ^/error-pages/public-key$
RewriteRule ^(.+)$ backend.php [L]

# Plugin pages
RewriteCond %{REQUEST_URI} ^/plugin-pages/public-key$
RewriteRule ^(.+)$ backend.php [L]

# already on backend.php: email|calendar|templates-email|templates-email-global|kt-smart-copy-writer
RewriteCond %{REQUEST_URI} ^/ipa/kt-(?:(?!(automation|automation-email|automation-sms|config|field|field-checkbox|field-date|field-datetime|field-decimal|field-dropdown|field-email|field-html|field-number|field-paragraph|field-single|field-time|field-url|listbuilding|marketingtool|metalabels|newsletter|newsletter-autoresponder|notification-email|notification-sms|outbound|signature|splittest|statistics|tag|tag-apikey|tag-automation-finished|tag-automation-started|tag-chargedback|tag-clicked|tag-converted|tag-digistore-affiliation|tag-email-clicked|tag-email-opened|tag-email-sent|tag-email-viewed|tag-facebook|tag-facebook-audience|tag-forms|tag-kajabi-activated|tag-kajabi-deactivated|tag-opened|tag-outbound-activated|tag-payment|tag-payment-completed|tag-payment-expired|tag-plugin-inbound-finished|tag-plugin-inbound-inprogress|tag-plugin-inbound-ready|tag-plugin-inbound-started|tag-rebill-canceled|tag-rebill-resumed|tag-refunded|tag-request|tag-sent|tag-smartlink|tag-sms-clicked|tag-sms-sent|kt-tag-smslistbuilding|kt-tag-tagging-pixel|tag-viewed|variable)(/|\.|\z)))
RewriteRule ^(.+)$ backend.php [L]

# api endpoints migrated to symfony
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/account/login(\..*)?$ backend.php [L,QSA]
RewriteRule ^api/list([./].*)?$ backend.php [L,QSA]
RewriteRule ^api/tag([./].*)?$ backend.php [L,QSA]
RewriteRule ^api/field([./].*)?$ backend.php [L,QSA]
RewriteRule ^api/group([./].*)?$ backend.php [L,QSA]
RewriteRule ^api/bcrevent([./].*)?$ backend.php [L,QSA]
RewriteRule ^api/bcrsubscriber([./].*)?$ backend.php [L,QSA]
RewriteRule ^api/bcrregex([./].*)?$ backend.php [L,QSA]
RewriteRule ^api/bcraccount([./].*)?$ backend.php [L,QSA]
RewriteRule ^api/blacklist([./].*)?$ backend.php [L,QSA]
RewriteRule ^api/subscriber([./].*)?$ backend.php [L,QSA]
RewriteRule ^pix/.+ backend.php [L,QSA]

# countdown
RewriteRule ^(clock|infourl)/.+ backend.php [L,QSA]

RewriteRule ^vcard/.+ backend.php [L,QSA]
RewriteRule ^signature_vcard/.+ backend.php [L,QSA]
RewriteRule ^wufooipn/ backend.php [L,QSA]
RewriteRule ^wufoothankyou/ backend.php [L,QSA]
RewriteRule ^wufoo/ backend.php [L,QSA]
RewriteRule ^businesscard/.+ backend.php [L,QSA]
RewriteRule ^digit/.+ backend.php [L,QSA]

RewriteRule ^auth/.+ backend.php [L,QSA]

# api account/login_keycloak migrated to symfony
RewriteRule ^api/account/login_keycloak(\.[a-zA-Z0-9]+)?$ backend.php [L,QSA]

<IfModule mod_headers.c>
# Required for angular WebApp.
# Stops index.html from being cached.
<FilesMatch index\.html$>
  Header unset ETag
  Header set Cache-Control "max-age=0, no-cache, no-store, must-revalidate"
  Header set Pragma "no-cache"
  Header set Expires "Wed, 11 Jan 1984 05:00:00 GMT"
</FilesMatch>
</IfModule>


# Rewrite URLs of the form 'x' to the form 'index.php?q=x'.
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !=/favicon.ico
# rewrite rule for server-status to prevent 404 error
RewriteCond %{REQUEST_URI} !=/server-status
RewriteCond %{REQUEST_URI} !=/fpm-status
RewriteRule ^ index.php [L]

# Rules to correctly serve gzip compressed CSS and JS files.
# Requires both mod_rewrite and mod_headers to be enabled.
<IfModule mod_headers.c>
# Serve gzip compressed CSS files if they exist and the client accepts gzip.
RewriteCond %{HTTP:Accept-encoding} gzip
RewriteCond %{REQUEST_FILENAME}\.gz -s
RewriteRule ^(.*)\.css $1\.css\.gz [QSA]

# Serve gzip compressed JS files if they exist and the client accepts gzip.
RewriteCond %{HTTP:Accept-encoding} gzip
RewriteCond %{REQUEST_FILENAME}\.gz -s
RewriteRule ^(.*)\.js $1\.js\.gz [QSA]

# Serve correct content types, and prevent double compression.
RewriteRule \.css\.gz$ - [T=text/css,E=no-gzip:1,E=no-brotli:1]
RewriteRule \.js\.gz$ - [T=text/javascript,E=no-gzip:1,E=no-brotli:1]

<FilesMatch "(\.js\.gz|\.css\.gz)$">
  # Serve correct encoding type.
  Header set Content-Encoding gzip
  # Force proxies to cache gzipped & non-gzipped css/js files separately.
  Header append Vary Accept-Encoding
</FilesMatch>
</IfModule>
</IfModule>

# Various header fixes.
<IfModule mod_headers.c>
  # Disable content sniffing, since it's an attack vector.
  Header always set X-Content-Type-Options nosniff
  # Disable Proxy header, since it's an attack vector.
  RequestHeader unset Proxy

  # prevent indexing api responses by search engines
  Header always set X-Robots-Tag "noindex, nofollow" "expr=%{THE_REQUEST} =~ m#\s/api/#"
</IfModule>
</Directory>

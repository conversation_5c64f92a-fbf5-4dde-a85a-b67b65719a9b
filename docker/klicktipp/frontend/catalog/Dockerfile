ARG TAG=latest
ARG NGINX_BASE_VERSION=1.6.1
FROM 035931995993.dkr.ecr.eu-west-1.amazonaws.com/klicktipp/frontend/nginx-base:${NGINX_BASE_VERSION} AS local

USER root

RUN mkdir -pv /usr/share/nginx/html/build && \
    chown -R nginx:nginx /usr/share/nginx/html/build

# === Copy nginx config ===
COPY docker/klicktipp/frontend/catalog/rootfs/ /

USER nginx

WORKDIR /usr/share/nginx/html


FROM local AS source

# === Copy klicktipp angular sources ===
COPY --chown=nginx:nginx ./app/catalog/dist/catalog/ /usr/share/nginx/html/

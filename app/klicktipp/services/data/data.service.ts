import {Observable, throwError as observableThrowError} from 'rxjs';
import {catchError, map} from 'rxjs/operators';
// @TODO: normalize the responses and throw proper errors from the API
import {Injectable} from '@angular/core';
import {Headers, Http, RequestOptions, Response} from '@angular/http';
import "rxjs/Rx";

@Injectable()
export class DataService {
  // token to identify cockpit api
  private xipa:string;

  constructor(private http:Http) {}

  setToken(t:string) {
    this.xipa = t;
  }

  public getToken():string {
    return this.xipa;
  }

  getOptions() {
    let headers = new Headers();
    headers.append('Content-Type', 'application/json');
    // identify cockpit api
    headers.append('X-IPA', this.xipa);
    return new RequestOptions({headers: headers, withCredentials: true});
  }

  //CRUD index == GET apiPath
  getIndex(apiPath, field:string = '', sort:string = 'asc'):Observable<any> {
    return this.http.get(`/ipa/${apiPath}.json`, this.getOptions()).pipe(
      map(res => {

        res = res.json();

        if (field == '') {
          //no sort, return result as is
          return res;
        }

        //sort and format for select options

        let options = [];
        for (let i in res) {
          options.push({id: i, name: res[i]});
        }

        if (sort == 'desc') {

          options.sort((a, b) => {
            if (typeof a[field] == 'string') {
              return String(b[field]).localeCompare(a[field]);
            }
            return b[field] - a[field];
          });

        }
        else {
          options.sort((a, b) => {
            if (typeof a[field] == 'string') {
              return String(a[field]).localeCompare(b[field]);
            }
            return a[field] - b[field];
          });
        }

        return options;

      }),
      catchError(DataService.handleError),);
  }

  //CRUD action == POST apiPath
  getIndexAdvanced(apiPath, options: any = {}, field: string = '', sort: string = 'asc', action: string = '/index_advanced.json'): Observable<any> {
    return this.http.post('/ipa/' + apiPath + action, this.stringify(options), this.getOptions()).pipe(
      map(res => {

        res = res.json();

        if (field == '') {
          //no sort, return result as is
          return res;
        }

        //sort and format for select options

        let options = [];
        for (let i in res) {
          options.push({id: i, name: res[i]});
        }

        if (sort == 'desc') {

          options.sort((a, b) => {
            if (typeof a[field] == 'string') {
              return String(b[field]).localeCompare(a[field]);
            }
            return b[field] - a[field];
          });

        }
        else {

          options.sort((a, b) => {
            if (typeof a[field] == 'string') {
              return String(a[field]).localeCompare(b[field]);
            }
            return a[field] - b[field];
          });
        }

        return res;

      }),
      catchError(DataService.handleError),);
  }

  //CRUD create == POST apiPath
  create(apiPath: string, data:any):Observable<string> {
    let datajson = this.stringify(data);

    return this.http.post(`/ipa/${apiPath}.json`, datajson, this.getOptions()).pipe(
      map(res => res.json()),
      catchError(DataService.handleError),);
  }

  //CRUD retrieve == GET apiPath/<id>
  retrieve(apiPath, id:number):Observable<any> {
    return this.http.get(`/ipa/${apiPath}/${id}.json`, this.getOptions()).pipe(
      map(res => res.json()),
      catchError(DataService.handleError),);
  }

  //CRUD update == PUT apiPath/<id>
  update(apiPath, id:number, data:any):Observable<string> {
    let datajson = this.stringify(data);

    return this.http.put(`/ipa/${apiPath}/${id}.json`, datajson, this.getOptions()).pipe(
      map(res => res.json()),
      catchError(DataService.handleError),);
  }

  //CRUD delete == DELETE apiPath/<id>
  delete(apiPath, id:number):Observable<string> {
    return this.http.delete(`/ipa/${apiPath}/${id}.json`, this.getOptions()).pipe(
      map(res => res.json()),
      catchError(DataService.handleError),);
  }


  action(apiPath, data:any):Observable<any> {
    let datajson = this.stringify(data);

    return this.http.post(`/ipa/${apiPath}.json`, datajson, this.getOptions()).pipe(
      map(res => res.json()),
      catchError(DataService.handleError),);
  }

  stringify(item: any): any {
    // JSON.stringify cannot handle DOM nodes
    return JSON.stringify(item, (key, value) => {
      if (value instanceof Element) return;
      return value;
    });
  }

  static handleError(error:Response) {
    switch (error.status) {
      case 403:
        return observableThrowError("Access denied");
      case 404:
        return observableThrowError("Not found");
      case 406:
        let message = decodeURIComponent(JSON.parse(error['_body'])[0]);
        return observableThrowError(message);
    }

    return observableThrowError("Server error.");
  }
}

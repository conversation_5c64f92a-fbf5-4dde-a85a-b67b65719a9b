import {first} from 'rxjs/operators';
import {Injectable} from '@angular/core';
import {Observable, BehaviorSubject} from 'rxjs';
import {EntityService} from "../entity.service";
import {DataService} from "../../data/data.service";
import {Metalabel, MetaSearchResult} from '../../../entities/metalabel/metalabel';

@Injectable()
export class MetalabelService {

  static STATUS_READY = 'ready';
  static STATUS_RETRIEVING = 'retrieving';
  static STATUS_RETRIEVED = 'retrieved';

  protected name:string = 'metalabel-service';

  private resultObserver: BehaviorSubject<{status: string, data: MetaSearchResult}>;

  constructor(public crud: DataService) {
  }

  // path to klicktipp api
  apiPath(type: string = ''): string {
    return 'kt-metalabels';
  }


  //CRUD index == GET email
  getIndex(type: string, field: string = '', sort: string = 'asc'): Observable<any[]> {
    return this.crud.getIndex(this.apiPath(type), field, sort);
  }

  //CRUD create == POST email
  //TODO: check for errors, previous signature = create(type: string, name: string)
  create(name: string, type: string ): Observable<string> {
    return this.crud.create(this.apiPath(type), {name: name});
  }

  //CRUD retrieve == GET email/<id>
  get(id: number, type:string = ''): Observable<Metalabel> {
    return this.crud.retrieve(this.apiPath(type), id);
  }

  //CRUD update == PUT email/<id>
  update(entity: any): Observable<string> {
    //@TODO: change value of EntityService.TYPE_ to actual types from api if possible
    let type:string = EntityService.getType(entity);
    return this.crud.update(this.apiPath(type), entity.id, entity);
  }

  //TODO: needed if we always push the complete entity? -> Check all...
  static getUpdateFields(entity:any) : any {

    return {
      'name': entity['name'],
      'description': entity['description'],
    }

  }

  //CRUD delete == DELETE email/<id>
  delete(id: number): Observable<string> {
    return this.crud.delete(this.apiPath(''), id);
  }

  getIndexAdvanced(types: string[] = []): Observable<string> {

    let data = {
      types: types,
    };

    return this.crud.getIndexAdvanced(this.apiPath(''), data);

  }

  addLabels(entities: {id: number, baseType: number, entityType: number}[], labels: string[]):Observable<string> {
    let data = {
      entities: entities,
      labels: labels
    };
    return this.crud.action(this.apiPath() + '/add', data);
  }

  removeLabels(entities: {id: number, baseType: number, entityType: number}[], labels: string[]):Observable<string> {
    let data = {
      entities: entities,
      labels: labels
    };
    return this.crud.action(this.apiPath() + '/remove', data);
  }

  search(mask: object = {}): void { //TODO: create interface for search mask

    this.resultObserver.next({
      status: MetalabelService.STATUS_RETRIEVING,
      data: null
    });

    this.crud.action(this.apiPath('') + '/search', mask).pipe(first()).subscribe(res => {
      this.resultObserver.next({
        status: MetalabelService.STATUS_RETRIEVED,
        data: res
      });
    });

  }

  searchResult(): Observable<{status: string, data: MetaSearchResult}> {

    if (!this.resultObserver) {
      this.resultObserver = new BehaviorSubject({
        status: MetalabelService.STATUS_READY,
        data: null
      });
    }

    return this.resultObserver.asObservable();
  }

  getEditURL(entityID) {
    //Note: EntityService.CLASSNAME_EMAIL works for all email/sms types
    return "/build/metalabel/" + entityID;
  }

  getCreateURL() {
    return "/build/metalabel";
  }

  static getSearchMask(id: number, mask:string ) : string {
    return '';
  }

}

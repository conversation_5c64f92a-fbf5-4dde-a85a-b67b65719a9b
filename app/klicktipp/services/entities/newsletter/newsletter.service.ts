import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {DataService} from "../../data/data.service";

@Injectable()
export class NewsletterService {

  protected name:string = 'newsletter-service';

  constructor(private crud: DataService) {
  }

  // path to klicktipp api
  apiPath(type: string = ''): string {
    return 'kt-newsletter-autoresponder';
  }


  //CRUD index == GET email
  getIndex(type: string, field: string = '', sort: string = 'asc'): Observable<any[]> {
    return this.crud.getIndex(this.apiPath(type), field, sort);
  }

  //CRUD create == POST email
  //TODO: check for errors, previous signature = create(type: string, name: string)
  create(name: string, type: string ): Observable<string> {
    return this.crud.create(this.apiPath(type), {name: name});
  }

  //CRUD retrieve == GET email/<id>
  get(id: number, type:string = ''): Observable<any> {
    return this.crud.retrieve(this.apiPath(type), id);
  }

  //CRUD update == PUT email/<id>
  update(entity: any): Observable<string> {
    let id = entity.campaignid;
    let type = entity.type;
    let data = NewsletterService.getUpdateFields(entity);
    return this.crud.update(this.apiPath(), id, data);
  }

  static getUpdateFields(entity:any) : any {

    return entity;

  }

  //CRUD delete == DELETE email/<id>
  delete(id: number): Observable<string> {
    return this.crud.delete(this.apiPath(''), id);
  }

  getIndexAdvanced(types: string[] = []): Observable<string> {

    let data = {
      types: types,
    };

    return this.crud.getIndexAdvanced(this.apiPath(''), data);

  }

}

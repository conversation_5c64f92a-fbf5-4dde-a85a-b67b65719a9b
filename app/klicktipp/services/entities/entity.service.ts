// @TODO: refactor EntityService to better handle success/error cases
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Automation } from '../../entities/automation/automation';
import { AutomationEmail } from '../../entities/email/email';
import { AutomationSMS } from '../../entities/sms/sms';
import { get } from '../../shared/utilities/object.utility';
//entity services
import { DeprAccountService } from '../account/depr-account.service';
import { TranslateService } from '../translate/translate.service';
import { CampaignService } from './campaign/campaign.service';
import { EmailService } from './email/email.service';
import { FieldService } from './field/field.service';
import { ListbuildingService } from './listbuildings/listbuilding.service';
import { MetalabelService } from './metalabel/metalabel.service';
import { MetaTypesService } from './metalabel/metatypes.service';
import { NewsletterService } from './newsletter/newsletter.service';
import { RulesService } from './rules/rules.service';
import { SplittestService } from './splittest/splittest.service';
import { StatisticsService } from './statistics/statistics.service';
import { TagService } from './tag/tag.service';
import { ToolService } from './tool/tool.service';
import { ToolCalendarService } from './tool/tool_calendar.service';

interface ApiEntityTypes {
  id: string;
  name: string;
}

@Injectable()
export class EntityService {
  //@TODO: move statics to an extended class

  static STATUS_RETRIEVING = 'retrieving';
  static STATUS_RETRIEVED = 'retrieved';
  static STATUS_UPDATING = 'updating';
  static STATUS_UPDATED = 'updated';
  static STATUS_DELETING = 'deleting';
  static STATUS_DELETED = 'deleted';
  static STATUS_RELEASED = 'released';
  static STATUS_ERROR = 'error';

  static LIMIT_CHECK = 'limit check';
  static LIMIT_REACHED = 'limit reached';
  static LIMIT_OK = 'limit ok';

  //entity types
  static TYPE_MANUAL_TAG = 'tag';
  static TYPE_SMARTLINK = 'smartlink';
  static TYPE_SMART_TAG = 'smarttag';
  static TYPE_SMARTTAG_SENT = 'campaign-sent';
  static TYPE_SMARTTAG_OPENED = 'campaign-opened';
  static TYPE_SMARTTAG_CLICKED = 'campaign-clicked';
  static TYPE_SMARTTAG_VIEWED = 'campaign-viewed';
  static TYPE_SMARTTAG_CONVERTED = 'campaign-converted';
  static TYPE_SMARTTAG_OUTBOUND = 'outbound-activated';
  static TYPE_SMARTTAG_KAJABI_ACTIVATE = 'kajabi-activated';
  static TYPE_SMARTTAG_KAJABI_DEACTIVATE = 'kajabi-deactivated';
  static TYPE_SMARTTAG_TAGGING_PIXEL = 'taggingpixel-triggered';
  static TYPE_SMARTTAG_EMAIL_SENT = 'email-sent';
  static TYPE_SMARTTAG_EMAIL_OPENED = 'email-opened';
  static TYPE_SMARTTAG_EMAIL_CLICKED = 'email-clicked';
  static TYPE_SMARTTAG_EMAIL_VIEWED = 'email-viewed';
  static TYPE_SMARTTAG_SMS_SENT = 'sms-sent';
  static TYPE_SMARTTAG_SMS_CLICKED = 'sms-clicked';
  static TYPE_SMARTTAG_AUTOMATION_STARTED = 'automation-started';
  static TYPE_SMARTTAG_AUTOMATION_FINISHED = 'automation-finished';
  static TYPE_SMARTTAG_LISTBUILDING_APIKEY = 'apikey-subscribed';
  static TYPE_SMARTTAG_LISTBUILDING_BUSINESSCARD = 'businesscard-subscribed';
  static TYPE_SMARTTAG_LISTBUILDING_BCREVENT = 'event-subscribed';
  static TYPE_SMARTTAG_LISTBUILDING_REQUEST = 'request-email-subscribed';
  static TYPE_SMARTTAG_LISTBUILDING_SMS = 'request-sms-subscribed';
  static TYPE_SMARTTAG_LISTBUILDING_FORMS = 'form-subscribed';
  static TYPE_SMARTTAG_LISTBUILDING_PAYMENT = 'payment-received';
  static TYPE_SMARTTAG_LISTBUILDING_REFUNDED = 'payment-refunded';
  static TYPE_SMARTTAG_LISTBUILDING_CHARGEDBACK = 'payment-chargeback';
  static TYPE_SMARTTAG_LISTBUILDING_SUBSEQUENT = 'payment-subsequent';
  static TYPE_SMARTTAG_LISTBUILDING_DEFERRED = 'payment-deferred';
  static TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED = 'rebill-canceled';
  static TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED_LAST_DAY = 'rebill-canceled-lastday';
  static TYPE_SMARTTAG_LISTBUILDING_REBILL_RESUMED = 'rebill-resumed';
  static TYPE_SMARTTAG_LISTBUILDING_DIGISTORE_AFFILIATION = 'digistore-affiliation';
  static TYPE_SMARTTAG_LISTBUILDING_LANDING_PAGE_SUBSCRIBED = 'landing-page-subscribed';
  static TYPE_SMARTTAG_PAYMENT_COMPLETED = 'payment-completed';
  static TYPE_SMARTTAG_PAYMENT_EXPIRED = 'payment-expired';
  static TYPE_SMARTTAG_FACEBOOK_AUDIENCE = 'facebook-audience';
  static TYPE_SMARTTAG_PLUGIN_INBOUND_READY = 'plugin-inbound-ready';
  static TYPE_SMARTTAG_PLUGIN_INBOUND_STARTED = 'plugin-inbound-started';
  static TYPE_SMARTTAG_PLUGIN_INBOUND_INPROGRESS = 'plugin-inbound-inprogress';
  static TYPE_SMARTTAG_PLUGIN_INBOUND_FINISHED = 'plugin-inbound-finished';
  static TYPE_AUTOMATION = 'automation';
  static TYPE_RULE = 'rule';
  static TYPE_NEWSLETTER_EMAIL = 'newsletter-email';
  static TYPE_NEWSLETTER_SMS = 'newsletter-sms';
  static TYPE_AUTORESPONDER_EMAIL = 'autoresponder-email';
  static TYPE_AUTORESPONDER_EMAIL_DATETIME = 'autoresponder-email-datetime';
  static TYPE_AUTORESPONDER_EMAIL_BIRTHDAY = 'autoresponder-email-birthday';
  static TYPE_AUTORESPONDER_SMS = 'autoresponder-sms';
  static TYPE_AUTORESPONDER_SMS_DATETIME = 'autoresponder-sms-datetime';
  static TYPE_AUTORESPONDER_SMS_BIRTHDAY = 'autoresponder-sms-birthday';
  static TYPE_EMAIL = 'email';
  static TYPE_SMS = 'sms';
  static TYPE_NOTIFY_EMAIL = 'notify-email';
  static TYPE_NOTIFY_SMS = 'notify-sms';
  static TYPE_EMAIL_NEWSLETTER = 'email-newsletter';
  static TYPE_SMS_NEWSLETTER = 'sms-newsletter';
  static TYPE_FORM_CUSTOM = 'form-custom';
  static TYPE_FORM_INLINE = 'form-inline';
  static TYPE_FORM_WIDGET = 'form-widget';
  static TYPE_FORM_RAW = 'form-raw';
  static TYPE_REQUESTS = 'request-email';
  static TYPE_API = 'apikey';
  static TYPE_SMS_LISTBUILDING_GLOBAL = 'request-sms-global';
  static TYPE_SMS_LISTBUILDING_NEXMO = 'request-sms-nexmo';
  static TYPE_SMS_LISTBUILDING_TWILIO = 'request-sms-twilio';
  static TYPE_DIGISTORE = 'payment-digiStore';
  static TYPE_AFFILICON = 'payment-affilicon';
  static TYPE_CLICKBANK = 'payment-clickbank';
  static TYPE_PAYPAL = 'payment-paypal'; //TODO: renamed from product paypal, check possible errors
  static TYPE_WUFOO = 'wufoo';
  static TYPE_LANDING_PAGE = 'landing-page';
  static TYPE_FORM_LEADPAGES = 'leadpages';
  static TYPE_FORM_OPTIMIZEPRESS = 'optimize-press';
  static TYPE_FORM_WISTIA = 'wistia';
  static TYPE_FORM_THRIVE = 'thrive';
  static TYPE_BUSINESSCARD = 'businesscard';
  static TYPE_BCREVENT = 'event';
  static TYPE_CUSTOMFIELD_SINGLE = 'field-single';
  static TYPE_CUSTOMFIELD_PARAGRAPH = 'field-paragraph';
  static TYPE_CUSTOMFIELD_EMAIL = 'field-email';
  static TYPE_CUSTOMFIELD_NUMBER = 'field-number';
  static TYPE_CUSTOMFIELD_URL = 'field-url';
  static TYPE_CUSTOMFIELD_TIME = 'field-time';
  static TYPE_CUSTOMFIELD_DATE = 'field-date';
  static TYPE_CUSTOMFIELD_DATETIME = 'field-datetime';
  static TYPE_CUSTOMFIELD_HTML = 'field-html';
  static TYPE_CUSTOMFIELD_DECIMAL = 'field-decimal';
  static TYPE_ZAPIER = 'zapier';
  static TYPE_COUNTDOWN = 'countdown';
  static TYPE_KAJABI = 'kajabi';
  static TYPE_TAGGINGPIXEL = 'tagging-pixel';
  static TYPE_TEMPLATE = 'automation-template';
  static TYPE_WEBINAR = 'webinar';
  static TYPE_STATISTICS = 'statistics';
  static TYPE_WEBSITE_EXITLIGHTBOX = 'website-exitlightbox';
  static TYPE_WEBSITE_FEEDBACK = 'website-feedback';
  static TYPE_WEBSITE_ONETIMEOFFER = 'website-onetimeoffer';
  static TYPE_WEBSITE_SOCIALPROOF = 'website-socialproof';
  static TYPE_WEBSITE_SPLITTEST = 'website-splittest';
  static TYPE_OUTBOUND = 'outbound';
  static TYPE_PLUGIN = 'plugin';
  static TYPE_SPLITTEST = 'splittest';
  static TYPE_FACEBOOK_AUDIENCE = 'facebook-audience';
  static TYPE_CALENDAR = 'calendar';
  static TYPE_METALABEL = 'metalabel';
  static TYPE_METATYPE = 'metatype';
  static TYPE_SIGNATURE = 'signature';
  static TYPE_SUBSCRIBER_LISTS = 'subscriber-list';

  //SmartTags
  static SMARTTAG_ALL = { CATEGORY: EntityService.TYPE_SMART_TAG, KEY: 'id' };
  static SMARTTAG_MANUAL_TAG = { CATEGORY: EntityService.TYPE_MANUAL_TAG, KEY: 'id' };
  static SMARTTAG_SMARTLINK = { CATEGORY: EntityService.TYPE_SMARTLINK, KEY: 'id' };
  static SMARTTAG_SENT = { CATEGORY: EntityService.TYPE_SMARTTAG_SENT, KEY: 'SentSmartTagID' };
  static SMARTTAG_OPENED = { CATEGORY: EntityService.TYPE_SMARTTAG_OPENED, KEY: 'OpenedSmartTagID' };
  static SMARTTAG_CLICKED = { CATEGORY: EntityService.TYPE_SMARTTAG_CLICKED, KEY: 'ClickedSmartTagID' };
  static SMARTTAG_VIEWED = { CATEGORY: EntityService.TYPE_SMARTTAG_VIEWED, KEY: 'ViewedSmartTagID' };
  static SMARTTAG_CONVERTED = { CATEGORY: EntityService.TYPE_SMARTTAG_CONVERTED, KEY: 'ConvertedSmartTagID' };
  static SMARTTAG_KAJABI_ACTIVATE = { CATEGORY: EntityService.TYPE_SMARTTAG_KAJABI_ACTIVATE, KEY: 'ActivationSmartTagID' };
  static SMARTTAG_KAJABI_DEACTIVATE = { CATEGORY: EntityService.TYPE_SMARTTAG_KAJABI_DEACTIVATE, KEY: 'DeactivationSmartTagID' };
  static SMARTTAG_OUTBOUND = { CATEGORY: EntityService.TYPE_SMARTTAG_OUTBOUND, KEY: 'ActivationSmartTagID' };
  static SMARTTAG_TAGGING_PIXEL = { CATEGORY: EntityService.TYPE_SMARTTAG_TAGGING_PIXEL, KEY: 'SmartTagID' };
  static SMARTTAG_EMAIL_SENT = { CATEGORY: EntityService.TYPE_SMARTTAG_EMAIL_SENT, KEY: 'EmailSentSmartTagID' };
  static SMARTTAG_EMAIL_OPENED = { CATEGORY: EntityService.TYPE_SMARTTAG_EMAIL_OPENED, KEY: 'EmailOpenedSmartTagID' };
  static SMARTTAG_EMAIL_CLICKED = { CATEGORY: EntityService.TYPE_SMARTTAG_EMAIL_CLICKED, KEY: 'EmailClickedSmartTagID' };
  static SMARTTAG_EMAIL_VIEWED = { CATEGORY: EntityService.TYPE_SMARTTAG_EMAIL_VIEWED, KEY: 'EmailViewedSmartTagID' };
  static SMARTTAG_SMS_SENT = { CATEGORY: EntityService.TYPE_SMARTTAG_SMS_SENT, KEY: 'SMSSentSmartTagID' };
  static SMARTTAG_SMS_CLICKED = { CATEGORY: EntityService.TYPE_SMARTTAG_SMS_CLICKED, KEY: 'SMSClickedSmartTagID' };
  static SMARTTAG_AUTOMATION_STARTED = {
    CATEGORY: EntityService.TYPE_SMARTTAG_AUTOMATION_STARTED,
    KEY: 'AutomationStartedSmartTagID',
  };
  static SMARTTAG_AUTOMATION_FINISHED = {
    CATEGORY: EntityService.TYPE_SMARTTAG_AUTOMATION_FINISHED,
    KEY: 'AutomationFinishedSmartTagID',
  };
  static SMARTTAG_LISTBUILDING_APIKEY = { CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_APIKEY, KEY: 'SmartTagID' };
  static SMARTTAG_LISTBUILDING_BUSINESSCARD = { CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_BUSINESSCARD, KEY: 'SmartTagID' };
  static SMARTTAG_LISTBUILDING_BCREVENT = { CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_BCREVENT, KEY: 'SmartTagID' };
  static SMARTTAG_LISTBUILDING_REQUEST = { CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_REQUEST, KEY: 'SmartTagID' };
  static SMARTTAG_LISTBUILDING_SMS = { CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_SMS, KEY: 'SmartTagID' };
  static SMARTTAG_LISTBUILDING_FORMS = { CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_FORMS, KEY: 'SmartTagID' };
  static SMARTTAG_LISTBUILDING_PAYMENT = { CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_PAYMENT, KEY: 'SmartTagID' };
  static SMARTTAG_LISTBUILDING_REFUNDED = { CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_REFUNDED, KEY: 'RefundSmartTagID' };
  static SMARTTAG_LISTBUILDING_CHARGEDBACK = {
    CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_CHARGEDBACK,
    KEY: 'ChargebackSmartTagID',
  };
  static SMARTTAG_LISTBUILDING_SUBSEQENT = {
    CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_SUBSEQUENT,
    KEY: 'SubsequentSmartTagID',
  };
  static SMARTTAG_LISTBUILDING_DEFERRED = {
    CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_DEFERRED,
    KEY: 'DeferredSmartTagID',
  };
  static SMARTTAG_LISTBUILDING_REBILL_CANCELED = {
    CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED,
    KEY: 'RebillCanceledSmartTagID',
  };
  static SMARTTAG_LISTBUILDING_REBILL_CANCELED_LAST_DAY = {
    CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED_LAST_DAY,
    KEY: 'RebillCanceledLastDaySmartTagID',
  };
  static SMARTTAG_LISTBUILDING_REBILL_RESUMED = {
    CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_RESUMED,
    KEY: 'RebillResumedSmartTagID',
  };
  static SMARTTAG_PAYMENT_COMPLETED = { CATEGORY: EntityService.TYPE_SMARTTAG_PAYMENT_COMPLETED, KEY: 'PaymentCompletedSmartTagID' };
  static SMARTTAG_PAYMENT_EXPIRED = { CATEGORY: EntityService.TYPE_SMARTTAG_PAYMENT_EXPIRED, KEY: 'PaymentExpiredSmartTagID' };
  static SMARTTAG_LISTBUILDING_DIGISTORE_AFFILIATION = {
    CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_DIGISTORE_AFFILIATION,
    KEY: 'AffiliationSmartTagID',
  };
  static SMARTTAG_LISTBUILDING_LANDING_PAGE_SUBSCRIBED = {
    CATEGORY: EntityService.TYPE_SMARTTAG_LISTBUILDING_LANDING_PAGE_SUBSCRIBED,
    KEY: 'SmartTagID',
  };
  static SMARTTAG_FACEBOOK_AUDIENCE = { CATEGORY: EntityService.TYPE_FACEBOOK_AUDIENCE, KEY: 'SmartTagID' };
  //TODO the "key" of plugin smarttags is dynamic (at least if that is the field name of the db data field
  static SMARTTAG_PLUGIN_INBOUND_READY = { CATEGORY: EntityService.TYPE_SMARTTAG_PLUGIN_INBOUND_READY, KEY: 'PluginReadySmartTagID' };
  static SMARTTAG_PLUGIN_INBOUND_STARTED = { CATEGORY: EntityService.TYPE_SMARTTAG_PLUGIN_INBOUND_STARTED, KEY: 'PluginStartedSmartTagID' };
  static SMARTTAG_PLUGIN_INBOUND_INPROGRESS = { CATEGORY: EntityService.TYPE_SMARTTAG_PLUGIN_INBOUND_INPROGRESS, KEY: 'PluginInProgressSmartTagID' };
  static SMARTTAG_PLUGIN_INBOUND_FINISHED = { CATEGORY: EntityService.TYPE_SMARTTAG_PLUGIN_INBOUND_FINISHED, KEY: 'PluginFinishedSmartTagID' };

  static GROUP_LISTBUILDING = 'listbuilding';
  static GROUP_LISTUILDING_KLICKTIPP = 'listbuilding-klicktipp';
  static GROUP_LISTUILDING_PAYMENTS = 'listbuilding-payments';
  static GROUP_LISTUILDING_INTEGRATION = 'listbuilding-integration';
  static GROUP_LISTUILDING_FORMS = 'listbuilding-forms';
  static GROUP_LISTUILDING_SMS = 'listbuilding-sms';
  static GROUP_EMAILS = 'emails';
  static GROUP_FIELDS = 'fields';
  static GROUP_MARKETINGTOOLS = 'marketingtools';
  static GROUP_CAMPAIGNS = 'campaigns'; //TODO deprecated: after merging autoresponders there will be only Newsletters (no group)

  //PHP Class names
  static CLASSNAME_MANUALTAG = 'Tag';
  static CLASSNAME_SMARTLINK = 'SmartLink';
  static CLASSNAME_CAMPAIGN = 'Campaigns';
  static CLASSNAME_OUTBOUND = 'ToolOutboundGeneral';
  static CLASSNAME_TAGGING_PIXEL = 'ToolTaggingPixel';
  static CLASSNAME_EMAIL = 'EmailsAutomationEmail';
  static CLASSNAME_SMS = 'EmailsAutomationSMS';
  static CLASSNAME_NOTIFY_EMAIL = 'EmailsNotificationEmail';
  static CLASSNAME_NOTIFY_SMS = 'EmailsNotificationSMS';
  static CLASSNAME_AUTOMATION = 'CampaignsProcessFlow';
  static CLASSNAME_RULE = 'CampaignsRule';
  static CLASSNAME_LISTBUILDING = 'Listbuildings';
  static CLASSNAME_CUSTOMFIELDS = 'CustomFields';
  static CLASSNAME_STATISTICS = 'ToolStatistics';
  static CLASSNAME_FACEBOOK_AUDIENCE = 'ToolFacebookAudience';
  static CLASSNAME_MARKETING_TOOLS = 'MarketingTools';
  static CLASSNAME_SIGNATURES = 'Signatures';
  static CLASSNAME_SUBSCRIBER_LISTS = 'Lists';

  //translatable strings for the listbuilding types as in PHP Listbuildings::$DisplayListbuildingType
  static DISPLAY_TYPE = {
    [EntityService.TYPE_AUTOMATION]: TranslateService.translatable('entity::type::Automation'),
    [EntityService.TYPE_RULE]: TranslateService.translatable('entity::type::Rule'),
    [EntityService.TYPE_EMAIL]: TranslateService.translatable('entity::type::Automation Email'),
    [EntityService.TYPE_SMS]: TranslateService.translatable('entity::type::Automation SMS'),
    [EntityService.TYPE_NOTIFY_EMAIL]: TranslateService.translatable('entity::type::Notification Email'),
    [EntityService.TYPE_NOTIFY_SMS]: TranslateService.translatable('entity::type::Notification SMS'),
    [EntityService.TYPE_EMAIL_NEWSLETTER]: TranslateService.translatable('entity::type::Newsletter/Autoresponder Email'),
    [EntityService.TYPE_SMS_NEWSLETTER]: TranslateService.translatable('entity::type::Newsletter/Autoresponder SMS'),
    [EntityService.TYPE_NEWSLETTER_EMAIL]: TranslateService.translatable('entity::type::Email Newsletter'),
    [EntityService.TYPE_NEWSLETTER_SMS]: TranslateService.translatable('entity::type::SMS Newsletter'),
    [EntityService.TYPE_AUTORESPONDER_EMAIL]: TranslateService.translatable('entity::type::Email Autoresponder'),
    [EntityService.TYPE_AUTORESPONDER_EMAIL_DATETIME]: TranslateService.translatable('entity::type::Email Autoresponder (Field)'),
    [EntityService.TYPE_AUTORESPONDER_EMAIL_BIRTHDAY]: TranslateService.translatable('entity::type::Email Autoresponder (Birthday)'),
    [EntityService.TYPE_AUTORESPONDER_SMS]: TranslateService.translatable('entity::type::SMS Autoresponder'),
    [EntityService.TYPE_AUTORESPONDER_SMS_DATETIME]: TranslateService.translatable('entity::type::SMS Autoresponder (Field)'),
    [EntityService.TYPE_AUTORESPONDER_SMS_BIRTHDAY]: TranslateService.translatable('entity::type::SMS Autoresponder (Birthday)'),
    [EntityService.TYPE_CUSTOMFIELD_SINGLE]: TranslateService.translatable('entity::type::Single'),
    [EntityService.TYPE_CUSTOMFIELD_PARAGRAPH]: TranslateService.translatable('entity::type::Paragraph'),
    [EntityService.TYPE_CUSTOMFIELD_EMAIL]: TranslateService.translatable('entity::type::Email'),
    [EntityService.TYPE_CUSTOMFIELD_NUMBER]: TranslateService.translatable('entity::type::Number'),
    [EntityService.TYPE_CUSTOMFIELD_URL]: TranslateService.translatable('entity::type::URL'),
    [EntityService.TYPE_CUSTOMFIELD_DATE]: TranslateService.translatable('entity::type::Date'),
    [EntityService.TYPE_CUSTOMFIELD_TIME]: TranslateService.translatable('entity::type::Time'),
    [EntityService.TYPE_CUSTOMFIELD_DATETIME]: TranslateService.translatable('entity::type::Datetime'),
    [EntityService.TYPE_CUSTOMFIELD_HTML]: TranslateService.translatable('entity::type::HTML'),
    [EntityService.TYPE_CUSTOMFIELD_DECIMAL]: TranslateService.translatable('entity::type::Decimal'),
    [EntityService.TYPE_FORM_CUSTOM]: TranslateService.translatable('entity::type::Subscription form - Custom'),
    [EntityService.TYPE_FORM_RAW]: TranslateService.translatable('entity::type::Subscription form - Raw'),
    [EntityService.TYPE_FORM_INLINE]: TranslateService.translatable('entity::type::Subscription form - Inline'),
    [EntityService.TYPE_FORM_LEADPAGES]: TranslateService.translatable('entity::type::Subscription form - Leadpages'),
    [EntityService.TYPE_FORM_WISTIA]: TranslateService.translatable('entity::type::Subscription form - Wistia Turnstile'),
    [EntityService.TYPE_FORM_OPTIMIZEPRESS]: TranslateService.translatable('entity::type::Subscription form - OptimizePress'),
    [EntityService.TYPE_FORM_THRIVE]: TranslateService.translatable('entity::type::Subscription form - Thrive'),
    [EntityService.TYPE_FORM_WIDGET]: TranslateService.translatable('entity::type::Subscription form - Widget'),
    [EntityService.TYPE_REQUESTS]: TranslateService.translatable('entity::type::Request'),
    [EntityService.TYPE_LANDING_PAGE]: TranslateService.translatable('entity::type::Landing page'),
    [EntityService.TYPE_PAYPAL]: TranslateService.translatable('entity::type::PayPal product'),
    [EntityService.TYPE_DIGISTORE]: TranslateService.translatable('entity::type::DigiStore24 product'),
    [EntityService.TYPE_AFFILICON]: TranslateService.translatable('entity::type::AffiliCon product'),
    [EntityService.TYPE_CLICKBANK]: TranslateService.translatable('entity::type::Clickbank product'),
    [EntityService.TYPE_API]: TranslateService.translatable('entity::type::API key'),
    [EntityService.TYPE_WUFOO]: TranslateService.translatable('entity::type::Wufoo Subscription form'),
    [EntityService.TYPE_SMS_LISTBUILDING_GLOBAL]: TranslateService.translatable('entity::type::SMS Listbuilding'),
    [EntityService.TYPE_SMS_LISTBUILDING_NEXMO]: TranslateService.translatable('entity::type::SMS Listbuilding Nexmo'),
    [EntityService.TYPE_SMS_LISTBUILDING_TWILIO]: TranslateService.translatable('entity::type::SMS Listbuilding Twilio'),
    [EntityService.TYPE_BUSINESSCARD]: TranslateService.translatable('entity::type::Business card'),
    [EntityService.TYPE_BCREVENT]: TranslateService.translatable('entity::type::Business Card Reader Event'),
    [EntityService.TYPE_SMART_TAG]: TranslateService.translatable('entity::type::Smart Tag'),
    [EntityService.TYPE_MANUAL_TAG]: TranslateService.translatable('entity::type::Manual Tag'),
    [EntityService.TYPE_SMARTLINK]: TranslateService.translatable('entity::type::Smart Link'),
    [EntityService.TYPE_SMARTTAG_SENT]: TranslateService.translatable('entity::smarttag::Newsletter/Autoresponser - Sent'),
    [EntityService.TYPE_SMARTTAG_OPENED]: TranslateService.translatable('entity::smarttag::Newsletter/Autoresponser - Opened'),
    [EntityService.TYPE_SMARTTAG_CLICKED]: TranslateService.translatable('entity::smarttag::Newsletter/Autoresponser - Link clicked'),
    [EntityService.TYPE_SMARTTAG_VIEWED]: TranslateService.translatable('entity::smarttag::Newsletter/Autoresponser - Browser view'),
    [EntityService.TYPE_SMARTTAG_CONVERTED]: TranslateService.translatable('entity::smarttag::Newsletter/Autoresponser - Conversion'),
    [EntityService.TYPE_SMARTTAG_KAJABI_ACTIVATE]: TranslateService.translatable('entity::smarttag::Kajabi Activation'),
    [EntityService.TYPE_SMARTTAG_KAJABI_DEACTIVATE]: TranslateService.translatable('entity::smarttag::Kajabi Deactivation'),
    [EntityService.TYPE_SMARTTAG_OUTBOUND]: TranslateService.translatable('entity::smarttag::Outbound Activation'),
    [EntityService.TYPE_SMARTTAG_TAGGING_PIXEL]: TranslateService.translatable('entity::smarttag::Tagging Pixel'),
    [EntityService.TYPE_SMARTTAG_EMAIL_SENT]: TranslateService.translatable('entity::smarttag::Email - Sent'),
    [EntityService.TYPE_SMARTTAG_EMAIL_OPENED]: TranslateService.translatable('entity::smarttag::Email - Opened'),
    [EntityService.TYPE_SMARTTAG_EMAIL_CLICKED]: TranslateService.translatable('entity::smarttag::Email - Link clicked'),
    [EntityService.TYPE_SMARTTAG_EMAIL_VIEWED]: TranslateService.translatable('entity::smarttag::Email - Browser view'),
    [EntityService.TYPE_SMARTTAG_SMS_SENT]: TranslateService.translatable('entity::smarttag::SMS - Sent'),
    [EntityService.TYPE_SMARTTAG_SMS_CLICKED]: TranslateService.translatable('entity::smarttag::SMS - Link clicked'),
    [EntityService.TYPE_SMARTTAG_AUTOMATION_STARTED]: TranslateService.translatable('entity::smarttag::Automation started'),
    [EntityService.TYPE_SMARTTAG_AUTOMATION_FINISHED]: TranslateService.translatable('entity::smarttag::Automation finished'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_APIKEY]: TranslateService.translatable('entity::smarttag::Subscribed by API'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_BUSINESSCARD]: TranslateService.translatable('entity::smarttag::Subscribed by Business Card Reader'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_BCREVENT]: TranslateService.translatable('entity::smarttag::Subscribed by Business Card Reader Event'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REQUEST]: TranslateService.translatable('entity::smarttag::Subscribed by Email Request'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_SMS]: TranslateService.translatable('entity::smarttag::Subscribed by SMS Request'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_FORMS]: TranslateService.translatable('entity::smarttag::Subscribed by Form'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_PAYMENT]: TranslateService.translatable('entity::smarttag::Subscribed by Payment'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REFUNDED]: TranslateService.translatable('entity::smarttag::Payment Refunded'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_CHARGEDBACK]: TranslateService.translatable('entity::smarttag::Payment Chargedback'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED]: TranslateService.translatable('entity::smarttag::Rebill Canceled'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED_LAST_DAY]: TranslateService.translatable('entity::smarttag::Rebill Canceled Last Day'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_RESUMED]: TranslateService.translatable('entity::smarttag::Rebill Resumed'),
    [EntityService.TYPE_SMARTTAG_PAYMENT_COMPLETED]: TranslateService.translatable('entity::smarttag::Payment Completed'),
    [EntityService.TYPE_SMARTTAG_PAYMENT_EXPIRED]: TranslateService.translatable('entity::smarttag::Payment Expired'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_DIGISTORE_AFFILIATION]: TranslateService.translatable('entity::smarttag::Digistore Affiliation'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_LANDING_PAGE_SUBSCRIBED]: TranslateService.translatable('entity::smarttag::Landing page subscribed'),
    [EntityService.TYPE_SMARTTAG_PLUGIN_INBOUND_READY]: TranslateService.translatable('entity::smarttag::Plugin Inbound Ready'),
    [EntityService.TYPE_SMARTTAG_PLUGIN_INBOUND_STARTED]: TranslateService.translatable('entity::smarttag::Plugin Inbound Started'),
    [EntityService.TYPE_SMARTTAG_PLUGIN_INBOUND_INPROGRESS]: TranslateService.translatable('entity::smarttag::Plugin Inbound In Progress'),
    [EntityService.TYPE_SMARTTAG_PLUGIN_INBOUND_FINISHED]: TranslateService.translatable('entity::smarttag::Plugin Inbound Finished'),
    [EntityService.TYPE_FACEBOOK_AUDIENCE]: TranslateService.translatable('entity::smarttag::Added to Facebook Audience'),
    [EntityService.TYPE_COUNTDOWN]: TranslateService.translatable('entity::type::Countdown'),
    [EntityService.TYPE_KAJABI]: TranslateService.translatable('entity::type::Kajabi'),
    [EntityService.TYPE_OUTBOUND]: TranslateService.translatable('entity::type::Outbound'),
    [EntityService.TYPE_PLUGIN]: TranslateService.translatable('entity::type::Plugin'),
    [EntityService.TYPE_TAGGINGPIXEL]: TranslateService.translatable('entity::type::Tagging Pixel'),
    [EntityService.TYPE_TEMPLATE]: TranslateService.translatable('entity::type::Automation Template'),
    [EntityService.TYPE_WEBINAR]: TranslateService.translatable('entity::type::Webinar'),
    [EntityService.TYPE_ZAPIER]: TranslateService.translatable('entity::type::Zapier'),
    [EntityService.TYPE_STATISTICS]: TranslateService.translatable('entity::type::Statistics'),
    [EntityService.TYPE_WEBSITE_EXITLIGHTBOX]: TranslateService.translatable('entity::type::Exit Lightbox'),
    [EntityService.TYPE_WEBSITE_FEEDBACK]: TranslateService.translatable('entity::type::Feedback Form'),
    [EntityService.TYPE_WEBSITE_ONETIMEOFFER]: TranslateService.translatable('entity::type::One Time Offer'),
    [EntityService.TYPE_WEBSITE_SOCIALPROOF]: TranslateService.translatable('entity::type::Social Proof Counter'),
    [EntityService.TYPE_WEBSITE_SPLITTEST]: TranslateService.translatable('entity::type::Website Splittest'),
    [EntityService.TYPE_SPLITTEST]: TranslateService.translatable('entity::type::Splittest'),
    [EntityService.TYPE_CALENDAR]: TranslateService.translatable('entity::type::Calendar'),
    [EntityService.TYPE_METALABEL]: TranslateService.translatable('entity::type::Metalabel'),
    [EntityService.TYPE_SIGNATURE]: TranslateService.translatable('entity::type::Signature'),
    [EntityService.TYPE_SUBSCRIBER_LISTS]: TranslateService.translatable('entity::type::Double OptIn Process'),
  };

  static DISPLAY_CREATE = {
    [EntityService.TYPE_AUTOMATION]: TranslateService.translatable('entity::create::Create new Automation'),
    [EntityService.TYPE_RULE]: TranslateService.translatable('entity::create::Create new Rule'),
    [EntityService.TYPE_EMAIL]: TranslateService.translatable('entity::create::Create new Automation Email'),
    [EntityService.TYPE_SMS]: TranslateService.translatable('entity::create::Create new Automation SMS'),
    [EntityService.TYPE_NOTIFY_EMAIL]: TranslateService.translatable('entity::create::Create new Notification Email'),
    [EntityService.TYPE_NOTIFY_SMS]: TranslateService.translatable('entity::create::Create new Notification SMS'),
    [EntityService.TYPE_EMAIL_NEWSLETTER]: TranslateService.translatable('entity::create::Create new Newsletter/Autoresponder Email'),
    [EntityService.TYPE_SMS_NEWSLETTER]: TranslateService.translatable('entity::create::Create new Newsletter/Autoresponder SMS'),
    [EntityService.TYPE_NEWSLETTER_EMAIL]: TranslateService.translatable('entity::create::Create new Email Newsletter'),
    [EntityService.TYPE_NEWSLETTER_SMS]: TranslateService.translatable('entity::create::Create new SMS Newsletter'),
    [EntityService.TYPE_AUTORESPONDER_EMAIL]: TranslateService.translatable('entity::create::Create new Email Autoresponder'),
    [EntityService.TYPE_AUTORESPONDER_EMAIL_DATETIME]: TranslateService.translatable('entity::create::Create new Email Autoresponder'),
    [EntityService.TYPE_AUTORESPONDER_EMAIL_BIRTHDAY]: TranslateService.translatable('entity::create::Create new Email Autoresponder'),
    [EntityService.TYPE_AUTORESPONDER_SMS]: TranslateService.translatable('entity::create::Create new SMS Autoresponder'),
    [EntityService.TYPE_AUTORESPONDER_SMS_DATETIME]: TranslateService.translatable('entity::create::Create new SMS Autoresponder'),
    [EntityService.TYPE_AUTORESPONDER_SMS_BIRTHDAY]: TranslateService.translatable('entity::create::Create new SMS Autoresponder'),
    [EntityService.TYPE_CUSTOMFIELD_SINGLE]: TranslateService.translatable('entity::create::Create new Custom Field (Single)'),
    [EntityService.TYPE_CUSTOMFIELD_PARAGRAPH]: TranslateService.translatable('entity::create::Create new Custom Field (Paragraph)'),
    [EntityService.TYPE_CUSTOMFIELD_EMAIL]: TranslateService.translatable('entity::create::Create new Custom Field (Email)'),
    [EntityService.TYPE_CUSTOMFIELD_NUMBER]: TranslateService.translatable('entity::create::Create new Custom Field (Number)'),
    [EntityService.TYPE_CUSTOMFIELD_URL]: TranslateService.translatable('entity::create::Create new Custom Field (URL)'),
    [EntityService.TYPE_CUSTOMFIELD_DATE]: TranslateService.translatable('entity::create::Create new Custom Field (Date)'),
    [EntityService.TYPE_CUSTOMFIELD_TIME]: TranslateService.translatable('entity::create::Create new Custom Field (Time)'),
    [EntityService.TYPE_CUSTOMFIELD_DATETIME]: TranslateService.translatable('entity::create::Create new Custom Field (Datetime)'),
    [EntityService.TYPE_CUSTOMFIELD_HTML]: TranslateService.translatable('entity::create::Create new Custom Field (HTML)'),
    [EntityService.TYPE_CUSTOMFIELD_DECIMAL]: TranslateService.translatable('entity::create::Create new Custom Field (Decimal)'),
    [EntityService.TYPE_FORM_CUSTOM]: TranslateService.translatable('entity::create::Create new Subscription form - Custom'),
    [EntityService.TYPE_FORM_RAW]: TranslateService.translatable('entity::create::Create new Subscription form - Raw'),
    [EntityService.TYPE_FORM_INLINE]: TranslateService.translatable('entity::create::Create new Subscription form - Inline'),
    [EntityService.TYPE_FORM_LEADPAGES]: TranslateService.translatable('entity::create::Create new Subscription form - Leadpages'),
    [EntityService.TYPE_FORM_WISTIA]: TranslateService.translatable('entity::create::Create new Subscription form - Wistia Turnstile'),
    [EntityService.TYPE_FORM_OPTIMIZEPRESS]: TranslateService.translatable('entity::create::Create new Subscription form - OptimizePress'),
    [EntityService.TYPE_FORM_THRIVE]: TranslateService.translatable('entity::create::Create new Subscription form - Thrive'),
    [EntityService.TYPE_FORM_WIDGET]: TranslateService.translatable('entity::create::Create new Subscription form - Widget'),
    [EntityService.TYPE_REQUESTS]: TranslateService.translatable('entity::create::Create new Request'),
    [EntityService.TYPE_LANDING_PAGE]: TranslateService.translatable('entity::create::Create new Landing page'),
    [EntityService.TYPE_PAYPAL]: TranslateService.translatable('entity::create::Create new PayPal product'),
    [EntityService.TYPE_DIGISTORE]: TranslateService.translatable('entity::create::Create new DigiStore24 product'),
    [EntityService.TYPE_AFFILICON]: TranslateService.translatable('entity::create::Create new AffiliCon product'),
    [EntityService.TYPE_CLICKBANK]: TranslateService.translatable('entity::create::Create new Clickbank product'),
    [EntityService.TYPE_API]: TranslateService.translatable('entity::create::Create new API key'),
    [EntityService.TYPE_WUFOO]: TranslateService.translatable('entity::create::Create new Wufoo Subscription form'),
    [EntityService.TYPE_SMS_LISTBUILDING_GLOBAL]: TranslateService.translatable('entity::create::Create new SMS Listbuilding'),
    [EntityService.TYPE_SMS_LISTBUILDING_NEXMO]: TranslateService.translatable('entity::create::Create new SMS Listbuilding Nexmo'),
    [EntityService.TYPE_SMS_LISTBUILDING_TWILIO]: TranslateService.translatable('entity::create::Create new SMS Listbuilding Twilio'),
    [EntityService.TYPE_BUSINESSCARD]: TranslateService.translatable('entity::create::Create new Business card'),
    [EntityService.TYPE_BCREVENT]: TranslateService.translatable('entity::create::Create new Business Card Reader Event'),
    [EntityService.TYPE_SMART_TAG]: TranslateService.translatable('entity::create::Create new Smart Tag'),
    [EntityService.TYPE_MANUAL_TAG]: TranslateService.translatable('entity::create::Create new Manual Tag'),
    [EntityService.TYPE_SMARTLINK]: TranslateService.translatable('entity::create::Create new Smart Link'),
    [EntityService.TYPE_SMARTTAG_SENT]: TranslateService.translatable('entity::create::Create new Newsletter/Autoresponser'),
    [EntityService.TYPE_SMARTTAG_OPENED]: TranslateService.translatable('entity::create::Create new Newsletter/Autoresponser'),
    [EntityService.TYPE_SMARTTAG_CLICKED]: TranslateService.translatable('entity::create::Create new Newsletter/Autoresponser'),
    [EntityService.TYPE_SMARTTAG_VIEWED]: TranslateService.translatable('entity::create::Create new Newsletter/Autoresponser'),
    [EntityService.TYPE_SMARTTAG_CONVERTED]: TranslateService.translatable('entity::create::Create new Newsletter/Autoresponser'),
    [EntityService.TYPE_SMARTTAG_KAJABI_ACTIVATE]: TranslateService.translatable('entity::create::Create new Kajabi'),
    [EntityService.TYPE_SMARTTAG_KAJABI_DEACTIVATE]: TranslateService.translatable('entity::create::Create new Kajabi'),
    [EntityService.TYPE_SMARTTAG_OUTBOUND]: TranslateService.translatable('entity::create::Create new Outbound'),
    [EntityService.TYPE_SMARTTAG_TAGGING_PIXEL]: TranslateService.translatable('entity::create::Create new Tagging Pixel'),
    [EntityService.TYPE_SMARTTAG_EMAIL_SENT]: TranslateService.translatable('entity::create::Create new Email'),
    [EntityService.TYPE_SMARTTAG_EMAIL_OPENED]: TranslateService.translatable('entity::create::Create new Email'),
    [EntityService.TYPE_SMARTTAG_EMAIL_CLICKED]: TranslateService.translatable('entity::create::Create new Email'),
    [EntityService.TYPE_SMARTTAG_EMAIL_VIEWED]: TranslateService.translatable('entity::create::Create new Email'),
    [EntityService.TYPE_SMARTTAG_SMS_SENT]: TranslateService.translatable('entity::create::Create new SMS'),
    [EntityService.TYPE_SMARTTAG_SMS_CLICKED]: TranslateService.translatable('entity::create::Create new SMS'),
    [EntityService.TYPE_SMARTTAG_AUTOMATION_STARTED]: TranslateService.translatable('entity::create::Create new Automation'),
    [EntityService.TYPE_SMARTTAG_AUTOMATION_FINISHED]: TranslateService.translatable('entity::create::Create new Automation'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_APIKEY]: TranslateService.translatable('entity::create::Create new API Key'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_BUSINESSCARD]: TranslateService.translatable('entity::create::Create new Business Card Reader'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_BCREVENT]: TranslateService.translatable('entity::create::Create new Business Card Reader Event'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REQUEST]: TranslateService.translatable('entity::create::Create new Email Request'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_SMS]: TranslateService.translatable('entity::create::Create new SMS Request'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_FORMS]: TranslateService.translatable('entity::create::Create new Subscription Form'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_PAYMENT]: TranslateService.translatable('entity::create::Create new Product'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REFUNDED]: TranslateService.translatable('entity::create::Create new Product'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_CHARGEDBACK]: TranslateService.translatable('entity::create::Create new Product'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED]: TranslateService.translatable('entity::create::Create new Product'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED_LAST_DAY]: TranslateService.translatable('entity::create::Create new Product'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_RESUMED]: TranslateService.translatable('entity::create::Create new Product'),
    [EntityService.TYPE_SMARTTAG_PAYMENT_COMPLETED]: TranslateService.translatable('entity::create::Create new Product'),
    [EntityService.TYPE_SMARTTAG_PAYMENT_EXPIRED]: TranslateService.translatable('entity::create::Create new Product'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_DIGISTORE_AFFILIATION]: TranslateService.translatable('entity::create::Create new Product'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_LANDING_PAGE_SUBSCRIBED]: TranslateService.translatable('entity::create::Create new Landing page'),
    [EntityService.TYPE_FACEBOOK_AUDIENCE]: TranslateService.translatable('entity::create::Create new Facebook Audience'),
    [EntityService.TYPE_COUNTDOWN]: TranslateService.translatable('entity::create::Create new Countdown'),
    [EntityService.TYPE_KAJABI]: TranslateService.translatable('entity::create::Create new Kajabi'),
    [EntityService.TYPE_OUTBOUND]: TranslateService.translatable('entity::create::Create new Outbound'),
    [EntityService.TYPE_PLUGIN]: TranslateService.translatable('entity::create::Create new Plugin'),
    [EntityService.TYPE_TAGGINGPIXEL]: TranslateService.translatable('entity::create::Create new Tagging Pixel'),
    [EntityService.TYPE_TEMPLATE]: TranslateService.translatable('entity::create::Create new Automation Template'),
    [EntityService.TYPE_WEBINAR]: TranslateService.translatable('entity::create::Create new Webinar'),
    [EntityService.TYPE_ZAPIER]: TranslateService.translatable('entity::create::Create new Zapier'),
    [EntityService.TYPE_STATISTICS]: TranslateService.translatable('entity::create::Create new Statistics'),
    [EntityService.TYPE_WEBSITE_EXITLIGHTBOX]: TranslateService.translatable('entity::create::Create new Exit Lightbox'),
    [EntityService.TYPE_WEBSITE_FEEDBACK]: TranslateService.translatable('entity::create::Create new Feedback Form'),
    [EntityService.TYPE_WEBSITE_ONETIMEOFFER]: TranslateService.translatable('entity::create::Create new One Time Offer'),
    [EntityService.TYPE_WEBSITE_SOCIALPROOF]: TranslateService.translatable('entity::create::Create new Social Proof Counter'),
    [EntityService.TYPE_WEBSITE_SPLITTEST]: TranslateService.translatable('entity::create::Create new Website Splittest'),
    [EntityService.TYPE_SPLITTEST]: TranslateService.translatable('entity::create::Create new Splittest'),
    [EntityService.TYPE_CALENDAR]: TranslateService.translatable('entity::create::Create new Calendar'),
    [EntityService.TYPE_METALABEL]: TranslateService.translatable('entity::create::Create new Metalabel'),
    [EntityService.TYPE_SIGNATURE]: TranslateService.translatable('entity::create::Create new Signature'),
    [EntityService.TYPE_SUBSCRIBER_LISTS]: TranslateService.translatable('entity::create::Create new Double OptIn Process'),
  };

  static DISPLAY_ENTITY_ICON = {
    [EntityService.TYPE_CUSTOMFIELD_SINGLE]: 'glyph-text',
    [EntityService.TYPE_CUSTOMFIELD_PARAGRAPH]: 'glyph-text-multiple',
    [EntityService.TYPE_CUSTOMFIELD_EMAIL]: 'glyph-email-sign',
    [EntityService.TYPE_CUSTOMFIELD_NUMBER]: 'glyph-number-sign',
    [EntityService.TYPE_CUSTOMFIELD_URL]: 'glyph-url',
    [EntityService.TYPE_CUSTOMFIELD_TIME]: 'glyph-sandclock',
    [EntityService.TYPE_CUSTOMFIELD_DATE]: 'glyph-calendar',
    [EntityService.TYPE_CUSTOMFIELD_DATETIME]: 'glyph-calendar-clock-2',
    [EntityService.TYPE_CUSTOMFIELD_HTML]: 'glyph-html',
    [EntityService.TYPE_CUSTOMFIELD_DECIMAL]: 'glyph-decimal-dot',
    [EntityService.TYPE_AUTOMATION]: 'glyph-automation',
    [EntityService.TYPE_RULE]: 'glyph-automation',
    [EntityService.TYPE_EMAIL]: 'glyph-email',
    [EntityService.TYPE_SMS]: 'glyph-sms',
    [EntityService.TYPE_NOTIFY_EMAIL]: 'glyph-email-bell',
    [EntityService.TYPE_NOTIFY_SMS]: 'glyph-sms-bell',
    [EntityService.TYPE_NEWSLETTER_EMAIL]: 'glyph-email',
    [EntityService.TYPE_NEWSLETTER_SMS]: 'glyph-sms',
    [EntityService.TYPE_EMAIL_NEWSLETTER]: 'glyph-email',
    [EntityService.TYPE_SMS_NEWSLETTER]: 'glyph-sms',
    [EntityService.TYPE_AUTORESPONDER_EMAIL]: 'glyph-email-gear',
    [EntityService.TYPE_AUTORESPONDER_EMAIL_DATETIME]: 'glyph-email-gear',
    [EntityService.TYPE_AUTORESPONDER_EMAIL_BIRTHDAY]: 'glyph-email-gear',
    [EntityService.TYPE_AUTORESPONDER_SMS]: 'glyph-sms-gear',
    [EntityService.TYPE_AUTORESPONDER_SMS_DATETIME]: 'glyph-sms-gear',
    [EntityService.TYPE_AUTORESPONDER_SMS_BIRTHDAY]: 'glyph-sms-gear',
    [EntityService.TYPE_OUTBOUND]: 'glyph-outbound',
    [EntityService.TYPE_ZAPIER]: 'glyph-outbound',
    [EntityService.TYPE_KAJABI]: 'glyph-outbound',
    [EntityService.TYPE_STATISTICS]: 'glyph-folder-images',
    [EntityService.TYPE_TAGGINGPIXEL]: 'glyph-world',
    [EntityService.TYPE_FACEBOOK_AUDIENCE]: 'glyph-facebook',
    [EntityService.TYPE_MANUAL_TAG]: 'glyph-tag',
    [EntityService.TYPE_SMARTLINK]: 'glyph-url',
    [EntityService.TYPE_FORM_CUSTOM]: 'glyph-form-custom',
    [EntityService.TYPE_FORM_RAW]: 'glyph-form-raw',
    [EntityService.TYPE_FORM_INLINE]: 'glyph-form-inline',
    [EntityService.TYPE_FORM_LEADPAGES]: 'glyph-leadpages',
    [EntityService.TYPE_FORM_WISTIA]: 'glyph-wistia',
    [EntityService.TYPE_FORM_OPTIMIZEPRESS]: 'glyph-optimizepress',
    [EntityService.TYPE_FORM_THRIVE]: 'glyph-thrivethemes',
    [EntityService.TYPE_FORM_WIDGET]: 'glyph-form-widget',
    [EntityService.TYPE_REQUESTS]: 'glyph-email-gear',
    [EntityService.TYPE_LANDING_PAGE]: 'glyph-form-custom',
    [EntityService.TYPE_PAYPAL]: 'glyph-paypal',
    [EntityService.TYPE_DIGISTORE]: 'glyph-digistore',
    [EntityService.TYPE_AFFILICON]: 'glyph-affilicon',
    [EntityService.TYPE_CLICKBANK]: 'glyph-clickbank',
    [EntityService.TYPE_API]: 'glyph-api-key',
    [EntityService.TYPE_WUFOO]: 'glyph-wufoo',
    [EntityService.TYPE_SMS_LISTBUILDING_GLOBAL]: 'glyph-sms-gear',
    [EntityService.TYPE_SMS_LISTBUILDING_NEXMO]: 'glyph-nexmo',
    [EntityService.TYPE_SMS_LISTBUILDING_TWILIO]: 'glyph-twilio',
    [EntityService.TYPE_BUSINESSCARD]: 'glyph-cardreaderpro',
    [EntityService.TYPE_BCREVENT]: 'glyph-cardreaderpro',
    [EntityService.TYPE_CALENDAR]: 'glyph-calendar',
    [EntityService.TYPE_METALABEL]: 'glyph-tag',
    [EntityService.TYPE_SIGNATURE]: 'glyph-email',
    [EntityService.TYPE_SUBSCRIBER_LISTS]: 'glyph-email',
  };

  static DISPLAY_NO_ENTITIES = {
    [EntityService.TYPE_AUTOMATION]: TranslateService.translatable('entity::create::You have not created any Automations yet.'),
    [EntityService.TYPE_RULE]: TranslateService.translatable('entity::create::You have not created any Rules yet.'),
    [EntityService.TYPE_EMAIL]: TranslateService.translatable('entity::create::You have not created any Automation Emails yet.'),
    [EntityService.TYPE_SMS]: TranslateService.translatable('entity::create::You have not created any Automation SMS yet.'),
    [EntityService.TYPE_NOTIFY_EMAIL]: TranslateService.translatable('entity::create::You have not created any Notification Emails yet.'),
    [EntityService.TYPE_NOTIFY_SMS]: TranslateService.translatable('entity::create::You have not created any Notification SMS yet.'),
    [EntityService.TYPE_EMAIL_NEWSLETTER]: TranslateService.translatable('entity::create::You have not created any Newsletter/Autoresponder Emails yet.'),
    [EntityService.TYPE_SMS_NEWSLETTER]: TranslateService.translatable('entity::create::You have not created any Newsletter/Autoresponder SMS yet.'),
    [EntityService.TYPE_NEWSLETTER_EMAIL]: TranslateService.translatable('entity::create::You have not created any Email Newsletter yet.'),
    [EntityService.TYPE_NEWSLETTER_SMS]: TranslateService.translatable('entity::create::You have not created any SMS Newsletter yet.'),
    [EntityService.TYPE_AUTORESPONDER_EMAIL]: TranslateService.translatable('entity::create::You have not created any Email Autoresponders yet.'),
    [EntityService.TYPE_AUTORESPONDER_EMAIL_DATETIME]: TranslateService.translatable('entity::create::You have not created any Email Autoresponders yet.'),
    [EntityService.TYPE_AUTORESPONDER_EMAIL_BIRTHDAY]: TranslateService.translatable('entity::create::You have not created any Email Autoresponders yet.'),
    [EntityService.TYPE_AUTORESPONDER_SMS]: TranslateService.translatable('entity::create::You have not created any SMS Autoresponders yet.'),
    [EntityService.TYPE_AUTORESPONDER_SMS_DATETIME]: TranslateService.translatable('entity::create::You have not created any SMS Autoresponders yet.'),
    [EntityService.TYPE_AUTORESPONDER_SMS_BIRTHDAY]: TranslateService.translatable('entity::create::You have not created any SMS Autoresponders yet.'),
    [EntityService.TYPE_CUSTOMFIELD_SINGLE]: TranslateService.translatable('entity::create::You have not created any Custom Fields (Single) yet.'),
    [EntityService.TYPE_CUSTOMFIELD_PARAGRAPH]: TranslateService.translatable('entity::create::You have not created any Custom Fields (Paragraph) yet.'),
    [EntityService.TYPE_CUSTOMFIELD_EMAIL]: TranslateService.translatable('entity::create::You have not created any Custom Fields (Email) yet.'),
    [EntityService.TYPE_CUSTOMFIELD_NUMBER]: TranslateService.translatable('entity::create::You have not created any Custom Fields (Number) yet.'),
    [EntityService.TYPE_CUSTOMFIELD_URL]: TranslateService.translatable('entity::create::You have not created any Custom Fields (URL) yet.'),
    [EntityService.TYPE_CUSTOMFIELD_DATE]: TranslateService.translatable('entity::create::You have not created any Custom Fields (Date) yet.'),
    [EntityService.TYPE_CUSTOMFIELD_TIME]: TranslateService.translatable('entity::create::You have not created any Custom Fields (Time) yet.'),
    [EntityService.TYPE_CUSTOMFIELD_DATETIME]: TranslateService.translatable('entity::create::You have not created any Custom Fields (Datetime) yet.'),
    [EntityService.TYPE_CUSTOMFIELD_HTML]: TranslateService.translatable('entity::create::You have not created any Custom Fields (HTML) yet.'),
    [EntityService.TYPE_CUSTOMFIELD_DECIMAL]: TranslateService.translatable('entity::create::You have not created any Custom Fields (Decimal) yet.'),
    [EntityService.TYPE_FORM_CUSTOM]: TranslateService.translatable('entity::create::You have not created any Subscription forms - Custom yet.'),
    [EntityService.TYPE_FORM_RAW]: TranslateService.translatable('entity::create::You have not created any Subscription forms - Raw yet.'),
    [EntityService.TYPE_FORM_INLINE]: TranslateService.translatable('entity::create::You have not created any Subscription forms - Inline yet.'),
    [EntityService.TYPE_FORM_LEADPAGES]: TranslateService.translatable('entity::create::You have not created any Subscription forms - Leadpages yet.'),
    [EntityService.TYPE_FORM_WISTIA]: TranslateService.translatable('entity::create::You have not created any Subscription forms - Wistia Turnstile yet.'),
    [EntityService.TYPE_FORM_OPTIMIZEPRESS]: TranslateService.translatable('entity::create::You have not created any Subscription forms - OptimizePress yet.'),
    [EntityService.TYPE_FORM_THRIVE]: TranslateService.translatable('entity::create::You have not created any Subscription forms - Thrive yet.'),
    [EntityService.TYPE_FORM_WIDGET]: TranslateService.translatable('entity::create::You have not created any Subscription forms - Widget yet.'),
    [EntityService.TYPE_REQUESTS]: TranslateService.translatable('entity::create::You have not created any Requests yet.'),
    [EntityService.TYPE_LANDING_PAGE]: TranslateService.translatable('entity::create::You have not created any Landing pages yet.'),
    [EntityService.TYPE_PAYPAL]: TranslateService.translatable('entity::create::You have not created any PayPal products yet.'),
    [EntityService.TYPE_DIGISTORE]: TranslateService.translatable('entity::create::You have not created any DigiStore24 products yet.'),
    [EntityService.TYPE_AFFILICON]: TranslateService.translatable('entity::create::You have not created any AffiliCon products yet.'),
    [EntityService.TYPE_CLICKBANK]: TranslateService.translatable('entity::create::You have not created any Clickbank products yet.'),
    [EntityService.TYPE_API]: TranslateService.translatable('entity::create::You have not created any API keys yet.'),
    [EntityService.TYPE_WUFOO]: TranslateService.translatable('entity::create::You have not created any Wufoo Subscription forms yet.'),
    [EntityService.TYPE_SMS_LISTBUILDING_GLOBAL]: TranslateService.translatable('entity::create::You have not created any SMS Listbuildings yet.'),
    [EntityService.TYPE_SMS_LISTBUILDING_NEXMO]: TranslateService.translatable('entity::create::You have not created any SMS Listbuildings Nexmo yet.'),
    [EntityService.TYPE_SMS_LISTBUILDING_TWILIO]: TranslateService.translatable('entity::create::You have not created any SMS Listbuildings Twilio yet.'),
    [EntityService.TYPE_BUSINESSCARD]: TranslateService.translatable('entity::create::You have not created any Business cards yet.'),
    [EntityService.TYPE_BCREVENT]: TranslateService.translatable('entity::create::You have not created any Business Card Reader Events yet.'),
    [EntityService.TYPE_SMART_TAG]: TranslateService.translatable('entity::create::You have not created any Smart Tags yet.'),
    [EntityService.TYPE_MANUAL_TAG]: TranslateService.translatable('entity::create::You have not created any Manual Tags yet.'),
    [EntityService.TYPE_SMARTLINK]: TranslateService.translatable('entity::create::You have not created any Smart Links yet.'),
    [EntityService.TYPE_SMARTTAG_SENT]: TranslateService.translatable('entity::create::You have not created any Newsletter/Autoresponsers yet.'),
    [EntityService.TYPE_SMARTTAG_OPENED]: TranslateService.translatable('entity::create::You have not created any Newsletter/Autoresponsers yet.'),
    [EntityService.TYPE_SMARTTAG_CLICKED]: TranslateService.translatable('entity::create::You have not created any Newsletter/Autoresponsers yet.'),
    [EntityService.TYPE_SMARTTAG_VIEWED]: TranslateService.translatable('entity::create::You have not created any Newsletter/Autoresponsers yet.'),
    [EntityService.TYPE_SMARTTAG_CONVERTED]: TranslateService.translatable('entity::create::You have not created any Newsletter/Autoresponsers yet.'),
    [EntityService.TYPE_SMARTTAG_KAJABI_ACTIVATE]: TranslateService.translatable('entity::create::You have not created any Kajabi outbounds yet.'),
    [EntityService.TYPE_SMARTTAG_KAJABI_DEACTIVATE]: TranslateService.translatable('entity::create::You have not created any Kajabi outbounds yet.'),
    [EntityService.TYPE_SMARTTAG_OUTBOUND]: TranslateService.translatable('entity::create::You have not created any Outbounds yet.'),
    [EntityService.TYPE_SMARTTAG_TAGGING_PIXEL]: TranslateService.translatable('entity::create::You have not created any Tagging Pixels yet.'),
    [EntityService.TYPE_SMARTTAG_EMAIL_SENT]: TranslateService.translatable('entity::create::You have not created any Emails yet.'),
    [EntityService.TYPE_SMARTTAG_EMAIL_OPENED]: TranslateService.translatable('entity::create::You have not created any Emails yet.'),
    [EntityService.TYPE_SMARTTAG_EMAIL_CLICKED]: TranslateService.translatable('entity::create::You have not created any Emails yet.'),
    [EntityService.TYPE_SMARTTAG_EMAIL_VIEWED]: TranslateService.translatable('entity::create::You have not created any Emails yet.'),
    [EntityService.TYPE_SMARTTAG_SMS_SENT]: TranslateService.translatable('entity::create::You have not created any SMS yet.'),
    [EntityService.TYPE_SMARTTAG_SMS_CLICKED]: TranslateService.translatable('entity::create::You have not created any SMS yet.'),
    [EntityService.TYPE_SMARTTAG_AUTOMATION_STARTED]: TranslateService.translatable('entity::create::You have not created any Automations yet.'),
    [EntityService.TYPE_SMARTTAG_AUTOMATION_FINISHED]: TranslateService.translatable('entity::create::You have not created any Automations yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_APIKEY]: TranslateService.translatable('entity::create::You have not created any API Keys yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_BUSINESSCARD]: TranslateService.translatable('entity::create::You have not created any Business Card Readers yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_BCREVENT]: TranslateService.translatable('entity::create::You have not created any Business Card Reader Events yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REQUEST]: TranslateService.translatable('entity::create::You have not created any Email Requests yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_SMS]: TranslateService.translatable('entity::create::You have not created any SMS Requests yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_FORMS]: TranslateService.translatable('entity::create::You have not created any Subscription Forms yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_PAYMENT]: TranslateService.translatable('entity::create::You have not created any Products yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REFUNDED]: TranslateService.translatable('entity::create::You have not created any Products yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_CHARGEDBACK]: TranslateService.translatable('entity::create::You have not created any Products yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED]: TranslateService.translatable('entity::create::You have not created any Products yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED_LAST_DAY]: TranslateService.translatable('entity::create::You have not created any Products yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_RESUMED]: TranslateService.translatable('entity::create::You have not created any Products yet.'),
    [EntityService.TYPE_SMARTTAG_PAYMENT_COMPLETED]: TranslateService.translatable('entity::create::You have not created any Products yet.'),
    [EntityService.TYPE_SMARTTAG_PAYMENT_EXPIRED]: TranslateService.translatable('entity::create::You have not created any Products yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_DIGISTORE_AFFILIATION]: TranslateService.translatable('entity::create::You have not created any Products yet.'),
    [EntityService.TYPE_SMARTTAG_LISTBUILDING_LANDING_PAGE_SUBSCRIBED]: TranslateService.translatable('entity::create::You have not created any Landing pages yet.'),
    [EntityService.TYPE_FACEBOOK_AUDIENCE]: TranslateService.translatable('entity::create::You have not created any Facebook Audiences yet.'),
    [EntityService.TYPE_COUNTDOWN]: TranslateService.translatable('entity::create::You have not created any Countdowns yet.'),
    [EntityService.TYPE_KAJABI]: TranslateService.translatable('entity::create::You have not created any Kajabis yet.'),
    [EntityService.TYPE_OUTBOUND]: TranslateService.translatable('entity::create::You have not created any Outbounds yet.'),
    [EntityService.TYPE_TAGGINGPIXEL]: TranslateService.translatable('entity::create::You have not created any Tagging Pixels yet.'),
    [EntityService.TYPE_TEMPLATE]: TranslateService.translatable('entity::create::You have not created any Automation Templates yet.'),
    [EntityService.TYPE_WEBINAR]: TranslateService.translatable('entity::create::You have not created any Webinars yet.'),
    [EntityService.TYPE_ZAPIER]: TranslateService.translatable('entity::create::You have not created any Zapier outbounds yet.'),
    [EntityService.TYPE_STATISTICS]: TranslateService.translatable('entity::create::You have not created any Statistics yet.'),
    [EntityService.TYPE_WEBSITE_EXITLIGHTBOX]: TranslateService.translatable('entity::create::You have not created any Exit Lightboxes yet.'),
    [EntityService.TYPE_WEBSITE_FEEDBACK]: TranslateService.translatable('entity::create::You have not created any Feedback Forms yet.'),
    [EntityService.TYPE_WEBSITE_ONETIMEOFFER]: TranslateService.translatable('entity::create::You have not created any One Time Offers yet.'),
    [EntityService.TYPE_WEBSITE_SOCIALPROOF]: TranslateService.translatable('entity::create::You have not created any Social Proof Counters yet.'),
    [EntityService.TYPE_WEBSITE_SPLITTEST]: TranslateService.translatable('entity::create::You have not created any Website Splittests yet.'),
    [EntityService.TYPE_SPLITTEST]: TranslateService.translatable('entity::create::You have not created any Splittests yet.'),
    [EntityService.TYPE_CALENDAR]: TranslateService.translatable('entity::create::You have not created any Calendars yet.'),
    [EntityService.TYPE_METALABEL]: TranslateService.translatable('entity::create::You have not created any Labels yet.'),
    [EntityService.TYPE_SIGNATURE]: TranslateService.translatable('entity::create::You have not created any Signatures yet.'),
    [EntityService.TYPE_SUBSCRIBER_LISTS]: TranslateService.translatable('entity::create::You have not created any Double OptIn Processes yet.'),
  };

  static DISPLAY_PLEASE_SELECT_ENTITY = {
    [EntityService.TYPE_CALENDAR]: TranslateService.translatable('entity::select::Please select a Calendar from the left or create a new one.'),
    [EntityService.TYPE_METALABEL]: TranslateService.translatable('entity::select::Please select a MetaLabel from the left or create a new one.'),
  };

  //TODO: missing
  static UNFILTER_API_TYPE = {
    'Process Flow': EntityService.TYPE_AUTOMATION,
    'Automation Rule': EntityService.TYPE_RULE,
    splittest: EntityService.TYPE_SPLITTEST,
    'automation email': EntityService.TYPE_EMAIL,
    'automation sms': EntityService.TYPE_SMS,
  };

  //TODO: use EntityService.TYPE_
  static UNFILTER_API_ID_FIELD = {
    'Process Flow': 'campaignid',
    'Automation Rule': 'campaignid',
    'automation email': 'emailid',
    'automation sms': 'emailid',
    splittest: 'testid',
    statistics: 'toolid',
    smarttag: 'tagid',
    tag: 'tagid',
    smartlink: 'tagid',
    'campaign-sent': 'tagid',
    'campaign-opened': 'tagid',
    'campaign-clicked': 'tagid',
    'campaign-viewed': 'tagid',
    'campaign-converted': 'tagid',
    'outbound-activated': 'tagid',
    'kajabi-activated': 'tagid',
    'kajabi-deactivated': 'tagid',
    'taggingpixel-triggered': 'tagid',
    'email-sent': 'tagid',
    'email-opened': 'tagid',
    'email-clicked': 'tagid',
    'email-viewed': 'tagid',
    'sms-sent': 'tagid',
    'sms-clicked': 'tagid',
    'automation-started': 'tagid',
    'automation-finished': 'tagid',
    'apikey-subscribed': 'tagid',
    'businesscard-subscribed': 'tagid',
    'event-subscribed': 'tagid',
    'request-email-subscribed': 'tagid',
    'request-sms-subscribed': 'tagid',
    'form-subscribed': 'tagid',
    'facebook-subscribed': 'tagid',
    'payment-received': 'tagid',
    'payment-refunded': 'tagid',
    'payment-chargeback': 'tagid',
    'payment-subsequent': 'tagid',
    'payment-deferred': 'tagid',
    'rebill-canceled': 'tagid',
    'rebill-canceled-lastday': 'tagid',
    'rebill-resumed': 'tagid',
    'digistore-affiliation': 'tagid',
    'plugin-inbound-ready': 'tagid',
    'plugin-inbound-started': 'tagid',
    'plugin-inbound-inprogress': 'tagid',
    'plugin-inbound-finished': 'tagid',
  };

  private storedIndices: any = {};
  private indexSubscriptions: any = {};
  private indexServices: any = {};

  private storedSearchData: any = {}; //TODO needed?
  private _indexObserver: BehaviorSubject<any> = new BehaviorSubject(null); //TODO needed?
  private searchdata: Observable<any> = this._indexObserver.asObservable(); //TODO needed?

  private storedEntities: any = {};
  private entitySubscriptions: any = {};
  private entityServices: any = {};

  private apiEntityTypesObserver: BehaviorSubject<{ status: string; data: ApiEntityTypes[] }>;

  constructor(
    public _TagService: TagService,
    public _CampaignService: CampaignService,
    public _NewsletterService: NewsletterService,
    public _EmailService: EmailService,
    public _ToolService: ToolService,
    public _FieldService: FieldService,
    public _RulesServcie: RulesService, ////TODO not needed, @see Campaign service?
    public _StatisticsService: StatisticsService, //TODO not needed, @see Tool service?
    public _ListbuildingService: ListbuildingService,
    public _SplittestService: SplittestService,
    public _AccountService: DeprAccountService,
    public _ToolCalendarService: ToolCalendarService,
    public _MetalabelService: MetalabelService,
    public _MetatypesService: MetaTypesService
  ) {}

  static getLabel(type: string): string {
    type = EntityService.unfilterApiType(type);
    let display = get(EntityService.DISPLAY_TYPE, type);
    if (!display) {
      console.log('EntityService.getLabel: entity of type has no label', type);
      return type;
    }
    return display;
  }

  static getID(entity: any): number {
    if (entity.hasOwnProperty('type')) {
      let field = EntityService.unfilterApiIdField(entity.type);
      return entity[field];
    } else {
      console.log('EntityService.getID: entity has no type');
      console.log(entity);
    }

    return 0;
  }

  static getType(entity: any): string {
    if (!entity.hasOwnProperty('type')) {
      console.log('entity has no type');
      console.log(entity);
      return '';
    }

    return EntityService.unfilterApiType(entity.type);
  }

  //TODO: get service by type and use GetEditURL()
  static getEditURL(type: string, id: number) {
    let entityClass = '';

    switch (type) {
      case EntityService.TYPE_MANUAL_TAG:
        entityClass = EntityService.CLASSNAME_MANUALTAG;
        break;
      case EntityService.TYPE_SMARTLINK:
        entityClass = EntityService.CLASSNAME_SMARTLINK;
        break;
      case EntityService.TYPE_AUTOMATION:
        entityClass = EntityService.CLASSNAME_AUTOMATION;
        break;
      case EntityService.TYPE_RULE:
        entityClass = EntityService.CLASSNAME_RULE;
        break;
      case EntityService.TYPE_NEWSLETTER_EMAIL:
      case EntityService.TYPE_NEWSLETTER_SMS:
      case EntityService.TYPE_AUTORESPONDER_EMAIL:
      case EntityService.TYPE_AUTORESPONDER_EMAIL_DATETIME:
      case EntityService.TYPE_AUTORESPONDER_EMAIL_BIRTHDAY:
      case EntityService.TYPE_AUTORESPONDER_SMS:
      case EntityService.TYPE_AUTORESPONDER_SMS_DATETIME:
      case EntityService.TYPE_AUTORESPONDER_SMS_BIRTHDAY:
        entityClass = EntityService.CLASSNAME_CAMPAIGN;
        break;
      case EntityService.TYPE_EMAIL:
        entityClass = EntityService.CLASSNAME_EMAIL;
        break;
      case EntityService.TYPE_SMS:
        entityClass = EntityService.CLASSNAME_SMS;
        break;
      case EntityService.TYPE_NOTIFY_EMAIL:
        entityClass = EntityService.CLASSNAME_NOTIFY_EMAIL;
        break;
      case EntityService.TYPE_NOTIFY_SMS:
        entityClass = EntityService.CLASSNAME_NOTIFY_SMS;
        break;
      case EntityService.TYPE_FORM_CUSTOM:
      case EntityService.TYPE_FORM_INLINE:
      case EntityService.TYPE_FORM_WIDGET:
      case EntityService.TYPE_FORM_RAW:
      case EntityService.TYPE_FORM_LEADPAGES:
      case EntityService.TYPE_FORM_OPTIMIZEPRESS:
      case EntityService.TYPE_FORM_WISTIA:
      case EntityService.TYPE_FORM_THRIVE:
      case EntityService.TYPE_BUSINESSCARD:
      case EntityService.TYPE_BCREVENT:
      case EntityService.TYPE_REQUESTS:
      case EntityService.TYPE_API:
      case EntityService.TYPE_SMS_LISTBUILDING_GLOBAL:
      case EntityService.TYPE_SMS_LISTBUILDING_NEXMO:
      case EntityService.TYPE_SMS_LISTBUILDING_TWILIO:
      case EntityService.TYPE_DIGISTORE:
      case EntityService.TYPE_AFFILICON:
      case EntityService.TYPE_CLICKBANK:
      case EntityService.TYPE_PAYPAL:
      case EntityService.TYPE_WUFOO:
      case EntityService.TYPE_LANDING_PAGE:
        entityClass = EntityService.CLASSNAME_LISTBUILDING;
        break;
      case EntityService.TYPE_CUSTOMFIELD_SINGLE:
      case EntityService.TYPE_CUSTOMFIELD_PARAGRAPH:
      case EntityService.TYPE_CUSTOMFIELD_EMAIL:
      case EntityService.TYPE_CUSTOMFIELD_NUMBER:
      case EntityService.TYPE_CUSTOMFIELD_URL:
      case EntityService.TYPE_CUSTOMFIELD_TIME:
      case EntityService.TYPE_CUSTOMFIELD_DATE:
      case EntityService.TYPE_CUSTOMFIELD_DATETIME:
      case EntityService.TYPE_CUSTOMFIELD_HTML:
      case EntityService.TYPE_CUSTOMFIELD_DECIMAL:
        entityClass = EntityService.CLASSNAME_CUSTOMFIELDS;
        break;
      /*
      case EntityService.TYPE_OUTBOUND:
        entityClass = EntityService.CLASSNAME_OUTBOUND;
        break;
      case EntityService.TYPE_CALENDAR:
        return 'calendar/' + id;
        */
      case EntityService.TYPE_METALABEL:
        return '/build/metalabel/' + id; //@TODO: for all if merged to cockpit, use service.getEditUrl()
      case EntityService.TYPE_SIGNATURE:
        entityClass = EntityService.CLASSNAME_SIGNATURES;
        break;
      case EntityService.TYPE_SUBSCRIBER_LISTS:
        //return '/lists/me/' + id + '/edit'; //@TODO: for all, use service.getEditUrl()
        entityClass = EntityService.CLASSNAME_SUBSCRIBER_LISTS;
        break;
      case EntityService.TYPE_ZAPIER:
      case EntityService.TYPE_COUNTDOWN:
      case EntityService.TYPE_KAJABI:
      case EntityService.TYPE_TAGGINGPIXEL:
      case EntityService.TYPE_TEMPLATE:
      case EntityService.TYPE_WEBINAR:
      case EntityService.TYPE_STATISTICS:
      case EntityService.TYPE_FACEBOOK_AUDIENCE:
      case EntityService.TYPE_CALENDAR:
      case EntityService.TYPE_OUTBOUND:
      case EntityService.TYPE_PLUGIN:
        entityClass = EntityService.CLASSNAME_MARKETING_TOOLS;
        break;
      default:
        entityClass = type; // so we can also pass the class directly
    }

    return '/entity/edit/' + entityClass + '/' + id;
  }

  static unfilterApiType(type: string): string {
    if (type && EntityService.UNFILTER_API_TYPE[type]) {
      return EntityService.UNFILTER_API_TYPE[type];
    }

    return type;
  }

  static unfilterApiIdField(type: string): string {
    if (type && EntityService.UNFILTER_API_ID_FIELD[type]) {
      return EntityService.UNFILTER_API_ID_FIELD[type];
    }

    return 'id';
  }

  static _formatApiErrorMessage(error: string): string {
    return error;
  }

  static _createObjectFromAPI(api: Object): Object {
    let type = EntityService.getType(api);

    switch (type) {
      case EntityService.TYPE_EMAIL:
        return new AutomationEmail(api);
      case EntityService.TYPE_SMS:
        return new AutomationSMS(api);
      case EntityService.TYPE_AUTOMATION:
        return new Automation(api);
    }

    if (!api.hasOwnProperty('id')) {
      api['id'] = EntityService.getID(api);
    }

    return api;
  }

  getService(types: any) {
    if (typeof types === 'string') {
      types = [types];
    }

    let service: any;

    for (let type of types) {
      let check_service: any;
      switch (type) {
        case EntityService.TYPE_SMART_TAG:
        case EntityService.TYPE_MANUAL_TAG:
        case EntityService.TYPE_SMARTLINK:
        case EntityService.TYPE_SMARTTAG_SENT:
        case EntityService.TYPE_SMARTTAG_OPENED:
        case EntityService.TYPE_SMARTTAG_CLICKED:
        case EntityService.TYPE_SMARTTAG_VIEWED:
        case EntityService.TYPE_SMARTTAG_CONVERTED:
        case EntityService.TYPE_SMARTTAG_OUTBOUND:
        case EntityService.TYPE_SMARTTAG_KAJABI_ACTIVATE:
        case EntityService.TYPE_SMARTTAG_KAJABI_DEACTIVATE:
        case EntityService.TYPE_SMARTTAG_TAGGING_PIXEL:
        case EntityService.TYPE_SMARTTAG_EMAIL_SENT:
        case EntityService.TYPE_SMARTTAG_EMAIL_OPENED:
        case EntityService.TYPE_SMARTTAG_EMAIL_CLICKED:
        case EntityService.TYPE_SMARTTAG_EMAIL_VIEWED:
        case EntityService.TYPE_SMARTTAG_SMS_SENT:
        case EntityService.TYPE_SMARTTAG_SMS_CLICKED:
        case EntityService.TYPE_SMARTTAG_AUTOMATION_STARTED:
        case EntityService.TYPE_SMARTTAG_AUTOMATION_FINISHED:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_APIKEY:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_BUSINESSCARD:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_BCREVENT:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_REQUEST:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_SMS:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_FORMS:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_PAYMENT:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_REFUNDED:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_CHARGEDBACK:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_CANCELED_LAST_DAY:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_REBILL_RESUMED:
        case EntityService.TYPE_SMARTTAG_PAYMENT_COMPLETED:
        case EntityService.TYPE_SMARTTAG_PAYMENT_EXPIRED:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_DIGISTORE_AFFILIATION:
        case EntityService.TYPE_SMARTTAG_LISTBUILDING_LANDING_PAGE_SUBSCRIBED:
        case EntityService.TYPE_SMARTTAG_FACEBOOK_AUDIENCE:
          check_service = this._TagService;
          break;
        case EntityService.TYPE_AUTOMATION:
          check_service = this._CampaignService;
          break;
        case EntityService.TYPE_RULE:
          check_service = this._RulesServcie;
          break;
        case EntityService.TYPE_NEWSLETTER_EMAIL:
        case EntityService.TYPE_NEWSLETTER_SMS:
        case EntityService.TYPE_AUTORESPONDER_EMAIL:
        case EntityService.TYPE_AUTORESPONDER_EMAIL_DATETIME:
        case EntityService.TYPE_AUTORESPONDER_EMAIL_BIRTHDAY:
        case EntityService.TYPE_AUTORESPONDER_SMS:
        case EntityService.TYPE_AUTORESPONDER_SMS_DATETIME:
        case EntityService.TYPE_AUTORESPONDER_SMS_BIRTHDAY:
          check_service = this._NewsletterService;
          break;
        case EntityService.TYPE_EMAIL:
        case EntityService.TYPE_SMS:
        case EntityService.TYPE_NOTIFY_EMAIL:
        case EntityService.TYPE_NOTIFY_SMS:
        case EntityService.TYPE_EMAIL_NEWSLETTER:
        case EntityService.TYPE_SMS_NEWSLETTER:
          check_service = this._EmailService;
          break;
        case EntityService.TYPE_FORM_CUSTOM:
        case EntityService.TYPE_FORM_INLINE:
        case EntityService.TYPE_FORM_WIDGET:
        case EntityService.TYPE_FORM_RAW:
        case EntityService.TYPE_REQUESTS:
        case EntityService.TYPE_API:
        case EntityService.TYPE_SMS_LISTBUILDING_GLOBAL:
        case EntityService.TYPE_SMS_LISTBUILDING_NEXMO:
        case EntityService.TYPE_SMS_LISTBUILDING_TWILIO:
        case EntityService.TYPE_DIGISTORE:
        case EntityService.TYPE_AFFILICON:
        case EntityService.TYPE_CLICKBANK:
        case EntityService.TYPE_PAYPAL:
        case EntityService.TYPE_WUFOO:
        case EntityService.TYPE_LANDING_PAGE:
        case EntityService.TYPE_FORM_LEADPAGES:
        case EntityService.TYPE_FORM_OPTIMIZEPRESS:
        case EntityService.TYPE_FORM_WISTIA:
        case EntityService.TYPE_FORM_THRIVE:
        case EntityService.TYPE_BUSINESSCARD:
        case EntityService.TYPE_BCREVENT:
          check_service = this._ListbuildingService;
          break;
        case EntityService.TYPE_CUSTOMFIELD_SINGLE:
        case EntityService.TYPE_CUSTOMFIELD_PARAGRAPH:
        case EntityService.TYPE_CUSTOMFIELD_EMAIL:
        case EntityService.TYPE_CUSTOMFIELD_NUMBER:
        case EntityService.TYPE_CUSTOMFIELD_URL:
        case EntityService.TYPE_CUSTOMFIELD_TIME:
        case EntityService.TYPE_CUSTOMFIELD_DATE:
        case EntityService.TYPE_CUSTOMFIELD_DATETIME:
        case EntityService.TYPE_CUSTOMFIELD_HTML:
        case EntityService.TYPE_CUSTOMFIELD_DECIMAL:
          check_service = this._FieldService;
          break;
        case EntityService.TYPE_ZAPIER:
        case EntityService.TYPE_COUNTDOWN:
        case EntityService.TYPE_KAJABI:
        case EntityService.TYPE_TAGGINGPIXEL:
        case EntityService.TYPE_TEMPLATE:
        case EntityService.TYPE_WEBINAR:
        case EntityService.TYPE_WEBSITE_EXITLIGHTBOX:
        case EntityService.TYPE_WEBSITE_FEEDBACK:
        case EntityService.TYPE_WEBSITE_ONETIMEOFFER:
        case EntityService.TYPE_WEBSITE_SOCIALPROOF:
        case EntityService.TYPE_WEBSITE_SPLITTEST:
        case EntityService.TYPE_OUTBOUND:
        case EntityService.TYPE_PLUGIN:
          check_service = this._ToolService;
          break;
        case EntityService.TYPE_CALENDAR:
          check_service = this._ToolCalendarService;
          break;
        case EntityService.TYPE_METALABEL:
          check_service = this._MetalabelService;
          break;
        case EntityService.TYPE_METATYPE:
          check_service = this._MetatypesService;
          break;
        case EntityService.TYPE_STATISTICS:
          check_service = this._StatisticsService;
          break;
        case EntityService.TYPE_SPLITTEST:
          check_service = this._SplittestService;
          break;
      }

      if (!service) {
        service = check_service;
      } else if (service && service.name != check_service.name) {
        console.log(`Not allowed: types ${types.join()} use multiple databases.`);
        return null;
      }
    }

    return service;
  }

  getEntityService(entity: any): any {
    let type = EntityService.getType(entity);
    let service = this.getService([type]);

    if (!service) {
      console.log('EntityService - getEntityService: Service for entity not found.');
      console.log(entity);
      return null;
    }

    return service;
  }

  // @TODO: store filter in indexSubscriptions for indexRefresh, also refresh if filter changes
  index(types: string[], filter: any = {}): Observable<any[]> {
    let sub_id = types.join(); //TODO POSSIBLE BUG

    if (!this.indexSubscriptions[sub_id]) {
      let observer: BehaviorSubject<any> = new BehaviorSubject({
        status: EntityService.STATUS_RETRIEVING,
        data: [],
      });

      this.indexSubscriptions[sub_id] = {
        status: EntityService.STATUS_RETRIEVING,
        observer: observer,
      };

      this._retrieveIndex(types);
    }

    return this.indexSubscriptions[sub_id].observer.asObservable();
  }

  indexRefresh(type: string) {
    this.storedIndices[type] = null;

    for (let sub_id in this.indexSubscriptions) {
      if (this.indexSubscriptions.hasOwnProperty(sub_id)) {
        let types = sub_id.split(',');

        if (types && types.indexOf(type) != -1) {
          this.indexSubscriptions[sub_id]['status'] = EntityService.STATUS_RETRIEVING;
          this.indexSubscriptions[sub_id].observer.next({
            status: EntityService.STATUS_RETRIEVING,
            data: [],
          });

          this._retrieveIndex(types);
        }
      }
    }
  }

  getEntity(type: string, id: number, reset: boolean = false): Observable<any> {
    type = EntityService.unfilterApiType(type);

    if (reset || !this._entityExists(type, id) || this.entitySubscriptions[type][id].status == EntityService.STATUS_RELEASED) {
      // this is the first time the entity (type + id) is requested or it has been released before

      //create an observer for type+id
      let observer: BehaviorSubject<any> = new BehaviorSubject({
        status: EntityService.STATUS_RETRIEVING,
        entity: null,
      });

      //store the subscription
      this.entitySubscriptions[type][id] = {
        status: EntityService.STATUS_RETRIEVING,
        observer: observer,
      };

      this._retrieveEntity(type, id);
    }

    //return the observable
    return this.entitySubscriptions[type][id].observer.asObservable();
  }

  setEntity(entity: any): boolean {
    let id = EntityService.getID(entity);
    let type = EntityService.getType(entity);

    if (!this._entityExists(type, id)) {
      //the entity has not yet been retrieved via the api, use getEntity(type,id) first
      return false;
    }

    this.entitySubscriptions[type][id].status = EntityService.STATUS_UPDATED;
    this.entitySubscriptions[type][id].observer.next({
      status: EntityService.STATUS_UPDATED,
      entity: entity,
    });

    return true;
  }

  updateEntity(entity: any, retrieveAfter: boolean = true): boolean {
    let id = EntityService.getID(entity);
    let type = EntityService.getType(entity);

    if (!this._entityExists(type, id)) return false;

    let service = this.getService(type);

    if (service) {
      this.entitySubscriptions[type][id].status = EntityService.STATUS_UPDATING;
      this.entitySubscriptions[type][id].observer.next({
        status: EntityService.STATUS_UPDATING,
        entity: entity,
      });

      service.update(entity).subscribe(
        (res) => {
          if (res) {
            this.entitySubscriptions[type][id].status = EntityService.STATUS_UPDATED;
            this.entitySubscriptions[type][id].observer.next({
              status: EntityService.STATUS_UPDATED,
              entity: entity,
            });

            if (retrieveAfter) {
              this._retrieveEntity(type, id);
              this.indexRefresh(type);
            }
          } else {
            this.entitySubscriptions[type][id].status = EntityService.STATUS_ERROR;
            this.entitySubscriptions[type][id].observer.next({
              status: EntityService.STATUS_ERROR,
              entity: null,
              error: TranslateService.translatable('service::error::No response'),
              error_action: EntityService.STATUS_UPDATING,
            });
          }
        },
        (error) => {
          this.entitySubscriptions[type][id].status = EntityService.STATUS_ERROR;
          this.entitySubscriptions[type][id].observer.next({
            status: EntityService.STATUS_ERROR,
            entity: null,
            error: EntityService._formatApiErrorMessage(error),
            error_action: EntityService.STATUS_UPDATING,
          });
        }
      );
    } else {
      this.entitySubscriptions[type][id].status = EntityService.STATUS_ERROR;
      this.entitySubscriptions[type][id].observer.next({
        status: EntityService.STATUS_ERROR,
        entity: null,
        error: TranslateService.translatable('service::error::Service not found'),
        error_action: EntityService.STATUS_UPDATING,
      });
    }

    return true;
  }

  deleteEntity(entity: any): boolean {
    let id = EntityService.getID(entity);
    let type = EntityService.getType(entity);

    if (!this._entityExists(type, id)) {
      //the entity has not yet been retrieved via the api, use getEntity(type,id) first
      return false;
    }

    //TODO status should be deleting
    this.entitySubscriptions[type][id].status = EntityService.STATUS_DELETING;
    this.entitySubscriptions[type][id].observer.next({
      status: EntityService.STATUS_DELETED,
      entity: entity,
    });

    let service = this.getService(type);

    if (service) {
      service
        .delete(id)
        .first()
        .subscribe((item) => {
          this.releaseEntity(entity);
          this.indexRefresh(type);

          this.entitySubscriptions[type][id].status = EntityService.STATUS_DELETED;
          this.entitySubscriptions[type][id].observer.next({
            status: EntityService.STATUS_DELETED,
            entity: null,
          });
        });
    }

    return true;
  }

  releaseEntity(entity: any): boolean {
    let id = EntityService.getID(entity);
    let type = EntityService.getType(entity);

    if (!this._entityExists(type, id)) {
      //the entity has not yet been retrieved via the api, use getEntity(type,id) first
      return false;
    }

    this.entitySubscriptions[type][id].status = EntityService.STATUS_RELEASED;
    this.entitySubscriptions[type][id].observer.next({
      status: EntityService.STATUS_RELEASED,
      entity: null,
    });

    return true;
  }

  checkEntityLimit(type): Observable<any> {
    let limit = this._AccountService.getLimit(type);

    let observer: BehaviorSubject<any> = new BehaviorSubject({
      type: EntityService.LIMIT_CHECK,
      limit: limit,
    });

    if (limit == 0) {
      observer.next({
        type: EntityService.LIMIT_REACHED,
        limit: limit,
      });
    } else if (limit == 'unlimited') {
      observer.next({
        type: EntityService.LIMIT_OK,
        limit: limit,
      });
    } else {
      this.indexRefresh(type);
      this.index([type]).subscribe((response) => {
        if (response && response['status'] == EntityService.STATUS_RETRIEVED) {
          response['data'] = response['data'] || [];

          if (response['data'].length >= limit) {
            observer.next({
              type: EntityService.LIMIT_REACHED,
              limit: limit,
            });
          } else {
            observer.next({
              type: EntityService.LIMIT_OK,
              limit: limit,
            });
          }
        }
      });
    }

    return observer.asObservable();
  }

  private _entityExists(type, id): boolean {
    if (!this.entitySubscriptions) {
      this.entitySubscriptions = {};
    }

    if (!this.entitySubscriptions.hasOwnProperty(type)) {
      this.entitySubscriptions[type] = {};
      return false;
    }

    if (!this.entitySubscriptions[type].hasOwnProperty(id)) {
      return false;
    }

    if (!this.entitySubscriptions[type][id].hasOwnProperty('status')) {
      return false;
    }

    return true;
  }

  private _pushIndex(observer: any, types: string[]) {
    let data = [];

    for (let type of types) {
      // not all types are retrieved, do not push yet
      if (!this.storedIndices[type]) return;

      data = data.concat(this.storedIndices[type]);
    }

    observer.next({
      status: EntityService.STATUS_RETRIEVED,
      data: data,
    });
  }

  private _retrieveEntity(type: string, id: number) {
    let service = this.getService(type);

    if (service) {
      service.get(id, type).subscribe(
        (res) => {
          if (res) {
            let entity: Object = EntityService._createObjectFromAPI(res);

            this.entitySubscriptions[type][id].status = EntityService.STATUS_RETRIEVED;
            this.entitySubscriptions[type][id].observer.next({
              status: EntityService.STATUS_RETRIEVED,
              entity: entity,
            });
          } else {
            this.entitySubscriptions[type][id].status = EntityService.STATUS_ERROR;
            this.entitySubscriptions[type][id].observer.next({
              status: EntityService.STATUS_ERROR,
              entity: null,
              error: TranslateService.translatable('service::error::No response'),
              error_action: EntityService.STATUS_RETRIEVING,
            });
          }
        },
        (error) => {
          this.entitySubscriptions[type][id].status = EntityService.STATUS_ERROR;
          this.entitySubscriptions[type][id].observer.next({
            status: EntityService.STATUS_ERROR,
            entity: null,
            error: EntityService._formatApiErrorMessage(error),
            error_action: EntityService.STATUS_RETRIEVING,
          });
        }
      );
    } else {
      this.entitySubscriptions[type][id].status = EntityService.STATUS_ERROR;
      this.entitySubscriptions[type][id].observer.next({
        status: EntityService.STATUS_ERROR,
        entity: null,
        error: TranslateService.translatable('service::error::Service not found'),
        error_action: EntityService.STATUS_RETRIEVING,
      });
    }
  }

  private _retrieveIndex(types: string[]) {
    let data_ready: boolean = true;
    for (let type in types) {
      if (!this.storedIndices[type]) {
        data_ready = false;
        break;
      }
    }

    if (data_ready) {
      this._pushIndex(this.indexSubscriptions[types.join()].observer, types);
      return;
    }

    let service = this.getService(types);

    if (service) {
      service.getIndexAdvanced(types).subscribe(
        (res) => {
          if (res) {
            let data = {};
            for (let type of types) {
              data[type] = [];
            }

            for (let id in res.data) {
              if (res.data.hasOwnProperty(id)) {
                data[res.data[id].type].push(res.data[id]);
              }
            }

            for (let type in data) {
              if (data.hasOwnProperty(type)) {
                this.storedIndices[type] = data[type];
              }
            }

            for (let sub_id in this.indexSubscriptions) {
              if (!this.indexSubscriptions.hasOwnProperty(sub_id)) {
                continue;
              }

              let sub_types = sub_id.split(',');

              for (let sub_type of sub_types) {
                if (types.indexOf(sub_type) != -1) {
                  this._pushIndex(this.indexSubscriptions[sub_id].observer, sub_types);
                  break;
                }
              }
            }
          }
        },
        (error) => console.log
      );
    }
  }

  public static formatDependencies(items: object): { category: string; entities: string[] }[] {
    let format: { [category: string]: string[] } = {};

    for (let category in items) {
      if (!items.hasOwnProperty(category)) continue;
      format[category] = format[category] || [];
      for (let entityID in items[category]) {
        if (!items[category].hasOwnProperty(entityID)) continue;
        format[category].push(items[category][entityID]);
      }
    }

    let dependencies: { category: string; entities: string[] }[] = [];
    for (let category in format) {
      if (!format.hasOwnProperty(category)) continue;
      dependencies.push({
        category: category,
        entities: format[category],
      });
    }

    return dependencies;
  }

  public static formatDependenciesToHTMLString(items: object): string {
    let dependencies = EntityService.formatDependencies(items);

    let html: string = '';
    for (let item of dependencies) {
      if (item) {
        if (item.entities && !!item.entities.length) {
          for (let entity_link of item.entities) {
            html += '<div class="message medium"><strong>' + item.category + ':</strong> ' + entity_link + '</div>';
          }
        }
      }
    }

    return html;
  }
}

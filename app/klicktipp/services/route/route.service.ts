import { Injectable, Type } from "@angular/core";
import {RouteInfo, Routes, RouteTabs} from './route';
import {EventService} from '../event/event.service';
import {get} from '../../shared/utilities/object.utility';


@Injectable()
export class RouteService {

  private routeInfo: RouteInfo;
  private routes: Routes;
  private group: string;

  constructor(public eventService: EventService) {
    this.initializeEvents();
  }

  private initializeEvents() {
    this.eventService.on('route.set', item => this.setRoute(item['group'], item['id'], item['tab'], item['data']));
  }

  public initializeRoutes(routes: Routes, group: string, id: number = 0) {
    this.routes = routes;
    this.group = group;

    //set the entity id if we have one
    let routeInfo: RouteInfo = this.routes[this.group];
    routeInfo.entityID = Number(id);
    this.setRouteInfo(routeInfo);
    this.triggerResize();

    return routeInfo;
  }

  public setRouteInfo(info: RouteInfo) {
    this.routeInfo = info;
    this.eventService.emit('route.update', this.routeInfo);
  }

  public getRouteInfo() : RouteInfo {
    return this.routeInfo;
  }

  public getActiveTab() : RouteTabs {
    let tab = this.routeInfo.content.tabs.find(item => item.id === this.routeInfo.content.activeTab);
    return (tab) ? tab : this.routeInfo.content.tabs[0];
  }

  public getComponent(location: string) : Type<any> {

    if (location === 'index') {
      return this.routeInfo.index.component;
    }

    let activeTab = this.getActiveTab();
    return (activeTab[location]) ? activeTab[location].component : null;

  }

  public getComponentConfig(location: string) : object {

    if (location === 'index') {
      return get(this, 'routeInfo.index.config') || {};
    }

    let activeTab = this.getActiveTab();
    return get(activeTab, 'content.config') || {};

  }

  private setRoute(group: string, id?: number, tab?: string, data?: object) {

    group = group || this.group;
    if (typeof id === 'undefined') {
      //use currently selected entityID
      id = this.routeInfo.entityID || 0;
    }
    else {
      //allow id = 0 to show overview
      id = id || 0;
    }

    if (id === -1) id = 0; //-1 => show overview
    tab = tab || this.getActiveTab().id;

    let routeInfo: RouteInfo = this.routes[group];

    if (!routeInfo) return;

    this.group = group;
    routeInfo.entityID = id;
    routeInfo.content.activeTab = tab;
    routeInfo.event = data;

    this.setRouteInfo(routeInfo);

    this.triggerResize();

    let url = '/build/' + group;
    if (id) {
      url = url + '/' + id;
    }

    window.history.replaceState('', '', url);

  }

  public triggerResize(): void {
    window.setTimeout(function() {
      let event = new Event('resize');
      window.dispatchEvent(event);
    }, 50);
  }

  // Fullscreen

  public activateFullscreen(): void {

    if (this.routeInfo.isFullscreen) {
      //we are already in fullscreen, do nothing
      return;
    }

    this.routeInfo.isFullscreen = true;

    this.eventService.emit('route.fullscreen', true);

  }

  public deactivateFullscreen(): void {

    if (!this.routeInfo.isFullscreen) {
      //we are not in fullscreen, do nothing
      return;
    }

    this.routeInfo.isFullscreen = false;

    this.eventService.emit('route.fullscreen', false);

  }

}

import {filter} from 'rxjs/operators';
import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {TranslateService} from '../../../services/translate/translate.service';
import {FormController} from '../../form-controller';
import {EntityService} from '../../../services/entities/entity.service';
import {MetaSearchMask} from '../../../entities/metalabel/metalabel';

@Component({
  selector: 'forms-entities-metasearch-search',
  templateUrl: './search.form.html'
})
export class FormsEntitiesMetasearchSearchComponent implements OnInit {
  //contains all inputs in one object
  @Input() config: MetaSearchMask;

  //EventEmitter
  @Output() events: EventEmitter<any> = new EventEmitter();

  cmpFormController: FormController;
  cmpFormData: object = {};
  entities: object[] = [];

  constructor(private entityService: EntityService,) {}

  ngOnInit() {
    this.initialize();
  }

  private initialize() {

    let observable = this.entityService.index([EntityService.TYPE_METATYPE]);
    observable.pipe(filter(item => item["status"] === EntityService.STATUS_RETRIEVED)).subscribe(item => {
      this.entities = item['data'];
    });

    this.config = this.config || {
      label: 0,
      name: '',
      types: []
    };

    this.config.label = this.config.label || 0;
    let noLabels = (this.config.label == -1) ? 1 : 0;
    this.config.name = this.config.name || '';
    this.config.types = this.config.types || [];

    let label = {
      id: (noLabels) ? 0 : this.config.label,
      field: 'id'
    };

    this.cmpFormData = {
      label: label,
      _noLabels: noLabels,
      name: this.config.name,
      types: this.config.types || []
    };

  }

  //TODO typedef config
  public setConfig(config: MetaSearchMask) {
    this.config = config;
    this.initialize();
  }

  // --- Forms ---

  formCreate() {

    let form: Object = {
      name: 'metasearch-search',
      disableQuickhelps: true,
      elements: {
        'label': {
          type: 'entity',
          title: TranslateService.translatable("metasearch::form::element:title::Label"),
          original_value: this.cmpFormData['label'],
          form_value: this.cmpFormData['label'],
          source: EntityService.TYPE_METALABEL,
          createType: EntityService.TYPE_METALABEL,
          creatable: false,
          disabled: this.cmpFormData['_noLabels'],
          minimal: true,
          hideInfo: true,
          placeholder: TranslateService.translatable("general::option::Please select"),
        },
        '_noLabels': {
          type: 'checkbox',
          label: TranslateService.translatable("metasearch::form::element:title::Show objects without any labels"),
          original_value: this.cmpFormData['_noLabels'],
          form_value: this.cmpFormData['_noLabels'],
          checked_value: 1,
          unchecked_value: 0,
        },
        'name': {
          type: 'textfield',
          title: TranslateService.translatable('metasearch::form::element::title::Name contains'),
          original_value: this.cmpFormData['name'],
          form_value: this.cmpFormData['name'],
          placeholder: TranslateService.translatable('general::form::placeholder::Insert a name...'),
          live_update: true,
        },
        'types': {
          type: 'entity-multi-favorites',
          title: TranslateService.translatable("general::label::Klick-Tipp Entities"),
          original_value: this.cmpFormData['types'],
          form_value: this.cmpFormData['types'],
          source: EntityService.TYPE_METATYPE,
          hideInfo: true,
          creatable: false,
          disableEdit: true,
          noSort: true,
          minimal: true,
          placeholder: TranslateService.translatable("general::option::Please select"),
        },
        'btn_row': {
          type: 'buttonrow',
          elements: {
            'btn_save': {
              type: 'button',
              theme: 'save',
              label: TranslateService.translatable("general::button::Search"),
            },
            'btn_cancel': {
              type: 'button',
              label: TranslateService.translatable("general::button::Reset"),
              theme: 'cancel',
            }
          }
        }

      }
    };

    return form;

  }

  formOnChanges() {

    let values = this.cmpFormController.getValues();

    if (this.cmpFormData['_noLabels'] !== values['_noLabels'].form_value) {
      this.cmpFormData['_noLabels'] = values['_noLabels'].form_value;
      this.cmpFormData['label'] = (this.cmpFormData['_noLabels']) ? -1 : 0;
      this.cmpFormController.init(this.formCreate());
    }

  }

  formValidate() {
    return this.cmpFormController.validate();
  }

  formOnEvent(event: any = {}) {

    event['type'] = event.type || FormController.EVENT_CLICKED;

    switch (event.type) {
      case FormController.EVENT_INIT:
        this.cmpFormController = event.value;
        this.cmpFormController.init(this.formCreate());
        break;
      case FormController.EVENT_SUBMIT:
        this.formOnChanges();
        break;
      case FormController.EVENT_CLICKED:
        this.formOnClick(event);
        break;
    }

  }

  formOnClick(event: any) {
    switch (event.id) {
      case 'btn_save':
        this.formOnChanges();
        this.formOnSubmit();
        break;
      case 'btn_cancel':
        this.formOnCancel();
        break;
      default:
        break;
    }
  }

  formOnSubmit() {

    if (!this.formValidate()) {
      return;
    }

    let values = this.cmpFormController.getValues();

    this.config.label = (values['_noLabels'].form_value) ? -1 : values['label'].form_value.id;
    this.config.name = values['name'].form_value;

    this.config.types = [];
    let entities = this.entities.filter(item => values['types'].form_value.includes(item['id']));
    entities.forEach(item => {
      this.config.types.push({
        baseType: item['baseType'],
        entityType: item['entityType']
      });
    });

    this.events.emit({
      type: FormController.EVENT_SUBMIT,
      data: this.config,
    });

  }

  formOnCancel() {
    this.cmpFormData = {
      label: {id: 0},
      _noLabel: 0,
      name: '',
      types: []
    };
    this.cmpFormController.init(this.formCreate());
    this.events.emit({
      type: FormController.EVENT_CANCEL,
      data: {}, //TODO
    });
  }

  //@TODO move to form controller with component extension
  formGetMessage(data: Object = {}): Object {

    return {
      message: data['msg'] || data['message'] || '',
      type: data['type'] || 'success'
    };

  }

}

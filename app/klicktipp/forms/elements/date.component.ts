import {Component,Input,Output, EventEmitter, OnInit, ViewChild} from '@angular/core';
import {FormController} from "../form-controller";
import * as moment_ from 'moment';
import {FormsElementsDropdownComponent} from "./dropdown.component";
import {ModalService} from "../../services/modal/modal.service";
import {TranslateService} from "../../services/translate/translate.service";
import {endEvent} from '../../shared/utilities/object.utility';

const moment: any = (<any>moment_).default || moment_;

@Component({
  selector: 'forms-elements-date',
  templateUrl: './date.component.html',
})
export class FormsElementsDateComponent implements OnInit {

  @Input() config: any;
  @Input() form_value: number; //overwrites config.form_value
  @Input() formatInput: string; //overwrites config.formatInput
  @Input() formatDisplay: string; //overwrites config.formatDisplay
  @Input() id: string; //overwrites config.title
  @Input() css: string; //overwrites config.css
  @Input() disabled: boolean; //overwrites config.disabled
  @Input() fixedDates: {label: string, date: string}[]; //overwrites config.disabled

  @ViewChild('monthDropdown', {read: FormsElementsDropdownComponent}) monthDropdown: FormsElementsDropdownComponent;
  @ViewChild('centuryDropdown', {read: FormsElementsDropdownComponent}) centuryDropdown: FormsElementsDropdownComponent;
  @ViewChild('decadeDropdown', {read: FormsElementsDropdownComponent}) decadeDropdown: FormsElementsDropdownComponent;
  @ViewChild('yearDropdown', {read: FormsElementsDropdownComponent}) yearDropdown: FormsElementsDropdownComponent;

  //EventEmitter that the modal service can subscribe to
  @Output() events: EventEmitter<any> = new EventEmitter();

  cmpSelectedDate:any;
  cmpDisplayDate:string = '';

  cmpDisplayCalendar:boolean = false;
  cmpCalendarDate:any;

  cmpSelectedMonth:number = 1;

  cmpSelectedCentury: number = 20;

  cmpSelectedDecade:number = 1;

  cmpSelectedYear:number = 1;

  cmpCalendar:any[] = [];

  constructor(
    private modalService:ModalService,
    private ts:TranslateService,
  ) {
  }

  availableMonths:any = {
    form_value: 0,
    id: 'month',
    css: {
      container: {
        'style-text': true,
        'no-icon': true,
        'air-right-small': true,
      },
    },
    options: [
      {value: 0, label: TranslateService.translatable("general::option::January")},
      {value: 1, label: TranslateService.translatable("general::option::February")},
      {value: 2, label: TranslateService.translatable("general::option::March")},
      {value: 3, label: TranslateService.translatable("general::option::April")},
      {value: 4, label: TranslateService.translatable("general::option::May")},
      {value: 5, label: TranslateService.translatable("general::option::June")},
      {value: 6, label: TranslateService.translatable("general::option::July")},
      {value: 7, label: TranslateService.translatable("general::option::August")},
      {value: 8, label: TranslateService.translatable("general::option::September")},
      {value: 9, label: TranslateService.translatable("general::option::October")},
      {value: 10, label: TranslateService.translatable("general::option::November")},
      {value: 11, label: TranslateService.translatable("general::option::December")},
    ]
  };

  availableCenturies:any = {
    form_value: 2000,
    id: 'century',
    css: {
      container: {
        'style-text': true,
        'no-icon': true,
      },
      dropdown: {
          'numbers': true,
      },
    },
    options: [
      {value: 1900, label: '19'},
      {value: 2000, label: '20'},
    ]
  };
  availableDecades:any = {
    form_value: 0,
    id: 'decade',
    css: {
      container: {
        'style-text': true,
        'no-icon': true,
      },
      dropdown: {
        'numbers': true,
      },
    },
    options: [
      {value: 0, label: '0'},
      {value: 10, label: '1'},
      {value: 20, label: '2'},
      {value: 30, label: '3'},
      {value: 40, label: '4'},
      {value: 50, label: '5'},
      {value: 60, label: '6'},
      {value: 70, label: '7'},
      {value: 80, label: '8'},
      {value: 90, label: '9'},
    ]
  };

  availableYears:any = {
    form_value: 0,
    id: 'year',
    css: {
      container: {
        'style-text': true,
        'no-icon': true
      },
      dropdown: {
          'numbers': true,
      },
    },
    options: [
      {value: 0, label: '0'},
      {value: 1, label: '1'},
      {value: 2, label: '2'},
      {value: 3, label: '3'},
      {value: 4, label: '4'},
      {value: 5, label: '5'},
      {value: 6, label: '6'},
      {value: 7, label: '7'},
      {value: 8, label: '8'},
      {value: 9, label: '9'},
    ]
  };

  displayWeekdays:string[] = [
    TranslateService.translatable("general::option::weekday::short::Mon"),
    TranslateService.translatable("general::option::weekday::short::Tue"),
    TranslateService.translatable("general::option::weekday::short::Wed"),
    TranslateService.translatable("general::option::weekday::short::Thu"),
    TranslateService.translatable("general::option::weekday::short::Fri"),
    TranslateService.translatable("general::option::weekday::short::Sat"),
    TranslateService.translatable("general::option::weekday::short::Sun"),
  ];

  ngOnInit() {

    //config overwrites
    this.config.form_value = (typeof this.form_value === 'undefined') ? ((typeof this.config.form_value === 'undefined') ? '' : this.config.form_value) : this.form_value;
    this.config.formatInput = this.formatInput || this.config.formatInput || 'YYYY-MM-DD';
    this.config.formatDisplay = this.formatDisplay || this.config.formatDisplay || 'DD.MM.YYYY';
    this.config.id = this.id || this.config.id || '';
    this.config.css = this.css || this.config.css || '';
    this.config.disabled = (typeof this.disabled === 'undefined') ? ((typeof this.config.disabled === 'undefined') ? false : this.config.disabled) : this.disabled;
    this.config.fixedDates = (typeof this.fixedDates === 'undefined') ? ((typeof this.config.fixedDates === 'undefined') ? [] : this.config.fixedDates) : this.fixedDates;

    if (this.config.form_value != '' ) {
      this.cmpSelectedDate = moment(this.config.form_value, this.config.formatInput);
      if ( this.cmpSelectedDate && !this.cmpSelectedDate.isValid() ) {
        this.cmpSelectedDate = null;
      }
    }

    this.displayDate();

  }

  // --- UI events ---

  cmpOnChanges() {

    if ( this.config.disabled ) {
      return;
    }

    let value:string = (this.cmpSelectedDate) ? this.cmpSelectedDate.format(this.config.formatInput) : '';

    this.events.emit({
      type: FormController.EVENT_SUBMIT,
      id: this.config.id,
      value: value,
    });

  }

  cmpOnNgModel() {

    this.cmpDisplayCalendar = false;
    this.modalService.adjustDropdown(false);

    if ( this.cmpDisplayDate == '' ) {
      this.cmpSelectedDate = null;
      this.cmpOnChanges();
      return;
    }

    let date:any = this.validateDateString(this.cmpDisplayDate);
    if ( date ) {
      this.cmpSelectedDate = date;
      this.cmpOnChanges();
    }
    else {
      this.cmpSelectedDate = null;
    }

  }

  validateInput(event:any): void {

    endEvent(event);

    if ( this.cmpDisplayDate != '' && !this.validateDateString(this.cmpDisplayDate)) {
      this.cmpSelectedDate = null;
      this.cmpDisplayDate = '';
      this.cmpOnChanges();
    }

  }

  validateDateString(value:string): any {

    let valid_formats:string[] = [
      this.config.formatInput,
      'D.M.YYYY',
      'D.M.YY',
      'DD.M.YYYY',
      'DD.M.YY',
      'D.MM.YYYY',
      'D.MM.YY',
      'DD.MM.YYYY',
      'DD.MM.YY',
    ];

    let valid:boolean = false;
    for ( let format of valid_formats ) {
      let date = moment(value, format);
      if ( date.format(format) == value ) {
        return date;
      }
    }

    return null;

  }

  displayDate() : void {

    if ( !this.cmpSelectedDate ) {
      this.cmpDisplayDate = '';
    }
    else {
      this.cmpDisplayDate = this.cmpSelectedDate.format(this.config.formatDisplay);
    }

  }

  // --- Popup ---

  showCalendar(event:any) {

    endEvent(event);

    if ( this.cmpDisplayDate != '' && !this.validateDateString(this.cmpDisplayDate)) {
      this.cmpSelectedDate = null;
      this.cmpDisplayDate = '';
    }

    if ( !this.cmpSelectedDate ) {
      this.cmpCalendarDate = moment();
    }
    else {
      this.cmpCalendarDate = moment(this.cmpSelectedDate);
    }

    this.generateCalendar(this.cmpCalendarDate);
    this.modalService.adjustDropdown();
    this.cmpDisplayCalendar = true;

  }

  generateCalendar(date:any) {

    let month = date.month();
    let year = date.year();
    let n: number = 1;
    let firstWeekDay: number = moment(date.format('DD.MM.YYYY'), 'DD.MM.YYYY').date(1).isoWeekday();

    this.cmpSelectedMonth = month;
    this.availableMonths.form_value = month;

    this.cmpSelectedCentury = Math.floor(year / 100) * 100;
    this.availableCenturies.form_value = Math.floor(year / 100) * 100;

    this.cmpSelectedDecade = Math.floor((year - this.cmpSelectedCentury) / 10) * 10;
    this.availableDecades.form_value = Math.floor((year - this.cmpSelectedCentury) / 10) * 10;

    this.cmpSelectedYear = year - this.cmpSelectedCentury - this.cmpSelectedDecade;
    this.availableYears.form_value = year - this.cmpSelectedCentury - this.cmpSelectedDecade;

    //this.displayNavigation();

    if (firstWeekDay !== 1) {
      n -= (firstWeekDay + 6) % 7;
    }

    let week:number = 0;
    this.cmpCalendar = [];
    this.cmpCalendar[week] = [];

    let lastDay = moment(date.format('DD.MM.YYYY'), 'DD.MM.YYYY').endOf('month').date();

    for (let i = n; i <= lastDay; i += 1) {

      if (i > 0) {

        let currentDate = moment(`${i}.${month + 1}.${year}`, 'D.M.YYYY');
        let today = (moment().isSame(currentDate, 'day') && moment().isSame(currentDate, 'month'));
        let selected = (this.cmpSelectedDate && this.cmpSelectedDate.format('DD.MM.YYYY') == currentDate.format('DD.MM.YYYY'));

        this.cmpCalendar[week].push({
          label: i,
          day: i,
          month: month + 1,
          year: year,
          enabled: true,
          today: today,
          selected: selected
        });

        if ( currentDate.isoWeekday() == 7 ) {
          week++;
          this.cmpCalendar[week] = [];
        }

      } else {

        this.cmpCalendar[week].push({
          label: '',
          day: 0,
          month: 0,
          year: 0,
          enabled:false,
          today: false,
          selected: false //before:selected
        });

      }

    }

    this.updateDropdowns();

  }

  onNavigate(event:any) {

    if ( event && event.hasOwnProperty('id') && event.hasOwnProperty('value') ) {

      switch(event.id) {
        case 'month':
          this.cmpCalendarDate.month(+event.value);
          break;
        case 'century':
          this.cmpSelectedCentury = +event.value;
          break;
        case 'decade':
          this.cmpSelectedDecade = +event.value;
          break;
        case 'year':
          this.cmpSelectedYear = +event.value;
          break;
      }

      this.cmpCalendarDate.year(this.cmpSelectedCentury + this.cmpSelectedDecade + this.cmpSelectedYear);

      this.generateCalendar(this.cmpCalendarDate);

    }

  }

  onSelectDate(event:any, date: any) {

    endEvent(event);

    this.cmpDisplayCalendar = false;
    this.modalService.adjustDropdown(false);

    if ( !date.enabled ) {
      return;
    }

    this.cmpCalendarDate = moment(`${date.day}.${date.month}.${date.year}`, 'DD.MM.YYYY');
    this.cmpSelectedDate = moment(this.cmpCalendarDate);
    this.displayDate();
    this.generateCalendar(this.cmpCalendarDate);
    this.cmpOnChanges();

  }

  onCancelSelect(event:any) {
    this.cmpDisplayCalendar = false;
    this.modalService.adjustDropdown(false);
  }

  onPrevMonth(event:any) {
    endEvent(event);
    this.cmpCalendarDate.subtract(1, 'month');
    this.generateCalendar(this.cmpCalendarDate);

    this.updateDropdowns();

  }

  onNextMonth(event:any) {
    endEvent(event);
    this.cmpCalendarDate.add(1, 'month');
    this.generateCalendar(this.cmpCalendarDate);

    this.updateDropdowns();

  }

  onSelectFixedDate(event: MouseEvent, date: any) {

    let fixed_date:object = {
      label: '',
      day: date.date(),
      month: date.month() + 1,
      year: date.year(),
      enabled:true,
      today: false,
      selected: false
    };

    this.onSelectDate(event, fixed_date);
  }

  private updateDropdowns() : void {

    if ( this.monthDropdown ) {
      this.monthDropdown.setValue(this.cmpSelectedMonth);
    }

    if ( this.centuryDropdown ) {
      this.centuryDropdown.setValue(this.cmpSelectedCentury);
    }

    if ( this.decadeDropdown ) {
      this.decadeDropdown.setValue(this.cmpSelectedDecade);
    }

    if ( this.yearDropdown ) {
      this.yearDropdown.setValue(this.cmpSelectedYear);
    }

  }

}

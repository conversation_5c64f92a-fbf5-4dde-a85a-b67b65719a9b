import {Component,Input,Output, EventEmitter, OnInit} from '@angular/core';
import {FormController} from "../form-controller";
import {ModalService} from "../../services/modal/modal.service";
import {TranslateService} from "../../services/translate/translate.service";
import * as _ from 'lodash';

@Component({
  selector: 'forms-elements-weekdays',
  templateUrl: './weekdays.component.html',
})
export class FormsElementsWeekdaysComponent implements OnInit {
  @Input() config: any;
  @Input() form_value: string; //overwrites config.form_value
  @Input() id: string; //overwrites config.title
  @Input() css: string; //overwrites config.css
  @Input() disabled: boolean; //overwrites config.disabled
  @Input() required: boolean; //overwrites config.required
  @Input() exclude: boolean; //overwrites config.exclude

  //EventEmitter that the modal service can subscribe to
  @Output() events: EventEmitter<any> = new EventEmitter();

  cmpWeekdays:any[] = [
    { value: 1,label: TranslateService.translatable("general::option::Monday"), classes: {'selected': false} },
    { value: 2,label: TranslateService.translatable("general::option::Tuesday"), classes: {'selected': false} },
    { value: 3,label: TranslateService.translatable("general::option::Wednesday"), classes: {'selected': false} },
    { value: 4,label: TranslateService.translatable("general::option::Thursday"), classes: {'selected': false} },
    { value: 5,label: TranslateService.translatable("general::option::Friday"), classes: {'selected': false} },
    { value: 6,label: TranslateService.translatable("general::option::Saturday"), classes: {'selected': false} },
    { value: 7,label: TranslateService.translatable("general::option::Sunday"), classes: {'selected': false} },
  ];

  cmpDisplaySelection:any[] = [];
  cmpDisplayCalendar:boolean = false;

  constructor(private modalService:ModalService,
              private ts:TranslateService
  ) {
  }

  ngOnInit() {

    //config overwrites
    this.config.form_value = this.form_value || this.config.form_value || [];
    this.config.id = this.id || this.config.id || '';
    this.config.css = this.css || this.config.css || {};
    this.config.disabled = (typeof this.disabled === 'undefined') ? ((typeof this.config.disabled === 'undefined') ? false : this.config.disabled) : this.disabled;
    this.config.required = (typeof this.required === 'undefined') ? ((typeof this.config.required === 'undefined') ? false : this.config.required) : this.required;
    this.config.exclude = (typeof this.exclude === 'undefined') ? ((typeof this.config.exclude === 'undefined') ? false : this.config.exclude) : this.exclude;

    if (typeof this.config.form_value === 'number') {
      //@TODO temporary fix since [number] is converted to number
      this.config.form_value = [this.config.form_value];
    }
    
    if ( this.config.form_value.length == 0 ) {
      this.cmpSelectAll(null);
    }
    else {
      this.cmpDisplayWeekdays();
    }

  }

  // --- UI events ---

  cmpOnToggleCalendar(event?:any): void {
    if (event) event.stopPropagation();

    if ( this.cmpDisplayCalendar ){
      this.modalService.adjustDropdown(false);
      this.events.emit({
        type: FormController.EVENT_SUBMIT,
        id: this.config.id,
        value: this.config.form_value,
      });
    }
    else {
      this.modalService.adjustDropdown();
    }

    this.cmpDisplayCalendar = !this.cmpDisplayCalendar;
  }

  cmpOnToggleWeekday(event:any, day:number) {

    if ( event ) event.stopPropagation();

    if ( this.config.disabled ) {
      return;
    }

    if (  day < 1 || day > 7 ) {
      return;
    }

    let i = this.config.form_value.indexOf(day);
    if ( i == -1 ) {
      this.config.form_value.push(day);
    }
    else {
      this.config.form_value.splice(i,1);
    }

    this.cmpDisplayWeekdays();

  }

  cmpSelectAll(event:any) {
    if ( event ) event.stopPropagation();
    this.config.form_value = _.range(1, 7+1);
    this.cmpDisplayWeekdays();
  }

  cmpSelectNone(event:any) {
    if ( event ) event.stopPropagation();
    this.config.form_value = [];
    this.cmpDisplayWeekdays();
  }

  cmpDisplayWeekdays() : void {

    let count:number = 0;
    for ( let weekday of this.cmpWeekdays ) {
      if ( weekday) {
        let selected = this.config.form_value.includes(weekday.value);
        if ( selected ) {
          count++;
        }
        weekday['classes'] = {
          'selected': selected,
          'exclude': (this.config.exclude && !selected)
        };

      }
    }

    let days:any[] = [];
    let group:number = 0;
    for ( let day of this.cmpWeekdays ) {

      if ( day ) {
        if ( this.config.form_value.includes(day.value) ) {
          if ( !days[group] ) days[group] = [];
          days[group].push(day.label);
        }
        else {
          group++;
        }
      }

    }

    this.cmpDisplaySelection = [];
    for ( let g of days ) {
      if ( g && g.length > 0 ) {
        this.cmpDisplaySelection.push({
          label: ( g.length > 1 ) ? this.ts.get(g[0]) + ' - ' + this.ts.get(g[g.length-1]) : this.ts.get(g[0]),
          classes: {'include': true},
        });
      }
    }

    if (count == 0 ) {
      this.cmpDisplaySelection = [{
        label: TranslateService.translatable("general::option::Please select at least 1 weekday."),
        classes: {'exclude': true},
      }];
    }

    this.cmpDisplaySelection.push({
      label: TranslateService.translatable("general::button::Edit"),
      classes: {'edit': true},
    });

  }

}

<div class="ui-day-picker">
  <ul class="list-tags">
    <li *ngFor="let selection of cmpDisplaySelection"
        class="ui-tag-pocket"
        [ngClass]="selection.classes"
        (click)="cmpOnTogglePopup($event)"
        [translate]="selection.label"
    ></li>
  </ul>
  <div *ngIf="cmpDisplayCalendar" class="ui-datepicker multiselect dropdown is-active" (clickOutside)="cmpOnTogglePopup()">
    <div class="ui-datepicker-head pointer-up">
      <ul class="menu-horizontal selection">
        <li [translate]="config.header"></li>
      </ul>
      <ul class="menu-horizontal tools">
        <li *ngIf="!config.disabled" (click)="cmpSelectAll($event)" translate="general::option::All"></li>
        <li *ngIf="!config.disabled" (click)="cmpSelectNone($event)" translate="general::option::Clear"></li>
      </ul>
    </div>
    <div class="ui-datepicker-content days no-padding">
      <table>
        <tbody>
        <tr *ngFor="let grid of cmpOptionGrid">
          <td *ngFor="let option of grid;" (click)="cmpOnToggleOption($event, option.value)" [ngClass]="option.classes">
            <span class="label">{{option.label}}</span></td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
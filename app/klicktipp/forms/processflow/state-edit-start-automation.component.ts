import {Component,Input, Output, EventEmitter, OnInit} from '@angular/core';

import {FormController} from "../form-controller";
import {ProcessFlowService} from "../../services/flowchart/processflow";
import {TranslateService} from "../../services/translate/translate.service";
import {ProcessState} from "../../services/entities/campaign/campaign";
import {EntityService} from '../../services/entities/entity.service';

@Component({
  selector: 'forms-processflow-state-edit-start-automation',
  templateUrl: './state-edit-start-automation.component.html',
})
export class FormsProcessflowStateEditStartAutomationComponent implements OnInit {
  //contains all inputs in one object
  @Input() config:any;
  //the state to be edited, overwrites config.state -> do not use directly
  @Input() state: ProcessState;
  //editable: state can be edited, overwrites config.editable -> do not use directly
  @Input() editable: boolean;
  @Input() campaignID: number;
  //true: the form is shown in the create state dialog, overwrites config.fromCreate -> do not use directly
  @Input() fromCreate: boolean;
  @Input() presetColor: string;

  //EventEmitter
  @Output() events: EventEmitter<any> = new EventEmitter();

  cmpFormController:FormController;

  cmpMessage:string = '';
  cmpMessageType:string = 'success';

  cmpEntityCreateType:string = EntityService.TYPE_AUTOMATION;
  cmpEntityCreateForElement:string = '';
  cmpIsCreating:boolean = false;
  cmpEntityCreatePrefill:string = '';

  constructor(private ts: TranslateService) {
  }

  ngOnInit() {

    //config overwrites
    this.config.state = this.state || this.config.state || {};
    this.config.editable = (typeof this.editable === 'undefined') ? ((typeof this.config.editable === 'undefined') ? true : this.config.editable) : this.editable;
    this.config.campaignID = this.campaignID || this.config.campaignID || 0;
    this.config.fromCreate = (typeof this.fromCreate === 'undefined') ? ((typeof this.config.fromCreate === 'undefined') ? false : this.config.fromCreate) : this.fromCreate;
    this.config.presetColor = this.presetColor || this.config.presetColor || '';

  }

  //TODO form message
  cmpSetMessage(message:string, type:string = 'success' ) {
    this.cmpMessage = message;
    this.cmpMessageType = type;
  }

  cmpClearMessage() {
    this.cmpMessage = '';
  }

  // --- Forms ---

  formCreate(data:any = {}) {

    let entity = {
      id: data['campaignID'] ? data['campaignID'] : 0,
      field: 'id',
    };

    const message = {
      message: this.ts.translate(
        'flowchart::action::startAutomation::message::Note: This action will still check if the start condition of the selected automation is true for the contact.'),
      type: 'warning'
    };

    return {
      name: 'flowchart-state-edit-start-automation',
      disableQuickhelps: true,
      disabled: !this.config.editable,
      message: message,
      elements: {
        'name': {
          type: 'textfield',
          title: TranslateService.translatable("general::form::element::title::Name"),
          original_value: (this.config.editable) ? data['name'] : '',
          form_value: (this.config.editable) ? data['name'] : '',
          placeholder: (this.config.editable) ? TranslateService.translatable("general::form::placeholder::Insert a name...") : ProcessFlowService.getStateName(this.config.state),
          live_update: true,
        },
        'color': {
          type: 'color-palette',
          title: TranslateService.translatable("flowchart::form::element:title::Color"),
          original_value: this.config.state.color || '',
          form_value: this.config.state.color || '',
          palette: 'flowchart-cards',
          placeholder: this.config.presetColor,
        },
        'entityID': {
          type: 'entity',
          title: TranslateService.translatable("flowchart::form::element:title::Automation"),
          original_value: entity,
          form_value: entity,
          source: EntityService.TYPE_AUTOMATION,
          excludeIDs: [this.config.campaignID], //exclude the current cockpit campaign
          createType: EntityService.TYPE_AUTOMATION,
        },
        'btn_row': {
          type: 'buttonrow',
          elements: {
            'btn_save': {
              type: 'button',
              theme: 'save',
              hidden: (!this.config.editable),
            },
            'btn_cancel': {
              type: 'button',
              label: (!this.config.editable) ? TranslateService.translatable("general::button::Close") : TranslateService.translatable("general::button::Cancel"),
              theme: 'cancel',
            }
          }
        }

      }

    };

  }

  formOnChanges() {

    if ( this.config.fromCreate ) {
      //this form is shown in the create state dialog, submit immediately
      this.formOnSubmit(true);
      return;
    }

  }

  formValidate() {
    return this.cmpFormController.validate();
  }

  formOnEvent(event:any = {}) {

    event['type'] = event.type || FormController.EVENT_CLICKED;

    switch(event.type) {
      case FormController.EVENT_INIT:
        this.cmpFormController = event.value;
        this.cmpFormController.init(this.formCreate(this.config.state));
        break;
      case FormController.EVENT_SUBMIT:
        this.formOnChanges();
        break;
      case FormController.EVENT_CLICKED:
        this.formOnClick(event);
        break;
      case FormController.EVENT_ENTITY_SET:
        break;
      case FormController.EVENT_ENTITY_NOT_FOUND:
        break;
      case FormController.EVENT_ENTITY_CREATE:
        this.formOnCreateEntity(event);
        break;
    }

  }

  formOnClick(event:any) {
    switch( event.id ) {
      case 'btn_save':
        this.formOnSubmit();
        break;
      case 'btn_cancel':
        this.formOnCancel();
        break;
      default:
        break;
    }
  }

  formOnSubmit(emitChangesOnly:boolean = false) {

    if ( !this.formValidate() ) {
      return;
    }

    let values = this.cmpFormController.getValues();

    this.config.state.name = values['name'].form_value;
    this.config.state.color = values['color'].form_value;
    this.config.state.campaignID = values['entityID'].form_value.id;

    this.events.emit({
      type: (emitChangesOnly) ? FormController.EVENT_CHANGED : FormController.EVENT_SUBMIT,
      data: this.config.state,
    });

  }

  formOnCancel() {

    this.events.emit({
      type: FormController.EVENT_CANCEL,
      data: this.config.state,
    });

  }

  // --- UI events ---

  //called when the user clicked create in the entity select component
  //show the entity create component //TODO more comments
  formOnCreateEntity(event:any) {
    this.cmpEntityCreateForElement = event.id;
    this.cmpEntityCreatePrefill = event.value.prefill;
    this.cmpIsCreating = true;
  }

  //called after an event in the entity create component
  onEntityCreated(event:any) {

    //hide the entity create component
    this.cmpIsCreating = false;

    if ( !event || !event.type || event.type == FormController.EVENT_CANCEL ) {
      return;
    }

    if ( !this.cmpEntityCreateForElement || this.cmpEntityCreateForElement == '' ) {
      return;
    }

    let values = this.cmpFormController.getValues();

    if ( values.hasOwnProperty(this.cmpEntityCreateForElement) ) {

      let entity = values[this.cmpEntityCreateForElement].form_value;
      entity.id = event.data.id;

      this.cmpFormController.setValue(this.cmpEntityCreateForElement, entity);

      this.formOnChanges();

    }

  }

}

import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';

import { clone } from '../../shared/utilities/object.utility';
import { FormController } from '../form-controller';
import { ProcessFlowService } from '../../services/flowchart/processflow';
import { TranslateService } from '../../services/translate/translate.service';
import { ProcessState } from '../../services/entities/campaign/campaign';
import { EntityService } from '../../services/entities/entity.service';

@Component({
  selector: 'forms-processflow-state-edit-facebook-audience-add',
  templateUrl: './state-edit-facebook-audience-add.component.html',
})
export class FormsProcessflowStateEditFacebookAudienceAddComponent implements OnInit {
  //contains all inputs in one object
  @Input() config: any;
  //the state to be edited, overwrites config.state -> do not use directly
  @Input() state: ProcessState;
  //editable: state can be edited, overwrites config.editable -> do not use directly
  @Input() editable: boolean;
  @Input() audienceID: number;
  //true: the form is shown in the create state dialog, overwrites config.fromCreate -> do not use directly
  @Input() fromCreate: boolean;
  @Input() presetColor: string;

  //EventEmitter
  @Output() events: EventEmitter<any> = new EventEmitter();

  cmpFormController: FormController;

  cmpAudiences: any[] = [];

  cmpState: ProcessState;

  cmpIsCreating: boolean = false;

  constructor(private _EntityService: EntityService) {}

  ngOnInit() {
    //config overwrites
    this.config.state = this.state || this.config.state || {};
    this.config.editable = typeof this.editable === 'undefined' ? (typeof this.config.editable === 'undefined' ? true : this.config.editable) : this.editable;
    this.config.audienceID = this.audienceID || this.config.audienceID || 0;
    this.config.fromCreate = typeof this.fromCreate === 'undefined' ? (typeof this.config.fromCreate === 'undefined' ? false : this.config.fromCreate) : this.fromCreate;
    this.config.presetColor = this.presetColor || this.config.presetColor || '';

    //clone the state to remove the reference, important for cancel and modal dismiss
    this.cmpState = clone(this.config.state);

    this.cmpAudiences = [
      {
        value: 0,
        label: TranslateService.translatable('flowchart::message::Loading Facebook Audiences...'),
        disabled: true,
      },
    ];

    this._EntityService.index([EntityService.TYPE_FACEBOOK_AUDIENCE]).subscribe(
      (res) => {
        if (res && res.hasOwnProperty('status') && res['status'] == EntityService.STATUS_RETRIEVED) {
          if (res.hasOwnProperty('data') && res['data'].length > 0) {
            this.cmpAudiences = [
              {
                value: 0,
                label: TranslateService.translatable('general::option::Please select'),
              },
            ];

            for (let audience of res['data']) {
              if (audience) {
                this.cmpAudiences.push({
                  value: audience['id'],
                  label: audience['name'],
                  type: audience['type'],
                });
              }
            }

            if (this.cmpFormController) {
              this.cmpFormController.init(this.formCreate());
            }
          } else {
            this.cmpAudiences = [
              {
                value: 0,
                label: TranslateService.translatable('flowchart::message::You do not have any Facebook Audiences'),
              },
            ];
          }

          if (this.cmpFormController) {
            this.cmpFormController.init(this.formCreate());
          }
        }
      },
      (error) => {
        this.cmpAudiences = [
          {
            value: 0,
            label: TranslateService.translatable('flowchart::message::You do not have any Facebook Audiences'),
          },
        ];

        if (this.cmpFormController) {
          this.cmpFormController.init(this.formCreate());
        }
      }
    );
  }

  // --- Forms ---

  formCreate() {
    this.cmpState['audienceID'] = this.cmpState['audienceID'] || 0;

    return {
      name: 'flowchart-state-edit-facebook-audience-add',
      disableQuickhelps: true,
      disabled: !this.config.editable,
      elements: {
        name: {
          type: 'textfield',
          title: TranslateService.translatable('general::form::element::title::Name'),
          original_value: this.config.editable ? this.cmpState['name'] : '',
          form_value: this.config.editable ? this.cmpState['name'] : '',
          placeholder: this.config.editable ? TranslateService.translatable('general::form::placeholder::Insert a name...') : ProcessFlowService.getStateName(this.config.state),
          live_update: true,
        },
        color: {
          type: 'color-palette',
          title: TranslateService.translatable('flowchart::form::element:title::Color'),
          original_value: this.cmpState.color || '',
          form_value: this.cmpState.color || '',
          palette: 'flowchart-cards',
          placeholder: this.config.presetColor,
        },
        entityID: {
          type: 'dropdown',
          title: EntityService.getLabel(EntityService.TYPE_FACEBOOK_AUDIENCE),
          original_value: this.cmpState['audienceID'],
          form_value: this.cmpState['audienceID'],
          options: this.cmpAudiences,
        },
        ignoreExpiredAccess: {
          type: 'checkbox',
          label: TranslateService.translatable('flowchart::form::element:title::Ignore Facebook Audience expired access'),
          original_value: this.cmpState['ignoreExpiredAccess'],
          form_value: this.cmpState['ignoreExpiredAccess'],
          checked_value: 1,
          unchecked_value: 0,
        },
        btn_row: {
          type: 'buttonrow',
          elements: {
            btn_save: {
              type: 'button',
              theme: 'save',
              hidden: !this.config.editable,
            },
            btn_cancel: {
              type: 'button',
              label: !this.config.editable ? TranslateService.translatable('general::button::Close') : TranslateService.translatable('general::button::Cancel'),
              theme: 'cancel',
            },
          },
        },
      },
    };
  }

  formOnChanges() {}

  formValidate() {
    return this.cmpFormController.validate();
  }

  formOnEvent(event: any = {}) {
    event['type'] = event.type || FormController.EVENT_CLICKED;

    switch (event.type) {
      case FormController.EVENT_INIT:
        this.cmpFormController = event.value;
        this.cmpFormController.init(this.formCreate());
        break;
      case FormController.EVENT_SUBMIT:
        this.formOnChanges();
        break;
      case FormController.EVENT_CLICKED:
        this.formOnClick(event);
        break;
    }
  }

  formOnClick(event: any) {
    switch (event.id) {
      case 'btn_save':
        this.formOnSubmit();
        break;
      case 'btn_cancel':
        this.formOnCancel();
        break;
      default:
        break;
    }
  }

  formOnSubmit(emitChangesOnly: boolean = false) {
    if (!this.formValidate()) {
      return;
    }

    let values = this.cmpFormController.getValues();

    this.config.state.name = values['name'].form_value;
    this.config.state.color = values['color'].form_value;
    this.config.state.audienceID = values['entityID'].form_value;
    this.config.state.ignoreExpiredAccess = values['ignoreExpiredAccess'].form_value;

    this.events.emit({
      type: emitChangesOnly ? FormController.EVENT_CHANGED : FormController.EVENT_SUBMIT,
      data: this.config.state,
    });
  }

  formOnCancel() {
    this.events.emit({
      type: FormController.EVENT_CANCEL,
      data: this.config.state,
    });
  }
}

<ng-container *ngFor="let action of actions">
  <div class="card-entity" *ngIf="!action.isDecision()" [flowchart-action]="action.id"></div>

  <div class="condition-group" *ngIf="action.isDecision() && !flowchartFeatures.LISTVIEW" [ngClass]="classes.condition.group">
    <div class="card-entity" [flowchart-action]="action.id"></div>

    <div class="condition-connection" *ngIf="!flowchartFeatures.COLLAPSE">
      <div class="close-group"
           [attr.ui-tooltip]="ts.translate('flowchart::tooltip::Click to collapse this fork')"
           (click)="toggleGroup($event)"></div>
      <div class="expand-group"
           [attr.ui-tooltip]="ts.translate('flowchart::tooltip::Click to expand this fork')"
           (click)="toggleGroup($event)"><span></span></div>
    </div>

    <div class="group-yes" [ngClass]="classes.condition.yes" [ngStyle]="styles.condition.yes"
         [attr.data-translation]="ts.translate('flowchart::label::Yes')"
         (click)="togglePath($event, 'yes')">

      <div class="group-loop" [flowchart-branch]="getActionBranch(action.decision.yes) | async" [parent]="action" [path]="'yes'"></div>
    </div>

    <div class="group-no" [ngClass]="classes.condition.no" [ngStyle]="styles.condition.no"
         [attr.data-translation]="ts.translate('flowchart::label::No')"
         (click)="togglePath($event, 'no')">

      <div class="group-loop" [flowchart-branch]="getActionBranch(action.decision.no) | async" [parent]="action" [path]="'no'"></div>
    </div>
  </div>
</ng-container>

<div id="branch-{{branch}}" #dropzoneAfter class="dropzone" [ngClass]="classes.dropzone.addBefore">
  <p>{{ ts.translate("flowchart::command::Insert here...") }}</p>
</div>

<!--<div class="ui-card add-card" [ngClass]="classes.dropzone.add" (click)="addAction($event)">-->
  <!--<div class="button-text glyph-plus">{{ ts.translate('flowchart::command::Add action') }}</div>-->
<!--</div>-->

<div [ngClass]="classes.dropzone.add">
  <div class="ui-card add-card" (click)="addAction($event)">
    <div class="button-text glyph-plus">{{ ts.translate('flowchart::command::Add action') }}</div>
  </div>
</div>

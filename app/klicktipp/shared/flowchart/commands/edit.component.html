<forms-processflow-state-edit-decision *ngIf="config.state.type == ACTION.START" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-decision>
<forms-processflow-state-edit-decision *ngIf="config.state.type == ACTION.DECISION || config.state.type == ACTION.GOAL" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-decision>
<forms-processflow-state-edit-detect-gender *ngIf="config.state.type == ACTION.DETECTGENDER" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-detect-gender>
<forms-processflow-state-edit-detect-name *ngIf="config.state.type == ACTION.DETECTNAME" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-detect-name>
<forms-processflow-state-edit-goto *ngIf="config.state.type == ACTION.GOTO" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-goto>
<forms-processflow-state-edit-splittest *ngIf="config.state.type == ACTION.SPLITTEST" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-splittest>
<forms-processflow-state-edit-wait *ngIf="config.state.type == ACTION.WAIT" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-wait>
<forms-processflow-state-edit-email *ngIf="config.state.type == ACTION.SENDEMAIL" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-email>
<forms-processflow-state-edit-sms *ngIf="config.state.type == ACTION.SENDSMS" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-sms>
<forms-processflow-state-edit-notify-email *ngIf="config.state.type == ACTION.NOTIFYEMAIL" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-notify-email>
<forms-processflow-state-edit-notify-sms *ngIf="config.state.type == ACTION.NOTIFYSMS" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-notify-sms>
<forms-processflow-state-edit-tagging *ngIf="config.state.type == ACTION.TAGGING" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-tagging>
<forms-processflow-state-edit-untagging *ngIf="config.state.type == ACTION.UNTAGGING" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-untagging>
<forms-processflow-state-edit-setfield *ngIf="config.state.type == ACTION.SETFIELD" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-setfield>
<forms-processflow-state-edit-unsubscribe *ngIf="config.state.type == ACTION.UNSUBSCRIBE" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-unsubscribe>
<forms-processflow-state-edit-start-automation *ngIf="config.state.type == ACTION.STARTAUTOMATION" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-start-automation>
<forms-processflow-state-edit-stop-automation *ngIf="config.state.type == ACTION.STOPAUTOMATION" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-stop-automation>
<forms-processflow-state-edit-outbound *ngIf="config.state.type == ACTION.OUTBOUND" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-outbound>
<forms-processflow-state-edit-exit *ngIf="config.state.type == ACTION.EXIT" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-exit>
<forms-processflow-state-edit-restart *ngIf="config.state.type == ACTION.RESTART" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-restart>
<forms-processflow-state-edit-facebook-audience-add *ngIf="config.state.type == ACTION.FACEBOOK_AUDIENCE_ADD" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-facebook-audience-add>
<forms-processflow-state-edit-fullcontact *ngIf="config.state.type == ACTION.FULLCONTACT" [config]="config" (events)="execute($event)"></forms-processflow-state-edit-fullcontact>

<main id="main-content" class="ui-cockpit" style="margin-top:52px;height:100vh;padding: 50px;">

  <h3>This module is deprecated.</h3>

  <p>Please send your translations to <PERSON> until Transifex is activated.</p>

  <!-- deprecated

  <div class="ui-form">



    <div class="ui-box app padding air-bottom">
      <h3>Publish</h3>
      <div class="air-top">
        <label>Languages</label>
        <div class="toolbar">
          <ul class="ui-toolbar selectable">
            <li *ngFor="let language of languages" (click)="onSelectLanguage(language.code)" [ngClass]="language.classes">{{ language.name }}</li>
          </ul>
        </div>
      </div>

    </div>

    <div *ngIf="message != ''" class="ui-alert success">
      {{ message }}
    </div>

    <ng-container *ngIf="updated && updated[selectedLanguage] && message == ''" >

      <div *ngFor="let item of updated[selectedLanguage]; let id = index" class="ui-box padding air-top" [ngStyle]="item.styles">
        <div class="type-strong">{{ item.original}}</div>
        <div *ngFor="let version of item.items; let vid = index" class="air-top">
            <fieldset class="ui-checkbox" (click)="onSelectVersion(id, vid)">
              <div class="checkbox" [ngClass]="version.classes.checkbox">
                {{ version.translation}}
                <p class="type-note">{{version.date}} | {{version.owner}} | {{version.location}}</p>
              </div>
            </fieldset>
        </div>
      </div>
    </ng-container>

    <div class="air-top">
      <button class="button-primary bevel glyph-checkmark" (click)="publish()" >Publish</button>
      <button class="button-danger bevel glyph-trash" (click)="clear()" >Clear</button>
    </div>

  </div>
-->
</main>

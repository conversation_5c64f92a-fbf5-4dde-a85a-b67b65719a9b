/** Libraries */
const _ = require('lodash');
const browser = require('browser-sync');
const chalk = require('chalk');
const del = require('del');
const filesize = require('filesize');
const fs = require('fs');
const gulp = require('gulp');
const moment = require('moment');
const path = require('path');
const readFiles = require('read-vinyl-file-stream');
const rename = require('gulp-rename');
const sass = require('gulp-sass');
const sourcemaps = require('gulp-sourcemaps');
const through = require('gulp-if');
const util = require('util');
const webpack = require('webpack');
const yargs = require('yargs');

/** Constants */
const { format, inspect } = util;
const options = require('./gulp.conf');
const signal = new Promise((resolve) => resolve());

/** Initialize */
generateDocumentation();
parseOptions(options);
createBrowser();

/** Gulp Tasks */
gulp.task('clean', clean);
gulp.task('assets', addAssetsTask);
gulp.task('styles', addStylesTask);
gulp.task('scripts', addScriptsTask);

gulp.task('build', gulp.series(['assets', 'styles', 'scripts']));
gulp.task('default', gulp.series(['clean', 'build']));

function clean() {
  const output = getOutputDirectories(options);

  _.each(output, cleanDirectory);

  log('removed', output.map((item) => chalk.green(getRelativePath(item))).join(', '));

  return signal;
}

function cleanDirectory(directory) {
  const opts = {
    force: true,
    ignore: ['**/translation.js', '**/content', '**/webinar.css'],
  };
  return fs.stat(directory, (error) => !error && del(path.join(directory, '*'), opts));
}

function addAssetsTask() {
  _.map(options.assets, (item) => {
    compileAssets(item);
    watch(mapOriginGlob(item), () => compileAssets(item));
  });

  return signal;
}

function compileAssets(config) {
  const output = config.output;
  const pattern = mapOriginGlob(config);
  const start = moment();

  _.each(output, cleanDirectory);

  let stream = gulp.src(pattern);

  _.each(output, (item) => {
    stream.pipe(gulp.dest(item));
  });

  stream.on('end', () => {
    browser.reload();
    log(format('updated %s after %s', chalk.green(output.join(', ')), chalk.magenta(getTimeElapsed(start) + 's')));
  });

  return signal;
}

function addStylesTask() {
  _.map(options.styles, (item) => {
    compileStyles(item);
    watch(mapOriginGlob(item), () => compileStyles(item));
  });

  return signal;
}

function compileStyles(config) {
  const entry = path.join(config.origin, config.entry);
  const start = moment();

  config.sass = _.extend({}, options.defaults.sass, config.sass);

  config.sass.outputStyle = options.production ? 'compressed' : 'expanded';

  let stream = gulp
    .src(entry)
    .pipe(through(!options.production, sourcemaps.init()))
    .pipe(sass(config.sass).on('error', logSassError))
    .pipe(through(!options.production, sourcemaps.write()))
    .pipe(rename(config.file));

  _.each(config.output, (item) => {
    stream.pipe(gulp.dest(item));
  });

  stream.on('end', () => {
    browser.reload();

    // @NOTE: gulp.dest completes before file is written to disk
    setTimeout(() => logStats(config.output, config.file, start), 1000);
  });

  return signal;
}

function addScriptsTask() {
  _.map(options.scripts, (item) => {
    compileScripts(item);
  });

  return signal;
}

function compileScripts(config) {
  config.webpack = _.extend({}, options.defaults.webpack, config.webpack);

  config.webpack.mode = options.production ? 'production' : 'development';
  config.webpack.devtool = options.production ? false : 'cheap-module-eval-source-map';
  config.webpack.watch = options.watch;

  if (config.build) {
    webpack(config.webpack, (error, stats) => {
      const data = stats.toJson();

      if (options.report) {
        fs.writeFileSync('../../build/js/stats.json', JSON.stringify(data));
      }

      if (stats.hasErrors()) {
        log(
          stats.toString({
            all: false,
            colors: true,
            errors: true,
            errorDetails: true,
          })
        );
      }

      _.each(data.assets, (item) => {
        if (item.emitted) logStats(data.outputPath, item.name, data.time);
      });

      browser.reload();
    });
  }

  compileTranslations(config);

  return signal;
}

var translationsMap = new Map();
var translationsTransifexMap = new Map();
function compileTranslations(config) {
  if (!config.translations || !options.translations) return;

  const pattern = mapOriginGlob(config);

  let translations = [];

  let stream = gulp.src(pattern).pipe(
    readFiles((content, file, stream, done) => {
      const { path } = file;
      const result = getTranslations(content, path);
      const { translationsSet, commentsMap, filename } = result;

      translations = translations.concat(Array.from(translationsSet));

      translationsSet.forEach((key) => {
        const split = key.split('::');
        const text = split.pop();
        const identifier = split.join('::');

        const commentsFromMap = split.filter((part) => commentsMap.has(part)).map((part) => commentsMap.get(part));

        // --- new (Transifex)

        if (translationsTransifexMap.has(key)) {
          const entry = translationsTransifexMap.get(key);

          entry.total++;
          entry.filenames.push(filename);

          if (!!commentsFromMap && commentsFromMap.length > 0) {
            entry.comments[filename] = commentsFromMap;
          }
        }
        else {
          const total = 1;
          const comments = {};
          if (!!commentsFromMap && commentsFromMap.length > 0) {
            comments[filename] = commentsFromMap;
          }
          const filenames = [filename];
          const entry = { text, identifier, filenames, comments, total };
          translationsTransifexMap.set(key, entry);
        }

        // --- old
        if (translationsMap.has(text)) {
          const entry = translationsMap.get(text);

          entry.total++;
          entry.filenames.push(filename);

          if (entry.identifiers.indexOf(identifier) === -1) {
            entry.identifiers.push(identifier);
          }

          if (!!commentsFromMap && commentsFromMap.length > 0) {
            entry.comments[filename] = commentsFromMap;
          }
        } else {
          const total = 1;
          const comments = {};
          if (!!commentsFromMap && commentsFromMap.length > 0) {
            comments[filename] = commentsFromMap;
          }
          const filenames = [filename];
          const identifiers = [identifier];
          const entry = { text, identifiers, filenames, comments, total };
          translationsMap.set(text, entry);
        }
      });

      return done();
    })
  );

  stream.on('finish', () => {

    // --- new (Transifex)

    const outputPathTransifexJson = path.join(config.webpack.output.path, 'transifex.json');
    const contentJson = JSON.stringify(Object.fromEntries(translationsTransifexMap), null, 2);
    fs.writeFile(outputPathTransifexJson, contentJson, 'utf8', (error) => {
      if (error) log(error);
      log('saved', chalk.green(translationsTransifexMap.size), 'Transifex translations to ', outputPathTransifexJson, 'for ', pattern);
    });

    // --- old

    let transifex = '';
    let listKeys = '';
    const sortedTranslationsMap = new Map([...translationsMap.entries()].sort((a, b) => {
      if(a[0] < b[0]) { return -1; }
      if(a[0] > b[0]) { return 1; }
      return 0;
    }));
    sortedTranslationsMap.forEach((entry, key) => {
      const { text, identifiers, filenames, comments, total } = entry;

      transifex += '#: Total: ' + total + '\n';

      let filecount = {};
      filenames.forEach((filename) => {
        filecount[filename] = (filecount[filename] || 0) + 1;
      });

      for (let filename in filecount) {
        transifex += '#: ' + filename + ' (' + filecount[filename] + ')\n';
      }

      identifiers.forEach(ident => {
          listKeys += ident + '::' + text + '\n';
      });

      transifex += '# Identifier: ' + identifiers.join('\n# Identifier: ') + '\n';
      transifex += getCommentsString(comments);
      transifex += 'msgid "' + text + '"\n';
      transifex += 'msgstr "' + text + '"\n';
      transifex += '\n';
    });

    const outputPathJson = path.join(config.webpack.output.path, 'angular.po');
    // const contentJson = JSON.stringify(transifex, null, 2);
    fs.writeFile(outputPathJson, transifex, 'utf8', (error) => {
      if (error) log(error);
      log('saved', chalk.green(translationsMap.size), 'translations to ', outputPathJson);
    });

    const outputPathListKeys = path.join(config.webpack.output.path, 'keys.txt');
    fs.writeFile(outputPathListKeys, listKeys, 'utf8', (error) => {
      if (error) log(error);
      log('saved', chalk.green(translationsMap.size), 'translationkeys to ', outputPathListKeys);
    });

  });
}

function getCommentsString(comments) {
  let result = '';
  Object.keys(comments).forEach((filename) => {
    result += '# ' + filename + '\n';
    comments[filename].forEach((comment) => {
      result += '# ' + comment + '\n';
    });
  });
  return result;
}

/** Helpers */
function createBrowser() {
  if (!options.browser) return;
  browser.init(options.sync.server);
  watch(options.sync.glob, browser.reload);
}

function generateDocumentation() {
  const commands = [
    // used for terminal spacing
    { command: '             ', desc: 'clean and build the application' },
    { command: 'assets', desc: 'move assets into the build folder' },
    { command: 'build', desc: 'generate all files for the application' },
    { command: 'clean', desc: 'remove generated files' },
    { command: 'scripts', desc: 'generate scripts for the application' },
    { command: 'styles', desc: 'generate styles for the application' },
  ];

  const flags = [
    {
      option: 'browser',
      alias: 'b',
      type: 'boolean',
      desc: 'enable synchronized browser',
    },
    {
      option: 'production',
      alias: 'p',
      type: 'boolean',
      desc: 'enable build optimizations for production',
    },
    {
      option: 'report',
      alias: 'r',
      type: 'boolean',
      desc: 'generate webpack stats for `npm run report`',
    },
    {
      option: 'silent',
      alias: 'S',
      type: 'boolean',
      desc: 'reduce messages logged to the terminal',
    },
    {
      option: 'translations',
      alias: 't',
      type: 'boolean',
      desc: 'generate translation file',
    },
    {
      option: 'watch',
      alias: 'w',
      type: 'boolean',
      desc: 'monitor file system for changes',
    },
  ];

  _.each(commands, (item) => yargs.command(item));
  _.each(flags, (item) => yargs.option(item.option, item));

  yargs.usage(format('%s gulp %s %s', chalk.green('usage:'), chalk.yellow('<command>'), chalk.cyan('[options]')));

  // used for terminal spacing
  yargs.example('$0 -ptS          ', 'production build with translations included');
  yargs.example('$0 -wbS', 'standard workflow with monitoring and synchronization');
  yargs.example('$0 styles -wbS', 'compile styles with monitoring and synchronization');

  yargs.updateStrings({
    'Commands:': chalk.yellow('commands:'),
    'Options:': chalk.cyan('options:'),
    'Examples:': chalk.green('examples:'),
  });

  yargs.help('help', 'show help dialog for gulp');
  yargs.alias('help', 'h');
  yargs.version(false);
  yargs.wrap(Math.max(100, yargs.terminalWidth() / 2));
}

function getOutputDirectories(item) {
  let dirs = [];

  _.each(item, (value, key) => {
    if (key === 'output') {
      if (_.isString(value)) dirs.push(value);
      if (_.isArray(value)) dirs = dirs.concat(value);
      if (_.isObject(value) && value.path) dirs.push(value.path);
    }

    if (_.isObject(value)) dirs = dirs.concat(getOutputDirectories(value));
  });

  return Array.from(new Set(dirs));
}

function getTimeElapsed(start) {
  let time = start instanceof moment ? moment().diff(start) : start;
  return Math.round((time / 1000) * 100) / 100;
}

function getTranslations(content, path) {
  const regexTranslation = /(?:translate|translatable|isTranslateKey)[=|(]\s*["|']([^"']+)["|']/gi;
  //in template files with translate pipe
  const regexTranslatePipe = /(?:\{\{\s*["|'])(.*?)(?:["|']\s*\|\s*translate.*\}\})/gi;
  const regexTranslatePipeInnerHtmlAttr = /(?:\[(?:innerHTML|innerHtml)\]\s*=\s*"\s*')(.*?)(?:'\s*\|\s*translate.*")/gi;

  //[placeholder]="'Translatable string' | translate"
  const regexTranslatePipeAttr = /(?:\[placeholder\]\s*=\s*"\s*')(.*?)(?:'\s*\|\s*translate.*")/gi;
  const regexTranslatePipeAttrTitle = /(?:\[title\]\s*=\s*"\s*')(.*?)(?:'\s*\|\s*translate.*")/gi;
  const regexMarkupComment = /(?:<!--\s+)<#([a-zA-Z]+)>([a-zA-Z\s]+)(?:<\/#>)(?:\s+-->)/gis;
  const regexScriptComment = /(?:\/\/\s*)<#([a-zA-Z]+)>([a-zA-Z\s]+)(?:<\/#>)/gis;

  const translationsSet = new Set();
  const commentsMap = new Map();

  const filename = path.replace(/^.*[\\\/]/, '');

  function addMatchesFromRegex(regex) {
    let match; // Declare match here
    while ((match = regex.exec(content))) {
      const [, key] = match;
      translationsSet.add(key);
    }
  }

  addMatchesFromRegex(regexTranslation);
  addMatchesFromRegex(regexTranslatePipe);
  addMatchesFromRegex(regexTranslatePipeAttr);
  addMatchesFromRegex(regexTranslatePipeAttrTitle);
  addMatchesFromRegex(regexTranslatePipeInnerHtmlAttr); // Add matches from new regex

  let match; // Declare match here
  while ((match = regexMarkupComment.exec(content))) {
    addCommentMatchToMap(match, commentsMap);
  }

  while ((match = regexScriptComment.exec(content))) {
    addCommentMatchToMap(match, commentsMap);
  }

  return { translationsSet, commentsMap, filename };
}

function addCommentMatchToMap(match, map) {
  const [, key, comment] = match;
  map.set(key, map.has(key) ? [...map.get(key), comment] : [comment]);
}

function log(...rest) {
  let items = rest.map((item) => (_.isObject(item) ? inspect(item, { colors: true }) : item));
  let time = moment().format('HH:mm:ss');

  console.log.call(console, format('[%s] ', chalk.grey(time)) + format(...items));
}

function logSassError(error) {
  log(format('%s: %s', chalk.red('Error'), error.messageOriginal));
  log(format('       on line %s of %s', chalk.cyan(error.line), chalk.green(error.relativePath)));
}

function logStats(output, file, start) {
  let time = getTimeElapsed(start);

  output = _.isArray(output) ? output : [output];
  output = _.map(output, (item) => path.join(item, file));

  _.each(output, (item) => {
    fs.stat(item, (error, stats) => {
      if (error) {
        log('%s: no such file or directory %s', chalk.red('Error'), chalk.green(error.path));
      } else {
        log('updated %s %s after %s', chalk.green(getRelativePath(item)), chalk.cyan(filesize(stats.size)), chalk.magenta(time + 's'));
      }
    });
  });
}

function mapOriginGlob(config) {
  let origin = config.origin;

  return _.map(config.watch, (item) => {
    if (item.charAt(0) === '!') {
      item = item.slice(1);
      origin = '!' + origin;
    }

    return path.join(origin, item);
  });
}

function getRelativePath(output) {
  return path
    .normalize(output)
    .replace(__dirname, '')
    .replace(/^\/|\/$/g, '');
}

function parseOptions(options) {
  let hints = {
    browser: false,
    production: false,
    report: false,
    silent: false,
    translations: false,
    watch: false,
  };
  let flags = Object.keys(hints);

  options = _.extend(options, hints, _.pick(yargs.parse(), flags));

  if (!options.silent) {
    log(
      'options:',
      ..._.map(flags, (item) => {
        let value = options[item] ? chalk.green(true) : chalk.red(false);
        return format('%s[%s]', item, value);
      })
    );
  }
}

function watch(glob, callable) {
  if (!options.watch) return;

  const logPath = (action, path) => {
    if (!options.silent) log(action, chalk.green(path));
    callable.call(callable);
  };

  return gulp
    .watch(glob)
    .on('add', (path) => logPath('added', path))
    .on('change', (path) => logPath('changed', path))
    .on('unlink', (path) => logPath('removed', path));
}

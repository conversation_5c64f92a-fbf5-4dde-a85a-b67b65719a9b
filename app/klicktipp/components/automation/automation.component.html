<main #main id="main-content" class="ui-cockpit" [ngClass]="classes.main">
  <section  class="page-content" *ngIf="automation">
    <section #flowchart class="page-section ui-flowchart" [flowchart-component] style="margin-top: 4rem;"></section>
  </section>
  <section class="page-sidebar" [ngClass]="classes.sidebar">
    <header class="sidebar-header">
      <div class="sidebar-close" (click)="toggleSidebar()"></div>
    </header>
    <section class="sidebar-content" scrollable>
      <automation-todolist [id]="id"></automation-todolist>
    </section>
  </section>
</main>

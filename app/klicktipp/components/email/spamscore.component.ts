import {Component, EventEmitter, Input, OnChanges, OnInit, Output} from '@angular/core';
import {extend} from '../../shared/utilities/object.utility';
import {TranslateService} from '../../services/translate/translate.service';

@Component({
  selector: 'email-spamscore-component',
  templateUrl: './spamscore.component.html'
})
export class EmailSpamscoreComponent implements OnInit, OnChanges {
  //contains all inputs in one object
  @Input() config: Object;

  //EventEmitter
  @Output() events: EventEmitter<any> = new EventEmitter();

  //@TODO output close message
  cmpSpamscore: {
    mode: string,
    open?: boolean,
    scores?: {
      Points: number,
      Code: string,
      Description: string,
      Quickhelp?: string,
    }[],
    displayScore?: string,
    calculate: string,
    message: string,
    hits?: number,
    signature?: {
      id: number,
      name: string,
      href?: string,
    },
    classes: {
      totalScore: {
        [key:string]: boolean,
      }
    }
  };

  cmpIsOpen: boolean = false;
  cmpLoading: boolean = false;

  constructor(
    private ts: TranslateService
  ) {}

  ngOnInit(): void {
    this.displaySpamscore();
  }

  ngOnChanges(): void {
    this.displaySpamscore();
  }

  private displaySpamscore(): void {

    this.cmpSpamscore = extend({
      mode: '', //do not show anything
      open: false,
      scores: [],
      displayScore: '',
      calculate: this.ts.translate("email::spamscore::Calculate SpamScore"),
      message: '',
      hits: -1,
      signature: {
        id: 0,
        name: '',
        href: '',
      },
      classes: {
        totalScore:{},
      }
    }, this.config);

    if (this.cmpSpamscore.mode == 'badge' && this.cmpSpamscore.open) {
      this.cmpIsOpen = true;
    }

    if (this.cmpSpamscore.hits >= 0) {
      this.cmpSpamscore.displayScore = String(Math.floor(this.cmpSpamscore.hits * 10) / 10);
      this.cmpSpamscore.calculate = '';
      if ( this.cmpSpamscore.hits >= 12 ) {
        this.cmpSpamscore.classes.totalScore['red'] = true;
        this.cmpSpamscore.message = TranslateService.translatable("email::spamscore::Your SpamScore is too high!");
      }
      else if ( this.cmpSpamscore.hits >= 6 ) {
        this.cmpSpamscore.classes.totalScore['yellow'] = true;
        this.cmpSpamscore.message = TranslateService.translatable("email::spamscore::Your SpamScore is high.");
      }
      else {
        this.cmpSpamscore.classes.totalScore['green'] = true;
        this.cmpSpamscore.message = TranslateService.translatable("email::spamscore::Your SpamScore is OK.");
      }
    }

    if ( this.cmpSpamscore.signature ) {
      this.cmpSpamscore.signature.href = '/app/signature/settings/me/' + this.cmpSpamscore.signature.id;
    }

    this.cmpLoading = false;

  }

  public onToggleDetails(): void {

    if ( this.cmpSpamscore.mode != 'badge' ) {
      return;
    }

    this.cmpIsOpen = !this.cmpIsOpen;

    if ( this.cmpIsOpen && !this.cmpSpamscore.scores ) {
      this.cmpLoading = true;
      this.events.emit({});
    }

  }

  public onClose(event:any): void {
    this.cmpIsOpen = false;
  }

}

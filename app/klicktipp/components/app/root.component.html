<section class="flex-container air-navigation">
  <section class=" app-panel flex-item" [ngClass]="classes.index">
    <app-window-index-component></app-window-index-component>
  </section>
  <section *ngIf="hasContent" class="app-viewer flex-item flex-body" [ngClass]="classes.content">
    <app-window-controller-component></app-window-controller-component>
  </section>
  <!-- <section class="flex-item flex-sidebar grow-sm"></section> -->
</section>
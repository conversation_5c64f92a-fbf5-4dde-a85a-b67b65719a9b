import { Component } from '@angular/core';

import { EventService } from '../../services/event/event.service';
import { RouteInfo } from '../../services/route/route';
import { RouteService } from '../../services/route/route.service';
import { APPROUTES } from './routes';
import { AppExtensionDefinitions } from './extension/definitions';
import { Extension } from '../../shared/decorators/extension.decorator';
import { AppExtensionEvent } from './extension/event';
import { AppExtensionRouter } from './extension/router';
import { EntityService } from '../../services/entities/entity.service';

@Component({
  selector: 'app-root.component',
  templateUrl: 'root.component.html',
})
@Extension([AppExtensionRouter, AppExtensionEvent])
export class AppRootComponent extends AppExtensionDefinitions {
  public id: number; //from the router
  public group: string; //from the router

  public routeInfo: RouteInfo;

  hasContent: boolean = false;
  private indexCollapsed: boolean = false;

  classes: { [key: string]: string[] } = { index: [], content: [] };

  constructor(public routeService: RouteService, public eventService: EventService, public entityService: EntityService) {
    super();
  }

  initialize(): void {
    this.routeInfo = this.routeService.initializeRoutes(APPROUTES, this.group, this.id);
    this.initializeEvents();
    this.initializeLayout();
  }

  private initializeEvents(): void {
    this.addGlobalEventListener('root.index.toggle', (item) => this.onToggleIndex(item));
  }

  private initializeLayout() {
    /*
    if (this.routeInfo.entityID === 0 && !this.routeInfo.withoutEntity) {
      //there is no entity selected, show only the overview
      this.classes = {
        index: ['grow-1 innercenterbox'],
        content: []
      };
      this.hasContent = false;
    }
    else {
    */
    this.classes = {
      index: this.indexCollapsed ? ['flex-sidebar flex-collapse'] : ['flex-sidebar grow-sm'],
      content: this.routeInfo.entityID || this.routeInfo.withoutEntity ? ['grow-2'] : ['grow-2 bg-app-header'],
    };
    this.hasContent = true;
    //}
  }

  private onToggleIndex(op: string = ''): void {
    if (op === '') {
      this.indexCollapsed = !this.indexCollapsed;
    } else {
      this.indexCollapsed = op === 'collapse';
    }
    this.initializeLayout();
  }

  protected onAfterRouteUpdate(routeInfo: RouteInfo) {
    this.initializeLayout();
  }
}

import { Component, OnInit } from '@angular/core';
import {AccountApiService} from '../../../../services/api/account-api.service';
import {IAccount, ISupport} from '../../../../models/entities/account.entity';
import {TranslateService} from '../../../../services/translate/translate.service';
import {DeprAccountService} from '../../../../services/account/depr-account.service';

@Component({
    selector: 'app-info-bar',
    templateUrl: './info-bar.component.html',
    styleUrls: ['./info-bar.component.scss']
})
export class InfoBarComponent implements OnInit {
    supportData: ISupport;
    account: IAccount;
    userIDLabel: string;
    emailLabel: string;
    productIdLabel: string;
    tierLabel: string;
    termLabel: string;
    groupIdLabel: string;
    amemberIdLabel: string;
    subaccountUsernameLabel: string;
    supportUsernameLabel: string;
    usernameLabel: string;
    spammerLabel: string;
    spammerStatus: string;

    constructor(private api: AccountApiService, private translateService: TranslateService, private accountService: DeprAccountService) {
    }

    ngOnInit() {
        this.accountService.getAccountData().subscribe(account => {
            this.getTranslations();

            if (account && account.data) {
                this.account = account.data;
                this.supportData = account.data.support;
                this.usernameLabel = account.showSupportInfo ? this.supportUsernameLabel : this.subaccountUsernameLabel;
                this.spammerStatus = this.supportData.spamActivity ? 'YES' : 'NO';
            }
        });
    }

    getTranslations() {
        this.userIDLabel = this.translateService.translate('infobar::label::UserId');
        this.emailLabel = this.translateService.translate('infobar::label::E-Mail');
        this.productIdLabel = this.translateService.translate('infobar::label::ProductId');
        this.tierLabel = this.translateService.translate('infobar::label::Tier');
        this.termLabel = this.translateService.translate('infobar::label::Term');
        this.groupIdLabel = this.translateService.translate('infobar::label::GroupId');
        this.amemberIdLabel = this.translateService.translate('infobar::label::AmemberId');
        this.subaccountUsernameLabel = this.translateService.translate('infobar::message::You are working in the account of');
        this.supportUsernameLabel = this.translateService.translate('infobar::label::Username');
        this.spammerLabel = this.translateService.translate('infobar::label::Spammer');
    }
}

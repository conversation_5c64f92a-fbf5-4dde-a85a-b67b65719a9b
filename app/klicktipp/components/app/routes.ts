import {RouteInfo} from '../../services/route/route';
import {EntityService} from '../../services/entities/entity.service';
import {AppPrototypeContentComponent} from './prototype/content.component';
import {AppPrototypeSidebarComponent} from './prototype/sidebar.component';
import {AppPrototypeToolbarComponent} from './prototype/toolbar.component';
import {AppSharedIndexComponent} from './shared/index.component';
import {CalendarViewComponent} from '../calendar/view.component';
import {CalendarSettingsComponent} from '../calendar/settings.component';
import {MetalabelSettingsTabComponent} from '../metalabel/settings.tab';
import {MetasearchResultTabComponent} from '../metasearch/result.tab';
import {MetasearchSearchIndexComponent} from '../metasearch/search.index';
import {TranslateService} from '../../services/translate/translate.service';
import {TranslationLanguagesIndexComponent} from '../translation/languages.index';
import {TranslationTranslateTabComponent} from '../translation/translate.tab';


export const APPROUTES: {[key: string]: RouteInfo} = {
  'prototype': {
    entityID: 0,
    entityType: EntityService.TYPE_EMAIL,
    entityIcon: [''],
    eventID: 'window.prototype',
    index: {
      component: AppSharedIndexComponent,
      config: {}
    },
    content: {
      activeTab: '',
      allowFullscreen: true,
      tabs: [
        {
          id: 'overview',
          label: TranslateService.translatable('prototype::tab::Overview'),
          content: {
            component: AppPrototypeContentComponent
          }
        },
        {
          id: 'settings',
          label: TranslateService.translatable('prototype::tab::Settings'),
          content: {
            component: AppPrototypeContentComponent,
          },
          toolbar: {
            component: AppPrototypeToolbarComponent
          }
        },
        {
          id: 'email',
          label: TranslateService.translatable('prototype::tab::E-Mail'),
          content: {
            component: AppPrototypeContentComponent,
          },
          toolbar: {
            component: AppPrototypeToolbarComponent
          },
          sidebar: {
            component: AppPrototypeSidebarComponent
          }
        }
      ],

    }
  },
  'calendar': {
    entityID: 0,
    entityType: EntityService.TYPE_CALENDAR,
    entityIcon: ['glyph-calendar'],
    eventID: 'window.calendar',
    index: {
      component: AppSharedIndexComponent,
      config: {}
    },
    content: {
      activeTab: 'view',
      tabs: [
        {
          id: 'view',
          label: TranslateService.translatable('calendar::tab::View'),
          icon: 'glyph-view',
          content: {
            component: CalendarViewComponent,
          }
        },
        {
          id: 'settings',
          label: TranslateService.translatable('calendar::tab::Settings'),
          icon: 'glyph-settings',
          content: {
            component: CalendarSettingsComponent,
          }
        }
      ],
    }
  },
  'metalabel': {
    entityID: 0,
    entityType: EntityService.TYPE_METALABEL,
    entityIcon: [EntityService.DISPLAY_ENTITY_ICON[EntityService.TYPE_METALABEL]],
    eventID: 'window.metalabel',
    index: {
      component: AppSharedIndexComponent,
      config: {
        disallowCreate: true
      }
    },
    content: {
      activeTab: 'settings',
      tabs: [
        {
          id: 'settings',
          label: TranslateService.translatable('metalabel::tab::Settings'),
          icon: 'glyph-settings',
          content: {
            component: MetalabelSettingsTabComponent
          }
        }
      ],
    },
  },
  'metasearch': {
    entityID: 0,
    withoutEntity: true,
    title: TranslateService.translatable('metasearch::title::Meta Search'),
    entityType: EntityService.TYPE_METALABEL, //the service has the search action
    entityIcon: ['glyph-search'],
    eventID: 'window.metasearch',
    index: {
      component: MetasearchSearchIndexComponent,
      config: {},
    },
    content: {
      activeTab: 'result',
      tabs: [
        {
          id: 'result',
          label:  TranslateService.translatable('metasearch::tab::Result'),
          icon: 'glyph-menu',
          content: {
            component: MetasearchResultTabComponent
          }
        }
      ],
    },
  },
  'translation': {
    entityID: 0,
    withoutEntity: true,
    title: TranslateService.translatable('translation::title::Translations'),
    entityType: '',
    entityIcon: [],
    eventID: 'window.translation',
    index: {
      component: TranslationLanguagesIndexComponent,
      config: {},
    },
    content: {
      activeTab: 'not-translated',
      tabs: [
        {
          id: 'all',
          label:  TranslateService.translatable('translation::tab::translate::All'),
          icon: 'glyph-menu',
          content: {
            component: TranslationTranslateTabComponent,
            config: {
              showAll: true
            }
          }
        },
        {
          id: 'not-translated',
          label:  TranslateService.translatable('translation::tab::translate::Not translated'),
          icon: 'glyph-menu',
          content: {
            component: TranslationTranslateTabComponent,
            config: {
              showAll: false
            }
          }
        }
      ],
    },
  },
};

<ng-template transclude-to="component">
  <header class="panel-header frame-app-vertical-small frame-app-top-small">
    <h3>
      <span translate="metasearch::title::Search Filter"></span>
      <!-- <span class="button-icon circle glyph-reload" (click)="reloadLabels($event)"></span> -->
    </h3>
    <div class="panel-tools">
    </div>
  </header>
  <section adjust-height style="overflow-y:scroll;" class="panel-body frame-app-vertical-small frame-app-top-small">
    <forms-entities-metasearch-search [config]="searchMask" (events)="onFormEvent($event)"></forms-entities-metasearch-search>
  </section>
</ng-template>
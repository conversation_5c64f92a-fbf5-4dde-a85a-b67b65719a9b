import {Component} from '@angular/core';
import {EventService} from '../../services/event/event.service';
import {EntityService} from '../../services/entities/entity.service';
import {ToolCalendar} from '../../entities/calendar/calendar';
import {FormController} from '../../forms/form-controller';
import {AppExtensionTemplate} from '../app/extension/template.component';
import {RouteService} from '../../services/route/route.service';
import {Extension} from '../../shared/decorators/extension.decorator';
import {AppExtensionEntity} from '../app/extension/entity';
import {AppExtensionEvent} from '../app/extension/event';
import {AppExtensionRouter} from '../app/extension/router';
import {AppSharedModalEntityDeleteConfirmComponent} from '../app/shared/modal/entity-delete-confirm.component';
import {TranslateService} from '../../services/translate/translate.service';
import {AppExtensionModal} from '../app/extension/modal';

@Component({
  selector: 'calendar-settings-component',
  templateUrl: 'settings.component.html'
})

@Extension([AppExtensionModal, AppExtensionRouter, AppExtensionEvent, AppExtensionEntity])
export class CalendarSettingsComponent extends AppExtensionTemplate {

  protected entity: ToolCalendar;

  private editEntity: {entity: ToolCalendar};

  constructor(
    public routeService: RouteService,
    public eventService: EventService,
    public entityService: EntityService,
    public ts: TranslateService
  ) {
    super();
  }

  //called from ngOnInit()
  protected initialize() {}

  //called from ngOnInit() and when an entity has been selected from the index (overview)
  //before the entity is retrieved
  protected onEntitySelect() {}

  protected onRetrieve(first: boolean = true) {
    this.editEntity = {entity: this.entity};
  }

  onFormEvent(event: object) {

    if (!event) return;

    switch(event['type']) {
      case FormController.EVENT_CHANGED:
        //the user changed something in the form but hasn't saved yet
        //mark tab as changed
        this.setHasChanges();
        break;
      case FormController.EVENT_SUBMIT:
        //update entity
        this.entity = event['data'];
        this.updateEntity();

        //unmark tab has changes
        this.unsetHasChanges();
        break;
      case FormController.EVENT_CANCEL:
        //unmark tab has changes
        this.unsetHasChanges();
        break;
      case FormController.EVENT_DELETE:
        this.openModal(AppSharedModalEntityDeleteConfirmComponent, {
          title: this.ts.translate("entity::delete::Delete confirm"),
          component: {}
        });
        break;
    }

  }

}
import { Component, Input, Output, EventEmitter } from '@angular/core';
import {TranslateService} from "../../services/translate/translate.service";

@Component({
  selector: 'system-confirm-delete',
  templateUrl: './delete.component.html'
})
export class SystemConfirmDelete {
  //contains all inputs in one object
  @Input() config:any;
  @Input() headline: string;
  @Input() headline_vars: {[key:string]: string|number};
  @Input() subheadline: string;
  @Input() subheadline_vars: {[key:string]: string|number};
  @Input() message: string;
  @Input() message_vars: {[key:string]: string|number};
  @Input() image: string;

  //EventEmitter that the modal service can subscribe to
  @Output() events: EventEmitter<any> = new EventEmitter();

  images:any = {
    'attention': '/build/images/klicky-sign-warning.svg',
  };

  constructor() {
  }

  ngOnInit() {

    //config overwrites
    this.config.headline = this.headline || this.config.headline || TranslateService.translatable("general::confirm::Attention!");
    this.config.headline_vars = this.headline_vars || this.config.headline_vars || {};
    this.config.subheadline = this.subheadline || this.config.subheadline || TranslateService.translatable("general::confirm::Do you really want to delete?");
    this.config.subheadline_vars = this.subheadline_vars || this.config.subheadline_vars || {};
    this.config.message = this.message || this.config.message || TranslateService.translatable("general::confirm::Note: This action can not be undone!");
    this.config.message_vars = this.message_vars || this.config.message_vars || {};
    this.config.image = this.image || this.config.image || 'attention';

    if ( !this.images.hasOwnProperty(this.config.image) ) {
      this.config.image = 'repair';
    }

  }

  onClickButton(event:any, confirmed:boolean): void {

    if ( event ) event.stopPropagation();

    this.events.emit({
      type: 'confirm',
      value: confirmed,
    });

  }

}

// MAPS: colors
@function color($key) {
  @if map-has-key($colors, $key) {
    @return map-get($colors, $key);
  }

  @warn "Unknown `#{$key}` in $colors.";
  @return null;
}

// MAPS: card-colors
@function card-color($key) {
  @if map-has-key($card-colors, $key) {
    @return map-get($card-colors, $key);
  }

  @warn "Unknown `#{$key}` in $card-colors.";
  @return null;
}

// MAPS: Fonts
@function font($key) {
  @if map-has-key($fonts, $key) {
    @return map-get($fonts, $key);
  }

  @warn "Unknown `#{$key}` in $fonts.";
  @return null;
}

// MAPS: icons
@function glyph($key) {
  @if map-has-key($glyphs, $key) {
    @return map-get($glyphs, $key);
  }

  @warn "Unknown `#{$key}` in $icons.";
  @return null;
}

// MAPS: sizes
@function size($key) {
  @if map-has-key($sizes, $key) {
    @return map-get($sizes, $key);
  }

  @warn "Unknown `#{$key}` in $sizes.";
  @return null;
}

// CONVERSION: em
@function em($px, $base: $font-size-base) {
  @return ($px / $base) * 1em;
}

// CONVERSION: rem
@function rem($px, $base: $font-size-base) {
  @return ($px / $base) * 1rem;
}

// ASPECT RATIO CALCULATOR
@function ratio($w, $h) {
  @return ($w/$h) + 1px;
}

// DECIMALS PRECISION FIX
@function fix-precision ($number, $digits: 6) {
  $n: 1;

  @for $i from 1 through $digits {
    $n: $n * 10;
  }

  @return floor($number * $n) / $n;
}




.ui-alert {
  display: block;
  width: 100%;
  height: auto;
  padding: 1rem 1.25rem;
  border-radius: size(border-radius);
  position: relative;
  font-size: 0.95rem;
  line-height: 1.1em;
  margin: 1rem auto;

  .ui-close {
    &:before {
      @include scale(1);
      @include icon;
      content: glyph(cross-bold);
      width: 1rem;
      height: 1rem;
      font-size: 0.75rem;
      line-height: 1rem;
      text-align: center;
      position: absolute;
      top: 0.35rem;
      right: 0.35rem;
      cursor: pointer;
    }

    &:hover {
      &:before {
        @include scale(1.25);
        transition: transform 0.15s ease-out;
      }
    }
  }

  .title {
    display: block;
    margin-bottom: 1em;
    font-size: 1.125em;
  }

  .message {
    display: inline-block;

    &.block {
      display: block;
    }

    &.medium {
      font-size: 1.125em;
    }
  }

  .button-warning {
    margin-top: 1rem;
  }

  .ui-badge,
  .message {
    vertical-align: middle;
  }

  .ui-badge + .message {
    margin-left: 1.25rem;
  }

  &[class*="icon-"],
  &[class*="glyph-"] {
    padding-left: 4rem;

    &:before {
      @include translateY(-50%);
      display: table-cell;
      width: 2rem;
      height: 2rem;
      line-height: 2rem;
      font-size: 1.25rem;
      position: absolute;
      top: 50%;
      left: 1rem;
    }
  }

  &:not(.style-modal) {
    &.error {
      background: rgba(color(alert-error-light), 0.25);
      border: 1px solid color(alert-error);
      color: color(alert-error);
      border-left: 4px solid color(alert-error);

      .ui-close {
        color: color(alert-error);
      }
    }

    &.success {
      background: rgba(color(alert-success-light), 0.25);
      border: 1px solid color(alert-success);
      color: color(alert-success);
      border-left: 4px solid color(alert-success);

      .ui-close {
        color: color(alert-success);
      }
    }

    &.warning {
      background: rgba(color(alert-warning), 0.25);
      border: 1px solid color(alert-warning);
      color: color(cta-orange-medium);
      border-left: 4px solid color(alert-warning);

      .ui-close {
        color: color(alert-warning);
      }
    }

    &.information {
      background: rgba(color(alert-information-light), 0.25);
      border: 1px solid color(alert-information);
      color: color(primary);
      border-left: 4px solid color(alert-information);

      .ui-close {
        color: color(alert-warning);
      }
    }
  }

  &.style-modal {
    @include ui-box('float');
    @include bg('app-sidebar');
    padding: 1.5rem 2rem;

    &:before {
      display: block;
      font-family: $font-glyphs;
      content: "";
      width: 1.5em;
      height: 1.5em;
      line-height: 1.5em;
      text-align: center;
      font-size: 1em;
      position: absolute;
      top: 1.5rem;
      left: 2rem;
      margin-top: -2px;
      color: color(ui-primary-medium);
    }

    .title {
      margin-left: 1.75rem;
      color: color(body);
    }

    .message {
      color: color(body-dark);
    }

    a {
      &:hover {
        color: color(primary);
        transition: all 0.15s ease-out;
      }
    }

    &.warning {
      &:before {
        content: glyph(warning);
        background: -webkit-linear-gradient(#e2a211, #f1cc46);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  & + & {
    margin-top: 1rem;
  }
}

.ui-notification {
  display: block;
  width: 100%;
  height: auto;
  text-align: center;
  position: relative;

  p {
    display: block;
    width: auto;
    margin: 0 0 1.25rem 0;
    font-size: 1.25rem;
    line-height: 1.125em;
    color: color(body-ui);
  }

  h1,
  h2,
  h3 {
    color: color(accent-one);
    margin-top: 0.5em;
  }

  h1 + p,
  h2 + p,
  h3 + p {
    margin-top: 1.25em;
  }

  &.system-message {
    padding-top: 3rem;

    figure {
      height: auto;
      width: 18.75rem;
      margin: 0 auto;
    }

    .klicky {
      width: 6rem;
      height: auto;
    }

    &.small {
      .klicky {
        width: 4rem;
      }
    }
  }

  /*
  FOR REFERENCE:

  <section class="page-section">
      <div class="ui-notification system-message">
          <h2>Title is optional</h2>
          <p>This is a system message.</p>
      </div>
  </section>

  */

}

.system-page {
  padding: 5rem 0;

  header {
    text-align: center;
    position: relative;

    h1 {
      font-size: 2.625rem;
      line-height: 1.25em;
      color: color(primary);
      font-weight: 500;
    }

    .klicky {
      overflow: visible;

      img {
        width: 100%;
        height: auto;
      }
    }
  }

  .system-message {
    display: block;
    width: 70%;
    margin: 0 auto;

    .message {
      text-align: center;
      font-size: 1.25rem;
      line-height: 1.35em;
    }

    .button-action {
      &.google-logo {
        display: block;
        width: 25rem;
        margin: 0 auto 1.25rem auto;
        padding-left: 5rem;
        text-align: center;
        position: relative;

        .google-icon {
          display: block;
          width: 2.5rem;
          height: 2.5rem;
          background-image: url('/build/images/google_chrome.png');
          background-position: center center;
          background-repeat: no-repeat;
          background-size: contain;
          position: absolute;
          top: 50%;
          left: 2rem;
          margin-top: -1.25rem;
        }
      }
    }
  }

  &.page-404 {
    header {
      h1 {
        font-size: 6rem;

      }

      .klicky {
        width: 16rem;
        margin: 0 auto;
      }
    }
  }

  &.page-load {
    header {
      .klicky {
        width: 8rem;
        position: relative;
        margin: 0 auto;

        img {
          margin-left: -2rem;
        }

        .ui-spinner {
          position: absolute;
          bottom: -9.5%;
          right: -1%;
          margin-right: 1rem;
        }
      }
    }
  }
}


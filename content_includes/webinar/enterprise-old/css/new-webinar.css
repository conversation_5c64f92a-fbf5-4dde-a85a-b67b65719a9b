@font-face {
  font-family: 'glyphs';
  src: url("/build/fonts/glyphs.eot");
  src: url("/build/fonts/glyphs.eot") format("embedded-opentype"), url("/build/fonts/glyphs.ttf") format("truetype"), url("/build/fonts/glyphs.woff") format("woff"), url("/build/fonts/glyphs.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}

/*

GRIDS:

- Grid sizes define width percentages based on predefined values. Example: 5, 12, 24. Generated grid classes (grid-4-12) can be used to define the width of elements regardless of their parent container. An element with a grid class (grid-4-12) will only behave as a grid element if placed inside a parent container with a class of "grid".

- Different widths can be used together to achieve different grid combinations. Example:

| grid-1-5 | grid-3-12 | grid-1-5 |

| 20% | 60% | 20% |

- Grids are only responsive if the class "responsive" is added to the parent container with class "grid".


COLUMNS:

- Columns can only contain "column" elements.
- Columns gutters are defined by adding the classes "gutter-xsmall, gutter-small, gutter-medium, gutter-large, gutter-xlarge" to the parent element. If gutter is not defined, columns will use the default gutter "1rem".
- Columns are inherently responsive.

*/
/* _____

GRID SIZES:
- Defines grid-based width percentages.
- Can be used on any element, indepentedly from the .grid element.

---- */
.grid-1-5 {
  width: 20%;
}

.grid-2-5 {
  width: 40%;
}

.grid-3-5 {
  width: 60%;
}

.grid-4-5 {
  width: 80%;
}

.grid-5-5 {
  width: 100%;
}

.grid-1-12 {
  width: 8.333333%;
}

.grid-2-12 {
  width: 16.666666%;
}

.grid-3-12 {
  width: 25%;
}

.grid-4-12 {
  width: 33.333333%;
}

.grid-5-12 {
  width: 41.666666%;
}

.grid-6-12 {
  width: 50%;
}

.grid-7-12 {
  width: 58.333333%;
}

.grid-8-12 {
  width: 66.666666%;
}

.grid-9-12 {
  width: 75%;
}

.grid-10-12 {
  width: 83.333333%;
}

.grid-11-12 {
  width: 91.666666%;
}

.grid-12-12 {
  width: 100%;
}

.grid-1-24 {
  width: 4.166666%;
}

.grid-2-24 {
  width: 8.333333%;
}

.grid-3-24 {
  width: 12.5%;
}

.grid-4-24 {
  width: 16.666666%;
}

.grid-5-24 {
  width: 20.833333%;
}

.grid-6-24 {
  width: 25%;
}

.grid-7-24 {
  width: 29.166666%;
}

.grid-8-24 {
  width: 33.333333%;
}

.grid-9-24 {
  width: 37.5%;
}

.grid-10-24 {
  width: 41.666666%;
}

.grid-11-24 {
  width: 45.833333%;
}

.grid-12-24 {
  width: 50%;
}

.grid-13-24 {
  width: 54.166666%;
}

.grid-14-24 {
  width: 58.333333%;
}

.grid-15-24 {
  width: 62.5%;
}

.grid-16-24 {
  width: 66.666666%;
}

.grid-17-24 {
  width: 70.833333%;
}

.grid-18-24 {
  width: 75%;
}

.grid-19-24 {
  width: 79.166666%;
}

.grid-20-24 {
  width: 83.333333%;
}

.grid-21-24 {
  width: 87.5%;
}

.grid-22-24 {
  width: 91.666666%;
}

.grid-23-24 {
  width: 95.833333%;
}

.grid-24-24 {
  width: 100%;
}

/* ---- GRID & COLUMNS ---- */
.grid,
*[class*="columns-"] {
  display: block;
  position: relative;
  box-sizing: border-box;
  clear: both;
  margin-left: 0.25rem;
}

.grid:after,
*[class*="columns-"]:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.grid *,
*[class*="columns-"] * {
  box-sizing: inherit;
}

.grid *[class*="grid-"],
.grid .column,
*[class*="columns-"] *[class*="grid-"],
*[class*="columns-"] .column {
  display: inline-block;
  position: relative;
  margin-left: -0.25rem;
  vertical-align: top;
}

/* ----- GRIDS ---- */
.grid *[class*="grid-"].padding-right-gutter-small {
  padding-right: 1rem !important;
}

.grid *[class*="grid-"].padding-left-gutter-small {
  padding-left: 1rem !important;
}

.grid *[class*="grid-"].padding-right-gutter-medium {
  padding-right: 2rem !important;
}

.grid *[class*="grid-"].padding-left-gutter-medium {
  padding-left: 2rem !important;
}

.grid *[class*="grid-"].padding-right-gutter-large {
  padding-right: 3rem !important;
}

.grid *[class*="grid-"].padding-left-gutter-large {
  padding-left: 3rem !important;
}

.grid *[class*="grid-"].padding-right-gutter-xlarge {
  padding-right: 4rem !important;
}

.grid *[class*="grid-"].padding-left-gutter-xlarge {
  padding-left: 4rem !important;
}

.grid *[class*="grid-"].padding-right {
  padding-right: 1.5rem !important;
}

.grid *[class*="grid-"].padding-left {
  padding-left: 1.5rem !important;
}

@media (max-width: 1280px) {
  .grid.responsive *[class*="grid-"] {
    width: 20%;
  }
}

@media (max-width: 1024px) {
  .grid.responsive *[class*="grid-"] {
    width: 25%;
  }
}

@media (max-width: 768px) {
  .grid.responsive *[class*="grid-"] {
    width: 33.333333%;
  }
}

@media (max-width: 560px) {
  .grid.responsive *[class*="grid-"] {
    width: 50%;
  }
}

@media (max-width: 480px) {
  .grid.responsive *[class*="grid-"] {
    width: 100% !important;
    float: none !important;
  }
}

/* ----- COLUMNS ---- */
.columns-2.gutter-small .column {
  width: calc(50% - 0.5rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.columns-2.gutter-small .column:nth-child(2n + 2) {
  margin-right: 0;
}

.columns-2.gutter-medium .column {
  width: calc(50% - 1rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.columns-2.gutter-medium .column:nth-child(2n + 2) {
  margin-right: 0;
}

.columns-2.gutter-large .column {
  width: calc(50% - 1.5rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.columns-2.gutter-large .column:nth-child(2n + 2) {
  margin-right: 0;
}

.columns-2.gutter-xlarge .column {
  width: calc(50% - 2rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.columns-2.gutter-xlarge .column:nth-child(2n + 2) {
  margin-right: 0;
}

.columns-2 .column {
  width: calc(50% - 0.75rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.columns-2 .column:nth-child(2n + 2) {
  margin-right: 0;
}

.columns-3.gutter-small .column {
  width: calc(33.333333% - 0.666666667rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.columns-3.gutter-small .column:nth-child(3n + 3) {
  margin-right: 0;
}

.columns-3.gutter-medium .column {
  width: calc(33.333333% - 1.333333333rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.columns-3.gutter-medium .column:nth-child(3n + 3) {
  margin-right: 0;
}

.columns-3.gutter-large .column {
  width: calc(33.333333% - 2rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.columns-3.gutter-large .column:nth-child(3n + 3) {
  margin-right: 0;
}

.columns-3.gutter-xlarge .column {
  width: calc(33.333333% - 2.666666667rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.columns-3.gutter-xlarge .column:nth-child(3n + 3) {
  margin-right: 0;
}

.columns-3 .column {
  width: calc(33.333333% - 1rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.columns-3 .column:nth-child(3n + 3) {
  margin-right: 0;
}

.columns-4.gutter-small .column {
  width: calc(25% - 0.75rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.columns-4.gutter-small .column:nth-child(4n + 4) {
  margin-right: 0;
}

.columns-4.gutter-medium .column {
  width: calc(25% - 1.5rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.columns-4.gutter-medium .column:nth-child(4n + 4) {
  margin-right: 0;
}

.columns-4.gutter-large .column {
  width: calc(25% - 2.25rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.columns-4.gutter-large .column:nth-child(4n + 4) {
  margin-right: 0;
}

.columns-4.gutter-xlarge .column {
  width: calc(25% - 3rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.columns-4.gutter-xlarge .column:nth-child(4n + 4) {
  margin-right: 0;
}

.columns-4 .column {
  width: calc(25% - 1.125rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.columns-4 .column:nth-child(4n + 4) {
  margin-right: 0;
}

.columns-5.gutter-small .column {
  width: calc(20% - 0.8rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.columns-5.gutter-small .column:nth-child(5n + 5) {
  margin-right: 0;
}

.columns-5.gutter-medium .column {
  width: calc(20% - 1.6rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.columns-5.gutter-medium .column:nth-child(5n + 5) {
  margin-right: 0;
}

.columns-5.gutter-large .column {
  width: calc(20% - 2.4rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.columns-5.gutter-large .column:nth-child(5n + 5) {
  margin-right: 0;
}

.columns-5.gutter-xlarge .column {
  width: calc(20% - 3.2rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.columns-5.gutter-xlarge .column:nth-child(5n + 5) {
  margin-right: 0;
}

.columns-5 .column {
  width: calc(20% - 1.2rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.columns-5 .column:nth-child(5n + 5) {
  margin-right: 0;
}

.columns-6.gutter-small .column {
  width: calc(16.666666% - 0.833333333rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.columns-6.gutter-small .column:nth-child(6n + 6) {
  margin-right: 0;
}

.columns-6.gutter-medium .column {
  width: calc(16.666666% - 1.666666667rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.columns-6.gutter-medium .column:nth-child(6n + 6) {
  margin-right: 0;
}

.columns-6.gutter-large .column {
  width: calc(16.666666% - 2.5rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.columns-6.gutter-large .column:nth-child(6n + 6) {
  margin-right: 0;
}

.columns-6.gutter-xlarge .column {
  width: calc(16.666666% - 3.333333333rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.columns-6.gutter-xlarge .column:nth-child(6n + 6) {
  margin-right: 0;
}

.columns-6 .column {
  width: calc(16.666666% - 1.25rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.columns-6 .column:nth-child(6n + 6) {
  margin-right: 0;
}

.columns-8.gutter-small .column {
  width: calc(12.5% - 0.875rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.columns-8.gutter-small .column:nth-child(8n + 8) {
  margin-right: 0;
}

.columns-8.gutter-medium .column {
  width: calc(12.5% - 1.75rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.columns-8.gutter-medium .column:nth-child(8n + 8) {
  margin-right: 0;
}

.columns-8.gutter-large .column {
  width: calc(12.5% - 2.625rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.columns-8.gutter-large .column:nth-child(8n + 8) {
  margin-right: 0;
}

.columns-8.gutter-xlarge .column {
  width: calc(12.5% - 3.5rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.columns-8.gutter-xlarge .column:nth-child(8n + 8) {
  margin-right: 0;
}

.columns-8 .column {
  width: calc(12.5% - 1.3125rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.columns-8 .column:nth-child(8n + 8) {
  margin-right: 0;
}

.columns-12.gutter-small .column {
  width: calc(8.333333% - 0.916666667rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.columns-12.gutter-small .column:nth-child(12n + 12) {
  margin-right: 0;
}

.columns-12.gutter-medium .column {
  width: calc(8.333333% - 1.833333333rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.columns-12.gutter-medium .column:nth-child(12n + 12) {
  margin-right: 0;
}

.columns-12.gutter-large .column {
  width: calc(8.333333% - 2.75rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.columns-12.gutter-large .column:nth-child(12n + 12) {
  margin-right: 0;
}

.columns-12.gutter-xlarge .column {
  width: calc(8.333333% - 3.666666667rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.columns-12.gutter-xlarge .column:nth-child(12n + 12) {
  margin-right: 0;
}

.columns-12 .column {
  width: calc(8.333333% - 1.375rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.columns-12 .column:nth-child(12n + 12) {
  margin-right: 0;
}

.columns-24.gutter-small .column {
  width: calc(4.166666% - 0.958333333rem);
  margin-right: 1rem;
  margin-bottom: 1rem;
}

.columns-24.gutter-small .column:nth-child(24n + 24) {
  margin-right: 0;
}

.columns-24.gutter-medium .column {
  width: calc(4.166666% - 1.916666667rem);
  margin-right: 2rem;
  margin-bottom: 2rem;
}

.columns-24.gutter-medium .column:nth-child(24n + 24) {
  margin-right: 0;
}

.columns-24.gutter-large .column {
  width: calc(4.166666% - 2.875rem);
  margin-right: 3rem;
  margin-bottom: 3rem;
}

.columns-24.gutter-large .column:nth-child(24n + 24) {
  margin-right: 0;
}

.columns-24.gutter-xlarge .column {
  width: calc(4.166666% - 3.833333333rem);
  margin-right: 4rem;
  margin-bottom: 4rem;
}

.columns-24.gutter-xlarge .column:nth-child(24n + 24) {
  margin-right: 0;
}

.columns-24 .column {
  width: calc(4.166666% - 1.4375rem);
  margin-right: 1.5rem;
  margin-bottom: 1.5rem;
}

.columns-24 .column:nth-child(24n + 24) {
  margin-right: 0;
}

@media (min-width: 1024px) and (max-width: 1280px) {
  .columns-5.gutter-small .column,
  .columns-6.gutter-small .column,
  .columns-8.gutter-small .column,
  .columns-12.gutter-small .column {
    width: calc(25% - 0.75rem);
    margin-right: 1rem !important;
  }
  .columns-5.gutter-small .column:nth-child(4n+4),
  .columns-6.gutter-small .column:nth-child(4n+4),
  .columns-8.gutter-small .column:nth-child(4n+4),
  .columns-12.gutter-small .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
  .columns-5.gutter-medium .column,
  .columns-6.gutter-medium .column,
  .columns-8.gutter-medium .column,
  .columns-12.gutter-medium .column {
    width: calc(25% - 1.5rem);
    margin-right: 2rem !important;
  }
  .columns-5.gutter-medium .column:nth-child(4n+4),
  .columns-6.gutter-medium .column:nth-child(4n+4),
  .columns-8.gutter-medium .column:nth-child(4n+4),
  .columns-12.gutter-medium .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
  .columns-5.gutter-large .column,
  .columns-6.gutter-large .column,
  .columns-8.gutter-large .column,
  .columns-12.gutter-large .column {
    width: calc(25% - 2.25rem);
    margin-right: 3rem !important;
  }
  .columns-5.gutter-large .column:nth-child(4n+4),
  .columns-6.gutter-large .column:nth-child(4n+4),
  .columns-8.gutter-large .column:nth-child(4n+4),
  .columns-12.gutter-large .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
  .columns-5.gutter-xlarge .column,
  .columns-6.gutter-xlarge .column,
  .columns-8.gutter-xlarge .column,
  .columns-12.gutter-xlarge .column {
    width: calc(25% - 3rem);
    margin-right: 4rem !important;
  }
  .columns-5.gutter-xlarge .column:nth-child(4n+4),
  .columns-6.gutter-xlarge .column:nth-child(4n+4),
  .columns-8.gutter-xlarge .column:nth-child(4n+4),
  .columns-12.gutter-xlarge .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
  .columns-5 .column,
  .columns-6 .column,
  .columns-8 .column,
  .columns-12 .column {
    width: calc(25% - 1.125rem);
    margin-right: 1.5rem !important;
  }
  .columns-5 .column:nth-child(4n+4),
  .columns-6 .column:nth-child(4n+4),
  .columns-8 .column:nth-child(4n+4),
  .columns-12 .column:nth-child(4n+4) {
    margin-right: 0 !important;
  }
}

@media (min-width: 850px) and (max-width: 1024px) {
  .columns-4.gutter-small .column,
  .columns-5.gutter-small .column,
  .columns-6.gutter-small .column,
  .columns-8.gutter-small .column,
  .columns-12.gutter-small .column {
    width: calc(33.333333% - 0.666666667rem);
    margin-right: 1rem !important;
  }
  .columns-4.gutter-small .column:nth-child(4n+4),
  .columns-5.gutter-small .column:nth-child(4n+4),
  .columns-6.gutter-small .column:nth-child(4n+4),
  .columns-8.gutter-small .column:nth-child(4n+4),
  .columns-12.gutter-small .column:nth-child(4n+4) {
    margin-right: 1rem !important;
  }
  .columns-4.gutter-small .column:nth-child(3n+3),
  .columns-5.gutter-small .column:nth-child(3n+3),
  .columns-6.gutter-small .column:nth-child(3n+3),
  .columns-8.gutter-small .column:nth-child(3n+3),
  .columns-12.gutter-small .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
  .columns-4.gutter-medium .column,
  .columns-5.gutter-medium .column,
  .columns-6.gutter-medium .column,
  .columns-8.gutter-medium .column,
  .columns-12.gutter-medium .column {
    width: calc(33.333333% - 1.333333333rem);
    margin-right: 2rem !important;
  }
  .columns-4.gutter-medium .column:nth-child(4n+4),
  .columns-5.gutter-medium .column:nth-child(4n+4),
  .columns-6.gutter-medium .column:nth-child(4n+4),
  .columns-8.gutter-medium .column:nth-child(4n+4),
  .columns-12.gutter-medium .column:nth-child(4n+4) {
    margin-right: 2rem !important;
  }
  .columns-4.gutter-medium .column:nth-child(3n+3),
  .columns-5.gutter-medium .column:nth-child(3n+3),
  .columns-6.gutter-medium .column:nth-child(3n+3),
  .columns-8.gutter-medium .column:nth-child(3n+3),
  .columns-12.gutter-medium .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
  .columns-4.gutter-large .column,
  .columns-5.gutter-large .column,
  .columns-6.gutter-large .column,
  .columns-8.gutter-large .column,
  .columns-12.gutter-large .column {
    width: calc(33.333333% - 2rem);
    margin-right: 3rem !important;
  }
  .columns-4.gutter-large .column:nth-child(4n+4),
  .columns-5.gutter-large .column:nth-child(4n+4),
  .columns-6.gutter-large .column:nth-child(4n+4),
  .columns-8.gutter-large .column:nth-child(4n+4),
  .columns-12.gutter-large .column:nth-child(4n+4) {
    margin-right: 3rem !important;
  }
  .columns-4.gutter-large .column:nth-child(3n+3),
  .columns-5.gutter-large .column:nth-child(3n+3),
  .columns-6.gutter-large .column:nth-child(3n+3),
  .columns-8.gutter-large .column:nth-child(3n+3),
  .columns-12.gutter-large .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
  .columns-4.gutter-xlarge .column,
  .columns-5.gutter-xlarge .column,
  .columns-6.gutter-xlarge .column,
  .columns-8.gutter-xlarge .column,
  .columns-12.gutter-xlarge .column {
    width: calc(33.333333% - 2.666666667rem);
    margin-right: 4rem !important;
  }
  .columns-4.gutter-xlarge .column:nth-child(4n+4),
  .columns-5.gutter-xlarge .column:nth-child(4n+4),
  .columns-6.gutter-xlarge .column:nth-child(4n+4),
  .columns-8.gutter-xlarge .column:nth-child(4n+4),
  .columns-12.gutter-xlarge .column:nth-child(4n+4) {
    margin-right: 4rem !important;
  }
  .columns-4.gutter-xlarge .column:nth-child(3n+3),
  .columns-5.gutter-xlarge .column:nth-child(3n+3),
  .columns-6.gutter-xlarge .column:nth-child(3n+3),
  .columns-8.gutter-xlarge .column:nth-child(3n+3),
  .columns-12.gutter-xlarge .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
  .columns-4 .column,
  .columns-5 .column,
  .columns-6 .column,
  .columns-8 .column,
  .columns-12 .column {
    width: calc(33.333333% - 1rem);
    margin-right: 1.5rem !important;
  }
  .columns-4 .column:nth-child(4n+4),
  .columns-5 .column:nth-child(4n+4),
  .columns-6 .column:nth-child(4n+4),
  .columns-8 .column:nth-child(4n+4),
  .columns-12 .column:nth-child(4n+4) {
    margin-right: 1.5rem !important;
  }
  .columns-4 .column:nth-child(3n+3),
  .columns-5 .column:nth-child(3n+3),
  .columns-6 .column:nth-child(3n+3),
  .columns-8 .column:nth-child(3n+3),
  .columns-12 .column:nth-child(3n+3) {
    margin-right: 0 !important;
  }
}

@media (min-width: 680px) and (max-width: 850px) {
  .columns-3.gutter-small .column,
  .columns-4.gutter-small .column,
  .columns-5.gutter-small .column,
  .columns-6.gutter-small .column,
  .columns-8.gutter-small .column,
  .columns-12.gutter-small .column {
    width: calc(50% - 0.5rem);
    margin-right: 1rem !important;
  }
  .columns-3.gutter-small .column:nth-child(4n+4),
  .columns-4.gutter-small .column:nth-child(4n+4),
  .columns-5.gutter-small .column:nth-child(4n+4),
  .columns-6.gutter-small .column:nth-child(4n+4),
  .columns-8.gutter-small .column:nth-child(4n+4),
  .columns-12.gutter-small .column:nth-child(4n+4) {
    margin-right: 1rem !important;
  }
  .columns-3.gutter-small .column:nth-child(3n+3),
  .columns-4.gutter-small .column:nth-child(3n+3),
  .columns-5.gutter-small .column:nth-child(3n+3),
  .columns-6.gutter-small .column:nth-child(3n+3),
  .columns-8.gutter-small .column:nth-child(3n+3),
  .columns-12.gutter-small .column:nth-child(3n+3) {
    margin-right: 1rem !important;
  }
  .columns-3.gutter-small .column:nth-child(2n+2),
  .columns-4.gutter-small .column:nth-child(2n+2),
  .columns-5.gutter-small .column:nth-child(2n+2),
  .columns-6.gutter-small .column:nth-child(2n+2),
  .columns-8.gutter-small .column:nth-child(2n+2),
  .columns-12.gutter-small .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .columns-3.gutter-medium .column,
  .columns-4.gutter-medium .column,
  .columns-5.gutter-medium .column,
  .columns-6.gutter-medium .column,
  .columns-8.gutter-medium .column,
  .columns-12.gutter-medium .column {
    width: calc(50% - 1rem);
    margin-right: 2rem !important;
  }
  .columns-3.gutter-medium .column:nth-child(4n+4),
  .columns-4.gutter-medium .column:nth-child(4n+4),
  .columns-5.gutter-medium .column:nth-child(4n+4),
  .columns-6.gutter-medium .column:nth-child(4n+4),
  .columns-8.gutter-medium .column:nth-child(4n+4),
  .columns-12.gutter-medium .column:nth-child(4n+4) {
    margin-right: 2rem !important;
  }
  .columns-3.gutter-medium .column:nth-child(3n+3),
  .columns-4.gutter-medium .column:nth-child(3n+3),
  .columns-5.gutter-medium .column:nth-child(3n+3),
  .columns-6.gutter-medium .column:nth-child(3n+3),
  .columns-8.gutter-medium .column:nth-child(3n+3),
  .columns-12.gutter-medium .column:nth-child(3n+3) {
    margin-right: 2rem !important;
  }
  .columns-3.gutter-medium .column:nth-child(2n+2),
  .columns-4.gutter-medium .column:nth-child(2n+2),
  .columns-5.gutter-medium .column:nth-child(2n+2),
  .columns-6.gutter-medium .column:nth-child(2n+2),
  .columns-8.gutter-medium .column:nth-child(2n+2),
  .columns-12.gutter-medium .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .columns-3.gutter-large .column,
  .columns-4.gutter-large .column,
  .columns-5.gutter-large .column,
  .columns-6.gutter-large .column,
  .columns-8.gutter-large .column,
  .columns-12.gutter-large .column {
    width: calc(50% - 1.5rem);
    margin-right: 3rem !important;
  }
  .columns-3.gutter-large .column:nth-child(4n+4),
  .columns-4.gutter-large .column:nth-child(4n+4),
  .columns-5.gutter-large .column:nth-child(4n+4),
  .columns-6.gutter-large .column:nth-child(4n+4),
  .columns-8.gutter-large .column:nth-child(4n+4),
  .columns-12.gutter-large .column:nth-child(4n+4) {
    margin-right: 3rem !important;
  }
  .columns-3.gutter-large .column:nth-child(3n+3),
  .columns-4.gutter-large .column:nth-child(3n+3),
  .columns-5.gutter-large .column:nth-child(3n+3),
  .columns-6.gutter-large .column:nth-child(3n+3),
  .columns-8.gutter-large .column:nth-child(3n+3),
  .columns-12.gutter-large .column:nth-child(3n+3) {
    margin-right: 3rem !important;
  }
  .columns-3.gutter-large .column:nth-child(2n+2),
  .columns-4.gutter-large .column:nth-child(2n+2),
  .columns-5.gutter-large .column:nth-child(2n+2),
  .columns-6.gutter-large .column:nth-child(2n+2),
  .columns-8.gutter-large .column:nth-child(2n+2),
  .columns-12.gutter-large .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .columns-3.gutter-xlarge .column,
  .columns-4.gutter-xlarge .column,
  .columns-5.gutter-xlarge .column,
  .columns-6.gutter-xlarge .column,
  .columns-8.gutter-xlarge .column,
  .columns-12.gutter-xlarge .column {
    width: calc(50% - 2rem);
    margin-right: 4rem !important;
  }
  .columns-3.gutter-xlarge .column:nth-child(4n+4),
  .columns-4.gutter-xlarge .column:nth-child(4n+4),
  .columns-5.gutter-xlarge .column:nth-child(4n+4),
  .columns-6.gutter-xlarge .column:nth-child(4n+4),
  .columns-8.gutter-xlarge .column:nth-child(4n+4),
  .columns-12.gutter-xlarge .column:nth-child(4n+4) {
    margin-right: 4rem !important;
  }
  .columns-3.gutter-xlarge .column:nth-child(3n+3),
  .columns-4.gutter-xlarge .column:nth-child(3n+3),
  .columns-5.gutter-xlarge .column:nth-child(3n+3),
  .columns-6.gutter-xlarge .column:nth-child(3n+3),
  .columns-8.gutter-xlarge .column:nth-child(3n+3),
  .columns-12.gutter-xlarge .column:nth-child(3n+3) {
    margin-right: 4rem !important;
  }
  .columns-3.gutter-xlarge .column:nth-child(2n+2),
  .columns-4.gutter-xlarge .column:nth-child(2n+2),
  .columns-5.gutter-xlarge .column:nth-child(2n+2),
  .columns-6.gutter-xlarge .column:nth-child(2n+2),
  .columns-8.gutter-xlarge .column:nth-child(2n+2),
  .columns-12.gutter-xlarge .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .columns-3 .column,
  .columns-4 .column,
  .columns-5 .column,
  .columns-6 .column,
  .columns-8 .column,
  .columns-12 .column {
    width: calc(50% - 0.75rem);
    margin-right: 1.5rem !important;
  }
  .columns-3 .column:nth-child(4n+4),
  .columns-4 .column:nth-child(4n+4),
  .columns-5 .column:nth-child(4n+4),
  .columns-6 .column:nth-child(4n+4),
  .columns-8 .column:nth-child(4n+4),
  .columns-12 .column:nth-child(4n+4) {
    margin-right: 1.5rem !important;
  }
  .columns-3 .column:nth-child(3n+3),
  .columns-4 .column:nth-child(3n+3),
  .columns-5 .column:nth-child(3n+3),
  .columns-6 .column:nth-child(3n+3),
  .columns-8 .column:nth-child(3n+3),
  .columns-12 .column:nth-child(3n+3) {
    margin-right: 1.5rem !important;
  }
  .columns-3 .column:nth-child(2n+2),
  .columns-4 .column:nth-child(2n+2),
  .columns-5 .column:nth-child(2n+2),
  .columns-6 .column:nth-child(2n+2),
  .columns-8 .column:nth-child(2n+2),
  .columns-12 .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
}

@media (min-width: 480px) and (max-width: 680px) {
  .columns-2 .column,
  .columns-3 .column,
  .columns-4 .column,
  .columns-5 .column,
  .columns-6 .column,
  .columns-8 .column,
  .columns-12 .column {
    width: 100% !important;
    margin-right: 0 !important;
    float: none !important;
  }
  .replace-above.gutter-small .column {
    width: calc(50% - 0.5rem);
    margin-right: 1rem !important;
  }
  .replace-above.gutter-small .column:nth-child(4n+4) {
    margin-right: 1rem !important;
  }
  .replace-above.gutter-small .column:nth-child(3n+3) {
    margin-right: 1rem !important;
  }
  .replace-above.gutter-small .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .replace-above.gutter-medium .column {
    width: calc(50% - 1rem);
    margin-right: 2rem !important;
  }
  .replace-above.gutter-medium .column:nth-child(4n+4) {
    margin-right: 2rem !important;
  }
  .replace-above.gutter-medium .column:nth-child(3n+3) {
    margin-right: 2rem !important;
  }
  .replace-above.gutter-medium .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .replace-above.gutter-large .column {
    width: calc(50% - 1.5rem);
    margin-right: 3rem !important;
  }
  .replace-above.gutter-large .column:nth-child(4n+4) {
    margin-right: 3rem !important;
  }
  .replace-above.gutter-large .column:nth-child(3n+3) {
    margin-right: 3rem !important;
  }
  .replace-above.gutter-large .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .replace-above.gutter-xlarge .column {
    width: calc(50% - 2rem);
    margin-right: 4rem !important;
  }
  .replace-above.gutter-xlarge .column:nth-child(4n+4) {
    margin-right: 4rem !important;
  }
  .replace-above.gutter-xlarge .column:nth-child(3n+3) {
    margin-right: 4rem !important;
  }
  .replace-above.gutter-xlarge .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
  .replace-above .column {
    width: calc(50% - 0.75rem);
    margin-right: 1.5rem !important;
  }
  .replace-above .column:nth-child(4n+4) {
    margin-right: 1.5rem !important;
  }
  .replace-above .column:nth-child(3n+3) {
    margin-right: 1.5rem !important;
  }
  .replace-above .column:nth-child(2n+2) {
    margin-right: 0 !important;
  }
}

@media (max-width: 480px) {
  .columns-2 .column,
  .columns-3 .column,
  .columns-4 .column,
  .columns-5 .column,
  .columns-6 .column,
  .columns-8 .column,
  .columns-12 .column {
    width: 100% !important;
    margin-right: 0 !important;
    float: none !important;
  }
}

.fullwidth {
  width: 100%;
  height: auto;
  position: relative;
}

.centerbox {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

@media (max-width: 1199px) {
  .centerbox {
    max-width: 1140px;
  }
}

@media (max-width: 1150px) {
  .centerbox {
    width: 90%;
    max-width: 90%;
  }
}

.air-top {
  margin-top: 40px;
}

.air-top-small {
  margin-top: 20px;
}

.air-bottom {
  margin-bottom: 40px;
}

.air-bottom-small {
  margin-bottom: 20px;
}

.align-center {
  text-align: center;
}

.centerblock {
  margin-left: auto;
  margin-right: auto;
}

.centerblock.full {
  width: 100%;
}

.centerblock.large {
  width: 75%;
}

.centerblock.medium {
  width: 65%;
}

.centerblock.small {
  width: 50%;
}

@media (max-width: 1040px) {
  .centerblock.large, .centerblock.medium, .centerblock.small {
    width: 100%;
  }
}

.col-2 {
  clear: both;
  position: relative;
}

.col-2:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.col-2 .column {
  width: calc(50% - 40px);
  height: auto;
  min-height: 40px;
  float: left;
  position: static;
}

.col-2 .column .center-vertical {
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  display: inline-block;
  width: calc(50% - 20px);
  height: auto;
  position: absolute;
  top: 50%;
}

.col-2 .column:nth-child(1) {
  margin-right: 40px;
}

.col-2 .column:nth-child(1) .center-vertical {
  left: 0;
}

.col-2 .column:nth-child(2) .center-vertical {
  right: 0;
}

.col-2.responsive-flip .column:nth-child(1) {
  float: right;
}

.col-2.responsive-flip .column:nth-child(1) .center-vertical {
  left: auto;
  right: 0;
}

.col-2.responsive-flip .column:nth-child(2) {
  float: left;
}

.col-2.responsive-flip .column:nth-child(2) .center-vertical {
  right: auto;
  left: 0;
}

@media (max-width: 960px) {
  .col-2 .column, .col-2.responsive-flip .column {
    width: 100%;
    float: none;
  }
  .col-2 .column .center-vertical, .col-2.responsive-flip .column .center-vertical {
    -moz-transform: translateY(0);
    -o-transform: translateY(0);
    -ms-transform: translateY(0);
    -webkit-transform: translateY(0);
    transform: translateY(0);
    display: block;
    width: 100%;
    position: static;
  }
  .col-2 .column:nth-child(odd), .col-2.responsive-flip .column:nth-child(odd) {
    margin-right: 0;
  }
}

.page-landing-webinar .webinar-header-banner {
  background-image: -o-linear-gradient(180deg, #f6f6f6, #dddddd);
  background-image: -moz-linear-gradient(180deg, #f6f6f6, #dddddd);
  background-image: -webkit-linear-gradient(180deg, #f6f6f6, #dddddd);
  background-image: linear-gradient(180deg, #f6f6f6, #dddddd);
  box-shadow: inset 0 -6px 0 0 #d5d5d5;
}

.page-landing-webinar .webinar-header {
  padding: 15px 396px 25px 34px;
  text-align: center;
}

@media (max-width: 1150px) {
  .page-landing-webinar .webinar-header {
    padding-right: 34px;
    padding-bottom: 80px;
  }
  .page-landing-webinar .webinar-header #webinar-invitation-header-arrow {
    display: none;
  }
  .page-landing-webinar .webinar-header #webinar-invitation-header-seal {
    top: auto;
    bottom: -60px;
    left: 50%;
    margin-left: -60px;
  }
}

.page-landing-webinar .webinar-content figure img {
  display: block;
  width: 100%;
  height: auto;
  margin: 0 auto;
}

@media (max-width: 560px) {
  .page-landing-webinar .webinar-content figure img {
    max-width: 100%;
  }
}

.page-landing-webinar .webinar-section {
  clear: both;
  width: 100%;
  height: auto;
  position: relative;
}

.page-landing-webinar .webinar-section:after {
  display: block;
  visibility: hidden;
  clear: both;
  height: 0;
  content: " ";
  font-size: 0;
}

.page-landing-webinar .webinar-section .webinar-video,
.page-landing-webinar .webinar-section .webinar-form {
  display: block;
  float: left;
  height: auto;
}

.page-landing-webinar .webinar-section .webinar-video {
  width: calc(100% - 400px);
  margin-top: 20px;
  margin-right: 120px;
}

.page-landing-webinar .webinar-section .webinar-video .webinar-video-box {
  width: 100%;
  height: auto;
  margin-top: 78px;
  position: relative;
}

.page-landing-webinar .webinar-section .webinar-video .webinar-video-box .video-frame {
  display: block;
  width: 100%;
  height: 0;
  padding-top: calc(100% / 1.7783);
  border: 1px solid #e5e5e5;
  position: relative;
}

.page-landing-webinar .webinar-section .webinar-video .webinar-video-box .video-frame img,
.page-landing-webinar .webinar-section .webinar-video .webinar-video-box .video-frame iframe {
  display: block;
  width: calc(100% - 4px);
  height: calc(100% - 4px);
  position: absolute;
  top: 2px;
  left: 2px;
}

.page-landing-webinar .webinar-section .webinar-video .webinar-video-box .video-frame .arrow {
  display: block;
  width: 120px;
  height: 100%;
  position: absolute;
  right: -120px;
  top: 0;
  background: url(../img/video-arrow.png) no-repeat left center;
  background-size: contain;
}

.page-landing-webinar .webinar-section .webinar-video .webinar-video-box .video-frame.invitation-imk {
  padding-top: calc(100% / 1.78908);
}

.page-landing-webinar .webinar-section .webinar-video .webinar-video-box.bottom-section {
  margin-top: 0;
}

.page-landing-webinar .webinar-section .webinar-video .webinar-video-footer {
  margin-top: 60px;
}

.page-landing-webinar .webinar-section .webinar-form {
  width: 280px;
  margin-top: 20px;
  background-color: transparent;
}

.page-landing-webinar .webinar-section .webinar-form .webinar-optin-box {
  background-image: -o-linear-gradient(0deg, #ca88b1, #f0d5e7);
  background-image: -moz-linear-gradient(0deg, #ca88b1, #f0d5e7);
  background-image: -webkit-linear-gradient(0deg, #ca88b1, #f0d5e7);
  background-image: linear-gradient(0deg, #ca88b1, #f0d5e7);
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 6px;
  padding: 12px;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.35), 0 0 0 1px #a44580;
}

.page-landing-webinar .webinar-section.registration {
  margin-top: 20px;
}

@media (max-width: 1150px) {
  .page-landing-webinar .webinar-section .webinar-video,
  .page-landing-webinar .webinar-section .webinar-form {
    float: none;
  }
  .page-landing-webinar .webinar-section .webinar-video {
    width: 100%;
  }
  .page-landing-webinar .webinar-section .webinar-video .webinar-video-box {
    text-align: center;
  }
  .page-landing-webinar .webinar-section .webinar-video .webinar-video-box .video-frame .invitation-image {
    margin-left: auto !important;
  }
  .page-landing-webinar .webinar-section .webinar-video .webinar-video-box .video-frame .arrow {
    display: none;
  }
  .page-landing-webinar .webinar-section .webinar-video .webinar-video-footer {
    text-align: center;
    margin: 30px 0;
  }
  .page-landing-webinar .webinar-section .webinar-form {
    width: 70%;
    max-width: 800px;
    margin: 0 auto;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box {
    padding: 30px;
    text-align: center;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box h3 {
    text-align: center;
    margin-bottom: 30px;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-select-bk {
    display: inline-block;
    width: 25%;
    padding: 16px;
    vertical-align: top;
    box-shadow: inset 0 0 0 1px black;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-select-bk .webinar-select-date {
    padding-top: 30px;
    position: relative;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-select-bk .webinar-select-date .webinar-select-option {
    position: absolute;
    top: 0;
    left: 50%;
    margin-left: -8px;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-select-bk .free {
    padding-left: 0;
    line-height: 1.25em;
    margin-top: 8px;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-select {
    width: 70%;
    margin: 0 auto 16px auto;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-select .webinar-select-date,
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-select .free {
    display: inline-block;
    vertical-align: top;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-select .free {
    padding-left: 8px;
    margin-top: -1px;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .form-type-textfield {
    display: inline-block;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-btn-register {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
  .page-landing-webinar .webinar-section .webinar-form .webinar-optin-box .webinar-optin-secure {
    display: block;
    width: 80%;
    margin: 16px auto 0 auto;
  }
  .page-landing-webinar .webinar-section .webinar-form #webinar-support {
    width: 100%;
    height: auto;
    background: none;
    background-color: #e8e8e8;
    border-radius: 6px;
    border: 1px solid #d6d6d6;
    text-align: center;
  }
  .page-landing-webinar .webinar-section .webinar-form:last-of-type {
    margin-top: 30px;
  }
  .page-landing-webinar .webinar-section.registration {
    text-align: center;
  }
}

@media (max-width: 1090px) {
  .page-landing-webinar .webinar-section .webinar-form {
    width: 85%;
  }
}

@media (max-width: 1023px) {
  .page-landing-webinar .webinar-section .webinar-form {
    width: 100%;
  }
}

.page-landing-webinar .webinar-invitation-separator {
  margin: 60px 0;
  width: 100%;
  height: 1px;
  background-color: #d6d6d6;
  position: relative;
}

.page-landing-webinar .webinar-invitation-separator:before, .page-landing-webinar .webinar-invitation-separator:after {
  display: block;
  content: "";
  width: 60px;
  height: 1px;
  position: absolute;
  top: 0;
}

.page-landing-webinar .webinar-invitation-separator:before {
  left: 0;
  background-image: -o-linear-gradient(90deg, #ffffff, #d6d6d6);
  background-image: -moz-linear-gradient(90deg, #ffffff, #d6d6d6);
  background-image: -webkit-linear-gradient(90deg, #ffffff, #d6d6d6);
  background-image: linear-gradient(90deg, #ffffff, #d6d6d6);
}

.page-landing-webinar .webinar-invitation-separator:after {
  right: 0;
  background-image: -o-linear-gradient(-90deg, #ffffff, #d6d6d6);
  background-image: -moz-linear-gradient(-90deg, #ffffff, #d6d6d6);
  background-image: -webkit-linear-gradient(-90deg, #ffffff, #d6d6d6);
  background-image: linear-gradient(-90deg, #ffffff, #d6d6d6);
}

/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */

@CHARSET "UTF-8";


body {
  background-color: pink;
}

#body-container {
    background: #ffffff url("../img/webinar_invitation_sprite.png") repeat-x scroll 0 -1250px;
    height: 179px;
}

body #body-container #container {
  padding-top: 0px;
  background-color: #f6f4f2;
}

#webinar-content {
  position: relative;
  display: block;
  width: 1200px;
  min-width: 1200px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Webinar support signin form */

#webinar-support-signin-form {
    border: 1px solid #555555;
    margin-top: 100px;
    padding-top: 15px;
    padding-bottom: 15px;
}

#webinar-support-signin-form .webinar-select {
    display: none;
}


/* Webinar Invitation */
/*-------------------*/

/* Webinar Invitation Style for Paid Advertising with menu on the top */

.webinar-welcome #body-container.invitation {
   margin-top: 50px;
}

.webinar-welcome #webinar-invitation h4 {
  font-size: 20px;
}

.webinar-welcome h1.webinar-invitation-parts-headline {
  margin: 0 0 35px 0;
}

.webinar-welcome .navbar-logo-toggle {
  margin: 0 75px 0 0;
}

/*-------------------*/

.webinar-invitation-separator {
  position: relative;
  margin: 40px 0px;
  width: 1150px;
  height: 1px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat 0px -1164px;
}

#webinar-invitation {
  position: relative;
  display: block;
  width: 1150px;
  min-width: 1150px;
  max-width: 1150px;
  margin: 0 auto;
}

#webinar-invitation-header {
  position: relative;
  height: 179px;
  padding: 15px 396px 25px 34px;
  text-align: center;
  margin-bottom: 10px;
}

#webinar-invitation-video-footer {
  position: relative;
  margin: 60px 0 0 34px;
}

#webinar-invitation .swoosh-difficult {
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -492px -1187px;
  width: 198px;
  height: 41px;
  display: inline-block;
  margin: 0 0 0 -17px;
}

h1.webinar-invitation {
  position: relative;
  line-height: 41px;
  font-size: 35px;
  font-weight: bold;
  color: #000000;
  vertical-align: middle;
  margin: 0;
  text-shadow: 1px 1px 1px #FFFFFF;
}

h1.webinar-invitation.swoosh {
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat 7px -1187px;
}

h2.webinar-invitation {
  position: relative;
  line-height: 41px;
  font-size: 35px;
  font-weight: normal;
  color: #838383;
  text-shadow: 1px 1px 1px #FFFFFF;
  vertical-align: middle;
  margin: 0;
}

h1.webinar-invitation.smaller,
h2.webinar-invitation.smaller {
  line-height: 35px;
  font-size: 28px;
}

h1.webinar-invitation-sms {
    position: relative;
    line-height: 41px;
    font-size: 35px;
    font-weight: bold;
    color: #000000;
    vertical-align: middle;
    margin: 0;
    text-shadow: 1px 1px 1px #FFFFFF;
    background: transparent url("../img/webinar_sms_sprite.png") no-repeat scroll -443px -71px;
}

h2.webinar-invitation-sms {
    position: relative;
    line-height: 41px;
    font-size: 25px;
    text-shadow: 1px 1px 1px #ffffff;
    color: #333333;
    vertical-align: middle;
    margin: 0;
    font-weight: bold;
}

h3.webinar-invitation-sms {
    position: relative;
    font-size: 25px;
    line-height: 41px;
    font-weight: normal;
    color: #838383;
    text-shadow: 1px 1px 1px #ffffff;
    margin: 0;
}

#webinar-invitation-video-footer-sms {
    position: relative;
    margin: 30px 0 0 34px;
    padding: 0 80px 0 0;
    text-align: center;
}

#webinar-invitation-sms-skip {
    position: relative;
    display: inline-block;
    width: 316px;
    height: 52px;
    background: transparent url(../img/webinar_sms_sprite.png) scroll no-repeat -350px -171px;
}

#webinar-invitation-sms-skip:hover {
    background-position: -670px -171px;
}

#webinar-invitation-sms-skip:active {
    background-position: -990px -171px;
}

#webinar-invitation-sms-iphone {
    position: absolute;
    right: -20px;
    top: 90px;
    width: 320px;
    height: 659px;
    background: transparent url(../img/webinar_sms_sprite.png) scroll no-repeat -14px -12px;
}

#webinar-invitation-sms-iphone .form-control:focus {
    box-shadow: none;
}

#webinar-sms-breadcrumb {
    position: relative;
    width: 723px;
    height: 30px;
    background: transparent url(../img/webinar_sms_sprite.png) scroll no-repeat -351px -12px;
    margin: 17px 0 0 34px;
}

#iphone-screen {
    position: relative;
    width: 282px;
    height: 504px;
    margin: 78px 0 0 19px;
}

#iphone-screen .screen-header {
    position: relative;
    text-align: center;
    height: 97px;
    line-height: 23px;
    font-size: 17px;
    color: #56103c;
    font-weight: bold;
    vertical-align: middle;
    padding: 24px 0 0 0;
}

#iphone-screen .screen-body {
    position: relative;
    padding: 15px;
}
#iphone-screen .screen-body .headline {
    position: relative;
    line-height: 29px;
    font-size: 23px;
    color: #56103c;
    font-weight: normal;
    vertical-align: middle;
    text-align: left;
    margin: 0 0 12px 0;
    padding: 0 0 0 4px;
}

#iphone-screen .screen-body .mike {
    position: relative;
    width: 95px;
    height: 95px;
    float: right;
    background: transparent url(../img/webinar_sms_sprite.png) scroll no-repeat -350px -62px;
    margin: -3px 0 0 0;
}

#iphone-screen .screen-body .intro {
    position: relative;
    line-height: 19px;
    font-size: 13px;
    color: #56103c;
    font-weight: normal;
    vertical-align: middle;
    text-align: left;
    margin: 0 0 12px 0;
    padding: 0 0 0 4px;
}

#iphone-screen .screen-body .note {
    position: relative;
    line-height: 15px;
    font-size: 12px;
    color: #56103c;
    font-weight: normal;
    vertical-align: middle;
    text-align: left;
    margin: 5px 0 10px;
    padding: 0 0 0 4px;
}

#iphone-screen .screen-body .steps {
    position: relative;
    line-height: 19px;
    font-size: 12px;
    color: #ffffff;
    font-weight: normal;
    vertical-align: middle;
    text-align: left;
    padding: 0 0 0 4px;
}

.steps .checked {
    position: relative;
    display: inline-block;
    width: 13px;
    height: 13px;
    margin: 0 0 0 5px;
    background: transparent url(../img/webinar_sms_sprite.png) scroll no-repeat -1118px -12px;
}

#iphone-screen .screen-body input.form-control.form-text {
    width: 248px;
    margin: 0 0 0 2px;
    border-color: #9f4a7e;
}

#iphone-screen .screen-body .form-group.form-type-textfield {
    margin-bottom: 6px;
}

#iphone-screen .screen-body input.form-submit {
    position: relative;
    width:252px;
    height: 48px;
    background: transparent url(../img/webinar_sms_sprite.png) scroll no-repeat -350px -231px;
    border: none;
    margin: 0;
    padding: 0;
}

#iphone-screen .screen-body input.form-submit:hover {
    background-position: -607px -231px;
}

#iphone-screen .screen-body input.form-submit:active {
    background-position: -866px -231px;
}

#modal-sms-confirmation #iphone-screen .screen-body .headline {
    padding: 10px 0 20px 4px;
}

#modal-sms-confirmation #iphone-screen .screen-body .mike {
    margin: -20px 0 0 0;
}

#modal-sms-confirmation #iphone-screen .screen-body .intro {

}

#modal-sms-confirmation #iphone-screen .screen-body .note {
    margin-top: 0px;
}

#modal-sms-confirmation #iphone-screen .screen-body .steps {

}

#iphone-screen #modal-iframe {
    position: relative;
    border: none;
    width: 252px;
    height: 88px;
    background-color: transparent;
}

#modal-form {
    background-color: transparent;
}

#modal-form input.form-control.form-text {
    border-color: #9f4a7e;
}

#modal-form .form-group.form-type-textfield {
    margin-bottom: 6px;
}

#modal-form input.form-submit {
    position: relative;
    width: 252px;
    height: 48px;
    background: transparent url(../img/webinar_sms_sprite.png) scroll no-repeat -350px -288px;
    border: none;
    margin: 0;
    padding: 0;
}

#modal-form input.form-submit:hover {
    background-position: -607px -288px;
}

#modal-form input.form-submit:active {
    background-position: -866px -288px;
}


#modal-sms-confirmation .modal-close {
    background: transparent url("/misc/img/modal_close_sprite.png") no-repeat scroll 0 0;
    cursor: pointer;
    height: 63px;
    margin: -32px -32px 0 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 63px;
    z-index: 100;
}

#modal-sms-confirmation #webinar-invitation-sms-iphone {
    position: relative;
    margin: 0;
    left: 0;
    top: 0;
}

#modal-sms-confirmation .modal-close:hover {
    background-position: -63px 0;
}
#modal-sms-confirmation .modal-close:active {
    background-position: -126px 0;
}

#modal-sms-confirmation .modal-dialog,
#modal-sms-confirmation .modal-content,
#modal-sms-confirmation .modal-body {
    width: 320px;
    background-color: transparent;
    padding: 0;
    border: none;
    box-shadow: none;
    position: relative;
}

#modal-sms-confirmation .modal-dialog {
    margin-top: 90px;
}
#modal-sms-confirmation #iphone-screen {
    position: absolute;
    width: 282px;
    height: 504px;
    margin: 78px 0 0 19px;
}

#iphone-screen-message {
    position: absolute;
    width: 282px;
    height: 504px;
    margin: 78px 0 0 19px;
}

#iphone-screen-message-backdrop {
    position: absolute;
    width: 282px;
    height: 504px;
    margin: 78px 0 0 19px;
    background-color: #000000;
    opacity: 0.85;
}

#iphone-screen-message #message-popup {
    position: absolute;
    top: 20%;
    left: 50%;
    width: 220px;
    height: 200px;
    margin: 0 0 0 -110px;
    background-color: #ffffff;
    border-radius: 10px;
}

#iphone-screen-message #message-popup .popup-content {
    position: relative;
    width: 100%;
    margin: 20px 0 0 0;
    line-height: 20px;
    vertical-align: middle;
    font-size: 14px;
    font-weight: normal;
    text-align: center;
    color: #333333;
    padding: 17px;
}

#iphone-screen-message #message-popup .popup-footer {
    position: absolute;
    width: 100%;
    height: 50px;
    line-height: 50px;
    vertical-align: middle;
    font-size: 16px;
    font-weight: normal;
    text-align: center;
    cursor: pointer;
    color: #2885d1;
    border-top: 1px solid #eeeeee;
    border-radius: 0 0 10px 10px;
    bottom: 0;
}

#iphone-screen-message #message-popup .popup-footer:hover {
    background-color: #eeeeee;
}

.modal-backdrop.in {
    opacity: 0.8;
}

#webinar-bottom-header {
  margin: 0 0 25px 34px;
}

#webinar-invitation-header-arrow {
  position: absolute;
  right: 57px;
  top: 0px;
  width: 175px;
  height: 190px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -232px -5px;
}

#webinar-invitation-header-arrow.modal-splittest {
  height: 338px;
  background-position: -407px -5px;
}

#webinar-invitation-header-seal {
  position: absolute;
  left: 712px;
  top: 126px;
  width: 120px;
  height: 118px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat 0px 0px;
}

#webinar-invitation-header-new-seal {
  position: absolute;
  left: 712px;
  top: 126px;
  width: 120px;
  height: 118px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat 0px -120px;
}

#webinar-invitation-video {
  padding-right: 34px;
}

#webinar-invitation-video-box {
  position: relative;
}

#invitation-video {
  position: relative;
  width: 723px;
  height: 408px;
  border: 1px solid #eeeeee;
  margin: 78px 0 0 34px;
}

#body-container.sms #invitation-video {
    margin-top: 28px;
}

#webinar-invitation-video-box .arrow {
  position: absolute;
  right: 0;
  top: 0;
  width: 79px;
  height: 407px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -280px -350px;
}

#webinar-invitation-video-box .arrow.modal-splittest {
  height: 332px;
  background-position: -359px -350px;
}

#webinar-invitation-video-bandarole {
  position: absolute;
  z-index: 100;
  width: 113px;
  height: 113px;
  left: 0px;
  top: 0px;
  margin: -5px 0 0 -6px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -119px 0px;
}

#webinar-invitation-form {
  padding-left: 0px;
}

#webinar-optin-bar-top.modal-splittest {
  margin-top: 175px;
}

#webinar-optin-bar-bottom.modal-splittest {
  margin-top: 60px;
}

h4.webinar-optin {
  position: relative;
  text-align: center;
  line-height: 24px;
  font-size: 18px;
  vertical-align: middle;
  color: #000000;
}

h5.webinar-optin {
  position: relative;
  text-align: center;
  line-height: 24px;
  font-size: 18px;
  font-weight: bold;
  vertical-align: middle;
  color: #6a0736;
}

.webinar-optin-box {
  position: relative;
  width: 280px;
  height: 407px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat 0px -350px;
  padding: 12px;
}

form.webinar-form .form-group {
  margin: 10px 0 7px 0;
}
form input[type=text].webinar-optin-email {
  width: 251px;
  margin-left: 3px;
  color: #626262;
  font-size: 16px;
  border-color: #a64381;
}

form input[type=text].webinar-optin-email:focus {
    box-shadow: none;
}

h3.webinar-optin {
  line-height: 24px;
  font-size: 22px;
  vertical-align: middle;
  font-weight: bold;
  color: #570e3c;
  margin: 0 0 10px 0;
  text-shadow: 1px 1px 1px #FFFFFF;
}

.webinar-select {
  margin-bottom: 7px;
}

.webinar-select-date {
  line-height: 17px;
  font-size: 15px;
  font-weight: bold;
  color: #6d1740;
  vertical-align: middle;
  text-shadow: 1px 1px 1px #FFFFFF;
  display: block;
}

.webinar-select.inactive .webinar-select-date {
  color: #ae6689;
}

.webinar-select .free {
  display: block;
  padding-left: 25px;
  font-weight: normal;
  text-shadow: 1px 1px 1px #FFFFFF;
  color: #6d1740;
}

.webinar-select.inactive .free {
  color: #ae6689;
}

.webinar-select-option {
  display: inline-block;
  width: 17px;
  height: 17px;
  line-height: 17px;
  vertical-align: top;
  margin-right: 8px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -582px 0px;
  cursor: pointer;
}

.webinar-select .webinar-select-option:hover { background-position: -599px 0px; }
.webinar-select .webinar-select-option:active { background-position: -616px 0px; }

.webinar-select .webinar-select-option.selected,
.webinar-select .webinar-select-option.selected:hover {
  background-position: -616px 0px;
}

.webinar-select.inactive .webinar-select-option,
.webinar-select.inactive .webinar-select-option:hover,
.webinar-select.inactive .webinar-select-option:active {
  background-position: -633px 0px;
}

.webinar-btn-register.btn.btn-submit.btn-green {
  position: relative;
  width: 256px;
  height: 55px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat 0px -1429px;
  border: none;
  box-shadow: none;
}

.webinar-btn-register.btn.btn-submit.btn-green,
.webinar-btn-register.btn.btn-submit.btn-green:focus,
.webinar-btn-register.btn.btn-submit.btn-green:hover,
.webinar-btn-register.btn.btn-submit.btn-green:active {
    background-color: transparent;
    border: none;
}

.webinar-btn-register.modal-splittest {
  display: block;
  margin: 21px auto 0;
}

.webinar-btn-register.btn.btn-submit.btn-green { background-position: 0px -1539px; }
.webinar-btn-register.btn.btn-submit.btn-green:hover { background-position: -256px -1539px; }
.webinar-btn-register.btn.btn-submit.btn-green:active { background-position: -512px -1539px; }

.webinar-btn-startseite {
  position: relative;
  width: 348px;
  height: 55px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat 0px -1597px;
  border: none;
  cursor: pointer;
}
.webinar-btn-startseite:hover { background-position: -348px -1597px; }
.webinar-btn-startseite:active { background-position: -696px -1597px; }

.webinar-optin-secure {
  line-height: 14px;
  font-size: 12px;
  vertical-align: middle;
  color: #ffffff;
  margin: 14px 0 0 0;
}

.secure-icon {
  position: relative;
  display: inline-block;
  width: 10px;
  height: 12px;
  line-height: 14px;
  margin-right: 3px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -650px 0px;
  vertical-align: middle;
}

#webinar-optin-modal .modal-dialog {
  width: 280px;
}

#webinar-optin-modal .modal-body {
  background-color: transparent;
  padding: 0px;
}

.webinar-optin-box button.close { display: none; }
.webinar-optin-box button.close.modal-splittest { display: inherit; }

.webinar-social {
  position: relative;
  height: 18px;
  margin: 20px 0px;
}

.webinar-social.modal-splittest {
  margin: 34px 0px;
}

#webinar-optin-bar-bottom .webinar-social.modal-splittest {
  margin: 14px 0px 16px 0;
}

.fb-like {
  position: absolute;
  top: 0;
  left: 20px;
}

.gplus {
  position: absolute;
  top: 0;
  right: 0px;
}

#webinar-support {
  position: relative;
  width: 271px;
  height: 90px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -438px -350px;
  padding: 7px 20px;
}

#webinar-support p {
  font-size: 14px;
  line-height: 19px;
  color: #5e5e5e;
  vertical-align: middle;
  margin: 0;
  padding: 0;
}

#webinar-support p.question {
  font-weight: bold;
  color: #000000;
}

#webinar-welcome-invitation-part1-image-large {
  position: relative;
  width: 900px;
  height: 567px;
  margin: 15px auto 35px;
  background: transparent url(https://assets.klicktipp.com/webinar/obama_squeeze_big.png) scroll no-repeat;
}

#webinar-invitation-part1-image {
  position: relative;
  width: 350px;
  height: 283px;
  margin: 0 auto;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -848px 0px;
}

#webinar-welcome-invitation-part2-image-1 {
  position: relative;
  width: 540px;
  height: 437px;
  margin: -25px 0 auto;
  background: transparent url(https://assets.klicktipp.com/webinar/conversion_rates.png) scroll no-repeat;
}

#webinar-welcome-invitation-part2-image-2 {
  position: relative;
  width: 540px;
  height: 437px;
  margin: 0 auto;
  background: transparent url(https://assets.klicktipp.com/webinar/return_on_investment.png) scroll no-repeat;
}

#webinar-welcome-invitation-part2-image-3 {
  position: relative;
  width: 540px;
  height: 412px;
  margin: 24px 0 auto;
  background: transparent url(https://assets.klicktipp.com/webinar/amazon_revenue.png) scroll no-repeat;
}

#webinar-invitation-part2-image {
  position: relative;
  width: 350px;
  height: 227px;
  margin: 0 auto;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -848px -283px;
}

#webinar-welcome-invitation-part3-image-large {
  position: relative;
  width: 839px;
  height: 572px;
  margin: 0 auto;
  background: transparent url(https://assets.klicktipp.com/webinar/happy_family_big.jpg) scroll no-repeat;
}

#webinar-invitation-part3-image {
  position: relative;
  width: 350px;
  height: 227px;
  margin: 0 auto;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat -848px -510px;
}

h1.webinar-invitation-parts-headline {
  position: relative;
  line-height: 41px;
  font-size: 35px;
  font-weight: normal;
  color: #000000;
  vertical-align: middle;
  margin: 0 0 15px 0;
}

p.webinar-invitation-parts-text {
  position: relative;
  color: #212121;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 18px;
  font-family: "Lucida Grande", "Lucida Sans Unicode", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

#invitation-imk {
  position: relative;
  width: 724px;
  height: 407px;
  background: transparent url(../img/webinar_invitation_sprite.png) scroll no-repeat 0px -757px;
  margin-left: 33px;
}
#invitation-imk.modal-splittest {
  height: 332px;
  background-position: -725px -757px;
}

/* Footer */
/*-------------------*/

#webinar-footer,
#webinar-footer span {
  text-align: center;
  margin: 50px 0px;
  color: #cccccc;
  font-size: 12px;
  line-height: 16px;
  vertical-align: middle;
}

#webinar-footer span.footer-separator {
  padding: 0px 10px;
}

#webinar-footer a,
#webinar-footer a:visited,
#webinar-footer a:active,
#webinar-footer a:focus {
  color: #cccccc;
  font-size: 12px;
  line-height: 16px;
  vertical-align: middle;
  text-decoration: none;
  display: inline-block;
}

#webinar-footer a:hover {
  text-decoration: underline;
  color: #bbbbbb;
}

/* Drupal Debug */
.dev-query {
    display: none;
}

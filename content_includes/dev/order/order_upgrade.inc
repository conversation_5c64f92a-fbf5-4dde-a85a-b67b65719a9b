<?php

global $user;

$CurrentPackage = ciapi_get_current_user_product($user);

$current_package = $CurrentPackage['ProductName'];

$user_group_id = $user->RelUserGroupID;

if ( strpos($current_package, 'Enterprise') !== FALSE ) { // any enterprise package
  $current_product = 'Enterprise';
  $upgrade_product = 'Enterprise';
}

switch ($user_group_id) {

  case 6: // Standard 10.000
    $current_product = 'Standard';
    $upgrade_product = 'Deluxe';
    $product_type = 'current';
    break;
  case 9: // Standard 20.000
    $current_product = 'Standard';
    $upgrade_product = 'Enterprise';
    $upgrade_subpackage = '25.000';
    break;
  case 11: // Premium 10.000
    $current_product = 'Premium';
    $upgrade_product = 'Deluxe';
    $product_type = 'current';
    break;
  case 13: // Premium 20.000
    $current_product = 'Premium';
    $upgrade_product = 'Enterprise';
    $upgrade_subpackage = '25.000';
    break;
  case 21: // Premium 40.000
    $current_product = 'Premium';
    $upgrade_product = 'Enterprise';
    $upgrade_subpackage = '50.000';
    break;
  case 16: // Deluxe 10.000
  case 23:
    $current_product = 'Deluxe';
    $upgrade_product = 'Enterprise';
    $upgrade_subpackage = '10.000';
    $product_type = 'current';
    break;
  case 17: // Deluxe 20.000
    $current_product = 'Deluxe';
    $upgrade_product = 'Enterprise';
    $upgrade_subpackage = '25.000';
    break;
  case 18: // Deluxe 30.000
    $current_product = 'Deluxe';
    $upgrade_product = 'Enterprise';
    $upgrade_subpackage = '50.000';
    break;
  case 19: // Deluxe 50.000
    $current_product = 'Deluxe';
    $upgrade_product = 'Enterprise';
    $upgrade_subpackage = '100.000';
    break;
  case 20: // Deluxe 100.000
    $current_product = 'Deluxe';
    $upgrade_product = 'Enterprise';
    $upgrade_subpackage = '250.000';
    break;
  case 29: // Enterprise 1.000.000
    $current_subpackage = '1.000.000';
    $upgrade_subpackage = 'empty'; // "empty" because otherwise Enterprise 1.000.000 would have been selected by default when clicking in the Enterprise Section
    break;
  case 28: // Enterprise 500.000
    $current_subpackage = '500.000';
    $upgrade_subpackage = '1.000.000';
    break;
  case 27: // Enterprise 250.000
    $current_subpackage = '250.000';
    $upgrade_subpackage = '500.000';
    break;
  case 26: // Enterprise 100.000
    $current_subpackage = '100.000';
    $upgrade_subpackage = '250.000';
    break;
  case 25: // Enterprise 50.000
    $current_subpackage = '50.000';
    $upgrade_subpackage = '100.000';
    break;
  case 24: // Enterprise 25.000
    $current_subpackage = '25.000';
    $upgrade_subpackage = '50.000';
    break;
  case 31: // Enterprise 10.000 with one mailserver
    $current_subpackage = '10.000';
    $upgrade_subpackage = '15.000';
    break;
  case 32: // Enterprise 10.000 with two mailservers
    $current_subpackage = '10.001';
    $upgrade_subpackage = '15.000';
    break;
  case 33: // Enterprise 15.000
    $current_subpackage = '15.000';
    $upgrade_subpackage = '25.000';
    break;
  default:
    $current_subpackage = '';
    $upgrade_subpackage = '';
    break;
}

$current_interval = ($CurrentPackage['MonthlyPayment']) ? 'monthly' : (($CurrentPackage['AnnualPayment']) ? 'annual' : 'biannual');

$Current = array(
  'package' => $current_product,
  'subpackage' => $current_subpackage,
  'interval' => $current_interval,
);

if (isset($_POST['submit'])) {

  $Products = array(

    'Standard' => array(
      'monthly' => array(
        'product_id' => '36467',
        'upgrade_id' => '539-Vui6SJHIHkYs',
      ),
      'annual' => array(
        'product_id' => '36469',
        'upgrade_id' => '541-3vpWdN3R5Amj',
      ),
      'biannual' => array(
        'product_id' => '36471',
        'upgrade_id' => '543-hlNzkJa5ILww',
      ),
    ),
    'Premium' => array(
      'monthly' => array(
        'product_id' => '36473',
        'upgrade_id' => '545-vRoCmtedcpfu',
      ),
      'annual' => array(
        'product_id' => '36475',
        'upgrade_id' => '547-KEqppP2k6x37',
      ),
      'biannual' => array(
        'product_id' => '36477',
        'upgrade_id' => '549-GPAxmGnR2Q2T',
      ),
    ),
    'Deluxe' => array(
      'monthly' => array(
        'product_id' => '36479',
        'upgrade_id' => '551-87aFWSgKmH0l',
      ),
      'annual' => array(
        'product_id' => '36481',
        'upgrade_id' => '553-EDn9kdbOwcBD',
      ),
      'biannual' => array(
        'product_id' => '36483',
        'upgrade_id' => '555-QeAqLLUV7niI',
      ),
    ),
    'Enterprise' => array(
      '10.000' => array(
        'monthly' => array(
          'product_id' => '36485',
          'upgrade_id' => '479-SeEdzxa18muk',
        ),
        'annual' => array(
          'product_id' => '36487',
          'upgrade_id' => '557-I0yYS8vQpIKm',
        ),
        'biannual' => array(
          'product_id' => '36489',
          'upgrade_id' => '559-FOeV3rSnyX6i',
        ),
      ),
      '10.001' => array(
        'monthly' => array(
          'product_id' => '36491',
          'upgrade_id' => '507-obBOk9bjWTIJ',
        ),
        'annual' => array(
          'product_id' => '37079',
          'upgrade_id' => '561-0hhG34pYGxPm',
        ),
        'biannual' => array(
          'product_id' => '37081',
          'upgrade_id' => '563-MIZxoLkI031s',
        ),
      ),
      '15.000' => array(
        'monthly' => array(
          'product_id' => '36491',
          'upgrade_id' => '507-obBOk9bjWTIJ',
        ),
        'annual' => array(
          'product_id' => '37079',
          'upgrade_id' => '561-0hhG34pYGxPm',
        ),
        'biannual' => array(
          'product_id' => '37081',
          'upgrade_id' => '563-MIZxoLkI031s',
        ),
      ),
      '25.000' => array(
        'monthly' => array(
          'product_id' => '37083',
          'upgrade_id' => '483-gYRNZhJrfqxp',
        ),
        'annual' => array(
          'product_id' => '37085',
          'upgrade_id' => '565-2h1MqCWwY4ZW',
        ),
        'biannual' => array(
          'product_id' => '37087',
          'upgrade_id' => '567-yLluQWP1q9wT',
        ),
      ),
      '50.000' => array(
        'monthly' => array(
          'product_id' => '37089',
          'upgrade_id' => '569-XxFnYBkb732E',
        ),
        'annual' => array(
          'product_id' => '37091',
          'upgrade_id' => '571-IlRuytrRHtMd',
        ),
        'biannual' => array(
          'product_id' => '37093',
          'upgrade_id' => '573-foWY3BVX4ntM',
        ),
      ),
      '100.000' => array(
        'monthly' => array(
          'product_id' => '37095',
          'upgrade_id' => '577-jtz0I25Wjn2F',
        ),
        'annual' => array(
          'product_id' => '37097',
          'upgrade_id' => '579-XAxYPb8qKpS1',
        ),
        'biannual' => array(
          'product_id' => '37099',
          'upgrade_id' => '581-G06ciqvoIyK0',
        ),
      ),
      '250.000' => array(
        'monthly' => array(
          'product_id' => '37101',
          'upgrade_id' => '583-CGK6i8RFn3kt',
        ),
        'annual' => array(
          'product_id' => '37103',
          'upgrade_id' => '585-tlkeqEAfaXOH',
        ),
        'biannual' => array(
          'product_id' => '37105',
          'upgrade_id' => '587-N0riX1KvOvxk',
        ),
      ),
      '500.000' => array(
        'monthly' => array(
          'product_id' => '37107',
          'upgrade_id' => '589-PnYH9qi7o5Hn',
        ),
        'annual' => array(
          'product_id' => '37109',
          'upgrade_id' => '591-Nn8w096Zx6iY',
        ),
        'biannual' => array(
          'product_id' => '37113',
          'upgrade_id' => '593-ug3h0JfITEaK',
        ),
      ),
      '1.000.000' => array(
        'monthly' => array(
          'product_id' => '37115',
          'upgrade_id' => '595-hYAzdFXva7Q4',
        ),
        'annual' => array(
          'product_id' => '37119',
          'upgrade_id' => '597-yezLHAHxAivB',
        ),
        'biannual' => array(
          'product_id' => '37121',
          'upgrade_id' => '599-hvj9Oc269tZJ',
        ),
      ),
    ),
  );

  $package = check_plain($_POST['package']);
  $subpackage = check_plain($_POST['subpackage']);
  $interval = check_plain($_POST['interval']);

  if ($package == 'Enterprise') {
    $product_id = $Products[$package][$subpackage][$interval]['product_id'];
    $upgrade_id = $Products[$package][$subpackage][$interval]['upgrade_id'];
  } else {
      $product_id = $Products[$package][$interval]['product_id'];
      $upgrade_id = $Products[$package][$interval]['upgrade_id'];
  }

  if ( empty($product_id) ) {
    print theme('klicktipp_whitelabel', 'Titel', 'Sie haben kein Produkt ausgewählt');
    exit;
  }

  $package = ($package == 'Enterprise') ? "$package $subpackage" : $package;

  $current_product = strpos($CurrentPackage['ProductName'], $package);

  if ( $current_product !== FALSE && $current_interval == $interval ) {
    print theme('klicktipp_whitelabel', 'Titel', 'gleiches Produkt');
    exit;
  }

  $amemberID = $user->amemberid;
  $username = $user->name;
  $firstname = $user->FirstName;
  $lastname = $user->LastName;
  $client_email = Subscribers::DepunycodeEmailAddress($user->mail);

  $support_email = "<EMAIL>";
  $email_subject = "Upgrade / Downgrade";

  $message = "
  <p><strong>Up- / Downgrade von $firstname $lastname ($client_email):</strong></p>
  <p>---------</p>
  <p>Klick-Tipp Benutzername: $username</p>
  <p>Derzeitiges Paket: $current_package</p>
  <p>Derzeitiges Zahlungsintervall: $current_interval</p>
  <p>Will up- / downgraden auf: Klick-Tipp $package</p>
  <p>Gewünschtes Zahlungsintervall: $interval</p>
  <p>Zahlungsinformationen des Kunden: https://www.klick-tipp.com/crm/admin/users.php?member_id={$amemberID}&action=payments</p>
  <p>---------</p>
  ";

  // send email with subscriber information to the support system
  $params = array(
    'subject' => $email_subject,
    'body' => $message,
    'placeholders' => array(
      '!site' => KLICKTIPP_DOMAIN,
    ),
  );

  drupal_mail('klicktipp', 'system', $support_email, language_default(), $params, $client_email);

  $account = user_load($user->uid);

  $latest_payment = ciapi_digistore_get_current_user_payment($account);

  $order_receipt = explode("-", $latest_payment['receipt_id']);
  $order_id = $order_receipt[0];

  if (!empty($order_id)) {
    $cart_url = 'https://www.digistore24.com/upgrade/' . $upgrade_id . '/' . $order_id;
    klicktipp_goto($cart_url);
    exit;
  }

  klicktipp_goto(ciapi_digistore_personalize_cart_url($product_id, $user));
  exit;

}

$Prices = array(

    'Standard' => array(
      'monthly' => '27,00 €',
      'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
      'annual' => '289,00 €',
      'annual_actual' => '324,00 €',
      'annual_saving' => 'Ihre Ersparnis: 35,00 €',
      'biannual' => '518,40 €',
      'biannual_actual' => '648,00 €',
      'biannual_saving' => 'Ihre Ersparnis: 129,60 €',
    ),
    'Premium' => array(
      'monthly' => '47,00 €',
      'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
      'annual' => '499,00 €',
      'annual_actual' => '564,00 €',
      'annual_saving' => 'Ihre Ersparnis: 65,00 €',
      'biannual' => '902,40 €',
      'biannual_actual' => '1.128,00 €',
      'biannual_saving' => 'Ihre Ersparnis: 225,60 €',
    ),
    'Deluxe' => array(
      'monthly' => '67,00 €',
      'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
      'annual' => '719,00 €',
      'annual_actual' => '804,00 €',
      'annual_saving' => 'Ihre Ersparnis: 85,00 €',
      'biannual' => '1.286,40 €',
      'biannual_actual' => '1.608,00 €',
      'biannual_saving' => 'Ihre Ersparnis: 321,60 €',
    ),
    'Enterprise' => array(
        '10.000' => array(
        'monthly' => '149,00 €',
        'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
        'annual' => '1.609,20 €',
        'annual_actual' => '1.788,00 €',
        'annual_saving' => 'Ihre Ersparnis: 178,80 €',
        'biannual' => '2.860,80 €',
        'biannual_actual' => '3.576,00 €',
        'biannual_saving' => 'Ihre Ersparnis: 715,20 €',
      ),
      '10.001' => array(
        'monthly' => '199,00 €',
        'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
        'annual' => '2.149,20 €',
        'annual_actual' => '2.388,00 €',
        'annual_saving' => 'Ihre Ersparnis: 238,80 €',
        'biannual' => '3.820,80 €',
        'biannual_actual' => '4.776,00 €',
        'biannual_saving' => 'Ihre Ersparnis: 955,20 €',
      ),
      '15.000' => array(
        'monthly' => '199,00 €',
        'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
        'annual' => '2.149,20 €',
        'annual_actual' => '2.388,00 €',
        'annual_saving' => 'Ihre Ersparnis: 238,80 €',
        'biannual' => '3.820,80 €',
        'biannual_actual' => '4.776,00 €',
        'biannual_saving' => 'Ihre Ersparnis: 955,20 €',
      ),
      '25.000' => array(
        'monthly' => '290,00 €',
        'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
        'annual' => '3.132,00 €',
        'annual_actual' => '3.480,00 €',
        'annual_saving' => 'Ihre Ersparnis: 348,00 €',
        'biannual' => '5.568,00 €',
        'biannual_actual' => '6.960,00 €',
        'biannual_saving' => 'Ihre Ersparnis: 1.392,00 €',
      ),
      '50.000' => array(
        'monthly' => '450,00 €',
        'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
        'annual' => '4.860,00 €',
        'annual_actual' => '5.400,00 €',
        'annual_saving' => 'Ihre Ersparnis: 540,00 €',
        'biannual' => '8.640,00 €',
        'biannual_actual' => '10.800,00 €',
        'biannual_saving' => 'Ihre Ersparnis: 2.160,00 €',
      ),
      '100.000' => array(
        'monthly' => '750,00 €',
        'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
        'annual' => '8.100,00 €',
        'annual_actual' => '9.000,00 €',
        'annual_saving' => 'Ihre Ersparnis: 900,00 €',
        'biannual' => '14.400,00 €',
        'biannual_actual' => '18.000,00 €',
        'biannual_saving' => 'Ihre Ersparnis: 3.600,00 €',
      ),
      '250.000' => array(
        'monthly' => '1.500,00 €',
        'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
        'annual' => '16.200,00 €',
        'annual_actual' => '18.000,00 €',
        'annual_saving' => 'Ihre Ersparnis: 1.800,00 €',
        'biannual' => '28.800,00 €',
        'biannual_actual' => '36.000,00 €',
        'biannual_saving' => 'Ihre Ersparnis: 7.200,00 €',
      ),
      '500.000' => array(
        'monthly' => '2.300,00 €',
        'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
        'annual' => '24.840,00 €',
        'annual_actual' => '27.600,00 €',
        'annual_saving' => 'Ihre Ersparnis: 2.760,00 €',
        'biannual' => '44.160,00 €',
        'biannual_actual' => '55.200,00 €',
        'biannual_saving' => 'Ihre Ersparnis: 11.040,00 €',
      ),
      '1.000.000' => array(
        'monthly' => '3.700,00 €',
        'monthly_saving' => 'Ihre Ersparnis: 0,00 €',
        'annual' => '39.960,00 €',
        'annual_actual' => '44.400,00 €',
        'annual_saving' => 'Ihre Ersparnis: 4.440,00 €',
        'biannual' => '71.040,00 €',
        'biannual_actual' => '88.800,00 €',
        'biannual_saving' => 'Ihre Ersparnis: 17.760,00 €',
      ),
    ),
  );

drupal_add_js(array('Prices' => $Prices, 'CurrentPackages' => $Current), 'setting');

drupal_set_title('');

drupal_set_breadcrumb(array());

drupal_add_css("content_includes/css/order_page.css", 'theme');

?>


<style type="text/css">

#upgrade-headline {
    border-bottom: 2px solid #555;
    color: #555555;
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    padding: 10px 0 5px;
}

#upgrade-box {
    margin: 0 0 30px 0;
}

.package-section {
    position: relative;
    border-bottom: 1px solid #b3b3b3;
}

.package-section.subtotal {
    height: 76px;
}

.payment-section {
    position: relative;
    border-bottom: 1px solid #b3b3b3;
    height: 76px;
}

.bundle-header {
    padding: 18px 10px;
}

.package-section.selected .bundle-header {
    background-color: #f7f7f7;
}

.bundle-body {
    border-top: 1px dotted #b3b3b3;
    display: none;
}

.package-section.selected .bundle-body {
    display: block;
    background-color: #f7f7f7;
}

.radio-button-purple:hover { background-position: -20px -340px; }
.radio-button-purple:active,
.selected .radio-button-purple { background-position: -40px -340px; }

.selected .row-header .radio-button-purple { background-position: 0 -340px; }
.selected .row-header .radio-button-purple:hover { background-position: -40px -340px; }
.selected .row-header .radio-button-purple:active,
.selected .row-header.selected .radio-button-purple { background-position: -40px -340px; }

.upgrade-product-title {
    color: #555555;
    display: inline-block;
    font-size: 16px;
    padding: 0 6px;
    width: 238px;
}

.selected .upgrade-product-title {
    font-weight: bold;
}

.selected .enterprise-section .upgrade-product-title {
    font-weight: normal;
    width: 472px;
}

.selected .enterprise-section .selected .upgrade-product-title {
    font-weight: bold;
    width: 500px;
}

.subtotal .upgrade-product-title {
    font-weight: bold;
}

.upgrade-product-subtitle {
    color: #848b94;
    display: inline-block;
    font-size: 14px;
}

.selected .upgrade-product-subtitle {
    font-weight: bold;
}

.selected .enterprise-section .upgrade-product-price {
    font-weight: normal;
}

.selected .enterprise-section .selected .upgrade-product-price {
    font-weight: bold;
}

.upgrade-product-title.interval {
    width: 259px;
}

.upgrade-product-subtitle.interval {
    padding: 0 0 0 5px;
    font-size: 16px;
}

.upgrade-product-price {
    color: #555555;
    display: inline-block;
    font-size: 16px;
    line-height: 15px;
    position: absolute;
    right: 25px;
    top: 20px;
}

.selected .upgrade-product-price,
.subtotal .upgrade-product-price,
.row-header.selected .upgrade-product-setup-price {
  font-weight: bold;
}

.upgrade-product-setup-price {
  display: inline-block;
  font-size: 13px;
  line-height: 15px;
  position: absolute;
  right: 25px;
  top: 40px;
}

#upgrade-box .package-section .bundle-body .row {
    padding: 25px 50px 22px 43px;
}

#upgrade-box .package-section .row .title {
    color: #353535;
    display: block;
    font-size: 18px;
    padding: 10px 0 15px;
}

#upgrade-box .package-section .row .text {
    color: #848b94;
    display: block;
    font-size: 14px;
    padding: 0 0 10px 0;
}

#upgrade-box .package-section .row-header {
    border-top: 1px dotted #b3b3b3;
    padding: 17px 15px 23px;
    position: relative;
}

.continue-align {
    position: relative;
    margin: 0 0 40px 57px;
    text-align: center;
    width: 353px;
}

.package-section .asterix {
    color: #555555;
    display: inline-block;
    font-size: 13px;
    line-height: 15px;
    position: absolute;
    right: 25px;
    top: 40px;
}

.payment-section .asterix {
    display: inline-block;
    font-size: 13px;
    line-height: 15px;
    position: absolute;
    right: 25px;
    top: 40px;
    margin: 0;
}

.asterix {
    margin: 6px 0 35px 0;
}

.asterix.left {
    text-align: left;
}

.package-features {
    padding: 0 0 0 25px;
}

.upgrade-quickhelp.quickhelp-inline .quickhelp-icon {
    position: relative;
    display: inline-block;
    margin: 4px 0 0 5px;
}

.package-cover {
    height: 191px;
    width: 160px;
    margin: 10px 0 0 0;
    position: relative;
    display: block;
}

.standard-box.package-cover {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll 0 -300px transparent;
}

.premium-box.package-cover {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll -160px -300px transparent;
}

.deluxe-box.package-cover {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll -320px -300px transparent;
}

.enterprise-box.package-cover {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll -480px -300px transparent;
}

.package-cover-small {
    width: 95px;
    height: 114px;
    position: relative;
    display: block;
}

.splittest-cover {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll -640px -300px transparent;
    height: 191px;
    width: 160px;
    position: relative;
    display: block;
    margin: 63px 0 0 0;
}

.bundle-body .package-badge {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll 0 0 transparent;
    height: 98px;
    left: 0;
    margin: -25px 0 0 -27px;
    position: absolute;
    top: 0;
    width: 96px;
}

.bundle-body .splittest-badge {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll 0 -98px transparent;
    height: 66px;
    left: 0;
    position: absolute;
    top: 222px;
    width: 190px;
}

.bundle-body .upgrade-plus {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll 0 -164px transparent;
    height: 23px;
    left: 85px;
    position: absolute;
    top: 195px;
    width: 23px;
}

.logo-affilicon {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll 0 -233px transparent;
    height: 25px;
    margin: 4px 10px 0 45px;
    position: relative;
    width: 90px;
}

.logo-digistore {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll -90px -233px transparent;
    height: 25px;
    margin: 4px 10px 0 45px;
    position: relative;
    width: 97px;
}

.logo-wufoo {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll -187px -233px transparent;
    height: 25px;
    margin: 4px 10px 0 45px;
    position: relative;
    width: 71px;
}

.logo-box {
    background: none repeat scroll 0 0 #ffffff;
    border: 1px solid #cecece;
    display: inline-block;
    height: 35px;
    margin: 0 0 0 24px;
}

.logo-box .number-box {
    width: 35px;
    height: 33px;
    border-right: 1px solid #cecece;
    margin: -29px 0 0;
    background-color: #f0f0f0;
}

.logo-box .number {
    font-size: 22px;
    font-weight: bold;
    color: #000000;
    height: 35px;
    text-align: center;
}

#klicktipp-seals {
    background: url("https://assets.klicktipp.com/content_includes/orderpage/klicktipp_seals.png") no-repeat scroll 0 0 transparent;
    display: block;
    height: 150px;
    position: relative;
    width: 845px;
}

#progress-bar {
    background: url("https://assets.klicktipp.com/content_includes/orderpage/progress-bar.png") no-repeat scroll 0 0 transparent;
    display: block;
    height: 37px;
    margin: 0 0 10px 0;
    position: relative;
    width: 848px;
}

#big-clients {
    color: #6b6b6b;
    font-size: 12px;
    padding-top: 10px;
}

.fixed {
    position: fixed;
    top: 50px;
    z-index: 0;
}

.payment-arrow {
    background: url("/misc/img/upgrade_sprite.png") no-repeat scroll 0 -491px transparent;
    height: 260px;
    position: relative;
    width: 100px;
    margin: 0 0 0 41px;
}

.instruction-box {
    background: none repeat scroll 0 0 #f6fae8;
    border: 1px solid #d0dea0;
    display: block;
    padding: 15px;
}

#instruction-text {
    font-size: 17px;
}

</style>

<script type="text/javascript">

$( document ).ready(function() {

  $(".selected").each( function () {

      var func = $(this).attr('data-event-click');
      if ( typeof window[func] === 'function' ) {

        var args = $(this).attr('data-event-click-args');

        if ( args == 'Enterprise' && $("#set_subpackage").val() == '' ) {
          $('#25000').trigger('click');
        }

        window[func]($(this), args);

      }

  });

  var top = $('.sticky').offset().top;

  $(window).scroll(function (event) {
    var y = $(this).scrollTop();
    if (y >= top) {
      $('.sticky').addClass('fixed');
    } else {
      $('.sticky').removeClass('fixed');
      $('.sticky').width($('.sticky').parent().width());
    }
  });

});

window['SelectPayment'] = function ( element, args ) {

    $(".package-section").removeClass("selected");

    element.addClass("selected");

    if ( args != 'Enterprise' ) {
      $('#big-clients').text('');
    } else {
      $('#big-clients').html('* Wenn Sie mit Klick-Tipp mehr als 1.000.000 Kontakte verwalten möchten, dann senden Sie uns bitte eine Anfrage über unser <a href="kontakt">Kontaktformular</a>.');
    }

    set_package( element, args );

    set_savings();

    validation();

    update_view ();
}

window['SelectPaymentInterval'] = function ( element, args ) {

    $(".payment-section").removeClass("selected");

    element.addClass("selected");
    set_interval( element, args );

    set_savings();

    validation();

    update_view ();

}

window['Enterprise'] = function ( element, args ) {

    $(".row-header").removeClass("selected");

    element.addClass("selected");

    $('#big-clients').html('* Wenn Sie mit Klick-Tipp mehr als 1.000.000 Kontakte verwalten möchten, dann senden Sie uns bitte eine Anfrage über unser <a href="kontakt">Kontaktformular</a>.');

    set_subpackage( element, args );

    set_savings();

    validation();

    update_view ();

}

function set_package ( element, args ) {

  $("#set_package").val(args);

  if ( args == 'Enterprise' && $("#set_subpackage").val() == '' ) {
    $('#25000').trigger('click');
  }

}

function set_subpackage ( element, args ) {

  $("#set_subpackage").val(args);

}

function set_interval ( element, args ) {

  $("#set_interval").val(args);

}

function set_savings () {

  var package = $("#set_package").val();
  var subpackage = $("#set_subpackage").val();
  var interval = $("#set_interval").val();

  if ( package != 'Enterprise') {

    $('#monthly .asterix').text(Drupal.settings.Prices[package]['monthly_saving']);
    $('#annual .asterix').text(Drupal.settings.Prices[package]['annual_saving']);
    $('#biannual .asterix').text(Drupal.settings.Prices[package]['biannual_saving']);

    $('#monthly .upgrade-product-price').html("monatlich " + '<span style="color: #b12704;">' + Drupal.settings.Prices[package]['monthly'] + "</span>");
    $('#annual .upgrade-product-price').html("jährlich " + '<span style="color: #888888;">' + "<s>" + Drupal.settings.Prices[package]['annual_actual'] + "</s>" + "</span> " + '<span style="color: #b12704;">' + Drupal.settings.Prices[package]['annual'] + "</span>");
    $('#biannual .upgrade-product-price').html("zweijährlich " + '<span style="color: #888888;">' + "<s>" + Drupal.settings.Prices[package]['biannual_actual'] + "</s>" + "</span> " + '<span style="color: #b12704;">' + Drupal.settings.Prices[package]['biannual'] + "</span>");

    if ( interval == 'annual' ) {

        $('#annual .upgrade-product-price').html("jährlich " + '<span style="color: #888888;">' + "<s>" + Drupal.settings.Prices[package]['annual_actual'] + "</s>" + "</span> " + '<span style="color: #b12704;">' + Drupal.settings.Prices[package]['annual']) + "</span>";
        $('#annual .asterix').text(Drupal.settings.Prices[package]['annual_saving']);

    } else if ( interval == 'biannual' ) {

          $('#biannual .asterix').text(Drupal.settings.Prices[package]['biannual_saving']);
    }

  } else {

      $('#monthly .asterix').text(Drupal.settings.Prices[package][subpackage]['monthly_saving']);
      $('#annual .asterix').text(Drupal.settings.Prices[package][subpackage]['annual_saving']);
      $('#biannual .asterix').text(Drupal.settings.Prices[package][subpackage]['biannual_saving']);

      $('#monthly .upgrade-product-price').html("monatlich " + '<span style="color: #b12704;">' + Drupal.settings.Prices[package][subpackage]['monthly'] + "</span>");
      $('#annual .upgrade-product-price').html("jährlich " + '<span style="color: #888888;">' + "<s>" + Drupal.settings.Prices[package][subpackage]['annual_actual'] + "</s>" + "</span> " + '<span style="color: #b12704;">' + Drupal.settings.Prices[package][subpackage]['annual'] + "</span>");
      $('#biannual .upgrade-product-price').html("zweijährlich " + '<span style="color: #888888;">' + "<s>" + Drupal.settings.Prices[package][subpackage]['biannual_actual'] + "</s>" + "</span> " + '<span style="color: #b12704;">' + Drupal.settings.Prices[package][subpackage]['biannual'] + "</span>");

  }

}

function validation () {

  var package = $("#set_package").val();
  var subpackage = $("#set_subpackage").val();
  var interval = $("#set_interval").val();
  var validation = $("#set_validation").val();

  $(".btn-cta-in-den-warenkorb").show();
  $(".btn-cta-in-den-warenkorb.inactive").hide();
  $("#payment-info").html('Sie können Ihre Auswahl im<br />nächsten Schritt noch einmal überprüfen.');

  if ( package != 'Enterprise') {

    if ( package == Drupal.settings.CurrentPackages['package'] && interval == Drupal.settings.CurrentPackages['interval'] && validation == 'current' ) {
      $(".btn-cta-in-den-warenkorb").hide();
      $(".btn-cta-in-den-warenkorb.inactive").show();
      $("#payment-info").text('Dieses Paket haben Sie bereits ;-)');
    }

  } else {
       if ( package == Drupal.settings.CurrentPackages['package'] && subpackage == Drupal.settings.CurrentPackages['subpackage'] && interval == Drupal.settings.CurrentPackages['interval'] ) {
          $(".btn-cta-in-den-warenkorb").hide();
          $(".btn-cta-in-den-warenkorb.inactive").show();
          $("#payment-info").text('Dieses Paket haben Sie bereits ;-)');
       }
  }

}

</script>

<div id="progress-bar"></div>

<div class="row">

  <div class="col-lg-9 col-md-9 col-sm-9 col-xs-9">

<h1 class="text-center" style="margin-top:1em;">Upgraden für mehr Umsatz.</h1>
<h3 class="text-center" style="margin-top:0,5em; margin-bottom:2em;">Downgraden für weniger ;-)</h3>

  <div id="upgrade-box">
    <p id="upgrade-headline">1. Wählen Sie Ihr Paket</p>

      <div id="standard" class="package-section" data-event-click="SelectPayment" data-event-click-args="Standard">

       <div class="bundle-header">
        <span class="radio-button-purple"></span>
        <div class="upgrade-product-title">Klick-Tipp Standard 10.000</div>
        <div class="upgrade-product-subtitle">Einfacher E-Mail-Versand</div>
        <div class="upgrade-product-price">27,00 € pro Monat</div>
       </div>

       <div id="test" class="bundle-body">
         <div class="row">
           <div class="col-md-3">
             <div class="standard-box package-cover"></div>
           </div>
           <div class="col-md-9 package-features">
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>ContactCloud – alle Kontakte an einem Ort<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-contact-cloud', '#title' => t('ContactCloud'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Tags statt E-Mail-Listen – herausragende Selektion<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-selection', '#title' => t('Tagging selection'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>E-Mail-Marketing Flatrate – unbegrenzter E-Mail-Versand<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-mail-flatrate', '#title' => t('Email flatrate'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Rechtssicheres Double-Opt-in-System<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-doi-system', '#title' => t('Double-Opt-in-System'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Hochoptimierte Mailserver – exzellente Zustellraten<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-mailserver', '#title' => t('Mailserver'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Bis zu 10.000 Kontakte verwalten<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-standard-contacts', '#title' => t('Manage 10.000 subscribers'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>E-Mail-Support, Antwort innerhalb 1 Werktag<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-standard-support', '#title' => t('Email support'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Festpreisgarantie<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-price-guarantee', '#title' => t('Price guarantee'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Upgrade jederzeit möglich<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-upgrade', '#title' => t('Possible upgrade'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline">
             <div class="logo-box">
               <div class="logo-digistore"></div>
               <div class="number-box">
               <div class="number">1</div>
               </div>
             </div>
             <div class="logo-box">
               <div class="logo-affilicon"></div>
               <div class="number-box">
               <div class="number">1</div>
               </div>
             </div>
             <div class="logo-box">
               <div class="logo-wufoo"></div>
               <div class="number-box">
               <div class="number">1</div>
               </div>
             </div>
             <?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-standard-integration-number', '#title' => t('Feature'))); ?>
             </div>
           </div>
         </div>
       </div>

      </div>

      <div id="premium" class="package-section" data-event-click="SelectPayment" data-event-click-args="Premium">

       <div class="bundle-header">
        <span class="radio-button-purple"></span>
        <div class="upgrade-product-title">Klick-Tipp Premium 10.000</div>
        <div class="upgrade-product-subtitle">Automatisierung & Segmentierung</div>
        <div class="upgrade-product-price">47,00 € pro Monat</div>
       </div>

       <div class="bundle-body">
         <div class="row">
           <div class="col-md-3">
             <div class="premium-box package-cover"></div>
           </div>
           <div class="col-md-9 package-features">
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Alle Funktionen von Klick-Tipp Standard inklusive<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-standard-inclusive', '#title' => t('Standard features inclusive'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Newsletter-Splittesting – mehr Reichweite<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-nl-splittest', '#title' => t('Newsletter splittesting'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Tags als Ausschlusskriterium – laserscharfe Segmentierung<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-segmentation', '#title' => t('Tagging segmentation'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>SmartLinks – spezifische Klicks taggen<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-smart-links', '#title' => t('Smart links'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>API-Schnittstelle – einfache Integration<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-api', '#title' => t('API interface'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Bis zu 10.000 Kontakte verwalten<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-premium-contacts', '#title' => t('Manage 10.000 subscribers'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>E-Mail-Support, Antwort innerhalb 1 Werktag<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-premium-support', '#title' => t('Email support'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Festpreisgarantie<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-price-guarantee', '#title' => t('Price guarantee'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Up- und Downgrade jederzeit möglich<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-premium', '#title' => t('Possible downgrade'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline">
             <div class="logo-box">
               <div class="logo-digistore"></div>
               <div class="number-box">
               <div class="number">3</div>
               </div>
             </div>
             <div class="logo-box">
               <div class="logo-affilicon"></div>
               <div class="number-box">
               <div class="number">3</div>
               </div>
             </div>
             <div class="logo-box">
               <div class="logo-wufoo"></div>
               <div class="number-box">
               <div class="number">3</div>
               </div>
             </div>
             <?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-premium-integration-number', '#title' => t('Feature'))); ?>
             </div>
           </div>
         </div>
       </div>

      </div>

      <?php $deluxe = ( $upgrade_product == 'Deluxe' ) ? ' selected' : ''; ?>

      <div id="deluxe" class="package-section<?php print $deluxe; ?>" data-event-click="SelectPayment" data-event-click-args="Deluxe">

       <div class="bundle-header">
        <span class="radio-button-purple"></span>
        <div class="upgrade-product-title">Klick-Tipp Deluxe 10.000</div>
        <div class="upgrade-product-subtitle">Optimierung & Listbuilding-Turbo</div>
        <div class="upgrade-product-price">67,00 € pro Monat</div>
       </div>

       <div class="bundle-body">
         <div class="row">
           <div class="col-md-3">
             <div class="package-badge"></div>
             <div class="deluxe-box package-cover"></div>
             <div class="splittest-cover"></div>
             <div class="upgrade-plus"></div>
             <div class="splittest-badge"></div>
             <div class="splittest-value-badge"></div>
           </div>
           <div class="col-md-9 package-features">
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Alle Funktionen von Klick-Tipp Premium inklusive<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-premium-inclusive', '#title' => t('Premium features inclusive'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Autoresponder-Splittesting – herausragende Optimierung<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-ar-splittest', '#title' => t('Autoresponder splittesting'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Listbuilding auf unbegrenzt vielen Facebook-Seiten<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-facebook-pages', '#title' => t('Listbuilding on facebook'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Facebook-Button – 1-Klick-Eintragung in ContactCloud<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-facebook-button', '#title' => t('Facebook button'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Combo-Formulare mit integriertem Facebook-Button<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-combo-form', '#title' => t('Combo forms'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Bis zu 10.000 Kontakte verwalten<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-deluxe-contacts', '#title' => t('Manage 10.000 subscribers'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Bonus-Geschenk – Splittest-Club (Wert: 444,00 € pro Jahr)<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-splittest-club', '#title' => t('Splittest-Club bonus'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>E-Mail-Support, Antwort innerhalb 1 Werktag<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-deluxe-support', '#title' => t('Email support'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Festpreisgarantie<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-price-guarantee', '#title' => t('Price guarantee'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Up- und Downgrade jederzeit möglich<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-deluxe', '#title' => t('Possible downgrade'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline">
             <div class="logo-box">
               <div class="logo-digistore"></div>
               <div class="number-box">
               <div class="number">5</div>
               </div>
             </div>
             <div class="logo-box">
               <div class="logo-affilicon"></div>
               <div class="number-box">
               <div class="number">5</div>
               </div>
             </div>
             <div class="logo-box">
               <div class="logo-wufoo"></div>
               <div class="number-box">
               <div class="number">5</div>
               </div>
             </div>
             <?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-deluxe-integration-number', '#title' => t('Feature'))); ?>
             </div>
           </div>
         </div>
       </div>

      </div>

      <?php $enterprise = ( $upgrade_product == 'Enterprise' ) ? ' selected' : ''; ?>

      <div id="enterprise" class="package-section<?php print $enterprise; ?>" data-event-click="SelectPayment" data-event-click-args="Enterprise">

       <div class="bundle-header">
           <span class="expander-caret-purple"></span>
           <div class="upgrade-product-title">Klick-Tipp Enterprise</div>
           <div class="upgrade-product-subtitle">Unbegrenzte E-Mail-Marketing-Power</div>
           <div class="upgrade-product-price">ab 149,00 € pro Monat</div>
       </div>

       <div class="bundle-body">
         <div class="row">
           <div class="col-md-3">
             <div class="enterprise-box package-cover"></div>
             <div class="splittest-cover"></div>
             <div class="upgrade-plus"></div>
             <div class="splittest-badge"></div>
             <div class="splittest-value-badge"></div>
           </div>
           <div class="col-md-9 package-features">
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Alle Funktionen von Klick-Tipp Deluxe inklusive<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-enterprise-inclusive', '#title' => t('Deluxe features inclusive'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Lifetime-Upgrades auf alle künftigen Klick-Tipp Features<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-updates', '#title' => t('Lifetime upgrades'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Dedizierte Mailserver<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-dedicated-mailserver', '#title' => t('Dedicated mailserver'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Bonus-Geschenk – Splittest-Club (Wert: 444,00 € pro Jahr)<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-splittest-club', '#title' => t('Splittest-Club bonus'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Premium-Telefonsupport<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-enterprise-support', '#title' => t('Phone support'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Festpreisgarantie<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-price-guarantee', '#title' => t('Price guarantee'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline"><span class="icon-bullet-hook-small"></span>Up- und Downgrade jederzeit möglich<?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-enterprise', '#title' => t('Possible downgrade'))); ?></div>
             <div class="title upgrade-quickhelp quickhelp-inline">
             <div class="logo-box">
               <div class="logo-digistore"></div>
               <div class="number-box">
               <div class="number">&infin;</div>
               </div>
             </div>
             <div class="logo-box">
               <div class="logo-affilicon"></div>
               <div class="number-box">
               <div class="number">&infin;</div>
               </div>
             </div>
             <div class="logo-box">
               <div class="logo-wufoo"></div>
               <div class="number-box">
               <div class="number">&infin;</div>
               </div>
             </div>
             <?php print theme('klicktipp_quickhelp', array('#quickhelp' => 'upgrade-enterprise-integration-number', '#title' => t('Feature'))); ?>
             </div>
           </div>
         </div>
         <div class="enterprise-section">

           <div id="10000" class="row-header<?php print $sub_enterprise; ?>" data-event-click="Enterprise" data-event-click-args="10.000">
               <span class="radio-button-purple"></span>
               <div class="upgrade-product-title">Klick-Tipp Enterprise, bis zu 10.000 Kontakte (1 Mailserver)</div>
               <div class="upgrade-product-price">149,00 € pro Monat</div>
               <div class="upgrade-product-setup-price">+ einmalig 690,00 € Einrichtung</div>
           </div>

           <?php $sub_enterprise = ( $upgrade_subpackage == '15.000' ) ? ' selected' : ''; ?>

           <div id="15000" class="row-header<?php print $sub_enterprise; ?>" data-event-click="Enterprise" data-event-click-args="15.000">
               <span class="radio-button-purple"></span>
               <div class="upgrade-product-title">Klick-Tipp Enterprise, bis zu 15.000 Kontakte (2 Mailserver)</div>
               <div class="upgrade-product-price">199,00 € pro Monat</div>
               <div class="upgrade-product-setup-price">+ einmalig 790,00 € Einrichtung</div>
           </div>

           <?php $sub_enterprise = ( $upgrade_subpackage == '25.000' ) ? ' selected' : ''; ?>

           <div id="25000" class="row-header<?php print $sub_enterprise; ?>" data-event-click="Enterprise" data-event-click-args="25.000">
               <span class="radio-button-purple"></span>
               <div class="upgrade-product-title">Klick-Tipp Enterprise, bis zu 25.000 Kontakte (3 Mailserver)</div>
               <div class="upgrade-product-price">290,00 € pro Monat</div>
               <div class="upgrade-product-setup-price">+ einmalig 1.000,00 € Einrichtung</div>
           </div>

           <?php $sub_enterprise = ( $upgrade_subpackage == '50.000' ) ? ' selected' : ''; ?>

           <div id="50000" class="row-header<?php print $sub_enterprise; ?>" data-event-click="Enterprise" data-event-click-args="50.000">
               <span class="radio-button-purple"></span>
               <div class="upgrade-product-title">Klick-Tipp Enterprise, bis zu 50.000 Kontakte (5 Mailserver)</div>
               <div class="upgrade-product-price">450,00 € pro Monat</div>
               <div class="upgrade-product-setup-price">+ einmalig 1.500,00 € Einrichtung</div>
           </div>

           <?php $sub_enterprise = ( $upgrade_subpackage == '100.000' ) ? ' selected' : ''; ?>

           <div id="100000" class="row-header<?php print $sub_enterprise; ?>" data-event-click="Enterprise" data-event-click-args="100.000">
               <span class="radio-button-purple"></span>
               <div class="upgrade-product-title">Klick-Tipp Enterprise, bis zu 100.000 Kontakte (10 Mailserver)</div>
               <div class="upgrade-product-price">750,00 € pro Monat</div>
               <div class="upgrade-product-setup-price">+ einmalig 2.200,00 € Einrichtung</div>
           </div>

           <?php $sub_enterprise = ( $upgrade_subpackage == '250.000' ) ? ' selected' : ''; ?>

           <div id="250000" class="row-header<?php print $sub_enterprise; ?>" data-event-click="Enterprise" data-event-click-args="250.000">
               <span class="radio-button-purple"></span>
               <div class="upgrade-product-title">Klick-Tipp Enterprise, bis zu 250.000 Kontakte (25 Mailserver)</div>
               <div class="upgrade-product-price">1.500,00 € pro Monat</div>
               <div class="upgrade-product-setup-price">+ einmalig 4.000,00 € Einrichtung</div>
           </div>

           <?php $sub_enterprise = ( $upgrade_subpackage == '500.000' ) ? ' selected' : ''; ?>

           <div id="500000" class="row-header<?php print $sub_enterprise; ?>" data-event-click="Enterprise" data-event-click-args="500.000">
               <span class="radio-button-purple"></span>
               <div class="upgrade-product-title">Klick-Tipp Enterprise, bis zu 500.000 Kontakte (40 Mailserver)</div>
               <div class="upgrade-product-price">2.300,00 € pro Monat</div>
               <div class="upgrade-product-setup-price">+ einmalig 5.500,00 € Einrichtung</div>
           </div>

           <?php $sub_enterprise = ( $upgrade_subpackage == '1.000.000' || $upgrade_subpackage == 'empty' ) ? ' selected' : ''; ?>

           <div id="1000000" class="row-header<?php print $sub_enterprise; ?>" data-event-click="Enterprise" data-event-click-args="1.000.000">
               <span class="radio-button-purple"></span>
               <div class="upgrade-product-title">Klick-Tipp Enterprise, bis zu 1.000.000 Kontakte* (60 Mailserver)</div>
               <div class="upgrade-product-price">3.700,00 € pro Monat</div>
               <div class="upgrade-product-setup-price">+ einmalig 7.100,00 € Einrichtung</div>
         </div>
         </div>
       </div>

      </div>
      <div id="big-clients"></div>

  </div>

  <div id="upgrade-box">
    <p id="upgrade-headline">2. Wählen Sie Ihr Zahlungsintervall</p>

    <?php $selected =  ($CurrentPackage['MonthlyPayment']) ? ' selected' : ''; ?>

      <div id="monthly" class="payment-section<?php print $selected; ?>" data-event-click="SelectPaymentInterval" data-event-click-args="monthly">
        <div class="bundle-header">
          <span class="radio-button-purple"></span>
          <div class="upgrade-product-title interval">Monatliche Zahlung</div>
          <div class="upgrade-product-price"></div>
          <div class="asterix"></div>
        </div>
      </div>

      <?php $selected =  ($CurrentPackage['AnnualPayment']) ? ' selected' : ''; ?>

      <div id="annual" class="payment-section<?php print $selected; ?>" data-event-click="SelectPaymentInterval" data-event-click-args="annual">
        <div class="bundle-header">
          <span class="radio-button-purple"></span>
          <div class="upgrade-product-title interval">Jahreszahlung (10% Rabatt)</div>
          <div class="upgrade-product-price"></div>
          <div class="asterix"></div>
        </div>
      </div>

      <?php $selected =  ($CurrentPackage['BiAnnualPayment']) ? ' selected' : ''; ?>

      <div id="biannual" class="payment-section<?php print $selected; ?>" data-event-click="SelectPaymentInterval" data-event-click-args="biannual">
        <div class="bundle-header">
          <span class="radio-button-purple"></span>
          <div class="upgrade-product-title interval">Zweijahreszahlung (20% Rabatt)</div>
          <div class="upgrade-product-price"></div>
          <div class="asterix"></div>
        </div>
      </div>

  </div>

  <div class="row">
    <div class="col-md-6">
      <p class="asterix left">Sie können Klick-Tipp jederzeit up- oder downgraden und<br/>mit Wirkung zum Ende der bezahlten Nutzungsperiode kündigen.<br /><br />Alle Preise verstehen sich zuzüglich Umsatzsteuer.</p>
    </div>
    <div class="col-md-6">
      <div class="continue-align">
        <form action="" method="post">
         <input type="submit" name="submit" value=" " class="btn-cta-in-den-warenkorb" />
         <div class="btn-cta-in-den-warenkorb inactive" style="display: none;"></div>
         <input id="set_package" type="hidden" name="package" value="<?php print $upgrade_product; ?>">
         <input id ="set_subpackage" type="hidden" name="subpackage" value="<?php print $upgrade_subpackage; ?>">
         <input id="set_interval" type="hidden" name="interval" value="<?php print $current_interval; ?>">
         <input id="set_validation" type="hidden" value="<?php print $product_type; ?>"
        </form>
      <p id="payment-info" class="asterix">Sie können Ihre Auswahl im<br />nächsten Schritt noch einmal überprüfen.</p>
      </div>
    </div>
  </div>

  <div id="klicktipp-seals"></div>

  </div>

  <?php $current_payment = ($CurrentPackage['MonthlyPayment']) ? 'monatlich' : (($CurrentPackage['AnnualPayment']) ? 'Jahreszahlung' : '2-Jahreszahlung');

  $package_name = explode('Klick-Tipp ', $current_package);
  $package_name = $package_name[1];

  ?>

  <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3">
   <div class="sticky">
    <div id="upgrade-box">
    <p id="upgrade-headline">Ihr aktuelles Paket:</p>
      <div class="package-section">
        <div class="bundle-header">
          <div style="background-image: url(https://assets.klicktipp.com/content_includes/orderpage/box_<?php print $current_product; ?>_small.png);" class="package-cover-small"></div>
          <div style="font-size: 15px; position: absolute; right: 85px; top: 25px;">Klick-Tipp</div>
          <div style="font-size: 15px; font-weight: bold; position: absolute; left: 109px; top: 45px;"><?php print $package_name; ?></div>
          <div style="position: absolute; top: 85px; left: 109px; font-size: 13px;">Aktuelle Zahlungsweise:</div>
          <div style="font-weight: bold; position: absolute; top: 100px; left: 109px;"><?php print $current_payment; ?></div>
        </div>
      </div>

    </div>
    <div class="instruction-box">
      <div id="instruction-text">1. Neues Paket aussuchen</div>
      <div id="instruction-text">2. Zahlungsintervall wählen</div>
      <div id="instruction-text">3. Neues Paket bestellen</div>
    </div>
    <div class="payment-arrow"></div>
  </div>
 </div>

</div>
